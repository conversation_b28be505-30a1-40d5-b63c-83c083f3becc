<div id="add_new_unit_modal" class="modal fade" role="dialog" data-backdrop="static" data-keyboard="false">
  	<div class="modal-dialog">
    	<div class="modal-content">
      		<div class="modal-header">
      			<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Create New Unit</h4>
      		</div>
      		<div class="modal-body">
		        <div class="form-group">
			        <label>Unit Name<span class="mandatory_mark"> *</span></label>
	                <input type="text" name="unit_name" id="unit_name" class="form-control" placeholder="E.g. KG" maxlength="20"
	                        onkeypress="return validateStringOnKeyPress(this,event, 'string')" onblur="return validateStringOnBlur(this, event, 'string')" />
		        </div>
		        <div class="form-group">
			        <label>Description<span class="mandatory_mark"> *</span></label>
	                <input type="text" name="unit_desc" id="unit_desc" class="form-control" placeholder="E.g. Kilogram" placeholder="" maxlength="20"
	                       onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialCharMini')" />
		        </div>
	        </div>
		    <div class="modal-footer">
				<span onclick="addNewUnit()" id="addNewUnit" class="btn btn-save">Add</span>
			    <span onclick="cancelUnitAdd();" class="btn btn-cancel">Cancel</span>
		    </div>
	    </div>
    </div>
</div>

<div id="add_new_alternate_unit" class="modal fade" role="dialog" data-backdrop="static" data-keyboard="false">
  	<div class="modal-dialog" id="alternate_measure" style="width : 750px;">
    	<div class="modal-content" >
      		<div class="modal-header">
      			<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">ALTERNATE UNITS OF MEASUREMENT</h4>
      		</div>
      		<div class="modal-body">
		        <div class="row remove-margin">
			         <div hidden="hidden">
						<input type="text" value="0" hidden="hidden" />
						{{ alternate_unit_formset.empty_form.enterprise_id }}
				        {{ alternate_unit_formset.empty_form.primary_unit_name }}
					 </div>
		        	 <div class="form-group" style="width: 40%; float: left;">
		        	 	<label>Unit<span class="mandatory_mark"> *</span></label>
		                {{ alternate_unit_formset.empty_form.alternate_unit_id }}
			        </div>
			        <div class="form-group text-center" style="width: 10%; float: left;margin-top: 18px; font-size: 24px;">
	        			=
			        </div>
			        <div class="form-group" style="width: 44%; float: left;">
			        	<label>Scale Factor<span class="mandatory_mark"> *</span></label>
				        {{ alternate_unit_formset.empty_form.scale_factor }}
		                <label class="unit_display pull-right" id="alternate_primary_unit"></label>
			        </div>
			        <div class="form-group" style="width: 6%; float: left;margin-top: 18px;">
			        	<a href="javascript:void(0);" style="float: right" onclick="addNewAlternateUnit()" id="addNewUnit" class="btn btn-save">+</a>
			        </div>

			        <div class="clearfix"></div>
			        <hr style="margin-left: -15px; margin-right: -15px; margin-top: 10px;" />
			        <div class="">
						{{alternate_unit_formset.management_form}}
						<table class="table table-striped custom-table text_box_in_table" id="alternate_unit_table">
							<tbody>
								<tr bgcolor="#ececec" id="alternate_unit-__dummy__" hidden="hidden">
									<td hidden="hidden" class="exclude_export" style="border-top: none;">
										{{ alternate_unit_formset.empty_form.DELETE }}
										{{ alternate_unit_formset.empty_form.enterprise_id }}
										{{ alternate_unit_formset.empty_form.alternate_unit_id }}

									</td>
									<td class='td-alternate-unit-name' style="width: 40%; ">
										{{ alternate_unit_formset.empty_form.unit_name }}
									</td>
									<td class="text-center" style="width: 10%; margin-top: 18px; font-size: 24px;">
										=
									</td>
									<td style="width: 45%; ">
										{{ alternate_unit_formset.empty_form.scale_factor }}
										<label class="unit_display unit_display-small pull-right" id="id_{{ alternate_unit_formset.empty_form.prefix }}-primary_unit_name"></label>
									</td>
									<td class="text-right" style="width: 5%; margin-top: 18px;">
										<a role='button' id="id_{{ alternate_unit_formset.empty_form.prefix }}-deleteAlternateUnit"
										   onclick="javascript:isDeleteMaterialAlternateUnit(this, '{{ alternate_unit_formset.empty_form.prefix }}','{{ alternate_unit_formset.empty_form.alternate_unit_id.value }}')">
											<i class="fa fa-trash" style='color: red; font-size: 16px; padding: 4px; ' id='deleteImage_{{ alternate_unit_formset.empty_form.alternate_unit_id.value }}'></i>
										</a>
									</td>
								</tr>

								{% for alternate_unit in alternate_unit_formset.initial_forms %}
									<tr bgcolor="#ececec" id="{{ alternate_unit.prefix }}" style="border-top: none;">
										<td hidden="hidden" class="exclude_export">
											{{ alternate_unit.enterprise_id }}
											{{ alternate_unit.DELETE }}
											{{ alternate_unit.alternate_unit_id }}
										</td>
										<td class='td-alternate-unit-name' style="width: 40%;">
											{{ alternate_unit.unit_name.value }}
										</td>
										<td class="text-center" style="width: 10%; margin-top: 18px; font-size: 24px;">
											=
										</td>
										<td class="text-right" style="width: 45%; ">
											{{ alternate_unit.scale_factor }}
											<label class="unit_display unit_display-small pull-right" id="id_{{ alternate_unit.prefix }}-primary_unit_name"  title="{{ alternate_unit.primary_unit_name.value }}" >{{ alternate_unit.primary_unit_name.value }}</label>
											<input hidden="hidden" id="id_{{ alternate_unit.prefix }}-scale_factor_value" value="{{ alternate_unit.scale_factor.value }}"> </td>
										</td>
										<td class="text-right" style="width: 5%; margin-top: 18px;">
											<a role='button' id="id_{{ alternate_unit.prefix }}-deleteAlternateUnit"
											   onclick="javascript:isDeleteMaterialAlternateUnit(this, '{{ alternate_unit.prefix }}',{{ alternate_unit.alternate_unit_id.value}})">
												<i class="fa fa-trash" style='color: red; font-size: 16px; padding: 4px;' id='deleteImage_{{ alternate_unit.prefix }}'></i>
											</a>
										</td>
									</tr>
								{% endfor %}
							</tbody>
						</table>
					</div>
			    </div>
	        </div>
		    <div class="modal-footer">
				<a href="javascript:void(0);" class="btn btn-default" data-dismiss="modal">Close</a>
		    </div>
	    </div>
    </div>
</div>

<div id="materialcategory" class="modal fade" role="dialog" tabindex="-1">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Create New Category</h4>
			</div>
			<div class="modal-body">
				<div class="form-group">
					<label>Name</label>
					<input type="text"  id="catName" class="form-control" placeholder="Enter Category Name" maxlength="100" onKeyPress="validateStringOnKeyPress(this,event, 'string')" onblur="return validateStringOnBlur(this, event, 'string')" />
				</div>
			</div>
			<div class="modal-footer">
				<div class="material_txt">
					<span role="button" class="btn btn-save" id="cmdCatSave" onClick="saveNewCatergory()">Add</span>
					<a role="button" class="btn btn-cancel" data-dismiss="modal" id="cmdCatCancel" >Cancel</a>
				</div>
			</div>
		</div>
	</div>
</div>