<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name="description" content="">
	<meta name="author" content="">
	<title id="template_title">{% if template_title|lower == "xserp" %}xserp{%else%}{{template_title}}{%endif%}</title>
	<link rel="icon" type="image/png" href="/site_media/images/xs-logo-with-border.png" >
	<link rel="stylesheet" type="text/css" href="/site_media/css/roboto-font.css?v={{ current_version }}" >
	<link rel="stylesheet" type="text/css" href="/site_media/css/bootstrap.css?v={{ current_version }}">
	<link rel="stylesheet" type="text/css" href="/site_media/css/build.css?v={{ current_version }}">
	<link rel="stylesheet" type="text/css" href="/site_media/css/calender.css?v={{ current_version }}">
	<link rel="stylesheet" type="text/css" href="/site_media/css/easy-responsive-tabs.css?v={{ current_version }}" >
	<link rel="stylesheet" type="text/css" href="/site_media/css/font-awesome.min.css?v={{ current_version }}" >
	<link rel="stylesheet" type="text/css" href="/site_media/css/bootstrap-datepicker.css?v={{ current_version }}">
	<link rel="stylesheet" type="text/css" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
	<link rel="stylesheet" type="text/css" href="/site_media/css/chosen.css?v={{ current_version }}">
	<link rel="stylesheet" type="text/css" href="/site_media/css/daterangepicker.css?v={{ current_version }}">
	<link rel="stylesheet" type="text/css" href="/site_media/css/Fonts.css?v={{ current_version }}">
	<link rel="stylesheet" type="text/css" href="/site_media/css/customDatepicker.css?v={{ current_version }}">
	<link rel="stylesheet" type="text/css" href="/site_media/css/calculator.css?v={{ current_version }}">
	<link rel="stylesheet" type="text/css" href="/site_media/css/menu.css?v={{ current_version }}">
    <link rel="stylesheet" type="text/css" href="/site_media/css/erp_default.css?v={{ current_version }}">
    <link rel="stylesheet" type="text/css" href="/site_media/css/erp_buttons.css?v={{ current_version }}">
	<link rel="stylesheet" type="text/css" href="/site_media/css/JSCustomValidator.css?v={{ current_version }}">
	<link rel="stylesheet" type="text/css" href="/site_media/css/sweetalert.css?v={{ current_version }}">
	<link rel="stylesheet" type="text/css" href="/site_media/css/tagit.css?v={{ current_version }}">
	<link rel="stylesheet" type="text/css" href="/site_media/css/datatables.css?v={{ current_version }}">
	<link rel="stylesheet" type="text/css" href="/site_media/css/fixedHeader.bootstrap.css?v={{ current_version }}">
	<link rel="stylesheet" type="text/css" href="/site_media/css/dataTables.searchHighlight.css?v={{ current_version }}">
	<link rel="stylesheet" type="text/css" href="/site_media/css/jquery.qtip.min.css?v={{ current_version }}">
	<!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
	<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
	<!--[if lt IE 9]>
	<script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
	<script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
	<![endif]-->
	<!-- jQuery -->
	<script type="text/javascript" src="/site_media/js/jquery.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/jquery-1.12.4.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/jquery_ui.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/cookies.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/bootstrap-datepicker.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/utility.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/customDatepicker.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/bootstrap.min.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/calender-plugin.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/jquery.easyResponsiveTabs.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/notify.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/bootstrap-filestyle.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/erp_default.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/JSCustomValidator.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/sweetalert.min.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/tagit.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/chosen.jquery.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/moment.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/daterangepicker.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/datatables.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/dataTables.fixedHeader.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/dataTables.searchHighlight.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/csvDownload.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/field-validation.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/jquery.qtip.min.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/calculator.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/jquery.slimscroll.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/keyboardShortcut.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/countdown-timer.js?v={{ current_version }}"></script>

	<!--Fcm notification oriented works below-->
	<script type="text/javascript" src="https://www.gstatic.com/firebasejs/4.2.0/firebase-app.js"></script>
	<script type="text/javascript" src="https://www.gstatic.com/firebasejs/4.2.0/firebase-messaging.js"></script>
	<script>
		// Initialize Firebase
		var config = {
		  apiKey: "AIzaSyBIs-kSpEnbj8MQQTl38qwv870UeFC7yjg",
		  authDomain: "schnell-130317.firebaseapp.com",
		  databaseURL: "https://schnell-130317.firebaseio.com",
		  projectId: "schnell-130317",
		  storageBucket: "schnell-130317.appspot.com",
		  messagingSenderId: "135682287659"
		};
		firebase.initializeApp(config);
		// Retrieve Firebase Messaging object.
		const messaging = firebase.messaging();


		messaging.onMessage(function(payload) {
		  getNotificationCount();
		  alertcount = $('#alertbadge').attr('data-badge');
		  $("#alertbadge").attr('data-badge', parseInt(alertcount)+1);
		});
	</script>
	<!-- Global site tag (ga analytics.js) - Google Analytics -->
	<script type="text/javascript">
		(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
		  (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
		  m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
		  })(window,document,'script','//www.google-analytics.com/analytics.js','ga');
		ga('create', '{{ analytics_code }}', 'auto', {
		  	userId: '[{{logged_in_user.enterprise.id}}] {{logged_in_user.enterprise.name}}'
		});
		ga('send', 'pageview');
	</script>

	<style type="text/css">
		.error_panel {
			width: 100%;
			height: 100%;
			top: 0;
			left: 0;
			display: none;
			position: absolute;
			background-color: rgba(204, 204, 204, 0.5);
			color: #222222;
		}
		#error_display{
			color:#FF0000;
			background:#00FFFF;
			font-size:12px;
			font-weight:normal;
			float:center;
			display:show;
			position: absolute;
			top: 50%;
			left: 50%;
			margin-top: 100px;
			margin-left: 100px;
		}

		@media only screen and (max-width: 1366px) {
		    .navbar-inverse .navbar-nav > li > a {
				padding: 16px 15px 16px;
			}
		}

		@media only screen and (max-width: 1280px) {
			.navbar-inverse .navbar-nav > li > a {
				padding: 16px 10px 16px;
				font-size: 0.9em;
			}

			.span-profile-name {
				font-size: 1em;
			}
		}

		@media only screen and (max-width: 1100px) {
			.navbar-inverse .navbar-nav > li > a {
				padding: 10px 10px 12px;
			}

			.navbar-inverse .navbar-nav.navbar-left {
				width: 25%;
			}
		    .navbar-inverse .navbar-nav.navbar-left > li {
				width: 100%;
			}

			#horizontal-menu {
				margin-top: 60px;
			}
		}
		
		.no-acitve-link {
			color: #FFF;
			font-size: 16px;
			letter-spacing: 1px;
		}
		
		.navbar-inverse .navbar-nav > li > a:hover {
			color: #FFF;
			background: transparent;
		}
		
		.navbar-inverse .navbar-nav > li > div:hover {
			background: transparent;
		 }
		
		.acitve-settings-link:hover {
			background: red;
		}

		.rotate{
		    -moz-transition: all 0.3s linear;
		    -webkit-transition: all 0.3s linear;
		    transition: all 0.3s linear;
		}

		.rotate.down{
		    -moz-transform:rotate(90deg);
		    -webkit-transform:rotate(90deg);
		    transform:rotate(90deg);
		}

		.dropdown-menu-settings li {
			border-bottom: none;
		}

		.dropdown-menu.dropdown-e-invoice > li {
			border: none;
		}

		.dropdown-menu.dropdown-e-invoice > li:hover {
			background: transparent;
		}

		.count-down_timer{
			text-align: right;
			float: right;
			margin-top: -4px;
		}

		.subscribe-btn,
		.trial-extension{
			float: left;
			padding: 3px 18px;
			z-index: 1004;
			margin: 4px;
			text-shadow: 0 0 #fff;
		 	border: solid 2px #fff;
		 	color:#fff;
		}

		.subscribe-btn{
			background:#ff4500;
		}

		.trial-extension{
		    background: #209be1;
		}

		.subscribe-btn:hover,
		.trial-extension:hover,
		.subscribe-btn:focus,
		.trial-extension:focus {
			color:  #FFF;
			box-shadow: 0 0 3px #666;
		}

		.countdown > div{display: inline-block;}
		.countdown > div > span{display: block; text-align: center;}
		.countdown-container{margin:0 3px; margin-top: -2px; }
		.countdown-container .countdown-heading{font-size: 9px; margin: 3px; margin-top:-1px;}
		.countdown-container .countdown-value{font-size: 22px;}
		.count-down-heading{font-size:10px;}

		.subscription-bar {
			height:40px; position: fixed; text-align:center; margin-top: -40px; width: 100%;
			background: rgb(2,0,36);
			background: linear-gradient(90deg, rgba(2,0,36,1) 0%, rgba(255,213,87,1) 0%, rgba(255,233,166,1) 51%, rgba(255,213,87,1) 100%);
			z-index: 1004;
			box-shadow: 0px 2px 4px #bbb;
		}

		.subscription-bar.expired {
			background: rgb(2,0,36);
			background: linear-gradient(90deg, rgba(2,0,36,1) 0%, rgba(255,69,0,1) 0%, rgba(255,106,78,1) 51%, rgba(255,69,0,1) 100%);
			color:  #FFF;
		}

		.expired-message {
			display: inline-block;
			font-size:  15px;
			padding-right: 8px;
		}

		.expired-before-txt {
		    display: inline-block;
		    float: right;
		    margin-top: 1px;
		    margin-left: 6px;
		    font-size: 15px;
		}
		.choose_plan{
			float: left;
		    padding: 3px 18px;
		    z-index: 1004;
		    margin: 4px;
		    text-shadow: 0 0 #fff;
		    border: solid 2px #014195;
		    color: #014195;
		}
		.choose_plan:hover{
			background: #209be1;
			color:  #FFF;
			box-shadow: 0 0 3px #666;
			border: solid 2px #fff;

		}
		.show_template_label {
			color: #333;
			text-decoration: none;
			border-radius: 50px;
			margin-left: 9px;
			margin-top: 9px;
    		padding: 6px 7px 5px 6px;
    		border: 1px solid #333;
    		opacity: 0.5;
		}
		.show_template_label.on {
			border: 1px solid #004195;
			color: #004195;
			opacity: 1;
		}
		.show_template_label:active:before, .show_template_label.on:before {
			background-color: transparent;
			border: 1px solid #004195;
			box-shadow: 0px 1px 0px 0px rgba(250,250,250,0.1),
			inset 0px 1px 2px rgba(0, 0, 0, 0.5);
		}

		.li_view_template {
			margin-left: 33px;
    		border-left: 1px solid #999;
    		display: none;
		}

		.dropdown-menu li.li_view_template a{
			padding: 6px 24px 6px 0;
			font-size: 12px;
			color: #666;
		}
		.other_setting_menu{
			font-size: 16px;
			margin-left: 15px;
			margin-top: 28px;
		}
	</style>

</head>
<body onLoad="doOnLoad();" class="{% if subscription.shall_enable_expiry_timer %} with_subscription_bar {% endif%}">
	{% csrf_token %}
	<div class="loading-main-container" id="loading">
		<div class="loading-sub-container"></div>
		<div class="loading-sub-text">Processing, please wait<span>...</span></div>
	</div>

	<div class="downloading-main-container" id="downloading">
		<div class="downloading-sub-container"></div>
	</div>

	<div class="body-container">
		<div class="container-fluid main-menu-container">
			<input type="hidden" id="is_project_wise_pl" value="" />
			{% if subscription.shall_enable_expiry_timer %}
				<div class="subscription-bar {% if subscription.is_expired %}expired {%endif%}">
					<div class="col-sm-6 text-right" style="margin-top: 10px; padding: 0">
						{% if subscription.is_expired %}
							{% if subscription.plan == 'trial' %}
								<div class='expired-message after-expired-msg'>Your Trail Period expired since</div>
							{%else%}
								<div class='expired-message after-expired-msg'>Your Subscription expired since</div>
							{%endif%}
							<div class="expired-before-txt">before</div>
						{%else%}
							{% if subscription.plan == 'trial' %}
								<div class="expired-message after-expired-msg">Your Trial Period will expire in </div>
							{%else%}
								<div class="expired-message after-expired-msg">Your Subscription will expire in </div>
							{%endif%}
							<div class="expired-before-txt"></div>
						{%endif%}
						<div class="count-down_timer countdown" data-date="{{subscription.expired_on}}"></div>
						<input type="hidden" id="current_server_time" value="{{page_loaded_on}}" />
						<!-- {{subscription.is_expired}} {{subscription.expired_on}} -->
					</div>
					<input type="hidden" id="enterprise-plan" value="{{subscription.plan}}"/>
					<div class="col-sm-6">
						{% if subscription.plan == 'trial' %}
							<input type="button" class="btn subscribe-btn" value="Subscribe" data-toggle="modal" data-target="#subscription-modal">
							{% if subscription.shall_enable_request_extension %}
								<input type="button" class="btn trial-extension" value="Request Trial Extension" onclick="requestTrialExtension()">
							{% else %}
								<div class="expired-message btn trial-extension" style="opacity: 0.7;" data-tooltip="tooltip" data-placement="bottom" title="You have already requested for Trial Extension." onclick="requestedForTrialExtension('{{subscription.extension_requested_on}}')">Request Trial Extension</div>
							{% endif %}
						{% else %}
							<input type="button" class="btn subscribe-btn" value="Renew" data-toggle="modal" data-target="#subscription-modal">
							{% if subscription.shall_enable_request_extension %}
								<input type="button" class="btn trial-extension" value="Request Grace Period" onclick="requestGracePeriod()">
							{% else %}
								<div class="expired-message btn trial-extension" style="opacity: 0.7;" data-tooltip="tooltip" data-placement="bottom" title="You have already requested for grace period." onclick="requestedForGracePeriod('{{subscription.extension_requested_on}}')">Request Grace Period</div>
							{% endif %}
						{% endif %}
					</div>
				</div>
			{% endif %}
			<nav class="navbar navbar-inverse" style="position: fixed; width: 100%;z-index: 10;background: #FFF;">
			  	<div class="container-fluid">
			  		<div class="col-sm-6 navbar-mobile" style="font-size: 28px; padding: 7px 15px; cursor: pointer; width: 100px; z-index: 100; float: left">
						<i class="fa fa-bars" aria-hidden="true"></i>
					</div>
			  		<div class="col-sm-6 remove-padding">
						<a href="/erp/home/" class="erp-logo-container">
							<img src="/site_media/images/xs-logo.svg"  />
						</a>
					</div>
					<div class="col-sm-6 remove-padding pull-right" style="z-index: 100;">
						<ul class="nav navbar-nav navbar-right" style="margin-top: 4px; margin-right: 0;">
							<li style="margin-right: 8px;">
								{% if subscription.shall_enable_expiry_timer == False and subscription.plan == 'trial' %}
								<input type="button" class="btn choose_plan hide" value="Choose your Plan" data-toggle="modal" data-target="#subscription-modal">
								{% endif %}
							</li>
							<li class="no-acitve-link enterprise-profile-container">
								<span class="a-profile-container" onclick="toggleProject()" >
									<span class="span-profile-name">
										<span class="enterprise_name">{{ logged_in_user.enterprise.name }}</span>
										<small class="hide">{{ logged_in_user.enterprise.city }}, {{ logged_in_user.enterprise.state }}</small>
									</span>
									<i class="fa fa-chevron-down span-profile-name-arrow" aria-hidden="true"></i>
									<input type="hidden" id="id_enterprise_fy_start_day" value="{{logged_in_user.enterprise.fy_start_day}}"/>
									<input type="hidden" value={{ logged_in_user.enterprise.id }} class="textbox2" id="enterprise_id" />
									<input type="hidden" value="{{ home_currency }}" class="textbox2" id="home_currency_id" />
                                    <input type="hidden" value="{{ logged_in_user.enterprise.country_code }}" class="textbox2" id="home_country_id" />
                                    <input type="hidden" value="{{ logged_in_user.enterprise.state }}" class="textbox2" id="home_state_id" />
									<input type="hidden" value={{ logged_in_user }} class="textbox2" id="user_id" />
									<input type="hidden" value={{ logged_in_user.user_id }} class="textbox2" id="login_user_id" />
									<input type="hidden" id="enterprise_previous_closure_date" value="" />
									<input type="hidden" class="textbox2" id="hdn_notification_id"  value="" />
									<input type="hidden" id="is_super_user" value="{{logged_in_user.is_super}}" />
									<input type="hidden" id="enterprise_label" value="[{{logged_in_user.enterprise.id}}] {{logged_in_user.enterprise.name}}" />
									<input type="hidden" id="is_multiple_units" value="{{logged_in_user.enterprise.is_multiple_units}}"/>
									<input type="hidden" id="project_version" value="{{current_version}}"/>
								</span>
							</li>
							<li class="active-settings-link for-primary-ent" style="width: 50px; float: left;">
								<div class="dropdown" id="other-config">
									<button class="btn btn-default dropdown-toggle btn-setting" type="button" data-toggle="dropdown">
										<img title="settings" src="/site_media/images/settings.png" style="width: 22px;" />
									</button>
									<ul class="dropdown-menu dropdown-menu-settings arrow_box arrow_box_setting" style="margin-top: 2px; padding: 0;">
										<li >
									  		<a id="a-enterprise-profile-menu" tabindex="-1" href="/erp/admin/enterprise?type=profile">
										  		<i class="fa fa-building" aria-hidden="true" style="padding-right: 15px;"></i>Enterprise Profile
										  	</a>
									  	</li>
										<li role="button" id="li_print_template">
											<a>
												<i class="fa fa-cogs" aria-hidden="true" style="padding-right: 15px;"></i>Print Template<i class="fa fa-for-arrow fa-caret-down" style="float:right;color:#000;"></i>
											</a>
										</li>
										<li class="li_view_template">
											<a id="a-invoice-template-menu" tabindex="-1" href="/erp/admin/invoice_template/">
												<span style="color: #999;">------</span> Invoice
											</a>
										</li>
										<li class="li_view_template">
											<a id="a-po-config-menu" tabindex="-1" href="/erp/admin/po_template/">
												<span style="color: #999;">------</span> Purchase Order
											</a>
										</li>
										<li class="li_view_template">
											<a tabindex="-1" href="/erp/admin/sales_estimate_template/">
												<span style="color: #999;">------</span> Sales Estimate
											</a>
										</li>
										<li class="li_view_template">
											<a tabindex="-1" href="/erp/admin/oa_template/">
												<span style="color: #999;">------</span> Order Acknowledgement
											</a>
										</li>
										<li>
									  		<a id="a-expense-configration-menu" tabindex="-1" href="/erp/admin/enterprise?type=expense">
										  		<i class="fa fa-cogs" aria-hidden="true" style="padding-right: 15px;"></i>Expense Configuration
										  	</a>
									  	</li>
									  	<li>
						  					<a id="a-user-menu" tabindex="-1" href="/erp/admin/user/">
						  						<i class="fa fa-users" aria-hidden="true" style="padding-right: 15px;"></i>Users
											</a>
										</li>
										<li>
						  					<a id="a-whats-new" tabindex="-1" role="button" onClick="openSlider()">
						  						<i class="fa fa-gift" aria-hidden="true" style="padding-right: 15px;"></i>What's New
											</a>
										</li>
										<li>
											<hr style="margin: 0;" />
										</li>
									 	<li id="a-import-menu">
									        <a tabindex="-1" href="/erp/admin/enterprise?type=import">
										        <i class="fa fa-download" aria-hidden="true" style="padding-right: 15px;"></i>Import Data
										    </a>
									    </li>
									    <li id="a-export-menu">
									        <a tabindex="-1" href="/erp/admin/enterprise?type=export">
										        <i class="fa fa-upload" aria-hidden="true" style="padding-right: 15px;"></i>Export Data
										    </a>
									    </li>
										{% if logged_in_user.is_super %}
											{% if is_erase_data_expired %}
												<li id="a-request-erase-data-menu">
											        <a tabindex="-1" role="button" onClick="requestEraseData()">
												        <i class="fa fa-eraser" aria-hidden="true" style="padding-right: 15px;"></i>Request Data Erase
												    </a>
											    </li>
											{% else %}
											    <li id="a-erase-data-menu">
											        <a tabindex="-1" href="/erp/admin/enterprise?type=erase">
												        <i class="fa fa-eraser" aria-hidden="true" style="padding-right: 15px;"></i>Erase Data
												    </a>
											    </li>
											{% endif %}
										{% endif %}
									</ul>
								</div>
							</li>

							<li class="no-acitve-link notification-container for-primary-ent" style="width: 70px; float: left;">
								<div role="button" id="divNotificationBadge" style="padding: 5px 20px;">
									<button class="btn-setting">
										<img role="button" title="notification" src="/site_media/images/notification.png" id="imgNotificationBadge" style="border-radius: 50px; padding: 2px 0; width: 20px;" />
									</button>
									<span id="alertbadge" class="span-notification" data-badge="0"></span>
									<div class="arrow_box" id="tableNotificationContainer" style="display: none;">
										<table class="table table-stripped table-notification" id="tableNotification">
											<tbody id="notification"></tbody>
											<thead id ="delete_all" class='delete-all'><tr><td>Delete All</td></tr></thead>
										</table>
									</div>
								</div>
							</li>

							<li class="active-settings-link" style="width: 50px; float: left;">
								<div class="dropdown">
									<button class="btn btn-default dropdown-toggle btn-setting" type="button" data-toggle="dropdown">
										<span class="span-profile-img">{{logged_in_user.first_name|first}}</span>
									</button>
									<ul class="dropdown-menu arrow_box arrow_box_profile" style="margin-top: -1px; padding: 0; margin-right: 4px; width: 350px;">
								  		<li>
						  					<span id="a-user-name" tabindex="-1" style="pointer-events: none; padding: 10px 15px; display: inline-block;">
						  						<span class="user_profile_image_contianer" style="display: inline-block; width: 60px; float: left; margin-top: 14px; margin-left: 7px;">
						  							<span class="user_profile_image">{{logged_in_user.first_name|first}}</span>
						  						</span>
						  						<span class="user_profile_contianer" style="display: inline-block; width: 250px; float: left; color: #209be1; margin-top: 7px;">
							  						<span class="user_profile_name" style="font-size: 16px;">{{ logged_in_user.first_name }} {{logged_in_user.last_name }}</span>
							  						<span class="user_profile_login" style="color: #666; word-break: break-all; display: block;"><small>{{logged_in_user.email}}</small></span>
							  					</span>
											</span>
										</li>
									  	<li>
									  		<a id="a-change-pwd-menu" tabindex="-1" href="/erp/auth/change_pwd/">
										  		<i class="fa fa-unlock-alt" aria-hidden="true" style="padding-right: 15px;"></i>Change Password
										  	</a>
									  	</li>
									  	<li>
									  		<a id="a-logout-menu" tabindex="-1" href="/erp/logout/">
										  		<i class="fa fa-sign-out" aria-hidden="true" style="padding-right: 15px;"></i>Logout
										  	</a>
									  	</li>
									</ul>
								</div>
							</li>
						</ul>
						<div class="project_list hide" style="overflow: scroll;">
<!--							<ul style="margin-bottom:100px;"></ul>-->
						</div>
					</div>

					<div class="main-header">
						<ul class="nav navbar-nav main-header-plus-container">
							<li class="dropdown dropdown-large main-header-plus" style="float: none;">
								<a href="#" class="dropdown-toggle dropdown-header-plus" data-toggle="dropdown">
									<i class="fa fa-plus rotate" aria-hidden="true"></i>
								</a>
								<ul class="dropdown-menu dropdown-menu-large row arrow_box arrow_box_menu">
									<li class="col-sm-6">
										<ul>
											<li class="dropdown-header for-primary-ent">Finance</li>
											{% if logged_in_user|canEdit:'ACCOUNTS' %}
												<li class="for-primary-ent"><a href="/erp/accounts/voucher/">Voucher</a></li>
											{% else %}
												<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Accounts Module. Please contact the administrator." style="cursor: not-allowed;">
													<a class="disabled-link">Voucher</a>
												</li>
											{% endif %}
											<li class="divider for-primary-ent"></li>
											<li class="dropdown-header for-primary-ent">Purchase</li>
                                            {% if logged_in_user|canEdit:'PURCHASE INDENT' %}
												<li class="for-primary-ent"><a href="/erp/stores/indent/">Purchase Indent</a></li>
											{% else %}
												<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Indent Module. Please contact the administrator." style="cursor: not-allowed;">
													<a class="disabled-link">Purchase Indent</a>
												</li>
											{% endif %}
											{% if logged_in_user|canEdit:'PURCHASE' %}
												<li class="for-primary-ent"><a href="/erp/purchase/po/?id=custom-tab2">Purchase Order</a></li>
												<li class="for-primary-ent"><a href="/erp/purchase/jo/?id=custom-tab2">Job Order</a></li>
											{% else %}
												<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Purchase Module. Please contact the administrator." style="cursor: not-allowed;">
													<a class="disabled-link">Purchase Order</a>
												</li>
												<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Purchase Module. Please contact the administrator." style="cursor: not-allowed;">
													<a class="disabled-link">Job Order</a>
												</li>
											{% endif %}
											<li class="divider for-primary-ent"></li>
											<li class="dropdown-header">Sales</li>
											{% if logged_in_user|canEdit:'SALES' %}
												<li class="for-primary-ent"><a href="/erp/sales/sales_estimate/">Sales Estimate</a></li>
												<li ><a href="/erp/sales/oa/" id="for-sec-ent-oaT">Order Acknowledgement</a></li>
												<li><a href="/erp/sales/invoice/"id="for-sec-ent-invT">Invoice</a></li>
												<li class="for-primary-ent"><a href="/erp/sales/sr/">Sales Return</a></li>
											{% else %}
												<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Sales Module. Please contact the administrator." style="cursor: not-allowed;">
													<a class="disabled-link">Sales Estimate</a>
												</li>
												<li data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Sales Module. Please contact the administrator." style="cursor: not-allowed;">
													<a class="disabled-link">Order Acknowledgement</a>
												</li>
												<li data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Sales Module. Please contact the administrator." style="cursor: not-allowed;">
													<a class="disabled-link">Invoice</a>
												</li>
												<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Sales Module. Please contact the administrator." style="cursor: not-allowed;">
													<a class="disabled-link">Sales Return</a>
												</li>
											{% endif %}
										</ul>
									</li>
									<li class="col-sm-6">
										<ul>
											<li class="dropdown-header for-primary-ent">Audit</li>
											{% if logged_in_user|canEdit:'EXPENSES' %}
												<li class="for-primary-ent"><a href="/erp/expenses/home/<USER>">Expense</a></li>
											{% else %}
												<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Expense Module. Please contact the administrator." style="cursor: not-allowed;">
													<a class="disabled-link">Expense</a>
												</li>
											{% endif %}
											{% if logged_in_user|canEdit:'ICD' %}
												<li class="for-primary-ent"><a href="/erp/auditing/note/">Credit/Debit Note</a></li>
											{% else %}
												<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in ICD Module. Please contact the administrator." style="cursor: not-allowed;">
													<a class="disabled-link">Credit/Debit Note</a>
												</li>
											{% endif %}
											<li class="divider for-primary-ent"></li>
											<li class="dropdown-header for-primary-ent">Stores</li>
											{% if logged_in_user|canEdit:'STORES' %}
												<li class="for-primary-ent"><a href="/erp/stores/grn/">Goods Receipt</a></li>
												<li class="for-primary-ent"><a href="/erp/stores/dc/">Delivery Challan</a></li>
											{% else %}
												<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Stores Module. Please contact the administrator." style="cursor: not-allowed;">
													<a class="disabled-link">Goods Receipt</a>
												</li>
												<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Stores Module. Please contact the administrator." style="cursor: not-allowed;">
													<a class="disabled-link">Delivery Challan</a>
												</li>
											{% endif %}
											<li class="divider for-primary-ent"></li>
											<li class="dropdown-header for-primary-ent">Production</li>
											<li class="for-primary-ent"><a href="/erp/production/manufacturing_indent/">Manufacturing Indent</a></li>
											<li class="for-primary-ent"><a href="/erp/production/production_plan/">Production Plan </a></li>
											{% if logged_in_user|canEdit:'STORES' %}
												<li class="for-primary-ent"><a href="/erp/stores/issue/">Issue</a></li>
												<li class="for-primary-ent"><a href="/erp/stores/irn/">Issue Return</a></li>
											{% else %}
												<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Stores Module. Please contact the administrator." style="cursor: not-allowed;">
													<a class="disabled-link">Issue</a>
												</li>
												<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Stores Module. Please contact the administrator." style="cursor: not-allowed;">
													<a class="disabled-link">Issue Return</a>
												</li>
											{% endif %}
										</ul>
									</li>
								</ul>
							</li>
						</ul>
					</div>
			  	</div>
			</nav>
		</div><!-- Top Navigation End Here -->

		<div class="left-menu-container-mobile mobile-sidenav" id="id_menu_mobile">
			<ul class='mobile-sidenav-parent' id="mobile-sidenav-accounts">
			  	<li class="mobile-sidenav-header">
			  		<img src="/site_media/images/menu/accounts.png" style="width: 20px; filter:invert(100%); margin-right: 16px;" /> Accounts
			  	</li>
			  	<ul class="mobile-sidenav-child">
					<li><a href="/erp/accounts/home/">Dashboard</a></li>
					<li><a href="/erp/accounts/ledger/">Ledger</a></li>
					<li><a href="/erp/accounts/voucher/list/">Voucher</a></li>
					<li><a href="/erp/accounts/brs/">Bank Reconciliation</a></li>
					<li><a href="/erp/accounts/payments/">Bill Settlements</a></li>
					<li><a href="/erp/accounts/trial_balance/">Statements</a></li>
		            <li><a href="/erp/accounts/gstr1-purchase-report-statement/">Purchase Register <span class="super-markable-text">GST Related</span></a></li>
					<li><a href="/erp/accounts/gstr2-purchase-report-statement/">GSTR-2 Purchase Report</a></li>
		            <li><a href="/erp/accounts/gstr1-sales-report-statement/">GSTR-1 Sales Report</a></li>
		            <li><a href="/erp/accounts/gstr3b-reconciliation-report-statement/">GSTR-3B</a></li>
			  	</ul>
			</ul>
			<ul class='mobile-sidenav-parent' id="mobile-sidenav-audit">
			  	<li class="mobile-sidenav-header">
			  		<img src="/site_media/images/menu/audit.png" style="width: 20px; filter:invert(100%); margin-right: 16px;" /> Audit
			  	</li>
			  	<ul class="mobile-sidenav-child">
			  		<li><a href="/erp/auditing/icd/">Internal Control </a></li>
					<li><a href="/erp/expenses/home">Expenses</a></li>
					<li><a href="/erp/auditing/icd/?is_credit_debit=True">Credit/Debit Note</a></li>
			  	</ul>
			</ul>
			<ul class='mobile-sidenav-parent' id="mobile-sidenav-purchase">
			  	<li class="mobile-sidenav-header">
			  		<img src="/site_media/images/menu/purchase.png" style="width: 20px; filter:invert(100%); margin-right: 16px;" /> Purchase
			  	</li>
			  	<ul class="mobile-sidenav-child">
			  		<li><a href="/erp/purchase/home">Dashboard</a></li>
                    <li><a href="/erp/stores/indent_list/">Purchase Indent</a></li>
					<li><a href="/erp/purchase/po_list/">Purchase Order</a></li>
				    <li><a href="/erp/purchase/jo_list/">Job Order</a></li>
				    <li><a href="/erp/purchase/po/reports/">P.O Wise Reports</a></li>
					<li><a href="/erp/purchase/po/materialwisereports/">Material Wise Reports</a></li>
					<li><a href="/erp/reports/purchase">Custom Purchase Report</a></li>
			  	</ul>
			</ul>
			<ul class='mobile-sidenav-parent' id="mobile-sidenav-sales">
			  	<li class="mobile-sidenav-header">
			  		<img src="/site_media/images/menu/sales.png" style="width: 20px; filter:invert(100%); margin-right: 16px;" /> Sales
			  	</li>
			  	<ul class="mobile-sidenav-child">
			  		<li><a href="/erp/sales/home">Dashboard</a></li>
					<li><a href="/erp/sales/sales_estimate/view/">Sales Estimate</a></li>
					<li><a href="/erp/sales/oa/view/">Order Acknowledgement</a></li>
					<li><a href="/erp/sales/invoice_list/">Invoice</a></li>
					<li><a href="/erp/sales/sr_list/">Sales Return</a></li>
					<li><a href="/erp/sales/reports/">Status Report</a></li>
					<li><a href="/erp/sales/inv_tax_report/">Tax Report</a></li>
					<li><a href="/erp/sales/material_tax_report/">Material Report</a></li>
					<li><a href="/erp/sales/sales-report-statement/">Sales Report</a></li>
					<li><a href="/erp/reports/sales">Custom Sales Report</a></li>
					<li><a href="/erp/reports/oa_report">OA Report</a></li>
			  	</ul>
			</ul>
			<ul class='mobile-sidenav-parent' id="mobile-sidenav-stores">
			  	<li class="mobile-sidenav-header">
			  		<img src="/site_media/images/menu/stores.png" style="width: 20px; filter:invert(100%); margin-right: 16px;" /> Stores
			  	</li>
			  	<ul class="mobile-sidenav-child">
					<li><a href="/erp/stores/home">Dashboard</a></li>
					<li><a href="/erp/stores/grn_list/">Goods Receipt (GRN)</a></li>
					<li><a href="/erp/stores/dc_list/">Delivery Challan</a></li>
		            <li><a href="/erp/stores/stock-statement">Stock Report</a></li>
		            <li><a href="/erp/stores/grn-report-statement/">GRN Report</a></li>
		            <li><a href="/erp/stores/dc-report/">DC Report</a></li>
					<li><a href="/erp/stores/issue_stock_report/">Internal Stock Flow</a></li>
					<li><a href="/erp/stores/materialhistorypage">Material Receipt / Issue Report</a></li>
			  	</ul>
			</ul>
			<ul class='mobile-sidenav-parent' id="mobile-sidenav-production">
			  	<li class="mobile-sidenav-header">
			  		<img src="/site_media/images/menu/production.png" style="width: 20px; filter:invert(100%); margin-right: 16px;" /> Production
			  	</li>
			  	<ul class="mobile-sidenav-child">
					<li class="hide"><a href="/erp/stores/home">Dashboard</a>
					<li><a href="/erp/production/manufacturing_indent/">Manufacturing Indent </a></li>
					<li><a href="/erp/production/production_plan/">Production Plan</a></li>
					<li><a href="/erp/stores/issue_list/">Issue</a></li>
					<li><a href="/erp/stores/irn_list/">Issue Return / Internal Receipt</a></li>
					<li><a href="/erp/stores/materialshortage">Shortage Calculator (BOM Bases)</a></li>
<!--				    <li><a href="/erp/stores/materialshortage_pp/">Shortage Calculator (Production Plan Bases)</a></li>-->
					<li><a href="/erp/stores/jobinreport">Job-In Stock Register</a></li>
					<li><a href="/erp/stores/json/joboutreport">Job-Out Stock Register</a></li>
			  	</ul>
			</ul>
			<ul class='mobile-sidenav-parent' id="mobile-sidenav-master">
			  	<li class="mobile-sidenav-header">
			  		<img src="/site_media/images/menu/master.png" style="width: 20px; filter:invert(100%); margin-right: 16px;" /> Profile
			  	</li>
			  	<ul class="mobile-sidenav-child">
					<li><a href="/erp/masters/party_list">Party</a></li>
					<li><a href="/erp/masters/catalogues">Material</a></li>
					<li><a href="/erp/masters/tax">Tax</a></li>
			  	</ul>
			</ul>
			<ul class='mobile-sidenav-parent' id="mobile-sidenav-hr">
			  	<li class="mobile-sidenav-header">
			  		<img src="/site_media/images/menu/hr.png" style="width: 20px; filter:invert(100%); margin-right: 16px;" /> HR
			  	</li>
			  	<ul class="mobile-sidenav-child">
					<li><a href="/erp/hr/employee">Employees</a></li>
					<li><a href="/erp/hr/pay">Pay Structure</a></li>
					<li><a href="/erp/hr/attendance">Attendance</a></li>
			  	</ul>
			</ul>
		</div>

		<div class="slide_container" >
			<div class="slide_container_part" id="menu_accounts">
				<img class="menu-icon" src="/site_media/images/menu/accounts.png" />
				<span class="menu-text">FINANCE</span>
				{% if logged_in_user|canApprove:'ACCOUNTS' %}
				<span class="notification-count for-primary-ent" data-badge="" id="dash_voucher_count"></span>
				{% endif %}
			</div>
			<div class="slide_container_part for-primary-ent" id="menu_audit">
				<img class="menu-icon" src="/site_media/images/menu/audit.png" />
				<span class="menu-text">AUDIT</span>
				{% if logged_in_user|canApprove:'ICD' %}
				<span class="notification-count" data-badge="" id="dash_audit_count"></span>
				{% endif %}
			</div>
			<div class="slide_container_part for-primary-ent" id="menu_purchase">
				<img class="menu-icon" src="/site_media/images/menu/purchase.png" />
				<span class="menu-text">PURCHASE</span>
				{% if logged_in_user|canApprove:'PURCHASE' %}
				<span class="notification-count" data-badge="" id="dash_po_count"></span>
				{% endif %}
			</div>
			<div class="slide_container_part" id="menu_sales">
				<img class="menu-icon" src="/site_media/images/menu/sales.png" />
				<span class="menu-text">SALES</span>
				{% if logged_in_user|canApprove:'SALES' %}
				<span class="notification-count" data-badge="" id="dash_sales_count"></span>
				{% endif %}
			</div>
			<div class="slide_container_part for-primary-ent" id="menu_stores">
				<img class="menu-icon" src="/site_media/images/menu/stores.png" />
				<span class="menu-text">STORES</span>
				{% if logged_in_user|canApprove:'STORES' %}
				<span class="notification-count" data-badge="" id="dash_stores_count"></span>
				{% endif %}
			</div>
			<div class="slide_container_part for-primary-ent" id="menu_production_planning">
				<img class="menu-icon" src="/site_media/images/menu/production.png" />
				<span class="menu-text">PRODUCTION</span>
				{% if logged_in_user|canApprove:'PRODUCTION' or logged_in_user|canApprove:'MANUFACTURE INDENT' or logged_in_user|canApprove:'STORES' %}
				<span class="notification-count" data-badge="" id="dash_production_count"></span>
				{% endif %}
			</div>
			<div class="slide_container_part for-primary-ent" id="menu_master">
				<img class="menu-icon" src="/site_media/images/menu/master.png" />
				<span class="menu-text">PROFILE</span>
				{% if logged_in_user|canApprove:'MASTERS' %}
				<span class="notification-count" data-badge="" id="dash_material_count"></span>
				{% endif %}
			</div>
			<div class="slide_container_part for-primary-ent" id="menu_hr">
				<img class="menu-icon" src="/site_media/images/menu/hr.png" style="width: 30px;" />
				<span class="menu-text">HR</span>
			</div>
		</div>

		<div class="left-menu-container" id="id_menu_accounts">
			<ul class="nav nav-pills nav-stacked custom-left-nav">
				<li class="menu-item-header"><span id="li_accounts" class="no_link">Finance <img src="/site_media/images/menu/accounts.png" style="width: 35px; float: right;" /></span></li>
				<li class="for-primary-ent"><a id="li_ac_dashboard" href="/erp/accounts/home/">Dashboard</a></li>
				<li class="for-primary-ent"><a id="li_ledger" href="/erp/accounts/ledger/">Ledger</a></li>
				<li class="for-primary-ent"><a id="li_voucher" href="/erp/accounts/voucher/list/">Voucher</a></li>
				{% if logged_in_user|canApprove:'ACCOUNTS' %}
				<span class="sub-notification-count for-primary-ent" data-badge="Pending" id="get_pending_voucher_count" onclick="onSubmit('voucher_menu_submit')"></span>
				<form action="/erp/accounts/voucher/list/?view=pending" id="voucher_menu_submit" method="post" target="_blank">{% csrf_token %}
					<input type="hidden" name="voucher_fromdate" value="" id="get_pending_voucher_startdate"/>
				</form>
				{% endif %}
				<li class="for-primary-ent"><a id="li_brs" href="/erp/accounts/brs">Bank Reconciliation</a></li>
				<li class="for-primary-ent"><a id="li_payment" href="/erp/accounts/payments">Bill Settlements</a></li>
				<li><a id="li_statements" href="/erp/accounts/trial_balance/">P&L Statements</a></li>
				<li><a id="li_project_forecast" href="/erp/purchase/project_fore_cast/">CashFlow Statement</a></li>
			    <li class="for-primary-ent"><a class="li_slide_down no_link">Reports</a></li>
	            <li class="left-menu-child gstr1_purchase_report for-primary-ent"><a id="li_gstr_purchase_report" href="/erp/accounts/gstr1-purchase-report-statement/">Purchase Register <span class="super-markable-text">GST Related</span></a></li>
				<li class="left-menu-child gstr2_purchase_report for-primary-ent"><a id="li_gstr2_purchase_report" href="/erp/accounts/gstr2-purchase-report-statement/">GSTR-2 Purchase Report</a></li>
	            <li class="left-menu-child gstr1_sales_report for-primary-ent" ><a id="li_gstr_sales_report" href="/erp/accounts/gstr1-sales-report-statement/">GSTR-1 Sales Report</a></li>
	            <li class="left-menu-child gstr3b_reconciliation for-primary-ent" ><a id="li_gstr3b_reconciliation" href="/erp/accounts/gstr3b-reconciliation-report-statement/">GSTR-3B</a></li>
				<li class="left-menu-child cashflow_overview for-primary-ent" ><a id="li_cashflow_overview" href="/erp/sales/get_project_wise_cashflow_overview/">CashFlow Overview</a></li>
				<li class="left-menu-child cashflow_overview for-primary-ent" ><a id="li_consolidated_report" href="/erp/sales/consolidated_cashflow_report/">Consolidated Report</a></li>
				<li class="left-menu-child cashflow_overview for-primary-ent" ><a id="li_tcs_report" href="/erp/accounts/tcs_report/">TCS Report</a></li>
				<li class="left-menu-child cashflow_overview for-primary-ent" ><a id="li_gv_bv_report" href="/erp/accounts/gvbv_report/">Gv-Bv GSTR Report</a></li>
				<li class="left-menu-child cashflow_overview for-primary-ent" ><a id="li_sales_itemwise_report" href="/erp/accounts/sales_item_wise_report/">Sales Item wise Report</a></li>
				<li class="left-menu-child cashflow_overview for-primary-ent" ><a id="li_party_aging_report" href="/erp/accounts/outstanding_report/">Party Aging Report</a></li>
			</ul>
		</div>
		<div class="left-menu-container" id="id_menu_audit">
			<ul class="nav nav-pills nav-stacked custom-left-nav">
				<li class="menu-item-header"><span id="li_audit" class="no_link">Audit <img src="/site_media/images/menu/audit.png" style="width: 35px; float: right;" /></span></li>
				<li><a id="li_internal_control" href="/erp/auditing/icd/">Internal Control </a></li>
				{% if logged_in_user|canApprove:'ICD' %}
				{% if module_access.dummy_icd %}
				<span class="sub-notification-count" data-badge="Pending" id="icd_pending_count" onclick="onSubmit('icd_menu_submit')"></span>
				{% endif %}
						<form action="/erp/auditing/icd/?view=pending" id="icd_menu_submit" method="post" target="_blank">{% csrf_token %}
							<input type="hidden" name="icd_fromdate" value="" id="get_pending_icd_startdate"/>
							<input type="hidden" name="icd_status" value="1" id="get_pending_icd_status"/>
						</form>
				{% endif %}
				<li><a id="li_expenses" href="/erp/expenses/home">Expenses</a></li>
				{% if logged_in_user|canApprove:'ICD' or logged_in_user|canApprove:'EXPENSES' %}
				<span class="sub-notification-count" data-badge="Pending" id="expenses_pending_count" onclick="onSubmit('expenses_menu_submit')"></span>
				<form action="/erp/expenses/home/<USER>" id="expenses_menu_submit" method="post" target="_blank">{% csrf_token %}
							<input type="hidden" name="expenses_fromdate" value="" id="get_pending_expenses_startdate"/>
							<input type="hidden" name="expenses_status" value="0" id="get_pending_expenses_status"/>
						</form>
				{% endif %}
				<li><a id="credit_debit" href="/erp/auditing/icd/?is_credit_debit=True">Credit/Debit Note</a></li>
				{% if logged_in_user|canApprove:'ICD' %}
				<span class="sub-notification-count" data-badge="Pending" id="get_pending_credit_debit_count" onclick="onSubmit('credit_debit_menu_submit')"></span>
				<form action="/erp/auditing/icd/?is_credit_debit=True&view=pending" id="credit_debit_menu_submit" method="post" target="_blank">{% csrf_token %}
					<input type="hidden" name="credit_fromdate" value="" id="get_pending_credit_debit_startdate"/>
					<input type="hidden" name="credit_status" value="1" id="get_pending_credit_debit_status"/>
				</form>
				{% endif %}
			</ul>
		</div>
		<div class="left-menu-container" id="id_menu_purchase">
			<ul class="nav nav-pills nav-stacked custom-left-nav" >
				<li class="menu-item-header"><span id="li_purchase" class="no_link">Purchase <img src="/site_media/images/menu/purchase.png" style="width: 35px; float: right;" /></span></li>
				<li class="for-primary-ent"><a id="li_po_dashboard" href="/erp/purchase/home">Dashboard</a></li>
                <li class="for-primary-ent"><a id="li_indent" class="for-primary-ent" href="/erp/stores/indent_list/">Purchase Indent</a></li>
				<li class="po_side_menu"><a id="li_purchase_order" href="/erp/purchase/po_list/">Purchase Order</a></li>
				{% if logged_in_user|canApprove:'PURCHASE' %}
				<span class="sub-notification-count" id="get_pending_po_count" data-badge="Pending" onclick="onSubmit('po_menu_submit')"></span>
				<form action="/erp/purchase/po_list/?view=pending" id="po_menu_submit" method="post" target="_blank">{% csrf_token %}
					<input type="hidden" name="po_fromdate" value="" id="get_pending_po_startdate"/>
					<input type="hidden" name="po_status" value="0" id="get_pending_po_status"/>
				</form>
				{% endif %}
				<li class="po_side_menu"><a id="li_job_order" href="/erp/purchase/jo_list/">Job Order</a></li>
				{% if logged_in_user|canApprove:'PURCHASE' %}
				<span class="sub-notification-count" id="get_pending_jo_count" data-badge="Pending" onclick="onSubmit('jo_menu_submit')"></span>
				<form action="/erp/purchase/jo_list/?view=pending" id="jo_menu_submit" method="post" target="_blank">{% csrf_token %}
					<input type="hidden" name="jo_fromdate" value="" id="get_pending_jo_startdate"/>
					<input type="hidden" name="jo_status" value="0" id="get_pending_jo_status"/>
				</form>
				{% endif %}
				<li class="for-primary-ent"><a class="li_slide_down no_link">Reports</a></li>
			    <li class="left-menu-child po_wise_report for-primary-ent"><a id="li_po_report" href="/erp/purchase/po/reports/">P.O Wise Reports</a></li>
				<li class="left-menu-child material_wise_report for-primary-ent"><a id="li_material_report" href="/erp/purchase/po/materialwisereports/">Material Wise Reports</a></li>
				<li class="left-menu-child inv_custom_report_purchase for-primary-ent"> <a id="li_custom_report_purchase" href="/erp/reports/purchase">Custom Purchase Report</a></li>
			</ul>
		</div>
		<div class="left-menu-container" id="id_menu_sales">
			<ul class="nav nav-pills nav-stacked custom-left-nav" id="nav_temp_sales">
				<li class="menu-item-header"><span id="li_sales" class="no_link">Sales <img src="/site_media/images/menu/sales.png" style="width: 35px; float: right;" /></span></li>
				<li class="for-primary-ent"><a id="li_invoice_dashboard" href="/erp/sales/home">Dashboard</a></li>
				<li class="for-primary-ent"><a id="li_sales_estimate" href="/erp/sales/sales_estimate/view/">Sales Estimate</a></li>
				{% if logged_in_user|canApprove:'SALES' %}
					<span class="sub-notification-count for-primary-ent" data-badge="Pending" id="get_pending_se_count" onclick="onSubmit('se_menu_submit')"></span>
					<form action="/erp/sales/sales_estimate/view/?type=se&view=pending" id="se_menu_submit" method="post" target="_blank">{% csrf_token %}
						<input type="hidden" name="se_status" value="0"/>
						<input type="submit" class="hide" />
					</form>
				{% endif %}
				<li><a id="li_oa" href="/erp/sales/oa/view/">Order Acknowledgement</a></li>
				{% if logged_in_user|canApprove:'SALES' %}
				<span class="sub-notification-count" data-badge="Pending" id="get_pending_oa_count" onclick="onSubmit('oa_menu_submit')"></span>
				<form action="/erp/sales/oa/view/?type=oa&view=pending" id="oa_menu_submit" method="post" target="_blank">{% csrf_token %}
					<input type="hidden" name="oa_status" value="0"/>
					<input type="submit" class="hide" />
				</form>
				{% endif %}
				<li><a id="li_invoice" href="/erp/sales/invoice_list/">Invoice</a></li>
				{% if logged_in_user|canApprove:'SALES' %}
				<span class="sub-notification-count" data-badge="Pending" id="get_pending_invoice_count" onclick="onSubmit('invoice_menu_submit')"></span>
				<form action="/erp/sales/invoice_list/?view=pending&type=sales" id="invoice_menu_submit" method="post" target="_blank">{% csrf_token %}
					<input type="hidden" name="oa_status" value="0"/>
					<input type="submit" class="hide" />
				</form>
				{% endif %}
				<li class="for-primary-ent"><a id="li_sr" href="/erp/sales/sr_list/">Sales Return</a></li>
				{% if logged_in_user|canApprove:'STORES' %}
				<span class="sub-notification-count for-primary-ent" data-badge="Pending" id="get_pending_sr_count" onclick="onSubmit('sr_menu_submit')"></span>
				<form action="/erp/sales/sr_list/?view=pending" id="sr_menu_submit" method="post" target="_blank">{% csrf_token %}
					<input type="hidden" name="grn_fromdate" value="" id="get_pending_sr_startdate"/>
				</form>
				{% endif %}
				<li><a class="li_slide_down no_link">Reports</a></li>
				<li class="left-menu-child invoice_report for-primary-ent"> <a id="li_status_report" href="/erp/sales/reports/">Status Report</a></li>
				<li class="left-menu-child inv_tax_report for-primary-ent"> <a id="li_tax_report" href="/erp/sales/inv_tax_report/">Tax Report</a></li>
				<li class="left-menu-child material_tax_report for-primary-ent"> <a id="li_sales_material_report" href="/erp/sales/material_tax_report/">Material Report</a></li>
				<li class="left-menu-child inv_sales_report"> <a id="li_sales_report" href="/erp/sales/sales-report-statement/">Sales Report</a></li>
				<li class="left-menu-child inv_custom_report for-primary-ent"> <a id="li_custom_report" href="/erp/reports/sales">Custom Sales Report</a></li>
				<li class="left-menu-child oa_report"> <a id="li_oa_report" href="/erp/sales/oa_report/">OA Report</a></li>
			</ul>
		</div>
		<div class="left-menu-container" id="id_menu_stores">
			<ul class="nav nav-pills nav-stacked custom-left-nav">
				<li class="menu-item-header"><span id="li_stores" class="no_link">Stores <img src="/site_media/images/menu/stores.png" style="width: 35px; float: right;" /></span></li>
				<li class="for-primary-ent"><a id="li_st_dashboard" href="/erp/stores/home">Dashboard</a></li>
				<li><a id="li_receipt" href="/erp/stores/grn_list/">Goods Receipt (GRN)</a> </li>
				{% if logged_in_user|canApprove:'STORES' %}
				<span class="sub-notification-count" data-badge="Pending" id="get_pending_grn_count" onclick="onSubmit('grn_menu_submit')"></span>
				<form action="/erp/stores/grn_list/?view=pending" id="grn_menu_submit" method="post" target="_blank">{% csrf_token %}
					<input type="hidden" name="grn_fromdate" value="" id="get_pending_grn_startdate"/>
				</form>
				{% endif %}
				<li class="for-primary-ent"><a id="li_dc"  href="/erp/stores/dc_list/">Delivery Challan</a></li>
				{% if logged_in_user|canApprove:'STORES' %}
				<form action="/erp/stores/dc_list/?view=pending&type=dc" id="dc_menu_submit" method="post" target="_blank">{% csrf_token %}
					<input type="hidden" name="po_status" value="0"/>
					<input type="submit" class="hide" />
				</form>
				<span class="sub-notification-count for-primary-ent" data-badge="Pending" id="get_pending_dc_count" onclick="onSubmit('dc_menu_submit')"></span>
				{% endif %}
				<li class="for-primary-ent"><a id="li_material_requisition" href="/erp/stores/material_requsition_list/">Material Requisition</a></li>
				<form id="stock-transfer-form" method="post" action="/erp/stores/json/stock_transfer_list/">
				{% csrf_token %}
				<input type="hidden" name="enterprise_id" id="enterprise_id_input" value="{{ logged_in_user.enterprise.id }}">
					<input type="hidden" name="user_id" id="user_id_input" value="{{ logged_in_user.user_id }}"><!-- Hidden field for enterprise_id -->
				</form>
<!--				<a role="button" href="#" class="btn btn-add-new pull-right" data-tooltip="tooltip" title="Back" onclick="submitStockTransferForm()">-->
				<li class="for-primary-ent"><a role="button" id="li_stocktransfer" href="/erp/stores/json/stock_transfer_list/?enterprise_id={{logged_in_user.enterprise.id}}&user_id={{logged_in_user.user_id}}" onclick="submitStockTransferForm(); return false;">Internal Stock Transfer</a></li>

				<li class="for-primary-ent"><a id="li_issue_stores" href="/erp/stores/issue_list/">Issue</a></li>
	            <li><a class="li_slide_down no_link">Reports</a></li>
	            <li class="left-menu-child invoice_report store_stock_report for-primary-ent"><a id="li_stock_report" href="/erp/stores/stock-statement">Stock Report</a></li>
				<li class="left-menu-child invoice_report store_stock_report for-primary-ent"><a id="li_stock_report_location" href="/erp/stores/location_wise_stock_summary/">Location Wise Stock Report</a></li>
	            <li class="left-menu-child invoice_report store_grn_report"> <a id="li_grn_report" href="/erp/stores/grn-report-statement/">GRN Report</a></li>
	            <li class="left-menu-child inv_tax_report store_dc_report for-primary-ent"> <a id="li_dc_report" href="/erp/stores/dc-report/">DC Report</a></li>
				<li class="left-menu-child inv_tax_report store_issue_stock_report for-primary-ent"> <a id="li_issue_stock_report" href="/erp/stores/issue_stock_report/">Internal Stock Flow</a></li>
				<li class="for-primary-ent"><a id="li_issue_report" href="/erp/stores/materialhistorypage">Material Receipt / Issue Report</a></li>

			</ul>
		</div>
		<div class="left-menu-container" id="id_menu_production_planning">
			<ul class="nav nav-pills nav-stacked custom-left-nav">
				<li class="menu-item-header"><span id="li_production_planning" class="no_link">Production<img src="/site_media/images/menu/production.png" style="width: 35px; float: right;" /></span></li>
				<li class="hide"><a id="li_wo_dashboard" href="">Dashboard</a></li>
				<li><a id="li_manufacture_indent" href="/erp/production/manufacturing_indent/">Manufacturing Indent</a></li>
				<li><a id="li_production_plan" href="/erp/production/production_plan/">Production Plan</a></li>
				<span class="sub-notification-count" id="get_pending_pp_count" data-badge="Pending" onclick="onSubmit('pp_menu_submit')"></span>
				<form action="/erp/production/production_plan/?view=pending" id="pp_menu_submit" method="post" target="_blank">{% csrf_token %}
					<input type="hidden" name="wo_fromdate" value="" id="get_pending_wo_startdate"/>
					<input type="hidden" name="wo_status" value="0" id="get_pending_wo_status"/>
				</form>
				<li><a id="li_issue" href="/erp/stores/issue_list/">Issue</a></li>
				<li><a id="li_irn" href="/erp/stores/irn_list/">Issue Return / Internal Receipt</a></li>
				{% if logged_in_user|canApprove:'STORES' %}
				<span class="sub-notification-count" data-badge="Pending" id="get_pending_irn_count" onclick="onSubmit('irn_menu_submit')"></span>
				<form action="/erp/stores/irn_list/?view=pending" id="irn_menu_submit" method="post" target="_blank">{% csrf_token %}
					<input type="hidden" name="grn_fromdate" value="" id="get_pending_irn_startdate"/>
				</form>
				{% endif %}
				<li><a id="li_shortage" href="/erp/stores/materialshortage">Shortage Calculator</a></li>
<!--				<li><a id="li_shortage_PP" href="/erp/stores/materialshortage_pp">Shortage Calculator (Production Plan Bases)</a></li>-->
				<li><a id="li_job_in_register" href="/erp/stores/jobinreport">Job-In Stock Register</a></li>
				<li><a id="li_job_out_register" href="/erp/stores/json/joboutreport">Job-Out Stock Register</a></li>
			</ul>
		</div>
		<div class="left-menu-container" id="id_menu_master">
			<ul class="nav nav-pills nav-stacked custom-left-nav">
				<li class="menu-item-header"><span id="li_profile" class="no_link">Profiles <img src="/site_media/images/menu/master.png" style="width: 35px; float: right;" /></span></li>
				<li><a id="li_party" href="/erp/masters/party_list/">Party</a></li>
				<li><a id="li_goods" href="/erp/masters/catalogues/">Goods</a></li>
				{% if logged_in_user|canApprove:'MASTERS' %}
				<span class="sub-notification-count" data-badge="Pending" id="get_pending_material_count" onclick="onSubmit('material_menu_submit')"></span>
				<form action="/erp/masters/catalogues/?type=material&view=pending" id="material_menu_submit" method="post" target="_blank">{% csrf_token %}
					<input type="hidden" name="material_status" value="0"/>
					<input type="submit" class="hide" />
				</form>
				{% endif %}
				<li><a id="li_service" href="/erp/masters/services/">Service</a></li>
				{% if logged_in_user|canApprove:'MASTERS' %}
				<span class="sub-notification-count" data-badge="Pending" id="get_pending_service_count" onclick="onSubmit('service_menu_submit')"></span>
				<form action="/erp/masters/catalogues/?type=service&view=pending" id="service_menu_submit" method="post" target="_blank">{% csrf_token %}
					<input type="hidden" name="service_status" value="0"/>
					<input type="submit" class="hide" />
				</form>
				{% endif %}
				<li><a id="li_tax" href="/erp/masters/tax">Tax</a></li>
			</ul>
		</div>
		<div class="left-menu-container" id="id_menu_hr">
			<ul class="nav nav-pills nav-stacked custom-left-nav">
				<li class="menu-item-header"><span class="no_link" id="li_hr">HR <img src="/site_media/images/menu/hr.png" style="width: 35px; float: right;" /></span></li>
				<li><a id="li_employee" href="/erp/hr/employee">Employees</a></li>
				<li><a id="li_paystructure" href="/erp/hr/pay">Pay Structure</a></li>
				<li><a id="li_attendance" href="/erp/hr/attendance">Attendance</a></li>
			</ul>
		</div>
		<div class="menu-backdrop hide"></div>
		<div id="page-wrapper">
			{% block home %}{% endblock %}
			{% block erpHome %}{% endblock %}
			{% block reportsHome %}{% endblock %}
			{% block sidebar %}{% endblock %}
			{% block reports %}{% endblock %}
			{% block internal_error %}{% endblock %}
	    </div>
	    <div class="right-side-menu">
			<div class="right-side-menu-items hide" id="id-change-log" style="padding: 9px 1px 3px;" >
				<a role="button" id="btn_change_log" class="change_log" data-tooltip="tooltip" data-placement="left" title="Activity&nbsp;Log" aria-hidden="true">
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="18" fill="#004195" class="icon icon-xlg"><path d="M.6 256.1c0-7.7.4-15.6 1.1-23.3.8-8.8 7.4-15.1 15.6-15.1 8.4 0 15.7 7 15.7 15 0 .4 0 .8-.1 1.2-.7 7.1-1 14.3-1 21.5 0 43.8 12.3 86 35.5 122.2 21.5 33.5 51.7 60.8 87.2 78.8 37.5 19 79.9 27 122.7 23.1 43.1-3.9 83.3-19.7 116.2-45.5 54.6-42.9 87.2-109.5 87.2-178.3 0-14.8-1.5-29.7-4.6-44.1-9.2-43.4-29.9-82.4-60.1-112.8-29.5-29.7-66.4-50.6-106.6-60.4-41.6-10.1-84.8-8.3-125 5.4-38.5 13.1-74.1 37.5-100.4 68.8l-1.3 1.6 62.5.5c8.7.1 15.3 6.8 15.3 15.8 0 4.4-1.7 8.5-4.6 11.4-2.8 2.8-6.6 4.3-10.9 4.2l-95-.8c-8.6-.1-15.6-7.1-15.6-15.6l.8-93.2c.1-8.8 6.9-15.3 15.8-15.3 8.8 0 15.5 6.6 15.5 15.3l-.4 48.8 1.7-1.9c3.2-3.6 6.6-7 9.9-10.2 34.8-33.7 78.8-56.8 127-66.9C253.5-3.8 303 .1 347.9 17.7c43.5 17 81 44.5 108.3 79.3 29.8 37.9 48.5 82.9 54.1 130 1.1 9.6 1.7 19.3 1.7 29 0 40.1-9.8 80.5-28.4 116.8-18.7 36.5-45.8 68.1-78.6 91.6-37.5 26.8-82 42.9-128.8 46.6-46.8 3.6-93.3-5.4-134.5-26-41.1-20.6-75.8-51.7-100.4-90C14.7 353.4.6 305.4.6 256.1z"></path><path d="M353.9 355.2c-2.5 0-4.9-.6-7.2-1.8l-98.6-49.2c-5.2-2.4-8.5-7.3-8.5-13.1v-148c0-8.4 7.1-14.7 16-14.7s16 6.3 16 14.7v137.7L362 326c6.3 3.2 9.3 9.6 7.8 16.3-1.8 7.5-8.5 12.9-15.9 12.9z"></path></svg>
				</a>
			</div>
		    <div class="right-side-menu-items internal-tour hide" id="int-tour">
				<i class="fa fa-map" style="font-size: 18px;" data-tooltip="tooltip" data-placement="left" title="Tour" aria-hidden="true"></i>
			</div>
			<button class="show_template_label hide" role="menu" id="show_template_label" type="button" style="outline:none;" data-tooltip="tooltip" data-placement="bottom" title="Show Invoice Template Label">
				<i class="fa fa-tags" aria-hidden="true" style="font-size: 16px;margin-left: 2px;"></i>
			</button>
			<a class="hide for-primary-ent" role="menu" id="template_configuration" type="button" style="outline:none;" data-tooltip="tooltip" data-placement="bottom" title="Print Template configuration" target="_blank">
				<i class="fa fa-cogs other_setting_menu" aria-hidden="true"></i>
			</a>
           <div class="right-side-menu-items hide" id="closed_books" role="button" style="outline:none;" data-toggle="modal" data-target="#modal_closed_books" data-tooltip="tooltip" data-placement="bottom" title="Closed Books">
				<img src="/site_media/images/closed_books.png" width="44">
			</div>
			<div class="right-side-menu-items-bottom">
				<div class="right-side-menu-items new-tour hide" id="whats-new_tour">
					<i class="fa fa-gift" style="font-size:18px;" data-tooltip="tooltip" data-placement="left" title="What's&nbsp;new" arial-hidden="true"></i>
				</div>
				<div class="right-side-menu-items" id="hotkeys" data-toggle="modal" data-target="#hotkey_list_modal">
					<i class="fa fa-keyboard-o" style="font-size: 18px;" data-tooltip="tooltip" data-placement="left" title="Hotkeys" aria-hidden="true"></i>
				</div>
				<div class="right-side-menu-items" onclick="showCalculator();">
					<i class="fa fa-calculator" style="font-size: 17px;" data-tooltip="tooltip" data-placement="left" title="Calculator" aria-hidden="true"></i>
				</div>
			</div>
	    </div>
	</div>

	<div id="show_remarks_history_modal" class="modal fade"  role="dialog">
		<div class="modal-dialog modal-md">
		    <div class="modal-content">
	            <div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">&times;</button>
					<h4 class="modal-title">Remarks</h4>
				</div>
				<input type="hidden" id="remarks_list_json" value="{{remarks_list}}">
			    <div class="modal-body" id="remarks_list">

				</div>
				<div class="modal-footer" style="border-top: none;"></div>
		    </div>
		</div>
	</div>

	<div id="xserpDownloadAppModal" class="modal fade"  role="dialog">
		<div class="modal-dialog" style="width: 90%; max-width: 600px;">
		    <div class="modal-content">
	            <div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">&times;</button>
					<h4 class="modal-title">Download the App</h4>
				</div>
			    <div class="modal-body text-center">
					<h4>For better Usage experience download the application from <span class="mobile-type">PlayStore!</span></h4>
					<a href="https://play.app.goo.gl/?link=https://play.google.com/store/apps/details?id=com.schnell.xserp" target="_blank">
						<img class="img-responsive" style="margin: 0 auto;" src="/site_media/images/google_play.png" />
					</a>
				</div>
				<div class="modal-footer" style="border-top: none;"></div>
		    </div>
		</div>
	</div>

	{% include "template-supporting-files/calculator.html" %}
	{% include "template-supporting-files/hot_keys.html" %}
	{% include "modal-window/subscription.html" %}
	{% include "modal-window/whats-new.html" %}

	<footer>
		<a  href="/docs/release-notes/2-18-2-b/" target="_blank" >
			Version {{ current_version }}
		</a>
		 | © 2022,
		<a href="http://www.schnellenergy.com/" target="_blank" >
			Schnell Energy Equipments (P) Ltd.
		</a>, Coimbatore |
		<a  href="/erp/public/terms" target="_blank" >
			Terms and Conditions
		</a> |
		<a href="/erp/public/privacy" target="_blank" >
			Privacy Policy
		</a>
	</footer>
</body>
</html>

<script language="JavaScript">
function onSubmit(formId){
	$('#' + formId).submit();
}

var myCalendar;
function doOnLoad() {
	myCalendar = new dhtmlXCalendarObject(["issue_date","calendar1","calendar2","id_voucher-voucher_date","expense_date","desc_date","id_indent-expected_date","id_voucher-bill_date","bill_date"]);
	myCalendar.setDateFormat("%Y-%m-%d");
}
jQuery(document).ready(function () {
	$('#table_tab').easyResponsiveTabs({
        type: 'default', //Types: default, vertical, accordion
        width: 'auto', //auto or any width like 600px
        fit: true, // 100% fit in a container
        closed: 'accordion', // Start closed if in accordion view
        tabidentify: 'hor_1', // The tab groups identifier
        activate: function (event) { // Callback function if tab is switched
            var $tab = $(this);
            var $info = $('#nested-tabInfo');
            var $name = $('span', $info);
            $name.text($tab.text());
            $info.show();
        }
    });
    validateTextInputs();
    getNotificationCount();
    DateRangeInit();
    getAllMenuPendingCount();
    getStartDate();
    $.ajax({
        url: '/erp/json/get_previous_closure_date/',
        type: "POST",
        dataType: "json",
        data: {
            enterprise_id: $("#enterprise_id").val()
        },
        success: function (data) {
            $("#enterprise_previous_closure_date").val(data);
            SingleDatePickerInit();
        },
        error: function () {
        	SingleDatePickerInit();
        }
    });
    if($(".enterprise_name").width() > 450) {
    	$(".span-profile-name").css({fontSize: "0.70em"});
    }
    else if($(".enterprise_name").width() > 380) {
    	$(".span-profile-name").css({fontSize: "0.80em"})
    }
    else if($(".enterprise_name").width() > 325) {
    	$(".span-profile-name").css({fontSize: "0.95em"})
    }
    if($(".user_profile_image").text() == "I") {
    	$(".user_profile_image").css({paddingLeft: "15px" });
	}
	else if($(".user_profile_image").text() == "W" || $(".user_profile_image").text() == "M") {
		$(".user_profile_image").css({paddingLeft: "9px" });
	}
	else {
		$(".user_profile_image").css({paddingLeft: "11px" });
	}
    setUserDetailsInCookie();
    validateMobileDevice();
    setProjectInMenu();
    setEnterpriseProject();
});

function remarks_history(){
   $("#show_remarks_history_modal").modal('show');
}

if (jQuery(this).width() <= 689 ) {
	jQuery(document).ready(function () {
		$('#table_tab h2:first').click();
	});
}

$(document).ready(function() {
	MenuFormation();
	$(window).resize(function() {
		MenuFormation();
	});
	$("#li_print_template").click(function(){
		$(".li_view_template").slideToggle(function(){
			if($(".li_view_template").is(":visible")) {
				$(".fa-for-arrow").removeClass("fa-caret-down").addClass("fa-caret-up");
			}
			else {
				$(".fa-for-arrow").removeClass("fa-caret-up").addClass("fa-caret-down");
			}
		});
		setTimeout(function(){
			$("#other-config").addClass("open");
		},1);
	});
});


function MenuFormation(){
	if ($(window).width() <= 1100) {
		$('#horizontal-menu').hide();
		$("#menu-toggle").removeClass('hide');
	}
	else {
		$('#horizontal-menu').show();
		$("#menu-toggle").addClass('hide');
	}
}

function navContainerChange(x) {
	x.classList.toggle("change");
	$("#horizontal-menu").slideToggle(0);
}

$('body').click(function(evt){
	 if($(evt.target).attr('class') == "deleteNotification" ||
	    $(evt.target).attr('class') == "tdNotification" ||
		evt.target.id == "divNotificationBadge" ||
		evt.target.id == "imgNotificationBadge" ||
		evt.target.id == "alertbadge") {
		if($(evt.target).attr('class') != "deleteNotification") {
			getNotification();
		}
		if($("#alertbadge").attr('data-badge') >= 1) {
			$("#tableNotificationContainer").show();
		}
	}
	else {
		$("#tableNotificationContainer").hide();
	}
});
		
$(".slide_container_part").click(function(){
	if($(this).attr("id") != "menu_calc"){
		var isCurrectActive = $("#id_"+$(this).attr('id')).is(":visible");
		$(".slide_container_part").removeClass('active');
		
		if(isCurrectActive) {
			$(".left-menu-container").hide('slide', {direction: 'left'}, 400);
			$(".menu-backdrop").addClass('hide');
			$(this).removeClass('active');
			$(".slide_container_part.selected").css('background', '#209be1');
		}
		else {
			$(".left-menu-container").hide('slide', {direction: 'left'}, 400);
			$("#id_"+$(this).attr('id')).show('slide', {direction: 'left'}, 400);
			$(".menu-backdrop").removeClass('hide');
			$(this).addClass('active');
			$(".slide_container_part.selected").css('background', 'transparent');
		}
	}
});

$(".menu-backdrop, .main-menu-container").click(function(){
	$(".left-menu-container").hide('slide', {direction: 'left'}, 400);
	$(".menu-backdrop").addClass('hide');
	$(".slide_container_part").removeClass('active');
	$(".slide_container_part.selected").css('background', '#209be1');
});

$(".menu-backdrop").click(function(){
	$("#id_menu_mobile").hide('slide', {direction: 'left'}, 400);
	$(".menu-backdrop").addClass('hide');
});

$(".navbar-mobile").click(function(){
	$("#id_menu_mobile").show('slide', {direction: 'left'}, 400);
	setTimeout(function(){
		$(".menu-backdrop").removeClass('hide');
	},1);
})

$(".mobile-sidenav-header").click(function(){
	var existingActive = $(".mobile-sidenav-parent.active").attr("id");
	$(".mobile-sidenav-parent").removeClass("active")
	$(".mobile-sidenav-child").slideUp( '400');
	if(existingActive != $(this).closest(".mobile-sidenav-parent").attr("id")) {
		$(this).closest(".mobile-sidenav-parent").addClass("active");
		$(this).closest(".mobile-sidenav-parent").find(".mobile-sidenav-child").slideDown('400');
	}
});
function formatCountWithPlus(count) {
if (count < 10) {
    return count.toString();
  } else if (count < 1000) {
   count = Math.floor(count / 10) * 10;
   return `${count}+`;
  } else if (count < 100000) {
   const roundedCount = Math.floor(count / 100) * 100;
    const formattedCount = (roundedCount / 1000).toFixed(1);
    return `${formattedCount}k+`;
  } else {
   return "1L+";
  }
}

function getAllMenuPendingCount(){
	 $.ajax({
	        url: '/erp/commons/json/get_badge_count/',
	        type: "POST",
	        dataType: "json",
	        data: {"status":"pending"},
	        success: function (data) {
		        if (data.response_message =="Success") {
		            var sales_count = parseInt(data.result.invoice) + parseInt(data.result.oa) + parseInt(data.result.sr) + parseInt(data.result.se);
		            var stores_count = parseInt(data.result.grn) + parseInt(data.result.dc);
		            var auditing_count = parseInt(data.result.icd) + parseInt(data.result.expense) + parseInt(data.result.credit_debit);
					var production_count =parseInt(data.result.irn);
			        $('#dash_sales_count').attr("data-badge", (sales_count > 0 ? formatCountWithPlus(sales_count) : $('#dash_sales_count').hide()));
			        $('#dash_stores_count').attr("data-badge", (stores_count > 0 ? formatCountWithPlus(stores_count): $('#dash_stores_count').hide()));
			        $('#dash_production_count').attr("data-badge", (production_count > 0 ? formatCountWithPlus(production_count): $('#dash_production_count').hide()));
			        $('#dash_audit_count').attr("data-badge", (auditing_count > 0 ? formatCountWithPlus(auditing_count) : $('#dash_audit_count').hide()));
			        $('#dash_voucher_count').attr("data-badge",(data.result.voucher >0 ?formatCountWithPlus(data.result.voucher):$('#dash_voucher_count').hide()));
			        $('#dash_po_count').attr("data-badge",(data.result.po >0 || data.result.jo >0 ?formatCountWithPlus(parseInt(data.result.po) + parseInt(data.result.jo)): $('#dash_po_count').hide()));
			        $('#dash_material_count').attr("data-badge", (data.result.material > 0 ? formatCountWithPlus(parseInt(data.result.material) + parseInt(data.result.service)): $('#dash_material_count').hide()));
			        $('#get_pending_po_count').attr("data-badge",(data.result.po > 0 ? data.result.po : $('#get_pending_po_count').hide()));
			        $('#get_pending_jo_count').attr("data-badge",(data.result.jo > 0 ? data.result.jo : $('#get_pending_jo_count').hide()));
			        $('#get_pending_pp_count').attr("data-badge",(data.result.pp > 0 ? data.result.pp : $('#get_pending_pp_count').hide()));
			        $('#get_pending_voucher_count').attr("data-badge",(data.result.voucher > 0 ? data.result.voucher : $('#get_pending_voucher_count').hide()));
			        $('#get_pending_invoice_count').attr("data-badge",(data.result.invoice > 0 ? data.result.invoice: $('#get_pending_invoice_count').hide()));
			        $('#get_pending_dc_count').attr("data-badge", (data.result.dc > 0 ? data.result.dc: $('#get_pending_dc_count').hide() ));
			        $('#get_pending_oa_count').attr("data-badge", (data.result.oa > 0 ? data.result.oa: $('#get_pending_oa_count').hide()));
			        $('#get_pending_se_count').attr("data-badge", (data.result.se > 0 ? data.result.se: $('#get_pending_se_count').hide()));
			        $('#get_pending_grn_count').attr("data-badge", (data.result.grn > 0 ? data.result.grn: $('#get_pending_grn_count').hide()));
			        $('#get_pending_irn_count').attr("data-badge", (data.result.irn > 0 ? data.result.irn: $('#get_pending_irn_count').hide()));
			        $('#get_pending_sr_count').attr("data-badge", (data.result.sr > 0 ? data.result.sr: $('#get_pending_sr_count').hide()));
			        $('#icd_pending_count').attr("data-badge", (data.result.icd >0 ? data.result.icd: $('#icd_pending_count').hide()));
			        $('#expenses_pending_count').attr("data-badge", (data.result.expense > 0 ? data.result.expense: $('#expenses_pending_count').hide()));
			        $('#get_pending_credit_debit_count').attr("data-badge", (data.result.credit_debit > 0 ? data.result.credit_debit: $('#get_pending_credit_debit_count').hide()));
			        $('#get_pending_material_count').attr("data-badge", (data.result.material > 0 ? data.result.material: $('#get_pending_material_count').hide()));
			        $('#get_pending_service_count').attr("data-badge", (data.result.service > 0 ? data.result.service: $('#get_pending_service_count').hide()));		        }
	        },
	        error: function () {
	            SingleDatePickerInit();
	        }
	 });
}
function getStartDate(){
	 $.ajax({
	        url: '/erp/commons/json/get_initial_date/',
	        type: "POST",
	        dataType: "json",
	        data: { list: ['voucher', 'icd', 'expense', 'credit_debit', 'po', 'grn', 'irn', 'sr'], status:"pending" },
	        success: function (data) {
		        if (data.response_message =="Success") {
			        $('#get_pending_voucher_startdate').val(data.result.voucher);
			        $('#get_pending_icd_startdate').val(data.result.icd);
			        $('#get_pending_expenses_startdate').val(data.result.expense);
			        $('#get_pending_credit_debit_startdate').val(data.result.credit_debit);
			        $('#get_pending_po_startdate').val(data.result.po);
			        $('#get_pending_grn_startdate').val(data.result.grn);
			        $('#get_pending_irn_startdate').val(data.result.irn);
			        $('#get_pending_sr_startdate').val(data.result.sr);
		        }
	        },
	        error: function () {
	            SingleDatePickerInit();
	        }
	 });
}

function setUserDetailsInCookie() {
    document.cookie = "user_name='{{logged_in_user.first_name}} {{logged_in_user.last_name}}'; path=/;";
    document.cookie = "user_email='{{logged_in_user.email}}'; path=/;";
    document.cookie = "user_enterprise='{{logged_in_user.enterprise.name}} ({{logged_in_user.enterprise.code}})'; path=/;";
}

$(".dropdown-header-plus").click(function(){
 	$(this).find(".rotate").toggleClass("down")  ;
});


$(document).ready(function(){
	var project = JSON.parse(localStorage.getItem('project'));
	if(project && project.type == 'Secondary'){
		$('#for-sec-ent-oaT').text('Internal Work Order');
		$('#for-sec-ent-invT').text('Internal Invoice');
	}
	isViewed = [];
	var currentUserId = $(".user_profile_login").text().trim();
	var currentVersion = $("#project_version").val();
	var viewed_user = JSON.parse(localStorage.getItem('is_viewed'));
	var is_new_user = true;
	localStorage.removeItem("is_viewed");
	if(viewed_user) {
		for (var i = 0; i < viewed_user.length; i++){
			if(viewed_user[i].user_id != currentUserId || viewed_user[i].version != currentVersion) {
				if(viewed_user[i].version == currentVersion) {
					isViewed.push(viewed_user[i]);
				}
			}
			else {
				is_new_user = false;
			}
		}
	}
	
	viewedUser = {}
    viewedUser ["user_id"] = currentUserId;
    viewedUser ["version"] = currentVersion;
    isViewed.push(viewedUser);
	localStorage.setItem('is_viewed', JSON.stringify(isViewed));
	if(is_new_user) {
	    $(".whats-new-img-1").attr("src", '/site_media/images/whats_new/1.png')
	    if($("#id_enterprise-address_1").val() != "") {
	    	$("#whatNewModal").modal("show");
		}
	}
});

function validateMobileDevice() {
    const toMatchAndroid = [
        /Android/i,
        /webOS/i,
        /BlackBerry/i,
        /Windows Phone/i
    ];
    const toMatchApple = [
        /iPhone/i,
        /iPad/i,
        /iPod/i
    ];

    var checkForAndroid  = toMatchAndroid.some((toMatchAndroid) => {
        return navigator.userAgent.match(toMatchAndroid);
    });
    var checkForApple  = toMatchApple.some((toMatchApple) => {
        return navigator.userAgent.match(toMatchApple);
    });
    if(checkForAndroid) {
    	$(".mobile-type").text('PlayStore')
    }
    else if(checkForApple) {
    	$(".mobile-type").text('AppStore')
    }

	if(checkForApple || checkForAndroid){
		var isMobileAlert = localStorage.getItem('mobileAlert');
		if (isMobileAlert== null) {
			$('#xserpDownloadAppModal').modal("show");
			setTimeout(function(){
				localStorage.setItem('mobileAlert', true);
			},100);
		}
	}
}

const setProjectInMenu = async () => {
	let formattedProjectList = [];
    const projectList = await getProjects();
    const projectWise = projectList ?projectList.project_wise : null;
    if(!projectWise){
    	$('#li_project_forecast').hide();
    	$('#li_cashflow_overview').hide();
    	$('#li_consolidated_report').hide();
    }
    if(projectList.projects.length > 0){
    	formattedProjectList = formatProjectList(projectList);
    }
    const sessionProject = localStorage.getItem("project");
    const project = JSON.parse(sessionProject);
    let selectedItemId = ''
    if(project){selectedItemId = project.id;}
    let treeHTML = '';
    function generateTree(parentId) {
        const children = formattedProjectList.filter(project => project.parent_id === parentId);
        const initialDisplay = parentId === 0 ? '' : 'style="display: none;"';
        treeHTML += `<ul ${initialDisplay}>`;
        children.sort((a, b) => a.name.localeCompare(b.name));
        children.forEach(child => {
            const bgColor = child.id === selectedItemId ? 'background-color: #e6e6e6;' : '';
            const isAccessible = projectList.permission_list.includes(child.id);
            treeHTML += `<li data-id="${child.id}">`;
            if (isAccessible) {
				treeHTML += `${hasChildren(child.id) ? '<i class="fa fa-plus fa-for-arrow" aria-hidden="true" onClick="toggleExpandCollapse(this)" style="margin-right: 10px;font-size: 10px;border: 1px solid black;padding: 1px;"></i>' : ''}`;
				treeHTML += `<a style="padding:10px;${bgColor}" onClick="updateProject(${child.id}, ${child.parent_id}, '${child.name}', ${child.enterprise_id}); partyChange()">${child.name}</a>`;
			} else {
				treeHTML += `${hasChildren(child.id) ? '<i class="fa fa-plus fa-for-arrow" aria-hidden="true" onClick="toggleExpandCollapse(this)" style="margin-right: 10px;font-size: 10px;border: 1px solid black;padding: 1px;"></i>' : ''}`;
				treeHTML += `<a class="disabled-link" style="padding:10px;${bgColor}" disabled>${child.name}</a>`;
			}
            generateTree(child.id);
            treeHTML += `</li>`;
        });
        treeHTML += `</ul>`;
    }

    function hasChildren(nodeId) {
        return formattedProjectList.some(project => project.parent_id === nodeId);
    }
    generateTree(0);
    $(".project_list").html(treeHTML);
    let currentItemId = selectedItemId;
    while (currentItemId !== 0) {
        $(`.project_list [data-id="${currentItemId}"]`).parent("ul").show();
        const parentItem = formattedProjectList.find(item => item.id === currentItemId);
        currentItemId = parentItem ? parentItem.parent_id : 0;
    }
}
function toggleExpandCollapse(button) {
    const $toggleButton = $(button);
    const $listItem = $toggleButton.parent();
    const $ul = $listItem.children("ul");
    $ul.toggle();
    $toggleButton.toggleClass("fa-minus fa-plus");
}

const toggleProject = () => {
	$(".project_list").toggleClass("hide")
	$("#page-wrapper").one("click", function() {
		$(".project_list").addClass("hide");
	});
}

const updateProject = (id, parentId, name, enterprise_id) => {
	const data = {
		id: id,
		type: parentId === 0 ? "Primary" : "Secondary",
		name: name,
		enterprise_id :enterprise_id
	}
	localStorage.setItem("project", JSON.stringify(data));
	}
function partyChange(){
	const sessionProject = localStorage.getItem("project");
	if(sessionProject) {
		const project = JSON.parse(sessionProject);
		$.ajax({
				url: "/erp/sales/update/enterprise_id/",
				type: "GET",
				data: {"enterprise_id": project.enterprise_id, "type" : project.type},
				async : false,
				success: function (response) {
					window.location.href = "/erp/home/";
				}
		});
	}
}

const setEnterpriseProject = async () => {
	const sessionProject = localStorage.getItem("project");
	if(sessionProject) {
		const project = JSON.parse(sessionProject);
		let project_name = '';
		const projectList = await getProjects();
		const formattedProjectList = formatProjectListForDropdown(projectList);
		const filteredProjects = formattedProjectList.filter(item => item.id === project.id);
		if(filteredProjects.length>0){
		project_name = filteredProjects[0].name.replace(/>/g, '<i class="fa fa-chevron-circle-right" aria-hidden="true"></i>');}
		$(".enterprise_name").html(project_name);
		$("#enterprise_id").val(project.enterprise_id);
		if(sessionProject && project.type === "Primary") {
			$("body").addClass("primary-enterprise").removeClass("secondary-enterprise");
		}
		else {
			$("body").addClass("secondary-enterprise").removeClass("primary-enterprise");
			document.getElementById('li_invoice').textContent = 'Internal Invoice';
			document.getElementById('li_oa').textContent = 'Internal Work Order';
			$('#li_sales_report').text('Internal Invoice Report');
        	$('#li_oa_report').text('IWO Report');
		}
	}

}
$(document).ready(function() {
    $('.project_list').on('mouseenter', 'li > a', function() {
        $(this).closest('ul').children('li').removeClass('hovered');
        $(this).parent().addClass('hovered');
    }).on('mouseleave', 'li > a', function() {
        $(this).parent().removeClass('hovered');
    });
});

	 function submitStockTransferForm() {
			$('#stock-transfer-form').submit();
    }
</script>