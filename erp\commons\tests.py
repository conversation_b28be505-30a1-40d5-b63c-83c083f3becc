"""
"""
__author__ = 'nandha'

from datetime import datetime

from django.test import TestCase

from erp.commons import logger
from erp.commons.backend import BadgeService


class TestSubscription(TestCase):

	def setUp(self):
		pass

	def tearDown(self):
		pass

	def test_fetch_all_badge_count(self):
		start = datetime.now()
		data = BadgeService().fetchBadgeCount(enterprise_id=102, status="pending", user_id=69)
		end = datetime.now()
		processing_time = end-start
		logger.info("BadgeCount Processing time: %s " % [processing_time.seconds])
		logger.info("BadgeCount Data: %s " % data)
		self.assertLessEqual(processing_time.seconds, 5)
