import copy
import io
import operator
import os
import re
import sys
from datetime import *

import cv2
import fuzzyset
import numpy as np
import simplejson
from dateutil.parser import *
from django.core.files.storage import FileSystemStorage
from django.http import HttpResponse
from google.cloud import vision
from google.cloud.vision import types
from statistics import mode

import settings
from erp.auth import ENTERPRISE_GST_SESSION_KEY, ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.masters.backend import MasterDA<PERSON>
from erp.stores import logger


def save_uploaded_file(request):
	try:
		request_handler = RequestHandler(request)
		enterprise = Enterprise()
		enterprise.gstin = request_handler.getSessionAttribute(ENTERPRISE_GST_SESSION_KEY)
		enterprise.id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)

		if request.method == 'POST' and request.FILES['invoice_copy_uploader']:
			myfile = request.FILES['invoice_copy_uploader']
			fs = FileSystemStorage()
			filename = fs.save(myfile.name, myfile)
			uploaded_file_url = fs.url(filename)
			invoice_details = getInvoiceData(uploaded_file_url, enterprise)
			fs.delete(filename)
			fs.delete(filename + 'houghlines3.jpg')
	except Exception as e:
		logger.exception(e)
	invoice_dict = invoice_details.__dict__
	item_list = []
	for item in invoice_details.item_details:
		item_list.append(item.__dict__)
	po_list = []
	for po in invoice_details.poList:
		po_list.append(po)
	invoice_dict.update({"item_details": item_list})
	invoice_dict.update({"po_list": po_list})
	return HttpResponse(content=simplejson.dumps(invoice_dict), mimetype='application/json')


# FUNCTION to find the Vertical lines in the Table and the Horizontal line below Description
def find_col_line(filename, rowstartposy, rowendposy):
	img = cv2.imread(filename)
	u2 = 0
	v2 = 0
	u4 = 0
	u5 = 0
	lines1 = []
	lines2 = []
	lines4 = []
	lines5 = []
	lines6 = []
	lines7 = []
	lines8 = []
	lines9 = []
	lines10 = []
	lines11 = []
	gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
	edges = cv2.Canny(gray, 50, 100, apertureSize=3)

	minLineLength = 100
	maxLineGap = 10
	lines = cv2.HoughLinesP(edges, 1, np.pi / 100, 200, minLineLength, maxLineGap)
	# print (lines)
	# print (edges.shape)
	# print (rowstartstring)
	for u2 in range(0, len(lines)):
		for x1, y1, x2, y2 in lines[u2]:
			if abs(y1 - y2) < 30 or y1 < rowstartposy:  # (edges.shape[0] - rowstartstring):
				# if y1 > rowstartposy:
				# 	if abs(x1-x2) > 50:
				# 		lines1.append(x1)
				# 		cv2.line(img,(x1,y1),(x2,y2),(0,255,0),3)
				a = 0
			else:
				lines1.append(x1)
				cv2.line(img, (x1, y1), (x2, y2), (0, 255, 0), 3)

	cv2.imwrite(filename + 'houghlines3.jpg', img)
	lines1.sort()
	# print (lines1)
	u3 = 0
	if len(lines1) > 1:
		if lines1[u3] > 10:
			lines2.append(lines1[u3])
	for u3 in range(0, len(lines1) - 1):
		diff = lines1[u3] - lines1[u3 + 1]
		if abs(diff) > 5:
			lines2.append(lines1[u3 + 1])
	#############print (lines2)
	l2 = len(lines2) - 1

	# FINDING the horizontal line below Description

	for u4 in range(0, len(lines)):
		for x1, y1, x2, y2 in lines[u4]:
			if abs(y1 - y2) < 30 and y1 > (rowstartposy + 5):
				lines5.append(u4)
				lines4.append(y1)
			# cv2.line(img,(x1,y1),(x2,y2),(0,255,0),3)
			# break
			if abs(y1 - y2) < 30 and y1 < rowstartposy:
				lines7.append(u4)
				lines6.append(y1)
			if abs(y1 - y2) < 30 and y1 < rowendposy:
				lines9.append(u4)
				lines8.append(y1)
			if abs(y1 - y2) < 30 and y1 > rowendposy:
				lines11.append(u4)
				lines10.append(y1)
	if (lines4):
		index, value = min(enumerate(lines4), key=operator.itemgetter(1))
	if (lines6):
		index1, value1 = max(enumerate(lines6), key=operator.itemgetter(1))
	if (lines8):
		index2, value2 = max(enumerate(lines8), key=operator.itemgetter(1))
	if (lines10):
		index3, value3 = min(enumerate(lines10), key=operator.itemgetter(1))

	if (index1):
		tablestart = (lines[lines7[index1]])
	if (index):
		tableheadend = (lines[lines5[index]])
	if (index2):
		tableend = (lines[lines9[index2]])
	if (index3):
		tableendtotal = (lines[lines11[index3]])

	horizontallines = [tablestart, tableheadend, tableend, tableendtotal]

	for drawline in horizontallines:
		for x1, y1, x2, y2 in drawline:
			cv2.line(img, (x1, y1), (x2, y2), (0, 255, 0), 3)

	cv2.imwrite('houghlines3.jpg', img)

	# headerbotrow = (lines[lines5[index]])
	# for x1,y1,x2,y2 in lines[lines5[index]]:
	#  	cv2.line(img,(x1,y1),(x2,y2),(0,255,0),3)
	# cv2.imwrite('houghlines3.jpg',img)
	# print lines1

	return (lines2, l2, horizontallines)


def detect_text(path):
	client = vision.ImageAnnotatorClient()
	with io.open(path, 'rb') as image_file:
		content = image_file.read()

	image = types.Image(content=content)
	response = client.document_text_detection(image=image)
	texts = response.text_annotations
	return (texts)


def find_border_words(googlecloudvision, findword, headstringposy):
	u2 = 0
	v2 = 0
	stringposy = 0
	stringposx = 0
	while u2 < len(findword):
		for text in googlecloudvision:
			if text.description == findword[u2]:
				foundstring = findword[u2]
				pos0x = text.bounding_poly.vertices[0].x
				pos0y = text.bounding_poly.vertices[0].y
				pos1x = text.bounding_poly.vertices[1].x
				pos1y = text.bounding_poly.vertices[1].y
				pos2x = text.bounding_poly.vertices[2].x
				pos2y = text.bounding_poly.vertices[2].y
				pos3x = text.bounding_poly.vertices[3].x
				pos3y = text.bounding_poly.vertices[3].y
				stringposy = (pos0y + pos3y) / 2
				stringposx = (pos0x + pos1x) / 2
				############print (foundstring, stringposy, headstringposy)
				if abs(stringposy - headstringposy) > 50:
					u2 = len(findword)
					break
			else:
				foundstring = ""
		u2 += 1
	#############print (foundstring, stringposy)
	return (foundstring, stringposy, stringposx)


def findPO(googlecloudvision):
	ponumber = []
	if googlecloudvision is not None:
		popattern = re.compile("\d{2}-\d{2}/PO/\d{6,}")
		for text in googlecloudvision:
			for po in popattern.findall(text.description):
				ponumber.append(po)
	return ponumber


def getInvoiceData(imageFile, enterprise):
	try:
		test = settings.SITE_ROOT + imageFile
		os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = settings.SITE_ROOT + "/google_vision.json"
		print test
		# FINDING TODAY'S DATE & TIME
		today = datetime.now()

		# Word to find the END of the table
		endword = [u"total", u"Total", u"TOTAL"]
		# Word to find the START of the table
		startword = [u"Description", u"description", u"DESCRIPTION", u"Discription"]
		# All possible Date string Formats to be searched
		datestringformats = ['\d{2}-\d{2}-\d{2}', '\d{2}/\d{2}/\d{2}', '\d{2}-[a-zA-Z]{3}-\d{2}',
		                     '\d{2}/[a-zA-Z]{3}/\d{2}', '\d{1}-\d{2}-\d{2}', '\d{1}/\d{2}/\d{2}',
		                     '\d{1}-[a-zA-Z]{3}-\d{2}', '\d{1}/[a-zA-Z]{3}/\d{2}', '\d{1}-\d{1}-\d{2}',
		                     '\d{1}/\d{1}/\d{2}', '\d{2}-[a-zA-Z]{4}-\d{2}', '\d{2}/[a-zA-Z]{4}/\d{2}',
		                     '\d{2}-[a-zA-Z]{5}-\d{2}', '\d{2}/[a-zA-Z]{5}/\d{2}', '\d{2}-[a-zA-Z]{6}-\d{2}',
		                     '\d{2}/[a-zA-Z]{6}/\d{2}', '\d{2}-[a-zA-Z]{7}-\d{2}', '\d{2}/[a-zA-Z]{7}/\d{2}',
		                     '\d{2}-[a-zA-Z]{8}-\d{2}', '\d{2}/[a-zA-Z]{8}/\d{2}', '\d{2}-[a-zA-Z]{9}-\d{2}',
		                     '\d{2}/[a-zA-Z]{9}/\d{2}']

		# BUILD THE BOX HEADER SET
		fuzz = fuzzyset.FuzzySet()
		fuzztax = fuzzyset.FuzzySet()
		fuzzgstin = fuzzyset.FuzzySet()

		startword = [u"description", u"Description", u"Discription", u"Item Description", u"Description of Goods"]
		sno = [u"s.no", u"sino", u"s.no.", u"sno.", u"Sr.No."]
		qty = [u"quantity", u"qty"]
		price = [u"price", u"rate", u"unit price", u"unit rate", u"price per unit", u"rate per unit"]
		hsnsac = [u"HSN", u"HSN/SAC", u"HSN / SAC", u"HSN Code", u"HSN / SAC Code"]
		tax1 = [u"cgst"]
		tax2 = [u"sgst"]
		tax3 = [u"igst"]
		freight = [u"freight"]
		transport = [u"transport"]
		gstinwords = [u"gstin", u"gstn"]

		allwords1 = [sno, startword, qty, price, hsnsac, tax1, tax2, tax3]
		descendwords = [u"cgst", u"cgst@6%", u"cgst@9%", u"cgst@12%", u"sgst", u"sgst@6%", u"sgst@9%", u"sgst@12%",
		                u"igst@5%", u"igst@6%", u"igst@9%", u"igst@12%", u"igst@18%", u"igst@24%", u"freight",
		                u"freightcharge", u"transport"]

		for i in allwords1:
			for j in i:
				fuzz.add(j)

		for i in descendwords:
			fuzztax.add(i)

		for i in gstinwords:
			fuzzgstin.add(i)

		# Reading the Scanned Image, Convert to Grayscale and Run Edge detection
		img = cv2.imread(test)
		gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
		edges = cv2.Canny(gray, 50, 100, apertureSize=3)
		test = settings.SITE_ROOT + imageFile

		# CALLING THE GOOGLE CLOUD VISION FUNCTION - Has to be placed after Detect_Text Function
		googlecloudvision = detect_text(test)

		invoiceDetails = InvoiceDetails()
		po_number = findPO(googlecloudvision)
		invoiceDetails.poList = []
		invoiceDetails.poList.extend(po_number)

		# FINDING BORDER WORDS AND THEIR POSITION IN DOCUMENT
		# print (googlecloudvision)
		startstringposy = 0
		startborderword = find_border_words(googlecloudvision, startword, startstringposy)
		startstring = startborderword[0]
		startstringposy = startborderword[1]
		startstringposx = startborderword[2]
		#############print (startstringposy)

		endborderword = find_border_words(googlecloudvision, endword, startstringposy)
		endstring = endborderword[0]
		endstringposy = endborderword[1]
		endstringposx = endborderword[2]

		#############print (startstringposy, endstringposy)
		# CALLING THE FUNCTION TO FIND THE BORDERS
		borderreturn = find_col_line(test, startstringposy, endstringposy)
		colborder = borderreturn[0]
		noofcol = borderreturn[1]
		#############print (borderreturn[2][0][0][1])
		tablestartposy = borderreturn[2][0][0][1]
		tableheadendposy = borderreturn[2][1][0][1]
		# tableendposy = borderreturn[2][2][0][1]
		tableendposy = (endstringposy - 10)

		# PRINT STATEMENTS FOR CHECKING

		#############print (tablestartposy, tableheadendposy, tableendposy)
		# print (googlecloudvision)
		# FINDING THE DATA WITHIN THE TABULAR COLUMNS
		w3 = 0
		y3 = 0
		count = 0
		boxheader = []
		boxvalue = []
		wordpos = []
		gstinstringposx = 0
		gstinstringposy = 0
		gstin = []
		datestrings = []

		for text in googlecloudvision:
			col = 0
			fileword = text.description
			pos0x = text.bounding_poly.vertices[0].x
			pos0y = text.bounding_poly.vertices[0].y
			pos1x = text.bounding_poly.vertices[1].x
			pos1y = text.bounding_poly.vertices[1].y
			pos2x = text.bounding_poly.vertices[2].x
			pos2y = text.bounding_poly.vertices[2].y
			pos3x = text.bounding_poly.vertices[3].x
			pos3y = text.bounding_poly.vertices[3].y
			stringposy = (pos0y + pos3y) / 2
			stringposx = (pos0x + pos1x) / 2
			# SPLITTING EACH WORD WITHIN THE ENTIRE GOOGLE STRING
			if count == 0:
				allwords = fileword.split()
				for word in allwords:
					for j in datestringformats:
						match3 = re.search(j, word)
						if match3 is not None:
							datestrings.append(word)

			# FINDING GSTIN OF SUPPLIER
			gstinmatch = fuzzgstin.get(fileword)

			if gstinmatch is None:
				a = 0
			else:
				hd = float(gstinmatch[0][0])
				if hd > 0.9:
					gstinstringposx = stringposx
					gstinstringposy = stringposy
			if len(fileword) == 15:
				if abs(stringposy - gstinstringposy) < 50:
					gstin.append(fileword)

			if abs(pos0x - pos1x) > 800:
				stringposx = 5000
			for col in range(0, noofcol):
				wordpos = [stringposx, stringposy]
				if (stringposx > colborder[col] and stringposx < colborder[col + 1]):
					if (stringposy > tablestartposy and stringposy < tableheadendposy):
						boxheader.append((col + 1, wordpos, fileword))
					if (stringposy > tableheadendposy and stringposy < tableendposy):
						boxvalue.append((col + 1, wordpos, fileword))
		boxheader = sorted(boxheader, key=lambda x: (x[0]))
		boxvalue = sorted(boxvalue, key=lambda x: (x[0]))
		#############print (boxheader)
		# print (*boxvalue, sep = "\n")

		q2 = 0
		s2 = 0
		boxfinheader = copy.copy(boxheader)
		while q2 < len(boxheader) - 1:
			if boxfinheader[s2][0] == boxheader[q2 + 1][0]:
				x = boxfinheader[s2][0]
				y = boxfinheader[s2][1]
				z = boxfinheader[s2][2] + " " + boxheader[q2 + 1][2]
				boxfinheader[s2] = (x, y, z)
				del boxfinheader[s2 + 1]
			else:
				s2 += 1
			q2 += 1

		boxheader = boxfinheader

		q2 = 0
		s2 = 0
		boxfinvalue = copy.copy(boxvalue)
		while q2 < (len(boxvalue) - 1):
			if boxfinvalue[s2][0] == boxvalue[q2 + 1][0] and abs(boxfinvalue[s2][1][1] - boxvalue[q2 + 1][1][1]) < 2:
				x = boxfinvalue[s2][0]
				y = boxfinvalue[s2][1]
				# ASSUMING THAT SECOND COLUMN WILL MOSTLY BE DESCRIPTION
				if x == 1:
					z = boxfinvalue[s2][2] + " " + boxvalue[q2 + 1][2]
				# unicodedata.normalize('NFKD', z).encode('ascii','ignore')
				else:
					z = boxfinvalue[s2][2] + boxvalue[q2 + 1][2]
				# unicodedata.normalize('NFKD', z).encode('ascii','ignore')
				boxfinvalue[s2] = (x, y, z)
				del boxfinvalue[s2 + 1]
			else:
				s2 += 1
			q2 += 1

		boxvalue = boxfinvalue
		#############print(len(boxheader))
		# print (boxheader)
		# print (*boxheader, sep = "\n")
		# print (*boxvalue, sep = "\n")

		# ORDER THE DOCUMENT's BOX HEADER VALUES AGAINST DEFAULT HEADERS AND IGNORE OTHERS
		# MY FIXED ORDER IS //S.No // Description// Quantity // Rate // HSN // CGST // SGST // IGST
		headerorder = []
		draftheaderorder = []
		u = 0
		v = 0
		goodmatch = 0.59
		z = 1
		for u in range(0, len(boxheader)):
			#############print (boxheader[u][2])
			headword = fuzz.get(boxheader[u][2])
			#############print (headword)
			if headword is None:
				a = 0
			else:
				hd = float(headword[0][0])
				if hd > goodmatch:
					for v in range(0, len(allwords1)):
						if headword[0][1] in allwords1[v]:
							for w in range(0, len(headerorder)):
								if headerorder[w][3] == headword[0][1] and headerorder[w][2] < headword[0][0]:
									headerorder[w] = (v, boxheader[u][0], headword[0][0], headword[0][1])
									z = 0
							if z > 0:
								headerorder.append((v, boxheader[u][0], headword[0][0], headword[0][1]))
							z = 1

		#############print (headerorder)

		# FINDING THE ITEM-WISE QUANTITY AND RATE PER UNIT
		snocolvalue = []
		qtycolvalue = []
		ratecolvalue = []
		desccolvalue = []
		hsncolvalue = []
		snocolvaluepos = []
		qtycolvaluepos = []
		ratecolvaluepos = []
		hsncolvaluepos = []
		snocolno = 20
		qtycolno = 20
		ratecolno = 20
		desccolno = 20
		hsncolno = 20
		h = 0
		# print (len(headerorder))
		for h in range(0, len(headerorder)):
			if headerorder[h][0] == 0:
				snocolno = headerorder[h][1]
			elif headerorder[h][0] == 2:
				qtycolno = headerorder[h][1]
			elif headerorder[h][0] == 3:
				ratecolno = headerorder[h][1]
			elif headerorder[h][0] == 1:
				desccolno = headerorder[h][1]
			elif headerorder[h][0] == 4:
				hsncolno = headerorder[h][1]

		# print (headerorder[h][0])
		# print ("Description Column Nos is", desccolno)

		descvalue = []
		for q2 in range(0, len(boxvalue)):
			if boxvalue[q2][0] == desccolno:
				descvalue.append(boxvalue[q2])

		h2 = 0
		for h2 in range(0, len(boxvalue)):
			if boxvalue[h2][0] == snocolno:
				boxvalue[h2][2] == boxvalue[h2][2].replace(" ", "")
				snocolvalue.append(boxvalue[h2][2])
				snocolvaluepos.append(boxvalue[h2][1])
			elif boxvalue[h2][0] == qtycolno:
				boxvalue[h2][2] == boxvalue[h2][2].replace(" ", "")
				qtycolvalue.append(boxvalue[h2][2])
				qtycolvaluepos.append(boxvalue[h2][1])
			elif boxvalue[h2][0] == ratecolno:
				boxvalue[h2][2] == boxvalue[h2][2].replace(" ", "")
				ratecolvalue.append(boxvalue[h2][2])
				ratecolvaluepos.append(boxvalue[h2][1])
			elif boxvalue[h2][0] == hsncolno:
				boxvalue[h2][2] == boxvalue[h2][2].replace(" ", "")
				hsncolvalue.append(boxvalue[h2][2])
				hsncolvaluepos.append(boxvalue[h2][1])
		a2 = 0
		h2 = 0
		for h2 in range(0, len(descvalue)):
			# if the space between lines in the last item of the description column is more than a certain value, it should be regarded as the end of the item list.
			if h2 == 0:
				desccolvalue.append(descvalue[h2])
			elif abs(descvalue[h2][1][1] - descvalue[h2 - 1][1][1]) > 75:
				a2 = 1
			elif fuzztax.get(descvalue[h2][2]) is not None and fuzztax.get(descvalue[h2][2])[0][0] > 0.9:
				#############print ("probability is",fuzztax.get(descvalue[h2][2])[0][0])
				a2 = 1
			elif a2 == 0:
				desccolvalue.append(descvalue[h2])

		snocolvalue = [x for x in snocolvalue if any(c.isdigit() for c in x)]
		qtycolvalue = [x for x in qtycolvalue if any(c.isdigit() for c in x)]
		ratecolvalue = [x for x in ratecolvalue if any(c.isdigit() for c in x)]
		hsncolvalue = [x for x in hsncolvalue if any(c.isdigit() for c in x)]

		snoitems = int(len(snocolvalue))

		if len(snocolvalue) > 0:
			snomax = int(max(snocolvalue))
		else:
			snomax = 0

		qtyitems = int(len(qtycolvalue))

		rateitems = int(len(ratecolvalue))

		hsnitems = int(len(hsncolvalue))

		if snoitems == qtyitems:
			invoiceitems = mode([snoitems, qtyitems, rateitems])
		elif qtyitems == rateitems:
			invoiceitems = mode([snoitems, qtyitems, rateitems])
		elif snoitems == rateitems:
			invoiceitems = mode([snoitems, qtyitems, rateitems])
		else:
			invoiceitems = qtyitems

		refcolvaluepos = []

		if invoiceitems == snoitems:
			refcolvaluepos = snocolvaluepos
		elif invoiceitems == qtyitems:
			refcolvaluepos = qtycolvaluepos
		elif invoiceitems == rateitems:
			refcolvaluepos = qtycolvaluepos
		else:
			refcolvaluepos = snocolvaluepos

		desccolfinalval = []
		draftdesccolval = ""
		itemheight = []
		if len(desccolvalue) > 1:
			itemheight.append(desccolvalue[0][1][1] - 2)
			itemht = desccolvalue[0][1][1] - 2
		descnoitems = []

		if len(refcolvaluepos) > 1:
			for k in range(0, len(refcolvaluepos) - 1):
				itemht += refcolvaluepos[k + 1][1] - refcolvaluepos[k][1] - 10
				itemheight.append(itemht)
			itemheight.append(tableendposy)

			for o in range(0, len(desccolvalue)):
				for o2 in range(1, len(itemheight)):
					if desccolvalue[o][1][1] > itemheight[o2 - 1] and desccolvalue[o][1][1] < itemheight[o2]:
						desccolfinalval.append((o2 - 1, desccolvalue[o][2]))
						descnoitems.append(o2)
		else:
			m = 0

			for m in range(0, len(desccolvalue)):
				draftdesccolval = draftdesccolval + " " + desccolvalue[m][2]
			desccolfinalval.append((0, draftdesccolval))
			descnoitems = [1]

		index4, value4 = max(enumerate(descnoitems), key=operator.itemgetter(1))

		descitems = int(value4)
		k3 = 0
		descitemwiseval = []
		while k3 < descitems:
			itemwiseval = ""
			for m3 in range(0, len(desccolfinalval)):
				if desccolfinalval[m3][0] == k3:
					itemwiseval = itemwiseval + " " + desccolfinalval[m3][1]
			descitemwiseval.append(itemwiseval)
			k3 += 1

		# REMOVING LETTERS FROM QTY VALUE 
		i = 0
		j = 0
		qtycolfinalvalue = []
		for j in range(0, invoiceitems):
			qtycolfinalvalue.append(qtycolvalue[j])
		i = 0
		j = 0
		for j in range(0, len(qtycolvalue)):
			qtycolfinalvalue[j] = "".join([i for i in qtycolvalue[j] if i.isdigit()])

		# REMOVING ANY ADDITIONAL ITEMS IN RATE COLUMN and REMOVING LETTERS FROM RATE VALUE
		i = 0
		j = 0
		ratecolfinalvalue = []
		for j in range(0, invoiceitems):
			ratecolfinalvalue.append(ratecolvalue[j])
		i = 0
		j = 0
		for j in range(0, len(ratecolfinalvalue)):
			ratecolfinalvalue[j] = "".join([i for i in ratecolvalue[j] if i.isdigit() or i == "."])

		# FINDING MAXIMUM DATE AMONG THE DATE STRINGS
		datestringsfinal = []
		for c2 in datestrings:
			try:
				d = parse(c2)
				datestringsfinal.append(d)
			except ValueError:
				j3 = 0

		invoicedate = []
		if len(datestringsfinal) > 0:
			invoicedateindex, invoicedate = max(enumerate(datestringsfinal), key=operator.itemgetter(1))

		if len(gstin) > 0:
			identifySupplierGST = fuzzyset.FuzzySet()
			identifySupplierGST.add(unicode(enterprise.gstin, "utf-8"))
			for gstvalue in gstin:
				if (identifySupplierGST.get(gstvalue, "")[0][0] < 0.9):
					masterDao = MasterDAO()
					party = masterDao.getPartyByGst(enterprise.id, gstvalue)
					invoiceDetails.gstin = gstvalue

					if (party is not None):
						invoiceDetails.partyId = party.id
						invoiceDetails.partyName = party.name
						invoiceDetails.partyConfigFlag = party.config_flags
					break

		if (invoiceDetails.poList and invoiceDetails.partyId):
			invoiceDetails.poList[0]
			masterDao = MasterDAO()
			print invoiceDetails.poList[0].split("/")[-1].lstrip("0")
			po = masterDao.getPartyAndProjectByPO(enterprise.id, invoiceDetails.poList[0].split("/")[-1].lstrip("0"),
			                                      invoiceDetails.partyId)
			invoiceDetails.projectCode = po.project_code
			invoiceDetails.poOrderId = po.po_id
			invoiceDetails.poNumber = invoiceDetails.poList[0]

		if (invoicedate):
			invoiceDetails.invoicedate = invoicedate.strftime("%Y-%m-%d")

		counter = 0
		if len(descitemwiseval) > 0:
			for sepitem in descitemwiseval:
				invoiceitem = InvoiceItem()
				invoiceitem.name = sepitem
				print invoiceitem.name
				try:
					invoiceitem.qty = qtycolfinalvalue[counter]
				except IndexError as e:
					logger.error(e.message)

				try:
					invoiceitem.unit_price = ratecolfinalvalue[counter]
				except IndexError as e:
					logger.error(e.message)

				try:
					invoiceitem.hsn = hsncolvalue[counter]
				except IndexError as e:
					logger.error(e.message)
				print invoiceitem
				invoiceDetails.item_details.append(invoiceitem)
				counter = counter + 1

		return invoiceDetails
	except Exception as e:
		exc_type, exc_obj, exc_tb = sys.exc_info()
		logger.error(e.message)


class Enterprise():
	gstin = 0
	id = ""


class InvoiceDetails():
	gstin = 0,
	partyId = ""
	partyName = ""
	partyConfigFlag = ""
	projectCode = ""
	poOrderId = ""
	poNumber = ""
	invoicedate = ""
	item_details = []
	poList = []


class InvoiceItem():
	name = ""
	id = 0
	qty = 0
	unit_price = 0.0
	cgst = 0.0
	sgst = 0.0
	igst = 0.0
	hsn = ""
	make_id = ""
