
<div class="hsn_summary_contianer hide">
	<h6 style="margin: 0;"><b class="hsn_summary_title">HSN SUMMARY</b></h6>
	<atable class="table table-bordered row-seperator column-seperator hsn_table hsn_summary" style="width: 100%;">
		<athead>
			<atr class="row_seperator column_seperator header_shading">
				<ath class="text-center td_sno" rowspan="2" style="width: 6%">S.No</ath>
				<ath class="text-center" rowspan="2" style="width: 28%">HSN/SAC</ath>
				<ath class="text-center td_tax" rowspan="2" style="width: 18%">Taxable Value</ath>
				<ath class="text-center" colspan="2" style="width: 16%; ">CGST</ath>
				<ath class="text-center" colspan="2" style="width: 16%; ">SGST</ath>
				<ath class="text-center" colspan="2" style="width: 16%;  border-right-color: #ccc;">IGST</ath>
			</atr>
			<atr class="row_seperator column_seperator header_shading">
				<ath class="text-center" style="width: 7%;" >%</ath>
				<ath class="text-center" style="width: 9%;">{[ source.currency.code ]}</ath>
				<ath class="text-center" style="width: 7%;">%</ath>
				<ath class="text-center" style="width: 9%;">{[ source.currency.code ]}</ath>
				<ath class="text-center" style="width: 7%;">%</ath>
				<ath class="text-center" style="width: 9%;">{[ source.currency.code ]}</ath>
			</atr>
		</athead>
		<atbody>
			[% for summary in hsn_summary %]
				[%  if forloop.counter|divisibleby:2 %]
				<atr class="row_seperator column_seperator row_shading" style="background: #efefef;">
				[% else %]
				<atr class="row_seperator column_seperator row_shading" style="background: #ffffff;">
				[% endif %]
					<atd class="text-center td_sno">{[forloop.counter]}.</atd>
					<atd class="text-center">{[ summary.hsn_code ]}</atd>
					<atd class="text-right td_tax">{[ summary.consolidated_taxable_value|floatformat:2 ]}</atd>
					<atd class="text-right ">{[ summary.consolidated_cgst_rate|floatformat:2 ]}</atd>
					<atd class="text-right ">{[ summary.consolidated_cgst_value|floatformat:2 ]}</atd>
					<atd class="text-right ">{[ summary.consolidated_sgst_rate|floatformat:2 ]}</atd>
					<atd class="text-right ">{[ summary.consolidated_sgst_value|floatformat:2 ]}</atd>
					<atd class="text-right ">{[ summary.consolidated_igst_rate|floatformat:2 ]}</atd>
					<atd class="text-right" style="border-right-color: #ccc;">{[ summary.consolidated_igst_value|floatformat:2 ]}</atd>
				</atr>
			[% endfor %]
		</atbody>
	</atable>
</div>