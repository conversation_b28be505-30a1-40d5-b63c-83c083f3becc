import json
from collections import defaultdict
from datetime import datetime, time
from decimal import Decimal

import pymysql
import simplejson
from dateutil.relativedelta import relativedelta
from django.http import HttpResponse
from django.http import HttpResponseRedirect
from django.template.response import TemplateResponse
from sqlalchemy import and_, func
from sqlalchemy.orm import make_transient

from erp import properties, helper, DEFAULT_MAKE_ID
from erp.accounts import PURCHASE_ACCOUNT_GROUP_NAME
from erp.accounts.backend import AccountService
from erp.admin import enterprise_module_settings
from erp.admin.backend import UserDAO
from erp.auth import SESSION_USERNAME_KEY, USER_IN_SESSION_KEY, ENTERPRISE_IN_SESSION_KEY, SESSION_KEY, \
	ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.dao import executeQuery
from erp.helper import getProjectFrequents, getStateList, CustomJSONEncoder, validate_payload
from erp.masters.backend import MasterService
from erp.models import PurchaseOrder, PurchaseOrderMaterial, \
	PurchaseOrderMaterialDeliverySchedules, Tax, PurchaseOrderTax, \
	Invoice, Currency, Party, Enterprise
from erp.properties import TEMPLATE_TITLE_KEY
from erp.purchase import logger, PO_STATUS_REJECTED, PO_STATUS_REVIEWED, ORDER_TYPE_DICT
from erp.purchase.queries import GET_PURCHASE_PERFORMANCE_QUERY, GET_PENDING_PO_QUERY
from erp.purchase.service import PurchaseService
from erp.stores.backend import StoresDAO
from settings import HOST, USER, PASSWORD, DBNAME, PORT, SQLASession
from util.api_util import response_code, JsonUtil
from erp.purchase.changelog import PurchaseChangelog
from erp.production.backend import ProductionService

__author__ = 'saravanan'

# TODO Tax Row generation: Reorganise
# JSON Dump literals
TAX_ROW_DUMP = "<tr name='tr_%s'><th><a role='button' title='Remove Tax' onclick=\"javascript:removePoTax('%s');\" >" \
               "<input type='hidden' class='hnd_text_id' name='po_tax_code' value='%s'/>" \
               "<i class='fa fa-window-close' aria-hidden='true' style='color: #000; font-size: 18px;'></i>" \
               "</a></th><th><b>%s</b> <i>(Net Rate)</i></th>" \
               "<td><b>%s %%</b><input type='hidden' name='net_rate%s' id='net_%s' value='%s'/></td><td width='10px'>&nbsp;</td>" \
               "<td class='col-sm-6' style='padding-right:0px;'><input type='text' class='form-control tax-value' style='text-align:right;' name='tax%s' disabled/></td>" \
               "<td><input type='hidden' class='po_txt_box form-control' style='text-align:right;' name='asses_rate%s' id='asses%s' value='%s' disabled/></td></tr>"
SUB_TAX_ROW_DUMP = "<tr name='tr_%s'><td colspan='2'>%s</td><td>%s %%</td><td width='10px'>&nbsp;</td><td></td></tr>"
TAX_BASE_RATE_ROW_DUMP = "<tr name='tr_%s'><td colspan='2'><i>%s</i></td><td>%s %%</td><td width='10px'>&nbsp;</td><td></td></tr>"

# AJAX URL parameter separators
DATA_BLOCK_SEPARATOR = ",@,"
DATA_SEPARATOR = "[::]"
FIELD_SEPARATOR = "[:]"
COMMA_SEPARATOR = "%2C"  # ","

MATERIAL_FORM_KEY = 'material_form'
BILL_OF_MATERIALS_FORMSET_KEY = 'bill_of_materials_formset'
SPECIFICATION_FORMSET_KEY = 'specifications_formset'
PRICE_OF_SUPP_MATERIALS_FORMSET_KEY = 'materials_party_price_formset'
MATERIAL_MAKE_FORMSET_KEY = 'material_makes_formset'
ALTERNATE_UNIT_FORMSET_KEY = 'alternate_unit_formset'


def loadJobOrderList(request):
    return renderPurchaseHome(request, order_type="jo", edit_link=properties.MANAGE_JO_List_URL)


def loadWorkOrderList(request):
	"""
	Main page to manage Work Orders - Add, Search, Edit and Delete a PO

	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		since, till = JsonUtil.getDateRange(
			rh=request_handler, since_session_key="wo.list.since", till_session_key="wo.list.till")
		status = request_handler.getAndCacheData(key="status", session_key="wo.list.status")
		if request.POST.get('wo_fromdate'):
			since = datetime.strptime(request.POST.get('wo_fromdate'), "%Y-%m-%d")
			project_id = "5"  # All
		else:
			project_id = request_handler.getAndCacheData(key="project_id", session_key="wo.list.project_id")
		status = request.POST.get('wo_status') if request.POST.get('wo_status') else status

		query = "SELECT id, name FROM projects WHERE enterprise_id='%s' AND id<>'' ORDER BY name" % enterprise_id
		projects = executeQuery(query)
		return TemplateResponse(request=request, template='stores/work_order.html', context={
			TEMPLATE_TITLE_KEY: "Work Order", 'projects': projects, 'status': status, 'project_id': project_id,
			'since': since.strftime("%Y-%m-%d"), 'till': till.strftime("%Y-%m-%d")})
	except Exception as e:
		raise e


def renderPurchaseHome(request, order_type="po", edit_link=properties.MANAGE_PO_List_URL):
	"""
	Main page to manage Purchase Orders - Add, Search, Edit and Delete a PO

	:param request:
	:return:
	:param order_type:
	:param edit_link:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		since, till = JsonUtil.getDateRange(
			rh=request_handler, since_session_key="po.list.since", till_session_key="po.list.till")
		status = request_handler.getAndCacheData(key="status", session_key="po.list.status")
		if request.POST.get('po_fromdate'):
			since = datetime.strptime(request.POST.get('po_fromdate'), "%Y-%m-%d")
			project_id = "5"  # All
		else:
			project_id = request_handler.getAndCacheData(key="project_id", session_key="po.list.project_id")
		status = request.POST.get('po_status') if request.POST.get('po_status') else status
		pending_indents = loadIndentDetails(request)
		purchase_service = PurchaseService()

		po_type_choices = (('PO', 'PO'), ('JO', 'JO'), ('WO', 'WO'))
		enterprise = purchase_service.purchase_dao.db_session.query(Enterprise).filter(
			Enterprise.id == enterprise_id).first()
		indent_access = enterprise.setting_flags & enterprise_module_settings['indent_flag'] > 0
		query = """SELECT id, CONCAT(name,' (',code,')') as project FROM projects 
		WHERE enterprise_id='%s' AND is_active = 1 ORDER BY name""" % enterprise_id
		projects = executeQuery(query)
		return TemplateResponse(request=request, template='purchase/po_list.html', context={
			'indent_access': indent_access, 'pending_indents': pending_indents,
			TEMPLATE_TITLE_KEY: "Purchase Order" if order_type == "po" else ("Job Order" if order_type == "jo" else "Work Order"),
			'po_type': po_type_choices, 'projects': projects, 'status': status, 'project_id': project_id,
			'since': since.strftime("%Y-%m-%d"), 'till': till.strftime("%Y-%m-%d"), 'order_type': order_type,
			'edit_link': edit_link})
	except Exception as e:
		raise e


def manageJO(request):
	return managePO(request, order_type="jo", edit_link=properties.MANAGE_JO_List_URL)


def managePO(request, order_type="po", edit_link=properties.MANAGE_PO_List_URL):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	id = request.GET.get('id')
	po_no = request_handler.getPostData("po_no") if request.GET.get('pid') is None else request.GET.get('pid')
	status = request_handler.getPostData("po_status") if request.GET.get('status') is None else request.GET.get('status')
	purchase_service = PurchaseService()
	master_service = MasterService()
	gst_category = master_service.getGSTCategory()
	countries = master_service.getCountries()
	gst_category_list = []
	gst_country_list = []
	for category in gst_category:
		gst_category_list.append({"category_id": category.id, "category_name": category.name, "category_desc": category.description})

	for country in countries:
		gst_country_list.append({"country_code": country.code, "country_name": country.name})
	material_vo = master_service.constructMaterialVO(enterprise_id=enterprise_id)
	conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
	cur = conn.cursor()
	indents = []
	indent_type_dict = {}
	cur.execute("SELECT id, name FROM projects WHERE enterprise_id='%s' AND is_active = 1 ORDER BY name" % enterprise_id)
	projects = cur.fetchall()

	cur.execute(
		"SELECT party_id, party_name,party_code FROM party_master WHERE enterprise_id='%s' ORDER BY party_name" % enterprise_id)
	suppliers = cur.fetchall()

	currency = SQLASession().query(Currency).order_by(Currency.code).all()
	taxes = SQLASession().query(Tax).filter(
		Tax.enterprise_id == request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)).order_by(Tax.name).all()

	cur.execute(
		"SELECT unit_id, unit_name FROM unit_master WHERE enterprise_id='%s' ORDER BY unit_id" % enterprise_id)
	units = cur.fetchall()

	frequent_project_list = getProjectFrequents(table='purchase_order', enterprise_id=enterprise_id)
	purchase_account = helper.populateAccountLedgerChoices(enterprise_id=enterprise_id, group_names=(
		PURCHASE_ACCOUNT_GROUP_NAME,))

	enterprise = purchase_service.purchase_dao.db_session.query(Enterprise).filter(
		Enterprise.id == enterprise_id).first()
	indent_access = enterprise.setting_flags & enterprise_module_settings['indent_flag'] > 0
	po_type_choices = (('PO', 'PO'), ('JO', 'JO'), ('WO', 'WO'))

	issue_to = [
		helper.listUniqueEntriesFromDB(
			enterprise_id=enterprise_id, table='invoice', column='issued_to', frequents_only=True),
		helper.listUniqueEntriesFromDB(
			enterprise_id=enterprise_id, table='invoice', column='issued_to')]

	conn.close()
	return TemplateResponse(
		request=request, template='purchase/po.html', context={
			'id': id, 'po_no': po_no, 'status': status, 'indent_access': indent_access, 'indents': indents,
			'indent_type_dict': indent_type_dict, 'projects': projects, 'enterprise_profile': enterprise,
			'frequent_projects': frequent_project_list, 'suppliers': suppliers, 'currency': currency,
			'home_currency': enterprise.home_currency_id, 'purchase_account': purchase_account, 'po_type': po_type_choices,
			'taxes': taxes,	'units': units,
			MATERIAL_FORM_KEY: material_vo.material_form,
			ALTERNATE_UNIT_FORMSET_KEY: material_vo.alternate_unit_formset,
			BILL_OF_MATERIALS_FORMSET_KEY: material_vo.material_formset,
			SPECIFICATION_FORMSET_KEY: material_vo.specification_formset,
			PRICE_OF_SUPP_MATERIALS_FORMSET_KEY: material_vo.supplier_price_material_formset,
			MATERIAL_MAKE_FORMSET_KEY: material_vo.material_make_formset, "make_list": [],
			'issue_to': issue_to,
			TEMPLATE_TITLE_KEY: "Purchase Order" if order_type == "po" else ("Job Order" if order_type == "jo" else "Work Order"),
			'gst_category_list': gst_category_list, 'gst_country_list': gst_country_list, 'state_list': getStateList(),
			'edit_link': edit_link,'order_type': order_type})


def renderProjectHome(request, edit_link=properties.MANAGE_PROJECT_List_URL):
	"""
	Main page to manage Projects - Add, Search, Edit and Delete a PO

	:param request:
	:return:
	:param edit_link:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id

		return TemplateResponse(request=request, template='purchase/project_list.html', context={
			TEMPLATE_TITLE_KEY: "Projects", 'edit_link': edit_link})
	except Exception as e:
		raise e


def renderProjectForeCast(request, edit_link=properties.MANAGE_PROJECT_FORECAST_URL):
	"""
	Main page to manage Projects - Add, Search, Edit and Delete a PO

	:param request:
	:return:
	:param edit_link:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id

		return TemplateResponse(request=request, template='purchase/project_fore_cast.html', context={
			TEMPLATE_TITLE_KEY: "CashFlow Statements", 'edit_link': edit_link})
	except Exception as e:
		raise e


def renderDashboard(request):
	"""
	po dashboard page
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	since, till = JsonUtil.getDateRange(
		rh=request_handler, since_session_key='stores.home.since', till_session_key='stores.home.till')
	logger.info('Accessing store Dashboard for date range: %s - %s' % (since, till))
	dashboard_info = StoresDAO().getIndentData(enterprise_id)
	return TemplateResponse(request=request, template='purchase/home.html', context={'dash_res': dashboard_info,
		TEMPLATE_TITLE_KEY: "Purchase"})


def loadIndentDetails(request):  # load PO materials

	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	indent_type_condition = ""
	indent_type_condition_union = ""
	indent_type = request_handler.getPostData("indent_type")
	if indent_type:
		indent_type_condition = """ AND indent_module_id = %s""" % indent_type
		indent_type_condition_union = """ AND ind.indent_module_id = %s""" % indent_type
	pending_indent_query = """
	SELECT
		indent_no, indent_id, financial_year , purpose, DATE_FORMAT(raised_date,'%d-%m-%Y') AS tDate , indent_type,
		CONCAT(financial_year, IF(indent_module_id=0,'/IND/','/MI/'), IF(LENGTH(indent_id) < 6, LPAD(indent_id, 6, '0'), indent_id), IFNULL(sub_number, ''))
	FROM
		(SELECT
			i.indent_no, i.indent_id, i.sub_number, i.financial_year,i.purpose, i.raised_date, i.indent_type , im.enterprise_id,
			im.request_qty, SUM(IFNULL(pom.pur_qty, 0)) AS ordered_qty, i.indent_module_id as indent_module_id
		FROM
			indents i
		JOIN indent_material im ON i.enterprise_id = im.enterprise_id
			AND i.indent_no = im.indent_no
		JOIN purchase_order po ON i.enterprise_id = po.enterprise_id
			AND po.status <> 3
			AND i.indent_no = po.indent_no
		LEFT JOIN purchase_order_material pom ON im.enterprise_id = pom.enterprise_id
			AND po.id = pom.pid AND pom.make_id = im.make_id
			AND pom.item_id = im.item_id
		GROUP BY i.indent_no , im.item_id , im.make_id
		HAVING request_qty > ordered_qty) AS IND_PO_ITEMS
	WHERE
		enterprise_id = {enterprise_id} {indent_type_condition}
	GROUP BY enterprise_id , indent_no
	UNION SELECT
		indent_no, indent_id, financial_year , purpose, DATE_FORMAT(raised_date,'%d-%m-%Y') AS tDate, indent_type,
		CONCAT(financial_year, IF(indent_module_id=0,'/IND/','/MI/'), IF(LENGTH(indent_id) < 6, LPAD(indent_id, 6, '0'), indent_id), IFNULL(sub_number, ''))
	FROM
		indents as ind
	WHERE
		enterprise_id = {enterprise_id} {indent_type_condition_union}
		AND indent_no NOT IN (SELECT
				indent_no
			FROM
				purchase_order WHERE enterprise_id={enterprise_id} AND status <> 3)
		AND (SELECT SUM(IFNULL(im.request_qty, 0)) AS request_qty
			FROM
				indents i
			LEFT JOIN
				indent_material im ON i.enterprise_id = im.enterprise_id
				AND i.indent_no = im.indent_no
			WHERE i.indent_no = ind.indent_no) > 0
	ORDER BY indent_no""".format(
		enterprise_id=enterprise_id, indent_type_condition=indent_type_condition,
		indent_type_condition_union=indent_type_condition_union)
	logger.debug("Pending Indent Query:\n%s" % pending_indent_query)
	try:
		pending_indents = executeQuery(pending_indent_query)
	except Exception as e:
		logger.exception("Failed to fetch Pending Indents - %s" % e)
		pending_indents = ""

	indents = []
	for item in pending_indents:
		temp_list = list(item)
		date_time_obj = datetime.strptime(item[4], '%d-%m-%Y')
		temp_list[4] = date_time_obj
		indents.append(temp_list)
	response = HttpResponse(content=simplejson.dumps(pending_indents), mimetype='application/json')
	if request.path.startswith('/erp/purchase/po/loadinddetails'):
		return response
	else:
		return indents


def savePO(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	if request_handler.isPostRequest():
		purchase_service = PurchaseService()
		username = request_handler.getSessionAttribute(SESSION_USERNAME_KEY)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		curdatetime = datetime.now()
		purchase_order_dict = json.loads(request.POST.get('purchase_order_dict'))
		po_materials = purchase_order_dict['materials']
		stock_type = purchase_order_dict['stock_type'] if 'stock_type' in purchase_order_dict else 1
		po_taxes = purchase_order_dict['taxes'] if 'taxes' in purchase_order_dict else ""
		po_tags = purchase_order_dict['tags'] if 'tags' in purchase_order_dict else ""
		shipping_name = purchase_order_dict['shipping_name'] if 'shipping_name' in purchase_order_dict else ""
		shipping_address = purchase_order_dict['shipping_address'] if 'shipping_address' in purchase_order_dict else ""
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
		cur = conn.cursor()
		po_type = int(purchase_order_dict['po_type'])
		try:
			po_insert_query = """INSERT INTO purchase_order (
					project_code, order_date, indent_no, purpose, sp_instructions, supplier_id, quotation_refno,
					quotation_date, payment, transport, packing_forwarding, delivery, user, total, status, tax1type,
					tax1amt, tax2amt, purchase_account_id, currency_code, stock_type, enterprise_id, payment_type, payment_days,
					payment_mode, drafted_on, drafted_by, type, is_blanket_po, valid_since, valid_till, round_off, issue_to,
					shipping_name, shipping_address)
				VALUES ('%s', '%s', '%s', '%s', '%s', %s,'%s', %s, '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s',
					'%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', %s, %s, %s, %s, '%s', '%s', '%s') """ % (
				purchase_order_dict['project_code'], curdatetime,
				purchase_order_dict['indent_no'] if 'indent_no' in purchase_order_dict and purchase_order_dict['indent_no'] != 'NULL' else '000000',
				purchase_order_dict['purpose'] if 'purpose' in purchase_order_dict else 'NULL',
				purchase_order_dict['closing_remarks'] if 'closing_remarks' in purchase_order_dict else 'NULL',
				str(purchase_order_dict['supplier_id']) if 'supplier_id' in purchase_order_dict and purchase_order_dict['supplier_id'] != "" else 'NULL',
				purchase_order_dict['quotation_ref_no'] if 'quotation_ref_no' in purchase_order_dict else 'NULL',
				'"%s"' % purchase_order_dict['quotation_date'] if 'quotation_date' in purchase_order_dict and purchase_order_dict['quotation_date'] != '' else 'null',
				purchase_order_dict['payment'] if 'payment' in purchase_order_dict else 'NULL',
				purchase_order_dict['transport'] if 'transport' in purchase_order_dict else 'NULL',
				purchase_order_dict['packing'] if 'packing' in purchase_order_dict else 'NULL',
				purchase_order_dict['delivery'] if 'delivery' in purchase_order_dict else 'NULL',
				username, purchase_order_dict['total'] if 'total' in purchase_order_dict else 'NULL',
				PurchaseOrder._STATUS_DRAFT,
				purchase_order_dict['tax1type'] if 'tax1type' in purchase_order_dict else 'NULL',
				purchase_order_dict['tax1'] if 'tax1' in purchase_order_dict else 'NULL',
				purchase_order_dict['tax2'] if 'tax2' in purchase_order_dict else 'NULL',
				purchase_order_dict['purchase_account_id'] if 'purchase_account_id' in purchase_order_dict else 'NULL',
				purchase_order_dict['currency_code'] if 'currency_code' in purchase_order_dict else 'NULL',
				stock_type, enterprise_id,
				purchase_order_dict['pay_against'] if 'pay_against' in purchase_order_dict else 'NULL',
				purchase_order_dict['pay_in_days'] if 'pay_in_days' in purchase_order_dict else 'NULL',
				purchase_order_dict['pay_through'] if 'pay_through' in purchase_order_dict else 'NULL',
				curdatetime, user_id, po_type,
				purchase_order_dict['is_blanket_po'] if 'is_blanket_po' in purchase_order_dict else '0',
				'"%s"' % purchase_order_dict['valid_since'] if 'valid_since' in purchase_order_dict and purchase_order_dict['valid_since'] != '' else 'null',
				'"%s"' % purchase_order_dict['valid_till'] if 'valid_till' in purchase_order_dict and purchase_order_dict['valid_till'] != '' else 'null',
				purchase_order_dict['round_off'] if 'round_off' in purchase_order_dict else 'NULL',
				purchase_order_dict['issue_to'] if 'issue_to' in purchase_order_dict else 'NULL',
				shipping_name, shipping_address)
			cur.execute(po_insert_query)
			po_to_be_saved = purchase_order_dict
			po_to_be_saved["drafted_on"] = curdatetime
			po_to_be_saved["drafted_by"] = user_id
			po_to_be_saved["stock_type"] = stock_type
			po_to_be_saved["status"] = PurchaseOrder._STATUS_DRAFT
			po_to_be_saved["type"] = po_type
			po_to_be_saved["shipping_name"] = shipping_name
			po_to_be_saved["shipping_address"] = shipping_address
			# FIXME SELECT LAST_INSERT_ID()
			cur.execute("SELECT Max(id) FROM purchase_order")
			sid = cur.fetchall()
			po_id = sid[0][0]
			for material in po_materials:
				insert_po_material_tax_queries = []
				make_id = material['item_make']
				alternate_unit_id = ''
				enterprise = request_handler.getSessionAttribute(ENTERPRISE_IN_SESSION_KEY)
				if enterprise.is_multiple_units > 0 and material['alternate_unit_id'] != '0' and int(material['alternate_unit_id']) != 0:
					alternate_unit_id = material['alternate_unit_id']
					scale_factor = helper.getScaleFactor(
						enterprise_id=enterprise_id, item_id=material['item_id'], alternate_unit_id=material['alternate_unit_id'])
					if scale_factor:
						material['quantity'] = Decimal(material['quantity']) * Decimal(scale_factor)
						material['rate'] = Decimal(material['rate']) / Decimal(scale_factor)
				po_material_insert_query = """INSERT INTO purchase_order_material (
						pid, item_id,  pur_qty, po_price,
						discount, enterprise_id, make_id, alternate_unit_id)
					VALUES ('%s', '%s', '%s', '%s','%s','%s', NULLIF('%s', ''), NULLIF('%s', '')) """ % (
					po_id, material['item_id'],
					material['quantity'], material['rate'],
					material['discount'], enterprise_id, make_id, alternate_unit_id)

				# delivery schedule insert
				po_delivery_schedule_stock_query = []
				if material['delivery_schedules']:
					po_material_delivery_schedules = json.loads(material['delivery_schedules'])
					if len(po_material_delivery_schedules) > 0:
						po_delivery_schedule_remove_query = """DELETE FROM purchase_order_material_delivery_schedules 
						WHERE po_id ='%s' AND item_id ='%s' AND make_id='%s' AND enterprise_id='%s'""" % (
							po_id, material['item_id'], make_id, enterprise_id)
						cur.execute(po_delivery_schedule_remove_query)
						for schedule in po_material_delivery_schedules:
							po_delivery_schedule_stock_query.append("""INSERT INTO purchase_order_material_delivery_schedules(						
								po_id, item_id, make_id, qty, due_date, enterprise_id) VALUES('%s', '%s', '%s', '%s', '%s', '%s')""" % (
								po_id, material['item_id'], make_id, schedule['delivery_qty'], schedule['delivery_date'] + " 00:00:00", enterprise_id))

				for tax in material['material_taxes']:
					if tax != "0":
						insert_po_material_tax_queries.append("""INSERT INTO purchase_order_material_tax (
								po_id, item_id, enterprise_id, tax_code,make_id) 
							VALUES ('%s', '%s', '%s','%s', NULLIF('%s', '')) """ % (
							po_id, material['item_id'], enterprise_id, tax,
							make_id))
				logger.debug(po_material_insert_query)
				cur.execute(po_material_insert_query)
				for query in insert_po_material_tax_queries:
					cur.execute(query)
				if po_delivery_schedule_stock_query:
					for query in po_delivery_schedule_stock_query:
						cur.execute(query)

			logger.debug(po_taxes)
			for po_tax in po_taxes:
				po_tax_insert_query = """INSERT INTO purchase_order_tax (po_id, tax_code, tax_order,enterprise_id) 
						VALUES ('%s', '%s', '%s', '%s')""" % (
					po_id, po_tax['tax_code'], po_tax['tax_order'], enterprise_id)
				cur.execute(po_tax_insert_query)

			logger.debug("PO Tag Details:%s" % po_tags)
			if len(po_tags) > 0:
				for tag in po_tags:
					if tag['tag_id'] == "0":
						cur.execute("SELECT count(1) FROM tags where tag='%s' and enterprise_id='%s'" % (
							tag['tag_name'], enterprise_id))
						existing_tag_count = cur.fetchall()
						if existing_tag_count[0][0] == 0:
							new_tag_insert_query = "INSERT INTO tags(tag, enterprise_id) values('%s','%s')" % (
								tag['tag_name'], enterprise_id)
							cur.execute(new_tag_insert_query)
							logger.debug("New Tag Query%s" % new_tag_insert_query)
							cur.execute("SELECT Max(id) FROM tags where enterprise_id='%s'" % enterprise_id)
							tagid = cur.fetchall()
							tag_id = tagid[0][0]
						else:
							cur.execute("SELECT id FROM tags where tag='%s' and enterprise_id='%s'" % (
								tag['tag_name'], enterprise_id))
							tagid = cur.fetchall()
							tag_id = tagid[0][0]
					else:
						tag_id = tag['tag_id']
					po_tag_insert_query = """INSERT INTO purchase_order_tags (po_id, tag_id, enterprise_id) 
												VALUES ('%s', '%s', '%s')""" % (po_id, tag_id, enterprise_id)
					cur.execute(po_tag_insert_query)
			conn.commit()
			conn.close()
			purchase_service.updateRemarks(user_id, enterprise_id, po_id, purchase_order_dict['po_remarks'])
			po_to_be_saved["id"] = po_id
			po_need_to_load = PurchaseService().purchase_dao.getPurchaseOrder(enterprise_id=enterprise_id, po_id=po_id)
			po_to_be_saved['new_remarks'] = []
			if po_need_to_load.remarks:
				po_to_be_saved['new_remarks'] = po_need_to_load.remarks
			PurchaseChangelog().queryInsert(user_id=user_id, enterprise_id=enterprise_id, data=po_to_be_saved)
			order_type = 'Job order' if po_type == 1 else('Work Order' if po_type == 2 else 'Purchase order')
			response_json = simplejson.dumps({'message': order_type +" Saved Successfully", 'draft_no': po_id})
			if purchase_order_dict['quick_po_type'] == 4:
				purchase_service.approve_po(
					enterprise_id=enterprise_id, po_id=po_id, user_id=user_id, remarks=None)
				response_json = simplejson.dumps(
					{'message': order_type + " Saved & Approved Successfully", 'draft_no': po_id})
			else:
				purchase_service.notifyPendingPOApprovalCount(enterprise_id=enterprise_id, sender_id=user_id)

			return HttpResponse(response_json)
		except Exception as e:
			logger.exception('Error : %s' % e)
			conn.rollback()
			conn.close()
			response_json = simplejson.dumps("Purchase Order Failed to Save")
			return HttpResponse(response_json)
	return HttpResponseRedirect(properties.LOGIN_URL)


def editPO(request):
	"""

	:param request:
	:return:
	"""

	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	if request_handler.isPostRequest():
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
		cur = conn.cursor()
		try:
			purchase_order_dict = json.loads(request.POST.get('purchase_order_dict'))
			po_materials = purchase_order_dict['materials']
			po_taxes = purchase_order_dict['taxes'] if 'taxes' in purchase_order_dict else ""
			po_tags = purchase_order_dict['tags'] if 'tags' in purchase_order_dict else ""
			po_id = purchase_order_dict['po_id']
			po_type = int(purchase_order_dict['po_type'])
			modifying_user = UserDAO().getUserById(enterprise_id=enterprise_id, user_id=user_id)
			purchase_service = PurchaseService()
			try:
				purchase_service.purchase_dao.db_session.begin(subtransactions=True)
				po = purchase_service.purchase_dao.getPurchaseOrder(enterprise_id=enterprise_id, po_id=po_id)
				po.last_modified_on = datetime.now()
				po.last_modified_by = user_id
				po.updateRemarks(remarks=purchase_order_dict['po_remarks'], user=modifying_user)
				if int(po.type) != po_type and po.status not in (PurchaseOrder._STATUS_DRAFT, PurchaseOrder._STATUS_REVIEWED):
					response = purchase_service.superEditPOCode(
						enterprise_id=enterprise_id, user_id=user_id, po_id=po_id, new_financial_year=po.financial_year,
						new_po_type=po_type, new_po_no=po.po_no, new_sub_number=po.sub_number)
					if response['response_message'] != "Success":
						logger.warn("Edit PO/JO Failed! %s " % response)
						purchase_service.purchase_dao.db_session.rollback()
						return HttpResponse(json.dumps(response), 'content-type=text/json')
				purchase_service.purchase_dao.db_session.commit()
			except:
				purchase_service.purchase_dao.db_session.rollback()
				raise

			cur.execute("SELECT status FROM purchase_order where id='%s'" % po_id)
			status_info = cur.fetchall()
			if status_info[0][0] == PO_STATUS_REVIEWED:
				status = 0
			elif status_info[0][0] == PO_STATUS_REJECTED:
				status = 2
			else:
				status = status_info[0][0]
			approved_on = purchase_order_dict['approved_on']
			approved_on = ("'%s'" % approved_on) if approved_on and approved_on.strip() != '' else 'NULL'
			purchase_order_dict['valid_since'] = '"%s"' % purchase_order_dict['valid_since'] \
				if purchase_order_dict['valid_since'] != '' else 'null'
			purchase_order_dict['valid_till'] = '"%s"' % purchase_order_dict['valid_till'] \
				if purchase_order_dict['valid_till'] != '' else 'null'

			po_to_be_saved = purchase_order_dict
			po_to_be_saved["id"] = purchase_order_dict['po_id']
			po_to_be_saved["status"] = status
			po_to_be_saved["type"] = po_type
			po_need_to_load = PurchaseService().purchase_dao.getPurchaseOrder(enterprise_id=enterprise_id, po_id=po_id)
			if po_need_to_load.remarks:
				po_to_be_saved['new_remarks'] = po_need_to_load.remarks
			PurchaseChangelog().queryInsert(user_id=user_id, enterprise_id=enterprise_id, data=po_to_be_saved)

			update_po_query = """UPDATE purchase_order SET project_code = '%s', purpose='%s', sp_instructions='%s',
					supplier_id =%s, quotation_refno='%s', approved_on=%s, payment='%s', transport='%s',
					packing_forwarding='%s', delivery='%s', total='%s', user='%s', tax1type='%s', tax1amt='%s',
					tax2amt='%s', purchase_account_id='%s', currency_code='%s', payment_type='%s', payment_days='%s',
					payment_mode='%s', status='%s', last_modified_by='%s', indent_no='%s' , type='%s', is_blanket_po=%s,
					valid_since=%s, valid_till=%s, round_off=%s, issue_to=%s, shipping_name='%s', shipping_address='%s'
				WHERE id = '%s'""" % (
				purchase_order_dict['project_code'], purchase_order_dict['purpose'],
				purchase_order_dict['closing_remarks'] if 'closing_remarks' in purchase_order_dict else 'NULL',
				str(purchase_order_dict['supplier_id']) if 'supplier_id' in purchase_order_dict and purchase_order_dict['supplier_id'] != "" else 'NULL',
				purchase_order_dict['quotation_ref_no'] if 'quotation_ref_no' in purchase_order_dict else 'NULL',
				approved_on, purchase_order_dict['payment'] if 'payment' in purchase_order_dict else 'NULL',
				purchase_order_dict['transport'] if 'transport' in purchase_order_dict else 'NULL',
				purchase_order_dict['packing'] if 'packing' in purchase_order_dict else 'NULL',
				purchase_order_dict['delivery'] if 'delivery' in purchase_order_dict else 'NULL',
				purchase_order_dict['total'] if 'total' in purchase_order_dict else 'NULL', user_id,
				purchase_order_dict['tax1type'] if 'tax1type' in purchase_order_dict else 'NULL',
				purchase_order_dict['tax1'] if 'tax1' in purchase_order_dict else 'NULL',
				purchase_order_dict['tax2'] if 'tax2' in purchase_order_dict else 'NULL',
				purchase_order_dict['purchase_account_id'] if 'purchase_account_id' in purchase_order_dict else 'NULL',
				purchase_order_dict['currency_code'] if 'currency_code' in purchase_order_dict else 'NULL',
				purchase_order_dict['pay_against'] if 'pay_against' in purchase_order_dict else 'NULL',
				purchase_order_dict['pay_in_days'] if 'pay_in_days' in purchase_order_dict else 'NULL',
				purchase_order_dict['pay_through'] if 'pay_through' in purchase_order_dict else 'NULL',
				status, user_id, purchase_order_dict['indent_no'] if 'indent_no' in purchase_order_dict and purchase_order_dict['indent_no'] != 'NULL' else '000000',
				po_type, purchase_order_dict['is_blanket_po'] if 'is_blanket_po' in purchase_order_dict else '0',
				purchase_order_dict['valid_since'] if 'valid_since' in purchase_order_dict and purchase_order_dict['valid_since'] != '' else 'null',
				purchase_order_dict['valid_till'] if 'valid_till' in purchase_order_dict and purchase_order_dict['valid_till'] != '' else 'null',
				purchase_order_dict['round_off'] if 'round_off' in purchase_order_dict else 'NULL',
				purchase_order_dict['issue_to'] if 'issue_to' in purchase_order_dict and purchase_order_dict['issue_to'] != "" else 'NULL',
				purchase_order_dict['shipping_name'], purchase_order_dict['shipping_address'], po_id)
			cur.execute(update_po_query)
			if 'quotation_date' in purchase_order_dict and purchase_order_dict['quotation_date'] != '':
				quotation_date = '%s' % purchase_order_dict['quotation_date']
				update_quote_date_query = """UPDATE purchase_order SET quotation_date='{quotation_date}'					
				WHERE id = {po_id}""".format(quotation_date=quotation_date, po_id=po_id)
			else:
				quotation_date = 'null'
				update_quote_date_query = """UPDATE purchase_order SET quotation_date={quotation_date}					
				WHERE id = {po_id}""".format(quotation_date=quotation_date, po_id=po_id)
			cur.execute(update_quote_date_query)

			cur.execute("delete FROM purchase_order_material WHERE pid = " + po_id + "")
			cur.execute("delete FROM purchase_order_material_tax WHERE po_id = " + po_id + "")
			cur.execute("delete FROM purchase_order_tax WHERE po_id = " + po_id + "")
			cur.execute("delete FROM purchase_order_tags WHERE po_id = " + po_id + "")

			for material in po_materials:
				insert_po_material_tax_queries = []
				make_id = material['item_make']
				alternate_unit_id = ''
				enterprise = request_handler.getSessionAttribute(ENTERPRISE_IN_SESSION_KEY)
				if enterprise.is_multiple_units > 0 and material['alternate_unit_id'] != '0' and int(material['alternate_unit_id']) != 0:
					alternate_unit_id = material['alternate_unit_id']
					scale_factor = helper.getScaleFactor(
						enterprise_id=enterprise_id, item_id=material['item_id'],
						alternate_unit_id=material['alternate_unit_id'])
					if scale_factor:
						material['quantity'] = float(material['quantity']) * float(scale_factor)
						material['rate'] = float(material['rate']) / float(scale_factor)
				po_material_insert_query = """INSERT INTO purchase_order_material (
						pid, item_id,  pur_qty, po_price,
						discount, enterprise_id, make_id, alternate_unit_id)
					VALUES ('%s', '%s', '%s', '%s','%s','%s', NULLIF('%s', ''), NULLIF('%s', '')) """ % (
					po_id, material['item_id'],
					material['quantity'], material['rate'],
					material['discount'], enterprise_id, make_id, alternate_unit_id)

				# delivery schedule insert
				po_material_delivery_schedules = json.loads(material['delivery_schedules'])
				po_delivery_schedule_query = []
				if len(po_material_delivery_schedules) > 0:
					po_delivery_schedule_remove_query = """DELETE FROM purchase_order_material_delivery_schedules 
										WHERE po_id ='%s' AND item_id ='%s' AND make_id='%s' AND enterprise_id='%s'""" % (
						po_id, material['item_id'], make_id, enterprise_id)
					cur.execute(po_delivery_schedule_remove_query)
					for schedule in po_material_delivery_schedules:
						po_delivery_schedule_query.append("""INSERT INTO purchase_order_material_delivery_schedules(						
												po_id, item_id, make_id, qty, due_date, enterprise_id) VALUES('%s', '%s', '%s', '%s', '%s', '%s')""" % (
							po_id, material['item_id'], make_id, schedule['delivery_qty'],
							schedule['delivery_date'] + " 00:00:00", enterprise_id))
				for tax in material['material_taxes']:
					if tax != "0":
						insert_po_material_tax_queries.append("""INSERT INTO purchase_order_material_tax (
								po_id, item_id, enterprise_id, tax_code,make_id) 
							VALUES ('%s', '%s', '%s','%s', NULLIF('%s', '')) """ % (
							po_id, material['item_id'], enterprise_id, tax,
							make_id))
				logger.debug(po_material_insert_query)
				cur.execute(po_material_insert_query)
				for query in insert_po_material_tax_queries:
					cur.execute(query)
				for query in po_delivery_schedule_query:
					cur.execute(query)

			logger.debug(po_taxes)
			for po_tax in po_taxes:
				po_tax_insert_query = """INSERT INTO purchase_order_tax (po_id, tax_code, tax_order,enterprise_id) 
						VALUES ('%s', '%s', '%s', '%s')""" % (
					po_id, po_tax['tax_code'], po_tax['tax_order'], enterprise_id)
				logger.info('PO Tax Insert Query - %s' % po_tax_insert_query)
				cur.execute(po_tax_insert_query)

			logger.debug("PO Tag Details:%s" % po_tags)
			if len(po_tags) > 0:
				for tag in po_tags:
					if tag['tag_id'] == "0":
						cur.execute("SELECT count(1) FROM tags where tag='%s' and enterprise_id='%s'" % (
							tag['tag_name'], enterprise_id))
						existing_tag_count = cur.fetchall()
						if existing_tag_count[0][0] == 0:
							new_tag_insert_query = "INSERT INTO tags(tag, enterprise_id) values('%s','%s')" % (
								tag['tag_name'], enterprise_id)
							cur.execute(new_tag_insert_query)
							logger.debug("New Tag Query%s" % new_tag_insert_query)
							cur.execute("SELECT Max(id) FROM tags where enterprise_id='%s'" % enterprise_id)
							tagid = cur.fetchall()
							tag_id = tagid[0][0]
						else:
							cur.execute("SELECT id FROM tags where tag='%s' and enterprise_id='%s'" % (
								tag['tag_name'], enterprise_id))
							tagid = cur.fetchall()
							tag_id = tagid[0][0]
					else:
						tag_id = tag['tag_id']
					po_tag_insert_query = """INSERT INTO purchase_order_tags (po_id, tag_id, enterprise_id) 
												VALUES ('%s', '%s', '%s')""" % (po_id, tag_id, enterprise_id)
					logger.info('PO Tag Insert Query - %s' % po_tag_insert_query)
					cur.execute(po_tag_insert_query)

			conn.commit()
			conn.close()
			if po_type == 0:
				response_json = simplejson.dumps({'message': "Purchase Order Updated Successfully", 'draft_no': po_id})
			else:
				response_json = simplejson.dumps({'message': "Job Order Updated Successfully", 'draft_no': po_id})
			purchase_service.notifyPendingPOApprovalCount(
				enterprise_id=enterprise_id, sender_id=user_id,code=po.getCode(),type="PO" if po_type == 0 else 'JO')
			return HttpResponse(response_json)
		except Exception as e:
			conn.rollback()
			conn.close()
			logger.exception('Error : %s' % e)
			response_json = simplejson.dumps("Purchase Order Failed to Update")
			return HttpResponse(response_json)
	return HttpResponseRedirect(properties.LOGIN_URL)


def loadPO(request):  # load PO
	"""
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	po_list = []
	try:
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		since, till = JsonUtil.getDateRange(
			rh=request_handler, since_session_key="po.list.since", till_session_key="po.list.till")
		project_id = request_handler.getAndCacheData(key="project_id", session_key="po.list.project_id")
		status = request_handler.getAndCacheData(key="status", session_key="po.list.status")
		order_type = request_handler.getAndCacheData(key="order_type", session_key="po.list.order_type")
		condition = " t.order_date between  '%s' and '%s' and t.enterprise_id = '%s'" % (
			since, till, enterprise_id)
		if project_id != "0":
			condition += " AND t.project_code='%s'" % project_id
		if status != "5":
			condition += " AND t.status='%s'" % status
		if order_type:
			order_type_params = ORDER_TYPE_DICT[order_type]
			condition += " AND t.type='%s'" % order_type_params
		query = """SELECT t.id, DATE_FORMAT(t.order_date,'%%d-%%m-%%Y') AS tDate,
				CONCAT(t.orderno, IFNULL(t.sub_number, '')) as orderno, 
				DATE_FORMAT(t.approved_on,'%%d-%%m-%%Y') AS poDate,s.party_name, t.total, IFNULL(CONCAT(p.name,' (', p.code ,')'),""),
				t.indent_no, t.status, e.code, t.financial_year,
				CONCAT(i.financial_year, IF(i.indent_module_id = 0,'/IND/', '/MI/'), LPAD(i.indent_id, 6, 0), IFNULL(i.sub_number, '')),
				i.financial_year, t.type, t.is_mask_po  
			FROM purchase_order t LEFT JOIN party_master s ON s.party_id=t.supplier_id AND s.enterprise_id=t.enterprise_id 
				LEFT JOIN enterprise e ON t.enterprise_id=e.id 
				LEFT JOIN indents as i ON i.indent_no=t.indent_no AND t.enterprise_id=i.enterprise_id
				LEFT JOIN projects p ON t.project_code=p.id AND t.enterprise_id=p.enterprise_id 
			WHERE %s GROUP BY t.id ORDER by t.order_date, t.id""" % condition

		po_list = executeQuery(query)
	except Exception as e:
		logger.error(e)
	return HttpResponse(content=simplejson.dumps(po_list), mimetype='application/json')


def loadPOHeader(request):  # load PO header info
	"""

	:param request:
	:return:
	"""

	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	if request_handler.isPostRequest():
		po_id = request_handler.getPostData('po_id')
		# TODO UPDATE THE QUERY EFFECTIVELY
		po_header_query = """SELECT t.id, COALESCE((SELECT id FROM projects WHERE is_active = 1 AND id = p.id), (SELECT 
				id FROM projects WHERE is_active = 1 AND enterprise_id = '%s' LIMIT 1)) AS code, 
				DATE_FORMAT(t.order_date,'%%d/%%m/%%Y %%T') AS tDate, s.party_id,
				t.purpose, t.sp_instructions, t.quotation_refno, DATE_FORMAT(t.quotation_date, '%%Y-%%m-%%d') AS qDate,
				t.payment, t.transport, t.packing_forwarding, DATE_FORMAT(t.delivery, '%%Y-%%m-%%d') AS dDate,
				t.total, t.tax1type, t.tax1amt, t.tax2amt, t.orderno as orderno, t.purchase_account_id, t.currency_code, t.status, 
				t.stock_type, t.financial_year, t.payment_type, t.payment_days, t.payment_mode, t.remarks,
				t.indent_no, ifnull((SELECT indent_id FROM indents WHERE indent_no=t.indent_no), 0) as indent_id, 
				IFNULL((SELECT financial_year FROM indents WHERE indent_no=t.indent_no), 0) as indent_financiyal_year, 
				IFNULL((SELECT COUNT(1) FROM purchase_order_document WHERE po_id=t.id), 0) as revision_no,  t.type as po_type,
				IFNULL((SELECT sp_instructions FROM indents WHERE indent_no=t.indent_no), "")as indent_sp_instructions, IFNULL(t.sub_number,"") as po_sub_number,
				IFNULL(t.approved_on, '') AS approvedOn, ifnull((SELECT sub_number FROM indents WHERE indent_no=t.indent_no), '') as indent_sub_number,
				t.is_blanket_po, DATE_FORMAT(t.valid_since, '%%Y-%%m-%%d') AS valid_since, DATE_FORMAT(t.valid_till, '%%Y-%%m-%%d') AS valid_till,
				IFNULL(t.round_off, '0.00') as round_off,
				IFNULL((SELECT indent_module_id FROM indents WHERE indent_no=t.indent_no), 0) as indent_module_id,
				t.shipping_name, t.shipping_address
			FROM purchase_order AS t, projects AS p, party_master AS s 
			WHERE t.project_code=p.id AND s.party_id=t.supplier_id 
				AND t.id='%s' AND t.enterprise_id=p.enterprise_id AND t.enterprise_id='%s'""" % (
			enterprise_id, po_id, enterprise_id)
		try:
			po_header_info = executeQuery(po_header_query, as_dict=True)
		except Exception as e:
			logger.exception('Error : %s' % e)
			po_header_info = ""
		return HttpResponse(content=simplejson.dumps(po_header_info), mimetype='application/json')
	return HttpResponseRedirect(properties.LOGIN_URL)


def loadTax(request):
	"""
	Loads the Tax and Sub-tax information upon any ajax call to add a Tax Profile to a PO.

	:param request:
	:return:
	"""
	# TODO Move to masters
	request_handler = RequestHandler(request)
	if request_handler.isPostRequest():
		tax_code = request_handler.getPostData('code')
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		tax_json_dump = []
		logger.info('Adding Tax: %s' % tax_code)
		try:
			tax = SQLASession().query(Tax).filter(Tax.code == tax_code, Tax.enterprise_id == enterprise_id).first()
			tax_json_dump = __generateTaxJSONDump(tax)
			logger.info("Loading tax dump with %s rows" % len(tax_json_dump))
		except:
			logger.exception('Failed to construct JSON dump for Tax...')
		return HttpResponse(content=simplejson.dumps(tax_json_dump), mimetype='application/json')
	return HttpResponseRedirect(properties.LOGIN_URL)


def __generateTaxJSONDump(tax):
	"""

	:param tax:
	:return:
	"""
	compound_suffix = '_compound' if tax.is_compound else ''
	tax_json_dump = [TAX_ROW_DUMP % (
		tax.code, tax.code, tax.code, tax.name, tax.net_rate, compound_suffix, tax.code, tax.net_rate, compound_suffix,
		compound_suffix, tax.code, tax.assess_rate), TAX_BASE_RATE_ROW_DUMP % (tax.code, 'Base Rate', tax.base_rate)]
	for sub_tax in tax.sub_taxes:
		tax_json_dump.append(SUB_TAX_ROW_DUMP % (tax.code, sub_tax.name, sub_tax.rate))
	return tax_json_dump


def loadPOTaxes(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	po_tax_dump = []
	if request_handler.isPostRequest():
		po_id = request_handler.getPostData('po_id')
		try:
			po_taxes = SQLASession().query(PurchaseOrderTax).filter(PurchaseOrderTax.po_id == po_id).all()
			for po_tax in po_taxes:
				po_tax_dump.append(__generateTaxJSONDump(po_tax.tax))
		except:
			logger.exception('Fetching of PO Taxes failed...')
		return HttpResponse(content=simplejson.dumps(po_tax_dump), mimetype='application/json')


def loadPOMaterials(request):  # load PO materials
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	result = []
	dc_item = []
	dc_item_val = {}
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	receipt_no = request_handler.getPostData("receipt_no")
	supplier_id = request_handler.getPostData("supplier_id")
	po_ids = request_handler.getPostData("po_ids")

	sid = " in (%s)" % po_ids
	condition = ""
	if receipt_no and receipt_no != "":
		condition = " AND gm.grnNumber <> %s " % receipt_no

	party_condition = ""
	if supplier_id:
		party_condition = "AND gn.party_id = " + supplier_id

	po_materials_query = """SELECT b.name, IFNULL(b.drawing_no, "") as drawing_no, b.tariff_no as hsn_code,
				IFNULL((SELECT request_qty FROM indent_material AS inm, indents AS ind 
				WHERE ind.indent_no = inm.indent_no AND a.item_id = inm.item_id AND a.make_id = inm.make_id  
					AND inm.indent_no=c.indent_no), 0) AS ind_qty, IF(c.indent_no=0 OR c.indent_no IS NULL, 0, 
				IFNULL((SELECT SUM(pur_qty) FROM purchase_order_material AS pm, purchase_order AS pur 
					WHERE pur.id = pm.pid AND a.item_id = pm.item_id AND a.make_id = pm.make_id
						AND pur.indent_no=c.indent_no and pur.status <3), 0) -  IF (c.status != 3,a.pur_qty,0)) AS already_po_qty, a.pur_qty, a.pur_qty,
			IFNULL((SELECT price FROM supplier_materials_price 
				WHERE item_id=a.item_id AND supp_id=c.supplier_id AND make_id=a.make_id AND status=1
				ORDER BY effect_since DESC LIMIT 0, 1), 0) AS price,
			IFNULL((SELECT minimum_order_quantity FROM supplier_materials_price 
				WHERE item_id=a.item_id AND supp_id=c.supplier_id AND make_id=a.make_id AND status=1
				ORDER BY effect_since DESC LIMIT 0, 1), 0) AS moq,
			a.po_price, a.pur_qty as total, DATE_FORMAT(c.delivery, '%%m/%%d/%%Y') AS delivery_date, c.financial_year, IF(LENGTH(c.orderno) < 6, LPAD(c.orderno, 6, '0'), c.orderno) as orderno, c.id,0 as stock_type,
			IFNULL((SELECT SUM(acc_qty) FROM grn_material AS gm, grn AS g 
				WHERE g.grn_no=gm.grnNumber AND g.status >-1 AND a.item_id=gm.item_id AND gm.po_no=c.id 
					AND gm.make_id=a.make_id AND gm.dc_id IS NULL AND gm.enterprise_id=a.enterprise_id {condition} AND gm.rec_grn_id is NULL), 0) AS rec_qty,
			c.project_code, d.unit_name AS unit, a.discount, 
			IFNULL((SELECT tax_code FROM purchase_order_material_tax AS mt, tax AS t 
				WHERE mt.po_id = a.pid AND mt.item_id = a.item_id AND mt.enterprise_id=t.enterprise_id 
					AND mt.tax_code=t.code AND t.type='CGST' AND mt.make_id=a.make_id 
					AND mt.enterprise_id=a.enterprise_id), 0) AS CGST,
			IFNULL((SELECT tax_code FROM purchase_order_material_tax AS mt, tax AS t 
				WHERE mt.po_id = a.pid AND mt.item_id = a.item_id AND mt.enterprise_id=t.enterprise_id 
					AND mt.tax_code=t.code AND t.type='SGST' AND mt.make_id=a.make_id 
					AND mt.enterprise_id=a.enterprise_id), 0) AS SGST,
			IFNULL((SELECT tax_code FROM purchase_order_material_tax AS mt, tax AS t 
				WHERE mt.po_id = a.pid AND mt.item_id = a.item_id AND mt.enterprise_id=t.enterprise_id 
					AND mt.tax_code=t.code AND t.type='IGST' AND mt.make_id=a.make_id 
					AND mt.enterprise_id=a.enterprise_id), 0) AS IGST,
			d.unit_id AS unit_id, IFNULL(a.make_id, 1) as make_id, 
			IFNULL(b.makes_json, '') AS make_name,
			c.type as po_type,c.indent_no as indent_type,
			IFNULL(c.sub_number, " ") as sub_number,IFNULL((SELECT group_concat(JSON_OBJECT('code', t.code,'type', t.type )  SEPARATOR ';') AS tcode  FROM
			grn gn JOIN	grn_material_tax gmt ON gn.grn_no = gmt.grn_no AND gmt.enterprise_id = gn.enterprise_id
			JOIN tax AS t ON gmt.tax_code = t.code	AND gn.enterprise_id = t.enterprise_id	WHERE
			gmt.item_id = a.item_id	AND t.type  IN  ('SGST','CGST','IGST')	AND gmt.make_id = a.make_id	{party_condition}
			AND gmt.enterprise_id = t.enterprise_id	AND gn.status = 1 group by gn.grn_no ORDER BY gn.approved_on DESC
			limit 1),(SELECT group_concat(JSON_OBJECT('code', t.code,'type', t.type )  SEPARATOR ';') AS tcode FROM grn gn JOIN grn_material_tax gmt ON gn.grn_no = gmt.grn_no
			AND gmt.enterprise_id = gn.enterprise_id JOIN	tax AS t ON gmt.tax_code = t.code	AND gn.enterprise_id = t.enterprise_id
			WHERE	gmt.item_id = a.item_id	AND t.type  IN  ('SGST','CGST','IGST')	AND gmt.make_id = a.make_id
			AND gmt.enterprise_id = t.enterprise_id	AND gn.status = 1 group by gn.grn_no ORDER BY gn.approved_on DESC
			limit 1)) as smart_taxes, a.item_id as item_id,IFNULL(c.purchase_account_id, 0) as purchase_account_id,
			IFNULL(a.alternate_unit_id, 0) as alternate_unit_id,
			IFNULL(b.standard_packing_quantity, '') AS spq, c.is_blanket_po, b.is_service as is_service
		FROM purchase_order_material AS a, materials AS b, purchase_order AS c, unit_master AS d 
		WHERE TRIM(b.id)=TRIM(a.item_id) AND c.id = a.pid AND pid {sid} 
			AND b.enterprise_id = a.enterprise_id AND d.unit_id = b.unit AND d.enterprise_id = b.enterprise_id""".format(
		condition=condition, party_condition=party_condition, sid=sid)
	try:
		po_materials = executeQuery(po_materials_query, as_dict=True)
		for material in po_materials:
			ds_query = """SELECT DATE_FORMAT(due_date, '%%Y-%%m-%%d') AS delivery_date, qty AS delivery_qty from 
				purchase_order_material_delivery_schedules WHERE po_id = %s AND item_id = %s AND make_id = %s 
				AND enterprise_id = %s order by due_date asc""" % (
				material['id'], material['item_id'], material['make_id'], enterprise_id)
			material['delivery_schedules'] = executeQuery(ds_query, as_dict=True)
			material['scale_factor'] = 1
			if material['alternate_unit_id'] and int(material['alternate_unit_id']) != 0:
				scale_factor = helper.getScaleFactor(
					enterprise_id=enterprise_id, item_id=material['item_id'], alternate_unit_id=material['alternate_unit_id'])
				material['unit'] = helper.getUnitName(enterprise_id=enterprise_id, unit_id=material['alternate_unit_id'])
				if scale_factor:
					material['ind_qty'] = material['ind_qty'] / scale_factor
					material['already_po_qty'] = material['already_po_qty'] / scale_factor
					material['pur_qty'] = material['pur_qty'] / scale_factor if material['pur_qty'] != 0 else 0
					material['rec_qty'] = material['rec_qty'] / scale_factor if material['rec_qty'] != 0 else 0
					material['price'] = material['price'] * scale_factor
					material['po_price'] = material['po_price'] * scale_factor
					material['scale_factor'] = scale_factor if scale_factor else 1
		result.append(po_materials)
		dc_invoices = SQLASession().query(Invoice).filter(
			Invoice.job_po_id.in_(po_ids.split(",")), Invoice.enterprise_id == enterprise_id, Invoice.status == 1).all()
		# ReceiptMaterial
		for dc_invoice in dc_invoices:
			po_id = dc_invoice.job_po.po_id
			for item in dc_invoice.items:
				rec_qty = 0
				data_query = """SELECT SUM(acc_qty) FROM grn_material AS a, grn AS b 
						WHERE b.status > -1 AND a.grnNumber = b.grn_no AND 
						a.dc_id = {dc_id} AND a.po_no = {po_id} AND a.rec_grn_id is null AND 
						a.item_id='{item_id}' AND a.make_id={make_id} AND a.is_faulty = {is_faulty}""".format(
					dc_id=item.invoice_id, po_id=po_id, item_id=item.item_id, make_id=item.make_id,
					is_faulty=item.is_faulty)
				logger.debug("Query:%s" % data_query)
				data_materials = executeQuery(data_query)
				make_name = helper.constructDifferentMakeName(item.item.makes_json)
				if data_materials:
					rec_qty = data_materials[0][0] if data_materials[0][0] is not None else '0'
				if (float(item.quantity) - float(rec_qty)) != 0:
					dc_item_val['dc_mat_pending_qty'] = float(item.quantity) - float(rec_qty)
					dc_item_val['item_id'] = item.item.material_id
					dc_item_val['drawing_no'] = item.item.drawing_no
					dc_item_val['dc_id'] = item.invoice_id
					dc_item_val['item'] = item.item.name
					dc_item_val['hsn_code'] = item.hsn_code
					dc_item_val['qty'] = str(item.quantity)
					dc_item_val['unit_name'] = item.item.unit.unit_name
					dc_item_val['unit_id'] = item.item.unit.unit_id
					dc_item_val['rate'] = str(item.rate)
					dc_item_val['discount'] = str(item.discount)
					dc_item_val['price'] = str(item.item.price)
					dc_item_val['po_id'] = po_id
					dc_item_val['make_id'] = item.make_id
					dc_item_val['make_name'] = item.item.makes_json if item.item.makes_json else ""
					dc_item_val['dc_code'] = dc_invoice.getInternalCode()
					dc_item_val['dc_item_display_name'] = "%s" % item.item.name
					if item.make_id != DEFAULT_MAKE_ID:
						part_no = helper.getMakePartNumber(
							enterprise_id=item.enterprise_id, item_id=item.item_id, make_id=item.make_id)
						dc_item_val['dc_item_display_name'] += " [%s%s]" % (item.make.label, "-%s" % part_no if part_no else "")
					dc_item_val['dc_item_display_name'] += " - %s" % item.item.drawing_no if item.item.drawing_no else ""
					if item.is_faulty is True:
						dc_item_val['is_faulty'] = 1
						dc_item_val['dc_item_display_name'] += " [Faulty]"
					else:
						dc_item_val['is_faulty'] = 0
					dc_item_val['is_stock'] = 0
					dc_item_val['mat_type'] = 0
					dc_item_val['alternate_unit_id'] = 0
					dc_item_val['scale_factor'] = 1
					if item.alternate_unit_id and int(item.alternate_unit_id) != 0:
						scale_factor = helper.getScaleFactor(
							enterprise_id=enterprise_id, item_id=item.item_id, alternate_unit_id=item.alternate_unit_id)
						dc_item_val['unit_name'] = helper.getUnitName(enterprise_id=enterprise_id, unit_id=item.alternate_unit_id)
						if scale_factor:
							dc_item_val['dc_mat_pending_qty'] = round(Decimal(dc_item_val['dc_mat_pending_qty']) / Decimal(scale_factor), 3)
							dc_item_val['qty'] = round(Decimal(dc_item_val['dc_mat_pending_qty']) / Decimal(scale_factor), 3)
							dc_item_val['alternate_unit_id'] = item.alternate_unit_id
							dc_item_val['scale_factor'] = scale_factor if scale_factor else 1

					dc_item.append(dc_item_val.copy())
		result.append(dc_item)
	except Exception as e:
		logger.exception("Error : %s" % e)
	return HttpResponse(content=simplejson.dumps(result), mimetype='application/json')


def loadPOTags(request):  # load PO Tags
	po_ids = RequestHandler(request).getPostData("po_ids")
	tag_query = """SELECT DISTINCT (b.tag), a.tag_id FROM purchase_order_tags AS a, tags AS b 
		WHERE b.id=a.tag_id AND a.po_id in (%s) AND b.enterprise_id=a.enterprise_id""" % po_ids
	try:
		tag_list = executeQuery(tag_query)
	except Exception as e:
		logger.exception(e)
		tag_list = []
	response = HttpResponse(content=simplejson.dumps(tag_list), mimetype='application/json')
	return response


def loadIndentTags(request):  # load Ind Tags
	indent_no = RequestHandler(request).getPostData("indent_no")
	tag_query = """SELECT b.tag, a.tag_id FROM indent_tags AS a, tags AS b
			WHERE b.id=a.tag_id AND a.indent_id='%s' AND b.enterprise_id=a.enterprise_id""" % indent_no
	try:
		tag_list = executeQuery(tag_query)
	except Exception as e:
		logger.exception("Indent Tags cannot be fetched - %s" % e.message)
		tag_list = ""
	response = HttpResponse(content=simplejson.dumps(tag_list), mimetype='application/json')
	return response


def approve(request):
	"""
	Assigns a permanent identification number for the Purchase Order. Sets the PO's status as approved.

	:param request:
	:return: JSON dumps to the Ajax call that initiated this approval
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getPostData('enterprise_id')
	user_id = request_handler.getPostData("user_id")
	if not user_id:
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
	if not enterprise_id:
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	po_id = request_handler.getPostData('po_id')
	remarks = request_handler.getPostData('remarks')
	project_code = request_handler.getPostData('project_code')
	logger.info("Approving PO - po_id: %s User ID : %s" % (po_id, user_id))
	try:
		if po_id in (None, ""):
			raise Exception("PO Id given for approval is invalid")
		po_code = PurchaseService().approve_po(
			enterprise_id=enterprise_id, po_id=po_id, user_id=user_id, remarks=remarks, project_code=project_code)
		response = response_code.success()
		response['code'] = po_code
		return HttpResponse(simplejson.dumps(response), 'content-type=text/json')
	except Exception as e:
		logger.exception("PO Approval is failed %s" % e.message)
		response = response_code.internalError()
		response['custom_message'] = '%s' % e.message
		return HttpResponse(simplejson.dumps(response), 'content-type=text/json')

def reject(request):
	"""
	Rejects

	:param request:
	:return:
	"""
	logger.info('Rejecting a PO...')
	request_handler = RequestHandler(request)
	po_id = request_handler.getPostData('po_id')
	rejection_remarks = request_handler.getPostData('remarks')
	user_id = request_handler.getPostData("user_id")
	if not user_id:
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
	if po_id is None:
		po_id = request_handler.getPostData('po_id')
		rejection_remarks = request_handler.getPostData('remarks')
	db_session = SQLASession()
	db_session.begin(subtransactions=True)
	try:
		logger.info('Rejecting a PO... %s ' % po_id)
		po_to_reject = db_session.query(PurchaseOrder).filter(PurchaseOrder.po_id == po_id).first()
		purchase_service = PurchaseService()
		if po_to_reject.status == PurchaseOrder._STATUS_DRAFT or po_to_reject.status == PurchaseOrder._STATUS_REVIEWED:
			logger.info("Removing the PO %s permanently" % po_id)
			purchase_service.notifyPoDiscardedMessage(
				enterprise_id=po_to_reject.enterprise_id, sender_id=user_id, purchase_order=po_to_reject,
				include_count=False)
			db_session.delete(po_to_reject)
			db_session.commit()
			purchase_service.notifyPendingPOApprovalCount(enterprise_id=po_to_reject.enterprise_id, sender_id=user_id)
			response = response_code.delete()
			response['po_no'] = po_to_reject.getCode()
			response['po_type'] = "PO" if po_to_reject.type == 0 else "JO"
			return HttpResponse(simplejson.dumps(response), 'content-type=text/json')
		po_to_reject.status = PurchaseOrder._STATUS_REJECTED
		modifying_user = UserDAO().getUserById(enterprise_id=po_to_reject.enterprise_id, user_id=user_id)
		po_to_reject.updateRemarks(remarks=rejection_remarks, user=modifying_user)
		db_session.commit()
		response = response_code.success()
		response['po_no'] = po_to_reject.getCode()
		response['po_type'] = "PO" if po_to_reject.type == 0 else "JO"
		purchase_service.notifyPoRejectedMessage(
			enterprise_id=po_to_reject.enterprise_id, sender_id=user_id, purchase_order=po_to_reject)
		return HttpResponse(simplejson.dumps(response), 'content-type=text/json')
	except Exception as e:
		logger.exception("PO Rejection is failed %s" % e.message)
		db_session.rollback()
		response = response_code.internalError()
		response['error'] = '%s' % e.message
		return HttpResponse(simplejson.dumps(response), 'content-type=text/json')


def review(request):
	"""
	Review

	:param request:
	:return:
	"""
	logger.info('Review a PO...')
	db_session = SQLASession()
	request_handler = RequestHandler(request)
	user_id = request_handler.getPostData("user_id")
	if not user_id:
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
	db_session.begin(subtransactions=True)
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getPostData('enterprise_id')
		if enterprise_id is None:
			enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		reviewed_by = request_handler.getPostData("user_id")
		if reviewed_by is None:
			reviewed_by = request_handler.getSessionAttribute(SESSION_KEY)

		po_id = request_handler.getPostData('po_id')
		review_remarks = request_handler.getPostData('remarks')

		po_to_review = db_session.query(PurchaseOrder).filter(
			PurchaseOrder.po_id == po_id, PurchaseOrder.enterprise_id == enterprise_id).first()
		po_to_review.status = PurchaseOrder._STATUS_REVIEWED
		modifying_user = UserDAO().getUserById(enterprise_id=po_to_review.enterprise_id, user_id=user_id)
		po_to_review.updateRemarks(remarks=review_remarks, user=modifying_user)
		db_session.commit()
		PurchaseService().notifyPoReviewMessage(
			enterprise_id=po_to_review.enterprise_id, sender_id=reviewed_by, purchase_order=po_to_review)
		response = response_code.success()
		response['po_no'] = str(po_to_review.po_no)
	except Exception as e:
		db_session.rollback()
		logger.exception("Review a PO failed... %s" % e.message)
		response = response_code.internalError()
		response['error'] = e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')

def mask_po(request):
	"""
	Mask

	:param request:
	:return:
	"""
	logger.info('Mask a PO...')
	db_session = SQLASession()
	request_handler = RequestHandler(request)
	user_id = request_handler.getPostData("user_id")
	if not user_id:
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
	db_session.begin(subtransactions=True)
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getPostData('enterprise_id')
		if enterprise_id is None:
			enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		po_id = request_handler.getPostData('po_id')
		is_mask = request_handler.getPostData('is_mask')
		po_to_mask = db_session.query(PurchaseOrder).filter(
			PurchaseOrder.po_id == po_id, PurchaseOrder.enterprise_id == enterprise_id).first()
		po_to_mask.is_mask_po = True if int(is_mask) == 0 else False
		po_to_mask.last_modified_by = user_id
		db_session.commit()

		response = response_code.success()
		response['po_no'] = str(po_to_mask.po_no)
	except Exception as e:
		db_session.rollback()
		logger.exception("Masking a PO failed... %s" % e.message)
		response = response_code.internalError()
		response['error'] = e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def draft(request):
	"""
	Review

	:param request:
	:return:
	"""
	logger.info('Draft a PO...')
	request_handler = RequestHandler(request)
	po_id = request_handler.getPostData('po_id')
	db_session = SQLASession()
	db_session.begin(subtransactions=True)
	try:
		po_to_review = db_session.query(PurchaseOrder).filter(PurchaseOrder.po_id == po_id).first()
		po_to_review.status = PurchaseOrder._STATUS_DRAFT
		db_session.commit()
		return HttpResponse(simplejson.dumps(po_to_review.po_no))
	except Exception as e:
		db_session.rollback()
		logger.exception(e.message)
		return HttpResponse(simplejson.dumps("Draft Failed!!"))


def loadTransportDetails(request):
	"""
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	try:
		po_data_dump = executeQuery(
			"SELECT distinct(transport) FROM purchase_order where enterprise_id = '%s'" % enterprise_id)
	except Exception as e:
		logger.exception('Error : %s' % e)
		po_data_dump = ""
	response = HttpResponse(content=simplejson.dumps(po_data_dump), mimetype='application/json')
	return response


def loadPackingDetails(request):
	"""
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	po_query = "SELECT distinct(packing_forwarding) FROM purchase_order where enterprise_id = '%s'" % (enterprise_id)
	try:
		po_data_dump = executeQuery(po_query)
	except Exception as e:
		logger.exception('Error : %s' % e)
		po_data_dump = ""
	response = HttpResponse(content=simplejson.dumps(po_data_dump), mimetype='application/json')
	return response


def loadDeliveryDetails(request):
	"""
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	po_query = "SELECT distinct(delivery) FROM purchase_order where enterprise_id = '%s'" % (enterprise_id)
	try:
		po_data_dump = executeQuery(po_query)
	except Exception as e:
		logger.exception('Error : %s' % e)
		po_data_dump = ""
	response = HttpResponse(content=simplejson.dumps(po_data_dump), mimetype='application/json')
	return response


def countMaterialsReceivedForPO(request):
	"""
	Check GRN against PO Number

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	po_id = request_handler.getPostData('po_id')
	enterprise_id = request_handler.getPostData('enterprise_id')
	if not enterprise_id:
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id

	grn_query = """SELECT sum(a.rec_qty) FROM grn_material AS a JOIN grn AS b 
					ON a.enterprise_id=b.enterprise_id AND b.grn_no=a.grnNumber 
						AND a.po_no=%s AND b.status <> -1 AND a.enterprise_id='%s'""" % (po_id, enterprise_id)
	response = {}
	try:
		db_session = SQLASession()
		po_to_reject = db_session.query(PurchaseOrder).filter(PurchaseOrder.po_id == po_id).first()
		if len(po_to_reject.in_process_dcs) > 0:
			response['length'] = len(po_to_reject.in_process_dcs)
			response['type'] = 1
			response['message'] = """ "Sorry!",
									"Purchase Order cannot be Rejected, as items have been issued against this PO!",
									"warning" """
			response['custom_message'] = "Purchase Order cannot be Rejected, as items have been issued against this PO!"
		else:
			grn_counts = executeQuery(grn_query)
			counts = grn_counts
			response['length'] = counts[0][0]
			response['type'] = 0
			response['message'] = """ "Sorry!",
									"Purchase Order cannot be Rejected, as Items have been received against this PO!",
									"warning" """
			response[
				'custom_message'] = "Purchase Order cannot be Rejected, as Items have been received against this PO!"
		if response['length'] in (0, None):
			json_response = response_code.success()
		else:
			json_response = response_code.failure()
		json_response.update(response)
	except Exception as e:
		logger.exception(e)
		json_response = response_code.internalError()
		json_response['length'] = -1
	return HttpResponse(content=simplejson.dumps(json_response), mimetype='application/json')


def loadTaxDetails(request):
	"""
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	po_query = "SELECT code,name FROM tax where enterprise_id = '%s' order by name" % enterprise_id
	try:
		tax_data_dump = executeQuery(po_query)
	except Exception as e:
		logger.exception('Error : %s' % e)
		tax_data_dump = ""
	response = HttpResponse(content=simplejson.dumps(tax_data_dump), mimetype='application/json')
	return response


def loadTagDetails(request):
	"""
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	po_query = "SELECT id,tag FROM tags where enterprise_id = '%s'" % enterprise_id
	try:
		tag_data_dump = executeQuery(po_query)
	except Exception as e:
		logger.exception('Error : %s' % e)
		tag_data_dump = ""
	response = HttpResponse(content=simplejson.dumps(tag_data_dump), mimetype='application/json')
	return response


def loadGSTTaxDetails(request):
	"""
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	gst_type = request_handler.getPostData('gst_type')
	tax_query = "SELECT code, name, base_rate FROM tax where enterprise_id = '%s' and type='%s' " % (
		enterprise_id, gst_type)
	try:
		tax_data_dump = executeQuery(tax_query)
	except Exception as e:
		logger.exception('Error : %s' % e)
		tax_data_dump = ""
	response = HttpResponse(content=simplejson.dumps(tax_data_dump), mimetype='application/json')
	return response


def loadPOTotal(request):
	"""
	get total of po value till date since Book closure date
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	till = datetime.today()
	response = response_code.success()
	try:
		since = AccountService().accounts_dao.getNextBookOpeningDate(enterprise_id=enterprise_id)
		response['total'] = float(AccountService().getPurchaseExpenseTotal(enterprise_id=enterprise_id, since=since, till=till))
		response['since'] = since.strftime('%b %d,%Y')
	except Exception as e:
		logger.exception(e.message)
		response = response_code.internalError()
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def loadPOByDue(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	since = datetime.combine(datetime.now().date(), time())
	till = since
	db_session = SQLASession()
	db_session.begin(subtransactions=True)
	try:
		if request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.is_delivery_schedule:
			po_stock_ds = db_session.query(
				PurchaseOrderMaterialDeliverySchedules.quantity.label('qty'),
				func.sum((PurchaseOrderMaterial.rate - (PurchaseOrderMaterial.rate * (PurchaseOrderMaterial.discount/100))) * PurchaseOrderMaterialDeliverySchedules.quantity).label('total'),
				PurchaseOrder.financial_year, PurchaseOrder.type, PurchaseOrder.po_no, Party.name, PurchaseOrder.sub_number,
				PurchaseOrder.po_id, PurchaseOrder.status, PurchaseOrderMaterialDeliverySchedules.quantity, PurchaseOrderMaterial.rate).outerjoin(
				PurchaseOrderMaterial, and_(
					PurchaseOrderMaterial.po_id == PurchaseOrderMaterialDeliverySchedules.po_id,
					PurchaseOrderMaterial.item_id == PurchaseOrderMaterialDeliverySchedules.item_id,
					PurchaseOrderMaterial.make_id == PurchaseOrderMaterialDeliverySchedules.make_id,
					PurchaseOrderMaterial.enterprise_id == PurchaseOrderMaterialDeliverySchedules.enterprise_id)
			).outerjoin(
				PurchaseOrder, and_(
					PurchaseOrder.po_id == PurchaseOrderMaterial.po_id,
					PurchaseOrder.enterprise_id == PurchaseOrderMaterial.enterprise_id)
			).outerjoin(
				Party, and_(Party.id == PurchaseOrder.supplier_id)
			).filter(
				PurchaseOrderMaterialDeliverySchedules.due_date == since,
				PurchaseOrderMaterialDeliverySchedules.enterprise_id == enterprise_id,
				PurchaseOrder.status == '2', PurchaseOrder.type != 2
			).group_by(PurchaseOrder.po_id)
			response = po_stock_ds.all()
		else:
			response = db_session.query(
				PurchaseOrder.financial_year, PurchaseOrder.type, PurchaseOrder.po_no, PurchaseOrder.total,
				Party.name, PurchaseOrder.sub_number, PurchaseOrder.po_id, PurchaseOrder.status).join(
				Party, and_(Party.id == PurchaseOrder.supplier_id)).filter(
				PurchaseOrder.delivery >= since, PurchaseOrder.delivery <= till, PurchaseOrder.status == '2',
				PurchaseOrder.type != 2, PurchaseOrder.enterprise_id == enterprise_id).all()
		db_session.commit()
	except Exception as e:
		db_session.rollback()
		response = []
		logger.exception(e.message)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def loadRecentlyReceivedGRNByPO(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	grn_query = """SELECT     
				    IFNULL(materials.name,
				            IFNULL(materials.drawing_no,
				                    purchase_order_material.item_id)) AS materials_name,
				    IFNULL(materials.drawing_no, '') AS purchase_order_material_drawing_no,
				    IFNULL(um.unit_name, unit_master.unit_name) AS unit_master_unit_name,
				    IFNULL(purchase_order_material.pur_qty, 0) AS po_qty,
				    IFNULL(CONCAT(purchase_order.financial_year,
				                    IF(purchase_order.type = 1,
				                        '/JO/',
				                        '/PO/'),
				                    LPAD(purchase_order.orderno, 7, 0),
				                    IFNULL(purchase_order.sub_number, '')),
				            '') AS po_no,
				    IF(grn.receipt_no IS NOT NULL,
				        IF(grn.status > 0,
				            CONCAT(grn.financial_year,
				                    '/GRN/',
				                    LPAD(grn.receipt_no, 6, 0),
				                    IFNULL(grn.sub_number, '')),
				            grn.grn_no),
				        '-NA-') AS grn_no,
				    IF(grn.inward_date IS NOT NULL
				            AND grn.status IS NOT NULL,
				        grn.inward_date,
				        '') AS grn_grn_date,
				    IFNULL(party_master.party_name, '-NA-') AS party_master_party_name,
				    IFNULL(grn_material.acc_qty, '-NA-') AS acc_qty,
				    IFNULL(materials.makes_json, '') AS make_name
				FROM
				    ((SELECT 
				        enterprise_id,
				            grnNumber,
				            item_id,
				            alternate_unit_id,
				            rec_qty,
				            dc_qty,
				            acc_qty,
				            inv_rate,
				            discount,
				            po_no,
				            make_id,
				            dc_id,
				            is_faulty
				    FROM
				        grn_material)) AS grn_material
				        LEFT JOIN
				    (grn) ON grn.grn_no = grn_material.grnNumber
				        AND grn.status > 0
				        LEFT JOIN
				    (materials) ON materials.id = grn_material.item_id
				        AND materials.enterprise_id = grn_material.enterprise_id				        
				        LEFT JOIN
				    (party_master) ON party_master.party_id = grn.party_id
				        AND party_master.enterprise_id = grn_material.enterprise_id
				        LEFT JOIN
				    unit_master AS um ON um.unit_id = grn_material.alternate_unit_id
				        AND um.enterprise_id = grn_material.enterprise_id
				        LEFT JOIN
				    (unit_master) ON unit_master.unit_id = materials.unit
				        AND unit_master.enterprise_id = grn_material.enterprise_id
				        LEFT JOIN
				    (((SELECT 
				        pom.enterprise_id,
				            pom.pid,
				            pom.item_id,
				            pom.pur_qty,
				            pom.po_price,
				            pom.discount,
				            pom.make_id,
				            pom.is_faulty,
				            SUM(IF(g.status IS NULL, 0, gm.acc_qty)) AS gm_acc_qty,
				            '-NA-' AS unit_master_unit_name,
				            pom.alternate_unit_id
				    FROM
				        purchase_order_material pom
				    LEFT JOIN grn_material gm ON gm.po_no = pom.pid
				        AND gm.item_id = pom.item_id
				        AND gm.make_id = pom.make_id
				        AND gm.enterprise_id = pom.enterprise_id
				        AND gm.dc_id IS NULL
				    LEFT JOIN grn g ON g.status > - 1
				        AND g.grn_no = gm.grnNumber
				    GROUP BY pom.pid , pom.item_id , pom.make_id)) AS purchase_order_material) ON grn_material.po_no = purchase_order_material.pid
				        AND grn_material.item_id = purchase_order_material.item_id
				        AND grn_material.make_id = purchase_order_material.make_id
				        AND grn_material.enterprise_id = purchase_order_material.enterprise_id
				        AND grn_material.dc_id IS NULL
				        LEFT JOIN
				    purchase_order ON purchase_order.id = purchase_order_material.pid
				        AND purchase_order.enterprise_id = purchase_order_material.enterprise_id
				WHERE
				    grn.enterprise_id = %s AND grn.rec_against in ('Purchase Order','Job Work')
				GROUP BY grn.grn_no , grn_material.item_id , grn_material.make_id
				ORDER BY grn.inward_date DESC, grn.grn_no DESC
				LIMIT 10""" % enterprise_id
	try:
		grn_data_dump = executeQuery(grn_query, as_dict=True)
		logger.info("grn data:%s" % grn_data_dump)
		for material in grn_data_dump:
			material['make_name'] = helper.constructDifferentMakeName(material['make_name'])
	except Exception as e:
		logger.exception(e.message)
		grn_data_dump = ""
	return HttpResponse(content=simplejson.dumps(grn_data_dump), mimetype='application/json')


def loadRecentBadSuppliers(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	d_date = datetime.today() - relativedelta(months=6)
	start_date = datetime(d_date.year, d_date.month, 1, 0, 0, 0).strftime('%Y-%m-%d %H:%M:%S')
	end_date = datetime.today().strftime('%Y-%m-%d %H:%M:%S')
	logger.info("%s - %s" % (start_date, end_date))
	grn_query = """SELECT 
					    IF(grn.receipt_no IS NOT NULL,
					        IF(grn.status > 0,
					            CONCAT(grn.financial_year,
					                    '/GRN/',
					                    LPAD(grn.receipt_no, 6, 0)),
					            grn.grn_no),
					        '-NA-') AS grn_no,					    
					    IFNULL(party_master.party_name, '-NA-') AS party_name,
					    IFNULL(SUM(grn_material.dc_qty * grn_material.inv_rate), 0) AS purchase_order_total,
					    IFNULL(SUM(((grn_material.dc_qty - grn_material.acc_qty)) * grn_material.inv_rate), 0) AS rejection_value,
					    IFNULL((SUM(((grn_material.dc_qty - grn_material.acc_qty)) * grn_material.inv_rate)/SUM(grn_material.dc_qty * grn_material.inv_rate))*100, 0) AS rejection_percent
					FROM
					    (grn_material_tax_abstract AS grn_material)
					        RIGHT JOIN
					    (grn) ON grn.grn_no = grn_material.grn_no
					        AND grn.inv_type = 2
					        AND grn.enterprise_id = grn_material.enterprise_id
					        AND grn.status >= 1
					        AND grn.rec_against IN ('Purchase Order', 'Job Work')
					        LEFT JOIN
					    (party_master) ON party_master.party_id = grn.party_id
					        AND party_master.enterprise_id = grn.enterprise_id
				WHERE
				    (grn.enterprise_id = '%s'
				        AND grn.inward_date BETWEEN '%s' AND '%s')				
				GROUP BY  party_master.party_id 
				HAVING rejection_value > 0
				ORDER BY  rejection_percent desc""" % (enterprise_id, start_date, end_date)
	try:
		grn_data_dump = executeQuery(grn_query)
	except Exception as e:
		logger.exception(e.message)
		grn_data_dump = ""
	return HttpResponse(content=simplejson.dumps(grn_data_dump), mimetype='application/json')


def loadPurchasePerformance(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	val_title = []
	val_result = []
	val_title.append([])
	val_title[0].append("Year")
	val_title[0].append("Ontime")
	val_title[0].append("Delayed")

	try:
		response = response_code.success()
		query = GET_PURCHASE_PERFORMANCE_QUERY.format(enterprise_id=enterprise_id)
		result = executeQuery(query)
		for idx, item in enumerate(result):
			val_result.append([])
			val_result[idx].append(str(item[0]))
			val_result[idx].append(int(item[1]))
			val_result[idx].append(int(item[2]))
		val_title.extend(val_result)
		response['performance'] = val_title
	except Exception as e:
		logger.info(e)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def loadRecentlyOverDuePO(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	po_query = """
				SELECT 
				    IFNULL(CONCAT(IFNULL(po.financial_year, 'DRAFT#'),
				                    IF(po.type = 1, '/JO/', '/PO/'),
				                    LPAD(po.orderno, 7, 0),
				                    IFNULL(po.sub_number, '')),
				            '') AS po_code,
				    party_name AS supplier,
				    po.delivery AS po_due_on,
				    po.total AS po_value,
				    po.approved_on AS approved_on,
				    SUM(pom.adjacent_qty) - (SELECT 
				            IF(`g`.`grn_date` <= MAX(pom.adjacent_due_date),
				                    SUM(`gm`.`acc_qty`),
				                    0)
				        FROM
				            `grn_material` `gm`
				                LEFT JOIN
				            `grn` `g` ON ((`gm`.`grnNumber` = `g`.`grn_no`)
				                AND (`gm`.`enterprise_id` = `g`.`enterprise_id`)
				                AND (`g`.`status` > -(1)))
				        WHERE
				            ((`gm`.`enterprise_id` = `pom`.`enterprise_id`)
				                AND (`gm`.`item_id` = `pom`.`item_id`)
				                AND (`gm`.`make_id` = `pom`.`make_id`)
				                AND (`gm`.`po_no` = `pom`.`po_id`))) `adjacent_pending_qty`
				FROM
				    po_material_received_w_delivery_schedule_abstract pom
				        LEFT JOIN
				    purchase_order po ON po.id = pom.po_id
				        AND po.enterprise_id = pom.enterprise_id
				        INNER JOIN
				    party_master ON party_master.party_id = po.supplier_id
				        AND party_master.enterprise_id = po.enterprise_id
				WHERE
				    pom.enterprise_id = %s
				        AND po.status = 2 AND po.type != 2
				        AND po.approved_on IS NOT NULL
				GROUP BY pom.po_id
				HAVING IFNULL(adjacent_pending_qty > 0, FALSE)
				ORDER BY po.id DESC""" % enterprise_id
	po_report = []
	try:
		po_query_raw_data = executeQuery(po_query)
		for po in po_query_raw_data:
			if po[2] != '0000-00-00 00:00:00' or po[4] != '0000-00-00 00:00:00':
				po_item_list = {}
				po_item_list['po_code'] = po[0]
				po_item_list['supplier'] = po[1]
				po_item_list['po_due_on'] = datetime.strptime(
					str(po[2]) if po[2] is not None and po[2] != '0000-00-00 00:00:00' else str(po[4]),
					'%Y-%m-%d %H:%M:%S').strftime('%b %d,%Y')
				po_item_list['po_value'] = po[3]
				po_report.append(po_item_list)
	except Exception as e:
		logger.exception(e.message)
	return HttpResponse(content=(simplejson.dumps(po_report)), mimetype='application/json')


def loadJobPrice(request):
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		item_id = request_handler.getPostData('item_id')
		supplier_id = request_handler.getPostData('supplier_id')
		make_id = request_handler.getPostData('make_id')
		service_type = request_handler.getPostData('service_type')
		response = response_code.success()
		query_fetch_job_price = """ SELECT price,effect_since AS DATE,moq from recent_valid_price_profiles as smp
			WHERE
				smp.item_id = '{item_id}' AND smp.make_id = '{make_id}' AND smp.enterprise_id = '{enterprise_id}'
				AND smp.is_service = '{service_type}' AND smp.supp_id = '{supplier_id}'  ORDER BY effect_since DESC""".format(
			item_id=item_id, supplier_id=supplier_id, make_id=make_id, enterprise_id=enterprise_id,
			service_type=service_type)

		job_price = executeQuery(query_fetch_job_price)
		if job_price:
			response["job_price"] = job_price[0][0]
			response["moq"] = job_price[0][2]
		else:
			response["job_price"] = 0
			response["moq"] = 0
	except Exception as e:
		logger.info("Fetching DCs are Failed")
		logger.debug(e)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def loadPartyCurrency(request):
	try:
		request_handler = RequestHandler(request)
		party_id = request_handler.getPostData('party_id')
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		query_fetch_party_currency = "SELECT currency FROM party_master WHERE party_id='%s' AND enterprise_id='%s'" % (
			party_id, enterprise_id)
		party_currency = executeQuery(query_fetch_party_currency)
	except Exception as e:
		logger.exception("Fetching Party Currency are Failed: %s" % e.message)
		party_currency = ""
	return HttpResponse(content=simplejson.dumps(party_currency), mimetype='application/json')


def loadRemarks(request):
	"""
	Loads remarks for the PurchaseOrder in pdf page.

	:param request:
	:return: JSON dumps to the Ajax call that initiated to load the remarks
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getPostData('enterprise_id')
	if not enterprise_id:
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	po_id = request_handler.getPostData('po_id')
	po_need_to_load = PurchaseService().purchase_dao.getPurchaseOrder(enterprise_id=enterprise_id, po_id=po_id)
	try:
		response = response_code.success()
		if po_need_to_load.remarks:
			response['remarks'] = po_need_to_load.remarks
		else:
			response['remarks'] = []
		logger.exception("Remarks loaded successfully for Po_no: %s" % po_id)
		make_transient(po_need_to_load)
	except Exception as e:
		logger.exception("Loading remarks failed %s" % e.message)
		response = response_code.internalError()
		response['custom_message'] = '%s' % e.message
	return HttpResponse(simplejson.dumps(response), 'content-type=text/json')


def getPOLogList(request):
	response = {}
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	po_id = request_handler.getPostData('po_id')
	offset = request_handler.getPostData('offset')
	limit = request_handler.getPostData('limit')
	try:
		response = PurchaseChangelog().fetchLogList(
			po_id=po_id, enterprise_id=enterprise_id, offset=offset, limit=limit)
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getPOLogData(request):
	"""
	get changelog data from a specified voucher timeset
	:param request:
	:return:
	"""
	response = {}
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	po_id = request_handler.getPostData('po_id')
	modified_at = request_handler.getPostData('modified_at')
	try:
		response = PurchaseChangelog().fetchLogData(po_id=po_id, enterprise_id=enterprise_id, modified_at=modified_at)
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getDcMaterials(request):
	"""
	get dc materials data from po material table
	:param request:
	:return:
	"""
	response = {}
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	po_id = request_handler.getPostData('po_id')
	item_id = request_handler.getPostData('item_id')
	try:
		supplied_qty_query = """ SELECT sum(inv_mat.qty) from invoice_materials as inv_mat
					left join invoice inv on inv.id = inv_mat.invoice_id
					WHERE
						inv_mat.item_id = '{item_id}' AND inv.job_po_id = '{po_id}' 
						AND inv.enterprise_id = '{enterprise_id}' """.format(
			item_id=item_id, po_id=po_id, enterprise_id=enterprise_id)

		supplied_qty = executeQuery(supplied_qty_query)
		returned_qty_query = """ SELECT grn_mat.rec_qty from grn_material as grn_mat
					left join grn grn on grn.grn_no = grn_mat.grnNumber
					WHERE
						grn_mat.item_id = '{item_id}' AND grn_mat.po_no = '{po_id}' 
						AND grn.enterprise_id = '{enterprise_id}' and grn.rec_against = 'Job Work' """.format(
			item_id=item_id, po_id=po_id, enterprise_id=enterprise_id)
		returned_qty = executeQuery(returned_qty_query)
		if len(supplied_qty) > 0 and supplied_qty[0][0] != None:
			response["supplied_qty"] = supplied_qty[0][0]
		else:
			response["supplied_qty"] = 0
		if len(returned_qty) > 0 and returned_qty[0][0] != None:
			if returned_qty[0][0]:
				response["returned_qty"] = returned_qty[0][0]
		else:
			response["returned_qty"] = 0
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def saveReturnQty(request):
	"""
	get dc materials data from po material table
	:param request:
	:return:
	"""
	response = {}
	production_service = ProductionService()
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	user = request_handler.getSessionAttribute(USER_IN_SESSION_KEY)
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	jo_details = json.loads(request.POST.get('jo_detail'))
	location_id = request_handler.getData("location_id")
	try:
		production_log_details = production_service.saveProductionLog(
			production_log=jo_details, enterprise_id=enterprise_id, user=user, user_id=user_id, is_return=True,
			location_id=location_id)
		response = response_code.success()
		response['production_log_details'] = production_log_details
		if production_log_details:
			query_fetch_grn = """SELECT g.grn_no FROM grn g 
										left join grn_material gm on gm.grnNumber = g.grn_no 
										where gm.po_no = '{code}' order by grn_no desc limit 1   """.format(
										code=jo_details['pp_id'])
			grn_details = executeQuery(query_fetch_grn)
			if grn_details:
				grn_no = grn_details[0][0]
				response['grn_no'] = grn_no
	except Exception as e:
		logger.exception(e)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


@validate_payload(["enterprise_id", "gst_no"])
def get_gst_wise_pending_po(request):
	"""
	The API is used to get the GST wise PO's
	"""
	response = response_code.internalError()
	request_handler = RequestHandler(request)
	try:
		enterprise_id = request_handler.getData("enterprise_id")
		gst_no = request_handler.getData("gst_no")
		since, till = JsonUtil.getDateRange(rh=request_handler)
		pending_po_data = executeQuery(query=GET_PENDING_PO_QUERY.format(enterprise_id=enterprise_id, gst_no=gst_no,
																		 as_on=till), as_dict=True)
		# Initialize a dictionary to hold the transformed data
		transformed_data = defaultdict(lambda: {
			"party_currency": 0,
			"party_name": "",
			"party_id": 0,
			"pending_items": []
		})

		# Iterate through the input data
		for entry in pending_po_data:
			# Extract relevant fields from the entry
			party_id = entry["party_id"]
			party_name = entry["party_name"].strip()
			currency = entry["currency"]
			po_no = entry["po_no"]
			po_id = entry["po_id"]
			order_date = entry["order_date"]
			unit_price = float(entry["unit_price"])
			item_id = entry["item_id"]
			pending_qty = entry["pending_qty"]
			item_description = entry["item_name"]
			drawing_no = entry["drawing_no"]
			make_name = entry["make_name"]

			# Prepare the PO data
			po_data = {
				"po_no": po_no,
				"po_id": po_id,
				"order_date": order_date,
				"pending_qty": pending_qty,
				"unit_price": unit_price
			}

			# If the party is already in the transformed data, update it; otherwise, create a new entry
			transformed_data[party_id]["party_currency"] = currency
			transformed_data[party_id]["party_name"] = party_name
			transformed_data[party_id]["party_id"] = party_id

			# Find if the item already exists in the pending_items
			existing_item = next(
				(item for item in transformed_data[party_id]["pending_items"] if item["item_id"] == item_id), None
			)

			if existing_item:
				# If the item exists, add pendqty to pending_qty and append the new PO data
				existing_item["pending_qty"] += pending_qty
				existing_item["pos"].append(po_data)
			else:
				# Otherwise, create a new item entry
				new_item = {
					"item_id": item_id,
					"drawing_no": drawing_no,
					"make_name": make_name,
					"pending_qty": pending_qty,  # Initial pendqty for the new item
					"item_description": item_description,
					"pos": [po_data]
				}
				transformed_data[party_id]["pending_items"].append(new_item)

		# Convert the defaultdict back to a regular list of dictionaries
		output_data = list(transformed_data.values())
		response = response_code.success()
		response["data"] = output_data
	except Exception as e:
		logger.info("Failed to get the pending Po: %s" % e)
	return HttpResponse(content=json.dumps(response, cls=CustomJSONEncoder), mimetype='application/json',
						status=response['response_code'])

