{% extends "production/sidebar.html" %}
{% block productionPlan %}
{% if logged_in_user|canView:'MATERIAL REQUEST' %}
<link rel="stylesheet" href="/site_media/css/manufacturing_indent.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/load_material.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/add_material.js?v={{ current_version }}"></script>
<style>
	#id_mrs-instructions {
		height: 100px;
	}

	#cattable tr[data-toggle='open'] td:nth-child(8){
		opacity: 0.5;
		background: #ddd;
		pointer-events: none;
	}

	#cattable tr[data-toggle='open'] input,
	#cattable tr[data-toggle='open'] select	{
		opacity: 0.5;
		background: #ddd;
	}

	.side-content.div-disabled {
		padding: 0;
		background-color: transparent !important;
	}

	.side-content.div-disabled a,
	.side-content.div-disabled div,
	.side-content.div-disabled input {
		border: none;
		outline: none;
		box-shadow: 0 0;
		height: 30px;
		padding-left: 3px;
		background-color: transparent !important;
		font-size: 13px;
		color: #333;
	}

	.side-content.div-disabled input {
		padding-left: 6px;
	}

	.side-content.div-disabled .chosen-single div {
		display: none;
	}

	.side-content.div-disabled .chosen-single span {
		margin-top: 2px;
	}

	.table.text_box_in_table .chosen-single {
		height: 26px;
	    font-size: 13px;
	    line-height: 25px;
	    padding: 0 0 0 3px;
	}

	.table.text_box_in_table .chosen-container-single .chosen-single div b {
		margin-top: 2px;
	}

	.supplier_total_amt {
		font-size: 20px;
		float: right;
		margin-top: 15px;
	}

	.parent-supplier-container {
	    padding: 6px 12px;
	    border: solid 1px #ccc;
	    border-top: none;
	    margin-bottom: 15px;
	}

	.supplier_name_tab a{
		color:  #004195 !important;
		font-size: 16px;
	}

	.chosen-select[readonly='readonly']+div  {
		pointer-events: none;
	}

	.supplier_type_select {
		width:  100%;
		border-radius: 4px;
    	border-color: #ccc;
	}

	.disabled_material {
		text-decoration: line-through;
	}

	.disabled_material td {
		background: rgba(0,0,0,0.1);
	    opacity: 0.7;
	    pointer-events: none;
	}

	.disabled_material input {
		background: transparent;
	}

	.disabled_material td:last-child {
		background: transparent;
		opacity: 1;
		pointer-events: inherit;
		color: #000 !important;
	}

	.td_for_icon .fa.fa-ban,
	.td_for_icon .fa.fa-plus {
		font-size: 18px;
	    margin-top: 3px;
	}

	.side-content .custom-error-message {
		margin-top: -24px;
    	right: 26px;
	}

	.error-duplicate-supplier {
		background: rgba(249, 255, 81,0.35);
	}

	.error-duplicate-supplier .chosen-container {
		box-shadow: 0 0;
	}

	.error-duplicate-supplier input,
	.error-duplicate-supplier select,
	.error-duplicate-supplier a span{
		background: rgba(221, 75, 57,0.00);
	}

	.add_split_container .chosen-disabled {
		opacity: 1 !important;
	    box-shadow: 0 0;
	}

	.add_split_container .chosen-disabled a {
		border:  none;
	}

	.add_split_container .chosen-disabled b {
		display: none !important;
	}

	.add_split_container select[disabled] {
		-webkit-appearance: none;
	    -moz-appearance: none;
	    text-indent: 1px;
	    text-overflow: '';
	    background: transparent;
	}

	.loading-main-container {
		display: block;
	}

	.payment-terms-container {
		float: left;
	    width: 460px;
	    margin-left: 15px;
	    margin-top: 9px;
	}

	.payment-terms-container div {
		float: left;
	}

	.payment-terms-container div:nth-child(2) input {
		border-radius: 4px 0 0 4px !important;
	}

	.payment-terms-container div:last-child select{
		border-radius: 0 4px 4px 0 !important;
	}

	.payment-terms-container input {
		width: 90px;
	}

	.payment-terms-container select {
		width: 140px;
	}

	.table .payment-terms-container div input,
	.table .payment-terms-container div select {
		padding: 4px 12px 6px;
	    height: 34px;
	    font-size: 14px;
	    border-radius: 0;
	    margin-left: -1px;
	}

	.delivery_due_container {
		float: right;
	    margin-bottom: 5px;
	    margin-top: -5px;
	}

	.delivery_due_container input {
		padding: 10px;
	    height: 26px;
	    font-size: 12px;
	    padding-left: 45px;
	}

	.delivery_due_container i {
		font-size: 12px;
	    padding: 0px 12px;
	    height: 24px;
	    margin-top: -26px;
	    margin-left: 1px;
	}

	.delivery_due_container i:before {
		margin-top: 6px !important;
	}

	.tour_bucket{
		background:transparent;
	}
	#cattable tbody tr td .tree-view {
		position: relative;
	}

	#cattable tbody tr td .tree-view:before {
	    border-bottom: 1px solid #bbb;
	    content: "";
	    left: 6px;
	    position: absolute;
	    top: 6px;
	    width: 1.5em;
	}

	#cattable tbody tr td .tree-view:after {
	    border-left: 1px solid #bbb;
	    content: "";
	    left: 6px;
	    position: absolute;
	    top: -25px;
	    height: 4em;
	}

	#cattable .fa-plus:before,
	#cattable .fa-minus:before {
	    border: solid 1px #333;
	    padding: 2px 3px 2px 3px;
	    border-radius: 3px;
	    font-size: 10px;
	}
	.disabled {
		opacity: 0.5;
		pointer-events: none;
	}

</style>

<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/utility.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/make.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/tags.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/mrs.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/Component/projects.js?v={{ current_version }}"></script>


	<div class="right-content-container">
		<div id='loadingmessage_changelog_listing_ie' style="text-align:center;position: absolute;z-index: 1;top: 50%;left: 50%;display:none">
						<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' width="100">
						<br /> Please wait...
		</div>
		<div class="page-title-container">
			<span class="page-title">Material Request</span>
		</div>
		<div class="page-heading_new" style="padding: 0 30px;" id="mrs_id_header">
			<input type="hidden" value="{{ mrs_id }}"  id="mrs_id" name="mrs_no"/>
			<a role="button" href="/erp/stores/material_requsition_list/" class="btn btn-add-new pull-right view_mrs" data-tooltip="tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
			<span class="page_header_mrs">
				<span class="header_current_page"> {{ mrs_code }}</span>
			</span>
		</div>
		<div class="container-fluid" id="container-fluid">
			<div>
				<div class="col-lg-12">
					<div class="content_bg">
						<div class="tab-content">
							<div>
								<div class="row">
									<div class="col-sm-6">
										<table border="0" class="side-table table text_box_in_table">
											<tr>
												<td class="side-header" style="width: 190px;">MRS No. & Date</td>
												<td class="side-content" id="mrs_code">{{ mrs_code }} - {{ header_master.prepared_on|date:'M d, Y' }}</td>
											</tr>
											<tr>
												<td class="side-header" style="width: 190px;">Requisition For *</td>
												<td class="side-content ">
													<input type="text" class="form-control"
													value="{% if mrs_id %}{{ header_master.purpose }}{% endif %}" id="id_material_request_req_for"
													name="mrs-purpose" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" />
												</td>
											</tr>
											<tr>
												<td class="side-header" style="width: 190px;">Requisition By *</td>
												<td class="side-content ">
													<input id="requisitionBy" type="text" hidden="hidden" value="{{ header_master.issue_id }}"/>
													<select class="form-control chosen-select" name="invoice-issue_to" id="id_invoice-issue_to" >
														<span><option value="0" hidden="hidden">Select an option</option>
														</span>
														<optgroup label="Frequently used">
														  {% for j in issue_to.0 %}
														    {% if j.0|lower == invoice.issue_to.value|lower %}
														    <option value="{{ j.0|lower }}" selected="selected" value={{ j.0 }}>{{ j.0 }}</option>

														    {% else %}
														    <option value="{{ j.0|lower }}" value={{ j.0 }}>{{ j.0 }}</option>
														    {% endif %}
														  {% endfor %}
														</optgroup>
														<optgroup label="All">
															{% for j in issue_to.1 %}
																{% if j.0|lower == invoice.issue_to.value|lower %}
																<option value="{{ j.0|lower }}" selected="selected" value={{ j.0 }}>{{ j.0 }}</option>
																{% else %}
																<option value="{{ j.0|lower }}" value={{ j.0 }}>{{ j.0 }}</option>
																{% endif %}
															{% endfor %}
														</optgroup>
													</select>
												</td>
											</tr>
											<tr>
												<td class="side-header" style="width: 190px;">Project</td>
												<td class="side-content ">
													<div class="tour_project_tag">
														<div class="component_project" data-id="projectheader" data-name="select" data-isSuper={{logged_in_user.is_super}} data-value={{project_id}}></div>
														<label id="expRev" style="display: block;margin-top: 5px;">Revenue: <span id="revenue" style="margin-right: 10px;">0</span>Expense : <span id="expense" style="margin-right: 10px;">0</span> </label>
													</div>
<!--													<input id="projectTag" type="text" hidden="hidden" value="{{ header_master.project_id }}"/>-->
<!--													<select class="form-control chosen-select" name="invoice-project_choices" id="id_project_select" >-->
<!--													  <optgroup label="Frequently used">-->
<!--														 {% for j in project_choices.1.1 %}-->

<!--														    <option value="{{ j|lower }}" data-id={{ j.0 }}>{{ j.1 }}</option>-->

<!--														{% endfor %}-->
<!--													  </optgroup>-->
<!--													  <optgroup label="All">-->
<!--														{% for j in project_choices.2.1 %}-->

<!--														    <option value="{{ j|lower }}" data-id={{ j.0 }}>{{ j.1 }}</option>-->

<!--														{% endfor %}-->
<!--													  </optgroup>-->
<!--													</select>-->
												</td>
											</tr>
										</table>
									</div>
								</div>
								<div class="row">
									<div id="mrs_list_view">
										<div class="col-lg-12 search_result_table">
											<ul class="nav nav-tabs">
											  <li id="item_particulars" class="materials_add active"><a role="button">Item Particulars</a></li>
											  <li class="service_flag" style="margin-top: 12px"><span class='service-item-flag'></span> - Service</li>
											</ul>
											<div class="row item-particulars-add-container" id="materials_add" style="margin-bottom:0px !important;">
												<div class="col-sm-3 form-group">
													<label>Materials Requested<span class="mandatory_mark"> *</span></label>
													<input type="text" class="form-control" id="materialrequired" placeholder="Select Material" maxlength="100">
													<input type="hidden" value="" class="" id="material_id_hidden" placeholder="">
													<input type="hidden" value="" class="" id="id_ind_material-__prefix__-is_service" placeholder="" >
													<input type="hidden" value="" class="" id="material_id" placeholder="" >
													<input type="hidden" value="" class="" id="cat_code" placeholder="">
													<input type="hidden" value="" class="" id="id_material-material_id" placeholder="" >
													<input type="hidden" value="" class="" id="id_material-enterprise_id" placeholder="">
													<div class="duplicate_material_name"></div>
														<span class="material-removal-icon hide">
															<i class="fa fa-times"></i>
														</span>
												</div>
												<div class="col-sm-3">
													<label>Quantity<span class="mandatory_mark"> *</span></label>
													<input class="form-control" id="id_ind_material-__prefix__-quantity" maxlength="16" name="ind_material-__prefix__-quantity" onfocus="setNumberRangeOnFocus(this,12,3)" placeholder="Quantity" type="text">
													<div>
														<label id="ind_unit_display" class="unit_display pull-right">&nbsp;</label>
													</div>
													<div class="alternate_unit_select_box" id="alternate_unit_id">
													</div>
													<div class="hide">
														<select id="unit_id" name="selected_unit">
															{% for unit,id in all_units.items %}
																<option value="{{ id }}">{{ unit }}</option>
															{% endfor %}
														</select>
													</div>
												</div>

												<div class="col-sm-3">
													<div class="material_txt">
														<input type='button' id="add_new_material_req" class="btn btn-save btn-margin-1" value="+"/>
														<input type='button' id="cmdshow" class="btn btn-save btn-margin-1" value="BOM"/>
													</div>
												</div>
												<div class="clearfix"></div>
											</div>
										</div>
									</div>
								</div>

								<div class="row" style="margin-top:50px">
									<div class="table-responsive full_width_txt" id="mrs_materials">
										<div class="col-sm-12">
											<a role="button" id="a-export-mrs-material" class="btn btn-add-new pull-right export_ind_mat_details" style="margin-top: 18px;" onclick="GeneralExportTableToCSV.apply(this, [$('#MrsMaterial'), 'Material_request.csv']);" data-tooltip="tooltip" data-placement="bottom" title="Download MRS List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
											<a role="button" style="margin-right: 15px; margin-bottom: 5px;margin-top: 18px;" id="short_close" class="btn btn-save download_stock_report pull-right" data-tableid="tablesorter" onclick="short_close()">Short Close</a>
<!--											<input type="checkbox" class="btn pull-right" id="showIssueQty" onchange="issueQty_show()" style="margin-right: 25px;transform: scale(1.8);margin-top: 30px;"><label class="btn pull-right" style="margin-top:20px">Issue Qty</label>-->
											<div class=" col-sm-9 form-group checkbox chk-margin chk_qty_stock pull-right" style="margin-bottom: 2px;">
															<div class="col-sm-8"></div>
															<div class="col-sm-4">
																<div class="col-sm-6">
																	<input type="checkbox" class="chkcase" id="load_received_qty" onchange="showIssueQty()">
																	<label for="load_received_qty">Issue Qty </label>
																</div>
																<div class="col-sm-6">
																	<input type="checkbox" class="chkcase" id="load_stock_qty" onchange="showStockQty()">
																	<label for="load_stock_qty" class="">Stock In Hand</label>
																</div>
															</div>
														</div>
											<table class="table table-bordered table-striped custom-table text_box_in_table" id="MrsMaterial">
												<thead>
												<tr>
													<th style="width: 80px;">S No.</th>
													<th>Material</th>
													<th hidden="hidden" class="exclude_export">Make/Brand</th>
													<th style="width: 240px;">Quantity</th>
													<th class="table_received_qty hide" style="width: 240px;">Issue Quantity</th>
													<th style="width: 180px;" class="table_stock_in_hand hide">Stock In Hand</th>
													<th style="width: 180px;">Unit</th>
													<th style="width: 80px;" class="exclude_export">Delete</th>
												</tr>
												</thead>
												<tbody class="item-for-goods">
													{% for rec in materials %}
													   <tr>
														  <td class='text-center'> {{ forloop.counter }}</td>
														   {% if rec.7 %}
														  		<td>{{ rec.2}}<span class='service-item-flag'></span></td>
														   {% else %}
														   		<td>{{ rec.2}}</td>
														   {% endif %}
														  <td hidden='hidden' class="exclude_export">{{ rec.0}}</td>
														   <td hidden='hidden'>{{rec.1}}</td>
														  <td class='td_textbox_right exclude_export' ><input type='text' id="mrs_qty" class='form-control' onfocus='setNumberRangeOnFocus(this,12,3)' value={{rec.1}} onblur="validationWithIssueQty(this)"></td>
														   <td class="table_received_qty text-right hide" id='issue_qtyForVali'>{{ rec.5 }}</td>
														   <td class="table_stock_in_hand text-right hide">{{ rec.6 }}</td>
														  <td class='text-center'>{{ rec.4}}</td>
														  <td hidden='hidden' class="exclude_export">{{ rec.3}}</td>
														   {% if rec.5 > 0 %}
														  		<td class='text-center '><a href='#' title="The item can not be delete"><i class='fa fa-trash-o delete-row-btn disabled' ></i></a></td>
														   {% else %}
														   		<td class='text-center'><a href='#'><i class='fa fa-trash-o delete-row-btn'></i></a></td>
														   {% endif %}
													   </tr>
													{% endfor %}
												</tbody>
												<tbody class="item-for-service">

												</tbody>
											</table>
										</div>
									</div>
								</div>


								<div class="clearfix"></div>
								<div class="col-sm-3">
									<label>Remarks</label>
									<div class="remarks_count_link remarks_count disabled" onclick="remarks_show();">
										<span class="countContainer remarks_counter" id="remarks_counter">No</span><span> remarks</span>
										<p id="remarksLength" hidden="hidden">{{ header_master.remarks }}</p>
									</div>
									<textarea class="form-control" cols="40" id="id_mrs-instructions" maxlength="300" name="id_mrs-instructions" onkeypress="return validateStringOnKeyPress(this,event, &quot;alphaSpecialChar&quot;);" onblur="return validateStringOnBlur(this, event, &quot;alphaSpecialChar&quot;);" rows="5"></textarea>
								</div>

								<div class="material_txt col-sm-12 btn-margin text-right" style="margin-top: -34px;">
									{% if logged_in_user|canEdit:'MATERIAL REQUEST' %}
<!--									<input type="button" class="btn btn-save" id="approve_mrs" value="Approve" onclick="javascript:generate_pdf_ajax($('#mrs_id.value'),1)" />-->
									<input type="button" class="btn btn-save" id="save_mrs_button" value="Save"/>
									{% endif  %}
									<a href="{{list_link}}" class="btn btn-cancel">Back</a>
								</div>
							</div>
						</div>
					</div>
					<div class="row add_table" >
							<div id="catalogueModal" class="modal fade" role="dialog">
								<div class="modal-dialog modal-lg">
									<div class="modal-content">
										<div class="modal-header">
											<button type="button" class="close" data-dismiss="modal">&times;</button>
											<h4 class="modal-title">View BOM</h4>
										</div>
										<div class="modal-body">
											<table id="cattable" class="table table-bordered custom-table table-striped tableWithText">
												<thead>
													<tr align="center">
														<th>S.No</th>
														<th>Material</th>
														<th>Drawing No</th>
														<!-- <th>Make</th> -->
														<th>Qty</th>
														<th>Units</th>
														<th>Delete</th>
													</tr>
												</thead>
											</table>
										</div>
										<div id="catButton"></div>
									</div>
								</div>
							</div>
								<div class="clearfix"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
<div id="show_remarks_history_modal" class="modal fade"  role="dialog">
		<div class="modal-dialog modal-md">
		    <div class="modal-content">
	            <div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">&times;</button>
					<h4 class="modal-title">Remarks</h4>
				</div>
				<input type="hidden" id="remarks_list_json" value="{{remarks_list}}">
			    <div class="modal-body" id="remarks_list">

				</div>
				<div class="modal-footer" style="border-top: none;"></div>
		    </div>
		</div>
	</div>

<script>
	$(window).load(function(){
		actualProjectsBudget($('#projectheader').val(),$('#projectheader').find(':selected').attr('project-type'));
    	$('#projectheader').change(function() {
            actualProjectsBudget($(this).val(),$('#projectheader').find(':selected').attr('project-type'));
    	});
		$('.component_project label').hide();
		var oTable;
		var oSettings;
		var hash = window.location.hash;
		hash && $('ul.nav a[href="' + hash + '"]').tab('show');

		$('.nav-tabs a').click(function (e) {
			$(this).tab('show');
			var scrollmem = $('body').scrollTop() || $('html').scrollTop();
			window.location.hash = this.hash;
			$('html,body').scrollTop(scrollmem);
		});

	});

</script>
{% include "modal-window/add_issue_to.html" %}
{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
{% include "masters/add_material_modal.html" %}
{% endblock %}