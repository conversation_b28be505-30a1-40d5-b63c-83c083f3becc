"""
"""
from django.conf.urls import patterns, url

from erp.expenses import json_api
from erp.expenses.expense_views import viewExpenses, saveExpense

__author__ = 'saravanan'

urlpatterns = patterns(
	'',
	# HTML views
	url('home/$', viewExpenses),
	url('home/#tab2$', viewExpenses),
	url('save/$', saveExpense),

	# Json API
	url('json/head_ledgers/$', json_api.getHeadLedgers),
	url('json/expense_list/$', json_api.listExpenses),
	url('json/expense_group_list/$', json_api.listExpensesGroup),
	url('json/get_expense/$', json_api.getExpense),
	url('json/save_expense/$', json_api.saveExpense),
	url('json/super_edit_expense_code/', json_api.superEditExpenseCode),
	url('json/expense_account_linked_message/', json_api.getExpenseAccountsLinkedMessage)
)
