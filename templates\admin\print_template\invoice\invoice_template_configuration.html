{% extends 'admin/sidebar.html' %}
{% block invoice_template %}
<script type="text/javascript" src="/site_media/js/bootstrap-input-spinner.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/jquery.slimscroll.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/invoice_template_preview.css">
<link type="text/css" rel="stylesheet" href="/site_media/plugin/image_cropper/css/imgareaselect-default.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/plugin/image_cropper/css/jquery.awesome-cropper.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/plugin/ck_editor/js/ckeditor.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/plugin/ck_editor/js/ckeditor_other.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/plugin/image_cropper/js/jquery.imgareaselect.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/plugin/image_cropper/js/jquery.awesome-cropper.js?v={{ current_version }}"></script>
<style type="text/css">
	.div_tax_types label{
		width: 100%;
	}

	#id_ith-scan_code_option label{
		padding-right: 30px;
		cursor:  pointer;
		width: auto;
	}

	.tooltip {
		z-index: 100000 !important;
	}

	#id_ith-scan_code_option {
		padding:  0 15px;
	}

	.inv_template_editor select.form-control {
		padding: 0;
	}

	#id_ith-name_font {
		width: auto;
		margin-bottom: 4px;
	}

	.tax_rate_column.tr_second_row.hide {
		/*display: table-column !important;
		visibility: hidden;*/
	}

	.bill_to_text,
	.ship_to_text {
		width: 100%;
		display: block;
		text-decoration: underline;
    	margin-bottom: 4px;
    	padding: 2px;
	}

	.banner-image {
		width: 100px;
		height: 100px;
		border: dashed 1px #ccc;
		padding: 0;
		margin-right: 7px;
		margin-top: 8px;
	}

	.banner-image .uploader {
	    margin-top: 25px;
	    display: inline-block;
	    text-align: center;
	}

	.banner-container {
		padding: 0;
	}

	.banner-image.image-uploaded {
		border-style: solid;
		padding: 0;
	}

	.banner-image.image-uploaded:hover .uploaded-banner-image {
		opacity: 0.3;
	}

	.banner-image.image-uploaded:hover .uploader {
		display: inline-block !important;
	    position: absolute;
	    margin-left: 12px;
	    color: #000;
	}

	.banner-image .uploaded-banner-image {
		width: 100%;
		height: 100%;
		margin: 0;
		padding: 3px;
    	border-radius: 6px;
	}

	.uploaded-banner-image {
		width:100px;
		height: 100px;
		margin-top: 10px;
	}

	.remove-banner-image {
		position: absolute;
	    left: 90px;
	    top: -1px;
	    font-size: 13px;
	    color: red;
	    border: solid 1px red;
	    border-radius: 50px;
	    padding: 1px 2px 1px 3px;
	}

	.progress + .cropped_image {
		display: none;
	}

	.form-number_format{
		width: 285px !important;
	}

	.history-log-part {
		width: 100%;
	    margin: 0;
	    padding: 8px 15px;
	    cursor: pointer;
	    padding-left: 60px;
	}
	.history-log-part:last-child {
		border-bottom: none;
	}
	.history-log-part:hover {
		background: #e8f0fe;
	}
	.history-log-part:hover .history-log-date {
		font-weight: bold;
	}
	.history-log-date {
	   font-style: italic;
	}
	.history-log-username {
		font-size: 15px;
		padding-top: 3px;
		font-weight: bold;
	}

	.history-log-content {
		margin-top: 8px;
	}

	.history-log-content ul {
		padding: 0px 10px 0px 35px;
	}
	.history-log-content li {
		margin-bottom: 10px;
	}

	.history-log-content li:last-child {
		margin-bottom: 0;
	}

	.history-log-part.active .history-log-date,
	.history-log-part.active .history-log-username {
		color: #004195;
	}

	.history-log-part.active .history-log-date {
	    text-align: right;
		display: block;
	}

	.history-log-part .fa-chevron-down:before {
		content: "\f078";
		color: #999;
	}

	.history-log-part.active .fa-chevron-down:before {
		content: "\f077";
		color: #999;
	}

	#change_log_modal .modal-body {
		overflow: auto;
	}


	ul.timeline {
	    list-style-type: none;
	    position: relative;
		padding: 0px;
	}
	ul.timeline:before {
	    content: ' ';
	    background: #d4d9df;
	    display: inline-block;
	    position: absolute;
	    left: 29px;
	    width: 2px;
	    height: 94%;
	    z-index: 400;
        margin-top: 12px;
	}
	ul.timeline > li {

	}
	ul.timeline > li:before {
	    content: ' ';
	    background: white;
	    display: inline-block;
	    position: absolute;
	    border-radius: 50%;
	    border: 3px solid #209be1;
	    left: 23px;
	    width: 14px;
	    height: 14px;
	    z-index: 400;
	    margin-top: 4px;
	}
	ul.timeline > li:last-child:before {
		background: #209be1;
	}
	body {
		overflow-y: scroll;
	}

	#id_iti-data_separator {
		width: 100px;
		padding-left: 5px;
	}

	.table>tbody>tr.table_bottom>td {
		padding: 0 !important;
		line-height: 0 !important;
	}

.amount {
    margin-top: 40px;
    border-top: 1px solid;
    border-bottom: 1px solid;
    text-align: center;
    font-weight: bold;
}


.text-color{
    color:#3191ba;
    font-weight:bold;
    text-decoration: none;
}
.payment_schedule{
    font-size:11px;
}

.header-banner-container{
	margin-bottom: 25px;
}
</style>
<form id="template_add" method="post">{% csrf_token %}
	<div class="right-content-container">
		<div class="container" style="border: solid 1px #ccc; padding-right: 0">
			<h2 style="color: #113344; margin: 15px 0;">
				<span class="page_header_grn" style="margin-bottom: 15px;">
					Invoice Template
					<small class="last-modified-user-container hide">Last modified by <b id="last-modified-user" style="font-size: 12px;">{{modified_details.modifier.first_name}} {{modified_details.modifier.last_name}}</b> on <b>{{modified_details.last_modified_on}}</b></small>
				</span>
				{% if logged_in_user|canApprove:'SALES' %}
					<input type="button" class="btn btn-save pull-right" style="margin-left: 8px; margin-right: 15px;" value="Save" onclick='submitTemplateChanges("/erp/admin/invoice_template/save/")'/>
				{% else %}
					<input type="button" class="btn btn-save pull-right disabled" data-tooltip="tooltip" title="You do not have adequate permission to modify the Invoice Template" style="margin-left: 8px; margin-right: 15px; background: #999" value="Save" />
				{% endif %}
				{% if is_invoice_available %}
					<input type="button" class="btn btn-warning pull-right" id="pdf_preview" value="PDF Preview" onclick='submitTemplateChanges("/erp/admin/invoice_template/preview/")' />
				{% endif %}
			</h2>
			<div class="clearfix"></div>
			<div class="inv_template_container row" style="width: 100%;">
				<div class="inv_template_side_bar">
					<ul class="nav nav-stacked nav_invoice_template">
						<li data-field="select_appearence" data-tab="1" class="active">General</li>
						<li data-field="select_header" data-tab="2" class="tour_header">Header</li>
						<li data-field="select_table" data-tab="3">Item Table</li>
						<li data-field="select_total" data-tab="4">Totals</li>
						<li data-field="select_misc" data-tab="5">Misc</li>
					</ul>
				</div>
				<div class="inv_template_editor">
					{{config_res.id}}
					{{config_res.enterprise_id}}
					{{config_res.module}}
					{{config_res.template_id}}
				    {{general_res.config_id}}
				    {{general_res.page_size}}
				    {{general_res.alignment}}
					{{header_res.config_id}}
					{{item_res.config_id}}
					{{summary_res.config_id}}
					{{misc_res.config_id}}
					{{item_res.include_taxamount_hidden_field}}
					{{item_res.include_taxrate_hidden_field}}
					{{item_res.tax_type_hidden_field}}
					{{general_res.tab_retain}}
					<input type="hidden" id="saved_invoice_template" value="{{save_invoice_template}}">
					<div class="inv_template_edit select_appearence">
						<div class="form-horizontal">
						    <div class="form-group hide">
						        <label class="control-label col-sm-5">Paper Size</label>
						        <div class="col-sm-6">
						            <select class="form-control" id="template_paper_size" name="page_size">
									    <option>A3</option>
									    <option selected="selected">A4</option>
									    <option>A5</option>
									    <option>Letter</option>
								    </select>
						        </div>
						    </div>
						    <div class="form-group hide">
						        <label class="control-label col-sm-5">Layout</label>
						        <div class="col-sm-6">
						            <select class="form-control" id="template_layout" name="alignment">
									    <option>Portrait</option>
									    <option>Landscape</option>
								    </select>
						        </div>
						    </div>

						    <div class="form-group" style="margin-bottom: 0;">
						        <label class="control-label col-sm-12" style="color: #209be1; text-shadow: 0 0 #000;">Number Format</label>
						        <span class="preview_invoice_number preview_invoice_number_inline">{{formatted_invoice_number}}</span>
					            <div class="col-sm-12 enterprise_form_details" style="margin-top: 10px; padding-left: 30px;">
							        <div class="form-group" style="margin-bottom: 12px;">
										{{header_res.inv_number_format}}
								        <span class="custom-error-message hide">Invoice Code cannot be greater 30 Characters of length.</span>
										<i class="fa fa-info-circle" style="color: #666;float: right;margin-top: -20px;margin-right: 32px;font-size:12px;" aria-hidden="true" id="invoice_number_format_help"></i>
									</div>
						            <input type="hidden" id="invoice_number_validation_result" name="invoice_number_validation_result" value=''>
								</div>
								<div class="form-horizontal">
					        		<div class="col-sm-12 document-margin" id="tour_num-format-font">
							            <label class="spinner-label" style="width: 100px;">Font Size</label>
							               {{header_res.invoice_number_font_size}}
							            <span class="doc-margin-prefix"> px</span>
							        </div>
							    </div>
							</div>
							<div class="form-group form-medium-text div_tax_types" style="margin-bottom: -7px">
								<label class="control-label col-sm-12" style="color: #209be1; text-shadow: 0 0 #000; margin-bottom: 5px;">Scan Code</label>
								{{header_res.scan_code_option}}
							</div>
						    <label class="control-label" style="color: #209be1; text-shadow: 0 0 #000; margin-bottom: 5px;">Page Setup</label>
						    <div class="form-group">
						        <label class="control-label col-sm-5">Base Font</label>
						        <div class="col-sm-6">
                                    {{general_res.base_font}}
						        </div>
						    </div>

							<div class="form-group">
						        <label class="control-label col-sm-5">Page Margin</label>
						        <div class="col-sm-7 document-margin">
						            <div class="col-sm-12 remove-padding">
							            <label class="spinner-label">TOP</label>
							            {{general_res.margin_top}}
							            <span class="doc-margin-prefix"> mm</span>
							        </div>
							        <div class="col-sm-12 remove-padding">
							            <label class="spinner-label">LEFT</label>
								        {{general_res.margin_left}}
							            <span class="doc-margin-prefix"> mm</span>
							        </div>
							        <div class="col-sm-12 remove-padding">
							            <label class="spinner-label">BOTTOM</label>
								        {{general_res.margin_bottom}}
							            <span class="doc-margin-prefix"> mm</span>
							        </div>
							        <div class="col-sm-12 remove-padding">
							            <label class="spinner-label">RIGHT</label>
								        {{general_res.margin_right}}
							            <span class="doc-margin-prefix"> mm</span>
							        </div>
						        </div>
						    </div>
						    <div class="form-group">
						        <label class="control-label col-sm-5">Page Size</label>
						        <div class="col-sm-6" style="margin-top: 9px;">
                                    A4 (210mm x 297mm) <i class="fa fa-info-circle" style="color: #666;" aria-hidden="true" data-tooltip="tooltip" title="Other page size will be available in future release."></i>
						        </div>
						    </div>

							<div class="form-group">
								<label class="date-format-label" style="color: #209be1; text-shadow: 0 0 #000;margin-left: 15px;">Date Format</label>
				                <br />
								<div class="container" style="width:327px;border: 1px solid #ccc;">
					                <table id="dateFormatter" class="form-group" style="max-width: 383px; float: left;display: block;width:319px;padding-top: 10px;padding-left: 15px;">
					                    <tr>
					                        <td style="padding-right: 10px;">
												<label style="cursor: move;"><i style='font-size: 14px;' class="fa fa-bars" aria-hidden="true"></i> Date</label>
							                    <select class="form-control date-formatter date-formatter-date">
							                        <option value="" data-value="">-</option>
							                        <option value="%d" data-value="D">D</option>
							                    </select>
							                </td>
							                <td style="padding-right: 10px;">
							                    <label></label>
							                    <select class="form-control date-formatter date-formatter-seperator1">
								                    <option value=" " data-value=" "> </option>
								                    <option value="/" data-value="/">/</option>
								                    <option value="-" data-value="-">-</option>
								                    <option value="." data-value=".">.</option>
								                    <option value=", " data-value=", ">,</option>
								                </select>
								            </td>
							                <td style="padding-right: 10px;">
												<label style="cursor: move;"><i style='font-size: 14px;' class="fa fa-bars" aria-hidden="true"></i> Month</label>
							                    <select class="form-control date-formatter date-formatter-month">
							                        <option value="" data-value="">-</option>
							                        <option value="%m" data-value="MM">MM</option>
							                        <option value="%b" data-value="MMM">MMM</option>
							                        <option value="%B" data-value="MMMM">MMMM</option>
							                    </select>
							                </td>
							                <td style="padding-right: 10px;">
							                    <label></label>
							                    <select class="form-control date-formatter date-formatter-seperator2">
							                        <option value=" " data-value=" "> </option>
								                    <option value="/" data-value="/">/</option>
								                    <option value="-" data-value="-">-</option>
								                    <option value="." data-value=".">.</option>
								                    <option value=", " data-value=", ">,</option>
								                </select>
								            </td>
							                <td style="padding-right: 10px;">
					                            <label style="cursor: move;"><i style='font-size: 14px;' class="fa fa-bars" aria-hidden="true"></i> Year</label>
							                    <select class="form-control date-formatter date-formatter-year">
							                        <option value="" data-value="">-</option>
							                        <option value="%y" data-value="YY">YY</option>
							                        <option value="%Y" data-value="YYYY">YYYY</option>
							                    </select>
							                </td>
					                    </tr>
					                </table>

					                <table id="timeFormatter" style="margin-bottom: 10px;">
					                    <tr>
					                        <td style="padding-right:9px;">
							                    <label>Hour</label>
							                    <select class="form-control time-formatter time-formatter-hour" style="width: 60px;">
							                        <option value="" data-value="">-</option>
							                        <option value=" %H" data-value="HH">HH</option>
							                        <option value=" %I" data-value="hh">hh</option>
							                    </select>
							                </td>
							                <td style="padding-right:5px;">
							                    <label>Minutes</label>
							                    <select class="form-control time-formatter time-formatter-minute" style="width: 60px;">
							                        <option value="" data-value="">-</option>
							                        <option value=":%M" data-value="mm">mm</option>
							                    </select>
							                </td>
							                <td style="padding-left:7px;">
							                    <label>Seconds</label>
							                    <select class="form-control time-formatter time-formatter-second" style="width: 60px;";>
							                        <option value="" data-value="">-</option>
							                        <option value=":%S" data-value="ss">ss</option>
							                    </select>
							                </td>
					                    </tr>
					                </table>
									<span style="font-size: 10px;">* This date format will apply for all the dates in Invoice document.</span>
				                </div>
								{{general_res.inv_doc_datetime_format}}
							</div>
						</div>
					</div>

					<div class="inv_template_edit select_header hide">
						<div class="form-horizontal">
						    <div class="form-group form-group-small">
						        <label class="control-label col-sm-3" style="color: #209be1; text-shadow: 0 0 #000;">Company</label>
						        <div class="col-sm-12 checkbox">
							        {{header_res.include_logo}}
									<label for="id_ith-include_logo" style="float: left;">LOGO</label>
									<span class="slidecontainer" style="float: left; margin-left: 15px;">
										{{header_res.logo_size}}
							        </span>
							        <span id="logo_size_value" style="margin-left: 10px;"></span>
						        </div>
						    </div>
						    <div class="form-group form-group-small" style="border-bottom: solid 1px #eee;">
						        <div class=" checkbox">
							        {{header_res.include_name}}
							        <label for="id_ith-include_name">Name</label>
								</div>
								<div class="form-horizontal" >
							        <div class="col-sm-12"  style="margin-left: 40px;">
							            <label class="spinner-label" style="width: 100px; font-size: 10px;">FONT</label>
							            {{header_res.name_font}}
							            <div class="document-margin">
								            <label class="spinner-label" style="width: 100px; font-size: 10px;">Font Size</label>
								                {{header_res.name_font_size}}
								            <span class="doc-margin-prefix"> px</span>
								        </div>
								    </div>
								</div>
							</div>
							<div class="form-group form-group-small">
								<label class="control-label col-sm-12" style="color: #209be1; text-shadow: 0 0 #000;">Form Name</label
						        	>
					        	<div class="form-horizontal">
					        		<div class="col-sm-12 document-margin">
							            <label class="spinner-label" style="width: 100px;">Font Size</label>
							                {{header_res.form_name_font_size}}
							            <span class="doc-margin-prefix"> px</span>
							        </div>
							    </div>
							</div>
							<div class="form-group form-group-small form-medium-text" style="border-bottom: solid 1px #eee;">
					            <div class="col-sm-12 enterprise_form_details" style="margin-top: 10px; padding-left: 30px;">
							        <div class="form-group" style="margin-bottom: 12px;">
										<label>GST</label>
								        {{header_res.gst_label}}
									</div>
							        <div class="form-group" style="margin-bottom: 12px;">
										<label>Service</label>
								        {{header_res.service_label}}
									</div>
									<div class="form-group" style="margin-bottom: 12px;">
										<label>Trading</label>
										{{header_res.trading_label}}
									</div>
									<div class="form-group" style="margin-bottom: 12px;">
										<label>Bill of Supply</label>
										{{header_res.billofsupply_label}}
									</div>
									<div class="form-group" style="margin-bottom: 12px;">
										<label>Excise</label>
										{{header_res.others_label}}
									</div>
						            <div class="form-group" style="margin-bottom: 12px">
										<label for="id_itm-dc_label">Delivery Challan</label>
										{{header_res.dc_label}}
									</div>
						        </div>
						    </div>
						    <div class="form-group form-group-small">
						    	<div class="form-horizontal">
					        		<div class="col-sm-12 document-margin">
							            <label class="spinner-label" style="width: 100px;">Font Size</label>
							                {{header_res.font_size}}
							            <span class="doc-margin-prefix"> px</span>
							        </div>
							    </div>
						        <div class="col-sm-12 checkbox enterprise_header_details">
						            <div class="col-sm-12 remove-padding checkbox">
							            <div class="col-sm-6 remove-padding">
							            	{{header_res.include_address}}
											<label for="id_ith-include_address">Address</label>
										</div>
										<div class="col-sm-6 remove-padding">
								            {{header_res.include_phone_no}}
											<label for="id_ith-include_phone_no">Phone</label>
										</div>
										<div class="col-sm-6 remove-padding">
								            {{header_res.include_email}}
											<label for="id_ith-include_email">Email</label>
										</div>
										<div class="col-sm-6 remove-padding">
								            {{header_res.include_fax}}
											<label for="id_ith-include_fax">Fax</label>
										</div>
						            </div>
						            <div class="col-sm-12 remove-padding" style="margin-left: -34px; margin-bottom: 7px; letter-spacing: 0.8px;">
						            	<span class="control-label col-sm-12" style="color: #209be1; text-shadow: 0 0 #000;">REGISTRATION DETAILS</span>

						            </div>
						            <div class="col-sm-12 remove-padding checkbox registration_details">
							            {{header_res.included_reg_items}}
							            {% for item in enterprise_reg %}
								            <div class="col-sm-6 remove-padding">
								                <input onchange="updateRegistrationDetails()" checkboxfor="pdf_registration_{{ item.label_id }}" data-field-id="{{ item.label_id }}" class="required_checkbox" id="id_ith-include_in_invoice_template_{{ item.label_id }}" name="ith-include_in_invoice_template" type="checkbox"
								                       {% for i in included_reg_items %} {% if item.label == i.label %} checked="checked" {% endif %} {% endfor %}>
								                <label for="id_ith-include_in_invoice_template_{{ item.label_id }}" style="width: 125px;">{{ item.label }}</label>
								            </div>
										{% endfor %}
						            </div>
								</div>
							</div>

						    <div class="form-group form-group-small form-medium-text">
						        <label class="control-label col-sm-3" style="color: #209be1; text-shadow: 0 0 #000;">Party</label>
					            <div class="col-sm-12 enterprise_details">
							        <div class="checkbox form-group">
								        {{header_res.include_billingaddress}}
										<label for="id_ith-include_billingaddress" style="width: 148px;">Billing Address</label>
								        {{header_res.billingaddress_label}}
									</div>
									<div class="checkbox form-group checkbox" style="margin-left: 15px;">
										{{header_res.include_partycode}}
										<label for="id_ith-include_partycode" style="width: 118px;">Code</label>
										{{header_res.include_partyname}}
										<label for="id_ith-include_partyname" style="width: 118px;">Name</label>
										{{header_res.include_party_phoneno}}
										<label for="id_ith-include_party_phoneno" style="width: 118px;">Phone</label>
										{{header_res.include_party_email}}
										<label for="id_ith-include_party_email" style="width: 118px;">Email</label>
									</div>
							        <div class="checkbox form-group">
								        {{header_res.include_shippingaddress}}
										<label for="id_ith-include_shippingaddress" style="width: 148px;">Shipping Address</label>
								        {{header_res.shippingaddress_label}}
									</div>
						        </div>
						    </div>

						    <div class="form-group form-medium-text" style="margin-bottom: 0;">
						        <label class="control-label col-sm-12" style="color: #209be1; text-shadow: 0 0 #000;">Field Name</label>
					            <div class="col-sm-12 enterprise_form_details" style="margin-top: 10px; padding-left: 30px;">
							        <div class="form-group" style="margin-bottom: 12px;">
										<label>Invoice No</label>
								        {{header_res.invoiceno_label}}
									</div>
									<div class="form-group" style="margin-bottom: 12px;">
										<label for="id_itm-dcno_label">DC No <i class="fa fa-info-circle" style="color: #666;" aria-hidden="true" data-tooltip="tooltip" title="Invoice No label will be replaced with this one, in case of a DC."></i></label>
										{{header_res.dcno_label}}
									</div>
							        <div class="form-group" style="margin-bottom: 5px;">
										<label>Issue Date</label>
								        {{header_res.invoicedate_label}}
									</div>
								</div>
							</div>

							<div class="form-group form-group-small form-medium-text">
						        <label class="control-label col-sm-12"></label>
					            <div class="col-sm-12 enterprise_details enterprise_details_packing_details">
					               <div class="tour-po_no">
					                <div class="checkbox form-group checkbox">
						                {{header_res.include_pono}}
										<label for="id_ith-include_pono">PO No</label>
						                {{header_res.pono_label}}
									</div>
									<div class="checkbox form-group checkbox">
						                {{header_res.include_podate}}
										<label for="id_ith-include_podate">PO Date</label>
						                {{header_res.podate_label}}
									</div>
						               </div>
						            <div class="checkbox form-group checkbox">
						                {{header_res.include_estimate_nodate}}
										<label for="id_ith-include_estimate_nodate">Estimate No. & Date</label>
						                {{header_res.estimate_nodate_label}}
									</div>
					                <div class="checkbox form-group checkbox">
						                {{header_res.include_transportmode}}
										<label for="id_ith-include_transportmode">Transport Mode</label>
						                {{header_res.transportmode_label}}
									</div>
									<div class="checkbox form-group checkbox">
										{{header_res.include_lrnodate}}
										<label for="id_ith-include_lrnodate">LR No & Date</label>
										{{header_res.lrnodate_label}}
									</div>
									<div class="checkbox form-group checkbox">
										{{header_res.include_roadpermitno}}
										<label for="id_ith-include_roadpermitno">Road Permit No</label>
										{{header_res.roadpermitno_label}}
									</div>
									<div class="checkbox form-group checkbox">
										{{header_res.include_packingslipno}}
										<label for="id_ith-include_packingslipno">Packing Slip No</label>
										{{header_res.packingslipno_label}}
									</div>
									<div class="checkbox form-group checkbox">
										{{header_res.include_packingdescription}}
										<label for="id_ith-include_packingdescription">Packing Description</label>
										{{header_res.packingdescription_label}}
									</div>
									<div class="checkbox form-group checkbox">
										{{header_res.include_paymentterms}}
										<label for="id_ith-include_paymentterms">Payment Terms</label>
										{{header_res.paymentterms_label}}
									</div>
									<div class="checkbox form-group checkbox">
										{{header_res.include_splinstructions}}
										<label for="id_ith-include_splinstructions">Special Instruction</label>
										{{header_res.splinstructions_label}}
									</div>
									<div class="checkbox form-group checkbox">
										{{header_res.include_preparedsign}}
										<label for="id_ith-include_preparedsign">Prepared By</label>
										{{header_res.preparedsign_label}}
									</div>
									<div class="checkbox form-group checkbox">
										{{header_res.include_authorizedsign}}
										<label for="id_ith-include_authorizedsign">Authorised Sign</label>
										{{header_res.authorizedsign_label}}
									</div>
									<div class="checkbox form-group include_aut-sign">
									{{header_res.include_approver_signature}}
										<label for="id_ith-include_approver_signature" style="width: 100%;">Include Approver Signature</label>
									</div>
						        </div>
						    </div>
					    </div>
				</div>

				<div class="inv_template_edit select_table hide">
					<div class="form-horizontal">
						<div class="col-sm-12 document-margin" style="padding: 0;">
					         <label class="spinner-label" style="width: 100px;">Font Size</label>
					                {{item_res.font_size}}
					            <span class="doc-margin-prefix"> px</span>
					    </div>
					    <div class="col-sm-12 enterprise_details" style="padding: 0;">
					        <div style="display: inline-block;">
					              <label>Column</label>
					              <label>Label</label>
					               <label style="width: 60px; text-align: center;">Width%</label>
					        </div>
							<div class="checkbox form-group input_width_spinner">
								{{item_res.include_sno}}
								<label for="id_iti-include_sno">S. No</label>
								{{item_res.sno_label}}
								{{item_res.sno_width}}
							</div>
							<div class="input_width_spinner form-group" style="margin-left: 20px; margin-bottom: -6px;">
								<label style="padding-left: 6px;">Item Details</label>
									{{item_res.itemdetails_label}}
									{{item_res.itemdetails_width}}
									{{item_res.itemdetails_width_hidden_field}}
								</div>
						        <div class="form-group input_width_spinner input_sub_item" style="margin-top: 6px; margin-bottom: 5px;">
									<label style="padding-left: 5px;">Name</label>
									{{item_res.name_label}}
								</div>
								<div class="checkbox form-group input_width_spinner input_sub_item">
									{{item_res.include_itemcode}}
									<label for="id_iti-include_itemcode">Item Code</label>
									{{item_res.itemcode_label}}
								</div>
								<div class="checkbox form-group input_width_spinner input_sub_item" style="display:none">
									{{item_res.include_make }}
									<label for="id_iti-include_make">Make</label>
									{{item_res.make_label}}
							</div>
							<div class="checkbox form-group input_width_spinner input_sub_item" style="display:none">
									{{item_res.include_partno}}
									<label for="id_iti-include_partno">Part No.</label>
									{{item_res.partno_label}}
							</div>
							<div class="checkbox form-group input_width_spinner input_sub_item">
									{{item_res.include_oano}}
									<label for="id_iti-include_oano">OA No.</label>
									{{item_res.oano_label}}
							</div>
						    <div class="checkbox form-group input_width_spinner input_sub_item">
									{{item_res.include_dc_no}}
									<label for="id_iti-include_dc_no">DC No.</label>
									{{item_res.dc_no_label}}
							</div>
						    <div class="form-group input_sub_item">
			                    <label style="width: 77px; margin-top:8px;">Separator</label>
							    {{item_res.data_separator}}
				            </div>
						    <div class="checkbox form-group input_width_spinner input_sub_item">
									{{item_res.include_dc_qty}}
									<label for="id_iti-include_dc_qty" style="margin-left: 100px; width: calc(100% - 100px);">Display DC Qty</label>
							</div>
						    <div class="checkbox form-group input_width_spinner input_sub_item">
									{{item_res.include_dc_date}}
									<label for="id_iti-include_dc_date" style="margin-left: 100px; width: calc(100% - 100px);">Display DC Date</label>
							</div>
								<div class="checkbox form-group input_width_spinner input_sub_item">
									{{item_res.include_description}}
									<label for="id_iti-include_description">Description</label>
									{{item_res.description_label}}
								</div>
								<div class="checkbox form-group input_width_spinner input_sub_item">
									{{item_res.include_remarks}}
									<label for="id_iti-include_remarks">Remarks</label>
									{{item_res.remarks_label}}
								</div>
								<div class="checkbox form-group input_width_spinner">
									{{item_res.include_hsnsac}}
									<label for="id_iti-include_hsnsac">HSN/SAC</label>
									{{item_res.hsnsac_label}}
									{{item_res.hsnsac_width}}
									{{item_res.hsnsac_part_of_itemdetails}}
									<label for="id_iti-hsnsac_part_of_itemdetails" style="margin-left: 125px; width: calc(100% - 125px);">Part of Item Details</label>
								</div>
								<div class="checkbox form-group input_width_spinner">
									{{item_res.include_quantity}}
									<label for="id_iti-include_quantity">Quantity</label>
									{{item_res.quantity_label}}
									{{item_res.quantity_width}}
								</div>
								<div class="checkbox form-group input_width_spinner">
									{{item_res.include_units }}
									<label for="id_iti-include_units">Units</label>
									{{item_res.units_label}}
									{{item_res.units_width}}
									{{item_res.units_in_quantity_column}}
									<label for="id_iti-units_in_quantity_column" style="margin-left: 125px; width: calc(100% - 125px);">In Qty Column</label>
									{{item_res.include_primary_qty}}
									<label for="id_iti-include_primary_qty" style="margin-left: 125px; width: calc(100% - 125px);">Display Primary Qty</label>
								</div>
								<div class="checkbox form-group input_width_spinner">
									{{item_res.include_unit_price}}
									<label for="id_iti-include_unit_price">Unit Price</label>
									{{item_res.unit_price_label}}
									{{item_res.unit_price_width}}
								</div>
								<div class="checkbox form-group input_width_spinner">
									{{item_res.include_discount}}
									<label for="id_iti-include_discount">Discount</label>
									{{item_res.discount_label}}
									{{item_res.discount_width}}
								</div>
								<div class="checkbox form-group input_width_spinner">
									{{item_res.include_taxable_amount}}
									<label for="id_iti-include_taxable_amount">Taxable Amount</label>
									{{item_res.taxable_amount_label}}
									{{item_res.taxable_amount_width}}
								</div>
								<div class="checkbox form-group input_width_spinner">
									{{item_res.include_tax}}
									<label for="id_iti-include_tax">Tax</label>
								</div>
								<div class="form-group input_width_spinner div_tax_types">
									{{item_res.tax_type}}
								</div>
								<div class="checkbox form-group input_width_spinner input_sub_item">
									{{item_res.include_taxrate}}
									<label for="id_iti-include_taxrate" style="width: 177px;">Tax Rate</label>
									{{item_res.taxrate_width}}
								</div>
								<div class="checkbox form-group input_width_spinner input_sub_item">
									{{item_res.include_taxamount}}
									<label for="id_iti-include_taxamount" style="width: 177px;">Tax Amount</label>
									{{item_res.taxamount_width}}
								</div>
								<div class="checkbox form-group input_sub_item">
									{{item_res.show_tax_for_dc}}
									<label for="id_iti-show_tax_for_dc" style="width: 112px;">Show Tax for DC</label>
									<i class="fa fa-info-circle" style="color: #666; margin-top: 0px; display: table;" aria-hidden="true" data-tooltip="tooltip" title="As this template will be applicable to DC, this tax display flag is especially applicable to DC independent of Invoice Config."></i>
								</div>
						        <div>
									<label style="width: 310px;" >Tax applicable on Reverse Charges</label>
								</div>
						        <div class="form-group input_width_spinner div_tax_types">
									{{item_res.tax_payable_on_reverse_charge}}
								</div>
						        <div>
									<label style="margin: 15px 0 8px;" >Item Sort Order</label>
								</div>
						        <div class="form-group input_width_spinner div_tax_types">
									{{item_res.item_sort_order}}
								</div>
								<div class="checkbox form-group input_width_spinner">
									{{item_res.include_row_separator}}
									<label for="id_iti-include_row_separator">Row Separator</label>
								</div>
								<div class="checkbox form-group input_width_spinner">
									{{item_res.include_column_separator}}
									<label for="id_iti-include_column_separator">Column Separator</label>
								</div>
								<div class="checkbox form-group input_width_spinner">
									{{item_res.include_alternate_row_shading}}
									<label for="id_iti-include_alternate_row_shading" style="width: 177px;">Alternative Row Shading</label>
								</div>
							    <div class="col-sm-12 document-margin form-group input_width_spinner" style="margin-top:10px;">
								    <label for="id_iti-spacer">Spacer</label>
								    {{item_res.item_table_space}}
							    </div>
					        </div>
					    </div>
					</div>

					<div class="inv_template_edit select_total hide">
						<div class="form-horizontal">
						    <div class="col-sm-12 document-margin remove-padding">
					            <label class="spinner-label" style="width: 100px;">Font Size</label>
							    {{summary_res.font_size}}
					            <span class="doc-margin-prefix"> px</span>
					        </div>
					        <div class="col-sm-12 enterprise_total_details remove-padding">
								<div class="checkbox form-group">
									{{summary_res.include_total}}
									<label for="id_its-include_total">Show	Total Section</label>
								</div>
								<div class="checkbox form-group" style="margin-left: 15px;">
									{{summary_res.include_subtotal}}
									<label for="id_its-include_subtotal">Sub Total</label>
								</div>
								<div class="checkbox form-group" style="margin-left: 15px;">
									{{summary_res.include_qty_total}}
									<label for="id_its-include_qty_total">Quantity Total</label>
								</div>
								<div class="checkbox form-group">
									{{summary_res.include_total_in_words}}
									<label for="id_its-include_total_in_words">Total Value in Words</label>
								</div>
								<hr style="margin: 10px -13px 20px; border-color: #ccc;" />
								<div class="document-margin">
						            <label class="spinner-label" style="width: 100px; font-size: 0.9em;">Font Size</label>
								    {{summary_res.hsn_tax_font_size}}
						            <span class="doc-margin-prefix"> px</span>
						        </div>
								<div class="checkbox form-group">
									{{summary_res.include_hsn_summary}}
									<label for="id_its-include_hsn_summary">HSN Summary</label>
								</div>
						    </div>
					    </div>
					</div>

					<div class="inv_template_edit select_misc hide">
						<div class="form-horizontal">
					        <div class="checkbox form-group">
						        {{misc_res.include_page_no_in_footer}}
								<label for="id_itm-include_page_no_in_footer">Page Number</label>
							</div>
							<div class="form-group input_width_spinner hide" style="margin-left: 24px;">
								{{misc_res.footer_page_no_alignment}}
							</div>
							<div class="form-group" style="padding: 0 15px; margin-top: 15px;">
								<label>Notes</label>
								<div style="float: right; margin-top: -1px; background: #FFF; margin-right: -8px; color: #004195; cursor: pointer;" onclick="openNotesOverlay();">
									<i class="fa fa-pencil super_edit_in_field" style="margin-top: -3px; margin-right: 15px;"></i>
								</div>
								{{misc_res.notes}}
							</div>
							<label>Multi Page Settings <i class="fa fa-info-circle" style="color: #666;" aria-hidden="true" data-tooltip="tooltip" title="Can be viewed in PDF Preview"></i></label>
							<div class="checkbox form-group">
								{{misc_res.include_first_page_summary}}
								<label for="id_itm-include_first_page_summary">Summary in First Page</label>
							</div>
							<hr style="margin: 10px -13px 20px; border-color: #ccc;" />
							<div class="document-margin" style="margin-top: 15px;">
					            <label class="spinner-label" style="width: 100px; padding-top: 5px;">Font Size</label>
							    {{misc_res.font_size}}
					            <span class="doc-margin-prefix"> px</span>
					        </div>
							<div class="form-group" style="padding: 0 15px;">
								<label>Foot Note</label>
								{{misc_res.foot_note}}
							</div>
							<hr style="margin: 10px -13px 20px; border-color: #ccc;" />

							{{ header_banner_formset.management_form }}
							{{ footer_banner_formset.management_form }}
							<div class="document-margin" style="margin-top: 15px;margin-bottom: 15px;">
							 	<label class="spinner-label" style="color: #209be1; text-shadow: 0 0 #000;width: 100%; padding-top: 5px;margin-bottom: 10px;">Banner Images</label>
								<label class="spinner-label" style="width:35%;margin-top:-7px;">Header</label>
								<label  style="float: left;margin-top: 2px;">Size</label>
								{{ banner_res.header_size }}
								<span class="slidecontainer" style="float: left; margin-left: 11px;margin-top:3px;margin-bottom: 11px;width: 43%;">
									<input class="slider" id="header_banner_size" max="100" min="0" name="header_size" step="1" type="range" value="{{ header_image_size }}">
						        </span>
								<span id="header_banner_value" style="margin-left: 10px;">{{ header_image_size }}</span>
								{% for header_banner_form in header_banner_formset.initial_forms %}
                                    <div class="col-sm-4 banner-container" role="button">
                                        <div class="btn banner-image banner-header">
                                            <span class="uploader">
                                                <i class="fa fa-upload" aria-hidden="true"></i>
                                                <br/>{{ header_banner_form.position.value }} Image
                                            </span>
                                            {{ header_banner_form.config_id }}
                                            {{ header_banner_form.section }}
                                            {{ header_banner_form.position }}
                                            {{ header_banner_form.size }}
                                            {{ header_banner_form.attachment_id }}
                                            {{ header_banner_form.uploaded_banner_image }}
                                            {{ header_banner_form.is_removed }}
											{{ header_banner_form.banner_image }}
                                            <img class="uploaded-banner-image hide" src="{{ header_banner_form.banner_image.value }}">
                                        </div>
                                        <i class="fa fa-close remove-banner-image hide" aria-hidden="true"></i>
                                        <input class="header-banner" id="image_uploader-{{ header_banner_form.position.value|lower }}" type="hidden">
                                    </div>
								{% endfor %}
							</div>
							<div class="clearfix"></div>
							<div style="margin-top: 30px;">
								<label class="spinner-label" style="width:35%; margin-top:-9px;">Footer</label>
								<label style="float: left;margin-top: 2px;">Size</label>
								{{ banner_res.footer_size }}
								<span class="slidecontainer" style="float: left; margin-left: 11px;margin-top: 3px;margin-bottom:11px;width: 43%;">
									<input class="slider" id="footer_banner_size" max="100" min="0" name="footer_size" step="1" type="range" value="{{ footer_image_size }}">
								</span>
								<span id="footer_banner_value" style="margin-left: 10px;">{{ footer_image_size }}</span>
								{% for footer_banner_form in footer_banner_formset.initial_forms %}
                                    <div class="col-sm-4 banner-container" role="button">
                                        <div class="btn banner-image banner-footer">
                                            <span class="uploader">
                                                <i class="fa fa-upload" aria-hidden="true"></i>
                                                <br/>{{ footer_banner_form.position.value }} Image
                                            </span>
                                            {{ footer_banner_form.config_id }}
                                            {{ footer_banner_form.section }}
                                            {{ footer_banner_form.position }}
                                            {{ footer_banner_form.size }}
                                            {{ footer_banner_form.attachment_id }}
                                            {{ footer_banner_form.uploaded_banner_image }}
                                            {{ footer_banner_form.is_removed }}
											{{ footer_banner_form.banner_image }}
                                            <img class="uploaded-banner-image hide" src="{{ footer_banner_form.banner_image.value }}">
                                        </div>
                                        <i class="fa fa-close remove-banner-image hide" aria-hidden="true"></i>
                                        <input class="header-banner" id="footer_uploader-{{ footer_banner_form.position.value|lower }}" type="hidden">
                                    </div>
								{% endfor %}
							</div>
					    </div>
					</div>
				</div>
				<div class="invoice_template_default hide" style="float: right; margin-right: -3px; margin-top: -56px;">
					{% include "admin/print_template/invoice/preview/invoice_template_compact_preview.html" %}
				</div>
				<div class="invoice_template_1 hide" style="float: right; margin-right: -3px; margin-top: -56px;">
					{% include "admin/print_template/invoice/preview/invoice_template_simple_preview.html" %}
				</div>
				<div class="invoice_template_2 hide" style="float: right; margin-right: -3px; margin-top: -56px;">
					{% include "admin/print_template/invoice/preview/invoice_template_elegant_preview.html" %}
				</div>
			</div>
		</div>
	</div>

	<div id="template_chooser" class="modal fade" role="dialog">
	    <div class="modal-dialog" style="width: 80%; max-width: 1100px;">
	        <div class="modal-content" style="min-height: 650px;">
	            <div class="modal-header">
	                <button type="button" class="close" data-dismiss="modal">&times;</button>
	                <h4 class="modal-title">Choose Template</h4>
	            </div>
	            <div class="modal-body">
	                <div class="select_template row">
						<div class="col-sm-6 select_template_container text-center" id="id_template_2">
							<span style="font-size: 14px;">Simple</span>
							<a role="button" class="template_type" id="template_2" >
								<img src="/site_media/images/template_simple.png" style="width: 100%; border: solid 2px #333; border-radius: 4px;" />
							</a>
							<div class="selection_template">
								<i class="fa fa-check fa-6" aria-hidden="true"></i>
							</div>
						</div>
						<div class="col-sm-6 select_template_container text-center" id="id_template_1">
							<span style="font-size: 14px;">Compact</span>
							<a role="button" class="template_type" id="template_1" >
								<img src="/site_media/images/template_compact.png" style="width: 100%;  border: solid 2px #333; border-radius: 4px;" />
							</a>
							<div class="selection_template">
								<i class="fa fa-check fa-6" aria-hidden="true"></i>
							</div>
						</div>
						<div class="col-sm-6 select_template_container text-center" id="id_template_3"
						     style="margin-top:20px;">
							<span style="font-size: 14px;">Elegant</span>
							<a role="button" class="template_type" id="template_3">
								<img src="/site_media/images/template_elegant.png"style="width: 100%;  border: solid 2px #333; border-radius: 4px;"/>
							</a>
							<div class="selection_template">
								<i class="fa fa-check fa-6" aria-hidden="true"></i>
							</div>
						</div>
						<input type="hidden" name="template_name" value='{{master_res.template_name}}'>
					</div>
	            </div>
	        </div>
	    </div>
	</div>
	<div id="preview_modal" class="modal fade" role="dialog" tabindex="-1">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">&times;</button>
					<h4 class="modal-title">PDF Preview</h4>
				</div>
				<div class="modal-body">
					<div id="preview_document_container" class="full_txt_width">
						<div class="contant-container-nav" style="padding-top: 0;">
							<ul class="nav nav-tabs list-inline" style="margin-left: 0;">
								<li class="active"><a data-toggle="tab" href="#tab1" id="single-page-invoice">Single Page Invoice</a></li>
								<li><a data-toggle="tab" href="#tab2">Multi-Page Invoice</a></li>
							</ul>
							<span class="text-warning" style="position: absolute; color: #f0ad4e; right: 15px; top: 15px; width: 280px; text-align: right;">
								* Data populated below are for representation & does not depict any actual Invoice
							</span>
						</div>
						<div class="tab-content" style="border: 1px solid #ccc; padding: 10px; margin-top: -16px;">
							<div id="tab1" class="tab-pane fade in active">
							</div>
							<div id="tab2" class="tab-pane">
							</div>
						</div>
					</div>
				</div>
				<div class="modal-footer">
	                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
	            </div>
			</div>
		</div>
	</div>

	<div id="notesEditableContainer" class="modal fade" role="dialog">
		<div class="modal-dialog modal-lg" style="width: 80%;">
		    <div class="modal-content">
	            <div class="modal-header">
	                <button type="button" class="close" data-dismiss="modal">&times;</button>
	                <h4 class="modal-title">Notes</h4>
	            </div>
	            <div class="modal-body">
	                <div id="notes_editor">
	                	<b>{{misc_res.notes}}</b>
					</div>
	            </div>
	            <div class="modal-footer">
	            	<button  id="add_invoice_notes" type="button" class="btn btn-save" onclick="addInvoiceNote();">Add</button>
	            	<button  id="cancel_invoice_notes" data-dismiss="modal" type="button" class="btn btn-cancel">Cancel</button>
	            </div>
	        </div>
	    </div>
	</div>
</form>
<div id="change_log_modal" class="modal" tabindex="-1" role="dialog" aria-labelledby="" aria-hidden="true">
   <div class="modal-dialog">
	    <div class="modal-content">
		    <div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Activity Log</h4>
	      	</div>
      		<div class="modal-body remove-padding">
		        <div id='loadingmessage_changelog_listing_ie' style="text-align:center">
						<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' width="100">
						<br /> Please wait...
			    </div>
        		<ul class="history-log-container timeline">

        		</ul>
		         <ul class="show-more-log" style="list-style: none; text-align: right; margin-right: 15px; cursor: pointer;">
			        <li onclick="shoMoreLogs(20,20)">Show More</li>
		        </ul>
      		</div>
      		<div class="modal-footer">
      			<a href="#" class="btn btn-cancel" data-dismiss="modal">Close</a>
      		</div>
    	</div>
  	</div>
</div>
<script type="text/javascript">
	$(document).ready(function(){
		$('.header-banner').awesomeCropper(
	        { debug: false }
    	);
    	applyCroppedImage();
		$("input[type='number']").inputSpinner();
		$('.spinner-textbox').on("cut copy paste",function(e) {
	      e.preventDefault();
	   });

		tabSelection();
		setCurrentDateTime();
		constructDateFormatField();
		logoSliderChangeEvent();
		headerSliderChangeEvent();
		footerSliderChangeEvent();
		inputSpinnerChangeEvent();
		dropDownChangeEvent();
		textboxBlurEvent();
		checkboxChangeEvent();
		imageClickEvent();
		removeBannerImageInit();
		leftContainerScrollInit();
		if($("#saved_invoice_template").val() != ""){
			swal("","Invoice Configuration has been modified successfully.","success");
		}
		$(".form-control.input_spinner").attr({maxLength: "3", onfocus: "setNumberRangeOnFocus(this,3,0)"});

		if($("#last-modified-user").text().trim() != "") {
			$(".last-modified-user-container").removeClass("hide");
		}
		var editorElement = CKEDITOR.document.getById( 'id_itm-notes' );
		editorElement.setAttribute( 'contenteditable', 'false' );
		CKEDITOR.inline( 'id_itm-notes' );
		CKEDITOR.replace( 'notes_editor' );
		CKEDITOR.config.height = 300;
		var editorElement1 = CKEDITOR.document.getById( 'notes_editor' );
		var htmlText = $("#id_itm-notes").text();
		editorElement1.setHtml(
			htmlText
		);
		changeLogActivityInit();
		scanCode();
		$(window).resize(function(){
			leftContainerScrollInit();
		});
		$('#invoice_number_format_help').qtip({
            content: {
                text: ` <b>Default Format</b>
                		<br />------------------------<br />
                		{fy}/{type:.1}{number:05}{sub_number}
                		<br><br>
                		<b>Invoice Code Elements</b>
                		<br>-------------------------------------<b><br>
                		{fy} : </b> Financial Year of the invoice. <br>
                		<b> {type} :</b> Invoice Type - one of GST, Labour/Service, Bill of Supply, Delivery Challan & the like<br>
						<b> {number} :</b>  Serial number of Invoice for the Financial Year<br>
						<b> {sub_number} :</b> Sub-number available for Super-Edits used for Invoice Serial order correction<br><br>
						<b>Format Options</b>
						<br>--------------------------<br>
						<b> :.n : </b> First 'n' characters of the string will be printed <br> <b> :0N : </b> Number of total length 'N' with leading zeros will be printed
						<br><br>`,
                title: 'Number Format Info'
            }
        });
	});

	function shoMoreLogs(offset, limit){
		if($("#change_log_modal").hasClass("change_log")) {
			$.ajax({
		        url: '/erp/admin/invoice_template/getloglist/',
		        type: "POST",
		        dataType: "json",
		        data: {offset: offset, limit: limit},
		        success: function (data) {
		        if (data['response_code'] != 400){
		            x = data['data'];
					var row = "";
					var i2 = offset;
					for(var i = 0; i < x.length; i++) {
					    var obj = x[i].log;
					    var row = `<li class="history-log-part" id="history-part-${i2}" onclick="loadHistoryContent('${data['data'][0].eid}', '${obj.modified_at}', ${i2}, '${obj.preference}')">
				                        <span class="history-log-username">${obj.username[0]+" "+obj.username[1]}</span>
				                        <div class="history-log-content" style="display: none;"></div>
				                        <span class="history-log-date">${moment(obj.modified_at).format("MMM DD, YYYY hh:mm:ss a")}</span>
				                    </li>`;

		                $(".history-log-container").append(row);
		                var displayedCount = $(".history-log-container").find("li").length;
		                i2++;
		                if(x.length < 20){
		                    $(".show-more-log").addClass("hide");
		                }
		                else {
		                    $(".show-more-log").find("li").attr("onclick", "shoMoreLogs("+displayedCount+", 20)");
	                    }
					}
				}
				else {
					$(".show-more-log").addClass("hide");
				}
		        },
		        error: function ($xhr,textStatus,errorThrown) {
			        console.log("ERROR : ", $xhr);
			        if ($xhr.responseText.indexOf("Your session has expired") != -1){
			            location.reload();
			        }
		        }
			});
		}
	}

	function changeLogActivityInit(){
		$("#id-change-log").removeClass("hide");
		$("#btn_change_log").click(function(){
			$("#change_log_modal").addClass("change_log").removeClass("change_preference_log");
			$("#change_log_modal").modal("show");
			$("#loadingmessage_changelog_listing_ie").show();
			$(".history-log-container").html("");
			$.ajax({
		        url: '/erp/admin/invoice_template/getloglist/',
		        type: "POST",
		        dataType: "json",
		        data: {offset: 0, limit: 20},
		        success: function (data) {
		        if (data['response_code'] != 400){
		            x = data['data'];
					var row = "";
					for(var i = 0; i < x.length; i++) {
					    var obj = x[i].log;
					    var row = `<li class="history-log-part" id="history-part-${i}" onclick="loadHistoryContent('${data['data'][0].eid}', '${obj.modified_at}', ${i}, '${obj.preference}')">
				                        <span class="history-log-username">${obj.username[0]+" "+obj.username[1]}</span>
				                        <div class="history-log-content" style="display: none;"></div>
				                        <span class="history-log-date">${moment(obj.modified_at).format("MMM DD, YYYY hh:mm:ss a")}</span>
				                    </li>`;
				        $("#loadingmessage_changelog_listing_ie").hide();
		                $(".history-log-container").append(row);
		                var displayedCount = $(".history-log-container").find("li").length;
					}
						if(x.length < 20){
		                    $(".show-more-log").addClass("hide");
		                }
		                else {
		                    $(".show-more-log").removeClass("hide");
		                    $(".show-more-log").find("li").attr("onclick", "shoMoreLogs("+displayedCount+", 20)");
	                    }
				}
				else {
				    var row = `<span style="width: 100%; padding: 15px; text-align:center; display: block;">No Activity Log has been captured.</span>`;
					$("#loadingmessage_changelog_listing_ie").hide();
		            $("#change_log_modal .modal-body").html(row);
				}
		        },
		        error: function ($xhr,textStatus,errorThrown) {
			        console.log("ERROR : ", $xhr);
			        if ($xhr.responseText.indexOf("Your session has expired") != -1){
			            location.reload();
			        }
		        }
			});
		});

		$("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
	}

	function loadHistoryContent(voucher_id, modified_at, i, preference) {
		var currentLogContent = $("#history-part-"+i).find('.history-log-content');
		if(currentLogContent.text() == "") {
			currentLogContent.html(`<img src="/site_media/images/loading_spinner.gif" style="width: 30px;"> Loading, Please wait...`).show();
			currentLogContent.closest(".history-log-part").addClass("active");
			setTimeout(function(){
				$.ajax({
			        url: '/erp/admin/invoice_template/getlogdata/',
			        type: "POST",
			        dataType: "json",
			        data: {"voucher_id":voucher_id, "modified_at": modified_at, "preference": preference},
			        success: function (data) {
			        if (data['response_code'] != 400){
			        var row = '';
				        $.each( data['data'], function( key, value ) {
						  row += `<li>` + value + `</li>`;
						});
			        currentLogContent.html(`<ul>` + row + `</ul>`);
					currentLogContent.hide();
					currentLogContent.slideDown();
					}else{
					currentLogContent.html(`log data not available`);
					currentLogContent.hide();
					currentLogContent.slideDown();

					}
			        },
			        error: function () {
			        console.log('Changelog listing failed to load');

			        }
				});

			},100);
		}
		else {
			if(currentLogContent.find("img").length > 0) {
				return;
			}
			if(currentLogContent.is(":visible")) {
				currentLogContent.slideUp('fast', function() {
				   currentLogContent.closest(".history-log-part").removeClass("active");
				});

			}
			else {
				currentLogContent.slideDown();
				currentLogContent.closest(".history-log-part").addClass("active");
			}
		}
		$("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
	}

	function loadPreferenceHistoryContent(voucher_id, modified_at, i) {
		var currentLogContent = $("#history-part-"+i).find('.history-log-content');
		if(currentLogContent.text() == "") {
			currentLogContent.html(`<img src="/site_media/images/loading_spinner.gif" style="width: 30px;"> Loading, Please wait...`).show();
			currentLogContent.closest(".history-log-part").addClass("active");
			setTimeout(function(){
				$.ajax({
			        url: '/erp/admin/enterprise/getpreferencelogdata/',
			        type: "POST",
			        dataType: "json",
			        data: {"voucher_id":voucher_id, "modified_at": modified_at},
			        success: function (data) {
			        if (data['response_code'] != 400){
			        var row = '';
				        $.each( data['data'], function( key, value ) {
						  row += `<li>` + value + `</li>`;
						});
			        currentLogContent.html(`<ul>` + row + `</ul>`);
					currentLogContent.hide();
					currentLogContent.slideDown();
					}else{
					currentLogContent.html(`log data not available`);
					currentLogContent.hide();
					currentLogContent.slideDown();

					}
			        },
			        error: function () {
			        console.log('Changelog listing failed to load');

			        }
				});

			},100);
		}
		else {
			if(currentLogContent.find("img").length > 0) {
				return;
			}
			if(currentLogContent.is(":visible")) {
				currentLogContent.slideUp('fast', function() {
				   currentLogContent.closest(".history-log-part").removeClass("active");
				});

			}
			else {
				currentLogContent.slideDown();
				currentLogContent.closest(".history-log-part").addClass("active");
			}
		}
		$("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
	}

	function updateRegistrationDetails() {
		var registrationDetailsObj = [];
		$(".registration_details").find("input[type='checkbox']").each(function(){
			var current = $(this);
			if(current.is(":checked")) {
				var item = {};
				item ["label_id"] = Number(current.attr("data-field-id"));
                item ["label"] = current.next("label").text();
                registrationDetailsObj.push(item);
			}
		});
		registrationDetailsObj = JSON.stringify(registrationDetailsObj);
		$("#id_ith-included_reg_items").val(registrationDetailsObj)
	}

	$(window).load(function(){
		$(".inv_template_editor").find("input[type='number']").trigger("change");
		$(".inv_template_editor").find("select").trigger("change");
		$(".inv_template_editor").find("input[type='text'], textarea").trigger("blur");
		$(".inv_template_editor").find("input[type='checkbox']").trigger("change");
		$("input[name='iti-tax_type']").trigger("change");
		$("input[name='iti-tax_payable_on_reverse_charge']").trigger("change");
		var output = $("#logo_size_value").text();
		$(".pdf_company_logo").css({height: output});
		$(".invoice_template_default, .invoice_template_1, .invoice_template_2").addClass("hide");
		$(".select_template_container").removeClass("selected_template")
		if($("#id_itc-template_id").val() == "1") {
			$(".invoice_template_default").removeClass("hide");
			$("#id_template_1").addClass("selected_template");
		}
		else if($("#id_itc-template_id").val() == "2"){
			$(".invoice_template_1").removeClass("hide");
			$("#id_template_2").addClass("selected_template");
		}
		else{
			$(".invoice_template_2").removeClass("hide");
			$("#id_template_3").addClass("selected_template");
		}

		if($("#id_itg-tab_retain").val() != "") {
			var selectedTab = $("#id_itg-tab_retain").val();
			$(".nav_invoice_template li").removeClass("active")
			$(".nav_invoice_template").find("li:nth-child("+selectedTab+")").trigger("click")
		}
		updateBannerImagesOnLoad();
	});

	function tabSelection() {
		$(".nav_invoice_template li").click(function(){
			$(".nav_invoice_template li").removeClass("active");
			var selectedLi = $(this).data("field");
			var selectedTab = $(this).data("tab");
			$(this).addClass("active");
			$(".inv_template_edit").addClass("hide");
			$("."+selectedLi).removeClass("hide");
			$("#id_itg-tab_retain").val(selectedTab)
		});
	}

	function setCurrentDateTime() {
		var dt = new Date();
		var datetime = moment(dt).format("YYYY-MM-DD hh:mm:ss")
		var date = moment(dt).format("YYYY-MM-DD")
		$(".issue_date_time").text(datetime);
		$(".pdf_po_date").text(date);
		$(".pdf_estimate_date").text(date);
	}

	function logoSliderChangeEvent() {
		var slider = document.getElementById("id_ith-logo_size");
		var output = document.getElementById("logo_size_value");
		output.innerHTML = slider.value;
		slider.oninput = function() {
		  output.innerHTML = this.value;
		  $(".pdf_company_logo").css({height: this.value});
		}
	}

	function inputSpinnerChangeEvent() {
		$("input[type='number']").on("change", function (event) {
			var spinner_for = $(this).attr("spinner_for");
			var spinnerValue = $(this).val();
			if(spinnerValue == "") spinnerValue = 0;
			if(spinner_for == "pdf_company_name") {
			    $(".pdf_company_name").css({fontSize: spinnerValue+"px"});
			}
			else if(spinner_for == "pdf_company_details") {
				$(".pdf_company_address_container, .pdf_company_details_container").css({fontSize: spinnerValue+"px"});
				$(".pdf_issue_date_time").css({fontSize: spinnerValue+"px"});
			}
			else if(spinner_for == "pdf_invoice_number") {
				$(".pdf_invoice_prefix, .pdf_invoice_no").css({fontSize: spinnerValue+"px"});
			}
			else if(spinner_for == "pdf_form_name") {
				$(".pdf_form_name_text").css({fontSize: spinnerValue+"px"});
			}
			else if(spinner_for == "page_margin_top") {
				$(".inv_pdf_editor").css({paddingTop: spinnerValue+"mm"});
			}
			else if(spinner_for == "page_margin_bottom") {
				$(".inv_pdf_editor").css({paddingBottom: spinnerValue+"mm"});
			}
			else if(spinner_for == "page_margin_left") {
				$(".inv_pdf_editor").css({paddingLeft: spinnerValue+"mm"});
			}
			else if(spinner_for == "page_margin_right") {
				$(".inv_pdf_editor").css({paddingRight: spinnerValue+"mm"});
			}
			else if(spinner_for == "table_font_size") {
				$(".item_table thead, .item_table tbody").css("font-size", spinnerValue+"px");
				$(".primary_qty_font").css("font-size", Number(spinnerValue - 2)+"px");
			}
			else if(spinner_for == "total_font_size") {
				$(".item_table tfoot, .notes_section, .signatory_content").css({fontSize: spinnerValue+"px", lineHeight: spinnerValue+"px"});
			}
			else if(spinner_for == "hsn_tax_font_size") {
				$(".hsn_summary, .hsn_summary_title").css("font-size", spinnerValue+"px");
			}
			else if(spinner_for == "misc_font_size") {
				$(".footer_notes").css({fontSize: spinnerValue+"px"});
			}
			else if(spinner_for == "table_bottom_height") {
				$(".item_table tbody .table_bottom").css("height", spinnerValue+"px");
			}
			else {
				calculateWidthforItemTable();
			}
		});
	}

	function dropDownChangeEvent(){
		$("select").change(function(){
			var selectField = $(this).attr("id");
			var selectValue = $(this).val();
			if(selectField == "id_itg-base_font") {
				$(".inv_pdf_editor").css({fontFamily: selectValue });
			}
			else if(selectField == "id_ith-name_font") {
				$(".pdf_company_name").css({fontFamily: selectValue });
			}
		});
	}

	function textboxBlurEvent(){
		$("input[type='text'], textarea").keyup(function(){
			var checkbox_for = $(this).attr("textboxfor");
			$("."+checkbox_for).text($(this).val());
			if(checkbox_for == "bill_to_text" || checkbox_for == "ship_to_text") {
				if($(this).val().trim() == "") {
					$("."+checkbox_for).addClass("hide");
				}
				else {
					$("."+checkbox_for).removeClass("hide");
				}
			}
		});
		$("input[type='text'], textarea").blur(function(){
			var checkbox_for = $(this).attr("textboxfor");
			$("."+checkbox_for).html($(this).val());
			if(checkbox_for == "bill_to_text" || checkbox_for == "ship_to_text") {
				if($(this).val().trim() == "") {
					$("."+checkbox_for).addClass("hide");
				}
				else {
					$("."+checkbox_for).removeClass("hide");
				}
			}
		});
	}

	function checkboxChangeEvent() {
	    $('#id_iti-include_make').prop('checked', true);
	    $('#id_iti-include_partno').prop('checked', true);
		$(".required_checkbox").change(function(){
			var checkbox_for = $(this).attr("checkboxfor");
			if($(this).is(":checked")) {
				$("."+checkbox_for).removeClass("hide")
			}
			else {
				$("."+checkbox_for).addClass("hide")
			}
			if(checkbox_for == 'hsn_summary_contianer') {
				if($("#id_its-include_hsn_summary").is(":checked")) {
					$(".table_bottom").addClass("hide");
				}
				else {
					$(".table_bottom").removeClass("hide");
				}
			}
			if(checkbox_for == 'pdf_item_make' || checkbox_for == 'pdf_item_part') {
				var isMakeChecked = $("#id_iti-include_make").is(":checked");
				var isPartChecked = $("#id_iti-include_partno").is(":checked");
				if( isMakeChecked && isPartChecked) {
					$(".pdf_item_make_start").html("[");
					$(".pdf_item_part_start").html("");
					$(".pdf_item_make_value").html("NEW - ");
					$(".pdf_item_part_value").html("PN534]<br>");
				}
				else if( isMakeChecked || isPartChecked) {
					if(isMakeChecked) {
						$(".pdf_item_make_start").html("[");
						$(".pdf_item_part_start").html("");
						$(".pdf_item_make_value").html("NEW]<br>");
					}
					else {
						$(".pdf_item_part_start").html("[");
						$(".pdf_item_make_start").html("");
						$(".pdf_item_part_value").html("PN534]<br>");
					}
				}
				else {
					$(".pdf_item_make_value").html("");
					$(".pdf_item_part_value").html("");
				}
			}
			if(checkbox_for == 'pdf_bill_to_enterprise_code' || checkbox_for == 'pdf_bill_to_enterprise_name') {
				var isPartyCodeChecked = $("#id_ith-include_partycode").is(":checked");
				var isPartyNameChecked = $("#id_ith-include_partyname").is(":checked");
				if( isPartyCodeChecked && isPartyNameChecked) {
					$(".pdf_bill_to_enterprise_detail").html("ABC SOFTWARE SOLUTION (ABC001)");
					$(".pdf_ship_to_enterprise_detail").html("ABC ENERGY PVT LTD (ABC123)");
				}
				else if( isPartyCodeChecked || isPartyNameChecked) {
					if(isPartyCodeChecked) {
						$(".pdf_bill_to_enterprise_detail").html("ABC001");
						$(".pdf_ship_to_enterprise_detail").html("ABC123");
					}
					else {
						$(".pdf_bill_to_enterprise_detail").html("ABC SOFTWARE SOLUTION");
						$(".pdf_ship_to_enterprise_detail").html("ABC ENERGY PVT LTD");
					}
				}
				else {
					$(".pdf_bill_to_enterprise_detail").html("");
					$(".pdf_ship_to_enterprise_detail").html("");
				}
			}
		});

		$(".customized_checkbox").change(function(){
			var checkbox_for = $(this).attr("checkboxfor");
			if(checkbox_for == "row_seperator") {
				if($(this).is(":checked")) {
					$(".item_table, .hsn_table").addClass("row-seperator");
				}
				else {
					$(".item_table, .hsn_table").removeClass("row-seperator");
				}
			}
			if(checkbox_for == "column_seperator") {
				if($(this).is(":checked")) {
					$(".item_table, .hsn_table").addClass("column-seperator");
				}
				else {
					$(".item_table, .hsn_table").removeClass("column-seperator");
				}
			}
			if(checkbox_for == "row_shading") {
				if($(this).is(":checked")) {
					$(".row_shading").addClass("shaded");
					$(".header_shading").addClass("shaded");
				}
				else {
					$(".row_shading").removeClass("shaded");
					$(".header_shading").removeClass("shaded");
				}
			}
			if(checkbox_for == "show_total_section") {
				if($(this).is(":checked")) {
					$(".total_section").removeClass("hide");
					$("input[name='iti-tax_type']").trigger("change");
					$("#id_its-include_subtotal").removeAttr("disabled");
					if($("#id_its-include_subtotal").is(":checked")) {
						$("#id_its-include_qty_total").removeAttr("disabled");
					}
					else {
						$("#id_its-include_qty_total").attr("disabled", "disabled");
					}
				}
				else {
					$(".total_section").addClass("hide");
					$("#id_its-include_subtotal").attr("disabled", "disabled");
					$("#id_its-include_qty_total").attr("disabled", "disabled");
				}
				$("#id_its-include_subtotal").trigger("change");
			}

			if(checkbox_for == "show_sub_total_section") {
				if($(this).is(":checked") && $(this).is(":enabled")) {
					$(".sub_total_section").removeClass("hide");
					if($("#id_its-include_total").is(":checked")) {
						$("#id_its-include_qty_total").removeAttr("disabled");
					}
					else {
						$("#id_its-include_qty_total").attr("disabled", "disabled");
					}
				}
				else {
					$(".sub_total_section").addClass("hide");
					$("#id_its-include_qty_total").attr("disabled", "disabled");
				}
			}

			if(checkbox_for == "taxable_item") {
				if($(this).is(":checked")) {
					$("input[name='iti-tax_type'], .taxable_item").removeAttr("disabled");
					$(".tax_rate_column, .tax_one_column, .consolidated_tax, .tax_in_description, .other_tax_column").removeClass("hide");
					$("input[name='iti-tax_type']").trigger("change");
					$("#id_iti-show_tax_for_dc").removeAttr("disabled");
				}
				else {
					$("#id_iti-tax_type_hidden_field").val($("input[name='iti-tax_type']:checked").val())
					$("input[name='iti-tax_type'], .taxable_item").attr("disabled", "disabled");
					$(".tax_rate_column, .tax_one_column, .consolidated_tax, .tax_in_description, .other_tax_column").addClass("hide");
					$(".total_section_4").addClass("hide");
					$("#id_iti-show_tax_for_dc").attr("disabled", "disabled");
					colspanManipulation();
				}
				calculateWidthforItemTable();
			}

			if(checkbox_for == "td_tax"){
				if($(this).is(":checked")) {
					$(".td_tax").removeClass("hide");
				}
				else {
					$(".td_tax").addClass("hide");
				}
				colspanManipulation();
				calculateWidthforItemTable();
			}
			if(checkbox_for == "show_quantity_total"){
				if($(this).is(":checked")) {
					$(".total_section_2").removeClass("hide");
				}
				else {
					$(".total_section_2").addClass("hide");
				}
				colspanManipulation();
				calculateWidthforItemTable();
			}

			if(checkbox_for == "tax_rate_column" || checkbox_for == "tax_amount_column") {
				var isRateEnabled = $("#id_iti-include_taxrate").is(":checked") && $("#id_iti-include_taxrate").is(":enabled");
				var isAmtEnabled = $("#id_iti-include_taxamount").is(":checked") && $("#id_iti-include_taxamount").is(":enabled");
				if(isRateEnabled && isAmtEnabled) {
					$(".tax_one_column").addClass("hide");
					$(".tax_rate_column").removeClass("hide");
					$(".item_1 .tax_one_column_csgt").html("6.48<br><small>@ 2.50%</small>");
					$(".item_1 .tax_one_column_ssgt").html("6.48<br><small>@ 2.50%</small>");
					$(".item_1 .tax_one_column_isgt").html("-");
					$(".item_2 .tax_one_column_csgt").html("2.70<br><small>@ 2.50%</small>");
					$(".item_2 .tax_one_column_ssgt").html("2.70<br><small>@ 2.50%</small>");
					$(".item_2 .tax_one_column_isgt").html("-");
					$(".tax_one_column_csgt_total").html("9.35");
					$(".tax_one_column_ssgt_total").html("9.35");
					$(".tax_one_column_isgt_total").html("-");
					$(".total_section_4").attr({colspan: "6"}).removeClass("hide");
				}
				else if(isRateEnabled || isAmtEnabled) {
					$(".tax_one_column").removeClass("hide");
					$(".tax_rate_column").addClass("hide");
					if(isAmtEnabled) {
						var selected_tax = $("input[name='iti-tax_type']:checked").val();
						if(selected_tax == 4) {
							$(".item_1 .tax_one_column_csgt").html("6.48<br><small>@ 2.50%</small>");
							$(".item_1 .tax_one_column_ssgt").html("6.48<br><small>@ 2.50%</small>");
							$(".item_1 .tax_one_column_isgt").html("-");
							$(".item_2 .tax_one_column_csgt").html("2.70<br><small>@ 2.50%</small>");
							$(".item_2 .tax_one_column_ssgt").html("2.70<br><small>@ 2.50%</small>");
							$(".item_2 .tax_one_column_isgt").html("-");
							$(".tax_one_column_csgt_total").html("9.35");
							$(".tax_one_column_ssgt_total").html("9.35");
							$(".tax_one_column_isgt_total").html("-");
						}
						else {
							$(".item_1 .tax_one_column_csgt").text("6.65");
							$(".item_1 .tax_one_column_ssgt").text("6.65");
							$(".item_1 .tax_one_column_isgt").text("-");
							$(".item_2 .tax_one_column_csgt").text("2.70");
							$(".item_2 .tax_one_column_ssgt").text("2.70");
							$(".item_2 .tax_one_column_isgt").text("-");
							$(".tax_one_column_csgt_total").text("9.35");
							$(".tax_one_column_ssgt_total").text("9.35");
							$(".tax_one_column_isgt_total").text("-");
						}
					}
					else {
						$(".tax_one_column_csgt").text("2.5%");
						$(".tax_one_column_ssgt").text("2.5%");
						$(".tax_one_column_isgt").text("-");
						$(".tax_one_column_csgt_total").text("");
						$(".tax_one_column_ssgt_total").text("");
						$(".tax_one_column_isgt_total").text("");
					}
					$(".total_section_4").attr({colspan: "3"}).removeClass("hide");
				}
				else {
					$(".tax_one_column").addClass("hide");
					$(".tax_rate_column").addClass("hide");
					$(".tr_second_row").addClass("hide");
					$(".total_section_4").addClass("hide");
				}
				colspanManipulation();
				calculateWidthforItemTable();
			}
			if(checkbox_for == "pdf_item_hsn_code" || checkbox_for == "pdf_hsn_in_description") {
				var isHsnEnabled = $("#id_iti-include_hsnsac").is(":checked");
				var isHsnDescEnabled = $("#id_iti-hsnsac_part_of_itemdetails").is(":checked");
				if(isHsnEnabled && isHsnDescEnabled) {
					$(".pdf_item_hsn_code").removeClass("hide");
					$(".td_hsn_code").addClass('hide');
					$("#id_iti-hsnsac_width").attr("disabled", "disabled");
				}
				else if(isHsnEnabled || isHsnDescEnabled) {
					if(isHsnEnabled) {
						$(".pdf_item_hsn_code").addClass("hide");
						$(".td_hsn_code").removeClass('hide');
						$("#id_iti-hsnsac_width").removeAttr("disabled")
					}
					else {
						$(".td_hsn_code").addClass('hide');
						$(".pdf_item_hsn_code").addClass("hide");
						$("#id_iti-hsnsac_width").attr("disabled", "disabled")
					}

				}
				else {
					$(".pdf_item_hsn_code").addClass("hide");
					$(".td_hsn_code").addClass('hide');
					$("#id_iti-hsnsac_width").attr("disabled", "disabled")
				}
				if(isHsnEnabled) {
					$("#id_iti-hsnsac_part_of_itemdetails").removeAttr("disabled")
				}
				else {
					$("#id_iti-hsnsac_part_of_itemdetails").attr("disabled", "disabled")
				}
				setTimeout(function(){
					calculateWidthforItemTable();
					colspanManipulation();
				},10);
			}

			if(checkbox_for == "pdf_item_dc_no") {
				var isDcnoEnabled = $("#id_iti-include_dc_no").is(":checked");
				var isDcqtyEnabled = $("#id_iti-include_dc_qty").is(":checked");
				var isDcdateEnabled = $("#id_iti-include_dc_date").is(":checked");
				if(isDcnoEnabled) {
					$("#id_iti-include_dc_qty").removeAttr("disabled")
					$("#id_iti-include_dc_date").removeAttr("disabled")
					$(".pdf_item_dc_no").removeClass("hide");
				}
				else {
					$("#id_iti-include_dc_qty").attr("disabled", "disabled")
					$("#id_iti-include_dc_date").attr("disabled", "disabled")
					$(".pdf_item_dc_no").addClass("hide");
				}
			}

			if(checkbox_for == "td_uom" || checkbox_for == "pdf_unit_in_price" || checkbox_for == "pdf_primary_unit") {
				var isUnitEnabled = $("#id_iti-include_units").is(":checked");
				var isUnitInQtyEnabled = $("#id_iti-units_in_quantity_column").is(":checked");
				var isIncludePrimaryUnit = $("#id_iti-include_primary_qty").is(":checked");
				if(isUnitEnabled && isUnitInQtyEnabled && isIncludePrimaryUnit ) {
					$(".pdf_unit_in_price").removeClass("hide");
					$(".pdf_primary_unit").removeClass("hide");
					$(".pdf_primary_unit_value2").addClass("hide");
					$(".pdf_primary_unit_value").removeClass("hide");
					$(".td_uom").addClass('hide');
					$("#id_iti-units_width").attr("disabled", "disabled");
				}
				else if(isUnitEnabled && isUnitInQtyEnabled) {
					$(".pdf_unit_in_price").removeClass("hide");
					$(".pdf_primary_unit").addClass("hide");
					$(".pdf_primary_unit_value").addClass("hide");
					$(".pdf_primary_unit_value2").addClass("hide");
					$(".td_uom").addClass('hide');
					$("#id_iti-units_width").attr("disabled", "disabled");
				}
				else if(isUnitEnabled && isIncludePrimaryUnit) {
					$(".pdf_unit_in_price").addClass("hide");
					$(".pdf_primary_unit").addClass("hide");
					$(".pdf_primary_unit2").removeClass("hide");
					$(".pdf_primary_unit_value").addClass("hide");
					$(".pdf_primary_unit_value2").removeClass("hide");
					$(".td_uom").removeClass('hide');
					$("#id_iti-units_width").removeAttr("disabled")
				}
				else if(isUnitEnabled || isUnitInQtyEnabled) {
					if(isUnitEnabled) {
						$(".pdf_unit_in_price").addClass("hide");
						$(".pdf_primary_unit").addClass("hide");
						$(".pdf_primary_unit2").addClass("hide");
						$(".pdf_primary_unit_value").addClass("hide");
						$(".pdf_primary_unit_value2").addClass("hide");
						$(".td_uom").removeClass('hide');
						$("#id_iti-units_width").removeAttr("disabled")
					}
					else {
						$(".td_uom").addClass('hide');
						$(".pdf_unit_in_price").addClass("hide");
						$(".pdf_primary_unit").addClass("hide");
						$(".pdf_primary_unit_value").addClass("hide");
						$(".pdf_primary_unit_value2").addClass("hide");
						$("#id_iti-units_width").attr("disabled", "disabled")
					}
				}
				else {
					$(".pdf_unit_in_price").addClass("hide");
					$(".pdf_primary_unit").addClass("hide");
					$(".pdf_primary_unit_value").addClass("hide");
					$(".pdf_primary_unit_value2").addClass("hide");
					$(".td_uom").addClass('hide');
					$("#id_iti-units_width").attr("disabled", "disabled")
				}
				if(isUnitEnabled) {
					$("#id_iti-units_in_quantity_column").removeAttr("disabled")
					$("#id_iti-include_primary_qty").removeAttr("disabled")
				}
				else {
					$("#id_iti-units_in_quantity_column").attr("disabled", "disabled")
					$("#id_iti-include_primary_qty").attr("disabled", "disabled")
				}
				setTimeout(function(){
					calculateWidthforItemTable();
					colspanManipulation();
				},10);
			}

			if(checkbox_for == "td_qty" || checkbox_for == "show_quantity_total") {
				var isQtyEnabled = $("#id_iti-include_quantity").is(":checked");
				var isQtyInTotalEnabled = $("#id_its-include_qty_total").is(":checked");
				if(isQtyEnabled && isQtyInTotalEnabled) {
					$(".total_section_2").removeClass("hide");
					$(".td_qty").removeClass("hide")
				}
				else if(isQtyEnabled || isQtyInTotalEnabled) {
					$(".total_section_2").addClass("hide")
					if(isQtyEnabled) {
						$(".td_qty").removeClass("hide");
					}
					else {
						$(".td_qty").addClass("hide");
					}
				}
				else {
					$(".total_section_2").addClass("hide");
					$(".td_qty").addClass("hide");
				}
				setTimeout(function(){
					calculateWidthforItemTable();
					colspanManipulation();
				},10);
			}
		});
	}

	$(".width_calc_checkbox").change(function(){
		if($(this).is(":checked") && $(this).is(":enabled")) {
			$(this).closest("div").find(".input_spinner").removeAttr("disabled");
		}
		else {
			$(this).closest("div").find(".input_spinner").attr("disabled", "disabled");
		}
		setTimeout(function(){
			calculateWidthforItemTable();
			colspanManipulation();
		},10);
	});



	$("input[name='iti-tax_type']").change(function(){
		var selected_tax = $("input[name='iti-tax_type']:checked").val();
		if(selected_tax == "1") {
			$(".tax_rate_column").removeClass("hide");
			$(".tax_one_column, .consolidated_tax, .tax_in_description").addClass("hide");
			$(".total_section_4").attr({colspan: "6"}).removeClass("hide");
			if($("#id_iti-include_tax").is(":checked")) {
				$(".taxable_item").removeAttr("disabled");
			}
		}
		else if(selected_tax == "2") {
			$(".consolidated_tax").removeClass("hide");
			$(".tax_one_column, .tax_rate_column, .tax_in_description").addClass("hide");
			$(".total_section_4").addClass("hide");
			$(".taxable_item").attr("disabled", "disabled");
		}
		else if(selected_tax == "3") {
			$(".tax_in_description").removeClass("hide");
			$(".tax_one_column, .tax_rate_column, .consolidated_tax").addClass("hide");
			$(".total_section_4").addClass("hide");
			$(".taxable_item").attr("disabled", "disabled");
		}
		else if(selected_tax == "4") {
			$(".tax_one_column").removeClass("hide");
			$(".tax_rate_column, .consolidated_tax, .tax_in_description").addClass("hide");
			$(".total_section_4").attr({colspan: "3"}).removeClass("hide");
			$(".taxable_item").attr("disabled", "disabled");
			if($("#id_iti-include_tax").is(":checked")) {
				$("#id_iti-include_taxamount, #id_iti-taxamount_width").removeAttr("disabled")
			}
		}
		colspanManipulation();
		setTimeout(function(){
			calculateWidthforItemTable();
			$("#id_iti-include_taxrate").trigger("change");
			$("#id_iti-include_taxamount").trigger("change");
		},20);

	});

	function colspanManipulation() {
		var totalVisibleColumn = $(".item_table").find("tbody").find("tr:first-child").find("td:visible").length;
		var totalVisibleColumnForPrice = $(".item_table").find("thead").find("tr:first-child").find("th:lt(3):visible").length;
		var totalVisibleColumnForPrice_1 = $(".item_table").find("thead").find("tr:first-child").find("th:lt(7):visible").length;
		var isRateEnabled = $("#id_iti-include_taxrate").is(":checked") && $("#id_iti-include_taxrate").is(":enabled");
		var isAmtEnabled = $("#id_iti-include_taxamount").is(":checked") && $("#id_iti-include_taxamount").is(":enabled");



		var totalAmtVisibleColumn, totalTaxVisibleColumn = 0;
		if($(".td_uom_text").is(":visible")) {
			totalTaxVisibleColumn++;
		}
		if($(".td_price_text").is(":visible")) {
			totalTaxVisibleColumn++;
		}
		if($(".td_disc_text").is(":visible")) {
			totalTaxVisibleColumn++;
		}
		if($(".td_tax_text").is(":visible")) {
			totalTaxVisibleColumn++;
		}
		totalAmtVisibleColumn = totalTaxVisibleColumn;
		if($(".td_qty_text").is(":visible") && !$("#id_its-include_qty_total").is(":checked")) {
			totalAmtVisibleColumn++;
		}
		if($(".td_qty_text").is(":visible")) {
			totalTaxVisibleColumn++;
		}
		if(isRateEnabled) {
			totalTaxVisibleColumn += 3;
		}
		if(isAmtEnabled) {
			totalTaxVisibleColumn += 3;
		}
		if(isRateEnabled && isAmtEnabled) {
			$(".total_section_1").attr("colspan", totalVisibleColumnForPrice_1);
			$(".total_section_3").attr("colspan", Number(totalVisibleColumn - totalVisibleColumnForPrice_1));
		}
		else {
			$(".total_section_1").attr("colspan", totalVisibleColumnForPrice);
			$(".total_section_3").attr("colspan", Number(totalVisibleColumn - totalVisibleColumnForPrice));
		}
		$(".sub_total_section .total_section_1").attr("colspan", totalVisibleColumnForPrice);
		$(".full-length-td").attr("colspan", totalVisibleColumn);
		$(".sub_total_section .total_section_3").attr("colspan", totalAmtVisibleColumn);
	}

	function calculateWidthforItemTable() {
		var totalWidthValue = 0;
		$(".form-control.input_spinner_set_width:enabled").each(function() {
			if($(this).closest(".input_width_spinner").find(".width_calc_checkbox").attr("id") == "id_iti-include_taxrate" && $(this).closest(".input_width_spinner").find(".width_calc_checkbox").is(":checked")) {
				totalWidthValue += Number($(this).val() * 3);
			}
			else if($(this).closest(".input_width_spinner").find(".width_calc_checkbox").attr("id") == "id_iti-include_taxamount" && $(this).closest(".input_width_spinner").find(".width_calc_checkbox").is(":checked")) {
				totalWidthValue += Number($(this).val() * 3);
			}
			else {
				totalWidthValue += Number($(this).val());
			}
			var widthForDescription = 100 - totalWidthValue;
			$("input[spinner_for='td_description']").val(widthForDescription);
			$("#id_iti-itemdetails_width_hidden_field").val(widthForDescription);
		});
		setWidthToItemTable();
	}

	$("input[name='iti-tax_payable_on_reverse_charge']").change(function(){
		var selected_option = $("input[name='iti-tax_payable_on_reverse_charge']:checked").val();
		if(selected_option == "1") {
			$(".pdf_tax_payable").removeClass("hide");
		}
		else {
			$(".pdf_tax_payable").addClass("hide");
		}
	});

	$("#id_ith-inv_number_format").change(function(){
		var data = $(this).val();
		validateInvoiceNumberFormat(data, "edit", "");
	});

	function validateInvoiceNumberFormat(data, action, url){
		if(data != ""){
			$("#loading").show();
			$.ajax({
	            url : "erp/admin/json/validate_invoice_number_format/",
				type: "POST",
			    dataType: "json",
			    async: false,
			    data : {"number_format": data},
	            success: function (response){
	                $("#invoice_number_validation_result").val('');
	                $("#loading").hide();
	                if(response.response_message =="Success"){
	                    var formatted_number = response["formated_invoice_number"]
	                    var text_length = formatted_number.length;
						$(".custom-error-message").addClass('hide');
						if(text_length >16 && text_length <= 30)
						{
							swal({
								title: "",
								text: "Invoice code exceeds 16 Characters of length, which is the GST norm.<br> Do you wish to continue with this format?",
								type: "warning",
								showCancelButton: true,
								confirmButtonColor: "#209be1",
								confirmButtonText: "Yes, do it!",
								closeOnConfirm: true,
							},
							function(isConfirm){
								if(isConfirm) {
									$(".preview_invoice_number").text(formatted_number);
									$(".preview_invoice_number").removeClass('hide');
									$(".custom-error-message").addClass('hide');
									if(action == "save"){
										$("#template_add").attr('action', url);
										$("#template_add").submit();
									}
								}else{
									$(".preview_invoice_number").trigger('focus');
								}
							});
						}else if(text_length > 30){
							$(".error-border").removeClass('error-border');
                            $(".custom-error-message").removeClass('hide');
                            $(".preview_invoice_number").addClass('hide');
                        }else{
							$(".preview_invoice_number").text(formatted_number);
							 if(action == "save"){
								$("#template_add").attr('action', url);
								$("#template_add").submit();
							 }
						}
					}
					else{
	                    $("#invoice_number_validation_result").val("Invalid number format has been applied!<br> Please check the info");
	                    swal({title: "", text: "Invalid invoice number format has been applied!<br> Please check the info", type: "error"});
	                    $(".preview_invoice_number").trigger('focus');
	                }
	            },
	            error: function (xhr, errmsg, err) {
	                console.log(xhr.status + ": " + xhr.responseText);
	            }
	        });
        }else{
        	 swal({title: "", text: "Invoice number format should not be empty!", type: "error"});
        	 $(".preview_invoice_number").trigger('focus');
        	 $("#invoice_number_validation_result").val("Invoice number format should not be empty!");
        }
	}

	function setWidthToItemTable() {
		$(".form-control.input_spinner_set_width").each(function() {
			var spinner_for = $(this).closest(".input_width_spinner").find("input[type='number']").attr('spinner_for');
			var spinner_value = $(this).val();
			$("."+spinner_for).css({width: spinner_value+"%" });
			if(spinner_for == "td_tax_rate") {
				var total = Number($("#id_iti-taxrate_width").val()) + Number($("#id_iti-taxamount_width").val());
				$(".item_table").find("th.td_cgst, th.td_sgst, th.td_igst").css({width: total+"%" });
			}
		});
	}

	$(".select_template div").click(function(){
		$(".invoice_template_default, .invoice_template_1").addClass("hide");
		if($(this).find("a").attr("id") == "template_1") {
			$(".invoice_template_default").removeClass("hide");
			$("#id_itc-template_id").val("1");
			$(".invoice_template_2").addClass("hide");
		}
		else if($(this).find("a").attr("id") == "template_2") {
			$(".invoice_template_1").removeClass("hide");
			$("#id_itc-template_id").val("2");
			$(".invoice_template_2").addClass("hide");
		}
		else if($(this).find("a").attr("id") == "template_3"){
			$(".invoice_template_2").removeClass("hide");
			$("#id_itc-template_id").val("3");
		}

		$(".select_template_container").removeClass("selected_template")
		$(this).addClass("selected_template");
		$("#template_chooser").modal("hide");
	});


	$("#template_add").submit(function(event){
		var post_url = $(this).attr("action");
		var form_data = $(this).serialize();
		if(post_url.indexOf("preview") > 0){
			event.preventDefault();
			$.ajax({
	            url : $(this).attr("action"),
				type: "POST",
			    dataType: "json",
			    data : $(this).serialize(),
	            success: function (response){
	                if(response.response_message =="Success"){
	                    $("#preview_document_container").html();
						var row1 = '<object class="document_preview_viewer" data="'+response.single_page_data+'"></object>';
						var row2 = '<object class="document_preview_viewer" data="'+response.multi_page_data+'"></object>';
		                $("#preview_document_container #tab1").html(row1);
		                $("#preview_document_container #tab2").html(row2);
						$("#preview_modal").modal("show");
						$("#single-page-invoice").trigger("click");
					}
					else{
	                    swal({title: "", text: "Something went wrong!<br> Please try again", type: "error"});
	                }
	                $("#pdf_preview").val("PDF Preview").removeAttr("disabled");
	            },
	            error: function (xhr, errmsg, err) {
	                console.log(xhr.status + ": " + xhr.responseText);
	                $("#pdf_preview").val("PDF Preview").removeAttr("disabled");
	                if(xhr.responseText.indexOf('Your session has expired') != -1) {
	                	location.reload();
                    	return;
	                }
	            }
	        });
        }
	});

	$("#pdf_preview").click(function(){
		if($(".custom-error-message").hasClass('hide') && $("#invoice_number_validation_result").val() == ''){
			$(this).val("Generating PDF...").attr("disabled", "disabled");
		}
	});

	function openNotesOverlay() {
		$("#notesEditableContainer").modal("show");
	}

	function addInvoiceNote() {
		var notes = CKEDITOR.instances.notes_editor.getData();
		$("#id_itm-notes").html(notes);
		$("#id_itm-notes").next("div").html(notes);
		$(".notes").html(notes);
		$("#notesEditableContainer").modal("hide");
	}

	function submitTemplateChanges(url){
		var is_valid = validateInvoiceNumberFormat($("#id_ith-inv_number_format").val(), "save", url);
	}

	function imageClickEvent() {
		$(".banner-image").click(function(){
      		$(this).closest(".banner-container").find("input[type='file']").click();
      	});
	}

	function applyCroppedImage() {
		$(".apply-crop").click(function(){
			var currentContainer = $(this).closest(".banner-container");
			setTimeout(function(){
				var currentBase64Value = currentContainer.find(".header-banner").val();
				var currentImageId = currentContainer.find(".header-banner").attr("id");
				currentContainer.find("img.uploaded-banner-image").attr("src", currentBase64Value).removeClass("hide");
				currentContainer.find(".hidden-attachment").val(currentBase64Value);
				currentContainer.find(".uploader").addClass("hide");
				currentContainer.find(".remove-banner-image").removeClass("hide");
				currentContainer.find(".remove-attachment").val("False");
				$("."+currentImageId).attr("src",currentBase64Value);
				if(currentImageId.indexOf("footer") >= 0) {
					var imageSize = $("#footer_banner_value").text().trim();
				}
				else {
					var imageSize = $("#header_banner_value").text().trim();
				}
				currentContainer.find(".banner-image-size").val(imageSize);
				$("."+currentImageId).css({maxHeight: imageSize+"px"});
			},100);
		});
	}

	function updateBannerImagesOnLoad() {
		$(".uploaded-banner-image").each(function () {
			if($(this).attr("src") != "") {
				var currentContainer = $(this).closest(".banner-container");
				var currentBase64Value = $(this).attr("src");
				var currentImageId = currentContainer.find(".header-banner").attr("id");
				currentContainer.find("img.uploaded-banner-image").removeClass("hide");
				currentContainer.find(".uploader").addClass("hide");
				currentContainer.find(".remove-banner-image").removeClass("hide");
				currentContainer.find(".remove-attachment").val("False");
				$("."+currentImageId).attr("src",currentBase64Value);
				if(currentImageId.indexOf("footer") >= 0) {
					var imageSize = $("#footer_banner_value").text().trim();
				}
				else {
					var imageSize = $("#header_banner_value").text().trim();
				}
				currentContainer.find(".banner-image-size").val(imageSize);
				$("."+currentImageId).css({maxHeight: imageSize+"px"});
			}
		})
	}

	function headerSliderChangeEvent() {
		var slider = document.getElementById("header_banner_size");
		var output = document.getElementById("header_banner_value");
		output.innerHTML = slider.value;
		slider.oninput = function() {
		   	output.innerHTML = this.value;
		   	$(".banner-header .banner-image-size").val(this.value);
		   	$(".header-banner-container img").css({maxHeight:this.value+"px"});
		}
	}

	function footerSliderChangeEvent() {
		var slider = document.getElementById("footer_banner_size");
		var output = document.getElementById("footer_banner_value");
		output.innerHTML = slider.value;
		slider.oninput = function() {
		   	output.innerHTML = this.value;
		   	$(".banner-footer .banner-image-size").val(this.value);
	    	$(".footer-banner-container img").css({maxHeight:this.value+"px"});
		}
	}

	function removeBannerImageInit() {
        $(".remove-banner-image").click(function(){
        	var currentContainer = $(this).closest(".banner-container");
           	currentContainer.find("img.uploaded-banner-image").removeAttr("src").addClass("hide");
           	currentContainer.find(".banner-image").removeClass("image-uploaded");
           	currentContainer.find(".uploader").removeClass("hide");
		   	currentContainer.find(".remove-banner-image").addClass("hide");
		   	currentContainer.find(".remove-attachment").val("True");
		   var currentId = $(this).next("input.header-banner").attr("id");
		   $("."+currentId).removeAttr("src");

		})
	}

	function closeCropModal(current) {
	    $(current).closest(".modal").modal("hide")
	}

	function constructDateFormat(){
		var dateFormat = "";
		var timeFormat = "";
		var dateFormatPy = "";
		var timeFormatPy = "";
		var is12HoursFormat = false;
		$(".date-formatter").each(function(){
			dateFormat += $(this).find("option:selected").attr("data-value");
			dateFormatPy += $(this).val();
		});
		$(".time-formatter").each(function(){
			var lastDataValue = $(this).find("option:selected").attr("data-value");
			if(lastDataValue !="") {
				timeFormat += lastDataValue+":";
				timeFormatPy += $(this).val();
				if(lastDataValue == "hh") {
					is12HoursFormat = true;
				}
			}
			if(timeFormatPy.indexOf(":") == 0) {
				timeFormatPy = timeFormatPy.substring(1);
			}
		});
		timeFormat = timeFormat.substring(0, timeFormat.length - 1);

		if(is12HoursFormat) {
			timeFormatPy+= " %p";
			timeFormat+= " A";
		}
		var dateTimeFormatPy = dateFormatPy.trim()+" "+timeFormatPy.trim();
		if(timeFormat == "") timeFormat = " ";
		$(".dateFormat-preview").text(moment().format(dateFormat));
		$(".timeFormat-preview").text(moment().format(timeFormat));
		$(".issue_date_time").text(moment().format(dateFormat) + " " + moment().format(timeFormat));
		$(".pdf_po_date").text(moment().format(dateFormat));
		$(".pdf_estimate_date").text(moment().format(dateFormat));
		$(".pdf_item_dc_date").text(moment().format(dateFormat) + " " + moment().format(timeFormat));
		$("#id_itg-inv_doc_datetime_format").val(dateTimeFormatPy.trim())
	}

	function constructDateFormatField(){
		var dateFormat = $("#id_itg-inv_doc_datetime_format").val();
		var format = "";
		var formattedArray = [];
		for ( var i = 0; i < dateFormat.length; i++ ) {
			format += dateFormat[i];
			if(format == "%" || format == " %" || format == ",") {
				continue;
			}
			formattedArray.push(format);
			format = "";
		}
		var list = "";
		formattedArray.forEach(function(item) {
			if(list.length < 10) {
				if(item == "%d") {
					$(".date-formatter-date").val(item);
					list += "1,"
				}
				else if(item == "%b" || item == "%B" || item == "%m") {
					$(".date-formatter-month").val(item);
					list += "3,"
				}
				else if(item == "%y" || item == "%Y") {
					$(".date-formatter-year").val(item);
					list += "5,"
				}
				else if(["/", ",", "-", ", ",".", " "].indexOf(item) >=0) {
					if(list.indexOf("2,") == -1) {
						list += "2,"
						$('.date-formatter-seperator1').val(item);
					}
					else {
						list += "4,"
						$('.date-formatter-seperator2').val(item);
					}
				}
			}
			if(item == "%H") {
				$(".time-formatter-hour").val(" %H");
			}
			if(item == "%I") {
				$(".time-formatter-hour").val(" %I");
			}
			else if(item == "%M") {
				$(".time-formatter-minute").val(":%M");
			}
			else if(item == "%S") {
				$(".time-formatter-second").val(":%S");
			}
		});
		for (var i = 1; i <= 5; i++) {
			if(list.indexOf(i) == -1) list += i+",";
		}
		var orderArray = list.split(',');
		var listArray = $('#dateFormatter td');
		for (var i = 0; i < orderArray.length; i++) {
	   		$('#dateFormatter tr').append(listArray[orderArray[i]-1]);
		}
		setTimeout(function(){
			$("#dateFormatter").sortable({
			    items: 'tr td',
			    cursor: 'pointer',
			    axis: 'x',
			    dropOnEmpty: false,
			    start: function (e, ui) {
			        ui.item.addClass("selected");
			    },
			    stop: function (e, ui) {
			       constructDateFormat();
			    }
			});
		},2000);

		$(".date-formatter, .time-formatter").change(function(){
			constructDateFormat();
		});
		constructDateFormat();
	}

	$("input[name='ith-scan_code_option']").change(function(){
		var selected_scan = $("input[name='ith-scan_code_option']:checked").val();
		if(selected_scan == "1") {
			$(".barcode").addClass("hide");
			$(".qrcode").addClass("hide");
		}
		else if(selected_scan == "2") {
			$(".barcode").addClass("hide");
			$(".qrcode").removeClass("hide");
		}
		else if(selected_scan == "3") {
			$(".barcode").removeClass("hide");
			$(".qrcode").addClass("hide");

		}
	});

	$("#id_ith-include_pono, #id_ith-include_podate").change(function(){
		if(!$("#id_ith-include_pono").is(":checked") && !$("#id_ith-include_podate").is(":checked")) {
			$(".pdf_po_no_date").addClass("hide");
		}
		else {
			$(".pdf_po_no_date").removeClass("hide");
		}
	})

	function scanCode(){
		if($("#id_ith-scan_code_option_1").is(":checked")){
			$(".qrcode").removeClass("hide");
		}
		else if($("#id_ith-scan_code_option_2").is(":checked")){
			$(".barcode").removeClass("hide");
		}
	}

	function leftContainerScrollInit() {
		var windowHeight = Number($( window ).height() - 200);
			$('.inv_template_editor').slimScroll({
			height: windowHeight,
			alwaysVisible: true,
			size : '4px'
		});
	}
</script>


{% endblock %}