"""
Django settings for ERP project - Updated for Django 4.x
"""
__author__ = 'ka<PERSON><PERSON><PERSON>'

import os
import sys
from pathlib import Path

import pymongo
from google.cloud import storage
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from dotenv import load_dotenv
import pymysql

pymysql.install_as_MySQLdb()
load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent

DEBUG = os.getenv("DEBUG", "False").lower() == "true"

# TEMPLATE_DEBUG is deprecated in Django 4.x, DEBUG is used instead

CURRENT_VERSION = os.getenv("CURRENT_VERSION")
ANDROID_APP_VERSION = os.getenv("ANDROID_APP_VERSION")
FORCE_UPDATE_ANDROID_APP = os.getenv("FORCE_UPDATE_ANDROID_APP")

PORT = int(os.getenv("MYSQL_PORT"))
HOST = os.getenv("MYSQL_HOST")
USER = os.getenv("MYSQL_USER")
PASSWORD = os.getenv("MYSQL_PASSWORD")
DBNAME = os.getenv("MYSQL_DBNAME")

MONGODB_PORT = int(os.getenv("MONGODB_PORT"))
MONGODB_SERVER = os.getenv("MONGODB_SERVER")
MONGODB_USER = os.getenv("MONGODB_USER")
MONGODB_PASSWORD = os.getenv("MONGODB_PASSWORD")
MONGODB_NAME = os.getenv("MONGODB_NAME")

CIPHER_KEY = os.getenv("CIPHER_KEY")
GST_CLIENT_ID = os.getenv("GST_CLIENT_ID")
GST_CLIENT_SECRET_KEY = os.getenv("GST_CLIENT_SECRET_KEY")
GST_EMAIL = os.getenv("GST_EMAIL")
GST_API_CLIENT_ID = os.getenv("GST_API_CLIENT_ID")
GST_API_CLIENT_SECRET_ID = os.getenv("GST_API_CLIENT_SECRET_ID")
GST_API_EMAIL = os.getenv("GST_API_EMAIL")

FTP_IP = os.getenv("FTP_IP")
FTP_USER = os.getenv("FTP_USER")
FTP_PASSWORD = os.getenv("FTP_PASSWORD")
FTP_PATH = "Documents/{path}".format(path=DBNAME)

SMTP_USER = os.getenv("SMTP_USER")
print("SMTP_USER >>>>>>>>>>",SMTP_USER)
SMTP_USER_PASSWORD = os.getenv("SMTP_USER_PASSWORD")
print("SMTP_USER_PASSWORD >>>>>>>>>>",SMTP_USER_PASSWORD)
SMTP_SERVER = os.getenv("SMTP_SERVER")
print("SMTP_SERVER >>>>>>>>>>",SMTP_SERVER)
SMTP_PORT = int(os.getenv("SMTP_PORT"))
print("SMTP_PORT >>>>>>>>>>",SMTP_PORT)

# GCS Configuration
GCS_BUCKET_NAME = os.getenv("GCS_BUCKET_NAME")
print("GCS_BUCKET_NAME >>>>>>",GCS_BUCKET_NAME)
os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = os.path.join(os.path.dirname(__file__), os.getenv("PROJECT_EXTENSION"))
GCS_PUBLIC_URL = 'https://storage.googleapis.com/{GCS_BUCKET_NAME}'.format(GCS_BUCKET_NAME=GCS_BUCKET_NAME)
print("GCS_PUBLIC_URL >>>>>>>>>>>>",GCS_PUBLIC_URL)
storage_client = storage.Client()
bucket = storage_client.bucket(GCS_BUCKET_NAME)
bucket.cors = [
			{
				"origin": ["*"],
				"responseHeader": [
					"Content-Type",
					"x-goog-resumable"],
				"method": ['PUT', 'POST'],
				"maxAgeSeconds": 5
			}
		]
bucket.patch()

# FCS Application configuration
FCS_APP_ID = os.getenv("FCS_APP_ID")
FCS_API_KEY = os.getenv("FCS_API_KEY")
FCS_PROJECT_ID = os.getenv("FCS_PROJECT_ID")

JWT_SECRET = os.getenv("JWT_SECRET")

# Payment gateway configuration
BASE_PAYMENT_URL = os.getenv("BASE_PAYMENT_URL")

BILLING_ENTERPRISE_ID = int(os.getenv("BILLING_ENTERPRISE_ID"))
BILLING_USER_ID = int(os.getenv("BILLING_USER_ID"))
SALES_LEDGER_ID = int(os.getenv("SALES_LEDGER_ID"))

ADMINS = (
	('saravanan', '<EMAIL>'),
)

MANAGERS = ADMINS

DATABASES = {
	'default': {
		'ENGINE': 'django.db.backends.mysql',
		'NAME': DBNAME,
		'USER': USER,
		'PASSWORD': PASSWORD,
		'HOST': HOST,
		'PORT': PORT,
		'OPTIONS': {
			'charset': 'utf8mb4',
			'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
		},
	}
}

MONGODB_DB = {
	'default': {
		'NAME': MONGODB_NAME,
		'USER': MONGODB_USER,
		'PASSWORD': MONGODB_PASSWORD,
		'HOST': MONGODB_SERVER,
		'PORT': MONGODB_PORT,
	}
}

# Local time zone for this installation. Choices can be found here:
# http://en.wikipedia.org/wiki/List_of_tz_zones_by_name
# although not all choices may be available on all operating systems.
# On Unix systems, a value of None will cause Django to use the same
# timezone as the operating system.
# If running in a Windows environment this must be set to the same as your
# system time zone.
TIME_ZONE = 'Asia/Kolkata'

# Language code for this installation. All choices can be found here:
# http://www.i18nguy.com/unicode/language-identifiers.html
LANGUAGE_CODE = 'en_IN'

SITE_ID = 1

# If you set this to False, Django will make some optimizations so as not
# to load the internationalization machinery.
USE_I18N = True

# If you set this to False, Django will not format dates, numbers and
# calendars according to the current locale
USE_L10N = True

# If you set this to False, Django will not use timezone-aware datetimes.
USE_TZ = False

# Absolute filesystem path to the directory that will hold user-uploaded files.
MEDIA_ROOT = BASE_DIR / 'site_media' / 'media'

# URL that handles the media served from MEDIA_ROOT. Make sure to use a
# trailing slash.
MEDIA_URL = '/site_media/media/'

ANALYTICS_CODE = os.getenv("FCS_PROJECT_ID")
FIRE_BASE_AUTH_KEY = {
	'apiKey': os.getenv("apiKey"),
	'authDomain': os.getenv("authDomain"),
	'databaseURL': os.getenv("databaseURL"),
	'projectId': os.getenv("projectId"),
	'storageBucket': os.getenv("storageBucket"),
	'messagingSenderId': int(os.getenv("messagingSenderId")),
	'appId': os.getenv("appId"),
	'measurementId': os.getenv("measurementId")
}

# Absolute path to the directory static files should be collected to.
STATIC_ROOT = BASE_DIR / 'site_media' / 'static'

# URL prefix for static files.
STATIC_URL = '/static/'

# Additional locations of static files
STATICFILES_DIRS = (
	# Put strings here, like "/home/<USER>/static" or "C:/www/django/static".
	# Always use forward slashes, even on Windows.
	# Don't forget to use absolute paths, not relative paths.
)

# List of finder classes that know how to find static files in
# various locations.
STATICFILES_FINDERS = (
	'django.contrib.staticfiles.finders.FileSystemFinder',
	'django.contrib.staticfiles.finders.AppDirectoriesFinder',
	# 'django.contrib.staticfiles.finders.DefaultStorageFinder',
)

# Make this unique, and don't share it with anybody.
SECRET_KEY = os.environ.get("SECRET_KEY", "your-secret-key-here")

# Security settings for Django 4.x
ALLOWED_HOSTS = ['*']  # Configure this properly for production

# Django 4.x uses MIDDLEWARE instead of MIDDLEWARE_CLASSES
MIDDLEWARE = [
	'django.middleware.security.SecurityMiddleware',
	'django.contrib.sessions.middleware.SessionMiddleware',
	'django.middleware.common.CommonMiddleware',
	'django.middleware.csrf.CsrfViewMiddleware',
	'django.contrib.auth.middleware.AuthenticationMiddleware',
	'django.contrib.messages.middleware.MessageMiddleware',
	'django.middleware.clickjacking.XFrameOptionsMiddleware',
	'erp.middleware.CoreMiddleware',
]

ROOT_URLCONF = 'urls'

# Django 4.x uses TEMPLATES setting instead of TEMPLATE_DIRS and TEMPLATE_CONTEXT_PROCESSORS
TEMPLATES = [
	{
		'BACKEND': 'django.template.backends.django.DjangoTemplates',
		'DIRS': [BASE_DIR / 'templates'],
		'APP_DIRS': True,
		'OPTIONS': {
			'context_processors': [
				'django.template.context_processors.debug',
				'django.template.context_processors.request',
				'django.contrib.auth.context_processors.auth',
				'django.contrib.messages.context_processors.messages',
				'django.template.context_processors.i18n',
				'django.template.context_processors.media',
				'django.template.context_processors.static',
			],
		},
	},
]

TEMP_DIR = BASE_DIR / 'site_media' / 'tmp'
FILE_DIRS = (BASE_DIR / 'site_media' / 'upload',)

INSTALLED_APPS = (
	'django.contrib.humanize',
	'django.contrib.auth',
	'django.contrib.contenttypes',
	'django.contrib.sessions',
	'django.contrib.sites',
	'django.contrib.messages',
	'django.contrib.staticfiles',
	'erp',

	# 'cronjobs',
	# Uncomment the next line to enable the admin:
	# 'django.contrib.admin',
	# Uncomment the next line to enable admin documentation:
	# 'django.contrib.admindocs'
)

# A sample logging configuration. The only tangible logging
# performed by this configuration is to send an email to
# the site admins on every HTTP 500 error.
# See http://docs.djangoproject.com/en/dev/topics/logging for
# more details on how to customize your logging configuration.
LOGGING = {
	'version': 1,
	'disable_existing_loggers': True,
	'formatters': {
		'verbose': {
			'format': '[%(levelname)s] %(asctime)s <%(name)s.%(module)s> %(message)s'
		},
	},
	'handlers': {
		'console': {
			'level': 'DEBUG',
			'class': 'logging.StreamHandler',
			'formatter': 'verbose',
			'stream': sys.stdout
		},
		'file': {
			'level': 'INFO',
			'class': 'logging.handlers.TimedRotatingFileHandler',
			'filename': os.getenv("LOGGING_FILENAME"),
			'when': 'W0',
			'formatter': 'verbose'
		},
	},
	'root': {
		'handlers': ['console', 'file'],
		'level': 'INFO',
	}
}

# Firebase Cloud messaging api key
FCM_API_KEY = os.getenv("FCM_API_KEY")

SESSION_EXPIRE_AT_BROWSER_CLOSE = True
SESSION_COOKIE_AGE = 30 * 60
SESSION_SERIALIZER = 'django.contrib.sessions.serializers.PickleSerializer'

ENGINE = create_engine(
	'mysql+pymysql://{USER}:{PASSWORD}@{HOST}:{PORT}/{NAME}?charset=utf8mb4'.format(
		**DATABASES['default']),
	echo=True, pool_recycle=3600, isolation_level="READ_COMMITTED")
ORMSessionFactory = sessionmaker(bind=ENGINE, autoflush=False)
SQLASession = scoped_session(ORMSessionFactory)

MONGODB_CONNECTION_URL = "mongodb://{USER}:{PASSWORD}@{HOST}:{PORT}/{NAME}".format(**MONGODB_DB['default'])
MongoDbConnect = pymongo.MongoClient(MONGODB_CONNECTION_URL)[MONGODB_DB['default']['NAME']]

XSASSIST_MAIL_ID = ("<EMAIL>",)

# REDIS CONF

BROKER_URL = os.getenv("REDIS_BROKER_URL")  # Redis as message broker
CELERY_RESULT_BACKEND = os.getenv("REDIS_CELERY_RESULT_BACKEND")  # Redis as result backend
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'

# Default primary key field type for Django 4.x
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
