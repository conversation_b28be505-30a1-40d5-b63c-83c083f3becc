{% extends "accounts/sidebar.html" %}
{% block accounts_dashboard %}
	<style>
		@media (min-width: 564px) {
			.daterangepicker .ranges ul {
				width: 125px; 
			}
		}	

		.table_ledger_name {
			max-width: 250px;
			word-wrap: break-word;
		}

		.col-md-2_4 {
			width: 20%;
		}

	</style>
	<link type="text/css" rel="stylesheet" href="/site_media/css/account.css?v={{ current_version }}">
	<link type="text/css" rel="stylesheet" href="/site_media/css/Fonts.css?v={{ current_version }}">

	<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/jquery.slimscroll.js?v={{ current_version }}"></script>

	{% load humanize %}
	<div class="right-content-container col-sm-12">
		<div class="page-title-container">
			<span class="page-title">Dashboard</span>
		</div>
		<div class="container" style="margin-top: 20px;">
			<div class="header-container">
				<div class="row">
					<div class="col-sm-6 col-md-3 col-md-2_4">
						<div class="header-box header-box-1">
							<div class="header-box-value">
								<i class="fa fa-inr fa-black fa-5" aria-hidden="true"></i>
								<span id="bank_balance_total_card">loading...</span> <br>
							</div>
							<div class="header-box-text">
								Bank Balance
							</div>
						</div>
					</div>
					<div class="col-sm-6 col-md-3 col-md-2_4">
						<div class="header-box header-box-5">
							<div class="header-box-value">
								<i class="fa fa-inr fa-black fa-5" aria-hidden="true"></i>
								<span id="cash_in_hand_total_card">loading...</span>
							</div>
							<div class="header-box-text">
								Cash in Hand
							</div>
						</div>
					</div>
					<div class="col-sm-6 col-md-3 col-md-2_4">
						<div class="header-box header-box-2">
							<div class="header-box-value">
								<i class="fa fa-inr fa-black fa-5" aria-hidden="true"></i>
								<span id="receivable_total">loading...</span>
							</div>
							<div class="header-box-text">
								Receivables
							</div>
						</div>
					</div>
					<div class="col-sm-6 col-md-3 col-md-2_4">
						<div class="header-box header-box-3">
							<div class="header-box-value">
								<i class="fa fa-inr fa-black fa-5" aria-hidden="true"></i>
								<span id="payable_total">loading...</span>
							</div>
							<div class="header-box-text">
								Payables
							</div>
						</div>
					</div>
					<div class="col-sm-6 col-md-3 col-md-2_4">
						<div class="header-box header-box-4">
							<div class="header-box-value">
								<i class="fa fa-inr fa-black fa-5" aria-hidden="true"></i>
								<span id="salesrevenue_total">loading...</span>
							</div>
							<span style="font-size: 10px; position: absolute; top: 67px; left: 74px;">(Since {{ fiscal_start_date|date:'M d, Y'}})</span>
							<div class="header-box-text">
								Sales Revenue
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="invoice-container">
				<div class="row">
					<div class="col-sm-6">
						<h3>Receivables</h3>
						<div class="invoice-box bills">
							<div class="invoice-box-content" id="receivable_advance" role="button">
                                <span class="invoice-box-text">
                                    {{ 'Advances' }}
                                </span>
								<span class="invoice-box-value">
                                    <i class="fa fa-inr fa-black fa-4"
                                       aria-hidden="true"></i> <span id="receivable_aging_advance">loading...</span>
                                </span>
								<input type="hidden" id="receivable_advance_particulars"
								       value="">
							</div>
							<div class="invoice-box-content" id="receivable_billable" role="button">
                                <span class="invoice-box-text">
                                    {{ 'Un-Billed' }}
                                </span>
								<span class="invoice-box-value">
                                    <i class="fa fa-inr fa-black fa-4"
                                       aria-hidden="true"></i> <span id="receivable_aging_billable">loading...</span>
                                </span>
								<input type="hidden" id="receivable_billable_particulars"
								       value="">
							</div>
							<div class="invoice-box-content box-clickable"
							     onclick="getAgeingDetails(null, true, 'ledger_bills', 0, 0, true);">
                                        <span class="invoice-box-text">
                                            {{ 'Excess Received' }}
                            			</span>
								<span class="invoice-box-value">
                                            <i class="fa fa-inr fa-black fa-4"
                                               aria-hidden="true"></i> <span id="receivable_aging_excess">loading...</span>
                                        </span>
							</div>
							<div class="invoice-box-content box-clickable"
							     onclick="getAgeingDetails(null, true, 'ledger_bills', 0, 30, false);">
                                        <span class="invoice-box-text">
                                            {{ '< 30 Days' }}
                            			</span>
								<span class="invoice-box-value">
                                            <i class="fa fa-inr fa-black fa-4"
                                               aria-hidden="true"></i> <span id="receivable_aging_age1">loading...</span>
                                        </span>
							</div>
							<div class="invoice-box-content box-clickable"
							     onclick="getAgeingDetails(null, true, 'ledger_bills', 30, 60, false);">
                                        <span class="invoice-box-text">
                                            {{ '30 - 60 Days' }}
                            			</span>
								<span class="invoice-box-value">
                                            <i class="fa fa-inr fa-black fa-4"
                                               aria-hidden="true"></i> <span id="receivable_aging_age2">loading...</span>
                                        </span>
							</div>
							<div class="invoice-box-content box-clickable"
							     onclick="getAgeingDetails(null, true, 'ledger_bills', 60, 90, false);">
                                        <span class="invoice-box-text">
                                            {{ '60 - 90 Days' }}
                            			</span>
								<span class="invoice-box-value">
                                            <i class="fa fa-inr fa-black fa-4"
                                               aria-hidden="true"></i> <span id="receivable_aging_age3">loading...</span>
                                        </span>
							</div>
							<div class="invoice-box-content box-clickable"
							     onclick="getAgeingDetails(null, true, 'ledger_bills', 90, 0, false);">
                                        <span class="invoice-box-text">
                                            {{ '> 90 Days' }}
                            			</span>
								<span class="invoice-box-value">
                                            <i class="fa fa-inr fa-black fa-4"
                                               aria-hidden="true"></i> <span id="receivable_aging_age4">loading...</span>
                                        </span>
							</div>
							<div class="invoice-box-content box-clickable_2" onclick="getAgingByTotalDue(true)">
								<span class="invoice-box-text total" >
									Total Due
								</span>
								<span class="invoice-box-value total">
									<i class="fa fa-inr fa-black fa-4"
									   aria-hidden="true"></i> <span id="receivable_aging_total">loading...</span>
								</span>
							</div>
						</div>
						<br>
						<div class="invoice-box bills">
							<div class="invoice-box-content box-clickable_2" onclick="getAgingByDetailedView(true,'overdue',0,0,true)">
								<span class="invoice-box-text overdue">
									Overdue
								</span>
								<span class="invoice-box-value overdue">
									<i class="fa fa-inr fa-black fa-4"
									   aria-hidden="true"></i> <span id="receivable_aging_overdue">loading...</span>
								</span>
							</div>
							<div class="invoice-box-content box-clickable_2" onclick="getAgingByDetailedView(true,'due_in_days',0,0,true)">
								<span class="invoice-box-text">
									Due in Next 10 Days
								</span>
								<span class="invoice-box-value">
									<i class="fa fa-inr fa-black fa-4"
									   aria-hidden="true"></i> <span id="receivable_aging_age5">loading...</span>
								</span>
							</div>
						</div>
					</div>
					<div class="col-sm-6">
						<h3>Payables</h3>
						<div class="invoice-box invoice">
							<div class="invoice-box-content" id="payable_advance" role="button">
                                <span class="invoice-box-text">
                                    {{ 'Advances' }}
                            	</span>
								<span class="invoice-box-value">
                                    <i class="fa fa-inr fa-black fa-4"
                                       aria-hidden="true"></i> <span id="payable_aging_advance">loading...</span>
                                </span>
								<input type="hidden" id="payable_advance_particulars"
								       value="">
							</div>
							<div class="invoice-box-content" id="payable_billable" role="button">
                                <span class="invoice-box-text">
                                    {{ 'Un-Billed' }}
                            	</span>
								<span class="invoice-box-value">
                                    <i class="fa fa-inr fa-black fa-4"
                                       aria-hidden="true"></i> <span id="payable_aging_billable">loading...</span>
                                </span>
								<input type="hidden" id="payable_billable_particulars"
								       value="">
							</div>
							<div class="invoice-box-content box-clickable"
							     onclick="getAgeingDetails(null, false, 'ledger_bills', 0, 0, true);">
                                <span class="invoice-box-text">
                                    {{ 'Excess Paid' }}
                            	</span>
								<span class="invoice-box-value">
                                    <i class="fa fa-inr fa-black fa-4"
                                       aria-hidden="true"></i> <span id="payable_aging_excess">loading...</span>
                                </span>
							</div>
							<div class="invoice-box-content box-clickable"
							     onclick="getAgeingDetails(null, false, 'ledger_bills', 0, 30, false);">
                                <span class="invoice-box-text">
                                    {{ '< 30 Days' }}
                            	</span>
								<span class="invoice-box-value">
                                    <i class="fa fa-inr fa-black fa-4"
                                       aria-hidden="true"></i> <span id="payable_aging_age1">loading...</span>
                                </span>
							</div>
							<div class="invoice-box-content box-clickable"
							     onclick="getAgeingDetails(null, false, 'ledger_bills', 30, 60, false);">
                                        <span class="invoice-box-text">
                                            {{ '30 - 60 Days' }}
                            			</span>
								<span class="invoice-box-value">
                                            <i class="fa fa-inr fa-black fa-4"
                                               aria-hidden="true"></i> <span id="payable_aging_age2">loading...</span>
                                        </span>
							</div>
							<div class="invoice-box-content box-clickable"
							     onclick="getAgeingDetails(null, false, 'ledger_bills', 60, 90, false);">
                                        <span class="invoice-box-text">
                                            {{ '60 - 90 Days' }}
                            			</span>
								<span class="invoice-box-value">
                                            <i class="fa fa-inr fa-black fa-4"
                                               aria-hidden="true"></i> <span id="payable_aging_age3">loading...</span>
                                        </span>
							</div>
							<div class="invoice-box-content box-clickable"
							     onclick="getAgeingDetails(null, false, 'ledger_bills', 90, 0, false);">
                                        <span class="invoice-box-text">
                                            {{ '> 90 Days' }}
                            			</span>
								<span class="invoice-box-value">
                                            <i class="fa fa-inr fa-black fa-4"
                                               aria-hidden="true"></i> <span id="payable_aging_age4">loading...</span>
                                        </span>
							</div>
							<div class="invoice-box-content box-clickable_2" onclick="getAgingByTotalDue(false)">
								<span class="invoice-box-text total">
									Total Due
								</span>
								<span class="invoice-box-value total">
									<i class="fa fa-inr fa-black fa-4"
									   aria-hidden="true"></i> <span id="payable_aging_total">loading...</span>
								</span>
							</div>
						</div>
						<br>
						<div class="invoice-box invoice">
							<div class="invoice-box-content box-clickable_2" onclick="getAgingByDetailedView(false,'overdue',0,0,true)">
								<span class="invoice-box-text overdue">
									Overdue
								</span>
								<span class="invoice-box-value overdue">
									<i class="fa fa-inr fa-black fa-4"
									   aria-hidden="true"></i> <span id="payable_aging_overdue">loading...</span>
								</span>
							</div>
							<div class="invoice-box-content box-clickable_2" onclick="getAgingByDetailedView(false,'due_in_days',0,0,true)">
								<span class="invoice-box-text">
									Due in Next 10 Days
								</span>
								<span class="invoice-box-value">
									<i class="fa fa-inr fa-black fa-4"
									   aria-hidden="true"></i> <span id="payable_aging_age5">loading...</span>
								</span>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="accounts-container">
				<div class="row">
					<div class="col-sm-6" style="height:400px">
						<div class="account-box scroll-container">
							<table class="table table-bordered" id="tbl_cashinhand">
								<tr class="account-table-header">
									<th colspan="2">
										<span class="account-header-text">Cash In Hand</span>
										<span class="account-header-value" style="padding-right: 12px;">
											<i class="fa fa-inr fa-black fa-4" style="font-size: 18px;"></i>
                                          <span id="cash_in_hand_total">loading...</span>
                                        </span>
									</th>
								</tr>
								<tr>
									<th class="text-center">LEDGER</th>
									<th class="text-center">BALANCE</th>
								</tr>
							</table>
						</div>
					</div>
					<div class="col-sm-6">
						<div class="account-box scroll-container">
							<table class="table table-bordered" id="tbl_bankbalance">
								<tr class="account-table-header">
									<th colspan="2">
										<span class="account-header-text">Bank Balance</span>
										<span class="account-header-value" style="padding-right: 12px;">
											<i class="fa fa-inr fa-black fa-4" style="font-size: 18px;"></i>
                                            <span id="bank_balance_total">loading...</span>
                                        </span>
									</th>
								</tr>
								<tr>
									<th class="text-center">LEDGER</th>
									<th class="text-center">BALANCE</th>
								</tr>
							</table>
						</div>
					</div>
					<div class="col-sm-12">
						<h3>Cash & Bank Balance</h3>
					</div>
					<div class="col-sm-12">
						<div id="chart_div" class="text-center" style="height:400px;">
							<div id='loadingmessage1_ie' style="margin-top:150px">
								<img src='{{ MEDIA_URL }}../images/ajax-loader.gif'/>
								<br /> Please wait...
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="revenue-container">
				<div class="row">
					<div class="col-sm-12"><h3>Sales Revenue</h3></div>
					<div class="col-sm-6">
						<div id="chart_div_1" class="text-center" style="height:400px">
							<div id='loadingmessage2_ie' style="margin-top:150px">
								<img src='{{ MEDIA_URL }}../images/ajax-loader.gif'/>
								<br /> Please wait...
							</div>
						</div>
					</div>
					<div class="col-sm-6 remove-padding">
						<div class="col-md-12" style="margin-bottom: 10px;">
							<label>Date Range</label>
							<div id="reportrange" class="sales_revenue_report report-range form-control">
								<i class="glyphicon glyphicon-calendar"></i>&nbsp;
								<span></span> <b class="caret"></b>
								<input type="hidden" class="fromdate" id="fromdate" name="fromdate"/>
								<input type="hidden" class="todate" id="todate" name="todate"/>
							</div>
						</div>
						<div class="col-md-12">
						<div class="revenue-box scroll-container">
						<table class="table table-bordered">
							<thead style="background-color:#fff">
							<tr id="revenue-table-header">
								<th colspan="3">
								<span class="revenue-header-text">Top 8 Customers</span>
								<span class="revenue-header-value" style="padding-right: 12px;">
									<i class="fa fa-inr fa-black fa-4" style="font-size: 18px;"></i> 
									<span id="sales_top_customers_total"></span>
								</span>
							</th>

							</tr>
							</thead>
							<tbody id="top_customers_list">


							</tbody>
							<tfoot>
							<tr>
								<td colspan="4" class="text-right"><a data-toggle="modal"
								                                      data-target="#top_customers_model" role="button">Show
								                                                                                         All</a>
								</td>
							</tr>
							</tfoot>
						</table>
					</div>
					</div>

					</div>
				</div>
			</div>
			<div class="revenue-container">
				<div class="row">
					<div class="col-sm-12">
						<h3>Direct Income & Expenses</h3>
						<div id="chart_div_2" class="text-center" style="height:400px">
							<div id='loadingmessage3_ie' style="margin-top:150px">
							<img src='{{ MEDIA_URL }}../images/ajax-loader.gif'/>
								<br /> Please wait...
						</div>
						</div>
					</div>
				</div>
			</div>
		</div>
        <input type="hidden" id="incomeandexpenses" value="">
		<input type="hidden" id="get" value="">
		<!-- Bill modals and JS are segregated -->
		{% include "accounts/bills.html" %}
		<div id="top_customers_model" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Top Customers</h4>
			</div>
			<div class="modal-body">
				<div class="col-md-12">
					<div class="revenue-box">
						<table class="table table-bordered">
							<thead>
								<tr id="revenue-table-header">
									<th colspan="3">
									<span class="revenue-header-text">Top Order Customers List</span>
									<span class="revenue-header-value" style="padding-right: 12px;">
										<i class="fa fa-inr fa-black fa-4" style="font-size: 18px;"></i>
										<span id="sales_full_customers_total"></span>
									</span>
								</th>
							</thead>
							<tbody id="full_customers_list"></tbody>
						</table>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>
	</div>
	<script type="text/javascript" src="/site_media/js/loader.js?v={{ current_version }}"></script>

	<script type="text/javascript">
        $(document).ready(function () {
			localStorage.removeItem('receivable_totaldue');
			localStorage.removeItem('payable_totaldue');
            SalesRevenueReport();
            ResponsiveFontSize();
            getSalesRevenueTotal();
            getReceivablePayableTotal();
            getCashInHand();
            getBankBalance();
			DrawIncomeExpence();
			getReceivableAgingDetails();
			getPayableAgingDetails();
			getTotalDueConstruct(true);
			getTotalDueConstruct(false);

            $('.nav-pills li').removeClass('active');
			$('#li_ac_dashboard').addClass('active');
			$('.scroll-container').slimScroll({
				height: "380px",
				alwaysVisible: true
			});
        });
        $(".datepicker").datepicker({
            dateFormat: "dd-M-yy",
        });
        $("#clear-sync").click( function(e){
		    e.preventDefault();
		    Cookies.remove('dirIExp');
		    Cookies.remove('dirIExpsyncDateTime');
		    location.reload();
		});


		function getCashInHand() {
	        $.ajax({
		        url: '/erp/accounts/json/get_cash_in_hand/',
		        type: "POST",
		        dataType: "json",
		        data: {type:"cash_in_hand"},
		        success: function (json) {
		            row = ""
			        if(json.response_code = 200){
			            $("#cash_in_hand_total, #cash_in_hand_total_card").html(json.data[0].toFixed(2));
				        $.each(json.data[1], function( index, value ) {
						  row += 	`<tr>
					                 	<td class="text-left">${value.name}</td>
					                 	<td class="text-right" style="padding-right: 12px;">
					                 		<i class="fa fa-inr fa-black fa-4" aria-hidden="true"></i>
					                 		${value.closing_balance}
									 	</td>
									</tr>`;
						});
				        $("#tbl_cashinhand").find('tbody').append(row);
			        }
			        else {
			        	console.log("Cash in Hand fetch not working...");
			        }
		        }
		    });
        }

        function getBankBalance() {
	        $.ajax({
		        url: '/erp/accounts/json/get_bank_balance/',
		        type: "POST",
		        dataType: "json",
		        data: { type:"bank_balance"},
		        success: function (json) {
		            row = ""
			        if(json.response_code = 200){
			            $("#bank_balance_total, #bank_balance_total_card").html(json.data[0].toFixed(2));
				        $.each(json.data[1], function( index, value ) {
						  row += 	`<tr>
				                 		<td class="text-left">${value.name}</td>
				                 		<td class="text-right" style="padding-right: 12px;">
				                 			<i class="fa fa-inr fa-black fa-4" aria-hidden="true"></i>
											${value.closing_balance}
								 		</td>
									</tr>`;
						});
				        $("#tbl_bankbalance").find('tbody').append(row);
			        }
			        else {
			        	console.log("Cash in Hand fetch not working...");
			        }
		        }
		    });
        }

		function getReceivableAgingDetails() {
	        $.ajax({
		        url: '/erp/accounts/json/get_receivable_aging_details/',
		        type: "POST",
		        dataType: "json",
		        data: {},
		        success: function (json) {
		            $("#receivable_aging_advance").text(json.receivable_aging.advance.toFixed(2));
		            $("#receivable_advance_particulars").val(json.receivable_aging.advance_particulars);
		            $("#receivable_aging_billable").text(json.receivable_aging.billable.toFixed(2));
		            $("#receivable_billable_particulars").val(json.receivable_aging.billable_particulars);
		            $("#receivable_aging_excess").text(json.receivable_aging.excess.toFixed(2));
		            $("#receivable_aging_age1").text(json.receivable_aging.age1.toFixed(2));
		            $("#receivable_aging_age2").text(json.receivable_aging.age2.toFixed(2));
		            $("#receivable_aging_age3").text(json.receivable_aging.age3.toFixed(2));
		            $("#receivable_aging_age4").text(json.receivable_aging.age4.toFixed(2));
		            $("#receivable_aging_age5").text(json.receivable_aging.age5.toFixed(2));
		            $("#receivable_aging_total").text(json.receivable_aging.total.toFixed(2));
		            $("#receivable_aging_overdue").text(json.receivable_aging.overdue.toFixed(2));

		        }
		    });
        }

        function getSalesRevenueTotal() {
	        $.ajax({
		        url: '/erp/accounts/json/get_sales_revenue_total/',
		        type: "POST",
		        dataType: "json",
		        data: {},
		        success: function (json) {
		        if(json.response_code = 200){
			            $("#salesrevenue_total").html(json.data.toFixed(2));
			        }else{
			        console.log("sales revenue fetch not working...");
			        }
		        }
		    });
        }

        function getReceivablePayableTotal() {
	        $.ajax({
		        url: '/erp/accounts/json/dashboard_api/',
		        type: "POST",
		        dataType: "json",
		        data: {'enterprise_id': $('#enterprise_id').val()},
		        success: function (json) {
		        if(json.response_code = 200){
			            $("#receivable_total").html(json.receivable.toFixed(2));
			            $("#payable_total").html(json.payable.toFixed(2));
			        }else{
			        console.log("payable receivable total fetch not working...");
			        }
		        }
		    });
        }

        function getPayableAgingDetails() {
	        $.ajax({
		        url: '/erp/accounts/json/get_payable_aging_details/',
		        type: "POST",
		        dataType: "json",
		        data: {},
		        success: function (json) {
		            $("#payable_aging_advance").text(json.payable_aging.advance.toFixed(2));
		            $("#payable_advance_particulars").val(json.payable_aging.advance_particulars);
		            $("#payable_aging_billable").text(json.payable_aging.billable.toFixed(2));
		            $("#payable_billable_particulars").val(json.payable_aging.billable_particulars);
		            $("#payable_aging_excess").text(json.payable_aging.excess.toFixed(2));
		            $("#payable_aging_age1").text(json.payable_aging.age1.toFixed(2));
		            $("#payable_aging_age2").text(json.payable_aging.age2.toFixed(2));
		            $("#payable_aging_age3").text(json.payable_aging.age3.toFixed(2));
		            $("#payable_aging_age4").text(json.payable_aging.age4.toFixed(2));
		            $("#payable_aging_age5").text(json.payable_aging.age5.toFixed(2));
		            $("#payable_aging_total").text(json.payable_aging.total.toFixed(2));
		            $("#payable_aging_overdue").text(json.payable_aging.overdue.toFixed(2));
		        }
		    });
        }

        function getTopCustomers(since, till) {
            function getCookie(name) {
                var value = "; " + document.cookie;
                var parts = value.split("; " + name + "=");
                if (parts.length == 2) return parts.pop().split(";").shift();
            }

            $.ajaxSetup({
                headers: {"X-CSRFToken": getCookie('csrftoken')}
            });
            $.ajax({
                url: 'erp/accounts/json/top_customers/',
                type: "POST",
                data: {since: since, till: till},
                 beforeSend: function () {
                    $("#loadingmessage4_ie").show();
                },
                success: function (json) {
                     var top_customers_list_html = full_customers_list_html = '';
            var top_customers_total = full_customers_total = 0;
            document.getElementById("top_customers_list").innerHTML = "";
            if (isEmpty(json['sales_top_customers'])) {
                top_customers_list_html = "<tr>No data found</tr>";
            } else {
                for (i = 0, len = json['sales_top_customers'].length; i < len; i++) {
                if(i <= 7){
                 top_customers_list_html += '<tr>\
												<td class="text-left">' + json['sales_top_customers'][i].name + '</td>\
												<td class="text-right"><i class="fa fa-inr fa-black fa-4" aria-hidden="true"></i> ' + json['sales_top_customers'][i].closing_balance + '</td>\
												<td class="text-center"  >' + ((json['sales_top_customers'][i].closing_balance /json['sales_top_customers_total']) *100).toFixed(2) + '%</td>\
											</tr>';
					top_customers_total += json['sales_top_customers'][i].closing_balance;
					}
					full_customers_list_html += '<tr>\
												<td class="text-left">' + json['sales_top_customers'][i].name + '</td>\
												<td class="text-right"><i class="fa fa-inr fa-black fa-4" aria-hidden="true"></i> ' + json['sales_top_customers'][i].closing_balance + '</td>\
												<td class="text-center"  >' + ((json['sales_top_customers'][i].closing_balance /json['sales_top_customers_total']) *100).toFixed(2) + '%</td>\
											</tr>';
					full_customers_total += json['sales_top_customers'][i].closing_balance;

				}
            }
            document.getElementById("top_customers_list").innerHTML = top_customers_list_html;
            document.getElementById("full_customers_list").innerHTML = full_customers_list_html;
            document.getElementById("sales_top_customers_total").innerHTML = top_customers_total.toFixed(2);
            document.getElementById("sales_full_customers_total").innerHTML = full_customers_total;
                    $('.scroll-container').slimScroll({
						height: "380px",
						alwaysVisible: true
					});

                },
                error: function (xhr, errmsg, err) {
                    console.log(xhr.status + ": " + xhr.responseText);
                    console.log(xhr.status + ": " + xhr.responseText);
                }
            });

        }

        function getURLParameter(url, name) {
            return (RegExp(name + '=' + '(.+?)(&|$)').exec(url) || [, null])[1];
        }

        google.charts.load('current', {'packages': ['line', 'corechart']});
        google.charts.setOnLoadCallback(DrawCashBalance);

        function DrawCashBalance() {
	        $.ajax({
		        url: '/erp/accounts/json/get_cash_balance/',
		        type: "POST",
		        dataType: "json",
		        data: {},
		        beforeSend: function () {
                    $("#loadingmessage1_ie").show();
                },
		        success: function (json) {
		            $("#loadingmessage1_ie").hide();
		            var data = google.visualization.arrayToDataTable(json);
		            var options = {
		                seriesType: 'line',
		                legend: {position: 'right'},
		                width: '100%',
		                height: 400,
		                series: {0: {lineWidth: 6}},
		                'chartArea':{left:70, top:10, width:"70%", height: "90%"},
		                vAxis: {format: "₹#,##,###"}
		            };
		            var chart = new google.visualization.ComboChart(document.getElementById('chart_div'));
		            chart.draw(data, options);

		        }
		    });
        }

        google.charts.load('current', {'packages': ['corechart']});
        google.charts.setOnLoadCallback(DrawSalesRevenue);

        function DrawSalesRevenue() {
            $.ajax({
		        url: '/erp/accounts/json/get_sales_revenue/',
		        type: "POST",
		        dataType: "json",
		        data: {},
		        beforeSend: function () {
                    $("#loadingmessage2_ie").show();
                },
		        success: function (json) {
		            $("#loadingmessage2_ie").hide();
		            var data = google.visualization.arrayToDataTable(json);
		            var options = {
		                seriesType: 'bars',
		                legend: {position: 'bottom'},
		                series: {1: {type: 'line'}},
		                width: '100%',
		                height: 400,
		                'chartArea': {'width': '80%', 'height': '75%'},
		                colors: ['#efad4d', 'green'],
		                vAxis: {format: "₹#,##,###"}
		            };

		            var chart = new google.visualization.ComboChart(document.getElementById('chart_div_1'));
		            chart.draw(data, options);

		        }
		    });
        }

        function diExp() {
            ResponsiveFontSize();
            var json  = JSON.parse($("#incomeandexpenses").val());
            var data = google.visualization.arrayToDataTable(json);
            var options = {
                seriesType: 'bars',
                legend: {position: 'bottom'},
                width: '100%',
                height: 400,
                'chartArea': {'width': '85%', 'height': '75%'},
                colors: ['green', 'red'],
                vAxis: {format: "₹#,##,###"}
            };
            $('#loadingmessage3_ie').hide();
            var chart = new google.visualization.ComboChart(document.getElementById('chart_div_2'));
            chart.draw(data, options);
        }


        function DrawIncomeExpence() {
            $.ajaxSetup({
                headers: {"X-CSRFToken": getCookie('csrftoken')}
            });
            $.ajax({
                url: 'erp/accounts/json/income_and_expenses/',
                type: "POST",
                data: {},
                beforeSend: function () {
                    $("#loadingmessage3_ie").show();
                },
                success: function (response) {
                    var arr = [["Month","Income","Expense"]];
                    for (let i=0;i<response['income_and_expenses'].length; i++) {
                        temp=[];
                        temp.push(response['income_and_expenses'][i]['month']);
					    temp.push(response['income_and_expenses'][i]['income']);
						temp.push(response['income_and_expenses'][i]['expense']);
						arr.push(temp);
					}
                    document.getElementById('incomeandexpenses').value= JSON.stringify(arr);
                    google.charts.load('current', {'packages': ['corechart']});
                    google.charts.setOnLoadCallback(diExp);
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(xhr.status + ": " + xhr.responseText);
                    console.log(xhr.status + ": " + xhr.responseText);
                }
            });
        }

        function ResponsiveFontSize() {
            $(".header-box-value").each(function () {
                if ($(this).text().trim().length > 14) {
                    $(this).css({fontSize: "18px"});
                }
                else if ($(this).text().trim().length > 12) {
                    $(this).css({fontSize: "22px"});
                }
            });
        }
		
		function SalesRevenueReport(){
			$('.sales_revenue_report').on('hide.daterangepicker', function(ev, picker) {
				setTimeout(function(){
					getTopCustomers($(".fromdate").val(),$(".todate").val());
				},100);
			});
		}
		
		$(window).load(function(){
			setTimeout(function(){
				getTopCustomers($(".fromdate").val(),$(".todate").val());
			}, 100);
			$("#loading").hide();
		});
		$("#receivable_advance").click(function(){
		    if ($("#receivable_advance_particulars").val().length > 0){
		        populateAllUnsettledParticulars(JSON.parse($("#receivable_advance_particulars").val()), "Advance Received");
		    }else{
		        swal("", "No Records Found!", "warning");
		    }
            ga('send', 'event', 'Accounts', 'Dashboard - view Receivable Ageing details', $('#enterprise_label').val(), 1);
		});
		$("#receivable_billable").click(function(){
		    if ($("#receivable_advance_particulars").val().length > 0){
				populateAllUnsettledParticulars(JSON.parse($("#receivable_billable_particulars").val()), "Unbilled Outstanding");
		    }else{
		        swal("", "No Records Found!", "warning");
		    }
            ga('send', 'event', 'Accounts', 'Dashboard - view Receivable Un-billed details', $('#enterprise_label').val(), 1);
		});
		$("#payable_advance").click(function(){
		    if ($("#receivable_advance_particulars").val().length > 0){
				populateAllUnsettledParticulars(JSON.parse($("#payable_advance_particulars").val()), "Advances Paid");
		    }else{
		        swal("", "No Records Found!", "warning");
		    }
            ga('send', 'event', 'Accounts', 'Dashboard - view Payable Ageing details', $('#enterprise_label').val(), 1);
		});
		$("#payable_billable").click(function(){
		    if ($("#receivable_advance_particulars").val().length > 0){
				populateAllUnsettledParticulars(JSON.parse($("#payable_billable_particulars").val()), "Unbilled Outstanding");
		    }else{
		        swal("", "No Records Found!", "warning");
		    }
            ga('send', 'event', 'Accounts', 'Dashboard - view Payable Un-billed details', $('#enterprise_label').val(), 1);
		});
    </script>
{% endblock %}