<div id="add_material_modal" class="modal fade"  role="dialog">
	<div class="modal-dialog modal-md">
	  	<div class="modal-content">
        	<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Add New Items</h4>
			</div>
			<form action="/erp/masters/catalogues/save/?add_material_ajax=True" method="post" id="edit_catalogue_form">
				{% csrf_token %}
		  		<div class="modal-body">
					{% include "masters/add_material_react_form.html" %}
					{% include "masters/add_material_supporting_modal.html" %}
				</div>
				<div class="modal-footer">
					<span class="general-warning pull-left" style="padding: 8px 15px; margin: 0;">
						<u>Note:</u> <span class="service_add_new_material">add new Category/ Unit, go to Profile -> Goods Page.</span>
					</span>
					<div class="material_txt">
						{% if logged_in_user|canEdit:'MASTERS' %}
							<a href="#" id="save-material" class="btn btn-save" >Save</a>
						{% endif %}
				        <!-- <input type="button" hidden="hidden" id="saveCatalogue" value="Save"> -->
						<a data-dismiss="modal" class="btn btn-cancel">Cancel</a>
					</div>
				</div>
			</form>
	  	</div>
	</div>
</div>