"""
"""
import datetime
import json
import os

from dateutil.relativedelta import relativedelta
from django.http import HttpResponse
from django.utils.encoding import smart_str

from erp.auth import SESSION_KEY, ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from erp.hr import logger
from erp.hr.backend import HrService
from settings import TEMP_DIR
from util import helper
from util.api_util import response_code

__author__ = 'nandha'


def importEmployees(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		logger.info("Importing employee records...")
		hr_service = HrService()
		filename = rh.getPostData('filename')
		base64data = rh.getPostData('base64data')
		temp_filename = TEMP_DIR + "/" + hr_service.storeFileInTemp(
			enterprise_id=enterprise_id, filename=filename, base64data=base64data)
		response = hr_service.importEmployees(enterprise_id=enterprise_id, filename=temp_filename)
		if os.path.exists(temp_filename):
			os.remove(temp_filename)
	except Exception as e:
		logger.exception("Import failed %s" % e.message)
		response = response_code.internalError()
		response['custom_message'] = "Import failed!"
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def importEmployeePayStructure(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		logger.info("Importing employee pay structure records...")
		hr_service = HrService()
		filename = rh.getPostData('filename')
		base64data = rh.getPostData('base64data')
		temp_filename = TEMP_DIR + "/" + hr_service.storeFileInTemp(
			enterprise_id=enterprise_id, filename=filename, base64data=base64data)
		response = hr_service.importEmployeePayStructure(enterprise_id=enterprise_id, filename=temp_filename)
		if os.path.exists(temp_filename):
			os.remove(temp_filename)
	except Exception as e:
		logger.exception("Import failed %s" % e.message)
		response = response_code.internalError()
		response['custom_message'] = "Import failed!"
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def importAttendanceSheet(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		user_id = rh.getPostData('user_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
			user_id = rh.getSessionAttribute(SESSION_KEY)

		logger.info("Importing attendance data...")

		hr_service = HrService()
		dump_type = rh.getPostData('dump_type')  # punch, daily, monthly
		filename = rh.getPostData('filename')
		base64data = rh.getPostData('base64data')

		temp_filename = TEMP_DIR + "/" + hr_service.storeFileInTemp(
			enterprise_id=enterprise_id, filename=filename, base64data=base64data)
		if dump_type == 'monthly':
			month = rh.getPostData('month')
			response = hr_service.importMonthWiseAttendance(
				enterprise_id=enterprise_id, filename=temp_filename, month=month, user_id=user_id)
		else:
			response = response_code.paramMissing()
			response['custom_message'] = "dump_type is not mentioned!"
		if os.path.exists(temp_filename):
			os.remove(temp_filename)
	except Exception as e:
		logger.exception("Import failed %s" % e.message)
		response = response_code.internalError()
		response['custom_message'] = "Import failed. Wrong input data!"
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getAttendanceReport(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		month = rh.getPostData('month')

		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		if not month:
			today = datetime.date.today() + relativedelta(months=-1, day=1)
			month = today.strftime("%b, %Y")

		hr_service = HrService()
		response = response_code.success()
		response['reports'] = hr_service.getAttendanceReport(enterprise_id=enterprise_id, month=month)
		response['custom_message'] = "%s records" % len(response['reports'])
		logger.info("Loading %s of Attendance report" % response['custom_message'])
	except Exception as e:
		logger.exception("Failed loading report... %s" % e.message)
		response = response_code.internalError()
		response['custom_message'] = "Failed loading report!"
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def emailPaySlip(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		user_id = rh.getPostData('user_id')
		enterprise_id = rh.getPostData('enterprise_id')
		month = rh.getPostData('month')
		employee_code = rh.getPostData('employee_code')

		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		if not user_id:
			user_id = rh.getSessionAttribute(SESSION_KEY)
		if not month:
			today = datetime.date.today() + relativedelta(months=-1, day=1)
			month = today.strftime("%b, %Y")

		hr_service = HrService()
		if hr_service.emailPaySlip(
				enterprise_id=enterprise_id, month=month, employee_code=employee_code, user_id=user_id) is True:
			response = response_code.success()
			logger.info("Pay slip has been generated and mailed successfully.")
		else:
			response = response_code.failure()
			logger.warn("Pay has been generated, but failed to email it!")
	except Exception as e:
		response = response_code.internalError()
		response['custom_message'] = "Sending email failed"
		logger.error("Email Payslip failed... %s" % e.message)

	return HttpResponse(json.dumps(response), 'content-type=text/json')


def downloadPayStructure(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		hr_service = HrService()
		column_names = ["S.No", "EMPLOYEE CODE", "EMPLOYEE NAME"]
		pay_structure_items = hr_service.dao.getPayStructureItems(enterprise_id=enterprise_id)
		pay_dict = {}
		for pay in pay_structure_items:
			key = pay.type + "_" + pay.description.lower()
			pay_dict[key] = pay.description.upper()

		for key in sorted(pay_dict.keys()):
			column_names.append(pay_dict[key])

		data = helper.createXlsFile(column_names=column_names, sheet_name="EmployeeSalaryValues")
		filename = "EmployeeSalaryValues.xlsx"
		response = HttpResponse(data, mimetype="application/force-download")
		response['Content-Disposition'] = 'filename=%s' % smart_str(filename)
		return response
	except Exception as e:
		logger.error("Download Pay Structure failed... %s" % e.message)




