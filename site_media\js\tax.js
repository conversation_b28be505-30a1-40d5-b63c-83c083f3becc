
$(document).ready(function(){
	$.extend($.expr[":"], {
        "contains-ci": function(elem, i, match, array) {
            return (elem.textContent || elem.innerText || $(elem).text() || "").toLowerCase().indexOf((match[3] || "").toLowerCase()) >= 0;
        }
    });

    $('#save_tax_button').click(function(){
		$(".error-border").removeClass('error-border');
		$(".custom-error-message").remove();

		var ControlCollections = [
			{
				controltype: 'textbox',
				controlid: 'id_tax-code',
				isrequired: true,
				errormsg: 'Code is required.'
			},
			{
				controltype: 'textbox',
				controlid: 'id_tax-name',
				isrequired: true,
				errormsg: 'Name is required.'
			},
			{
				controltype: 'textbox',
				controlid: 'id_tax-base_rate',
				isrequired: true,
				errormsg: 'Rate is required.'
			}
		 ];
		var result = JSCustomValidator.JSvalidate(ControlCollections);
		if(result){
			SaveTaxButton();
		}
	});
	
	function SaveTaxButton(){
        $("#loading").show();
        if (($('#id_sub_tax-__prefix__-name').val().trim() != '') || (parseFloat($('#id_sub_tax-__prefix__-rate').val()) != 0)){
            $('#add_new_form').click(); // Adding a new form in case the empty_form is filled
        }

        var form_count = parseInt(document.getElementById('id_sub_tax-TOTAL_FORMS').value);
        for (i=0; i<form_count; i++){
            if(document.getElementById('id_sub_tax-'+i+'-parent_code').value == '')
                document.getElementById('id_sub_tax-'+i+'-parent_code').value=document.getElementById('id_tax-code').value;
        }
        if ($("#formset_errors").val() != 'Invalid Sub-tax'){
            if ($('#id_tax-code').is(':disabled')){
                $("#save_tax_button").text('Processing...').addClass('btn-processing');
                clickButton('saveTax');
                ga('send', 'event', 'Tax', 'Update', $('#enterprise_label').val(), 1);
                return;
            } else {
                $.ajax({
                    url: "/erp/masters/json/tax_code_exists/",
                    type: "POST",
                    dataType: "json",
                    data: {tax_code: $("#id_tax-code").val()},
                    success: function (json) {
                         if (json == 1) {
                            $("#loading").hide();
                            $(".duplicate_drawing").text('Tax Code already exists');
                            $("#id_tax-code").focus();
                            $("#id_tax-code").keyup(function(){
                                $(".duplicate_drawing").text('');
                            });
                        }
                        else {
                            $("#save_tax_button").text('Processing...').addClass('btn-processing');
                            clickButton('saveTax');
                            ga('send', 'event', 'Tax', 'Create', $('#enterprise_label').val(), 1);
                        }
                    },
                    error: function (xhr, errmsg, err) {
                        console.log(xhr.status + ": " + xhr.responseText);
                    }
                });
            }
        } else {
            $("#loading").hide();
            $("#formset_errors").val("");
        }
        calculateNetRate();
    }

    $("select#id_tax-type").change(function () {
        if ($("#id_tax-type option:selected").val() =='CGST' || $("#id_tax-type option:selected").val() =='SGST' || $("#id_tax-type option:selected").val() =='IGST' ){
            $("#id_sub_tax_form").addClass('hide');
            $("#sub_taxes").find(".added_tax").remove();
            $("#id_sub_tax-TOTAL_FORMS").val(0);
        }
        else {
            $("#id_sub_tax_form").removeClass('hide');
        }
    });

    $('#add_new_form').click(function() {
        var form_idx = $('#id_sub_tax-TOTAL_FORMS').val();
        var duplicate = false;
        var new_sub_tax_name = document.getElementById('id_sub_tax-__prefix__-name').value;
        for (i=0; i<form_idx; i++){
            duplicate = duplicate || (document.getElementById('id_sub_tax-'+i+'-name').value == new_sub_tax_name);
        }
        if (duplicate){
            swal({title: "",
                  type: "error",
                  text: "A Sub-tax with name '" + new_sub_tax_name + "' is already linked with this tax.\nKindly change the name to save the changes!"
                  });
            $("#formset_errors").val("Invalid Sub-tax");
        } else {
            new_form = $('#sub_tax-__prefix__').html().replace(/__prefix__/g, form_idx);
            new_form_html = "<tr bgcolor=\"#ECECEC\" id=\"sub_tax-" + form_idx + "\" align=\"center\" class=\"added_tax\" >" + new_form + "</tr>";
            $(new_form_html).insertBefore('#sub_tax-__prefix__');
            $('#id_sub_tax-TOTAL_FORMS').val(parseInt(form_idx) + 1);
            copyFromEmptyForm(form_idx);
            $("#formset_errors").val("");
        }
        calculateNetRate();
    });
    calculateNetRate();
    $("select#id_tax-type").trigger("change");
});

function deleteTax(taxCode, current){
    var taxName = $(current).attr('data_file_name');
	swal({
	  title: "Are you sure?",
	  text: "Do you want to delete Tax: <span style='color:#209be1'>" + taxName+" ("+taxCode+")</span>?",
	  type: "warning",
	  showCancelButton: true,
	  confirmButtonColor: "#209be1",
	  confirmButtonText: "Yes, delete it!",
	  closeOnConfirm: true
	},
	function(){
		$("#delete_tax_id").val(taxCode);
        $("#tax_delete_form").submit();
	});
}

function calculateNetRate(){
	/*
     For any change in the purchase price or quantity the total value is to be recalculated.
     */
    var base_rate = document.getElementById("id_tax-base_rate");
    var net_rate = document.getElementById("id_tax-net_rate");
    net_sub_tax_rate = 0;
    net_rate_value = parseFloat(base_rate.value);
    var form_count = parseInt(document.getElementById('id_sub_tax-TOTAL_FORMS').value);
    for (i = 0; i < form_count; i++) {
        if(!$('#id_sub_tax-'+i+'-DELETE').is(':checked'))
            net_sub_tax_rate += parseFloat($('#id_sub_tax-'+i+'-rate').val());
    }
    if ($('#id_sub_tax-__prefix__-rate').val() != 'None' && !$('#id_sub_tax-__prefix__-DELETE').is(':checked')){
        net_sub_tax_rate += parseFloat($('#id_sub_tax-__prefix__-rate').val());
    }
    if (net_sub_tax_rate > 0){
        net_rate_value = net_rate_value + net_rate_value * net_sub_tax_rate / 100;
    }
    net_rate.value = net_rate_value.toFixed(3);
    $('#id_net_rate_dummy').val(net_rate_value.toFixed(3));
}

function copyFromEmptyForm(form_idx){
    var added_form_parent_code = document.getElementById('id_sub_tax-'+form_idx+'-parent_code');
    var added_form_name = document.getElementById('id_sub_tax-'+form_idx+'-name');
    var added_form_percentage = document.getElementById('id_sub_tax-'+form_idx+'-rate');
    var added_form_delete_flag = document.getElementById('id_sub_tax-'+form_idx+'-DELETE');

    // Copy data from empty_form to the new form
    added_form_parent_code.value = document.getElementById('id_sub_tax-__prefix__-parent_code').value;
    added_form_percentage.value = document.getElementById('id_sub_tax-__prefix__-rate').value;
    added_form_name.value = document.getElementById('id_sub_tax-__prefix__-name').value;
    added_form_delete_flag.checked = document.getElementById('id_sub_tax-__prefix__-DELETE').checked;

    document.getElementById('id_sub_tax-__prefix__-parent_code').value = '';
    document.getElementById('id_sub_tax-__prefix__-name').value = '';
    document.getElementById('id_sub_tax-__prefix__-rate').value = 0.00;
}

function deleteSubTax(sub_tax_form_prefix){
	deleteForm(sub_tax_form_prefix);
	calculateNetRate();
}

$(function(){
  var hash = window.location.hash;
  hash && $('ul.nav a[href="' + hash + '"]').tab('show');

  $('.nav-tabs a').click(function (e) {
    $(this).tab('show');
    var scrollmem = $('body').scrollTop() || $('html').scrollTop();
    window.location.hash = this.hash;
    $('html,body').scrollTop(scrollmem);
  });
});

$('.nav-pills li').removeClass('active');
$('#li_tax').addClass('active');