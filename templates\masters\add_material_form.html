<style>
	.ui-autocomplete {
		z-index: 10021 !important;
	}

	.material_price_status > select {
		padding: 2px;
		font-size: 12px;
	}

	.input_unit_display {
		border:  none;
		background: transparent;
		text-align: right;
		padding: 0 !important;
	    height: 12px !important;
	    width: 70px;
	}

	.document_attachment_part {
	    display: inline-block;
	    width: 96px;
	    border: solid 1px #ccc;
	    padding: 10px 6px 0;
	    margin: 8px;
	    border-radius: 4px;
	    text-align: center;
	}

	.document_text {
	    display: block;
	    width: 75px;
	    text-overflow: ellipsis;
	    max-width: 75px;
	    overflow: hidden;
	    white-space: nowrap;
	}

	.document_remove {
	    position: absolute;
	    margin-left: 57px;
	    margin-top: -21px;
	    cursor: pointer;
	    font-size: 16px;
	}

	#material_attachment_upload .error-border {
		background-color: #fdeded !important;
	}

	#add_new_alternate_unit .unit_display {
	    max-width: 150px;
	    white-space: nowrap;
	    overflow: hidden;
	    text-overflow: ellipsis;
	}

	#alternate_unit_table td {
		border-top: none;
		padding: 3px 6px;
	}
</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>

<div class="col-lg-4 add_table_content remove-left-padding">
	<div class="form-group">
		{% if material_form.name.value == '' or material_form.name.value == None %}
			<div>
				<label>Name<span class="mandatory_mark"> *</span></label>
				{{ material_form.name }}
			</div>
		{% else %}
			{% if access_level.approve %}
				<div class='material-name-in-input hide' id="material-input">
					<label>Name<span class="mandatory_mark"> *</span></label>
					{{ material_form.name }}
				</div>
				<div class="material-name header-label" style="margin-top: -10px;" onclick="showContainer('material-input'); hideContainer(this); focusOn('id_material-name')">
					{{ material_form.name.value }}
					<i class="fa fa-pencil pull-right" style="color: #209be1; display: none;" aria-hidden="true"></i>
				</div>
			{% else %}
				<div class="material-name header-label">
					{{ material_form.name.value }}
				</div>
				<input type="hidden" value="{{ material_form.name.value }}" id="id_material-hidden_name" name="material-name"/>
			{% endif %}
		{% endif %}
	</div>
	<div class="form-group col-sm-6 remove-padding-left">
		<label>Drawing Number</label>
		<input type="hidden" id="last_saved_item_name" value="{{last_saved_item_name}}"/>
		{{ material_form.drawing_no }}
		{{ material_form.enterprise_id}}
		{{ material_form.material_id }}
		<div class="duplicate_drawing" style="font-size: 12px; color: #dd4b39;  position: absolute; letter-spacing: 0.30px; line-height: 20px;"></div>
	</div>
	<div class="form-group col-sm-6 remove-padding-right">
		<label>Category<span class="mandatory_mark"> *</span></label>
		{{ material_form.category_id }}
		<select id="id_{{material_form.prefix}}-category_select" class="form-control"></select>
	</div>
	<div class="form-group">
		<label>Description<span class="mandatory_mark"> *</span></label>
		{{ material_form.description }}
	</div>
	<div class="form-group">
		<label style="width: 100%;">Makes <small style="color: #999; text-transform: initial; float: right; right: 15px; letter-spacing: 0.5px; font-size: 11px;">Type and press enter to add new make</small></label>
		{{ material_makes_formset.management_form }}
		<div id="{{ material_makes_formset.empty_form.prefix }}">
			<div class="col-sm-12" style="padding: 0;">
				{{ material_makes_formset.empty_form.make }}
				<span hidden="hidden">{{ material_makes_formset.empty_form.DELETE }}</span>
				<span hidden="hidden">{{ material_makes_formset.empty_form.make_id }}</span>
			</div>
			<div class="col-sm-2 hide">
				<input id="add_make" class="btn btn-save" value="+" type="button">
			</div>
		</div>
	</div>
	<div class="form-group for_goods" id="makes">
		&nbsp;{% if makes_in_use|length >= 1 %}<span class="mandatory_mark"><i>Makes in use cannot be removed!</i></span>{% endif %}
		<ul id="ul-tagit-display" class="tagit-display" style="min-height: 0;">
			{% for make_form in material_makes_formset.initial_forms %}
			<li class="li-tagit-display" id="{{ make_form.prefix }}" {% if make_form.make_id.value == 1 %}style="display:none;"{% endif %}>
				<div hidden="hidden">{{ make_form.make }} {{ make_form.part_no }} {{ make_form.make_id }} {{ make_form.DELETE }}</div>
				<label class="label-tagit-display"  id="id_{{make_form.prefix}}-make_label" data-partno="{{ make_form.part_no.value }}" data-make="{{ make_form.make.value }}">
					{{ make_form.make.value }}
					{% if make_form.part_no.value != "" %}
						- {{ make_form.part_no.value }}
					{% endif %}
				</label>&nbsp;
				{% if make_form.make.value not in makes_in_use %}<a class="delete_tag" id="{{make_form.prefix}}-del_button"></a>{% endif %}
			</li>
		{% endfor %}
			<li id="material_make-__dummy__" hidden="hidden">
				<div hidden="hidden">{{ material_makes_formset.empty_form.make }} {{ material_makes_formset.empty_form.part_no }} {{ material_makes_formset.empty_form.make_id }}
				{{ material_makes_formset.empty_form.DELETE }}</div>
				<label class="label-tagit-display"  id="id_{{material_makes_formset.empty_form.prefix}}-make_label"></label>&nbsp;
				<a class="delete_tag" id="{{material_makes_formset.empty_form.prefix}}-del_button"></a>
				&nbsp;
			</li>
		</ul>
	</div>
	<div class="clearfix"></div>

	<div class="form-group col-sm-6 remove-padding-left" id="tariff_no">
		<label>HSN/SAC</label>
			{{ material_form.tariff_no }}
	</div>
	<div class="form-group col-sm-6 remove-padding-right">
		<label>Price<span class="mandatory_mark"> *</span></label>
		{{ material_form.price }}
		<input type="hidden" id="original_price" value="{{material_form.price.value}}"/>
	</div>
	<div class="form-group col-sm-6 remove-padding-left" id="material_unit">
		<label style="display: block;">Unit<span class="mandatory_mark"> *</span>
			{% if logged_in_user.enterprise.is_multiple_units %}
				<a class="pull-right"  id="alternateUoms" onClick="showAlternateUoms()" role="button" style="font-size: 9px;">+ More <span class="alternate-unit-count bracket-enclosed">{{alternate_unit_formset.initial_forms|length}}</span></a>
			{%endif%}
		</label>
		<div id="material_unit_no">{{ material_form.unit_id }}
		     {{ material_form.current_unit }}</div>
	</div>
	<div class="form-group col-sm-6 remove-padding-right">
		<label>MSL<span class="mandatory_mark"> *</span></label>
		{{ material_form.minimum_stock_level }}
	</div>
	<div class="form-group col-sm-12 remove-padding-left remove-padding-right" id="conversion_rate" hidden="hidden">
		<label>Conversion Rate<span class="mandatory_mark"> *</span></label>
		{{ material_form.unit_conversion_rate }}
	</div>
	<div class="form-group" id="div_is_stocked">
		<div class="checkbox service_stocked remove-chk-padding xs-styled-checkbox {% if stock_qty > 0 %}disabled{% endif %}" style="margin-top: 0;">
			{{ material_form.is_stocked}}
			<label for="id_material-is_stocked">Stock Accountable</label>
		</div>
		<div class="checkbox remove-chk-padding xs-styled-checkbox {% if stock_qty > 0 and material_form.in_use.value %}disabled{% endif %}">
			{{ material_form.in_use }}
			<label for="id_material-in_use">In Use</label>
		</div>
	</div>

	<div class="full_width_txt">
        <!-- Disabled fields are not read in POST, hence the hidden field -->
        <input type="text" value="{{ material_form.catalogue_code.value }}"
               id="id_material-catalogue_code" name="material-catalogue_code" hidden="hidden"/>
	</div>
	<div class="clearfix"></div>
	<div class="material_txt" id="divSaveCatalogueButton">
		{% if access_level.edit %}
		<a href="#" id="saveCatalogueButton" class="btn btn-save" >Save</a>
		{% endif %}
        <input type="button" hidden="hidden" id="saveCatalogue" value="Save">
		<a href="/erp/masters/catalogues/" id="cancellCatalogue" class="btn btn-cancel">Cancel</a>
	</div>
</div>
<div class="col-lg-8 remove-right-padding" style="margin-top: 15px;">
	<div class="available_stock_container" data-toggle="modal" data-target="#available_stock_modal">
		<h5 style="float: left; margin-bottom: 0;"><label>Available Stock:</label></h5>
		<span  class="stock_total">0.00</span>
	</div>
	<div class="clearfix"></div>
	<div class="accordion" id="accordion">
		<div class="accordion-group">
			<div class="accordion-heading">
				<span class="accordion-toggle" aria-expanded="false">
					Specifications
					<span class="btn btn-save pull-right" onclick="addNewSpecification()" style="padding: 2px 12px;">
						+
					</span>
				</span>
			</div>
			<div id="specification_container" class="collaps">
				<div class="accordion-inner row" style="margin: 0;">
					<div id="specification" style="margin: 0 -15px;" >
						<div hidden="hidden">
							{{ specifications_formset.empty_form.material_id }}
							{{ specifications_formset.empty_form.enterprise_id }}
						</div>
						<div id="edit_specification_modal" class="modal" role="dialog" data-backdrop="static" data-keyboard="false" style="margin-left: -730px;">
						  	<div class="modal-dialog" style="width: 400px; margin-top: 72px;">
						    	<div class="modal-content">
						      		<div class="modal-header">
						      			<button type="button" class="close" data-dismiss="modal">&times;</button>
						        		<h4 class="modal-title">Edit Specification</h4>
						      		</div>
						      		<div class="modal-body">
								        <div class="form-group">
								        	<label>Parameter<span class="mandatory_mark"> *</span></label>
											{{ specifications_formset.empty_form.parameter }}
										</div>
										<div class="form-group col-sm-4 remove-padding-left">
											<label>Min Value</label>
											{{ specifications_formset.empty_form.min_value }}
										</div>
										<div class="form-group col-sm-4 remove-padding">
											<label>Max Value</label>
											{{ specifications_formset.empty_form.max_value }}
										</div>
										<div class="form-group col-sm-4 remove-padding-right">
											<label>Unit</label>
											{{ specifications_formset.empty_form.unit }}
										</div>
										<div class="form-group">
											<label>Inspection Method/Tool</label>
											{{ specifications_formset.empty_form.inspection_method }}
										</div>
										<div class="form-group">
											<label>Reaction Plan</label>
											{{ specifications_formset.empty_form.reaction_plan }}
										</div>
										<div class="form-group" >
											<label>Instruction / comments</label>
											{{ specifications_formset.empty_form.comments }}
										</div>
										<div class="checkbox" style="padding-left: 20px !important;" >
											{{ specifications_formset.empty_form.qc_critical }}
											<label for="id_specification-__prefix__-qc_critical">QC Critical</label>
										</div>
							        </div>
								    <div class="modal-footer">
								    	<input type="button" id="add_new_specification" onclick="addSpecification(this, 'add');" class="btn btn-save hide" value="Add" />
										<input type="button" id="update_specification" onclick="addSpecification(this, 'update');" class="btn btn-save hide" value="Update"/>
									    <span class="btn btn-cancel" data-dismiss="modal">Cancel</span>
								    </div>
							    </div>
						    </div>
						</div>
						<div class="col-sm-12" style="overflow: auto; max-height: 265px;">
							{{ specifications_formset.management_form}}
							<table class="table custom-table text_box_in_table" id="specification_table" style="border-collapse: separate; border-spacing: 0 1em; border: none; margin-bottom: 0;">
								<tbody>
									<tr bgcolor="#ececec" id="specification-__dummy__" hidden="hidden">
										<td hidden="hidden" class="exclude_export">
											{{ specifications_formset.empty_form.material_id }}
											{{ specifications_formset.empty_form.DELETE }}
											{{ specifications_formset.empty_form.enterprise_id }}
										</td>
										<td class="material_specification_container hide">
											<span id="id_{{ specifications_formset.empty_form.prefix }}--parameterLabel">
												{{ specifications_formset.empty_form.parameter }}
											</span>
											<span id="id_{{ specifications_formset.empty_form.prefix }}-minvalueLabel">
												{{ specifications_formset.empty_form.min_value }}
											</span>
											<span id="id_{{ specifications_formset.empty_form.prefix }}-maxvalueLabel">
												{{ specifications_formset.empty_form.max_value }}
											</span>
											<span id="id_{{ specifications_formset.empty_form.prefix }}-unitLabel">
												{{ specifications_formset.empty_form.unit }}
											</span>
											<span id="id_{{ specifications_formset.empty_form.prefix }}-inspectionMethodLabel">
												{{ specifications_formset.empty_form.inspection_method }}
											</span>
											<span id="id_{{ specifications_formset.empty_form.prefix }}-reactionPlan">
												{{ specifications_formset.empty_form.reaction_plan }}
											</span>
											<span id="id_{{ specifications_formset.empty_form.prefix }}-commentsLabel">
												{{ specifications_formset.empty_form.comments }}
											</span>
											<span id="id_{{ specifications_formset.empty_form.prefix }}-qcCriticalLabel">
												{{ specifications_formset.empty_form.qc_critical }}
											</span>
										</td>
										<td class="material_specification_container_label" onclick="editSpecification(this)"></td>
										<td class="material_specification_container_delete"></td>
									</tr>

									{% for specification in specifications_formset.initial_forms %}
									<tr bgcolor="#ececec" id="{{ specification.prefix }}">
										<td hidden="hidden" class="exclude_export">
											{{ specification.material_id }}{{ specification.parameter }}{{ specification.comments }}
											{{ specification.inspection_method }}{{ specification.unit }}{{ specification.enterprise_id }}
											{{ specification.DELETE }}{{ specification.min_value}}{{ specification.max_value }}{{ specification.qc_critical }}{{ specification.reaction_plan }}
										</td>
										<td class="material_specification_container_label" onclick="editSpecification(this)">
											<span class="material_specification_parameter">{{ specification.parameter.value }}</span>
											<span class="material_specification_minMaxValue">
												{% if specification.min_value.value == specification.max_value.value and specification.min_value.value != None %}
													{{specification.min_value.value}}
												{% elif specification.min_value.value != None and  specification.max_value.value != None %}
													{{specification.min_value.value}} - {{specification.max_value.value}}
												{% elif specification.min_value.value == None and  specification.max_value.value != None %}
													<= {{specification.max_value.value}}
												{% elif specification.min_value.value != None and  specification.max_value.value == None %}
													>= {{specification.min_value.value}}
												{% endif %}
											</span>
						                    <span class="material_specification_unit">{{ specification.unit.value }}</span>
						                    <span class="material_specification_qc {% if specification.qc_critical.value%}checked{% endif%} pull-right"></span>
						                    <span class="material_specification_inspectionMethod">{{ specification.inspection_method.value }}</span>
						                    <span class="material_specification_reactionPlan">{{ specification.reaction_plan.value }}</span>
						                    <span class="material_specification_remarks">{{ specification.comments.value }}</span>
						                </td>
						                <td  class="material_specification_container_delete">
						                	<span class="material_specification_delete" role="button">
						                        <a onclick="javascript:deleteSpecification('{{specification.prefix}}')">
						                            <i class="fa fa-times"></i>
						                        </a>
					                    	</span>
					                    </td>
									</tr>
									{% endfor %}
									{% if specifications_formset.initial_forms|length == 0 %}
										<tr class="empty-specification">
											<td style="border: none; text-align: center"><b>No Specification profiled yet!</b></td>
										</tr>
									{% endif %}
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="clearfix"></div>
	</div>
	<div class="row" id="qc_related" style="padding: 0 15px;">
		<div class="form-group col-sm-6 remove-padding-left" >
			<label>QC Method </label>
			{{material_form.qc_method}}
		</div>
		<div class="form-group col-sm-3 remove-padding-left">
			<label>Sample Size</label>
			{{material_form.sample_size}}
		</div>
		<div class="form-group col-sm-3 remove-padding-right">
			<label>Lot Size</label>
			{{material_form.lot_size}}
		</div>
		<div class="form-group col-sm-12 remove-padding">
			<label>Reaction Plan</label>
			{{material_form.reaction_plan}}
		</div>
	</div>
</div>
<div id="material_bom_modal" class="modal fade" tabindex="-1" role="dialog" >
   <div class="modal-dialog" style="width: 1000px;">
	    <div class="modal-content">
		    <div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Bill of Materials</h4>
	      	</div>
      		<div class="modal-body remove-padding">
		        <div class="col-lg-12" id="bill_of_material" style="margin-bottom: 20px;margin-top: 10px;" >
					<div hidden="hidden">
						{{ bill_of_materials_formset.empty_form.catalogue_code }}
						<input type="text" value="0" hidden="hidden" id="newFormCount"/>
						{{ bill_of_materials_formset.empty_form.item_id }}
						{{ bill_of_materials_formset.empty_form.drawing_no }}
						{{ bill_of_materials_formset.empty_form.enterprise_id }}
						{{ bill_of_materials_formset.empty_form.material_select_field }}
						{{ bill_of_materials_formset.empty_form.make_select_field }}
						{{ bill_of_materials_formset.empty_form.make_id}}
					</div>
					<div class="col-sm-3 form-group" >
						<label>Item Name<span class="mandatory_mark"> *</span></label>
						<input type="text" value="" class="form-control" id="material_selected" placeholder="Select Item Name" maxlength="100"/>
						<input type="hidden" value="" class="text_box" id="drawing_no_selected"/>
					</div>
					<div class="col-sm-3" ><label>Suitable Makes<span class="mandatory_mark"> *</span></label>
						{{ bill_of_materials_formset.empty_form.makes }}
					</div>
					<div class="col-sm-3" ><label>Quantity<span class="mandatory_mark"> *</span></label>
						<div>{{ bill_of_materials_formset.empty_form.quantity }}</div>
						<div >
							<label id="unit_display" class="unit_display pull-right">&nbsp;</label>
							<div style="display:none">{{ bill_of_materials_formset.empty_form.units }}</div>
						</div>
					</div>
					<div class="col-sm-3">
						<label>&nbsp;</label>
						<span class="material_txt">
							<input type="button" id="add_new_catalogue_material" class="btn btn-save btn-margin-1" value="+"/> &nbsp;&nbsp;
							<a role="button" class="btn btn-add-new pull-right btn-margin-1 export_bom_list" onclick="validateBOMDownload(this);"  data-tooltip="tooltip" title="" data-original-title="Download BoM List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
							<span class="tool-tip pull-right" data-toggle="tooltip" data-placement="top" title="Please enter drawing number">
							{% if access_level.edit %}
								<a href="#" id="importbutton" style="margin-right: 10px;"  title="" class="btn btn-add-new btn-margin-1" onclick="javascript:showImportBom();"/><i class="fa fa-upload" aria-hidden="true" data-tooltip="tooltip" title="" data-original-title="Upload Bulk BoM"></i></a>
							{% endif %}
							</span>
							<label>&nbsp;</label>
							<a role="button" class="btn btn-add-new pull-right btn-margin-1" style="margin-right: 10px; padding: 4px 10px;" onclick="javascript:showCheapestSupplierBom(this);"  data-tooltip="tooltip" title="" data-placement="left" data-original-title="BOM Costing">
								<img src="/site_media/images/costing_sheet_icon.svg" style="width: 26px;" />
							</a>
						</span>
					</div>
					<div class="col-sm-12">
						{{bill_of_materials_formset.management_form}}
						<table class="table table-bordered table-striped custom-table text_box_in_table" id="materialtable">
							<thead>
								<tr>
									<th width="20px">S.No</th>
									<th width="150px">Drawing No</th>
									<th>Name</th>
									<th width="200px">Suitable Makes</th>
									<th width="84px">Quantity</th>
									<th width="55px">Unit</th>
									<th width="40px" class="exclude_export">Delete</th>
								</tr>
							</thead>

							<tbody>
							<tr bgcolor="#ececec" id="bill_material-__dummy__" hidden="hidden">
								<td hidden="hidden" class="exclude_export">
									{{ bill_of_materials_formset.empty_form.catalogue_code }}
									{{ bill_of_materials_formset.empty_form.DELETE }}
									{{ bill_of_materials_formset.empty_form.id}}
									{{ bill_of_materials_formset.empty_form.ORDER }}
									{{ bill_of_materials_formset.empty_form.item_id }}
									{{ bill_of_materials_formset.empty_form.drawing_no }}
									{{ bill_of_materials_formset.empty_form.enterprise_id }}
									{{ bill_of_materials_formset.empty_form.make_id }}
								</td>
								<td></td>
								<td colspan="2">{{ bill_of_materials_formset.empty_form.material_select_field }}</td>
								<td width="200px"><div id="id_{{ bill_of_materials_formset.empty_form.prefix }}-makeLabel"> {{ bill_of_materials_formset.empty_form.make_select_field }}</div></td>
								<td class="text-right">
										<div id="id_{{ bill_of_materials_formset.empty_form.prefix }}-quantityLabel">{{ bill_of_materials_formset.empty_form.quantity }}</div>
									</td>
								<td width="100px">{{ bill_of_materials_formset.empty_form.units }}</td>
								<td width="20px" class="text-center"><a href="#" id="id_{{ bill_of_materials_formset.empty_form.prefix }}-deleteCatMaterial"
									   onclick="javascript:deleteCatMaterial('{{ bill_of_materials_formset.empty_form.prefix }}')">
									<i class="fa fa-trash-o"
										 id='deleteImage_{{ bill_of_materials_formset.empty_form.catalogue_code.value }}'></i></a>
								</td>
							</tr>

							{% for bill_material in bill_of_materials_formset.initial_forms %}
							<tr bgcolor="#ececec" id="{{ bill_material.prefix }}">
								<td hidden="hidden" class="exclude_export">
									{{ bill_material.catalogue_code }}{{ bill_material.drawing_no }}{{ bill_material.item_id }}
									{{ bill_material.quantity }}{{ bill_material.units }}{{ bill_material.enterprise_id }}
									{{ bill_material.DELETE }}{{ bill_material.id}}{{ bill_material.ORDER }}{{ bill_material.make_id }}
								</td>
								<td class="text-right">{{ forloop.counter}}</td>
								<td width="150px">{{ bill_material.drawing_no.value }}</td>
								<td>
									<a role='button' onclick='javascript:editMaterialFromBom("{{ bill_material.drawing_no.value }}")'>{{ bill_material.name.value }}</a>
								</td>
								<td width="200px"><div id="id_{{ bill_material.prefix }}-makeLabel">{{ bill_material.make_select_field.value }}</div></td>
								<td class="text-right">
										<div id="id_{{ bill_material.prefix }}-quantityLabel"> {{ bill_material.quantity.value }}</div>
									</td>
								<td width="55px">{{ bill_material.units.value }}</td>
								<td width="20px" class="text-center"><a href="#" id="id_{{ bill_material.prefix }}-deleteCatMaterial"
									   onclick="javascript:deleteCatMaterial('{{ bill_material.prefix }}')">
									<i class="fa fa-trash-o" id='deleteImage_{{ bill_material.prefix }}'></i></a>
								</td>
							</tr>
							{% endfor %}
							{% if bill_of_materials_formset.initial_forms|length == 0 %}
							<tr><td colspan="9" align="center"><b class="service_package_profile">No BoM profiled yet!</b></td></tr>
							{% endif %}
							</tbody>
						</table>
						<hr/>
					</div>
					{% if participating_bom_of_materials|length > 0 %}
			        {% if material_type == "goods" %}
						<div class="col-sm-9"><h4 class="service_material_container"> BoMs containing this Material </h4></div>
			        {% endif %}
					{% if material_type == 'service' %}
			             <div class="col-sm-9 service_material_container"><h4> Service Package containing this Material </h4></div>
			        {% endif %}
					<div class="col-sm-3"><span class="material_txt"><a role="button" class="btn btn-add-new pull-right export_csv"
					   onclick="GeneralExportTableToCSV.apply(this, [$('#participating_bom_materialtable'), '{{material_form.drawing_no.value}}-parts_thereof.csv']);"
					   data-tooltip="tooltip" title="" data-original-title="Download the list of BoMs, this Material is part of as CSV">
						<i class="fa fa-download" aria-hidden="true"></i>
					</a></span></div>
					<div class="col-sm-12">
						<table class="table table-bordered table-striped custom-table text_box_in_table" id="participating_bom_materialtable">
							<thead>
								<tr>
									<th width="20px">S.No</th>
									<th width="150px" class="service_item_code">Drawing No</th>
									<th>Name</th>
									<th width="200px">Suitable Makes</th>
									<th width="84px">Quantity</th>
								</tr>
							</thead>
								{% for bom_material in participating_bom_of_materials %}
									<tr bgcolor="#ececec" id="{{ bom_material.prefix }}">
										<td class="text-right">{{ forloop.counter}}</td>
										<td width="150px">{{ bom_material.drawing_no }}</td>
										<td>
											<a role='button' onclick='javascript:editMaterialFromBom("{{ bom_material.drawing_no }}")'>{{ bom_material.name }}</a>
										</td>
										<td width="200px"><div id="id_{{ bom_material.prefix }}-makeLabel">{% if bom_material.make_name == None %} -NA- {% else %} {{ bom_material.make_name }} {% endif %}</div></td>
										<td align="right" width="84px"><div id="id_{{ bom_material.prefix }}-quantityLabel"> {{ bom_material.qty }}</div></td>
									</tr>
								{% endfor %}
							</tbody>
						</table>
					</div>
					{% else %}
					<div class="col-sm-12">
						<i>This Material does not form part of any other Material's BoM!</i>
					</div>
					{% endif %}
				</div>
      		</div>
      		<div class="modal-footer">
		       <span class="pull-left nonStock-text">  <span class=" nonStock-text non_stock-flag"></span> - NON-STOCKABLE</span>
				<span class="pull-left nonStock-text">  <span class=" nonStock-text service-item-flag"></span> - SERVICE</span>
      			<a href="#" class="btn btn-cancel" data-dismiss="modal">Close</a>
      		</div>
    	</div>
  	</div>
</div>
<div id="material_price_profile" class="modal fade" tabindex="-1" role="dialog" >
   <div class="modal-dialog" style="width: 1000px;">
	    <div class="modal-content">
		    <div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Price Profile</h4>
	      	</div>
      		<div class="modal-body remove-padding">
		        <div class="col-lg-12" id="material_sup_profile">
					<div class="general-warning" style="margin-top: 10px; padding: 3px 15px; margin-bottom: 10px;">
						<i>Ensure that Prices mentioned here are without Tax and with all Discounts applied.</i>
					</div>
					<div hidden="hidden">
						{{ materials_party_price_formset.empty_form.supp_id }}
						{{ materials_party_price_formset.empty_form.drawing_no }}
						{{ materials_party_price_formset.empty_form.item_id }}
						{{ materials_party_price_formset.empty_form.enterprise_id }}
						{{ materials_party_price_formset.empty_form.supplier_select_field }}
						{{ materials_party_price_formset.empty_form.effect_since }}
						{{ materials_party_price_formset.empty_form.effect_till }}
						{{ materials_party_price_formset.empty_form.is_primary }}
						{{ materials_party_price_formset.empty_form.is_service }}
						{{ materials_party_price_formset.empty_form.make_select_field }}
						{{ materials_party_price_formset.empty_form.currency_select_field }}
					</div>
					<div class="row">
						<div class="col-sm-5 form-group">
							<label>Supplier<span class="mandatory_mark"> *</span></label>
							<input type="text" value="" class="form-control"
									   id="supplier_selected" placeholder="Select Supplier"/>
							<input type="hidden" value="" class="text_box" id="supplier_no_selected"/>
						</div>
						<div class="col-sm-3 form-group">
							<label>Make<span class="mandatory_mark"> *</span></label>
							<select class="form-control" id="id_price_material-__prefix__-make_choices">
								{% for make in make_list %}
								<option value="{{ make.0 }}">{{ make.1 }}</option>
								{% endfor %}
							</select>

						</div>
						<div class="col-sm-3 form-group">
							<label>Price<span class="mandatory_mark"> *</span></label>
							{{ materials_party_price_formset.empty_form.price }}
							{{ materials_party_price_formset.empty_form.currency_id }}
							<label class="unit_display pull-right" id="currency_unit_display">&nbsp;</label>
						</div>
					</div>
					<div class="row effect_date_parent">
						<div class="col-sm-3 form-group effect_since_parent">
							<label>Effect Since</label>
							<div class="hide">{{ materials_party_price_formset.empty_form.effect_since }}</div>
							<input type="text" value="" class="hide" id="id_material-effect_since" placeholder="Effect From" />
							<input type="text" class="form-control custom_datepicker effect_since_child" readonly="readonly" id="effect_from_date" placeholder="Select Date" style="background: #FFF;">
							<i class="glyphicon glyphicon-calendar custom-calender-icon"></i>
						</div>
						<div class="col-sm-3 form-group effect_till_parent">
							<label>Effect Till</label>
							<div class="hide">{{ materials_party_price_formset.empty_form.effect_till }}</div>
							<input type="text" value="" class="hide" id="id_material-effect_till" placeholder="Effect Till" />
							<input type="text" class="form-control custom_datepicker set-empty-date erasable-date effect_till_child" autocomplete="off" id="effect_till_date" placeholder="Select Date">
							<i class="glyphicon glyphicon-calendar custom-calender-icon"></i>
						</div>
						<div class="col-sm-5 form-group">
							<label>Remarks</label>
							{{ materials_party_price_formset.empty_form.remarks }}
						</div>
						<label>&nbsp;</label>
						<div class="material_txt">
							<input type="button" id="add_new_supplier_material" class="btn btn-save"
								   value="+"/>
						</div>
					</div>
				</div>
				<div class="col-sm-12">
					{{materials_party_price_formset.management_form}}
					<table class="table table-striped table-bordered custom-table text_box_in_table" id="supp_price_table" style="margin-top: 15px;">
						<thead>
							<tr>
								<th rowspan="2" width="20px">Store Value</th>
								<th rowspan="2" width="20px">Job</th>
								<th class='hide' rowspan="2">Supplier</th>
								<th rowspan="2" width="30%">Make</th>
								<th rowspan="2" width="100px">Price</th>
								<th colspan="2" width="100px">Effective</th>
								<th class='hide'rowspan="2" width="70px">Remarks</th>
								<th rowspan="2" width="100px">Status</th>
							</tr>
							<tr>
								<th width="50px">Since</th>
								<th width="50px">Till</th>
							</tr>
						</thead>
						<tbody>
							<tr  id="price_material-__dummy__" hidden="hidden" class="effect_date_parent">
								<td hidden="hidden">
									{{ materials_party_price_formset.empty_form.supp_id }}
									{{ materials_party_price_formset.empty_form.DELETE }}
									{{ materials_party_price_formset.empty_form.id}}
									{{ materials_party_price_formset.empty_form.item_id }}
									{{ materials_party_price_formset.empty_form.drawing_no }}
									{{ materials_party_price_formset.empty_form.enterprise_id }}
									{{ materials_party_price_formset.empty_form.currency_id }}
								</td>
								<td class="text-centerdiv-disabled">
									{{ materials_party_price_formset.empty_form.is_primary }}
								</td>
								<td class="text-center">{{ materials_party_price_formset.empty_form.is_service }}</td>
								<td class='hide'>{{ materials_party_price_formset.empty_form.supplier_select_field }}</td>
								<td hidden="hidden">{{ materials_party_price_formset.empty_form.make_id }}</td>
								<td>{{ materials_party_price_formset.empty_form.make_select_field }}</td>
								<td class="td_textbox_right text-right">
									{{ materials_party_price_formset.empty_form.price }}
									<span class='hide'>{{ materials_party_price_formset.empty_form.currency_select_field }}</span>
									<span class="price-profile-unit" style="display: block;"></span>
								</td>
								<td class="td_load_date effect_since_parent">
									{{ materials_party_price_formset.empty_form.effect_since }}
									<input type="text" class="form-control custom_datepicker effect_since_child" readonly="readonly" id="id_{{ materials_party_price_formset.empty_form.prefix }}-effect_since_date" placeholder="Select Date" style="background: #FFF; min-width: 84px; font-size: 12px; padding: 4px;">
								</td>
								<td class="td_load_date effect_till_parent">
									{{ materials_party_price_formset.empty_form.effect_till }}
									<input type="text" class="form-control custom_datepicker set-empty-date erasable-date effect_till_child" autocomplete="off" readonly="readonly" id="id_{{ materials_party_price_formset.empty_form.prefix }}-effect_till_date" placeholder="Select Date" style="background: #FFF; min-width: 84px; font-size: 12px; padding: 4px;">
								</td>
								<td style="width: 100px; max-width: 100px; word-wrap: break-word;">
									<span class='price_profile_remarks' title='Remarks' onclick='openPriceProfileRemarks(this);'>
	                                    <i class="fa fa-comment" aria-hidden="true"></i>
	                                </span>
									{% if access_level.approve %}
										{{ materials_party_price_formset.empty_form.status }}
									{% else %}
									<div class="hide">{{ materials_party_price_formset.empty_form.status }}</div>
										Pending
									{% endif %}
								</td>
							</tr>

							{% for price_material in materials_party_price_formset.initial_forms %}
							<tr style="background: rgba(32,155,255,0);" data-supplier-id='{{ price_material.supp_id.value }}'id="{{ price_material.prefix }}" class="effect_date_parent">
								<td hidden="hidden">
									{{ price_material.supp_id }}{{ price_material.drawing_no }}{{ price_material.item_id }}
									{{ price_material.remarks }}{{ price_material.enterprise_id }}
									{{ price_material.DELETE }}{{ price_material.id}}{{ price_material.currency_id}}
								</td>
								<td class="text-center {% if price_material.status.value != 1 %}div-disabled{% endif %}">
									{{ price_material.is_primary}}
								</td>
								<td class="text-center {% if price_material.status.value == 1 %}div-disabled{% endif %}">
									{{ price_material.is_service}}
								</td>
								<td class='td-supplier-name hide'>{{ price_material.supplier_select_field.value }}</td>
								<td>{{ price_material.make_id }}<div id="id_{{ price_material.prefix }}-makeLabel">{{ price_material.make_select_field.value }}</div></td>
								<td  class="text-right td_textbox_righttable_text_box">
									{% if price_material.status.value != -1 and price_material.status.value != 0 %}
										<span class='hide'>{{ price_material.price }}</span>
										{{ price_material.price.value }}
									{% else %}
										{{ price_material.price }}
									{% endif %}
									<span class="price-profile-unit" style="display: block;">
										{{ price_material.currency_select_field.value}}
									</span>
								</td>
								<td class="text-center td_load_date effect_since_parent">
									<div {% if price_material.status.value != -1 and price_material.status.value != 0 %}class="hide"{% endif %}>
										{{ price_material.effect_since }}
										<input type="text" class="form-control custom_datepicker effect_since_child" readonly="readonly" id="effect_from_date_{{ price_material.prefix }}" placeholder="Select Date" style="background: #FFF; min-width: 84px; font-size: 12px; padding: 4px;">
									</div>
									{% if price_material.status.value != -1 and price_material.status.value != 0 %}{{ price_material.effect_since.value|date:'M d, Y' }}{% endif %}
								</td>
								<td class="text-center td_load_date effect_till_parent">
									<div {% if price_material.status.value != -1 and price_material.status.value != 0 %}class="hide"{% endif %} id="{{ price_material.prefix}}_effect_till">
										{% if price_material.effect_till.value and price_material.status.value == 1 %}
											{{ price_material.effect_till.value|date:'M d, Y' }}
											{{ price_material.effect_till }}
										{% else %}
											{{ price_material.effect_till }}
											<input type="text" class="form-control custom_datepicker set-empty-date erasable-date effect_till_child" autocomplete="off" readonly="readonly" id="effect_from_since_{{ price_material.prefix }}" placeholder="Select Date" style="background: #FFF; min-width: 84px; font-size: 12px; padding: 4px;">
										{% endif %}
									</div>
									{% if price_material.effect_till.value and price_material.status.value == 1 %}
										{{ price_material.effect_till.value|date:'M d, Y' }}
									{% else %}
										{% if price_material.status.value != -1 and price_material.status.value != 0 %}
											<a role="button" class="pull-right" style="font-size: 16px;" onclick="javascript:updateSupplierPrice('{{ price_material.prefix}}', '{{access_level.approve}}')">
												<i class="fa fa-pencil" aria-hidden="true"></i>
											</a>
										{% endif %}
									{% endif %}
								</td>
								<td class="text-center">
									<span class='price_profile_remarks' data-tooltip='tooltip' title='Remarks' onclick='openPriceProfileRemarks(this);'>
										<i class="fa fa-comment" aria-hidden="true"></i>
                                    </span>
									{% if access_level.approve and price_material.status.value == 0 %}
										<div class="material_price_status">{{ price_material.status }}</div>
									{% else %}
										<div class="hide">{{price_material.reject_remarks}}</div>
										<div class="hide">{{ price_material.status }}</div>
										<span class="supplier_price_status">
										{% if price_material.status.value == 0 %}
											Pending
										{% else %}
											{% if price_material.status.value == 1 %}
												Approved
											{% else %}
										</span>
										<select id="id_{{price_material.prefix}}-status_select" {% if price_material.reject_remarks.value|length > 0 %} data-tooltip="tooltip" title="Rejected due to:  {{price_material.reject_remarks.value}}" {% endif %} class="form-control" style="border: solid 1px red; color: red;">
											<option value="-1">Rejected</option>
											<option value="0"style="color: #000;">Re-Submit</option>
											<option value="-2"style="color: #000;">Remove</option>
										</select>
										<script type="text/javascript">
											$("#id_{{price_material.prefix}}-status_select").change(function(){
												if($("#id_{{price_material.prefix}}-status_select option:selected").val()=="-2"){
													$("#id_{{price_material.prefix}}-DELETE").attr("checked", "checked");
												}else{
													$("#id_{{price_material.prefix}}-DELETE").removeAttr("checked");
													$("#id_{{price_material.prefix}}-status").val($("#id_{{price_material.prefix}}-status_select option:selected").val());
												}
											});
										</script>
									{% endif %}
									{% endif %}
									{% endif %}
								</td>
							</tr>
							<tr class="general-remarks hide">
								<td colspan="3" class="text-right"><label>Remarks:</label></td>
								<td colspan="4">
									<div {% if price_material.status.value != -1 and price_material.status.value != 0  %}class="hide"{% endif %}>{{ price_material.remarks }}</div>
									{% if price_material.remarks.value and price_material.status.value == 1 %}
										{{ price_material.remarks.value }}
									{%endif %}
								</td>
							</tr>
							{% if access_level.approve and price_material.status.value == 0 %}
							<tr class="hide reject_remarks">
								<td colspan="3" class="text-right"><label>Rejection Remarks:</label></td>
								<td colspan="4">{{price_material.reject_remarks}}</td>
							</tr>
							{% endif %}
							{% endfor %}
							{% if materials_party_price_formset.initial_forms|length == 0 %}
								<tr><td colspan="7" align="center"><b>No prices profiled yet!</b></td></tr>
							{% endif %}
						</tbody>
					</table>
				</div>
			</div>
      		<div class="modal-footer">
      			<a href="#" class="btn btn-cancel" data-dismiss="modal">Close</a>
      		</div>
    	</div>
  	</div>
</div>
<div id="add_new_unit_modal" class="modal fade" role="dialog" data-backdrop="static" data-keyboard="false">
  	<div class="modal-dialog">
    	<div class="modal-content">
      		<div class="modal-header">
        		<h4 class="modal-title">Create New Unit</h4>
      		</div>
      		<div class="modal-body">
		        <div class="form-group">
			        <label>Unit Name<span class="mandatory_mark"> *</span></label>
	                <input type="text" name="unit_name" id="unit_name" class="form-control" placeholder="E.g. KG" maxlength="20"
	                        onkeypress="return validateStringOnKeyPress(this,event, 'string')" />
		        </div>
		        <div class="form-group">
			        <label>Description<span class="mandatory_mark"> *</span></label>
	                <input type="text" name="unit_desc" id="unit_desc" class="form-control" placeholder="E.g. Kilogram" placeholder="" maxlength="20"
	                       onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialCharMini')" />
		        </div>
	        </div>
		    <div class="modal-footer">
				<span onclick="addNewUnit()" id="addNewUnit" class="btn btn-save">Add</span>
			    <span onclick="cancelUnitAdd();" class="btn btn-cancel">Cancel</span>
		    </div>
	    </div>
    </div>
</div>

<div id="available_stock_modal" class="modal fade" role="dialog" >
  	<div class="modal-dialog">
    	<div class="modal-content">
      		<div class="modal-header">
      			<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Available Stock</h4>
      		</div>
      		<div class="modal-body">
		        <table class="table table-striped table-bordered custom-table text_box_in_table" id="supp_material_table" style="margin-top: 15px;">
					<thead>
						<tr>
							<th width="10%">S.No</th>
							<th width="50%">Make</th>
							<th>Stock Qty</th>
						</tr>
					</thead>
					<tbody></tbody>
					<tfoot>
						<tr>
							<td colspan="2" class="text-right grand-total-text">Total</td>
							<td class="text-right grand-total-amount" id="closing_stock_total" style="padding-right: 15px;">0</td>
						</tr>
					</tfoot>
				</table>
	        </div>
		    <div class="modal-footer">
			    <span class="btn btn-cancel" data-dismiss="modal">Close</span>
		    </div>
	    </div>
    </div>
</div>

<div id="add_new_alternate_unit" class="modal fade" role="dialog" data-backdrop="static" data-keyboard="false">
  	<div class="modal-dialog" id="alternate_measure" style="width : 750px;">
    	<div class="modal-content" >
      		<div class="modal-header">
      			<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">ALTERNATE UNITS OF MEASUREMENT</h4>
      		</div>
      		<div class="modal-body">
		        <div class="row remove-margin">
			         <div hidden="hidden">
						<input type="text" value="0" hidden="hidden" />
						{{ alternate_unit_formset.empty_form.enterprise_id }}
				        {{ alternate_unit_formset.empty_form.primary_unit_name }}
					 </div>
		        	 <div class="form-group" style="width: 40%; float: left;">
		        	 	<label>Unit<span class="mandatory_mark"> *</span></label>
		                {{ alternate_unit_formset.empty_form.alternate_unit_id }}
			        </div>
			        <div class="form-group text-center" style="width: 10%; float: left;margin-top: 18px; font-size: 24px;">
	        			=
			        </div>
			        <div class="form-group" style="width: 44%; float: left;">
			        	<label>Scale Factor<span class="mandatory_mark"> *</span></label>
				        {{ alternate_unit_formset.empty_form.scale_factor }}
		                <label class="unit_display pull-right" id="alternate_primary_unit"></label>
			        </div>
			        <div class="form-group" style="width: 6%; float: left;margin-top: 18px;">
			        	<a href="javascript:void(0);" style="float: right" onclick="addNewAlternateUnit()" id="addNewUnit" class="btn btn-save">+</a>
			        </div>

			        <div class="clearfix"></div>
			        <hr style="margin-left: -15px; margin-right: -15px; margin-top: 10px;" />
			        <div class="">
						{{alternate_unit_formset.management_form}}
						<table class="table table-striped custom-table text_box_in_table" id="alternate_unit_table">
							<tbody>
								<tr bgcolor="#ececec" id="alternate_unit-__dummy__" hidden="hidden">
									<td hidden="hidden" class="exclude_export" style="border-top: none;">
										{{ alternate_unit_formset.empty_form.DELETE }}
										{{ alternate_unit_formset.empty_form.enterprise_id }}
										{{ alternate_unit_formset.empty_form.alternate_unit_id }}

									</td>
									<td class='td-alternate-unit-name' style="width: 40%; ">
										{{ alternate_unit_formset.empty_form.unit_name }}
									</td>
									<td class="text-center" style="width: 10%; margin-top: 18px; font-size: 24px;">
										=
									</td>
									<td style="width: 45%; ">
										{{ alternate_unit_formset.empty_form.scale_factor }}
										<label class="unit_display unit_display-small pull-right" id="id_{{ alternate_unit_formset.empty_form.prefix }}-primary_unit_name"></label>
									</td>
									<td class="text-right" style="width: 5%; margin-top: 18px;">
										<a role='button' id="id_{{ alternate_unit_formset.empty_form.prefix }}-deleteAlternateUnit"
										   onclick="javascript:isDeleteMaterialAlternateUnit(this, '{{ alternate_unit_formset.empty_form.prefix }}','{{ alternate_unit_formset.empty_form.alternate_unit_id.value }}')">
											<i class="fa fa-trash" style='color: red; font-size: 16px; padding: 4px; ' id='deleteImage_{{ alternate_unit_formset.empty_form.alternate_unit_id.value }}'></i>
										</a>
									</td>
								</tr>

								{% for alternate_unit in alternate_unit_formset.initial_forms %}
									<tr bgcolor="#ececec" id="{{ alternate_unit.prefix }}" style="border-top: none;">
										<td hidden="hidden" class="exclude_export">
											{{ alternate_unit.enterprise_id }}
											{{ alternate_unit.DELETE }}
											{{ alternate_unit.alternate_unit_id }}
										</td>
										<td class='td-alternate-unit-name' style="width: 40%;">
											{{ alternate_unit.unit_name.value }}
										</td>
										<td class="text-center" style="width: 10%; margin-top: 18px; font-size: 24px;">
											=
										</td>
										<td class="text-right" style="width: 45%; ">
											{{ alternate_unit.scale_factor }}
											<label class="unit_display unit_display-small pull-right" id="id_{{ alternate_unit.prefix }}-primary_unit_name"  title="{{ alternate_unit.primary_unit_name.value }}" >{{ alternate_unit.primary_unit_name.value }}</label>
											<input hidden="hidden" id="id_{{ alternate_unit.prefix }}-scale_factor_value" value="{{ alternate_unit.scale_factor.value }}"> </td>
										</td>
										<td class="text-right" style="width: 5%; margin-top: 18px;">
											<a role='button' id="id_{{ alternate_unit.prefix }}-deleteAlternateUnit"
											   onclick="javascript:isDeleteMaterialAlternateUnit(this, '{{ alternate_unit.prefix }}',{{ alternate_unit.alternate_unit_id.value}})">
												<i class="fa fa-trash" style='color: red; font-size: 16px; padding: 4px;' id='deleteImage_{{ alternate_unit.prefix }}'></i>
											</a>
										</td>
									</tr>
								{% endfor %}
							</tbody>
						</table>
					</div>
			    </div>
	        </div>
		    <div class="modal-footer">
				<a href="javascript:void(0);" class="btn btn-default" data-dismiss="modal">Close</a>
		    </div>
	    </div>
    </div>
</div>
<script type="text/javascript">
	$(document).ready(function() {
		if($('#id_material-material_id').val() !=""){
			if($("#is_super_user").val().toLowerCase() == 'false') {
				$("#id_material-drawing_no").attr("readonly", true);
			}
		}
		var urleditcheck = $(location).attr("href");
		if(urleditcheck.search("edit") != -1){
			$("#material-attachment-icon").show();
		}
		else {
			$("#material-attachment-icon").hide();
			$(".show-bom-icon, .show-price-profile-icon").addClass("hide");
		}
		PriceStatusChangeEvent();
		$("#id_material-effect_since").datepicker("setDate", new Date());

		setTimeout(function(){
			var selDate = $("#id_material-effect_since").val();
			$("#effect_till_date").datepicker("setStartDate", new Date(selDate));
			$("#supp_price_table").find(".effect_since_child").each(function(){
	            var selDate = $(this).prev("input").val();
	            $(this).closest(".effect_date_parent").find(".effect_till_child").datepicker("setStartDate", new Date(selDate));
	        })
		},3000);
		ValidateEffectDate();
		validateNewAttachment();
		materialAttachmentSubmit();
		listAttachments();
		validateFileType();
	});

	$(window).load(function(){
		setTimeout(function(){
			$("#id_material-unit_id").append("<option disabled>──────────</option><option value='0'>[+] New Unit</option>");
		},2000);
	});

	function addNewUnit(){
		$(".error-border").removeClass('error-border');
	    $(".custom-error-message").remove();

	    var ControlCollections = [
	        {
	            controltype: 'textbox',
	            controlid: 'unit_name',
	            isrequired: true,
	            errormsg: 'Unit Name is required.'
	        },
	        {
	            controltype: 'textbox',
	            controlid: 'unit_desc',
	            isrequired: true,
	            errormsg: 'Description is required.'
	        }
		];
		var result = JSCustomValidator.JSvalidate(ControlCollections);
		if(result) {
			$("#addNewUnit").addClass('btn-processing').text("Processing...");
			$.ajax({
	            url: "/erp/masters/json/materials/checkunit/",
	            type: "POST",
	            dataType: "json",
	            data:{unit_name:$("#unit_name").val()},
	            success: function (response) {
	            	if (response.response_message =="Success") {
						if(response.custom_message == "Unit already exists"){
	                        swal("", response.custom_message,"warning");
	                        $("#addNewUnit").removeClass('btn-processing').text("Add");
	                    }
	                    else {
	                        saveMaterialUnit();
	                    }
	               	}
	               	else {
	                    swal({title: "", text: response.custom_message, type: "error"});
	               }
	            },
	            error: function (xhr, errmsg, err) {
	                console.log(xhr.status + ": " + xhr.responseText);
	            }
	        });
		}
	}

	function saveMaterialUnit(){
		$.ajax({
	        url: "/erp/masters/json/materials/saveunit/",
	        type: "POST",
	        dataType: "json",
	        data:{unit_name:$("#unit_name").val(), unit_desc:$("#unit_desc").val()},
	        success: function (json) {
	            $("#addNewUnit").removeClass('btn-processing').text("Add");
	            swal("",json["message"],"success");
	            $("#unit_name, #unit_desc").val("");
				$("#add_new_unit_modal").modal('hide');
				populateMaterialUnit();
	        },
	        error: function (xhr, errmsg, err) {
	        	swal("","Server side error.","error");
	        	$("#add_new_unit_modal").modal('hide');
	        	$("#addNewUnit").removeClass('btn-processing').text("Add");
	            console.log(xhr.status + ": " + xhr.responseText);
	        }
	    });
	}

	function populateMaterialUnit(){
		$.ajax({
	        url: "/erp/masters/json/materials/populate_unit_choices/",
	        type: "POST",
	        dataType: "json",
	        data: "",
	        success: function(unit_options) {
	            option_html = "";
	            currentlyAddedvalue = "";
	            for(i=0; i < unit_options.length; i++){
	                option_html += "<option value='"+unit_options[i][0]+"'>" + unit_options[i][1] + "</option>";
	                if(Number(i+1) == unit_options.length) {
	                	currentlyAddedvalue = unit_options[i][0];
	                }
	            }
	            option_html += "<option disabled>──────────</option><option value='0'>[+] New Unit</option>";
	            $("#id_material-unit_id").html(option_html);
	            $("#id_material-unit_id").val(currentlyAddedvalue);
	        }
	    });
	}

	function cancelUnitAdd() {
		$("#id_material-unit_id").val(1);
		$("#add_new_unit_modal").modal('hide');
		$("#addNewUnit").removeClass('btn-processing').text("Add");
	}

	function showUploadMaterialModal() {
	    $("#upload_material_modal").modal('show');
	    $("#materialhideUploadButton").attr("disabled", true);
	    $("#upload_material_modal").find(".error-border").removeClass('error-border');
	    $("#upload_material_modal").find(".custom-error-message").remove();
	    var itemName = $('#id_material-name').val();
	    var url = window.location.href;
	    if(url.indexOf("edit") > 0) {
	    	var materialName = $(".header_current_page").text();
	    }
	    else {
	    	var materialName = itemName
	    }
	    $(".attachment-title").text(materialName);
	    $('#upload_material_modal').on('hidden.bs.modal', function () {
	    	$("#attachmentupload").filestyle('clear');
	    	$("#document_label").val("");
	    });
	}

	function closeMaterialUpload() {
		$("#attachmentupload").filestyle('clear');
		$("#upload_material_modal").modal("hide");
	}

	function PriceStatusChangeEvent() {
		$(".material_price_status").find("select").change(function(){
			var tr = $(this).closest('tr').next().next('tr.reject_remarks')
			if($(this).val() == "-1") {
				tr.removeClass('hide');
			}
			else {
				tr.addClass('hide');
			}
		});
	}

	function validateFileType(){
		$("#attachmentupload").change(function(){
			var fileExtension = ['csv', 'xls', 'xlsx', 'doc', 'docx', 'pdf', 'jpg', 'jpeg', 'png', 'svg', 'gif', 'txt', 'log', 'md', 'json'];
			if($("#attachmentupload").val()) {
		    	if ($.inArray($("#attachmentupload").val().split('.').pop().toLowerCase(), fileExtension) == -1) {
		    		$("#materialhideUploadButton").attr("disabled", true);
		    		swal("Invalid File Format", "Please upload only "+fileExtension.join(', ')+" file format.", "warning");
		    		setTimeout(function(){
		                $("#attachmentupload").filestyle('clear');
		    	   },200);
		    	}	
		    	else if(this.files[0].size/(1024*1024) > 16){
		    		$("#materialhideUploadButton").attr("disabled", true);
		    		swal("File Size Exceeds", "Please select a lower size file. Maximum file size limit is 16MB", "warning");
		    		setTimeout(function(){
		                $("#attachmentupload").filestyle('clear');
		    	   },200);
		    	}
		    	else {
		    		$("#materialhideUploadButton").removeAttr("disabled");
		    	}
		    }
		});
	}

	function deleteAttachmentFile(drawing_no, description, attachment_id) {
		var material_name = $("#searchResult").find("input[value='"+drawing_no+"']").closest("td").text();
	  	swal({
			title: "Are you sure?",
			text: "You want to remove the attachment <b>"+description+"</b> from the <b>"+material_name+"</b> - <b>["+drawing_no+"]</b> profile.",
			type: "warning",
			showCancelButton: true,
			confirmButtonColor: "#209be1",
			confirmButtonText: "Yes, delete it!",
			closeOnConfirm: true
		},
		function(isConfirm){
			if(isConfirm){
		    	deleteMaterialAttachment(drawing_no, attachment_id);
		    }
		});
	}

	function deleteMaterialAttachment(drawing_no, attachment_id){
	 	$.ajax({
	        url : "/erp/masters/json/material/delete_material_attachment/",
	        type : "POST",
	        dataType: "json",
	        data : {
	            drawing_no: drawing_no,
	            attachment_id: attachment_id
	        },
	        success : function(data) {
	         	if (data.response_code == 200) {
                    swal({title: "File Deleted Successfully", text: "Your Attachment has been successfully deleted", type: "success"});
				}
				else if(data.response_code == 400){
	                swal({title: "Not deleted.", text: "Attachment not saved successfully", type: "danger"});
				}
				listAttachments();
	        },error : function(xhr,errmsg,err) {
	            console.log(xhr.status + ": " + xhr.responseText);
	            $("#loading").addClass('hide').removeClass('show');
	        }
	    });
	}

	function validateNewAttachment(){
		$("#materialhideUploadButton").click(function(){
			$(".error-border").removeClass('error-border');
		    $(".custom-error-message").remove();

		     var ControlCollections = [
		        {
		            controltype: 'textbox',
		            controlid: 'document_label',
		            isrequired: true,
		            errormsg: 'Description is required.'
		        },
		        {
		            controltype: 'file',
		            controlid: 'attachmentupload',
		            isrequired: true,
		            errormsg: 'Attachment is required.'
		        }
			];
			var result = JSCustomValidator.JSvalidate(ControlCollections);
			return result;
		});
	}

	function inlineAttachmentUpload(materialName, itemId){
		$("#materials_attachment_container").find(".document_attachment_part").remove();
		$('#id_material-material_id').val(itemId);
		$('#id_material-name').val(materialName);
		showUploadMaterialModal();
		listAttachments();
	}

	function materialAttachmentSubmit(){
		$("#material_attachment_upload").submit(function(){
		    var formData = new FormData($(this)[0]);
		    formData.append('item_id', $('#id_material-material_id').val());
		    $.ajax({
		        url:"/erp/masters/json/material/material_attachment_upload/",
		        type: 'POST',
		        data: formData,
		        async: false,
		        cache: false,
		        contentType: false,
		        processData: false,
		        success: function (data) {
			        $("#attachmentupload").filestyle('clear');
			        $("#document_label").val('');
		    	    listAttachments();
			        if (data.response_code == 200) {
			            swal({
							title: "Attachment Saved Successfully",
							text: "Your Attachment has been saved successfully",
							type: "success",
							confirmButtonColor: "#209be1",
							confirmButtonText: "Ok",
							closeOnConfirm: true
						});
					}
		        },
		        error : function(xhr,errmsg,err) {
		            console.log(xhr.status + ": " + xhr.responseText);
		        }
		    });
		    $("#materialhideUploadButton").attr("disabled", true);
		});
	}

	function listAttachments(){
		var item_id = $('#id_material-material_id').val();
		if (item_id == null) {
			return;
		}
		$.ajax({
	        url : "/erp/masters/json/material/material_attachment_list/",
	        type : "POST",
	        dataType: "json",
	        data : {
	            item_id: item_id
	        },
	        success : function(data) {
		        $("#materials_attachment_container").find(".document_attachment_part").remove();
	            $.each( data, function( key, value ) {
				  	addAttachmentMaterial(value.drawing_no, value.description, value.attachment_id, value.file, value.enterprise_id, value.ext);
				});
	        },
	        error : function(xhr,errmsg,err) {
	            console.log(xhr.status + ": " + xhr.responseText);
	        }
	    });
	}

	function addAttachmentMaterial(drawing_no, description, attachment_id, file, enterprise_id, extension) {
		var row = '<div class="document_attachment_part" data-tooltip="tooltip" title="'+description+'">\
						<form action="/erp/commons/json/document/" method="post" target="_blank">\
						<input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}" />\
						<input type="hidden" name="document_uri" value="'+ file +'"/>\
						<span class="document_remove" onclick="deleteAttachmentFile(\''+ drawing_no +'\', \''+ description +'\', \''+ attachment_id +'\');"><i class="fa fa-times" aria-hidden="true"></i></span>\
						<span class="document_attachment '+extension.toLowerCase()+' linked_text" onclick="viewAttachedDocument(this)" data-extension="'+extension.toLowerCase()+'" data-url = "'+file+'"></span>\
						<span class="document_text">'+description+'</span>\
					</form></div>';
		$("#materials_attachment_container").append(row);
		TooltipInit();
		$("#attachmentupload").filestyle({buttonBefore: true});
	}

	function ValidateEffectDate(){
		$('.effect_since_child').on('hide.datepicker', function(ev, picker) {
			var selectedDate = $(this).prev("input").val();
			$(this).closest(".effect_date_parent").find(".effect_till_child").datepicker("setStartDate", new Date(selectedDate));
			var fit_end_time = $(this).closest(".effect_date_parent").find(".effect_till_child").prev("input").val();
			if(new Date(selectedDate) > new Date(fit_end_time)) {
				$(this).closest(".effect_date_parent").find(".effect_till_child").prev("input").val("");
				$(this).closest(".effect_date_parent").find(".effect_till_child").val("");
			}
		});
	}

	function validateBOMDownload(tableThis) {
		if($("#materialtable tbody").find("tr.newly_added_item:visible").length <= 0) {
			GeneralExportTableToCSV.apply(tableThis, [$('#materialtable'), '{{ material_form.drawing_no.value }}-BoM.csv']);
		}
		else {
			swal("Unable to download", "Newly added item(s) are yet to be saved. Kindly save this material to download the complete BoM.", "warning");
		}
	}

	function costingBOMDownload(tableThis) {
		if($("#cheapest_supplier_bom tbody").find("tr.newly_added_item:visible").length <= 0) {
			GeneralExportTableToCSV.apply(tableThis, [$('#cheapest_supplier_bom'), '{{ material_form.drawing_no.value }}-CostingBoM.csv']);
		}
		else {
			swal("Unable to download", "Newly added item(s) are yet to be saved. Kindly save this material to download the complete BoM.", "warning");
		}
	}

	function editMaterialFromBom(drawingNumber){
		$("#edit_material_from_bom").find("#hdn_edit_material_from_bom").val(drawingNumber);
		$("#edit_material_from_bom").submit();
	}

	function updateSupplierPrice(priceProfileId, access_status){
		if($("#"+priceProfileId+"_effect_till").next("a").find("i").hasClass("fa-pencil")) {
			if (access_status != 'True'){
				$("#id_"+priceProfileId+"-status").val('0')
				$("#id_"+priceProfileId+"-status_select").val('0')
				$("#id_"+priceProfileId+"-status").closest("tr").find(".supplier_price_status").text("Pending")
			}
			$("#"+priceProfileId+"_effect_till").removeClass("hide").next("a").find("i").removeClass("fa-pencil").addClass("fa-times").css({color: "#dd4b39"});
		}
		else {
			$("#id_"+priceProfileId+"-status").val('1')
			$("#id_"+priceProfileId+"-status_select").val('1')
			$("#id_"+priceProfileId+"-effect_till").val('')
			$("#effect_from_since_"+priceProfileId).val("")
			$("#id_"+priceProfileId+"-status").closest("tr").find(".supplier_price_status").text("Approved")
			$("#"+priceProfileId+"_effect_till").addClass("hide").next("a").find("i").removeClass("fa-times").addClass("fa-pencil").css({color: "#337ab7"});
		}
	}

function openPriceProfileRemarks(current) {
	$(current).closest("tr").next("tr.general-remarks").toggleClass("hide");
}

</script>
