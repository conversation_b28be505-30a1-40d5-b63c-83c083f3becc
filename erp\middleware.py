"""
XS<PERSON>p - <PERSON>'s Core Middleware
"""
import json
from datetime import datetime

from django.http import HttpResponse
from django.shortcuts import render_to_response
from django.template.context import RequestContext

from erp import logger
from erp.auth import USER_IN_SESSION_KEY, HOME_CURRENCY_KEY, E<PERSON><PERSON><PERSON>ISE_IN_SESSION_KEY, ENTERPRISE_SUBSCRIPTION, \
	BASIC_MATERIAL_PRICE_KEY, STANDARD_MATERIAL_PRICE_KEY
from erp.auth.request_handler import RequestHandler
from erp.auth.views import logout
from erp.models import Module
from erp.properties import LOGIN_URL, SESSION_EXPIRY_TEMPLATE, ERROR_500_TEMPLATE, JSON_LOGIN_URL, AUTH_CHANGE_PWD, \
	AUTH_FORGET_PWD, TEMPLATE_TITLE_KEY, AUTH_CHANGE_PASSWORD, AUTH_REGISTER_ENTERPRISE, BROADCAST_URL, VERSION_INFO, \
	CONTACT_MAIL, AUTH_FORGET_PASSWORD, FEED<PERSON>CK_MAIL, AUTH_VALIDATE_EMAIL, SALES_ESTIMATE_STATUS_TEMPLATE, \
	VALIDATE_AND_REGISTER_ENTERPRISE, NOTE_STATUS_TEMPLATE, SEND_SIGN_UP_MAIL
from settings import ORMSessionFactory, SQLASession, ANALYTICS_CODE, CURRENT_VERSION
from util.api_util import response_code

__author__ = 'kalaivanan'

LOGIN_EXEMPT_URLS = (
	BROADCAST_URL,
	VERSION_INFO,
	JSON_LOGIN_URL,
	AUTH_CHANGE_PASSWORD,
	AUTH_FORGET_PASSWORD,
	AUTH_CHANGE_PWD,
	AUTH_FORGET_PWD,
	AUTH_REGISTER_ENTERPRISE,
	VALIDATE_AND_REGISTER_ENTERPRISE,
	SEND_SIGN_UP_MAIL,
	u'/erp/css/',  # CSS Files
	u'/site_media',  # Image & other media files
	u'/erp/login/',
	u'/erp/public/contact/',
	CONTACT_MAIL,
	FEEDBACK_MAIL,
	AUTH_VALIDATE_EMAIL,
	u'/erp/sales/sales_estimate/client_status/',
	SALES_ESTIMATE_STATUS_TEMPLATE,
	u'/erp/auth/send_query_mail/',
	u'/erp/admin/pay/failure/',
	u'/erp/admin/pay/success/',
	u'/erp/auditing/json/party_status/',
	NOTE_STATUS_TEMPLATE,
)

ACTION_MODULES = SQLASession().query(Module).order_by(Module.action)
EXCEPTION_RESPONSE_KEY = 'exception_response'


class CoreMiddleware(object):
	"""
	* Application Middleware - Handles the following processes (not limited-to):
	* Authentication and Access Monitoring - Processes all requests, if not authenticated, such requests will be
	restricted & redirected to Login Page.
	* Handle SQLAlchemy Connection sessions. Objective is to provide request scoped ORM Sessions for fetching
	"""

	def process_request(self, request):
		"""
		Authenticates each request as to whether the request was from an active Login Session. If the request was
		identified to be unauthenticated, the request will be redirected to Login Page.

		:param request:
		:return: the request will forwarded to the Page/Action if the Login Session is active, else it will be
				redirected to Login Page.
		"""
		# Check exempted urls like js, css, media etc from web authentication
		request_handler = RequestHandler(request)
		if request.path in ("", "/") or "/public/" in request.path:
			logger.info(request.path)
			return
		for exempt_url in LOGIN_EXEMPT_URLS:
			if request.path.startswith(exempt_url):
				if "site_media" not in request.path:
					logger.info('Request exempted from authentication - %s' % request.path)
				return

		# Check if the login session is active, only after exempting all the URL's that does not require authentication
		logger.info('HOST: %s, URL: %s' % (request.get_host(), request.path))
		if request.path in (LOGIN_URL, JSON_LOGIN_URL) or request_handler.isSessionActive():
			return
		logout(request)
		if "/json/" in request.path:
			return HttpResponse(json.dumps(response_code.sessionTimeout()), 'content-type=text/json')
		else:
			return render_to_response(
				SESSION_EXPIRY_TEMPLATE, {"message": "Your session has expired!", "current_version": CURRENT_VERSION},
				context_instance=RequestContext(request))

	def process_template_response(self, request, response):
		"""
		Identifies the Application Module that the user has requested from the URL. Attaches the User Permission for the
		Module requested to the Response. Such Permission data will be used to authenticate the request and authorizes
		permission for appropriate actions allowed.

		:param request:
		:param response:
		:return: the requested Template additionally loaded with the appropriate Access Permission for the User
		"""
		path = request.path
		try:
			module_code = 'DEFAULT'
			path = '/erp/purchase/po/reports/' if path == '/erp/reports/purchase/' else path
			for action_module in ACTION_MODULES:
				# Deeper the match, more appropriate is the Module
				if path.startswith(action_module.action):
					module_code = action_module.code
					break
			logged_in_user = request.session[USER_IN_SESSION_KEY]
			enterprise = request.session[ENTERPRISE_IN_SESSION_KEY]
			response.context_data['logged_in_user'] = logged_in_user
			access_level = logged_in_user.getAccessLevels(module_code)
			module_access = logged_in_user.getModuleAccess(enterprise.id, request)  # enterprise level access.
			is_erase_data_expired = True
			if (enterprise.erase_data_expiry_date and enterprise.erase_data_expiry_date >= datetime.now()) or (
					enterprise.created_on and (datetime.now() - enterprise.created_on).days <= 30):
				is_erase_data_expired = False
			request.session['module_access'] = module_access
			response.context_data['module_access'] = module_access
			response.context_data['access_level'] = access_level

			response.context_data['basic_material_price'] = request.session[BASIC_MATERIAL_PRICE_KEY]
			response.context_data['standard_material_price'] = request.session[STANDARD_MATERIAL_PRICE_KEY]
			response.context_data['subscription'] = request.session[ENTERPRISE_SUBSCRIPTION]
			response.context_data['home_currency'] = request.session[HOME_CURRENCY_KEY]
			response.context_data['analytics_code'] = ANALYTICS_CODE
			response.context_data['current_version'] = CURRENT_VERSION
			response.context_data['page_loaded_on'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
			response.context_data['is_erase_data_expired'] = is_erase_data_expired
			if TEMPLATE_TITLE_KEY not in response.context_data:
				response.context_data[TEMPLATE_TITLE_KEY] = "XSerp"
			logger.info('Request Path: %s |Module: %s | Access: %s' % (path, module_code, access_level))
		except Exception as e:
			logger.info('Could not attach permission for path - %s' % [path, e.message])
		return response

	def process_exception(self, request, exception):
		"""
		Captures and Handles application exceptions and other Library exceptions, especially DB/ORM connection & session
		related ones that makes application inaccessible once thrown.

		:param request:
		:param exception:
		:return:
		"""
		from pymysql import OperationalError, IntegrityError
		from sqlalchemy.exc import InvalidRequestError, ProgrammingError
		from sqlalchemy.orm.exc import DetachedInstanceError
		print("error >>>",exception.message)
		if "/site_media/" in exception.message:
			# logger.error("%s" % exception.message)
			pass
		else:
			pass
			# logger.exception(
			# 	"Exception Handling: %s\n\nHandle-able Exception:%s\n\n" % (exception, isinstance(exception, (
			# 		InvalidRequestError, ProgrammingError, IntegrityError, DetachedInstanceError, OperationalError))))
		if isinstance(exception, (InvalidRequestError, ProgrammingError, IntegrityError)): pass
			# logger.info("Exception Handling - Rolling Back the Previous State of DB Session.")
		elif isinstance(exception, (DetachedInstanceError, OperationalError)):
			ORMSessionFactory.close_all()
		import sys
		exc_type, value, tb = sys.exc_info()
		try:
			exception_response = request.session[EXCEPTION_RESPONSE_KEY]
			request.session.__delitem__(EXCEPTION_RESPONSE_KEY)
			return exception_response
		except KeyError:
			logger.warn("Redirecting from %s after Handling Exception" % request.path)
			logger.debug(request)
			from datetime import datetime
			from django.template.response import TemplateResponse
			from dateutil.relativedelta import relativedelta
			import time
			import traceback
			return TemplateResponse(
				request=request, template=ERROR_500_TEMPLATE,
				context={"error_trace": "[%s - %s] <%s> %s\n" % (
					request.META.get('REMOTE_ADDR'),
					datetime.now(), exception.message,
					traceback.format_exception(etype=exc_type, value=value, tb=tb, limit=5))})
