<div class="hsn_summary_contianer">
    <h6><b class="hsn_summary_title">HSN SUMMARY</b></h6>
    <atable class="table table-bordered row-seperator column-seperator hsn_table hsn_summary" style="font-size:11px;width: 100%;">
        <athead>
            <atr class="row_seperator column_seperator header_shading">
                <ath class="text-center td_sno" rowspan="2" style="width: 6%">S.No</ath>
                <ath class="text-center" rowspan="2" style="width: 28%">HSN/SAC</ath>
                <ath class="text-center td_tax" rowspan="2" style="width: 18%">Taxable Value</ath>
            </atr>
        </athead>
        <atbody>
            [% for summary in hsn_summary %]
                [%  if forloop.counter|divisibleby:2 %]
				    <atr class="row_seperator column_seperator row_shading">
				[% else %]
				    <atr class="row_seperator column_seperator row_shading" style="background: #ffffff;">
				[% endif %]
                    <atd class="text-center td_sno">{[forloop.counter]}.</atd>
                    <atd class="text-center">{[ summary.hsn_code ]}</atd>
                    <atd class="text-right td_tax">{[ summary.consolidated_taxable_value|floatformat:2 ]}</atd>
                    </atr>
            [% endfor %]

        </atbody>
    </atable>
</div>