"""
"""
from __future__ import absolute_import
import logging
from .celery_config import app as celery_app

from .template_tag_filters import *

__all__ = ['celery_app']

__author__ = 'moh<PERSON><PERSON><PERSON><PERSON>'

DEFAULT_MAKE_ID = 1
DRAFT_VOUCHER = 0
APPROVED_VOUCHER = 1

DRAFT_ORDER_ACKNOWLEDGEMENT = 0
APPROVED_ORDER_ACKNOWLEDGEMENT = 1

DRAFT_INVOICE = 0
APPROVED_INVOICE = 1

IS_FAULTY_FALSE = 0
IS_FAULTY_TRUE = 1

logger = logging.getLogger(__name__)
