(function(){var a="\n//# sourceURL=",k="' of type ",n='<script type="text/javascript" src="',p="SCRIPT",r="array",t="complete",u="function",v="google.charts.load",w="hasOwnProperty",x="number",y="object",z="pre-45",A="propertyIsEnumerable",B="string",C="text/javascript",D="toLocaleString";function E(){return function(b){return b}}function F(){return function(){}}function G(b){return function(){return this[b]}}var I,J=J||{};J.scope={};
J.Up=function(b,c,d){if(null==b)throw new TypeError("The 'this' value for String.prototype."+d+" must not be null or undefined");if(c instanceof RegExp)throw new TypeError("First argument to String.prototype."+d+" must not be a regular expression");return b+""};J.Gh=!1;J.fm=!1;J.gm=!1;J.defineProperty=J.Gh||typeof Object.defineProperties==u?Object.defineProperty:function(b,c,d){b!=Array.prototype&&b!=Object.prototype&&(b[c]=d.value)};
J.Ij=function(b){return"undefined"!=typeof window&&window===b?b:"undefined"!=typeof global&&null!=global?global:b};J.global=J.Ij(this);J.Sk=function(b){if(b){for(var c=J.global,d=["Promise"],e=0;e<d.length-1;e++){var f=d[e];f in c||(c[f]={});c=c[f]}d=d[d.length-1];e=c[d];b=b(e);b!=e&&null!=b&&J.defineProperty(c,d,{configurable:!0,writable:!0,value:b})}};
J.Fq=function(b,c,d){b instanceof String&&(b=String(b));for(var e=b.length,f=0;f<e;f++){var g=b[f];if(c.call(d,g,f,b))return{Zj:f,Ll:g}}return{Zj:-1,Ll:void 0}};J.yi="jscomp_symbol_";J.rg=function(){J.rg=F();J.global.Symbol||(J.global.Symbol=J.Symbol)};J.Symbol=function(){var b=0;return function(c){return J.yi+(c||"")+b++}}();
J.Fd=function(){J.rg();var b=J.global.Symbol.iterator;b||(b=J.global.Symbol.iterator=J.global.Symbol("iterator"));typeof Array.prototype[b]!=u&&J.defineProperty(Array.prototype,b,{configurable:!0,writable:!0,value:function(){return J.df(this)}});J.Fd=F()};J.df=function(b){var c=0;return J.uk(function(){return c<b.length?{done:!1,value:b[c++]}:{done:!0}})};J.uk=function(b){J.Fd();b={next:b};b[J.global.Symbol.iterator]=function(){return this};return b};
J.Qg=function(b){J.Fd();var c=b[Symbol.iterator];return c?c.call(b):J.df(b)};J.Yh=!1;
J.Sk(function(b){function c(b){this.$=g.wa;this.ia=void 0;this.Ub=[];var c=this.gd();try{b(c.resolve,c.reject)}catch(q){c.reject(q)}}function d(){this.Ma=null}function e(b){return b instanceof c?b:new c(function(c){c(b)})}if(b&&!J.Yh)return b;d.prototype.ef=function(b){null==this.Ma&&(this.Ma=[],this.Ni());this.Ma.push(b)};d.prototype.Ni=function(){var b=this;this.ff(function(){b.uj()})};var f=J.global.setTimeout;d.prototype.ff=function(b){f(b,0)};d.prototype.uj=function(){for(;this.Ma&&this.Ma.length;){var b=
this.Ma;this.Ma=[];for(var c=0;c<b.length;++c){var d=b[c];delete b[c];try{d()}catch(H){this.Oi(H)}}}this.Ma=null};d.prototype.Oi=function(b){this.ff(function(){throw b;})};var g={wa:0,Ja:1,ka:2};c.prototype.gd=function(){function b(b){return function(e){d||(d=!0,b.call(c,e))}}var c=this,d=!1;return{resolve:b(this.Xk),reject:b(this.Yd)}};c.prototype.Xk=function(b){if(b===this)this.Yd(new TypeError("A Promise cannot resolve to itself"));else if(b instanceof c)this.pl(b);else{a:switch(typeof b){case y:var d=
null!=b;break a;case u:d=!0;break a;default:d=!1}d?this.Wk(b):this.If(b)}};c.prototype.Wk=function(b){var c=void 0;try{c=b.then}catch(q){this.Yd(q);return}typeof c==u?this.ql(c,b):this.If(b)};c.prototype.Yd=function(b){this.mh(g.ka,b)};c.prototype.If=function(b){this.mh(g.Ja,b)};c.prototype.mh=function(b,c){if(this.$!=g.wa)throw Error("Cannot settle("+b+", "+c|"): Promise already settled in state"+this.$);this.$=b;this.ia=c;this.wj()};c.prototype.wj=function(){if(null!=this.Ub){for(var b=this.Ub,
c=0;c<b.length;++c)b[c].call(),b[c]=null;this.Ub=null}};var h=new d;c.prototype.pl=function(b){var c=this.gd();b.fc(c.resolve,c.reject)};c.prototype.ql=function(b,c){var d=this.gd();try{b.call(c,d.resolve,d.reject)}catch(H){d.reject(H)}};c.prototype.then=function(b,d){function e(b,c){return typeof b==u?function(c){try{f(b(c))}catch(ca){g(ca)}}:c}var f,g,h=new c(function(b,c){f=b;g=c});this.fc(e(b,f),e(d,g));return h};c.prototype["catch"]=function(b){return this.then(void 0,b)};c.prototype.fc=function(b,
c){function d(){switch(e.$){case g.Ja:b(e.ia);break;case g.ka:c(e.ia);break;default:throw Error("Unexpected state: "+e.$);}}var e=this;null==this.Ub?h.ef(d):this.Ub.push(function(){h.ef(d)})};c.resolve=e;c.reject=function(b){return new c(function(c,d){d(b)})};c.race=function(b){return new c(function(c,d){for(var f=J.Qg(b),g=f.next();!g.done;g=f.next())e(g.value).fc(c,d)})};c.all=function(b){var d=J.Qg(b),f=d.next();return f.done?e([]):new c(function(b,c){function g(c){return function(d){h[c]=d;l--;
0==l&&b(h)}}var h=[],l=0;do h.push(void 0),l++,e(f.value).fc(g(h.length-1),c),f=d.next();while(!f.done)})};return c});var K=K||{};K.global=this;K.R=function(b){return void 0!==b};K.L=function(b){return typeof b==B};K.ck=function(b){return"boolean"==typeof b};K.Rb=function(b){return typeof b==x};
K.md=function(b,c,d){b=b.split(".");d=d||K.global;b[0]in d||!d.execScript||d.execScript("var "+b[0]);for(var e;b.length&&(e=b.shift());)!b.length&&K.R(c)?d[e]=c:d=d[e]&&d[e]!==Object.prototype[e]?d[e]:d[e]={}};K.define=function(b,c){K.md(b,c)};K.ea=!0;K.ba="en";K.$c=!0;K.wi=!1;K.Uh=!K.ea;K.De=!1;K.Es=function(b){if(K.Kd())throw Error("goog.provide can not be used within a goog.module.");K.qf(b)};K.qf=function(b,c){K.md(b,c)};K.Di=/^[a-zA-Z_$][a-zA-Z0-9._$]*$/;
K.Td=function(b){if(!K.L(b)||!b||-1==b.search(K.Di))throw Error("Invalid module identifier");if(!K.Kd())throw Error("Module "+b+" has been loaded incorrectly. Note, modules cannot be loaded as normal scripts. They require some kind of pre-processing step. You're likely trying to load a module via a script tag or as a part of a concatenated bundle without rewriting the module. For more info see: https://github.com/google/closure-library/wiki/goog.module:-an-ES6-module-like-alternative-to-goog.provide.");
if(K.na.Ud)throw Error("goog.module may only be called once per module.");K.na.Ud=b};K.Td.get=function(){return null};K.Td.$q=function(){return null};K.na=null;K.Kd=function(){return null!=K.na};K.Td.jd=function(){K.na.jd=!0};K.rt=function(b){if(K.Uh)throw b=b||"",Error("Importing test-only code into non-debug environment"+(b?": "+b:"."));};K.Lq=F();K.rb=function(b){b=b.split(".");for(var c=K.global,d=0;d<b.length;d++)if(c=c[b[d]],!K.cb(c))return null;return c};
K.kr=function(b,c){c=c||K.global;for(var d in b)c[d]=b[d]};K.hp=function(b,c,d,e){if(K.Ae){var f;b=b.replace(/\\/g,"/");var g=K.la;e&&"boolean"!==typeof e||(e=e?{module:"goog"}:{});for(var h=0;f=c[h];h++)g.Sb[f]=b,g.Od[b]=e;for(e=0;c=d[e];e++)b in g.gb||(g.gb[b]={}),g.gb[b][c]=!0}};K.Ut=!1;K.Xm=!0;K.Ek=function(b){K.global.console&&K.global.console.error(b)};K.Qs=F();K.La="";K.eb=F();K.gp=function(){throw Error("unimplemented abstract method");};
K.ip=function(b){b.Gd=void 0;b.Zq=function(){if(b.Gd)return b.Gd;K.ea&&(K.wg[K.wg.length]=b);return b.Gd=new b}};K.wg=[];K.fi=!0;K.ti=K.ea;K.Ck={};K.Ae=!1;K.Ve="detect";K.zi="transpile.js";
K.Ae&&(K.la={Od:{},Sb:{},gb:{},zh:{},je:{},pb:{}},K.qg=function(){var b=K.global.document;return null!=b&&"write"in b},K.xj=function(){if(K.R(K.global.ye)&&K.L(K.global.ye))K.La=K.global.ye;else if(K.qg()){var b=K.global.document,c=b.currentScript;b=c?[c]:b.getElementsByTagName(p);for(c=b.length-1;0<=c;--c){var d=b[c].src,e=d.lastIndexOf("?");e=-1==e?d.length:e;if("base.js"==d.substr(e-7,7)){K.La=d.substr(0,e-7);break}}}},K.Ed=function(b,c){(K.global.zm||K.Tl)(b,c)&&(K.la.je[b]=!0)},K.di=!(K.global.atob||
!K.global.document||!K.global.document.all),K.$g=!1,K.ak=function(b,c,d){K.Ed("",'goog.retrieveAndExec_("'+b+'", '+c+", "+d+");")},K.Wd=[],K.Yt=function(b,c){return K.fi&&K.R(K.global.JSON)?"goog.loadModule("+K.global.JSON.stringify(c+a+b+"\n")+");":'goog.loadModule(function(exports) {"use strict";'+c+"\n;return exports});\n//# sourceURL="+b+"\n"},K.Ak=function(){var b=K.Wd.length;if(0<b){var c=K.Wd;K.Wd=[];for(var d=0;d<b;d++)K.Tg(c[d])}K.$g=!1},K.ks=function(b){K.Bg(b)&&K.Ji(b)&&K.Tg(K.La+K.zd(b))},
K.Bg=function(b){var c=(b=K.zd(b))&&K.la.Od[b]||{},d=c.lang||"es3";return b&&("goog"==c.module||K.Xg(d))?K.La+b in K.la.pb:!1},K.Ji=function(b){if((b=K.zd(b))&&b in K.la.gb)for(var c in K.la.gb[b])if(!K.mk(c)&&!K.Bg(c))return!1;return!0},K.Tg=function(b){if(b in K.la.pb){var c=K.la.pb[b];delete K.la.pb[b];K.Tj(c)}},K.es=F(),K.Sl=function(b){K.global.document.write(n+b+'">\x3c/script>')},K.Ki=function(b){var c=K.global.document,d=c.createElement("script");d.type=C;d.src=b;d.defer=!1;d.async=!1;c.head.appendChild(d)},
K.Tl=function(b,c){if(K.qg()){var d=K.global.document;if(!K.De&&d.readyState==t){if(/\bdeps.js$/.test(b))return!1;throw Error('Cannot write "'+b+'" after document load');}void 0===c?K.di?(K.$g=!0,c=" onreadystatechange='goog.onScriptLoad_(this, "+ ++K.Pg+")' ",d.write(n+b+'"'+c+">\x3c/script>")):K.De?K.Ki(b):K.Sl(b):d.write('<script type="text/javascript">'+K.Tk(c)+"\x3c/script>");return!0}return!1},K.Tk=function(b){return b.replace(/<\/(SCRIPT)/ig,"\\x3c/$1")},K.Xg=function(b){if("always"==K.Ve)return!0;
if("never"==K.Ve)return!1;K.Dc||(K.Dc=K.ej());if(b in K.Dc)return K.Dc[b];throw Error("Unknown language mode: "+b);},K.Dc=null,K.Pg=0,K.ys=function(b,c){b.readyState==t&&K.Pg==c&&K.Ak();return!0},K.Zt=function(b){function c(b){if(!(b in f.je||b in f.zh)){f.zh[b]=!0;if(b in f.gb)for(var g in f.gb[b])if(!K.mk(g))if(g in f.Sb)c(f.Sb[g]);else throw Error("Undefined nameToPath for "+g);b in e||(e[b]=!0,d.push(b))}}var d=[],e={},f=K.la;c(b);for(b=0;b<d.length;b++){var g=d[b];K.la.je[g]=!0}var h=K.na;K.na=
null;for(b=0;b<d.length;b++)if(g=d[b]){var l=f.Od[g]||{},m=K.Xg(l.lang||"es3");"goog"==l.module||m?K.ak(K.La+g,"goog"==l.module,m):K.Ed(K.La+g)}else throw K.na=h,Error("Undefined script input");K.na=h},K.zd=function(b){return b in K.la.Sb?K.la.Sb[b]:null},K.xj(),K.global.Am||K.Ed(K.La+"deps.js"));K.Cd=null;K.Jl=function(){if(null==K.Cd){try{var b=!eval('"use strict";let x = 1; function f() { return typeof x; };f() == "number";')}catch(c){b=!1}K.Cd=b}return K.Cd};
K.Ql=function(b){return"(function(){"+b+"\n;})();\n"};K.ds=function(b){var c=K.na;try{K.na={Ud:void 0,jd:!1};if(K.ya(b))var d=b.call(void 0,{});else if(K.L(b))K.Jl()&&(b=K.Ql(b)),d=K.zk.call(void 0,b);else throw Error("Invalid module definition");var e=K.na.Ud;if(!K.L(e)||!e)throw Error('Invalid module name "'+e+'"');K.na.jd?K.qf(e,d):K.ti&&Object.seal&&typeof d==y&&null!=d&&Object.seal(d);K.Ck[e]=d}finally{K.na=c}};K.zk=function(b){eval(b);return{}};
K.rs=function(b){b=b.split("/");for(var c=0;c<b.length;)"."==b[c]?b.splice(c,1):c&&".."==b[c]&&b[c-1]&&".."!=b[c-1]?b.splice(--c,2):c++;return b.join("/")};K.xk=function(b){if(K.global.Ph)return K.global.Ph(b);try{var c=new K.global.XMLHttpRequest;c.open("get",b,!1);c.send();return 0==c.status||200==c.status?c.responseText:null}catch(d){return null}};K.Ss=F();
K.Lt=function(b,c){var d=K.global.$jscomp;d||(K.global.$jscomp=d={});var e=d.he;if(!e){var f=K.La+K.zi,g=K.xk(f);if(g){eval(g+a+f);if(K.global.$gwtExport&&K.global.$gwtExport.$jscomp&&!K.global.$gwtExport.$jscomp.transpile)throw Error('The transpiler did not properly export the "transpile" method. $gwtExport: '+JSON.stringify(K.global.$gwtExport));K.global.$jscomp.he=K.global.$gwtExport.$jscomp.transpile;d=K.global.$jscomp;e=d.he}}if(!e){var h=" requires transpilation but no transpiler was found.";
h+=' Please add "//javascript/closure:transpiler" as a data dependency to ensure it is included.';e=d.he=function(b,c){K.Ek(c+h);return b}}return e(b,c)};
K.aa=function(b){var c=typeof b;if(c==y)if(b){if(b instanceof Array)return r;if(b instanceof Object)return c;var d=Object.prototype.toString.call(b);if("[object Window]"==d)return y;if("[object Array]"==d||typeof b.length==x&&"undefined"!=typeof b.splice&&"undefined"!=typeof b.propertyIsEnumerable&&!b.propertyIsEnumerable("splice"))return r;if("[object Function]"==d||"undefined"!=typeof b.call&&"undefined"!=typeof b.propertyIsEnumerable&&!b.propertyIsEnumerable("call"))return u}else return"null";
else if(c==u&&"undefined"==typeof b.call)return y;return c};K.Pr=function(b){return null===b};K.cb=function(b){return null!=b};K.isArray=function(b){return K.aa(b)==r};K.Nb=function(b){var c=K.aa(b);return c==r||c==y&&typeof b.length==x};K.Br=function(b){return K.ha(b)&&typeof b.getFullYear==u};K.ya=function(b){return K.aa(b)==u};K.ha=function(b){var c=typeof b;return c==y&&null!=b||c==u};K.kg=function(b){return b[K.Va]||(b[K.Va]=++K.Cl)};K.nr=function(b){return!!b[K.Va]};
K.Uk=function(b){null!==b&&"removeAttribute"in b&&b.removeAttribute(K.Va);try{delete b[K.Va]}catch(c){}};K.Va="closure_uid_"+(1E9*Math.random()>>>0);K.Cl=0;K.Yq=K.kg;K.Ms=K.Uk;K.aj=function(b){var c=K.aa(b);if(c==y||c==r){if(b.clone)return b.clone();c=c==r?[]:{};for(var d in b)c[d]=K.aj(b[d]);return c}return b};K.Si=function(b,c,d){return b.call.apply(b.bind,arguments)};
K.Ri=function(b,c,d){if(!b)throw Error();if(2<arguments.length){var e=Array.prototype.slice.call(arguments,2);return function(){var d=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(d,e);return b.apply(c,d)}}return function(){return b.apply(c,arguments)}};K.bind=function(b,c,d){K.bind=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?K.Si:K.Ri;return K.bind.apply(null,arguments)};
K.fb=function(b,c){var d=Array.prototype.slice.call(arguments,1);return function(){var c=d.slice();c.push.apply(c,arguments);return b.apply(this,c)}};K.ms=function(b,c){for(var d in c)b[d]=c[d]};K.now=K.$c&&Date.now||function(){return+new Date};
K.Tj=function(b){if(K.global.execScript)K.global.execScript(b,"JavaScript");else if(K.global.eval){if(null==K.lc)if(K.global.eval("var _evalTest_ = 1;"),"undefined"!=typeof K.global._evalTest_){try{delete K.global._evalTest_}catch(e){}K.lc=!0}else K.lc=!1;if(K.lc)K.global.eval(b);else{var c=K.global.document,d=c.createElement(p);d.type=C;d.defer=!1;d.appendChild(c.createTextNode(b));c.body.appendChild(d);c.body.removeChild(d)}}else throw Error("goog.globalEval not available");};K.lc=null;
K.Wq=function(b,c){function d(b){b=b.split("-");for(var c=[],d=0;d<b.length;d++)c.push(e(b[d]));return c.join("-")}function e(b){return K.uf[b]||b}if("."==String(b).charAt(0))throw Error('className passed in goog.getCssName must not start with ".". You passed: '+b);var f=K.uf?"BY_WHOLE"==K.kj?e:d:E();b=c?b+"-"+f(c):f(b);return K.global.Oh?K.global.Oh(b):b};K.bt=function(b,c){K.uf=b;K.kj=c};K.ar=function(b,c){c&&(b=b.replace(/\{\$([^}]+)}/g,function(b,e){return null!=c&&e in c?c[e]:b}));return b};
K.cr=E();K.zf=function(b,c){K.md(b,c,void 0)};K.Eq=function(b,c,d){b[c]=d};K.ab=function(b,c){function d(){}d.prototype=c.prototype;b.Lc=c.prototype;b.prototype=new d;b.prototype.constructor=b;b.Qi=function(b,d,g){for(var e=Array(arguments.length-2),f=2;f<arguments.length;f++)e[f-2]=arguments[f];return c.prototype[d].apply(b,e)}};
K.Qi=function(b,c,d){var e=arguments.callee.caller;if(K.wi||K.ea&&!e)throw Error("arguments.caller not defined.  goog.base() cannot be used with strict mode code. See http://www.ecma-international.org/ecma-262/5.1/#sec-C");if(e.Lc){for(var f=Array(arguments.length-1),g=1;g<arguments.length;g++)f[g-1]=arguments[g];return e.Lc.constructor.apply(b,f)}f=Array(arguments.length-2);for(g=2;g<arguments.length;g++)f[g-2]=arguments[g];g=!1;for(var h=b.constructor;h;h=h.Lc&&h.Lc.constructor)if(h.prototype[c]===
e)g=!0;else if(g)return h.prototype[c].apply(b,f);if(b[c]===e)return b.constructor.prototype[c].apply(b,f);throw Error("goog.base called from a method of one name to a method of a different name");};K.scope=function(b){if(K.Kd())throw Error("goog.scope is not supported within a goog.module.");b.call(K.global)};
K.oa=function(b,c){var d=c.constructor,e=c.ul;d&&d!=Object.prototype.constructor||(d=function(){throw Error("cannot instantiate an interface (no constructor defined).");});d=K.oa.fj(d,b);b&&K.ab(d,b);delete c.constructor;delete c.ul;K.oa.cf(d.prototype,c);null!=e&&(e instanceof Function?e(d):K.oa.cf(d,e));return d};K.oa.si=K.ea;
K.oa.fj=function(b,c){function d(){var c=b.apply(this,arguments)||this;c[K.Va]=c[K.Va];this.constructor===d&&e&&Object.seal instanceof Function&&Object.seal(c);return c}if(!K.oa.si)return b;var e=!K.oa.qk(c);return d};K.oa.qk=function(b){return b&&b.prototype&&b.prototype[K.Bi]};K.oa.Me=["constructor",w,"isPrototypeOf",A,D,"toString","valueOf"];
K.oa.cf=function(b,c){for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(b[d]=c[d]);for(var e=0;e<K.oa.Me.length;e++)d=K.oa.Me[e],Object.prototype.hasOwnProperty.call(c,d)&&(b[d]=c[d])};K.Et=F();K.Bi="goog_defineClass_legacy_unsealable";
K.ej=function(){function b(b,c){e?d[b]=!0:c()?d[b]=!1:e=d[b]=!0}function c(b){try{return!!eval(b)}catch(h){return!1}}var d={es3:!1},e=!1,f=K.global.navigator&&K.global.navigator.userAgent?K.global.navigator.userAgent:"";b("es5",function(){return c("[1,].length==1")});b("es6",function(){var b=f.match(/Edge\/(\d+)(\.\d)*/i);return b&&15>Number(b[1])?!1:c('(()=>{"use strict";class X{constructor(){if(new.target!=String)throw 1;this.x=42}}let q=Reflect.construct(X,[],String);if(q.x!=42||!(q instanceof String))throw 1;for(const a of[2,3]){if(a==2)continue;function f(z={a}){let a=0;return z.a}{function f(){return 0;}}return f()==3}})()')});
b("es6-impl",function(){return!0});b("es7",function(){return c("2 ** 2 == 4")});b("es8",function(){return c("async () => 1, true")});return d};K.debug={};K.debug.Error=function(b){if(Error.captureStackTrace)Error.captureStackTrace(this,K.debug.Error);else{var c=Error().stack;c&&(this.stack=c)}b&&(this.message=String(b))};K.ab(K.debug.Error,Error);K.debug.Error.prototype.name="CustomError";K.a={};K.a.fa={Ia:1,hm:2,cc:3,wm:4,Zm:5,Ym:6,oo:7,Fm:8,Xc:9,Rm:10,Vh:11,bo:12};K.f={};K.f.Wc=!1;K.f.Xh=!1;K.f.Ye={Ke:"\u00a0"};K.f.startsWith=function(b,c){return 0==b.lastIndexOf(c,0)};K.f.endsWith=function(b,c){var d=b.length-c.length;return 0<=d&&b.indexOf(c,d)==d};K.f.Zi=function(b){return 0==K.f.jf("tel:",b.substr(0,4))};K.f.Sp=function(b,c){return 0==K.f.jf(c,b.substr(b.length-c.length,c.length))};K.f.Tp=function(b,c){return b.toLowerCase()==c.toLowerCase()};
K.f.wl=function(b,c){for(var d=b.split("%s"),e="",f=Array.prototype.slice.call(arguments,1);f.length&&1<d.length;)e+=d.shift()+f.shift();return e+d.join("%s")};K.f.Zp=function(b){return b.replace(/[\s\xa0]+/g," ").replace(/^\s+|\s+$/g,"")};K.f.Id=function(b){return/^[\s\xa0]*$/.test(b)};K.f.Er=function(b){return 0==b.length};K.f.Qb=K.f.Id;K.f.ek=function(b){return K.f.Id(K.f.Jk(b))};K.f.Dr=K.f.ek;K.f.zr=function(b){return!/[^\t\n\r ]/.test(b)};K.f.wr=function(b){return!/[^a-zA-Z]/.test(b)};
K.f.Qr=function(b){return!/[^0-9]/.test(b)};K.f.xr=function(b){return!/[^a-zA-Z0-9]/.test(b)};K.f.Wr=function(b){return" "==b};K.f.Xr=function(b){return 1==b.length&&" "<=b&&"~">=b||"\u0080"<=b&&"\ufffd">=b};K.f.Ct=function(b){return b.replace(/(\r\n|\r|\n)+/g," ")};K.f.Yi=function(b){return b.replace(/(\r\n|\r|\n)/g,"\n")};K.f.ts=function(b){return b.replace(/\xa0|\s/g," ")};K.f.ss=function(b){return b.replace(/\xa0|[ \t]+/g," ")};
K.f.Yp=function(b){return b.replace(/[\t\r\n ]+/g," ").replace(/^[\t\r\n ]+|[\t\r\n ]+$/g,"")};K.f.trim=K.$c&&String.prototype.trim?function(b){return b.trim()}:function(b){return b.replace(/^[\s\xa0]+|[\s\xa0]+$/g,"")};K.f.trimLeft=function(b){return b.replace(/^[\s\xa0]+/,"")};K.f.trimRight=function(b){return b.replace(/[\s\xa0]+$/,"")};K.f.jf=function(b,c){b=String(b).toLowerCase();c=String(c).toLowerCase();return b<c?-1:b==c?0:1};
K.f.Zg=function(b,c,d){if(b==c)return 0;if(!b)return-1;if(!c)return 1;for(var e=b.toLowerCase().match(d),f=c.toLowerCase().match(d),g=Math.min(e.length,f.length),h=0;h<g;h++){d=e[h];var l=f[h];if(d!=l)return b=parseInt(d,10),!isNaN(b)&&(c=parseInt(l,10),!isNaN(c)&&b-c)?b-c:d<l?-1:1}return e.length!=f.length?e.length-f.length:b<c?-1:1};K.f.ur=function(b,c){return K.f.Zg(b,c,/\d+|\D+/g)};K.f.Aj=function(b,c){return K.f.Zg(b,c,/\d+|\.\d+|\D+/g)};K.f.ws=K.f.Aj;K.f.Tt=function(b){return encodeURIComponent(String(b))};
K.f.St=function(b){return decodeURIComponent(b.replace(/\+/g," "))};K.f.Yg=function(b,c){return b.replace(/(\r\n|\r|\n)/g,c?"<br />":"<br>")};
K.f.ta=function(b,c){if(c)b=b.replace(K.f.ke,"&amp;").replace(K.f.Je,"&lt;").replace(K.f.Ge,"&gt;").replace(K.f.Qe,"&quot;").replace(K.f.Te,"&#39;").replace(K.f.Le,"&#0;"),K.f.Wc&&(b=b.replace(K.f.Ee,"&#101;"));else{if(!K.f.Eh.test(b))return b;-1!=b.indexOf("&")&&(b=b.replace(K.f.ke,"&amp;"));-1!=b.indexOf("<")&&(b=b.replace(K.f.Je,"&lt;"));-1!=b.indexOf(">")&&(b=b.replace(K.f.Ge,"&gt;"));-1!=b.indexOf('"')&&(b=b.replace(K.f.Qe,"&quot;"));-1!=b.indexOf("'")&&(b=b.replace(K.f.Te,"&#39;"));-1!=b.indexOf("\x00")&&
(b=b.replace(K.f.Le,"&#0;"));K.f.Wc&&-1!=b.indexOf("e")&&(b=b.replace(K.f.Ee,"&#101;"))}return b};K.f.ke=/&/g;K.f.Je=/</g;K.f.Ge=/>/g;K.f.Qe=/"/g;K.f.Te=/'/g;K.f.Le=/\x00/g;K.f.Ee=/e/g;K.f.Eh=K.f.Wc?/[\x00&<>"'e]/:/[\x00&<>"']/;K.f.vh=function(b){return K.f.contains(b,"&")?!K.f.Xh&&"document"in K.global?K.f.wh(b):K.f.Fl(b):b};K.f.Pt=function(b,c){return K.f.contains(b,"&")?K.f.wh(b,c):b};
K.f.wh=function(b,c){var d={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"'};var e=c?c.createElement("div"):K.global.document.createElement("div");return b.replace(K.f.bi,function(b,c){var f=d[b];if(f)return f;"#"==c.charAt(0)&&(c=Number("0"+c.substr(1)),isNaN(c)||(f=String.fromCharCode(c)));f||(e.innerHTML=b+" ",f=e.firstChild.nodeValue.slice(0,-1));return d[b]=f})};
K.f.Fl=function(b){return b.replace(/&([^;]+);/g,function(b,d){switch(d){case "amp":return"&";case "lt":return"<";case "gt":return">";case "quot":return'"';default:return"#"!=d.charAt(0)||(d=Number("0"+d.substr(1)),isNaN(d))?b:String.fromCharCode(d)}})};K.f.bi=/&([^;\s<&]+);?/g;K.f.Ol=function(b){return K.f.Yg(b.replace(/  /g," &#160;"),void 0)};K.f.Ds=function(b){return b.replace(/(^|[\n ]) /g,"$1"+K.f.Ye.Ke)};
K.f.Dt=function(b,c){for(var d=c.length,e=0;e<d;e++){var f=1==d?c:c.charAt(e);if(b.charAt(0)==f&&b.charAt(b.length-1)==f)return b.substring(1,b.length-1)}return b};K.f.truncate=function(b,c,d){d&&(b=K.f.vh(b));b.length>c&&(b=b.substring(0,c-3)+"...");d&&(b=K.f.ta(b));return b};K.f.Nt=function(b,c,d,e){d&&(b=K.f.vh(b));e&&b.length>c?(e>c&&(e=c),b=b.substring(0,c-e)+"..."+b.substring(b.length-e)):b.length>c&&(e=Math.floor(c/2),b=b.substring(0,e+c%2)+"..."+b.substring(b.length-e));d&&(b=K.f.ta(b));return b};
K.f.de={"\x00":"\\0","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t","\x0B":"\\x0B",'"':'\\"',"\\":"\\\\","<":"<"};K.f.vc={"'":"\\'"};K.f.quote=function(b){b=String(b);for(var c=['"'],d=0;d<b.length;d++){var e=b.charAt(d),f=e.charCodeAt(0);c[d+1]=K.f.de[e]||(31<f&&127>f?e:K.f.xf(e))}c.push('"');return c.join("")};K.f.Dq=function(b){for(var c=[],d=0;d<b.length;d++)c[d]=K.f.xf(b.charAt(d));return c.join("")};
K.f.xf=function(b){if(b in K.f.vc)return K.f.vc[b];if(b in K.f.de)return K.f.vc[b]=K.f.de[b];var c=b.charCodeAt(0);if(31<c&&127>c)var d=b;else{if(256>c){if(d="\\x",16>c||256<c)d+="0"}else d="\\u",4096>c&&(d+="0");d+=c.toString(16).toUpperCase()}return K.f.vc[b]=d};K.f.contains=function(b,c){return-1!=b.indexOf(c)};K.f.kf=function(b,c){return K.f.contains(b.toLowerCase(),c.toLowerCase())};K.f.gq=function(b,c){return b&&c?b.split(c).length-1:0};
K.f.yb=function(b,c,d){var e=b;0<=c&&c<b.length&&0<d&&(e=b.substr(0,c)+b.substr(c+d,b.length-c-d));return e};K.f.remove=function(b,c){return b.replace(c,"")};K.f.Js=function(b,c){c=new RegExp(K.f.Xd(c),"g");return b.replace(c,"")};K.f.Ps=function(b,c,d){c=new RegExp(K.f.Xd(c),"g");return b.replace(c,d.replace(/\$/g,"$$$$"))};K.f.Xd=function(b){return String(b).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08")};
K.f.repeat=String.prototype.repeat?function(b,c){return b.repeat(c)}:function(b,c){return Array(c+1).join(b)};K.f.Bs=function(b,c,d){b=K.R(d)?b.toFixed(d):String(b);d=b.indexOf(".");-1==d&&(d=b.length);return K.f.repeat("0",Math.max(0,c-d))+b};K.f.Jk=function(b){return null==b?"":String(b)};K.f.Np=function(b){return Array.prototype.join.call(arguments,"")};K.f.fr=function(){return Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^K.now()).toString(36)};
K.f.Eb=function(b,c){var d=0;b=K.f.trim(String(b)).split(".");c=K.f.trim(String(c)).split(".");for(var e=Math.max(b.length,c.length),f=0;0==d&&f<e;f++){var g=b[f]||"",h=c[f]||"";do{g=/(\d*)(\D*)(.*)/.exec(g)||["","","",""];h=/(\d*)(\D*)(.*)/.exec(h)||["","","",""];if(0==g[0].length&&0==h[0].length)break;d=K.f.dd(0==g[1].length?0:parseInt(g[1],10),0==h[1].length?0:parseInt(h[1],10))||K.f.dd(0==g[2].length,0==h[2].length)||K.f.dd(g[2],h[2]);g=g[3];h=h[3]}while(0==d)}return d};
K.f.dd=function(b,c){return b<c?-1:b>c?1:0};K.f.or=function(b){for(var c=0,d=0;d<b.length;++d)c=31*c+b.charCodeAt(d)>>>0;return c};K.f.Gl=2147483648*Math.random()|0;K.f.pq=function(){return"goog_"+K.f.Gl++};K.f.Ht=function(b){var c=Number(b);return 0==c&&K.f.Id(b)?NaN:c};K.f.Jr=function(b){return/^[a-z]+([A-Z][a-z]*)*$/.test(b)};K.f.Yr=function(b){return/^([A-Z][a-z]*)+$/.test(b)};K.f.Gt=function(b){return String(b).replace(/\-([a-z])/g,function(b,d){return d.toUpperCase()})};
K.f.Jt=function(b){return String(b).replace(/([A-Z])/g,"-$1").toLowerCase()};K.f.Kt=function(b,c){c=K.L(c)?K.f.Xd(c):"\\s";return b.replace(new RegExp("(^"+(c?"|["+c+"]+":"")+")([a-z])","g"),function(b,c,f){return c+f.toUpperCase()})};K.f.Rp=function(b){return String(b.charAt(0)).toUpperCase()+String(b.substr(1)).toLowerCase()};K.f.parseInt=function(b){isFinite(b)&&(b=String(b));return K.L(b)?/^\s*-?0x/i.test(b)?parseInt(b,16):parseInt(b,10):NaN};
K.f.xt=function(b,c,d){b=b.split(c);for(var e=[];0<d&&b.length;)e.push(b.shift()),d--;b.length&&e.push(b.join(c));return e};K.f.as=function(b,c){if(c)typeof c==B&&(c=[c]);else return b;for(var d=-1,e=0;e<c.length;e++)if(""!=c[e]){var f=b.lastIndexOf(c[e]);f>d&&(d=f)}return-1==d?b:b.slice(d+1)};
K.f.xq=function(b,c){var d=[],e=[];if(b==c)return 0;if(!b.length||!c.length)return Math.max(b.length,c.length);for(var f=0;f<c.length+1;f++)d[f]=f;for(f=0;f<b.length;f++){e[0]=f+1;for(var g=0;g<c.length;g++)e[g+1]=Math.min(e[g]+1,d[g+1]+1,d[g]+Number(b[f]!=c[g]));for(g=0;g<d.length;g++)d[g]=e[g]}return e[c.length]};K.m={};K.m.ja=K.ea;K.m.Xb=function(b,c){c.unshift(b);K.debug.Error.call(this,K.f.wl.apply(null,c));c.shift()};K.ab(K.m.Xb,K.debug.Error);K.m.Xb.prototype.name="AssertionError";K.m.Sh=function(b){throw b;};K.m.kd=K.m.Sh;K.m.xa=function(b,c,d,e){var f="Assertion failed";if(d){f+=": "+d;var g=e}else b&&(f+=": "+b,g=c);b=new K.m.Xb(""+f,g||[]);K.m.kd(b)};K.m.ft=function(b){K.m.ja&&(K.m.kd=b)};K.m.assert=function(b,c,d){K.m.ja&&!b&&K.m.xa("",null,c,Array.prototype.slice.call(arguments,2));return b};
K.m.ma=function(b,c){K.m.ja&&K.m.kd(new K.m.Xb("Failure"+(b?": "+b:""),Array.prototype.slice.call(arguments,1)))};K.m.Ep=function(b,c,d){K.m.ja&&!K.Rb(b)&&K.m.xa("Expected number but got %s: %s.",[K.aa(b),b],c,Array.prototype.slice.call(arguments,2));return b};K.m.Hp=function(b,c,d){K.m.ja&&!K.L(b)&&K.m.xa("Expected string but got %s: %s.",[K.aa(b),b],c,Array.prototype.slice.call(arguments,2));return b};
K.m.sp=function(b,c,d){K.m.ja&&!K.ya(b)&&K.m.xa("Expected function but got %s: %s.",[K.aa(b),b],c,Array.prototype.slice.call(arguments,2));return b};K.m.Fp=function(b,c,d){K.m.ja&&!K.ha(b)&&K.m.xa("Expected object but got %s: %s.",[K.aa(b),b],c,Array.prototype.slice.call(arguments,2));return b};K.m.op=function(b,c,d){K.m.ja&&!K.isArray(b)&&K.m.xa("Expected array but got %s: %s.",[K.aa(b),b],c,Array.prototype.slice.call(arguments,2));return b};
K.m.pp=function(b,c,d){K.m.ja&&!K.ck(b)&&K.m.xa("Expected boolean but got %s: %s.",[K.aa(b),b],c,Array.prototype.slice.call(arguments,2));return b};K.m.qp=function(b,c,d){!K.m.ja||K.ha(b)&&b.nodeType==K.a.fa.Ia||K.m.xa("Expected Element but got %s: %s.",[K.aa(b),b],c,Array.prototype.slice.call(arguments,2));return b};K.m.tp=function(b,c,d,e){!K.m.ja||b instanceof c||K.m.xa("Expected instanceof %s but got %s.",[K.m.jg(c),K.m.jg(b)],d,Array.prototype.slice.call(arguments,3));return b};
K.m.rp=function(b,c,d){!K.m.ja||typeof b==x&&isFinite(b)||K.m.xa("Expected %s to be a finite number but it is not.",[b],c,Array.prototype.slice.call(arguments,2));return b};K.m.Gp=function(){for(var b in Object.prototype)K.m.ma(b+" should not be enumerable in Object.prototype.")};K.m.jg=function(b){return b instanceof Function?b.displayName||b.name||"unknown type name":b instanceof Object?b.constructor.displayName||b.constructor.name||Object.prototype.toString.call(b):null===b?"null":typeof b};K.f.Yo=F();K.f.I=function(){this.Kc="";this.xi=K.f.I.We};K.f.I.prototype.ua=!0;K.f.I.prototype.ga=G("Kc");K.f.I.prototype.toString=function(){return"Const{"+this.Kc+"}"};K.f.I.u=function(b){if(b instanceof K.f.I&&b.constructor===K.f.I&&b.xi===K.f.I.We)return b.Kc;K.m.ma("expected object of type Const, got '"+b+"'");return"type_error:Const"};K.f.I.from=function(b){return K.f.I.jj(b)};K.f.I.We={};K.f.I.jj=function(b){var c=new K.f.I;c.Kc=b;return c};K.f.I.EMPTY=K.f.I.from("");K.j={};K.Ca=K.$c;K.j.za=!1;K.j.Rk=function(b){return b[b.length-1]};K.j.$r=K.j.Rk;K.j.indexOf=K.Ca&&(K.j.za||Array.prototype.indexOf)?function(b,c,d){return Array.prototype.indexOf.call(b,c,d)}:function(b,c,d){d=null==d?0:0>d?Math.max(0,b.length+d):d;if(K.L(b))return K.L(c)&&1==c.length?b.indexOf(c,d):-1;for(;d<b.length;d++)if(d in b&&b[d]===c)return d;return-1};
K.j.lastIndexOf=K.Ca&&(K.j.za||Array.prototype.lastIndexOf)?function(b,c,d){return Array.prototype.lastIndexOf.call(b,c,null==d?b.length-1:d)}:function(b,c,d){d=null==d?b.length-1:d;0>d&&(d=Math.max(0,b.length+d));if(K.L(b))return K.L(c)&&1==c.length?b.lastIndexOf(c,d):-1;for(;0<=d;d--)if(d in b&&b[d]===c)return d;return-1};
K.j.forEach=K.Ca&&(K.j.za||Array.prototype.forEach)?function(b,c,d){Array.prototype.forEach.call(b,c,d)}:function(b,c,d){for(var e=b.length,f=K.L(b)?b.split(""):b,g=0;g<e;g++)g in f&&c.call(d,f[g],g,b)};K.j.Gf=function(b,c){for(var d=K.L(b)?b.split(""):b,e=b.length-1;0<=e;--e)e in d&&c.call(void 0,d[e],e,b)};
K.j.filter=K.Ca&&(K.j.za||Array.prototype.filter)?function(b,c,d){return Array.prototype.filter.call(b,c,d)}:function(b,c,d){for(var e=b.length,f=[],g=0,h=K.L(b)?b.split(""):b,l=0;l<e;l++)if(l in h){var m=h[l];c.call(d,m,l,b)&&(f[g++]=m)}return f};K.j.map=K.Ca&&(K.j.za||Array.prototype.map)?function(b,c,d){return Array.prototype.map.call(b,c,d)}:function(b,c,d){for(var e=b.length,f=Array(e),g=K.L(b)?b.split(""):b,h=0;h<e;h++)h in g&&(f[h]=c.call(d,g[h],h,b));return f};
K.j.reduce=K.Ca&&(K.j.za||Array.prototype.reduce)?function(b,c,d,e){e&&(c=K.bind(c,e));return Array.prototype.reduce.call(b,c,d)}:function(b,c,d,e){var f=d;K.j.forEach(b,function(d,h){f=c.call(e,f,d,h,b)});return f};K.j.reduceRight=K.Ca&&(K.j.za||Array.prototype.reduceRight)?function(b,c,d,e){e&&(c=K.bind(c,e));return Array.prototype.reduceRight.call(b,c,d)}:function(b,c,d,e){var f=d;K.j.Gf(b,function(d,h){f=c.call(e,f,d,h,b)});return f};
K.j.some=K.Ca&&(K.j.za||Array.prototype.some)?function(b,c,d){return Array.prototype.some.call(b,c,d)}:function(b,c,d){for(var e=b.length,f=K.L(b)?b.split(""):b,g=0;g<e;g++)if(g in f&&c.call(d,f[g],g,b))return!0;return!1};K.j.every=K.Ca&&(K.j.za||Array.prototype.every)?function(b,c,d){return Array.prototype.every.call(b,c,d)}:function(b,c,d){for(var e=b.length,f=K.L(b)?b.split(""):b,g=0;g<e;g++)if(g in f&&!c.call(d,f[g],g,b))return!1;return!0};
K.j.count=function(b,c,d){var e=0;K.j.forEach(b,function(b,g,h){c.call(d,b,g,h)&&++e},d);return e};K.j.find=function(b,c,d){c=K.j.findIndex(b,c,d);return 0>c?null:K.L(b)?b.charAt(c):b[c]};K.j.findIndex=function(b,c,d){for(var e=b.length,f=K.L(b)?b.split(""):b,g=0;g<e;g++)if(g in f&&c.call(d,f[g],g,b))return g;return-1};K.j.Gq=function(b,c,d){c=K.j.yj(b,c,d);return 0>c?null:K.L(b)?b.charAt(c):b[c]};
K.j.yj=function(b,c,d){for(var e=K.L(b)?b.split(""):b,f=b.length-1;0<=f;f--)if(f in e&&c.call(d,e[f],f,b))return f;return-1};K.j.contains=function(b,c){return 0<=K.j.indexOf(b,c)};K.j.Qb=function(b){return 0==b.length};K.j.clear=function(b){if(!K.isArray(b))for(var c=b.length-1;0<=c;c--)delete b[c];b.length=0};K.j.rr=function(b,c){K.j.contains(b,c)||b.push(c)};K.j.sg=function(b,c,d){K.j.splice(b,d,0,c)};K.j.tr=function(b,c,d){K.fb(K.j.splice,b,d,0).apply(null,c)};
K.j.insertBefore=function(b,c,d){var e;2==arguments.length||0>(e=K.j.indexOf(b,d))?b.push(c):K.j.sg(b,c,e)};K.j.remove=function(b,c){c=K.j.indexOf(b,c);var d;(d=0<=c)&&K.j.yb(b,c);return d};K.j.Os=function(b,c){c=K.j.lastIndexOf(b,c);return 0<=c?(K.j.yb(b,c),!0):!1};K.j.yb=function(b,c){return 1==Array.prototype.splice.call(b,c,1).length};K.j.Ns=function(b,c,d){c=K.j.findIndex(b,c,d);return 0<=c?(K.j.yb(b,c),!0):!1};
K.j.Ks=function(b,c,d){var e=0;K.j.Gf(b,function(f,g){c.call(d,f,g,b)&&K.j.yb(b,g)&&e++});return e};K.j.concat=function(b){return Array.prototype.concat.apply([],arguments)};K.j.join=function(b){return Array.prototype.concat.apply([],arguments)};K.j.th=function(b){var c=b.length;if(0<c){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];return d}return[]};K.j.clone=K.j.th;
K.j.extend=function(b,c){for(var d=1;d<arguments.length;d++){var e=arguments[d];if(K.Nb(e)){var f=b.length||0,g=e.length||0;b.length=f+g;for(var h=0;h<g;h++)b[f+h]=e[h]}else b.push(e)}};K.j.splice=function(b,c,d,e){return Array.prototype.splice.apply(b,K.j.slice(arguments,1))};K.j.slice=function(b,c,d){return 2>=arguments.length?Array.prototype.slice.call(b,c):Array.prototype.slice.call(b,c,d)};
K.j.Ls=function(b,c,d){function e(b){return K.ha(b)?"o"+K.kg(b):(typeof b).charAt(0)+b}c=c||b;d=d||e;for(var f={},g=0,h=0;h<b.length;){var l=b[h++],m=d(l);Object.prototype.hasOwnProperty.call(f,m)||(f[m]=!0,c[g++]=l)}c.length=g};K.j.gf=function(b,c,d){return K.j.hf(b,d||K.j.Pa,!1,c)};K.j.Kp=function(b,c,d){return K.j.hf(b,c,!0,void 0,d)};K.j.hf=function(b,c,d,e,f){for(var g=0,h=b.length,l;g<h;){var m=g+h>>1;var q=d?c.call(f,b[m],m,b):c(e,b[m]);0<q?g=m+1:(h=m,l=!q)}return l?g:~g};
K.j.sort=function(b,c){b.sort(c||K.j.Pa)};K.j.zt=function(b,c){for(var d=Array(b.length),e=0;e<b.length;e++)d[e]={index:e,value:b[e]};var f=c||K.j.Pa;K.j.sort(d,function(b,c){return f(b.value,c.value)||b.index-c.index});for(e=0;e<b.length;e++)b[e]=d[e].value};K.j.sl=function(b,c,d){var e=d||K.j.Pa;K.j.sort(b,function(b,d){return e(c(b),c(d))})};K.j.wt=function(b,c,d){K.j.sl(b,function(b){return b[c]},d)};
K.j.Vr=function(b,c,d){c=c||K.j.Pa;for(var e=1;e<b.length;e++){var f=c(b[e-1],b[e]);if(0<f||0==f&&d)return!1}return!0};K.j.Ib=function(b,c,d){if(!K.Nb(b)||!K.Nb(c)||b.length!=c.length)return!1;var e=b.length;d=d||K.j.lj;for(var f=0;f<e;f++)if(!d(b[f],c[f]))return!1;return!0};K.j.$p=function(b,c,d){d=d||K.j.Pa;for(var e=Math.min(b.length,c.length),f=0;f<e;f++){var g=d(b[f],c[f]);if(0!=g)return g}return K.j.Pa(b.length,c.length)};K.j.Pa=function(b,c){return b>c?1:b<c?-1:0};
K.j.vr=function(b,c){return-K.j.Pa(b,c)};K.j.lj=function(b,c){return b===c};K.j.Ip=function(b,c,d){d=K.j.gf(b,c,d);return 0>d?(K.j.sg(b,c,-(d+1)),!0):!1};K.j.Jp=function(b,c,d){c=K.j.gf(b,c,d);return 0<=c?K.j.yb(b,c):!1};K.j.Mp=function(b,c,d){for(var e={},f=0;f<b.length;f++){var g=b[f],h=c.call(d,g,f,b);K.R(h)&&(e[h]||(e[h]=[])).push(g)}return e};K.j.It=function(b,c,d){var e={};K.j.forEach(b,function(f,g){e[c.call(d,f,g,b)]=f});return e};
K.j.Gs=function(b,c,d){var e=[],f=0,g=b;d=d||1;void 0!==c&&(f=b,g=c);if(0>d*(g-f))return[];if(0<d)for(b=f;b<g;b+=d)e.push(b);else for(b=f;b>g;b+=d)e.push(b);return e};K.j.repeat=function(b,c){for(var d=[],e=0;e<c;e++)d[e]=b;return d};K.j.flatten=function(b){for(var c=[],d=0;d<arguments.length;d++){var e=arguments[d];if(K.isArray(e))for(var f=0;f<e.length;f+=8192)for(var g=K.j.flatten.apply(null,K.j.slice(e,f,f+8192)),h=0;h<g.length;h++)c.push(g[h]);else c.push(e)}return c};
K.j.rotate=function(b,c){b.length&&(c%=b.length,0<c?Array.prototype.unshift.apply(b,b.splice(-c,c)):0>c&&Array.prototype.push.apply(b,b.splice(0,-c)));return b};K.j.os=function(b,c,d){c=Array.prototype.splice.call(b,c,1);Array.prototype.splice.call(b,d,0,c[0])};
K.j.$t=function(b){if(!arguments.length)return[];for(var c=[],d=arguments[0].length,e=1;e<arguments.length;e++)arguments[e].length<d&&(d=arguments[e].length);for(e=0;e<d;e++){for(var f=[],g=0;g<arguments.length;g++)f.push(arguments[g][e]);c.push(f)}return c};K.j.vt=function(b,c){c=c||Math.random;for(var d=b.length-1;0<d;d--){var e=Math.floor(c()*(d+1)),f=b[d];b[d]=b[e];b[e]=f}};K.j.fq=function(b,c){var d=[];K.j.forEach(c,function(c){d.push(b[c])});return d};
K.j.bq=function(b,c,d){return K.j.concat.apply([],K.j.map(b,c,d))};K.h={};K.h.i={};K.h.i.Zh=!1;
K.h.i.Ie=K.h.i.Zh||("ar"==K.ba.substring(0,2).toLowerCase()||"fa"==K.ba.substring(0,2).toLowerCase()||"he"==K.ba.substring(0,2).toLowerCase()||"iw"==K.ba.substring(0,2).toLowerCase()||"ps"==K.ba.substring(0,2).toLowerCase()||"sd"==K.ba.substring(0,2).toLowerCase()||"ug"==K.ba.substring(0,2).toLowerCase()||"ur"==K.ba.substring(0,2).toLowerCase()||"yi"==K.ba.substring(0,2).toLowerCase())&&(2==K.ba.length||"-"==K.ba.substring(2,3)||"_"==K.ba.substring(2,3))||3<=K.ba.length&&"ckb"==K.ba.substring(0,3).toLowerCase()&&
(3==K.ba.length||"-"==K.ba.substring(3,4)||"_"==K.ba.substring(3,4));K.h.i.mb={gi:"\u202a",ji:"\u202b",Oe:"\u202c",hi:"\u200e",ki:"\u200f"};K.h.i.O={Ta:1,Ua:-1,qa:0};K.h.i.bc="right";K.h.i.$b="left";K.h.i.yn=K.h.i.Ie?K.h.i.$b:K.h.i.bc;K.h.i.xn=K.h.i.Ie?K.h.i.bc:K.h.i.$b;K.h.i.Al=function(b){return typeof b==x?0<b?K.h.i.O.Ta:0>b?K.h.i.O.Ua:K.h.i.O.qa:null==b?null:b?K.h.i.O.Ua:K.h.i.O.Ta};K.h.i.vb="A-Za-z\u00c0-\u00d6\u00d8-\u00f6\u00f8-\u02b8\u0300-\u0590\u0800-\u1fff\u200e\u2c00-\ufb1c\ufe00-\ufe6f\ufefd-\uffff";
K.h.i.zb="\u0591-\u06ef\u06fa-\u07ff\u200f\ufb1d-\ufdff\ufe70-\ufefc";K.h.i.Yj=/<[^>]*>|&[^;]+;/g;K.h.i.Sa=function(b,c){return c?b.replace(K.h.i.Yj,""):b};K.h.i.$k=new RegExp("["+K.h.i.zb+"]");K.h.i.Fk=new RegExp("["+K.h.i.vb+"]");K.h.i.Bd=function(b,c){return K.h.i.$k.test(K.h.i.Sa(b,c))};K.h.i.mr=K.h.i.Bd;K.h.i.og=function(b){return K.h.i.Fk.test(K.h.i.Sa(b,void 0))};K.h.i.Ik=new RegExp("^["+K.h.i.vb+"]");K.h.i.el=new RegExp("^["+K.h.i.zb+"]");K.h.i.nk=function(b){return K.h.i.el.test(b)};
K.h.i.jk=function(b){return K.h.i.Ik.test(b)};K.h.i.Nr=function(b){return!K.h.i.jk(b)&&!K.h.i.nk(b)};K.h.i.Gk=new RegExp("^[^"+K.h.i.zb+"]*["+K.h.i.vb+"]");K.h.i.bl=new RegExp("^[^"+K.h.i.vb+"]*["+K.h.i.zb+"]");K.h.i.nh=function(b,c){return K.h.i.bl.test(K.h.i.Sa(b,c))};K.h.i.Tr=K.h.i.nh;K.h.i.tl=function(b,c){return K.h.i.Gk.test(K.h.i.Sa(b,c))};K.h.i.Lr=K.h.i.tl;K.h.i.Jg=/^http:\/\/.*/;K.h.i.Or=function(b,c){b=K.h.i.Sa(b,c);return K.h.i.Jg.test(b)||!K.h.i.og(b)&&!K.h.i.Bd(b)};
K.h.i.Hk=new RegExp("["+K.h.i.vb+"][^"+K.h.i.zb+"]*$");K.h.i.cl=new RegExp("["+K.h.i.zb+"][^"+K.h.i.vb+"]*$");K.h.i.rj=function(b,c){return K.h.i.Hk.test(K.h.i.Sa(b,c))};K.h.i.Kr=K.h.i.rj;K.h.i.sj=function(b,c){return K.h.i.cl.test(K.h.i.Sa(b,c))};K.h.i.Rr=K.h.i.sj;K.h.i.dl=/^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Arab|Hebr|Thaa|Nkoo|Tfng))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;K.h.i.Sr=function(b){return K.h.i.dl.test(b)};K.h.i.Ui=/(\(.*?\)+)|(\[.*?\]+)|(\{.*?\}+)|(<.*?>+)/g;
K.h.i.lr=function(b,c){c=(void 0===c?K.h.i.Bd(b):c)?K.h.i.mb.ki:K.h.i.mb.hi;return b.replace(K.h.i.Ui,c+"$&"+c)};K.h.i.Aq=function(b){return"<"==b.charAt(0)?b.replace(/<\w+/,"$& dir=rtl"):"\n<span dir=rtl>"+b+"</span>"};K.h.i.Bq=function(b){return K.h.i.mb.ji+b+K.h.i.mb.Oe};K.h.i.yq=function(b){return"<"==b.charAt(0)?b.replace(/<\w+/,"$& dir=ltr"):"\n<span dir=ltr>"+b+"</span>"};K.h.i.zq=function(b){return K.h.i.mb.gi+b+K.h.i.mb.Oe};K.h.i.pj=/:\s*([.\d][.\w]*)\s+([.\d][.\w]*)\s+([.\d][.\w]*)\s+([.\d][.\w]*)/g;
K.h.i.vk=/left/gi;K.h.i.Zk=/right/gi;K.h.i.yl=/%%%%/g;K.h.i.ls=function(b){return b.replace(K.h.i.pj,":$1 $4 $3 $2").replace(K.h.i.vk,"%%%%").replace(K.h.i.Zk,K.h.i.$b).replace(K.h.i.yl,K.h.i.bc)};K.h.i.qj=/([\u0591-\u05f2])"/g;K.h.i.rl=/([\u0591-\u05f2])'/g;K.h.i.qs=function(b){return b.replace(K.h.i.qj,"$1\u05f4").replace(K.h.i.rl,"$1\u05f3")};K.h.i.Pl=/\s+/;K.h.i.Xj=/[\d\u06f0-\u06f9]/;K.h.i.al=.4;
K.h.i.yf=function(b,c){var d=0,e=0,f=!1;b=K.h.i.Sa(b,c).split(K.h.i.Pl);for(c=0;c<b.length;c++){var g=b[c];K.h.i.nh(g)?(d++,e++):K.h.i.Jg.test(g)?f=!0:K.h.i.og(g)?e++:K.h.i.Xj.test(g)&&(f=!0)}return 0==e?f?K.h.i.O.Ta:K.h.i.O.qa:d/e>K.h.i.al?K.h.i.O.Ua:K.h.i.O.Ta};K.h.i.tq=function(b,c){return K.h.i.yf(b,c)==K.h.i.O.Ua};K.h.i.ct=function(b,c){b&&(c=K.h.i.Al(c))&&(b.style.textAlign=c==K.h.i.O.Ua?K.h.i.bc:K.h.i.$b,b.dir=c==K.h.i.O.Ua?"rtl":"ltr")};
K.h.i.dt=function(b,c){switch(K.h.i.yf(c)){case K.h.i.O.Ta:b.dir="ltr";break;case K.h.i.O.Ua:b.dir="rtl";break;default:b.removeAttribute("dir")}};K.h.i.Tm=F();K.b={};K.b.C=function(){this.Bc="";this.Ai=K.b.C.ca};K.b.C.prototype.ua=!0;K.b.C.prototype.ga=G("Bc");K.b.C.prototype.Dd=!0;K.b.C.prototype.$a=function(){return K.h.i.O.Ta};K.ea&&(K.b.C.prototype.toString=function(){return"TrustedResourceUrl{"+this.Bc+"}"});K.b.C.u=function(b){if(b instanceof K.b.C&&b.constructor===K.b.C&&b.Ai===K.b.C.ca)return b.Bc;K.m.ma("expected object of type TrustedResourceUrl, got '"+b+k+K.aa(b));return"type_error:TrustedResourceUrl"};
K.b.C.format=function(b,c){b=K.b.C.Hf(b,c);return K.b.C.Hb(b)};K.b.C.Hf=function(b,c){var d=K.f.I.u(b);if(!K.b.C.Ih.test(d))throw Error("Invalid TrustedResourceUrl format: "+d);return d.replace(K.b.C.$h,function(b,f){if(!Object.prototype.hasOwnProperty.call(c,f))throw Error('Found marker, "'+f+'", in format string, "'+d+'", but no valid label mapping found in args: '+JSON.stringify(c));b=c[f];return b instanceof K.f.I?K.f.I.u(b):encodeURIComponent(String(b))})};K.b.C.$h=/%{(\w+)}/g;K.b.C.Ih=/^(?:https:)?\/\/[0-9a-z.:[\]-]+\/|^\/[^\/\\]|^about:blank(#|$)/i;
K.b.C.Kq=function(b,c,d){b=K.b.C.Hf(b,c);c=/\?/.test(b)?"&":"?";for(var e in d)for(var f=K.isArray(d[e])?d[e]:[d[e]],g=0;g<f.length;g++)null!=f[g]&&(b+=c+encodeURIComponent(e)+"="+encodeURIComponent(String(f[g])),c="&");return K.b.C.Hb(b)};K.b.C.mc=function(b){return K.b.C.Hb(K.f.I.u(b))};K.b.C.Nq=function(b){for(var c="",d=0;d<b.length;d++)c+=K.f.I.u(b[d]);return K.b.C.Hb(c)};K.b.C.ca={};K.b.C.Hb=function(b){var c=new K.b.C;c.Bc=b;return c};K.async={};K.async.Zb=function(b,c,d){this.wk=d;this.ij=b;this.Vk=c;this.xc=0;this.tc=null};K.async.Zb.prototype.get=function(){if(0<this.xc){this.xc--;var b=this.tc;this.tc=b.next;b.next=null}else b=this.ij();return b};K.async.Zb.prototype.put=function(b){this.Vk(b);this.xc<this.wk&&(this.xc++,b.next=this.tc,this.tc=b)};K.debug.Z={};K.debug.$m=F();K.debug.Z.xb=[];K.debug.Z.Vd=[];K.debug.Z.Wg=!1;K.debug.Z.register=function(b){K.debug.Z.xb[K.debug.Z.xb.length]=b;if(K.debug.Z.Wg)for(var c=K.debug.Z.Vd,d=0;d<c.length;d++)b(K.bind(c[d].Rl,c[d]))};K.debug.Z.ns=function(b){K.debug.Z.Wg=!0;for(var c=K.bind(b.Rl,b),d=0;d<K.debug.Z.xb.length;d++)K.debug.Z.xb[d](c);K.debug.Z.Vd.push(b)};K.debug.Z.Rt=function(b){var c=K.debug.Z.Vd;b=K.bind(b.u,b);for(var d=0;d<K.debug.Z.xb.length;d++)K.debug.Z.xb[d](b);c.length--};K.a.vn=F();K.a.c=function(b){this.xl=b};K.a.c.prototype.toString=G("xl");K.a.c.Ul=new K.a.c("A");K.a.c.Vl=new K.a.c("ABBR");K.a.c.Xl=new K.a.c("ACRONYM");K.a.c.Yl=new K.a.c("ADDRESS");K.a.c.bm=new K.a.c("APPLET");K.a.c.cm=new K.a.c("AREA");K.a.c.dm=new K.a.c("ARTICLE");K.a.c.em=new K.a.c("ASIDE");K.a.c.im=new K.a.c("AUDIO");K.a.c.jm=new K.a.c("B");K.a.c.km=new K.a.c("BASE");K.a.c.lm=new K.a.c("BASEFONT");K.a.c.mm=new K.a.c("BDI");K.a.c.nm=new K.a.c("BDO");K.a.c.qm=new K.a.c("BIG");K.a.c.rm=new K.a.c("BLOCKQUOTE");
K.a.c.sm=new K.a.c("BODY");K.a.c.we=new K.a.c("BR");K.a.c.tm=new K.a.c("BUTTON");K.a.c.um=new K.a.c("CANVAS");K.a.c.vm=new K.a.c("CAPTION");K.a.c.xm=new K.a.c("CENTER");K.a.c.ym=new K.a.c("CITE");K.a.c.Bm=new K.a.c("CODE");K.a.c.Cm=new K.a.c("COL");K.a.c.Dm=new K.a.c("COLGROUP");K.a.c.Em=new K.a.c("COMMAND");K.a.c.Gm=new K.a.c("DATA");K.a.c.Hm=new K.a.c("DATALIST");K.a.c.Im=new K.a.c("DD");K.a.c.Jm=new K.a.c("DEL");K.a.c.Km=new K.a.c("DETAILS");K.a.c.Lm=new K.a.c("DFN");K.a.c.Mm=new K.a.c("DIALOG");
K.a.c.Nm=new K.a.c("DIR");K.a.c.Om=new K.a.c("DIV");K.a.c.Pm=new K.a.c("DL");K.a.c.Sm=new K.a.c("DT");K.a.c.Vm=new K.a.c("EM");K.a.c.Wm=new K.a.c("EMBED");K.a.c.bn=new K.a.c("FIELDSET");K.a.c.cn=new K.a.c("FIGCAPTION");K.a.c.dn=new K.a.c("FIGURE");K.a.c.en=new K.a.c("FONT");K.a.c.fn=new K.a.c("FOOTER");K.a.c.gn=new K.a.c("FORM");K.a.c.hn=new K.a.c("FRAME");K.a.c.jn=new K.a.c("FRAMESET");K.a.c.kn=new K.a.c("H1");K.a.c.ln=new K.a.c("H2");K.a.c.mn=new K.a.c("H3");K.a.c.nn=new K.a.c("H4");K.a.c.on=new K.a.c("H5");
K.a.c.pn=new K.a.c("H6");K.a.c.qn=new K.a.c("HEAD");K.a.c.rn=new K.a.c("HEADER");K.a.c.sn=new K.a.c("HGROUP");K.a.c.tn=new K.a.c("HR");K.a.c.un=new K.a.c("HTML");K.a.c.wn=new K.a.c("I");K.a.c.zn=new K.a.c("IFRAME");K.a.c.An=new K.a.c("IMG");K.a.c.Bn=new K.a.c("INPUT");K.a.c.Cn=new K.a.c("INS");K.a.c.Hn=new K.a.c("ISINDEX");K.a.c.Jn=new K.a.c("KBD");K.a.c.Kn=new K.a.c("KEYGEN");K.a.c.Ln=new K.a.c("LABEL");K.a.c.Nn=new K.a.c("LEGEND");K.a.c.On=new K.a.c("LI");K.a.c.Pn=new K.a.c("LINK");K.a.c.Sn=new K.a.c("MAP");
K.a.c.Tn=new K.a.c("MARK");K.a.c.Un=new K.a.c("MATH");K.a.c.Vn=new K.a.c("MENU");K.a.c.Wn=new K.a.c("META");K.a.c.Xn=new K.a.c("METER");K.a.c.Zn=new K.a.c("NAV");K.a.c.$n=new K.a.c("NOFRAMES");K.a.c.ao=new K.a.c("NOSCRIPT");K.a.c.eo=new K.a.c("OBJECT");K.a.c.fo=new K.a.c("OL");K.a.c.ho=new K.a.c("OPTGROUP");K.a.c.io=new K.a.c("OPTION");K.a.c.jo=new K.a.c("OUTPUT");K.a.c.ko=new K.a.c("P");K.a.c.lo=new K.a.c("PARAM");K.a.c.no=new K.a.c("PRE");K.a.c.po=new K.a.c("PROGRESS");K.a.c.Q=new K.a.c("Q");
K.a.c.qo=new K.a.c("RP");K.a.c.ro=new K.a.c("RT");K.a.c.so=new K.a.c("RUBY");K.a.c.uo=new K.a.c("S");K.a.c.wo=new K.a.c("SAMP");K.a.c.xo=new K.a.c(p);K.a.c.yo=new K.a.c("SECTION");K.a.c.zo=new K.a.c("SELECT");K.a.c.Ao=new K.a.c("SMALL");K.a.c.Bo=new K.a.c("SOURCE");K.a.c.Co=new K.a.c("SPAN");K.a.c.Do=new K.a.c("STRIKE");K.a.c.Eo=new K.a.c("STRONG");K.a.c.Fo=new K.a.c("STYLE");K.a.c.Go=new K.a.c("SUB");K.a.c.Ho=new K.a.c("SUMMARY");K.a.c.Io=new K.a.c("SUP");K.a.c.Jo=new K.a.c("SVG");K.a.c.Ko=new K.a.c("TABLE");
K.a.c.Lo=new K.a.c("TBODY");K.a.c.Mo=new K.a.c("TD");K.a.c.No=new K.a.c("TEMPLATE");K.a.c.Oo=new K.a.c("TEXTAREA");K.a.c.Po=new K.a.c("TFOOT");K.a.c.Qo=new K.a.c("TH");K.a.c.Ro=new K.a.c("THEAD");K.a.c.So=new K.a.c("TIME");K.a.c.To=new K.a.c("TITLE");K.a.c.Uo=new K.a.c("TR");K.a.c.Vo=new K.a.c("TRACK");K.a.c.Xo=new K.a.c("TT");K.a.c.Zo=new K.a.c("U");K.a.c.$o=new K.a.c("UL");K.a.c.ap=new K.a.c("VAR");K.a.c.bp=new K.a.c("VIDEO");K.a.c.cp=new K.a.c("WBR");K.J={};K.J.ic=function(b){return function(){return b}};K.J.an=K.J.ic(!1);K.J.Wo=K.J.ic(!0);K.J.co=K.J.ic(null);K.J.$j=E();K.J.error=function(b){return function(){throw Error(b);}};K.J.ma=function(b){return function(){throw b;}};K.J.lock=function(b,c){c=c||0;return function(){return b.apply(this,Array.prototype.slice.call(arguments,0,c))}};K.J.vs=function(b){return function(){return arguments[b]}};
K.J.Cs=function(b,c){var d=Array.prototype.slice.call(arguments,1);return function(){var c=Array.prototype.slice.call(arguments);c.push.apply(c,d);return b.apply(this,c)}};K.J.Xt=function(b,c){return K.J.ll(b,K.J.ic(c))};K.J.Cq=function(b,c){return function(d){return c?b==d:b===d}};K.J.aq=function(b,c){var d=arguments,e=d.length;return function(){var b;e&&(b=d[e-1].apply(this,arguments));for(var c=e-2;0<=c;c--)b=d[c].call(this,b);return b}};
K.J.ll=function(b){var c=arguments,d=c.length;return function(){for(var b,f=0;f<d;f++)b=c[f].apply(this,arguments);return b}};K.J.kp=function(b){var c=arguments,d=c.length;return function(){for(var b=0;b<d;b++)if(!c[b].apply(this,arguments))return!1;return!0}};K.J.As=function(b){var c=arguments,d=c.length;return function(){for(var b=0;b<d;b++)if(c[b].apply(this,arguments))return!0;return!1}};K.J.us=function(b){return function(){return!b.apply(this,arguments)}};
K.J.create=function(b,c){function d(){}d.prototype=b.prototype;var e=new d;b.apply(e,Array.prototype.slice.call(arguments,1));return e};K.J.Kh=!0;K.J.Op=function(b){var c=!1,d;return function(){if(!K.J.Kh)return b();c||(d=b(),c=!0);return d}};K.J.once=function(b){var c=b;return function(){if(c){var b=c;c=null;b()}}};K.J.rq=function(b,c,d){var e=0;return function(f){K.global.clearTimeout(e);var g=arguments;e=K.global.setTimeout(function(){b.apply(d,g)},c)}};
K.J.Ft=function(b,c,d){function e(){g=K.global.setTimeout(f,c);b.apply(d,l)}function f(){g=0;h&&(h=!1,e())}var g=0,h=!1,l=[];return function(b){l=arguments;g?h=!0:e()}};K.J.Hs=function(b,c,d){function e(){f=0}var f=0;return function(g){f||(f=K.global.setTimeout(e,c),b.apply(d,arguments))}};K.g={};K.g.userAgent={};K.g.userAgent.A={};K.g.userAgent.A.Xf=function(){var b=K.g.userAgent.A.Kj();return b&&(b=b.userAgent)?b:""};K.g.userAgent.A.Kj=function(){return K.global.navigator};K.g.userAgent.A.xh=K.g.userAgent.A.Xf();K.g.userAgent.A.tt=function(b){K.g.userAgent.A.xh=b||K.g.userAgent.A.Xf()};K.g.userAgent.A.sb=function(){return K.g.userAgent.A.xh};K.g.userAgent.A.K=function(b){return K.f.contains(K.g.userAgent.A.sb(),b)};
K.g.userAgent.A.Pk=function(){return K.f.kf(K.g.userAgent.A.sb(),"WebKit")};K.g.userAgent.A.Af=function(b){for(var c=/(\w[\w ]+)\/([^\s]+)\s*(?:\((.*?)\))?/g,d=[],e;e=c.exec(b);)d.push([e[1],e[2],e[3]||void 0]);return d};K.object={};K.object.is=function(b,c){return b===c?0!==b||1/b===1/c:b!==b&&c!==c};K.object.forEach=function(b,c,d){for(var e in b)c.call(d,b[e],e,b)};K.object.filter=function(b,c,d){var e={},f;for(f in b)c.call(d,b[f],f,b)&&(e[f]=b[f]);return e};K.object.map=function(b,c,d){var e={},f;for(f in b)e[f]=c.call(d,b[f],f,b);return e};K.object.some=function(b,c,d){for(var e in b)if(c.call(d,b[e],e,b))return!0;return!1};K.object.every=function(b,c,d){for(var e in b)if(!c.call(d,b[e],e,b))return!1;return!0};
K.object.Vq=function(b){var c=0,d;for(d in b)c++;return c};K.object.Tq=function(b){for(var c in b)return c};K.object.Uq=function(b){for(var c in b)return b[c]};K.object.contains=function(b,c){return K.object.cj(b,c)};K.object.jr=function(b){var c=[],d=0,e;for(e in b)c[d++]=b[e];return c};K.object.Vf=function(b){var c=[],d=0,e;for(e in b)c[d++]=e;return c};K.object.ir=function(b,c){var d=K.Nb(c),e=d?c:arguments;for(d=d?0:1;d<e.length;d++){if(null==b)return;b=b[e[d]]}return b};
K.object.bj=function(b,c){return null!==b&&c in b};K.object.cj=function(b,c){for(var d in b)if(b[d]==c)return!0;return!1};K.object.zj=function(b,c,d){for(var e in b)if(c.call(d,b[e],e,b))return e};K.object.Hq=function(b,c,d){return(c=K.object.zj(b,c,d))&&b[c]};K.object.Qb=function(b){for(var c in b)return!1;return!0};K.object.clear=function(b){for(var c in b)delete b[c]};K.object.remove=function(b,c){var d;(d=c in b)&&delete b[c];return d};
K.object.add=function(b,c,d){if(null!==b&&c in b)throw Error('The object already contains the key "'+c+'"');K.object.set(b,c,d)};K.object.get=function(b,c,d){return null!==b&&c in b?b[c]:d};K.object.set=function(b,c,d){b[c]=d};K.object.ht=function(b,c,d){return c in b?b[c]:b[c]=d};K.object.ut=function(b,c,d){if(c in b)return b[c];d=d();return b[c]=d};K.object.Ib=function(b,c){for(var d in b)if(!(d in c)||b[d]!==c[d])return!1;for(d in c)if(!(d in b))return!1;return!0};
K.object.clone=function(b){var c={},d;for(d in b)c[d]=b[d];return c};K.object.Hl=function(b){var c=K.aa(b);if(c==y||c==r){if(K.ya(b.clone))return b.clone();c=c==r?[]:{};for(var d in b)c[d]=K.object.Hl(b[d]);return c}return b};K.object.Mt=function(b){var c={},d;for(d in b)c[b[d]]=d;return c};K.object.Pe=["constructor",w,"isPrototypeOf",A,D,"toString","valueOf"];
K.object.extend=function(b,c){for(var d,e,f=1;f<arguments.length;f++){e=arguments[f];for(d in e)b[d]=e[d];for(var g=0;g<K.object.Pe.length;g++)d=K.object.Pe[g],Object.prototype.hasOwnProperty.call(e,d)&&(b[d]=e[d])}};K.object.create=function(b){var c=arguments.length;if(1==c&&K.isArray(arguments[0]))return K.object.create.apply(null,arguments[0]);if(c%2)throw Error("Uneven number of arguments");for(var d={},e=0;e<c;e+=2)d[arguments[e]]=arguments[e+1];return d};
K.object.gj=function(b){var c=arguments.length;if(1==c&&K.isArray(arguments[0]))return K.object.gj.apply(null,arguments[0]);for(var d={},e=0;e<c;e++)d[arguments[e]]=!0;return d};K.object.iq=function(b){var c=b;Object.isFrozen&&!Object.isFrozen(b)&&(c=Object.create(b),Object.freeze(c));return c};K.object.Gr=function(b){return!!Object.isFrozen&&Object.isFrozen(b)};
K.object.Sq=function(b,c,d){if(!b)return[];if(!Object.getOwnPropertyNames||!Object.getPrototypeOf)return K.object.Vf(b);for(var e={};b&&(b!==Object.prototype||c)&&(b!==Function.prototype||d);){for(var f=Object.getOwnPropertyNames(b),g=0;g<f.length;g++)e[f[g]]=!0;b=Object.getPrototypeOf(b)}return K.object.Vf(e)};K.g.userAgent.v={};K.g.userAgent.v.Rg=function(){return K.g.userAgent.A.K("Opera")};K.g.userAgent.v.Nk=function(){return K.g.userAgent.A.K("Trident")||K.g.userAgent.A.K("MSIE")};K.g.userAgent.v.Sd=function(){return K.g.userAgent.A.K("Edge")};K.g.userAgent.v.Mk=function(){return K.g.userAgent.A.K("Firefox")};K.g.userAgent.v.Sg=function(){return K.g.userAgent.A.K("Safari")&&!(K.g.userAgent.v.Qd()||K.g.userAgent.v.Rd()||K.g.userAgent.v.Rg()||K.g.userAgent.v.Sd()||K.g.userAgent.v.Kg()||K.g.userAgent.A.K("Android"))};
K.g.userAgent.v.Rd=function(){return K.g.userAgent.A.K("Coast")};K.g.userAgent.v.Ok=function(){return(K.g.userAgent.A.K("iPad")||K.g.userAgent.A.K("iPhone"))&&!K.g.userAgent.v.Sg()&&!K.g.userAgent.v.Qd()&&!K.g.userAgent.v.Rd()&&K.g.userAgent.A.K("AppleWebKit")};K.g.userAgent.v.Qd=function(){return(K.g.userAgent.A.K("Chrome")||K.g.userAgent.A.K("CriOS"))&&!K.g.userAgent.v.Sd()};
K.g.userAgent.v.Lk=function(){return K.g.userAgent.A.K("Android")&&!(K.g.userAgent.v.zg()||K.g.userAgent.v.fk()||K.g.userAgent.v.Nd()||K.g.userAgent.v.Kg())};K.g.userAgent.v.Nd=K.g.userAgent.v.Rg;K.g.userAgent.v.uc=K.g.userAgent.v.Nk;K.g.userAgent.v.Ra=K.g.userAgent.v.Sd;K.g.userAgent.v.fk=K.g.userAgent.v.Mk;K.g.userAgent.v.Ur=K.g.userAgent.v.Sg;K.g.userAgent.v.Ar=K.g.userAgent.v.Rd;K.g.userAgent.v.Ir=K.g.userAgent.v.Ok;K.g.userAgent.v.zg=K.g.userAgent.v.Qd;K.g.userAgent.v.yr=K.g.userAgent.v.Lk;
K.g.userAgent.v.Kg=function(){return K.g.userAgent.A.K("Silk")};K.g.userAgent.v.Lb=function(){function b(b){b=K.j.find(b,e);return d[b]||""}var c=K.g.userAgent.A.sb();if(K.g.userAgent.v.uc())return K.g.userAgent.v.Jj(c);c=K.g.userAgent.A.Af(c);var d={};K.j.forEach(c,function(b){d[b[0]]=b[1]});var e=K.fb(K.object.bj,d);return K.g.userAgent.v.Nd()?b(["Version","Opera"]):K.g.userAgent.v.Ra()?b(["Edge"]):K.g.userAgent.v.zg()?b(["Chrome","CriOS"]):(c=c[2])&&c[1]||""};
K.g.userAgent.v.va=function(b){return 0<=K.f.Eb(K.g.userAgent.v.Lb(),b)};K.g.userAgent.v.Jj=function(b){var c=/rv: *([\d\.]*)/.exec(b);if(c&&c[1])return c[1];c="";var d=/MSIE +([\d\.]+)/.exec(b);if(d&&d[1])if(b=/Trident\/(\d.\d)/.exec(b),"7.0"==d[1])if(b&&b[1])switch(b[1]){case "4.0":c="8.0";break;case "5.0":c="9.0";break;case "6.0":c="10.0";break;case "7.0":c="11.0"}else c="7.0";else c=d[1];return c};K.g.userAgent.U={};K.g.userAgent.U.lk=function(){return K.g.userAgent.A.K("Presto")};K.g.userAgent.U.pk=function(){return K.g.userAgent.A.K("Trident")||K.g.userAgent.A.K("MSIE")};K.g.userAgent.U.Ra=function(){return K.g.userAgent.A.K("Edge")};K.g.userAgent.U.Mg=function(){return K.g.userAgent.A.Pk()&&!K.g.userAgent.U.Ra()};K.g.userAgent.U.gk=function(){return K.g.userAgent.A.K("Gecko")&&!K.g.userAgent.U.Mg()&&!K.g.userAgent.U.pk()&&!K.g.userAgent.U.Ra()};
K.g.userAgent.U.Lb=function(){var b=K.g.userAgent.A.sb();if(b){b=K.g.userAgent.A.Af(b);var c=K.g.userAgent.U.Hj(b);if(c)return"Gecko"==c[0]?K.g.userAgent.U.Rj(b):c[1];b=b[0];var d;if(b&&(d=b[2])&&(d=/Trident\/([^\s;]+)/.exec(d)))return d[1]}return""};K.g.userAgent.U.Hj=function(b){if(!K.g.userAgent.U.Ra())return b[1];for(var c=0;c<b.length;c++){var d=b[c];if("Edge"==d[0])return d}};K.g.userAgent.U.va=function(b){return 0<=K.f.Eb(K.g.userAgent.U.Lb(),b)};
K.g.userAgent.U.Rj=function(b){return(b=K.j.find(b,function(b){return"Firefox"==b[0]}))&&b[1]||""};K.async.qh=function(b){K.global.setTimeout(function(){throw b;},0)};K.async.pa=function(b,c,d){var e=b;c&&(e=K.bind(b,c));e=K.async.pa.Ah(e);K.ya(K.global.setImmediate)&&(d||K.async.pa.Kl())?K.global.setImmediate(e):(K.async.pa.kh||(K.async.pa.kh=K.async.pa.Nj()),K.async.pa.kh(e))};K.async.pa.Kl=function(){return K.global.Window&&K.global.Window.prototype&&!K.g.userAgent.v.Ra()&&K.global.Window.prototype.setImmediate==K.global.setImmediate?!1:!0};
K.async.pa.Nj=function(){var b=K.global.MessageChannel;"undefined"===typeof b&&"undefined"!==typeof window&&window.postMessage&&window.addEventListener&&!K.g.userAgent.U.lk()&&(b=function(){var b=document.createElement("IFRAME");b.style.display="none";b.src="";document.documentElement.appendChild(b);var c=b.contentWindow;b=c.document;b.open();b.write("");b.close();var d="callImmediate"+Math.random(),e="file:"==c.location.protocol?"*":c.location.protocol+"//"+c.location.host;b=K.bind(function(b){if(("*"==
e||b.origin==e)&&b.data==d)this.port1.onmessage()},this);c.addEventListener("message",b,!1);this.port1={};this.port2={postMessage:function(){c.postMessage(d,e)}}});if("undefined"!==typeof b&&!K.g.userAgent.v.uc()){var c=new b,d={},e=d;c.port1.onmessage=function(){if(K.R(d.next)){d=d.next;var b=d.lf;d.lf=null;b()}};return function(b){e.next={lf:b};e=e.next;c.port2.postMessage(0)}}return"undefined"!==typeof document&&"onreadystatechange"in document.createElement(p)?function(b){var c=document.createElement(p);
c.onreadystatechange=function(){c.onreadystatechange=null;c.parentNode.removeChild(c);c=null;b();b=null};document.documentElement.appendChild(c)}:function(b){K.global.setTimeout(b,0)}};K.async.pa.Ah=K.J.$j;K.debug.Z.register(function(b){K.async.pa.Ah=b});K.async.Ea=function(){this.Qc=this.Ab=null};K.async.Ea.Vc=100;K.async.Ea.Kb=new K.async.Zb(function(){return new K.async.ad},function(b){b.reset()},K.async.Ea.Vc);K.async.Ea.prototype.add=function(b,c){var d=K.async.Ea.Kb.get();d.set(b,c);this.Qc?this.Qc.next=d:this.Ab=d;this.Qc=d};K.async.Ea.prototype.remove=function(){var b=null;this.Ab&&(b=this.Ab,this.Ab=this.Ab.next,this.Ab||(this.Qc=null),b.next=null);return b};K.async.ad=function(){this.next=this.scope=this.od=null};
K.async.ad.prototype.set=function(b,c){this.od=b;this.scope=c;this.next=null};K.async.ad.prototype.reset=function(){this.next=this.scope=this.od=null};K.async.M=function(b,c){K.async.M.Hc||K.async.M.bk();K.async.M.Pc||(K.async.M.Hc(),K.async.M.Pc=!0);K.async.M.ie.add(b,c)};K.async.M.bk=function(){if(-1!=String(K.global.Promise).indexOf("[native code]")){var b=K.global.Promise.resolve(void 0);K.async.M.Hc=function(){b.then(K.async.M.Cc)}}else K.async.M.Hc=function(){K.async.pa(K.async.M.Cc)}};K.async.M.Jq=function(b){K.async.M.Hc=function(){K.async.pa(K.async.M.Cc);b&&b(K.async.M.Cc)}};K.async.M.Pc=!1;K.async.M.ie=new K.async.Ea;
K.ea&&(K.async.M.Rs=function(){K.async.M.Pc=!1;K.async.M.ie=new K.async.Ea});K.async.M.Cc=function(){for(var b;b=K.async.M.ie.remove();){try{b.od.call(b.scope)}catch(c){K.async.qh(c)}K.async.Ea.Kb.put(b)}K.async.M.Pc=!1};K.a.m={};K.a.m.Cp=F();K.a.m.up=F();K.a.m.zp=F();K.a.m.yp=F();K.a.m.vp=F();K.a.m.wp=F();K.a.m.xp=F();K.a.m.Ap=F();K.a.m.Bp=F();K.a.m.sq=function(b){return K.ha(b)?b.constructor.displayName||b.constructor.name||Object.prototype.toString.call(b):void 0===b?"undefined":null===b?"null":typeof b};K.a.m.qc=function(b){return(b=b&&b.ownerDocument)&&(b.defaultView||b.parentWindow)||K.global};K.g.userAgent.platform={};K.g.userAgent.platform.yg=function(){return K.g.userAgent.A.K("Android")};K.g.userAgent.platform.Hg=function(){return K.g.userAgent.A.K("iPod")};K.g.userAgent.platform.Gg=function(){return K.g.userAgent.A.K("iPhone")&&!K.g.userAgent.A.K("iPod")&&!K.g.userAgent.A.K("iPad")};K.g.userAgent.platform.Fg=function(){return K.g.userAgent.A.K("iPad")};K.g.userAgent.platform.Eg=function(){return K.g.userAgent.platform.Gg()||K.g.userAgent.platform.Fg()||K.g.userAgent.platform.Hg()};
K.g.userAgent.platform.Ig=function(){return K.g.userAgent.A.K("Macintosh")};K.g.userAgent.platform.ik=function(){return K.g.userAgent.A.K("Linux")};K.g.userAgent.platform.Og=function(){return K.g.userAgent.A.K("Windows")};K.g.userAgent.platform.Ag=function(){return K.g.userAgent.A.K("CrOS")};
K.g.userAgent.platform.Lb=function(){var b=K.g.userAgent.A.sb(),c="";K.g.userAgent.platform.Og()?(c=/Windows (?:NT|Phone) ([0-9.]+)/,c=(b=c.exec(b))?b[1]:"0.0"):K.g.userAgent.platform.Eg()?(c=/(?:iPhone|iPod|iPad|CPU)\s+OS\s+(\S+)/,c=(b=c.exec(b))&&b[1].replace(/_/g,".")):K.g.userAgent.platform.Ig()?(c=/Mac OS X ([0-9_.]+)/,c=(b=c.exec(b))?b[1].replace(/_/g,"."):"10"):K.g.userAgent.platform.yg()?(c=/Android\s+([^\);]+)(\)|;)/,c=(b=c.exec(b))&&b[1]):K.g.userAgent.platform.Ag()&&(c=/(?:CrOS\s+(?:i686|x86_64)\s+([0-9.]+))/,
c=(b=c.exec(b))&&b[1]);return c||""};K.g.userAgent.platform.va=function(b){return 0<=K.f.Eb(K.g.userAgent.platform.Lb(),b)};K.Ha={};K.Ha.object=function(b,c){return c};K.Ha.ce=function(b){K.Ha.ce[" "](b);return b};K.Ha.ce[" "]=K.eb;K.Ha.Pp=function(b,c){try{return K.Ha.ce(b[c]),!0}catch(d){}return!1};K.Ha.cache=function(b,c,d,e){e=e?e(c):c;return Object.prototype.hasOwnProperty.call(b,e)?b[e]:b[e]=d(c)};K.userAgent={};K.userAgent.oe=!1;K.userAgent.me=!1;K.userAgent.ne=!1;K.userAgent.te=!1;K.userAgent.Uc=!1;K.userAgent.re=!1;K.userAgent.Fh=!1;K.userAgent.Bb=K.userAgent.oe||K.userAgent.me||K.userAgent.ne||K.userAgent.Uc||K.userAgent.te||K.userAgent.re;K.userAgent.Qj=function(){return K.g.userAgent.A.sb()};K.userAgent.Yf=function(){return K.global.navigator||null};K.userAgent.Ne=K.userAgent.Bb?K.userAgent.re:K.g.userAgent.v.Nd();K.userAgent.Y=K.userAgent.Bb?K.userAgent.oe:K.g.userAgent.v.uc();
K.userAgent.Ce=K.userAgent.Bb?K.userAgent.me:K.g.userAgent.U.Ra();K.userAgent.Um=K.userAgent.Ce||K.userAgent.Y;K.userAgent.Yc=K.userAgent.Bb?K.userAgent.ne:K.g.userAgent.U.gk();K.userAgent.Cb=K.userAgent.Bb?K.userAgent.te||K.userAgent.Uc:K.g.userAgent.U.Mg();K.userAgent.kk=function(){return K.userAgent.Cb&&K.g.userAgent.A.K("Mobile")};K.userAgent.Yn=K.userAgent.Uc||K.userAgent.kk();K.userAgent.vo=K.userAgent.Cb;K.userAgent.nj=function(){var b=K.userAgent.Yf();return b&&b.platform||""};
K.userAgent.mo=K.userAgent.nj();K.userAgent.qe=!1;K.userAgent.ue=!1;K.userAgent.pe=!1;K.userAgent.ve=!1;K.userAgent.le=!1;K.userAgent.Sc=!1;K.userAgent.Rc=!1;K.userAgent.Tc=!1;K.userAgent.Da=K.userAgent.qe||K.userAgent.ue||K.userAgent.pe||K.userAgent.ve||K.userAgent.le||K.userAgent.Sc||K.userAgent.Rc||K.userAgent.Tc;K.userAgent.Rn=K.userAgent.Da?K.userAgent.qe:K.g.userAgent.platform.Ig();K.userAgent.ep=K.userAgent.Da?K.userAgent.ue:K.g.userAgent.platform.Og();
K.userAgent.hk=function(){return K.g.userAgent.platform.ik()||K.g.userAgent.platform.Ag()};K.userAgent.Qn=K.userAgent.Da?K.userAgent.pe:K.userAgent.hk();K.userAgent.tk=function(){var b=K.userAgent.Yf();return!!b&&K.f.contains(b.appVersion||"","X11")};K.userAgent.fp=K.userAgent.Da?K.userAgent.ve:K.userAgent.tk();K.userAgent.am=K.userAgent.Da?K.userAgent.le:K.g.userAgent.platform.yg();K.userAgent.Fn=K.userAgent.Da?K.userAgent.Sc:K.g.userAgent.platform.Gg();
K.userAgent.En=K.userAgent.Da?K.userAgent.Rc:K.g.userAgent.platform.Fg();K.userAgent.Gn=K.userAgent.Da?K.userAgent.Tc:K.g.userAgent.platform.Hg();K.userAgent.Dn=K.userAgent.Da?K.userAgent.Sc||K.userAgent.Rc||K.userAgent.Tc:K.g.userAgent.platform.Eg();K.userAgent.oj=function(){var b="",c=K.userAgent.Sj();c&&(b=c?c[1]:"");return K.userAgent.Y&&(c=K.userAgent.Of(),null!=c&&c>parseFloat(b))?String(c):b};
K.userAgent.Sj=function(){var b=K.userAgent.Qj();if(K.userAgent.Yc)return/rv\:([^\);]+)(\)|;)/.exec(b);if(K.userAgent.Ce)return/Edge\/([\d\.]+)/.exec(b);if(K.userAgent.Y)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(b);if(K.userAgent.Cb)return/WebKit\/(\S+)/.exec(b);if(K.userAgent.Ne)return/(?:Version)[ \/]?(\S+)/.exec(b)};K.userAgent.Of=function(){var b=K.global.document;return b?b.documentMode:void 0};K.userAgent.VERSION=K.userAgent.oj();K.userAgent.compare=function(b,c){return K.f.Eb(b,c)};
K.userAgent.rk={};K.userAgent.va=function(b){return K.userAgent.Fh||K.Ha.cache(K.userAgent.rk,b,function(){return 0<=K.f.Eb(K.userAgent.VERSION,b)})};K.userAgent.Zr=K.userAgent.va;K.userAgent.Pb=function(b){return Number(K.userAgent.Wh)>=b};K.userAgent.Cr=K.userAgent.Pb;var L;var M=K.global.document,aa=K.userAgent.Of();L=M&&K.userAgent.Y?aa||("CSS1Compat"==M.compatMode?parseInt(K.userAgent.VERSION,10):5):void 0;K.userAgent.Wh=L;K.a.ib={Lh:!K.userAgent.Y||K.userAgent.Pb(9),Mh:!K.userAgent.Yc&&!K.userAgent.Y||K.userAgent.Y&&K.userAgent.Pb(9)||K.userAgent.Yc&&K.userAgent.va("1.9.1"),xe:K.userAgent.Y&&!K.userAgent.va("9"),Nh:K.userAgent.Y||K.userAgent.Ne||K.userAgent.Cb,ci:K.userAgent.Y,Mn:K.userAgent.Y&&!K.userAgent.Pb(9)};K.a.Mc={};K.a.Mc.Hi={area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0};K.a.Mc.sk=function(b){return!0===K.a.Mc.Hi[b]};K.b.V=function(){this.yc="";this.ni=K.b.V.ca};K.b.V.prototype.ua=!0;K.b.V.ca={};K.b.V.mc=function(b){b=K.f.I.u(b);return 0===b.length?K.b.V.EMPTY:K.b.V.hd(b)};K.b.V.prototype.ga=G("yc");K.ea&&(K.b.V.prototype.toString=function(){return"SafeScript{"+this.yc+"}"});K.b.V.u=function(b){if(b instanceof K.b.V&&b.constructor===K.b.V&&b.ni===K.b.V.ca)return b.yc;K.m.ma("expected object of type SafeScript, got '"+b+k+K.aa(b));return"type_error:SafeScript"};K.b.V.hd=function(b){return(new K.b.V).bb(b)};
K.b.V.prototype.bb=function(b){this.yc=b;return this};K.b.V.EMPTY=K.b.V.hd("");K.sa={};K.sa.url={};K.sa.url.dj=function(b){return K.sa.url.lg().createObjectURL(b)};K.sa.url.Ts=function(b){K.sa.url.lg().revokeObjectURL(b)};K.sa.url.lg=function(){var b=K.sa.url.Ef();if(null!=b)return b;throw Error("This browser doesn't seem to support blob URLs");};K.sa.url.Ef=function(){return K.R(K.global.URL)&&K.R(K.global.URL.createObjectURL)?K.global.URL:K.R(K.global.webkitURL)&&K.R(K.global.webkitURL.createObjectURL)?K.global.webkitURL:K.R(K.global.createObjectURL)?K.global:null};
K.sa.url.Lp=function(){return null!=K.sa.url.Ef()};K.b.o=function(){this.Ga="";this.ri=K.b.o.ca};K.b.o.Ka="about:invalid#zClosurez";K.b.o.prototype.ua=!0;K.b.o.prototype.ga=G("Ga");K.b.o.prototype.Dd=!0;K.b.o.prototype.$a=function(){return K.h.i.O.Ta};K.ea&&(K.b.o.prototype.toString=function(){return"SafeUrl{"+this.Ga+"}"});K.b.o.u=function(b){if(b instanceof K.b.o&&b.constructor===K.b.o&&b.ri===K.b.o.ca)return b.Ga;K.m.ma("expected object of type SafeUrl, got '"+b+k+K.aa(b));return"type_error:SafeUrl"};K.b.o.mc=function(b){return K.b.o.Fa(K.f.I.u(b))};
K.b.Re=/^(?:audio\/(?:3gpp|3gpp2|aac|midi|mp4|mpeg|ogg|x-m4a|x-wav|webm)|image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|text\/csv|video\/(?:mpeg|mp4|ogg|webm))$/i;K.b.o.Mq=function(b){b=K.b.Re.test(b.type)?K.sa.url.dj(b):K.b.o.Ka;return K.b.o.Fa(b)};K.b.Rh=/^data:([^;,]*);base64,[a-z0-9+\/]+=*$/i;K.b.o.Oq=function(b){var c=b.match(K.b.Rh);c=c&&K.b.Re.test(c[1]);return K.b.o.Fa(c?b:K.b.o.Ka)};K.b.o.Qq=function(b){K.f.Zi(b)||(b=K.b.o.Ka);return K.b.o.Fa(b)};K.b.o.Rq=function(b){return K.b.o.Fa(K.b.C.u(b))};
K.b.Se=/^(?:(?:https?|mailto|ftp):|[^:/?#]*(?:[/?#]|$))/i;K.b.o.Gc=function(b){if(b instanceof K.b.o)return b;b=b.ua?b.ga():String(b);K.b.Se.test(b)||(b=K.b.o.Ka);return K.b.o.Fa(b)};K.b.o.Vb=function(b){if(b instanceof K.b.o)return b;b=b.ua?b.ga():String(b);K.b.Se.test(b)||(b=K.b.o.Ka);return K.b.o.Fa(b)};K.b.o.ca={};K.b.o.Fa=function(b){var c=new K.b.o;c.Ga=b;return c};K.b.o.Wl=K.b.o.Fa("about:blank");K.b.B=function(){this.Ac="";this.pi=K.b.B.ca};K.b.B.prototype.ua=!0;K.b.B.ca={};K.b.B.mc=function(b){b=K.f.I.u(b);return 0===b.length?K.b.B.EMPTY:K.b.B.Fb(b)};K.b.B.Vp=F();K.b.B.prototype.ga=G("Ac");K.ea&&(K.b.B.prototype.toString=function(){return"SafeStyle{"+this.Ac+"}"});K.b.B.u=function(b){if(b instanceof K.b.B&&b.constructor===K.b.B&&b.pi===K.b.B.ca)return b.Ac;K.m.ma("expected object of type SafeStyle, got '"+b+k+K.aa(b));return"type_error:SafeStyle"};K.b.B.Fb=function(b){return(new K.b.B).bb(b)};
K.b.B.prototype.bb=function(b){this.Ac=b;return this};K.b.B.EMPTY=K.b.B.Fb("");K.b.B.Ka="zClosurez";K.b.B.create=function(b){var c="",d;for(d in b){if(!/^[-_a-zA-Z0-9]+$/.test(d))throw Error("Name allows only [-_a-zA-Z0-9], got: "+d);var e=b[d];null!=e&&(e=K.isArray(e)?K.j.map(e,K.b.B.gh).join(" "):K.b.B.gh(e),c+=d+":"+e+";")}return c?K.b.B.Fb(c):K.b.B.EMPTY};
K.b.B.gh=function(b){return b instanceof K.b.o?'url("'+K.b.o.u(b).replace(/</g,"%3c").replace(/[\\"]/g,"\\$&")+'")':b instanceof K.f.I?K.f.I.u(b):K.b.B.il(String(b))};K.b.B.il=function(b){var c=b.replace(K.b.o.ai,"$1").replace(K.b.o.Xe,"url");return K.b.B.Ei.test(c)?K.b.B.Vj(b)?K.b.B.jl(b):(K.m.ma("String value requires balanced quotes, got: "+b),K.b.B.Ka):(K.m.ma("String value allows only "+K.b.B.$e+" and simple functions, got: "+b),K.b.B.Ka)};
K.b.B.Vj=function(b){for(var c=!0,d=!0,e=0;e<b.length;e++){var f=b.charAt(e);"'"==f&&d?c=!c:'"'==f&&c&&(d=!d)}return c&&d};K.b.B.$e="[-,.\"'%_!# a-zA-Z0-9]";K.b.B.Ei=new RegExp("^"+K.b.B.$e+"+$");K.b.o.Xe=/\b(url\([ \t\n]*)('[ -&(-\[\]-~]*'|"[ !#-\[\]-~]*"|[!#-&*-\[\]-~]*)([ \t\n]*\))/g;K.b.o.ai=/\b(hsl|hsla|rgb|rgba|(rotate|scale|translate)(X|Y|Z|3d)?)\([-0-9a-z.%, ]+\)/g;
K.b.B.jl=function(b){return b.replace(K.b.o.Xe,function(b,d,e,f){var c="";e=e.replace(/^(['"])(.*)\1$/,function(b,d,e){c=d;return e});b=K.b.o.Gc(e).ga();return d+c+b+c+f})};K.b.B.concat=function(b){function c(b){K.isArray(b)?K.j.forEach(b,c):d+=K.b.B.u(b)}var d="";K.j.forEach(arguments,c);return d?K.b.B.Fb(d):K.b.B.EMPTY};K.b.N=function(){this.zc="";this.oi=K.b.N.ca};K.b.N.prototype.ua=!0;K.b.N.ca={};
K.b.N.kq=function(b,c){if(K.f.contains(b,"<"))throw Error("Selector does not allow '<', got: "+b);var d=b.replace(/('|")((?!\1)[^\r\n\f\\]|\\[\s\S])*\1/g,"");if(!/^[-_a-zA-Z0-9#.:* ,>+~[\]()=^$|]+$/.test(d))throw Error("Selector allows only [-_a-zA-Z0-9#.:* ,>+~[\\]()=^$|] and strings, got: "+b);if(!K.b.N.Uj(d))throw Error("() and [] in selector must be balanced, got: "+b);c instanceof K.b.B||(c=K.b.B.create(c));b=b+"{"+K.b.B.u(c)+"}";return K.b.N.Gb(b)};
K.b.N.Uj=function(b){for(var c={"(":")","[":"]"},d=[],e=0;e<b.length;e++){var f=b[e];if(c[f])d.push(c[f]);else if(K.object.contains(c,f)&&d.pop()!=f)return!1}return 0==d.length};K.b.N.concat=function(b){function c(b){K.isArray(b)?K.j.forEach(b,c):d+=K.b.N.u(b)}var d="";K.j.forEach(arguments,c);return K.b.N.Gb(d)};K.b.N.mc=function(b){b=K.f.I.u(b);return 0===b.length?K.b.N.EMPTY:K.b.N.Gb(b)};K.b.N.prototype.ga=G("zc");K.ea&&(K.b.N.prototype.toString=function(){return"SafeStyleSheet{"+this.zc+"}"});
K.b.N.u=function(b){if(b instanceof K.b.N&&b.constructor===K.b.N&&b.oi===K.b.N.ca)return b.zc;K.m.ma("expected object of type SafeStyleSheet, got '"+b+k+K.aa(b));return"type_error:SafeStyleSheet"};K.b.N.Gb=function(b){return(new K.b.N).bb(b)};K.b.N.prototype.bb=function(b){this.zc=b;return this};K.b.N.EMPTY=K.b.N.Gb("");K.b.l=function(){this.Ga="";this.mi=K.b.l.ca;this.kc=null};K.b.l.prototype.Dd=!0;K.b.l.prototype.$a=G("kc");K.b.l.prototype.ua=!0;K.b.l.prototype.ga=G("Ga");K.ea&&(K.b.l.prototype.toString=function(){return"SafeHtml{"+this.Ga+"}"});K.b.l.u=function(b){if(b instanceof K.b.l&&b.constructor===K.b.l&&b.mi===K.b.l.ca)return b.Ga;K.m.ma("expected object of type SafeHtml, got '"+b+k+K.aa(b));return"type_error:SafeHtml"};
K.b.l.ta=function(b){if(b instanceof K.b.l)return b;var c=null;b.Dd&&(c=b.$a());return K.b.l.ra(K.f.ta(b.ua?b.ga():String(b)),c)};K.b.l.pr=function(b){if(b instanceof K.b.l)return b;b=K.b.l.ta(b);return K.b.l.ra(K.f.Yg(K.b.l.u(b)),b.$a())};K.b.l.qr=function(b){if(b instanceof K.b.l)return b;b=K.b.l.ta(b);return K.b.l.ra(K.f.Ol(K.b.l.u(b)),b.$a())};K.b.l.from=K.b.l.ta;K.b.l.Ze=/^[a-zA-Z0-9-]+$/;K.b.l.Ci={action:!0,cite:!0,data:!0,formaction:!0,href:!0,manifest:!0,poster:!0,src:!0};
K.b.l.ii={APPLET:!0,BASE:!0,EMBED:!0,IFRAME:!0,LINK:!0,MATH:!0,META:!0,OBJECT:!0,SCRIPT:!0,STYLE:!0,SVG:!0,TEMPLATE:!0};K.b.l.create=function(b,c,d){K.b.l.Ml(String(b));return K.b.l.Ya(String(b),c,d)};K.b.l.Ml=function(b){if(!K.b.l.Ze.test(b))throw Error("Invalid tag name <"+b+">.");if(b.toUpperCase()in K.b.l.ii)throw Error("Tag name <"+b+"> is not allowed for SafeHtml.");};
K.b.l.hq=function(b,c,d,e){b&&K.b.C.u(b);var f={};f.src=b||null;f.srcdoc=c&&K.b.l.u(c);b=K.b.l.hc(f,{sandbox:""},d);return K.b.l.Ya("iframe",b,e)};K.b.l.lq=function(b,c,d,e){if(!K.b.l.Wi())throw Error("The browser does not support sandboxed iframes.");var f={};f.src=b?K.b.o.u(K.b.o.Gc(b)):null;f.srcdoc=c||null;f.sandbox="";b=K.b.l.hc(f,{},d);return K.b.l.Ya("iframe",b,e)};K.b.l.Wi=function(){return K.global.HTMLIFrameElement&&"sandbox"in K.global.HTMLIFrameElement.prototype};
K.b.l.nq=function(b,c){K.b.C.u(b);b=K.b.l.hc({src:b},{},c);return K.b.l.Ya("script",b)};K.b.l.mq=function(b,c){for(var d in c){var e=d.toLowerCase();if("language"==e||"src"==e||"text"==e||"type"==e)throw Error('Cannot set "'+e+'" attribute');}d="";b=K.j.concat(b);for(e=0;e<b.length;e++)d+=K.b.V.u(b[e]);b=K.b.l.ra(d,K.h.i.O.qa);return K.b.l.Ya("script",c,b)};
K.b.l.oq=function(b,c){c=K.b.l.hc({type:"text/css"},{},c);var d="";b=K.j.concat(b);for(var e=0;e<b.length;e++)d+=K.b.N.u(b[e]);b=K.b.l.ra(d,K.h.i.O.qa);return K.b.l.Ya("style",c,b)};K.b.l.jq=function(b,c){b=K.b.o.u(K.b.o.Gc(b));(K.g.userAgent.v.uc()||K.g.userAgent.v.Ra())&&K.f.contains(b,";")&&(b="'"+b.replace(/'/g,"%27")+"'");return K.b.l.Ya("meta",{"http-equiv":"refresh",content:(c||0)+"; url="+b})};
K.b.l.Cj=function(b,c,d){if(d instanceof K.f.I)d=K.f.I.u(d);else if("style"==c.toLowerCase())d=K.b.l.Oj(d);else{if(/^on/i.test(c))throw Error('Attribute "'+c+'" requires goog.string.Const value, "'+d+'" given.');if(c.toLowerCase()in K.b.l.Ci)if(d instanceof K.b.C)d=K.b.C.u(d);else if(d instanceof K.b.o)d=K.b.o.u(d);else if(K.L(d))d=K.b.o.Gc(d).ga();else throw Error('Attribute "'+c+'" on tag "'+b+'" requires goog.html.SafeUrl, goog.string.Const, or string, value "'+d+'" given.');}d.ua&&(d=d.ga());
return c+'="'+K.f.ta(String(d))+'"'};K.b.l.Oj=function(b){if(!K.ha(b))throw Error('The "style" attribute requires goog.html.SafeStyle or map of style properties, '+typeof b+" given: "+b);b instanceof K.b.B||(b=K.b.B.create(b));return K.b.B.u(b)};K.b.l.qq=function(b,c,d,e){c=K.b.l.create(c,d,e);c.kc=b;return c};
K.b.l.concat=function(b){function c(b){K.isArray(b)?K.j.forEach(b,c):(b=K.b.l.ta(b),e+=K.b.l.u(b),b=b.$a(),d==K.h.i.O.qa?d=b:b!=K.h.i.O.qa&&d!=b&&(d=null))}var d=K.h.i.O.qa,e="";K.j.forEach(arguments,c);return K.b.l.ra(e,d)};K.b.l.cq=function(b,c){var d=K.b.l.concat(K.j.slice(arguments,1));d.kc=b;return d};K.b.l.ca={};K.b.l.ra=function(b,c){return(new K.b.l).bb(b,c)};K.b.l.prototype.bb=function(b,c){this.Ga=b;this.kc=c;return this};
K.b.l.Ya=function(b,c,d){var e=null;var f="<"+b+K.b.l.vl(b,c);K.cb(d)?K.isArray(d)||(d=[d]):d=[];K.a.Mc.sk(b.toLowerCase())?f+=">":(e=K.b.l.concat(d),f+=">"+K.b.l.u(e)+"</"+b+">",e=e.$a());(b=c&&c.dir)&&(e=/^(ltr|rtl|auto)$/i.test(b)?K.h.i.O.qa:null);return K.b.l.ra(f,e)};K.b.l.vl=function(b,c){var d="";if(c)for(var e in c){if(!K.b.l.Ze.test(e))throw Error('Invalid attribute name "'+e+'".');var f=c[e];K.cb(f)&&(d+=" "+K.b.l.Cj(b,e,f))}return d};
K.b.l.hc=function(b,c,d){var e={},f;for(f in b)e[f]=b[f];for(f in c)e[f]=c[f];for(f in d){var g=f.toLowerCase();if(g in b)throw Error('Cannot override "'+g+'" attribute, got "'+f+'" with value "'+d[f]+'"');g in c&&delete e[g];e[f]=d[f]}return e};K.b.l.Qm=K.b.l.ra("<!DOCTYPE html>",K.h.i.O.qa);K.b.l.EMPTY=K.b.l.ra("",K.h.i.O.qa);K.b.l.we=K.b.l.ra("<br>",K.h.i.O.qa);K.a.S={};K.a.S.In={Zl:"afterbegin",$l:"afterend",om:"beforebegin",pm:"beforeend"};K.a.S.sr=function(b,c,d){b.insertAdjacentHTML(c,K.b.l.u(d))};K.a.S.ui={MATH:!0,SCRIPT:!0,STYLE:!0,SVG:!0,TEMPLATE:!0};K.a.S.lh=function(b,c){if(K.m.ja&&K.a.S.ui[b.tagName.toUpperCase()])throw Error("goog.dom.safe.setInnerHtml cannot be used to set content of "+b.tagName+".");b.innerHTML=K.b.l.u(c)};K.a.S.ot=function(b,c){b.outerHTML=K.b.l.u(c)};K.a.S.qt=function(b,c){b.style.cssText=K.b.B.u(c)};K.a.S.wq=function(b,c){b.write(K.b.l.u(c))};
K.a.S.at=function(b,c){c=c instanceof K.b.o?c:K.b.o.Vb(c);b.href=K.b.o.u(c)};K.a.S.kt=function(b,c){c=c instanceof K.b.o?c:K.b.o.Vb(c);b.src=K.b.o.u(c)};K.a.S.et=function(b,c){b.src=K.b.C.u(c)};K.a.S.gt=function(b,c){b.src=K.b.C.u(c)};K.a.S.it=function(b,c){b.src=K.b.C.u(c)};K.a.S.jt=function(b,c){b.srcdoc=K.b.l.u(c)};K.a.S.lt=function(b,c,d){b.rel=d;K.f.kf(d,"stylesheet")?b.href=K.b.C.u(c):b.href=c instanceof K.b.C?K.b.C.u(c):c instanceof K.b.o?K.b.o.u(c):K.b.o.Vb(c).ga()};
K.a.S.nt=function(b,c){b.data=K.b.C.u(c)};K.a.S.ol=function(b,c){b.src=K.b.C.u(c)};K.a.S.pt=function(b,c){b.text=K.b.V.u(c)};K.a.S.mt=function(b,c){c=c instanceof K.b.o?c:K.b.o.Vb(c);b.href=K.b.o.u(c)};K.a.S.zs=function(b,c,d,e,f){b=b instanceof K.b.o?b:K.b.o.Vb(b);return(c||window).open(K.b.o.u(b),d?K.f.I.u(d):"",e,f)};K.b.hb={};K.b.hb.fl=function(b,c){return K.b.l.ra(c,null)};K.b.hb.Xs=function(b,c){return K.b.V.hd(c)};K.b.hb.Ys=function(b,c){return K.b.B.Fb(c)};K.b.hb.Zs=function(b,c){return K.b.N.Gb(c)};K.b.hb.$s=function(b,c){return K.b.o.Fa(c)};K.b.hb.Ot=function(b,c){return K.b.C.Hb(c)};K.s={};K.s.Fs=function(b){return Math.floor(Math.random()*b)};K.s.Qt=function(b,c){return b+Math.random()*(c-b)};K.s.Wp=function(b,c,d){return Math.min(Math.max(b,c),d)};K.s.Vg=function(b,c){b%=c;return 0>b*c?b+c:b};K.s.bs=function(b,c,d){return b+d*(c-b)};K.s.ps=function(b,c,d){return Math.abs(b-c)<=(d||1E-6)};K.s.fe=function(b){return K.s.Vg(b,360)};K.s.At=function(b){return K.s.Vg(b,2*Math.PI)};K.s.uh=function(b){return b*Math.PI/180};K.s.zl=function(b){return 180*b/Math.PI};
K.s.mp=function(b,c){return c*Math.cos(K.s.uh(b))};K.s.np=function(b,c){return c*Math.sin(K.s.uh(b))};K.s.angle=function(b,c,d,e){return K.s.fe(K.s.zl(Math.atan2(e-c,d-b)))};K.s.lp=function(b,c){b=K.s.fe(c)-K.s.fe(b);180<b?b-=360:-180>=b&&(b=360+b);return b};K.s.sign=function(b){return 0<b?1:0>b?-1:b};
K.s.gs=function(b,c,d,e){d=d||function(b,c){return b==c};e=e||function(c){return b[c]};for(var f=b.length,g=c.length,h=[],l=0;l<f+1;l++)h[l]=[],h[l][0]=0;for(var m=0;m<g+1;m++)h[0][m]=0;for(l=1;l<=f;l++)for(m=1;m<=g;m++)d(b[l-1],c[m-1])?h[l][m]=h[l-1][m-1]+1:h[l][m]=Math.max(h[l-1][m],h[l][m-1]);var q=[];l=f;for(m=g;0<l&&0<m;)d(b[l-1],c[m-1])?(q.unshift(e(l-1,m-1)),l--,m--):h[l-1][m]>h[l][m-1]?l--:m--;return q};K.s.ge=function(b){return K.j.reduce(arguments,function(b,d){return b+d},0)};
K.s.Pi=function(b){return K.s.ge.apply(null,arguments)/arguments.length};K.s.hl=function(b){var c=arguments.length;if(2>c)return 0;var d=K.s.Pi.apply(null,arguments);return K.s.ge.apply(null,K.j.map(arguments,function(b){return Math.pow(b-d,2)}))/(c-1)};K.s.Bt=function(b){return Math.sqrt(K.s.hl.apply(null,arguments))};K.s.Hr=function(b){return isFinite(b)&&0==b%1};K.s.Fr=function(b){return isFinite(b)};K.s.Mr=function(b){return 0==b&&0>1/b};
K.s.fs=function(b){if(0<b){var c=Math.round(Math.log(b)*Math.LOG10E);return c-(parseFloat("1e"+c)>b?1:0)}return 0==b?-Infinity:NaN};K.s.Vs=function(b,c){return Math.floor(b+(c||2E-15))};K.s.Us=function(b,c){return Math.ceil(b-(c||2E-15))};K.s.W=function(b,c){this.x=K.R(b)?b:0;this.y=K.R(c)?c:0};K.s.W.prototype.clone=function(){return new K.s.W(this.x,this.y)};K.ea&&(K.s.W.prototype.toString=function(){return"("+this.x+", "+this.y+")"});K.s.W.prototype.Ib=function(b){return b instanceof K.s.W&&K.s.W.Ib(this,b)};K.s.W.Ib=function(b,c){return b==c?!0:b&&c?b.x==c.x&&b.y==c.y:!1};K.s.W.vq=function(b,c){var d=b.x-c.x;b=b.y-c.y;return Math.sqrt(d*d+b*b)};K.s.W.hs=function(b){return Math.sqrt(b.x*b.x+b.y*b.y)};
K.s.W.azimuth=function(b){return K.s.angle(0,0,b.x,b.y)};K.s.W.yt=function(b,c){var d=b.x-c.x;b=b.y-c.y;return d*d+b*b};K.s.W.uq=function(b,c){return new K.s.W(b.x-c.x,b.y-c.y)};K.s.W.ge=function(b,c){return new K.s.W(b.x+c.x,b.y+c.y)};I=K.s.W.prototype;I.ceil=function(){this.x=Math.ceil(this.x);this.y=Math.ceil(this.y);return this};I.floor=function(){this.x=Math.floor(this.x);this.y=Math.floor(this.y);return this};I.round=function(){this.x=Math.round(this.x);this.y=Math.round(this.y);return this};
I.translate=function(b,c){b instanceof K.s.W?(this.x+=b.x,this.y+=b.y):(this.x+=Number(b),K.Rb(c)&&(this.y+=c));return this};I.scale=function(b,c){c=K.Rb(c)?c:b;this.x*=b;this.y*=c;return this};K.s.nb=function(b,c){this.width=b;this.height=c};K.s.nb.Ib=function(b,c){return b==c?!0:b&&c?b.width==c.width&&b.height==c.height:!1};K.s.nb.prototype.clone=function(){return new K.s.nb(this.width,this.height)};K.ea&&(K.s.nb.prototype.toString=function(){return"("+this.width+" x "+this.height+")"});I=K.s.nb.prototype;I.Li=function(){return this.width*this.height};I.aspectRatio=function(){return this.width/this.height};I.Qb=function(){return!this.Li()};
I.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};I.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};I.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};I.scale=function(b,c){c=K.Rb(c)?c:b;this.width*=b;this.height*=c;return this};K.a.Hh=!1;K.a.se=!1;K.a.Qh=K.a.Hh||K.a.se;K.a.td=function(b){return b?new K.a.lb(K.a.Qa(b)):K.a.mj||(K.a.mj=new K.a.lb)};K.a.Dj=function(){return document};K.a.ud=function(b){return K.a.xd(document,b)};K.a.xd=function(b,c){return K.L(c)?b.getElementById(c):c};K.a.Lj=function(b){return K.a.ig(document,b)};K.a.ig=function(b,c){return K.a.xd(b,c)};K.a.Bh=K.a.ud;K.a.getElementsByTagName=function(b,c){return(c||document).getElementsByTagName(String(b))};
K.a.yd=function(b,c,d){return K.a.nc(document,b,c,d)};K.a.Gj=function(b,c,d){return K.a.wd(document,b,c,d)};K.a.Rf=function(b,c){var d=c||document;return K.a.cd(d)?d.querySelectorAll("."+b):K.a.nc(document,"*",b,c)};K.a.vd=function(b,c){var d=c||document;return(d.getElementsByClassName?d.getElementsByClassName(b)[0]:K.a.wd(document,"*",b,c))||null};K.a.hg=function(b,c){return K.a.vd(b,c)};K.a.cd=function(b){return!(!b.querySelectorAll||!b.querySelector)};
K.a.nc=function(b,c,d,e){b=e||b;c=c&&"*"!=c?String(c).toUpperCase():"";if(K.a.cd(b)&&(c||d))return b.querySelectorAll(c+(d?"."+d:""));if(d&&b.getElementsByClassName){b=b.getElementsByClassName(d);if(c){e={};for(var f=0,g=0,h;h=b[g];g++)c==h.nodeName&&(e[f++]=h);e.length=f;return e}return b}b=b.getElementsByTagName(c||"*");if(d){e={};for(g=f=0;h=b[g];g++)c=h.className,typeof c.split==u&&K.j.contains(c.split(/\s+/),d)&&(e[f++]=h);e.length=f;return e}return b};
K.a.wd=function(b,c,d,e){var f=e||b,g=c&&"*"!=c?String(c).toUpperCase():"";return K.a.cd(f)&&(g||d)?f.querySelector(g+(d?"."+d:"")):K.a.nc(b,c,d,e)[0]||null};K.a.Ch=K.a.yd;K.a.Jc=function(b,c){K.object.forEach(c,function(c,e){c&&c.ua&&(c=c.ga());"style"==e?b.style.cssText=c:"class"==e?b.className=c:"for"==e?b.htmlFor=c:K.a.Be.hasOwnProperty(e)?b.setAttribute(K.a.Be[e],c):K.f.startsWith(e,"aria-")||K.f.startsWith(e,"data-")?b.setAttribute(e,c):b[e]=c})};
K.a.Be={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};K.a.mg=function(b){return K.a.ng(b||window)};K.a.ng=function(b){b=b.document;b=K.a.Ob(b)?b.documentElement:b.body;return new K.s.nb(b.clientWidth,b.clientHeight)};K.a.Ej=function(){return K.a.rd(window)};K.a.Xq=function(b){return K.a.rd(b)};
K.a.rd=function(b){var c=b.document,d=0;if(c){d=c.body;var e=c.documentElement;if(!e||!d)return 0;b=K.a.ng(b).height;if(K.a.Ob(c)&&e.scrollHeight)d=e.scrollHeight!=b?e.scrollHeight:e.offsetHeight;else{c=e.scrollHeight;var f=e.offsetHeight;e.clientHeight!=f&&(c=d.scrollHeight,f=d.offsetHeight);d=c>b?c>f?c:f:c<f?c:f}}return d};K.a.dr=function(b){return K.a.td((b||K.global||window).document).Pf()};K.a.Pf=function(){return K.a.Qf(document)};
K.a.Qf=function(b){var c=K.a.sd(b);b=K.a.qc(b);return K.userAgent.Y&&K.userAgent.va("10")&&b.pageYOffset!=c.scrollTop?new K.s.W(c.scrollLeft,c.scrollTop):new K.s.W(b.pageXOffset||c.scrollLeft,b.pageYOffset||c.scrollTop)};K.a.Fj=function(){return K.a.sd(document)};K.a.sd=function(b){return b.scrollingElement?b.scrollingElement:!K.userAgent.Cb&&K.a.Ob(b)?b.documentElement:b.body||b.documentElement};K.a.tb=function(b){return b?K.a.qc(b):window};K.a.qc=function(b){return b.parentWindow||b.defaultView};
K.a.fd=function(b,c,d){return K.a.sf(document,arguments)};K.a.sf=function(b,c){var d=String(c[0]),e=c[1];if(!K.a.ib.Lh&&e&&(e.name||e.type)){d=["<",d];e.name&&d.push(' name="',K.f.ta(e.name),'"');if(e.type){d.push(' type="',K.f.ta(e.type),'"');var f={};K.object.extend(f,e);delete f.type;e=f}d.push(">");d=d.join("")}d=b.createElement(d);e&&(K.L(e)?d.className=e:K.isArray(e)?d.className=e.join(" "):K.a.Jc(d,e));2<c.length&&K.a.bf(b,d,c,2);return d};
K.a.bf=function(b,c,d,e){function f(d){d&&c.appendChild(K.L(d)?b.createTextNode(d):d)}for(;e<d.length;e++){var g=d[e];K.Nb(g)&&!K.a.Ld(g)?K.j.forEach(K.a.Md(g)?K.j.th(g):g,f):f(g)}};K.a.Dh=K.a.fd;K.a.createElement=function(b){return K.a.Oa(document,b)};K.a.Oa=function(b,c){return b.createElement(String(c))};K.a.createTextNode=function(b){return document.createTextNode(String(b))};K.a.hj=function(b,c,d){return K.a.tf(document,b,c,!!d)};
K.a.tf=function(b,c,d,e){for(var f=K.a.Oa(b,"TABLE"),g=f.appendChild(K.a.Oa(b,"TBODY")),h=0;h<c;h++){for(var l=K.a.Oa(b,"TR"),m=0;m<d;m++){var q=K.a.Oa(b,"TD");e&&K.a.ae(q,K.f.Ye.Ke);l.appendChild(q)}g.appendChild(l)}return f};K.a.eq=function(b){var c=K.j.map(arguments,K.f.I.u);c=K.b.hb.fl(K.f.I.from("Constant HTML string, that gets turned into a Node later, so it will be automatically balanced."),c.join(""));return K.a.eh(c)};K.a.eh=function(b){return K.a.fh(document,b)};
K.a.fh=function(b,c){var d=K.a.Oa(b,"DIV");K.a.ib.ci?(K.a.S.lh(d,K.b.l.concat(K.b.l.we,c)),d.removeChild(d.firstChild)):K.a.S.lh(d,c);return K.a.$i(b,d)};K.a.$i=function(b,c){if(1==c.childNodes.length)return c.removeChild(c.firstChild);for(b=b.createDocumentFragment();c.firstChild;)b.appendChild(c.firstChild);return b};K.a.dk=function(){return K.a.Ob(document)};K.a.Ob=function(b){return K.a.Qh?K.a.se:"CSS1Compat"==b.compatMode};K.a.canHaveChildren=function(b){if(b.nodeType!=K.a.fa.Ia)return!1;switch(b.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case p:case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};
K.a.appendChild=function(b,c){b.appendChild(c)};K.a.append=function(b,c){K.a.bf(K.a.Qa(b),b,arguments,1)};K.a.Zd=function(b){for(var c;c=b.firstChild;)b.removeChild(c)};K.a.vg=function(b,c){c.parentNode&&c.parentNode.insertBefore(b,c)};K.a.ug=function(b,c){c.parentNode&&c.parentNode.insertBefore(b,c.nextSibling)};K.a.tg=function(b,c,d){b.insertBefore(c,b.childNodes[d]||null)};K.a.removeNode=function(b){return b&&b.parentNode?b.parentNode.removeChild(b):null};
K.a.dh=function(b,c){var d=c.parentNode;d&&d.replaceChild(b,c)};K.a.Ff=function(b){var c,d=b.parentNode;if(d&&d.nodeType!=K.a.fa.Vh){if(b.removeNode)return b.removeNode(!1);for(;c=b.firstChild;)d.insertBefore(c,b);return K.a.removeNode(b)}};K.a.Nf=function(b){return K.a.ib.Mh&&void 0!=b.children?b.children:K.j.filter(b.childNodes,function(b){return b.nodeType==K.a.fa.Ia})};K.a.Sf=function(b){return K.R(b.firstElementChild)?b.firstElementChild:K.a.oc(b.firstChild,!0)};
K.a.Wf=function(b){return K.R(b.lastElementChild)?b.lastElementChild:K.a.oc(b.lastChild,!1)};K.a.Zf=function(b){return K.R(b.nextElementSibling)?b.nextElementSibling:K.a.oc(b.nextSibling,!0)};K.a.fg=function(b){return K.R(b.previousElementSibling)?b.previousElementSibling:K.a.oc(b.previousSibling,!1)};K.a.oc=function(b,c){for(;b&&b.nodeType!=K.a.fa.Ia;)b=c?b.nextSibling:b.previousSibling;return b};
K.a.$f=function(b){if(!b)return null;if(b.firstChild)return b.firstChild;for(;b&&!b.nextSibling;)b=b.parentNode;return b?b.nextSibling:null};K.a.gg=function(b){if(!b)return null;if(!b.previousSibling)return b.parentNode;for(b=b.previousSibling;b&&b.lastChild;)b=b.lastChild;return b};K.a.Ld=function(b){return K.ha(b)&&0<b.nodeType};K.a.Hd=function(b){return K.ha(b)&&b.nodeType==K.a.fa.Ia};K.a.Ng=function(b){return K.ha(b)&&b.window==b};
K.a.eg=function(b){var c;if(K.a.ib.Nh&&!(K.userAgent.Y&&K.userAgent.va("9")&&!K.userAgent.va("10")&&K.global.SVGElement&&b instanceof K.global.SVGElement)&&(c=b.parentElement))return c;c=b.parentNode;return K.a.Hd(c)?c:null};K.a.contains=function(b,c){if(!b||!c)return!1;if(b.contains&&c.nodeType==K.a.fa.Ia)return b==c||b.contains(c);if("undefined"!=typeof b.compareDocumentPosition)return b==c||!!(b.compareDocumentPosition(c)&16);for(;c&&b!=c;)c=c.parentNode;return c==b};
K.a.mf=function(b,c){if(b==c)return 0;if(b.compareDocumentPosition)return b.compareDocumentPosition(c)&2?1:-1;if(K.userAgent.Y&&!K.userAgent.Pb(9)){if(b.nodeType==K.a.fa.Xc)return-1;if(c.nodeType==K.a.fa.Xc)return 1}if("sourceIndex"in b||b.parentNode&&"sourceIndex"in b.parentNode){var d=b.nodeType==K.a.fa.Ia,e=c.nodeType==K.a.fa.Ia;if(d&&e)return b.sourceIndex-c.sourceIndex;var f=b.parentNode,g=c.parentNode;return f==g?K.a.pf(b,c):!d&&K.a.contains(f,c)?-1*K.a.nf(b,c):!e&&K.a.contains(g,b)?K.a.nf(c,
b):(d?b.sourceIndex:f.sourceIndex)-(e?c.sourceIndex:g.sourceIndex)}e=K.a.Qa(b);d=e.createRange();d.selectNode(b);d.collapse(!0);b=e.createRange();b.selectNode(c);b.collapse(!0);return d.compareBoundaryPoints(K.global.Range.START_TO_END,b)};K.a.nf=function(b,c){var d=b.parentNode;if(d==c)return-1;for(;c.parentNode!=d;)c=c.parentNode;return K.a.pf(c,b)};K.a.pf=function(b,c){for(;c=c.previousSibling;)if(c==b)return-1;return 1};
K.a.Bf=function(b){var c,d=arguments.length;if(!d)return null;if(1==d)return arguments[0];var e=[],f=Infinity;for(c=0;c<d;c++){for(var g=[],h=arguments[c];h;)g.unshift(h),h=h.parentNode;e.push(g);f=Math.min(f,g.length)}g=null;for(c=0;c<f;c++){h=e[0][c];for(var l=1;l<d;l++)if(h!=e[l][c])return g;g=h}return g};K.a.Qa=function(b){return b.nodeType==K.a.fa.Xc?b:b.ownerDocument||b.document};K.a.Tf=function(b){return b.contentDocument||b.contentWindow.document};
K.a.Uf=function(b){try{return b.contentWindow||(b.contentDocument?K.a.tb(b.contentDocument):null)}catch(c){}return null};K.a.ae=function(b,c){if("textContent"in b)b.textContent=c;else if(b.nodeType==K.a.fa.cc)b.data=String(c);else if(b.firstChild&&b.firstChild.nodeType==K.a.fa.cc){for(;b.lastChild!=b.firstChild;)b.removeChild(b.lastChild);b.firstChild.data=String(c)}else{K.a.Zd(b);var d=K.a.Qa(b);b.appendChild(d.createTextNode(String(c)))}};
K.a.dg=function(b){if("outerHTML"in b)return b.outerHTML;var c=K.a.Qa(b);c=K.a.Oa(c,"DIV");c.appendChild(b.cloneNode(!0));return c.innerHTML};K.a.Cf=function(b,c){var d=[];return K.a.nd(b,c,d,!0)?d[0]:void 0};K.a.Df=function(b,c){var d=[];K.a.nd(b,c,d,!1);return d};K.a.nd=function(b,c,d,e){if(null!=b)for(b=b.firstChild;b;){if(c(b)&&(d.push(b),e)||K.a.nd(b,c,d,e))return!0;b=b.nextSibling}return!1};K.a.Ue={SCRIPT:1,STYLE:1,HEAD:1,IFRAME:1,OBJECT:1};K.a.ac={IMG:" ",BR:"\n"};
K.a.Jd=function(b){return K.a.pg(b)&&K.a.Lg(b)};K.a.jh=function(b,c){c?b.tabIndex=0:(b.tabIndex=-1,b.removeAttribute("tabIndex"))};K.a.Cg=function(b){var c;return(c=K.a.Qk(b)?!b.disabled&&(!K.a.pg(b)||K.a.Lg(b)):K.a.Jd(b))&&K.userAgent.Y?K.a.Wj(b):c};K.a.pg=function(b){return K.userAgent.Y&&!K.userAgent.va("9")?(b=b.getAttributeNode("tabindex"),K.cb(b)&&b.specified):b.hasAttribute("tabindex")};K.a.Lg=function(b){b=b.tabIndex;return K.Rb(b)&&0<=b&&32768>b};
K.a.Qk=function(b){return"A"==b.tagName||"INPUT"==b.tagName||"TEXTAREA"==b.tagName||"SELECT"==b.tagName||"BUTTON"==b.tagName};K.a.Wj=function(b){b=!K.ya(b.getBoundingClientRect)||K.userAgent.Y&&null==b.parentElement?{height:b.offsetHeight,width:b.offsetWidth}:b.getBoundingClientRect();return K.cb(b)&&0<b.height&&0<b.width};
K.a.pc=function(b){if(K.a.ib.xe&&null!==b&&"innerText"in b)b=K.f.Yi(b.innerText);else{var c=[];K.a.Ad(b,c,!0);b=c.join("")}b=b.replace(/ \xAD /g," ").replace(/\xAD/g,"");b=b.replace(/\u200B/g,"");K.a.ib.xe||(b=b.replace(/ +/g," "));" "!=b&&(b=b.replace(/^\s*/,""));return b};K.a.gr=function(b){var c=[];K.a.Ad(b,c,!1);return c.join("")};
K.a.Ad=function(b,c,d){if(!(b.nodeName in K.a.Ue))if(b.nodeType==K.a.fa.cc)d?c.push(String(b.nodeValue).replace(/(\r\n|\r|\n)/g,"")):c.push(b.nodeValue);else if(b.nodeName in K.a.ac)c.push(K.a.ac[b.nodeName]);else for(b=b.firstChild;b;)K.a.Ad(b,c,d),b=b.nextSibling};K.a.bg=function(b){return K.a.pc(b).length};K.a.cg=function(b,c){c=c||K.a.Qa(b).body;for(var d=[];b&&b!=c;){for(var e=b;e=e.previousSibling;)d.unshift(K.a.pc(e));b=b.parentNode}return K.f.trimLeft(d.join("")).replace(/ +/g," ").length};
K.a.ag=function(b,c,d){b=[b];for(var e=0,f=null;0<b.length&&e<c;)if(f=b.pop(),!(f.nodeName in K.a.Ue))if(f.nodeType==K.a.fa.cc){var g=f.nodeValue.replace(/(\r\n|\r|\n)/g,"").replace(/ +/g," ");e+=g.length}else if(f.nodeName in K.a.ac)e+=K.a.ac[f.nodeName].length;else for(g=f.childNodes.length-1;0<=g;g--)b.push(f.childNodes[g]);K.ha(d)&&(d.Is=f?f.nodeValue.length+c-e-1:0,d.node=f);return f};
K.a.Md=function(b){if(b&&typeof b.length==x){if(K.ha(b))return typeof b.item==u||typeof b.item==B;if(K.ya(b))return typeof b.item==u}return!1};K.a.qd=function(b,c,d,e){if(!c&&!d)return null;var f=c?String(c).toUpperCase():null;return K.a.pd(b,function(b){return(!f||b.nodeName==f)&&(!d||K.L(b.className)&&K.j.contains(b.className.split(/\s+/),d))},!0,e)};K.a.Kf=function(b,c,d){return K.a.qd(b,null,c,d)};
K.a.pd=function(b,c,d,e){b&&!d&&(b=b.parentNode);for(d=0;b&&(null==e||d<=e);){if(c(b))return b;b=b.parentNode;d++}return null};K.a.Jf=function(b){try{return b&&b.activeElement}catch(c){}return null};K.a.er=function(){var b=K.a.tb();return K.R(b.devicePixelRatio)?b.devicePixelRatio:b.matchMedia?K.a.wc(3)||K.a.wc(2)||K.a.wc(1.5)||K.a.wc(1)||.75:1};
K.a.wc=function(b){return K.a.tb().matchMedia("(min-resolution: "+b+"dppx),(min--moz-device-pixel-ratio: "+b+"),(min-resolution: "+96*b+"dpi)").matches?b:0};K.a.Mf=function(b){return b.getContext("2d")};K.a.lb=function(b){this.X=b||K.global.document||document};I=K.a.lb.prototype;I.td=K.a.td;I.Dj=G("X");I.ud=function(b){return K.a.xd(this.X,b)};I.Lj=function(b){return K.a.ig(this.X,b)};I.Bh=K.a.lb.prototype.ud;I.getElementsByTagName=function(b,c){return(c||this.X).getElementsByTagName(String(b))};
I.yd=function(b,c,d){return K.a.nc(this.X,b,c,d)};I.Gj=function(b,c,d){return K.a.wd(this.X,b,c,d)};I.Rf=function(b,c){return K.a.Rf(b,c||this.X)};I.vd=function(b,c){return K.a.vd(b,c||this.X)};I.hg=function(b,c){return K.a.hg(b,c||this.X)};I.Ch=K.a.lb.prototype.yd;I.Jc=K.a.Jc;I.mg=function(b){return K.a.mg(b||this.tb())};I.Ej=function(){return K.a.rd(this.tb())};I.fd=function(b,c,d){return K.a.sf(this.X,arguments)};I.Dh=K.a.lb.prototype.fd;I.createElement=function(b){return K.a.Oa(this.X,b)};
I.createTextNode=function(b){return this.X.createTextNode(String(b))};I.hj=function(b,c,d){return K.a.tf(this.X,b,c,!!d)};I.eh=function(b){return K.a.fh(this.X,b)};I.dk=function(){return K.a.Ob(this.X)};I.tb=function(){return K.a.qc(this.X)};I.Fj=function(){return K.a.sd(this.X)};I.Pf=function(){return K.a.Qf(this.X)};I.Jf=function(b){return K.a.Jf(b||this.X)};I.appendChild=K.a.appendChild;I.append=K.a.append;I.canHaveChildren=K.a.canHaveChildren;I.Zd=K.a.Zd;I.vg=K.a.vg;I.ug=K.a.ug;I.tg=K.a.tg;
I.removeNode=K.a.removeNode;I.dh=K.a.dh;I.Ff=K.a.Ff;I.Nf=K.a.Nf;I.Sf=K.a.Sf;I.Wf=K.a.Wf;I.Zf=K.a.Zf;I.fg=K.a.fg;I.$f=K.a.$f;I.gg=K.a.gg;I.Ld=K.a.Ld;I.Hd=K.a.Hd;I.Ng=K.a.Ng;I.eg=K.a.eg;I.contains=K.a.contains;I.mf=K.a.mf;I.Bf=K.a.Bf;I.Qa=K.a.Qa;I.Tf=K.a.Tf;I.Uf=K.a.Uf;I.ae=K.a.ae;I.dg=K.a.dg;I.Cf=K.a.Cf;I.Df=K.a.Df;I.Jd=K.a.Jd;I.jh=K.a.jh;I.Cg=K.a.Cg;I.pc=K.a.pc;I.bg=K.a.bg;I.cg=K.a.cg;I.ag=K.a.ag;I.Md=K.a.Md;I.qd=K.a.qd;I.Kf=K.a.Kf;I.pd=K.a.pd;I.Mf=K.a.Mf;K.bh={};K.bh.to=F();K.Thenable=F();K.Thenable.prototype.then=F();K.Thenable.He="$goog_Thenable";K.Thenable.af=function(b){b.prototype.then=b.prototype.then;b.prototype[K.Thenable.He]=!0};K.Thenable.Dg=function(b){if(!b)return!1;try{return!!b[K.Thenable.He]}catch(c){return!1}};K.Promise=function(b,c){this.$=K.Promise.P.wa;this.ia=void 0;this.ob=this.Na=this.da=null;this.ld=!1;0<K.Promise.Wa?this.Oc=0:0==K.Promise.Wa&&(this.rc=!1);K.Promise.Aa&&(this.ee=[],N(this,Error("created")),this.vf=0);if(b!=K.eb)try{var d=this;b.call(c,function(b){O(d,K.Promise.P.Ja,b)},function(b){if(K.ea&&!(b instanceof K.Promise.kb))try{if(b instanceof Error)throw b;throw Error("Promise rejected.");}catch(f){}O(d,K.Promise.P.ka,b)})}catch(e){O(this,K.Promise.P.ka,e)}};K.Promise.Aa=!1;
K.Promise.Wa=0;K.Promise.P={wa:0,Jh:1,Ja:2,ka:3};K.Promise.ze=function(){this.next=this.context=this.wb=this.Tb=this.Xa=null;this.dc=!1};K.Promise.ze.prototype.reset=function(){this.context=this.wb=this.Tb=this.Xa=null;this.dc=!1};K.Promise.Vc=100;K.Promise.Kb=new K.async.Zb(function(){return new K.Promise.ze},function(b){b.reset()},K.Promise.Vc);K.Promise.Lf=function(b,c,d){var e=K.Promise.Kb.get();e.Tb=b;e.wb=c;e.context=d;return e};K.Promise.Yk=function(b){K.Promise.Kb.put(b)};
K.Promise.resolve=function(b){if(b instanceof K.Promise)return b;var c=new K.Promise(K.eb);O(c,K.Promise.P.Ja,b);return c};K.Promise.reject=function(b){return new K.Promise(function(c,d){d(b)})};K.Promise.Ec=function(b,c,d){K.Promise.Ug(b,c,d,null)||K.async.M(K.fb(c,b))};K.Promise.race=function(b){return new K.Promise(function(c,d){b.length||c(void 0);for(var e=0,f;e<b.length;e++)f=b[e],K.Promise.Ec(f,c,d)})};
K.Promise.all=function(b){return new K.Promise(function(c,d){var e=b.length,f=[];if(e)for(var g=function(b,d){e--;f[b]=d;0==e&&c(f)},h=function(b){d(b)},l=0,m;l<b.length;l++)m=b[l],K.Promise.Ec(m,K.fb(g,l),h);else c(f)})};K.Promise.jp=function(b){return new K.Promise(function(c){var d=b.length,e=[];if(d)for(var f=function(b,f,g){d--;e[b]=f?{Bj:!0,value:g}:{Bj:!1,reason:g};0==d&&c(e)},g=0,h;g<b.length;g++)h=b[g],K.Promise.Ec(h,K.fb(f,g,!0),K.fb(f,g,!1));else c(e)})};
K.Promise.Iq=function(b){return new K.Promise(function(c,d){var e=b.length,f=[];if(e)for(var g=function(b){c(b)},h=function(b,c){e--;f[b]=c;0==e&&d(f)},l=0,m;l<b.length;l++)m=b[l],K.Promise.Ec(m,g,K.fb(h,l));else c(void 0)})};K.Promise.Wt=function(){var b,c,d=new K.Promise(function(d,f){b=d;c=f});return new K.Promise.li(d,b,c)};K.Promise.prototype.then=function(b,c,d){K.Promise.Aa&&N(this,Error("then"));return ba(this,K.ya(b)?b:null,K.ya(c)?c:null,d)};K.Thenable.af(K.Promise);
K.Promise.prototype.cancel=function(b){this.$==K.Promise.P.wa&&K.async.M(function(){var c=new K.Promise.kb(b);P(this,c)},this)};function P(b,c){if(b.$==K.Promise.P.wa)if(b.da){var d=b.da;if(d.Na){for(var e=0,f=null,g=null,h=d.Na;h&&(h.dc||(e++,h.Xa==b&&(f=h),!(f&&1<e)));h=h.next)f||(g=h);f&&(d.$==K.Promise.P.wa&&1==e?P(d,c):(g?(e=g,e.next==d.ob&&(d.ob=e),e.next=e.next.next):Q(d),R(d,f,K.Promise.P.ka,c)))}b.da=null}else O(b,K.Promise.P.ka,c)}
function S(b,c){b.Na||b.$!=K.Promise.P.Ja&&b.$!=K.Promise.P.ka||T(b);b.ob?b.ob.next=c:b.Na=c;b.ob=c}function ba(b,c,d,e){var f=K.Promise.Lf(null,null,null);f.Xa=new K.Promise(function(b,h){f.Tb=c?function(d){try{var f=c.call(e,d);b(f)}catch(q){h(q)}}:b;f.wb=d?function(c){try{var f=d.call(e,c);!K.R(f)&&c instanceof K.Promise.kb?h(c):b(f)}catch(q){h(q)}}:h});f.Xa.da=b;S(b,f);return f.Xa}K.Promise.prototype.Dl=function(b){this.$=K.Promise.P.wa;O(this,K.Promise.P.Ja,b)};
K.Promise.prototype.El=function(b){this.$=K.Promise.P.wa;O(this,K.Promise.P.ka,b)};function O(b,c,d){b.$==K.Promise.P.wa&&(b===d&&(c=K.Promise.P.ka,d=new TypeError("Promise cannot resolve to itself")),b.$=K.Promise.P.Jh,K.Promise.Ug(d,b.Dl,b.El,b)||(b.ia=d,b.$=c,b.da=null,T(b),c!=K.Promise.P.ka||d instanceof K.Promise.kb||K.Promise.Ii(b,d)))}
K.Promise.Ug=function(b,c,d,e){if(b instanceof K.Promise)return K.Promise.Aa&&N(b,Error("then")),S(b,K.Promise.Lf(c||K.eb,d||null,e)),!0;if(K.Thenable.Dg(b))return b.then(c,d,e),!0;if(K.ha(b))try{var f=b.then;if(K.ya(f))return K.Promise.Bl(b,f,c,d,e),!0}catch(g){return d.call(e,g),!0}return!1};K.Promise.Bl=function(b,c,d,e,f){function g(b){l||(l=!0,e.call(f,b))}function h(b){l||(l=!0,d.call(f,b))}var l=!1;try{c.call(b,h,g)}catch(m){g(m)}};function T(b){b.ld||(b.ld=!0,K.async.M(b.vj,b))}
function Q(b){var c=null;b.Na&&(c=b.Na,b.Na=c.next,c.next=null);b.Na||(b.ob=null);return c}K.Promise.prototype.vj=function(){for(var b;b=Q(this);)K.Promise.Aa&&this.vf++,R(this,b,this.$,this.ia);this.ld=!1};
function R(b,c,d,e){if(d==K.Promise.P.ka&&c.wb&&!c.dc)if(0<K.Promise.Wa)for(;b&&b.Oc;b=b.da)K.global.clearTimeout(b.Oc),b.Oc=0;else if(0==K.Promise.Wa)for(;b&&b.rc;b=b.da)b.rc=!1;if(c.Xa)c.Xa.da=null,K.Promise.xg(c,d,e);else try{c.dc?c.Tb.call(c.context):K.Promise.xg(c,d,e)}catch(f){K.Promise.sc.call(null,f)}K.Promise.Yk(c)}K.Promise.xg=function(b,c,d){c==K.Promise.P.Ja?b.Tb.call(b.context,d):b.wb&&b.wb.call(b.context,d)};
function N(b,c){if(K.Promise.Aa&&K.L(c.stack)){var d=c.stack.split("\n",4)[3];c=c.message;c+=Array(11-c.length).join(" ");b.ee.push(c+d)}}function U(b,c){if(K.Promise.Aa&&c&&K.L(c.stack)&&b.ee.length){for(var d=["Promise trace:"],e=b;e;e=e.da){for(var f=b.vf;0<=f;f--)d.push(e.ee[f]);d.push("Value: ["+(e.$==K.Promise.P.ka?"REJECTED":"FULFILLED")+"] <"+String(e.ia)+">")}c.stack+="\n\n"+d.join("\n")}}
K.Promise.Ii=function(b,c){0<K.Promise.Wa?b.Oc=K.global.setTimeout(function(){U(b,c);K.Promise.sc.call(null,c)},K.Promise.Wa):0==K.Promise.Wa&&(b.rc=!0,K.async.M(function(){b.rc&&(U(b,c),K.Promise.sc.call(null,c))}))};K.Promise.sc=K.async.qh;K.Promise.st=function(b){K.Promise.sc=b};K.Promise.kb=function(b){K.debug.Error.call(this,b)};K.ab(K.Promise.kb,K.debug.Error);K.Promise.kb.prototype.name="cancel";K.Promise.li=function(b,c,d){this.bh=b;this.resolve=c;this.reject=d};/*
 Portions of this code are from MochiKit, received by
 The Closure Authors under the MIT license. All other code is Copyright
 2005-2009 The Closure Authors. All Rights Reserved.
*/
K.async.w=function(b,c){this.Ic=[];this.ah=b;this.wf=c||null;this.ub=this.qb=!1;this.ia=void 0;this.be=this.Ti=this.bd=!1;this.Nc=0;this.da=null;this.ec=0;K.async.w.Aa&&(this.ed=null,Error.captureStackTrace&&(b={stack:""},Error.captureStackTrace(b,K.async.w),typeof b.stack==B&&(this.ed=b.stack.replace(/^[^\n]*\n/,""))))};K.async.w.vi=!1;K.async.w.Aa=!1;I=K.async.w.prototype;
I.cancel=function(b){if(this.qb)this.ia instanceof K.async.w&&this.ia.cancel();else{if(this.da){var c=this.da;delete this.da;b?c.cancel(b):(c.ec--,0>=c.ec&&c.cancel())}this.ah?this.ah.call(this.wf,this):this.be=!0;this.qb||this.Za(new K.async.w.jb(this))}};I.rf=function(b,c){this.bd=!1;V(this,b,c)};function V(b,c,d){b.qb=!0;b.ia=d;b.ub=!c;W(b)}function X(b){if(b.qb){if(!b.be)throw new K.async.w.Wb(b);b.be=!1}}I.Db=function(b){X(this);V(this,!0,b)};I.Za=function(b){X(this);da(this,b);V(this,!1,b)};
function da(b,c){K.async.w.Aa&&b.ed&&K.ha(c)&&c.stack&&/^[^\n]+(\n   [^\n]+)+/.test(c.stack)&&(c.stack=c.stack+"\nDEFERRED OPERATION:\n"+b.ed)}function Y(b,c,d){return Z(b,c,null,d)}function ea(b,c){Z(b,null,c,void 0)}function Z(b,c,d,e){b.Ic.push([c,d,e]);b.qb&&W(b);return b}I.then=function(b,c,d){var e,f,g=new K.Promise(function(b,c){e=b;f=c});Z(this,e,function(b){b instanceof K.async.w.jb?g.cancel():f(b)});return g.then(b,c,d)};K.Thenable.af(K.async.w);
K.async.w.prototype.Vi=function(){var b=new K.async.w;Z(this,b.Db,b.Za,b);b.da=this;this.ec++;return b};function fa(b){return K.j.some(b.Ic,function(b){return K.ya(b[1])})}
function W(b){b.Nc&&b.qb&&fa(b)&&(K.async.w.Il(b.Nc),b.Nc=0);b.da&&(b.da.ec--,delete b.da);for(var c=b.ia,d=!1,e=!1;b.Ic.length&&!b.bd;){var f=b.Ic.shift(),g=f[0],h=f[1];f=f[2];if(g=b.ub?h:g)try{var l=g.call(f||b.wf,c);K.R(l)&&(b.ub=b.ub&&(l==c||l instanceof Error),b.ia=c=l);if(K.Thenable.Dg(c)||typeof K.global.Promise===u&&c instanceof K.global.Promise)e=!0,b.bd=!0}catch(m){c=m,b.ub=!0,da(b,c),fa(b)||(d=!0)}}b.ia=c;e?(e=K.bind(b.rf,b,!0),l=K.bind(b.rf,b,!1),c instanceof K.async.w?(Z(c,e,l),c.Ti=
!0):c.then(e,l)):K.async.w.vi&&c instanceof Error&&!(c instanceof K.async.w.jb)&&(d=b.ub=!0);d&&(b.Nc=K.async.w.kl(c))}K.async.w.oh=function(b){var c=new K.async.w;c.Db(b);return c};K.async.w.Pq=function(b){var c=new K.async.w;c.Db();Y(c,function(){return b});return c};K.async.w.ma=function(b){var c=new K.async.w;c.Za(b);return c};K.async.w.Qp=function(){var b=new K.async.w;b.cancel();return b};K.async.w.Vt=function(b,c,d){return b instanceof K.async.w?Y(b.Vi(),c,d):Y(K.async.w.oh(b),c,d)};
K.async.w.Wb=function(b){K.debug.Error.call(this);this.pb=b};K.ab(K.async.w.Wb,K.debug.Error);K.async.w.Wb.prototype.message="Deferred has already fired";K.async.w.Wb.prototype.name="AlreadyCalledError";K.async.w.jb=function(b){K.debug.Error.call(this);this.pb=b};K.ab(K.async.w.jb,K.debug.Error);K.async.w.jb.prototype.message="Deferred was canceled";K.async.w.jb.prototype.name="CanceledError";K.async.w.Fe=function(b){this.Mb=K.global.setTimeout(K.bind(this.ph,this),0);this.tj=b};
K.async.w.Fe.prototype.ph=function(){delete K.async.w.Jb[this.Mb];throw this.tj;};K.async.w.Jb={};K.async.w.kl=function(b){b=new K.async.w.Fe(b);K.async.w.Jb[b.Mb]=b;return b.Mb};K.async.w.Il=function(b){var c=K.async.w.Jb[b];c&&(K.global.clearTimeout(c.Mb),delete K.async.w.Jb[b])};K.async.w.Dp=function(){var b=K.async.w.Jb,c;for(c in b){var d=b[c];K.global.clearTimeout(d.Mb);d.ph()}};K.D={};K.D.F={};K.D.F.Zc="closure_verification";K.D.F.Th=5E3;K.D.F.$d=[];K.D.F.gl=function(b,c){function d(){var e=b.shift();e=K.D.F.Fc(e,c);b.length&&Z(e,d,d,void 0);return e}if(!b.length)return K.async.w.oh(null);var e=K.D.F.$d.length;K.j.extend(K.D.F.$d,b);if(e)return K.D.F.hh;b=K.D.F.$d;K.D.F.hh=d();return K.D.F.hh};
K.D.F.Fc=function(b,c){var d=c||{};c=d.document||document;var e=K.b.C.u(b),f=K.a.createElement(p),g={ih:f,sh:void 0},h=new K.async.w(K.D.F.Xi,g),l=null,m=K.cb(d.timeout)?d.timeout:K.D.F.Th;0<m&&(l=window.setTimeout(function(){K.D.F.gc(f,!0);h.Za(new K.D.F.Error(K.D.F.Yb.TIMEOUT,"Timeout reached for loading script "+e))},m),g.sh=l);f.onload=f.onreadystatechange=function(){f.readyState&&"loaded"!=f.readyState&&f.readyState!=t||(K.D.F.gc(f,d.Xp||!1,l),h.Db(null))};f.onerror=function(){K.D.F.gc(f,!0,
l);h.Za(new K.D.F.Error(K.D.F.Yb.ei,"Error while loading script "+e))};g=d.attributes||{};K.object.extend(g,{type:C,charset:"UTF-8"});K.a.Jc(f,g);K.a.S.ol(f,b);K.D.F.Mj(c).appendChild(f);return h};
K.D.F.Ws=function(b,c,d){K.global[K.D.F.Zc]||(K.global[K.D.F.Zc]={});var e=K.global[K.D.F.Zc],f=K.b.C.u(b);if(K.R(e[c]))return K.async.w.ma(new K.D.F.Error(K.D.F.Yb.Gi,"Verification object "+c+" already defined."));b=K.D.F.Fc(b,d);var g=new K.async.w(K.bind(b.cancel,b));Y(b,function(){var b=e[c];K.R(b)?(g.Db(b),delete e[c]):g.Za(new K.D.F.Error(K.D.F.Yb.Fi,"Script "+f+" loaded, but verification object "+c+" was not defined."))});ea(b,function(b){K.R(e[c])&&delete e[c];g.Za(b)});return g};
K.D.F.Mj=function(b){var c=K.a.getElementsByTagName("HEAD",b);return!c||K.j.Qb(c)?b.documentElement:c[0]};K.D.F.Xi=function(){if(this&&this.ih){var b=this.ih;b&&b.tagName==p&&K.D.F.gc(b,!0,this.sh)}};K.D.F.gc=function(b,c,d){K.cb(d)&&K.global.clearTimeout(d);b.onload=K.eb;b.onerror=K.eb;b.onreadystatechange=K.eb;c&&window.setTimeout(function(){K.a.removeNode(b)},0)};K.D.F.Yb={ei:0,TIMEOUT:1,Fi:2,Gi:3};
K.D.F.Error=function(b,c){var d="Jsloader error (code #"+b+")";c&&(d+=": "+c);K.debug.Error.call(this,d);this.code=b};K.ab(K.D.F.Error,K.debug.Error);var google={G:{}};google.G.H={};google.G.H.Ba={};google.G.H.Ba.rh=3E4;google.G.H.Ba.js=function(b,c){return{format:b,Mi:c}};google.G.H.Ba.Pj=function(b){return K.b.C.format(b.format,b.Mi)};google.G.H.Ba.load=function(b,c){b=K.b.C.format(b,c);var d=K.D.F.Fc(b,{timeout:google.G.H.Ba.rh,attributes:{async:!1,defer:!1}});return new Promise(function(b){Y(d,b)})};
google.G.H.Ba.cs=function(b){b=K.j.map(b,google.G.H.Ba.Pj);if(K.j.Qb(b))return Promise.resolve();var c={timeout:google.G.H.Ba.rh,attributes:{async:!1,defer:!1}},d=[];!K.userAgent.Y||K.userAgent.va(11)?K.j.forEach(b,function(b){d.push(K.D.F.Fc(b,c))}):d.push(K.D.F.gl(b,c));return Promise.all(K.j.map(d,function(b){return new Promise(function(c){return Y(b,c)})}))};google.G.H.T={};if(K.rb(v))throw Error("Google Charts loader.js can only be loaded once.");google.G.H.T.Nl={41:z,42:z,43:z,44:z,1:"1.0","1.0":"current","1.1":"upcoming",current:"45.2",upcoming:"46"};google.G.H.T.Kk=function(b){var c=b,d=b.match(/^testing-/);d&&(c=c.replace(/^testing-/,""));b=c;do{var e=google.G.H.T.Nl[c];e&&(c=e)}while(e);d=(d?"testing-":"")+c;return{version:c==z?b:d,Dk:d}};google.G.H.T.yh=null;
google.G.H.T.Bk=function(b){var c=google.G.H.T.Kk(b),d=K.f.I.from("https://www.gstatic.com/charts/%{version}/loader.js");return google.G.H.Ba.load(d,{version:c.Dk}).then(function(){var d=K.rb("google.charts.loader.VersionSpecific.load")||K.rb("google.charts.loader.publicLoad")||K.rb("google.charts.versionSpecific.load");if(!d)throw Error("Bad version: "+b);google.G.H.T.yh=function(b){b=d(c.version,b);if(null==b||null==b.then){var e=K.rb("google.charts.loader.publicSetOnLoadCallback")||K.rb("google.charts.versionSpecific.setOnLoadCallback");
b=new Promise(function(b){e(b)});b.then=e}return b}})};google.G.H.T.Pd=null;google.G.H.T.jc=null;google.G.H.T.yk=function(b,c){google.G.H.T.Pd||(google.G.H.T.Pd=google.G.H.T.Bk(b));return google.G.H.T.jc=google.G.H.T.Pd.then(function(){return google.G.H.T.yh(c)})};google.G.H.T.nl=function(b){if(!google.G.H.T.jc)throw Error("Must call google.charts.load before google.charts.setOnLoadCallback");return b?google.G.H.T.jc.then(b):google.G.H.T.jc};
google.G.load=function(b){for(var c=[],d=0;d<arguments.length;++d)c[d-0]=arguments[d];d=0;"visualization"===c[d]&&d++;var e="current";K.L(c[d])&&(e=c[d],d++);var f={};K.ha(c[d])&&(f=c[d]);return google.G.H.T.yk(e,f)};K.zf(v,google.G.load);google.G.ml=google.G.H.T.nl;K.zf("google.charts.setOnLoadCallback",google.G.ml);}).call(this);