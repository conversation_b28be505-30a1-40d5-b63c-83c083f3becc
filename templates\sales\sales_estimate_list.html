{% extends "sales/sidebar.html" %}
{% block sales_estimate %}
<style xmlns="http://www.w3.org/1999/html">
	li.sales_side_menu a{
	outline: none;
	background-color: #484848 !important;
	}
	li.sales_side_menu {
	margin-bottom: 5px;
	}
	#cattable {
		width: 550px;
		position: absolute;
		color: #000000;
		background-color: #FFFFFF;
		/* To align popup window at the center of screen*/
		top: 50%;
		left: 50%;
		margin-top: 100px;
		margin-left: 100px;
	}

	#cattable_2 input,
	#cattable_2 select{
		padding: 0 1px;
		font-size: 12px;
	}

	#cattable_2 td {
		padding: 8px 4px;
	}
	#cattable_2 tr[data-toggle='open'] td:nth-child(13){
		opacity: 0.5;
		background: #ddd;
		pointer-events: none;
	}

	#cattable_2 tr[data-toggle='open'] input,
	#cattable_2 tr[data-toggle='open'] select {
	    opacity: 0.5;
	    background: #ddd;
	}

	.empty-error-border {
	    border: 1px solid #dd4b39 !important;
	}
</style>

<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/notify.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/mailer.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/sales_estimate_list.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/sales_estimate_document.js?v={{ current_version }}"></script>

<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">Sales Estimate</span>
	</div>
	<div class="col-lg-12">
		{% if access_level.edit %}
			<div class="page-heading_new">
				<a href="/erp/sales/sales_estimate/" class="btn btn-new-item pull-right" data-tooltip="tooltip" title="Create New Sales Estimate"><i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span></a>
			</div>
		{% else %}
			<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in Sales Module. Please contact the administrator." style="margin-right: 15px;">
				<a class="btn btn-new-item pull-right disabled"><i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span></a>
			</div>
		{% endif %}
		<div class="filter-components">
			<div class="filter-components-container">
				<div class="dropdown">
					<button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
						<i class="fa fa-filter"></i>
					</button>
					<span class="dropdown-menu arrow_box arrow_box_filter">
				  		<input type="hidden" id="last_created_order_code" value="{{last_created_order}}"/>
						<form id="se_search" method="post" action="/erp/sales/sales_estimate/view/">
							{%csrf_token %}
							<div class="col-sm-12 form-group" >
								<label>Date Range</label>
								<div id="reportrange" class="report-range form-control">
									<i class="glyphicon glyphicon-calendar"></i>&nbsp;
									<span></span> <b class="caret"></b>
									{{ search.from_date }}
									{{search.to_date }}
								</div>
							</div>
							<div class="col-sm-12 form-group">
								<label>Party Name</label>
								{{ search.party_name }}
							</div>
							<div class="col-sm-12 form-group">
								<label>Project/Tag</label>
								{{ search.project }}
							</div>
							<div class="col-sm-12 form-group">
								<label>Status</label>
								{{search.status}}
							</div>
							<div class="filter-footer">
								<input type="submit" class="btn btn-save" value="Apply" id="id_search_submit"/>
	      					</div>
						</form>
					</span>
				</div>
				<span class='filtered-condition filtered-date'>Date: <b></b></span>
				<span class='filtered-condition filtered-party'>Party Name: <b></b></span>
				<span class='filtered-condition filtered-project'>Project/Tag: <b></b></span>
				<span class='filtered-condition filtered-status'>Status: <b></b></span>
			</div>
		</div>
		<div id="view_icd">
			<div class="clearfix"></div>
			<div class="col-lg-12" id="search_result">
				<div class="csv_export_button">
	                <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#se_list'), 'se_List.csv']);" data-tooltip="tooltip" title="Download SE List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
	            </div>
				<table class="table table-bordered custom-table table-striped" id="se_list" style="width: 100%;">
					<thead>
					<tr>
						<th style="width: 30px;"> S.No. </th>
						<th style="width: 100px; min-width: 80px;"> SE No</th>
						<th style="width: 60px;"> Type</th>
						<th style="width: 120px;"> SE Date</th>
						<th style="width: 120px;"> SE Expiry Date</th>
						<th style="width: 300px;"> Customer</th>
						<th style="width: 80px;"> SE Value</th>
						<th style="width: 80px;">Invoice Value</th>
						<th style="width: 60px">OA Value</th>
						<th style="width: 80px;"> Status</th>
					</tr>
					</thead>
					<tbody>
					{% for in_list in se_list %}
					<tr align="center" data-seid='{{ in_list.id }}'>
						<td>{{forloop.counter}}.</td>
                        <td>
							<a role="button" class="edit_link_code" data-salesEstimateId="{{ in_list.id }}" onclick="editSeRow('{{ in_list.id }}');">
								{{in_list.code}}
							</a>
						</td>
						<td align="center"> {{in_list.type}}</td>
                        <td class="td-se-date"> {{in_list.approved_on|date:'M d, Y H:i:s'}}</td>
						<td class="td-se-date"> {{in_list.expiry_date|date:'M d, Y H:i:s'}}</td>
                        <td align="left"> {{in_list.customer }}</td>
                        <td align="right"> {{in_list.grand_total|floatformat:2 }}</td>
						<td align="right">{{ in_list.inv_value|floatformat:2 }}</td>
						<td align="right">{{ in_list.oa_value|floatformat:2 }}</td>
                        <td align="center" class='td_status'  style="padding: 0;" data-sort='{{in_list.status_name}}'>
							<a role="button" class='table-inline-icon-bg pdf_genereate {{in_list.status_name}}'>
								{{in_list.status_name|capfirst}}
							</a>
							<span class='table-inline-icon-container'>
								<span class='table-inline-icon pdf_genereate_icon hide' data-tooltip='tooltip' data-placement='left' title='Preview' onclick="javascript:{% if access_level.view %}generate_pdf_ajax({{in_list.id}},{{in_list.status}},'{{in_list.expiry_date|date:'M d, Y'}}');{% else %}alert('You don\'t have sufficient permission to Process this SE');{% endif %}">
									<i class="fa fa-file-text"></i>
								</span>
								<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit' role='button' onclick="editSeRow('{{ in_list.id }}');">
									<i class='fa fa-pencil'></i>
								</span>
								<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit in New Tab' role='button' onclick="editSeRow('{{ in_list.id }}', '_blank');">
									<i class='fa fa-external-link'></i>
								</span>
							</span>
						</td>
					</tr>
					{% endfor %}
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>

<div id="se_document_modal" class="modal fade" role="dialog">
  	<div class="modal-dialog modal-lg">
		<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Preview Document</h4>
      		</div>
      		<div class="modal-body text-center">
      			<input id="modal_se_id" name="se_id" value='' hidden="hidden"/>
      			<input id="expiry_date" name="expiry_date" value='' hidden="hidden"/>
				<div class="row" style="text-align: left;">
					<div class="col-lg-12">
						<div class="content_bg">
							<div class="add_table" id="se_doc_btn">
								<form id="se_approval_form" method="POST" action="/erp/sales/se/generateSEDoc/">
									{% csrf_token %}
									<div class="col-sm-6 add_table_content" style="padding-left: 0;">
										<input id="se_id" name="se_no" value='' hidden="hidden"/>
										<input type="submit" value="Edit" id="pdfGeneration_{{ se_id }}" hidden="hidden"/>
										<input id="remarks" class="form-control" enterkey="do_nothing" maxlength="248" placeholder="Approval/Rejection Remarks"/>
										<div class="remarks_count_link remarks_count disabled" onclick="remarksInSwal('se_document_remarks');">
											<span class="remarks_counter">No</span><span> remarks</span>
										</div>
									</div>
									{% if access_level.approve %}
										<a role='button' id='approve_se' class="btn btn-save" onclick="validateExpiryDate('approve')">Approve</a>
                                        <a role='button' id='regenerate' class="btn btn-save" onclick="validateExpiryDate('regenerate')">Regenerate</a>
										<a role='button' id='client_approve_se' class="btn btn-save" onclick="validateExpiryDate('client_approve')">Client Approve</a>
										<a role='button' id='client_reject_se' class="btn btn-danger for_se_approve" onclick="validateExpiryDate('client_reject')">Client Reject</a>
										<a role='button' id='review_se' class="btn btn-warning" onclick="validateExpiryDate('review')">Review</a>
								    {% endif %}
								    {% if access_level.approve %}
										<a role='button' id='reject_se' class="btn btn-danger for_se_approve" onclick="validateExpiryDate('reject')">Reject</a>
									{% else %}
									{% if access_level.edit %}
										<a role='button' id='reject_se' class="btn btn-danger for_se_edit" onclick="validateExpiryDate('reject')">Discard</a>
									{% endif %}
									{% endif %}
									<div style="display: inline-block; margin-right: 2px; float: right;" id="send_mail_div"  class="hide">
										<a role="button" id='display_popup' onclick="openMailPopup()" class="btn transparent-btn" data-tooltip="tooltip" data-placement="left" title="Email SE"><i class="fa fa-envelope" style="font-size: 14px;" aria-hidden="true"></i></a>
									</div>
									<div style="display: inline-block; float: right;">
										<a href="/erp/sales/sales_estimate/view/" class="btn transparent-btn" data-tooltip="tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
									</div>
								</form>
							</div>
						</div>
					</div>
				</div>
				<input type="hidden" id="se_document_remarks" />
				<div id="se_document_container"></div>
      		</div>
			<div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
      	</div>
  	</div>
</div>
<div class="hide">
	<form id="se_edit_form" method="post" action="/erp/sales/sales_estimate/editSalesEstimate/">
		{%csrf_token%}
		<input type="hidden" id="edit_se_id" name="se_no" value="" />
	</form>
</div>
<script type="text/javascript">
	function editSeRow(seId, openTarget="") {
		$("#edit_se_id").val(seId);
		$("#se_edit_form").attr("target", openTarget).submit();
	}
	$(document).ready(function() {
		$("#template_configuration").removeClass('hide');
		$("#template_configuration").click(function(){
			window.open('/erp/admin/sales_estimate_template','_blank');
		});
	});
</script>
{% endblock %}
