<div class="delivery_schedule_contianer hide">
   <h6 style="margin: 0; margin-bottom: 5px; text-align: center"><span class="delivery_schedule_title">DELIVERY SCHEDULES</span></h6>
   <atable class="table table-bordered  hsn_table delivery_schedule row-seperator column-seperator" id="delivery_table" style="font-size:11px">
        <athead>
            <atr class="row_seperator column_seperator header_shading">
                <ath class="text-center td_s_no td_s_no_text" >S.No</ath>
                <ath class="text-center td_delivery_description_text td_delivery_description" >Material</ath>
                <ath class="text-center td_delivery_due_text td_delivery_due" >Delivery Due Date</ath>
                <ath class="text-center td_delivery_qty_text td_delivery_qty">Delivery Qty</ath>
            </atr>
        </athead>
		<atbody>
			[% for schedule in po_delivery_materials %]
				[%  if forloop.counter|divisibleby:2 %]
				<atr class="row_seperator column_seperator" style="background: #efefef;">
				[% else %]
				<atr class="row_seperator column_seperator" style="background: #ffffff;">
				[% endif %]
					<atd class="text-center td_s_no" style="text-align:center">{[forloop.counter]}.</atd>
					<atd class="td_delivery_description">{[ schedule.material_name ]}</atd>
					<atd class="text-center">{[ schedule.due_date ]}</atd>
					<atd style="text-align:right" class="text-center td_delivery_qt">{[ schedule.qty ]}</atd>
				</atr>
			[% endfor %]
		</atbody>
   </atable>
</div>