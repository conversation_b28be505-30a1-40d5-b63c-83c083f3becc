"""
"""
import json

import simplejson
from django.http import HttpResponse

from erp import helper
from erp.auth import USER_IN_SESSION_KEY, SESSION_KEY, ENTERPRISE_ID_SESSION_KEY, PRIMARY_ENTERPRISE_ID_SESSION_KEY, \
ENTERPRISE_IN_SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.dao import executeQuery
from erp.helper import getPartyFrequents
from erp.sales.backend import InvoiceService
from erp.sales.views import calcMultiBOMMat, calcJDCBOMMat
from erp.stores import logger
from erp.stores.backend import StoresService, StoresDAO
from util.api_util import response_code, JsonUtil

FIELD_SEPARATOR = "%5B%3A%5D"  # "[:]"

__author__ = 'nandha'


def dashboard(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		since, till = JsonUtil.getDateRange(rh=rh)

		logger.info('Fetching store statistics (dashboard) between %s - %s ...' % (since, till))
		response = response_code.success()
		response.update(StoresDAO().getDashboardData(enterprise_id=enterprise_id, since=since, till=till))
	except Exception as e:
		logger.exception('Failed getting dashboard data. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getStockStatement(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		since = rh.getPostData('since').encode("utf-8") + " 00:00:00" if rh.getPostData('since') != "" else None
		till = rh.getPostData('till').encode("utf-8") + " 23:59:59" if rh.getPostData('till') != "" else None

		logger.info('Fetching store statistics (dashboard) between %s - %s ...' % (since, till))
		response = response_code.success()
		response['data'] = StoresDAO().getStockData(since, till, enterprise_id)

	except Exception as e:
		logger.exception('Failed getting dashboard data. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')

def getListStockStatement(request):
	"""

	:param request:
	:return:
	"""
	stock_statement_details = {}
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		since, till = JsonUtil.getDateRange(rh=rh)
		logger.info('Fetching store statistics (dashboard) between %s - %s ...' % (since, till))
		response = response_code.success()
		stock_statement = StoresDAO().getStockStatement(enterprise_id=enterprise_id, since=since, till=till)
		stock_statement_details['stock_statement'] = stock_statement
		response.update(stock_statement_details)
	except Exception as e:
		logger.exception('Failed getting dashboard data. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getIndentStatement(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		response = response_code.success()
		response['data'] = StoresDAO().getIndentData(enterprise_id)

	except Exception as e:
		logger.exception('Failed getting dashboard data. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getDCReport(request):
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		supplier = rh.getPostData('supplier').encode("utf-8") if rh.getPostData('supplier') != "" else None
		item_id = rh.getAndCacheData(key="item_id", session_key="dcr.item_id")
		make_id = rh.getAndCacheData(key="make_id", session_key="dcr.make_id")
		approved_only = rh.getAndCacheData(key="approved_only", session_key="dcr.approved_only") == "true"
		is_returnable = rh.getAndCacheData(key="returnable", session_key="dcr.returnable") == "true"
		is_non_returnable = rh.getAndCacheData(key="non_returnable", session_key="dcr.non_returnable") == "true"
		if make_id == "":
			make_id = None
		is_faulty = rh.getAndCacheData(key="is_faulty", session_key="dcr.is_faulty")
		if is_faulty == "":
			is_faulty = None
			supplier = rh.getAndCacheData(key="supplier", session_key="dcr.supplier")
		since, till = JsonUtil.getDateRange(rh=rh, since_session_key="dcr.since", till_session_key="dcr.till")
		response = response_code.success()
		response["value"] = StoresDAO().getDCReports(
			enterprise_id=enterprise_id, since=since, till=till, supplier=supplier, item_id=item_id, make_id=make_id,
			is_faulty=is_faulty, status=int(approved_only), is_returnable=is_returnable, is_non_returnable=is_non_returnable)
	except Exception as e:
		logger.exception('Failed getting dashboard data. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message

	return HttpResponse(simplejson.dumps(response), 'content-type=text/json')


def getJobPO(request):
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		supplier = rh.getPostData('party_id').encode("utf-8") if rh.getPostData('party_id').encode(
			"utf-8") != "None" else None
		invoice_id = rh.getPostData('invoice_id').encode("utf-8") if rh.getPostData('invoice_id').encode(
			"utf-8") != "None" else None
		issued_on = rh.getPostData('issued_on').encode("utf-8") if rh.getPostData('issued_on').encode(
			"utf-8") != "None" else None
		response = response_code.success()
		response["value"] = StoresDAO().getJobPOByParty(
			enterprise_id=enterprise_id, supplier=supplier, invoice_id=invoice_id, issued_on=issued_on)
	except Exception as e:
		logger.exception('Failed getting dashboard data. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message

	return HttpResponse(json.dumps(response["value"]), 'content-type=text/json')


def getPartyOA(request):
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		party_id = rh.getPostData('party_id')
		if party_id == "":
			party_id = None
		invoice_id = rh.getPostData('invoice_id')
		if invoice_id == "":
			invoice_id = None
		response = response_code.success()
		response["oa_list"] = StoresDAO().getOAByParty(
			enterprise_id=enterprise_id, party_id=party_id, invoice_id=invoice_id)
	except Exception as e:
		logger.exception('Failed getting dashboard data. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message

	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getPartyOAAgainstGRN(request):
	try:

		rh = RequestHandler(request)
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		party_id = rh.getPostData('party_id')
		if party_id == "":
			party_id = None
		invoice_id = rh.getPostData('invoice_id')
		if invoice_id == "":
			invoice_id = None
		response = response_code.success()
		response["oa_list"] = StoresDAO().getPendingOAByParty(
			enterprise_id=enterprise_id, party_id=party_id, invoice_id=invoice_id)
	except Exception as e:
		logger.exception('Failed to get OA numbers against GRN. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message

	return HttpResponse(json.dumps(response), 'content-type=text/json')


def materialStock(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		item_id = rh.getPostData('item_id')
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		logger.info("Loading Purchase Order material stock for material %s" % [enterprise_id, item_id])
		response = response_code.success()
		response['material_stock'] = StoresDAO().getMaterialStock(enterprise_id=enterprise_id, item_id=item_id)
		logger.info("Loaded Purchase Order material stock")
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getInvoiceOA(request):
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		invoice_id = rh.getPostData('invoice_id').encode("utf-8") if rh.getPostData('invoice_id').encode(
			"utf-8") != "None" else None
		response = response_code.success()
		response["oa_codes"] = StoresService().getOACodeByInvoice(enterprise_id=enterprise_id, invoice_id=invoice_id)
	except Exception as e:
		logger.exception('Failed getting dashboard data. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message

	return HttpResponse(json.dumps(response["oa_codes"]), 'content-type=text/json')


def getInvoiceGRN(request):
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		invoice_id = rh.getPostData('invoice_id').encode("utf-8") if rh.getPostData('invoice_id').encode(
			"utf-8") != "None" else None
		response = response_code.success()
		response["oa_codes"] = StoresService().getGRNCodeByInvoice(enterprise_id=enterprise_id, invoice_id=invoice_id)
	except Exception as e:
		logger.exception('Failed getting GRN Codes. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message

	return HttpResponse(json.dumps(response["oa_codes"]), 'content-type=text/json')


def getInvoicePO(request):
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		invoice_id = rh.getPostData('invoice_id').encode("utf-8") if rh.getPostData('invoice_id').encode(
			"utf-8") != "None" else None
		response = response_code.success()
		response["po_codes"] = StoresDAO().getPOCodeByInvoice(enterprise_id=enterprise_id, invoice_id=invoice_id)
	except Exception as e:
		logger.exception('Failed getting dashboard data. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message

	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getSupplierList(request):
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		supplier_type = rh.getPostData('type').encode("utf-8")
		supplier_type = supplier_type if supplier_type != "None" else None
		response = response_code.success()
		party_frequents = getPartyFrequents(
			table='invoice', enterprise_id=enterprise_id, condition="type='%s'" % supplier_type)
		all_party_choices = helper.populatePartyChoices(
			enterprise_id=enterprise_id, is_customer=supplier_type not in ("JDC", "DC"),
			is_supplier=supplier_type == "JDC")
		del all_party_choices[0]
		response["value"] = [party_frequents, all_party_choices, [len(party_frequents)]]
	except Exception as e:
		logger.exception('Failed getting dashboard data. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response["value"]), 'content-type=text/json')


def indentStatus(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')

		since, till = JsonUtil.getDateRange(rh=rh)
		logger.info('Fetching Indent statistics between %s - %s ...' % (since, till))
		response = response_code.success()
		response.update(StoresService().getEnterpriseIndentStatus(enterprise_id=enterprise_id))
	except Exception as e:
		logger.exception('Failed getting indent status. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def fetchDraftGrn(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		response = response_code.success()
		response['grn_list'] = StoresService().fetchDraftGrn(enterprise_id=enterprise_id)
		logger.info("Fetching %s Grn." % len(response['grn_list']))
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def grnStatus(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		since, till = JsonUtil.getDateRange(rh=rh)
		logger.info('Fetching GRN statistics between %s - %s...' % (since, till))
		response = response_code.success()
		response.update(StoresDAO().getGRNOverView(enterprise_id=enterprise_id, since=since, till=till))
	except Exception as e:
		logger.exception('Failed getting grn status. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def stockStatement(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')

		since, till = JsonUtil.getDateRange(rh=rh)
		logger.info('Fetching stock statement between %s - %s ...' % (since, till))
		response = response_code.success()
		response.update(StoresDAO().getStockStatement(enterprise_id=enterprise_id, since=since, till=till))
	except Exception as e:
		logger.exception('Failed getting stock statement. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def grnReportTbl(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		supplier = json.loads(rh.getPostData('supplier')) if rh.getPostData('supplier') else None
		material = rh.getPostData('material').encode("utf-8") if rh.getPostData('material') != "" else None
		approved_only = 0 if rh.getPostData('approved_only').encode("utf-8") == 'true' else -1
		since, till = JsonUtil.getDateRange(rh=rh)
		logger.info('Fetching GRN Report for Date range between %s - %s ...' % (since, till))
		response = response_code.success()
		response['value'] = StoresDAO().getGRNReport(
			enterprise_id=enterprise_id, since=since, till=till, supplier=supplier, material=material,
			approved_only=approved_only)
	except Exception as e:
		logger.exception('Failed getting stock statement. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response['value']), 'content-type=text/json')


def salesReportTbl(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		supplier = rh.getPostData('supplier').encode("utf-8") if rh.getPostData('supplier') != "" and rh.getPostData(
			'supplier') != "-1" else None
		material = rh.getPostData('material').encode("utf-8") if rh.getPostData('material') != "" else None
		if material:
			splits = material.split(",")
			item_id = splits[0]
			make_id = 1
		else:
			item_id = None
			make_id = 1
		approved_only = 0 if rh.getPostData('approved_only').encode("utf-8") == 'true' else -1
		since, till = JsonUtil.getDateRange(rh=rh)
		logger.info('Fetching Sales Invoice between %s - %s ...' % (since, till))
		response = response_code.success()
		response['value'] = StoresDAO().getSALESReport(
			enterprise_id=enterprise_id, since=since, till=till, supplier=supplier, item_id=item_id,
			make_id=make_id, approved_only=approved_only)
	except Exception as e:
		logger.exception('Failed Sales Invoice Report. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response['value']), 'content-type=text/json')


def stockCheck(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	enterprise_id = rh.getPostData('enterprise_id')
	item_id = rh.getPostData('item_id')
	make_id = rh.getPostData('make_id')
	is_faulty = rh.getPostData('is_faulty')
	exclude_drafts = rh.getPostData('exclude_drafts') and int(rh.getPostData('exclude_drafts')) == 1

	try:
		if not item_id:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')
		if make_id is None:
			make_id = 1
		since, till = JsonUtil.getDateRange(rh=rh)
		response = response_code.success()
		response.update(
			StoresDAO().getStockDetail(
				enterprise_id=enterprise_id, item_id=item_id, make_id=make_id,
				is_faulty=is_faulty, exclude_drafts=exclude_drafts, since=since, till=till))
	except Exception as e:
		logger.exception('Could check stock for material - %s. %s' % (item_id, e.message))
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def listAllIssues(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')

		since, till = JsonUtil.getDateRange(rh=rh)
		response = response_code.success()
		response['issues'] = StoresDAO().listAllIssues(enterprise_id=enterprise_id, since=since, till=till)
	except Exception as e:
		logger.exception('Failed getting issues list. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def listAllIndents(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')

		since, till = JsonUtil.getDateRange(rh=rh)
		response = response_code.success()
		response['issues'] = StoresDAO().listAllIndents(enterprise_id=enterprise_id, since=since, till=till)
	except Exception as e:
		logger.exception('Failed listing indents. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def lastUsedSupplierDetailsInvoice(request):
	"""
	
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getPostData('enterprise_id')
	if not enterprise_id:
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	party_id = request_handler.getPostData("party_id")
	party_details_dict = ""
	try:
		if party_id:
			query = """SELECT DISTINCT transport_mode, remarks, delivery_address FROM invoice
						WHERE party_id = '%s' AND enterprise_id='%s' ORDER BY id DESC""" % (party_id, enterprise_id)
			payment_terms_query = """SELECT pm.receipt_credit_days, pr.details, pm.address1, pm.currency, IFNULL(pm.pin_code, "")
								FROM party_master pm join party_registration_detail pr ON pm.party_id = pr.party_id 
								WHERE pm.party_id='%s' AND pm.enterprise_id='%s' and pr.label = 'GSTIN'""" % (party_id, enterprise_id)
			invoices = executeQuery(query)
			payment_terms = executeQuery(payment_terms_query)
			if len(invoices) > 0:
				invoice = invoices[0]
				payment_term = payment_terms[0]
				party_details_dict = {
					'transport_mode': invoice[0] if invoice[0] else '',
					'packing_description': invoice[1].replace('<BR/>', '') if invoice[1] else '',
					'deliver_to': invoice[2] if invoice[2] else '%s - %s' % (payment_term[2],  payment_term[4]),
					'gstin': payment_term[1] if payment_term[1] else '',
					'payment_terms': payment_term[0] if payment_term[0] else '',
					'currency_id': payment_term[3] if payment_term[3] else ''}
			elif len(payment_terms) > 0:
				payment_term = payment_terms[0]
				deliver_to = '%s - %s' % (payment_term[2],  payment_term[4]) if payment_term[4] else payment_term[2]
				party_details_dict = {
					'transport_mode': '',
					'packing_description': '',
					'deliver_to': deliver_to,
					'gstin': payment_term[1] if payment_term[1] else '',
					'payment_terms': payment_term[0] if payment_term[0] else '',
					'currency_id': payment_term[3] if payment_term[3] else ''}
			logger.info("GLO: %s" % party_details_dict)
			response = response_code.success()
			response['party_details'] = party_details_dict
		else:
			raise Exception("Party not selected")
	except Exception as e:
		response = response_code.internalError()
		response['error'] = '%s' % e.message
		logger.exception("Frequently dealt suppliers list cannot be fetched...\n%s" % e)
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def smartPopulateSalesTax(request):
	"""
	Fetches the recently profiled Sales taxes Code of an item for a particular Party.
	:param request:
	:return:
	"""
	recent_taxes = []
	try:
		request_handler = RequestHandler(request)
		item_id = request_handler.getPostData("item_id")
		make_id = request_handler.getPostData("make_id")
		is_faulty = request_handler.getPostData("is_faulty")
		party_id = request_handler.getPostData("party_id")
		material_type = request_handler.getPostData("material_type")
		if item_id and make_id and is_faulty and party_id and material_type:
			recent_taxes = getRecentSalesTax(
				item_id=item_id, make_id=make_id, is_faulty=is_faulty, party_id=party_id,
				table_name='invoice_item_tax')
	except Exception as e:
		logger.exception("Fetching recent sales taxes Failed - %s" % e)
	return HttpResponse(content=simplejson.dumps(recent_taxes), mimetype='application/json')


def superEditIndentCode(request):
	"""
	Super user can edit indent code through this api
	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		indent_no = request_handler.getPostData("indent_no")

		new_indent_id = int(request_handler.getPostData("new_indent_id"))
		new_financial_year = request_handler.getPostData("new_financial_year")
		new_sub_number = request_handler.getPostData("new_sub_number")
		if new_sub_number is not None and new_sub_number.strip() == "":
			new_sub_number = None
		response = StoresService().superEditIndentCode(
			enterprise_id=enterprise_id, user_id=user_id, indent_no=indent_no, new_financial_year=new_financial_year,
			new_indent_id=new_indent_id, new_sub_number=new_sub_number)
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def superEditReceiptCode(request):
	"""
	Super user can edit ian code through this api
	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		receipt_no = request_handler.getPostData("receipt_no")

		new_financial_year = request_handler.getPostData("new_financial_year")
		new_receipt_code = request_handler.getPostData("new_receipt_code")
		new_sub_number = request_handler.getPostData("new_sub_number")
		response = StoresService().superEditReceiptCode(
			enterprise_id=enterprise_id, user_id=user_id, receipt_no=receipt_no, new_financial_year=new_financial_year,
			new_receipt_code=new_receipt_code, new_sub_number=new_sub_number)

	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getDCLinkedMessage(request):
	"""

	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		invoice_id = request_handler.getPostData("invoice_id")
		invoice_tye = request_handler.getPostData("invoice_type")
		receipt_code = StoresService().getReceiptCodes(enterprise_id=enterprise_id, invoice_id=invoice_id)
		response = response_code.success()
		if receipt_code:
			if receipt_code.__len__() > 1:
				response['custom_message'] = """GRN - <b><i>%s</i></b> are linked with this %s. 
					Unfortunately system will not handle the perpetuation of these changes to the linked documents, 
					hence need to be addressed manually.<br/> Do you want to continue?""" % (
					", ".join(receipt_code), invoice_tye)
			else:
				response['custom_message'] = """GRN - <b><i>%s</i></b> is linked with this %s. 
					Unfortunately system will not handle the perpetuation of these changes to the linked documents, 
					hence need to be addressed manually.<br/> Do you want to continue?""" % (
					", ".join(receipt_code), invoice_tye)
			logger.warn("Trying to edit expense %s that is linked to %s" % (invoice_id, receipt_code))
		else:
			response['custom_message'] = ""
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
		response['custom_message'] = ""
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getReceiptLinkedMessage(request):
	"""

	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		receipt_no = request_handler.getPostData("receipt_no")
		ledger_bill = ""
		store_service = StoresService()
		dc_against_received = ""
		if receipt_no:
			ledger_bill = store_service.getLedgerBill(enterprise_id=enterprise_id, receipt_no=receipt_no)
			dc_against_received = store_service.getGRNAgainstDC(enterprise_id=enterprise_id, receipt_no=receipt_no)
		response = response_code.success()
		response['custom_message'] = ""
		if ledger_bill:
			voucher_codes = []
			for settlement in ledger_bill.settlements:
				if settlement.voucher:
					if settlement.voucher.type_id is 4 or 6:
						voucher_codes.append(settlement.voucher.getCode())
						response['custom_message'] = """Voucher - <b><i>%s</i></b> and Bill <b><i>%s</i></b> are linked with this GRN. 
							Unfortunately system will not handle the perpetuation of these changes to the linked documents, 
							hence need to be addressed manually.<br/> Do you want to continue?""" % (
							",".join(voucher_codes), ledger_bill.bill_no)
			logger.warn("Trying to edit invoice %s that is linked to %s" % (ledger_bill.bill_no, voucher_codes))
		grn_codes = ""
		if dc_against_received:
			if dc_against_received[0]:
				grn_codes = [dc_against_received[0].getCode()]
			if grn_codes:
				response['custom_message'] = """GRN - <b><i>%s</i></b>  is linked with this GRN. 
					Unfortunately system will not handle the perpetuation of these changes to the linked documents, 
					hence need to be addressed manually.<br/> Do you want to continue?""" % (",".join(grn_codes))
		else:
			response['custom_message'] = ""
		# Job In Super edit case here
		dc_list = store_service.getDcAgainstJobIn(receipt_no=receipt_no)
		if len(dc_list) > 0:
			for_reject = request_handler.getPostData("for_reject") == "true"
			grammar = "is" if len(dc_list) == 1 else "are"
			if for_reject:
				response["custom_message"] = """ GRN cannot be rejected, as {codes} {grammar} associated with it!
					Kindly Reject/Unlink them to reject this GRN! """.format(
					grammar=grammar, codes=", ".join(dc_list))
			else:
				response["custom_message"] = """DC/Invoice - <b><i>{codes}</i></b> {grammar} linked with this GRN. 
					Unfortunately system will not handle the perpetuation of these changes to the linked documents, 
					hence need to be addressed manually.<br/> Do you want to continue?""".format(
					grammar=grammar, codes=", ".join(dc_list))
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getDCReceiptLinkedMessage(request):
	"""

	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		receipt_no = request_handler.getPostData("receipt_no")
		party_id = request_handler.getPostData("party_id")
		received_against = request_handler.getPostData("received_against")
		invoice_code = StoresService().getDCLinkedInvoiceCode(
			grn_no=receipt_no, enterprise_id=enterprise_id, party_id=party_id, received_against=received_against)
		response = response_code.success()
		invoice_codes = ""
		if invoice_code:
			for code in invoice_code:
				invoice_codes += code[0] + ", "
			invoice_codes = invoice_codes[:-2]
			response['custom_message'] = """Invoice GRN- <b><i>{invoice_codes}</i></b> has been raised for this DC GRN""".format(
				invoice_codes=invoice_codes)
		else:
			response['custom_message'] = ""
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getRecentSalesTax(**kwargs):
	if kwargs['table_name'] == "invoice_item_tax":
		column_name = 'item_id'
		make = (" and it.make_id=%s") % (kwargs['make_id'])
		is_faulty = (" and it.is_faulty=%s") % (kwargs['is_faulty'])
	else:
		column_name = 'item_name'
		make = ''
		is_faulty = ''
	party = (" AND i.party_id = %s") % (kwargs['party_id'] )
	query = """
				SELECT t.code, t.type
				FROM   invoice i 
				JOIN   {table_name} it 
				ON     i.id = it.invoice_id 
				AND    it.enterprise_id = i.enterprise_id 
				JOIN   tax AS t 
				ON     it.tax_code = t.code 
				AND    i.enterprise_id = t.enterprise_id 
				WHERE  it.{column_name} = '{item_id}' 
				AND    t.type IN ( 'SGST', 
				                  'IGST', 
				                  'CGST' ) {make} {is_faulty} 
				AND    i.id = 
				       ( 
				                SELECT   i.id 
				                FROM     invoice AS i 
				                JOIN     {table_name} it 
				                ON       i.id = it.invoice_id 
				                AND      i.enterprise_id = it.enterprise_id 
				                AND      i.status > -1 
				                WHERE    it.{column_name} = '{item_id}' {make} {is_faulty} {party} 
				                ORDER BY i.approved_on DESC limit 1) {party}	
		"""
	tax_details = executeQuery(query.format(table_name=kwargs['table_name'], column_name=column_name,
	               item_id=kwargs['item_id'], type='', party_id=kwargs['party_id'],
	               make_id=kwargs['make_id'], make=make, is_faulty=is_faulty , party = party), as_dict=True)
	if not tax_details:
		tax_details = executeQuery(query.format(table_name=kwargs['table_name'], column_name=column_name,
	               item_id=kwargs['item_id'], type='', party_id=kwargs['party_id'],
	               make_id=kwargs['make_id'], make=make, is_faulty=is_faulty , party = ''), as_dict=True)
	tax_dict = {'CGST': '', 'SGST': '', 'IGST': ''}
	for item in tax_details:
		tax_dict[item['type']] = item['code']
	return tax_dict


def getIssueStockReport(request):
	"""
	Loads stock issue report filters
	[since, till, issued_to, material(drawing_no, make_id, is_faulty), ignore_transaction_period]
	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	try:
		enterprise_id = rh.getPostData("enterprise_id")
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		since, till = JsonUtil.getDateRange(rh=rh, since_session_key="isr.since", till_session_key="isr.till")
		item_id = rh.getAndCacheData(key="item_id", session_key="isr.item_id")
		make_id = rh.getAndCacheData(key="make_id", session_key="isr.make_id")

		if make_id == "":
			make_id = None
		is_faulty = rh.getAndCacheData(key="is_faulty", session_key="isr.is_faulty")
		if is_faulty == "":
			is_faulty = None
		issued_to = rh.getAndCacheData(key="issued_to", session_key="isr.issued_to")
		ignore_items_not_transacted = rh.getAndCacheData(
			key="ignore_items_not_transacted", session_key="isr.ignore_items_not_transacted") == "true"
		response = response_code.success()
		response['report'] = StoresService().getIssueStockReport(
			enterprise_id=enterprise_id, since=since, till=till, item_id=item_id, make_id=make_id,
			is_faulty=is_faulty, issued_to=issued_to, ignore_items_not_transacted=ignore_items_not_transacted)
	except Exception as e:
		logger.exception("Could not load page Stock Issue Report... %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(simplejson.dumps(response), 'content-type=text/json')


def getJobInRegisterReport(request):
	"""
	Loads stock issue report filters
	[since, till, party_id, material(drawing_no, make_id, is_faulty)]
	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	try:
		enterprise_id = rh.getPostData("enterprise_id")
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		since, till = JsonUtil.getDateRange(rh=rh, since_session_key="jnr.since", till_session_key="jnr.till")
		item_id = rh.getAndCacheData(key="item_id", session_key="jnr.item_id")
		make_id = rh.getAndCacheData(key="make_id", session_key="jnr.make_id")
		if make_id == "":
			make_id = None
		is_faulty = rh.getAndCacheData(key="is_faulty", session_key="jnr.is_faulty")
		if is_faulty == "":
			is_faulty = None
		party_id = rh.getAndCacheData(key="party_id", session_key="jnr.party_id")
		response = response_code.success()
		response['report'] = StoresService().getJobInRegisterReport(
			enterprise_id=enterprise_id, since=since, till=till, item_id=item_id, make_id=make_id,
			is_faulty=is_faulty, party_id=party_id)
	except Exception as e:
		logger.exception("Could not load page Job In Register Report... %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(simplejson.dumps(response), 'content-type=text/json')


def getJobOutRegisterReport(request):
	"""
	Loads stock issue report filters
	[since, till, party_id, material(drawing_no, make_id, is_faulty)]
	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	try:
		enterprise_id = rh.getPostData("enterprise_id")
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		since, till = JsonUtil.getDateRange(rh=rh, since_session_key="jor.since", till_session_key="jor.till")
		item_id = rh.getAndCacheData(key="item_id", session_key="jor.item_id")
		make_id = rh.getAndCacheData(key="make_id", session_key="jor.make_id")

		if make_id == "":
			make_id = None
		is_faulty = rh.getAndCacheData(key="is_faulty", session_key="jor.is_faulty")
		if is_faulty == "":
			is_faulty = None
		party_id = rh.getAndCacheData(key="party_id", session_key="jor.party_id")
		response = response_code.success()
		response['report'] = StoresService().getJobOutRegisterReport(
			enterprise_id=enterprise_id, since=since, till=till, item_id=item_id, make_id=make_id,
			is_faulty=is_faulty, party_id=party_id)
	except Exception as e:
		logger.exception("Could not load page Job Out Register Report... %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(simplejson.dumps(response), 'content-type=text/json')


def loadPartyOaFinancialYears(request):
	"""

	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		party_id = request_handler.getPostData('party_id')
		oa_type = request_handler.getPostData('oa_type')
		receipt_no = request_handler.getPostData('receipt_no')
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		store_service = StoresService()
		response = response_code.success()
		response['financial_years'] = store_service.loadPartyOaFinancialYears(
			enterprise_id=enterprise_id, party_id=party_id, oa_type=oa_type, receipt_no=receipt_no)
	except Exception as e:
		response = response_code.internalError()
		response['custom_message'] = e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def loadPendingJobOaNumbers(request):
	"""

	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		party_id = request_handler.getPostData('party_id')
		financial_years = request_handler.getPostData('financial_years')
		if financial_years and financial_years.strip() != "":
			financial_years = financial_years.split(",")
		else:
			financial_years = None
		receipt_no = request_handler.getPostData('receipt_no')
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		store_service = StoresService()
		response = response_code.success()
		response['oa_numbers'] = store_service.loadPendingJobOaNumbers(
			enterprise_id=enterprise_id, party_id=party_id, receipt_no=receipt_no,
			financial_years=financial_years)
	except Exception as e:
		response = response_code.internalError()
		response['custom_message'] = e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def materialUsageReport(request):
	receipt_no = RequestHandler(request).getPostData("receipt_no")
	enterprise_id = RequestHandler(request).getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	response = []
	try:
		logger.info("Fetching job order material for {enterprise: %s, po_id:%s}" % (enterprise_id, receipt_no))
		invoice_dao = InvoiceService()
		stores_dao = StoresDAO()
		grn_materials = invoice_dao.getGRNMaterialsNonOa(enterprise_id=enterprise_id, grn_no=receipt_no)
		grn_details, multi_bom_mat = invoice_dao.getClubbedCatalogue(materials=grn_materials, enterprise_id=enterprise_id)
		if len(multi_bom_mat) > 0:
			grn_details, grn_materials = calcMultiBOMMat(multi_bom_mat, grn_details, grn_materials)
		grn_details, grn_materials = calcJDCBOMMat(grn_details, grn_materials)
		for material in grn_materials:
			material.update({'actual_qty': 0})
			# FIXME performance issue while accessing item.material in loop; Consider 250 materials in a PO;
			if material['item_id']:
				catalogues = stores_dao.getCatalogues(material['cat_code'], enterprise_id)
				catalogues_child = 1 if len(catalogues) > 0 else 0
				make_list = [(material['make_id'], material['make_name'])]
			else:
				catalogues_child = ""
				make_list = ""
			material['child'] = catalogues_child
			material['makes'] = make_list
			material['actual_qty'] = material['qty']
			material['supplied'] = material['qty'] if material['qty'] else 0
			material['qty'] = 0
			material['returned'] = 0
			grn_details.append(material)
		response = invoice_dao.getCalcActualQty(po_id=receipt_no, po_material_lst=grn_details, job_type='GRN')
	except Exception as e:
		logger.exception("Failed loading GRN Usage Report... %s" % e.message)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def goodsReturnedStatus(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request=request)
	receipt_no = rh.getPostData("receipt_no")
	try:
		store_service = StoresService()
		response = store_service.getGoodsReturnedStatus(receipt_no=receipt_no)
	except Exception as e:
		logger.error(e.message)
		response = response_code.internalError()
		response["custom_message"] = "Failed to load goods returned status. Please contact Admin!"
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def loadAlternateUnits(request):
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		item_id = request_handler.getPostData('item_id')
		response = helper.populateAlternateUnits(enterprise_id=enterprise_id, item_id=item_id)
	except Exception as e:
		logger.error("Fetching Alternate units are Failed %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def loadAllUnits(request):
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		response = helper.populateAllUnits(enterprise_id=enterprise_id)
	except Exception as e:
		logger.error("Fetching units are Failed %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def loadAllProjects(request):
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
		enterprise = request_handler.getSessionAttribute(ENTERPRISE_IN_SESSION_KEY)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		response = response_code.success()
		response['projects'] = helper.populateAllProjects(enterprise_id=enterprise_id)
		response['permission_list'] = helper.getPermissionProjectsId(enterprise_id=enterprise_id, user_id=user_id) \
			if enterprise.is_project_wise_pl == 1 else []
		response['project_wise'] = True if enterprise.is_project_wise_pl == 1 else False
	except Exception as e:
		logger.error("Fetching projects are Failed %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')
