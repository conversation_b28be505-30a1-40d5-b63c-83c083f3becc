"""
"""
import os
import unittest

from erp.helper import getConsolidatedMaterial, getAccountGroupIDs
from erp.hr.backend import HrService
from util import logger, VALID_GROUP_NAMES
from erp.dao import DataAccessObject
from util.voucher_automation_views import VoucherService
from erp.models import Voucher, VoucherParticulars, LedgerBill, LedgerBillSettlement, Invoice, Receipt

__author__ = 'nandha'


class HelperTests(unittest.TestCase):
    def __init__(self, *args, **kwargs):
        super(HelperTests, self).__init__(*args, **kwargs)
        self.hr_service = HrService()
        logger.info("Running Helper tests")

    def testGeneratePaySlipFileIsPdf(self):
        filename, file_path = self.hr_service.generatePaySlip(
            enterprise_id="102", month_year="Dec, 2019", employee_code=1026)
        self.assertFalse(filename.endswith("pdf"), "Result file is not a pdf file")

    def testGeneratePaySlipFilePathExists(self):
        filename, file_path = self.hr_service.generatePaySlip(
            enterprise_id="102", month_year="Dec, 2019", employee_code=1026)
        self.assertFalse(os.path.exists(file_path), "Failed generating Pay Slip for employee code 1026")

    def testGeneratePaySlipsIsZipFile(self):
        filename, file_path = self.hr_service.generatePaySlips(
            enterprise_id="102", month_year="Dec, 2019", employee_codes=[1026, 1027])
        self.assertFalse(filename.endswith("zip"), "Result file is not Zip file")


class AutomateVouchers(unittest.TestCase):

    def setUp(self):
        self.dao = DataAccessObject()

    def test_tc(self):
        db_session = self.dao.db_session
        db_session.begin(subtransactions=True)
        _voucher = db_session.query(Voucher).filter_by(id=311072).first()
        voucher = Voucher()
        voucher.enterprise_id = 996  # project_entreprise_id
        voucher.voucher_date = _voucher.voucher_date
        voucher.voucher_no = _voucher.voucher_no
        voucher.type = _voucher.type
        voucher.narration = _voucher.narration
        voucher.created_by = _voucher.created_by
        voucher.created_on = _voucher.created_on
        voucher.status = _voucher.status
        voucher.financial_year = _voucher.financial_year
        voucher.project_code = 4479  # project_id
        print(voucher.id, _voucher.particulars)
        i = 0
        voucher_particulars = []
        for particular in _voucher.particulars:
            i += 1
            _vp = VoucherParticulars(voucher_id=voucher.id, item_no=i, ledger_id=particular.ledger_id,
                                     is_debit=particular.is_debit, amount=particular.amount,
                                     enterprise_id=996)
            print(voucher.id)
            if particular.ledger.bills:
                for bill in particular.ledger.bills:
                    if bill.voucher_id == particular.voucher_id:
                        print(_vp.voucher_id, bill)
                        _lb = LedgerBill(bill_no=bill.bill_no, bill_date=bill.bill_date,
                                         ledger_id=particular.ledger_id,
                                         net_value=bill.net_value, is_debit=bill.is_debit,
                                         enterprise_id=996,
                                         voucher_id=voucher.id)
                        _lb.voucher = voucher
                    for settlement in bill.settlements:
                        if settlement.voucher_id == particular.voucher_id:
                            print(settlement)
                            _lbs = LedgerBillSettlement(voucher_id=voucher.id, dr_value=settlement.dr_value,
                                                        cr_value=settlement.cr_value, enterprise_id=996)
                            print(_lbs)
                            _lbs.voucher = voucher
                            _lbs.bill = _lb
            voucher_particulars.append(_vp)
        voucher.particulars = voucher_particulars
        db_session.add(voucher)
        db_session.commit()
        self.assertDictEqual(_voucher.particulars, {})

    def test_clone_vouchers_for_projects(self):
        voucher_service = VoucherService()
        vouchers_id = [311140, 311134, 311131, 311109, 311089, 311072]
        result = voucher_service.clone_sales_or_purchase_voucher(voucher_id=312145)

        # result = voucher_service.clone_vouchers(voucher_id=311134)
        # self.assertTrue(result)

    def test_clone_vouchers_for_projects_3(self):
        voucher_service = VoucherService()
        vouchers_id = [311140, 311134, 311131, 311109, 311089, 311072]
        result = voucher_service.clone_sales_or_purchase_voucher(voucher_id=311656)

        # result = voucher_service.clone_vouchers(voucher_id=311134)
        # self.assertTrue(result)

    def test_clone_vouchers_for_projects_1(self):
        voucher_service = VoucherService()
        result = voucher_service.clone_sales_or_purchase_voucher(voucher_id='')
        self.assertFalse(result)

    def test_clone_vouchers_for_projects_2(self):
        voucher_service = VoucherService()
        result = voucher_service.clone_sales_or_purchase_voucher(voucher_id=4564324454564)
        self.assertFalse(result)

    def test_getLedgerId(self):
        voucher_service = VoucherService()
        result = voucher_service.get_ledger_id(enterprise_id=1061, group_id=23, name="Schnell")
        self.assertEqual(result, 99589)

    def test_getLedgerId_1(self):
        voucher_service = VoucherService()
        result = voucher_service.get_ledger_id(enterprise_id=997, group_id=80)
        self.assertEqual(result, None)

    def test_validate_ledgers(self):
        db_session = self.dao.db_session
        voucher_service = VoucherService()
        result = voucher_service.validate_ledgers(ledger_id=285, db_session=db_session)
        self.assertTrue(result)

    def test_validate_ledgers_1(self):
        db_session = self.dao.db_session
        voucher_service = VoucherService()
        result = voucher_service.validate_ledgers(ledger_id=403, db_session=db_session)
        self.assertFalse(result)

    def test_clone_cash_bank_vouchers_for_projects(self):
        voucher_service = VoucherService()
        result = voucher_service.clone_cash_or_bank_voucher(voucher_id=311933)
        # result = voucher_service.clone_vouchers(voucher_id=311134)
        # self.assertTrue(result)

    def test_check_the_internal_workorder(self):
        db_session = self.dao.db_session
        db_session.begin(subtransactions=True)
        voucher_service = VoucherService()
        result = voucher_service.check_internal_workorder(ledger_bill_id=311849)
        print(result)

    def test_get_internal_workorder_ledger_bill(self):
        db_session = self.dao.db_session
        db_session.begin(subtransactions=True)
        voucher_service = VoucherService()
        oa_nos = voucher_service.check_internal_workorder(ledger_bill_id=199154)
        print(oa_nos)
        result = voucher_service.get_internal_workorder_ledger_bill(oa_nos=tuple(item[0] for item in oa_nos),
                                                                    db_session=db_session)
        print(result[0].net_value)

    def test_get_consolidated_tax_for_parent_invoice(self):
        db_session = self.dao.db_session
        db_session.begin(subtransactions=True)
        voucher_service = VoucherService()
        invoice = db_session.query(Invoice).filter(Invoice.id==204258).first()
        print(invoice.getTotalMaterialValue())
        total_material_amount = invoice.getTotalMaterialValue()
        result, result_2 = getConsolidatedMaterial(invoice=invoice)
        print(invoice.getTaxSummary(invoice_materials=result, source=invoice))
        # print(result)

    def test_get_consolidated_tax_for_parent_invoice_1(self):
        db_session = self.dao.db_session
        db_session.begin(subtransactions=True)
        voucher_service = VoucherService()
        total = 0
        receipt = db_session.query(Receipt).filter(Receipt.receipt_no==94673).first()
        print(receipt.taxes)
        total_material_amount = receipt.getTotalMaterialValue()
        tax_values = receipt.getTaxValues()
        for value in tax_values.values():
                total += value
        print(tax_values, total)
        print(total_material_amount)

    def test_getAccountGroupIDs(self):
        revenue_names = ["Direct Income", "Indirect Incomes"]
        result = getAccountGroupIDs(enterprise_id=102, names=revenue_names, deep=True)
        print(result)

    def test_getAccountGroupIDs2(self):
        result = getAccountGroupIDs(enterprise_id=1033, names=VALID_GROUP_NAMES, deep=True)
        print(result)

