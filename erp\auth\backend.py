"""
"""
import datetime
import re

import jwt
from pymysql import OperationalError
from dateutil.relativedelta import relativedelta
from django.contrib.auth import SESSION_KEY
from sqlalchemy.exc import InvalidRequestError
from sqlalchemy.orm import make_transient

from erp.admin import enterprise_module_settings
from erp.admin.backend import User<PERSON><PERSON>
from erp.admin.pgi_backend import SubscriptionService
from erp.auth import USER_IN_SESSION_KEY, SESSION_USERNAME_KEY, VIEW_FLAG_POSITION, EDIT_FLAG_POSITION, \
	DELETE_FLAG_POSITION, APPROVE_FLAG_POSITION, ENTERPRISE_IN_SESSION_KEY, ENTERPRISE_GST_SESSION_KEY, \
	ENTERPRISE_ID_SESSION_KEY, NOTIFY_FLAG_POSITION, HOME_CURRENCY_KEY, \
	SESSION_USERMAIL_KEY, logger, ENTERPRISE_SUBSCRIPTION, BASIC_MATERIAL_PRICE_KEY, STANDARD_MATERIAL_PRICE_KEY, \
	PRIMARY_ENTERPRISE_ID_SESSION_KEY
from erp.commons.backend import sendMail, NotificationService
from erp.masters.backend import createTaxLedgers
from erp.models import User, UserPermission, Enterprise, Notification, NotificationUserMap, FcmUserRegister, \
	InvoiceTemplateConfig, Tax, EnterpriseContactMap, ContactDetails, EnterpriseSubscription, UserEnterpriseMap
from erp.properties import PASSWORD_RESET_MAIL, PASSWORD_CHANGED_MAIL, WELCOME_MAIL_MAIL, SIGN_UP_INTIMATION_MAIL, \
	REGISTERED_INTIMATION_MAIL, USER_INVITE_MAIL
from settings import ORMSessionFactory, SQLASession, JWT_SECRET, XSASSIST_MAIL_ID, BILLING_ENTERPRISE_ID, MongoDbConnect
from util.api_util import response_code
from util.helper import readFile, copyDictToDict

__author__ = 'kalaivanan'


class UserVO(object):
	"""
	Value Object modelling a lean-version of User data - to be used in Session Handling and other UI operations
	Used to hold basic user profile info - username, permissions, etc - required, for as long an user is logged in
	"""

	def __init__(
			self, user_id=None, username=u'anonymous', email='', is_super=False, user=None, enterprise=None,
			first_name="", last_name="", is_enterprise_active=False, **permissions):
		self.username = ""
		self.permissions = {}
		self.is_enterprise_active = is_enterprise_active
		self.is_super_admin = is_super
		if user is None:
			self.user_id = user_id
			self.setUsername(username)
			self.setPermissions(permissions)
			self.is_super = is_super and is_enterprise_active
			self.enterprise = enterprise
			self.email = email
			self.first_name = first_name
			self.last_name = last_name
		else:
			self.constructVO(user, enterprise)

	def __repr__(self):
		return "%s %s %s " % (self.username, self.is_super, self.permissions)

	def constructVO(self, user, enterprise):
		"""
		Constructs the lean User info object from the User persistent object

		:param user: Persistent model (SQL Alchemy Model) of User
		:param enterprise: Enterprise model
		:return Bean type lean object, that is used to hold user info, for as long an user is logged in the application
		"""
		self.user_id = user.id
		self.setUsername(user.username)
		self.email = user.email
		self.is_super_admin = user.is_super
		self.is_super = user.is_super and self.is_enterprise_active
		self.enterprise = enterprise
		self.first_name = user.first_name
		self.last_name = user.last_name
		permission_list = {}
		for permission in user.permissions:
			permission_list.__setitem__(permission.module_code, permission.getAllAccessFlags())
		self.setPermissions(permission_list)

	#  JavaBean type getter and setter methods
	def setPermissions(self, permissions):
		self.permissions = permissions

	def setUsername(self, username):
		self.username = username

	def getPermissions(self, module_code):
		try:
			permissons = self.permissions[module_code]
		except KeyError:
			permissons = [
				UserPermission._VIEW, UserPermission._EDIT, UserPermission._APPROVE, UserPermission._DELETE,
				UserPermission._NOTIFY] if self.is_super else [self.canViewModule(module_code), 0, 0, 0, 0]
		return permissons

	def hasModuleAccess(self, module_code):
		try:
			return self.permissions[module_code][VIEW_FLAG_POSITION]
		except KeyError:
			return UserPermission._VIEW if self.is_super or self.is_super_admin else 0

	def canViewModule(self, module_code):
		return self.hasModuleAccess(module_code)

	def canEditInModule(self, module_code):
		try:
			return self.permissions[module_code][EDIT_FLAG_POSITION]
		except KeyError:
			return UserPermission._EDIT if self.is_super else 0

	def canDeleteInModule(self, module_code):
		try:
			return self.permissions[module_code][DELETE_FLAG_POSITION]
		except KeyError:
			return UserPermission._DELETE if self.is_super else 0

	def canApproveInModule(self, module_code):
		try:
			return self.permissions[module_code][APPROVE_FLAG_POSITION]
		except KeyError:
			return UserPermission._APPROVE if self.is_super else 0

	def canNotifyInModule(self, module_code):
		try:
			return self.permissions[module_code][NOTIFY_FLAG_POSITION]
		except KeyError:
			return UserPermission._NOTIFY if self.is_super else 0

	def getAccessLevels(self, module_code):
		"""

		:param module_code:
		:return:
		"""
		return AccessLevels(
			self.canViewModule(module_code), self.canEditInModule(module_code),
			self.canApproveInModule(module_code), self.canDeleteInModule(module_code),
			self.canNotifyInModule(module_code), self.is_enterprise_active)

	def getModuleAccess(self, enterprise_id, request):
		enterprise = LoginService().user_dao.db_session.query(
			Enterprise.setting_flags).filter(Enterprise.id == enterprise_id).first()
		module_access = {}
		module_access['indent'] = enterprise.setting_flags & enterprise_module_settings['indent_flag'] > 0
		if request.GET.get('is_credit_debit'):
			module_access['icd'] = True
		else:
			module_access['icd'] = enterprise.setting_flags & enterprise_module_settings['icd_flag'] > 0
		module_access['icd_ignore_credit_note'] = enterprise.setting_flags & enterprise_module_settings[
			'icd_ignore_credit_note'] > 0
		module_access['icd_auto_gen_voucher'] = enterprise.setting_flags & enterprise_module_settings[
			'icd_auto_gen_voucher'] > 0
		module_access['scrutiny_flag'] = enterprise.setting_flags & enterprise_module_settings['scrutiny_flag'] > 0
		module_access['dummy_icd'] = enterprise.setting_flags & enterprise_module_settings['icd_flag'] > 0
		return module_access


class AccessLevels(object):
	"""
	Object that holds different level binary permission flags. Primarily for ease-of-use in front-end.
	"""

	def __init__(self, view=0, edit=0, approve=0, delete=0, notify=0, is_enterprise_active=False):
		self.view = (view == UserPermission._VIEW)
		self.edit = (edit == UserPermission._EDIT) and is_enterprise_active
		self.approve = (approve == UserPermission._APPROVE) and is_enterprise_active
		self.delete = (delete == UserPermission._DELETE) and is_enterprise_active
		self.notify = (notify == UserPermission._NOTIFY) and is_enterprise_active

	def __repr__(self):
		return ('[View-%s Edit-%s Approve-%s Delete-%s Notify-%s]' % (
			self.view, self.edit, self.approve, self.delete, self.notify))


class LoginService:
	"""
	Service Class that handles the business logic required for the Login module
	"""

	def __init__(self):
		"""

		"""
		self.user_dao = UserDAO()

	def getLoginUserByEmail(self, email):
		user = None
		try:
			logger.info("Fetching User by Email - %s" % email)
			user = self.user_dao.getUserByEmail(email=email)
			logger.info("User fetched: %s" % user)
		except Exception as e:
			logger.info("Fetching User by Email Failed due to - %s" % e.message)
			ORMSessionFactory.close_all()
			user = self.getLoginUserByEmail(email)
		except InvalidRequestError:
			logger.info("Stale open transactions made the DB connection invalid...")
			user = self.getLoginUserByEmail(email)
		except OperationalError:
			logger.info("SQL Server Gone away...")
			ORMSessionFactory.close_all()
			user = SQLASession().query(User).filter(User.email == email).first()
		finally:
			logger.info("User fetched after handling exceptions: %s" % user)
			return user

	def authenticate(self, user_email, password):
		"""
		Authenticates and provides access to valid users logging into the system

		:param user_email:
		:param password:
		:return: the authenticated user's Persistent Data model (SQL Alchemy)
		"""
		try:
			user = self.getLoginUserByEmail(user_email)
			if user and user.checkPassword(password):
				if user.is_active is True:
					try:
						self.user_dao.db_session.begin(subtransactions=True)
						user.last_login = datetime.datetime.now()
						self.user_dao.db_session.commit()
					except Exception as e:
						self.user_dao.db_session.rollback()
						logger.exception("Could not write last_login datetime for user %s\n%s" % (user_email, e.message))
				return user, user.is_active is True
			logger.error('Authentication Failed')
			return None, None
		except Exception as e:
			logger.exception('Authentication could not be completed - %s' % e.message)
			raise

	def prepareEnterpriseSubscriptionInfo(self, enterprise_id):
		# Updating subscription details in session
		latest_subscription = self.user_dao.db_session.query(EnterpriseSubscription).filter(
			EnterpriseSubscription.enterprise_id == enterprise_id,
			EnterpriseSubscription.is_active.is_(True)).order_by(EnterpriseSubscription.till.desc()).first()
		subscription_data = copyDictToDict(
			source=latest_subscription.__dict__, exclude_keys=(
				'_sa_instance_state', 'since', 'till', 'discount', 'created_on'))
		expired_on = subscription_data['expired_on']

		subscription_data['is_expired'] = datetime.datetime.now() > expired_on
		subscription_data['shall_enable_expiry_timer'] = (expired_on - datetime.datetime.now()).days < 14
		subscription_data['expired_on'] = expired_on.strftime("%Y-%m-%d %H:%M:%S")

		shall_enable_request_extension = True
		extension_requested_on = subscription_data['extension_requested_on']
		if extension_requested_on:
			subscription_data['extension_requested_on'] = extension_requested_on.strftime("%Y-%m-%d %H:%M:%S")
			shall_enable_request_extension = (datetime.datetime.now() - extension_requested_on).days > 2
		subscription_data['shall_enable_request_extension'] = shall_enable_request_extension

		return subscription_data

	def putUserInfoInSession(self, request, user, enterprise):
		"""
		Helper method that puts required User info in session to be used while logged in

		:param request:
		:param user:
		:param enterprise:
		:return: void
		"""
		registration_details = enterprise.registration_details
		gst_detail = ""
		for item in registration_details:
			if item.label.find("GST") != -1:
				gst_detail = item.details

		# Updating enterprise details in session
		request.session[ENTERPRISE_ID_SESSION_KEY] = enterprise.id
		request.session[PRIMARY_ENTERPRISE_ID_SESSION_KEY] = enterprise.id
		request.session[HOME_CURRENCY_KEY] = enterprise.home_currency
		request.session[ENTERPRISE_GST_SESSION_KEY] = gst_detail if gst_detail else None

		make_transient(enterprise)
		subscription_data = self.prepareEnterpriseSubscriptionInfo(enterprise_id=enterprise.id)
		request.session[ENTERPRISE_SUBSCRIPTION] = subscription_data
		request.session[ENTERPRISE_IN_SESSION_KEY] = enterprise

		# Updating user details in session
		request.session[SESSION_KEY] = user.id
		request.session[SESSION_USERNAME_KEY] = user.username
		request.session[SESSION_USERMAIL_KEY] = user.email
		request.session[USER_IN_SESSION_KEY] = UserVO(
			user=user, is_enterprise_active=subscription_data['is_expired'] is False, enterprise=enterprise)

		# Updating subscription material price in session
		subscription_service = SubscriptionService()
		basic_material = subscription_service.getMaterial(enterprise_id=BILLING_ENTERPRISE_ID, drawing_no='XSBASIC')
		standard_material = subscription_service.getMaterial(enterprise_id=BILLING_ENTERPRISE_ID, drawing_no='XSSTANDARD')
		request.session[BASIC_MATERIAL_PRICE_KEY] = int(basic_material.price)
		request.session[STANDARD_MATERIAL_PRICE_KEY] = int(standard_material.price)


		return

	def registerFcmId(self, enterprise_id=None, user_id=None, fcm_id=None):
		"""

		:param enterprise_id:
		:param user_id:
		:param fcm_id:
		:return:
		"""
		self.user_dao.db_session.begin(subtransactions=True)
		try:
			fcm_registrations = self.user_dao.db_session.query(FcmUserRegister).filter(FcmUserRegister.fcm_id == fcm_id)
			for fcm_registration in fcm_registrations.all():
				self.user_dao.db_session.delete(fcm_registration)
			fcm_entry = FcmUserRegister(user_id=user_id, enterprise_id=enterprise_id, fcm_id=fcm_id)
			self.user_dao.db_session.add(fcm_entry)
			self.user_dao.db_session.commit()
			return True
		except Exception as e:
			self.user_dao.db_session.rollback()
			logger.error("Could not update FCM id for user %s" % user_id)
			logger.exception(e)
		return False

	def unRegisterFcmId(self, enterprise_id=None, user_id=None, fcm_id=None):
		"""

		:param enterprise_id:
		:param user_id:
		:param fcm_id:
		:return:
		"""
		self.user_dao.db_session.begin(subtransactions=True)
		try:
			logger.info("Deleting/UnRegister FCM Registration for %s-%s" % (enterprise_id, user_id))
			fcm_registration = self.user_dao.db_session.query(FcmUserRegister).filter(
				FcmUserRegister.fcm_id == fcm_id, FcmUserRegister.user_id == user_id).first()
			if fcm_registration:
				self.user_dao.db_session.delete(fcm_registration)
			self.user_dao.db_session.commit()
			return True
		except Exception as e:
			self.user_dao.db_session.rollback()
			logger.error("Could not update FCM id for user %s" % user_id)
			logger.exception(e)
		return False

	def notifyPasswordChangedToAllMyDevices(self, user=None, message=None, action=None, enterprise_id=None):
		"""

		:param user:
		:param message:
		:param action:
		:param enterprise_id:
		:return:
		"""
		self.user_dao.db_session.begin(subtransactions=True)
		notification = None
		try:
			user_maps = []
			notification = Notification(enterprise_id=enterprise_id, message=message, created_by=user.id)
			notification.created_on = datetime.datetime.now().strftime('%Y-%m-%d %H:%M')
			n_map = NotificationUserMap(
				user_id=user.id, enterprise_id=enterprise_id, notification_id=notification.id)
			user_maps.append(n_map)
			notification.user_maps = user_maps
			logger.debug("Length of user map %s" % len(user_maps))
			self.user_dao.db_session.add(notification)
			self.user_dao.db_session.commit()
		except Exception as e:
			self.user_dao.db_session.rollback()
			logger.exception("Could not update message in notification table %s" % e.message)
		NotificationService().pushFcmNotification(notification=notification, action=action)

	def verify_and_set_password(self, url_base=None, user_email=None, reset_password=False):
		"""

		:param url_base:
		:param user_email:
		:param reset_password:
		:return:
		"""
		try:
			if user_email is None:
				response = response_code.paramMissing()
			else:
				user = self.resetPassword(
					url_base=url_base, user_email=user_email, reset_password=reset_password)
				if user:
					if user.is_active:
						logger.info("password %s" % user)
						response = response_code.success()
						if reset_password is True:
							message = "An email with password reset link has been sent to your email address. Follow the instructions in the email to reset your account password and get back into your account.<br><a role='button' onclick='gotoLogin()'>Login</a>"
						else:
							message = "An email with a set new password link has been sent to the newly created user email address."
						response['custom_message'] = message
					else:
						response = response_code.failure()
						message = "Inactive User"
						response['custom_message'] = message
				else:
					response = response_code.failure()
					response['custom_message'] = "Sorry! This email address is not registered with us!"

		except Exception as e:
			logger.exception("Failed forgot password request! %s" % e.message)
			response = response_code.internalError()
			response['custom_message'] = "Could not reset password"
		return response

	def resetPassword(self, url_base=None, user_email=None, reset_password=None):
		"""

		:param url_base:
		:param user_email:
		:param reset_password:
		:return:
		"""
		db_session = self.user_dao.db_session
		db_session.begin(subtransactions=True)
		try:
			user = self.getLoginUserByEmail(email=user_email)
			logger.info("Password requested from user %s-%s" % (user, user_email))
			if user:
				user_data = dict(
					user_email=user_email, password=user.password,
					reset_on=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
				token = jwt.encode(user_data, JWT_SECRET)
				url = "%s/erp/auth/forget_pwd/?u=%s" % (url_base, token)

				if reset_password is True:
					message = readFile(PASSWORD_RESET_MAIL).replace("{{username}}", user.__repr__()).replace("{{url}}", url)
					sendMail(recipients=(user_email,), subject="[xserp] Reset your account password.", body=message)
				else:
					user_mapping_tbl = db_session.query(UserEnterpriseMap.enterprise_id).filter(UserEnterpriseMap.user_id == user.id, UserEnterpriseMap.status == 1).first()
					enterprise_name = db_session.query(Enterprise.name).filter(Enterprise.id == user_mapping_tbl.enterprise_id).first()
					message = readFile(
						USER_INVITE_MAIL).replace(
						"{{first_name}}", user.first_name).replace(
						"{{last_name}}", user.last_name).replace(
						"{{enterprise_name}}", enterprise_name[0]).replace(
						"{{url}}", url)
					sendMail(recipients=(user_email,), subject="[xserp] User Invitation.", body=message)
			else:
				logger.warn("User is not registered for email %s" % user_email)
			db_session.commit()
			return user
		except:
			db_session.rollback()
			raise

	def changePassword(self, url_base=None, user_email=None, old_password=None, new_password=None):
		"""

		:param url_base:
		:param user_email:
		:param old_password:
		:param new_password:
		:param enterprise_id:
		:return:
		"""
		self.user_dao.db_session.begin(subtransactions=True)
		try:
			user = self.getLoginUserByEmail(email=user_email)
			result = None
			if user:
				logger.info("Password change requested from user %s-%s" % (user, user_email))
				result = old_password == user.password or user.checkPassword(old_password)
				if result is True:
					url = '<a href="%s/erp">login here</a>' % url_base
					user.setPassword(new_password)
					message = readFile(PASSWORD_CHANGED_MAIL).replace("{{username}}", user.__repr__()).replace(
						"{{url}}", url).replace("{{datetime}}", datetime.datetime.now().strftime("%b %d, %Y %H:%M:%S"))
					sendMail(recipients=(user_email,), subject="[xserp] Your account password has been changed.",
					         body=message)
					self.user_dao.db_session.commit()
					user_mapping_tbl = self.user_dao.db_session.query(UserEnterpriseMap.enterprise_id).filter(UserEnterpriseMap.user_id == user.id, UserEnterpriseMap.status == 1).first()
					enterprise_id = user_mapping_tbl.enterprise_id
					self.notifyPasswordChangedToAllMyDevices(
						user=user, message="Your password has been successfully changed", action="logout", enterprise_id=enterprise_id)
				else:
					self.user_dao.db_session.rollback()
			else:
				self.user_dao.db_session.rollback()
			return result
		except:
			self.user_dao.db_session.rollback()
			raise

	def validateEmailId(self, url_base=None, enterprise_name=None, first_name=None, last_name=None,
	                    enterprise_email=None, enterprise_mobile=None, password=None):
		"""
		:return:
		"""
		logger.info("Validating email id for enterprise registration")
		# Check email is already exists
		user = self.getLoginUserByEmail(email=enterprise_email)
		if user:
			return "", "Email address provided is already registered.", False
		# from django.core import signing
		data = {
			'name': enterprise_name, 'first_name': first_name, 'last_name': last_name, 'email': enterprise_email,
			'mobile': enterprise_mobile, 'password': password}
		token = jwt.encode(data, JWT_SECRET)
		validation_link = "%s/erp/auth/validate_and_register/?r=%s" % (url_base, token)
		user_name = first_name + " " + last_name
		message = readFile(WELCOME_MAIL_MAIL).replace("{{enterprise_name}}", enterprise_name).replace(
			"{{user_name}}", user_name).replace("{{url}}", validation_link)
		sendMail(recipients=(enterprise_email,), subject="[xserp] Email Verification. Welcome to xserp!", body=message)
		# Send email to validate email id
		return "Thank you for registering with us.", "The activation link has been sent to your registered email address. You can log in to your account after verifying the activation link.", True

	def sendSignupIntimationMail(
			self, enterprise_name=None, first_name=None, last_name=None, enterprise_email=None,
			enterprise_mobile=None, mail_body_prefix=None, mail_body_suffix=None):
		"""

		:param enterprise_name:
		:param first_name:
		:param last_name:
		:param enterprise_email:
		:param enterprise_mobile:
		:param mail_body_prefix:
		:param mail_body_suffix:
		:return:
		"""
		try:
			enterprise_contact_name = first_name + " " + last_name
			message = readFile(SIGN_UP_INTIMATION_MAIL).replace("{{enterprise_name}}", enterprise_name).replace(
				"{{enterprise_contact_name}}", enterprise_contact_name).replace(
				"{{enterprise_singup_email}}", enterprise_email).replace(
				"{{enterprise_signup_phone_no}}", enterprise_mobile).replace(
				"{{mail_body_prefix}}", mail_body_prefix).replace("{{mail_body_suffix}}", mail_body_suffix)
			sendMail(recipients=XSASSIST_MAIL_ID, subject="[Sign Up] %s" % enterprise_name.upper(), body=message)
		except Exception as e:
			logger.exception("Sending Sing-up intimation Mail Failed: %s" % e)

	def registerEnterprise(
			self, enterprise_name=None, first_name=None, last_name=None, enterprise_email=None,
			enterprise_mobile=None, password=None):
		"""

		:return:
		"""
		self.user_dao.db_session.begin(subtransactions=True)
		try:
			# Checking email is already exists
			if enterprise_email is None:
				return "Provide a valid Email address.", False
			user = self.getLoginUserByEmail(email=enterprise_email)
			if user:
				return "Email address provided is already registered.", False
			enterprise = self.user_dao.db_session.query(Enterprise).filter(Enterprise.email == enterprise_email).first()
			if not enterprise:
				# Creating new enterprise
				enterprise_code = 0
				enterprise_codes = self.user_dao.db_session.query(Enterprise.code).filter(
					Enterprise.code.like(enterprise_name[:3].upper() + "%")).order_by(Enterprise.code.desc()).all()
				if enterprise_codes:
					for code in enterprise_codes:
						if re.match(r'\d\d\d', code[0][3:]):
							enterprise_code = code[0][3:]
							break
				code = "%s%03d" % (enterprise_name[:3].upper(), int(enterprise_code) + 1 if enterprise_code else 1)
				erase_data_expiry_date = datetime.datetime.now() + datetime.timedelta(days=30)
				logger.info("Creating new enterprise %s with code %s" % (enterprise_name, code))
				enterprise = Enterprise(
					name=enterprise_name, code=code, email=enterprise_email, state='', city='', address_1='',
					phone=enterprise_mobile, erase_data_expiry_date=erase_data_expiry_date)
				# Adding trial subscription entry for the newly created enterprise
				since = datetime.datetime.today()
				till = since + relativedelta(months=1, days=1, hour=0, minute=0, second=0, microsecond=0, seconds=-1)
				enterprise_subscription = EnterpriseSubscription(plan='trial', since=since, till=till, expired_on=till)
				enterprise_subscription.enterprise = enterprise
				self.user_dao.db_session.add(enterprise)
				self.user_dao.db_session.add(enterprise_subscription)
			self.user_dao.db_session.commit()
			self.user_dao.db_session.refresh(enterprise)

			# Creating admin user for the new enterprise
			self.user_dao.db_session.begin(subtransactions=True)
			user = User(
				username=enterprise_email, email=enterprise_email, first_name=first_name, last_name=last_name,
				enterprise_id=enterprise.id, is_active=True, is_super=True)
			user.setPassword(password)
			self.user_dao.db_session.add(user)
			self.user_dao.db_session.commit()

			# Creating user enterprise map table
			self.user_dao.db_session.begin(subtransactions=True)
			user_enterprise_map = UserEnterpriseMap(user_id=user.id, enterprise_id=enterprise.id, status=1)
			self.user_dao.db_session.add(user_enterprise_map)
			self.user_dao.db_session.commit()

			# Creating default tax ledger for the new enterprise
			default_taxes = self.user_dao.db_session.query(Tax).filter(Tax.enterprise_id == enterprise.id)
			for tax in default_taxes:
				createTaxLedgers(
					tax=tax, created_by=user.id, db_session=self.user_dao.db_session, is_input=True, is_output=True,
					is_default=True)

			# Creating contact details for the new enterprise
			self.user_dao.db_session.begin(subtransactions=True)
			contact_name = first_name + " " + last_name
			contact_details = ContactDetails(
				name=contact_name, email=enterprise_email, phone_no=enterprise_mobile,
				fax_no="", is_whatsapp=0, created_by=user.id, last_modified_by=user.id, enterprise_id=enterprise.id)
			self.user_dao.db_session.add(contact_details)
			self.user_dao.db_session.commit()

			# Creating enterprise contact map for the new enterprise
			self.user_dao.db_session.begin(subtransactions=True)
			enterprise_contact_details = EnterpriseContactMap(
				contact_id=contact_details.id, enterprise_id=enterprise.id, sequence_id=1)
			self.user_dao.db_session.add(enterprise_contact_details)
			self.user_dao.db_session.commit()

			# Creating invoice_template_config for the new enterprise
			self.user_dao.db_session.begin(subtransactions=True)
			invoice_template_config = InvoiceTemplateConfig(
				enterprise_id=enterprise.id, module='Sales', template_id=2, last_modified_by=user.id,
				last_modified_on=datetime.datetime.now())
			self.user_dao.db_session.add(invoice_template_config)
			self.user_dao.db_session.commit()

			# Creating purchase_template_config for the new enterprise
			default_html = readFile('/templates/admin/print_template/po/po_template_default.html')
			collection = 'default_purchase_template'
			db = MongoDbConnect[collection]
			default_purchase_template = db.find({}, {"_id": 0})
			id = {
				"enterprise_id": enterprise.id,
				"created_by": user.id,
				"created_on": datetime.datetime.now(),
				"last_modified_by": user.id,
				"last_modified_on": datetime.datetime.now(),
				"print_template": default_html
			}
			for template in default_purchase_template:
				template.update(id)
				collection = 'purchase_template_config'
				db = MongoDbConnect[collection]
				db.create_index("enterprise_id", unique=True)
				db.insert(template)

			# Creating sales_estimate_template_config for the new enterprise
			default_se_html = readFile('/templates/admin/print_template/se/se_template_default.html')
			se_collection = 'default_se_template'
			db = MongoDbConnect[se_collection]
			default_se_template = db.find({}, {"_id": 0})
			id = {
				"enterprise_id": enterprise.id,
				"created_by": user.id,
				"created_on": datetime.datetime.now(),
				"last_modified_by": user.id,
				"last_modified_on": datetime.datetime.now(),
				"print_template": default_se_html
			}
			for template in default_se_template:
				template.update(id)
				se_collection_config = 'sales_estimate_template_config'
				db = MongoDbConnect[se_collection_config]
				db.create_index("enterprise_id", unique=True)
				db.insert(template)

			# Creating order_acknowledgement_template_config for the new enterprise
			default_oa_html = readFile('/templates/admin/print_template/oa/oa_template_default.html')
			oa_collection = 'default_oa_template'
			db = MongoDbConnect[oa_collection]
			default_oa_template = db.find({}, {"_id": 0})
			id = {
				"enterprise_id": enterprise.id,
				"created_by": user.id,
				"created_on": datetime.datetime.now(),
				"last_modified_by": user.id,
				"last_modified_on": datetime.datetime.now(),
				"print_template": default_oa_html
			}
			for template in default_oa_template:
				template.update(id)
				oa_collection_config = 'order_acknowledgement_template_config'
				db = MongoDbConnect[oa_collection_config]
				db.create_index("enterprise_id", unique=True)
				db.insert(template)

			# Capturing owner/creator of the enterprise
			self.user_dao.db_session.begin(subtransactions=True)
			user = self.getLoginUserByEmail(email=enterprise_email)
			enterprise.created_by = user.id
			enterprise.last_modified_by = user.id
			self.user_dao.db_session.add(enterprise)
			self.user_dao.db_session.commit()

			user_data = dict(
				user_email=enterprise_email, password=password,
				reset_on=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
			token = jwt.encode(user_data, JWT_SECRET)
			return token, True
		except Exception as e:
			self.user_dao.db_session.rollback()
			raise e

	def sendRegisteredIntimationMail(self, enterprise_email=None, first_name=None, last_name=None):
		"""

		:param enterprise_email:
		:param first_name:
		:param last_name:
		:return:
		"""
		try:
			enterprise = self.user_dao.db_session.query(Enterprise).filter(Enterprise.email == enterprise_email).first()
			contact_name = first_name + " " + last_name
			message = readFile(REGISTERED_INTIMATION_MAIL).replace("{{enterprise_name}}", enterprise.name).replace(
				"{{registration_contact_name}}", contact_name).replace("{{status}}", "verified").replace(
				"{{enterprise_code}}", enterprise.code).replace("{{registration_email}}", enterprise.email).replace(
				"{{registration_phone_no}}", enterprise.phone)
			sendMail(recipients=XSASSIST_MAIL_ID, subject="[Sign Up] %s" % enterprise.name.upper(), body=message)
		except Exception as e:
			logger.exception("Sending Sing-up intimation Mail Failed: %s" % e)
