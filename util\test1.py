"""
"""
__author__ = 'saravanan'

import mysql.connector

# Connect to MySQL
conn = mysql.connector.connect(user="ensqlbackup", password="PMH!Q87d$l!(1)", host="bugs.seepl18.net.in", database="xserp-workorder", port=11985)
cursor = conn.cursor()

# Data to insert
data = [
	('24-25/GV00015', '2024-04-02', 814, 10000.00, 1, 102, 312192),
	('24-25/GV00018', '2024-04-05', 822, 228972.00, 1, 102, 315651),
	# Add all other rows
]

for row in data:
	# Insert into ledger_bills
	cursor.execute("""INSERT INTO ledger_bills (bill_no, bill_date, ledger_id, net_value, is_debit, enterprise_id, voucher_id)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
    """, row)

	# Get the last inserted id
	bill_id = cursor.lastrowid

	# Determine dr_value and cr_value based on is_debit
	if row[4] == 1:
		dr_value = row[3]
		cr_value = 0.00
	else:
		dr_value = 0.00
		cr_value = row[3]

	# Insert into ledger_bill_settlements
	cursor.execute("""
        INSERT INTO ledger_bill_settlements (bill_id, voucher_id, item_no, dr_value, cr_value, enterprise_id)
        VALUES (%s, %s, %s, %s, %s, %s)
    """, (bill_id, row[6], 1, dr_value, cr_value, row[5]))

# Commit the transaction
conn.commit()

# Close the connection
cursor.close()
conn.close()
