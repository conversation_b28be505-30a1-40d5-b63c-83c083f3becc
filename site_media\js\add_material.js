$(function () {
    $(document).ready(function () {
    	$(".chosen-select").chosen();
    	transformToDummyForm('material_make');
    });

     $("#saveCatalogueButton, #save-material").click(function(){
        $("#saveCatalogueButton").addClass("disabled").text("Processing...");
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();
        $(".suggestion-container").remove();
        var ControlCollections = [
            {
                controltype: 'dropdown',
                controlid: 'id_material-category_select',
                isrequired: true,
                errormsg: 'Category is required.'
            },
            {
                controltype: 'textbox',
                controlid: 'id_material-name',
                isrequired: true,
                errormsg: 'Name is required.'
            },
            {
                controltype: 'dropdown',
                controlid: 'id_material-unit_id',
                isrequired: true,
                errormsg: 'Unit is required.'
            },
            {
                controltype: 'textbox',
                controlid: 'id_material-price',
                isrequired: true,
                errormsg: 'Price is required.'
            },
            {
                controltype: 'textbox',
                controlid: 'id_material-minimum_stock_level',
                isrequired: true,
                errormsg: 'MSL is required. MSL should be at least zero.'
            },
            {
                controltype: 'dropdown',
                controlid: 'id_material-unit_conversion_rate',
                isrequired: true,
                errormsg: 'Rate is required.',
                mindigit: 0.00001,
                mindigiterrormsg: 'Conversion Rate cannot be 0.'
            },
            {
                controltype: 'textbox',
                controlid: 'id_material-tariff_no',
                isrequired: false,
                ishsn: true,
                hsnerrormsg: 'Invalid HSN Code.'
            },
        ];
        var result = JSCustomValidator.JSvalidate(ControlCollections);
        $("#saveCatalogueButton").removeClass("disabled").text("Save");
        if(result){
            $("#supp_price_table tr.reject_remarks:visible").each(function() {
                if($(this).find('input').val() == ""){
                    $(this).find('input').addClass('error-border');
                    result = false;
                }
            });
            if(!result){
                swal('','Rejection remarks is required', '')
            }
        }

        if(result) {
            SaveCatalogueButton();
        }
        return result;
    });

    function SaveCatalogueButton() {
        $("#saveCatalogueButton").addClass("disabled").text("Processing...");
        if ($('#id_material-drawing_no').is(':disabled')){
            $("#edit_catalogue_form").submit();
            ga('send', 'event', 'Material', 'Update', $('#enterprise_label').val(), 1);
            $('#loading').show();
            return;
        }
        else {
            $.ajax({
                url: "/erp/masters/json/materials/checkdrawingno/",
                type: "POST",
                dataType: "json",
                data: {drawing_no: $("#id_material-drawing_no").val(), item_id: $("#id_material-material_id").val(),
                    material_name: $("#id_material-name").val() },
                success: function (duplicate_count) {
                    if (parseInt(duplicate_count.drawing_no) > 0) {
                        $(".duplicate_drawing").text('Drawing Number already exists');
                        $("#id_material-drawing_no").addClass('error-border');
                        $("#id_material-drawing_no").focus();
                        $("#saveCatalogueButton").removeClass("disabled").text("Save");
                        $("#id_material-drawing_no").keyup(function(){
                            $(".duplicate_drawing").text('');
                            $("#id_material-drawing_no").removeClass('error-border');
                            $('#loading').hide();
                        });
                    }
                    else if(parseInt(duplicate_count.name) > 0 ){
                        if(!$('#add_material_modal').hasClass('in')) {
                            swal("","Material Name already exists.","warning")
                        }
                        $(".duplicate_material_name").text('Material Name already exists');
                        $("#id_material-name").addClass('error-border')
                        $("#id_material-name").focus();
                        $("#saveCatalogueButton").removeClass("disabled").text("Save");
                        $("#id_material-name").keyup(function(){
                            $(".duplicate_material_name").text('');
                            $("#id_material-name").removeClass('error-border')
                            $('#loading').hide();
                        });
                    }
                    else {
                        $("#edit_catalogue_form").submit();
                        $('#loading').show();
                        var event_action = window.location.pathname.indexOf('catalogues') > -1 ? "Create" : "Create via In-place Addition";
                        ga('send', 'event', 'Material', event_action, $('#enterprise_label').val(), 1);
                    }
                },
                error: function (xhr, errmsg, err) {
                    console.log(xhr.status + ": " + xhr.responseText);
                    $("#saveCatalogueButton").removeClass("disabled").text("Save");
                    $('#loading').hide();
                }
            });
        }
    }

    $("select#id_material-category_select").change(function () {
        var category = $("#id_material-category_select").val();
        $("#id_material-category_id").val(category);
    });
});

function saveNewCatergory(){
    $(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();
    var ControlCollections = [
        {
            controltype: 'textbox',
            controlid: 'catName',
            isrequired: true,
            errormsg: 'Category is required.'
        }
    ];
    var result = JSCustomValidator.JSvalidate(ControlCollections);
    if(result) {
        $.ajax({
            url: "/erp/masters/json/materials/categorysave/",
            type: "POST",
            dataType: "json",
            data:{"name":$("#catName").val(), "is_service": $("#id_material-is_service").is(":checked")},
            success: function (json) {
                console.log(json)
                if (json["message"].indexOf("1062") != -1) {
                    removeValidationError();
                    var row = `<span class="custom-error-message">Category Name already Exists.</span>`
                    $("#catName").addClass('error-border');
                    $(row).insertAfter("#catName");
                }
                else {
                    if(json["message"].indexOf("Success") >= 0) {
                        swal("",json["message"],"success");
                    }
                    else {
                        swal("",json["message"],"warning");
                    }
                    $("#catName").val("");
                    $("#materialcategory").modal('hide');
                    populateCategoryOptions(json["new_category_id"]);
                    $("#id_material-category_id").val(json["new_category_id"]);
                }
                $("#id_material-category_select").focus();
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });
    }
}

function populateCategoryOptions(selected_category_id){
    $.ajax({
        url: "/erp/masters/json/materials/populate_categories/",
        type: "POST",
        dataType: "json",
        data: {"category_id": selected_category_id, "is_service": $("#id_material-is_service").is(":checked")},
        success: function(category_options) {
            option_html = "";
            for(i=0; i < category_options.length; i++){
                option_html += "<option value=\"" + category_options[i]["category_id"] + "\"" +
                                category_options[i]["attrs"] + ">" +
                                category_options[i]["label"] + "</option>";
            }
            $("#id_material-category_select").html(option_html).chosen().trigger("chosen:updated");
        },
        error: function (xhr, errmsg, err) {
            console.log("VV", errmsg, err, xhr.status + ": " + xhr.responseText);
        }
    });
}

function addNewUnit(){
    $(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();
    var ControlCollections = [
        {
            controltype: 'textbox',
            controlid: 'unit_name',
            isrequired: true,
            errormsg: 'Unit Name is required.'
        },
        {
            controltype: 'textbox',
            controlid: 'unit_desc',
            isrequired: true,
            errormsg: 'Description is required.'
        }
    ];
    var result = JSCustomValidator.JSvalidate(ControlCollections);
    if(result) {
        $("#addNewUnit").addClass('btn-processing').text("Processing...");
        $.ajax({
            url: "/erp/masters/json/materials/checkunit/",
            type: "POST",
            dataType: "json",
            data:{unit_name:$("#unit_name").val()},
            success: function (response) {
                if (response.response_message =="Success") {
                    if(response.custom_message == "Unit already exists"){
                        var row = `<span class="custom-error-message">Unit Name already Exists.</span>`
                        $("#unit_name").addClass('error-border');
                        $(row).insertAfter("#unit_name");
                        $("#addNewUnit").removeClass('btn-processing').text("Add");
                    }
                    else {
                        saveMaterialUnit();
                    }
                }
                else {
                    swal({title: "", text: response.custom_message, type: "error"});
                    $("#addNewUnit").removeClass('btn-processing').text("Add");
               }
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
                $("#addNewUnit").removeClass('btn-processing').text("Add");
            }
        });
    }
}

function saveMaterialUnit(){
    $.ajax({
        url: "/erp/masters/json/materials/saveunit/",
        type: "POST",
        dataType: "json",
        data:{unit_name:$("#unit_name").val(), unit_desc:$("#unit_desc").val()},
        success: function (json) {
            $("#addNewUnit").removeClass('btn-processing').text("Add");
            swal("",json["message"],"success");
            $("#unit_name, #unit_desc").val("");
            $("#add_new_unit_modal").modal('hide');
            populateMaterialUnit(json["new_unit_id"]);
        },
        error: function (xhr, errmsg, err) {
            swal("","Server side error.","error");
            $("#add_new_unit_modal").modal('hide');
            $("#addNewUnit").removeClass('btn-processing').text("Add");
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });
}

function populateMaterialUnit(selected_unit_id){
    $.ajax({
        url: "/erp/masters/json/materials/populate_unit_choices/",
        type: "POST",
        dataType: "json",
        data: "",
        success: function(unit_options) {
            option_html = "";
            for(i=0; i < unit_options.length; i++){
                option_html += "<option value='"+unit_options[i][0]+"'>" + unit_options[i][1] + "</option>";
            }
            $("#id_material-unit_id").html(option_html);
            $("#id_material-unit_id").val(selected_unit_id).trigger("chosen:updated");
        }
    });
}

function updateHSNValue(current, currentTd) {
    $("#"+currentTd).removeClass('error-border');
    $("#"+currentTd).closest("div").find(".custom-error-message").remove();
    $("#"+currentTd).closest("div").find(".hsn-suggestion").remove();
    $("#"+currentTd).val(current);
}
