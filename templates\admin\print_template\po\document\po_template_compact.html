<!DOCTYPE html>

<style type="text/css">
	.po_template_view{
		color: #000;
		font-family: pdf_{{general_res.font_family}};
	}

	.po_template_view table {
		width:100%;
	}
	.po_template_view table, th, td {
		border-collapse: collapse;
	}

	.po_template_view .table.row-seperator th,
	.po_template_view .table.row-seperator td,
	.po_template_view .table.row-seperator th,
	.po_template_view .table.row-seperator td {
	    border-top: 1px solid #666666 !important;
	    border-bottom: 1px solid #666666 !important;
	}

	.po_template_view .table.column-seperator th,
	.po_template_view .table.column-seperator td,
	.po_template_view .table.column-seperator th,
	.po_template_view .table.column-seperator td {
	    border-right: 1px solid #666666 !important;
	    border-left: 1px solid #666666 !important;
	}

	.po_template_view th{
		text-align: center;
		line-height: 25px;
	}
	.po_template_view td{
		line-height: 25px;
	}
	
	.po_template_view .enterprise_details{
		font-size:12pt;
	}
	.po_template_view .vendors{
		font-weight:bold;
	}
	.po_template_view .page_title{
		font-size: 16pt;
		margin-left: 5px;
	}
	.po_template_view .po_details{
		font-weight: bold;
		font-size:12pt;
	}
	.po_template_view .total_price{
		font-size:11pt;
		font-weight: bold;
		margin-top: 4px;
		margin-bottom: 3px;
		text-align:right;
		margin-right:-295px;
	}
	.po_template_view .total_gst{
		text-align: right;
		border-bottom:hidden;
		border-left:hidden;
		border-right:hidden;
		line-height:25px;
	}
	.po_template_view .amt{
		font-size: 11pt;
		font-weight: bold;
		margin-top: 4px;
		float:right;
		margin-right:-14px;
	}
	.po_template_view .sub_total th ,
	.po_template_view .sub_total td{
		text-align:right;
		font-weight: normal;
		padding-right:3px;
		font-size:11pt;
	}
	.po_template_view .total th ,
	.po_template_view .total td{
		text-align:right;
	}

	.po_template_view .terms_condition{
		margin-top: 50px;
		font-size: 11pt;
		line-height:25px;
	}
	.po_template_view .terms_condition_bottom{
		margin-bottom:20px;
	    margin-top:20px;
	}
	.po_template_view .notes{
		font-size:11pt;
	}
	.po_template_view .notes_bottom{
		margin-bottom:10px;
	    margin-top:5px;
	}
	.po_template_view .acknowledge_details{
		font-size:10pt;
	}

	.po_template_view .reg_details{
			font-size:11pt;
	}
	.po_template_view .grand_total {
		border-left:hidden;
		border-right:hidden;
		border-bottom:hidden;
		font-size:11pt;
	}

	.po_template_view .purchase_order{
		margin-left: -4px;
		font-size:10pt;
	}

	.po_template_view .subject{
		text-align: justified;
		margin-top: 6px;
		font-size: 12pt;
	}

	.po_template_view .vendor_list {
		width:100px;
		font-weight:bold;
		font-size:12pt;
		display: inline-table;
	    word-break: break-all;
	}

	.po_template_view .vendor_data{
		width: 280px;
		font-size:10pt;
		display: inline-table;
	    word-break: break-all;
	    vertical-align:top;

	}
	.po_template_view .po_list {
	    width: 86px;
	    font-weight: bold;
	    font-size: 12px;
	    display: inline-table;
	    word-break: break-all;
	}

	.po_template_view .po_data {
	    width: 240px;
	    font-size: 12px;
	    display: inline-table;
	    vertical-align:top;
	     word-break: break-all;
	}

	.po_template_view .payment_term {
		width:150px;
		display:inline-block;
	}

	.po_template_view .payment_details{
		width: 700px;
		display: inline-block;
		word-break:break-all;
		vertical-align:top;
	}

	.po_template_view .annexture_po, 
	.po_template_view .delivery_schedules_po{
		font-size:11pt;
		text-align:center;
	 	margin-bottom:30px;
	 	margin-top:30px;
	}

	.po_template_view .registration_label{
		width: 50px;
		display: inline-block;
		word-break: break-word;
		vertical-align: top
	}
	.po_template_view  .registration_data{
		width: 180px;
		display: inline-table;
		word-break: break-word;
		vertical-align: top;
	}

	.bold {
        font-weight: bold;
    }

	@font-face {
        font-family: 'Times New Roman';
        font-style: normal;
        font-weight: 400;
        src: url(https://www.xserp.in/site_media/fonts/times.ttf) format('truetype');
	}
	@font-face {
	        font-family: 'pdf_Roboto';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/roboto.regular.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Times New Roman';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/times.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Helvetica';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/roboto.regular.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Ubuntu';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/Ubuntu-Regular.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Comic Sans';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/comicsansms3.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_DejaVu Sans';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/DejaVuSans.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Courier New';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/cour.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Verdana';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/verdana.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Open Sans';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/open-sans-Regular.ttf) format('truetype');
	}

	.pp-document .for-non-pp {
	    display: none;
	}
</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/bootstrap.css?v={[ current_version ]}">
<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={[ current_version ]}">
<body>
	<div class="po_template_view [% if source.type == 2 %] pp-document [% endif %]" style="border-color: transparent; width: 100%; font-family: 'pdf_{[general_res.font_family]}'">
		<div class="row">
			<div class="header-banner-container col-sm-12 for-non-pp">
				<div class="text-left pull-left misc_header-left_banner" style="padding-left: 0;">
					<img class="image_uploader-left img-responsive" src="{[header_left_image]}">
				</div>
				<div class="text-left pull-left misc_header-center_banner">
					<img class="image_uploader-center img-responsive" src="{[header_center_image]}" style="margin:0 auto;" >
				</div>
				<div class="text-left pull-left misc_header-right_banner" style="padding-right: 0;">
					<img class="image_uploader-right img-responsive" src="{[header_right_image]}" style="float: right;">
				</div>
			</div>
            <div style="clear: both;"></div>
	        <div class="col-sm-8">
	            <img src="{[enterprise_logo]}" class="pdf_company_logo" style="height:{[header_res.company.logo.size]}px;"><br>
	            [% if header_res.company.company_name.print %]
	            	 <span  class="pdf_company_name" style="font-size:12px;font-family: 'pdf_{[header_res.company.company_name.font_family]}'">{[source.enterprise.name]}</span><br>
            	[% endif %]
            	[% if header_res.company_info.print_address %]
		            <div class="pdf_company_address pdf_company_address_container" style="font-size:{[header_res.font_size]}px;">
                        {[ enterprise_address ]}
		            </div>
	            [% endif %]
	            [% if header_res.company_info.print_phone_no %]
					<span class="pdf_company_contact pdf_company_address_container" style="font-size:{[header_res.font_size]}px;">
						<b>Ph:</b> [% if source.enterprise.primary_contact_details.contact.phone_no %] {[ source.enterprise.primary_contact_details.contact.phone_no ]} [% endif %]
					</span>
				[% endif %]
				[% if header_res.company_info.print_email %]
					<span  class="pdf_company_email pdf_company_address_container" style="font-size:{[header_res.font_size]}px;">
						<b>Email:</b>[% if source.enterprise.primary_contact_details.contact.email %] {[ source.enterprise.primary_contact_details.contact.email ]} [% endif %]
					</span>
				[% endif %]
				[% if header_res.company_info.print_fax %]
					<span class="pdf_company_fax pdf_company_address_container" style="font-size:{[header_res.font_size]}px;">
						<b>Fax:</b>[% if source.enterprise.primary_contact_details.contact.fax_no %] {[ source.enterprise.primary_contact_details.contact.fax_no ]} [% endif %]
					</span>
				[% endif %]
	        </div>

	        <div class="col-sm-4 pdf_company_address_container" >
		        <div>
					[% if source.is_blanket_po %]
						<span style="border:1px solid #A9A9A9; border-radius:5px;padding:2px 6px; font-size:13px;">Blanket / Open</span>
					[% endif %]
				</div>
	            <div class="page_title pdf_form_name_text_size" style='word-break: break-all;font-size: {[header_res.form_name.font_size]}px;'>[% if source.type != 2 %]{[form_name]}[% else %]PRODUCTION PLAN[% endif %]</div>
	            [% for reg_detail in po_reg_details %]
					<span class="pdf_registration_{[ reg_detail.label_id ]}" style="font-size:{[header_res.font_size]}px;">
						<span class="pdf_enterprise_details registration_label">{[ reg_detail.label ]}</span><span class="registration_data">:{[ reg_detail.details ]}</span>
						<br />
					</span>
				[% endfor %]
	        </div>
	    </div>

	    <div class="clearfix"></div><br>

	    <div class="row">
			<div class="col-sm-7  pdf_company_details_container for-non-pp">
	            [% if header_res.vendor.name.print %]
		             <div class="pdf_vendor_to_enterprise_name">
                        <span class="vendor_list pdf_company_details_container vendorname_to_text">{[ header_res.vendor.name.label ]}</span>
						<span class="vendor_data pdf_company_details_container">: <b>{[ source.supplier.name ]}</b><br></span>
		            </div>
		        [% endif %]
		        [% if header_res.vendor.address.print %]
		             <div class="pdf_vendor_to_address pdf_company_details_container">
	                    <span class="vendor_list pdf_company_details_container vendoraddress_to_text">{[ header_res.vendor.address.label ]}</span>
			            <span class="vendor_data">
	                        <span class="pdf_company_details_container">: {[ source.supplier.address_1 ]}, {[ source.supplier.address_2 ]} </span><br>
			                <span class="pdf_company_details_container">
			                	[% if source.supplier.city != '' %]
				                    {[ source.supplier.city ]},
				                [% endif %]
				                [% if source.supplier.state != '' %]
				                    {[ source.supplier.state ]},
				                [% endif %]
				                [% if source.supplier.pin_code != '' and source.supplier.pin_code != 'None' %]
				                    {[ source.supplier.pin_code ]},
				                [% endif %]
			                	[% for country in country_list %]
									[% if country.country_code == source.supplier.country_code %]
										{[ country.country_name|upper ]}
									[% endif %]
								[% endfor %]
			                </span>
				        </span>
		            </div>
	            [% endif %]
	            [% if header_res.vendor.code.print %]
		            <div class="pdf_vendor_to_enterprise_code pdf_company_details_container">
                        <span class="vendor_list pdf_company_details_container vendorcode_to_text" >{[ header_res.vendor.code.label ]}</span>
		                <span class="vendor_data pdf_company_details_container">: {[ source.supplier.code ]}
			               [% if source.supplier.registration_details and source.supplier.registration_details != '' %]
			                    <b style="font-size:14px;" class="pdf_company_details_container">GSTIN</b> :
			                    [% for reg_detail in source.supplier.registration_details %]
									[% if reg_detail.label == "GSTIN" %]
										{[ reg_detail.details ]}
									[% endif %]
								[% endfor %]
                            [% endif %]

		                </span>
		            </div>
	            [% endif %]
	            [% if header_res.vendor.print_phone_no and source.supplier.primary_contact_details.contact.phone_no != "" %]
		             <div class="pdf_vendor_to_contact pdf_company_details_container">
						<span class="vendor_list pdf_company_details_container" >Phone No </span>
						<span class="vendor_data pdf_company_details_container">: {[ source.supplier.primary_contact_details.contact.phone_no ]}</span>
		             </div>
	            [% endif %]
	            [% if header_res.vendor.print_email and source.supplier.primary_contact_details.contact.email != "" %]
			        <div class="pdf_vendor_to_email pdf_company_details_container">
		               <span class="vendor_list pdf_company_details_container" >E-Mail </span>
		               <span class="vendor_data pdf_company_details_container">: {[ source.supplier.primary_contact_details.contact.email ]}</span>
		            </div><br>
	            [% endif %]
	        </div>

	        <div class="col-sm-5 pdf_company_details_container">
		        [% if source.type == 1 %]
					<span class="pdf_po_no "><b class="pdf_others_details pdf_jo_no_txt pdf_po_no_txt po_list pdf_company_details_container">{[ header_res.field_name.jono_label ]}</b> <span class="po_data pdf_company_details_container"> : {[ source.getCode ]} </span></br></span>
		        [% elif source.type == 2 %]
					<span class="pdf_po_no "><b class="pdf_others_details po_list pdf_company_details_container">PP Number</b> <span class="po_data pdf_company_details_container"> : {[ source.getCode ]} </span></br></span>
				[% else %]
		            <span class="pdf_po_no "><b class="pdf_others_details pdf_po_no_txt pdf_po_no_txt po_list pdf_company_details_container">{[ header_res.field_name.pono_label ]}</b> <span class="po_data pdf_company_details_container"> : {[ source.getCode ]} <i>[% if source.revisions.length > 0 %] (#{[ source.revisions.length ]}) [% endif %]</i></span></br></span>
		        [% endif %]
				[% if source.type != 2 %]
		        	<span class="pdf_podate_span"><b class="pdf_others_details pdf_podate_txt po_list pdf_company_details_container">{[ header_res.field_name.po_datelabel ]}</b> <span class="po_data pdf_company_details_container"> : {[ po_date ]}</span></br></span>
		        [% else %]
		        	<span class="pdf_podate_span"><b class="pdf_others_details po_list pdf_company_details_container">PP Date</b> <span class="po_data pdf_company_details_container"> : {[ po_date ]}</span></br></span>
		        [% endif %]
				[% if header_res.field_name.indent_no.print %]
		            <span class="pdf_indent_no"><b class="pdf_others_details pdf_indent_no_txt po_list pdf_company_details_container"> {[ header_res.field_name.indent_no.label ]}</b> <span class="po_data pdf_company_details_container">: [% if source.indent %] {[ source.indent.getCode ]} [% else %] -NA- [% endif %]</span></br></span>
		        [% endif %]
		        [% if header_res.field_name.indent_date.print %]
		            <span class="pdf_indent_date"><b class="pdf_others_details pdf_indent_date_txt po_list pdf_company_details_container"> {[ header_res.field_name.indent_date.label ]}</b> <span class="po_data pdf_company_details_container">: [% if indent_date %] {[ indent_date ]} [% else %] -NA- [% endif %]</span></br></span>
		        [% endif %]
				[% if source.type == 2 %]
					<span class="pdf_podate_span"><b class="pdf_others_details po_list pdf_company_details_container">Issue To</b> <span class="po_data pdf_company_details_container"> : {[ source.issue_to ]}</span></br></span>
					<span class="pdf_podate_span"><b class="pdf_others_details po_list pdf_company_details_container">Schedule End <br>Date</br></b> <span class="po_data pdf_company_details_container"> : {[ source.delivery|date:"M d, Y" ]}</span></br></span>
				[% endif %]
				[% if header_res.field_name.currency.print %]
					<span class="pdf_transport_currency for-non-pp"><b class="pdf_others_details pdf_currency_txt po_list pdf_company_details_container">{[ header_res.field_name.currency.label ]}</b> <span class="po_data pdf_company_details_container">: {[ source.currency.code ]}</span></br></span>
				[% endif %]
				[% if header_res.field_name.quotn_ref_no.print %]
					<span class="pdf_quotn_ref for-non-pp"><b class="pdf_others_details pdf_quotn_ref_txt po_list pdf_company_details_container">{[ header_res.field_name.quotn_ref_no.label ]}</b> <span class="po_data pdf_company_details_container">: [% if source.quotation_ref_no %] {[ source.quotation_ref_no ]} [% else %] -NA- [% endif %]</span></br></span>
				[% endif %]
				[% if header_res.field_name.quotn_date.print %]
					<span class="pdf_quotn_date for-non-pp"><b class="pdf_others_details pdf_quotn_date_txt po_list pdf_company_details_container">{[ header_res.field_name.quotn_date.label ]}</b> <span class="po_data pdf_company_details_container">: [% if quotation_date %] {[ quotation_date ]} [% else %] -NA- [% endif %]</span></br></span>
				[% endif %]
				[% if source.is_blanket_po %]
					<span>
						<span class="po_list"><b>Validity Period</b></span>
						<span class="po_data">: {[ valid_since ]} - {[ valid_till ]}</span></br>
					</span>
				[% endif %]
	        </div>
	    </div> <br>

		<div class="clearfix"></div>

		<div>
			<div class="row for-non-pp">
			    <div class="col-sm-12 greeting_subject" style="font-size:16px">
					<div>[% autoescape off %]{[ subject ]}[% endautoescape %]</div>
				 </div>
			    <div class="col-sm-12 greeting_mailbody" style="font-size:16px">
					<div>[% autoescape off %]{[ body ]}[% endautoescape %]</div>
				</div>
			</div>
				[% if misc_res.print_summary_first_page and appendix_pages > 0 %]
					<atable class="table item_table table-bordered row-seperator column-seperator" id="item_table" style="font-size:11px; width: 100%">
					    <athead>
					        <atr class="row_seperator column_seperator header_shading">
					            <ath class="text-center td_sno td_sno_text" rowspan="2" scope="col">S.no</ath>
					            <ath class="text-center td_description td_description_text" rowspan="2" style="width:30%;">Description</ath>
					            <ath class="text-center pdf_item_hsn_code_txt td_hsn_code hide for-non-pp" rowspan="2">HSN/SAC</ath>
					            <ath class="text-center td_qty td_qty_text" rowspan="2" scope="col">Qty</ath>
					            <ath class="text-center td_uom td_uom_text" rowspan="2" scope="col">UOM</ath>
					            <ath class="text-center td_price td_price_text for-non-pp" rowspan="2" scope="col">Price<br>{[ source.currency.code ]}</ath>
					            <ath class="text-center td_disc td_disc_text for-non-pp" rowspan="2" scope="col">Disc<br>%</ath>
					            <ath class="text-center td_tax td_tax_text for-non-pp" rowspan="2" scope="col">Total<br>{[ source.currency.code ]}</ath>
								<ath class="text-center td_cgst tax_rate_column for-non-pp" colspan="2" style="width: 12%; ">CGST</ath>
								<ath class="text-center td_sgst tax_rate_column for-non-pp" colspan="2" style="width: 12%; ">SGST</ath>
								<ath class="text-center td_igst tax_rate_column for-non-pp" colspan="2" style="width: 12%;  border-right-color: #ccc;">IGST</ath>
								<ath class="text-center td_tax_amt tax_one_column hide for-non-pp" rowspan="2" style="width: 6%">CGST</ath>
								<ath class="text-center td_tax_amt tax_one_column hide for-non-pp" rowspan="2" style="width: 6%">SGST</ath>
								<ath class="text-center td_tax_amt tax_one_column hide for-non-pp" rowspan="2" style="width: 6%">IGST</ath>
							</atr>
							<atr class="row_seperator column_seperator tax_rate_column header_shading tr_second_row for-non-pp">
								<ath class="text-center td_cgst_rate td_tax_rate td_gst_rate">%</ath>
								<ath class="text-center td_cgst_amt td_tax_amt td_gst_amt">{[ source.currency.code ]}</ath>
								<ath class="text-center td_sgst_rate td_tax_rate td_gst_rate">%</ath>
								<ath class="text-center td_sgst_amt td_tax_amt td_gst_amt">{[ source.currency.code ]}</ath>
								<ath class="text-center td_igst_rate td_tax_rate td_gst_rate">%</ath>
								<ath class="text-center td_igst_amt td_tax_amt td_gst_amt">{[ source.currency.code ]}</ath>
							</atr>
						</athead>
						<atbody>
							<atr class="sub_total for-non-pp">
								<atd colspan="13" class="full-length-td" style="text-align: center; line-height:30px; font-size:11px;"><b>AS PER ANNEXURE TO PO NO:<br>{[ source.getCode ]}</b>
								</atd>
							</atr>
						</atbody>
						<atfoot>
							<atr class="row_seperator column_seperator total_section sub_total_section for-non-pp">
								<atd colspan="5" class="text-right total_section_1"><b>Sub-Total</b></atd>
								<atd class="total_section_2 hide">{[ total_quantity ]}</atd>
								<atd colspan="2" class="text-right total_section_3">{[ total_value]}</atd>
								<atd class="tax_rate_column td_gst_rate"></atd>
								<atd class="tax_rate_column text-right td_gst_amt">{[ total_cgst_value ]}</atd>
								<atd class="tax_rate_column td_gst_rate"></atd>
								<atd class="tax_rate_column text-right td_gst_amt">{[ total_sgst_value ]}</atd>
								<atd class="tax_rate_column td_gst_rate"></atd>
								<atd class="tax_rate_column text-right td_gst_amt" style="border-right-color: #ccc;">{[ total_igst_value ]}</atd>
								<atd class="text-center tax_one_column tax_one_column_csgt_total hide">{[ total_cgst_value ]}<br></atd>
								<atd class="text-center tax_one_column tax_one_column_ssgt_total hide">{[ total_sgst_value ]}<br></atd>
								<atd class="text-center tax_one_column tax_one_column_isgt_total hide" style="border-right-color: #ccc;">{[ total_igst_value ]}<br></atd>
							</atr>
							[% for tax in po_taxes %]
								<atr class="row_seperator column_seperator other_tax_column">
									<atd colspan="5" class="text-right total_section_1">{[ tax.tax_name ]} @ {[ tax.tax_rate ]}%</atd>
									<atd colspan="2" class="text-right total_section_3">{[ tax.tax_value ]}</atd>
								</atr>
							[% endfor %]
							[% for key, value in tax_summary.cgst_summary.items %]
								<atr  class="row_seperator column_seperator consolidated_tax">
									<atd colspan="5" class="text-right total_section_1">CGST @ {[ key|floatformat:2 ]}%</atd>
									<atd colspan="2" class="text-right total_section_3">{[ value|floatformat:2 ]}</atd>
								</atr>
							[% endfor %]
							[% for key, value in tax_summary.sgst_summary.items %]
								<atr class="row_seperator column_seperator consolidated_tax">
									<atd colspan="5" class="text-right total_section_1">SGST @ {[ key|floatformat:2 ]}%</atd>
									<atd colspan="2" class="text-right total_section_3">{[ value|floatformat:2 ]}</atd>
								</atr>
							[% endfor %]
							[% for key, value in tax_summary.igst_summary.items %]
								<atr  class="row_seperator column_seperator consolidated_tax">
									<atd colspan="5" class="text-right total_section_1">IGST @ {[ key|floatformat:2 ]}%</atd>
									<atd colspan="2" class="text-right total_section_3">{[ value|floatformat:2 ]}</atd>
								</atr>
							[% endfor %]
							[% if source.round_off != None %]
							<atr class="row_seperator column_seperator total_section">
								<atd colspan="5" class="text-right total_section_1"><b>Round Off</b></atd>
								<atd colspan="2" class="text-right total_section_3">{[ source.round_off ]}</atd>
							</atr>
							[% endif %]
							<atr class="row_seperator column_seperator total_section">
								<atd colspan="5" class="text-right total_section_1"><b>Grand Total</b><span class="total_inclusice_tax hide"> (inclusive tax)</span></atd>
								<atd colspan="2" class="text-right total_section_3">{[ source.currency.code ]} <b>{[ source.total ]}</b></atd>
							</atr>
							<atr class="tr_total_in_words row_seperator show_total_in_words total_in_words">
								<atd colspan="13" class="full-length-td"><b>Total Value ({[ source.currency.code ]}):</b>  {[ total_in_words|upper ]}</atd>
							</atr>
						</atfoot>
					</atable>
				[% else %]
					{% include "admin/print_template/po/document/common/po_template_item_table.html" %}
					[% if source.type != 2 %]
						[% if summary_res.hsn_summary.print %]
							{% include "admin/print_template/po/document/common/po_template_hsn_summary.html" %}
						[% endif %]
						[% if summary_res.delivery_schedules_fields.delivery_schedules.print and po_delivery_materials|length >= 1 %]
							<div id="purchase_ds_table">
								{% include "admin/print_template/po/document/common/po_template_delivery_schedule.html" %}
							</div>
						[% endif %]
					[% endif %]
				[% endif %]
			</div>

			<div id="terms" >
				<div class="row">
			        <div class="col-sm-12" style="line-height:1.5">
			            [% if source.type != 2 %]
							<h6>Terms and Conditions</h6>
							<hr style="margin:0;border: 1px solid #666666; width:20%" />
							[% if header_res.terms_conditions.payment_terms.print %]
								<div class="pdf_payment_term for-non-pp">
									<span class="payment_term pdf_payment_term_txt" style="font-weight: normal;word-break:break-all;">{[ header_res.terms_conditions.payment_terms.label ]}</span>
									<span class="payment_details">: {[ payment_terms ]}</span><br /></span>

								</div>
							[% endif %]
						[% endif %]
			            <div class=" for-non-pp">
			                <span class="payment_term"  style="font-size:12px;">Delivery Due on </span>
			                <span class="payment_details"  style="font-size:12px;">:  {[ po_delivery ]}</span>
			            </div>
			            [% if header_res.terms_conditions.special_instructions.print %]
							[% if source.closing_remarks %]
								<div class="po_special_instruction">
									<span class="payment_term po_special_instruction_txt" >{[ header_res.terms_conditions.special_instructions.label ]}</span>
									<span class="payment_details">:  {[ source.closing_remarks ]}</span>
								</div>
							[% endif %]
			            [% endif %]
			        </div>
			    </div>

			    <div class="notes_bottom for-non-pp">
			        <hr style="margin:0;border: 1px solid #666666;"/>
			    </div>
			    <div class="row for-non-pp">
			        <div class="col-sm-12" style="margin-bottom: 10px"><strong>Notes:</strong></div>
				    <div style="margin-left:15px !important; margin-top:0px !important" class="notes notes_section">
				    </div>
			    </div>

				<div class="notes_bottom for-non-pp">
					<hr style="margin:0;border: 1pt solid #666666; " />
				</div>

				<div class="row for-non-pp">
					<div class="col-sm-4 pdf_vendor_ack pdf_company_details_container" style="font-size:{[header_res.font_size]}px;">
			            <span class="vendoracknowledgement_to_text">Vendor Acknowledgement</span>
			        </div>
			        <div class="col-sm-4 pdf_bill_to_address pdf_company_details_container">
				        <span class="bill_to_text bold" style="border-bottom: none; padding-bottom: 3px;">BILL TO:-</span>
			            <div> <b>{[ source.enterprise.name ]}</b> <br>{[ source.enterprise.address_1 ]}, <br>{[ source.enterprise.address_2 ]} {[ source.enterprise.city ]},</div>
						<div>E-mail:{[ source.enterprise.email ]}</div>
			        </div>
					[% if source.shipping_name != "" and source.shipping_address != "" %]
					<div class="col-sm-4 pdf_ship_to_address pdf_company_details_container">
						<span class="ship_to_text bold" style="border-bottom: none; padding-bottom: 3px;">SHIP TO:-</span>
						<div><b> {[ source.shipping_name ]}</b><br> {[ source.shipping_address ]}</div>
					</div>
					[% endif %]
                </div>
			</div>
			[% if misc_res.print_summary_first_page and appendix_pages > 0 %]
				<div style="page-break-after: always"></div>
			[% endif %]
			[% if misc_res.print_summary_first_page and appendix_pages > 0 %]
				<div class="annexture_po">ANNEXURE TO PURCHASE ORDER {[ source.getCode ]} DATED {[ order_date ]}</div>
				<div class="col-sm-12" id="purchase_material_table">
					{% include "admin/print_template/po/document/common/po_template_item_table.html" %}
				</div>
				[% if source.type != 2 %]
					[% if summary_res.hsn_summary.print %]
						{% include "admin/print_template/po/document/common/po_template_hsn_summary.html" %}
					[% endif %]
					[% if po_delivery_materials|length >= 1 %]
						<div id="purchase_ds_table">
							{% include "admin/print_template/po/document/common/po_template_delivery_schedule.html" %}
						</div>
					[% endif %]
				[% endif %]
			[% endif %]
		</div>
	</div>
</body>