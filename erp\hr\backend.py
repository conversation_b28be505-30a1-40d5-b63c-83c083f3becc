"""
"""
import calendar
import datetime
import os
import re
import shutil
from smtplib import SMTPRecipientsRefused

import xlrd
from dateutil.relativedelta import relativedelta
from sqlalchemy import and_, func

from erp import helper
from erp.commons.backend import sendMail
from erp.dao import DataAccessObject
from erp.hr import logger
from erp.hr.document_compiler import PaySlipGenerator
from erp.models import EmployeeAttendanceLog, Employee, Department, EmployeeSalary, User, PayStructureItem, \
	EmployeePayStructureItem, Enterprise
from erp.template_tag_filters import toDOMId
from settings import TEMP_DIR
from util.api_util import response_code
from util.ftp_helper import FTPUtil
from util.helper import writeFile, getAbsolutePath

__author__ = 'nandha'


class HrDAO(DataAccessObject):
	"""

	Access db objects using alchemy ORM
	"""

	def getEmployee(self, enterprise_id=None, employee_code=None):
		"""

		:param enterprise_id:
		:param employee_code:
		:return:
		"""

		try:
			return self.db_session.query(Employee).filter(
				Employee.enterprise_id == enterprise_id, Employee.emp_code == employee_code).first()
		except:
			raise

	def getAllAttendanceLog(self, enterprise_id=None, since=None, till=None):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:return:
		"""
		try:
			logger.info("Fetching all attendance log for %s, %s - %s" % (enterprise_id, since, till))
			query = self.db_session.query(
				EmployeeAttendanceLog.description.label('description'), Employee.emp_name.label("name"),
				Employee.emp_id.label("id"), Employee.emp_code.label('code'), Department.name.label("department")).join(
				Employee,
				and_(Employee.enterprise_id == enterprise_id, Employee.emp_code == EmployeeAttendanceLog.employee_code)
			).outerjoin(Department, and_(Employee.department_id == Department.id)).filter(
				EmployeeAttendanceLog.enterprise_id == enterprise_id,
				EmployeeAttendanceLog.since == since, EmployeeAttendanceLog.till == till)
			return query.all()
		except:
			raise

	def getAttendanceLog(self, enterprise_id=None, since=None, till=None, employee_code=None):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:param employee_code:
		:return:
		"""
		try:
			logger.info(
				"Getting attendance log for %s-%s between %s-%s" % (enterprise_id, employee_code, since, till))
			return self.db_session.query(EmployeeAttendanceLog).filter(
				EmployeeAttendanceLog.employee_code == employee_code,
				EmployeeAttendanceLog.enterprise_id == enterprise_id,
				EmployeeAttendanceLog.since == since,
				EmployeeAttendanceLog.till == till).first()
		except:
			raise

	def getSalaryReport(self, enterprise_id=None, since=None, till=None, employee_code=None):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:param employee_code:
		:return:
		"""
		try:
			logger.info(
				"Getting salary report for %s-%s between %s-%s" % (enterprise_id, employee_code, since, till))
			return self.db_session.query(EmployeeSalary).filter(
				EmployeeSalary.employee_code == employee_code,
				EmployeeSalary.enterprise_id == enterprise_id,
				EmployeeSalary.since == since,
				EmployeeSalary.till == till).first()
		except:
			raise

	def getAllSalaryReport(self, enterprise_id=None, since=None, till=None):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:return:
		"""
		try:
			logger.info(
				"Getting salary report of all employees for %s between %s-%s" % (enterprise_id, since, till))
			return self.db_session.query(EmployeeSalary).filter(
				EmployeeSalary.enterprise_id == enterprise_id,
				EmployeeSalary.since == since,
				EmployeeSalary.till == till).all()
		except:
			raise

	def getDepartmentByName(self, enterprise_id=None, name=None, parent_id=None):
		"""

		:param enterprise_id:
		:param name:
		:param parent_id:
		:return:
		"""
		try:
			return self.db_session.query(Department).filter(
				Department.name == name, Department.parent_id == parent_id,
				Department.enterprise_id == enterprise_id).first()
		except:
			raise

	def getDepartmentById(self, enterprise_id=None, department_id=None):
		"""

		:param enterprise_id:
		:param department_id:
		:return:
		"""
		try:
			return self.db_session.query(Department).filter(
				Department.id == department_id, Department.enterprise_id == enterprise_id).first()
		except:
			raise

	def getPayStructureItems(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		try:
			return self.db_session.query(PayStructureItem).filter(
				PayStructureItem.enterprise_id == enterprise_id).order_by(PayStructureItem.type).all()
		except Exception as e:
			raise e


class HrService:
	"""

	"""

	def __init__(self):
		"""

		"""
		self.dao = HrDAO()

	@staticmethod
	def __generateTempFilename(enterprise_id=None, filename=None, include_timestamp=True):
		"""

		Generates unique filename by upload timestamp
		:param enterprise_id:
		:param filename:
		:param include_timestamp:
		:return:
		"""
		timestamp = ""
		if include_timestamp:
			timestamp = datetime.datetime.today().strftime("%Y%m%d_%H%M%S")
		return "/site_media/tmp/%sHR%s_%s" % (enterprise_id, timestamp, filename)

	def storeFileInTemp(self, enterprise_id=None, filename=None, base64data=None, include_timestamp=False):
		"""

		:param enterprise_id:
		:param filename:
		:param base64data:
		:param include_timestamp:
		:return:
		"""
		try:
			temp_file = self.__generateTempFilename(
				enterprise_id=enterprise_id, filename=filename, include_timestamp=include_timestamp)
			logger.info("Storing file... %s" % temp_file)
			if base64data.__contains__(","):
				base64data = base64data.split(',')[-1]
			decoded_data = base64data.decode('base64')
			writeFile(data=decoded_data, file_path_name=temp_file)
			return temp_file.split("/")[-1]
		except Exception as e:
			raise e

	def saveDepartment(self, enterprise_id=None, created_by=None, department_dict=None):
		"""

		:param enterprise_id:
		:param created_by:
		:param department_dict:{id, name, parent_id}
		:return:
		"""
		self.dao.db_session.begin(subtransactions=True)
		try:

			department = self.dao.getDepartmentById(
				enterprise_id=enterprise_id, department_id=department_dict['id'])
			name = department_dict['name']
			parent_id = department_dict['parent_id']
			parent_id = parent_id if parent_id and parent_id.isdigit() else None
			if not department:
				department = self.dao.getDepartmentByName(enterprise_id=enterprise_id, name=name, parent_id=parent_id)
			if department:
				response = "Department exists already"
			else:
				department = Department(
					enterprise_id=enterprise_id, name=name, parent_id=parent_id,
					created_by=created_by, created_on=datetime.datetime.today())
				self.dao.db_session.add(department)
				response = "Saved Department successfully!"
				logger.info("Department to be saved: %s" % department)

			self.dao.db_session.commit()
		except Exception as e:
			self.dao.db_session.rollback()
			raise e
		return response

	def importEmployees(self, enterprise_id=None, filename=None):
		"""

		Imports employee from spreadsheet with extension [xls, xlsx, ods]
		:param enterprise_id:
		:param filename:
		:return: String result
		"""
		self.dao.db_session.begin(subtransactions=True)
		try:
			logger.info("Importing employees for enterprise %s" % enterprise_id)
			xl_book = xlrd.open_workbook(filename)
			sheet = xl_book.sheet_by_index(0)

			# Defining index for text columns
			text_columns = {
				"emp_code": 1, "emp_name": 2, "last_name": 3, "email": 4, "employment_type": 7, "status": 8,
				"designation": 10, "place_of_work": 11, "gender": 15, "address1": 16, "address2": 17,
				"country": 18, "state": 19, "city": 20, "postal_code": 21, "father_name": 22, "mother_name": 23,
				"nationality": 25, "martial_status": 26, "esi_no": 28, "pf_no": 29,
				"account_type": 31, "ifsc_code": 32, "account_name": 33, "pan_number": 34, "blood_group": 36,
				"spouse_name": 37}

			# Defining index for number columns
			number_columns = {
				"phone_no": 5, "no_of_el": 12, "no_of_cl": 13, "contact_number": 24,
				"aadhar_number": 27, "account_number": 30}

			# Defining index for columns like id, enum, date and manipulated data
			other_columns = {"department_id": 6, "date_of_joining": 9, "date_of_birth": 14, "pay_structure_id": 35}

			# Defining mandatory columns
			mandatory_fields = (
				"emp_code", "emp_name", "phone_no", "department_id", "employment_type", "gender", "pay_structure_id")

			# Reading department id map
			departments = helper.populateDepartment(enterprise_id=enterprise_id, need_blank_first=False)
			departments_id_map = {}
			for department in departments:
				departments_id_map[department[1]] = department[0]

			# Reading pay structure id map
			paystructures = helper.populatePayStructure(enterprise_id=enterprise_id, need_blank_first=False)
			paystructure_id_map = {}
			for paystruct in paystructures:
				paystructure_id_map[paystruct[1]] = paystruct[0]

			logger.info("Reading employee records from XL...")
			inserted_count, updated_count = 0, 0
			skipped_rows = {}
			warning_rows = {}
			unique_employees = []
			for row_number in range(1, sheet.nrows):
				error_key = ""
				try:
					record = sheet.row(row_number)
					employee_dict = {}
					logger.debug("Raw record: %s" % record)
					# Reading text Columns
					for key in text_columns:
						value = record[text_columns[key]].value
						if value and value != "":
							if key == "emp_code":
								employee_dict[key] = str(value).strip().replace(".0", "")
							elif str(value).strip().replace(".0", "").isdigit():
								employee_dict[key] = str(int(value)).strip()
							else:
								employee_dict[key] = str(value).strip()
					if "emp_code" not in employee_dict:
						skipped_rows["%s" % row_number] = "Employment code should not be blank"
						continue
					# Error key (Row Number - Employee Code)
					error_key = "(%s - %s)" % (row_number, employee_dict['emp_code'])
					if "employment_type" in employee_dict and employee_dict[
						"employment_type"] not in helper.EMPLOYMENT_TYPES:
						skipped_rows[error_key] = "Employment type belongs to %s" % helper.EMPLOYMENT_TYPES
						continue

					if "status" in employee_dict and employee_dict["status"] not in helper.EMPLOYMENT_STATUS:
						skipped_rows[error_key] = "Employment status belongs to %s " % helper.EMPLOYMENT_STATUS
						continue

					# Reading number Columns
					for key in number_columns:
						value = record[number_columns[key]].value
						if value != "":
							value = int(value)
							if key not in ("contact_number", "phone_no") or len(str(value)) == 10:
								employee_dict[key] = value

					# Reading department
					department_name = record[other_columns["department_id"]].value
					if department_name != "" and department_name in departments_id_map:
						employee_dict["department_id"] = departments_id_map[department_name]

					# Reading Pay structure
					paystructure_name = record[other_columns["pay_structure_id"]].value
					if paystructure_name != "" and paystructure_name in paystructure_id_map:
						employee_dict["pay_structure_id"] = paystructure_id_map[paystructure_name]

					# Reading Date of joining
					try:
						doj = str(record[other_columns["date_of_joining"]].value)
						if doj != "":
							employee_dict["date_of_joining"] = datetime.datetime.strptime(doj, "%d-%m-%Y")
					except Exception as e:
						warning_rows[error_key] = "employee import DOJ date format error. %s" % e.message
						logger.debug("employee import DOJ date format error. %s" % e.message)

					# Reading Date of birth
					try:
						dob = str(record[other_columns["date_of_birth"]].value)
						if dob != "":
							employee_dict["date_of_birth"] = datetime.datetime.strptime(dob, "%d-%m-%Y")
					except Exception as e:
						warning_rows[error_key] = "employee import DOB date format error. %s" % e.message
						logger.debug("employee import DOB date format error. %s" % e.message)

					# Checking mandatory fields
					if "account_type" not in employee_dict:
						employee_dict["account_type"] = "-NA-"

					data_issue = []
					for key in mandatory_fields:
						if key not in employee_dict:
							data_issue.append(key)

					if "email" in employee_dict and not re.match(r'[^@]+@[^@]+\.[^@]+', employee_dict['email']):
						data_issue.append("Email id format mismatch. %s" % employee_dict['email'])

					# Validating employee records
					if len(data_issue) > 0:
						# Failed here
						skipped_rows[error_key] = data_issue
					else:
						# Added here
						employee = self.dao.db_session.query(Employee).filter(
							Employee.enterprise_id == enterprise_id,
							Employee.emp_code == employee_dict['emp_code']).update(employee_dict)
						if not employee:
							if employee_dict["emp_code"] not in unique_employees:
								unique_employees.append(employee_dict["emp_code"])
								employee = Employee(enterprise_id=enterprise_id)
								employee.__dict__.update(employee_dict)
								self.dao.db_session.add(employee)
								inserted_count = inserted_count + 1
							else:
								skipped_rows[error_key] = "Duplicate record to insert"
						else:
							updated_count = updated_count + 1
						logger.info("Importing %s" % employee)
				except IndexError as e:
					raise e
				except Exception as e:
					skipped_rows[error_key] = "Exception"
					logger.exception("Import failed for record number %s. %s" % (row_number, e.message))

			# Writing to db
			self.dao.db_session.commit()

			# Writing result if db writing is success else exception will be raised
			response = response_code.success()
			message = ""
			if inserted_count > 0:
				logger.info("Imported %s employee records" % inserted_count)
				message = "Inserted %s record(s).<BR/>" % inserted_count
			if updated_count > 0:
				message = "%sUpdated %s record(s).<BR/>" % (message, updated_count)

			if message == "":
				response["response_message"] = "Failure"
				message = "No employees imported.<BR/>"

			if len(skipped_rows) > 0:
				logger.info("Failed importing %s employee records" % len(skipped_rows))
				logger.debug("Failed employee records are %s" % skipped_rows)
				message = "%s Failed records are,<BR/>" % message
				for key in skipped_rows:
					message = "%s%s: %s<BR/>" % (message, key, skipped_rows[key])
			if len(warning_rows) > 0:
				logger.info("Warned importing %s employee records" % len(warning_rows))
				logger.debug("Warned employee records are %s" % warning_rows)
				message = "%s<BR/>Warning!<BR/>" % message
				for key in warning_rows:
					message = "%s%s: %s<BR/>" % (message, key, warning_rows[key])
			response['custom_message'] = message
			return response
		except IndexError as e:
			self.dao.db_session.rollback()
			logger.error("Invalid data %s" % e.message)
			response = response_code.failure()
			response['custom_message'] = "Invalid input data!"
			return response
		except Exception as e:
			self.dao.db_session.rollback()
			raise e

	def importEmployeePayStructure(self, enterprise_id=None, filename=None):
		"""

		Imports employee pay structure from spreadsheet with extension [xls, xlsx, ods]
		:param enterprise_id:
		:param filename:
		:return: String result
		"""
		self.dao.db_session.begin(subtransactions=True)
		try:
			logger.info("Importing employee pay structures for enterprise %s" % enterprise_id)
			xl_book = xlrd.open_workbook(filename)
			sheet = xl_book.sheet_by_index(0)

			text_columns = {"s_no": 0, "emp_code": 1, "emp_name": 2}

			logger.info("Reading pay structure from XL...")
			imported_number_of_records = 0
			skipped_rows = {}
			record = sheet.row(0)
			pay_structure_item_map = {}
			for item in self.dao.getPayStructureItems(enterprise_id=enterprise_id):
				pay_structure_item_map[item.description.upper().strip()] = item.type
			new_columns = []
			duplicate_columns = []
			valid_columns_count = 0
			for col_number in range(3, len(record)):
				heading = "%s" % str(record[col_number].value).upper()
				if heading not in pay_structure_item_map.keys():
					new_columns.append(heading.encode("utf-8"))
				elif heading in text_columns:
					duplicate_columns.append(heading.encode("utf-8"))
				else:
					valid_columns_count = valid_columns_count + 1
					text_columns[heading] = col_number
			response = response_code.failure()
			response["custom_message"] = ""
			if len(new_columns) > 0:
				response["new_columns"] = new_columns
				response["custom_message"] = """Found un profiled headings. 
					Please profile in one pay structure.<BR/>%s<BR/><BR/>""" % new_columns
			if len(duplicate_columns) > 0:
				response["duplicate_columns"] = duplicate_columns
				response["custom_message"] += "Found duplicate headings. Please correct.<BR/>%s<BR/><BR/>" % duplicate_columns
			if valid_columns_count < len(pay_structure_item_map.keys()):
				response["missed_columns"] = list(set(pay_structure_item_map.keys()) - set(text_columns.keys()))
				response["custom_message"] += "Some profiled headings are missed.<BR/>%s<BR/><BR/>" % response["missed_columns"]
			if response["custom_message"] != "":
				return response
			for row_number in range(1, sheet.nrows):
				error_key = ""
				try:
					record = sheet.row(row_number)
					logger.debug("Raw record: %s" % record)
					emp_code = str(record[1].value).strip().replace(".0", "")
					error_key = "(%s - %s)" % (row_number, emp_code)

					if not emp_code or emp_code == "":
						skipped_rows[error_key] = "Employment code should not be blank"
						continue
					employee = self.dao.db_session.query(Employee).filter(
						Employee.emp_code == emp_code, Employee.enterprise_id == enterprise_id).first()

					if employee is None:
						skipped_rows[error_key] = "Employee is not profiled for code %s" % emp_code
						continue
					pay_structure_items = []
					for heading in text_columns:
						index = text_columns[heading]
						if index in (0, 1, 2):
							continue
						value = record[index].value
						if str(value).strip().replace(".", "").isdigit():
							if float(value) > 0:
								pay_structure_items.append(EmployeePayStructureItem(
									employee_id=employee.emp_id, type=pay_structure_item_map[heading], description=heading,
									amount=float(value), enterprise_id=enterprise_id))
						else:
							skipped_rows[error_key] = "Pay value for column '%s' is not a number" % heading
					if error_key not in skipped_rows:
						imported_number_of_records = imported_number_of_records + 1
						employee.pay_structure_items = pay_structure_items
				except Exception as e:
					skipped_rows[error_key] = "Exception"
					logger.exception("Import failed for record number %s. %s" % (row_number, e.message))

			self.dao.db_session.commit()
			# Writing result if db writing is success else exception will be raised
			response = response_code.success()
			message = ""
			if imported_number_of_records > 0:
				message = "Imported %s record(s).<BR/>" % imported_number_of_records

			if message == "":
				response["response_message"] = "Failure"
				message = "No employees pay imported.<BR/>"

			if len(skipped_rows) > 0:
				response["response_message"] = "Failure"
				logger.info("Failed importing %s employee pay records" % len(skipped_rows))
				logger.debug("Failed employee pay records are %s" % skipped_rows)
				message = "%s Failed records are,<BR/>" % message
				for key in skipped_rows:
					message = "%s%s: %s<BR/>" % (message, key, skipped_rows[key])
			response['custom_message'] = message
			return response
		except IndexError as e:
			self.dao.db_session.rollback()
			logger.error("Invalid data %s" % e.message)
			response = response_code.failure()
			response['custom_message'] = "Invalid input data!"
			return response
		except Exception as e:
			self.dao.db_session.rollback()
			raise e

	def importMonthWiseAttendance(self, enterprise_id=None, filename=None, month=None, user_id=None):
		"""

		:param enterprise_id:
		:param filename:
		:param month: month_of_attendance
		:param user_id:
		:return:
		"""

		self.dao.db_session.begin(subtransactions=True)
		try:
			since = datetime.datetime.strptime(month, "%b, %Y")
			till = since + relativedelta(months=1, days=-1)
			number_of_week_offs = len([1 for i in calendar.monthcalendar(since.year, since.month) if i[6] != 0])
			month_days = calendar.monthrange(since.year, since.month)[1]
			logger.info("Importing manually formed month wise attendance for enterprise %s" % enterprise_id)
			xl_book = xlrd.open_workbook(filename)
			sheet = xl_book.sheet_by_index(0)
			columns = {"CODE": 1, "LP": 3, "WD": 4, "HD": 5, "CL": 6, "EL": 7, "CPL": 8, "LOP": 9, "TSD": 10}
			deductions = {"SALARY ADVANCE": 11, "MEDICLAIM": 12, "TDS": 13, "ESI": 14, "PF": 15}
			additional_columns = {"PREVIOUS BALANCE": 16, "UNPAID CHANGES": 17}
			logger.info("Reading XL with columns: %s" % sheet.row(0))

			inserted_count, updated_count = 0, 0
			skipped_rows = {}
			for row_number in range(1, sheet.nrows):
				record = sheet.row(row_number)
				employee_code = ""
				try:
					employee_code = str(record[columns["CODE"]].value).replace(".0", "").strip()
					employee = self.dao.getEmployee(employee_code=employee_code, enterprise_id=enterprise_id)
					if not employee:
						skipped_rows["%s" % row_number] = "Found invalid employee code! %s." % employee_code
						continue
					if len(employee.pay_structure_items) < 1:
						skipped_rows["%s" % row_number] = "Pay structure is not profiled!"
						continue
					attendance_description = {"WO": number_of_week_offs}
					# Updating attendance
					number_of_total_salary_days = 0
					number_of_working_days = 0
					number_of_leave_days = number_of_week_offs
					for key in columns:
						if key != "CODE":
							attendance_description[key] = float(record[columns[key]].value) if str(
								record[columns[key]].value).strip() != "" else 0
							if key == "TSD":
								number_of_total_salary_days += attendance_description[key]
							if key in ("WD", "CL", "EL", "CPL", "LOP"):
								number_of_working_days += attendance_description[key]
							if key == "HD":
								number_of_leave_days += attendance_description[key]
							if not 0 <= attendance_description[key] <= month_days:
								skipped_rows["%s" % row_number] = "Value must be between 0 - %s for column %s" % (
									month_days, key)
								break
					if "%s" % row_number in skipped_rows:
						continue
					if number_of_working_days + number_of_leave_days != month_days:
						skipped_rows["%s" % row_number] = "Sum of the days classified are not equal to the month days"
						continue
					if number_of_total_salary_days != number_of_working_days:
						skipped_rows["%s" % row_number] = "Total Salary days is not valid for Emp Code: %s." % employee_code
						continue
					attendance_log = self.dao.getAttendanceLog(
						enterprise_id=enterprise_id, employee_code=employee_code, since=since, till=till)
					if not attendance_log:
						attendance_log = EmployeeAttendanceLog(
							enterprise_id=enterprise_id, employee_code=employee_code, since=since, till=till)
						inserted_count = inserted_count + 1
					else:
						updated_count = updated_count + 1
					attendance_log.description = attendance_description
					attendance_log.employee = employee
					self.dao.db_session.add(attendance_log)

					# Updating Salary deductions from XL
					salary_description = {}
					for key in additional_columns:
						salary_description[key] = 0
						if str(record[additional_columns[key]].value).strip() != "":
							salary_description[key] = float(record[additional_columns[key]].value)
					salary_description['DEDUCTION'] = []
					for key in deductions:
						if str(record[deductions[key]].value).strip() != "":
							salary_description['DEDUCTION'].append({
								"DESCRIPTION": key, "VALUE": float(record[deductions[key]].value)})
					salary = self.dao.getSalaryReport(
						enterprise_id=enterprise_id, employee_code=employee_code, since=since, till=till)
					if not salary:
						salary = EmployeeSalary(
							enterprise_id=enterprise_id, employee_code=employee_code, since=since, till=till,
							created_by=user_id)
					salary.updated_by = user_id
					salary.updated_on = datetime.datetime.now()
					salary.description = salary_description
					salary.employee = employee
					# Updating salary earning
					salary.updateSalary(attendance=attendance_log)
					self.dao.db_session.add(salary)
				except IndexError as e:
					raise e
				except Exception as e:
					skipped_rows["%s" % row_number] = "Exception: Emp Code -> %s" % employee_code
					logger.exception("Failed uploading attendance record %s<br/>%s" % (record, e.message))
			self.dao.db_session.commit()
			# Writing result if db writing is success else exception will be raised
			response = response_code.success()
			message = ""
			if inserted_count > 0:
				logger.info("Imported %s attendance records" % inserted_count)
				message = "Inserted %s record(s).<BR/>" % inserted_count
			if updated_count > 0:
				logger.info("Updated %s attendance records" % updated_count)
				message = "%sUpdated %s record(s).<BR/>" % (message, updated_count)

			if message == "":
				response["response_message"] = "Failure"
				message = "No attendance record is imported.<BR/>"

			if len(skipped_rows) > 0:
				response["response_message"] = "Failure"
				logger.info("Failed importing %s attendance records" % len(skipped_rows))
				logger.debug("Failed attendance records are %s" % skipped_rows)
				message = "%s Failed records are,<BR/>" % message
				for key in skipped_rows:
					message = "%s%s: %s<BR/>" % (message, key, skipped_rows[key])
			response['custom_message'] = message
			return response
		except IndexError as e:
			self.dao.db_session.rollback()
			logger.error("Invalid data %s" % e.message)
			response = response_code.failure()
			response['custom_message'] = "Invalid input data!"
			return response
		except Exception as e:
			self.dao.db_session.rollback()
			raise e

	def getEmployeeReport(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		try:
			query = self.dao.db_session.query(
				Employee.emp_code, Employee.emp_name, Employee.employment_type, Employee.date_of_joining,
				Department.name.label("department_name"),
				func.ifnull(Employee.designation, "").label("designation"),
				func.ifnull(Employee.phone_no, "").label("phone_no"),
				func.ifnull(Employee.email, "").label("email")).outerjoin(Employee.department).filter(
				Employee.enterprise_id == enterprise_id)
			return query.all()
		except:
			raise

	def getAttendanceReport(self, enterprise_id=None, month=None):
		"""

		:param enterprise_id:
		:param month:
		:return:
		"""
		try:
			since = datetime.datetime.strptime(month, "%b, %Y")
			till = since + relativedelta(months=1, days=-1)
			records = self.dao.getAllAttendanceLog(enterprise_id=enterprise_id, since=since, till=till)
			attendance_reports = []
			for log in records:
				attendance_log = log._asdict()
				attendance_log['month'] = month
				if attendance_log['department'] is None:
					attendance_log['department'] = ""
				attendance_reports.append(attendance_log)
			return attendance_reports
		except:
			raise

	def storeEmployeeProfilePic(self, enterprise_id=None, base64data=None, filename=None):
		"""

		:param enterprise_id:
		:param base64data:
		:param filename:
		:return:
		"""
		try:
			if not base64data or base64data == "":
				return None
			tmp_filename = self.storeFileInTemp(
				enterprise_id=enterprise_id, filename=filename, base64data=base64data, include_timestamp=False)
			FTPUtil().upload(file_path=enterprise_id, filename=tmp_filename, fp=open(
				"%s/%s" % (TEMP_DIR, tmp_filename)))
			return tmp_filename
		except Exception as e:
			logger.exception("FTP persisting failed for filename %s...<BR/>Error:%s" % (filename, e.message))
			return None

	def generatePaySlip(self, enterprise_id=None, month_year=None, employee_code=None, file_path="/site_media/tmp"):
		"""

		:param enterprise_id:
		:param month_year:
		:param employee_code:
		:param file_path:
		:return: filename, os_file_path
		"""
		try:
			since = datetime.datetime.strptime(month_year, "%b, %Y")
			till = since + relativedelta(months=1, days=-1)
			attendance_log = self.dao.getAttendanceLog(
				enterprise_id=enterprise_id, employee_code=employee_code, since=since, till=till)
			salary = self.dao.getSalaryReport(
				enterprise_id=enterprise_id, employee_code=employee_code, since=since, till=till)
			filename = 'pay_slip%s_%s.pdf' % (enterprise_id, employee_code.replace("/", "_"))
			# Constructing filename
			payslip_generator = PaySlipGenerator(double_copy=False, target_file_path="%s/%s" % (file_path, filename))

			if os.path.exists(payslip_generator.target_file_path):
				os.remove(payslip_generator.target_file_path)

			document_pdf = payslip_generator.generatePDF(source=(attendance_log, salary), filename=filename)
			writeFile(document_pdf, "%s/%s" % (file_path, filename))
			return filename, payslip_generator.target_file_path
		except Exception as e:
			raise e

	def generatePaySlips(self, enterprise_id=None, month_year=None, employee_codes=None):
		"""

		:param enterprise_id:
		:param month_year:
		:param employee_codes:
		:return: filename, os_file_path
		"""
		try:
			if len(employee_codes) == 1:
				return self.generatePaySlip(
					enterprise_id=enterprise_id, month_year=month_year, employee_code=employee_codes[0])
			else:
				zip_filename = "PaySlip-%s" % datetime.datetime.today().strftime("%Y%m%dT%H%M%S")
				root_dir = "/site_media/tmp/%s" % zip_filename
				absolute_path = getAbsolutePath(root_dir)
				if os.path.exists(absolute_path):
					os.removedirs(absolute_path)
				os.mkdir(absolute_path)
				for employee_code in employee_codes:
					self.generatePaySlip(
						enterprise_id=enterprise_id, month_year=month_year, employee_code=employee_code,
						file_path=root_dir)
				shutil.make_archive(base_name=absolute_path, format="zip", root_dir=absolute_path)
				shutil.rmtree(absolute_path)
				return "%s.zip" % zip_filename, "%s.zip" % absolute_path
		except Exception as e:
			logger.exception("Failed loading report... %s" % e.message)
			raise e

	def emailPaySlip(self, enterprise_id=None, month=None, employee_code=None, user_id=()):
		"""

		:param enterprise_id:
		:param month: Ex: Jan, 2020
		:param employee_code:
		:param user_id:
		:return:
		"""
		try:
			employee = self.dao.getEmployee(enterprise_id=enterprise_id, employee_code=employee_code)
			filename, absolute_path = self.generatePaySlip(
				enterprise_id=enterprise_id, month_year=month, employee_code=employee_code)
			if os.path.exists(absolute_path):
				user = self.dao.db_session.query(User).filter(User.id == user_id).first()
				enterprise = self.dao.db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
				if not employee.email or employee.email == "":
					raise SMTPRecipientsRefused("Email id is not registered!")
				message = """<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
					"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
					<html xmlns="http://www.w3.org/1999/xhtml">
					<body style="font-size: 12px; font-family: Arial">
					<p>{name},<BR/><p>Please find your Pay-slip for the month {month} attached</p>
					</p><p>Regards<br/>{user},<br/>{enterprise}</p></body></html>""".format(
					name=employee.__repr__(), month=month, user=user.__repr__(), enterprise=enterprise.name)
				status = sendMail(
					recipients=(employee.email,), reply_to=(user.email,), body=message,
					subject="Pay-slip for %s" % month, from_alias="%s" % user, from_addr=user.email,
					files=({"path": absolute_path, "name": "%s_%s.pdf" % (employee_code, toDOMId(month))},))
				os.remove(absolute_path)
				return status
		except Exception as e:
			logger.exception(e.message)
			raise e
		return False
