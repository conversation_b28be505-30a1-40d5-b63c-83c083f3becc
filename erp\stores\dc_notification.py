import os
import datetime
import logging
import pymysql
from erp.commons.backend import sendMail
from settings import PORT, HOST, USER, PASSWORD, DBNAME

formatted_date = datetime.datetime.today().strftime('%Y-%m-%d')

logging.basicConfig(
    filename='dc_returnable_status.log',
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filemode='w')


def dc_reminder_email(to_email, return_date, invoice_code, file_path):
    body = """
    Hi, <br>
    <br>
    hope this email finds you well.<br>
    This is a reminder that today (<b>Date : %s</b>) is the last date for returning the delivery challan (<b>No : %s</b>). Please check.<br>
    <br>
    Please find the attached DC PDF for your reference.<br>
    <br>
    Thank you.""" % (return_date, invoice_code)

    _mail_body = """<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
    <html xmlns="http://www.w3.org/1999/xhtml">
        <body style="font-size:12px;font-family:Arial">
            <p>{body}</p>
        </body>
    </html>""".format(
        body=body)
    recipients = [to_email]
    subject = "Delivery Challan(%s)-Reminder" % invoice_code
    files = ({"name": 'DC(%s).pdf' % invoice_code, "path": file_path},)
    res = sendMail(recipients=recipients, body=body, subject=subject, cc_list=[], files=files)
    return res


def create_pdf_from_blob(blob_data, output_file_path):
    with open(output_file_path, 'wb') as pdf_file:
        pdf_file.write(blob_data)


def pdf_remove(file_path):
    os.remove(file_path)


def verify_prepare_and_approval_email(prepared_email, approved_email, return_date, invoice_code, file_path):

    if prepared_email == approved_email and dc_reminder_email(prepared_email, return_date, invoice_code,
                                                              file_path):
        pdf_remove(file_path)
    elif not prepared_email and not approved_email:
        pass
    elif not prepared_email and dc_reminder_email(approved_email, return_date, invoice_code, file_path):
        pdf_remove(file_path)

    elif not approved_email and dc_reminder_email(prepared_email, return_date, invoice_code, file_path):
        pdf_remove(file_path)

    elif dc_reminder_email(prepared_email, return_date, invoice_code, file_path) and dc_reminder_email(
            approved_email, return_date, invoice_code, file_path):
        pdf_remove(file_path)


def fetch_returnable_status(return_date):

    conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT)
    query = """
    SELECT DISTINCT
        i.invoice_code,
        i.id,
        prepared_by_user.email AS prepared_email,
        approved_by_user.email AS approved_email
    FROM
        invoice_materials im
    JOIN 
        invoice i ON im.invoice_id = i.id
    JOIN
        auth_user prepared_by_user ON i.prepared_by = prepared_by_user.id
    JOIN
        auth_user approved_by_user ON i.approved_by = approved_by_user.id
    WHERE 
        i.return_date = '2024-06-10' AND im.is_returnable = 1;
    """
    try:
        with conn.cursor() as cursor:
            cursor.execute(query, )
            result = cursor.fetchall()
            for row in result:
                invoice_code, invoice_id, prepared_email, approved_email = row
                print(invoice_code, invoice_id, prepared_email, approved_email)
                doc_query = """
                SELECT
                    document_pdf
                FROM
                    invoice_document
                WHERE
                    invoice_id = %s"""
                cursor.execute(doc_query, (invoice_id,))
                document_pdf = cursor.fetchone()
                if document_pdf is not None:
                    logging.info(" return_date : %s, invoice_code : %s, invoice_id : %s, prepared email : %s, "
                                 "approved email : %s",
                                 return_date, invoice_code, invoice_id, prepared_email, approved_email)

                    blob_data = document_pdf[0]
                    dc_invoice_code = invoice_code.replace('/', '_')
                    file_path = '/home/<USER>/Desktop/code/2.21.1/erp/stores/tem_pdf_copy/DC(%s).pdf' % dc_invoice_code
                    create_pdf_from_blob(blob_data, file_path)

                    verify_prepare_and_approval_email(prepared_email, approved_email, return_date, invoice_code,
                                                      file_path)
    except pymysql.MySQLError as e:
        logging.error("Error: unable to fetch data")
        logging.error(e)
    finally:
        conn.close()


fetch_returnable_status(formatted_date)
