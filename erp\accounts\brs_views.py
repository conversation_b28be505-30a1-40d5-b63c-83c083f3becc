import datetime
import json

import simplejson
from django.http import HttpResponse
from django.http import HttpResponseRedirect
from django.template.response import TemplateResponse
from sqlalchemy import and_, func

from erp import APPROVED_VOUCHER
from erp.accounts import logger
from erp.accounts.changelog import VoucherChangelog
from erp.auth import USER_IN_SESSION_KEY, ENTERPRISE_IN_SESSION_KEY, SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.forms import BankReconciliationForm, BRSSearchForm
from erp.formsets import BankReconciliationFormset
from erp.models import BankReconciliation, Ledger, Voucher, VoucherParticulars, AccountGroup, VoucherType
from erp.properties import MANAGE_BRS_TEMPLATE, MANAGE_BRS_URL, TEMPLATE_TITLE_KEY
from settings import SQLASession
from util import helper
from util.api_util import JsonU<PERSON>, response_code
from util.helper import getFinancialYear

__author__ = 'saravanan'


def manageBRS(request):
	"""
	Renders the main page to manage BRS.
	:param request:
	:return:
	"""
	logger.info('Inside Manage BRS... ')
	
	request_handler = RequestHandler(request)
	enterprise = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise
	from_date, to_date = JsonUtil.getDateRange(
		rh=request_handler, since_session_key="brs.since", till_session_key="brs.till")
	logger.info("Enterprise ID:%s" % enterprise.id)
	brs_form = BankReconciliationForm(initial={'enterprise_id': enterprise.id})
	brs_search_form = BRSSearchForm(initial={'enterprise_id': enterprise.id}, enterprise_id=enterprise.id)
	return TemplateResponse(template=MANAGE_BRS_TEMPLATE, request=request, context={
		'brs_search_form': brs_search_form, 'brs_form': brs_form, 'to_date': to_date.strftime("%Y-%m-%d"),
		'bank_reconciliation_formset': BankReconciliationFormset(initial=[], prefix='bank_reconciliation'),
		'from_date': from_date.strftime("%Y-%m-%d"), TEMPLATE_TITLE_KEY: "BRS"})


def editBRS(request):
	"""
	Renders the Voucher Form loaded with the details of the Voucher chosen for edit identified by the voucher_no

	:param request:
	:return:
	"""
	logger.info('Editing a BRS...')
	request_handler = RequestHandler(request)
	enterprise = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise

	from_date, to_date = JsonUtil.getDateRange(
		rh=request_handler, since_session_key="brs.since", till_session_key="brs.till")
	# fin_start_date = helper.getFYStartDate(fy_start_day=enterprise.fy_start_day)
	fin_start_date = from_date
	edit_ledger_id = request_handler.getPostData('ledger_id')
	already_reconciled = request_handler.getPostData('chk_reconciled')
	if already_reconciled == '1':
		already_reconciled = 1
	else:
		already_reconciled = 0
	if edit_ledger_id is None:
		edit_ledger_id = request_handler.getPostData('save_ledger_id')
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	# Todo Query has to be enhanced
	voucher_particulars = SQLASession().query(VoucherParticulars.voucher_id).join(
		VoucherParticulars.voucher).filter(
		VoucherParticulars.enterprise_id == enterprise_id, VoucherParticulars.ledger_id == edit_ledger_id,
		Voucher.voucher_date >= fin_start_date, Voucher.voucher_date <= to_date, Voucher.status > 0)
	voucher_ids = []
	for v in voucher_particulars:
		voucher_ids.append(v.voucher_id)
	voucher_particulars = SQLASession().query(
		VoucherParticulars.voucher_id, VoucherParticulars.ledger_id, VoucherParticulars.is_debit,
		VoucherParticulars.enterprise_id, func.sum(VoucherParticulars.amount).label("amount"),
		Ledger.name.label("ledger_label"),
		AccountGroup.name.label("ledger_group"), BankReconciliation.bank_reconciliation_date, Voucher.voucher_date,
		Voucher.transaction_instrument_no.label("instrument_no"), Voucher.voucher_no.label("voucher_code"),
		Voucher.narration, Voucher.financial_year, VoucherType.code, Voucher.sub_number
	).outerjoin(
		VoucherParticulars.voucher
	).outerjoin(Voucher.type).outerjoin(
		VoucherParticulars.ledger
	).outerjoin(Ledger.group).outerjoin(
		BankReconciliation, and_(
			BankReconciliation.voucher_id == VoucherParticulars.voucher_id,
			BankReconciliation.ledger_id == VoucherParticulars.ledger_id)
	).filter(
		VoucherParticulars.enterprise_id == enterprise_id, VoucherParticulars.ledger_id != edit_ledger_id,
		VoucherParticulars.voucher_id.in_(voucher_ids),
		Voucher.voucher_date >= fin_start_date, Voucher.voucher_date <= to_date).group_by(
		VoucherParticulars.voucher_id, VoucherParticulars.ledger_id, VoucherParticulars.is_debit)

	bank_reconciliation_forms = []
	for particular in voucher_particulars:
		initializer = particular._asdict()
		initializer['voucher_no'] = Voucher.generateCode(
			voucher_id=particular.voucher_id, voucher_no=particular.voucher_code,
			financial_year=particular.financial_year, voucher_type=particular.code, sub_number=particular.sub_number)
		initializer['amount'] = float(particular.amount)
		initializer['voucher_date'] = particular.voucher_date.strftime("%b %d, %Y")
		initializer['instrument_no'] = particular.instrument_no
		if already_reconciled == 1:
			if particular.bank_reconciliation_date:
				initializer['bank_reconciliation_date'] = particular.bank_reconciliation_date.strftime("%b %d, %Y")
			else:
				initializer['bank_reconciliation_date'] = ""
			bank_reconciliation_forms.append(initializer)
		else:
			if particular.bank_reconciliation_date:
				initializer['bank_reconciliation_date'] = particular.bank_reconciliation_date.strftime("%b %d, %Y")
			else:
				initializer['bank_reconciliation_date'] = ""
				bank_reconciliation_forms.append(initializer)
	bank_reconciliation_formset = BankReconciliationFormset(
		initial=bank_reconciliation_forms, prefix='bank_reconciliation')
	brs_search_form = BRSSearchForm(enterprise_id=enterprise.id)
	ledger = SQLASession().query(Ledger).filter(
		Ledger.enterprise_id == enterprise_id, Ledger.id == edit_ledger_id).first()
	ledger_opening = ledger.getOpeningBalance(as_on=to_date)
	if ledger_opening >= 0:
		opening_type = "Dr"
	else:
		opening_type = "Cr"
		ledger_opening = ledger_opening * -1

	logger.info('Readying template for rendering...')
	return TemplateResponse(template=MANAGE_BRS_TEMPLATE, request=request, context={
		'bank_reconciliation_formset': bank_reconciliation_formset, 'brs_search_form': brs_search_form,
		'ledger_opening': ledger_opening, 'opening_type': opening_type, 'to_date': to_date.strftime("%Y-%m-%d"),
		'from_date': from_date.strftime("%Y-%m-%d"), 'save_ledger_id': edit_ledger_id,
		'already_reconciled': already_reconciled, TEMPLATE_TITLE_KEY: "Bank reconciliation Statement"})


def saveBRS(request):
	"""
	Save Ledger
	Persists them in appropriate tables after validation.

	:param request:
	:return:
	"""
	logger.info('Inside Save BRS...')
	request_handler = RequestHandler(request)
	try:
		particulars_formset = BankReconciliationFormset(request_handler.getPostData(), prefix='bank_reconciliation')
		if particulars_formset.is_valid():
			db_session = SQLASession()
			db_session.begin(subtransactions=True)
			try:
				for particulars_form in particulars_formset:
					bank_reconciliation_date = particulars_form.cleaned_data['bank_reconciliation_date']
					voucher_id = particulars_form.cleaned_data['voucher_id']
					ledger_id = particulars_form.cleaned_data['ledger_id']
					if bank_reconciliation_date in (None, "") or voucher_id in (None, "", "undefined") or \
						ledger_id in (None, "", "undefined"):
						continue
					logger.info('Voucher Particular Voucher ID: %s Ledger ID: %s ' % (
						particulars_form.cleaned_data['voucher_id'], particulars_form.cleaned_data['ledger_id']))
					brs_particular = db_session.query(BankReconciliation).filter(
						BankReconciliation.voucher_id == particulars_form.cleaned_data['voucher_id'],
						BankReconciliation.enterprise_id == particulars_form.cleaned_data['enterprise_id'],
						BankReconciliation.ledger_id == particulars_form.cleaned_data['ledger_id']).first()
					if brs_particular is None:
						brs_particular = BankReconciliation(
							voucher_id=particulars_form.cleaned_data['voucher_id'],
							ledger_id=particulars_form.cleaned_data['ledger_id'],
							enterprise_id=particulars_form.cleaned_data['enterprise_id'])
					brs_particular.bank_reconciliation_date = bank_reconciliation_date
					db_session.add(brs_particular)
					voucher_to_update = db_session.query(Voucher).filter(
						Voucher.id == voucher_id, Voucher.enterprise_id == particulars_form.cleaned_data['enterprise_id']).first()
					if voucher_to_update:
						voucher_to_update.transaction_instrument_no = particulars_form.cleaned_data['instrument_no']
						db_session.add(voucher_to_update)
					logger.debug(db_session.dirty)
				db_session.commit()
			except:
				db_session.rollback()
				raise
		else:
			logger.info('Save BRS Form validation failed...\n%s' % particulars_formset.errors)
		return editBRS(request=request)
	except Exception as e:
		logger.exception('Saving BRS Form Failed... %s' % e.message)
		return HttpResponseRedirect(MANAGE_BRS_URL)


def loadLedgerWiseBRS(request):

	request_handler = RequestHandler(request)

	from_date, to_date = JsonUtil.getDateRange(
		rh=request_handler, since_session_key="brs.since", till_session_key="brs.till")
	fin_start_date = from_date

	edit_ledger_id = request_handler.getPostData('ledger_id')
	if edit_ledger_id is None:
		edit_ledger_id = request_handler.getPostData('save_ledger_id')
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	# Todo Query has to be enhanced
	voucher_particulars = SQLASession().query(VoucherParticulars.voucher_id).join(
		VoucherParticulars.voucher).filter(
		VoucherParticulars.enterprise_id == enterprise_id, VoucherParticulars.ledger_id == edit_ledger_id,
		Voucher.voucher_date >= fin_start_date, Voucher.voucher_date <= to_date, Voucher.status > 0)
	voucher_ids = []
	for v in voucher_particulars:
		voucher_ids.append(v.voucher_id)
	voucher_particulars = SQLASession().query(
		VoucherParticulars.voucher_id, VoucherParticulars.ledger_id, VoucherParticulars.is_debit,
		VoucherParticulars.enterprise_id, func.sum(VoucherParticulars.amount).label("amount"),
		Ledger.name.label("ledger_label"),
		AccountGroup.name.label("ledger_group"), BankReconciliation.bank_reconciliation_date, Voucher.voucher_date,
		Voucher.transaction_instrument_no.label("instrument_no"), Voucher.voucher_no.label("voucher_code"),
		Voucher.narration, Voucher.financial_year, VoucherType.code, Voucher.sub_number
	).outerjoin(
		VoucherParticulars.voucher
	).outerjoin(Voucher.type).outerjoin(
		VoucherParticulars.ledger
	).outerjoin(Ledger.group).outerjoin(
		BankReconciliation, and_(
			BankReconciliation.voucher_id == VoucherParticulars.voucher_id,
			BankReconciliation.ledger_id == VoucherParticulars.ledger_id)
	).filter(
		VoucherParticulars.enterprise_id == enterprise_id, VoucherParticulars.ledger_id != edit_ledger_id,
		VoucherParticulars.voucher_id.in_(voucher_ids),
		Voucher.voucher_date >= fin_start_date, Voucher.voucher_date <= to_date).group_by(
		VoucherParticulars.voucher_id, VoucherParticulars.ledger_id, VoucherParticulars.is_debit)
	bank_reconciliation_forms = []
	for particular in voucher_particulars:
		initializer = particular._asdict()
		initializer['voucher_no'] = Voucher.generateCode(
			voucher_id=particular.voucher_id, voucher_no=particular.voucher_code,
			financial_year=particular.financial_year, voucher_type=particular.code, sub_number=particular.sub_number)
		initializer['amount'] = float(particular.amount)
		initializer['voucher_date'] = particular.voucher_date.strftime("%b %d, %Y")
		initializer['is_matched'] = False
		initializer['enterprise_id'] = enterprise_id
		if particular.bank_reconciliation_date:
			initializer['bank_reconciliation_date'] = particular.bank_reconciliation_date.strftime("%b %d, %Y")
		else:
			initializer['bank_reconciliation_date'] = ""
		bank_reconciliation_forms.append(initializer)
	response = response_code.success()
	response['voucherParticulars'] = bank_reconciliation_forms
	return HttpResponse(content=json.dumps(response), mimetype='application/json')


def LedgerWiseOpening(request):
	try:
		request_handler = RequestHandler(request)
		enterprise = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise
		edit_ledger_id = request_handler.getPostData('ledger_id')
		to_date = datetime.datetime.now()
		if edit_ledger_id is None:
			edit_ledger_id = request_handler.getPostData('save_ledger_id')
		ledger = SQLASession().query(Ledger).filter(
			Ledger.enterprise_id == enterprise.id, Ledger.id == edit_ledger_id).first()
		ledger_opening = ledger.getOpeningBalance(as_on=to_date)
		if ledger_opening >= 0:
			opening_type = "Dr"
		else:
			opening_type = "Cr"
			ledger_opening = ledger_opening * -1

		ledger_opening_values = []
		ledger_opening_dict = dict()
		ledger_opening_dict["opening_type"] = opening_type
		ledger_opening_dict["opening"] = ledger_opening
		ledger_opening_values.append(ledger_opening_dict)
	except Exception as e:
		logger.exception(e)
		ledger_opening_values = []
	logger.debug("Ledger Opening - %s" % ledger_opening_values)
	response = HttpResponse(content=simplejson.dumps(ledger_opening_values), mimetype='application/json')
	return response


def SaveVoucherBRS(request):

	request_handler = RequestHandler(request)
	enterprise = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise
	data = simplejson.loads(simplejson.dumps(request_handler.getPostData()))
	voucher_id = data["voucher_id"]
	logger.info("Voucher ID:%s" % voucher_id)
	db_session = SQLASession()
	db_session.begin(subtransactions=True)
	try:
		voucher_under_edit = SQLASession().query(Voucher).filter(
			Voucher.id == voucher_id, Voucher.enterprise_id == enterprise.id).first()
		if voucher_under_edit:
			logger.info("Instrument No:%s" % data["instrument_no"])
			voucher_under_edit.transaction_instrument_no = data["instrument_no"]
			db_session.add(voucher_under_edit)
			logger.debug(db_session.dirty)
			db_session.commit()

			return HttpResponse(simplejson.dumps("Saved Successfully"))
	except Exception as e:
		db_session.rollback()
		logger.exception(e)
		return HttpResponse(simplejson.dumps("Save Failed"))


def CreateVoucherBRS(request):
	"""
	Save Voucher
	Persists them in appropriate tables after validation.

	:param request:
	:return:
	"""
	voucher_particulars = []
	request_handler = RequestHandler(request)
	data = simplejson.loads(simplejson.dumps(request_handler.getPostData()))
	bank_ledger_id = data["bank_ledger_id"]
	ledger_id = data["ledger_id"]
	is_debit = data["is_debit"]
	amount = data["amount"]
	voucher_date = data["bank_reconciliation_date"]
	instrument_no = data["instrument_no"]
	narration = "Bank Voucher Created.."
	if int(is_debit) == 1:
		bank_entry_is_debit = 0
		other_entry_is_debit = 1
	else:
		bank_entry_is_debit = 1
		other_entry_is_debit = 0
	db_session = SQLASession()
	db_session.begin(subtransactions=True)
	try:
		enterprise = request_handler.getSessionAttribute(ENTERPRISE_IN_SESSION_KEY)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		voucher_particulars.append(VoucherParticulars(
			item_no=1, ledger_id=ledger_id, is_debit=other_entry_is_debit, amount=round(float(amount), 2),
			enterprise_id=enterprise.id))
		voucher_particulars.append(VoucherParticulars(
			item_no=2, ledger_id=bank_ledger_id, is_debit=bank_entry_is_debit, amount=round(float(amount), 2),
			enterprise_id=enterprise.id))
		voucher_to_be_saved = Voucher(
			type_id=3, voucher_no=0, voucher_date=voucher_date, narration=narration, transaction_instrument_no=instrument_no,
			enterprise_id=enterprise.id, created_by=user_id, status=1)
		time_now = datetime.datetime.today()
		if not voucher_to_be_saved.created_by:
			voucher_to_be_saved.created_on = time_now.strftime('%Y-%m-%d %H:%M:%S')
			voucher_to_be_saved.created_by = request_handler.getSessionAttribute(SESSION_KEY)
		logger.info("Voucher no: %s | Voucher Status: %s | Approval: %s" % (
			voucher_to_be_saved.voucher_no, voucher_to_be_saved.status, (
				voucher_to_be_saved.voucher_no is None or int(
					voucher_to_be_saved.voucher_no) == 0 or voucher_to_be_saved.voucher_no == '') and (
				voucher_to_be_saved.status == APPROVED_VOUCHER)))

		current_fy = getFinancialYear(
			for_date=datetime.datetime.strptime(voucher_date, '%Y-%m-%d'), fy_start_day=enterprise.fy_start_day)
		logger.debug("Financial Year '%s'" % current_fy)
		voucher_to_be_saved.financial_year = current_fy
		voucher_to_be_saved.status = 1
		if not voucher_to_be_saved.approved_by:
			voucher_to_be_saved.approved_on = time_now.strftime('%Y-%m-%d %H:%M:%S')
			voucher_to_be_saved.approved_by = request_handler.getSessionAttribute(SESSION_KEY)
		latest_vou_no = db_session.query(Voucher.voucher_no).filter(
			Voucher.financial_year == current_fy, Voucher.type_id == voucher_to_be_saved.type_id,
			Voucher.enterprise_id == enterprise.id, Voucher.voucher_no != '0').order_by(
			Voucher.voucher_no.desc()).first()
		logger.debug('Recent Vou No: %s' % latest_vou_no)
		voucher_to_be_saved.voucher_no = '%s' % (
			1 if not ('%s' % latest_vou_no).isdigit() or latest_vou_no == '0' else int(
				'%s' % latest_vou_no) + 1)
		voucher_to_be_saved.particulars = voucher_particulars
		db_session.add(voucher_to_be_saved)
		db_session.commit()
		VoucherChangelog().queryVoucherInsert(user_id=user_id, enterprise_id=enterprise.id, data=voucher_to_be_saved)
		return HttpResponse(simplejson.dumps(voucher_to_be_saved.getCode()))

	except Exception as e:
		db_session.rollback()
		logger.exception(e)
	return HttpResponse(simplejson.dumps("Voucher Creation Failed"))
