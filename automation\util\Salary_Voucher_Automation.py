import pandas as pd
from conf import *
from erp.dao import DataAccessObject
from erp.models import Voucher, VoucherParticulars, LedgerBill, LedgerBillSettlement



def get_latest_voucher_no(): # static
    query = "SELECT voucher_no FROM voucher where enterprise_id = %s ORDER BY voucher_no DESC LIMIT 1;" % enterprise_id
    result = executeQuery(query, as_dict=True)
    return result[0]['voucher_no'] + 1


class GeneralVouchersBulkUpload:
    def __init__(self, file_stored_path, login_employee_id, enterprise_id):
        self.file_path = file_stored_path
        self.login_employee = login_employee_id
        self.enterprise_id = enterprise_id
        self.date_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        self.logger = logging.getLogger(os.path.splitext(os.path.basename(__file__))[0])
        self.logger.setLevel(logging.DEBUG)
        self.logger.addHandler(logHandler)

        self.Debit = None
        self.Credit = None
        self.bill_code = None
        self.clean_df = None
        self.month = None
        self.Bill_Date = None
        self.invalid_projects_list = []
        self.invalid_ledgers_list = []
        self.invalid_ledgers_names = []
        self.invalid_columns_name = []
        self.empty_bill_code_date= []

        self.total = 0

    def process(self):
        column_names = ['S.No', 'Project Code', 'Net Salary']
        required_values = ['debit', 'credit', 'bill_code', 'month', 'bill_date']

        dc = self.read_file(self.file_path, sheet_name=None)

        for sheet_name, df in dc.items():

            col_values = [str(x).lower() for x in df.columns]
            missing_cols = [value for value in column_names if value.lower() not in col_values]

            if missing_cols:
                for value in missing_cols:
                    self.invalid_columns_name.append("sheet name: %s - Mismatch column name: %s" % (sheet_name, value))
                continue

            last_values = [str(x).lower() for x in df['S.No'].tail(5).values]
            missing_values = [value for value in required_values if value.lower() not in last_values]

            if missing_values:
                for value in missing_values:
                    self.invalid_ledgers_names.append("sheet name: %s - Missing required: %s" % (sheet_name, value))

            for project_code in df['Project Code'][:-6]:
                if self.check_projects_list(project_code) == 0:
                    self.invalid_projects_list.append("sheet name: %s - project code: %s" % (sheet_name, project_code))

            for index, row in df.tail(5).iterrows():
                s_no_value = row['S.No']

                if pd.isna(s_no_value):
                    continue

                s_no_lower = str(s_no_value).lower()
                project_code = row['Project Code']

                if s_no_lower in ['debit', 'credit']:
                    result = self.get_account_ledger_id(project_code)
                    if not result:
                        self.invalid_ledgers_list.append("sheet name: %s - ledger: %s" % (sheet_name, project_code))

                elif s_no_lower in ['month', 'bill_code', 'bill_date']:
                    if pd.isna(project_code):
                        self.empty_bill_code_date.append(
                            "sheet name: %s - %s: %s" % (sheet_name, s_no_lower.capitalize(), project_code))


        if (len(self.invalid_ledgers_list) == 0 and len(self.invalid_projects_list) == 0 and
                len(self.empty_bill_code_date) == 0 and len(self.invalid_ledgers_names) == 0 and
                len(self.invalid_columns_name) ==0):

            for sheet_name, df in dc.items():

                self.logger.info("Sheet name: '%s'", sheet_name)
                self.clean_df = df[column_names].dropna(subset=['S.No'])

                for index, row in self.clean_df.tail(5).iterrows():
                    if row['S.No'].lower() == 'debit':
                        self.Debit = row['Project Code']
                    elif row['S.No'].lower() == 'credit':
                        self.Credit = row['Project Code']
                    elif row['S.No'].lower() == 'month':
                        self.month = row['Project Code']
                    elif row['S.No'].lower() == 'bill_code':
                        self.bill_code = row['Project Code']
                    elif row['S.No'].lower() == 'bill_date':
                        self.Bill_Date = row['Project Code']
                    self.clean_df.drop(index, inplace=True)

                credit = self.get_account_ledger_id(self.Credit)
                debit = self.get_account_ledger_id(self.Debit)

                self.logger.info("Credit: %s", credit)
                self.logger.info("Debit: %s", debit)


                for _, row in self.clean_df.iterrows():
                    project_details = self.get_project_id(row["Project Code"])
                    if project_details:
                        project_id = project_details[0]['id']
                        project_name = project_details[0]['name']
                        salary = row['Net Salary']
                        voucher_no = get_latest_voucher_no()
                        bill_num = str(self.bill_code) + "/" + str(row["Project Code"])

                        self.logger.info("voucher: %s Sheet: %s - Processing project: %s (ID: %d) with salary: %f",
                                         voucher_no, sheet_name, project_name, project_id, salary)
                        self.total += int(salary)
                        self.voucher_creation(project_name, voucher_no, project_id, salary, bill_num)

                self.logger.info("Process completed for sheet: %s, and total Salary: %s\n", sheet_name, self.total)
                self.total = 0


        if self.invalid_columns_name:
            self.logger.error("Mismatch columns:\n%s", '\n'.join(map(str, self.invalid_columns_name)))
        if self.invalid_ledgers_names:
            self.logger.error(" Mismatch ledgers :\n%s", '\n'.join(map(str, self.invalid_ledgers_names)))
        if self.invalid_ledgers_list:
            self.logger.error("Invalid ledgers list:\n%s", '\n'.join(map(str, self.invalid_ledgers_list)))
        if self.invalid_projects_list:
            self.logger.error("Invalid project list:\n%s", '\n'.join(map(str, self.invalid_projects_list)))
        if self.empty_bill_code_date:
            self.logger.error("empty bill  list:\n%s" % '\n'.join(map(str, self.empty_bill_code_date)))

    def read_file(self, file_path, **kwargs):
        _, file_extension = os.path.splitext(file_path)
        if file_extension.lower() == '.xlsx':
            return pd.read_excel(file_path, **kwargs)
        elif file_extension.lower() == '.csv':
            return pd.read_csv(file_path, **kwargs)
        else:
            self.logger.error("Unsupported file format: %s Please use .xlsx or .csv files." % file_extension)


    def check_projects_list(self, project_code):
        query = "SELECT EXISTS (SELECT 1 FROM projects WHERE code = '%s' AND enterprise_id = %s) AS project_exists;" % (project_code, self.enterprise_id)
        result = executeQuery(query, as_dict=True)
        return result[0]["project_exists"] if result else False

    def get_project_id(self, project_code):
        query = "SELECT id, name  FROM projects WHERE code = '%s' AND enterprise_id = %s ;" % (project_code, self.enterprise_id)
        result = executeQuery(query, as_dict=True)
        return result

    def get_account_ledger_id(self,ledger_name):
        query = "SELECT id FROM account_ledgers WHERE name = '%s' AND enterprise_id = %s;" % (ledger_name, self.enterprise_id)
        result = executeQuery(query, as_dict=True)
        ledger_id = result[0]['id']if result and result[0]['id'] else False
        return  ledger_id

    def voucher_creation(self,project_name, voucher_no, project_id, salary, bill_num):
        dao = DataAccessObject()
        db_session = dao.db_session
        try:
            if not db_session.is_active:
                db_session.begin()
                db_session.begin(subtransactions=True)

            voucher = Voucher(
                enterprise_id=self.enterprise_id,
                voucher_date=self.Bill_Date,
                voucher_no=voucher_no,
                type_id=1,
                narration="%s of the month: %s and Project name: %s" % (self.Debit,self.month, project_name),
                created_by=self.login_employee,
                created_on=self.date_time,
                status=1,
                financial_year=getFinancialYear(),
                project_code=project_id,
                project_automation_status=0
            )
            db_session.add(voucher)
            db_session.commit()

            self.logger.info("Voucher created successfully project : %s with number: %d", project_name, voucher_no)
            self.voucher_particular_arg_creation(voucher, salary, bill_num)

        except Exception as e:
            self.logger.error("Error during voucher creation: %s", e)
            db_session.rollback()
        finally:
            db_session.close()

    def voucher_particular_arg_creation(self,voucher, salary,bill_num):

        ledger_id = self.get_account_ledger_id(self.Credit)
        self.voucher_particular(item_no=1, ledger_id=ledger_id, is_debit=False, salary=salary, voucher=voucher,bill_num=bill_num)

        ledger_id = self.get_account_ledger_id(self.Debit)
        self.voucher_particular(item_no=2, ledger_id=ledger_id, is_debit=True, salary=salary, voucher=voucher, bill_num=bill_num)

    def voucher_particular(self, item_no=None, ledger_id=None, is_debit=None, salary=None, voucher=None, bill_num=None):
        dao = DataAccessObject()
        db_session = dao.db_session

        try:
            db_session.begin(subtransactions=True)

            vp = VoucherParticulars(
                voucher_id=voucher.id,
                item_no=item_no,
                ledger_id=ledger_id,
                is_debit=is_debit,
                amount=int(salary),
                enterprise_id=self.enterprise_id
            )
            db_session.add(vp)
            db_session.commit()

            if is_debit is False:
                _lb = LedgerBill(
                    bill_no=bill_num,
                    bill_date=self.Bill_Date,
                    is_debit=is_debit,
                    ledger_id=ledger_id,
                    enterprise_id=self.enterprise_id,
                    voucher_id=voucher.id,
                    net_value=float(salary)
                )
                db_session.add(_lb)
                db_session.commit()

                db_session.begin(subtransactions=True)
                lbs = LedgerBillSettlement(
                    bill_id=_lb.id,
                    voucher_id=voucher.id,
                    dr_value=0.00,
                    cr_value=float(salary),
                    enterprise_id=self.enterprise_id
                )
                db_session.add(lbs)
                db_session.commit()
                self.logger.info("Successfully committed transaction for voucher ID: %d", voucher.id)

        except Exception as e:
            self.logger.error("Error during voucher particulars processing: %s", e)
            db_session.rollback()
            raise
        finally:
            db_session.close()
# Usage

file_path = '01-Salary Abstract - jul-24.xlsx'
login_employee = 51
enterprise_id = 102

processor = GeneralVouchersBulkUpload(file_stored_path=file_path, login_employee_id=login_employee,
                                    enterprise_id=enterprise_id)
processor.process()
