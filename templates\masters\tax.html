{% extends "masters/sidebar.html" %}
{% block tax %}
<title>{{ template_title }}</title>
<style>
	li.tax_side_menu a{
	outline: none;
	background-color: #e6983c !important;
	}
</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">

<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/tax.js?v={{ current_version }}"></script>

<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">Tax</span>
	</div>
	<div class="page-heading_new" style="padding: 0 30px;">
		<span class="page_header"></span>
		{% if access_level.edit %}
			<a data-toggle="tab" href="#tab2" class="btn btn-new-item pull-right create_tax" data-tooltip="tooltip" title="Add New Tax"><i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span></a>
		{% else %}
			<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in Profile Module. Please contact the administrator.">
				<a class="btn btn-new-item pull-right create_tax disabled"><i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span></a>
			</div>	
		{% endif %}	
		<a href="/erp/masters/tax/" class="btn btn-add-new pull-right view_tax hide" data-tooltip="tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
	</div>
	<div class="col-lg-12">
		<div class="content_bg">
			<div class="contant-container-nav hide">
				<ul class="nav nav-tabs list-inline">
					<li class="active"><a id="tab_view" href="/erp/masters/tax">VIEW</a></li>
					<li><a id="tab_add" data-toggle="tab" href="#tab2">ADD</a></li>
				</ul>
			</div>
			<div class="tab-content">
				<div id="tab2" class="tab-pane fade">
					<div class="add_table">
						<form action="/erp/masters/tax/save/#tab2" method="post" id="addTax" class="form-group">{% csrf_token %}
							<div class="col-lg-4 add_table_content">
								{% if taxForm.code.value != '' and taxForm.code.value != None %}
									<script type="text/javascript">$(".page_header").html('<span class="header_current_page"> {{ taxForm.name.value }}</span>'); $(".create_tax, .export_csv").addClass('hide');$(".view_tax").removeClass('hide');</script>

									<!--<h2>Edit Voucher </h2><div class="sub-heading-content">
									<span class="font-bold mycolor-grey-6">Voucher No: </span>
									<span class="mycolor-blue-2 font-bold myfont-number">{{ voucherForm.code.value }} </span></div>-->
								{% else %}
									<script type="text/javascript">$(".page-title").text('Tax');</script>
								{% endif %}
								<div>
									<div class="form-group">
										<label>Code<span class="mandatory_mark"> *</span></label>
										<!-- Render the field in non-editable for Edit and editable for Add -->
										{% if taxForm.code.value != None %}
										<input type="text" class="form-control" disabled
											   value="{{ taxForm.code.value }}" id="id_{{taxForm.prefix}}-code"
											   name="name_display"/>
										<!-- Disabled fields are not read in POST, hence the hidden field -->
										<input type="text" value="{{ taxForm.code.value }}"
											   id="id_hidden_code" class="" name="{{taxForm.prefix}}-code" hidden="hidden"/>
										{% else %}
										{{ taxForm.code }}
										{% endif %}
										{{ taxForm.enterprise_id}}
										<div class="duplicate_drawing" style="font-size: 12px; color: #dd4b39;  position: absolute; letter-spacing: 0.30px; line-height: 20px;"></div>
									</div>
									<div class="form-group">
										<label>Name<span class="mandatory_mark"> *</span></label>
										<!-- Render the field in non-editable for Edit and editable for Add -->
										{% if taxForm.name.value != None %}
										<input type="text" class="form-control" disabled
											   value="{{ taxForm.name.value }}" id="id_{{taxForm.prefix}}-name"
											   name="name_display"/>
										<!-- Disabled fields are not read in POST, hence the hidden field -->
										<input type="text" value="{{ taxForm.name.value }}"
											   id="id_hidden_name" name="{{taxForm.prefix}}-name" hidden="hidden"/>
										{% else %}
										{{ taxForm.name }}
										{% endif %}
									</div>

									<div class="form-group">
										<label>Type<span class="mandatory_mark"> *</span></label>
										{{ taxForm.type }}
									</div>

									<div class="checkbox remove-chk-padding">
									{{ taxForm.is_compound }}
									<label for="id_{{taxForm.prefix}}-is_compound">Compound</label>
									</div>
									<div class="form-group">
										<label>Base Rate (%)<span class="mandatory_mark"> *</span></label>
										<input id="id_{{taxForm.prefix}}-{{ taxForm.base_rate.name }}" maxLength="7"
											   type="text" onblur="calculateNetRate();" onfocus="setNumberRangeOnFocus(this,3,3)"
											   class="form-control" name="{{taxForm.prefix}}-{{ taxForm.base_rate.name }}"
											   value="{% if taxForm.base_rate.value != None %}{{ taxForm.base_rate.value }}{% else %}0.00{% endif %}"/>
									</div>
									<div class="form-group">
										<div id="assess_rate">
											<label>Assess Rate (Min.)</label>
											{{ taxForm.assess_rate }}
										</div>
									</div>
								</div>

								<div class="material_txt">
									{% if access_level.edit %}
									<a href="#" id="save_tax_button" class="btn btn-save">Save</a>
									<input type="submit" hidden="hidden" id="saveTax" value="Save"/>
									{% endif %}
								</div>
							</div>
							<div id ="id_sub_tax_form" class="col-lg-8 view_table_content hide"  style="padding-left: 30px">
								<div class="table-responsive full_width_txt">
									{{ sub_tax_formset.management_form }}

									<table class="table table-bordered custom-table table-striped" id="sub_taxes">
										<thead>
										<tr>
											<th hidden="hidden">Parent Code</th>
											<th width="70%"> Sub-tax Name</th>
											<th width="25%"> Rate (%)</th>
											<th>Delete</th>
										</tr>
										</thead>
										<tbody>
										<div id="form_set">
											{% for subtaxForm in sub_tax_formset.forms %}
											<tr id="{{ subtaxForm.prefix }}" align="center">
												<td hidden="hidden">
													{{ subtaxForm.parent_code}}{{ subtaxForm.DELETE }}
												</td>
												<td>{{ subtaxForm.name }}</td>
												<td><input
														id="id_{{ subtaxForm.prefix }}-{{ subtaxForm.rate.name }}"
														value="{{ subtaxForm.rate.value }}"
														type="text" placeholder="Rate" class="form-control" maxLength="7"
														onblur="calculateNetRate();" onfocus="setNumberRangeOnFocus(this,3,3)"
														name="{{ subtaxForm.prefix }}-{{ subtaxForm.rate.name }}"/>
												</td>
												<td><a href="#"
													   id="id_{{ subtaxForm.prefix }}-deleteSubTax"
													   onclick="javascript:deleteSubTax('{{ subtaxForm.prefix }}');">
														<i class="fa fa-trash-o"></i>
													</a>
												</td>
											</tr>
											{% endfor %}
										</div>
										<div id="new_forms"></div>
										<div id="empty_form">
											<tr bgcolor="#ECECEC" id="{{sub_tax_formset.empty_form.prefix}}" align="center">
												<td hidden="hidden">
													{{ sub_tax_formset.empty_form.DELETE }}
													{{ sub_tax_formset.empty_form.parent_code }}
													<input type="text" value="0" id="newFormCount"/>
												</td>
												<td>{{ sub_tax_formset.empty_form.name }}</td>
												<td>
													<input id="id_{{ sub_tax_formset.empty_form.prefix }}-{{ sub_tax_formset.empty_form.rate.name }}"
														   value="0.00"
														   type="text" placeholder="Rate"
														   class="form-control" maxLength="7"
														   onblur="calculateNetRate();" onfocus="setNumberRangeOnFocus(this,3,3)"
														   name="{{ sub_tax_formset.empty_form.prefix }}-{{ sub_tax_formset.empty_form.rate.name }}"/>
												</td>
												<td><a href="#"
													   id="id_{{ sub_tax_formset.empty_form.prefix }}-deleteSubTax"
													   onclick="javascript:deleteSubTax('{{ sub_tax_formset.empty_form.prefix }}');">
														<i class="fa fa-trash-o"></i>
													</a>
												</td>
											</tr>
										</div>
										<div>
											<tr>
												<td colspan="3" class="text-right">
													<a href="#" id="add_new_form" class="btn btn-save">
														<i class="fa fa-plus" aria-hidden="true"></i>
													</a>
												</td>
											</tr>
										</div>
										<div id="net_rate">
											<tr>
												<td align="right"><label>Net Rate (%)</label></td>
												<td>
													{{ taxForm.net_rate }}
													<input id="id_net_rate_dummy" type="text"
														   disabled="disabled"
														   class="form-control"
														   value="{{ taxForm.net_rate.value }}"
														   name="net_rate_dummy"/>
												</td>
												<td>&nbsp;</td>
											</tr>
										</div>
										</tbody>
									</table>
								</div>
							</div>
						</form>
						<div class="clearfix"></div>
					</div>
				</div>
				<div id="tab1" class="tab-pane fade in active">
					<div>
						<div class="col-lg-12">
							<div class="csv_export_button">
                                <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#taxList'), 'Tax_List.csv']);" data-tooltip="tooltip" title="Download Tax List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
                            </div>
							<table class="table table-bordered table-striped custom-table " id="taxList" style="width: 100%;">
								<thead>
									<tr>
										<th> S.No</th>
										<th> Code</th>
										<th> Type </th>
										<th> Name</th>
										<th> Net Rate (%)</th>
									</tr>
								</thead>
								<tbody>
								{% for tax in taxes %}
								<tr align="center">
									<td width="5%" align="center">{{forloop.counter}}.</td>
									<td width="30%" align="left">
										<span style="width: calc(100% - 140px); display: inline-block;">
	                                        <a class="edit_link_code" onclick="editTaxRow('{{ tax.code }}')">
												{{tax.code}}
											</a>
	                                    </span>    
	                                    <span style="width: 140px; display: inline-block; float: right; text-align: right;">
	                                        <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit' onclick="editTaxRow('{{ tax.code }}')" >
	                                            <i class='fa fa-pencil'></i>
	                                        </span>
	                                        <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit in New Tab' onclick="editTaxRow('{{ tax.code }}', '_blank')" >
	                                            <i class='fa fa-external-link'></i>
	                                        </span>
	                                        {% if access_level.delete %}
		                                        <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Delete' onclick="javascript:deleteTax('{{ tax.code }}', this);" data_file_name="{{ tax.name }}">
		                                            <i class='fa fa-trash-o' aria-hidden='true'></i>
		                                        </span>
		                                    {% endif %}    
	                                    </span>
									</td>
									<td width="10%" align="left">{{ tax.type }}</td>
									<td align="left">{{ tax.name }}</td>
									<td width="10%" align="right">{{ tax.net_rate }}</td>
								</tr>
								{% endfor %}
								{% if taxes|length == 0 %}
								
								{% endif %}
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<div class="hide">
	<form action="/erp/masters/tax/edit/#tab2" method="post" id="tax_edit_form">
		{% csrf_token %}
		<input type="hidden" id="edit_tax_id" name="code" value="" />
	</form>
	<form action="/erp/masters/tax/delete/" method="post" id="tax_delete_form">
		{% csrf_token %}
		<input type="hidden" id="delete_tax_id" name="deleteCode" value=""/>
	</form>
</div>
<!-- /#wrapper -->

<div id="error_messages" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Validation Errors</h4>
			</div>
			<div class="modal-body">
				<table cellpadding="2" cellspacing="2" border="0">
					<tr>
						<td colspan="3">
							<ul>
							{% for field in taxForm.hidden_fields %}
								{% for error in field.errors %}
									<li>{{field.label_tag}} : {{error}}</li>
								{% endfor %}
							{% endfor %}
							{% for field in taxForm.visible_fields %}
								{% for error in field.errors %}
									<li>{{field.label_tag}} : {{error}}</li>
								{% endfor %}
							{% endfor %}
							</ul>
						</td>
					</tr>
					<tr>
						<td colspan="4">
							<ul>
							{% for form in sub_tax_formset.initial_forms %}
								{% for field in form.hidden_fields %}
									{% for error in field.errors %}
										<li>Sub-Tax {{form.prefix}}:{{field.label_tag}}:{{error}}</li>
									{%endfor%}
								{% endfor %}
								{% for field in form.visible_fields %}
									{% for error in field.errors %}
										<li>Sub-Tax {{form.prefix}}:{{field.label_tag}}:{{error}}</li>
									{%endfor%}
								{% endfor %}
							{% endfor %}
							{% for form in sub_tax_formset.forms %}
								{% for field in form.hidden_fields %}
									{% for error in field.errors %}
										<li>{{form.prefix}}:{{field.label_tag}}:{{error}}</li>
									{%endfor%}
								{% endfor %}
								{% for field in form.visible_fields %}
									{% for error in field.errors %}
										<li>{{form.prefix}}:{{field.label_tag}}:{{error}}</li>
									{%endfor%}
								{% endfor %}
							{% endfor %}
							</ul>
						</td>
					</tr>
					<tr>
						<td>
							<input type="text" value='{{ taxForm.errors }}' id="form_errors" hidden="hidden"/>
							<input type="text" value='{{ sub_tax_formset.errors }}' id="formset_errors" hidden="hidden"/>
						</td>
					</tr>
				</table>
			</div>
		</div>
	</div>
</div>

<script>
	$(document).ready(function(){
		var url = window.location.href; 
		  if(url.indexOf('tab2') < 0) {
			TableHeaderFixed();
		  }
		NavTableRemove();
	});
	
	var oTable;
	var oSettings;
	function TableHeaderFixed() {
	   oTable = $('#taxList').DataTable({
			fixedHeader: false,
            "scrollY": Number($(document).height() - 230),
            "scrollX": true,
			"pageLength": 50,
			"search": {
				"smart": false
			}
		});
		oTable.on("draw",function() {
			var keyword = $('#taxList_filter > label:eq(0) > input').val();
			$('#taxList').unmark();
			$('#taxList').mark(keyword,{});
			setHeightForTable();
			listTableHoverIconsInit('taxList');
		});
		oTable.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		  listTableHoverIconsInit('taxList');
		});
		oSettings = oTable.settings();
		listTableHoverIconsInit('taxList');
		$( window ).resize();
	}
	
	function NavTableRemove(){
		$('ul.nav-tabs li a').click(function() {
			if($(this).attr('id') != "tab_view") {
				if($("#taxList").hasClass('dataTable')) {
					oTable.destroy();
				}
			}
		});
	}	

	$(".create_tax").click(function(){
		$(".create_tax, .export_csv").addClass('hide');
		$(".view_tax").removeClass('hide');
		$(".page-title").html('New Tax');	
		if($("#taxList").hasClass('dataTable')) {
			oTable.destroy();
		}
	});

	function editTaxRow(taxId, openTarget="") {
		$("#edit_tax_id").val(taxId);
		$("#tax_edit_form").attr("target", openTarget).submit();
	}
</script>
{% endblock %}