{% block bill_modals %}
	<script type="text/javascript" src="/site_media/js/bills.js?v={{ current_version }}"></script>
	<div id="DueDetailsByAging" class="modal fade" role="dialog">
		<div class="modal-dialog modal-lg" style="width: 100%; max-width: 1180px;">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">&times;</button>
					<h4 class="modal-title" id="DueDetailsTTitleByAging">Dues By Aging</h4>
					<span class="modal_download_link">
						<a role="button" class="btn btn-add-new pull-right a_consolidated_aging export_csv disable_download" onclick="GeneralExportTableToCSV.apply(this, [$('#consolidated_aging'), 'Consolidated_Aging_List.csv']);" data-tooltip="tooltip" title="" data-placement="bottom" data-original-title="Loading... Please Wait."><i class="fa fa-download" aria-hidden="true"></i></a>
					</span>
				</div>
				<div class="modal-body">
					<table id="consolidated_aging" class="table table-bordered table-striped custom-table"
				        style="font-size: 13px;">
						<thead class="DueByAgingLedgerTHead">
						<tr>
							<th>S. No</th>
							<th>Ledger</th>
						</tr>
						</thead>
						<div id='loadingmessage' class="text-center" style='display:none'>
							<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' style="width: 75px;" />
							<br />Please wait...
						</div>
						<tbody id="DueByAgingLedgerTBody">
						</tbody>
					</table>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
				</div>
			</div>
		</div>
	</div>

	<div id="DueDetails" class="modal fade" role="dialog" style="z-index: 10021;">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">&times;</button>
					<h4 class="modal-title" id="DueDetailsTTitle">Dues in 0 - 30 Days</h4>
				</div>
				<div class="modal-body">
					<table class="table table-bordered table-striped custom-table" style="font-size: 13px;" id="bills_table">
						<thead>
						<tr>
							<th>S. No</th>
							<th colspan="5">Ledger</th>
							<th>Due</th>
						</tr>
						</thead>
						<div id='loadingmessage_part' class="text-center" style='display:none'>
							<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' style="width: 75px;" />
							<br />Please wait...
						</div>
						<tbody id="ledgerTBody">
						</tbody>
					</table>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
				</div>
			</div>
		</div>
	</div>

	<div id="particulars_listing" class="modal fade" role="dialog" style="z-index: 10021;">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">&times;</button>
					<h4 class="modal-title" id="particulars_title">Advance</h4>
				</div>
				<div class="modal-body">
					<table class="table table-bordered table-striped custom-table hide" style="font-size: 13px;"
					       id="particulars_table">
						<thead id="particulars_thead">
						<tr>
							<th width="10%">S. No</th>
							<th width="25%">Voucher</th>
							<th width="15%">Date</th>
							<th>Narration</th>
							<th width="15%">Outstanding</th>
						</tr>
						</thead>
						<div id='loadingmessage' class="text-center hide">
							<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' style="width: 75px;" />
							<br/>Please wait...
						</div>
						<tbody id="particulars_tbody">
						</tbody>
					</table>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
				</div>
			</div>
		</div>
	</div>
{% endblock %}