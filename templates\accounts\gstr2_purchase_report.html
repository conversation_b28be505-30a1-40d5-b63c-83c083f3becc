{% extends "accounts/sidebar.html" %}
{% block gstr2 %}
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}" />
<link type="text/css" rel="stylesheet" href="/site_media/css/datatables.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/fixedHeader.bootstrap.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/dataTables.searchHighlight.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/tabs-vertical.css?v={{ current_version }}">

<script type="text/javascript" src="/site_media/js/chosen.jquery.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/daterangepicker.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/datatables.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/dataTables.fixedHeader.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/dataTables.searchHighlight.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/jquery.scrolling-tabs.js?v={{ current_version }}"></script>
<style type="text/css">
    .dataTables_scrollBody {
        overflow: auto hidden !important;
    }

    .dataTables_scrollBody table{
        margin-bottom: 20px;
    }
    .gstr_csv_export_button {
	    position: absolute;
	    right: 33px;
	    margin-top: -37px;
    }

    .table tfoot .report-total-text {
    	font-size: 20px !important;
    }

    .report-total-text:after {
    	content: '* includes all field';
    	color:  #dd4b39;
    	font-size: 12px;
    	display: block;
	}

	.table tfoot.gstr-report-foot td {
		font-size: 14px !important;
		font-weight: bold;
		text-align: right;
	}
</style>
<div class="right-content-container download-icon-onTop">
    <div class="page-title-container">
        <span class="page-title">GSTR-2 Purchase Report</span>
    </div>
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <div class="content_bg">
                    <ul class="resp-tabs-list hor_1"></ul>
                    <div class="resp-tabs-container hor_1">
                        <div class="row">
                            <div class="add_table">
                                <div class="col-lg-12 add_table_content">
                                    <div class="filter-components" style="width: 100%; margin-left: 0; margin-bottom: 0;">
                                    	<button class="btn btn-add-new pull-right gstr-xsl-download disabled" style="background: #FFF; color: #337ab7;" onclick="tablesToExcelLoad();" data-tooltip="tooltip" title="Download consoldated report (as XLS)"><i class="fa fa-download" aria-hidden="true"></i> &nbsp; as xls</button>
                                        <div class="filter-components-container">
                                            <div class="dropdown">
                                                <button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
                                                    <i class="fa fa-filter"></i>
                                                </button>
                                                <span class="dropdown-menu arrow_box arrow_box_filter">
                                                    <div class="col-sm-12 form-group" >
                                                        <label>Date Range</label>
                                                        <div id="reportrange" class="report-range form-control">
                                                            <i class="glyphicon glyphicon-calendar"></i>&nbsp;
                                                            <span></span> <b class="caret"></b>
                                                            <input type="hidden" class="fromdate" id="fromdate" name="fromdate"/>
                                                            <input type="hidden" class="todate" id="todate" name="todate"/>
                                                        </div>
                                                    </div>
                                                    <div class="filter-footer">
                                                        <button type="submit" class="btn btn-save" id="grnreportview">Apply</button>
                                                    </div>
                                                </span>
                                            </div>
                                            <span class='filtered-condition filtered-date'>Date: <b></b></span>
	                                        <span style="float:right">*IMPS, ITCR, AT & ATADJ are not captured in GSTR2 report for now. They will be available soon.</span>
                                        </div>
                                    </div>
                                    <ul class="nav nav-tabs" id="ul-report-header" role="tablist">
								      	<li role="presentation" class="active">
								      		<a href="#tab1" role="tab" data-toggle="tab">
								      			<span class="xs-vertical-tabs__header">B2B <!-- - <small class="b2b-total-amt">500000.00</small></span><br />
								           	 	<span class="xs-vertical-tabs__sub-header">
								           	 		CGST - <span class="b2b-cgst-total-amt">1000.00</span> / 
								           	 		SGST - <span class="b2b-sgst-total-amt">1000.00</span> / 
								           	 		IGST - <span class="b2b-igst-total-amt">500.00</span>
								           	 	</span> -->
								      		</a>
								      	</li>
								      	<li role="presentation">
								      		<a href="#tab2" role="tab" data-toggle="tab">
								      			<span class="xs-vertical-tabs__header">B2BUR <!-- - <small class="b2bur-total-amt">500000.00</small></span><br />
								           	 	<span class="xs-vertical-tabs__sub-header">
								           	 		CGST - <span class="b2bur-cgst-total-amt">1000.00</span> / 
								           	 		SGST - <span class="b2bur-sgst-total-amt">1000.00</span> / 
								           	 		IGST - <span class="b2bur-igst-total-amt">500.00</span>
								           	 	</span> -->
								      		</a>
								      	</li>
								      	<li role="presentation">
								      		<a href="#tab3" role="tab" data-toggle="tab">
								      			<span class="xs-vertical-tabs__header">HSN <!-- - <small class="hsn-total-amt">500000.00</small></span><br />
								           	 	<span class="xs-vertical-tabs__sub-header">
								           	 		CGST - <span class="hsn-cgst-total-amt">1000.00</span> / 
								           	 		SGST - <span class="hsn-sgst-total-amt">1000.00</span> / 
								           	 		IGST - <span class="hsn-igst-total-amt">500.00</span>
								           	 	</span> -->
								      		</a>
								      	</li>
	                                    <li role="presentation">
							      			<a href="#tab4" role="tab" data-toggle="tab">
							      				<span class="xs-vertical-tabs__header">CDNR <!-- - <small class="cdnr-total-amt">0.00</small></span><br />
								           	 	<span class="xs-vertical-tabs__sub-header">
								           	 		CGST - <span class="cdnr-cgst-total-amt">1000.00</span> /
								           	 		SGST - <span class="cdnr-sgst-total-amt">1000.00</span> /
								           	 		IGST - <span class="cdnr-igst-total-amt">500.00</span>
								           	 	</span> -->
							      			</a>
							      		</li>
							      		<li role="presentation">
							      			<a href="#tab5" role="tab" data-toggle="tab">
							      				<span class="xs-vertical-tabs__header">CDNUR <!--- <small class="cdnur-total-amt">500000.00</small></span><br />
								           	 	<span class="xs-vertical-tabs__sub-header">
								           	 		CGST - <span class="cdnur-cgst-total-amt">1000.00</span> /
								           	 		SGST - <span class="cdnur-sgst-total-amt">1000.00</span> /
								           	 		IGST - <span class="cdnur-igst-total-amt">500.00</span>
								           	 	</span> -->
							      			</a>
							      		</li>
								      	<li role="presentation">
								      		<a href="#tab6" role="tab" data-toggle="tab">
								      			<span class="xs-vertical-tabs__header">EXEMP <!-- - <small class="exemp-total-amt">500000.00</small></span><br />
								           	 	<span class="xs-vertical-tabs__sub-header">
								           	 		CGST - <span class="exemp-cgst-total-amt">1000.00</span> / 
								           	 		SGST - <span class="exemp-sgst-total-amt">1000.00</span> / 
								           	 		IGST - <span class="exemp-igst-total-amt">500.00</span>
								           	 	</span>-->
								      		</a>
								      	</li>
							      		<li role="presentation">
							      			<a href="#tab7" role="tab" data-toggle="tab">
							      				<span class="xs-vertical-tabs__header">IMPG <!-- - <small class="ipmg-total-amt">500000.00</small></span><br />
								           	 	<span class="xs-vertical-tabs__sub-header">
								           	 		CGST - <span class="ipmg-cgst-total-amt">1000.00</span> / 
								           	 		SGST - <span class="ipmg-sgst-total-amt">1000.00</span> / 
								           	 		IGST - <span class="ipmg-igst-total-amt">500.00</span>
								           	 	</span> -->
							      			</a>
							      		</li>
	                                    <li role="presentation">
							      			<a href="#tab8" role="tab" data-toggle="tab">
							      				<span class="xs-vertical-tabs__header">IMPS
							      			</a>
							      		</li>
								    </ul>
								    <div class="tab-content report-tab-content">
								      	<div role="tabpanel" class="tab-pane active" id="tab1">
							      			<div class="xs-vertical-tabs__title">B2B</div>
							   				<div class="gstr_csv_export_button">
                                            	<a role="button" class="btn btn-add-new pull-right export_csv gstr-b2b-download" disabled onclick="GeneralExportTableToCSVInit(this, [$('#gstr-b2b-report'), 'GSTR B2B Report.csv']);" data-tooltip="tooltip" title="Please wait... Your Report is not generated completely for download."><i class="fa fa-download" aria-hidden="true"></i></a>
                                        	</div>
									      	<table class="table table-bordered custom-table table-striped grn_tbl search_result_table" id="gstr-b2b-report" style="width: 100%;margin-right: 80px; display: inline-table;">
												<thead class="th-vertical-center">
													<tr class='total-header'>
														<th data-style="tableHeader" style="min-width: 80px;">S. NO</th>
														<th data-style="tableHeader" style="width: 80px;">GSTIN/UIN</th>
														<th data-style="tableHeader" style="width: 80px;">Supplier Name</th>
														<th data-style="tableHeader" style="width: 80px;">Invoice Number</th>
														<th data-style="tableHeader" style="width: 80px;">Invoice Date</th>
														<th data-style="tableHeader" style="width: 80px;">Invoice Value</th>
														<th data-style="tableHeader" style="width: 80px;">Voucher Number</th>
														<th data-style="tableHeader" style="width: 160px;">Place of Supply</th>
														<th data-style="tableHeader" style="width: 160px;">Reverse Charge</th>
														<th data-style="tableHeader" style="width: 160px;">Invoice Type</th>
														<th data-style="tableHeader" style="width: 160px;">Rate</th>
														<th data-style="tableHeader" style="width: 160px;">Taxable Value</th>
														<th data-style="tableHeader" style="width: 160px;">Integrated Tax Paid</th>
														<th data-style="tableHeader" style="width: 160px;">Central Tax Paid</th>
														<th data-style="tableHeader" style="width: 160px;">State/UT Tax Paid</th>
														<th data-style="tableHeader" style="width: 160px;">Cess Paid</th>
														<th data-style="tableHeader" style="width: 160px;">Eligibility for ITC</th>
														<th data-style="tableHeader" style="width: 160px;">Availed ITC Integrated Tax</th>
														<th data-style="tableHeader" style="width: 160px;">Availed ITC Central Tax</th>
														<th data-style="tableHeader" style="width: 160px;">Availed ITC State/UT Tax</th>
														<th data-style="tableHeader" style="width: 160px;">Availed ITC Cess</th>
													</tr>
												</thead>
												<tbody id="grn_b2b_tbl_tbody" class="gstr-report-body"></tbody>
												<tfoot id="grn_b2b_tbl_tfoot" class="gstr-report-foot"></tfoot>
											</table>
								      	</div>
								      	<div role="tabpanel" class="tab-pane" id="tab2">
								      		<div class="xs-vertical-tabs__title">B2BUR</div>
								   			<div class="gstr_csv_export_button">
                                            	<a role="button" class="btn btn-add-new pull-right export_csv gstr-b2bur-download" disabled onclick="GeneralExportTableToCSVInit(this, [$('#gstr-b2bur-report'), 'GSTR B2BUR Report.csv']);" data-tooltip="tooltip" title="Please wait... Your Report is not generated completely for download."><i class="fa fa-download" aria-hidden="true"></i></a>
                                        	</div>
											<table class="table table-bordered custom-table table-striped grn_tbl search_result_table" id="gstr-b2bur-report" style="width: 100%;margin-right: 80px; display: inline-table;">
												<thead class="th-vertical-center">
													<tr class='total-header'>
														<th data-style="tableHeader" style="min-width: 80px;">S. NO</th>
														<th data-style="tableHeader" style="width: 80px;">Supplier Name</th>
														<th data-style="tableHeader" style="width: 80px;">Invoice Number</th>
														<th data-style="tableHeader" style="width: 80px;">Invoice Date</th>
														<th data-style="tableHeader" style="width: 80px;">Invoice Value</th>
														<th data-style="tableHeader" style="width: 160px;">Place of Supply</th>
														<th data-style="tableHeader" style="width: 160px;">Supply Type</th>
														<th data-style="tableHeader" style="width: 160px;">Rate</th>
														<th data-style="tableHeader" style="width: 160px;">Taxable Value</th>
														<th data-style="tableHeader" style="width: 160px;">Integrated Tax Paid</th>
														<th data-style="tableHeader" style="width: 160px;">Central Tax Paid</th>
														<th data-style="tableHeader" style="width: 160px;">State/UT Tax Paid</th>
														<th data-style="tableHeader" style="width: 160px;">Cess Paid</th>
														<th data-style="tableHeader" style="width: 160px;">Eligibility for ITC</th>
														<th data-style="tableHeader" style="width: 160px;">Availed ITC Integrated Tax</th>
														<th data-style="tableHeader" style="width: 160px;">Availed ITC Central Tax</th>
														<th data-style="tableHeader" style="width: 160px;">Availed ITC State/UT Tax</th>
														<th data-style="tableHeader" style="width: 160px;">Availed ITC Cess</th>
													</tr>
												</thead>
												<tbody id="grn_b2burc_tbl_tbody" class="gstr-report-body"></tbody>
												<tfoot id="grn_b2burc_tbl_tfoot" class="gstr-report-foot"></tfoot>
											</table>
								      	</div>
								      	<div role="tabpanel" class="tab-pane" id="tab3">
								      		<div class="xs-vertical-tabs__title">HSN</div>
									      	<div class="gstr_csv_export_button">
	                                            <a role="button" class="btn btn-add-new pull-right export_csv gstr-hsn-download" disabled onclick="GeneralExportTableToCSVInit(this, [$('#gstr-hsn-report'), 'GSTR HSN Report.csv']);" data-tooltip="tooltip" title="Please wait... Your Report is not generated completely for download."><i class="fa fa-download" aria-hidden="true"></i></a>
	                                        </div>
											<table class="table table-bordered custom-table table-striped grn_tbl search_result_table" id="gstr-hsn-report" style="width: 100%;margin-right: 80px; display: inline-table;">
												<thead class="th-vertical-center">
                                                    <tr class='total-header'>
														<th data-style="tableHeader" style="min-width: 80px;">S. NO</th>
														<th data-style="tableHeader" style="min-width: 60px;">HSN </th>
														<th data-style="tableHeader" style="min-width: 60px;">Description</th>
														<th data-style="tableHeader" style="min-width: 60px;">UQC</th>
														<th data-style="tableHeader" style="min-width: 60px;">Total Quantity</th>
														<th data-style="tableHeader" style="min-width: 60px;">Total Value</th>
														<th data-style="tableHeader" style="min-width: 60px;">Taxable Value</th>
														<th data-style="tableHeader" style="min-width: 60px;">Integrated Tax Amount</th>
														<th data-style="tableHeader" style="min-width: 60px;">Central Tax Amount</th>
														<th data-style="tableHeader" style="min-width: 60px;">State/UT Tax Amount</th>
														<th data-style="tableHeader" style="min-width: 60px;">Cess Amount</th>
                                                    </tr>
												</thead>
												<tbody id="grn_hsn_tbl_tbody" class="gstr-report-body"></tbody>
												<tfoot id="grn_hsn_tbl_tfoot" class="gstr-report-foot"></tfoot>
											</table>
								      	</div>
									    <div role="tabpanel" class="tab-pane" id="tab4">
								      		<div class="xs-vertical-tabs__title">CDNR</div>
									      	<div class="gstr_csv_export_button">
	                                            <a role="button" class="btn btn-add-new pull-right export_csv gstr-cdnr-download" disabled onclick="GeneralExportTableToCSVInit(this, [$('#gstr-cdnr-report'), 'GSTR CDNR Report.csv']);" data-tooltip="tooltip" title="Please wait... Your Report is not generated completely for download."><i class="fa fa-download" aria-hidden="true"></i></a>
	                                        </div>
											<table class="table table-bordered custom-table table-striped search_result_table" id="gstr-cdnr-report" style="width: 100%;margin-right: 80px; display: inline-table;">
												<thead class="th-vertical-center">
												<tr class='total-header'>
													<th data-style="tableHeader" style="min-width: 80px;">S. NO</th>
													<th data-style="tableHeader" style="min-width: 60px;">GSTIN/UIN</th>
													<th data-style="tableHeader" style="min-width: 60px;">Note/Refund Voucher No</th>
													<th data-style="tableHeader" style="min-width: 60px;">Note/Refund Voucher Date</th>
													<th data-style="tableHeader" style="min-width: 60px;">Invoice/Advance Receipt No</th>
													<th data-style="tableHeader" style="min-width: 60px;">Invoice/Advance Receipt Date</th>
													<th data-style="tableHeader" style="min-width: 60px;">Pre GST</th>
													<th data-style="tableHeader" style="min-width: 60px;">Document Type</th>
													<th data-style="tableHeader" style="min-width: 60px;">Reason for issuing document</th>
													<th data-style="tableHeader" style="min-width: 60px;">Place of Supply </th>
													<th data-style="tableHeader" style="min-width: 60px;">Note/Refund Voucher Value</th>
													<th data-style="tableHeader" style="min-width: 60px;">Rate</th>
													<th data-style="tableHeader" style="min-width: 60px;">Taxable Value</th>
													<th data-style="tableHeader" style="min-width: 60px;">Integrated Tax Paid</th>
													<th data-style="tableHeader" style="min-width: 60px;">Central Tax Paid</th>
													<th data-style="tableHeader" style="min-width: 60px;">State/UT Tax</th>
													<th data-style="tableHeader" style="min-width: 60px;">Cess Paid</th>
													<th data-style="tableHeader" style="min-width: 60px;">Eligibility For ITC*</th>
													<th data-style="tableHeader" style="min-width: 60px;">Availed ITC Integrated Tax</th>
													<th data-style="tableHeader" style="min-width: 60px;">Availed ITC Central Tax</th>
													<th data-style="tableHeader" style="min-width: 60px;">Availed ITC State/UT Tax</th>
													<th data-style="tableHeader" style="min-width: 60px;">Availed ITC Cess</th>
												</tr>
												</thead>
												<tbody id="grn_cdnr_tbl_tbody" class="gstr-report-body"></tbody>
												<tfoot id="grn_cdnr_tbl_tfoot" class="gstr-report-foot"></tfoot>
											</table>
								      	</div>
								      	<div role="tabpanel" class="tab-pane" id="tab5">
								      		<div class="xs-vertical-tabs__title">CDNUR</div>
									      	<div class="gstr_csv_export_button">
	                                            <a role="button" class="btn btn-add-new pull-right export_csv gstr-cdnur-download" disabled onclick="GeneralExportTableToCSVInit(this, [$('#gstr-cdnur-report'), 'GSTR CDNUR Report.csv']);" data-tooltip="tooltip" title="Please wait... Your Report is not generated completely for download."><i class="fa fa-download" aria-hidden="true"></i></a>
	                                        </div>
											<table class="table table-bordered custom-table table-striped search_result_table" id="gstr-cdnur-report" style="width: 100%;margin-right: 80px; display: inline-table;">
												<thead class="th-vertical-center">
												<tr class='total-header'>
													<th data-style="tableHeader" style="min-width: 80px;">S. NO</th>
													<th data-style="tableHeader" style="min-width: 60px;">Note/Voucher Number*</th>
													<th data-style="tableHeader" style="min-width: 60px;">Note/ Voucher date*</th>
													<th data-style="tableHeader" style="min-width: 60px;">Invoice/Advance Payment Voucher number*</th>
													<th data-style="tableHeader" style="min-width: 60px;">Invoice/Advance Payment Voucher date*</th>
													<th data-style="tableHeader" style="min-width: 60px;">Pre GST</th>
													<th data-style="tableHeader" style="min-width: 60px;">Document Type*</th>
													<th data-style="tableHeader" style="min-width: 60px;">Reason For Issuing document*</th>
													<th data-style="tableHeader" style="min-width: 60px;">Supply Type</th>
													<th data-style="tableHeader" style="min-width: 60px;">Note/Refund Voucher value*</th>
													<th data-style="tableHeader" style="min-width: 60px;">Rate*</th>
													<th data-style="tableHeader" style="min-width: 60px;">Taxable value</th>
													<th data-style="tableHeader" style="min-width: 60px;">Integrated Tax Paid</th>
													<th data-style="tableHeader" style="min-width: 60px;">Central Tax Paid</th>
													<th data-style="tableHeader" style="min-width: 60px;">State/UT Tax</th>
													<th data-style="tableHeader" style="min-width: 60px;">Cess Paid</th>
													<th data-style="tableHeader" style="min-width: 60px;">Eligibility For ITC*</th>
													<th data-style="tableHeader" style="min-width: 60px;">Availed ITC Integrated Tax</th>
													<th data-style="tableHeader" style="min-width: 60px;">Availed ITC Central Tax</th>
													<th data-style="tableHeader" style="min-width: 60px;">Availed ITC State/UT Tax</th>
													<th data-style="tableHeader" style="min-width: 60px;">Availed ITC Cess</th>
												</tr>
												</thead>
												<tbody id="grn_cdnur_tbl_tbody" class="gstr-report-body"></tbody>
												<tfoot id="grn_cdnur_tbl_tfoot" class="gstr-report-foot"></tfoot>
											</table>
								      	</div>
								      	<div role="tabpanel" class="tab-pane" id="tab6">
								      		<div class="xs-vertical-tabs__title">EXEMP</div>
									      	<div class="gstr_csv_export_button">
	                                            <a role="button" class="btn btn-add-new pull-right export_csv gstr-exemp-download" disabled onclick="GeneralExportTableToCSVInit(this, [$('#gstr-exemp-report'), 'GSTR EXEMP Report.csv']);" data-tooltip="tooltip" title="Please wait... Your Report is not generated completely for download."><i class="fa fa-download" aria-hidden="true"></i></a>
	                                        </div>
											<table class="table table-bordered custom-table table-striped grn_tbl search_result_table" id="gstr-exemp-report" style="width: 100%;margin-right: 80px; display: inline-table;">
												<thead class="th-vertical-center">
                                                    <tr class='total-header'>
														<th data-style="tableHeader" style="min-width: 80px;">S. NO</th>
														<th data-style="tableHeader" >Description</th>
	                                                    <th data-style="tableHeader">Composition taxable person</th>
														<th data-style="tableHeader" >Nil Rated Supplies</th>
														<th data-style="tableHeader" >Exempted(other than nil rated/non GST supply)</th>
														<th data-style="tableHeader" >Non-GST Supplies</th>
                                                    </tr>
												</thead>
												<tbody id="grn_exemp_tbl_tbody" class="gstr-report-body"></tbody>
												<tfoot id="grn_exemp_tbl_tfoot" class="gstr-report-foot"></tfoot>
											</table>
								      	</div>
								      	<div role="tabpanel" class="tab-pane" id="tab7">
								      		<div class="xs-vertical-tabs__title">IMPG</div>
									      	<div class="gstr_csv_export_button">
	                                            <a role="button" class="btn btn-add-new pull-right export_csv gstr-impg-download" disabled onclick="GeneralExportTableToCSVInit(this, [$('#gstr-impg-report'), 'GSTR IMPG Report.csv']);" data-tooltip="tooltip" title="Please wait... Your Report is not generated completely for download."><i class="fa fa-download" aria-hidden="true"></i></a>
	                                        </div>
											<table class="table table-bordered custom-table table-striped grn_tbl search_result_table" id="gstr-impg-report" style="width: 100%;margin-right: 80px; display: inline-table;">
												<thead class="th-vertical-center">
                                                    <tr class='total-header'>
														<th data-style="tableHeader" style="min-width: 80px;">S. NO</th>
														<th data-style="tableHeader" style="min-width: 40px;">Port Code</th>
														<th data-style="tableHeader" style="min-width: 40px;">Bill Of Entry Number</th>
														<th data-style="tableHeader" style="min-width: 40px;">Bill Of Entry Date</th>
														<th data-style="tableHeader" style="min-width: 40px;">Bill Of Entry Value	</th>
														<th data-style="tableHeader" style="min-width: 40px;">Document type	</th>
														<th data-style="tableHeader" style="min-width: 40px;">GSTIN Of Supplier</th>
														<th data-style="tableHeader" style="min-width: 40px;">Rate	</th>
														<th data-style="tableHeader" style="min-width: 40px;">Taxable Value</th>
														<th data-style="tableHeader" style="min-width: 40px;">Integrated Tax Paid</th>
														<th data-style="tableHeader" style="min-width: 40px;">Cess Paid</th>
														<th data-style="tableHeader" style="min-width: 40px;">Eligibility For ITC</th>
														<th data-style="tableHeader" style="min-width: 40px;">Availed ITC Integrated Tax</th>
														<th data-style="tableHeader" style="min-width: 40px;">Availed ITC Cess</th>
                                                    </tr>
												</thead>
												<tbody id="grn_impg_tbl_tbody" class="gstr-report-body"></tbody>
												<tfoot id="grn_impg_tbl_tfoot" class="gstr-report-foot"></tfoot>
											</table>
								      	</div>
									    <div role="tabpanel" class="tab-pane" id="tab8">
								      		<div class="xs-vertical-tabs__title">IMPS</div>
									      	<div class="gstr_csv_export_button">
	                                            <a role="button" class="btn btn-add-new pull-right export_csv gstr-impg-download" disabled onclick="GeneralExportTableToCSVInit(this, [$('#gstr-impg-report'), 'GSTR IMPG Report.csv']);" data-tooltip="tooltip" title="Please wait... Your Report is not generated completely for download."><i class="fa fa-download" aria-hidden="true"></i></a>
	                                        </div>
											<table class="table table-bordered custom-table table-striped grn_tbl search_result_table" id="gstr-imps-report" style="width: 100%;margin-right: 80px; display: inline-table;">
												<thead class="th-vertical-center">
                                                    <tr class='total-header'>
														<th data-style="tableHeader" style="min-width: 80px;">S. NO</th>
														<th data-style="tableHeader" style="min-width: 40px;">Invoice Number </th>
														<th data-style="tableHeader" style="min-width: 40px;">Invoice Date</th>
														<th data-style="tableHeader" style="min-width: 40px;">Invoice Value</th>
														<th data-style="tableHeader" style="min-width: 40px;">Place of Supply</th>
														<th data-style="tableHeader" style="min-width: 40px;">Supply Type</th>
														<th data-style="tableHeader" style="min-width: 40px;">Rate	</th>
														<th data-style="tableHeader" style="min-width: 40px;">Taxable Value</th>
														<th data-style="tableHeader" style="min-width: 40px;">Integrated Tax Paid</th>
														<th data-style="tableHeader" style="min-width: 40px;">Cess Paid</th>
														<th data-style="tableHeader" style="min-width: 40px;">Eligibility For ITC</th>
														<th data-style="tableHeader" style="min-width: 40px;">Availed ITC Integrated Tax</th>
														<th data-style="tableHeader" style="min-width: 40px;">Availed ITC Cess</th>
                                                    </tr>
												</thead>
												<tbody id="grn_imps_tbl_tbody" class="gstr-report-body"></tbody>
												<tfoot id="grn_imps_tbl_tfoot" class="gstr-report-foot"></tfoot>
											</table>
								      	</div>
								    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    $(document).ready(function () {
        setTimeout(function () {
			$("#grnreportview").trigger('click');
        }, 10);
        activateTabs();
    });

    $(window).load(function(){
        updateFilterText();
    });

    function updateFilterText() {
        $(".filtered-date b").text($("#reportrange").find("span").text());
    }

    var oTable1, oTable2, oTable3, oTable4, oTable5, oTable6, oTable7, tableReadyStatus = [];
	var oSettings1, oSettings2, oSettings3, oSettings4, oSettings5, oSettings6, oSettings7;

    function b2bTblData(data){
        var row = '';
        var b2b_invoice_value=0,b2b_taxable_value=0,b2b_igst_amount=0,b2b_cgst_amount=0,b2b_sgst_amount=0,b2b_cess_amount=0;
        if(data.length != 0){
            var invoice_type = '';
            $.each(data, function (key, value) {
                if(value['name'] === 'Registered Business' || value['name'] === 'Registered Business - Composition'||value['name'] === 'Tax Deductor'){
                    invoice_type = 'Regular'
                }
                else if(value['name'] === 'Special Economic Zone (SEZ)' || value['name'] === 'SEZ Developer') {
                    invoice_type = 'SEZ Supplies With Payment'
                }
                else if(value['name'] === 'Deemed Export') {
                    invoice_type = 'Deemed Exp'
                }

            	row = `<tr class='text-center'>
            			<td>${key+1}</td>
                		<td class='text-left'>${value['gstno']}</td>
                		<td class='text-left'>${value['party_name']}</td>
                		<td class='text-left'>${value['invno']}</td>
	                    <td data-sort='${moment(value['inv_date']).format("YYYYMMDDHHmmss")}'>
	                    	${moment(value['inv_date']).format("DD/MM/YYYY")}
                    	</td>
	                    <td class='text-left'>${Number(value['net_inv_value']).toFixed(2)}</td>
	                    <td class='text-left'>${value['voucher_code']}</td>
	                    <td class='text-left'>${value['place_of_supply']}</td>
	                    <td class='text-left'>${value['reverse_charge']}</td>
	                    <td class='text-left'>${invoice_type}</td>
	                    <td class='text-left'>${value['total_rate']}</td>
	                    <td class='text-left'>${Number(value['taxable_value']).toFixed(2)}</td>
	                    <td class='text-left'>${value['igst_value']}</td>
	                    <td class='text-left'>${value['cgst_value']}</td>
	                    <td class='text-left'>${value['sgst_value']}</td>
	                    <td class='text-left'>${Number(value['cess']).toFixed(2)}</td>
	                    <td class='text-left'>${value['ITC']}</td>
	                    <td class='text-left'></td>
	                    <td class='text-left'></td>
	                    <td class='text-left'></td>
	                    <td class='text-left'></td>
                    </tr>`;
                b2b_invoice_value = parseFloat((value['net_inv_value'] ? value['net_inv_value'] : "0.00")) + b2b_invoice_value;
                b2b_taxable_value = parseFloat((value['taxable_value'] ? value['taxable_value'] : "0.00")) + b2b_taxable_value;
                b2b_igst_amount = parseFloat((value['igst_value'] ? value['igst_value'] : "0.00")) + b2b_igst_amount;
                b2b_cgst_amount = parseFloat((value['cgst_value'] ? value['cgst_value'] : "0.00")) + b2b_cgst_amount;
                b2b_sgst_amount = parseFloat((value['sgst_value'] ? value['sgst_value'] : "0.00")) + b2b_sgst_amount;
                b2b_cess_amount = parseFloat((value['cess'] ? value['cess'] : "0.00")) + b2b_cess_amount;
                $('#grn_b2b_tbl_tbody').append(row);    
            });
            var foot_row = `<tr class='exclude-export'>
	            				<td></td>
	            				<td></td>
								<td></td>
								<td class="text-right report-total-text">Total</td>
								<td class="text-right"><b style="font-size: 14px;">${Number(b2b_invoice_value).toFixed(2)}</b></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(b2b_taxable_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(b2b_igst_amount).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(b2b_cgst_amount).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(b2b_sgst_amount).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(b2b_cess_amount).toFixed(2)}</b></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
							</tr>`;
			var total_header_for_excel = `<tr class='hide total-header gstr-report-head'>
				            				<td data-style="reportHeader"></td>
				            				<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Invoice Value</td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Taxable Value</td>
											<td data-style="reportHeader">Total Integrated Tax Paid</td>
											<td data-style="reportHeader">Total Central Tax Paid</td>
											<td data-style="reportHeader">Total TState/UT Tax Paid</td>
											<td data-style="reportHeader">Total Cess</td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
										</tr>
										<tr class='hide gstr-report-head'>
				            				<td></td>
				            				<td></td>
											<td></td>
											<td></td>
											<td>${Number(b2b_invoice_value).toFixed(2)}</td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td>${Number(b2b_taxable_value).toFixed(2)}</td>
											<td>${Number(b2b_igst_amount).toFixed(2)}</td>
											<td>${Number(b2b_cgst_amount).toFixed(2)}</td>
											<td>${Number(b2b_sgst_amount).toFixed(2)}</td>
											<td>${Number(b2b_cess_amount).toFixed(2)}</td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
										</tr>`;
			$("#gstr-b2b-report thead").prepend(total_header_for_excel);
			$('#grn_b2b_tbl_tfoot').append(foot_row);
        }
        oTable1 = $('#gstr-b2b-report').DataTable({
			fixedHeader: true,
			"pageLength": 50,
			"search": {
				"smart": false
			}
		});
		oTable1.on("draw",function() {
			var keyword = $('#gstr-b2b-report_filter > label:eq(0) > input').val();
			$('#gstr-b2b-report').unmark();
			$('#gstr-b2b-report').mark(keyword,{});
		});
		oTable1.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
        oSettings1 = oTable1.settings();
        $( window ).resize();
        tableReadyStatus.push("b2b");
        validateTableDownload();
    }

     function b2burcTblData(data){
        var row = '';
        var b2burc_invoice_value=0,b2burc_taxable_value=0,b2burc_igst_amount=0,b2burc_cgst_amount=0,b2burc_sgst_amount=0,b2burc_cess_amount=0;
        if(data.length != 0){
            $.each(data, function (key, value) {
            	row = `	<tr class='text-center'>
            			<td>${key+1}</td>
	                    <td class='text-left'>${value['party_name']}</td>
	                    <td class='text-left'>${value['invno']}</td>
	                    <td data-sort='${moment(value['inv_date']).format("YYYYMMDDHHmmss")}'>${moment(value['inv_date']).format("DD/MM/YYYY")}</td>
	                    <td class='text-left'>${Number(value['net_inv_value']).toFixed(2)}</td>
	                    <td class='text-left'>${value['place_of_supply']}</td>
	                    <td class='text-left'>${value['supply_type']}</td>
	                    <td class='text-left'>${value['total_rate']}</td>
	                    <td class='text-left'>${Number(value['taxable_value']).toFixed(2)}</td>
	                    <td class='text-left'>${value['igst_value']}</td>
	                    <td class='text-left'>${value['cgst_value']}</td>
	                    <td class='text-left'>${value['sgst_value']}</td>
	                    <td class='text-left'>${Number(value['cess']).toFixed(2)}</td>
	                    <td class='text-left'>${value['ITC']}</td>
	                    <td class='text-left'></td>
	                    <td class='text-left'></td>
	                    <td class='text-left'></td>
	                    <td class='text-left'></td>
                    </tr>`;
                b2burc_invoice_value = parseFloat((value['net_inv_value'] ? value['net_inv_value'] : "0.00")) + b2burc_invoice_value;
                b2burc_taxable_value = parseFloat((value['taxable_value'] ? value['taxable_value'] : "0.00")) + b2burc_taxable_value;
                b2burc_igst_amount = parseFloat((value['igst_value'] ? value['igst_value'] : "0.00")) + b2burc_igst_amount;
                b2burc_cgst_amount = parseFloat((value['cgst_value'] ? value['cgst_value'] : "0.00")) + b2burc_cgst_amount;
                b2burc_sgst_amount = parseFloat((value['sgst_value'] ? value['sgst_value'] : "0.00")) + b2burc_sgst_amount;
                b2burc_cess_amount = parseFloat((value['cess'] ? value['cess'] : "0.00")) + b2burc_cess_amount;        
                $('#grn_b2burc_tbl_tbody').append(row);        
            });
            var foot_row = `<tr class='exclude-export'>
	            				<td></td>
	            				<td></td>
								<td></td>
								<td class="text-right report-total-text">Total</td>
								<td class="text-right"><b style="font-size: 14px;">${Number(b2burc_invoice_value).toFixed(2)}</b></td>
								<td></td>
								<td></td>
								<td></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(b2burc_taxable_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(b2burc_igst_amount).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(b2burc_cgst_amount).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(b2burc_sgst_amount).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(b2burc_cess_amount).toFixed(2)}</b></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
							</tr>`;
			var total_header_for_excel = `<tr class='hide total-header gstr-report-head'>
				            				<td data-style="reportHeader"></td>
				            				<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Invoice Value</td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Taxable Value</td>
											<td data-style="reportHeader">Total Integrated Tax Paid</td>
											<td data-style="reportHeader">Total Central Tax Paid</td>
											<td data-style="reportHeader">Total TState/UT Tax Paid</td>
											<td data-style="reportHeader">Total Cess Paid</td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
										</tr>
										<tr class='hide gstr-report-head'>
				            				<td></td>
				            				<td></td>
											<td></td>
											<td></td>
											<td>${Number(b2burc_invoice_value).toFixed(2)}</td>
											<td></td>
											<td></td>
											<td></td>
											<td>${Number(b2burc_taxable_value).toFixed(2)}</td>
											<td>${Number(b2burc_igst_amount).toFixed(2)}</td>
											<td>${Number(b2burc_cgst_amount).toFixed(2)}</td>
											<td>${Number(b2burc_sgst_amount).toFixed(2)}</td>
											<td>${Number(b2burc_cess_amount).toFixed(2)}</td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
										</tr>`;
			$("#gstr-b2bur-report thead").prepend(total_header_for_excel);
			$('#grn_b2burc_tbl_tfoot').append(foot_row);
        }
        oTable2 = $('#gstr-b2bur-report').DataTable({
			fixedHeader: true,
			"pageLength": 50,
			"search": {
				"smart": false
			}
		});
		oTable2.on("draw",function() {
			var keyword = $('#gstr-b2bur-report_filter > label:eq(0) > input').val();
			$('#gstr-b2bur-report').unmark();
			$('#gstr-b2bur-report').mark(keyword,{});
		});
		oTable2.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
        oSettings2 = oTable2.settings();
        $( window ).resize();
        tableReadyStatus.push("b2bur");
        validateTableDownload();
    }

    function loopHSNTblData(data){
        var row = '';
        var hsn_total_qty=0,hsn_total_value=0,hsn_tax_value=0,hsn_igst=0,hsn_cgst=0,hsn_sgst=0,hsn_cess=0;
        if(data.length != 0){
            $.each(data, function (key, value) {
                row = `<tr class='text-center'>
                			<td>${key+1}</td>
		                    <td>${value['hsn_code']}</td>
		                    <td>${value['description']}</td>
		                    <td>${value['unit_name']}</td>
		                    <td>${value['qty']}</td>
		                    <td class='text-right'>${(value['total_value'] == undefined ? "0.00" : Number(value['total_value']).toFixed(2))}</td>
		                    <td class='text-right'>${(value['taxable_value'] == undefined ? "0.00" : Number(value['taxable_value']).toFixed(2))}</td>
		                    <td>${Number(value['igst_value']).toFixed(2)}</td>
		                    <td>${Number(value['cgst_value']).toFixed(2)}</td>
		                    <td>${Number(value['sgst_value']).toFixed(2)}</td>
		                    <td class='text-right'>${(value['cess_value'] == undefined ? "0.00" : Number(value['cess_value']).toFixed(2))}</td>
	                    </tr>`;
                $('#grn_hsn_tbl_tbody').append(row);
                hsn_total_qty = parseFloat(value['qty']) + hsn_total_qty;
                hsn_total_value = parseFloat((value['total_value'] ? value['total_value'] : "0.00")) + hsn_total_value;
                hsn_tax_value = parseFloat((value['taxable_value'] ? value['taxable_value'] : "0.00")) + hsn_tax_value;
                hsn_igst = parseFloat(value['igst_value']) + hsn_igst;
                hsn_cgst = parseFloat(value['cgst_value']) + hsn_cgst;
                hsn_sgst = parseFloat(value['sgst_value']) + hsn_sgst;
                hsn_cess = parseFloat((value['cess_value'] ? value['cess_value'] : "0.00")) + hsn_cess;
            });
            var foot_row = `<tr class='exclude-export'>
								<td></td>
								<td></td>
								<td></td>
								<td class="text-right report-total-text">Total</td>
								<td class="text-right"><b style="font-size: 14px;">${Number(hsn_total_qty).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(hsn_total_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(hsn_tax_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(hsn_igst).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(hsn_cgst).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(hsn_sgst).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(hsn_cess).toFixed(2)}</b></td>
							</tr>`;
			var total_header_for_excel = `<tr class='hide total-header gstr-report-head'>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Quantity</td>
											<td data-style="reportHeader">Total Value</td>
											<td data-style="reportHeader">Total Taxable Value</td>
											<td data-style="reportHeader">Total Integrated Tax</td>
											<td data-style="reportHeader">Total Central Tax</td>
											<td data-style="reportHeader">Total State/UT Tax</td>
											<td data-style="reportHeader">Total Cess</td>
										</tr>
										<tr class='hide gstr-report-head'>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td>${Number(hsn_total_qty).toFixed(2)}</td>
											<td>${Number(hsn_total_value).toFixed(2)}</td>
											<td>${Number(hsn_tax_value).toFixed(2)}</td>
											<td>${Number(hsn_igst).toFixed(2)}</td>
											<td>${Number(hsn_cgst).toFixed(2)}</td>
											<td>${Number(hsn_sgst).toFixed(2)}</td>
											<td>${Number(hsn_cess).toFixed(2)}</td>
										</tr>`;
			$("#gstr-hsn-report thead").prepend(total_header_for_excel);
			$('#grn_hsn_tbl_tfoot').append(foot_row);
        }
        oTable3 = $('#gstr-hsn-report').DataTable({
			fixedHeader: true,
			"pageLength": 50,
			"search": {
				"smart": false
			}
		});
		oTable3.on("draw",function() {
			var keyword = $('#gstr-hsn-report_filter > label:eq(0) > input').val();
			$('#gstr-hsn-report').unmark();
			$('#gstr-hsn-report').mark(keyword,{});
		});
		oTable3.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
        oSettings3 = oTable3.settings();
        $( window ).resize();
        tableReadyStatus.push("hsn");
        validateTableDownload();
    }

    function loopEXEMPTblData(data){
        var row = '';
        var exemp_comp_person = 0, exemp_nil_rated=0, exemp_exempted=0, exemp_non_gst=0;
        if(data.length != 0){
            $.each(data, function (key, value) {
                row = ` <tr class='text-center'>
                			<td>${Number(key)+1}</td>
		                    <td>${value[0]}</td>
		                    <td>${value[1]}</td>
		                    <td class='text-right'>${(value[2] == undefined ? "0.00" : Number(value[2]).toFixed(2))}</td>
		                    <td class='text-right'>${(value[3] == undefined ? "0.00" : Number(value[3]).toFixed(2))}</td>
		                    <td class='text-right'>${(value[4] == undefined ? "0.00" : Number(value[4]).toFixed(2))}</td>
	                    </tr>`;
                $('#grn_exemp_tbl_tbody').append(row);
                exemp_comp_person = parseFloat((value[1] ? value[1] : "0.00")) + exemp_comp_person;
                exemp_nil_rated = parseFloat((value[2] ? value[2] : "0.00")) + exemp_nil_rated;
                exemp_exempted = parseFloat((value[3] ? value[3] : "0.00")) + exemp_exempted;
                exemp_non_gst = parseFloat((value[4] ? value[4] : "0.00")) + exemp_non_gst;
            });
            var foot_row = `<tr class='exclude-export'>
								<td></td>
								<td class="text-right report-total-text">Total</td>
								<td class="text-right"><b style="font-size: 14px;">${Number(exemp_comp_person).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(exemp_nil_rated).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(exemp_exempted).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(exemp_non_gst).toFixed(2)}</b></td>
							</tr>`;
			var total_header_for_excel = `<tr class='hide total-header gstr-report-head'>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Composition taxable person</td>
											<td data-style="reportHeader">Total Nil Rated Supplies</td>
											<td data-style="reportHeader">Total Exempted Supplies</td>
											<td data-style="reportHeader">Total Non-GST Supplies</td>
										</tr>
										<tr class='hide gstr-report-head'>
											<td></td>
											<td></td>
											<td>${Number(exemp_comp_person).toFixed(2)}</td>
											<td>${Number(exemp_nil_rated).toFixed(2)}</td>
											<td>${Number(exemp_exempted).toFixed(2)}</td>
											<td>${Number(exemp_non_gst).toFixed(2)}</td>
										</tr>`;	
			$("#gstr-exemp-report thead").prepend(total_header_for_excel);
			$('#grn_exemp_tbl_tfoot').append(foot_row);
        }
        oTable6 = $('#gstr-exemp-report').DataTable({
			fixedHeader: true,
			"pageLength": 50,
			"search": {
				"smart": false
			}
		});
		oTable6.on("draw",function() {
			var keyword = $('#gstr-exemp-report_filter > label:eq(0) > input').val();
			$('#gstr-exemp-report').unmark();
			$('#gstr-exemp-report').mark(keyword,{});
		});
		oTable6.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
        oSettings6 = oTable6.settings();
        $( window ).resize();
        tableReadyStatus.push("exepm");
        validateTableDownload();
	}

     function loopIMPGTblData(data){
        var row = '';
        var entry_value=0, taxable_value=0, igst_tax=0, cess_amt = 0;
        if(data.length != 0){
            $.each(data, function (key, value) {
                row = `<tr class='text-center'>
                			<td>${key+1}</td>
		                    <td>${value['port_code']}</td>
		                    <td>${value['inv_no']}</td>
		                    <td data-sort='${moment(value['invoice_date']).format("YYYYMMDDHHmmss")}'>${moment(value['invoice_date']).format("DD/MM/YYYY")}</td>
		                    <td>${Number(value['grn_total']).toFixed(2)}</td>
		                    <td>${value['doc_type']}</td>
		                    <td>${value['gstin']}</td>
		                    <td class='text-right'>${(value['rate'] == undefined ? "0.00" : value['rate'])}</td>
		                    <td class='text-right'>${(value['taxable_value'] == undefined ? "0.00" : Number(value['taxable_value']).toFixed(2))}</td>
		                    <td>${Number(value['igst_value']).toFixed(2)}</td>
		                    <td class='text-right'>${(value['cess_value'] == undefined ? "0.00" : Number(value['cess_value']).toFixed(2))}</td>
		                    <td> </td>
		                    <td> </td>
		                    <td> </td>
	                    </tr>`;
                $('#grn_impg_tbl_tbody').append(row);
                entry_value = parseFloat((value['grn_total'] ? value['grn_total'] : "0.00")) + entry_value;
                taxable_value = parseFloat((value['taxable_value'] ? value['taxable_value'] : "0.00")) + taxable_value;
                igst_tax = parseFloat((value['igst_value'] ? value['igst_value'] : "0.00")) + igst_tax;
                cess_amt = parseFloat((value['cess_value'] == undefined ? "0.00" : value['cess_value'])) + cess_amt;
            });
            var foot_row = `<tr class='exclude-export'>
								<td></td>
								<td></td>
								<td></td>
								<td class="text-right report-total-text">Total</td>
								<td class="text-right"><b style="font-size: 14px;">${Number(entry_value).toFixed(2)}</b></td>
								<td></td>
								<td></td>
								<td></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(taxable_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(igst_tax).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cess_amt).toFixed(2)}</b></td>
								<td></td>
								<td></td>
								<td></td>
							</tr>`;
			var total_header_for_excel = `<tr class='hide total-header gstr-report-head'>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Bill of Entry Value</td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Taxable Value</td>
											<td data-style="reportHeader">Total Integrated Tax Paid</td>
											<td data-style="reportHeader">Total Cess Paid</td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
										</tr>
										<tr class='hide gstr-report-head'>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td>${Number(entry_value).toFixed(2)}</td>
											<td></td>
											<td></td>
											<td></td>
											<td>${Number(taxable_value).toFixed(2)}</td>
											<td>${Number(igst_tax).toFixed(2)}</td>
											<td>${Number(cess_amt).toFixed(2)}</td>
											<td></td>
											<td></td>
											<td></td>
										</tr>`;
			$("#gstr-impg-report thead").prepend(total_header_for_excel);
			$('#grn_impg_tbl_tfoot').append(foot_row);
        }
        oTable7 = $('#gstr-impg-report').DataTable({
			fixedHeader: true,
			"pageLength": 50,
			"search": {
				"smart": false
			}
		});
		oTable7.on("draw",function() {
			var keyword = $('#gstr-impg-report_filter > label:eq(0) > input').val();
			$('#gstr-impg-report').unmark();
			$('#gstr-impg-report').mark(keyword,{});
		});
		oTable7.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
        oSettings7 = oTable7.settings();
        $( window ).resize();
        tableReadyStatus.push("impg");
        validateTableDownload();
    }


    function loopIMPSTblData(data){
        var row = '';
        var entry_value=0, taxable_value=0, igst_tax=0, cess_amt = 0;
        if(data.length != 0){
            $.each(data, function (key, value) {
                row = `<tr class='text-center'>
                			<td>${key+1}</td>
		                    <td>${value['inv_no']}</td>
		                    <td data-sort='${moment(value['invoice_date']).format("YYYYMMDDHHmmss")}'>${moment(value['invoice_date']).format("DD/MM/YYYY")}</td>
		                    <td>${Number(value['grn_total']).toFixed(2)}</td>
		                    <td>${value['place_of_supply']}</td>
		                    <td>${value['doc_type']}</td>
		                    <td class='text-right'>${(value['rate'] == undefined ? "0.00" : value['rate'])}</td>
		                    <td class='text-right'>${(value['taxable_value'] == undefined ? "0.00" : Number(value['taxable_value']).toFixed(2))}</td>
		                    <td>${Number(value['igst_value']).toFixed(2)}</td>
		                    <td class='text-right'>${(value['cess_value'] == undefined ? "0.00" : Number(value['cess_value']).toFixed(2))}</td>
		                    <td> </td>
		                    <td> </td>
		                    <td> </td>
	                    </tr>`;
                $('#grn_imps_tbl_tbody').append(row);
                entry_value = parseFloat((value['grn_total'] ? value['grn_total'] : "0.00")) + entry_value;
                taxable_value = parseFloat((value['taxable_value'] ? value['taxable_value'] : "0.00")) + taxable_value;
                igst_tax = parseFloat((value['igst_value'] ? value['igst_value'] : "0.00")) + igst_tax;
                cess_amt = parseFloat((value['cess_value'] == undefined ? "0.00" : value['cess_value'])) + cess_amt;
            });
            var foot_row = `<tr class='exclude-export'>
								<td></td>
								<td></td>
								<td class="text-right report-total-text">Total</td>
								<td class="text-right"><b style="font-size: 14px;">${Number(entry_value).toFixed(2)}</b></td>
								<td></td>
								<td></td>
								<td></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(taxable_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(igst_tax).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cess_amt).toFixed(2)}</b></td>
								<td></td>
								<td></td>
								<td></td>
							</tr>`;
			var total_header_for_excel = `<tr class='hide total-header gstr-report-head'>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Bill of Entry Value</td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Taxable Value</td>
											<td data-style="reportHeader">Total Integrated Tax Paid</td>
											<td data-style="reportHeader">Total Cess Paid</td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
										</tr>
										<tr class='hide gstr-report-head'>
											<td></td>
											<td></td>
											<td></td>
											<td>${Number(entry_value).toFixed(2)}</td>
											<td></td>
											<td></td>
											<td></td>
											<td>${Number(taxable_value).toFixed(2)}</td>
											<td>${Number(igst_tax).toFixed(2)}</td>
											<td>${Number(cess_amt).toFixed(2)}</td>
											<td></td>
											<td></td>
											<td></td>
										</tr>`;
			$("#gstr-imps-report thead").prepend(total_header_for_excel);
			$('#grn_imps_tbl_tfoot').append(foot_row);
        }
        oTable8 = $('#gstr-imps-report').DataTable({
			fixedHeader: true,
			"pageLength": 50,
			"search": {
				"smart": false
			}
		});
		oTable8.on("draw",function() {
			var keyword = $('#gstr-imps-report_filter > label:eq(0) > input').val();
			$('#gstr-imps-report').unmark();
			$('#gstr-imps-report').mark(keyword,{});
		});
		oTable8.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
        oSettings8 = oTable8.settings();
        $( window ).resize();
        tableReadyStatus.push("imps");
        validateTableDownload();
    }

    function loopCDNRTblData(data){
        var row = '';
        var cdnr_note_value=0,cdnr_taxable_value=0,cdnr_igst_value=0,cdnr_cgst_value=0,cdnr_sgst_value=0,cdnr_cess_value=0;
        if(data.length != 0){
            $.each(data, function (key, value) {
                row = `	<tr class='text-center'>
                			<td>${key+1}</td>
		                    <td>${value['gstin']}</td>
		                    <td>${value['note_no']}</td>
		                    <td data-sort='${moment(value['note_date']).format("YYYYMMDDHHmmss")}'>${moment(value['note_date']).format("DD/MM/YYYY")}</td>
		                    <td>${value['inv_no']}</td>
		                    <td data-sort='${moment(value['inv_date']).format("YYYYMMDDHHmmss")}'>${moment(value['inv_date']).format("DD/MM/YYYY")}</td>
		                    <td>${value['pre_gst']}</td>
		                    <td>${value['doc_type']}</td>
		                    <td> </td>
		                   <td>${value['supply_type']}</td>
		                    <td class='text-right'>${Number(value['total_amount']).toFixed(2)}</td>
		                    <td class='text-right'>${value['rate']}</td>
		                    <td class='text-right'>${(value['taxable_value'] == undefined ? "0.00" : Number(value['taxable_value']).toFixed(2))}</td>
		                    <td class='text-right'>${(value['igst_value'] == undefined ? "0.00" : Number(value['igst_value']).toFixed(2))}</td>
		                    <td class='text-right'>${(value['cgst_value'] == undefined ? "0.00" : Number(value['cgst_value']).toFixed(2))}</td>
		                    <td class='text-right'>${(value['sgst_value'] == undefined ? "0.00" : Number(value['sgst_value']).toFixed(2))}</td>
		                    <td class='text-right'>${(value['cess_value'] == undefined ? "0.00" : Number(value['cess_value']).toFixed(2))}</td>
		                    <td></td>
		                    <td></td>
		                    <td></td>
		                    <td></td>
		                    <td></td>
	                    </tr>`;
                $('#grn_cdnr_tbl_tbody').append(row);
                cdnr_note_value = parseFloat((value['total_amount'] == undefined ? "0.00" : value['total_amount'])) + cdnr_note_value;
                cdnr_taxable_value = parseFloat((value['taxable_value'] == undefined ? "0.00" : value['taxable_value'])) + cdnr_taxable_value;
                cdnr_igst_value = parseFloat((value['igst_value'] == undefined ? "0.00" : value['igst_value'])) + cdnr_igst_value;
                cdnr_cgst_value = parseFloat((value['cgst_value'] == undefined ? "0.00" : value['cgst_value'])) + cdnr_cgst_value;
                cdnr_sgst_value = parseFloat((value['sgst_value'] == undefined ? "0.00" : value['sgst_value'])) + cdnr_sgst_value;
                cdnr_cess_value = parseFloat((value['cess_value'] == undefined ? "0.00" : value['cess_value'])) + cdnr_cess_value;
            });
            var foot_row = `<tr class='exclude-export'>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td class="text-right report-total-text">Total</td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cdnr_note_value).toFixed(2)}</b></td>
								<td></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cdnr_taxable_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cdnr_igst_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cdnr_cgst_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cdnr_sgst_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cdnr_cess_value).toFixed(2)}</b></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
							</tr>`;
			var total_header_for_excel = `<tr class='hide total-header gstr-report-head'>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Note/Refund Voucher Value</td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Taxable Value</td>
											<td data-style="reportHeader">Total Integrated Tax Paid</td>
											<td data-style="reportHeader">Total Central Tax Paid</td>
											<td data-style="reportHeader">Total TState/UT Tax Paid</td>
											<td data-style="reportHeader">Total Cess Paid</td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
										</tr>
										<tr class='hide gstr-report-head'>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td>${Number(cdnr_note_value).toFixed(2)}</td>
											<td></td>
											<td>${Number(cdnr_taxable_value).toFixed(2)}</td>
											<td>${Number(cdnr_igst_value).toFixed(2)}</td>
											<td>${Number(cdnr_cgst_value).toFixed(2)}</td>
											<td>${Number(cdnr_sgst_value).toFixed(2)}</td>
											<td>${Number(cdnr_cess_value).toFixed(2)}</td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
										</tr>`;
			$("#gstr-cdnr-report thead").prepend(total_header_for_excel);
			$('#grn_cdnr_tbl_tfoot').append(foot_row);
        }
        oTable4 = $('#gstr-cdnr-report').DataTable({
			fixedHeader: true,
			"pageLength": 50,
			"search": {
				"smart": false
			}
		});
		oTable4.on("draw",function() {
			var keyword = $('#gstr-cdnr-report_filter > label:eq(0) > input').val();
			$('#gstr-cdnr-report').unmark();
			$('#gstr-cdnr-report').mark(keyword,{});
		});
		oTable4.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
        oSettings4 = oTable4.settings();
        $( window ).resize();
        tableReadyStatus.push("cdnr");
        validateTableDownload();
    }

    function loopCDNURTblData(data){
        var row = '';
        var cdnur_note_value=0,cdnur_taxable_value=0,cdnur_igst_value=0,cdnur_cgst_value=0,cdnur_sgst_value=0,cdnur_cess_value=0;
        if(data.length != 0){
            $.each(data, function (key, value) {
                row = `	<tr class='text-center'>
                			<td>${key+1}</td>
		                    <td>${value['note_no']}</td>
		                    <td data-sort='${moment(value['note_date']).format("YYYYMMDDHHmmss")}'>${moment(value['note_date']).format("DD/MM/YYYY")}</td>
		                    <td>${value['inv_no']}</td>
		                    <td data-sort='${moment(value['inv_date']).format("YYYYMMDDHHmmss")}'>${moment(value['inv_date']).format("DD/MM/YYYY")}</td>
		                    <td>${value['pre_gst']}</td>
		                    <td>${value['doc_type']}</td>
		                    <td></td>
		                    <td>${value['supply_type']}</td>
		                    <td class='text-right'>${Number(value['total_amount']).toFixed(2)}</td>
		                    <td class='text-right'>${value['rate']}</td>
		                    <td class='text-right'>${(value['taxable_value'] == undefined ? "0.00" : Number(value['taxable_value']).toFixed(2))}</td>
		                    <td class='text-right'>${(value['igst_value'] == undefined ? "0.00" : Number(value['igst_value']).toFixed(2))}</td>
		                    <td class='text-right'>${(value['cgst_value'] == undefined ? "0.00" : Number(value['cgst_value']).toFixed(2))}</td>
		                    <td class='text-right'>${(value['sgst_value'] == undefined ? "0.00" : Number(value['sgst_value']).toFixed(2))}</td>
		                    <td class='text-right'>${(value['cess_value'] == undefined ? "0.00" : Number(value['cess_value']).toFixed(2))}</td>
		                    <td></td>
		                    <td></td>
		                    <td></td>
		                    <td></td>
		                    <td></td>

	                    </tr>`;
	            $('#grn_cdnur_tbl_tbody').append(row);
	            cdnur_note_value = parseFloat((value['total_amount'] == undefined ? "0.00" : value['total_amount'])) + cdnur_note_value;
	            cdnur_taxable_value = parseFloat((value['taxable_value'] == undefined ? "0.00" : value['taxable_value'])) + cdnur_taxable_value;
	            cdnur_igst_value = parseFloat((value['igst_value'] == undefined ? "0.00" : value['igst_value'])) + cdnur_igst_value;
                cdnur_cgst_value = parseFloat((value['cgst_value'] == undefined ? "0.00" : value['cgst_value'])) + cdnur_cgst_value;
                cdnur_sgst_value = parseFloat((value['sgst_value'] == undefined ? "0.00" : value['sgst_value'])) + cdnur_sgst_value;
                cdnur_cess_value = parseFloat((value['cess_value'] == undefined ? "0.00" : value['cess_value'])) + cdnur_cess_value;

            });
            var foot_row = `<tr class='exclude-export'>
            					<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td class="text-right report-total-text">Total</td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cdnur_note_value).toFixed(2)}</b></td>
								<td></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cdnur_taxable_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cdnur_igst_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cdnur_cgst_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cdnur_sgst_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cdnur_cess_value).toFixed(2)}</b></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
							</tr>`;
			var total_header_for_excel = `<tr class='hide total-header gstr-report-head'>
			            					<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Note/Refund Voucher Value</td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Taxable Value</td>
											<td data-style="reportHeader">Total Integrated Tax Paid</td>
											<td data-style="reportHeader">Total Central Tax Paid</td>
											<td data-style="reportHeader">Total TState/UT Tax Paid</td>
											<td data-style="reportHeader">Total Cess Paid</td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
										</tr>
										<tr class='hide gstr-report-head'>
			            					<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td>${Number(cdnur_note_value).toFixed(2)}</td>
											<td></td>
											<td>${Number(cdnur_taxable_value).toFixed(2)}</td>
											<td>${Number(cdnur_igst_value).toFixed(2)}</td>
											<td>${Number(cdnur_cgst_value).toFixed(2)}</td>
											<td>${Number(cdnur_sgst_value).toFixed(2)}</td>
											<td>${Number(cdnur_cess_value).toFixed(2)}</td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
										</tr>`;				
			$('#grn_cdnur_tbl_tfoot').append(foot_row);
			$("#gstr-cdnur-report thead").prepend(total_header_for_excel);
        }
        oTable5 = $('#gstr-cdnur-report').DataTable({
			fixedHeader: true,
			"pageLength": 50,
			"search": {
				"smart": false
			}
		});
		oTable5.on("draw",function() {
			var keyword = $('#gstr-cdnur-report_filter > label:eq(0) > input').val();
			$('#gstr-cdnur-report').unmark();
			$('#gstr-cdnur-report').mark(keyword,{});
		});
		oTable5.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
        oSettings5 = oTable5.settings();
        $( window ).resize();
        tableReadyStatus.push("cdnur");
        validateTableDownload();
    }

    $("#grnreportview").click(function () {
    	if(oTable1 != undefined) {
         	oTable1.destroy();
        }
        if(oTable2 != undefined) {
         	oTable2.destroy();
        }
        if(oTable3 != undefined) {
         	oTable3.destroy();
        }
        if(oTable4 != undefined) {
         	oTable4.destroy();
        }
        if(oTable5 != undefined) {
         	oTable5.destroy();
        }
        if(oTable6 != undefined) {
         	oTable6.destroy();
        }
        if(oTable7 != undefined) {
         	oTable7.destroy();
        }
        tableReadyStatus = [];
        setDownloadButtonToDisabled();

        $("#loading").removeClass('hide');
        updateFilterText();
        from_date = $('.fromdate').val();
        to_date = $('.todate').val();
        $('.gstr-report-body, .gstr-report-foot').html("");
        $(".gstr-report-head").remove();

        // b2b Report
        $.ajax({
            url : "/erp/accounts/json/gstr2_b2b_report/",
            type : "POST",
            dataType: "json",
            data : {
                from_date: from_date,
                to_date: to_date,
                report_type:'b2b'
            },
            success : function(data) {
                b2bTblData(data);
            },error : function(xhr,errmsg,err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });

        // b2burc Report
        $.ajax({
            url : "/erp/accounts/json/gstr2_b2burc_report/",
            type : "POST",
            dataType: "json",
            data : {
                from_date: from_date,
                to_date: to_date,
                report_type:'b2burc'
            },
            success : function(data) {
                b2burcTblData(data);
            },error : function(xhr,errmsg,err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });

        //hsn
        $.ajax({
            url : "/erp/accounts/json/gstr2_hsn_report/",
            type : "POST",
            dataType: "json",
            data : {
                from_date: from_date,
                to_date: to_date,
                report_type:'hsn'
            },
            success : function(data) {
                loopHSNTblData(data)
            },error : function(xhr,errmsg,err) {
                console.log(xhr.status + ": " + xhr.responseText);
                $("#loading").addClass('hide').removeClass('show');
            }
        });

        // cdnur Report
        $.ajax({
            url : "/erp/accounts/json/gstr2_cdnur_report/",
            type : "POST",
            dataType: "json",
            data : {
                from_date: from_date,
                to_date: to_date,
            },
            success : function(data) {
                loopCDNURTblData(data);
            },error : function(xhr,errmsg,err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });

        // cdnr Report
        $.ajax({
            url : "/erp/accounts/json/gstr2_cdnr_report/",
            type : "POST",
            dataType: "json",
            data : {
                from_date: from_date,
                to_date: to_date,
            },
            success : function(data) {
                loopCDNRTblData(data);
            },error : function(xhr,errmsg,err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });

        // exemp Report
        $.ajax({
            url : "/erp/accounts/json/gstr2_exemp_report/",
            type : "POST",
            dataType: "json",
            data : {
                from_date: from_date,
                to_date: to_date,
                report_type:'exemp'
            },
            success : function(data) {
                loopEXEMPTblData(data);
            },error : function(xhr,errmsg,err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });

        // impg Report
        $.ajax({
            url : "/erp/accounts/json/gstr2_impg_report/",
            type : "POST",
            dataType: "json",
            data : {
                from_date: from_date,
                to_date: to_date,
                report_type:'impg'
            },
            success : function(data) {
                loopIMPGTblData(data);
            },error : function(xhr,errmsg,err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });

        // imps Report
        $.ajax({
            url : "/erp/accounts/json/gstr2_imps_report/",
            type : "POST",
            dataType: "json",
            data : {
                from_date: from_date,
                to_date: to_date,
                report_type:'imps'
            },
            success : function(data) {
                loopIMPSTblData(data);
            },error : function(xhr,errmsg,err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });
        closeFilterOption();
    	$("#loading").addClass('hide');
        ga('send', 'event', 'Accounts', 'View GSTR1 Purchase Report', $('#enterprise_label').val(), 1);
    });

    function tablesToExcelLoad() {
    	if(!($(".gstr-xsl-download").hasClass("disabled"))) {
    		removeDataTable();
			$(".downloading-main-container").removeClass('hide').addClass('show');
			setTimeout(function(){
				tablesToExcel(['gstr-b2b-report','gstr-b2bur-report','gstr-hsn-report','gstr-cdnr-report','gstr-cdnur-report','gstr-exemp-report','gstr-impg-report','gstr-imps-report'], ['B2B','B2BUR','HSN','CDNR','CDNUR','EXEMP','IMPG', 'IMPS'], 'GSTR-2 Purchase Report.xls', 'Excel');
			},100);
		}
	}

  	var tablesToExcel = (function() {
    var uri = 'data:application/vnd.ms-excel;base64,'
    , tmplWorkbookXML = '<?xml version="1.0" encoding="windows-1252"?><?mso-application progid="Excel.Sheet"?>'
	  + '	<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel"  xmlns:html="http://www.w3.org/TR/REC-html40">'
    + '		<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">'
	  + '			<Author>Qompare</Author>'
	  + '			<Created>{created}</Created>'
	  + '		</DocumentProperties>'
    + '		<Styles>'
	  + '			<Style ss:ID="Default" ss:Name="Normal">'
	  + '				<NumberFormat ss:Format=""/>'
	  + '			</Style>'
	  + '			<Style ss:ID="reportHeader">'
	  + '				<Alignment ss:WrapText="1" />'
    + '				<Borders>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Right"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Left"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Top"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Bottom"/>'
    + '				</Borders>'
	  + '				<Font ss:Bold="1" ss:Color="#FFFFFF"/>'
	  + '				<Interior ss:Color="#209be1" ss:Pattern="Solid" />'
	  + '				<NumberFormat/>'
	  + '				<Protection/>'
	  + '			</Style>'
	  + '			<Style ss:ID="tableHeader">'
	  + '				<Alignment ss:WrapText="1" />'
    + '				<Borders>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Right"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Left"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Top"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Bottom"/>'
    + '				</Borders>'
	  + '				<Font ss:Bold="1" ss:Color="#000000"/>'
	  + '				<Interior ss:Color="#cccccc" ss:Pattern="Solid" />'
	  + '				<NumberFormat/>'
	  + '				<Protection/>'
	  + '			</Style>'
    + '			<Style ss:ID="Changed">'
	  + '				<Borders/>'
	  + '				<Font ss:Color="#ff0000"></Font>'
	  + '				<Interior ss:Color="#99CC00" ss:Pattern="Solid"></Interior>'
	  + '				<NumberFormat/>'
	  + '				<Protection/>'
	  + '			</Style>'
    + '			<Style ss:ID="Missed">'
	  + '				<Borders/>'
	  + '				<Font ss:Color="#ff0000"></Font>'
	  + '				<Interior ss:Color="#ff0000" ss:Pattern="Solid"></Interior>'
	  + '				<NumberFormat/>'
	  + '				<Protection/>'
	  + '			</Style>'
    + '			<Style ss:ID="Decimals">'
	  + '				<NumberFormat ss:Format="Fixed"/>'
	  + '			</Style>'    
    + '	</Styles>' 
    + '	{worksheets}'
	  + '</Workbook>'
    , tmplWorksheetXML = '<Worksheet ss:Name="{nameWS}">'
	  + '	<ss:Table ss:DefaultColumnWidth="60">'
	  + '		{rows}'
	  + '	</ss:Table>'
	  + '</Worksheet>'
    , tmplCellXML = '			<ss:Cell{attributeStyleID}{attributeFormula}>'
	  + '				<ss:Data ss:Type="{nameType}">{data}</ss:Data>'
	  + '			</ss:Cell>'
    , base64 = function(s) { return window.btoa(unescape(encodeURIComponent(s))) }
    , format = function(s, c) { return s.replace(/{(\w+)}/g, function(m, p) { return c[p]; }) }
	    return function(tables, wsnames, wbname, appname) {
      var ctx = "";
      var workbookXML = "";
      var worksheetsXML = "";
      var rowsXML = "";

      for (var i = 0; i < tables.length; i++) {
        if (!tables[i].nodeType) tables[i] = document.getElementById(tables[i]);
        for (var j = 0; j < tables[i].rows.length; j++) {
        	if(tables[i].rows[j].classList.value.indexOf("exclude-export") == -1) {
        		if(tables[i].rows[j].classList.value.indexOf("total-header") != -1) {
	          		rowsXML += '<ss:Row ss:Height="50">'
	          	}
	          	else {
	          		rowsXML += '<ss:Row>'
	          	}
	          	for (var k = 0; k < tables[i].rows[j].cells.length; k++) {
	            	var dataType = tables[i].rows[j].cells[k].getAttribute("data-type");
	            	var dataStyle = tables[i].rows[j].cells[k].getAttribute("data-style");
	            	var dataValue = tables[i].rows[j].cells[k].getAttribute("data-value");
	            	dataValue = (dataValue)?dataValue:tables[i].rows[j].cells[k].innerHTML;
	            
	            	var dataFormula = tables[i].rows[j].cells[k].getAttribute("data-formula");
	            	dataFormula = (dataFormula)?dataFormula:(appname=='Calc' && dataType=='DateTime')?dataValue:null;
	            	ctx = {  attributeStyleID: (dataStyle=='Changed' || dataStyle=='Missed'|| dataStyle=='Header'|| dataStyle=='reportHeader'|| dataStyle=='tableHeader')?' ss:StyleID="'+dataStyle+'"':''
	                   , nameType: (dataType=='Number' || dataType=='DateTime' || dataType=='Boolean' || dataType=='Error')?dataType:'String'
	                   , data: (dataFormula)?'':dataValue
	                   , attributeFormula: (dataFormula)?' ss:Formula="'+dataFormula+'"':''
	                  };
	            	rowsXML += format(tmplCellXML, ctx);
	          	}
	          	rowsXML += '</ss:Row>'
          	}
        }
        ctx = {rows: rowsXML, nameWS: wsnames[i] || 'Sheet' + i};
        worksheetsXML += format(tmplWorksheetXML, ctx);
        rowsXML = "";
      }

      ctx = {created: (new Date()).getTime(), worksheets: worksheetsXML};
      workbookXML = format(tmplWorkbookXML, ctx);
      $(".dataTables_length").find("select").trigger('change');
      var link = document.createElement("A");
      link.href = uri + base64(workbookXML);
      link.download = wbname || 'Workbook.xls';
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      $(".downloading-main-container").removeClass('show').addClass('hide');
    }
  })();

	function activateTabs() {
	    $('.nav-tabs')
	      .scrollingTabs({
	        enableSwiping: true
      	})
	      .on('ready.xs', function() {
	        $('.tab-content').show();
      	});
	    $("#ul-report-header").click(function(){
	      	setTimeout(function(){
	      		$( window ).resize();
	      	},10)
	    })
  	}

  	function  GeneralExportTableToCSVInit(current, field) {
  		if(!$(current)[0].hasAttribute("disabled")) {
	  		removeDataTable();
	  		GeneralExportTableToCSV.apply(current, field);
	  	}
  	}

  	function  removeDataTable() {
  		if(oTable1 != undefined) {
         	oSettings1[0]._iDisplayLength = oSettings1[0].fnRecordsTotal();
	        oTable1.draw();
        }
        if(oTable2 != undefined) {
         	oSettings2[0]._iDisplayLength = oSettings2[0].fnRecordsTotal();
	        oTable2.draw();
        }
        if(oTable3 != undefined) {
         	oSettings3[0]._iDisplayLength = oSettings3[0].fnRecordsTotal();
	        oTable3.draw();
        }
        if(oTable4 != undefined) {
         	oSettings4[0]._iDisplayLength = oSettings4[0].fnRecordsTotal();
	        oTable4.draw();
        }
        if(oTable5 != undefined) {
         	oSettings5[0]._iDisplayLength = oSettings5[0].fnRecordsTotal();
	        oTable5.draw();
        }
        if(oTable6 != undefined) {
         	oSettings6[0]._iDisplayLength = oSettings6[0].fnRecordsTotal();
	        oTable6.draw();
        }
        if(oTable7 != undefined) {
         	oSettings7[0]._iDisplayLength = oSettings7[0].fnRecordsTotal();
	        oTable7.draw();
        }
  	}

  	function setDownloadButtonToDisabled(){
  		$(".gstr-xsl-download").addClass("disabled").attr("data-original-title", "Please wait... Your Report is not generated completely for download.");
  		$(".export_csv").attr("disabled","disabled").attr("data-original-title", "Please wait... Your Report is not generated completely for download.");
  	}

  	function  validateTableDownload() {
  		if(tableReadyStatus.length == $("#ul-report-header li").length) {
  			$(".gstr-xsl-download").removeClass("disabled");
  			$(".export_csv").removeAttr("disabled")
  			$(".gstr-xsl-download").attr("data-original-title", "Download consoldated report (as XLS)");
  			$(".gstr-b2b-download").attr("data-original-title", "Download GSTR B2B Report as CSV");
  			$(".gstr-b2bur-download").attr("data-original-title", "Download GSTR B2BUR Report as CSV");
  			$(".gstr-hsn-download").attr("data-original-title", "Download GSTR HSN Report as CSV");
  			$(".gstr-cdnr-download").attr("data-original-title", "Download GSTR CDNR Report as CSV");
  			$(".gstr-cdnur-download").attr("data-original-title", "Download GSTR CDNUR Report as CSV");  			
  			$(".gstr-exemp-download").attr("data-original-title", "Download GSTR EXEMP Report as CSV");
  			$(".gstr-impg-download").attr("data-original-title", "Download GSTR IMPG Report as CSV");
  		}
  	}

	$('.nav-pills li').removeClass('active');
	$("#li_gstr2_purchase_report").addClass('active');
</script>
{% endblock %}