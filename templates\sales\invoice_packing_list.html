<html>
	<link type="text/css" rel="stylesheet" href="/site_media/css/styles/bootstrap.css?v={{ current_version }}">
	<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<style>
	body{
		font-family: 'Roboto';
		color: #000;
		margin-top: 50px;
		font-size:11pt;
		line-height:19px;
	}
	img{
		width:20%;
		margin-top: 0px;
	}
	table{
		width:100%;
	}
	table, th, td {
		border: 1px solid black;
		border-collapse: collapse;
	}
	th, td {
		text-align: center;
		line-height: 22px;
		font-size:11pt;
	}
	hr{
		border: 1px solid #000;
		width:100%;
	}
	.page_tit{
		text-align:right;
		font-size: 19pt;
	}
	.estimation_date{
		margin-left: -90px;
    	font-size: 12pt;
	}

	.reg_data {
		width: calc(60% - 1em);
	    float: left;
	    font-size: 11pt;
	}
	.reg_details {
		 float: left;
	    font-weight: bold;
	    font-size: 11pt;
	}
	.pay_data{
		 width: calc(60% - 1em);
	   	 float: left;
		 margin-left: 160px;
   		 margin-top: -19px;
		 font-size:11pt;
		 width:100%;
	}
	.pay_details {
	    width: 50%;
	    float: left;
	    font-weight: bold;
	    margin-left: 33px;
	    font-size:11pt;
	}
	.tax_detail{
		text-align:right;
		padding-right:2px;
	}
	.grand_total{
		text-align:right;
		padding-right:2px;
		font-weight:bold;
	}
	.conditions{
		text-align:left;
		padding-right:4px;
		padding-left:4px;
		font-size:12pt;
		line-height:20px;
	}
	.bill_container{
		margin-left:-12px;
		margin-top:-10px;
	}

	.bill_container > hr {
		margin-bottom:4px;
		margin-top:2px;
	}
	.total_words{
		text-align:left;
		padding-left:3px;
		font-size:12pt;
	}
	.address{
		margin-left:-12px;
		font-size:12pt;
	}
	.align_table{
		text-align:right;
		padding-right:5px;
	}
	.description{
		text-align:left;
		padding-left:5px;
	}
	.test_environment{
		text-align:right;
		border-bottom:hidden;
		border-left:hidden;
		border-right:hidden;
		font-size:11pt;
	}

	@font-face {
	        font-family: 'Roboto';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/roboto.regular.ttf) format('truetype');
	}

	.packing-list-data {
		padding: 0;
		text-align: left;
		width: 100%;
		margin: 0;
	}

	.packing-list-data span {
		width: 33.33334%;
		border-bottom: solid 1px #666;
		border-right: solid 1px #666;
		padding: 4px;
		display: inline-block;
		float: left;
		border-collapse: collapse;
		margin: 0;
		font-size: 12px;
	}
	.party_details {
		float: left;
		font-size:12pt;
		font-weight:bold;
	}
	.party_data{
		width: calc(100% - 100px);
		font-size:11pt;
	}
	.item_header{
		text-align: center;
		margin-top: 6px;
		font-size: 12pt;
	}
</style>
<body>
	<div class="container">
		<div class="col-sm-12">
			<div class="col-sm-8" style="font-size:13pt;margin-left: -12px;">
				<img src="{{enterprise_logo}}" style="max-height: 10mm">
				<div style="margin-bottom:2px;"><b>{{ source.enterprise.name }}</b></div>
			</div>

			<div class="col-sm-4">
		        <span class="page_tit">{{ form_name }} </span><br>
				<div class="party_data">For : <b style="font-size:16px;">{{ invoice_no }}</b> </div>
				<div class="party_data">Issued Date  : {{ issued_date }}</div>
			</div>
			<hr style="margin-bottom:4px;">
		</div>

		<div class="col-sm-12">
			<div class="col-sm-6 address">
				<div>{{ enterprise_address }}</div>
				<div><b>Ph:</b>{% if source.enterprise.primary_contact_details.contact.phone_no %} {{ source.enterprise.primary_contact_details.contact.phone_no }} {% endif %}<b>E-mail:</b>{% if source.enterprise.primary_contact_details.contact.email %} {{ source.enterprise.primary_contact_details.contact.email }} {% endif %}</div>
			</div>

			<hr style="margin-bottom:14px;">
		</div>
		<div class="col-sm-12">
			<div class="col-sm-6 address">
				<div class="party_details">Customer Name</div>
				<div class="col-sm-6 pdf_ship_to_address">
					<div class="pdf_company_details_container" style="padding: 4px;">
						<span>
							{{ source.deliver_to }}<br />
							{% if source.gstin %}
								<b>GSTIN:</b> {{ source.gstin }}
							{% endif %}
						</span>
					</div>
				</div>
			</div>
			<div class="col-sm-6 ">
				<div class=" party_data" style="width:35%;float:left;"><b>Contact Person </b> </div> <div class="" style="width:65%;float:right;">: {{ source.customer.primary_contact_details.contact.name }}</div>
				<div class=" party_data" style="width:35%;float:left;"><b>Phone No </b> </div> <div class="" style="width:65%;float:right;">: {{ source.customer.primary_contact_details.contact.phone_no }}</div>
				<div class=" party_data" style="width:35%;float:left;"><b>Email </b> </div> <div class="" style="width:65%;float:right;overflow-wrap: break-all">: {{ source.customer.primary_contact_details.contact.email }}</div>
				<div class=" party_data" style="width:35%;float:left;"><b>Packing Slip No </b> </div> <div class="" style="width:65%;float:right;overflow-wrap: break-all">: {{ source.packing_slip_no }}</div>
				<div class=" party_data" style="width:35%;float:left;"><b>Packing Description </b> </div> <div class="" style="width:65%;float:right;overflow-wrap: break-all">: {{ source.packing_description }}</div>
			</div>
		</div>
		<div class="clearfix"></div>

		<div class="col-sm-12">
			<table>
					<div class="col-sm-12 item_header"><b>Packing Details</b></div>
				{% for item in invoice_items %}
					<tr>
						<th style="border: none !important; min-width: 60px;">{{forloop.counter}}.</th>
						<th class="description" style="width:185px; padding: 10px 6px; text-shadow: 0 0 #000; font-size: 16px; ">
							<span>{{ item.item_name }}<br></span>
						</th>
						<th class="align_table">{{ item.qty }}</th>
						<th>{{ item.unit }}</th>
					</tr>
					<tr>
						<td></td>
						<td colspan="3" class="packing-list-data">
							{% for s_no in item.serial_numbers %}
								<span>{{ s_no }}</span>
							{% endfor %}
						</td>
					</tr>
				{% endfor %}

			</table>
		</div>

	</div>
</body>
</html>
