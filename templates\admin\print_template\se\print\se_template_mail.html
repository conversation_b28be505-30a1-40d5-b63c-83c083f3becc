<!DOCTYPE html>
<html lang="en">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400&display=swap" rel="stylesheet">
    <body style="font-size:12px; font-family: <PERSON><PERSON>; background: #eee; padding: 30px 0;">
        <div border="0" cellpadding="0" cellspacing="0" style="max-width:700px;min-width:320px;width:100%; margin: 0 auto; font-size: 12px !important;">
            <div style="text-align: right; font-size:14px;line-height:24px;margin-bottom: 15px;">
                <a href="<<approve_url>>" target="_blank" style="text-decoration: none;">
                    <span style="display: inline-block;padding: 6px 12px;margin-bottom: 0;font-size: 14px;font-weight: normal;line-height: 1.42857143;text-align: center;cursor: pointer; border: 1px solid transparent;border-radius: 4px;background: #209be1;color: #FFF;">Approve</span>
                </a>
                <a href="<<reject_url>>" target="_blank" style="text-decoration: none;">
                    <span style="display: inline-block;padding: 6px 12px;margin-bottom: 0;font-size: 14px;font-weight: normal;line-height: 1.42857143;text-align: center;cursor: pointer;border: 1px solid transparent;border-radius: 4px;color: #fff;background-color: #d9534f;">Reject</span>
                </a>
            </div>
            <div style="border:1px solid #ccc;padding: 28px; background: #FFF;">
                <div style="border-color: transparent; width: 100%; font-family: 'pdf_{{general_res.font_family}}'">
                    <div class="row">
                        <div style="width:100%;">
                            <div style="width: 41.66666667%;float:left;">
                                {% if header_res.company.company_name.print %}
                                     <span class="pdf_company_name" style="font-size:12px;font-family: 'pdf_{{header_res.company.company_name.font_family}}'">{{source.enterprise.name}}</span><br>
                                {% endif %}
                            </div>
                            <div style="display: inline-block;width: 58.33333333%;text-align: right;">
                                <div style="margin-top: 0px;width: 100%">
                                    <span style="font-size: 14px;word-break: break-all;"><b class="pdf_form_name_text">{{ header_res.form_name.label.se }}</b></span>
                                    <span style="font-size: 12px; padding: 0;word-break: break-all;"><b>#</b> <span>{{ source.getCode }} </span></span><br>
                                </div>
                               <div class="col-sm-12 text-left" style="margin-top: 3px;width: 100%">
                                    <span style="font-size: 10px;">
                                        <span style="width:40% !important">{{ header_res.field_name.se_date.label }}</span> <b>
                                        <span style="word-break: break-all">{{ se_date }}</span></b>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <hr style="margin:0;border: 1px solid #000; " />
                        </div>
                        <div style="clear:both"></div>
                        <div style="width: 50%;float:left">
                            {% if header_res.company_info.print_address %}
                                <span style="word-break: break-word;">
                                    {{ enterprise_address }}
                                </span> <br>
                            {% endif %}
                            {% if header_res.company_info.print_phone_no %}
                                <span>
                                    <b>Ph:</b> {% if source.enterprise.primary_contact_details.contact.phone_no %} {{ source.enterprise.primary_contact_details.contact.phone_no }} {% endif %}
                                </span>
                            {% endif %}
                            {% if header_res.company_info.print_email %}
                                <span>
                                    <b>E-mail:</b>{% if source.enterprise.primary_contact_details.contact.email %} {{ source.enterprise.primary_contact_details.contact.email }} {% endif %}
                                </span>
                            {% endif %}
                            {% if header_res.company_info.print_fax %}
                                <span style="font-size:{{header_res.font_size}}px;">
                                    <b>Fax:</b>{% if source.enterprise.primary_contact_details.contact.fax_no %} {{ source.enterprise.primary_contact_details.contact.fax_no }} {% endif %}
                                </span>
                            {% endif %}
                        </div>
                        <div style="font-size: 12px;word-break: break-all;width:50%;float:left;">
                           {% for reg_item in se_reg_details %}
                                <span class="pdf_registration_{{ reg_detail.label_id }}"><b class="pdf_enterprise_details registration_label" >{{ reg_item.label }}</b><span class="registration_data">:  {{ reg_item.details }}</span><br /></span>
                            {% endfor %}
                        </div>
                        <div style="clear:both"></div>
                        <div style="width:100%">
                            <hr style="margin:0;border: 1px solid #000; " />
                        </div>
                        <div style="width:50%;float:left;">
                            <span style="font-size:14pt;word-break: break-word;"> <b> {{header_res.billing_address.label}}:</b></span>
                            <div style="width:100%">
                                <hr style="margin:0;border: 1px solid #000; " />
                            </div>
                            <div style="word-break: break-word;">
                                <b>
                                    {% if  source.customer.name %}
                                        {{ source.customer.name }}<br>
                                    {% endif %}
                                </b>
                                {% if  source.customer.address_1 %}
                                    {{ source.customer.address_1 }},
                                {% endif %}
                                {% if  source.customer.address_2 %}
                                    {{ source.customer.address_2 }}<br>
                                {% endif %}
                                {% if source.customer.city %}
                                    {{ source.customer.city }},
                                {% endif %}
                                {% if source.customer.state %}
                                    {{ source.customer.state }},
                                {% endif %}
                                {% if source.customer.pin_code %}
                                    {{ source.customer.pin_code }},
                                {% endif %}
                                {% for country in country_list %}
                                    {% if country.country_code == source.customer.country_code %}
                                        {{ country.country_name|upper }}
                                    {% endif %}
                                {% endfor %}<br>
                                {% if source.customer.primary_contact_details.contact.phone_no %}<b>Ph:</b> {{ source.customer.primary_contact_details.contact.phone_no }} {% endif %}<br>
                                {% for reg_detail in source.customer.registration_details %}
                                    {% if reg_detail.label == "GSTIN" and reg_detail.details != "" %}
                                        <b>GSTIN:</b> {{ reg_detail.details }}
                                    {% endif %}
                                {% endfor %}
                                {% for reg_detail in source.customer.registration_details %}
                                    {% if reg_detail.label != "GSTIN" and reg_detail.details != "" %}
                                        <b>{{ reg_detail.label }}:</b> {{ reg_detail.details }}
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        <div style="width:50%;float:left;">
                            {% if header_res.field_name.payment_terms.print %}
                                <div style="margin-left:20px">
                                    <span style="font-weight: bold;word-break:break-all;"><b>{{ header_res.field_name.payment_terms.label }}</b></span>
                                    <span>: {{ payment_terms }}</span>
                                </div>
                            {% endif %}
                            {% if header_res.field_name.expiry_date.print %}
                                <div style="margin-left:20px">
                                    <span style="font-size:12px;font-weight: bold;word-break:break-all;"><b>{{header_res.field_name.expiry_date.label}}</b></span>
                                    <span>: {{ se_expiry_date }}</span>
                                </div>
                            {% endif %}
                        </div>
                        <br>
                        {% if misc_res.print_summary_first_page and appendix_pages > 0 %}
                            <table  id="item_table" style="font-size:11px;width: 100%;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
                                 <thead style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
                                    <tr style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
                                        {% if item_res.item_table.sno.print %}
                                            <th rowspan="2" scope="col" style="width: 6%;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">S.no</th>
                                        {% endif %}
                                        <th rowspan="2" style="width:30%;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">Description</th>
                                        {% if item_res.item_table.hsnsac.print and not item_res.item_table.hsnsac.print_in_itemdetails %}
                                            <th rowspan="2" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">HSN/SAC</th>
                                        {% endif %}
                                        {% if item_res.item_table.quantity.print %}
                                            <th rowspan="2" scope="col" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">Qty</th>
                                        {% endif %}
                                        {% if item_res.item_table.units.print and not item_res.item_table.units.units_in_qty %}
                                            <th rowspan="2" scope="col" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">UOM</th>
                                        {% endif %}
                                        {% if item_res.item_table.unit_price.print %}
                                            <th rowspan="2" scope="col" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">Price<br>USD</th>
                                        {% endif %}
                                        {% if item_res.item_table.discount.print %}
                                            <th rowspan="2" scope="col" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">Disc<br>%</th>
                                        {% endif %}
                                        {% if item_res.item_table.taxable_amount.print %}
                                            <th rowspan="2" scope="col" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">Total<br>USD</th>
                                        {% endif %}
                                    </tr>
                                </thead>
                                <tbody style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
                                    <tr style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
                                        <td colspan="8" style="text-align: center; line-height:30px; font-size:11px;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;"><b>AS PER ANNEXURE TO SE NO:<br>{{ source.getCode }}</b>
                                        </td>
                                    </tr>
                                </tbody>
                                <tfoot>
                                    <tr style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
                                        {% if summary_res.totals.print_subtotal.print %}
                                            <td colspan="3" style="font-size: 12px; line-height: 12px;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: right;"><b>Sub-Total</b></td>
                                        {% endif %}
                                        {% if summary_res.totals.print_qtytotal.print %}
                                            <td style="font-size: 12px; line-height: 12px;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">{{ total_quantity }}</td>
                                        {% endif %}
                                        {% if summary_res.totals.print_total_in_words.print %}
                                            <td colspan="4" style="font-size: 12px; line-height: 12px;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;text-align: right;">{{ total_value | floatformat:2}}</td>
                                        {% endif %}
                                    </tr>
                                     {% for tax in se_taxes %}
                                        <tr style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
                                            <td colspan="3" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: right;">{{ tax.tax_name }} @ {{ tax.tax_rate }}%</td>
                                            <td colspan="5" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: right;">{{ tax.tax_value }}</td>
                                        </tr>
                                    {% endfor %}
                                    {% if source.round_off != 0 %}
                                        <tr id="round-off-container" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
                                            <td colspan="3" style="font-size: 12px; line-height: 12px;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;text-align: right;"><b>Round Off</b></td>
                                            <td colspan="5" style="font-size: 12px; line-height: 12px;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;text-align: right;">{{ source.round_off }}</td>
                                        </tr>
                                    {% endif %}
                                    <tr style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
                                        <td colspan="3" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;text-align: right;"><b>Grand Total</b><span class="total_inclusice_tax hide"> (inclusive tax)</span></td>
                                        <td colspan="5" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;text-align: right;">{{ source.currency.code }} <b>{{ source.grand_total }}</b></td>
                                    </tr>
                                    <tr style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
                                        <td colspan="8" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;"><b>Total Value ({{ source.currency.code }}):</b>  {{ total_in_words|upper }}</td>
                                    </tr>
                                    {% if header_res.field_name.special_instructions.print %}
                                        {% if special_instructions %}
                                            <tr style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
                                                <td colspan="8" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;"><b class="se_special_instruction_txt">{{ header_res.field_name.special_instructions.label }}</b>:  {{ special_instructions }}</td>
                                            </tr>
                                        {% endif %}
                                    {% endif %}
                                    {% if source.notes != "" %}
                                        <tr style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
                                            <td colspan="8" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
                                                <strong>Notes:</strong>
                                                {% autoescape off %} {{ source.notes }} {% endautoescape %}
                                            </td>
                                        </tr>
                                    {% endif %}
                                </tfoot>
                            </table>
                        {% else %}
                             {% include "admin/print_template/se/print/common/se_template_mail_item_table.html" %}
                            {% if source.type != 2 %}
                                {% if summary_res.hsn_summary.print %}
                                    {% include "admin/print_template/se/print/common/se_template_mail_hsn_summary.html" %}
                                {% endif %}
                            {% endif %}
                        {% endif %}
                        {% if misc_res.print_summary_first_page and appendix_pages > 0 %}
                            <div style="page-break-after: always"></div>
                        {% endif %}
                        {% if misc_res.print_summary_first_page and appendix_pages > 0 %}
                            <div class="annexture_se">ANNEXURE TO SALES ESTIMATE {{ source.getCode }} DATED {{ order_date }}</div>
                            <div class="col-sm-12" id="salesestimate_material_table">
                                 {% include "admin/print_template/se/print/common/se_template_mail_item_table.html" %}
                            </div>
                            {% if source.type != 2 %}
                                {% if summary_res.hsn_summary.print %}
                                    {% include "admin/print_template/se/print/common/se_template_mail_hsn_summary.html" %}
                                {% endif %}
                            {% endif %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>