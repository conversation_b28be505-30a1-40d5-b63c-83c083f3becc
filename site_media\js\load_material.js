function generateMaterialAsComboBox(itemId, makeId, isFaulty, dataToSend){
    var material_choices_for_combobox = generateMaterialAsAutoComplete(dataToSend);
    var options = '<option value="All[::][::]">All</option>';
    console.log(material_choices_for_combobox)
    $.each(material_choices_for_combobox, function (i, item) {
        options += '<option value="' + item.id + '[::]' + item.mid + '[::]0">' + item.label + '</option>';
        options += '<option value="' + item.id + '[::]' + item.mid + '[::]1">' + item.label + ' [Faulty]</option>';
    });
    $("#id-material").html(options);
    if (itemId == "All") {
        $("#id-material").val("All[::][::]");
    } else {
        $("#id-material").val(`${itemId}[::]${makeId}[::]${isFaulty}`);
    }
    $("#id-material").trigger("chosen:updated");
}

function generateFrequentlyMaterialAsAutoComplete(dataToSend){
    var frequently_material_choices = [];
    $.ajax({
        url: "/erp/masters/json/material_frequent_list/",
        type: "post",
        datatype: "json",
        data: dataToSend,
        async:false,
        success: function (response) {
            console.log("GENERATE FREQUENTLY MATERIAL", response.materials.length)
            $.each(response.materials, function (i, item) {
                var description = item.name.trim()
                if (item.drawing_no !="" && item.drawing_no!=null){
                    description += " - " + item.drawing_no ;
                }
                if(item.make_id != 1) {
                  description += " [" + item.make_name  + "]" ;
                }
                if(item.is_service == 1) {
                    description = description+ "<span class='service-item-flag'></span>";
                }
                frequently_material_choices.push({
                    alt_uom: item.alternate_unit_count,
                    bom: item.bom,
                    category: "FREQUENTLY USED",
                    hsn: item.hsn_code,
                    stock: item.is_stock,
                    id: item.item_id, 
                    ikey: item.item_id+"-"+item.make_id,
                    label: description.trim(),
                    mid: item.make_id,
                    spq: item.spq,
                    unit:item.unit_name,
                    is_service: item.is_service
                });
            });
        }
    });
    console.log(frequently_material_choices)
    return frequently_material_choices;
}

function generateMaterialAsAutoComplete(dataToSend){
    localStorage.removeItem("material_master");
    localStorage.removeItem("material_master_data");
    localStorage.removeItem("material_master_data_v1");
    var current_enterprise = $("#enterprise_id").val().trim();
    var exsiting_material_list  = JSON.parse(localStorage.getItem('goods_service_master_data'));
    var is_new_material_list = false;

    if(exsiting_material_list) {
        if(current_enterprise != exsiting_material_list[0].enterprise_id) {
            localStorage.removeItem("goods_service_master_data");
            dataToSend["last_accessed_on"] = "";
            is_new_material_list = true;
        }
        else {
            dataToSend["last_accessed_on"] = exsiting_material_list[0].last_accessed_on;
        }
    }
    else {
        dataToSend["last_accessed_on"] = "";
        is_new_material_list = true;
    }

    var material_choices = [];
    var non_material_choices = [];
    var material_master_list = [];
    var material_masters_storage = [];

    $.ajax({
        url: "/erp/masters/json/material_list/",
        type: "post",
        datatype: "json",
        data: dataToSend,
        async:false,
        success: function (response) {
            console.log("GENERATE MATERIAL", response)
            $.each(response.materials, function (i, item) {
                var description = item.name.trim()
                if (item.drawing_no !="" && item.drawing_no!=null){
                    description += " - " + item.drawing_no ;
                }
                if(item.make_name != "") {
                  description += " [" + item.make_name  + "]" ;
                }
                if(item.is_service == 1) {
                 description = description+ "<span class='service-item-flag'></span>";
                }

                material_choices.push({
                    alt_uom: item.alternate_unit_count,
                    bom: item.bom,
                    category: "ALL",
                    hsn: item.hsn_code,
                    stock: item.is_stock,
                    id: item.item_id,
                    ikey: item.item_id+"-"+item.make_id,
                    label: description.trim(),
                    mid: item.make_id,
                    spq: item.spq,
                    unit:item.unit_name,
                    is_service: item.is_service,
                    store_price : item.price,
                    is_faulty : item.is_faulty
                });
            });
            $.each(response.materials_not_in_use, function (i, item) {
                non_material_choices.push({item_id: item.item_id, ikey: item.item_id+"-"+item.make_id });
            });
            if(is_new_material_list) {
                material_master = {}
                material_master ["enterprise_id"] = current_enterprise;
                material_master ["last_accessed_on"] = response.last_accessed_on;
                material_master ["material"] = material_choices;
                material_masters_storage.push(material_master);
                localStorage.setItem('goods_service_master_data', JSON.stringify(material_masters_storage));
            }
            else {
                var existing_material = localStorage.getItem('goods_service_master_data');
                existing_material = existing_material ? JSON.parse(existing_material) : {};
                existing_material[0]["last_accessed_on"] = response.last_accessed_on;
                if(material_choices.length > 0) {
                    $.each(material_choices, function (i, item) {
                        var keyPosition = searckKeyPosition(existing_material[0]["material"], item.ikey);
                        if(keyPosition >= 0) {
                            existing_material[0]["material"].splice(keyPosition, 1);
                            existing_material[0]["material"].splice(keyPosition, 0, item);
                        }
                        else {
                            existing_material[0]['material'].push(item);
                        }
                    });
                }
                if(non_material_choices.length > 0) {
                    $.each(non_material_choices, function (i, item) {
                        var keyPosition = searckKeyPosition(existing_material[0]["material"], item.ikey);
                        existing_material[0]["material"].splice(keyPosition, 1);
                    });
                }
                localStorage.setItem('goods_service_master_data', JSON.stringify(existing_material));
                material_choices = existing_material[0].material;
            }

        }
    });
    return material_choices;
}


function searckKeyPosition(array,valuetofind) {
    for (i = 0; i < array.length; i++) {
        if (array[i]['ikey'] === valuetofind) {
            return i;
        }
    }
    return -1;
}

$.widget( "custom.materialAutocomplete", $.ui.autocomplete, {
    _create: function() {
        this._super();
        this.widget().menu( "option", "items", "> :not(.ui-autocomplete-category)" );
    },
    _renderMenu: function( ul, items ) {
        var that = this,
        currentCategory = "";
        $.each( items, function( index, item ) {
            var li;
            if ( index==0 ) {
                //ul.append( "<b><a  href=''>+ ADD NEW MATERIAL</a><b>" );
            }
            if ( item.category != currentCategory ) {
                ul.append( "<b><li class='ui-autocomplete-category'>" + item.category + "</li></b>" );
                currentCategory = item.category;
            }
            li = that._renderItemData( ul, item );
            if ( item.category ) {
                li.attr( "aria-label", item.category + " : " + item.label );
            }
        });
    }
});