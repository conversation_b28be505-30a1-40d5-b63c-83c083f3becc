"""
"""
import datetime
from decimal import Decimal

import simplej<PERSON>
from dateutil.relativedelta import relativedelta
from django.template import Context
from django.template.loader import get_template

from erp.admin import logger
from erp.commons.backend import sendMail, push_notification
from erp.exceptions import RecordAlreadyExistsException
from erp.masters.backend import MasterService
from erp.models import Party, Enterprise, EnterpriseSubscription, Material, PaymentGatewayMerchantInfo, \
	InvoiceTemplateConfig, Project
from erp.models import User
from erp.properties import MANAGE_PAYMENT_SUCCESS_MAIL_TEMPLATE
from erp.sales.backend import InvoiceService
from settings import SALES_LEDGER_ID, BILLING_ENTERPRISE_ID, BILLING_USER_ID
from util.helper import getAbsolutePath, getFormattedDocPath
from erp.dao import executeQuery

__author__ = 'nandha'


class SubscriptionService:
	def __init__(self):
		self.invoice_service = InvoiceService()

	def getLatestSubscription(self, enterprise_id=None):
		"""
		Gets the latest EnterpriseSubscription record
		:param enterprise_id:
		:return:
		"""
		return self.invoice_service.invoice_dao.db_session.query(EnterpriseSubscription).filter(
			EnterpriseSubscription.enterprise_id == enterprise_id, EnterpriseSubscription.is_active.is_(True)).order_by(
			EnterpriseSubscription.till.desc()).first()

	def getSubscriptionForTransaction(self, enterprise_id=None, transaction_id=None):
		"""
		Gets the EnterpriseSubscription record
		:param enterprise_id:
		:param transaction_id:
		:return:
		"""
		return self.invoice_service.invoice_dao.db_session.query(EnterpriseSubscription).filter(
			EnterpriseSubscription.transaction_id == transaction_id,
			EnterpriseSubscription.enterprise_id == enterprise_id).first()

	def getPgiMerchantInfo(self, enterprise_id=None):
		return self.invoice_service.invoice_dao.db_session.query(PaymentGatewayMerchantInfo).filter(
			PaymentGatewayMerchantInfo.enterprise_id == enterprise_id).first()

	def getMaterials(self, enterprise_id=None, name_like="XSERP%Plan Annual Subscription Fee"):
		"""
		Get/Find the Material that matches the given material name suffix
		:param enterprise_id:
		:param name_like:
		:return:
		"""
		db_session = self.invoice_service.invoice_dao.db_session
		materials = db_session.query(Material).filter(
			Material.enterprise_id == enterprise_id, Material.name.like(name_like)).all()
		logger.info("SubscriptionService: Finding material %s" % len(materials))
		return materials

	def getMaterial(self, enterprise_id=None, drawing_no=None):
		"""
		Get/Find the Material that matches the amount and the given material name suffix
		:param enterprise_id:
		:param drawing_no:
		:return:
		"""
		db_session = self.invoice_service.invoice_dao.db_session
		material = db_session.query(Material).filter(
			Material.enterprise_id == enterprise_id, Material.drawing_no == drawing_no).first()
		logger.info("SubscriptionService: Finding material %s for drawing_no %s" % (material, drawing_no))
		return material

	def createSubscriptionEntry(
			self, enterprise_id=None, paid_material=None, transaction_id=None, is_active=True, payment_info=None):
		"""
		Creates a new EnterpriseSubscription record for the enterprise_id given
		:param enterprise_id:
		:param paid_material: Material object for fetching
		:param transaction_id:
		:param is_active: Default value is True, which means subscription payment has been success.
		:param payment_info:
		:return:
		"""

		db_session = self.invoice_service.invoice_dao.db_session
		db_session.begin(subtransactions=True)
		try:
			if self.getSubscriptionForTransaction(enterprise_id=enterprise_id, transaction_id=transaction_id):
				raise RecordAlreadyExistsException(
					message="Subscription record with tranasction_id(%s) already exists" % transaction_id)

			latest_subscription = self.getLatestSubscription(enterprise_id=enterprise_id)

			if latest_subscription.plan == EnterpriseSubscription.PLAN_TRIAL:
				since = datetime.datetime.today()
			else:
				since = latest_subscription.till
			till = since + relativedelta(years=1)
			enterprise_subscription = EnterpriseSubscription(
				enterprise_id=enterprise_id, plan=paid_material.drawing_no, since=since, till=till, expired_on=till,
				transaction_id=transaction_id, is_active=is_active, payment_info=payment_info)
			max_users = 10
			for specification in paid_material.specifications:
				if specification.parameter == "MAX_USERS":
					max_users = specification.max_value
			if max_users > latest_subscription.enterprise.user_max_count:
				latest_subscription.enterprise.user_max_count = max_users
			db_session.add(enterprise_subscription)
			db_session.commit()
			db_session.refresh(enterprise_subscription)
			return enterprise_subscription
		except Exception as e:
			logger.warn("Creating Subscription entry failed for plan %s" % [enterprise_id, paid_material, e])
			db_session.rollback()
			raise e

	def updateEnterpriseSubscription(self, enterprise_id=None, invoice_id=None):
		self.invoice_service.invoice_dao.db_session.begin(subtransactions=True)
		try:
			latest_subscription = self.getLatestSubscription(enterprise_id=enterprise_id)
			latest_subscription.invoice_id = invoice_id
			self.invoice_service.invoice_dao.db_session.commit()
		except Exception as e:
			self.invoice_service.invoice_dao.db_session.rollback()
			logger.exception("Failed linking invoice to subscription for %s due to %s" % (enterprise_id, e.message))

	def fetchGSTNoFromEnterprise(self, enterprise=None):
		gst_no = ""
		try:
			for item in enterprise.registration_details:
				if item.label.find("GST") != -1:
					gst_no = item.details
					break
		except Exception as e:
			logger.exception("Could not fetch GST Number from enterprise %s [%s]" % (enterprise.id, e.message))
		return gst_no

	def prepareParty(self, enterprise_id=None, user_enterprise_id=None, user_id=None):
		"""

		:param enterprise_id: id of enterprise where the party will be created
		:param user_enterprise_id: id of enterprise where the details of the party can be obtained
		:param user_id: id of user who created this party
		:return: party_id will be returned if the transaction is success else None
		"""
		db_session = self.invoice_service.invoice_dao.db_session
		try:
			enterprise = db_session.query(Enterprise).filter(Enterprise.id == user_enterprise_id).first()
			party = db_session.query(Party).filter(
				Party.code == enterprise.code, Party.enterprise_id == enterprise_id).first()
			if not party:
				db_session.begin(subtransactions=True)
				master_service = MasterService()
				try:
					gst_no = self.fetchGSTNoFromEnterprise(enterprise=enterprise)
					category_id = 3
					if gst_no != "":
						category_id = 1
					party = Party(
						code=enterprise.code.upper(), name=enterprise.name.upper(), address_1=enterprise.address_1,
						address_2=enterprise.address_2, phone_no=enterprise.phone, email=enterprise.email,
						contact_person=enterprise.primary_contact_details.contact.name, city=enterprise.city,
						state=enterprise.state, enterprise_id=enterprise_id, payment_credit_days=0,
						receipt_credit_days=0, currency=enterprise.home_currency.id, category_id=category_id)
					party.setCustomer()
					db_session.add(party)
					db_session.commit()
					db_session.refresh(party)
					party_contact_details = simplejson.loads(simplejson.dumps([{
						'sequence_id': 1, 'name': party.contact, 'email': party.email, 'phone_no': party.phone, 'fax_no': "",
						'is_whatsapp': 0, 'is_deleted': 0}]))
					party_reg_detail = simplejson.loads(simplejson.dumps([
						{"label_id": 1, "details": gst_no, "label": "GSTIN", "is_deleted": 0},
						{"label_id": 2, "details": "", "label": "PAN", "is_deleted": 0}]))
					primary_contact_id = master_service.savePartyContactDetails(
						enterprise_id=enterprise_id, party_contact_details=party_contact_details, user_id=user_id, party_id=party.id)
					party.primary_contact_id = primary_contact_id
					master_service.savePartyRegistration(
						enterprise_id=enterprise_id, party_id=party.id, party_reg_detail=party_reg_detail, user_id=user_id)
				except Exception as e:
					db_session.rollback()
					raise e
			return party
		except Exception as e:
			db_session.rollback()
			logger.warn("Party creation failed due to %s" % e.message)
			return None

	def prepareApprovedInvoiceForSubscription(
			self, enterprise_id=None, party=None, prepared_by=None, amount=0, paid_material=None, remarks=None, discount = 0.0):
		"""

		:param enterprise_id: id of enterprise where the invoice to be created
		:param party:
		:param prepared_by: prepared_by and approved_by are same for this invoice
		:param amount: amount includes IGST/(SGST+CGST) of 18% split into amount = 5900 = 5000 + 900
		:param paid_material: Material object for the amount paid
		:param remarks: Remarks for the paid_material
		:return:
		"""
		try:
			prepared_date = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
			db_session = self.invoice_service.invoice_dao.db_session
			taxes = [
				{'tax_code': 'SGST9', 'tax_type': '', 'rate': 9.0},
				{'tax_code': 'CGST9', 'tax_type': '', 'rate': 9.0}
			]
			tax = [{'tax_code': 'IGST18', 'tax_type': '', 'rate': 18.0}]
			applicable_taxes = tax
			for item in party.registration_details:
				if item.label.find("GST"):
					if item.details[:2] == self.fetchGSTNoFromEnterprise(enterprise=party.enterprise)[:2]:
						applicable_taxes = taxes
					else:
						applicable_taxes = tax

			invoice_template = db_session.query(InvoiceTemplateConfig).filter(
					InvoiceTemplateConfig.enterprise_id == enterprise_id).first()
			enterprise = db_session.query(Enterprise).filter(
				Enterprise.id == enterprise_id).first()
			project_code = db_session.query(Project.id).filter(
				Project.enterprise_id == BILLING_ENTERPRISE_ID, Project.name == "XSERP", Project.is_active != -1).first()
			invoice_data = {
				'type': 'GST', 'prepared_by': prepared_by, 'enterprise_id': enterprise_id,
				'currency_id': enterprise.home_currency_id, 'id': None,
				'order_accept_date': prepared_date, 'party_id': party.id,
				'currency_conversion_rate': 1.0, 'project_code': project_code[0],
				'po_date': prepared_date, 'tags': ["xserp", "online", "subscription"],
				'issued_on': prepared_date,
				'sale_account_id': SALES_LEDGER_ID,
				'goods_already_supplied': False,
				'grand_total': amount,
				'charges': [], 'taxes': [], 'deliver_to': '', 'gstin': '',
				'notes': invoice_template.template_misc_details.notes if invoice_template else '',
				'tax_payable_on_reverse_charge': False,
				'remarks': 'Auto created invoice for xserp online Subscription',
				'items': [{
					'DELETE':False,
					'material_type': 0,
					'item_id': paid_material.material_id, 'make_id': 1, 'is_faulty': 0, 'oa_no': None,
					'rate': paid_material.price, 'discount': discount,
					'unit_id': '',
					'hsn_code': paid_material.tariff_no,
					'enterprise_id': enterprise_id,
					'alternate_unit_id': 1,
					'taxes': applicable_taxes,
					'is_returnable': False,
					'entry_order': 1,
					'remarks': remarks,
					'quantity': 1.0,
					'inspection_log': paid_material.inspection_log if hasattr(paid_material, 'inspection_log') else []
				}]
			}
			invoice = self.invoice_service.saveInvoice(
				enterprise_id=enterprise_id, user_id=prepared_by, invoice_data=invoice_data)
			self.invoice_service.approveInvoice(
				invoice_id=invoice.id, remarks="Auto Approve for Subscription Payment",
				enterprise_id=enterprise_id, approved_by=prepared_by)
			return invoice
		except Exception as e:
			logger.exception("Could not create invoice for subscription for {party_id: %s, amount: %s} due to %s" % (
				party, amount, e.message))
			return None

	def sendPaymentSuccessfulMail(self, invoice, paid_user_id=None, latest_subscription=None, amount=None, enterprise_id=None):
		"""

		:param invoice: subscription invoice
		:param paid_user_id: user id
		:param latest_subscription: latest EnterpriseSubscription record for the enterprise_id given
		:param amount: amount includes IGST/(SGST+CGST) of 18% split into amount = 5900 = 5000 + 900
		:return:
		"""
		try:
			logger.info("Sending invoice mail for xserp online subscription ...")
			cc_list = ()
			paid_user = self.invoice_service.invoice_dao.db_session.query(User).filter(User.id == paid_user_id).first()
			if paid_user and paid_user.email != invoice.customer.email:
				cc_list = (paid_user.email,)
			# Reusing invoice document generation.
			# The document will stored in INVOICE_PDF_PATH
			self.invoice_service.generateInvoiceDocument(
				invoice_id=invoice.id, enterprise_id=invoice.enterprise_id, invoice_type='Sales', user=invoice.approver)

			context = Context({
				'customer_name': paid_user.__repr__(), 'amount': "{:0.2f}".format(Decimal(amount)),
				'transaction_id': latest_subscription.transaction_id, 'invoice_no': invoice.getCode(),
				'transaction_time': invoice.prepared_on.strftime('%b %d, %Y'), 'plan': latest_subscription.plan,
				'next_renewal_date': latest_subscription.till.strftime('%b %d, %Y'), 'company_name': invoice.customer.name})

			mail_body = get_template(getAbsolutePath(MANAGE_PAYMENT_SUCCESS_MAIL_TEMPLATE)).render(context)

			sendMail(
				recipients=(invoice.customer.email,), cc_list=cc_list,
				subject="[xserp] Payment received for subscription",
				body=mail_body,
				files=({"path": getAbsolutePath(getFormattedDocPath(code=invoice.getCode(), id=invoice.id)), "name": "xserp_invoice.pdf"},))
		except Exception as e:
			logger.warn("Could not send email... %s" % e.message)

	def processPaymentSuccessEvent(
			self, enterprise_id=None, user_id=None, transaction_id=None, amount=0, payment_info=None, drawing_no=None):
		try:
			logger.info("""Payment Information 
			{enterprise_id: %s, user_id: %s, transaction_id: %s, amount: %s}""" % (
				enterprise_id, user_id, transaction_id, amount))
			paid_material = self.getMaterial(enterprise_id=BILLING_ENTERPRISE_ID, drawing_no=drawing_no)

			# Creating subscription entry and setting user limit for the subscription
			try:
				latest_subscription = self.createSubscriptionEntry(
					enterprise_id=enterprise_id, paid_material=paid_material, transaction_id=transaction_id, is_active=True,
					payment_info=payment_info)
			except RecordAlreadyExistsException as e:
				logger.warn(e.message)
				latest_subscription = self.getSubscriptionForTransaction(enterprise_id=enterprise_id, transaction_id=transaction_id)
			try:
				if latest_subscription.invoice_id is not None:
					raise RecordAlreadyExistsException("Invoice has been prepared already for the transaction: %s" % transaction_id)

				# Preparing party for the logged in user's enterprise in billing enterprise
				party = self.prepareParty(enterprise_id=BILLING_ENTERPRISE_ID, user_enterprise_id=enterprise_id, user_id=user_id)

				# Preparing and approving invoice for the fetched material to the party prepared
				invoice = self.prepareApprovedInvoiceForSubscription(
					enterprise_id=BILLING_ENTERPRISE_ID, party=party, prepared_by=BILLING_USER_ID, amount=amount,
					paid_material=paid_material, remarks="Since %s to %s" % (
						latest_subscription.since.strftime("%Y-%m-%d"), latest_subscription.till.strftime("%Y-%m-%d")))
				# Updating invoice_id to the latest subscription entry
				self.updateEnterpriseSubscription(enterprise_id=enterprise_id, invoice_id=invoice.id)
				message = """xserp has received payment for subscription. 
				Your enterprise has been activated! Trouble using xserp? Login again!"""
				push_notification(
					enterprise_id=enterprise_id, sender_id=user_id, message=message, include_sender=True,
					action='permission')

				# Sending invoice to the user via email
				self.sendPaymentSuccessfulMail(
					invoice=invoice, paid_user_id=user_id, latest_subscription=latest_subscription, amount=amount, enterprise_id=enterprise_id)
			except RecordAlreadyExistsException as e:
				logger.warn(e.message)
				invoice = latest_subscription.invoice
			logger.info('EnterpriseSubscription =  {"transaction": "%s", "invoice": "%s", "till": "%s"}' % (
				transaction_id, invoice.getCode(), latest_subscription.till))
			return invoice
		except Exception as e:
			logger.exception("Exception on payment... %s" % e.message)
			raise e

	def processPaymentFailureEvent(
			self, enterprise_id=None, user_id=None, transaction_id=None, amount=0, payment_info=None, drawing_no=None):
		try:
			logger.info("""Payment Information 
			{enterprise_id: %s, user_id: %s, transaction_id: %s, amount: %s}""" % (
				enterprise_id, user_id, transaction_id, amount))
			paid_material = self.getMaterial(enterprise_id=BILLING_ENTERPRISE_ID, drawing_no=drawing_no)
			try:
				# Creating subscription entry and setting user limit for the subscription
				latest_subscription = self.createSubscriptionEntry(
					enterprise_id=enterprise_id, paid_material=paid_material, transaction_id=transaction_id, is_active=False,
					payment_info=payment_info)

				return latest_subscription
			except RecordAlreadyExistsException as e:
				logger.warn("Processing Failed transaction with error... %s" % e.message)
				return None
		except Exception as e:
			logger.exception("Exception on payment... %s" % e.message)
			raise e

	def new_party_creation(self, enterprise_id=None, party_details=None, user_id=None):
		try:
			db_session = self.invoice_service.invoice_dao.db_session
			party = db_session.query(Party).filter(
				Party.code == party_details["code"], Party.enterprise_id == party_details["enterprise_id"]).first()
			if party:
				return party
			db_session.begin(subtransactions=True)
			master_service = MasterService()
			gst_no = self.NewfetchGSTNoFromEnterprise(party_details=party_details)
			category_id = 1 if gst_no else 3
			party = Party(
				code=party_details["code"].upper(),
				name=party_details["name"].upper(),
				address_1=party_details["address_1"],
				address_2=party_details["address_2"],
				phone_no=party_details["phone_no"],
				email=party_details["email"],
				contact_person=party_details["contact_person"],
				city=party_details["city"],
				state=party_details["state"],
				enterprise_id=party_details["enterprise_id"],
				payment_credit_days=0,
				receipt_credit_days=0,
				currency=party_details["currency"],
				category_id=category_id)
			party.setCustomer()
			db_session.add(party)
			db_session.commit()
			db_session.refresh(party)
			party_contact_details = [{
				'sequence_id': 1, 'name': party.contact, 'email': party.email, 'phone_no': party.phone,
				'fax_no': "", 'is_whatsapp': 0, 'is_deleted': 0}]
			party_reg_detail = simplejson.loads(simplejson.dumps([
				{"label_id": 1, "details": gst_no, "label": "GSTIN", "is_deleted": 0},
				{"label_id": 2, "details": "", "label": "PAN", "is_deleted": 0}]))

			primary_contact_id = master_service.savePartyContactDetails(
				enterprise_id=enterprise_id, party_contact_details=party_contact_details, user_id=user_id,
				party_id=party.id)
			party.primary_contact_id = primary_contact_id
			master_service.savePartyRegistration(
				enterprise_id=enterprise_id, party_id=party.id, party_reg_detail=party_reg_detail, user_id=user_id)
			return party
		except Exception as e:
			logger.warn("An error occurred in new party creation: %s" % e.message)

	def party_invoice_creation(self, party_details, user_id, amount, drawing_no, remarks, discount):
		try:
			party = self.new_party_creation(
				enterprise_id=BILLING_ENTERPRISE_ID, party_details=party_details, user_id=user_id)
			paid_material = self.getMaterial(enterprise_id=BILLING_ENTERPRISE_ID, drawing_no=drawing_no)
			invoice = self.prepareApprovedInvoiceForSubscription(
				enterprise_id=BILLING_ENTERPRISE_ID, party=party, prepared_by=BILLING_USER_ID, amount=amount,
				paid_material=paid_material, remarks=remarks, discount=discount)
			invoice_service_instance = InvoiceService()
			invoice_service_instance.generateInvoiceDocument(
				invoice_id=invoice.id,
				enterprise_id=invoice.enterprise_id,
				invoice_type='Sales',
				user=invoice.approver)
			invoice_id = invoice.id
			doc_query = "SELECT document_pdf FROM invoice_document where invoice_id = %s;" % invoice_id
			document_pdf = executeQuery(doc_query)
			invoice_code = invoice.invoice_code
			prepared_on_str = invoice.prepared_on
			prepared_on = prepared_on_str.strftime('%Y-%m-%d %H:%M:%S')
			invoice_dict = {
				'invoice_id': invoice_id,
				'invoice_code': invoice_code,
				'prepared_on': prepared_on,
				'document_pdf': document_pdf[0][0]}
			return invoice_dict
		except Exception as e:
			logger.error("An error occurred in party invoice creation: %s", str(e))
			return None

	@staticmethod
	def NewfetchGSTNoFromEnterprise(party_details=None):
		gst_no = ""
		try:
			registration_details = party_details.get('registration_details', [])
			for item in registration_details:
				if item.get('label', '').find("GST") != -1:
					gst_no = item.get('details', '')
					break
		except Exception as e:
			logger.exception("Could not fetch GST Number from party details [%s]" % e)
		return gst_no

