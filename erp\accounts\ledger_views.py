# coding=utf-8
import json
import os
from datetime import datetime, timed<PERSON>ta

import simplejson
from django.http import HttpResponseRedirect, HttpResponse
from django.template.response import TemplateResponse
from django.utils.encoding import smart_str
from sqlalchemy import and_, func
from sqlalchemy.orm import make_transient

from erp import helper
from erp.accounts import logger
from erp.accounts.backend import AccountService
from erp.accounts.document_compiler import AccountsPDFGeneration
from erp.auth import USER_IN_SESSION_KEY, SESSION_KEY, ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.commons.backend import push_notification
from erp.dao import executeQuery
from erp.forms import LedgerForm, AccountGroupForm
from erp.models import Ledger, AccountGroup, Party, PartyLedgerMap, Enterprise, EnterpriseImages
from erp.notifications import PUSH_NOTIFICATION
from erp.properties import MANAGE_LEDGER_TEMPLATE, <PERSON><PERSON><PERSON>_LEDGER_URL, LOGIN_URL, TEMPLATE_TITLE_KEY, \
	OUTSTANDING_DOC_PATH
from settings import SQLASession
from util.api_util import response_code, JsonUtil
from util.helper import copyFormToEntity, getFYDateRange, getFYStartDate, readFile

__author__ = 'saravanan'


def manageLedger(request):
	"""
	Renders the main page to manage Ledger.
	:param request:
	:return:
	"""
	logger.info('Inside Manage Ledger... ')
	request_handler = RequestHandler(request)
	enterprise = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise
	ledger_form = LedgerForm(initial={'enterprise_id': enterprise.id})
	root_group = SQLASession().query(AccountGroup).filter(
		AccountGroup.enterprise_id == enterprise.id, AccountGroup.id == 0).first()
	account_group_options = helper.constructOptionTree(
		root=root_group, selected_group_id=None, for_subgroup_creation=True, for_ledger_creation=True,
		for_import_voucher=True)
	accounts_service = AccountService()
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	next_opening_date = accounts_service.accounts_dao.getNextBookOpeningDate(enterprise_id=enterprise_id)
	return TemplateResponse(template=MANAGE_LEDGER_TEMPLATE, request=request, context={
		'ledgerForm': ledger_form, 'account_group_options': account_group_options,
		'account_group_form': AccountGroupForm(prefix='account_group', initial={'enterprise_id': enterprise_id}),
		TEMPLATE_TITLE_KEY: "Ledger", 'next_opening_date': next_opening_date})


def populateLedgerList(request):
	"""
	Loads the ledger List with choices appropriate for the enterprise in context.

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise
	ledgers = SQLASession().query(
		Ledger.name, Ledger.description, Ledger.id, Ledger.status, Ledger.is_default,
		AccountGroup.name.label('group_name')).outerjoin(AccountGroup, and_(
			Ledger.group_id == AccountGroup.id, Ledger.enterprise_id == AccountGroup.enterprise_id)).filter(
		Ledger.enterprise_id == enterprise.id).group_by(Ledger.id).all()
	ledgers_list = []
	for ledger in ledgers:
		ledgers_list_dict = dict()
		ledgers_list_dict["id"] = ledger.id
		ledgers_list_dict["name"] = ledger.name
		ledgers_list_dict["group_name"] = ledger.group_name
		ledgers_list_dict["description"] = ledger.description
		ledgers_list_dict["status"] = ledger.status
		ledgers_list_dict["is_default"] = ledger.is_default
		ledgers_list.append(ledgers_list_dict)
	return HttpResponse(content=simplejson.dumps(ledgers_list), mimetype='application/json')


def saveLedger(request):
	"""
	Save Ledger
	Persists them in appropriate tables after validation.

	:param request:
	:return:
	"""
	logger.info('Inside Save Ledgers...')
	request_handler = RequestHandler(request)
	ledger_to_be_saved = Ledger()
	db_session = SQLASession()
	db_session.begin(subtransactions=True)
	try:
		ledger_form = LedgerForm(data=request_handler.getPostData())
		old_ledger_name = None
		if ledger_form.is_valid():
			if ledger_form.cleaned_data['id'] is not None and ledger_form.cleaned_data['id'] != '':
				ledger_to_be_saved = db_session.query(Ledger).filter(
					Ledger.id == ledger_form.cleaned_data['id']).first()
				old_ledger_name = ledger_to_be_saved.name
			_copyLedgerFormToEntity(ledger_form, ledger_to_be_saved)
			new_ledger_name = ledger_to_be_saved.name
			if ledger_to_be_saved.created_by is None:
				ledger_to_be_saved.created_by = request_handler.getSessionAttribute(SESSION_KEY)
			logger.debug(db_session.dirty)
			db_session.add(ledger_to_be_saved)
			if old_ledger_name:
				message = PUSH_NOTIFICATION['ledger_saved'] % (old_ledger_name, new_ledger_name)
				push_notification(
					enterprise_id=ledger_to_be_saved.enterprise_id, sender_id=ledger_to_be_saved.created_by,
					module_code="LEDGER", message=message)
			db_session.commit()
		else:
			logger.info('Ledger Form validation failed...\n%s' % ledger_form.errors)
			db_session.rollback()
			return TemplateResponse(
				request=request, template=MANAGE_LEDGER_TEMPLATE, context={'ledgerForm': ledger_form, 'edit': False})
		return HttpResponseRedirect(MANAGE_LEDGER_URL)
	except Exception as e:
		logger.exception('Saving Ledger Failed... %s' % e.message)
		db_session.rollback()
		return HttpResponseRedirect(MANAGE_LEDGER_URL)


def update_ledger_status(request):
	response = {"status_code": 200, "message": "success"}
	request_handler = RequestHandler(request)
	db_session = SQLASession()
	db_session.begin(subtransactions=True)
	try:
		ledger_id = int(request_handler.getPostData("id"))
		enterprise_id = int(request_handler.getPostData("enterprise_id"))
		status = int(request_handler.getPostData("status"))
		if status in (0, 1):
			ledger = db_session.query(Ledger).filter(
				Ledger.id == ledger_id, Ledger.enterprise_id == enterprise_id).first()
			ledger.status = status
			db_session.commit()
			response["id"] = ledger_id
			response["status"] = status
			return HttpResponse(json.dumps(response), status=200, mimetype='application/json')
		else:
			response = {"status_code": 400, "message": "Bad request"}
			return HttpResponse(json.dumps(response), status=400, mimetype='application/json')
	except Exception as e:
		db_session.rollback()
		logger.info("Failed to update the ledger status %s", e)
		response = {"status_code": 500, "message": "Internal server Error"}
		return HttpResponse(json.dumps(response), status=500, mimetype='application/json')


def editLedger(request):
	"""
	Prepares and Renders a Ledger Edit screen with details of a Ledger requested.

	:param request:
	:return:
	"""
	logger.info('Editing a Ledger...')
	request_handler = RequestHandler(request)
	enterprise = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise
	if request_handler.isPostRequest():
		ledger_id = request_handler.getPostData('ledger_id')
		logger.debug("Ledger Id selected: %s" % ledger_id)
		ledger_under_edit = SQLASession().query(Ledger).filter(Ledger.id == ledger_id).first()
		party = SQLASession().query(PartyLedgerMap, Party.name.label('party_name'), func.concat(
			Party.address_1, Party.address_2, Party.city, Party.state).label('party_address'), Party.phone.label(
			'party_phone_no')).join(
			PartyLedgerMap.party).filter(PartyLedgerMap.ledger_id == ledger_id).first()
		party_name = party.party_name if party else ""
		# Formatting opening date
		since = request_handler.getPostData('opening_as_on')
		last_thirty_days = datetime.today() - timedelta(days=30)
		if since:
			opening_as_on = datetime.strptime("%s 00:00:00" % since, '%Y-%m-%d %H:%M:%S')
		else:
			# opening_as_on = getFYDateRange(fy_start_day=ledger_under_edit.enterprise.fy_start_day)[0]
			opening_as_on = last_thirty_days

		# Formatting closing date
		till = request_handler.getPostData('closing_as_on')
		if till:
			closing_as_on = datetime.strptime("%s 23:59:59" % till, '%Y-%m-%d %H:%M:%S')
		else:
			# closing_as_on = getFYDateRange(fy_start_day=ledger_under_edit.enterprise.fy_start_day)[1]
			closing_as_on = datetime.today()

		approved_only = request_handler.getPostData("approved_only")
		ledger_form = LedgerForm(
			initial=_getLedgerFormInitializerFromEntity(ledger_under_edit, opening_as_on, closing_as_on, approved_only))
		response = TemplateResponse(template=MANAGE_LEDGER_TEMPLATE, request=request, context={
			'ledgerForm': ledger_form, 'voucher_entries': ledger_under_edit.getVoucherEntries(
				since=opening_as_on, till=closing_as_on, approved_only=approved_only), 'party_name': party_name,
			'party_address': party.party_address if party else "", 'party_phone_no': party.party_phone_no if party else "",
			'is_default_ledger': ledger_under_edit.is_default,
			'account_group_form': AccountGroupForm(prefix='account_group', initial={'enterprise_id': enterprise.id}),
			'is_ledger_billable': ledger_under_edit.billable, TEMPLATE_TITLE_KEY: ledger_under_edit.name})
		make_transient(ledger_under_edit)
		return response
	else:
		HttpResponseRedirect(LOGIN_URL)


def deleteLedger(request):
	"""
	Soft Delete of Ledger - setting the status to 0

	:param request:
	:return:
	"""
	logger.info('Deleting a Ledger...')
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	db_session = SQLASession()
	db_session.begin(subtransactions=True)
	try:
		delete_ledger_name = request_handler.getPostData('deleteCode')
		ledger_to_be_deleted = db_session.query(Ledger).filter(
			Ledger.name == delete_ledger_name, Ledger.enterprise_id == enterprise_id).first()
		if not ledger_to_be_deleted.vouchers:
			db_session.delete(ledger_to_be_deleted)
		db_session.commit()
	except Exception as e:
		db_session.rollback()
		logger.exception(e)
	return HttpResponseRedirect(MANAGE_LEDGER_URL)


def _copyLedgerFormToEntity(ledger_form, entity):
	if entity is None or entity.id is None:
		return copyFormToEntity(ledger_form, entity)
	entity.id = ledger_form.cleaned_data['id']
	entity.group_id = ledger_form.cleaned_data['group_id']
	entity.name = ledger_form.cleaned_data['name']
	entity.description = ledger_form.cleaned_data['description']
	entity.enterprise_id = ledger_form.cleaned_data['enterprise_id']
	entity.billable = ledger_form.cleaned_data['billable']
	return entity


def _getLedgerFormInitializerFromEntity(
		entity=Ledger(), opening_on=None, closing_on=datetime.now(), approved_only=False):
	"""
	Constructing Ledger and its Book of Entries VO to be displayed in UI.
	
	:param entity: 
	:param opening_on: 
	:param closing_on: 
	:param approved_only: 
	:return: 
	"""
	if not opening_on:
		opening_on = getFYStartDate(fy_start_day=entity.enterprise.fy_start_day)
	ledger_opening_balance = entity.getBalance(till=opening_on, approved_only=approved_only)
	logger.debug("Opening Balance: %s" % ledger_opening_balance)
	ledger_closing_balance = entity.getBalance(till=closing_on, approved_only=approved_only)
	return {
		'id': entity.id, 'group_id': entity.group_id, 'group_label': "%s" % entity.group.name, 'name': entity.name,
		'description': entity.description, 'enterprise_id': entity.enterprise_id,
		'closing_debit': (ledger_closing_balance * -1) if ledger_closing_balance < 0 else 0.00,
		'closing_credit': ledger_closing_balance if ledger_closing_balance > 0 else 0.00,
		'opening_debit': (ledger_opening_balance * -1) if ledger_opening_balance < 0 else 0.00,
		'opening_credit': ledger_opening_balance if ledger_opening_balance > 0 else 0.00,
		'opening_as_on': opening_on,
		'closing_as_on': closing_on,
		'total_debit': entity.getDebitValueForPeriod(
			since=opening_on, till=closing_on, approved_only=approved_only),
		'total_credit': entity.getCreditValueForPeriod(
			since=opening_on, till=closing_on, approved_only=approved_only),
		'approved_only': approved_only, 'billable': entity.billable, 'is_default': entity.is_default}


def populateLedgerChoices(request):
	"""

	Loads Ledgers as choice options to be selected.
	:param request:
	:return:
	"""
	logger.info("Populating Ledger choices to be listed in Choice fields...")
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		ledger_choices = helper.populateAccountLedger(enterprise_id)
		logger.info("No of Ledgers fetched: %s" % len(ledger_choices))
		return HttpResponse(content=simplejson.dumps(ledger_choices), mimetype='application/json')
	except Exception as e:
		logger.info("Failed to fetch ledger choices: %s" % e)
	return HttpResponse(content=simplejson.dumps(""), mimetype='application/json')


def generatePaymentSummaryDocument(request):
	"""
	Generates and returns the payment summary for the ledger as PDF document.
	:param request:
	:return:
	"""
	logger.info("Generating payment document...")
	try:
		request_handler = RequestHandler(request)
		db_session = SQLASession()
		data = request_handler.getPostData('data')
		ageing_table = request_handler.getPostData('ageing_table')
		outsourcing_value = request_handler.getPostData('outsourcing_value')
		advance_outstanding = request_handler.getPostData('advance_outstanding')
		section_title = request_handler.getPostData('section_title')
		ledger_id = request_handler.getPostData('ledger_id')
		from_date = request_handler.getPostData('from_date')
		to_date = request_handler.getPostData('to_date')
		ledger_name = request_handler.getPostData('ledger_name')
		show_advance_outsourcing = request_handler.getPostData('show_advance_outsourcing')
		document_type = request_handler.getPostData('response_data_type')  # data, file
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		user = request_handler.getSessionAttribute(USER_IN_SESSION_KEY)
		enterprise = db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
		enterprise_image = db_session.query(EnterpriseImages).filter(EnterpriseImages.enterprise_id == enterprise.id).first()
		logger.info('Ledger to be printed: %s' % ledger_name)
		party_details = db_session.query(
			PartyLedgerMap, Party.name.label('party_name'), Party.address_1.label('address_1'),
			Party.address_2.label('address_2'), Party.city.label('city'), Party.state.label('state'),
			Party.phone.label('phone_no')).join(
			PartyLedgerMap.party).filter(PartyLedgerMap.ledger_id == ledger_id).first()
		document_generator = AccountsPDFGeneration(target_file_path=OUTSTANDING_DOC_PATH % (str(ledger_id) + str(enterprise.id)))
		document = document_generator.generatePDF(
			enterprise=enterprise, data=data, ledger_name=ledger_name, party_details=party_details, section_title=section_title,
			ledger_id=ledger_id, advance_outstanding=advance_outstanding, enterprise_image=enterprise_image,
			last_name=user.last_name, first_name=user.first_name, show_advance_outsourcing=show_advance_outsourcing,
			from_date=from_date, to_date=to_date, outsourcing_value=outsourcing_value, ageing_table=ageing_table)
		filename = smart_str("%s(%s).pdf" % (ledger_id, enterprise_id))
		data = readFile(document)
		if document_type == "file":
			# mimetype is replaced by content_type for django 1.7
			response = HttpResponse(data, mimetype='application/force-download')
			response['Content-Disposition'] = 'attachment; filename=%s' % filename
			return response
		else:
			response = response_code.success()
			response['ext'] = "pdf"
			response['data'] = "data:application/pdf;base64,%s" % data.encode('base64')
			response['url'] = os.path.join(os.path.dirname(__file__), document)
	except Exception as e:
		logger.info("Failed to generate the outsourcing document: %s" %e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def loadVoucherBillDetails(request):
	"""

	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		ledger_id = request_handler.getPostData('ledger_id')
		is_debit = request_handler.getPostData('is_debit')
		from_date = request_handler.getPostData('from_date')
		to_date = request_handler.getPostData('to_date')
		start_date, end_date = JsonUtil.getDateRange(
			rh=request_handler, since_session_key="%s" % from_date, till_session_key="%s" % to_date)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)

		party_details = SQLASession().query(Party.name.label('party_name'), func.concat(
			Party.address_1, Party.address_2, Party.city, Party.state).label('party_address'), Party.phone.label(
			'party_phone_no'), Party.email.label('party_email')).join(
			PartyLedgerMap).filter(PartyLedgerMap.ledger_id == ledger_id).first()

		bill_and_voucher_details = """SELECT 
		voucher_particulars.voucher_id AS voucher_id, 
		voucher_particulars.is_debit AS is_debit,
		voucher_particulars.amount AS tnx_value,
		voucher.voucher_no AS voucher_no,
		DATE_FORMAT(voucher.voucher_date, '%d %b, %Y') AS txn_date,
		voucher.voucher_date AS voucher_date,
		voucher.transaction_instrument_no AS txn_no,
		ledger_bills.bill_no AS bill_no,
		ledger_bills.id AS bill_id,
		DATE_FORMAT(ledger_bills.bill_date, '%d %b, %Y') AS bill_date,
		ledger_bills.net_value AS ledger_bills_net_value,
		IFNULL(
			IF(
				voucher_particulars.is_debit = 1, ledger_bill_settlements.dr_value, ledger_bill_settlements.cr_value), 
				0.00) AS amount_settled
		FROM
		voucher_particulars
		LEFT OUTER JOIN
		voucher ON voucher_particulars.voucher_id = voucher.id
		LEFT OUTER JOIN 
		ledger_bill_settlements ON ledger_bill_settlements.voucher_id = voucher_particulars.voucher_id
		AND ledger_bill_settlements.enterprise_id = voucher_particulars.enterprise_id
		JOIN
		ledger_bills ON ledger_bills.id = ledger_bill_settlements.bill_id
		AND ledger_bills.enterprise_id = ledger_bill_settlements.enterprise_id
		AND ledger_bills.ledger_id = {ledger_id}
		WHERE
		voucher_particulars.ledger_id = {ledger_id}
		AND voucher_particulars.enterprise_id = {enterprise_id}
		AND voucher.voucher_date BETWEEN '{from_date}' AND '{to_date}'
		AND voucher_particulars.is_debit = {is_debit} 
		GROUP BY voucher_particulars.voucher_id,ledger_bills.id 
		ORDER BY voucher.voucher_date""".format(
			ledger_id=ledger_id, from_date=start_date, to_date=end_date, enterprise_id=enterprise_id, is_debit=is_debit)
		result = executeQuery(bill_and_voucher_details, as_dict=True)

		consolidated_bill_and_voucher_details = {}

		for detail in result:
			voucher_date = datetime.strptime(
				datetime.strftime(detail['voucher_date'], "%Y-%m-%d") + " 23:59:59", "%Y-%m-%d %H:%M:%S")
			outstanding_query = """SELECT 
				IFNULL((IF(SUM(ledger_bill_settlements.cr_value) - SUM(ledger_bill_settlements.dr_value) > 0, 
					SUM(ledger_bill_settlements.cr_value) - SUM(ledger_bill_settlements.dr_value), 
					-1 * (SUM(ledger_bill_settlements.cr_value) - SUM(ledger_bill_settlements.dr_value))) - 
					{amount_settled}), 0.00) AS outstanding,
				IFNULL(IF({is_debit} = 1, SUM(ledger_bill_settlements.cr_value), SUM(
						ledger_bill_settlements.dr_value)),0.00) AS bill_value
				FROM
				ledger_bill_settlements
				WHERE
				ledger_bill_settlements.bill_id = {bill_id}
				AND ledger_bill_settlements.enterprise_id = {enterprise_id}
				AND ledger_bill_settlements.voucher_id <> {voucher_id}
				AND DATE_FORMAT(ledger_bill_settlements.created_on,'%Y-%m-%d %H:%M:%S' ) <= '{voucher_date}'""".format(
					amount_settled=detail['amount_settled'],
					enterprise_id=enterprise_id, is_debit=detail['is_debit'], voucher_id=detail['voucher_id'],
					voucher_date=voucher_date, bill_id=detail['bill_id'])
			result = executeQuery(outstanding_query, as_dict=True)
			detail.pop('voucher_date')
			detail['outstanding'] = result[0]['outstanding']
			detail['bill_value'] = result[0]['bill_value']
			if detail['voucher_id'] in consolidated_bill_and_voucher_details.keys():
				detail['voucher_date'] = ''
				consolidated_bill_and_voucher_details[detail['voucher_id']].append(detail)
			else:
				detail['voucher_date'] = ''
				consolidated_bill_and_voucher_details[detail['voucher_id']] = [detail]
		response = response_code.success()
		response['bill_and_voucher_details'] = consolidated_bill_and_voucher_details
		response['party_details'] = party_details
	except Exception as e:
		logger.info("Failed to load voucher bill details:%s" %e)
		response = response_code.failure()
		response["response_message"] = "Failed to load Voucher Bill details..."
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')