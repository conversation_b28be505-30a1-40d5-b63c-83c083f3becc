/***********************************Common Utilities*******************************/
/**
 * formats material name from the item record provided
 */
function formatMaterialDescription(item) {
    if (item.item == undefined) {
        item.item = item.item_name;
    }
    var drawingNumber="", description="";
    description = item.item
    if (item.drawing_no != null && trim(item.drawing_no) != "") {
        description = `${description} - ${item.drawing_no}`;
        drawingNumber = item.drawing_no;
    }
    if (item.make_name != null && item.make_name != "" && item.make_name != "[]") {
        var make_name = constructDifferentMakeName(item.make_name);
        if (make_name){
            description = `${description} <i>[${make_name}]</i>`;
        }
    }
    if (item.is_faulty == 1) {
        description = `${description} [Faulty]`;
    }
    return [drawingNumber, description];
}

var openReceipt = function (receiptId="", action="/erp/stores/grn/") {
    if ($("#link-ReceiptForm").text() == "") {
        $("body").append(`<div class="hide">
            <form id="link-ReceiptForm" method="POST" action="${action}">
                <input type="hidden" name="csrfmiddlewaretoken" value="${$('input[name="csrfmiddlewaretoken"]').val()}">
                <input type="hidden" name="receipt_no" id="link-ReceiptId" value="" />
            </form>
        </div>`);
    }
    $("#link-ReceiptId").val(receiptId);
    $("#link-ReceiptForm").attr("target", "_blank");
    $("#link-ReceiptForm").submit();
}

$(document).ready(function(){
	DatePickerIconClick();
	TooltipInit();
	SingleNewDatePickerInit();
	enterKeyDoNothingEvent();
    browseButtonInit();
    bootstrapModalBodyHeightInit();
    $('.unit_box').each(function(){
        populateUnitOptions($(this).find("option:selected").val(), $(this).attr("id"));
    });
    $(".btn-filter").click(function(){
		$(this).closest(".dropdown").find(".dropdown-menu").addClass("show");
	});

	$('body').click(function(evt){
		if(!$(evt.target).closest(".dropdown-menu").hasClass('arrow_box_filter')) {
			$(".btn-filter").closest(".dropdown").find(".dropdown-menu").removeClass("show");
		}
		else {
			$(".btn-filter").closest(".dropdown").find(".dropdown-menu.arrow_box_filter").addClass("show");
		}
	});
    setTimeout(function(){
        $(".daterangepicker").click(function(){
            setTimeout(function(){
                //$(".filter-components-container").find(".dropdown").addClass("open")
            },2);
        });
    },1000)
    
});

$(window).load(function(){
	filterOptionInit();
});

function TooltipInit() {
	$('[data-tooltip="tooltip"]').tooltip({
		trigger : 'hover'
	});
}

function bootstrapModalBodyHeightInit() {
    $(".modal-body").each(function(){
        var headerFooterHeight = 62;
        if($( this ).closest(".modal-dialog").find(".modal-header").length >= 1) {
            headerFooterHeight += 56; 
        }
        if($( this ).closest(".modal-dialog").find(".modal-footer").length >= 1) {
            headerFooterHeight += 65; 
        }
        var pageHeight = Number($( window ).height() - headerFooterHeight);
        $(this).css({maxHeight: pageHeight+"px", overflow: "auto"});
    })
}

function populateUnitOptions(selected_unit_id, target_field_id){
    $.ajax({
        url: "/erp/masters/json/populate_units/",
        type: "POST",
        dataType: "json",
        data: {"unit_id": selected_unit_id},
        success: function(unit_options) {
            option_html = "";
            for(i=0; i < unit_options.length; i++){
                option_html += "<option value=\"" + unit_options[i]["category_id"] + "\"" +
                                unit_options[i]["attrs"] + ">" +
                                unit_options[i]["label"] + "</option>";
            }
            $("#" + target_field_id).html(option_html);
        }
    });
}

function validateTextInputs(){
    $("input[validatefor='integer']").blur(function (e) {
        this.value = this.value.replace(/[^0-9]/g,'');
    });
    $("input[validatefor='integer']").bind('keypress', function (event) {
        var regex = new RegExp("^[0-9]+$");
        var key = String.fromCharCode(!event.charCode ? event.which : event.charCode);
        var keyChar = event.charCode ? event.which : event.charCode;
        if(keyChar != 0){
            if (!regex.test(key)) {
                event.preventDefault();
                return false;
            }
        }
    });

    $("input[validatefor='decimal']").blur(function (e) {
        this.value = this.value.replace(/[^0-9\.]/g,'');
		if(this.value.split('.').length) {
			var input = this.value;
			var index = input.indexOf( '.' );
			if ( index > -1 ) {
				input = input.substr( 0, index + 1 ) + input.slice( index ).replace( /\./g, '' );
			}
			this.value = input;
		}
    });
    $("input[validatefor='decimal']").bind('keypress', function (event) {
        var regex = new RegExp("^[0-9\.]+$");
        var key = String.fromCharCode(!event.charCode ? event.which : event.charCode);
        var keyChar = event.charCode ? event.which : event.charCode;
        if(keyChar != 0){
            if (!regex.test(key)) {
                event.preventDefault();
                return false;
            }
        }
        if (event.charCode === 46 && this.value.split('.').length === 2) {
            return false;
        }
    });

     $("input[validatefor='amount']").blur(function (e) {
        this.value = this.value.replace(/[^0-9\.]/g,'');
		if(this.value.split('.').length) {
			var input = this.value;
			var index = input.indexOf( '.' );
			if ( index > -1 ) {
				input = input.substr( 0, index + 1 ) + input.slice( index ).replace( /\./g, '' );
			}
			this.value = Number(input).toFixed(2);
		}
    });
    $("input[validatefor='amount']").bind('keypress', function (event) {
        var regex = new RegExp("^[0-9\.]+$");
        var key = String.fromCharCode(!event.charCode ? event.which : event.charCode);
        var keyChar = event.charCode ? event.which : event.charCode;
        if(keyChar != 0){
            if (!regex.test(key)) {
                event.preventDefault();
                return false;
            }
        }
        if (event.charCode === 46 && this.value.split('.').length === 2) {
            return false;
        }
    });

    $("input[validatefor='character']").blur(function (e) {
        this.value = this.value.replace(/[^a-z\s.]/gi, '');
    });
    $("input[validatefor='character']").bind('keypress', function (event) {
        var regex = new RegExp("^[a-zA-Z. ]+$");
        var key = String.fromCharCode(!event.charCode ? event.which : event.charCode);
        var keyChar = event.charCode ? event.which : event.charCode;
        if(keyChar != 0){
            if (!regex.test(key)) {
                event.preventDefault();
                return false;
            }
        }
    });
	
	$("input[validatefor='alphanumberic']").blur(function (e) {
        this.value = this.value.replace(/[^0-9A-Za-z\s.]/gi, '');
    });
    $("input[validatefor='alphanumberic']").bind('keypress', function (event) {
        var regex = new RegExp("^[0-9a-zA-Z. ]+$");
        var key = String.fromCharCode(!event.charCode ? event.which : event.charCode);
        var keyChar = event.charCode ? event.which : event.charCode;
        if(keyChar != 0){
            if (!regex.test(key)) {
                event.preventDefault();
                return false;
            }
        }
    });
	
	$("input[validatefor='alphaSpecialChar'], textarea[validatefor='alphaSpecialChar']").blur(function (e) {
        this.value = this.value.replace(/[^0-9A-Za-z(,) /@+&_,-][\s.]/gi, '');
    });
    $("input[validatefor='alphaSpecialChar'], textarea[validatefor='alphaSpecialChar']").bind('keypress', function (event) {
        var regex = new RegExp("^[a-zA-Z0-9 @&,()+/._-]+$");
        var key = String.fromCharCode(!event.charCode ? event.which : event.charCode);
        var keyChar = event.charCode ? event.which : event.charCode;		
        if(keyChar != 0){
            if (!regex.test(key)) {
                event.preventDefault();
                return false;
            }
        }
    });
	
	$("input[validatefor='aadharValidation']").blur(function (e) {
        this.value = this.value.replace(/[^0-9-\s.]/gi, '').toLowerCase();
    });
    $("input[validatefor='aadharValidation']").bind('keypress', function (event) {
        var regex = new RegExp("^[0-9-]+$");
        var key = String.fromCharCode(!event.charCode ? event.which : event.charCode);
        var keyChar = event.charCode ? event.which : event.charCode;		
        if(keyChar != 0){
            if (!regex.test(key)) {
                event.preventDefault();
                return false;
            }
        }
    });
}

function DatePickerInit(){
	$(".datepicker").datepicker();
}

function DatePickerIconClick(){ 
	$(".datepicker").next('.input-group-addon').click(function(){
		$(this).prev('.datepicker').focus();
	});
}

function SingleNewDatePickerInit(){
	$( ".single_datePicker" ).datepicker({
		format: "M d, yyyy",
		autoclose: true,
		orientation: 'bottom auto'
	}).on('changeDate', dateChanged);

	$(".single_datePicker").blur(function(){
		if($(this).val() == "") {
			UdateSingleDatePicker($(this).attr('id'));
		}
	});

	$(".single_datePicker").keyup(function(event){
		var key = window.event ? event.keyCode : event.which;
		if(event.keyCode == 8) {
			$(this).datepicker("setDate", new Date());
			$(this).val("");
			UdateSingleDatePicker($(this).attr('id'));
		}
	});

	$(".single_datePicker").keypress(function(event){
		event.preventDefault();
	});

	$(".single_datePicker").next('.glyphicon').click(function(){
		$(this).prev(".single_datePicker").focus();
	});

	$(".single_datePicker").each(function(){
		var dateval = $(this).prev('input').val();
		if($(this).hasClass('single_datePicker_till_date')) {
			$(this).datepicker("setEndDate", new Date());	
		}
		if(dateval != "") {
			$(this).datepicker("setDate", new Date(dateval));
		}
	})
}

function dateChanged(ev) {
	UdateSingleDatePicker($(this).attr('id'));
}

function UdateSingleDatePicker(dateid) {
	if($("#"+dateid).val() != "") {
		var curDate = moment($("#"+dateid).val()).format('YYYY-M-D');
		$("#"+dateid).prev('input').val(curDate);
	}
	else {
		$("#"+dateid).prev('input').val("");
	}
}

function getNotificationCount(){
	$.ajax({
		url: "/erp/commons/json/nm_count/",
		type: "post",
		success: function(response) {
            if (response.response_code == 200) {
                    $("#alertbadge").attr('data-badge', response.notification_count);
                    if(response.notification_count == 0) {
                    	$("#divNotificationBadge").addClass('alert-badge-disable');
                    }
                    else {
                    	$("#divNotificationBadge").removeClass('alert-badge-disable');
                    }
            }
		},
		error : function(xhr,errmsg,err) {
			console.log(xhr.status + ": " + xhr.responseText);
		}
	});
}

function getNotification(){
	$.ajax({
		url: "/erp/commons/json/nm_list/",
		type: "post",
		success: function(response) {
		        if (response.response_code == 200) {
                                var ul = document.getElementById("notification");
                                $(ul).empty();
                                $("#alertbadge").attr('data-badge', response.notification_list.length);
                                if(response.notification_list.length == 0) {
			                    	$("#divNotificationBadge").addClass('alert-badge-disable');
			                    }
			                    else {
			                    	$("#divNotificationBadge").removeClass('alert-badge-disable');
			                    }
                                $.each(response.notification_list, function (i, item) {
                                         var tr = document.createElement("tr");
                                         var td = document.createElement("td");
                                         var td1 = document.createElement("td");
                                         var td2 = document.createElement("td");
                                         td.className = "tdNotification";
                                         td2.className = "tdIdNotification";
                                         td.innerHTML = item.message;
                                         td1.innerHTML = "<img src='/site_media/images/delete_img.png' class='deleteNotification' />";
                                         td2.innerHTML = item.id;
                                         td2.hidden= true
                                         tr.appendChild(td);
                                         tr.appendChild(td1);
                                         tr.appendChild(td2);
                                         ul.appendChild(tr);
                                 });
                                 if(response.notification_list.length == 0) {
                                        $("#delete_all").addClass("hide")
                                 } else {
                                        $("#delete_all").removeClass("hide")
                                 }
                                $(".deleteNotification").click(function(){
                                        var deletedId = $(this).closest('tr').find('.tdIdNotification').text();
                                        $("#hdn_notification_id").val(deletedId);
                                        deleteNotification();
                                        $("#hdn_notification_id").val("")
                                        $(this).closest('tr').remove();
                                        $("#alertbadge").attr('data-badge', response.notification_count);
                                        if(response.notification_count == 0) {
					                    	$("#divNotificationBadge").addClass('alert-badge-disable');
					                    }
					                    else {
					                    	$("#divNotificationBadge").removeClass('alert-badge-disable');
					                    }
                                });
			} else {
                                window.location.href = "/erp/logout/";
			}
		},
		error : function(xhr,errmsg,err) {
			console.log(xhr.status + ": " + xhr.responseText);
		}
	});
}

function deleteNotification(){
	nm_list = $("#hdn_notification_id").val()
	$.ajax({
		url: "/erp/commons/json/del_nm/",
		type: "post",
		datatype: "json",
        data: {'notification_ids' : nm_list},
		success: function(response){
            // No Action required, as this is get notification count on every 5 seconds
            if (response.response_code== 200) {
                $("#alertbadge").attr('data-badge', response.notification_count);
                if(response.notification_count == 0) {
                	$("#divNotificationBadge").addClass('alert-badge-disable');
                }
                else {
                	$("#divNotificationBadge").removeClass('alert-badge-disable');
                }
            }
            getNotificationCount();
		},
		error : function(xhr,errmsg,err) {
			console.log(xhr.status + ": " + xhr.responseText);
		}
	});
}

$(function() {
    $('#delete_all').click(function () {
		swal({
		  title: "Are you sure?",
		  text: "Do you want to delete all notifications?",
		  type: "warning",
		  showCancelButton: true,
		  confirmButtonColor: "#209be1",
		  confirmButtonText: "Yes, delete it!",
		  closeOnConfirm: true
		},
		function(isConfirm) {
			if (isConfirm) {
				nm_list ="";
				var table = document.getElementById('tableNotification');
				var rowcount = document.getElementById('tableNotification').rows.length;
				for(i=0; i<rowcount; i++){
					if (nm_list === "") {
						nm_list += $(table.rows[i].cells[2]).text();
					} else {
					nm_list += "," + $(table.rows[i].cells[2]).text();
					}
				}
				$("#hdn_notification_id").val(nm_list);
				deleteNotification();
				$("#tableNotificationContainer #notification tr").remove();
			} else {
				setTimeout(function(){
					var rowcountDelete = document.getElementById('notification').rows.length;
					if(rowcountDelete > 0){
						$("#tableNotificationContainer").show();
					}
				},10);
			}
		});
    });
});

function getFinancialYearList() {
	var financialStart = $("#id_enterprise_fy_start_day").val().split('/');
	var financialStartMonth = financialStart[1] <= 12 ? financialStart[1] : 1;
	var financialStartDate = financialStart[0] <= 31 ? financialStart[0] : 1;

	var currentYear = (new Date).getFullYear();
	var months = [ "Dec", "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec" ];
	var monthsDays = [ "31", "31", "29", "31", "30", "31", "30", "31", "31", "30", "31", "30", "31" ];
	var financialYearList = [];
	if(moment().year(currentYear).month(months[financialStartMonth-0]).date(financialStartDate) < moment()){
		var ThisFinanacialYear = moment().year(currentYear).month(months[financialStartMonth-0]).date(financialStartDate);
		var ThisFinanacialYearLast = moment().year(currentYear-1).month(months[financialStartMonth-0]).date(financialStartDate);
	}
	else {
		var ThisFinanacialYear = moment().year(currentYear-1).month(months[financialStartMonth-0]).date(financialStartDate)
		var ThisFinanacialYearLast = moment().year(currentYear-2).month(months[financialStartMonth-0]).date(financialStartDate)
	}

	financialYearList.push(ThisFinanacialYear);
	financialYearList.push(ThisFinanacialYearLast);
	if(moment().year(currentYear).month(months[financialStartMonth-1]).date(monthsDays[financialStartMonth-1]) < moment()){
		var NextFinanacialYear = moment().year(currentYear+1).month(months[financialStartMonth-1]).date(monthsDays[financialStartMonth-1]);
		var NextFinanacialYearLast = moment().year(currentYear).month(months[financialStartMonth-1]).date(monthsDays[financialStartMonth-1]);
	}
	else {
		var NextFinanacialYear = moment().year(currentYear).month(months[financialStartMonth-1]).date(monthsDays[financialStartMonth-1])
		var NextFinanacialYearLast = moment().year(currentYear-1).month(months[financialStartMonth-1]).date(monthsDays[financialStartMonth-1])
	}
	financialYearList.push(NextFinanacialYear);
	financialYearList.push(NextFinanacialYearLast);
	return financialYearList;
}

function DateRangeInit() {
	if($(".fromdate").val() == ""){
		var start = moment().subtract(29, 'days');
	}
	else {
		var start = moment($(".fromdate").val());
	}	
	if(!start.isValid()) {
		start = moment().subtract(29, 'days');
	}
	if($(".todate").val() == ""){
		var end = moment();
	}
	else {
		var end = moment($(".todate").val());
	}	
	if(!end.isValid()) {
		end = moment();
	}
	$(".fromdate").val(start.format('YYYY-M-D'));
	$(".todate").val(end.format('YYYY-M-D'));
	function cb(start, end) {
		$('.report-range span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
	}

	var financialYearList = getFinancialYearList();
	//console.log(financialYearList)
	var ThisFinanacialYear = financialYearList[0];
	var ThisFinanacialYearLast = financialYearList[1];
	var NextFinanacialYear = financialYearList[2];
	var NextFinanacialYearLast = financialYearList[3];

	$('.report-range').daterangepicker({
		startDate: start,
		endDate: end,
		ranges: {
		   'Last 30 Days': [moment().subtract(29, 'days'), moment()],
		   'Last Month': [moment().subtract(1, 'months').startOf('month'), moment().subtract(1, 'months').endOf('month')],
		   'Current Month': [moment().startOf('month'), moment()],
		   'Current Year': [moment().month("Jan").date("01"), moment()],
		   'Last Financial Year': [ThisFinanacialYearLast, NextFinanacialYearLast],
		   'Current Financial Year': [ThisFinanacialYear, NextFinanacialYear]
		},
		showDropdowns: true,
		autoApply: true,
		maxDate: NextFinanacialYear
	}, cb);
	cb(start, end);
	
	$('.report-range').on('hide.daterangepicker', function(ev, picker) {
		var FromDateCalc = $( ".daterangepicker:visible input[name='daterangepicker_start']" ).val().split('/');
		$(".fromdate").val(FromDateCalc[2]+"-"+FromDateCalc[0]+"-"+FromDateCalc[1]);
		var ToDateCalc = $( ".daterangepicker:visible input[name='daterangepicker_end']" ).val().split('/');
		$(".todate").val(ToDateCalc[2]+"-"+ToDateCalc[0]+"-"+ToDateCalc[1]);
		$(".daterangepicker.show-calendar").hide();
	});
}

function DateRangeInit_2() {
	if($(".fromdate_2").val() == ""){
		var start = moment().subtract(29, 'days');
	}
	else {
		var start = moment($(".fromdate_2").val());
	}	
	if(!start.isValid()) {
		start = moment().subtract(29, 'days');
	}
	if($(".todate_2").val() == ""){
		var end = moment();
	}
	else {
		var end = moment($(".todate_2").val());
	}	
	if(!end.isValid()) {
		end = moment();
	}
	$(".fromdate_2").val(start.format('YYYY-M-D'));
	$(".todate_2").val(end.format('YYYY-M-D'));
	function cb(start, end) {
		$('.report-range_2 span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
	}

	var financialYearList = getFinancialYearList();
	var ThisFinanacialYear = financialYearList[0];
	var ThisFinanacialYearLast = financialYearList[1];
	var NextFinanacialYear = financialYearList[2];
	var NextFinanacialYearLast = financialYearList[3];

	$('.report-range_2').daterangepicker({
		startDate: start,
		endDate: end,
		ranges: {
		   'Last 30 Days': [moment().subtract(29, 'days'), moment()],
		   'Last Month': [moment().subtract(1, 'months').startOf('month'), moment().subtract(1, 'months').endOf('month')],
		   'Current Month': [moment().startOf('month'), moment()],
		   'Current Year': [moment().month("Jan").date("01"), moment()],
		   'Last Financial Year': [ThisFinanacialYearLast, NextFinanacialYearLast],
		   'Current Financial Year': [ThisFinanacialYear, NextFinanacialYear]
		},
		showDropdowns: true,
		autoApply: true,
		maxDate: NextFinanacialYear
	}, cb);
	cb(start, end);
	
	$('.report-range_2').on('hide.daterangepicker', function(ev, picker) {
		var FromDateCalc = $( ".daterangepicker:visible input[name='daterangepicker_start']" ).val().split('/');
		$(".fromdate_2").val(FromDateCalc[2]+"-"+FromDateCalc[0]+"-"+FromDateCalc[1]);
		var ToDateCalc = $( ".daterangepicker:visible input[name='daterangepicker_end']" ).val().split('/');
		$(".todate_2").val(ToDateCalc[2]+"-"+ToDateCalc[0]+"-"+ToDateCalc[1]);
		$(".daterangepicker.show-calendar").hide();
	});
}

//Single Date
function SingleDatePickerInit() {
	var singleEnd;
	var financialStart = $("#id_enterprise_fy_start_day").val().split('/');
	var financialStartMonth = financialStart[1] <= 12 ? financialStart[1] : 1;
	var financialStartDate = financialStart[0] <= 31 ? financialStart[0] : 1;

	var currentYear = (new Date).getFullYear();
	var months = [ "Dec", "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec" ];
	var monthsDays = [ "31", "31", "29", "31", "30", "31", "30", "31", "31", "30", "31", "30", "31" ];
	
	if(moment().year(currentYear).month(months[financialStartMonth-0]).date(financialStartDate) < moment()){
		var ThisFinanacialYear = moment().year(currentYear).month(months[financialStartMonth-0]).date(financialStartDate);
		var ThisFinanacialYearLast = moment().year(currentYear-1).month(months[financialStartMonth-0]).date(financialStartDate);
	}
	else {
		var ThisFinanacialYear = moment().year(currentYear-1).month(months[financialStartMonth-0]).date(financialStartDate)
		var ThisFinanacialYearLast = moment().year(currentYear-2).month(months[financialStartMonth-0]).date(financialStartDate)
	}
	
	if(moment().year(currentYear).month(months[financialStartMonth-1]).date(monthsDays[financialStartMonth-1]) < moment()){
		var NextFinanacialYear = moment().year(currentYear+1).month(months[financialStartMonth-1]).date(monthsDays[financialStartMonth-1]);
		var NextFinanacialYearLast = moment().year(currentYear).month(months[financialStartMonth-1]).date(monthsDays[financialStartMonth-1]);
	}
	else {
		var NextFinanacialYear = moment().year(currentYear).month(months[financialStartMonth-1]).date(monthsDays[financialStartMonth-1])
		var NextFinanacialYearLast = moment().year(currentYear-1).month(months[financialStartMonth-1]).date(monthsDays[financialStartMonth-1])
	}
	$(".daterange-single").each(function(){
		if($(this).val() == ""){
			singleEnd = moment();
		}
		else {
			singleEnd = moment($(this).val());
		}
		if(!singleEnd.isValid()) {
			singleEnd = moment();
		}
		$(this).val(singleEnd.format('YYYY-M-D'));
		var curID = $(this).attr('id');
		cbs(singleEnd, curID);
		if($(this).next('.report-date').hasClass("full-date")){
			$(this).next('.report-date').daterangepicker({
				startDate: singleEnd,
				showDropdowns: true,
				autoApply: true,
				singleDatePicker: true
			}, cbs);
		}
		else if($(this).next('.report-date').hasClass("till-date")){
			$(this).next('.report-date').daterangepicker({
				startDate: singleEnd,
				showDropdowns: true,
				autoApply: true,
				singleDatePicker: true,
				maxDate: new Date()
			}, cbs);
		}
		else if($(this).next('.report-date').hasClass("future-date")){
			$(this).next('.report-date').daterangepicker({
				startDate: singleEnd,
				showDropdowns: true,
				autoApply: true,
				singleDatePicker: true,
				minDate: new Date()
			}, cbs);
		}
		else if($(this).next('.report-date').hasClass("financial-date-end")){
			if($("#enterprise_previous_closure_date").val() !="") {
				var  enterprise_next_closure_date = moment($("#enterprise_previous_closure_date").val()).add(1, 'days');
				$(this).next('.report-date').daterangepicker({
					startDate: singleEnd,
					showDropdowns: true,
					autoApply: true,
					singleDatePicker: true,
					minDate: enterprise_next_closure_date,
					maxDate: NextFinanacialYear
				}, cbs);
			}
			else {
				$(this).next('.report-date').daterangepicker({
					startDate: singleEnd,
					showDropdowns: true,
					autoApply: true,
					singleDatePicker: true,
					maxDate: NextFinanacialYear
				}, cbs);
			}
		}
		else {
			if($("#enterprise_previous_closure_date").val() !="") {
				var  enterprise_next_closure_date = moment($("#enterprise_previous_closure_date").val()).add(1, 'days');
				$(this).next('.report-date').daterangepicker({
					startDate: singleEnd,
					showDropdowns: true,
					autoApply: true,
					singleDatePicker: true,
					minDate: enterprise_next_closure_date,
					maxDate: new Date()
				}, cbs);
			}
			else {
				$(this).next('.report-date').daterangepicker({
					startDate: singleEnd,
					showDropdowns: true,
					autoApply: true,
					singleDatePicker: true,
					maxDate: new Date()
				}, cbs);
			}
		}
	});
	
	function cbs(single, curID) {	
		$('#'+curID).next('.report-date').find('span').html(single.format('MMM D, YYYY'));
	}
	
	$('.report-date').on('hide.daterangepicker', function(ev, picker) {
		var SingleDateCalc = $( ".single.show-calendar:visible input[name='daterangepicker_start']" ).val().split('/');
		$(this).prev('.daterange-single').val(SingleDateCalc[2]+"-"+SingleDateCalc[0]+"-"+SingleDateCalc[1]);
		$(".daterange-single").each(function(){
			var singleVal = moment($(this).val());
			$(this).next(".report-date").find('span').html(singleVal.format('MMM D, YYYY'));
		});
		$( ".single.show-calendar").hide();
	});
	
}

function UpdateSingleDate(dateid) {
	var single = moment($("#"+dateid).val());
	$("#"+dateid).next('.report-date').find('span').html(single.format('MMM D, YYYY'));	
}

function UpdateDateRange(start, end) {
	var start_r = moment($("#"+start).val());
	var end_r = moment($("#"+end).val());
	$('.report-range span').html(start_r.format('MMM D, YYYY') + ' - ' + end_r.format('MMM D, YYYY'));
}

function removeValidationError() {
	$(".error-border").removeClass('error-border');
	$(".custom-error-message").remove();
}

function displayRemarksHistory(containerId, remarks, countContainer) {
    try {
        if (remarks == null || remarks == "") {
            $("#"+containerId).html("");
            $("."+countContainer+" .remarks_counter").html("No");
            $("."+countContainer).addClass("disabled");
            return;
        }
        remarkList = JSON.parse(remarks);
        if (remarkList.length > 0) {
        	$("."+countContainer+" .remarks_counter").html(remarkList.length);
        	$("."+countContainer).removeClass("disabled");
            var history = '<div class="chat-container">';
            $.each(remarkList, function(i, remark) {
            	var sDate = moment(remark.date).format('MMM D, YYYY');
            	if(sDate == 'Invalid date') sDate =' ';
                history += '<div class="chat-list">';
                history += '<span class="chat-list-name">' + remark.by + '</span>';
                history += '<span class="chat-list-date">' + sDate + '</span>';
                history += '<span class="chat-list-description">' + remark.remarks + '</span>';
                history += '</div>';
            });
            history += '</div><BR/>';
            $("#"+containerId).html(history);
        }
    } catch(e) {
        console.log("Loading remarks failed", e);
    }
}

function setContainerSizeFixed(containerClass, containerHeight){
	if($(window).height() >= 600) {
		var setContainerHeight = $(window).height() - containerHeight;
		$("."+containerClass).css({maxHeight: setContainerHeight+"px", overflow: "auto"});
		if($("."+containerClass).height() >= setContainerHeight) {
			$("."+containerClass).css({paddingRight: 0});
		}
		else {
			$("."+containerClass).css({paddingRight: "15px"});
		}
	}
}

function remarksInSwal(remarks_id){
	var document_remarks = JSON.parse($("#"+remarks_id).val());
    var chatContainer = '<div class="chat-container">';
    $.each(document_remarks, function(i, item) {
    	chatContainer += '<div class="chat-list"><span class="chat-list-name">'+item.by+'</span><span class="chat-list-date">'+moment(item.date).format("MMM DD, YYYY")+'</span><span class="chat-list-description">'+item.remarks+'</span></div>'
    });
    chatContainer += '</div>';
    swal({
    	title: "",
    	text: chatContainer,
    	customClass: 'swal-remarks'
    })
}

function enterKeyDoNothingEvent() {
	$( "input[enterkey='do_nothing']" ).keypress(function (e) {
		if (e.which == 13) {
			return false;
		}
	});
}

function enterKeySubmitEvent() {
	$("input[data-enterkey-submit]").each(function(){
		$(this).keypress(function (e) {
	        if (e.which == 13) {
	            $("#"+ $(this).data('enterkey-submit')).click();
	        	return false;
	        }
	    });
	});
	enterKeyDoNothingEvent();
}

/**
 * It's a warning message before invoking the callback
 */
function confirmAction(message="", callback=function(isConfirm){}, title="Please confirm!", outsideClick = !1) {
    if (message == "") {
        callback(false);
    } else {
        swal({title: title, text: message, type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#209be1",
            confirmButtonText: "Yes, do it!",
            closeOnConfirm: true,
            closeOnCancel: true,
            allowOutsideClick: outsideClick
        }, callback);
    }
}

function getPdfErrorMessage() {
	if(navigator.userAgent.indexOf("Firefox") != -1 ) {
		return `<div class="alert alert-warning text-left" role="alert" style='margin-top: 15px;'>
					<h3 class='text-center' style='color: #dd4b39;'>Unable to Preview PDF!</h3>
	           		<div style='text-align: center; margin-bottom: 12px; font-size: 14px; text-shadow: 0 0 #8a6d3b;'>Browser is not able to render a Preview of the document. This may be because of any of the below reasons</div>
	           		<ul>
	           	    	<li>PDF content rendering related browser configuration is not set appropriately. (In Firefox, setting the 'Action' for 'PDF' Content found under Settings --> Applications --> Content Type section to 'Preview in Firefox' should enable PDF Preview)</li>
	           	    	<li>Generated document may have not been completely downloaded, due to network issues. Kindly try to load again.</li>
	           	    	<li>User may have been logged out. Kindly Login & try again.</li>
	           	    	<li>PDF generation may have failed in the backend. If the issue still persists after following all the above steps, kindly report to <a href='mailto:<EMAIL>'><EMAIL></a></li>
	           		</ul>
	           	</div>`;
   }
   else {
   		return "";
   }
}

function getPdfLoadingImage() {
	return `<img src="/site_media/media/../images/loading_spinner.gif" style="width: 75px;"><br />Generating your document, Please wait...`
}

function setHeightForTable() {
    if($("body").hasClass("with_subscription_bar")) {
        if($(".page-title").text().indexOf("Expenses") == -1) {
            var setHeight = $(".dataTables_scrollHeadInner").height() + $(".dataTables_scrollFootInner").height() + 230;
         }
        else{
            var setHeight =$(".dataTables_scrollHeadInner").height() + $(".dataTables_scrollFootInner").height() + 290;
        }
    }
    else {
        if($(".page-title").text().indexOf("Expenses") == -1) {
            var url = window.location.href;
            if(url.indexOf("type=export") >= 0) {
                var setHeight = $(".dataTables_scrollHeadInner").height() + $(".dataTables_scrollFootInner").height() + 252;
            }
            else {
                var setHeight = $(".dataTables_scrollHeadInner").height() + $(".dataTables_scrollFootInner").height() + 192;
            }
         }
        else{
            var setHeight =$(".dataTables_scrollHeadInner").height() + $(".dataTables_scrollFootInner").height() + 250;
        }
    }

	var windowHeight = Number($( window ).height() - setHeight);
	var tableHeight = Number($(".dataTables_scroll .dataTables_scrollBody tbody").height() + $(".dataTables_scroll .dataTables_scrollFoot tfoot").height() + 22);
	var setHeight = Math.min(windowHeight, tableHeight);
    if(setHeight <= 50 ) setHeight = 50;
	$('.dataTables_scrollBody').slimScroll({
		height: setHeight,
		alwaysVisible: true
	});
}

$( window ).resize(function() {
  	setHeightForTable();
});

function closeFilterOption() {
	setTimeout(function(){
		$(".btn-filter").closest(".dropdown").removeClass("open");
		$(".btn-filter").closest(".dropdown").find(".dropdown-menu").removeClass("show");
	},10);
}

function openFilterOption() {
	setTimeout(function(){
		$(".btn-filter").closest(".dropdown").addClass("open");
		$(".btn-filter").closest(".dropdown").find(".dropdown-menu").addClass("show");
	},10);
}

function filterOptionInit() {
	if($(".filter-components").length > 0) {
		updateFilterText();
		$(".filtered-condition").click(function(){
			openFilterOption();
		});
	}
}

function loadAlternateUnits(itemId, defaultUnit, elementId, callback=function(){}){
    $.ajax({
        url: "/erp/stores/json/indent/loadalternateunits/",
        type: "post",
        datatype: "json",
        data: {item_id: itemId},
        success: function(response) {
            $(elementId).html("");
            items = `<option value='0' data-val='1'>${defaultUnit}</option>`;
            $.each(response, function(i, unit) {
                items = `${items}<option value="${unit.alternate_unit_id}" data-val="${unit.scale_factor}">${unit.unit_name}</option>`;
            });
            $(elementId).html(items);
            callback();
        }
    });
}

function loadAllUnits(elementId){
    $.ajax({
        url: "/erp/stores/json/indent/loadallunits/",
        type: "post",
        datatype: "json",
        data: {data: ""},
        success: function(response) {
            $(elementId).html("");
            items = "";
            $.each(response, function(i, unit) {
                items = `${items}<option value="${unit.unit_id}">${unit.unit_name}</option>`;
            });
            $(elementId).html(items);
        }
    });
}


function emailRequest(reason='') {
    $.ajax({
        url: "/erp/admin/mail_request/",
        type: "post",
        data: {reason: reason},
        async: true,
        success: function (response) {
            $("#loading").hide();
            setTimeout(function() {
                if (response.response_message == "Success") {
                    swal("Your Request has been Sent.", response.custom_message, "success");
                } else {
                    swal("Your Request has not been Sent.", response.custom_message, "error");
                }
            }, 500);
        }, error: function (xhr, errmsg, err) {
            console.log(xhr.status + ": " + xhr.responseText);
            $("#loading").hide();
            setTimeout(function() {
                swal("Your Request has not been Sent.", response.custom_message, "error");
            }, 500);
        }
    });
}

function validateExtensionRequest(reason='') {
    $.ajax({
        url: "/erp/admin/validate_extension_request/",
        type: "post",
        data: {reason: reason},
        async: true,
        success: function (response) {
            setTimeout(function() {
                if(response.response_message == "Success"){
                    if (response.shall_enable_request_extension == false) {
                        swal('',"<span style='text-shadow: 0 0 #000;'>Extension of Trial period requested on " + response.requested_date + " is under process!</span><br /><br /> Our support team will reach you within two days regarding the same!<br />In case that didn't happen, kindly check with <NAME_EMAIL> / +91 99777 89545",'warning');
                    } else{
                        if(reason == "grace_period"){
                            var popup_message = "Grace Period for Renewal"
                        }else{
                            var popup_message = "Extend Trial Period"
                        }
                        confirmAction(
                            message="You are about to request a feature to <b>"+ popup_message +"</b> with xserp. <br /><br />Are you sure you want to continue?",
                            callback=function(isConfirm) {
                                if (isConfirm) {
                                    $("#loading").show();
                                    emailRequest(reason=reason);
                                }
                            });
                    }
                } else {
                    swal("Your Request has not been Sent.", response.custom_message, "error");
                }
            }, 500);
        }, error: function (xhr, errmsg, err) {
            console.log(xhr.status + ": " + xhr.responseText);
            $("#loading").hide();
            setTimeout(function() {
                swal("Your Request has not been Sent.", response.custom_message, "error");
            }, 500);
        }
    });
}

function requestEraseData() {
	confirmAction(
		message="You are about to request a feature to <b>Erase your Enterprise Data</b> with xserp. <br /><br />Are you sure you want to continue?",
		callback=function(isConfirm) {
	        if (isConfirm) {
	            $("#loading").show();
	            emailRequest(reason='erase_data');
	        }
	    });
}

function requestTrialExtension() {
	validateExtensionRequest(reason='trial_extension');
}

function requestedForTrialExtension(requested_date){
    swal('',"<span style='text-shadow: 0 0 #000;'>Extension of Trial period requested on " + moment(requested_date).format("MMM DD, YYYY") + " is under process!</span><br /><br /> Our support team will reach you within two days regarding the same!<br />In case that didn't happen, kindly check with <NAME_EMAIL> / +91 99777 89545",'warning');
}

function requestGracePeriod() {
	validateExtensionRequest(reason='grace_period');
}

function requestedForGracePeriod(requested_date){
    swal('',"<span style='text-shadow: 0 0 #000;'>Extension of Grace period requested on " + moment(requested_date).format("MMM DD, YYYY") + " is under process!</span><br /><br /> Our support team will reach you within two days regarding the same!<br />In case that didn't happen, kindly check with <NAME_EMAIL> / +91 99777 89545",'warning');
}

function requestEnterpriseSubscription() {
	confirmAction(
		message="You are about to request a feature to <b>ENTERPRISE SUBSCRIPTION</b> with xserp. <br /><br />Are you sure you want to continue?",
		callback=function(isConfirm) {
	        if (isConfirm) {
	            $("#loading").show();
	            emailRequest(reason='enterprise_plan');
	        }
	    });
}
function generateUniqueCode(code) {
    let uniqueCode = '';
    for (let i = 0; i < code.length; i++) {
      uniqueCode += code[i].charCodeAt(0);
    }
    uniqueCode = uniqueCode.trim();
    return uniqueCode;
}

function materialListBlurEvent(materialFieldID) {
    $("#"+materialFieldID).blur(function () {
        var materialName = $(this).val();
        $(".ui-autocomplete:visible").find("li").each(function(){
            if (materialName.toLowerCase() == $(this).text().toLowerCase()) {
                $(this).trigger("click");
                return;
            }
        });
        if(materialName.toLowerCase() == 'add_new_item') {
            $("#"+materialFieldID).val("");
            return;
        }
        if(!$('#add_material_modal').hasClass('in')) {
            if ($(this).val().trim() != "" && $("#material_id_hidden").val() == "")
            {
                swal("","Please Select from the material list.","warning")
            $(".duplicate_material_name").text('Material Name is required.');
            $("#"+materialFieldID).addClass('error-border')
            $("#"+materialFieldID).val("");
            $("#"+materialFieldID).keyup(function(){
                $(".duplicate_material_name").text('');
                $("#"+materialFieldID).removeClass('error-border')
                $('#loading').hide();
            });
        }

        if ($("#id_ind_material-__prefix__-quantity").val() != "" && ($("#id_ind_material-__prefix__-quantity").val() <0))
            {
                swal("","Quantity cannot be 0.")
            
            
        }
    }

        // if(!$('#add_material_modal').hasClass('in')) {
        //     if ($(this).val().trim() != "" && $("#material_id_hidden").val() == "") {
        //         confirmAction(
        //             message=`The item <b>'${$(this).val()}'</b> is not profiled yet.<br /><br />Do you want to profile this  
        //                     <span id="switch_material_service_inline" class="switch-radio-button btn-group" style="max-width: 120px;">
        //                         <a style="padding: 4px;" onclick="activateMaterialType(this)" class="goods active" data-toggle="switch_material_type" data-title= "1" >Goods</a>
        //                         <a style="padding: 4px;" onclick="activateMaterialType(this)" class="service noActive" data-toggle="switch_material_type" data-title="2" >Service</a>
        //                     </span> Item?
                            
        //                      <i class="fa fa-info confirm-box-info" data-tooltip="tooltip" data-placement="left" title="By default, the item will be profiled as Non-stock one, without any Item Code/ Drawing Number being assigned. Such details can be modified via the Material Profile page." aria-hidden="true"></i>`,
        //             callback=function(isConfirm) {
        //                 if (isConfirm) {
        //                     $(".alternate_unit_select_box").addClass("hide");
        //                     $(".all_units_select_box").removeClass("hide");
        //                     $("#id_ind_material-__prefix__-quantity").focus();
        //                     if(materialFieldID == "id_oa_particular-__prefix__-item_name") {
        //                         $("#id_oa_particular-__prefix__-unit_id").addClass("hide")
        //                     }
        //                     if(materialFieldID == "id_se_particular-__prefix__-item_name") {
        //                         $("#id_se_particular-__prefix__-unit_id").addClass("hide")
        //                     }
        //                     if($("#switch_material_service").length > 0) {
        //                         var materialType = $("#switch_material_service_inline a.active").attr("data-title");
        //                         $('#switch_material_service a').not('[data-title="'+materialType+'"]').removeClass('active').addClass('noActive');
        //                         $('#switch_material_service a[data-title="'+materialType+'"]').removeClass('noActive').addClass('active');
        //                         if(materialType == 1) {
        //                             $("#id_material-is_service").prop("checked", false);
        //                         }
        //                         else {
        //                             $("#id_material-is_service").prop("checked", true);
        //                         }
        //                     }
        //                 }
        //                 else {
        //                     $("#"+materialFieldID).val("").focus();
        //                     $(".alternate_unit_select_box, .all_units_select_box").addClass("hide");
        //                 }
        //         });
        //         TooltipInit();
        //     }
        // }
        $(".material-removal-icon").click(function(){
            if($(this).hasClass('removal-icon-table')) {
                $(this).closest("tr").find("input").val("");
            }
            else if($(this).hasClass('removal-icon-td')) {
                $(this).closest("td").find("input").val("");
            }
            else if($(this).hasClass('removal-icon-th')) {
                $(this).closest("th").find("input").val("");
            }
            else {
                $(this).closest("div").find("input").val("");    
            }
            $("#"+materialFieldID).removeAttr("readonly").focus();
            $(this).addClass("hide");
            $('#description_display').html('');
            $(".alternate_unit_select_box, .all_units_select_box").addClass("hide");
        });
    });
}


function activateMaterialType(current) {
    $("#switch_material_service_inline a").removeClass("active").addClass("noActive");
    $(current).addClass("active").removeClass("noActive");
}

function showContainer(current) {
    if(typeof(current) == "string") {
        $("#"+current).removeClass("hide");
    }
    else {

    }
}

function hideContainer(current) {
    if(typeof(current) == "string") {

    }
    else {
        $(current).addClass("hide");
    }
}

function focusOn(current) {
    if(typeof(current) == "string") {
        $("#"+current).focus();
        var $thisVal = $("#"+current).val();
        $("#"+current).val('').val($thisVal);
    }
    else {

    }
}

function getGCSKeyData(){
    var field_set_html = "";
    return $.ajax({
        url: '/erp/commons/json/get_gcs_security_key/',
        type: "post",
        dataType: "json",
        data:{},
        success: function(response) {
            $.each(response['policy']['fields'], function(key, value){
                field_set_html += "  <input name='"+ key +"' value='"+ value +"' type='hidden'/>\n";
            });
            $("#gcs_upload_general").attr("action",response['policy']['url']);
            $("#gcs_upload_general").prepend(field_set_html);
        },
        error: function (xhr, errmsg, err) {
            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
        }
    });
}

function ajaxSubmit(){
    var form = document.querySelector('form#gcs_upload_general');
    var data = new FormData(form);
    var url = form.getAttribute('action');
    $(".btn-browse").addClass("div-disabled");
    $('#loadingmessage2_ie').show();
    $.ajax({
        type: "POST",
        url: url,
        data: data,
        //  below should be false for GCS file upload
        contentType: false,
        processData: false,
        success: function(data) {
            console.log('Hurray file uploaded successfully');
            var file= $("#gcs_upload_general input[type='file']").val().split("\\").pop();
            var uid = $("#gcs_upload_general input[name=key]").val().split("/");
            var form = document.querySelector('form#gcs_upload_general');
            var data = new FormData(form);
            var fileName = $(".btn-browse-container").find(".base64-file-value").attr("data-filename");
            if(fileName != ""){
                var public_url = form.getAttribute('action') + $("#gcs_upload_general input[name=key]").val();
                $(".btn-browse-container").find(".base64-file-value").val(public_url);
                var documents = JSON.stringify({'uid': uid[1], 'name':  file.split('.')[0], 'ext': file.split('.')[1]})
                $(".file_upload_json").val(documents);
            }
            $('#loadingmessage2_ie').hide();
            $(".btn-browse").removeClass("div-disabled");
        }
    });
}

function browseButtonInit(){
    $(".btn-browse").click(function(){
        var current = $(this);
        if(current.closest(".btn-browse-container").find(".base64-file-value").val() != "") {
            viewAttachedDocumenta(current);
        }
        else {
            $(".google_upload_form").html(`<input type="file" name="file" onchange="convertFileToBase64(this)" style="display:none">
												  	    <input type="submit" value="Upload File" style="display:none">`);
            $.when(getGCSKeyData()).done(function(){
                jQuery("#gcs_upload_general input[type='file']").change(function () {
                    ajaxSubmit();
                });
            });
            $(".google_upload_form").find("input[type='file']").click();
        }
    });
}

function convertFileToBase64(current){
    var fileExtension = ['jpeg', 'jpg', 'png', 'gif', 'pdf'];
    var browsedFile = current.files;
    var browsedFileValue = $(current).val();
    var browsedFileExtension = browsedFileValue.split('.').pop().toLowerCase();
    var browsedFileName = $(current).val().split('\\').pop();
    $(".btn-browse-container").find(".base64-file-value").attr("data-extension", browsedFileExtension);
    var defaultBrowseName = $(".btn-browse-container").find(".browsed-text").attr("data-default");

    if ($.inArray(browsedFileExtension, fileExtension) == -1) {
        swal("Invalid File Format", "Please upload only "+fileExtension.join(', ')+" file format.", "warning");
        setTimeout(function(){
            $(".btn-browse-container").find("input[type='file']").val('').clone(true);
            $(".btn-browse-container").find(".btn-browse").removeClass("active").attr("data-original-title", "");
            $(".btn-browse-container").find(".browsed-text").text(defaultBrowseName)
        }, 200);
    }
    else if((browsedFile[0].size) > 10485760) {
        swal("Large File Size", "Please upload a file with maximum of 10mb.", "warning");
        setTimeout(function(){
            $(".btn-browse-container").find("input[type='file']").val('').clone(true);
            $(".btn-browse-container").find(".btn-browse").removeClass("active").attr("data-original-title", "");
            $(".btn-browse-container").find(".browsed-text").text(defaultBrowseName)
        }, 200);
    }
    else {
        $(".btn-browse-container").find(".base64-file-value").attr("data-filename", browsedFileName);
        $(".btn-browse-container").find(".btn-browse").addClass("active").attr("data-original-title", "Preview");
        $(".btn-browse-container").find(".browsed-text").text(browsedFileName)
        TooltipInit();
    }
}

function removeBrowsedFile(current) {
    var defaultBrowseName = $(".btn-browse-container").find(".browsed-text").attr("data-default");
    $(".btn-browse-container").find("input[type='file']").val('').clone(true);
    $(".btn-browse-container").find(".btn-browse").removeClass("active").attr("data-original-title", "");
    $(".btn-browse-container").find(".browsed-text").text(defaultBrowseName)
    $("#gcs_upload_general").html('');
    $(".btn-browse-container").find(".base64-file-value").val('').attr("data-filename", "");
    var code_html = "<input type='file' name='file' onChange='convertFileToBase64(this)' style='display: none;' /> <input type='submit' value='Upload File' style='display: none;' />";
    $("#gcs_upload_general").html(code_html);
    var url = window.location.href;
    if(url.indexOf("sr") >= 0 || url.indexOf("grn") >=0 ) {
        enableEditButton();
    }
}

function viewAttachedDocumenta(current){
    var filePath = $(current).find(".base64-file-value").val();
    var fileName = $(current).find(".base64-file-value").attr("data-filename")
    var extension = $(current).find(".base64-file-value").attr("data-extension")
    if($(current).find(".base64-file-value").hasClass('base64')) {
      var url = filePath;  
    }
    else {
      var url = `/erp/commons/json/document/?document_uri=${filePath}`;
    }
    $("#attachmentPreviewSrc, #attachmentPreviewSrcPdf").removeAttr('src');
    if (extension == "pdf") {
        $("#attachmentPreviewSrc").addClass("hide");
        $("#attachmentPreviewSrcPdf").attr('height', Number($( window ).height()-220));
        $("#attachmentPreviewSrcPdf").attr('src', url).removeClass("hide");
    } else {
        $("#attachmentPreviewSrcPdf").addClass("hide");
        $("#attachmentPreviewSrc").attr('src', url).removeClass("hide");
    }
    $(".attachment-description").text(fileName);
    $("#attachmentPreview").modal('show');
}


function updateProjectChosen(projectCode, select) {
    if($(`#${select} option[value='${projectCode}']`).length == 0 && projectCode !="") {
        $.ajax({
            url: "/erp/stores/indent/load_selected_project/",
            type: "post",
            datatype: "json",
            data: {project_code:projectCode},
            success: function (response) {
                if(response !=null){
                    var option = `<option value='${response.code}'>${response.name}<option>`;
                    $(`#${select}`).append(option);
//                    $(`#${select}`).val(projectCode);
                    var projectCodeExists = $('#' + select + ' option[value="' + projectCode + '"]').length > 0;
                    if (projectCodeExists) {
                        $('#' + select).val(projectCode);
                    } else {
                        $('#' + select + ' option:first').prop('selected', true);
                    }
                    $(`#${select}`).trigger('chosen:updated');
                }
            }
        });
    }
}

function validateProjectChange(current) {
    if ($(current).val()=='add_new_project') {
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();
        $('#modalProjectDetails').modal('show');
        $('#modalProjectDetails').on('hidden.bs.modal', function(e) {
            $(this).find('.error-border').removeClass("error-border");
            $(this).find('.custom-error-message').remove();
            if ($(current).val() === "add_new_project") {
                $(current).val($('.project_select_dropdown optgroup:eq(0) option:eq(0)').val()).trigger("chosen:updated");
            }
        });
    }
}

function validateNewProject(){
    $(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();
    var ControlCollections = [
        {
            controltype: 'textbox',
            controlid: 'project_name',
            isrequired: true,
            errormsg: 'Name is required.'
        }
    ];
    var result = JSCustomValidator.JSvalidate(ControlCollections);
    if(result) {
        saveNewProject();
        ga('send', 'event', 'Project', 'Create', $('#enterprise_label').val(), 1);
    }
}

function saveNewProject() {
    $.ajax({
        data: {project_name: $("#project_name").val()},
        type: 'post',
        url: '/erp/masters/json/projects/save/',
        success: function(response) {
            if(response['response_message'] == "Success"){
                var swalType = "success";
                $("#modalProjectDetails").modal('hide');
                $('.project_select_dropdown').append($('<option>', {
                    value: response['code'],
                    text: response['name']
                }));
                $(".project_select_dropdown").val(response['code']).trigger("chosen:updated");
                $("#project_name").val("");
            }
            else {
                var swalType = "warning";
            }
            swal("",response['custom_message'], swalType);
        }
    });
}

function setCurrentValueToData(current) {
    var currentValue = $(current).val();
    $(current).attr("data-default-value", currentValue);
}

function setHsnSuggestion(current, closestElement, errorMessage="") {
    return `<span class="custom-error-message">${errorMessage}</span>
            <div class="warning-text">Suggestion:</div>
            <span class="btn hsn-suggestion-btn" onclick="updateHSNValueFromSuggestion('NIL-RATED', '${current}', '${closestElement}');">NIL-RATED</span>
            <span class="btn hsn-suggestion-btn" onclick="updateHSNValueFromSuggestion('EXEMPT', '${current}', '${closestElement}');">EXEMPT</span>
            <span class="btn hsn-suggestion-btn" onclick="updateHSNValueFromSuggestion('NON-GST', '${current}', '${closestElement}');">NON-GST</span>`
}

function validateHsnWithSuggestion(current, qtyId = ""){
    var isQtyChecked = true;
    var closestElement = "hsn-wrapper";
    if(qtyId != "") {
        if($("#"+qtyId).val().trim() == "" || $("#"+qtyId).val().trim() == 0) {
            $(current).closest(`.${closestElement}`).find(".custom-error-message").remove();
            $(current).removeClass("error-border").closest(`.${closestElement}`).find(".hsn-suggestion").remove();
            isQtyChecked = false;
        }
    }

    if(isQtyChecked) {
        $(current).closest(`.${closestElement}`).find(".custom-error-message").remove();
        $(current).removeClass("error-border").closest(`.${closestElement}`).find(".hsn-suggestion").remove();
        var currentElementId = $(current).attr("id");
        var ControlCollections = [
            {
                controltype: 'textbox',
                controlid: currentElementId,
                isrequired: true,
                errormsg: 'HSN/SAC is required.',
                ishsn: true,
                hsnerrormsg: 'Invalid HSN Code',
                suggestioncontainer: closestElement
            }
        ];
        JSCustomValidator.JSvalidate(ControlCollections);
    }
}

function updateHSNValueFromSuggestion(current, currentTd, closestElement) {
    $("#"+currentTd).removeClass('error-border');
    $("#"+currentTd).closest(`.${closestElement}`).find(".custom-error-message").remove();
    $("#"+currentTd).closest(`.${closestElement}`).find(".hsn-suggestion").remove();
    $("#"+currentTd).val(current).blur();
}

function listTableHoverIconsInit(table){
    $(`#${table} tbody tr`).hover(function(){
        $(`#${table} tbody tr .table-inline-icon`).addClass('hide');
        $(this).css({height: $(this).height()+"px"});
        $(this).find(".table-inline-icon").toggleClass("hide");
        $(this).find(".table-inline-icon-bg").addClass("hide");
        $(this).find(".inline-icon-content").css({width: 'calc(100% - 75px)', display: 'inline-block'});
    },
    function(){
        $(`#${table} tbody tr .table-inline-icon`).addClass('hide');
        $(this).find(".table-inline-icon-bg").removeClass("hide");
        $(this).find(".inline-icon-content").css({width: '', display: ''});
    });
    TooltipInit();
}

function currencyChangeEvent(loadEvent="") {
    var baseCurrencyTxt = $("#home_currency_id").val();
    var convertedCurrencyTxt = $("select.currency_chosen_select").find("option:selected").text();
    if(convertedCurrencyTxt != baseCurrencyTxt){
        $('.conversion_rate_container').removeClass("hide");
        $(".conversion_rate_container .converted_currency_txt").text(convertedCurrencyTxt);
        $(".conversion_rate_container .base_currency_txt").text(baseCurrencyTxt);
        var item_json = {"from_currency": convertedCurrencyTxt, "to_currency": baseCurrencyTxt};
        if(loadEvent == "onchange") {
            $.ajax({
                  url : "/erp/commons/json/currency_converter/",
                  type : "POST",
                  dataType: "json",
                  data :  item_json,
                  success : function(response) {
                      $(".txt_conversion_rate").val(response.result);
                  }
            });
        }
    }
    else {
        $('.conversion_rate_container').addClass("hide");
        $('.txt_conversion_rate').val('1.0');
    }
}


function constructDifferentMakeName(make_list) {
    let make_names = "";
    try {
        const make_name_list = [];
        if (make_list) {
            const records = JSON.parse(make_list);
            for (const rec of records) {
                make_name_list.push(
                    rec.make + (rec.mpn ? '-' + rec.mpn : '')
                );
            }
            make_names = make_name_list.join(" / ");
        }
    } catch (e) {
        console.error(e.message);
    }
    return make_names;
}

const getProjects = async() => {
        const response = await $.ajax({
            url: '/erp/stores/json/indent/loadallprojectlist/',
            type: "POST",
            async: false,
        });
        if (response) {
            return response;
        }
    }

const getProjectName = (projectId, jsonData) => {
  const project = jsonData.projects.find(p => p.id === projectId);
  if (!project || project.parent_id === 0) return '';
  const parentName = getProjectName(project.parent_id, jsonData);
  return `${parentName} > ${project.name}`;
}

const formatProjectList = (jsonData) => {
    const arrList = [];
    jsonData.projects.forEach(project => {
      const projectName = getProjectName(project.id, jsonData);
      if ((project.parent_id === 0 || projectName !== '') && (project.parent_id != null) ){
          const name = projectName !== '' ? projectName.substring(projectName.lastIndexOf('>') + 2) : project.name;
          arrList.push({name: `${project.code} - ${name}`, id: project.id, enterprise_id: project.project_enterprise_id, parent_id: project.parent_id});
      }

    });
    const projectWise = jsonData ?jsonData.project_wise : null;
    if(!projectWise){
        $('.a-profile-container').attr('onclick', '');
        $('.fa.fa-chevron-down.span-profile-name-arrow').hide();
        $('.a-profile-container').css('cursor', 'default');
        $('a[data-original-title="Internal Work Order"]').hide();
    }
    return arrList.sort((a, b) => a.name.localeCompare(b.name));
}

const formatProjectListForDropdown = (jsonData) => {
    const arr = [];
    jsonData.projects.forEach(project => {
      const projectName = getProjectName(project.id, jsonData);
      if (project.parent_id === 0 || projectName !== '') {
          const name = projectName !== '' ? projectName.replace(' > ', '') : project.name;
          arr.push({name: project.code !== null ? `${project.code} - ${name}` : name, id: project.id, enterprise_id: project.project_enterprise_id, parent_id: project.parent_id});
      }
    });

    return arr.sort((a, b) => a.name.localeCompare(b.name));
}

const getLocations = async () => {
    try {
        const response = await $.ajax({
            url: '/erp/masters/json/location_fetch/',
            type: "POST",
            data: { "enterprise_id": $('#enterprise_id').val(), "user_id": $("#login_user_id").val()},
            dataType: "json",
            async : false
        });
        localStorage.setItem("locationList", JSON.stringify(response));
        return response;
    } catch (error) {
        console.error("Error fetching locations:", error);
    }
};