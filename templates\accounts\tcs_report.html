{% extends "stores/sidebar.html" %}
{% block indent %}
{% if logged_in_user|canView:'ACCOUNTS' %}
<style>
   #cattable tr[data-toggle='open'] td:nth-child(8),
   #cattable tr[data-toggle='open'] input,
   #cattable tr[data-toggle='open'] select {
   opacity: 0.5;
   background: #ddd;
   pointer-events: none;
   }
   .side-content.div-disabled {
   padding: 0;
   background-color: transparent !important;
   }
   .side-content.div-disabled a,
   .side-content.div-disabled div,
   .side-content.div-disabled input {
   border: none;
   outline: none;
   box-shadow: 0 0;
   height: 30px;
   padding-left: 3px;
   background-color: transparent !important;
   font-size: 13px;
   color: #333;
   }
   .side-content.div-disabled input {
   padding-left: 6px;
   }
   .side-content.div-disabled .chosen-single div {
   display: none;
   }
   .side-content.div-disabled .chosen-single span {
   margin-top: 2px;
   }
   .table.text_box_in_table .chosen-single {
   height: 26px;
   font-size: 13px;
   line-height: 25px;
   padding: 0 0 0 3px;
   }
   .table.text_box_in_table .chosen-container-single .chosen-single div b {
   margin-top: 2px;
   }
   .supplier_total_amt {
   font-size: 20px;
   float: right;
   margin-top: 15px;
   }
   .parent-supplier-container {
   padding: 6px 12px;
   border: solid 1px #ccc;
   border-top: none;
   margin-bottom: 15px;
   }
   .supplier_name_tab a {
   color: #000;
   font-size: 16px;
   }
   .chosen-select[readonly='readonly']+div {
   pointer-events: none;
   }
   .supplier_type_select {
   width: 100%;
   border-radius: 4px;
   border-color: #ccc;
   }
   .disabled_material td {
   background: rgba(255, 0, 0, 0.15);
   opacity: 0.5;
   pointer-events: none;
   color: rgba(255, 0, 0, 0.7) !important;
   }
   .disabled_material input {
   background: transparent !important;
   border-color: rgba(255, 0, 0, 0.3);
   color: rgba(255, 0, 0, 0.7) !important;
   }
   .disabled_material td:last-child {
   background: transparent;
   opacity: 1;
   pointer-events: inherit;
   color: #000 !important;
   }
   .td_for_icon .fa.fa-ban,
   .td_for_icon .fa.fa-plus {
   font-size: 18px;
   margin-top: 3px;
   }
   .side-content .custom-error-message {
   margin-top: -24px;
   right: 26px;
   }
   .error-duplicate-supplier {
   background: rgba(249, 255, 81, 0.35);
   }
   .error-duplicate-supplier .chosen-container {
   box-shadow: 0 0;
   }
   .error-duplicate-supplier input,
   .error-duplicate-supplier select,
   .error-duplicate-supplier a span {
   background: rgba(221, 75, 57, 0.00);
   }
   .add_split_container .chosen-disabled {
   opacity: 1 !important;
   box-shadow: 0 0;
   }
   .add_split_container .chosen-disabled a {
   border: none;
   }
   .add_split_container .chosen-disabled b {
   display: none !important;
   }
   .add_split_container select[disabled] {
   -webkit-appearance: none;
   -moz-appearance: none;
   text-indent: 1px;
   text-overflow: '';
   background: transparent;
   }
   .loading-main-container {
   display: block;
   }
   .table-inline-icon-container-list {
   position: absolute;
   margin-top: -8px;
   float: left;
   display: block
   }
</style>
<div class="right-content-container">
   <div class="page-title-container">
      <span class="page-title">TCS Report</span>
   </div>
   <div class="container-fluid" id="container-fluid">
      <div>
         <div class="col-lg-12">
            <div class="content_bg">
               <div class="tab-content">
                  <div>
                     <div class="row">
                        <div class="filter-components">
                           <div class="filter-components-container">
                              <div class="dropdown">
                                 <button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
                                 <i class="fa fa-filter"></i>
                                 </button>
                                 <span class="dropdown-menu arrow_box arrow_box_filter">
                                    <div class="col-sm-12 form-group" >
                                       <label>Date Range</label>
                                       <div id="reportrange" class="report-range form-control">
                                          <i class="glyphicon glyphicon-calendar"></i>&nbsp;
                                          <span></span> <b class="caret"></b>
                                          <input type="hidden" class="fromdate" id="fromdate"
                                             name="fromdate"/>
                                          <input type="hidden" class="todate" id="todate" name="todate"/>
                                       </div>
                                          <select id="is_sales" style="width:100%;padding: 0 0 0 15px;margin-top: 25px;height: 34px;border: 1px solid #ccc;border-radius: 4px;background-color: white;">
                                           <option value="1" selected>Sales</option>
                                           <option value="0">Purchase</option>
                                         </select>
                                    </div>
                                    <div class="filter-footer">
                                       <button type="submit" class="btn btn-save" id="tcsReportTbl" onclick="tcsReportTbl()">Apply</button>
                                    </div>
                                 </span>
                              </div>
                              <span class='filtered-condition filtered-date'>Date: <b></b></span>
                           </div>
                        </div>
                        <div class="col-sm-12">
                           <div class="csv_export_button">
                              <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#tcs_report'), 'TCS_Report.csv']);" data-tooltip="tooltip" title="Download TCS Report as CSV">
                              <i class="fa fa-download" aria-hidden="true"></i>
                              </a>
                           </div>
                           <table class="table table-bordered custom-table table-striped" id="tcs_report" style="width: 100%;">
                              <thead>
                                 <tr>
                                    <th style="min-width: 70px; max-width: 70px;">Si No</th>
                                    <th width="15%">Supplier Name</th>
                                    <th width="15%">Total</th>
                                 </tr>
                              </thead>
                              <tbody id="issue_table_list_tbody">
                              </tbody>
                           </table>
                        </div>
                     </div>
                     <div class="clearfix"></div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
</div>
<script type="text/javascript">
   $(window).load(function() {
       var oTable;
       var oSettings;
       var hash = window.location.hash;
       hash && $('ul.nav a[href="' + hash + '"]').tab('show');

       $('.nav-tabs a').click(function(e) {
           $(this).tab('show');
           var scrollmem = $('body').scrollTop() || $('html').scrollTop();
           window.location.hash = this.hash;
           $('html,body').scrollTop(scrollmem);
       });

       var url = window.location.href;
       TableHeaderFixed();
       NavTableRemove();
       updateFilterText();
       $("#loading").hide();
   });
   function TableHeaderFixed(){
   oTable = $('#tcs_report').DataTable({
   fixedHeader: false,
        "scrollY": Number($(document).height() - 230),
        "scrollX": true,
   "pageLength": 50,
   "search": {
   "smart": false
   },
   "columns": [
   { "className": "text-center" },{ "className": "text-center" },{ "className": "text-right" }
   ]
   });
   oTable.on("draw",function() {
   var keyword = $('#tcs_report_filter > label:eq(0) > input').val();
   $('#tcs_report').unmark();
   $('#tcs_report').mark(keyword,{});
   setHeightForTable();
   listTableHoverIconsInit('tcs_report');
   });
   oTable.on('page.dt', function() {
   $('html, body').animate({
   scrollTop: $(".dataTables_wrapper").offset().top - 15
   }, 'slow');
   listTableHoverIconsInit('tcs_report');
   });
   oSettings = oTable.settings();
   listTableHoverIconsInit('tcs_report');
   $( window ).resize();
   }

   function NavTableRemove() {
   $('ul.nav-tabs li a').click(function() {
   if($(this).attr('id') != "tab_view") {
   if($("#tcs_report").hasClass('dataTable')) {
   oTable.destroy();
   }
   }
   else {
   TableHeaderFixed();
   }
   });
   }


   function updateFilterText() {
   $(".filtered-date b").text($("#reportrange").find("span").text());
   $(".filtered-party b").text($("#party_id option:selected").text());
   }

   function tcsReportTbl() {
   setTimeout(function(){
            $(".filter-components-container").find(".dropdown").removeClass("open");
            $(".filter-components-container").find(".dropdown-menu").removeClass("show");
        },100);
   var fromDate = document.getElementById("fromdate").value;
   var toDate = document.getElementById("todate").value;
   var is_sales = document.getElementById("is_sales").value;

   $("#loading").show();

   $.ajax({
    url: "/erp/accounts/json/tcs_report_json/",
    type: "POST",
    datatype: "json",
    data: { from_date: fromDate, to_date: toDate, is_sales: is_sales },
    success: function(response) {
        $("#loading").hide();
        oTable.clear().draw();
        if (response.length > 0) {
            $('#dateFilterContainer').click();
            response.forEach(function(project, index) {
                var row = [
                    (index + 1),
                    project[0],
                    project[1]
                ];
                oTable.row.add(row).draw(false);
            });
            updateFilterText();
        }
    }
});

   }


   function formatDate(inputDate) {
       var date = new Date(inputDate);
       var month = date.toLocaleString('default', {
           month: 'short'
       });
       var year = date.getFullYear();
       return month + "-" + year;
   }
</script>
{% else %}
<div class="text-center" style="margin-top: 100px;">
   <h3>You don't have adequate permission to access this module.</h3>
   <h4>Please Contact your Administrator.</h4>
</div>
{% endif %}
{% endblock %}