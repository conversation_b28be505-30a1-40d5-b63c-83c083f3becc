"""
"""
import copy
import datetime
from decimal import Decimal
from sqlalchemy import and_, Integer, or_, func
from dateutil.relativedelta import relativedelta
from sqlalchemy.orm import make_transient

from erp import helper, DEFAULT_MAKE_ID
from erp.accounts.backend import AccountService
from erp.admin.backend import UserDAO, PurchaseTemplateConfigService
from erp.commons.backend import push_notification, sendMail
from erp.dao import executeQuery
from erp.models import Material, Party, Currency, MaterialSupplierProfile, Make, \
	PartyLedgerMap, UnitMaster, MaterialMakeMap, Receipt, ReceiptMaterial, SalesEstimate, SEParticulars, OA, \
	OAParticulars
from erp.models import PurchaseOrder, PurchaseOrderMaterial, PurchaseOrderDocument, \
	PurchaseOrderMaterialDeliverySchedules
from erp.notifications import PUSH_NOTIFICATION
from erp.purchase import logger, POStatus, PO_STATUS_REJECTED
from erp.purchase.changelog import PurchaseChangelog
from erp.purchase.document_compiler import PoPDFGenerator
from erp.purchase.queries import GET_PURCHASE_PERFORMANCE_QUERY
from erp.sales.document_compiler import SalesEstimatePDFGenerator, OrderAcknowlegdementPDFGenerator
from erp.stores.backend import StoresDAO
from settings import SQLASession
from util.api_util import response_code
from util.document_properties import CANCEL_WATERMARK_DOC_PATH
from util.helper import readFile, writeFile, getAbsolutePath, getFinancialYear, getFormattedDocPath, \
	getFormattedRevisedDocPath

__author__ = 'nandha'


class PurchaseDAO:
	def __init__(self):
		"""

		"""
		self.db_session = SQLASession()

	def getPurchaseOrder(self, enterprise_id=None, po_id=None, financial_year=None, po_type=None, po_no=None,
	                     sub_number=None):
		"""
		enterprise_id and either po_id or (financial_year, po_no, sub_number) are mandatory
		:return:
		"""
		try:
			if po_id:
				return self.db_session.query(PurchaseOrder).filter(
					PurchaseOrder.enterprise_id == enterprise_id, PurchaseOrder.po_id == po_id).first()
			else:
				return self.db_session.query(PurchaseOrder).filter(
					PurchaseOrder.enterprise_id == enterprise_id, PurchaseOrder.financial_year == financial_year,
					PurchaseOrder.type == po_type, PurchaseOrder.po_no == po_no,
					PurchaseOrder.sub_number == sub_number).first()
		except:
			raise

	def getPurchaseOrders(self, enterprise_id=None, since=None, till=None):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:return:
		"""
		try:
			till = till if till else datetime.datetime.today()
			purchase_orders = self.db_session.query(PurchaseOrder).filter(
				PurchaseOrder.enterprise_id == enterprise_id,
				PurchaseOrder.approved_on < till,
				PurchaseOrder.type.in_(('0', '1')))
			purchase_orders = purchase_orders.filter(
				PurchaseOrder.approved_on >= since) if since is not None else purchase_orders
			return purchase_orders.all()
		except:
			raise

	def getPendingPOCount(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""

		try:
			return self.db_session.query(PurchaseOrder).filter(
				PurchaseOrder.enterprise_id == enterprise_id, PurchaseOrder.status == 0).count()
		except:
			raise

	def getReceiptCodes(self, enterprise_id=None, po_id=None):
		"""

		:return:
		"""
		try:
			receipts = self.db_session.query(Receipt).join(Receipt.items_received).filter(
				ReceiptMaterial.po_no == po_id,
				ReceiptMaterial.enterprise_id == enterprise_id,
				Receipt.status >= Receipt.STATUS_DRAFT).group_by(Receipt.receipt_no).all()
			receipt_codes = [receipt.getCode() for receipt in receipts]

			return list(receipt_codes)
		except:
			raise


class PurchaseService:
	"""

	"""

	def __init__(self):
		"""

		"""
		self.purchase_dao = PurchaseDAO()

	def notifyPendingPOApprovalCount(self, enterprise_id=None, sender_id=None, include_sender=True, code=None,
	                                 type=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param include_sender:
		:return:
		"""
		try:

			pending_po_count = self.purchase_dao.getPendingPOCount(enterprise_id=enterprise_id)
			message = None
			if pending_po_count == 1:
				message = "1 Purchase order is pending for approval"
			elif pending_po_count > 1:
				message = "%s Purchase orders are pending for approval" % pending_po_count
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="PURCHASE",
				message=message, collapse_key="pending_po_count_notification", include_sender=include_sender, code=code,
				type=type)
		except Exception as e:
			logger.error("Notification failed... %s" % e.message)

	def notifyPOUpdatedMessage(self, enterprise_id=None, sender_id=None, purchase_order=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param purchase_order:
		:return:
		"""
		try:
			po_amount = "%s %s" % (purchase_order.currency.code, float(purchase_order.total))
			message = PUSH_NOTIFICATION['purchase_oder_updated'] % (
				"PO" if purchase_order.type == 0 else 'JO', purchase_order.getCode(), purchase_order.supplier.name,
				po_amount)
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="PURCHASE", message=message,
				include_sender=True,code=purchase_order.getCode(),type="PO" if purchase_order.type == 0 else 'JO')
		except Exception as e:
			logger.error("Notification failed... %s" % e.message)

	def notifyPOApprovedMessage(self, enterprise_id=None, sender_id=None, purchase_order=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param purchase_order:
		:return:
		"""
		try:
			po_amount = "%s %s" % (purchase_order.currency.code, float(purchase_order.total))
			type = None
			if purchase_order.type == 0:
				type = "PO"
			else:
				type = 'JO'
			message = PUSH_NOTIFICATION['purchase_oder_approved'] % (
				type, purchase_order.getCode(), purchase_order.supplier.name, po_amount)

			push_notification(
				enterprise_id=purchase_order.enterprise.id, sender_id=sender_id, module_code="PURCHASE",
				message=message, code=purchase_order.getCode(), type=type)
			self.notifyPendingPOApprovalCount(enterprise_id=enterprise_id, sender_id=sender_id,
			                                  code=purchase_order.getCode(), type=type)
		except Exception as e:
			logger.error("Notification failed... %s" % e.message)

	def notifyPoDiscardedMessage(self, enterprise_id=None, sender_id=None, purchase_order=None, include_count=True):
		"""

		:param enterprise_id:
		:param sender_id:
		:param purchase_order:
		:param include_count:
		:return:
		"""
		try:
			po_amount = "%s %s" % (purchase_order.currency.code, float(purchase_order.total))
			type="PO" if purchase_order.type == 0 else 'JO'
			message = PUSH_NOTIFICATION['purchase_oder_discarded'] % (
				type, purchase_order.getCode(), purchase_order.supplier.name, po_amount)
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="PURCHASE", message=message,code=purchase_order.getCode(),
				type=type)
			if include_count is True:
				self.notifyPendingPOApprovalCount(enterprise_id=enterprise_id, sender_id=sender_id,code=purchase_order.getCode(), type=type)
		except Exception as e:
			logger.exception("Notification failed... %s" % e.message)

	def notifyPoRejectedMessage(self, enterprise_id=None, sender_id=None, purchase_order=None, include_count=True):
		"""

		:param enterprise_id:
		:param sender_id:
		:param purchase_order:
		:param include_count:
		:return:
		"""
		try:
			po_amount = "%s %s" % (purchase_order.currency.code, float(purchase_order.total))
			message = PUSH_NOTIFICATION['purchase_oder_rejected'] % (
				"PO" if purchase_order.type == 0 else 'JO', purchase_order.getCode(), purchase_order.supplier.name,
				po_amount)
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="PURCHASE", message=message,code=purchase_order.getCode(),
				type="PO" if purchase_order.type == 0 else 'JO')
			if include_count is True:
				self.notifyPendingPOApprovalCount(enterprise_id=enterprise_id, sender_id=sender_id,code=purchase_order.getCode(), type="PO" if purchase_order.type == 0 else 'JO')
		except Exception as e:
			logger.exception("Notification failed... %s" % e.message)

	def notifyPoReviewMessage(self, enterprise_id=None, sender_id=None, purchase_order=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param purchase_order:
		:return:
		"""
		try:
			message = PUSH_NOTIFICATION['purchase_oder_review'] % (
				"PO" if purchase_order.type == 0 else 'JO', purchase_order.getCode(), purchase_order.supplier.name)
			push_notification(
				enterprise_id=purchase_order.enterprise.id, sender_id=sender_id, module_code="PURCHASE",
				message=message,code=purchase_order.getCode(),type="PO" if purchase_order.type == 0 else 'JO')
			self.notifyPendingPOApprovalCount(enterprise_id=enterprise_id, sender_id=sender_id,code=purchase_order.getCode(), type="PO" if purchase_order.type == 0 else 'JO')
		except Exception as e:
			logger.error("Notification failed... %s" % e.message)

	def getPurchasePerformance(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		performance = []
		try:
			# Processing for status
			query = GET_PURCHASE_PERFORMANCE_QUERY.format(enterprise_id=enterprise_id)
			from erp.dao import executeQuery
			result = executeQuery(query)
			for idx, item in enumerate(result):
				data = {'on_time': int(item[1]), 'delayed': int(item[2]), 'po_month': str(item[0]).replace(" ", "")[:-2]}
				performance.append(data)
		except Exception as e:
			logger.info("Purchase Performance: %s" % e)
		return performance

	def searchPO(self, enterprise_id=None, since=None, till=None, po_no=None,
	             supplier_id=None, item_id=None, project_code=None, status=None, finance_year=None):
		"""
		:param enterprise_id:
		:param since:
		:param till:
		:param po_no:
		:param supplier_id:
		:param item_id:
		:param project_code:
		:param status:
		:param finance_year:
		:return:
		"""
		po_list = []
		try:
			today = datetime.datetime.today()
			query = self.purchase_dao.db_session.query(PurchaseOrder).filter(
				PurchaseOrder.enterprise_id == enterprise_id, PurchaseOrder.type.in_(('0', '1')))
			if po_no != '-1':
				if finance_year == 'All':
					query = query.filter(PurchaseOrder.po_no == po_no)
				else:
					query = query.filter(PurchaseOrder.po_no == po_no, PurchaseOrder.financial_year == finance_year)
			else:
				query = query.filter(PurchaseOrder.drafted_on >= since, PurchaseOrder.drafted_on <= till)
				if supplier_id != '-1':
					query = query.filter(PurchaseOrder.supplier_id == supplier_id)
				if item_id != '-1':
					query = query.join(PurchaseOrder.items).filter(PurchaseOrderMaterial.item_id == item_id)
				if project_code != '-1':
					query = query.filter(PurchaseOrder.project_code == project_code)
				if status != '100':
					query = query.filter(PurchaseOrder.status == status)
			pos = query.all()

			for po in pos:
				po_item = self.__getPODetails(po=po, today=today)
				po_list.append(po_item)
			po_list = sorted(po_list, key=lambda a: a['po_code'])

		except:
			raise
		return po_list

	def fetchDraftPO(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		po_list = []
		try:
			today = datetime.datetime.today()
			pos = self.purchase_dao.db_session.query(PurchaseOrder).filter(
				PurchaseOrder.enterprise_id == enterprise_id, PurchaseOrder.status == PurchaseOrder._STATUS_DRAFT).all()
			for po in pos:
				po_item = self.__getPODetails(po=po, today=today)
				po_list.append(po_item)
			po_list = sorted(po_list, key=lambda a: a['po_code'])

		except:
			raise
		return po_list

	def fetchPoYear(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		years = self.purchase_dao.db_session.query(PurchaseOrder.financial_year).filter(
			PurchaseOrder.enterprise_id == enterprise_id, PurchaseOrder.financial_year.isnot(None)).group_by(
			PurchaseOrder.financial_year).order_by(PurchaseOrder.financial_year.desc()).all()
		years = [year.financial_year for year in years]
		years.insert(0, "ALL")
		return years

	def getValidPODeliveryDate(self, po_id=None, enterprise_id=None, po_delivery=None):
		"""
		get the delivery date also considering with delivery schedule by po wise
		:param po_id:
		:param enterprise_id:
		:param po_delivery:
		:return date:
		"""
		delivery_by_schedule_query = SQLASession().query(PurchaseOrderMaterialDeliverySchedules.due_date).filter(
			PurchaseOrderMaterialDeliverySchedules.po_id == po_id,
			PurchaseOrderMaterialDeliverySchedules.enterprise_id == enterprise_id,
			PurchaseOrderMaterialDeliverySchedules.due_date >= datetime.datetime.today().strftime('%Y-%m-%d 00:00:00')).order_by(
			PurchaseOrderMaterialDeliverySchedules.due_date).first()
		return delivery_by_schedule_query[0] if delivery_by_schedule_query is not None else po_delivery

	def getPurchaseStatus(self, today=None, purchase_order=None):
		"""

		:param today:
		:param purchase_order:
		:return:
		"""
		try:
			material_status = POStatus.PENDING
			delivery_status = POStatus.ON_TRACK
			delivery_overdue = 0
			if purchase_order.status != 0:
				if purchase_order.status == PO_STATUS_REJECTED:
					material_status = POStatus.REJECTED
					delivery_overdue = 0
					delivery_status = ""
				else:
					delivery = purchase_order.delivery if purchase_order.delivery is True or purchase_order.delivery != '0000-00-00 00:00:00' else purchase_order.order_date
					delivery = self.getValidPODeliveryDate(
						po_id=purchase_order.po_id, enterprise_id=purchase_order.enterprise_id, po_delivery=delivery)
					if purchase_order.isAllMaterialsSupplied():
						material_status = POStatus.SUPPLIED
						if purchase_order.materialLastReceivedOn() > delivery:
							delivery_status = POStatus.DELAYED
							delivery_overdue = (purchase_order.materialLastReceivedOn() - delivery).days
						else:
							delivery_status = POStatus.ON_TIME
					elif len(purchase_order.materials_received) > 0:
						material_status = POStatus.PARTIAL
						if purchase_order.materialLastReceivedOn() >= delivery:
							delivery_status = POStatus.DELAYED
							delivery_overdue = (today - delivery).days
					else:
						if (today - delivery).days > 0:
							delivery_overdue = (today - delivery).days
							delivery_status = POStatus.DELAYED
			else:
				material_status = ""
				delivery_status = ""
			po_status = dict()
			po_status['material_status'] = material_status
			po_status['delivery_status'] = delivery_status
			po_status['delivery_overdue'] = delivery_overdue
			po_status['due_days'] = delivery_overdue
			return po_status
		except:
			raise

	def __getPODetails(self, po=None, today=None):
		"""

		:param po:
		:param today:
		:return:
		"""

		po_item = {}
		_PAYMENT_MODES = {0: "", 1: 'PDC', 2: 'Cheque', 3: 'Cash', 4: 'DD', 5: 'Bank Transfer'}
		try:
			po_item['po_id'] = po.po_id
			po_item['po_number'] = po.po_no
			po_item['po_code'] = po.getCode()
			po_item['project_name'] = po.project.name if po.project else ""
			po_item['project_code'] = po.project.code if po.project else ""
			po_item['enterprise_id'] = po.enterprise_id
			po_item['status'] = po.status
			po_item["delivery"] = po.delivery.strftime("%Y-%m-%d") if po.delivery else None
			po_item["pay_through"] = _PAYMENT_MODES[po.pay_through]
			po_item["remarks"] = po.remarks
			try:
				po_item["approved_on"] = po.approved_on.strftime("%Y-%m-%d") if po.approved_on else None
			except Exception as e:
				po_item["approved_on"] = ''
			po_item["drafted_on"] = po.drafted_on.strftime("%Y-%m-%d") if po.drafted_on else None
			po_item["po_type"] = po.type

			po_item['supplier'] = dict(id=po.supplier.id, name=po.supplier.name)
			if po.indent is None:
				po_item['indent'] = dict(id=0, code="-NA-", purpose="-NA-")
			else:
				po_item['indent'] = dict(id=po.indent.indent_id, code=po.indent.getCode(), purpose=po.indent.purpose)
			po_item['tags'] = helper.getTagList(tags=po.tags)
			po_item['po_value'] = float(po.total)
			po_item['currency_name'] = po.currency.code

			# PO Status
			po_item.update(self.getPurchaseStatus(today=today, purchase_order=po))
		except Exception as e:
			logger.exception("Could not make the object PurchaseOrder - %s" % e)

		return po_item

	def getPOStatus(self, enterprise_id=None, since=None, till=None):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:return:
		"""
		po_status = {}
		try:
			start_time = datetime.datetime.now()

			# Processing for status
			logger.info("date %s" % till)
			from_date1 = datetime.date.today() + relativedelta(days=-till.day + 1)
			from_date1 = from_date1 + relativedelta(months=-till.month + 4)

			logger.info("fromdate %s" % from_date1)

			# FIXME 2.9 #NEW @Nandha Purchase order status for mobile with date range specified
			# pos = self.db_session.query(PurchaseOrder).filter(PurchaseOrder.enterprise_id == enterprise_id,
			#                                              PurchaseOrder.approved_on >= from_date1,
			#                                              PurchaseOrder.approved_on <= to_date
			#                                              ).all()
			pos = []
			logger.info("Dashboard data of Purchase Orders for Enterprise %s" % enterprise_id)

			po_pending_on_track = 0
			po_pending_delayed = 0

			po_part_on_time = 0
			po_part_delayed = 0
			today = datetime.date.today()
			for po in pos:
				if po.isAllMaterialsSupplied():
					material_status = POStatus.SUPPLIED
				elif len(po.materials_received) > 0:
					if po.materialLastReceivedOn() > po.delivery:
						po_part_delayed += 1
					else:
						po_part_on_time += 1
				else:
					if today > po.delivery:
						po_pending_delayed += 1
					else:
						po_pending_on_track += 1

			po_status['pending'] = po_pending_on_track + po_pending_delayed
			po_status['pending_on_track'] = po_pending_on_track
			po_status['pending_delayed'] = po_pending_delayed

			po_status['part_supplied'] = po_part_delayed + po_part_on_time
			po_status['part_on_track'] = po_part_on_time
			po_status['part_delayed'] = po_part_delayed

			# logger.info("PO Status : %s" % self.po_status)
			po_status['performance'] = self.getPurchasePerformance(enterprise_id=enterprise_id)
			logger.info("Time taken for thread completion: %s " % (datetime.datetime.now() - start_time))
		except:
			raise
		return po_status

	def generatePODocument(self, enterprise_id=None, po_id=None, document_regenerate=False):
		"""

		:param enterprise_id:
		:param po_id:
		:param document_regenerate:
		:param is_req_water_mark:
		:return:
		"""
		template_config = PurchaseTemplateConfigService().purchase_template_dao.getTemplateConfig(
			enterprise_id=enterprise_id, collection='purchase_template_config')
		if template_config["print_template"] is None or template_config["print_template"].strip() == "":
			return None
		self.purchase_dao.db_session.begin(subtransactions=True)
		try:
			po_to_print = self.purchase_dao.db_session.query(PurchaseOrder).filter(
				PurchaseOrder.enterprise_id == enterprise_id,
				PurchaseOrder.po_id == po_id).first()
			logger.info('Purchase order to be printed - %s' % po_to_print)
			po_revisions = po_to_print.revisions
			po_documents = po_to_print.document
			po_document = po_documents[0] if len(po_documents) > 0 else None
			not_exists = True
			if po_document:
				if po_to_print.last_modified_on == po_document.revised_on:
					not_exists = False
				elif po_to_print.last_modified_on != po_document.revised_on:
					not_exists = True
			temp_doc_path = getFormattedDocPath(code=po_to_print.getCode(), id=po_id)
			temp_rev_doc_path = getFormattedRevisedDocPath(code=po_to_print.getCode(), id=po_id, suffix="-rev")
			pdf_generator = PoPDFGenerator(purchase_order=po_to_print, target_file_path=temp_doc_path)
			if document_regenerate == "true" or po_document is None or po_document.document is None or len(
					po_document.document) < 1:
				logger.info('Document not persisted for PO: %s' % po_to_print)
				document_pdf = pdf_generator.generatePDF(source=po_to_print, template_config=template_config)
				writeFile(document_pdf, temp_doc_path)
				if po_document is None:
					po_document = PurchaseOrderDocument(
						enterprise_id=enterprise_id, po_id=po_to_print.po_id, document=readFile(temp_doc_path),
						revised_on=po_to_print.last_modified_on, revised_by=po_to_print.approved_by)
			else:
				logger.info('Printed the Purchase order - %s' % po_to_print)
				writeFile(po_document.document, temp_doc_path)
			if po_to_print.status == PurchaseOrder._STATUS_REJECTED and not po_document.is_rejected:
				# Adding Water-mark for the rejected PO for the first time
				logger.info(
					"PO Status: %s PO Doc Rejected Status: %s" % (po_to_print.status, po_document.is_rejected))
				PoPDFGenerator.addCancelWaterMark(target_file_path=temp_doc_path)
				po_document.is_rejected = True
			if len(po_revisions) > 0 or not po_document:
				po_document = PurchaseOrderDocument(
					enterprise_id=enterprise_id, po_id=po_to_print.po_id, document=readFile(temp_doc_path),
					revised_on=po_to_print.last_modified_on, revised_by=po_to_print.approved_by)
			po_document.document = readFile(temp_doc_path)
			if po_to_print.po_no and po_to_print.po_no != '0' and not_exists:
				logger.info('Persisting Document for PO: %s' % po_to_print.po_no)
				self.purchase_dao.db_session.add(po_document)
				self.purchase_dao.db_session.commit()
				self.purchase_dao.db_session.refresh(po_document)
			else:
				make_transient(po_document)
				make_transient(po_to_print)
				self.purchase_dao.db_session.rollback()

			po_revisions = po_to_print.revisions
			if len(po_revisions) > 0:
				for revised_doc in po_revisions:
					writeFile(revised_doc.document, temp_rev_doc_path)
					# pdf_generator.addWaterMark(file(getAbsolutePath(CANCEL_WATERMARK_DOC_PATH), "rb"), getAbsolutePath(temp_rev_doc_path))
					# pdf_generator._appendToPDF(getAbsolutePath(temp_rev_doc_path), getAbsolutePath(temp_doc_path))
					logger.info("File to download: %s" % getAbsolutePath(temp_doc_path))
			logger.info('Generating Rendering PO PDF... %s' % po_to_print)
			return po_document
		except Exception as e:
			self.purchase_dao.db_session.rollback()
			logger.exception('PDF generation failed %s' % e.message)

	def getPoDraft(self, enterprise_id=None, po_id=None):
		"""

		:param enterprise_id:
		:param po_id:
		:return:
		"""
		po = dict(materials=[], po_id=po_id, enterprise_id=enterprise_id, po_tags=[])
		# Reading purchaser order materials
		po_materials = self.purchase_dao.db_session.query(
			Material.drawing_no,Material.price, PurchaseOrderMaterial.quantity, PurchaseOrderMaterial.make_id, PurchaseOrderMaterial.rate,
			PurchaseOrderMaterial.discount, Material.name.label("material_name"), Material.price, Make.label.label("make_name"),
			Party.name, Party.id, UnitMaster.unit_name, Currency.code.label("currency_name"), MaterialMakeMap.part_no,
			PurchaseOrderMaterial.item_id, PurchaseOrderMaterial.alternate_unit_id.label('alternate_unit_id'),
			PurchaseOrderMaterial.enterprise_id.label('enterprise_id'),Material.is_stocked
		).outerjoin(
			PurchaseOrder.currency
		).outerjoin(
			PurchaseOrder.items
		).outerjoin(
			PurchaseOrderMaterial.material
		).outerjoin(
			PurchaseOrderMaterial.make
		).outerjoin(
			MaterialMakeMap, and_(
				MaterialMakeMap.item_id == PurchaseOrderMaterial.item_id
				, MaterialMakeMap.make_id == PurchaseOrderMaterial.make_id
				, MaterialMakeMap.enterprise_id == PurchaseOrderMaterial.enterprise_id)
		).outerjoin(
			UnitMaster, and_(Material.unit_id == UnitMaster.unit_id, Material.enterprise_id == UnitMaster.enterprise_id)
		).outerjoin(PurchaseOrder.supplier).filter(
			PurchaseOrder.enterprise_id == enterprise_id, PurchaseOrder.po_id == po_id).all()
		for po_material in po_materials:
			supp = dict(make_id=po_material.make_id, name=po_material.name, id=po_material.id)
			po["supplier"] = supp
			if po_material.item_id:
				part_no = ""
				if po_material.make_id != DEFAULT_MAKE_ID:
					part_no = po_material.part_no
				make_name = "%s%s" % (po_material.make_name, (" - %s" % part_no) if part_no else "")
				quantity = float(po_material.quantity)
				price = float(po_material.rate)
				unit = po_material.unit_name
				store_price=float(po_material.price)
				if po_material.alternate_unit_id:
					scale_factor = helper.getScaleFactor(
						enterprise_id=po_material.enterprise_id, item_id=po_material.item_id,
						alternate_unit_id=po_material.alternate_unit_id)
					if scale_factor:
						quantity = Decimal(po_material.quantity) / Decimal(scale_factor)
						price = Decimal(po_material.rate) * Decimal(scale_factor)
						unit = helper.getUnitName(
							enterprise_id=po_material.enterprise_id, unit_id=po_material.alternate_unit_id)

				material = dict(
					drawing_no=po_material.drawing_no, name=po_material.material_name, item_id=po_material.item_id,
					price=price, quantity=quantity, make_id=po_material.make_id,
					unit=unit, discount=float(po_material.discount), make_name=make_name,
					store_price = store_price,is_stocked=po_material.is_stocked)
				po['materials'].append(material)

		return po

	def getPoMaterialDetails(self, enterprise_id=None, item_id=None, party_id=None, po_date=None, is_job=False):
		"""

		:param enterprise_id:
		:param item_id:
		:param party_id:
		:param po_date:
		:param is_job:
		:return:
		"""
		material = dict(supplier_prices=[], supplier_history=[])

		query = self.purchase_dao.db_session.query(
			MaterialSupplierProfile.price, MaterialSupplierProfile.status, MaterialSupplierProfile.effect_since,
			MaterialSupplierProfile.effect_till, MaterialSupplierProfile.supp_id,
			Party.id, Party.name, Make.label, Currency.code.label("currency_name")
		).outerjoin(
			MaterialSupplierProfile.supplier
		).outerjoin(
			MaterialSupplierProfile.make
		).outerjoin(
			MaterialSupplierProfile.material
		).outerjoin(
			MaterialSupplierProfile.supplier_price_currency
		).filter(
			MaterialSupplierProfile.is_service.is_(is_job),
			MaterialSupplierProfile.item_id == item_id, MaterialSupplierProfile.enterprise_id == enterprise_id,
			MaterialSupplierProfile.status == MaterialSupplierProfile.APPROVED,
			po_date >= MaterialSupplierProfile.effect_since,
			or_(MaterialSupplierProfile.effect_till.is_(None), MaterialSupplierProfile.effect_till >= po_date)
		)

		history = query.all()
		for supplier_price in history:
			supplier_pro = dict(
				price=float(supplier_price.price), supplier=dict(name=supplier_price.name, code=supplier_price.id),
				make=supplier_price.label, currency=supplier_price.currency_name, status=supplier_price.status)
			if int(party_id) == supplier_price.supp_id:
				material["supplier_prices"].append(supplier_pro)
			else:
				material["supplier_history"].append(supplier_pro)
		return material

	def getMaterialOverDue(self, enterprise_id=None, item_id=None, include_party=(), po_date=None, is_job=False):
		"""

		:param enterprise_id:
		:param item_id:
		:param include_party:
		:param po_date:
		:param is_job:
		:return:
		"""

		party_ledger_maps = self.purchase_dao.db_session.query(PartyLedgerMap.party_id,
		                                                       PartyLedgerMap.ledger_id).outerjoin(
			MaterialSupplierProfile, and_(
				MaterialSupplierProfile.supp_id == PartyLedgerMap.party_id,
				MaterialSupplierProfile.enterprise_id == PartyLedgerMap.enterprise_id
			)).filter(
			PartyLedgerMap.enterprise_id == enterprise_id, PartyLedgerMap.is_supplier.is_(True),
			or_(
				PartyLedgerMap.party_id.in_(include_party),
				and_(
					MaterialSupplierProfile.item_id == item_id,
					MaterialSupplierProfile.is_service.is_(is_job),
					MaterialSupplierProfile.enterprise_id == enterprise_id,
					MaterialSupplierProfile.status == MaterialSupplierProfile.APPROVED,
					po_date >= MaterialSupplierProfile.effect_since,
					or_(MaterialSupplierProfile.effect_till.is_(None), MaterialSupplierProfile.effect_till >= po_date)
				))).group_by(PartyLedgerMap.ledger_id)
		account_service = AccountService()

		outstandings = []
		for party_ledger_map in party_ledger_maps:
			ledger_data = account_service.getLedgerAging(
				enterprise_id=enterprise_id, ledger_id=party_ledger_map.ledger_id)
			ledger_data['party_id'] = party_ledger_map.party_id
			# TODO remove this pop lines by correcting exact issue
			ledger_data["aging"].pop("advance_particulars")
			ledger_data["aging"].pop("billable_particulars")
			outstandings.append(ledger_data)
		return outstandings

	def savePOChangelog(self, po_to_approve=None, user_id=None, po_id=None, enterprise_id=None):
		try:
			po_to_be_saved = {}
			query_fetch_project_name = """SELECT id,project_code, supplier_id
										from purchase_order WHERE id = '{code}' """.format(code=po_id)
			project_details = executeQuery(query_fetch_project_name)
			project_code = project_details[0][1]
			supplier_id = project_details[0][2]
			po_need_to_load = PurchaseService().purchase_dao.getPurchaseOrder(enterprise_id=enterprise_id, po_id=po_id)
			po_to_be_saved['new_remarks'] = []
			if po_need_to_load.remarks:
				po_to_be_saved['new_remarks'] = po_need_to_load.remarks
			po_to_be_saved["id"] = project_details[0][0]
			po_to_be_saved["status"] = po_to_approve.status
			po_to_be_saved["type"] = po_to_approve.type
			po_to_be_saved["indent_no"] = po_to_approve.indent_no if po_to_approve.indent_no else ''
			po_to_be_saved["supplier_id"] = supplier_id
			po_to_be_saved["project_code"] = project_code
			po_to_be_saved["quotation_ref_no"] = po_to_approve.quotation_ref_no if po_to_approve.quotation_ref_no else ''
			po_to_be_saved["quotation_date"] = po_to_approve.quotation_date if po_to_approve.quotation_date else ''
			po_to_be_saved["payment"] = po_to_approve.payment if po_to_approve.payment else ''
			po_to_be_saved["pay_in_days"] = po_to_approve.pay_in_days
			po_to_be_saved["pay_against"] = po_to_approve.pay_against if po_to_approve.pay_against else ''
			po_to_be_saved["pay_through"] = po_to_approve.pay_through if po_to_approve.pay_through else ''
			po_to_be_saved["transport"] = po_to_approve.transport
			po_to_be_saved["delivery"] = str(po_to_approve.delivery)[0:10] if po_to_approve.delivery else ''
			po_to_be_saved["purpose"] = po_to_approve.purpose if po_to_approve.purpose else ''
			po_to_be_saved["round_off"] = float(po_to_approve.round_off) if po_to_approve.round_off else '0.00'
			po_to_be_saved["is_blanket_po"] = po_to_approve.is_blanket_po if po_to_approve.is_blanket_po else ''
			po_to_be_saved["total"] = float(po_to_approve.total) if po_to_approve.total else '0.00'
			po_to_be_saved["packing"] = po_to_approve.packing if po_to_approve.packing else 0
			po_to_be_saved["closing_remarks"] = po_to_approve.closing_remarks if po_to_approve.closing_remarks else ''
			po_to_be_saved["currency_code"] = po_to_approve.currency_code if po_to_approve.currency_code else ''
			po_to_be_saved["financial_year"] = ''
			po_to_be_saved["approved_on"] = po_to_approve.approved_on if po_to_approve.approved_on else ''
			po_to_be_saved["issue_to"] = po_to_approve.issue_to if po_to_approve.issue_to else ''
			po_to_be_saved["user"] = user_id
			query_fetch_materials = """SELECT (select CONCAT (name, ' ',CASE 
											WHEN drawing_no IS NOT NULL AND drawing_no != "" THEN CONCAT(" - ", drawing_no, " ") 
											ELSE '' END) from materials where id = item_id) as item_name, pur_qty, 
											po_price, discount, item_id from 
											purchase_order_material where pid  = '{code}' """.format(code=po_id)
			material_details = executeQuery(query_fetch_materials)
			material_list = []
			for material in material_details:
				material_dict = dict()
				query_fetch_tax_details = """select tax_code from purchase_order_material_tax where po_id = '{code}' 
												and item_id = {item_id}""".format(code=po_id, item_id=material[4])
				tax_details = executeQuery(query_fetch_tax_details)
				tax_list = []
				cgst_flag = False
				sgst_flag = False
				igst_flag = False
				if len(tax_details) == 0:
					material_dict["cgst"] = "0"
					material_dict["sgst"] = "0"
					material_dict["igst"] = "0"
				else:
					for tax in tax_details:
						taxes = {}
						query_fetch_tax_type = """select distinct type from tax where code = '{code}' """.format(
							code=tax[0])
						tax_type_details = executeQuery(query_fetch_tax_type)
						taxes["type"] = tax_type_details[0][0]
						taxes["code"] = tax[0]
						tax_list.append(taxes)
				for taxs in tax_list:
					if taxs["type"] == 'CGST':
						cgst_flag = True
						material_dict["cgst"] = taxs["code"]
					if taxs["type"] == 'SGST':
						sgst_flag = True
						material_dict["sgst"] = taxs["code"]
					if taxs["type"] == 'IGST':
						igst_flag = True
						material_dict["igst"] = taxs["code"]
				if not cgst_flag:
					material_dict["cgst"] = "0"
				if not sgst_flag:
					material_dict["sgst"] = "0"
				if not igst_flag:
					material_dict["igst"] = "0"
				material_dict["drawing_name"] = material[0]
				material_dict["quantity"] = float(material[1])
				material_dict["rate"] = float(material[2])
				material_dict["discount"] = float(material[3])
				material_dict["total_price"] = float(material[1]) * float(material[2])
				material_list.append(material_dict)
			po_to_be_saved["materials"] = material_list
			PurchaseChangelog().queryInsert(user_id=user_id, enterprise_id=enterprise_id, data=po_to_be_saved)
		except:
			raise

	def approve_po(self, enterprise_id=None, po_id=None, user_id=None, remarks=None, project_code=None):
		db_session = self.purchase_dao.db_session
		db_session.begin(subtransactions=True)
		try:
			po_to_approve = db_session.query(PurchaseOrder).filter(PurchaseOrder.po_id == po_id).first()
			approved_date = datetime.datetime.now()
			current_fy = getFinancialYear(for_date=approved_date, fy_start_day=po_to_approve.enterprise.fy_start_day)
			if po_to_approve.po_no is None or po_to_approve.po_no == '0':
				latest_po_no = db_session.query(func.max(PurchaseOrder.po_no.cast(Integer))).filter(
					PurchaseOrder.financial_year == current_fy,
					PurchaseOrder.enterprise_id == po_to_approve.enterprise.id,
					PurchaseOrder.type == po_to_approve.type,
					PurchaseOrder.po_no != '0').first()
				logger.info('Recent PO No: %s' % latest_po_no)
				po_to_approve.po_no = '%s' % (
					1 if not ('%s' % latest_po_no).isdigit() or latest_po_no == '0' else int('%s' % latest_po_no) + 1)
				po_to_approve.financial_year = current_fy
			modifying_user = UserDAO().getUserById(enterprise_id=enterprise_id, user_id=user_id)
			po_to_approve.updateRemarks(remarks=remarks, user=modifying_user)
			po_to_approve.status = PurchaseOrder._STATUS_APPROVED
			if po_to_approve.approved_on == '0000-00-00 00:00:00' or po_to_approve.approved_on is None:
				po_to_approve.approved_on = approved_date.strftime('%Y-%m-%d %H:%M:%S')
			po_to_approve.last_modified_on = approved_date.strftime('%Y-%m-%d %H:%M:%S')
			po_to_approve.approved_by = user_id
			po_to_approve.last_modified_by = user_id
			if project_code:
				po_to_approve.project_code = project_code

			logger.info('PO to be approved: %s' % po_to_approve)
			db_session.add(po_to_approve)
			db_session.commit()
			db_session.refresh(po_to_approve)
			self.savePOChangelog(po_to_approve, user_id, po_id, enterprise_id)
			# Notifications here
			self.notifyPOApprovedMessage(
				enterprise_id=enterprise_id, sender_id=user_id, purchase_order=po_to_approve)
			self.send_po_approve_notification_email(po_id=po_id, approved_po=po_to_approve)
			return po_to_approve.getCode()
		except:
			db_session.rollback()
			raise

	def send_po_approve_notification_email(self, po_id, approved_po):
		to_mail_list = []
		cc_list = []
		po_pdf_file = []
		mail_status = False
		try:
			# Send E-mail Notifications here
			to_mail_list.append(approved_po.supplier.primary_contact_details.contact.email)
			cc_list.append(approved_po.drafter.email)
			po_file = {"name": 'PO [%s].pdf' % approved_po.getCode(),
					   "path": getAbsolutePath(getFormattedDocPath(code=approved_po.getCode(), id=po_id))}
			po_pdf_file.append(po_file)
			if to_mail_list and cc_list:
				mail_status = sendMail(recipients=to_mail_list, cc_list=cc_list,
						 subject="PO [" + approved_po.getCode() + "]",
						 body="<p>Hi,<p>We are happy to place a new Purchase Order "
							  "with your organisation.</p><p>PFA - PDF Copy of the "
							  "Purchase Order - " + approved_po.getCode() + "</p>",
						 files=po_pdf_file)
		except Exception as e:
			logger.info("Failed to Send Email Notification: %s", e)
		return mail_status

	def superEditPOCode(
			self, enterprise_id=None, user_id=None, po_id=None, new_financial_year=None,
			new_po_type=None, new_po_no=None, new_sub_number=None):
		"""

		:return: [is_success, json_response]
		"""
		self.purchase_dao.db_session.begin(subtransactions=True)
		try:
			po_to_be_modified = self.purchase_dao.getPurchaseOrder(enterprise_id=enterprise_id, po_id=po_id)
			old_code = po_to_be_modified.getCode()
			if new_sub_number is not None and new_sub_number.strip() == "":
				new_sub_number = None
			existing_po = self.purchase_dao.getPurchaseOrder(
				enterprise_id=enterprise_id, financial_year=new_financial_year,
				po_type=new_po_type, po_no=new_po_no, sub_number=new_sub_number)
			response = response_code.failure()
			response['code'] = old_code
			if not existing_po:
				po_to_be_modified.financial_year = new_financial_year
				po_to_be_modified.type = new_po_type
				po_to_be_modified.po_no = new_po_no
				po_to_be_modified.sub_number = new_sub_number
				po_to_be_modified.super_modified_on = datetime.datetime.now()
				po_to_be_modified.last_modified_on = datetime.datetime.now()
				po_to_be_modified.last_modified_by = user_id
				po_to_be_modified.super_modified_by = user_id
				self.purchase_dao.db_session.add(po_to_be_modified)
				response = response_code.success()
				response['custom_message'] = "Successfully changed the PO Code from '%s' to '%s'!" % (
					old_code, po_to_be_modified.getCode())
				response['code'] = po_to_be_modified.getCode()
				message = PUSH_NOTIFICATION['purchase_code'] % (old_code, po_to_be_modified.getCode())
				push_notification(
					enterprise_id=enterprise_id, sender_id=user_id, module_code="PURCHASE", message=message,code=po_to_be_modified.getCode(),type=po_to_be_modified.type)
			elif po_to_be_modified.po_id == existing_po.po_id:
				response['custom_message'] = "No changes detected in PO code to save!"
			else:
				response['custom_message'] = "An PO with Code '%s' already exists. Please assign a different Code!" % (
					existing_po.getCode())
			self.purchase_dao.db_session.commit()
			return response
		except:
			self.purchase_dao.db_session.rollback()
			raise

	def updateRemarks(self, user_id=None, enterprise_id=None, po_id=None, remarks=None):
		self.purchase_dao.db_session.begin(subtransactions=True)
		try:
			po = self.purchase_dao.getPurchaseOrder(enterprise_id=enterprise_id, po_id=po_id)
			modifying_user = UserDAO().getUserById(enterprise_id=enterprise_id, user_id=user_id)
			user = "%s %s" % (modifying_user.first_name.capitalize(), modifying_user.last_name.capitalize())
			po.updateRemarks(remarks=remarks, user=user)
			self.purchase_dao.db_session.commit()
		except Exception as e:
			logger.exception("PurchaseOrder remarks update failed %s" % e.message)
			self.purchase_dao.db_session.rollback()

	def generatePreviewPurchaseDocument(
			self, po_id=None, user=None, po_config_details=None, updated_banner_image=None, db_session=None, enterprise_id=None):
		"""
		Fetches the document persisted for a given purchase number. If such a document is not available in DB, fetches the
		Purchase details and generates the respective document.

		:param po_id:
		:param user:
		:param po_config_details:
		:param updated_banner_image:
		:param db_session:
		:param enterprise_id:
		:return: PurchaseDocument instance holding the document in PDF format queried (or generated)
		"""
		logger.info('Generating documents for preview pdf of Purchase id: %s' % po_id)
		preview_documents = []
		if not db_session:
			db_session = self.purchase_dao.db_session
		try:
			po_to_print = db_session.query(PurchaseOrder).filter(PurchaseOrder.po_id == po_id).first()
			# Preview for single Page
			total_items = len(po_to_print.items)
			no_of_items_needed = 6
			logger.info(no_of_items_needed)
			if total_items > no_of_items_needed:
				no_of_items_need_to_remove = total_items - no_of_items_needed
				if len(po_to_print.items) > no_of_items_need_to_remove:
					po_to_print.items = po_to_print.items[:len(po_to_print.items) - no_of_items_need_to_remove]

			temp_rev_doc_path = getFormattedRevisedDocPath(code=po_to_print.getCode(), id=po_id, suffix="_single_page_preview")
			document_generator = PoPDFGenerator(purchase_order=po_to_print, target_file_path=temp_rev_doc_path)
			document_pdf = document_generator.generatePDF(
				source=po_to_print, template_config=po_config_details, doc_status="PREVIEW", logged_in_user=user,
				updated_banner_image=updated_banner_image)
			logger.info('Purchase to be printed: %s' % po_to_print.getCode())
			writeFile(document_pdf, temp_rev_doc_path)
			preview_documents.append(readFile(temp_rev_doc_path))
			# Preview for multiple Page
			if not po_config_details["misc_config"]["print_summary_first_page"]:
				no_of_items_needed = 12
			total_items_multi_page = (no_of_items_needed * 2) - (len(po_to_print.items))
			db_session.refresh(po_to_print)
			if len(po_to_print.items) > 0:
				for i in range(total_items_multi_page):
					material = copy.deepcopy(po_to_print.items[0])

					item = PurchaseOrderMaterial(
						po_id=po_id, item_id="%s%s" % (material.item_id, i), enterprise_id=enterprise_id,
						quantity=material.quantity, rate=material.rate + i, discount=material.discount)
					item.material = Material(
						drawing_no="%s%s" % (material.item_id, i), name="Material_%s" % i, description=None, price=material.rate + i,
						in_use=True, unit_id=None, category_id=None, enterprise_id=None, created_on=None,
						tariff_no=None, material_id=None, is_stocked=True, minimum_stock_level=0.000)
					po_to_print.items.append(item)

			temp_rev_doc_path = getFormattedRevisedDocPath(
				code=po_to_print.getCode(), id=po_id, suffix="_multiple_page_preview")
			document_generator = PoPDFGenerator(purchase_order=po_to_print, target_file_path=temp_rev_doc_path)
			document_pdf = document_generator.generatePDF(
				source=po_to_print, template_config=po_config_details, doc_status="PREVIEW", logged_in_user=user,
				updated_banner_image=updated_banner_image)
			logger.info('Purchase to be printed: %s' % po_to_print.getCode())
			writeFile(document_pdf, temp_rev_doc_path)
			preview_documents.append(readFile(temp_rev_doc_path))
		except Exception as e:
			logger.exception('Purchase Document creation failed - %s' % e.message)
		return preview_documents

	def generatePreviewSEDocument(
			self, se_id=None, user=None, se_config_details=None, updated_banner_image=None, db_session=None, enterprise_id=None):
		"""
		Fetches the document persisted for a given purchase number. If such a document is not available in DB, fetches the
		Purchase details and generates the respective document.

		:param se_id:
		:param user:
		:param se_config_details:
		:param updated_banner_image:
		:param db_session:
		:param enterprise_id:
		:return: SalesEstimateDocument instance holding the document in PDF format queried (or generated)
		"""
		logger.info('Generating documents for preview pdf of Sales Estimate id: %s' % se_id)
		preview_documents = []
		if not db_session:
			db_session = self.purchase_dao.db_session
		try:
			se_to_print = db_session.query(SalesEstimate).filter(SalesEstimate.id == se_id).first()
			# Preview for single Page
			total_items = len(se_to_print.items)
			no_of_items_needed = 6
			logger.info(no_of_items_needed)
			if total_items > no_of_items_needed:
				no_of_items_need_to_remove = total_items - no_of_items_needed
				if len(se_to_print.items) > no_of_items_need_to_remove:
					se_to_print.items = se_to_print.items[:len(se_to_print.items) - no_of_items_need_to_remove]
			temp_rev_doc_path = getFormattedRevisedDocPath(code=se_to_print.getCode(), id=se_id, suffix="_single_page_preview")
			document_generator = SalesEstimatePDFGenerator(sales_estimate=se_to_print, target_file_path=temp_rev_doc_path)
			# document_generator = PoPDFGenerator(purchase_order=se_to_print, target_file_path=temp_rev_doc_path)
			document_pdf = document_generator.generatePDF(
				source=se_to_print, template_config=se_config_details, doc_status="PREVIEW", logged_in_user=user,
				updated_banner_image=updated_banner_image)
			logger.info('Sales Estimate to be printed: %s' % se_to_print.getCode())
			writeFile(document_pdf, temp_rev_doc_path)
			preview_documents.append(readFile(temp_rev_doc_path))
			# Preview for multiple Page
			if not se_config_details["misc_config"]["print_summary_first_page"]:
				no_of_items_needed = 12
			total_items_multi_page = (no_of_items_needed * 2) - (len(se_to_print.items))
			db_session.refresh(se_to_print)
			if len(se_to_print.items) > 0:
				for i in range(total_items_multi_page):
					material = copy.deepcopy(se_to_print.items[0])
					item = SEParticulars(
						se_id=se_id, item_id="%s%s" % (material.item_id, i), enterprise_id=enterprise_id,
						qty=material.quantity, unit_rate=material.unit_rate + i, discount=material.discount)
					item.material = Material(
						drawing_no="%s%s" % (material.item_id, i), name="Material_%s" % i, description=None, price=material.unit_rate + i,
						in_use=True, unit_id=None, category_id=None, enterprise_id=None, created_on=None,
						tariff_no=None, material_id=None, is_stocked=True, minimum_stock_level=0.000)
					se_to_print.items.append(item)

			temp_rev_doc_path = getFormattedRevisedDocPath(
				code=se_to_print.getCode(), id=se_id, suffix="_multiple_page_preview")
			document_generator = SalesEstimatePDFGenerator(sales_estimate=se_to_print, target_file_path=temp_rev_doc_path)
			# document_generator = PoPDFGenerator(purchase_order=se_to_print, target_file_path=temp_rev_doc_path)
			document_pdf = document_generator.generatePDF(
				source=se_to_print, template_config=se_config_details, doc_status="PREVIEW", logged_in_user=user,
				updated_banner_image=updated_banner_image)
			logger.info('Sales Estimate to be printed: %s' % se_to_print.getCode())
			writeFile(document_pdf, temp_rev_doc_path)
			preview_documents.append(readFile(temp_rev_doc_path))
		except Exception as e:
			logger.exception('Purchase Document creation failed - %s' % e.message)
		return preview_documents

	def generatePreviewOADocument(
			self, oa_id=None, user=None, oa_config_details=None, updated_banner_image=None, db_session=None, enterprise_id=None):
		"""
		Fetches the document persisted for a given OA number. If such a document is not available in DB, fetches the
		OA details and generates the respective document.

		:param oa_id:
		:param user:
		:param oa_config_details:
		:param updated_banner_image:
		:param db_session:
		:param enterprise_id:
		:return: Order AcknowledgeDocument instance holding the document in PDF format queried (or generated)
		"""
		logger.info('Generating documents for preview pdf of Order Acknowledgement id: %s' % oa_id)
		preview_documents = []
		if not db_session:
			db_session = self.purchase_dao.db_session
		try:
			se_to_print = db_session.query(OA).filter(OA.id == oa_id).first()
			# Preview for single Page
			total_items = len(se_to_print.items)
			no_of_items_needed = 6
			logger.info(no_of_items_needed)
			if total_items > no_of_items_needed:
				no_of_items_need_to_remove = total_items - no_of_items_needed
				if len(se_to_print.items) > no_of_items_need_to_remove:
					se_to_print.items = se_to_print.items[:len(se_to_print.items) - no_of_items_need_to_remove]
			temp_rev_doc_path = getFormattedRevisedDocPath(code=se_to_print.getCode(), id=oa_id, suffix="_single_page_preview")
			document_generator = OrderAcknowlegdementPDFGenerator(order_acknowledgement=se_to_print, target_file_path=temp_rev_doc_path)
			document_pdf = document_generator.generatePDF(
				source=se_to_print, template_config=oa_config_details, doc_status="PREVIEW", logged_in_user=user,
				updated_banner_image=updated_banner_image)
			logger.info('Order Acknowledgement to be printed: %s' % se_to_print.getCode())
			writeFile(document_pdf, temp_rev_doc_path)
			preview_documents.append(readFile(temp_rev_doc_path))
			# Preview for multiple Page
			if not oa_config_details["misc_config"]["print_summary_first_page"]:
				no_of_items_needed = 12
			total_items_multi_page = (no_of_items_needed * 2) - (len(se_to_print.items))
			db_session.refresh(se_to_print)
			if len(se_to_print.items) > 0:
				for i in range(total_items_multi_page):
					material = copy.deepcopy(se_to_print.items[0])
					item = OAParticulars(
						oa_id=oa_id, item_id="%s%s" % (material.item_id, i), enterprise_id=enterprise_id,
						quantity=material.quantity, price=material.price + i, discount=material.discount)
					item.material = Material(
						drawing_no="%s%s" % (material.item_id, i), name="Material_%s" % i, description=None, price=material.price + i,
						in_use=True, unit_id=None, category_id=None, enterprise_id=None, created_on=None,
						tariff_no=None, material_id=None, is_stocked=True, minimum_stock_level=0.000)
					se_to_print.items.append(item)

			temp_rev_doc_path = getFormattedRevisedDocPath(
				code=se_to_print.getCode(), id=oa_id, suffix="_multiple_page_preview")
			document_generator = OrderAcknowlegdementPDFGenerator(order_acknowledgement=se_to_print, target_file_path=temp_rev_doc_path)
			document_pdf = document_generator.generatePDF(
				source=se_to_print, template_config=oa_config_details, doc_status="PREVIEW", logged_in_user=user,
				updated_banner_image=updated_banner_image)
			logger.info('Order Acknowledgement to be printed: %s' % se_to_print.getCode())
			writeFile(document_pdf, temp_rev_doc_path)
			preview_documents.append(readFile(temp_rev_doc_path))
		except Exception as e:
			logger.exception('Order Acknowledgement Document creation failed - %s' % e.message)
		return preview_documents
