"""
"""
__author__ = 'saravanan'

import pymongo

MONGODB_PORT = 27394
MONGODB_SERVER = "3.6.22.55"
MONGODB_USER = "sevagan2"
MONGODB_PASSWORD = "pMoEU53r(s3rv3R"
MONGODB_NAME = "migutharavu"

client = pymongo.MongoClient(MONGODB_SERVER, MONGODB_PORT, username=MONGODB_USER, password=MONGODB_PASSWORD)
MongoDbConnect = client[MONGODB_NAME]

def update_purchase_template_config():
	collection = 'purchase_template_config'
	db = MongoDbConnect[collection]

	default_html = '/home/<USER>/code/xserp-branch/templates/admin/print_template/po/po_template_default.html'
	print_template = open(default_html, mode="rb")
	print_template_data = print_template.read()
	filter = {'enterprise_id': 984}
	update = {'$set': {'print_template': str(print_template_data)}}
	result = db.update_one(filter, update)
	print(result.modified_count)

update_purchase_template_config()
