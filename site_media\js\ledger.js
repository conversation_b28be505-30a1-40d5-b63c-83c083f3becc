$(function() {
    $("#cmdUpload").bind("click", function () {
        if(!$('#voucher_import_date').val() && !$('#fileUpload').val()){
                swal({
				title:"CSV file and Date is required",
				text: "Please select Csv file Date. ",
				type: "warning",
				showCancelButton: false,
				confirmButtonColor: "#209be1",
				confirmButtonText: "OK",
				closeOnConfirm: true
			});
            return false;
        }

        if(!$('#fileUpload').val()){
                swal({
				title:"File is required",
				text: "Please select CSV file. ",
				type: "warning",
				showCancelButton: false,
				confirmButtonColor: "#209be1",
				confirmButtonText: "OK",
				closeOnConfirm: true
			});
            return false;
        }
         if(!$('#voucher_import_date').val()){
                swal({
				title:"Date is required",
				text: "Please select Date. ",
				type: "warning",
				showCancelButton: false,
				confirmButtonColor: "#209be1",
				confirmButtonText: "OK",
				closeOnConfirm: true
			});
            return false;
        }

        var regex = /^([a-zA-Z0-9\s_\\.\-:])+(.csv|.txt)$/;
        if (regex.test($("#fileUpload").val().toLowerCase())) {
            if (typeof (FileReader) != "undefined") {
                var reader = new FileReader();
                reader.onload = function (e) {
                    var table = $("<table />");
                    var lines = this.result.split('\n');
                    var trial_balance_list = []
                    for(var line = 0; line < lines.length; line++){
                         console.log(lines[line]);
                         voucher_data = lines[line].splitCSV();
                         ledger_name = $.trim(voucher_data[0])
                         account_group_id = $.trim(voucher_data[1])
                         opening_debit =  $.trim(voucher_data[2])
                         opening_credit = $.trim(voucher_data[3])
                         var voucher_obj = {
                            "ledger_name":ledger_name, "account_group_id":account_group_id,
                            "opening_debit":opening_debit, "opening_credit":opening_credit
                            };
                         var partyJSON = JSON.stringify(voucher_obj)
                             trial_balance_list.push(partyJSON)
                    }
                    if(trial_balance_list.length<=2){
                      $('#importVoucher').modal('hide');
                      swal({
                            title:"Your file is empty",
                            text: "No trial balance imported.",
                            type: "error",
                            showCancelButton: false,
                            confirmButtonColor: "#209be1",
                            confirmButtonText: "OK",
                            closeOnConfirm: true
                        });
                        return false;
                    }
                    $('#loading').show();
                    setTimeout(function(){
                        $.ajax({
                            url : "erp/accounts/voucher/import/",
                            type : "POST",
                            dataType: "json",
                            data : {'trial_balance_list[]': trial_balance_list, voucher_date :$('#voucher_import_date').val()},
                            success : function(response) {
                               if(response['is_valid_file_headers'] == false){
                                     $('#importVoucher').modal('hide');
                                     swal({
                                        title:" Import Status",
                                        text: response['message'],
                                        type: "error",
                                        showCancelButton: false,
                                        confirmButtonColor: "#209be1",
                                        confirmButtonText: "OK",
                                        closeOnConfirm: true
                                    });
                                    $('#loading').hide();
                                }
                                else {
                                    if(response['status'] == 3){
                                        $('#importVoucher').modal('hide');
                                         swal({
                                            title:" Import Failed",
                                            text: response['message'],
                                            type: "error",
                                            showCancelButton: false,
                                            confirmButtonColor: "#209be1",
                                            confirmButtonText: "OK",
                                            closeOnConfirm: true
                                        });
                                        $('#loading').hide();
                                        return false;
                                    }
                                    if (response['status'] == 1) {
                                        $("button.confirm").removeClass('processing');
                                        $('#loading').hide();
                                        $('#importVoucher').modal('hide');

                                        swal({
                                            title: "Do you want to add the following mismatched Ledgers?",
                                            text: response["new_ledgers"],
                                            type: "warning",
                                            showCancelButton: true,
                                            confirmButtonColor: "#DD6B55",
                                            confirmButtonText: "Yes, import it!",
                                            closeOnConfirm: false
                                        }, function (isConfirm) {
                                            if (isConfirm) {
                                                $("button.confirm").addClass('processing').html('<span class="savingDots">Processing<span>.</span><span>.</span><span>.</span></span>');
                                                $.ajax({
                                                    url: "erp/accounts/voucher/import/",
                                                    type: "POST",
                                                    data : {'trial_balance_list[]': trial_balance_list,'is_user_accept': 1, voucher_date :$('#voucher_import_date').val() },
                                                    dataType: "json",
                                                    success: function (response) {
                                                        setTimeout(function() {
                                                            $("button.confirm").removeClass('processing');
                                                            if(response['failed_items_name_list'].length==0) {
                                                                 if ( response['status'] == 3){
                                                                     swal({
                                                                        title:"Import Failed",
                                                                        text: response['message'],
                                                                        type: "error",
                                                                        showCancelButton: false,
                                                                        confirmButtonColor: "#209be1",
                                                                        confirmButtonText: "OK",
                                                                        closeOnConfirm: false
                                                                    },
                                                                    function(){
                                                                        window.location.href = "/erp/accounts/ledger/";
                                                                    });
                                                                }
                                                                else{
                                                                     swal({
                                                                        title:"Import Success",
                                                                        text: response['message'],
                                                                        type: "success",
                                                                        showCancelButton: false,
                                                                        confirmButtonColor: "#209be1",
                                                                        confirmButtonText: "OK",
                                                                        closeOnConfirm: false
                                                                    },
                                                                    function(){
                                                                        window.location.href = "/erp/accounts/ledger/";
                                                                    });
                                                                }
                                                            }
                                                            else {
                                                                swal.close();
                                                                $("#importVoucher").modal("hide");
                                                                $("#import_voucher_status_modal").modal("show");
                                                                $("#failed_item_table").find("tr:gt(0)").remove();
                                                                $.each(response['failed_items'], function(i, item) {
                                                                    var row = "<tr bgcolor='#ececec' border='0' align='center' style='font-size:11px; font-weight:normal;'>"+
                                                                    "<td>"+item['ledger_name']+ "</td><td>"+item['account_group_id']+ "</td><td align='left'>" +
                                                                    item['opening_debit']  + "</td><td align='left'>" +
                                                                    item['opening_credit']  + "</td><td align='left'>" +
                                                                    item['error']  + "</td></tr>"
                                                                    $('#failed_item_table').append(row).addClass('tbl');
                                                                });
                                                            }
                                                        },1000);
                                                    },
                                                    error: function (xhr, ajaxOptions, thrownError) {
                                                        $("button.confirm").removeClass('processing');
                                                        swal("Error in Importing!", "Please try again", "error");
                                                    }
                                                });
                                            }
                                        });
                                        $('#loading').hide();
                                        return false;
                                    }
                                    if(response['failed_items'].length==0){
                                        $('#importVoucher').modal('hide');
                                        swal({
                                            title: "",
                                            text:"Ledger Import Success",
                                            type: "success",
                                            showCancelButton: false,
                                            confirmButtonColor: "#209be1",
                                            confirmButtonText: "OK",
                                            closeOnConfirm: true
                                        });
                                    }
                                    else {
                                        $("#importVoucher").modal("hide");
                                        $("#import_voucher_status_modal").modal("show");
                                        $("#failed_item_table").find("tr:gt(0)").remove();
                                        $.each(response['failed_items'], function(i, item) {
                                            var row = "<tr bgcolor='#ececec' border='0' align='center' style='font-size:11px; font-weight:normal;'>"+
                                            "<td>"+item['ledger_name']+ "</td><td>"+item['account_group_id']+ "</td><td align='left'>" +
                                            item['opening_debit']  + "</td><td align='left'>" +
                                            item['opening_credit']  + "</td><td align='left'>" +
                                            item['error']  + "</td></tr>"
                                            $('#failed_item_table').append(row).addClass('tbl');
                                        });
                                    }
                                    $('#loading').hide();
                                }
                            }, error : function(xhr,errmsg,err) {
                                console.log(xhr.status + ": " + xhr.responseText);
                                $('#loading').hide();
                            }
                        });
                    },1000);
                }
                reader.readAsText($("#fileUpload")[0].files[0]);
            } else {
                alert("This browser does not support HTML5.");
            }
        } else {
			swal({
				title:"Invalid File Format",
				text: "Please upload a valid CSV file. ",
				type: "warning",
				showCancelButton: false,
				confirmButtonColor: "#209be1",
				confirmButtonText: "OK",
				closeOnConfirm: true
			});
        }
    });

    $('#importVoucher').on('hidden.bs.modal', function(e) {
        $(this).find('.error-border').removeClass("error-border");
        $(this).find('form')[0].reset();
        $('#voucher_import_date').val(moment().format('YYYY-M-D'))
        var newdate = moment(new Date()).format('MM-DD-YYYY');
        $('#voucher_import_date_field').datepicker("setDate", newdate);
        $(this).find('.custom-error-message').remove();
    });
});

 function showImportVoucher() {
        $("#importVoucher").modal('show');
    }
 function show_Account_Group_Modal() {
  $("#account_group_list_modal").modal('show');
}