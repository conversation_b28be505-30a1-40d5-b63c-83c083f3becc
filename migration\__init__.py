"""
"""
__author__ = 'saravanan'

import logging

logger = logging.getLogger(__name__)

material_query = """SELECT 
    CONCAT(materials.name,
                    CASE
                        WHEN materials.drawing_no IS NULL THEN ''
                        ELSE CONCAT('-', materials.drawing_no)
                    END) AS name,
    CONCAT(price, ' /', um.unit_name) AS price,
    0 as opening_balance,
    0  as opening_value,
    mc.name AS category,
    um.unit_name AS unit,
    materials.id as item_id,
    1 as make_id,
    price as item_unit_rate,
    materials.makes_json as make_name
FROM
    materials
        JOIN
    material_category AS mc ON materials.category = mc.id
        AND materials.enterprise_id = mc.enterprise_id
        JOIN
    unit_master AS um ON materials.unit = um.unit_id
        AND materials.enterprise_id = um.enterprise_id        
WHERE
    materials.enterprise_id={enterprise_id}"""
