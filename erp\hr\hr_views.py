"""
"""
import datetime
import os

from dateutil.relativedelta import relativedelta
from django.http import HttpResponse
from django.template.response import TemplateResponse
from django.utils.encoding import smart_str

from erp.auth import ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from erp.hr import logger
from erp.hr.backend import HrService
from erp.properties import MANAGE_HR_TEMPLATE, MANAGE_HR_ATTENDANCE_TEMPLATE, TEMPLATE_TITLE_KEY

__author__ = 'nandha'


def dashboard(request):
	"""

	:param request:
	:return:
	"""

	logger.info("Loading HR Dashboard")
	return TemplateResponse(template=MANAGE_HR_TEMPLATE, request=request, context={})


def attendance(request):
	"""

	:param request:
	:return:
	"""
	logger.info("Loading Attendance report")
	return TemplateResponse(template=MANAGE_HR_ATTENDANCE_TEMPLATE, request=request, context={
		TEMPLATE_TITLE_KEY: "Attendance", "response": ""})


def generatePaySlip(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		month = rh.getPostData('month')
		employee_codes = rh.getPostData('employee_codes')
		if employee_codes:
			employee_codes = employee_codes.split(",")
			if not enterprise_id:
				enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
			if not month:
				today = datetime.date.today() + relativedelta(months=-1, day=1)
				month = today.strftime("%b, %Y")

			hr_service = HrService()
			filename, absolute_path = hr_service.generatePaySlips(
				enterprise_id=enterprise_id, month_year=month, employee_codes=employee_codes)
			logger.info("Pay slip is generated and will be downloaded")
			if absolute_path and os.path.exists(absolute_path):
				with open(absolute_path, "r") as payslip:
					data = payslip.read()
					response = HttpResponse(data, mimetype='application/force-download')
					response['Content-Disposition'] = 'filename=%s' % smart_str(filename)
				os.remove(absolute_path)
				return response
		response_message = "Pay Slip generation failed! Please Check the attendance log imported properly."
	except Exception as e:
		response_message = "Internal server error! Please contact Admin."
		logger.exception("Failed loading report... %s" % e.message)

	return TemplateResponse(template=MANAGE_HR_ATTENDANCE_TEMPLATE, request=request, context={
		TEMPLATE_TITLE_KEY: "Attendance", "response": response_message})
