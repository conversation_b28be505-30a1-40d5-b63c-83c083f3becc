<html>
	<link type="text/css" rel="stylesheet" href="/site_media/css/styles/bootstrap.css?v={{ current_version }}">
	<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<style>
	body{		
		font-family: 'pdf_Times New Roman';
		color: #000;
		margin-top: 50px;
		font-size:11pt;
		line-height:19px;
	}	
	img{
		margin-top: 0px;
	}
	table{
		width:100%;
	}
	table, th, td {
		border: 1px solid black;
		border-collapse: collapse;
	}
	th, td {
		text-align: center;
		line-height: 22px;
		font-size:11pt;
	}
	hr{
		border: 1px solid #000;
		width:100%;
	}
	.oa_number{
		font-size: 19pt;
		margin-left: -160px;
	}
	.oa_date{
		margin-left: -95px;
    	font-size: 11pt;
	}	

	.reg_data {
		width: calc(60% - 1em);
	    float: left;
	    font-size: 11pt;
	}
	.reg_details {
		 float: left;
	    font-weight: bold;
	    font-size: 11pt;
	}
	.pay_data{
		 width: calc(60% - 1em);
	   	 float: left;
		 margin-left: 160px;
   		 margin-top: -19px;
		 font-size:11pt;
		 width:100%;
	}
	.pay_details {
	    width: 50%;
	    float: left;
	    font-weight: bold;
	    margin-left: 33px;
	    font-size:11pt;
	}
	.tax_detail{
		text-align:right;
		padding-right:2px;
	}
	.total{
		text-align:center;
		font-weight:bold;
	}
	.grand_total{
		text-align:right;
		padding-right:2px;
		font-weight:bold;
	}
	.conditions{
		text-align:left;
		padding-right:4px;
		padding-left:4px;
		font-size:12pt;
		line-height:20px;
	}
	.bill_container{
		margin-left:-12px;
		margin-top:-10px;
	}

	.bill_container > hr {
		margin-bottom:4px;
		margin-top:2px;
	}
	.bill_to_details {
	    font-size:10pt;
	}
	.total_words{
		text-align:left;
		padding-left:3px;
		font-size:12pt;
	}
	.address{
		margin-left:-12px;
		font-size:12pt;
	}
	.align_table{
		text-align:right;
		padding-right:5px;
	}
	.description{
		text-align:left;
		padding-left:5px;
	}
	.test_environment{
		text-align:right;
		border-bottom:hidden;
		border-left:hidden;
		border-right:hidden;
		font-size:11pt;
	}

	@font-face {
	        font-family: 'pdf_Times New Roman';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/times.ttf) format('truetype');
	}
	.annexture_oa{
	font-size:11pt;
	text-align:center;
	 margin-bottom:30px;
	 margin-top:30px;
	}
	
</style>
<body>
	<div class="container">
		<div class="col-sm-12">
			<div class="col-sm-8" style="font-size:13pt;padding-left:4px;">
				<img src="{{enterprise_logo}}" style="max-height: 10mm">
				<div style="margin-bottom:2px;"><b>{{ source.enterprise.name }}</b></div>
			</div>

			<div class="col-sm-4" style="margin-top:15px;">
			     <span class="oa_number">{{ form_name }} </span>  # <span>{{ oa_no }}</span><br>
				<span class="oa_date">Acknowledgement Date & Time: <b>{{ oa_date }}</b></span>
			</div>
			<hr style="margin-bottom:4px;">
		</div>

		<div class="col-sm-12">
			<div class="col-sm-6 address">
				<div>{{ enterprise_address }}</div>
				<div><b>Ph:</b>{% if source.enterprise.primary_contact_details.contact.phone_no %} {{ source.enterprise.primary_contact_details.contact.phone_no }} {% endif %}<b>E-mail:</b>{% if source.enterprise.primary_contact_details.contact.email %} {{ source.enterprise.primary_contact_details.contact.email }} {% endif %}</div>
			</div>

			<div class="col-sm-6" style="margin-left: -30px;">
				<div>
                    {% if cin_detail %}
                        <div class="col-sm-4 reg_details"><b>CIN</b></div>
                        <div class="col-sm-8 reg_data">:  {{ cin_detail }}</div>
                    {% endif %}
                    {% if gst_detail %}
                        <div class="col-sm-4 reg_details"><b>GST</b></div>
                        <div class="col-sm-8 reg_data">:  {{ gst_detail }}</div>
                    {% endif %}
				</div><br>
			</div>
			<hr style="margin-bottom:14px;">
		</div>

		<div class="col-sm-12">
			<div class="col-sm-4 bill_container">
				<span style="font-size:14pt;"> <b> BILL TO :</b></span><hr>
				<div class="bill_to_details"> <b>{{ source.customer.name }}</b><br>
					{{ source.customer.address_1 }}, {{ source.customer.address_2 }}<br>
					{{ source.customer.city }}, {{ source.customer.state }},
					{% for country in country_list %}
						{% if country.country_code == source.customer.country_code %}
							{{ country.country_name|upper }}
						{% endif %}
					{% endfor %}
					{% if source.customer.primary_contact_details.contact.phone_no %}<br><b>Ph:</b> {{ source.customer.primary_contact_details.contact.phone_no }} {% endif %}<br>
					{% for reg_detail in source.customer.registration_details %}
						{% if reg_detail.label == "GSTIN" %}
							<b>GSTIN:</b> {{ reg_detail.details }}
						{% endif %}
					{% endfor %}
					{% for reg_detail in source.customer.registration_details %}
						{% if reg_detail.label != "GSTIN" and reg_detail.details != "" %}
							<b>{{ reg_detail.label }}:</b> {{ reg_detail.details }}
						{% endif %}
					{% endfor %}
				</div>
			</div>
			<div class="col-sm-4 bill_container">
				<div>
					<div class="pay_details"><b>PO No. & Date    </b></div>
					<div class="pay_data">  :  {{ source.po_no }} - {{ po_date }}</div>
				</div>
				{% if se_no %}
					<div>
						<div class="pay_details"><b>Estimate No. & Date    </b></div>
						<div class="pay_data">  :  {{ se_no }} - {{ se_date }}</div>
					</div>
				{% endif %}
				<div>
					<div class="pay_details"><b>Payment Terms    </b></div>
					<div class="pay_data">  :  {{ source.payment_terms }}</div>
				</div>
				<div>
					<div class="pay_details"><b>Delivery Due Date   </b></div>
					<div class="pay_data">  : {{ delivery_due_date }}</div>
				</div>
			</div>
		</div>

		<div class="col-sm-12">
			{% if appendix_pages > 0 %}
				<table>
					<tr>
						<th>S.No</th>
						<th>Description</th>
						<th>Quantity</th>
						<th>Unit</th>
						<th>Price</th>
						<th>Total Value</th>
					</tr>
					<tr>
						<td></td>
						<td style="text-align:center; line-height:25px; font-size:14px;">AS PER ANNEXURE TO SALE NO:<br>{{ source.getCode }}</td>
						<td style="text-align:center;">-</td>
						<td style="text-align:center;">-</td>
						<td style="text-align:center;">-</td>
						<td style="text-align:center;"></td>
					</tr>
					{% for tax in oa_taxes %}
						<tr>
							<td colspan="4" class="tax_detail">{{ tax.tax_name }} @ {{ tax.tax_rate }} %</td>
							<td colspan="2" class="tax_detail">{{ tax.tax_value }}</td>
						</tr>
					{% endfor %}
					<tr>
						<td colspan="4" class="grand_total" >Grand Total</td>
						<td colspan="2" class="grand_total">{{ source.currency }} <b>{{ source.grand_total }}</b></td>
					</tr>
					<tr>
						<td colspan="12" class="total_words"><b>Total Value ({{ source.currency }}) : </b> {{ total_in_words|upper }}</td>
					</tr>
					{% if source.special_instructions %}
						<tr>
							<td colspan="12" class="conditions"><b>Special Instructions:</b> {{ source.special_instructions }}
							</td>
						</tr>
					{% endif %}
					<tr>
						<td colspan="12" class="test_environment"><i>For {{ source.enterprise.name }}</i></td>
					</tr>
				</table>
			{% else %}
				{% include "sales/oa_print_item_table.html" %}
			{% endif %}
		</div>

		<div class="col-sm-12" style="text-align:right;margin-top:20px;">
			<span>{{ source.modifier }}</span><br>
			<span><b>Authorized Signatory</b></span>
			<hr style="margin-top:5px;">
		</div>
		{% if appendix_pages > 0 %}
			<div style="page-break-after: always"></div>
		{% endif %}
		{% if appendix_pages > 0 %}
			<div class="annexture_oa">ANNEXURE TO ORDER# <b>{{ source.getCode }}</b> ACKNOWLEDGEMENT ON <b>{{ oa_date }}</b></div>
			<div class="col-sm-12">
				{% include "sales/oa_print_item_table.html" %}
			</div>
			<div class="col-sm-12" style="text-align:right;margin-top:20px;">
				<span>{{ source.modifier }}</span><br>
				<span><b>Authorized Signatory</b></span>
				<hr style="margin-top:5px;">
			</div>
		{% endif %}
	</div>
</body>
</html>

