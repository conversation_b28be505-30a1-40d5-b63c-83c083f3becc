function selectAllEmployee() {
	if($("#select-all-emp").is(":checked")) {
		$(".emp_checkbox").prop("checked", true);
	}
	else {
		$(".emp_checkbox").prop("checked", false);	
	}
	selectAllCheckboxFlag();
}

function selectAllCheckboxFlag() {
	if($(".emp_checkbox").length == $(".emp_checkbox:checked").length) {
		$("#select-all-emp").next("label").removeClass("partial");
		$("#select-all-emp").prop("checked", true);
		$(".bulk_mail_download").removeClass("hide");
	}
	else if($(".emp_checkbox:checked").length >= 1 ){ 
		$("#select-all-emp").next("label").addClass("partial")
		$("#select-all-emp").prop("checked", true);
		$(".bulk_mail_download").removeClass("hide");
	}
	else {
		$("#select-all-emp").next("label").removeClass("partial");
		$("#select-all-emp").prop("checked", false);
		$(".bulk_mail_download").addClass("hide");
	}
	$(".fa-paper-plane").remove();
	$(".emp_checkbox").next("label").removeClass('hide');
	$(".currently-sent").text(0);
}