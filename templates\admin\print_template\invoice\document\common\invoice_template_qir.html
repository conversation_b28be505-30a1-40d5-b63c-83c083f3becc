<table style="width: 100% !important;">
	<tbody>
		<style>
		sup {
			line-height: 1.6;
		}
		</style>
		<tr>
			<td>
				<div class="hsn_summary_contianer">
					<h6 style="margin: 0"><b style="font-size: {{summary_res.hsn_tax_font_size}}pt;">QUALITY INSPECTION REPORT</b></h6>
					{% for key, value in qir_data.items %}
						<br/>
						{% if value|length > 0 %}
						<b style="font-size: {{summary_res.hsn_tax_font_size}}pt;">{{key}}</b>
						<table class="table qir_table{% if item_res.include_row_separator %} row-seperator{% endif %}{% if item_res.include_column_separator %} column-seperator{% endif %}" style="width: 100%; font-size: {{summary_res.hsn_tax_font_size}}pt;">
							{% for data in value %}
							<tr>
								{% for cell_data in data %}
								<td {% if forloop.counter == 1 %}style="font-weight: bold;" class="text-left"{% else %}style="font-weight: normal; word-break: break-word;" class="text-right"{% endif %}>
									{{ cell_data }}</td>
								{% endfor %}
							</tr>
							{% endfor %}
							</thead>
						</table>
						{% endif %}
					{% endfor %}
				</div>
			</td>
		</tr>
	</tbody>
</table>
