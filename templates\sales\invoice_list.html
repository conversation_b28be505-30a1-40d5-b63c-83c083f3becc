{% extends "sales/sidebar.html" %}
{% block sales %}
<style xmlns="http://www.w3.org/1999/html">
	.loading-main-container {
		display: block;
	}

	.sales-receipt-arrow_box:before,
	.sales-receipt-arrow_box:after {
		left: 342px;
	}

	.sw-deviate .sa-confirm-button-container button:focus,
	 .sw-deviate .sa-confirm-button-container button:active,
	 .sw-deviate .sa-button-container .sa-confirm-button-container button {
		background-color: #FF0000 !important;
	}

	.sw-deviate .sa-button-container button {
		background-color: #4cae4c !important;
	}

</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/invoice_list.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/issue-document.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/print.min.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/issue-document.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/mailer.js?v={{ current_version }}"></script>
<script>
	$('.nav-pills li').removeClass('active');
	{% if type == 'sales' %}
		$('#li_invoice').addClass('active');
		$("#menu_sales").addClass('selected');
	{% endif %}
	{% if type == 'dc' %}
		$('#li_dc').addClass('active');
		$("#menu_stores").addClass('selected');
	{% endif %}
	{% if type == 'internal' %}
		$('#li_issue').addClass('active');
		$("#menu_production_planning").addClass('selected');
	{% endif %}
</script>
<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">
			{% if type == 'sales' %}Invoice{% endif %}
			{% if type == 'dc' %}Delivery Challan{% endif %}
			{% if type == 'internal' %}Issue{% endif %}
		</span>
	</div>
	<div style="display:none">
		<form id="invoice_submit" method="post" action="{{edit_link}}">
			{%csrf_token%}
			<input type="hidden" value="{{ invoice.id.value }}" id="id_invoice_no" name="invoice_no"/>
			<input type="hidden" value="{{ type }}" id="id_edit_dc_type" name="edit_dc_type"/>
			<input type="submit" value="Edit" id="invoice_resubmit" hidden="hidden"/>
		</form>
	</div>
	<div>
		<div class="container-fluid">
		<!-- Page Heading -->
		<input type="hidden"  id="cgst_tax" name="cgst_tax"/>
		<input type="hidden"  id="sgst_tax" name="sgst_tax"/>
		<input type="hidden"  id="igst_tax" name="igst_tax"/>
		<div class="row">
			<div class="col-sm-12">
				{% if type == 'sales' %}
					{% if logged_in_user|canEdit:'SALES' %}
						<a href="{{edit_link}}" class="btn btn-new-item pull-right create_invoice for-primary-ent" data-tooltip="tooltip" title="Create New
							{% if type == 'sales' %} {% if isprimary_project %} Invoice {% else %}Internal Invoice{% endif %}{% endif %}
							{% if type == 'dc' %} DC {% endif %}
							{% if type == 'internal' %} Issue {% endif %}
							" style="margin-right: 15px;">
							<i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span>
						</a>
					{% else %}
						<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in Sales Module. Please contact the administrator." style="margin-right: 15px;">
							<a class="btn btn-new-item pull-right create_invoice disabled">
								<i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span>
							</a>
						</div>	
					{% endif %}	
				{% else %}
					{% if logged_in_user|canEdit:'STORES' %}
						<a href="{{edit_link}}" class="btn btn-new-item pull-right create_invoice" data-tooltip="tooltip" title="Create New
							{% if type == 'sales' %} Invoice{% endif %}
							{% if type == 'dc' %} DC {% endif %}
							{% if type == 'internal' %} Issue {% endif %}
							" style="margin-right: 15px;">
							<i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span>
						</a>
					{% else %}
						<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in Stores Module. Please contact the administrator." style="margin-right: 15px;">
							<a class="btn btn-new-item pull-right create_invoice disabled">
								<i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span>
							</a>
						</div>	
					{% endif %}	
				{% endif %}
				{% if type == 'sales' and logged_in_user|canEdit:'SALES' %}
					<span class="btn transparent-btn pull-right tour_e-invoice for-primary-ent" style="margin-top: 13px; margin-right: 15px;" onclick="generateEInvoiceRequest('multiple', 'invoice');">Generate e-Invoice</span>
				{% endif %}
				<div class="content_bg">
					<div class="page-heading">
						<input type="hidden" value="{{ type }}" id="id_dc_type" name="invoice_type"/>
					</div>
					<div class="tab-content">
						<div id="tab1" class="tab-pane fade in active">
							<div class="view_table add_table">
								<div class="filter-components">
									<div class="filter-components-container">
										<div class="dropdown">
											<button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
												<i class="fa fa-filter"></i>
											</button>
											<span class="dropdown-menu arrow_box arrow_box_filter">
												<form id="invoice_search" method="post">
													{%csrf_token %}
													<div class="col-sm-12 form-group" >
														<label>Date Range</label>
														<div id="reportrange" class="report-range form-control">
															<i class="glyphicon glyphicon-calendar"></i>&nbsp;
															<span></span> <b class="caret"></b>
															{{ search.from_date }}
															{{search.to_date }}
														</div>
													</div>
													<div class="col-sm-12 form-group for_dc for_invoice">
														<label>Party Name</label>
														{{ search.party_name }}
													</div>
													<div class="col-sm-12 form-group for-primary-ent">
														<label>Project/Tag</label>
														{{search.project}}
													</div>
													<div class="col-sm-12 form-group for_dc for_invoice">
														<label>Status</label>
														{{search.status}}
													</div>
													<div class="filter-footer">
														<input type="hidden" name="type" value="{{type}}">
														<input type="submit" class="btn btn-save" value="Apply" id="id_search_submit"/>
							      					</div>
												</form>
											</span>
										</div>
										<span class='filtered-condition filtered-date'>Date: <b></b></span>
										<span class='filtered-condition filtered-party for_dc for_invoice'>Party Name: <b></b></span>
										<span class='filtered-condition filtered-project for-primary-ent'>Project/Tag: <b></b></span>
										<span class='filtered-condition filtered-status for_dc for_invoice'>Status: <b></b></span>
									</div>
								</div>
							</div>
                            <div class="col-md-12" id="search_result">
                            	<div class="csv_export_button">
                                    {% if type == 'sales' %}
										{% if isprimary_project %}
											<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#invoice_list'), 'Invoice_List.csv']);" data-tooltip="tooltip" title="Download Invoice List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
										{% else %}
											<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#invoice_list'), 'Internal_Invoice_List.csv']);" data-tooltip="tooltip" title="Download Internal Invoice List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
										{% endif %}
									{% endif %}
									{% if type == 'dc' %}
										<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#invoice_list'), 'DC_list.csv']);" data-tooltip="tooltip" title="Download DC List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
									{% endif %}
									{% if type == 'internal' %}
										<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#invoice_list'), 'Issue_list.csv']);" data-tooltip="tooltip" title="Download Issue List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
									{% endif %}
                                </div>
								<table class="table table-bordered custom-table table-striped" id="invoice_list" style="width: 100%;">
									<thead>
									<tr>
										<th style="width: 5%;">S.No.</th>
										<th style="width: 20%; min-width: 110px;" id="for_sec_ent_invno">
											{% if type == 'internal' %}Issue No{%endif%}
											{% if type == 'dc' %}DC No{%endif%}
											{% if type == 'sales' %}Invoice No{%endif%}
										</th>
										<th style="width: 5%" class="for_dc for_invoice secondaryColumn"> Type</th>
										<th style="width: 10%;" class="for_dc for_invoice"> Prepared On</th>
										<th class="secondaryColumn" style="width: 10%;"> Issued On</th>
										<th style="width: 10%;" class="for_issue"> Issued To</th>
										<th style="width: 10%;" class="for_issue"> Issued For</th>
										<th style="width: 20%;" class="secondaryColumn">Project</th>
										<th style="width: 15%;" class="for_issue"> Material</th>
										<th style="width: 10%;" class="for_issue"> Quantity</th>
										<th style="width: 15%;"class="for_issue"> Remarks</th>
										<th style="width: 15%;" class="for_dc for_invoice"> Customer</th>
										<th style="width: 8%;" class="for_dc for_invoice"> Total Value</th>
										<th style="width: 10%;" class="for_dc for_invoice"> Status</th>
									</tr>
									</thead>
									<tbody>
										{% for in_list in inv_list %}
											<tr data-invid="{{in_list.id}}">
												<td class="text-center" width="5%">{{forloop.counter}}.</td>
		                                        <td class="text-center" style="{% if type == 'internal' %}width:200px; max-width: 250px;{% else %}width:10% {% endif %}">
		                                        	<a role="button" class="edit_link_code" data-invoiceId="{{ in_list.id }}" data-invoiceType="{{type}}" onclick="editInvoiceRow('{{ in_list.id }}','{{type}}','{{edit_link}}');">
		                                        		{{in_list|getInternalCode}}
		                                        	</a>
													{% if type == 'internal' %}
														<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement="left" title='Preview' onclick="generate_issue_pdf({{in_list.id}},'{{type}}',{{in_list.status}})">
															<i class="fa fa-file-text" aria-hidden="true" style="font-size:16px;"></i>
														</span>
														<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit' onclick="editInvoiceRow('{{ in_list.id }}', '{{type}}', '{{edit_link}}');">
															<i class='fa fa-pencil'></i>
														</span>
														<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit in New Tab' onclick="editInvoiceRow('{{ in_list.id }}', '{{type}}', '{{edit_link}}', '_blank');">
															<i class='fa fa-external-link'></i>
														</span>
													{% endif %}
												</td>
												<td class="text-center for_dc for_invoice secondaryColumn"> {{in_list.type}}</td>
												<td class="text-center for_dc for_invoice"> {{in_list.prepared_on|date:'M d, Y H:i:s'}}</td>
		                                        <td class="text-center secondaryColumn" > {{in_list.issued_on|date:'M d, Y H:i:s'}}</td>
												<td class="for_issue"> {{in_list.issued_to}}</td>
												<td class="for_issue"> {{in_list.issued_for}}</td>
												<td class="secondaryColumn"> {{in_list.project.name}} ({{in_list.project.code}})</td>
												<td class="for_issue">
													{% if in_list.items|length > 1 %}
														<i>Multiple</i>
													{% else %}
														{{in_list.items.0.item.name}}
													{% endif%}
												</td>
												<td class="text-right for_issue">
													{% if in_list.items|length > 1 %}
														{{in_list.items|length}} Items
													{% else %}
														{{in_list.getConsolidatedQuantity|floatformat:3}}
													{% endif%}
												</td>
												<td class="for_issue">
													<span class="matchCountdown">{{in_list.remarks_as_text }}</span>
												</td>
												<td class="for_dc for_invoice"> {{in_list.customer.name }} {% if isprimary_project != True %}({{in_list.customer.code }}){% endif %}</td>
												<td class="text-right for_dc for_invoice"> {{in_list.grand_total|floatformat:2 }}</td>
		                                        <td class="text-center for_dc for_invoice td_status">
		                                        	{% if in_list.status == 0 %}
		                                        		<a role="button" class="pdf_genereate table-inline-icon-bg pending pdf_genereate">Pending</a>
		                                        	{% elif in_list.status == 1 %}
		                                        		<a role="button" class="pdf_genereate table-inline-icon-bg approved pdf_genereate">Approved</a>
		                                        	{% elif in_list.status == -1 %}
		                                        		<a role="button" class="pdf_genereate table-inline-icon-bg cancelled pdf_genereate">Cancelled</a>
		                                        	{% endif %}
			                                        <span class='table-inline-icon-container'>
				                                        <span class='table-inline-icon inline-icon-document hide' data-tooltip='tooltip' data-placement='left' title='Preview' onclick="generate_pdf_ajax({{in_list.id}}, '{{type}}', {{in_list.status}});">
			                                                 <i class="fa fa-file-text"></i>
			                                            </span>
					                                    <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit' onclick="editInvoiceRow('{{ in_list.id }}', '{{type}}', '{{edit_link}}');">
					                                        <i class='fa fa-pencil'></i>
					                                    </span>
					                                    <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit&nbsp;in&nbsp;New&nbsp;Tab' onclick="editInvoiceRow('{{ in_list.id }}', '{{type}}', '{{edit_link}}', '_blank');">
					                                        <i class='fa fa-external-link'></i>
					                                    </span>
					                                </span>
												</td>
											</tr>
										{% endfor %}
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
</div>
<div id="inv_document_modal" class="modal fade" role="dialog">
  	<div class="modal-dialog modal-lg">
		<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Preview Document</h4>
      		</div>
      		<div class="modal-body text-center">
      			<input type="hidden" name="type" value="" id="modal_inv_type"/>
				<input hidden="hidden" id="modal_inv_id" name="invoice_no" value='' />
		        <input type="hidden" name="download_headers[]" value=" " id="id_header_text"/>
				<div class="row" style="display: inline-block; text-align: left;">
					<div class="col-lg-12" style="width: 898px;">
						<div class="content_bg">
							<div class="add_table" id="inv_doc_btn">
								<div class="col-sm-6 add_table_content remove-padding">
									<input id="remarks" class="form-control" enterkey="do_nothing" maxlength="248" value='{{ approval_remarks }}' placeholder="Approval/Rejection Remarks"/>
									<div class="remarks_count_link remarks_count disabled" onclick="remarksInSwal('inv_document_remarks');">
										<span class="remarks_counter">No</span><span> remarks</span>
									</div>
								</div>
								<div class="col-sm-3">
									<div>
										{% if logged_in_user|canApprove:'SALES' %}
										<span class="material_txt">
											<a role='button' id='approve_invoice' class="btn btn-save">Approve</a>
										</span>
									    {% endif %}
									    {% if logged_in_user|canApprove:'SALES' %}
											<a role='button' id='reject_invoice' class="btn btn-danger for_inv_approve">Reject</a>
										{% else %}
										{%if logged_in_user|canEdit:'SALES' %}
											<a role='button' id='reject_invoice' class="btn btn-danger for_inv_edit">Discard</a>
										{% endif %}
										{% endif %}
										{% if logged_in_user|canApprove:'SALES' %}
											<a role='button' id='regenerate_invoice' class="btn btn-save" >Regenerate</a>
										{% endif %}
									</div>
								</div>
								<div class="col-sm-3 remove-padding">
									<div class="dropdown pull-right">
										<div style="display: inline-block;">
                                            <a role="button" id='display_popup' onclick="openMailPopup()" class="btn transparent-btn" data-tooltip="tooltip" data-placement="left" title="Email"><i class="fa fa-envelope" style="font-size: 14px;" aria-hidden="true"></i></a>
										</div>
										{% if type|lower != 'internal' and type|lower != 'issue' %}
										    <button class="btn transparent-btn dropdown-toggle" id="download_copy" type="button" data-toggle="dropdown">
										    	<i class="fa fa-print" aria-hidden="true" data-tooltip="tooltip" title="Print" style="font-size: 18px; padding-right: 4px;"></i>
											    <span class="caret"></span>
											</button>
										{% endif %}
									    <div class="custom-dropdown-menu arrow_box sales-receipt-arrow_box" role="menu" id="download_copy_list" style="width: 370px;margin-top: 13px; margin-right: 5px;">
									    	<div class="print_option_container">
										      	<span role="button" class="checkbox" style="padding: 5px;">
										      		<input type="checkbox" class="chkcase parent-chk" id="print_original" value="original" />
										      		<label for="print_original">Original For Recipient</label>
										      	</span>
										      	<span role="button" class="checkbox" style="margin-left: 35px;">
										      		<input type="checkbox" class="chkcase child-chk" id="print_original_receipt" value="original_receipt" />
										      		<label for="print_original_receipt">Print Receipt Acknowledgement</label>
										      	</span>
										      	<hr style="margin: 0;" />
									      	</div>
									      	<div class="print_option_container">
										      	<span role="button" class="checkbox" style="padding: 5px;">
										      		<input type="checkbox" class="chkcase parent-chk" id="print_buyer" value="transport" />
										      		<label for="print_buyer">Duplicate For Transporter</label>
										      	</span>
										      	<span role="button" class="checkbox" style="margin-left: 35px;">
										      		<input type="checkbox" class="chkcase child-chk" id="print_buyer_receipt" value="transport_receipt" />
										      		<label for="print_buyer_receipt">Print Receipt Acknowledgement</label>
										      	</span>
										      	<hr style="margin: 0;" />
									      	</div>
									      	<div class="print_option_container">
										      	<span role="button" class="checkbox" style="padding: 5px;">
										      		<input type="checkbox" class="chkcase parent-chk" id="print_transport" value="supplier" />
										      		<label for="print_transport">Triplicate For Supplier</label>
										      	</span>
										      	<span role="button" class="checkbox" style="margin-left: 35px;">
										      		<input type="checkbox" class="chkcase child-chk" id="print_transport_receipt" value="supplier_receipt" />
										      		<label for="print_transport_receipt">Print Receipt Acknowledgement</label>
										      	</span>
										      	<hr style="margin: 0;" />
									      	</div>
									      	<div class="print_option_container">
										      	<span role="button" class="checkbox" style="padding: 5px;">
										      		<input type="checkbox" class="chkcase parent-chk" id="print_extra" value="extra_copy" />
										      		<label for="print_extra">Extra Copy</label>
										      	</span>
										      	<span role="button" class="checkbox" style="margin-left: 35px;">
										      		<input type="checkbox" class="chkcase child-chk" id="print_extra_receipt" value="extra_copy_receipt" />
										      		<label for="print_extra_receipt">Print Receipt Acknowledgement</label>
										      	</span>
										      	<hr style="margin: 0;" />
									      	</div>
									      	<span style="text-align: center; display: inline-block; width: 100%; margin-bottom: 15px; margin-top: 15px;">
									      		<span class="btn transparent-btn" onclick="generateCustomDropdown();">Print</span>
									      		<span class="btn transparent-btn" onclick="closeCustomDropdown();">Close</span>
									      	</span>
									    </div>
								  	</div>
							  	</div>
							</div>
						</div>
					</div>
				</div>
				<input type="hidden" id="inv_document_remarks" />
				<div id="inv_document_container"></div>
   			</div>
   			<div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
   		</div>
  	</div>
</div>

{% include "modal-window/eInvoice-generator.html" %}
<div class="hide">
	<form id="invoice_edit_form" method="post" action="">
		{%csrf_token%}
		<input type="hidden" id="id_edit_invoice_no" name="invoice_no" value="" />
		<input type="hidden" id="id_edit_invoice_type" name="edit_dc_type" value="" />
	</form>
</div>

<script type="text/javascript">
	$( document ).ready(function() {
	var project = JSON.parse(localStorage.getItem('project'));
    	if(project && project.type == 'Secondary'){
        	$('#for_sec_ent_invno').text('Internal Invoice No');
    	}
		$("#template_configuration").removeClass('hide');
		$("#template_configuration").click(function(){
			window.open('/erp/admin/invoice_template/','_blank');
		});
	});

	function editInvoiceRow(invoiceNo, type, url, openTarget="") {
		$("#invoice_edit_form").attr("action", url);
		$("#id_edit_invoice_no").val(invoiceNo);
		$("#id_edit_invoice_type").val(type);
		$("#invoice_edit_form").attr("target", openTarget).submit();
		var po_id = "";
		var issue_id = $("#search_invoice_id").val();
		localStorage.removeItem('PPStockDetails');
		$.ajax({
			url: "/erp/stores/json/loadpolist/",
			type: "post",
			datatype:"json",
			async: false,
			data: {invoice_id: invoiceNo},
			success: function(response){
				try{
					if (response.po_codes != null && response.po_codes.length != 0){
					po_id = response['po_codes'][0].po_id;
					}
				} catch (e) {
					console.log(e)
				}
			}
		});
		if(po_id !='')
		{
			localStorage.removeItem('PPStockDetails');
			localStorage.removeItem('MRSStockDetails');
			var issue_response = "";
			$.ajax({
				url: "/erp/sales/json/invoice/getPPStockqty/",
				type: "post",
				datatype: "json",
				data: {po_id:po_id,issue_id:invoiceNo},
				success: function (response) {
					issue_response = response;
					localStorage.setItem("PPStockDetails",JSON.stringify(response));
				}
			});
<!--			if(!issue_response){-->
				 $.ajax({
					url: "/erp/sales/json/invoice/getMRSMaterials/",
					type: "post",
					datatype: "json",
					data: {po_id:po_id},
					async: false,
					success: function (response) {
						localStorage.setItem("MRSStockDetails",JSON.stringify(response));
					}
				});
<!--            }-->

		}
	}
</script>
{% endblock %}