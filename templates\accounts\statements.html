{% extends "accounts/sidebar.html" %}
{% block statements %}
<style>
	#id_inclusions p {
		margin-bottom: 15px;
	}
	
	.netAmt_tr td {
		border: 1px solid #ddd !important;
		background:#edf2f8;
	}

	.toggle.btn {
		width: 275px !important;
		margin-top: -8px;
		background: #209be1;
		border-radius: 25px;
	}

	.toggle .toggle-handle {
		border-radius: 50px;
	    margin-left: -35px;
	    height: 25px;
	    margin-top: 3px;
	}

	.toggle .toggle-handle circle {
	    display: block;
	    margin-left: -9px;
	    margin-top: 5px;
	    font-weight: bold;
	    width: 20px;
	    background: url(/site_media/images/left_arrow.png) no-repeat;
	    height: 14px;
	}

	.toggle.off .toggle-handle circle {
	    background: url(/site_media/images/right_arrow.png) no-repeat;
	}

	.toggle.off .toggle-handle {
	    margin-left: 35px;
	}

	.toggle .btn-primary {
		background: #004195;
    	border-color: transparent;
	}

	.toggle .btn-primary:hover {
		background: #004195;
    	border-color: transparent;
    	opacity: 0.8;
	}

	.toggle .btn-default {
		background: #209be1;
    	border-color: transparent;
    	color: #FFF;
	}

	.toggle .btn-default:hover {
		background: #209be1;
    	border-color: transparent;
    	color: #FFF;
    	opacity: 0.8;
	}

	.toggle .toggle-handle.btn-default {
		background: #FFF;
	}

	.arrow_front,
	.arrow_back {
	    height: 34px;
	    margin-right: 40px;
	    position: relative;
	    line-height: 1.4em;
	    padding-right: 1em;
	    border-radius: 0 4px 5px 0;
	}
	.arrow_front:after {
	  border-left: 20px solid #209be1;
	}

	.arrow_front:after {
      	content: "";
	    position: absolute;
	    border-bottom: 17px solid transparent;
	    border-top: 17px solid transparent;
	    margin-right: -19px;
	    right: 0;
	    top: -1px;
	}

	.arrow_back:before {
	     content: '';
	    border-top: 17px solid #209be1;
	    border-bottom: 17px solid #209be1;
	    position: absolute;
	    top: -1px;
	    border-left: 20px solid transparent;
	    left: -20px;
	}

	.arrow_back:hover,
	.arrow_back:active,
	.arrow_front:hover,
	.arrow_front:active {
	  background: #44ad6b;
	  color: #FFF;
	}
	.arrow_front:hover:after,
	.arrow_front:active:after {
	  border-left: 20px solid #44ad6b;
	}

	.arrow_back:hover:before,
	.arrow_back:active:before {
	  	border-top: 17px solid #44ad6b;
	    border-bottom: 17px solid #44ad6b;
	}

	a.btn.disabled, fieldset[disabled] a.btn {
		cursor: not-allowed;
		pointer-events: auto;
		opacity: 1;
	}

	a.arrow_back.disabled,
	a.arrow_back.disabled:hover {
		background: #ddd;
		color: #555;
	}

	.arrow_back.disabled:before {
		border-top: 17px solid #ddd;
    	border-bottom: 17px solid #ddd;
	}

	.arrow_front.disabled:after {
		border-left: 20px solid #ddd;
	}

	.balance-sheet-csv {
		position: absolute;
		right: 16px;
		top: -40px;
	}

</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-toggle.min.css?v={{ current_version }}" >
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-toggle.min.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-multiselect.js?v={{ current_version }}"></script>

<div class="right-content-container download-icon-onTop" style="padding-bottom: 15px;">
	<div class="page-title-container">
		<span class="page-title">P&L Statements</span>
	</div>
	<div class="col-sm-12">
    <h3 style="padding-bottom: 1px;"></h3>
		<input type="hidden" id="id_message_box" value="{{closure_voucher_code}}"/>
		<input type="label" name="projects" id="default_projects"  value="{{projects}}" hidden="hidden">
        {% ifequal view_book 'true'%}
        <span style="font-size: 20px; margin-left: 15px;"> FINALIZED STATEMENTS</span><br />
        {% endifequal %}
        <span style="font-size: 15px; margin-left: 15px;"> {% ifequal next_opening_date previous_closure_date %}Earliest Transaction Date:{% else %}Previous Book Closure Date:{% endifequal %} <b>{{previous_closure_date|date:'M d, Y'}}</b></span>
        <form action="/erp/accounts/trial_balance/" method="post" id="id_accounts_trial_balance_criteria_form">
	        {% csrf_token %}
            <div class="filter-components" style="width: auto; margin-left: 0; float: left; margin-top: 22px;">
                <div class="filter-components-container">
                    <div class="dropdown">
                    <button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
                            <i class="fa fa-filter"></i>
                        </button>
                        <span class="dropdown-menu arrow_box arrow_box_filter">
                            <div class="col-sm-12 form-group">
                                <div class="hide">
                                    <input type="checkbox" id="statement_type" data-toggle="toggle" data-on="Provisional" data-off="Actual"
                                       {%ifequal is_provisional 'true'%}checked="checked"{%endifequal%}/>
                                </div>
                                <span class="super-markable-text large statement_type_txt" style="margin-top: 7px;"></span>
                            </div>
                            <div class="col-sm-12 form-group" >
                                <label>For Period</label>
                                <div id="statement_period" class="report-range form-control">
                                    <i class="glyphicon glyphicon-calendar"></i>&nbsp;
                                    <span></span> <b class="caret"></b>
                                    <input type="hidden" value="{{opening_on|date:'Y-m-d'}}" id="tb_opening_on">
                                    <input type="hidden" value="{{closing_on|date:'Y-m-d'}}" id="tb_closing_on">
                                    <input id="id_opening_on" class="form-control fromdate datepicker opening_date" value="{{opening_on|date:'Y-m-d'}}" name="opening_on" type="hidden"/>
                                    <input id="id_closing_on" class="form-control todate datepicker closing_date" value="{{closing_on|date:'Y-m-d'}}" name="closing_on" type="hidden"/>
                                    <input id="id_is_provisional" value="{{is_provisional}}" name="is_provisional" type="hidden" class="statement_type"/>
                                    <input type="hidden" value="{{previous_closure_date|date:'Y-m-d'}}" id="id_previous_closure" name="previous_closure_date">
                                    <input type="hidden" value="{{next_opening_date|date:'Y-m-d'}}" id="id_next_opening" name="next_opening_date">
                                </div>
                            </div>
                            <div class="col-sm-12 form-group hide" >
                                <div class=" form-group multiselect_option hide" id="project_div">
                                    <label id="project_display">Project<span class="mandatory_mark hide"> *</span></label>
                                    <select class="form-control" name="select" id="project" multiple="multiple"></select>
                                    <input type="label" value="" class="txtbox10" name="project_details" id="project_details"  width="30%" hidden="hidden">
                                </div>
                            </div>
                            <div class="filter-footer">
                                <input type="submit" value="Run Trial Balance" id="run_trial_balance" class="btn btn-save btn-margin-1">
                            </div>
                        </span>
                    </div>
                    <span class='filtered-condition filtered-type'><b></b></span>
                    <span class='filtered-condition filtered-date'>Date: <b></b></span>
<!--                    <span class='filtered-condition filtered-project for-primary-ent'>Project/Tag: <b></b></span>-->
                </div>
            </div>
            <div class="col-sm-5 ">
                <div class="material_txt {% ifequal view_book 'true'%}hide{% endifequal %}">
                	<a class="arrow_front btn btn-save btn-margin-1" id="a_trial_balance" style="border-radius: 4px; width: 140px;">Trial Balance</a>
                	<a class="arrow_back btn btn-save btn-margin-1 disabled {%ifequal is_provisional 'false'%}arrow_front{%endifequal%}"
	                   id="a_generate" style="margin-left: -16px; width: 150px;">Generate BS, P&L</a>
	                {% if access_level.approve %}
                		<a class="arrow_back btn btn-save form-actual btn-margin-1 disabled {%ifequal is_provisional 'true'%}hide{%endifequal%}"
	                   id="a_finalize" style="margin-left: -16px; width: 130px;">Close Book</a>
	                {% else %}
	                	<a class="arrow_back btn btn-save form-actual btn-margin-1 disabled {%ifequal is_provisional 'true'%}hide{%endifequal%}"
	                	data-tooltip="tooltip" title="You do not have adequate permission to Close Book in Accounts Module. Please contact the administrator." style="margin-left: -16px; width: 130px;">Close Book</a>
	                {% endif %}
                    <input type="submit" value="Run Trial Balance" id="id_run_trial_balance" class="btn btn-save btn-margin-1 hide">
				</div>
			</div>
        </form>

		<div id="modal_closed_books" class="modal fade" role="dialog">
			<div class="modal-dialog modal-lg">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal">&times;</button>
						<h4 class="modal-title">Closed Books</h4>
					</div>
					<div class="modal-body">
						<table class="table table-bordered table-striped custom-table sm-padding" id="closed_book_listing">
							<thead>
								<tr>
									<th>Period</th>
									<th>Voucher</th>
									<th>Closed By</th>
									<th>Closed On</th>
									<th>Reopen</th>
								</tr>
							</thead>
							<tbody>
							{%for book in closed_books %}
							<tr>
							    <td class="text-center">
								    <a href="#" id="id_view_book_{{book.since|date:'Y_m_d'}}">
									    {{book.since|date:'M d, Y'}} - {{book.till|date:'M d, Y'}}</a>
									<form action="/erp/accounts/view_book/" method="post" target="_blank">{% csrf_token %}
										<input type="hidden" name="since" value="{{book.since|date:'Y-m-d'}}"
										       id="view_since_{{book.since|date:'Y_m_d'}}"/>
										<input type="hidden" name="till" value="{{book.till|date:'Y-m-d'}}"
										       id="view_till_{{book.till|date:'Y_m_d'}}"/>
										<input type="submit" class="hide"
										       id="view_book_{{book.since|date:'Y_m_d'}}-{{book.till|date:'Y_m_d'}}"/>
									</form>
									<script type="text/javascript">
										$("#id_view_book_{{book.since|date:'Y_m_d'}}").click(function(){
											$("#view_book_{{book.since|date:'Y_m_d'}}-{{book.till|date:'Y_m_d'}}").click();
                                            ga('send', 'event', 'Accounts', 'View Closed Book', $('#enterprise_label').val(), 1);
										});
									</script></td>
								<td class="text-center">
									<form target="_blank" action="/erp/accounts/voucher/edit/#tab2" method="post" id="edit_{{ book.closure_voucher_id }}_voucher">{% csrf_token %}
										<a role="button" onclick="javascript:clickButton('edit_voucher_{{ book.closure_voucher_id }}');">{{book.closure_voucher|getCode}}</a>
										<input type="hidden" value="{{ book.closure_voucher_id }}" id="id_id" name="voucher_no"/>
										<input type="hidden" value="{{ book.closure_voucher.type_id }}"  name="voucher_type"/>
										<input type="submit" value="Edit" id="edit_voucher_{{ book.closure_voucher_id }}" hidden="hidden"/>
									</form>
								</td>
								<td>{{book.closed_by.first_name}} {{book.closed_by.last_name}}</td>
								<td class="text-center">{{book.created_on|date:'M d, Y h:i A'}}</td>
								<td class="text-center">
									{% if access_level.approve %}
										<a role="button" id="re_open_book_{{book.since|date:'Y_m_d'}}">
											<img src="/site_media/images/book_unlock.png" style="width: 20px;" />
										</a>
									{% else %}
										<a role="button" style="opacity: 0.7; cursor: not-allowed;" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to Reopen the Closed Book. Please contact the administrator.">
											<img src="/site_media/images/book_unlock.png" style="width: 20px; " />
										</a>
									{% endif %}
									<form action="/erp/accounts/reopen_books/" method="post">{% csrf_token %}
										<input type="hidden" name="since" value="{{book.since|date:'Y-m-d'}}" id="since_{{book.since|date:'Y_m_d'}}"/>
										{% if access_level.approve %}
											<input type="submit" class="hide" id="re_open_button_{{book.since|date:'Y_m_d'}}"/>
								     	{% endif %}
									</form>
									<script type="text/javascript">
										$("#re_open_book_{{book.since|date:'Y_m_d'}}").click(function(){
											swal({
												title: "Account Books Re-Open!",
												text: "All Account Statements finalized later than {{book.since|date:'M d, Y'}} will have to be re-opened.<br /><br />Do you want to continue?",
												type: "warning",
												showCancelButton: true,
												confirmButtonColor: "#209be1",
												confirmButtonText: "Yes, Open All!",
												closeOnConfirm: true
											},
											function(){
												$("#re_open_button_{{book.since|date:'Y_m_d'}}").click();
                                                ga('send', 'event', 'Accounts', 'Re-open Closed Book', $('#enterprise_label').val(), 5);
											});
										});
									</script>
								</td>
							</tr>
							{%endfor%}
							{% if closed_books|length == 0 %}
							<tr><td colspan="5" align="center"><b>No records found</b></td></tr>
							{% endif %}
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>

		{% if trial_balance|length > 0 %}
		<script type="text/javascript">$("#a_generate").removeClass('disabled');</script>
		<div class="col-sm-12">
			<div class="col-sm-3"></div>
			<div class="col-sm-9">
				<form action="/erp/accounts/statements/" method="post" id="id_accounts_statements_criteria_form">{% csrf_token %}
					<input id="id_statements_opening_on" value="{{opening_on|date:'Y-m-d'}}" class="opening_date" name="opening_on" type="hidden"/>
					<input id="id_statements_closing_on" value="{{closing_on|date:'Y-m-d'}}" class="closing_date" name="closing_on" type="hidden"/>
					<input id="id_statement_is_provisional" value="{{is_provisional}}" name="is_provisional" type="hidden" class="statement_type"/>
					<input type="hidden" value="{{previous_closure_date|date:'Y-m-d'}}" id="id_st_previous_closure" name="previous_closure_date">
					<input type="hidden" value="{{next_opening_date|date:'Y-m-d'}}" id="id_st_next_opening" name="next_opening_date">
	                <div class="material_txt hide">
		                <input type="button" value="Generate" id="div_generate_statements" class="btn btn-save btn-margin-1">
						<input type="submit" value="Generate" id="id_generate_statements" class="btn btn-save btn-margin-1 hide">
		                <input type="label" value="" class="txtbox10" name="statement_project_details" id="statement_project_details"  width="30%" hidden="hidden">
					</div>
					<div id="modalInclusions" class="modal fade" role="dialog">
						<div class="modal-dialog">
							<div class="modal-content">
								<div class="modal-header">
									<button type="button" class="close" data-dismiss="modal">&times;</button>
									<h4 class="modal-title">Moderation</h4>
								</div>
								<div class="modal-body">
									<div id="id_inclusions">
										{{inclusions.period_profit}}{{ inclusions.goods_opening }}{{ inclusions.goods_closing }}
										<div class="col-sm-12 form-group">
											<center><h4>Raw Materials & Components</h4></center>
										</div>
										<div class="row">
											<div class="col-sm-6 form-group"><label>Opening Stock</label></div>
											<div class="col-sm-6 form-group">{{inclusions.consumables_opening}}</div>
										</div>
										<div class="row">
											<div class="col-sm-6 form-group"><label>Closing Stock</label></div>
											<div class="col-sm-6 form-group">{{inclusions.consumables_closing}}</div>
										</div>
										<div class="col-sm-12 form-group">
											<center><h4>Goods (Work-In-Progress & Finished)</h4></center>
										</div>
										<div class="row">
											<div class="col-sm-6 form-group"><label>Work-In-Progress Opening</label></div>
											<div class="col-sm-6 form-group">{{inclusions.wip_opening}}</div>
										</div>
										<div class="row">
											<div class="col-sm-6 form-group"><label>Work-In-Progress Closing</label></div>
											<div class="col-sm-6 form-group">{{inclusions.wip_closing}}</div>
										</div>
										<div class="row">
											<div class="col-sm-6 form-group"><label>Finished Goods Opening</label></div>
											<div class="col-sm-6 form-group">{{inclusions.fg_opening}}</div>
										</div>
										<div class="row">
											<div class="col-sm-6 form-group"><label>Finished Goods Closing</label></div>
											<div class="col-sm-6 form-group">{{inclusions.fg_closing}}</div>
										</div>
										<div class="row">
											<div class="col-sm-6 form-group"><label>Total Opening (FG + WIP)</label></div>
											<div class="col-sm-6 form-group">{{inclusions.goods_opening}}
												<input id="id_goods_opening_display" class="form-control disabled" readonly="readonly"
												       value="{{inclusions.goods_opening.value}}" name="goods_closing_display"
												       type="text">
											</div>
										</div>
										<div class="row">
											<div class="col-sm-6 form-group"><label>Total Closing (FG + WIP)</label></div>
											<div class="col-sm-6 form-group">{{inclusions.goods_closing}}
											    <input id="id_goods_closing_display" class="form-control disabled" readonly="readonly"
											       value="{{inclusions.goods_closing.value}}" name="goods_closing_display"
											       type="text"></div>
										</div>
										<div class="col-sm-12 form-group">
											<center><h4>Sales</h4></center>
										</div>
										<div class="row">
											<div class="col-sm-4 form-group"><label>Tax Value</label></div>
											<div class="col-sm-8 form-group">{{inclusions.sales_tax}}</div>
										</div>
									</div>
								</div>
								<div class="modal-footer">
									<button type="button" class="btn btn-save" id="generate_statement">Proceed</button>
									<button type="button" class="btn btn-cancel" data-dismiss="modal">Close</button>
								</div>
							</div>
						</div>
					</div>
				</form>
			</div>
		</div>
		<div class="col-sm-2 hide">
			<form action="/erp/accounts/close_book/" method="post" id="id_accounts_book_close_criteria_form">{% csrf_token %}
				<input id="id_book_opening_on" value="{{opening_on|date:'Y-m-d'}}" class="opening_date" name="opening_on" type="hidden"/>
				<input id="id_book_closing_on" value="{{closing_on|date:'Y-m-d'}}" class="closing_date" name="closing_on" type="hidden"/>
				<input id="id_bc_previous_closure" value="{{previous_closure_date|date:'Y-m-d'}}" name="previous_closure_date" type="hidden"/>
				<input id="id_book_is_provisional" value="{{is_provisional}}" name="is_provisional" type="hidden" class="statement_type"/>
				<input type="hidden" value="{{next_opening_date|date:'Y-m-d'}}" id="id_bc_next_opening" name="next_opening_date">
				<div class="hide">{{inclusions}}</div>
                <div class="material_txt">
                    <input type="submit" id="id_close_book" class="btn btn-save btn-margin-1">
                </div>
			</form>
		</div>
	{% endif %}
    </div>
	<div class="col-sm-12">
		<div class="col-sm-12">
			{% if trial_balance|length > 0 %}
			<div class="contant-container-nav hide" id="show-all-tab" style="padding: 15px 5px 0;">
				<ul class="nav nav-tabs list-inline">
					<li class="active"><a id="tab1_view" data-toggle="tab" href="#tab1">Trial Balance</a></li>
					<li><a id="tab2_view" data-toggle="tab" href="#tab2">{%ifequal is_provisional 'true'%}Provisional {%endifequal%}Balance Sheet</a></li>
					<li><a id="tab3_view" data-toggle="tab" href="#tab3">{%ifequal is_provisional 'true'%}Provisional {%endifequal%}Profit and Loss</a></li>
					<li><a id="tab4_view" data-toggle="tab" href="#tab4">Notes</a></li>
				</ul>
				<button class="btn btn-add-new pull-right" style="margin-top: -40px; background: #FFF; color: #337ab7;" onclick="tablesToExcelLoad();" data-tooltip="tooltip" title="Download Entire Statement in XLS"><i class="fa fa-download" aria-hidden="true"></i> &nbsp; as xls</button>
			</div>
			<div class="tab-content" style="margin-top: 30px;">
				<div id="tab1" class="tab-pane fade in active">
					<div class="col-sm-6" style="padding-left: 320px;">
						<i>(For period between {{opening_on|date:'M d, Y'}} and {{closing_on|date:'M d, Y'}})</i>
					</div>
					<div class="col-sm-6 remove-padding">
						<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#table_trial_balance'), 'Trial_Balance.csv']);" data-tooltip="tooltip" title="Download Trial Balance in CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
					</div>
					<table class="table table-bordered table-striped custom-table sm-padding" id="table_trial_balance">
						<thead>
						<tr>
							<th align="center">S.No</th>
							<th align="center">Particulars</th>
							<th align="center">Debit ({{home_currency}})</th>
							<th align="center">Credit ({{home_currency}})</th>
						</tr>
						</thead>
						<tbody>
						{% for note in trial_balance %}
						<tr>
							{% if forloop.counter == 1 %}
								<td align="right"></td>
								<td align="center"><b>{{note.label|safe}}</b></td>
							{% else %}
								<td align="right" data-type="Number">{{forloop.counter|add:-1}}.</td>
								<td>{{note.label|safe}}</td>
							{% endif %}
							<td align="right" data-type="Number">{{note.debit}}</td>
							<td align="right" data-type="Number">{{note.credit}}</td>
						</tr>
						{% endfor %}
						</tbody>
					</table>
				</div>
				{% if notes|length > 0 %}
				<script>$("#show-all-tab").removeClass('hide');</script>
				<script type="text/javascript">$("#a_finalize").removeClass('disabled');</script>
				<div id="tab2" class="tab-pane fade">
					<i>(As on {{closing_on|date:'M d, Y'}})</i>
					<div class="col-sm-12 remove-padding">
						<div class="col-sm-6" style="padding-left: 0;">
							<a role="button" class="btn btn-add-new balance-sheet-csv" onclick="GeneralExportTableToCSV.apply(this, [$('#{{liabilities.label|toDOMId}}'), 'Liabilities-{{closing_on|date:'M d, Y'|toDOMId}}.csv']);" data-tooltip="tooltip" title="Download Liability Sheet in CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
							<table class="table table-bordered table-striped custom-table sm-padding" id="{{liabilities.label|toDOMId}}">
							<thead>
							<tr>
								<th align="center">S.No</th>
								<th align="center">{{liabilities.label}}</th>
								<th align="center">{{home_currency}}</th>
							</tr>
							</thead>
							<tbody>
								{% for note in liabilities.children %}
								<tr>
									<td align="right">{{forloop.counter}}.</td>
									<td><b>{{note.label|safe}}</b></td>
									<td align="right">{% if note.credit >= note.debit %}
											{{note|getBalance:0|floatformat:0}}
										{% else %}
											({{note|getBalance:0|multiply:-1|floatformat:0}})
										{%endif%}</td>
								</tr>
									{% for child in note.children%}
									<tr>
										<td></td>
										<td>({{forloop.counter|to_char}}) {{child.label|safe}}</td>
										<td align="right">{% if child.credit >= child.debit %}
												{{child|getBalance:0|floatformat:0}}
											{% else %}
												({{child|getBalance:0|multiply:-1|floatformat:0}})
											{%endif%}</td>
									</tr>
									{% endfor %}
								{% endfor %}
								{% if liabilities|getBalance:0 < assets|getBalance:0|multiply:-1 %}
									<tr id="row_liabilities_difference">
										<td></td>
										<td class="text-right"><b><i>Difference</i></b></td>
										<td align="right"><b><i><span id="liabilities_difference">
										{% if liabilities|getBalance:0 >= 0 %}
											{% custom_subtract assets|getBalance:0|multiply:-1|floatformat:0 liabilities|getBalance:0|floatformat:0 %}
										{% else %}
											{% custom_subtract assets|getBalance:0|multiply:-1|floatformat:0 liabilities|getBalance:0|multiply:-1|floatformat:0 %}
										{%endif%}
										</span></i></b></td>
									</tr>
								{%endif%}
								<tr>
									<td></td>
									<td class="text-right"><b>Total</b></td>
									<td align="right"><b>
										<span id="id_liabilities_total">
											{% if liabilities|getBalance:0 < assets|getBalance:0|multiply:-1%}
												{% if liabilities|getBalance:0 >= 0 %}
													{% getGrandTotal liabilities|getBalance:0|floatformat:0 assets|getBalance:0|multiply:-1|floatformat:0 liabilities|getBalance:0|floatformat:0%}
												{% else %}
													({%getGrandTotal liabilities|getBalance:0|multiply:-1|floatformat:0 assets|getBalance:0|multiply:-1|floatformat:0 liabilities|getBalance:0|multiply:-1|floatformat:0%})
												{%endif%}
											{% else %}
												{% if liabilities|getBalance:0 >= 0 %}
													{{liabilities|getBalance:0|floatformat:0}}
												{% else %}
													({{liabilities|getBalance:0|multiply:-1|floatformat:0}})
												{%endif%}
											{%endif%}
										</span>
									</b></td>
								</tr>
								</tbody>
							</table>
						</div>
						<div class="col-sm-6" style="padding-right: 0;">
							<a role="button" class="btn btn-add-new balance-sheet-csv" style="right: 0;" onclick="GeneralExportTableToCSV.apply(this, [$('#{{assets.label|toDOMId}}'), 'Assets-{{closing_on|date:'M d, Y'|toDOMId}}.csv']);" data-tooltip="tooltip" title="Download Asset Sheet in CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
							<table class="table table-bordered table-striped custom-table sm-padding" id="{{assets.label|toDOMId}}">
							<thead>
							<tr>
								<th align="center">S.No</th>
								<th align="center">{{assets.label}}</th>
								<th align="center">{{home_currency}}</th>
							</tr>
							</thead>
							<tbody>
								{% for note in assets.children %}
								<tr>
									<td align="right">{{forloop.counter}}.</td>
									<td><b>{{note.label|safe}}</b></td>
									<td align="right">{% if note.credit > note.debit %}
											({{note|getBalance:0|floatformat:0}})
										{% else %}
											{{note|getBalance:0|multiply:-1|floatformat:0}}
										{%endif%}</td>
								</tr>
									{% for child in note.children%}
									<tr>
										<td></td>
										<td>({{forloop.counter|to_char}}) {{child.label|safe}}</td>
										<td align="right">{% if child.credit > child.debit %}
												({%if child.label == 'Inventories' %}{{child.credit|floatformat:0}}{% else %}{{child|getBalance:0|floatformat:0}}{%endif%})
											{% else %}
												{%if child.label == 'Inventories' %}{{child.debit|floatformat:0}}{% else %}{{child|getBalance:0|multiply:-1|floatformat:0}}{%endif%}
											{%endif%}</td>
									</tr>
									{% endfor %}
								{% endfor %}
								{% if liabilities|getBalance:0 > assets|getBalance:0|multiply:-1 %}
									<tr id="row_assets_difference">
										<td></td>
										<td class="text-right"><b><i>Difference</i> </b></td>
										<td align="right"><b><i><span id="assets_difference">
										{% if assets|getBalance:0|multiply:-1 >= 0 %}
											{% custom_subtract liabilities|getBalance:0|floatformat:0 assets|getBalance:0|multiply:-1|floatformat:0 %}
										{% else %}
											{% custom_subtract liabilities|getBalance:0|floatformat:0 assets|getBalance:0|multiply:-1|floatformat:0 %}
										{%endif%}
										</span></i></b></td>
									</tr>
								{%endif%}
								<tr>
									<td></td>
									<td class="text-right"><b>Total</b></td>
									<td align="right"><b>
										<span id="id_assets_total">
											{% if liabilities|getBalance:0 > assets|getBalance:0|multiply:-1 %}
												{% if assets|getBalance:0|multiply:-1 >= 0 %}
													{% getGrandTotal assets|getBalance:0|multiply:-1|floatformat:0 liabilities|getBalance:0|floatformat:0 assets|getBalance:0|multiply:-1|floatformat:0%}
												{% else %}
													{%getGrandTotal assets|getBalance:0|multiply:-1|floatformat:0 liabilities|getBalance:0|floatformat:0 assets|getBalance:0|multiply:-1|floatformat:0%}
												{%endif%}
											{% else %}
												{% if assets|getBalance:0 > 0 %}({{assets|getBalance:0|floatformat:0}})
												{% else %}{{assets|getBalance:0|multiply:-1|floatformat:0}}{%endif%}
											{%endif%}
											</span>
										</b>
										<input type="text" class="hide" id="id_net_assets"
										       value="{{assets|getBalance:0|multiply:-1|floatformat:0}}"></td>
								</tr>
								</tbody>
							</table>
						</div>
						<div class="col-sm-12 hide" id="div_asset_and_liability">
							<table class="table table-bordered table-striped custom-table sm-padding" id="asset_and_liability">
								<tr>
									<td colspan="3" style="text-align: center;">EQUITIES AND LIABILITIES</td>
								</tr>
								<tr>
									<th align="center">S.No</th>
									<th align="center">{{liabilities.label}}</th>
									<th align="center">{{home_currency}}</th>
								</tr>

								{% for note in liabilities.children %}
								<tr>
									<td align="right" data-type="Number">{{forloop.counter}}.</td>
									<td><b>{{note.label|safe}}</b></td>
									{% if note|getBalance:0 >= 0 %}
										<td align="right" data-type="Number" data-value='{{note|getBalance:0|floatformat:0}}'>{{note|getBalance:0|floatformat:0}}</td>
									{% else %}
										<td align="right" data-type="Number" data-value='-{{note|getBalance:0|multiply:-1|floatformat:0}}'>-{{note|getBalance:0|multiply:-1|floatformat:0}}</td>
									{%endif%}
								</tr>
									{% for child in note.children%}
									<tr>
										<td></td>
										<td>({{forloop.counter|to_char}}) {{child.label|safe}}</td>
										{% if child|getBalance:0 >= 0 %}
											<td align="right" data-type="Number" data-value='{{child|getBalance:0|floatformat:0}}'>{{child|getBalance:0|floatformat:0}}</td>
										{% else %}
											<td align="right" data-type="Number" data-value='-{{child|getBalance:0|multiply:-1|floatformat:0}}'>-{{child|getBalance:0|multiply:-1|floatformat:0}}</td>
										{%endif%}
									</tr>
									{% endfor %}
								{% endfor %}
								{% if liabilities|getBalance:0 < assets|getBalance:0|multiply:-1 %}
									<tr>
										<td></td>
										<td class="text-right"><b><i>Difference</i></b></td>
										<td align="right"><b><i><span>
										{% if liabilities|getBalance:0 >= 0 %}
											{% custom_subtract assets|getBalance:0|multiply:-1|floatformat:0 liabilities|getBalance:0|floatformat:0 %}
										{% else %}
											{% custom_subtract assets|getBalance:0|multiply:-1|floatformat:0 liabilities|getBalance:0|multiply:-1|floatformat:0 %}
										{%endif%}
										</span></i></b></td>
									</tr>
								{%endif%}
								<tr>
									<td></td>
									<td class="text-right"><b>Total</b></td>
									<td align="right"><b>
										<span>
											{% if liabilities|getBalance:0 < assets|getBalance:0|multiply:-1%}
												{% if liabilities|getBalance:0 >= 0 %}
													{% getGrandTotal liabilities|getBalance:0|floatformat:0 assets|getBalance:0|multiply:-1|floatformat:0 liabilities|getBalance:0|floatformat:0%}
												{% else %}
													({%getGrandTotal liabilities|getBalance:0|multiply:-1|floatformat:0 assets|getBalance:0|multiply:-1|floatformat:0 liabilities|getBalance:0|multiply:-1|floatformat:0%})
												{%endif%}
											{% else %}
												{% if liabilities|getBalance:0 >= 0 %}
													{{liabilities|getBalance:0|floatformat:0}}
												{% else %}
													({{liabilities|getBalance:0|multiply:-1|floatformat:0}})
												{%endif%}
											{%endif%}
										</span>
									</b></td>
								</tr>
								<tr>
									<td colspan="3"></td>
								</tr>
								<tr>
									<td colspan="3"></td>
								</tr>
								<tr>
									<td colspan="3"></td>
								</tr>
								<tr>
									<td colspan="3" style="text-align: center;">ASSETS</td>
								</tr>
								<tr>
									<th align="center">S.No</th>
									<th align="center">{{assets.label}}</th>
									<th align="center">{{home_currency}}</th>
								</tr>

								{% for note in assets.children %}
								<tr>
									<td align="right" data-type="Number">{{forloop.counter}}.</td>
									<td><b>{{note.label|safe}}</b></td>
									{% if note.credit > note.debit %}
										<td align="right" data-type="Number" data-value='-{{note|getBalance:0|floatformat:0}}'>-{{note|getBalance:0|floatformat:0}}</td>
									{% else %}
										<td align="right" data-type="Number" data-value='{{note|getBalance:0|multiply:-1|floatformat:0}}'>{{note|getBalance:0|multiply:-1|floatformat:0}}</td>
									{%endif%}
								</tr>
									{% for child in note.children%}
									<tr>
										<td></td>
										<td>({{forloop.counter|to_char}}) {{child.label|safe}}</td>
										{% if child.credit > child.debit %}
											<td align="right" data-type="Number" data-value='-{{child|getBalance:0|floatformat:0}}'>-{{child|getBalance:0|floatformat:0}}</td>
										{% else %}
											<td align="right" data-type="Number" data-value='{{child|getBalance:0|multiply:-1|floatformat:0}}'>{{child|getBalance:0|multiply:-1|floatformat:0}}</td>
										{%endif%}
									</tr>
									{% endfor %}
								{% endfor %}
								{% if liabilities|getBalance:0 > assets|getBalance:0|multiply:-1 %}
									<tr>
										<td></td>
										<td class="text-right"><b><i>Difference</i></b></td>
										<td align="right"><b><i><span>
										{% if assets|getBalance:0|multiply:-1 >= 0 %}
											{% custom_subtract liabilities|getBalance:0|floatformat:0 assets|getBalance:0|multiply:-1|floatformat:0 %}
										{% else %}
											{% custom_subtract liabilities|getBalance:0|floatformat:0 assets|getBalance:0|multiply:-1|floatformat:0 %}
										{%endif%}
										</span></i></b></td>
									</tr>
								{%endif%}
								<tr>
									<td></td>
									<td class="text-right"><b>Total</b></td>
									<td align="right"><b><span>
											{% if liabilities|getBalance:0 > assets|getBalance:0|multiply:-1 %}
												{% if assets|getBalance:0|multiply:-1 >= 0 %}
													{% getGrandTotal assets|getBalance:0|multiply:-1|floatformat:0 liabilities|getBalance:0|floatformat:0 assets|getBalance:0|multiply:-1|floatformat:0%}
												{% else %}
													{% getGrandTotal assets|getBalance:0|multiply:-1|floatformat:0 liabilities|getBalance:0|floatformat:0 assets|getBalance:0|multiply:-1|floatformat:0%}
												{%endif%}
											{% else %}
												{% if assets|getBalance:0 > 0 %}-{{assets|getBalance:0|floatformat:0}}
												{% else %}{{assets|getBalance:0|multiply:-1|floatformat:0}}{%endif%}
											{%endif%}
											</span>
										</b>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</div>

				<div id="tab3" class="tab-pane fade">
<!--					<h4>{%ifequal is_provisional 'true'%}Provisional {%endifequal%}Profit and Loss Statement</h4>-->
					<i>(For period between {{opening_on|date:'M d, Y'}} and {{closing_on|date:'M d, Y'}})</i>
					<a role="button" class="btn btn-add-new pull-right" style="margin-top: -20px;" onclick="GeneralExportTableToCSV.apply(this, [$('#provision_PL_statement'), 'Profit_and_Loss_{{opening_on|date:'M d, Y'|toDOMId}}-{{closing_on|date:'M d, Y'|toDOMId}}.csv']);" data-tooltip="tooltip" title="Download Profit & Loss Statement in CSV"><i class="fa fa-download" aria-hidden="true"></i></a>

					<table class="table table-bordered table-striped custom-table sm-padding" id="provision_PL_statement">
						<thead>
						<tr>
							<th align="center">S.No</th>
							<th align="center">Particulars</th>
							<th align="center">{{home_currency}}</th>
						</tr>
						</thead>
						<tbody>
						{% for note in profit_and_loss %}
						<tr>
							<td align="right" data-type="Number">{{forloop.counter}}.</td>
							<td><b>{{note.label|safe}}</b></td>
							{% if note|getBalance:0 >= 0 %}
								{% if note.credit == note.debit %}<td align="right" data-value='0' data-type="Number">0</td>{%else%}
									<td align="right" data-value='{%if note.display_debit_positive %}-{%endif%}{{note|getBalance:0|floatformat:0}}' data-type="Number">{%if note.display_debit_positive %}({{note|getBalance:0|floatformat:0}}){%else%}{{note|getBalance:0|floatformat:0}}{%endif%}</td>
								{% endif %}
							{% else %}
								<td align="right" data-value='{%if not note.display_debit_positive %}-{%endif%}{{note|getBalance:0|multiply:-1|floatformat:0}}' data-type="Number">{%if note.display_debit_positive %}{{note|getBalance:0|multiply:-1|floatformat:0}}{%else%}({{note|getBalance:0|multiply:-1|floatformat:0}}){%endif%}</td>
							{%endif%}
						</tr>
						{% for child in note.children%}
						<tr>
							<td></td>
							<td>({{forloop.counter|to_char}}) {{child.label|safe}}</td>
							{% if child|getBalance:0 >= 0 %}
								{% if child.credit == child.debit %}<td align="right" data-value='0' data-type="Number">0</td>{%else%}
									<td align="right" data-value='{%if child.display_debit_positive %}-{%endif%}{{child|getBalance:0|floatformat:0}}' data-type="Number">{%if child.display_debit_positive %}({{child|getBalance:0|floatformat:0}}){%else%}{{child|getBalance:0|floatformat:0}}{%endif%}</td>
								{% endif %}
							{% else %}
								<td align="right" data-value='{%if not child.display_debit_positive %}-{%endif%}{{child|getBalance:0|multiply:-1|floatformat:0}}' data-type="Number">{%if child.display_debit_positive %}{{child|getBalance:0|multiply:-1|floatformat:0}}{%else%}({{child|getBalance:0|multiply:-1|floatformat:0}}){%endif%}</td>
							{%endif%}
						</tr>
						{% endfor %}
						{% endfor %}
						</tbody>
					</table>
				</div>
				<div id="tab4" class="tab-pane fade">
<!--					<h4>Notes</h4>-->
					<i>(For period between {{opening_on|date:'M d, Y'}} and {{closing_on|date:'M d, Y'}})</i>
					<a role="button" class="btn btn-add-new pull-right csv_download_hidden" style="margin-top: -20px;" onclick="GeneralExportTableToCSV.apply(this, [$('#report_notes'), 'Notes_{{opening_on|date:'M d, Y'|toDOMId}}-{{closing_on|date:'M d, Y'|toDOMId}}.csv']);" data-tooltip="tooltip" title="Download Notes Report in CSV"><i class="fa fa-download" aria-hidden="true"></i></a>

					<table class="table table-bordered table-striped custom-table sm-padding" id="report_notes">
						<thead>
						<tr>
							<th align="center">Note No.</th>
							<th align="center">Particulars</th>
							<th align="center">{{home_currency}}</th>
						</tr>
						</thead>
						<tbody>
						{% for note in notes %}
							<tr>
								<td align="right" data-type="Number">{{forloop.counter}}.</td>
								<td><b>{{note.label|safe}}</b></td>
								{% if note|getBalance:0 >= 0 %}
									{% if note.credit == note.debit %}<td align="right" data-value='0' data-type="Number">0</td>{%else%}
										<td align="right" data-value='{{note|getBalance:0|floatformat:0}}' data-type="Number">{%if note.display_debit_positive %}({{note|getBalance:0|floatformat:0}}){%else%}{{note|getBalance:0|floatformat:0}}{%endif%}</td>
									{% endif %}
								{% else %}
								{% if note|getBalance:0 != "" %}
									<td align="right" data-value='{{note|getBalance:0|floatformat:0}}' data-type="Number">{%if note.display_debit_positive %}{{note|getBalance:0|multiply:-1|floatformat:0}}{%else%}({{note|getBalance:0|multiply:-1|floatformat:0}}){%endif%}</td>
								{%endif%}{%endif%}
							</tr>
							{% for child in note.children %}
								<tr >
									<td></td>
									<td style="padding-left: 15px;">{% if child.children|length > 0 %}<a class="toggle_view" onclick="javascript:$('.{{child.label|toDOMId}}' ).toggle();">{%endif%}      ({{forloop.counter}}) {{child.label|safe}}{% if child.children|length > 0 %}</a>{%endif%}</td>
									{% if child|getBalance:0 >= 0 %}
										{% if child.credit == child.debit %}<td align="right" data-value='0' data-type="Number">0</td>{%else%}
											<td align="right" data-value='{{child|getBalance:0|floatformat:0}}' data-type="Number">{%if child.display_debit_positive %}({{child|getBalance:0|floatformat:0}}){%else%}{{child|getBalance:0|floatformat:0}}{%endif%}</td>
										{% endif %}
									{% else %}
										<td align="right" data-value='{{child|getBalance:0|floatformat:0}}' data-type="Number">{%if child.display_debit_positive %}{{child|getBalance:0|multiply:-1|floatformat:0}}{%else%}({{child|getBalance:0|multiply:-1|floatformat:0}}){%endif%}</td>
									{%endif%}
								</tr>
								{% for gChild in child.children %}
									<tr style="display:none" class="{{child.label|toDOMId}}">
										<td></td>
										<td style="padding-left: 45px;">            {{forloop.counter}}. {{gChild.label|safe}}</td>
										{% if gChild|getBalance:0 >= 0 %}
											{% if gChild.credit == gChild.debit %}<td align="right" data-value='0' data-type="Number">0</td>{%else%}
												<td align="right" data-value='{{gChild|getBalance:0|floatformat:0}}' data-type="Number">{%if gChild.display_debit_positive %}({{gChild|getBalance:0|floatformat:0}}){%else%}{{gChild|getBalance:0|floatformat:0}}{%endif%}</td>
											{% endif %}
										{% else %}
											<td align="right" data-value='{{gChild|getBalance:0|floatformat:0}}' data-type="Number">{%if gChild.display_debit_positive %}{{gChild|getBalance:0|multiply:-1|floatformat:0}}{%else%}({{gChild|getBalance:0|multiply:-1|floatformat:0}}){%endif%}</td>
										{%endif%}
									</tr>
								{% endfor %}
							{% endfor %}
						{% endfor %}
						</tbody>
					</table>
				</div>
				{% endif %}
			</div>
			{% endif %}
		</div>
	</div>
</div>
<script type="text/javascript">
	$(document).ready(function(){
		TableHeaderFixed();
		StatementTypeSelection();
		loadProjects();

		if($("#id_message_box").val()!="" && $("#id_message_box").val()!="None"){
			swal({
			  text: $("#id_message_box").val(),
			  title: "",
			  type: "success"
			});
		}
		$(".toggle-handle").append("<circle></circle>");
		$("#closed_books").removeClass("hide");
	});

	function loadProjects(){
	    $.ajax({
	        url: "/erp/accounts/json/load_projects/",
	        type: "post",
	        datatype: "json",
	        data: {},
	        success: function(response) {
	            $("#project").html("")
				for (i = 0; i < response.length; i++) {
			        $("#project").append("<option value="+response[i][0]+"> "+response[i][1] +" </option>");
				}
				$('#project').multiselect({
					includeSelectAllOption: true,
					selectAllValue: 0,
					onChange: function(){
					    updateFilterOverlay();
					}
				});
				if ($('#default_projects').val() != "" && $('#default_projects').val() != "None") {
					var projectList = $('#default_projects').val().split(",");
					$.each(projectList,function(i){
						$('#project').multiselect('select', projectList[i], true);
					});
				}
				else {
				    $('#project').multiselect('select', '0', true);
				}
				$(".multiselect-all").change(function(){
                    updateFilterOverlay();
                });
				updateFilterText();
	        }
	    });
	}

	function loadSelectedProjects() {
	    var prevItem = $("#project_details").val();
	    if($("#project_details").val() != "") {
	        prevItemList = prevItem.split(",");
	        for (i = 0; i < prevItemList.length; i++) {
	            $("#project").multiselect('select', prevItemList[i], true);
	        }
	    }
	    var selected_projects="";
	    var multi_select_project =  $("select#project").next('.btn-group').find('ul').find('li input:checked');
	    multi_select_project.each(function () {
	        selected_projects += $(this).val() + ",";
	    });
	    document.getElementById("project_details").value = selected_projects;
	}
	
	function StatementTypeSelection(){
		$("#statement_type").on("change", function(){
			if($(this).is(":checked")) {
		    	$('.form-actual').addClass('hide');
		    	$("#a_generate").removeClass('arrow_front');
		    	$(".statement_type").val("true");
			}
			else {
		    	$('.form-actual').removeClass('hide');
		    	$("#a_generate").addClass('arrow_front');
		    	$(".statement_type").val("false");
			}
		});
		$("#a_trial_balance, #run_trial_balance").click(function(){
			loadSelectedProjects();
			$("#id_run_trial_balance").click();
		});

		$("#a_generate").click(function(){
			if(!$(this).hasClass('disabled')) {
				loadSelectedProjects();
				document.getElementById("statement_project_details").value = $("#project_details").val();
				$("#div_generate_statements").click();
			}
		});

		$("#a_finalize").click(function(){
			if(!$(this).hasClass('disabled')) {
				if($("#id_next_opening").val() != $("#id_statements_opening_on").val()){
					swal({
						title: "Book cannot be Closed!",
						type: "error",
						text: "Statement Period not contiguous with previous<br/><b>Closure date - <i>{{previous_closure_date|date:'M d, Y'}}</i></b>" +
						    " !<br/><br/>Kindly re-run the Statements with<br/><b>Opening date - <i>{{next_opening_date|date:'M d, Y'}}</i></b> !"
					});
				}
				else if(parseFloat($("#id_assets_total").text()) == parseFloat($("#id_liabilities_total").text())){
					$("#id_close_book").click();
                    ga('send', 'event', 'Accounts', 'Close Book', $('#enterprise_label').val(), 50);
				} else {
					swal({
						title: "Book cannot be Closed!",
						type: "error",
						text: "Assets (" + $('#id_assets_total').text() + ") and Liabilities (" + $('#id_liabilities_total').text() +
						    ") are NOT properly Balanced.<br/><br/>Kindly try again after tallying the Balance Sheet by including all missing Transactions (Vouchers)."
					});
				}
			}
		});
	}

	setTimeout(function(){
		$('#statement_period').on('hide.daterangepicker', function(ev, picker) {
			if(!$("#a_generate").hasClass('disabled') && ($("#id_opening_on").val() != $("#tb_opening_on").val() ||
				$("#id_closing_on").val() != $("#tb_closing_on").val())){
				$("#id_generate_statements").hide();
				$("#div_generate_statements").hide();
				$("#a_generate, #a_finalize").addClass('disabled');
				swal({
					title: "Run Trial Balance again!",
					type: "warning",
					text: "Trial Balance was generated for period between <br/><i>{{opening_on|date:'M d, Y'}} and {{closing_on|date:'M d, Y'}}</i><br/><br/><b>Kindly run Trial Balance again</b><br/> to the generate BS, P&L statements for the chosen period!"
				}, function(){
				    setTimeout(function(){
				        $(".btn-filter").trigger("click");
                    },100);
				});
			} else {
				$("#id_generate_statements").show();
				$("#div_generate_statements").show();
				{% if trial_balance|length > 0 %}$("#a_generate").removeClass('disabled');{% endif %}
				{% if notes|length > 0 %}$("#a_finalize").removeClass('disabled');{% endif %}
			}
			$(".closing_date").val($("#id_closing_on").val());
			$(".opening_date").val($("#id_opening_on").val());
            updateFilterOverlay();
		});
	}, 100);

	$("#id_run_trial_balance, #id_generate_statements").click(function(){
		$('#loading').show();
	});
	
	$("#id_generate_statements").click(function(){
		$('#loading').show();
        ga('send', 'event', 'Accounts', 'Generate Statements', $('#enterprise_label').val(), 1);
	});
	
	$("#generate_statement").click(function(){
		$("#id_generate_statements").click();
	});
	
	$("#div_generate_statements").click(function(){
		$("#modalInclusions").modal('show');
	});

	$("#id_fg_closing, #id_wip_closing").blur(function(){
		$("#id_goods_closing, #id_goods_closing_display").val(parseFloat($("#id_wip_closing").val()) + parseFloat($("#id_fg_closing").val()));
	});
	
	$("#id_fg_opening, #id_wip_opening").blur(function(){
		$("#id_goods_opening, #id_goods_opening_display").val(parseFloat($("#id_wip_opening").val()) + parseFloat($("#id_fg_opening").val()));
	});
	
	var oTable;
	var oSettings;
	function TableHeaderFixed(){
		var netTd = $("#table_trial_balance tbody tr:first-child").html();
		$("#table_trial_balance tbody tr:first-child").remove();
		$("#table_trial_balance thead").append("<tr class='netAmt_tr'>"+netTd+"</tr>");
		oTable = $('#table_trial_balance').DataTable({
			fixedHeader: true,
			"pageLength": 50,
			"search": {
				"smart": false
			},
			"bSortCellsTop": true
		});
		oTable.on("draw",function() {
			var keyword = $('#table_trial_balance_filter > label:eq(0) > input').val();
			$('#table_trial_balance').unmark();
			$('#table_trial_balance').mark(keyword,{});
		});
		oTable.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
		oSettings = oTable.settings();
	}

	function TableHeaderFixed_2(){
		oTable = $('#table_trial_balance').DataTable({
			fixedHeader: true,
			"pageLength": 50,
			"search": {
				"smart": false
			},
			"bSortCellsTop": true
		});
		oTable.on("draw",function() {
			var keyword = $('#table_trial_balance_filter > label:eq(0) > input').val();
			$('#table_trial_balance').unmark();
			$('#table_trial_balance').mark(keyword,{});
		});
		oTable.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
		oSettings = oTable.settings();
	}

	$('ul.nav-tabs li a').click(function() {
		if($(this).attr('id') != "tab1_view") {
			if($("#table_trial_balance").hasClass('dataTable')) {
				oTable.destroy();
				oTable  = undefined;
			}
		}
		else {
			TableHeaderFixed_2();
		}
	});
	
	$('.nav-pills li').removeClass('active');
	$('#li_statements').addClass('active');
</script>

<script>
	function tablesToExcelLoad() {
		$("#tab2_view").click();
		$(".downloading-main-container").removeClass('hide').addClass('show');
		setTimeout(function(){
			tablesToExcel(['table_trial_balance','asset_and_liability','provision_PL_statement','report_notes'], ['Trial_Balance','Balance_sheet','Profit_and_Loss','Notes'], 'Provision_Statement_{{opening_on|date:'M d, Y'|toDOMId}}-{{closing_on|date:'M d, Y'|toDOMId}}.xls', 'Excel');
		},100);
	}

	  var tablesToExcel = (function() {
	    var uri = 'data:application/vnd.ms-excel;base64,'
	    , tmplWorkbookXML = '<?xml version="1.0"?><?mso-application progid="Excel.Sheet"?><Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet">'
	      + '<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office"><Author>Axel Richter</Author><Created>{created}</Created></DocumentProperties>'
	      + '<Styles>'
	      + '<Style ss:ID="Currency"><NumberFormat ss:Format="Currency"></NumberFormat></Style>'
	      + '<Style ss:ID="Date"><NumberFormat ss:Format="Medium Date"></NumberFormat></Style>'
	      + '</Styles>'
	      + '{worksheets}</Workbook>'
	    , tmplWorksheetXML = '<Worksheet ss:Name="{nameWS}"><Table>{rows}</Table></Worksheet>'
	    , tmplCellXML = '<Cell{attributeStyleID}{attributeFormula}><Data ss:Type="{nameType}">{data}</Data></Cell>'
	    , base64 = function(s) { return window.btoa(unescape(encodeURIComponent(s))) }
	    , format = function(s, c) { return s.replace(/{(\w+)}/g, function(m, p) { return c[p]; }) }
	    return function(tables, wsnames, wbname, appname) {
	      var ctx = "";
	      var workbookXML = "";
	      var worksheetsXML = "";
	      var rowsXML = "";
	       if($('ul.nav-tabs li.active a').attr('id') == 'tab1_view') {
				oSettings[0]._iDisplayLength = oSettings[0].fnRecordsTotal();
		        oTable.draw();
		    }
	      for (var i = 0; i < tables.length; i++) {
	        if (!tables[i].nodeType) tables[i] = document.getElementById(tables[i]);
	        for (var j = 0; j < tables[i].rows.length; j++) {
	          rowsXML += '<Row>'
	          for (var k = 0; k < tables[i].rows[j].cells.length; k++) {
	            var dataType = tables[i].rows[j].cells[k].getAttribute("data-type");
	            var dataStyle = tables[i].rows[j].cells[k].getAttribute("data-style");
	            var dataValue = tables[i].rows[j].cells[k].getAttribute("data-value");
	            dataValue = (dataValue)?dataValue:tables[i].rows[j].cells[k].innerHTML;
	            var dataFormula = tables[i].rows[j].cells[k].getAttribute("data-formula");
	            dataFormula = (dataFormula)?dataFormula:(appname=='Calc' && dataType=='DateTime')?dataValue:null;
	            ctx = {  attributeStyleID: (dataStyle=='Currency' || dataStyle=='Date')?' ss:StyleID="'+dataStyle+'"':''
	                   , nameType: (dataType=='Number' || dataType=='DateTime' || dataType=='Boolean' || dataType=='Error')?dataType:'String'
	                   , data: (dataFormula)?'':dataValue
	                   , attributeFormula: (dataFormula)?' ss:Formula="'+dataFormula+'"':''
	                  };
	            rowsXML += format(tmplCellXML, ctx);
	          }
	          rowsXML += '</Row>'
	        }
	        ctx = {rows: rowsXML, nameWS: wsnames[i] || 'Sheet' + i};
	        worksheetsXML += format(tmplWorksheetXML, ctx);
	        rowsXML = "";
	      }

	      ctx = {created: (new Date()).getTime(), worksheets: worksheetsXML};
	      workbookXML = format(tmplWorkbookXML, ctx);

	      	if($('ul.nav-tabs li.active a').attr('id') == 'tab1_view') {
	      		$("#pagination-select").trigger('change');
	    	}

	      var link = document.createElement("A");
	      link.href = uri + base64(workbookXML);
	      link.download = wbname || 'Workbook.xls';
	      link.target = '_blank';
	      document.body.appendChild(link);
	      link.click();
	      document.body.removeChild(link);
	      $(".downloading-main-container").removeClass('show').addClass('hide');
	    }
	  })();


function updateFilterOverlay() {
    var statementType = "provisional";
	var firstTransactionDate = $("#id_next_opening").val();
    var prevClosureDate = $("#id_previous_closure").val();
    if(firstTransactionDate == prevClosureDate) {
        var currentFromDate = moment($("#id_opening_on").val()).format("YYYY-MM-DD");
    }
    else {
        var currentFromDate = moment($("#id_opening_on").val()).subtract("days",1).format("YYYY-MM-DD");
    }
    if($("#project option").length == $("#project option:selected").length) {
        if(prevClosureDate == currentFromDate) {
            statementType = "actual";
        }
        else {
            statementType = "provisional";
        }
    }
    $(".statement_type_txt").text(statementType.toUpperCase());
}
function updateFilterText() {
	$(".filtered-date b").text($("#statement_period").find("span").text());
	$(".filtered-project b").text($("#project").next("div").find(".multiselect-selected-text").text());
    var statementType = "provisional";
	var firstTransactionDate = $("#id_next_opening").val();
    var prevClosureDate = $("#id_previous_closure").val();
    if(firstTransactionDate == prevClosureDate) {
        var currentFromDate = moment($("#id_opening_on").val()).format("YYYY-MM-DD");
    }
    else {
        var currentFromDate = moment($("#id_opening_on").val()).subtract("days",1).format("YYYY-MM-DD");
    }

    if($("#project option").length == $("#project option:selected").length) {
        if(prevClosureDate == currentFromDate) {
            statementType = "actual";
        }
        else {
            statementType = "provisional";
        }
    }

    if(statementType == "provisional") {
        $("#statement_type").prop("checked", true).trigger("change");
    }
    else {
        $("#statement_type").prop("checked", false).trigger("change");
    }
    $(".filtered-type b, .statement_type_txt").text(statementType.toUpperCase());
	$(".filtered-condition").click(function(){
	    setTimeout(function(){
            $("#project").next("div").find(".dropdown-menu").removeClass("show");
        },100);
    });
}
</script>
{% endblock %}