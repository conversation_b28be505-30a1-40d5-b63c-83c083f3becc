<!DOCTYPE html>
<style type="text/css">
	body{
		color: #000;
		font-family: 'Times New Roman';
	}
	.rejection_table{
		text-align:center;
		font-weight:bold;
	}
	@font-face {
		font-family: 'Times New Roman';
		font-style: normal;
		font-weight: 400;
		src: url(https://www.xserp.in/site_media/fonts/times.ttf) format('truetype');
		}
</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/bootstrap.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">

<body>
<div class="container">
	<div class="rejection_table" style="font-size:11pt;">REJECTION REPORT for <i>{{ source.getCode }}</i></div>
	<div class="col-sm-12" style="margin-top:20px;">
		<table border=1 bordercolor="#A9A9A9" class="row-seperator column-seperator" style="width:100%;">
			<tr>
				<th rowspan="2" style="width:5%">S.No</th>
				<th rowspan="2">Reason</th>
				<th rowspan="2" style="width:15%">Quantity</th>
				<th rowspan="2" style="width:15%">Debit</th>
			</tr>
			<tbody>
				{% for item in source.items %}
					{% if item.rejections|length > 0 %}
						<tr>
							<td colspan="4" class="rejection_table"><b>{{ item.material.name }}</b> {% if item.material.drawing_no %} ({{ item.material.drawing_no }}) {% endif %}
<!--								<i>{% if item.make and item.make.label != "-NA-" %} {{ item.make }} {% endif %} {% if item.is_faulty == 1 %} [Faulty] {% endif %}</i></td>-->
							<i>{% if item.is_faulty == 1 %} [Faulty] {% endif %}</i></td>
						</tr>
						{% for rejection in item.rejections %}
							<tr>
								<td>{{ forloop.counter }}</td>
								<td>{{ rejection.reason }}</td>
								<td>{{ rejection.quantity  }} ({{ item.material.unit }})</td>
								<td>{{ rejection.debit  }}</td>
							</tr>
						{% endfor %}
					{% endif %}
				{% endfor %}
				{% if source.rejection_remarks and source.rejection_remarks|length > 0 %}
					<tr>
						<td colspan="4" style="word-break: break-all"><b>Rejection Remarks:</b> {{ source.rejection_remarks }}</td>
					</tr>
				{% endif %}
			</tbody>
		</table>
	</div>
</div>
<br>
</body>