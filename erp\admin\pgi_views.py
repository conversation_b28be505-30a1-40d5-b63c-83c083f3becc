"""
"""
import datetime
import hashlib
import json

import jwt
from django.core.context_processors import csrf
from django.http.response import HttpResponse, HttpResponseRedirect
from django.shortcuts import render_to_response
from django.template import RequestContext
from django.template.response import TemplateResponse
from django.views.decorators.csrf import csrf_exempt

from erp.admin import logger
from erp.admin.pgi_backend import SubscriptionService
from erp.auth import ENTERPRISE_ID_SESSION_KEY, USER_IN_SESSION_KEY, ENTERPRISE_SUBSCRIPTION, SESSION_KEY
from erp.auth.backend import LoginService
from erp.auth.request_handler import RequestHandler
from erp.helper import getUser, getEnterprise
from settings import BILLING_ENTERPRISE_ID, BASE_PAYMENT_URL, JWT_SECRET
from util.api_util import response_code
from util.helper import getAbsolutePath
import base64

__author__ = 'nandha'


@csrf_exempt
def pgiResponseHandler(request):
	"""

	Terms and conditions
	:param request:
	:return:
	"""
	c = {
		'transaction_id': "None", 'invoice_no': "None",
		'datetime': datetime.datetime.today().strftime("%b %d, %Y %H:%M:%S")}
	try:
		subscription_service = SubscriptionService()
		pgi_merchant_info = subscription_service.getPgiMerchantInfo(enterprise_id=BILLING_ENTERPRISE_ID)
		rh = RequestHandler(request)
		payment_info = request.POST.dict()
		status = rh.getPostData("status")
		first_name = rh.getPostData("firstname")
		amount = rh.getPostData("amount")
		txn_id = rh.getPostData("txnid")
		key = rh.getPostData("key")
		product_info = rh.getPostData("productinfo")
		email = rh.getPostData("email")
		drawing_no = rh.getPostData("udf1")
		posted_hash = rh.getPostData("hash")
		token = rh.getData('token')
		decoded_token = jwt.decode(token, JWT_SECRET)
		enterprise_id = decoded_token['enterprise_id']
		user_id = decoded_token['user_id']
		logger.warn("Received User Subscription payment for enterprise %s" % [enterprise_id, payment_info])
		logger.warn("Processing payment gateway response with transaction id %s for %s" % (txn_id, dict(
			enterprise_id=enterprise_id, user_id=user_id, amount=amount, status=status)))
		try:
			additional_charges = rh.getPostData("additionalCharges")
			ret_hash_seq = \
				additional_charges + '|' + pgi_merchant_info.merchant_salt + '|'+status+'||||||||||' + drawing_no + '|' \
				+ email+'|'+first_name+'|'+product_info+'|'+amount+'|'+txn_id+'|'+key
		except Exception as e:
			ret_hash_seq = \
				pgi_merchant_info.merchant_salt + '|'+status+'||||||||||' + drawing_no + '|'+email+'|' + first_name+'|' +\
				product_info+'|'+amount+'|'+txn_id+'|'+key
			logger.warn(e.message)
		hashh = hashlib.sha512(ret_hash_seq).hexdigest().lower()

		# Processing response from payment gateway
		c["csrf_token"] =str(csrf(request)[u'csrf_token'])
		c["phone"] = rh.getPostData("phone")
		c["payment_mode"] = rh.getPostData("mode")
		c["transaction_id"] = txn_id
		amount = float(amount) if amount else 0
		c["amount"] = amount
		user = getUser(enterprise_id=enterprise_id, user_id=user_id)
		if hashh != posted_hash or rh.getPostData("status") != 'success':
			subscription_service.processPaymentFailureEvent(
				enterprise_id=enterprise_id, user_id=user_id, amount=amount, transaction_id=c['transaction_id'],
				payment_info=payment_info, drawing_no=drawing_no)
			c['title'] = rh.getPostData("error_Message")
			c['message'] = "Unfortunately your transaction was failed due to some technical issues."
			c['event'] = "Failure"
			encoded_jwt = jwt.encode(c, JWT_SECRET)
			return HttpResponseRedirect('redirectResult?data={}'.format(encoded_jwt))
		else:
			logger.warn("%s has paid successfully for %s" % (user.email, [enterprise_id, amount]))
			# Fetching material for the amount paid
			invoice = subscription_service.processPaymentSuccessEvent(
				enterprise_id=enterprise_id, user_id=user_id, amount=amount, transaction_id=c['transaction_id'],
				payment_info=payment_info, drawing_no=drawing_no)
			request.session[ENTERPRISE_SUBSCRIPTION] = LoginService().prepareEnterpriseSubscriptionInfo(
				enterprise_id=enterprise_id)
			if invoice:
				c['message'] = "Invoice reference number: %s" % invoice.getCode()
			else:
				c['message'] = """We have received payment but we could not generate invoice at this moment. 
				Please contact support for the invoice!"""

			c['message'] = "Thank You. Your order status is %s" % status
			c['invoice_code'] = invoice.getCode()
			c['paid_item'] = invoice.items[0].item.drawing_no
			logger.info("Thank You. Your order status is %s" % status)
			logger.info("Your Transaction ID for this transaction is %s" % txn_id)
			logger.info("We have received a payment of Rs. %s. Your order will soon be shipped." % amount)
			c['event'] = "Success"
			encoded_jwt = jwt.encode(c, JWT_SECRET)
			return HttpResponseRedirect('redirectResult?data={}'.format(encoded_jwt))
	except Exception as e:
		logger.exception("PayU Payment handling error: %s" % e.message)
		c['message'] = "%s" % e.message
		c['title'] = "Unfortunately your transaction was failed due to some technical issues."
		c['event'] = "Failure"
		encoded_jwt = jwt.encode(c, JWT_SECRET)
		return HttpResponseRedirect('redirectResult?data={}'.format(encoded_jwt))


def pgiResultHandler(request):
	"""

	:param request:
	:return:
	"""
	encoded_jwt = request.GET.get("data")
	encoded_jwt = encoded_jwt.replace("u'", "").replace("'", "")
	decoded_jwt = jwt.decode(encoded_jwt, JWT_SECRET)
	if decoded_jwt['event'] == "Failure":
		return TemplateResponse(request=request, template='pgi/failure.html', context=decoded_jwt)
	elif decoded_jwt['event'] == "Success":
		return TemplateResponse(request=request, template='pgi/success.html', context=decoded_jwt)

def pgiUpdateTransaction(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		user_id = rh.getPostData('user_id')
		enterprise_id = rh.getPostData('enterprise_id')
		amount = float(rh.getPostData('amount'))
		transaction_id = rh.getPostData('transaction_id')
		payment_info = json.loads(rh.getPostData('payment_info'))
		status = rh.getPostData('status')
		if status == 'success':
			invoice = SubscriptionService().processPaymentSuccessEvent(
				enterprise_id=enterprise_id, user_id=user_id, amount=amount, transaction_id=transaction_id,
				payment_info=payment_info)
			response = response_code.success()
			response['transaction_invoice_number'] = invoice.getCode()
		else:
			SubscriptionService().processPaymentFailureEvent(
				enterprise_id=enterprise_id, user_id=user_id, amount=amount, transaction_id=transaction_id,
				payment_info=payment_info)
			response = response_code.failure()
	except Exception as e:
		logger.exception("PayU Payment handling error: %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def pgiGenerateHash(request):
	"""

	Terms and conditions
	:param request:
	:return:
	"""
	try:

		rh = RequestHandler(request)
		first_name = rh.getPostData("firstname")
		amount = rh.getPostData("amount")
		txn_id = rh.getPostData("txnid")
		key = rh.getPostData("key")
		product_info = rh.getPostData("productInfo")
		email = rh.getPostData("email")
		udf1 = rh.getPostData("udf1")
		udf2 = rh.getPostData("udf2")
		udf3 = rh.getPostData("udf3")
		udf4 = rh.getPostData("udf4")
		udf5 = rh.getPostData("udf5")
		pgi_merchant_info = SubscriptionService().getPgiMerchantInfo(enterprise_id=BILLING_ENTERPRISE_ID)
		response = response_code.success()
		ret_hash_seq = key + '|' + txn_id + '|' + amount + '|' + product_info + '|' + first_name + '|' + email + \
		               '|' + udf1 + '|' + udf2 + '|' + udf3 + '|' + udf4 + '|' + udf5 + '||||||' + pgi_merchant_info.merchant_salt
		checksum = hashlib.sha512(ret_hash_seq).hexdigest().lower()
		response["checksum"] = checksum
	except Exception as e:
		logger.exception("Checksum handling error: %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def pgiMerchantDetails(request):
	"""

	Terms and conditions
	:param request:
	:return:
	"""
	try:
		subscription_service = SubscriptionService()
		pgi_merchant_info = subscription_service.getPgiMerchantInfo(enterprise_id=BILLING_ENTERPRISE_ID)
		response = response_code.success()
		for material in subscription_service.getMaterials(enterprise_id=BILLING_ENTERPRISE_ID):
			amount = float(material.price) + float(material.price * 18 / 100)
			response[material.drawing_no] = {
				"drawing_no": material.drawing_no, "price": float(material.price), "amount": amount}
		response["merchant_id"] = pgi_merchant_info.merchant_id
		response["merchant_key"] = pgi_merchant_info.merchant_key
	except Exception as e:
		logger.exception("get merchant key in id error: %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def success(request):
	return TemplateResponse(request=request, template='pgi/success.html', context={"amount": 2500})


def failure(request):
	return TemplateResponse(request=request, template='pgi/failure.html', context={"amount": 2500})


def initiateSubscriptionPayment(request):
	request_handler = RequestHandler(request)
	user = request_handler.getSessionAttribute(USER_IN_SESSION_KEY)
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	subscription_service = SubscriptionService()
	enterprise = getEnterprise(enterprise_id=enterprise_id)
	pgi_merchant_info = subscription_service.getPgiMerchantInfo(enterprise_id=BILLING_ENTERPRISE_ID)
	logger.warn("Initiated User Subscription payment for enterprise %s" % enterprise_id)
	posted = {}
	for i in request.POST:
		posted[i] = request.POST[i]
	txnid = str(enterprise_id) + "-" + str(datetime.datetime.now().strftime('%Y%m%d%H%M%S%f'))
	posted['txnid'] = txnid
	posted['key'] = pgi_merchant_info.merchant_key
	posted['name'] = '%s [%s]' % (enterprise.name, enterprise_id)
	posted['firstname'] = '%s [%s]' % (enterprise.name, enterprise_id)
	posted['lastname'] = '%s %s' % (user.first_name, user.last_name)

	material = subscription_service.getMaterial(enterprise_id=BILLING_ENTERPRISE_ID, drawing_no=posted['pay_type'])
	from dateutil.relativedelta import relativedelta
	expired_on = datetime.datetime.now() + relativedelta(minutes=10)
	token = request_handler.generateJwtToken(user_id=user_id, enterprise_id=enterprise_id, expired_on=expired_on)
	posted['product_info'] = "XSERP"
	posted['amount'] = float(material.price) + float(material.price * 18 / 100)
	posted['email'] = user.email
	if request.is_secure():
		protocol = "https://"
	else:
		protocol = "http://"
	current_url = request.META.get('HTTP_HOST')
	success_url = protocol + current_url + "/erp/admin/pay/success/?token=%s" % token
	failure_url = protocol + current_url + "/erp/admin/pay/failure/?token=%s" % token
	posted['success_url'] = success_url
	posted['failure_url'] = failure_url
	posted['udf1'] = posted['pay_type']
	hash_sequence = "key|txnid|amount|product_info|firstname|email|udf1|udf2|udf3|udf4|udf5|udf6|udf7|udf8|udf9|udf10"
	hash_vars_seq = hash_sequence.split('|')
	hash_string = ''
	for i in hash_vars_seq:
		try:
			hash_string += str(posted[i])
		except Exception:
			hash_string += ''
		hash_string += '|'
	hash_string += pgi_merchant_info.merchant_salt
	hashh = hashlib.sha512(hash_string).hexdigest().lower()
	if posted.get("key") is not None and posted.get("txnid") is not None and posted.get(
			"amount") is not None:
		return render_to_response(getAbsolutePath('/templates/pgi/payment_details.html'), RequestContext(
				request, {
					"posted": posted, "hashh": hashh, "MERCHANT_KEY": pgi_merchant_info.merchant_key, "txnid": txnid,
					"hash_string": hash_string, "action": BASE_PAYMENT_URL,
					"csrf_token": posted['csrfmiddlewaretoken']}))
	else:
		return render_to_response(getAbsolutePath('/templates/pgi/payment_details.html'), RequestContext(
				request, {
					"posted": posted, "hashh": hashh, "MERCHANT_KEY": pgi_merchant_info.merchant_key, "txnid": txnid,
					"hash_string": hash_string, "action": ".", "csrf_token": posted['csrfmiddlewaretoken']}))


def pgiInvoiceCreation(request):
	try:
		rh = RequestHandler(request)
		user_id = rh.getPostData('user_id')
		drawing_no = rh.getPostData('drawing_no')
		amount = float(rh.getPostData('amount'))
		remarks = rh.getPostData('remarks')
		party_details = json.loads(rh.getPostData('party_details'))
		status = rh.getPostData('status')
		subscription_service = SubscriptionService()
		logger.info("party_details:%s" % party_details)
		discount = rh.getPostData('discount')

		if status == 'success':
			logger.info("Invoice Will be Create")
			invoice_dict = subscription_service.party_invoice_creation(
				party_details=party_details, user_id=user_id, amount=amount,
				drawing_no=drawing_no, remarks=remarks, discount=discount)
			logger.info("Step 3:%s" % invoice_dict['invoice_id'])
			invoice_id = invoice_dict['invoice_id']
			invoice_code = invoice_dict['invoice_code']
			prepared_on = invoice_dict['prepared_on']
			document_pdf = invoice_dict['document_pdf']
			document_pdf_base64 = base64.b64encode(document_pdf).decode('utf-8')
			logger.info("Data:%s" % [invoice_id, invoice_code, prepared_on])
			response = {
				'invoice_id': invoice_id,
				'invoice_code': invoice_code,
				'prepared_on': prepared_on,
				'document_pdf': document_pdf_base64}
		else:
			response = {'status': 'failure'}
			logger.info("response: %s" % response)

	except Exception as e:
		response = {'status': 'error', 'error': str(e)}
		logger.error("Exception: %s" % response)
	return HttpResponse(json.dumps(response), content_type='application/json')
