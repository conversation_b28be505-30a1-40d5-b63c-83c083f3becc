/*...............Smart...................... */
body {
	background:#ececec;
	margin:0px;
	padding:0px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	color:#525151;
}
/*............TOP SECTION.............*/
#top-bg {
	background:#525151;
	width:100%;
	height:47px;
	float:left;
	position:fixed;
	z-index:1000;
}
#login {
	width:100%;
	height:47px;
	float:center;
	position:fixed;
	z-index:1000;
}
#login-top {
	width:1064px;
	margin:0px auto;
}
#login h1 {
	font-family: 'open_sansregular';
	font-size:28px;
	color:#51afc4;
	font-weight:normal;
	line-height:20px;
}
.top-bg {
	width:1064px;
	margin:0px auto;
}
.top-bg1 {
	width:1004px;
	margin:0px auto;
	margin-top:40px;
}
.erp-logo {
	float:left;
	display:block;
	margin:18px 0px 0px 10px;
}
.schnell-logo {
	float:right;
	display:block;
	margin:18px 10px 0px 0px;
}
.schnell-logo1 {
	float:right;
	display:block;
	margin:-40px -150px 0px 0px;
}
.limiter {
	display:block;
	width:477px;
	height:332px;
	float:left;
	margin-left:-40px;
}
/*............CONTENT SECTION.............*/
#lft-cont {
	width:478px;
	float:left;
	margin:90px 0px 0px 0px;
}
#rgt-cont {
	width:410px;
	float:left;
	margin:80px 0px 0px 115px;
}
.blue-bg {
	background:#51afc4;
	width:477px;
	float:left;
	margin-top:10px;
}
.blue-bg h1 {
	font-family: Arial, Helvetica, sans-serif;
	font-size:28px;
	color:#ffffff;
	font-weight:normal;
	line-height:20px;
	margin:25px 0px 0px 27px;
}
.blue-bg p {
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	color:#ffffff;
	font-weight:normal;
	line-height:20px;
	padding:0px 27px 0px 27px;
}
a.click-btn {
	background:#FFFFFF;
	width:87px;
	height:25px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	font-weight:bold;
	color:#0aaed3;
	text-decoration:none;
	border:none;
	padding:5px 10px;
}
a.click-btn:hover {
	color:#000000;
	text-decoration:none;
}
#rgt-cont h1 {
	font-family: Arial, Helvetica, sans-serif;
	font-size:28px;
	color:#51afc4;
	font-weight:normal;
	line-height:20px;
}
.txtbox {
	border:1px #c1c0c0 solid;
	background:#ececec;
	width:357px;
	height:34px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:12px;
	color:#6c6c6c;
	line-height:34px;
	padding-left:10px;
}
.txtbox1 {
	border:1px #c1c0c0 solid;
	background:#ececec;
	width:370px;
	height:34px;
	font-family: 'Open Sans', sans-serif;
	font-size:12px;
	line-height:34px;
	color:#6c6c6c;
	padding-left:10px;
}
.submit-btn {
	background:#51afc4;
	width:79px;
	height:28px;
	font-family: 'Open Sans', sans-serif;
	font-size:13px;
	color:#ffffff;
	font-weight:bold;
	border:none;
	cursor:pointer;
}
.submit-btn:hover {
	background:#595c5d;
	text-decoration:none;
}
.phone-icon {
	margin:57px 0px 0px 0px;
	float:left;
}
.ver-line {
	border-right:1px #c0c0c0 solid;
	height:67px;
	float:left;
	margin:57px 0px 0px 12px;
}
.phone-txt {
	font-family: Arial, Helvetica, sans-serif;
	font-size:16px;
	color:#6e6e6e;
	float:left;
	margin:62px 0px 0px 12px;
}
.phone-txt span {
	font-family:Arial, Helvetica, sans-serif;
	font-size:30px;
	color:#5e5e5e;
}
/*............FOOTER SECTION.............*/
.copy {
	font-family: Arial, Helvetica, sans-serif;
	font-size:11px;
	color:#525151;
	text-align:center;
	padding-top:53px;
	padding-bottom:20px;
}
/*............INNER PAGES SECTION.............*/
a {
    text-decoration: none !important;
    color: #357fad;
    cursor:pointer
}
.menu ul {
	list-style:none;
	margin:0px 0px 0px 40px;
	float:left;
	padding:0px;
}
.menu li {
	list-style:none;
	font-family: 'Open Sans', sans-serif;
	font-size:12px;
	color:#ffffff;
	line-height:43px;
	float:left;
}
.menu li a {
	font-family: Arial, Helvetica, sans-serif;
	font-size:12px;
	color:#ffffff;
	text-decoration:none;
	padding:0px 20px 0px 20px;
	text-transform:uppercase;
	margin-right:1px;
}
.menu ul li a:hover {
	border-bottom:4px #51afc4 solid;
	color:#51afc4;
	display:block;
}
li.menuact a {
	border-bottom:4px #51afc4 solid;
	color:#51afc4;
	display:block;
}
li.menuact a:hover {
	border-bottom:4px #51afc4 solid;
	color:#51afc4;
	display:block;
}
a.sign-out {
	font-family: Arial, Helvetica, sans-serif;
	font-size:12px;
	color:#ffffff;
	line-height:47px;
	text-transform:uppercase;
	text-decoration:none;
	float:left;
	padding-left:90px;
	float:right;
	display:block;
	margin:-30px 30px 0px 0px;
}
a.sign-out:hover {
	color:#51afc4;
	text-decoration:none;
}
#innerlft-cont {
	width:677px;
	height:341px;
	float:left;
	margin:90px 0px 0px 0px;
}
#innerrgt-cont {
	width:273px;
	float:left;
	margin:80px 0px 0px 50px;
}
.map {
	background:#e3e4e4;
	width:677px;
	height:341px;
	float:left;
}
#innerrgt-cont h1 {
	font-family: Arial, Helvetica, sans-serif;
	font-size:14px;
	font-weight:600;
	color:#525151;	
	text-align:center;
	text-transform:uppercase;
}
.num-bold {
	font-family: Arial, Helvetica, sans-serif;
	font-size:40px;	
	color:#ffffff;
	line-height:25px;text-align:center; 	
	padding:27px 0px 16px 0px;
}
.num-bold a {			
	color:#ffffff;		
	text-decoration: none !important;
}
.num-bold span {
	font-size:12px;			
}
a.load-map {
	background:#51afc4;
	width:134px;
	height:30px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:12px;
	color:#ffffff; line-height:30px;
	font-weight:600; text-align:center;
	border:none;
	cursor:pointer;
	text-transform:uppercase;
	text-decoration:none;
	margin-top:5px; float:left; 
}
a.load-map:hover {
	background:#337F90;	
	text-decoration:none;
}
a.load-map1 {
	background:#51afc4;
	width:106px;
	height:30px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:12px;
	color:#ffffff; line-height:30px;
	font-weight:600; text-align:center;
	border:none;
	cursor:pointer;
	text-transform:uppercase;
	text-decoration:none;
	margin-top:5px; float:left; 
}
a.load-map1:hover {
	background:#337F90;	
	text-decoration:none;
}
a.update {
	background:#595c5d;
	width:136px;
	height:30px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:12px;
	color:#ffffff; line-height:30px;
	font-weight:600; text-align:center;
	border:none;
	cursor:pointer;
	text-transform:uppercase;
	text-decoration:none;
	margin-top:5px; float:left; 
}
a.update:hover {
	background:#929798;	
	text-decoration:none;
}
a.update1 {
	background:#595c5d;
	width:106px;
	height:32px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:12px;
	color:#ffffff; line-height:30px;
	font-weight:600; text-align:center;
	border:none;
	cursor:pointer;
	text-transform:uppercase;
	text-decoration:none;
	margin-top:5px; float:left; 
}
a.update1:hover {
	background:#929798;	
	text-decoration:none;
}

a.update2 {
	background:#595c5d;
	width:160px;
	height:32px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:12px;
	color:#ffffff; line-height:30px;
	font-weight:600; text-align:center;
	border:none;
	cursor:pointer;
	text-transform:uppercase;
	text-decoration:none;
	margin-top:5px; float:left; 
}
a.update2:hover {
	background:#929798;	
	text-decoration:none;
}
a.set {
	background:#51afc4;
	width:50px;
	height:25px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:12px;
	color:#ffffff; line-height:25px;
	font-weight:600; text-align:center;
	border:none;
	cursor:pointer;
	text-transform:uppercase;
	text-decoration:none;
	margin-top:0px; float:left; 
}
a.set:hover {
	background:#337F90;
	text-decoration:none;
}
a.get {
	background:#595c5d;
	width:50px;
	height:25px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:12px;
	color:#ffffff; line-height:25px;
	font-weight:600; text-align:center;
	border:none;
	cursor:pointer;
	text-transform:uppercase;
	text-decoration:none;
	margin-top:0px; float:left; 
}
a.get:hover {
	background:#929798;
	text-decoration:none;
}

.txtbox_frm {
	border:1px #c1c0c0 solid;
	background:#F5F5F5;
	width:80px;
	height:30px;
	line-height:30px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	color:#6c6c6c;
	padding-left:10px;
}


.txtbox11 {
	border:1px #c1c0c0 solid;
	background:#F5F5F5;
	width:219px;
	height:34px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	line-height:34px;
	color:#6c6c6c;
	padding-left:10px;
}
.txtbox12 {
	border:1px #c1c0c0 solid;
	background:#fff;
	width:300px;
	height:34px;
	line-height:34px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	color:#535457;
	padding-left:10px;
}
.txtbox13 {
	border:1px #c1c0c0 solid;
	background:#ececec;
	width:188px;
	height:34px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	line-height:34px;
	color:#6c6c6c;
	padding-left:10px;
}
.txtbox14 {	
	border:1px #c1c0c0 solid;
	background:#fff;
	width:80px;
	height:35px;
	line-height:25px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	color:#6c6c6c;
	padding-left:10px;
}
.txtbox15 {	
	border:1px #c1c0c0 solid;
	background:#F5F5F5;
	width:80px;
	height:30px;
	line-height:30px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	color:#6c6c6c;
	padding-left:10px;
}
.txtbox15_disabled {
	border:1px #c1c0c0 solid;
	background:#E0E0E0;
	width:80px;
	height:30px;
	line-height:30px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	color:#6c6c6c;
	padding-left:10px;
}
.txtbox16 {	
	border:1px #c1c0c0 solid;
	background:#fff;
	width:145px;
	height:30px;
	line-height:30px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	color:#6c6c6c;
	padding-left:10px;
}
.txtbox17 {
	border:1px #c1c0c0 solid;
	background:#F5F5F5;
	width:210px;
	height:34px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	line-height:34px;
	color:#6c6c6c;
	padding-left:10px;
}
.txtbox18 {
	border:1px #c1c0c0 solid;
	background:#F5F5F5;
	width:255px;
	height:34px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	line-height:34px;
	color:#6c6c6c;
	padding-left:10px;
}
.txtbox19 {	
	border:1px #c1c0c0 solid;
	background:#fff;
	width:35px;
	height:20px;
	line-height:20px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	color:#6c6c6c;
	padding-left:5px;
}
.txtbox20 {	
	border:1px #c1c0c0 solid;
	background:#fff;
	width:190px;
	height:20px;
	line-height:20px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	color:#6c6c6c;
	padding-left:5px;
}
.txtbox21 {	
	border:1px #c1c0c0 solid;
	background:#fff;
	width:115px;
	height:34px;
	line-height:20px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	color:#6c6c6c;
	padding-left:5px;
}
.txtbox22 {	
	border:1px #c1c0c0 solid;
	background:#ececec;
	width:50px;
	height:20px;
	line-height:20px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	color:#6c6c6c;
	padding-left:5px;
}
.textarea {	
	border:1px #c1c0c0 solid;
	background:#fff;
	width:145px;
	height:75px;
	line-height:30px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	color:#6c6c6c;
	padding-left:10px;
}
.po_textarea {
    border:1px #c1c0c0 solid;
	background:#FFFFFF;
	width:300px;
	height:75px;
	line-height:30px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	color:#6c6c6c;
	padding-left:10px;
	resize:none;
}
#lft-dash {
	width:256px;
	float:left;
}
#rgt-dash {
	width:748px;
	float:left;
}
.dashboard-greenbg {
	background:#69b7b2;
	width:256px;
	height:247px;
	float:left;
}
.dashboard-pinkbg {
	background:#904e77;
	width:256px;
	height:247px;
	float:left;
}
.dashboard-yellowbg {
	background:#f19a42;
	width:256px;
	height:121px;
	float:left;
}
.dashboard-darkpinkbg {
	background:#cd6451;
	width:256px;
	height:126px;
	float:left;
}
.dash-txtbold {
	font-family: Arial, Helvetica, sans-serif;
	font-size:72px;
	color:#ffffff;
	font-weight:100;
	text-align:center;
	margin:30px 0px 0px 0px;
}
.dash-txtbold1 {
	font-family: Arial, Helvetica, sans-serif;
	font-size:72px;
	color:#ffffff;
	font-weight:100;
	text-align:center;
	margin:-25px 0px 0px 0px;
}
.dash-txtbold2 {
	font-family: Arial, Helvetica, sans-serif;
	font-size:72px;
	color:#ffffff;
	font-weight:100;
	text-align:center;
	margin:0px 0px 0px 0px;
}
.dash-txt {
	font-family: Arial, Helvetica, sans-serif;
	font-size:14px;
	color:#ffffff;
	text-align:center;
	text-transform:uppercase;
	font-weight:normal;
}
.dash-txt span {
	font-size:18px;
}
.dash-txt1 {
	font-family: Arial, Helvetica, sans-serif;
	font-size:14px;
	color:#ffffff;
	text-align:center;
	text-transform:uppercase;
	font-weight:normal;
}
.dash-txt1 span {
	font-size:16px;
}
.dash-txt2 {
	font-family: Arial, Helvetica, sans-serif;
	font-size:12px;
	color:#ffffff;
	text-align:right;
	text-transform:uppercase;
	font-weight:normal;
	margin:5px 10px 0px 0px;
}
.catPrdtNameDis-txtbox{
        border:1px #c1c0c0 solid;
	background:#F5F5F5;
	width:300px;
	height:34px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	line-height:34px;
	color:#6c6c6c;
	padding-left:10px;
}
.catPrdtName-txtbox {
        border:1px #c1c0c0 solid;
	background:#FFFFFF;
	width:300px;
	height:34px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	line-height:34px;
	color:#6c6c6c;
	padding-left:10px;
}
.catCode-txtbox {
	border:1px #c1c0c0 solid;
	background:#FFFFFF;
	width:150px;
	height:30px;
	line-height:30px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	color:#6c6c6c;
	padding-left:10px;
}
.catCodeDis-txtbox {
	border:1px #c1c0c0 solid;
	background:#F5F5F5;
	width:150px;
	height:30px;
	line-height:30px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	color:#6c6c6c;
	padding-left:10px;
}
.catDesc-txtbox {
        border:1px #c1c0c0 solid;
	background:#FFFFFF;
	width:300px;
	height:75px;
	line-height:30px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	color:#6c6c6c;
	padding-left:10px;
	resize:none;
}
.srchMat-txtbox {
        border:1px #c1c0c0 solid;
	background:#FFFFFF;
	width:300px;
	height:34px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	line-height:34px;
	color:#6c6c6c;
	padding-left:10px;
}
.qty-txtbox {
	border:1px #c1c0c0 solid;
	background:#fff;
	width:80px;
	height:30px;
	line-height:30px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	color:#6c6c6c;
	padding-left:10px;
}

.grn_qty-txtbox {
	border:1px #c1c0c0 solid;
	background:#fff;
	width:40px;
	height:30px;
	line-height:30px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:13px;
	color:#6c6c6c;
	padding-left:10px;
}
/* Error Panel Style Sheet*/

.error_panel {
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        display: none;
        position: absolute;
        background-color: rgba(204, 204, 204, 0.5);
        color: #222222;
}
#error_display{
        color:#FF0000;
        background:#00FFFF;
        font-size:12px;
        font-weight:normal;
        float:center;
        display:show;
        position: absolute;
        /* To align popup window at the center of screen*/
        top: 50%;
        left: 50%;
        margin-top: 100px;
        margin-left: 100px;
}