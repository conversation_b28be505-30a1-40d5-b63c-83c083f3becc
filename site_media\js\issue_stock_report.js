var onPageLoad = function() {
    $("#id-search_isr").click(function () {
        searchIssueStockReport();
    });
}

function TableHeaderFixed() {
    oTable = $('#tbl_issue_stock_report').DataTable({
        fixedHeader: true,
        "pageLength": 50,
        "orderCellsTop": true,
        "search": {
            "smart": false
        }

    });
    oTable.on("draw",function() {
        var keyword = $('#tbl_issue_stock_report_filter > label:eq(0) > input').val();
        $('#tbl_issue_stock_report').unmark();
        $('#tbl_issue_stock_report').mark(keyword,{});
    });
    oTable.on('page.dt', function() {
      $('html, body').animate({
        scrollTop: $(".dataTables_wrapper").offset().top - 15
       }, 'slow');
    });
    oSettings = oTable.settings();
}


function loadAllMaterials(itemId, makeId, isFaulty) {
    var dataToSend = {
        'type': $("#id_invoice-type option:selected").val(),
        'party_id': $("#id_invoice-party_id option:selected").val(),
        'module': 'invoice',
        'particulars':'invoice_materials'
    }
    generateMaterialAsComboBox(itemId, makeId, isFaulty, dataToSend)
    searchIssueStockReport();
}

function searchIssueStockReport() {
    $('#loading').show();

    var material = $('#id-material').val().split("[::]");
    var [itemId, makeId, isFaulty] = $('#id-material').val().split("[::]");
    var searchCriteria = {
        since: $('.fromdate').val(), till: $('.todate').val(),
        issued_to: $('#id-issued_to').val(),
        item_id: itemId, make_id: makeId, is_faulty: isFaulty,
        ignore_items_not_transacted: $('#ignore_items_not_transacted').is(":checked")
    };
    $.ajax({
        url: "/erp/stores/json/isr/",
        type: "post",
        dataType: "json",
        data: searchCriteria,
        success: function(response) {
            $('#tbl_issue_stock_report').DataTable().clear();
            $('#tbl_issue_stock_report').DataTable().destroy();
            $("#tbl_issue_stock_report tbody").find("tr").remove();
            $.each(response.report, function (key, value) {
                constructReportRow(key, value);
            });
            TableHeaderFixed();
            updateFilterText();
            closeFilterOption();
            $('#loading').hide();
        }, error: function(xhr,errmsg,err) {
            $('#loading').hide();
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });
}

/**
 *
 */
function constructReportRow(key, value) {
    var row = `<tr bgcolor="#ececec" border="0" align="center" style="font-size:16px; font-weight:normal;">
        <td> ${key + 1} </td>
        <td class="text-left"> ${value.issued_to} </td>
        <td class="text-left"> ${value.material_name} </td>
        <td> ${value.unit} </td>
        <td class="text-right"> ${value.stock_price} </td>
        <td class="text-right"> ${value.opening_qty} </td>
        <td class="text-right"> ${value.opening_value}</td>
        <td class="text-right"> ${value.received} </td>
        <td class="text-right"> ${value.received_value}</td>
        <td class="text-right"> ${value.issued} </td>
        <td class="text-right"> ${value.issued_value}</td>
        <td class="text-right"> ${value.closing_qty} </td>
        <td class="text-right"> ${value.closing_value}</td></tr>`;
    $('#tbl_issue_stock_report').append(row);
}