"""
"""
import json
from datetime import datetime
from decimal import Decimal

import sqlalchemy
from sqlalchemy import and_, func, distinct

from erp.masters.changelog import MaterialChangelog
from erp import helper, DEFAULT_MAKE_ID
from erp.accounts import SUNDRY_DEBTORS_GROUP_ID, SUNDRY_CREDITORS_GROUP_ID
from erp.commons.backend import push_notification
from erp.dao import DataAccessObject, executeQuery
from erp.forms import MaterialForm
from erp.formsets import CatalogueMaterialFormset, MaterialMakeFormset, MaterialPartyProfileFormset, \
    MaterialAlternateUnitFormset, SpecificationFormset
from erp.helper import getAccountGroupIDs
from erp.masters import logger
from erp.models import Material, CatalogueMaterial, MaterialSupplierProfile, MaterialMakeMap, Make, \
    CatalogueMaterialMake, Category, IndentMaterial, PurchaseOrderMaterial, ReceiptMaterial, InvoiceMaterial, \
    OAParticulars, Party, Employee, Tax, AccountGroup, Ledger, PartyLedgerMap, TaxLedgerMap, PurchaseOrder, \
    NoteItem, Enterprise, MaterialAlternateUnit, SEParticulars, Specification, Project, UnitMaster, PartyContactMap, \
    ContactDetails, PartyRegistrationDetail, GSTCategory, Country, LocationMaster, UserLocationMap, Receipt, Currency, \
    MaterialPricing, MRP_Materials, ClosingStock
from erp.notifications import PUSH_NOTIFICATION
from util.api_util import json_util
from util.helper import constructFormInitializer, copyFormToEntity

__author__ = 'kalaivanan'

MATERIAL_FORM_PREFIX = 'material'
CATALOGUE_MATERIAL_FORMSET_PREFIX = 'bill_material'
SUPPLIER_PRICE_MATERIAL_FORMSET_PREFIX = 'price_material'
MATERIAL_MAKE_FORMSET_PREFIX = 'material_make'
MATERIAL_ALTERNATE_UNIT_FORMSET_PREFIX = 'alternate_unit'
SPECIFICATION_FORMSET_PREFIX = 'specification'


class MaterialVO(object):
    """
    Value Object that clubs a Catalogue & the list of materials attached to it.
    """

    def __init__(
            self, material_form=MaterialForm(prefix=MATERIAL_FORM_PREFIX),
            material_formset=CatalogueMaterialFormset(prefix=CATALOGUE_MATERIAL_FORMSET_PREFIX),
            supplier_price_material_formset=MaterialPartyProfileFormset(
                prefix=SUPPLIER_PRICE_MATERIAL_FORMSET_PREFIX),
            material_make_formset=MaterialMakeFormset(prefix=MATERIAL_MAKE_FORMSET_PREFIX),
            alternate_unit_formset=MaterialAlternateUnitFormset(prefix=MATERIAL_ALTERNATE_UNIT_FORMSET_PREFIX),
            specification_formset=SpecificationFormset(prefix=SPECIFICATION_FORMSET_PREFIX)
            ):
        self.material_form = material_form
        self.material_formset = material_formset
        self.supplier_price_material_formset = supplier_price_material_formset
        self.material_make_formset = material_make_formset
        self.alternate_unit_formset = alternate_unit_formset
        self.specification_formset = specification_formset
        logger.debug("%s - %s - %s" % (
            self.supplier_price_material_formset.data, self.supplier_price_material_formset.as_p(),
            self.supplier_price_material_formset.errors))

    def __repr__(self):
        return 'Material Form: %s\nBoM Formset: %s' % (
            self.material_form.as_p(), self.material_formset.as_p())

    def is_valid(self):
        logger.debug(
            "Material Valid - %s|BoM Formset - %s|Price Profile Formset - %s|Make Formset - %s |Alternate Units Formset - %s|Specification Formset - %s" % (
                self.material_form.is_valid(), self.material_formset.is_valid(), self.supplier_price_material_formset.is_valid(),
                self.material_make_formset.is_valid(), self.alternate_unit_formset.is_valid(), self.specification_formset.is_valid()
            ))
        return self.material_form.is_valid() and self.material_formset.is_valid() and self.supplier_price_material_formset.is_valid() and self.material_make_formset.is_valid() and self.alternate_unit_formset.is_valid() and self.specification_formset.is_valid()


def createPartyLedgers(party=None, user_id=None, db_session=None, is_customer=False, is_supplier=False):
    """
    Create Ledger for Party of the types asked for namely CUSTOMER or SUPPLIER

    :param party:
    :param user_id:
    :param db_session:
    :param is_customer:
    :param is_supplier:
    :return:
    """
    db_session.begin(subtransactions=True)
    try:
        logger.debug("Is Supplier - %s | Is Customer - %s" % (party.isSupplier(), party.isCustomer()))
        if is_customer and not party.getCustomerLedger():
            logger.debug("Customer Ledger for Party - %s needs to be created" % party)
            db_session.begin(subtransactions=True)
            try:
                customer_ledger = Ledger(
                    group_id=SUNDRY_DEBTORS_GROUP_ID, name="%s" % party.name, created_by=user_id,
                    enterprise_id=party.enterprise_id, description="%s (Customer)" % party.name, billable=1)
                db_session.add(customer_ledger)
                db_session.commit()
                db_session.refresh(customer_ledger)
                party.customer_ledger_map = PartyLedgerMap(
                    is_supplier=False, enterprise_id=party.enterprise_id, ledger_id=customer_ledger.id)
            except Exception as e:
                db_session.rollback()
                logger.exception("Customer Ledger Map creation Failed!")
                raise Exception("Customer Ledger Map creation Failed due to - %s" % e.message)
        if is_supplier and not party.getSupplierLedger():
            logger.debug("Supplier Ledger for Party - %s needs to be created" % party)
            db_session.begin(subtransactions=True)
            try:
                supplier_ledger = Ledger(
                    group_id=SUNDRY_CREDITORS_GROUP_ID, name="%s" % party.name, created_by=user_id,
                    enterprise_id=party.enterprise_id, description="%s (Supplier)" % party.name, billable=1)
                db_session.add(supplier_ledger)
                db_session.commit()
                db_session.refresh(supplier_ledger)
                party.supplier_ledger_map = PartyLedgerMap(
                    is_supplier=True, enterprise_id=party.enterprise_id, ledger_id=supplier_ledger.id)
            except Exception as e:
                db_session.rollback()
                logger.exception("Supplier Ledger Map creation Failed!")
                raise Exception("Supplier Ledger Map creation Failed due to - %s" % e.message)
        db_session.commit()
        db_session.refresh(party)
        logger.debug("::Ledger Maps:: Supplier map - %s | Customer Map - %s" % (
            party.getSupplierLedger(), party.getCustomerLedger()))
    except:
        db_session.rollback()
        raise


def createTaxLedgers(tax=None, created_by=None, db_session=None, is_input=False, is_output=False, is_default=False):
    """
    Create Tax Ledgers of the type specified namely, INPUT or OUTPUT or both

    :param tax:
    :param created_by:
    :param db_session:
    :param is_input:
    :param is_output:
    :param is_default:
    :return:
    """
    db_session.begin(subtransactions=True)
    try:
        _tax_group_id = getAccountGroupIDs(
            enterprise_id=tax.enterprise_id, names=[AccountGroup.DUTIES_AND_TAXES_GROUP_NAME], deep=False)
        if is_input and (not tax.input_ledger_map):
            db_session.begin(subtransactions=True)
            try:
                input_ledger = Ledger(
                    group_id=_tax_group_id[0], name="INPUT " + tax.name, created_by=created_by,
                    description="INPUT " + tax.name, enterprise_id=tax.enterprise_id, is_default=is_default)
                db_session.add(input_ledger)
                db_session.commit()
                db_session.refresh(input_ledger)
            except:
                db_session.rollback()
                raise
            logger.info("Input Ledger ID: %s" % input_ledger.id)
            input_tax_ledger_map = TaxLedgerMap(
                tax_id=tax.code, enterprise_id=tax.enterprise_id, is_input=1, ledger_id=input_ledger.id)
            db_session.add(input_tax_ledger_map)
        if is_output and (not tax.output_ledger_map):
            db_session.begin(subtransactions=True)
            try:
                output_ledger = Ledger(
                    group_id=_tax_group_id[0], name="OUTPUT " + tax.name, created_by=created_by,
                    description="OUTPUT " + tax.name, enterprise_id=tax.enterprise_id, is_default=is_default)
                db_session.add(output_ledger)
                db_session.commit()
                db_session.refresh(output_ledger)
            except:
                db_session.rollback()
                raise
            logger.info("Output Ledger ID: %s" % output_ledger.id)
            output_tax_ledger_map = TaxLedgerMap(
                tax_id=tax.code, enterprise_id=tax.enterprise_id, is_input=0, ledger_id=output_ledger.id)
            db_session.add(output_tax_ledger_map)
        db_session.commit()
        db_session.refresh(tax)
    except:
        db_session.rollback()
        raise


class MasterDAO(DataAccessObject):
    """
    Data Access Object to handle all master information related ORM-DB operations
    """
    # Material Data ops
    def getAllMaterials(self, enterprise_id=None):
        return self.db_session.query(Material).filter(Material.enterprise_id == enterprise_id).order_by(
            Material.in_use.desc(), Material.created_on.desc())

    def getMaterial(self, drawing_no, enterprise_id):
        return self.db_session.query(Material).filter(
            Material.drawing_no == drawing_no, Material.enterprise_id == enterprise_id).first()

    def getMaterialByMaterialName(self, name, enterprise_id):
        return self.db_session.query(Material).filter(
            Material.name == name, Material.enterprise_id == enterprise_id).first()

    def getMaterialBydrawing_no(self, drawing_no, enterprise_id):
        return self.db_session.query(Material).filter(
            Material.drawing_no == drawing_no, Material.enterprise_id == enterprise_id).first()

    def deleteMaterial(self, material=None, item_id=None, enterprise_id=None):
        """
        Soft Delete of the material specified, either as an object instance or denoted through the drawing_no

        :param material: Object Instance of the Material to be deleted
        :param item_id: Item id of the Material to be deleted
        :param enterprise_id:
        :return: None
        """
        self.db_session.begin(subtransactions=True)
        try:
            material = material if item_id is None else self.db_session.query(Material).filter(
                Material.material_id == item_id, Material.enterprise_id == enterprise_id).first()
            material.in_use = False  # Turn-off the IN_USE Flag - Soft Delete
            self.db_session.add(material)
            self.db_session.commit()
        except Exception as e:
            logger.exception("Deleting Material Failed: %s" % e.message)
            self.db_session.rollback()

    def scaleMaterialQuantities(self, item_id, enterprise_id, scale=1):
        """
        Scales relevant quantity fields for various tables that keeps stock of materials.

        :param item_id: identifies the material scaling is to be done
        :param enterprise_id:
        :param scale: Scaling factor
        :return: None
        """
        if scale == 1:
            logger.info('No scaling of quantities required')
            return
        scale = Decimal('%0.5f' % scale)
        logger.info('Unit Scaling of quantities for %s/%s-%s(rate) begun' % (enterprise_id, item_id, scale))
        self.db_session.begin(subtransactions=True)
        try:
            # Price-profile scaling
            self.db_session.query(MaterialSupplierProfile).filter(
                MaterialSupplierProfile.item_id == item_id, MaterialSupplierProfile.enterprise_id == enterprise_id
            ).update({
                MaterialSupplierProfile.price: MaterialSupplierProfile.price / scale})

            # Scaling quantity in BOM / Catalogue materials
            self.db_session.query(CatalogueMaterial).filter(
                CatalogueMaterial.item_id == item_id, CatalogueMaterial.enterprise_id == enterprise_id
            ).update({CatalogueMaterial.quantity: CatalogueMaterial.quantity * scale})

            # Scaling quantity in Indent Materials
            self.db_session.query(IndentMaterial).filter(
                IndentMaterial.item_id == item_id, IndentMaterial.enterprise_id == enterprise_id
            ).update({IndentMaterial.quantity: IndentMaterial.quantity * scale})

            # Scaling quantity & rate in Purchase Order Materials
            self.db_session.query(PurchaseOrderMaterial).filter(
                PurchaseOrderMaterial.item_id == item_id, PurchaseOrderMaterial.enterprise_id == enterprise_id
            ).update({
                PurchaseOrderMaterial.quantity: PurchaseOrderMaterial.quantity * scale,
                PurchaseOrderMaterial.rate: PurchaseOrderMaterial.rate / scale})
            # Scaling quantity & rate in Receipt Materials
            self.db_session.query(ReceiptMaterial).filter(
                ReceiptMaterial.item_id == item_id, ReceiptMaterial.enterprise_id == enterprise_id
            ).update({
                ReceiptMaterial.quantity: ReceiptMaterial.quantity * scale,
                ReceiptMaterial.accepted_qty: ReceiptMaterial.accepted_qty * scale,
                ReceiptMaterial.received_qty: ReceiptMaterial.received_qty * scale,
                ReceiptMaterial.rate: ReceiptMaterial.rate / scale})

            # Scaling quantity & rate in Invoice Materials
            self.db_session.query(InvoiceMaterial).filter(
                InvoiceMaterial.item_id == item_id, InvoiceMaterial.enterprise_id == enterprise_id
            ).update({
                InvoiceMaterial.quantity: InvoiceMaterial.quantity * scale,
                InvoiceMaterial.rate: InvoiceMaterial.rate / scale})

            # Scaling quantity & price in Order Acknowledgement Materials
            self.db_session.query(OAParticulars).filter(
                OAParticulars.item_id == item_id, OAParticulars.enterprise_id == enterprise_id
            ).update({
                OAParticulars.quantity: OAParticulars.quantity * scale,
                OAParticulars.price: OAParticulars.price / scale})

            self.db_session.query(NoteItem).filter(
                NoteItem.item_id == item_id, NoteItem.enterprise_id == enterprise_id
            ).update({
                NoteItem.quantity: NoteItem.quantity * scale,
                NoteItem.rate: NoteItem.rate / scale})

            # Scaling quantity & price in Sales Estimate Materials
            self.db_session.query(SEParticulars).filter(
                SEParticulars.item_id == item_id, SEParticulars.enterprise_id == enterprise_id
            ).update({
                SEParticulars.quantity: SEParticulars.quantity * scale,
                SEParticulars.unit_rate: SEParticulars.unit_rate / scale})

            self.db_session.commit()
            logger.info('Unit Scaling quantities of material: %s to new unit completed!' % item_id)
        except Exception as e:
            self.db_session.rollback()
            logger.error("Unit Scaling failed due to %s" % e.message)
            raise

    # Catalogue Data ops
    def getAllCatalogues(self):
        return self.db_session.query(Material).all()

    def getMaterialByDrawingNo(self, drawing_no, enterprise_id):
        return self.db_session.query(Material).filter(
            Material.drawing_no == drawing_no, Material.enterprise_id == enterprise_id).first()

    def getMaterialByMaterialName(self, name, enterprise_id):
        return self.db_session.query(Material).filter(
            Material.name == name, Material.enterprise_id == enterprise_id).first()

    def getMaterialByItemId(self, item_id, enterprise_id):
        return self.db_session.query(Material).filter(
            Material.material_id == item_id, Material.enterprise_id == enterprise_id).first()

    def getParentByItemId(self, item_id, enterprise_id):
        return self.db_session.query(Material).filter(
            CatalogueMaterial.parent_id == item_id, CatalogueMaterial.enterprise_id == enterprise_id).first()

    def getCatalogueMaterial(self, parent_id, item_id, enterprise_id):
        return self.db_session.query(CatalogueMaterial).filter(
            CatalogueMaterial.parent_id == parent_id, CatalogueMaterial.item_id == item_id,
            CatalogueMaterial.enterprise_id == enterprise_id).first()

    def material_pricing(self,item_id, enterprise_id):
        return (self.db_session.query(MaterialPricing.purchase_price, MaterialPricing.purchase_currency_id, MaterialPricing.approved_price,
            MaterialPricing.approved_currency_id, MaterialPricing.supplier_id, MaterialPricing.stored_price).filter
            (MaterialPricing.item_id == item_id, MaterialPricing.enterprise_id == enterprise_id).first())

    def get_supplier_name(self, supplier_id):
        return self.db_session.query(Party.name).filter(Party.id == supplier_id).first()

    def get_currency_name(self, currency_id):
        return self.db_session.query(Currency.code).filter(Currency.id == currency_id).first()

    def getMaterialsForCatalogue(self, parent_id, enterprise_id):
        return self.db_session.query(CatalogueMaterial).filter(
            CatalogueMaterial.parent_id == parent_id, CatalogueMaterial.enterprise_id == enterprise_id).all()

    def getMaterialSupplierProfile(self, item_id, enterprise_id, supp_id, effect_since, make_id, is_service):
        return self.db_session.query(MaterialSupplierProfile).filter(
            MaterialSupplierProfile.item_id == item_id, MaterialSupplierProfile.enterprise_id == enterprise_id,
            MaterialSupplierProfile.supp_id == supp_id, MaterialSupplierProfile.effect_since == effect_since,
            MaterialSupplierProfile.make_id == make_id, MaterialSupplierProfile.is_service == is_service).first()

    def getCurrentMaterialSupplierPrice(self, item_id, enterprise_id, supp_id, make_id, is_job):
        return self.db_session.query(MaterialSupplierProfile).filter(
            MaterialSupplierProfile.item_id == item_id, MaterialSupplierProfile.enterprise_id == enterprise_id,
            MaterialSupplierProfile.supp_id == supp_id, MaterialSupplierProfile.effect_since < datetime.today(),
            MaterialSupplierProfile.make_id == make_id, MaterialSupplierProfile.is_service == is_job,
            MaterialSupplierProfile.status == MaterialSupplierProfile.APPROVED).order_by(
            MaterialSupplierProfile.effect_since.desc()).first()

    def getMaterialMake(self, make_id, item_id, enterprise_id):
        return self.db_session.query(MaterialMakeMap).filter(
            MaterialMakeMap.make_id == make_id, MaterialMakeMap.item_id == item_id,
            MaterialMakeMap.enterprise_id == enterprise_id).first()

    def getSpecification(self, material_id, enterprise_id, parameter):
        return self.db_session.query(Specification).filter(
            Specification.material_id == material_id, Specification.enterprise_id == enterprise_id,
            Specification.parameter == parameter).first()

    def getMaterialAlternateUnit(self, item_id, alternate_unit_id, enterprise_id):
        return self.db_session.query(MaterialAlternateUnit).filter(
            MaterialAlternateUnit.alternate_unit_id == alternate_unit_id, MaterialAlternateUnit.item_id == item_id,
            MaterialAlternateUnit.enterprise_id == enterprise_id).first()

    def getPBOMMaterialList(self, item_id, enterprise_id):
        """

        :param item_id:
        :param enterprise_id:
        :return:
        """
        query = """SELECT 
                        cat_materials.qty AS qty,
                        materials.drawing_no AS drawing_no,
                        materials.name AS name,
                        materials.description AS description,
                        materials.makes_json,
                        materials.id as item_id, materials.is_stocked AS is_stock,
                        IFNULL(materials.is_service, 0) AS is_service    
                    FROM
                        cat_materials
                            LEFT OUTER JOIN
                        catalogue_material_makes ON catalogue_material_makes.parent_id = cat_materials.parent_id
                            AND catalogue_material_makes.item_id = cat_materials.item_id
                            AND catalogue_material_makes.enterprise_id = cat_materials.enterprise_id
                            LEFT OUTER JOIN
                        materials ON cat_materials.parent_id = materials.id
                            AND cat_materials.enterprise_id = materials.enterprise_id
                    WHERE
                        cat_materials.item_id = '%s'
                            AND cat_materials.enterprise_id = '%s'
                    GROUP BY cat_materials.parent_id""" % (item_id, enterprise_id)
        return executeQuery(query)


    def deleteCatalogueMaterial(
            self, catalogue_material=None, catalogue_code=None, item_id=None, enterprise_id=None):
        self.db_session.begin(subtransactions=True)
        try:
            if catalogue_material:
                self.db_session.delete(catalogue_material)
            else:
                self.db_session.delete(self.db_session.query(CatalogueMaterial).filter(
                    CatalogueMaterial.parent_id == catalogue_code, CatalogueMaterial.item_id == item_id,
                    CatalogueMaterial.enterprise_id == enterprise_id).first())
            self.db_session.commit()
        except Exception as e:
            self.db_session.rollback()
            logger.exception("Deleting Catalogue Material Failed - %s" % e.message)

    # Party
    def getAllParties(self, enterprise_id=None):
        """

        :param enterprise_id:
        :return:
        """
        logger.info('Fetching all parties of enterprise_id: %s' % enterprise_id)
        return self.db_session.query(Party).filter(Party.enterprise_id == enterprise_id).all()

    def getLastAddedParty(self, enterprise_id=None):
        """

        :param enterprise_id:
        :return:
        """
        logger.info('Fetching last added party of enterprise_id: %s' % enterprise_id)
        party_detail = self.db_session.query(Party.id, Party.code, Party.name).filter(Party.enterprise_id == enterprise_id).order_by(Party.id.desc()).first()
        return party_detail._asdict()

    def getAllPartiesArrayMap(self, enterprise_id=None):
        """

        :param enterprise_id:
        :return:
        """
        try:
            return json_util.objectAsArrayMap(self.getAllParties(enterprise_id=enterprise_id))
        except:
            raise

    def getAllPartyNames(self, enterprise_id=None):
        """

        :param enterprise_id:
        :return:
        """
        logger.info('Fetching all parties of enterprise_id: %s' % enterprise_id)
        party_list = []
        try:
            parties = self.db_session.query(
                Party.id.label('id'), Party.name.label('name'),
                Party.code.label('code')).filter(Party.enterprise_id == enterprise_id).order_by(Party.name).all()
            for party in parties:
                party_item = party._asdict()
                party_list.append(party_item)
        except:
            raise
        return party_list

    def getPartyByCode(self, party_code="", enterprise_id=None):
        """

        :param party_code:
        :param enterprise_id:
        :return:
        """
        try:
            party_query = self.db_session.query(Party).filter(
                Party.code == party_code, Party.enterprise_id == enterprise_id)
            party = party_query.first()
            logger.info("Party Fetched: %s" % party)
            return party
        except:
            raise

    def getPartyByGst(self, enterprise_id=None, gst=""):
        """

        :param gst:
        :param enterprise_id:
        :return:
        """

        try:
            party_query = self.db_session.query(Party).filter(
                Party.enterprise_id == enterprise_id, func.soundex(Party.gst_no) == func.soundex(gst))
            party = party_query.first()
            logger.info("Party Fetched: %s" % party)
            return party
        except:
            raise

    def getPartyAndProjectByPO(self, enterprise_id=None, po="", supplier_id=""):
        """

        :param po number:
        :param enterprise_id:
        :return:
        """

        try:
            po_query = self.db_session.query(PurchaseOrder).filter(
                PurchaseOrder.enterprise_id == enterprise_id, PurchaseOrder.po_no == po,
                PurchaseOrder.supplier_id == supplier_id)
            po = po_query.first()
            return po
        except:
            raise

    def deleteParty(self, party_code="", enterprise_id=None):
        """

        :param party_code:
        :param enterprise_id:
        :return:
        """
        self.db_session.begin(subtransactions=True)
        try:
            self.db_session.query(Party).filter(
                Party.code == party_code, Party.enterprise_id == enterprise_id).delete()
            self.db_session.commit()
            return True
        except:
            self.db_session.rollback()
            raise

    def getPartyContactDetail(self, enterprise_id=None, party_id=None, sequence_id=None):
        return self.db_session.query(
            PartyContactMap).filter(
            PartyContactMap.enterprise_id == enterprise_id, PartyContactMap.party_id == party_id,
            PartyContactMap.sequence_id == sequence_id).first()

    # Employee
    def getEmployeeByCode(self, emp_code=None, enterprise_id=None):
        """

        :param emp_code:
        :param enterprise_id:
        :return:
        """
        logger.info('Fetching employee of by code : %s' % emp_code)
        employee_query = self.db_session.query(Employee).filter(
            Employee.emp_code == emp_code, Employee.enterprise_id == enterprise_id)
        return employee_query.first()

    def getAllEmployees(self, enterprise_id=None):
        """

        :param enterprise_id:
        :return:
        """
        logger.info('Fetching all employees of enterprise_id: %s' % enterprise_id)
        return self.db_session.query(Employee).filter(Employee.enterprise_id == enterprise_id).all()

    def getAllEmployeesArrayMap(self, enterprise_id=None):
        """

        :param enterprise_id:
        :return:
        """
        return json_util.objectAsArrayMap(self.getAllEmployees(enterprise_id=enterprise_id))

    def deleteEmployee(self, emp_code=None, enterprise_id=None):
        """

        :param emp_code:
        :param enterprise_id:
        :return:
        """
        self.db_session.begin(subtransactions=True)
        try:
            self.db_session.query(Employee).filter(
                Employee.emp_code == emp_code, Employee.enterprise_id == enterprise_id).delete()
            self.db_session.commit()
            return True
        except:
            self.db_session.rollback()
            raise

    def saveEmployee(self, employee=None):
        """

        :param employee:
        :return:
        """
        self.db_session.begin(subtransactions=True)
        try:
            self.db_session.add(employee)
            self.db_session.commit()
            return True
        except:
            self.db_session.rollback()
            raise

    # Material
    def getMaterialByDrawingNumber(self, drawing_no=None, enterprise_id=None):
        """

        :param drawing_no:
        :param enterprise_id:
        :return:
        """
        material_query = self.db_session.query(Material).filter(
            Material.drawing_no == drawing_no, Material.enterprise_id == enterprise_id)
        return material_query.first()

    # Material
    def getMaterialDetail(self, item_id=None, enterprise_id=None):
        """

        :param item_id:
        :param enterprise_id:
        :return:
        """
        try:
            material = self.db_session.query(Material).filter(
                Material.material_id == item_id, Material.enterprise_id == enterprise_id).first()
            result = dict(
                drawing_no=material.drawing_no, name=material.name, unit=material.unit.unit_name,
                price=float(material.price), item_id=item_id)
            result['unit_id'] = material.unit.unit_id
            result["makes"] = []
            if material.makes_json:
                for make_json in json.loads((material.makes_json)):
                    result["makes"].append(make_json['make'] + ' - ' + make_json['mpn'] if make_json['mpn'] else make_json['make'])
            if material.category:
                result['category'] = material.category.name
                result['category_id'] = material.category.id
            else:
                result['category'] = "No Category"
            result['in_use'] = material.in_use
            result['stock_accountable'] = material.is_stocked
            result['description'] = material.description
            result['tariff_no'] = material.tariff_no
            result['price'] = str(material.price)
            result['msl'] = str(material.minimum_stock_level)
            result['is_service'] = str(material.is_service)

            # Bill of material

            bom = []
            for bom_object in material.bill_of_materials:
                m = dict(
                    drawing_no=bom_object.material.drawing_no if bom_object.material.drawing_no else "", name=bom_object.material.name,
                    unit=bom_object.material.unit.unit_name, quantity=float(bom_object.quantity), makes=[],
                    is_stock=bom_object.material.is_stocked)
                if bom_object.material.makes_json:
                    for make_json in json.loads((bom_object.material.makes_json)):
                        m["makes"].append(make_json['make'])
                bom.append(m)
            result['bill_of_material'] = bom

            supplier_prices = []
            history = []
            today = datetime.today()
            for p in material.supplier_wise_materials_price:
                if p.status == MaterialSupplierProfile.APPROVED:
                    price = self.getMaterialPriceProfileAsJson(p)
                    supplier_price = {"make": p.make.label}
                    supplier_price.update(price)
                    supplier_prices.append(supplier_price)
                    if today >= p.effect_since:
                        history.append(price)

            result['supplier_prices'] = supplier_prices
            result['supplier_history'] = history
            return result
        except:
            raise

    def getAllMaterialNames(self, enterprise_id=None):
        """

        :param enterprise_id:
        :return:
        """
        material_array = []
        try:
            logger.info('Fetching all material of enterprise_id: %s' % enterprise_id)
            material_list = self.db_session.query(
                Material.drawing_no.label('drawing_no'), Material.name.label('name'),
                Material.material_id.label('item_id'), Material.is_stocked, Material.unit_id.label('unit_id'),
                UnitMaster.unit_name.label('primary_unit'), Material.tariff_no.label('hsn_code'), Material.makes_json.label('make_name')
            ).outerjoin(
                UnitMaster, and_(
                    UnitMaster.unit_id == Material.unit_id, UnitMaster.enterprise_id == Material.enterprise_id)
            ).filter(
                Material.enterprise_id == enterprise_id).order_by(Material.name).all()
            for material in material_list:
                m = material._asdict()
                m['make_name'] = helper.constructDifferentMakeName(material.make_name)
                material_array.append(m)
        except:
            raise
        return material_array

    def getAllMaterialsArrayMap(self, enterprise_id=None):
        """

        :param enterprise_id:
        :return:
        """
        return json_util.objectAsArrayMap(self.getAllMaterials(enterprise_id=enterprise_id))

    def saveMaterial(self, material=None):
        """

        :param material:
        :return:
        """
        self.db_session.begin(subtransactions=True)
        try:
            self.db_session.add(material)
            self.db_session.commit()
            return True
        except:
            self.db_session.rollback()
            raise

    def getTaxByCode(self, code=None, enterprise_id=None):
        """

        :param code:
        :param enterprise_id:
        :return:
        """
        tax_query = self.db_session.query(Tax).filter(Tax.code == code, Tax.enterprise_id == enterprise_id)
        return tax_query.first()

    def getAllTaxes(self, enterprise_id=None):
        """

        :param enterprise_id:
        :return:
        """
        logger.info('Fetching all taxes of enterprise_id: %s' % enterprise_id)
        return self.db_session.query(Tax).filter(Tax.enterprise_id == enterprise_id).all()

    def getAllTaxesArrayMap(self, enterprise_id=None):
        """

        :param enterprise_id:
        :return:
        """
        return json_util.objectAsArrayMap(self.getAllTaxes(enterprise_id=enterprise_id))

    def deleteTax(self, tax_code=None, enterprise_id=None):
        """

        :param tax_code:
        :param enterprise_id:
        :return:
        """
        self.db_session.begin(subtransactions=True)
        try:
            self.db_session.query(Tax).filter(
                Tax.code == tax_code, Tax.enterprise_id == enterprise_id).delete()
            self.db_session.commit()
            return True
        except:
            self.db_session.rollback()
            raise

    def saveTax(self, tax=None):
        """

        :param tax:
        :return:
        """
        self.db_session.begin(subtransactions=True)
        try:
            self.db_session.add(tax)
            self.db_session.commit()
            return True
        except:
            self.db_session.rollback()
            raise

    def getMaterialPriceProfileAsJson(self, profile):
        """

        :param profile:
        :return:
        """
        try:
            currency = profile.supplier_price_currency if profile.supplier_price_currency else profile.supplier.party_currency
            return dict(
                price=float(profile.price), status=profile.status, currency=currency.code,
                effect_since=profile.effect_since.strftime("%d-%m-%Y"),
                effect_till=profile.effect_till.strftime("%d-%m-%Y") if profile.effect_till else "",
                supplier={"id": profile.supplier.id, "code": profile.supplier.code, "name": profile.supplier.name})
        except Exception as e:
            logger.exception("Party Profile JSON construction failed - %s" % e.message)
            raise

    def getSupplierPrices(self, enterprise_id=None):
        """

        :param enterprise_id:
        :return:
        """
        supplier_prices = []
        # TODO optimize this method; ensure no queries should be inside a loop
        try:
            query = self.db_session.query(MaterialSupplierProfile).filter(
                MaterialSupplierProfile.enterprise_id == enterprise_id,
                MaterialSupplierProfile.status == MaterialSupplierProfile.PENDING)

            logger.info("Material profile query %s" % MaterialSupplierProfile.make)
            profiles_to_be_approved = query.all()

            logger.info("Fetching %s supplier profile to approval process" % len(profiles_to_be_approved))
            for profile_to_be_approved in profiles_to_be_approved:
                profile_dict = self.getMaterialPriceProfileAsJson(profile_to_be_approved)
                profile_dict.update(dict(
                    reject_remarks=profile_to_be_approved.reject_remarks,
                    material={
                        'drawing_no': profile_to_be_approved.material.drawing_no,
                        'item_id':profile_to_be_approved.material.material_id,'name': profile_to_be_approved.material.name}))
                profile_dict['make_id'] = profile_to_be_approved.make_id
                if profile_to_be_approved.make_id != DEFAULT_MAKE_ID:
                    part_no = helper.getMakePartNumber(
                        enterprise_id=enterprise_id, item_id=profile_to_be_approved.material.material_id
                        , make_id=profile_to_be_approved.make_id)
                    profile_dict['make'] = "%s%s" % (
                        profile_to_be_approved.make.label, (" - %s" % part_no) if part_no else "")
                else:
                    profile_dict['make'] = "%s" % profile_to_be_approved.make.label
                profile_dict['remarks'] = "%s" % profile_to_be_approved.remarks
                other_approved_profiles = self.db_session.query(MaterialSupplierProfile).filter(
                    MaterialSupplierProfile.enterprise_id == enterprise_id,
                    MaterialSupplierProfile.item_id == profile_to_be_approved.material.material_id,
                    MaterialSupplierProfile.status == MaterialSupplierProfile.APPROVED).group_by(
                    MaterialSupplierProfile.supp_id).order_by(MaterialSupplierProfile.effect_since.desc()).all()
                profile_dict['supplier_prices'] = []
                for approved_profile in other_approved_profiles:
                    approved_profile_dict = self.getMaterialPriceProfileAsJson(approved_profile)
                    if approved_profile.make_id != DEFAULT_MAKE_ID:
                        part_no = helper.getMakePartNumber(
                            enterprise_id=enterprise_id, item_id=approved_profile.material.material_id
                            , make_id=approved_profile.make_id)
                        approved_profile_dict['make'] = "%s%s" % (
                            approved_profile.make.label, (" - %s" % part_no) if part_no else "")
                    else:
                        approved_profile_dict['make'] = "%s" % approved_profile.make.label
                    profile_dict['supplier_prices'].append(approved_profile_dict)
                supplier_prices.append(profile_dict)
        except Exception as e:
            self.db_session.rollback()
            logger.exception("Fetching Material price profiles failed - %s" % e.message)
            raise

        return supplier_prices

    def getPendingPriceApprovalCount(self, enterprise_id=None):
        try:
            logger.info('Fetching count of checked GRN for enterprise %s' % enterprise_id)
            return self.db_session.query(MaterialSupplierProfile).filter(
                MaterialSupplierProfile.enterprise_id == enterprise_id,
                MaterialSupplierProfile.status == MaterialSupplierProfile.PENDING).count()
        except:
            raise

    def getAllProjects(self, enterprise_id=None):
        logger.info('Fetching all tags of enterprise_id: %s' % enterprise_id)
        project_list = []
        try:
            projects = self.db_session.query(Project.id.label('id'), Project.name.label('name'), Project.code.label('code')).filter(
                Project.enterprise_id == enterprise_id, Project.is_active).order_by(Project.code).all()
            for project in projects:
                project_item = project._asdict()
                project_list.append(project_item)
        except:
            raise
        return project_list


class MasterService:
    """
    Controller class that handles the business logic of various master information modules
    """

    def __init__(self):
        """

        """
        self.master_dao = MasterDAO()

    def getMaterials(self, enterprise_id=None, modified_after=None, criteria="", in_use=1):
        try:
            if modified_after and modified_after != "":
                criteria = "%s AND m.last_modified_on >= '%s'" % (criteria, modified_after)
            material_query = """SELECT m.drawing_no as drawing_no, m.name as name, m.price as price, um.unit_name as unit_name, 
                                IFNULL((SELECT COUNT(1) FROM cat_materials WHERE parent_id=m.id AND enterprise_id=m.enterprise_id), 0) AS bom,
                                makes_json as make_name,
                                m.standard_packing_quantity as spq, 1 as make_id , m.id as item_id, m.id as cat_code,
                                IFNULL((SELECT COUNT(1) FROM materials_alternate_units WHERE item_id=m.id AND enterprise_id=m.enterprise_id), 0) AS alternate_unit_count, 
                                m.tariff_no as hsn_code, m.is_stocked as is_stock, m.description AS description, m.is_service as is_service 
                                FROM materials AS m, unit_master AS um
                                WHERE m.in_use = {in_use} AND m.enterprise_id='{enterprise_id}' AND m.unit = um.unit_id
                                AND um.enterprise_id = m.enterprise_id {additional_where_clause}
                                ORDER BY m.name""".format(
                enterprise_id=enterprise_id, additional_where_clause=criteria, in_use=in_use)

            materials = executeQuery(material_query, as_dict=True)
            for item in materials:
                item['make_name'] = helper.constructDifferentMakeName(item['make_name'])

        except Exception as e:
            materials = []
            logger.exception(e.message)
        return materials

    def getAllMaterials(
            self, enterprise_id=None, pending_only=None, price_profile=None, is_service=None, stock_material=None,
            in_use_items=None, not_in_use_items=None, all_items=None):
        """

        :param enterprise_id:
        :param pending_only:
        :param price_profile:
        :param not_in_use_items:
        :param is_service:
        :param stock_material:
        :param in_use_items:
        :param all_items:
        :return:
        """
        try:
            num_suppliers, pending, returned = [sqlalchemy.sql.expression.literal_column("0")] * 3
            if price_profile:
                pending = func.sum(func.IF(MaterialSupplierProfile.status == MaterialSupplierProfile.PENDING, 1, 0))
                returned = func.sum(func.IF(MaterialSupplierProfile.status == MaterialSupplierProfile.REJECTED, 1, 0))
                num_suppliers = func.count(distinct(MaterialSupplierProfile.supp_id))

            # Subquery for aggregated MRP
            mrp_subquery = None
            if is_service == 0:
                mrp_subquery = (
                    self.master_dao.db_session.query(
                        MRP_Materials.item_id.label("item_id"),
                        MRP_Materials.enterprise_id.label("enterprise_id"),
                        func.sum(MRP_Materials.allocated_qty).label("allocated_qty"),
                        func.sum(MRP_Materials.issued_qty).label("issued_qty"),
                    )
                    .group_by(MRP_Materials.item_id, MRP_Materials.enterprise_id)
                    .subquery()
                )

            # Subquery for aggregated ClosingStock
            closing_stock_subquery = None
            if is_service == 0:
                closing_stock_subquery = (
                    self.master_dao.db_session.query(
                        ClosingStock.item_id.label("item_id"),
                        ClosingStock.enterprise_id.label("enterprise_id"),
                        func.sum(ClosingStock.qty).label("qty"),
                    )
                    .filter(ClosingStock.is_faulty == 0)
                    .group_by(ClosingStock.item_id, ClosingStock.enterprise_id)
                    .subquery()
                )

            # Main query
            query = self.master_dao.db_session.query(
                Material.drawing_no.label("drawing_no"),
                Material.name.label("name"),
                Category.name.label("category"),
                Material.price.label("price"),
                Material.in_use.label("in_use"),
                Material.last_modified_on.label("last_modified_on"),
                num_suppliers.label("num_suppliers"),
                pending.label("price_approvals_pending"),
                returned.label("price_approvals_returned"),
                Material.material_id.label("item_id"),
                Material.is_stocked.label("is_stocked"),
                Material.is_service.label("is_service"),
                Material.makes_json.label("part_number"),
                UnitMaster.unit_name.label("uom"),
            )
            if is_service == 0:
                query = query.add_columns(
                    (func.coalesce(mrp_subquery.c.allocated_qty, 0) - func.coalesce(mrp_subquery.c.issued_qty,
                                                                                    0)).label("allocated"),
                    func.coalesce(closing_stock_subquery.c.qty, 0).label("available"),
                ).outerjoin(
                    mrp_subquery,
                    and_(
                        mrp_subquery.c.item_id == Material.material_id,
                        mrp_subquery.c.enterprise_id == Material.enterprise_id,
                    ),
                ).outerjoin(
                    closing_stock_subquery,
                    and_(
                        closing_stock_subquery.c.item_id == Material.material_id,
                        closing_stock_subquery.c.enterprise_id == Material.enterprise_id,
                    ),
                )
            # Joins
            query = query.outerjoin(Category, Material.category_id == Category.id).outerjoin(
                UnitMaster,
                and_(
                    UnitMaster.unit_id == Material.unit_id,
                    UnitMaster.enterprise_id == Material.enterprise_id,
                ),
            )
            if price_profile:
                query = query.outerjoin(MaterialSupplierProfile, and_(
                    MaterialSupplierProfile.item_id == Material.material_id,
                    MaterialSupplierProfile.enterprise_id == Material.enterprise_id))
            filters = [Material.enterprise_id == enterprise_id]
            if is_service == 0:
                filters.append(Material.is_stocked == stock_material)
            else:
                filters.append(Material.is_stocked == 0)
            if all_items:
                filters.append(Material.in_use.in_((0, 1)))
            elif in_use_items:
                filters.append(Material.in_use == 1)
            elif not_in_use_items:
                filters.append(Material.in_use == 0)

            filters.append(Material.is_service == is_service)

            query = query.filter(*filters)
            if pending_only is not None:
                query = query.having(pending != 0)
            query = query.group_by(Material.drawing_no, Material.name).order_by(
                Material.in_use.desc(), Material.last_modified_on.desc())
            return query.all()
        except:
            raise

    def generateMaterialForm(self, material=Material(), enterprise_id=None):
        """

        :return:
        """
        enterprise = self.master_dao.db_session.query(Enterprise.is_negative_stock_allowed).filter(
            Enterprise.id == enterprise_id).first()
        return MaterialForm(
            prefix=MATERIAL_FORM_PREFIX, enterprise_id=enterprise_id,
            is_negative_stock_allowed=enterprise.is_negative_stock_allowed,
            initial={
                'drawing_no': material.drawing_no,
                'name': material.name,
                'description': material.description,
                'price': material.price,
                'category_id': material.category_id,
                'is_stocked': material.is_stocked,
                'in_use': material.in_use,
                'is_service': material.is_service,
                'unit_id': material.unit_id,
                'current_unit': material.unit_id,
                'tariff_no': material.tariff_no,
                'standard_packing_qty': material.standard_packing_qty,
                'material_id': material.material_id,
                'minimum_stock_level': material.minimum_stock_level,
                'sample_size': material.sample_size,
                'lot_size': material.lot_size,
                'qc_method': material.qc_method,
                'reaction_plan': material.reaction_plan,
                'location': material.location,
                'remarks': material.remarks,
                'enterprise_id': enterprise_id if enterprise_id is not None else material.enterprise_id})

    def getAllMaterialsForms(self):
        """

        :return:
        """
        return self.master_dao.getAllMaterials()

    def deleteMaterial(self, item_id, enterprise_id):
        """

        :param item_id: identifies the material chosen to be deleted
        :param enterprise_id: identifies the Enterprise in context (logged-in-user's Enterprise)
        :return:
        """
        self.master_dao.deleteMaterial(item_id=item_id, enterprise_id=enterprise_id)
        return

    @staticmethod
    def _getMaterialEntityFromForm(form=MaterialForm(), entity=None):
        """

        :param form:Catalogue Material
        :param entity:
        :return:
        """
        if not entity:
            if form.cleaned_data['drawing_no'] is None or form.cleaned_data['drawing_no'] == '':
                return copyFormToEntity(form, Material(), exclude_field_list='drawing_no')
            else:
                return copyFormToEntity(form, Material())
        if form.cleaned_data['drawing_no']:
            entity.drawing_no = form.cleaned_data['drawing_no']
        else:
            entity.drawing_no = None
        entity.name = form.cleaned_data['name']
        entity.description = form.cleaned_data['description']
        entity.price = round(float(form.cleaned_data['price']), 5)
        entity.category_id = form.cleaned_data['category_id']
        entity.in_use = form.cleaned_data['in_use']
        entity.is_stocked = form.cleaned_data['is_stocked']
        entity.is_service = form.cleaned_data['is_service']
        entity.unit_id = form.cleaned_data['unit_id']
        entity.enterprise_id = form.cleaned_data['enterprise_id']
        entity.tariff_no = form.cleaned_data['tariff_no']
        entity.minimum_stock_level = form.cleaned_data['minimum_stock_level']
        entity.sample_size = round(float(form.cleaned_data['sample_size']), 3) if form.cleaned_data['sample_size'] else None
        entity.lot_size = round(float(form.cleaned_data['lot_size']), 3) if form.cleaned_data['lot_size'] else None
        entity.qc_method = form.cleaned_data['qc_method']
        entity.reaction_plan = form.cleaned_data['reaction_plan']
        entity.standard_packing_qty = form.cleaned_data['standard_packing_qty']
        entity.location = form.cleaned_data['location']
        entity.remarks = form.cleaned_data['remarks']
        return entity

    def constructMaterialVO(self, item_id=None, material=None, enterprise_id=None):
        """

        :param item_id:
        :param material:
        :param enterprise_id:
        :return:
        """
        if material is None:
            material = self.master_dao.getMaterialByItemId(
                item_id, enterprise_id) if item_id is not None else Material(enterprise_id=enterprise_id)
        return MaterialVO(
            material_form=self.generateMaterialForm(material=material, enterprise_id=enterprise_id),
            material_formset=self._generateBillOfMaterialFormset(material.bill_of_materials,enterprise_id),
            supplier_price_material_formset=self._generatePriceOfMaterialFormset(
                material.supplier_wise_materials_price),
            specification_formset=self._generateSpecificationFormset(material.specifications),
            material_make_formset=self._generateMakeFormset(material.makes_json),
            alternate_unit_formset=self._generateAlternateUnitFormset(material.alternate_units))

    def saveCatalogue(self, material_vo=None, user=None, material_type=None):
        """

        :param material_vo:
        :param user:
        :return:
        """
        try:
            if material_vo.is_valid():
                try:
                    self.master_dao.db_session.begin(subtransactions=True)
                    item_id = material_vo.material_form.cleaned_data['material_id']
                    enterprise_id = material_vo.material_form.cleaned_data['enterprise_id']
                    scale = material_vo.material_form.cleaned_data['unit_conversion_rate']
                    default_make_spq = material_vo.material_form.cleaned_data['standard_packing_qty']
                    # Saving Catalogue
                    material_queried = self.master_dao.getMaterialByItemId(
                        item_id=item_id, enterprise_id=enterprise_id)

                    material_to_be_saved, status_changed_materials = self._convertMaterialVOToEntity(
                        material_vo, material_queried, user_id=user.id, default_make_spq=default_make_spq)
                    self.master_dao.db_session.add(material_to_be_saved)
                    # Scaling Material quantity by unit conversion rate
                    if scale != '':
                        self.master_dao.scaleMaterialQuantities(
                            item_id=item_id, enterprise_id=enterprise_id, scale=scale)
                    self.master_dao.db_session.commit()
                    self.master_dao.db_session.refresh(material_to_be_saved)
                    MaterialChangelog(material_type).queryInsert(
                        user_id=user.id, enterprise_id=enterprise_id, data=material_to_be_saved)
                except Exception as e:
                    logger.exception("Cannot Save Material - %s" % e)
                    self.master_dao.db_session.rollback()
                    raise
                if len(status_changed_materials) != 0:
                    for supplier_price in status_changed_materials:
                        self.notifySupplierPriceStatus(
                            enterprise_id=enterprise_id, sender_id=user.id, supplier_price=supplier_price)
                else:
                    self.notifyPendingPriceApprovalCount(enterprise_id=enterprise_id, sender_id=user.id)
                return self.constructMaterialVO(enterprise_id=material_to_be_saved.enterprise_id)
            logger.info(
                'Is Valid - %s |Material Errors: %s\nBoM Errors: %s\nSupplier Profile Errors: %s \nMake Profile Errors: %s'
                ' \nAlternate Unit Errors: %s \nSpecification Errors: %s' % (
                    material_vo.is_valid(), material_vo.material_form.errors, material_vo.material_formset.errors,
                    material_vo.supplier_price_material_formset.errors, material_vo.material_make_formset.errors,
                    material_vo.alternate_unit_formset.errors, material_vo.specification_formset.errors))
            return material_vo
        except Exception as e:
            logger.exception("Notification failed after Material Save - %s" % e)
            raise

    @staticmethod
    def _generateBillOfMaterialFormset(bom_materials=(),enterprise_id=None):
        """
        :param bom_materials:
        :return:
        """
        initial_material_values = []
        for bom_material in bom_materials:
            make_name = helper.constructDifferentMakeName(bom_material.material.makes_json)
            material_name = bom_material.material.name + " [" + make_name + "]" if make_name else bom_material.material.name
            bom_material_form_initial = constructFormInitializer(bom_material)
            bom_material_form_initial['units'] = bom_material.material.unit.unit_name
            bom_material_form_initial['name'] = material_name
            bom_material_form_initial['drawing_no'] = bom_material.material.drawing_no if bom_material.material.drawing_no else ""
            bom_material_form_initial['item_id'] = bom_material.material.material_id
            bom_material_form_initial['material_select_field'] = "%s - %s" % (
                bom_material.material.drawing_no, bom_material.material.name)
            bom_material_form_initial['is_stock'] = "%s" % bom_material.material.is_stocked
            bom_material_form_initial['hasChildren'] = True if MasterDAO().getParentByItemId(item_id=bom_material.material.material_id, enterprise_id=enterprise_id) else False
            bom_material_form_initial['is_service'] = "%s" % bom_material.material.is_service
            initial_material_values.append(bom_material_form_initial)
        return CatalogueMaterialFormset(initial=initial_material_values, prefix=CATALOGUE_MATERIAL_FORMSET_PREFIX)

    @staticmethod
    def _generateAlternateUnitFormset(alternate_units=()):
        """
        :param alternate_units:
        :return:
        """
        initial_alternate_unit_values = []
        for alternate_unit in alternate_units:
            alternate_unit_form_initial = constructFormInitializer(alternate_unit)
            alternate_unit_form_initial['alternate_unit_id'] = alternate_unit.alternate_unit_id
            alternate_unit_form_initial['unit_name'] = "%s (%s)" % (
                alternate_unit.unit.unit_name, alternate_unit.unit.unit_description)
            alternate_unit_form_initial['scale_factor'] = alternate_unit.scale_factor
            alternate_unit_form_initial['item_id'] = alternate_unit.material.material_id
            alternate_unit_form_initial['primary_unit_name'] = "%s (%s)" % (
                alternate_unit.material.unit.unit_name, alternate_unit.material.unit.unit_description)
            initial_alternate_unit_values.append(alternate_unit_form_initial)
        return MaterialAlternateUnitFormset(
            initial=initial_alternate_unit_values, prefix=MATERIAL_ALTERNATE_UNIT_FORMSET_PREFIX)


    @staticmethod
    def _generateSpecificationFormset(specifications=()):
        """
        #:param specification:
        #:return:
        """
        initial_specification_values = []
        for specification in specifications:
            initial_specification_values.append(constructFormInitializer(specification))
        return SpecificationFormset(initial=initial_specification_values, prefix=SPECIFICATION_FORMSET_PREFIX)

    @staticmethod
    def _generatePriceOfMaterialFormset(supplier_price_profiles=()):
        """

        :param supplier_price_profiles:
        :return:
        """
        initial_supp_price_material_values = []
        for supplier_price_profile in supplier_price_profiles:
            supplier_price_form_initial = constructFormInitializer(supplier_price_profile)
            supplier_price_form_initial['supplier_select_field'] = "%s" % supplier_price_profile.supplier.name
            supplier_price_form_initial[
                'currency_select_field'] = "%s" % supplier_price_profile.supplier_price_currency.code if supplier_price_profile.supplier_price_currency else ""
            supplier_price_form_initial['make_select_field'] = "%s" % (
                supplier_price_profile.make.label if supplier_price_profile.make else "-NA-")
            supplier_price_form_initial['status'] = supplier_price_profile.status
            supplier_price_form_initial['unit_name'] = "%s" % supplier_price_profile.material.unit.unit_name
            if supplier_price_profile.alternate_unit_id:
                scale_factor = helper.getScaleFactor(
                    enterprise_id=supplier_price_profile.enterprise_id, item_id=supplier_price_profile.item_id,
                    alternate_unit_id=supplier_price_profile.alternate_unit_id)
                logger.info(supplier_price_profile.alternate_unit.unit.unit_name)
                if scale_factor:
                    supplier_price_form_initial['moq'] = Decimal(supplier_price_profile.moq) / Decimal(scale_factor)
                    supplier_price_form_initial['unit_name'] = "%s" % supplier_price_profile.alternate_unit.unit.unit_name
            initial_supp_price_material_values.append(supplier_price_form_initial)
        return MaterialPartyProfileFormset(
            initial=initial_supp_price_material_values, prefix=SUPPLIER_PRICE_MATERIAL_FORMSET_PREFIX)

    @staticmethod
    def _generateMakeFormset(material_make_profiles=()):
        """

        :param material_make_profiles:
        :return:
        """
        initial_material_make_values = []
        if material_make_profiles:
            for material_make_profile in json.loads(material_make_profiles):
                material_make_form_initial = {}
                material_make_form_initial['make'] = str(material_make_profile['make'])
                material_make_form_initial['part_no'] = str(material_make_profile['mpn'])
                initial_material_make_values.append(material_make_form_initial)
        return MaterialMakeFormset(initial=initial_material_make_values, prefix=MATERIAL_MAKE_FORMSET_PREFIX)

    def _convertMaterialVOToEntity(self, material_vo, entity, user_id=None, default_make_spq=None):
        """

        :param material_vo:
        :param entity:
        :param user_id:
        :return:
        """
        entity = self._getMaterialEntityFromForm(material_vo.material_form, entity)
        entity.bill_of_materials = self.__extractBillMaterialsFromFormset(material_vo.material_formset, entity)
        new_prices, status_changed_materials = self.__extractPriceMaterialsFromFormset(
            material_vo.supplier_price_material_formset, entity,
            material_vo.material_form.cleaned_data['unit_conversion_rate'], user_id=user_id)
        entity.supplier_wise_materials_price = new_prices
        entity.specifications = self.__extractSpecificationFromFormset(material_vo.specification_formset, entity)
        entity.makes_json = self.__extractMaterialsMakeFromFormset(material_vo.material_make_formset)
        entity.alternate_units = self.__extractAlternateUnitsFromFormset(material_vo.alternate_unit_formset, entity)
        entity.last_modified_on = datetime.now()
        entity.last_modified_by = user_id
        return entity, status_changed_materials

    def __extractBillMaterialsFromFormset(self, bill_of_materials_formset, material):
        """
        :param bill_of_materials_formset:
        :param material:
        :return:
        """
        bill_of_materials = []
        for bill_material_form in bill_of_materials_formset:
            if not bill_material_form.cleaned_data['DELETE']:
                bill_material = self.master_dao.getCatalogueMaterial(
                    parent_id=material.material_id, enterprise_id=material.enterprise_id,
                    item_id=bill_material_form.cleaned_data['item_id'])
                if bill_material is None:
                    bill_material = CatalogueMaterial(
                        enterprise_id=material.enterprise_id,
                        item_id=bill_material_form.cleaned_data['item_id'])
                bill_material.quantity = bill_material_form.cleaned_data['quantity']
                bill_material.catalogue = material
                bill_of_materials.append(bill_material)

        return bill_of_materials

    def __extractAlternateUnitsFromFormset(self, alternate_unit_formset, material):
        """
        :param alternate_unit_formset:
        :param material:
        :return:
        """
        alternate_units = []
        for alternate_unit_form in alternate_unit_formset:
            if not alternate_unit_form.cleaned_data['DELETE']:
                alternate_unit = self.master_dao.getMaterialAlternateUnit(
                    item_id=material.material_id, enterprise_id=material.enterprise_id,
                    alternate_unit_id=alternate_unit_form.cleaned_data['alternate_unit_id'])
                if alternate_unit is None:
                    alternate_unit = MaterialAlternateUnit(
                        enterprise_id=material.enterprise_id,
                        alternate_unit_id=alternate_unit_form.cleaned_data['alternate_unit_id'])
                    alternate_unit.material = material
                alternate_unit.scale_factor = alternate_unit_form.cleaned_data['scale_factor']
                alternate_unit.material = material
                alternate_units.append(alternate_unit)

        return alternate_units



    def __extractMaterialsMakeFromFormset(self, materials_make_formset):
        """
        :param materials_make_formset:
        :param material:
        :return:
        """
        material_makes = []
        make_json = []
        for material_make_form in materials_make_formset:
            make_info = {}
            if not material_make_form.cleaned_data['DELETE']:
                _material_make = None
                _label = material_make_form.cleaned_data['make'].strip()
                _part_no = material_make_form.cleaned_data['part_no'].strip()
                if _label != '-NA-':
                    make_info['make'] = _label
                    make_info['mpn'] = _part_no
                    material_makes.append(_material_make)
            if make_info:
                make_json.append(make_info)
        return json.dumps(make_json)

    def __extractPriceMaterialsFromFormset(self, price_of_materials_formset=(), material=None, scale=1, user_id=None):
        """

        :param price_of_materials_formset:
        :param material:
        :param scale:
        :param user_id:
        :return:
        """
        price_of_materials = []
        status_changed_materials = []
        for price_material_form in price_of_materials_formset:
            if not price_material_form.cleaned_data['DELETE']:
                price_material = self.master_dao.getMaterialSupplierProfile(
                    item_id=material.material_id, enterprise_id=material.enterprise_id,
                    supp_id=price_material_form.cleaned_data['supp_id'],
                    effect_since=price_material_form.cleaned_data['effect_since'],
                    make_id=price_material_form.cleaned_data['make_id'],
                    is_service=price_material_form.cleaned_data['is_service'])
                logger.info("Is Price Material None: %s" % (price_material is None))
                if price_material is None:
                    price_material = MaterialSupplierProfile(
                        item_id=material.material_id, enterprise_id=material.enterprise_id,
                        supp_id=price_material_form.cleaned_data['supp_id'],
                        effect_since=price_material_form.cleaned_data['effect_since'],
                        price=price_material_form.cleaned_data['price'],
                        make_id=price_material_form.cleaned_data['make_id'],
                        is_service=price_material_form.cleaned_data['is_service'],
                        moq=price_material_form.cleaned_data['moq'],
                        alternate_unit_id=price_material_form.cleaned_data['alternate_unit_id'],
                        lead_time=price_material_form.cleaned_data['lead_time'])
                else:
                    price_material.price = Decimal(price_material_form.cleaned_data['price']) / Decimal(scale)
                price_material.effect_till = price_material_form.cleaned_data['effect_till'] if len(
                    "%s" % price_material_form.cleaned_data['effect_till']) > 0 else None
                price_material.currency_id = price_material_form.cleaned_data['currency_id']
                price_material.remarks = price_material_form.cleaned_data['remarks']
                price_material.is_primary = price_material_form.cleaned_data['is_primary']
                price_material.is_service = price_material_form.cleaned_data['is_service']
                price_material.reject_remarks = price_material_form.cleaned_data['reject_remarks']
                price_material.alternate_unit_id = price_material_form.cleaned_data['alternate_unit_id']
                price_material.moq = price_material_form.cleaned_data['moq'] if price_material_form.cleaned_data['moq'] else 0
                price_material.lead_time = price_material_form.cleaned_data['lead_time']

                if price_material.alternate_unit_id:
                    scale_factor = helper.getScaleFactor(
                        enterprise_id=price_material.enterprise_id, item_id=price_material.item_id,
                        alternate_unit_id=price_material.alternate_unit_id)
                    if scale_factor:
                        price_material.moq =round(Decimal(price_material_form.cleaned_data['moq']) * Decimal(scale_factor), 3)

                if price_material.status != int(price_material_form.cleaned_data['status']):
                    status_changed_materials.append(price_material)
                    price_material.status = int(price_material_form.cleaned_data['status'])
                    if price_material.status != 0:
                        price_material.approved_on = datetime.today()
                        price_material.approved_by = user_id

                price_of_materials.append(price_material)

        return price_of_materials, status_changed_materials

    def __extractSpecificationFromFormset(self, specification_formset=(), material=None):
        specifications = []
        for specification_form in specification_formset:
            if not specification_form.cleaned_data['DELETE']:
                specification = self.master_dao.getSpecification(
                    material_id=material.material_id, enterprise_id=material.enterprise_id,
                    parameter=specification_form.cleaned_data['parameter'])
                if specification is None:
                    specification = Specification(
                        material_id=material.material_id, enterprise_id=material.enterprise_id,
                        parameter=specification_form.cleaned_data['parameter'])
                specification.comments = specification_form.cleaned_data['comments']
                specification.inspection_method = specification_form.cleaned_data['inspection_method']
                specification.min_value = specification_form.cleaned_data['min_value']
                specification.max_value = specification_form.cleaned_data['max_value']
                specification.unit = specification_form.cleaned_data['unit']
                specification.reaction_plan = specification_form.cleaned_data['reaction_plan']
                specification.qc_critical = specification_form.cleaned_data['qc_critical']

                specifications.append(specification)
        return specifications

    def getPartyDetail(self, party_id="", enterprise_id=None):
        """

        :param party_id:
        :param enterprise_id:
        :return:
        """
        party = self.master_dao.db_session.query(Party).filter(
            Party.id == party_id, Party.enterprise_id == enterprise_id).first()
        result = {
            'id': party.id, 'code': party.code, 'name': party.name, 'contact': party.contact,
            'address_1': party.address_1, 'address_2': party.address_2, 'cst_no': party.cst_no, 'gst_no': party.gst_no,
            'tin_no': party.tin_no, 'ecc_no': party.ecc_no, 'cin_no': party.cin_no, 'tan_no': party.tan_no,
            'pan_no': party.pan_no, 'city': party.city, 'state': party.state, 'phone': party.phone,
            'email': party.email,
            'enterprise_id': party.enterprise_id, 'is_supplier': party.isSupplier(), 'is_customer': party.isCustomer(),
            'remind_duty_pass': party.remindDutyPass()}

        return result

    def notifyPendingPriceApprovalCount(
            self, enterprise_id=None, sender_id=None, increment=0, include_sender=True, code=None, type=None):
        """

        :param enterprise_id:
        :param sender_id:
        :param increment:
        :param include_sender:
        :return:
        """

        try:

            pending_supplier_price_count = self.master_dao.getPendingPriceApprovalCount(
                enterprise_id=enterprise_id) + increment
            message = None
            if pending_supplier_price_count == 1:
                message = "1 profiled price is pending for approval"
            elif pending_supplier_price_count > 1:
                message = "%s profiled prices are pending for approval" % pending_supplier_price_count
            push_notification(
                enterprise_id=enterprise_id, sender_id=sender_id, module_code="MASTERS",
                message=message, collapse_key="master_material_price", include_sender=include_sender, code=code, type=type)
        except Exception as e:
            logger.error("Notification failed... %s" % e.message)

    def notifySupplierPriceStatus(
            self, enterprise_id=None, sender_id=None, supplier_price=MaterialSupplierProfile()):
        """

        :param enterprise_id:
        :param sender_id:
        :param supplier_price:
        :return:
        """
        try:
            part_no_message = ""
            if supplier_price.make_id != DEFAULT_MAKE_ID:
                part_no_message = supplier_price.make.__repr__()
            price = '%s %.2f' % (supplier_price.supplier_price_currency.code, float(supplier_price.price))
            message = None
            if supplier_price.status == MaterialSupplierProfile.APPROVED:
                message = PUSH_NOTIFICATION['material_price_approved'] % (
                    price, supplier_price.material.name, part_no_message, supplier_price.supplier.name,
                    supplier_price.effect_since.strftime("%b %d, %Y"))
            if supplier_price.status == MaterialSupplierProfile.REJECTED:
                message = PUSH_NOTIFICATION['material_price_rejected'] % (price, supplier_price.material.name)
            if message is not None:
                push_notification(
                    enterprise_id=enterprise_id, sender_id=sender_id, module_code="MASTERS", message=message,
                    code=supplier_price.material.drawing_no)
            self.notifyPendingPriceApprovalCount(enterprise_id=enterprise_id, sender_id=sender_id, include_sender=True)
        except Exception as e:
            logger.error("Price approval Notification failed... %s " % e.message)

    def updateRateApprovalStatus(
            self, enterprise_id=None, item_id=None, supplier_id=None, price=0.0, effect_since=None, updated_since=None,
            effect_till=None, updated_till=None, status=0, approved_by=None, reject_remarks=None, remarks=None, make_id=None):
        """

        :param enterprise_id:
        :param item_id:
        :param supplier_id:
        :param price:
        :param effect_since:
        :param updated_since:
        :param effect_till:
        :param updated_till:
        :param status:
        :param approved_by:
        :param reject_remarks:
        :param remarks:
        :param make_id:
        :return:
        """
        self.master_dao.db_session.begin(subtransactions=True)
        try:
            effect_since_date = datetime.strptime(effect_since, "%d-%m-%Y")
            effect_till_date = datetime.strptime(effect_till, "%d-%m-%Y") if effect_till else None

            logger.info("Approving price for %s-%s-%s-%s-%s-%s" % (
                enterprise_id, item_id, price, supplier_id, effect_since_date, make_id))
            query = self.master_dao.db_session.query(MaterialSupplierProfile).filter(
                MaterialSupplierProfile.item_id == item_id,
                MaterialSupplierProfile.enterprise_id == enterprise_id, MaterialSupplierProfile.supp_id == supplier_id,
                MaterialSupplierProfile.effect_since == effect_since_date,
                MaterialSupplierProfile.effect_till == effect_till_date, MaterialSupplierProfile.make_id == make_id)

            supplier_price = query.first()
            supplier_price.status = status
            if updated_since is not None:
                supplier_price.effect_since = datetime.strptime(updated_since, "%d-%m-%Y").strftime("%Y-%m-%d")
            if updated_till is not None:
                supplier_price.effect_till = datetime.strptime(updated_till, "%d-%m-%Y").strftime("%Y-%m-%d")
            supplier_price.approved_by = approved_by
            supplier_price.approved_on = datetime.now()
            supplier_price.price = price
            supplier_price.material.last_modified_on = datetime.now()
            supplier_price.material.last_modified_by = approved_by
            supplier_price.remarks = remarks
            supplier_price.reject_remarks = reject_remarks

            self.master_dao.db_session.add(supplier_price)
            self.master_dao.db_session.commit()
            self.master_dao.db_session.refresh(supplier_price)
            self.notifySupplierPriceStatus(
                enterprise_id=enterprise_id, sender_id=approved_by, supplier_price=supplier_price)
            return True
        except:
            self.master_dao.db_session.rollback()
            raise

    def savePartyContactDetails(self, enterprise_id=None, party_contact_details=None, user_id=None, party_id=None):
        """

        :param enterprise_id:
        :param party_contact_details:
        :param user_id:
        :param party_id:
        :return:
        """
        try:
            logger.info("Saving Party Contact Details...")
            primary_contact_id = ""
            for contact in party_contact_details:
                if contact["is_deleted"] == 0:
                    contact_details = self.updatePartyContactDetails(
                        enterprise_id=enterprise_id, party_id=party_id, sequence_id=contact["sequence_id"],
                        name=contact["name"], email=contact["email"], phone_no=contact["phone_no"],
                        fax_no=contact["fax_no"], is_whatsapp=contact["is_whatsapp"], user_id=user_id)
                    self.updatePartyContactMap(
                        enterprise_id=enterprise_id, party_id=party_id, contact_id=contact_details.id,
                        sequence_id=contact["sequence_id"])
                    if contact["sequence_id"] == 1:
                        primary_contact_id = contact_details.id
                else:
                    self.deletePartyContact(enterprise_id=enterprise_id, party_id=party_id, sequence_id=contact["sequence_id"])
            return primary_contact_id
        except Exception as e:
            logger.exception("Party Contact Details update failed - %s" % e.message)
            raise

    def updatePartyContactDetails(
            self, enterprise_id, party_id=None, sequence_id=None, name=None, email=None, phone_no=None, fax_no=None,
            is_whatsapp=None, user_id=None):
        """

        :param enterprise_id:
        :param party_id:
        :param sequence_id:
        :param name:
        :param email:
        :param phone_no:
        :param fax_no:
        :param is_whatsapp:
        :param user_id:
        :return:
        """
        logger.info("Updating Party Contact Details...")
        self.master_dao.db_session.begin(subtransactions=True)
        try:
            party_contact_detail = self.master_dao.getPartyContactDetail(
                enterprise_id=enterprise_id, party_id=party_id, sequence_id=sequence_id)

            if not party_contact_detail:
                contact_detail = ContactDetails(
                    name=name, email=email, phone_no=phone_no, fax_no=fax_no,
                    is_whatsapp=is_whatsapp, created_by=user_id, last_modified_by=user_id, enterprise_id=enterprise_id)
            else:
                contact_detail = party_contact_detail.contact
                contact_detail.name = name
                contact_detail.email = email
                contact_detail.phone_no = phone_no
                contact_detail.fax_no = fax_no
                contact_detail.is_whatsapp = is_whatsapp
                contact_detail.last_modified_by = user_id
                contact_detail.enterprise_id = enterprise_id

            logger.info("Fetched Party Contact Details - %s" % contact_detail)
            self.master_dao.db_session.add(contact_detail)
            logger.debug('Save party dirty %s' % self.master_dao.db_session.dirty)
            self.master_dao.db_session.commit()
            self.master_dao.db_session.refresh(contact_detail)
            return contact_detail
        except Exception as e:
            self.master_dao.db_session.rollback()
            logger.exception("Failed to update Party Contact detail - %s" % e.message)
            raise

    def updatePartyContactMap(self, enterprise_id=None, party_id=None, contact_id=None, sequence_id=None):
        """

        :param enterprise_id:
        :param party_id:
        :param contact_id:
        :param sequence_id:
        :return:
        """
        logger.info("Updating Party Contact Map...")
        self.master_dao.db_session.begin(subtransactions=True)
        try:
            party_contact_detail = self.master_dao.getPartyContactDetail(
                enterprise_id=enterprise_id, party_id=party_id, sequence_id=sequence_id)
            if not party_contact_detail:
                party_contact_detail = PartyContactMap(
                    enterprise_id=enterprise_id, party_id=party_id, contact_id=contact_id, sequence_id=sequence_id)
            else:
                party_contact_detail.enterprise_id = enterprise_id
                party_contact_detail.party_id = party_id
                party_contact_detail.contact_id = contact_id
                party_contact_detail.sequence_id = sequence_id
            logger.info("Fetched Party Contact Map - %s" % party_contact_detail)
            self.master_dao.db_session.add(party_contact_detail)
            logger.debug('Save Party dirty %s' % self.master_dao.db_session.dirty)
            self.master_dao.db_session.commit()
            self.master_dao.db_session.refresh(party_contact_detail)
        except Exception as e:
            self.master_dao.db_session.rollback()
            logger.exception("Failed to updated Party contact Map - %s" % e.message)
            raise

    def getContactDetails(self, enterprise_id=None, party_id=None, sequence_id=None):
        """

        :param enterprise_id:
        :param party_id:
        :param sequence_id:
        :return:
        """
        logger.info("Fetch the party contact details...")
        party_contact_details = []
        try:
            if sequence_id:
                party_contact_map = self.master_dao.db_session.query(
                    PartyContactMap).filter(
                    PartyContactMap.enterprise_id == enterprise_id, PartyContactMap.party_id == party_id,
                    PartyContactMap.sequence_id == sequence_id).order_by(PartyContactMap.sequence_id).first()
                party_contact_details_dict = {
                    "sequence_id": party_contact_map.sequence_id, "name": party_contact_map.contact.name,
                    "phone_no": party_contact_map.contact.phone_no, "email": party_contact_map.contact.email,
                    "fax_no": party_contact_map.contact.fax_no, "is_whatsapp": party_contact_map.contact.is_whatsapp}
                party_contact_details.append(party_contact_details_dict)
            else:
                party_contact_map = self.master_dao.db_session.query(
                    PartyContactMap).filter(
                    PartyContactMap.enterprise_id == enterprise_id, PartyContactMap.party_id == party_id).order_by(
                    PartyContactMap.sequence_id).all()
                for contact_map in party_contact_map:
                    party_contact_details_dict = {
                        "sequence_id": contact_map.sequence_id, "name": contact_map.contact.name,
                        "phone_no": contact_map.contact.phone_no, "email": contact_map.contact.email,
                        "fax_no": contact_map.contact.fax_no, "is_whatsapp": contact_map.contact.is_whatsapp}
                    party_contact_details.append(party_contact_details_dict)
        except Exception as e:
            logger.exception("Failed to fetch Party contact details - %s" % e.message)
            raise
        return party_contact_details

    def deletePartyContact(self, enterprise_id=None, party_id=None, sequence_id=None):
        """

        :param enterprise_id:
        :param party_id:
        :param sequence_id:
        :return:
        """
        logger.info('Deleting Party Contact Detail...')
        self.master_dao.db_session.begin(subtransactions=True)
        try:
            contact_id = self.master_dao.db_session.query(PartyContactMap.contact_id).filter(
                PartyContactMap.enterprise_id == enterprise_id, PartyContactMap.party_id == party_id,
                PartyContactMap.sequence_id == sequence_id).first()
            if contact_id is not None:
                self.master_dao.db_session.query(ContactDetails).filter(ContactDetails.id == contact_id[0]).delete()
            self.master_dao.db_session.commit()
        except Exception as e:
            self.master_dao.db_session.rollback()
            logger.exception(("Failed to delete Party Contact - %s" % e.message))

    def getPartyRegistrationDetails(self, enterprise_id=None, party_id=None, label_id=None):
        """

        :param enterprise_id:
        :param party_id:
        :param label_id:
        :return:
        """
        try:
            if label_id:
                party_registration_detail = self.master_dao.db_session.query(PartyRegistrationDetail).filter(
                    PartyRegistrationDetail.enterprise_id == enterprise_id, PartyRegistrationDetail.party_id == party_id,
                    PartyRegistrationDetail.label_id == label_id).first()
            else:
                party_registration_detail = self.master_dao.db_session.query(PartyRegistrationDetail).filter(
                    PartyRegistrationDetail.enterprise_id == enterprise_id, PartyRegistrationDetail.party_id == party_id).order_by(
                    PartyRegistrationDetail.label_id).all()

            return party_registration_detail
        except Exception as e:
            logger.error("Fetch Party Registration Detail failed... %s " % e.message)

    def getGSTCategory(self):
        gst_category = self.master_dao.db_session.query(GSTCategory).all()
        return gst_category

    def getCountries(self):
        countries = self.master_dao.db_session.query(Country).order_by(Country.name).all()
        return countries

    def getEnterprise(self, enterprise_id=None):
        enterprise = self.master_dao.db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
        return enterprise

    def getCountryByCode(self, country_code=None):
        country = self.master_dao.db_session.query(
            Country).filter(Country.code == country_code).order_by(Country.name).first()
        return country

    def savePartyRegistration(self, enterprise_id=None, party_id=None, party_reg_detail=None, user_id=None):
        """

        :param enterprise_id:
        :param party_id:
        :param party_reg_detail:
        :param user_id:
        :return:
        """
        try:
            logger.info("Updating Party Registration Details...")
            updated_reg_detail = []

            for reg_item in party_reg_detail:
                if reg_item["is_deleted"] == 0:
                    updated_reg_detail.append(reg_item)
                    self.updatePartyRegistration(
                        enterprise_id=enterprise_id, party_id=party_id, label_id=reg_item["label_id"], label=reg_item["label"],
                        details=reg_item["details"], user_id=user_id)
                else:
                    self.deletePartyRegistration(enterprise_id=enterprise_id, party_id=party_id, label_id=reg_item["label_id"])
            return updated_reg_detail
        except Exception as e:
            logger.exception("Party Registration Update failed - %s" % e.message)
            raise

    def updatePartyRegistration(self, enterprise_id=None, party_id=None, label_id=None, label=None, details=None, user_id=None):
        """

        :param enterprise_id:
        :param party_id:
        :param label_id:
        :param label:
        :param details:
        :param user_id:
        :return:
        """
        logger.info("Party Registration detail Update Service Call..")
        self.master_dao.db_session.begin(subtransactions=True)
        try:
            reg_detail = self.getPartyRegistrationDetails(enterprise_id=enterprise_id, party_id=party_id, label_id=label_id)
            if not reg_detail:
                reg_detail = PartyRegistrationDetail(
                    enterprise_id=enterprise_id, party_id=party_id, label_id=label_id, label=label, details=details,
                    created_by=user_id, last_modified_by=user_id)
            else:
                reg_detail.label = label
                reg_detail.details = details
                reg_detail.last_modified_by = user_id

            logger.info("Enterprise Party fetched - %s" % reg_detail)
            self.master_dao.db_session.add(reg_detail)
            logger.debug('Save enterprise dirty %s' % self.master_dao.db_session.dirty)
            self.master_dao.db_session.commit()
            self.master_dao.db_session.refresh(reg_detail)
        except Exception as e:
            self.master_dao.db_session.rollback()
            logger.exception("Update Party Registration detail Failed - %s" % e.message)
            raise

    def deletePartyRegistration(self, enterprise_id=None, party_id=None, label_id=None):
        """

        :param enterprise_id:
        :param party_id:
        :param label_id:
        :return:
        """
        logger.info('Party Registration detail Delete Service Call..')
        self.master_dao.db_session.begin(subtransactions=True)
        try:
            self.master_dao.db_session.query(PartyRegistrationDetail).filter(
                PartyRegistrationDetail.enterprise_id == enterprise_id, PartyRegistrationDetail.party_id == party_id,
                PartyRegistrationDetail.label_id == label_id).delete()
            self.master_dao.db_session.commit()
        except Exception as e:
            self.master_dao.db_session.rollback()
            logger.exception("Delete Party Registration Failed - %s" % e.message)

    def recursive_bom_material_list(self, item_id=None, enterprise_id=None, bom_materials_list=None):
        """This is a recursive function
        to find the parent id of items"""
        master_service = MasterService()
        participating_bom_material_list = master_service.master_dao.getPBOMMaterialList(
            item_id=item_id, enterprise_id=enterprise_id)
        if len(participating_bom_material_list) > 0:
            for item in participating_bom_material_list:
                item_id = {"item_id": item[5]}
                if item_id not in bom_materials_list:
                    bom_materials_list.append(item_id)
                    self.recursive_bom_material_list(
                        item_id=item[5], enterprise_id=enterprise_id, bom_materials_list=bom_materials_list)
            return bom_materials_list
        else:
            return bom_materials_list

    def get_catalogue_materials(self, enterprise_id, cat_code, cat_item_qty, depth=0):
        materials = {}
        try:
            if depth >= 6:
                return {}  # Termination condition
            master_dao = MasterDAO()
            catalogues = master_dao.getCatalogues(cat_code, enterprise_id)
            for catalogue in catalogues:
                catalogue_material = self.get_catalogue_material(enterprise_id, catalogue, cat_item_qty)
                materials[catalogue.item_id] = catalogue_material
        except Exception as e:
            logger.exception("Failed to Get catalogue Materials - %s" % e)
        return materials

    def get_catalogue_material(self, enterprise_id, catalogue, cat_item_qty):
        try:
            master_dao = MasterDAO()
            catalogues_child = master_dao.getCatalogues(catalogue.item_id, enterprise_id)
            supp_price = self.getSupplierPrice(enterprise_id, catalogue.item_id)
            sub_child_materials = {}
            if catalogues_child:
                for child_catalogue in catalogues_child:
                    sub_child_material = self.get_catalogue_material(enterprise_id, child_catalogue, cat_item_qty)
                    sub_child_materials[child_catalogue.item_id] = sub_child_material
            return {
                "name": catalogue.name,
                "qty": float(catalogue.quantity * cat_item_qty),
                "drawing_no": catalogue.drawing_no,
                "unit_name": catalogue.unit_name,
                "hasChildren": bool(sub_child_materials),
                "store_price": float(supp_price[0]['stored_price']) if supp_price and supp_price[0]['stored_price'] else 0,
                "item_id": catalogue.item_id,
                "cat_code": catalogue.parent_id,
                "sub_child_materials": sub_child_materials,
                "party_name": supp_price[0]['party_name'] if supp_price and supp_price[0]['party_name'] else "-NA-",
                "purchase_Material_Price": float(supp_price[0]['purchase_price']) if supp_price and supp_price[0]['purchase_price'] else "-NA-",
                "purchase_currency_name": supp_price[0]['purchase_currency_name'] if supp_price and supp_price[0]['purchase_currency_name'] else "-NA-",
                "purchase_currency_code": supp_price[0]['purchase_currency_code'] if supp_price and supp_price[0]['purchase_currency_code'] else "-NA-",
                "approved_price": float(supp_price[0]['approved_price']) if supp_price and supp_price[0]['approved_price']  else "-NA-",
                "approved_currency_name": supp_price[0]['approved_currency_name'] if supp_price and supp_price[0]['approved_currency_name'] else "-NA-",
                "approved_currency_code": supp_price[0]['approved_currency_code'] if supp_price and supp_price[0]['approved_currency_code'] else "-NA-",

            }
        except Exception as e:
            logger.info("Failed to catalogue material: %s", e, exc_info=True)

    def getSupplierPrice(self, enterprise_id, item_id):
        query_data = []
        try:
            totals_query = """SELECT 
                                mp.stored_price, 
                                mp.purchase_price,
                                mp.approved_price,
                                pc.currency_name AS purchase_currency_name,
                                pc.code AS purchase_currency_code,
                                ac.currency_name AS approved_currency_name,
                                ac.code AS approved_currency_code,
                                pm.party_name 
                            FROM 
                                material_pricing AS mp 
                            JOIN 
                                currency AS pc ON mp.purchase_currency_id = pc.id 
                            JOIN 
                                currency AS ac ON mp.approved_currency_id = ac.id
                            JOIN 
                                party_master AS pm ON pm.party_id = mp.supplier_id 
                            WHERE 
                                mp.enterprise_id = {enterprise_id} 
                                AND mp.item_id = {item_id};
                                """.format(enterprise_id=enterprise_id, item_id=item_id)
            query_data = executeQuery(totals_query, as_dict=True)
        except Exception as e:
            logger.exception("Failed to supplier price -  %s" % e.message)
        return query_data


class LocationService(DataAccessObject):

    def get_location(self, location_id):
        location_details = None
        try:
            location_details = self.db_session.query(LocationMaster).filter(
                LocationMaster.id == location_id).first()
        except Exception as e:
            logger.exception('Failed to get the location details - %s' % e.message)
        return location_details

    def get_user_locations(self, user_id, enterprise_id):
        location_list = []
        try:
            location_list = self.db_session.query(LocationMaster).join(UserLocationMap,
                LocationMaster.id == UserLocationMap.location_id).filter(
                UserLocationMap.user_id == user_id,
                UserLocationMap.enterprise_id == enterprise_id,
                UserLocationMap.status == 1,
            ).all()
        except Exception as e:
            logger.exception("Failed to get the user locations: %s", e)
        return location_list

    def get_all_locations(self, enterprise_id):
        location_list = []
        try:
            location_list = self.db_session.query(LocationMaster).filter(
                LocationMaster.enterprise_id == enterprise_id).all()
        except Exception as e:
            logger.exception("Failed to get the user locations: %s", e)
        return location_list

    def update_default_location(self, enterprise_id):
        result = False
        try:
            default_location = self.get_default_location(enterprise_id=enterprise_id)
            if default_location:
                self.db_session.begin(subtransactions=True)
                default_location.is_default = 0
                self.db_session.add(default_location)
                self.db_session.commit()
                result = True
        except Exception as e:
            self.db_session.rollback()
            logger.exception("Failed to update the user location: %s", e)
        finally:
            self.db_session.close()
        return result

    def get_default_location(self, enterprise_id=None):
        default_location = None
        try:
            default_location = self.db_session.query(LocationMaster).filter(
                LocationMaster.enterprise_id == enterprise_id,
                LocationMaster.is_default == 1).first()
        except Exception as e:
            logger.exception("Failed to get default location : %s", e)
        finally:
            self.db_session.close()
        return default_location

class MrpService:
    def bom_checking_from_mrp_materials(self,enterprise_id=None, item_id=None, pp_id=None, parent_id=None):
        query = """
    	SELECT allocated_qty FROM mrp_materials where enterprise_id = {enterprise_id} AND item_id = {item_id} and pp_id = {pp_id} and parent_id = {parent_id};
    	""".format(enterprise_id=enterprise_id, item_id=item_id, pp_id=pp_id, parent_id=parent_id)
        allocated_value = executeQuery(query, as_dict=True)
        return False if allocated_value and allocated_value[0].get("allocated_qty", 0) != 0 else True