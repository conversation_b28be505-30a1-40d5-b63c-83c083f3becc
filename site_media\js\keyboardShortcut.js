$(document).keyup(function (e) {
        e.preventDefault();
        var keyCode = e.keyCode ? e.keyCode : e.which
       if (e.altKey && e.keyCode == 73)  {
            window.location.href="/erp/sales/invoice";
       }
       else if (e.alt<PERSON>ey && e.keyCode == 75) {
            window.location.href="/erp/sales/oa/";
       }
       else if(e.altKey && e.keyCode == 74 ) {
            window.location.href="/erp/accounts/voucher/";
       }
       else if(e.altKey && e.keyCode == 79) {
            window.location.href="/erp/purchase/po/#tab2";
       }
       //else if(e.metaKey && e.keyCode == 67) {
        // 	$("#other-config").addClass('open');
         //	 e.preventDefault();
      // }
       else if(e.metaKey && e.keyCode == 88) {
	        window.location.href="/erp/logout/";
       }
       else if(e.meta<PERSON>ey && e.keyCode == 190){
            e.preventDefault();
            var currentId= $(".slide_container_part.selected").attr("id");
            $(".slide_container_part.selected").removeClass("selected");
            $("#"+currentId).click();
            setTimeout(function(){
                $("#"+currentId).addClass("selected");
            },10);
           }
           return false;
   });

