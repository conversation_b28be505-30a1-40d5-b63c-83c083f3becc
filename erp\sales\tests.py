import datetime

from django.test import TestCase

from erp.sales import logger
from erp.sales.backend import InvoiceService
from erp.sales.oa_backend import OrderAcknowledgementService
from erp.sales.se_backend import SalesEstimateDAO


class TestUM(TestCase):

	def setUp(self):
		pass

	def tearDown(self):
		pass

	def test_inv_qty_against_DC_pass(self):
		c = InvoiceService().getInvoicedItemQuantity(item_id=1416, make_id=54, enterprise_id=102, dc_id=36734)
		logger.info("inv_qty_against_DC_pass:%s" % c)
		self.assertNotEquals(c, 0)

	def test_inv_qty_against_DC_fail(self):
		c = InvoiceService().getInvoicedItemQuantity(item_id=1235, make_id=54, enterprise_id=102, dc_id=36734)
		logger.info("inv_qty_against_DC_fail:%s" % c)
		self.assertEquals(c, 0)

	def saveOaFromJson_pass(self):
		materials = [{
			u'mat_type': 0, u'item_code': 'PAVITHRA', u'quantity': 2, u'price': 120, u'remarks': None,
			u'enterprise_id': 102, u'make_id': 1, u'is_faulty': False}, {
			u'mat_type': 1, u'item_name': 'Non_Stock_Mat', u'quantity': 2, u'price': 120, u'remarks': None,
			u'enterprise_id': 102, u'unit_id': 1}]
		oa_dict = {'party_id': 456, 'oa_type': "Job", 'currency_code': 'INR', 'materials': materials}
		c = OrderAcknowledgementService().saveOaFromJson(enterprise_id=102, user_id=192, oa_dict=oa_dict)
		logger.info("saveOaFromJson_pass:%s" % c)
		self.assertEquals(len(c), 0)

	def test_oaStatus(self):
		result = OrderAcknowledgementService().getOAWithStatus(enterprise_id=102)
		result_by_query = OrderAcknowledgementService().getOADeliveryStatus(enterprise_id=102)
		logger.info("OA STATUS %s" % result)
		logger.info("OA STATUS BY QUERY %s" % result_by_query)
		self.assertNotEqual(result['oa_pending'], 0, "OA status cannot be calculated")

	def test_getOATotal(self):
		obj = SalesEstimateDAO()
		c = obj.getOATotal(enterprise_id=None, se_id=None)
		self.assertEquals(c, None)

	def test1_getOATotal(self):
		obj = SalesEstimateDAO()
		c = obj.getOATotal(enterprise_id=102, se_id=None)
		self.assertEquals(len(c), 1)

	def test_getInvoiceTotal(self):
		obj = SalesEstimateDAO()
		c = obj.getInvoiceTotal(enterprise_id=None, se_id=None)
		self.assertEquals(c, None)

	def test1_getInvoiceTotal(self):
		obj = SalesEstimateDAO()
		c = obj.getInvoiceTotal(enterprise_id=102, se_id=None)
		self.assertEquals(len(c), 1)
