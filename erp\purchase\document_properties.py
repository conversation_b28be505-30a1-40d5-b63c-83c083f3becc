from reportlab.lib import colors
from reportlab.platypus import TableStyle

__author__ = 'kalaivanan'

# PDF File path
TARGET_PATH = '/site_media/tmp/po.pdf'
DEFAULT_PO_PDF_PATH = '/site_media/docs/po.pdf'
PO_PDF_PATH = '/site_media/tmp/po_%s.pdf'
ICD_PO_PDF_PATH = '/site_media/tmp/grn_po_%s.pdf'
GRN_INVOICE_DOC_PATH = '/site_media/tmp/grn_inv_%s.%s'
ICD_GRN_INVOICE_DOC_PATH = '/site_media/tmp/grn_%s.pdf'
CANCEL_WATERMARK_DOC_PATH = '/site_media/docs/cancel_watermark.pdf'
DUMMY_DOC_PATH = '/site_media/tmp/dummy_%s.pdf'
CANCEL_WATERMARK_IMG_PATH = '/site_media/images/po_cancel_watermark.png'
INVOICE_PDF_PATH = '/site_media/tmp/invoice_%s.pdf'
ICD_INVOICE_PDF_PATH = '/site_media/tmp/grn_invoice_%s.pdf'

# Logo image location
LOGO_FILE_PATH = '/site_media/images/schnell-logo.png'

# Labels
VENDOR_NAME_LABEL = 'Vendor Name'
VENDOR_CODE_LABEL = 'Vendor Code'
VENDOR_PHONE_LABEL = 'Phone No.'

SL_NO_LABEL = 'S.No'
DRAWING_NO_LABEL = 'Drawing No'
DESCRIPTION_LABEL = 'Description'
ORDER_QTY_LABEL = 'Qty'
UNIT_LABEL = 'UoM'
UNIT_PRICE_LABEL = 'Price'
TOTAL_PRICE_LABEL = 'Total'
DISCOUNT_LABEL = 'Disc.'
CGST_LABEL = 'CGST'
SGST_LABEL = 'SGST'
IGST_LABEL = 'IGST'
RATE_LABEL = 'Rate'
VALUE_LABEL = 'Value'

PO_NO_LABEL = 'P.O. No.'
PO_DATE_LABEL = 'P.O. Date'
INDENT_NO_LABEL = 'Indent No'
PAGE_LABEL = 'Page'
CURRENCY_LABEL = 'Currency'
QUOTN_REF_LABEL = 'Quotn. Ref'
DATE_LABEL = 'Quotn. Date'

ANNEXURE_REF_LABEL = 'AS PER ANNEXURE TO PO NO:'
ANNEXURE_HEADER = 'ANNEXURE TO PURCHASE ORDER %s DATED %s'
ANNEXURE_FOOTER = 'For PPP (Schnell)'

TOTAL_LABEL = 'Total'
SUB_TOTAL_LABEL = 'Sub-Total'
GRAND_TOTAL_LABEL = 'Grand Total'

TERMS_LABEL = 'Terms and Conditions'
DRAWING_REF_LABEL = 'Drawing Ref'
PAYMENT_TERMS_LABEL = 'Payment Terms'
DESPATCH_LABEL = 'Mode of Despatch'
DELIVERY_LABEL = 'Delivery Due on'
SP_INSTRUCTIONS_LABEL = 'Special Instructions'

NOTES_LABEL = 'Notes :'

VENDOR_ACK_LABEL = 'Vendor Acknowledgement'
AUTH_SIGN_LABEL = 'Authorised Signatory'
BILL_TO_LABEL = 'Bill To:-'
SHIP_TO_LABEL = 'Ship To:- '
PREPARED_LABEL = 'Prepared & Checked By'

PAGE_TOTAL = 'Page Total: %d'
TOTAL_PAGES = 'Total Pages : %d'
# Styles
COMPANY_STYLE = 'H4'
FORM_NO_STYLE = 'H4'
FORM_NAME_STYLE = 'H2_CENTER'
IT_STYLE = 'TINY'
LABEL_STYLE = 'H4'
SMALL_LABEL_STYLE = 'H5'
SPL_DETAILS_STYLE = 'REGULAR_BOLD_L10'
DETAILS_STYLE = 'REGULAR_L5'
SUBJECT_STYLE = 'REGULAR_BOLD_CENTER_L5'
SMALL_FONT = 'SMALL'
DETAILS_ITEM_LIST = 'REGULAR_L10'
DETAILS_STYLE_RIGHT = 'REGULAR_CENTER_L5'

# Company particulars
COMPANY_NAME = ''
FORM_NO = 'FORM No. MM-01 REV#2'
PO = 'PURCHASE ORDER'
JO = 'JOB ORDER'
TIN_NO_LABEL = 'TIN'
CST_NO_LABEL = 'CST NO'
GST_NO_LABEL = 'GSTIN'
PAN_LABEL = 'PAN'
CIN_LABEL = 'CIN'

MAIL_GREETING = 'Dear Sir/Madam,'
MAIL_SUBJECT = 'Sub: Purchase Order - Reg'
JOB_MAIL_SUBJECT = 'Sub: Job Order - Reg'

MATERIAL_REVISION_INFO = 'Rev No:0 Rev Date: 01/Jan/2006\n'

_HEADER_DETAILS_TABLE_STYLE = TableStyle([('ALIGN', (1, 1), (-1, -1), 'LEFT'),
                                          ('TEXTCOLOR', (1, 1), (-2, -2), colors.red),
                                          ('VALIGN', (0, 0), (0, -1), 'TOP'),
                                          ('TEXTCOLOR', (0, 0), (0, -1), colors.blue),
                                          ('ALIGN', (0, -1), (-1, -1), 'CENTER'),
                                          ('VALIGN', (0, -1), (-1, -1), 'TOP'),
                                          ('TEXTCOLOR', (0, -1), (-1, -1), colors.green),
                                          ('LEFTPADDING', (0, 0), (-1, -1), 0),
                                          ])

MAIL_CONTACT = 'E-mail:'
