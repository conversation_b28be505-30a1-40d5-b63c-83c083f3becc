from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.pdfmetrics import registerFontFamily
from reportlab.pdfbase.ttfonts import TTFont

from util import helper


def registeredFonts():
	pdfmetrics.registerFont(TTFont('times', helper.getAbsolutePath('/site_media/fonts/times.ttf')))
	pdfmetrics.registerFont(TTFont('times_bold', helper.getAbsolutePath('/site_media/fonts/timesbd.ttf')))
	pdfmetrics.registerFont(TTFont('times_italic', helper.getAbsolutePath('/site_media/fonts/timesi.ttf')))
	pdfmetrics.registerFont(
		TTFont('times_bold_italic', helper.getAbsolutePath('/site_media/fonts/timesbi.ttf')))

	pdfmetrics.registerFont(TTFont('roboto', helper.getAbsolutePath('/site_media/fonts/roboto.regular.ttf')))
	pdfmetrics.registerFont(TTFont('roboto_bold', helper.getAbsolutePath('/site_media/fonts/roboto.bold.ttf')))
	pdfmetrics.registerFont(TTFont('roboto_italic', helper.getAbsolutePath('/site_media/fonts/roboto.italic.ttf')))
	pdfmetrics.registerFont(
		TTFont('roboto_bold_italic', helper.getAbsolutePath('/site_media/fonts/roboto.bold-italic.ttf')))

	pdfmetrics.registerFont(TTFont('ubuntu', helper.getAbsolutePath('/site_media/fonts/Ubuntu-Regular.ttf')))
	pdfmetrics.registerFont(TTFont('ubuntu_bold', helper.getAbsolutePath('/site_media/fonts/Ubuntu-Bold.ttf')))
	pdfmetrics.registerFont(TTFont('ubuntu_italic', helper.getAbsolutePath('/site_media/fonts/Ubuntu-Italic.ttf')))
	pdfmetrics.registerFont(
		TTFont('ubuntu_bold_italic', helper.getAbsolutePath('/site_media/fonts/Ubuntu-BoldItalic.ttf')))

	pdfmetrics.registerFont(TTFont('cosmicsans', helper.getAbsolutePath('/site_media/fonts/comicsansms3.ttf')))
	pdfmetrics.registerFont(TTFont('cosmicsans_bold', helper.getAbsolutePath('/site_media/fonts/comicsansbold.ttf')))
	pdfmetrics.registerFont(TTFont('cosmicsans_italic', helper.getAbsolutePath('/site_media/fonts/comicitalic.ttf')))
	pdfmetrics.registerFont(
		TTFont('cosmicsans_bold_italic', helper.getAbsolutePath('/site_media/fonts/comicbolditalic.ttf')))

	pdfmetrics.registerFont(TTFont('dejavu', helper.getAbsolutePath('/site_media/fonts/DejaVuSans.ttf')))
	pdfmetrics.registerFont(TTFont('dejavu_bold', helper.getAbsolutePath('/site_media/fonts/DejaVuSans-Bold.ttf')))
	pdfmetrics.registerFont(TTFont('dejavu_italic', helper.getAbsolutePath('/site_media/fonts/DejaVuSans-Oblique.ttf')))
	pdfmetrics.registerFont(
		TTFont('dejavu_bold_italic', helper.getAbsolutePath('/site_media/fonts/DejaVuSans-BoldOblique.ttf')))

	pdfmetrics.registerFont(TTFont('cour', helper.getAbsolutePath('/site_media/fonts/cour.ttf')))
	pdfmetrics.registerFont(TTFont('cour_bold', helper.getAbsolutePath('/site_media/fonts/courbd.ttf')))
	pdfmetrics.registerFont(TTFont('cour_italic', helper.getAbsolutePath('/site_media/fonts/couri.ttf')))
	pdfmetrics.registerFont(
		TTFont('cour_bold_italic', helper.getAbsolutePath('/site_media/fonts/courbi.ttf')))

	pdfmetrics.registerFont(TTFont('verdana', helper.getAbsolutePath('/site_media/fonts/verdana.ttf')))
	pdfmetrics.registerFont(TTFont('verdana_bold', helper.getAbsolutePath('/site_media/fonts/verdanab.ttf')))
	pdfmetrics.registerFont(TTFont('verdana_italic', helper.getAbsolutePath('/site_media/fonts/verdanai.ttf')))
	pdfmetrics.registerFont(
		TTFont('verdana_bold_italic', helper.getAbsolutePath('/site_media/fonts/verdanabi.ttf')))

	pdfmetrics.registerFont(TTFont('open_sans', helper.getAbsolutePath('/site_media/fonts/open-sans-Regular.ttf')))
	pdfmetrics.registerFont(TTFont('open_sans_bold', helper.getAbsolutePath('/site_media/fonts/open-sans.bold.ttf')))
	pdfmetrics.registerFont(TTFont('open_sans_italic', helper.getAbsolutePath('/site_media/fonts/open-sans.italic.ttf')))
	pdfmetrics.registerFont(
		TTFont('open_sans_bold_italic', helper.getAbsolutePath('/site_media/fonts/open-sans.bold-italic.ttf')))

	pdfmetrics.registerFont(TTFont('tahoma', helper.getAbsolutePath('/site_media/fonts/tahoma.ttf')))
	pdfmetrics.registerFont(TTFont('tahoma_bold', helper.getAbsolutePath('/site_media/fonts/tahomabd.ttf')))
	pdfmetrics.registerFont(
		TTFont('tahoma_italic', helper.getAbsolutePath('/site_media/fonts/tahoma_italic.ttf')))
	pdfmetrics.registerFont(
		TTFont('tahoma_bold_italic', helper.getAbsolutePath('/site_media/fonts/tahoma_bolditalic.ttf')))

	registeredFontFamily()


def registeredFontFamily():
	registerFontFamily('Times New Roman', normal='times', bold='times_bold', italic='times_italic',
	                   boldItalic='times_bold_italic')
	registerFontFamily('roboto', normal='roboto', bold='roboto_bold', italic='roboto_italic',
	                   boldItalic='roboto_bold_italic')
	registerFontFamily('ubuntu', normal='ubuntu', bold='ubuntu_bold', italic='ubuntu_italic',
	                   boldItalic='ubuntu_bold_italic')
	registerFontFamily('comic sans', normal='cosmicsans', bold='cosmicsans_bold', italic='cosmicsans_italic',
	                   boldItalic='cosmicsans_bold_italic')
	registerFontFamily('dejavu sans', normal='dejavu', bold='dejavu_bold', italic='dejavu_italic',
	                   boldItalic='dejavu_bold_italic')
	registerFontFamily('courier new', normal='cour', bold='cour_bold', italic='cour_italic',
	                   boldItalic='cour_bold_italic')
	registerFontFamily('verdana', normal='verdana', bold='verdana_bold', italic='verdana_italic',
	                   boldItalic='verdana_bold_italic')
	registerFontFamily('open sans', normal='open_sans', bold='open_sans_bold', italic='open_sans_italic',
	                   boldItalic='open_sans_bold_italic')
	registerFontFamily('tahoma', normal='tahoma', bold='tahoma_bold', italic='tahoma_italic',
	                   boldItalic='tahoma_bold_italic')