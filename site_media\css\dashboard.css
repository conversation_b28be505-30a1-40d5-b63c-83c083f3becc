/* GREEN */
.dashboard_table_green {
    background: rgba(93, 183, 93, 0.15);
}

.table.dashboard_table_green tr th {
    background: rgba(93, 183, 93, 0.7) !important;
    color: #fff !important;
}

.dashboard_table_green td {
    color: #376d37 !important;
}

/* RED */
.dashboard_table_red {
    background: rgba(217, 84, 79,0.2);
}

.table.dashboard_table_red tr th {
    background: rgba(217, 84, 79,0.7) !important;
    color: #fff !important;
}

.dashboard_table_red td {
    color: #d9544f;
}

/* BLUE */
.dashboard_table_blue {
    background: rgba(52, 122, 184, 0.2);
    margin-bottom: 0;
}

.table.dashboard_table_blue tr th{
    background-color: #347ab8 !important;
    color:  #fff !important;
}

.dashboard_table_blue td{
    border: solid 1px rgba(52, 122, 184, 0.3) !important;
    color: rgb(52, 122, 184);
    font-size: 13px;
}

.dashboard-status-box {
    background: rgba(239, 173, 77,0.2);
    color: #FF8C00;
    border: 1px solid #ccc;
}

.dashboard-status-box .status-header-text {
    font-size: 16px;
    color : #333;
}

.dashboard-status-box .status-header-value {
    font-size: 20px;
    float: right;
    color : #333;
}

.dashboard-status-box table th,
.dashboard-status-box table td {
    border-bottom: 1px solid #ccc;
    color: #FF8C00;
    font-size: 14px;
    padding:  15px !important;
}

.dashboard-status-box table tr:last-child td {
    border-bottom: none;
}

.dashboard-status-box table td.text-right,
.dashboard-status-box table td.text-center {
    color: #50585c;
}

.dashboard-status-box .status-table-header {
    background:#fff;
}

.dashboard-status-box .status-table-header th{
    border: none !important;
}

.dashboard-status-box-heading {
    width: 420px;
}

.dashboard-status-box-btn {
    border:  none;
    background: transparent;
    outline: none;
}

.dashboard-status-box-content:hover {
    background: rgba(239, 173, 77,0.21);
}