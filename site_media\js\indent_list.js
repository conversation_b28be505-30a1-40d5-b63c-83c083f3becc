$(document).ready(function () {
    var url = window.location.href;
    url = url.substring(7)
    window.urlpath = url.substring(0, url.indexOf('/'));

    var oTable;
	var oSettings;
	var hash = window.location.hash;
	hash && $('ul.nav a[href="' + hash + '"]').tab('show');

	$('.nav-tabs a').click(function (e) {
		$(this).tab('show');
		var scrollmem = $('body').scrollTop() || $('html').scrollTop();
		window.location.hash = this.hash;
		$('html,body').scrollTop(scrollmem);
	});

	var url = window.location.href;
	TableHeaderFixed();
	NavTableRemove();
});

function TableHeaderFixed(){
	oTable = $('#indentList').DataTable({
			fixedHeader: false,
	        "scrollY": Number($(document).height() - 230),
	        "scrollX": true,
			"pageLength": 50,
			"search": {
				"smart": false
			},
			"columns": [
				null,null,null,
				{ "type": "date" },
				null,null,null,null,null,null
				]
		});
	updateIndentListJson();
	oTable.on("draw",function() {
		var keyword = $('#indentList_filter > label:eq(0) > input').val();
		$('#indentList').unmark();
		$('#indentList').mark(keyword,{});
		updateIndentListJson();
		setHeightForTable();
		listTableHoverIconsInit('indentList');
	});
	oTable.on('page.dt', function() {
	  $('html, body').animate({
		scrollTop: $(".dataTables_wrapper").offset().top - 15
	   }, 'slow');
	   listTableHoverIconsInit('indentList');
	});
	oSettings = oTable.settings();
	listTableHoverIconsInit('indentList');
	$( window ).resize();
}

function updateIndentListJson(){
	setTimeout(function(){
    	IndentListjsonObj = [];
    	var rows = oTable.rows( { page : 'current'} ).data();
        for(var i=0;i<rows.length;i++) {
        	var selectedRow = $(rows[i][1]);
        	indent = {}
	        indent ["indentId"] = selectedRow.attr("data-indentId")
	        indent ["indentNumber"] = selectedRow.text().trim();
            IndentListjsonObj.push(indent);
        }
		localStorage.setItem('indentListNav', JSON.stringify(IndentListjsonObj));
	},10);
}

function NavTableRemove() {
	$('ul.nav-tabs li a').click(function() {
		if($(this).attr('id') != "tab_view") {
			if($("#indentList").hasClass('dataTable')) {
				oTable.destroy();
			}
		}
		else {
			TableHeaderFixed();
		}
	});
}

function deleteIndent(indentNumber) {
	$.ajax({
		url: "/erp/stores/json/indent/checkpo/",
		type: "post",
		datatype: "json",
		data: {indent_no: indentNumber},
		success: function (response) {
			if (response[0][0]==0) {
				swal({
				  title: "Are you sure?",
				  text: "Do you want to delete this Indent!",
				  type: "warning",
				  showCancelButton: true,
				  confirmButtonColor: "#209be1",
				  confirmButtonText: "Yes, delete it!",
				  closeOnConfirm: true
				},
				function(){
					$("#delete_indent_id").val(indentNumber);
					$("#delete_indent_form").submit();
				});
			}
			else
			swal("WARNING", "Some POs have been raised against this Indent. Please delete those POs before deleting this Indent.", "warning");
		}
	});
}