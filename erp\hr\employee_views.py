import hashlib

import simplej<PERSON>
from django.db import transaction
from django.http import HttpResponseRedirect, HttpResponse
from django.template.response import TemplateResponse
from sqlalchemy import and_

from erp.auth import SESSION_KEY, ENTERPRISE_ID_SESSION_KEY
from erp.auth import USER_IN_SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.dao import executeQuery
from erp.forms import EmployeeForm, DepartmentForm
from erp.formsets import EmployeePayStructureFormset
from erp.helper import getStateList
from erp.hr import logger
from erp.hr.backend import HrService
from erp.masters.backend import MasterService
from erp.models import Department, PayStructure
from erp.models import Employee, PayStructureItem, EmployeePayStructureItem
from erp.properties import MANAGE_EMPLOYEE_TEMPLATE, MANAGE_EMPLOYEE_URL, TEMPLATE_TITLE_KEY
from settings import SQLASession
from util.ftp_helper import FTPUtil
from util.helper import constructFormInitializer

__author__ = 'saravanan'

EMPLOYEE_PAY_FORMSET_PREFIX = 'employee_pay_structure'


def manageEmployee(request):
	"""
	Renders the main page to manage Employees.
	:param request:
	:return:
	"""

	logger.info('Inside Manage Employee... ')
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	employee_form = EmployeeForm(enterprise_id=enterprise_id)
	employee_pay_formset = EmployeePayStructureFormset(prefix=EMPLOYEE_PAY_FORMSET_PREFIX)
	countries = MasterService().getCountries()
	country_list = []

	for country in countries:
		country_list.append({"country_code": country.code, "country_name": country.name})

	employees = HrService().getEmployeeReport(enterprise_id=enterprise_id)

	return TemplateResponse(template=MANAGE_EMPLOYEE_TEMPLATE, request=request, context={
		'employees': employees, 'employeeForm': employee_form,
		'departmentForm': DepartmentForm(
			prefix="department", initial={"enterprise_id": enterprise_id}, enterprise_id=enterprise_id),
		'employee_pay_structure_formset': employee_pay_formset, 'country_list': country_list, 'state_list': getStateList(),
		TEMPLATE_TITLE_KEY: "Employees"})


@transaction.commit_on_success
def saveEmployee(request):
	"""
	Save Employee
	Persists them in appropriate tables after validation.

	:param request:
	:return:
	"""
	logger.info('Inside Save Employees...')
	text = ""
	hr_service = HrService()
	db_session = hr_service.dao.db_session
	db_session.begin(subtransactions=True)
	employee_to_be_saved = None
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		logger.debug(request.POST)
		employee_form = EmployeeForm(data=request.POST, enterprise_id=enterprise_id)
		employee_pay_structure_formset = EmployeePayStructureFormset(
			request_handler.getPostData(), prefix='employee_pay_structure')

		if employee_form.is_valid() and employee_pay_structure_formset.is_valid():
			logger.info('Form valid...')
			if employee_form.cleaned_data['emp_id'] is not None and employee_form.cleaned_data['emp_id'] != '':
				logger.info('Existing Employee...')
				employee_to_be_saved = db_session.query(Employee).filter(
					Employee.emp_id == employee_form.cleaned_data['emp_id']).first()
				employee_to_be_saved.pay_structure_items = _saveEmployeePayParticulars(
					employee_pay_structure_formset, employee_form.cleaned_data['emp_id'],
					employee_to_be_saved, db_session)
				_copyEmployeeFormToEntity(employee_form, employee_to_be_saved)
				text = "Employee Updated Successfully"
			else:
				logger.info('New Employee...')
				employee_to_be_saved = _getEntityFromEmployeeForm(employee_form)
				employee_to_be_saved.pay_structure_items = _saveEmployeePayParticulars(
					employee_pay_structure_formset, employee_form.cleaned_data['emp_id'],
					employee_to_be_saved, db_session)

				text = "Employee Added Successfully"
			logger.debug('Changed objects: %s' % db_session.dirty)

			# Persisting employee profile pic
			if employee_to_be_saved.profile_pic_ext and employee_to_be_saved.profile_pic_ext != "":
				md5 = hashlib.md5()
				md5.update("employee_%s%s" % (enterprise_id, employee_to_be_saved.emp_code))
				filename = "%s.%s" % (md5.hexdigest(), employee_to_be_saved.profile_pic_ext)
				profile_pic_filename = HrService().storeEmployeeProfilePic(
					enterprise_id=enterprise_id, base64data=employee_to_be_saved.profile_pic, filename=filename)
				employee_to_be_saved.profile_pic_filename = profile_pic_filename
				logger.info("Employee Profile picture upload result: %s" % profile_pic_filename)
				if profile_pic_filename is None:
					text = "%s. Profile picture not uploaded!" % text
			db_session.add(employee_to_be_saved)
		logger.error("Employee Validation: %s %s" % (employee_form.errors, employee_pay_structure_formset.errors))
		db_session.commit()
		if request.GET.get('add_employee_ajax'):
			material_data_dump = {
				"employee_name": employee_to_be_saved.emp_name, "employee_id": employee_to_be_saved.emp_id}
			return HttpResponse(content=simplejson.dumps(material_data_dump), mimetype='application/json')
		return HttpResponseRedirect(MANAGE_EMPLOYEE_URL)
	except:
		db_session.rollback()
		logger.info("Saving Employee Failed...")
		raise


def _saveEmployeePayParticulars(employee_pay_particulars, emp_id, employee_to_be_saved, db_session):
	"""
	Save the Voucher Particulars Formset Details
	:param employee_pay_particulars:
	:return:
	"""

	particulars_to_be_saved = []
	for particulars_form in employee_pay_particulars:
		employee_pay_particular = db_session.query(EmployeePayStructureItem).filter(
			EmployeePayStructureItem.employee_id == emp_id,
			EmployeePayStructureItem.description == particulars_form.cleaned_data['description'],
			EmployeePayStructureItem.type == particulars_form.cleaned_data['type'],
			EmployeePayStructureItem.enterprise_id == employee_to_be_saved.enterprise_id).first()
		if not particulars_form.cleaned_data['DELETE']:
			logger.info("Delete Flag : %s" % particulars_form.cleaned_data['DELETE'])
			if employee_pay_particular is None:
				employee_pay_particular = EmployeePayStructureItem(
					employee_id=emp_id, description=particulars_form.cleaned_data['description'],
					type=particulars_form.cleaned_data['type'], enterprise_id=employee_to_be_saved.enterprise_id)
			employee_pay_particular.amount = particulars_form.cleaned_data['amount']
			particulars_to_be_saved.append(employee_pay_particular)
	return particulars_to_be_saved


def editEmployee(request):
	logger.info('Editing a Employee...')
	if request.method == 'POST':
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		emp_id = request.POST['emp_code']
		logger.info("Cus Id selected: %s" % emp_id)
		emp_under_edit = SQLASession().query(Employee).filter(
			Employee.emp_code == emp_id, Employee.enterprise_id == enterprise_id).first()
		emp_form_initializer = _getEmployeeFormInitializerFromEntity(emp_under_edit)
		countries = MasterService().getCountries()
		country_list = []

		for country in countries:
			country_list.append({"country_code": country.code, "country_name": country.name})
		try:
			data = None
			if emp_under_edit.profile_pic_filename:
				data = FTPUtil().download(file_path=enterprise_id, filename=emp_under_edit.profile_pic_filename)
			if data:
				ext = emp_under_edit.profile_pic_filename.split(".")[-1]
				emp_form_initializer['profile_pic'] = "data:application/%s;base64,%s" % (ext, data.encode('base64'))
		except Exception as e:
			logger.error("Fetching Employee pic from ftp failed %s " % e.message)

		employee_form = EmployeeForm(enterprise_id=enterprise_id, initial=emp_form_initializer)
		pay_structure_id = emp_under_edit.pay_structure_id
		employee_pay_formset = EmployeePayStructureFormset(prefix=EMPLOYEE_PAY_FORMSET_PREFIX)

		emp_forms = SQLASession().query(Employee).filter(Employee.enterprise_id == enterprise_id)
		return TemplateResponse(template=MANAGE_EMPLOYEE_TEMPLATE, request=request, context={
			'employeeForm': employee_form, 'employees': emp_forms, 'pay_structure_id': pay_structure_id,
			'departmentForm': DepartmentForm(
				prefix="department", initial={"enterprise_id": enterprise_id}, enterprise_id=enterprise_id),
			'employee_pay_structure_formset': employee_pay_formset, 'country_list': country_list, 'state_list': getStateList(),
			TEMPLATE_TITLE_KEY: emp_form_initializer['first_name']})
	else:
		return HttpResponseRedirect(MANAGE_EMPLOYEE_URL)


def _generateEmployeePayFormset(pay_particulars=()):
	"""
	:param pay_particulars:
	:return:
	"""
	initial_pay_particulars = []
	for pay_particular in pay_particulars:
		pay_particular_form_initial = constructFormInitializer(pay_particular)
		initial_pay_particulars.append(pay_particular_form_initial)
	return EmployeePayStructureFormset(initial=initial_pay_particulars, prefix=EMPLOYEE_PAY_FORMSET_PREFIX)


def deleteEmployee(request):
	"""
	Soft Delete of Employee - setting the status to 0
	:param request:
	:return:
	"""
	logger.info('Deleting a Employee...')
	db_session = SQLASession()
	db_session.begin(subtransactions=True)

	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		emp_code = request.POST['deleteCode']
		emp_to_delete = db_session.query(Employee).filter(
			Employee.emp_code == emp_code, Employee.enterprise_id == enterprise_id).first()
		db_session.delete(emp_to_delete)
		db_session.commit()
	except Exception as e:
		db_session.rollback()
		logger.exception(e)
	return HttpResponseRedirect(MANAGE_EMPLOYEE_URL)


def load_pay_structure_items(request):
	"""

	:param request:
	:return:
	"""

	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	pay_structure_id = request_handler.getPostData("pay_structure_id")
	pay_items = []
	try:
		pay_structure_items = SQLASession().query(PayStructureItem).filter(
			PayStructureItem.pay_structure_id == pay_structure_id, PayStructureItem.enterprise_id == enterprise_id).all()
		for pay in pay_structure_items:
			pay_items.append((pay.type, pay.description))
		logger.info("Pay Items:%s" % pay_items)

	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(pay_items), mimetype='application/json')


def load_emp_pay_items(request):
	"""

	:param request:
	:return:
	"""

	if request.method == 'POST':
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		emp_id = request_handler.getPostData("employee_id")
		logger.info("Emp Code:%s" % emp_id)
		combined_pay_items = []
		try:
			db_session = SQLASession()
			employee_pay_items = db_session.query(
				EmployeePayStructureItem.type, EmployeePayStructureItem.description, EmployeePayStructureItem.amount
			).filter(
				EmployeePayStructureItem.employee_id == emp_id,
				EmployeePayStructureItem.enterprise_id == enterprise_id).all()

			pay_items = []
			for pay_item in employee_pay_items:
				combined_pay_items.append((pay_item.type, pay_item.description, pay_item.amount))
				pay_items.append((pay_item.type.lower().strip(), pay_item.description.lower().strip()))

			pay_structure_items = db_session.query(
				PayStructureItem.type, PayStructureItem.description
			).outerjoin(PayStructureItem.pay_structure).outerjoin(
				Employee, and_(Employee.pay_structure_id == PayStructure.id, Employee.enterprise_id == PayStructure.enterprise_id)
			).filter(
				Employee.emp_id == emp_id,
				Employee.enterprise_id == enterprise_id).all()

			for pay_structure_item in pay_structure_items:
				if (pay_structure_item.type.lower().strip(), pay_structure_item.description.lower().strip()) not in pay_items:
					combined_pay_items.append((pay_structure_item.type, pay_structure_item.description, 0))
			logger.info("Pay Items:%s" % pay_items)

		except Exception as e:
			logger.exception(e)
		return HttpResponse(content=simplejson.dumps(combined_pay_items), mimetype='application/json')


def _copyEmployeeFormToEntity(form, entity):
	entity.emp_id = form.cleaned_data['emp_id']
	entity.emp_code = form.cleaned_data['emp_code']
	entity.emp_name = form.cleaned_data['first_name']
	entity.address1 = form.cleaned_data['address1']
	entity.address2 = form.cleaned_data['address2']
	entity.city = form.cleaned_data['city']
	entity.state = form.cleaned_data['state']
	entity.phone_no = form.cleaned_data['phone_no']
	entity.department_id = form.cleaned_data['department_id']
	entity.email = form.cleaned_data['email']
	entity.enterprise_id = form.cleaned_data['enterprise_id']
	entity.last_name = form.cleaned_data['last_name']
	entity.employment_type = form.cleaned_data['employment_type']
	entity.status = form.cleaned_data['status']
	entity.designation = form.cleaned_data['designation']
	entity.date_of_joining = form.cleaned_data['date_of_joining']
	entity.date_of_birth = form.cleaned_data['date_of_birth']
	entity.gender = form.cleaned_data['gender']
	entity.country = form.cleaned_data['country']
	entity.postal_code = form.cleaned_data['postal_code']
	entity.nationality = form.cleaned_data['nationality']
	entity.martial_status = form.cleaned_data['martial_status']
	entity.aadhar_number = form.cleaned_data['aadhar_number']
	entity.pan_number = form.cleaned_data['pan_number']
	entity.account_number = form.cleaned_data['account_number']
	entity.account_name = form.cleaned_data['account_name']
	entity.account_type = form.cleaned_data['account_type']
	entity.ifsc_code = form.cleaned_data['ifsc_code']
	entity.esi_no = form.cleaned_data['esi_no']
	entity.pf_no = form.cleaned_data['pf_no']
	entity.pay_structure_id = form.cleaned_data['pay_structure_id']
	entity.no_of_el = form.cleaned_data['no_of_el']
	entity.no_of_cl = form.cleaned_data['no_of_cl']
	entity.place_of_work = form.cleaned_data['place_of_work']
	entity.father_name = form.cleaned_data['father_name']
	entity.mother_name = form.cleaned_data['mother_name']
	entity.contact_number = form.cleaned_data['contact_number']
	entity.profile_pic_filename = form.cleaned_data['profile_pic_filename']
	entity.spouse_name = form.cleaned_data['spouse_name']
	entity.blood_group = form.cleaned_data['blood_group']

	logo_as_texts = form.cleaned_data['profile_pic'].split(",") if form.cleaned_data['profile_pic'] else ""
	logo_extension = logo_as_texts[0][logo_as_texts[0].index('/') + 1:logo_as_texts[0].index(';')] if form.cleaned_data[
		'profile_pic'] else ""
	entity.profile_pic = logo_as_texts[1] if form.cleaned_data['profile_pic'] else ""
	entity.profile_pic_ext = logo_extension


def _getEntityFromEmployeeForm(form):
	entity = Employee(
		emp_id=form.cleaned_data['emp_id'], emp_code=form.cleaned_data['emp_code'],
		emp_name=form.cleaned_data['first_name'], address1=form.cleaned_data['address1'],
		address2=form.cleaned_data['address2'], city=form.cleaned_data['city'],
		state=form.cleaned_data['state'], phone_no=form.cleaned_data['phone_no'],
		department_id=form.cleaned_data['department_id'], email=form.cleaned_data['email'],
		enterprise_id=form.cleaned_data['enterprise_id'], last_name=form.cleaned_data['last_name'],
		employment_type=form.cleaned_data['employment_type'], status=form.cleaned_data['status'],
		designation=form.cleaned_data['designation'],
		date_of_joining=form.cleaned_data['date_of_joining'], date_of_birth=form.cleaned_data['date_of_birth'],
		country=form.cleaned_data['country'], gender=form.cleaned_data['gender'],
		postal_code=form.cleaned_data['postal_code'], nationality=form.cleaned_data['nationality'],
		martial_status=form.cleaned_data['martial_status'], aadhar_number=form.cleaned_data['aadhar_number'],
		pan_number=form.cleaned_data['pan_number'], account_number=form.cleaned_data['account_number'],
		account_name=form.cleaned_data['account_name'], account_type=form.cleaned_data['account_type'],
		ifsc_code=form.cleaned_data['ifsc_code'], esi_no=form.cleaned_data['esi_no'],
		pf_no=form.cleaned_data['pf_no'], pay_structure_id=form.cleaned_data['pay_structure_id'],
		no_of_el=form.cleaned_data['no_of_el'], no_of_cl=form.cleaned_data['no_of_cl'],
		place_of_work=form.cleaned_data['place_of_work'], father_name=form.cleaned_data['father_name'],
		mother_name=form.cleaned_data['mother_name'], contact_number=form.cleaned_data['contact_number'],
		profile_pic_filename=form.cleaned_data['profile_pic_filename'], spouse_name=form.cleaned_data['spouse_name'],
		blood_group=form.cleaned_data['blood_group'])

	logo_as_texts = form.cleaned_data['profile_pic'].split(",") if form.cleaned_data['profile_pic'] else ""
	logo_extension = logo_as_texts[0][logo_as_texts[0].index('/') + 1:logo_as_texts[0].index(';')] if form.cleaned_data[
		'profile_pic'] else ""
	entity.profile_pic = logo_as_texts[1] if form.cleaned_data['profile_pic'] else ""
	entity.profile_pic_ext = logo_extension
	return entity


def _getEmployeeFormInitializerFromEntity(entity):
	return {
		'emp_id': entity.emp_id,
		'emp_code': entity.emp_code,
		'first_name': entity.emp_name,
		'address1': entity.address1,
		'address2': entity.address2,
		'city': entity.city,
		'state': entity.state,
		'phone_no': entity.phone_no,
		'department_id': entity.department_id,
		'email': entity.email,
		'enterprise_id': entity.enterprise_id,
		'last_name': entity.last_name,
		'employment_type': entity.employment_type,
		'status': entity.status,
		'designation': entity.designation,
		'date_of_joining': entity.date_of_joining,
		'date_of_birth': entity.date_of_birth,
		'gender': entity.gender,
		'country': entity.country,
		'postal_code': entity.postal_code,
		'nationality': entity.nationality,
		'martial_status': entity.martial_status,
		'aadhar_number': entity.aadhar_number,
		'pan_number': entity.pan_number,
		'account_number': entity.account_number,
		'account_name': entity.account_name,
		'account_type': entity.account_type,
		'ifsc_code': entity.ifsc_code,
		'esi_no': entity.esi_no,
		'pf_no': entity.pf_no,
		'pay_structure_id': entity.pay_structure_id,
		'no_of_el': entity.no_of_el,
		'no_of_cl': entity.no_of_cl,
		'place_of_work': entity.place_of_work,
		'father_name': entity.father_name,
		'mother_name': entity.mother_name,
		'contact_number': entity.contact_number,
		'profile_pic': "",
		'profile_pic_filename': "",'spouse_name': entity.spouse_name,
		'blood_group': entity.blood_group
	}


def saveDepartment(request):
	"""
	Save Department
	Persists them in appropriate tables after validation.

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	try:
		department_form = DepartmentForm(enterprise_id=enterprise_id, data=request.POST, prefix="department")
		if department_form.is_valid():
			response = HrService().saveDepartment(
				enterprise_id=enterprise_id, created_by=user_id, department_dict=department_form.cleaned_data)
			logger.info("Save Department: %s" % response)
		else:
			logger.warn('Department Form errors: %s' % department_form.errors)
	except:
		raise
	return HttpResponseRedirect(request.META.get('HTTP_REFERER'))


def _copyDepartmentFormToEntity(form, entity):
	entity.name = form.cleaned_data['name']
	entity.parent_id = form.cleaned_data['parent_id']
	entity.enterprise_id = form.cleaned_data['enterprise_id']


def _getEntityFromDepartmentForm(form):
	entity = Department(
		name=form.cleaned_data['name'],
		parent_id=form.cleaned_data['parent_id'] if form.cleaned_data['parent_id'].isdigit() else None,
		enterprise_id=form.cleaned_data['enterprise_id'])
	return entity


def _getDepartmentFormInitializerFromEntity(entity):
	return {
		'id': entity.id, 'name': entity.name, 'parent_id': entity.parent_id, 'enterprise_id': entity.enterprise_id}


def checkEmployeeCode(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	emp_code = request_handler.getPostData("emp_code")
	try:
		employee_count_query = "Select count(1) from employee_master where enterprise_id='%s' and emp_code = '%s'" % (
			enterprise_id, emp_code)
		number_count = executeQuery(employee_count_query)
		json = simplejson.dumps(str(number_count[0][0]))
	except Exception as e:
		logger.exception(e)
		json = simplejson.dumps(str(e))
	return HttpResponse(json)
