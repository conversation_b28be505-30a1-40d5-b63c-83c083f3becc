"""

"""
import re
import json

from collections import OrderedDict
from datetime import datetime
from deepdiff import DeepDiff

from erp.accounts import logger
from erp.dao import executeQuery
from erp.helper import getUser
# from erp.models import Material
from settings import SQLASession
from util.api_util import response_code
from util.changelog import ChangeLog

__author__ = 'Nithin'

CONST_LOG_VC1 = '%s <b>%s</b> added'
CONST_LOG_VC2 = '%s <b>%s</b> removed'
CONST_LOG_VC3 = ' <b>%s</b>: <i>%s</i>'


class PurchaseChangelog(ChangeLog):
	"""
	Changelog to do mongodb operations
	"""
	material = {}
	collection = 'purchase_order'
	uid = "po_id"

	def __init__(self):
		self.db_session = SQLASession()
		super(PurchaseChangelog, self).__init__(collection=self.collection, uid=self.uid)
		self.response_log = []
		self.logged_key_set = []
		self.status = {0: 'Draft', 1: 'Reviewed', 2: 'Approved', 3: 'Rejected', 4: 'Completed'}
		self.type = {0: 'PO', 1: 'JO', 2: 'PP'}
		self.pay_through = {0: '', 1: 'PDC', 2: 'Cheque', 3: 'Cash', 4: 'DD', 5: 'Bank Transfer'}
		self.pay_against = {0: '', 1: 'No of Days', 2: 'Proforma Invoice', 3: 'Cash On Delivery', 4: 'Advance'}
		self.module_data_set = dict()
		common = OrderedDict()
		common['type'] = 'Type'
		common['project_name'] = 'Project'
		common['supplier_name'] = 'Supplier'
		common['is_blanket_po'] = 'Is Blanket PO'
		common['indent_no'] = 'Indent No'
		common['indent_type'] = 'Indent Type'
		common['purpose'] = 'Purpose'
		common['status'] = 'Status'
		common['financial_year'] = 'Financial Year'
		common['issue_to'] = 'Issue to'
		self.module_data_set = common
		items = OrderedDict()
		items['title'] = 'Material'
		items['drawing_name'] = 'Name'
		items['quantity'] = 'Quantity'
		items['rate'] = 'Price'
		items['discount'] = 'Discount'
		items['cgst'] = 'CGST'
		items['sgst'] = 'SGST'
		items['igst'] = 'IGST'
		items['total_price'] = 'Total Price'
		self.module_data_set['materials'] = items
		others = OrderedDict()
		others['currency_code'] = 'Currency'
		others['quotation_ref_no'] = 'Quotation Ref No'
		others['quotation_date'] = 'Quotation Ref Date'
		others['round_off'] = 'Round Off'
		others['total'] = 'Grand Total'
		others['closing_remarks'] = 'Instructions'
		self.module_data_set.update(others)
		taxes = OrderedDict()
		taxes['title'] = 'Taxes'
		taxes['name'] = 'Name'
		taxes['rate'] = 'Net Rate'
		self.module_data_set['taxes'] = taxes
		remarks = OrderedDict()
		remarks['title'] = 'Remarks'
		remarks['remark'] = 'Description'
		remarks['by'] = 'By'
		remarks['date'] = 'Date'
		self.module_data_set['remarks'] = remarks
		other_details = OrderedDict()
		other_details['payment'] = 'Payment'
		other_details['pay_in_days'] = 'Pay In Days'
		other_details['pay_against'] = 'Pay Against'
		other_details['pay_through'] = 'Pay Through'
		other_details['transport'] = 'Transport'
		other_details['packing'] = 'Packing'
		other_details['delivery'] = 'Delivery'
		other_details['user'] = 'User'
		other_details['approved_on'] = 'Approved Date'
		other_details['approved_by'] = 'Approved By'
		other_details['super_modified_on'] = 'Super Modified Date'
		other_details['super_modified_by'] = 'Super Modified By'
		other_details['valid_since'] = 'Valid Since'
		other_details['valid_till'] = 'Valid Till'
		other_details['short_close_qty'] = 'Short Close Quantity'
		other_details['shipping_name'] = 'Shipping Name'
		other_details['shipping_address'] = 'Shipping Address'
		self.module_data_set.update(other_details)
		logger.info('Purchase Changelog module initiated :)')

	def buildInsertDataStructure(self, user=None, data=None):
		"""
		Build/formulate material data to insert on mongoDB
		:param user:
		:param data:
		:return:
		"""
		response = dict()
		response['modified_at'] = datetime.now()
		response['materials'] = []
		response['remarks'] = []
		response['taxes'] = []
		try:
			project_name = ''
			currency_name = ''
			supplier_name = ''
			if 'supplier_id' in data:
				query_fetch_supplier = """SELECT party_name FROM party_master where 
															party_id = '{supplier_id}' """.format(
					supplier_id=data['supplier_id'])
				supplier_name = executeQuery(query_fetch_supplier)
				if supplier_name:
					supplier_name = supplier_name[0][0]
				else:
					supplier_name = ''
			if data['type'] != 2:
				if data['project_code']:
					query_fetch_project_name = """ SELECT pjt.name from projects as pjt 
								WHERE pjt.id = '{code}' """.format(code=data['project_code'])
					project_name = executeQuery(query_fetch_project_name)
					project_name = project_name[0][0]
				if data['currency_code']:
					query_fetch_currency_name = """ SELECT cur.code from currency as cur 
								WHERE cur.id = '{code}' """.format(code=data['currency_code'])
					currency_name = executeQuery(query_fetch_currency_name)
					currency_name = currency_name[0][0]
				indent = ''
				if 'indent_no' in data:
					query_fetch_indent = """SELECT case when indent_module_id = 0 then 
												CONCAT(financial_year, '/IND/', IF(LENGTH(indent_id) < 6, LPAD(indent_id, 6, '0'), indent_id),
					   							IFNULL(sub_number, '')) else
					   							CONCAT(financial_year, '/MI/', IF(LENGTH(indent_id) < 6, LPAD(indent_id, 6, '0'), indent_id),
					   							IFNULL(sub_number, '')) end
									FROM indents where indent_no = '{indent_no}' """.format(indent_no=data['indent_no'])
					indent = executeQuery(query_fetch_indent)
					if indent:
						indent = indent[0][0]
					else:
						indent = ''
				status = self.status[int(data['status'])]
				response['indent_no'] = str(indent)
				response['supplier_name'] = str(supplier_name)
				response['project_name'] = str(project_name)
				response['id'] = str(data['id']) if 'id' in data else ''
				response['status'] = status
				response['quotation_ref_no'] = str(data['quotation_ref_no']) if 'quotation_ref_no' in data else ''
				response['quotation_date'] = str(data['quotation_date']) if 'quotation_date' in data else ''
				response['payment'] = str(data['payment']) if 'payment' in data else ''
				response['pay_in_days'] = str(data['pay_in_days']) if 'pay_in_days' in data else ''
				response['pay_against'] = self.pay_against[int(data['pay_against'])]
				response['pay_through'] = self.pay_through[int(data['pay_through'])]
				response['transport'] = 'Checked' if data['transport'] != 0 else 'Un-Checked'
				response['delivery'] = str(data['delivery']) if 'delivery' in data else ''
				response['purpose'] = str(data['purpose']) if 'purpose' in data else ''
				response['round_off'] = str(data['round_off']) if 'round_off' in data else ''
				response['is_blanket_po'] = 'Checked' if data['is_blanket_po'] is True else 'Un-Checked'
				response['total'] = (data['total']) if 'total' in data else ''
				response['packing'] = 'Checked' if data['packing'] != 0 else 'Un-Checked'
				response['closing_remarks'] = str(data['closing_remarks']) if 'closing_remarks' in data else ''
				response['currency_code'] = str(currency_name)
				response['financial_year'] = str(data['financial_year']) if 'financial_year' in data else ''
				if self.status[int(data['status'])] == 'Approved':
					response['approved_on'] = str(data['approved_on']) if 'approved_on' in data else ''
					response['approved_by'] = (user.first_name + " " + user.last_name)
				response['last_modified_by'] = [user.first_name, user.last_name]
				response['type'] = self.type[int(data['type'])]
				response['issue_to'] = (data['issue_to']) if 'issue_to' in data else ''
				response['username'] = [user.first_name, user.last_name]
				if data['is_blanket_po'] is True:
					response['valid_since'] = data['valid_since']
					response['valid_till'] = data['valid_till']
				else:
					response['valid_since'] = ''
					response['valid_till'] = ''
				if 'materials' in data:
					for item in data['materials']:
						temp = dict()
						temp['drawing_name'] = str(item["drawing_name"])
						temp['quantity'] = str(item["quantity"])
						temp['rate'] = str(item["rate"])
						temp['discount'] = str(item["discount"])
						temp['cgst'] = self.getTaxRate(item["cgst"]) if item["cgst"] != "0" else "0.00"
						temp['sgst'] = self.getTaxRate(item["sgst"]) if item["sgst"] != "0" else "0.00"
						temp['igst'] = self.getTaxRate(item["igst"]) if item["igst"] != "0" else "0.00"
						temp['total_price'] = str(item["total_price"])
						response['materials'].append(temp)
				if 'new_remarks' in data:
					for remark in data['new_remarks']:
						item = dict()
						item['remark'] = remark['remarks']
						item['date'] = remark['date']
						item['by'] = remark['by']
						response['remarks'].append(item)
				if 'taxes' in data:
					for tax in data['taxes']:
						tax_item = dict()
						tax_item['name'] = str(tax['tax_code'])
						tax_item['rate'] = str(tax['tax_value'])
						response['taxes'].append(tax_item)
				response['shipping_name'] = (data['shipping_name']) if 'shipping_name' in data else ''
				response['shipping_address'] = (data['shipping_address']) if 'shipping_address' in data else ''
			else:
				indent = ''
				if 'indent_no' in data:
					query_fetch_indent ="""SELECT(CONCAT(financial_year, '/MI/', LPAD(indent_id, 6, 0), IFNULL(sub_number, '')))
					FROM indents where indent_no = '{indent_no}' """.format(indent_no=data['indent_no'])
					indent = executeQuery(query_fetch_indent)
					indent = indent[0][0]
				enterprise_id = (data['enterprise_id']) if 'enterprise_id' in data else ''
				approved_by = getUser(enterprise_id=enterprise_id, user_id=data['approved_by'])
				approved_by = str(approved_by.first_name) + " " + str(approved_by.last_name) if approved_by else ''
				response['status'] = self.status[int(data['status'])]
				response['id'] = str(data['id']) if 'id' in data else ''
				response['indent_no'] = str(indent)
				response['closing_remarks'] = str(data['closing_remarks']) if 'closing_remarks' in data else ''
				response['username'] = [user.first_name, user.last_name]
				if data["supplier_id"]:
					response['type'] = self.type[int(1)]
					response['status'] = self.status[int(data['status'])]
					response['supplier_name'] = supplier_name
				else:
					response['type'] = self.type[int(data['type'])]
					response['status'] = self.status[int(data['status'])]
					response['issue_to'] = (data['issue_to']) if 'issue_to' in data else ''
					response['approved_on'] = str(data['approved_on']) if 'approved_on' in data else ''
					response['approved_by'] = approved_by
					response['short_close_qty'] = str(data['short_close_qty']) if 'short_close_qty' in data else ''
				if data['new_remarks']:
					for remark in data['new_remarks']:
						item = dict()
						item['remark'] = remark['remarks']
						item['date'] = remark['date']
						item['by'] = remark['by']
						response['remarks'].append(item)
				if 'materials' in data:
					for item in data['materials']:
						temp = dict()
						temp['drawing_name'] = str(item["drawing_name"])
						temp['quantity'] = str(item["quantity"])
						temp['rate'] = "0"
						temp['discount'] = "0"
						temp['cgst'] = "0"
						temp['sgst'] = "0"
						temp['igst'] = "0"
						temp['total_price'] = "0"
						response['materials'].append(temp)
		except Exception as e:
			logger.exception(e)
		return response

	def getTaxRate(self, tax_code):
		if tax_code:
			query_fetch_tax_rate = """select distinct net_rate from tax where code = '{tax_code}' """.format(
				tax_code=tax_code)
			tax_rate = executeQuery(query_fetch_tax_rate)
			tax_rate = tax_rate[0][0]
			return str(tax_rate)


	def queryInsert(self, user_id=None, enterprise_id=None, data=None):
		"""
		:param user_id:
		:param enterprise_id:
		:param data:
		:return:
		"""
		response = {}
		try:
			user = getUser(enterprise_id=enterprise_id, user_id=user_id)
			formatted_data = self.buildInsertDataStructure(user=user, data=data)
			response = self.insert(id=int(data["id"]), enterprise_id=enterprise_id, data=formatted_data)
		except Exception as e:
			logger.exception(e)
		return response

	def fetchLogList(self, po_id=None, enterprise_id=None, offset=None, limit=None):
		"""
		get listed voucher log data
		:param po_id:
		:param offset:
		:param limit:
		:param enterprise_id:
		:return:
		"""
		response = {}
		result = []
		try:
			query = self.fetchLog(id=int(po_id), enterprise_id=int(enterprise_id), offset=offset, limit=limit)
			for material in query:
				material['created_at'] = str(material['created_at'])
				material['log']['modified_at'] = str(material['log']['modified_at'])
				result.append(material)
			response = response_code.success() if len(result) > 0 else response_code.failure()
			response['data'] = result
		except Exception as e:
			logger.exception(e)
		return response

	def fetchLogData(self, po_id=None, enterprise_id=None, modified_at=None):
		"""
		get individual log data
		:param po_id:
		:param enterprise_id:
		:param modified_at:
		:return:
		"""
		response = response_code.failure()
		result = []
		try:
			query = self.fetchLogDetails(id=int(po_id), enterprise_id=int(enterprise_id), identifier=modified_at)
			for voucher in query:
				del voucher['log']['modified_at']
				result.append(voucher)
			response = response_code.success() if len(result) > 0 else response
			if len(result) > 1:
				response['data'] = self.buildLogData(result[1]['log'], result[0]['log'])
			elif len(result) > 0:
				response['data'] = self.buildLogData(None, result[0]['log'])
			response['data'] = response['data'] if len(response['data']) > 0 else ['No change detected']
		except Exception as e:
			logger.exception(e)
			response = response_code.failure()
		return response

	def fieldLogData(self, key=None, title=None, old=None, new=None):
		"""

		:param key:
		:param title:
		:param old:
		:param new:
		:return:
		"""
		if old is not None and key in old:
			if type(new[key]) == list and type(old[key]) == list:
				diff = DeepDiff(old[key], new[key], ignore_order=True)
				if 'iterable_item_added' in diff:
					for item_added in diff['iterable_item_added'].items():
						if type(self.module_data_set[key]) is not str:
							log = '<b>%s</b> added with following values: <br />' % self.module_data_set[key]['title']
							for i, elem in self.module_data_set[key].items():
								if i != 'QIR':
									log += ' <b>%s</b>: <i>%s</i>' % (elem, ', '.join(str(l_data) for l_data in item_added[1][i]) if type(item_added[1][i]) == list else str(item_added[1][i])) if len(item_added) > 1 and i in item_added[1] and i is not 'title' else ''
								elif item_added[1].get('QIR') != None:
									for qir in item_added[1]['QIR']:
										log += '<br /> <b>%s</b> added with following values: <br />' % self.module_data_set[key]['QIR']['title']
										for j, ele in self.module_data_set[key]['QIR'].items():
											log += ' <b>%s</b>: <i>%s</i>' % (ele, ', '.join(str(l_data) for l_data in qir[j]) if type(qir[j]) == list else str(qir[j])) if j is not 'title' and j in qir else ''
							self.response_log.append('%s' % log)
						else:
							self.response_log.append('%s named <b>%s</b> was added' % (self.module_data_set[key], item_added[1]))

				if 'iterable_item_removed' in diff:
					for item_removed in diff['iterable_item_removed'].items():
						if type(self.module_data_set[key]) is not str:
							log = '<b>%s</b> removed with following values: <br />' % self.module_data_set[key]['title']
							for i, elem in self.module_data_set[key].items():
								if i != 'QIR':
									log += ' <b>%s</b>: <i>%s</i>' % (elem, ', '.join(str(l_data) for l_data in item_removed[1][i]) if type(item_removed[1][i]) == list else str(item_removed[1][i])) if len(item_removed) > 1 and i in item_removed[1] and i is not 'title' else ''
								elif item_removed[1].get('QIR') != None:
									for qir in item_removed[1]['QIR']:
										log += '<br /> <b>%s</b> removed with following values: <br />' % self.module_data_set[key]['QIR']['title']
										for j, ele in self.module_data_set[key]['QIR'].items():
											log += ' <b>%s</b>: <i>%s</i>' % (ele, ', '.join(str(l_data) for l_data in qir[j]) if type(qir[j]) == list else str(qir[j])) if j is not 'title' and j in qir else ''
							self.response_log.append('%s' % log)
						else:
							self.response_log.append(
								'%s named <b>%s</b> was removed' % (self.module_data_set[key], item_removed[1]))
				if 'values_changed' in diff:
					for item in diff['values_changed'].keys():
						var_chg = re.findall("\[(.*?)\]", item.replace('\'', ''))
						self.response_log.append('<b>%s</b> of ledger <b>%s</b> has changed to <b>%s</b>' % (var_chg[1].capitalize(), old[key][int(var_chg[0])][var_chg[1]], new[key][int(var_chg[0])][var_chg[1]]))
			else:
				diff = DeepDiff(old[key], new[key])
				if 'values_changed' in diff:
					if str(diff['values_changed']['root']['old_value']) == '' and str(
							diff['values_changed']['root']['new_value']) != '':
						self.response_log.append(
							'%s <b>%s</b> added' % (title, diff['values_changed']['root']['new_value']))
					elif str(diff['values_changed']['root']['new_value']) == '' and str(
							diff['values_changed']['root']['old_value']) != '':
						self.response_log.append(
							'%s <b>%s</b> removed' % (title, diff['values_changed']['root']['new_value']))
					else:
						self.response_log.append('%s changed from <b>%s</b> to <b>%s</b>' % (
							title,
							diff['values_changed']['root']['old_value'],
							diff['values_changed']['root']['new_value']))
		elif key in new:
			if type(new[key]) == list:
				valid_od = type(OrderedDict())
				if type(title) == dict or type(title) == valid_od:
					for item in new[key]:
						log = '<b>%s</b> added with following values: <br />' % self.module_data_set[key]['title']
						for i, elem in self.module_data_set[key].items():
							if i != 'QIR':
								log += ' <b>%s</b>: <i>%s</i>' % (elem, ', '.join(str(l_data) for l_data in item[i]) if type(item[i]) == list else str(item[i])) if i is not 'title' and i in item else ''
							elif item.get('QIR') != None:
								for qir in item['QIR']:
									log += '<br /> <b>%s</b> added with following values: <br />' % self.module_data_set[key]['QIR']['title']
									for j, ele in self.module_data_set[key]['QIR'].items():
										log += ' <b>%s</b>: <i>%s</i>' % (ele, ', '.join(str(l_data) for l_data in qir[j]) if type(qir[j]) == list else str(qir[j])) if j is not 'title' and j in qir else ''
						self.response_log.append('%s' % log)
				else:
					self.response_log.append('%s <b>%s</b> added' % (title, ', '.join(str(l_data) for l_data in new[key])))
			elif new[key] != '':
				self.response_log.append('%s <b>%s</b> added' % (title, new[key]))
		logger.info('tags LOG DATA updated %s' % self.response_log)
		return False

	def buildLogData(self, old=None, new=None):
		"""

		:param old:
		:param new:
		:return:
		"""
		for key, title in self.module_data_set.items():
			self.fieldLogData(key=key, title=title, old=old, new=new)
		return self.response_log
