"""
"""
import json
import os
from datetime import datetime

import jwt
from django.http import HttpResponse
from django.shortcuts import render_to_response
from django.template.context import RequestContext
from django.utils.encoding import smart_str

from erp import properties
from erp.auth import USER_IN_SESSION_KEY, SESSION_KEY, SESSION_USERNAME_KEY
from erp.auth.request_handler import RequestHandler
from erp.icd import logger
from erp.icd.backend import ICDService
from erp.models import Receipt, CreditDebitNote, Enterprise
from erp.stores.backend import StoresService
from settings import JWT_SECRET, CURRENT_VERSION
from util.api_util import response_code
from util.helper import getFormattedDocPath

__author__ = 'nandha'


def getCheckedGRN(request):
	"""

	Here Pending means checked in database
	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		icd_service = ICDService()
		enterprise_id = rh.getPostData('enterprise_id')
		enterprise = icd_service.audit_dao.db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
		icd_request_acknowledgement = enterprise.is_icd_request_acknowledgement
		data = icd_service.getCheckedGRN(enterprise_id=enterprise_id, icd_request_acknowledgement=icd_request_acknowledgement)
		if data:
			response = response_code.success()
			response['grn_list'] = data
		else:
			response = response_code.databaseError()
		logger.info("Getting %s checked grn" % len(data))
	except Exception as e:
		logger.exception("Getting Checked grn list failed. %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def grnMaterials(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		grn_number = rh.getPostData('grn_number')
		if not grn_number:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')

		logger.info('Fetching materials for GRN %s' % grn_number)
		data = ICDService().getGrnMaterials(enterprise_id=enterprise_id, grn_number=grn_number)
		if data is not None:
			response = response_code.success()
			response['materials'] = data
		else:
			response = response_code.databaseError()
		logger.info("Fetching %s grn materials." % len(data))
	except Exception as e:
		logger.exception("Failed fetching grn materials... %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def downloadDoc(request):
	"""
	http://************:8007/erp/api/audit/downloadDoc/
	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		document_type = rh.getPostData('response_data_type')  # data, file
		if not document_type:
			document_type = 'file'
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		receipt_no = rh.getPostData('receipt_no')
		note_id = rh.getPostData('note_id')
		grn_code = rh.getPostData('grn_code')
		doc_type = rh.getPostData('doc_type')
		if receipt_no is None or doc_type is None:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')
		icd_service = ICDService()
		data, receipt_code = icd_service.getDocument(
			enterprise_id=enterprise_id, receipt_no=note_id if doc_type == "note" else receipt_no, doc_type=doc_type)
		if doc_type == "note":
			receipt_remarks = icd_service.audit_dao.db_session.query(CreditDebitNote.remarks.label("remarks")).filter(
				CreditDebitNote.id == note_id, CreditDebitNote.enterprise_id == enterprise_id).first()
		else:
			receipt_remarks = icd_service.audit_dao.db_session.query(Receipt.remarks).filter(
				Receipt.receipt_no == receipt_no, Receipt.enterprise_id == enterprise_id).first()
		response = response_code.success()
		if data and len(data) > 0:
			filename = smart_str("%s(%s).%s" % (grn_code, receipt_no, data[1]))
			if document_type == 'file':
				# mimetype is replaced by content_type for django 1.7
				response = HttpResponse(data[0], mimetype='application/force-download')
				response['Content-Disposition'] = 'attachment; filename=%s' % filename
				logger.info("File name %s " % smart_str("%s(%s).%s" % (grn_code, receipt_no, data[1])))
				return response
			else:
				response['ext'] = data[1]
				response['data'] = "data:application/%s;base64,%s" % (data[1], data[0].encode('base64'))
				response['url'] = os.path.join(os.path.dirname(__file__), (
					getFormattedDocPath(code=receipt_code, id=note_id if doc_type == "note" else receipt_no)).replace("#", "%23"))
				response['filename'] = filename
		else:
			response['custom_message'] = "No document found"
			response['data'] = ""
		response['remarks'] = receipt_remarks.remarks if receipt_remarks and receipt_remarks.remarks else []
	except Exception as e:
		logger.exception("GRN Document %s" % e.message)
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def returnOnAudit(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		grn_number = rh.getPostData('grn_number')
		user_id = rh.getPostData('user_id')
		remarks = rh.getPostData('audit_remarks')
		logger.info('remarks %s' % remarks)
		return_status = rh.getPostData('return_status')
		note_id = rh.getPostData('note_id')
		if return_status is None:
			return_status = Receipt.STATUS_ICD_CHECKED
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		if not user_id:
			user_id = rh.getSessionAttribute(SESSION_KEY)
		if not grn_number and not note_id:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')

		logger.info('Fetching pending GRN of enterprise %s' % enterprise_id)
		data = ICDService().returnNote(
			enterprise_id=enterprise_id, receipt_no=grn_number, user_id=user_id, remarks=remarks, return_status=return_status,
			note_id=note_id)
		if data:
			if data == 'ALREADY_UPDATED':
				response = response_code.failure()
				response['custom_message'] = data
			else:
				response = response_code.success()
				response['grn_number'] = grn_number
				response['custom_message'] = data
		else:
			response = response_code.databaseError()
	except Exception as e:
		logger.info("Failed to return note... %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def verifyNote(request):
	"""

	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getPostData('enterprise_id')
		grn_number = request_handler.getPostData('grn_number')
		note_id = request_handler.getPostData('note_id')
		icd_remarks = request_handler.getPostData('icd_remarks')
		user_id = request_handler.getPostData('user_id')
		project_code = request_handler.getPostData('project_code')
		user_name = request_handler.getSessionAttribute(SESSION_USERNAME_KEY)
		if not enterprise_id:
			enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		if not user_id:
			user_id = request_handler.getSessionAttribute(SESSION_KEY)
		if not grn_number and not note_id:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')

		# Verifying note
		receipt_no, is_bill_exist = ICDService().verifyNote(
			enterprise_id=enterprise_id, receipt_no=grn_number, user_id=user_id, user_name=user_name
			, icd_remarks=icd_remarks, note_id=note_id, project_code=project_code)
		if receipt_no:
			response = response_code.success()
			if is_bill_exist:
				ledger_bill = StoresService().getLedgerBill(enterprise_id=enterprise_id, receipt_no=receipt_no)
				voucher_codes = []
				for settlement in ledger_bill.settlements:
					if settlement.voucher.type_id is 4 or 6:
						voucher_codes.append(settlement.voucher.getCode())
				response['custom_message'] = """Voucher - <b><i>%s</i></b> and Bill <b><i>%s</i></b> are linked with this Note. 
								Unfortunately system will not handle the perpetuation of these changes to the linked documents, 
								hence need to be addressed manually.""" % (
					", ".join(voucher_codes), ledger_bill.bill_no)
				logger.warn("Trying to edit invoice %s that is linked to %s" % (ledger_bill.bill_no, voucher_codes))
			else:
				response['custom_message'] = "Note verified Successfully"
			response['grn_number'] = grn_number
			response['grn_code'] = receipt_no
		elif note_id:
			response = response_code.success()
			response['custom_message'] = "Note verified Successfully"
		else:
			response = response_code.databaseError()
			response['custom_message'] = "Receipt number is none"
	except Exception as e:
		logger.exception("Verify Note failed %s" % e.message)
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def superEditNoteCode(request):
	"""
	Super user can edit ian code through this api
	:param request:
	:return:
	"""
	try:
		requestHandler = RequestHandler(request)
		enterprise_id = requestHandler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		user_id = requestHandler.getSessionAttribute(SESSION_KEY)
		receipt_no = requestHandler.getPostData("receipt_no")

		new_financial_year = requestHandler.getPostData("new_financial_year")
		new_code = int(requestHandler.getPostData("new_code"))
		new_sub_number = requestHandler.getPostData("new_sub_number")
		response = ICDService().superEditNoteCode(
			enterprise_id=enterprise_id, user_id=user_id, receipt_no=receipt_no, new_financial_year=new_financial_year,
			new_code=new_code, new_sub_number=new_sub_number)

	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def updateNotePartyStatus(request):
	"""
	Sets the Note status as party acknowledged.
	:param request:
	:return: JSON dumps to the Ajax call that initiated this paryt acknowledgement
	"""
	logger.info("Note Party Acknowledgement Status Update Triggered...")
	try:
		icd_service = ICDService()
		token = None
		status = ""
		time_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
		if 'aicd' in request.GET:
			token = request.GET['aicd']
		if 'status' in request.GET:
			status = request.GET['status']
		if token:
			response = response_code.success()
			decoded_token = jwt.decode(token, JWT_SECRET)
			note_id = decoded_token['note_id']
			note_no = decoded_token['note_no']
			receipt_no = decoded_token['receipt_no']
			if decoded_token['expired_on'] >= time_now:
				note_to_be_approved = None
				if note_id:
					note_to_be_approved = icd_service.audit_dao.db_session.query(CreditDebitNote).filter(
						CreditDebitNote.id == note_id).first()
				if note_to_be_approved.status in (3, 7, -2):
					message = "Note - %s has already been %s on %s." % (
						note_no, "acknowledged" if note_to_be_approved.status in (3, 7) else "rejected", note_to_be_approved.party_revised_on)
				else:
					if status == 'party_acknowledged':
						response = icd_service.partyStatusNote(
							grn_number=receipt_no, note_id=note_id, party_status=Receipt.STATUS_PARTY_ACKNOWLEDGED,
							user_id=note_to_be_approved.last_modified_by, enterprise_id=note_to_be_approved.enterprise_id)
						if response['response_message'] == "Success":
							message = "Note %s has been acknowledged" % note_no
						else:
							message = "Party Acknowledgement of Note %s has failed" % note_no
					else:
						response = icd_service.partyStatusNote(
							grn_number=receipt_no, note_id=note_id, party_status=Receipt.STATUS_PARTY_REJECTED,
							user_id=note_to_be_approved.last_modified_by, enterprise_id=note_to_be_approved.enterprise_id)
						if response['response_message'] == "Success":
							message = "Note %s has been rejected" % note_no
						else:
							message = "Rejection of Note %s has failed" % note_no
			else:
				message = "Note - %s has been expired on %s." % (note_no, decoded_token['expired_on'])
		else:
			response = response_code.failure()
			message = "Token verification failed"
	except Exception as e:
		response = response_code.internalError()
		logger.exception('Error in Note Party Acknowledgement Status update: %s' % e.message)
		message = "Party Acknowledgement Status Update Failed..."
	response.update({"message": message, "current_version": CURRENT_VERSION})
	return render_to_response(properties.NOTE_STATUS_TEMPLATE, response, context_instance=RequestContext(request))


def updateNoteStatusforParty(request):
	"""
	Sets the Note status as party acknowledged.
	:param request:
	:return: JSON dumps to the Ajax call that initiated this acknowledgement
	"""
	logger.info("Note Party Status Update Triggered...")
	request_handler = RequestHandler(request)
	icd_service = ICDService()
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	grn_number = request_handler.getPostData('grn_number')
	note_id = request_handler.getPostData('note_id')
	status = request_handler.getPostData('status')
	remarks = request_handler.getPostData('remarks')
	note = icd_service.audit_dao.getNote(enterprise_id=enterprise_id, note_id=note_id)
	try:
		response = response_code.success()
		if note and (note.status == 7 or note.status == -2):
			message = "Note - %s has already been %s on %s." % (
				note.getCode(), "acknowledged" if note.status == 7 else "rejected",
				note.party_revised_on)
		else:
			if status == 'party_acknowledged':
				response = icd_service.partyStatusNote(
					grn_number=grn_number, note_id=note_id, party_status=Receipt.STATUS_PARTY_ACKNOWLEDGED, user_id=user_id,
					enterprise_id=enterprise_id, remarks=remarks)
				if response['response_message'] == "Success":
					message = "Note %s has been acknowledged" % note.getCode() if note else ""
				else:
					message = "Party Acknowledgement of Note %s has failed" % note.getCode() if note else ""
			else:
				response = icd_service.partyStatusNote(
					grn_number=grn_number, note_id=note_id, party_status=Receipt.STATUS_PARTY_REJECTED,  user_id=user_id,
					enterprise_id=enterprise_id, remarks=remarks)
				if response['response_message'] == "Success":
					message = "Note %s has been rejected" % note.getCode() if note else ""
				else:
					message = "Rejection of Note %s has failed" % note.getCode() if note else ""
		response['custom_message'] = message
		response['code'] = note.getCode() if note else ""
		response['receipt_no'] = grn_number if grn_number else note_id
	except Exception as e:
		logger.exception('Error in Note Party Status update: %s' % e)
		response = response_code.internalError()
		response['error'] = "Party Status Update Failed..."
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def checkPendingAckICD(request):
	"""
	:param request:
	:return: JSON dumps to the Ajax call that initiated this acknowledgement
	"""
	logger.info("Fetch Party Pending/Acknowledged Note Triggered...")
	request_handler = RequestHandler(request)
	icd_service = ICDService()
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	try:
		response = response_code.success()
		note_count = icd_service.audit_dao.getPendingAckNote(enterprise_id=enterprise_id)
		response['note_count'] = note_count[0]
		return HttpResponse(json.dumps(response), 'content-type=text/json')
	except Exception as e:
		logger.exception('Error in fetching Party Pending/Acknowledged Note: %s' % e)
		response = response_code.internalError()
		response['error'] = "Fetching Party Pending/Acknowledged Note Failed..."
		return HttpResponse(json.dumps(response), 'content-type=text/json')
