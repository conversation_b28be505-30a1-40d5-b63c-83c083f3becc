{% extends "template.html" %}
{% block sidebar %}

{% if access_level.view %}
	{% block purchaseHome %}{% endblock %}
	{% block purchase_orders %}{% endblock %}
	{% block edit_po %}{% endblock %}
	{% block po_reports %}{% endblock %}
	{% block material_wise_reports %}{% endblock %}
	{% block quick_po %}{% endblock %}
	{% block po_edit %}{% endblock %}
	{% block POCustomReport %}{% endblock %}
	{% block projects %}{% endblock %}
	{% block project_fore_cast %}{% endblock %}
{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
<script>
	$(".slide_container_part").removeClass('selected');
	if ($("title").text().trim() == "Purchase Order") {
		$('#li_purchase_order').addClass('active');
	}
	$(".slide_container_part").removeClass('selected');
	if ($("title").text().trim() == "Job Order") {
		$('#li_job_order').addClass('active');
	}
	$(".slide_container_part").removeClass('selected');
	if ($("title").text().trim() == "Work Order") {
		$('#li_work_order').addClass('active');
	}
	$(".slide_container_part").removeClass('selected');
	if ($("title").text().trim() == "Purchase Order" || $("title").text().trim() == "Job Order") {
		$("#menu_purchase").addClass('selected');
	}
	if ($("title").text().trim() == "Work Order") {
		$("#menu_production_planning").addClass('selected');
	}
</script>
{% endblock %}