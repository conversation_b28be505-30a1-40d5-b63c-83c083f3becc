# Updated requirements for modern Python 3.10+ and Django 4.x
# Core Django and web framework
Django==4.2.7
django-cors-headers==4.3.1

# Database connectors
PyMySQL==1.1.0
pymongo==4.6.0
SQLAlchemy==2.0.23

# Caching and task queue
redis==5.0.1
celery==5.3.4
kombu==5.3.4

# HTTP and API clients
requests==2.31.0
requests-toolbelt==1.0.0
httplib2==0.22.0
urllib3==2.1.0

# Google Cloud services
google-api-core==2.14.0
google-api-python-client==2.108.0
google-auth==2.23.4
google-auth-httplib2==0.1.1
google-cloud-core==2.3.3
google-cloud-storage==2.10.0
google-cloud-vision==3.4.5
google-resumable-media==2.6.0
googleapis-common-protos==1.61.0
grpcio==1.59.3

# Cryptography and security
cryptography==41.0.7
PyJWT==2.8.0
pyasn1==0.5.1
pyasn1-modules==0.3.0
rsa==4.9

# Document processing
reportlab==4.0.7
Pillow==10.1.0
pdfkit==1.0.0
opencv-python==4.8.1.78
qrcode==7.4.2
python-barcode==0.15.1
lxml==4.9.3

# Data processing
numpy==1.24.4
pandas==2.0.3
xlrd==2.0.1
openpyxl==3.1.2
xlwt==1.3.0

# Utilities
python-dateutil==2.8.2
pytz==2023.3
six==1.16.0
Unidecode==1.3.7
python-Levenshtein==0.23.0
fuzzyset==0.0.19
num2words==0.5.13
texttable==1.7.0
simplejson==3.19.2
xmltodict==0.13.0
python-dotenv==1.0.0
cachetools==5.3.2

# Firebase and push notifications
pyfcm==1.5.4

# Currency and financial
currencyconverter==0.17.13

# Development and testing
certifi==2023.11.17
chardet==5.2.0
idna==3.6

# Compatibility and backports (removed old Python 2 specific packages)
# enum34, futures, contextlib2, pathlib2, scandir, configparser - not needed in Python 3.10+

# Data comparison and serialization
deepdiff==6.7.1
jsonpickle==3.0.2

# Statistics (using built-in statistics module in Python 3.10+)
# statistics==******* - removed, using built-in

# Additional modern packages
zipp==3.17.0
protobuf==4.25.1