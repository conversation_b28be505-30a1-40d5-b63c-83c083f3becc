from conf import *

logger = logging.getLogger(os.path.splitext(os.path.basename(__file__))[0])
logger.setLevel(logging.DEBUG)
logger.addHandler(logHandler)

CHECK_TABLE_EXISTS_QUERY = """
SELECT COUNT(*) as table_count
FROM information_schema.tables
WHERE table_schema = '%s' 
AND table_name = 'material_pricing';
""" % DB_NAME

CREATE_TABLE_QUERY = """
CREATE TABLE material_pricing (
    enterprise_id INT NOT NULL,
    item_id INT NOT NULL,
    stored_price DECIMAL(30, 2) NULL,
    purchase_price DECIMAL(30, 2) NULL,
    purchase_currency_id INT NULL,
    approved_price DECIMAL(30, 2) NULL,
    approved_currency_id INT NULL,
    supplier_id INT NULL,
    PRIMARY KEY (enterprise_id, item_id));"""

def database_updates(enterprise_id=None, item_id=None, stored_price=None, purchase_price=None, purchase_currency_id=None,
                     approved_price=None, approved_currency_id=None,supplier_id=None):
    try:
        UPDATE_VALUES_QUERY = """
            UPDATE material_pricing
            SET stored_price = %s, purchase_price = %s, purchase_currency_id = %s, approved_price = %s, 
            approved_currency_id = %s, supplier_id = %s WHERE enterprise_id = %s AND item_id = %s;"""
        update_query_data = (stored_price, purchase_price, purchase_currency_id, approved_price,
                             approved_currency_id, supplier_id, enterprise_id, item_id)

        INSERT_VALUES_QUERY = """
                        INSERT INTO material_pricing (enterprise_id, item_id, stored_price, purchase_price, 
                        purchase_currency_id, approved_price, approved_currency_id, supplier_id)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s);"""
        insert_query_data = (enterprise_id, item_id, stored_price, purchase_price, purchase_currency_id,
                             approved_price, approved_currency_id, supplier_id)

        ITEM_CHECK = """
                SELECT COUNT(*) as is_item FROM material_pricing 
                WHERE enterprise_id = {enterprise_id} AND item_id = {item_id};
            """.format(enterprise_id=enterprise_id, item_id=item_id)

        item_check = executeQuery(ITEM_CHECK, as_dict=True)
        is_item = item_check[0]['is_item']

        if is_item:
            executeQuery(UPDATE_VALUES_QUERY, query_data=update_query_data)
        else:
            executeQuery(INSERT_VALUES_QUERY, query_data=insert_query_data)

        logger.info("Successfully updated all data")

    except Exception as e:
        logger.error("during update or insert operation: %s", e, exc_info=True)


def material_pricing(enterprise_id=None,item_id=None, stored_price=None):
    try:
        current_date_time = datetime.now().strftime('%Y-%m-%d') + ' 11:59:59'

        query = """
            SELECT
                gm.inv_rate as latest_purchase_price,
                g.inv_currency_id as purchase_currency_id
            FROM
                grn AS g
                    JOIN
                grn_material AS gm ON gm.grnNumber = g.grn_no
                    JOIN
                materials AS m ON m.id = gm.item_id
            WHERE
                g.enterprise_id = {enterprise_id}
                    AND rec_against = 'Purchase Order'
                    AND inv_type = 2
                    AND m.is_stocked = 1
                    AND m.in_use = 1
                    AND m.id = {item_id}
            ORDER BY g.inward_date DESC LIMIT 1;
        """
        formatted_query = query.format(enterprise_id=enterprise_id, item_id=item_id)
        item_purchase_price = executeQuery(formatted_query, as_dict=True)

        query1 = """
            SELECT price as lowest_approved_price, supp_id, currency_id as approved_currency_id 
            FROM supplier_materials_price 
            WHERE (effect_till IS NULL OR effect_till >= '{current_date_time}') 
              AND status = 1 
              AND approved_on IS NOT NULL 
              AND item_id = {item_id}
              AND enterprise_id = {enterprise_id}
              AND effect_since IS NOT NULL order by price limit 1;
        """
        formatted_query1 = query1.format(enterprise_id=enterprise_id, item_id=item_id,
                                         current_date_time=current_date_time)
        item_approved_price = executeQuery(formatted_query1, as_dict=True)

        stored_price = stored_price if stored_price else None
        purchase_price = item_purchase_price[0].get('latest_purchase_price') if item_purchase_price else None
        purchase_currency_id = item_purchase_price[0].get('purchase_currency_id') if item_purchase_price else None
        approved_price = item_approved_price[0].get('lowest_approved_price') if item_approved_price else None
        approved_currency_id =item_approved_price[0].get('approved_currency_id') if item_approved_price else None
        supplier_id = item_approved_price[0].get('supp_id') if item_approved_price else None

        database_updates(enterprise_id=enterprise_id, item_id=item_id, stored_price=stored_price,
                         purchase_price=purchase_price, purchase_currency_id=purchase_currency_id,
                         approved_price=approved_price, approved_currency_id=approved_currency_id,
                         supplier_id=supplier_id )

    except Exception as e:
        logger.error("In material_pricing function : %s", e, exc_info=True)

def materials_list():
    try:
        table_check = executeQuery(CHECK_TABLE_EXISTS_QUERY, as_dict=True)
        is_table = table_check[0]['table_count']

        if not is_table:
            logger.info("Table does not exist. Creating table.")
            executeQuery(CREATE_TABLE_QUERY)
        else:
            logger.info("Table already exists.")

        query = ("SELECT enterprise_id, id, price FROM materials "
                 "WHERE enterprise_id in(102, 103) AND is_stocked = 1 AND in_use = 1 ;")  # If want the list of all materials to remove the enterprise_id from this query,
        result = executeQuery(query, as_dict=True)

        for data in result:
            enterprise_id = data['enterprise_id']
            item_id = data['id']
            stored_price = data['price']
            material_pricing(enterprise_id=enterprise_id, item_id=item_id, stored_price=stored_price)
            break
    except Exception as e:
        logger.error("In materials_list function :%s" % e, exc_info=True)

if __name__ == "__main__":
    materials_list()



