.welcome-text {
    padding: 8px;
    font-size: 16px;
    text-align: center;
    border: dashed 1px #209be1;
    color: #004195;
    background: rgba(32, 155, 225,0.15);
    letter-spacing: 0.8px;
}

.form-register .steps ul {
    display: flex;
    display: -webkit-flex;
    list-style: none;
    padding-left: 334px;
}
.form-register .steps li,
.form-register .steps li.current {
	outline: none;
	-o-outline: none;
    -ms-outline: none;
    -moz-outline: none;
    -webkit-outline: none;
    position: relative;
}
.form-register .steps li .current-info {
	display: none;
}
.form-register .steps li a {
	text-decoration: none;
	outline: none;
	-o-outline: none;
    -ms-outline: none;
    -moz-outline: none;
    -webkit-outline: none;
}
.form-register .steps li a .title span {
	display: block;
}
.form-register .steps li a .title .step-icon { 
    width: 60px;
    height: 60px;
    border-radius: 50%;
    -o-border-radius: 50%;
    -ms-border-radius: 50%;
    -moz-border-radius: 50%;
    -webkit-border-radius: 50%;
    background: #ccc;
    margin: 0 auto;
    position: relative;
    outline: none;
    -o-outline: none;
    -ms-outline: none;
    -moz-outline: none;
    -webkit-outline: none;
    color: #fff;
    font-size: 25.6px;
    margin-right: 200px;
}
.form-register .steps li a .step-icon i {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
}
.form-register .steps li .step-icon::before,
.form-register .steps li:last-child .step-icon::after {
	position: absolute;
	content: "";
	background: #e5e5e5;
	width: 200px;
	height: 2px;
	top: 50%;
	transform: translateY(-50%);
	-o-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
}
.form-register .steps li .step-icon::before {
	right: 100%;
}
.form-register .steps li:last-child .step-icon::after {
    left: 100%;
}

.form-register .steps li.current a .step-icon,
.form-register .steps li.current a:active .step-icon,
.form-register .steps li.done a .step-icon,
.form-register .steps li.done a:active .step-icon {
    background-image: -moz-linear-gradient( 136deg, #004195 0%, #209be1 50%, #004195 100%);
    background-image: -webkit-linear-gradient( 136deg, #004195 0%, #209be1 50%, #004195 100%);
    background-image: -ms-linear-gradient( 136deg, #004195 0%, #209be1 50%, #004195 100%);
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.25);
	-o-box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.25);
    -ms-box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.25);
    -moz-box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.25);
    -webkit-box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.25);
}
.form-register .steps .current .step-icon::before,
.form-register .steps .current:last-child .step-icon::after,
.form-register .steps .done .step-icon::before {
	background-image: -moz-linear-gradient( 0deg, #004195 0%, #209be1 50%, #004195 100%);
    background-image: -webkit-linear-gradient( 0deg, #004195 0%, #209be1 50%, #004195 100%);
    background-image: -ms-linear-gradient( 0deg, #004195 0%, #209be1 50%, #004195 100%);
}
.form-register .steps li a .step-text {
	color: #999;
	font-weight: 400;
	font-size: 14px;
	padding:  14px 0 8px;
}
.form-register .steps .current .step-text,
.form-register .steps .done .step-text {
	color: #333;
}

.registration-container-list span.profile-label {
    /*width: 200px;
    vertical-align: top;*/
    float: left;
}

.registration-container-list span.profile-value {
    /*width: calc(100% - 218px);
    float: left;*/
    vertical-align: top;
    margin-left: 8px;   
    word-break: break-all;
}

.registration-container-list span.profile-colon {
    float: right;
}

span.profile-colon {
    float: none;
}

/*.indv-registration-details {
    width: 50%;
}*/

.registration-container-list .indv-registration-details {
    width: 100%;
}

#edit_enterprise_modal {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

#edit_enterprise_modal .modal-content {
    overflow-y: auto;
}

#edit_enterprise_modal .custom-error-message {
    position: inherit;
}

#edit_enterprise_modal .error-border + label {
  color: #dd4b39;
}


.do-do-switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.do-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
    position: inherit;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    text-transform: none;
    -webkit-transition: .4s;
    transition: .4s;
    padding-top: 4px;
    padding-left: 7px;
    letter-spacing: 2px;
    color: #FFF;
    width: 64px;
    display: block;
    padding: 3px 9px;
    height: 30px;
}

.slider:before {
    position: inherit;
    content: "Don't";
    background-color: transparent;
    -webkit-transition: .4s;
    transition: .4s;
}

input:checked + .slider:before {
    content: "Do";
}

input:checked + .slider {
  background-color: #209be1;
   width: 40px;
    
}

input:focus + .slider {
  box-shadow: 0 0 1px #209be1;
}

input:checked + .slider:before {
  -webkit-transform: translateX(34px);
  -ms-transform: translateX(34px);
  transform: translateX(34px);
  width: 0;
}

input + .slider:before {
  -webkit-transform: translateX(34px);
  -ms-transform: translateX(34px);
  transform: translateX(34px);
  width: 0px;
}


.btn-next,
.btn-prev {
    background: #209be1;
    color: #fff;
    border-radius: 50px;
    padding: 6px 11px;
    font-size: 16px;
}

.line-divider {
    border-top: 1px dashed #004195;
    padding: 0;
    margin-bottom: 26px;
    margin-top: 6px;
    margin-left: -14px;
    width: calc(100% + 30px);
}

.cropped_image {
    border:  dashed 1px transparent;
    padding:  4px;
}

.cropped_image:hover {
    border:  dashed 1px #999;
}

.financial-content {
    width: 20%;
    float: left;
    border: solid 1px #999;
    padding: 15px;
    text-align: center;
    font-size: 16px;
    min-height: 120px;
    margin-left: -1px;
}

.financial-content-header {
    min-height: 62px;
    text-transform: uppercase;
}

.financial-content.active {
    border-bottom-color:  transparent;
}

.financial-config {
    border: solid 1px #999;
    width: calc(100% - 4px);
    margin-left: -1px;
    border-top: none;
    padding-top: 2px;
}

.financial-content.activated .financial-content-header{
    color: #44ad6b;
}

.financial-content.activated.active,
.financial-config.activated {
    background: rgba(68, 173, 107,0.2);
}

.financial-config.activated label {
    color: #44ad6b;
}

.floating-label { 
    position:relative; 
    margin-bottom: 22px;
}
.floating-input , .floating-select {
    font-size: 14px;
    padding: 6px 12px;
    display: block;
    width: 100%;
    height: 34px;
    background-color: transparent;
    border: navajowhite;
    box-shadow: 0 0 !important;
    border-bottom: 1px solid #ccc;
    border-radius: 0;
    padding-left: 3px;
    outline: none;
}

.floating-input:focus , .floating-select:focus {
    outline:none;
}

.floating-label label {
    color:#999; 
    font-weight:normal;
    position:absolute;
    pointer-events:none;
    left:4px;
    top:9px;
    transition:0.2s ease all; 
    -moz-transition:0.2s ease all; 
    -webkit-transition:0.2s ease all;
}

.floating-input:focus ~ label, .floating-input:not(:placeholder-shown) ~ label,
.floating-select:focus ~ label , .floating-select:not([value=""]):valid ~ label {
    top: -18px;
    color: #777;
    left: 4px;
    font-size: 11px;
}

/* active state */
.floating-input:focus ~ .bar:before, .floating-input:focus ~ .bar:after, .floating-select:focus ~ .bar:before, .floating-select:focus ~ .bar:after {
    width:50%;
}

*, *:before, *:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.floating-textarea {
    min-height: 30px;
    max-height: 260px; 
    overflow:hidden;
    overflow-x: hidden; 
}

.registraion-value {
    width: 65%;
    border-radius: 0 6px 6px 0;
    float: left;
    font-size: 12px;
    border-left: none;
    font-size:  12px;
    padding-right: 25px;
}

.registraion-key {
    width: 35%;
    border-radius: 6px 0 0 6px; 
    float: left;
    text-shadow: 0 0 #ccc;
    font-size: 14px;
}

.payment-details .payment-percentage {
    width: 18%;
    float: left;
    text-shadow: 0 0 #ccc;
    font-size:  16px;
}

.payment-details .payment-days {
    width: 18%;
    float: left;
    text-shadow: 0 0 #ccc;
    font-size:  16px;
}

.payment-details .payment-remarks {
    width: 40%;
    float: left;
    text-shadow: 0 0 #ccc;
    font-size:  16px;
}

.enterprice-item-heading {
    background: #209be1;
    font-size: 13px;
    margin-bottom: 16px;
    margin-left: 4px;
    color: #fff;
    padding: 4px 12px;
    margin-left: 15px;
    border-radius: 26px;
}

.address-container {
    margin: 0; 
    padding: 35px 15px 0px; 
    border: solid 1px #209be1; 
    margin-top: -30px; 
    margin-bottom: 15px;
}

.address-container .custom-error-message {
    line-height: 15px;
}

.multi-wrapper .non-selected-wrapper, 
.multi-wrapper .selected-wrapper {
    height: 300px;
}

./*profile-value.inr:after {
    content: "(₹)"
}

.profile-value.eur:after {
    content: "(€)"
}

.profile-value.usd:after {
    content: "($)"
}*/

.chat-border {
    
}

.reg-chat-container {
    margin-bottom: 15px;
    font-size: 18px;
    width: 100%;
    float: left;
}

.chat-img-char {
    font-size:  22px;
    float: none !important;
}

.chat-border {
    position: relative;
    background: #cfeeff;
    /* height: 100px; */
    max-width: 500px;
    margin-left: -2px;
    border-radius: 3px;
    padding: 6px 15px;
    margin-top: -6px;
    display: inline-block;
    min-width: 200px;
}

.reg-chat-container.no-arrow {
     margin-top: -7px;
}

.reg-chat-container.no-arrow .chat-border:after {
    border-color:  transparent;
}

.no-arrow .chat-img {
    visibility: hidden;
}

.chat-border:after{
    content: '';
    position: absolute;
    border: 10px solid transparent;
    border-top: 10px solid #cfeeff;
    top: 0px;
    left: -10px;
}

.chat-img {
    padding: 0px 0px 6px 9px;
    border: #209be1 solid 1px;
    height: 32px;
    border-radius: 50px;
    width: 32px;
    color: #fff;
    font-size: 16px;
    margin-top: -2.5px;
    text-transform: uppercase;
    display: inline-block;
    vertical-align: top;
    color:  #209be1;
    font-weight: bold;
    font-size: 22px;
}

.chat-right span {
    float: right;
}

.chat-right span.chat-border {
    padding: 4px 10px 10px;
    margin-right: 6px;
}

.chat-right .chat-border:after{
    right: -10px;
    left: 0;
}

.switch_radio_btn {
    width:  100%;
}

.switch_radio_btn a{
    text-shadow: 0px 0px #000;
    font-size:  18px;
    padding: 4px 15px;
    width:  50%;
    margin-top: 6px;
}

.switch_radio_btn .noActive{
    color: #3276b1;
    background-color: #fff;
}

.switch_radio_btn .active {
    color: #fff;
    background-color: #004195;   
}

.switch_radio_btn .noActive:hover {
    color: #fff;
    background-color: rgba(0, 65, 149,0.5);
}

.switch_radio_btn.disabled {
    opacity: 0.6;
    pointer-events: none;
}


.financial-container,
.financial-container a {
    font-family: 'PT Sans', sans-serif;
    letter-spacing: 1px;
}

.financial-indv-container {
    border-bottom: 1px dashed #666; 
    padding-bottom: 15px;
    margin-top: 30px;
}

.chat-img img {
    width: 16px; 
    margin-top: -5px;
    margin-left: -2px;
}

.address-container .fa-whatsapp{
    color:#999;
    position: absolute;
    right: 24px;
    top: 7px;
    font-size: 20px;
}

.remove-contact-details {
    color: #dd4b39;
    position: absolute;
    top: 10px;
    right: 15px;
    font-size:  18px;
}

.fa-whatsapp.whatsapp-enable{
    color:#25D366;
}

.contact-authorized {
    position: absolute;
}

div.wave {
    position: relative;
    width: 100px;
    height: 20px;
    margin-left: 7px;
    transform: translateY(0px);
}
 div.wave .dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: -2px;
    background: #666;
    animation: wave 1.3s linear infinite;
}
 div.wave .dot:nth-child(2) {
     animation-delay: -1.1s;
}
 div.wave .dot:nth-child(3) {
     animation-delay: -0.9s;
}
 @keyframes wave {
     0%, 60%, 100% {
         transform: initial;
    }
     30% {
         transform: translateY(-12px);
    }
}

.sc_loading .chat-border {
    width: 80px;
    min-width: 80px
}

.overlay-import-btn:hover {
    box-shadow: inset 0px 0px 20px #ccc;
}

.overlay-import-btn a:hover {
    text-decoration: none;
}

.delete-contact-container {
    color: #dd4b39;
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 18px;
}

.other_contact_details_indv {
    border-top: 1px solid #ccc;
    padding: 8px 0 0;
    margin: 8px 0;
}

.show_more_contact {
    font-size: 12px;
    margin-top: 15px;
    text-align: right;
    color: #004195;
    cursor: pointer;
}





.configurable-checkbox.item-disabled .item-checkbox {
    display: none;
}
.configurable-checkbox.item-disabled .text-color {
    color: #999;
}
.configurable-checkbox.item-disabled .box-color {
    fill: #ccc;
}
.configurable-checkbox.item-disabled .ellipse-color {
    fill: #ccc;
}

.configurable-checkbox .ellipse-color,
.purchase_configration_settings path,
.expense_configration_settings path,
.expense_configration_settings font {
    transition: all 0.4s;
}

.configurable-checkbox:hover .ellipse-color {
    rx:  18;
    ry:  18;
    fill:  red;
}

.configurable-checkbox.configurable-checkbox-small:hover .ellipse-color {
    rx:  10;
    ry:  10;
    fill:  red;
}

.purchase_configration_settings:hover path,
.expense_configration_settings:hover path {
    fill: red;
}

.expense_configration_settings:hover font {
    color:  red;
    font-weight: bold;
}

.view-profile-container .mandate-visible {
    display: block !important;
    padding-left: 7px;
}