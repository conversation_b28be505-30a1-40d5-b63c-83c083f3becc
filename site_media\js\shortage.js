function generateShortageReport(){
    $("#apply-shortage-list").click(function(){
        $('#loading').show();
        if($("#report-table").hasClass("dataTable")) {
            $('#report-table').DataTable().clear();
            $('#report-table').DataTable().destroy();
        }   
        $('#report-table thead tr').remove();
        $('#report-table tbody tr').remove();
        var selected_boms=""
        var include_po = $("#include_item_order").is(":checked")
        $("#shortage-list-table tbody tr").each(function() {
            selected_boms += $(this).find("td[name='material_cat_code']").text() + "[:]" + $(this).find("td[name='material_qty']").text() + "[:]"
        });

		var headerRow =`<th rowspan="2" style="width: 60px; min-width: 60px; max-width: 60px;" class="align-middle">S.No.</th>
						<th rowspan="2" style="width: 250px; min-width: 250px; max-width: 250px;" class="align-middle">Name</th>
                        <th rowspan="2" style="width: 150px; min-width: 150px; max-width: 150px;" class="align-middle">Drawing No</th>
						<th rowspan="2" style="width: 60px; min-width: 60px; max-width: 60px;" class="align-middle">UOM</th>
						<th rowspan="2" style="width: 100px; min-width: 100px; max-width: 100px;">Available Stock </th>
						<th rowspan="2" style="width: 100px; min-width: 100px; max-width: 100px;">Required</th>
						<th rowspan="2" style="width: 100px; min-width: 100px; max-width: 100px;">Shortage</th>`

		// This <tr> is used for csv download. Don't delete
        var csv_header =  `<th hidden="hidden" style="width: 60px; min-width: 60px; max-width: 60px;">S. No</th>
                            <th hidden="hidden" style="width: 250px; min-width: 250px; max-width: 250px;">Name</th>
                            <th hidden="hidden" style="width: 150px; min-width: 150px; max-width: 150px;">Drawing No</th>

                            <th hidden="hidden" style="width: 60px; min-width: 60px; max-width: 60px;">UOM</th>
                            <th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">Available Stock - Qty</th>
                            <th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">Required - Qty</th>
                            <th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">Shortage - Qty</th>`
        $.ajax({
            url: "/erp/stores/materialshortagelist/",
            type: "post",
            datatype: "json",
            data: {"cat_list": selected_boms, "include_po": include_po,"location_id" : JSON.stringify($('#location_id').val())},
            success: function(response) {
                try {
                    if (response.response_message == "Internal server error") {
                        swal("", response.custom_message, "error")
                    } else if (response["data"] != "") {
                        var materials = response["data"]
                        var bom_header = response["catalogue_materials_name"]
                        var subHeaderRow =  ""
                        if(include_po == true){
                            headerRow = `${headerRow} <th style="width: 100px; min-width: 100px; max-width: 100px;" align="center" rowspan="2">Pending PO</th>`
                            csv_header = `${csv_header} <th hidden="hidden">Pending PO</th>`
                        }
                        for(i=0; i<response["bom_count"]; i++){
                            headerRow = `${headerRow} <th style="width: 300px; min-width: 300px; max-width: 300px;" align="left" colspan="2">${bom_header[i]}</th>`
                            subHeaderRow = `${subHeaderRow} <th style="width: 100px; min-width: 100px; max-width: 100px;" align="left">Required qty</th><th style="width: 100px; min-width: 100px; max-width: 100px;" align="left">Shortage qty</th>`
                            csv_header = `${csv_header}<th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">${bom_header[i]} - Required qty</th><th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">${bom_header[i]} - Shortage qty</th>`
                        }
                        headerRow += `<th rowspan="2" style="visibility: hidden; border: none;"></th>`;
                        csv_header += `<th style="display: none;"></th>`;
                        $('#report-table thead').append(`<tr class="exclude_export tr-report-header">${headerRow} </tr>`)
                        $('#report-table thead').append(`<tr class="exclude_export tr-report-subheader">${subHeaderRow} </tr>`)
                        $('#report-table thead').append(`<tr class="tr-report-download">${csv_header}></tr>`)
                        shortagelist_construct = {}
                        for(i =1; i<= materials.length;i++){
                            var item = materials[i-1]
                            shortagelist_construct[item.item_id] = item.present_qty
                           var itemTypeFlag = "";
                            var item_name = item.item_name;
                            if(item.is_service == true){
                                itemTypeFlag += `<span class="service-item-flag"></span>`;
                            }
                            if(item.is_stockable == 0 && item.is_service != true){
                               itemTypeFlag += `<span class='non_stock-flag'></span>`;
                            }
                            if(typeof(Storage) !== "undefined") {
                                if (sessionStorage.clickcount) {
                                    sessionStorage.clickcount = Number(sessionStorage.clickcount) + 1;
                                } else {
                                    sessionStorage.clickcount = 1;
                                }
                            }
                            var requ_qty=""
                            $.each(item.contains, function(i, contains) {
                            requ_qty += contains.cat_code+ "[:]" + contains.req_qty+ "[:]"
                            });

                            if(item.hasChildren) {
                                dataP = item.item_id+ "_" + sessionStorage.clickcount
                                item_parent_material = item.contains[1]["bom_material"]
                                sessionStorage.setItem('bom_header', JSON.stringify(bom_header))
                                childArrow = '<i class="fa fa-plus fa-for-arrow" role="button" onclick="return appendMaterial(\''
                                + item.item_id + '\',\'' + sessionStorage.clickcount + '\',\'' + dataP + '\',\'' + item_parent_material +  '\',\'' + requ_qty + '\')"><a style="padding-left: 26px;display: block;margin-top: -12px;">'+item_name+itemTypeFlag+'</a></i>';
                            } else {childArrow = item_name+""+itemTypeFlag;}

                            var row = `<td class='bom-sno text-left'>${ i }</td>
                                <td class='text-left'>
                                    ${childArrow}
                                </td>
                                <td class='text-left'>${item.drawing_no}</td>
                                <td class="text-center">${item.unit}</td>
                                <td class="text-right field-changes restore-values item-available-qty" data-default-value="${item.present_qty.toFixed(2)}">${item.present_qty.toFixed(2)}</td>
                                <td class="text-right field-changes restore-values item-required-qty" data-default-value="${item.required_qty.toFixed(2)}">${item.required_qty.toFixed(2)}</td>
                                <td class="text-right field-changes restore-values item-shortage-qty" data-default-value="${item.shortage_qty.toFixed(2)}">${item.shortage_qty.toFixed(2)}</td>`
                            var bom_materials = item.contains
                            if(include_po == true){
                                 row = `${row} <td align="center">${item.pending_po.toFixed(2)}</td>`
                            }
                            const bom_material_values = Object.values(bom_materials);
                            result_set = {}
                            bom_material_values.forEach(function (bom_material){
                                for(j=0; j < response["bom_count"]; j++){
                                    if(bom_material.bom_material == bom_header[j] && (!result_set[bom_header[j]] || result_set[bom_header[j]] == false)){
                                          result_set[bom_header[j]] = bom_material
                                    }else if(!result_set[bom_header[j]]){
                                          result_set[bom_header[j]] = false
                                    }
                                }
                            });
                            for(const key in result_set){
                                const bom_material = result_set[key]
                                row = (bom_material ? `${row}
                                            <td align="right" class="field-changes restore-values bom_required_qty" data-default-value="${bom_material.req_qty.toFixed(2)}">${bom_material.req_qty.toFixed(2)}</td>
                                            <td align="right" class="field-changes restore-values bom_shortage_material" data-default-value="${bom_material.short_qty.toFixed(2)}">${bom_material.short_qty.toFixed(2)}</td>` : `${row}
                                            <td align="right"> - </td>
                                            <td align="right"> - </td>`)
                            }
                            $('#report-table tbody').append(`<tr data-toggle='close' data-padding='0' align="center" valign="middle" data-id="${ item.item_id }" data-parent= "${ item.item_id }_${ sessionStorage.clickcount }" id= "${ item.item_id }_${ sessionStorage.clickcount }" >${row} <td style="min-width: 40px; visibility: hidden; border: none;"></td></tr>`);
                        }
                        sessionStorage.setItem("shortagelist_construct" , JSON.stringify(shortagelist_construct))
                    }
                    else{
                    $('#report-table tbody').append(`<tr><th class="text-center" style="width: 100px; min-width: 100px; max-width: 100px">No Matching Results Found!</td></tr>`);
                    }
                } catch(e) {
                    console.log(e);
                } finally {
                    $('#loading').hide();
//                    setDragableSno();
//                    TableHeaderFixed();
                }
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });

        function setDragableSno() {
            var dragRowSno = 0;
            $('#shortage-list-table .drag_serial_number').each(function(){
                $(this).text(Number(++dragRowSno));
            });
        }
        $('.material_shortage_report').removeClass("hide");
    });
}

function appendMaterial(cat_code, dataChild, dataP, parent_material_name, requ_qty){
    var include_po = $("#include_item_order").is(":checked")
    var dataParent = dataP;
    var bom_header = JSON.parse(sessionStorage.getItem('bom_header'))
    var shortagelist_construct = JSON.parse(sessionStorage.getItem("shortagelist_construct"))
    var current_bom_sno = $("#"+dataP).find(".bom-sno").text().trim();
    var constructedRow = '';
    var bom_count = bom_header.length
    var headerRow =`<th rowspan="2" style="width: 60px; min-width: 60px; max-width: 60px;" class="align-middle">S.No.</th>
                    <th rowspan="2" style="width: 250px; min-width: 250px; max-width: 250px;" class="align-middle">Name</th>
                    <th rowspan="2" style="width: 150px; min-width: 150px; max-width: 150px;" class="align-middle">Drawing No</th>
                    <th rowspan="2" style="width: 60px; min-width: 60px; max-width: 60px;" class="align-middle">UOM</th>
                    <th rowspan="2" style="width: 100px; min-width: 100px; max-width: 100px;">Available Stock </th>
                    <th rowspan="2" style="width: 100px; min-width: 100px; max-width: 100px;">Required</th>
                    <th rowspan="2" style="width: 100px; min-width: 100px; max-width: 100px;">Shortage</th>`

    // This <tr> is used for csv download. Don't delete
    var csv_header =  `<th hidden="hidden" style="width: 60px; min-width: 60px; max-width: 60px;">S. No</th>
                        <th hidden="hidden" style="width: 250px; min-width: 250px; max-width: 250px;">Name</th>
                        <th hidden="hidden" style="width: 150px; min-width: 150px; max-width: 150px;">Drawing No</th>

                        <th hidden="hidden" style="width: 60px; min-width: 60px; max-width: 60px;">UOM</th>
                        <th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">Available Stock - Qty</th>
                        <th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">Required - Qty</th>
                        <th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">Shortage - Qty</th>`

    var currentCatChild = $("#"+cat_code + "_" + dataChild).attr('id');
    if($("#"+currentCatChild).attr('data-toggle') == 'open') {
        var curPadding = $("#"+currentCatChild).attr('data-padding');
        $("#"+currentCatChild).nextAll('tr').each(function(){
            if(Number($(this).attr('data-padding')) > Number(curPadding)) {
                $(this).remove();
            }
            else {
                return false;
            }
        });
        $("#report-table #"+cat_code + "_" + dataChild).find('.fa.fa-for-arrow').removeClass('fa-minus').addClass('fa-plus');
        $("#report-table #"+cat_code + "_" + dataChild).removeClass('exclude_export');
        $("#report-table #"+cat_code + "_" + dataChild).attr('data-toggle','close');

        $("#report-table #"+cat_code + "_" + dataChild).find(".restore-values").each(function(){
            var default_value_qty = ($(this).attr('data-default-value'));
            $(this).text(default_value_qty);
        });
        $("#"+dataP).find(".field-changes").removeClass('disabled-field').addClass('enabled-field');
        shortageListConstruct();
    }
    else {
        $("#report-table #"+cat_code + "_" + dataChild).find('.fa.fa-for-arrow').removeClass('fa-plus').addClass('fa-minus');
        $("#report-table #"+cat_code + "_" + dataChild).addClass('exclude_export');
        $("#report-table #"+cat_code + "_" + dataChild).attr('data-toggle','open');

        $("#"+dataP).find(".restore-values").text("0.00");
        $("#"+dataP).find(".field-changes").removeClass('enabled-field').addClass('disabled-field');
        $("#loadingmessage_changelog_listing_ie").show();

        $.ajax({
            url: "/erp/stores/append_generate_Shortage_ListReport/",
            type: "post",
            datatype: "json",
            data: {"cat_list": requ_qty, "include_po": include_po, "cat_code": cat_code},
            success: function(response) {
                try {
                    if (response.response_message == "Internal server error") {
                        swal("", response.custom_message, "error")
                    }
                    else {
                        var materials = response["data"]
                        var subHeaderRow =  ""

                        if(include_po == true){
                            headerRow = `${headerRow} <th style="width: 100px; min-width: 100px; max-width: 100px;" align="center" rowspan="2">Pending PO</th>`
                            csv_header = `${csv_header} <th hidden="hidden">Pending PO</th>`
                        }
                        for(i=0; i<bom_count; i++){
                            headerRow = `${headerRow} <th style="width: 300px; min-width: 300px; max-width: 300px;" align="left" colspan="2">${bom_header[i]}</th>`
                            csv_header = `${csv_header} <th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">${bom_header[i]} - Required qty</th><th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">${bom_header[i]} - Shortage qty</th>`
                        }
                        csv_header += `<th style="display: none;"></th>`;
                        for(i =1; i<= materials.length;i++){
                            var item = materials[i-1]
                            shortagelist_construct[item.item_id] = item.present_qty

                            if(typeof(Storage) !== "undefined") {
                                if (sessionStorage.clickcount) {
                                    sessionStorage.clickcount = Number(sessionStorage.clickcount)+1;
                                } else {
                                    sessionStorage.clickcount = 1;
                                }
                            }
                            var itemTypeFlag = "";
                            var item_name = item.item_name;
                            if(item.is_service == true){
                                itemTypeFlag += `<span class="service-item-flag"></span>`;
                            }
                            if(item.is_stockable == 0 && item.is_service != true){
                               itemTypeFlag += `<span class='non_stock-flag'></span>`;
                            }
                            var dataPadding = Number($("#"+cat_code + "_" + dataChild).data('padding') + 30);
                            var requ_qty=""
                            $.each(item.contains, function(i, contains) {
                            requ_qty += contains.cat_code+ "[:]" + contains.req_qty+ "[:]"
                            });

                            if(item.hasChildren) {
                                dataP = item.item_id+ "_" + sessionStorage.clickcount
                                item_parent_material = parent_material_name
                                sessionStorage.setItem('bom_header', JSON.stringify(bom_header))
                                childArrow = '<i class="fa fa-plus fa-for-arrow" role="button" onclick="return appendMaterial(\''
                                + item.item_id + '\',\'' + sessionStorage.clickcount + '\',\'' + dataP + '\',\'' + item_parent_material +  '\',\'' + requ_qty + '\')"><a  style="padding-left: 26px;display: block;margin-top: -12px;">'+item_name+itemTypeFlag+'</a></i>';
                            } else {childArrow = item_name+""+itemTypeFlag;}

                            var row = `<td class='bom-sno text-left'>${current_bom_sno}.${ i }</td>
                                <td class='text-left' style='padding-left:${dataPadding-20}px'>
                                <span style='padding-left:30px; display: block' class='tree-view'>
                                    ${childArrow}
                                </span>
                                </td>
                                <td class='text-left'>${item.drawing_no}</td>
                                <td class="text-center">${item.unit}</td>
                                <td class="text-right field-changes restore-values item-available-qty" data-default-value="${item.present_qty.toFixed(2)}">${item.present_qty.toFixed(2)}</td>
                                <td class="text-right field-changes restore-values item-required-qty" data-default-value="${item.required_qty.toFixed(2)}">${item.required_qty.toFixed(2)}</td>
                                <td class="text-right field-changes restore-values item-shortage-qty" data-default-value="${item.shortage_qty.toFixed(2)}">${item.shortage_qty.toFixed(2)}</td>`
                            var bom_materials = item.contains
                            if(include_po == true){
                                 row = `${row} <td align="center">${item.pending_po.toFixed(2)}</td>`
                            }
                            const bom_material_values = Object.values(bom_materials);
                            result_set = {}
                            bom_material_values.forEach(function (bom_material){
                                for(j=0; j < bom_count; j++){
                                    if(bom_material.bom_material == bom_header[j] && (!result_set[bom_header[j]] || result_set[bom_header[j]] == false)){
                                          result_set[bom_header[j]] = bom_material
                                    }else if(!result_set[bom_header[j]]){
                                          result_set[bom_header[j]] = false
                                    }
                                }
                            });
                            for(const key in result_set){
                                const bom_material = result_set[key]
                                row = (bom_material ? `${row}
                                            <td align="right" class="field-changes restore-values bom_required_qty" data-default-value="${bom_material.req_qty.toFixed(2)}">${bom_material.req_qty.toFixed(2)}</td>
                                            <td align="right" class="field-changes restore-values bom_shortage_material" data-default-value="${bom_material.short_qty.toFixed(2)}">${bom_material.short_qty.toFixed(2)}</td>` : `${row}
                                            <td align="right"> - </td>
                                            <td align="right"> - </td>`)
                            }
                            constructedRow += `<tr data-toggle='close' data-padding="${ dataPadding }" align="center" valign="middle" data-id="${ item.item_id }" data-parent= "${ dataParent }" id= "${ item.item_id }_${ sessionStorage.clickcount }" data-child="${ item.cat_code }_${ dataChild }">${row} <td style="min-width: 40px; visibility: hidden; border: none;"></td></tr>`;
                        }
                        sessionStorage.setItem("shortagelist_construct" , JSON.stringify(shortagelist_construct))
                        $('#report-table #'+dataParent).after(constructedRow);
                        shortageListConstruct();
                    }
                } catch(e) {
                    console.log(e);
                } finally {
                    $('#loading').hide();
  $("#loadingmessage_changelog_listing_ie").hide();
                }
            },

    });
}
}

function shortageListConstruct(){
    var shortagelist_data = JSON.parse(sessionStorage.getItem("shortagelist_construct"));
    $.each(shortagelist_data, function(item_id, available_qty) {
        var currentAvaialbeQty = available_qty;
        $("#report-table").find("tr[data-toggle='close']").each(function(){
            if (item_id == $(this).attr('data-id')){
                $(this).find(".item-available-qty").text(currentAvaialbeQty.toFixed(2));
                var bom_cur_ava = $(this).find(".item-available-qty").text()
                $(this).find(".bom_required_qty").each(function(){
                    var bom_cur_req = $(this).text()
                    var bom_cur_balance = Number(bom_cur_ava) -  Number(bom_cur_req)
                    var bom_shortQty = 0;

                    if (bom_cur_balance >= 0){
                        bom_shortQty = 0
                        bom_cur_ava = bom_cur_balance;
                    }
                    else {
                        bom_shortQty = bom_cur_balance * (-1);
                        bom_cur_ava = 0;
                    }
                    $(this).next("td").text(Number(bom_shortQty).toFixed(2));
                });

                var balanceQty = Number(currentAvaialbeQty) - Number($(this).find(".item-required-qty").text()).toFixed(2);
                var shortageQty = 0;
                if(balanceQty > 0) {
                    shortageQty = 0;
                    currentAvaialbeQty = balanceQty;
                }
                else {
                    shortageQty = balanceQty * (-1);
                    currentAvaialbeQty = 0;
                }
                $(this).find(".item-shortage-qty").text(Number(shortageQty).toFixed(2));
            }
        });
    });
}