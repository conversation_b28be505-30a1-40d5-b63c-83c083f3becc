/**
 * If newList contains any new item the callback will be invoked
 */
var hasNewItem = function (oldList=[], newList=[], callback=function (dummy) {
    console.log("hasNewItem() is invoked without callback");}) {
    for (i = 0; i < newList.length; i++) {
        if (oldList.indexOf(newList[i]) == -1) {
            callback(newList[i]);
            break;
        }
    }
}

/**
 * If newList contains any new item the callback will be invoked
 */
var forEachNewItem = function (oldList=[], newList=[], callback=function (dummy) {
    console.log("forEachNewItem() is invoked without callback");}) {
    for (i = 0; i < newList.length; i++) {
        if (oldList.indexOf(newList[i]) == -1) {
            callback(newList[i]);
        }
    }
}

/**
 * If newList contains any new item the callback will be invoked
 */
var forEachMatchedItem = function (oldList=[], newList=[], callback=function (dummy) {
    console.log("forEachMatchedItem() is invoked without callback");}) {
    for (i = 0; i < newList.length; i++) {
        if (oldList.indexOf(newList[i]) != -1) {
            callback(newList[i]);
        }
    }
}
/************* Application level Utility  ends here *************/

/**
 *  Updates Page Header as like "Goods Receipt > 44939 (Draft No.)"
 */
function updatePageTitle() {
    var receiptCode = $("#grnnolbl").text().trim();
    if(receiptCode == "") {
        receiptCode = $("#orderno").text()+ " (Draft No.)";
    }
    if (receiptCode != "") {
        $(".page_header_grn").html('<span class="header_current_page"> '+receiptCode+' </span>');
        $(".page-title").html($("#id-page_header").val());
    }
}

/**
 * Update fields visibility on page load
 */
function updateInitialVisibility() {
    var url = window.location.href;
    url = url.substring(7)
    window.urlpath = url.substring(0, url.indexOf('/'));
    customCheckbox("custom[]");
    $('#orderno').hide();
    $('#grnnolbl').hide();
    $('#view_receipt_document').hide();
}

/**
 * Initializing view page; call this function in ready event
 */
function initializePage() {
  //console.log('initalize page');
    // UI functions
    updateInitialVisibility();
    initListeners();
    onChangeReceivedAgainst();
    inlineAddMaterialToTable();
    grnSuperEditInit();
    $("#txtnetvalue").val('0')

	// Data functions
	if($("#recagainst").val() != "Issues") {
	    loadGstTaxes();
	    loadTaxDetails();
	}
	if ($("#id_receipt_no").val() == "") {
	    loadPo("");
	}
	if($("#recagainst").val() == "Purchase Order" || $("#recagainst").val() == "Job In" || $("#recagainst").val() == "Job Work"){
	    loadMaterial($("#recagainst").val(), "onload");
	}
	if($("#materialtable tbody .item-for-goods tr").length >= 1){
        $(".item-for-goods").removeClass('hide');
	}

	if($("#materialtable tbody .item-for-service tr").length >= 1){
        $(".item-for-service").removeClass('hide');
	}
}

/**
 * Loads and display tags for the given url and params
 * @see loadPoTags(), loadInvoiceTags()
 */
function loadTags(url, params) {
    $("#grn_tags_table .li-tagit-display").remove();
    $.ajax({
        url: url, type: "post", datatype: "json", data: params,
        success: function (response) {
            $.each(response, function (i, item) {
                var tag = {
                    label: item.tag_name == undefined ? item[0]: item.tag_name,
                    id: item.tag_id == undefined ? item[1]: item.tag_id
                }
                var row = "<li class='li-tagit-display'>" +
                    "<label class='label-tagit-display'>"+tag.label+"</label>"+
                    "<div class='hidden-div-tagit-id' hidden='hidden'>"+ tag.id +"</div>"+
                    "<a class='delete_tag' ></a>"  + "</li>";
                $(row).insertBefore('#grn_tags_table .text_for_tag');
            });
            create_delete_tag_button();
        }
    });
}

function loadPoTags(params) {
    loadTags("/erp/purchase/json/po/load_po_tags/", {po_ids: params});
}

function loadInvoiceTags(params) {
    loadTags("/erp/sales/json/invoice/load_invoice_tags/", {dc_ids: params});
}

function loadOaTags(params) {
    loadTags("/erp/sales/json/invoice/load_oa_tags/", {oa_ids: params});
}

function showOcrConfirmBox() {
	confirmAction(message="Please let the system identify data from your receipt", callback=function(isConfirm) {
        if (isConfirm) {
           	$("#invoice_copy_uploader").trigger("click");
		    setTimeout(function() {
		        autoLoadInvoice();
		    }, 3000);
        }
    }, "Invoice Auto Load", 1);
}

function autoLoadInvoice(){
    $("#loading").show();
    var formData = new FormData(document.getElementById("avatar_form"));
    $.ajax({
        url: "/erp/stores/grn/save_uploaded_file/",
        type: "POST",
        data: formData,
        processData: false,
        contentType: false,
        dataType: "json",
        success: function (invoiceDetails) {
            try {
                $('#supplier').val(invoiceDetails["partyId"]+"[::]"+invoiceDetails["partyConfigFlag"]);
                $('#supplier').trigger("chosen:updated");

                $('#project').val(invoiceDetails["projectCode"]);
                $('#project').trigger("chosen:updated");



                $("#pos").multiselect('destroy');
                var items = "<option value="+invoiceDetails["poOrderId"]+"> "+invoiceDetails["poNumber"]+" </option>";
                $("#pos").html(items);
                $("#pos").multiselect();
                $("#pos").multiselect('select', invoiceDetails["poOrderId"]);
                document.getElementById("invdate").value =invoiceDetails["invoicedate"];
                UpdateSingleDate('invdate');
                $("#loading").hide();
            } catch(err) {
                $("#loading").hide();
            }
        }
    });
}

/************* Super Edit GRN Number starts here *************/
function grnSuperEditInit() {
    if($("#is_super_user").val().toLowerCase() == 'true') {
        if($("#id_receipt_no").val() == "") {
            $(".super_user_icon, .xsid_number_edit, .super_user_tool_tip").remove();
            $(".super_edit_for_confirm, .super_edit_for_draft").addClass("hide");
            $(".super_edit_for_load").removeClass("hide");
        }
        else if($(".header_current_page").text().indexOf("Draft") >=0) {
            $(".super_user_icon, .xsid_number_edit, .super_user_tool_tip").addClass("hide");
            $(".super_edit_for_confirm").addClass("hide");
            $(".super_edit_for_load, .super_edit_for_draft").removeClass("hide");
        }
        else {
            $(".super_user_icon, .super_user_tool_tip").removeClass("hide");
            $(".super_edit_for_load, .super_edit_for_draft, .super_edit_for_confirm").removeClass("hide");
        }
        var numChars = 3;
        if (moduleName == "Sales Return") numChars = 2;
        $('.super_user_tool_tip span').qtip({
            content: {
                text: `<ul class='ul_for_tool_tip'><li>Super-edit enables modifying the ${moduleName} Code subject to Duplication Check.</li>
                   <li>Code format will be 'FY-FY/${moduleName}/NNNNNNx', <br />eg. '18-19/${moduleName}/000731b'.<br/>
                   FY details - 5 characters (max), <br />${moduleName} Type - ${numChars} characters, <br />Number - 6 digits (max), <br />Sub-number - 1 alphabet (max).</li>
                   <li>Subsequent numbering of ${moduleName} will pick from the highest of the ${moduleName} Code, <br/> i.e., gaps will not be filled automatically.</li></ul>`,
                title: 'Super-Edit'
            }
        });
    }
    else{
          if($("#id_receipt_no").val() == "") {
            $(".super_user_icon, .xsid_number_edit, .super_user_tool_tip").remove();
            $(".super_edit_for_confirm, .super_edit_for_draft").addClass("hide");
            $(".super_edit_for_load").removeClass("hide");
        }
        else if($(".header_current_page").text().indexOf("Draft") >=0) {
            $(".super_user_icon, .xsid_number_edit, .super_user_tool_tip").addClass("hide");
            $(".super_edit_for_confirm").addClass("hide");
            $(".super_edit_for_load, .super_edit_for_draft").removeClass("hide");
        }
        else {
            $(".super_user_icon, .super_user_tool_tip").removeClass("hide");
            $(".super_edit_for_load, .super_edit_for_draft").removeClass("hide");
        }
    }
}

function editGrnNumber() {
    if($('#chkVerified').hasClass("selected") || $("#recagainst").val() == "Job In") {
        $.ajax({
                url: "/erp/stores/json/get_receipt_linked_message/",
                method: "POST",
                data:{
                    receipt_no: $("#id_receipt_no").val()
                },
                success: function(response) {
                    if (response.response_message =="Success") {
                        if(response.custom_message == "") {
                           superEditGrnNumber();
                        } else {
                            confirmAction(message=response.custom_message, callback=function(isConfirm) {
                                if (isConfirm) {
                                   superEditGrnNumber();
                                }
                            });
                        }
                     } else {
                        swal({title: "", text: response.custom_message, type: "error"});
                     }
                },
                error: function (xhr, errmsg, err) {
                    swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
                }
            });

    }else if($('#chkChecked').hasClass("selected")) {
        var warning = `${moduleName} is already checked. Unfortunately system will not handle the perpetuation of these changes to the linked note, hence need to be addressed manually.<br/> Do you want to continue?`
        confirmAction(message=warning, callback=function(isConfirm) {
            if (isConfirm) {
                superEditGrnNumber();
            }
        });
    } else {
        superEditGrnNumber();
    }
}

function superEditGrnNumber(){
    var grnNumber = $(".header_current_page").text().trim();
    var grnNumberSplit = grnNumber.split("/");
    $("#grn_financial_year").val(grnNumberSplit[0]);
    $("#grn_type").val(grnNumberSplit[1]);
    if($.isNumeric(grnNumberSplit[2].substr(-1))){
        $("#grn_number").val(grnNumberSplit[2]);
    }
    else {
        $("#grn_number").val(grnNumberSplit[2].slice(0, -1));
        $("#grn_number_division").val(grnNumberSplit[2].substr(-1));
    }
    $(".xsid_number_edit").removeClass("hide");
    $(".super_user_icon, .header_current_page").addClass("hide");
}

function discardEditGrnNumber(){
    $(".xsid_number_edit").addClass("hide");
    $(".super_user_icon, .header_current_page").removeClass("hide");
    $("#grn_financial_year, #grn_number, #grn_number_division").val("");
    $("#grn_type option").removeAttr("selected");
}

function superEditGrnSelect(field){
    $(field).closest("label").next("select").removeAttr("disabled");
    $('.chosen-select').trigger('chosen:updated');
    $(field).addClass("hide");
    event_action = "Super-Edit";
}

function saveGrnNumber(){
    if($("#grn_financial_year").val() =="" || $("#grn_number").val() == "" || $("#grn_type").val() == "") {
        $(".save_xsid_error_format").removeClass("hide");
        if($("#grn_financial_year").val() == "") $("#grn_financial_year").addClass("super_edit_error_border");
        if($("#grn_number").val() == "") $("#grn_number").addClass("super_edit_error_border");
    }
    else {
        $(".save_xsid_error_format").addClass("hide");
        $("#grn_number_division").val($("#grn_number_division").val().toLowerCase());
        $.ajax({
            url: "/erp/stores/json/super_edit_receipt_code/",
            method: "POST",
            data:{
                receipt_no: $("#id_receipt_no").val(),
                new_financial_year: $("#grn_financial_year").val().trim(),
                new_grn_type: $("#grn_type").val().trim(),
                new_receipt_code: $("#grn_number").val().trim(),
                new_sub_number: $("#grn_number_division").val(),
            },
            success: function(response) {
                if (response.response_message == "Success") {
                    ga('send', 'event', moduleName, 'Super-Edit Code', $('#enterprise_label').val(), 1);
                    swal({title: "", text: response.custom_message, type: "success"},
                    function() {
                        discardEditGrnNumber();
                        $(".header_current_page").text(response.code);
                    });
                } else {
                    swal({title: "", text: response.custom_message, type: "warning"});
                }
            },
            error: function (xhr, errmsg, err) {
               swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
            }
        });
    }
}
/************* Super Edit GRN number ends here *************/

function resetMaterialSelectionVisibility (receivedAgainst) {
    if ("Job In" == receivedAgainst || ($("#is_purchase_order_mandatory").val() == "False" && ["Purchase Order", "Job Work"].indexOf(receivedAgainst) != -1)) {
        if($("#goods_already_received").is(":checked")) {
            $("#materialrow").addClass("hide");
        }
        else {
            $("#materialrow").removeClass("hide");
        }
    } else {
        $("#materialrow").addClass("hide");
    }
}

function onChangeReceivedAgainst() {
    $(".OANumbers").addClass("hide");
    $(".OANumbers").html("");
    var receivedAgainst = $("#recagainst").val();
    $('#div_dc_invoice').show();
    $('#div_indtype').removeClass("hide");
    $('#div_issued_to').hide();
    $('#div_issueno').hide();
    $(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();
    $("#switch_dc_no").removeClass("disabled");
    checkGoodsAlreadyReceived(receivedAgainst);
    $(".indent_details_position").css({display: "inline-block"});
    $("#po_div .mandatory_mark").removeClass("hide");
    if (receivedAgainst == "Job Work" ) {
        $("#po_name_display").text("Job Order No");
        $("#mat_received a").trigger('click');
        if($("#goods_already_received").is(":checked")){
            $("#mat_returned").hide();
        }else{
            $("#mat_returned").show();
        }
        $("#mat_received").show();
    } else if (receivedAgainst == "Delivery Challan") {
        $("#po_name_display").text("DC No");
        $("#dc_issue_title").text("DC No");
        $("#mat_returned a").trigger('click');
        $("#mat_received").hide();
        $("#mat_returned").show();
        $('#div_indtype').addClass("hide");
        $("#div_pp_no").addClass("hide");
    } else if (receivedAgainst == "Issues") {
        $("#dc_issue_title").text("Issue No");
        $("#mat_returned, #mat_received").show();
        $("#mat_received a").trigger('click');
        $("#materialrow").removeClass("hide");
        $(".received_against_margin").addClass("hide");
        $("#inv_det").addClass("hide");
        $('#div_indtype').addClass("hide");
        $("#div_pp_no").removeClass("hide");
    } else if (receivedAgainst == "Sales Return") {
        $("#po_name_display").text("Invoice No");
        $("#mat_received a").trigger('click');
        $("#mat_received").show();
        $("#mat_returned").hide();
        $('#div_indtype').addClass("hide");
        $("#div_pp_no").addClass("hide");
    } else if (receivedAgainst == "Job In") {
        $("#po_name_display").text("Job OA No");
        $("#mat_received").show();
        $("#mat_received a").trigger('click');
        $("#mat_returned").hide();
        $("#materialrow").removeClass("hide");
        $("#po_div .mandatory_mark").addClass("hide");
        $("#div_pp_no").addClass("hide");
    } else {
        $("#po_name_display").text("Purchase Order No");
        $("#mat_received").show();
        $("#mat_received a").trigger('click');
        $("#mat_returned").hide();
        $("#materialrow").removeClass("hide");
        $("#div_pp_no").addClass("hide");
    }

    if (["Issues", "Others"].indexOf(receivedAgainst) != -1) {
        loadMaterial(receivedAgainst, "onload");
        $("#inv_det").removeClass("hide");
        resetMaterialTables();
        constructOtherReceiptMaterialTHeader();
        $("#materialrow").removeClass("hide");
        $('.invoicerow').hide();
        $('#taxrow').hide();
        $('#po_div').hide();
        $("#pos").prop('disabled', true);
        $("#pos").multiselect('destroy');
        $("#pos").multiselect();
        $('#inv_det').hide();
        $('#checkinvoicediv').hide();
        $("#dc_invoice").val("0");
        $("#switch_dc_no a[data-original-title='Invoice']").removeClass("noActive").addClass("active");
        $("#switch_dc_no a[data-original-title='DC']").addClass("noActive").removeClass("active");
        $("#party_div").addClass("hide");
        $('#div_dc_invoice').hide();
        $('#div_indtype').addClass("hide");
        $("#div_currency_details").addClass("hide");
        if(receivedAgainst == "Issues") {
            $('#rec_from_div').hide();
            $(".received_against_option").addClass("hide");
            $('#div_issued_to').show();
            $('#div_issueno').show();
        } else {
            $("#supplier .frequently_used_supplier").hide();
            $("#supplier .frequently_used_jobwork_supplier").hide();
            $("#supplier .frequent_job_work_in_party_list").hide();
            $("#supplier .other_frequently_used_supplier").show();
            $("#supplier .frequently_used_dc_supplier").hide();
            if($('#supplier optgroup:eq(0) option:eq(0)').val()==undefined) {
                $("#supplier").val(0).trigger("chosen:updated");
            } else {
                $("#supplier").val($('#supplier .other_frequently_used_supplier:eq(0)').val()).trigger("chosen:updated");
            }
            $('#rec_from_div').show();
            $(".received_against_option").removeClass("hide");
            $(".indent_details_position").css({display: "block"});
        }
    }
    else {
        $("#supplier .frequently_used_supplier").hide();
        $("#supplier .other_frequently_used_supplier").hide();
        $("#supplier .frequently_used_jobwork_supplier").hide();
        $("#supplier .frequent_job_work_in_party_list").hide();
        $("#supplier .frequently_used_dc_supplier").hide();
        $("#supplier .frequently_used_salesreturn_supplier").hide();
        $("#supplier optgroup option:first").attr("selected", "selected");

        if(receivedAgainst == 'Purchase Order') {
            $( "#supplier .frequently_used_supplier").show();
            if($('#supplier optgroup:eq(0) option:eq(0)').val()==undefined) {
                $("#supplier").val(0).trigger("chosen:updated");
            } else {
                $("#supplier").val($('#supplier .frequently_used_supplier:eq(0)').val()).trigger("chosen:updated");
            }
        }
        else if (receivedAgainst == 'Sales Return') {
            $("#supplier .frequently_used_salesreturn_supplier").show();
            if($('#supplier optgroup:eq(0) option:eq(0)').val()==undefined) {
                $("#supplier").val(0).trigger("chosen:updated");
            } else {
                $("#supplier").val($('#supplier .frequently_used_salesreturn_supplier:eq(0)').val()).trigger("chosen:updated");
            }
        }
        else if (receivedAgainst == 'Job Work') {
            $( "#supplier .frequently_used_jobwork_supplier").show();
            if($('#supplier optgroup:eq(0) option:eq(0)').val() == undefined) {
                $("#supplier").val(0).trigger("chosen:updated");
            } else {
                $("#supplier").val($('#supplier .frequently_used_jobwork_supplier:eq(0)').val()).trigger("chosen:updated");
            }
        }
        else if (receivedAgainst == 'Job In') {
            $('#id-oaYears').html("");
            $('#id-oaYears').multiselect();
            $('#div_indtype').addClass("hide");
            $("#switch_dc_no a[data-original-title='DC']").removeClass("noActive").addClass("active");
            $("#switch_dc_no a[data-original-title='Invoice']").addClass("noActive").removeClass("active");
            $("#dc_invoice").val("1")
            $("#switch_dc_no").addClass("disabled");
            $( "#supplier .frequent_job_work_in_party_list").show();
            if($('#supplier optgroup:eq(0) option:eq(0)').val() == undefined) {
                $("#supplier").val(0).trigger("chosen:updated");
            } else {
                $("#supplier").val($('#supplier .frequent_job_work_in_party_list:eq(0)').val()).trigger("chosen:updated");
            }
        }
        else {
            $( "#supplier .frequently_used_dc_supplier").show();
            if($('#supplier optgroup:eq(0) option:eq(0)').val() == undefined) {
                $("#supplier").val(0).trigger("chosen:updated");
            } else {
                $("#supplier").val($('#supplier .frequently_used_dc_supplier:eq(0)').val()).trigger("chosen:updated");
            }
        }
        resetMaterialTables();
        constructPoReceiptMaterialTHeader(receivedAgainst);
        resetMaterialSelectionVisibility(receivedAgainst);
        $("#pos").prop('disabled', false);
        $('#checkinvoicediv').show();
        $('.invoicerow').show();
        $('#taxrow').show();
        $(".received_against_option").removeClass("hide");
        $('#inv_det').show();
        $("#div_currency_details").removeClass("hide");
        if($("#goods_already_received").is(":checked")) {
            $('#po_div').hide();
            new MaterialManager().loadGoodsReceivedDcNumbers();
        } else {
            $('#po_div').show();
        }
        $('#rec_from_div').show();
    }
    $(".item-for-goods, .item-for-service").addClass('hide');
}

function initListeners() {
   //console.log("init listener");
    $("#switch_dc_no a").on('click', function(){
        var selected = $(this).data('title');
        var toggle = $(this).data('toggle');
        $('#'+toggle).prop('value', selected);
        $('a[data-toggle="'+toggle+'"]').not('[data-title="'+selected+'"]').removeClass('active').addClass('noActive');
        $('a[data-toggle="'+toggle+'"][data-title="'+selected+'"]').removeClass('noActive').addClass('active');
        $("#div_dc_invoice .custom-error-message").remove();
        $("#switch_dc_no a").removeClass("error-border");
        if(selected == 2) {
            $(".party_dc_text").html('Party Invoice No<span class="mandatory_mark"> *</span>');
            $("#invno").attr("placeholder", "Enter Invoice No");
            $(".party_date_text").text("Party Invoice Date");
        }
        else {
            $(".party_dc_text").html('Party DC No<span class="mandatory_mark"> *</span>');
            $("#invno").attr("placeholder", "Enter DC No");
            $(".party_date_text").text("Party DC Date");
        }
    });

    $("#id_note_preview").click(function(){
        $("#id_note_preview").text("Processing...");
        setTimeout(function(){
            if ($("#grn_status").val() > 1 && $("#grn_status").val() < 4){
                generate_pdf_ajax($("#id_note_id").val().trim(), $("#grn_status").val())
            }else{
                load_grn_note_preview();
                $("#grn_note_preview-modal").modal("show");
            }
        },10);
    });

    $("#add_inspector_button").click(function(){
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();
        var ControlCollections = [
            {
                controltype: 'textbox',
                controlid: 'id_inspector',
                isrequired: true,
                errormsg: 'Inspector name is required.'
            }
        ];
        var result = JSCustomValidator.JSvalidate(ControlCollections);
        return result;
    });

    $("#inspector").change(function () {
        if ($("#inspector").val()=='add_new_inspector') {
            $('#add_inspector').modal('show');
            $("#id_inspector").val("");
            setTimeout(function(){
                $("#id_inspector").focus();
            },1000)
        }
    });

    $("#addPartyButton").click(function(){
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();
        var ControlCollections = [
            {
                controltype: 'textbox',
                controlid: 'code',
                isrequired: true,
                errormsg: 'Code is required.'
            },
            {
                controltype: 'textbox',
                controlid: 'name',
                isrequired: true,
                errormsg: 'Name is required.'
            },
            {
                controltype: 'textbox',
                controlid: 'email',
                isemail: true,
                emailerrormsg: 'Invalid Email ID.'
            }
        ];
        var result = JSCustomValidator.JSvalidate(ControlCollections);
        if(result) {
            if($(this).attr('id') == 'addPartyButton'){
                CmdSave();
            }
        }
        return result;
    });

    $("select#id_invoice-issued_to").change(function(){
        $("#issueno").html("");
        if(trim($("#id_invoice-issued_to").val()) != ""){
            loadPo($("#id_receipt_no").val().trim());
            $("#div_issueno").find(".custom-error-message").remove();
            $("#div_issueno").find(".error-border").removeClass("error-border");
        }
    });

    $("select#supplier").change(function() {
        if ($("#id_receipt_no").val().trim() != ""){
            receipt_no = $("#id_receipt_no").val().trim();
        }else{
            receipt_no = "";
        }

        if ($("#supplier").val() == 'add_new') {
            AddNewParty();
            $("#cmdUpdateGRN").addClass("hide");
        }
        $("#po_details").val("");
        $("#party_details").val("");
        if($("#goods_already_received").is(":checked")) {
            $("#po_div").find("input[type='checkbox']").prop("checked", false);
            $("#party_DcNo").find("option").remove();
            $('#party_DcNo').multiselect('destroy').multiselect();
            $('#po_div').addClass("hide");
            new MaterialManager().loadGoodsReceivedDcNumbers();
        } else {
            loadPo(receipt_no);
        }
        lastUsedSupplierDetails();
        if($("#recagainst").val() != "Others") {
            resetMaterialTables();
        }
        ChangePartyCurrency(getSelectedSupplierId(), "id_currency", "");
        currencyChangeEvent("onchange");
    });

    $("select#recagainst").change(function() {
        // UI Changes
        onChangeReceivedAgainst();
        inlineAddMaterialToTable();
        $("#materialtable tbody.item-for-goods tr, #materialtable tbody.item-for-service tr").remove();
        // Reset the select options
        loadPo("");
    });

    $('#cmdSaveAndApprove').click(function () {
        $("#id_grn_save_approve_status").val(1);
        $("#cmdUpdateGRN").trigger("click");
    });

    $('#cmdSavegrn, #cmdUpdateGRN').click(function () {
   //  console.log("cmdSavegrn clicked");
        var receivedAgainst =  $("#recagainst").val();
        $(".error-border").removeClass('error-border');
		$(".custom-error-message, .hsn-suggestion").remove();
        isValidHSN = true;
        var isValidated = false;
        var ControlCollections = [
            {
                controltype: 'dropdown',
                controlid: 'project',
                isrequired: true,
                errormsg: 'Project is required.'
            },
            {
                controltype: 'textbox',
                controlid: 'id_gstin',
                isrequired: false,
                minvalue: 15,
	            minvalerrormsg: 'GSTIN number should be 15 characters.'
            },
            {
                controltype: 'dropdown',
                controlid: 'grn-location',
                isrequired: true,
	            errormsg: 'Location is required.'
            }
        ];
        if(receivedAgainst != "Issues"){
            var control = {
                controltype: 'dropdown',
                controlid: 'purchase_account',
                isrequired: true,
                errormsg: 'Indent Type is required.'
            };
            ControlCollections[ControlCollections.length] = control;
            var control = {
                controltype: 'textbox',
                controlid: 'dc_invoice',
                isrequired: true,
                errormsg: ''
            };
            ControlCollections[ControlCollections.length] = control;
        }
        else {
            var wo_id =  $("#id_wo").val();
            if(wo_id =="" || wo_id == 0){
                var control = {
                    controltype: 'dropdown',
                    controlid: 'id_invoice-issued_to',
                    isrequired: true,
                    errormsg: 'Issue to is required.'
                };
                ControlCollections[ControlCollections.length] = control;
                var control = {
                    controltype: 'dropdown',
                    controlid: 'issueyear',
                    isrequired: true,
                    errormsg: 'Issue year is required.',
                    margintop: "32px"
                };
                ControlCollections[ControlCollections.length] = control;
                var control = {
                    controltype: 'dropdown',
                    controlid: 'issueno',
                    isrequired: true,
                    errormsg: 'Issue no is required.',
                    margintop: "32px"
                };
                ControlCollections[ControlCollections.length] = control;
            }
            else{
                var control = {
                    controltype: 'dropdown',
                    controlid: 'id_wo',
                    isrequired: true,
                    errormsg: 'PP No. is required.'
                };
            }
        }

        if ($("#div_con_rate").is(":visible")){
            var control = {
                controltype: 'textbox',
                controlid: 'currency_conversion_rate',
                isrequired: true,
                errormsg: 'Required.',
                mindigit: 0.00001,
                mindigiterrormsg: 'Rate cannot be 0.'
            };
            ControlCollections[ControlCollections.length] = control;
        }

        var control = {

        };
        ControlCollections[ControlCollections.length] = control;

        if($("#dc_invoice").val() == "") {
            $("#switch_dc_no a").addClass("error-border")
        }
        if(["Purchase Order", "Job Work", "Delivery Challan", "Sales Return", "Job In"].indexOf(receivedAgainst) != -1){
            if($("#dc_invoice").val() == 2){
                var errorMsg = 'Party Invoice No is required.';
            }
            else {
                var errorMsg = 'Party DC No is required.';
            }
            var control = {
                controltype: 'textbox',
                controlid: 'invno',
                isrequired: true,
                errormsg: errorMsg
            };
            ControlCollections[ControlCollections.length] = control;

            if(receivedAgainst == "Purchase Order"){
                error_msg = 'PO No is required.'
            }else if(receivedAgainst == "Job Work"){
                error_msg = 'JO No is required.'
            }else{
                error_msg = 'DC No is required.'
            }

            if($("#is_purchase_order_mandatory").val() == "True" && $("#goods_already_received").is(":checked") != true && receivedAgainst != "Job In"){
		        if($("#pos").next("div").find(".multiselect-container input[type='checkbox']:checked").length == 0) {
		            $("#pos").next("div").find(".multiselect").addClass('error-border');
		            var control = {
		                controltype: 'dropdown',
		                controlid: 'pos',
		                isrequired: true,
		                errormsg: error_msg,
		                margintop: '34px'
		            };
		            ControlCollections[ControlCollections.length] = control;
		        }
            }
            if($("#goods_already_received").is(":checked")) {
                if($("#party_DcNo").next("div").find(".multiselect-container input[type='checkbox']:checked").length == 0) {
                    $("#party_DcNo").next("div").find(".multiselect").addClass('error-border');
                    var control = {
                        controltype: 'dropdown',
                        controlid: 'party_DcNo',
                        isrequired: true,
                        errormsg: 'Party DC No is required.',
                        margintop: '34px'
                    };
                    ControlCollections[ControlCollections.length] = control;
                }
            }
        }

        if(["Purchase Order", "Job Work", "Job In", "Delivery Challan", "Sales Return", "Others"].indexOf(receivedAgainst) != -1){
            var control = {
                controltype: 'dropdown',
                controlid: 'supplier',
                isrequired: true,
                errormsg: 'Received From is required.'
            };
            ControlCollections[ControlCollections.length] = control;
            if($('#is_gate_inward_no_automated').val() == "True" && $("#id_receipt_no").val() == ""){
                generateInwardNo();
            }else{
                if($('#is_gate_inward_no_mandatory').val()=="True" ){
                    var control = {
                        controltype: 'textbox',
                        controlid: 'inwardno',
                        isrequired: true,
                        errormsg: 'Gate Inward No is required.'
                    };
                ControlCollections[ControlCollections.length] = control;
                }
            }
            var control = {
                controltype: 'textbox',
                controlid: 'inwdate',
                isrequired: true,
                errormsg: 'Gate Date is required.'
            };
            ControlCollections[ControlCollections.length] = control;
            var control = {
                controltype: 'dropdown',
                controlid: 'inspector',
                isrequired: true,
                errormsg: 'Inspector Name is required.'
            };
            ControlCollections[ControlCollections.length] = control;
        }
        if($("#rejected-remarks").is(":visible")) {
            var control = {
                controltype: 'textbox',
                controlid: 'txtrejremarks',
                isrequired: true,
                errormsg: 'Rejection Remarks is required.'
            };
            ControlCollections[ControlCollections.length] = control;
        }
        $("#materialtable tbody, #dc_materialtable tbody").find("tr").each(function(){
            if($("#goods_already_received").is(":checked")){
                var currentQty = $(this).find(".total_dc_qty").text();
            }
            else {
                if($(this).find("input[name='dc_qty']").length == 0) {
                    var currentQty = $(this).find("input[name='acc_qty']").val();
                }
                else {
                    var currentQty = $(this).find("input[name='dc_qty']").val();
                }
            }

            if(currentQty > 0) {
                var currentHsn = $(this).find("input[name*='hsn_code']");
                var currentElementId = currentHsn.attr("id");
                var control = {
                    controltype: 'textbox',
                    controlid: currentElementId,
                    isrequired: true,
                    errormsg: 'HSN/SAC is required.',
                    ishsn: true,
                    hsnerrormsg: 'Invalid HSN Code',
                    suggestioncontainer: "hsn-wrapper"
                }
                ControlCollections[ControlCollections.length] = control;
            }
        });
        var result = JSCustomValidator.JSvalidate(ControlCollections);
        if(result && isValidHSN) {
            if($("#materialrequired").val() != "" && $("#materialrequired").val()) {
                if($("#qty").val() == "" || $("#job_qty").val() == 0 ||  $("#material_id").val() == "" || $("#materialrequired").val().indexOf($("#material_id").val()) < 0) {
                  $("#cmdadd").trigger('click');
                }
                else {
                    $("#cmdadd").trigger('click');
                    isValidated = true;
                }
            }
            else {
                isValidated = true;
            }
          //  console.log("isvalidate -> ",isValidated);
           if(isValidated) {
                var isEmptyValidation = isTableEmptyValidation();
                var quantityValidation = quantityValidationResult();
                if(quantityValidation && isEmptyValidation) {
                    if($('#is_unique_in_fiscal_yr').val() == "True" && ($("#inwardno").val() != "" ) && !(
                    $('#is_gate_inward_no_automated').val() == "True" && $("#id_receipt_no").val() == "") && !(
                    $('#is_gate_inward_no_automated').val() == "True" && $("#is_gate_inward_no_editable").val() == "False"
                    && $("#id_receipt_no").val() != "")){
                        validateInwardNoForFiscalYear();
                    }else{
                        checkClosingStockValue();
                    }
                }
           }
        } else {
            $("html, body").animate({ scrollTop: 0 }, "fast");
            return result;
        }
    });

    $("#chk_packing").click(function(){
        if($(this).is(':checked'))
            $("#packing_charges").prop("disabled", false);
        else {
            $("#packing_charges").prop("disabled", true);
            $("#packing_charges").val("0.00");
            Calculate();
        }
    });

    $("#chk_transport").click(function(){
        if($(this).is(':checked')) {
            $("#transport_charges").prop("disabled", false);
        } else {
            $("#transport_charges").prop("disabled", true);
            $("#transport_charges").val("0.00");
            Calculate();
        }
    });

    toggleConversionRate();

    $("#id_currency").change(function(){
         currencyChangeEvent("onchange");
     });

    $("#add_grn_tag").click(function(){
        var tag_code = $("#grn_tag_value").val();
        var tag_text = $("#id_grn_tag").val();

        if (tag_code == ""){
            if (tag_text == ""){
                return;
            }
            else {
                $(".li-tagit-display").removeClass('flash');
                var currentFieldName = $("#id_grn_tag").val();
                $("#grn_tags_table").find('.li-tagit-display:visible').each(function(){
                    if(currentFieldName.toLowerCase().trim() == $(this).find('.label-tagit-display').text().toLowerCase().trim()){
                        $('#id_grn_tag').val('');
                        $(this).addClass('flash');
                        return;
                    }
                });
                if($("#id_grn_tag").val().trim() !="") {
                    var row = "<li class='li-tagit-display'>" +
                                "<label class='label-tagit-display'>"+$("#id_grn_tag").val()+"</label>"+
                                "<div class='hidden-div-tagit-id' hidden='hidden'>0</div>"+
                                "<a class='delete_tag'></a>"  + "</li>";
                    //$('#grn_tags_table').append(row);
                    $(row).insertBefore('#grn_tags_table .text_for_tag');
                }
            }
        }
        else {
            $(".li-tagit-display").removeClass('flash');
            var currentFieldName = $("#id_grn_tag").val();
            $("#grn_tags_table").find('.li-tagit-display:visible').each(function(){
                if(currentFieldName.toLowerCase().trim() == $(this).find('.label-tagit-display').text().toLowerCase().trim()){
                    $('#grn_tag_value').val('');
                    $('#id_grn_tag').val('');
                    $(this).addClass('flash');
                    return;
                }
            });
            if($("#id_grn_tag").val().trim() !="") {
                var row = "<li class='li-tagit-display'>" +
                            "<label class='label-tagit-display'>"+$("#id_grn_tag").val()+"</label>"+
                            "<div class='hidden-div-tagit-id' hidden='hidden'>"+ $("#grn_tag_value").val() +"</div>"+
                            "<a class='delete_tag' ></a>"  + "</li>";
                //$('#grn_tags_table').append(row);
                $(row).insertBefore('#grn_tags_table .text_for_tag');
            }
        }
        $('#grn_tag_value').val('');
        $('#id_grn_tag').val('');
        setTimeout(function(){
            $('#id_grn_tag').focus();
        },500);
        create_delete_tag_button();
    });
}

function checkGoodsAlreadyReceived(receivedAgainst) {
    if(["Purchase Order", "Job Work", "Sales Return"].indexOf(receivedAgainst) != -1) {
        $("#div_goods_already_received").removeClass("hide");
        $("#party_div").find("input[type='checkbox']").prop("checked", false);
        if($("#goods_already_received").is(":checked")){
            $("#party_div").removeClass("hide");
        }else{
            $("#party_div").addClass("hide");
        }
    }
    else {
        $("#goods_already_received").prop("checked", false);
        $("#div_goods_already_received").addClass("hide");
        $("#party_div").addClass("hide");
        $("#po_div").find("input[type='checkbox']").prop("checked", false);
        $("#party_DcNo").find("option").remove();
        $('#party_DcNo').multiselect('destroy').multiselect();
        $("#po_details").val("");
        $("#party_details").val("");
        $(".pending-class").removeClass("hide");
        $(".td-material").removeClass("hide");
    }
}

function toggleConversionRate(){
    if($("#id_currency option:selected").text() != $("#id_home_currency").val()) {
        $("#div_con_rate").removeClass("hide");
    } else {
        $("#div_con_rate").addClass("hide");
    }
}

function loadGstTaxes() {
    $.ajax({
        url: "/erp/purchase/json/po/load_gst_tax/",
        type: "post",
        datatype:"json",
        data: {gst_type: "CGST"},
        success: function(response){
            CGST_rates += "<option value=0></option>";
            for (i = 0; i < response.length ; i++) {
                CGST_rates += "<option value='" + response[i][0] + "'> " + response[i][2].toFixed(2) + " </option>";
            }
        }
    });
    $.ajax({
        url: "/erp/purchase/json/po/load_gst_tax/",
        type: "post",
        datatype:"json",
        data: {gst_type: "SGST"},
        success: function(response){
            SGST_rates += "<option value=0></option>";
            for (i = 0; i < response.length ; i++) {
                SGST_rates += "<option value='" + response[i][0] + "'> " + response[i][2].toFixed(2) + " </option>";
            }
        }
    });

    $.ajax({
        url: "/erp/purchase/json/po/load_gst_tax/",
        type: "post",
        datatype:"json",
        data: {gst_type: "IGST"},
        success: function(response){
            IGST_rates += "<option value=0></option>";
            for (i = 0; i < response.length ; i++) {
                IGST_rates += "<option value='" + response[i][0] + "'> " + response[i][2].toFixed(2) + " </option>";
            }
        }
    });
}

$(document).ready(function() {
    action_count = 0;
    customJsValidation();
    validateTextInputs();
    materialListBlurEvent('materialrequired');
    loadAllUnits("#id_material-all_units");
    $('#loadingmessage2_ie').hide();
    setTimeout(function(){
        $(".chosen-select").chosen();
        if(document.location.search.length) {
            var vars = [], hash;
            var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
            for(var i = 0; i < hashes.length; i++)
            {
                hash = hashes[i].split('=');
                vars.push(hash[0]);
                vars[hash[0]] = hash[1];
            }
            if(hash[1] != "pending") {
                editReceipt(hash[1]);
            }
        }
        if($("#recagainst").val() == "Issues") {
            $("#issueyear, #issueno").multiselect();
            $(".for-issue_remove, .td_unit_text").addClass('hide');
        }
        $("#id_gstin").change(function(){
            $("#cmdUpdateGRN").removeClass("hide");
        });
    }, 500);

    $("#pos").multiselect();
    $("#party_DcNo").multiselect();
    $('#mat_returned').hide();
    $('#mat_received').show();
    $('#id_currency').prop('disabled',true);
    ChangePartyCurrency(getSelectedSupplierId(), "id_currency","");

    $('.modal').on('hidden.bs.modal', function(e) {
        $(this).find('.error-border').removeClass("error-border");
        $(".custom-error-message").remove();
        if ($("#supplier").val() === "add_new") {
            $("#supplier").val(0).trigger("chosen:updated");
        }
        if ($("#inspector").val() === "add_new_inspector") {
            $("#inspector").val(0).trigger("chosen:updated");
        }
    });

    $('#edit_catalogue_form').submit(function() {
        $.ajax({
            data: $(this).serialize(),
            type: $(this).attr('method'),
            url: $(this).attr('action'),
            success: function(response) {
                $('#loading').hide();
                $("#material_id_hidden").val(response['item_id']);
                $("#material_id").val(response['item_id']);
                $("#materialrequired").val(response['name']).attr("readonly","readonly");
                $("#materialrequired").closest("div").find(".material-removal-icon").removeClass("hide");
                $("#id-makeId").val(response['make_id']);
                $("#material_is_service").val(response['is_service']);
                $("#unit_display").text(response['unit_name']);
                if(!$('#add_material_modal').hasClass('in')) {
                    $("#cmdadd").trigger("click");
                }
                $('#add_material_modal').modal('hide')
            }
        });
        return false;
    });

    $("#id_wo").change(function() {
	    var wo_id = $("#id_wo").val();
	    if (wo_id != "" && wo_id != 0){
            $.ajax({
                url: "/erp/stores/json/grn/get_wo_materials/",
                type: "post",
                datatype: "json",
                data: { wo_id: wo_id },
                success: function (response) {
                    resetMaterialTables();
                    $(".add_item_details").addClass('hide');
                    $("#div_issueno").addClass("hide");
                    $("#id_invoice-issued_to").val(response["issue_to"]).trigger("chosen:updated");
                    $("#id_invoice_issued_to_chosen").addClass('div-disabled');

                    constructOtherReceiptMaterialTHeader();
                    changeGrnTableHeader();
                    var materialTable = new MaterialTable(receivedAgainst=$("#recagainst").val(), tableId="materialtable")
                    $.each(response["po_materials"], function(i, item) {
                        item.item_name = item.name;
                        item.hsn_code = item.hsn_code;
                        item.acc_qty = item.qty;
                        item.qty = item.is_faulty;
                        item.po_id = wo_id;
                        item.dc_id = "";
                        materialTable.createRow(item=item);
                    });
                    materialReturnSerialNo();
                    var dc_materialTable = new MaterialTable(receivedAgainst=$("#recagainst").val(), tableId="dc_materialtable")
                    $.each(response["invoice_materials"], function(i, item) {
                        item.item_name = item.name;
                        item.hsn_code = item.hsn_code;
                        item.dc_qty = 0;
                        item.dc_id = item.invoice_id;
                        item.po_id = ""
                        dc_materialTable.createRow(item=item);
                    });
                    materialReturnSerialNo();
                    $("#loading").hide();
                }
            });
         }
         else if(wo_id == 0){
            $("#id_invoice_issued_to_chosen").removeClass('div-disabled');
            $("#id_invoice-issued_to").val(0).trigger("chosen:updated");
            $("#div_issueno").removeClass('hide');
            resetMaterialTables();
            $(".add_item_details").removeClass('hide');

         }
    });

    var select = document.getElementById("id_currency");
    for (var i = 0; i < select.options.length; i++) {
        if (select.options[i].text == $("#id_home_currency").val()) {
            select.options[i].selected = true;
        }
    }
    $( "#supplier .other_frequently_used_supplier").hide();
    $( "#supplier .frequently_used_jobwork_supplier").hide();
    $( "#supplier .frequently_used_supplier").show();
    $( "#supplier .frequently_used_dc_supplier").hide();
    $( "#supplier .frequently_used_salesreturn_supplier").hide();

    $( "#supplier .other_frequently_used_supplier").attr('label', '');
    $( "#supplier .frequently_used_jobwork_supplier").attr('label', '');
    $( "#supplier .frequently_used_supplier").attr('label', '');
    $( "#supplier .frequently_used_salesreturn_supplier").attr('label', '');
    $("#inspector").val($('#inspector optgroup:eq(0) option:eq(0)').val()).trigger("chosen:updated")

    var arr = [];
    $('#inspector optgroup[label="Frequently used"] option').each(function () {
        arr.push($(this).text().toLowerCase())
    });
    $('#inspector optgroup[label="All"] option').each(function () {
        arr.push($(this).text().toLowerCase())
    });
    $("#id_inspector").keyup( function() {
        var Frequently_used = $("#inspector").val().toLowerCase();
        if(arr.indexOf($('#id_inspector').val().toLowerCase()) > -1) {
            $(".duplicate_inspector").show();
        }
        else{
            $(".duplicate_inspector").hide();
        }
    });
    $("#add_inspector_button").click(function() {
        var Frequently_used = $("#inspector").val().toLowerCase();
        if($('#id_inspector').val()!="") {
            if(arr.indexOf($('#id_inspector').val().toLowerCase()) > -1) {
                $(".duplicate_inspector").show();
            }
            else{
                $(".duplicate_inspector").hide();
                $("#inspector").append(new Option($('#id_inspector').val(), $('#id_inspector').val().toLowerCase()));
                $("#inspector").val($('#id_inspector').val().toLowerCase()).trigger("chosen:updated");
                $('#add_inspector').modal("hide");
                $('#loading').hide();
            }
        }
    });
    if(($("#id_receipt_no").val() == "" && $("#is_gate_inward_no_automated").val() == "True") || ($("#id_receipt_no").val() != "" && $("#is_gate_inward_no_automated").val() == "True" && $("#is_gate_inward_no_editable").val() == "False")) {
        $("#inwardno").prop('readonly', true);
    }else{
        $("#inwardno").prop('readonly', false);
    }
});


function changeGrnTableHeader(){
//TODO revisit this method
    if(["Purchase Order", "Job Work", "Job In", "Delivery Challan", "Sales Return"].indexOf($("#recagainst").val()) != -1){
        $('.invoicerow').show();
        $('#taxrow').show();
        $('#inv_det').show();
		$(".addDc_Invoice").show();
		$("#div_currency_details").removeClass("hide");
        var tags = document.getElementsByName('price_col');
        for (var i = 0; i < tags.length; i++) {
            tags[i].removeAttribute("style", "display");
        }
        var tags = document.getElementsByName('tot_price_col');
        for (var i = 0; i < tags.length; i++) {
            tags[i].removeAttribute("style", "display");
        }
    }
    else {
        $('.invoicerow').hide();
        $('#taxrow').hide();
        $('#inv_det').hide();
        $(".addDc_Invoice").hide();
        $("#div_currency_details").addClass("hide");
        var tags = document.getElementsByName('price_col');
        for (var i = 0; i < tags.length; i++) {
            tags[i].style.display = 'none';
        }
        var tags = document.getElementsByName('tot_price_col');
        for (var i = 0; i < tags.length; i++) {
            tags[i].style.display = 'none';
        }
    }
}

/**
 * Loads purchase order numbers for the params
 */
function loadSelectedPoNumbers(receipt_no, isGoodsAlreadyReceived) {
    $.ajax({
        url: "/erp/stores/json/grn/loadpolist/",
        type: "post",
        datatype:"json",
        data: {grn_id: receipt_no, goods_already_received: isGoodsAlreadyReceived},
        success: function(response){
            var selectedPoIds = []
            for (j = 0; j < response.length; j++) {
                $("#pos").multiselect('select', response[j][0], true);
                selectedPoIds.push(response[j][0]);
            }
            $("#po_details").val(selectedPoIds.join(","));
            posSelectChange();
            if (isGoodsAlreadyReceived){
                new MaterialManager().loadGoodsReceivedDcNumbers();
            } else {
                loadGrnMaterials(receipt_no, $("#recagainst").val());
            }
        }
    });
}

/**
 * Loads purchase order numbers for the params
 * params is a type of JSON object i.e. a dictionary
 */
function loadPoNumbers(params, receipt_no, isGoodsAlreadyReceived) {
    $("#pos").next(".btn-group").find(".multiselect").addClass("disabled").attr("title", "Loading... Please wait");
    $.ajax({
        url: "/erp/stores/json/grn/po_numbers/",
        type: "post",
        datatype:"json",
        data: params,
        async: false,
        success: function(response) {
            if (response.response_message == "Success") {
                $("#pos").multiselect('destroy');
                if(!isGoodsReceivedCheckChanged){
                    $("#party_DcNo").html('').multiselect("rebuild");
                }
                var items =  "";
                $.each(response.po_numbers, function(i, item) {
                    items += "<option value="+item[0]+"> "+item[1]+" "+item[2]+"</option>";
                });
                $("#pos").html(items);
                $("#pos").multiselect();
                setTimeout(function() {
                    if (!isGoodsReceivedCheckChanged && receipt_no != "") {
                        resetMaterialTables();
                        loadSelectedPoNumbers(receipt_no, isGoodsAlreadyReceived);
                    } else {
                        posSelectChange();
                    }
                }, 1000);
            } else {
                swal({text:"Failed loading PO numbers", type:"warning"});
                $("#pos").next(".btn-group").find(".multiselect").removeClass("disabled").attr("title", "");
            }
        }
    });
}

function customDateChanged(ev) {
    inv_date = $("#invdate").val();
    UdateCustomDatePicker($(this).attr('id'));
    changed_inv_date = $("#invdate").val();
    if (ev.target.id == "invdateNew"){
        if($("#recagainst").val() !='Issues'){
            var supplierId = getSelectedSupplierId();
            if (supplierId == "") {
                return
            }
        }
        var receivedAgainst = $("#recagainst").val();
        var isGoodsAlreadyReceived = false;
        if(["Purchase Order", "Job Work", "Sales Return"].indexOf(receivedAgainst) != -1) {
            isGoodsAlreadyReceived = $("#goods_already_received").is(":checked");
        }
        if (isGoodsAlreadyReceived == false){
            params = {party_id: supplierId, received_against: receivedAgainst, goods_already_received: isGoodsAlreadyReceived, inv_date: inv_date, changed_inv_date: changed_inv_date};
            $.ajax({
                url: "/erp/stores/json/grn/open_po_numbers/",
                type: "post",
                datatype:"json",
                data: params,
                async: false,
                success: function(response) {
                    var isItemSelected = 0;
                    var invalidPoList = [];
                    $.each(response.removed_pos, function(index, item) {
                        if($("#pos").next(".btn-group").find(".multiselect-container").find(`input[value='${item[0]}']`).is(":checked")) {
                            isItemSelected++;
                            invalidPoList.push(" "+item[1]);
                        }
                    });
                    if(isItemSelected > 0) {
                        swal({
                            title: "Are you sure?",
                            text: `Invoice Date falls out of the validity period of the POs -<br /> <b>[${invalidPoList} ]</b>, hence related items will be removed from the Item Particulars.
                                    <br /><br />Do you still want to proceed?<span class="hide" id='grn_old_date'>${inv_date}</span>`,
                            type: "warning",
                            showCancelButton: true,
                            confirmButtonColor: "#209be1",
                            confirmButtonText: "Yes, do it!",
                            closeOnConfirm: true
                        },
                        function(isConfirm){
                            if(isConfirm){
                                updateBlanketPosInList(response);
                            }
                            else {
                                $("#invdateNew").datepicker("setDate", new Date($("#grn_old_date").text()));
                            }
                        });
                    }
                    else {
                        updateBlanketPosInList(response);
                    }
                }
            });
        }
    }
}

function updateBlanketPosInList(response) {
    $.each(response.added_pos, function(index, item) {
        if($(`#pos option[value='${item[0]}']`).length == 0) {
            var option = `<option value='${item[0]}'>${item[1]} (Blanket PO)</option>`;
            $("#pos").append(option);
        }
    });

    $.each(response.removed_pos, function(index, item) {
        $("#pos").next(".btn-group").find(".multiselect-container").find(`input[value='${item[0]}']`).prop("checked", false).trigger("change");
        $("#pos").find(`option[value='${item[0]}']`).remove();
    });
    $("#pos").multiselect("rebuild");
    posSelectChange();
}

var loadOaNumbers = function (partyId="", receiptId="", receivedAgainst="Job In") {
    $("#id-oaYears #pos").next(".btn-group").find(".multiselect").addClass("disabled").attr("title", "Loading... Please wait");
    $.ajax({
        url: "/erp/stores/json/pending_job_oa_numbers/",
        type: "post",
        dataType: "json",
        data: {
            party_id: partyId,
            financial_years: "",
            receipt_no: receiptId,
            receivedAgainst: receivedAgainst
        },
        success: function(response) {
            if (response.response_message == "Success") {
                $("#pos").multiselect('destroy');
                var items =  "";
                $.each(response.oa_numbers, function(i, oa) {
                    items = `${items}<option value="${oa.id}">${oa.code}</option>`;
                });
                $("#pos").html(items);

                $(".OANumbers").removeClass("hide");
                $(".OANumbers").html(`<label class="label_job_oa_no" for="id-OANumbers">Job OA No</label>
                    <select class="form-control" id="id-OANumbers">
                        <option value="">Select OA</option>
                        ${items}
                    </select`);
                $("#id-OANumbers").chosen();

                $("#pos").multiselect({
                    onDropdownHide: function(event){
                        new MaterialManager().onChangePoSelection();
                    }
                });
                $.each(response.oa_numbers, function(i, oa) {
                    if(oa.selected == true){
                        $("#pos").multiselect('select', oa.id , true);
                    }
                });
                setTimeout(function() {
                    if (receiptId != "") {
                        loadGrnMaterials(receiptId, receivedAgainst);
                    }
                }, 1000);
                new MaterialManager(); // this is to initialize the po selection do not remove
            } else {
                swal({title:"", text:"Failed loading OA numbers", type:"warning"});
                $("#pos").next(".btn-group").find(".multiselect").removeClass("disabled").attr("title", "");
            }
        }
    });
}

/**
 * Load Delivery Challan Numbers for the given params
 * params is a JSON object
 */
function loadDcNumbers(params, receipt_no) {
    $.ajax({
        url: "/erp/stores/json/grn/loaddcnumber/",
        type: "post",
        datatype:"json",
        data: params,
        success: function(response){
            $("#pos").multiselect('destroy');
            $("#party_DcNo").html('').multiselect("rebuild");
            var items =  "";
            for (i = 0; i < response.length; i++) {
               items += "<option value="+response[i][0]+"> "+ response[i][3]+" </option>";
            }
            $("#pos").html(items);
            $("#pos").multiselect();
            setTimeout(function(){
                if(!isGoodsReceivedCheckChanged && receipt_no != "") {
                    resetMaterialTables();
                    $.ajax({
                        url: "/erp/stores/json/grn/load_selected_dc_list/",
                        type: "post",
                        datatype:"json",
                        data: {grn_id: receipt_no, goods_already_received: false},
                        success: function(response){
                            for (j = 0; j < response.length; j++) {
                                $("#pos").multiselect('select', response[j][0] , true);
                            }
                            posSelectChange();
                            var materialMan = new MaterialManager();
                            if (materialMan.selectedPoIds.length > 0) {
                                if ($("#recagainst").val() == "Delivery Challan" || !$("#goods_already_received").is(":checked")) {
                                    loadGrnMaterials($("#id_receipt_no").val(), $("#recagainst").val());
                                } else {
                                    materialMan.onChangePoSelection();
                                }
                            }
                        }
                    });
                } else {
                    posSelectChange();
                }
            }, 1000);
        }
    });
}

/**
 * Loads selected issued to for the receipt is being edited
 */
function loadSelectedIssuedTo(receipt_no) {
    $.ajax({
        url: "/erp/stores/json/grn/loadinvoicegrnfetch/",
        type: "post",
        datatype: "json",
        data: {grn_id: receipt_no},
        success: function (response) {
            if(response.length > 0) {
                $("#id_invoice-issued_to").val(response[0][0]);
                $('#id_invoice-issued_to').trigger("chosen:updated");
                if($("#id_invoice-issued_to").val() != "") {
                    loadPo(receipt_no);
                    $("#div_issueno").find(".custom-error-message").remove();
                    $("#div_issueno").find(".error-border").removeClass("error-border");
                }
            } else {
                loadGrnMaterials(receipt_no, $("#recagainst").val());
            }
        }
    });
}

function loadIssueNumbers(params, receipt_no) {
    $.ajax({
        url: "/erp/stores/json/grn/loadissuenumber/",
        type: "post",
        datatype:"json",
        data: params,
        success: function(response) {
            $('#issueno, #issueyear').multiselect('destroy');
            $('#dc_materialtable tbody tr').remove();
            var items = '';
            for (i = 0; i < response.length ; i++) {
               items += "<option value="+response[i][2].trim()+"> "+response[i][2] +" </option>";
            }
            $("#issueyear").html(items);
            $("#issueyear").multiselect();
            $('#issueno').multiselect();
            loadIssueYearChangeEvent();
            if(!isGoodsReceivedCheckChanged && receipt_no != "") {
                resetMaterialTables();
                $.ajax({
                    url: "/erp/stores/json/grn/loadinvoicegrnfetch/",
                    type: "post",
                    datatype: "json",
                    async: false,
                    data: {grn_id: receipt_no},
                    success: function (response) {
                        for (i = 0; i < response.length ; i++) {
                            if($("#id_wo").val() == "" || $("#id_wo").val() == 0) {
                                  $("#issueyear").multiselect('select', response[i][1], true);
                                  $("#issueno_details").val(response[i][2]+","+$("#issueno_details").val());
                              }
                        }
                        onIssueYearChange();
                        loadPreviouslySelectedIssues();
                        loadGrnMaterials(receipt_no, $("#recagainst").val());
                    }
                });
            } else {
                issueSelectChange();
            }
        }
    });
}

/**
 * Loads PO/JO materials for the selected numbers
 */
function loadPoMaterials(receivedAgainst="Purchase Order", receiptId, supplierId, poIds) {
    $.ajax({
        url: "/erp/purchase/json/po/loadpomaterial/",
        type: "post",
        datatype:"json",
        data: {receipt_no: receiptId, supplier_id:supplierId, po_ids: poIds},
        success: function(response){
            constructPoReceiptMaterialTHeader(receivedAgainst);
            var all_goods_received = 0;
            var materialTable = new MaterialTable(receivedAgainst=receivedAgainst);
            $.each(response[0], function(i, item) {
                if($("#id-select_po_tag").val() == "true"){
                    $('#project').val(item.project_code);
                    $('#project').trigger("chosen:updated");
                    updateProjectChosen(item.project_code, "project");
                }
                if (item.purchase_account_id > 0){
                    $('#purchase_account').val(item.purchase_account_id);
                    $('#purchase_account').trigger("chosen:updated");
                }
                var itemCount = $("#materialtable tbody").find("tr").length;
                if ((item.is_blanket_po == 0 && (item.pur_qty - item.rec_qty) > 0) || item.is_blanket_po == 1 ) {
                    item_details = item.name
                    var pending_qty = 0;
                    if (item.is_blanket_po == 0){
                        pending_qty = item.pur_qty - item.rec_qty;
                    }
                    else{
                        pending_qty = item.pur_qty;
                    }
                    if (item.drawing_no != "" && item.drawing_no !=null && item.drawing_no !="undefined") {
                        item_details = item_details + " - " + item.drawing_no;
                    }
                    if(item.make_name != null && item.make_name != "" && item.make_name != "[]") {
                       item_details = item_details + " <i>[" + constructDifferentMakeName(item.make_name) + "]</i>";
                    }
                    if (item.po_type != 0){ po_type = "/JO/"} else{ po_type="/PO/" }
                    material = {
                        mat_type: item.stock_type,
                        drawing_no: item.drawing_no,
                        item_id: item.item_id,
                        item_name: item_details,
                        make_id: item.make_id,
                        is_faulty: false,
                        is_service: item.is_service,
                        alternate_unit_id:item.alternate_unit_id,
                        scale_factor:item.scale_factor,
                        po_code: item.financial_year + po_type + item.orderno + item.sub_number,
                        hsn_code: item.hsn_code,
                        pending_qty: pending_qty,
                        rec_qty: 0, dc_qty: 0, acc_qty: 0,
                        short_qty: 0, rej_qty: 0,
                        unit_name: item.unit, unit_id: item.unit_id,
                        price: 0, discount: 0,
                        po_id: item.id, dc_id: "", oa_id: "", rec_grn_id: "",
                        is_blanket_po: item.is_blanket_po,
                    };
                    if(item.CGST == '0' && item.SGST == '0' && item.IGST == '0') {
                        $.each(item.smart_taxes ? item.smart_taxes.split(';'):[], function(i, tax){
                            var tax = JSON.parse(tax);
                            if (tax.type == "cgst") {
                                material.cgst_code = tax.code;
                            }
                            if (tax.type == "sgst") {
                                material.sgst_code = tax.code;
                            }
                            if (tax.type == "igst") {
                                material.igst_code = tax.code;
                            }
                        });
                    } else {
                        material.cgst_code = item.CGST;
                        material.sgst_code = item.SGST;
                        material.igst_code = item.IGST;
                    }
                    materialTable.createRow(item=material);
                } else {
                    all_goods_received= all_goods_received + 1;
                }
                if(all_goods_received == response[0].length) {
                    swal({
                        title: "",
                        text:"All Goods for the chosen POs has been received, but at least some of the received Goods are yet to be Invoiced. If you are attempting to Invoice them, kindly check the <b>Goods Already Received</b> check-box and then select appropriate PO Nos.",
                        type: "warning"
                    });
                }
            }); // css for update the table row color and border line
            changeGrnTableHeader();
            var materialTable = new MaterialTable(receivedAgainst=receivedAgainst, tableId="dc_materialtable");
            $.each(response[1], function(index, item) {
                [item.drawing_no, item.item_name] = formatMaterialDescription(item);

                item.pending_qty = item.dc_mat_pending_qty == undefined? Number(item.qty): Number(item.dc_mat_pending_qty);
                if(item.pending_qty > 0){
                    materialTable.createRow(item=item);
                }
            });
            materialReturnSerialNo();
            $("#id-select_po_tag").val(true)
        }
    });
}

/**
 * Loads DC materials for the selected receipt number (po numbers)
 */
function loadDcMaterials(selectedDcIds, receivedAgainst, supplierId, receiptId) {
    $.ajax({
        url: "/erp/stores/json/grn/load_dc_materials/",
        type: "post",
        datatype:"json",
        data: {receipt_no:receiptId, dc_ids: selectedDcIds, received_against: receivedAgainst, supplier_id: supplierId},
        success: function(response) {
            constructPoReceiptMaterialTHeader(receivedAgainst);
            changeGrnTableHeader();
            var all_goods_received = 0;
            var tableId = receivedAgainst == "Delivery Challan" ? "dc_materialtable": "materialtable";
            var materialTable = new MaterialTable(receivedAgainst=receivedAgainst, tableId=tableId);
            var countOfAddedItems = 0;
            $.each(response[0], function(index, item) {
                if(item.dc_mat_pending_qty > 0) {
                    countOfAddedItems ++;
                    [item.drawing_no, item.item_name] = formatMaterialDescription(item);
                    item.item_name = item.dc_item_display_name;
                    item.pending_qty = item.dc_mat_pending_qty == undefined? 0: Number(item.dc_mat_pending_qty);
                    item.price = item.rate;
                    materialTable.createRow(item=item);
                }
            });
            if (countOfAddedItems == 0) {
                swal("", "All Goods for the chosen invoices has been received, but at least some of the received Goods are yet to be Invoiced. If you are attempting to Invoice them, kindly check the Goods Already Received check-box and then select appropriate Invoice Nos.", "warning");
            } else {
                materialReturnSerialNo();
            }
        }
    });
}

/**
 * loads Issue materials for the selected issued to
 */
function loadIssueMaterials(selectedIssueIds) {
    $("#loading").show();
    $.ajax({
        url: "/erp/stores/json/grn/loadissuematerial/",
        type: "post",
        dataType:"json",
        data: {issue_ids: selectedIssueIds},
        success: function(response) {
            constructOtherReceiptMaterialTHeader();
            changeGrnTableHeader();
            var materialTable = new MaterialTable(receivedAgainst=$("#recagainst").val(), tableId="dc_materialtable")
            $.each(response[0], function(i, item) {
                if (item.dc_mat_pending_qty > 0) {
                    [item.drawing_no, item.item_name] = formatMaterialDescription(item);
                    item.item_name = item.dc_item_display_name;
                    item.pending_qty = item.dc_mat_pending_qty == undefined ? 0 : item.dc_mat_pending_qty;
                    item.shall_construct_item_name = true
                    materialTable.createRow(item=item);
                }
            });
            materialReturnSerialNo();
            $("#loading").hide();
        }
    });
}

var loadOaMaterials = function (receivedAgainst="Job In", supplierId="", selectedOaIdsAsString="", receiptId="") {
    $("#loading").show();
    try {
        $.ajax({
            url: "/erp/sales/json/oa_materials/",
            type: "post",
            dataType:"json",
            data: {oa_ids: selectedOaIdsAsString, oa_against_type: 'GRN',receipt_id: receiptId},
            success: function(response) {
                $("#loading").hide();
                var materialSelector = new MaterialSelector();
                materialSelector.insertMaterialSelectorPopup(title="OA Materials");
                materialSelector.showOaMaterials(response, addOaMaterialsToTable);
            }
        });
    } catch(e) {
        $("#loading").hide();
    }
}

var addOaMaterialsToTable = function (oaMaterials) {
    if (oaMaterials.length > 0) {
        var materialTable = new MaterialTable(receivedAgainst=$("#recagainst").val(), tableId="materialtable");
        $.each(oaMaterials, function(i, item) {
            item.shall_construct_item_name = true
            materialTable.createRow(item=item);
        });
    } else {
        swal("", "Please select materials with quantity to proceed.", "warning");
    }
}

function getSelectedSupplierId() {
    if ($("#recagainst").val() != "Issues" && $("#supplier option:selected").val() !=='add_new') {
        var selectedParty = $("#supplier option:selected").val();
        if (selectedParty != undefined) {
            return $("#supplier option:selected").val().split("[::]")[0];
        }
    }
    return 0;
}

/**
 * This method should be invoked on ready event
 */
function loadPo(receipt_no) {
    if($("#recagainst").val() !='Issues'){
        var supplierId = getSelectedSupplierId();
        if (supplierId == "") {
            return
        }
    }
    //var receipt_no = $("#id_receipt_no").val().trim();
    var receivedAgainst = $("#recagainst").val();
    var isGoodsAlreadyReceived = false;
    if(["Purchase Order", "Job Work", "Sales Return"].indexOf(receivedAgainst) != -1) {
        isGoodsAlreadyReceived = $("#goods_already_received").is(":checked");
    }
    if (receivedAgainst == "Others") {
        loadGrnMaterials(receipt_no, receivedAgainst);
    } else if ("Job In" == receivedAgainst) {
        loadOaNumbers(partyId=supplierId, receiptId=receipt_no, receivedAgainst=receivedAgainst);
    } else if (["Purchase Order", "Job Work"].indexOf(receivedAgainst) != -1) {
        loadPoNumbers({
            party_id: supplierId, grn_no: receipt_no,
            received_against: receivedAgainst,
            goods_already_received: isGoodsAlreadyReceived
        }, receipt_no, isGoodsAlreadyReceived);
    } else if(receivedAgainst == "Issues" && $("#id_invoice-issued_to option:selected").val() != "") {
        params = {supplier_id: supplierId, project: $("#project").val(), receipt_no: receipt_no,
                received_against: receivedAgainst, isGoodsAlreadyReceived: isGoodsAlreadyReceived,
                issued_to: trim($("#id_invoice-issued_to").val()), groupbyyear: 'groupbyyear'}
        loadIssueNumbers(params, receipt_no);
    } else {
//    DC or Sales Return
        loadDcNumbers({
            party_id:supplierId, grn_no: receipt_no,
            received_against:receivedAgainst,
            goods_already_received: isGoodsAlreadyReceived
        }, receipt_no);
    }
}

function loadPreviouslySelectedDC() {
    var prevItem = $("#party_details").val();
    if($("#party_details").val() != "") {
        prevItemList = prevItem.split(",");
        for (i = 0; i < prevItemList.length; i++) {
            $("#party_DcNo").multiselect('select', prevItemList[i], true);
        }
    }
    var selected_dc_nos="";
    var multi_select_dc =  $("select#party_DcNo").next('.btn-group').find('ul').find('li input:checked');
    multi_select_dc.each(function () {
        selected_dc_nos += $(this).val() + ",";
    });
    document.getElementById("party_details").value = selected_dc_nos;
}

//TODO refactor below method
function loadIssue() {
    if($("#recagainst").val() == "Issues" && $("#id_invoice-issued_to").val() != ""){
        var seltext= "None" + ","+$("#project").val() + "," + $("#id_receipt_no").val() + "," + $("#recagainst").val() + "," + $("#goods_already_received").is(":checked");
        seltext = seltext + ',' +trim($("#id_invoice-issued_to").val()) + ',groupbyyear';
        $.ajax({
            url: "/erp/stores/json/grn/loadissuenumber/",
            type: "post",
            datatype:"json",
            data: seltext,
            success: function(response){
                $('#issueno').multiselect('destroy');
                var items =  "";
                for (i = 0; i < response.length ; i++) {
                   items += "<option value="+response[i][0]+"> "+response[i][2] + '/I/' + response[i][3]+" </option>";
                }
                $("#issueno").html(items);
                $('#issueno').multiselect();
                issueSelectChange();
            }
        });
    }
}

function loadIssueNo(financial_yr) {
    if($("#recagainst").val() == "Issues" && $("#id_invoice-issued_to").val() != ""){
        params = {project: $("#project").val(), receipt_no: $("#id_receipt_no").val(),
                received_against: $("#recagainst").val(), isGoodsAlreadyReceived: $("#goods_already_received").is(":checked"),
                issued_to: trim($("#id_invoice-issued_to").val()), groupbyyear: financial_yr}
        $.ajax({
            url: "/erp/stores/json/grn/loadissuenumber/",
            type: "post",
            datatype:"json",
            data: params,
            success: function(response){
                $('#issueno').multiselect('destroy');
                var items =  $("#issueno").html();
                for (i = 0; i < response.length ; i++) {
                    var issueno = response[i][3];
                    while(issueno.length < 6){
                        issueno = '0' + issueno;
                    }
                   items += "<option value="+response[i][0]+"> "+response[i][2] + '/I' + issueno+" </option>";
                }
                $("#issueno").html(items);
                $('#issueno').multiselect();
                issueSelectChange();
                loadPreviouslySelectedIssues();
            }
        });
    }
}

function loadPreviouslySelectedIssues() {
    var prevItem = $("#issueno_details").val();
    if($("#issueno_details").val() != "") {
        prevItemList = prevItem.split(",");
        for (i = 0; i < prevItemList.length; i++) {
            $("#issueno").multiselect('select', prevItemList[i], true);
        }
    }
}

function lastUsedSupplierDetails() {
    $.ajax({
        url: "/erp/stores/json/last_used_supplier_detail/",
        type: "post",
        datatype:"json",
        data: {
            party_id: getSelectedSupplierId()},
        success: function(response){
            if (response !=""){
                $("#recthrough").val(response['trans_mode']);
                $("#currency_conversion_rate").val(response['conversion_rate']);
            }
        }
    });
}

function customCheckbox(checkboxName){
    var checkBox = $('input[name="'+ checkboxName +'"]');
    $(checkBox).each(function(){
        $(this).wrap( "<span class='custom-checkbox'></span>" );
        if($(this).is(':checked')){
            $(this).parent().addClass("selected");
        }
    });
    $(checkBox).click(function(){
	    $(this).parent().toggleClass("selected");
    });
}

function extractMaterialJson(element) {
    material = {rec_grn_id: "", taxes: []};
    $(element).find(`input`).each(function() {
        if ($(this).attr("disabled") != "disabled") {
            material[`${$(this).attr("name")}`] = $(this).val();
        }
    });
    oaId = $(element).find(`select[name="select_oa_id"]`);
    if (oaId.length > 0) {
        material.oa_id = oaId.val();
    }
    $.each(["cgst", "sgst", "igst"], function(i, taxType) {
        tax = $(element).find(`select[name="${taxType}"]`);
        if (tax.val() != null && tax.val() != 0) {
            material.taxes.push(tax.val());
        }
    });
    return material;
}

function constructMaterialDetails(receivedAgainst) {
    var grn_materials = [];
    var taxMissing = false;
    $("#materialtable tbody.item-for-goods tr, #materialtable tbody.item-for-service tr").each(function () {
        var row = this;
        if (receivedAgainst == "Others") {
            grn_materials.push({
                item_id: $(this).find(`input[name="item_id"]`).val(),
                drawing_no: $(this).find(`input[name="drawing_no"]`).val(),
                make_id: $(this).find(`input[name="make_id"]`).val(),
                hsn_code: $(this).find(`input[name="hsn_code"]`).val(),
                is_faulty: $(this).find(`input[name="is_faulty"]`).val(),
                received_qty: $(this).find(`input[name="acc_qty"]`).val(),
                quantity: $(this).find(`input[name="acc_qty"]`).val(),
                accepted_qty: $(this).find(`input[name="acc_qty"]`).val(),
                rej_qty: 0, price: 0,
                alternate_unit_id: $(this).find(`input[name="alternate_unit_id"]`).val(),
                scale_factor: $(this).find(`input[name="scale_factor"]`).val(),
                inspectiorecagainstn_log: $(this).find(`input[name="inspection_log"]`).val()
            });
        } else {
            if (receivedAgainst == "Issues") {
                var material = extractMaterialJson(this);
                if (material.acc_qty > 0) {
                    material.received_qty = material.acc_qty;
                    material.quantity = material.acc_qty;
                    material.accepted_qty = material.acc_qty;
                    material.rej_qty = 0;
                    grn_materials.push(material);
                }
            } else {
                if($(this).find(`input[name="dc_qty"]`).val() > 0 && !($(this).hasClass('consolidated_row'))) {
                    var material = extractMaterialJson(this);
                    material.received_qty = material.rec_qty;
                    material.quantity = material.dc_qty;
                    material.accepted_qty = material.acc_qty;
                    material.hsn_code = $(this).find(`input[name="hsn_code"]`).val();
                    grn_materials.push(material);
                    if (!taxMissing && ["Purchase Order", "Job Work", "Sales Return"].indexOf(receivedAgainst) != -1) {
                        taxMissing = material.taxes.length == 0;
                    }
                }
     	    } // Non Issues block
     	} // Non others block
    });

    // preparing material json from dc material table
    $("#dc_materialtable tbody").find("tr").each(function () {
        if($(this).find(`input[name="dc_qty"]`).val() > 0 && !$(this).hasClass('consolidated_row')) {
            material = extractMaterialJson(this)
            material.received_qty = material.rec_qty;
            material.quantity = material.dc_qty;
            material.accepted_qty = material.acc_qty;
            material.price = 0
            material.hsn_code = $(this).find(`input[name="hsn_code"]`).val();
            grn_materials.push(material);
        }
    });
    return [grn_materials, taxMissing];
}

function findMaterialsWithLowStock(availableStockList, receivedAgainst) {
    var [inwardDate, warningMaterials, softWarningMaterials] = ["", [], []];
    var receivedAgainst = $("#recagainst").val();
    $(".MSLWarning").remove();
    $.each(availableStockList, function(index, item) {
        inwardDate = item.inward_date;
        var materialIndex = `data-material-id="${item.item_id}:${item.make_id}:${item.is_faulty}"`;
        if(item.dc_id == 0 || item.dc_id == null || receivedAgainst == "Sales Return") {
            var tableId= "materialtable";
        } else {
            var tableId = "dc_materialtable";
        }
        var rowElement = $(`#${tableId} tbody td[${materialIndex}]`);
        if (rowElement.length > 0 && item.is_stocked == 1) {
            var trElement = $(rowElement).closest("tr");
            var stockToBeRemoved = Number($(trElement).find(`input[name="saved_acc_qty"]`).val()) - Number($(trElement).find(`input[name="acc_qty"]`).val());
            var closingQty = item.Closing < item.Opening ? item.Closing : item.Opening;
            var finalStock = closingQty - stockToBeRemoved;
            var message = "";
            if (finalStock < 0) {
                if (finalStock >= item.minimum_stock_level) {
                    message = `Stock (${closingQty} - including this GRN) goes below 0!`;
                    softWarningMaterials.push(`[${trim($(rowElement).text())}]`);
                } else {
                    if (0 <= item.minimum_stock_level) {
                        message = `Stock (${closingQty} - including this GRN) goes below 0!`;
                    } else {
                        message = `Stock (${closingQty} - including this GRN) goes below MSL (${item.minimum_stock_level})!`;
                    }
                    warningMaterials.push(`[${trim($(rowElement).text())}]`);
                }
            } else if (finalStock < item.minimum_stock_level) {
                softWarningMaterials.push(`[${trim($(rowElement).text())}]`);
                message = `Stock (${closingQty} - including this GRN) goes below MSL (${item.minimum_stock_level})!`;
            }
            if (message.trim().length > 0) {
                showErrorTip(
                    message=message, element=$(trElement).find(`input[name="acc_qty"]`), additionalClass="MSLWarning");
            }
        }
    });
    return [inwardDate, warningMaterials, softWarningMaterials];
}

var isAcceptedQuantityChanged = function() {
    var acceptedQtyElement = document.getElementsByName("acc_qty");
    var savedAcceptedQtyElement = document.getElementsByName('saved_acc_qty');
    var changeInAcceptedQuantity = false;
    for(k = 0; k < savedAcceptedQtyElement.length; k++) {
        if(acceptedQtyElement[k].value != savedAcceptedQtyElement[k].value) {
            changeInAcceptedQuantity = true;
            break;
        }
    }
    return changeInAcceptedQuantity;
}

function checkForStockAvailability() {
    var receivedAgainst = $("#recagainst").val();
    if(isAcceptedQuantityChanged() && $("#grn_status").val()  > 0) {
        $.ajax({
            url: "/erp/stores/json/grn/check_stock_availability/",
            method: "POST",
            data: {
                grn_no: $("#id_receipt_no").val(),
                received_against: receivedAgainst,
            },
            success: function(response) {
                var [inwardDate, warningMaterials, softWarningMaterials] = findMaterialsWithLowStock(response, receivedAgainst);
                if (warningMaterials.length > 0) {
                    $.each(softWarningMaterials, function(i, item) {
                        warningMaterials.push(item);
                    });
                    if ($("#is_super_user").val().toLowerCase().trim() == "true") {
                        confirmAction(message=formatMessageForMaterialDelete(warningMaterials.join(","), inwardDate), callback = function(isConfirm) {
                            if (isConfirm) {
                                validateAndSaveReceipt();
                                $("materialtable").find(".error-border").removeClass("error-border");
                            }
                        });
                    } else {
                        swal({title: "", type: "error",
                            text: "Accepted quantity of item(s) "+ warningMaterials.join(",") + " should not exceeds the Minimum Stock Level. Please check the quantity.",
                            showCancelButton: false,
                            confirmButtonColor: "#209be1",
                            confirmButtonText: "Ok",
                            closeOnConfirm: true
                        });
                    }
                } else if (softWarningMaterials.length > 0) {
                    confirmAction(message=formatMessageForMaterialDelete(softWarningMaterials.join(","), inwardDate), callback = function(isConfirm) {
                        if (isConfirm) {
                            validateAndSaveReceipt();
                            $("materialtable").find(".error-border").removeClass("error-border");
                        }
                    });
                } else {
                    validateAndSaveReceipt();
                }
            }, error: function (xhr, errmsg, err) {
                swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
            }
        });
    } else {
        validateAndSaveReceipt();
    }
}

var checkStockWithJobInReturn = function () {
    if(isAcceptedQuantityChanged()) {
        $.ajax({
            url: "/erp/stores/json/grn/goods_returned_status/",
            method: "POST",
            data: {
                receipt_no: $("#id_receipt_no").val()
            },
            success: function(response) {
                if (response.response_message == "Internal server error") {
                    swal("", response.custom_message, "error")
                } else {
                    var isValid = true;
                    var invalidMaterials = [];
                    $.each(response, function(i, item) {
                        var materialIndex = `data-material-id="${item.item_id}:${item.make_id}:${item.is_faulty}"`;
                        var rowElement = $(`#materialtable tbody td[${materialIndex}]`);
                        if (rowElement.length > 0) {
                            var trElement = $(rowElement).closest("tr");
                            var quantity = Number($(trElement).find(`input[name="acc_qty"]`).val());
                            if(quantity < item.quantity) {
                                invalidMaterials.push(`[${trim($(rowElement).text())}]`);
                                isValid = false;
                                showErrorTip(
                                    message=`Accepted quantity ${quantity} can not be less than return quantity ${item.quantity}`, element=$(trElement).find(`input[name="acc_qty"]`));
                            }
                        }
                    });

                    if (isValid) {
                        checkForStockAvailability();
                    } else {
                        message = `Accepted quantity of item(s) ${invalidMaterials.join(",")} can not be less than return quantity.`;
                        if ($("#is_super_user").val().toLowerCase().trim() == "true") {
                            message = `${message}.<BR/> Do you want to continue?`;
                            confirmAction(message=message, callback = function(isConfirm) {
                                if (isConfirm) {
                                    checkForStockAvailability();
                                }
                            });
                        } else {
                            swal({title: "", type: "error",
                                text: message,
                                showCancelButton: false,
                                confirmButtonColor: "#209be1",
                                confirmButtonText: "Ok",
                                closeOnConfirm: true
                            });
                        }
                    }
                }
            }
        });
    } else {
        validateAndSaveReceipt();
    }
}

function validateAndSaveReceipt() {
    if($('#chkVerified').hasClass("selected") || (!$('#chkChecked').hasClass("selected") && $('#chkApproved').hasClass("selected"))){
        $.ajax({
            url: "/erp/stores/json/get_receipt_linked_message/",
            method: "POST",
            data:{
                receipt_no: $("#id_receipt_no").val()
            },
            success: function(response) {
                if (response.response_message =="Success") {
                    if (response.custom_message == "") {
                        validateForOaThenSave();
                    } else {
                        confirmAction(message=response.custom_message, callback=function (isConfirm) {
                            if (isConfirm) {
                                setTimeout(function () {
                                    validateForOaThenSave();
                                }, 500);
                             }
                        });
                    }
                }
                else {
                    swal({title: "", text: response.custom_message, type: "error"});
                }
            }, error: function (xhr, errmsg, err) {
                swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
            }
        });
    } else if($('#chkChecked').hasClass("selected")) {
        setTimeout(function() {
            var warning = `${moduleName} is already processed. Unfortunately system will not handle the perpetuation of these changes to the linked note, hence need to be addressed manually.<br/> Do you want to continue?`;
            confirmAction(message=warning, callback=function (isConfirm){
                if (isConfirm) {
                   setTimeout(function(){
                        validateForOaThenSave();
                   }, 500);
                }
            });
        }, 100);
    } else {
        validateForOaThenSave();
    }
}

function validateForOaThenSave() {
    var receivedAgainst = $("#recagainst").val();
    if(receivedAgainst == "Job In") {
        var isValid = true
        $.each(constructMaterialDetails(receivedAgainst)[0], function(i, item) {
            if(item.oa_id == "") {
                isValid = false;
            }
        });

        if(!isValid) {
            new OaPopup().insertOaMaterialsPopup().show();
        } else {
            saveReceipt();
        }
    } else {
        saveReceipt();
    }
}

function saveReceipt(oa_code="") {
//console.log('saveReceipt is invoked');
    var receivedAgainst = $("#recagainst").val();
    var tax_details = [];
    var tag_details = [];
    var quantityCheck = true;
	var purchase_account_id = ($("#purchase_account option:selected").val() == "0") ? null : $("#purchase_account option:selected").val();
    material_data = constructMaterialDetails(receivedAgainst);
    grn_materials = material_data[0];
    if ($("#recagainst").val() == "Issues") {
        var wo_id = $("#id_wo").val();
        if (wo_id && wo_id != 'undefined' && wo_id != '' && wo_id !=0) {
            var acc_qty = grn_materials[0]['acc_qty'];
            var text = $('#id_wo option:selected').text();
            if (text.includes("PP")){
                $.ajax({
                    url: "/erp/stores/json/po_qty/",
                    type: "POST",
                    dataType: "json",
                    data: {wo_id:wo_id},
                    async: false,
                    success: function(response) {
                        var qty = response['pp_qty'] - response['completed_qty'] - acc_qty
                        if (qty < 0){
                            quantityCheck = false
                            swal("Failure", `IRN Accepted Qty cannot be greater than PP Qty.`, "warning")
                        }
                    }
                });
            }
        }
    }
    if(!quantityCheck) {
        return false;
    }
    check_tax = material_data[1];
    tax_codes = document.getElementsByName('po_tax_code');
    var tax_order = 0
    for(i=0; i<tax_codes.length; i++){
        var tax_data ={tax_code: tax_codes[i].value, tax_order: tax_order }
        tax_order += 1;
        tax_details.push(tax_data);
    }
    var tag_details = [];
    $("#grn_tags_table").find('.li-tagit-display:visible').each(function(){
        var tag_data = $(this).find('.label-tagit-display').text()
        tag_details.push(tag_data)
    });

    var selectedIssueIds = findSelectedValuesFromMultiSelect("issueno");
    var rejectionRemarks = $("#txtrejremarks").val();

    if($("#rejected-remarks").hasClass("hide")) {
        rejectionRemarks = "";
    }
    grn_header = {

        receipt_no: $("#id_receipt_no").val(),
        received_against: receivedAgainst,
        supplier_id: getSelectedSupplierId(),
        project_code: $("#project option:selected").val(),
        invoice_type: $("#dc_invoice").val(),
        invoice_no: $("#invno").val().trim(),
        invoice_date: $("#invdate").val(),
        matrecthrough: $("#recthrough").val(),
        purchase_account_id: purchase_account_id,
        packing_charges: $("#packing_charges").val(),
        transport_charges: $("#transport_charges").val(),
        inward_no: $("#inwardno").val(),
        inward_date: $("#inwdate").val(),
        inspected_by: $("#inspector").val(),
        remarks: $("#txtremarks").val(),
        rejection_remarks: rejectionRemarks,
        grn_save_approve_status: $("#id_grn_save_approve_status").val(),
        other_charges: $("#txtothers").val(),
        invoice_value: (receivedAgainst == 'Issues' || receivedAgainst == 'Others'? 0 : $("#txtnetvalue").val()),
        duty_passed: $("#txt_duty_passed").val(),
        currency_id: $("#id_currency option:selected").val(),
        currency_conversion_rate: $("#currency_conversion_rate").val(),
        round_off: $("#txtround_off").val(),
        goods_already_received: $("#goods_already_received").is(":checked"),
        tax_details: tax_details, tag_details: tag_details, selectedIssueIds: selectedIssueIds,
        ecommerce_gstin: $("#id_gstin").val(),
        grn_materials: grn_materials,
        location_id:  $("#grn-location option:selected").val()
    };

    var document_data = null;
    var document_name = $("#id_receipt-document").val();
    if (document_name != "") {
        document_data = $("#id_receipt-document_data").val();
    }
    var data = {
        grn_header: JSON.stringify(grn_header),
        invoice_doc_name: document_name,
        invoice_doc_data: document_data,
        is_super_edit: $("#is_super_user").val().toLowerCase(),
        oa_code: oa_code,
        documents: $(".file_upload_json").val(),
        validate_invoice_no: true
    };

    if (tax_codes.length == 0 && check_tax) {
        setTimeout(function() {
            confirmAction(message="One or more item has no tax attached!", callback=function(isConfirm) {
                if (isConfirm) {
                    setTimeout(function() {
                        saveProcess(data);
                    }, 100);
                }
            });
        }, 500);
    } else {
        saveProcess(data);
    }
}

function saveProcess(data) {
//console.log('-->  saveProcess is invoked');
    var current_currency = $("#id_currency option:selected").text();
    var home_currency = $("#home_currency_id").val();
    var duty_pass_reminder_message ="Party seems to be passing Duties in the past.";
    var currency_conversion_reminder_message = `Rate of Currency Conversion from ${current_currency} to ${home_currency} is mentioned as 1.00`;
    var confirm_message =  "Do you confirm?";
    var allow_save_flag = true;

//    $("#grn-location").val();

   //     alert("func started");

    if ($("#recagainst").val() == "Issues") {
        supplier_code = 0;
    } else if (((parseInt($("#supplier option:selected").val().split("[::]")[1]) & 1) == 1) && (parseFloat($("#txt_duty_passed").val()) <= 0)){
        confirm_message = duty_pass_reminder_message + '\n' + confirm_message;
        allow_save_flag = false;
    }
    if($("#id_currency option:selected").text()!=$("#id_home_currency").val() && $("#recagainst").val() != "Others"){
        if(parseFloat($("#currency_conversion_rate").val())==parseFloat(1)){
            confirm_message = currency_conversion_reminder_message + '\n' + confirm_message;
            allow_save_flag = false;
        }
    }
    if(!allow_save_flag){
        allow_save_flag = window.confirm(confirm_message);
    }
    if (allow_save_flag) {
        $("#loading").show();
        $("#cmdSavegrn").val('Processing...').addClass('btn-processing');

        $.ajax({
            url: "/erp/stores/json/receipt/",
            type: "POST",
            dataType: "json",
            data: data,
            success: function(response) {
                if (response.response_message == "Session Timeout") {
                    location.reload();
                    return;
                }
                if (response.response_message == "Success") {
                    if($("#id_receipt_no").val() != '' && event_action != 'Super-Edit'){event_action="Update";}
                    ga('send', 'event', moduleName, event_action, $('#enterprise_label').val(), 1);
                    $('#order_no').html(response.grn_no);
                    $('#view_receipt_document').show();
                    $("#id_receipt_no").val(response.grn_no);
                    setTimeout(function() {
                        $("#loading").hide();
                        swal({
                            title: '<span style="color: #44ad6b;">Saved Successfully</span>',
                            text: response.custom_message,
                            type: "success",
                            allowEscapeKey: false
                        },
                        function() {
                            $("#po_details").val("");
                            editReceipt(response.grn_no);
                        });
                    }, 100);
                } else {
                    $('#view_receipt_document').hide();
                    if(response.warning_type == "Hard_warning"){
                        setTimeout(function() {
                            $("#loading").hide();
                            swal({
                                title: "",
                                text: response.custom_message,
                                type: "error"
                            });
                        }, 100);
                    }else{
                        confirmAction(message=response.custom_message, callback=function(isConfirm){
                            if (isConfirm){
                                data['validate_invoice_no'] = false
                                saveProcess(data);
                            }
                        });
                    }
                }
                $("#loading").hide();
                $("#cmdSavegrn").val('Save').removeClass('btn-processing');
                getAllMenuPendingCount();
            }
        });
        return false;
    }
}

function grnPrevNextPaging() {
    if($(".header_current_page").text().indexOf("New") >=0 || $(".header_current_page").text() == "") {
        $(".prev_next_container").remove();
    }
    else {
        var storageKey = "grnListNav";
        if ($("#recagainst").val().trim() == "Issues") {
            storageKey = 'irnListNav';
        } else if ($("#recagainst").val().trim() == "Sales Return") {
            storageKey = 'srListNav';
        }
        var receiptIdList = JSON.parse(localStorage.getItem(storageKey));
        if(receiptIdList != null) {
            var curGRNId = $("#id_receipt_no").val();
            for (var i = 0; i < receiptIdList.length; i++) {
                if (receiptIdList[i].grnId == curGRNId){
	                if(i != 0) {
	                    var prevGRNId = receiptIdList[i-1].grnId;
	                    var prevGRNNo = receiptIdList[i-1].grnNumber;
	                }
	                if(i != Number(receiptIdList.length - 1)) {
	                    var nextGRNId = receiptIdList[i+1].grnId;
	                    var nextGRNNo = receiptIdList[i+1].grnNumber;
	                }
                }
            }
            var prevNextGRN = "";
            var isPrevNextGRNEnabled = false;
            if(nextGRNId) {
                prevNextGRN += '<a role="button" class="btn btn-add-new pull-right btn-data-tooltip" data-tooltip="tooltip" title="Next '+moduleName+': '+nextGRNNo+'" style="margin-right: 7px;" onclick="javascript:editReceipt('+nextGRNId+');"><i class="fa fa-forward" aria-hidden="true"></i></a>';
                isPrevNextGRNEnabled = true;
            }
            else {
                prevNextGRN += '<a class="btn btn-add-new pull-right disabled btn-data-tooltip" data-tooltip="tooltip" title="Next/ Prev will appear only for the current page" style="padding: 5px 12px; margin-right: 7px;"><i class="fa fa-forward" aria-hidden="true"></i></a>';
            }
            if(prevGRNId) {
                prevNextGRN += '<a role="button" class="btn btn-add-new pull-right btn-data-tooltip" data-tooltip="tooltip" title="Prev. '+moduleName+': '+prevGRNNo+'" style="margin-right: 7px;" onclick="javascript:editReceipt('+prevGRNId+');"><i class="fa fa-backward" aria-hidden="true"></i></a>';
                isPrevNextGRNEnabled = true;
            }
            else {
                prevNextGRN += '<a class="btn btn-add-new pull-right disabled btn-data-tooltip" data-tooltip="tooltip" title="Next/ Prev will appear only for the current page" style="padding: 5px 12px; margin-right: 7px;"><i class="fa fa-backward" aria-hidden="true"></i></a>';
            }
            if(isPrevNextGRNEnabled) {
                $(".prev_next_container").html(prevNextGRN)
                $('.btn-data-tooltip').tooltip({
                    trigger : 'hover'
                });
            }
        }
    }
}

var resetMaterialTables = function () {
   $("#materialtable tbody.item-for-goods tr, #materialtable tbody.item-for-service tr").remove();
    $("#dc_materialtable tbody tr").remove();
    $("#po_taxes_table tr").remove();
    Calculate();
}

/**
 * ************** MaterialManager.constructor starts here **************
 * MaterialManager loads/adds/removes materials to the tables based on the selection criteria
 **/
var MaterialManager = function() {
    // Basic filters and headers of Receipt
    this.receiptId = $("#id_receipt_no").val();
    this.receivedAgainst = $("#recagainst").val();
    this.isGoodsAlreadyReceived = false;
    if(["Purchase Order", "Job Work", "Sales Return"].indexOf(this.receivedAgainst) != -1) {
        this.isGoodsAlreadyReceived = $("#goods_already_received").is(":checked");
    };
    this.supplierId = getSelectedSupplierId();

    // Finding selected PO Ids and previously Selected PO Ids
    this.selectedPoIds = findSelectedValuesFromMultiSelect("pos");
    this.selectedPoIdsAsString = this.selectedPoIds.join(",");
    this.pastSelectedPoIds = [];
    if ($("#po_details").val().trim().length > 0) {
        this.pastSelectedPoIds = $("#po_details").val().split(",");
    }
    $("#po_details").val(this.selectedPoIdsAsString);
    this.recentSelectedPoId = null;
    this.recentUnSelectedPoId = null;
    var changedItem = null;
    if(this.pastSelectedPoIds.length > this.selectedPoIds.length) {
        hasNewItem(oldList=this.selectedPoIds, newList=this.pastSelectedPoIds, callback=function(newItem) {
            changedItem = newItem;
        });
        this.recentUnSelectedPoId = changedItem;
    } else {
        hasNewItem(oldList=this.pastSelectedPoIds, newList=this.selectedPoIds, callback=function(newItem) {
            changedItem = newItem;
        });
        this.recentSelectedPoId = changedItem;
    }

    // Finding selected PartyDcIds and previously Selected Party Ids
    this.selectedPartyDcIds = findSelectedValuesFromMultiSelect("party_DcNo");
    this.selectedPartyDcIdsAsString = this.selectedPartyDcIds.join(",");
    this.pastSelectedPartyDcIds = [];
    if ($("#party_details").val().trim().length > 0)
        this.pastSelectedPartyDcIds = $("#party_details").val().split(",");
    $("#party_details").val(this.selectedPartyDcIdsAsString);
    // Finding recent PartyDcIds selected
    this.recentSelectedPartyDcId = null;
    this.recentUnSelectedPartyDcId = null;
    changedItem = null;
    if(this.pastSelectedPartyDcIds.length > this.selectedPartyDcIds.length) {
        hasNewItem(oldList=this.selectedPartyDcIds, newList=this.pastSelectedPartyDcIds, callback=function(newItem) {
            changedItem = newItem;
        });
        this.recentUnSelectedPartyDcId = changedItem;
    } else {
        hasNewItem(oldList=this.pastSelectedPartyDcIds, newList=this.selectedPartyDcIds, callback=function(newItem) {
            changedItem = newItem;
        });
        this.recentSelectedPartyDcId = changedItem;
    }

    // Finding selected Issue/DC Ids and previously Selected Issue/DC Ids
    this.selectedIssueIds = findSelectedValuesFromMultiSelect("issueno");
    this.selectedIssueIdsAsString = this.selectedIssueIds.join(",");
    this.pastSelectedIssueIds = [];
    if ($("#issueno_details").val().trim().length > 0) {
        this.pastSelectedIssueIds = $("#issueno_details").val().split(",");
    }
    $("#issueno_details").val(this.selectedIssueIdsAsString);

    // Finding recent IssueId selected
    this.recentSelectedIssueId = null;
    this.recentUnSelectedIssueId = null;
    changedItem = null;
    if(this.pastSelectedIssueIds.length > this.selectedIssueIds.length) {
        hasNewItem(oldList=this.selectedIssueIds, newList=this.pastSelectedIssueIds, callback=function(newItem) {
            changedItem = newItem;
        });
        this.recentUnSelectedIssueId = changedItem;
    } else {
        hasNewItem(oldList=this.pastSelectedIssueIds, newList=this.selectedIssueIds, callback=function(newItem) {
            changedItem = newItem;
        });
        this.recentSelectedIssueId = changedItem;
    }
}
/************** MaterialManager.constructor ends here **************/

/************** MaterialManager.UtilityWorks starts here **************/
var findSelectedValuesFromMultiSelect = function (elementId) {
    var selectedValues = [];
    $(`select#${elementId}`).next('.btn-group').find('ul').find('li input:checked').each(function () {
        selectedValues.push($(this).val());
    });
    return selectedValues;
}
/************** MaterialManager.UtilityWorks ends here **************/



MaterialManager.prototype.fetchChargeFlags = function () {
    $.ajax({
		url: "/erp/stores/json/grn/fetch_charge_flags/",
		type: "post",
		datatype:"json",
		data: {po_ids: this.selectedPoIdsAsString},
		success: function(response) {
            if (response[1] || parseFloat($("#packing_charges").val()) > 0) {
                $("#chk_packing").attr('checked', true);
                $("#packing_charges").attr('disabled', false);
            } else {
                $("#chk_packing").attr('checked', false);
                $("#packing_charges").attr('disabled', true);
            }
            if (response[0] || parseFloat($("#transport_charges").val()) > 0) {
                $("#chk_transport").attr('checked', true);
                $("#transport_charges").attr('disabled', false);
            } else {
                $("#chk_transport").attr('checked', false);
                $("#transport_charges").attr('disabled', true);
            }
		}
	});
}

/************** MaterialManager.SelectWorks starts here **************/
/************** MaterialManager.onChangePoSelection **************/
MaterialManager.prototype.onChangePoSelection = function () {
    if (this.recentSelectedPoId == null && this.recentUnSelectedPoId == null) {
        console.log("Unwanted call ", [this.pastSelectedPoIds, this.selectedPoIds, this.recentSelectedPoId, this.recentUnSelectedPoId]);
        return
    }
    if(this.selectedPoIds != "") {
        if ("Job In" == this.receivedAgainst) {
            loadOaMaterials(
                receivedAgainst=this.receivedAgainst, supplierId=this.supplierId,
                selectedOaIdsAsString=this.selectedPoIdsAsString, receiptId=this.receiptId);
            if (this.receiptId == "") {
                loadOaTags(this.selectedPoIdsAsString);
            }
        }
    }
    if (this.isGoodsAlreadyReceived) {
        this.loadGoodsReceivedDcNumbers();
    } else if (this.recentSelectedPoId != null) {
        if(["Purchase Order", "Job Work"].indexOf(this.receivedAgainst) != -1) {
            loadPoMaterials(
                receivedAgainst=this.receivedAgainst, receipt_no=this.receiptId,
                supplierId=this.supplierId, poIds=this.recentSelectedPoId);
            if (this.receiptId == "") {
                loadPoTags(this.selectedPoIdsAsString);
            }
        }
        else if(["Delivery Challan", "Sales Return"].indexOf(this.receivedAgainst) != -1) {
            loadDcMaterials(this.recentSelectedPoId, this.receivedAgainst, this.supplierId, this.receiptId);
            if (this.receiptId == "") {
                loadInvoiceTags(this.selectedPoIdsAsString);
            }
        }
    } else {
        var materialMan = this;
        this.validateStock(callback=function(isConfirm) {
            if (isConfirm) {
                removeTableRow(materialMan.recentUnSelectedPoId);
                removeDeletedPo(materialMan.recentUnSelectedPoId);
                changeGrnTableHeader();
                if (materialMan.receiptId == "" && materialMan.selectedPoIds.length != 0) {
                    loadPoTags(materialMan.selectedPoIdsAsString);
                }
            } else {
               $("#pos").multiselect('select', materialMan.recentUnSelectedPoId, true);
            }
        });
    }
    this.fetchChargeFlags();
}

/************** MaterialManager.onChangePartyDcSelection **************/
MaterialManager.prototype.onChangeDcSelection = function () {
    if (this.recentSelectedPartyDcId == null && this.recentUnSelectedPartyDcId == null) {
        console.log("Unwanted call ", [this.recentSelectedPartyDcId, this.recentUnSelectedPartyDcId]);
        return
    }
    if (this.recentSelectedPartyDcId != null) {
        this.addGrnMaterials(this.recentSelectedPartyDcId);
    } else if (this.recentUnSelectedPartyDcId != null) {
        var materialMan = this;
        this.validateStock(callback=function (isConfirm) {
            if (isConfirm) {
                deleteDcRemoveRow(materialMan.recentUnSelectedPartyDcId);
                changeGrnTableHeader();
                removeDeletedDc(materialMan.recentUnSelectedPartyDcId);
                if (materialMan.receiptId == "" && materialMan.selectedPartyDcIds.length != 0) {
                    loadPoTags(materialMan.selectedPoIdsAsString);
                }
                Calculate();
            } else {
                $("#party_DcNo").multiselect('select', materialMan.recentUnSelectedPartyDcId, true);
            }
        });
    }
}

/************** MaterialManager.onChangeIssueSelection **************/
MaterialManager.prototype.onChangeIssueSelection = function () {
    if (this.recentSelectedIssueId == null && this.recentUnSelectedIssueId == null) {
        console.log("Unwanted call ", [this.recentSelectedIssueId, this.recentUnSelectedIssueId]);
        return
    }
    if (this.recentSelectedIssueId != null) {
        loadIssueMaterials(this.recentSelectedIssueId);
        if (this.receiptId == "") {
            loadInvoiceTags(this.selectedIssueIdsAsString);
        }
    } else if (this.recentUnSelectedIssueId != null) {
        var materialMan = this;
        this.validateStock(callback=function(isConfirm){
            if (isConfirm) {
                removeTableRow(materialMan.recentUnSelectedIssueId);
                changeGrnTableHeader();
                removeDeletedPo(materialMan.recentUnSelectedIssueId);
                if (materialMan.receiptId == "" && materialMan.selectedIssueIds.length != 0) {
                    loadInvoiceTags(materialMan.selectedIssueIdsAsString);
                }
            } else {
               $("#issueno").multiselect('select', materialMan.recentUnSelectedIssueId, true);
            }
        });
    }
}

/************** MaterialManager.loadSelectedDcNumbers **************/
MaterialManager.prototype.loadSelectedDcNumbers = function () {
    var materialMan = this;
    $.ajax({
        url:"/erp/stores/json/grn/load_selected_dc_list/",
        type: "post",
        datatype:"json",
        data: {grn_id:this.receiptId, goods_already_received:true},
        success: function(response) {
            for (j = 0; j < response.length; j++) {
                $("#party_DcNo").multiselect('select', response[j][0], true);
            }
            partyDCSelectChange();
            if (new MaterialManager().selectedPartyDcIds.length > 0) {
                loadGrnMaterials($("#id_receipt_no").val(), $("#recagainst").val());
            } else {
                $("#loading").hide();
            }
        }
    });
}

/************** MaterialManager.loadGoodsReceivedDcNumbers **************/
MaterialManager.prototype.loadGoodsReceivedDcNumbers = function () {
    var materialMan = this;
    //$('#loading').show();
    $("#party_DcNo").next(".btn-group").find(".multiselect").addClass("disabled").attr("title", "Loading... Please wait");
    $.ajax({
        url: "/erp/stores/json/grn/goods_already_received_numbers/",
        type: "post",
        datatype:"json",
        async: false,
        data: {
            received_against: this.receivedAgainst,
            selected_supplier: this.supplierId,
            grn_no: this.receiptId
        },
        success: function(response) {
            try {
                $("#party_DcNo").html("")
                for (i = 0; i < response.length; i++) {
                    if ($("#party_DcNo").find("option[value='"+response[i][1]+"']").length == 0) {
                        $("#party_DcNo").append("<option value="+response[i][1]+"> "+response[i][0] +" ("+ response[i][2] + ")"+"</option>");
                    }
                }
                $('#party_DcNo').multiselect("rebuild");
                var prevSelected = $('#party_details').val().split(",");
                $.each(prevSelected,function(i) {
                    if(prevSelected[i] != "") {
                        if($("#party_DcNo").find("option[value='"+prevSelected[i]+"']").length <= 0) {
                           deleteDcRemoveRow(prevSelected[i]);
                           changeGrnTableHeader();
                           removeDeletedDc(prevSelected[i]);
                        }
                    }
                });
                loadPreviouslySelectedDC();
                if(materialMan.receiptId != "") {
                    materialMan.loadSelectedDcNumbers();
                } else {
                    partyDCSelectChange();
                }
            } catch(e) {
                console.log(".loadGoodsReceivedDcNumbers", e);
            } finally {
                $("#party_DcNo").next(".btn-group").find(".multiselect").removeClass("disabled").attr("title", "");
                //$('#loading').hide();
            }
        }
    });
}
/************** MaterialManager.SelectWorks ends here **************/
/************** MaterialManager.MaterialWorks **************/
MaterialManager.prototype.addGrnMaterials = function (recentSelectedPartyDcId) {
    var reset_values = false;
    var materialMan = this;
    $('#loading').show();
    $.ajax({
        url: "/erp/stores/json/grn/loadgrnmaterial/",
        type: "post",
        datatype: "json",
        data: {
            grn_id: recentSelectedPartyDcId,
            received_against: this.receivedAgainst,
            selected_supplier: this.supplierId,
            goods_already_received: this.isGoodsAlreadyReceived,
            grn_no: this.receiptId
        },
        success: function(response) {
            try {
                constructPoReceiptMaterialTHeader(materialMan.receivedAgainst);
                var dcGrnCodeString = $(`#party_DcNo option[value="${recentSelectedPartyDcId}"]`).text();
                dcGrnCode = dcGrnCodeString.replace(")", "").split("(")[1].split(" ")[0];
                var poIds = [];
                var materialTable = new MaterialTable(
                    receivedAgainst=materialMan.receivedAgainst, tableId="materialtable", isGoodsAlreadyReceived=materialMan.isGoodsAlreadyReceived);
                var dcMaterialTable = new MaterialTable(
                    receivedAgainst=materialMan.receivedAgainst, tableId="dc_materialtable", isGoodsAlreadyReceived=materialMan.isGoodsAlreadyReceived);
                $.each(response, function(i, item) {
                    var drawingNumber = "";
                    var itemDescription = "";
                    [drawingNumber, itemDescription] = formatMaterialDescription(item);
                    item.drawing_no = drawingNumber;
                    item.item_name = itemDescription;
                    item.dc_grn_code = dcGrnCode;
                    // Below are elements of GoodsAlreadyReceived
                    item.rec_grn_id = recentSelectedPartyDcId;
                    if (materialMan.isGoodsAlreadyReceived) {
                        if (item.purchase_account_id > 0){
                            $('#purchase_account').val(item.purchase_account_id);
                            $('#purchase_account').trigger("chosen:updated");
                        }
                        item.dc_qty = item.pending_qty = item.pending_dc_qty;
                        item.rec_qty = item['total_rec_qty'];
                        item.short_qty = item['total_dc_shortage_qty'];
                        item.acc_qty = item['total_acc_qty'];
                        item.rej_qty = item['total_dc_rejected_qty'];
                    }
                    if (item.pending_dc_qty > 0){
                        if (item.dc_id > 0 && receivedAgainst == "Job Work") {
                            dcMaterialTable.createRow(item=item);
                        } else {
                            materialTable.createRow(item=item);
                        }
                    }
                    if (item.po_id != null && item.po_id != "" && poIds.indexOf[item.po_id] == -1) {
                        poIds.push(item.po_id);
                    }
                });
                if (poIds.length > 0) {
                    loadPoTags(poIds.join(","));
                }

                $("#materialrow").addClass("hide");
                $(".material-header").text("Item Detail").css({width: "200px"});
                $('.invoicerow').show();
                $('#taxrow').show();
                $('#rec_from_div').show();
                $('#party_DcNo').prop('disabled',false);
            } catch(e) {
                console.log(".addGrnMaterials", e);
            } finally {
                $('#loading').hide();
            }
            Calculate();
        }
    });
}

function updateConsolidatedPrice(curr) {
    var currentClass = $(curr).closest('tr').attr('consolidated-for');
    var closestTR = $(curr).closest('tr');
    setTimeout(function(){
        $("#materialtable ."+currentClass).each(function(){
            $(this).find('input[name="price"]').val(closestTR.find('input[name="price"]').val());
            $(this).find('input[name="discount"]').val(closestTR.find('input[name="discount"]').val());
            $(this).find('select[name="cgst"]').val(closestTR.find('select[name="cgst"] option:selected').val());
            $(this).find('select[name="sgst"]').val(closestTR.find('select[name="sgst"] option:selected').val());
            $(this).find('select[name="igst"]').val(closestTR.find('select[name="igst"] option:selected').val());
            $(this).find('input[name="hsn_code"]').val(closestTR.find('input[name="hsn_code"]').val());
        });
        Calculate();
        calculateConsolidatedPrice(curr);
    }, 10);
}

function calculateConsolidatedPrice(curr){
    var closestTR = $(curr).closest('tr');
    var currDCQty = closestTR.find('.total_dc_qty').text();
    var currPrice = closestTR.find('input[name="price"]').val();
    var currDisc = closestTR.find('input[name="discount"]').val()/100;
    var currCgst = Number(closestTR.find('select[name="cgst"] option:selected').text())/100;
    var currSgst = Number(closestTR.find('select[name="sgst"] option:selected').text())/100;
    var currIgst = Number(closestTR.find('select[name="igst"] option:selected').text())/100;
    var calcDisc = Number((currDCQty * currPrice) * currDisc);
    var currTotal = Number((currDCQty * currPrice) - calcDisc);
    closestTR.find('input[name="totalprice"]').val(Number(currTotal).toFixed(2));
    closestTR.find('span[name="cgstamt"]').text(Number(currTotal * currCgst).toFixed(2));
    closestTR.find('span[name="sgstamt"]').text(Number(currTotal * currSgst).toFixed(2));
    closestTR.find('span[name="igstamt"]').text(Number(currTotal * currIgst).toFixed(2));
}

function updateConsolidatedQty(element) {
    setTimeout(function() {
        if($(element).is("input")) {
            currRow = $(element).closest('tr');
        }else{
            currRow = element
        }
        var currentClass = $(currRow).attr('data-row');
        var currentTable = $(currRow).closest('table').attr('id');
        var curDCQty = 0, curRecQty = 0, curShortQty = 0, curAcceptQty = 0, curRejectQty = 0, curQtyUnit = "";
        $("#"+currentTable+" ."+currentClass).each(function(){
            curDCQty += Number($(this).find(`input[name="dc_qty"]`).val());
            curRecQty += Number($(this).find(`input[name="rec_qty"]`).val());
            curShortQty += Number($(this).find(`span[name="short_qty"]`).text());
            curAcceptQty += Number($(this).find(`input[name="acc_qty"]`).val());
            curRejectQty += Number($(this).find(`span[name="rej_qty"]`).text());
            curQtyUnit = $(this).find(".td-unit-name:first").find(".unit_value").text();
        });

        $("#"+currentTable).find("tr[consolidated-for='"+currentClass+"'] .total_dc_qty").text(curDCQty.toFixed(3));
        $("#"+currentTable).find("tr[consolidated-for='"+currentClass+"'] .total_rec_qty").text(curRecQty.toFixed(3));
        $("#"+currentTable).find("tr[consolidated-for='"+currentClass+"'] .total_short_qty").text(curShortQty.toFixed(3));
        $("#"+currentTable).find("tr[consolidated-for='"+currentClass+"'] .total_accept_qty").text(curAcceptQty.toFixed(3));
        $("#"+currentTable).find("tr[consolidated-for='"+currentClass+"'] .total_reject_qty").text(curRejectQty.toFixed(3));
        $("#"+currentTable).find("tr[consolidated-for='"+currentClass+"'] .td-unit-selected-text").text(curQtyUnit);
        if(currentTable == "materialtable") {
            calculateConsolidatedPrice($("#materialtable tr[consolidated-for='"+currentClass+"']").find(`input[name="totalprice"]`));
        }
    }, 100);
}

function updateConsolidatedHeaderUnit(element) {
    setTimeout(function() {
        var currentClass = $(currRow).attr('data-row');
        var currentTable = $(currRow).closest('table').attr('id');
        var unitList = [];
        $("#"+currentTable+" ."+currentClass).each(function(){
            unitList.push($(this).find("input[name='alternate_unit_id']").val());
        });
        if(!unitList.every( (val, i, arr) => val === arr[0] )) {
            $("#"+currentTable).find("tr[consolidated-for='"+currentClass+"']").find("select[name='alternate_units']").val("0");
        }
        else {
            $("#"+currentTable).find("tr[consolidated-for='"+currentClass+"']").find("select[name='alternate_units']").val(unitList[0]);
        }
        $("#"+currentTable).find("tr[consolidated-for='"+currentClass+"']").find("select[name='alternate_units']").trigger("change");
    },100);
}

function updateConsolidatedUnit(element) {
    var currentClass = $(element).closest('tr').attr('consolidated-for');
    var currentTable = $(element).closest('table').attr('id');
    var currentUnitValue = $(element).val();
    var currentUnitText = $(element).find("option:selected").text();
    var currentUnitScaleFactor = $(element).find("option:selected").attr("data-val");
    var lastItem = "";
    $("#"+currentTable+" ."+currentClass).each(function(){
        $(this).find('input[name="alternate_unit_id"]').val(currentUnitValue);
        $(this).find('input[name="scale_factor"]').val(currentUnitScaleFactor);
        $(this).find(".td-unit-name span").text(currentUnitText);

        var updatedDcQty = ($(this).find(`input[name="dc_qty"]`).attr("data-default-value") / currentUnitScaleFactor).toFixed(3)
        var updatedRecQty = ($(this).find(`input[name="rec_qty"]`).attr("data-default-value") / currentUnitScaleFactor).toFixed(3)
        var updatedShortQty = ($(this).find(`span[name="short_qty"]`).attr("data-default-value") / currentUnitScaleFactor).toFixed(3)
        var updatedAccQty = ($(this).find(`input[name="acc_qty"]`).attr("data-default-value") / currentUnitScaleFactor).toFixed(3)
        var updatedRejQty = ($(this).find(`span[name="rej_qty"]`).attr("data-default-value") / currentUnitScaleFactor).toFixed(3)

        $(this).find(`input[name="dc_qty"]`).val(updatedDcQty);
        $(this).find(`input[name="rec_qty"]`).val(updatedRecQty);
        $(this).find(`span[name="short_qty"]`).text(updatedShortQty);
        $(this).find(`input[name="acc_qty"]`).val(updatedAccQty).trigger("blur");
        $(this).find(`span[name="rej_qty"]`).text(updatedRejQty);
        if(updatedRejQty > 0 ) {
            $(this).find(`span[name="rej_qty"]`).closest("span").addClass("link-addon");
        }
        else {
            $(this).find(`span[name="rej_qty"]`).closest("span").removeClass("link-addon");
        }
        lastItem = $(this).find(`span[name="rej_qty"]`);
        var updatedTotalDcQty = ($(this).find(`.total_dc_qty`).attr("data-default-value") / currentUnitScaleFactor).toFixed(3)
        var updatedTotalRecQty = ($(this).find(`.total_rec_qty`).attr("data-default-value") / currentUnitScaleFactor).toFixed(3)
        var updatedTotalShortQty = ($(this).find(`.total_short_qty`).attr("data-default-value") / currentUnitScaleFactor).toFixed(3)
        var updatedTotalAccQty = ($(this).find(`.total_accept_qty`).attr("data-default-value") / currentUnitScaleFactor).toFixed(3)
        var updatedTotalRejQty = ($(this).find(`.total_reject_qty`).attr("data-default-value") / currentUnitScaleFactor).toFixed(3)

        $(this).find(`.total_dc_qty`).text(updatedTotalDcQty);
        $(this).find(`.total_rec_qty`).text(updatedTotalRecQty);
        $(this).find(`.total_short_qty`).text(updatedTotalShortQty);
        $(this).find(`.total_accept_qty`).text(updatedTotalAccQty);
        $(this).find(`.total_reject_qty`).text(updatedTotalRejQty);
        $(this).find(`.td-unit-selected-text`).text(currentUnitText);

        var updatedPendingQty = ($(this).find(`input[name="pending_qty"]`).attr("data-default-value") / currentUnitScaleFactor).toFixed(3)
        var updatedSavedAccQty = ($(this).find(`input[name="saved_acc_qty"]`).attr("data-default-value") / currentUnitScaleFactor).toFixed(3)

        $(this).find(`input[name="pending_qty"]`).val(updatedPendingQty);
        $(this).find(`input[name="saved_acc_qty"]`).val(updatedSavedAccQty);

        if($(this).find('input[name="alternate_unit_id"]').attr('data-default-value') == currentUnitValue) {
            var currentPrice = $(this).find('input[name="price"]').attr('data-default-value');
            var currentDiscount = $(this).find('input[name="discount"]').attr('data-default-value');
            $(element).closest('tr').find('input[name="price"]').val(currentPrice).trigger('blur');
            $(element).closest('tr').find('input[name="discount"]').val(currentDiscount).trigger('blur');
        }
    });
    if(lastItem != "") {
        updateConsolidatedQty(lastItem);
    }

}

MaterialManager.prototype.validateStock = function(callback=function(isConfirm) {}) {
//    if ($("#grn_status").val()  > 0) {
    $('#loading').show();
    $.ajax({
        url: "/erp/stores/json/grn/check_stock_availability/",
        method: "POST",
        data: {
            grn_no: this.receiptId,
            received_against: this.receivedAgainst,
        },
        success: function(response) {
            var [inwardDate, warningMaterials, softWarningMaterials] = findMaterialsWithLowStock(response, this.receivedAgainst);
            if (warningMaterials.length > 0) {
                $.each(softWarningMaterials, function(i, item) {
                    warningMaterials.push(item);
                });
                if ($("#is_super_user").val().toLowerCase().trim() == "true") {
                    confirmAction(message=formatMessageForMaterialDelete(warningMaterials.join(", "), inwardDate), callback=callback);
                } else {
                    var message = "Accepted quantity of item(s) "+ warningMaterials.join(",") + " should not exceeds the Minimum Stock Level. Please check the quantity.";
                    swal({title: "", text: message, type: "error",
                        showCancelButton: false,
                        confirmButtonColor: "#209be1",
                        confirmButtonText: "Ok",
                        closeOnConfirm: true
                    });
                    callback(false);
                }
            } else if (softWarningMaterials.length > 0) {
                confirmAction(message=formatMessageForMaterialDelete(softWarningMaterials.join(", "), inwardDate), callback=callback);
            } else {
                callback(true);
            }
            $('#loading').hide();
        },
        error: function (xhr, errmsg, err) {
            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
        }
    });
//    }
}

function formatMessageForMaterialDelete(itemNames, inwardDate) {
    if (itemNames == "") {
        return "";
    }
    return `Item(s) ${itemNames} have been removed from Stock via one of Issue, DC or Invoice later than ${inwardDate}.
        This may cause the Closing Stock at any of the days later than ${inwardDate} to breach the Minimum Stock Level. Do you still want to continue with the change?`;
}

function materialReturnSerialNo(){
    setTimeout(function(){
        var SNo = 1;
        $("#materialtable tbody").find("tr:visible").each(function(){
            $(this).find('.sno_order').text(SNo);
            SNo++;
        });
    },100);
}

function removeDeletedPo(poNo) {
    if(poNo != "") {
        $("#materialtable").find('.'+poNo).remove();
        materialReturnSerialNo();
        removeDeletedDc(poNo);
    }
}

function removeDeletedDc(dc_id) {
    if(dc_id != "") {
        $("#dc_materialtable").find('.'+dc_id).remove();
        materialReturnSerialNo();
    }
}

/*
 * Removes table row while un selecting po number, jo number and issue number.
 */
function removeTableRow(id) {
    $.each(["materialtable tbody.item-for-goods tr,materialtable tbody.item-for-service tr", "dc_materialtable"], function (i, tableId) {
        $(`#${tableId} tbody`).find("tr").each(function() {
            var rowId = $(this).find(`input[name="po_id"]`).val().trim()
            if($("#recagainst").val() == "Sales Return") {
                rowId = $(this).find(`input[name="dc_id"]`).val().trim()
            }
            if (rowId == id.trim()) {
                $(this).remove();
            }
        });
    });
    Calculate();
}

/*
 * Removes table row for the given GRN id in case of goods already received
 */
function deleteDcRemoveRow(receivedGrnId) {
    $.each(["materialtable", "dc_materialtable"], function (i, tableId) {
        $(`#${tableId} tbody`).find("tr").each(function() {
            if (!$(this).hasClass("consolidated_row") && !$(this).hasClass("tr-add_item_details")) {
                if ($(this).find(`input[name="rec_grn_id"]`).val().trim() == receivedGrnId.trim()) {
                    var consolidatedBy = $(this).attr('data-row');
                    $(this).remove();
                    if($(`#${tableId}`).find(`.${consolidatedBy}`).length <= 0) {
                        $(`#${tableId}`).find(`tr[consolidated-for="${consolidatedBy}"]`).remove();
                    }
                    updateConsolidatedQty(this);
                    var rowSpanCount = $(`#${tableId} .${consolidatedBy}`).length + 1;
                    var consolidateRowId = `#${tableId} tr[consolidated-for="${consolidatedBy}"]`;
                    $(consolidateRowId).find(".consolidated_price_column").attr("data-rowspan", rowSpanCount);
                    if($(consolidateRowId).find(".consolidated_fa").hasClass("fa-chevron-down")) {
                        $(consolidateRowId).find(".consolidated_price_column").attr("rowspan", rowSpanCount);
                    }
                }
            }
        });
        $(`#${tableId} tbody`).find("tr").find("input[name='dc_qty']").blur();
    });
}

var TotalAmountCalculator = function() {
    this.isRejectedRemarksRequired = false;
}

TotalAmountCalculator.prototype.calculate = function() {
    this.calculateForMaterialTable();
    if($("#mat_returned").is(":visible")) {
        this.calculateForDcMaterialTable();
    }
    if(this.isRejectedRemarksRequired) {
        $("#rejected-remarks").removeClass("hide");
    } else {
        $("#rejected-remarks").addClass("hide");
    }
    TooltipInit();
}

TotalAmountCalculator.prototype.calculateForMaterialTable = function() {
    var calculator = this;
    var [subTotal, totalGst, totalNetTax] = [0,0,0];
    var materialTable = document.getElementById('materialtable');
    var materialTableRowCount = $("#materialtable tbody .item-for-goods,#materialtable tbody.item-for-service").length;
    var [packingCharges, transportCharges, otherCharges, roundedOff] = [$("#packing_charges").val(), $("#transport_charges").val(), $("#txtothers").val(), $("#txtround_off").val()];
    var [simpleRates, simpleAssessRates, taxWiseSubtotal] = [$("input[name='net_rate']"), $("input[name='asses_rate']"), $("input[name='tax']")];
    var [compoundRates, compoundTaxWiseSubtotal, compoundAssessRates] = [$("input[name='net_rate_compound']"), $("input[name='tax_compound']"), $("input[name='asses_rate_compound']")];
    $("input[name='tax_compound'], input[name='tax']").val(0);
    $("#materialtable tbody.item-for-goods tr, #materialtable tbody.item-for-service tr").each(function() {
        if($(this).hasClass("consolidated_row")) return true;
        var currentRow = $(this);

        var [pendingQty, dcQty, receivedQty, shortageQty, acceptedQty, rejectedQty, is_blanket_po] = [currentRow.find("input[name='pending_qty']"), currentRow.find("input[name='dc_qty']"), currentRow.find("input[name='rec_qty']"), currentRow.find("span[name='short_qty']"), currentRow.find("input[name='acc_qty']"), currentRow.find("span[name='rej_qty']"), currentRow.find("input[name='is_blanket_po']")];
        shortageQty.text((dcQty.val() - receivedQty.val()).toFixed(3));
        var prevRejectedQty = Number(rejectedQty.text()).toFixed(3);
        var updatedRejectedQty = (receivedQty.val() - acceptedQty.val()).toFixed(3);
        rejectedQty.text((receivedQty.val() - acceptedQty.val()).toFixed(3));
        if(Number(rejectedQty.text()) > 0) {
            rejectedQty.closest("span").addClass("link-addon");
        }
        else {
            rejectedQty.closest("span").removeClass("link-addon");
        }
        if(updatedRejectedQty == 0) {
            $(this).find("input[name='rejection_profiles']").val('[]')
        }
        else if(Number(prevRejectedQty) > Number(updatedRejectedQty)) {
            var currentRejectionValue =$(this).find("input[name='rejection_profiles']").val();
            if(currentRejectionValue == "") currentRejectionValue = '[]';
            currentRejectionValue = JSON.parse(currentRejectionValue);
            if((currentRejectionValue.length) == 1){
                if((currentRejectionValue[0].reason.toLowerCase() != "rejected")) {
                    rejectedQty.trigger("click");
                }
                else {
                    currentRejectionValue[0].quantity = Number(rejectedQty.text());
                    $(this).find("input[name='rejection_profiles']").val(JSON.stringify(currentRejectionValue))
                }
            }
            else {
                rejectedQty.trigger("click");
            }
        }
        else if(Number(prevRejectedQty) < Number(updatedRejectedQty)) {
            updateRejectionRemarksJson(currentRow)
        }

        var [unitPrice, unitDiscount, totalPrice] = [currentRow.find("input[name='price']"), currentRow.find("input[name='discount']"), currentRow.find("input[name='totalprice']")];
        totalPrice.val(unitPrice.val() * dcQty.val());
        totalPrice.val(((totalPrice.val()) - (totalPrice.val() * unitDiscount.val())/100).toFixed(2));
        subTotal += parseFloat(totalPrice.val());
        var gst = {cgstAmount:0, sgstAmount:0, igstAmount: 0}
        if (currentRow.find("span[name='cgstamt']").length > 0 && currentRow.find("span[name='sgstamt']").length > 0 && currentRow.find("span[name='igstamt']").length > 0) {
            var [cgstRate, sgstRate, igstRate] = [currentRow.find("select[name='cgst'] option:selected"), currentRow.find("select[name='sgst'] option:selected"), currentRow.find("select[name='igst'] option:selected")];
            var [cgstAmount, sgstAmount, igstAmount] = [currentRow.find("span[name='cgstamt']"), currentRow.find("span[name='sgstamt']"), currentRow.find("span[name='igstamt']")];
            if(totalPrice.val() > 0 && cgstRate.text() > 0) {
                gst.cgstAmount = (totalPrice.val() * cgstRate.text()) / 100;
                cgstAmount.text(gst.cgstAmount.toFixed(2))

            }
            else {
                cgstAmount.text("0.00")
            }
            if(totalPrice.val() > 0 && sgstRate.text() > 0) {
                gst.sgstAmount = (totalPrice.val() * sgstRate.text()) / 100;
                sgstAmount.text(gst.sgstAmount.toFixed(2));
            }
            else {
                sgstAmount.text("0.00")
            }
            if(totalPrice.val() > 0 && igstRate.text() > 0) {
                gst.igstAmount = (totalPrice.val() * igstRate.text()) / 100;
                igstAmount.text(gst.igstAmount.toFixed(2))
            }
            else {
                igstAmount.text("0.00")
            }
            totalGst += parseFloat(cgstAmount.text()) + parseFloat(sgstAmount.text()) + parseFloat(igstAmount.text());
        }

        var itemTaxes = currentRow.find("input[name='item_tax']");
        itemTaxes.val(0);
        var itemSimpleTax = calculateItemTax(dcQty.val(), unitPrice.val(), unitDiscount.val(), simpleRates, simpleAssessRates, taxWiseSubtotal, false, 0,0,0,0);
        var itemTaxValue = parseFloat(itemTaxes.val()) +
                             parseFloat(itemSimpleTax) +
                             parseFloat(calculateItemTax(dcQty.val(), unitPrice.val(), unitDiscount.val(), compoundRates, compoundAssessRates, compoundTaxWiseSubtotal, true,
                             parseFloat(itemSimpleTax), gst.cgstAmount, gst.sgstAmount , gst.igstAmount))
        itemTaxes.val(itemTaxValue);
        totalNetTax = parseFloat(totalNetTax) + parseFloat(itemTaxes.val());
        if($("#goods_already_received").is(":checked")) {
            var [totalDcReceivedQty, totalDcShortageQty, totalDcAcceptedQty, totalDcRejectedQty] = [currentRow.find("td.total_dc_qty"), currentRow.find("td.total_short_qty"), currentRow.find("td.total_accept_qty"), currentRow.find("td.total_reject_qty")];
            quantityValidation(pendingQty, dcQty, receivedQty, shortageQty, acceptedQty, rejectedQty, totalDcReceivedQty, totalDcShortageQty, totalDcAcceptedQty, totalDcRejectedQty, 0);
        } else {
            if (currentRow.find("td.doc_code").text().trim() == '-' || currentRow.find("td.doc_code").text().trim() == 'null'){
                quantityValidation(currentRow.find("td.doc_code"), dcQty, receivedQty, shortageQty, acceptedQty, rejectedQty,0,0,0,0,is_blanket_po.val());
            } else {
                quantityValidation(pendingQty, dcQty, receivedQty, shortageQty, acceptedQty, rejectedQty,0,0,0,0,is_blanket_po.val());
            }
        }
        calculator.isRejectedRemarksRequired = calculator.isRejectedRemarksRequired || rejectedQty.text() > 0;
    });
    $("#materialtable tbody.item-for-goods tr, #materialtable tbody.item-for-service tr").each(function() {
        if($(this).hasClass("consolidated_row")) {
            updateConsolidatedQty(this);
        }
    });
    subTotal += parseFloat(packingCharges);
    $("#txttotal").val(subTotal.toFixed(2));

    var packing_simple_tax = calculateItemTax(1, packingCharges, 0,  simpleRates, simpleAssessRates, taxWiseSubtotal, false, 0,0,0,0);
    totalNetTax = parseFloat(totalNetTax) +
              parseFloat(packing_simple_tax) +
              parseFloat(calculateItemTax(1, packingCharges, 0, compoundRates, compoundAssessRates, compoundTaxWiseSubtotal, true,
              parseFloat(packing_simple_tax),0,0,0));

    $("#txtnetvalue").val(((parseFloat(subTotal) + parseFloat(transportCharges) + parseFloat(otherCharges) + parseFloat(roundedOff) + parseFloat(totalGst)) + parseFloat(totalNetTax)).toFixed(2));
}

TotalAmountCalculator.prototype.calculateForDcMaterialTable = function() {
    var calculator = this;
    $("#dc_materialtable tbody tr").each(function() {
        var currentRow = $(this);
        var [dcPendingQty, dcDcQty, dcReceivedQty, dcShortageQty, dcAcceptedQty, dcRejectedQty] = [currentRow.find("input[name='pending_qty']"), currentRow.find("input[name='dc_qty']"), currentRow.find("input[name='rec_qty']"), currentRow.find("span[name='short_qty']"), currentRow.find("input[name='acc_qty']"), currentRow.find("span[name='rej_qty']")];
        var dcPrevRejectedQty = Number(dcRejectedQty.text()).toFixed(3);
        var dcUpdatedRejectedQty = (dcReceivedQty.val() - dcAcceptedQty.val()).toFixed(3);
        dcRejectedQty.text((dcReceivedQty.val() - dcAcceptedQty.val()).toFixed(3));
        if(Number(dcRejectedQty.text()) > 0) {
            dcRejectedQty.closest("span").addClass("link-addon");
        }
        else {
            dcRejectedQty.closest("span").removeClass("link-addon");
        }
        if(dcUpdatedRejectedQty == 0) {
            $(this).find("input[name='rejection_profiles']").val('[]')
        }
        else if(Number(dcPrevRejectedQty) > Number(dcUpdatedRejectedQty)) {
            var currentRejectionValue =$(this).find("input[name='rejection_profiles']").val();
            if(currentRejectionValue == "") currentRejectionValue = '[]';
            currentRejectionValue = JSON.parse(currentRejectionValue);
            if((currentRejectionValue.length) == 1){
                if((currentRejectionValue[0].reason.toLowerCase() != "rejected")) {
                    dcRejectedQty.trigger("click");
                }
                else {
                    currentRejectionValue[0].quantity = Number(dcRejectedQty.text());
                    $(this).find("input[name='rejection_profiles']").val(JSON.stringify(currentRejectionValue))
                }
            }
            else {
                dcRejectedQty.trigger("click");
            }
        }
        else if(Number(dcPrevRejectedQty) < Number(dcUpdatedRejectedQty)) {
            updateRejectionRemarksJson(currentRow)
        }

        dcShortageQty.text((dcDcQty.val() - dcReceivedQty.val()).toFixed(3));
        quantityValidation(dcPendingQty, dcDcQty, dcReceivedQty, dcShortageQty, dcAcceptedQty, dcRejectedQty, 0, 0, 0, 0, 0);
        calculator.isRejectedRemarksRequired = calculator.isRejectedRemarksRequired || dcRejectedQty.text() > 0;
    });
}

function showErrorTip(message, element, additionalClass="") {
    if(message != "") {
        element.before(`<span class="grn_warning_message ${additionalClass}"><i class="fa fa-info" aria-hidden="true"></span>`);
        element.addClass("qty-error-border");
        element.prev('.grn_warning_message').find("i").qtip({
            content: {
                text: `<ul class='ul_for_tool_tip'>${message}</ul>`,
                title: 'Error'
            }
        });
    }
}

function quantityValidation(pendingQty, dcQty, receivedQty, shortageQty, acceptedQty, rejectedQty, totalDcReceivedQty, totalDcShortageQty, totalDcAcceptedQty, totalDcRejectedQty, is_blanket_po){
    dcQty.removeClass("qty-error-border").prev('.grn_warning_message').remove();
    acceptedQty.removeClass("qty-error-border").prev('.grn_warning_message').remove();
    shortageQty.removeClass("qty-error-border").prev('.grn_warning_message').remove();
    receivedQty.removeClass("qty-error-border").prev('.grn_warning_message').remove();
    rejectedQty.removeClass("qty-error-border").prev('.grn_warning_message').remove();
    if(pendingQty.is("input")) {
        var pendingQtyValue = pendingQty.val();
    }
    else {
        var pendingQtyValue = pendingQty.text().trim();
    }
    var acceptedTooltipMessage = "";
    var acceptedBlockedMessage = false;
    if (is_blanket_po == 0 || is_blanket_po == 'null' || is_blanket_po == 'undefined'){
        if($("#recagainst").val() != "Job In" &&  parseFloat(pendingQtyValue) > 0 && parseFloat(pendingQtyValue) < parseFloat(acceptedQty.val())) {
            acceptedTooltipMessage += "<li>Accepted quantity for the Invoice being more than the quantity mentioned as Pending in this Invoice. <br />Pending Qty: "+pendingQtyValue+"</li>";
        }
    }

    if(parseFloat(receivedQty.val()) < parseFloat(acceptedQty.val())) {
        acceptedTooltipMessage += "<li>Accepted quantity for the Invoice being more than the quantity mentioned as Received in this Invoice. <br />Received Qty: "+receivedQty.val()+"</li>";
        acceptedBlockedMessage = true;
    }
    if($("#goods_already_received").is(":checked")){
        if(parseFloat(totalDcAcceptedQty.text()) < parseFloat(acceptedQty.val())){
            acceptedTooltipMessage += "<li>Total Accepted quantity mentioned in this Invoice being more than the total of those mentioned in the DC's received earlier. <br />Dc Accepted Qty: "+totalDcAcceptedQty.text()+"</li>";
        }
        var dcTooltipMessage = "";
        if(parseFloat(pendingQtyValue) < parseFloat(dcQty.val())){
            dcTooltipMessage += "<li>Invoice quantity being more than total DC quantity waiting to be Invoiced.<br /> DC Pending Qty: "+pendingQtyValue+".</li>";
        }

        var shortageTooltipMessage = "";
        if(parseFloat(totalDcShortageQty.text()) < (parseFloat(dcQty.val()) - parseFloat(receivedQty.val()))){
            shortageTooltipMessage += "<li>Total Shortage calculated for an Item being more than the total of shortages calculated from the DC's received earlier.<br /> DC Shortage Qty: "+totalDcShortageQty.text()+".</li>";
        }

        var rejectedTooltipMessage = "";
        if(parseFloat(totalDcRejectedQty.text()) < (parseFloat(receivedQty.val()) - parseFloat(acceptedQty.val()))) {
            rejectedTooltipMessage = "<li>Total Rejection calculated for an Item being more than the total of rejection calculated from the DC's received earlier. <br />DC Rejected Qty: "+totalDcRejectedQty.text()+".</li>";
        }

        var receivedTooltipMessage = "";
        if(parseFloat(dcQty.val()) < parseFloat(receivedQty.val())){
            receivedTooltipMessage = "<li>Received quantity for the Invoice being more than the quantity to be Invoiced. <br />Invoice Qty: "+dcQty.val()+".</li>";
        }
        if(parseFloat(totalDcReceivedQty.text()) < parseFloat(receivedQty.val())){
            receivedTooltipMessage = "<li>Total Received quantity mentioned in this Invoice being more than the total of those mentioned in the DC's received earlier. <br />DC Received Qty: "+totalDcReceivedQty.text()+".</li>";
        }
        showErrorTip(dcTooltipMessage, dcQty);
        showErrorTip(shortageTooltipMessage, shortageQty);
        showErrorTip(rejectedTooltipMessage, rejectedQty);
        showErrorTip(receivedTooltipMessage, receivedQty);
    }
    showErrorTip(acceptedTooltipMessage, acceptedQty, acceptedBlockedMessage ? "grn_blocker_warning_message": "");
}

function quantityValidationResult() {
    var quantityResult = true;
    if(($(".grn_warning_message").length - $(".grn_warning_message.MSLWarning").length) > 0) {
        quantityResult = false;
        if($("#is_super_user").val().toLowerCase() == 'true') {
            if($(".grn_blocker_warning_message").length > 0) {
                swal({
                    title: "Invalid Accepted Quantity",
                    text: "<p>At least one of the items Accepted Quantity mentioned in this Invoice is greater than Received Quantity.</p><br />Kindly review & correct the quantities appropriately.",
                    type: "error"
                });
            }
            else {
                var warning = "<p>At least one of the Item quantities mentioned in this Invoice does not seem proper.</p><br />Do you want to update the values as such?"
                confirmAction(message=warning, callback=function(isConfirm){
                    if (isConfirm){
                        validateAndSaveReceipt();
                        quantityResult = true;
                    }
                });
            }
        }
        else {
            swal({
                title: "Invalid Quantity",
                text: "<p>At least one of the Item quantities mentioned in this Invoice does not seem proper.</p><br />Kindly review & correct the quantities appropriately.",
                type: "error"
            });
        }
    }
    return quantityResult;
}

function isTableEmptyValidation() {
    var isEmptyValidation = true;
    if($("#mat_received").is(":visible") && $("#mat_returned").is(":visible")) {
        if($("#materialtable tbody.item-for-goods tr, #materialtable tbody.item-for-service tr").length == 0 && $("#dc_materialtable tbody tr").length == 0){
            swal("","Both Material Received and Material Returned table seems to be empty. Please add one or more material to continue.","error")
            isEmptyValidation = false;
        }
    }
    else if($("#mat_received").is(":visible")){
        if($("#materialtable tbody.item-for-goods tr, #materialtable tbody.item-for-service tr").length == 0) {
            swal("","Material Received table seems to be empty. Please add one or more material to continue.","error")
            isEmptyValidation = false;
        }
    }
    else if($("#mat_returned").is(":visible")){
        if($("#dc_materialtable tbody tr").length == 0) {
            swal("","Material Returned table seems to be empty. Please add one or more material to continue.","error")
            isEmptyValidation = false;
        }
    }
    if(isEmptyValidation) {
        if($("#mat_received").is(":visible") && $("#mat_returned").is(":visible")) {
            var isMaterialTableValid = validateTable("materialtable", "dc_qty");
            var isDcMaterialTableValid = validateTable("dc_materialtable", "dc_qty");
            if(!isMaterialTableValid && !isDcMaterialTableValid) {
                swal("","In Both Material Received and Material Returned table's DC Quantity seems to be empty. Please atleast add one or more material quantity to continue.","error")
                isEmptyValidation = false;
            }
        }
        else if($("#mat_received").is(":visible")){
            var isMaterialTableValid = validateTable("materialtable", "dc_qty");
            if(!isMaterialTableValid) {
                swal("","In Material Received table all DC Quantity seems to be empty. Please atleast add one or more material quantity to continue.","error")
                isEmptyValidation = false;
            }
        }
        else if($("#mat_returned").is(":visible")){
            var isDcMaterialTableValid = validateTable("dc_materialtable", "dc_qty");
            if(!isDcMaterialTableValid) {
                swal("","In Material Returned table all DC Quantity seems to be empty. Please atleast add one or more material quantity to continue.","error")
                isEmptyValidation = false;
            }
        }
    }
    return isEmptyValidation;
}

function validateTable(table, dcName){
    var isTableHasValidQty = false;
    $("#materialtable tbody.item-for-goods tr, #materialtable tbody.item-for-service tr, #dc_materialtable tbody tr").each(function(){
        if($(this).hasClass("consolidated_row")) return true;
        var currentRow = $(this);
        var dcQty = currentRow.find("input[name='"+dcName+"']");
        if(dcQty.val() != 0) {
            isTableHasValidQty = true;
        }
    });
    return isTableHasValidQty;
}

function Calculate() {
    new TotalAmountCalculator().calculate();
}

function calculateAssessValue(quantity, price, discount, assess_rate){
    var value = (parseFloat(quantity) * parseFloat(price));
    if (( 100-parseFloat(discount)) > parseFloat(assess_rate)){
        return (value * (100 - parseFloat(discount))/100);
    }else{
        return (value * parseFloat(assess_rate)/100);
    }
}

function calculateItemTax(quantity, unit_rate, discount, tax_rates, assess_rates, tax_subtotal, is_compound, cascading_item_tax, cgst_amt, sgst_amt, igst_amt){
    var item_tax = 0;
    var gst_amt = 0;
    var tax_count = tax_rates.length;
    // Calculating the net Taxes
    for (i=0; i<tax_count; i++){
        var item_assess_value = calculateAssessValue(quantity, unit_rate, discount, assess_rates[i].value);
        gst_amt = parseFloat(cgst_amt) + parseFloat(sgst_amt) + parseFloat(igst_amt);
        if (is_compound){
            var sub_total = ((parseFloat(item_assess_value)  + parseFloat(gst_amt) + parseFloat(cascading_item_tax)) * parseFloat(tax_rates[i].value))/100;
            cascading_item_tax = parseFloat(cascading_item_tax) + parseFloat(sub_total);
        } else {
            var sub_total = parseFloat(item_assess_value) * parseFloat(tax_rates[i].value)/100;
        }
        tax_subtotal[i].value = (parseFloat(tax_subtotal[i].value) + parseFloat(sub_total)).toFixed(2);
        item_tax = parseFloat(item_tax) + parseFloat(sub_total);
    }
    return parseFloat(item_tax).toFixed(2);
}

var extractNewMaterialJson = function() {
    var makeId = $('#id-makeId').val();
    var mat_type = $('#id-stock').val();
    var [oaId , oaCode] = ["", ""];
    if ($(".OANumbers").text() != "") {
        oaId = $("#id-OANumbers").val();
        oaCode = $("#id-OANumbers").val() == "" ? "-": $("#id-OANumbers option:selected").text();
    }
    var itemName = $('#materialrequired').val().replace(` - ${$('#material_id_hidden').val()}`, "");
    if ($(".unitChoices-container").hasClass("hide")) {
        unit_id = "";
        unit_name = $('#unit_display').text();
    } else {
        unit_id = $("#unitChoices").val();
        unit_name = $("#unitChoices option:selected").text().substr(0, $("#unitChoices option:selected").text().indexOf(" ("));
    }
    var alternate_unit_id = 0
    var scale_factor = 1
    if ($('#id_material-alternate_units').val() != 0 && $('#id_material-alternate_units').val() != null ) {
        unit_name = $('#id_material-alternate_units option:selected').text()
        alternate_unit_id = $('#id_material-alternate_units').val()
        scale_factor = $('#id_material-alternate_units option:selected').attr('data-val')
        unit = unit_name
    }else{
        unit = $('#unit_display').text();
    }
    return {
        oa_code: oaCode,
        oa_id: oaId,
        po_id: "", dc_id: "", rec_grn_id: "",

        item_id: $('#material_id').val(),
        drawing_no: "",
        item_name: `${$('#materialrequired').val()}${$("#chkfaulty").is(':checked')? " [Faulty]": ""}`,
        make_id: makeId == "" ? 1 : makeId,
        is_faulty: $("#chkfaulty").is(':checked'),
        mat_type: mat_type,
        alternate_unit_id: alternate_unit_id,
        scale_factor: scale_factor,
        inspection_log: '[]',

        hsn_code: $("#hsn_code").val(),
        unit_id: unit_id,
        unit_name:  unit_name,
        price: Number($('#materialprice1').val()),
        discount: 0.00,

        pending_qty: 0.00,
        dc_qty: Number($("#qty").val()),
        rec_qty: Number($("#qty").val()),
        shortage_qty: 0.00,
        acc_qty: Number($("#qty").val()),
        rej_qty: 0.00,
        // BOM related
        unit: unit,
        oa_pending_qty: Number($("#qty").val()),
        make_name: "",
        name: itemName,
        is_service: $("#material_is_service").val()
    }
}

var loadBomMaterials = function() {
    if ($("#qty").val() == "" || $("#qty").val() <= 0) {
        $("#qty").focus();
        return;
    }
    material = extractNewMaterialJson();
    material.hasChildren = true;
    var materialSelector = new MaterialSelector();
    materialSelector.insertMaterialSelectorPopup(title="BoM Materials");
    materialSelector.showOaMaterials([material], addOaMaterialsToTable);
}

function addNewMaterialToTable() {
    if ($('#material_id_hidden').val() != "") {
    	if ($('#materialrequired').val() != "" && $("#qty").val() != "" ) {
            var item = extractNewMaterialJson();
            var receivedAgainst = $("#recagainst").val();
            result = new MaterialTable(receivedAgainst=receivedAgainst).createRow(item=item);
            if (result.object == null) {
                swal("", result.message, "warning");
            } else {
                $("#qty").val("");
                $("#hsn_code").val("");
                $("#price").val("");
                $("#price1").val("");
                $("#unit_display").text(" ");
                $("#materialrequired").val("");
                $("#material_id_hidden").val("");
                $("#material_id").val("");
                $("#material_is_service").val("");
                $("#id-makeId").val("1");
                $("#materialprice1").val("");
                $("#chkfaulty").attr("checked", false);
                $("#materialrequired").focus();
                $(".unitChoices-container").addClass("hide");
                $("#unitChoices").val("1");
                if (receivedAgainst == "Job In" && item.oa_id != "") {
                    $("#pos").multiselect("select", item.oa_id, true);
                }
                $("#materialrequired").removeAttr("readonly");
                $(".material-removal-icon").addClass("hide")
            }
    	}
    }
    else {
        if ($('#material_id_hidden').val() == "" && $('#materialrequired').val() != ""){
            var materialName = $("#materialrequired").val();
            var materialUnit = $('#id_material-all_units').val();
            addNewMaterial(materialName, materialUnit);
        }
    }
}

function deleteRow(currentRow) {
	try {
		if (window.confirm('Do you want delete this row?')) {
			var table = document.getElementById("materialtable");
			var rowCount = table.rows.length;
			for (var i = 0; i < rowCount; i++) {
				var row = table.rows[i];
				if (row==currentRow.parentNode.parentNode) {
				if (rowCount <= 1) {
					alert("Cannot delete all the rows.");
					break;
				}
				table.deleteRow(i);
				rowCount--;
				i--;
				}
			}
		}else{
			e.preventDefault();
		}
	} catch (e) {
		//alert(e);
	}
	enableEditButton();
}

function deleteRowReturnMaterial(currentRow) {
	try {
		if (window.confirm('Do you want delete this row?')) {
			var table = document.getElementById("dc_materialtable");
			var rowCount = table.rows.length;
			for (var i = 2; i < rowCount; i++) {
				var row = table.rows[i];
				if (row==currentRow.parentNode.parentNode) {
				if (rowCount <= 1) {
					alert("Cannot delete all the rows.");
					break;
				}
				table.deleteRow(i);
				rowCount--;
				i--;
				}
			}
		}else{
			e.preventDefault();
		}
	} catch (e) {
		//alert(e);
	}
}

function resetHiddenFieldForNewLoad() {
    $("#po_details, #party_details, #issueno_details").val("");
    $("#goods_already_received").prop("checked", false);
    $("#pos, #party_DcNo").find("option").remove();
    $('#pos, #party_DcNo').multiselect('destroy').multiselect();
}

function editReceipt(receipt_no) {
    $("#id-edit_receipt_no").val(receipt_no);
    $("#id-edit_receipt_form").submit();
}

/************************************** Loads Receipt for the param ***************************************************/
var loadReceipt = function(grn_id) {
    isGoodsReceivedCheckChanged = false;
    $("#loading").show();
	$('#cmdSavegrn, #cmdUpdateGRN').addClass("hide");
	$(".btn-save-update").addClass("hide");
    resetHiddenFieldForNewLoad();
    $.ajax({
        url: "/erp/stores/json/grn/loadgrnheader/",
        type: "post",
        datatype:"json",
        data: {receipt_no: grn_id},
        success: function(response) {
            console.log('response', response)
            if(response['response_message'] == "Session Timeout") {
                location.reload();
                return;
            }
            resetMaterialTables();
            var title_receipt = "";
            var grn = response[0];
            var receivedAgainst = grn.received_against;
            $("#id-select_po_tag").val(false)
            $('#mmsnorow').show();
            $('#view_receipt_document').show();
            $("#cmdUpdateGRN").removeClass("hide");
            if (grn.receipt_no == 0) {
                $("#orderno").html(grn.grn_no);
                $("#grnnolbl").html("");
                title_receipt = grn.grn_no;
            } else {
                title_receipt = `${grn.financial_year}/${moduleName}/${grn.receipt_no}${grn.sub_number}`;
                $("#grnnolbl").html(title_receipt);
            }
            $('#template_title').text(title_receipt);
            $("#txtremarks").val("");
            $("#id_receipt_no").val(grn_id);
            $('#project').val(grn.project_code).trigger("chosen:updated");
            $('#grn-location').val(grn.location_id).trigger("chosen:updated");
            updateProjectChosen(grn.project_code, "project");
            $("#recagainst").val(receivedAgainst).prop('disabled', true);
            $("#supplier").val(grn.part_id+"[::]"+grn.config_flags).prop('disabled', true).trigger("chosen:updated");
            $("#recthrough").val(grn.material_received_through);
            $("#purchase_account").val(grn.purchase_account_id);
            $("#invno").val(grn.invoice_no);
            $("#invdate").val(grn.invoice_date);
            $("#id_currency").val(grn.inv_currency_id);
            $("#currency_conversion_rate").val(grn.cur_conversion_rate);
            $("#inwardno").val(grn.inward_no);
            $("#inwdate").val(grn.inward_date);
            $("#old_inwdate").val(grn.inward_date);
            $("#grn_status").val(grn.status);
            $('#inspector').val(grn.inspector.toLowerCase()).trigger("chosen:updated");
            $("#txtrejremarks").val(grn.rejected_remarks);
            $("#txt_duty_passed").val(grn.duty_passed.toFixed(2));
            $("#txtround_off").val(grn.round_off.toFixed(2));
            $("#txtothers").val(grn.other_charges.toFixed(2) );
            $("#dc_invoice").val(grn.invoice_type);
            $("#id_gstin").val(grn.ecommerce_gstin);
            $("#id_note_id").val(grn.note_id);
            CustomDatePickerInit();
            toggleConversionRate();
//            checkGoodsAlreadyReceivedtChosen(grn.proj(receivedAgainst));
            displayRemarksHistory("remarks_list", grn.remarks, "remarks_count");
            if (["Purchase Order", "Job Work", "Delivery Challan", "Sales Return"].indexOf(receivedAgainst) != -1 && grn.status > -1) {
                if((receivedAgainst != "Delivery Challan" && $("#dc_invoice").val() == 2) || (receivedAgainst == "Delivery Challan") ){
                    $("#id_grn_note_preview_div").removeClass("hide");
                }else {
                    $("#id_grn_note_preview_div").addClass("hide");
                }
            }
            if(grn.goods_already_received == 1) {
                $("#goods_already_received").prop("checked", true);
                $("#party_div").removeClass("hide");
                $("#po_div").addClass("hide");
            }

            $("#packing_charges").val(grn.packing_charges.toFixed(2));
            if(parseFloat(grn.packing_charges) > 0){
                $("#chk_packing").attr("checked", true);
                $("#packing_charges").removeAttr("disabled");
            }
            $("#transport_charges").val(grn.transport_charges.toFixed(2));
            if(parseFloat(grn.transport_charges) > 0){
                $("#chk_transport").attr("checked", true);
                $("#transport_charges").removeAttr("disabled");
            }

            if (receivedAgainst == "Sales Return") {
                $("#po_name_display").text("Invoice No");
            }

            if (receivedAgainst == "Others") {
                $('#po_div').hide();
            } else {
                if (receivedAgainst == "Job In") {
                    $("#po_name_display").text("Job OA No");
                }
                $('#po_div').show();
                $('#statusrow').show();
            }

            if (grn.status == 0) {
                $('#statusrow').hide();
                $("#cmdUpdateGRN").removeClass("hide");
            } else {
                if(!$("#is_super_user").val().toLowerCase() == 'true') {
                   $("#cmdUpdateGRN").addClass("hide");
                }
            }
            $('#chkApproved, #chkChecked, #chkVerified, #chkAccounted').removeClass("selected");

            if(grn.status > 0 && $("#is_super_user").val().toLowerCase() == 'true') {
                $(".btn-special-update").show();
            }

            if (grn.status == 1 || grn.status == 5) {
                $('#chkApproved').addClass("selected");
            }
            else if (grn.status == 2) {
                $('#chkApproved, #chkChecked').addClass("selected");
            }
            else if (grn.status == 3) {
                $('#chkApproved, #chkChecked, #chkVerified').addClass("selected");
            } else if(grn.status == 4){
                $(".btn-save-update").removeClass("hide");
            }
            if (grn.acc_status > 0) {
                $('#chkAccounted').addClass("selected");
            }
            else {
                $('#chkAccounted').removeClass("selected");
            }
            $("#view_receipt_document").attr("onclick", `generatePdfFromAjax("${grn_id}", "${grn.status}", ${getSelectedSupplierId()}, $("#recagainst").val())`);

            $("#switch_dc_no a").addClass("noActive").removeClass("active");
            if($("#dc_invoice").val() == 2) {
                $("#switch_dc_no a[data-original-title='Invoice']").removeClass("noActive").addClass("active");
                if($("#goods_already_received").is(":checked")) {
                    $("#switch_dc_no").addClass("disabled")
                    $(".grn_chk_class").removeClass("hide");
                }
                $(".party_dc_text").html('Party Invoice No<span class="mandatory_mark"> *</span>');
                $("#invno").attr("placeholder", "Enter Invoice No");
                $(".party_date_text").text("Party Invoice Date");
            } else {
                $("#switch_dc_no a[data-original-title='DC']").removeClass("noActive").addClass("active");
                $("#switch_dc_no").removeClass("disabled");
                $(".grn_chk_class").addClass("hide");
                $(".party_dc_text").html('Party DC No<span class="mandatory_mark"> *</span>');
                $("#invno").attr("placeholder", "Enter DC No");
                $(".party_date_text").text("Party DC Date");
            }
            if (receivedAgainst == "Job In") {
                $("#switch_dc_no").addClass("disabled");
            }

            if(receivedAgainst == "Issues") {
                $("#dc_issue_title").text("Issue No");
                $('#rec_from_div, #po_div, #div_dc_invoice, #div_indtype, .invoicerow').addClass("hide");
                $('#div_issued_to, #div_issueno').show();
                loadSelectedIssuedTo(grn_id);
            } else {
                $("#dc_issue_title").text("DC No");
                loadPo(grn_id);
            }

            if(receivedAgainst == "Others") {
                $("#switch_dc_no a[data-original-title='Invoice']").removeClass("noActive").addClass("active");
                $("#party_div, #div_dc_invoice").addClass("hide");
                $("#div_currency_details").addClass("hide");
            }
            if(receivedAgainst  == "Delivery Challan" || receivedAgainst  == "Issues"){
                $('#chkAccounted, #chkAccounted_arrow').addClass("hide");
            }
            if (["Purchase Order", "Job Work", "Job In", "Delivery Challan", "Sales Return"].indexOf(receivedAgainst) != -1) {
                try {
                    $("#inv_det").removeClass("hide");
                    $("#div_currency_details").removeClass("hide");
                    if (grn.document_uri != "" && grn.document_uri != null) {
                        var url = `/erp/commons/json/document/?document_uri=${grn.document_uri}`;
                        var ext = grn.document_ext.toLowerCase();
                        $("#id-display_document").attr("data-url", url).attr("data-extension", ext).addClass(ext);
                    }
                } catch(e) {console.log(e);}
            } else {
                loadMaterial(receivedAgainst, "onload");
                $("#inv_det").addClass("hide");
                $("#div_currency_details").addClass("hide");
            }

            $("#mat_returned, #mat_received").hide();
            if(receivedAgainst == "Issues") {
                $("#mat_returned, #mat_received").show();
            } else if(receivedAgainst == "Job Work"){
                if($("#goods_already_received").is(":checked") == false) {
                    $("#mat_returned").show();
                }
                $("#mat_received").show();
            } else if(receivedAgainst == "Delivery Challan") {
                $("#mat_returned").show();
            } else {
                $("#mat_received").show();
            }
            if (["Purchase Order", "Job Work", "Sales Return"].indexOf(receivedAgainst) != -1 && grn.invoice_type == 1 && grn.status > 0) {
                $("#id-invoicedItems").removeClass("hide");
            } else {
                $("#id-invoicedItems").addClass("hide");
            }
            $("#id-materialUsage").addClass("hide");

            $("#stock_materials").find("ul").find("li:visible:first").find("a").trigger("click");
            loadTags("/erp/stores/json/grn/load_grn_tags/", {receipt_no: grn_id});
            updatePageTitle();
            grnSuperEditInit();
            grnPrevNextPaging();
        },
    });
}

function loadGrnTaxes(grn_id) {
    $.ajax({
        url: "/erp/stores/json/grn/loadgrntaxes/",
        type: "post",
        datatype: "json",
        data: {receipt_no: grn_id},
        success: function (response) {
            $.each(response, function (i, po_tax_detail_as_row) {
                $('#po_taxes_table').append(po_tax_detail_as_row);
            });
            $("#po_taxes_table").find('.hnd_text_id').each(function(){
                var selectedTax = $(this).val();
                $("#id_po_tax option[value='"+selectedTax+"']").hide();
                $('.chosen-select').trigger('chosen:updated');
            });
            Calculate();
        }
    });
}

function loadGrnMaterialsAgainstDc(receiptId, receivedAgainst) {
    $("#loading").show();
    if (receivedAgainst == "Delivery Challan") {
        resetMaterialTables();
        constructPoReceiptMaterialTHeader(receivedAgainst);
        $("#materialrow").addClass("hide");
    } else {
        constructOtherReceiptMaterialTHeader();
        $("#materialrow").removeClass("hide");
    }

    $.ajax({
        url: "/erp/stores/json/grn/loadgrnmaterialagainstdc/",
        type: "post",
        datatype:"json",
        data: {grn_no: receiptId},
        success: function(response) {
            var cRowcount = 1;
            constructPoReceiptMaterialTHeader(receivedAgainst);
            var dcMaterialTable = new MaterialTable(
                    receivedAgainst=receivedAgainst, tableId="dc_materialtable");
            $.each(response[0], function(i, item) {
                    item.item_name = ""
                    [item.drawing_no, item.item_name] = formatMaterialDescription(item);
                    item.item_name = item.dc_item_display_name
                    // TODO why we need to check dc_id>0 if this function works against only dc
                    if (item.dc_id > 0) {
                        item.pending_qty = item.dc_mat_pending_qty == undefined? Number(item.qty): Number(item.dc_mat_pending_qty);
                        dcMaterialTable.createRow(item=item);
                    }
                });
            $('#loading').hide();
            $('.invoicerow').show();
            $('#taxrow').show();
            $('#rec_from_div').show();
            $("#pos").prop('disabled', false);
            loadGrnTaxes(receiptId);
            if (receivedAgainst == "Delivery Challan") {
                loadDcMaterials(
                    findSelectedValuesFromMultiSelect("pos").join(","), receivedAgainst, getSelectedSupplierId(), receiptId);
            } else if (receivedAgainst == "Issues") {
                loadIssueMaterials(findSelectedValuesFromMultiSelect("issueno").join(","));
            }
            Calculate();
            $("#loading").hide();
        }
    });
}

/**
 * formats key value for consolidated row
 */
function formatMaterialConsolidatedBy(item_id, makeId, isFaulty) {
    var consolidated_item_details = item_id;
    if (makeId != 1) {
        consolidated_item_details = `${consolidated_item_details}-${makeId}`;
    }
    consolidated_item_details = `${consolidated_item_details}-${isFaulty}`;
    var consolidated_by = consolidated_item_details.trim().replace(/ /g, '');
    return consolidated_by.replace(/\./g, "");
}

var constructMaterialTableHeader = function (receivedAgainst="Purchase Order") {
    if(["Purchase Order", "Job Work", "Sales Return", "Job In"].indexOf(receivedAgainst) != -1) {
        constructPoReceiptMaterialTHeader(receivedAgainst);
    } else {
        // Others and Issues
        constructOtherReceiptMaterialTHeader();
    }
}

var loadGrnMaterials = function (receiptId, receivedAgainst) {
    if(receiptId == "") {
        return;
    }
    constructMaterialTableHeader(receivedAgainst=receivedAgainst);
    if(receivedAgainst == "Delivery Challan") {
        loadGrnMaterialsAgainstDc(receiptId, receivedAgainst);
    } else {
        var supplierId = getSelectedSupplierId();
        var isGoodsAlreadyReceived = $("#goods_already_received").is(":checked");
        $("#loading").show();
        $.ajax({
            url: "/erp/stores/json/grn/loadgrnmaterial/",
            type: "post",
            datatype:"json",
            data: {
                grn_id: receiptId, received_against: receivedAgainst,
                selected_supplier: supplierId,
                goods_already_received: isGoodsAlreadyReceived},
            success: function(response) {
                if(["Purchase Order", "Job Work", "Sales Return", "Job In"].indexOf(receivedAgainst) != -1) {
                    var materialTable = new MaterialTable(
                    receivedAgainst=receivedAgainst, tableId="materialtable", isGoodsAlreadyReceived=isGoodsAlreadyReceived);
                    var dcMaterialTable = new MaterialTable(
                    receivedAgainst=receivedAgainst, tableId="dc_materialtable", isGoodsAlreadyReceived=isGoodsAlreadyReceived);
                    if (receivedAgainst == "Job In") {
                        $("#id-materialUsage").addClass("hide");
                    }
                    $.each(response, function(i, item) {
                        var drawingNumber = "";
                        var itemDescription = "";
                        [drawingNumber, itemDescription] = formatMaterialDescription(item);
                        item.drawing_no = drawingNumber;
                        item.item_name = itemDescription;
                        item.saved_acc_qty = item.acc_qty;
                        // Below are elements of GoodsAlreadyReceived
                        item.pending_qty = item.pending_dc_qty == undefined ? item.pending_qty: item.pending_dc_qty
                        if (isGoodsAlreadyReceived) {
                            if (item.purchase_account_id > 0){
                                $('#purchase_account').val(item.purchase_account_id);
                                $('#purchase_account').trigger("chosen:updated");
                            }
                            item.pending_dc_qty = item.dc_qty + item.pending_dc_qty;
                        } else {
                            item.pending_qty = item.pending_qty == null ? 0.00 : item.acc_qty + item.pending_qty;
                        }
                        if (item.dc_id > 0 && (receivedAgainst == "Job Work" || receivedAgainst == "Sales Return")){
                            item.pending_qty =  item.issue_qty - item.ret_qty + item.acc_qty
                            receivedAgainst == "Job Work" ? dcMaterialTable.createRow(item=item) : materialTable.createRow(item=item);
                        } else {
                            materialTable.createRow(item=item);
                        }
                        if (receivedAgainst == "Job In" && item.oa_id == "" && $("#chkApproved").hasClass("selected")) {
                            $("#id-materialUsage").removeClass("hide");
                        }
                    });// css for update the table row color and border line

                    if(receivedAgainst == "Job In" || $('#is_purchase_order_mandatory').val() == 'False' && receivedAgainst != "Sales Return"){
                        $("#materialrow").removeClass("hide");
                    } else {
                        $("#materialrow").addClass("hide");
                    }
                    $('.invoicerow').show();
                    $('#taxrow').show();
                    $('#rec_from_div').show();
                    $("#pos").prop('disabled', false);
                    loadGrnTaxes(receiptId);
                    if (isGoodsAlreadyReceived) {
                        var materialMan = new MaterialManager();
                        $.each($("#party_details").val().split(","), function(i, item){
                            materialMan.addGrnMaterials(item);
                        });
                    } else {
                        if(["Purchase Order", "Job Work"].indexOf(receivedAgainst) != -1) {
                            if(findSelectedValuesFromMultiSelect("pos").join(",") != ""){
                                loadPoMaterials(
                                receivedAgainst=receivedAgainst, receipt_no=receiptId,
                                supplierId=supplierId, poIds=findSelectedValuesFromMultiSelect("pos").join(","));

                            }
                        } else if(receivedAgainst == "Sales Return") {
                            loadDcMaterials(
                                findSelectedValuesFromMultiSelect("pos").join(","), receivedAgainst, supplierId, receiptId);
                        }
                    }
                } else {
                    // Others and Issues
                    resetMaterialTables();
                    var count = 0
                    var pp_no = 0
                    var issue_name = ''
                    var pp_name = ''
                    $.each(response, function(i, item) {
                        if(item.dc_id == 0) {
                            var [drawing_no, item_name] = formatMaterialDescription(item);
                            if (count == 0 && item.po_id){
                                pp_no = item.po_id
                                issue_name = item.issue_to
                                pp_name = item.pp_no
                            }
                            item.drawing_no = drawing_no;
                            item.item_name = item_name;
                            item.shall_construct_item_name = true;
                            result = new MaterialTable(receivedAgainst=receivedAgainst).createRow(item);
                            if(result.object == null) {
                                console.log("Could not load GRN materials");
                            }
                        }
                    }); // css for update the table row color and border line
                    if (pp_no >0){
                        var currentPpId = pp_no;
                        var isPpAvailable = $(`#id_wo option[value='${pp_no}']`).length;
                        if(isPpAvailable == 0) {
                            var option = `<option value="${pp_no}">${pp_name}</option>`;
                            $("#id_wo").find("optgroup[label='All']").append(option);
                        }
                        $("#id_invoice-issued_to").val(issue_name).prop('disabled', true).trigger("chosen:updated");
                        $("#id_wo").val(pp_no).trigger("chosen:updated");
                        if ($("#id_wo").val() != "" && $("#id_wo").val() != 0){
                            $("select#issueyear").next(".btn-group").find("ul").find("li input[type='checkbox']").prop("checked", false);
                            $("select#issueno").next(".btn-group").find("ul").find("li input[type='checkbox']").prop("checked", false);
                            $("select#issueyear").val("");
                            $("select#issueno").val("");
                            $('#div_issueno').addClass("hide");
                            $("#div_pp_no").removeClass("hide");
                        }
                    }
                    $("#materialrow").removeClass("hide");
                    $('.invoicerow').hide();
                    $('#taxrow').hide();
                    $('#inv_det').hide();
                    $("#pos").prop('disabled', true);
                    $("#div_currency_details").addClass("hide");
                    if(receivedAgainst == "Issues") {
                        $('#rec_from_div').hide();
                        loadGrnMaterialsAgainstDc(receiptId, receivedAgainst);
                    } else {
                        $('#rec_from_div').show();
                    }
                }
                $("#loading").hide();
                Calculate();
            }
        });
    }
}

function consolidatedRowToggle(self){
    var consolidated_row = $(self).closest("tr").attr("consolidated-for");
    var table_name = $(self).closest("table").attr("id");
    var rowspanCount = "1";
    if(table_name == "materialtable") {
        rowspanCount = $(self).closest("tr").find(".consolidated_price_column").attr("data-rowspan");
    }
    if($(self).find("i").hasClass("fa-chevron-right")){
        $("#"+table_name).find("."+consolidated_row).removeClass("hide");
        $(self).closest("tr").find("i.consolidated_fa").addClass("fa-chevron-down").removeClass("fa-chevron-right");
        $(self).closest("tr").find(".consolidated_price_column").attr("rowspan", rowspanCount);
    }
    else {
        $("#"+table_name).find("."+consolidated_row).addClass("hide");
        $(self).closest("tr").find("i.consolidated_fa").addClass("fa-chevron-right").removeClass("fa-chevron-down");
        $(self).closest("tr").find(".consolidated_price_column").removeAttr("rowspan");
    }
}

function addPoTax(tax_code){
// TODO Tax Row generation: Reorganise JS
    $.ajax({
        url: "/erp/purchase/json/po/add_tax/",
        type: "POST",
        dataType: "json",
        data: {code: tax_code},
        success:function (response) {
            $.each(response, function(i, tax_detail_as_row){
                $('#po_taxes_table').append(tax_detail_as_row);
            });
            Calculate();    // Calculate the Total Price after applying new Tax profile
            enableEditButton();
        }
    });//attr
    $("#id_po_tax option:selected").hide(); // Remove the added Tax from the Tax-list to avoid multiple addition
    $("#id_po_tax").val(0);    // Reset the Tax Select input
	$('.chosen-select').trigger('chosen:updated');
}

function removePoTax(tax_code){
	tax_rows = document.getElementsByName('tr_' + tax_code);
	for(i=tax_rows.length - 1; i>=0; i--){
        // Removed in reverse order as the removal renumbers the RowIndices immediately
        document.getElementById("po_taxes_table").deleteRow(tax_rows[i].rowIndex);
	}
	$("#id_po_tax option[value='"+tax_code+"']").show();    // Re-show the tax in the Tax list
	$('.chosen-select').trigger('chosen:updated');
	Calculate();    // Calculate the PO price after Removing the Tax
	enableEditButton();
}

function loadTaxDetails(){
// TODO reuse method po.js -> loadTaxDetails()
	$.ajax({
		url: "/erp/purchase/json/po/loadtaxdetails/",
		type: "post",
		datatype:"json",
		data: "",
		success: function(response){
			$('#id_po_tax').val("");
		    var items =  "";
		    items = "<option value='0'> -NA- </option>";
		    for (i = 0; i < response.length ; i++) {
	            items += "<option value='"+response[i][0]+"'> "+response[i][1]+" </option>";
		    }
		    $("#id_po_tax").html(items);
		    $("#id_po_tax").trigger('chosen:updated');
		}
	});
}

function loadMaterial(receivedAgainst, loadType = "") {
    var dataToSend = {
        module: "grn",
        received_against: receivedAgainst
    }
    var local_material_list  = JSON.parse(localStorage.getItem('goods_service_master_data'));
    if(loadType == "onchange" && local_material_list) {
        var material_choices = local_material_list[0].material;
    }
    else {
        var material_choices = generateMaterialAsAutoComplete(dataToSend);
    }
    var frequently_material_choices = generateFrequentlyMaterialAsAutoComplete(dataToSend);

    $("#materialrequired").materialAutocomplete({
        source: frequently_material_choices.concat(material_choices),
        minLength:1,
        response: function( event, ui ) {
            ui.content.unshift({value:"add_new_item", label:"+ ADD NEW MATERIAL ", category: ""});
        },
        open: function(event, ui) {
            $("ul.ui-autocomplete.ui-menu a:contains('+ ADD NEW MATERIAL')").addClass("add_new_material_link");
        },
        select: function (event, ui) {
            event.preventDefault();
            if(ui.item.value != "add_new_item") {
                $("#price1").val(ui.item.price);
                $("#materialprice1").val(ui.item.price);
                $.ajax({
                url: "/erp/masters/json/materials/get_supplier_price/",
                type: "POST",
                dataType: "json",
                data:{supplier_id:getSelectedSupplierId(), item_id:ui.item.id, make_id: ui.item.mid},
                success: function (response) {
                    if(response.length > 0 ){
                        $("#price1").val(response[0][0]);
                        $("#materialprice1").val(response[0][0]);
                    }
                },
                error: function (xhr, errmsg, err) {
                    console.log(xhr.status + ": " + xhr.responseText);
                }
                });
                var itemName = ui.item.label.replace("<span class='service-item-flag'></span>", '');
                $("#material_id_hidden").val(ui.item.id);
                $("#material_id").val(ui.item.id);
                $("#material_is_service").val(ui.item.is_service);
                $("#materialrequired").val(itemName).attr("readonly", true);
                $("#id-makeId").val(ui.item.mid);
                $("#id-stock").val(ui.item.stock);
                $("#unit_display").text(ui.item.unit);
                $("#hsn_code").val(ui.item.hsn);
                $("#unit_display").removeClass("hide");
                $(".unitChoices-container").addClass("hide");
                if($('#is_multiple_units').val()=="True" ){
                    if (ui.item.alt_uom > 0){
                       loadAlternateUnits(ui.item.id, ui.item.unit, '#id_material-alternate_units');
                        $(".alternate_unit_select_box").removeClass("hide");
                    }
                    else {
                        $(".alternate_unit_select_box").addClass("hide");
                    }
                }
                if ($("#recagainst").val() == "Job In") {
                    if (parseInt(ui.item.bom) > 0 ) {
                        $("#explode_bom").removeClass("hide");
                    } else {
                        $("#explode_bom").addClass("hide")
                    }
                }
                $(".material-removal-icon").removeClass("hide");
            } else {
                openMaterialAddModal();
            }
        }
    }).on('focus', function() { $(this).keydown(); });
}

$(function () {
    // TODO ready function organize code
    var tags = []
    $.ajax({
        url: "/erp/purchase/json/po/loadtags/",
        type: "post",
        datatype:"json",
        data: "",
        success: function(response){
            for (i = 0; i < response.length ; i++) {
                tags.push({value:response[i][0],label:response[i][1]});
            }
        }
    });
    $("#id_grn_tag").autocomplete({
        source: function(request, response) {
            var results = $.ui.autocomplete.filter(tags, request.term);
            response(results.slice(0, 30));
        },
        focus: function (event, ui) {
            event.preventDefault();
        },
        minLength: 0,
        select: function (event, ui) {
            event.preventDefault();
            $("#id_grn_tag").val(ui.item.label);
            $("#grn_tag_value").val(ui.item.value);
            $("#add_grn_tag").click();
        }
    }).focus(function () {
        var that = $(this)
        $("#id_grn_tag").addClass('ui-autocomplete-loading');
        setTimeout(function(){
            that.autocomplete("search");
        },300);
    });
    $("#id_grn_tag").keydown(function(event,ui){
        if(event.keyCode == 13) {
          if($(this).val() != "") {
            $("#add_grn_tag").click();
            setTimeout(function(){
                $(this).focus();
            },10);
          }
          event.preventDefault();
        }
    });
});

/**
 * Modify the table header according to the received against column
 */
function constructPoReceiptMaterialTHeader(receivedAgainst) {
    document.getElementById("materialtable").deleteTHead();
    var table = document.getElementById("materialtable");
    table.setAttribute('class','table table-bordered custom-table custom-table-large table-striped table-fixed tableWithText quality-inpection-table');
    var header = table.createTHead();
    header.setAttribute('class','th-vertical-center');

    var row = header.insertRow(0);
    var header_cell = document.createElement("TH");
    header_cell.setAttribute("rowspan", "2");
    if (receivedAgainst == "Sales Return") {
        header_cell.innerHTML = "Invoice No";
    } else if (receivedAgainst == "Job In") {
        header_cell.innerHTML = "OA No";
    } else {
        header_cell.innerHTML = "PO No";
    }
    header_cell.setAttribute("class","material-header");
    header_cell.style.width="8%";
    row.appendChild(header_cell);
    var header_cell = document.createElement("TH");
    header_cell.setAttribute("rowspan","2");
    header_cell.innerHTML = "Material";
    if($("#goods_already_received").is(":checked")){
        header_cell.setAttribute("class","td-material hide");
    }else{
        header_cell.setAttribute("class","td-material");
    }
    row.appendChild(header_cell);
    var header_cell = document.createElement("TH");
    header_cell.setAttribute("class","grn_quantity_class");
    if($("#goods_already_received").is(":checked") || receivedAgainst == "Job In"){
        header_cell.setAttribute("colspan","3");
    }else{
        header_cell.setAttribute("colspan","3");
    }
    header_cell.innerHTML = "Quantity";
    header_cell.style.width="21%";
    row.appendChild(header_cell);
//    var header_cell = document.createElement("TH");
//    header_cell.setAttribute("rowspan","2");
//    header_cell.innerHTML = "Unit";
//    header_cell.style.width="6%";
//    row.appendChild(header_cell);
    var header_cell = document.createElement("TH");
    header_cell.setAttribute("class","tour_hsn");
    header_cell.setAttribute("rowspan","2");
    header_cell.innerHTML = "HSN/ SAC";
    header_cell.style.width="6%";
    row.appendChild(header_cell);
    var header_cell = document.createElement("TH");
    header_cell.setAttribute("name","price_col");
    header_cell.setAttribute("rowspan","2");
    header_cell.innerHTML = "Price/ Unit";
    header_cell.style.width="6%";
    row.appendChild(header_cell);
    var header_cell = document.createElement("TH");
    header_cell.setAttribute("name","price_col");
    header_cell.setAttribute("rowspan","2");
    header_cell.innerHTML = "Disc. (%)";
    header_cell.style.width="6%";
    row.appendChild(header_cell);

    var header_cell = document.createElement("TH");
    header_cell.setAttribute("rowspan","2");
    header_cell.innerHTML = "Total Price";
    header_cell.style.width="8%";
    row.appendChild(header_cell);
    if (receivedAgainst != "Job In") {
        var header_cell = document.createElement("TH");
        header_cell.setAttribute("rowspan","2");
        header_cell.innerHTML = "CGST (%) <span class='th-sub-heading bracket-enclosed'>Amount</span>";
        header_cell.style.width="6%";
        row.appendChild(header_cell);

        var header_cell = document.createElement("TH");
        header_cell.setAttribute("rowspan","2");
        header_cell.innerHTML = "SGST  (%) <span class='th-sub-heading bracket-enclosed'>Amount</span>";
        header_cell.style.width="6%";
        row.appendChild(header_cell);

        var header_cell = document.createElement("TH");
        header_cell.setAttribute("rowspan","2");
        header_cell.innerHTML = "IGST  (%) <span class='th-sub-heading bracket-enclosed'>Amount</span>";
        header_cell.style.width="6%";
        row.appendChild(header_cell);
    }
    var row = header.insertRow(1);
    var header_cell = document.createElement("TH");
    if (receivedAgainst == "Job In") {
        header_cell.innerHTML = "<div style='font-size:9px'>DC <span class='th-sub-heading bracket-enclosed'>PO Pending</span></div>";
    }
    else {
        if (receivedAgainst == "Sales Return") {
            header_cell.innerHTML = "<div style='font-size:9px'>DC/Invoice <span class='th-sub-heading bracket-enclosed po-pending' name='po-pending'>Invoice Pending</span></div>";
        }
        else {
             header_cell.innerHTML = "<div style='font-size:9px'>DC/Invoice <span class='th-sub-heading bracket-enclosed po-pending' name='po-pending'>PO Pending</span></div>";
        }
    }
    row.appendChild(header_cell);
    var header_cell = document.createElement("TH");
    header_cell.innerHTML = "<div style='font-size:9px'>Received<span class='th-sub-heading bracket-enclosed'>Shortage</span></div>";
    row.appendChild(header_cell);
    var header_cell = document.createElement("TH");
    header_cell.innerHTML = "<div style='font-size:9px'>Accepted <span class='th-sub-heading bracket-enclosed'>Rejected</span></div>";
    row.appendChild(header_cell);
     inlineAddMaterialToTable();
}

function constructOtherReceiptMaterialTHeader() {
    $("#materialtable").find("thead").html(`<tr>
        <th>Item/Material</th>
        <th >Quantity</th>
        <th>HSN/ SAC</th>
        <th>Delete</th></tr>`);
         inlineAddMaterialToTable();
}

function posSelectChange() {
    $("select#pos").next(".btn-group").find("ul").find("li input[type='checkbox']").change(function(){
        new MaterialManager().onChangePoSelection();
        $("#po_div, #party_div").find(".multiselect").removeClass('error-border');
        $("#po_div .custom-error-message, #party_div .custom-error-message").remove();
    });
}

function partyDCSelectChange() {
    $("select#party_DcNo").next(".btn-group").find("ul").find("li input[type='checkbox']").change(function(){
        new MaterialManager().onChangeDcSelection();
        $("#party_div").find(".multiselect").removeClass('error-border');
        $("#party_div .custom-error-message").remove();
    });
}

function onIssueYearChange() {
    var multi_select_year = $("select#issueyear").next('.btn-group').find('ul').find('li input:checked');
    $("#issueno").html("");
    if(multi_select_year.length <= 0){
        $('#issueno').multiselect('destroy');
        $('#issueno').multiselect();
        $('#div_pp_no').removeClass("hide");
        $(".add_item_details").removeClass('hide');
        resetMaterialTables()
    }
    else{
        if($("#id_wo").val() != "" && $("#id_wo").val() != 0) {
            $('#div_pp_no').removeClass("hide");
        }
        else {
            $('#div_pp_no').addClass("hide");
        }

        $(".add_item_details").addClass('hide');
    }

    multi_select_year.each(function () {
        loadIssueNo($(this).val());
    });
    $("#div_issueno").find(".custom-error-message").remove();
    $("#div_issueno").find(".error-border").removeClass("error-border");
}

function issueSelectChange() {
    $("select#issueno").next(".btn-group").find("ul").find("li input[type='checkbox']").change(function(){
        new MaterialManager().onChangeIssueSelection();
        $(".multiselect").removeClass('error-border');
        $("#div_issueno .custom-error-message").remove();
        $("#div_issueno").find(".custom-error-message").remove();
        $("#div_issueno").find(".error-border").removeClass("error-border");
    });
}

function loadIssueYearChangeEvent() {
    $("select#issueyear").next(".btn-group").find("ul").find("li input[type='checkbox']").change(function(){
        onIssueYearChange();
    });
}

function customJsValidation(){
	$("#add_po_tax").click(function(){
		$(".error-border").removeClass('error-border');
		$(".custom-error-message").remove();

		var ControlCollections = [
			{
				controltype: 'dropdown',
				controlid: 'id_po_tax',
				isrequired: true,
				errormsg: 'Tax Type is required.'
			}];
		var result = JSCustomValidator.JSvalidate(ControlCollections);
		if(result){
            tax_code = $("#id_po_tax option:selected").val();
            addPoTax(tax_code);
		}
		return result;
	});

	$("#cmdadd").click(function() {
		$(".error-border").removeClass('error-border');
		$(".custom-error-message").remove();
        $(".suggestion-container").remove();
		var ControlCollections = [
			{
				controltype: 'textbox',
				controlid: 'materialrequired',
				isrequired: true,
				errormsg: 'Material Name is required.'
			},
			{
				controltype: 'textbox',
				controlid: 'hsn_code',
				isrequired: true,
				errormsg: 'HSN/ SAC is required.',
				ishsn: true,
                hsnerrormsg: 'Invalid HSN Code.'
            },
		    {
				controltype: 'textbox',
				controlid: 'qty',
				isrequired: true,
				errormsg: 'Quantity is required.',
                mindigit: 0.01,
                mindigiterrormsg: 'Quantity cannot be 0.'
			}
		];
		if($('#material_id_hidden').val().trim() != "" && $("#recagainst").val() != "Job In") {
            var control = {
                controltype: 'textbox',
                controlid: 'material_id_hidden',
                isrequired: true,
                errormsg: 'Invalid Material Name.'
            };
		}
		ControlCollections[ControlCollections.length] = control;
		var result = JSCustomValidator.JSvalidate(ControlCollections);
		if(result) {
            addNewMaterialToTable();
            refreshSessionPerNActions(10);
            removeValidationError();
            $("#explode_bom").addClass("hide");
            Calculate();
           enableEditButton();
		}
		return result;
	});
}

var isGoodsReceivedCheckChanged = false;
function changeGoodsAlreadyReceivedEvent() {
    isGoodsReceivedCheckChanged = true;
    resetMaterialTables();
    $("#switch_dc_no a").removeClass("active").addClass("noActive");
    $("#pos").find("option").remove();
    $("#pos").multiselect('destroy').multiselect();
    $("#party_DcNo").find("option").remove();
    $('#party_DcNo').multiselect('destroy').multiselect();
    $("#po_details, #party_details").val("");
    if($("#goods_already_received").is(":checked")) {
         $("#dc_invoice").val("2");
         $("#switch_dc_no a[data-original-title='Invoice']").removeClass("noActive").addClass("active");
         $("#switch_dc_no").addClass("disabled");
         $("#party_div").removeClass("hide");
         if($("#recagainst").val() == "Job Work"){
              $("#mat_returned").hide();
         }
         $(".pending-class").addClass("hide");
         $(".td-material").addClass("hide");
         $(".party_dc_text").html('Party Invoice No<span class="mandatory_mark"> *</span>');
         $("#invno").attr("placeholder", "Enter Invoice No");
         $(".party_date_text").text("Party Invoice Date");
         $("#materialrow").addClass("hide");
         $('#po_div').addClass("hide");
         new MaterialManager().loadGoodsReceivedDcNumbers();
    }
    else {
        $("#dc_invoice").val("");
        $("#switch_dc_no").removeClass("disabled");
        $("#party_div").addClass("hide");
        $('#po_div').removeClass("hide");
        if($("#recagainst").val() == "Job Work") {
            $("#mat_returned").show();
        }
        $(".pending-class").removeClass("hide");
        $(".td-material").removeClass("hide");
        $(".party_dc_text").html('Party DC No<span class="mandatory_mark"> *</span>');
        $("#invno").attr("placeholder", "Enter DC No");
        $(".party_date_text").text("Party DC Date");
        resetMaterialSelectionVisibility($("#recagainst").val());
        loadPo("");
    }
    constructPoReceiptMaterialTHeader($("#recagainst").val())
}

function showInvoicedItems() {
    if ($("#invoicedItems").text() == "") {
        $("body").append(`<div id="invoicedItems" class="modal fade" role="dialog" >
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                        <h4 class="modal-title">Items Invoiced</h4>
                    </div>
                    <div class="modal-body">
                        <table class="table table-bordered custom-table table-striped">
                        <thead> <tr>
                            <th width="5%"> S.No. </th>
                            <th width="18%"> GRN No</th>
                            <th width="10%"> Date</th>
                            <th width="12%"> Party Inv No</th>
                            <th width="10%"> Invoice Date</th>
                            <th width="30%"> Item Details</th>
                            <th width="15%"> Qty</th>
                        </tr></thead>
                        <tbody> </tbody>
                        </table>
                    </div>
                    <div class="modal-footer">
                         <span class="pull-left nonStock-text">  <span class=" nonStock-text non_stock-flag"></span> - NON-STOCKABLE</span>
                         <span class="pull-left nonStock-text">  <span class=" nonStock-text service-item-flag"></span> - SERVICE</span>
                        <a class="btn btn-cancel" data-dismiss="modal">Close</a>
                    </div>
                </div>
            </div></div>`);
    } // this code will avoid loading this modal always from html
    $.ajax({
        url: "/erp/stores/json/grn/invoiced_items/",
        type: "post",
        datatype: "json",
        data: {receipt_no: $("#id_receipt_no").val()},
        success: function(response) {
            $("#invoicedItems").find("tbody").html('');
            $.each(response, function (i, item){
            console.log("response",item)
                var item_name=""
                if (item.make != "") {
                    item_name  = item.item_code + " [" + item.make + "]"
                }else{
                    item_name  = item.item_code
                }
                if (item.is_faulty != 0) {
                    item_name  = item_name + " [Faulty]"
                }

                if(item.is_service == true){
                     item_name += `<span class="service-item-flag"></span>`;
                }

                if (item.is_stock==0 && item.is_service != true) {
                    item_name += `<span class=" service-text non_stock-flag"></span>`;
                }
                var edit_grn = "<a role='button' onclick=\"javascript:openReceipt(" + item.receipt_no + ");\">"+item.code+"</a>";
                row="<tr align='left'>"+
                    "<td width='5%' class='text-center'>"+(i+1)+"</td>"+
                    "<td width='12%'>"+ edit_grn +"</td>"+
                    "<td width='15%' class='text-center'>"+item.inward_date+"</td>"+
                    "<td width='15%'>"+item.invoice_no+"</td>"+
                    "<td width='9%' class='text-center'>"+item.invoice_date+"</td>"+
                    "<td width='15%' class='${isStockable}'>"+ item_name +"</td>"+
                    "<td width='20%' align='right'>" +item.accepted_qty+"</td></tr>";
                $('#invoicedItems tbody').append(row);
            });
            if($("#invoicedItems tbody").find("tr").length <= 0) {
                var row = "<tr><td colspan='8' class='text-center' style='font-weight: bold;'>No Records Found!</td></tr>";
                $('#invoicedItems tbody').append(row);
            }
        }
    });
    $("#invoicedItems").modal('show');
}

var saveOa = function (oaMaterials) {
    if (oaMaterials.length == 0) {
        saveReceipt();
        return;
    }
    $("#loading").show();
    var grandTotal = 0
    $.each(oaMaterials, function(i, item) {
        grandTotal += item.quantity * item.price;
    });
    var params = {
        party_id: getSelectedSupplierId(),
        oa_type: "Job",
        currency_id: $("#id_currency").val(),
        remarks: `Auto generated OA against Job In DC ${$("#invno").val()} ${$("#invdate").val()}`,
        grand_total: grandTotal,
        materials: oaMaterials
    };
    $.ajax({
        url: "/erp/sales/json/order_ack/",
        type: "post",
        contentType: 'application/json; charset=utf-8',
        dataType: 'text',
        data: JSON.stringify(params),
        success: function(response) {
            $("#loading").hide();
            response=JSON.parse(response);
            if (response.response_message == "Success") {
                $("#materialtable").find("tr").each(function() {
                    if ($(this).find(`select[name="select_oa_id"]`).val() == "") {
                        var option = document.createElement("option");
                        option.text = response.code;
                        option.value = response.id;
                        var select = $(this).find(`select[name="select_oa_id"]`);
                        select.append(option)
                        select.val(option.value);
                    }
                });
                saveReceipt(response.code);
            } else {
                swal("", `Could not auto create OA. Please try later. <BR/> Reason: ${response.custom_message}`, "error");
            }
        }, error: function() {
            $("#loading").hide();
        }
    });
}

var showGrnUsageReport = function () {
    showUsageReport(url="/erp/stores/json/grn/usage_report/", params={receipt_no: $("#id_receipt_no").val()});
}

function validateInwardNoForFiscalYear(){
   $.ajax({
        url: "/erp/stores/json/grn/check_gate_inward_no/",
        method: "POST",
        async: false,
        data:{
            inward_date: $("#inwdate").val() ,
            gate_inward_no: $("#inwardno").val(),
            receipt_no : $("#id_receipt_no").val()
        },
        success: function(response) {
            if (response.response_message =="Success") {
                if(response.receipt_no == null || response.receipt_no == ""){
                    checkClosingStockValue();
                } else {
                    swal({title: "", text: "Gate inward no already exist in the financial year. Please try some other gate inward no", type: "warning"});
                }
             } else {
                swal({title: "", text: response.custom_message, type: "error"});
             }
        },
        error: function (xhr, errmsg, err) {
            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
        }
    });
}

function generateInwardNo(){
    $.ajax({
        url: "/erp/stores/json/grn/generate_gate_inward_no/",
        method: "POST",
        async: false,
        data:{
            inward_date: $("#inwdate").val() ,
        },
        success: function(response) {
            if (response.response_message =="Success") {
                if(response.inward_no != "") {
                    var inward_no = response.inward_no;
                    $("#inwardno").val(parseInt(inward_no) + 1)
                } else {
                    $("#inwardno").val(1)
                }
             } else {
                swal({title: "", text: response.custom_message, type: "error"});
             }
        },
        error: function (xhr, errmsg, err) {
            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
        }
    });
}

function grnValidation(){
    var receivedAgainst =  $("#recagainst").val();
    if($("#id_receipt_no").val() != "") {
        if (receivedAgainst == "Job In") {
            checkStockWithJobInReturn();
        } else {
            checkForStockAvailability();
        }
    }
    else {
        validateAndSaveReceipt();
    }
}

function checkClosingStockValue(){
    if($("#grn_status").val() < 0){
        grnValidation();
    }else{
        if ($("#goods_already_received").is(":checked") != true){

            var mat_list = [];
            $("#materialtable tbody tr").each(function(){
                if($(this).find("input[name='item_id']").val()!="" && !$(this).hasClass('consolidated_row')){
                    if ($(this).find("input[name='mat_type']").val()== 1){
                        var material_obj = { "drawing_no":$(this).find("input[name='drawing_no']").val(), "item_id":$(this).find("input[name='item_id']").val(),
                        "make_id":$(this).find("input[name='make_id']").val(), "is_faulty":$(this).find("input[name='is_faulty']").val(),
                        "invoice_id":"", "alternate_unit_id":$(this).find("input[name='alternate_unit_id']").val()};
                        var materialJSON = JSON.stringify(material_obj);
                        mat_list.push(materialJSON);
                    }
                }
            });
            if(mat_list.length > 0 && $("#grn_status").val() > 0){
                $.ajax({
                    url: "/erp/stores/json/issue/check_closing_stock/",
                    method: "POST",
                    async: false,
                    data:{'mat_list[]': mat_list, "new_inward_date": $("#inwdate").val(), "old_inward_date": $("#old_inwdate").val()},
                    success: function(response) {
                         if (response.response_message =="Success") {
                            loop_count = response["closing_stock_1"].length
                            show_warning_message = false
                            for(i=0; i < loop_count; i++){
                                material_1 = response["closing_stock_1"][i]
                                for(j=0; j < loop_count; j++){
                                    material_2 = response["closing_stock_2"][j]
                                    if(material_1["item_id"] == material_2["item_id"] &&
                                        material_1["drawing_no"] == material_2["drawing_no"] &&
                                        material_1["make_id"] == material_2["make_id"] &&
                                        material_1["is_faulty"] == material_2["is_faulty"] &&
                                        material_1["closing_qty"] > material_2["closing_qty"]){
                                        show_warning_message = true;
                                        break;
                                    }
                                }
                            }
                            if(show_warning_message == true){
                                confirmAction(message="Change of Gate inward date leads to stock below msl value.<br/> Do you want to continue?", callback=function(isConfirm) {
                                    if (isConfirm) {
                                        grnValidation();
                                    }
                                });
                            }else{
                                grnValidation();
                            }
                         } else {
                            swal({title: "", text: response.custom_message, type: "error"});
                         }
                    },
                    error: function (xhr, errmsg, err) {
                        swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
                    }
                });
            } else{ grnValidation(); }
        }
        else{
            grnValidation();
        }
     }
}

function load_grn_note_preview(){
    if($("#id_receipt_no").val() != "") {
        $("#grn_note_preview_table tbody tr").remove();
        $.ajax({
            url: "/erp/stores/json/grn/loadgrnnotepreview/",
            method: "POST",
            async: false,
            data:{'grn_id': $("#id_receipt_no").val()},
            success: function(response) {
                if (response[0].length > 0){
                    total_note_value_dr=0;
                    total_note_value_cr=0;
                    total_note_value = 0
                    total_note_value_type = ""
                    $.each(response[0], function (i, item){
                        cgst_tax_amt=0
                        sgst_tax_amt=0
                        igst_tax_amt=0
                        txt_total_amount=0;
                        txt_total_amount = (parseFloat(item.qty) * parseFloat(item.rate)).toFixed(2);
                        cgst_tax_amt = ((txt_total_amount * parseFloat(item.cgst_rate))/100).toFixed(2)
                        sgst_tax_amt = ((txt_total_amount * parseFloat(item.sgst_rate))/100).toFixed(2)
                        igst_tax_amt = ((txt_total_amount * parseFloat(item.igst_rate))/100).toFixed(2)
                        if (item.note_type == "Dr"){
                            total_note_value_dr = parseFloat(total_note_value_dr) + parseFloat(txt_total_amount) + parseFloat(cgst_tax_amt) + parseFloat(sgst_tax_amt) + parseFloat(igst_tax_amt)
                        }else{
                            total_note_value_cr = parseFloat(total_note_value_cr) + parseFloat(txt_total_amount) + parseFloat(cgst_tax_amt) + parseFloat(sgst_tax_amt) + parseFloat(igst_tax_amt)
                        }
                        var row_grn_table = "<tr bgcolor='#ececec' border='0' style='font-size:9px; font-weight:normal;'><td class='text-center'> "+(i+1)+"</td><td align='left'>" +
                                item.description + "</td><td>" +
                                item.reason + "</td><td class='text-right'>" +
                                item.qty + "</td><td class='text-right'>" +
                                item.rate + " /" + item.unit_name +" </td><td class='text-right'>" +
                                parseFloat( item.rate * item.qty).toFixed(2) + " " + item.note_type + "</td><td class='text-right'>" +
                                cgst_tax_amt + " @"+ parseFloat(item.cgst_rate).toFixed(1) +"%</td><td class='text-right'>" +
                                sgst_tax_amt + " @"+ parseFloat(item.sgst_rate).toFixed(1) + "%</td><td class='text-right'>" +
                                igst_tax_amt + " @"+ parseFloat(item.igst_rate).toFixed(1) + "%</td></tr class='text-right'>"
                        $('#grn_note_preview_table tbody').append(row_grn_table);
                    });
                    if (total_note_value_dr > total_note_value_cr){
                        total_note_value = total_note_value_dr - total_note_value_cr
                        total_note_value_type = "Dr"
                    } else{
                        total_note_value = total_note_value_cr - total_note_value_dr
                        total_note_value_type = "Cr"
                    }
                    var row_grn_table = "<tr><td colspan='4' class='text-center' style='font-weight: bold;'>Note Value</td><td colspan='5' class='text-right' style='font-weight: bold;'>"+parseFloat(total_note_value).toFixed(2) + " " + total_note_value_type +"</td></tr>";
                    $('#grn_note_preview_table tbody').append(row_grn_table);
                } else{
                    var row_grn_table = "<tr><td colspan='9' class='text-center' style='font-weight: bold;'>No Records Found!</td></tr>";
                    $('#grn_note_preview_table tbody').append(row_grn_table);
                }
                if (response[1].length > 0){
                    $.each(response[1], function (i, item){
                        document.getElementById("inv_no_inv").innerHTML = item.invoice_no;
                        document.getElementById("inv_date_inv").innerHTML = item.invoice_date;
                        document.getElementById("inv_value_inv").innerHTML = item.invoice_value.toFixed();
                        document.getElementById("party_name_inv").innerHTML = item.party_name;
                        document.getElementById("grn_no_inv").innerHTML = item.receipt_no;
                    });
                }
                $("#id_note_preview").text("Note Preview");
            },
            error: function (xhr, errmsg, err) {
                $("#id_note_preview").text("Note Preview");
                swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
            }
        });
    }
}


function generate_pdf_ajax(icd_id, status){
    $("#id-receipt_no").val(icd_id);
    $.ajax({
        url: "/erp/auditing/json/downloadDoc/",
        type: "post",
        datatype: "json",
        data: {receipt_no: icd_id, grn_code: icd_id, note_id: icd_id, response_data_type:"data", doc_type:"note"},
        success: function (response) {
            $("#id_note_preview").text("Note Preview");
            if (response.response_message == "Internal server error") {
                swal("",response.custom_message,"warning");
                return;
            }
        	if(response.remarks != undefined) {
                if(response.remarks.length > 0){
                    $(".remarks_count_link").text(response.remarks.length+" remarks").removeClass('disabled')
                }
                else {
                    $(".remarks_count_link").text("No remarks").addClass('disabled')
                }
                $("#icd_document_remarks").val(JSON.stringify(response.remarks));
            }
            $("#icd_document_modal").modal("show");
            $("#modal_icd_id").val(icd_id);
            if(response.data != ""){
            	var row = '<object id="icd_document" class="document_viewer" data="'+response.url+'">'+getPdfErrorMessage()+'</object>';
            	$("#icd_document_container").html(row);
            	$(".no_doc_available").addClass("hide").text('');
            } else {
            	$("#icd_document").addClass("hide");
            	$(".no_doc_available").removeClass("hide").text('Note is not generated for this Receipt.');
            }
        }
    });
}

function inlineAddMaterialToTable(){
    var receivedAgainst = $("#recagainst").val();
    if (receivedAgainst == "Job In" ) {
        $(".td_po_no, .td_gst").addClass('hide');
        $(".td_qty").attr("colspan","3");
        $(".td_others").removeClass('hide');
    }
    else if (receivedAgainst == "Others" ) {
         $(".td_po_no, .td_gst, .td_others").addClass('hide');
         $(".td_qty").attr("colspan","");
    }
    else if  (receivedAgainst == "Issues" ) {
          $(".td_po_no, .td_gst, .td_others").addClass('hide');
          $(".td_qty").attr("colspan","");
          $(".for-issue_remove, .td_unit_text").addClass('hide');
    }

    else if (receivedAgainst == "Sales Return"){
        $(".add_item_details").addClass('hide');
    }
    else{
         $(".td_po_no, .td_gst, .td_others").removeClass('hide');
         $(".td_qty").attr("colspan","3");

    }
}
$(window).load(function(){
    currencyChangeEvent("onload");
    if($("#id_receipt_no").val() == "") {
        $("#supplier").trigger("change");
    }
    $('#grn-location').on('change', function() {
        $("#materialtable tbody.item-for-goods tr, #materialtable tbody.item-for-service tr").remove();
        loadPo($("#id_receipt_no").val());
    });

})
