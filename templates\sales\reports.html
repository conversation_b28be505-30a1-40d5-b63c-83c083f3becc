{% extends "sales/sidebar.html" %}
{% block sales_reports %}
<style xmlns="http://www.w3.org/1999/html">
li.po_reports_side_menu a{
    outline: none;
    background-color: #e6983c !important;
}
</style>

<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/purchase_report.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-multiselect.js?v={{ current_version }}"></script>

<div class="right-content-container download-icon-onTop">
	<div class="page-title-container">
		<span class="page-title">Status Report</span>
	</div>
	<div class="container-fluid">
		<div class="view_table add_table row">
			<form action="/erp/sales/reports/" method="post">{% csrf_token %}
				<div class="col-lg-12 view_list_table" style="align:center;">
					<div class="filter-components">
						<div class="filter-components-container">
							<div class="dropdown">
								<button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
									<i class="fa fa-filter"></i>
								</button>
								<span class="dropdown-menu arrow_box arrow_box_filter">
									<div class="col-sm-12 form-group" >
										<label>Date Range</label>
										<div id="reportrange" class="report-range form-control">
											<i class="glyphicon glyphicon-calendar"></i>&nbsp;
											<span></span> <b class="caret"></b>
											<input type="hidden" class="fromdate" id="fromdate" name="since" value={{from_date}} />
											<input type="hidden" class="todate" id="todate" name="till" value={{to_date}} />
										</div>
									</div>
									<div class="col-sm-12 form-group">
										<label>Party Name</label>
										<select class="form-control chosen-select" name="party_id" id="party_id" >
											{% for j in customers %}
											<option value="{{j.0}}"{% if j.0 == party_id %}selected{% endif %}>{{ j.1 }}</option>
											{% endfor %}
										</select>
									</div>
									<div class="filter-footer">
										<input type="submit" class="btn btn-save" value="Apply" id="refresh"/>
			      					</div>
								</span>
							</div>
							<span class='filtered-condition filtered-date'>Date: <b></b></span>
							<span class='filtered-condition filtered-party'>Party Name: <b></b></span>
						</div>
					</div>
				</div>
			</form>
		</div>

		<div class="view_table add_table">
			<div class="col-lg-12 ">
				<div class="csv_export_button">
                    <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#report-table'), 'Status_Report.csv']);" data-tooltip="tooltip" data-placement="bottom" title="Download&nbsp;Status&nbsp;Report as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
                </div>
				<table class="table custom-table table-striped table-bordered" id="report-table" style="width: 100%;">
					<thead>
						<tr align="left" valign="left">
							<th>S.No.</th>
							<th>Invoice No</th>
							<th>Date</th>
							<th>Type</th>
							<th>OA No</th>
							<th>OA Date</th>
							<th>Project/Tag</th>
							<th>Customer</th>
							<th>Invoice Value</th>
							<th>Payment Status</th>
						</tr>

					</thead>
					{% for report_item in report_rows %}
					<tr align="left" valign="left">
						<td align="center">{{ forloop.counter}}.</td>
						<td align="center">{{ report_item.inv_no }}</td>
						<td align="center">{{ report_item.inv_date|date:"M d, Y" }}</td>
						<td align="center">{{ report_item.inv_type }}</td>
						<td align="center">{{ report_item.oa_no }}</td>
						<td align="center">{{ report_item.oa_date|date:"M d, Y" }}</td>
						<td>{{ report_item.project }}</td>
						<td>{{ report_item.customer }}</td>
						<td align="right">{{ report_item.inv_value }}</td>
						<td class="text-center"><span class='{{ report_item.payment_status|lower }}'>{{ report_item.payment_status }}</span></td>

					</tr>
					{% endfor %}
				</table>
			</div>
		</div>
	</div>
</div>
<script>
	$(document).ready(function(){
		TableHeaderFixed();
	});

	$(window).load(function(){
		updateFilterText();
	});

	function updateFilterText() {
		$(".filtered-date b").text($("#reportrange").find("span").text());
		$(".filtered-party b").text($("#party_id option:selected").text());
	}

	var oTable;
	var oSettings;
	function TableHeaderFixed() {
		oTable = $('#report-table').DataTable({
		fixedHeader: true,
		"pageLength": 50,
		"search": {
			"smart": false
		},
		"columns": [
			null,null,					
			{ "type": "date" },
			null,null,
			{ "type": "date" },
			null,null,null,null
			]
		});
		oTable.on("draw",function() {
			var keyword = $('#report-table_filter > label:eq(0) > input').val();
			$('#report-table').unmark();
			$('#report-table').mark(keyword,{});
			setHeightForTable();
		});
		oTable.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
		oSettings = oTable.settings();
		$( window ).resize();
	}
	
	$('.nav-pills li').removeClass('active');
	$("#li_status_report").addClass('active');
	$(".slide_container_part").removeClass('selected');
	$("#menu_sales").addClass('selected');
</script>
{% endblock %}