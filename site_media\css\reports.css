.adv_filter_list_container {
		border-right: solid 1px #ccc;
		height: 500px;
    	max-height: 500px;
    	padding:  0;
    	overflow: auto;
	}

	.adv_filter_list {
	    display: block;
	    padding: 10px 20px;
	    border-bottom: 1px solid #ccc;
	    color: #666;
	    text-shadow: 0 0 0 #666;
	    font-size: 14px;
	    cursor: pointer;
	}

	.adv_filter_list.active {
		background: #209be1;
		color: #fff;
	}

	.adv_filter_list.active.selected {
		background: #209be1;
		color: #fff;
	}

	.adv_filter_list.selected {
	    background: #eee;
	    opacity: 0.5;
	    /*pointer-events: none;
	    cursor: not-allowed;*/
	}

	.adv_filter_list.selected .fa-check {
		display: inline-block !important;
	}

	.adv_filter_list:hover {
		background: #eee;
		color: #000;
	}

	.adv_filter_condition div{
		padding: 7px;
	    display: inline-block;
	    min-width: 130px;
	}

	.adv_filter_consolidated label {
		padding: 7px;
	    display: inline-block;
	    min-width: 100px;
	}

	.adv_filter_condition .styled-checkbox.chk-wo-margin + label:before {
		margin-right: 15px;
	}

	#adv_filter_table tr td:first-child {
		display: list-item;
    	list-style-position: inside;
	}

	.fa.fa-check {
		color: green;
		padding-right: 6px;
	}