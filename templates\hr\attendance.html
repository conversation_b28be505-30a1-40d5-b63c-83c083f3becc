{% extends "hr/sidebar.html" %}
{% block hr_dashboard %}
<style>
	@media (min-width: 564px) {
		.daterangepicker .ranges ul {
			width: 125px;
		}
	}

	body {
		overflow-y: scroll;
	}

	#importtable td {
		word-break: inherit;
	}

	.styled-checkbox + label:before {
		margin-right: 0;
	}

	.bulk_mail_download {
		font-size: 13px;
	    margin-left: -9px;
	    display: inline-block;
	    margin-bottom: 10px;
	}

	.slimScrollDiv .bulk_mail_download {
		display: none !important;
	}

	.bulk_mail_download i {
		padding: 5px 6px 7px;
	    border: solid 1px #004195;
	    border-radius: 52px;
	    color: #004195;
	    cursor: pointer;
	    position: absolute;
        /*position: absolute;
	    margin-top: -22px;
	    margin-left: -30px;*/
	}

	.bulk_mail_download i.fa-envelope {
	    margin-left: -25px;
    	margin-top: -2px;
	}

	.bulk_mail_download i.fa-download {
		margin-left: 30px;
    	margin-top: -2px;
	}

	.mail-sent {
		color: #28a745;
	    text-shadow: 0 0 0 #28a745;
	    font-size: 13px;
	}

	.mail-failed {
		color: #dc3545;
	    text-shadow: 0 0 0 #dc3545;
	    font-size: 13px;
	}

	.mail-sending-process {
		position: absolute;
	    top: 46%;
	    left: 39%;
	    z-index: 100110;
	    background: #333;
	    color: #fff;
	    padding: 14px 20px;
	    text-align: center;
	    height: 120px;
	    width: 400px;
	}

	.datepicker .next.disabled {
		visibility: visible !important;
	}

	table[aria-describedby='payList_info'] thead tr th:nth-child(2) {
		background-image: none;
	}
</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/Fonts.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/attendance.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>

{% load humanize %}
<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">Attendance</span>
	</div>
	<input type="hidden" id="server_response" value="{{response}}"/>
	{% if access_level.edit %}
		<a role="button" class="btn btn-new-item pull-right export_csv" style="position: absolute; right: 85px; z-index: 1;" onclick="javascript:showImportAttendance();" data-tooltip="tooltip" title="Import Attendance Details">
			<i class="fa fa-upload" aria-hidden="true"></i>
			<span class="btn-new-upload-label"></span>
		</a>
	{%endif%}
	<div class="col-lg-12">
		<div class="view_table add_table">
			<div class="col-lg-12 view_list_table">
				<div class="row">
					<div class="clearfix"></div>
					<div class="col-md-6" style="padding-left: 330px; width: 530px;">
						<label>Month & Year:</label>
						<input type="text" id="attendance_search_month" class="form-control search_month"  placeholder="Select Month" style="background: #FFF;" readonly>
					</div>
					<div class="col-sm-3">
						<input type="button" id="search_report" class="btn btn-save btn-margin-1" value="Search" />
					</div>
				</div>
			</div>
		</div>
		<div class="col-lg-12 table-with-upload-button" >
			<div class="csv_export_button">
				<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#payList'), 'Attendance_List.csv']);" data-tooltip="tooltip" title="Download Attendance as CSV">
					<i class="fa fa-download" aria-hidden="true"></i>
				</a>
			</div>
			<table class="table table-bordered table-striped custom-table" id="payList" style="width: 100%;">
				<thead>
					<tr>
						<th hidden="hidden">S No</th>
						<th style="width: 66px;" class="exclude_export">
							<span class="bulk_mail_download hide">
								<i class="fa fa-envelope" aria-hidden="true" data-tooltip="tooltip" data-placement="right" title="Email Payslip" onclick="emailPayslips()"></i>&nbsp;&nbsp;
								<i class="fa fa-download" aria-hidden="true" data-tooltip="tooltip" data-placement="right" title="Download Payslip" onclick="downloadPaySlips()"></i>
							</span>
							<input type='checkbox' class='multi_check styled-checkbox' id="select-all-emp" onchange="selectAllEmployee();">
							<label for ='select-all-emp'>
						</th>
						<th style="width: 100px;">Emp. Code</th>
						<th>Name</th>
						<!--<th>Month</th>-->
						<th style="width: 100px;">Department</th>
						<th style="width: 30px;">Late Punch</th>
						<th style="width: 30px;">Holiday</th>
						<th style="width: 30px;"><span data-tooltip="tooltip" data-placement="left" title="Loss of Pay">LOP</span></th>
						<th style="width: 30px;"><span data-tooltip="tooltip" data-placement="left" title="Compensatory Paid Leave">CPL</span></th>
						<th style="width: 30px;"><span data-tooltip="tooltip" data-placement="left" title="Casual Leave">CL</span></th>
						<th style="width: 30px;"><span data-tooltip="tooltip" data-placement="left" title="Earned Leave">EL</span></th>
						<th style="width: 30px;"><span data-tooltip="tooltip" data-placement="left" title="Worked Days">WD</span></th>
						<th style="width: 30px;"><span data-tooltip="tooltip" data-placement="left" title="Payable Days">PD</span></th>
					</tr>
				</thead>
				<tbody id="attendance_reports">
				</tbody>
			</table>
		</div>
	</div>	
	
	
	<div id="modal_importattendance" class="modal fade" role="dialog">
		<div class="modal-dialog modal-lg" style="width: 90%;">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">&times;</button>
					<h4 class="modal-title">Import Attendance Details</h4>
				</div>
				<div class="modal-body">
					<div id="importattendance" style="overflow-x: auto;">
						<table id="importtable" class="table table-bordered table-striped custom-table custom-table-large">
							<thead>
							<tr align="center">
								<th>S. No</th>
								<th>Emp. Code</th>
								<th>Name</th>
								<th>Late Punch</th>
								<th>Worked Days</th>
								<th>Holidays</th>
								<th>Casual Leave</th>
								<th>Earned Leave</th>
								<th>Compensation</th>
								<th>LOP</th>
								<th>Total Salary Days</th>
								<th>Salary Advance</th>
								<th>Mediclaim</th>
								<th>TDS</th>
								<th>ESI</th>
								<th>PF</th>
								<th>Previous Balance</th>
								<th>Unpaid Changes</th>
							</tr>
							</thead>
							<tr>
								<td class="text-center">1</td>
								<td>ABC001</td>
								<td>ABC</td>
								<td class="text-center">0</td>
								<td class="text-center">19</td>
								<td class="text-center">0</td>
								<td class="text-center">1</td>
								<td class="text-center">2</td>
								<td class="text-center">1</td>
								<td class="text-center">2</td>
								<td class="text-center">25</td>
								<td class="text-center">0</td>
								<td class="text-center">0</td>
								<td class="text-center">1112</td>
								<td class="text-center">232</td>
								<td class="text-center">743</td>
								<td class="text-center">0</td>
								<td class="text-center">0</td>
							</tr>
							<tr>
								<td style="font-size:8px">(Number)</td>
								<td style="font-size:8px">(Text)</td>
								<td style="font-size:8px">(Text)</td>
								<td style="font-size:10px">(Decimal)</td>
								<td style="font-size:10px">(Decimal)</td>
								<td style="font-size:10px">(Decimal)</td>
								<td style="font-size:10px">(Decimal)</td>
								<td style="font-size:10px">(Decimal)</td>
								<td style="font-size:10px">(Decimal)</td>
								<td style="font-size:10px">(Decimal)</td>
								<td style="font-size:10px">(Decimal)</td>
								<td style="font-size:10px">(Decimal)</td>
								<td style="font-size:10px">(Decimal)</td>
								<td style="font-size:10px">(Decimal)</td>
								<td style="font-size:10px">(Decimal)</td>
								<td style="font-size:10px">(Decimal)</td>
								<td style="font-size:10px">(Decimal)</td>
								<td style="font-size:10px">(Decimal)</td>
							</tr>
							<tr>
								<td colspan="11" style="border: none;"><b>Please select the file to import Attendance Details</b></td>
								<td colspan="7" style="border: none;" class="text-right"><a href="/site_media/docs/Attendance_Details_Sample.xlsx" >Download Sample</a> </td>
							</tr>
							<tr class="tr-file-upload">
								<td colspan="18">
									<div class="row col-sm-12">
										<div class="col-sm-4" style="max-width: 150px;">
											<div class="form-group">
												<label>Month & Year:</label>
												<input type="text" id="attendance_month" class="form-control select_month" placeholder="Select Month" value="" style="background: #FFF; font-size: 14px !important;" readonly>
											</div>

										</div>
										<div class="col-sm-8" style="max-width: 450px;">
											<label>Month Wise Format</label>
											<div class="material_txt">
												<form action="#" method="post"  enctype="multipart/form-data">
													<input type="file" id="month_wise_uploader" name="month_wise_uploader" accept=".xls, .xlsx"  class="filestyle" data-buttonBefore="true" data-buttonText= "Attachment">
													<input type="button" name="upload" id="month_wise_upload" hidden="hidden" value="Upload" />
													<input type="hidden" id="month_wise_base64" />
												</form>
											</div>
										</div>
									</div>
									<div class="col-sm-12">
										<span style="font-size: 14px; color: #8a6d3b; text-shadow: 0px 0px red;">Note: Make sure the employees is profiled before uploading attendance for them to avoid failure!</span>
									</div>
								</td>
							</tr>
							<tr>
								<td colspan="18">
									<div class="material_txt">
										<input type="button" id="upload_month_wise_report" class="btn btn-save" value="Upload">
										<a href="#" class="btn btn-cancel" id="cmdhideUpload" data-dismiss="modal">Cancel</a></div>
								</td>
							</tr>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
	<form id="download_pay_slips" method="post" action="/erp/hr/payslip/">{% csrf_token %}
		<input type="hidden" value="" id="month" name="month"/>
		<input type="hidden" value="" id="employee_codes" name="employee_codes"/>
		<input type="submit" value="Download" id="bulkDownload" hidden="hidden">
	</form>
</div>

<div class="mail-sending-process hide">
	<div class="mail-sending-info">Sending mail... Please wait.<br /><br /></div>
	<div class="progress">
	  <div class="progress-bar progress-bar-striped active" role="progressbar"
	  aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" style="width:0%">
	    0%
	  </div>
	</div>
	<div class="mail-sending-count">
		<span class="currently-sent">1</span> out of <span class="total-mail"></span> mail sent.
	</div>
</div>

<script>
$('#li_attendance').addClass('active');

$(document).ready(function(){
	$("#search_report").click();
	var date = new Date();
	var currentMonth = date.getMonth();
	var currentDate = date.getDate();
	var currentYear = date.getFullYear();
	$('.select_month').datepicker({
		autoclose: true,
		minViewMode: 1,
		format: 'M, yyyy',
		endDate: new Date()
	});
	$('.search_month').datepicker({
		autoclose: true,
		minViewMode: 1,
		format: 'M, yyyy'
	});
	var date =  new Date();
	date.setDate(date.getDate() - 30);
	$('.select_month, .search_month').datepicker("setDate", date);
	if ($("#server_response").val().trim() != "") {
		message = $("#server_response").val().split('[::]')
		if (message.length > 1) {
			swal('', message[0], message[1]);
		} else {
			swal('', $("#server_response").val(), 'error');
		}
		$("#server_response").val("")
	}
});

$("#search_report").click(function(){
	loadAttendanceReport($("#attendance_search_month").val());
});

$("#upload_month_wise_report").click(function(){
	var month = $("#attendance_month").val();
	if (month == "") {
		swal('','Please choose the month of the data to be uploaded.','error');
		return;
	}
	if ($("#month_wise_uploader")[0].files.length == 0) {
		swal('','Please choose a file to upload.','error');
		return;
	}
	
	var filename = $("#month_wise_uploader")[0].files[0].name;
	var base64data = $("#month_wise_base64").val();
    if (base64data == "") {
		swal('', 'Please choose another file to upload.', 'error');
		return;
	}
	$.ajax({
        url: '/erp/hr/json/attendance_import/',
        type: "POST",
        dataType: "json",
        data: {
            month : month,
            dump_type: "monthly",
            base64data: base64data,
            filename: filename
        },
        success: function (data) {
            if (data.response_code == 200) {
                if (data.response_message === "Success") {
                    swal('', data.custom_message, 'success');
                } else {
                    swal('', data.custom_message, 'warning');
                }
                $("#modal_importattendance").modal('hide');
                $("#month_wise_base64").val("");
                setTimeout(function(){
					$("#month_wise_uploader").val('').clone(true);
					$("#month_wise_uploader").closest('form').find('.bootstrap-filestyle').find('input.form-control ').val('');
				},200);
				loadAttendanceReport($("#attendance_search_month").val());
            } else {
                swal('', data.response_message + ". " + data.custom_message, 'error');
				setTimeout(function(){
					$("#month_wise_uploader").val('').clone(true);
					$("#month_wise_uploader").closest('form').find('.bootstrap-filestyle').find('input.form-control ').val('');
				},200);
            }
            ga('send', 'event', 'HR', 'Upload Monthly Attendance & Pay Details', $('#enterprise_label').val(), 10);
        },
        error: function (xhr, errmsg, err) {
            console.log(err);
            console.log(errmsg);
            console.log(xhr.status + ": " + xhr.responseText);
            alert("Failed: " + errmsg);
        }
    });
});

$("#month_wise_uploader").change(function(){
	var fileExtension = ['xls', 'xlsx'];
	if ($.inArray($("#month_wise_uploader").val().split('.').pop().toLowerCase(), fileExtension) == -1) {
		swal("Invalid File Format", "Please upload only "+fileExtension.join(', ')+" file format.", "warning");
		setTimeout(function(){
			$("#month_wise_uploader").val('').clone(true);
			$("#month_wise_uploader").closest('form').find('.bootstrap-filestyle').find('input.form-control ').val('');
		},200);
	}
	else {
		var file = $(this)[0].files[0];
		var reader = new FileReader();
		reader.readAsDataURL(file);
		reader.onload = function () {
			$("#month_wise_base64").val(reader.result);
		};
		reader.onerror = function (error) {
			console.log('Error: ', error);
		};
	}
});

function loadAttendanceReport(month) {
	data = {}
	if (month != null){
		data = {month : month}
	}
	$.ajax({
		url: '/erp/hr/json/attendance_report/',
        type: "POST",
        dataType: "json",
        data: data,
        success: function (data) {
			$('#payList').DataTable().clear();
			$('#payList').DataTable().destroy();
            console.log("Loaded " + data.reports.length + " records.");
            var row = "";
            if (data.reports.length <= 0) {
                row = "";
            } else {
            	$("#month").val($("#attendance_search_month").val());
                $.each(data.reports, function (index, value) {
                    row += `<tr>
			                    <td hidden="hidden">${index+1}</td>
			                    <td class="text-center exclude_export" data-emp_code="${value.code}">
			                    	<input type="checkbox" class="multi_check styled-checkbox emp_checkbox" id="emp_${index}" onchange="selectAllCheckboxFlag();"><label for ="emp_${index}">
			                    </td>
			                    <td class="text-center">
			                    	<form target="_blank" method="post" action="/erp/hr/employee/edit/">
			                    	<input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">
			                    	<a role="button" onclick="javascript:clickButton('editEmployeeCode_${value.code}');">${value.code}</a>
			                    	<input type="hidden" value="${value.code}" name="emp_code">
			                    	<input type="submit" value="Edit" id="editEmployeeCode_${value.code}" hidden="hidden">
			                    	</form>
			                	</td>
			                    <td class="text-left">
			                    	<span style="width: calc(100% - 140px); display: inline-block;">
			                    		${value.name}
			                    	</span>	
			                    	<span style="width: 140px; display: inline-block; float: right; text-align: right;">
				                    	<a class="table-inline-icon hide" data-tooltip="tooltip" title="" onclick="downloadPaySlip('${value.code}')" data-original-title="Download Payslip"><i class="fa fa-download" aria-hidden="true"></i></a>
				                    	<a class="table-inline-icon hide" data-tooltip="tooltip" title="" onclick="emailPayslip('${value.code}', '${value.month}', 0)" data-original-title="Email Payslip"><i class="fa fa-envelope" aria-hidden="true"></i></a>
			                    	</span>
			                    </td>
			                    <td class="text-left">${value.department}</td>
			                    <td class="text-right">${value.description.LP}</td>
			                    <td class="text-right">${value.description.HD}</td>
			                    <td class="text-right">${value.description.LOP}</td>
			                    <td class="text-right">${value.description.CL}</td>
			                    <td class="text-right">${value.description.EL}</td>
			                    <td class="text-right">${value.description.CPL}</td>
			                    <td class="text-right">${value.description.WD}</td>
			                    <td class="text-right">${value.description.TSD}</td>
		                    </tr>`;

                    var non = '<td class="text-center exclude_export"><form target="_blank" id="download_pay_slip"'+ value.code +' method="post" action="/erp/hr/payslip/"><input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}"><a role="button" onclick="javascript:clickButton(\'downloadPaySlip_' + value.code + '\');">Download</a><input type="hidden" value="' + $("#attendance_search_month").val() + '" id="month" name="month"/><input type="hidden" value="' + value.code + '" id="employee_code" name="employee_code"/><input type="submit" value="Download" id="downloadPaySlip_' + value.code + '" hidden="hidden"></form></td>' +
                    '{% if access_level.approve or logged_in_user|canEdit:'HR' or logged_in_user|canApprove:'HR' %}' +
                    '<td class="text-center exclude_export"><a role="button" onclick="javascript:emailPayslip(\'' + value.code + '\', \'' + month +'\');">Email</a></td>' +
                    '{% endif %}' +
                    '</tr>';
                });
            }
            $('#attendance_reports').html(row);
			TableHeaderFixed();
			$("#select-all-emp").attr("checked", false);
			$(".bulk_mail_download").addClass("hide");
			if($("#attendance_reports").find("tr").text().trim() == "No Matching Results Found!") {
				$("label[for='select-all-emp']").addClass("hide");
			}
			else {
				$("label[for='select-all-emp']").removeClass("hide");
			}
        },
        error: function (xhr, errmsg, err) {
            console.log(err);
            console.log(errmsg);
            console.log(xhr.status + ": " + xhr.responseText);
            alert("Failed: " + errmsg);
        }
	});
}

function downloadPaySlips() {
	var empCodes = [];
	if($(".emp_checkbox:checked").length > 0) {
		$(".emp_checkbox:checked").each(function(){
			empCodes.push($(this).closest("td").attr("data-emp_code"));
		});
		$("#employee_codes").val(empCodes.join(","));
		$("#bulkDownload").click();
	}
}

function downloadPaySlip(employeeCode) {
	$("#employee_codes").val(employeeCode);
	$("#bulkDownload").click();
}

function emailPayslips() {
	if($(".emp_checkbox:checked").length > 0) {
		$(".emp_checkbox:checked").each(function(){
			emailPayslip($(this).closest("td").attr("data-emp_code"), $("#month").val(), 1);
		});
		$(".mail-sending-process").removeClass("hide");
		$(".total-mail").text($(".emp_checkbox:checked").length);
	}
}

function emailPayslip(employeeCode, month, isBulk) {
	$("#loading").show();
	data = {
		employee_code: employeeCode,
		month: month
	}
	$.ajax({
		url: '/erp/hr/json/payslip_email/',
        type: "POST",
        dataType: "json",
        data: data,
        success: function (response) {
	        $("#attendance_reports").find("td[data-emp_code='"+employeeCode+"']").find("label").addClass("hide");
	        $("#attendance_reports").find("td[data-emp_code='"+employeeCode+"']").find("i.fa-paper-plane").remove();
            if (response.response_message == "Success") {
            	$("#attendance_reports").find("td[data-emp_code='"+employeeCode+"']").append('<i class="fa fa-paper-plane mail-sent" aria-hidden="true" data-tooltip="tooltip" title="Sent"></i>');
			} else {
            	$("#attendance_reports").find("td[data-emp_code='"+employeeCode+"']").append('<i class="fa fa-paper-plane mail-failed" aria-hidden="true"  data-tooltip="tooltip" title="Failed"></i>');
			}
			TooltipInit();
			if(isBulk) {
				$(".mail-sending-count, .mail-sending-process").removeClass("hide");
				var count = $(".currently-sent").text();
				$(".currently-sent").text(Number(++count));
				var progressbar = ((count * 100)/$(".emp_checkbox:checked").length).toFixed(0);
				$(".progress-bar").text(progressbar+"%").css({width: progressbar+"%"})
				if($(".emp_checkbox:visible:checked").length == $("#attendance_reports").find(".fa-paper-plane").length) {
					var totalMailSent = $("#attendance_reports").find(".fa-paper-plane.mail-sent").length;
					var totalMailFailed = $("#attendance_reports").find(".fa-paper-plane.mail-failed").length;
					var info = `<h4>Mail sending process completed.</h4> <br> ${totalMailSent} mail sent successfully. <br> ${totalMailFailed} mail failed.`;
					$(".mail-sending-process").addClass("hide");
					$("#loading").hide();
					$(".progress-bar").text("0%").css({width: "0%"});
					$(".currently-sent, .total-mail").text(0)
					swal("",info, "info");
				}
			}
			else {
				if (response.response_message == "Success") {
					swal("","Mail sent successfully", "success");
				}
				else {
					swal("","Mail not sent due to some technical issue.<br />Please try again.", "error");
				}
				$("#loading").hide();
			}
        },
        error: function (xhr, errmsg, err) {
            $("#loading").hide();
            console.log(err);
            console.log(errmsg);
            console.log(xhr.status + ": " + xhr.responseText);
            alert("Failed: " + errmsg);
        }
	});
}

function showImportAttendance(){
	$("#modal_importattendance").modal('show');
}

var oTable;
var oSettings;
function TableHeaderFixed() {
   oTable = $('#payList').DataTable({
		fixedHeader: false,
        "scrollY": Number($(document).height() - 230),
        "scrollX": true,
		"pageLength": 50,
		"search": {
			"smart": false
		},
		columnDefs: [ { orderable: false, targets: [ 1 ] }]
	});
	oTable.on("draw",function() {
		var keyword = $('#payList_filter > label:eq(0) > input').val();
		$('#payList').unmark();
		$('#payList').mark(keyword,{});
		listTableHoverIconsInit('payList');
		setHeightForTable();
		$(".dataTables_paginate").find(".paginate_button").click(function(){
	    	selectAllCheckboxFlag();
	    });
	});
	oTable.on('page.dt', function() {
	  $('html, body').animate({
		scrollTop: $(".dataTables_wrapper").offset().top - 15
	   }, 'slow');
	  listTableHoverIconsInit('payList');
	});
	oSettings = oTable.settings();
	listTableHoverIconsInit('payList');
	$( window ).resize();
}
</script>
{% endblock %}