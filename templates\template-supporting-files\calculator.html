<table class="calculator_table hide" id="draggable">
	<thead>
		<tr>
			<td colspan=4>
				<i class="fa fa-minus calc_minimize" onclick="MinimizeCalculator();" data-tooltip="tooltip" title="Minimize" aria-hidden="true"></i>
				<i class="fa fa-times calc_close" onclick="closeCalculator();" data-tooltip="tooltip" title="Close" aria-hidden="true" ></i>
			</td>
		</tr>
		<tr class="calc-display-box">

			<td colspan=4>
				<span class="calc_invalid">Invalid Expresssion</span>
				<input type="text" class="calc-display-text" onkeypress="validateArthimatciNumberOnKeyPress(this,event)" id="calc_display" placeholder="0"  style="text-align:right;"/>
			</td>
		</tr>
	</thead>
	<tbody class="calc-num-pad">
		<tr>
			<td><button type="button" value="C" onClick="clearCalculator();">C</button></td>
			<td><button type="button" value="%"  onClick="CalculatePercentage();">%</button></td>
			<td><button type="button" value="/" onClick="calcKeyPadPress(this.value)">/</button></td>
			<td><button type="button" value="*"  onClick="calcKeyPadPress(this.value)">*</button></td>
		</tr>
		<tr>
			<td><button type="button" value="7" onClick="calcKeyPadPress(this.value)">7</button></td>
			<td><button type="button" value="8" onClick="calcKeyPadPress(this.value)">8</button></td>
			<td><button type="button" value="9" onClick="calcKeyPadPress(this.value)">9</button></td>
			<td><button type="button" value="-"  onClick="calcKeyPadPress(this.value)">-</button></td>
		</tr>
		<tr>
			<td><button type="button" value="4" onClick="calcKeyPadPress(this.value)">4</button></td>
			<td><button type="button" value="5" onClick="calcKeyPadPress(this.value)">5</button></td>
			<td><button type="button" value="6" onClick="calcKeyPadPress(this.value)">6</button></td>
			<td><button type="button" value="+" onClick="calcKeyPadPress(this.value)">+</button></td>
		</tr>
		<tr>
			<td><button type="button" value="1" onClick="calcKeyPadPress(this.value)">1</button></td>
			<td><button type="button" value="2" onClick="calcKeyPadPress(this.value)">2</button></td>
			<td><button type="button" value="3" onClick="calcKeyPadPress(this.value)">3</button></td>
			<td rowspan=2><button type="button" style="height:80px;"  onClick="calcCalculateTotal()" >=</button></td>
		</tr>
		<tr>
			<td colspan=2><button type="button" value="0" onClick="calcKeyPadPress(this.value)" style="width:80px; text-align: center;">0</button></td>
			<td><button type="button">.</button></td>
		</tr>
	</tbody>
</table>