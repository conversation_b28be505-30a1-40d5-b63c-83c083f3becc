<div class="row">
	<div class="col-sm-12">
		<input type="label" value="" class="txtbox10" id="idsupp" disabled="disabled" maxlength="12" width="30%" hidden="hidden">
		<div class="form-group row">
			<div class="col-sm-9">
				<label>Name<span class="mandatory_mark"> *</span></label>
				<input type="text" id="name" class="form-control" placeholder="Enter Name" maxlength="50" onkeypress="validateStringOnKeyPress(this,event,'alphaSpecialChar');" onblur="validateStringOnBlur(this,event,'alphaSpecialChar');"/>
			</div>
			<div class="col-sm-3">
				<label>Code</label>
				<input type="text" id="code" class="form-control" placeholder="Enter Code" onkeypress='validateStringOnKeyPress(this,event, "alphanumeric_with");' onblur='constructSupplierCode(); validateStringOnBlur(this, event, "alphanumeric_with");' maxlength="10" />
			</div>
		</div>
		<div class="form-group">
			<label>Address</label>
			<textarea id="add1" class="form-control" placeholder="Enter Address" maxlength="250" onkeypress="validateStringOnKeyPress(this,event,'alphaSpecialChar');" onblur="validateStringOnBlur(this,event,'alphaSpecialChar');"> </textarea>
		</div>
		<div class="hide">
			<label>Address2</label>
			<input type="text" id="add2" class="form-control" placeholder="Enter Address2" maxlength="100" onkeypress="validateStringOnKeyPress(this,event,'alphaSpecialChar');" onblur="validateStringOnBlur(this,event,'alphaSpecialChar');"/>
		</div>
		<div class="form-group row">
			<div class="col-sm-6">
				<label>City</label>
				<input type="text" id="city" class="form-control" placeholder="Enter City"  maxlength="50" onkeypress="validateStringOnKeyPress(this,event,'alphanumeric');" onblur="validateStringOnBlur(this,event,'alphanumeric');"/>
			</div>
			<div class="col-sm-6">
				<label>Pincode</label>
				<input type="text" id="pincode" class="form-control" placeholder="Enter Pincode" maxlength="6" setNumberRangeOnFocus(this,6,0,true) />
			</div>
		</div>
		<div class="form-group row">
			<div class="col-sm-6">
				<label>Country<span class="mandatory_mark"> *</span></label>
				<select class="form-control" id="id_country-__prefix__-make_choices" onchange="updateCountryValueInline('onchange')">
					{% for country in gst_country_list %}
						<option value="{{ country.country_code }}" {% if country.country_code == "IN" %} selected {% endif %}>{{ country.country_name }}</option>
					{% endfor %}
				</select>
			</div>
			<div class="col-sm-6 address-container-state-list">
				<label>State<span class="mandatory_mark"> *</span></label>
				<select class="form-control" id="id_state-__prefix__-make_choices" onchange="updateStateValue()">
					{% for state in state_list %}
						<option value="{{ state.code }}">{{ state.description }}</option>
					{% endfor %}
				</select>
			</div>
			<div class="col-sm-6 address-container-state-text hide">
				<label>State<span class="mandatory_mark"> *</span></label>
				<input type="text" id="state" class="form-control" placeholder="Enter State" maxlength="50" onkeypress="validateStringOnKeyPress(this,event,'alphanumeric');" onblur="validateStringOnBlur(this,event,'alphanumeric');"/>
			</div>
		</div>
		<div class="form-group row">
			<div class="col-sm-6">
				<label>Contact Person</label>
				<input type="text" id="conperson" class="form-control" placeholder="Contact Person" maxlength="50" onkeypress="validateStringOnKeyPress(this,event,'alphanumeric');" onblur="validateStringOnBlur(this,event,'alphanumeric');"/>
			</div>
			<div class="col-sm-6">
				<label>Email</label>
				<input type="text" id="email" class="form-control" placeholder="Enter Email address" maxlength="50" onkeypress="validateStringOnKeyPress(this,event,'alphaSpecialChar');" onblur="validateStringOnBlur(this,event,'alphaSpecialChar');"/>
			</div>
		</div>
		<div class="form-group row">
			<div class="col-sm-6">
				<label>Contact Number</label>
				<input type="text" id="phoneno" class="form-control" maxlength="30" placeholder="Enter Phone Number" onkeypress="validateStringOnKeyPress(this,event,'ph_number');" onblur="validateStringOnBlur(this,event,'ph_number');" />
			</div>
			<div class="col-sm-6">
				<label>Fax</label>
				<input type="text" id="faxno" class="form-control" maxlength="30" placeholder="Enter Fax No" onkeypress="validateStringOnKeyPress(this,event,'ph_number');" onblur="validateStringOnBlur(this,event,'ph_number');" />
			</div>
			
		</div>
		<div class="form-group">
			<label>GST Category<span class="mandatory_mark"> *</span></label>
			<select class="form-control" id="id_gst_category-__prefix__-make_choices" onChange='gstCategoryChangeEvent()'>
				{% for category in gst_category_list %}
					<option value="{{ category.category_id }}" data-desc="{{ category.category_desc }}">{{ category.category_name }}</option>
				{% endfor %}
			</select>
		</div>
		<div class="col-sm-12 remove-padding form-group hide" id="party-port-contianer" style="margin-top: 10px;">
			<label>Port</label>
			<input class="form-control" id="id_party-port" maxlength="18" name="party-port" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" placeholder="Enter PORT" type="text" value="{{ party_details.port }}">
			
		</div>
		<div class="form-group row registration-extra-details">
			<div class="col-sm-6 registration-details">
				<label>GSTIN<span class="mandatory_mark gstin_mandate">*</span></label>
				<input type="text" id="gstno" class="form-control" placeholder="Enter GST No" maxlength="30" onkeypress="validateStringOnKeyPress(this,event,'alphanumeric');" onblur="validateStringOnBlur(this,event,'alphanumeric');"/>
			</div>
			<div class="col-sm-6 registration-details">
				<label>PAN No</label>
				<input type="text" id="panno" class="form-control" placeholder="Enter PAN No" maxlength="30" onkeypress="validateStringOnKeyPress(this,event,'alphanumeric');" onblur="validateStringOnBlur(this,event,'alphanumeric');"/>
			</div>
		</div>
		<div class="form-group row">
			<div class="col-sm-6">
				<label>Currency</label>
				 <select class="form-control" name="select" id="currency">
					{% for j in currency %}
					<option value="{{ j.id }}">{{ j.code }}</option>
					{% endfor %}
				 </select>
				<input type="hidden" name="home_currency" id="id_home_currency" value="{{home_currency}}">
			</div>
			<div class="col-sm-6" style="margin-top: 2px;">
				<div class="checkbox" style="padding-left: 20px !important;">
					<input type="checkbox" id="duty_pass_reminder" />
					<label for="duty_pass_reminder">Remind Duty Passed</label>
					<br />
					<input type="checkbox" id="id_is_supplier"/>
					<label for="id_is_supplier" style="margin-top: 5px; width: 100px;">Supplier</label>
					<input type="checkbox" id="id_is_customer"/>
					<label for="id_is_customer">Customer</label>
				</div>
			</div>
		</div>
		<div class=" row">
			<div class="form-group col-sm-6 hide" id="div_supplier">
				<label>Supplier Cr. Period</label>
				<input type="text" id="pay_cre_days" class="form-control" maxlength="5" placeholder="Enter Supplier Credit Days" onfocus="setNumberRangeOnFocus(this,5,0)" autocomplete="off" />
				<span class="unit_display pull-right">days</span>
			</div>
			<div class="form-group col-sm-6 hide" id="div_customer">
				<label>Customer Cr. Period</label>
				<input type="text" id="rec_cre_days" class="form-control" maxlength="5" placeholder="Enter Customer Credit Days" onfocus="setNumberRangeOnFocus(this,5,0)" autocomplete="off" />
				<span class="unit_display pull-right">days</span>
			</div>
		</div>
	</div>
</div>