 function firstTimeRegistration(){
    var enterpriseName = $(".enterprise-name-label").text().trim();
    $(".chat-img-char").text(enterpriseName.charAt(0));
    $("div[class*='mc_']").addClass("hide");
    $("div[class*='sc_']").addClass("hide");
    $(".next-prev-container").addClass("hide");
    
    $(".switch_radio_btn a").click(function(){
        $(this).closest(".switch_radio_btn").find("a").removeClass("active").addClass("noActive");
        $(this).removeClass("noActive").addClass("active");
        setTimeout(function(){
            scrollToBottom();
        },600)
    });

    setTimeout(function(){
        $(".mc_1").removeClass("hide");
        $(".sc_1").removeClass("hide");
        $(".mc_1_loading").removeClass("hide");
    },500);
    setTimeout(function(){
        $(".sc_1a").removeClass("hide");
        $(".mc_1_loading").addClass("hide");
    },2000);
    setTimeout(function(){
        $(".sc_1b").removeClass("hide");
    },2500);

    $(".switch_radio_btn").click(function(){
        if($(this).attr("id") == "switch_btn_1") {
            if($(this).find("a.active").data("title") == 0) {
                $(".mc_2, .mc_3, .mc_4, .mc_5, .mc_6").addClass("hide-permanent");
                $("div[class*='sc_2'], div[class*='sc_3'], div[class*='sc_4'], div[class*='sc_5'], div[class*='sc_6']").addClass("hide-permanent");
                $(".skip-preference-settings").addClass("hide");
                setTimeout(function(){
                    $(".save-preferenses-btn").click();
                },5000);
            }
            else {
                $(".skip-preference-settings").removeClass("hide");
            }
            $("#switch_btn_1").addClass("div-disabled").css({opacity: "0.5"});
        }
        else if($(this).attr("id") == "switch_btn_2_dummy") {
            if($(this).find("a.active").data("title") == 0) {
                $("#switch_btn_2, #switch_btn_2a, #switch_btn_2b, #switch_btn_2c").find("a[data-title='0']").removeClass("noActive").addClass("active");
            }
        }
        else if($(this).attr("id") == "switch_btn_4") {
            if($(this).find("a.active").data("title") == 0) {
                $(".sc_4f").addClass("hide-permanent")
            }
            else {
                $(".sc_4f").removeClass("hide-permanent")
            }
        }
        else if($(this).attr("id") == "switch_btn_5") {
            $(".sc_4d, .sc_4e").addClass("hide");
        }
        else if($(this).attr("id") == "switch_btn_6") {
            $(".sc_5d, .sc_5e, .sc_5f, .sc_5g, .sc_5h, .sc_5i, .sc_5j").addClass("hide");
            if($(this).find("a.active").data("title") == 0) {
                $("#switch_btn_7, #switch_btn_8").find("a").removeClass("active").addClass("noActive");
                $("#switch_btn_7").find("a[data-title='0']").removeClass("noActive").addClass("active");
                $("#switch_btn_8").find("a[data-title='0']").removeClass("noActive").addClass("active");
            }
            else {
                $("#switch_btn_7, #switch_btn_8").find("a").removeClass("active").addClass("noActive");
            }
        }
        else if($(this).attr("id") == "switch_btn_9") {
            $(".skip-preference-settings").addClass("hide");
            setTimeout(function(){
                $(".save-preferenses-btn").click();
            },5000);
        }

        var loading = $(this).find("a.active").data("loading");
        var addItem = $(this).find("a.active").data("locations");
        var removeItem = $(this).find("a.active").data("remove");
        $("."+loading).removeClass("hide");
        
        for (i = 0; i < addItem.length; ++i) {
            apperance(i);
        }
        if(removeItem.length > 0) {
            for (j = 0; j < removeItem.length; ++j) {
                $("."+removeItem[j].path).addClass("hide");
            }
        }
        function apperance(i) {
            var path = addItem[i].path;
            var time = addItem[i].time;
            setTimeout(function(){
                if(path.indexOf("mc") != -1) {
                    $("div[class*='_loading']").addClass("hide");
                    $("."+path+"_loading").removeClass("hide");
                }
                if(i == addItem.length-1) {
                    $("div[class*='_loading']").addClass("hide");
                }
                $("."+path).removeClass("hide");
                scrollToBottom();
            },time);
            
            /*setTimeout(function(){
                scrollToBottom();
            },600)*/
        }

    });
}

function skipConversation() {
    swal({
        title: "Are you sure?",
        text: "This bot messages will help you to understand the overall application, as well you can set your preferences as your need. Still do you want to skip?",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#209be1",
        confirmButtonText: "Yes, skip it!",
        closeOnConfirm: true
    },
    function(isConfirm){
        if (isConfirm) {
            $("#switch_btn_1").removeClass("div-disabled").find("a").last().click();
        }
    });
}

function scrollToBottom(){
    $('#edit_enterprise_modal .modal-content').animate({scrollTop:$(".financial-container").height()}, 'slow');
}

function closeCropModal(current) {
    $(current).closest(".modal").modal("hide")
}

function showMoreContact(){
    $(".other_contact_details_loop").slideToggle(100);
    setTimeout(function(){
        if($(".other_contact_details_loop").is(":visible")) {
            $(".show_more_contact").html('Show less <i class="fa fa-chevron-up" aria-hidden="true"></i>');
        }
        else {
            $(".show_more_contact").html('Show more <i class="fa fa-chevron-down" aria-hidden="true"></i>');
        }
    }, 120);
    
}

function skipUpload(){
    swal({
        title: "All Set!",
        text: "Play around!",
        type: "success"
    },
    function(){
        window.location.href = "/erp/admin/enterprise";
    });
    $.ajax({
        url: "/erp/admin/send_enterprise_registered_mail/",
        type: "post",
        success: function(response) {

        }
    });
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('event', 'conversion', {'send_to': 'AW-722929303/rfwJCO6B7NABEJeN3NgC'});
}

$(document).ready(function(){
    if ($("#id_page_message").val() != "") {
        //console.log($("#id_page_message").val())
        //swal($("#id_page_message").val());
        //firstTimeRegistration();
    }
    $("#id_page_message").val("");
    if($("#id_enterprise-address_1").val().trim() == "") {
        $("#is_new_registration").val(1);
        $(".next-slide-btn").removeClass("hide");
        openEditableModal('profile-container');
        $(".enterprice-modal-close").remove();
        $(".welcome-text").removeClass("hide");
    }
    else {
        $(".save-current-btn").removeClass("hide");
        $(".skip-upload").addClass("hide");
    }

    $(".workflow-container .configurable-checkbox").click(function(){
        $(this).toggleClass('item-disabled');
        if($(this).hasClass("icd_config")) {
            if($(this).hasClass("item-disabled")) {
                $(".suggest_cr_note, .dr_cr_auto").addClass("item-disabled");
                $(".path-to-icd").attr("stroke-dasharray", "3 3")
                $(".path-to-accounts").removeAttr("stroke-dasharray");
            }
            else {
                $(".path-to-icd").removeAttr("stroke-dasharray");
                $(".path-to-accounts").attr("stroke-dasharray", "3 3");
            }
        }
        if($(this).hasClass("suggest_cr_note") || $(this).hasClass("dr_cr_auto")) {
            if(!$(this).hasClass("item-disabled")) {
                $(".icd_config").removeClass("item-disabled")
            }
        }

        if($(this).hasClass("request_ack")) {
            var current = $(this);
            if($(this).hasClass("item-disabled") && !$("#dr_cr_auto").hasClass("item-disabled")) {
                $.ajax({
                    url: "/erp/auditing/json/check_icd_pending_ack/",
                    type: "POST",
                    data: "",
                    success: function(response) {
                        if (response.note_count > 0){
                            swal({
                                title: "",
                                text: "Kindly make note that Audit Notes pending Party Acknowledgement will need your attention & action to make them accounted.",
                                type: "warning"
                            });
                            current.removeClass("item-disabled");
                        }
                        else{
                            current.addClass("item-disabled");
                        }
                    }
                });
            }
        }
    });
    saveBulkFile();
    svgConfigrationSettingsInit();
    if($("#id_enterprise-purchase").val() != "") {
        var po_configurations = JSON.parse($("#id_enterprise-purchase").val());
        var po_terms = "<ol>";
        if (po_configurations["terms"].length > 1){
            for(i=0; i<po_configurations["terms"].length; i++){
                po_terms = po_terms + "<li>" + po_configurations["terms"][i] + "</li>";
            }
            po_terms = po_terms + "</ol>";
        }else{
            po_terms = po_configurations["terms"][0];
        }
        $("#po_mail_subject").val(po_configurations["subject"]);
        $("#purchase_editor").html(po_terms);
    }

    $("#save_purchase").click(function(){
        var purchase_notes = CKEDITOR.instances.purchase_editor.getData().replace(/<\/?(o|u)l>\s*<\/?li>\s*/g, "").replace(/<\/li>\s*<li>/g, "\", \"");
        console.log("PP", purchase_notes)
        purchase_notes = purchase_notes.replace(/<\/p>\s*<p>/g, "<br/>").replace(/<\/?p>/g, "");
        modified_po_configurations = "{\"subject\":\"" + $("#po_mail_subject").val() + "\", \"terms\":[\"" + purchase_notes + "\"]}";

        $("#id_enterprise-purchase").val(modified_po_configurations);
        savePurchaseNote();
    });

    if($("#update_status").val()=="success"){
        $(".enterprise_name").text($("#id_enterprise-name").val())
    }
});


function showBulkImports() {
    $("#bulkImports").modal('show');
}

function showImportTallyXml() {
    $("#ImportTallyXml").modal('show');
    UnprocessedTallyxmlFiles();
}
function cancelExportToTally() {
    $('#inventory_trasaction').prop('checked', false);
    $('#closing_balance_as_opening').prop('checked', false);
    $('#from_date').val("");
    $('#to_date').val("");
    DateRangeInit();
}
function showTallyExport() {
    getFirstTransactionDate();
    $("#exportTallyData").modal('show');
}

function clearTransaction(type) {
    if(type=="Transaction") {
        var swaltext = "<span style='color: #dd4b39;'>All Transaction data like Indents, Purchase & Sale Orders, Purchase & Sale Invoices, Bills, Vouchers will be lost forever & cannot be restored.<br/>Are you sure you wish to clear them all?</span>";        
        var swalConfirmText = "Yes! Clear it";
    }
    else {
        var swaltext = "<span style='color: #dd4b39;'>System will re-boot your Enterprise to a Clean State. All your data will be lost forever.<br />Are you sure, you wish to Reset?</span>";
        var swalConfirmText = "Yes! Reset it";
    }

    swal({
        title: "<span style='color: #dd4b39;'>Are you sure?</span>",
        text: swaltext,
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#209be1",
        confirmButtonText: swalConfirmText,
        closeOnConfirm: true
    },
    function(){
        $("#loading").show();
        setTimeout(function(){
                $.ajax({
                    url:"/erp/admin/erase_enterprise_data/",
                    type: 'POST',
                    data: {'erase_type': type},
                    async: true,
                    success: function (response) {
                        $("#loading").hide();
                        if(response.response_message == "Success") {
                            swal({
                                title: "",
                                text: response.custom_message,
                                type: "success"
                            },
                            function() {
                                window.location.href = "/erp/admin/enterprise";
                            });
                        }
                    },
                    error: function(){
                       $("#loading").hide();
                    }
                });
        },10);
    });
}

function svgConfigrationSettingsInit() {
    var indentSetting = $("#id_enterprise-setting_flags_0").is(":checked");
    var icdSetting = $("#id_enterprise-setting_flags_1").is(":checked");
    var ignoreCrSetting = $("#id_enterprise-setting_flags_2").is(":checked");
    var suggestCrSetting = $("#id_enterprise-setting_flags_3").is(":checked");
    var autoVoucherSetting = $("#id_enterprise-setting_flags_4").is(":checked");
    var negativeStockSetting = $("#id_enterprise-is_negative_stock_allowed").is(":checked");
    var gateInwardSetting = $("#id_enterprise-is_gate_inward_no_mandatory").is(":checked");
    var gateInwardNoMandatorySetting = $("#id_enterprise-gate_inward_no_flags_0").is(":checked");
    var uniqueInFiscalYrSetting = $("#id_enterprise-gate_inward_no_flags_1").is(":checked");
    var gateInwardNoAutomatedSetting = $("#id_enterprise-gate_inward_no_flags_2").is(":checked");
    var gateInwardNoEditableSetting = $("#id_enterprise-gate_inward_no_flags_3").is(":checked");

    var poMandatorySetting = $("#id_enterprise-is_purchase_order_mandatory").is(":checked");
    var deliverySchedule = $("#id_enterprise-is_delivery_schedule").is(":checked");
    var multipleUnits = $("#id_enterprise-is_multiple_units").is(":checked");
    var blanket_po = $("#id_enterprise-is_blanket_po").is(":checked");
    var request_ack = $("#id_enterprise-is_icd_request_acknowledgement").is(":checked");
    var is_price_modification_disabled = $("#id_enterprise-is_price_modification_disabled").is(":checked");
    // TODO #7966: Enable and disable the gate inward no fields based on the above values.
    if(!indentSetting) {
        $("g").find(".indent-config").addClass("item-disabled")
    }
    else {
        $("g").find(".indent-config").removeClass("item-disabled")   
    }
    if(!icdSetting) {
        $("g").find(".icd_config").addClass("item-disabled")
    }
    else {
        $("g").find(".icd_config").removeClass("item-disabled")
    }
    if(!suggestCrSetting) {
        $("g").find(".dr_cr_auto").addClass("item-disabled")
    }
    else {
        $("g").find(".dr_cr_auto").removeClass("item-disabled")
    }
    if(!ignoreCrSetting) {
        $("g").find(".suggest_cr_note").addClass("item-disabled")
    }
    else {
        $("g").find(".suggest_cr_note").removeClass("item-disabled")
    }
    if(!autoVoucherSetting) {
        $("g").find(".voucher_auto").addClass("item-disabled")
    }
    else {
        $("g").find(".voucher_auto").removeClass("item-disabled")
    }
    if(!negativeStockSetting) {
        $("g").find(".negative_invertory").addClass("item-disabled")
    }
    else {
        $("g").find(".negative_invertory").removeClass("item-disabled")
    }
    if(!gateInwardNoMandatorySetting) {
        $("g.mandate_gate_inward").addClass("item-disabled")
    }
    else {
        $("g.mandate_gate_inward").removeClass("item-disabled")
    }
     if(!uniqueInFiscalYrSetting) {
        $("g.unique_gate_inward").addClass("item-disabled")
    }
    else {
        $("g.unique_gate_inward").removeClass("item-disabled")
    }
     if(!gateInwardNoAutomatedSetting) {
        $("g.automate_gate_inward").addClass("item-disabled")
    }
    else {
        $("g.editable_gate_inward").removeClass("hide");
        $("g.automate_gate_inward").removeClass("item-disabled")
    }
     if(!gateInwardNoEditableSetting) {
        $("g.editable_gate_inward").addClass("item-disabled")
    }
    else {
        $("g.editable_gate_inward").removeClass("item-disabled")
    }
    if(!poMandatorySetting) {
        $("g").find(".mandate_po").addClass("item-disabled");
        $("g.mandate_po").find("span[data-span]").removeClass("i-enabled");
    }
    else {
        $("g").find(".mandate_po").removeClass("item-disabled")
        $("g.mandate_po").find("span[data-span]").addClass("i-enabled");
    }
    if(!deliverySchedule) {
        $("g").find(".delivery_schedule").addClass("item-disabled");
    }
    else {
        $("g").find(".delivery_schedule").removeClass("item-disabled")
    }
    if(!multipleUnits) {
        $("g").find(".multiple_units").addClass("item-disabled");
    }
    else {
        $("g").find(".multiple_units").removeClass("item-disabled")
    }
    if(!blanket_po) {
        $("g").find(".blanket_po").addClass("item-disabled");
    }
    else {
        $("g").find(".blanket_po").removeClass("item-disabled")
    }
    if(!request_ack) {
        $("g").find(".request_ack").addClass("item-disabled");
    }
    else {
        $("g").find(".request_ack").removeClass("item-disabled")
    }

    if(icdSetting) {
        $(".path-to-icd").removeAttr("stroke-dasharray");
        $(".path-to-accounts").attr("stroke-dasharray", "3 3");
    }
    else {
        $(".path-to-icd").attr("stroke-dasharray", "3 3")
        $(".path-to-accounts").removeAttr("stroke-dasharray");
    }
    if(!is_price_modification_disabled) {
        $("g").find(".is_price_modification_disabled").addClass("item-disabled");
    }
    else {
        $("g").find(".is_price_modification_disabled").removeClass("item-disabled")
    }
}


function UnprocessedTallyxmlFiles(){
    $.ajax({
        url:"/erp/admin/json/unprocessXMLFiles/",
        type: 'POST',
        data: "",
        async: true,
        success: function (response) {
            if (response.length > 0){
                $("#tally_xml_un_process_table").find("tr:gt(0)").remove();
                $.each(response, function (i, item) {
                    var row = '<tr class="tr_tally_import"><td>'+item['file_name']+' '+item['uploaded_on']+'  </td><td class="text-center" style="width: 60px;"><i class="fa fa-trash-o" role="button"  id="'+item['file_id']+'" value="'+item['file_id']+'" onclick="deleteUnprocessedTallyFile(this);"></i></td></tr>';
                    $('#tally_xml_un_process_table').append(row);
                });
                $("#tallyunprocessfile").removeClass("hide");
            }else{
                $("#tallyunprocessfile").addClass("hide");
            }
        },
        error : function(xhr,errmsg,err)
         {
            console.log(xhr.status + ": " + xhr.responseText);
        },
        cache: false,
        contentType: false,
        processData: false
    });
}

function getFirstTransactionDate(){
    $.ajax({
        url: "/erp/migration/getFirstTransactionDate/",
        type: "post",
        datatype:"json",
        data: {enterprise_id:$("#enterprise_id").val()},
        success: function (response) {
            $('#first_trasaction_date').text(": "+moment(response['earliest_transaction_date']).format('MMM DD, YYYY'))
            if (response['previous_closure_date'] != ""){
                $('#previous_closure_date').text(": "+moment(response['previous_closure_date']).format('MMM DD, YYYY'))
            }
        }
    });
}

function deleteUnprocessedTallyFile(evt) {
    swal({
        title: "Are you sure?",
        text: "Do you want to Delete this file?",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#209be1",
        confirmButtonText: "Yes, delete it!",
        closeOnConfirm: true
    },
    function(){
        file_id = evt.id;
        $.ajax({
            url:"/erp/admin/json/unprocessXMLFilesDelete/",
            type: 'POST',
            data: {file_id: file_id},
            async: true,
            success: function (response) {
                $(evt).closest("tr").remove();
            },
        });
    });
}

function saveBulkFile(){
    $("form#bulk_file").submit(function(){
        var regex = /^([a-zA-Z0-9\s_\\.\-:])+(.csv|.xlsx)$/;
        if (regex.test($("#bulkfileUpload").val().toLowerCase())) {
            var formData = new FormData($(this)[0]);
            $.ajax({
                url:"/erp/admin/json/bulkImport/",
                type: 'POST',
                data: formData,
                async: false,
                success: function (data) {
                    if(data['response_message'] == "Session Timeout") {
                        location.reload();
                        return;
                    }
                    $("#bulkImports").modal('hide');
                    if(data['status']['status_code']==0)
                    {
                        swal(data['status']['title'], data['status']['message'] + " Sheet names are case sensitive", "");
                    }
                      else
                      {
                            $("#bulk_import_status_modal").modal('show');
                             $('#material_status').text(data["material_response"]["message"]);
                              $("#failed_import_table").find("tr:gt(0)").remove();
                             if (data["material_response"]["failed_items"].length !=0){
                                 $.each(data["material_response"]["failed_items"], function(i, item) {
                                    var row = "<tr bgcolor='#ececec' border='0' align='center' style='font-size:11px; font-weight:normal;'>"+
                                    "<td>"+item[0]+ "</td><td>"+item[1]+ "</td><td align='left'>" +
                                    item[2]  + "</td><td align='left'>" +
                                    item[3]  + "</td><td align='left'>" +
                                    item[4]  + "</td><td align='left'>" +
                                    item[5]  + "</td><td align='left'>" +
                                    item[6]  + "</td><td align='right'>" +
                                    item[7]  + "</td><td align='left'>" +
                                    item[8]  + "</td><td align='left'>" +
                                    item[9]  + "</td></tr>"
                                    $('#failed_import_table').append(row).addClass('tbl');
                                   });
                             }
                            else{
                                $('#failed_import_table').hide();
                            }
                                 $('#ledger_status').text(data["ledger_response"]["message"]);
                                  $("#failed_item_table").find("tr:gt(0)").remove();
                                if (data["ledger_response"]["failed_items"].length != 0){
                                    $.each(data["ledger_response"]["failed_items"], function(i, item) {
                                        var row = "<tr bgcolor='#ececec' border='0' align='center' style='font-size:11px; font-weight:normal;'>"+
                                        "<td>"+item['ledger_name']+ "</td><td>"+item['account_group_id']+ "</td><td align='left'>" +
                                        item['opening_debit']  + "</td><td align='left'>" +
                                        item['opening_credit']  + "</td><td align='left'>" +
                                        item['error']  + "</td></tr>"
                                        $('#failed_item_table').append(row).addClass('tbl');
                                    });
                                }
                                else{
                                    $('#failed_item_table').hide();
                                }

                                 $('#party_status').text(data["party_response"]["message"]);
                                 $("#party_failed_import_table").find("tr:gt(0)").remove();
                                if (data["party_response"]["failed_items"].length != 0){
                                   console.log(".....................res.....................")
                                   console.log(data["party_response"]["failed_items"])
                                    $.each(data["party_response"]["failed_items"], function(i, item) {
                                            var row = "<tr bgcolor='#ececec' border='0' align='center' style='font-size:11px; font-weight:normal;'>"+
                                            "<td>"+item['code']+ "</td><td>"+item['name']+ "</td><td align='left'>" +
                                            item['address_1']  + "</td><td align='left'>" +
                                            item['city']  + "</td><td align='left'>" +
                                            item['state']  + "</td><td align='left'>" +
                                            item['country_code']  + "</td><td align='left'>" +
                                            item['pin_code']  + "</td><td align='left'>" +
                                            item['contact']  + "</td><td align='right'>" +
                                            item['phone']  + "</td><td align='left'>" +
                                            item['email']  + "</td><td align='right'>" +
                                            item['fax_no']  + "</td><td align='left'>" +
                                            item['payment_credit_days']  + "</td><td align='left'>" +
                                            item['payable_opening']  + "</td><td align='left'>" +
                                            item['receipt_credit_days']  + "</td><td align='left'>" +
                                            item['receivable_opening']  + "</td><td align='left'>" +
                                            item['as_on']  + "</td><td align='left'>" +
                                            item['gst_category']  + "</td><td align='left'>" +
                                            item['port']  + "</td><td align='left'>" +
                                            item['gstin']  + "</td><td align='left'>" +
                                            item['pan']  + "</td><td align='left'>" +
                                            item['error']  + "</td></tr>"
                                            $('#party_failed_import_table').append(row).addClass('tbl');
                                        });
                                    }
                                else{
                                    $('#party_failed_import_table').hide();
                                }

                      }
                },
                error : function(xhr,errmsg,err)
                 {
                    console.log(xhr.status + ": " + xhr.responseText);
                },
                cache: false,
                contentType: false,
                processData: false
            });
        }
        else {
           alert('please upload the  xslx  or CSV file');
           return false;
        }
    });
}

function exportAsTallyXML(){
    $.ajax({
        url: "/erp/migration/export_as_tally_xml/",
        type: "post",
        datatype:"json",
        data: {
            from_date:$("#from_date").val(),
            to_date:$("#to_date").val(),
            is_inventory_transaction:$('#inventory_trasaction').is(":checked"),
            is_closing_balance_as_opening:$('#closing_balance_as_opening').is(":checked")
        },
        success: function(response) {
            $('#exportTallyData').modal('hide')
            fetchExportDetails();
        }
    });
}

function fetchExportDetails() {
     $.ajax({
        url: "/erp/migration/fetch_export_details/",
        type: "post",
        datatype:"json",
        data: {enterprise_id:$("#enterprise_id").val()},
        success: function(response) {
            if(oTableExport != undefined) {
                oTableExport.destroy();
            }
            $("#export_history tbody").html("");

            $.each(response.result, function (i, item) {
                var itemStatus;
                if(item.status=="Pending" || item.status=="In Progress") {
                    if($("#admin_edit_access").val() == "True") {
                        itemStatus = `<span data-tooltip="tooltip" title="View Status" data-placement='left' class="btn transparent-btn" onclick="fetchExportLogDetails('${item.id}')">In Progress</span>
                                        <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Refresh Status' role='button' onclick="fetchExportLogDetails('${item.id}')">
                                            <i class='fa fa-refresh'></i>
                                        </span>
                                        <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Cancel Export'  onclick="deleteExportDetails('${item.id}')">
                                            <i class="fa fa-times"></i>
                                        </span>`;
                    }
                    else {
                        itemStatus = `<span data-tooltip="tooltip" title="View Status" data-placement='left' class="btn transparent-btn" onclick="fetchExportLogDetails('${item.id}')">In Progress</span>`;
                    }
                }
                else if(item.status=="Completed") {
                    if($("#admin_edit_access").val() == "True") {
                        itemStatus = `<span class="btn transparent-btn btn-success" onclick="fetchExportLogDetails('${item.id}')">Completed</span>
                                        <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Download' style='margin-left: 5px;'>
                                           <a href="${item.download_file_name}?&response-content-disposition=attachment" target="_blank" >
                                                <i class="fa fa-download"></i>
                                            </a>
                                        </span>`;
                    }
                    else {
                        itemStatus = `<span class="btn transparent-btn btn-success" onclick="fetchExportLogDetails('${item.id}')">Completed</span>`;
                    }
                }
                else if(item.status=="Aborted") {
                    itemStatus=`<span class="btn transparent-btn btn-danger">Aborted</span>`;
                }
                else if(item.status=="Failed") {
                    itemStatus=`<span class="btn transparent-btn btn-danger" onclick="fetchExportLogDetails('${item.id}')">Failed</span>`;
                }
                var row = `<tr data-export_id="${item.id}">
                                <td class='text-center'>${i+1}</td>
                                <td class="text-center" data-sort='${moment(item.initiated_on).format('YYYYMMDDHHmm')}'>${moment(item.initiated_on).format('D MMM, YYYY H:mm')}</td>
                                <td>${item.initiated_by}</td>
                                <td class="text-left">
                                    <ul>
                                        <li>${moment(item.from_date).format('D MMMM, YYYY')} - ${moment(item.to_date).format('D MMMM, YYYY')}</li>
                                        ${(item.is_inventory_transaction == 'true' ? '<li>Included Inventory Transaction</li>' : '')}
                                        ${(item.is_closing_balance_as_opening == 'true' ? '<li>Closing Balance as Opening</li>' : '')}
                                    </ul>
                                </td>
                                <td class="text-center td-status-row" data-sort='${item.status}'>
                                    ${itemStatus}
                                </td>
                                <td hidden='hidden' > ${item.id} </td>
                            </tr>`;
                $("#export_history tbody").append(row)
            });
            TableHeaderFixedExport();
            listTableHoverIconsInit('export_history');
        }
    });
}

function fetchExportLogDetails(exportId) {
    $.ajax({
        url: "/erp/migration/fetch_export_log_details/",
        type: "post",
        datatype:"json",
        data: {export_id:exportId},
        success: function(response) {
            $(".export-status-container").html("");
            if(response.result.length==0){
                var row=`No Record Found`;
                $(".export-status-container").append(row);
            }
            else{
                $.each(response.result, function (i, item) {
                    var row=`<li>${item.message}</li>`;
					$(".export-status-container").append(row);
                });
            }
            if(response.status.toLowerCase()=="completed"){
                if($("#admin_edit_access").val() == "True") {
                    var row = ` <span class="btn transparent-btn btn-success" onclick="fetchExportLogDetails('${exportId}')">Completed</span>
                                <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Download'>
                                   <a href="${response.download_file_name}?&response-content-disposition=attachment" target="_blank" >
                                        <i class="fa fa-download"></i>
                                    </a>
                                </span>`;
                }
                else {
                    var row = ` <span class="btn transparent-btn btn-success" onclick="fetchExportLogDetails('${exportId}')">Completed</span>`;
                }
            }
            else if(response.status.toLowerCase()=="failed"){
                  var row = ` <span class="btn transparent-btn btn-danger" onclick="fetchExportLogDetails('${exportId}')">Failed</span>`;
            }
            $("#export_history").find(`tr[data-export_id = ${exportId}]`).find(".td-status-row").html(row);
            listTableHoverIconsInit('export_history');
            $("#exportTallyStatus").modal("show");
        }
    });
}

function deleteExportDetails(exportId) {
    swal({
        title: "Are you sure!",
        text: "Do you want to cancel this Export?",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#209be1",
        confirmButtonText: "Yes, do it!",
        closeOnConfirm: true,
        closeOnCancel: true
    },
    function(isConfirm){
        if (isConfirm) {
            $.ajax({
                url: "/erp/migration/delete_export_details/",
                type: "post",
                datatype:"json",
                data: {export_id:exportId},
                success: function(response) {
                    if(response.response_message.toLowerCase() == "success") {
                        var row = `<span class="btn transparent-btn btn-danger">Aborted</span>`;
                        $("#export_history").find(`tr[data-export_id = ${exportId}]`).find(".td-status-row").html(row);
                    }
                }
            });
        }
    });
}

var oTableExport;
var oSettingsExport;
function TableHeaderFixedExport() {
    oTableExport = $('#export_history').DataTable( {
        "pageLength": 50,
        fixedHeader: false,
        "scrollY": Number($(document).height() - 230),
        "scrollX": true,
        "search": {
            "smart": false
        },
        "columnDefs": [
            { "sortable": false, "targets": [3] }
        ],
    });
    oTableExport.on("draw",function() {
        var keyword = $('#export_history_filter > label:eq(0) > input').val();
        $('#export_history').unmark();
        $('#export_history').mark(keyword,{});
        listTableHoverIconsInit('export_history');
        $( window ).resize();
    });
    oTableExport.on('page.dt', function() {
      $('html, body').animate({
        scrollTop: $(".dataTables_wrapper").offset().top - 15
       }, 'slow');
      listTableHoverIconsInit('export_history');
    });
    oSettingsExport = oTableExport.settings();
    
    listTableHoverIconsInit('export_history');
    $('#loading').hide();
    setTimeout(function(){
        $( window ).resize();
        $("#loading").hide();
    },100);
}