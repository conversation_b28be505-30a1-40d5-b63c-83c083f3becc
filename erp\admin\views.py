"""
View layer of the User management module
"""
import base64
import json
import os
import re
import string
import uuid
from datetime import datetime
from xml.etree import ElementTree

import pymysql
import simplejson
import xlrd
from cryptography.fernet import Fernet
from django.contrib.auth import SESSION_KEY
from django.http import HttpResponseRedirect, HttpResponse
from django.template.response import TemplateResponse
from sqlalchemy.orm import make_transient

from erp import properties
from erp.accounts.voucher_views import importVoucher, __constructVoucher
from erp.admin import logger
from erp.admin.backend import UserService, UserPermissionFormset, UserVO, EnterpriseProfileService, UserDAO, \
	TemplateConfigService, InvoiceTemplateVO, IT_CONFIG_FORM_PREFIX, IT_GENERAL_FORM_PREFIX, \
	IT_HEADER_FORM_PREFIX, IT_ITEMS_FORM_PREFIX, IT_SUMMARY_FORM_PREFIX, IT_MISC_FORM_PREFIX, \
	IT_HEADER_BANNER_FORM_PREFIX, IT_FOOTER_BANNER_FORM_PREFIX, PurchaseTemplateConfigService
from erp.admin.changelog import EnterpriseProfileChangelog, EnterprisePreferenceChangelog, InvoiceTemplateChangelog, \
	PurchaseTemplateChangelog
from erp.admin.import_backend import TallyService
from erp.admin.pgi_backend import SubscriptionService
from erp.admin.xml_parse_views import ImportService
from erp.auth import USER_IN_SESSION_KEY, ENTERPRISE_IN_SESSION_KEY, HOME_CURRENCY_KEY, \
	ENTERPRISE_CONFIGURATION_CHANGED, \
	ENTERPRISE_SUBSCRIPTION, ENTERPRISE_ID_SESSION_KEY
from erp.auth.backend import LoginService
from erp.auth.request_handler import RequestHandler
from erp.commons.backend import push_notification
from erp.dao import executeQuery
from erp.forms import UserForm, InvoiceTemplateConfigForm, InvoiceTemplateGeneralConfigForm, \
	InvoiceTemplateHeaderConfigForm, InvoiceTemplateItemsConfigForm, InvoiceTemplateSummaryConfigForm, \
	InvoiceTemplateMiscConfigForm
from erp.formsets import InvoiceTemplateHeaderBannerFormset, InvoiceTemplateFooterBannerFormset
from erp.helper import getUser, saveAttachment, populateFiscalYear, getStateList, populatePurchaseTemplateBaseFont, \
	getProjectListBasedPermissions
from erp.masters.backend import MasterService, LocationService
from erp.masters.material_views import importMaterials, constructReceipt
from erp.masters.party_views import importParties
from erp.models import EnterpriseImages, EnterpriseRegistrationDetail, InvoiceTemplateConfig, ImportDocuments, \
	Invoice, EnterpriseNotes, EraseLog, SalesEstimateTemplateConfig, Enterprise, Project, Currency, PurchaseOrder, \
	SalesEstimate, OA
from erp.properties import LOGIN_URL, MANAGE_USER_TEMPLATE, MANAGE_ENTERPRISE_TEMPLATE, TEMPLATE_TITLE_KEY, \
	MANAGE_INVOICE_TEMPLATE_NEW, MANAGE_PO_TEMPLATE, MANAGE_SALES_ESTIMATE_TEMPLATE_CONFIG, MANAGE_OA_TEMPLATE_CONFIG
from erp.purchase.service import PurchaseService
from erp.sales.backend import InvoiceService
from settings import SQLASession, HOST, USER, PASSWORD, DBNAME, PORT, TEMP_DIR, GCS_BUCKET_NAME, bucket, CIPHER_KEY, \
	GST_CLIENT_ID, GST_CLIENT_SECRET_KEY, GST_EMAIL
from util import helper
from util.api_util import response_code
from util.helper import writeFile, getAbsolutePath, readFile

__author__ = 'kalaivanan'

ENTERPRISE_UPDATE_STATUS_KEY = 'enterprise_update_status'
INVOICE_PREVIEW_TEMPLATE = 'preview_res'
INVOICE_TEMPLATE = 'master_res'
INVOICE_CONFIG_TEMPLATE = 'config_res'
INVOICE_GENERAL_TEMPLATE = 'general_res'
INVOICE_HEADER_TEMPLATE = 'header_res'
INVOICE_ITEM_TEMPLATE = 'item_res'
INVOICE_SUMMARY_TEMPLATE = 'summary_res'
INVOICE_MISC_TEMPLATE = 'misc_res'
INVOICE_TEMPLATE_HEADER_BANNER = 'header_banner_formset'
INVOICE_TEMPLATE_FOOTER_BANNER = 'footer_banner_formset'
ENTERPRISE_REG_DETAIL = 'enterprise_reg_detail'
ENTERPRISE_CONTACT_DETAIL = 'enterprise_contact_detail'
PO_GENERAL_TEMPLATE = 'general_res'
PO_HEADER_TEMPLATE = 'header_res'
PO_ITEM_TEMPLATE = 'item_res'
PO_SUMMARY_TEMPLATE = 'summary_res'
PO_MISC_TEMPLATE = 'misc_res'


def manageUser(request):
	"""
	Renders a view to manage User profiles - displays all the Users by default

	:param request:
	:return:
	"""
	user_service = UserService()
	logger.info('Rendering User Management view...')
	request_handler = RequestHandler(request)
	enterprise = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise
	user_vo = user_service.constructUserVO(enterprise_id=enterprise.id)
	last_saved_user_detail = request_handler.getSessionAttribute("last_saved_user_detail")
	request_handler.removeSessionAttribute("last_saved_user_detail")
	return TemplateResponse(template=MANAGE_USER_TEMPLATE, request=request, context={
		'user': user_vo.user_form, 'permissions': user_vo.permission_formset,
		'user_max_count': enterprise.user_max_count, 'last_saved_user_detail': last_saved_user_detail,
		'users': user_service.user_dao.getAllUsers(enterprise_id=enterprise.id), TEMPLATE_TITLE_KEY: "User"})


def editUser(request):
	"""
	Renders a view loaded with a selected User's profile - general info, permissions, projects associated - that will
	enable editing of the chosen User's profile

	:param request:
	:return:
	"""
	user_service = UserService()
	user_dao_service = UserDAO()
	request_handler = RequestHandler(request)
	user_locations = []
	if request_handler.isPostRequest():
		email = request_handler.getPostData('email')
		logger.info('User to be edited: %s' % email)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		user_vo = user_service.constructUserVO(email=email, enterprise_id=enterprise_id)
		logger.debug(user_vo)
		user = user_dao_service.getUserByEmail(email=email)
		permission_projects = [] if not user or user.is_super else getProjectListBasedPermissions(user.id)
		location_list = LocationService().get_user_locations(
			user_id=user.id, enterprise_id=enterprise_id)
		for location in location_list:
			user_locations.append(location.id)
		return TemplateResponse(template=MANAGE_USER_TEMPLATE, request=request, context={
			'user': user_vo.user_form, 'permissions': user_vo.permission_formset,
			TEMPLATE_TITLE_KEY: user_vo.user_form.initial['first_name'], "permission_projects": permission_projects,
			"user_locations": user_locations})
	return HttpResponseRedirect(LOGIN_URL)


def saveUser(request):
	"""
	Saves the edited User profile and renders the manage view on successful save.

	:param request:
	:return:
	"""
	user_locations = []
	user_service = UserService()
	logger.info('Saving a User...')
	request_handler = RequestHandler(request)
	url_base = request.build_absolute_uri().split("/erp/")[0]
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	is_project_wise_pl = request_handler.getSessionAttribute(ENTERPRISE_IN_SESSION_KEY).is_project_wise_pl
	username = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).username
	user_vo_to_be_saved = UserVO(
		UserForm(data=request_handler.getPostData(), prefix='user', enterprise_id=enterprise_id),
		UserPermissionFormset(request_handler.getPostData(), prefix='permission'))
	[last_saved_user_detail, saved_vo] = user_service.saveUser(
		user_vo=user_vo_to_be_saved, username=username, profile_img=request_handler.getPostData("party-picture"),
		signature=request_handler.getPostData("party-sign"), url_base=url_base, enterprise_id=enterprise_id, is_project_wise_pl=is_project_wise_pl)
	location_list = LocationService().get_user_locations(
		user_id=saved_vo.user_form.cleaned_data["id"], enterprise_id=enterprise_id)
	for location in location_list:
		user_locations.append(location.id)
	request_handler.setSessionAttribute("last_saved_user_detail", last_saved_user_detail)
	return HttpResponseRedirect(properties.MANAGE_USER_URL) if saved_vo.is_valid() else TemplateResponse(
		template=MANAGE_USER_TEMPLATE, request=request,
		context={'user': saved_vo.user_form, 'permissions': saved_vo.permission_formset,
				 "user_locations": user_locations})


def checkUser(request):
	"""
	Check The User profile already Exist.

	:param request:
	:return:
	"""
	user_dao = UserDAO()
	logger.info('Check a User...')
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	email = request_handler.getPostData("user_email")
	username = request_handler.getPostData("username")

	logger.info('User to be edited: %s' % email)
	user = user_dao.getUserByEmail(email=email)
	has_email = user is not None
	user_detail = {"has_email": has_email}
	logger.info("Has user %s" % json.dumps(user_detail))
	response = HttpResponse(content=json.dumps(user_detail), mimetype='application/json')
	return response


def deleteUser(request):
	"""
	Deletes User requested from a set of users listed, identified by Username

	:param request:
	:return:
	"""
	user_service = UserService()
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	delete_user_by_email = request_handler.getPostData('delete_user_by_email')
	activate_user_by_email = request_handler.getPostData('activate_user_by_email')
	if activate_user_by_email:
		logger.info('Activating User: %s' % activate_user_by_email)
		user_service.activateUser(activate_user_by_email, enterprise_id)
		return HttpResponseRedirect(properties.MANAGE_USER_SEARCH_URL)
	logger.info('Deleting User: %s' % delete_user_by_email)
	user_service.deleteUser(delete_user_by_email, enterprise_id)
	return HttpResponseRedirect(properties.MANAGE_USER_SEARCH_URL)


def manageEnterprise(request):
	"""
	Renders a view to manage User profiles - displays all the Users by default

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	service = EnterpriseProfileService()
	db_session = SQLASession()
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	logger.info('Rendering Details of Enterprise - %s' % enterprise_id)
	enterprise_form = service.constructEnterpriseProfileForm(enterprise_id=enterprise_id)
	logger.debug("Enterprise Form - %s" % enterprise_form)
	enterprise = service.profile_dao.getProfile(enterprise_id=enterprise_id)
	enterprise_reg_detail = service.getRegistrationDetails(enterprise_id=enterprise_id)
	enterprise_contact_detail = service.getContactDetails(enterprise_id=enterprise_id)
	enterprise_image = db_session.query(EnterpriseImages).filter(EnterpriseImages.enterprise_id == enterprise_id).first()
	countries = MasterService().getCountries()
	message = request_handler.getSessionAttribute('message')
	message = message if message else ''
	request_handler.removeSessionAttribute('message')
	status = request_handler.getSessionAttribute('status')
	status = status if status else ''
	request_handler.removeSessionAttribute('status')
	if enterprise_image and enterprise_image.logo and enterprise_image.logo != "":
		logo = "data:image/%s;base64,%s" % (enterprise_image.ext, base64.encodestring(enterprise_image.logo))
	else:
		logo = ""
	enterprise_notes = db_session.query(
		EnterpriseNotes.po_doc_reg_items, EnterpriseNotes.include_hsnsac, EnterpriseNotes.po_doc_datetime_format,
		EnterpriseNotes.include_annexure, EnterpriseNotes.logo_size, EnterpriseNotes.include_hsnsac_column).filter(
		EnterpriseNotes.enterprise_id == enterprise_id).first()
	se_template_config = db_session.query(
		SalesEstimateTemplateConfig.se_doc_reg_items).filter(SalesEstimateTemplateConfig.enterprise_id == enterprise_id).first()
	projects = db_session.query(Project).filter(
		Project.enterprise_id == enterprise_id, Project.is_active != -1).order_by(Project.is_active.desc(), Project.name).all()
	po_doc_reg_items = []
	se_doc_reg_items = []
	include_hsnsac = False
	include_annexure = False
	logo_size = 30
	include_hsnsac_column = False
	po_doc_datetime_format = ''
	country_list = []
	for country in countries:
		country_list.append({"country_code": country.code, "country_name": country.name})

	if request_handler.getSessionAttribute('HOME_CURRENCY'):
		home_currency = request_handler.getSessionAttribute('HOME_CURRENCY')
	else:
		home_currency = enterprise.home_currency.code
	currency = db_session.query(Currency).filter_by(code=home_currency.code).first()
	if enterprise_notes:
		if enterprise_notes[0]:
			po_doc_reg_items = json.loads(enterprise_notes[0]) if enterprise_notes[0] else ""
		include_hsnsac = enterprise_notes[1]
		po_doc_datetime_format = enterprise_notes[2]
		include_annexure = enterprise_notes[3]
		logo_size = enterprise_notes[4]
		include_hsnsac_column = enterprise_notes[5]
	if se_template_config:
		if se_template_config[0]:
			se_doc_reg_items = json.loads(se_template_config[0]) if se_template_config[0] else ""
	erase_log = db_session.query(EraseLog).filter(EraseLog.enterprise_id == enterprise_id)
	return TemplateResponse(template=MANAGE_ENTERPRISE_TEMPLATE, request=request, context={
		'enterprise': enterprise_form, 'home_currency': home_currency, 'currency_symbol': currency.symbols, 'enterprise_logo': logo,
		ENTERPRISE_REG_DETAIL: enterprise_reg_detail, ENTERPRISE_CONTACT_DETAIL: enterprise_contact_detail,
		'fiscal_year': populateFiscalYear(), 'po_doc_reg_items': po_doc_reg_items, 'include_hsnsac': include_hsnsac,
		'po_doc_datetime_format': po_doc_datetime_format, 'include_annexure': include_annexure, 'logo_size': logo_size,
		'include_hsnsac_column': include_hsnsac_column, 'erase_log': erase_log, 'se_doc_reg_items': se_doc_reg_items,
		TEMPLATE_TITLE_KEY: "Enterprise Profile", 'message': message, 'status': status,
		'GCS_BUCKET_NAME': GCS_BUCKET_NAME, 'projects': projects, 'country_list': country_list, 'state_list': getStateList()})


def saveEnterpriseProfile(request):
	"""

	:param request:
	:return:
	"""
	try:
		logger.info("Updating Enterprise Profile...")
		request_handler = RequestHandler(request)
		service = EnterpriseProfileService()
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		enterprise_detail = request_handler.getPostData('enterprise_detail')
		enterprise_contact_detail = request_handler.getPostData('enterprise_contact_details')

		enterprise_reg_detail = request_handler.getPostData('enterprise_reg_details')
		response = response_code.success()

		if enterprise_detail != "":
			enterprise_detail = json.loads(enterprise_detail)

		updated_enterprise = service.saveEnterpriseDetails(
			enterprise_id=enterprise_id, enterprise_detail=enterprise_detail, user_id=user_id)

		if enterprise_contact_detail != "":
			enterprise_contact_detail = json.loads(enterprise_contact_detail)

		service.saveEnterpriseContactDetails(
			enterprise_id=enterprise_id, enterprise_contact_detail=enterprise_contact_detail, user_id=user_id)

		if enterprise_reg_detail:
			enterprise_reg_detail = json.loads(enterprise_reg_detail)

		service.saveEnterpriseRegistration(
			enterprise_id=enterprise_id, enterprise_reg_detail=enterprise_reg_detail, user_id=user_id)

		request_handler.setSessionAttribute(ENTERPRISE_CONFIGURATION_CHANGED, "YES")
		message = "Enterprise Profile modified."
		push_notification(enterprise_id=enterprise_id, sender_id=user_id, message=message, include_sender=True)
		logged_in_user = request.session[USER_IN_SESSION_KEY]
		logged_in_user.enterprise = updated_enterprise
		request_handler.setSessionAttribute(HOME_CURRENCY_KEY, updated_enterprise.home_currency)

		request_handler.setSessionAttribute('status', 'success')
		EnterpriseProfileChangelog().queryInsert(
			user_id=user_id, enterprise_id=enterprise_id, data=dict(
				enterprise_detail=enterprise_detail, enterprise_contact_detail=enterprise_contact_detail,
				enterprise_reg_detail=enterprise_reg_detail))
		response['custom_message'] = "Enterprise Profile updated successfully"
	except Exception as e:
		logger.exception("Enterprise Profile Update failed - %s" % e.message)
		response = response_code.internalError()
		response['exception'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getEnterpriseProfileLogList(request):
	"""
	get Specified voucher log list
	:return:
	"""
	response = {}
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	offset = request_handler.getPostData('offset')
	limit = request_handler.getPostData('limit')
	try:
		response = EnterpriseProfileChangelog().fetchLogList(
			eid=enterprise_id, enterprise_id=enterprise_id, offset=offset, limit=limit)
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getEnterpriseProfileLogData(request):
	"""
	get changelog data from a specified voucher timeset
	:param request:
	:return:
	"""
	response = {}
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	modified_at = request_handler.getPostData('modified_at')
	preference = request_handler.getPostData('preference')
	try:
		response = EnterpriseProfileChangelog().fetchLogData(
			eid=enterprise_id, enterprise_id=enterprise_id, modified_at=modified_at, preference=preference)
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def savePreference(request):
	"""

	:param request:
	:return:
	"""
	try:
		logger.info("Updating Enterprise Preference Configuration...")
		request_handler = RequestHandler(request)
		service = EnterpriseProfileService()
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		user = request_handler.getSessionAttribute(USER_IN_SESSION_KEY)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		setting_flags = request_handler.getPostData('setting_flags')
		is_negative_stock_allowed = int(request_handler.getPostData('is_negative_stock_allowed'))
		is_gate_inward_no_mandatory = int(request_handler.getPostData('is_gate_inward_no_mandatory'))
		gate_inward_no_flags = request_handler.getPostData('gate_inward_no_flags')
		is_purchase_order_mandatory = int(request_handler.getPostData('is_purchase_order_mandatory'))
		is_delivery_schedule = int(request_handler.getPostData('is_delivery_schedule'))
		is_multiple_units = int(request_handler.getPostData('is_multiple_units'))
		is_icd_request_acknowledgement = int(request_handler.getPostData('is_icd_request_acknowledgement'))
		is_blanket_po = int(request_handler.getPostData('is_blanket_po'))
		is_price_modification_disabled = int(request_handler.getPostData('is_price_modification_disabled'))
		response = response_code.success()
		enterprise_detail = service.updateEnterprisePreferences(
			enterprise_id=enterprise_id, setting_flags=setting_flags,
			is_negative_stock_allowed=is_negative_stock_allowed,
			is_gate_inward_no_mandatory=is_gate_inward_no_mandatory, gate_inward_no_flags=gate_inward_no_flags,
			is_purchase_order_mandatory=is_purchase_order_mandatory, user_id=user_id,
			is_multiple_units=is_multiple_units, is_delivery_schedule=is_delivery_schedule,
			is_icd_request_acknowledgement=is_icd_request_acknowledgement, is_blanket_po=is_blanket_po,
			is_price_modification_disabled=is_price_modification_disabled)
		make_transient(enterprise_detail)
		EnterprisePreferenceChangelog().queryInsert(
			user_id=user_id, enterprise_id=enterprise_id, data=dict(
				setting_flags=setting_flags, is_negative_stock_allowed=is_negative_stock_allowed,
				gate_inward_no_flags=gate_inward_no_flags,
				is_purchase_order_mandatory=is_purchase_order_mandatory, is_multiple_units=is_multiple_units,
				is_delivery_schedule=is_delivery_schedule, is_icd_request_acknowledgement=is_icd_request_acknowledgement,
				is_blanket_po=is_blanket_po))
		request_handler.setSessionAttribute(ENTERPRISE_CONFIGURATION_CHANGED, "YES")
		request_handler.setSessionAttribute(ENTERPRISE_IN_SESSION_KEY, enterprise_detail)
		user.enterprise = enterprise_detail
		message = "Enterprise Preferences modified."
		push_notification(
			enterprise_id=enterprise_id, sender_id=user_id, message=message, include_sender=True, action='permission')

		response['custom_message'] = "Enterprise Preferences updated successfully"
	except Exception as e:
		logger.exception("Enterprise Preference Configuration Update failed - %s" % e.message)
		response = response_code.internalError()
		response['exception'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def saveExpense(request):
	"""

	:param request:
	:return:
	"""
	try:
		logger.info("Updating Enterprise Expenses Configuration...")
		request_handler = RequestHandler(request)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		service = EnterpriseProfileService()
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		claim_heads = request_handler.getPostData('claim_heads').encode('utf-8').split(',')
		expense_heads = request_handler.getPostData('expense_heads').encode('utf-8').split(',')
		response = response_code.success()

		service.updateEnterpriseClaimHeads(enterprise_id=enterprise_id, claim_heads=claim_heads)

		service.updateEnterpriseExpenseHeads(
			enterprise_id=enterprise_id, expense_heads=expense_heads)

		message = "Expense Configuration modified."
		push_notification(enterprise_id=enterprise_id, sender_id=user_id, message=message, include_sender=True)

		response['custom_message'] = "Enterprise Expense Configuration updated successfully"
	except Exception as e:
		logger.exception("Enterprise Expenses Configuration Update failed - %s" % e.message)
		response = response_code.internalError()
		response['exception'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def savePurchase(request):
	"""

	:param request:
	:return:
	"""
	try:
		logger.info("Updating Enterprise Purchase Notes...")
		request_handler = RequestHandler(request)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		service = EnterpriseProfileService()
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		purchase_notes = request_handler.getPostData('purchase_notes')
		po_doc_reg_items = request_handler.getPostData('po_doc_reg_items')
		include_hsnsac = int(request_handler.getPostData('include_hsnsac'))
		date_time_format = str(request_handler.getPostData('date_time_format'))
		include_annexure = int(request_handler.getPostData('include_annexure'))
		logo_size = int(request_handler.getPostData('logo_size'))
		include_hsnsac_column = int(request_handler.getPostData('include_hsnsac_column'))
		is_price_modification_disabled = int(request_handler.getPostData('is_price_modification_disabled'))
		is_price_modification_disabled_quick_po = int(request_handler.getPostData('is_price_modification_disabled_quick_po'))
		response = response_code.success()
		service.updateEnterprisePurchase(
			enterprise_id=enterprise_id, purchase_notes=purchase_notes, po_doc_reg_items=po_doc_reg_items,
			include_hsnsac=include_hsnsac, date_time_format=date_time_format, include_annexure=include_annexure,
			logo_size=logo_size, include_hsnsac_column=include_hsnsac_column,
			is_price_modification_disabled=is_price_modification_disabled,
			is_price_modification_disabled_quick_po=is_price_modification_disabled_quick_po)


		message = "PO Print Configuration modified."
		push_notification(enterprise_id=enterprise_id, sender_id=user_id, message=message, include_sender=True)

		response['custom_message'] = "PO Print Configuration updated successfully"
	except Exception as e:
		logger.exception("Purchase Configuration Update failed - %s" % e.message)
		response = response_code.internalError()
		response['exception'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def saveSETemplateConfig(request):
	"""

	:param request:
	:return:
	"""
	try:
		logger.info("Updating Sales Estimate Template Config...")
		request_handler = RequestHandler(request)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		service = EnterpriseProfileService()
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		se_doc_reg_items = request_handler.getPostData('se_doc_reg_items')
		response = response_code.success()
		service.updateSETemplateConfig(enterprise_id=enterprise_id, se_doc_reg_items=se_doc_reg_items)

		message = "Sales Estimate Template Configuration modified."
		push_notification(enterprise_id=enterprise_id, sender_id=user_id, message=message, include_sender=True)

		response['custom_message'] = "Sales Estimate Template Configuration updated successfully"
	except Exception as e:
		logger.exception("Sales Estimate Template  Configuration Update failed - %s" % e.message)
		response = response_code.internalError()
		response['exception'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def bulkImport(request):
	try:
		bulk_file = request.FILES['bulkfile']
		book = xlrd.open_workbook(file_contents=bulk_file.read())
		name_list = ['materials', 'party', 'ledger']
		for name in book.sheet_names():
			if name not in name_list:
				raise Exception('Invalid sheet names')
		material_response = importMaterials(request)
		party_response = importParties(request)
		ledger_response = importVoucher(request)
	except Exception as e:
		if str(e) == 'Invalid sheet names':
			return HttpResponse(content=simplejson.dumps(
				{'status': {'message': 'Allowed sheet names' + str(name_list), 'title': str(e), 'status_code': 0}}),
				mimetype='application/json')
		else:
			return HttpResponse(
				content=simplejson.dumps({'status': {'message': str(e), 'title': 'Import error', 'status_code': 0}}),
				mimetype='application/json')
	return HttpResponse(
		content=simplejson.dumps({
			'material_response': material_response, 'party_response': party_response,
			'ledger_response': ledger_response,
			'status': {'status_message': "", 'status_code': 1}}), mimetype='application/json')


def unprocessXMLFiles(request):
	try:
		request_handler = RequestHandler(request)
		enterprise = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise
		uploaded_by = request_handler.getSessionAttribute(SESSION_KEY)
		unprocessed_docs = TallyService().unprocessedTallyDocument(
			enterprise_id=enterprise.id, user_id=uploaded_by, process=0)
		file_json = []
		if unprocessed_docs:
			for xml_file in unprocessed_docs:
				file_json.append({
					'file_name': xml_file.file_name, 'uploaded_on': xml_file.uploaded_on.strftime("%d-%m-%Y"),
					'file_id': xml_file.id})
		logger.info("File Details:%s" % file_json)
	except Exception as e:
		logger.exception("Fetch Unprocessed document %s" % e.message)
	return HttpResponse(content=simplejson.dumps(file_json), mimetype='application/json')


def unprocessXMLFilesDelete(request):
	try:
		request_handler = RequestHandler(request)
		enterprise = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise
		file_id = request_handler.getPostData("file_id")
		TallyService().deleteTallyDocument(enterprise_id=enterprise.id, file_id=file_id)
		response = True
	except Exception as e:
		logger.exception("Fetch Unprocessed document %s" % e.message)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def upload_blob(source_file_name, destination_blob_name):
	"""Uploads a file to the bucket."""
	blob = bucket.blob(destination_blob_name)
	blob.upload_from_filename(source_file_name)
	return True


def download_blob(source_blob_name, destination_file_name):
	"""

	:param bucket_name:
	:param source_blob_name:
	:param destination_file_name:
	:return:
	"""
	blob = bucket.blob(source_blob_name)
	blob.download_to_filename(destination_file_name)


def tallyXMLImportSave(request):
	try:
		tally_service = TallyService()
		tally_service.import_dao.db_session.begin(subtransactions=True)
		request_handler = RequestHandler(request)
		enterprise = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise
		uploaded_by = request_handler.getSessionAttribute(SESSION_KEY)
		uploaded_on = datetime.now()
		label = "Tally_XML_Attachment"

		for file_to_be_saved in request.FILES.getlist('tallyxmlfile[]'):
			logger.info("File to be Saved:%s" % file_to_be_saved)
			file_description = file_to_be_saved.name
			fil_ext = file_to_be_saved.name.split(".")[-1].lower()
			file_to_be_saved = file_to_be_saved.read()
			file_to_be_saved = file_to_be_saved.decode('8859')
			filename = "%s.%s" % (uuid.uuid4(), fil_ext)
			temp_file = "/site_media/tmp/%s" % filename
			writeFile(file_to_be_saved.encode('utf-8'), temp_file)
			upload_blob(source_file_name="%s/%s" % (TEMP_DIR, filename),
				destination_blob_name="{enterprise_id}/{filename}".format(enterprise_id=enterprise.id, filename=filename))
			attachment_id = saveAttachment(
				enterprise_id=enterprise.id, label=label, file_to_be_saved=filename, ext=fil_ext,
				uploaded_by=uploaded_by,
				uploaded_on=uploaded_on, db_session=tally_service.import_dao.db_session)
			import_documents = ImportDocuments(
				file_name=str(file_description), uploaded_by=uploaded_by, uploaded_on=uploaded_on,
				enterprise_id=enterprise.id, attachment_id=attachment_id)
			tally_service.import_dao.db_session.add(import_documents)
			logger.info("Tally XML Attachment imported successfully")
		tally_service.import_dao.db_session.commit()
		response = response_code.success()
	except Exception as e:
		logger.exception("Import data %s" % e)
		response = response_code.internalError()
	return HttpResponse(
		content=simplejson.dumps(response), mimetype='application/json')


def tallyXMLImport(request):
	conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
	processed_files = []
	try:
		request_handler = RequestHandler(request)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		cur = conn.cursor()
		enterprise = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise
		user = request_handler.getSessionAttribute(USER_IN_SESSION_KEY)
		opening_date = request_handler.getPostData("opening_date")
		service = ImportService(enterprise=enterprise)
		tally_service = TallyService()
		attachments = service.readImportedFiles(enterprise_id=enterprise.id)
		my_dict_unit = []
		my_dict_voucher_type = []
		my_dict_material = []
		my_dict_group = []
		my_dict_ledger = []
		my_dict_voucher = []
		for attachment in attachments:
			temp_filename = "/site_media/tmp/%s" % attachment.file
			gcs_temp_loc = getAbsolutePath(temp_filename)
			processed_files.append(temp_filename)
			logger.info("Tally XML Import: Processing file %s" % attachment.file)
			if not os.path.exists(getAbsolutePath(temp_filename)):
				download_blob(source_blob_name="{enterprise_id}/{filename}".format(enterprise_id=enterprise.id, filename=attachment.file), destination_file_name=gcs_temp_loc)
			xml_data = readFile(temp_filename)
			file_string = re.sub('&#[^>]+;', '', xml_data)
			writeFile(filter(lambda x: x in string.printable, file_string), temp_filename)
			tally_xml_file = file(getAbsolutePath(temp_filename))
			tally_xml_file.seek(0)
			parser = ElementTree.XMLParser(encoding="utf-8")
			tally_et = ElementTree.parse(tally_xml_file, parser=parser)
			my_dict_unit.extend([item for item in tally_et.iter('UNIT')])
			my_dict_voucher_type.extend([item for item in tally_et.iter('VOUCHERTYPE')])
			my_dict_material.extend([item for item in tally_et.iter('STOCKITEM')])
			my_dict_group.extend([item for item in tally_et.iter('GROUP')])
			my_dict_ledger.extend([item for item in tally_et.iter('LEDGER')])
			my_dict_voucher.extend([item for item in tally_et.iter('VOUCHER')])
		result_dict = []
		if my_dict_unit:
			import_unit = service.importUnits(enterprise_id=enterprise.id, units_dict=my_dict_unit, user=user_id)
			if not import_unit:
				tally_service.deleteTallyMessage(enterprise_id=enterprise.id, user_id=user_id)
				return HttpResponse(content=simplejson.dumps({
					'response': {'Message': 'Unit import Failed.. '}}), mimetype='application/json')

		if my_dict_material:
			import_category = service.importCategory(enterprise_id=enterprise.id, material_dict=my_dict_material,
			                                         user=user_id)
			if not import_category:
				tally_service.deleteTallyMessage(enterprise_id=enterprise.id, user_id=user_id)
				return HttpResponse(content=simplejson.dumps({'response': {
					'Message': 'Material Categories import Failed.. '}}), mimetype='application/json')
			grn_materials = service.importMaterial(enterprise_id=enterprise.id, my_dict=my_dict_material, user=user_id)
			if grn_materials:
				logger.info("Construct Receipts only for  the new materials")
				user_name = getUser(enterprise_id=enterprise.id, user_id=user_id)
				constructReceipt(
					grn_materials=grn_materials, db_session=service.dao.db_session, conn=conn, cur=cur,
					modifying_user=user_name,
					enterprise=enterprise, receipt_date=opening_date, import_type='XML')
		if my_dict_group:
			import_group = service.importGroup(enterprise_id=enterprise.id, my_dict=my_dict_group, user=user_id)
			if not import_group:
				tally_service.deleteTallyMessage(enterprise_id=enterprise.id, user_id=user_id)
				return HttpResponse(content=simplejson.dumps({'response': {
					'Message': 'Account Groups import Failed.. '}}), mimetype='application/json')
		if my_dict_ledger:
			result_dict = service.importLedger(
				enterprise_id=enterprise.id, my_dict=my_dict_ledger, user=user_id, enterprise=enterprise)
		service.refreshMasters(enterprise=enterprise)

		if result_dict:
			ledger_opening_balance_list = result_dict['ledger_opening_balance_list']
			bill_list = result_dict['bill_allocations_list']
			if len(ledger_opening_balance_list) > 0:
				voucher_id = __constructVoucher(
					enterprise=enterprise, user=user, db_session=service.dao.db_session,
					valid_items=ledger_opening_balance_list,
					voucher_date=opening_date)
				if len(bill_list) > 0:
					for bill, bill_details in bill_list.iteritems():
						service.insertOpeningLedgerBill(
							enterprise_id=enterprise.id, bill_no=bill_details['bill_no'], amount=bill_details['amount'],
							bill_date=bill_details['bill_date'], ledger_id=bill_details['ledger_id'],
							voucher_id=voucher_id)

		if my_dict_voucher:
			voucher_type_dict = service.getVoucherTypeDict(my_dict=my_dict_voucher_type)
			order = service.importOrders(
				enterprise=enterprise, my_dict=my_dict_voucher, user=user_id, voucher_type_dict=voucher_type_dict)
			service.refreshMasters(enterprise=enterprise)
			voucher_status = service.importVoucher(
				enterprise=enterprise, my_dict=my_dict_voucher, user=user_id, voucher_type_dict=voucher_type_dict)
			if not voucher_status:
				tally_service.deleteTallyMessage(enterprise_id=enterprise.id, user_id=user_id)
				return HttpResponse(content=simplejson.dumps({'response': {
					'Message': 'One or more ledger details not available., Please import all master details.. '}}),
					mimetype='application/json')
			service.importLedgerBills(
				enterprise=enterprise, my_dict=my_dict_voucher, user=user_id, bill_type="New Ref",
				voucher_type_dict=voucher_type_dict)
			service.importLedgerBills(
				enterprise=enterprise, my_dict=my_dict_voucher, user=user_id, bill_type="Advance",
				voucher_type_dict=voucher_type_dict)
			service.importLedgerBills(
				enterprise=enterprise, my_dict=my_dict_voucher, user=user_id, bill_type="Agst Ref",
				voucher_type_dict=voucher_type_dict)

		tally_service.updateLedgerBillNetValue(enterprise_id=enterprise.id)
		tally_service.updateLedgerBillIdsInReceipts(enterprise_id=enterprise.id)
		tally_service.updateLedgerBillIdsInInvoices(enterprise_id=enterprise.id)
		tally_service.processTallyDocument(enterprise_id=enterprise.id, user_id=user_id, process=1)

	except Exception as e:
		conn.rollback()
		logger.exception("Import data %s" % e.message)
	finally:
		conn.close()
		for temp_filename in processed_files:
			os.remove(helper.getAbsolutePath(temp_filename))
			logger.info("Removed temp file..  %s" % temp_filename)
	return HttpResponse(
		content=simplejson.dumps({'response': {'Message': 'Import Process Completed.\n'}}), mimetype='application/json')


def saveFileToDB(file_name, uploaded_by, uploaded_on, import_file, enterprise_id):
	logger.info("Inserting File into import_documents table")
	try:
		import_file = import_file.read()
		import_file = import_file.decode('8859')
		insert_blob_tuple = (file_name, uploaded_by, uploaded_on, import_file, enterprise_id)
		sql_insert_blob_query = """
		INSERT INTO import_documents (
			file_name, uploaded_by, uploaded_on, uploaded_file, enterprise_id) VALUES (
			'%s','%s','%s','%s','%s')""" % insert_blob_tuple
		executeQuery(sql_insert_blob_query)
	except Exception as e:
		logger.info("Failed inserting BLOB data into MySQL table {}".format(e))


def emailRequestFromUser(request):
	try:
		rh = RequestHandler(request)
		reason = rh.getPostData('reason')
		user_id = rh.getPostData('user_id')
		enterprise_id = rh.getPostData('enterprise_id')
		if not user_id:
			user_id = rh.getSessionAttribute(SESSION_KEY)
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		mail_request = {
			'erase_data': {
				'subject': '[Data Erase Request] %s (%s) - reg',
				'message': 'has requested to Erase their Data.',
				'response': 'Our support team will reach out to you within 2 working-days & enable you to erase your Enterprise Data with xserp, after moderating your request.'},
			'enterprise_plan': {
				'subject': '[Request] ENTERPRISE SUBSCRIPTION Plan %s (%s)',
				'message': 'has requested ENTERPRISE SUBSCRIPTION plan.',
				'response': 'Our support team will reach out to you within 2 working-days & help you to subscribe for ENTERPRISE SUBSCRIPTION.'},
			'trial_extension': {
				'subject': '[Request] Trial Extension %s (%s)',
				'message': 'has requested an extension of Trial period.',
				'response': 'Our support team will reach out to you within 2 working-days & help you with your request to extend your Trial Period.'},
			'grace_period': {
				'subject': '[Request] Grace Period to renew Subscription %s (%s)',
				'message': 'has requested to extend of Grace period to renew Subscription.',
				'response': 'Our support team will reach out to you within 2 working-days & will help you with your request to extend Grace period for Renewal.'}}

		if reason in mail_request:
			service = EnterpriseProfileService()
			result = service.emailRequestFromUser(user_id=user_id, enterprise_id=enterprise_id, **mail_request[reason])
			if reason in ('trial_extension', 'grace_period'):
				service.updateSubscriptionExtension(enterprise_id=enterprise_id, user_id=user_id)
				request.session[ENTERPRISE_SUBSCRIPTION] = LoginService().prepareEnterpriseSubscriptionInfo(
					enterprise_id=enterprise_id)
			response = response_code.success()
			response['custom_message'] = result

			# Force logout after payment has been successfully received.
			# rh.validateAndResetExpiredOnValue(enterprise_id=enterprise_id)
		else:
			response = response_code.failure()
			response['custom_message'] = "Please try after some time or contact support for further details!"
	except Exception as e:
		logger.exception("Sending Erase Data Request mail failed... %s" % e.message)
		response = response_code.internalError()
		response['custom_message'] = "Server error. Please contact Admin!"
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def validateExtensionRequest(request):
	try:
		rh = RequestHandler(request)
		reason = rh.getPostData('reason')
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		if reason in ('trial_extension', 'grace_period'):
			latest_subscription = SubscriptionService().getLatestSubscription(enterprise_id=enterprise_id)
			response = response_code.success()
			shall_enable_request_extension = True
			if latest_subscription.extension_requested_on is not None:
				shall_enable_request_extension = (datetime.now() - latest_subscription.extension_requested_on).days > 2
				response['requested_date'] = latest_subscription.extension_requested_on.strftime('%b %d, %Y')
			else:
				response['requested_date'] = None
			response['shall_enable_request_extension'] = shall_enable_request_extension
		else:
			response = response_code.failure()
			response['custom_message'] = "Please try after some time or contact support for further details!"
	except Exception as e:
		logger.exception("Validating Extension Request has been failed... %s" % e.message)
		response = response_code.internalError()
		response['custom_message'] = "Server error. Please contact Admin!"
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def gstAuthentication(request):
	"""

	:param request:
	:return:
	"""
	response = {}
	rh = RequestHandler(request)
	enterprise_id = rh.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	import requests
	try:
		gst_username = rh.getPostData('username')
		gst_password = rh.getPostData('password')
		query = """SELECT erd.details as gstin from \
						enterprise as e LEFT JOIN enterprise_registration_detail as erd ON erd.enterprise_id = e.id AND \
						erd.label='GSTIN' WHERE id= '%s' GROUP BY e.id""" % enterprise_id
		result = executeQuery(query, as_dict=True)
		gstin = result[0]['gstin'] if result[0]['gstin'] is not None and len(result[0]['gstin']) > 0 else None
		ip_addr = request.get_host()
		headers = {
			'username': str(gst_username), 'password': str(gst_password),
			'ip_address': ip_addr, 'client_id': GST_CLIENT_ID,
			'client_secret': GST_CLIENT_SECRET_KEY, 'gstin': gstin}
		url = 'https://api.mastergst.com/einvoice/authenticate?email=%s' % GST_EMAIL
		r = requests.get(url, headers=headers)
		logger.info("first json: %s" % r.json())
		if str(r.json()['status_cd']) == 'Sucess':
			cipher_suite = Fernet(CIPHER_KEY)
			ciphered_password = cipher_suite.encrypt(b"%s" % str(gst_password).strip()) #required to be bytes
			auth_token = r.json()['data']['AuthToken']
			query = """UPDATE enterprise SET gst_username='%s', gst_password='%s', gst_auth_token='%s' WHERE id='%s'""" % (str(gst_username).strip(), ciphered_password, str(auth_token),enterprise_id)
			executeQuery(query)
			response = {'result': 'success', 'error': ''}
		else:
			response = {'result': 'failure', 'error': r.json()['status_desc']}

	except Exception as e:
		logger.exception("Validating Extension Request has been failed... %s" % e.message)
		response = response_code.internalError()
		response['custom_message'] = "Server error. Please contact Admin!"
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getImportMessages(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		user_id = rh.getPostData('user_id')
		if not user_id:
			user_id = rh.getSessionAttribute(SESSION_KEY)
		if enterprise_id and user_id:
			tally_service = TallyService()
			data = tally_service.getTallyMessage(enterprise_id=enterprise_id, user_id=user_id)
			if data is None:
				response = response_code.failure()
			else:
				response = response_code.success()
				response['message_list'] = data
				tally_service.deleteTallyMessage(enterprise_id=enterprise_id, user_id=user_id)
		else:
			response = response_code.paramMissing()
			logger.error("Invalid params")
	except Exception as e:
		logger.exception("Could not get message list due to >>> %s" % e)
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def sendEnterpriseRegisteredMail(request):
	try:
		rh = RequestHandler(request)
		service = EnterpriseProfileService()
		user = rh.getSessionAttribute(USER_IN_SESSION_KEY)
		user_id = rh.getSessionAttribute(SESSION_KEY)
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		service.sendRegisteredIntimationMail(
			enterprise_email=user.email, first_name=user.first_name, last_name=user.last_name)
		response = response_code.success()

		# Updating changelog for invoice_template.
		db_session = SQLASession()
		invoice_template = db_session.query(InvoiceTemplateConfig).filter(
			InvoiceTemplateConfig.enterprise_id == enterprise_id).first()
		InvoiceTemplateChangelog().queryInsert(
			user_id=user_id, enterprise_id=enterprise_id, data=invoice_template, is_db_data=True)

	except Exception as e:
		logger.exception("Sending enterprise registration complete mail failed... %s" % e.message)
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def manageInvoiceTemplatePreview(request, invoice_template_service=None, invoice_template=None, message=""):
	request_handler = RequestHandler(request)
	service = EnterpriseProfileService()
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	db_session = SQLASession()
	enterprise = db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
	enterprise_image = db_session.query(EnterpriseImages).filter(
		EnterpriseImages.enterprise_id == enterprise_id).first()
	is_invoice_available = 0
	invoice = db_session.query(Invoice).filter(Invoice.enterprise_id == enterprise_id).first()
	config_info = invoice_template_service.invoice_dao.getInvoiceTemplateConfig(enterprise_id, 'Sales')
	if invoice:
		is_invoice_available = 1
	modified_details = db_session.query(InvoiceTemplateConfig).filter(
		InvoiceTemplateConfig.enterprise_id == enterprise_id).first()
	included_reg_items = json.loads(modified_details.template_header_details.included_reg_items) \
		if modified_details.template_header_details.included_reg_items else ""
	if enterprise_image and enterprise_image.logo and enterprise_image.logo != "":
		logo = "data:image/%s;base64,%s" % (enterprise_image.ext, base64.encodestring(enterprise_image.logo))
	else:
		logo = ""
	enterprise_contact_detail = service.getContactDetails(enterprise_id=enterprise_id, sequence_id=1)

	enterprise_registration_detail = db_session.query(EnterpriseRegistrationDetail).filter(
		EnterpriseRegistrationDetail.enterprise_id == enterprise_id).order_by(
		EnterpriseRegistrationDetail.label_id).all()

	header_image_size = 14
	footer_image_size = 14

	formatted_invoice_number = generateFormattedInvoiceNumber(
		inv_number_format=config_info.template_header_details.inv_number_format, enterprise_id=enterprise_id)
	if invoice_template_service:
		invoice_banner_image = invoice_template_service.invoice_dao.getInvoiceTemplateBannerDetails(
			config_id=modified_details.id)

		for image in invoice_banner_image:
			if image.section == 'Header':
				header_image_size = image.size

			if image.section == 'Footer':
				footer_image_size = image.size

	return TemplateResponse(
		template=MANAGE_INVOICE_TEMPLATE_NEW, request=request, context={
			'enterprise': enterprise, 'enterprise_logo': logo, 'enterprise_reg': enterprise_registration_detail,
			'modified_details': modified_details, 'is_invoice_available': is_invoice_available,
			INVOICE_CONFIG_TEMPLATE: invoice_template.invoice_template_config_form,
			INVOICE_GENERAL_TEMPLATE: invoice_template.invoice_template_generalconfig_form,
			INVOICE_HEADER_TEMPLATE: invoice_template.invoice_template_headerconfig_form,
			INVOICE_ITEM_TEMPLATE: invoice_template.invoice_template_itemsconfig_form,
			INVOICE_SUMMARY_TEMPLATE: invoice_template.invoice_template_summaryconfig_form,
			INVOICE_MISC_TEMPLATE: invoice_template.invoice_template_miscconfig_form,
			INVOICE_TEMPLATE_HEADER_BANNER: invoice_template.invoice_template_header_banner_formset,
			INVOICE_TEMPLATE_FOOTER_BANNER: invoice_template.invoice_template_footer_banner_formset,
			ENTERPRISE_CONTACT_DETAIL: enterprise_contact_detail, 'included_reg_items': included_reg_items,
			'formatted_invoice_number': formatted_invoice_number,
			'header_image_size': header_image_size, 'footer_image_size': footer_image_size,
			TEMPLATE_TITLE_KEY: "Invoice Template", 'save_invoice_template': message if message else ""})


def manageInvoiceTemplate(request):
	"""
	Renders a view to manage User profiles - displays all the Users by default

	:param request:
	:return:
	"""
	invoice_template_service = TemplateConfigService()
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	logger.info('Rendering Invoice Template view...')
	config_info = invoice_template_service.invoice_dao.getInvoiceTemplateConfig(enterprise_id, 'Sales')
	save_invoice_template = request_handler.getSessionAttribute("save_invoice_template")
	request_handler.removeSessionAttribute("save_invoice_template")
	tab_to_be_retained = request_handler.getSessionAttribute("tab_to_be_retained")
	request_handler.removeSessionAttribute("tab_to_be_retained")

	invoice_template = invoice_template_service.constructTemplateConfigVo(
		config_info, tab_to_be_retained=tab_to_be_retained)
	make_transient(config_info)
	return manageInvoiceTemplatePreview(
		request, invoice_template_service=invoice_template_service, invoice_template=invoice_template,
		message=save_invoice_template)


def manageSalesEstimateTemplate(request):
	"""
	Renders a view to manage Sales Estimate Template Config - displays all the PO Configurations.

	:param request:
	:return:
	"""
	service = EnterpriseProfileService()
	se_template_service = PurchaseTemplateConfigService()
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	db_session = SQLASession()
	enterprise = db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
	enterprise_image = db_session.query(EnterpriseImages).filter(
		EnterpriseImages.enterprise_id == enterprise_id).first()
	is_se_available = 0
	sales_estimate = db_session.query(SalesEstimate).filter(SalesEstimate.enterprise_id == enterprise_id).first()
	logger.info('Rendering Sales Estimate Template view...')
	template_config = se_template_service.purchase_template_dao.getTemplateConfig(
		enterprise_id=enterprise_id, collection='sales_estimate_template_config')
	if sales_estimate:
		is_se_available = 1
	if enterprise_image and enterprise_image.logo and enterprise_image.logo != "":
		logo = "data:image/%s;base64,%s" % (enterprise_image.ext, base64.encodestring(enterprise_image.logo))
	else:
		logo = ""
	enterprise_contact_detail = service.getContactDetails(enterprise_id=enterprise_id, sequence_id=1)
	enterprise_registration_detail = db_session.query(EnterpriseRegistrationDetail).filter(
		EnterpriseRegistrationDetail.enterprise_id == enterprise_id).order_by(
		EnterpriseRegistrationDetail.label_id).all()
	tax_type = [(1, 'Columns'), (2, 'Consolidated Summary'), (3, 'Part of Item Details'),
				(4, 'Rate & Amount in one Column')]
	item_sort_order = [(1, 'Order of Entry'), (2, 'Item Name'), (3, 'Item Code/ Drawing No')]
	template_general_details = template_config["general_config"]
	template_header_details = template_config["header_config"]
	template_item_details = template_config["items_config"]
	template_summary_details = template_config["summary_config"]
	template_misc_details = template_config["misc_config"]
	template_last_modify_on = template_config["last_modified_on"]
	template_id = template_config["template_id"] if "template_id" in template_config else 1
	print_template_available = 0 if template_config["print_template"] == " " else 1
	template_last_modified_by = getUser(
		enterprise_id=enterprise_id,
		user_id=int(template_config["last_modified_by"]) if template_config["last_modified_by"] != '' else '')
	included_reg_items = template_header_details["include_reg_items"] if template_header_details[
		"include_reg_items"] else ""
	header_image_height = 14
	footer_image_height = 14
	template_header_banner = []
	template_footer_banner = []
	if template_misc_details["banner_header"]["height"] and template_misc_details["banner_header"]["height"] != "":
		header_image_height = template_misc_details["banner_header"]["height"]
	if template_misc_details["banner_footer"]["height"] and template_misc_details["banner_footer"]["height"] != "":
		footer_image_height = template_misc_details["banner_footer"]["height"]

	for attachment in template_misc_details["banner_header"]["attachment"]:
		banner_image, attachments = se_template_service.purchase_template_dao.getBannerAttachment(
			attachment_id=attachment["attachment_id"], enterprise_id=enterprise_id)
		template_header_banner.append({
			"enterprise_id": enterprise_id, "position": attachment["position"],
			"width": attachment["width"], "banner_image": "data:image/%s;base64,%s" % (
				attachments.file_ext, banner_image.encode('base64')) if banner_image != "" else ""})
	for attachment in template_misc_details["banner_footer"]["attachment"]:
		banner_image, attachments = se_template_service.purchase_template_dao.getBannerAttachment(
			attachment_id=attachment["attachment_id"], enterprise_id=enterprise_id)
		template_footer_banner.append({
			"enterprise_id": enterprise_id, "position": attachment["position"],
			"width": attachment["width"], "banner_image": "data:image/%s;base64,%s" % (
				attachments.file_ext, banner_image.encode('base64')) if banner_image != "" else ""})

	return TemplateResponse(template=MANAGE_SALES_ESTIMATE_TEMPLATE_CONFIG, request=request, context={
		'enterprise': enterprise, 'enterprise_logo': logo, 'enterprise_reg': enterprise_registration_detail,
		'modified_on': template_last_modify_on, 'modified_by': template_last_modified_by,
		'is_po_available': is_se_available, 'tax_type': tax_type, 'item_sort_order': item_sort_order,
		PO_GENERAL_TEMPLATE: template_general_details, PO_HEADER_TEMPLATE: template_header_details,
		PO_ITEM_TEMPLATE: template_item_details, PO_SUMMARY_TEMPLATE: template_summary_details,
		PO_MISC_TEMPLATE: template_misc_details, ENTERPRISE_CONTACT_DETAIL: enterprise_contact_detail,
		'included_reg_items': included_reg_items, 'header_image_height': header_image_height,
		'footer_image_height': footer_image_height, 'base_fonts': populatePurchaseTemplateBaseFont(),
		'template_header_banner': template_header_banner, 'template_footer_banner': template_footer_banner,
		'print_template_available': print_template_available,
		'template_id': template_id,
		TEMPLATE_TITLE_KEY: "Sales Estimate Template", 'save_se_template': "Configuration saved successfully"
	})

def manageOATemplate(request):
	"""
	Renders a view to manage OA Template Config - displays all the PO Configurations.

	:param request:
	:return:
	"""
	service = EnterpriseProfileService()
	se_template_service = PurchaseTemplateConfigService()
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	db_session = SQLASession()
	enterprise = db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
	enterprise_image = db_session.query(EnterpriseImages).filter(
		EnterpriseImages.enterprise_id == enterprise_id).first()
	is_se_available = 0
	sales_estimate = db_session.query(OA).filter(OA.enterprise_id == enterprise_id).first()
	logger.info('Rendering OA Template view...')
	template_config = se_template_service.purchase_template_dao.getTemplateConfig(
		enterprise_id=enterprise_id, collection='order_acknowledgement_template_config')
	if sales_estimate:
		is_se_available = 1
	if enterprise_image and enterprise_image.logo and enterprise_image.logo != "":
		logo = "data:image/%s;base64,%s" % (enterprise_image.ext, base64.encodestring(enterprise_image.logo))
	else:
		logo = ""
	enterprise_contact_detail = service.getContactDetails(enterprise_id=enterprise_id, sequence_id=1)
	enterprise_registration_detail = db_session.query(EnterpriseRegistrationDetail).filter(
		EnterpriseRegistrationDetail.enterprise_id == enterprise_id).order_by(
		EnterpriseRegistrationDetail.label_id).all()
	tax_type = [(1, 'Columns'), (2, 'Consolidated Summary'), (3, 'Part of Item Details'),
	            (4, 'Rate & Amount in one Column')]
	item_sort_order = [(1, 'Order of Entry'), (2, 'Item Name'), (3, 'Item Code/ Drawing No')]
	template_general_details = template_config["general_config"]
	template_header_details = template_config["header_config"]
	template_item_details = template_config["items_config"]
	template_summary_details = template_config["summary_config"]
	template_misc_details = template_config["misc_config"]
	template_last_modify_on = template_config["last_modified_on"]
	template_id = template_config["template_id"] if "template_id" in template_config else 1
	print_template_available = 0 if template_config["print_template"] == " " else 1
	template_last_modified_by = getUser(
		enterprise_id=enterprise_id,
		user_id=int(template_config["last_modified_by"]) if template_config["last_modified_by"] != '' else '')
	included_reg_items = template_header_details["include_reg_items"] if template_header_details[
		"include_reg_items"] else ""
	header_image_height = 14
	footer_image_height = 14
	template_header_banner = []
	template_footer_banner = []
	if template_misc_details["banner_header"]["height"] and template_misc_details["banner_header"]["height"] != "":
		header_image_height = template_misc_details["banner_header"]["height"]
	if template_misc_details["banner_footer"]["height"] and template_misc_details["banner_footer"]["height"] != "":
		footer_image_height = template_misc_details["banner_footer"]["height"]

	for attachment in template_misc_details["banner_header"]["attachment"]:
		banner_image, attachments = se_template_service.purchase_template_dao.getBannerAttachment(
			attachment_id=attachment["attachment_id"], enterprise_id=enterprise_id)
		template_header_banner.append({
			"enterprise_id": enterprise_id, "position": attachment["position"],
			"width": attachment["width"], "banner_image": "data:image/%s;base64,%s" % (
				attachments.file_ext, banner_image.encode('base64')) if banner_image != "" else ""})
	for attachment in template_misc_details["banner_footer"]["attachment"]:
		banner_image, attachments = se_template_service.purchase_template_dao.getBannerAttachment(
			attachment_id=attachment["attachment_id"], enterprise_id=enterprise_id)
		template_footer_banner.append({
			"enterprise_id": enterprise_id, "position": attachment["position"],
			"width": attachment["width"], "banner_image": "data:image/%s;base64,%s" % (
				attachments.file_ext, banner_image.encode('base64')) if banner_image != "" else ""})

	return TemplateResponse(template=MANAGE_OA_TEMPLATE_CONFIG, request=request, context={
		'enterprise': enterprise, 'enterprise_logo': logo, 'enterprise_reg': enterprise_registration_detail,
		'modified_on': template_last_modify_on, 'modified_by': template_last_modified_by,
		'is_po_available': is_se_available, 'tax_type': tax_type, 'item_sort_order': item_sort_order,
		PO_GENERAL_TEMPLATE: template_general_details, PO_HEADER_TEMPLATE: template_header_details,
		PO_ITEM_TEMPLATE: template_item_details, PO_SUMMARY_TEMPLATE: template_summary_details,
		PO_MISC_TEMPLATE: template_misc_details, ENTERPRISE_CONTACT_DETAIL: enterprise_contact_detail,
		'included_reg_items': included_reg_items, 'header_image_height': header_image_height,
		'footer_image_height': footer_image_height, 'base_fonts': populatePurchaseTemplateBaseFont(),
		'template_header_banner': template_header_banner, 'template_footer_banner': template_footer_banner,
		'print_template_available': print_template_available,
		'template_id': template_id,
		TEMPLATE_TITLE_KEY: "Order Acknowledgement Template", 'save_oa_template': "Configuration saved successfully"
	})


def getInvoiceTemplateLogList(request):
	"""
	get Specified voucher log list
	:return:
	"""
	response = {}
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	offset = request_handler.getPostData('offset')
	limit = request_handler.getPostData('limit')
	try:
		response = InvoiceTemplateChangelog().fetchLogList(
			eid=enterprise_id, enterprise_id=enterprise_id, offset=offset, limit=limit)
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getInvoiceTemplateLogData(request):
	"""
	get changelog data from a specified voucher timeset
	:param request:
	:return:
	"""
	response = {}
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	modified_at = request_handler.getPostData('modified_at')
	try:
		response = InvoiceTemplateChangelog().fetchLogData(eid=enterprise_id, enterprise_id=enterprise_id, modified_at=modified_at)
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def saveInvoiceTemplate(request):
	"""
	Saves the edited Invoice template config and renders the manage view on successful save.

	:param request:
	:return:
	"""
	template_service = TemplateConfigService()
	logger.info('Saving a Template general config...')
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	last_modified_on = datetime.now()
	invoice_template_vo = InvoiceTemplateVO(invoice_template_config_form=InvoiceTemplateConfigForm(
		data=request_handler.getPostData(), prefix=IT_CONFIG_FORM_PREFIX),
		invoice_template_generalconfig_form=InvoiceTemplateGeneralConfigForm(
			request_handler.getPostData(), prefix=IT_GENERAL_FORM_PREFIX),
		invoice_template_headerconfig_form=InvoiceTemplateHeaderConfigForm(
			request_handler.getPostData(), prefix=IT_HEADER_FORM_PREFIX),
		invoice_template_itemsconfig_form=InvoiceTemplateItemsConfigForm(
			request_handler.getPostData(), prefix=IT_ITEMS_FORM_PREFIX),
		invoice_template_summaryconfig_form=InvoiceTemplateSummaryConfigForm(
			request_handler.getPostData(), prefix=IT_SUMMARY_FORM_PREFIX),
		invoice_template_miscconfig_form=InvoiceTemplateMiscConfigForm(
			request_handler.getPostData(), prefix=IT_MISC_FORM_PREFIX),
		invoice_template_header_banner_formset=InvoiceTemplateHeaderBannerFormset(
			request_handler.getPostData(), prefix=IT_HEADER_BANNER_FORM_PREFIX),
		invoice_template_footer_banner_formset=InvoiceTemplateFooterBannerFormset(
			request_handler.getPostData(), prefix=IT_FOOTER_BANNER_FORM_PREFIX))

	invoice_template = template_service.saveTemplateConfig(
		invoice_template_vo=invoice_template_vo, enterprise_id=enterprise_id, user_id=user_id,
		last_modified_on=last_modified_on)

	if not invoice_template.is_valid():
		return manageInvoiceTemplatePreview(request, invoice_template=invoice_template, message="error")
	request_handler.setSessionAttribute("save_invoice_template", "Configuration saved successfully")
	request_handler.setSessionAttribute(
		"tab_to_be_retained", invoice_template_vo.invoice_template_generalconfig_form["tab_retain"].value())
	return HttpResponseRedirect(properties.INVOICE_TEMPLATE_URL)


def previewInvoiceTemplate(request):
	"""
	Saves the edited Invoice template config and renders the manage view on successful save.

	:param request:
	:return:
	"""
	invoice_id = None
	try:
		logger.info('Previwing a Template general config...')
		request_handler = RequestHandler(request)
		invoice_template_service = TemplateConfigService()
		user = request_handler.getSessionAttribute(USER_IN_SESSION_KEY)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		invoice_type = ('GST', 'Trading', 'Service', 'BoS', 'Excise')
		invoice_id = executeQuery(
			"""SELECT a.id as invoice_id FROM invoice as a, enterprise as b WHERE b.id = a.enterprise_id and b.id = %s 
			and a.type in %s ORDER BY a.id DESC LIMIT 1""" % (enterprise_id, invoice_type), as_dict=True)
		invoice_id = invoice_id[0]["invoice_id"]
		invoice_template_vo = InvoiceTemplateVO(invoice_template_config_form=InvoiceTemplateConfigForm(
			data=request_handler.getPostData(), prefix=IT_CONFIG_FORM_PREFIX),
			invoice_template_generalconfig_form=InvoiceTemplateGeneralConfigForm(
				request_handler.getPostData(), prefix=IT_GENERAL_FORM_PREFIX),
			invoice_template_headerconfig_form=InvoiceTemplateHeaderConfigForm(
				request_handler.getPostData(), prefix=IT_HEADER_FORM_PREFIX),
			invoice_template_itemsconfig_form=InvoiceTemplateItemsConfigForm(
				request_handler.getPostData(), prefix=IT_ITEMS_FORM_PREFIX),
			invoice_template_summaryconfig_form=InvoiceTemplateSummaryConfigForm(
				request_handler.getPostData(), prefix=IT_SUMMARY_FORM_PREFIX),
			invoice_template_miscconfig_form=InvoiceTemplateMiscConfigForm(
				request_handler.getPostData(), prefix=IT_MISC_FORM_PREFIX),
			invoice_template_header_banner_formset=InvoiceTemplateHeaderBannerFormset(
				request_handler.getPostData(), prefix=IT_HEADER_BANNER_FORM_PREFIX),
			invoice_template_footer_banner_formset=InvoiceTemplateFooterBannerFormset(
				request_handler.getPostData(), prefix=IT_FOOTER_BANNER_FORM_PREFIX))
		if invoice_template_vo.is_valid():
			response = response_code.success()
			db_session = invoice_template_service.invoice_dao.db_session
			db_session.begin(subtransactions=True)
			try:
				updated_banner_image = []
				template_config = invoice_template_service.invoice_dao.getInvoiceTemplateConfig(
					enterprise_id=enterprise_id, module='Sales')
				template_to_be_previewed = invoice_template_service.copyInvoiceTemplateVOToEntity(
					updated_banner_image, invoice_template_vo=invoice_template_vo, entity=template_config,
					is_pdf_preview=True)
				preview_documents = InvoiceService().generatePreviewInvoiceDocument(
					invoice_id=invoice_id, user=user, inv_config_details=template_to_be_previewed,
					updated_banner_image=updated_banner_image, db_session=db_session, enterprise_id=enterprise_id)
				single_page_data = preview_documents[0]
				multi_page_data = preview_documents[1]

				response['ext'] = "pdf"
				response['single_page_data'] = "data:application/pdf;base64,%s" % single_page_data.encode('base64')
				response['multi_page_data'] = "data:application/pdf;base64,%s" % multi_page_data.encode('base64')
			except Exception as e:
				logger.info("Problem with Invoice Preview generation - %s" % e.message)
			finally:
				db_session.rollback()
			return HttpResponse(json.dumps(response), 'content-type=text/json')
		else:
			return manageInvoiceTemplatePreview(
				request, invoice_template_service=invoice_template_service, invoice_template=invoice_template_vo,
				message="error")
	except Exception as e:
		logger.exception("Download invoice document failed for id %s. %s" % (invoice_id, e.message))
		response = response_code.internalError()
		response['error'] = '%s' % e.message
		return HttpResponse(json.dumps(response), 'content-type=text/json')


def erase_enterprise_data(request):
	"""

	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		erase_type = request_handler.getPostData('erase_type')
		response = response_code.success()
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		enterprise_service = EnterpriseProfileService()
		enterprise_service.deleteExpenseParticularsFromFTP(enterprise_id=enterprise_id)
		enterprise_service.deleteAttachmentsFromFTP(enterprise_id=enterprise_id)
		enterprise_service.deleteEnterpriseTransactions(enterprise_id=enterprise_id)
		message = "All transactions are cleared"
		response['custom_message'] = message
		push_notification(enterprise_id=enterprise_id, sender_id=user_id, message=message, include_sender=True)
		if erase_type == "Reset":
			enterprise_service.resetEnterprise(enterprise_id=enterprise_id)
			enterprise_service.profile_dao.db_session.begin(subtransactions=True)
			invoice_template_config = InvoiceTemplateConfig(
				enterprise_id=enterprise_id, module='Sales', template_id=2, last_modified_by=user_id,
				last_modified_on=datetime.now())
			enterprise_service.profile_dao.db_session.add(invoice_template_config)
			enterprise_service.profile_dao.db_session.commit()

			response['custom_message'] = "Reset Enterprise completed."
		enterprise_service.saveEraseLog(
			enterprise_id=enterprise_id, erase_type=erase_type, user_id=user_id, erased_on=datetime.now())
	except Exception as e:
		logger.exception("Failed to erase enterprise data. %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def validateInvoiceNumberFormat(request):
	logger.info("Validation of invoice number format has been Triggered...")
	try:
		request_handler = RequestHandler(request)
		inv_number_format = request_handler.getPostData("number_format")
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		formatted_invoice_number = generateFormattedInvoiceNumber(inv_number_format=inv_number_format, enterprise_id=enterprise_id)
		if formatted_invoice_number != "":
			response = response_code.success()
			response['formated_invoice_number'] = formatted_invoice_number
		else:
			response = response_code.failure()
			response['formated_invoice_number'] = ''

	except Exception as e:
		logger.exception("Validation of invoice number format is failed - %s" % e.message)
		response = response_code.internalError()
		response['exception'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def generateFormattedInvoiceNumber(inv_number_format=None, enterprise_id=None):
	try:
		valid_format = False
		formatted_invoice_number = ""
		if "{number:" in inv_number_format:
			valid_format = True

		if valid_format is True:
			invoice = executeQuery(
				"""SELECT a.invoice_no as invoice_no, a.type as type, a.financial_year as fy, a.sub_number as sub_number 
				FROM invoice as a, enterprise as b WHERE b.id = a.enterprise_id and b.id = %s and a.status > 0 
				ORDER BY a.last_modified_on DESC LIMIT 1""" % enterprise_id, as_dict=True)
			if len(invoice) > 0:
				inv_type = invoice[0]["type"]
				fy = invoice[0]["fy"]
				sub_number = invoice[0]["sub_number"]
				inv_no = invoice[0]["invoice_no"]
			else:
				inv_type = "SALES"
				fy = "20-21"
				sub_number = ""
				inv_no = 1
			formatted_invoice_number = inv_number_format.format(type=inv_type, fy=fy, number=int(inv_no), sub_number=sub_number if sub_number is not None else "")
	except Exception as e:
		logger.exception("Validation of invoice number format is failed - %s" % e.message)
		formatted_invoice_number = ""
	return formatted_invoice_number


def managePurchaseTemplate(request):
	"""
	Renders a view to manage Purchase Template Config - displays all the PO Configurations.

	:param request:
	:return:
	"""
	service = EnterpriseProfileService()
	po_template_service = PurchaseTemplateConfigService()
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	db_session = SQLASession()
	enterprise = db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
	enterprise_image = db_session.query(EnterpriseImages).filter(
		EnterpriseImages.enterprise_id == enterprise_id).first()
	is_po_available = 0
	purchase_order = db_session.query(PurchaseOrder).filter(PurchaseOrder.enterprise_id == enterprise_id).first()
	logger.info('Rendering Purchase Template view...')
	template_config = po_template_service.purchase_template_dao.getTemplateConfig(
		enterprise_id=enterprise_id, collection='purchase_template_config')
	if purchase_order:
		is_po_available = 1

	if enterprise_image and enterprise_image.logo and enterprise_image.logo != "":
		logo = "data:image/%s;base64,%s" % (enterprise_image.ext, base64.encodestring(enterprise_image.logo))
	else:
		logo = ""
	enterprise_contact_detail = service.getContactDetails(enterprise_id=enterprise_id, sequence_id=1)

	enterprise_registration_detail = db_session.query(EnterpriseRegistrationDetail).filter(
		EnterpriseRegistrationDetail.enterprise_id == enterprise_id).order_by(
		EnterpriseRegistrationDetail.label_id).all()
	tax_type = [(1, 'Columns'), (2, 'Consolidated Summary'), (3, 'Part of Item Details'), (4, 'Rate & Amount in one Column')]
	item_sort_order = [(1, 'Order of Entry'), (2, 'Item Name'), (3, 'Item Code/ Drawing No')]

	template_general_details = template_config["general_config"]
	template_header_details = template_config["header_config"]
	template_item_details = template_config["items_config"]
	template_summary_details = template_config["summary_config"]
	template_misc_details = template_config["misc_config"]
	template_last_modify_on = template_config["last_modified_on"]
	template_id = template_config["template_id"]
	print_template_available = 0 if template_config["print_template"] == " " else 1
	template_last_modified_by = getUser(
		enterprise_id=enterprise_id,
		user_id=int(template_config["last_modified_by"]) if template_config["last_modified_by"] != '' else '')

	included_reg_items = template_header_details["include_reg_items"] if template_header_details["include_reg_items"] else ""

	header_image_height = 14
	footer_image_height = 14
	template_header_banner = []
	template_footer_banner = []

	if template_misc_details["banner_header"]["height"] and template_misc_details["banner_header"]["height"] != "":
		header_image_height = template_misc_details["banner_header"]["height"]
	if template_misc_details["banner_footer"]["height"] and template_misc_details["banner_footer"]["height"] != "":
		footer_image_height = template_misc_details["banner_footer"]["height"]

	for attachment in template_misc_details["banner_header"]["attachment"]:
		banner_image, attachments = po_template_service.purchase_template_dao.getBannerAttachment(
								attachment_id=attachment["attachment_id"], enterprise_id=enterprise_id)
		template_header_banner.append({
				"enterprise_id": enterprise_id, "position": attachment["position"],
				"width": attachment["width"], "banner_image": "data:image/%s;base64,%s" % (
					attachments.file_ext, banner_image.encode('base64')) if banner_image != "" else ""})

	for attachment in template_misc_details["banner_footer"]["attachment"]:
		banner_image, attachments = po_template_service.purchase_template_dao.getBannerAttachment(
								attachment_id=attachment["attachment_id"], enterprise_id=enterprise_id)
		template_footer_banner.append({
				"enterprise_id": enterprise_id, "position": attachment["position"],
				"width": attachment["width"], "banner_image": "data:image/%s;base64,%s" % (
					attachments.file_ext, banner_image.encode('base64')) if banner_image != "" else ""})

	return TemplateResponse(template=MANAGE_PO_TEMPLATE, request=request, context={
		'enterprise': enterprise, 'enterprise_logo': logo, 'enterprise_reg': enterprise_registration_detail,
		'modified_on': template_last_modify_on, 'modified_by': template_last_modified_by,
		'is_po_available': is_po_available, 'tax_type': tax_type, 'item_sort_order': item_sort_order,
		PO_GENERAL_TEMPLATE: template_general_details, PO_HEADER_TEMPLATE: template_header_details,
		PO_ITEM_TEMPLATE: template_item_details, PO_SUMMARY_TEMPLATE: template_summary_details,
		PO_MISC_TEMPLATE: template_misc_details, ENTERPRISE_CONTACT_DETAIL: enterprise_contact_detail,
		'included_reg_items': included_reg_items, 'header_image_height': header_image_height,
		'footer_image_height': footer_image_height, 'base_fonts': populatePurchaseTemplateBaseFont(),
		'template_header_banner': template_header_banner, 'template_footer_banner': template_footer_banner,
		'print_template_available': print_template_available,
		'template_id' : template_id,
		TEMPLATE_TITLE_KEY: "Purchase Template", 'save_po_template': "Configuration saved successfully"
	})


def savePurchaseTemplate(request):
	"""
	Saves the edited Invoice template config and renders the manage view on successful save.

	:param request:
	:return:
	"""
	try:
		po_template_service = PurchaseTemplateConfigService()
		logger.info('Saving a Purchase Template config...')
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		last_modified_on = datetime.now()
		po_template_config = json.loads(request_handler.getPostData("data"))
		header_banner = json.loads(request_handler.getPostData("header_banner"))
		footer_banner = json.loads(request_handler.getPostData("footer_banner"))
		response = response_code.success()

		po_template_service.savePurchaseTemplateConfig(
			po_template_config=po_template_config, enterprise_id=enterprise_id, user_id=user_id,
			last_modified_on=last_modified_on, header_banner=header_banner, footer_banner=footer_banner)

		request_handler.setSessionAttribute("save_purchase_template", "Configuration saved successfully")
		response['custom_message'] = "Purchase Configuration saved successfully"
	except Exception as e:
		logger.exception("Purchase  Configuration Update failed - %s" % e.message)
		response = response_code.internalError()
		response['custom_message'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def previewPurchaseTemplate(request):
	"""
	Saves the edited Purchase template config and renders the manage view on successful save.

	:param request:
	:return:
	"""
	purchase_id = None
	try:
		logger.info('Previewing a Purchase Template config...')
		request_handler = RequestHandler(request)
		po_template_service = PurchaseTemplateConfigService()
		user = request_handler.getSessionAttribute(USER_IN_SESSION_KEY)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		po_template_config = json.loads(request_handler.getPostData("data"))
		header_banner = json.loads(request_handler.getPostData("header_banner"))
		footer_banner = json.loads(request_handler.getPostData("footer_banner"))
		purchase_id = executeQuery(
			"""SELECT a.id as purchase_id FROM purchase_order as a, enterprise as b WHERE b.id = a.enterprise_id and b.id = %s  
			ORDER BY a.id DESC LIMIT 1""" % enterprise_id, as_dict=True)
		purchase_id = purchase_id[0]["purchase_id"]
		response = response_code.success()
		db_session = po_template_service.purchase_template_dao.db_session
		db_session.begin(subtransactions=True)
		try:
			updated_banner_image = []

			po_template_service.purchase_template_dao.updateTemplateBannerImages(
				updated_banner_image, enterprise_id=enterprise_id, template_header_banner=header_banner,
				template_footer_banner=footer_banner, is_pdf_preview=True, db_session=db_session,
				collection='purchase_template_config')
			preview_documents = PurchaseService().generatePreviewPurchaseDocument(
				po_id=purchase_id, user=user, po_config_details=po_template_config,
				updated_banner_image=updated_banner_image, db_session=db_session, enterprise_id=enterprise_id)
			single_page_data = preview_documents[0]
			multi_page_data = preview_documents[1]

			response['ext'] = "pdf"
			response['single_page_data'] = "data:application/pdf;base64,%s" % single_page_data.encode('base64')
			response['multi_page_data'] = "data:application/pdf;base64,%s" % multi_page_data.encode('base64')
		except Exception as e:
			logger.info("Problem with Purchase Preview generation - %s" % e.message)
		finally:
			db_session.rollback()
		return HttpResponse(json.dumps(response), 'content-type=text/json')
	except Exception as e:
		logger.exception("Download purchase document failed for id %s. %s" % (purchase_id, e.message))
		response = response_code.internalError()
		response['error'] = '%s' % e.message
		return HttpResponse(json.dumps(response), 'content-type=text/json')


def getPurchaseTemplateLogList(request):
	"""
	get Specified voucher log list
	:return:
	"""
	response = {}
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	offset = request_handler.getPostData('offset')
	limit = request_handler.getPostData('limit')
	try:
		response = PurchaseTemplateChangelog().fetchLogList(
			eid=enterprise_id, enterprise_id=enterprise_id, offset=offset, limit=limit)
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getPurchaseTemplateLogData(request):
	"""
	get changelog data from a specified voucher timeset
	:param request:
	:return:
	"""
	response = {}
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	modified_at = request_handler.getPostData('modified_at')
	try:
		response = PurchaseTemplateChangelog().fetchLogData(eid=enterprise_id, enterprise_id=enterprise_id, modified_at=modified_at)
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def saveSalesEstimationTemplate(request):
	"""
	Saves the edited Invoice template config and renders the manage view on successful save.

	:param request:
	:return:
	"""
	try:
		se_template_service = PurchaseTemplateConfigService()
		logger.info('Saving a Sales Estimate Template config...')
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		last_modified_on = datetime.now()
		template_config = json.loads(request_handler.getPostData("data"))
		header_banner = json.loads(request_handler.getPostData("header_banner"))
		footer_banner = json.loads(request_handler.getPostData("footer_banner"))
		response = response_code.success()

		se_template_service.saveSalesEstimateTemplateConfig(
			template_config=template_config, enterprise_id=enterprise_id, user_id=user_id,
			last_modified_on=last_modified_on, header_banner=header_banner, footer_banner=footer_banner)

		request_handler.setSessionAttribute("save_sales_estimate_template", "Configuration saved successfully")
		response['custom_message'] = "Sales Estimate Configuration saved successfully"
	except Exception as e:
		logger.exception("Sales Estimate  Configuration Update failed - %s" % e.message)
		response = response_code.internalError()
		response['custom_message'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def previewSalesEstimationTemplate(request):
	"""
	Saves the edited Purchase template config and renders the manage view on successful save.

	:param request:
	:return:
	"""
	se_id = None
	try:
		logger.info('Previewing a Sales Estimate Template config...')
		request_handler = RequestHandler(request)
		se_template_service = PurchaseTemplateConfigService()
		user = request_handler.getSessionAttribute(USER_IN_SESSION_KEY)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		se_template_config = json.loads(request_handler.getPostData("data"))
		header_banner = json.loads(request_handler.getPostData("header_banner"))
		footer_banner = json.loads(request_handler.getPostData("footer_banner"))
		se_id = executeQuery(
			"""SELECT a.id as se_id FROM sales_estimate as a, enterprise as b WHERE b.id = a.enterprise_id and b.id = %s  
			ORDER BY a.id DESC LIMIT 1""" % enterprise_id, as_dict=True)
		se_id = se_id[0]["se_id"]
		response = response_code.success()
		db_session = se_template_service.purchase_template_dao.db_session
		db_session.begin(subtransactions=True)
		try:
			updated_banner_image = []

			se_template_service.purchase_template_dao.updateTemplateBannerImages(
				updated_banner_image, enterprise_id=enterprise_id, template_header_banner=header_banner,
				template_footer_banner=footer_banner, is_pdf_preview=True, db_session=db_session,
				collection='sales_estimate_template_config')
			preview_documents = PurchaseService().generatePreviewSEDocument(
				se_id=se_id, user=user, se_config_details=se_template_config,
				updated_banner_image=updated_banner_image, db_session=db_session, enterprise_id=enterprise_id)
			single_page_data = preview_documents[0]
			multi_page_data = preview_documents[1]

			response['ext'] = "pdf"
			response['single_page_data'] = "data:application/pdf;base64,%s" % single_page_data.encode('base64')
			response['multi_page_data'] = "data:application/pdf;base64,%s" % multi_page_data.encode('base64')
		except Exception as e:
			logger.info("Problem with sales estimate Preview generation - %s" % e.message)
		finally:
			db_session.rollback()
		return HttpResponse(json.dumps(response), 'content-type=text/json')
	except Exception as e:
		logger.exception("Download sales estimate document failed for id %s. %s" % (se_id, e.message))
		response = response_code.internalError()
		response['error'] = '%s' % e.message
		return HttpResponse(json.dumps(response), 'content-type=text/json')


def saveOrderAcknowledgementTemplate(request):
	"""
	Saves the edited Invoice template config and renders the manage view on successful save.

	:param request:
	:return:
	"""
	try:
		oa_template_service = PurchaseTemplateConfigService()
		logger.info('Saving a Order Acknowledgement Template config...')
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		last_modified_on = datetime.now()
		template_config = json.loads(request_handler.getPostData("data"))
		header_banner = json.loads(request_handler.getPostData("header_banner"))
		footer_banner = json.loads(request_handler.getPostData("footer_banner"))
		response = response_code.success()
		oa_template_service.saveOrderAcknowledgementTemplateConfig(
			template_config=template_config, enterprise_id=enterprise_id, user_id=user_id,
			last_modified_on=last_modified_on, header_banner=header_banner, footer_banner=footer_banner)

		request_handler.setSessionAttribute("save_order_acknowledgement_template", "Configuration saved successfully")
		response['custom_message'] = "Order Acknowledgement Configuration saved successfully"
	except Exception as e:
		logger.exception("Order Acknowledgement  Configuration Update failed - %s" % e.message)
		response = response_code.internalError()
		response['custom_message'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def previewOrderAcknowledgementTemplate(request):
	"""
	Saves the edited Purchase template config and renders the manage view on successful save.

	:param request:
	:return:
	"""
	oa_id = None
	try:
		logger.info('Previewing a Order Acknowledgement Template config...')
		request_handler = RequestHandler(request)
		oa_template_service = PurchaseTemplateConfigService()
		user = request_handler.getSessionAttribute(USER_IN_SESSION_KEY)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		oa_template_config = json.loads(request_handler.getPostData("data"))
		header_banner = json.loads(request_handler.getPostData("header_banner"))
		footer_banner = json.loads(request_handler.getPostData("footer_banner"))
		oa_id = executeQuery(
			"""SELECT a.id as oa_id FROM order_acknowledgement as a, enterprise as b WHERE b.id = a.enterprise_id and b.id = %s  
			ORDER BY a.id DESC LIMIT 1""" % enterprise_id, as_dict=True)
		oa_id = oa_id[0]["oa_id"]
		response = response_code.success()
		db_session = oa_template_service.purchase_template_dao.db_session
		db_session.begin(subtransactions=True)
		try:
			updated_banner_image = []

			oa_template_service.purchase_template_dao.updateTemplateBannerImages(
				updated_banner_image, enterprise_id=enterprise_id, template_header_banner=header_banner,
				template_footer_banner=footer_banner, is_pdf_preview=True, db_session=db_session,
				collection='order_acknowledgement_template_config')
			preview_documents = PurchaseService().generatePreviewOADocument(
				oa_id=oa_id, user=user, oa_config_details=oa_template_config,
				updated_banner_image=updated_banner_image, db_session=db_session, enterprise_id=enterprise_id)
			single_page_data = preview_documents[0]
			multi_page_data = preview_documents[1]

			response['ext'] = "pdf"
			response['single_page_data'] = "data:application/pdf;base64,%s" % single_page_data.encode('base64')
			response['multi_page_data'] = "data:application/pdf;base64,%s" % multi_page_data.encode('base64')
		except Exception as e:
			logger.info("Problem with Order Acknowledgement Preview generation - %s" % e.message)
		finally:
			db_session.rollback()
		return HttpResponse(json.dumps(response), 'content-type=text/json')
	except Exception as e:
		logger.exception("Download Order Acknowledgement document failed for id %s. %s" % (oa_id, e.message))
		response = response_code.internalError()
		response['error'] = '%s' % e.message
		return HttpResponse(json.dumps(response), 'content-type=text/json')
