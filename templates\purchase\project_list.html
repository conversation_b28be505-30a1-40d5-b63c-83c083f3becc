{% extends "purchase/sidebar.html" %}
{% block projects %}
<style xmlns="http://www.w3.org/1999/html">
#custom-tab3 {
	border-radius: 0;
    border-right: 1px solid #ccc;
}

#custom-tab2 {
	border-radius: 50px 0 0 50px;
    border-right: 1px solid #ccc;
    padding-left: 30px;
    padding-right: 30px;
}

.create_po_container a.btn-new-item {
	box-shadow: -4px 4px 5px 0 rgba(60,64,67,0.302), 0 1px 3px 1px rgba(60,64,67,0.149);
}

.create_indent_po.btn-new-item  {
	border-radius: 0 50px 50px 0;
}

.btn-new-item-label {
    color: #004195;
    padding: 6px !important;
}

.btn-new-item-label:after {
	content: '';
}

.btn-new-item-label.create_jo_page:after {
    content: 'Create';
}

.btn-new-item-label.normal_po:after {
	content: '+';
	font-size: 24px;
}

.btn-new-item-label.quick_po:after {
	content: 'Quick';
}

.btn-new-item-label.po_via_indent:after {
	content: 'via Indent';
}

#custom-tab3:hover,
#custom-tab2:hover,
.create_indent_po:hover {
	background: rgba(32, 155, 225,0.1);
	box-shadow: -4px 4px 5px 0 rgba(60,64,67,0.302), 0 1px 3px 1px rgba(60,64,67,0.149) !important;
}

#chooseIndent .dataTables_paginate {
	margin-right: 0 !important;
}

</style>

<script type="text/javascript" src="/site_media/js/po_list.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/mailer.js?v={{ current_version }}"></script>

<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">{{ template_title }}</span>
	</div>
</div>
<script type="text/javascript">
$(document).ready(function() {
	initializePage();
	$("#template_configuration").removeClass('hide');
	$("#template_configuration").click(function(){
		window.open('/erp/admin/po_template/','_blank');
	});
});
</script>
{% endblock %}
