function setNumberRangeOnFocus(el, beforeDecimal, afterDecimal, isEmptyAllowed=false, isNegativeAllowded=false) {
    $(el).select();
    var totalLength = $(el).attr("maxlength");
    if(totalLength == undefined) {
        totalLength = 15;
    }
    for(i=0;i<=totalLength;i++) {
        if(afterDecimal > 0) {
            if(isNegativeAllowded) {
                $(el).on('input', function () {
                    this.value = this.value
                      .replace(/[^\d.-]/g, '')            
                      .replace(new RegExp("(^[\\d]{" + beforeDecimal + "})[\\d]", "g"), '$1') 
                      .replace(/(\..*)\./g, '$1')
                      .replace(/(\-\-*)\-/g, '$1')
                      .replace(new RegExp("(\\.[\\d]{" + afterDecimal + "}).", "g"), '$1');  
                });
            }
            else {
                $(el).on('input', function () {
                    this.value = this.value
                      .replace(/[^\d.]/g, '')            
                      .replace(new RegExp("(^[\\d]{" + beforeDecimal + "})[\\d]", "g"), '$1') 
                      .replace(/(\..*)\./g, '$1')
                      .replace(new RegExp("(\\.[\\d]{" + afterDecimal + "}).", "g"), '$1');  
                });
            }
        }
        else {
            $(el).on('input', function () {
                this.value = this.value
                  .replace(/[^\d]/g, '')            
                  .replace(new RegExp("(^[\\d]{" + beforeDecimal + "})[\\d]", "g"), '$1');  
            });    
        } 
    }
    $(el).change(function(){
        if(el.value == "" && isEmptyAllowed) {
            el.value = ''
        }
        else {
            var updated_value = el.value;
            updated_value = updated_value+'';
            if(afterDecimal > 0) {
                pos = updated_value.indexOf('.');
                if (pos==-1) updated_value = updated_value + '.00';
                else {
                    var integer = updated_value.substring(0,pos);
                    var decimals = updated_value.substring(pos+1);
                    while(decimals.length<2) decimals=decimals+'0';
                    updated_value = integer+'.'+decimals;
                }
            }
            if (isNaN(updated_value)) {
                updated_value = '0.00'
            }
            el.value = updated_value;
           // $(el).removeClass("error-border");
           // $(el).closest("div").find(".custom-error-message").remove();
        }
    })
}

function validateStringOnKeyPress(el, evt, type) {
    if(type == "character"){
        var regex = new RegExp("^[a-zA-Z]+$");
    }
    else if(type == "string"){
        var regex = new RegExp("^[a-zA-Z. ]+$");
    }
    else if(type == "number"){
        var regex = new RegExp("^[0-9]+$");
    }
    else if(type == "ph_number"){
        var regex = new RegExp("^[0-9-+() ]+$");
    }
     else if(type == "mobile_number"){
        var regex = new RegExp("^[0-9]+$");
    }
    else if(type == "alphanumeric"){
        var regex = new RegExp("^[0-9a-zA-Z. ]+$");
    }
    else if(type == "alphanumeric_with"){
        var regex = new RegExp("^[0-9a-zA-Z_ ]+$");
    }
    else if(type == "alphaSpecialCharMini"){
        var regex = new RegExp("^[0-9a-zA-Z ':,()/._-]+$");
    }
    else if(type == "alphaSpecialWithoutSingleQuotes"){
        var regex = new RegExp("^[a-zA-Z0-9 \"@$#%:?!*&,()+/._-]+$");
    }
    else if(type == "alphaSpecialChar"){
        var regex = new RegExp("^[a-zA-Z0-9 '\"@$#%:?!*&,()+/._-]+$");
    }
    else if(type == "hsn_specialChar"){
            var regex =new RegExp("^[0-9]+$");
    }
    var key = String.fromCharCode(!evt.charCode ? evt.which : evt.charCode);
    var keyChar = evt.charCode ? evt.which : evt.charCode;

    if(keyChar != 0){
        if (!regex.test(key)) {
            if(type != "alphaSpecialChar"){
                evt.preventDefault();
                return false;
            }
            else if(type == "alphaSpecialChar" && (keyChar != 91 && keyChar != 93 && keyChar != 13)) {
                evt.preventDefault();
                return false;
            }
        }
    }
}

function validateStringOnBlur(el, evt, type) {
    if(type == "character"){
         el.value = el.value.replace(/[^a-z]/gi, '');
    }
    else if(type == "string"){
         el.value = el.value.replace(/[^a-z\s.]/gi, '');
    }
    else if(type == "number"){
         el.value = el.value.replace(/[^0-9]/gi, '');
    }
    else if(type == "ph_number"){
         el.value = el.value.replace(/[^0-9)(+-\s.]/gi, '');
    }
    else if(type == "mobile_number"){
         el.value = el.value.replace(/[^0-9\s.]/gi, '');
    }
    else if(type == "alphanumeric"){
        el.value = el.value.replace(/[^0-9A-Za-z\s.]/gi, '');
    }
    else if(type == "alphanumeric_with"){
        el.value = el.value.replace(/[^0-9A-Za-z\s_]/gi, '');
    }
     else if(type == "alphaSpecialCharMini"){
        el.value = el.value.replace(/[^0-9A-Za-z._\'\-/:(,)\s.]/gi, '');
    }
    else if(type == "alphaSpecialWithoutSingleQuotes"){
        el.value = el.value.replace(/[^0-9A-Za-z._\-/:(,)\s.]/gi, '');
    }
    else if(type == "alphaSpecialChar") {
        el.value = el.value.replace(/[^0-9A-Za-z(,)\'\"\[\] /@+$#?:!%*&_,-\s.]/gi, '');
    }
    if(el.value != "") {
        if(el.classList.value.indexOf('auto-expandable') >= 0) {
            el.value = el.value.trim();
        }
        else {
            el.value = el.value.trim().replace(/\s\s+/g, ' ');
        }
    }
}

function validatePercentage(el, evt){
    if(el.value > 100){
        el.value = Number(100.00).toFixed(2);
    }
}