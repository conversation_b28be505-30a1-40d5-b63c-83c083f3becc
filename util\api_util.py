"""
"""

import datetime
from calendar import monthrange

from dateutil.relativedelta import relativedelta

from erp.models import AutoSerialize
from util import logger

__author__ = 'nandha, charles'

debuggable = True


class ResponseTemplate(object):
	@staticmethod
	def success():
		"""

		:return: 200
		"""
		return {'response_code': 200, 'response_message': 'Success'}

	@staticmethod
	def delete():
		"""

		:return: 200
		"""
		return {'response_code': 200, 'response_message': 'Delete'}

	@staticmethod
	def failure():
		"""

		:return: 400
		"""
		return {'response_code': 400, 'response_message': 'Failure'}

	@staticmethod
	def unknownFailure():
		"""

		:return: 400
		"""
		return {'response_code': 400, 'response_message': 'Unknown Failure'}

	@staticmethod
	def invalidRequestBody():
		"""

        :return: 400
        """
		return {'response_code': 400, 'response_message': 'Invalid Request Body'}

	@staticmethod
	def paramMissing():
		"""

		:return: 400
		"""
		return {'response_code': 400, 'response_message': 'Parameter Missing'}

	@staticmethod
	def alreadyExists():
		"""

        :return: 400
        """
		return {'response_code': 400, 'response_message': 'Requested Item Already Exists'}

	@staticmethod
	def sessionTimeout():
		"""

		:return: 400
		"""
		return {
			'response_code': 400, 'response_message': 'Session Timeout',
			'custom_message': 'Session Timeout. Please login again!'}

	@staticmethod
	def internalError():
		"""

		:return: 500
		"""
		return {
			'response_code': 500, 'response_message': 'Internal server error',
			'custom_message': 'Internal server error. Please Contact Admin!'}

	@staticmethod
	def databaseError():
		"""

		:return: 109
		"""
		return {'response_code': 109, 'response_message': 'Database error'}


class JsonUtil(object):
	@staticmethod
	def objectAsArrayMap(data_object=None):
		"""
		Converts the Model dataObject to map or array of map
		:param data_object:
		:return:
		"""
		ret = None
		try:
			if hasattr(data_object, '__iter__'):
				ret = []
				for item in data_object:
					value = JsonUtil.objectAsArrayMap(item)
					if value is not None:
						ret.append(value)
			elif AutoSerialize in data_object.__class__.__bases__:
				ret = data_object.getPublicValue()
			else:
				ret = data_object

		except Exception as e:
			logger.exception("Failed to construct object to map / list : %s" % e)
			return ret
		return ret

	@staticmethod
	def getValue(json_object=None, key=''):
		"""
		:param json_object:
		:param key:
		:return:
		"""
		try:
			if json_object[key]:
				return json_object[key]
		except KeyError:
			return None
		return None

	@staticmethod
	def getDateRange(rh=None, since_session_key=None, till_session_key=None):
		"""

		Returns non null date range since and till
		:param rh:  Request Handler
		:param since_session_key:
		:param till_session_key:
		:return:
		"""

		since, till = rh.getData('since'), rh.getData('till')
		if till in ("", None):
			till = rh.getData('to_date')
			if till in ("", None):
				till = rh.getData('todate')

		if since in ("", None):
			since = rh.getData('from_date')
			if since in ("", None):
				since = rh.getData('fromdate')

		if since in ("", None) and since_session_key:
			since = rh.getSessionAttribute(since_session_key)
		if till in ("", None) and till_session_key:
			till = rh.getSessionAttribute(till_session_key)
		till = datetime.datetime.strptime(till, "%Y-%m-%d") if till else datetime.datetime.today()
		since = datetime.datetime.strptime(since, "%Y-%m-%d") if since else datetime.datetime.today() + relativedelta(
			days=-6, hour=0, minute=0, second=0, microsecond=0)
		till = till + relativedelta(days=1, hour=0, minute=0, second=0, microsecond=0, seconds=-1)
		# keeping in session
		if since_session_key:
			rh.setSessionAttribute(since_session_key, since.strftime("%Y-%m-%d"))
		if till_session_key:
			rh.setSessionAttribute(till_session_key, till.strftime("%Y-%m-%d"))
		return since, till

	@staticmethod
	def getDateRangeByMonth(year, month):
		"""

		:param year:
		:param month:
		:return:
		"""
		if year is None:
			today = datetime.date.today()
			year = today.year
		if month is None:
			today = datetime.date.today()
			month = today.month
		if month <= 0:
			year = year - 1
			month = 12 - abs(month)
		_, num_days = monthrange(year, month)
		since = datetime.datetime.strptime(str(datetime.date(year, month, 1)), "%Y-%m-%d")
		till = datetime.datetime.strptime(str(datetime.date(year, month, num_days)), "%Y-%m-%d")

		return since, till


json_util = JsonUtil()
response_code = ResponseTemplate()
