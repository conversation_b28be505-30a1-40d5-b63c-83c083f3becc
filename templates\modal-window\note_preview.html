<div id="icd_document_modal" class="modal fade" role="dialog">
  	<div class="modal-dialog modal-lg">
		<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Preview Document</h4>
      		</div>
      		<div class="modal-body text-center">
      			<input id="modal_icd_id" name="icd_id" value='' hidden="hidden"/>
      			<div class="row">
					<div style="padding-right: 15px; padding-left: 15px;">
						<div class="content_bg">
							<div class="add_table">
								{% if access_level.edit %}
									<div id="checked_note" class="hide" style="width:100%;float:left;margin-bottom: 15px;">
										<input name="remarks" id="id-acknowledgement" value="" style="width: 300px; margin-right: 15px;" maxlength="248" class="form-control pull-left" placeholder="Rejection Remarks"/>
										<a role='button' id='party_acknowledge_note' class="btn btn-save" onclick="partyStatusICD('party_acknowledged')" style="float: left;margin-left: 12px;">Acknowledged by Party</a>
										<a role='button' id='party_reject_note' class="btn btn-danger for_party_rejection" onclick="partyStatusICD('party_rejected')" style="float: left;margin-left: 6px;">Rejected by Party</a>
										<div style="display: inline-block; margin-right: 30px; float: right;" id="send_mail_div">
											<a role="button" id='display_popup' onclick="openMailPopup()" class="btn transparent-btn" data-tooltip="tooltip" data-placement="left" title="Email Note">
												<i class="fa fa-envelope" style="font-size: 14px;" aria-hidden="true"></i></a>
										</div>
									</div>

									<div id="icd_note_returned" class="row col-sm-12 hide">
										<div class=" div_return_note" style="width: 300px; float: left; margin-right: 15px;">
											<input type="hidden" name="receipt_no" id="id-receipt_no" value=""/>
											<input type="hidden" name="receipt_no" id="id-note_id" value=""/>
											<input name="remarks" id="id-remarks" value="" style="width: 300px; margin-right: 15px;" maxlength="248" class="form-control pull-left" placeholder="Return Remarks"/>
											<div class="remarks_count_link remarks_count disabled" onclick="remarksInSwal('icd_document_remarks');">
												<span class="remarks_counter">No</span><span> remarks</span>
											</div>
										</div>
										<span role="button" class="btn btn-save pull-left" style="margin-right: 16px;" id="id-returnNote" onclick="returnNote();">Return</span>

										{% if access_level.approve %}
											<div class="material_txt form-group pull-left" style="margin-right: 7px;">
												<span role="button" class="btn btn-save" id="cmdverify">Verify</span>
												<input type="hidden" name="receipt_no" id="id_receipt_no" value="{{receipt_no}}"/>
												<input type="hidden" name="note_id" id="id_note_id" value="{{note_id}}"/>
											</div>
										{% endif %}
									</div>
									<div class="remarks_count_link remarks_count disabled hide" id="icd_note_non_returned" onclick="remarksInSwal('icd_document_remarks');">
										<span class="remarks_counter">No</span><span> remarks</span>
									</div>
								<div style="color: #28a745; font-size: 16px; width: 50%; float: left;" class="text-left hide" id="icd_note_verified"> Note Verified!</div>
									<div style="color: #dc3545; font-size: 16px; width: 50%; float: left;" class="text-left hide" id="icd_note_returned_text"> Note Returned!</div>
									<div style="color: #28a745; font-size: 16px; width: 45%; float: left;" class="text-left hide" id="icd_note_party_acknowledged"> Party Acknowledged!</div>
									<div style="color: #dc3545; font-size: 16px; width: 50%; float: left;" class="text-left hide" id="icd_note_party_rejected"> Party Rejected!</div>
								{% endif %}
							</div>
						</div>
					</div>

				</div>
				<input type="hidden" id="icd_document_remarks" />
				<div class="no_doc_available general-warning hide" style="margin-top: 15px; font-size: 15px;"></div>
				<div id="icd_document_container"></div>
      		</div>
      		<div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
    	</div>
  	</div>
</div>