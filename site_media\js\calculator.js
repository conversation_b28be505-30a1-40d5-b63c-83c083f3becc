function calcKeyPadPress(x){
	if(x == "+" || x == "-" || x == "*" || x == "/") {
		var calcVal = $("#calc_display").val();
		var lastChar = calcVal[calcVal.length -1];
		if(lastChar == "+" || lastChar == "-" || lastChar == "*" || lastChar == "/") {
            return false;
		}
	}
	document.getElementById("calc_display").value+=x;
	$("#calc_display").focus();
	checkLength($("#calc_display").val());
}
function calcCalculateTotal(){
	if($("#calc_display").val() !="") {
		var calcVal = $("#calc_display").val();
		var lastChar = calcVal[calcVal.length -1];
		if(lastChar == "+" || lastChar == "-" || lastChar == "*" || lastChar == "/") {
			calcVal = calcVal.substring(0, calcVal.length - 1);
			$("#calc_display").val(calcVal);
		}
		setTimeout(function(){
			if(!$.isNumeric($("#calc_display").val())) {
				$(".calc_invalid").show();
				setTimeout(function(){
					$(".calc_invalid").fadeOut(500);
				},1500)
			}
		},10)
		var x = document.getElementById("calc_display").value;
		document.getElementById("calc_display").value = eval(x);
		checkLength(eval(x));
	}
}
function clearCalculator(){
	$("#calc_display").val("").focus();
}

$(document).ready(function(){
	$( "#draggable" ).draggable();
	$("#calc_display").keyup(function(e){
		if(e.which == 27) {
			closeCalculator();
		}
		if(e.which == 8) {
			checkLength($(this).val())
		}
	})
});

function showCalculator() {
	$(".calculator_table").removeClass("hide");
	$("#calc_display").focus();
}

function closeCalculator() {
	$(".calculator_table").addClass("hide");
	$(".calculator_table").css({left: "auto", top: "auto"})
	$("#calc_display").val("");
}

function MinimizeCalculator() {
	$(".calculator_table").addClass("hide");
}

function checkLength(num) {
	if (num.toString().length <=8 ) {
		$('.calc-display-text').css('font-size', '40px')
	}
	if (num.toString().length > 8 && num.toString().length <= 15) {
		$('.calc-display-text').css('font-size', '26px')
	}

	if (num.toString().length > 15) {
		$('.calc-display-text').css('font-size', '18px')
	}
}

function CalculatePercentage() {
	var expression = $("#calc_display").val();
	var copy = expression;
	var totalLen = 1;

	expression = expression.replace(/[0-9]+/g, "#").replace(/[\(|\|\.)]/g, "");
	var numbers = copy.split(/[^0-9\.]+/);
	var operators = expression.split("#").filter(function(n){return n});
	var result = [];

	for(i = 0; i < numbers.length; i++){
	     result.push(numbers[i]);
	     if (i < operators.length) result.push(operators[i]);
	}
	if(result.length > 1) {
		totalLen += (result[result.length - 1].length)
		calc = (result[result.length - 1]) / 100;
		totalLen += (result[result.length - 3].length)
		calc = calc * result[result.length - 3];
		
		var arthVal = $("#calc_display").val();
		arthVal = arthVal.substring(0, arthVal.length - totalLen);
		$("#calc_display").val(arthVal+""+calc);
	}
	else {
		$("#calc_display").val("");
	}
}

function validateArthimatciNumberOnKeyPress(el, evt) {
	if (evt.which == 13) {
		calcCalculateTotal();
	}
	else if(evt.which == 37) {
		CalculatePercentage();
		evt.preventDefault();
        return false;
	}
	else if(evt.which == 43 || evt.which == 45 || evt.which == 42 || evt.which == 47) {
		var calcVal = $("#calc_display").val();
		var lastChar = calcVal[calcVal.length -1];
		if(lastChar == "+" || lastChar == "-" || lastChar == "*" || lastChar == "/") {
			evt.preventDefault();
            return false;
		}
	}
	else {
	    var regex = new RegExp("^[0-9+-/*\.]+$");
	    var key = String.fromCharCode(!evt.charCode ? evt.which : evt.charCode);
	    var keyChar = evt.charCode ? evt.which : evt.charCode;
	    if(keyChar != 0){
	        if (!regex.test(key)) {
	            evt.preventDefault();
	            return false;
	        }
	    }
	    checkLength($("#calc_display").val());
	}
}