{% extends "template.html" %}
{% block home %}
<title>{{ template_title }}</title>
<link rel="stylesheet" type="text/css" href="/site_media/css/login.css?v={{ current_version }}" >
<style>
	.form-group {
		margin-bottom: 22px;
	}

	.change-password-container {
		width: 100%;
		max-width: 600px;
    	text-align: center;
    	margin: auto;
    	padding: 14px 0 20px;
    	margin: 0;
	    position: absolute;
	    top: 50%;
	    left: 50%;
	    margin-right: -50%;
	    transform: translate(-50%, -50%)
	}
</style>
<div class="main__theme__wrap contact-main-container">
	<div class="container login-testimonial-container change-password-container" style="max-width: 600px;">
	  	<div class="col-sm-12 contact-container">
		    <div class="col-sm-12 xserp-contact panel-signup" style="margin-bottom: 45px;">
		    	<div class="xserp-heading">
                	<div class="contact-header text-center">
            			Change Your Password
          			</div>
             	</div>
             	{% if user_email != None  %}
	          		<div class="form-group wrap-input floating-label hide">
	             		<input type="text" class="form-control floating-input" name="user_email" id="id_user_email" placeholder=" " value="{{ user_email }}">
	             		<label>Username *</label>
	          		</div>
	          	{% else %}
	          		<div class="form-group wrap-input floating-label">
	             		<input type="text" class="form-control floating-input" name="user_email" id="id_user_email" placeholder=" " value="">
	             		<label>Username *</label>
	          		</div>
          		{% endif %}
          		<div class="form-group wrap-input floating-label">
          			<div class="pwdMask">
	          			<input id="id_token" name="token" type="password" class="form-control input-lg hide">
	             		<input type="password" class="form-control floating-input" name="old_password" id="id_old_password" placeholder=" ">
	             		<label>Old Password *</label>
	             		<span class="fa fa-eye-slash pwd-toggle"></span>
	             	</div>
          		</div>

          		<div class="form-group wrap-input floating-label">
          			<div class="pwdMask">
	             		<input type="password" class="form-control floating-input" name="new_password" id="id_new_password" placeholder=" " maxlength="25" >
	             		<label>New Password *</label>
	             		<span class="fa fa-eye-slash pwd-toggle"></span>
	             	</div>
          		</div>
          		<div class="form-group wrap-input floating-label">
          			<div class="pwdMask">
	             		<input type="password" class="form-control floating-input" name="confirm_password" id="id_confirm_password" placeholder=" " maxlength="25" >
	             		<label>Confirm Password *</label>
	             		<span class="fa fa-eye-slash pwd-toggle"></span>
	             	</div>
          		</div>
          		
          		<div class="form-group" style="margin-top: 40px;">
                 	<button class="btn btn-lg btn-primary btn-block" id="change_password" type="button">Change Password</button>
              	</div>
       	 	</div>
  	 	</div> 
  	</div>
</div>
<script type="text/javascript">
$(document).ready(function(){
	ChangePasswordValidation();
	$(".pwd-toggle").click(function(){
		if($(this).hasClass("fa-eye-slash")) {
			$(this).closest(".pwdMask").find("input.floating-input").attr("type", "text");
			$(this).addClass("fa-eye").removeClass("fa-eye-slash");
		}
		else {
			$(this).closest(".pwdMask").find("input.floating-input").attr("type", "password");
			$(this).addClass("fa-eye-slash").removeClass("fa-eye");
		}
	});
});

function ChangePasswordValidation(){
	$("#change_password").click(function(){
		$(".error-border").removeClass('error-border');
		$(".custom-error-message").remove();
		var ControlCollections = [
			{
				controltype: 'textbox',
				controlid: 'id_new_password',
				isrequired: true,
				errormsg: 'New password is required.',
				minvalue: 6,
				minvalerrormsg: 'Password should be atleast 6 character.'
			},
			{
				controltype: 'textbox',
				controlid: 'id_confirm_password',
				isrequired: true,
				errormsg: 'Confirm password is required.',
				minvalue: 6,
				minvalerrormsg: 'Password should be atleast 6 character.',

			},
			{
				controltype: 'textbox',
				controlid: 'id_old_password',
				isrequired: true,
				errormsg: 'Old password is required.'
			}
		];
		var result = JSCustomValidator.JSvalidate(ControlCollections);
		if ($('#id_new_password').val() != $('#id_confirm_password').val()) {
			$('#id_new_password, #id_confirm_password').addClass('error-border');
			$("#id_confirm_password").parent().find(".custom-error-message").addClass("hide");
			$("#id_confirm_password").parent().append('<span class="custom-error-message">New Password and Confirm Password does not match.</span>');
			result = false;
		}

		if(result){
			if($("#id_old_password").val() != "") {
				if($("#id_old_password").val() ==  $("#id_new_password").val()){
					swal("","Your new password cannot be the same as your current password. Please try with another password. ","warning")
				}
				else {
					changePassword();		
				}
			}
			else {
				changePassword();
			}
		}
	});
}

function changePassword() {
	$("#id_old_password, #id_new_password, #id_confirm_password").attr("type", "password");
	$("#change_password").addClass("btn-theme-processing").text("Processing...");
	$('#id_new_password').removeClass('error-border');
	$.ajax({
		url: "/erp/auth/json/change_password/",
		type: "post",
		data: {
			user_email: $("#id_user_email").val(),
			old_password: $("#id_old_password").val(),
			new_password: $("#id_new_password").val()
		},
		success: function(response) {
			$("#change_password").removeClass("btn-theme-processing").text("Change Password");
			if (response.response_message === "Success") {
				swal({
				  title: "",
				  text: "Your Password has been changed successfully.",
				  type: "success"
				}, function () {
				    location.href = "/erp/";
				});
				
			} else if (response.response_message === "Session Timeout") {
				swal('Activation Link has Expired','Please regenerate it by clicking forgot password link again...','error');
                ga('send', 'event', 'Users', 'Change Password', $('#enterprise_label').val(), 1);
			}else{
				swal('Incorrect old password','Your old password seems to be incorrect. Please enter the correct password.','error');
                ga('send', 'event', 'Users', 'Change Password', $('#enterprise_label').val(), 1);
			}
		},
		error : function(xhr,errmsg,err) {
			$("#change_password").removeClass("btn-theme-processing").text("Change Password");
			console.log(xhr.status + ": " + xhr.responseText);
		}
	});
}

function password_cancel() {
	history.back();
}
</script>

{% endblock %}