import os
import pymysql
from logging import handlers
import logging


"""
"""
__author__ = 'saravanan'


# Logs
PATH = os.path.dirname(os.path.realpath(__file__))
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log_path = "/var/log/django/insert_ledger_bill.log"
logHandler = handlers.TimedRotatingFileHandler(log_path, when='midnight', interval=1, backupCount=100)
logHandler.setFormatter(formatter)
logger = logging.getLogger(os.path.splitext(os.path.basename(__file__))[0])
# Setting the threshold of logger to DEBUG
logger.setLevel(logging.DEBUG)
logger.addHandler(logHandler)


DB_PORT = 11985
DB_HOST = "bugs.seepl18.net.in"
DB_USER = "ensqlbackup"
DB_PASSWORD = "PMH!Q87d$l!(1)"
DB_NAME = "xserp-0910"


def executeQuery(query, as_dict=False, query_data=None, conversions=None):
	"""
	Executes a query & returns a result-set, as a list of either indexed tuples or dicts

	:param query:
	:param as_dict: Flag to specify the type of result-set, if True return a list of dicts, else a list of tuples
	:param query_data: Query parameters
	:param conversions
	:return:
	"""
	if conversions is None:
		conversions = pymysql.converters.conversions.copy()
	db_connection = pymysql.connect(host=DB_HOST, user=DB_USER, passwd=DB_PASSWORD, db=DB_NAME, port=DB_PORT,
	                                conv=conversions, charset="utf8mb4", binary_prefix=True)
	if as_dict:
		db_connection.cursorclass = pymysql.cursors.DictCursor
	cursor = db_connection.cursor()
	try:
		cursor.execute(query, query_data)
		db_connection.commit()
		return cursor

	except Exception as e:
		logger.info("Query cannot be executed %s" % e)
		db_connection.rollback()
	finally:
		db_connection.close()

# Data to insert

data = [
	('24-25/GV/000003','2024-04-02 00:00:00',781,5892.88,0,102,315808),
	('24-25/GV/000004','2024-04-02 00:00:00',781,6242.2,0,102,315809),
	('24-25/GV/000005','2024-04-02 00:00:00',781,592.96,0,102,315810),
	('24-25/GV/000006','2024-04-02 00:00:00',781,7300.66,0,102,315811),
	('24-25/GV/000007','2024-04-02 00:00:00',781,6829.58,0,102,315812),
	('24-25/GV/000008','2024-04-02 00:00:00',781,11292.03,0,102,315813),
	('24-25/GV/000009','2024-04-02 00:00:00',781,0.6,0,102,315814),
	('24-25/GV/000010','2024-04-02 00:00:00',781,7870.89,0,102,315816),
	('24-25/GV/000011','2024-04-02 00:00:00',781,3306.84,0,102,315825),
	('24-25/GV/000012','2024-04-02 00:00:00',781,3147.22,0,102,315826),
	('24-25/GV/000013','2024-04-02 00:00:00',781,8131.5,0,102,315827),
	('24-25/GV/000014','2024-04-02 00:00:00',781,8119.1,0,102,315828),
	('24-25/GV/000015','2024-04-02 00:00:00',781,2.65,0,102,315830),
	('24-25/GV/000016','2024-04-02 00:00:00',781,0.29,0,102,315831),
	('24-25/GV/000017','2024-04-02 00:00:00',781,2.36,0,102,315832),
	('24-25/GV/000018','2024-04-02 00:00:00',781,39.29,0,102,315838),
	('24-25/GV/000019','2024-04-02 00:00:00',781,6676.44,0,102,315841),
	('24-25/GV/000020','2024-04-02 00:00:00',781,6296.48,0,102,315844),
	('24-25/GV/000021','2024-04-02 00:00:00',781,8006.3,0,102,315845),
	('24-25/GV/000022','2024-04-02 00:00:00',781,799.21,0,102,315846),
	('24-25/GV/000023','2024-04-02 00:00:00',781,2.07,0,102,315881),
	('24-25/GV/000024','2024-04-02 00:00:00',781,189.98,0,102,315882),
	('24-25/GV/000025','2024-04-02 00:00:00',781,7.08,0,102,315883),
	('24-25/GV/000026','2024-04-02 00:00:00',781,9173.61,0,102,315884),
	('24-25/GV/000027','2024-04-02 00:00:00',781,0.29,0,102,315885),
	('24-25/GV/000028','2024-04-02 00:00:00',781,6350.76,0,102,315886),
	('24-25/GV/000029','2024-04-02 00:00:00',781,9987.52,0,102,315887),
	('24-25/GV/000030','2024-04-02 00:00:00',781,0.89,0,102,315888),
	('24-25/GV/000031','2024-04-02 00:00:00',781,6.19,0,102,315891),
	('24-25/GV/000032','2024-04-02 00:00:00',781,66422.2,0,102,315892),
	('24-25/GV/000033','2024-04-02 00:00:00',781,68416.4,0,102,315893),
	('24-25/GV/000034','2024-04-02 00:00:00',781,62894,0,102,315894),
	('24-25/GV/000035','2024-04-02 00:00:00',781,69183.4,0,102,315895),
	('24-25/GV/000036','2024-04-02 00:00:00',781,67802.8,0,102,315896),
	('24-25/GV/000037','2024-04-02 00:00:00',781,69490.2,0,102,315899),
	('24-25/GV/000038','2024-04-02 00:00:00',781,66422.2,0,102,315900),
	('24-25/GV/000039','2024-04-02 00:00:00',781,67496,0,102,315903),
	('24-25/GV/000040','2024-04-02 00:00:00',781,69643.6,0,102,315904),
	('24-25/GV/000041','2024-04-02 00:00:00',781,64428,0,102,315905),
	('24-25/GV/000042','2024-04-02 00:00:00',781,41.12,0,102,315906),
	('24-25/GV/000043','2024-04-02 00:00:00',781,306.8,0,102,315907),
	('24-25/GV/000044','2024-04-02 00:00:00',781,13806,0,102,315908),
	('24-25/GV/000045','2024-04-02 00:00:00',781,20709,0,102,315909),
	('24-25/GV/000046','2024-04-02 00:00:00',781,130.98,0,102,315910),
	('24-25/GV/000047','2024-04-02 00:00:00',781,83.4,0,102,315911),
	('24-25/GV/000048','2024-04-02 00:00:00',781,7.97,0,102,315912),
	('24-25/GV/000049','2024-04-02 00:00:00',781,0.6,0,102,315913),
	('24-25/GV/000050','2024-04-02 00:00:00',781,3.25,0,102,315914),
	('24-25/GV/000051','2024-04-02 00:00:00',781,0.6,0,102,315915),
	('24-25/GV/000052','2024-04-02 00:00:00',781,41.12,0,102,315916),
	('24-25/GV/000053','2024-04-02 00:00:00',781,3.13,0,102,315919),
	('24-25/GV/000054','2024-04-02 00:00:00',781,0.6,0,102,315920),
	('24-25/GV/000055','2024-04-02 00:00:00',781,0.29,0,102,315921),
	('24-25/GV/000056','2024-04-02 00:00:00',781,311.52,0,102,315922),
	('24-25/GV/000057','2024-04-02 00:00:00',781,17641.6,0,102,315923),
	('24-25/GV/000058','2024-04-02 00:00:00',781,18602.93,0,102,315924),
	('24-25/GV/000059','2024-04-02 00:00:00',781,41612.52,0,102,315925),
	('24-25/GV/000060','2024-04-02 00:00:00',781,0.89,0,102,315926),
	('24-25/GV/000061','2024-04-02 00:00:00',781,0.29,0,102,315927),
	('24-25/GV/000062','2024-04-02 00:00:00',781,1.47,0,102,315928),
	('24-25/GV/000063','2024-04-02 00:00:00',781,0.29,0,102,315929),
	('24-25/GV/000064','2024-04-02 00:00:00',781,38.94,0,102,315930),
	('24-25/GV/000065','2024-04-02 00:00:00',781,87.92,0,102,315931),
	('24-25/GV/000066','2024-04-02 00:00:00',781,272.99,0,102,315932),
	('24-25/GV/000067','2024-04-02 00:00:00',781,41.12,0,102,315933),
	('24-25/GV/000068','2024-04-02 00:00:00',781,72711.6,0,102,315934),
	('24-25/GV/000069','2024-04-02 00:00:00',781,72098,0,102,315935),
	('24-25/GV/000070','2024-04-02 00:00:00',781,0.89,0,102,315936),
	('24-25/GV/000071','2024-04-02 00:00:00',781,32.46,0,102,315937),
	('24-25/GV/000072','2024-04-02 00:00:00',781,17.7,0,102,315938),
	('24-25/GV/000073','2024-04-02 00:00:00',781,1.18,0,102,315940),
	('24-25/GV/000074','2024-04-02 00:00:00',781,2.65,0,102,315941),
	('24-25/GV/000075','2024-04-02 00:00:00',781,2.65,0,102,315942),
	('24-25/GV/000076','2024-04-02 00:00:00',781,77.99,0,102,315943),
	('24-25/GV/000083','2024-04-18 00:00:00',99238,12474,0,102,316472),
	('24-25/GV/000084','2024-04-18 00:00:00',99096,46461,0,102,316473),
	('24-25/GV/000085','2024-04-18 00:00:00',99096,64518,0,102,316474),
	('24-25/GV/000086','2024-04-18 00:00:00',99184,30700,0,102,316479),
	('24-25/GV/000090','2024-04-20 00:00:00',98241,129146,0,102,316519),
	('24-25/GV/000091','2024-04-20 00:00:00',98241,736824,0,102,316520),
	('24-25/GV/000099','2024-04-24 00:00:00',99171,83754,0,102,316699),
	('24-25/GV/000100','2024-04-24 00:00:00',99184,3128,0,102,316700),
	('24-25/GV/000103','2024-04-24 00:00:00',98653,134784,0,102,316724),
	('24-25/GV/000113','2024-04-25 00:00:00',76976,5940,0,102,316841),
	('24-25/GV/000114','2024-04-25 00:00:00',76976,10890,0,102,316842),
	('24-25/GV/000116','2024-04-25 00:00:00',59480,10890,0,102,316847),
	('24-25/GV/000121','2024-05-01 00:00:00',99096,16365,0,102,317099),
	('24-25/GV/000124','2024-04-29 00:00:00',99238,59519,0,102,317102),
	('24-25/GV/000147','2024-04-12 00:00:00',99060,4455,0,102,317483),
	('24-25/GV/000149','2024-04-30 00:00:00',99165,142391,0,102,317518),
	('24-25/GV/000159','2024-05-01 00:00:00',7455,103552,0,102,317655),
	('24-25/GV/000218','2024-04-13 00:00:00',57628,266579,0,102,319064),
	('24-25/GV/000219','2024-04-13 00:00:00',94062,11762,0,102,319116),
	('24-25/GV/000221','2024-05-14 00:00:00',76976,4950,0,102,319208),
	('24-25/GV/000222','2024-05-14 00:00:00',76976,4455,0,102,319239),
	('24-25/GV/000223','2024-05-14 00:00:00',76976,4455,0,102,319240),
	('24-25/GV/000224','2024-05-14 00:00:00',76976,12870,0,102,319251),
	('24-25/GV/000225','2024-05-14 00:00:00',76976,5940,0,102,319252),
	('24-25/GV/000226','2024-05-14 00:00:00',76976,9405,0,102,319253),
	('24-25/GV/000227','2024-05-14 00:00:00',76976,1732,0,102,319254),
	('24-25/GV/000228','2024-05-14 00:00:00',76976,12870,0,102,319265),
	('24-25/GV/000229','2024-05-14 00:00:00',76976,11880,0,102,319276),
	('24-25/GV/000230','2024-05-14 00:00:00',76976,5940,0,102,319287),
	('24-25/GV/000231','2024-05-14 00:00:00',76976,7920,0,102,319288),
	('24-25/GV/000232','2024-05-14 00:00:00',76976,4455,0,102,319300),
	('24-25/GV/000233','2024-05-14 00:00:00',76976,6435,0,102,319304),
	('24-25/GV/000234','2024-05-14 00:00:00',76976,16830,0,102,319347),
	('24-25/GV/000235','2024-05-14 00:00:00',76976,15840,0,102,319348),
	('24-25/GV/000236','2024-05-14 00:00:00',76976,16830,0,102,319359),
	('24-25/GV/000237','2024-05-14 00:00:00',76976,15840,0,102,319360),
	('24-25/GV/000238','2024-05-14 00:00:00',76976,16830,0,102,319361),
	('24-25/GV/000239','2024-05-14 00:00:00',76976,3960,0,102,319362),
	('24-25/GV/000240','2024-05-14 00:00:00',76976,7920,0,102,319373),
	('24-25/GV/000241','2024-05-14 00:00:00',76976,7920,0,102,319374),
	('24-25/GV/000242','2024-05-14 00:00:00',76976,7920,0,102,319385),
	('24-25/GV/000243','2024-05-14 00:00:00',76976,6435,0,102,319386),
	('24-25/GV/000244','2024-05-14 00:00:00',76976,4455,0,102,319387),
	('24-25/GV/000245','2024-05-14 00:00:00',76976,60,0,102,319388),
	('24-25/GV/000245','2024-05-14 00:00:00',76976,5940,0,102,319388),
	('24-25/GV/000246','2024-05-14 00:00:00',76976,3168,0,102,319399),
	('24-25/GV/000247','2024-05-14 00:00:00',76976,4455,0,102,319400),
	('24-25/GV/000248','2024-05-14 00:00:00',76976,4455,0,102,319401),
	('24-25/GV/000249','2024-05-14 00:00:00',76976,14850,0,102,319402),
	('24-25/GV/000250','2024-05-14 00:00:00',76976,16335,0,102,319413),
	('24-25/GV/000251','2024-05-14 00:00:00',76976,2673,0,102,319414),
	('24-25/GV/000252','2024-05-14 00:00:00',76976,2871,0,102,319415),
	('24-25/GV/000253','2024-05-14 00:00:00',99060,2475,0,102,319416),
	('24-25/GV/000254','2024-05-14 00:00:00',99060,16335,0,102,319427),
	('24-25/GV/000255','2024-05-14 00:00:00',99060,11385,0,102,319428),
	('24-25/GV/000256','2024-05-14 00:00:00',99060,21285,0,102,319429),
	('24-25/GV/000257','2024-05-14 00:00:00',99060,8910,0,102,319430),
	('24-25/GV/000258','2024-05-14 00:00:00',99060,6930,0,102,319431),
	('24-25/GV/000259','2024-05-14 00:00:00',59480,11880,0,102,319442),
	('24-25/GV/000260','2024-05-14 00:00:00',59480,11880,0,102,319443),
	('24-25/GV/000261','2024-05-14 00:00:00',59480,10890,0,102,319444),
	('24-25/GV/000262','2024-05-15 00:00:00',346,2160,0,102,320512),
	('24-25/GV/000263','2024-05-15 00:00:00',98241,83817,0,102,320513),
	('24-25/GV/000264','2024-05-15 00:00:00',98241,640881,0,102,320524),
	('24-25/GV/000268','2024-05-15 00:00:00',82488,68805,0,102,320548),
	('24-25/GV/000269','2024-05-15 00:00:00',97992,28466,0,102,320549),
	('24-25/GV/000275','2024-05-15 00:00:00',103397,15840,0,102,320807),
	('24-25/GV/000276','2024-05-15 00:00:00',97992,36462,0,102,320828),
	('24-25/GV/000278','2024-05-15 00:00:00',97992,17140,0,102,320861),
	('24-25/GV/000279','2024-05-15 00:00:00',97992,144589,0,102,320872),
	('24-25/GV/000280','2024-05-15 00:00:00',103458,2600,0,102,320874),
	('24-25/GV/000306','2024-05-20 00:00:00',99131,108801,0,102,326605),
	('24-25/GV/000310','2024-05-20 00:00:00',2997,216840,0,102,326783),
	('24-25/GV/000311','2024-05-20 00:00:00',99230,23364,0,102,326789),
	('24-25/GV/000312','2024-05-20 00:00:00',104981,11880,0,102,326791),
	('24-25/GV/000313','2024-05-20 00:00:00',82486,10850,0,102,326792),
	('24-25/GV/000314','2024-05-20 00:00:00',104982,12672,0,102,326798),
	('24-25/GV/000321','2024-05-20 00:00:00',866,13860,0,102,326841),
	('24-25/GV/000326','2024-05-21 00:00:00',866,38412,0,102,327302),
	('24-25/GV/000329','2024-05-21 00:00:00',99184,39432,0,102,327321),
	('24-25/GV/000345','2024-05-24 00:00:00',781,60785.34,0,102,329012),
	('24-25/GV/000346','2024-05-24 00:00:00',781,28.32,0,102,329013),
	('24-25/GV/000347','2024-05-24 00:00:00',781,0.94,0,102,329014),
	('24-25/GV/000348','2024-05-24 00:00:00',781,18.3,0,102,329015),
	('24-25/GV/000349','2024-05-24 00:00:00',781,414.54,0,102,329016),
	('24-25/GV/000350','2024-05-24 00:00:00',781,465.21,0,102,329017),
	('24-25/GV/000351','2024-05-24 00:00:00',781,1006.76,0,102,329018),
	('24-25/GV/000352','2024-05-24 00:00:00',781,0.29,0,102,329029),
	('24-25/GV/000353','2024-05-24 00:00:00',781,48.38,0,102,329030),
	('24-25/GV/000354','2024-05-24 00:00:00',781,0.6,0,102,329031),
	('24-25/GV/000355','2024-05-24 00:00:00',781,0.29,0,102,329032),
	('24-25/GV/000356','2024-05-24 00:00:00',781,15.7,0,102,329033),
	('24-25/GV/000357','2024-05-24 00:00:00',781,30.1,0,102,329034),
	('24-25/GV/000358','2024-05-24 00:00:00',781,213.17,0,102,329044),
	('24-25/GV/000359','2024-05-24 00:00:00',781,28.32,0,102,329045),
	('24-25/GV/000360','2024-05-24 00:00:00',781,1715.99,0,102,329046),
	('24-25/GV/000361','2024-05-24 00:00:00',781,1692.69,0,102,329047),
	('24-25/GV/000362','2024-05-24 00:00:00',781,71791.2,0,102,329055),
	('24-25/GV/000363','2024-05-24 00:00:00',781,73495.12,0,102,329056),
	('24-25/GV/000364','2024-05-24 00:00:00',781,22.42,0,102,329057),
	('24-25/GV/000365','2024-05-24 00:00:00',781,62587.2,0,102,329058),
	('24-25/GV/000366','2024-05-24 00:00:00',781,83309.47,0,102,329059),
	('24-25/GV/000367','2024-05-24 00:00:00',781,17487.6,0,102,329068),
	('24-25/GV/000368','2024-05-24 00:00:00',781,64428,0,102,329069),
	('24-25/GV/000369','2024-05-24 00:00:00',781,70161.38,0,102,329070),
	('24-25/GV/000370','2024-05-24 00:00:00',781,66509.52,0,102,329071),
	('24-25/GV/000371','2024-05-24 00:00:00',781,52156,0,102,329079),
	('24-25/GV/000372','2024-05-24 00:00:00',781,50948.68,0,102,329080),
	('24-25/GV/000373','2024-05-24 00:00:00',781,52781.02,0,102,329081),
	('24-25/GV/000374','2024-05-24 00:00:00',781,65820.4,0,102,329082),
	('24-25/GV/000375','2024-05-24 00:00:00',781,66298.06,0,102,329083),
	('24-25/GV/000376','2024-05-24 00:00:00',781,1522.82,0,102,329096),
	('24-25/GV/000377','2024-05-24 00:00:00',781,1635.01,0,102,329097),
	('24-25/GV/000378','2024-05-24 00:00:00',781,1585.55,0,102,329098),
	('24-25/GV/000379','2024-05-24 00:00:00',781,1559.56,0,102,329099),
	('24-25/GV/000380','2024-05-24 00:00:00',781,1632.04,0,102,329100),
	('24-25/GV/000381','2024-05-24 00:00:00',781,1593.66,0,102,329101),
	('24-25/GV/000382','2024-05-24 00:00:00',781,1630.55,0,102,329102),
	('24-25/GV/000383','2024-05-24 00:00:00',781,1482.28,0,102,329121),
	('24-25/GV/000384','2024-05-24 00:00:00',781,1606.61,0,102,329122),
	('24-25/GV/000385','2024-05-24 00:00:00',781,1559.92,0,102,329123),
	('24-25/GV/000386','2024-05-24 00:00:00',781,7.08,0,102,329124),
	('24-25/GV/000387','2024-05-24 00:00:00',781,5982.56,0,102,329125),
	('24-25/GV/000388','2024-05-24 00:00:00',781,9645.52,0,102,329126),
	('24-25/GV/000389','2024-05-24 00:00:00',781,49855.29,0,102,329127),
	('24-25/GV/000390','2024-05-24 00:00:00',781,9198.51,0,102,329142),
	('24-25/GV/000391','2024-05-24 00:00:00',781,32520.8,0,102,329143),
	('24-25/GV/000392','2024-05-24 00:00:00',781,3681.6,0,102,329145),
	('24-25/GV/000393','2024-05-24 00:00:00',781,38091.87,0,102,329147),
	('24-25/GV/000394','2024-05-24 00:00:00',781,23174.91,0,102,329148),
	('24-25/GV/000395','2024-05-24 00:00:00',781,8119.02,0,102,329166),
	('24-25/GV/000396','2024-05-24 00:00:00',781,34248.29,0,102,329167),
	('24-25/GV/000397','2024-05-24 00:00:00',781,6399.47,0,102,329168),
	('24-25/GV/000398','2024-05-24 00:00:00',781,6232.28,0,102,329169),
	('24-25/GV/000399','2024-05-24 00:00:00',781,30708.92,0,102,329177),
	('24-25/GV/000400','2024-05-24 00:00:00',781,12885.6,0,102,329178),
	('24-25/GV/000401','2024-05-24 00:00:00',781,36509.8,0,102,329179),
	('24-25/GV/000402','2024-05-24 00:00:00',781,36204.76,0,102,329180),
	('24-25/GV/000403','2024-05-24 00:00:00',781,36969.4,0,102,329181),
	('24-25/GV/000404','2024-05-24 00:00:00',781,46336.84,0,102,329182),
	('24-25/GV/000405','2024-05-24 00:00:00',781,8003.92,0,102,329183),
	('24-25/GV/000406','2024-05-24 00:00:00',781,8068.07,0,102,329201),
	('24-25/GV/000407','2024-05-24 00:00:00',781,3965.6,0,102,329202),
	('24-25/GV/000408','2024-05-24 00:00:00',781,3199.29,0,102,329203),
	('24-25/GV/000409','2024-05-24 00:00:00',781,42031.6,0,102,329204),
	('24-25/GV/000410','2024-05-24 00:00:00',781,7979.84,0,102,329205),
	('24-25/GV/000411','2024-05-24 00:00:00',781,51695.8,0,102,329206),
	('24-25/GV/000412','2024-05-24 00:00:00',781,12675.37,0,102,329216),
	('24-25/GV/000413','2024-05-24 00:00:00',781,45560.09,0,102,329217),
	('24-25/GV/000414','2024-05-24 00:00:00',781,6811.27,0,102,329218),
	('24-25/GV/000416','2024-05-24 00:00:00',781,33441.2,0,102,329220),
	('24-25/GV/000417','2024-05-24 00:00:00',781,7472.95,0,102,329221),
	('24-25/GV/000418','2024-05-24 00:00:00',781,699.28,0,102,329222),
	('24-25/GV/000419','2024-05-24 00:00:00',781,6313.6,0,102,329223),
	('24-25/GV/000422','2024-05-24 00:00:00',781,11505.29,0,102,329237),
	('24-25/GV/000423','2024-05-24 00:00:00',781,6100.42,0,102,329238),
	('24-25/GV/000424','2024-05-24 00:00:00',781,1.47,0,102,329239),
	('24-25/GV/000426','2024-05-24 00:00:00',781,73.16,0,102,329241),
	('24-25/GV/000427','2024-05-24 00:00:00',781,8.86,0,102,329252),
	('24-25/GV/000428','2024-05-24 00:00:00',781,3.83,0,102,329253),
	('24-25/GV/000429','2024-05-24 00:00:00',781,62433.8,0,102,329254),
	('24-25/GV/000430','2024-05-24 00:00:00',781,59214.76,0,102,329255),
	('24-25/GV/000437','2024-05-27 00:00:00',99171,63974,0,102,330268),
	('24-25/GV/000438','2024-05-27 00:00:00',99238,27621,0,102,330273),
	('24-25/GV/000470','2024-05-25 00:00:00',99184,48302,0,102,332808),
	('24-25/GV/000473','2024-05-25 00:00:00',99184,74181,0,102,332813),
	('24-25/GV/000478','2024-06-08 00:00:00',346,2160,0,102,334160),
	('24-25/GV/000479','2024-06-08 00:00:00',346,2160,0,102,334161),
	('24-25/GV/000480','2024-06-08 00:00:00',346,2160,0,102,334162),
	('24-25/GV/000481','2024-06-08 00:00:00',346,2160,0,102,334167),
	('24-25/GV/000482','2024-06-08 00:00:00',346,2160,0,102,334168),
	('24-25/GV/000484','2024-06-10 00:00:00',7455,114386.59,0,102,335164),
	('24-25/GV/000485','2024-06-10 00:00:00',98241,998324.48,0,102,335167),
	('24-25/GV/000486','2024-06-10 00:00:00',98241,53726.4,0,102,335168),
	('24-25/GV/000496','2024-06-11 00:00:00',97992,113586,0,102,335513),
	('24-25/GV/000497','2024-06-11 00:00:00',99131,7623,0,102,335514),
	('24-25/GV/000498','2024-06-11 00:00:00',99131,9949,0,102,335515),
	('24-25/GV/000499','2024-06-11 00:00:00',92429,36504,0,102,335520),
	('24-25/GV/000500','2024-06-11 00:00:00',92429,438272,0,102,335521),
	('24-25/GV/000501','2024-06-11 00:00:00',92429,60840,0,102,335525),
	('24-25/GV/000517','2024-06-12 00:00:00',57628,90777,0,102,336096),
	('24-25/GV/000518','2024-06-12 00:00:00',94062,14401,0,102,336098),
	('24-25/GV/000519','2024-06-12 00:00:00',76976,5445,0,102,336109),
	('24-25/GV/000520','2024-06-12 00:00:00',76976,14850,0,102,336110),
	('24-25/GV/000521','2024-06-12 00:00:00',76976,14850,0,102,336116),
	('24-25/GV/000522','2024-06-12 00:00:00',76976,3960,0,102,336117),
	('24-25/GV/000523','2024-06-12 00:00:00',76976,5940,0,102,336118),
	('24-25/GV/000524','2024-06-12 00:00:00',76976,16830,0,102,336119),
	('24-25/GV/000525','2024-06-12 00:00:00',76976,16830,0,102,336126),
	('24-25/GV/000526','2024-06-12 00:00:00',76976,16830,0,102,336135),
	('24-25/GV/000527','2024-06-12 00:00:00',76976,11880,0,102,336145),
	('24-25/GV/000528','2024-06-12 00:00:00',76976,2871,0,102,336146),
	('24-25/GV/000529','2024-06-12 00:00:00',76976,4455,0,102,336147),
	('24-25/GV/000530','2024-06-12 00:00:00',76976,3960,0,102,336151),
	('24-25/GV/000531','2024-06-12 00:00:00',76976,3960,0,102,336155),
	('24-25/GV/000532','2024-06-12 00:00:00',76976,8910,0,102,336156),
	('24-25/GV/000533','2024-06-12 00:00:00',76976,15345,0,102,336162),
	('24-25/GV/000534','2024-06-12 00:00:00',76976,14850,0,102,336163),
	('24-25/GV/000535','2024-06-12 00:00:00',76976,990,0,102,336164),
	('24-25/GV/000536','2024-06-12 00:00:00',76976,3960,0,102,336165),
	('24-25/GV/000537','2024-06-12 00:00:00',76976,3960,0,102,336172),
	('24-25/GV/000538','2024-06-12 00:00:00',76976,3960,0,102,336173),
	('24-25/GV/000539','2024-06-12 00:00:00',76976,3960,0,102,336174),
	('24-25/GV/000540','2024-06-12 00:00:00',76976,3960,0,102,336175),
	('24-25/GV/000541','2024-06-12 00:00:00',76976,3960,0,102,336176),
	('24-25/GV/000542','2024-06-12 00:00:00',76976,2871,0,102,336183),
	('24-25/GV/000543','2024-06-12 00:00:00',76976,118800,0,102,336184),
	('24-25/GV/000544','2024-06-12 00:00:00',76976,39600,0,102,336185),
	('24-25/GV/000545','2024-06-12 00:00:00',99060,14850,0,102,336190),
	('24-25/GV/000546','2024-06-12 00:00:00',98067,12375,0,102,336193),
	('24-25/GV/000547','2024-06-12 00:00:00',98067,18315,0,102,336194),
	('24-25/GV/000548','2024-06-12 00:00:00',98067,9405,0,102,336195),
	('24-25/GV/000549','2024-06-12 00:00:00',98067,15345,0,102,336201),
	('24-25/GV/000550','2024-06-12 00:00:00',98067,11385,0,102,336202),
	('24-25/GV/000551','2024-06-12 00:00:00',98067,8910,0,102,336214),
	('24-25/GV/000552','2024-06-12 00:00:00',98067,8910,0,102,336215),
	('24-25/GV/000553','2024-06-12 00:00:00',98067,12375,0,102,336225),
	('24-25/GV/000554','2024-06-12 00:00:00',98067,14850,0,102,336226),
	('24-25/GV/000555','2024-06-12 00:00:00',98067,9900,0,102,336227),
	('24-25/GV/000556','2024-06-12 00:00:00',59480,40590,0,102,336233),
	('24-25/GV/000557','2024-06-14 00:00:00',59480,28641,0,102,336501),
	('24-25/GV/000612','2024-06-18 00:00:00',97992,28407,0,102,345745),
	('24-25/GV/000613','2024-06-18 00:00:00',97992,63476.47,0,102,345750),
	('24-25/GV/000614','2024-06-18 00:00:00',3212,91232,0,102,345751),
	('24-25/GV/000615','2024-06-18 00:00:00',99131,92431,0,102,345752),
	('24-25/GV/000616','2024-06-18 00:00:00',92429,443117,0,102,345757),
	('24-25/GV/000617','2024-06-18 00:00:00',92429,159939,0,102,345758),
	('24-25/GV/000620','2024-06-18 00:00:00',98653,112320,0,102,345766),
	('24-25/GV/000621','2024-06-18 00:00:00',99171,31720,0,102,345771),
	('24-25/GV/000623','2024-06-18 00:00:00',99096,73507,0,102,345776),
	('24-25/GV/000624','2024-06-18 00:00:00',99096,11029,0,102,345777),
	('24-25/GV/000629','2024-06-19 00:00:00',866,27769,0,102,345976),
	('24-25/GV/000630','2024-06-19 00:00:00',113953,56232,0,102,345979),
	('24-25/GV/000657','2024-06-20 00:00:00',98303,65520,0,102,346214),
	('24-25/GV/000658','2024-06-20 00:00:00',98303,29250,0,102,346215),
	('24-25/GV/000667','2024-06-25 00:00:00',781,253.17,0,102,397772),
	('24-25/GV/000668','2024-06-25 00:00:00',781,6333.45,0,102,397773),
	('24-25/GV/000669','2024-06-25 00:00:00',781,821.8,0,102,397774),
	('24-25/GV/000670','2024-06-25 00:00:00',781,7472.94,0,102,397775),
	('24-25/GV/000671','2024-06-25 00:00:00',781,6197.66,0,102,397776),
	('24-25/GV/000672','2024-06-25 00:00:00',781,782.29,0,102,397777),
	('24-25/GV/000673','2024-06-25 00:00:00',781,6971.85,0,102,397785),
	('24-25/GV/000674','2024-06-25 00:00:00',781,1069.16,0,102,397786),
	('24-25/GV/000675','2024-06-25 00:00:00',781,13932.43,0,102,397787),
	('24-25/GV/000676','2024-06-25 00:00:00',781,1214.31,0,102,397788),
	('24-25/GV/000677','2024-06-25 00:00:00',781,7816.32,0,102,397794),
	('24-25/GV/000678','2024-06-25 00:00:00',781,986.99,0,102,397795),
	('24-25/GV/000679','2024-06-25 00:00:00',781,3184.85,0,102,397796),
	('24-25/GV/000680','2024-06-25 00:00:00',781,4133.96,0,102,397797),
	('24-25/GV/000681','2024-06-25 00:00:00',781,8111.28,0,102,397798),
	('24-25/GV/000682','2024-06-25 00:00:00',781,8149.87,0,102,397806),
	('24-25/GV/000683','2024-06-25 00:00:00',781,1103.73,0,102,397807),
	('24-25/GV/000684','2024-06-25 00:00:00',781,865.28,0,102,397808),
	('24-25/GV/000685','2024-06-25 00:00:00',781,858.21,0,102,397809),
	('24-25/GV/000686','2024-06-25 00:00:00',781,856.69,0,102,397810),
	('24-25/GV/000687','2024-06-25 00:00:00',781,749.9,0,102,397811),
	('24-25/GV/000688','2024-06-25 00:00:00',781,286.58,0,102,397819),
	('24-25/GV/000689','2024-06-25 00:00:00',781,6609.69,0,102,397832),
	('24-25/GV/000690','2024-06-25 00:00:00',781,6389,0,102,397833),
	('24-25/GV/000691','2024-06-25 00:00:00',781,7967.36,0,102,397834),
	('24-25/GV/000692','2024-06-25 00:00:00',781,1596.04,0,102,397839),
	('24-25/GV/000693','2024-06-25 00:00:00',781,563.1,0,102,397841),
	('24-25/GV/000694','2024-06-25 00:00:00',781,1088.65,0,102,397842),
	('24-25/GV/000695','2024-06-25 00:00:00',781,84.86,0,102,397843),
	('24-25/GV/000696','2024-06-25 00:00:00',781,762.15,0,102,397844),
	('24-25/GV/000697','2024-06-25 00:00:00',781,9037.62,0,102,397846),
	('24-25/GV/000698','2024-06-25 00:00:00',781,1169.91,0,102,397854),
	('24-25/GV/000699','2024-06-25 00:00:00',781,6220.26,0,102,397856),
	('24-25/GV/000700','2024-06-25 00:00:00',781,9838.7,0,102,397857),
	('24-25/GV/000701','2024-06-25 00:00:00',781,306.8,0,102,397859),
	('24-25/GV/000702','2024-06-25 00:00:00',781,1587.18,0,102,397865),
	('24-25/GV/000703','2024-06-25 00:00:00',781,1553.06,0,102,397867),
	('24-25/GV/000704','2024-06-25 00:00:00',781,1238.11,0,102,397868),
	('24-25/GV/000705','2024-06-25 00:00:00',781,1191.05,0,102,397869),
	('24-25/GV/000706','2024-06-25 00:00:00',781,1224.58,0,102,397871),
	('24-25/GV/000707','2024-06-25 00:00:00',781,1570.29,0,102,397878),
	('24-25/GV/000708','2024-06-25 00:00:00',781,1706.76,0,102,397879),
	('24-25/GV/000709','2024-06-25 00:00:00',781,1513.25,0,102,397881),
	('24-25/GV/000710','2024-06-25 00:00:00',781,410.53,0,102,397887),
	('24-25/GV/000711','2024-06-25 00:00:00',781,1985.73,0,102,397931),
	('24-25/GV/000712','2024-06-25 00:00:00',781,1471.28,0,102,397932),
	('24-25/GV/000713','2024-06-25 00:00:00',781,1394.67,0,102,397936),
	('24-25/GV/000714','2024-06-25 00:00:00',781,1466.71,0,102,397937),
	('24-25/GV/000715','2024-06-25 00:00:00',781,1520.07,0,102,397938),
	('24-25/GV/000716','2024-06-25 00:00:00',781,28.32,0,102,397939),
	('24-25/GV/000717','2024-06-25 00:00:00',781,15.34,0,102,397945),
	('24-25/GV/000718','2024-06-25 00:00:00',781,28.32,0,102,397946),
	('24-25/GV/000719','2024-06-25 00:00:00',781,28.32,0,102,397947),
	('24-25/GV/000720','2024-06-25 00:00:00',781,42645.2,0,102,397948),
	('24-25/GV/000721','2024-06-25 00:00:00',781,33288.69,0,102,397949),
	('24-25/GV/000722','2024-06-25 00:00:00',781,70103.8,0,102,397958),
	('24-25/GV/000723','2024-06-25 00:00:00',781,71178.2,0,102,397959),
	('24-25/GV/000724','2024-06-25 00:00:00',781,15.05,0,102,397960),
	('24-25/GV/000725','2024-06-25 00:00:00',781,30.39,0,102,397965),
	('24-25/GV/000726','2024-06-25 00:00:00',781,1.42,0,102,397966),
	('24-25/GV/000727','2024-06-25 00:00:00',781,20.06,0,102,397967),
	('24-25/GV/000728','2024-06-25 00:00:00',781,592.36,0,102,397975),
	('24-25/GV/000729','2024-06-25 00:00:00',781,2.36,0,102,397976),
	('24-25/GV/000730','2024-06-25 00:00:00',781,28.32,0,102,397977),
	('24-25/GV/000731','2024-06-25 00:00:00',781,1694.25,0,102,397978),
	('24-25/GV/000732','2024-06-25 00:00:00',781,1739.5,0,102,397979),
	('24-25/GV/000733','2024-06-25 00:00:00',781,62603.72,0,102,397986),
	('24-25/GV/000734','2024-06-25 00:00:00',781,59532.18,0,102,397987),
	('24-25/GV/000735','2024-06-25 00:00:00',781,15.34,0,102,397994),
	('24-25/GV/000736','2024-06-25 00:00:00',781,1.47,0,102,397995),
	('24-25/GV/000737','2024-06-25 00:00:00',781,17405,0,102,397996),
	('24-25/GV/000738','2024-06-25 00:00:00',781,99.12,0,102,397997),
	('24-25/GV/000739','2024-06-25 00:00:00',781,0.29,0,102,397998),
	('24-25/GV/000740','2024-06-25 00:00:00',781,13.87,0,102,397999),
	('24-25/GV/000745','2024-06-27 00:00:00',346,2160,0,102,412313),
	('24-25/GV/000747','2024-06-28 00:00:00',114300,44460,0,102,412429),
	('24-25/GV/000793','2024-07-16 00:00:00',98067,12870,0,102,632195),
	('24-25/GV/000794','2024-07-16 00:00:00',98067,16830,0,102,632334),
	('24-25/GV/000795','2024-07-16 00:00:00',98067,15840,0,102,632416),
	('24-25/GV/000796','2024-07-16 00:00:00',98067,9405,0,102,632489),
	('24-25/GV/000797','2024-07-16 00:00:00',98067,38115,0,102,632524),
	('24-25/GV/000798','2024-07-16 00:00:00',98067,11880,0,102,632540),
	('24-25/GV/000799','2024-07-16 00:00:00',98067,14850,0,102,632565),
	('24-25/GV/000800','2024-07-16 00:00:00',94062,8014,0,102,635927),
	('24-25/GV/000801','2024-07-16 00:00:00',57628,130491,0,102,635947),
	('24-25/GV/000803','2024-07-16 00:00:00',76976,16830,0,102,635969),
	('24-25/GV/000804','2024-07-16 00:00:00',76976,118800,0,102,635970),
	('24-25/GV/000805','2024-07-16 00:00:00',76976,80190,0,102,635971),
	('24-25/GV/000806','2024-07-16 00:00:00',76976,8415,0,102,635972),
	('24-25/GV/000807','2024-07-16 00:00:00',76976,5940,0,102,635973),
	('24-25/GV/000808','2024-07-16 00:00:00',76976,2970,0,102,636033),
	('24-25/GV/000809','2024-07-16 00:00:00',76976,2475,0,102,636034),
	('24-25/GV/000810','2024-07-16 00:00:00',76976,11880,0,102,636035),
	('24-25/GV/000811','2024-07-16 00:00:00',76976,2475,0,102,637713),
	('24-25/GV/000812','2024-07-16 00:00:00',76976,14850,0,102,637714),
	('24-25/GV/000813','2024-07-16 00:00:00',76976,11880,0,102,637736),
	('24-25/GV/000814','2024-07-16 00:00:00',76976,21780,0,102,637737),
	('24-25/GV/000815','2024-07-16 00:00:00',76976,19800,0,102,637759),
	('24-25/GV/000816','2024-07-16 00:00:00',76976,14850,0,102,637760),
	('24-25/GV/000817','2024-07-16 00:00:00',76976,12870,0,102,637761),
	('24-25/GV/000818','2024-07-16 00:00:00',76976,12870,0,102,637762),
	('24-25/GV/000819','2024-07-16 00:00:00',76976,27720,0,102,637763),
	('24-25/GV/000820','2024-07-16 00:00:00',76976,11880,0,102,637787),
	('24-25/GV/000821','2024-07-16 00:00:00',76976,11880,0,102,637788),
	('24-25/GV/000822','2024-07-16 00:00:00',76976,4455,0,102,637789),
	('24-25/GV/000878','2024-07-25 00:00:00',2997,39425,0,102,723672),
	('24-25/GV/000884','2024-07-25 00:00:00',97992,70698,0,102,723807),
	('24-25/GV/000885','2024-07-25 00:00:00',113953,9999,0,102,723808),
	('24-25/GV/000886','2024-07-25 00:00:00',99096,28037,0,102,723831),
	('24-25/GV/000887','2024-07-25 00:00:00',99184,3960,0,102,723832),
	('24-25/GV/000888','2024-07-25 00:00:00',113953,21235,0,102,723856),
	('24-25/GV/000891','2024-07-25 00:00:00',103458,3530,0,102,723882),
	('24-25/GV/000893','2024-07-25 00:00:00',92429,612605,0,102,723925),
	('24-25/GV/000894','2024-07-25 00:00:00',99131,89669,0,102,723946),
	('24-25/GV/000895','2024-07-25 00:00:00',113953,30621,0,102,724128),
	('24-25/GV/000896','2024-07-25 00:00:00',866,46282,0,102,724157),
	('24-25/GV/000898','2024-07-25 00:00:00',92429,136848,0,102,724186),
	('24-25/GV/000901','2024-07-26 00:00:00',98653,64935,0,102,726490),
	('24-25/GV/000902','2024-07-26 00:00:00',97992,54645,0,102,726491),
	('24-25/GV/000903','2024-07-26 00:00:00',92429,136890,0,102,726532),
	('24-25/GV/000904','2024-07-26 00:00:00',98653,87750,0,102,726533),
	('24-25/GV/000905','2024-07-26 00:00:00',103458,5295,0,102,726560),
	('24-25/GV/000906','2024-07-26 00:00:00',92429,121738,0,102,726589),
	('24-25/GV/000907','2024-07-26 00:00:00',99131,107306,0,102,726590),
	('24-25/GV/000908','2024-07-26 00:00:00',99066,60014,0,102,726614),
	('24-25/GV/000909','2024-07-26 00:00:00',92429,463554,0,102,726615),
	('24-25/GV/000910','2024-07-26 00:00:00',2997,219410,0,102,726656),
	('24-25/GV/000911','2024-07-27 00:00:00',7455,109874,0,102,729745),
	('24-25/GV/000939','2024-07-30 00:00:00',99165,142387,0,102,738900),
	('24-25/GV/000970','2024-07-31 00:00:00',99165,142387,0,102,742573),
	('24-25/GV/000974','2024-07-31 00:00:00',346,2160,0,102,745957),
	('24-25/GV/000975','2024-07-31 00:00:00',346,2160,0,102,745958),
	('24-25/GV/000976','2024-07-31 00:00:00',346,2160,0,102,745959),
	('24-25/GV/000977','2024-07-31 00:00:00',98241,707672,0,102,745960),
	('24-25/GV/001049','2024-07-31 00:00:00',781,5932.27,0,102,759431),
	('24-25/GV/001050','2024-07-31 00:00:00',781,0.29,0,102,759432),
	('24-25/GV/001051','2024-07-31 00:00:00',781,6187.92,0,102,759433),
	('24-25/GV/001052','2024-07-31 00:00:00',781,16988.75,0,102,759434),
	('24-25/GV/001053','2024-07-31 00:00:00',781,7300.66,0,102,759435),
	('24-25/GV/001054','2024-07-31 00:00:00',781,6752.42,0,102,759467),
	('24-25/GV/001055','2024-07-31 00:00:00',781,12411.22,0,102,759468),
	('24-25/GV/001056','2024-07-31 00:00:00',781,7750.97,0,102,759469),
	('24-25/GV/001057','2024-07-31 00:00:00',781,2804.06,0,102,759470),
	('24-25/GV/001058','2024-07-31 00:00:00',781,21177.75,0,102,759506),
	('24-25/GV/001059','2024-07-31 00:00:00',781,7043.22,0,102,759507),
	('24-25/GV/001060','2024-07-31 00:00:00',781,21187.5,0,102,759508),
	('24-25/GV/001061','2024-07-31 00:00:00',781,1.18,0,102,759509),
	('24-25/GV/001062','2024-07-31 00:00:00',781,28.61,0,102,759510),
	('24-25/GV/001063','2024-07-31 00:00:00',781,6459.32,0,102,759511),
	('24-25/GV/001064','2024-07-31 00:00:00',781,939.28,0,102,759548),
	('24-25/GV/001065','2024-07-31 00:00:00',781,6242.2,0,102,759549),
	('24-25/GV/001066','2024-07-31 00:00:00',781,7953.49,0,102,759550),
	('24-25/GV/001067','2024-07-31 00:00:00',781,3.25,0,102,759551),
	('24-25/GV/001068','2024-07-31 00:00:00',781,788.24,0,102,759580),
	('24-25/GV/001069','2024-07-31 00:00:00',781,190.27,0,102,759603),
	('24-25/GV/001070','2024-07-31 00:00:00',781,9039.09,0,102,759604),
	('24-25/GV/001071','2024-07-31 00:00:00',781,6079.36,0,102,759605),
	('24-25/GV/001072','2024-07-31 00:00:00',781,9743.26,0,102,759651),
	('24-25/GV/001073','2024-07-31 00:00:00',781,0.29,0,102,759674),
	('24-25/GV/001074','2024-07-31 00:00:00',781,153.4,0,102,759891),
	('24-25/GV/001075','2024-07-31 00:00:00',781,0.94,0,102,759892),
	('24-25/GV/001076','2024-07-31 00:00:00',781,28.32,0,102,759893),
	('24-25/GV/001077','2024-07-31 00:00:00',781,0.29,0,102,759894),
	('24-25/GV/001078','2024-07-31 00:00:00',781,7.08,0,102,759895),
	('24-25/GV/001079','2024-07-31 00:00:00',781,2.36,0,102,759922),
	('24-25/GV/001080','2024-07-31 00:00:00',781,59.48,0,102,759923),
	('24-25/GV/001081','2024-07-31 00:00:00',781,5.9,0,102,759924),
	('24-25/GV/001082','2024-07-31 00:00:00',781,2000.99,0,102,759925),
	('24-25/GV/001083','2024-07-31 00:00:00',781,68876.6,0,102,759926),
	('24-25/GV/001084','2024-07-31 00:00:00',781,58445.4,0,102,760029),
	('24-25/GV/001085','2024-07-31 00:00:00',781,67064.12,0,102,760030),
	('24-25/GV/001086','2024-07-31 00:00:00',781,63201.09,0,102,760065),
	('24-25/GV/001087','2024-07-31 00:00:00',781,1.47,0,102,760084),
	('24-25/GV/001088','2024-07-31 00:00:00',781,28.32,0,102,760101),
	('24-25/GV/001089','2024-07-31 00:00:00',781,28.32,0,102,760102),
	('24-25/GV/001090','2024-07-31 00:00:00',781,1.18,0,102,760103),
	('24-25/GV/001091','2024-07-31 00:00:00',781,66882.4,0,102,760105),
	('24-25/GV/001092','2024-07-31 00:00:00',781,70717.69,0,102,760106),
	('24-25/GV/001093','2024-07-31 00:00:00',781,61259.12,0,102,760131),
	('24-25/GV/001094','2024-07-31 00:00:00',781,0.29,0,102,760140),
	('24-25/GV/001095','2024-07-31 00:00:00',781,11.8,0,102,760141),
	('24-25/GV/001096','2024-07-31 00:00:00',781,1.47,0,102,760142),
	('24-25/GV/001097','2024-07-31 00:00:00',781,315.88,0,102,760143),
	('24-25/GV/001098','2024-07-31 00:00:00',781,28.32,0,102,760144),
	('24-25/GV/001099','2024-07-31 00:00:00',781,2.65,0,102,760145),
	('24-25/GV/001100','2024-07-31 00:00:00',781,3.54,0,102,760174),
	('24-25/GV/001101','2024-07-31 00:00:00',781,0.6,0,102,760209),
	('24-25/GV/001102','2024-07-31 00:00:00',781,4.43,0,102,760210),
	('24-25/GV/001103','2024-07-31 00:00:00',781,2.36,0,102,760211),
	('24-25/GV/001104','2024-07-31 00:00:00',781,54.57,0,102,760212),
	('24-25/GV/001108','2024-08-09 00:00:00',99131,88961,0,102,762534),
	('24-25/GV/001109','2024-08-09 00:00:00',27956,122562,0,102,762535),
	('24-25/GV/001110','2024-08-09 00:00:00',99238,32432,0,102,762536),
	('24-25/GV/001118','2024-08-09 00:00:00',346,2160,0,102,763117),
	('24-25/GV/001119','2024-08-09 00:00:00',346,2160,0,102,763140),
	('24-25/GV/001120','2024-08-09 00:00:00',346,2160,0,102,763141),
	('24-25/GV/001188','2024-08-14 00:00:00',781,74.63,0,102,778770),
	('24-25/GV/001189','2024-08-14 00:00:00',781,3.54,0,102,778771),
	('24-25/GV/001190','2024-08-14 00:00:00',781,14.45,0,102,778772),
	('24-25/GV/001191','2024-08-14 00:00:00',781,2.65,0,102,778799),
	('24-25/GV/001192','2024-08-14 00:00:00',781,9.15,0,102,778800),
	('24-25/GV/001193','2024-08-14 00:00:00',781,5.9,0,102,778801),
	('24-25/GV/001194','2024-08-14 00:00:00',781,28.61,0,102,778802),
	('24-25/GV/001195','2024-08-14 00:00:00',781,644.16,0,102,778803),
	('24-25/GV/001196','2024-08-14 00:00:00',781,0.29,0,102,778804),
	('24-25/GV/001197','2024-08-14 00:00:00',781,1.47,0,102,778805),
	('24-25/GV/001198','2024-08-14 00:00:00',781,1.18,0,102,778837),
	('24-25/GV/001199','2024-08-14 00:00:00',781,1.23,0,102,778838),
	('24-25/GV/001200','2024-08-14 00:00:00',781,0.29,0,102,778864),
	('24-25/GV/001201','2024-08-14 00:00:00',781,0.29,0,102,778865),
	('24-25/GV/001202','2024-08-14 00:00:00',781,0.6,0,102,779006),
	('24-25/GV/001203','2024-08-14 00:00:00',781,2.31,0,102,779037),
	('24-25/GV/001204','2024-08-14 00:00:00',781,0.94,0,102,779038),
	('24-25/GV/001205','2024-08-14 00:00:00',781,28.32,0,102,779039),
	('24-25/GV/001206','2024-08-14 00:00:00',781,28.32,0,102,779040),
	('24-25/GV/001207','2024-08-14 00:00:00',781,0.29,0,102,779041),
	('24-25/GV/001208','2024-08-14 00:00:00',781,7.68,0,102,779077),
	('24-25/GV/001209','2024-08-14 00:00:00',781,55070.6,0,102,779078),
	('24-25/GV/001210','2024-08-14 00:00:00',781,1.88,0,102,779079),
	('24-25/GV/001211','2024-08-14 00:00:00',781,28.32,0,102,779080),
	('24-25/GV/001212','2024-08-14 00:00:00',781,10.04,0,102,779111),
	('24-25/GV/001213','2024-08-14 00:00:00',781,10.33,0,102,779112),
	('24-25/GV/001214','2024-08-14 00:00:00',781,57.53,0,102,779113),
	('24-25/GV/001215','2024-08-14 00:00:00',781,1.88,0,102,779115),
	('24-25/GV/001216','2024-08-14 00:00:00',781,28.32,0,102,779117),
	('24-25/GV/001217','2024-08-14 00:00:00',781,1.88,0,102,779119),
	('24-25/GV/001218','2024-08-14 00:00:00',781,1.47,0,102,779121),
	('24-25/GV/001219','2024-08-14 00:00:00',781,11.51,0,102,779158),
	('24-25/GV/001220','2024-08-14 00:00:00',781,0.6,0,102,779159),
	('24-25/GV/001221','2024-08-14 00:00:00',781,9498.35,0,102,779161),
	('24-25/GV/001222','2024-08-14 00:00:00',781,5956.76,0,102,779162),
	('24-25/GV/001223','2024-08-14 00:00:00',781,0.6,0,102,779163),
	('24-25/GV/001224','2024-08-14 00:00:00',781,8945.67,0,102,779164),
	('24-25/GV/001225','2024-08-14 00:00:00',781,159.33,0,102,779165),
	('24-25/GV/001226','2024-08-14 00:00:00',781,9.15,0,102,779202),
	('24-25/GV/001227','2024-08-14 00:00:00',781,734.02,0,102,779204),
	('24-25/GV/001228','2024-08-14 00:00:00',781,4641.56,0,102,779206),
	('24-25/GV/001229','2024-08-14 00:00:00',781,4982.58,0,102,779208),
	('24-25/GV/001230','2024-08-14 00:00:00',781,6067.91,0,102,779209),
	('24-25/GV/001231','2024-08-14 00:00:00',781,0.6,0,102,779236),
	('24-25/GV/001232','2024-08-14 00:00:00',781,28.61,0,102,779242),
	('24-25/GV/001233','2024-08-14 00:00:00',781,1.78,0,102,779243),
	('24-25/GV/001234','2024-08-14 00:00:00',781,10.62,0,102,779244),
	('24-25/GV/001235','2024-08-14 00:00:00',781,7901.72,0,102,779245),
	('24-25/GV/001236','2024-08-14 00:00:00',781,6477.6,0,102,779246),
	('24-25/GV/001237','2024-08-14 00:00:00',781,3775.51,0,102,779247),
	('24-25/GV/001238','2024-08-14 00:00:00',781,2524.04,0,102,779248),
	('24-25/GV/001239','2024-08-14 00:00:00',781,7169.26,0,102,779280),
	('24-25/GV/001240','2024-08-14 00:00:00',781,11990.55,0,102,779281),
	('24-25/GV/001241','2024-08-14 00:00:00',781,6604.61,0,102,779282),
	('24-25/GV/001242','2024-08-14 00:00:00',781,1.47,0,102,779283),
	('24-25/GV/001243','2024-08-14 00:00:00',781,7239.36,0,102,779284),
	('24-25/GV/001244','2024-08-14 00:00:00',781,6095.97,0,102,779285),
	('24-25/GV/001245','2024-08-14 00:00:00',781,620.39,0,102,779286),
	('24-25/GV/001246','2024-08-14 00:00:00',781,0.29,0,102,779287),
	('24-25/GV/001247','2024-08-14 00:00:00',781,5876.91,0,102,779319),
	('24-25/GV/001277','2024-08-16 00:00:00',7455,114894,0,102,781527),
	('24-25/GV/001278','2024-08-16 00:00:00',98241,465695,0,102,781528),
	('24-25/GV/001323','2024-08-20 00:00:00',76976,13860,0,102,795779),
	('24-25/GV/001324','2024-08-20 00:00:00',76976,16830,0,102,795780),
	('24-25/GV/001325','2024-08-20 00:00:00',76976,77220,0,102,795784),
	('24-25/GV/001326','2024-08-20 00:00:00',76976,16830,0,102,795786),
	('24-25/GV/001327','2024-08-20 00:00:00',76976,18810,0,102,795815),
	('24-25/GV/001328','2024-08-20 00:00:00',76976,15840,0,102,795816),
	('24-25/GV/001329','2024-08-20 00:00:00',76976,12870,0,102,795817),
	('24-25/GV/001330','2024-08-20 00:00:00',76976,4950,0,102,795818),
	('24-25/GV/001331','2024-08-20 00:00:00',76976,5445,0,102,795832),
	('24-25/GV/001332','2024-08-20 00:00:00',76976,4950,0,102,795846),
	('24-25/GV/001333','2024-08-20 00:00:00',76976,3960,0,102,795847),
	('24-25/GV/001334','2024-08-20 00:00:00',76976,10890,0,102,795848),
	('24-25/GV/001335','2024-08-20 00:00:00',76976,78210,0,102,795849),
	('24-25/GV/001336','2024-08-20 00:00:00',76976,4455,0,102,795879),
	('24-25/GV/001337','2024-08-20 00:00:00',76976,7920,0,102,795880),
	('24-25/GV/001338','2024-08-20 00:00:00',76976,14850,0,102,795881),
	('24-25/GV/001339','2024-08-20 00:00:00',76976,11880,0,102,795933),
	('24-25/GV/001340','2024-08-20 00:00:00',76976,14850,0,102,795934),
	('24-25/GV/001341','2024-08-20 00:00:00',76976,6435,0,102,795935),
	('24-25/GV/001342','2024-08-20 00:00:00',76976,3960,0,102,795963),
	('24-25/GV/001343','2024-08-20 00:00:00',76976,2871,0,102,795964),
	('24-25/GV/001344','2024-08-20 00:00:00',76976,5940,0,102,795965),
	('24-25/GV/001345','2024-08-20 00:00:00',76976,3168,0,102,795966),
	('24-25/GV/001346','2024-08-20 00:00:00',98067,14355,0,102,795967),
	('24-25/GV/001347','2024-08-20 00:00:00',98067,14355,0,102,795997),
	('24-25/GV/001351','2024-08-20 00:00:00',94062,21068,0,102,796053),
	('24-25/GV/001363','2024-08-22 00:00:00',99131,16607,0,102,801887),
	('24-25/GV/001364','2024-08-22 00:00:00',99131,74913,0,102,801914),
	('24-25/GV/001365','2024-08-22 00:00:00',99131,1921,0,102,801915),
	('24-25/GV/001494','2024-08-28 00:00:00',346,2160,0,102,847765),
	('24-25/GV/001495','2024-08-28 00:00:00',346,4320,0,102,847766),
	('24-25/GV/001502','2024-08-29 00:00:00',346,2160,0,102,848123),
	('24-25/GV/001503','2024-08-29 00:00:00',346,2160,0,102,848125),
	('24-25/GV/001504','2024-08-30 00:00:00',82488,29423,0,102,848526),
	('24-25/GV/001507','2024-08-30 00:00:00',866,80388,0,102,848533),
	('24-25/GV/001509','2024-08-30 00:00:00',27956,66597,0,102,848539),
	('24-25/GV/001511','2024-08-30 00:00:00',27956,34987,0,102,848542),
	# Add all other rows
]

for row in data:
	# Insert into ledger_bills
	query = """INSERT INTO ledger_bills (bill_no, bill_date, ledger_id, net_value, is_debit, enterprise_id, voucher_id)
        VALUES ('%s', '%s', %s, %s, %s, %s, %s)
    """ % row
	print query
	cursor = executeQuery(query=query)

	if cursor:
		# Get the last inserted id
		bill_id = cursor.lastrowid

		# Determine dr_value and cr_value based on is_debit
		if row[4] == 1:
			dr_value = row[3]
			cr_value = 0.00
		else:
			dr_value = 0.00
			cr_value = row[3]

		# Insert into ledger_bill_settlements
		query = """
	        INSERT INTO ledger_bill_settlements (bill_id, voucher_id, item_no, dr_value, cr_value, enterprise_id)
	        VALUES (%s, %s, %s, %s, %s, %s)
	    """ % (bill_id, row[6], 1, dr_value, cr_value, row[5])
		executeQuery(query=query)
	else:
		logger.info("Failed ledger bills: %s" % str(row))
