"""
"""
from datetime import datetime

import pymysql
from dateutil.relativedelta import relativedelta
from sqlalchemy import and_

from erp import logger
from erp.models import Receipt, Invoice, Voucher, CatalogueMaterial, Material, UnitMaster, CatalogueMaterialMake, Make, \
	AccountBook, Specification
from settings import HOST, USER, PASSWORD, DBNAME, PORT, SQLASession

__author__ = 'kalaivanan'


class DataAccessObject(object):
	"""
	Base Data Access Object that will be abstracting any ORM execution (SQL Alchemy, for now) dealing with the DB.
	Manage DB session level transaction and basic queries
	"""

	def __init__(self):
		self.db_session = SQLASession()

	def getCatalogues(self, cat_code, enterprise_id):
		"""
	
		:param cat_code:
		:param enterprise_id:
		:return:
		"""
		#Make to be removed in future
		query = self.db_session.query(
			CatalogueMaterial.quantity, CatalogueMaterial.item_id, Material.drawing_no, Material.name, Material.price,
			UnitMaster.unit_name, Material.tariff_no, Material.sample_size, Material.lot_size,
			Material.qc_method, Material.reaction_plan, CatalogueMaterial.parent_id, Material.unit_id,
			Material.is_stocked, Material.is_service, Material.makes_json
		).join(Material, and_(Material.material_id == CatalogueMaterial.item_id)).join(
			UnitMaster, and_(Material.unit_id == UnitMaster.unit_id, Material.enterprise_id == UnitMaster.enterprise_id)
		).filter(CatalogueMaterial.parent_id == cat_code, Material.enterprise_id == enterprise_id)
		return query.all()

	def getEarliestTransactionDate(self, enterprise_id=None):
		try:
			_earliest_grn = self.db_session.query(Receipt.inward_date).filter(
				Receipt.enterprise_id == enterprise_id).order_by(Receipt.inward_date).first()
			_earliest_dc = self.db_session.query(Invoice.prepared_on).filter(
				Invoice.enterprise_id == enterprise_id).order_by(Invoice.prepared_on).first()
			_earliest_voucher = self.db_session.query(Voucher.voucher_date).filter(
				Voucher.enterprise_id == enterprise_id).order_by(Voucher.voucher_date).first()

			__inward_date = _earliest_grn.inward_date if _earliest_grn and _earliest_grn.inward_date else datetime.today()
			__prepared_on = _earliest_dc.prepared_on if _earliest_dc and _earliest_dc.prepared_on else datetime.today()
			__voucher_date = _earliest_voucher.voucher_date if _earliest_voucher and _earliest_voucher.voucher_date else datetime.today()

			logger.debug("Earliest Dates of TXN: %s" % ([__inward_date, __prepared_on, __voucher_date]))
			return min(__inward_date, __prepared_on, __voucher_date)
		except Exception:
			raise

	def getPreviousBookClosureVoucher(self, before=None, enterprise_id=None):
		"""
		
		:param before:
		:param enterprise_id:
		:return:
		"""
		try:
			before = before if before else datetime.now()
			previous_book = self.getPreviousClosedBook(before=before, enterprise_id=enterprise_id)
			return previous_book.closure_voucher if previous_book else None
		except Exception:
			raise

	def getPreviousClosedBook(self, before=None, enterprise_id=None):
		"""

		:param before:
		:param enterprise_id:
		:return:
		"""
		try:
			before = before if before else datetime.now()
			return self.db_session.query(AccountBook).filter(
				AccountBook.enterprise_id == enterprise_id, AccountBook.till < before).order_by(
				AccountBook.till.desc()).first()
		except Exception:
			raise

	def getNextBookOpeningDate(self, before=None, enterprise_id=None):
		"""
		
		:param before:
		:param enterprise_id:
		:return:
		"""
		try:
			before = before if before else datetime.now()
			previous_closure_voucher = self.getPreviousBookClosureVoucher(before=before, enterprise_id=enterprise_id)
			return (previous_closure_voucher.voucher_date + relativedelta(
				days=1)) if previous_closure_voucher else self.getEarliestTransactionDate(enterprise_id=enterprise_id)
		except Exception:
			raise


def executeQuery(query, as_dict=False, query_data=None, conversions=None):
	"""
	Executes a query & returns a result-set, as a list of either indexed tuples or dicts
	
	:param query:
	:param as_dict: Flag to specify the type of result-set, if True return a list of dicts, else a list of tuples
	:param query_data: Query parameters
	:param conversions
	:return:
	"""
	if conversions is None:
		conversions = pymysql.converters.conversions.copy()
	db_connection = pymysql.connect(
		host=HOST, user=USER, passwd=PASSWORD, db=DBNAME, port=PORT,
		conv=conversions, charset="utf8mb4", binary_prefix=True)
	if as_dict:
		db_connection.cursorclass = pymysql.cursors.DictCursor
	cursor = db_connection.cursor()
	try:
		cursor.execute(query, query_data)
		query_result = cursor.fetchall()
		db_connection.commit()
		return query_result
	except Exception as e:
		logger.exception("Query cannot be executed - %s" % e)
		db_connection.rollback()
		raise
	finally:
		db_connection.close()
