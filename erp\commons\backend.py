"""
"""
import base64
import datetime
import email
import smtplib
from decimal import Decimal
from threading import Thread

import jwt
from dateutil.relativedelta import relativedelta
from django.template import Context
from django.template.loader import get_template
from jsonpickle import json
from pyfcm import FCMNotification
from sqlalchemy import and_, or_

from erp import DEFAULT_MAKE_ID, helper
from erp.admin import enterprise_module_settings
from erp.commons import logger
from erp.dao import DataAccessObject, executeQuery
from erp.models import FcmUserRegister, NotificationUserMap, Notification, UserPermission, User, Voucher, Receipt, \
	Expense, Invoice, OA, PurchaseOrder, Enterprise, SalesEstimate, UserEnterpriseMap
from erp.properties import CLIENT_APPROVAL_MAIL, NOTE_CLIENT_APPROVAL_MAIL
from settings import SMTP_SERVER, SMTP_PORT, SMTP_USER, SMTP_USER_PASSWORD, FCM_API_KEY, JWT_SECRET
from util.document_compiler import PDFGenerator
from util.helper import getAbsolutePath, readFile, writeFile
from util.properties import GST_LABEL

__author__ = 'nandha'


# Send to single device.

# Your api-key can be gotten from:  https://console.firebase.google.com/project/<project-name>/settings/cloudmessaging

# OR initialize with proxies

# proxy_dict = {
#           "http"  : "http://127.0.0.1",
#           "https" : "http://127.0.0.1",
#         }
# push_service = FCMNotification(api_key="AIzaSyBIs-kSpEnbj8MQQTl38qwv870UeFC7yjg", proxy_dict=proxy_dict)


class NotificationDAO(DataAccessObject):
	"""

	"""

	def createNotification(self, enterprise_id=None, created_by=None, message=None, collapse_key=None):
		"""

		Creates notification either message or collapse_key is not None
		:param enterprise_id:
		:param created_by:
		:param message:
		:param collapse_key:
		:return:
		"""
		try:
			notification = None
			if message or collapse_key:
				if collapse_key:
					notification = self.db_session.query(Notification).filter(
						Notification.enterprise_id == enterprise_id, Notification.collapse_key == collapse_key).first()
				if not notification:
					notification = Notification(
						enterprise_id=enterprise_id, created_by=created_by, collapse_key=collapse_key)
				notification.message = message
				notification.created_on = datetime.datetime.now()
			return notification
		except:
			raise

	def getCount(self, enterprise_id=None, user_id=None):
		"""

		:param enterprise_id:
		:param user_id:
		:return: int Count of notification
		"""
		count = 0
		try:
			count = self.db_session.query(NotificationUserMap).filter(NotificationUserMap.user_id == user_id).count()
			from erp.auth.backend import LoginService
			enterprise_settings_flag = LoginService().user_dao.db_session.query(
				Enterprise.setting_flags).filter(Enterprise.id == enterprise_id).first()
			icd_flag = enterprise_settings_flag.setting_flags & enterprise_module_settings['icd_flag'] > 0
			if not icd_flag:
				count = self.db_session.query(
					NotificationUserMap.notification_id, Notification.message,
					Notification.created_on).join(NotificationUserMap.notification).filter(
					NotificationUserMap.user_id == user_id)
				count = count.filter(or_(
					Notification.collapse_key.notin_(("icd_approved_count", "icd_checked_count")),
					Notification.collapse_key.is_(None))).order_by(Notification.created_on.desc()).count()
			logger.debug("Notification Count: %s for user_id %s" % (count, user_id))
		except Exception as e:
			logger.error("Could not fetch notification count... %s" % e.message)
		return count

	def getNotifications(self, enterprise_id=None, user_id=None):
		"""

		:param enterprise_id:
		:param user_id:
		:return:
		"""
		try:
			notifications = self.db_session.query(
				NotificationUserMap.notification_id, Notification.message,
				Notification.created_on).join(NotificationUserMap.notification).filter(
				NotificationUserMap.user_id == user_id)
			from erp.auth.backend import LoginService
			enterprise_settings_flag = LoginService().user_dao.db_session.query(
				Enterprise.setting_flags).filter(Enterprise.id == enterprise_id).first()
			icd_flag = enterprise_settings_flag.setting_flags & enterprise_module_settings['icd_flag'] > 0
			if not icd_flag:
				notifications = notifications.filter(or_(
					Notification.collapse_key.notin_(("icd_approved_count", "icd_checked_count")),
					Notification.collapse_key.is_(None)))
			return notifications.order_by(Notification.created_on.desc()).all()
		except Exception as e:
			logger.exception("Could not fetch notifications %s" % e.message)


class NotificationService:
	"""

	Service to handle notification process
	"""

	def __init__(self):
		"""

		"""
		self.fcm_sender = FCMNotification(api_key=FCM_API_KEY)
		self.dao = NotificationDAO()

	def getNotifications(self, enterprise_id=None, user_id=None):
		"""

		:param enterprise_id:
		:param user_id:
		:return:
		"""
		notification_list = []
		try:
			notifications = self.dao.getNotifications(enterprise_id=enterprise_id, user_id=user_id)

			for notification in notifications:
				date = ""
				if notification.created_on:
					date = notification.created_on.strftime('%Y-%m-%d %H:%M')
				if notification.message:
					notification_list.append({
						'id': notification.notification_id, 'message': notification.message,
						'created_on': date})
		except Exception as e:
			logger.exception("Could not fetch notification %s" % e.message)
		return notification_list

	def deleteNotifications(self, user_id=None, nm_ids=None):
		"""

		:param user_id:
		:param nm_ids:
		:return:
		"""
		self.dao.db_session.begin(subtransactions=True)
		try:
			n_maps = self.dao.db_session.query(NotificationUserMap).filter(
				NotificationUserMap.user_id == user_id, NotificationUserMap.notification_id.in_(nm_ids)).all()
			for nm in n_maps:
				self.dao.db_session.delete(nm)
			logger.info("Deleted %s notifications for user_id %s" % (len(n_maps), user_id))
			self.dao.db_session.commit()
			return True
		except Exception as e:
			self.dao.db_session.rollback()
			logger.error("Could not fetch notification %s " % e)
		return False

	def __removeInvalidFcmIds(self, fcm_reg_ids=()):
		"""

		:param fcm_reg_ids:
		:return:
		"""
		try:
			self.dao.db_session.begin(subtransactions=True)
			logger.info("Validating %s fcm ids " % len(fcm_reg_ids))
			valid_reg_ids = self.fcm_sender.clean_registration_ids(fcm_reg_ids)
			invalid_reg_ids = set(fcm_reg_ids) - set(valid_reg_ids)
			if len(invalid_reg_ids) > 0:
				logger.info("Removing %s invalid fcm ids " % len(invalid_reg_ids))
				fcm_registers = self.dao.db_session.query(FcmUserRegister).filter(
					FcmUserRegister.fcm_id.in_(invalid_reg_ids)).all()
				for fcm_register in fcm_registers:
					self.dao.db_session.delete(fcm_register)
			self.dao.db_session.commit()
		except Exception as e:
			logger.exception("Could not remove invalid fcm registration ids: %s" % e.message)
			self.dao.db_session.rollback()

	def __notifyFcmUsers(self, fcm_reg_ids=(), message="", collapse_key=None, action="",module_code=None,code=None,type=None):
		"""

		:param fcm_reg_ids:
		:param message:
		:param collapse_key:
		:param action:
		:return:
		"""

		try:
			if not fcm_reg_ids or len(fcm_reg_ids) == 0:
				return
			if module_code is None:
				module_code = "XSerp"
			json_message = dict(title=module_code, message=message, action=action, image="",code=code,type=type)
			logger.info("Push notification to %s device(s) " % json_message)
			if len(fcm_reg_ids) == 1:
				fcm_response = self.fcm_sender.single_device_data_message(
					registration_id=fcm_reg_ids[0], data_message=json_message, collapse_key=collapse_key)
			else:
				fcm_response = self.fcm_sender.multiple_devices_data_message(
					registration_ids=fcm_reg_ids, data_message=json_message, collapse_key=collapse_key)
				if fcm_response and int(fcm_response['failure']) > 0:
					self.__removeInvalidFcmIds(fcm_reg_ids=fcm_reg_ids)
			logger.debug("Push notification result %s" % fcm_response)
		except Exception as e:
			logger.exception("Notification failed... %s" % e.message)

	def pushFcmNotification(self, notification=Notification(), action="",module_code=None,code=None,type=None):
		"""

		:param notification:
		:param action:
		:return:
		"""

		try:
			if not notification.message:
				logger.warn("Could not push a None message")
				return
			fcm_register_list = self.dao.db_session.query(FcmUserRegister.fcm_id).join(NotificationUserMap, and_(
				FcmUserRegister.user_id == NotificationUserMap.user_id)).filter(
				NotificationUserMap.notification_id == notification.id).all()
			fcm_reg_ids = []
			for fcm_register in fcm_register_list:
				fcm_reg_ids.append(fcm_register.fcm_id)

			self.__notifyFcmUsers(
				fcm_reg_ids=fcm_reg_ids, message=notification.message, collapse_key=notification.collapse_key,
				action=action,module_code=module_code,code=code,type=type)
		except Exception as e:
			logger.exception("Sending Notification failed due to  %s " % e.message)

	def pushNotification(
			self, enterprise_id=None, sender_id=None, module_code=None, message=None,
			collapse_key=None, include_sender=False,code=None,type=None,expense_created_by = None):
		"""

		:param enterprise_id:
		:param sender_id: login user_id
		:param module_code: module of the project
		:param message: None will delete the notification if collapse_key is associated
		:param collapse_key:
		:param include_sender:
		:param expense_created_by:
		:return:
		"""
		self.dao.db_session.begin(subtransactions=True)
		notification = None
		try:
			logger.info("BROADCAST: %s:[%s-%s-%s]" % (message, enterprise_id, module_code, collapse_key))
			notification = self.dao.createNotification(
				enterprise_id=enterprise_id, created_by=sender_id, message=message, collapse_key=collapse_key)
			if not notification:
				return
			notification_user_list = []
			if message:
				user_permissions = self.dao.db_session.query(UserPermission).filter(
					UserPermission.module_code == module_code, UserPermission.enterprise_id == enterprise_id).all()
				is_creator=True
				for permission in user_permissions:
					if collapse_key == 'expense':
						user_access = self.dao.db_session.query(User).filter(User.id == permission.user_id, User.is_active).first()
						is_approver = user_access.hasModuleAccess(module_code="EXPENSES", access_level=UserPermission._APPROVE) > 0
						is_notify = user_access.hasModuleAccess(module_code="EXPENSES", access_level=UserPermission._NOTIFY) > 0
						if expense_created_by == sender_id and is_approver and is_notify and not permission.user.is_super and int(permission.user_id) != int(sender_id):
							n_map = NotificationUserMap(
									user_id=permission.user_id, enterprise_id=enterprise_id,
									notification_id=notification.id)
							notification_user_list.append(n_map)

						if expense_created_by != sender_id and is_approver and is_notify and not permission.user.is_super and int(permission.user_id) != int(sender_id):
							if expense_created_by == int(permission.user_id):
								is_creator = False
							n_map = NotificationUserMap(
									user_id=permission.user_id, enterprise_id=enterprise_id,
									notification_id=notification.id)
							notification_user_list.append(n_map)
					else:
						if include_sender or int(permission.user_id) != int(sender_id):
							if permission.hasNotifyAccess() > 0 and not permission.user.is_super:
								n_map = NotificationUserMap(
								user_id=permission.user_id, enterprise_id=enterprise_id,
								notification_id=notification.id)
								notification_user_list.append(n_map)
				users = self.dao.db_session.query(User).join(UserEnterpriseMap).filter(
					UserEnterpriseMap.enterprise_id == enterprise_id, User.is_super, User.is_active).all()

				if collapse_key == 'expense' and is_creator:
					n_map = NotificationUserMap(
						user_id=expense_created_by, enterprise_id=enterprise_id, notification_id=notification.id)
					notification_user_list.append(n_map)

				for user in users:
					if include_sender or int(user.id) != int(sender_id):
						n_map = NotificationUserMap(
							user_id=user.id, enterprise_id=enterprise_id, notification_id=notification.id)
						notification_user_list.append(n_map)
			notification.user_maps = notification_user_list
			self.dao.db_session.add(notification)
			self.dao.db_session.commit()
			logger.debug("Length of user map %s" % len(notification_user_list))
		except Exception as e:
			self.dao.db_session.rollback()
			logger.exception("Could not update message in notification table %s" % e.message)
		self.pushFcmNotification(notification=notification,module_code=module_code,code=code,type=type)

	def pushNotificationToAll(self, enterprise_id=None, sender_id=None, message="", collapse_key=None,action=""):
		"""

		:param enterprise_id:
		:param sender_id:
		:param message:
		:param collapse_key:
		:param action:
		:return:
		"""
		self.dao.db_session.begin(subtransactions=True)
		notification = None
		try:
			logger.info("Broadcasting notification to enterprise %s" % enterprise_id)
			notification = self.dao.createNotification(
				enterprise_id=enterprise_id, created_by=sender_id, message=message, collapse_key=collapse_key)
			if not notification:
				return
			users = self.dao.db_session.query(User).filter(User.is_active)
			if enterprise_id:
				user_list = self.dao.db_session.query(UserEnterpriseMap.user_id).filter(
					UserEnterpriseMap.enterprise_id == enterprise_id).all()
				users = users.filter(User.id.in_(user_list))
			notification_user_list = []
			for user in users.all():
				notification_user_list.append(NotificationUserMap(
					user_id=user.id, enterprise_id=enterprise_id, notification_id=notification.id))
			notification.user_maps = notification_user_list
			self.dao.db_session.add(notification)
			self.dao.db_session.commit()
		except Exception as e:
			self.dao.db_session.rollback()
			logger.exception("Could not update message in notification table %s" % e.message)
		self.pushFcmNotification(notification=notification,action=action)


class BadgeDAO(DataAccessObject):
	"""
	This service helps to get menu badge notification counts
	"""

	def getVoucherInitialDate(self, enterprise_id=None):
		"""
		get voucher start date
		:param enterprise_id:
		:return:
		"""
		try:
			query = self.db_session.query(Voucher.voucher_date).filter(Voucher.enterprise_id == enterprise_id)
			response = query.order_by('voucher_date').first()
			return response[0].strftime("%Y-%m-%d") if response else None
		except:
			raise

	def getICDInitialDate(self, enterprise_id=None, status=None):
		"""
		get voucher startdate
		:param enterprise_id:
		:param status:
		:return:
		"""
		try:
			query = self.db_session.query(Receipt.receipt_date).filter(Receipt.enterprise_id == enterprise_id)
			if status is not None:
				query = query.filter(Receipt.status == status)
			response = query.order_by('grn_date').first()
			response = response[0].strftime("%Y-%m-%d") if response is not None else None
			return response
		except:
			raise

	def getExpenseInitialDate(self, enterprise_id=None, status=None):
		"""
		get voucher start date
		:param enterprise_id:
		:param status:
		:return:
		"""
		try:
			query = self.db_session.query(Expense.created_on).filter(Expense.enterprise_id == enterprise_id)
			if status is not None:
				query = query.filter(Expense.status == status)
			response = query.order_by('created_on').first()
			return response.created_on.strftime("%Y-%m-%d") if response else None
		except:
			raise

	def getCreditDebitInitialDate(self, enterprise_id=None, status=None):
		"""
		get credit debit start date
		:param enterprise_id: NUMBER
		:param status: NUMBER
		:return: NUMBER
		"""
		try:
			query = self.db_session.query(Receipt.receipt_date).filter(
				Receipt.received_against == 'Note', Receipt.enterprise_id == enterprise_id)
			if status is not None:
				query = query.filter(Receipt.status == status)
			response = query.order_by('inward_date').first()
			return response[0].strftime("%Y-%m-%d") if response is not None else None
		except:
			raise

	def getPOInitialDate(self, enterprise_id=None, status=None):
		"""
		get voucher start date
		:param enterprise_id:
		:param status:
		:return:
		"""
		try:
			query = self.db_session.query(PurchaseOrder.order_date).filter(PurchaseOrder.enterprise_id == enterprise_id)
			if status is not None:
				query = query.filter(PurchaseOrder.status == status)
			response = query.order_by('order_date').first()
			return response[0].strftime("%Y-%m-%d") if response is not None and str(response[0]) != '0000-00-00 00:00:00' else None
		except:
			raise

	def getInitialReceiptDate(self, enterprise_id=None, status=None, received_against=None):
		"""

		:param enterprise_id:
		:param status:
		:param received_against:
		:return:
		"""
		try:
			query = self.db_session.query(Receipt.receipt_date).filter(Receipt.enterprise_id == enterprise_id)
			if status is not None:
				query = query.filter(Receipt.status.in_(status))
			if received_against:
				query = query.filter(Receipt.received_against == received_against)
			else:
				query = query.filter(Receipt.received_against.notin_(("Sales Return", "Issues")))
			response = query.order_by('grn_date').first()
			return response[0].strftime("%Y-%m-%d") if response else None
		except:
			raise


class BadgeService:
	"""
	To handle badge module count
	"""

	def __init__(self):
		self.dao = BadgeDAO()
		self.status = {
			'pending': {
				'voucher': Voucher.DRAFT,
				'icd': Receipt.STATUS_APPROVED,
				'expense': 0,
				'credit_debit': 1,
				'po': 0,
				'jo': 0,
				'wo': 0,
				'grn': (Receipt.STATUS_DRAFT, Receipt.STATUS_GRN_RETURNED),
				'invoice': Invoice.STATUS_DRAFT,
				'dc': Invoice.STATUS_DRAFT,
				'oa': OA.STATUS_DRAFT,
				'material': 0,
				'se': SalesEstimate.STATUS_DRAFT
			}
		}

	def fetchBadgeCount(self, enterprise_id=None, status=None, user_id=None):
		"""

		:param enterprise_id: INTEGER
		:param status: PENDING, APPROVAL, ALL
		:param user_id:
		:return:
		"""
		result = {}
		try:
			default_badge_count = {"enterprise_id": enterprise_id, "otherexpense": 0, "invoice": 0, "service": 0, "credit_debit": 0, "sr": 0,
								   "material": 0,"dc": 0, "oa": 0, "myexpense": 0, "wo": 0, "jo": 0, "voucher": 0, "irn": 0, "grn": 0,
								   "icd": 0, "po": 0, "se": 0 }
			getbadgecountquery = "SELECT * FROM badge_count_service WHERE enterprise_id=%s" % enterprise_id
			query_data = executeQuery(query=getbadgecountquery, as_dict=True)
			if query_data:
				result = query_data[0]
			else:
				result = default_badge_count
			result["expense"] = result["myexpense"] + result["otherexpense"]
			return result
		except Exception as e:
			logger.exception("Fetching Receipt Note count Failed: %s" % e.message)

	def fetchInitialDate(self, enterprise_id=None, module_list=None, status=None):
		"""
		Get initial date value of given list of modules
		:param enterprise_id: ENTERPRISE ID
		:param module_list: voucher, icd, expense, po, grn, invoice, dc, oa
		:param status: PENDING, APPROVED, ALL
		:return:
		"""
		result = {}
		try:
			for val in module_list:
				if val == 'voucher':
					result['voucher'] = self.dao.getVoucherInitialDate(enterprise_id=enterprise_id)
				if val == 'icd':
					result['icd'] = self.dao.getICDInitialDate(
						enterprise_id=enterprise_id, status=self.status[status]['icd'] if status else None)
				if val == 'expense':
					result['expense'] = self.dao.getExpenseInitialDate(
						enterprise_id=enterprise_id, status=self.status[status]['expense'] if status else None)
				if val == 'credit_debit':
					result['credit_debit'] = self.dao.getCreditDebitInitialDate(
						enterprise_id=enterprise_id, status=self.status[status]['credit_debit'] if status else None)
				if val == 'po':
					result['po'] = self.dao.getPOInitialDate(
						enterprise_id=enterprise_id, status=self.status[status]['po'] if status else None)
				if val == 'grn':
					result['grn'] = self.dao.getInitialReceiptDate(
						enterprise_id=enterprise_id, status=self.status[status]['grn'] if status else None)
				if val == 'irn':
					result['irn'] = self.dao.getInitialReceiptDate(
						enterprise_id=enterprise_id, status=self.status[status]['grn'] if status else None,
						received_against="Issues")
				if val == 'sr':
					result['sr'] = self.dao.getInitialReceiptDate(
						enterprise_id=enterprise_id, status=self.status[status]['grn'] if status else None,
						received_against="Sales Return")
			return result
		except Exception as e:
			logger.exception("Fetching startdate Failed: %s" % e.message)


def __pushNotificationInBackground(
		enterprise_id=None, sender_id=None, module_code=None, message=None,
		collapse_key=None, include_sender=False, code=None, type=None, expense_created_by=None, action=None):
	"""

	:param enterprise_id:
	:param sender_id: login user_id
	:param module_code: module of the project
	:param message:
	:param collapse_key:
	:param include_sender:
	:param expense_created_by:
	:param action:
	:return:
	"""
	if module_code:
		NotificationService().pushNotification(
			enterprise_id=enterprise_id, sender_id=sender_id, module_code=module_code,
			message=message, collapse_key=collapse_key, include_sender=include_sender,code=code,
			type = type,expense_created_by=expense_created_by)
	else:
		NotificationService().pushNotificationToAll(
			enterprise_id=enterprise_id, sender_id=sender_id, message=message, collapse_key=collapse_key,action=action)


def push_notification(
		enterprise_id=None, sender_id=None, module_code=None, message=None, collapse_key=None,
		include_sender=False,code=None,type = None,expense_created_by=None,action=None):
	"""

	:param enterprise_id:
	:param sender_id:
	:param module_code:
	:param message:
	:param collapse_key:
	:param include_sender:
	:param code:
	:param type:
	:param expense_created_by:
	:param action:
	:return:
	"""
	Thread(target=__pushNotificationInBackground, kwargs=dict(
		enterprise_id=enterprise_id, sender_id=sender_id, module_code=module_code, message=message,
		collapse_key=collapse_key, include_sender=include_sender,code=code,
		type=type,expense_created_by=expense_created_by,action=action)).start()


def sendMail(
		recipients=(), cc_list=(), subject="", body="", files=(), from_alias="", from_addr=SMTP_USER, reply_to=(),
		sender_mail=SMTP_USER, sender_password=SMTP_USER_PASSWORD, mail_server=SMTP_SERVER, mail_port=SMTP_PORT):
	"""
	Utility to send an SMTP mail from Server
	
	:param recipients:
	:param cc_list:
	:param subject:
	:param body:
	:param files:
	:param from_alias:
	:param from_addr:
	:param reply_to:
	:param sender_mail:
	:param sender_password:
	:param mail_server:
	:param mail_port:
	:return:
	"""
	try:
		mail_content = email.MIMEMultipart.MIMEMultipart('alternative')
		mail_content['subject'] = subject
		mail_content['from'] = "%s <%s>" % (from_alias, from_addr)
		mail_content['to'] = ",".join(recipients)
		mail_content['reply-to'] = ", ".join(reply_to)
		if len(cc_list) > 0:
			mail_content['Cc'] = ",".join(cc_list)
		mail_content.attach(email.mime.text.MIMEText(body, 'html'))
		# now attach the file
		for _file_item in files:
			attachment = email.mime.base.MIMEBase('application', 'octet-stream')
			attachment.set_payload(file(_file_item["path"], "rb").read())
			email.encoders.encode_base64(attachment)
			attachment.add_header('Content-Disposition', 'attachment;filename=' + _file_item["name"])
			mail_content.attach(attachment)

		to_addrs = recipients + cc_list
		mailer = smtplib.SMTP(mail_server, mail_port)
		mailer.ehlo()
		mailer.starttls()
		mailer.login(sender_mail, sender_password)
		mailer.sendmail(from_addr=sender_mail, to_addrs=to_addrs, msg=mail_content.as_string())
		mailer.quit()
		return True
	except Exception as e:
		logger.exception("Sending Mail Failed: %s" % e)
		pass
	return False


def getMailBody(enterprise_id=None, data=None, url_base=None, se_template=None, icd_service=None):
	"""

	:param enterprise_id:
	:param data:
	:param url_base:
	:param se_template:
	:param icd_service:
	:return:
	"""
	mail_body = ""
	try:
		if data['type'] == "se":
			se_id = data['se_id']
			se_no = data['code']
			expired_on = datetime.datetime.now() + relativedelta(days=7)
			se_data = dict(se_id=se_id, se_no=se_no, expired_on=expired_on.strftime("%Y-%m-%d %H:%M:%S"))
			token = jwt.encode(se_data, JWT_SECRET)
			client_status_url = "%s/erp/sales/sales_estimate/client_status/?ase=%s&status=%s"
			se_mail_template = str(se_template)
			se_mail_file_path = '/site_media/tmp/se_%s.html' % se_id
			writeFile(se_mail_template, se_mail_file_path)
			mail_body = readFile(
				se_mail_file_path).replace("<<approve_url>>", client_status_url % (url_base, token, 'approve')).replace(
				"<<reject_url>>", client_status_url % (url_base, token, 'reject'))
		if data['type'] == "icd" or data['type'] == "note":
			note_id = data['note_id']
			note_no = data['code']
			receipt_no = data['receipt_no']
			include_btns = data['include_btns']
			expired_on = datetime.datetime.now() + relativedelta(days=7)
			note_data = dict(note_id=note_id, note_no=note_no, receipt_no=receipt_no, expired_on=expired_on.strftime("%Y-%m-%d %H:%M:%S"))
			token = jwt.encode(note_data, JWT_SECRET)
			party_status_url = "%s/erp/auditing/json/party_status/?aicd=%s&status=%s"
			note_mail_template = getICDDocDetailsForMail(enterprise_id=enterprise_id, note_id=note_id, note_service=icd_service, include_btns=include_btns)
			note_mail_file_path = '/site_media/tmp/note_%s.html' % note_id
			writeFile(note_mail_template, note_mail_file_path)
			mail_body = readFile(note_mail_file_path).replace(
				"<<approve_url>>", party_status_url % (url_base, token, 'party_acknowledged')).replace(
				"<<reject_url>>", party_status_url % (url_base, token, 'party_reject'))
	except Exception as e:
		logger.exception("Construct Mail body Failed: %s" % e)

	return mail_body


def getICDDocDetailsForMail(enterprise_id=None, note_id=None, note_service=None, include_btns=False):
	try:
		source = note_service.audit_dao.getNote(enterprise_id=enterprise_id, note_id=note_id)
		# logger.info('Generating PDF for GRN: %s' % self.receipt)
		form_name = "%s NOTE" % ("CREDIT" if source.is_credit else "DEBIT")
		if source.enterprise.images and source.enterprise.images.logo and source.enterprise.images.logo != "":
			logo = "data:image/%s;base64,%s" % (source.enterprise.images.ext, base64.encodestring(source.enterprise.images.logo))
		else:
			logo = ""

		if source.enterprise.address_2 and source.enterprise.address_2 != "":
			address = source.enterprise.address_1 + ", " + source.enterprise.address_2
		else:
			address = source.enterprise.address_1

		enterprise_address = "{address}, {city}, {pin_code} {state}".format(
			address=address, city=source.enterprise.city, pin_code=source.enterprise.pin_code, state=source.enterprise.state)

		gst_label = GST_LABEL
		gst_detail = ""
		for item in source.enterprise.registration_details:
			if item.label.find("GST") != -1:
				gst_label = item.label
				gst_detail = item.details

		note_date = str(datetime.datetime.strptime(str(source.created_on), '%Y-%m-%d %H:%M:%S')) if source.created_on is not None and source.created_on != '0000-00-00 00:00:00' else source.created_on
		receipt_date = str(datetime.datetime.strptime(str(source.approved_on), '%Y-%m-%d %H:%M:%S')) if source.approved_on is not None and source.approved_on != '0000-00-00 00:00:00' else source.approved_on
		invoice_date = str(datetime.datetime.strptime(str(source.inv_date), '%Y-%m-%d %H:%M:%S')) if source.inv_date is not None and source.inv_date != '0000-00-00 00:00:00' else source.inv_date
		verified_date = str(datetime.datetime.strptime(str(source.approved_on), '%Y-%m-%d %H:%M:%S')) if source.approved_on is not None and source.approved_on != '0000-00-00 00:00:00' else source.approved_on

		mail_subject = "In Reference to %s <b>Invoice No.: %s (<i>Date: %s Value: %s</i>)" % (
			"our" if source.is_credit else "your", source.inv_no, source.inv_date.strftime('%d/%m/%Y') if source.inv_date is not None and source.inv_date != '0000-00-00 00:00:00' else "",
			source.inv_value)
		body = "We wish to advice having %s your Account with us as follows :" % (
			"credited" if source.is_credit else "debited")

		note_item_details, note_taxes, summary_details = getICDMaterialDetails(source=source)

		note_drafter = source.note_creator if source.note_creator else ""
		note_approver = source.note_approver if source.note_approver and source.status > 2 else ""

		context = Context({
			'form_name': form_name, 'enterprise_logo': logo, 'enterprise_address': enterprise_address, 'note_date': note_date,
			'receipt_date': receipt_date, 'verified_date': verified_date, 'source': source, 'gst_label': gst_label,
			'gst_detail': gst_detail, 'subject': mail_subject, 'body': body, 'invoice_date': invoice_date,
			'note_item_details': note_item_details, 'note_taxes': note_taxes, 'summary_details': summary_details,
			'note_drafter': note_drafter, 'note_approver': note_approver, 'include_btns': include_btns,
			'template_title': "Note Document"})

		template_src = getAbsolutePath(NOTE_CLIENT_APPROVAL_MAIL)

		template = get_template(template_src).render(context)
		return template
	except Exception as e:
		logger.exception('Something went wrong while creating PDF self.canvas - %s' % e)


def getICDMaterialDetails(source=None):
	note_materials = []
	available_taxes = []

	logger.info('No of Materials: %s' % len(source.items))

	index = 0
	total = Decimal(0)
	total_qty = Decimal(0)
	net_cgst = 0
	net_sgst = 0
	net_igst = 0
	for item in source.items:
		item_total = Decimal(item.rate * item.quantity) * (1 if item.is_credit else -1)

		if source.note_receipt_map.receipt.received_against == "Delivery Challan" or item.dc_id != 0:
			item_get_code = "%s" % item.dc.getInternalCode()
		elif source.note_receipt_map.receipt.received_against == "Purchase Order" or source.note_receipt_map.receipt.received_against == "Job Work":
			item_get_code = "" if item.purchase_order is None else "%s" % item.purchase_order.getCode()
		else:
			item_get_code = ""

		if item_total != 0:
			unit_name = item.material.unit
			if item.alternate_unit_id:
				unit_name = helper.getUnitName(enterprise_id=item.enterprise_id, unit_id=item.alternate_unit_id)

			index += 1
			total += Decimal("%0.2f" % item_total)
			total_qty += Decimal(item.quantity)
			make = ""
			cgst_rate = 0.00
			sgst_rate = 0.00
			igst_rate = 0.00
			cgst_value = 0.00
			sgst_value = 0.00
			igst_value = 0.00
			if item.make_id != DEFAULT_MAKE_ID:
				part_no = helper.getMakePartNumber(
					enterprise_id=item.enterprise_id, item_id=item.item_id, make_id=item.make_id)
				make = "[%s%s]" % (item.make.__repr__(), (" - %s" % part_no) if part_no else "")
			item_description = "%s%s %s" % (
				(" %s -" % item.material.drawing_no) if item.material.drawing_no else "", item.material.name, '[Faulty]' if item.is_faulty == 1 else "")
			rate = "%0.2f %s" % (item.rate, "(Cr)" if item.is_credit else "(Dr)")
			amount = "%0.2f" % (item.rate * item.quantity)
			for item_tax in item.getTaxesOfType("CGST"):
				cgst_rate = item_tax.tax.net_rate if item_tax else 0
				if cgst_rate != 0:
					cgst_value = Decimal(item.quantity) * Decimal(item.rate) * Decimal(cgst_rate) / 100
				net_cgst += Decimal("%0.2f" % cgst_value) * (1 if item.is_credit else -1)
			for item_tax in item.getTaxesOfType("SGST"):
				sgst_rate = item_tax.tax.net_rate if item_tax else 0
				if sgst_rate != 0:
					sgst_value = Decimal(item.quantity) * Decimal(item.rate) * Decimal(sgst_rate) / 100
				net_sgst += Decimal("%0.2f" % sgst_value) * (1 if item.is_credit else -1)
			for item_tax in item.getTaxesOfType("IGST"):
				igst_rate = item_tax.tax.net_rate if item_tax else 0
				if igst_rate != 0:
					igst_value = Decimal(item.quantity) * Decimal(item.rate) * Decimal(igst_rate) / 100
				net_igst += Decimal("%0.2f" % igst_value) * (1 if item.is_credit else -1)
			icd_items = {
				"item_index": index, "item_code": item_get_code, "item_description": item_description, "make": make,
				"reason": item.reason, "quantity": "%0.3f" % item.quantity, "unit_name": unit_name, "rate": rate,
				"amount": amount, "cgst_rate": "%0.2f" % cgst_rate, "cgst_value": "%0.2f" % cgst_value,
				"sgst_rate": "%0.2f" % sgst_rate, "sgst_value": "%0.2f" % sgst_value, "igst_rate": "%0.2f" % igst_rate,
				"igst_value": "%0.2f" % igst_value, "hsn_code": item.hsn_code}
			note_materials.append(icd_items)

	for item in source.non_stock_items:
		item_total = Decimal(item.rate * item.quantity) * (1 if item.is_credit else -1)
		po_code = ""
		make = ""
		if item.po_no:
			if source.note_receipt_map.receipt.received_against != "Delivery Challan":
				po_code = item.purchase_order.getCode() if item.purchase_order and item.po_no != 0 else ""
			else:
				po_code = "%s" % item.dc.getInternalCode()
		if item_total != 0:
			unit_name = item.unit.unit_name if item.unit.unit_name else ""
			index += 1
			total += Decimal("%0.2f" % item_total)
			total_qty += item.quantity
			cgst_rate = 0.00
			sgst_rate = 0.00
			igst_rate = 0.00
			cgst_value = 0.00
			sgst_value = 0.00
			igst_value = 0.00

			rate = "%0.2f %s" % (item.rate, "(Cr)" if item.is_credit else "(Dr)")
			amount = "%0.2f" % (item.rate * item.quantity)
			for item_tax in item.getTaxesOfType("CGST"):
				cgst_rate = item_tax.tax.net_rate if item_tax else 0
				if cgst_rate != 0:
					cgst_value = item.quantity * item.rate * Decimal(cgst_rate) / 100
				net_cgst += Decimal("%0.2f" % cgst_value) * (1 if item.is_credit else -1)
			for item_tax in item.getTaxesOfType("SGST"):
				sgst_rate = item_tax.tax.net_rate if item_tax else 0
				if sgst_rate != 0:
					sgst_value = item.quantity * item.rate * Decimal(sgst_rate) / 100
				net_sgst += Decimal("%0.2f" % sgst_value) * (1 if item.is_credit else -1)
			for item_tax in item.getTaxesOfType("IGST"):
				igst_rate = item_tax.tax.net_rate if item_tax else 0
				if igst_rate != 0:
					igst_value = item.quantity * item.rate * Decimal(igst_rate) / 100
				net_igst += Decimal("%0.2f" % igst_value) * (1 if item.is_credit else -1)
			icd_items = {
				"item_index": index, "item_code": po_code, "item_description": item.description, "make": make,
				"reason": item.reason, "quantity": "%0.3f" % item.quantity, "unit_name": unit_name, "rate": rate,
				"amount": amount, "cgst_rate": "%0.2f" % cgst_rate, "cgst_value": "%0.2f" % cgst_value,
				"sgst_rate": "%0.2f" % sgst_rate, "sgst_value": "%0.2f" % sgst_value, "igst_rate": "%0.2f" % igst_rate,
				"igst_value": "%0.2f" % igst_value, "hsn_code": item.hsn_code}
			note_materials.append(icd_items)
		logger.info('Total Purchase Amount: %s CGST: %s SGST: %s IGST: %s' % (total, net_cgst, net_sgst, net_igst))
	# Tax Calculation
	sorted_taxes = source.getTaxes()
	cascading_tax = Decimal(net_cgst + net_sgst + net_igst)
	for note_tax in sorted_taxes:
		tax_amount = Decimal(note_tax.tax.net_rate) * (
			(Decimal(total + cascading_tax)) if note_tax.tax.is_compound else Decimal(total)) / 100
		cascading_tax += tax_amount
		taxes = {
			'tax_name': note_tax.tax.name, 'tax_rate': note_tax.tax.net_rate,
			'tax_value': '%0.2f (%s)' % (abs(tax_amount), "Cr" if tax_amount > 0 else ("" if tax_amount == 0 else "Dr"))}
		available_taxes.append(taxes)

	total_value = '%0.2f (%s)' % (abs(total), "Cr" if total > 0 else ("" if total == 0 else "Dr"))
	total_cgst_value = '%0.2f %s' % (
			abs(net_cgst), "(%s)" % "Cr" if net_cgst > 0 else ("" if net_cgst == 0 else "Dr"))
	total_sgst_value = '%0.2f %s' % (
			abs(net_sgst), "(%s)" % "Cr" if net_sgst > 0 else ("" if net_sgst == 0 else "Dr"))
	total_igst_value = '%0.2f %s' % (
			abs(net_igst), "(%s)" % "Cr" if net_igst > 0 else ("" if net_igst == 0 else "Dr"))

	grand_total = Decimal(total + cascading_tax + source.round_off * (1 if source.is_credit else -1))
	grand_total_value = "%0.2f (%s)" % (abs(grand_total), "Cr" if grand_total > 0 else ("" if grand_total == 0 else "Dr"))
	if abs(grand_total) > 10000000000:
		grand_total_in_words = ""
	else:
		grand_total_in_words = PDFGenerator.getTotalInWords(value=abs(grand_total), currency=source.currency)
	note_total_details = {
		'total_qty': total_qty, 'total': total_value, 'net_cgst': total_cgst_value, 'net_sgst': total_sgst_value,
		'net_igst': total_igst_value, 'round_off': "%0.2f" % source.round_off, 'grand_total': grand_total_value,
		'grand_total_in_words': grand_total_in_words}
	return note_materials, available_taxes, note_total_details