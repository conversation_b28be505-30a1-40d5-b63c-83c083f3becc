var Mailer = function () {
    this.prepareEmailPopup = function () {
        if ($("#mail_popup_box").text() == "") {
            $("body").append(`<div id="mail_popup_box"  class="modal fade" role="dialog">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                                        <h4 class="modal-title">Send Mail</h4>
                                    </div>
                                    <div id="info_text" class="modal-body">
                                        <div class="form-group">
                                            <label>TO<span class="mandatory_mark"> *</span></label>
                                            <input type="text" class="form-control" id="mail_id" placeholder="Enter E-mail Address" data-enterkey-submit='btn_mailsend' />
                                        </div>
                                        <div class="form-group">
                                            <label>CC</label>
                                            <input type="text" onblur="removeExtraSpace()" class="form-control" id="id_mail_cc" placeholder="CC (multiple E-mail Address should be comma-separated)" data-enterkey-submit='btn_mailsend' />
                                        </div>
                                        <input type="hidden" id="code" value="">
                                        <input type="hidden" id="file_name" value="">
                                        <input type="hidden" id="type" value="">
                                        <input type="hidden" id="se_id" value="">
                                        <input type="hidden" id="note_id" value="">
                                        <input type="hidden" id="issuer" value="">
                                        <input type="hidden" id="subject" value="">
                                        <input type="hidden" id="ledger_name" value="">
                                        <input type="hidden" id="receipt_no" value="">
                                        <input type="hidden" id="receipt_code" value="">
                                    </div>
                                    <div class="modal-footer">
                                        <input style="margin-top:5px;margin-left:30px;" type="button" id="btn_mailsend" value="Send Mail" onclick="sendMail()" class="btn btn-save" >
                                    </div>
                                </div>
                            </div>
                        </div>`);
        }
        return this;
    }

    this.show = function () {
        $("#mail_popup_box").modal("show");
        enterKeySubmitEvent();
        return this;
    }
}

Mailer.prototype.getSupplierMailID = function(id=None, type=None) {
    $("#id_mail_cc").val("");
    $.ajax({
        url: "/erp/commons/json/party/mail_detail/",
        type: "POST",
        dataType: "json",
        data: {"id": id, "type": type},
        success: function (json) {
            $("#mail_id").val(json.mail_id);
            $("#code").val(json.code);
            var code =  $("#code").val().replace(/\//g, "_");
            $("#file_name").val(code +"("+id+").pdf");
            $("#type").val(type);
            $("#issuer").val(json.issuer);
            $("#id_mail_cc").val(json.mail_id_cc);
            if (type=="icd" || type=="note"){
                $("#note_id").val(id);
                $("#receipt_no").val(json.receipt_no);
                $("#receipt_code").val(json.receipt_code);
            }
        }
    });
    return this;
}

Mailer.prototype.getPartyMailID = function(
    file_name=None, type=None, ledger_id=None, mail_id=None, subject=None, ledger_name=None) {
        $("#id_mail_cc").val("");
        $("#mail_id").val(mail_id);
        $("#code").val(ledger_id);
        $("#file_name").val(file_name +".pdf");
        $("#type").val(type);
        $("#subject").val(subject);
        $("#ledger_name").val(ledger_name);
        $("#issuer").val("");
        return this;
}

function removeExtraSpace(){
   var ccMail = $("#id_mail_cc").val().trim().replace(/ /g, "");
   lastCharacter = ccMail.charAt(ccMail.length-1);
   if(lastCharacter == ",") {
        ccMail = ccMail.substring(0, ccMail.length - 1);
   }
   $("#id_mail_cc").val(ccMail)
}

var sendMail = function() {
    var email = $("#mail_id").val().trim();
    $("#id_mail_cc").next(".custom-error-message").remove();
    $("#id_mail_cc").removeClass("error-border");

    var all_mail_string = email;
    if($("#id_mail_cc").val().replace(/ /g, "")!=""){
        all_mail_string = (email + "," + $("#id_mail_cc").val()).replace(/ /g, "");
    }
    all_mail_ids = all_mail_string.split(",");
    cc_mail_ids = $("#id_mail_cc").val().split(",")
    if(validateEmails(all_mail_ids)){
        $("#btn_mailsend").val('Processing...').css({pointerEvents: "none" });
        setTimeout(function(){
            var type = $("#type").val()
            var code = $("#code").val()
            var se_id = ""
            var issuer = $("#issuer").val()
            var footer = ""
            var include_btns = false
            if(type == "po"){
               var subject = "PO ["+ code + "]"
               var body = "<p>Hi,<p>We are happy to place a new Purchase Order with your organisation.</p><p>PFA - PDF Copy of the Purchase Order - " + code + "</p>"
            }else if(type == "sales"){
               var subject = "Invoice ["+ code + "]"
               var body = "<p>Hi,<p>We are happy to have made a Sale with your organisation.</p><p>PFA - PDF Copy of the Sale Invoice -" + code + "</p>"
            }else if(type == "internal"){
               var subject = "Issue ["+ code + "]"
               var body = "<p>Hi,<p>Please be advised, that the Goods listed in the attached Issue [" + code + "] has been issued to " + issuer + ". </p><p>PFA - PDF Copy of the Delivery Challan -" + code + "</p>"
            }else if(type == "dc"){
               var subject = "DC ["+ code + "]"
               var body = "<p>Hi,<p>Please be advised, that the Goods listed in the attached Delivery Challan [" + code + "] is out for Delivery. You can expect Delivery shortly.</p><p>PFA - PDF Copy of the Delivery Challan -" + code + "</p>"
            }else if(type == "oa"){
               var subject = "Order Acknowledgement ["+ code + "]"
               var body = "<p>Hi,<p>We are happy to have receive a Sale Order from your organisation.</p><p>PFA - PDF Copy of the Order Acknowledgement -" + code + "</p>"
            }
            else if(type == "se"){
               var subject = "Sales Estimate ["+ code + "]"
               var body = "<p>Hi,<p>We are happy to have prepare the below Sales Estimate for you. Kindly review it & convey us your decision. (use the Approve/Reject links for Quick Response).</p>"
               footer = "<p>PFA - PDF Copy of the Sales Estimate -" + code + "</p>"
               se_id = $("#se_id").val()
            }else if(type == "accounts"){
                var subject = $("#subject").val()
                var body = "<p>Hi,<p>Please find the PFA for the " + $("#ledger_name").val() + " ledger"
            }else if(type == "note" || type == "icd"){
                var subject = "Note ["+ code + "]"
                if (type == "icd"){
                    var body = "<p>Hi,<p>Please find details of Note " + code + " raised against " + $("#receipt_code").val() + " below."
                }
                else{
                    var body = "<p>Hi,<p>Please find details of Note " + code + " raised."
                }
                note_id = $("#note_id").val()
                swal({
                    title: "Are you sure?",
                    text: "Do you want to include Acknowledgement buttons in mail?",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#209be1",
                    confirmButtonText: "Yes",
                    cancelButtonText: "No",
                    closeOnConfirm: true
                },
                function(isConfirm) {
                    if (isConfirm) {
                        include_btns = true;
                    }
                    else {
                        include_btns = false;
                    }
                    send_mail(body, subject, footer, type, se_id, note_id, include_btns);
                })
            }

            if(type != "note" && type != "icd"){
                send_mail(body, subject, footer, type, se_id, note_id, include_btns);
            }
            return false;
        },10);
    }
    else {
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();
        var ControlCollections = [
            {
                controltype: 'textbox',
                controlid: 'mail_id',
                isrequired: true,
                errormsg: 'Please enter email address.',
                isemail: 'true',
                emailerrormsg: "Invalid Email Address"
            },
        ];
        var result = JSCustomValidator.JSvalidate(ControlCollections);
        if(!validateEmails(cc_mail_ids) && $("#id_mail_cc").val() != "") {
            var row = `<span class="custom-error-message error-auto-hide">One or more email address is invalid.</span>`;
            $(row).insertAfter($("#id_mail_cc"));
            $("#id_mail_cc").addClass("error-border");
        }
    	return result;
    }
}

function send_mail(body, subject, footer, type, se_id, note_id, include_btns){
    params = {"file_name": $("#file_name").val(), "send_to": $("#mail_id").val().trim(), "cc_to": $("#id_mail_cc").val().split(","), "code": $("#code").val(), "body":body, "subject":subject, "footer":footer, "type": type, "se_id": se_id, "note_id": note_id, "include_btns": include_btns, "receipt_no": $("#receipt_no").val()};
    $.ajax({
        url: "/erp/commons/json/send_mail/",
        type: "POST",
        dataType: "json",
        data:JSON.stringify(params) ,
        success: function (json) {
            $("#mail_popup_box").modal('hide');
            $("#btn_mailsend").val('Send Mail').css({pointerEvents: "" });
            swal("Success", "Mail has been sent successfully", "success");
        },
        error: function (xhr, errmsg, err) {
            console.log(xhr.status + ": " + xhr.responseText);
            $("#btn_mailsend").html('Send Mail').css({pointerEvents: "" });
        }
    });
}