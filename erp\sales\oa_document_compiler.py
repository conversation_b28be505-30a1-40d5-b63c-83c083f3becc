"""
"""
import base64
import datetime
from decimal import Decimal
from math import ceil

import pdfkit
from django.template import Context
from django.template.loader import get_template
from num2words import num2words

from erp import helper, DEFAULT_MAKE_ID
from erp.masters.backend import MasterService
from erp.models import OA
from erp.sales import logger
from util.document_compiler import PDFGenerator

__author__ = 'saravanan'

from util.helper import getAbsolutePath

TARGET_PATH = '/site_media/tmp/oa.pdf'


class OAPDFGenerator(PDFGenerator):
	"""
	Generates a PDF replica of the Invoice that will be available printed on a pre-printed sheet.
	"""

	def __init__(self, order_acknowledgement=None, target_file_path=TARGET_PATH):
		super(OAPDFGenerator, self).__init__(target_file_path)
		self.order_acknowledgement = order_acknowledgement

	def getOAMaterialDetails(self, source=None):
		available_materials = []
		available_taxes = []
		material_make = ""
		total_qty = Decimal(0)
		total = Decimal(0)
		for material in source.items:
			material_drawing_no = material.item.drawing_no
			material_name = material.item.name
			if material.make_id != DEFAULT_MAKE_ID:
				part_no = helper.getMakePartNumber(
					enterprise_id=material.enterprise_id, item_id=material.item_id, make_id=material.make_id)
				material_make = "[%s%s]" % (material.make.__repr__(), (" - %s" % part_no) if part_no else "")
			hsn_code = material.hsn_code if material.hsn_code and material.hsn_code != "" else ""
			material_quantity = material.quantity
			material_unit = material.item.unit.unit_name
			material_rate = material.price
			if material.alternate_unit_id:
				scale_factor = helper.getScaleFactor(
					enterprise_id=material.enterprise_id, item_id=material.item_id,
					alternate_unit_id=material.alternate_unit_id)
				material_unit = helper.getUnitName(enterprise_id=material.enterprise_id, unit_id=material.alternate_unit_id)
				if scale_factor:
					material_quantity = material.quantity / scale_factor
					material_rate = material.price * scale_factor
			total_qty += Decimal(material_quantity)
			material_discount = material.discount
			material_taxable_value = "{:0.2f}".format(Decimal(material_rate) * Decimal(material_quantity) * (100 - Decimal(material_discount)) / 100)
			total += Decimal(material_taxable_value)
			oa_material = {
				"material_drawing_no": material_drawing_no if material_drawing_no else "", "material_name": material_name, "material_make": material_make,
				"hsn_code": hsn_code, "material_quantity": material_quantity, "material_unit": material_unit,
				"material_rate": "%0.2f" % material_rate, "material_discount": "%0.2f" % material_discount,
				"material_taxable_value": material_taxable_value}
			available_materials.append(oa_material)


		sorted_taxes = source.getTaxes()
		tax_value = 0
		total_value = total
		for oa_tax in sorted_taxes:
			if oa_tax.tax.is_compound:
				tax_value = Decimal(total_value) * Decimal(oa_tax.tax.net_rate / 100)
			else:
				tax_value = Decimal(total) * Decimal(oa_tax.tax.net_rate / 100)
			total_value = total_value + tax_value
			taxes = {'tax_name': oa_tax.tax.name, 'tax_rate': oa_tax.tax.net_rate, 'tax_value': round(tax_value, 2)}
			available_taxes.append(taxes)
		return available_materials, total_qty, available_taxes

	def generatePDF(self, source=None, template_config=None, doc_status="", logged_in_user=None, updated_banner_image=None):
		try:
			logger.info('Generating PDF for Order Acknowledgement: %s' % source.id)
			countries = MasterService().getCountries()
			if source.enterprise.images and source.enterprise.images.logo and source.enterprise.images.logo != "":
				logo = "data:image/%s;base64,%s" % (source.enterprise.images.ext, base64.encodestring(source.enterprise.images.logo))
			else:
				logo = ""

			country_list = []
			enterprise_country = ""
			for country in countries:
				if source.enterprise.country_code == country.code:
					enterprise_country = country.name
				country_list.append({"country_code": country.code, "country_name": country.name})

			if source.enterprise.address_2 and source.enterprise.address_2 != "":
				address = source.enterprise.address_1 + ", " + source.enterprise.address_2
			else:
				address = source.enterprise.address_1

			enterprise_address = "{address}, {city}, {pin_code} {state}, {country}".format(
				address=address, city=source.enterprise.city, pin_code=source.enterprise.pin_code, state=source.enterprise.state,
				country=enterprise_country.upper())

			cin_detail = ""
			gst_detail = ""
			for item in source.enterprise.registration_details:
				if item.label.find("CIN") != -1:
					cin_detail = item.details

				if item.label.find("GST") != -1:
					gst_detail = item.details
			oa_date = str(datetime.datetime.strptime(str(source.last_modified_on), '%Y-%m-%d %H:%M:%S')) if source.last_modified_on is not None and source.last_modified_on != '0000-00-00 00:00:00' else source.last_modified_on
			po_date = source.po_date.strftime("%d-%b-%Y") if source.po_date else ""
			delivery_due_date = source.delivery_due_date.strftime("%d-%b-%Y") if source.delivery_due_date else ""
			se_date = str(datetime.datetime.strptime(str(source.se_date), '%Y-%m-%d %H:%M:%S').strftime("%d-%b-%Y")) if source.se_date is not None and source.se_date != '0000-00-00 00:00:00' else source.se_date
			if source.status == OA.STATUS_APPROVED:
				oa_date = str(datetime.datetime.strptime(str(source.approved_on), '%Y-%m-%d %H:%M:%S')) if source.approved_on is not None and source.approved_on != '0000-00-00 00:00:00' else source.approved_on

			oa_item_details, total_quantity, oa_taxes = self.getOAMaterialDetails(source=source)
			appendix_pages = 0
			item_count = len(source.items)
			if item_count > 6:
				appendix_pages = ceil(item_count / 10.0)
			material_value = 0
			for material in source.items:
				material_value += Decimal(round(material.quantity * material.price * (100 - material.discount) / 100, 2))
			total_value = Decimal(material_value)
			total_in_words = PDFGenerator.getTotalInWords(value=abs(source.grand_total), currency=source.currency)

			item_count = len(source.items) + len(source.non_stock_items)
			logger.info('No of Materials: %s' % item_count)

			context = Context({
				'form_name': 'Order Acknowledgement', 'enterprise_logo': logo, 'enterprise_address': enterprise_address, 'source': source,
				'oa_no': source.getInternalCode(), 'oa_date': oa_date, 'cin_detail': cin_detail, 'gst_detail': gst_detail,
				'po_date': po_date, 'delivery_due_date': delivery_due_date, 'oa_item_details': oa_item_details,
				'total_quantity': total_quantity, 'total_value': "%0.2f" % total_value,
				'oa_taxes': oa_taxes, 'total_in_words': total_in_words, 'appendix_pages': appendix_pages,
				'se_no': source.se.getInternalCode() if source.se else "", 'country_list': country_list,
				'se_date': se_date, 'template_title': "OA Document"})

			options = {
				'page-size': 'A4', 'orientation': 'Portrait', 'margin-top': '8', 'margin-right': '8', 'margin-bottom': '15',
				'margin-left': '7', 'title': source.getCode(), '--header-right': 'Page [page] of [topage]',
				'--header-font-size': '8'}

			css = [
				getAbsolutePath('/site_media/css/bootstrap.css'), getAbsolutePath('/site_media/css/invoice_template_preview.css'),
				getAbsolutePath('/site_media/css/invoice_template.css')]

			template_src = getAbsolutePath('/templates/sales/oa_print.html')

			template = get_template(template_src).render(context)
			data = pdfkit.from_string(template, False, options=options, css=css)
			return data
		except Exception as e:
			logger.exception('Something went wrong while creating PDF self.canvas - %s' % e)
