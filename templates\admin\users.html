{% extends 'admin/sidebar.html' %}
{% block user_profile %}
{% if access_level.view %}
<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/multi-select.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/plugin/image_cropper/css/imgareaselect-default.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/plugin/image_cropper/css/jquery.awesome-cropper.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/multi-select.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/plugin/image_cropper/js/jquery.imgareaselect.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/plugin/image_cropper/js/jquery.awesome-cropper.js?v={{ current_version }}"></script>
<style type="text/css">
	.rounded_chk + label:before {
		border-radius: 50px;
	}

	.rounded_chk:checked + label:before {
		border-radius: 50px;
	}

	.styled-checkbox[readonly='readonly'] + label:before {
		background: #ccc;
	}

	.styled-checkbox[readonly='readonly'] + label,
	.styled-checkbox[readonly='readonly'] {
		pointer-events: none;
	}

	.multi-wrapper .non-selected-wrapper, .multi-wrapper .selected-wrapper {
	    height: 300px;
	}	

	.existing-user-container {
		border:  1px solid transparent;
		height: 150px;
	}

	.existing-user-container:hover {
		border: dashed 1px #666;
		opacity: 0.9;
	}

	.cropped_image {
	    border:  dashed 1px transparent;
	    padding:  4px;
	}

	.cropped_image:hover {
	    border:  dashed 1px #999;
	}

	.party-profile-pic .awesome-cropper .progress + .cropped_image {
		max-width: 150px !important;
    	max-height: 150px !important;
	}
	

	.party-sign-pic .awesome-cropper .progress + .cropped_image {
		max-width: 240px !important;
    	max-height: 90px !important;
	}
	.checkbox{
	margin-top:0px !important;
	margin-bottom: 0px !important;
	}
</style>
<div class="right-content-container">
	<input hidden="hidden" value="{{user.is_super.value}}" id="superUser">
	<div class="page-title-container">
		<span class="page-title">Users</span>
	</div>
	<div class="page-heading_new" style="margin-right: 15px; margin-left: 15px;">
		<span class="page_header hide"></span>
		{% if access_level.edit %}	
			<a data-toggle="tab" href="#tab2" class="btn btn-new-item pull-right create_user hide" data-tooltip="tooltip" title="Create New User"><i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span></a>
			<span class="btn btn-new-item pull-right create_user_link" data-tooltip="tooltip" title="Create New User" onclick="validateUserCount();"><i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span></span>
		{% else %}
			<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in Admin Module. Please contact the administrator.">
				<a class="btn btn-new-item pull-right disabled_create_user disabled"><i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span></a>
			</div>	
		{% endif %}	
	</div>
	<div class="container-fluid">
		<div class="row">
			<div class="col-lg-12">
				<div class="content_bg">
					<div class="contant-container-nav hide">
						<ul class="nav nav-tabs list-inline">
							<li class="active"><a href="/erp/admin/user/">VIEW</a></li>
							<li>{% if user.username.value != '' %}
							        <a data-toggle="tab" href="#tab2">EDIT</a>
							    {%else%}
							        <a data-toggle="tab" href="#tab2">ADD</a>
							    {%endif%}
							</li>
						</ul>
					</div>
					<div class="tab-content">
						<input type="hidden" id="last_saved_user_detail" value="{{last_saved_user_detail}}"/>
						<div id="tab1" class="tab-pane fade in active">
							<div class="col-sm-12">
								<div class="csv_export_button">
				                    <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#user_list'), 'User_List.csv']);" data-tooltip="tooltip" title="Download User List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
				                </div>
								<table class="table table-bordered custom-table table-striped" id="user_list" style="width: 100%;">
									<thead>
									<tr>
										<th width="5%">S.No</th>
										<th width="25%">First Name</th>
										<th width="25%">Last Name</th>
										<th width="35%">e-Mail</th>
										{% if access_level.edit %}
											<th width="10%">Status</th>
										{% endif %}
									</tr>
									</thead>
									<tbody>
										{% for a_user in users %}
										<tr bgcolor="#ececec" align="center">
											<td align="center">{{ forloop.counter }}.</td>
											<td align="left">{{ a_user.first_name }}</td>
											<td align="left">{{ a_user.last_name }}</td>
											<td align="left">
												<a class="edit_link_code" onclick="editUserRow('{{ a_user.email }}');"> {{a_user.email}} </a>
												<span class="pull-right">
													<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit'
				                                            onclick="editUserRow('{{ a_user.email }}');">
			                                                <i class='fa fa-pencil'></i>
		                                            </span>
		                                            <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit in New Tab' onclick="editUserRow('{{ a_user.email }}', '_blank');" >
		                                                <i class='fa fa-external-link'></i>
		                                            </span>
		                                        </span>
											</td>
											{% if access_level.edit %}
											<td>
												{% if not a_user.is_super %}
												<form action="/erp/admin/user/delete/" method="post" id="delete_{{ a_user.username }}">{% csrf_token %}
													{% if a_user.is_active %}
													<a href="#" deleteid='deleteUser_{{ a_user.username }}' onclick="DeleteUser('deleteUser_{{ a_user.username }}');">
														<label class="switch">
														  <input type="checkbox"  checked>
														  <span class="slider round"></span>
														</label>
													</a>
													<input type="hidden" value="{{ a_user.username }}" name="delete_username"/>
													<input type="hidden" value="{{ a_user.email }}" name="delete_user_by_email"/>
													<input type="submit" value="Delete" hidden="hidden" id="deleteUser_{{ a_user.username }}">
													{% else %}
													<a href="#" Activateid='ActivateUser_{{ a_user.username }}' onclick="ActivateUser('activateUser_{{ a_user.username }}');">
														<label class="switch">
														  <input type="checkbox">
														  <span class="slider round"></span>
														</label>
													</a>
													<input type="hidden" value="{{ a_user.username }}" name="activate_username"/>
													<input type="hidden" value="{{ a_user.email }}" name="activate_user_by_email"/>
													<input type="submit" value="Activate" hidden="hidden" id="activateUser_{{ a_user.username }}">
													{% endif %}
												</form>
												{% endif %}
											</td>
											{% endif %}
										</tr>
										{% endfor %}
									</tbody>
								</table>
							</div>
						</div>
						<div id="tab2" class="tab-pane fade container">
							{% if user.id.value != None and user.id.value != '' %}
                                <script type="text/javascript">$(".page_header").html('<span class="header_current_page"> {{ user.email.value }}</span>');</script>
                            {% else %}
                                <script type="text/javascript">$(".page-title").text('Users');</script>
                            {% endif %}
							<div class="add_table">
								<form action="/erp/admin/user/save/#table_tab2" id="add_user_form" method="post">
									<a href="/erp/admin/user/" class="btn btn-add-new pull-right view_user hide" data-tooltip="tooltip" title="Back" style="margin-right: 20px;">
										<i class="fa fa-arrow-left"></i>
									</a>
									<a class="btn btn-add-new pull-right view_exp_config hide" style="margin-right: 8px" data-toggle="modal" data-target="#expense_heads_modal" data-tooltip="tooltip" title="Expense Configuration">
										<i class="fa fa fa-cogs" aria-hidden="true"></i>
									</a>
									{%csrf_token%}
									<div class="">
										<div class="col-sm-12 remove-padding" style="margin-top: 15px;">
											<div class="form-group">
												<div class="col-sm-7 new-user-container {% if user.id.value != None %}hide{% endif %}">
													<div class="floating-label col-sm-8 remove-left-padding">
										      			{% if user.id.value != None %}
										      				<div style="font-size: 22px;margin-bottom: 12px;">{{ user.email.value }}</div>
										      				<span class="hide">{{ user.email }}</span>
										      			{% else %}
										      				{{ user.email }}
											      			<label >Email Address<small style="font-size: 11px; text-transform: initial; letter-spacing: 0.5px;"> (username) </small><span class="mandatory_mark"> *</span></label>
										      			{% endif %}
										      		</div>
										      		<div class="col-sm-4 checkbox remove-chk-padding xs-styled-checkbox xs-styled-checkbox-slim" style="width: 154px; padding: 0; float: right; text-align: right; margin-top: 4px; margin-right: 15px;">
														{{ user.is_super }}
														<label for="id_user-is_super">Super User</label>
													</div>
													<div class="floating-label col-sm-6 remove-left-padding">
														{{ user.first_name }}
											      		<label >First Name<span class="mandatory_mark"> *</span></label>
										      		</div>
										      		<div class="floating-label col-sm-6 remove-left-padding">
											      		{{ user.last_name }}
											      		<label >Last Name<span class="mandatory_mark"> *</span></label>
										      		</div>
												</div>
												{% if user.id.value != None %}
													<div class="col-sm-7 existing-user-container">
														{% if access_level.edit %}
															<i class="fa fa-pencil hide" role="button" onclick="editUserDetails()" aria-hidden="true" id="edit-profile" style="font-size: 24px; position: absolute; right: 3px; margin-top: 3px; z-index: 10025; padding: 3px;"></i>
														{% endif %}
														<span style="font-size: 22px">{{ user.first_name.value }} {{ user.last_name.value }}</span>
														{% if user.is_super.value %}
															<span style="font-size: 12px;"> (Super User)</span>
														{% endif%}
														<br />
														<span style="font-size: 16px">{{ user.email.value }}</span><br />
														
													</div>
												{% endif %}
												<div class="col-sm-5">
													<div class="party_upload party-profile-pic col-sm-5">
														<i class="fa fa-times remove_image hide" aria-hidden="true" onclick="removePicImage('profile')"></i>
														<div class="btn profile_image" role="button" style="width: 150px;height: 150px;border: dashed 1px #ccc; padding-top: 50px;">
															<i class="fa fa-upload" aria-hidden="true"></i>
																<br/>Profile Image
														</div>
														<input id="id_party-pic" name="party-picture" type="hidden" value="{{user.profile_img.value}}">
														<div class="profile_image upload_camera hide">
															<i class="fa fa-camera fa-4" aria-hidden="true"></i>
														</div>
														<!-- <div id="profile_image" class="col-sm-12" style="padding-top: 15px;">
															<img src="{{user.profile_img.value}}" style="max-height: 150px; max-width: 150px;" >
														</div> -->
													</div>
													<div class="party_upload party-sign-pic col-sm-6">
														<i class="fa fa-times remove_image hide" aria-hidden="true" onclick="removePicImage('signature')"></i>
														<div class="btn profile_image" role="button" style="width: 240px;height: 90px;border: dashed 1px #ccc; padding-top: 23px;">
															<i class="fa fa-upload" aria-hidden="true"></i>
																<br/>Signature
														</div>
														<input id="id_party-sign" name="party-sign" type="hidden" value="{{user.signature.value}}">
														<div class="profile_image upload_camera hide">
															<i class="fa fa-camera fa-4" aria-hidden="true"></i>
														</div>
														<!-- <div id="signature" class="col-sm-12" style="padding-top: 15px;">
															<img src="{{user.signature.value}}" style="max-height: 150px; max-width: 150px;" >
														</div> -->
													</div>
												</div>

												{{ user.landing_url }}
												{{ user.id }}
												{{ user.enterprise_id }}
											</div>
											<div class="form-group col-sm-6 hide">
												<label>Nickname<span class="mandatory_mark"> *</span></label>
												<!--Render the field in non-editable for Edit and editable for Add -->
												{% if user.id.value != None %}
												<input type="text" class="form-control" disabled   value="{{ user.username.value }}" id="id_user-username" name="username_display"/>
												<!-- Disabled fields are not read in POST, hence the hidden field -->
												<input type="text" value="{{ user.username.value }}"
													   name="user-username" id="id_hidden_username"
													   hidden="hidden"/>
												{% else %}
												{{ user.username }}
												{% endif %}
											</div>
											
											
											<div hidden="hidden">
												{{ user.is_active }}
												{{ user.id }}
											</div>
											<div id="expense_heads_modal" class="modal fade" role="dialog">
											  	<div class="modal-dialog" style="width: 1200px;">
											    	<div class="modal-content">
											      		<div class="modal-header">
											        		<button type="button" class="close" data-dismiss="modal">&times;</button>
											        		<h4 class="modal-title">Expense Configuration</h4>
											      		</div>
											  			<div class="modal-body">
											        		<div class="form-group col-sm-6">
																<label class="title_txt">Claim Heads<span class="mandatory_mark for_expense_edit hide"> *</span></label>
																{{ user.claim_heads }}
															</div>
															
															<div class="form-group col-sm-6">
																<label class="title_txt">EXPENSE HEADS<span class="mandatory_mark for_expense_edit hide"> *</span></label>
																{{ user.expense_heads }}
															</div>
											      		</div>
												  		<div class="modal-footer">
												  			<div class="col-sm-6 text-left" style="color: #dd4b39;">
																<small>Note: Click Left/ Right item to Add/ Remove items.</small>
															</div>
															<div class="col-sm-6">
												        		<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
												        	</div>
												      	</div>
													</div>
											  	</div>
											</div>
										</div>
									</div>
									<div class="col-sm-12 table-responsive" style="display:inline;width:60%">
										<label>Permissions</label>
										<table class="table table-bordered table-striped custom-table" id="user_permission">
											<thead>
												<tr>
													<th width="10%" rowspan="2">Landing Page</th>
													<th width="35%" rowspan="2">Module</th>
													<th width="50%" colspan="5">Access Levels</th>
												</tr>
												<tr>
													<th width="10%">
														<div style="font-size:8px">View</div>
													</th>
													<th width="10%">
														<div style="font-size:8px">Edit</div>
													</th>
													<th width="10%">
														<div style="font-size:8px">Approve</div>
													</th>
													<th width="10%">
														<div style="font-size:8px">Alert</div>
													</th>
												</tr>
											</thead>
											{{ permissions.management_form }}
											{% for permission in permissions.forms %}
											<tr align="center">
												<td class="chkcase  text-center">
													<input type="radio" name="chk_landing_url" value="{{permission.landing_url.value}}" onclick="landing_url()" class="styled-checkbox chk-wo-margin rounded_chk" id="chk_landing_url_{{ forloop.counter }}">
													<label for="chk_landing_url_{{ forloop.counter }}"></label>
												</td>
												<td align="left"> {{ permission.module_code.value }}</td>
												<!-- Flags are rendered in the order View, Edit, Approve, Delete, Alert/Notify
													The render order can be changed in UserPermissionForm class
												-->
												<div align="center" >{{permission.bit_flags}}</div>
												<td hidden="hidden">
													{{ permission.module_code}}
													{{ permission.user_id }}
													{{ permission.enterprise_id }}
												</td>
											</tr>
											{% endfor %}
										</table>
									</div>
									<div class="col-sm-5" style="display:inline;width:40%" id="location_access">
										<div style="border: 1px solid #ddd; height: 100px;margin-bottom:10px;padding-top: 7px;">
											<h5 style="text-align:center">LOCATION ACCESS</h5>
											<input type="hidden" value="{{user_locations}}" id="selected_location_list">
											<div class="btn-group" style="margin-left:10%;width:90%">
												<button type="button" class="multiselect dropdown-toggle btn btn-default" data-toggle="dropdown" title="None selected" aria-expanded="false" style="width:90%">
													<span class="multiselect-selected-text">None selected</span> <b class="caret"></b>
												</button>
												<ul class="multiselect-container dropdown-menu" style="width:90%;height:479px;overflow-y:auto" id="locationList">
													<li class="multiselect-item filter" value="0">
														<div class="input-group">
															<span class="input-group-addon" style="margin-right: 24px;margin-top: -23px;"><i class="glyphicon glyphicon-search"></i></span>
															<input class="form-control multiselect-search" type="text" placeholder="Search">
															<span class="input-group-btn">
																<button class="btn btn-default multiselect-clear-filter" type="button"><i class="glyphicon glyphicon-remove-circle"></i></button>
															</span>
														</div>
													</li>
												</ul>
											</div>
										</div>
									</div>

									<div class="col-sm-5" style="display:inline;width:40%" id="project_access">
										<div style="border: 1px solid #ddd; height: 443px; overflow-y: scroll; padding-top: 20px;">
											<h5 style="text-align:center">PROJECT ACCESS</h5>
											<input type="checkbox" onchange="selectAll()" id="selectDeselectAll" style="margin-left: 10%; margin-right: 2%;">
											<label for="selectDeselectAll">Select/Deselect All</label>
											<input type="hidden" value='{{permission_projects}}' id="selected_proj_list">
											<input type="checkbox" class="expandAll">
    										<label for="selectDeselectAll">Expand / Collapse All</label>
										<div id="projectTree">

										</div>
											</div>
									</div>


									<div class="col-lg-12 text-right">
										{% if logged_in_user.is_super or access_level.edit %}
										<a href="#" class="save_user btn btn-save" onclick="user_permission(); update_user_permission();">
											{% if user.username.value != '' %}Update{%else%}Save{%endif%}
										</a>
										{%else%}
										<div data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to add / edit. Please contact the administrator." class="btn btn-save" style="cursor: not-allowed; opacity: 0.5;">
											{% if user.username.value != '' %}Update{%else%}Save{%endif%}
										</div>	
										{% endif %}
										<input type="button" hidden="hidden" id="save_user" value="Save">
										<a href="/erp/admin/user/" class="btn btn-cancel">Cancel</a>
									</div>
									<div id="error_messages" width="50%" style="display:none"
										 class="error_panel">
										<table class="error_display"
											   style="margin:100px 0px 0px 470px;color:#FF0000; background:#00FFFF;"
											   cellpadding="2"
											   cellspacing="2" border="0">
											<tr>
												<td style="align:center">
													<div>
														{% if user.errors %}
														<h4>User Profile Errors:</h4>
														{% endif %}
														{% for field in user.visible_fields %}
														{% if field.errors %}
														<i>{{field.label_tag}}</i>:
														{% endif %}
														<ul>{% for error in field.errors %}
															<li style="list-style-type: none;">{{error}}</li>
															{% endfor %}
														</ul>
														{% endfor %}
														<ul>{% for error in user.errors %}
															<li>{{error}}</li>
															{% endfor %}
														</ul>
														{% if permissions.errors %}
														<h4>Permission Formset Errors:</h4>
														{{ permissions.errors }}
														{% endif %}
														{% for perm_form in permissions.forms %}
														{% for field in perm_form.visible_fields %}
														{% if field.errors %}
														<i>{{field.label_tag}}</i>:
														{% endif %}
														<ul>{% for error in field.errors %}
															<li style="list-style-type: none;">{{error}}</li>
															{% endfor %}
														</ul>
														{% endfor %}
														{% endfor %}
													</div>
												</td>
											</tr>
											<tr>
												<td>
													<input type="text" value='{{ user.errors }}'
														   id="form_errors" hidden="hidden"/>
													<input type="text" value='{{permissions.errors }}'
														   id="formset_errors" hidden="hidden"/>
													<input type="text" value='{{ alertresponse }}'
														   id="form_alert" hidden="hidden"/>
												</td>
											</tr>
											<tr align="center">
												<td><a href="#" id="error_close" class="update1">Close</a></td>
											</tr>
										</table>
									</div>
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<div class="hide">
	<form action="/erp/admin/user/edit/#tab2" method="post" id="user_edit_form">
		{% csrf_token %}
		<input type="hidden" id="edit_user_id" name="email" value="" />
	</form>
</div>
<input type="hidden" id="userCount" value="{{users|length}}" />
<input type="hidden" id="userMaxCount" value="{{user_max_count}}" />
<script language="javascript">
$(function() {
	$("#cmdUpload").bind("click", function () {
        var regex = /^([a-zA-Z0-9\s_\\.\-:])+(.csv|.txt)$/;
        if (regex.test($("#fileUpload").val().toLowerCase())) {
            if (typeof (FileReader) != "undefined") {
                var reader = new FileReader();
                reader.onload = function (e) {
                    var table = $("<table />");
                    var rows = e.target.result.split("\n");
                    var input_string = "0,"+ rows ;
                    $.ajax({
						url : "/erp/masters/customer/import/",
						type : "POST",
						dataType: "json",
						data : input_string ,
						success : function(json) {
								if(json.indexOf("1062") != -1){
									swal("Customer already exists");
								}
								else {
										swal(json);
									}
								window.location.href = "/erp/masters/customer/";
						},
					error : function(xhr,errmsg,err) {
						console.log(xhr.status + ": " + xhr.responseText);
						}
					});
                }
                reader.readAsText($("#fileUpload")[0].files[0]);
            } else {
                swal("This browser does not support HTML5.");
            }
        } else {
            swal("Please upload a valid CSV file.");
        }
    });
    $("#cmdhideUpload").bind("click", function () {
    	hideImportmaterial();
    });
});

$(document).ready(function(){
	fetchAndPopulateLocations();
	$('.multiselect-search').on('keyup', function() {
		var searchText = $(this).val().toLowerCase();
		var items = $('.multiselect-container li:not(.filter)');

		items.each(function() {
			var text = $(this).text().toLowerCase();
			if (text.indexOf(searchText) === -1) {
				$(this).hide();
			} else {
				$(this).show();
			}
		});
	});

	$('.multiselect-clear-filter').on('click', function() {
		$('.multiselect-search').val('');
		$('.multiselect-container li:not(.filter)').show();
	});
	setProjectInMenuUser();
	var url = window.location.href;
    if(url.indexOf('edit') < 0) {
        TableHeaderFixed();
    }
    else {
    	$(".view_user, .view_exp_config").removeClass('hide');
        $(".create_user, .create_user_link, .disabled_create_user, .export_csv").addClass('hide');
        if($("#user_list").hasClass('dataTable')) {
            oTable.destroy();
        }
    }

    $('#id_party-pic').awesomeCropper(
    	{ debug: false,width: 150, height: 150, isAspectRatio: true }
    );

    $('#id_party-sign').awesomeCropper(
    	{ debug: false,width: 150, height: 75, isAspectRatio: true }
    );
    
	$(".profile_image").click(function(){
		$(this).closest(".party_upload").find('.awesome-cropper').find('input').click();
	});


	$.extend($.expr[":"], {
            "contains-ci": function(elem, i, match, array) {
                    return (elem.textContent || elem.innerText || $(elem).text() || "").toLowerCase().indexOf((match[3] || "").toLowerCase()) >= 0;
            }
    });
    if($('#form_errors').val() != '' || ($('#formset_errors').val().search(/[a-z]/ig) >= 0)){
        document.getElementById('error_messages').style.display = "block";
    }
    $('#error_close').click(function(){
        document.getElementById('error_messages').style.display = "none";
    });
    if($('#form_alert').val() != ''){
    	$('#form_alert').val("");
    }

    transformToDummyForm('user_');
	MovableMultiselect();
	ExpenseEditChange();
	landingPageSelection();
	setLandingPageSelection();
	saveUserFunctionality();
	removeCreateUserButton();
	editableByHoverInit();
	setProfileImage();
	{% if not logged_in_user.is_super %}
		$('#id_user-is_super').attr('disabled', true);
		for(var i=0;i<=9;i++){
			$('#id_permission-'+ i +'-bit_flags_1').attr('disabled', true);
			$('#id_permission-'+ i +'-bit_flags_2').attr('disabled', true);
			$('#id_permission-'+ i +'-bit_flags_3').attr('disabled', true);
		}

	{% endif %}

	if ($('#id_user-id').val() != "" && Number($('#id_user-id').val()) == {{logged_in_user.user_id}}){
		if(!$('#id_user-is_super').prop("checked")){
			$('.styled-checkbox').each(function(){
				if($(this).attr("type") != "radio"){
					$(this).attr('readonly', true);
				}
			});
		}
	}
	if($("title").text() == "User") {
		$("title").text("Users")
	}

	if($("#last_saved_user_detail").val()!=undefined){
		if($("#last_saved_user_detail").val()!="" && $("#last_saved_user_detail").val()!="None"){
			var lasMessage = $("#last_saved_user_detail").val();
			var swalType = "warning";
			var swalText = "";
            $("button.confirm").removeClass('processing');
            if(lasMessage.indexOf("updated") >=0) {
            	swalType = "success";
            	swalText = "User Updated Successfully"
            }
            if(lasMessage.indexOf("newly created") >=0) {
            	swalType = "success";
            	swalText = "User Added Successfully"
            }
            swal({
                title: swalText,
                text: $("#last_saved_user_detail").val(),
                type: swalType,
                allowEscapeKey : false,
                allowOutsideClick: false
            },
            function(){
                setTimeout(function(){
                    $( window ).resize();
                },10);
            });
            $("#last_saved_user_detail").val("");
        }
    }
    setTimeout(setUserLocation, 100);
	setTimeout(updateButtonText, 100);
});

function MovableMultiselect() {
	$('#id_user-claim_heads, #id_user-expense_heads').multi();
}

function deleteCustomer(cusCode){
    var confirmDelete = confirm('Do you want to delete Customer: ' + cusCode);
    if (confirmDelete == true)
        clickButton('deleteCustomer_'+cusCode);
}

function showImportmaterial() {
    document.getElementById("importsupplier").style.display = "block";
}

function hideImportmaterial() {
    document.getElementById("importsupplier").style.display = "none";
}

$(function(){
  	var hash = window.location.hash;
  	hash && $('ul.nav a[href="' + hash + '"]').tab('show');

  	$('.nav-tabs a').click(function (e) {
    	$(this).tab('show');
    	var scrollmem = $('body').scrollTop() || $('html').scrollTop();
    	window.location.hash = this.hash;
    	$('html,body').scrollTop(scrollmem);
  	});
});

$(function(){
	$("#id_user-is_super").click(function () {
		if($(this).is(":checked")){
			$('.multi_check').prop('checked', true);
			landingPageSelection();
	    }
	    else {
	        //$('.multi_check').removeAttr("checked");
	    }
	});

	$(".multi_check").click(function(){
		var current_instance = $(this);
		if($(this).val() == 1) {
			if(!$(this).is(":checked")){
				$(this).closest('tr').find('input[value="2"]').prop("checked", false);
				$(this).closest('tr').find('input[value="4"]').prop("checked", false);
				if($(this).closest('tr').find("input[name='chk_landing_url']").is(":checked")) {
					var pageName = $(this).closest('tr').find("td:nth-child(2)").text();
					swal({
                       title: "",
                       text: "The <b>"+ pageName +"</b> page has been used as Landing Page. <br />Do you still want to remove view access?",
                       type: "warning",
                       showCancelButton: true,
                       confirmButtonColor: "#209be1",
                       confirmButtonText: "Yes, do it!",
                       closeOnConfirm: true,
                       closeOnCancel: true
	               },
	               function(isConfirm){
		                if (isConfirm) {
							$("#id_user-landing_url").val('');
		                }
		                else {
			                current_instance.closest('tr').find("input[name='chk_landing_url']").removeAttr("disabled").prop("checked", true);
			                current_instance.prop("checked", true);
		                }
	               });
				}
			}
		}
		
		if($(this).val() == 2) {
			if($(this).is(":checked")){
				$(this).closest('tr').find('input[value="1"]').prop("checked", "checked");
			}
			if(!$(this).is(":checked")){
				$(this).closest('tr').find('input[value="4"]').prop("checked", false);				
			}
		}
		
		if($(this).val() == 4) {
			if($(this).is(":checked")){
				$(this).closest('tr').find('input[value="1"]').prop("checked", "checked");
				$(this).closest('tr').find('input[value="2"]').prop("checked", "checked");
			}
		}
		
		if($(".multi_check").length == $(".multi_check:checked").length) {
			//$("#id_user-is_super").prop("checked", "checked");
		} else {
			$("#id_user-is_super").removeAttr("checked");
		}
		landingPageSelection();
	});
});

function saveUserFunctionality(){
	$(".save_user").click(function(){
		$(".error-border").removeClass('error-border');
		$(".custom-error-message").remove();

		var ControlCollections = [
			{
				controltype: 'textbox',
				controlid: 'id_user-email',
				isrequired: true,
				errormsg: 'Email Address is required.',
				isemail: true,
				emailerrormsg: 'Invalid Email Address'
			},
			{
				controltype: 'textbox',
				controlid: 'id_user-first_name',
				isrequired: true,
				errormsg: 'First Name is required.'
			},
			{
				controltype: 'textbox',
				controlid: 'id_user-last_name',
				isrequired: true,
				errormsg: 'Last Name is required.'
			},
		];
		var result = JSCustomValidator.JSvalidate(ControlCollections);
		if(result) {
			if($("#id_permission-2-bit_flags_1").is(":checked")) {
				if($("#id_user-claim_heads").val() == null || $("#id_user-expense_heads").val() == null) {
					$(".view_exp_config").click();
					swal({
					  	title: "Claim & Expense Head is Required!",
					  	text: "Claim Head & Expense Head is required for the user who have <b>EDIT ACCESS</b> in <b>EXPENSE.</b>",
					  	type: "error"
					});
					result = false;
				}
			}
		}
		if(result) {
			if ($('#id_user-id').val() =="" ){
				var data = {
					user_email: $('#id_user-email').val(),
					username: $('#id_user-username').val()
				}
				$("#loading").show();
				$.ajax({
			        url : "/erp/admin/user/check/",
			        type : "POST",
			        dataType: "json",
			        data :  data,
			        success : function(response) {
			            if (response.has_email){
			                swal("","This Email Address is already register. Please try another one!","warning");
			            }else{
			            	$(".save_user").text('Processing...').addClass('btn-processing');
			                $("#add_user_form").submit();
                            ga('send', 'event', 'Users', 'Create', $('#enterprise_label').val(), 1);
			            }
			            $("#loading").hide();
			        },
			        error : function(xhr,errmsg,err) {
			        	$("#loading").show();
			            //console.log(xhr.status + ": " + xhr.responseText);
			        }
			    });
			}
			else {
				$(".save_user").text('Processing...').addClass('btn-processing');
				$("#loading").show();
				$("#add_user_form").submit();
                ga('send', 'event', 'Users', 'Edit', $('#enterprise_label').val(), 1);
			}

		}
		return result;
	});
}

function DeleteUser(deleteUser){
    selected_user=deleteUser
	swal({
		title: "Are you sure?",
		text: "Do you want to deactivate this User?",
		type: "warning",
		showCancelButton: true,
		confirmButtonColor: "#209be1",
		confirmButtonText: "Yes",
		closeOnConfirm: true
	},
	function(deleteUser) {
      	if (deleteUser) {
      		clickButton(selected_user);
     	}
     	else {
       		window.location.href = "/erp/admin/user/";
     	}
   	}
);

}
function ActivateUser(activateUser){
    selected_user=activateUser
	swal({
		title: "Are you sure?",
		text: "Do you want to Activate this User?",
		type: "warning",
		showCancelButton: true,
		confirmButtonColor: "#209be1",
		confirmButtonText: "Yes",
		closeOnConfirm: true
	},
	function(activateUser){
	    if (activateUser) {
		  	clickButton(selected_user);
		}
		else {
		  	window.location.href = "/erp/admin/user/";
		}
	});
}

var oTable;
var oSettings;

function TableHeaderFixed(){
	if($("#user_list thead").find("th").length >= 5) {
		oTable = $('#user_list').DataTable({
			fixedHeader: false,
	        "scrollY": Number($(document).height() - 230),
	        "scrollX": true,
			"pageLength": 50,
			"search": {
				"smart": false
			},
			columnDefs: [ { orderable: false, targets: [4] }]
		});
	}
	else {
		oTable = $('#user_list').DataTable({
			fixedHeader: false,
	        "scrollY": Number($(document).height() - 230),
	        "scrollX": true,
			"pageLength": 50,
			"search": {
				"smart": false
			}
		});
	}
	oTable.on("draw",function() {
		var keyword = $('#user_list_filter > label:eq(0) > input').val();
		$('#user_list').unmark();
		$('#user_list').mark(keyword,{});
		listTableHoverIconsInit('user_list');
		setHeightForTable();
	});
	oTable.on('page.dt', function() {
	  $('html, body').animate({
		scrollTop: $(".dataTables_wrapper").offset().top - 15
	   }, 'slow');
	  listTableHoverIconsInit('user_list');
	});
	oSettings = oTable.settings();
	listTableHoverIconsInit('user_list');
	$( window ).resize();
}

$(".create_user").click(function(){
	$(this).addClass('hide');
	$(".create_user_link").addClass("hide");
	$(".export_csv").addClass('hide');
	$(".view_user, .view_exp_config").removeClass('hide');
	$(".page-title").html('New User');	
	if($("#user_list").hasClass('dataTable')) {
        oTable.destroy();
    }
});

$("#id_permission-2-bit_flags_1").change(function(){
	ExpenseEditChange();
});

function ExpenseEditChange() {
	if($("#id_permission-2-bit_flags_1").is(":checked")) {
		$(".for_expense_edit").removeClass('hide');
	}
	else {
		$(".for_expense_edit").addClass('hide');
	}
}

function landingPageSelection(){
	$("#user_permission tbody").find("tr").each(function(){
		if($(this).find("td:nth-child(3)").find("input").is(":checked") && $(this).find("td:nth-child(2)").text().trim() != "MASTERS") {
			$(this).find("td:nth-child(1)").find("input").removeAttr("disabled");
		}
		else {
			$(this).find("td:nth-child(1)").find("input").prop("checked", false).attr("disabled", true);
		}
	});
}

function setLandingPageSelection(){
	var landing_url = null;
	{% if user.landing_url.value %}
		landing_url = "{{user.landing_url.value}}";
	{% endif %}
	if(landing_url != null){
		if($("#user_permission tbody").find("input[value='"+landing_url+"']").length > 0 ) {
			$("#user_permission tbody").find("input[value='"+landing_url+"']").prop("checked", true);
		}
	}
}

function landing_url(){
	$("#id_user-landing_url").val($("input[name='chk_landing_url']:checked").val());
}

function removeCreateUserButton() {
	if(Number($("#userCount").val()) >= $("#userMaxCount").val()) {
		$(".create_user").remove();
	}
}

function validateUserCount() {
	if(Number($("#userCount").val()) >= $("#userMaxCount").val()) {
		swal({
           	title: "User Limit Exceeds",
           	text: "You have already profiled <b>"+$("#userCount").val()+" users</b> for your Enterprise! <br />Your can profile a maximum of <b>"+$("#userMaxCount").val()+" Users</b> for Free! <br />To create more User Accounts kindly",
           	type: "warning",
           	showCancelButton: true,
           	confirmButtonColor: "#209be1",
           	confirmButtonText: "Reach Out to Us",
           	cancelButtonText: "Cancel",
           	closeOnConfirm: false,
           	closeOnCancel: true
       	},
       	function(isConfirm){
            if (isConfirm) {
            	if($(".showSweetAlert.visible").find("#btn-sa-confirm").text() =="Reach Out to Us") {
            		$(".showSweetAlert.visible").find("#btn-sa-confirm").blur().addClass("sa-btn-theme-processing").text("Processing...");
            		$(".showSweetAlert.visible").find(".cancel").addClass("hide");
            	}
				var enterprise_id = $("#id_user-enterprise_id").val();
				var user_first_name = $("#id_user-first_name").val();
				var user_last_name = $("#id_user-last_name").val();
				var email = $('#id_user-email').val()
				$.ajax({
					url: "/erp/admin/send_contact_us_mail/",
					type: "post",
					data: {
						enterprise_id: enterprise_id, user_first_name: user_first_name, user_last_name: user_last_name, user_email: email
					},
					success: function(response) {
						if (response.response_message === "Success") {
							swal({
								title: "Mail Status",
								text: response.custom_message,
								type: "success"
							},
							function() {
								location.reload();
							});

						} else {
							swal({title: "", text: response.custom_message, type: "error"});
						}
						$(".showSweetAlert.visible").find("#btn-sa-confirm").removeClass("sa-btn-theme-processing").text("OK");
					},
					error: function(xhr, errmsg, err) {
						console.log(xhr.status + ": " + xhr.responseText);
						$(".showSweetAlert.visible").find("#btn-sa-confirm").removeClass("sa-btn-theme-processing").text("OK");
					}
				});
            }
       	});
	}
	else {
		$(".create_user").trigger("click");
	}
}

function editableByHoverInit() {
	$( ".existing-user-container" ).hover(
	  	function() {
	    	$(this).find(".fa-pencil").removeClass("hide");
	  	}, function() {
	    	$(this).find(".fa-pencil").addClass("hide");
	  	}
	);
}

function setProfileImage(){
	var imageDataSrc = $("#id_party-pic").val();
	if(imageDataSrc !="") {
		$(".party-profile-pic").find(".cropped_image").attr('src',imageDataSrc);
		$(".party-profile-pic").find('.btn.profile_image').addClass('hide');
		$(".party-profile-pic").find(".cropped_image, .remove_image").hover(function(){
		      $(".party-profile-pic").find(".remove_image").removeClass("hide");
		}, function () {
		      $(".party-profile-pic").find(".remove_image").addClass("hide");
	  	});
	}

	var signatureDataSrc = $("#id_party-sign").val();
	if(signatureDataSrc !="") {
		$(".party-sign-pic").find(".cropped_image").attr('src',signatureDataSrc);
		$(".party-sign-pic").find('.btn.profile_image').addClass('hide');
		$(".party-sign-pic").find(".cropped_image, .remove_image").hover(function(){
		    $(".party-sign-pic").find(".remove_image").removeClass("hide");
		}, function () {
		    $(".party-sign-pic").find(".remove_image").addClass("hide");
	  	});
	}
}	

function removePicImage(type) {
	if(type == "profile") {
		$(".party-profile-pic").find(".cropped_image").removeAttr('src');
		$("#id_party-pic").val("");
		$(".party-profile-pic").find('.btn.profile_image').removeClass('hide');
		$(".party-profile-pic").find(".remove_image").addClass("hide");
	}
	else {
		$(".party-sign-pic").find(".cropped_image").removeAttr('src');
		$("#id_party-sign").val("");
		$(".party-sign-pic").find('.btn.profile_image').removeClass('hide');
		$(".party-sign-pic").find(".remove_image").addClass("hide")
	}
}

function editUserDetails() {
	$(".existing-user-container").addClass("hide");
	$(".new-user-container").removeClass("hide");
}

function editUserRow(employeeId,  openTarget="") {
	$("#edit_user_id").val(employeeId);
	$("#user_edit_form").attr("target", openTarget).submit();
}
let userEditProject = [];
let checkedProjects = [];
if($('#selected_proj_list').val()){
userEditProject = JSON.parse($('#selected_proj_list').val());
}
function selectProject(checkbox) {
    const projectId = parseInt(checkbox.id.split('_')[1]);
    const initialStatus = userEditProject.includes(projectId) ? 1 : 0;
    const projectIndex = checkedProjects.findIndex(item => item.project_id === projectId);

    const currentStatus = checkbox.checked ? 1 : 0;
    if (currentStatus !== initialStatus) {
        if (projectIndex === -1) {
            checkedProjects.push({"project_id": projectId, "status": currentStatus});
        } else {
            checkedProjects[projectIndex].status = currentStatus;
        }
    } else {
        if (projectIndex !== -1) {
            checkedProjects.splice(projectIndex, 1);
        }
    }

    console.log("checkedProjects", checkedProjects);
    const allCheckboxes = document.querySelectorAll('input[type="checkbox"][id^="project_"]');
    const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
    const selectAllCheckbox = document.getElementById('selectDeselectAll');
    selectAllCheckbox.checked = allChecked;
}

function defaultSelect() {
    const checkboxes = document.querySelectorAll('.project-checkbox');
    const selectedProjects = [];

    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
        const projectId = parseInt(checkbox.id.split('_')[1]);
        selectedProjects.push(projectId);
    });
    $('#selectDeselectAll').prop('checked', true);
    return selectedProjects;
}

function selectAll() {
    const selectAllCheckbox = document.getElementById('selectDeselectAll');
    const checkboxes = document.querySelectorAll('.project-checkbox');

    if (selectAllCheckbox.checked) {
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
            const projectId = parseInt(checkbox.id.split('_')[1]);
            if (!checkedProjects.includes(projectId)) {
                checkedProjects.push(projectId);
            }
        });
    } else {

        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        checkedProjects = [];
    }
}
const setProjectInMenuUser = async () => {
    const projectList = await getProjects();
    const projectWise = projectList ?projectList.project_wise : null;
    if(!projectWise){
    	$('#project_access').hide();
    }
    const formattedProjectList = formatProjectList(projectList);
    let treeHTML = '';
    function generateTree(parentId) {
        const children = formattedProjectList.filter(project => project.parent_id === parentId);
        const initialDisplay = (parentId === 0 || parentId === 4550) ? 'style="display: block;"' : 'style="display: none;"';
        treeHTML += `<ul ${initialDisplay} style="list-style-type: none;">`;
        children.sort((a, b) => a.name.localeCompare(b.name));
        children.forEach(child => {
			const isChecked = userEditProject.includes(child.id);
            treeHTML += `<li data-id="${child.id}" style="list-style-type: none;">`;
            treeHTML += `${hasChildren(child.id) ? '<i class="fa fa-plus fa-for-arrow" aria-hidden="true" onClick="toggleExpandCollapse(this)" style="margin-right: 10px;font-size: 10px;border: 1px solid black;padding: 1px;"></i>' : ''}`;
            treeHTML += `<input type="checkbox" id="project_${child.id}" name="project_${child.id}" class="project-checkbox" ${isChecked ? 'checked' : ''} onchange="selectProject(this)">`;
            treeHTML += `<label for="project_${child.id}" style="padding:10px;" >${child.name}</label>`;
            generateTree(child.id);
            treeHTML += `</li>`;
        });
        treeHTML += `</ul>`;
    }

    function hasChildren(nodeId) {
        return formattedProjectList.some(project => project.parent_id === nodeId);
    }
    generateTree(0);
    $("#projectTree").html(treeHTML);
     $('#selectDeselectAll').on('change', function() {
        $('.project-checkbox').prop('checked', this.checked);
    });
    function collapseAllTrees() {
        $('#projectTree ul ul ul').css('display', 'none');
        $('i').removeClass('fa-minus').addClass('fa-plus');
    }
     function expandAllTrees() {
        $('#projectTree ul').css('display', 'block');
        $('i').removeClass('fa-plus').addClass('fa-minus');
    }
      $('.expandAll').on('change', function() {
        if ($(this).prop('checked')) {
            expandAllTrees();
        } else {
            collapseAllTrees();
        }
    });
}
	function user_permission(){
			const getMappingList = () => {
        const checkboxes = document.querySelectorAll('.locationCheckbox');
        let mappingList = [];

        checkboxes.forEach((checkbox) => {
            const locationId = parseInt(checkbox.value);
            const status = checkbox.checked ? 1 : 0;

            mappingList.push({
                location_id: locationId,
                status: status
            });
        });

        return mappingList;
    };
    const mappingList = getMappingList();
    console.log(mappingList);

			$.ajax({
					url:'/erp/admin/json/user_location_map/',
					type: 'POST',
					data: {mapping_list: JSON.stringify(mappingList) , user_id : $('#id_user-id').val() , enterprise_id : $('#enterprise_id').val()},
					success: function (response) {
						console.log("response",response);
					}
			});
	}
	function update_user_permission(){
	$.ajax({
					url:'/erp/admin/json/update_project_permission/',
					type: 'POST',
					data: {project_list: JSON.stringify(checkedProjects) , user_id : $('#id_user-id').val()},
					success: function (response) {
					}
			});
	}
	function populateDropdown(values) {
		var dropdownMenu = $('.multiselect-container');
		dropdownMenu.empty();
		dropdownMenu.append('<li><a tabindex="0"><label class="checkbox"><input type="checkbox" style="opacity: 1" id="select-all"><span class="checkmark"></span> Select/Deselect All </label></a></li>');
		values.forEach(function(item) {
			dropdownMenu.append(
				'<li><a tabindex="0"><label class="checkbox" style="white-space: normal;"><input type="checkbox" style="opacity: 1" class="locationCheckbox" value="' + item.id + '"><span class="checkmark"></span><span style="word-wrap: break-word;display: inline-block;"> ' + item.name + ' </span></label></a></li>'
			);
		});
		dropdownMenu.find('input[type="checkbox"]:not(#select-all)').change(function() {
			updateButtonText();

			const checkboxesLocation = document.querySelectorAll('.multiselect-container input[type="checkbox"]:not(#select-all)');
			const allChecked = Array.from(checkboxesLocation).every(checkbox => checkbox.checked);

			const selectAllCheckboxLocation = document.getElementById('select-all');
			selectAllCheckboxLocation.checked = allChecked;
		});

			dropdownMenu.find('li').click(function(e) {
				e.stopPropagation();
			});
			$('#select-all').change(function() {
				var isChecked = $(this).is(':checked');
				dropdownMenu.find('input[type="checkbox"]').prop('checked', isChecked);
				updateButtonText();
			});
			var isSuperUser = document.getElementById('superUser').value === 'True';
			if (isSuperUser) {
				$('#select-all').prop('checked', true).trigger('change');
			}
	}
	function updateButtonText() {
			var selectedItems = $('.multiselect-container input[type="checkbox"]:checked').not('#select-all').length;
			var buttonText = selectedItems > 0 ? selectedItems + ' items selected' : 'None selected';
			$('.multiselect-selected-text').text(buttonText);
	}
	const fetchAndPopulateLocations = async () => {
    const locationListAll = await getLocations();
    const fromLocation = "from";
    locationList = locationListAll[fromLocation]

    if (locationList) {
        const dynamicValues = locationList;
        populateDropdown(dynamicValues);
    } else {
        console.error("No data received");
    }
};

const setUserLocation = async () => {
	if($('#selected_location_list').val()){
		var selectedLocations = JSON.parse($('#selected_location_list').val());
        selectedLocations.forEach(function(location) {
            var checkbox = document.querySelector('.locationCheckbox[value="' + location + '"]');
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    }
	}
</script>
{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
{% endblock %}
