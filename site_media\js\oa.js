// TODO remove static variables in JS
var material_choices = [];
var isDateChanged = false;
var isShortClosed = false;
$(window).load(function(){
    s_no_init();
    var project = JSON.parse(localStorage.getItem('project'));
    actualProjectsBudget($('#id_oa-project_code').val(),$('#id_oa-project_code').find(':selected').attr('project-type'));
    $('#id_oa-project_code').change(function() {
            actualProjectsBudget($(this).val(),$('#id_oa-project_code').find(':selected').attr('project-type'));
            if(project.id == $('#id_oa-project_code').val()){
                $('img#oa_parti_details').addClass('hide');
            }
            else
            {
                $('img#oa_parti_details').removeClass('hide');
            }
    });

    if(project && project.type == 'Secondary'){
        $('.page-title').text($('#id_oa-id').val() ? 'Internal Work Order' : 'New Internal Work Order');
        $('#for-primary-ent-oa_qty').text('QTY');
        $('#id_oa-project_code').val(project.id).trigger("chosen:updated");
        $('#id_oa-type').val('IWO').trigger("chosen:updated");
        $('#oa_type option').prop('selected', false);
        $('#oa_type').val('IWO');
        $('#oa_type option').not(':selected').hide();
        $('#oa_type').trigger("chosen:updated");
        $('#id_oa-type').closest('div.col-sm-4').addClass('for-primary-ent');
        $('#oaNo_header').text('IWO NO.')
        $('#oaDate_header').text('IWO Date & Time');
        $(".chosen-container a").on('click', function(){
        $('#id_oa_party_id_chosen .chosen-results li[data-option-array-index="1"]:contains("+ Add New")').hide();
        });


    }
    $('option[value="IWO"]').hide();
    if(project && project.type == 'Secondary'){
    $('option[value="IWO"]').show();
    }
});
$(document).ready(function() {
    $(".chosen-select").chosen();
    CalculateSubTotal();
    QtyOnkeyPress();
    transformToDummyForm('oa_particular');
    transformToDummyForm('oa_tax');
    transformToDummyForm('tag');
    create_delete_tag_button();
    $("#oa_add").trackChanges();
    $("#id_oa_no_display_div").modal('hide');
    OnLoadTotalCalculation();
    populateUnitOptions("",'id_oa_particular-__prefix__-unit_id');
    populateUnitOptions("",'id_oa_particular-__prefix__-all_units');
    TagitFocus();
    materialListBlurEvent('id_oa_particular-__prefix__-item_name');
    setTimeout(function(){
       $("#id_tag-__prefix__-tag").val("");
    },100);

    $("#cmd_save_draft, #amend_oa").click(function(){
        if(!$('#id_oa-project_code').val()){
        swal('','Please Select Project!','error');
        }
        var isValidated = false;
        var clickedEvent  = $(this).attr('id');

        $(".error-border").removeClass('error-border');
        $(".custom-error-message, .hsn-suggestion").remove();

        var ControlCollections = [
            {
                controltype: 'dropdown',
                controlid: 'id_oa-party_id',
                isrequired: true,
                errormsg: 'Party is required.'
            },
            {
                controltype: 'dropdown',
                controlid: 'id_oa-project_code',
                isrequired: true,
                errormsg: 'Project Name is required.'
            }
        ];

        if ($("#div_con_rate").is(":visible")){
            var control = {
                controltype: 'textbox',
                controlid: 'id_oa-currency_conversion_rate',
                isrequired: true,
                errormsg: 'Required.',
                mindigit: 0.00001,
                mindigiterrormsg: 'Rate cannot be 0.'
            };
            ControlCollections[ControlCollections.length] = control;
        }

        $("#oa_particulars_table").find("tr:visible").each(function(){
            var currentHsn = $(this).find(".oa_hsn_code");
            var currentElementId = currentHsn.attr("id");
            if(currentElementId != "id_oa_particular-__prefix__-hsn_code") {
                var control = {
                    controltype: 'textbox',
                    controlid: currentElementId,
                    isrequired: true,
                    errormsg: 'HSN/SAC is required',
                    ishsn: true,
                    hsnerrormsg: 'Invalid HSN Code',
                    suggestioncontainer: "hsn-wrapper"
                }
                ControlCollections[ControlCollections.length] = control;            
            }
        });
        var result = JSCustomValidator.JSvalidate(ControlCollections);

        if(result){
            if($("#id_oa_particular-__prefix__-item_name").val() != "") {
                if($("#id_oa_particular-__prefix__-price").val() == 0 || $("#id_oa_particular-__prefix__-price").val() == "" || $("#id_oa_particular-__prefix__-quantity").val() == "" || $("#id_oa_particular-__prefix__-quantity").val() == 0 ||  $("#id_oa_particular-__prefix__-item_code").val() == "" || $("#id_oa_particular-__prefix__-item_name").val().indexOf($("#id_oa_particular-__prefix__-item_code").val()) < 0) {
                    AddNewMateriaRow();
                    $("#tabs_stock").trigger('click');
                }
                else {
                    AddNewMateriaRow();
                    isValidated = true;
                }
            }
            else {
                isValidated = true;
            }
            if(isValidated) {
                setTimeout(function(){
                    checkAndSaveOA(clickedEvent);
                },100);
            }
        }else{
              $("html, body").animate({ scrollTop: 0 }, "fast");
        }
    });

    $("#short_close").click(function(){
            short_close()
    });

    showDocumentListener();
    setTimeout(function(){
        $('.report-date').on('hide.daterangepicker', function(ev, picker) {
            isDateChanged = true;
        });
    },100);

    if (isNaN($("#id_oa-party_id").val())) {
        $("#id_oa-party_id").val($('#id_oa-party_id optgroup:eq(0) option:eq(0)').val()).trigger("chosen:updated");
    } else {
        $("#id_oa-party_id").trigger("chosen:updated");
    }
    $("#doc_uploader").click(function(){
    	$("#bill_copy_uploader").click();
    });
    $("#se_party").val($("#id_oa-party_id").val());
    loadSENumbers();
    generateTaxList($("#id_edit_data").val());
    addOATaxClickEvent();
    loadMaterial("onload");
    //ChangePartyCurrency($("#id_oa-party_id").val(),"id_oa-currency_id","id_oa-currency_conversion_rate");
    currencyChangeEvent("onload")
});

function addInternalPrice(current){
    $(`#iwo-project option[value='${$('#id_oa-project_code').val()}']`).hide();
    $("#current-editable-id").val($(current).closest("tr").attr("id"));
    var title = $(current).closest("tr").find('.table_text_box input.text-left').val();


    $("#iwo_modal").find(".modal-title").html('Internal Work Order - <small>' + title +'</small>');
    $("#iwo_modal").modal("show");

    const prevProject = $(current).closest("tr").find(".iwo-project").val();
    const prevPrice = $(current).closest("tr").find(".iwo-price").val();

    $("#iwo-project").val(prevProject).trigger("chosen:updated");
    $("#iwo-price").val(prevPrice)
    const iwoProjectId = $("#iwo-project").val()
    const iwoPrice = $("#iwo-price").val();
    if ($('#approved_status').val() == 1) {
//        $('.component_project[data-id="iwo-project"]').addClass('div-disabled');
        $('.component_project[data-id="iwo-project"]').find('#iwo_project_chosen').addClass('div-disabled');
        $('#iwo-price').prop('disabled', true);
    }
    else
    {
      $('.component_project[data-id="iwo-project"]').removeClass('div-disabled');
      $('#iwo-price').prop('disabled', false);
    }

    console.log("=============", prevProject, prevPrice)
    var currentRowId = $(current).closest("tr").attr("id") + "-internal_oa_id";
    var oa_id_value = $('#id_' + currentRowId).val();
    var item_id_value = $("#id_" + $(current).closest("tr").attr("id") + "-item_id").val();

    if(oa_id_value != "None" || oa_id_value != ''){
        $.ajax({
		        url: '/erp/sales/json/internal_invoice_qty/',
		        type: "POST",
		        dataType: "json",
		        data: {'item_id': item_id_value, internaloa_id: oa_id_value},
		        success: function (data) {
                    if (data['response_code'] == 200){
                        $('#inv_qty_label').removeClass('hide');
                        $('#oa_code_val').text(data['code']);
                        $('#inv_qty').text(data['invoice_qty']);

                    }
                    else
                    {
                    $('#inv_qty_label').addClass('hide');
//                        $('#oa_code_val').text(data['code']);
//                        $('#inv_qty').text(data['invoice_qty']);
                    }
		        }
		});
    }

//    project_id = 'id_' + $("#current-editable-id").val() + '-project_id';
//    console.log(project_id);
//    $('#project-id').val(project_id);
//    project_td = ($('#project-id').val());
//    if($('#' + project_td).val() != "None") {
//       $('.component_project').attr('data-value', $('#' + project_td).val());
//    }
}

    function internalWorkOrderPrice(){
        $(`#iwo-project option[value='${$('#id_oa-project_code').val()}']`).show();
        const iwoProjectId = $("#iwo-project").val()
        const iwoPrice = $("#iwo-price").val();
        const currentRowId = $("#current-editable-id").val();
        $("#oa_particulars_table").find(`#${currentRowId}`).find(".iwo-project").val(iwoProjectId)
        $("#oa_particulars_table").find(`#${currentRowId}`).find(".iwo-price").val(iwoPrice)
        //    project_td = ($('#project-id').val());
        //    $("#id_oa_mat-project_code").on("change", function() {
        //        var selectedValue = $(this).val();
        //        console.log("Selected value:", selectedValue);
        //        $('.component_project').attr('data-value',selectedValue);
        //
        //    });
        if(iwoProjectId != null && iwoPrice != 0){
            $("#oa_particulars_table").find(`#${currentRowId}`).find('img#oa_parti_details').attr('src', '/site_media/images/clipboard-regular.png').css('width', '30px');
        }
        $('#iwo_modal').modal('hide');
    }

function OAPartyChangeEvent(){
    if ($("#id_oa-party_id").val()=='add_new_party') {
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();
        $('#modalPartyDetails').modal('show');
    } else{
        $("#id_oa-se_no_display").next(".btn-group").find(".select").addClass("disabled").attr("title", "Loading... Please wait");
        if($("#id_oa-se_id").val() != ""){
            $("#id_oa-se_id").val(0);
        }
        loadSENumbers();
    }
}

function loadSENumbers(){
    $.ajax({
            url: "/erp/sales/json/get_party_se/",
            type: "post",
            async: false,
            datatype: "json",
            data: {party_id: $("#id_oa-party_id").val(), se_id:$("#id_oa-se_id").val(), oa_type: $("#id_oa-type").val()},
            success: function (response) {
                $('select[name=oa-se_no_display]').html('');
                $('select[name=oa-se_no_display]').append(
                    $(`<option value="0">--select--</option>`)
                );
                if (response.response_message == "Success") {
                    $.each(response.se_list, function(index, se) {
                        $('select[name=oa-se_no_display]').append(
                            $(`<option value="${se.id}" >${se.code}</option>`)
                        );
                    });
                } else {
                    swal("", response.response_message, "error");
                }

                $("#id_oa-se_no_display").next(".btn-group").find(".select").removeClass("disabled").attr("title", "None selected");

                if($("#id_oa-se_id").val() != ""){
                    $("#id_oa-se_no_display").val($("#id_oa-se_id").val()).trigger("chosen:updated");
                    $("#id_oa-se_no").val($("#id_oa-se_id").val());
                    if($("#id_oa-se_id").val() != 0){
                        $("#oa_particular-__prefix__").addClass('hide');
                        $("#add_new_material").addClass('hide');
                    }
                }
                $("#id_oa-se_no_display").trigger("chosen:updated");
            },
            error: function() {
                $("#id_oa-se_no_display").next(".btn-group").find(".select").removeClass("disabled").attr("title", "None selected");
            }
    });

}

function oaPrevNextPaging() {
    if($(".header_current_page").text().indexOf("New") >=0 || $(".header_current_page").text().trim() == "") {
        $(".prev_next_container").remove();
    } else {
        var oaListNav = JSON.parse(localStorage.getItem('oaListNav'));
        if(oaListNav != null) {
            var curOaId = $("#id_oa-id").val();
            for (var i = 0; i < oaListNav.length; i++){
              if (oaListNav[i].oaId == curOaId){
                if(i != 0) {
                    var prevOaId = oaListNav[i-1].oaId;
                    var prevOaNo = oaListNav[i-1].oaNumber;
                }
                if(i != Number(oaListNav.length - 1)) {
                    var nextOaId = oaListNav[i+1].oaId;
                    var nextOaNo = oaListNav[i+1].oaNumber;
                 }
              }
            }
            var PrevNextOA = "";
            if(prevOaId) {
                PrevNextOA += '<form id="oa_edit_'+prevOaId+'" method="post" action="/erp/sales/oa/editOA/">\
                                    <div style="display:none"><input type="hidden" name="csrfmiddlewaretoken" value="'+getCookie('csrftoken')+'"></div>\
                                    <a role="button" onclick="javascript:clickButton(\'editOA_'+prevOaId+'\');" class="btn btn-add-new pull-right btn-data-tooltip" data-tooltip="tooltip" title="Prev. OA: '+prevOaNo+'" style="margin-right: 7px;"><i class="fa fa-backward" aria-hidden="true"></i></a>\
                                    <input type="hidden" value="'+prevOaId+'" name="oa_no">\
                                    <input type="submit" value="Edit" id="editOA_'+prevOaId+'" hidden="hidden">\
                                </form>';
            }
            else {
                PrevNextOA += '<form><a class="btn btn-add-new pull-right disabled btn-data-tooltip" data-tooltip="tooltip" title="Next/ Prev will appear only for the current page" style="padding: 5px 12px; margin-right: 7px;"><i class="fa fa-backward" aria-hidden="true"></i></a></form>';
            }
            if(nextOaId) {
                PrevNextOA += '<form id="oa_edit_'+nextOaId+'" method="post" action="/erp/sales/oa/editOA/">\
                                    <div style="display:none"><input type="hidden" name="csrfmiddlewaretoken" value="'+getCookie('csrftoken')+'"></div>\
                                    <a role="button" onclick="javascript:clickButton(\'editOA_'+nextOaId+'\');" class="btn btn-add-new pull-right btn-data-tooltip" data-tooltip="tooltip" title="Next OA: '+nextOaNo+'" style="margin-right: 7px;"><i class="fa fa-forward" aria-hidden="true"></i></a>\
                                    <input type="hidden" value="'+nextOaId+'" name="oa_no">\
                                    <input type="submit" value="Edit" id="editOA_'+nextOaId+'" hidden="hidden">\
                                </form>';
            }
            else {
                PrevNextOA += '<form><a class="btn btn-add-new pull-right disabled btn-data-tooltip" data-tooltip="tooltip" title="Next/ Prev will appear only for the current page" style="padding: 5px 12px; margin-right: 7px;"><i class="fa fa-forward" aria-hidden="true"></i></a></form>';
            }
            $(".prev_next_container").html(PrevNextOA);
            TooltipInit();
        }
    }
}

function OASuperEditInit(){
    if($("#is_super_user").val().toLowerCase() == 'true') {
        if($(".header_current_page").text().trim() == "" || $(".header_current_page").text().indexOf("New") >=0 || $(".header_current_page").text().indexOf("PF") >=0) {
            $(".super_user_icon, .xsid_number_edit, .super_user_tool_tip").remove();
            $(".super_edit_field").remove();
        }
        else {
            $(".super_user_icon, .super_user_tool_tip").removeClass("hide");
            $('.super_user_tool_tip span').qtip({
               content: {
                    text: "<ul class='ul_for_tool_tip'><li>Super-edit enables modifying the OA Code subject to Duplication Check.</li>\
                               <li>Code format will be 'FY-FY/OTNNNNNNx', <br />eg. '18-19/L000731b'.<br />\
                               FY details - 5 characters (max), <br />OA Type - 1 characters, <br />Number - 6 digits (max), <br />Sub-number - 1 alphabet (max).</li>\
                               <li>Subsequent numbering of OA will pick from the highest of the OA Code, <br/> i.e., gaps will not be filled automatically.</li></ul>",

                    title: 'Super-Edit'
                }
            });
        }
    }
}

function editOANumber(editable) {
    var oaNumber = $(".header_current_page").text().trim();
    var oaNumberSplit = oaNumber.split("/");
    $("#oa_financial_year").val(oaNumberSplit[0]);
    $("#oa_type").val($("#oa_type option[data-val="+oaNumberSplit[1].charAt(0)+"]").val())
    if($.isNumeric( oaNumberSplit[1].substr(-1) )){
        $("#oa_number").val(oaNumberSplit[1].slice(1));
    }
    else {
        $("#oa_number").val(oaNumberSplit[1].slice(1, -1));
        $("#oa_number_division").val(oaNumberSplit[1].substr(-1));
    }
    if (editable) {
        $(".xsid_number_edit").removeClass("hide");
        $(".super_user_icon, .header_current_page").addClass("hide");
    }
}

function DiscardEditOANumber(){
    $(".xsid_number_edit").addClass("hide");
    $(".super_user_icon, .header_current_page").removeClass("hide");
    $("#oa_financial_year, #oa_number, #oa_number_division").val("");
    //$("#oa_type").val(0);
}

function SaveOANumber(){
    if($("#oa_financial_year").val() =="" || $("#oa_number").val() == "" || $("#oa_type").val() == "") {
        $(".save_xsid_error_format").removeClass("hide");
        if($("#oa_financial_year").val() == "") $("#oa_financial_year").addClass("super_edit_error_border");
        if($("#oa_number").val() == "") $("#oa_number").addClass("super_edit_error_border");
        if($("#oa_type").val() == "") $("#oa_type").addClass("super_edit_error_border");
    }
    else {
        $(".save_xsid_error_format").addClass("hide");
        $("#oa_number_division").val($("#oa_number_division").val().toLowerCase());
        $.ajax({
            url: "erp/sales/json/super_edit_oa_code/",
            method: "POST",
            data:{
                oa_id: $("#id_oa-id").val(),
                new_financial_year: $("#oa_financial_year").val().trim(),
                new_oa_type: $("#oa_type").val(),
                new_oa_no: $("#oa_number").val().trim(),
                new_sub_number: $("#oa_number_division").val().trim()
            },
            success: function(response) {
                ga('send', 'event', 'Order Acknowledgement', 'Super-Edit Code', $('#enterprise_label').val(), 1);
                if (response.response_message == "Success") {
                    swal({title: "", text: response.custom_message, type: "success"});
                    DiscardEditOANumber();
                    $(".header_current_page, title").text(response.code);
                } else {
                    swal({title: "", text: response.custom_message, type: "warning"});
                }
            },
            error: function (xhr, errmsg, err) {
               swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
            }
        });
    }
}

function SuperEditOADetails(field){
    $(field).closest("label").next(".div-disabled").removeClass("div-disabled");
    $(field).addClass("hide");
}

oaRowcount = $("#oa_particulars_table tbody").length - 1;

function SaveOAClick(){
    $(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();
    var ControlCollections = [
        {
            controltype: 'dropdown',
            controlid: 'id_oa-type',
            isrequired: true,
            errormsg: 'Type is required.'
        },
        {
            controltype: 'dropdown',
            controlid: 'id_oa-party_id',
            isrequired: true,
            errormsg: 'Customer is required.'
        }

    ];
    var result = JSCustomValidator.JSvalidate(ControlCollections);
    var current_currency = $("#id_oa-currency_id option:selected").text();
    var home_currency = $("#home_currency_id").val();
    var currency_conversion_reminder_message = `Rate of Currency Conversion from ${current_currency} to ${home_currency} is mentioned as 1.00`;
    var confirm_message =  "Do you confirm?";
    var allow_save_flag = true;
    if(current_currency != home_currency){
        if(parseFloat($("#id_oa-currency_conversion_rate").val())==parseFloat(1)){
            confirm_message = currency_conversion_reminder_message + '\n' + confirm_message;
            allow_save_flag = false;
        }
    }
    if(!allow_save_flag){
        allow_save_flag = window.confirm(confirm_message);
        result = allow_save_flag
    }
    if(result) {
        OASaveFunction();
    }
    else {
        $("html, body").animate({ scrollTop: 0 }, "fast");
    }
    return result;
}

function AmendOAClick() {
    if($("#oa_add").isChanged() || isDateChanged || isShortClosed || isTagChanged) {
        if (checkIndentMaterialQty() == true){
            $("#loading").show();
            $("#amend_oa").val('Processing...').addClass('btn-processing');
            editOANumber(false);
            if ($("#id_oa-id").val() == "" || $("#is_super_user").val() == "False"|| $("#id_oa-type").val() == $("#oa_type").val())  {
                $("#oa_add").submit();
                ga('send', 'event', 'Order Acknowledgement', 'Amend', $('#enterprise_label').val(), 1);
            } else {
                $.ajax({
                    url: "erp/sales/json/super_edit_oa_code/",
                    method: "POST",
                    data:{
                        oa_id: $("#id_oa-id").val(),
                        new_financial_year: $("#oa_financial_year").val().trim(),
                        new_oa_type: $("#id_oa-type").val(),
                        new_oa_no: $("#oa_number").val().trim(),
                        new_sub_number: $("#oa_number_division").val().trim()
                    },
                    success: function(response) {
                        if (response.response_message == "Success") {
                            $("#oa_add").submit();
                            ga('send', 'event', 'Order Acknowledgement', 'Amend', $('#enterprise_label').val(), 1);
                        } else {
                            $("#loading").hide();
                            $("#amend_oa").val('Amend').removeClass('btn-processing');
                            swal({title: "", text: response.custom_message, type: "warning"});
                        }
                    },
                    error: function (xhr, errmsg, err) {
                        $("#loading").hide();
                        $("#amend_oa").val('Amend').removeClass('btn-processing');
                       swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
                    }
                });
            }
        }
    }
    else{
        swal("", "Amending this OA is not necessary, as no change has been made for the OA!", "warning");
    }
}

function TagitFocus() {
    $(".tagit-display").click(function(){
        $(this).find('input[type="text"]').focus();
    });
}

$(function() {
    try {
        $('select#id_oa-currency_id').change(function(){
            currencyChangeEvent("onchange");
        });

        $("select#id_oa-se_no_display").change(function(){
            if($("#id_oa-se_no_display").val() != 0 ) {
                $("#id_oa-se_id").val($("#id_oa-se_no_display option:selected").val())
                load_se_details();
            }else{
                $("#add_new_material").removeClass('hide')
                $("#oa_tax_table tbody tr.delete_indv_tax").each(function(){
                    $(this).find("a").click()
                });
                $("#oa_particular-__prefix__").removeClass('hide')
                deleteOAParticulars(null);
                $("#id_oa-se_id").val("");
            }
        });
    } catch(e) {
        console.log(e);
    }
});

function load_se_details(){
    $.ajax({
        url: "/erp/sales/json/get_se_details/",
        type: "post",
        datatype: "json",
        data: {se_id:$("#id_oa-se_no_display").val()},
        success: function (response) {
            $("#id_oa-payment_terms").val(response['se_header']['payment_terms'])
            $("#id_oa-special_instructions").val(response['se_header']['special_instructions'])

            $("#id_oa-currency_id").val(response['se_header']['currency_id']).trigger("chosen:updated");
            if($("#id_oa-currency_id option:selected").text()!=$("#home_currency_id").val()){
                $('#div_con_rate').removeClass("hide");
                $("#id_oa-currency_conversion_rate").val(response['se_header']['conversion_rate'])
            }else {
                $('#div_con_rate').addClass("hide");
                $('#id_oa-currency_conversion_rate').val('1.0');
            }

            for (var index = 0; index < response['se_tags'].length; index++){
                generateFormsetForm('tag');
                var tag_index = parseInt(parseInt($('#id_tag-TOTAL_FORMS').val()) - 1);
                $('#id_tag-' + tag_index + '-tag_label').html(response['se_tags'][index]['tag_name']);
                $('#id_tag-' + tag_index + '-tag').val(response['se_tags'][index]['tag_name']);
                create_delete_tag_button();
            }

            $('#id_se_taxes').val(response['se_taxes']);
            $("#seMaterialTable").find("tr:gt(0)").remove();
            $("#SEButton").find('.modal-footer').remove();
            var s_no = 0
            $.each(response['se_materials'], function (i, item) {
                var item_description ="";
                var is_non_stockable="";
                item_description = item.item_name
                if (item.drawing_no!="" && item.drawing_no!=null){
                    item_description += " - " + item.drawing_no;
                }
                if (item.make_name != ""){
                    var make = constructDifferentMakeName(item.make_name);
                    item_description += " [" + make + "]";
                }
                if (item.is_faulty == 1){
                    item_description += " [Faulty]";
                }
                if (item.mat_type == 1){
                    is_non_stockable = "stock_qty stockable for_oa";
                }
                var hsn_sac = (item.hsn_code=='undefined' || item.hsn_code==null) ? '': item.hsn_code;
                var hsn_code = `<input id="id_sematerial-${i}-hsn_code" type="text" class="form-control text-left" name="sematerial_hsn_code" onkeypress="validateStringOnKeyPress(this, event, 'alphaSpecialChar');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" autocomplete="off" value="${hsn_sac}"/>`;
                var item_code = `<input id="id_sematerial-${i}-item_code" type="text" name="sematerial_item_code" value="${item.item_id}"/>`;
                var item_name = `<input id="id_sematerial-${i}-item_name" type="text" name="sematerial_item_name" value="${item.item_name}"/>`;
                var remarks = '<input id="id_sematerial-'+i+'-remarks" type="text" class="form-control text-left" name="sematerial_remarks" value=""/>';
                var qty = '<input id="id_sematerial-'+i+'-qty" type="text" class="form-control text-right se_quantity" name="sematerial_qty" maxlength="16" onfocus="setNumberRangeOnFocus(this,12,3)" autocomplete="off" value="'+item.quantity.toFixed(3)+'">';
                var make_id = '<input hidden="hidden" id="id_sematerial-'+i+'-make_id" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="sematerial_make_id" value='+item.make_id+' >';
                var is_faulty = '<input hidden="hidden" id="id_sematerial-'+i+'-is_faulty_" name="sematerial_is_faulty" value='+item.is_faulty+' >';
                var is_service = '<input hidden="hidden" id="id_sematerial-'+i+'-is_service_" name="sematerial_is_service" value='+item.is_service+' >';
                var material_type = '<input hidden="hidden" id="id_sematerial-'+i+'-material_type" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="sematerial_material_type" value='+item.mat_type+' >';
                var unit_id = '<input hidden="hidden" id="id_sematerial-'+i+'-unit_id" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="sematerial_unit_id" value='+item.unit_id+' >';
                var scale_factor = '<input hidden="hidden" id="id_sematerial-'+i+'-scale_factor" type="text" class="form-control text-left " name="sematerial_scale_factor" value='+item.scale_factor+' >';
                var alternate_unit_id= '<input hidden="hidden" id="id_sematerial-'+i+'-alternate_unit_id" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="sematerial_alternate_unit_id" value='+item.alternate_unit_id+' >';
                var itemTypeFlag = '';
                if(item.is_service == true){
                    itemTypeFlag = `<span class="service-item-flag"></span>`;
                }
                if(!item.mat_type && item.is_service != true) {
                    itemTypeFlag = `<span class="non_stock-flag"></span>`;
                }
                s_no = s_no +1;
                var rowClass = item.item_code+"_"+item.make_id+"-"+item.is_faulty+"_"+item.is_service;
                var row = ` <tr id='id_sematerial-${i}-row' data-description='${rowClass}'>
                                <td hidden=hidden>${item.item_code} - ${item.drawing_no}</td>
                                <td class='text-center'>
                                    <a href='#' onclick='deleteoaRow(this)'>
                                        <i class='fa fa-trash-o' title='Delete' alt='Delete'></i>
                                    </a>
                                </td>
                                <td id='id_sematerial-${i}-item_description'>${item_description} ${itemTypeFlag}</td>
                                <td>${hsn_code}</td>
                                <td>${remarks}</td>
                                <td>${qty}</td>
                                <td>
                                    <input type='text' name ='sematerial_unitrate' class='form-control text-right mandatory_field se_unitPrice' id='id_sematerial-${i}-price' maxlength='16' onfocus="setNumberRangeOnFocus(this,10,5)" value='${item.unit_rate.toFixed(5)}' />
                                </td>
                                <td>
                                    <input type='text' name ='sematerial_disc' class='form-control text-right se_discount' id='id_sematerial-${i}-discount' maxlength='6' onfocus="setNumberRangeOnFocus(this,3,2)" onblur="validatePercentage(this, event);" value='${item.discount.toFixed(2)}' />
                                </td>
                                <td>${item.unit}</td>
                                <td>
                                    <input type='text' name ='sematerial_amount' value='${item.amount.toFixed(2)}' class='form-control text-right se_amount' tabindex='-1' readonly id='id_sematerial-${i}-amount' />
                                </td>
                                <td class='td_item_drawing_no' hidden=hidden>${item.drawing_no}</td>
                                <td hidden=hidden>${item.enterprise_id}</td>
                                <td hidden=hidden>${item_name}</td>
                                <td hidden=hidden>${make_id}</td>
                                <td hidden=hidden>${is_faulty}</td>
                                <td hidden=hidden>${is_service}</td>
                                <td hidden=hidden>${material_type}</td>
                                <td class='unit_id' hidden=hidden>${unit_id}</td>
                                <td class='alternate_unit_id' hidden=hidden>${alternate_unit_id}</td>
                                <td class='scale_factor' hidden=hidden>${scale_factor}</td>
                                <td hidden=hidden >${item_code}</td>
                                <script type='text/javascript'>
                                    $('#id_sematerial-${i}-qty').blur(function() {
                                        if(parseFloat($('#id_sematerial-${i}-qty').val()) > 0){
                                            $('#id_sematerial-${i}-hsn_code').addClass('mandatory_field');
                                            $('#id_sematerial-${i}-row').css({backgroundColor:'#ffe'});
                                        }
                                        else {
                                            $('#id_sematerial-${i}-hsn_code').removeClass('mandatory_field empty-error-border');
                                            $('#id_sematerial-${i}-row').css({backgroundColor:''})
                                        }
                                    });
                                </script>
                            </tr>`;
                $('#seMaterialTable tbody').append(row);
                $("#seMaterialModal").modal('show');
            });
            CalculateAmount();
        }
    });
}

function deleteoaRow(currentRow) {
    try {
        swal({
            title: "Are you sure?",
            text: "Do you want to delete this Item!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#209be1",
            confirmButtonText: "Yes, delete it!",
            closeOnConfirm: true
        },
        function(){
            var table = document.getElementById("seMaterialTable");
            var rowCount = table.rows.length;
            for (var i = 0; i < rowCount; i++) {
                var row = table.rows[i];
                if (row == currentRow.parentNode.parentNode) {
                    if (rowCount <= 1) {
                        swal("","Cannot delete all the rows.","warning");
                        break;
                    }
                    table.deleteRow(i);
                    rowCount--;
                    i--;
                }
            }
        });

    } catch (e) {
        alert(e);
    }
}

function validateAndAddSEItems() {
    $(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();
    var isValid = true;

    $("#seMaterialTable").find('.se_quantity').each(function(){
        var se_qty = $(this).val();
        if($(this).closest('tr').find(`input[name="sematerial_hsn_code"]`).val() == "" && $(this).val() > 0) {
            $(this).closest('tr').find(`input[name="sematerial_hsn_code"]`).addClass("error-border");
            isValid = false;
        }
    });

    if(isValid) {
        $("#add_new_material").addClass('hide')
        $("#oa_tax_table tbody tr.delete_indv_tax").each(function(){
            $(this).find("a").click()
        });
        $("#oa_particular-__prefix__").addClass('hide')
        deleteOAParticulars(null);
        enterprise_id = $("#id_oa-enterprise_id").val();
        $( "#id_oa_particular-__prefix__-enterprise_id").val(enterprise_id);
        $("#id_oa-se_no").val($("#id_oa-se_no_display option:selected").val());
        var materialtable = document.getElementById("oa_particulars_table");
        var materialrowCount = materialtable.rows.length-1;
        var match = false;
        var row_id = 0;

        var se_table = document.getElementById("seMaterialTable");
        var se_rowCount = $("#seMaterialTable tbody").find("tr").length;
        var oa_index = parseInt(parseInt($('#id_oa_particular-TOTAL_FORMS').val()) - 1) ;
        for (se_index=0; se_index <se_rowCount; se_index++){
           var is_service = document.getElementById('id_sematerial-'+se_index+'-is_service_').value;
           if(document.getElementById('id_sematerial-'+se_index+'-qty').value > 0){
//                generateFormsetFormRowBefore('oa_particular')
               if(is_service == "true") {
                    generateFormsetFormRowAppend('oa_particular', ".item-for-service");
                    $(".item-for-service").removeClass('hide');
                }
                else {
                    generateFormsetFormRowAppend('oa_particular', ".item-for-goods");
                    $(".item-for-goods").removeClass('hide');
                }
                oa_index += 1;
                addSEMaterial(se_index, oa_index);
                s_no_init();
           }
        }

        var se_taxes = $('#id_se_taxes').val();
        var splitted_se_taxes = se_taxes.split(",");
        generateSETaxList($("#id_oa-se_no_display").val())
        $('#id_oa_particular-__prefix__-quantity').val(0.00)
        $('#id_oa_particular-__prefix__-price').val(0.00)
        $("#id_oa_particular-__prefix__-unit_id").val('') ;
        CalculateGrandTotal();
        $('#id_oa_particular-__prefix__-item_name').val("");
        $('#id_oa_particular-__prefix__-item_code').val("");
        $('#id_oa_particular-__prefix__-item_id').val("");
        $("#material_id_hidden").val("")
        $('#id_oa_particular-__prefix__-item_name').focus();
        $("#description_display").text('');
        $("#id_oa_particular-__prefix__-alternate_units").closest('.alternate_unit_select_box').addClass('hide')
        $("#id_oa_particular-__prefix__-unit_id").removeClass('hide')
        $('#chkfaulty').attr('checked', false);
        setTimeout(function(){
                $(".error-border").removeClass('error-border');
        },100);
        $(".custom-error-message").remove();
        CalculateSubTotal();
        $("#id_oa_particular-__prefix__-item_name").removeAttr("readonly");
        $(".material-removal-icon").addClass("hide");
        $("#seMaterialModal").modal('hide');
    }
}

function s_no_init(){
    var i = 1;
    $(".s_no_item:visible").each(function(){
        $(this).text(i++);
    });
}

function addSEMaterial(se_index, oa_index) {
    var new_form_item_code = document.getElementById('id_oa_particular-'+oa_index+'-item_code');
    var new_form_item_id = document.getElementById('id_oa_particular-'+oa_index+'-item_id');
    var new_form_item_name = document.getElementById('id_oa_particular-'+oa_index+'-item_name');
    var new_form_qty = document.getElementById('id_oa_particular-'+oa_index+'-quantity');
    var new_form_units = document.getElementById('id_oa_particular-'+oa_index+'-unit_id');
    var new_form_price = document.getElementById('id_oa_particular-'+oa_index+'-price');
    var new_form_hsn_code = document.getElementById('id_oa_particular-'+oa_index+'-hsn_code');
    var new_form_discount = document.getElementById('id_oa_particular-'+oa_index+'-discount');
    var new_form_amount = document.getElementById('id_oa_particular-'+oa_index+'-amount');
    var new_form_remarks = document.getElementById('id_oa_particular-'+oa_index+'-remarks');
    var new_form_enterprise_id= document.getElementById('id_oa_particular-'+oa_index+'-enterprise_id');
    var new_form_is_faulty = document.getElementById('id_oa_particular-'+oa_index+'-is_faulty');
    var new_form_is_service = document.getElementById('id_sematerial-'+se_index+'-is_service_');
    var new_form_alternate_unit_id = document.getElementById('id_oa_particular-'+oa_index+'-alternate_unit_id');
    var new_form_scale_factor = document.getElementById('id_oa_particular-'+oa_index+'-scale_factor');
    new_form_item_code.value = document.getElementById('id_sematerial-'+se_index+'-item_code').value;
    new_form_item_id.value = document.getElementById('id_sematerial-'+se_index+'-item_code').value;
    new_form_qty.value = document.getElementById('id_sematerial-'+se_index+'-qty').value;
    new_form_is_service.value =document.getElementById('id_sematerial-'+se_index+'-is_service_').value;
    new_form_units.value = $('#id_sematerial-'+se_index+'-unit').text();
    new_form_price.value = document.getElementById('id_sematerial-'+se_index+'-price').value;
    new_form_hsn_code.value = document.getElementById('id_sematerial-'+se_index+'-hsn_code').value;
    new_form_discount.value = document.getElementById('id_sematerial-'+se_index+'-discount').value;
    new_form_amount.value = document.getElementById('id_sematerial-'+se_index+'-amount').value;
    new_form_remarks.value = document.getElementById('id_sematerial-'+se_index+'-remarks').value;
    new_form_enterprise_id.value = $("#id_oa-enterprise_id").val();
    if (document.getElementById('id_sematerial-'+se_index+'-alternate_unit_id').value != 0 && document.getElementById('id_sematerial-'+se_index+'-alternate_unit_id').value != "null" && document.getElementById('id_sematerial-'+se_index+'-alternate_unit_id').value != "" ) {
        new_form_alternate_unit_id.value = document.getElementById('id_sematerial-'+se_index+'-alternate_unit_id').value
        new_form_scale_factor.value = $('#id_sematerial-__prefix__-'+se_index+' option:selected').attr("data-val");
    } else{
        new_form_units.value = $('#id_sematerial-'+se_index+'-unit').text();
    }

    var item_name = $('#id_sematerial-'+se_index+'-item_description').text();
    if ($('#id_sematerial-'+se_index+'-make_id').val() != 1){
        var new_form_make_id = document.getElementById('id_oa_particular-' + oa_index + '-make_id');
        new_form_make_id.value = $('#id_sematerial-'+se_index+'-make_id').val();
    }else{
        var new_form_make_id = document.getElementById('id_oa_particular-' + oa_index + '-make_id');
        new_form_make_id.value = 1;
    }


    if($("#chkfaulty").is(':checked')){
       new_form_is_faulty.value = 1;
       item_name = item_name + "[Faulty]"
    }else{ new_form_is_faulty.value = 0; }

    new_form_item_name.value = item_name
    if(new_form_is_service.value == "true") {
        $('<span class="service-item-flag"></span>').insertAfter( new_form_item_name );
        $(new_form_item_name).addClass("item_text_box");
        $("#oa_particulars_table .service-item-flag").addClass("floated-right-flag");
    }
    document.getElementById('id_oa_particular-__prefix__-item_name').value = '';
    document.getElementById('id_oa_particular-__prefix__-item_code').value = '';
    document.getElementById('id_oa_particular-__prefix__-quantity').value = '';
    document.getElementById('id_oa_particular-__prefix__-price').value = '';
    document.getElementById('id_oa_particular-__prefix__-hsn_code').value = '';
    document.getElementById('id_oa_particular-__prefix__-discount').value = '';
    document.getElementById('id_oa_particular-__prefix__-amount').value = '';
    document.getElementById('id_oa_particular-__prefix__-remarks').value = '';
    document.getElementById('id_oa_particular-__prefix__-unit_id').value ='';
    document.getElementById('id_oa_particular-__prefix__-make_id').value ='';
    document.getElementById('id_oa_particular-__prefix__-alternate_unit_id').value ='';
} /* Copy Form End Here */

function CalculateAmount() {
    $('.se_quantity, .se_unitPrice, .se_discount').blur(function(){
        var cUnitPrice = $(this).closest('tr').find('.se_unitPrice').val();
        var cQty = $(this).closest('tr').find('.se_quantity').val();
        var cDiscount = $(this).closest('tr').find('.se_discount').val();
        var discount_val = ((cUnitPrice * cQty) * (cDiscount/100))
        $(this).closest('tr').find('.se_amount').val(Number((cUnitPrice*cQty) - discount_val).toFixed(2));
    });
}

function checkAndSaveOA(clickedEvent) {
    if ($("#se_party").val() != $("#id_oa-party_id").val()){
        $("#id_oa-se_date").val("");
    }
    if ($("#id_oa-id").val() == "" || $("#is_super_user").val() == "False") {
        if(clickedEvent == 'cmd_save_draft') {
            SaveOAClick();
        }
        else if(clickedEvent == 'amend_oa') {
           AmendOAClick();
        }
        return;
    }

    $.ajax({
        url: "/erp/sales/json/get_oa_linked_message/",
        method: "POST",
        data:{
            oa_id: $("#id_oa-id").val()
        },
        success: function(response) {
            if (response.response_message =="Success") {
                if(response.custom_message == "") {
                    setTimeout(function(){
                        if(clickedEvent == 'cmd_save_draft') {
                            SaveOAClick();
                        }
                        else if(clickedEvent == 'amend_oa') {
                           AmendOAClick();
                        }
                    },100);
                } else {
                    swal({title: "", text: response.custom_message, type: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#209be1",
                        confirmButtonText: "Yes, do it!",
                        closeOnConfirm: true,
                        closeOnCancel: true
                    },
                    function(isConfirm){
                        if (isConfirm) {
                            setTimeout(function(){
                                if(clickedEvent == 'cmd_save_draft') {
                                    SaveOAClick();
                                }
                                else if(clickedEvent == 'amend_oa') {
                                   AmendOAClick();
                                }
                            },100);
                        }
                    });
                }
            } else {
                   swal({title: "", text: response.custom_message, type: "error"});
            }
        },
        error: function (xhr, errmsg, err) {
            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
        }
    });
}

function OASaveFunction(){
    var project = JSON.parse(localStorage.getItem('project'));
    var item_count = parseFloat($("#id_oa_particular-TOTAL_FORMS").val());
    var item_list = 0;

    if($('#id_oa-party_id option:selected').val() == "None"){
        swal('Please Select a Party');
        return;
    }
    for(i=0;i<item_count;i++){
        if(!document.getElementById("id_oa_particular-"+i+"-DELETE").checked){
            item_list = item_list+1
        }
    }

    if(item_list == 0) {
        if(project && project.type == 'Secondary'){
            swal('','Please add atleast one material to the IWO!','error');
        }
        else{
            swal('','Please add atleast one material to the OA!','error');
        }
    }
    else {
        if (checkIndentMaterialQty()== true){
            $("#loading").show();
            $("#cmd_save_draft").val('Processing...').addClass('btn-processing');
            $("#oa_add").submit();
            var event_action = $("#id_oa-id").val() == '' ? 'Create' : 'Update';
            ga('send', 'event', 'Order Acknowledgement', event_action, $('#enterprise_label').val(), 1);
        }
    }
}


function BindMaterialList(material_choices, textid) {
    $("#"+textid).autocomplete({
        source: material_choices,
        minLength: 1,
        focus: function (event, ui) {
            event.preventDefault();
        },
        select: function (event, ui) {
            $(this).closest('tr').find('.oa_material_text').val(ui.item.value);
            $(this).closest('tr').find('.oa_material_code').val(ui.item.code);
            $(this).closest('tr').find('.oa_unitPrice').val(ui.item.rate);
            $(this).closest('tr').find('.oa_unit').text(ui.item.unit);
            $(this).closest('tr').find('.oa_qty').find('input').focus();
        }
   });
}

function create_delete_tag_button(){
    $(".delete_tag").click(function(){
        $(this).closest('li').remove();
    });
}

$(document).on('click', '.browse', function(){
    var file = $(this).parent().parent().parent().find('.file');
    file.trigger('click');
});

$(document).on('change', '.file', function(){
    $(this).parent().find('.form-control').val($(this).val().replace(/C:\\fakepath\\/i, ''));
});

function DeleteMaterial(selectedItem) {
    $(selectedItem).closest('tr').remove();
    CalculateGrandTotal();
}


// Delete The OA Material list Items
function deleteOAParticulars(selectedItem) {
    var project = JSON.parse(localStorage.getItem('project'));
    if(selectedItem != null){
        var inv_qty = document.getElementById('id_' + selectedItem + '-inv_quantity');
        if (inv_qty.value == 0){
            swal({
                title: "Are you sure?",
                text: "Do you want to delete this Item!",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#209be1",
                confirmButtonText: "Yes, delete it!",
                closeOnConfirm: true
            },
            function(){
                var deleteFlag = document.getElementById('id_' + selectedItem + '-DELETE');
                var deleteRow = document.getElementById(selectedItem);
                deleteFlag.checked = true;
                deleteRow.style.display = 'none';
                deleteRow.classList.add("deleted-row");
                CalculateGrandTotal();
                NetTotalCalculation();
            });
        }else{
            errMsg="This OA item Can't be deleted,!"
    		if(project && project.type == 'Secondary'){
    			errMsg="This IWO item Can't be deleted,!"
    			}
            swal("", errMsg, "error");
        }
    }else{
         $("#oa_particulars_table tbody tr").each(function(){
            currentElement = $(this).attr("id");
            if(currentElement != 'oa_particular-__dummy__' && currentElement != 'oa_particular-__prefix__' && currentElement != 'id-total_section'){
                var deleteFlag = document.getElementById('id_' + currentElement + '-DELETE');
                var deleteRow = document.getElementById(currentElement);
                deleteFlag.checked = true;
                deleteRow.style.display = 'none';
                deleteRow.classList.add("deleted-row");
            }
        });
        CalculateGrandTotal();
        NetTotalCalculation();
    }
}

function QtyOnkeyPress() {
    $(".oa_quantity").keypress(function(e){
        if (e.which == 13) {
           AddNewMateriaRow();
        }
    });
}

function OnLoadTotalCalculation() {
    $(".oa_price").each(function(){
        var cUnitPrice = $(this).closest('tr').find('.oa_unitPrice').val();
        var cQty = $(this).closest('tr').find('.oa_quantity').val();
        var cDiscount = $(this).closest('tr').find('.oa_discount').val();
        var discount_val = ((cUnitPrice * cQty) * (cDiscount/100))
        $(this).val(Number((cUnitPrice*cQty) - discount_val).toFixed(2));
    });
    CalculateGrandTotal();
    NetTotalCalculation();
}

function CalculateSubTotal() {
    $('.oa_quantity, .oa_unitPrice, .oa_discount').blur(function(){
        var cUnitPrice = $(this).closest('tr').find('.oa_unitPrice').val();
        var cQty = $(this).closest('tr').find('.oa_quantity').val();
        var cDiscount = $(this).closest('tr').find('.oa_discount').val();
        var discount_val = ((cUnitPrice * cQty) * (cDiscount/100))
        $(this).closest('tr').find('.oa_price').val(Number((cUnitPrice*cQty) - discount_val).toFixed(2));
        CalculateGrandTotal();
    });
}

function CalculateGrandTotal() {
    var gTotal = 0;
    var sNo = 1;
    $('table').find('.oa_price').each(function(){
        if(!$(this).closest("tr").hasClass("deleted-row")) {
            gTotal += Number($(this).val());
        }
    });
    $('.oa_total_amt').text(gTotal.toFixed(2));
    NetTotalCalculation();
}


function NetTotalCalculation() {
    var netTotal = Number($(".oa_total_amt").text());
    var simple_rates = document.getElementsByName('net_rate');
    var compound_rates = document.getElementsByName('net_rate_compound');
    var tax_wise_subtotal = document.getElementsByName('tax');
    var compound_tax_wise_subtotal = document.getElementsByName('tax_compound');
    var simple_assess_rates = document.getElementsByName('asses_rate');
    var compound_assess_rates = document.getElementsByName('asses_rate_compound');
    var item_taxes = document.getElementsByName('item_tax');

    //Calculate The Total with out Tax
	var item_count = parseFloat($("#id_oa_particular-TOTAL_FORMS").val());
	var i,j, total= 0,discount_val =0,assess_total=0,net_value_for_tax=0;
    //	Calculate The Net Value
        for (i=0; i<simple_rates.length; i++){tax_wise_subtotal[i].value=0;}
        for (i=0; i<compound_rates.length; i++){compound_tax_wise_subtotal[i].value=0;}
        var compound_tax_total = 0;
        var compound_tax = 0;
        var net_tax =0 ;

    for(j=0;j<item_count;j++){
    // This if condition is used to avoid deleted item in calculation
        if(!document.getElementById("id_oa_particular-"+j+"-DELETE").checked){
            item_taxes[j].value = 0;
            if ($("#id_oa_particular-"+j+"-discount").val() == ""){
                $("#id_oa_particular-"+j+"-discount").val("0.00");
            }
            if(!isNaN(parseFloat($("#id_oa_particular-"+j+"-quantity").val())) && parseFloat($("#id_oa_particular-"+j+"-quantity").val()) > 0){
                var item_simple_tax = calculateItemTax($("#id_oa_particular-"+j+"-quantity").val(), $("#id_oa_particular-"+j+"-price").val(), $("#id_oa_particular-"+j+"-discount").val(), simple_rates, simple_assess_rates, tax_wise_subtotal, false, 0);
                item_taxes[j].value = parseFloat(item_taxes[j].value) + parseFloat(item_simple_tax) +
                        parseFloat(calculateItemTax($("#id_oa_particular-"+j+"-quantity").val(), $("#id_oa_particular-"+j+"-price").val(), $("#id_oa_particular-"+j+"-discount").val(), compound_rates, compound_assess_rates, compound_tax_wise_subtotal, true, parseFloat(item_simple_tax)));
                net_tax = parseFloat(net_tax) + parseFloat(item_taxes[j].value);
            }
        }
    }
    netTotal = parseFloat(netTotal) +  parseFloat(net_tax)
    $(".net_total").text(netTotal.toFixed(2));
    $("#id_oa-grand_total").val(netTotal.toFixed(2));
}

function calculateAssessValue(quantity, price, discount, assess_rate){
    var value = (parseFloat(quantity) * parseFloat(price)).toFixed(2);
    if (( 100-parseFloat(discount)) > parseFloat(assess_rate)){
        return (value * (100- parseFloat(discount))/100).toFixed(2);
    }else{
        return (value * parseFloat(assess_rate)/100).toFixed(2);
    }
}

function calculateItemTax(quantity, unit_rate, discount, tax_rates, assess_rates, tax_subtotal, is_compound, cascading_item_tax){
    var item_tax = 0;
    var tax_count = tax_rates.length;
    // Calculating the net Taxes
    for (i=0; i<tax_count; i++){
        var item_assess_value = calculateAssessValue(quantity, unit_rate, discount, assess_rates[i].value);
        if (is_compound){
            var sub_total = Math.round((parseFloat(item_assess_value) + parseFloat(cascading_item_tax)) * parseFloat(tax_rates[i].value))/100;
            cascading_item_tax = parseFloat(cascading_item_tax) + parseFloat(sub_total);
        } else {
            var sub_total = Math.round(parseFloat(item_assess_value) * parseFloat(tax_rates[i].value))/100;
        }
        tax_subtotal[i].value = (parseFloat(tax_subtotal[i].value) + parseFloat(sub_total)).toFixed(2);
        item_tax = parseFloat(item_tax) + parseFloat(sub_total);
    }
    return item_tax;
}

function AddNewMateriaRow() {
    $(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();
    $(".suggestion-container").remove();

    var ControlCollections = [
        {
            controltype: 'textbox',
            controlid: 'id_oa_particular-__prefix__-item_name',
            isrequired: true,
            errormsg: 'Material Name is required.'
        },
        {
            controltype: 'textbox',
            controlid: 'id_oa_particular-__prefix__-quantity',
            isrequired: true,
            errormsg: 'Quantity is required.',
            mindigit: 0.001,
            mindigiterrormsg: 'Quantity is required.'
        },
        {
            controltype: 'textbox',
            controlid: 'id_oa_particular-__prefix__-price',
            isrequired: true,
            errormsg: 'Unit Rate is required.'
        },
        {
            controltype: 'textbox',
            controlid: 'id_oa_particular-__prefix__-hsn_code',
            isrequired: true,
            errormsg: 'HSN/SAC is required.',
            ishsn: true,
            hsnerrormsg: 'Invalid HSN Code.'
        }
    ];
    var result = JSCustomValidator.JSvalidate(ControlCollections);
    if(result) {
        if ($('#material_id_hidden').val() != "") {
            if ($("#id_oa_particular-__prefix__-item_code").val()== ""){
    	        swal("","Invalid Material. Please select the Material again.","error");
                $("#id_oa_particular-__prefix__-item_name").addClass('error-border');
    	        return;
    	    }
            enterprise_id = $("#id_oa-enterprise_id").val();
            $( "#id_oa_particular-__prefix__-enterprise_id").val(enterprise_id);


            var materialtable = document.getElementById("oa_particulars_table");
            var materialrowCount = materialtable.rows.length-1;
            var match = false;
            var row_id = 0;

            // Update the repeated values in the invoice material table...
            for(j=0;j<materialrowCount;j++){
                if($('#id_oa_particular-__prefix__-item_code').val() == $('#id_oa_particular-'+parseInt(j) +'-item_code').val() && $('#id_oa_particular-__prefix__-make_id').val() == $('#id_oa_particular-'+parseInt(j) +'-make_id').val() && !document.getElementById("id_oa_particular-"+j+"-DELETE").checked ){
                   match = true;
                   row_id = j;
                }
            }
            if(match){

                if (window.confirm('Item has already been added to the Order. Do you still want to add to its quantity?')) {
                        $("#id_oa_particular-"+row_id+"-quantity").val(parseInt($("#id_oa_particular-__prefix__-quantity").val()) +parseInt($("#id_oa_particular-"+row_id+"-quantity").val()));
                        $("#id_oa_particular-"+row_id+"-amount").val((parseFloat($("#id_oa_particular-"+row_id+"-quantity").val()) *parseFloat($("#id_oa_particular-"+row_id+"-price").val())).toFixed(2));
                        $("#id_oa_particular-__prefix__-amount").val('');
                }
                else {
                    $('#id_oa_particular-__prefix__-amount').val('');
                    CalculateGrandTotal();
                }
                match=false;
            }
            else{
                //generateFormsetFormRowBefore('oa_particular');
                if($("#id_oa_particular-__prefix__-is_service").val() == 1) {
                    generateFormsetFormRowAppend('oa_particular', ".item-for-service");
                    $(".item-for-service").removeClass('hide');
                }
                else {
                    generateFormsetFormRowAppend('oa_particular', ".item-for-goods");
                    $(".item-for-goods").removeClass('hide');
                }
                copyOAEmptyForm('oa_particular', parseInt($('#id_oa_particular-TOTAL_FORMS').val()) - 1, 'oa_particular-oa_id', 'oa_id');
                var index = parseInt(parseInt($('#id_oa_particular-TOTAL_FORMS').val()) - 1) ;
                s_no_init();
            }
            $('#id_oa_particular-__prefix__-quantity').val(0.00)
            $('#id_oa_particular-__prefix__-price').val(0.00)
            $("#id_oa_particular-__prefix__-unit_id").val('') ;
            CalculateGrandTotal();
            $('#id_oa_particular-__prefix__-item_name').val("");
            $('#id_oa_particular-__prefix__-item_code').val("");
            $('#id_oa_particular-__prefix__-item_id').val("");
            $("#material_id_hidden").val("")
            $('#id_oa_particular-__prefix__-item_name').focus();
            $("#description_display").text('');
            $("#id_oa_particular-__prefix__-alternate_units").closest('.alternate_unit_select_box').addClass('hide')
            $("#id_oa_particular-__prefix__-unit_id").removeClass('hide')
            $('#chkfaulty').attr('checked', false);
                setTimeout(function(){
                        $(".error-border").removeClass('error-border');
                },100);
                $(".custom-error-message").remove();
            CalculateSubTotal();
            $("#id_oa_particular-__prefix__-item_name").removeAttr("readonly");
            $(".material-removal-icon").addClass("hide");
        }
        else {
            if ($('#material_id_hidden').val() == "" && $('#id_oa_particular-__prefix__-item_name').val() != ""){
                var materialName = $("#id_oa_particular-__prefix__-item_name").val();
                var materialUnit = $('#id_oa_particular-__prefix__-all_units').val();
                $('#id_oa_particular-__prefix__-alternate_units').html("");
                var materialHsn = $('#id_oa_particular-__prefix__-hsn_code').val();
                var materialPrice = $("#id_oa_particular-__prefix__-price").val();
                addNewMaterial(materialName, materialUnit, materialHsn, materialPrice);
            }
        }
    }
}

function copyOAEmptyForm(form_prefix, form_idx, fk_field_name, new_form_fk_field_name) {

    var new_form_item_code = document.getElementById('id_' + form_prefix + '-' + form_idx + '-item_code');
    var new_form_item_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-item_id');
    var new_form_item_name = document.getElementById('id_' + form_prefix + '-' + form_idx + '-item_name');
    var new_form_qty = document.getElementById('id_' + form_prefix + '-' + form_idx + '-quantity');
    var new_form_units = document.getElementById('id_' + form_prefix + '-' + form_idx + '-unit_id');
    var new_form_price = document.getElementById('id_' + form_prefix + '-' + form_idx + '-price');
    var new_form_hsn_code = document.getElementById('id_' + form_prefix + '-' + form_idx + '-hsn_code');
    var new_form_discount = document.getElementById('id_' + form_prefix + '-' + form_idx + '-discount');
    var new_form_amount = document.getElementById('id_' + form_prefix + '-' + form_idx + '-amount');
    var new_form_remarks = document.getElementById('id_' + form_prefix + '-' + form_idx + '-remarks');
    var new_form_enterprise_id= document.getElementById('id_'+ form_prefix +'-'+form_idx+'-enterprise_id');
    var new_form_is_faulty = document.getElementById('id_' + form_prefix + '-' + form_idx + '-is_faulty');
    var new_form_is_service = document.getElementById('id_' + form_prefix + '-' + form_idx + '-is_service');
    var new_form_alternate_unit_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-alternate_unit_id');
    var new_form_scale_factor = document.getElementById('id_' + form_prefix + '-' + form_idx + '-scale_factor');

    new_form_item_code.value = document.getElementById('id_' + form_prefix + '-__prefix__-item_code').value;
    new_form_item_id.value = document.getElementById('id_' + form_prefix + '-__prefix__-item_id').value;
    new_form_qty.value = document.getElementById('id_' + form_prefix + '-__prefix__-quantity').value;
    new_form_units.value = document.getElementById('id_'+form_prefix +'-__prefix__-unit_id').value;
    new_form_price.value = document.getElementById('id_' + form_prefix + '-__prefix__-price').value;
    new_form_hsn_code.value = document.getElementById('id_' + form_prefix + '-__prefix__-hsn_code').value;
    new_form_discount.value = document.getElementById('id_' + form_prefix + '-__prefix__-discount').value;
    new_form_amount.value = document.getElementById('id_'+form_prefix + '-__prefix__-amount').value;
    new_form_remarks.value = document.getElementById('id_' + form_prefix + '-__prefix__-remarks').value;
    new_form_enterprise_id.value = document.getElementById('id_'+form_prefix +'-__prefix__-enterprise_id').value;
    new_form_is_service.value = document.getElementById('id_'+form_prefix +'-__prefix__-is_service').value;
    if (document.getElementById('id_' + form_prefix + '-__prefix__-alternate_units').value != 0 ) {
        new_form_units.value = $('#id_oa_particular-__prefix__-alternate_units option:selected').text();
        new_form_alternate_unit_id.value = $('#id_oa_particular-__prefix__-alternate_units option:selected').val();
        new_form_scale_factor.value = $('#id_oa_particular-__prefix__-alternate_units option:selected').attr("data-val");
    } else{
        new_form_units.value = document.getElementById('id_'+form_prefix +'-__prefix__-unit_id').value;
    }

    var item_name = document.getElementById('id_oa_particular-__prefix__-item_name').value
    if ($('#id_oa_particular-__prefix__-make_id').val() != 1){
        var new_form_make_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-make_id');
        new_form_make_id.value = $('#id_oa_particular-__prefix__-make_id').val();
    }else{
        var new_form_make_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-make_id');
        new_form_make_id.value = 1;
    }


    if($("#chkfaulty").is(':checked')){
       new_form_is_faulty.value = 1;
       item_name = item_name + "[Faulty]"
    }else{ new_form_is_faulty.value = 0; }

    new_form_item_name.value = item_name;
    if(new_form_is_service.value == 1) {
        $('<span class="service-item-flag"></span>').insertAfter( new_form_item_name );
        $(new_form_item_name).addClass("item_text_box");
        $("#oa_particulars_table .service-item-flag").addClass("floated-right-flag");
    }
    document.getElementById('id_oa_particular-__prefix__-item_name').value = '';
    document.getElementById('id_oa_particular-__prefix__-item_code').value = '';
    document.getElementById('id_oa_particular-__prefix__-quantity').value = '';
    document.getElementById('id_oa_particular-__prefix__-price').value = '';
    document.getElementById('id_oa_particular-__prefix__-hsn_code').value = '';
    document.getElementById('id_oa_particular-__prefix__-discount').value = '';
    document.getElementById('id_oa_particular-__prefix__-amount').value = '';
    document.getElementById('id_oa_particular-__prefix__-remarks').value = '';
    document.getElementById('id_oa_particular-__prefix__-unit_id').value ='';
} /* Copy Form End Here */

function showDocumentListener(){
    var oa_saved_attachment = $("#oa_attachment_flag").val();
    if(oa_saved_attachment != "true"){
        $(".document_attachment").each(function() {
        var self = $(this);
        filename = self.attr('data-url');
        if (filename != null) {
            var extension = filename.replace(/^.*\./, '').toLowerCase();
            self.attr('data-extension', extension).addClass(extension);
        }
    });
    }
}

function short_close(){

    var initialFormCount = parseInt(document.getElementById('id_oa_particular-INITIAL_FORMS').value);
    for (i = 0; i < initialFormCount; i++) {
        var qty = document.getElementById('id_oa_particular-' + i + '-quantity');
        var inv_qty = document.getElementById("id_oa_particular-" + i + "-inv_quantity");
        qty.value = inv_qty.value
    }
    OnLoadTotalCalculation();
    isShortClosed = true;
}

function checkIndentMaterialQty(){
    check =true
    var message="\n"
    var se_id = $("#id_promote_se_data").val();
    if (se_id == ""){
        var initialFormCount = parseInt(document.getElementById('id_oa_particular-INITIAL_FORMS').value);
        for (i = 0; i < initialFormCount; i++) {
            var qty = document.getElementById('id_oa_particular-' + i + '-quantity');
            var inv_qty = document.getElementById("id_oa_particular-" + i + "-inv_quantity");
            var description = document.getElementById("id_oa_particular-" + i + "-item_name");
            if (parseFloat(qty.value) < parseFloat(inv_qty.value)){
                check = false;
                message += description.value + "\n"
            }
        }
    }
    if (check==false){
        swal('','Requested Quantity cannot be less than Invoice Quantity!'+ message ,'error');
        return false;
    }
    return true;
}

/* This Function is used to Load OA Items List to the Drop Down Box*/
function loadMaterial(loadType = "") {
    var project = JSON.parse(localStorage.getItem('project'));
    var dataToSend = {
        'type': $("#id_oa-type option:selected").val(),
        'party_id': $("#id_oa-party_id option:selected").val(),
        'module': 'order_acknowledgement','particulars':'oa_particulars'
    }
    var local_material_list  = JSON.parse(localStorage.getItem('goods_service_master_data'));
    if(loadType == "onchange" && local_material_list) {
        var material_choices = local_material_list[0].material;
    }
    else {
        var material_choices = generateMaterialAsAutoComplete(dataToSend);
    }
    var frequently_material_choices = generateFrequentlyMaterialAsAutoComplete(dataToSend);
    $("#id_oa_particular-__prefix__-item_name").materialAutocomplete({
        source: frequently_material_choices.concat(material_choices),
        minLength:1,
        response: function( event, ui ) {
            ui.content.unshift({value:"add_new_item", label:"+ ADD NEW MATERIAL ", category: ""});
        },
        open: function(event, ui) {
            $("ul.ui-autocomplete.ui-menu a:contains('+ ADD NEW MATERIAL')").addClass("add_new_material_link");
            if(project && project.type == 'Secondary'){
            $("ul.ui-autocomplete.ui-menu a:contains('+ ADD NEW MATERIAL')").addClass("hide");
            }
        },
        select: function (event, ui) {
            event.preventDefault();
            if(ui.item.value != "add_new_item") {
                var itemName = ui.item.label.replace("<span class='service-item-flag'></span>", '');
                $("#description_display").text('');
                $("#id_oa_particular-__prefix__-item_name").val(itemName).attr("readonly", true);
                $("#id_oa_particular-__prefix__-item_code").val(ui.item.id);
                $("#id_oa_particular-__prefix__-item_id").val(ui.item.id);
                $("#id_oa_particular-__prefix__-make_id").val(ui.item.mid);
                $("#id_oa_particular-__prefix__-is_service").val(ui.item.is_service);
                $("#material_id_hidden").val(ui.item.id);
                loadPartyRate()
                $("#id_oa_particular-__prefix__-discount").val(ui.item.discount);
                $("#id_oa_particular-__prefix__-unit_id").val(ui.item.unit);
                $('#id_oa_particular-__prefix__-alternate_units').html("");
                if($('#is_multiple_units').val()=="True" ){
                    if (ui.item.alt_uom > 0){
                        loadAlternateUnits(ui.item.id, ui.item.unit, '#id_oa_particular-__prefix__-alternate_units', function(){
                            $("#id_oa_particular-__prefix__-unit_id").addClass('hide')
                            $("#id_oa_particular-__prefix__-alternate_units").closest(".alternate_unit_select_box").removeClass("hide");
                            $("#id_oa_particular-__prefix__-all_units").closest(".all_units_select_box").addClass("hide");
                        });
                    }
                    else {
                        $("#id_oa_particular-__prefix__-alternate_units").closest(".alternate_unit_select_box").addClass("hide");
                        $("#id_oa_particular-__prefix__-unit_id").removeClass('hide')
                    }
                }
                //$("#description_display").text(ui.item.description);
                $(".material-removal-icon").removeClass("hide");
            }
            else {
                openMaterialAddModal();
            }
        }
    }).on('focus', function() { $(this).keydown(); });
}


$(function () {
    $('#edit_catalogue_form').submit(function() {
        $.ajax({
            data: $(this).serialize(),
            type: $(this).attr('method'),
            url: $(this).attr('action'),
            success: function(response) {
                $('#loading').hide();
                $("#description_display").html('');
                $("#material_id_hidden").val(response['item_id']);
                $("#id_oa_particular-__prefix__-item_id").val(response['item_id']);
                $("#id_oa_particular-__prefix__-item_code").val(response['item_id']);
                $("#id_oa_particular-__prefix__-item_name").val(response['name']).attr("readonly","readonly");
                $("#id_oa_particular-__prefix__-item_name").closest("tr").find(".material-removal-icon").removeClass("hide");
                $("#id_oa_particular-__prefix__-make_id").val(response['make_id']);
                $("#id_oa_particular-__prefix__-unit_id").val(response['unit_name']);
                $("#id_oa_particular-__prefix__-hsn_code").val(response['hsn_code'])
                $("#id_oa_particular-__prefix__-is_service").val(response['is_service'])
                $("#id_oa_particular-__prefix__-price").val(response['price'].toFixed(5))
                if(!$('#add_material_modal').hasClass('in')) {
                    $("#add_new_material").trigger("click");
                }
                $('#add_material_modal').modal('hide')
                setTimeout(function(){
                    $("#id_oa_particular-__prefix__-hsn_code").focus();
                },250);
            }
        });
        return false;
    });


    $("select#id_oa-party_id,#id_oa-type").change(function(){
        if ($("#id_oa-party_id").val()!='add_new_party') {
            loadMaterial("onchange");
            message = "<b>" + $("#id_oa-se_no_display option:selected").text() + "</b> reference will be cleared, as Party is changed.<br><br> Do you want to clear the Items loaded from the Sales Estimate <b>" + $("#id_oa-se_no_display option:selected").text()+" </b> ?"
            if($(this).attr("id") == 'id_oa-type'){
                message = "<b>" + $("#id_oa-se_no_display option:selected").text() + "</b> reference will be cleared, as Type is changed.<br><br> Do you want to clear the Items loaded from the Sales Estimate <b>" + $("#id_oa-se_no_display option:selected").text()+" </b> ?"
            }
            if($("#id_oa-se_no_display").val() != 0){
                swal({
                    title: "",
                    text: message,
                    type: "warning",
                    confirmButtonText: "Yes, clear the items",
                    showCancelButton: true,
                    cancelButtonText: "No",
                    closeOnConfirm: true,
                    closeOnCancel: true,
                },
                function(isConfirm){
                    if (isConfirm) {
                        deleteOAParticulars(null);
                    }
                    $("#oa_particular-__prefix__").removeClass('hide');
                    $("#add_new_material").removeClass('hide');
                    $("#id_oa-se_no_display").val(0).trigger("chosen:updated");
                    $("#id_oa-se_id").val("")
                });
            }
            var party_id = $("#id_oa-party_id").val();
            OAPartyChangeEvent();
            ChangePartyCurrency(party_id,"id_oa-currency_id","id_oa-currency_conversion_rate");
            currencyChangeEvent("onchange");
        }
        else {
            OAPartyChangeEvent();
        }
    });
});

function loadPartyRate(){
    var item_id=$('#id_oa_particular-__prefix__-item_id').val();
    var party_id =$("#id_oa-party_id option:selected").val()
    var make_id = $('#id_oa_particular-__prefix__-make_id').val()
    var oa_type = $("#id_oa-type option:selected").val()
    $("#material_rate_hidden").val("0.00000");
    if ($('#id_oa_particular-__prefix__-item_code').val()!="")
        $.ajax({
          url: "/erp/sales/json/invoice/loadPartyRate/",
          type: "post",
          datatype: "json",
          data: {party_id:party_id,item_id:item_id,make_id:make_id,oa_type:oa_type},
          success: function(response)
          {
                $("#id_oa_particular-__prefix__-price").val(response.item_rate.price);
                $("#material_rate_hidden").val(response.item_rate.price);
                $("#id_oa_particular-__prefix__-hsn_code").val(response.hsn_code)
          }
        });
}

function prepareReportModal() {
    if ($("#oaReport").text() == "") {
        $("body").append(`<div id="oaReport" class="modal fade" role="dialog">
            <div class="modal-dialog modal-lg"><div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Materials Received</h4>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="id_job_oa" name="job_oa_id" value="">
                    <table class="table table-bordered custom-table table-striped" id="grn_material_list">
                    <thead><tr>
                    <th width="5%"> S.No. </th>
                    <th width="12%"> GRN No</th>
                    <th width="40%"> Item Details</th>
                    <th width="20%"> Quantity</th>
                    <th width="9%"> Unit rate</th>
                    <th width="5%"> Discount</th>
                    </tr></thead>
                    <tbody></tbody>
                    </table>
                </div>
                <div class="modal-footer">
                    <span class="pull-left nonStock-text">  <span class=" nonStock-text non_stock-flag"></span> - NON-STOCKABLE</span>
                    <span class="pull-left nonStock-text">  <span class=" nonStock-text service-item-flag"></span> - SERVICE</span>
                    <a class="btn btn-cancel" data-dismiss="modal">Close</a>
                </div>
            </div></div></div>`);
    }
}

function showReceivedItems() {
    prepareReportModal();
    $.ajax({
        url: "/erp/sales/json/oa/job_oa_received/",
        type: "post",
        datatype: "json",
        data: {oa_id: $("#id_oa-id").val()},
        success: function(response){
            $("#grn_material_list").find("tbody").html('');
            $.each(response, function (i, item) {
                var itemTypeFlag ="";
                var item_name = item.item_name
                if(item.is_service == 1){
                    itemTypeFlag += `<span class="service-item-flag"></span>`;
                }
                if (item.is_stock !=1 && item.is_service != 1){
                     itemTypeFlag += `<span class='non_stock-flag'></span>`;
                }
                var pdf_generation_form = `<a role="button" onclick="openReceipt(${item.receipt_no});">${item.code}</a>`;
                row="<tr align='center' >"+
                    "<td width='5%'>"+(i+1)+"</td>"+
                    "<td width='12%'>"+ pdf_generation_form +"</td>"+
                    "<td width='15%' class='text-left'>"+ item_name +" "+itemTypeFlag+"</td>"+
                    "<td width='20%' class='text-right'>" +item.accepted_qty+"</td>"+
                    "<td width='9%' class='text-right'>"+item.rate+"</td>"+
                    "<td width='9%' class='text-right'>"+item.discount+"</td>"+
                    "</tr>";
                $('#grn_material_list').append(row);
            });
            if($("#grn_material_list tbody").find("tr").length <= 0) {
                var row = "<tr><td colspan='8' class='text-center' style='font-weight: bold;'>No Records Found!</td></tr>";
                $('#grn_material_list').append(row);
            }
        }
    });
    $("#oaReport").modal('show');
}

var showJobOaUsageReport = function () {
    showUsageReport(url="/erp/sales/json/oa/job_oa_usage/", params={oa_id: $("#id_oa-id").val()});
}

function loadAlternateUnitPrice(){
    if($("#id_oa_particular-__prefix__-alternate_units option:selected").attr('data-val') !=0 && $("#material_rate_hidden").val() != 0 && $("#material_rate_hidden").val() != ""){
        $("#id_oa_particular-__prefix__-price").val($("#id_oa_particular-__prefix__-alternate_units option:selected").attr('data-val') * $("#material_rate_hidden").val());
    }else{
        $("#id_oa_particular-__prefix__-price").val("0.00000")
    }
}

function deleteOATax(catTaxFormId) {
	var deleteFlag = document.getElementById('id_' + catTaxFormId + '-DELETE');
	var deleteRow = document.getElementById(catTaxFormId);
	deleteFlag.checked = true;
	deleteRow.style.display = 'none';
	isFormChanged = true;
}

function addOATaxClickEvent() {
        $("#add_oa_tax").click(function(){
                $(".error-border").removeClass('error-border');
                $(".custom-error-message").remove();
                var ControlCollections = [
                        {
                                controltype: 'dropdown',
                                controlid: 'oa_tax_list',
                                isrequired: true,
                                errormsg: 'Tax type is required.'
                        }
                ];
                var result = JSCustomValidator.JSvalidate(ControlCollections);
                if(result) {
                        AddOATaxClick();
                }
                return result;
        });
}

function AddOATaxClick() {
        var selected_item =$("#oa_tax_list option:selected").val();
        enterprise_id = $("#id_oa-enterprise_id").val();
        if($("#oa_tax_table").find("tr[name*='"+selected_item+"']").length){
                swal("Tax Already Added...");
        }
        else{
                /* Fetching Assessment Value */
                generateFormsetFormRowAtIndex('oa_tax', '#description_display');
                copyTaxEmptyForm('oa_tax',parseInt($('#id_oa_tax-TOTAL_FORMS').val()) - 1, enterprise_id, selected_item, $("#id_edit_data").val());
                oaTaxListAdding();
                isFormChanged = true;
        }
        CalculateGrandTotal();
}

function oaTaxListAdding(){
	$.ajax({
        url: "/erp/sales/json/invoice/getTax/",
        type: "post",
        datatype:"json",
        data: {tax_code: $("#oa_tax_list option:selected").val() },
        success: function(response){
			$.each(response, function(i, tax_detail_as_row){
				$('#oa_tax_table').append(tax_detail_as_row);
            });
        // Calculate the Total Price after applying new Tax profile
            CalculateGrandTotal();
        },
        error: function (xhr, errmsg, err) {
           console.log(xhr.status + ": " + xhr.responseText);
        }
    });
	$("#oa_tax_list option:selected").hide(); // Remove the added Tax from the Tax-list to avoid multiple addition
	$("#oa_tax_list").val('None');// Reset the Tax Select input non_excise_oa_tax_table
	$('.chosen-select').trigger('chosen:updated');
}

function generateTaxList(oa_id){
	se_id = $("#id_promote_se_data").val()
	if (oa_id != ""){
        $.ajax({
            url: "/erp/sales/json/invoice/editTax/",
            type: "post",
            datatype: "json",
            data: { oa_id: oa_id},
            success: function (response) {
                $.each(response, function (i, oa_tax_detail_as_row) {
                    $('#oa_tax_table').append(oa_tax_detail_as_row).addClass('tbl');
                    CalculateGrandTotal();
                });
				$("#oa_tax_table").find('.hnd_text_id').each(function(){
					var selectedTax = $(this).val();
					$("#oa_tax_list option[value='"+selectedTax+"']").hide();
				});
				$('.chosen-select').trigger('chosen:updated');
			}
		});
	}
	if (se_id != ""){
        $.ajax({
            url: "/erp/sales/json/invoice/editTax/",
            type: "post",
            datatype: "json",
            data: { se_id: se_id},
            success: function (response) {
                $.each(response, function (i, oa_tax_detail_as_row) {
                    $('#oa_tax_table').append(oa_tax_detail_as_row).addClass('tbl');
                    CalculateGrandTotal();
                });
				$("#oa_tax_table").find('.hnd_text_id').each(function(){
					var selectedTax = $(this).val();
					$("#oa_tax_list option[value='"+selectedTax+"']").hide();
				});
				$('.chosen-select').trigger('chosen:updated');
			}
		});
	}
}

function generateSETaxList(se_id){
	if (se_id != ""){
        $.ajax({
            url: "/erp/sales/json/invoice/editTax/",
            type: "post",
            datatype: "json",
            data: { se_id: se_id},
            success: function (response) {
                $.each(response, function (i, oa_tax_detail_as_row) {
                    $('#oa_tax_table').append(oa_tax_detail_as_row).addClass('tbl');
                    CalculateGrandTotal();
                });
				$("#oa_tax_table").find('.hnd_text_id').each(function(){
					var selectedTax = $(this).val();
					$("#oa_tax_table").find("tr[name*='"+selectedTax+"']").remove();
					$("#oa_tax_list").val(selectedTax);
					$('.chosen-select').trigger('chosen:updated');
					$("#add_oa_tax").click();
				});
			}
		});
	}
}

function removeInvoiceTax(tax_code){
	var data_count = document.getElementById('oa_tax_table').rows.length - 1;
	var row_id=0;
	var param_tax;
	for(i=0;i<=data_count;i++){
		if($("#id_oa_tax-"+i+"-tax_code").val()== tax_code)
			row_id = i;
    }
	tax_rows = document.getElementsByName('tr_' + tax_code);
	for(i=tax_rows.length - 1; i>=0; i--){
		// Removed in reverse order as the removal renumbers the RowIndices immediately
		document.getElementById("oa_tax_table").deleteRow(tax_rows[i].rowIndex);
	}
	$("#oa_tax_list option[value='"+ tax_code +"']").show();
	$('.chosen-select').trigger('chosen:updated');
	param_tax ="oa_tax-"+row_id;

	deleteOATax(param_tax);
	CalculateGrandTotal();
	isFormChanged = true;
    // Re-show the tax in the Tax list
}

/*Tax Copy Processing Start here...*/
function copyTaxEmptyForm(form_prefix, form_idx, enterprise_id, tax_code, oa_id) {
	var new_form_tax_code = document.getElementById('id_' + form_prefix + '-' + form_idx + '-tax_code');
	var new_form_enterprise_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-enterprise_id');
	var new_form_oa_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-oa_id');

    if (tax_code != ""){
	    new_form_tax_code.value = tax_code;
	}
	else {
	    new_form_tax_code.value = document.getElementById('id_' + form_prefix + '-__prefix__-tax_code').value;
	}
	if (enterprise_id != ""){
	    new_form_enterprise_id.value = enterprise_id;
	}
	else {
	    new_form_enterprise_id.value = document.getElementById('id_' + form_prefix + '-__prefix__-enterprise_id').value;
	}
	if (oa_id != ""){
	    new_form_oa_id.value = oa_id;
	}
	else {
	    new_form_oa_id.value = document.getElementById('id_' + form_prefix + '-__prefix__-oa_id').value;
	}

	document.getElementById('id_' + form_prefix + '-__prefix__-tax_code').value = '';
	document.getElementById('id_' + form_prefix + '-__prefix__-enterprise_id').value = '';
	document.getElementById('id_' + form_prefix + '-__prefix__-oa_id').value = '';
}/*Tax Copy Processing End here...*/

$(function() {
    try {
        $("select#oa_tax_list").change(function(){
               $("#id_oa_tax-__prefix__-tax_code").val($(this).find("option:selected").attr("value"));
                $("#id_oa_tax-__prefix__-oa_id").val($("#id_oa-id").val());
                $("#id_oa_tax-__prefix__-enterprise_id").val($("#id_oa-enterprise_id").val());

        });

    } catch(e) {
        console.log(e);
    }
});

$(window).load(function(){
    if($("#id_oa-id").val() == "" && $("#id_promote_se_data").val()== "") {
        $("#id_oa-party_id").trigger("change");
    }
});