import base64
import pdfkit
from django.template.loader import get_template
from datetime import datetime
from erp.properties import OUTSTANDING_DOC_CONTENT_PATH, OUTSTANDING_DOC_PATH, OUTSTANDING_DOC_FOOTER_PATH
from util.document_compiler import PDFGenerator
from util.helper import getAbsolutePath, writeFile
from django.template import Context
from util.document_properties import getStyleSheet
from erp.accounts import logger

__author__ = 'Pavithra'

styles = getStyleSheet()
TARGET_PATH = '/site_media/tmp/accounts.pdf'


class AccountsPDFGeneration(PDFGenerator):
	"""
	Generates a PDF replica of the Accounts that will be available printed on a pre-printed sheet.
	"""
	def __init__(self, target_file_path=TARGET_PATH):
		super(AccountsPDFGeneration, self).__init__(target_file_path)

	def generatePDF(
			self, enterprise=None, data=None, party_details=None, ledger_name=None, ledger_id=None, advance_outstanding=None,
			enterprise_image=None, first_name=None, last_name=None, show_advance_outsourcing=None, section_title=None,
			from_date=None, to_date=None, outsourcing_value=None, ageing_table=None):
		"""

		:param enterprise:
		:param data:
		:param party_details:
		:param ledger_name:
		:param ledger_id:
		:param advance_outstanding:
		:param enterprise_image:
		:param first_name:
		:param last_name:
		:param show_advance_outsourcing:
		:param section_title:
		:param from_date:
		:param to_date:
		:param outsourcing_value:
		:param ageing_table:
		:return:
		"""

		try:
			if enterprise_image and enterprise_image.logo and enterprise_image.logo != "":
				logo = "data:image/%s;base64,%s" % (enterprise_image.ext, base64.encodestring(enterprise_image.logo))
			else:
				logo = ""
			if section_title == "Payment Advice":
				content_text = "payment"
			else:
				content_text = "receipt"
			context = Context({
					'enterprise': enterprise, 'enterprise_logo': logo, 'party_details': party_details,
					'current_date': datetime.now().strftime("%b %d, %Y"), 'advance_outstanding': advance_outstanding,
					'last_name': last_name, 'first_name': first_name, 'data': data, "ledger_name": ledger_name,
					'show_advance_outsourcing': show_advance_outsourcing, 'section_title': section_title,
					'from_date': from_date, 'to_date': to_date, 'content_text': content_text,
					'outsourcing_value': outsourcing_value, 'ageing_table': ageing_table})

			css = [getAbsolutePath('/site_media/css/bootstrap.css')]
			if party_details and party_details.party_name:
				title = str(party_details.party_name) + "-" + str(section_title) + "-" + str(from_date) + "-" + str(to_date)
			else:
				title = str(ledger_name) + "-" + str(section_title) + "-" + str(from_date) + "-" + str(to_date)
			logger.info("Writing persisted Doc in temp path...")

			outstanding_data = get_template(getAbsolutePath('/templates/accounts/billable_print_content.html')).render(context)
			outstanding_file_name = OUTSTANDING_DOC_CONTENT_PATH % (str(ledger_id) + str(enterprise.id))
			writeFile(outstanding_data, outstanding_file_name)
			template_src = getAbsolutePath(OUTSTANDING_DOC_CONTENT_PATH % (str(ledger_id) + str(enterprise.id)))
			out_src = getAbsolutePath(OUTSTANDING_DOC_PATH % (str(title)))

			footer_data = get_template(getAbsolutePath('/templates/accounts/billable_print_footer.html')).render(context)
			footer_file_name = OUTSTANDING_DOC_FOOTER_PATH % (str(ledger_id) + str(enterprise.id))
			writeFile(footer_data, footer_file_name)

			options = {
				'page-size': 'A4', 'orientation': 'Portrait', 'title': title, 'margin-top': '10mm',
				'margin-right': '10mm',	'margin-bottom': '12mm', 'margin-left': '10mm',
				'footer-html': getAbsolutePath(footer_file_name)}

			pdfkit.from_file(input=template_src, output_path=out_src, options=options, css=css)
			return OUTSTANDING_DOC_PATH % (str(title))
		except Exception as e:
			logger.exception('Something went wrong while creating PDF self.canvas - %s' % e)


