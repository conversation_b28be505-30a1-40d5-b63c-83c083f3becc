import json
from datetime import datetime
from decimal import Decimal

import simplejson
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.template.response import TemplateResponse
from sqlalchemy import and_

from erp import properties, IS_FAULTY_TRUE, helper
from erp.auth import SESSION_KEY, ENTERPRISE_IN_SESSION_KEY, ENTERPRISE_ID_SESSION_KEY, PRIMARY_ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.forms import OAForm, OASearchForm
from erp.formsets import OAParticularsFormset, OATaxFormset
from erp.helper import populatePartyChoices, populateMaterialChoices, getUser, populateTaxChoices, getStateList
from erp.masters.backend import MasterService
from erp.models import OA, Currency, ReceiptMaterial, Receipt, Make, Material, MaterialMakeMap, SalesEstimate
from erp.properties import TEMPLATE_TITLE_KEY
from erp.sales import logger
from erp.sales.backend import InvoiceService
from erp.sales.oa_backend import OrderAcknowledgementService, OA_FORM_PREFIX, OA_PARTICULARS_PREFIX, \
	OrderAcknowledgementVO, OA_TAX_PREFIX, InternalOrderAcknowledgementService
from erp.sales.views import DatetimeEncoder, calcMultiBOMMat, calcJDCBOMMat
from erp.stores.backend import StoresDAO
from erp.tags import generateTagFormset
from settings import SQLASession, GCS_PUBLIC_URL
from util.api_util import response_code, JsonUtil
from util.helper import copyDictToDict

"""
"""
__author__ = 'saravanan'

FIELD_SEPARATOR_O = "%26"  # &
COMMA_SEPARATOR = "%2C"  # ","

OA_KEY = 'oa'
OA_PK_FIELD_KEY = 'oa_no'
OA_PARTICULAR_FORMSET_KEY = 'oa_particular_formset'
OA_NON_STOCK_PARTICULAR_FORMSET_KEY = 'oa_non_stock_particular_formset'
OA_MATERIAL_POPULATE = 'material_list'
EDIT_OA_ID = 'oa_edit_id'
PARTY_KEY = 'party'
OA_TAG = 'tag'
OA_TAGS_FORMSET = 'tags_formset'
OA_LIST = 'oa_list'
OA_TAX_LIST = 'tax_list'
OA_TAX_LOAD = 'load_tax'
OA_SEARCH = 'search'
MATERIAL_FORM_KEY = 'material_form'
BILL_OF_MATERIALS_FORMSET_KEY = 'bill_of_materials_formset'
SPECIFICATION_FORMSET_KEY = 'specifications_formset'
MATERIAL_MAKE_FORMSET_KEY = 'material_makes_formset'
PRICE_OF_SUPP_MATERIALS_FORMSET_KEY = 'materials_party_price_formset'
ALTERNATE_UNIT_FORMSET_KEY = 'alternate_unit_formset'
PROMOTE_SE_ID = 'promote_oa_se_id'
PROMOTE_SE_CODE = 'promote_oa_se_code'


def manageOAPage(request):
	request_handler = RequestHandler(request)
	master_service = MasterService()
	se_no = request_handler.getPostData("se_no")
	se_code = None
	enterprise = request_handler.getSessionAttribute(ENTERPRISE_IN_SESSION_KEY)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	gst_category = master_service.getGSTCategory()
	countries = master_service.getCountries()
	gst_category_list = []
	gst_country_list = []
	for category in gst_category:
		gst_category_list.append({"category_id": category.id, "category_name": category.name, "category_desc": category.description})

	for country in countries:
		gst_country_list.append({"country_code": country.code, "country_name": country.name})
	oa_obj = OA(
		prepared_by=request_handler.getSessionAttribute(SESSION_KEY), enterprise_id=enterprise_id,
		currency_id=enterprise.home_currency_id, prepared_on=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
	oa_service = OrderAcknowledgementService()
	if se_no:
		oa_obj, se_code = oa_service.generateOAFromSE(
			oa_obj=oa_obj, se_no=se_no, enterprise_id=enterprise_id)
	oa_vo = oa_service.constructOrderAcknowledgementVo(oa=oa_obj)
	message = request_handler.setSessionAttribute("last_created_order", "")
	currency = SQLASession().query(Currency).order_by(Currency.code).all()
	material_vo = master_service.constructMaterialVO(enterprise_id=enterprise_id)
	return TemplateResponse(request=request, template='sales/oa.html', context={
		PROMOTE_SE_ID: se_no if se_no else "",
		PROMOTE_SE_CODE: se_code if se_code else "",
		OA_KEY: oa_vo.oa_form, 'currency': currency,
		OA_PARTICULAR_FORMSET_KEY: oa_vo.oa_particulars_formset,
		OA_MATERIAL_POPULATE: populateMaterialChoices(enterprise_id),
		PARTY_KEY: populatePartyChoices(enterprise_id), OA_TAX_LIST: oa_vo.oa_tax_formset,
		OA_TAX_LOAD: populateTaxChoices(enterprise_id),
		OA_TAGS_FORMSET: oa_vo.oa_tag_formset, TEMPLATE_TITLE_KEY: "Order Acknowledgement",
		MATERIAL_FORM_KEY: material_vo.material_form,
		ALTERNATE_UNIT_FORMSET_KEY: material_vo.alternate_unit_formset,
		BILL_OF_MATERIALS_FORMSET_KEY: material_vo.material_formset,
		SPECIFICATION_FORMSET_KEY: material_vo.specification_formset,
		PRICE_OF_SUPP_MATERIALS_FORMSET_KEY: material_vo.supplier_price_material_formset,
		MATERIAL_MAKE_FORMSET_KEY: material_vo.material_make_formset,
		"last_created_order": message if message else "", 'gst_category_list': gst_category_list,
		'gst_country_list': gst_country_list, 'state_list': getStateList()})


def manageOAViewPage(request):
	return TemplateResponse(request=request, template='sales/oaview.html', context={})


def saveOA(request):
	logger.info("Save OA Triggered...")
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		primary_enterprise_id = request_handler.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
		logger.info("The Enterprise ID: %s" % enterprise_id)
		oa_vo = OrderAcknowledgementVO(oa_form=OAForm(
			data=request_handler.getPostData(), prefix=OA_FORM_PREFIX, enterprise_id=enterprise_id),
			oa_particulars_formset=OAParticularsFormset(request_handler.getPostData(), prefix=OA_PARTICULARS_PREFIX),
			oa_tax_formset=OATaxFormset(request_handler.getPostData(), prefix=OA_TAX_PREFIX),
			oa_tag_formset=generateTagFormset(request_handler.getPostData()))
		logger.info("The OA Construction Completed...")
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		oa_attachment_json = request_handler.getPostData('oa_attachment_upload_json') if request_handler.getPostData('oa_attachment_upload_json') != "" else None
		oa_service = OrderAcknowledgementService()
		saved_oa_vo, oa_id = oa_service.saveOA(oa_vo=oa_vo, user_id=user_id, oa_attachment_json=oa_attachment_json)
		logger.info("The SaveOA Event Triggered...")
		material_list = populateMaterialChoices(enterprise_id)
		custom_message = "%s has been saved successfully" %("OA" if enterprise_id == primary_enterprise_id else "IWO")
		messages.success(request, "OA Saved Successfully....")
		request_handler.setSessionAttribute("last_created_order", custom_message)
		if oa_vo.oa_form.cleaned_data['id'] is None:
			InvoiceService().notifyPendingOACount(enterprise_id=enterprise_id, sender_id=user_id)
		oa_entity = oa_service.oa_dao.getOA(oa_id=oa_id)
		if oa_entity.status == 1:
			internal_oa = InternalOrderAcknowledgementService()
			internal_oa.createInternalOA(enterprise_id=enterprise_id, user_id=user_id, parent_oa_id=oa_id, primary_enterprise_id=primary_enterprise_id)
	except Exception as e:
		logger.exception("Saving OA Failed...\n%s" % e.message)
		raise
	if not saved_oa_vo.is_valid():
		return TemplateResponse(template=properties.MANAGE_OA_TEMPLATE, request=request, context={
			OA_KEY: saved_oa_vo.oa_form,
			OA_PARTICULAR_FORMSET_KEY: saved_oa_vo.oa_particulars_formset,
			OA_MATERIAL_POPULATE: material_list,
			OA_TAX_LIST: oa_vo.oa_tax_formset,
			OA_TAX_LOAD: populateTaxChoices(enterprise_id),
			OA_TAGS_FORMSET: saved_oa_vo.oa_tag_formset, "last_created_order": ""})
	return HttpResponseRedirect(properties.MANAGE_OA_VIEW_URL)


def loadOA(request):
	# Load OA List
	try:
		logger.info("The Load OA Triggered...")
		request_handler = RequestHandler(request)
		from_date, to_date = JsonUtil.getDateRange(
			rh=request_handler, since_session_key="oa.since", till_session_key="oa.till")
		party_id = request_handler.getPostData('party_name')
		status = request_handler.getPostData('status')
		sales_status = request_handler.getPostData('sales_status')
		party_id = party_id if party_id else -1
		status = status if status else ""
		sales_status = 1 if sales_status else 0

		# Get the Enterprise id
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		primary_enterprise_id = request_handler.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
		isprimary_project = enterprise_id == primary_enterprise_id
		message = request_handler.getSessionAttribute("last_created_order")
		request_handler.setSessionAttribute("last_created_order", "")

		if request_handler.getData("view") == 'pending' and request_handler.getData("type") == 'oa':
			oa_initial_date = SQLASession().query(OA.prepared_on).filter(
				OA.enterprise_id == enterprise_id).order_by('prepared_on').first()
			from_date = oa_initial_date[0].strftime('%Y-%m-%d')
			status = '0'
		return TemplateResponse(template=properties.MANAGE_OA_VIEW_TEMPLATE, request=request, context={
			OA_LIST: searchOA(
				enterprise_id=enterprise_id, from_date=from_date, to_date=to_date,
				party_id=party_id, status=status),
			OA_TAX_LOAD: populateTaxChoices(enterprise_id),
			PARTY_KEY: populatePartyChoices(enterprise_id),
			OA_SEARCH: OASearchForm(enterprise_id=enterprise_id, initial={
				'to_date': to_date, 'from_date': from_date, 'party_name': party_id, 'status': status,
				'sales_status': sales_status}), 'exclude_value': sales_status,
			TEMPLATE_TITLE_KEY: "Order Acknowledgement" if isprimary_project else "Internal Work Order",
			"last_created_order": message if message else "",
			"isprimary_project": isprimary_project})
	except Exception as e:
		logger.exception("Loading OA Interrupted...\n%s" % e.message)
		raise


def searchOA(enterprise_id=None, from_date=None, to_date=None, party_id=-1, status=None):
	logger.info("Enterprise Id : %s , From Date %s and Party id: %s Status: %s" % (
		enterprise_id, from_date, party_id, status))
	condition = """order_acknowledgement.type !='MRS' and order_acknowledgement.prepared_on between '%s' and '%s' and
		order_acknowledgement.enterprise_id = '%s' and order_acknowledgement.status %s %s""" % (
		from_date, to_date, enterprise_id, " = '%s'" % status if status != '' else " >= -1",
		"" if int(party_id) == -1 else " and order_acknowledgement.party_id='%s'" % party_id)
	query = SQLASession().query(OA).filter(condition).order_by('prepared_on')
	result = query.all()
	return result


def is_json(myjson):
	"""

	:param myjson:
	:return:
	"""
	try:
		json_object = json.loads(myjson)
	except ValueError as e:
		return False
	return True


def editOA(request):
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		oa_no_to_be_edited = request_handler.getPostData(OA_PK_FIELD_KEY)
		oa_to_edit = getOA(oa_no_to_be_edited, enterprise_id)
		status = oa_to_edit.status
		oa_type = [(item[0], item[1], item[0][0]) for item in OA.TYPE_CHOICES["oa"]]
		if oa_to_edit:
			logger.debug("Invoice To Edit : %s" % oa_to_edit.getCode())

		oa_vo = OrderAcknowledgementService().constructOrderAcknowledgementVo(oa_to_edit)
		material_list = populateMaterialChoices(enterprise_id)
		master_service = MasterService()
		material_vo = master_service.constructMaterialVO(enterprise_id=enterprise_id)

		attachment_data = {}
		if oa_to_edit.document != None and is_json(oa_to_edit.document):
			json_data = json.loads(oa_to_edit.document)
			gcs_url = "{url}/{enterprise_id}/{key}".format(url=GCS_PUBLIC_URL, enterprise_id=enterprise_id, key=json_data['uid'])
			attachment_data = {'name': json_data['name'], 'ext': json_data['ext'], 'file': gcs_url}
		selected_project_code = oa_to_edit.project_code if oa_to_edit.project_code else None

		return TemplateResponse(template=properties.ADD_EDIT_OA_TEMPLATE, request=request, context={
			'se_no': oa_to_edit.se.getInternalCode() if oa_to_edit.se else "",
			OA_KEY: oa_vo.oa_form,
			'oa_attachment': attachment_data if bool(attachment_data) else "",
			OA_PARTICULAR_FORMSET_KEY: oa_vo.oa_particulars_formset,
			OA_MATERIAL_POPULATE: material_list,
			EDIT_OA_ID: oa_no_to_be_edited,
			OA_TAX_LIST: oa_vo.oa_tax_formset,
			OA_TAX_LOAD: populateTaxChoices(enterprise_id),
			OA_TAGS_FORMSET: oa_vo.oa_tag_formset,
			'remarks_list': json.dumps(oa_vo.remarks_list),
			'oa_type': oa_type,
			MATERIAL_FORM_KEY: material_vo.material_form,
			ALTERNATE_UNIT_FORMSET_KEY: material_vo.alternate_unit_formset,
			BILL_OF_MATERIALS_FORMSET_KEY: material_vo.material_formset,
			SPECIFICATION_FORMSET_KEY: material_vo.specification_formset,
			PRICE_OF_SUPP_MATERIALS_FORMSET_KEY: material_vo.supplier_price_material_formset,
			MATERIAL_MAKE_FORMSET_KEY: material_vo.material_make_formset,
			TEMPLATE_TITLE_KEY: oa_to_edit.getInternalCode(), 'status': status, 'selected_project_code' : selected_project_code})
	except Exception as e:
		logger.exception("Invoice Rendering for Edit Failed...\n%s" % e.message)
		raise


def getOA(oa_id=None, enterprise_id=None):
	return SQLASession().query(OA).filter(OA.id == oa_id, OA.enterprise_id == enterprise_id).first()


def approveOA(request):
	"""
	Assigns a permanent identification number for the OA. Sets the OA status as approved.
	:param request:
	:return: JSON dumps to the Ajax call that initiated this approval
	"""
	logger.info("OA Approval Triggered...")
	request_handler = RequestHandler(request)
	oa_id = request_handler.getPostData('oa_id')
	enterprise_id = request_handler.getPostData('enterprise_id')
	user_id = request_handler.getPostData('user_id')
	remarks = request_handler.getPostData('approved_remarks')
	project_code = request_handler.getPostData('project_code')
	primary_enterprise_id = request_handler.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
	if user_id is None:
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
	if enterprise_id is None:
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		oa_service = OrderAcknowledgementService()
		response = oa_service.approveOa(enterprise_id=enterprise_id, oa_id=oa_id, user_id=user_id, remarks=remarks,
										project_code=project_code, primary_enterprise_id=primary_enterprise_id)
		return HttpResponse(json.dumps(response), 'content-type=text/json')
	except Exception as e:
		logger.exception('Error in OA Approval: %s' % e)
		response = response_code.internalError()
		response['error'] = "Approval Failed..."
		return HttpResponse(json.dumps(response), 'content-type=text/json')


def rejectOA(request):
	"""
	Rejects

	:param request:
	:return:
	"""
	logger.debug('Rejecting a OA...')
	request_handler = RequestHandler(request)
	oa_id = request_handler.getPostData('oa_id')
	enterprise_id = request_handler.getPostData('enterprise_id')
	user_id = request_handler.getPostData('user_id')
	rejection_remarks = request_handler.getPostData('remarks')
	logger.debug("OA ID%s" % oa_id)
	if user_id is None:
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
	if enterprise_id is None:
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		if request_handler.isSessionActive() and request_handler.isPostRequest():
			response = getRejectStatus(
				enterprise_id=enterprise_id, user_id=user_id, oa_id=oa_id,
				rejection_remarks=rejection_remarks, flag=True)
			return HttpResponse(simplejson.dumps(response))
	else:
		status = getRejectStatus(
			enterprise_id=enterprise_id, user_id=user_id, oa_id=oa_id,
			rejection_remarks=rejection_remarks, flag=False)
		if status == -1:
			response = response_code.failure()
		else:
			response = response_code.success()
		return HttpResponse(json.dumps(response), 'content-type=text/json')


def getRejectStatus(enterprise_id=None, user_id=None, oa_id=None, rejection_remarks=None, flag=True):
	"""

	:return:
	"""
	db_session = SQLASession()
	try:
		db_session.begin(subtransactions=True)
		oa_to_reject = db_session.query(OA).filter(OA.id == oa_id).first()
		user = getUser(enterprise_id=enterprise_id, user_id=user_id)
		if oa_to_reject.status == OA.STATUS_DRAFT:
			logger.debug("Removing the OA %s permanently" % oa_id)
			oa_to_reject.status = OA.STATUS_REJECTED
			oa_to_reject.updateRemarks(remarks="Rejection Remarks: %s" % rejection_remarks, user=user)
			db_session.commit()
			InvoiceService().notifyOArejectCount(
				enterprise_id=enterprise_id, sender_id=user_id, oa=oa_to_reject, code=oa_to_reject.getInternalCode(),
				type=oa_to_reject.type)
			return 0
		oa_to_reject.status = OA.STATUS_CANCELLED
		oa_to_reject.updateRemarks(remarks="Cancelled Remarks: %s" % rejection_remarks, user=user)
		db_session.add(oa_to_reject)
		db_session.commit()
		InvoiceService().notifyOArejectCount(
			enterprise_id=enterprise_id, sender_id=user_id, oa=oa_to_reject, code=oa_to_reject.getInternalCode(),
			type=oa_to_reject.type)
		return oa_to_reject.getInternalCode()
	except Exception as e:
		db_session.rollback()
		logger.exception(e.message)
		if flag:
			return "Rejection Failed!!"
		else:
			return -1


def jobOAAgainstReceived(request):
	grn_materials_list = ()
	try:
		job_oa_id = RequestHandler(request).getPostData("oa_id")
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		grn_list_orm = SQLASession().query(
			ReceiptMaterial.receipt_no, Material.drawing_no, Receipt.sub_number, Material.name, ReceiptMaterial.is_faulty,
			ReceiptMaterial.accepted_qty, ReceiptMaterial.rate, ReceiptMaterial.discount, Receipt.financial_year,
			Receipt.receipt_code, Receipt.received_against, Make.label.label('make'), MaterialMakeMap.part_no,
			ReceiptMaterial.alternate_unit_id, ReceiptMaterial.item_id, Material.is_stocked, Material.is_service, Material.makes_json
		).outerjoin(ReceiptMaterial.receipt).outerjoin(
			Make, and_(Make.id == ReceiptMaterial.make_id, Make.enterprise_id == ReceiptMaterial.enterprise_id)
		).outerjoin(
			Material, and_(Material.material_id == ReceiptMaterial.item_id,
			Material.enterprise_id == ReceiptMaterial.enterprise_id)
		).outerjoin(
			MaterialMakeMap, and_(MaterialMakeMap.item_id == ReceiptMaterial.item_id,
			MaterialMakeMap.make_id == ReceiptMaterial.make_id,
			MaterialMakeMap.enterprise_id == ReceiptMaterial.enterprise_id)
		).filter(
			ReceiptMaterial.oa_id == job_oa_id, Receipt.status > -1).all()

		grn_materials_list = []
		for grn in grn_list_orm:
			grn_dict = grn._asdict()
			grn_dict['is_stock'] = grn.is_stocked
			item_name = grn.name
			if grn.drawing_no:
				item_name += " - " + grn.drawing_no
			make_name = helper.constructDifferentMakeName(grn.makes_json)
			if make_name:
				item_name += " [" + make_name + "]"
			if grn.is_faulty == IS_FAULTY_TRUE:
				item_name += " [Faulty]"
			grn_dict['item_name'] = item_name
			grn_dict['code'] = Receipt.generateReceiptCode(
				receipt_no=grn.receipt_no, receipt_code=grn.receipt_code, received_against=grn.received_against,
				financial_year=grn.financial_year, sub_number=grn.sub_number)
			if grn.alternate_unit_id and grn.alternate_unit_id != 0:
				scale_factor = helper.getScaleFactor(
					enterprise_id=enterprise_id, item_id=grn.item_id,
					alternate_unit_id=grn.alternate_unit_id)
				if scale_factor:
					grn_dict['accepted_qty'] = "{:0.3f}".format(Decimal(grn.accepted_qty) / Decimal(scale_factor)) if grn.accepted_qty else 0
					grn_dict['rate'] = "{:0.5f}".format(Decimal(grn.rate) * Decimal(scale_factor)) if grn.rate else 0

			grn_materials_list.append(grn_dict)
	except Exception as e:
		logger.exception("Fetching DCs are Failed %s" % e.message)
	return HttpResponse(content=json.dumps(grn_materials_list, cls=DatetimeEncoder), mimetype='application/json')


def jobOAUsage(request):
	"""

	:param request:
	:return:
	"""
	logger.info("The Edit PO Loaded...")
	rh = RequestHandler(request)
	oa_id = rh.getPostData('oa_id')
	response = []
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		logger.info("Fetching job order material for {enterprise: %s, po_id:%s}" % (enterprise_id, oa_id))
		store_dao = StoresDAO()
		invoice_dao = InvoiceService()
		oa_materials = invoice_dao.getOAMaterials(enterprise_id=enterprise_id, oa_id=oa_id)
		grn_materials = invoice_dao.getGRNMaterials(enterprise_id=enterprise_id, oa_id=oa_id)
		oa_details, multi_bom_mat = invoice_dao.getClubbedCatalogue(materials=oa_materials, enterprise_id=enterprise_id)
		if len(multi_bom_mat) > 0:
			oa_details, grn_materials = calcMultiBOMMat(multi_bom_mat, oa_details, grn_materials)
		oa_details, grn_materials = calcJDCBOMMat(oa_details, grn_materials)
		for material in grn_materials:
			material.update({'actual_qty': 0})
			# FIXME performance issue while accessing item.material in loop; Consider 250 materials in a PO;
			if material['drawing_no'] != "-NA-":
				catalogues = store_dao.getCatalogues(material['cat_code'], enterprise_id)
				catalogues_child = 1 if len(catalogues) > 0 else 0
				make_list = [(material['make_id'], material['make_name'])]
			else:
				catalogues_child = ""
				make_list = ""
			material['child'] = catalogues_child
			material['makes'] = make_list
			material['actual_qty'] = material['qty']
			material['supplied'] = material['qty'] if material['qty'] else 0
			material['qty'] = 0
			material['returned'] = 0
			oa_details.append(material)
		response = invoice_dao.getCalcActualQty(po_id=oa_id, po_material_lst=oa_details, job_type='OA')
	except Exception as e:
		logger.exception("Tax retrieval fails... %s" % e.message)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getPartySE(request):
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		party_id = rh.getPostData('party_id')
		oa_type = rh.getPostData('oa_type')
		se_id = rh.getPostData('se_id')
		if party_id == "":
			party_id = None
		if se_id in ["", '0']:
			se_id = None
		response = response_code.success()
		response["se_list"] = OrderAcknowledgementService().getSECodeByPartyAndType(
			enterprise_id=enterprise_id, party_id=party_id, se_id=se_id, oa_type=oa_type)
	except Exception as e:
		logger.exception('Failed getting dashboard data. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message

	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getSEDetails(request):
    try:
        request_handler = RequestHandler(request)
        enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
        se_id = request_handler.getPostData('se_id')

        se_details = {}
        se_details['se_tags'] = []
        se_details['se_taxes'] = []
        se_details['se_materials'] = []

        se = SQLASession().query(SalesEstimate).filter(
            SalesEstimate.id == se_id, SalesEstimate.enterprise_id == enterprise_id).first()
        se_details['se_header'] = {"payment_terms": se.payment_terms,
                 "special_instructions": se.special_instructions, "currency_id": se.currency_id, "conversion_rate": se.currency_conversion_rate}
        for tag in se.tags:
            se_details['se_tags'].append({"tag_id": tag.tag_id, "tag_name": tag.tag.tag})
        for tax in se.taxes:
            se_details['se_taxes'].append(tax.tax_code)
        for item in se.items:
			se_material = copyDictToDict(source=item.__dict__, exclude_keys=('_sa_instance_state'))
			material = item.item
			se_material['unit_id'] = material.unit_id
			se_material['unit'] = material.unit.unit_name
			se_material['drawing_no'] = material.drawing_no
			se_material['item_name'] = material.name
			se_material['mat_type'] = material.is_stocked
			se_material['make_name'] = material.makes_json if material.makes_json else ""
			se_material['is_service'] = material.is_service
			se_material['scale_factor'] = 1
			se_material['enterprise_id'] = enterprise_id
			if se_material['alternate_unit_id'] and int(se_material['alternate_unit_id']) != 0:
				scale_factor = helper.getScaleFactor(
					enterprise_id=enterprise_id, item_id=se_material['item_id'],
					alternate_unit_id=se_material['alternate_unit_id'])
				se_material['unit'] = helper.getUnitName(enterprise_id=enterprise_id,
													  unit_id=se_material['alternate_unit_id'])
				if scale_factor:
					se_material['quantity'] = round(Decimal(se_material['quantity']) / Decimal(scale_factor), 3)
					se_material['unit_rate'] = Decimal(se_material['unit_rate']) * Decimal(scale_factor)
					se_material['scale_factor'] = scale_factor if scale_factor else 1
			se_material['amount'] = round(
				Decimal(se_material['quantity']) * Decimal(se_material['unit_rate']) * (
						100 - Decimal(se_material['discount'])) / 100, 2)
			se_details['se_materials'].append(se_material)

        return HttpResponse(content=simplejson.dumps(se_details), mimetype='application/json')

    except Exception as e:
        logger.exception("Sales Estimate Rendering for Edit Failed...\n%s" % e.message)
    raise
