String.prototype.capitalize = function() {
    return this.charAt(0).toUpperCase() + this.slice(1);
}

String.prototype.camelize = function() {
	var camel_string = "";
	words = this.split(" ");
	for(i=0; i < words.length; i++){
		camel_string += words[i].charAt(0).toUpperCase() + words[i].slice(1).toLowerCase() + " ";
	}
    return camel_string;
}

String.prototype.splitCSV = function(sep){
    for (var foo = this.split(sep = sep || ","), x = foo.length - 1, tl; x >= 0; x--){
        if (foo[x].replace(/"\s+$/, '"').charAt(foo[x].length - 1) == '"'){
            if ((tl = foo[x].replace(/^\s+"/, '"')).length > 1 && tl.charAt(0) == '"'){
                foo[x] = foo[x].replace(/^\s*"|"\s*$/g, '').replace(/""/g, '"');
            } else if (x) {
                foo.splice(x - 1, 2, [foo[x - 1], foo[x]].join(sep));
            }
            else foo = foo.shift().split(sep).concat(foo);
        } else foo[x].replace(/""/g, '"');
    }
    return foo;
};

function trim(str) {
    if(str != undefined) {
	   return str.replace(/^\s+|\s+$/g,'');
    }
}

$.extend($.expr[":"], {
	"contains-ci": function(elem, i, match, array) {
		return (elem.textContent || elem.innerText || $(elem).text() || "").toLowerCase().indexOf((match[3] || "").toLowerCase()) >= 0;
	}
});

function clickButton(id) {
    var button = document.getElementById(id);
    if (button != null) {
        button.click();
    }
    else {
        alert("Button not available");
    }
}

function deleteForm(form_prefix) {
    var deleteFlag = document.getElementById('id_' + form_prefix + '-DELETE');
    var deleteRow = document.getElementById(form_prefix);
    deleteFlag.checked = !deleteFlag.checked;
    if (deleteFlag.checked) {
        deleteRow.style.backgroundColor = "#f0aaaa";
    } else {
        deleteRow.style.backgroundColor = "#ececec";
    }
}

function transformToDummyForm(form_prefix) {
    /* Creating a dummy placeholder form to be used as model for newly added forms */
    if(($('#' + form_prefix + '-__dummy__').html()) != undefined) {
	    $('#' + form_prefix + '-__dummy__').html($('#' + form_prefix + '-__dummy__').html().replace(/__prefix__/g, '__dummy__'));
	}
}

function generateFormsetFormRow(form_prefix) {
    /* Generates a new form for the Formset from a dummy form generated on page load */
    var form_idx = parseInt($('#id_' + form_prefix + '-TOTAL_FORMS').val());
    new_form = $('#' + form_prefix + '-__dummy__').html().replace(/__dummy__/g, form_idx);
    new_form_html = "<tr bgcolor=\"#ECECEC\" class='exclude_export' id=\"" + form_prefix + "-" + form_idx +
     "\" >" + new_form + "</tr>";
    $(new_form_html).insertAfter('#' + form_prefix + '-__dummy__');
    $('#id_' + form_prefix + '-TOTAL_FORMS').val(form_idx + 1);
}

function generateFormsetFormRowBefore(form_prefix) {
    /* Generates a new form for the Formset from a dummy form generated on page load */
    var form_idx = parseInt($('#id_' + form_prefix + '-TOTAL_FORMS').val());
    new_form = $('#' + form_prefix + '-__dummy__').html().replace(/__dummy__/g, form_idx);
    new_form_html = "<tr bgcolor=\"#ECECEC\" class='exclude_export' id=\"" + form_prefix + "-" + form_idx +
     "\" >" + new_form + "</tr>";
    $(new_form_html).insertBefore('#' + form_prefix + '-__dummy__');
    $('#id_' + form_prefix + '-TOTAL_FORMS').val(form_idx + 1);
}

function generateFormsetFormRowAtIndex(form_prefix, index) {
    /* Generates a new form for the Formset from a dummy form generated on page load */
    var form_idx = parseInt($('#id_' + form_prefix + '-TOTAL_FORMS').val());
    new_form = $('#' + form_prefix + '-__dummy__').html().replace(/__dummy__/g, form_idx);
    new_form_html = "<tr bgcolor=\"#ECECEC\" class='exclude_export' id=\"" + form_prefix + "-" + form_idx +
     "\" >" + new_form + "</tr>";
    $(new_form_html).insertBefore(index);
    $('#id_' + form_prefix + '-TOTAL_FORMS').val(form_idx + 1);
}

function generateFormsetForm(form_prefix) {
    /* Generates a new form for the Formset from a dummy form generated on page load */
    var form_idx = parseInt($('#id_' + form_prefix + '-TOTAL_FORMS').val());
    new_form = $('#' + form_prefix + '-__dummy__').html().replace(/__dummy__/g, form_idx);
    new_form_html = "<li class='li-tagit-display' id=\"" + form_prefix + "-" + form_idx + "\">" + new_form + "</li>";
    $(new_form_html).insertBefore('#' + form_prefix + '-__dummy__');
    $('#id_' + form_prefix + '-TOTAL_FORMS').val(form_idx + 1);
}

function generateFormsetFormRowAppend(form_prefix, index) {
    /* Generates a new form for the Formset from a dummy form generated on page load */
    var form_idx = parseInt($('#id_' + form_prefix + '-TOTAL_FORMS').val());
    new_form = $('#' + form_prefix + '-__dummy__').html().replace(/__dummy__/g, form_idx);
    new_form_html = "<tr bgcolor=\"#ECECEC\" class='exclude_export' id=\"" + form_prefix + "-" + form_idx +
     "\" >" + new_form + "</tr>";
    $(index).append(new_form_html);
    $('#id_' + form_prefix + '-TOTAL_FORMS').val(form_idx + 1);
}

function keepSessionAlive(){
	/*
	Dummy AJAX call, to be fired to keep the User Logged In on necessary situations.
	Basically to instruct the User has been busy on the browser-side of the application.
	*/
	$.ajax({
		url: "/erp/json/dummy/",
		type: "post",
		success: function(response){
		// No Action required, as this is a dummy call to keep session alive in certain special cases, like
		// when Indent Materials or GRN Materials are added.
		},
		error : function(xhr,errmsg,err) {
			console.log(xhr.status + ": " + xhr.responseText);
		}
	});
}


var action_count = 0; // Global variable to be used to count the user actions
function refreshSessionPerNActions(N){
	/*
	 Refreshes the session per 'N' number of relevant user-actions, to avoid unnecessary time-outs.
	 Basically this informs the server that the current User has been relevantly active in the Browser side.
	*/
	action_count += 1;
	if(action_count % N == 0){
		keepSessionAlive();
		action_count = 0;
	}
}

$.fn.extend({
    trackChanges: function() {
        $(":input",this).change(function() {
            $(this.form).data("changed", true);
        });
    },
    isChanged: function() {
        return this.data("changed");
    }
});

function setSelectedValue(selectObj, valueToSet) {
    for (var i = 0; i < selectObj.options.length; i++) {
        if (selectObj.options[i].text == valueToSet) {
            selectObj.options[i].selected = true;
            return;
        }
    }
}

function setSelectedNumericValue(selectObj, valueToSet) {
    for (var i = 0; i < selectObj.options.length; i++) {
        if (parseFloat(selectObj.options[i].text) == parseFloat(valueToSet)) {
            selectObj.selectedIndex = i;
            return;
        }
    }
}

function triggerOnChange(element){
    if ("createEvent" in document) {
        var evt = document.createEvent("HTMLEvents");
        evt.initEvent("change", false, true);
        element.dispatchEvent(evt);
    }
    else
        element.fireEvent("onchange");
}

function validateFloatKeyPress(el, evt) {
    var charCode = (evt.which) ? evt.which : event.keyCode;
	if (charCode == 8){
        return true;
    }
    var number = el.value.split('.');
    if (charCode != 46 && charCode > 31 && (charCode < 48 || charCode > 57)) {
        return false;
    }
    if(number.length>1 && charCode == 46){
         return false;
    }
    //get the carat position
    var caratPos = getSelectionStart(el);
    var dotPos = el.value.indexOf(".");
    if( caratPos > dotPos && dotPos>-1 && (number[1].length > 1)){
        return false;
    }
    return true;
}

function validateFloatKeyPressForPrice(el, evt) {
    var charCode = (evt.which) ? evt.which : event.keyCode;
	if (charCode == 8){
        return true;
    }
    var number = el.value.split('.');
    if (charCode != 46 && charCode > 31 && (charCode < 48 || charCode > 57)) {
        return false;
    }
    if(number.length>=2 && charCode == 46){
         return false;
    }
    //get the carat position
    var caratPos = getSelectionStart(el);
    var dotPos = el.value.indexOf(".");
    if( caratPos > dotPos && dotPos>-1 && (number[1].length > 4)){
        return false;
    }
    return true;
}

function getSelectionStart(o) {
    if (o.createTextRange) {
        var r = document.selection.createRange().duplicate()
        r.moveEnd('character', o.value.length)
        if (r.text == '') return o.value.length
        return o.value.lastIndexOf(r.text)
    } else return o.selectionStart
}



function zeroPad(nr,base){
  var  len = (String(base).length - String(nr).length)+1;
  return len > 0? new Array(len).join('0')+nr : nr;
}

function isEmpty(obj) {
    for (var key in obj) {
        if (obj.hasOwnProperty(key))
            return false;
    }
    return true;
}

function validateEmail(email) {
    var re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
}

function validateEmails(email_list) {
    var all_mails_valid = email_list.length > 0;
    var re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    for(i=0;i<email_list.length;i++){
        all_mails_valid = all_mails_valid && re.test(String(email_list[i]).toLowerCase());
    }
    return all_mails_valid;
}

function ChangePartyCurrency(party_id,currency_name,conversion_rate){
    if (party_id !=""){
        $.ajax({
            url: "/erp/purchase/json/po/load_party_currency/",
            type: "post",
            datatype: "json",
            data: {party_id:party_id},
            async: false,
            success: function(response)
            {
                if (response !=""){
                    $("#"+currency_name).val(response);
                    $('#'+currency_name).trigger('chosen:updated');
                    var selectedText = $("#"+currency_name+" option:selected").text();
                    if(response == "None" || response == "0" || response ==null) {
                        selectedText = "-";
                    }
                    $("#currency_unit_display").text(selectedText);
                    if (conversion_rate!=""){
                        if($("#"+currency_name).find("option:selected").text()!=$("#home_currency_id").val()){
                            $('#div_con_rate').removeClass("hide");
                        }else {
                            $('#div_con_rate').addClass("hide");
                            $('#'+conversion_rate).val('1.0');
                        }
                    }
                }
            }
        });
    }
}