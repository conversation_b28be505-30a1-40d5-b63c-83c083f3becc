from datetime import datetime, timedelta

import jwt
from dateutil.relativedelta import relativedelta
from django.contrib.auth import SESSION_KEY
from django.contrib.sessions.models import Session

from erp.auth import SESSION_KEY, ENTERPRISE_ID_SESSION_KEY
from erp.auth import logger, LOGIN_TOKEN
from erp.auth.backend import LoginService
from settings import JWT_SECRET, SESSION_COOKIE_AGE

POST_METHOD = 'POST'
GET_METHOD = 'GET'


class RequestHandler(object):
	"""
	Handles the Http Requests - validates if a request is a valid one, has appropriate permission, handles the request
	session attributes.
	"""

	def __init__(self, request):
		self.request = request

	def setRequest(self, request):
		if request is not None:
			self.request = request

	def isPostRequest(self):
		return self.request.method == POST_METHOD

	def isGetRequest(self):
		return self.request.method == GET_METHOD

	def getData(self, key=None):
		value = None
		if key is None:
			return self.request.GET
		if key in self.request.GET:
			value = self.request.GET[key]
		elif key in self.request.POST:
			value = self.request.POST[key]
		return value

	def getPostData(self, key=None):
		"""
		Fetches the request's Post Data against the key provided.

		:param key:
		:return:
		"""
		value = None
		if self.isPostRequest():
			if key is None:
				return self.request.POST
			if key in self.request.POST:
				value = self.request.POST[key]
		return value

	def getAndCacheData(self, key=None, session_key=None):
		"""
		Fetches the request's Post Data against the key provided.
		(Else) fetches from Session with session_key provided

		:param key:
		:param session_key:
		:return:
		"""
		value = None
		if self.isPostRequest():
			value = self.getPostData(key=key)
		elif key in self.request.GET:
			value = self.request.GET[key]
		if session_key:
			value = self.getSessionAttribute(key=session_key) if value is None else value
			self.setSessionAttribute(key=session_key, value=value)
		return value

	def getEncodedPostData(self, key=None):
		"""
		Fetches the URL Post Data against any key provided, in URL-encoded format

		:param key:
		:return:
		"""
		return self.getPostData(key).urlencode()

	def setSessionAttribute(self, key, value=''):
		if key is not None:
			self.request.session[key] = value

	def getSessionAttribute(self, key, removable=False):
		try:
			if key in self.request.session:
				return self.request.session[key]
			if removable:
				self.removeSessionAttribute(key=key)
		except KeyError:
			return None

	def removeSessionAttribute(self, key):
		if key in self.request.session:
			self.request.session.__delitem__(key)

	def setSessionId(self, session_id):
		self.request.session.session_key = session_id

	def getSessionId(self):
		return self.request.session.session_key

	@staticmethod
	def generateJwtToken(user_id=None, enterprise_id=None, login_on=None, expired_on=None):
		"""

		:param user_id:
		:param enterprise_id:
		:param login_on:
		:param expired_on:
		:return:
		"""
		if not login_on:
			login_on = datetime.now()
		if not expired_on:
			expired_on = datetime.now() + relativedelta(minutes=30)
		token = dict(
			user_id=user_id, enterprise_id=enterprise_id, login_on=login_on.strftime("%Y-%m-%d %H:%M:%S"),
			expired_on=expired_on.strftime("%Y-%m-%d %H:%M:%S"))
		return jwt.encode(token, JWT_SECRET)

	def setSessionToken(self, user_id=None, enterprise_id=None):
		"""

		:param user_id:
		:param enterprise_id:
		:return:
		"""
		self.request.session[LOGIN_TOKEN] = self.generateJwtToken(user_id=user_id, enterprise_id=enterprise_id)

	def isValidJWTRequest(self):
		"""

		:return:
		"""
		try:
			token = self.getPostData('token')
			user_id = self.getPostData('user_id')
			enterprise_id = self.getPostData('enterprise_id')
			if token:
				decoded_token = jwt.decode(token, JWT_SECRET)
				return decoded_token['user_id'] == int(user_id) and decoded_token['enterprise_id'] == int(enterprise_id)
		except Exception as e:
			logger.error("Invalid JWT based request: %s" % e.message)
		return False

	def isSessionActive(self):
		"""
		Verifies if the request is from an active Session, i.e., whether the request is from a Valid logged-in User

		:return:
		"""
		try:
			token = self.getSessionAttribute(LOGIN_TOKEN)
			if token is None and self.isValidJWTRequest():
				return True
			time_now = datetime.now()
			user_id = self.getSessionAttribute(SESSION_KEY)
			enterprise_id = self.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
			if not token and self.getPostData('token'):
				token = self.getPostData('token')
				decoded_token = jwt.decode(token, JWT_SECRET)
				expired_on = datetime.strptime(decoded_token['expired_on'], "%Y-%m-%d %H:%M:%S")
				if expired_on > time_now:
					return True
			if token and enterprise_id:
				decoded_token = jwt.decode(token, JWT_SECRET)
				login_on = datetime.strptime(decoded_token['login_on'], "%Y-%m-%d %H:%M:%S")
				expired_on = login_on + relativedelta(seconds=SESSION_COOKIE_AGE)
				if decoded_token['user_id'] == int(user_id) \
						and decoded_token['enterprise_id'] == int(enterprise_id) and expired_on > time_now:
					self.setSessionToken(user_id=user_id, enterprise_id=enterprise_id)
					logger.debug('Valid Access...')
					return True
			# Removing FCM id for de activated user session
			fcm_id = self.getSessionAttribute('fcm_id')
			if fcm_id:
				LoginService().unRegisterFcmId(
					enterprise_id=self.getSessionAttribute('enterprise_id'),
					user_id=self.getSessionAttribute(SESSION_KEY), fcm_id=fcm_id)
			if user_id:
				logger.warn('Access Denied. No Active Login Session for user_id: %s' % user_id)
			return False
		except Exception as e:
			logger.warn('Session with Id - %s has expired!' % self.getSessionId())
			logger.error(e.message)
		return

	@staticmethod
	def validateAndResetExpiredOnValue(enterprise_id=None):
		"""
		Validate and fetch the recent sessions and filter for given enterprise. Then reset the session expire time for
		force logout of all the users of the enterprise except current user and admin user.
		:param enterprise_id:
		:return:
		"""
		try:
			sessions = Session.objects.filter(
				expire_date__range=[datetime.now(), datetime.now() + timedelta(minutes=30)])
			for session in sessions:
				if ENTERPRISE_ID_SESSION_KEY in session.get_decoded().keys() and session.get_decoded()[
					ENTERPRISE_ID_SESSION_KEY] == enterprise_id:
					session.expire_date = session.expire_date + timedelta(minutes=-60)
					session.save()
			return True
		except Exception as e:
			logger.exception("Something went wrong...%s" % e.message)
			return False

