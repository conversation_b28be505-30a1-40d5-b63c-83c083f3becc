{% extends 'admin/sidebar.html' %}
{% block enterprise %}
<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/registration.css?v={{ current_version }}">
<style type="text/css">
    .registration-details .custom-error-message {
        display: block;
        margin-top: 33px;
    }
    #tagsModal input:checked ~ .toggle-round.on {
		background: #004195;
		color: #FFF;
	}

	#tagsModal input:not(:checked) ~ .toggle-round.off {
		background: #004195;
		color: #FFF;
	}

	#tagsModal .modal-body {
		max-height: 500px !important;
	}

	#tagsModal input:disabled ~ .toggle-round {
		opacity: 0.7;
		cursor: not-allowed;
	}
    	#tagsModal .toggle-round {
		width: 50%;
	    text-align: center;
	    display: inline-block;
	    float: left;
	    border-radius: 50px;
	    padding:  4px;
	}

</style>
<div class="right-content-container">
    <div class="page-title-container">
        <span class="page-title">Location</span>
    </div>
    <div class="container" style="margin-top: 15px;">
        <div class="page-heading_new">
            <form id="location-form" method="post" action="/erp/masters/location_list/">
				{% csrf_token %}
				<input type="hidden" name="enterprise_id" id="enterpriseId" value="">
                <input type="hidden" name="user_id" id="user_id_location" value="">
			</form>

            <a role="button" href="#" class="btn btn-add-new pull-right view_po" style="margin-top: -15px; margin-bottom: 20px;" data-tooltip="tooltip" title="Back" onclick="locationList()">
                <i class="fa fa-arrow-left" aria-hidden="true"></i>
            </a>
        </div>

        <div class="col-sm-10">
            <div class="location-name-container">
                <div class="location-name-edit-container" {% if location_details.location_id %} style="display: none;"{% endif %}>
                    <div class="floating-label col-sm-6 remove-left-padding">
                        <input type="text" class="form-control floating-input" id="id_location-name" maxlength="100" name="location-name" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar');" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar');" placeholder=" " value="{% if location_details.location_id %}{{ location_details.name }}{%endif%}">
                        <label>Location Name<span class="mandatory_mark"> *</span></label>
                    </div>
                    <div class="floating-label col-sm-6">
                        <input class="form-control floating-input" id="id_location-code" name="location-code" type="text" placeholder=" " maxlength="10" value="{% if location_details.location_id %}{{ location_details.code }}{%endif%}" style="max-width: 255px;">
                        <label style="padding-left: 15px;">Location Code<span class="mandatory_mark"> *</span></label>
                    </div>
                    <input id="id_location-id" name="location-id" type="hidden" value="{{ location_details.location_id }}">
                </div>
                {% if location_details.location_id %}
                <div class="location-name-lable-container" style="font-size:24px; cursor: pointer; display: block; margin-bottom: 7px;">
                    {{ location_details.name }}
                    {% if location_details.code %}<small style="font-size:12px;">(<span>{{ location_details.code }}</span>)</small>{% endif %}
                    {% if location_details.is_default == '1' %}<small style="font-size:12px;">(<span>Default</span>)</small>{% endif %}
                    <i class="fa fa-pencil" style="visibility: hidden; font-size: 20px;" aria-hidden="true"></i>
                </div>
                {% endif %}
            </div>
            <div class="clearfix"></div>
        </div>
        <div class="col-lg-5 col-md-12">
            <label class="enterprice-item-heading" style="margin-top: 10px;">Address Details</label>
            <div class="row address-container">
                <div class="floating-label col-sm-12 address-container-address remove-padding">
                    <textarea class="form-control floating-input" cols="40" id="id_location-address" maxlength="300" name="location-address" onkeypress="validateStringOnKeyPress(this,event,'alphaSpecialChar');" onblur="validateStringOnBlur(this, event,'alphaSpecialChar');" placeholder=" " rows="3">{% if location_details.location_id %}{{ location_details.address1 }}{%endif%}</textarea>
                    <label>Address<span class="mandatory_mark"> *</span></label>
                </div>
                <div class="floating-label col-sm-6 address-container-city" style="padding-left: 0;">
                    <input type="text" class="form-control floating-input" id="id_location-city" maxlength="50" name="location-city" onkeypress="validateStringOnKeyPress(this,event, 'alphanumeric');" onblur="validateStringOnBlur(this, event, 'alphanumeric');" placeholder=" " value="{% if location_details.location_id %}{{ location_details.city }}{%endif%}">
                    <label>City<span class="mandatory_mark"> *</span></label>
                </div>
                <div class="floating-label col-sm-6 address-container-country" style="padding-left: 0;margin-bottom:0px !important">
                    <label style="top: -18px; color: #777; left: 4px; font-size: 11px;">Country<span class="mandatory_mark"> *</span></label>
                </div>

                 <div class="floating-label col-sm-6 address-container-country-list" style="padding-left: 0;">
                    <select class="form-control" id="location_country_code" style="border: none; border-bottom: solid 1px #DDD; box-shadow: 0 0; border-radius: 0; padding: 0;">
                    </select>
                </div>

                <div class="floating-label col-sm-6 address-container-state-list" style="padding-left: 0;">
                    <label style="top: -18px; color: #777; left: 4px; font-size: 11px;">State<span class="mandatory_mark"> *</span></label>
                    <select class="form-control" id="location_state_code" style="border: none; border-bottom: solid 1px #DDD; box-shadow: 0 0; border-radius: 0; padding: 0;">
                    </select>
                    <input type="text" class="form-control" id="location_state_text" style="display: none; border: none; border-bottom: solid 1px #DDD; box-shadow: 0 0; border-radius: 0; padding: 0;" value="{{location_details.state}}" />
                </div>
                <div class="floating-label col-sm-6 address-container-pin-code" style="padding-left: 0; padding-right: 0;">
                    <input type="text" class="form-control floating-input" id="id_location-pincode" maxlength="10" name="location-pincode" onfocus="setNumberRangeOnFocus(this,10,0,true)" placeholder=" " value="{% if location_details.location_id %}{{ location_details.pin_code }}{%endif%}">
                    <label>Pincode<span class="mandatory_mark"> *</span></label>
                </div>
                <div class="col-sm-12 line-divider"></div>
                <div class="floating-label col-sm-6" style="padding-left: 0;">
                    <input class="form-control floating-input" id="id_location-contact_person" maxlength="50" name="location-contact_person" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" placeholder=" " type="text" value="{% if location_details.location_id %}{{ location_details.contact_person }}{%endif%}">
                    <label>Contact Person</label>
                </div>
                <div class="floating-label col-sm-6" style="padding-left: 0; padding-right: 0;">
                    <input class="form-control floating-input" id="id_location-email" maxlength="75" name="location-email" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" placeholder=" " type="text" value="{% if location_details.location_id %}{{ location_details.email }}{%endif%}">
                    <label>Email<span class="mandatory_mark"> *</span></label>
                </div>
                <div class="floating-label col-sm-6" style="padding-left: 0;">
                    <input class="form-control floating-input" id="id_location-phone" maxlength="30" name="location-phone" onblur="return validateStringOnBlur(this, event, 'ph_number');" onkeypress="return validateStringOnKeyPress(this,event, 'ph_number');" placeholder=" " type="text" value="{% if location_details.location_id %}{{ location_details.phone_no }}{%endif%}">
                    <label>Contact Number<span class="mandatory_mark"> *</span></label>
                </div>
                <div class="floating-label col-sm-6" style="padding-left: 0; padding-right: 0;">
                    <input class="form-control floating-input" id="id_location-fax" maxlength="30" name="location-fax" onblur="return validateStringOnBlur(this, event, 'ph_number');" onkeypress="return validateStringOnKeyPress(this,event, 'ph_number');" placeholder=" " type="text" value="{% if location_details.location_id %}{{ location_details.fax }}{%endif%}">
                    <label>Fax</label>
                </div>
            </div>
        </div>
        <div class="col-lg-5 col-md-12">
            <label class="enterprice-item-heading" style="margin-top: 10px;">Registration Details</label>
            <div class="row" style="margin: 0; padding: 35px 15px 15px; border: solid 1px #209be1; margin-top: -30px; margin-bottom: 15px;">
                <div class="col-sm-12 remove-padding form-group hide" id="location-port-contianer" style="margin-top: 10px;">
                    <label>Port</label>
                    <input class="form-control" id="id_location-port" maxlength="30" name="location-port" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" placeholder="Enter PORT" type="text" value="{{ location_details.port }}">
                </div>
                <div class="registration-extra-details" style="margin-top: 15px;">
                    <div class="col-sm-12 form-group registration-details" style="padding: 0;">
                        <input type="text" class="form-control registration-label-id hide" value="1" />
                        <label class="gstin_mandate" style="position: absolute; margin-left: 54px; margin-top: 8px;"></label>
                        <input type="text" class="form-control registraion-key" maxlength="30" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" value="GSTIN" placeholder="Label" readonly="" />
                        <input type="text" class="form-control registraion-value" id="location_gstin_number" maxlength="150" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" value='{% if location_details.location_id %}{{ location_details.gst_label }}{%endif%}' placeholder="Detail" />
                    </div>
                    {% if logged_in_user.is_super %}
                     <div class="text-center remove-left-border" id="tagsModal">
                         <input type="text" class="form-control registraion-key" maxlength="30" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" value="Default Location" placeholder="Label" readonly="" />
                         <input hidden="hidden"  value="{{location_details.is_default}}" id="isDefaultValue"/>
									<label role='button' class="form-control registraion-value" id="is_default">

								  		<input class='hide' type="checkbox" id="is_active" name="is_active" {% if location_details.is_default == '1' %} disabled =" " {% endif %}{% if location_details.is_default == '1' %}checked{% endif %}/>
								  		<span class="toggle-round round on" onClick="tagChangeEvent(this)">Yes</span>
								  		<span class="toggle-round round off" onClick="tagChangeEvent(this)">No</span>
									</label>
                     </div>
                      {% endif %}
                </div>
            </div>
            <div class="clearfix"></div>

            <div class="pull-right" style="margin-right: 15px;">
                 {% if logged_in_user.is_super %}
                    {% if location_details.location_id %}
                        <button class="btn btn-save btn-location-save" id="update-location-details" onclick="saveLocationDetails()">Update</button>
                    {% else %}
                        <button class="btn btn-save btn-location-save" id="save-location-details" onclick="saveLocationDetails()">Save</button>
                    {% endif %}
                    </div>
                 {% else %}
                        <button class="btn btn-save btn-location-save" id="update-location-details" onclick="saveLocationDetails()" disabled>Update</button>
                 {% endif %}

        </div>
    </div>
</div>

<script type="text/javascript">
        var is_default =0;
        if($('#isDefaultValue').val() !='None'){
             is_default =$('#isDefaultValue').val();
            }
    function saveLocationDetails() {
        var ControlCollections = [
            {
                controltype: 'textbox',
                controlid: 'id_location-name',
                isrequired: true,
                errormsg: 'Location Name is Required'
            },
            {
                controltype: 'textbox',
                controlid: 'id_location-code',
                isrequired: true,
                errormsg: 'Location code is Required'
            },
             {
                controltype: 'textbox',
                controlid: 'id_location-address',
                isrequired: true,
                errormsg: 'Location Address is Required'
            },
            {
                controltype: 'textbox',
                controlid: 'id_location-city',
                isrequired: true,
                errormsg: 'City is Required'
            },
            {
                controltype: 'dropdown',
                controlid: 'id_country-__prefix__-make_choices',
                isrequired: true,
                errormsg: 'Location Country is Required'
            },
             {
                controltype: 'textbox',
                controlid: 'id_location-pincode',
                isrequired: true,
                errormsg: 'Pincode is Required'
            },
             {
                controltype: 'textbox',
                controlid: 'id_location-email',
                isrequired: true,
                errormsg: 'Email is Required'
            },
            {
                controltype: 'textbox',
                controlid: 'id_location-phone',
                isrequired: true,
                errormsg: 'Contact Number is Required'
            }
            ]
            if ($('#location_state_code').css('display') == 'none') {
                ControlCollections.push({
                controltype: 'textbox',
                controlid: 'location_state_text',
                isrequired: true,
                errormsg: 'Location State Text is Required'
                });
            }
            if ($('#location_state_text').css('display') == 'none') {
                ControlCollections.push({
                controltype: 'dropdown',
                controlid: 'location_state_code',
                isrequired: true,
                errormsg: 'Location State is Required'
                });
            }
        var result = JSCustomValidator.JSvalidate(ControlCollections);
        if(result){
            var url = '/erp/masters/json/location_json/';
            location_id = $('#id_location-id').val();
            let stateCode = $("#location_state_code").val() + "," +$('#location_state_code option:selected').text();
            let stateText = $("#location_state_text").val();
            var countryCode = $('#location_country_code').val().split(',')[0];
            let state = countryCode == 'IN' ? stateCode : stateText;
            var response_message = "";
            if(location_id != 'None'){
                response_message = "Location Updated Successfully";
            }
            else{
                 response_message = "Location Created Successfully";
            }
            $.ajax({
                url: url,
                type: "POST",
                dataType: "json",
                data: {
                    "id": $('#id_location-id').val(),
                    "phone_no": $('#id_location-phone').val(),
                    "email": $('#id_location-email').val(),
                    "fax": $("#id_location-fax").val(),
                    "code": $("#id_location-code").val(),
                    "name": $("#id_location-name").val(),
                    "address1": $("#id_location-address").val().replace(/\n/g, ' ').trim(),
                    "gst_label": $("#location_gstin_number").val(),
                    "contact_person": $('#id_location-contact_person').val(),
                    "enterprise_id": $('#enterprise_id').val(),
                    "city": $("#id_location-city").val(),
                    "state": state,
                    "pin_code": $("#id_location-pincode").val(),
                    "country": $('#location_country_code').val() + "," + $('#location_country_code option:selected').text(),
                    "state":state,
                    "last_modified_by": $('#login_user_id').val(),
                    "is_default": is_default
                },
                success: function (json) {
                    swal({
                        title: "",
                        text: response_message,
                        type: "success",
                        showCancelButton: false,
                        confirmButtonColor: "#209be1",
                        confirmButtonText: "OK",
                        closeOnConfirm: true
                    }, function() {
                        var enterpriseId = $('#enterprise_id').val();
                        var userId = $('#login_user_id').val();
                        var url = "/erp/masters/location_list/?enterprise_id=" + encodeURIComponent(enterpriseId) + "&user_id=" + encodeURIComponent(userId);
                        window.location.href = url;
                    });
                },
                error: function(xhr, errmsg, err) {
                    var response = JSON.parse(xhr.responseText);
                    if (response.response_message == "Requested Item Already Exists") {
                        swal("", "Location Code/ Name already exists", "error");
                    } else if (response.response_message == "Session Timeout") {
                        location.reload();
                        return;
                    }
                    console.log(xhr.status + ": " + xhr.responseText);
                    $(".btn-location-save").val('Save').removeClass('btn-processing');
                }
            });
        }
    }
$(document).ready(function() {
    editableByHoverInit();
    $.ajax({
        url: '/erp/masters/json/countries/',
        type: 'GET',
        success: function(response) {
            if (response.response_code === 200) {
                var countryDropdown = $('#location_country_code');
                countryDropdown.empty();

                $.each(response.data, function(index, country) {
                    countryDropdown.append('<option value="' + country.country_code + '">' + country.country_name + '</option>');
                });

                var selectedCountry = "{{ location_details.country }}" || 'IN';
                selectedCountry = selectedCountry === 'None' ? 'IN' : selectedCountry.split(',')[0];

                var selectedState = "{{ location_details.state }}";

                if (selectedCountry) {
                    $('#location_country_code').val(selectedCountry).trigger('change');

                    if (selectedCountry !== 'IN') {
                        $('#location_state_code').hide();
                        $('#location_state_text').show().val(selectedState);
                    } else {
                        $('#location_state_code').show();
                        $('#location_state_text').hide();
                    }
                }
            } else {
                alert('Failed to fetch countries: ' + response.message);
            }
        },
        error: function() {
            alert('Error fetching countries');
        }
    });
});


$('#location_country_code').change(function() {
    var countryCode = $(this).val();
    if (countryCode !== 'IN') {
        $('#location_state_code').hide();
        $('#location_state_text').show().val('');
    } else {
        $('#location_state_code').show();
        $('#location_state_text').hide();
        $.ajax({
            url: '/erp/masters/json/states/',
            method: 'GET',
            data: {country_code: countryCode},
            success: function(response) {
                if (response.response_code === 200) {
                    var stateDropdown = $('#location_state_code');
                    stateDropdown.empty();
                     var selectedState = "{{ location_details.state }}";
                        var selectedStateCode = selectedState.split(',')[0];
                        $('#location_state_code').val(selectedStateCode).trigger('change');
                    $.each(response.data, function(index, state) {
                        var isSelected = '';
                        if ($('#id_location-id').val() === 'None' && state.description.toUpperCase() === 'TAMIL NADU') {
                            isSelected = ' selected';
                        } else if ($('#id_location-id').val() !== 'None' && state.code == selectedStateCode) {
                            isSelected = ' selected';
                        }
                        stateDropdown.append('<option value="' + state.code + '"' + isSelected + '>' + state.description + '</option>');
                    });
                } else {
                    alert('Failed to fetch states: ' + response.message);
                }
            },
            error: function() {
                alert('Error fetching states');
            }
        });
    }
});
function editableByHoverInit() {
		$( ".location-name-lable-container" ).hover(
		  	function() {
		    	$(this).find(".fa-pencil").css("visibility", "");
		  	}, function() {
		    	$(this).find(".fa-pencil").css("visibility", "hidden");
		  	}
		);

		$(".location-name-lable-container").click(function(){
			$(this).hide();
			$(".location-name-edit-container").show();
			var $initialVal = $("#id_location-name").val();
			$("#id_location-name").val("")
    		$("#id_location-name").focus().val($initialVal);
		});
	}
 function locationList() {
        var enterpriseId = $('#enterprise_id').val();
        var userId = $('#login_user_id').val();
        $('#enterpriseId').val(enterpriseId);
        $('#user_id_location').val(userId);
        $('#location-form').submit();
    }
    function tagChangeEvent(element) {
    if ($(element).hasClass('on')) {
        is_default = 1
    }
    else if ($(element).hasClass('off')) {
        is_default = 0;
    }
            return is_default;
}


</script>

{% endblock %}
