import requests
import json
from conf import *

# Set up logger
logger = logging.getLogger(os.path.splitext(os.path.basename(__file__))[0])
logger.setLevel(logging.DEBUG)
logger.addHandler(logHandler)


# Function to fetch data from MySQL database
def fetch_data_from_mysql():
    try:
        query = """SELECT id, type, project_automation_status
                            FROM voucher
                            WHERE enterprise_id = %s
                              AND voucher_date > '%s'
                              AND project_automation_status in (0,2)
                              AND project_code != %s
                              AND project_code > %s
                              AND status = 1 ORDER BY voucher_date ASC LIMIT 300;""" % (PARENT_ENTERPRISE_ID,
                                                                                     VOUCHER_DATE, PROJECT_CODE,
                                                                                     PROJECT_CODE)
        voucher_data = executeQuery(query=query)
        if voucher_data:
            process_data(rows=voucher_data)
    except Exception as e:
        logger.error("An error occurred while fetching data from MySQL: %s", e)


# Function to process fetched data
def process_data(rows):
    auth_token = login(credentials=CREDENTIALS)
    try:
        for row in rows:  # Example: Process each row
            voucher_id = row[0]
            voucher_type = row[1]
            automation_status = row[2]
            if automation_status == 0:
                is_cloned = clone_voucher(voucher_id=voucher_id, voucher_type=voucher_type, token=auth_token)
                if is_cloned:
                    logger.info("voucher cloned successfully for id %s", voucher_id)
                else:
                    logger.info("Failed to clone the voucher for id %s", voucher_id)
            elif automation_status == 2:
                is_updated = update_voucher(voucher_id=voucher_id, token=auth_token)
                if is_updated:
                    logger.info("child voucher updated successfully for id %s", voucher_id)
                else:
                    logger.info("Failed to update the child voucher for id %s", voucher_id)
    except Exception as e:
        logger.error("An error occurred while processing data: %s", e)


def login(credentials=None):
    """
    :param credentials:
    :return:
    """
    api_url = API_BASE_URL + "/erp/user/json/login_api/"
    token = None
    try:
        login_response = requests.post(api_url, headers=HEADER, data=credentials, timeout=30)
        login_response.raise_for_status()
        response_data = json.loads(login_response.text)
        token = response_data.get("token")
        logger.info("Login successfully")
        return token
    except Exception as e:
        logger.debug("Login Failed - %s" % str(e))
    return token


def clone_voucher(voucher_id, voucher_type, token):
    api_url = API_BASE_URL + "/erp/voucher/clone/service/"
    data = {u'voucher_id': [voucher_id], u'enterprise_id': [PARENT_ENTERPRISE_ID],
            u'csrfmiddlewaretoken': [u'2085bdf9f70dcf986b0b542863cfb3aa'],
            u'type': [voucher_type], u'token': [token]}
    try:
        login_response = requests.post(api_url, headers=HEADER, data=data, timeout=30)
        login_response.raise_for_status()
        response_data = json.loads(login_response.text)
        return response_data.get("data").get("clone_status")
    except Exception as e:
        logger.debug("failed to clone Voucher - %s" % str(e))
        return False


def update_voucher(voucher_id, token):
    api_url = API_BASE_URL + "/erp/voucher/update/child/"
    data = {u'parent_voucher_id': [voucher_id],
            u'csrfmiddlewaretoken': [u'2085bdf9f70dcf986b0b542863cfb3aa'],
            u'token': [token]}
    try:
        login_response = requests.post(api_url, headers=HEADER, data=data, timeout=30)
        login_response.raise_for_status()
        return True
    except Exception as e:
        logger.debug("failed to update child Voucher - %s" % str(e))
        return False


if __name__ == "__main__":
    fetch_data_from_mysql()
