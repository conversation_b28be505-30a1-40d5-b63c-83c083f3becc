"""
Backend for the Stores Module.
"""
import ast
import copy
import itertools
import json
from itertools import zip_longest

import pymysql
import time
from collections import namedtuple
from datetime import datetime, date
from dateutil.relativedelta import relativedelta
from decimal import Decimal

from django.http import HttpResponse
from sqlalchemy import and_, cast, Integer
from sqlalchemy import func
from sqlalchemy import or_
from sqlalchemy import desc

from erp import helper, DEFAULT_MAKE_ID, IS_FAULTY_TRUE
from erp.admin.backend import UserDAO
from erp.commons.backend import push_notification
from erp.dao import DataAccessObject, executeQuery
from erp.forms import IndentForm
from erp.formsets import IndentMaterialFormset, TagFormset
from erp.helper import getUser, constructDifferentMakeName, updateRemarks, model_to_dict
from erp.models import IndentTag, Material, ReceiptMaterial, \
	InvoiceMaterial, Receipt, Invoice, PurchaseOrder, Category, OA, \
	Project, PurchaseOrderMaterial, OAParticulars, OADocument, \
	MaterialMakeMap, Indent, IndentMaterial, Party, MaterialSupplierProfile, ReceiptAttachment, ReceiptDocument, \
	LedgerBill, CreditDebitNote, NoteItem, NoteItemNonStock, NoteReason, \
	NoteItemTax, NoteItemNonStockTax, ReceiptIssueMap, MaterialAlternateUnit, UnitMaster, ReceiptTag, \
	ReceiptMaterialTax, ReceiptTax, ReceiptMaterialRejection, NoteReceiptMap, StockTransfer, TransferDetails, \
	MRP_Materials
from erp.notifications import PUSH_NOTIFICATION
from erp.purchase import logger
from erp.stores import INDENT_COMPLETED, INDENT_PENDING_DUE_TO_PO, INDENT_PENDING_DUE_TO_MATERIAL, logger, \
	RECEIPT_EXCLUDE_FIELD_LIST
from erp.stores.document_compiler import ReceiptPDFGenerator, MaterialRequisitionPDFGenerator
from erp.stores.queries import GRN_COMMON_COLUMNS, GRN_NOT_OTHER_COLUMNS, GRN_COMMON_QUERY, GRN_NOT_OTHER_QUERY
from erp.tags import generateTagFormset, extractEntityTagMapsFromFormset, getEntityTagMaps
from settings import HOST, USER, PASSWORD, DBNAME, PORT, SQLASession
from util.api_util import json_util, response_code
from util.helper import getFinancialYear, constructFormInitializer, copyFormToEntity, readFile, writeFile, \
	convertStringToDate, getFYDateRange, copyDictToDict, getFormattedDocPath
from erp.tasks import process_inventory_update, update_closing_stock

__author__ = 'nandha, kalaivanan'

INDENT_FORM_PREFIX = 'indent'
IND_MAT_PREFIX = 'ind_material'
IND_TAG_PREFIX = 'tag'
PURCHASE_INDENT = 0

def assembleQueriesToDropReceipt():
	return [
		"DELETE FROM grn_material_tax WHERE grn_no=%s",
		"DELETE FROM grn_material WHERE grnNumber=%s",
		"DELETE FROM grn_tax WHERE grn_no=%s",
		"DELETE FROM receipt_tags WHERE receipt_id=%s",
		"DELETE FROM grn_issue_map WHERE grn_no=%s"
	]


class IndentVO(object):
	"""
	A container object that holds both the IndentForm and IndentMaterialFormset to be presented as a single UI object.
	"""

	def __init__(
			self, indent_form=IndentForm(prefix=INDENT_FORM_PREFIX),
			indent_material_formset=IndentMaterialFormset(prefix=IND_MAT_PREFIX),
			indent_tag_formset=TagFormset(prefix=IND_TAG_PREFIX)):
		self.indent_form = indent_form
		self.indent_material_formset = indent_material_formset
		self.indent_tag_formset = indent_tag_formset

	def is_valid(self):
		return self.indent_form.is_valid() and self.indent_material_formset.is_valid() and self.indent_tag_formset.is_valid()

	def __repr__(self):
		return 'IndentForm: %s\nIndentMaterialFormset: %s\n IndentTagFormset: %s' % (
			self.indent_form, self.indent_material_formset, self.indent_tag_formset)


class StoresDAO(DataAccessObject):
	"""
	Class that handles all the Purchase module related DB activities - like fetching, save and deleting persistent objects
	"""

	def getAllIndents(self):
		return self.db_session.query(Indent).all()

	def getReceipt(self, enterprise_id=None, receipt_no=None, financial_year=None, receipt_code=None, sub_number=None):
		"""
		enterprise_id and either receipt_no or (financial_year, code, sub_number) are mandatory
		:return:
		"""
		try:
			if receipt_no:
				return self.db_session.query(Receipt).filter(
					Receipt.enterprise_id == enterprise_id, Receipt.receipt_no == receipt_no).first()
			else:
				return self.db_session.query(Receipt).filter(
					Receipt.enterprise_id == enterprise_id, Receipt.financial_year == financial_year,
					Receipt.receipt_code == receipt_code, Receipt.sub_number == sub_number).first()
		except Exception as e:
			logger.debug(e.message)
			raise

	def getReceiptMaterial(
			self, receipt_no=None, item_id=None, enterprise_id=None, make_id=None, is_faulty=None, oa_id=None,
			po_id=None, dc_id=None, received_grn_id=None):
		return self.db_session.query(ReceiptMaterial).filter(
			ReceiptMaterial.receipt_no == receipt_no, ReceiptMaterial.item_id == item_id,
			ReceiptMaterial.make_id == make_id, ReceiptMaterial.is_faulty == is_faulty,
			or_(ReceiptMaterial.oa_id == oa_id, and_(ReceiptMaterial.oa_id.is_(None), oa_id is None)),
			or_(ReceiptMaterial.po_no == po_id, and_(ReceiptMaterial.po_no.is_(None), po_id is None)),
			or_(ReceiptMaterial.dc_id == dc_id, and_(ReceiptMaterial.dc_id.is_(None), dc_id is None)),
			or_(ReceiptMaterial.received_grn_id == received_grn_id, and_(
				ReceiptMaterial.received_grn_id.is_(None), received_grn_id is None)),
			ReceiptMaterial.enterprise_id == enterprise_id).first()

	def getReceiptMaterialTax(self, receipt_no, item_id, enterprise_id, tax_code, make_id, is_faulty, oa_id, dc_id, po_no, received_grn_id):
		return self.db_session.query(ReceiptMaterialTax).filter(
			ReceiptMaterialTax.receipt_no == receipt_no, ReceiptMaterialTax.item_id == item_id,
			ReceiptMaterialTax.tax_code == tax_code, ReceiptMaterialTax.make_id == make_id,
			ReceiptMaterialTax.is_faulty == is_faulty,
			or_(ReceiptMaterialTax.oa_id == oa_id, and_(ReceiptMaterialTax.oa_id.is_(None), oa_id is None)),
			or_(ReceiptMaterialTax.po_id == po_no, and_(ReceiptMaterialTax.po_id.is_(None), po_no is None)),
			or_(ReceiptMaterialTax.dc_id == dc_id, and_(ReceiptMaterial.dc_id.is_(None), dc_id is None)),
			or_(ReceiptMaterialTax.received_grn_id == received_grn_id, and_(
				ReceiptMaterialTax.received_grn_id.is_(None), received_grn_id is None)),
			ReceiptMaterialTax.enterprise_id == enterprise_id).first()

	def getReceiptTax(self, receipt_no, tax_code, enterprise_id):
		return self.db_session.query(ReceiptTax).filter(
			ReceiptTax.receipt_no == receipt_no, ReceiptTax.tax_code == tax_code,
			ReceiptTax.enterprise_id == enterprise_id).first()

	def getReceiptMaterialRejection(self, receipt_no, item_id, enterprise_id, reason, make_id, is_faulty, oa_id, dc_id, po_no):
		return self.db_session.query(ReceiptMaterialRejection).filter(
			ReceiptMaterialRejection.grn_id == receipt_no, ReceiptMaterialRejection.item_id == item_id,
			ReceiptMaterialRejection.reason == reason, ReceiptMaterialRejection.make_id == make_id,
			ReceiptMaterialRejection.is_faulty == is_faulty, ReceiptMaterialRejection.oa_id == oa_id,
			ReceiptMaterialRejection.dc_id == dc_id, ReceiptMaterialRejection.po_no == po_no,
			ReceiptMaterialRejection.enterprise_id == enterprise_id).first()

	def getReceiptIssueMap(self, receipt_no, issue_no, enterprise_id):
		return self.db_session.query(ReceiptIssueMap).filter(
			ReceiptIssueMap.receipt_no == receipt_no, ReceiptIssueMap.issue_no == issue_no,
			ReceiptIssueMap.enterprise_id == enterprise_id).first()

	def getIndentsByProject(self, project_code):
		return self.db_session.query(Indent).filter(Indent.project_code == project_code).all()

	def getIndent(self, indent_no=None, enterprise_id=None):
		"""

		:param indent_no:
		:param enterprise_id:
		:return:
		"""
		return self.db_session.query(Indent).filter(
			Indent.indent_no == indent_no, Indent.enterprise_id == enterprise_id).first()

	def getIndentMaterial(self, indent_no=None, item_id=None, enterprise_id=None, make_id=None):
		ind_mat_query = self.db_session.query(IndentMaterial).filter(
			IndentMaterial.indent_no == indent_no, IndentMaterial.item_id == item_id,
			IndentMaterial.enterprise_id == enterprise_id, IndentMaterial.make_id == make_id)
		return ind_mat_query.first()

	def getPartyDetails(self, enterprise_id=None):
		part_query = self.db_session.query(Party).filter(Party.enterprise_id == enterprise_id, Party.id != 0).order_by(
			Party.name)
		return part_query

	def getMaterialDetails(self, enterprise_id=None):
		material_query = self.db_session.query(Material).filter(Material.enterprise_id == enterprise_id).order_by(
			'name')
		return material_query

	def getIndentMaterials(self, indent_no=None, enterprise_id=None):
		ind_mat_query = self.db_session.query(IndentMaterial).filter(
			IndentMaterial.indent_no == indent_no, IndentMaterial.enterprise_id == enterprise_id)
		return ind_mat_query.all()

	def getLatestIndentCode(self, financial_year=None, enterprise_id=None):
		"""
		Generate the new indent code, combined with the financial year. As per requirement the numeric part of code is
		to be recycled every year.
		Latest indent number called to generate the new indent no. This method is called instead of the DB's
		autoincrement as id counter will increment even when the data persist is not successful, which hinders the
		functional requirement that indent's must be sequentially numbered.
		:return:
		"""
		financial_year = financial_year if financial_year else datetime.now().strftime('%Y')
		fy_latest_indent_code = self.db_session.query(Indent.indent_id).filter(
			Indent.financial_year == financial_year, Indent.enterprise_id == enterprise_id,
			Indent.indent_module_id == PURCHASE_INDENT,
			Indent.indent_id.isnot(None)).order_by(cast(Indent.indent_id, Integer).desc()).first()
		logger.debug(
			"No of Indents for FY %s in Enterprise %s: %s" % (financial_year, enterprise_id, fy_latest_indent_code))
		indent_code = 1 if fy_latest_indent_code is None or not (
			'%s' % fy_latest_indent_code.indent_id).isdigit() else (
			int('%s' % fy_latest_indent_code.indent_id) + 1)
		logger.debug('Latest Indent Code: %s' % indent_code)
		return indent_code

	def getReceiptAttachment(self, enterprise_id=None, receipt_no=None):
		attachment_query = self.db_session.query(ReceiptAttachment).filter(
			ReceiptAttachment.enterprise_id == enterprise_id, ReceiptAttachment.receipt_no == receipt_no).first()
		return attachment_query

	def getScaleFactor(self, enterprise_id=None, item_id=None, alternate_unit_id=None):
		scale_factor = self.db_session.query(MaterialAlternateUnit.scale_factor).filter(
			MaterialAlternateUnit.enterprise_id == enterprise_id, MaterialAlternateUnit.item_id == item_id,
			MaterialAlternateUnit.alternate_unit_id == alternate_unit_id).first()
		return scale_factor.scale_factor

	def getAlternateUnitName(self, enterprise_id=None, unit_id=None):
		unit_name = self.db_session.query(UnitMaster.unit_name).filter(
			UnitMaster.enterprise_id == enterprise_id, UnitMaster.unit_id == unit_id).first()
		return unit_name.unit_name

	@staticmethod
	def deleteReceipt(enterprise_id=None, receipt_no=None, attachment_id=None):
		"""

		:param enterprise_id:
		:param receipt_no:
		:param attachment_id:
		:return:
		"""
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
		try:
			logger.info("Discarding Receipt for %s" % [enterprise_id, receipt_no])
			cur = conn.cursor()
			cur.execute("DELETE FROM grn_material_rejection WHERE grnNumber=%s" % receipt_no)
			for query in assembleQueriesToDropReceipt():
				cur.execute(query % receipt_no)
			cur.execute("DELETE FROM grn_attachment WHERE grn_no=%s" % receipt_no)
			cur.execute("DELETE FROM grn WHERE grn_no=%s" % receipt_no)
			cur.execute("DELETE FROM attachment WHERE id = " + str(attachment_id))
			conn.commit()
		except Exception as e:
			logger.error("Discarding Receipt is failed due to %s" % e.message)
			conn.rollback()
			raise e
		finally:
			conn.close()

	def saveIndent(self, indent=None):
		"""

		:param indent:
		:return:
		"""
		try:
			if indent.indent_id is None:
				indent.indent_id = self.getLatestIndentCode(
					financial_year=indent.financial_year, enterprise_id=indent.enterprise_id)
				indent.raised_date = datetime.now()
			indent.modified_date = datetime.now()
			self.db_session.add(indent)
		except Exception:
			raise

	def getDashboardData(self, enterprise_id=None, since=None, till=None):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:return:
		"""
		dashboard_details = {}
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
		try:
			stock_statement = self.getStockStatement(enterprise_id=enterprise_id, since=since, till=till)
			dashboard_details['stock_statement'] = stock_statement

			dashboard_details['stock_mix'] = self.__getStockMix(enterprise_id=enterprise_id)
			since = datetime.today() + relativedelta(months=-5)
			since = since - relativedelta(hour=0, minute=0, second=0, microsecond=0)
			since = since + relativedelta(days=-since.day + 1)
			dashboard_details['monthly_closing_stock'] = []

			cur = conn.cursor()
			for i in range(0, 5):
				till = since + relativedelta(months=1)
				to_date_end = till + relativedelta(seconds=-1)
				logger.info("Fetching closing stock between %s and %s" % (since, to_date_end))
				cur.execute("BEGIN")
				cur.callproc("stock_statement", (since, to_date_end, enterprise_id, 0, 0, 0, 0))
				cur.execute(
					'SELECT @_stock_statement_3, @_stock_statement_4 , @_stock_statement_5, @_stock_statement_6')
				stock_result = cur.fetchall()
				cur.execute("COMMIT")
				stock = {'month': ("%s" % since.strftime("%b'%y")), 'closing_stock': stock_result[0][3]}
				dashboard_details['monthly_closing_stock'].append(stock)
				since = till

			stock = {'month': ("%s" % till.strftime("%b'%y")), 'closing_stock': stock_statement['closing_stock']}
			dashboard_details['monthly_closing_stock'].append(stock)
		except:
			raise
		finally:
			conn.close()
		return dashboard_details

	@staticmethod
	def getStockData(since, till, enterprise_id):
		dashboard_details = ()
		try:
			conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
			cur = conn.cursor()
			cur.execute("BEGIN")
			cur.callproc("stock_statement", (since, till, enterprise_id, 0, 0, 0, 0))
			cur.execute('SELECT @_stock_statement_3, @_stock_statement_4 , @_stock_statement_5, @_stock_statement_6')
			stock_result = cur.fetchall()
			cur.execute("COMMIT")
			conn.close()

			for detail in stock_result[0]:
				dashboard_details += (detail,)
		except Warning:
			logger.info('A warning has been caught...')
		return dashboard_details

	@staticmethod
	def getIndentData(enterprise_id):
		dashboard_details = ()
		try:
			indent_status = StoresService().getEnterpriseIndentStatus(enterprise_id=enterprise_id)
			dashboard_details += (indent_status['indent_raised'],)
			dashboard_details += (indent_status['indent_closed'],)
			dashboard_details += (indent_status['indent_pending'],)
			dashboard_details += (indent_status['indent_due_po'],)
			dashboard_details += (indent_status['indent_due_material'],)
		# logger.info("Dashboard Details: %s" % dashboard_details)
		except Warning:
			logger.info('A warning has been caught...')
		return dashboard_details

	def getDCReports(
			self, enterprise_id=None, since=None, till=None, supplier=None, item_id=None, make_id=None, is_faulty=None,
			status=0, is_returnable=False, is_non_returnable=False):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:param supplier:
		:param item_id:
		:param make_id:
		:param is_faulty:
		:param status:
		:param is_returnable:
		:param is_non_returnable:
		:return:
		"""
		logger.info('Fetching DC Details between %s, %s' % (since, till))
		condition = ""
		returnable_condition = ""
		if supplier and supplier != "All":
			condition += " AND invoice.party_id=%s" % supplier
		if item_id and item_id != "All":
			condition += """ AND invoice_materials.item_id='%s' AND invoice_materials.make_id=%s 
				AND invoice_materials.is_faulty=%s """ % (item_id, make_id, is_faulty)
		if is_returnable == is_non_returnable:
			if is_returnable is False:
				returnable_condition = " AND invoice_materials.is_returnable not in (0,1)"
		elif is_returnable != is_non_returnable:
			returnable_condition = " AND invoice_materials.is_returnable=%s" % int(is_returnable)

		material_invoices_query = """SELECT
		invoice.id AS invoice_id,
		invoice.invoice_no AS invoice_no,
		invoice.invoice_code AS invoice_code,
		DATE(invoice.issued_on) AS date,
		party_master.party_name AS customer,
		invoice.type AS dc_type,
		invoice.job_po_id AS ref,
		CONCAT(CONCAT(materials.name, 
			CASE WHEN materials.drawing_no  IS NULL THEN '' 
			ELSE CONCAT(' - ', materials.drawing_no) END),
			IF(invoice_materials.is_faulty = 1, ' (FAULTY)','')) AS instrument,
		unit_master.unit_name AS unit,
		invoice.enterprise_id AS enterprise_id,
		ifnull(SUM(invoice_materials.qty),0) AS quantity,
		CASE WHEN invoice_materials.discount > 0  THEN 
		invoice_materials.unit_rate - (invoice_materials.unit_rate * invoice_materials.discount) / 100  
		ELSE invoice_materials.unit_rate END AS rate,
		invoice_materials.is_returnable AS is_returnable,
		invoice.return_date as return_due_on,
		sum(ifnull(g.acc_qty, 0)) as return_qty,
		MAX(DATE(g.inward_date)) as inward_date,
		invoice.financial_year as financial_year,
		invoice.sub_number as sub_number,
		ifnull(group_concat(CASE WHEN g.status = 0 THEN concat("TMP#", g.grn_no) ELSE concat(g.financial_year, "/GRN/", 
		LPAD(g.receipt_no, 6, 0),ifnull(g.sub_number,"")) END),"") as grn_code,
		po.id as po_id,
		po.orderno as po_no,
		po.financial_year as po_financial_year,
		po.type as po_type,
		po.sub_number as po_sub_number,
		materials.makes_json as make_name
		FROM
		invoice_materials
		    INNER JOIN
		invoice ON invoice.id = invoice_materials.invoice_id
			LEFT JOIN
		purchase_order as po ON po.id =	invoice.job_po_id
		    INNER JOIN
		party_master ON party_master.party_id = invoice.party_id
		    INNER JOIN
		materials ON invoice_materials.item_id = materials.id
		    JOIN		
		unit_master ON materials.unit = unit_master.unit_id
		    AND materials.enterprise_id = unit_master.enterprise_id
		    AND invoice_materials.enterprise_id = materials.enterprise_id
		    LEFT JOIN
		(SELECT item_id, make_id, is_faulty, acc_qty, dc_id, rec_grn_id, grn.status, grn.enterprise_id,
		    grn.inward_date, grn.grn_no, grn.receipt_no, grn.financial_year, grn.sub_number
			FROM grn_material igm 
			JOIN grn ON 
				grn.grn_no = igm.grnNumber AND grn.enterprise_id = igm.enterprise_id 
				AND grn.status > -1 AND igm.rec_grn_id is NULL) AS g
			ON invoice.id = g.dc_id
		    AND invoice.enterprise_id = g.enterprise_id
		    AND invoice_materials.item_id = g.item_id
		    AND invoice_materials.make_id = g.make_id
		    AND invoice_materials.is_faulty = g.is_faulty 
		WHERE
		invoice.type IN ('DC' , 'JDC', 'JIN')
		    AND invoice.enterprise_id = {enterprise_id}
		    AND invoice.prepared_on >= '{since}'
		    AND invoice.prepared_on <= '{till}'
		    AND invoice.status >= {status} {condition} {returnable_condition} group by invoice.id, instrument""".format(
			enterprise_id=enterprise_id, since=since, till=till, status=status, condition=condition,
			returnable_condition=returnable_condition)
		query_list = executeQuery(material_invoices_query, as_dict=True)

		result = []
		today = date.today()
		for item in query_list:
			if item['inward_date'] is None:
				item['inward_date'] = ""
			invoice = SQLASession().query(Invoice).filter(
				Invoice.enterprise_id == enterprise_id, Invoice.id == item['invoice_id']).first()
			inv_number_format = invoice.enterprise.inv_template_config.template_header_details.inv_number_format
			item['code'] = Invoice.generateInternalCode(
				invoice_id=item['invoice_id'], invoice_no=item['invoice_no'], invoice_type=item['dc_type'],
				financial_year=item['financial_year'], sub_number=item['sub_number'], inv_number_format= inv_number_format, invoice_code=item['invoice_code'])
			if item['ref'] and int(item['ref']) != 0:
				item['ref'] = PurchaseOrder.generateInternalCode(
					financial_year=item['po_financial_year'], po_id=item['po_id'], po_no=item['po_no'],
					po_type=item['po_type'],
					sub_number=item['po_sub_number'])
			else:
				item['ref'] = ""
			if item['make_name']:
				make = helper.constructDifferentMakeName(item['make_name'])
				if make:
					item['instrument'] += " [%s]" % make

			item['material_status'] = "-NA-"
			item['delivery_status'] = "-NA-"
			item['delivery_overdue'] = "-NA-"
			item['pending_qty'] = "-NA-"

			if item['is_returnable'] == 1:
				item['material_status'] = "Pending"
				item['delivery_status'] = "On Time"
				item['delivery_overdue'] = 0
				item['pending_qty'] = 0
				delivery = None
				item['pending_qty'] = float(item['quantity']) - float(item['return_qty'])
				if item['return_due_on']:
					delivery = item['return_due_on']
				is_inward_date = type(item['inward_date']) in (date, datetime)
				if float(item['quantity']) <= float(item['return_qty']):
					item['material_status'] = "Returned"
					if item['return_due_on']:
						if is_inward_date and item['inward_date'] > delivery:
							item['delivery_status'] = "Delayed"
							item['delivery_overdue'] = (item['inward_date'] - delivery).days
						else:
							item['delivery_status'] = "On Time"
				elif float(item['return_qty']) > 0:
					item['material_status'] = "Partially Pending"
					if item['return_due_on']:
						if is_inward_date and (item['inward_date'] >= delivery or delivery < today):
							item['delivery_status'] = "Delayed"
							item['delivery_overdue'] = (today - delivery).days
				else:
					if item['return_due_on']:
						if (today - delivery).days > 0:
							item['delivery_overdue'] = (today - delivery).days
							item['delivery_status'] = "Delayed"
				item['return_due_on'] = item['return_due_on'].strftime("%Y-%m-%d") if type(item['return_due_on']) in (
					datetime, date) else item['return_due_on']
			else:
				item['return_due_on'] = ""
				item['grn_code'] = "-NA-"

			item['date'] = item['date'].strftime("%Y-%m-%d") if type(item['date']) in (
				datetime, date) else item['date']
			item['inward_date'] = item['inward_date'].strftime("%Y-%m-%d") if type(item['inward_date']) in (
				datetime, date) else item['inward_date']

			result.append(item)
		return result

	@staticmethod
	def getJobPOByParty(enterprise_id=None, supplier=None, invoice_id=None, issued_on=None):
		try:
			result = []
			issued_on = str(datetime.strptime(str(issued_on), '%Y-%m-%d %H:%M:%S').strftime(str('%Y-%m-%d'))) if issued_on is not None and issued_on != '0000-00-00 00:00:00' and issued_on != "" else issued_on
			if not issued_on or issued_on == "":
				issued_on = datetime.now().strftime("%Y-%m-%d")
			if supplier:
				db_session = SQLASession()
				po_materials_po = db_session.query(
					PurchaseOrder.po_id, PurchaseOrder.po_no, PurchaseOrder.financial_year,
					func.sum(PurchaseOrderMaterial.quantity).label('quantity'), PurchaseOrder.sub_number, PurchaseOrder.is_blanket_po,
					PurchaseOrder.valid_since, PurchaseOrder.valid_till
				).outerjoin(PurchaseOrder.items).filter(
					PurchaseOrder.enterprise_id == enterprise_id, PurchaseOrder.supplier_id == supplier,
					PurchaseOrder.status == 2, PurchaseOrder.type == 1).group_by(PurchaseOrder.po_id).all()

				pos = {}
				for po_s in po_materials_po:
					pos[po_s.po_id] = dict(
						po_id=po_s.po_id, po_no=po_s.po_no, financial_year=po_s.financial_year,
						sub_number=po_s.sub_number, quantity=float(po_s.quantity) if po_s.quantity else 0,
						is_blanket_po=po_s.is_blanket_po, valid_since=po_s.valid_since, valid_till=po_s.valid_till)

				for po in pos.values():
					po = namedtuple("PO", po.keys())(*po.values())
					inv_job_po_no = db_session.query(Invoice.job_po_id).filter(Invoice.id == invoice_id).first()
					query = db_session.query(
						func.sum(ReceiptMaterial.accepted_qty).label('quantity')).join(
						ReceiptMaterial.receipt).filter(
						ReceiptMaterial.po_no == po.po_id, ReceiptMaterial.dc_id.is_(None),
						Receipt.status >= Receipt.STATUS_DRAFT)
					if invoice_id:
						query = query.filter(ReceiptMaterial.dc_id != invoice_id)
					if inv_job_po_no:
						query = query.filter(ReceiptMaterial.po_no != inv_job_po_no[0])
					receipt_materials = query.all()

					receipt_quantity = 0
					for mat in receipt_materials:
						if mat.quantity:
							receipt_quantity = float(mat.quantity)
					valid_since = po.valid_since.strftime('%Y-%m-%d') if po.valid_since is not None else '0000-00-00 00:00:00'
					valid_till = po.valid_till.strftime('%Y-%m-%d') if po.valid_till is not None else '0000-00-00 00:00:00'
					if (po.is_blanket_po and valid_since <= issued_on <= valid_till) or (not po.is_blanket_po and receipt_quantity < po.quantity):
						po_code = PurchaseOrder.generateInternalCode(
							financial_year=po.financial_year, po_no=po.po_no, po_id=po.po_id, po_type=1,
							sub_number=po.sub_number)
						result.append(dict(id=po_code, value=po.po_id))

			return sorted(result, key=lambda a: a['id'])
		except:
			raise

	def getOAByParty(self, enterprise_id=None, party_id=None, invoice_id=None):
		try:
			result = []
			condition = ""
			if invoice_id:
				condition = " AND im.invoice_id != '%s' " % invoice_id
			if party_id:
				oa_query = """SELECT 
							    oa_id, oa_code 
							FROM
							    (SELECT 
							        OA.id AS oa_id,
							            OA.oa_no AS oa_no,
							            OA.financial_year AS financial_year,
							            OA.type AS type,
							            oap.item_id AS item_id,
							            oap.make_id AS make_id,
							            OA.party_id AS party_id,
							            SUM(oap.quantity) AS quantity,
							            OA.enterprise_id AS enterprise_id,
							            CONCAT(OA.financial_year, '/', SUBSTR(OA.type, 1, 1), LPAD(OA.oa_no, 6, 0), IFNULL(OA.sub_number, '')) AS oa_code,
							            (SELECT IFNULL(SUM(im.qty), 0) FROM invoice_materials AS im 
							            JOIN
										invoice AS inv ON inv.id = im.invoice_id
											AND inv.enterprise_id = im.enterprise_id
											AND inv.type != 'JIN'
											AND inv.status > 0				
										Where im.enterprise_id = OA.enterprise_id
											AND im.oa_no = OA.id
											AND im.item_id = oap.item_id
											AND im.make_id = oap.make_id
											AND im.delivered_dc_id IS NULL %s							                
											) as inv_qty
							    FROM
							        order_acknowledgement AS OA
							    JOIN oa_particulars AS oap ON OA.enterprise_id = oap.enterprise_id
							        AND oap.oa_id = OA.id
							        AND OA.status = 1
							    WHERE
							        OA.enterprise_id = '%s'
							            AND OA.party_id = '%s'
							    GROUP BY OA.enterprise_id , OA.id , OA.party_id , oap.item_id , oap.make_id) AS A  
							    where quantity > inv_qty group by oa_id """ % (condition, enterprise_id , party_id)
				oa_materials = executeQuery(oa_query, as_dict=True)
				for mat in oa_materials:
					grn_invoice_no_list = self.getReceiptInvoiceNo(oa_id=mat['oa_id'])
					more_string = ""
					if len(grn_invoice_no_list) > 1:
						more_string = " [+" + str(len(grn_invoice_no_list) - 1) + "]"
					result.append(
						{"code": mat['oa_code'] + "-" + grn_invoice_no_list[0] + more_string if len(grn_invoice_no_list) > 0 else mat['oa_code'],
						 "id": mat['oa_id'], "invoice_against": 'OA'})

				receipts = self.db_session.query(
					Receipt.receipt_no.label("id"), Receipt.invoice_no.label("code"),
					func.sum(ReceiptMaterial.quantity).label('quantity')).join(Receipt.items).filter(
					Receipt.enterprise_id == enterprise_id, Receipt.supplier_id == party_id, Receipt.goods_already_received == 0, ReceiptMaterial.oa_id.is_(None),
					Receipt.received_against == 'Job In', Receipt.status > 0).group_by(Receipt.receipt_no).all()

				grns = {}
				for grn_s in receipts:
					grns[grn_s.id] = grn_s._asdict()
					if not grns[grn_s.id]['quantity']:
						grns[grn_s.id]['quantity'] = 0

				for grn_dict in grns.values():
					grn = namedtuple("GRNObject", grn_dict.keys())(*grn_dict.values())
					query = self.db_session.query(
						InvoiceMaterial.invoice_id, func.sum(InvoiceMaterial.quantity).label('quantity')).join(
						InvoiceMaterial.invoice).filter(
						InvoiceMaterial.receipt_no == grn.id, InvoiceMaterial.delivered_dc_id.is_(None),
						Invoice.status >= Invoice.STATUS_DRAFT)
					if invoice_id:
						query = query.filter(InvoiceMaterial.invoice_id != invoice_id)
					invoice_materials = query.all()
					receipt_quantity = 0
					for mat in invoice_materials:
						if mat.quantity:
							receipt_quantity = float(mat.quantity)
					if receipt_quantity < grn.quantity:
						result.append({"code": grn.code, "id": grn.id, "invoice_against": 'GRN'})

			return sorted(result, key=lambda a: a['id'])
		except:
			raise

	def getReceiptInvoiceNo(self, oa_id=None):
		try:
			receipts = self.db_session.query(Receipt).join(Receipt.items).join(ReceiptMaterial.oa).filter(
				ReceiptMaterial.oa_id == oa_id, ReceiptMaterial.received_grn_id.is_(None),
				Receipt.status > 0).all()
			grn_invoice_numbers = set()
			if receipts:
				for receipt in receipts:
					grn_invoice_numbers.add(receipt.invoice_no)
			return list(grn_invoice_numbers)
		except:
			raise

	@staticmethod
	def getPendingOAByParty(enterprise_id=None, party_id=None, invoice_id=None):
		try:
			result = []
			if party_id:
				db_session = SQLASession()
				grn_materials = db_session.query(
					Receipt.receipt_no.label("id"), Receipt.receipt_code.label("receipt_code"), Receipt.sub_number.label("sub_number"),
					Receipt.financial_year.label("financial_year"), Receipt.invoice_no.label("party_inv_no"),
					Receipt.received_against.label("type"), func.sum(ReceiptMaterial.accepted_qty).label('quantity')
				).outerjoin(Receipt.items).filter(
					Receipt.enterprise_id == enterprise_id, Receipt.supplier_id == party_id,
					Receipt.status == 1, Receipt.received_against == 'Job In').group_by(Receipt.receipt_no).all()

				grn_items = {}
				for grn in grn_materials:
					grn_items[grn.id] = grn._asdict()
					if not grn_items[grn.id]['quantity']:
						grn_items[grn.id]['quantity'] = 0
				for grn_dict in grn_items.values():
					grn = namedtuple("GRNObject", grn_dict.keys())(*grn_dict.values())
					query = db_session.query(
						InvoiceMaterial.invoice_id, func.sum(InvoiceMaterial.quantity).label('quantity')).join(
						InvoiceMaterial.invoice).filter(
						InvoiceMaterial.receipt_no == grn.id, Invoice.status >= Invoice.STATUS_DRAFT,
						Invoice.type == 'JIN')
					if invoice_id:
						query = query.filter(InvoiceMaterial.invoice_id != invoice_id)
					invoice_materials = query.all()
					receipt_quantity = 0
					for mat in invoice_materials:
						if mat.quantity:
							receipt_quantity = float(mat.quantity)
					if receipt_quantity < grn.quantity:
						grn_code = Receipt.generateReceiptCode(
							receipt_no=grn.id, receipt_code=grn.receipt_code, received_against='Job In',
							financial_year=grn.financial_year, sub_number=grn.sub_number)
						result.append({"code": grn.party_inv_no + " - " + grn_code if grn.party_inv_no else grn_code,
						               "invoice_against": 'GRN',  "id": grn.id})
			return sorted(result, key=lambda a: a['id'])
		except:
			raise

	@staticmethod
	def __getInvoiceData(invoice=None, item=None):
		"""

		:param invoice:
		:param item:
		:return:
		"""
		invoice_data = {}
		try:
			today = datetime.today()
			invoice_data['invoice_id'] = invoice.id
			invoice_data['invoice_no'] = invoice.getCode()
			invoice_data['code'] = invoice.getSimpleCode()
			invoice_data['date'] = invoice.issued_on.strftime("%d-%m-%Y") if invoice.issued_on else today.strftime("%d-%m-%Y")
			due_on = invoice.issued_on if invoice.issued_on else today + relativedelta(days=+invoice.customer.receipt_credit_days)
			invoice_data['prepared_on'] = invoice.prepared_on.strftime("%d-%m-%Y")
			invoice_data['customer'] = invoice.customer.name
			invoice_data['dc_type'] = "Jobwork DC" if invoice.type == "JDC" else "DC"
			ref = invoice.po_no if invoice.po_no else ""
			if invoice.order_accept_no:
				ref = ", %s" % invoice.order_accept_no
			invoice_data['ref'] = invoice.job_po.getCode() if invoice.type == "JDC" else ref
			invoice_data['instrument'] = "%s %s %s" % (
				item.item.name, "[%s]" % item.make.label if item.make_id != DEFAULT_MAKE_ID else "",
				"[%s]" % "Faulty" if item.is_faulty == IS_FAULTY_TRUE else "")
			invoice_data['unit'] = item.item.unit.unit_name
			invoice_data['enterprise_id'] = invoice.enterprise_id
			invoice_data['quantity'] = float(item.quantity)
			invoice_data['rate'] = float(item.rate)
			invoice_data['value'] = round(float(invoice.grand_total), 2)
			if invoice.currency:
				invoice_data['currency_name'] = invoice.currency.code

			invoice_data['due_on'] = due_on.strftime("%d-%m-%Y")
			invoice_data['due_days'] = 0
			if invoice.ledger_bill and invoice.ledger_bill.net_value == 0:
				invoice_data['payment_status'] = 'Received'
			else:
				if due_on >= today:
					invoice_data['payment_status'] = 'On Track'
				else:
					invoice_data['payment_status'] = 'Delayed'
					td = today - due_on
					invoice_data['due_days'] = int(td.days)
		except Exception as e:
			logger.exception("Exception while forming invoice_data.  %s" % e)
		return invoice_data

	@staticmethod
	def __getInvoiceNonStockData(invoice=None, item=None):
		"""

		:param invoice:
		:param item:
		:return:
		"""
		invoice_data = {}

		try:
			today = datetime.today()
			invoice_data['invoice_id'] = invoice.id
			invoice_data['invoice_no'] = invoice.getCode()
			invoice_data['code'] = invoice.getSimpleCode()
			invoice_data['date'] = invoice.issued_on.strftime("%d-%m-%Y")
			due_on = invoice.issued_on + relativedelta(days=+invoice.customer.receipt_credit_days)
			invoice_data['customer'] = invoice.customer.name
			invoice_data['dc_type'] = "Jobwork DC" if invoice.type == "JDC" else "DC"
			invoice_data['ref'] = invoice.po_no + invoice.order_accept_no
			invoice_data['instrument'] = item.item_name
			invoice_data['unit'] = item.unit.unit_name
			invoice_data['enterprise_id'] = invoice.enterprise_id
			invoice_data['quantity'] = float(item.quantity)
			invoice_data['rate'] = float(item.rate)
			invoice_data['value'] = float(invoice.grand_total)
			if invoice.currency:
				invoice_data['currency_name'] = invoice.currency.code
			invoice_data['due_on'] = due_on.strftime("%d-%m-%Y")
			invoice_data['due_days'] = 0
			if invoice.ledger_bill and invoice.ledger_bill.net_value == 0:
				invoice_data['payment_status'] = 'Received'
			else:
				if due_on >= today:
					invoice_data['payment_status'] = 'On Track'
				else:
					invoice_data['payment_status'] = 'Delayed'
					td = today - due_on
					invoice_data['due_days'] = int(td.days)
		except Exception as e:
			logger.exception("Exception while forming invoice_data.  %s" % e)
		return invoice_data

	def __getStockMix(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		stock_mix = []
		try:
			categories = self.db_session.query(Category).filter(Category.enterprise_id == enterprise_id).all()
			for category in categories:
				category_materials = self.db_session.query(Material).filter(
					Material.category_id == category.id, Material.enterprise_id == category.enterprise_id).all()

				received_value = 0
				issue_value = 0
				for material in category_materials:
					# Receipt value
					receipt_materials = self.db_session.query(
						func.sum(ReceiptMaterial.accepted_qty).label('accepted_qty')).join(
						ReceiptMaterial.receipt).filter(
						ReceiptMaterial.item_id == material.material_id,
						ReceiptMaterial.enterprise_id == material.enterprise_id,
						Receipt.status >= 0).first()
					if receipt_materials:
						received_value += receipt_materials.accepted_qty * material.price if receipt_materials.accepted_qty else 0

					# Invoice value
					invoice_materials = self.db_session.query(
						func.sum(InvoiceMaterial.quantity).label('quantity')).filter(
						InvoiceMaterial.enterprise_id == material.enterprise_id,
						InvoiceMaterial.item_id == material.material_id).first()
					if invoice_materials:
						issue_value += invoice_materials.quantity * material.price if invoice_materials.quantity else 0

				value = float(received_value - issue_value)
				data = {'category': category.name, 'value': value}
				stock_mix.append(data)
			logger.info("Size Stock mix report: %s" % len(stock_mix))
		except:
			raise
		return stock_mix

	@staticmethod
	def getStockStatement(enterprise_id=None, since=None, till=None):
		"""

		Gets the Stock overview
		:param enterprise_id:
		:param since:
		:param till:
		:return:
		"""
		stock_statement = {}
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
		try:
			cur = conn.cursor()
			cur.execute("BEGIN")
			cur.callproc("stock_statement", (since, till, enterprise_id, 0, 0, 0, 0))
			cur.execute('SELECT @_stock_statement_3, @_stock_statement_4 , @_stock_statement_5, @_stock_statement_6')
			stock_result = cur.fetchall()
			cur.execute("COMMIT")

			stock_statement['opening_stock'] = stock_result[0][0]
			stock_statement['stock_receipt'] = stock_result[0][1]
			stock_statement['stock_issues'] = stock_result[0][2]
			stock_statement['closing_stock'] = stock_result[0][3]
		except:
			raise
		finally:
			conn.close()
		return stock_statement

	def getGRNReport(self, enterprise_id=None, since=None, till=None, supplier=None, material=None, approved_only=-1):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:param supplier:
		:param material:
		:param approved_only:
		:return:
		"""
		try:
			logger.info('Fetching Goods Receipts between %s, %s' % (since, till))
			receipt_materials_query = self.db_session.query(ReceiptMaterial).join(ReceiptMaterial.receipt).filter(
				Receipt.enterprise_id == enterprise_id, Receipt.inward_date >= since, Receipt.inward_date <= till,
				Receipt.received_against != "Note",
				Receipt.status > approved_only)
			if supplier:
				receipt_materials_query = receipt_materials_query.filter(Receipt.supplier_id.in_(supplier))
			if material:
				receipt_materials_query = receipt_materials_query.filter(ReceiptMaterial.item_id == material)
			receipt_materials = receipt_materials_query.order_by(Receipt.inward_date, Receipt.receipt_code).all()
			grn_report_response = []
			for item in receipt_materials:
				make = None
				make = helper.constructDifferentMakeName(item.material.makes_json)
				material_name = item.material.name
				if make:
					material_name += " [%s]" % make
				if item.is_faulty == IS_FAULTY_TRUE:
					material_name += " [Faulty]"
				response = {
                    'grn_drawing_no': item.material.drawing_no if item.material.drawing_no else '',
					'grn_no': item.receipt.getCode(), 'grn_id': item.receipt_no,
					'grn_date': str(item.receipt.receipt_date),
					'party_name': item.receipt.supplier.name if item.receipt.supplier else (item.po.issue_to if item.po and item.receipt.received_against == 'Issues' else ""),
					'party_inv_no': item.receipt.invoice_no,
					'party_inv_date': str(item.receipt.invoice_date),
					'grn_material': material_name, 'unit': str(item.material.unit),
					'dc_qty': str(item.quantity), 'rec_qty': str(item.received_qty),
					'inw_qty': str(item.accepted_qty), 'short_qty': str(
						item.quantity - item.received_qty) if item.receipt.received_against in (
						"Purchase Order", "Job Work", "Delivery Challan", "Job In") else 0,
					'rej_qty': str(item.received_qty - item.accepted_qty), 'rate': str(item.rate),
					'disc': str(item.discount),
					'gtotal': str(round(item.receipt.getTotalMaterialValue(), 2)),
					'project': " %s (%s)" % (item.receipt.project.name, item.receipt.project.code) if item.receipt.project else ""}
				if item.taxes:
					for item_tax in item.taxes:
						response[item_tax.tax.type] = round(
							Decimal(item.quantity) * Decimal(item.rate) * Decimal(item_tax.tax.net_rate) * Decimal(
								100 - (item.discount if item.discount else 0)) / (100 * 100), 2)
						response[item_tax.tax.type + '_rate'] = "%s" % item_tax.tax.net_rate
				grn_report_response.append(response)

		except:
			raise
		return sorted(grn_report_response, key=lambda a: a['grn_id'])

	def getSALESReport(
			self, enterprise_id=None, since=None, till=None, supplier=None, item_id=None, make_id=1,
			approved_only=-1):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:param supplier:
		:param item_id:
		:param make_id:
		:param approved_only:
		:return:
		"""
		try:
			logger.info('Fetching Sales Invoice between %s, %s' % (since, till))
			invoice_materials_query = self.db_session.query(InvoiceMaterial).join(InvoiceMaterial.invoice).filter(
				Invoice.enterprise_id == enterprise_id, Invoice.prepared_on >= since, Invoice.prepared_on <= till,
				Invoice.type.in_(Invoice.TYPES["sales"]), Invoice.status > approved_only)
			if supplier:
				invoice_materials_query = invoice_materials_query.filter(Invoice.party_id == supplier)
			if item_id:
				invoice_materials_query = invoice_materials_query.filter(
					InvoiceMaterial.item_id == item_id, InvoiceMaterial.make_id == make_id)
			invoice_materials = invoice_materials_query.order_by(Invoice.prepared_on, Invoice.invoice_no).all()
			sales_report_response = []
			for invoice_item in invoice_materials:
				make_name = helper.constructDifferentMakeName(invoice_item.item.makes_json)
				response = {
                    'drawing_no': invoice_item.item.drawing_no if invoice_item.item.drawing_no else '',
					'invoice_no': invoice_item.invoice.getCode(),
					'invoice_date': str(invoice_item.invoice.issued_on),
					'invoice_id': str(invoice_item.invoice_id),
					'party_name': invoice_item.invoice.customer.name, 'invoice_material': "%s %s %s" % (
						invoice_item.item.name, "[%s]" % make_name if make_name else "",
						"[Faulty]" if invoice_item.is_faulty == IS_FAULTY_TRUE else ""), 'unit': str(invoice_item.item.unit),
					'qty': str(invoice_item.quantity), 'rate': str(invoice_item.rate),
					'disc': str(invoice_item.discount), 'gtotal': str(
						round(invoice_item.quantity * invoice_item.rate * (100 - invoice_item.discount) / 100, 2))}
				if invoice_item.taxes:
					for item_tax in invoice_item.taxes:
						response[item_tax.tax.type] = round(
							Decimal(invoice_item.quantity) * Decimal(invoice_item.rate) * Decimal(
								item_tax.tax.net_rate) * Decimal(
								100 - (invoice_item.discount if invoice_item.discount else 0)) / (100 * 100), 2)
						response[item_tax.tax.type + '_rate'] = "%s" % item_tax.tax.net_rate
				sales_report_response.append(response)

		except:
			raise

		return sorted(sales_report_response, key=lambda a: a['invoice_id'])

	@staticmethod
	def getGRNOverView(enterprise_id=None, since=None, till=None):
		"""

		Gets the GRN overview
		:param enterprise_id:
		:param since:
		:param till:
		:return:
		"""
		dashboard_details = {}
		try:
			query = """SELECT `status`, COUNT(1) as `count`
						FROM grn WHERE enterprise_id = %s AND rec_against NOT IN ('Note', 'Sales Return', 'Issues')
						AND grn_date BETWEEN '%s' AND '%s' GROUP BY `status`""" % (enterprise_id, since, till)
			grn_result = executeQuery(query=query)
			dashboard_details['grn_raised'] = 0
			dashboard_details['grn_inprocess'] = 0
			for status in grn_result:
				if status[0] == Receipt.STATUS_REJECTED:
					dashboard_details['grn_rejected'] = status[1]
				if status[0] == Receipt.STATUS_ICD_VERIFIED:
					dashboard_details['grn_accounted'] = status[1]
				else:
					dashboard_details['grn_inprocess'] += status[1]
				dashboard_details['grn_raised'] += status[1]
			return dashboard_details
		except:
			raise

	def getStockDetail(
			self, enterprise_id=None, drawing_no=None, item_id=None, make_id=1, is_faulty=0, exclude_drafts=False, since=None,
			till=None):
		"""

		:param enterprise_id:
		:param drawing_no:
		:param item_id:
		:param make_id:
		:param is_faulty:
		:param exclude_drafts:
		:param since:
		:param till:
		:return:
		"""
		try:
			stock_detail = dict(opening_stock=self.getClosingStock(
				enterprise_id=enterprise_id, item_id=item_id, make_id=make_id, is_faulty=is_faulty,
				exclude_drafts=exclude_drafts, till=since))
			stock_detail['closing_stock'] = stock_detail['opening_stock']

			stock_detail['drawing_no'] = drawing_no
			draft_status = -1  # Rejected
			if exclude_drafts:
				draft_status = 0  # Draft
			query = []
			query.append("""select receipt_no, grn_date, 0, acc_qty, grn.enterprise_id, grn_no, 'REC' 
				from grn inner join grn_material gm on gm.grnNumber=grn.grn_no 
				where grn.enterprise_id=%s and gm.item_id='%s' and gm.make_id=%s 
				and is_faulty=%s and status>%s and inward_date between '%s' and '%s' and gm.rec_grn_id is null""" % (
				enterprise_id, item_id, make_id, is_faulty, draft_status, since, till))

			query.append("""select invoice_no, IFNULL(issued_on, NOW()) AS issued_on, qty, 0, invoice.enterprise_id, invoice.id, 
				substring(invoice.type, 1, 1) as `type` from invoice inner join invoice_materials imd on imd.invoice_id=invoice.id 
				where invoice.enterprise_id=%s and imd.item_id='%s' and imd.make_id=%s and is_faulty=%s and status>%s 
				and IFNULL(issued_on, NOW()) between '%s' and '%s' and imd.delivered_dc_id is null """ % (
				enterprise_id, item_id, make_id, is_faulty, draft_status, since, till))

			details = []
			for q in query:
				result = executeQuery(q)
				for row in result:
					if float(row[2]) == 0.0 and float(row[3]) == 0.0:
						continue
					detail = {}
					if row[0] is None or '%s' % row[0] == '0':
						detail['doc_no'] = "%s-Draft-%s" % (row[6], row[5])
					else:
						detail['doc_no'] = "%s-%s" % (row[6], row[0])
					detail['date'] = row[1].strftime("%Y-%m-%d")
					detail['issue_qty'] = float(row[2])
					detail['receipt_qty'] = float(row[3])
					stock_detail['closing_stock'] = stock_detail['closing_stock'] + float(row[3]) - float(row[2])
					details.append(detail)
			details = sorted(details, key=lambda a: a['date'])
			stock_detail['stock_details'] = details
		except:
			raise
		return stock_detail

	def listAllIssues(self, enterprise_id=None, since=None, till=None):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:return:
		"""
		result_map = None
		try:
			logger.info('Fetching issues between %s, %s' % (since, till))
			result = self.db_session.query(Invoice).filter(
				Invoice.enterprise_id == enterprise_id, Invoice.issued_on >= since, Invoice.type == "Issue",
				Invoice.issued_on <= till).order_by(Invoice.issued_on).all()
			result_map = json_util.objectAsArrayMap(result)
			for item in result_map:
				materials = item['materials']
				if len(materials) > 1:
					item['material_name'] = 'Multiple'
				else:
					m = self.db_session.query(Material).filter(
						Material.material_id == materials[0]['item_id']).first()
					item['material_name'] = m.name

				item['project_name'] = self.db_session.query(Project).filter(
					Project.id == item['project_code']).first().name
				item['issued_to_person'] = item['issued_to']
		except Exception as e:
			logger.exception(e)
		return result_map

	def listAllIndents(self, enterprise_id=None, since=None, till=None):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:return:
		"""
		try:
			logger.info('Fetching indents between %s, %s' % (since, till))
			result = self.db_session.query(Indent).filter(
				Indent.enterprise_id == enterprise_id,
				Indent.raised_date >= since,
				Indent.raised_date <= till).order_by(Indent.raised_date).all()
			result_map = json_util.objectAsArrayMap(result)
			for item in result_map:
				item['project_name'] = self.db_session.query(Project).filter(
					Project.id == item['project_code']).first().name
		except:
			raise
		return result_map

	def getClosingStock(
			self, enterprise_id=None, item_id=None, till=datetime.now(), is_faulty=None, issued_on=None,
			exclude_drafts=False, location_id=None, invoice_id=None):
		"""

		:param enterprise_id:
		:param item_id:
		:param till:
		:param is_faulty:
		:param issued_on:
		:param exclude_drafts:
		:param location_id:
		:param invoice_id
		:return:
		"""
		from_date = str(issued_on) if issued_on else till.strftime("%Y-%m-%d %H:%M:%S")
		grn_draft_status = 0
		draft_status = 0 if exclude_drafts else -1
		try:
			received_stock_query = self.db_session.query(
				func.sum(ReceiptMaterial.accepted_qty).label('accepted_qty')).join(
				ReceiptMaterial.receipt)
			receipt_filter = [
				Receipt.inward_date < from_date, Receipt.enterprise_id == enterprise_id,
				ReceiptMaterial.item_id == item_id, Receipt.status > grn_draft_status,
				ReceiptMaterial.received_grn_id.is_(None)]
			received_transfer_query = self.db_session.query(
				func.sum(TransferDetails.quantity).label('quantity')).join(
				TransferDetails.stock_transfer)
			receipt_transfer_filter = [
				StockTransfer.approved_on < from_date, StockTransfer.enterprise_id == enterprise_id,
				TransferDetails.item_id == item_id, StockTransfer.status == 3]
			issued_stock_query = self.db_session.query(
				func.sum(InvoiceMaterial.quantity).label('issued_qty')).join(
				InvoiceMaterial.invoice)
			issue_filter = [
				or_(Invoice.issued_on < from_date, Invoice.issued_on.is_(None)), Invoice.enterprise_id == enterprise_id,
				InvoiceMaterial.item_id == item_id, Invoice.status > draft_status,
				InvoiceMaterial.delivered_dc_id.is_(None)]
			issue_transfer_query = self.db_session.query(
				func.sum(TransferDetails.quantity).label('quantity')).join(
				TransferDetails.stock_transfer)
			issue_transfer_filter = [
				StockTransfer.approved_on < from_date, StockTransfer.enterprise_id == enterprise_id,
				TransferDetails.item_id == item_id, StockTransfer.status > 1, StockTransfer.status != 4]
			mrp_allocated_query = ""
			mrp_allocated_filter = ""
			mrp_issued_filter = ""
			mrp_issued_query = ""
			if is_faulty == 0:
				mrp_allocated_query = self.db_session.query(func.sum(MRP_Materials.allocated_qty).label('allocated_qty'))
				mrp_allocated_filter = [
					MRP_Materials.updated_on < from_date, MRP_Materials.enterprise_id == enterprise_id,
					MRP_Materials.item_id == item_id]
				mrp_issued_query = self.db_session.query(func.sum(MRP_Materials.issued_qty).label('issued_qty'))
				mrp_issued_filter = [
					MRP_Materials.updated_on < from_date, MRP_Materials.enterprise_id == enterprise_id,
					MRP_Materials.item_id == item_id]
			if invoice_id:
				issue_filter.append(InvoiceMaterial.invoice_id != invoice_id)
			if is_faulty is not None:
				is_faulty = int(is_faulty)
				receipt_filter.append(ReceiptMaterial.is_faulty == is_faulty)
				issue_filter.append(InvoiceMaterial.is_faulty == is_faulty)
				receipt_transfer_filter.append(TransferDetails.is_faulty == is_faulty)
				issue_transfer_filter.append(TransferDetails.is_faulty == is_faulty)
			if location_id is not None:
				location_id = int(location_id)
				receipt_filter.append(ReceiptMaterial.location_id == location_id)
				receipt_transfer_filter.append(StockTransfer.to_location == location_id)
				issue_filter.append(InvoiceMaterial.location_id == location_id)
				issue_transfer_filter.append(StockTransfer.from_location == location_id)
				if is_faulty == 0:
					mrp_allocated_filter.append(MRP_Materials.location_id == location_id)
					mrp_issued_filter.append(MRP_Materials.location_id == location_id)
			received_qty_row = received_stock_query.filter(*receipt_filter).group_by(ReceiptMaterial.item_id).first()
			issued_qty_row = issued_stock_query.filter(*issue_filter).group_by(InvoiceMaterial.item_id).first()

			received_transfer_qty_row = received_transfer_query.filter(*receipt_transfer_filter).group_by(TransferDetails.item_id).first()
			issue_transfer_qty_row = issue_transfer_query.filter(*issue_transfer_filter).group_by(TransferDetails.item_id).first()
			mrp_allocated_row = ""
			mrp_issued_row = ""
			if is_faulty == 0:
				mrp_allocated_row = mrp_allocated_query.filter(*mrp_allocated_filter).group_by(MRP_Materials.item_id).first()
				mrp_issued_row = mrp_issued_query.filter(*mrp_issued_filter).group_by(MRP_Materials.item_id).first()

			opening_rec_qty = received_qty_row.accepted_qty if received_qty_row else 0
			opening_inv_qty = issued_qty_row.issued_qty if issued_qty_row else 0

			opening_transfer_rec_qty = received_transfer_qty_row.quantity if received_transfer_qty_row else 0
			opening_transfer_iss_qty = issue_transfer_qty_row.quantity if issue_transfer_qty_row else 0
			issued_qty = 0
			allocated_qty = 0
			if is_faulty == 0:
				allocated_qty = mrp_allocated_row[0] if mrp_allocated_row else 0
				issued_qty = mrp_issued_row[0] if mrp_issued_row else 0
			opening_qty = (opening_rec_qty+opening_transfer_rec_qty + issued_qty) - (opening_inv_qty + opening_transfer_iss_qty + allocated_qty)

			return float(opening_qty)
		except Exception as e:
			logger.exception(e)
			raise

	def canDeleteReceipt(self, enterprise_id=None, receipt_no=None):
		"""

		:return:
		"""
		try:
			grn_materials = self.db_session.query(ReceiptMaterial).filter(
				ReceiptMaterial.enterprise_id == enterprise_id, ReceiptMaterial.receipt_no == receipt_no).all()
			store_service = StoresService()
			for material in grn_materials:
				if material.material.is_stocked:
					# stock = self.getClosingStock(
					# 	enterprise_id=enterprise_id, item_id=material.item_id,
					# 	is_faulty=material.is_faulty, till=datetime.now())
					stock = 0
					stock_details = store_service.getClosingStock(
						mat_list=[int(material.item_id)], enterprise_id=enterprise_id, is_faulty=0)
					for item in stock_details:
						stock = item['closing_qty']

					logger.info("Stock Qty:%s GRN Qty:%s" % (stock, material.accepted_qty))
					if stock < material.accepted_qty:
						return False
			return True
		except:
			raise

	def getMaterialStock(self, enterprise_id=None, item_id=None):
		"""

		:param enterprise_id:
		:param item_id:
		:return:
		"""
		makes = self.db_session.query(MaterialMakeMap).filter(
			MaterialMakeMap.item_id == item_id,
			MaterialMakeMap.enterprise_id == enterprise_id).all()
		today = datetime.today()
		material_stock = []
		store_service = StoresService()
		for make in makes:
			# stock = self.getClosingStock(
			# 	enterprise_id=enterprise_id, item_id=item_id, till=today, is_faulty=True)
			stock = 0
			stock_details = store_service.getClosingStock(
				mat_list=[int(item_id)], enterprise_id=enterprise_id, is_faulty=1)
			for item in stock_details:
				stock = item['closing_qty']

			if stock != 0:
				material_stock.append({
					"make_name": make.make.label, "make_id": make.make_id, "closing_stock": float(stock),
					"is_faulty": True, "part_name": "-" + make.part_no if (make.part_no is not None) else "",
					"unit": make.material.unit.unit_name if make.material.unit else ""})
			# stock = self.getClosingStock(
			# 	enterprise_id=enterprise_id, item_id=item_id, till=today, is_faulty=False)
			stock = 0
			stock_details = store_service.getClosingStock(
				mat_list=[int(item_id)], enterprise_id=enterprise_id, is_faulty=0)
			for item in stock_details:
				stock = item['closing_qty']
			if stock != 0:
				material_stock.append({
					"make_name": make.make.label, "make_id": make.make_id, "closing_stock": float(stock),
					"is_faulty": False, "part_name": "-" + make.part_no if (make.part_no is not None) else "",
					"unit": make.material.unit.unit_name if make.material.unit else ""})

		return material_stock

	def notificationReceiptCount(self, enterprise_id=None, status=(None,), received_against=None):
		"""

		:param enterprise_id:
		:param status:
		:param received_against:
		:return:
		"""
		try:
			query = self.db_session.query(Receipt).filter(Receipt.enterprise_id == enterprise_id)
			if status is not None:
				query = query.filter(Receipt.status.in_(status))

			if received_against in ("Issues", "Sales Return"):
				query = query.filter(Receipt.received_against == received_against)
			else:
				query = query.filter(Receipt.received_against.notin_(("Issues", "Sales Return")))
			return query.count()
		except:
			raise

	def getCustomerDetails(self, enterprise_id=None, supplier_id=None):
		"""

		:param enterprise_id:
		:param supplier_id:
		:return:
		"""
		try:
			return self.db_session.query(Party).filter(
				Party.enterprise_id == enterprise_id, Party.id == supplier_id).first()
		except:
			raise

	def getIssuedToOfGrn(self, enterprise_id=None, receipt_no=None):
		"""

		:param enterprise_id:
		:param receipt_no:
		:return:
		"""
		try:
			return self.db_session.query(Invoice.issued_to).outerjoin(
				ReceiptIssueMap, and_(
					ReceiptIssueMap.issue_no == Invoice.id,
					ReceiptIssueMap.enterprise_id == Invoice.enterprise_id)
			).filter(ReceiptIssueMap.enterprise_id == enterprise_id, ReceiptIssueMap.receipt_no == receipt_no).first()
		except:
			raise

	def getReceiptNoteCount(self, enterprise_id=None, status=None):
		"""

		:param enterprise_id:
		:param status:
		:return:
		"""
		try:
			query = self.db_session.query(Receipt).filter(
				Receipt.received_against.in_(
					('Purchase Order', 'Job Work', 'Note', 'Delivery Challan', 'Sales Return')),
				Receipt.enterprise_id == enterprise_id)
			if status is not None:
				query = query.filter(Receipt.status == status)
			return query.count()
		except:
			raise

	@staticmethod
	def notifySaveIntentCount(enterprise_id=None, sender_id=None, include_sender=True, entity=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param include_sender:
		:param entity:
		:return:
		"""
		try:
			message = PUSH_NOTIFICATION["indent"] % (
				entity.getCode(), len(entity.materials))
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="PURCHASE",
				message=message, include_sender=include_sender)
		except Exception as e:
			logger.error("Failed notification.. %s " % e.message)

	def getOACodeByInvoice(self, enterprise_id=None, invoice_id=None):
		"""

		:param enterprise_id:
		:param invoice_id:
		:return:
		"""
		try:
			query = self.db_session.query(OA).join(OA.invoice_materials).filter(
				OA.enterprise_id == enterprise_id, InvoiceMaterial.invoice_id == invoice_id,
				InvoiceMaterial.enterprise_id == OA.enterprise_id).group_by(OA.id)
			oas = query.all()
			oa_list = []
			for oa in oas:
				oa_list.append(dict(oa_id=oa.id, oa_code=oa.getInternalCode()))
			return oa_list
		except:
			raise

	def getPOCodeByInvoice(self, enterprise_id=None, invoice_id=None):
		"""

		:param enterprise_id:
		:param invoice_id:
		:return:
		"""
		try:
			query = self.db_session.query(
				Invoice.id, PurchaseOrder.financial_year, PurchaseOrder.po_id, PurchaseOrder.po_no,
				PurchaseOrder.type, PurchaseOrder.sub_number).join(Invoice.job_po).filter(
				Invoice.enterprise_id == enterprise_id, Invoice.id == invoice_id).group_by(Invoice.id)
			pos = query.all()
			po_codes = []
			for po in pos:
				po_codes.append(dict(po_id=po.po_id, po_code=PurchaseOrder.generateInternalCode(
					financial_year=po.financial_year, po_id=po.po_id, po_no=po.po_no, sub_number=po.sub_number)))
			return po_codes
		except:
			raise


class StoresService:
	"""
	Service class that handles the backend business logic and other manipulations related to presentation for the various
	Purchase modules - PurchaseOrders, Indents, etc.
	"""

	def __init__(self):
		"""

		"""
		self.stores_dao = StoresDAO()

	@staticmethod
	def getEnterpriseIndentStatus(enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""

		query = """
		SELECT indent_status.indent_status, COUNT(1)
		FROM
			(SELECT CASE WHEN IFNULL(SUM(im.indent_qty), 0) > IFNULL(SUM(im.po_qty), 0) THEN '%s'
				ELSE CASE
					WHEN IFNULL(SUM(im.po_qty), 0) > IFNULL(SUM(im.rec_qty), 0) THEN '%s'
						ELSE '%s'
						END
					END AS indent_status
			FROM indents i
			LEFT JOIN (SELECT 
				inm.indent_no,
				inm.enterprise_id,
				inm.item_id,
				inm.make_id,
				IFNULL(SUM(inm.request_qty), 0) / IF(COUNT(purchase_details.pid) = 0
					OR COUNT(purchase_details.pid) IS NULL, 1, COUNT(purchase_details.pid)) AS indent_qty,
				IFNULL(SUM(purchase_details.po_qty), 0) AS po_qty,
				IFNULL(SUM(purchase_details.rec_qty), 0) AS rec_qty
			FROM
				(SELECT 
				enterprise_id, indent_no, item_id, make_id, request_qty
			FROM
				indent_material) AS inm
			LEFT JOIN (SELECT 
				pom.pid AS pid,
				po.indent_no,
				pom.item_id,
				pom.make_id,
				SUM(pom.pur_qty) / IF(COUNT(gm.grnNumber) = 0
					OR COUNT(gm.grnNumber) IS NULL, 1, COUNT(gm.grnNumber)) AS po_qty,
				IFNULL(SUM(gm.acc_qty), 0) AS rec_qty,
				pom.enterprise_id
			FROM
				purchase_order po
			JOIN (SELECT 
				enterprise_id, pid, item_id, make_id, pur_qty
			FROM
				purchase_order_material) pom ON pom.pid = po.id
			LEFT JOIN (SELECT grm.*
			FROM
				(SELECT 
				enterprise_id, po_no, grnNumber, item_id, make_id, acc_qty
			FROM grn_material) grm
				JOIN grn ON grn.grn_no = grm.grnNumber
				AND grn.enterprise_id = grm.enterprise_id
				AND grn.status > - 1) gm ON gm.enterprise_id = pom.enterprise_id
				AND gm.po_no = pom.pid
				AND gm.item_id = pom.item_id
				AND gm.make_id = pom.make_id
			WHERE po.status NOT IN (1 , 3)
			GROUP BY pom.pid , pom.item_id , pom.make_id , pom.enterprise_id) purchase_details ON purchase_details.indent_no = inm.indent_no
				AND purchase_details.enterprise_id = inm.enterprise_id
				AND purchase_details.item_id = inm.item_id
				AND purchase_details.make_id = inm.make_id
			GROUP BY inm.indent_no , inm.enterprise_id , inm.item_id , inm.make_id) AS im ON i.indent_no = im.indent_no
			AND i.enterprise_id = im.enterprise_id
			WHERE i.enterprise_id = %s GROUP BY i.indent_no) AS indent_status
		GROUP BY indent_status.indent_status;
		""" % (INDENT_PENDING_DUE_TO_PO, INDENT_PENDING_DUE_TO_MATERIAL, INDENT_COMPLETED, enterprise_id)

		cursor = executeQuery(query)
		completed, due_to_material, due_to_po = 0, 0, 0
		for record in cursor:
			if record[0] == "Completed":
				completed = record[1]
			elif record[0] == INDENT_PENDING_DUE_TO_MATERIAL:
				due_to_material = record[1]
			elif record[0] == INDENT_PENDING_DUE_TO_PO:
				due_to_po = record[1]
		indent_status = dict(
			indent_raised=completed + due_to_po + due_to_material, indent_closed=completed,
			indent_pending=due_to_po + due_to_material, indent_due_po=due_to_po, indent_due_material=due_to_material)

		return indent_status

	@staticmethod
	def getDCLinkedInvoiceCode(grn_no=None, enterprise_id=None, party_id=None, received_against=None):
		"""
			Gets the invoice number associated with the selected dc in grn

			:param grn_no:
			:param enterprise_id:
			:param party_id:
			:param received_against:
			:return: JSON dumps of the POs queried
		"""

		try:
			query = """
				SELECT IF(r.status != 0, 
					(CONCAT(r.financial_year, '/GRN/', LPAD(r.receipt_no, 6, 0), IFNULL(r.sub_number,''))), 
					(CONCAT('TMP#GRN-',r.grn_no))) 
				FROM grn AS r 
				LEFT JOIN grn_material as a ON r.grn_no = a.grnNumber 
				WHERE r.party_id = {party_id} AND r.rec_against = '{received_against}' AND r.status > -1 
				AND r.enterprise_id = {enterprise_id} AND a.rec_grn_id={grn_no}""".format(
				received_against=received_against, party_id=party_id, enterprise_id=enterprise_id, grn_no=grn_no)

			invoices = executeQuery(query)
		except Exception as e:
			logger.exception(e.message)
			invoices = []
		return invoices

	@staticmethod
	def getIndentsWithStatus(
			since=None, till=None, project_code=None, enterprise_id=None, status=(
					INDENT_COMPLETED, INDENT_PENDING_DUE_TO_PO, INDENT_PENDING_DUE_TO_MATERIAL)):
		"""

		:param since:
		:param till:
		:param project_code:
		:param enterprise_id:
		:param status:
		:return:
		"""
		filters = ""
		if since or till:
			filters = " AND DATE(i.raised_date) BETWEEN DATE('%s') AND DATE('%s')" % (since, till)
		if project_code:
			filters = "%s AND project_code = '%s'" % (filters, project_code)
		required_statuses_as_string = "'%s'" % "', '".join(status)
		query = """
		SELECT 
			i.indent_no,
			CONCAT(projects.name,' (', projects.code ,')') as projects,
			i.raised_date,
			al.name,
			i.purpose,
			IFNULL(GROUP_CONCAT(DISTINCT tag
					SEPARATOR ', '),
					'') AS tag,
			IFNULL(SUM(im.indent_value), 0) AS indent_value,
			CASE
				WHEN IFNULL(SUM(im.indent_qty), 0) > IFNULL(SUM(im.po_qty), 0) THEN '%s'
				ELSE CASE
					WHEN IFNULL(SUM(im.po_qty), 0) > IFNULL(SUM(im.rec_qty), 0) THEN '%s'
					ELSE '%s'
				END
			END AS indent_status,
			IFNULL(SUM(im.po_basic_value), 0) AS basic_po_value,
			IFNULL(SUM(im.po_value), 0) AS po_value,
			IFNULL(SUM(im.indent_qty), 0),
			IFNULL(SUM(im.po_qty), 0),
			IFNULL(SUM(im.rec_qty), 0),
			i.enterprise_id,
			i.indent_id, i.sub_number,
			i.financial_year
		FROM indents i
			LEFT JOIN
				projects ON i.project_code = projects.id
				AND i.enterprise_id = projects.enterprise_id
				LEFT JOIN
			account_ledgers AS al ON i.purchase_account_id = al.id
				AND i.enterprise_id = al.enterprise_id
				LEFT JOIN
			indent_tags AS i_tags ON i.indent_no = i_tags.indent_id
				AND i.enterprise_id = i_tags.enterprise_id
				LEFT JOIN
			tags ON tags.id = i_tags.tag_id
				AND tags.enterprise_id = i_tags.enterprise_id
				LEFT JOIN
			(SELECT 
				inm.indent_no,
				inm.enterprise_id,
				inm.item_id,
				inm.make_id,
				IFNULL(SUM(inm.indent_value), 0) / IF(COUNT(purchase_details.pid) = 0
					OR COUNT(purchase_details.pid) IS NULL, 1, COUNT(purchase_details.pid)) AS indent_value, 
				IFNULL(SUM(purchase_details.po_basic_value), 0) AS po_basic_value,
				IFNULL(SUM(purchase_details.po_value), 0) AS po_value,
				IFNULL(SUM(inm.request_qty), 0) / IF(COUNT(purchase_details.pid) = 0
					OR COUNT(purchase_details.pid) IS NULL, 1, COUNT(purchase_details.pid)) AS indent_qty,
				IFNULL(SUM(purchase_details.po_qty), 0) AS po_qty,
				IFNULL(SUM(purchase_details.rec_qty), 0) AS rec_qty
			FROM
				(SELECT 
				im.enterprise_id,
				indent_no,
				im.item_id,
				im.make_id,
				request_qty,
				(request_qty * m.price) AS indent_value
			FROM
				indent_material im
			JOIN materials m ON m.enterprise_id = im.enterprise_id
				AND m.id = im.item_id) AS inm
			LEFT JOIN (SELECT 
				pom.pid AS pid,
				po.indent_no,
				pom.item_id,
				pom.make_id,
				SUM(pom.pur_qty * price) / IF(COUNT(gm.grnNumber) = 0
				OR COUNT(gm.grnNumber) IS NULL, 1, COUNT(gm.grnNumber)) AS po_basic_value,
				po.total AS po_value,
				SUM(pom.pur_qty) / IF(COUNT(gm.grnNumber) = 0
				OR COUNT(gm.grnNumber) IS NULL, 1, COUNT(gm.grnNumber)) AS po_qty,
				IFNULL(SUM(gm.acc_qty), 0) AS rec_qty,
				pom.enterprise_id
			FROM
				purchase_order po
			JOIN (SELECT 
				enterprise_id,
				pid,
				item_id,
				make_id,
				pur_qty,
				po_price AS price
			FROM
				purchase_order_material) pom ON pom.pid = po.id
			LEFT JOIN (SELECT 
				grm.*
			FROM
				(SELECT 
				enterprise_id,
				po_no,
				grnNumber,
				item_id,
				make_id,
				acc_qty
			FROM
				grn_material) grm
			JOIN grn ON grn.grn_no = grm.grnNumber
				AND grn.enterprise_id = grm.enterprise_id
				AND grn.status > - 1) gm ON gm.enterprise_id = pom.enterprise_id
				AND gm.po_no = pom.pid
				AND gm.item_id = pom.item_id
				AND gm.make_id = pom.make_id
			WHERE
				po.status NOT IN (1 , 3)
			GROUP BY pom.pid , pom.item_id , pom.make_id , pom.enterprise_id) 
			purchase_details ON purchase_details.indent_no = inm.indent_no
			AND purchase_details.enterprise_id = inm.enterprise_id
			AND purchase_details.item_id = inm.item_id
			AND purchase_details.make_id = inm.make_id
			GROUP BY inm.indent_no , inm.enterprise_id , inm.item_id , inm.make_id) AS im ON i.indent_no = im.indent_no
				AND i.enterprise_id = im.enterprise_id
		WHERE
			i.indent_module_id = 0 AND
			i.enterprise_id = %s %s
		GROUP BY i.indent_no HAVING indent_status in (%s); """ % (
			INDENT_PENDING_DUE_TO_PO, INDENT_PENDING_DUE_TO_MATERIAL, INDENT_COMPLETED, enterprise_id, filters,
			required_statuses_as_string)

		cursor = executeQuery(query)
		indents = []
		for record in cursor:
			indent = {
				"code": Indent.generateCode(indent_id=record[14], sub_number=record[15], financial_year=record[16]),
				"project": record[1],
				"raised_date": record[2],
				"type": record[3],
				"purpose": record[4],
				"tags": record[5],
				"indent_value": "%.2f" % round(float(record[6]), 2),
				"status": record[7],
				"basic_po_value": "%.2f" % round(float(record[8]), 2),
				"po_value": record[9],
				"indent_no": record[0]
			}
			indents.append(indent)
		indents = sorted(indents, key=lambda x: x['indent_no'])
		return indents

	def constructIndentVO(self, indent=Indent()):
		"""
		Converts the DB persistent entity into a Value Object presentable to the UI layer.

		:param indent:
		:return:
		"""
		logger.debug("Indent Materials - %s" % len(indent.materials))
		return IndentVO(
			self._generateIndentForm(indent, prefix=INDENT_FORM_PREFIX),
			self._generateIndentMaterialFormset(indent.materials),
			generateTagFormset(tags=indent.tags, prefix=IND_TAG_PREFIX))

	def saveIndent(self, indent_vo, user_id=None, enterprise_id=None):
		"""

		:param indent_vo:
		:param user_id:
		:param enterprise_id:
		:return:
		"""
		self.stores_dao.db_session.begin(subtransactions=True)
		try:
			if indent_vo.is_valid():
				entity = self.stores_dao.getIndent(
					indent_no=indent_vo.indent_form.cleaned_data['indent_no'], enterprise_id=enterprise_id)
				user = UserDAO().getUserById(enterprise_id=enterprise_id, user_id=user_id)
				is_new = False
				if not entity:
					entity = Indent()
					is_new = True
				if entity.remarks == '':
					logger.info("Indent code %s" % len(entity.remarks))
				indent_to_be_saved = self._copyIndentVOtoEntity(indent_vo=indent_vo, entity=entity)
				if isinstance(entity.remarks, list):
					entity.updateRemarks(remarks=indent_vo.indent_form.cleaned_data['instructions'], user=user)
				elif indent_vo.indent_form.cleaned_data['instructions'] != '':
					entity.remarks = ([dict(
						date=datetime.today().strftime("%Y-%m-%d %H:%M:%S"),
						remarks=str(indent_vo.indent_form.cleaned_data['instructions']).replace('\n', '<BR/>'),
						by="%s" % user)])
				indent_to_be_saved.modified_by = user_id
				self.stores_dao.saveIndent(indent_to_be_saved)
				logger.info("Indent code %s" % len(entity.materials))
				self.stores_dao.db_session.commit()
				if is_new is True:
					self.notifySaveIntentCount(
						enterprise_id=indent_to_be_saved.enterprise_id, sender_id=user_id, entity=entity,
						code=indent_to_be_saved.getCode())
				return self.constructIndentVO(indent_to_be_saved)
			else:
				self.stores_dao.db_session.rollback()
				logger.info('Indent VO validation failed: %s %s' % (
					indent_vo.indent_form.errors, indent_vo.indent_material_formset.errors))
		except Exception as e:
			self.stores_dao.db_session.rollback()
			logger.exception("Saving Indent failed %s" % e.message)
		return indent_vo

	def deleteIndent(self, indent_no=None, indent=None, enterprise_id=None):
		"""

		:param indent_no:
		:param indent:
		:param enterprise_id:
		:return:
		"""
		try:
			self.stores_dao.db_session.begin(subtransactions=True)
			self.stores_dao.db_session.delete(
				indent if indent_no is None else self.stores_dao.getIndent(indent_no, enterprise_id))
			self.stores_dao.db_session.commit()
		except:
			self.stores_dao.db_session.rollback()
			raise

	def _copyIndentVOtoEntity(self, indent_vo=None, entity=None):
		"""
		Method converts VO into persist-able data entity.
		Caution: Don't use this method unless the data is to be persisted immediately, as it might lead to inconsistent
			indent numbers

		:param indent_vo:
		:param entity:
		:return:
		"""
		entity = self.__copyIndentFormToEntity(indent_vo.indent_form, entity)

		entity.materials = self.__extractIndentMaterialsFromFormset(
			indent_vo.indent_material_formset, entity.indent_no)
		entity.tags = extractEntityTagMapsFromFormset(
			enterprise_id=entity.enterprise_id, map_class=IndentTag, tag_formset=indent_vo.indent_tag_formset,
			db_session=self.stores_dao.db_session)
		return entity

	@staticmethod
	def _generateIndentForm(indent=Indent(), prefix=INDENT_FORM_PREFIX):
		"""

		:param indent:
		:param prefix:
		:return:
		"""
		if indent.indent_no is None:
			date_today = datetime.now()
			indent.financial_year = getFinancialYear(
				for_date=date_today, fy_start_day=indent.enterprise.fy_start_day)
		request_qty = Decimal(sum(material.quantity for material in indent.materials))
		# Consider only stock-able materials as Indent is associated only with Materials that are stocked.
		purchase_qty = Decimal(sum(po_material.quantity for po in indent.purchase_orders for po_material in po.items))
		received_qty = Decimal(
			sum(material_received.accepted_qty for po in indent.purchase_orders for material_received in
			    po.materials_received))

		initializer = constructFormInitializer(indent)
		# append with empty string to avoid natural fetch of sp_instructions
		initializer['instructions'] = ''
		initializer['indent_code'] = indent.getCode()
		initializer['request_qty'] = request_qty
		initializer['balance_qty'] = request_qty - received_qty
		initializer['purchase_qty'] = purchase_qty
		initializer['received_qty'] = received_qty
		initializer['project_name'] = indent.project.name if indent.project is not None else ''
		initializer['raised_date'] = indent.raised_date
		initializer['expected_date'] = indent.expected_date
		initializer['modifier_name'] = "%s" % indent.modifier
		return IndentForm(initial=initializer, prefix=prefix, enterprise_id=indent.enterprise_id)

	def _generateIndentMaterialFormset(self, ind_materials=()):
		"""

		:param ind_materials:
		:return:
		"""
		initializer = []
		for ind_material in ind_materials:
			make_label = constructDifferentMakeName(ind_material.material.makes_json)
			form_initializer = constructFormInitializer(ind_material)
			description = ind_material.material.name
			if ind_material.material.drawing_no:
				description += "- %s" % ind_material.material.drawing_no
			form_initializer['description'] = "%s%s" % (description, " [%s]" % make_label if make_label else "")
			form_initializer['drawing_no'] = ind_material.material.drawing_no
			form_initializer['is_service'] = 1 if ind_material.material.is_service else 0
			form_initializer['units'] = ind_material.material.unit.unit_name
			form_initializer['make_label'] = make_label
			po_qty_details = ind_material.indent.getPurchaseQuantityMap()
			po_qty = 0
			for item_key in po_qty_details:
				if item_key == (ind_material.item_id, ind_material.make_id):
					po_qty = po_qty_details[item_key]
			form_initializer['po_qty'] = po_qty
			form_initializer['scale_factor'] = 1
			if ind_material.alternate_unit_id:
				scale_factor = self.stores_dao.getScaleFactor(
						enterprise_id=ind_material.enterprise_id, item_id=ind_material.item_id, alternate_unit_id=ind_material.alternate_unit_id)
				form_initializer['quantity'] = Decimal(ind_material.quantity) / Decimal(scale_factor)
				form_initializer['units'] = ind_material.alternate_unit.unit.unit_name
				form_initializer['scale_factor'] = scale_factor if scale_factor else 1
			initializer.append(form_initializer)
		return IndentMaterialFormset(initial=initializer, prefix=IND_MAT_PREFIX)

	@staticmethod
	def __copyIndentFormToEntity(indent_form, entity):
		"""

		:param indent_form:
		:param entity:
		:return:
		"""
		if entity.indent_no is None or entity.indent_no == '':
			logger.info('Raised by: %s' % entity.raised_by)
			entity = copyFormToEntity(indent_form, entity)
		else:
			logger.info('Indent No: %s' % entity.indent_no)
			entity.indent_no = indent_form.cleaned_data['indent_no']
			entity.enterprise_id = indent_form.cleaned_data['enterprise_id']
			entity.financial_year = indent_form.cleaned_data['financial_year']
			entity.purchase_account_id = indent_form.cleaned_data['purchase_account_id']
			entity.indent_id = indent_form.cleaned_data['indent_id']
			entity.project_code = indent_form.cleaned_data['project_code']
			entity.purpose = indent_form.cleaned_data['purpose']
			entity.expected_date = indent_form.cleaned_data['expected_date']
			entity.modified_date = time.strftime('%Y-%m-%d %H:%M:%S')
			entity.is_stockable = indent_form.cleaned_data['is_stockable']
		return entity

	def __extractIndentMaterialsFromFormset(self, indent_materials_formset=None, indent_no=None):
		"""

		:param indent_materials_formset:
		:param indent_no:
		:return:
		"""
		indent_materials = []
		for indent_material_form in indent_materials_formset:
			if indent_material_form.is_valid() and not indent_material_form.cleaned_data['DELETE']:
				item_id = indent_material_form.cleaned_data['item_id']
				enterprise_id = indent_material_form.cleaned_data['enterprise_id']
				make_id = indent_material_form.cleaned_data['make_id']
				alternate_unit_id = indent_material_form.cleaned_data['alternate_unit_id']
				indent_material = self.stores_dao.getIndentMaterial(
					indent_no=indent_no, item_id=item_id, enterprise_id=enterprise_id, make_id=make_id)

				if not indent_material:
					indent_material = IndentMaterial(
						indent_no=indent_no, item_id=item_id, enterprise_id=enterprise_id, make_id=make_id)
				if alternate_unit_id:
					scale_factor = self.stores_dao.getScaleFactor(
						enterprise_id=enterprise_id, item_id=item_id, alternate_unit_id=alternate_unit_id)
					indent_material.quantity = Decimal(indent_material_form.cleaned_data['quantity']) * Decimal(scale_factor)
					indent_material.alternate_unit_id = alternate_unit_id
				else:
					indent_material.quantity = indent_material_form.cleaned_data['quantity']
				indent_materials.append(indent_material)
				logger.debug('Indent Material constructed: %s' % indent_material)
		return indent_materials

	def getSuppliersData(self, enterprise_id=None):
		suppliers = []
		try:
			sl = self.stores_dao.getPartyDetails(enterprise_id=enterprise_id)
			for l in sl:
				suppliers.append({'id': l.id, 'name': l.name, 'code': l.code})
			logger.info("Fetched %s suppliers names" % len(suppliers))
		except:
			raise
		return suppliers

	def getMaterialData(self, enterprise_id=None):
		materials = []
		try:
			ml = self.stores_dao.getMaterialDetails(enterprise_id=enterprise_id)
			for l in ml:
				materials.append({'drawing_no': l.drawing_no, 'name': l.name, 'item_id': l.material_id})
			logger.info("Fetched %s material names" % len(materials))
		except:
			raise
		return materials

	@staticmethod
	def getStockQuery(
			enterprise_id=None, from_date=None, to_date=None, material=None, material_list=None, stock_type=None,
			location_id=None):
		"""

		:param enterprise_id:
		:param from_date:
		:param to_date:
		:param material:
		:param material_list:
		:param stock_type:
		:param location_id:
		:return:
		"""
		if material:
			where = "AND MATERIALS_MAKE.item_id='%s' and " \
			        "MATERIALS_MAKE.is_faulty=%s" % (material['item_id'], material['is_faulty'])
		else:
			if material_list:
				item_ids = []
				for item in material_list:
					item_ids.append(ast.literal_eval(item)['item_id'])
				where = "AND MATERIALS_MAKE.item_id in {item_ids}".format(
					item_ids='(%s)' % ', '.join(map(repr, tuple(item_ids))))
			else:
				where = ""
		stock_type_condition = ""
		if stock_type:
			stock_type_condition = "AND MATERIALS_MAKE.is_stocked in {stock_type}".format(
				stock_type='(%s)' % stock_type)

		location_condition_receipt = ""
		location_condition_issue = ""
		st_location_condition_receipt = ""
		st_location_condition_issue = ""
		mrp_location_condition = ""
		if location_id:
			location_condition_receipt = " AND grnm.location_id in {location_id}".format(
				location_id='(%s)' % location_id)
			location_condition_issue = " AND invm.location_id in {location_id}".format(
				location_id='(%s)' % location_id)
			st_location_condition_receipt = " AND st.to_location in {location_id}".format(
				location_id='(%s)' % location_id)
			st_location_condition_issue = " AND st.from_location in {location_id}".format(
				location_id='(%s)' % location_id)
			mrp_location_condition = " AND mm.location_id in {location_id}".format(
				location_id='(%s)' % location_id)

		query = """SELECT MATERIALS_MAKE.item_id, MATERIALS_MAKE.material_name, MATERIALS_MAKE.price,
				(IFNULL(OPENING_RECEIVED.received_quantity_before_given_date,0) + IFNULL(OPENING_TRANSFER_RECEIVED.received_transfer_quantity_before_given_date,0)) 
				- (IFNULL(OPENING_INVOICE.invoice_quantity_before_given_date,0) + IFNULL(OPENING_TRANSFER_ISSUED.issued_transfer_quantity_before_given_date,0)) AS Opening,
				(IFNULL(CLOSING_RECEIVED.received_quantity_after_given_date,0) + IFNULL(CLOSING_TRANSFER_RECEIVED.received_transfer_quantity_after_given_date,0) + IFNULL(issued_qty,0)) AS Received,
				(IFNULL(CLOSING_INVOICE.invoice_quantity_after_given_date,0) + IFNULL(CLOSING_TRANSFER_ISSUED.issued_transfer_quantity_after_given_date,0) + IFNULL(allocated_qty,0)) AS Issued,				
				(IFNULL(OPENING_RECEIVED.received_quantity_before_given_date,0) + IFNULL(OPENING_TRANSFER_RECEIVED.received_transfer_quantity_before_given_date,0)) 
                - (IFNULL(OPENING_INVOICE.invoice_quantity_before_given_date,0) + IFNULL(OPENING_TRANSFER_ISSUED.issued_transfer_quantity_before_given_date,0)) 
                + (IFNULL(CLOSING_RECEIVED.received_quantity_after_given_date,0) + IFNULL(CLOSING_TRANSFER_RECEIVED.received_transfer_quantity_after_given_date,0) + IFNULL(issued_qty,0)) 
                - (IFNULL(CLOSING_INVOICE.invoice_quantity_after_given_date,0) + IFNULL(CLOSING_TRANSFER_ISSUED.issued_transfer_quantity_after_given_date,0) + IFNULL(allocated_qty,0)) AS Closing,	
				((IFNULL(OPENING_RECEIVED.received_quantity_before_given_date,0) + IFNULL(OPENING_TRANSFER_RECEIVED.received_transfer_quantity_before_given_date,0)) 
                - (IFNULL(OPENING_INVOICE.invoice_quantity_before_given_date,0) + IFNULL(OPENING_TRANSFER_ISSUED.issued_transfer_quantity_before_given_date,0)) 
                + (IFNULL(CLOSING_RECEIVED.received_quantity_after_given_date,0) + IFNULL(CLOSING_TRANSFER_RECEIVED.received_transfer_quantity_after_given_date,0) + IFNULL(issued_qty,0)) 
                - (IFNULL(CLOSING_INVOICE.invoice_quantity_after_given_date,0) + IFNULL(CLOSING_TRANSFER_ISSUED.issued_transfer_quantity_after_given_date,0) + IFNULL(allocated_qty,0))) * MATERIALS_MAKE.price AS ClosingValue,
				MATERIALS_MAKE.unit_name, MATERIALS_MAKE.category, MATERIALS_MAKE.is_faulty, 
				MATERIALS_MAKE.in_use, MATERIALS_MAKE.minimum_stock_level,
				MATERIALS_MAKE.material_drawing_no, 1 as make_id,MATERIALS_MAKE.is_faulty,
				MATERIALS_MAKE.category_name, IFNULL(MATERIALS_MAKE.location," ") as location,
				MATERIALS_MAKE.make_name,
				IFNULL(CLOSING_TRANSFER_IN_TRANSIT.in_transit_quantity_after_given_date, 0) as in_transit_qty,
				allocated_qty, issued_qty
			FROM
				(SELECT mat.enterprise_id AS enterprise_id, IFNULL(mat.drawing_no, "") AS material_drawing_no,
					CONCAT(mat.name,IF(FAULT_FLAGS.is_faulty = 1,' (FAULTY)','')) AS material_name,
					mat.price, um.unit_name as unit_name, mat.makes_json AS make_name,
					mat.category as category, mc.name AS category_name, FAULT_FLAGS.is_faulty, mat.in_use as in_use,
					mat.is_stocked as is_stocked , mat.minimum_stock_level, mat.id AS item_id, mat.is_service AS is_service,
					IFNULL(mat.location, " ") as location
				FROM materials mat
					JOIN material_category mc ON mat.category = mc.id AND mat.enterprise_id = mc.enterprise_id
					JOIN unit_master um ON mat.unit = um.unit_id AND mat.enterprise_id=um.enterprise_id					
					JOIN (SELECT 0 as is_faulty UNION SELECT 1 AS is_faulty) AS FAULT_FLAGS ON 1=1) AS MATERIALS_MAKE
				LEFT OUTER JOIN
				(SELECT grnm.enterprise_id AS enterprise_id, grnm.item_id AS item_id,
					SUM(IFNULL(grnm.acc_qty, 0)) AS received_quantity_before_given_date,
					grnm.is_faulty
				FROM grn_material grnm
					INNER JOIN grn gr ON gr.grn_no = grnm.grnNumber AND gr.status > 0
						AND gr.inward_date < '{from_date}' AND grnm.rec_grn_id is null
						{location_condition_receipt}
				GROUP BY grnm.enterprise_id, grnm.item_id, grnm.is_faulty) AS OPENING_RECEIVED
					ON OPENING_RECEIVED.item_id = MATERIALS_MAKE.item_id
					AND OPENING_RECEIVED.enterprise_id = MATERIALS_MAKE.enterprise_id					
					AND OPENING_RECEIVED.is_faulty= MATERIALS_MAKE.is_faulty
				LEFT JOIN
				(SELECT invm.enterprise_id AS enterprise_id, invm.item_id AS item_id,
					IFNULL(SUM(invm.qty), 0) AS invoice_quantity_before_given_date,
					invm.is_faulty
				FROM invoice_materials AS invm
					INNER JOIN invoice AS inv ON invm.invoice_id = inv.id
				WHERE
					IFNULL(inv.issued_on, NOW()) < '{from_date}' AND inv.status > -1 AND invm.delivered_dc_id is null
					{location_condition_issue}
				GROUP BY invm.enterprise_id, invm.item_id, invm.is_faulty) AS OPENING_INVOICE
				ON MATERIALS_MAKE.item_id = OPENING_INVOICE.item_id
					AND MATERIALS_MAKE.enterprise_id = OPENING_INVOICE.enterprise_id					
					AND OPENING_INVOICE.is_faulty = MATERIALS_MAKE.is_faulty
				LEFT JOIN
				(SELECT grnm.enterprise_id AS enterprise_id, grnm.item_id AS item_id,
					IFNULL(SUM(grnm.acc_qty), 0) AS received_quantity_after_given_date,
					grnm.is_faulty
				FROM grn_material grnm
					INNER JOIN grn gr ON gr.grn_no = grnm.grnNumber AND gr.status > 0
						AND gr.inward_date BETWEEN '{from_date}' AND '{to_date}' AND grnm.rec_grn_id is null
						{location_condition_receipt}
				GROUP BY grnm.enterprise_id, grnm.item_id, grnm.is_faulty) AS CLOSING_RECEIVED
					ON MATERIALS_MAKE.item_id = CLOSING_RECEIVED.item_id
					AND MATERIALS_MAKE.enterprise_id = CLOSING_RECEIVED.enterprise_id					
					AND CLOSING_RECEIVED.is_faulty = MATERIALS_MAKE.is_faulty
				LEFT JOIN
				(SELECT invm.enterprise_id AS enterprise_id, invm.item_id AS item_id,
					IFNULL(SUM(invm.qty), 0) AS invoice_quantity_after_given_date,
					invm.is_faulty
				FROM invoice_materials AS invm
					INNER JOIN invoice AS inv ON invm.invoice_id = inv.id
				WHERE
					IFNULL(inv.issued_on, NOW()) BETWEEN '{from_date}' AND '{to_date}' AND inv.status > -1 AND invm.delivered_dc_id is null
					{location_condition_issue}
				GROUP BY invm.enterprise_id, invm.item_id, invm.is_faulty) AS CLOSING_INVOICE
					ON MATERIALS_MAKE.item_id = CLOSING_INVOICE.item_id
					AND MATERIALS_MAKE.enterprise_id = CLOSING_INVOICE.enterprise_id					
					AND CLOSING_INVOICE.is_faulty = MATERIALS_MAKE.is_faulty
				LEFT JOIN
				(SELECT 
				    st.enterprise_id AS enterprise_id,
				        strm.item_id AS item_id,
				        SUM(IFNULL(strm.quantity, 0)) AS received_transfer_quantity_before_given_date, strm.is_faulty as is_faulty
				FROM
				    transfer_details strm
				INNER JOIN stock_transfer st ON st.transfer_id = strm.transfer_id
				    AND st.status = 3
				    AND st.created_on < '{from_date}'
				    {st_location_condition_receipt}
				GROUP BY st.enterprise_id , strm.item_id, strm.is_faulty) AS OPENING_TRANSFER_RECEIVED ON OPENING_TRANSFER_RECEIVED.item_id = MATERIALS_MAKE.item_id
				    AND OPENING_TRANSFER_RECEIVED.enterprise_id = MATERIALS_MAKE.enterprise_id
				    AND OPENING_TRANSFER_RECEIVED.is_faulty= MATERIALS_MAKE.is_faulty
				    LEFT JOIN
				(SELECT 
				    st.enterprise_id AS enterprise_id,
				        stim.item_id AS item_id,
				        SUM(IFNULL(stim.quantity, 0)) AS issued_transfer_quantity_before_given_date, stim.is_faulty as is_faulty
				FROM
				    transfer_details stim
				INNER JOIN stock_transfer st ON st.transfer_id = stim.transfer_id
				    AND st.status > 1
				    AND st.status != 4
				    AND st.created_on < '{from_date}'
				    {st_location_condition_issue}
				GROUP BY st.enterprise_id , stim.item_id, stim.is_faulty) AS OPENING_TRANSFER_ISSUED ON OPENING_TRANSFER_ISSUED.item_id = MATERIALS_MAKE.item_id
				    AND OPENING_TRANSFER_ISSUED.enterprise_id = MATERIALS_MAKE.enterprise_id
				    AND OPENING_TRANSFER_ISSUED.is_faulty= MATERIALS_MAKE.is_faulty
				    LEFT JOIN
				(SELECT 
				    st.enterprise_id AS enterprise_id,
				        strm.item_id AS item_id,
				        SUM(IFNULL(strm.quantity, 0)) AS received_transfer_quantity_after_given_date, strm.is_faulty as is_faulty
				FROM
				    transfer_details strm
				INNER JOIN stock_transfer st ON st.transfer_id = strm.transfer_id
				    AND st.status = 3
				    AND st.created_on BETWEEN '{from_date}' AND '{to_date}'
				    {st_location_condition_receipt}
				GROUP BY st.enterprise_id , strm.item_id, strm.is_faulty) AS CLOSING_TRANSFER_RECEIVED ON CLOSING_TRANSFER_RECEIVED.item_id = MATERIALS_MAKE.item_id
				    AND CLOSING_TRANSFER_RECEIVED.enterprise_id = MATERIALS_MAKE.enterprise_id
				    AND CLOSING_TRANSFER_RECEIVED.is_faulty= MATERIALS_MAKE.is_faulty
				    LEFT JOIN
				(SELECT 
				    st.enterprise_id AS enterprise_id,
				        stim.item_id AS item_id,
				        SUM(IFNULL(stim.quantity, 0)) AS issued_transfer_quantity_after_given_date, stim.is_faulty as is_faulty
				FROM
				    transfer_details stim
				INNER JOIN stock_transfer st ON st.transfer_id = stim.transfer_id
				    AND st.status > 1
				    AND st.status != 4
				    AND st.created_on BETWEEN '{from_date}' AND '{to_date}'
				    {st_location_condition_issue}
				GROUP BY st.enterprise_id , stim.item_id, stim.is_faulty) AS CLOSING_TRANSFER_ISSUED ON CLOSING_TRANSFER_ISSUED.item_id = MATERIALS_MAKE.item_id
				    AND CLOSING_TRANSFER_ISSUED.enterprise_id = MATERIALS_MAKE.enterprise_id
				    AND CLOSING_TRANSFER_ISSUED.is_faulty= MATERIALS_MAKE.is_faulty
				    LEFT JOIN
				(SELECT 
				    st.enterprise_id AS enterprise_id,
				        strm.item_id AS item_id,
				        SUM(IFNULL(strm.quantity, 0)) AS in_transit_quantity_after_given_date, strm.is_faulty as is_faulty
				FROM
				    transfer_details strm
				INNER JOIN stock_transfer st ON st.transfer_id = strm.transfer_id
				    AND st.status = 2
				    AND st.created_on BETWEEN '{from_date}' AND '{to_date}'
				    {st_location_condition_receipt}
				GROUP BY st.enterprise_id , strm.item_id, strm.is_faulty) AS CLOSING_TRANSFER_IN_TRANSIT ON CLOSING_TRANSFER_IN_TRANSIT.item_id = MATERIALS_MAKE.item_id
				    AND CLOSING_TRANSFER_IN_TRANSIT.enterprise_id = MATERIALS_MAKE.enterprise_id
				    AND CLOSING_TRANSFER_IN_TRANSIT.is_faulty = MATERIALS_MAKE.is_faulty
				    LEFT JOIN
			    (SELECT 
			        mm.item_id AS item_id,
			            mm.enterprise_id AS enterprise_id,
			            SUM(IFNULL(mm.allocated_qty, 0)) AS allocated_qty,
			            SUM(IFNULL(mm.issued_qty, 0)) AS issued_qty,
			            0 as is_faulty
			    FROM
			        mrp_materials AS mm where mm.updated_on < '{to_date}' {mrp_location_condition} 
			    group by mm.item_id,mm.enterprise_id) AS mrp ON mrp.item_id = MATERIALS_MAKE.item_id
			        AND mrp.enterprise_id = MATERIALS_MAKE.enterprise_id AND mrp.is_faulty = MATERIALS_MAKE.is_faulty	
			WHERE  MATERIALS_MAKE.enterprise_id = '{enterprise_id}' AND MATERIALS_MAKE.in_use=1 {stock_type_condition} {where} AND MATERIALS_MAKE.is_service=0   
			having Opening != 0 OR Received !=0 OR Issued !=0
			ORDER BY MATERIALS_MAKE.category, MATERIALS_MAKE.material_name, MATERIALS_MAKE.is_faulty;""" .format (
			from_date=from_date, to_date=to_date, enterprise_id=enterprise_id, stock_type_condition=stock_type_condition,
			where=where, location_condition_receipt=location_condition_receipt, location_condition_issue=location_condition_issue,
			st_location_condition_receipt=st_location_condition_receipt, st_location_condition_issue=st_location_condition_issue,
			mrp_location_condition=mrp_location_condition)
		return query

	@staticmethod
	def current_stock_query(enterprise_id=None, location_id=None, material=None, exact_issued_on_date=None):
		"""
		The function is used to get the latest stock info based on the GRN, Invoice, stock transfer entries
		"""
		stock_transfer_condition_from = ""
		stock_transfer_condition_to = ""
		location_condition = ""
		if location_id:
			stock_transfer_condition_from = "AND st.created_on < '%s' AND st.from_location IN (%s)" % (exact_issued_on_date,
																								   location_id)
			stock_transfer_condition_to = "AND st.created_on < '%s' AND st.to_location IN (%s)" % (exact_issued_on_date,
																								   location_id)
			location_condition = "AND c.location_id = %s" % location_id
		condition = ""
		str_condition = ""
		sti_condition = ""
		if material["is_faulty"] != "":
			condition += "AND c.is_faulty = %s" % material["is_faulty"]
			str_condition += " AND strm.is_faulty = %s" % material["is_faulty"]
			sti_condition += " AND stim.is_faulty = %s" % material["is_faulty"]

		grn_condition = "AND b.inward_date <= '%s'" % exact_issued_on_date
		invoice_condition = ""
		if material["invoice_id"] != "":
			invoice_condition += "AND b.id <> %s" % material["invoice_id"]
		invoice_condition += " AND ( b.issued_on <= '%s' OR b.status = 0)" % exact_issued_on_date

		query ="""select _a.id,
						 (IFNULL(_a.grn_quantity, 0) + IFNULL(_a.st_received_qty, 0) + IFNULL(_a.mrp_issued_qty, 0) ) - 
						 (IFNULL(_a.invoice_qty, 0) + IFNULL(_a.st_issued_qty, 0) + IFNULL(_a.mrp_allocated_qty, 0)) AS Closing, 
						 _a.minimum_stock_level
						 from(SELECT
							a.id,
							
							(
								SELECT IFNULL(SUM(c.acc_qty), 0)
								FROM grn b
								JOIN grn_material c ON b.grn_no = c.grnNumber
								WHERE b.enterprise_id = a.enterprise_id
									AND c.item_id = a.id
									AND b.status > 0
									{condition} 
									{grn_condition}
									{location_condition}
									AND c.rec_grn_id IS NULL
							) AS grn_quantity,
							(
								SELECT IFNULL(SUM(c.qty), 0)
								FROM invoice b
								JOIN invoice_materials c ON b.id = c.invoice_id
								WHERE b.enterprise_id = a.enterprise_id
									AND c.item_id = a.id
									AND b.status > -1
									AND c.delivered_dc_id IS NULL
									{condition} 
									{invoice_condition}
									{location_condition}
							) AS invoice_qty,
							(
								SELECT IFNULL(SUM(strm.quantity), 0)
								FROM transfer_details strm
								JOIN stock_transfer st ON st.transfer_id = strm.transfer_id
								WHERE st.enterprise_id = a.enterprise_id
									AND strm.item_id = a.id
									AND st.status = 3
									{str_condition}
									{stock_transfer_condition_to}
								GROUP BY st.enterprise_id, strm.item_id
							) AS st_received_qty,
							(
								SELECT IFNULL(SUM(stim.quantity), 0)
								FROM transfer_details stim
								JOIN stock_transfer st ON st.transfer_id = stim.transfer_id
								WHERE st.enterprise_id = a.enterprise_id
									AND stim.item_id = a.id
									AND st.status > 1
									AND st.status != 4
									{sti_condition}
									{stock_transfer_condition_from}
								GROUP BY st.enterprise_id, stim.item_id
							) AS st_issued_qty,
							a.minimum_stock_level,
						(SELECT 				
							SUM(IFNULL(c.allocated_qty, 0)) AS allocated_qty
						FROM
							mrp_materials AS c
						WHERE
							c.updated_on < '{exact_issued_on_date}' 
							AND c.item_id = a.id
							AND c.enterprise_id = a.enterprise_id
							{location_condition}
						GROUP BY c.item_id , c.enterprise_id) AS mrp_allocated_qty,
						(SELECT 									
							SUM(IFNULL(c.issued_qty, 0)) AS issued_qty
						FROM
							mrp_materials AS c
						WHERE
							c.updated_on < '{exact_issued_on_date}' 
							AND c.item_id = a.id
							AND c.enterprise_id = a.enterprise_id
							{location_condition}
						GROUP BY c.item_id , c.enterprise_id) AS mrp_issued_qty
						FROM
							materials AS a
						WHERE
							a.is_stocked = 1
							AND a.in_use = 1
							AND a.id = '{item_id}'
							AND a.enterprise_id = '{enterprise_id}') as _a;

		""".format(
			condition=condition, invoice_condition=invoice_condition, grn_condition=grn_condition,
			item_id=material["item_id"], enterprise_id=enterprise_id, location_condition=location_condition,
			stock_transfer_condition_from=stock_transfer_condition_from, stock_transfer_condition_to=stock_transfer_condition_to,
			str_condition=str_condition, sti_condition=sti_condition, exact_issued_on_date=exact_issued_on_date)
		return query

	def build_stock_query(self, enterprise_id=None, location_id=None, mat_list=None, is_faulty=None):
		"""
		The function is used to get the latest stock info based on the GRN, Invoice, stock transfer entries
		"""
		location_condition = ""
		if location_id:
			location_condition = " AND cs.location_id = %s " % location_id
		query = """
			SELECT cs.item_id AS item_id, sum(IFNULL(cs.qty,0)) AS closing_qty, cs.is_faulty AS is_faulty,
				mat.minimum_stock_level as msl_qty
				FROM 
			closing_stock AS cs
			JOIN materials as mat ON mat.enterprise_id = cs.enterprise_id and mat.id=cs.item_id
			JOIN location_master as lm ON lm.id = cs.location_id and lm.enterprise_id = cs.enterprise_id
			WHERE 
			cs.enterprise_id = {enterprise_id} AND cs.item_id in {mat_list} {location_condition} 
			AND cs.is_faulty = {is_faulty} group by cs.item_id, cs.is_faulty
		""".format(
			mat_list='(%s)' % ', '.join(map(repr, tuple(mat_list))), enterprise_id=enterprise_id,
			location_condition=location_condition, is_faulty=is_faulty)
		logger.info("Stock Query:%s" % query)
		return query

	def getClosingStock(self, mat_list=None, enterprise_id=None, location_id=None, is_faulty=None):
		closing_stock = []
		try:
			closing_stock = executeQuery(
				self.build_stock_query(enterprise_id=enterprise_id, location_id=location_id, mat_list=mat_list,
				                       is_faulty=is_faulty), as_dict=True)
		except Exception as e:
			logger.exception(e)
		return closing_stock

	@staticmethod
	def construct_location_wise_item_stock_query(enterprise_id=None, material=None, exact_issued_on_date=None):
		"""
		The Function is used to get the item closing stock for all the locations till now
		"""
		condition = ""
		item_id_condition = ""

		if material["item_id"] != "":
			item_id_condition = "AND m.id in (%s)" % ','.join(map(str, material["item_id"]))
		if not exact_issued_on_date:
			exact_issued_on_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

		if material["is_faulty"] != "":
			condition += "AND cs.is_faulty = %s" % material["is_faulty"]

		query = """SELECT 
						m.id,m.name,m.drawing_no,m.price,um.unit_name,mc.name as category,
						lm.name AS location_name,
						cs.location_id AS location_id,						
						cs.qty AS Closing,
						m.minimum_stock_level
					FROM 
						materials m
					JOIN unit_master as um ON
						um.unit_id = m.unit AND m.enterprise_id=um.enterprise_id
					JOIN material_category as mc ON
						mc.id = m.category  AND m.enterprise_id=mc.enterprise_id
					JOIN closing_stock as cs ON m.id = cs.item_id and m.enterprise_id = cs.enterprise_id
					JOIN location_master as lm ON lm.id = cs.location_id
				    AND lm.enterprise_id = cs.enterprise_id 
					where m.enterprise_id = {enterprise_id} {condition}
					{item_id_condition}	 
						""".format(
			condition=condition,  item_id_condition=item_id_condition, enterprise_id=enterprise_id, exact_issued_on_date=exact_issued_on_date)
		return query

	# @staticmethod
	# def construct_location_wise_item_stock_query(enterprise_id=None, material=None, exact_issued_on_date=None):
	# 	"""
	# 	The Function is used to get the item closing stock for all the locations till now
	# 	"""
	# 	condition = ""
	# 	str_condition = ""
	# 	sti_condition = ""
	# 	item_id_condition = ""
	#
	# 	if material["item_id"] != "":
	# 		item_id_condition = "AND m.id in (%s)" % ','.join(map(str, material["item_id"]))
	# 	if not exact_issued_on_date:
	# 		exact_issued_on_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
	#
	# 	stock_transfer_till = "AND st.created_on < '%s'" % exact_issued_on_date
	# 	if material["is_faulty"] != "":
	# 		condition += "AND c.is_faulty = %s" % material["is_faulty"]
	# 		str_condition += " AND strm.is_faulty = %s" % material["is_faulty"]
	# 		sti_condition += " AND stim.is_faulty = %s" % material["is_faulty"]
	#
	# 	grn_condition = "AND b.inward_date <= '%s'" % exact_issued_on_date
	# 	invoice_condition = ""
	# 	if material["invoice_id"] != "":
	# 		invoice_condition += "AND b.id <> %s" % material["invoice_id"]
	# 	invoice_condition += "AND ( b.issued_on <= '%s' OR b.status = 0)" % exact_issued_on_date
	# 	query = """SELECT
	# 					m.id,m.name,m.drawing_no,m.price,um.unit_name,mc.name as category,
	# 					(select name from location_master where id = loc.location_id) as location_name,
	# 					loc.location_id AS location_id,
	# 					(IFNULL(grn.grn_quantity, 0) + IFNULL(str.st_received_qty, 0) + IFNULL(mrp.issued_qty, 0)) - (IFNULL(inv.invoice_qty, 0) + IFNULL(sti.st_issued_qty, 0)+ IFNULL(mrp.allocated_qty, 0)) AS Closing,
	# 					m.minimum_stock_level
	# 				FROM
	# 					materials m
	# 					JOIN unit_master as um ON
	# 					um.unit_id = m.unit AND m.enterprise_id=um.enterprise_id
	# 					JOIN material_category as mc ON
	# 					mc.id = m.category  AND m.enterprise_id=mc.enterprise_id
	# 				JOIN (
	# 					SELECT DISTINCT location_id FROM grn_material
	# 					UNION
	# 					SELECT DISTINCT location_id FROM invoice_materials
	# 					UNION
	# 					SELECT DISTINCT to_location AS location_id FROM stock_transfer
	# 					UNION
	# 					SELECT DISTINCT from_location AS location_id FROM stock_transfer
	# 				) loc ON 1=1
	# 				LEFT JOIN (
	# 					SELECT
	# 						c.item_id,
	# 						c.location_id,
	# 						SUM(c.acc_qty) AS grn_quantity
	# 					FROM
	# 						grn b
	# 					JOIN
	# 						grn_material c ON b.grn_no = c.grnNumber
	# 					WHERE
	# 						b.status > 0
	# 						{condition}
	# 						{grn_condition}
	# 						AND c.rec_grn_id IS NULL
	# 					GROUP BY
	# 						c.item_id, c.location_id
	# 				) AS grn ON m.id = grn.item_id AND loc.location_id = grn.location_id
	# 				LEFT JOIN (
	# 					SELECT
	# 						c.item_id,
	# 						c.location_id,
	# 						SUM(c.qty) AS invoice_qty
	# 					FROM
	# 						invoice b
	# 					JOIN
	# 						invoice_materials c ON b.id = c.invoice_id
	# 					WHERE
	# 						b.status > -1
	# 						AND c.delivered_dc_id IS NULL
	# 						{condition}
	# 						{invoice_condition}
	# 					GROUP BY
	# 						c.item_id, c.location_id
	# 				) AS inv ON m.id = inv.item_id AND loc.location_id = inv.location_id
	# 				LEFT JOIN (
	# 					SELECT
	# 						strm.item_id,
	# 						st.to_location AS location_id,
	# 						SUM(strm.quantity) AS st_received_qty
	# 					FROM
	# 						transfer_details strm
	# 					JOIN
	# 						stock_transfer st ON st.transfer_id = strm.transfer_id
	# 					WHERE
	# 						st.status = 3
	# 						{str_condition}
	# 						{stock_transfer_till}
	# 					GROUP BY
	# 						strm.item_id, st.to_location
	# 				) AS str ON m.id = str.item_id AND loc.location_id = str.location_id
	# 				LEFT JOIN (
	# 					SELECT
	# 						stim.item_id,
	# 						st.from_location AS location_id,
	# 						SUM(stim.quantity) AS st_issued_qty
	# 					FROM
	# 						transfer_details stim
	# 					JOIN
	# 						stock_transfer st ON st.transfer_id = stim.transfer_id
	# 					WHERE
	# 						st.status > 1
	# 						AND st.status != 4
	# 						{sti_condition}
	# 						{stock_transfer_till}
	# 					GROUP BY
	# 						stim.item_id, st.from_location
	# 				) AS sti ON m.id = sti.item_id AND loc.location_id = sti.location_id
	# 				LEFT JOIN
	# 				(SELECT mm.item_id AS item_id,
	# 					mm.enterprise_id AS enterprise_id,
	# 					SUM(IFNULL(mm.allocated_qty, 0)) AS allocated_qty,
	# 					SUM(IFNULL(mm.issued_qty, 0)) AS issued_qty,
	# 					0 AS is_faulty, location_id
	# 				FROM
	# 					mrp_materials AS mm
	# 				WHERE
	# 					mm.updated_on < '{exact_issued_on_date}'
	# 				GROUP BY mm.item_id , mm.enterprise_id,mm.location_id) AS mrp ON mrp.item_id = m.id
	# 					AND mrp.enterprise_id = m.enterprise_id
	# 					AND mrp.location_id = loc.location_id
	# 					AND mrp.is_faulty = 0
	# 				WHERE
	# 					m.is_stocked = 1
	# 					AND m.in_use = 1
	# 					AND m.enterprise_id = '{enterprise_id}'
	# 					{item_id_condition}
	# 				GROUP BY
	# 					m.id, loc.location_id, m.minimum_stock_level having loc.location_id is not null;
	# 					""".format(
	# 		condition=condition, invoice_condition=invoice_condition, grn_condition=grn_condition,
	# 		item_id_condition=item_id_condition, enterprise_id=enterprise_id, str_condition=str_condition,
	# 		sti_condition=sti_condition, stock_transfer_till=stock_transfer_till, exact_issued_on_date=exact_issued_on_date)
	# 	return query

	@staticmethod
	def notifySaveIntentCount(enterprise_id=None, sender_id=None, include_sender=True, entity=None, code=None,
	                          type=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param include_sender:
		:param entity:
		:return:
		"""
		try:
			message = PUSH_NOTIFICATION["indent"] % (
				entity.getCode(), len(entity.materials))
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="STORES",
				message=message, collapse_key="", include_sender=include_sender, code=code)
		except Exception as e:
			logger.error("Failed notification.. %s " % e.message)

	def notifyReceiptCount(
			self, enterprise_id=None, sender_id=None, include_sender=True, received_against=None, code=None, type=None):
		"""
		"""
		try:
			count = self.stores_dao.notificationReceiptCount(
				enterprise_id=enterprise_id, status=(Receipt.STATUS_DRAFT, Receipt.STATUS_GRN_RETURNED),
				received_against=received_against)
			document = "Receipt"
			module_code = "STORES"
			if received_against == "Issues":
				document = "Internal Receipt"
			elif received_against == "Sales Return":
				document = received_against
				module_code = "SALES"
			message = None
			if count == 1:
				message = "1 %s is pending for approval" % document
			elif count > 1:
				message = "%s %ss are pending for approval" % (count, document)
			collapse_key = "%s_save_count" % Receipt.TYPE_CODE[received_against].lower().replace('grn', 'receipt')
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code=module_code,
				message=message, collapse_key=collapse_key, include_sender=include_sender, code=code, type=type)
		except Exception as e:
			logger.error("Failed notification.. %s " % e.message)

	def notifyPendingReceiptNoteCount(self, enterprise_id=None, sender_id=None, code=None, type=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:return:
		"""
		try:
			count = self.stores_dao.getReceiptNoteCount(enterprise_id=enterprise_id, status=Receipt.STATUS_APPROVED)
			message = None
			if count == 1:
				message = "1 Note is pending for approval"
			elif count > 1:
				message = "%s Notes are pending for approval" % count

			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="ICD", message=message,
				collapse_key="icd_approved_count", include_sender=True, code=code, type=type)
		except Exception as e:
			logger.exception("ICD Pending notification failed %s" % e.message)

	def notifyReceiptApprovalCount(self, enterprise_id=None, sender_id=None, receipt=None, icd=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param receipt:
		:param icd:
		:return:
		"""
		try:
			if receipt.received_against == "Issues":
				customer = self.stores_dao.getIssuedToOfGrn(enterprise_id=enterprise_id, receipt_no=receipt.receipt_no)[0]
			else:
				customer = self.stores_dao.getCustomerDetails(
					enterprise_id=enterprise_id, supplier_id=receipt.supplier_id).name
			invoice_value = "%s %s " % (receipt.currency.code, float(receipt.invoice_value))
			message = PUSH_NOTIFICATION["receipt_approved"] % (receipt.getCode(), customer, invoice_value)
			module_code = "STORES"
			if receipt.received_against == "Sales Return":
				module_code = "SALES"
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code=module_code, message=message,
				code=receipt.getCode(), type=receipt.invoice_type)
			self.notifyReceiptCount(
				enterprise_id=enterprise_id, sender_id=sender_id, received_against=receipt.received_against,
				code=receipt.getCode(), type=receipt.invoice_type)
			if receipt.received_against in ('Purchase Order', 'Job Work', 'Delivery Challan', 'Sales Return') and icd:
				if receipt.received_against == 'Delivery Challan' or int(receipt.invoice_type) == 2:
					self.notifyPendingReceiptNoteCount(
						enterprise_id=enterprise_id, sender_id=sender_id, code=receipt.getCode(),
						type=receipt.invoice_type)
		except Exception as e:
			logger.error("ICD receipt note updated notification failed %s" % e.message)

	def autoGenerateNote(
			self, receipt=None, enterprise_id=None, user_id=None, icd_ignore_credit_note=None, fy_start_day=None,
			icd_request_acknowledgement=False):
		try:
			logger.info("Auto note generation is triggered... ")
			enterprise_id = enterprise_id
			self.stores_dao.db_session.begin(subtransactions=True)
			from erp.icd import backend
			icd_service = backend.ICDService()
			grn_materials = icd_service.getIcdMaterials(
				grn_no=str(receipt.receipt_no), received_against=receipt.received_against)
			receipt.status = Receipt.STATUS_ICD_CHECKED
			code = backend.NoteDAO().getNoteNo(enterprise_id=enterprise_id)
			note = CreditDebitNote(
				code=code, financial_year=getFinancialYear(fy_start_day=fy_start_day), party_id=receipt.supplier_id,
				created_on=datetime.now(), enterprise_id=enterprise_id, round_off=receipt.round_off)
			items = []
			non_stock_items = []
			for material in grn_materials:
				if receipt.received_against == 'Sales Return':
					if material['dc_qty'] > 0:  # return
						note_reason_id = self.stores_dao.db_session.query(NoteReason).filter(
							NoteReason.reason == 'RETURN').first().id
						is_credit = True
						quantity = material['dc_qty']
						rate = material['inv_rate']
						amount = quantity * rate
						if amount > 0:
							self.createNoteItem(
								note=note, enterprise_id=enterprise_id, items=items, non_stock_items=non_stock_items,
								quantity=quantity, rate=rate, is_credit=is_credit, amount=amount, material=material,
								note_reason_id=note_reason_id)

				balance_quantity = material['dc_qty'] - material['rec_qty']
				is_return_material = True if material['dc_id'] != 0 else False
				if balance_quantity > 0:  # shortage
					note_reason_id = self.stores_dao.db_session.query(NoteReason).filter(
						NoteReason.reason == 'SHORTAGE').first().id
					is_credit = False
					quantity = balance_quantity
					rate = material['store_price'] if material['dc_id'] != 0 and receipt.received_against != 'Sales Return' else material['inv_rate']
					amount = quantity * rate
					if amount > 0:
						self.createNoteItem(
							note=note, enterprise_id=enterprise_id, items=items, non_stock_items=non_stock_items,
							quantity=quantity, rate=rate, is_credit=is_credit, amount=amount, material=material,
							note_reason_id=note_reason_id)

				if material['inv_rate'] != material['po_price']:
					# Rate difference assessment
					if material['acc_qty'] > 0 and is_return_material is False:
						note_reason_id = self.stores_dao.db_session.query(NoteReason).filter(
							NoteReason.reason == 'Rate Difference').first().id
						is_credit = material['inv_rate'] < material['po_price']
						if not (is_credit and icd_ignore_credit_note):
							# Checking if Credit Note automation is configured for this Enterprise
							quantity = material['acc_qty']
							rate = material['inv_rate'] - (material['po_price'] if material['po_price'] else 0) if \
								material['inv_rate'] > material[
									'po_price'] else material['po_price'] - material['inv_rate']  # try abs(a-b)
							amount = quantity * rate
							if amount > 0:
								self.createNoteItem(
									note=note, enterprise_id=enterprise_id, items=items,
									non_stock_items=non_stock_items,
									quantity=quantity, rate=rate, is_credit=is_credit, amount=amount, material=material,
									note_reason_id=note_reason_id)

				if material['po_price'] > material['approved_rate'] and material['drawing_no']:  # PO Rate Diffrence
					if material['acc_qty'] > 0 and is_return_material is False:
						note_reason_id = self.stores_dao.db_session.query(NoteReason).filter(
							NoteReason.reason == 'PO Rate Difference').first().id
						is_credit = False
						quantity = material['acc_qty']
						rate = material['po_price'] - material['approved_rate']
						amount = quantity * rate
						if amount > 0:
							self.createNoteItem(
								note=note, enterprise_id=enterprise_id, items=items,
								non_stock_items=non_stock_items,
								quantity=quantity, rate=rate, is_credit=is_credit, amount=amount, material=material,
								note_reason_id=note_reason_id)

				if material['rec_qty'] - material['acc_qty']:  # REJECTED
					note_reason_id = self.stores_dao.db_session.query(NoteReason).filter(
						NoteReason.reason == 'REJECTED').first().id
					is_credit = False
					quantity = material['rec_qty'] - material['acc_qty']
					rate = material['store_price'] if material['dc_id'] != 0 and receipt.received_against != 'Sales Return' else material['inv_rate']
					amount = quantity * rate
					if amount > 0:
						self.createNoteItem(
							note=note, enterprise_id=enterprise_id, items=items, non_stock_items=non_stock_items,
							quantity=quantity, rate=rate, is_credit=is_credit, amount=amount, material=material,
							note_reason_id=note_reason_id)
			if len(items) > 0 or len(non_stock_items) > 0:
				if len(items) > 0:
					note.items = items
				if len(non_stock_items) > 0:
					note.non_stock_items = non_stock_items
				note_receipt_map = NoteReceiptMap(receipt_no=receipt.receipt_no, enterprise_id=enterprise_id)
				note_receipt_map.note = note
				note.project_code = receipt.project_code
				note.currency_id = receipt.currency_id
				note.inv_no = receipt.invoice_no
				note.inv_value = receipt.invoice_value
				note.inv_date = receipt.invoice_date
				note.created_on = datetime.now()
				note.raised_on = datetime.now()
				note.approved_on = datetime.now()
				note.approved_by = user_id
				note.last_modified_on = datetime.now()
				note.raised_by = user_id
				note.last_modified_by = user_id
				note.receipt_code = receipt.getCode()

				self.stores_dao.db_session.add(note)
				self.stores_dao.db_session.add(receipt)
				self.stores_dao.db_session.commit()
				self.setNoteValueAndStatus(note=note, icd_request_acknowledgement=icd_request_acknowledgement)
			else:
				self.stores_dao.db_session.rollback()
			if note.id is None or not icd_request_acknowledgement:
				icd_service.verifyNote(
					enterprise_id=enterprise_id, receipt_no=receipt.receipt_no, user_id=user_id)
		except Exception as e:
			self.stores_dao.db_session.rollback()
			logger.error("ICD auto generation of note is failed %s" % e.message)

	def setNoteValueAndStatus(self, note=None, icd_request_acknowledgement=False):
		"""

		:param note:
		:param icd_request_acknowledgement:
		:return:
		"""
		self.stores_dao.db_session.begin(subtransactions=True)
		try:
			total_tax = 0
			if note.note_receipt_map.receipt.received_against != 'Sales Return':
				for code, amount in note.note_receipt_map.receipt.getTaxValues(discounts_applied=False).items():
					total_tax += amount
			else:
				for code, amount in note.getTaxValues(discounts_applied=False).items():
					total_tax += amount
			is_credit = note.getNetCreditValue() > 0
			grand_total1 = note.getNetCreditValue() + total_tax
			note.value = abs(grand_total1)
			note.is_credit = is_credit

			if note.value > 0:
				note.status = Receipt.STATUS_PARTY_ACK_PENDING if icd_request_acknowledgement else Receipt.STATUS_ICD_VERIFIED
			else:
				note.status = Receipt.STATUS_ICD_VERIFIED
			self.stores_dao.db_session.commit()
		except Exception as e:
			self.stores_dao.db_session.rollback()
			logger.error("Set value and status for note is failed %s" % e.message)

	def createNoteItem(
			self, note=None, enterprise_id=None, items=None, non_stock_items=None, quantity=None, rate=None,
			is_credit=None, amount=None, material=None, note_reason_id=None):
		try:
			if material['item_id'] != '' and material['item_id'] is not None:
				item = NoteItem(
					item_id=material['item_id'], po_no=material['po_no'],
					reason_id=note_reason_id, dc_id=material['dc_id'], enterprise_id=enterprise_id,
					quantity=quantity, rate=rate, is_credit=is_credit, amount=amount, hsn_code=material['hsn_code'])
				item.make_id = material['make_id']  # TODO: Check this once again
				item.taxes, individual_item_taxes = self.createNoteItemTaxes(
					note_item=item, material=material, enterprise_id=enterprise_id,
					reason_id=note_reason_id)
				item.note = note
				items.append(item)
			else:
				item = NoteItemNonStock(
					description=material['item'], po_no=material['po_no'],
					reason_id=note_reason_id,
					enterprise_id=enterprise_id, dc_id=material['dc_id'], quantity=quantity, rate=rate,
					is_credit=is_credit,
					amount=amount, hsn_code=material['hsn_code'])

				item.taxes, individual_item_taxes = self.createNoteItemTaxes(
					note_item=item, material=material, enterprise_id=enterprise_id,
					reason_id=note_reason_id)
				item.note = note
				non_stock_items.append(item)
			logger.info("item added successfully")
		except Exception as e:
			logger.exception("Exception occurred while creating Note items %s " % e.message)

	@staticmethod
	def createNoteItemTaxes(note_item=None, enterprise_id=None, reason_id=None, material=None):
		try:
			logger.info("Creating item taxes")
			note_item_taxes = []
			item_taxes_dict = {}
			for tax_type in ['cgst_code', 'sgst_code', 'igst_code']:
				if material[tax_type] is not None:
					logger.info("Creating stocked item taxes...")
					if material['item_id'] != '' and material['item_id'] is not None:
						item_tax = NoteItemTax(
							item_id=material['item_id'], enterprise_id=enterprise_id,
							po_no=material['po_no'], reason_id=reason_id, tax_code=material[tax_type],
							make_id=material['make_id'], dc_id=material['dc_id'])
						item_tax.is_faulty = material['is_faulty']
						item_taxes_dict[tax_type.replace('code', 'rate')] = material[tax_type.replace('code', 'rate')]
						item_tax.note_item = note_item
						note_item_taxes.append(item_tax)
					else:
						logger.info("Creating non stock item taxes...")
						non_stock_item_tax = NoteItemNonStockTax(
							description=material['item'], enterprise_id=enterprise_id,
							po_no=material['po_no'], reason_id=reason_id, tax_code=material[tax_type])
						item_taxes_dict[tax_type.replace('code', 'rate')] = material[tax_type.replace('code', 'rate')]
						non_stock_item_tax.note_nonstock_item = note_item
						note_item_taxes.append(non_stock_item_tax)
		except Exception as e:
			logger.info("Exception occurred while creating item taxes %s " % e.message)
		return note_item_taxes, item_taxes_dict

	def notifyReceiptRejectCount(self, enterprise_id=None, sender_id=None, receipt=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param receipt:
		:return:
		"""
		try:
			customer = self.stores_dao.getCustomerDetails(enterprise_id=enterprise_id, supplier_id=receipt.supplier_id)
			message = PUSH_NOTIFICATION["receipt_rejected"] % (
				receipt.getCode(), " to %s" % customer.name if customer else "")
			module_code = "STORES"
			if receipt.received_against == "Sales Return":
				module_code = "SALES"
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code=module_code, message=message)
		except Exception as e:
			logger.exception("ICD receipt note updated notification failed %s" % e.message)

	@staticmethod
	def notifySaveIssueCount(enterprise_id=None, sender_id=None, issues=None, include_sender=True):
		"""
		:param enterprise_id:
		:param sender_id:
		:param issues:
		:param include_sender:
		:return:
		"""

		try:
			message = PUSH_NOTIFICATION["issues"] % (issues.getCode(), issues.issue_for, issues.employee.emp_name)
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="STORES",
				message=message, include_sender=include_sender)

		except Exception as e:
			logger.error("Failed notification.. %s " % e.message)

	def getOACodeByInvoice(self, enterprise_id=None, invoice_id=None):
		"""

		:param enterprise_id:
		:param invoice_id:
		:return:
		"""
		try:
			query = self.stores_dao.db_session.query(OA).join(OA.invoice_materials).filter(
				OA.enterprise_id == enterprise_id, InvoiceMaterial.invoice_id == invoice_id,
				InvoiceMaterial.enterprise_id == OA.enterprise_id).group_by(OA.id)
			oas = query.all()
			oa_list = []
			for oa in oas:
				oa_list.append(dict(oa_id=oa.id, oa_code=oa.getInternalCode()))
			grn_list = StoresService().getGRNCodeByInvoice(enterprise_id=enterprise_id, invoice_id=invoice_id)
			if grn_list:
				oa_list.extend(grn_list)
			return oa_list
		except:
			raise

	def getGRNCodeByInvoice(self, enterprise_id=None, invoice_id=None):
		"""

		:param enterprise_id:
		:param invoice_id:
		:return:
		"""
		try:
			query = self.stores_dao.db_session.query(Receipt).join(Receipt.invoice_materials).filter(
				Receipt.enterprise_id == enterprise_id, InvoiceMaterial.invoice_id == invoice_id,
				InvoiceMaterial.enterprise_id == Receipt.enterprise_id).group_by(Receipt.receipt_no)
			grns = query.all()
			oa_list = []
			for grn in grns:
				oa_list.append(dict(oa_id=grn.receipt_no, oa_code=grn.getCode()))
			return oa_list
		except:
			raise

	def getSupplierBucket(self, enterprise_id=None, indent_to_be_edited=None):
		new_buckets = []
		indent_no = indent_to_be_edited.indent_no
		#Make to be removed in future
		indent_buckets_query = """SELECT   *
				FROM
					(SELECT 
						im.indent_no,
						im.request_qty,
						im.purqty,
						m.drawing_no,
						m.name,
						m.is_service,
						m.unit AS unit_id,
						um.unit_name,
						im.make_id AS make_id,
						m.makes_json AS make_name,
						IF(smp.is_service IS NOT NULL, IF(smp.is_service > 0, 'JO', 'PO'), NULL) AS service,
						IFNULL(smp.price, m.price) AS supplier_price,
						smp.supp_id AS party_id,
						pm.party_name,
						pm.party_code,
						pm.currency AS party_currency_code,
						c.currency_name AS party_currency_name,
						m.id as item_id,
						IFNULL(im.alternate_unit_id, 0) AS alternate_unit_id,
						'' as scale_factor,
						CASE WHEN m.standard_packing_quantity IS NULL THEN '' ELSE m.standard_packing_quantity END AS spq,
						smp.moq
					FROM
						(SELECT 
						inm.*, IFNULL(SUM(m.pur_qty), 0) AS purqty
					FROM
						indents i
					JOIN indent_material inm ON i.indent_no = inm.indent_no
						AND i.indent_no = %s
					LEFT JOIN purchase_order AS o ON o.indent_no = i.indent_no
						AND o.enterprise_id = i.enterprise_id
						AND o.status <> 3
					LEFT JOIN purchase_order_material AS m ON o.id = m.pid
						AND TRIM(m.item_id) = TRIM(inm.item_id)
						AND m.enterprise_id = inm.enterprise_id
						AND m.make_id = inm.make_id
						GROUP BY inm.indent_no, inm.item_id, inm.make_id, inm.enterprise_id) AS im					    
					LEFT JOIN recent_valid_price_profiles smp ON im.item_id = smp.item_id
						AND im.make_id = smp.make_id
						AND im.enterprise_id = smp.enterprise_id
						AND status = 1
						AND effect_since <= CURDATE()
						AND (effect_till IS NULL
						OR effect_till >= CURDATE())
					LEFT JOIN make mk ON im.make_id = mk.id
						AND im.enterprise_id = mk.enterprise_id
					LEFT JOIN materials m ON im.item_id = m.id
						AND im.enterprise_id = m.enterprise_id
					LEFT JOIN unit_master um ON um.unit_id = m.unit
						AND um.enterprise_id = m.enterprise_id
					LEFT JOIN party_master pm ON pm.party_id = smp.supp_id
						AND pm.enterprise_id = smp.enterprise_id
					LEFT JOIN currency c ON c.id = pm.currency
					GROUP BY im.indent_no , im.item_id , im.make_id , smp.supp_id) indent_po
				ORDER BY party_name""" % indent_no

		result_dict = executeQuery(indent_buckets_query, as_dict=True)
		result_dict = list(result_dict)
		profiled_materials = []
		for item in result_dict:
			item['make_name'] = constructDifferentMakeName(item['make_name'])
			if item['party_id'] is not None:
				profiled_materials.append(
					{'item_id': item['item_id'], 'drawing_no': item['drawing_no'], 'make_id': item['make_id'], 'enterprise_id': enterprise_id})
		logger.debug("Profiled material:%s" % profiled_materials)

		def keyfunc(x):
			return x['party_name']

		BUCKET_DATA = sorted(result_dict, key=keyfunc)
		for party, group in itertools.groupby(BUCKET_DATA, keyfunc):  # bucket construction will takes over here.
			bucket = list(group)
			supplier_id = 'no-supplier' if bucket[0]['party_id'] is None else bucket[0]['party_id']
			bucket_name = 'NO SUPPLIERS PRICE' if bucket[0]['party_id'] is None else bucket[0]['party_name']
			party_currency_id = 1 if bucket[0]['party_currency_code'] is None else bucket[0]['party_currency_code']
			party_currency_name = 'INR' if bucket[0]['party_currency_name'] is None else bucket[0][
				'party_currency_name']
			if bucket[0]['alternate_unit_id']:
				scale_factor = self.stores_dao.getScaleFactor(
					enterprise_id=enterprise_id, item_id=bucket[0]['item_id'], alternate_unit_id=bucket[0]['alternate_unit_id'])
				if scale_factor:
					bucket[0]['request_qty'] = Decimal(bucket[0]['request_qty']) / Decimal(scale_factor) if bucket[0]['request_qty'] != 0 else 0
					bucket[0]['purqty'] = Decimal(bucket[0]['purqty']) / Decimal(scale_factor) if bucket[0]['purqty'] != 0 else 0
					bucket[0]['supplier_price'] = "{:0.5f}".format(Decimal(bucket[0]['supplier_price']) * Decimal(scale_factor)) if bucket[0]['supplier_price'] != 0 else 0
					bucket[0]['unit_name'] = self.stores_dao.getAlternateUnitName(
						enterprise_id=enterprise_id, unit_id=bucket[0]['alternate_unit_id'])
					bucket[0]['scale_factor'] = scale_factor
			new_buckets.append(
				[supplier_id, bucket_name, tuple(bucket), party_currency_id, party_currency_name])
		self.filter_best_priced_bucket(new_buckets, profiled_materials, enterprise_id, )
		job_list = []
		po_list = []
		job_mat_id_list = []
		for bucket in new_buckets[:]:
			if len(bucket[2]) != 0:
				material_flag = False
				for material in bucket[2]:
					if material['service'] == 'JO':
						job_list.append(material)
						job_mat_id_list.append(material['item_id'] + material['make_id'])
					else:
						if material['item_id'] + material['make_id'] in job_mat_id_list:
							material_flag = True
						if not material_flag:
							po_list.append(material)
					material_flag = False
				if bucket[0] != 'no-supplier':
					bucket.append([tuple([job_list if job_list else []][0]), tuple([po_list if po_list else []][0])])
				else:
					bucket.append((bucket[2], bucket[2]))
				job_list = []
				po_list = []
			else:
				new_buckets.remove(bucket)

		new_buckets = self.update_drawing_no(new_buckets)
		new_buckets = self.updatePoValue(buckets=new_buckets, enterprise_id=enterprise_id, indent_no=indent_no)
		other_po_value = self.updateOtherPoValue(buckets=new_buckets, enterprise_id=enterprise_id, indent_no=indent_no)
		return new_buckets, other_po_value

	@staticmethod
	def getIndentSuppliers(indent_no=None, enterprise_id=None, is_job=None, date=None):
		suppliers = SQLASession().query(
			IndentMaterial.indent_no, Party.id, Party.name, Party.currency
		).join(MaterialSupplierProfile, and_(
			MaterialSupplierProfile.item_id == IndentMaterial.item_id,
			MaterialSupplierProfile.enterprise_id == IndentMaterial.enterprise_id)
		       ).join(Party, and_(
			Party.id == MaterialSupplierProfile.supp_id, Party.enterprise_id == MaterialSupplierProfile.enterprise_id)
		              ).filter(
			IndentMaterial.indent_no == indent_no, IndentMaterial.enterprise_id == enterprise_id,
			IndentMaterial.make_id == MaterialSupplierProfile.make_id,
			MaterialSupplierProfile.status == MaterialSupplierProfile.APPROVED,
			MaterialSupplierProfile.effect_since <= date,
			or_(MaterialSupplierProfile.effect_till.is_(None), MaterialSupplierProfile.effect_till >= date),
			MaterialSupplierProfile.is_service.is_(is_job == 'true')
		).group_by(Party.id)
		return suppliers

	@staticmethod
	def filter_best_priced_bucket(buckets, profiled_materials, enterprise_id):
		for material in profiled_materials:
			sql = """SELECT supp_id FROM recent_valid_price_profiles as smp 
				WHERE smp.item_id = '{item_id}' AND smp.make_id = '{make_id}' AND smp.status != -1 
				AND smp.enterprise_id = '{enterprise_id}' ORDER BY effect_since DESC, price""".format(
				item_id=material['item_id'], make_id=material['make_id'], enterprise_id=enterprise_id)
			result = executeQuery(sql)
			supp_id = ""
			if result:
				supp_id = result[0][0]
			for bucket in buckets:
				mat_list = list(bucket[2])
				supplier_id = bucket[0]
				if supplier_id != supp_id:
					for mat in mat_list:
						if mat['item_id'] == material['item_id'] and mat['make_id'] == material['make_id']:
							mat_list.remove(mat)
					bucket[2] = tuple(mat_list)

	@staticmethod
	def updatePoValue(buckets=None, indent_no=None, enterprise_id=None):
		try:
			profiled_supplier_id = []
			for bucket in buckets:
				supplier_id = bucket[0]
				if bucket[0] != 'no-supplier':
					condition = " i.indent_no = '%s' and s.party_id = '%s' and t.enterprise_id = '%s'" % (
						indent_no, supplier_id, enterprise_id)
				else:
					party_id_list = []
					for party_id in profiled_supplier_id:
						party_id_list.append(party_id)
					if party_id_list:
						supplier_list_str = ""
						for bucket in buckets:
							if bucket[2]:
								if bucket[0] != "no-supplier":
									supplier_list_str = supplier_list_str + str(bucket[0]) + ','
						condition = " i.indent_no = '%s' and s.party_id not in %s and t.enterprise_id = '%s'" % (
							indent_no, '(' + supplier_list_str.rstrip(',') + ')', enterprise_id)
					else:
						condition = " i.indent_no = '%s' and t.enterprise_id = '%s'" % (indent_no, enterprise_id)
				po_query = """SELECT t.id,DATE_FORMAT(t.order_date,'%%d-%%m-%%Y') AS tDate, t.orderno, 
						DATE_FORMAT(t.approved_on,'%%d-%%m-%%Y') AS poDate,s.party_name, t.total, p.name, t.indent_no, t.status, 
						e.code, t.financial_year, i.indent_id, i.financial_year,t.type 
					FROM purchase_order t JOIN projects p ON t.project_code=p.id AND t.enterprise_id=p.enterprise_id 
						JOIN party_master s ON s.party_id=t.supplier_id AND s.enterprise_id=t.enterprise_id 
						JOIN enterprise e ON t.enterprise_id=e.id 
						LEFT JOIN indents as i ON i.indent_no=t.indent_no AND t.enterprise_id=i.enterprise_id AND t.status!=3
					WHERE %s GROUP BY t.id ORDER by t.order_date, t.id""" % condition
				po_list = executeQuery(po_query)
				profiled_supplier_id.append(bucket[0])
				if po_list:
					po_value = 0
					for item in po_list:
						po_value += item[5]
					bucket.append(po_value)
				else:
					bucket.append(0)
		except Exception as e:
			logger.info("Exception occurred while updating po value %s " % e)
		return buckets

	@staticmethod
	def updateOtherPoValue(buckets=None, indent_no=None, enterprise_id=None):
		try:
			other_party_id_list = []
			other_party_id_list_str = ''
			for bucket in buckets:
				supplier_id = bucket[0]
				if supplier_id != 'no-supplier':
					other_party_id_list_str += str(supplier_id) + ','
					other_party_id_list.append(supplier_id)
			if other_party_id_list:
				condition = " i.indent_no = '%s' and s.party_id not in %s and t.enterprise_id = '%s'" % (
					indent_no, '(' + other_party_id_list_str.rstrip(',') + ')', enterprise_id)
			else:
				condition = " i.indent_no = '%s' and t.enterprise_id = '%s'" % (indent_no, enterprise_id)
			po_query = """SELECT t.id,DATE_FORMAT(t.order_date,'%%d-%%m-%%Y') AS tDate, t.orderno, 
					DATE_FORMAT(t.approved_on,'%%d-%%m-%%Y') AS poDate,s.party_name, t.total, p.name, t.indent_no, t.status, 
					e.code, t.financial_year, i.indent_id, i.financial_year,t.type 
				FROM purchase_order t JOIN projects p ON t.project_code=p.id AND t.enterprise_id=p.enterprise_id 
					JOIN party_master s ON s.party_id=t.supplier_id AND s.enterprise_id=t.enterprise_id 
					JOIN enterprise e ON t.enterprise_id=e.id 
					LEFT JOIN indents as i ON i.indent_no=t.indent_no AND t.enterprise_id=i.enterprise_id 
				WHERE %s GROUP BY t.id ORDER by t.order_date, t.id""" % condition
			po_list = executeQuery(po_query)
			po_value = 0
			if po_list:
				for item in po_list:
					po_value += item[5]
				po_value = po_value
			else:
				po_value = 0
		except Exception as e:
			logger.info("Exception occurred while updating other po value %s " % e)
		return po_value

	@staticmethod
	def update_drawing_no(buckets):
		try:
			for supplier in buckets:
				for mat_details in supplier[5][0]:

					if mat_details['drawing_no']:
						mat_details['cleaned_drawing_no'] = mat_details['drawing_no'].replace(".", "_")
					else:
						mat_details['cleaned_drawing_no'] = ""

			for supplier in buckets:
				for mat_details in supplier[5][1]:
					if mat_details['drawing_no']:
						mat_details['cleaned_drawing_no'] = mat_details['drawing_no'].replace(".", "_")
					else:
						mat_details['cleaned_drawing_no'] = ""
		except Exception as e:
			logger.info("Exception occurred while updating drawing_no %s " % e)
		return buckets

	def getIndentValue(self, indent_no, enterprise_id):
		indent = self.stores_dao.getIndent(indent_no, enterprise_id)
		indent_mat_drawing_no_list = []
		total_sum = 0
		for material in indent.materials:
			indent_mat_drawing_no_list.append(material.item_id)
			mat_store_price_query = """SELECT price FROM materials where id = '%s' and enterprise_id = %s  """ % (
				material.item_id, enterprise_id)
			store_price = executeQuery(mat_store_price_query)
			price = store_price[0][0]
			if material.alternate_unit_id:
				scale_factor = self.stores_dao.getScaleFactor(
						enterprise_id=material.enterprise_id, item_id=material.item_id, alternate_unit_id=material.alternate_unit_id)
				price = Decimal(price) * Decimal(scale_factor)
			total_store_value = float(price) * float(material.quantity)
			total_sum += total_store_value
		return total_sum

	def fetchDraftGrn(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		grn_list = []
		try:
			receipts = self.stores_dao.db_session.query(Receipt).filter(
				Receipt.enterprise_id == enterprise_id, Receipt.status == Receipt.STATUS_DRAFT,
				Receipt.received_against.notin_(("Issues", "Sales Return"))).all()
			for receipt in receipts:
				grn_item = self.__getGrnDetails(receipt=receipt)
				grn_list.append(grn_item)
			grn_list = sorted(grn_list, key=lambda a: a['code'])

		except:
			raise
		return grn_list

	def __getGrnDetails(self, receipt=None):
		"""

		:param po:
		:return:
		"""

		receipt_item = {}
		try:
			receipt_item['receipt_no'] = receipt.receipt_no
			receipt_item['code'] = receipt.getCode()
			receipt_item['enterprise_id'] = receipt.enterprise_id
			receipt_item['project_name'] = receipt.project.name if receipt.project else ''
			receipt_item['project_code'] = receipt.project.code if receipt.project else ''
			receipt_item['status'] = receipt.status
			receipt_item["invoice_date"] = receipt.invoice_date.strftime("%d-%m-%Y") if receipt.invoice_date else None
			receipt_item["receipt_date"] = receipt.receipt_date.strftime("%d-%m-%Y") if receipt.receipt_date else None
			try:
				receipt_item["approved_on"] = receipt.approved_on.strftime("%d-%m-%Y") if receipt.approved_on else None
			except Exception as e:
				receipt_item["approved_on"] = ''
			receipt_item['document_uri'] = None if receipt.attachment is None else receipt.attachment.attachment.file
			receipt_item['supplier_name'] = receipt.supplier.name
			receipt_item['tags'] = helper.getTagList(tags=receipt.tags)
			receipt_item['invoice_value'] = float(receipt.invoice_value) if receipt.invoice_value else 0
			receipt_item['note_value'] = float(receipt.dr_note_amt)
			receipt_item['currency_name'] = receipt.currency.code
			receipt_item['remarks'] = receipt.remarks if receipt.remarks else []
			receipt_item['audit_remarks'] = receipt.audit_remarks if receipt.audit_remarks else []
			receipt_item['is_grn']='true'
			if receipt.documents and receipt.documents != '':
				receipt_item['documents'] = json.loads(json.dumps(receipt.documents))
			# Grn Status
			receipt_item.update(receipt_item)
		except Exception as e:
			logger.exception("Could not make the object PurchaseOrder - %s" % e)

		return receipt_item

	def generateGRNDocument(self, receipt_no):
		"""
		Fetches the document persisted for a given receipt_no. If such a document is not available in DB, fetches the
		Receipt details and generates the respective document.

		:param receipt_no:
		:return: ReceiptDocument instance holding the document in PDF format queried (or generated)
		"""
		logger.info('Generating documents for Receipt no: %s' % receipt_no)
		db_session = self.stores_dao.db_session
		db_session.begin(subtransactions=True)
		receipt_document = None
		receipt_code = None
		try:
			receipt_to_print = db_session.query(Receipt).filter(Receipt.receipt_no == receipt_no).first()
			receipt_document = db_session.query(ReceiptDocument).join(Receipt, and_(
				Receipt.last_modified_on == ReceiptDocument.revised_on,
				Receipt.receipt_no == ReceiptDocument.receipt_no)).filter(
				ReceiptDocument.receipt_no == receipt_no).first()
			# FIXME: Generating, Printing and persisting the documents must be segregated
			logger.info("Receipt Document object %s" % (
				"exists" if (receipt_document and receipt_document.receipt_doc) else "does not exist"))
			receipt_code = receipt_to_print.getCode()
			temp_doc_path = getFormattedDocPath(code=receipt_code, id=receipt_no)
			if not (receipt_document and receipt_document.receipt_doc):
				document_generator = ReceiptPDFGenerator(receipt=receipt_to_print, target_file_path=temp_doc_path)
				document_pdf = document_generator.generatePDF(receipt_to_print)
				writeFile(document_pdf, temp_doc_path)
				if receipt_to_print.status == Receipt.STATUS_REJECTED:
					document_generator.addCancelWaterMark(temp_doc_path)
				if receipt_document is None:
					receipt_document = ReceiptDocument(receipt_no=receipt_no, enterprise_id=receipt_to_print.enterprise_id)
				if receipt_to_print.status != Receipt.STATUS_DRAFT and not receipt_document.receipt_doc:
					receipt_document.receipt_doc = readFile(temp_doc_path)
				receipt_document.revised_on = receipt_to_print.last_modified_on
				receipt_document.revised_by = receipt_to_print.last_modified_by
				if receipt_to_print.status != Receipt.STATUS_DRAFT:
					logger.info("Approved Receipt, hence Persisting the document...")
					db_session.add(receipt_document)
			else:
				logger.info("Writing persisted Doc in Temp path...")
				writeFile(receipt_document.receipt_doc, temp_doc_path)
				receipt_status = db_session.query(Receipt.status).filter(Receipt.receipt_no == receipt_no).first()
				logger.info("Receipt Status - %s" % "REJECTED" if int(
					"%s" % receipt_status) == Receipt.STATUS_REJECTED else "APPROVED")
				if int("%s" % receipt_status) == Receipt.STATUS_REJECTED:
					logger.info("Adding Cancelled Watermark")
					receipt_document.revised_on = receipt_to_print.last_modified_on
					receipt_document.revised_by = receipt_to_print.last_modified_by
					document_generator = ReceiptPDFGenerator(temp_doc_path)
					document_generator.addCancelWaterMark(temp_doc_path)
			db_session.commit()
		except Exception as e:
			db_session.rollback()
			logger.exception('Receipt Document creation failed - %s' % e.message)
		return receipt_document, receipt_code

	def getLedgerBill(self, enterprise_id=None, receipt_no=None):
		"""

		:return:
		"""
		try:
			ledger_bill = self.stores_dao.db_session.query(LedgerBill).join(Receipt, and_(
				Receipt.ledger_bill_id == LedgerBill.id, Receipt.enterprise_id == LedgerBill.enterprise_id
			)).filter(Receipt.enterprise_id == enterprise_id, Receipt.receipt_no == receipt_no).first()
			logger.info("Ledger Bill %s for receipt_no %s " % (ledger_bill, receipt_no))
			return ledger_bill
		except:
			raise

	def getGRNAgainstDC(self, enterprise_id=None, receipt_no=None):
		"""

		:param enterprise_id:
		:param receipt_no:
		:return:
		"""
		try:
			grn_against_dc = self.stores_dao.db_session.query(Receipt).join(ReceiptMaterial, and_(
				Receipt.receipt_no == ReceiptMaterial.receipt_no,
				Receipt.enterprise_id == ReceiptMaterial.enterprise_id)).filter(
				ReceiptMaterial.enterprise_id == enterprise_id, ReceiptMaterial.received_grn_id == receipt_no).first()
			return [grn_against_dc]
		except Exception as e:
			raise e

	def getDcAgainstJobIn(self, receipt_no):
		linked_dc_codes = set()
		try:
			for dc in self.stores_dao.db_session.query(Invoice).join(Invoice.items).filter(
				InvoiceMaterial.receipt_no == receipt_no, Invoice.status >= Invoice.STATUS_DRAFT):
				linked_dc_codes.add(dc.getInternalCode())
			return list(linked_dc_codes)
		except Exception as e:
			raise e

	def superEditIndentCode(
			self, enterprise_id=None, user_id=None, indent_no=None, new_financial_year=None,
			new_indent_id=None, new_sub_number=None):
		"""

		:return: [is_success, json_response]
		"""
		self.stores_dao.db_session.begin(subtransactions=True)
		try:
			indent_to_be_modified = self.stores_dao.getIndent(indent_no=indent_no, enterprise_id=enterprise_id)
			old_code = indent_to_be_modified.getCode()
			existing_indent = self.stores_dao.db_session.query(Indent).filter(
				Indent.enterprise_id == enterprise_id, Indent.financial_year == new_financial_year,
				Indent.indent_id == new_indent_id, Indent.sub_number == new_sub_number).first()
			response = response_code.failure()
			response['code'] = old_code
			if not existing_indent:
				indent_to_be_modified.financial_year = new_financial_year
				indent_to_be_modified.indent_id = new_indent_id
				indent_to_be_modified.sub_number = new_sub_number
				indent_to_be_modified.super_modified_on = datetime.now()
				indent_to_be_modified.super_modified_by = user_id
				self.stores_dao.db_session.add(indent_to_be_modified)
				response = response_code.success()
				response['custom_message'] = "Successfully changed the Indent Code from '%s' to '%s'!" % (
					old_code, indent_to_be_modified.getCode())
				response['code'] = indent_to_be_modified.getCode()
				message = PUSH_NOTIFICATION['indent_code'] % (old_code, indent_to_be_modified.getCode())
				push_notification(
					enterprise_id=enterprise_id, sender_id=user_id, module_code="INDENT", message=message,
					code=indent_to_be_modified.getCode(), type=indent_to_be_modified.purchase_account_id)
			elif indent_to_be_modified.indent_no == existing_indent.indent_no:
				response['custom_message'] = "No changes detected in Indent code to save!"
			else:
				response['custom_message'] = "An Indent with Code '%s' already exists." \
				                             " Please assign a different Code!" % existing_indent.getCode()
			self.stores_dao.db_session.commit()
			return response
		except:
			self.stores_dao.db_session.rollback()
			raise

	def superEditReceiptCode(
			self, enterprise_id=None, user_id=None, receipt_no=None, new_financial_year=None,
			new_receipt_code=None, new_sub_number=None):
		"""

		:return: [is_success, json_response]
		"""
		self.stores_dao.db_session.begin(subtransactions=True)
		try:
			receipt_to_be_modified = self.stores_dao.getReceipt(enterprise_id=enterprise_id, receipt_no=receipt_no)
			old_code = receipt_to_be_modified.getCode()
			if new_sub_number is not None and new_sub_number.strip() == "":
				new_sub_number = None
			existing_receipt = self.stores_dao.getReceipt(
				enterprise_id=enterprise_id, financial_year=new_financial_year,
				receipt_code=new_receipt_code, sub_number=new_sub_number)
			response = response_code.failure()
			response['code'] = old_code
			if not existing_receipt:
				receipt_to_be_modified.financial_year = new_financial_year
				receipt_to_be_modified.receipt_code = new_receipt_code
				receipt_to_be_modified.sub_number = new_sub_number
				receipt_to_be_modified.super_modified_on = datetime.now()
				receipt_to_be_modified.last_modified_on = datetime.now()
				receipt_to_be_modified.last_modified_by = user_id
				receipt_to_be_modified.super_modified_by = user_id
				self.stores_dao.db_session.add(receipt_to_be_modified)
				response = response_code.success()
				response['custom_message'] = "Successfully changed the Receipt code from '%s' to '%s'!" % (
					old_code, receipt_to_be_modified.getCode())
				response['code'] = receipt_to_be_modified.getCode()
				message = PUSH_NOTIFICATION['receipt_code'] % (old_code, receipt_to_be_modified.getCode())
				push_notification(
					enterprise_id=enterprise_id, sender_id=user_id, module_code="RECEIPT", message=message,
					code=receipt_to_be_modified.getCode())
			elif receipt_to_be_modified.receipt_no == existing_receipt.receipt_no:
				response['custom_message'] = "No changes detected in Receipt code to save!"
			else:
				response[
					'custom_message'] = "An Receipt with code '%s' already exists. Please assign a different Code!" % (
					existing_receipt.getCode())
			self.stores_dao.db_session.commit()
			return response
		except:
			self.stores_dao.db_session.rollback()
			raise

	def getReceiptCodes(self, enterprise_id=None, invoice_id=None):
		"""

		:return:
		"""
		try:
			receipts = self.stores_dao.db_session.query(Receipt).join(Receipt.items_returned).filter(
				ReceiptMaterial.dc_id == invoice_id,
				ReceiptMaterial.enterprise_id == enterprise_id,
				Receipt.status > -1).group_by(Receipt.receipt_no).all()
			return [receipt.getCode() for receipt in receipts]

		except:
			raise

	@staticmethod
	def getIssueStockReport(
			enterprise_id=None, since=None, till=None, item_id=None, make_id=1, is_faulty=False, issued_to=None,
			ignore_items_not_transacted=False):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:param item_id:
		:param make_id:
		:param is_faulty:
		:param issued_to:
		:param ignore_items_not_transacted:
		:return:
		"""
		try:
			logger.info('Fetching Issue Details between %s, %s' % (since, till))
			condition = ""
			issued_condition = ""
			if issued_to and issued_to != "All" and issued_to != "":
				condition += " issued_to = '%s' AND " % issued_to
				issued_condition += " issued_to = '%s' AND " % issued_to
			if item_id and item_id != "All":
				condition += " item_id = '%s' AND " % item_id
				issued_condition += " m.id = '%s' AND " % item_id
				if make_id:
					condition += " make_id = %s  AND " % make_id
					issued_condition += " im.make_id = %s  AND " % make_id
				if is_faulty:
					condition += " is_faulty = %s  AND " % is_faulty
					issued_condition += " im.is_faulty = %s  AND " % is_faulty
			received_field = "SUM(im.acc_qty)"
			issued_field = "SUM(im.qty)"
			received_query = """SELECT IFNULL(issued_to,"") as issued_to, material_name, drawing_no, make_id, is_faulty, unit,
				stock_price, opening_received, opening_issued, SUM(received) AS received, SUM(issued) AS issued,item_id,make_name FROM
					(SELECT 
				g.grn_no AS grn_no,g.enterprise_id,IFNULL(GROUP_CONCAT(DISTINCT i.issued_to), (SELECT issued_to FROM invoice join 
				grn_issue_map as gim ON gim.issue_id = invoice.id WHERE gim.grn_no=g.grn_no limit 1)) AS issued_to,
				CONCAT(m.name, CASE WHEN m.drawing_no IS NULL THEN '' ELSE CONCAT(' - ', m.drawing_no) END,
						IF(im.is_faulty=1, ' (FAULTY)', '')) AS material_name,
				m.drawing_no AS drawing_no,im.make_id AS make_id,im.is_faulty AS is_faulty,um.unit_name AS unit,
				m.price AS stock_price,{opening_received_field} AS opening_received,0 AS opening_issued,
				{received_field} AS received,0 AS issued,g.inward_date as inward_date,m.id as item_id,m.makes_json as make_name 
				FROM grn_material AS im
				JOIN grn AS g ON im.grnNumber = g.grn_no
				AND im.enterprise_id = g.enterprise_id
				LEFT JOIN materials AS m ON im.item_id = m.id
				AND im.enterprise_id=m.enterprise_id				
				JOIN unit_master AS um ON um.unit_id = m.unit
				AND um.enterprise_id = m.enterprise_id
				LEFT JOIN invoice AS i ON i.id = im.dc_id
				AND i.enterprise_id = im.enterprise_id
				WHERE
				g.rec_against = 'Issues' AND g.status > -1 GROUP BY g.grn_no, i.issued_to, item_id , make_id , is_faulty) AS issue_stock_abstract
				WHERE enterprise_id = {enterprise_id} AND			 
				{condition} """

			issued_query = """SELECT 
				IFNULL(i.issued_to,"") AS issued_to,				
				CONCAT(m.name, CASE WHEN m.drawing_no IS NULL THEN '' ELSE CONCAT(' - ', m.drawing_no) END,
						IF(im.is_faulty=1, ' (FAULTY)', '')) AS material_name,
				m.drawing_no AS drawing_no,im.make_id AS make_id,im.is_faulty AS is_faulty,um.unit_name AS unit,
				m.price AS stock_price,0 AS opening_received,{opening_issued_field} AS opening_issued,
				0 AS received,{issued_field} AS issued,m.id as item_id,m.makes_json as make_name
				FROM
				invoice_materials AS im
				JOIN invoice AS i ON im.invoice_id = i.id
				AND im.enterprise_id = i.enterprise_id
				LEFT JOIN materials AS m ON im.item_id = m.id
				AND im.enterprise_id=m.enterprise_id				
				JOIN unit_master AS um ON um.unit_id = m.unit
				AND um.enterprise_id = m.enterprise_id
				WHERE
				i.type = 'Issue' AND im.enterprise_id= {enterprise_id} AND
				{issued_condition}"""

			opening_received_query = received_query.format(
				condition=condition, opening_received_field=received_field, received_field=0,
				enterprise_id=enterprise_id)
			received_query = received_query.format(
				condition=condition, opening_received_field=0, received_field=received_field,
				enterprise_id=enterprise_id)

			opening_issued_query = issued_query.format(
				issued_condition=issued_condition, opening_issued_field=issued_field, issued_field=0,
				enterprise_id=enterprise_id)
			issued_query = issued_query.format(
				issued_condition=issued_condition, opening_issued_field=0, issued_field=issued_field,
				enterprise_id=enterprise_id)

			having_clause = ""
			if ignore_items_not_transacted is True:
				having_clause = " HAVING received > 0 OR issued > 0 "
			issue_materials_query = """SELECT 
				issued_to,material_name,unit,stock_price,(SUM(opening_issued)- SUM(opening_received)) AS opening,
				SUM(received) AS received,SUM(issued) AS issued,is_faulty, drawing_no, item_id,make_name
				FROM
				({opening_received_query} 
					inward_date < '{since}'
				GROUP BY issued_to , item_id , make_id , is_faulty UNION {received_query} 
					inward_date BETWEEN '{since}' AND '{till}'
				GROUP BY issued_to , item_id , make_id , is_faulty UNION {opening_issued_query} 
					IFNULL(i.issued_on, NOW()) < '{since}'
				GROUP BY issued_to , item_id , make_id , is_faulty UNION {issued_query} 
					IFNULL(i.issued_on, NOW()) BETWEEN '{since}' AND '{till}'
				GROUP BY issued_to , item_id , make_id , is_faulty) AS isr
				GROUP BY isr.issued_to , isr.item_id , isr.make_id , isr.is_faulty {having_clause}""".format(
				since=since, till=till, received_query=received_query, issued_query=issued_query,
				opening_received_query=opening_received_query, opening_issued_query=opening_issued_query,
				having_clause=having_clause)
			result = executeQuery(issue_materials_query, as_dict=True)
			issue_report_list = []
			for item in result:
				if item['make_name']:
					make_name = helper.constructDifferentMakeName(item['make_name'])
					if make_name:
						make_name = " [" + make_name + "]"
					item['material_name'] = item['material_name'] + make_name
				issue_report_list.append({
					'issued_to': item['issued_to'], 'material_name': item['material_name'], 'unit': item['unit'],
					'stock_price': item['stock_price'], 'opening_qty': item['opening'], 'opening_value': item['opening'] * item['stock_price'],
					'received': item['issued'], 'received_value': item['issued'] * item['stock_price'], 'issued': item['received'],
					'issued_value': item['received'] * item['stock_price'], 'closing_qty': (item['opening'] + item['issued']) - item['received'],
					'closing_value': ((item['opening'] + item['issued']) - item['received']) * item['stock_price']})
			return issue_report_list
		except Exception as e:
			raise e

	@staticmethod
	def getJobInRegisterReport(
			enterprise_id=None, since=None, till=None, item_id=None, make_id=1, is_faulty=False, party_id=None):
		"""
		:param enterprise_id:
		:param since:
		:param till:
		:param item_id:
		:param make_id:
		:param is_faulty:
		:param party_id:
		:return:
		"""
		try:
			logger.info('Fetching Job In Details between %s, %s' % (since, till))
			condition = ""
			invoice_condition = ""
			party_condition = ""
			if party_id and party_id != "All":
				party_condition += " AND p.party_id = '%s' " % party_id
			if item_id and item_id != "All":
				condition += " AND im.item_id = '%s' " % item_id
				invoice_condition += " AND im.item_id = '%s' " % item_id
				if make_id:
					condition += " AND im.make_id = %s " % make_id
					invoice_condition += " AND im.make_id = %s " % make_id
				if is_faulty:
					condition += "  AND im.is_faulty = %s " % is_faulty
					invoice_condition += "  AND im.is_faulty = %s " % is_faulty

			received_query = """SELECT IFNULL(CONCAT(o.financial_year, "/", LEFT(UPPER(o.type), 1), LPAD(o.oa_no, 5, 0), 
							IFNULL(o.sub_number, "")), "") AS oa_no, IFNULL(DATE(o.approved_on), "") as oa_date, 
							IFNULL(group_concat(CASE WHEN g.status = 0 THEN concat("TMP#", g.grn_no) 
							ELSE concat(g.financial_year, "/GRN/", LPAD(g.receipt_no, 6, 0),
							IFNULL(g.sub_number,"")) END),"") as grn_no,
							IFNULL(DATE(g.inward_date), "") AS grn_date, g.invno as party_inv_no,
							IFNULL(DATE(g.inv_date), "") as party_inv_date, p.party_name as party_name,
							CONCAT(IFNULL(CONCAT(m.name , "-" , m.drawing_no), m.name),  
							IF(im.is_faulty = 1, ' (FAULTY)', '')) AS material_name, um.unit_name AS unit, 
							SUM(im.acc_qty) AS received, 0 AS delivered, "" as dc_code, "" as dc_date,g.inward_date as order_date,
							m.makes_json as make_name 
							FROM grn_material AS im JOIN grn AS g ON im.grnNumber = g.grn_no AND 
							im.enterprise_id = g.enterprise_id LEFT JOIN materials AS m ON im.item_id = m.id 
							AND im.enterprise_id = m.enterprise_id JOIN unit_master AS um ON 
							um.unit_id = m.unit AND um.enterprise_id = m.enterprise_id JOIN party_master AS p ON 
							p.party_id = g.party_id AND p.enterprise_id = g.enterprise_id LEFT JOIN order_acknowledgement 
							AS o ON o.id = im.oa_id AND o.enterprise_id = im.enterprise_id WHERE g.rec_against = 'Job In' 
							AND g.status > - 1 AND im.rec_grn_id is null AND g.enterprise_id = {enterprise_id} AND 
							g.inward_date BETWEEN '{since}' AND '{till}' {party_condition} {condition} GROUP BY o.id, g.grn_no ,party_name, material_name """ .format(
				since=since, till=till, enterprise_id=enterprise_id, party_condition=party_condition, condition=condition)

			invoice_query = """SELECT IFNULL(CONCAT(o.financial_year, "/", LEFT(UPPER(o.type), 1), LPAD(o.oa_no, 5, 0), 
							IFNULL(o.sub_number, "")), "") AS oa_no, IFNULL(DATE(o.approved_on), '') AS oa_date,
							IFNULL(group_concat(CASE WHEN g.status = 0 THEN concat("TMP#", g.grn_no) 
							ELSE concat(g.financial_year, "/GRN/", LPAD(g.receipt_no, 6, 0),
							IFNULL(g.sub_number,"")) END),"") as grn_no, IFNULL(DATE(g.inward_date), "") AS grn_date,
							IFNULL(GROUP_CONCAT(g.invno),"") AS party_inv_no, IFNULL(DATE(g.inv_date), "") AS party_inv_date,
							p.party_name AS party_name, CONCAT(IFNULL(CONCAT(m.name, '-', m.drawing_no),m.name),
							IF(im.is_faulty = 1, ' (FAULTY)', '')) AS material_name, um.unit_name AS unit, 0 AS received,
							SUM(im.qty) AS delivered, IFNULL(i.invoice_code, CONCAT('PROFORMA#', LEFT(i.type, 1),
							IFNULL(CONVERT( LPAD(i.invoice_no, 6, '0') USING LATIN1), i.id))) AS dc_code,
							IFNULL(DATE(i.issued_on),"") as dc_date,g.inv_date as order_date,m.makes_json as make_name FROM invoice_materials AS im JOIN invoice AS i ON 
							im.invoice_id = i.id AND im.enterprise_id = i.enterprise_id LEFT JOIN materials AS m ON 
							im.item_id = m.id AND im.enterprise_id = m.enterprise_id JOIN unit_master AS um ON 
							um.unit_id = m.unit AND um.enterprise_id = m.enterprise_id	JOIN party_master AS p ON 
							p.party_id = i.party_id AND p.enterprise_id = i.enterprise_id LEFT JOIN grn_material AS gm ON 
							gm.oa_id = im.oa_no AND gm.item_id = im.item_id AND gm.make_id = im.make_id AND 
							gm.is_faulty = im.is_faulty AND gm.enterprise_id = im.enterprise_id LEFT JOIN grn AS g ON 
							g.grn_no = gm.grnNumber AND g.enterprise_id = gm.enterprise_id LEFT JOIN order_acknowledgement AS o ON 
							o.id = im.oa_no AND o.type = 'Job' WHERE i.type != 'Issue' AND o.type = 'Job' AND i.status > -1 AND im.delivered_dc_id is null AND im.oa_no IS NOT NULL AND 
							im.enterprise_id = {enterprise_id} AND i.issued_on BETWEEN '{since}' AND '{till}' {party_condition} {invoice_condition} 
							GROUP BY o.id, im.invoice_id, party_name, material_name """ .format(
						since=since, till=till, enterprise_id=enterprise_id, party_condition=party_condition, invoice_condition=invoice_condition)
			# Non Linked OA Material
			non_oa_invoice_query = """ UNION SELECT  "" oa_no, "" AS oa_date, IFNULL(GROUP_CONCAT(CASE WHEN g.status = 0 
					and g.rec_against = 'Job IN' THEN CONCAT('TMP#', g.grn_no) ELSE IF(g.rec_against = 'Job IN', 
					CONCAT(g.financial_year, '/GRN/', LPAD(g.receipt_no, 6, 0),IFNULL(g.sub_number, '')),'') END), '') AS grn_no,
					IFNULL(DATE(g.inward_date), '') AS grn_date, IFNULL(GROUP_CONCAT(g.invno),'') AS party_inv_no, IFNULL(DATE(g.inv_date), '') AS party_inv_date,
					p.party_name AS party_name, CONCAT(IFNULL(CONCAT(m.name, '-', m.drawing_no),m.name), 
					IF(im.is_faulty = 1, ' (FAULTY)', '')) AS material_name, um.unit_name AS unit, 0 AS received, 
					SUM(im.qty) AS delivered, IFNULL(i.invoice_code, CONCAT('PROFORMA#', LEFT(i.type, 1), 
					IFNULL(CONVERT( LPAD(i.invoice_no, 6, '0') USING LATIN1), i.id))) AS dc_code,
					IFNULL(DATE(i.issued_on), '') AS dc_date, i.prepared_on AS order_date,m.makes_json as make_name FROM invoice_materials AS im JOIN 
					invoice AS i ON im.invoice_id = i.id AND im.enterprise_id = i.enterprise_id LEFT JOIN materials AS m ON 
                    im.item_id = m.id AND im.enterprise_id = m.enterprise_id JOIN unit_master AS um ON 
                    um.unit_id = m.unit AND um.enterprise_id = m.enterprise_id JOIN party_master AS p ON p.party_id = i.party_id
                    AND p.enterprise_id = i.enterprise_id LEFT JOIN grn_material AS gm ON gm.oa_id is Null AND 
                    gm.item_id = im.item_id AND gm.make_id = im.make_id AND gm.is_faulty = im.is_faulty AND 
                    gm.enterprise_id = im.enterprise_id AND gm.grnNumber = im.grn_id LEFT JOIN grn AS g ON g.grn_no = gm.grnNumber 
                    AND g.enterprise_id = gm.enterprise_id AND g.rec_against = 'Job IN' WHERE i.type != 'Issue'  AND i.status > -1 AND  
                    im.delivered_dc_id IS NULL AND im.oa_no IS NULL AND im.enterprise_id = {enterprise_id} AND g.rec_against = 'Job IN'
                    AND i.issued_on BETWEEN '{since}' AND '{till}' {party_condition} {invoice_condition} GROUP BY im.invoice_id , party_name , 
                    material_name """.format(since=since, till=till, enterprise_id=enterprise_id,
						party_condition=party_condition, invoice_condition=invoice_condition)

			job_in_query = """select * from ( {received_query} UNION {invoice_query} {non_oa_invoice_query}) 
				as job_in_report order by order_date  """ .format(
				received_query=received_query, invoice_query=invoice_query, non_oa_invoice_query=non_oa_invoice_query)
			result = executeQuery(job_in_query, as_dict=True)
			issue_report_list = []
			for item in result:
				make_name = ""
				if item['make_name']:
					make_name = helper.constructDifferentMakeName(item['make_name'])
					if make_name:
						make_name = " [" + make_name + "]"
					item['material_name'] = item['material_name'] + make_name

				issue_report_list.append({
					'oa_no': item['oa_no'], 'oa_date': item['oa_date'], 'grn_no': item['grn_no'], 'grn_date': item['grn_date'], 'party_inv_no': item['party_inv_no'],
					'party_inv_date': item['party_inv_date'], 'party_name': item['party_name'], 'material_name': item['material_name'], 'unit': item['unit'],
					'received': item['received'], 'issued': item['delivered'], 'dc_no': item['dc_code'], 'dc_date': item['dc_date']})
			return issue_report_list
		except Exception as e:
			raise e

	@staticmethod
	def getJobOutRegisterReport(
			enterprise_id=None, since=None, till=None, item_id=None, make_id=1, is_faulty=False, party_id=None):
		"""
		:param enterprise_id:
		:param since:
		:param till:
		:param item_id:
		:param make_id:
		:param is_faulty:
		:param party_id:
		:return:
		"""
		try:
			logger.info('Fetching Job Out Details between %s, %s' % (since, till))
			condition = ""
			invoice_condition = ""
			party_condition = ""
			if party_id and party_id != "All":
				party_condition += " AND p.party_id = '%s' " % party_id
			if item_id and item_id != "All":
				condition += " AND im.item_id = '%s' " % item_id
				invoice_condition += " AND im.item_id = '%s' " % item_id
				if make_id:
					condition += " AND im.make_id = %s " % make_id
					invoice_condition += " AND im.make_id = %s " % make_id
				if is_faulty:
					condition += "  AND im.is_faulty = %s " % is_faulty
					invoice_condition += "  AND im.is_faulty = %s " % is_faulty

			received_query = """SELECT IFNULL(CONCAT(o.financial_year, "/JO/", LPAD(o.orderno, 6, 0), 
							IFNULL(o.sub_number, "")), "") AS oa_no, IFNULL(DATE(o.approved_on), "") as oa_date, 
							IFNULL(group_concat(CASE WHEN g.status = 0 THEN concat("TMP#", g.grn_no) 
							ELSE concat(g.financial_year, "/GRN/", LPAD(g.receipt_no, 6, 0),
							IFNULL(g.sub_number,"")) END),"") as grn_no,
							IFNULL(DATE(g.inward_date), "") AS grn_date, g.invno as party_inv_no,
							IFNULL(DATE(g.inv_date), "") as party_inv_date, p.party_name as party_name,
							CONCAT(IFNULL(CONCAT(m.name , "-" , m.drawing_no),m.name),  
							IF(im.is_faulty = 1, ' (FAULTY)', '')) AS material_name, um.unit_name AS unit, 
							SUM(im.acc_qty) AS received, 0 AS delivered, "" as dc_code, "" as dc_date, g.inward_date as order_date,
							m.makes_json as make_name
							FROM grn_material AS im JOIN grn AS g ON im.grnNumber = g.grn_no AND 
							im.enterprise_id = g.enterprise_id LEFT JOIN materials AS m ON im.item_id = m.id 
							AND im.enterprise_id = m.enterprise_id JOIN unit_master AS um ON 
							um.unit_id = m.unit AND um.enterprise_id = m.enterprise_id JOIN party_master AS p ON 
							p.party_id = g.party_id AND p.enterprise_id = g.enterprise_id LEFT JOIN purchase_order 
							AS o ON o.id = im.po_no AND o.enterprise_id = im.enterprise_id WHERE g.rec_against = 'Job Work' 
							AND g.status > - 1 AND im.rec_grn_id is null AND g.enterprise_id = {enterprise_id} AND 
							g.inward_date BETWEEN '{since}' AND '{till}' {party_condition} {condition} GROUP BY g.grn_no ,party_name, material_name """ .format(
				since=since, till=till, enterprise_id=enterprise_id, party_condition=party_condition, condition=condition)

			invoice_query = """SELECT IFNULL(CONCAT(o.financial_year, '/JO/', LPAD(o.orderno, 6, 0), 
						IFNULL(o.sub_number, '')),'') AS oa_no, IFNULL(DATE(o.approved_on), '') AS oa_date, 
						"" AS grn_no,"" AS grn_date,"" AS party_inv_no,"" AS party_inv_date,p.party_name AS party_name,
						CONCAT(IFNULL(CONCAT(m.name, '-', m.drawing_no),m.name),
						IF(im.is_faulty = 1, ' (FAULTY)', '')) AS material_name, um.unit_name AS unit, 0 AS received,
						SUM(im.qty) AS delivered, IFNULL(i.invoice_code, CONCAT('PROFORMA#', LEFT(i.type, 1),
						IFNULL(CONVERT( LPAD(i.invoice_no, 6, '0') USING LATIN1),i.id))) AS dc_code,
						IFNULL(DATE(i.issued_on), '') AS dc_date, o.order_date as order_dater,m.makes_json as make_name FROM invoice_materials AS im
						JOIN  invoice AS i ON im.invoice_id = i.id AND im.enterprise_id = i.enterprise_id LEFT JOIN
						materials AS m ON im.item_id = m.id AND im.enterprise_id = m.enterprise_id 
						JOIN unit_master AS um ON um.unit_id = m.unit AND 
						um.enterprise_id = m.enterprise_id JOIN party_master AS p ON p.party_id = i.party_id AND 
						p.enterprise_id = i.enterprise_id LEFT JOIN purchase_order AS o ON o.id = i.job_po_id 
						WHERE i.type = 'JDC' AND i.status > -1  AND im.delivered_dc_id IS NULL AND im.enterprise_id = {enterprise_id} 
						AND i.issued_on BETWEEN '{since}' AND '{till}' {party_condition} {invoice_condition} GROUP BY o.id ,
						i.id, party_name ,material_name """ .format(
					since=since, till=till, enterprise_id=enterprise_id, party_condition=party_condition,
					invoice_condition=invoice_condition)

			job_in_query = """ SELECT * from ( {received_query} UNION {invoice_query}) as job_out_report order by order_date""" \
				.format(received_query=received_query, invoice_query=invoice_query)
			result = executeQuery(job_in_query, as_dict=True)
			issue_report_list = []
			for item in result:
				make_name = ""
				if item['make_name']:
					make_name = helper.constructDifferentMakeName(item['make_name'])
					if make_name:
						make_name = " [" + make_name + "]"
					item['material_name'] = item['material_name'] + make_name

				issue_report_list.append({
					'oa_no': item['oa_no'], 'oa_date': item['oa_date'], 'grn_no': item['grn_no'], 'grn_date': item['grn_date'], 'party_inv_no': item['party_inv_no'],
					'party_inv_date': item['party_inv_date'], 'party_name': item['party_name'], 'material_name': item['material_name'], 'unit': item['unit'],
					'received': item['received'], 'issued': item['delivered'], 'dc_no': item['dc_code'], 'dc_date': item['dc_date']})
			return issue_report_list
		except Exception as e:
			raise e

	def loadPartyOaFinancialYears(self, enterprise_id=None, party_id=None, oa_type=None, receipt_no=None):
		"""

		:param enterprise_id:
		:param party_id:
		:param oa_type:
		:param receipt_no:
		:return:
		"""
		financial_years = []
		try:
			q1 = self.stores_dao.db_session.query(OA.financial_year).join(
				ReceiptMaterial, and_(OA.id == ReceiptMaterial.oa_id)).filter(
				ReceiptMaterial.receipt_no == receipt_no,
				ReceiptMaterial.enterprise_id == enterprise_id).group_by(OA.financial_year)
			selected_financial_years = []
			for y in q1.all():
				selected_financial_years.append(y.financial_year)
			for f in self.stores_dao.db_session.query(OA.financial_year).filter(
					OA.enterprise_id == enterprise_id, OA.party_id == party_id, OA.type == oa_type,
					OA.status == OA.STATUS_APPROVED).group_by(
				OA.financial_year).order_by(OA.financial_year.desc()):
				if f.financial_year.strip() != "":
					financial_years.append(
						{"f_year": f.financial_year, "selected": f.financial_year in selected_financial_years})
			logger.info("%s" % financial_years)
		except Exception as e:
			logger.exception(e.message)
			raise e
		return financial_years

	def loadPendingJobOaNumbers(self, enterprise_id=None, party_id=None, receipt_no=None, financial_years=None):
		"""
		Loading OA number list of selected
		:param enterprise_id:
		:param party_id:
		:param receipt_no:
		:param financial_years:
		:return:
		"""
		oa_numbers = {}
		try:
			logger.info("Loading OA List for %s" % [enterprise_id, party_id, receipt_no])
			q1 = self.stores_dao.db_session.query(ReceiptMaterial.oa_id).filter(
				ReceiptMaterial.receipt_no == receipt_no,
				ReceiptMaterial.enterprise_id == enterprise_id).group_by(ReceiptMaterial.oa_id)
			selected_oa_list = q1.all()
			oas = self.stores_dao.db_session.query(
				OA.id.label('id'), OA.financial_year.label('financial_year'), OA.type.label('type'),
				OA.oa_no.label('oa_no'), OA.sub_number.label('sub_number'), OAParticulars.quantity.label('qty'),
				func.sum(InvoiceMaterial.quantity).label('inv_qty'), Invoice.status.label('inv_status')).join(
				OAParticulars, and_(OAParticulars.oa_id == OA.id)).outerjoin(
				InvoiceMaterial, and_(
					InvoiceMaterial.oa_no == OA.id,
					InvoiceMaterial.receipt_no != receipt_no if receipt_no else "")).outerjoin(
				Invoice, and_(
					Invoice.status >= Invoice.STATUS_DRAFT, Invoice.id == InvoiceMaterial.invoice_id,
					Invoice.enterprise_id == enterprise_id)).filter(
				OA.enterprise_id == enterprise_id, OA.party_id == party_id, OA.status == OA.STATUS_APPROVED,
				OA.type == "Job").group_by(OA.id).having(
				func.IF(and_(Invoice.status >= 0), func.ifnull(func.sum(InvoiceMaterial.quantity), 0) < func.sum(OAParticulars.quantity), True))
			if financial_years:
				oas.filter(OA.financial_year.in_(financial_years))
			for oa in oas.all():
				code = OA.generateInternalCode(
						financial_year=oa.financial_year, oa_id=oa.id, type=oa.type, oa_no=oa.oa_no,
						sub_number=oa.sub_number)
				oa_numbers[oa.id] = {"id": oa.id, "code": code, "selected": len(selected_oa_list) > 0 and bool(
					[True for item in selected_oa_list if item[0] == oa.id])}

		except Exception as e:
			raise e
		return oa_numbers.values()

	def getMaterialUsageReport(self, receipt_no=None):
		"""

		:param receipt_no:
		:return:
		"""

		try:
			return []
		except Exception as e:
			logger.exception(e.message)
			raise

	def getGoodsReturnedStatus(self, receipt_no=None):
		"""

		:param receipt_no:
		:return:
		"""
		try:
			query = """(SELECT m.drawing_no as drawing_no, im.make_id as make_id, im.is_faulty as is_faulty,
				sum(im.qty) as quantity, IFNULL(im.alternate_unit_id, 0) as alternate_unit_id,im.enterprise_id,
				im.item_id as item_id
				FROM invoice_materials as im 
				JOIN invoice as i ON i.id = im.invoice_id AND i.status >= 0
				JOIN materials as m ON m.id = im.item_id AND m.enterprise_id = im.enterprise_id 
				WHERE im.grn_id = {receipt_no} group by im.item_id, im.make_id, im.is_faulty)				
			""".format(receipt_no=receipt_no)
			materials = executeQuery(query, as_dict=True)
			material_list = []
			for material in materials:
				if material['alternate_unit_id'] and material['alternate_unit_id'] != 0:
					scale_factor = helper.getScaleFactor(
						enterprise_id=material['enterprise_id'], item_id=material['item_id'],
						alternate_unit_id=material['alternate_unit_id'])
					if scale_factor:
						material['quantity'] = Decimal(material['quantity']) / Decimal(scale_factor)

				material_list.append(material)
			return material_list
		except Exception as e:
			logger.exception(e.message)
			raise e

	def saveGrnDetails(
			self, grn_header=None, grn_no=None, user=None, enterprise_id=None, home_currency_id=None,
			grn_material=None, grn_taxes=None, grn_tags=None, grn_against_issue_no=None, is_super_edit=None,
			oa_code=None, username=None, user_id=None, custom_message=None, validate_invoice_no=False,
			fy_start_day=None, documents=None, grn_save_approve_status=None):
		db_session = self.stores_dao.db_session
		response_message = []
		items_before_update = []
		try:
			grn_header['invoice_no'] = grn_header['invoice_no'].strip()
			if grn_header['received_against'] not in ("Others", "Issues") and grn_header['invoice_no'] != "" and \
					validate_invoice_no:
				invoice_receipt = self.getReceiptForPurchaseInvoice(
					invoice_no=grn_header['invoice_no'], current_grn_no=grn_no,
					invoice_date=convertStringToDate(grn_header['invoice_date']),
					fy_start_day=fy_start_day, supplier_id=grn_header['supplier_id'], enterprise_id=enterprise_id,
					invoice_type=grn_header['invoice_type'])
				supplier = executeQuery("SELECT party_name FROM party_master where party_id ='%s' and enterprise_id='%s'" % (
					grn_header['supplier_id'], enterprise_id))
				supp_name = supplier[0][0]
				if invoice_receipt:
					custom_message += """<BR/>A Purchase Invoice with Invoice No <b>%s</b> has already been received from 
						<b>%s</b> during the Financial Year <b>%s</b> Kindly check & correct either of the Invoices No to save the GRN.
						""" % (
						grn_header['invoice_no'], supp_name, getFinancialYear(
							convertStringToDate(grn_header['invoice_date']), fy_start_day=fy_start_day))
					response_message.append(custom_message)
					response_message.append("Hard_warning")
					raise Exception(response_message)
				else:
					invoice_receipt = self.getReceiptForPurchaseInvoice(
						invoice_no=grn_header['invoice_no'], current_grn_no=grn_no,
						invoice_date=convertStringToDate(grn_header['invoice_date']), fy_start_day=fy_start_day,
						supplier_id=grn_header['supplier_id'], enterprise_id=enterprise_id,
						invoice_type=grn_header['invoice_type'], consider_date_validation=False)
					if invoice_receipt:
						custom_message += """<BR/>A Purchase Invoice with Invoice No <b>%s</b> has already been received from 
						<b>%s</b> during the Financial Year <b>%s</b><BR/>Do you want to continue? 
						""" % (
							grn_header['invoice_no'], supp_name, getFinancialYear(
								convertStringToDate(grn_header['invoice_date']), fy_start_day=fy_start_day))
						response_message.append(custom_message)
						response_message.append("Soft_warning")
						raise Exception(response_message)
			remarks = grn_header['remarks'] if 'remarks' in grn_header and grn_header['remarks'] != "" else None
			grn_header["user_id"] = user.user_id
			grn_header["enterprise_id"] = enterprise_id
			grn_header["username"] = ("%s" % username).capitalize()
			if int(grn_header['currency_id']) == home_currency_id:
				grn_header['currency_conversion_rate'] = "1"
			grn_header['goods_already_received'] = 1 if grn_header['goods_already_received'] else 0
			# Save receipt process
			db_session.begin(subtransactions=True)
			receipt = self.stores_dao.getReceipt(enterprise_id=enterprise_id, receipt_no=grn_no)
			if receipt and receipt.status in (
					receipt.STATUS_APPROVED, receipt.STATUS_ICD_VERIFIED, receipt.STATUS_ICD_CHECKED,
					receipt.STATUS_GRN_RETURNED, receipt.STATUS_ICD_RETURNED) and not receipt.goods_already_received:
				for item in receipt.items:
					items_before_update.append(copy.deepcopy(item))
			if receipt is None:
				receipt = Receipt(enterprise_id=enterprise_id)
				receipt.received_by = ("%s" % username).capitalize()
			[db_session.delete(tag) for tag in receipt.tags]
			receipt = self.__copyReceiptHeaderDataToEntity(receipt_header_data=grn_header, entity=receipt)
			receipt.items = self.__extractReceiptMaterialsFromData(
				receipt_material_data=grn_material, entity=receipt, enterprise_id=enterprise_id, location_id=grn_header['location_id'])
			receipt.taxes = self.__extractReceiptTaxFromData(
				receipt_taxes_data=grn_taxes, entity=receipt, enterprise_id=enterprise_id)
			receipt.tags = getEntityTagMaps(
				enterprise_id=enterprise_id, tags=grn_tags, tag_map_class=ReceiptTag,
				db_session=self.stores_dao.db_session)
			modifying_user = getUser(enterprise_id=enterprise_id, user_id=user_id)
			if remarks:
				if len(grn_header['remarks']) > 0:
					receipt.updateRemarks(remarks=remarks, user=modifying_user)
			if len(grn_against_issue_no) > 0:
				receipt.issue_map = self.__extractReceiptIssueMapFromData(
					receipt_issue_map_datas=grn_against_issue_no, entity=receipt, enterprise_id=enterprise_id)
			if grn_save_approve_status == '1':
				executeQuery("UPDATE grn SET status = 1 WHERE grn_no = " + grn_no + "")

			if receipt.receipt_no is None or receipt.receipt_no == '':
				receipt.receipt_date = datetime.now()
			if is_super_edit is True:
				receipt.super_modified_by = user_id
				receipt.super_modified_on = datetime.now()
			receipt.last_modified_by = user_id
			receipt.last_modified_on = datetime.now()
			if documents and documents != "":
				receipt.documents = documents
			db_session.add(receipt)
			db_session.commit()
			db_session.refresh(receipt)

			grn_module = "GRN"
			if receipt.received_against == "Issues":
				grn_module = "IRN"
			elif receipt.received_against == "Sales Return":
				grn_module = "SR"
			custom_message += "{module} No: <b>{code}</b> has been saved successfully with OA <b>{oa_code}</b>".format(
				module=grn_module, code=receipt.getCode(),
				oa_code=oa_code) if oa_code is not None else "{module} No: <b>{code}</b> has been saved successfully".format(
				module=grn_module, code=receipt.getCode())
			if not grn_no:
				self.notifyReceiptCount(
					enterprise_id=enterprise_id, sender_id=user_id, received_against=receipt.received_against)
			if receipt.status in (
					receipt.STATUS_APPROVED, receipt.STATUS_ICD_VERIFIED, receipt.STATUS_ICD_CHECKED,
					receipt.STATUS_GRN_RETURNED, receipt.STATUS_ICD_RETURNED) and not receipt.goods_already_received:
				old = [{"item_id": i.item_id, "quantity": float(i.accepted_qty), "is_faulty": i.is_faulty,
						"enterprise_id": i.enterprise_id} for i in items_before_update]
				new = [{"item_id": i.item_id, "quantity": float(i.accepted_qty), "is_faulty": i.is_faulty,
						"enterprise_id": i.enterprise_id} for i in receipt.items]

				for old_item, new_item in zip_longest(old, new, fillvalue=0):
					if old_item != 0 and old_item["quantity"] != new_item["quantity"]:

						if old_item["quantity"] < new_item["quantity"]:
							qty = abs(old_item["quantity"] - new_item["quantity"])
							update_closing_stock.delay(enterprise_id=enterprise_id, item_id=new_item["item_id"],
													   is_sales=False, quantity=float(qty),
													   location_id=receipt.location_id, is_faulty=0)
						else:
							qty = abs(old_item["quantity"] - new_item["quantity"])
							update_closing_stock.delay(enterprise_id=enterprise_id, item_id=new_item["item_id"],
													   is_sales=True, quantity=float(qty),
													   location_id=receipt.location_id, is_faulty=0)
					elif old_item == 0 and new_item:
						update_closing_stock.delay(enterprise_id=enterprise_id, item_id=new_item["item_id"],
												   is_sales=False, quantity=float(new_item["quantity"]),
												   location_id=receipt.location_id, is_faulty=0)
		except Exception as e:
			db_session.rollback()
			raise e
		return custom_message, receipt.receipt_no

	def __copyReceiptHeaderDataToEntity(self, receipt_header_data=None, entity=None):
		try:
			if entity.receipt_no is None or entity.receipt_no == '':
				copyDictToDict(source=receipt_header_data, destination=entity.__dict__, exclude_keys=RECEIPT_EXCLUDE_FIELD_LIST)
			else:
				logger.info("Grn Header:%s" % receipt_header_data['location_id'])
				entity.enterprise_id = receipt_header_data['enterprise_id']
				entity.received_against = receipt_header_data['received_against']
				entity.supplier_id = receipt_header_data['supplier_id']
				entity.project_code = receipt_header_data['project_code']
				entity.invoice_type = receipt_header_data['invoice_type']
				entity.invoice_no = receipt_header_data['invoice_no']
				entity.invoice_date = receipt_header_data['invoice_date']
				entity.purchase_account_id = receipt_header_data[
					'purchase_account_id'] if 'purchase_account_id' in receipt_header_data else None
				entity.packing_charges = receipt_header_data['packing_charges']
				entity.transport_charges = receipt_header_data['transport_charges']
				entity.inward_no = receipt_header_data['inward_no']
				entity.inward_date = receipt_header_data['inward_date']
				entity.inspected_by = receipt_header_data['inspected_by']
				entity.rejection_remarks = receipt_header_data['rejection_remarks']
				entity.username = receipt_header_data['username']
				entity.other_charges = receipt_header_data['other_charges']
				entity.invoice_value = receipt_header_data['invoice_value']
				entity.duty_passed = receipt_header_data['duty_passed']
				entity.currency_id = receipt_header_data['currency_id']
				entity.currency_conversion_rate = receipt_header_data['currency_conversion_rate']
				entity.user_id = receipt_header_data['user_id']
				entity.round_off = receipt_header_data['round_off']
				entity.goods_already_received = receipt_header_data['goods_already_received']
				entity.matrecthrough = receipt_header_data['matrecthrough']
				entity.ecommerce_gstin = receipt_header_data['ecommerce_gstin']
				entity.location_id = receipt_header_data['location_id']

			if not ('%s' % entity.supplier_id).isdigit():
				entity.supplier_id = None
		except Exception as e:
			logger.exception("Error in Copy json to Entity Part - %s" % e.message)
			raise e
		return entity

	def __extractReceiptMaterialsFromData(self, receipt_material_data=None, entity=None, enterprise_id=None, location_id=None):
		receipt_items = []
		try:
			for item_data in receipt_material_data:
				item_id = item_data['item_id']
				make_id = item_data['make_id']
				is_faulty = item_data['is_faulty'] if 'is_faulty' in item_data and item_data['is_faulty'] != "" else 0
				oa_id = item_data['oa_id'] if 'oa_id' in item_data and item_data['oa_id'] != "" else None
				po_id = item_data['po_id'] if 'po_id' in item_data and item_data['po_id'] != "" else None
				dc_id = item_data['dc_id'] if 'dc_id' in item_data and item_data['dc_id'] != "" and item_data['dc_id'] != '0' else None
				received_grn_id = item_data['rec_grn_id'] if 'rec_grn_id' in item_data and item_data['rec_grn_id'] != "" else None
				alternate_unit_id = item_data['alternate_unit_id'] if 'alternate_unit_id' in item_data and item_data['alternate_unit_id'] != "" and item_data['alternate_unit_id'] != '0' else None
				receipt_item = self.stores_dao.getReceiptMaterial(
					receipt_no=entity.receipt_no, item_id=item_id, enterprise_id=enterprise_id, make_id=make_id,
					is_faulty=is_faulty, oa_id=oa_id, po_id=po_id, dc_id=dc_id, received_grn_id=received_grn_id)
				if not receipt_item:
					receipt_item = ReceiptMaterial(
						item_id=item_id, enterprise_id=enterprise_id, make_id=make_id,
						is_faulty=is_faulty, po_no=po_id, oa_id=oa_id, dc_id=dc_id)
					receipt_item.receipt = entity
				receipt_item.quantity = item_data['quantity']
				receipt_item.hsn_code = item_data['hsn_code']
				receipt_item.accepted_qty = item_data['accepted_qty']
				receipt_item.received_qty = item_data['received_qty']
				receipt_item.rate = item_data['price'] if 'price' in item_data and item_data['price'] != "" else 0
				receipt_item.received_grn_id = received_grn_id
				if alternate_unit_id:
					scale_factor = helper.getScaleFactor(
						enterprise_id=enterprise_id, item_id=item_id, alternate_unit_id=alternate_unit_id)
					if scale_factor:
						receipt_item.quantity = Decimal(item_data['quantity']) * Decimal(scale_factor)
						receipt_item.accepted_qty = Decimal(item_data['accepted_qty']) * Decimal(scale_factor)
						receipt_item.received_qty = Decimal(item_data['received_qty']) * Decimal(scale_factor)
						receipt_item.rate = Decimal(item_data['price']) / Decimal(scale_factor) if 'price' in item_data and item_data['price'] != "" else 0
						receipt_item.alternate_unit_id = alternate_unit_id
				else:
					receipt_item.alternate_unit_id = None
				receipt_item.discount = item_data['discount'] if 'discount' in item_data and item_data['discount'] != "" else 0
				receipt_item.po_no = po_id
				receipt_item.dc_id = dc_id
				receipt_item.oa_id = oa_id
				logger.info("Grn items:%s" % location_id)
				receipt_item.location_id = location_id
				if 'taxes' in item_data:
					if len(item_data['taxes']) > 0:
						receipt_item.taxes = self.__extractReceiptMaterialTaxesFromData(
							receipt_item_taxes=item_data['taxes'], receipt_item=receipt_item)
					else:
						receipt_item.taxes = []
				if 'rejection_profiles' in item_data:
					if len(item_data['rejection_profiles']) > 0:
						receipt_item.rejections = self.__extractReceiptMaterialRejectionFromData(
							receipt_item_rejections=item_data['rejection_profiles'], receipt_item=receipt_item)
				if 'inspection_log' in item_data:
					receipt_item.inspection_log = json.loads(
						item_data['inspection_log'] if item_data['inspection_log'] and len(
							item_data['inspection_log']) > 0 else None)
				receipt_items.append(receipt_item)
		except Exception as e:
			logger.exception("Receipt Material Extraction from JSON failed...\n %s" % e.message)
			raise e
		return receipt_items

	def __extractReceiptMaterialTaxesFromData(self, receipt_item_taxes=None, receipt_item=None):
		receipt_material_taxes = []
		try:
			for receipt_item_tax in receipt_item_taxes:
				tax_code = receipt_item_tax
				if receipt_item_tax.strip() == "":
					continue
				receipt_material_tax = self.stores_dao.getReceiptMaterialTax(
					receipt_no=receipt_item.receipt_no, item_id=receipt_item.item_id, enterprise_id=receipt_item.enterprise_id,
					tax_code=tax_code, make_id=receipt_item.make_id, is_faulty=receipt_item.is_faulty, oa_id=receipt_item.oa_id,
					dc_id=receipt_item.dc_id, po_no=receipt_item.po_no, received_grn_id=receipt_item.received_grn_id)
				if not receipt_material_tax:
					receipt_material_tax = ReceiptMaterialTax(
						receipt_no=receipt_item.receipt_no, item_id=receipt_item.item_id, enterprise_id=receipt_item.enterprise_id,
						tax_code=tax_code, make_id=receipt_item.make_id, is_faulty=receipt_item.is_faulty, oa_id=receipt_item.oa_id,
						dc_id=receipt_item.dc_id, po_id=receipt_item.po_no, received_grn_id=receipt_item.received_grn_id)
				receipt_material_taxes.append(receipt_material_tax)
		except Exception as e:
			logger.exception("Receipt Material Taxes Extraction from JSON failed...\n %s" % e.message)
			raise e
		return receipt_material_taxes

	def __extractReceiptMaterialRejectionFromData(self, receipt_item_rejections=None, receipt_item=None):
		receipt_material_rejections = []
		try:
			for receipt_item_rejection in json.loads(receipt_item_rejections):
				rejection_reason = receipt_item_rejection['reason']
				rejection_debit = receipt_item_rejection['debit']
				rejection_quantity = receipt_item_rejection['quantity']
				receipt_material_rejection = self.stores_dao.getReceiptMaterialRejection(
					receipt_no=receipt_item.receipt_no, item_id=receipt_item.item_id, enterprise_id=receipt_item.enterprise_id,
					reason=rejection_reason, make_id=receipt_item.make_id, is_faulty=receipt_item.is_faulty,
					oa_id=receipt_item.oa_id, dc_id=receipt_item.dc_id, po_no=receipt_item.po_no)
				if not receipt_material_rejection:
					receipt_material_rejection = ReceiptMaterialRejection(
						grn_id=receipt_item.receipt_no, item_id=receipt_item.item_id, enterprise_id=receipt_item.enterprise_id,
						make_id=receipt_item.make_id, is_faulty=receipt_item.is_faulty, oa_id=receipt_item.oa_id,
						dc_id=receipt_item.dc_id, po_no=receipt_item.po_no, rec_grn_id=receipt_item.received_grn_id)
				receipt_material_rejection.reason = rejection_reason
				receipt_material_rejection.debit = rejection_debit
				receipt_material_rejection.quantity = rejection_quantity
				receipt_material_rejections.append(receipt_material_rejection)
		except Exception as e:
			logger.exception("Receipt Material Rejection Extraction from JSON failed...\n %s" % e.message)
			raise e
		return receipt_material_rejections

	def __extractReceiptTaxFromData(self, receipt_taxes_data=None, entity=None, enterprise_id=None):
		receipt_tax_list = []
		try:
			for receipt_tax_data in receipt_taxes_data:
				tax_code = receipt_tax_data['tax_code']
				receipt_tax = self.stores_dao.getReceiptTax(
					receipt_no=entity.receipt_no, tax_code=tax_code, enterprise_id=enterprise_id)
				if not receipt_tax:
					receipt_tax = ReceiptTax(receipt_no=entity.receipt_no, tax_code=tax_code, enterprise_id=enterprise_id)
				receipt_tax_list.append(receipt_tax)
			logger.debug('Receipt Tax constructed: %s' % receipt_tax_list)
		except Exception as e:
			logger.exception("Receipt Tax Extraction from JSON failed...\n %s" % e.message)
			raise e
		return receipt_tax_list

	def __extractReceiptIssueMapFromData(self, receipt_issue_map_datas=None, entity=None, enterprise_id=None):
		receipt_issue_map_list = []
		try:
			for receipt_issue_map_data in receipt_issue_map_datas:
				issue_no = receipt_issue_map_data
				receipt_issue_map = self.stores_dao.getReceiptIssueMap(
					receipt_no=entity.receipt_no, issue_no=issue_no, enterprise_id=enterprise_id)
				if not receipt_issue_map:
					receipt_issue_map = ReceiptIssueMap(receipt_no=entity.receipt_no, issue_no=issue_no, enterprise_id=enterprise_id)
				receipt_issue_map_list.append(receipt_issue_map)
			logger.debug('Receipt Issue Map constructed: %s' % receipt_issue_map_list)
		except Exception as e:
			logger.exception("Receipt Issue Map Extraction from JSON failed...\n %s" % e.message)
			raise e
		return receipt_issue_map_list
	
	def getReceiptForPurchaseInvoice(
			self, invoice_no, invoice_date, fy_start_day='01/04', supplier_id=None, current_grn_no=None, enterprise_id=None,
			invoice_type=None, consider_date_validation=True):
		"""
		Verifies if an Invoice, from a particular Supplier, with the given Invoice number, whose invoice date falls within
		a particular Financial year has already been associated with another Receipt.

		:param invoice_no:
		:param invoice_date:
		:param fy_start_day:
		:param supplier_id:
		:param current_grn_no:  current receipt's invoice association needs to be excluded, esp while updating.
		:param enterprise_id:
		:param invoice_type:
		:param consider_date_validation:
		:return: Boolean value denoting if the invoice is a duplicate or not
		"""
		try:
			fy_date_range = getFYDateRange(invoice_date, fy_start_day)
			query = self.stores_dao.db_session.query(Receipt)
			filters = [
				func.lower(Receipt.invoice_no) == func.lower(invoice_no), Receipt.enterprise_id == enterprise_id,
				Receipt.status > -1, Receipt.invoice_date >= fy_date_range[0],
				Receipt.invoice_date <= fy_date_range[1], Receipt.supplier_id == supplier_id,
				Receipt.invoice_type == invoice_type]
			if current_grn_no:
				filters.append(Receipt.receipt_no != current_grn_no)
			if consider_date_validation:
				filters.append(Receipt.invoice_date == invoice_date)
			receipts_with_invoice = query.filter(*filters).first()
		except Exception as e:
			logger.exception("Receipt for purchase invoice details are failed...\n %s" % e.message)
			raise e
		return receipts_with_invoice

	def getNoteForPurchaseInvoice(
			self, invoice_no, invoice_date, fy_start_day='01/04', supplier_id=None, current_note_no=None, enterprise_id=None,
			consider_date_validation=True):
		"""
		Verifies if an Invoice, from a particular Supplier, with the given Invoice number, whose invoice date falls within
		a particular Financial year has already been associated with another Receipt.

		:param invoice_no:
		:param invoice_date:
		:param fy_start_day:
		:param supplier_id:
		:param current_note_no:  current receipt's invoice association needs to be excluded, esp while updating.
		:param enterprise_id:
		:param consider_date_validation:
		:return: Boolean value denoting if the invoice is a duplicate or not
		"""
		try:
			fy_date_range = getFYDateRange(invoice_date, fy_start_day)
			query = self.stores_dao.db_session.query(CreditDebitNote)
			filters = [
				func.lower(CreditDebitNote.inv_no) == func.lower(invoice_no), CreditDebitNote.enterprise_id == enterprise_id,
				CreditDebitNote.status > -1, CreditDebitNote.inv_date >= fy_date_range[0],
				CreditDebitNote.inv_date <= fy_date_range[1], CreditDebitNote.party_id == supplier_id]
			if current_note_no:
				filters.append(CreditDebitNote.id != current_note_no)
			if consider_date_validation:
				filters.append(CreditDebitNote.inv_date == invoice_date)
			note_with_invoice = query.filter(*filters).first()
		except Exception as e:
			logger.exception("Note for purchase invoice details are failed...\n %s" % e.message)
			raise e
		return note_with_invoice

	def getInvoiceQty(
			self, received_against=None, party_id=None, receipt_no=None, item_id=None, make_id=1, is_faulty=0,
			po_no=None, enterprise_id=None, dc_id=None, current_grn_no=None, edit_grn_no=None):
		"""

		:param received_against:
		:param party_id:
		:param receipt_no: rec_grn_id
		:param item_id:
		:param make_id:
		:param is_faulty:
		:param po_no:
		:param enterprise_id:
		:param dc_id:
		:param current_grn_no: for which the materials need to be fetched in edit page.
		:param edit_grn_no: grn number for which goods already supplied has been checked.
		:return:
		"""
		# TODO Correct query to make good performance
		try:
			po_no_constrain = """AND a.po_no ={po_no}""".format(po_no=po_no) if po_no is not None else ''
			dc_id_constrain = ""
			dc_id_sum_constrain = ""
			if dc_id:
				dc_id_sum_constrain = """AND gm.dc_id = {dc_id}""".format(dc_id=dc_id) if dc_id is not None else ''
				dc_id_constrain = """AND a.dc_id = {dc_id}""".format(dc_id=dc_id) if dc_id is not None else ''

			grn_condition = """g.grn_no = gm.grnNumber AND gm.enterprise_id = {enterprise_id} AND gm.item_id='{item_id}' 
							AND gm.make_id='{make_id}' AND gm.is_faulty='{is_faulty}' {dc_id_sum_constrain} AND g.status > -1 """.format(
				enterprise_id=enterprise_id, item_id=item_id, make_id=make_id, is_faulty=is_faulty,
				dc_id_sum_constrain=dc_id_sum_constrain)

			if receipt_no is not None and receipt_no != "" and (current_grn_no is None or current_grn_no == ""):
				grn_condition += """AND gm.rec_grn_id = {receipt_no} AND gm.grnNumber != {receipt_no} AND gm.po_no =={}""".format(
					receipt_no=receipt_no)
			elif receipt_no is not None and receipt_no != "" and current_grn_no is not None and current_grn_no != "":
				grn_condition += """AND gm.rec_grn_id = {receipt_no} AND gm.grnNumber != {current_grn_no} """.format(
					receipt_no=receipt_no, current_grn_no=current_grn_no)
			elif current_grn_no is not None and current_grn_no != "" and edit_grn_no is not None and edit_grn_no != "":
				grn_condition += """AND gm.rec_grn_id = {current_grn_no} AND gm.grnNumber != {edit_grn_no} """.format(
					current_grn_no=current_grn_no, edit_grn_no=edit_grn_no)
			elif current_grn_no is not None and current_grn_no != "" and (edit_grn_no is None or edit_grn_no == ""):
				grn_condition += """AND gm.rec_grn_id = {current_grn_no} AND gm.grnNumber != {current_grn_no} """.format(
					current_grn_no=current_grn_no)

			if po_no is not None and po_no != "":
				grn_condition += """AND gm.po_no ={po_no}""".format(po_no=po_no)
			if receipt_no is not None and receipt_no != "":
				grn_code_condition = """AND a.grnNumber = {receipt_no} AND r.grn_no = {receipt_no}""".format(receipt_no=receipt_no)
				grn_common_condition = """AND a.grnNumber={receipt_no}""".format(receipt_no=receipt_no)
			elif current_grn_no is not None and current_grn_no != "":
				grn_code_condition = """AND a.grnNumber = {current_grn_no} AND r.grn_no = {current_grn_no}""".format(
					current_grn_no=current_grn_no)
				grn_common_condition = """AND a.grnNumber={current_grn_no}""".format(current_grn_no=current_grn_no)

			invoice_by_enterprise_project_supplier = """
											SELECT a.dc_qty AS dc_qty, a.rec_qty AS rec_qty, a.acc_qty AS acc_qty,
											IFNULL((SELECT SUM(dc_qty) FROM grn_material as gm, grn as g WHERE {grn_condition}), 0) AS tot_dc_qty, 
											IFNULL((SELECT SUM(rec_qty) FROM grn_material as gm, grn as g WHERE {grn_condition}), 0) AS tot_rec_qty,
											IFNULL((SELECT SUM(acc_qty) FROM grn_material as gm, grn as g WHERE {grn_condition}), 0) AS tot_acc_qty,
											(SELECT CONCAT(r.financial_year, '/GRN/', LPAD(r.receipt_no, 6, 0), IFNULL(r.sub_number,'')) 
												FROM grn as r, grn_material as a 
												WHERE r.party_id = {party_id} AND 
													r.rec_against = '{received_against}' AND r.status != 0 {po_no} {grn_code_condition} GROUP BY r.grn_no) AS grn_code
											FROM grn AS r, grn_material as a
												WHERE r.party_id = {party_id} AND r.rec_against = '{received_against}' AND r.status > -1 
												{po_no} AND r.enterprise_id = {enterprise_id} AND a.grnNumber = r.grn_no 
												AND a.item_id = '{item_id}' AND a.make_id = '{make_id}' AND a.is_faulty = '{is_faulty}' 
												{dc_id_constrain} {grn_common_condition}"""

			return executeQuery(invoice_by_enterprise_project_supplier.format(
				grn_condition=grn_condition, party_id=party_id, received_against=received_against,
				po_no=po_no_constrain, grn_code_condition=grn_code_condition, enterprise_id=enterprise_id,
				item_id=item_id, make_id=make_id, is_faulty=is_faulty, dc_id_constrain=dc_id_constrain,
				grn_common_condition=grn_common_condition))
		except Exception as e:
			logger.exception("Error in getInvoiceQty: %s" % str(e))
			raise e