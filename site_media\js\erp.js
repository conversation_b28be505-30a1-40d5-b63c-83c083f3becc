function deleteMaterial(materialName) {
    var confirmDelete = confirm('Deleting this material will delete all related entries across Catalogues, Indents' +
        'and PO\'s. \n\nDo you still want to delete the Material: ' + materialName + ' ?');
    if (confirmDelete == true)
        clickButton('deleteMaterial_' + materialName);
}

$(function () {

    $(document).ready(function () {
        var url = window.location.href;
        url = url.substring(7)
        window.urlpath = url.substring(0, url.indexOf('/'));

        $("#search").keyup(function () {
            // Common Search functionality across Materials and Catalogue pages
            if ($(this).val() != "") {
                $("#searchResult tbody>tr").hide();
                $("#searchResult td:contains-ci('" + $(this).val() + "')").parent("tr").show();
            }
            else {
                $("#searchResult tbody>tr").show();
            }
        });

        if ($('#form_errors').val() != '' || ($('#formset_errors').val().search(/[a-z]/ig) >= 0)) {
            $('#error_messages').show();
            $('#error_messages').modal('show');
        }
        $('#error_close').click(function () {
            $('#error_messages').hide();
            $('#error_messages').modal('hide');
        });
    });
    $.extend($.expr[":"], {
        "contains-ci": function (elem, i, match, array) {
            return (elem.textContent || elem.innerText || $(elem).text() || "").toLowerCase().indexOf((match[3] || "").toLowerCase()) >= 0;
        }
    });
})

function changeImage(imgId, imgSrc) {
    var img = document.getElementById(imgId);
    img.src = imgSrc;
}