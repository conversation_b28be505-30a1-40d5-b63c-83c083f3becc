{% extends "purchase/sidebar.html" %}
{% block purchase_orders %}
<style xmlns="http://www.w3.org/1999/html">
#cattable {
    width: 550px;
    position: absolute;
    color: #000000;
    background-color: #FFFFFF;
    top: 50%;
    left: 50%;
    margin-top: 100px;
    margin-left: 100px;
}

#job_po_checkbox_div .custom-error-message {
	display: block;
}
.tr-alert-danger , .tr-alert-danger input, .tr-alert-danger select {
	color: #a94442 !important;
	background-color: #f2dede !important;
	border-color: #bbb !important;
}

.tr-alert-danger td {
	border: solid 1px #aaa !important;
}

.table.text_box_in_table .chosen-single {
	height: 26px;
	height: 26px;
    font-size: 13px;
    line-height: 24px;
    padding: 0 0 0 8px;
}

.table.text_box_in_table .chosen-container-single .chosen-single div b {
	margin-top: 2px;
}

.tab_quick_po .for_indent_po {
	display: none !important
}

.tab_indent_po .for_quick_po {
	display: none !important
}

.form-inline .chosen-container {
    display: inline-block;
    width: 60% !important;
}

.form-inline .fixed-width-medium {
    width: 60%;
}

.form-inline .form-group {
	margin-bottom: 10px;
}

.custom-table.custom-table-large td.quantity_unit_display {
	padding:  14px 4px 0 !important;
}

.custom-table.custom-table-large td.quantity_unit_display small{
	float: right;
}

#chooseIndent .dataTables_info, 
#chooseIndent #filter_textbox {
	margin-right: 0;
}

.create_po_container a.btn-new-item {
	box-shadow: -4px 4px 5px 0 rgba(60,64,67,0.302), 0 1px 3px 1px rgba(60,64,67,0.149);
}

.btn-new-item-label {
    color: #004195;
    padding: 6px !important;
}

.btn-new-item-label:after {
	content: '';
}

.btn-new-item-label.normal_po:after {
	content: '+';
	font-size: 24px;
}

.btn-new-item-label.quick_po:after {
	content: 'Quick';
}

.btn-new-item-label.po_via_indent:after {
	content: 'via Indent';
}

#chooseIndent .dataTables_paginate {
	margin-right: 0 !important;
}

.edit_delivery_schedule {
	position: absolute;
    right: 0px;
    /* margin-top: -10px; */
    cursor: pointer;
    margin: -4px 12px !important;
    padding: 8px !important;
}

.edit_delivery_schedule img{
	width: 24px;
    padding: 0px;
    border-radius: 50px;
}

.blurred-tr td {
	color: #ccc !important;
}

.active-tr td {
	color: #333 !important;
}

.active-tr .img-split,
.blurred-tr .img-split {
	cursor: not-allowed;
	opacity: 0.3;
}

.active-tr td {
	border-bottom: transparent solid 1px !important;
	font-size: 125%;
	color: #004195 !important;
}

.deliverydue-temporary-row td{
	border:  none;
}

.td_ordered_qty,
.td_balance_qty,
.tr-ordered-qty,
.tr-balance-qty,
.tr-uom,
.td-uom,
.th-sub-heading,
.td-sub-content {
	display: block;
	font-size: 75%;
	text-align: right;
	letter-spacing: 0.5px;
}

#po_materials_table tbody tr td {
	vertical-align: top !important;
}
.spq_icon{
	background:transparent;
}

.spq_icon:hover{
    background: #ccc;
    color: #000;
    border-radius:50px;
}

.job_order{
	border-bottom-left-radius: 50px;
    border-top-left-radius: 50px;
}

.purchase_order{
	border-bottom-right-radius: 50px;
    border-top-right-radius: 50px;
}

.unit_select_box{
    padding-right: 0px !important;
    padding-left: 0px !important;
    position: relative !important;
    margin-top: -26px !important;
    width: 50px !important;
    right: 0px !important;
    left: -15px;
    top: -8px;
}

.all_units_select_box{
	margin-top: 8px;
	margin-right: -15px;
}

#po_materials_table .item-for-goods:empty,
#po_materials_table .item-for-service:empty{
    display: none;
}

#change_log_modal .modal-body {
	overflow: auto;
}

.history-log-part {
	width: 100%;
	margin: 0;
	padding: 8px 15px;
	cursor: pointer;
	padding-left: 60px;
}
.history-log-part:last-child {
	border-bottom: none;
}
.history-log-part:hover {
	background: #e8f0fe;
}
.history-log-part:hover .history-log-date {
	font-weight: bold;
}
.history-log-date {
   font-style: italic;
}
.history-log-username {
	font-size: 15px;
	padding-top: 3px;
	font-weight: bold;
}

.history-log-content {
	margin-top: 8px;
}

.history-log-content ul {
	padding: 0px 10px 0px 35px;
}
.history-log-content li {
	margin-bottom: 10px;
}

.history-log-content li:last-child {
	margin-bottom: 0;
}

.history-log-part.active .history-log-date,
.history-log-part.active .history-log-username {
	color: #004195;
}

.history-log-part.active .history-log-date {
	text-align: right;
	display: block;
}

.history-log-part .fa-chevron-down:before {
	content: "\f078";
	color: #999;
}

.history-log-part.active .fa-chevron-down:before {
	content: "\f077";
	color: #999;
}

#change_log_modal .modal-body {
	overflow: auto;
}

ul.timeline {
	list-style-type: none;
	position: relative;
	padding: 0px;
}
ul.timeline:before {
	content: ' ';
	background: #d4d9df;
	display: inline-block;
	position: absolute;
	left: 29px;
	width: 2px;
	height: 94%;
	z-index: 400;
	margin-top: 12px;
}
ul.timeline > li {

}
ul.timeline > li:before {
	content: ' ';
	background: white;
	display: inline-block;
	position: absolute;
	border-radius: 50%;
	border: 3px solid #209be1;
	left: 23px;
	width: 14px;
	height: 14px;
	z-index: 400;
	margin-top: 4px;
}
ul.timeline > li:last-child:before {
	background: #209be1;
}

#cattable_3 tbody tr td .tree-view {
	position: relative;
}

#cattable_3 tbody tr td .tree-view:before {
	border-bottom: 1px solid #bbb;
	content: "";
	left: 6px;
	position: absolute;
	top: 6px;
	width: 1.5em;
}

#cattable_3 tbody tr td .tree-view:after {
	border-left: 1px solid #bbb;
	content: "";
	left: 6px;
	position: absolute;
	top: -25px;
	height: 4em;
}

#cattable_3 .fa-plus:before,
#cattable_3 .fa-minus:before {
	border: solid 1px #333;
	padding: 2px 3px 2px 3px;
	border-radius: 3px;
	font-size: 10px;
}

table {
    border-collapse: collapse;
}


tr.strikeout td:not(.bom-sno) {
    text-decoration: line-through !important;
    opacity: 0.5;
}




</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/po.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/mailer.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/make.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/tags.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/load_material.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/Component/projects.js?v={{ current_version }}"></script>


<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">{{template_title}}</span>
	</div>
	<div style="display:none">
		<form id="po_submit" method="post" action="/erp/purchase/po/">
			{%csrf_token%}
			<input type="hidden" value="{{ po.po_id.value }}" id="id_po-id" name="po_no"/>
			<input type="hidden" value="{{ type }}" id="id_edit_dc_type" name="edit_dc_type"/>
			<input type="submit" value="Edit" id="po_resubmit" hidden="hidden"/>
			<input type="hidden" value="{{ order_type }}" id="id_order_type" name="order_type"/>
		</form>
	</div>
	<div class="col-lg-12 remove-padding">
		<div class="page-heading_new">
			<input type="hidden" class="indent_access" id="indent_access" name="indent_access" value="{{ module_access.indent }}" />
			<input type="hidden" id="is_delivery_schedule" value="{{ enterprise_profile.is_delivery_schedule }}" />
			<input type="hidden" id="is_price_modification_disabled" value="{{ enterprise_profile.is_price_modification_disabled }}" />
			<input type="hidden" id="is_price_modification_disabled_quick_po" value="{{ enterprise_profile.is_price_modification_disabled_quick_po }}" />
			<span class="page_header" style="margin-left: 15px;"></span>
			{% if logged_in_user.is_super %}
				<a class="btn super_user_icon hide" onclick="EditPONumber();" style="" data-tooltip="tooltip" data-title="Super User" data-placement="right">
					<i class="fa fa-pencil"></i>
				</a>
				<div class="xsid_number_edit hide">
					<form class="form-inline" style="display: inline-block;" action="">
					    <div class="form-group">
					      	<input type="text" class="form-control" id="po_financial_year" name="po_financial_year" maxlength="5">
					    </div>
					    <div class="form-group">
						    <select class="form-control" name="po_type" id="po_type">
							    {% for type in po_type %}
							        <option value="{{type.0}}" data-val="{{type.0}}">&nbsp;{{type.0}}&nbsp;</option>
						        {% endfor %}
						    </select>
					    </div>
					    <div class="form-group">
					      <input type="text" id="po_number" name="po_number" class="form-control" maxlength="6" onfocus="setNumberRangeOnFocus(this,6,0,true)" style="width: 85px;">
					    </div>
					    <div class="form-group">
					      	<input type="text" class="form-control text-lowercase" onkeypress="validateStringOnKeyPress(this,event,'character');" onblur="validateStringOnBlur(this,event,'character');" id="po_number_division" name="po_number_division" maxlength="1" >
					    </div>
					    <div class="form-group super_edit_submit_icon">
					    	<span role='button' data-tooltip="tooltip" title="Save" class="super_edit_save" onclick="SavePONumber();"><i class="fa fa-check"  aria-hidden="true" style="color: green;"></i></span>
					    	<span role='button' data-tooltip="tooltip" title="Cancel" class="super_edit_cancel" onclick="DiscardEditPONumber();"><i class="fa fa-times" aria-hidden="true" style="color: red;"></i></span>
					    </div>
			  		</form>
			  		<div class="save_xsid_error_format hide">Required. Please check help tool for valid format.</div>
			  	</div>
			  	<div class="super_user_tool_tip hide"><span class="question-mark"></span></div>
				<!--<span class="super_user_tool_tip hide"><img src="/site_media/images/tooltip.png" style="vertical-align: top;" /></span>-->
			{%else%}
				<a class="btn super_user_icon hide" onclick="" style="">
						<i class="fa fa-pencil" style="color:#777;" data-tooltip="tooltip" data-placement="right" data-title="Only Super User can edit this field"></i>
				</a>
			{% endif %}
			{% if order_type == "po" %}
				<a href="/erp/purchase/po_list/" class="btn btn-add-new pull-right view_po" style="margin-right: 15px; margin-bottom: 15px;" data-tooltip="tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
			{% elif order_type == "jo" %}
				<a href="/erp/purchase/jo_list/" class="btn btn-add-new pull-right view_po" style="margin-right: 15px; margin-bottom: 15px;" data-tooltip="tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
			{% endif %}
			<span class="prev_next_container"></span>
			<div class="pull-right">
				<a href="#" id="dc_report_button" class="btn pull-right btn-warning-border hide" style="margin-right: 7px;"  onclick="javascript:showReport();">Items Delivered</a>
				<a href="#" id="dc_usage_button" class="btn pull-right btn-warning-border hide" style="margin-right: 7px;" onclick="javascript:showUsageReport();">Material Usage</a>
			</div>
		</div>
		<div class="tab-content">
			 <div id="dcReport" class="modal fade" role="dialog" >
			  <div class="modal-dialog modal-lg">
			    <div class="modal-content">
			      <div class="modal-header">
			        <button type="button" class="close" data-dismiss="modal">&times;</button>
			        <h4 class="modal-title">Materials Supplied</h4>
			      </div>
			      <div class="modal-body">
			        <input type="hidden" id="id_job_po" name="job_po_id" value="">
			        <table class="table table-bordered custom-table table-striped" id="dc_invoice_list">
			            <thead>
			                <tr>
			                    <th width="5%"> S.No. </th>
			                    <th width="12%"> DC ID</th>
			                    <th width="15%"> Item Details</th>
			                    <th width="15%"> Hsn code</th>
			                    <th width="39%"> Quantity</th>
			                    <th width="39%"> UOM</th>
			                </tr>
			            </thead>
			            <tbody>
			            </tbody>
			        </table>
			      </div>
				     <div class='modal-footer'>
					     <span class="pull-left nonStock-text">  <span class=" nonStock-text non_stock-flag"></span> - NON-STOCKABLE</span>
						 <span class="pull-left nonStock-text">  <span class=" nonStock-text service-item-flag"></span> - SERVICE</span>
					     <a class='btn btn-cancel' id='OACancel' data-dismiss='modal'>Close</a>
				     </div>
			    </div>
			  </div>
			</div>

			<div id="tab2" class="tab_indent_po">
				<div class="add_table">
					<form name="purchase_order" id="po_form">
						<input type="checkbox" id="dirt_flag" hidden="hidden">
						<div class="col-sm-12 add_table_content">
							<div class="hide">
								<div class="po_title_txt">
									<div hidden="hidden">
										<div class="col-sm-6 checkbox" >
											<input type="checkbox" class="chkcase" name="case" id="chknonstockable" value="" />
											<label for="chknonstockable">Non Stockable</label>
										</div>
									</div>
								</div>
								<div class="col-sm-6 maring-tb-10 hide">
									<input type="hidden" id="id_revision_no" name="revision_no" value="0"/>
									<label><span id="po_no_label"  hidden>PO No:</span></label>
									<label><span id="draft_po_no_label" hidden>PO Draft No: </span></label>

								</div>
								<div>
									<label><span id="review_po_no_label" hidden>Review Remarks: </span></label>
									<label id="review_remarks"/>
								</div>
							</div>
							<div class="clearfix"></div>
							<label id="orderno" class="hide"></label>
							<div class="col-sm-8" style="margin-top: -15px;">
								<input type="hidden" name="po_order" id="po_order" value="">
								<input type="hidden" name="po_draft_date" id="po_draft_date" value="">
								<input type="hidden" name="is_blanket_po" id="is_blanket_po" value="{{ enterprise_profile.is_blanket_po }}">
								<div class="col-sm-6">
									<div style="margin-top: 16px;">
										<div class="col-sm-6 form-group checkbox" style="margin-left: -27px;width: 179px;float:left;">
											<div class="blanket_po" style="background-color:transparent !important;">
												<input type="checkbox" class="chkcase" id="txt_blanket_open_po" />
												<label for="txt_blanket_open_po" style="font-weight: 0 !important; vertical-align:middle;">Blanket/Open {{order_type}} </label>
											</div>
										</div>
										<div class="col-sm-1 for-indent-no">
											{% if logged_in_user.is_super %}
												<a class="super_edit_field open_po super_edit_blanket_po hide" onclick="superEditPOBlanket(this);" data-tooltip="tooltip" data-placement="left" title="Super Edit" style="margin-top:0px;background:transparent;margin-left:-27px;">
													<i class="fa fa-pencil super_edit_in_field approve_date"></i>
												</a>
											{%else%}
												<a class="super_edit_field open_po hide" onclick="" data-tooltip="tooltip" data-placement="left" title="Only Super User can edit this field" style=" margin-top: -10px; background:transparent;margin-left:-27px;">
													<i class="fa fa-pencil super_edit_in_field approve_date" style="color:#777;"></i>
												</a>
											{%endif%}
										</div>
										<div class="col-sm-5 valid_until hide" style="margin-top:-18px;margin-left: -24px;float:left;" >
											<label>Validity Period</label>
											<div id="reportrange" class="report-range form-control"  style="width: 225px;">
												<i class="glyphicon glyphicon-calendar"></i>&nbsp;
												<span></span> <b class="caret"></b>
												<input type="hidden" class="fromdate" id="fromdate" name="fromdate" value="" />
												<input type="hidden" class="todate" id="todate" name="todate" value=""/>
											</div>
										</div>

									</div>
								</div>
								<div class="pull-left hide" style="width: calc(100% - 300px); margin-left: 30px;"><!-- Tags are hidden on the 2.16.3 release -->
									<label>&nbsp;</label>
									<ul id="po_tags_table" class="tagit-display form-control">
		                            	<span class="text_for_tag input" id="id_text_for_tag">
											<input type="text" value="" class="form-control" name="po_tags" id="id_po_tag" placeholder ="Type your tags here" >
											<input type="text" value="" class="textbox2" id="po_tag_value" maxlength="10" hidden="hidden">
										</span>
									</ul>
									<span style="position: absolute; margin-top: -8px; color: #aaa; font-size: 11px;">Type and press enter to add new tag.</span>
									<input type="button" class="btn btn-save hide" id="add_po_tag" value="+"/>
								</div>
								<div class="clearfix"></div>
								<div class="row remove-padding">
									<div class="form-group col-sm-6">
										<div class="tour_project_tag">
											<div class="component_project" data-id="projectheader" data-name="select" data-isSuper={{logged_in_user.is_super}} data-isEditDisable="{% if status == '2' %}true{% else %}false{% endif %}"></div>
											<label id="expRev" style="display: block;margin-top: 20px;">Revenue: <span id="revenue" style="margin-right: 10px;">0</span>Expense : <span id="expense" style="margin-right: 10px;">0</span>Budget : <span id="budgetAmount" style="margin-right: 10px;">0</span> </label>
											<span hidden="hidden" id="cashflow_amount" style="margin-right: 10px;">0</span><span hidden="hidden" id="cash_allocated" style="margin-right: 10px;">0</span>
										</div>
									</div>
									<div class="form-group col-sm-6">
										<label>
											{% if logged_in_user.is_super %}
												<a class="super_edit_field super_edit_for_draft hide" onclick="SuperEditPOSelect(this);" data-tooltip="tooltip" data-placement="left" title="Super Edit">
													<i class="fa fa-pencil super_edit_in_field"></i>
												</a>
											{% else %}
												<a class="super_edit_field super_edit_for_draft hide"  onclick="" data-tooltip="tooltip" data-placement="left" title="Only Super User can edit this field">
													<i class="fa fa-pencil super_edit_in_field" style="color:#777;"></i>
												</a>
											{% endif %}
                                            <span class="supplier_label">Supplier</span><span class="mandatory_mark"> *</span>
										</label>
                                        <select class="form-control chosen-select party_select_dropdown" name="select" id="supplier">
                                            <option value="">--Select Supplier--</option>
                                            {% for supplier in suppliers %}
                                                <option value="{{ supplier.0 }}">{{ supplier.1 }} {% if supplier.2 != "" %}({{ supplier.2 }}){% endif %}</option>
                                            {% endfor %}
                                            <option id="cmdSupAdd" value="0" >+ Add new Supplier</option>
                                        </select>
									</div>
								</div>
								<div class="col-sm-12 remove-padding">
									<label>DELIVERY ADDRESS</label>
									<span class="enable_edit_option" onclick="enableShippingAddress();" data-tooltip="tooltip" title="" data-original-title="Edit">
										<i class="fa fa-pencil super_edit_in_field" style="margin-top: -3px; margin-right: 15px;"></i>
									</span>
									<input class="form-control" id="id_purchase-shipping_name" maxlength="100" onkeypress="validateStringOnKeyPress(this,event,'alphaSpecialWithoutSingleQuotes');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialWithoutSingleQuotes');" name="purchase-ship_to_name" placeholder="Ship to Name" type="text" readonly="readonly" style="width: calc(100% - 140px);float: right;">
									<textarea class="form-control" cols="40" id="id_purchase-shipping_address" onkeypress="validateStringOnKeyPress(this,event,'alphaSpecialWithoutSingleQuotes');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialWithoutSingleQuotes');" maxlength="300" name="purchase-deliver_to" rows="2" readonly="readonly" style="margin-top: 20px;margin-bottom: 15px;"></textarea>

								</div>
							</div>
							<div class="col-sm-4 for_indent_po" {% if not  module_access.indent %} hidden {% endif %}>
								<table border="0" class="side-table table text_box_in_table for_indent_available_side_container hide">
									<tr>
										<td class="side-header" style="width: 180px;">Indent No.</td>
										<td class="side-content">
											{% if logged_in_user.is_super %}
												<a class="super_edit_field super_edit_for_draft hide" id="super_edit_for_indent" onclick="SuperEditPOIndentSelect(this);" data-tooltip="tooltip" data-placement="left" title="Super User" style="margin-top: 4px; margin-right: 10px; background: transparent; height: 20px; padding: 0;">
													<i class="fa fa-pencil super_edit_in_field"></i>
												</a>
											{%else%}
												<a class="super_edit_field super_edit_for_draft hide" id="super_edit_for_indent" onclick="" data-tooltip="tooltip" data-placement="left" title="Only Super User can edit this field" style="margin-top: 4px; margin-right: 10px; background: transparent; height: 20px; padding: 0;">
													<i class="fa fa-pencil super_edit_in_field" style="color:#777;"></i>
												</a>
											{%endif%}
											<select class="form-control chosen-select" name="select" id="indents">
												<option value="0"> -- Select an Indent -- </option>
												{% for indent in indents %}
												<option value="{{ indent.0 }}">{{ indent.2 }}/IND/{{ indent.1 }}</option>
												{% endfor %}
											</select>

										</td>
									</tr>
									<tr>
										<td class="side-header" style="width: 180px;">Purchase Account (Indent Type)</td>
										<td class="side-content">
											<select class="form-control" name="select" id="indent_type">
												<option value="0" id="indent_type_default_option">--Indent Type--</option>
												{% for j in purchase_account %}
								                <option value="{{ j.0 }}">{{ j.1 }}</option>
								                {% endfor %}
											</select>
										</td>
									</tr>
									<tr>
										<td class="side-header" style="width: 180px;">Indent For</td>
										<td class="side-content">
											<input type="text" id="purpose" class="form-control" placeholder="Purpose" readonly="true" />
										</td>
									</tr>
									<tr>
										<td class="side-header" style="width: 180px;">Indent Remarks</td>
										<td class="side-content">
											<input type="text" value="" class="form-control" id="indent_remarks" placeholder="Remarks" maxlength="300" readonly="true" style="width: 80%; float: left;">
											<div class="remarks_count_link indent_remarks_count disabled" style="margin-top: 3px;" onclick="indentRemarksHistory();">
												<span class="remarks_counter">No</span><span> remarks</span>
											</div>
											<div id="show_indent_remarks_history_modal" class="modal fade"  role="dialog">
												<div class="modal-dialog modal-md">
													<div class="modal-content">
														<div class="modal-header">
															<button type="button" class="close" data-dismiss="modal">&times;</button>
															<h4 class="modal-title">Remarks</h4>
														</div>
														<div class="modal-body">
															<div id="indentRemarksList"></div>
														</div>
														<div class="modal-footer" style="border-top: none;"></div>
													</div>
												</div>
											</div>
										</td>
									</tr>
								</table>
							</div>

							<div class="clearfix"></div>

							<div class="col-lg-12">
								<ul class="nav nav-tabs">
									<li id="item_particulars" class="active"><a role="button"><h5>ITEM PARTICULARS</h5></a></li>
									<li style="margin-top: 20px"><span class='service-item-flag'></span> - Service</li>
								</ul>

								<!--from-today: 5164-->
								<div class="form-inline">
									<div class="form-group remove-padding" style="width: 320px; position: absolute; right: 15px; margin-top: -40px;">
										<label style="margin-right: 15px;" class="production_due_on">Delivery Due On</label>
										<input type="text" id="deliverydate" class="form-control hide" placeholder="Select Date" />
										<input type="text" class="form-control custom_datepicker set-my-start-date fixed-width-medium" data-setdate="" placeholder="Select Date" id="po_delivery_date" readonly="readonly">
										<i class="glyphicon glyphicon-calendar custom-calender-icon" style="margin-left: 127px;"></i>
									</div>
								</div>
								<div class="row item-particulars-add-container" id="stockable_material">
									<div id="stockable">
										<table class="table table-striped table-bordered custom-table custom-table-large table-fixed text_box_in_table" id="po_materials_table">
											<thead class="th-vertical-center">
											<tr>
												<th style="width: 40px;" rowspan="2" valign="center">S.No</th>
												<th rowspan="2" valign="center" id="id_material_label">Material</th>
												<th style="width: 120px;" colspan="2" class='for_indent_po colspan_indent_ava_qty '>Quantity</th>
												<th style="width: 80px;" rowspan="2" class='for_quick_po'>Quantity</th>
												<th style="width: 80px;" rowspan="2" valign="center" class="quantity_unit" >Price/ Unit <br>(MRP)</th>
												<th style="width: 80px;" rowspan="2" valign="center">DISC.(%)</th>
												<th style="width: 80px;" rowspan="2" class='for_indent_po'>
													Unit Price
													<span class="th-sub-heading bracket-enclosed">Approved</span>
												</th>
												<th style="width: 80px;" rowspan="2" class='for_quick_po'>Price</th>
												<th style="width: 80px;" rowspan="2" valign="center">Value</th>
												<th style="width: 70px;" rowspan="2" class="for_indent_po po_taxes">CGST (%)<span class="th-sub-heading bracket-enclosed">Amount</span></th>
												<th style="width: 70px;" rowspan="2" class="for_indent_po">SGST (%)<span class="th-sub-heading bracket-enclosed">Amount</span></th>
												<th style="width: 70px;" rowspan="2" class="for_indent_po">IGST (%)<span class="th-sub-heading bracket-enclosed">Amount</span></th>
												<th style="width: 100px;" class='{% if not enterprise_profile.is_delivery_schedule %}hide{% endif %}' rowspan="2">DELIVERY DUE
													<span style="text-align: center" class="th-sub-heading bracket-enclosed">Next Schedule</span>
													<span style="width: 30px;position: absolute; right: 30px; margin-top: -15px; cursor: pointer;" onclick="javascript:showConsolidatedDeliverySchedule();" data-tooltip="tooltip" title="Consolidated Delivery Schedule">
														<img src="/site_media/images/consolidated.png" style="width: 24px;"/>
													</span>
												</th>
											</tr>
											<tr>
												<th class="for_indent_available hide">
													<span>Indent</span>
													<span class="tr-ordered-qty">(Ordered)</span>
												</th>
												<th class="for_indent_po">
													<span>{{order_type}}</span>
													<span class="for_indent_available tr-balance-qty hide">(Balance)</span>
												</th>
												<th class="for_indent_po">
													<span>Received</span>
													<span class="tr-uom">UoM</span>
												</th>
											</tr>
											</thead>
											<tbody class='item-for-goods'></tbody>
											<tbody class='item-for-service'></tbody>
											<tbody class="add_item_details row" id="job_stockable">
												<tr>
													<td class='text-center'>
														<div class="material_txt" style="float: right;margin-top: -18px;position: absolute;right: 2px;">
																<button type="button" id="addmaterials"  class="btn btn-save btn-margin-1" style="border-radius:50px;">+</button>
														</div>
													</td>
													<td class='text-left'>
														<div class="material_name">
															<input type="text" value="" class="form-control" id="materialrequiredjob" maxlength="100" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" placeholder="Select Item">
															<input type="hidden" value="" class="" id="material_id_hidden" placeholder="" hidden="hidden">
															<input type="hidden" value="" class="" id="material_id" placeholder="" hidden="hidden">
															<input type="hidden" value="" class="" id="material_is_service" placeholder="" hidden="hidden">
															<input type="hidden" value="" class="" id="material_spq" placeholder="" hidden="hidden">
															<input type="hidden" value="" class="" id="material_moq" placeholder="" hidden="hidden">
															<input type="hidden" name="po_type" value="Job Purchase order" class="" id="Po_type" placeholder="" hidden="hidden">
															<div hidden="hidden">
																<input type="text" value="0" hidden="hidden" id="newFormCount"/>
																<input id="id_ind_material-__prefix__-make_id" hidden="hidden" type="text" class="form-control" name="ind_material-__prefix__-make_id" />
															</div>
															<span class="material-removal-icon hide" style="padding: 5px 15px;margin-top: -25px;">
																	<i class="fa fa-times"></i>
															</span>
														</div>
													</td>

													<td class='for_indent_available text-right hide'>
														<span class='td_indent_qty'></span>
														<span class='td_ordered_qty bracket-enclosed'></span>
													</td>

													<td colspan="2" class="quick_po_qty">
														<input type="text" id="job_qty" class="form-control" maxlength="16" placeholder="Enter Qty" value="0.00" onfocus="setNumberRangeOnFocus(this,12,3)" onblur="addItemTableCalaculation();"/>
														<span class='for_indent_available td_balance_qty bracket-enclosed hide'></span>
														<span id="unit_display" class="unit_display unit_display-small pull-right" style="padding-bottom: 2px;margin-top:-26px;">&nbsp;</span>
														<div class="alternate_unit_select_box hide" style="position: relative;margin-top: 8px;margin-right: -16px;">
															<select class="form-control unit_select_box" name="select" id="id_material-alternate_units" onchange="loadAlternateUnitPrice();"></select>
														</div>
														<div class="all_units_select_box hide">
															<select class="form-control unit_select_box" name="select" id="id_material-all_units" >
															</select>
														</div>
													</td>
													<td class="form-group units-container hide">
														<label>Units</label>
														<select class="form-control" name="select" disabled="disabled" id="job_unit" >
															{% for unit in units %}
															<option value="{{ unit.0 }}">{{ unit.1 }}</option>
															{% endfor %}
														</select>
													</td>

													<td>
														{% if enterprise_profile.is_price_modification_disabled %}
														<input type="text" id="job_price" class="form-control" disabled="disabled" maxlength="19" placeholder="Price" value="0.00" onfocus="setNumberRangeOnFocus(this,13,5)" onblur="addItemTableCalaculation();"/>
														{%else%}
														<input type="text" id="job_price" class="form-control" maxlength="19" placeholder="Price" value="0.00" onfocus="setNumberRangeOnFocus(this,13,5)" onblur="addItemTableCalaculation();"/>
														{% endif %}
														<input type="text" hidden="hidden" id="job_price_hidden" value="0.00">
													</td>
													<td>
														<input type="text" id="job_discount" class="form-control" maxlength="6" placeholder="Discount" value="0.00" onfocus="setNumberRangeOnFocus(this,3,2)" onblur="validatePercentage(this, event); addItemTableCalaculation();" />
													</td>
													<td class='text-right'>
														<input type='text' id="job_unit_price" class='form-control text-right' value ='0.00' disabled='true' onblur="addItemTableCalaculation();">
													</td>
													<td>
														<input type='text' id="job_value" class='form-control text-right'  value ='0.00' disabled='true'>
													</td>
													<td hidden='hidden'> 0 </td>
													<td class='for_indent_po'></td>
													<td class='for_indent_po'></td>
													<td class='for_indent_po'></td>
													<td class='{% if not enterprise_profile.is_delivery_schedule %}hide{% endif %}'></td>
	                                            </tr>
											</tbody>
											<tfoot>

											</tfoot>
										</table>
									</div>
								</div>

							</div>
							<div class="col-sm-8 remove-padding">
								<div class="col-sm-7">
									<div class="form-inline">
										<div class="form-group col-sm-12 remove-padding" style="margin-bottom: 10px;">
											<label style="width: 160px;">
												{% if logged_in_user.is_super %}
													<a class="super_edit_field super_edit_for_load hide" onclick="SuperEditPOSelect(this);" data-tooltip="tooltip" data-placement="left" title="Super Edit" style="left: calc(132px + 60%); margin-top: -7px; right: 100%;">
														<i class="fa fa-pencil super_edit_in_field"></i>
													</a>
												{%else%}
													<a class="super_edit_field super_edit_for_load hide" onclick="" data-tooltip="tooltip" data-placement="left" title="Only Super User can edit this field" style="left: calc(132px + 60%); margin-top: -7px; right: 100%;">
														<i class="fa fa-pencil super_edit_in_field" style="color:#777;"></i>
													</a>
												{%endif%}
												Currency</label>
											<select class="form-control chosen-select" name="select" id="currency">
												{% for j in currency %}
												<option value="{{ j.id }}">{{ j.code }}</option>
												{% endfor %}
											</select>
											<input type="hidden" name="home_currency" id="id_home_currency" value="{{home_currency}}">
										</div>

										<div class="form-group col-sm-12 remove-padding">
											<label style="width: 160px;" class="form-group">Quotation Ref No</label>
											<input type="text" value="" class="form-control fixed-width-medium" id="refno"
											       onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" placeholder="Quotation Reference No" maxlength="50">
										</div>
										<div class="form-group col-sm-12 remove-padding">
											<label style="width: 160px;">Quotation Date</label>
											<input type="text" id="quotdate" class="form-control hide" placeholder="Select Date" />
											<input type="text" class="form-control custom_datepicker full-datepicker fixed-width-medium set-empty-date erasable-date" placeholder="Select Date" id="po_quot_date" readonly="readonly" autocomplete="off">
											<i class="glyphicon glyphicon-calendar custom-calender-icon" style="position: absolute; left: 163px; top: 35px;"></i>
										</div>

										<div class="form-group col-sm-12 remove-padding disabled" id="div_approved_on_date">
											<label style="width: 160px;">
												{% if logged_in_user.is_super %}
													<a class="super_edit_field super_edit_for_load hide" onclick="SuperEditPODate(this);" data-tooltip="tooltip" data-placement="left" title="Super Edit" style="left: calc(132px + 60%); margin-top: -7px; right: 100%;">
														<i class="fa fa-pencil super_edit_in_field approve_date"></i>
													</a>
												{%else%}
													<a class="super_edit_field super_edit_for_load hide" onclick="" data-tooltip="tooltip" data-placement="left" title="Only Super User can edit this field" style="left: calc(132px + 60%); margin-top: -7px; right: 100%;">
														<i class="fa fa-pencil super_edit_in_field approve_date" style="color:#777;"></i>
													</a>
												{%endif%}
												Approved Date
											</label>
											<input type="text" id="approve_on_date" class="form-control hide" placeholder="Select Date" name="approve_on_date">
											<input type="text" class="form-control custom_datepicker till-today set-empty-date fixed-width-medium" disabled id="approvedOn" placeholder="Approved Date">
											<i class="glyphicon glyphicon-calendar custom-calender-icon" style="position: absolute; left: 163px; top: 35px;"></i>
										</div>
									</div>
								</div>
								<div class="col-sm-5">
									<div class="form-inline">
										<div class="form-group col-sm-12 remove-padding for_indent_po" style="margin-bottom: 20px;">
											<label style="width: 60px;">Taxes<span class="mandatory_mark"> *</span></label>
											<select class="form-control chosen-select" name="po_taxes" id="id_po_tax">
												<option value=""> -NA- </option>
												{% for tax in taxes %}
												<option value="{{ tax.code }}">{{ tax.name }}</option>
												{% endfor %}
											</select>
											<input type="button" class="btn btn-save" id="add_po_tax" value="+" style="margin-left: 15px;" />
										</div>
										<div style="margin-top: -4px;">
											<label>Charges Included</label>
											<div class="checkbox">
												<input type="checkbox" id="txttransport"/>
												<label for="txttransport" style="font-weight: 0 !important; vertical-align:middle;">Transport/Freight</label><br>
												<input type="checkbox" id="txtpacking" />
												<label for="txtpacking" style="font-weight: 0 !important; vertical-align:middle;">Packing & Forwarding</label><br />
												<input type="checkbox" id="txttaxes" class="for_quick_po" />
												<label for="txttaxes" class="for_quick_po" style="margin-bottom:5px;font-weight: 0 !important; vertical-align:middle;">Taxes Extra</label>
											</div>
										</div>
									</div>
								</div>
								<div class="form-group col-sm-6">
									<label style="width: 160px;">Instructions <span class="qtip-question-mark" data-tooltip="tooltip" title="Special Instructions for Party. Will be printed in the PO" data-hasqtip="0" aria-describedby="qtip-0"></span></label>
									<textarea rows="3" name="po-instruction" maxlength="300" id="instructions" cols="40" class="form-control" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')"></textarea>
								</div>
								<div class="col-sm-6">
									<div class="form-group">
										<label for="id_po-remarks">Remarks <span class="qtip-question-mark" data-tooltip="tooltip" title="Internal Remarks. Will not be printed in document." data-hasqtip="0" aria-describedby="qtip-0"></span></label>
										<div class="remarks_count_link remarks_count disabled" onclick="remarks_history();">
											<span class="remarks_counter">No</span><span> remarks</span></div>
										<textarea rows="3" name="po-remarks" maxlength="300" id="id_po-remarks" cols="40" class="form-control" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')"></textarea>
									</div>
								</div>
								<div class="form-group col-sm-12">
									<label>Payment Terms</label><br>
									<div class="col-sm-2 remove-left-padding">
										<input id="txtpayment" class="form-control col-sm-3" maxlength="6" onfocus="setNumberRangeOnFocus(this,3,2)" onblur="validatePercentage(this, event)" placeholder="%" default="100"/><br>
										(%)
									</div>
									<div class="col-sm-2 remove-left-padding">
										<input id="no_of_days" class="form-control col-sm-3" maxlength="6" onfocus="setNumberRangeOnFocus(this,6,0)"  placeholder="days" /><br>
										(in)
									</div>
									<div class="col-sm-4 remove-left-padding">
										<select class="form-control col-sm-3" name="select" id="pay_type">
											<option value="1">No of Days</option>
											<option value="2">Proforma Invoice</option>
											<option value="3">Cash On Delivery</option>
											<option value="4">Advance</option>
										</select>
										<br>
										(against)
									</div>
									<div class="col-sm-4 remove-left-padding remove-right-padding">
										<select class="form-control col-sm-3" name="select" id="pay_mode">
											<option value="1">PDC</option>
											<option value="2">Cheque</option>
											<option value="3">Cash</option>
											<option value="4">DD</option>
											<option value="5">Bank Transfer</option>
										</select><br>
										(through)
									</div>
								</div>
							</div>
							<div class="col-sm-4 for_indent_po">
								<div>
									<table id="po_taxes_table" border="0" width="100%">
										<!-- Loaded through AJAX call or u-->
									</table>
								</div>
								<div class="pull-right" style="width: 366px;">
									<label class="grand-total-text" style="margin-left: -7px; margin-bottom: 45px; margin-top:7px;">Round Off</label>
									<input id="txt_round_off" class="form-control col-sm-3 text-right"
									       onfocus="setNumberRangeOnFocus(this,5,2,false,true)"
									       onblur="calculateTotalPOValue()" style="width: 60%; float: right;" value="0.00" />
								</div>
								<div class="pull-right" style="width: 430px;">
									<label class="grand-total-text">Grand Total</label>
									<input id="txttotalpurvalue" class="grand-total-amount" disabled="disabled" style="width: 60%; float: right;" value="0.00" />
								</div>
							</div>
							<div class="clearfix"></div>
							<div class="form-group col-sm-12 text-right">
								<input id="po_id" name="po_id" value="" hidden="hidden"/>
								{% if access_level.edit %}
								<input type="button" class="btn btn-save" value="Save Draft" id="cmdSave"/>
								{% if access_level.approve %}
								<input type="button" class="btn btn-save for_quick_po" value="Save & Approve" id="cmdSaveApprove"/>
								{% endif %}
								<input type="button" class="btn btn-save hide" value="Update" id="cmdupdate" disabled="disabled" />
								<input type="button" class="btn btn-save hide" value="Submit for Approval" id="cmdreview" />
								{% endif %}
								{% if access_level.approve %}
								<input id="generate_pdf" class="btn btn-save" type="button" value="Approve / Reject" hidden="hidden" style="display:none !important" />
								<input id="amend_po" class="btn btn-save" type="button" value="Amend" disabled="disabled"  style="display:none !important"/>
								<a href="#" id='approve_po' hidden="hidden">Approve</a>
								<input id="remarks" value="" hidden="hidden" placeholder="Remarks"/>
								{% endif %}
							</div>
							<input type="hidden" name="id" id="create_id" value="{{id}}" />
							<input type="hidden" name="po_no" id="po_no" value="{{po_no}}" />
							<input type="text" value="" class="textbox2" id="txtproject" maxlength="10" hidden="hiddden">
							<input type="text" value="" class="textbox2" id="price1" maxlength="10" hidden="hiddden">
							<input type="text" value="" class="textbox2" id="cateloguerequired1" maxlength="10" hidden="hiddden">
						</div>
						<div class="clearfix"></div>
					</form>
				</div>
				<form id="pdf_generation" method="POST" action="/erp/purchase/po/pdf/">{% csrf_token %}
					<input id="id_po_draft_no" name="po_id" value="" hidden>
					<input id="generate_pdf_btn"  type="submit" value="Approve/Reject PO" hidden="hidden"/>
				</form>
			</div>
		</div>
	</div>
</div>
{% include "purchase/po_document.html" %}
<div hidden="hidden">
	<form id='pdf_generation_quick_po' method='POST' action='/erp/purchase/po/pdf/'>
    <input type='hidden' name='csrfmiddlewaretoken' value='{{csrf_token}}' />
    <input type='hidden' id='pdf_po_id' name='po_id' value='' />
    <input type='submit' hidden id='generate_pdf_quick_po' /></form>
</div>

<div id="dcUsageReport" class="modal fade" role="dialog">
  	<div class="modal-dialog modal-lg">
    	<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Material Usage</h4>
		         <span>
	                <a role="button" class="btn btn-add-new pull-right" style="margin-top: -23px;margin-right: 30px;" onclick="GeneralExportTableToCSV.apply(this, [$('#cattable_3'), 'Material_Usage_Report.csv']);"   data-tooltip="tooltip" title="" data-original-title="Download Material Usage List as CSV" data-placement="left">
				        <i class="fa fa-download" aria-hidden="true"></i>
			        </a>
		         </span>
      		</div>
      		<div class="modal-body">
		         <table id="cattable_3" class="table table-bordered custom-table table-striped tableWithText no-sorting">
					<thead>
					<tr>
						<th style="width: 50px;" >S.No</th>
                        <th >Item Name</th>
						<th style="width: 100px;"  class="for_dc">Expected <span class="th-sub-heading">(Based on BoM & JO)</span></th>
						<th style="width: 100px;" class="for_dc" >Supplied</th>
						<th style="width: 100px;" class="for_dc" >Returned</th>
						<th style="width: 100px;" class="for_dc" >To Be Supplied</th>
						<th style="width: 100px;" class="for_dc" >Excess Supplied</th>
						<th style="width: 100px;" class="for_dc exclude_export" >Return Item</th>
					</tr>
					</thead>
				</table>
		        <div id='loadingmessage' class="text-center" style='display:none;margin-top:95px'>
					<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' style="width:75px"/>
				    <br>
				    Please wait...
		        </div>
      		</div>
		    <div class='modal-footer'>
			    <span class="pull-left nonStock-text">  <span class=" nonStock-text non_stock-flag"></span> - NON-STOCKABLE</span>
				<span class="pull-left nonStock-text">  <span class=" nonStock-text service-item-flag"></span> - SERVICE</span>
			    <a class='btn btn-save' id='SaveMU' data-dismiss='modal' onclick="javascript:saveReturnItem();">Save</a>
			    <a class='btn btn-cancel' id='OACancel' data-dismiss='modal'>Close</a></div>
    	</div>
  	</div>
</div>

<div id="show_po_remarks_history_modal" class="modal fade"  role="dialog">
	<div class="modal-dialog modal-md">
	    <div class="modal-content">
            <div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Remarks</h4>
			</div>
		    <div class="modal-body" id="remarks_list_pdf">

		    </div>
			<div class="modal-footer" style="border-top: none;"></div>
	    </div>
	</div>
</div>

<div id="deliveryScheduleParticulars" class="modal fade"  role="dialog">
	<div class="modal-dialog">
	    <div class="modal-content">
            <div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Delivery Schedule</h4>
			</div>
		    <div class="modal-body">
		    	<table class="table table-bordered custom-table" id="deliveryScheduleParticularsTable">
		    		<thead>
			    		<tr>
			    			<th>DUE DATE</th>
			    			<th>QUANTITY</th>
			    			<th style="width: 150px; min-width: 120px;">SPLIT</th>
			    		</tr>
			    	</thead>
			    	<tbody>

			    	</tbody>
		    	</table>
		    </div>
			<div class="modal-footer">
				<span class="btn btn-default" data-dismiss="modal">Close</span>
			</div>
	    </div>
	</div>
</div>

<div id="consolidatedDeliverySchedule" class="modal fade"  role="dialog">
	<div class="modal-dialog">
	    <div class="modal-content">
            <div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Consolidated Delivery Schedule</h4>
			</div>
		    <div class="modal-body">
		    	<table class="table table-bordered custom-table" id="consolidatedDeliveryScheduleTable">
		    		<thead>
			    		<tr>
			    			<th>ITEM DESCRIPTION</th>
			    			<th>DUE DATE</th>
			    			<th>QUANTITY</th>
			    		</tr>
			    	</thead>
			    	<tbody>

			    	</tbody>
		    	</table>
		    </div>
			<div class="modal-footer">
				<span class="btn btn-default" data-dismiss="modal">Close</span>
			</div>
	    </div>
	</div>
</div>

<div id="change_log_modal" class="modal" tabindex="-1" role="dialog" aria-labelledby="" aria-hidden="true">
   <div class="modal-dialog">
	    <div class="modal-content">
		    <div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Activity Log</h4>
	      	</div>
      		<div class="modal-body remove-padding">
		        <div id='loadingmessage_changelog_listing_ie' style="text-align:center">
						<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' width="100">
						<br /> Please wait...
			    </div>
        		<ul class="history-log-container timeline">

        		</ul>
        		<ul class="show-more-log" style="list-style: none; text-align: right; margin-right: 15px; cursor: pointer;">
			        <li onclick="shoMoreLogs(20,20)">Show More</li>
		        </ul>
      		</div>
      		<div class="modal-footer">
      			<a href="#" class="btn btn-cancel" data-dismiss="modal">Close</a>
      		</div>
    	</div>
  	</div>
</div>

{% include "masters/add_material_modal.html" %}
{% include "masters/add_project_modal.html" %}
{% include "masters/add_party_modal.html" %}

<script type="text/javascript">
$(document).ready(function(){
	$('#loading').show();
	function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for(var i=0;i < ca.length;i++)
        {
            var c = ca[i];
            while (c.charAt(0)==' ') c = c.substring(1,c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length,c.length);
        }
        return null;
    }
	var from_date_from_cookie=getCookie('from_date_purchase')
	var to_date_from_cookie = getCookie('to_date_purchase');
	var project_value_from_cookie=getCookie('project_purchase')

	if (from_date_from_cookie) {
		$("#fromdate").val(from_date_from_cookie);
	}
	if (to_date_from_cookie) {
		$("#todate").val(to_date_from_cookie);
	}
	if (project_value_from_cookie) {
		$("#project").val(project_value_from_cookie)
		$("#project").trigger("chosen:updated");
	}
	$(".po_side_menu").click(function() {
       function eraseCookie(name) {
            document.cookie = name+'=; Max-Age=-99999999;';
        }
        eraseCookie('from_date_purchase');
		eraseCookie('to_date_purchase');
		eraseCookie('status_id_purchase');
		eraseCookie('project_purchase');
		eraseCookie('project_value_purchase');
    });

	$("#menu_purchase").click(function() {
		function eraseCookie(name) {
            document.cookie = name+'=; Max-Age=-99999999;';
        }
		eraseCookie('from_date_purchase');
		eraseCookie('to_date_purchase');
		eraseCookie('status_id_purchase');
		eraseCookie('project_purchase');
		eraseCookie('project_value_purchase');
		});
	$(".chosen-select").chosen();
	$('.nav-pills li').removeClass('active');
	if($('#create_id').val() != "" && $('#create_id').val() != "None" ){
		initializePage();
	}
	{% if po_no %}
		editrow("{{po_no}}", "{{status}}");
 	{% else %}
 		$('#loading').hide();
	{% endif %}
});

function initializePage(){
	if($("#orderno").text() == "") {
		if($("#id_order_type").val() == "po"){
			$(".page-title").html('New Purchase Order');
		}
		if($("#id_order_type").val() == "jo"){
			$(".page-title").html('New Job Order');
		}
	}
	else {
		var poCode = $("#orderno").text().split(" ")
		var revisionNo = "";
		if(poCode[1]) { revisionNo = poCode[1] }
		$(".page_header").html('<span class="header_current_page"> '+ poCode[0] +' </span><span class="header_current_page_revision">'+revisionNo+'</span>');
		changeLogActivityInit();
	}
	if($('#create_id').val() == "custom-tab3") {
		$("#tab2").addClass("tab_quick_po").removeClass("tab_indent_po");
		$("#po_materials_table tbody").find(".quick_po_qty").attr("colspan","");
		$("#tab2").addClass("tab_quick_po").removeClass("tab_indent_po");
		if($("#is_price_modification_disabled").val() == "True") {
			$(".purchaseprice").attr('disabled','disabled');
		}else{
			$(".purchaseprice").removeAttr('disabled');
		}
		if($("#is_price_modification_disabled_quick_po").val() == "True") {
			$("#job_price").attr('disabled', 'disabled');
		}else{
			$("#job_price").removeAttr('disabled');
		}

	}
	else {
		$("#tab2").addClass("tab_indent_po").removeClass("tab_quick_po");
		if($("#is_price_modification_disabled").val() == "True") {
			$(".purchaseprice").attr('disabled','disabled');
		}
		if($("#is_price_modification_disabled_quick_po").val() == "True") {
			$("#job_price").attr('disabled', 'disabled');
		}
	}
	if($("#po_draft_date").val() == ""){
		$("#po_delivery_date").attr("data-setdate", new Date());
		$("#valid_until_date").attr("data-setdate", new Date());
	}
	else {
		$("#po_delivery_date").attr("data-setdate", $("#po_draft_date").val());
		$("#valid_until_date").attr("data-setdate", $("#po_draft_date").val());
	}
	if($("#approve_on_date").val() == "") {
		$("#div_approved_on_date").remove();
	}
	POSuperEditInit();
	IsIndentContainerVisible();
}

function getURLParameter(url, name) {
    return (RegExp(name + '=' + '(.+?)(&|$)').exec(url) || [, null])[1];
}

$(window).load(function(){
	var x = document.cookie;
	if (document.cookie.indexOf('po_draft_no_indent=') != -1) {
        var draft_no = getCookie('po_draft_no_indent');
        var status = getCookie('po_draft_no_status');
        editrow(draft_no, status);
        document.cookie = "po_draft_no_indent"+" =; Path=/; expires = Thu, 01 Jan 1970 00:00:00 UTC";
    }
    setTimeout(function(){
		enableEditButtonInit();
		$('#amend_po').attr('title', 'update the changes to enable');
		projectIdFetch();
	},2000);
});
function projectIdFetch(){
	let projectId = $('#projectheader').val();
	 actualProjectsBudget(projectId, $('#projectheader').find(':selected').attr('project-type'));
	$('#projectheader').change(function() {
			actualProjectsBudget($(this).val(),$('#projectheader').find(':selected').attr('project-type'));
	});
}
$(function(){
	var hash = window.location.hash;
	hash && $('ul.nav a[href="' + hash + '"]').tab('show');
	$('.nav-tabs a').click(function (e) {
		$(this).tab('show');
		var scrollmem = $('body').scrollTop() || $('html').scrollTop();
		window.location.hash = this.hash;
		$('html,body').scrollTop(scrollmem);
	});
});

$("#add_po_tax").click(function(){
	$(".error-border").removeClass('error-border');
	$(".custom-error-message").remove();

	var ControlCollections = [
		{
			controltype: 'dropdown',
			controlid: 'id_po_tax',
			isrequired: true,
			errormsg: 'Tax type is required.'
		}
	];
	var result = JSCustomValidator.JSvalidate(ControlCollections);
	return result;
});

$("#addmaterials").click(function(){
	$(".error-border").removeClass('error-border');
	$(".custom-error-message").remove();

	var ControlCollections = [
		{
			controltype: 'textbox',
			controlid: 'materialrequiredjob',
			isrequired: true,
			errormsg: 'Material name is required.'
		},
		{
			controltype: 'textbox',
			controlid: 'job_qty',
			isrequired: true,
			errormsg: 'Quantity is required.',
			mindigit: 0.001,
  			mindigiterrormsg: 'Quantity is required.'
		},
		{
			controltype: 'textbox',
			controlid: 'job_price',
			isrequired: true,
			errormsg: 'Price is required.'
		}
	];
	var result = JSCustomValidator.JSvalidate(ControlCollections);
	if(result){
		$(".custom-error-message").remove();
		var baseSpqValue = Number($('#material_spq').val());
		var baseMoqValue = Number($('#material_moq').val());
        var currentQty = Number($("#job_qty").val());
        var currentUnit = $("#unit_display").text();
        var isSpqValidated = confirmOrderQtyFromClient(baseSpqValue, baseMoqValue, currentQty, currentUnit, 'job_qty');
        if(isSpqValidated) {
    		addupdate_job(1);
        }
        $("#job_unit_price, #job_value").val('0.00');
	}
	return result;
});


function confirmOrderQtyFromClient(orderSpqValue, orderMoqValue, qty, unit, current){
	var alternateUnitValue = 1;
	var defaultValue = qty;
	if(!$(".alternate_unit_select_box").hasClass("hide") && current == "job_qty") {
		alternateUnitValue = $(".alternate_unit_select_box option:selected").attr("data-val");
		unit = $(".alternate_unit_select_box option:selected").text();
		orderSpqValue = orderSpqValue / alternateUnitValue;
		orderMoqValue = orderMoqValue / alternateUnitValue;
	}
	if(current != "job_qty") {
		if(parseFloat($("#"+current).closest("tr").find(".td_balance_qty").text()) < parseFloat(qty) && parseFloat(qty) != qty_round_ceil(orderSpqValue, qty)) {
			defaultValue = parseFloat($("#"+current).closest("tr").find(".td_balance_qty").text())	
			qty = parseFloat($("#"+current).closest("tr").find(".td_balance_qty").text())	
		}
	}

	var checkMod = (qty / orderSpqValue).toFixed(3);
	var ceilValue = qty_round_ceil(orderSpqValue, qty) ;
	var floorValue = qty_round_floor(orderSpqValue, qty);
	if (!Number.isInteger(Number(checkMod)) && orderSpqValue > 0){
		var swalText = `<span class='btn-custom-swal' onclick="updateOrderValue(${floorValue}, '${current}');">
							${floorValue}<small>(Round-down to previous SQP)</small>
						</span>`;
		{% if logged_in_user|canApprove:'PURCHASE' %}
			swalText += `<span class='btn-custom-swal' onclick="updateOrderValue(${defaultValue}, '${current}');">
							${Number(defaultValue).toFixed(3)}<small>(Use As-Is)</small>
						</span>`;
		{% endif %}
		swalText += `<span class='btn-custom-swal' onclick="updateOrderValue(${ceilValue}, '${current}');">
						${ceilValue}<small>(Round-up to next SQP)</small>
					</span>`;
		if(ceilValue < orderMoqValue ) {
			swalText += `<span class='btn-custom-swal' onclick="updateOrderValue(${orderMoqValue}, '${current}');">
							${orderMoqValue.toFixed(3)}<small>(MOQ Value)</small>
						</span>`;
		}
        orderQtyCheckSwal(swalText, qty, unit);
        return false;
    }
    else if(Number(defaultValue) < Number(orderMoqValue)) {
    	var swalText = `<span class='btn-custom-swal' onclick="updateOrderValue(${defaultValue}, '${current}');">
							${Number(defaultValue).toFixed(3)}<small>(Use As-Is)</small>
						</span>`;
		swalText += `<span class='btn-custom-swal' onclick="updateOrderValue(${orderMoqValue}, '${current}');">
						${orderMoqValue.toFixed(3)}<small>(MOQ Value)</small>
					</span>`;
		orderQtyCheckSwal(swalText, qty, unit);
        return false;		
    }
    else {
    	return true;
    }
}

function orderQtyCheckSwal(text, qty, unit) {
	swal({
        title: `<h4 style='line-height:23px;'>Order Quantity specified <b>(${Number(qty).toFixed(3)} ${unit})</b> does not match with at least one constraint!<br /></h4>
        	    <h5 style='margin-top: 15px;'>Choose Appropriate Quantity</h5>`,
        text: text,
        type: "warning",
        showCancelButton: false,
        showConfirmButton: false,
        closeOnConfirm: true,
        allowOutsideClick: false,
        allowEscapeKey: false,
        customClass: 'swal-wide'
    });
}

function updateOrderValue(value, current) {
 	swal.close();
 	$("#"+current).val(Number(value).toFixed(3));
 	if(current == "job_qty") {
	    addupdate_job(1);
	}
	else {
	    setTimeout(function(){
	    	calculateDeliveryDue($("#"+current));
	    	$("#"+current).trigger("blur");
	    },260);
	}
}

function qty_round_ceil(spq_value, input_value) {
    return (Math.ceil(input_value / spq_value) * spq_value).toFixed(3);
}

function qty_round_floor(spq_value, input_value) {
    return (Math.floor(input_value / spq_value) * spq_value).toFixed(3);
}

function poPrevNextPaging() {
    if($(".header_current_page").text().indexOf("New") >=0 || $(".header_current_page").text() == "") {
        $(".prev_next_container").remove();
    }
    else {
        var POListNav = JSON.parse(localStorage.getItem('poListNav'));
        if(POListNav != null) {
            var curPOId = $("#id_job_po").val();
            for (var i = 0; i < POListNav.length; i++){
              if (POListNav[i].poId == curPOId){
                if(i != 0) {
                    var prevPOId = POListNav[i-1].poId;
                    var prevPONo = POListNav[i-1].poNumber;
                    if(prevPONo == "-NA-") prevPONo = prevPOId;
                }
                if(i != Number(POListNav.length - 1)) {
                    var nextPOId = POListNav[i+1].poId;
                    var nextPONo = POListNav[i+1].poNumber;
                    if(nextPONo == "-NA-") nextPONo = nextPOId;
                 }
              }
            }
            var PrevNextPO = "";
            
            if(nextPOId) {
                PrevNextPO += '<a role="button" class="btn btn-add-new pull-right btn-data-tooltip" data-tooltip="tooltip" title="Next PO: '+nextPONo+'" style="margin-right: 7px;" onclick="javascript:editrow('+nextPOId+');"><i class="fa fa-forward" aria-hidden="true"></i></a>';
            }
            else {
                PrevNextPO += '<a class="btn btn-add-new pull-right disabled btn-data-tooltip" data-tooltip="tooltip" title="Next/ Prev will appear only for the current page" style="padding: 5px 12px; margin-right: 7px;"><i class="fa fa-forward" aria-hidden="true"></i></a>';
            }
            if(prevPOId) {
                PrevNextPO += '<a role="button" class="btn btn-add-new pull-right btn-data-tooltip" data-tooltip="tooltip" title="Prev. PO: '+prevPONo+'" style="margin-right: 7px;" onclick="javascript:editrow('+prevPOId+');"><i class="fa fa-backward" aria-hidden="true"></i></a>';
            }
            else {
                PrevNextPO += '<a class="btn btn-add-new pull-right disabled btn-data-tooltip" data-tooltip="tooltip" title="Next/ Prev will appear only for the current page" style="padding: 5px 12px; margin-right: 7px;"><i class="fa fa-backward" aria-hidden="true"></i></a>';
            }
            
            $(".prev_next_container").html(PrevNextPO);
            TooltipInit();
        } 
    }
}

$("#tab_view").click(function(){
	window.location.assign('/erp/purchase/po/')
});

$('#chooseIndent').on('hidden.bs.modal', function () {
	if($("#indent_po").hasClass('dataTable')) {
		iTable.destroy();
	}
});

$("#txttaxes").change(function(){
	if($(this).is(":checked")) {
		var instruction = $("#instructions").val().replace(/ Taxes extra./g,'');;
		$("#instructions").val(instruction +" Taxes extra.");
	}
	else {
		swal({
			title: "",
			text: "<b>Taxes Extra</b> checkbox has been unchecked. <br />Kindly check for the text <b>'Taxes extra'</b> in Instructions field & remove it if not necessary!.",
			type: "warning"
		})
	}
});

function POSuperEditInit(){	
	if($("#is_super_user").val().toLowerCase() == 'true') {
		if($(".page-title").text().indexOf("New") >=0) {
			$(".super_user_icon, .xsid_number_edit, .super_user_tool_tip").remove();
			$(".super_edit_for_confirm, .super_edit_for_draft").remove();
			$(".super_edit_for_load").removeClass("hide");
		}
		else if($(".header_current_page").text().trim().length <= 5) {
			$(".super_user_icon, .xsid_number_edit, .super_user_tool_tip").remove();
			$(".super_edit_for_confirm").remove();
			$(".super_edit_for_load, .super_edit_for_draft").removeClass("hide");
		}
		else {
			$(".super_user_icon, .super_user_tool_tip").removeClass("hide");	
			$(".super_edit_for_load, .super_edit_for_draft, .super_edit_for_confirm").removeClass("hide");
		}
		$('.super_user_tool_tip span').qtip({
		   content: {
		        text: "<ul class='ul_for_tool_tip'><li>Super-edit enables modifying the P.O. Code subject to Duplication Check.</li>\
		        		   <li>Code format will be 'FY-FY/PO/NNNNNNx', <br />eg. '18-19/PO/000731b'.<br />\
		        		   FY details - 5 characters (max), <br />P.O. Type - 2 characters, <br />Number - 6 digits (max), <br />Sub-number - 1 alphabet (max).</li>\
		        		   <li>Subsequent numbering of P.O. will pick from the highest of the P.O. Code, <br/> i.e., gaps will not be filled automatically.</li></ul>",

		        title: 'Super-Edit'
		    }
		});
	}
	else {
		if($(".page-title").text().indexOf("New") >=0) {
			$(".super_user_icon, .xsid_number_edit, .super_user_tool_tip").remove();
			$(".super_edit_for_confirm, .super_edit_for_draft").remove();
			$(".super_edit_for_load").removeClass("hide");
		}
		else if($(".header_current_page").text().trim().length <= 5) {
			$(".super_user_icon, .xsid_number_edit, .super_user_tool_tip").remove();
			$(".super_edit_for_confirm").remove();
			$(".super_edit_for_load, .super_edit_for_draft").removeClass("hide");
		}
		else {
			$(".super_user_icon, .super_user_tool_tip").removeClass("hide");
			$(".super_edit_for_load, .super_edit_for_draft, .super_edit_for_confirm").removeClass("hide");
		}
	}
}

function EditPONumber() {
	var po_type = $("#orderno").text().split("/")[1];
	var url = "/erp/purchase/json/get_po_linked_message/";
	if(po_type == "JO"){
		url = "/erp/sales/json/get_jo_linked_message/";
	}
	$.ajax({
			url: url,
			method: "POST",
			data:{
				po_id: $("#po_id").val(),
			},
			success: function(response) {
				if (response.response_message =="Success") {
				    if(response.custom_message == "") {
				        var poNumber = $(".header_current_page").text().trim();
						var poNumberSplit = poNumber.split("/");
						$("#po_financial_year").val(poNumberSplit[0]);
						$("#po_type").val($("#po_type option[data-val="+poNumberSplit[1]+"]").val())
						if($.isNumeric( poNumberSplit[2].substr(-1) )){
							$("#po_number").val(poNumberSplit[2]);
						}
						else {
							$("#po_number").val(poNumberSplit[2].slice(0, -1));
							$("#po_number_division").val(poNumberSplit[2].substr(-1));
						}
						$(".xsid_number_edit").removeClass("hide");
						$(".super_user_icon, .header_current_page").addClass("hide")
				    } else {
				        swal({title: "", text: response.custom_message, type: "warning",
					            showCancelButton: true,
                                confirmButtonColor: "#209be1",
                                confirmButtonText: "Yes, do it!",
                                closeOnConfirm: true,
                                closeOnCancel: true
                              },
                              function(isConfirm){
                                    if (isConfirm) {
										var poNumber = $(".header_current_page").text().trim();
										var poNumberSplit = poNumber.split("/");
										$("#po_financial_year").val(poNumberSplit[0]);
										$("#po_type").val($("#po_type option[data-val="+poNumberSplit[1]+"]").val())
										if($.isNumeric( poNumberSplit[2].substr(-1) )){
											$("#po_number").val(poNumberSplit[2]);
									}
									else {
										$("#po_number").val(poNumberSplit[2].slice(0, -1));
										$("#po_number_division").val(poNumberSplit[2].substr(-1));
									}
									$(".xsid_number_edit").removeClass("hide");
									$(".super_user_icon, .header_current_page").addClass("hide")
                                    }
                              });
				    }
				} else {
					swal({title: "", text: response.custom_message, type: "error"});
				}
			},
			error: function (xhr, errmsg, err) {
	                swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
	        }
        });
}

function DiscardEditPONumber(){
    $(".xsid_number_edit").addClass("hide");
    $(".super_user_icon, .header_current_page").removeClass("hide");
    $("#po_financial_year, #po_number, #po_number_division").val("");
    //$("#po_type").val(0);
}

function SavePONumber(){
	if($("#po_financial_year").val() =="" || $("#po_number").val() == "" || Number($("#po_number").val()) <= 0 || $("#po_type").val() == "") {
		$(".save_xsid_error_format").removeClass("hide");
		if($("#po_financial_year").val() == "") $("#po_financial_year").addClass("super_edit_error_border");
		if($("#po_number").val() == "") $("#po_number").addClass("super_edit_error_border");
		if($("#po_type").val() == "") $("#po_type").addClass("super_edit_error_border");
	}
	else {
		$(".save_xsid_error_format").addClass("hide");
		$("#po_number_division").val($("#po_number_division").val().toLowerCase());
		$.ajax({
			url: "erp/purchase/json/super_edit_po_code/",
			method: "POST",
			data:{
				po_id: $("#po_id").val(),
				new_financial_year: $("#po_financial_year").val().trim(),
				new_po_type: $("#po_type").val().trim(),
				new_po_no: $("#po_number").val().trim(),
				new_sub_number: $("#po_number_division").val().trim()
			},
			success: function(response) {
				if (response.response_message == "Success") {
					swal({
						title: "", 
						text: response.custom_message, 
						type: "success",
                		allowEscapeKey: false
                	},
					function(){
						DiscardEditPONumber();
						$(".header_current_page").text(response.code);
					    editrow($("#po_id").val());
					});
				} else {
					swal({title: "", text: response.custom_message, type: "warning"});
				}
				var event_label = $("#po_type").val().trim() == "JO" ? "Job" : "Purchase";
                ga('send', 'event', event_label + ' Order', 'Super-Edit Code', $('#enterprise_label').val(), 1);
			},
			error: function (xhr, errmsg, err) {
		       swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
	        }
		});
	}
}

function SuperEditPOSelect(field){
	$(field).closest("label").next("select").removeAttr("disabled");
	$('.chosen-select').trigger('chosen:updated');
	$(field).addClass("hide");
}

function SuperEditPODetails(field) {
	$(field).closest("label").next("div").removeClass("disabled");
	$(field).addClass("hide");
}

function SuperEditPODate(field) {
	$(field).closest("div").find("input").removeAttr("disabled");
	$(field).addClass("hide");
}

function SuperEditPOIndentSelect(field) {
	$("#indents, #indent_type").removeAttr("disabled");
	$('.chosen-select').trigger('chosen:updated');
	$(field).addClass("hide");
}

function  superEditPOBlanket(field){
	$(".blanket_po").removeClass("div-disabled");
	$("#reportrange").removeClass("div-disabled");
	$(field).hide();
}

if($(".valid_until").is(":visible")){
	$(".open_po").css({ "right" : "-250px" ,"margin-top" : "-11px"});
	$("#reportrange").addClass("div-disabled");
}


function constructSupplierCode(){
    setTimeout(function(){
        var supplier_code = $('#code').val().replace(/ /g,"_").toUpperCase();
        $('#code').val(supplier_code);
    },10);
}

function enableEditButtonInit(){
	$('body').on('change keypress paste', ':input', function(e) {
		if($(this).attr("id") !="materialrequiredjob" && $(this).attr("id") !="job_qty" && $(this).attr("id") !="job_unit" && $(this).attr("id") !="job_price" && $(this).attr("id") !="job_discount" && $(this).attr("id") !="id_po_tax") {
		    enableEditButton();
	    }
	});

	$('body').on('keyup', ':input', function(e) {
		 if(e.which == 8 || e.which == 46) {
		    enableEditButton();
		 }
	});

	$("#po_tags_table").click(function(e){
	enableEditButton();
	})

	$("#reportrange").click(function(e){
	enableEditButton();
	})
}

function enableEditButton() {
	$('#amend_po').prop('disabled', false);
	$('#amend_po').attr('title', '');
	$('#cmdupdate').prop('disabled', false);
 }

$("#txt_blanket_open_po").click(function(){
	if($("#txt_blanket_open_po").is(":checked")){
		$(".valid_until").removeClass('hide');
	}
	else{
		$(".valid_until").addClass('hide');
		$("#validuntildate").val('');
	}
});

function addItemTableCalaculation(){
	 $("#job_unit_price").val($("#job_price").val() - $("#job_price").val() * $("#job_discount").val()/100);
	 $("#job_value").val($("#job_qty").val() * $("#job_unit_price").val());
}

function shoMoreLogs(offset, limit){
	if($("#change_log_modal").hasClass("change_log")) {
		var po_id = $("#po_id").val();
		$.ajax({
			url: '/erp/purchase/json/po/getpologlist/',
			type: "POST",
			dataType: "json",
			data: {'po_id': po_id, offset: offset, limit: limit},
			success: function (data) {
			if (data['response_code'] != 400){
				x = data['data'];
				var row = "";
				var i2 = offset;
				for(var i = 0; i < x.length; i++) {
					var obj = x[i].log;
					var row = `<li class="history-log-part" id="history-part-${i2}" onclick="loadHistoryContent('${data['data'][0].po_id}', '${obj.modified_at}', ${i2})">
									<span class="history-log-username">${obj.username[0]+" "+obj.username[1]}</span>
									<div class="history-log-content" style="display: none;"></div>
									<span class="history-log-date">${moment(obj.modified_at).format("MMM DD, YYYY hh:mm:ss a")}</span>
								</li>`;

					$(".history-log-container").append(row);
					var displayedCount = $(".history-log-container").find("li").length;
					i2++;
					if(x.length < 20){
						$(".show-more-log").addClass("hide");
					}
					else {
						$(".show-more-log").find("li").attr("onclick", "shoMoreLogs("+displayedCount+", 20)");
					}
				}
			}
			else {
				$(".show-more-log").addClass("hide");
			}
			},
			error: function ($xhr,textStatus,errorThrown) {
				console.log("ERROR : ", $xhr);
				if ($xhr.responseText.indexOf("Your session has expired") != -1){
					location.reload();
				}
			}
		});
	}
}
function changeLogActivityInit(){
	if($("#orderno").text() !=  "") {
	    $("#id-change-log").removeClass("hide");
	    }
	$("#btn_change_log").click(function(){
		$("#change_log_modal").addClass("change_log").removeClass("change_preference_log");
		$("#change_log_modal").modal("show");
		$("#loadingmessage_changelog_listing_ie").show();
		$(".history-log-container").html("");
		var po_id = $("#po_id").val();
		$.ajax({
			url: '/erp/purchase/json/po/getpologlist/',
			type: "POST",
			dataType: "json",
			data: {'po_id': po_id, 'offset': 0, 'limit': 20},
			success: function (data) {
			if (data['response_code'] != 400){
				x = data['data'];
				var row = "";
				for(var i = 0; i < x.length; i++) {
					var obj = x[i].log;
					var row = `<li class="history-log-part" id="history-part-${i}" onclick="loadHistoryContent('${data['data'][0].po_id}', '${obj.modified_at}', ${i})">
									<span class="history-log-username">${obj.username[0]+" "+obj.username[1]}</span>
									<div class="history-log-content" style="display: none;"></div>
									<span class="history-log-date">${moment(obj.modified_at).format("MMM DD, YYYY hh:mm:ss a")}</span>
								</li>`;
					$("#loadingmessage_changelog_listing_ie").hide();
					$(".history-log-container").append(row);
					var displayedCount = $(".history-log-container").find("li").length;
				}
					if(x.length < 20){
						$(".show-more-log").addClass("hide");
					}
					else {
						$(".show-more-log").removeClass("hide");
						$(".show-more-log").find("li").attr("onclick", "shoMoreLogs("+displayedCount+", 20)");
					}
			}
			else {
				var row = `<span style="width: 100%; padding: 15px; text-align:center; display: block;">No Activity Log has been captured.</span>`;
				$("#loadingmessage_changelog_listing_ie").hide();
				$("#change_log_modal .modal-body").html(row);
			}
			},
			error: function ($xhr,textStatus,errorThrown) {
				console.log("ERROR : ", $xhr);
				if ($xhr.responseText.search("Session Expired!") != -1){
					location.reload();
				}
			}
		});
	});

	$("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
}
function loadHistoryContent(po_id, modified_at, i) {
		var currentLogContent = $("#history-part-"+i).find('.history-log-content');
		if(currentLogContent.text() == "") {
			currentLogContent.html(`<img src="/site_media/images/loading_spinner.gif" style="width: 30px;"> Loading, Please wait...`).show();
			currentLogContent.closest(".history-log-part").addClass("active");
			setTimeout(function(){
				$.ajax({
			        url: '/erp/purchase/json/po/getpologdata/',
			        type: "POST",
			        dataType: "json",
			        data: {"po_id":po_id, "modified_at": modified_at},
			        success: function (data) {
			        if (data['response_code'] != 400){
			        var row = '';
				        $.each( data['data'], function( key, value ) {
						  row += `<li>` + value + `</li>`;
						});
			        currentLogContent.html(`<ul>` + row + `</ul>`);
					currentLogContent.hide();
					currentLogContent.slideDown();
					}else{
					currentLogContent.html(`log data not available`);
					currentLogContent.hide();
					currentLogContent.slideDown();

					}
			        },
			        error: function () {
			        console.log('Changelog listing failed to load');

			        }
				});

			},100);
		}
		else {
			if(currentLogContent.find("img").length > 0) {
				return;
			}
			if(currentLogContent.is(":visible")) {
				currentLogContent.slideUp('fast', function() {
				   currentLogContent.closest(".history-log-part").removeClass("active");
				});

			}
			else {
				currentLogContent.slideDown();
				currentLogContent.closest(".history-log-part").addClass("active");
			}
		}
		$("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
}
</script>
{% endblock %}