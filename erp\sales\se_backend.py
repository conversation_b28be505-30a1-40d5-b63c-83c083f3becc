"""
"""
import datetime
from decimal import Decimal

import simple<PERSON><PERSON>
from sqlalchemy import func
from sqlalchemy.orm import make_transient

from erp import DEFAULT_MAKE_ID, IS_FAULTY_TRUE, helper
from erp.admin.backend import PurchaseTemplateConfigService
from erp.commons.backend import push_notification
from erp.dao import DataAccessObject
from erp.forms import SalesEstimateForm
from erp.formsets import SEParticularsFormset, TagFormset, SETaxFormset
from erp.helper import getUser
from erp.models import SalesEstimate, SETag, SEParticulars, SEDocument, SETax, SEContactMap, SEAttachments, Attachment, \
	OA, Invoice
from erp.notifications import PUSH_NOTIFICATION
from erp.sales import logger
from erp.sales.backend import InvoiceService
from erp.sales.changelog import SalesEstimateChangelog
from erp.sales.document_compiler import SalesEstimatePDFGenerator
from erp.tags import generateTagFormset, extractEntityTagMapsFromFormset
from settings import SQLASession
from util.api_util import response_code
from util.document_properties import CANCEL_WATERMARK_DOC_PATH
from util.ftp_helper import FTPUtil
from util.helper import copyFormToEntity, writeFile, constructFormInitializer, getFinancialYear, readFile, \
	getAbsolutePath, getFormattedDocPath, getFormattedRevisedDocPath

__author__ = 'benedict'

SE_FORM_PREFIX = 'se'
SE_PARTICULARS_PREFIX = 'se_particular'
SE_TAX_PREFIX = 'se_tax'
SE_TAG_PREFIX = 'tag'


class SalesEstimateVO(object):
	"""
	This Class holds both Sales Estimate Form and Sales Estimate Form Set
	"""

	def __init__(
			self, sales_estimate_form=SalesEstimateForm(prefix=SE_FORM_PREFIX),
			se_particulars_formset=SEParticularsFormset(prefix=SE_PARTICULARS_PREFIX),
			se_tax_formset=SETaxFormset(prefix=SE_TAX_PREFIX),
			se_tag_formset=TagFormset(prefix=SE_TAG_PREFIX), remarks_list=[], se_contacts_list=[]):
		self.sales_estimate_form = sales_estimate_form
		self.se_particulars_formset = se_particulars_formset
		self.se_tax_formset = se_tax_formset
		self.se_tag_formset = se_tag_formset
		self.remarks_list = remarks_list
		self.se_contacts_list = se_contacts_list

	def is_valid(self):
		try:
			is_valid = self.sales_estimate_form.is_valid() and self.se_particulars_formset.is_valid() and self.se_tax_formset.is_valid()
			logger.info(
				"Sales Estimate - Valid Header: %s, Valid Particulars: %s, Valid Tax: %s" % (
					self.sales_estimate_form.is_valid(), self.se_particulars_formset.is_valid(), self.se_tax_formset.is_valid()))
			if not is_valid:
				logger.info("Sales Estimate Form Errors: \n%s\n%s\n%s\n%s" % (
					self.sales_estimate_form.errors, self.se_particulars_formset.errors,
					self.se_tax_formset.errors, self.se_tag_formset.errors))
		except Exception as e:
			is_valid = False
			logger.exception("The Form Set Validation Error... %s " % e.message)
		return is_valid

	def __repr__(self):
		return 'Sales Estimate Form : %s\n SE particulars Formset : %s\n SE Tag Formset :%s\n SE Tax Formset :%s' % (
			self.sales_estimate_form, self.se_particulars_formset, self.se_tag_formset, self.se_tax_formset)


class SalesEstimateDAO(DataAccessObject):
	"""
	Class that handles all the Sales Estimate module related DB activities - like fetching, save and deleting persistent objects
	"""

	def getSalesEstimate(self, se_id=None):
		se_query = self.db_session.query(SalesEstimate).filter(SalesEstimate.id == se_id)
		return se_query.first()

	def getLatestSalesEstimateCode(self, financial_year=None, enterprise_id=None):
		"""
		Generate the new Sales Estimate code, combined with the financial year.
		As per requirement the numeric part of code is to be recycled every year.
		:param financial_year:
		:param enterprise_id:
		:return:
		"""
		financial_year = financial_year if financial_year else datetime.datetime.now()
		latest_se_id = len(self.db_session.query(SalesEstimate.id).filter(
			SalesEstimate.financial_year == financial_year, SalesEstimate.enterprise_id == enterprise_id).all())
		logger.info('Latest Sales Estimate Code: %s' % (
			1 if not ('%s' % latest_se_id).isdigit() else (int('%s' % latest_se_id) + 1)))
		return 1 if not ('%s' % latest_se_id).isdigit() else (int('%s' % latest_se_id) + 1)

	def getSEParticulars(self, sales_estimate_id=None, item_id=None, enterprise_id=None, make_id=None, is_faulty=None):
		return self.db_session.query(SEParticulars).filter(
			SEParticulars.se_id == sales_estimate_id, SEParticulars.item_id == item_id,
			SEParticulars.make_id == make_id, SEParticulars.is_faulty == is_faulty,
			SEParticulars.enterprise_id == enterprise_id).first()

	def getSETax(self, se_id, tax_code, enterprise_id):
		return self.db_session.query(SETax).filter(
			SETax.se_id == se_id, SETax.tax_code == tax_code,
			SETax.enterprise_id == enterprise_id).first()

	def getSEContact(self, se_id, contact_id):
		return self.db_session.query(SEContactMap).filter(
			SEContactMap.se_id == se_id, SEContactMap.contact_id == contact_id).first()

	def getOATotal(self, enterprise_id=None, se_id=None):
		"""Fuction gives total of all OA with unique sales estimate id"""
		oa_value = SQLASession().query(func.sum(OA.grand_total)).filter(
			OA.enterprise_id == enterprise_id, OA.se_id == se_id, OA.status.in_((0, 1))).group_by(OA.se_id).all()
		return oa_value[0] if oa_value else None

	def getInvoiceTotal(self, enterprise_id=None, se_id=None):
		""" Fuction gives total of all invoice with unique sales estimate id"""
		invoice_value = SQLASession().query(func.sum(Invoice.grand_total)).filter(
			Invoice.enterprise_id == enterprise_id, Invoice.se_id == se_id, Invoice.status.in_((0, 1))).group_by(
			Invoice.se_id).all()
		return invoice_value[0] if invoice_value else None

	def getOAInvoiceDetails(self, enterprise_id=None, se_id=None):
		""" Function collects details of OA and invoice with unique sales estimate id"""
		logger.info("Enterprise_id: %s , Se_no: %s" % (enterprise_id, se_id))
		oa_details = []
		invoice_details = []

		oa = SQLASession().query(OA).filter(OA.enterprise_id == enterprise_id, OA.se_id == se_id, OA.status.in_((0, 1))).all()
		for item in oa:
			logger.info(item.getInternalCode())
			oa_details.append({
				"oa_id": item.id, "oa_code": item.getInternalCode(), "oa_date": item.approved_on, "oa_total": item.grand_total})

		invoice = SQLASession().query(Invoice).filter(
			Invoice.enterprise_id == enterprise_id, Invoice.se_id == se_id, Invoice.status.in_((0, 1))).all()
		for item in invoice:
			invoice_details.append({
				"invoice_id": item.id, "invoice_code": item.getInternalCode(), "type": "sales",
				"invoice_approved": item.approved_on, "invoice_issued": item.issued_on, "invoice_total": item.grand_total})

		return oa_details, invoice_details


class SalesEstimateService:
	def __init__(self):
		"""

		"""
		self.sales_estimate_dao = SalesEstimateDAO()

	def constructSalesEstimateVo(self, sales_estimate=SalesEstimate(), enterprise=None, default_notes=""):
		"""
		Converts the DB persistent entity into a Value Object presentable to the UI layer.

		:param sales_estimate:
		:param enterprise:
		:param default_notes:
		:return:
		"""

		if sales_estimate.remarks:
			remarks_list = sales_estimate.remarks
			for remark in remarks_list:
				remark['date'] = datetime.datetime.strptime(remark['date'], '%Y-%m-%d %H:%M:%S').strftime("%b %d, %Y")
		else:
			remarks_list = []

		return SalesEstimateVO(
			self._generateSalesEstimateForm(sales_estimate=sales_estimate, prefix=SE_FORM_PREFIX, default_notes=default_notes),
			self._genereateSEParticulrasFormset(sales_estimate.items, enterprise),
			self._generateSETaxFormset(sales_estimate.taxes),
			generateTagFormset(tags=sales_estimate.tags, prefix='tag'), remarks_list=remarks_list,
			se_contacts_list=sales_estimate.se_contacts)

	def approveSalesEstimate(self, enterprise_id=None, se_id=None, user_id=None, remarks=None, se_attachments=None):
		"""

		:param enterprise_id:
		:param se_id:
		:param user_id:
		:param remarks:
		:param se_attachments:
		:return:
		"""
		user = getUser(enterprise_id=enterprise_id, user_id=user_id)
		db_session = SQLASession()
		db_session.begin(subtransactions=True)
		try:
			se_to_approve = db_session.query(SalesEstimate).filter(SalesEstimate.id == se_id).first()
			if se_to_approve.status != -1 and (se_to_approve.se_no and se_to_approve.se_no != '0'):
				db_session.rollback()
				response = response_code.success()
				response['custom_message'] = "Sales Estimate has been already Approved - Sales Estimate No: %s" % se_to_approve.getCode()
				response['code'] = se_to_approve.getCode()
				return response
			approved_date = datetime.datetime.now()
			if se_to_approve.se_no == 0 or not se_to_approve.se_no:
				current_fy = getFinancialYear(for_date=approved_date, fy_start_day=se_to_approve.enterprise.fy_start_day)
				latest_se_no = db_session.query(func.max(SalesEstimate.se_no)).filter(
					SalesEstimate.financial_year == current_fy, SalesEstimate.enterprise_id == se_to_approve.enterprise.id,
					SalesEstimate.id != '0', SalesEstimate.type == se_to_approve.type).first()
				logger.info('Recent Sales Estimate No & Type: %s' % latest_se_no)
				new_se_no = 0
				if latest_se_no and latest_se_no[0] is not None:
					new_se_no = int(latest_se_no[0])
				new_se_no += 1
				se_to_approve.se_no = new_se_no
				logger.info("The Status of Sales Estimate :%s" % SalesEstimate.STATUS_APPROVED)
				se_to_approve.status = SalesEstimate.STATUS_APPROVED
				se_to_approve.approved_on = approved_date.strftime('%Y-%m-%d %H:%M:%S')
				se_to_approve.financial_year = current_fy
				se_to_approve.approved_by = user_id
				se_to_approve.last_modified_by = user_id
				se_to_approve.enterprise_id = enterprise_id
			else:
				# This case will happen on Super edit
				se_to_approve.status = SalesEstimate.STATUS_APPROVED
				se_to_approve.approved_on = approved_date
				se_to_approve.approved_by = user_id

			if remarks and remarks.strip() != "":
				se_to_approve.updateRemarks(remarks="Approved Remarks: %s" % remarks, user=user)
			if se_attachments and se_attachments != []:
				for attachment in se_attachments:
					attachments = db_session.query(SEAttachments).filter(
						SEAttachments.se_id == se_id, SEAttachments.enterprise_id == enterprise_id,
						SEAttachments.attachment_id == attachment['attachment_id']).first()
					if not attachments:
						attachments = SEAttachments(
							se_id=se_id, enterprise_id=enterprise_id, doc_extension=attachment['file_ext'],
							revised_on=approved_date.strftime('%Y-%m-%d %H:%M:%S'), revised_by=user_id,
							attachment_id=attachment['attachment_id'])
					else:
						attachments.doc_extension = attachment['file_ext']
						attachments.attachment_id = attachment['attachment_id']
						attachments.revised_on = approved_date.strftime('%Y-%m-%d %H:%M:%S')
						attachments.revised_by = user_id
					db_session.add(attachments)
			db_session.add(se_to_approve)
			logger.debug('Sales Estimate in orm session: %s' % db_session.dirty)
			db_session.commit()
			db_session.refresh(se_to_approve)
			SalesEstimateChangelog().queryInsert(
				user_id=user_id, enterprise_id=enterprise_id, data=se_to_approve)
			InvoiceService().notifySalesEstimateApproveCount(
				enterprise_id=enterprise_id, sender_id=user_id, sales_estimate=se_to_approve,
				code=se_to_approve.getCode(), type=se_to_approve.type)
			response = response_code.success()
			response['code'] = se_to_approve.getCode()
			response['id'] = se_id
		except Exception as e:
			db_session.rollback()
			raise e
		return response

	def saveSalesEstimate(
			self, sales_estimate_vo=None, enterprise_id=None, user_id=None, se_attachments=None, change_as_draft=False):
		"""

		:param sales_estimate_vo:
		:param enterprise_id:
		:param user_id:
		:param se_attachments:
		:return:
		"""
		db_session = self.sales_estimate_dao.db_session
		db_session.begin(subtransactions=True)
		se_id = None
		try:
			if sales_estimate_vo.is_valid():
				se_id = sales_estimate_vo.sales_estimate_form.cleaned_data['id']
				entity = self.sales_estimate_dao.getSalesEstimate(se_id=se_id)
				entity = entity if entity else SalesEstimate()
				[db_session.delete(tag) for tag in entity.tags]
				modified_date = datetime.datetime.now()
				sales_estimate_to_be_saved = self._copySalesEstimateVOtoEntity(
					sales_estimate_vo=sales_estimate_vo, entity=entity, user_id=user_id, modified_date=modified_date)
				logger.debug('Sales Estimate to be saved: %s' % sales_estimate_to_be_saved)
				modifying_user = getUser(enterprise_id=enterprise_id, user_id=user_id)
				sales_estimate_to_be_saved.updateRemarks(
					remarks=sales_estimate_vo.sales_estimate_form.cleaned_data['remarks'], user=modifying_user)
				sales_estimate_to_be_saved.prepared_by = user_id
				sales_estimate_to_be_saved.last_modified_by = user_id
				if change_as_draft:
					sales_estimate_to_be_saved.status = entity.STATUS_DRAFT
				if sales_estimate_to_be_saved.status and sales_estimate_to_be_saved.status < 0:
					sales_estimate_to_be_saved.status = entity.STATUS_APPROVED
				sales_estimate_to_be_saved.last_modified_on = modified_date.strftime('%Y-%m-%d %H:%M:%S')
				db_session.add(sales_estimate_to_be_saved)
				db_session.commit()
				db_session.refresh(sales_estimate_to_be_saved)
				if not se_id or se_id == "":
					se_id = sales_estimate_to_be_saved.id
				SalesEstimateChangelog().queryInsert(
					user_id=user_id, enterprise_id=enterprise_id, data=sales_estimate_to_be_saved)
			else:
				db_session.rollback()

		except Exception as e:
			db_session.rollback()
			logger.exception("Error in Save Sales Estimate - %s" % e.message)
			pass
		return sales_estimate_vo, se_id

	def saveSEAttachments(
			self, se_id=None, enterprise_id=None, user_id=None, se_attachments=None):
		"""

		:param se_id:
		:param enterprise_id:
		:param user_id:
		:param se_attachments:
		:return:
		"""
		db_session = self.sales_estimate_dao.db_session
		db_session.begin(subtransactions=True)
		try:
			if se_attachments and se_attachments != []:
				modified_date = datetime.datetime.now()
				for attachment in se_attachments:
					get_initial = db_session.query(SEAttachments).filter(
						SEAttachments.attachment_id == attachment['attachment_id'],
						SEAttachments.enterprise_id == enterprise_id,
						SEAttachments.se_id == 0
					).first()
					if get_initial:
						get_initial.se_id = se_id
					else:
						attachments = SEAttachments(
							se_id=se_id, enterprise_id=enterprise_id, doc_extension=attachment['file_ext'],
							revised_on=modified_date.strftime('%Y-%m-%d %H:%M:%S'), revised_by=user_id,
							attachment_id=attachment['attachment_id'])
						db_session.add(attachments)
				db_session.commit()
			else:
				db_session.rollback()
		except Exception as e:
			db_session.rollback()
			logger.exception("Error in Save Sales Estimate - %s" % e.message)
			pass

	def _copySalesEstimateVOtoEntity(self, sales_estimate_vo=None, entity=None, user_id=None, modified_date=None):
		"""
		Method converts VO into persist-able data entity.
		Caution: Don't use this method unless the data is to be persisted immediately, as it might lead to inconsistent
			Sales Estimate numbers
		:param sales_estimate_vo:
		:param entity:
		:return:
		"""
		try:
			logger.info(
				"Sales Estimate Id fetched from Sales_Estimate_VO: %s" % sales_estimate_vo.sales_estimate_form.cleaned_data['id'])
			entity = self.__copySalesEstimateFormToEntity(sales_estimate_vo.sales_estimate_form, entity)
			entity.items = self.__extractSEParticularsFromFormset(
				se_particulars_formset=sales_estimate_vo.se_particulars_formset,
				sales_estimate_id=sales_estimate_vo.sales_estimate_form.cleaned_data['id'])
			entity.taxes = self.__extractSETaxFormset(
				sales_estimate_vo.se_tax_formset, sales_estimate_vo.sales_estimate_form.cleaned_data['id'])
			entity.tags = extractEntityTagMapsFromFormset(
				tag_formset=sales_estimate_vo.se_tag_formset, enterprise_id=entity.enterprise_id,
				map_class=SETag, db_session=self.sales_estimate_dao.db_session)
			entity.se_contacts = self.__extractSEContactsFormset(
				se_contact_id_list=sales_estimate_vo.se_contacts_list,
				se_id=sales_estimate_vo.sales_estimate_form.cleaned_data['id'], user_id=user_id, modified_date=modified_date)
			return entity
		except Exception as e:
			logger.exception("Error in Copy Sales Estimate Vo to Entity...%s" % e.message)
		return entity

	def _generateSalesEstimateForm(self, sales_estimate=SalesEstimate(), prefix=SE_FORM_PREFIX, default_notes=""):
		initializer = constructFormInitializer(entity=sales_estimate, exclude_field_keys=('remarks',))
		initializer[u'code'] = sales_estimate.getInternalCode()
		initializer[u'notes'] = default_notes
		return SalesEstimateForm(
			enterprise_id=sales_estimate.enterprise_id, initial=initializer, prefix=prefix)

	def _genereateSEParticulrasFormset(self, se_items=(), enterprise=None):
		initializer = []
		for se_particular in se_items:
			form_initializer = constructFormInitializer(se_particular)
			make = ""
			description = se_particular.item.name
			make_name = helper.constructDifferentMakeName(se_particular.item.makes_json)
			if make_name:
				make = "[" + make_name +"]"
			if se_particular.item.drawing_no:
				description += " - %s" % se_particular.item.drawing_no
			form_initializer['item_name'] = "%s  %s %s " % (
				description, make, "[Faulty]" if se_particular.is_faulty == IS_FAULTY_TRUE else "")
			form_initializer['unit_id'] = "%s" % se_particular.item.unit.unit_name
			form_initializer['item_code'] = "%s" % se_particular.item.drawing_no
			form_initializer['is_service'] = "%s" % se_particular.item.is_service
			form_initializer['make_label'] = "%s" % se_particular.make.__repr__() if se_particular.make.label else "-NA-"
			form_initializer['hsn_code'] = "%s" % se_particular.hsn_code
			form_initializer['discount'] = "%s" % se_particular.discount
			form_initializer['quantity'] = "%s" % se_particular.quantity
			form_initializer['unit_price'] = "%s" % se_particular.unit_rate

			if enterprise.is_multiple_units > 0:
				is_alternate_unit = helper.getAlternateUnitCount(
					enterprise_id=se_particular.enterprise_id, item_id=se_particular.item_id)
				form_initializer['alternate_unit_list'] = []
				if is_alternate_unit > 0:
					alternate_unit_list = [{
						"alternate_unit_id": 0, "unit_name": form_initializer['unit_id'], "scale_factor": 1}]
					alternate_unit_list.extend(helper.populateAlternateUnits(
						enterprise_id=se_particular.enterprise_id, item_id=se_particular.item_id))
					form_initializer['alternate_unit_list'] = simplejson.dumps(alternate_unit_list)
			if se_particular.alternate_unit_id:
				scale_factor = helper.getScaleFactor(
					enterprise_id=se_particular.enterprise_id, item_id=se_particular.item_id,
					alternate_unit_id=se_particular.alternate_unit_id)
				if scale_factor:
					form_initializer['quantity'] = Decimal(se_particular.quantity) / Decimal(scale_factor)
					form_initializer['unit_price'] = Decimal(se_particular.unit_rate) * Decimal(scale_factor)
					form_initializer['unit_id'] = se_particular.alternate_unit.unit.unit_name
					form_initializer['scale_factor'] = scale_factor if scale_factor else 1
			else:
				form_initializer['alternate_unit_id'] = 0
			initializer.append(form_initializer)
		se_particulars_formset = SEParticularsFormset(initial=initializer, prefix=SE_PARTICULARS_PREFIX)
		return se_particulars_formset

	def _generateSETaxFormset(self, se_taxes=()):
		initializer = []
		logger.info("Sales Estimate has %s se_taxes associated" % len(se_taxes))
		for tax in se_taxes:
			from_initializer = constructFormInitializer(tax)
			initializer.append(from_initializer)
		return SETaxFormset(initial=initializer, prefix=SE_TAX_PREFIX)

	def __copySalesEstimateFormToEntity(self, sales_estimate_form, entity):
		try:
			if entity.id is None or entity.id == '':
				entity = copyFormToEntity(
					entity=entity, form=sales_estimate_form, exclude_field_list=('document', 'document_data', 'remarks'))
			else:
				entity.enterprise_id = sales_estimate_form.cleaned_data['enterprise_id']
				entity.party_id = sales_estimate_form.cleaned_data['party_id']
				entity.project_code = sales_estimate_form.cleaned_data['project_code']
				entity.se_no = sales_estimate_form.cleaned_data['se_no']
				entity.grand_total = sales_estimate_form.cleaned_data['grand_total']
				entity.round_off = sales_estimate_form.cleaned_data['round_off']
				entity.currency_id = sales_estimate_form.cleaned_data['currency_id']
				entity.payment_terms = sales_estimate_form.cleaned_data['payment_terms']
				entity.special_instructions = sales_estimate_form.cleaned_data['special_instructions']
				entity.notes = sales_estimate_form.cleaned_data['notes']
				entity.currency_conversion_rate = sales_estimate_form.cleaned_data['currency_conversion_rate']
				entity.purpose = sales_estimate_form.cleaned_data['purpose']
				entity.type = sales_estimate_form.cleaned_data['type']
				entity.ref_no = sales_estimate_form.cleaned_data['ref_no']
				entity.ref_date = sales_estimate_form.cleaned_data['ref_date']
				entity.expiry_date = sales_estimate_form.cleaned_data['expiry_date']
		except Exception as e:
			logger.exception("Error in Copy Form to Entity Part... %s" % e.message)
		return entity

	def __extractSEParticularsFromFormset(self, se_particulars_formset=None, sales_estimate_id=None):
		se_items = []
		for se_particular_form in se_particulars_formset:
			quantity = se_particular_form.cleaned_data['quantity']
			if not se_particular_form.cleaned_data['DELETE'] and quantity > 0:
				item_id = se_particular_form.cleaned_data['item_id']
				enterprise_id = se_particular_form.cleaned_data['enterprise_id']
				make_id = se_particular_form.cleaned_data['make_id']
				is_faulty = se_particular_form.cleaned_data['is_faulty']
				alternate_unit_id = se_particular_form.cleaned_data['alternate_unit_id']
				se_particular = self.sales_estimate_dao.getSEParticulars(
					sales_estimate_id=sales_estimate_id, item_id=item_id, enterprise_id=enterprise_id, make_id=make_id,
					is_faulty=is_faulty)
				if not se_particular:
					se_particular = SEParticulars(
						se_id=sales_estimate_id, item_id=item_id, enterprise_id=enterprise_id, make_id=make_id,
						is_faulty=is_faulty)
				se_particular.quantity = quantity
				se_particular.hsn_code = se_particular_form.cleaned_data['hsn_code']
				se_particular.discount = se_particular_form.cleaned_data['discount'] if se_particular_form.cleaned_data['discount'] else '0.00'
				se_particular.unit_rate = se_particular_form.cleaned_data['unit_price']
				if alternate_unit_id:
					scale_factor = helper.getScaleFactor(
						enterprise_id=enterprise_id, item_id=item_id, alternate_unit_id=alternate_unit_id)
					if scale_factor:
						se_particular.quantity = Decimal(se_particular_form.cleaned_data['quantity']) * Decimal(scale_factor)
						se_particular.unit_rate = Decimal(
							se_particular_form.cleaned_data['unit_price']) / Decimal(scale_factor) if se_particular_form.cleaned_data['unit_price'] else 0
						se_particular.alternate_unit_id = alternate_unit_id
				else:
					se_particular.alternate_unit_id = None
				se_items.append(se_particular)
		logger.debug('Sales Estimate Material constructed: %s' % se_items)
		return se_items

	def __extractSETaxFormset(self, se_tax_formset, se_id):
		se_tax_list = []
		try:
			for se_tax_form in se_tax_formset:
				if not se_tax_form.cleaned_data['DELETE']:
					tax_code = se_tax_form.cleaned_data['tax_code']
					enterprise_id = se_tax_form.cleaned_data['enterprise_id']
					se_tax = self.sales_estimate_dao.getSETax(
						se_id=se_id, tax_code=tax_code, enterprise_id=enterprise_id)
					if not se_tax:
						se_tax = SETax(se_id=se_id, tax_code=tax_code, enterprise_id=enterprise_id)
					se_tax_list.append(se_tax)
			logger.debug('Sales Estimate Tax constructed: %s' % se_tax_list)
		except Exception as e:
			logger.exception("Sales Estimate Tax Extraction from Formset failed...\n %s" % e.message)
			pass
		return se_tax_list

	def __extractSEContactsFormset(self, se_contact_id_list=None, se_id=None, user_id=None, modified_date=None):
		se_contact_list = []
		try:
			for se_contact_id in se_contact_id_list:
				contact_id = se_contact_id
				se_contact_map = self.sales_estimate_dao.getSEContact(se_id=se_id, contact_id=contact_id)
				if not se_contact_map:
					se_contact_map = SEContactMap(
						se_id=se_id, contact_id=contact_id, last_modified_by=user_id,
						last_modified_on=modified_date.strftime('%Y-%m-%d %H:%M:%S'))
				se_contact_list.append(se_contact_map)
			logger.debug('Sales Estimate Contacts_list Constructed: %s' % se_contact_list)
			return se_contact_list

		except Exception as e:
			logger.exception("Sales Estimate Contacts Extraction from Formset failed...\n %s" % e.message)
			raise

	def generateSalesEstimateDocument(self, enterprise_id=None, se_id=None, document_regenerate=False):
		"""

		:param enterprise_id:
		:param se_id:
		:param document_regenerate:
		:return:
		"""
		template_config = PurchaseTemplateConfigService().purchase_template_dao.getTemplateConfig(
			enterprise_id=enterprise_id, collection='sales_estimate_template_config')
		if template_config["print_template"] is None or template_config["print_template"].strip() == "":
			return None
		self.sales_estimate_dao.db_session.begin(subtransactions=True)
		try:
			se_to_print = self.sales_estimate_dao.getSalesEstimate(se_id=se_id)
			logger.debug('Order to be printed - %s' % se_to_print)
			se_revisions = se_to_print.revisions
			se_documents = se_to_print.documents
			se_document = se_documents[0] if len(se_documents) > 0 else None
			temp_doc_path = getFormattedDocPath(code=se_to_print.getCode(), id=se_id)
			temp_rev_doc_path = getFormattedRevisedDocPath(code=se_to_print.getCode(), id=se_id, suffix="-rev")
			pdf_generator = SalesEstimatePDFGenerator(sales_estimate=se_to_print, target_file_path=temp_doc_path)
			if document_regenerate == "true" or se_document is None or se_to_print.documents is None or len(
					se_to_print.documents) < 1:
				logger.info('Document not persisted for Sales Estimate: %s' % se_id)
				document_pdf = pdf_generator.generatePDF(source=se_to_print, template_config=template_config)
				writeFile(document_pdf, temp_doc_path)
				if se_document is None:
					se_document = SEDocument(
						se_id=se_to_print.id, document_pdf=readFile(temp_doc_path),
						revised_on=se_to_print.last_modified_on,
						revised_by=se_to_print.approved_by, enterprise_id=enterprise_id)
			else:
				logger.info('Printed the Sales Estimate - %s' % se_to_print)
				writeFile(se_to_print.documents[0].document_pdf, temp_doc_path)
			for revised_doc in se_revisions:
				writeFile(revised_doc.document_pdf, temp_rev_doc_path)
				pdf_generator.addWaterMark(file(getAbsolutePath(CANCEL_WATERMARK_DOC_PATH), "rb"),
										   getAbsolutePath(temp_rev_doc_path))
				pdf_generator._appendToPDF(getAbsolutePath(temp_rev_doc_path), getAbsolutePath(temp_doc_path))
				logger.info("File to download: %s" % getAbsolutePath(temp_doc_path))
			if ((se_to_print.status == SalesEstimate.STATUS_CANCELLED and not se_document.is_rejected) or (
					se_to_print.status == SalesEstimate.STATUS_CLIENT_Rejected and not se_document.is_rejected)):
				# Adding Water-mark for the rejected SE for the first time
				logger.info(
					"Sales Estimate Status: %s Sales Estimate Doc Rejected Status: %s" % (
						se_to_print.status, se_document.is_rejected))
				SalesEstimatePDFGenerator.addCancelWaterMark(target_file_path=temp_doc_path)
				se_document.document_pdf = readFile(temp_doc_path)
				se_document.is_rejected = True
			if len(se_revisions) > 0 or not se_document:
				se_document = SEDocument(
					se_id=se_to_print.id, document_pdf=readFile(temp_doc_path))
			se_document.document_pdf = readFile(temp_doc_path)
			se_document.enterprise_id = enterprise_id
			if se_to_print.se_no and se_to_print.se_no != '0':
				logger.info('Persisting Document for Sales Estimate: %s' % se_to_print.se_no)
				self.sales_estimate_dao.db_session.add(se_document)
				self.sales_estimate_dao.db_session.commit()
			else:
				make_transient(se_document)
				make_transient(se_to_print)
				self.sales_estimate_dao.db_session.rollback()

			logger.info('Generating Rendering Sales Estimate PDF... %s' % se_to_print.id)
			return se_document
		except Exception as e:
			self.sales_estimate_dao.db_session.rollback()
			logger.exception('Sales Estimate PDF generation failed %s' % e.message)

	def deleteSEAttachmentFromFTP(self, enterprise_id=None, se_id=None, attachment_id=None):
		"""

		:param enterprise_id:
		:param se_id:
		:param attachment_id:
		:return:
		"""
		self.sales_estimate_dao.db_session.begin(subtransactions=True)
		try:
			self.sales_estimate_dao.db_session.query(SEAttachments).filter(
				SEAttachments.se_id == se_id, SEAttachments.enterprise_id == enterprise_id,
				SEAttachments.attachment_id == attachment_id).delete()
			attachment = self.sales_estimate_dao.db_session.query(Attachment).filter(
				Attachment.enterprise_id == enterprise_id, Attachment.attachment_id == attachment_id).first()
			# FTPUtil().delete(file_path=enterprise_id, filename=attachment.file)
			self.sales_estimate_dao.db_session.delete(attachment)
			self.sales_estimate_dao.db_session.commit()
		except Exception as e:
			self.sales_estimate_dao.db_session.rollback()
			logger.error("Could not fetch attachment %s " % e.message)
			raise e

	def notifySEReviewMessage(self, enterprise_id=None, sender_id=None, sales_estimate=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param sales_estimate:
		:return:
		"""
		try:
			message = PUSH_NOTIFICATION['sales_estimate_review'] % (
				sales_estimate.type, sales_estimate.getCode(), sales_estimate.supplier.name)
			push_notification(
				enterprise_id=sales_estimate.enterprise.id, sender_id=sender_id, module_code="SALES",
				message=message, code=sales_estimate.getCode(), type=sales_estimate.type)

			InvoiceService().notifyPendingSalesEstimateCount(enterprise_id=enterprise_id, sender_id=sender_id, code=sales_estimate.getCode(), type=sales_estimate.type)
		except Exception as e:
			logger.error("Notification failed... %s" % e.message)

	def clientStatusSalesEstimate(self, se_id=None, client_status=None, user_id=None, enterprise_id=None, remarks=None):
		"""
		
		:param se_id:
		:param client_status:
		:param user_id:
		:param enterprise_id:
		:param remarks:
		:return:
		"""
		db_session = SQLASession()
		db_session.begin(subtransactions=True)
		try:
			se_to_approve = db_session.query(SalesEstimate).filter(SalesEstimate.id == se_id).first()
			client_revised_on = datetime.datetime.now()

			logger.info("The Status of Sales Estimate :%s" % client_status)
			se_to_approve.status = client_status
			se_to_approve.client_revised_on = client_revised_on.strftime('%Y-%m-%d %H:%M:%S')
			user = getUser(enterprise_id=enterprise_id, user_id=user_id)
			se_to_approve.updateRemarks(remarks=remarks, user=user)

			db_session.add(se_to_approve)
			logger.debug('Sales Estimate in orm session: %s' % db_session.dirty)
			db_session.commit()
			SalesEstimateChangelog().queryInsert(
				user_id=user_id, enterprise_id=enterprise_id, data=se_to_approve)
			response = response_code.success()
			response['id'] = se_id
		except Exception as e:
			db_session.rollback()
			raise e
		return response
