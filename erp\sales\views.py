import json
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
from json import dumps, loads
import jwt

import requests
import simplejson
from cryptography.fernet import Fernet
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.template.response import TemplateResponse
from sqlalchemy import and_
from sqlalchemy.orm import make_transient
from sqlalchemy.sql.functions import func

from erp import properties, helper, dao
from erp.accounts.backend import AccountService
from erp.auth import SESSION_KEY, ENTERPRISE_IN_SESSION_KEY, USER_IN_SESSION_KEY, ENTERPRISE_ID_SESSION_KEY, \
	LOGIN_TOKEN, PRIMARY_ENTERPRISE_ID_SESSION_KEY
from erp.admin.backend import UserDAO
from erp.auth.request_handler import RequestHandler
from erp.dao import executeQuery
from erp.forms import InvoiceSearchForm
from erp.helper import populatePartyChoices, populateTaxChoices, \
	generateTaxJSONDump, saveEInvoiceAckData, EinvoiceJsonMaker, getStateList, constructDifferentMakeName, \
	populateAllProjects
from erp.masters.backend import MasterDAO, MasterService
from erp.models import Invoice, Tax, InvoiceTax, InvoiceMaterial, OA, OATag, \
	PurchaseOrderTag, Currency, PurchaseOrder, \
	InvoiceTemplateConfig, Receipt, ReceiptMaterial, Material, SETax, OATax, UnitMaster
from erp.properties import TEMPLATE_TITLE_KEY
from erp.sales import logger, SE_EXCLUDE_FIELD_LIST, SE_PARTICULAR_EXCLUDE_FIELD_LIST, SE_TAX_EXCLUDE_FIELD_LIST
from erp.sales.backend import InvoiceService, getCatalogueMaterials, getProjectForecast, getTotalCreditDebitValue, \
	ProjectForecastService, getProjectEnterpriseIdByProjectId, getTotalExpensesBudgetAmount, get_actual_cashflow_record, \
	get_start_end_date_from_month, getTotalRevenueBudgetAmount, updateIssuedQuantity, getJDCMaterials, getPPMaterials
from erp.sales.changelog import SalesEstimateChangelog, InvoiceChangeLog
from erp.sales.se_views import getSalesEstimate
from erp.stores.backend import StoresDAO, StoresService
from settings import SQLASession, CIPHER_KEY, GST_CLIENT_SECRET_KEY, GST_CLIENT_ID, GST_EMAIL
from util.api_util import JsonUtil, response_code
from util.helper import copyDictToDict, getMonthStartEndDate, getFinancialStartEndDate
from settings import JWT_SECRET, MongoDbConnect
from bson.objectid import ObjectId

__author__ = 'Kalaivanan, Saravanan'

FIELD_SEPARATOR_O = "%26"  # &
COMMA_SEPARATOR = "%2C"  # ","

# Key-String constants
INVOICE_KEY = 'invoice'
INVOICE_PK_FIELD_KEY = 'invoice_no'
INVOICE_MATERIAL_FORMSET_KEY = 'inv_material_formset'
INVOICE_NON_STOCK_FORMSET_KEY = 'inv_non_stock_material_formset'
INVOICE_CHARGES_FORMSET_KEY = 'invoice_charges'
INVOICE_LIST = 'inv_list'
INVOICE_TAX_LIST = 'tax_list'
INVOICE_TAX_LOAD = 'load_tax'
INVOICE_SEARCH = 'search'
EDIT_INV_ID = 'inv_edit_id'
PARTY_KEY = 'party'
INVOICE_TAG = 'tag'
INVOICE_TAGS_FORMSET = 'tags_formset'
INVOICE_MISC_TEMPLATE = 'inv_template_misc'
PROMOTE_SE_ID = 'promote_inv_se_id'
PROMOTE_SE_CODE = 'promote_inv_se_code'

NON_EXCISE_INV_KEY = 'non_excise_invoice'
NON_EXCISE_INV_MATERIAL_FORMSET_KEY = 'non_excise_material_formset'
NON_EXCISE_INV_TAX_LIST = 'non_excise_tax_list'
NON_EXCISE_INV_TAX_LOAD = 'non_excise_load_tax'

MATERIAL_FORM_KEY = 'material_form'
BILL_OF_MATERIALS_FORMSET_KEY = 'bill_of_materials_formset'
SPECIFICATION_FORMSET_KEY = 'specifications_formset'
MATERIAL_MAKE_FORMSET_KEY = 'material_makes_formset'
PRICE_OF_SUPP_MATERIALS_FORMSET_KEY = 'materials_party_price_formset'
ALTERNATE_UNIT_FORMSET_KEY = 'alternate_unit_formset'


class DatetimeEncoder(json.JSONEncoder):
	def default(self, obj):
		try:
			return super(DatetimeEncoder, obj).default(obj)
		except TypeError:
			return str(obj)


def manageSalesHome(request):
	"""
	Sales dashboard
	:param request:
	:return:
	"""
	try:
		accounts_service = AccountService()
		response = {}
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		logger.info('Fetching dashboard data for enterprise %s' % enterprise_id)
		since, till = accounts_service.getSelectFyDateRange(enterprise_id=enterprise_id)
		response["dashboard"] = accounts_service.getDashboardEntries(
			enterprise_id=enterprise_id, since=since, till=till)
		response['sales_revenue'] = accounts_service.salesRevenueChartRender(enterprise_id=enterprise_id)
		response["aging"] = accounts_service.getReceivableAging(enterprise_id=enterprise_id, list_unsettled_particulars=True)
	except Exception as e:
		logger.exception("Failed fetching account dashboard entries. %s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message
	return TemplateResponse(request=request, template='sales/home.html', context={
		"response": response["dashboard"], "response_bills": response["aging"],
		"response_sales_revenue": dumps(loads(dumps(response["sales_revenue"]))),
		"fiscal_start_date": since, TEMPLATE_TITLE_KEY: "Sales"})


def getOADueToday(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	oa_due_query = """SELECT 
					    IF(order_acknowledgement.oa_no IS NOT NULL,
					        IF(order_acknowledgement.status > 0,
					            CONCAT(order_acknowledgement.financial_year,
					                    '/',
					                    LEFT(order_acknowledgement.type, 1),
					                    '/',
					                    LPAD(order_acknowledgement.oa_no, 5, 0),
					                    IFNULL(order_acknowledgement.sub_number, '')),
					            '-NA-'),
					        '-NA-') AS oa_code,
					    party_master.party_name,
					    order_acknowledgement.grand_total,
					    order_acknowledgement.id,
					    IFNULL(order_acknowledgement.delivery_due_date,
					            NOW()) AS due_date,
					    SUM(IFNULL(oa_particulars.quantity, 0)) AS oa_qty,
					    IFNULL(invoice_materials.qty, 0) AS in_qty
					FROM
					    order_acknowledgement
					        LEFT JOIN
					    (oa_particulars) ON oa_particulars.oa_id = order_acknowledgement.id
					        AND oa_particulars.enterprise_id = order_acknowledgement.enterprise_id
					        LEFT JOIN
					    (SELECT 
					        invoice_materials.invoice_id,
					            invoice_materials.oa_no,
					            invoice_materials.enterprise_id,
					            SUM(invoice_materials.qty) AS qty,
					            invoice.status
					    FROM
					        invoice_materials
					    INNER JOIN invoice ON invoice.id = invoice_materials.invoice_id
					        AND invoice.enterprise_id = invoice_materials.enterprise_id
					    WHERE
					        invoice.status IN (0 , 1)
					            AND invoice_materials.oa_no IS NOT NULL
					            AND invoice_materials.oa_no != ''
					            AND invoice.enterprise_id = %s
					    GROUP BY invoice_materials.oa_no) invoice_materials ON invoice_materials.oa_no = order_acknowledgement.id
					        AND invoice_materials.enterprise_id = order_acknowledgement.enterprise_id
					        LEFT JOIN
					    (party_master) ON party_master.party_id = order_acknowledgement.party_id
					        AND party_master.enterprise_id = order_acknowledgement.enterprise_id
					WHERE
					    order_acknowledgement.enterprise_id = %s
					        AND order_acknowledgement.status = 1
					GROUP BY order_acknowledgement.id
					HAVING DATE(due_date) < CURDATE() AND oa_qty > in_qty
					""" % (enterprise_id, enterprise_id)
	response = []
	try:
		for idx, item in enumerate(executeQuery(oa_due_query)):
			data = {}
			data['oa_code'] = item[0]
			data['party'] = item[1]
			data['oa_value'] = item[2]
			data['oa_id'] = item[3]
			data['due_date'] = datetime.strptime(str(item[4]), '%Y-%m-%d %H:%M:%S').strftime('%b %d,%Y') \
				if item[4] is not None else datetime.now().strftime('%Y-%m-%d %H:%M:%S')
			response.append(data)
	except Exception as e:
		logger.exception(e.message)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getRecentCustomers(request):
	"""

	:param request:
	:return:
	"""
	response = []
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	oa_due_query = """SELECT 
		    party_master.party_name,
		    MIN(invoice.issued_on) AS since,
		    SUM(invoice.grand_total) AS total
		FROM
		    ((SELECT 
		        IFNULL(invoice.issued_on, NOW()) AS issued_on,
		        invoice.grand_total AS grand_total,
		        invoice.enterprise_id AS enterprise_id,
		        invoice.party_id AS party_id
		    FROM
		        invoice
		        WHERE invoice.type in ('Excise', 'Service', 'Trading', 'GST', 'BoS')
		        AND invoice.status = 1) UNION ALL (SELECT 
		        order_acknowledgement.prepared_on AS issued_on,
		            0 AS grand_total,
		            order_acknowledgement.enterprise_id AS enterprise_id,
		            order_acknowledgement.party_id AS party_id
		    FROM
		        order_acknowledgement
		        WHERE order_acknowledgement.status = 1)) AS invoice
		        LEFT JOIN
		    (party_master) ON party_master.party_id = invoice.party_id
		        AND party_master.enterprise_id = invoice.enterprise_id
		WHERE
		    invoice.party_id IS NOT NULL
		        AND invoice.enterprise_id = '%s'
		GROUP BY invoice.party_id
		ORDER BY since DESC
		LIMIT 10""" % enterprise_id
	try:
		for idx, item in enumerate(executeQuery(oa_due_query)):
			data = {}
			data['party'] = item[0]
			data['since'] = datetime.strptime(str(item[1]), '%Y-%m-%d %H:%M:%S').strftime('%b %d,%Y')
			data['value'] = item[2]
			response.append(data)
	except Exception as e:
		logger.exception(e.message)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getTopGeographies(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	type = request_handler.getPostData("type")
	query_reqion = 'party_master.city' if type == 'city' else 'party_master.state'
	since = datetime.strptime(request_handler.getPostData("since") + " 00:00:00", "%Y-%m-%d %H:%M:%S")
	till = datetime.strptime(request_handler.getPostData("till") + " 23:59:59", "%Y-%m-%d %H:%M:%S")
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	region_query = """SELECT 		    		    		    
		    %s,
		    SUM(invoice.grand_total) AS total,
		    LOWER(TRIM(%s)) AS region
		FROM
		    invoice
		        LEFT JOIN
		    (ledger_bills) ON ledger_bills.id = invoice.ledger_bill_id
		        AND ledger_bills.enterprise_id = invoice.enterprise_id
		        RIGHT JOIN
		    (voucher) ON voucher.id = ledger_bills.voucher_id
		        AND voucher.enterprise_id = ledger_bills.enterprise_id
		        AND voucher.status = 1
		        LEFT JOIN
		    (party_master) ON party_master.party_id = invoice.party_id
		        AND party_master.enterprise_id = invoice.enterprise_id
		WHERE
		        %s <> ''
		        AND invoice.enterprise_id = %s
		        AND invoice.status = 1
		        AND invoice.type IN ('Excise' , 'Service', 'Trading', 'GST', 'BoS')
		        AND invoice.approved_on BETWEEN '%s' AND '%s'
		GROUP BY region
		ORDER BY total DESC""" % (query_reqion, query_reqion, query_reqion, enterprise_id, since, till)
	try:
		response = []
		for idx, item in enumerate(executeQuery(region_query)):
			data = {}
			data['region'] = item[0]
			data['total'] = item[1]
			response.append(data)
	except Exception as e:
		logger.exception("Failed fetching account dashboard entries. %s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message

	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getTopSalesCustomers(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	since = datetime.strptime(request_handler.getPostData("since") + " 00:00:00", "%Y-%m-%d %H:%M:%S")
	till = datetime.strptime(request_handler.getPostData("till") + " 23:59:59", "%Y-%m-%d %H:%M:%S")
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	customer_query = """SELECT 		    		    		    
		    party_master.party_name,
		    SUM(invoice.grand_total) AS total		    
		FROM
		    invoice
		        LEFT JOIN
		    (ledger_bills) ON ledger_bills.id = invoice.ledger_bill_id
		        AND ledger_bills.enterprise_id = invoice.enterprise_id
		        RIGHT JOIN
		    (voucher) ON voucher.id = ledger_bills.voucher_id
		        AND voucher.enterprise_id = ledger_bills.enterprise_id
		        AND voucher.status = 1
		        LEFT JOIN
		    (party_master) ON party_master.party_id = invoice.party_id
		        AND party_master.enterprise_id = invoice.enterprise_id
		WHERE		        
		        invoice.enterprise_id = %s
		        AND invoice.status = 1
		        AND invoice.type IN ('Excise' , 'Service', 'Trading', 'GST', 'BoS')
		        AND invoice.approved_on BETWEEN '%s' AND '%s'
		GROUP BY party_master.party_id
		ORDER BY total DESC""" % (enterprise_id, since, till)
	try:
		response = {'sales_top_customers': {}, 'sales_top_customers_total': 0}
		closing_value = 0
		data = []
		for idx, item in enumerate(executeQuery(customer_query)):
			data.append({'name': item[0], 'closing_balance': item[1]})
			closing_value = closing_value + item[1]
		response['sales_top_customers'] = data
		response['sales_top_customers_total'] = closing_value
	except Exception as e:
		logger.exception("Failed fetching account dashboard entries. %s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message

	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getRecentSoldItems(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	oa_due_query = """SELECT     	
			IF(materials.name IS NOT NULL, IFNULL(materials.drawing_no, ''),''),
		    materials.name,
		    invoice_materials.qty,		    		    
		    IFNULL(invoice.invoice_code,'-NA-') AS invoice_no,
		    IFNULL(invoice.issued_on, NOW()) AS issued_on,
		    IF(order_acknowledgement.oa_no IS NOT NULL,
		        IF(order_acknowledgement.status > 0,
		            CONCAT(order_acknowledgement.financial_year,
		                    '/',
		                    LEFT(order_acknowledgement.type, 1),
		                    '/',
		                    LPAD(order_acknowledgement.oa_no, 5, 0),
		                    IFNULL(order_acknowledgement.sub_number, '')),
		            '-NA-'),
		        '-NA-') AS oa_code,
		    party_master.party_name,
		    IF(invoice_materials.alternate_unit_id IS NOT NULL, um2.unit_name , IF(um.unit_name IS NOT NULL, um.unit_name, '-NA-')),
		    materials.makes_json as make_name    
		FROM
		    `invoice`
		        LEFT JOIN
		    (((SELECT 
		        invoice_id,
		            item_id as item_code,
		            hsn_code,
		            qty,
		            NULL AS unit_id,
		            unit_rate,
		            discount,
		            remarks,
		            enterprise_id,
		            make_id,
		            is_faulty,
		            oa_no,
		            is_returnable,
		            alternate_unit_id
		    FROM
		        invoice_materials)) AS invoice_materials) ON invoice_materials.invoice_id = invoice.id
		        AND invoice_materials.enterprise_id = invoice.enterprise_id
		        LEFT JOIN 
		         unit_master um2 ON um2.unit_id = invoice_materials.alternate_unit_id
		         AND um2.enterprise_id = invoice_materials.enterprise_id
		        LEFT JOIN
		    (materials) ON materials.id = invoice_materials.item_code
		        AND materials.enterprise_id = invoice_materials.enterprise_id
		        LEFT JOIN 
		         unit_master um ON um.unit_id = materials.unit
		         AND um.enterprise_id = materials.enterprise_id		        
		        LEFT JOIN order_acknowledgement ON order_acknowledgement.id = invoice_materials.oa_no 
		        AND order_acknowledgement.enterprise_id = invoice_materials.enterprise_id  
		        LEFT JOIN
		    (party_master) ON party_master.party_id = invoice.party_id
		        AND party_master.enterprise_id = invoice.enterprise_id
		WHERE
		    invoice.enterprise_id = '%s'
		    AND invoice_materials.item_code is not null
		    AND invoice.status = 1
		    AND invoice.type IN ('GST' , 'Trading', 'Service', 'BoS', 'Excise')
		GROUP BY invoice.id, invoice_materials.item_code, invoice_materials.make_id
		ORDER BY invoice.approved_on DESC
		LIMIT 10
		""" % enterprise_id
	try:
		response = []
		logger.debug('%s' % oa_due_query)
		for idx, item in enumerate(executeQuery(oa_due_query)):
			data = {}
			make_name = helper.constructDifferentMakeName(item[8])
			if item[0] != '':
				data['material'] = "%s - %s" % (item[0], item[1])
			else:
				data['material'] = "%s" % (item[1])
			if make_name:
				data['material'] += " [%s]" % make_name
			data['qty'] = item[2]
			data['invoice_no'] = item[3]
			data['issued_on'] = datetime.strptime(str(item[4]), '%Y-%m-%d %H:%M:%S').strftime('%b %d,%Y')
			data['oa_code'] = item[5]
			data['party'] = item[6]
			data['unit'] = item[7]
			response.append(data)
	except Exception as e:
		logger.exception(e.message)
		response = response_code.internalError()
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getOAOverDue(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	grn_query = """SELECT 
		    IF(order_acknowledgement.oa_no IS NOT NULL,
		        IF(order_acknowledgement.status > 0,
		            CONCAT(order_acknowledgement.financial_year,
		                    '/',
		                    LEFT(order_acknowledgement.type, 1),
		                    '/',
		                    LPAD(order_acknowledgement.oa_no, 5, 0),
		                    IFNULL(order_acknowledgement.sub_number, '')),
		            '-NA-'),
		        '-NA-') AS oa_code,
		    party_master.party_name,
		    IFNULL(order_acknowledgement.delivery_due_date, NOW()) AS due_date,
		    order_acknowledgement.grand_total,
		    order_acknowledgement.id
		FROM
		    (order_acknowledgement)
		        LEFT JOIN
		    (oa_particulars) ON oa_particulars.oa_id = order_acknowledgement.id
		        LEFT JOIN
		    (party_master) ON party_master.party_id = order_acknowledgement.party_id
		        LEFT JOIN
		    (((SELECT 
		        invoice_id,
		            item_id as item_code,
		            hsn_code,
		            qty,
		            NULL AS unit_id,
		            unit_rate,
		            discount,
		            remarks,
		            enterprise_id,
		            make_id,
		            is_faulty,
		            oa_no,
		            is_returnable
		    FROM
		        invoice_materials) UNION ALL (SELECT 
		        invoice_id,
		            item_name AS item_code,
		            hsn_code,
		            1 AS qty,
		            NULL AS unit_id,
		            rate AS unit_rate,
		            discount,
		            NULL AS remarks,
		            enterprise_id,
		            NULL AS make_id,
		            NULL AS is_faulty,
		            NULL AS oa_no,
		            NULL AS is_returnable
		    FROM
		        invoice_charges)) AS invoice_materials) ON invoice_materials.oa_no = order_acknowledgement.id
		        AND invoice_materials.enterprise_id = order_acknowledgement.enterprise_id
		WHERE
		    order_acknowledgement.enterprise_id = '%s'
		        AND order_acknowledgement.status = 1
		        AND DATE(order_acknowledgement.delivery_due_date) < CURDATE()
		        AND oa_particulars.quantity > IFNULL(invoice_materials.qty, 0)
		GROUP BY order_acknowledgement.id
		ORDER BY order_acknowledgement.delivery_due_date asc""" % enterprise_id
	try:
		response = []
		for idx, item in enumerate(executeQuery(grn_query)):
			data = {}
			data['oa_code'] = item[0]
			data['party'] = item[1]
			data['due_date'] = datetime.strptime(str(item[2]), '%Y-%m-%d %H:%M:%S').strftime('%b %d,%Y')
			data['oa_value'] = item[3]
			data['oa_id'] = item[4]
			response.append(data)
	except Exception as e:
		logger.exception(e.message)
		response = response_code.internalError()
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getChartBySales(char_data):
	"""
	Desighn a chart
	:return:
	"""
	val_title = []
	val_result = []
	val_title.append([])
	val_title[0].append("Month")
	val_title[0].append("Ontime")
	val_title[0].append("Delayed")
	index = 0
	for idx, item in enumerate(char_data):
		if len(item) > 3:
			val_result.append([])
			val_result[index].append(item['month'])
			val_result[index].append(item['on_time'])
			val_result[index].append(item['delayed'])
			index = index + 1
	val_title.extend(val_result)
	return val_title


def getPaymentCollection(request):
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		logger.info('Fetching Sales dashboard data...')
		response = response_code.success()
		response.update(InvoiceService().formDashboardData(enterprise_id=enterprise_id))

		response['payment_collection'] = getChartBySales(response['payment_collection'])
		response['sales_performance'] = getChartBySales(response['sales_performance'])
	except Exception as e:
		logger.exception(e.message)
		response = response_code.internalError()
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def jobDcSupplied(request):
	dc_materials_list = ()
	try:
		request_handler = RequestHandler(request)
		job_po_id = request_handler.getPostData('jop_po_id')
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		logger.debug("job_po_id : %s" % job_po_id)
		dc_list_orm = SQLASession().query(
			InvoiceMaterial.invoice_id, Material.drawing_no.label('item_code'), InvoiceMaterial.hsn_code, Invoice.sub_number,
			InvoiceMaterial.quantity, InvoiceMaterial.rate, InvoiceMaterial.discount, InvoiceMaterial.remarks,
			Invoice.financial_year, Invoice.invoice_code, Invoice.invoice_no, Invoice.type, Material.makes_json.label('make'),
			Material.name.label('item_name'), InvoiceMaterial.is_faulty, Material.is_stocked.label('is_stock'),
			Material.is_service.label('is_service'), UnitMaster.unit_name.label('uom')
		).outerjoin(InvoiceMaterial.invoice).outerjoin(InvoiceMaterial.item).outerjoin(
			UnitMaster, and_(UnitMaster.enterprise_id == enterprise_id, UnitMaster.unit_id == Material.unit_id)).filter(
			Invoice.enterprise_id == enterprise_id, Invoice.job_po_id == job_po_id, Invoice.status > -1).all()

		dc_materials_list = []
		for dc in dc_list_orm:
			dc_dict = dc._asdict()
			dc_dict['item_code'] = '%s - %s' % (dc.item_code, dc.item_name) if dc.item_code else dc.item_name
			dc_dict['code'] = Invoice.generateInternalCode(
				invoice_id=dc.invoice_id, invoice_no=dc.invoice_no, invoice_type=dc.type,
				financial_year=dc.financial_year, sub_number=dc.sub_number, invoice_code=dc.invoice_code)
			dc_materials_list.append(dc_dict)

	except Exception as e:
		logger.exception("Fetching DCs are Failed %s" % e.message)
	logger.info("No of material:%s" % len(dc_materials_list))
	return HttpResponse(content=json.dumps(dc_materials_list, cls=DatetimeEncoder), mimetype='application/json')


def jobDcUsage(request):
	"""

	:param request:
	:return:
	"""
	logger.info("The Edit PO Loaded...")
	rh = RequestHandler(request)
	po_id = rh.getPostData('po_id')
	response = []
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		logger.info("Fetching purchase order material for {enterprise: %s, po_id:%s}" % (enterprise_id, po_id))
		store_dao = StoresDAO()
		invoice_dao = InvoiceService()
		po_materials = invoice_dao.getPOMaterials(enterprise_id=enterprise_id, po_id=po_id)
		invoice_materials = invoice_dao.getInvoiceMaterials(enterprise_id=enterprise_id, po_id=po_id)
		po_details, multi_bom_mat = invoice_dao.getClubbedCatalogue(materials=po_materials, enterprise_id=enterprise_id)
		if len(multi_bom_mat) > 0:
			po_details, invoice_materials = calcMultiBOMMat(multi_bom_mat, po_details, invoice_materials)
		po_details, invoice_materials = calcJDCBOMMat(po_details, invoice_materials)

		for material in invoice_materials:
			material.update({'actual_qty': 0})
			# FIXME performance issue while accessing item.material in loop; Consider 250 materials in a PO;
			if material['cat_code']:
				catalogues = store_dao.getCatalogues(material['cat_code'], enterprise_id)
				catalogues_child = 1 if len(catalogues) > 0 else 0
				make_list = [(material['make_id'], material['make_name'])]
			else:
				catalogues_child = ""
				make_list = ""
			material['child'] = catalogues_child
			material['makes'] = make_list
			material['actual_qty'] = material['qty']
			material['supplied'] = material['qty'] if material['qty'] else 0
			material['qty'] = 0
			material['returned'] = 0
			po_details.append(material)
		response = invoice_dao.getCalcActualQty(po_id=po_id, po_material_lst=po_details, job_type='PO')
	except Exception as e:
		logger.exception("Tax retrieval fails... %s" % e.message)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def calcMultiBOMMat(multi_bom_mat, po_details, invoice_materials):
	"""

	:return:
	"""
	for idx, i_mat in enumerate(invoice_materials):
		i_mat.update({'del': 0})
		for mat in po_details:
			if len(mat['child']) > 0:
				for item in mat['child']:
					if item['item_id'] in multi_bom_mat:
						for multi_mat in multi_bom_mat:
							if item['item_id'] == multi_mat:
								if item['item_id'] == i_mat['item_id'] and len(set(multi_bom_mat[multi_mat][0]).intersection({i_mat['make_id']}))>0:
									if len(set(item['make_id'].split(",")).intersection({i_mat['make_id']}))>0:
										item['actual_qty'] += round(item['qty'] * (i_mat['qty'] / multi_bom_mat[multi_mat][1]), 2)
										item['supplied'] += round(item['qty'] * (i_mat['qty'] / multi_bom_mat[multi_mat][1]), 2)
										i_mat.update({'del': 1})
	for idx, item in enumerate(invoice_materials):
		if item['del'] == 1:
			del invoice_materials[idx]
	return po_details, invoice_materials


def calcJDCBOMMat(po_details, invoice_materials):
	"""

	:param po_details:
	:param invoice_materials:
	:return:
	"""
	for mat in po_details:
		if len(mat['child']) > 0:
			for item in mat['child']:
				if "," in item['make_id']:
					make_array = item['make_id'].split(",")
					for make in make_array:
						for idx, i_mat in enumerate(invoice_materials):
							if item['item_id'] == i_mat['item_id'] and i_mat['make_id'] == int(make) and i_mat['is_faulty'] == item['is_faulty']:
								item['actual_qty'] += i_mat['qty']
								item['supplied'] += i_mat['qty']
								del invoice_materials[idx]
				else:
					for idx, i_mat in enumerate(invoice_materials):
						if item['item_id'] == i_mat['item_id'] and i_mat['make_id'] == int(item['make_id']) and i_mat['is_faulty'] == item['is_faulty']:
							item['actual_qty'] += i_mat['qty']
							item['supplied'] += i_mat['qty']
							del invoice_materials[idx]
		if mat['item_id'] != "-NA-":
			for idx, i_mat in enumerate(invoice_materials):
				if mat['item_id'] == i_mat['item_id'] and i_mat['make_id'] == mat['make_id'] and i_mat['is_faulty'] == mat['is_faulty']:
					if 'actual_qty' not in mat.keys():
						mat.update({'actual_qty': 0})
						mat.update({'supplied': 0})
					mat['actual_qty'] += i_mat['qty']
					mat['supplied'] += i_mat['qty']
					del invoice_materials[idx]
		else:
			for idx, i_mat in enumerate(invoice_materials):
				if mat['name'].strip() == i_mat['name'].strip():
					if 'actual_qty' not in mat.keys():
						mat.update({'actual_qty': 0})
						mat.update({'supplied': 0})
					mat['actual_qty'] += i_mat['qty']
					mat['supplied'] += i_mat['qty']
					del invoice_materials[idx]

	return po_details, invoice_materials


def returnDcMaterial(request):
	dc_materials_list = []
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		dc_id = request_handler.getPostData('dc_id')
		dc_materials_list = InvoiceService().getReturnedItems(enterprise_id=enterprise_id, dc_id=dc_id)
	except Exception as e:
		logger.info("Fetching DCs are Failed:%s" % e.message)
		logger.debug(e)
	return HttpResponse(content=json.dumps(dc_materials_list, cls=DatetimeEncoder), mimetype='application/json')


def loadIssueList(request):
	invoice_type = "internal"
	return loadInvoiceList(request, invoice_type=invoice_type, edit_link=properties.ISSUE_EDIT_URL)


def loadDcList(request):
	invoice_type = "dc"
	return loadInvoiceList(request, invoice_type=invoice_type, edit_link=properties.DC_EDIT_URL)


def loadInvoiceList(request, invoice_type="sales", edit_link=properties.INVOICE_EDIT_URL):
	# Load Invoice List
	request_handler = RequestHandler(request)
	logger.info("The Load Issue Triggered - %s..." % invoice_type)
	from_date, to_date = JsonUtil.getDateRange(
		rh=request_handler, since_session_key='%s.since' % invoice_type, till_session_key='%s.till' % invoice_type)
	# Get the Enterprise Id
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	primary_enterprise_id = request_handler.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
	isprimary_project = enterprise_id == primary_enterprise_id
	try:

		party_id = request_handler.getAndCacheData(key="party_name", session_key="%s.party_name" % invoice_type)
		status = request_handler.getAndCacheData(key="status", session_key="%s.status" % invoice_type)
		project = request_handler.getAndCacheData(key="project", session_key="%s.project" % invoice_type)

		party_id = party_id if party_id else -1
		status = status if status else ""
		project = project if project else ""
		type_ = request_handler.getData("type")  # TODO fix with invoice_type
		if request_handler.getData("view") == 'pending' and type_ in ("dc", "sales"):
			initial_date = SQLASession().query(Invoice.prepared_on).filter(
				Invoice.enterprise_id == enterprise_id, Invoice.type.in_(Invoice.TYPES[type_])).order_by(
				'prepared_on').first()
			from_date = initial_date[0].strftime('%Y-%m-%d')
			status = '0'
		query = """SELECT gst_username, gst_password from enterprise WHERE id='%s'""" % enterprise_id
		enterprise_credentials = executeQuery(query)
		is_gst_credentials = True if enterprise_credentials[0][0] is not None and len(enterprise_credentials[0][0]) > 0 else False
		# TODO Optimized Raw query to fetch the invoice details process.
		invoices = searchInvoice(
				enterprise_id=enterprise_id, from_date=from_date, to_date=to_date, party_id=party_id,
				status=status, type=invoice_type, project=project)
		for invoice in invoices:
			invoice.remarks_as_text = json.dumps(invoice.remarks)
		context = {
			INVOICE_LIST: invoices,
			INVOICE_TAX_LOAD: populateTaxChoices(enterprise_id),
			PARTY_KEY: populatePartyChoices(enterprise_id),
			INVOICE_SEARCH: InvoiceSearchForm(enterprise_id=enterprise_id, type=invoice_type, initial={
				'to_date': to_date, 'from_date': from_date, 'party_name': party_id, 'status': status,
				'project': project}), 'type': invoice_type, 'is_gst_credentials': is_gst_credentials,
			TEMPLATE_TITLE_KEY: "Internal Invoice" if not isprimary_project else ("Invoice" if invoice_type == "sales" else (
				"Delivery Challan" if invoice_type == "dc" else "Issue")), 'edit_link': edit_link, "isprimary_project":isprimary_project}
		return TemplateResponse(template=properties.INVOICE_LIST_TEMPLATE, request=request, context=context)
	except Exception as e:
		logger.exception("Loading Invoice Interrupted...\n%s" % e.message)
		raise


def getSalesEstimateLogList(request):
	"""

	:param request:
	:return:
	"""
	response = {}
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	se_id = request_handler.getPostData('se_id')
	offset = request_handler.getPostData('offset')
	limit = request_handler.getPostData('limit')
	try:
		response = SalesEstimateChangelog().fetchLogList(
			se_id=se_id, enterprise_id=enterprise_id, offset=offset, limit=limit)
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')

def getInvoiceLogList(request):
	response = {}
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	se_id = request_handler.getPostData('se_id')
	offset = request_handler.getPostData('offset')
	limit = request_handler.getPostData('limit')
	try:
		response = InvoiceChangeLog().fetchLogList(
			se_id=se_id, enterprise_id=enterprise_id, offset=offset, limit=limit)
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getSalesEstimateLogData(request):
	"""
	get changelog data from a specified voucher timeset
	:param request:
	:return:
	"""
	response = {}
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	se_id = request_handler.getPostData('se_id')
	modified_at = request_handler.getPostData('modified_at')
	try:
		response = SalesEstimateChangelog().fetchLogData(se_id=se_id, enterprise_id=enterprise_id, modified_at=modified_at)
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')

def getInvoiceLogData(request):
	"""
	get changelog data from a specified voucher timeset
	:param request:
	:return:
	"""
	response = {}
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	se_id = request_handler.getPostData('se_id')
	modified_at = request_handler.getPostData('modified_at')
	try:
		response = InvoiceChangeLog().fetchLogData(se_id=se_id, enterprise_id=enterprise_id, modified_at=modified_at)
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getInvoiceEinvoiceList(request):
	"""

	:param request:
	:return:
	"""
	response = {}
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		query = """select  
					i.id,
					p.party_name,
					IFNULL(i.invoice_code, CONCAT(i.financial_year, '/', SUBSTR(i.type, 1, 1), LPAD(i.invoice_no, 6, 0), IFNULL(i.sub_number, ''))) AS invoice_code,
					i.grand_total 
				FROM invoice i 
				LEFT JOIN party_master p ON p.party_id = i.party_id
					AND p.enterprise_id = i.enterprise_id
				WHERE i.enterprise_id = '%s' AND i.issued_on  > now() - interval 720 hour 
				AND i.type IN ('GST', 'Trading', 'Service', 'BoS', 'Excise') 
				AND i.status > 0 AND (i.irn_ack_json IS NULL OR TRIM(i.irn_ack_json) = 'null') ORDER BY i.id DESC""" % enterprise_id
		result = dao.executeQuery(query)
		logger.debug("Load Invoice Query:%s" % query)
		response = result
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getGSTCred(enterprise_id=None):
	"""

	:param enterprise_id:
	:return:
	"""
	response = {}
	try:
		query = """SELECT e.gst_username, e.gst_password, e.gst_auth_token, erd.details as gstin from \
						enterprise as e LEFT JOIN enterprise_registration_detail as erd ON erd.enterprise_id = e.id AND \
						erd.label='GSTIN' WHERE id= '%s' GROUP BY e.id""" % enterprise_id
		result = executeQuery(query, as_dict=True)
		response['gst_username'] = result[0]['gst_username'] if result[0]['gst_username'] is not None and len(result[0]['gst_username']) > 0 else None
		response['gst_password'] = result[0]['gst_password'] if result[0]['gst_password'] is not None and len(result[0]['gst_password']) > 0 else None
		response['gst_auth_token'] = result[0]['gst_auth_token'] if result[0]['gst_auth_token'] is not None and len(result[0]['gst_auth_token']) > 0 else None
		response['gstin'] = result[0]['gstin'] if result[0]['gstin'] is not None and len(result[0]['gstin']) > 0 else None
		return response if all(response.values()) else False
	except Exception as e:
		logger.exception(e)
		return False


def updateIrnResponse(request):
	"""
	get changelog data from a specified voucher timeset
	:param request:
	:return:
	"""
	response = []
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	invoice_id = request_handler.getPostData('invoice_id')
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	uploadIrnRequest = request.FILES['uploadIrnRequest']
	data = uploadIrnRequest.read()
	data = data.replace('\"{', '{')
	data = data.replace('}"', '}')
	data = simplejson.loads(data)
	try:
		invoice_service = InvoiceService()
		invoice = invoice_service.invoice_dao.getInvoice(
			enterprise_id=enterprise_id, invoice_id=invoice_id if invoice_id != "" else None)
		db_session = invoice_service.invoice_dao.db_session
		saveEInvoiceAckData(entity=invoice, ack_data=data, user_id=user_id, db_session=db_session)
		response_data = {'result': "GSTR request succeeds"}
		response.append(response_data)

	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def cancelIrn(request):
	"""

	:param request:
	:return:
	"""
	response = []
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		invoice_id = request_handler.getPostData('invoice_id')
		gst_credentials = getGSTCred(enterprise_id=enterprise_id)
		query = """SELECT IFNULL(invoice_code, CONCAT(financial_year, '/', SUBSTR(type, 1, 1), LPAD(invoice_no, 6, 0), IFNULL(sub_number, ''))) invoice_code, irn_ack_json FROM invoice WHERE irn_ack_json is not NULL and irn_ack_json != 'null' AND id = %s AND enterprise_id = %s""" % (invoice_id, enterprise_id)
		result = dao.executeQuery(query, as_dict=True)
		if len(result) > 0 and gst_credentials:
			cipher_suite = Fernet(CIPHER_KEY)
			byte_string = b'%s' % str(gst_credentials['gst_password'])
			gst_username = str(gst_credentials['gst_username'])
			gst_password = cipher_suite.decrypt(byte_string)
			auth_token = str(gst_credentials['gst_auth_token'])
			email = GST_EMAIL
			gstin = str(gst_credentials['gstin'])
			data = """{"Irn": "%s", "CnlRsn": "1", "CnlRem": "Wrong entry"}""" % str(simplejson.loads(result[0]['irn_ack_json'])['Irn'])
			invoice_code = str(result[0]['invoice_code'])
			ip_address = request.get_host()
			if not len(auth_token) > 0:
				auth_token = getGSTAuthToken(username=gst_username, password=gst_password, ip_addr=ip_address, gstin=gstin, email=email, enterprise_id=enterprise_id)
			res = postIrnRequest(gst_username=gst_username, auth_token=auth_token, email=email, gstin=gstin, data=data, ip_address=ip_address)
			if len(res.content) > 0:
				if res.json()['status_cd'] != '0':
					response_data = {'invoice_code': invoice_code, 'result': "GSTR request succeeds"}
					response.append(response_data)
				else:
					for error in json.loads(res.json()['status_desc']):
						if error['ErrorCode'] == '1005':
							auth_token = getGSTAuthToken(username=gst_username, password=gst_password, ip_addr=ip_address, gstin=gstin, email=email, enterprise_id=enterprise_id)
							res = postIrnRequest(gst_username=gst_username, auth_token=auth_token, email=email, gstin=gstin, data=data, ip_address=ip_address)
							if len(res.content) > 0:
								if res.json()['status_cd'] != '0':
									response_data = {'invoice_code': invoice_code, 'result': "GSTR request succeeds"}
									response.append(response_data)
						else:
							response_data = {'invoice_code': invoice_code, 'result': res.json()['status_desc']}
							response.append(response_data)
			else:
				auth_token = getGSTAuthToken(username=gst_username, password=gst_password, ip_addr=ip_address, gstin=gstin, email=email, enterprise_id=enterprise_id)
				res = postIrnRequest(gst_username=gst_username, auth_token=auth_token, email=email, gstin=gstin, data=data, ip_address=ip_address)
				response = response_code.success() if len(res.content) > 0 and res.json()['status_cd'] != '0' else response
	except Exception as e:
		response = response_code.failure()
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def postIrnRequest(gst_username=None, auth_token=None, email=None, gstin=None, data=None, ip_address=None):
	"""

	:param gst_username:
	:param auth_token:
	:param data:
	:return:
	"""
	try:
		irn_headers = {
				'ip_address': ip_address, 'client_id': GST_CLIENT_ID,
				'client_secret': GST_CLIENT_SECRET_KEY, 'username': gst_username,
				'auth-token': '%s' % auth_token, 'gstin': gstin, 'Content-type': 'application/json'}
		url = 'https://api.mastergst.com/einvoice/type/CANCEL/version/V1_03?email=%s' % email
		response = requests.post(url, headers=irn_headers, data=data)
	except Exception as e:
		logger.exception(e)
	return response

def getEinvoiceJson(request):
	"""

	:param request:
	:return:
	"""

	response = []
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		invoice_list = request.POST.getlist('invoice_list[]')
		if 'on' in invoice_list:
			invoice_list.remove('on')
		invoice_lst_str = ','.join(invoice_list)
		query = """SELECT 
					i.id AS invoice_id,
					"1.1" AS Version,
					"GST" AS TaxSch,
					"B2B" AS SupTyp,
					"N" AS IgstOnIntra,
					NULL AS RegRev,
					NULL AS EcmGstin,
					"INV" AS Typ,
					IFNULL(i.invoice_code, CONCAT(i.financial_year, '/', SUBSTR(i.type, 1, 1), LPAD(i.invoice_no, 6, 0), IFNULL(i.sub_number, ''))) AS No,
					i.issued_on AS Dt,
					erd.details AS Gstin,
					e.name AS LglNm,
					e.name AS TrdNm,
					e.address1 AS Addr1,
					e.address2 AS Addr2,
					e.city AS Loc,
					e.pin_code AS Pin,
					left(erd.details, 2) as Stcd,
					left(erd.details, 2) as Pos,
					e.phone_no AS Ph,
					e.email AS Em,
					prd.details AS p_Gstin,
					p.party_name AS p_LglNm,
					p.party_name AS p_TrdNm,
					p.address1 AS p_Addr1,
					p.address2 AS p_Addr2,
					p.city AS p_Loc,
					p.pin_code AS p_Pin,
					left(prd.details, 2) as p_Stcd,
					left(prd.details, 2) as p_Pos,
					p.phone_no AS p_Ph,
					p.email AS p_Em,
					i.grand_total AS AssVal,
					IF(t.type = 'IGST', SUM(t.net_rate)/count(t.type) , NULL) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS IgstVal,
					IF(t.type = 'CGST', SUM(t.net_rate)/count(t.type) , NULL) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS CgstVal,
					IF(t.type = 'SGST', SUM(t.net_rate)/count(t.type) , NULL) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS SgstVal,
					IF(t.type = 'CESS', SUM(t.net_rate)/count(t.type) , NULL) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS CesVal,
					IF(t.type = 'CESS', SUM(t.net_rate)/count(t.type) , NULL) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS StCesVal,
					im.discount AS Discount,
					0 AS OthChrg,
					0 AS RndOffAmt,
					i.grand_total AS TotInvVal,
					0 AS TotInvValFc,
					NULL AS EwbDtls,
					NULL AS PayDtls,
					NULL AS RefDtls,
					NULL AS Url,
					NULL AS Docs,
					NULL AS Info
					
					FROM
						invoice i
					LEFT JOIN invoice_materials im ON i.id = im.invoice_id
						AND i.enterprise_id = im.enterprise_id
					LEFT JOIN invoice_item_tax iit ON im.item_id = iit.item_id
						AND im.make_id = iit.make_id
						AND i.id = iit.invoice_id
						AND i.enterprise_id = iit.enterprise_id
					LEFT JOIN tax t ON iit.tax_code = t.code
						AND i.enterprise_id = t.enterprise_id
					LEFT JOIN party_master p ON p.party_id = i.party_id
						AND p.enterprise_id = i.enterprise_id
					LEFT JOIN party_registration_detail AS prd ON prd.party_id = p.party_id
						AND prd.enterprise_id = p.enterprise_id
						AND TRIM(prd.label) = 'GSTIN'
					LEFT JOIN gst_category gc ON gc.id = p.category_id
					LEFT JOIN enterprise_registration_detail erd ON erd.enterprise_id = i.enterprise_id
						AND erd.label = 'GSTIN'
					LEFT JOIN enterprise e ON e.id = i.enterprise_id
					WHERE i.id in (%s) AND i.enterprise_id = '%s'
					GROUP BY i.id""" % (invoice_lst_str, enterprise_id)
		invoice_data = dao.executeQuery(query, as_dict=True)

		for invoice in invoice_data:
			query = """SELECT 
						 m.description AS PrdDesc, 
						 IF(m.is_service = 1, 'Y', 'N') AS IsServc,
						 im.hsn_code AS HsnCd,
						 im.qty AS Qty,
						 um.unit_name AS Unit,
						 ROUND(im.unit_rate, 2) AS UnitPrice,
						 ROUND((im.qty * im.unit_rate), 2) AS TotAmt,
						 ROUND((im.qty * im.unit_rate), 2) - ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100), 2) AS Discount, 
						 0 AS PreTaxVal,
						 ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100), 2) AS AssAmt,
						 ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100) * SUM(IF(t.type = 'IGST', (t.net_rate), NULL))/100, 2) AS IgstAmt,
						 ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100) * SUM(IF(t.type = 'CGST', (t.net_rate), NULL))/100, 2) AS CgstAmt, 
						 ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100) * SUM(IF(t.type = 'SGST', (t.net_rate), NULL))/100, 2) AS SgstAmt,
						 ROUND(SUM(IF(t.type = 'CGST', (t.net_rate), 0)) + SUM(IF(t.type = 'SGST', (t.net_rate), 0)) + SUM(IF(t.type = 'IGST', (t.net_rate), 0)) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate), 2) AS GstRt,
						 IF(t.type = 'CESS', SUM(t.net_rate) , NULL) AS CesRt,
						 ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100) * SUM(IF(t.type = 'CESS', (t.net_rate), NULL))/100, 2) AS CesAmt,
						 0 AS CesNonAdvlAmt,
						 0 AS StateCesRt,
						 0 AS StateCesAmt,
						 0 AS StateCesNonAdvlAmt,
						 0 AS OthChrg,
						ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100), 2) AS TotItemVal						
						FROM
							invoice i
						JOIN invoice_materials im ON i.id = im.invoice_id
							AND i.enterprise_id = im.enterprise_id
						LEFT JOIN materials m ON m.id = im.item_id
							AND m.enterprise_id = im.enterprise_id
						LEFT JOIN unit_master um ON um.unit_id = m.unit
							AND um.enterprise_id = m.enterprise_id
						LEFT JOIN invoice_item_tax iit ON im.item_id = iit.item_id
							AND im.make_id = iit.make_id
							AND i.id = iit.invoice_id
							AND i.enterprise_id = iit.enterprise_id
						LEFT JOIN tax t ON iit.tax_code = t.code
							AND i.enterprise_id = t.enterprise_id
						LEFT JOIN party_master p ON p.party_id = i.party_id
							AND p.enterprise_id = i.enterprise_id
						LEFT JOIN party_registration_detail AS prd ON prd.party_id = p.party_id
							AND prd.enterprise_id = p.enterprise_id
							AND TRIM(prd.label) = 'GSTIN'
						LEFT JOIN gst_category gc ON gc.id = p.category_id
						LEFT JOIN enterprise_registration_detail erd ON erd.enterprise_id = i.enterprise_id
							AND TRIM(erd.label) = 'GSTIN'
						LEFT JOIN enterprise e ON e.id = i.enterprise_id
						WHERE i.id = '%s' AND i.enterprise_id = '%s'
						GROUP BY i.id, im.item_id
						""" % (invoice['invoice_id'], enterprise_id)
			item_data = dao.executeQuery(query, as_dict=True)
			query = """SELECT 
						 i.id,
						 ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100) * SUM(IF(t.type = 'CESS', (t.net_rate), NULL))/100, 2) AS CesAmt,
						 ROUND(SUM(IF(t.type = 'CESS', (t.net_rate), 0)), 2) AS CesRt											
						FROM
							invoice i
						JOIN invoice_materials im ON i.id = im.invoice_id
							AND i.enterprise_id = im.enterprise_id
						LEFT JOIN materials m ON m.id = im.item_id
							AND m.enterprise_id = im.enterprise_id
						LEFT JOIN unit_master um ON um.unit_id = m.unit
							AND um.enterprise_id = m.enterprise_id
						LEFT JOIN invoice_tax it ON i.id = it.invoice_id
							AND i.enterprise_id = it.enterprise_id
						LEFT JOIN tax t ON it.tax_code = t.code
							AND i.enterprise_id = t.enterprise_id
						LEFT JOIN enterprise e ON e.id = i.enterprise_id
						WHERE i.id = '%s' AND i.enterprise_id = '%s'
						GROUP BY i.id""" % (invoice['invoice_id'], enterprise_id)
			common_tax_data = dao.executeQuery(query, as_dict=True)
			result = EinvoiceJsonMaker().validateEInvoiceJson(invoice=invoice, item_data=item_data, common_tax_data=common_tax_data)

			if not result['error']:
				logger.info("JSON: %s" % simplejson.dumps(result['response']))
				# ip_addr = request.get_host()
				# user_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).user_id
				# irn_response = getIRNResponse(enterprise_id=enterprise_id, data=result['response'], invoice_id=invoice['invoice_id'], user_id=user_id, ip_addr=ip_addr)
				irn_response = simplejson.dumps(result['response'])
				response_data = {'invoice_code': invoice['No'], 'message': 'success', 'result': [irn_response]}
				response.append(response_data)
			else:
				response_data = {'invoice_code': invoice['No'], 'result': result['error']}
				response.append(response_data)

		logger.info("%s" % response)
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def is_json(myjson):
	try:
		json_object = json.loads(myjson)
	except ValueError as e:
		return False
	return True


def getIRNResponse(enterprise_id=None, data=None, invoice_id=None, user_id=None, ip_addr=None):
	"""

	:param enterprise_id:
	:param data:
	:param invoice_id:
	:param ip_addr:
	:return:
	"""
	response = ""
	try:
		enterprise_credentials = getGSTCred(enterprise_id=enterprise_id)
		gst_username = enterprise_credentials['gst_username']
		cipher_suite = Fernet(CIPHER_KEY)
		byte_string = b'%s' % str(enterprise_credentials['gst_password'])
		gst_password = cipher_suite.decrypt(byte_string)
		auth_token = str(enterprise_credentials['gst_auth_token'])
		gstin = str(enterprise_credentials['gstin'])
		email = GST_EMAIL

		if not len(auth_token) > 0:
			auth_token = getGSTAuthToken(username=gst_username, password=gst_password, ip_addr=ip_addr, gstin=gstin, email=email, enterprise_id=enterprise_id)
		params = simplejson.dumps(data)
		irn_headers = {
			'ip_address': ip_addr, 'client_id': GST_CLIENT_ID,
			'client_secret': GST_CLIENT_SECRET_KEY, 'username': gst_username,
			'auth-token': '%s' % auth_token, 'gstin': gstin, 'Content-type': 'application/json'}
		url = 'https://api.mastergst.com/einvoice/type/GENERATE/version/V1_03?email=%s' % email
		res = requests.post(url, headers=irn_headers, data=params)

		if len(res.content) > 0:
			if res.json()['status_cd'] != '0':
				invoice_service = InvoiceService()
				invoice = invoice_service.invoice_dao.getInvoice(
				enterprise_id=enterprise_id, invoice_id=invoice_id if invoice_id != "" else None)
				db_session = invoice_service.invoice_dao.db_session
				data = res.json()['data']
				saveEInvoiceAckData(entity=invoice, ack_data=data, user_id=user_id, db_session=db_session)
				response = "GSTR request succeeds"

			else:
				if is_json(res.json()['status_desc']):
					for error in json.loads(res.json()['status_desc']):
						if error['ErrorCode'] == '1005':
							auth_token = getGSTAuthToken(username=gst_username, password=gst_password, ip_addr=ip_addr, gstin=gstin, email=email, enterprise_id=enterprise_id)
							irn_response = getIRNResponse(enterprise_id=enterprise_id, data=data, invoice_id=invoice_id, user_id=user_id, ip_addr=ip_addr)
							response = irn_response
						else:
							response = res.json()['status_desc']
				else:
					response = [{'ErrorCode': res.json()['status_cd'], 'ErrorMessage': res.json()['status_desc']}]
		logger.info("auth token: %s" % auth_token)
	except Exception as e:
		logger.exception(e)
	return response


def getGSTAuthToken(username=None, password=None, ip_addr=None, gstin=None, email=None, enterprise_id=None):
	"""

	:param username:
	:param password:
	:param ip_addr:
	:param enterprise_id:
	:return:
	"""
	auth_token = ""
	try:
		headers = {
				'username': username, 'password': password, 'ip_address': ip_addr,
				'client_id': GST_CLIENT_ID, 'client_secret': GST_CLIENT_SECRET_KEY, 'gstin': gstin}
		url = 'https://api.mastergst.com/einvoice/authenticate?email=%s' % email
		r = requests.get(url, headers=headers)
		auth_token = r.json()['data']['AuthToken']
		query = """UPDATE enterprise SET gst_auth_token='%s' WHERE id='%s'""" % (str(auth_token), enterprise_id)
		executeQuery(query)
	except Exception as e:
		logger.exception(e)
	return auth_token


def searchInvoice(enterprise_id=None, from_date=None, to_date=None, party_id=-1, status=None, type=None, project=None):
	logger.info(" Enterprise Id : %s , From Date %s and Party id: %s Status: %s" % (
		enterprise_id, from_date, party_id, status))
	condition = """invoice.prepared_on BETWEEN '%s' AND '%s' AND invoice.enterprise_id = '%s'
					AND invoice.status %s %s AND invoice.type %s %s order by invoice.prepared_on""" % (
		from_date, to_date, enterprise_id, " = '%s'" % status if status != '' else " >= -1",
		"" if int(party_id) == -1 else " and invoice.party_id='%s'" % party_id,
		" = 'Issue'" if type == "internal" else "IN {types} ".format(types=Invoice.TYPES[type]),
		"" if project == 'None' or project == '' else " and invoice.project_code='%s'" % project)
	query = SQLASession().query(Invoice).filter(condition)
	logger.debug("Load Invoice Query:%s" % query)
	result = query.all()
	return result


def saveInvoice(request):
	logger.info("Save Issue Triggered...")
	invoice_service = InvoiceService()
	try:
		request_handler = RequestHandler(request)
		issue_type = request_handler.getPostData("type")
		is_super_edit = request_handler.getPostData("invoice-super_edit")
		pp_id = request_handler.getPostData("invoice-job_po_id")
		mrs_type = request_handler.getPostData("mrs_type")
		current_user = request_handler.getSessionAttribute(SESSION_KEY)
		if is_super_edit:
			is_super_edit = bool(is_super_edit)
		else:
			is_super_edit = 0
		enterprise = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		logger.info("The Enterprise ID: %s" % enterprise_id)
		invoice_vo = invoice_service.extractInvoiceVO(
			post_data=request_handler.getPostData(), enterprise_id=enterprise_id, issue_type=issue_type,
			current_user=current_user)
		saved_invoice_vo = invoice_service.saveInvoiceFromVO(
			is_super_edit=is_super_edit, invoice_vo=invoice_vo, enterprise_id=enterprise_id,
			user_id=user_id, fy_start_day=enterprise.fy_start_day, mrs_type=mrs_type)
		if issue_type == "internal":
			updated_data = saved_invoice_vo.invoice_material_formset.cleaned_data
			updateIssuedQuantity(updated_data, pp_id)
		logger.info("The SaveInvoice Event Triggered...")
		invoice_tax_list = populateTaxChoices(enterprise_id)
		from_date, to_date = JsonUtil.getDateRange(
			rh=request_handler, since_session_key='%s.since' % issue_type, till_session_key='%s.till' % issue_type)

		party_id = request_handler.getAndCacheData(key="party_name", session_key="%s.party_name" % issue_type)
		status = request_handler.getAndCacheData(key="status", session_key="%s.status" % issue_type)

		party_id = party_id if party_id else -1
		status = status if status else ""
		inv_list = searchInvoice(enterprise_id, from_date, to_date, party_id, status, issue_type)
		messages.success(request, "Invoice Saved Successfully....")
		redirect_link = properties.INVOICE_LIST_URL
		if issue_type == "internal":
			redirect_link = properties.ISSUE_LIST_URL
		elif issue_type == "dc":
			redirect_link = properties.DC_LIST_URL

	except Exception as e:
		logger.exception("Saving Invoice Failed... %s" % e)
		raise
	if not saved_invoice_vo.is_valid():
		search_form = InvoiceSearchForm(enterprise_id=enterprise_id, type=issue_type, initial={
			'to_date': to_date, 'from_date': from_date, 'party_name': party_id, 'status': status})
		return TemplateResponse(template=properties.INVOICE_LIST_TEMPLATE, request=request, context={
			INVOICE_KEY: saved_invoice_vo.invoice_form,
			INVOICE_MATERIAL_FORMSET_KEY: saved_invoice_vo.invoice_material_formset,
			INVOICE_CHARGES_FORMSET_KEY: invoice_vo.invoice_charges_formset,
			INVOICE_TAX_LIST: saved_invoice_vo.invoice_tax_formset,
			INVOICE_LIST: inv_list,
			INVOICE_TAX_LOAD: invoice_tax_list, INVOICE_SEARCH: search_form,
			INVOICE_TAGS_FORMSET: saved_invoice_vo.invoice_tag_formset
		})
	return HttpResponseRedirect(redirect_link)


def manageIssue(request):
	return manageInvoice(request, list_link=properties.ISSUE_LIST_URL, invoice_type="internal")


def manageDc(request):
	return manageInvoice(request, list_link=properties.DC_LIST_URL, invoice_type="dc")


def manageInvoice(request, list_link=properties.INVOICE_LIST_URL, invoice_type="sales"):
	invoice_service = InvoiceService()
	master_service = MasterService()
	try:
		request_handler = RequestHandler(request)
		enterprise = request_handler.getSessionAttribute(ENTERPRISE_IN_SESSION_KEY)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		parent_enterprise_id = request_handler.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
		current_user = request_handler.getSessionAttribute(SESSION_KEY)
		isprimary_project = enterprise_id == parent_enterprise_id
		se_no = request_handler.getPostData("se_no")
		gst_category = master_service.getGSTCategory()
		countries = master_service.getCountries()
		gst_category_list = []
		gst_country_list = []
		for category in gst_category:
			gst_category_list.append({"category_id": category.id, "category_name": category.name, "category_desc": category.description})

		for country in countries:
			gst_country_list.append({"country_code": country.code, "country_name": country.name})
		se_obj = None
		invoice_id = request_handler.getPostData(INVOICE_PK_FIELD_KEY)
		issue_to_list = helper.listAllIssueTo(
			enterprise_id=enterprise_id, table='invoice', column='issued_to')
		issue_to_list.extend(helper.listAllIssueTo(
			enterprise_id=enterprise_id, table='purchase_order', column='issue_to'))
		set(issue_to_list)
		issue_to_list = list(set(issue_to_list))
		issue_to = [
			helper.listUniqueEntriesFromDB(
				enterprise_id=enterprise_id, table='invoice', column='issued_to', frequents_only=True), issue_to_list
			]
		issue_for = [
			helper.listUniqueEntriesFromDB(
				enterprise_id=enterprise_id, table='invoice', column='issued_for', frequents_only=True),
			helper.listUniqueEntriesFromDB(
				enterprise_id=enterprise_id, table='invoice', column='issued_for')]
		master_service = MasterService()
		material_vo = master_service.constructMaterialVO(enterprise_id=enterprise_id)
		currency = SQLASession().query(Currency).order_by(Currency.code).all()
		inv_config_details = invoice_service.invoice_template_dao.getInvoiceTemplateConfig(
			enterprise_id=enterprise_id, module='Sales')
		cnl_einvoice = False
		set_einvoice = False
		is_gst_credentials = False
		if invoice_id is None:
			invoice_template = invoice_service.invoice_dao.db_session.query(InvoiceTemplateConfig).filter(
				InvoiceTemplateConfig.enterprise_id == enterprise_id).first()
			invoice_template_misc_form = invoice_template.template_misc_details
			invoice_obj = Invoice(
				prepared_by=request_handler.getSessionAttribute(SESSION_KEY), enterprise_id=enterprise_id,
				currency_id=enterprise.home_currency_id, prepared_on=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
			if se_no:
				invoice_obj, se_obj = invoice_service.generateInvoiceFromSE(invoice_obj=invoice_obj, se_no=se_no, enterprise_id=enterprise_id)

			notes = invoice_obj.notes if invoice_obj.notes else invoice_template_misc_form.notes
			invoice_vo = invoice_service.constructInvoiceVo(
				invoice_obj, type=invoice_type, default_notes=notes, current_user=current_user)
		else:
			is_item_invoiced = 0
			is_item_returned = 0
			is_job_returned = 0
			module_key = request_handler.getPostData('edit_dc_type')
			item_list = invoice_service.getInvoicedItems(enterprise_id=enterprise_id, dc_id=invoice_id)
			if len(item_list) > 0:
				is_item_invoiced = 1
			dc_materials_list = invoice_service.getReturnedItems(enterprise_id=enterprise_id, dc_id=invoice_id)
			if len(dc_materials_list) > 0:
				is_item_returned = 1
			from_date, to_date = JsonUtil.getDateRange(
				rh=request_handler, since_session_key='%s.since' % module_key, till_session_key='%s.till' % module_key)
			party_id = request_handler.getAndCacheData(key="party_name", session_key="%s.party_name" % module_key)
			status = request_handler.getAndCacheData(key="status", session_key="%s.status" % module_key)

			invoice_to_edit = getInvoice(invoice_id, enterprise_id)
			if invoice_to_edit.status > 0 and (invoice_to_edit.irn_ack_json is None or invoice_to_edit.irn_ack_json == "null") and (datetime.utcnow() - invoice_to_edit.approved_on) < timedelta(1):
				set_einvoice = True
			if invoice_to_edit.irn_ack_json is not None and invoice_to_edit.irn_ack_json != "null" and (datetime.utcnow() - invoice_to_edit.approved_on) < timedelta(1):
				cnl_einvoice = True
			query = """SELECT gst_username, gst_password from enterprise WHERE id='%s'""" % enterprise_id
			enterprise_credentials = executeQuery(query)
			is_gst_credentials = True if enterprise_credentials[0][0] is not None and len(enterprise_credentials[0][0]) > 0 else False

			remarks_history = json.dumps(invoice_to_edit.remarks if invoice_to_edit else None)
			if invoice_to_edit:
				logger.debug("Invoice To Edit : %s" % invoice_to_edit.getSimpleCode())
			invoice_vo = invoice_service.constructInvoiceVo(
				invoice_to_edit, type=module_key, default_notes=invoice_to_edit.notes, is_dc_edit=True,
				current_user=current_user)
			is_returnable = 0
			is_not_returnable = 0
			for material in invoice_to_edit.items:
				if material.is_returnable is True:
					is_returnable = 1
				if material.is_returnable is False:
					is_not_returnable = 1

			if is_returnable is 0 or is_not_returnable is 0:
				for material in invoice_to_edit.non_stock_items:
					if material.is_returnable is True:
						is_returnable = 1
					if material.is_returnable is False:
						is_not_returnable = 1
			if invoice_to_edit.type == 'JIN':
				is_job_returned = 1
			party_id = party_id if party_id else -1
			status = status if status else ""
			inv_list = searchInvoice(enterprise_id, from_date, to_date, party_id, status, module_key)
			search_form = InvoiceSearchForm(enterprise_id=enterprise_id, type=module_key, initial={
				'to_date': to_date, 'from_date': from_date, 'party_name': party_id, 'status': status})
			invoice_vo.invoice_form.initial['remarks'] = ''
			invoice_type = [(item[0], item[1], item[0][0]) for item in Invoice.TYPE_CHOICES['sales']]
			logger.info("*********************************** %s" % cnl_einvoice)
		context = {
			INVOICE_KEY: invoice_vo.invoice_form,
			'list_link': list_link,
			'cnl_einvoice': cnl_einvoice,
			'set_einvoice': set_einvoice,
			'is_gst_credentials': is_gst_credentials,
			INVOICE_MATERIAL_FORMSET_KEY: invoice_vo.invoice_material_formset,
			INVOICE_CHARGES_FORMSET_KEY: invoice_vo.invoice_charges_formset,
			INVOICE_TAX_LIST: invoice_vo.invoice_tax_formset,
			INVOICE_TAX_LOAD: populateTaxChoices(enterprise_id),
			'issued_to': issue_to, 'issued_for': issue_for,
			INVOICE_TAGS_FORMSET: invoice_vo.invoice_tag_formset,
			MATERIAL_FORM_KEY:  material_vo.material_form,
			BILL_OF_MATERIALS_FORMSET_KEY: material_vo.material_formset,
			SPECIFICATION_FORMSET_KEY: material_vo.specification_formset,
			PRICE_OF_SUPP_MATERIALS_FORMSET_KEY: material_vo.supplier_price_material_formset,
			MATERIAL_MAKE_FORMSET_KEY: material_vo.material_make_formset,
			ALTERNATE_UNIT_FORMSET_KEY: material_vo.alternate_unit_formset,
			'selected_project_code': invoice_to_edit.project_code if invoice_id is not None else None,
			'gst_category_list': gst_category_list, 'gst_country_list': gst_country_list, 'state_list': getStateList(),
			'currency': currency, 'inv_config_details': inv_config_details
			}
		if invoice_id is None:
			context[PROMOTE_SE_ID] = se_no if se_no else ""
			context[PROMOTE_SE_CODE] = se_obj.getCode() if se_obj is not None else ""
			context['se_project_code'] = se_obj.project_code if se_obj is not None else None
			context[MATERIAL_FORM_KEY] = material_vo.material_form
			context[BILL_OF_MATERIALS_FORMSET_KEY] = material_vo.material_formset
			context[SPECIFICATION_FORMSET_KEY] = material_vo.specification_formset
			context[PRICE_OF_SUPP_MATERIALS_FORMSET_KEY] = material_vo.supplier_price_material_formset
			context[MATERIAL_MAKE_FORMSET_KEY] = material_vo.material_make_formset
			context[PARTY_KEY] = populatePartyChoices(enterprise_id)
			context['type'] = invoice_type
			context[TEMPLATE_TITLE_KEY] = "Internal Invoice" if not isprimary_project else ("Invoice" if invoice_type == "sales" else (
				"Delivery Challan" if invoice_type == "dc" else "Issue"))
		else:
			context['se_no'] = invoice_to_edit.se.getInternalCode() if invoice_to_edit.se else ""
			context['remarks_list'] = remarks_history
			context[EDIT_INV_ID] = invoice_id if invoice_id else ""
			context[INVOICE_LIST] = inv_list
			context["invoice_type"] = invoice_type
			context['tax_payable_on_reverse_charge'] = invoice_to_edit.tax_payable_on_reverse_charge,
			context[INVOICE_SEARCH] = search_form
			context['type'] = module_key
			context[TEMPLATE_TITLE_KEY] = invoice_to_edit.getSimpleCode()
			context['is_item_invoice'] = is_item_invoiced
			context['is_item_returned'] = is_item_returned
			context['is_returnable'] = is_returnable
			context['is_not_returnable'] = is_not_returnable
			context['is_job_returned'] = is_job_returned
			make_transient(invoice_to_edit)
		response = TemplateResponse(template=properties.INVOICE_TEMPLATE, request=request, context=context)

		return response
	except Exception as e:
		logger.exception("Invoice Rendering for Edit Failed...\n%s" % e.message)
		raise


def generateInvoiceFromSE(invoice_obj=None, se_no=None, enterprise_id=None):
	try:

		se_obj = getSalesEstimate(se_id=se_no, enterprise_id=enterprise_id)
		se_code = se_obj.getCode()
		copyDictToDict(source=se_obj.__dict__, destination=invoice_obj.__dict__, exclude_keys=SE_EXCLUDE_FIELD_LIST)
		invoice_obj.enterprise = se_obj.enterprise
		invoice_obj.special_instruction = se_obj.special_instructions

		invoice_items = []
		i = 0
		for item in se_obj.items:
			inv_item = InvoiceMaterial(enterprise_id=enterprise_id)
			copyDictToDict(
						source=item.__dict__, destination=inv_item.__dict__, exclude_keys=SE_PARTICULAR_EXCLUDE_FIELD_LIST)
			inv_item.entry_order = i + 1
			inv_item.rate = item.unit_rate
			inv_item.item = item.item
			inv_item.alternate_unit = item.alternate_unit
			inv_item.invoice = invoice_obj
			invoice_items.append(inv_item)
			i = i + 1
		logger.info(invoice_items)
		invoice_obj.items = invoice_items

		invoice_taxes = []
		for tax in se_obj.taxes:
			inv_tax = InvoiceTax(enterprise_id=enterprise_id)
			copyDictToDict(
						source=tax.__dict__, destination=inv_tax.__dict__, exclude_keys=SE_TAX_EXCLUDE_FIELD_LIST)
			invoice_taxes.append(inv_tax)
		invoice_obj.taxes = invoice_taxes

		return invoice_obj, se_code
	except Exception as e:
		logger.exception("Invoice generation from SE Failed...\n%s" % e.message)
		raise


def loadTax(request):
	"""
	Loads the Tax and Sub-tax information upon any ajax call to add a Tax Profile to a PO.
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	if request_handler.isPostRequest():
		tax_code = request_handler.getPostData('tax_code')
		try:
			tax = SQLASession().query(Tax).filter(
				Tax.code == tax_code,
				Tax.enterprise_id == request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)).first()
			tax_json_dump = generateTaxJSONDump(tax)
			logger.info(tax_json_dump)
		except:
			logger.exception('Failed to construct JSON dump for Tax...')
			raise
		return HttpResponse(content=simplejson.dumps(tax_json_dump), mimetype='application/json')
	return HttpResponseRedirect(properties.LOGIN_URL)


def loadTaxEdit(request):
	logger.info("The Edit Tax Loaded...")
	request_handler = RequestHandler(request)
	tax_json_dump_res = []
	try:
		if request_handler.isPostRequest():
			inv_id = request_handler.getPostData('inv_id')
			se_id = request_handler.getPostData('se_id')
			oa_id = request_handler.getPostData('oa_id')
			enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
			tax_li = None
			if inv_id:
				tax_li = SQLASession().query(InvoiceTax.tax_code).filter(
					InvoiceTax.invoice_id == inv_id, InvoiceTax.enterprise_id == enterprise_id).all()

			if se_id:
				tax_li = SQLASession().query(SETax.tax_code).filter(
					SETax.se_id == se_id, SETax.enterprise_id == enterprise_id).all()

			if oa_id:
				tax_li = SQLASession().query(OATax.tax_code).filter(
					OATax.oa_id == oa_id, OATax.enterprise_id == enterprise_id).all()

			for res_tax in tax_li:
				tax = (generateTaxJSONDump(SQLASession().query(Tax).filter(
					Tax.code == res_tax.tax_code, Tax.enterprise_id == enterprise_id).first()))
				tax_json_dump_res.append(tax)
	except:
		logger.exception("Tax retrieval fails...")
		from erp.middleware import EXCEPTION_RESPONSE_KEY
		request.session[EXCEPTION_RESPONSE_KEY] = HttpResponse(simplejson.dumps(""), mimetype='application/json')
		raise
	return HttpResponse(content=simplejson.dumps(tax_json_dump_res), mimetype='application/json')


def listCatalogueMaterialsJSON(request):
	logger.info("Preparing BoM list as JSON...")
	rh = RequestHandler(request)
	item_id = rh.getPostData('item_id')
	issued_on = rh.getPostData('issued_on')
	alternate_unit_id = rh.getPostData('alternate_unit_id')
	bom_items = []
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		logger.info("Fetching Catalogue Materials for {enterprise: %s, item_code:%s}" % (enterprise_id, item_id))
		store_dao = StoresDAO()
		store_service = StoresService()
		bom_item = MasterDAO().getMaterialByItemId(item_id=item_id, enterprise_id=enterprise_id)
		for item in bom_item.bill_of_materials:
			catalogues = store_dao.getCatalogues(item.item_id, enterprise_id)
			catalogues_child = 1 if len(catalogues) > 0 else 0
			stock_materials = []
			make_name = item.material.makes_json if item.material.makes_json else ""
			# stock_qty = store_dao.getClosingStock(
			# 	enterprise_id=enterprise_id, item_id=item.material.material_id,
			# 	issued_on=issued_on if issued_on else datetime.now(), till=issued_on if issued_on else datetime.now(), is_faulty=0)
			stock_qty = 0
			stock_details = store_service.getClosingStock(mat_list=[int(item.material.material_id)], enterprise_id=enterprise_id, is_faulty=0)
			for stock_item in stock_details:
				stock_qty = stock_item['closing_qty']
			scale_factor = 1
			if alternate_unit_id and int(alternate_unit_id) != 0:
				scale_factor = helper.getScaleFactor(
					enterprise_id=enterprise_id, item_id=item.parent_id,
					alternate_unit_id=alternate_unit_id)
				if scale_factor:
					item.quantity = Decimal(item.quantity) * Decimal(scale_factor)
			bom_items.append({
				"drawing_no": item.material.drawing_no, "cat_code": item.parent_id,
				"name": item.material.name, "description": item.material.description, "unit": str(item.material.unit),
				"price": item.material.price, "child": catalogues_child, "makes": stock_materials,
				'unit_id': item.material.unit_id, "enterprise_id": item.enterprise_id, 'material_type': item.material.is_stocked,
				"quantity": item.quantity, 'minimum_stock_level': item.material.minimum_stock_level,
				"item_id": item.material.material_id, "scale_factor": scale_factor if scale_factor else 1,
				"is_service": item.material.is_service, "make_name" : make_name, "stock_qty": stock_qty})
		logger.info("BoM has %s items" % len(bom_item.bill_of_materials))
	except Exception as e:
		logger.exception("BoM Listing as JSON fails... %s" % e.message)
	return HttpResponse(content=simplejson.dumps(bom_items), mimetype='application/json')


def loadBomCatalogueMaterials(request):
	"""
	Append selected material subset
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	cat_code = request_handler.getPostData('cat_code')
	store_dao = StoresDAO()
	catalogues = store_dao.getCatalogues(cat_code, enterprise_id)
	try:
		catalogue_material_data_dump = []
		materials = {}
		for catalogue in catalogues:
			if catalogue.item_id in materials:
				material = materials[catalogue.item_id]
			else:
				catalogues_child = store_dao.getCatalogues(catalogue.item_id, enterprise_id)
				make_name = constructDifferentMakeName(catalogue.makes_json)
				material_name = catalogue.name + " [" + make_name + "]" if make_name else catalogue.name
				material = {
					"name": material_name, "quantity": catalogue.quantity, "drawing_no": catalogue.drawing_no,
					"unit_name": catalogue.unit_name, "makes": [], "hasChildren": len(catalogues_child) > 0,
					"price": catalogue.price, "tariff_no": catalogue.tariff_no if catalogue.tariff_no else "",
					"item_id": catalogue.item_id, "cat_code": catalogue.parent_id, "alternate_unit_id": 0,
					"scale_factor": 1, "unit_id": catalogue.unit_id, "material_type": catalogue.is_stocked,
					"is_service": catalogue.is_service
				}
				catalogue_material_data_dump.append(material)
			# if catalogue.label:
			# 	stock = 0
			# 	if catalogue.is_stocked:
			# 		stock = store_dao.getClosingStock(
			# 			enterprise_id=enterprise_id, item_id=catalogue.item_id,
			# 			till=datetime.now(), is_faulty=0)
				# part_no = helper.getMakePartNumber(
				# 	enterprise_id=enterprise_id, item_id=catalogue.item_id, make_id=catalogue.id)
			# 	make = "%s" % (catalogue.label)
			# 	material["makes"].append({"make_id": catalogue.id, "make_name": make, "stock": stock})
			# materials[catalogue.item_id] = material

	except Exception:
		logger.info("Adding a material to a Catalogue failed...")
		from erp.middleware import EXCEPTION_RESPONSE_KEY
		request.session[EXCEPTION_RESPONSE_KEY] = HttpResponse(simplejson.dumps(""), mimetype='application/json')
		raise
	return HttpResponse(content=simplejson.dumps(catalogue_material_data_dump), mimetype='application/json')


def getPOMaterials(request):
	logger.info("The Edit Tax Loaded...")
	rh = RequestHandler(request)
	po_id = rh.getPostData('po_id')
	type = rh.getPostData('type')
	issued_on = rh.getPostData('issued_on')
	location_id = rh.getPostData('location_id')
	po_details = []
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		logger.info("Fetching purchase order material for {enterprise: %s, po_id:%s}" % (enterprise_id, po_id))
		if type == 'JDC':
			po_details = getJDCMaterials(po_id, enterprise_id, issued_on, location_id)
		else:
			po_details = getPPMaterials(po_id, enterprise_id)
	except Exception as e:
		logger.exception("Tax retrieval fails... %s" % e.message)

	return HttpResponse(content=simplejson.dumps(po_details), mimetype='application/json')


def getMRSMaterials(request):
	logger.info("The Edit Tax Loaded...")
	rh = RequestHandler(request)
	oa_id = rh.getPostData('po_id')
	issued_on = rh.getPostData('issued_on')
	invoice_id = rh.getPostData('invoice_id')
	location_id = rh.getPostData('location_id') if rh.getPostData('location_id') else ""
	po_details = []
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		logger.info("Fetching order acknowledgement for {enterprise: %s, po_id:%s}" % (enterprise_id, oa_id))
		store_dao = StoresDAO()
		store_service = StoresService()
		invoice_condition = ""
		if invoice_id:
			invoice_condition = " and invoice_id !={invoice_id}".format(invoice_id=invoice_id)
		mrs_materials_query = """SELECT m.drawing_no as drawing_no, m.id as cat_code, m.name as name, 
			m.description as description, u.unit_name as unit, m.price as price, m.unit as unit_id, 
			m.minimum_stock_level as minimum_stock_level, pom.enterprise_id as enterprise_id, 
			m.is_stocked as material_type, IFNULL(m.tariff_no, "") as hsn_code, 
			m.makes_json AS make_name, pom.make_id AS make_id, m.id as item_id,
			IFNULL(pom.alternate_unit_id, 0) as alternate_unit_id, m.is_service as is_service,
			pom.quantity as quantity,(Select ifnull(sum(qty),0) from invoice_materials where 
			enterprise_id=pom.enterprise_id and oa_no=pom.oa_id and item_id=pom.item_id and make_id=pom.make_id {invoice_condition}) as issue_qty,
			pom.oa_id as oa_id, (select IFNULL(issue_id, "") from order_acknowledgement where id = {oa_id}) as issue_to,
			(select IFNULL(project_code, "") from order_acknowledgement where id = {oa_id}) as project_id
			FROM oa_particulars as pom
			LEFT JOIN materials as m ON pom.item_id = m.id AND pom.enterprise_id = m.enterprise_id
			LEFT JOIN unit_master as u ON u.enterprise_id = m.enterprise_id AND u.unit_id = m.unit			
			WHERE pom.oa_id={oa_id} AND pom.enterprise_id = {enterprise_id} AND (pom.quantity -
			(SELECT IFNULL(SUM(qty), 0) FROM invoice_materials WHERE enterprise_id = pom.enterprise_id AND oa_no = pom.oa_id 
			AND item_id = pom.item_id AND make_id = pom.make_id)) > 0 """.format(
			oa_id=oa_id, enterprise_id=enterprise_id, invoice_condition=invoice_condition)
		materials = dao.executeQuery(mrs_materials_query, as_dict=True)
		for material in materials:
			stock = 0
			if material['material_type'] != 0:
				# stock_on_date = store_dao.getClosingStock(
				# 	enterprise_id=enterprise_id, item_id=material['item_id'],
				# 	till=datetime.now(), is_faulty=0, issued_on=issued_on if issued_on else datetime.now(), location_id=location_id)
				# stock_till_today = store_dao.getClosingStock(
				# 	enterprise_id=enterprise_id, item_id=material['item_id'],
				# 	till=datetime.now(), is_faulty=0, location_id=location_id)
				# stock = min(stock_on_date, stock_till_today)
				stock_details = store_service.getClosingStock(
					mat_list=[int(material['item_id'])], enterprise_id=enterprise_id, is_faulty=0, location_id=location_id)
				for item in stock_details:
					stock = item['closing_qty']
				material['scale_factor'] = 1
			if material['alternate_unit_id'] and int(material['alternate_unit_id']) != 0:
				scale_factor = helper.getScaleFactor(
					enterprise_id=enterprise_id, item_id=material['item_id'], alternate_unit_id=material['alternate_unit_id'])
				material['unit'] = helper.getUnitName(enterprise_id=enterprise_id, unit_id=material['alternate_unit_id'])
				if scale_factor:
					stock = Decimal(stock) / Decimal(scale_factor) if stock != 0 else 0
					material['minimum_stock_level'] = Decimal(material['minimum_stock_level']) / Decimal(scale_factor)
					material['price'] = Decimal(material['price']) * Decimal(scale_factor)
					material['scale_factor'] = scale_factor if scale_factor else 1
			stock_materials = [(material['make_id'], material['make_name'], stock, material['hsn_code'])]

			material['makes'] = stock_materials

			po_details.append(material)

	except Exception as e:
		logger.exception("Tax retrieval fails... %s" % e.message)

	return HttpResponse(content=simplejson.dumps(po_details), mimetype='application/json')


def getPPStackQty(request):
	rh = RequestHandler(request)
	po_id = rh.getPostData('po_id')
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	issue_id = rh.getPostData('issue_id')
	pp_issues = {}
	child_issue_qty = {}
	child_stock_qty = {}
	try:
		pp_stock_query = """SELECT pom.pid as pid, pom.pur_qty as purchase_qty, pom.alternate_unit_id, pom.item_id as master_item_id,
						inv.id as invoice_id, inv_m.item_id as inv_item_id, sum(inv_m.qty) as issue_qty
						FROM purchase_order_material as pom
						LEFT JOIN invoice as inv ON pom.enterprise_id = inv.enterprise_id AND pom.pid = inv.job_po_id
						LEFT JOIN invoice_materials as inv_m ON inv.enterprise_id = inv_m.enterprise_id AND inv.id = inv_m.invoice_id
						WHERE pom.pid = {po_id} AND pom.enterprise_id = {enterprise_id}
						GROUP BY inv_item_id""".format(po_id=po_id, enterprise_id=enterprise_id)
		materials = dao.executeQuery(pp_stock_query, as_dict=True)
		if materials:
			pid = po_id
			po_qty = 0
			master_item_id = None
			issue_qty = 0
			for rec in materials:
				po_qty = rec['purchase_qty']
				master_item_id = rec['master_item_id']
				issue_item = rec['inv_item_id']
				if issue_item == master_item_id:
					issue_qty += rec['issue_qty']
				else:
					if issue_item in child_issue_qty:
						child_issue_qty[issue_item] += rec['issue_qty']
					else:
						child_issue_qty[issue_item] = rec['issue_qty']
			pp_stock_query = """
					SELECT item_id,parent_id,pp_id,allocated_qty, required_qty,issued_qty
					FROM mrp_materials where pp_id = {pp_id} and enterprise_id = {enterprise_id} and allocated_qty != 0;
					""".format(pp_id=po_id, enterprise_id=enterprise_id)
			mrp_materials = dao.executeQuery(pp_stock_query, as_dict=True)
			for rec in mrp_materials:
				child_stock_qty[rec["item_id"]] = {
					"allocated_qty": rec["allocated_qty"],
					"required_qty": rec["required_qty"],
					"issued_qty": 0 if issue_id else rec["issued_qty"]}
			pp_issues = {
				"pid" : pid,
				"po_qty" : po_qty,
				"master_item_id" : master_item_id,
				"issue_qty" : issue_qty,
				"child_items" : child_stock_qty
			}
	except Exception as e:
		logger.exception("Get stock Qty failed ... %s" % e.message)
	return HttpResponse(content=simplejson.dumps(pp_issues), mimetype='application/json')


def getOAMaterials(request):
	oa_details = []
	try:
		logger.info("The OA Material Details Loaded...")
		rh = RequestHandler(request)
		oa_ids = rh.getPostData("oa_ids")
		grn_ids = rh.getPostData("receipt_ids")
		issued_on = rh.getPostData('issued_on')
		invoice_id = rh.getPostData('invoice_id')
		receipt_id = rh.getPostData('receipt_id')
		oa_against_type = rh.getPostData('oa_against_type')
		location_id = rh.getPostData('location_id')
		if not issued_on:
			issued_on = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		primary_enterprise_id = rh.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
		if oa_ids:
			oa_details = fetchOAMaterials(
				enterprise_id=enterprise_id, oa_ids=oa_ids, issued_on=issued_on, invoice_id=invoice_id,
				receipt_id=receipt_id, oa_against_type=oa_against_type, primary_enterprise_id=primary_enterprise_id,
				location_id=location_id)
		if grn_ids:
			oa_details.extend(fetchGRNMaterials(enterprise_id=enterprise_id, grn_ids=grn_ids, issued_on=issued_on, invoice_id=invoice_id))
	except Exception as e:
		logger.exception("OA Particulars retrieval fails... %s" % e.message)

	return HttpResponse(content=simplejson.dumps(oa_details), mimetype='application/json')


def fetchOAMaterials(
		enterprise_id=None, oa_ids=None, issued_on=None, invoice_id=None, receipt_id=None, oa_against_type=None,
		primary_enterprise_id=None, location_id=None):
	oa_details = []
	try:
		logger.info("Fetching order material for {enterprise: %s, oa_id:%s}" % (enterprise_id, oa_ids))
		store_dao = StoresDAO()
		store_service = StoresService()
		oa_materials_query = """(SELECT m.drawing_no AS drawing_no, oap.quantity AS OA_qty, m.name AS name
					, m.description AS description, u.unit_name AS unit, oap.price AS price, oap.make_id AS make_id, oap.is_faulty AS is_faulty
					, oap.enterprise_id AS enterprise_id, m.is_stocked AS mat_type, oap.oa_id AS oa_id
					, oap.remarks AS remarks, u.unit_id AS unit_id, IFNULL(oap.hsn_code, m.tariff_no) AS hsn_code
					, m.minimum_stock_level AS minimum_stock_level, '' AS makes
					, m.makes_json AS make_name, oa.oa_no as oa_no, oa.id as id, oa.financial_year as fin_year, 
						oa.sub_number as sub_number, oa.type as oa_type
					, IFNULL(oa.currency_id, 1) as currency_id, IFNULL(oa.currency_conversion_rate, 1) as conversion_rate, 
					0 as discount, m.id as item_id,IFNULL(oap.alternate_unit_id, 0) as alternate_unit_id,
					m.is_service as is_service
					FROM oa_particulars AS oap
					LEFT JOIN materials AS m ON oap.item_id = m.id 
					LEFT JOIN unit_master AS u ON u.enterprise_id = m.enterprise_id AND u.unit_id = m.unit					
					LEFT JOIN order_acknowledgement AS oa ON oa.id = oap.oa_id
					WHERE oap.enterprise_id = {enterprise_id} AND oap.oa_id in ({oa_ids}))""".format(
			enterprise_id=enterprise_id, oa_ids=oa_ids)

		materials = dao.executeQuery(oa_materials_query, as_dict=True)
		for material in materials:
			oa_pending_qty = 0
			stock = 0
			if material['item_id']:
				# stock_on_date = store_dao.getClosingStock(
				# 	enterprise_id=primary_enterprise_id, item_id=material['item_id'],
				# 	till=datetime.now(), is_faulty=0, issued_on=issued_on if issued_on else datetime.now(), location_id=location_id)
				# stock_till_today = store_dao.getClosingStock(
				# 	enterprise_id=primary_enterprise_id, item_id=material['item_id'],
				# 	till=datetime.now(), is_faulty=0, location_id=location_id)
				# stock = min(stock_on_date, stock_till_today)
				stock_details = store_service.getClosingStock(
					mat_list=[int(material['item_id'])], enterprise_id=enterprise_id, is_faulty=0, location_id=location_id)
				for item in stock_details:
					stock = item['closing_qty']
				if oa_against_type == 'Invoice':
					oa_pending_qty = float(material['OA_qty']) - float(getInvoicedOAQty(
						item_id=material['item_id'], make_id=material['make_id'], enterprise_id=enterprise_id,
						oa_id=material['oa_id'], invoice_id=invoice_id))
				elif oa_against_type == 'GRN':
					oa_pending_qty = float(material['OA_qty']) - float(getReceivedOAQty(
						item_id=material['item_id'], make_id=material['make_id'], enterprise_id=enterprise_id,
						oa_id=material['oa_id'], receipt_id=receipt_id))
			material['scale_factor'] = 1
			if material['alternate_unit_id'] and int(material['alternate_unit_id']) != 0:
				scale_factor = helper.getScaleFactor(
					enterprise_id=primary_enterprise_id, item_id=material['item_id'], alternate_unit_id=material['alternate_unit_id'])
				material['unit'] = helper.getUnitName(enterprise_id=primary_enterprise_id, unit_id=material['alternate_unit_id'])
				if scale_factor:
					material['OA_qty'] = round(Decimal(material['OA_qty']) / Decimal(scale_factor), 3)
					oa_pending_qty = round(Decimal(oa_pending_qty) / Decimal(scale_factor), 3)
					stock = Decimal(stock) / Decimal(scale_factor)
					material['minimum_stock_level'] = Decimal(material['minimum_stock_level']) / Decimal(scale_factor)
					material['price'] = Decimal(material['price']) * Decimal(scale_factor)
					material['scale_factor'] = scale_factor if scale_factor else 1
			oa_code = OA.generateInternalCode(
				material['fin_year'], material['oa_type'], material['oa_no'], material['id'], material['sub_number'])
			material['oa_pending_qty'] = oa_pending_qty
			material['stock'] = stock
			material['oa_code'] = oa_code
			material['hasChildren'] = len(store_dao.getCatalogues(material["item_id"], primary_enterprise_id)) > 0
			oa_details.append(material)

	except Exception as e:
		logger.exception("OA Particulars retrieval fails... %s" % e.message)
	return oa_details


def getGRNMaterials(request):
	try:
		logger.info("The GRN Material Details Loaded...")
		rh = RequestHandler(request)
		issued_on = rh.getPostData('issued_on')
		grn_ids = rh.getPostData("receipt_ids")
		invoice_id = rh.getPostData('invoice_id')
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		location_id = rh.getPostData('location_id')
		grn_details = fetchGRNMaterials(
			enterprise_id=enterprise_id, grn_ids=grn_ids, issued_on=issued_on, invoice_id=invoice_id, location_id=location_id)
	except Exception as e:
		logger.exception("GRN Materials retrieval fails... %s" % e.message)

	return HttpResponse(content=simplejson.dumps(grn_details), mimetype='application/json')


def fetchGRNMaterials(enterprise_id=None, grn_ids=None, issued_on=None, invoice_id=None, location_id=None):
	try:
		grn_details = []
		logger.info("Fetching GRN material for {enterprise: %s, grn_id:%s}" % (enterprise_id, grn_ids))
		store_dao = StoresDAO()
		store_service = StoresService()
		oa_materials_query = """(SELECT m.drawing_no AS drawing_no, SUM(grm.acc_qty) AS grn_qty,m.name AS name,
										m.description AS description,u.unit_name AS unit,grm.inv_rate AS price,
										grm.make_id AS make_id,grm.enterprise_id AS enterprise_id,m.is_stocked AS mat_type, grm.oa_id AS oa_id,
										grm.grnNumber AS grn_no,'' AS remarks,u.unit_id AS unit_id,
										IF(grm.hsn_code != "" AND grm.hsn_code IS NOT NULL, grm.hsn_code, IFNULL(m.tariff_no,"")) AS hsn_code,
										m.minimum_stock_level AS minimum_stock_level,'' AS makes,
										m.makes_json AS make_name,grm.is_faulty as is_faulty,
										grn.receipt_no AS receipt_no,grn.financial_year AS fin_year,
										grn.sub_number AS sub_number,IFNULL(grn.inv_currency_id, 1) AS currency_id,
										IFNULL(grn.cur_conversion_rate,1) AS conversion_rate, grn.invno  AS party_inv_no,
										grm.discount as discount, m.id as item_id, IFNULL(grm.alternate_unit_id, 0) as alternate_unit_id,
										m.is_service as is_service
										FROM grn_material AS grm 
										LEFT JOIN materials AS m ON grm.item_id = m.id AND grm.enterprise_id = m.enterprise_id
										LEFT JOIN unit_master AS u ON u.enterprise_id = m.enterprise_id AND u.unit_id = m.unit										
										LEFT JOIN grn AS grn ON grn.grn_no = grm.grnNumber WHERE grm.enterprise_id = {enterprise_id}
										AND grm.grnNumber IN ({grn_ids})
										GROUP BY grm.grnNumber , drawing_no, name , make_id, is_faulty)""".format(
			enterprise_id=enterprise_id, grn_ids=grn_ids)
		materials = dao.executeQuery(oa_materials_query, as_dict=True)
		for material in materials:
			if material['item_id']:
				# stock_on_date = store_dao.getClosingStock(
				# 	enterprise_id=enterprise_id, item_id=material['item_id'], make_id=material['make_id'],
				# 	till=datetime.now(), is_faulty=material['is_faulty'], issued_on=issued_on, location_id=location_id)
				# stock_till_today = store_dao.getClosingStock(
				# 	enterprise_id=enterprise_id, item_id=material['item_id'], make_id=material['make_id'],
				# 	till=datetime.now(), is_faulty=material['is_faulty'], location_id=location_id)
				# stock = min(stock_on_date, stock_till_today)
				stock_details = store_service.getClosingStock(
					mat_list=[int(material['item_id'])], enterprise_id=enterprise_id, is_faulty=0, location_id=location_id)
				for item in stock_details:
					stock = item['closing_qty']

				grn_pending_qty = float(material['grn_qty']) - float(getGRNAgainstDCQty(
					item_id=material['item_id'], make_id=material['make_id'], is_faulty=material['is_faulty'],
					enterprise_id=enterprise_id, grn_no=material['grn_no'], invoice_id=invoice_id))
			oa_code = Receipt.generateReceiptCode(
				receipt_no=material['grn_no'], receipt_code=material['receipt_no'], received_against='Job In',
				financial_year=material['fin_year'])
			material['scale_factor'] = 1
			if material['alternate_unit_id'] and int(material['alternate_unit_id']) != 0:
				scale_factor = helper.getScaleFactor(
					enterprise_id=enterprise_id, item_id=material['item_id'], alternate_unit_id=material['alternate_unit_id'])
				material['unit'] = helper.getUnitName(enterprise_id=enterprise_id, unit_id=material['alternate_unit_id'])
				if scale_factor:
					material['grn_qty'] = Decimal(material['grn_qty']) / Decimal(scale_factor)
					grn_pending_qty = Decimal(grn_pending_qty) / Decimal(scale_factor)
					stock = Decimal(stock) / Decimal(scale_factor)
					material['minimum_stock_level'] = Decimal(material['minimum_stock_level']) / Decimal(scale_factor)
					material['price'] = Decimal(material['price']) * Decimal(scale_factor)
					material['scale_factor'] = scale_factor if scale_factor else 1
			material['grn_no'] = material['grn_no']  # oa_id is grn_no
			material['OA_qty'] = float(material['grn_qty'])  # OA_qty is grn_qty
			material['oa_pending_qty'] = grn_pending_qty
			material['stock'] = stock
			material['oa_code'] = material['party_inv_no'] + " - " + oa_code
			grn_details.append(material)
	except Exception as e:
		logger.exception("GRN Materials retrieval fails... %s" % e.message)
	return grn_details


def getOAHeader(request):
	logger.info("The OA Header Details Loaded...")
	rh = RequestHandler(request)
	oa_id = rh.getPostData('oa_id')
	oa_header = []
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		logger.info("Fetching order Details for {enterprise: %s, oa_id:%s}" % (enterprise_id, oa_id))
		oa_details = SQLASession().query(OA).filter(
			OA.id == oa_id, OA.enterprise_id == enterprise_id).first()
		po_date = ""
		oa_date = ""
		if oa_details.po_date:
			po_date = oa_details.po_date.strftime('%Y-%m-%d')
		if oa_details.approved_on:
			oa_date = oa_details.approved_on.strftime('%Y-%m-%d')
		oa_header.append({
			"po_no": oa_details.po_no, "po_date": po_date, "oa_date": oa_date, "payment_terms": oa_details.payment_terms,
			"special_instructions": oa_details.special_instructions})

		logger.info("OA Details:%s" % oa_header)
	except Exception as e:
		logger.exception("OA Details retrieval fails... %s" % e.message)

	return HttpResponse(content=simplejson.dumps(oa_header), mimetype='application/json')


def getDCHeader(request):
	logger.info("The DC Header Details Loaded...")
	rh = RequestHandler(request)
	dc_id = rh.getPostData('dc_id')
	dc_header = []
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		dc_details = SQLASession().query(Invoice).filter(
			Invoice.id == dc_id, Invoice.enterprise_id == enterprise_id).first()
		dc_header.append({
			"po_no": dc_details.po_no,
			"location_id": dc_details.location_id,
			"po_date": dc_details.po_date.strftime('%Y-%m-%d') if dc_details.po_date else "",
			"dc_date": dc_details.approved_on.strftime('%Y-%m-%d'), "payment_terms": dc_details.payment_terms,
			"special_instruction": dc_details.special_instruction})
	except Exception as e:
		logger.exception("DC Details retrieval fails... %s" % e.message)
	return HttpResponse(content=simplejson.dumps(dc_header), mimetype='application/json')


def getDCMaterials(request):
	rh = RequestHandler(request)
	dc_ids = rh.getPostData('dc_ids')  # dc_ids is a comma separated value
	invoice_id = rh.getPostData('invoice_id')
	dc_ids = dc_ids[:-1] if dc_ids.endswith(",") else dc_ids
	dc_details = []
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		logger.info("Fetching DC material for {enterprise: %s, dc_id:%s}" % (enterprise_id, dc_ids))
		dc_materials_query = """(SELECT 
								m.drawing_no AS drawing_no,inm.qty AS DC_qty,m.name AS name,m.description AS description,
								u.unit_name AS unit,inm.unit_rate AS price,inm.make_id AS make_id,inm.enterprise_id AS enterprise_id,
								m.is_stocked AS mat_type,inm.invoice_id AS dc_id,oa.id AS oa_id,inm.remarks AS remarks,u.unit_id AS unit_id,
								inm.hsn_code AS hsn_code,m.minimum_stock_level AS minimum_stock_level,'' AS makes,
								m.makes_json AS make_name,
								inv.invoice_no AS invoice_no,inv.id AS id,inv.financial_year AS fin_year,inv.sub_number AS sub_number,
								inv.type AS invoice_type,inv.currency_id AS currency_id,inm.is_faulty as is_faulty, inv.invoice_code AS invoice_code,
								oa.oa_no AS oa_oa_no,oa.financial_year AS oa_fin_year,oa.sub_number AS oa_sub_number,oa.type AS oa_type,
								inm.discount as discount,inm.is_returnable as is_returnable, m.id as item_id,
								IFNULL(inm.alternate_unit_id, 0) as alternate_unit_id, m.is_service as is_service 
								FROM invoice_materials AS inm
								LEFT JOIN materials AS m ON inm.item_id = m.id AND inm.enterprise_id = m.enterprise_id
								LEFT JOIN unit_master AS u ON u.enterprise_id = m.enterprise_id AND u.unit_id = m.unit
								LEFT JOIN invoice AS inv ON inv.id = inm.invoice_id
								LEFT JOIN order_acknowledgement AS oa ON oa.id = inm.oa_no AND inm.enterprise_id = oa.enterprise_id
								WHERE inm.enterprise_id = {enterprise_id} AND inm.invoice_id IN ({dc_ids}))""".format(
			enterprise_id=enterprise_id, dc_ids=dc_ids)
		materials = dao.executeQuery(dc_materials_query, as_dict=True)
		invoice_dao = InvoiceService()
		for material in materials:
			if material['item_id']:
				dc_pending_qty = float(material['DC_qty']) - float(invoice_dao.getInvoicedItemQuantity(
					item_id=material['item_id'], make_id=material['make_id'], is_faulty=material['is_faulty'],
					enterprise_id=enterprise_id, dc_id=material['dc_id'], invoice_id=invoice_id,
					hsn_code=material['hsn_code']))
			else:
				dc_pending_qty = float(material['DC_qty']) - float(invoice_dao.getInvoicedNonStockItemQuantity(
					material['name'], enterprise_id=enterprise_id, dc_id=material['dc_id'], invoice_id=invoice_id))
			invoice = SQLASession().query(Invoice).filter(
				Invoice.enterprise_id == enterprise_id, Invoice.id == material['id']).first()
			inv_number_format = invoice.enterprise.inv_template_config.template_header_details.inv_number_format
			dc_code = Invoice.generateInternalCode(
				invoice_id=material['id'], invoice_no=material['invoice_no'], invoice_type=material['invoice_type'],
				financial_year=material['fin_year'], sub_number=material['sub_number'], inv_number_format=inv_number_format,
				invoice_code=material['invoice_code'])
			oa_code = ""
			if material['oa_id']:
				oa_code = OA.generateInternalCode(
					financial_year=material['oa_fin_year'], type=material['oa_type'], oa_no=material['oa_oa_no'],
					oa_id=material['oa_id'], sub_number=material['oa_sub_number'])
			enterprise = rh.getSessionAttribute(USER_IN_SESSION_KEY).enterprise
			enterprise.is_multiple_units
			if enterprise.is_multiple_units:
				is_alternate_unit = helper.getAlternateUnitCount(enterprise_id=enterprise_id, item_id=material['item_id'])
			else:
				is_alternate_unit = 0
			material['scale_factor'] = 1
			material['alternate_unit_list'] = []
			if enterprise.is_multiple_units and is_alternate_unit > 0:
				material['alternate_unit_list'] = [{
					"alternate_unit_id": 0, "unit_name": material['unit'], "scale_factor": 1}]
				material['alternate_unit_list'].extend(helper.populateAlternateUnits(
					enterprise_id=enterprise_id, item_id=material['item_id']))
				if material['alternate_unit_id'] and int(material['alternate_unit_id']) != 0:
					scale_factor = helper.getScaleFactor(
						enterprise_id=enterprise_id, item_id=material['item_id'], alternate_unit_id=material['alternate_unit_id'])
					material['unit'] = helper.getUnitName(enterprise_id=enterprise_id, unit_id=material['alternate_unit_id'])
					if scale_factor:
						material['DC_qty'] = Decimal(material['DC_qty']) / Decimal(scale_factor)
						dc_pending_qty = Decimal(dc_pending_qty) / Decimal(scale_factor)
						material['minimum_stock_level'] = Decimal(material['minimum_stock_level']) / Decimal(scale_factor)
						material['price'] = Decimal(material['price']) * Decimal(scale_factor)
						material['scale_factor'] = scale_factor if scale_factor else 1
			material['dc_pen_qty'] = dc_pending_qty
			material['dc_code'] = dc_code
			material['oa_code'] = oa_code
			dc_details.append(material)
	except Exception as e:
		logger.exception("DC Particulars retrieval fails... %s" % e.message)
	return HttpResponse(content=simplejson.dumps(dc_details), mimetype='application/json')


def getOATags(request):
	logger.info("The OA Tags Details Loaded...")
	rh = RequestHandler(request)
	oa_id = rh.getPostData('oa_ids')
	oa_ids = oa_id.split(",")
	oa_tags = []
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		logger.debug("Fetching Tag Details for order {enterprise: %s, oa_id:%s}" % (enterprise_id, oa_ids))
		tags = SQLASession().query(OATag).filter(
			OATag.oa_id.in_(oa_ids), OATag.enterprise_id == enterprise_id).group_by(OATag.tag_id).all()
		for tag in tags:
			oa_tags.append({"tag_id": tag.tag_id, "tag_name": tag.tag.tag})

	except Exception as e:
		logger.exception("OA Tags retrieval fails... %s" % e.message)

	return HttpResponse(content=simplejson.dumps(oa_tags), mimetype='application/json')


def getPOTags(request):
	logger.info("The OA Tags Details Loaded...")
	rh = RequestHandler(request)
	po_id = rh.getPostData('po_id')
	po_tags = []
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		logger.debug("Fetching Tag Details for order {enterprise: %s, oa_id:%s}" % (enterprise_id, po_id))
		tags = SQLASession().query(PurchaseOrderTag).filter(
			PurchaseOrderTag.po_id == po_id, PurchaseOrderTag.enterprise_id == enterprise_id).all()
		for tag in tags:
			po_tags.append({"tag_id": tag.tag_id, "tag_name": tag.tag.tag})

	except Exception as e:
		logger.exception("OA Tags retrieval fails... %s" % e.message)

	return HttpResponse(content=simplejson.dumps(po_tags), mimetype='application/json')


def loadInvoiceTags(request):
	dc_ids = RequestHandler(request).getPostData("dc_ids")
	tag_query = """SELECT DISTINCT (b.tag), a.tag_id FROM invoice_tags AS a, tags AS b 
		WHERE b.id=a.tag_id AND a.invoice_id in (%s) AND b.enterprise_id=a.enterprise_id""" % dc_ids
	try:
		tag_list = executeQuery(tag_query)
	except Exception as e:
		logger.exception(e)
		tag_list = ""
	response = HttpResponse(content=simplejson.dumps(tag_list), mimetype='application/json')
	return response


def getPODate(request):
	logger.info("The Po Date Details Loaded...")
	rh = RequestHandler(request)
	po_id = rh.getPostData('po_id')
	po_date = []
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		logger.debug("Fetching Tag Details for order {enterprise: %s, oa_id:%s}" % (enterprise_id, po_id))
		po = SQLASession().query(PurchaseOrder).filter(
			PurchaseOrder.po_id == po_id, PurchaseOrder.enterprise_id == enterprise_id).first()
		po_date.append({
			"po_date": po.approved_on.strftime('%Y-%m-%d'), "project": po.project.id,
			"issue_to": po.issue_to if po.issue_to else ""})
	except Exception as e:
		logger.exception("PO Date retrieval fails... %s" % e.message)

	return HttpResponse(content=simplejson.dumps(po_date), mimetype='application/json')


def loadPartyRate(request):
	rh = RequestHandler(request)
	party_id = rh.getPostData('party_id')
	item_id = rh.getPostData('item_id')
	make_id = rh.getPostData('make_id')
	oa_type = rh.getPostData('oa_type')
	enterprise_id = rh.getPostData('enterprise_id')
	if oa_type and oa_type == 'Job':
		is_job = 1
	else:
		is_job = 0
	if enterprise_id is None:
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	item_rate = dict(party_id=party_id, item_id=item_id, make_id=make_id, price=0)
	try:
		material_price = MasterDAO().getCurrentMaterialSupplierPrice(
			item_id=item_id, enterprise_id=enterprise_id, supp_id=party_id, make_id=make_id, is_job=is_job)
		if material_price:
			item_rate['price'] = float(material_price.price)
		else:
			item = MasterDAO().getMaterialByItemId(item_id=item_id, enterprise_id=enterprise_id)
			item_rate['price'] = item.price if item else 0
		response = response_code.success()
		response['item_rate'] = item_rate
		response['hsn_code'] = loadHSNCode(enterprise_id=enterprise_id, party_id=party_id, item_id=item_id)
		logger.info("Hsn Code:%s" % response['hsn_code'])

	except Exception as e:
		response = response_code.internalError()
		response['error'] = '%s' % e.message
		logger.exception("OA Party Price retrieval fails... %s" % e.message)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def loadHSNCode(enterprise_id, party_id, item_id):  # load HSN
	hsn_code = ''
	try:
		query = """SELECT im.hsn_code AS hsn_code, i.approved_on, i.status
					FROM invoice_materials im
						JOIN invoice i ON im.invoice_id = i.id
							AND i.enterprise_id = im.enterprise_id
							AND i.party_id = '%s'
							AND im.item_id = '%s'
							AND im.enterprise_id = '%s'
					UNION SELECT m.tariff_no AS hsn_code, NOW() AS approved_on, 0 AS status
						FROM materials m
						WHERE id='%s' AND enterprise_id='%s'
					ORDER BY status DESC, approved_on DESC LIMIT 0, 1""" % (
			party_id if party_id else 'NULL', item_id, enterprise_id, item_id, enterprise_id)
		logger.debug("HSN Query:\n%s" % query)
		rows = executeQuery(query)
		if len(rows) > 0:
			hsn_code = rows[0][0]
	except Exception as e:
		logger.exception("Fetching HSN Code Failed - %s" % e)
		pass
	return hsn_code


def getInvoicedOAQty(item_id, make_id, enterprise_id, oa_id, invoice_id):
	"""

	:param item_id:
	:param make_id:
	:param enterprise_id:
	:param oa_id:
	:param invoice_id:
	:return:
	"""
	try:
		query = SQLASession().query(func.sum(InvoiceMaterial.quantity)).outerjoin(InvoiceMaterial.invoice)
		oa_filter = [
			InvoiceMaterial.item_id == item_id, InvoiceMaterial.make_id == make_id,
			InvoiceMaterial.enterprise_id == enterprise_id, InvoiceMaterial.oa_no == oa_id, Invoice.status > -1,
			Invoice.type != 'JIN', InvoiceMaterial.delivered_dc_id.is_(None)]
		if invoice_id:
			oa_filter.append(InvoiceMaterial.invoice_id != invoice_id)
		count = query.filter(*oa_filter).first()
		oa_pending_qty = count[0] if count[0] else 0
	except Exception as e:
		logger.error("Failed fetching OA material quantity... %s " % e.message)
		oa_pending_qty = 0
	return oa_pending_qty


def getReceivedOAQty(item_id, make_id, enterprise_id, oa_id, receipt_id):
	"""

	:param item_id:
	:param make_id:
	:param enterprise_id:
	:param oa_id:
	:param receipt_id:
	:return:
	"""
	try:
		query = SQLASession().query(func.sum(ReceiptMaterial.quantity)).outerjoin(ReceiptMaterial.receipt)
		oa_filter = [
			ReceiptMaterial.item_id == item_id, ReceiptMaterial.make_id == make_id,
			ReceiptMaterial.enterprise_id == enterprise_id, ReceiptMaterial.oa_id == oa_id, Receipt.status > -1,
			Receipt.received_against == 'Job In', ReceiptMaterial.received_grn_id.is_(None)]
		if receipt_id:
			oa_filter.append(ReceiptMaterial.receipt_no != receipt_id)
		count = query.filter(*oa_filter).first()
		oa_pending_qty = count[0] if count[0] else 0
	except Exception as e:
		logger.error("Failed fetching OA material pending quantity... %s " % e.message)
		oa_pending_qty = 0
	return oa_pending_qty


def getGRNAgainstDCQty(item_id, make_id, is_faulty, enterprise_id, grn_no, invoice_id):
	"""

	:param item_id:
	:param make_id:
	:param is_faulty:
	:param enterprise_id:
	:param grn_no:
	:param invoice_id:
	:return:
	"""
	try:
		query = SQLASession().query(func.sum(InvoiceMaterial.quantity)).outerjoin(InvoiceMaterial.invoice)
		oa_filter = [
			InvoiceMaterial.item_id == item_id, InvoiceMaterial.make_id == make_id, InvoiceMaterial.is_faulty == is_faulty,
			InvoiceMaterial.enterprise_id == enterprise_id, InvoiceMaterial.receipt_no == grn_no, Invoice.status > -1,
			InvoiceMaterial.delivered_dc_id.is_(None)]
		if invoice_id:
			oa_filter.append(InvoiceMaterial.invoice_id != invoice_id)
		count = query.filter(*oa_filter).first()
		oa_pending_qty = count[0] if count[0] else 0
	except Exception as e:
		logger.error("Failed fetching GRN material quantity... %s " % e.message)
		oa_pending_qty = 0
	return oa_pending_qty


def getRecInvoiceQty(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	oa_id = request_handler.getPostData('oa_id')
	enterprise_id = request_handler.getPostData('enterprise_id')
	if enterprise_id is None:
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	oa_inv_qty = 0
	grn_rec_qty = 0
	mi_qty = 0
	try:
		count = SQLASession().query(func.sum(InvoiceMaterial.quantity)).outerjoin(InvoiceMaterial.invoice).filter(
			InvoiceMaterial.enterprise_id == enterprise_id,
			InvoiceMaterial.oa_no == oa_id, Invoice.status > -1).first()
		oa_pending_qty = count[0] if count[0] else 0
		oa_inv_qty = oa_pending_qty
		count = SQLASession().query(func.sum(ReceiptMaterial.quantity)).outerjoin(ReceiptMaterial.receipt).filter(
			ReceiptMaterial.enterprise_id == enterprise_id,
			ReceiptMaterial.oa_id == oa_id, Receipt.status > -1).first()
		grn_received_qty = count[0] if count[0] else 0
		grn_rec_qty = grn_received_qty

		query = """SELECT IFNULL(SUM(im.request_qty), 0) as quantity FROM
			order_acknowledgement AS o
		JOIN
			oa_particulars AS oap ON o.id = oap.oa_id
			AND oap.enterprise_id = o.enterprise_id				
		LEFT JOIN
			oa_indent_map AS oai ON oai.oa_id = o.id
			AND oai.enterprise_id = o.enterprise_id
		LEFT JOIN
			indent_material AS im ON im.indent_no = oai.indent_no
			AND im.enterprise_id = oai.enterprise_id
			AND im.item_id = oap.item_id
		LEFT JOIN
			indents AS ind ON oai.indent_no = ind.indent_no
			AND ind.enterprise_id= oai.enterprise_id
			AND ind.indent_module_id = 1 
		WHERE
			o.status > 0 AND o.enterprise_id ={enterprise_id} AND
			o.id = {oa_id}""".format(enterprise_id=enterprise_id, oa_id=oa_id)
		count = executeQuery(query)
		mi_rec_qty = count[0] if count[0] else 0
		mi_qty = int(mi_rec_qty)
	except Exception as e:
		logger.error("Failed fetching OA material quantity... %s " % e.message)
	if oa_inv_qty > 0 or grn_rec_qty > 0 or mi_qty > 0:
		response = response_code.failure()
		response[
			'custom_message'] = "Warning!! Order cannot be Rejected, as Items have been Issued/Received against this Order!"
	else:
		response = response_code.success()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getInvoice(invoice_id=None, enterprise_id=None):
	return SQLASession().query(Invoice).filter(Invoice.id == invoice_id, Invoice.enterprise_id == enterprise_id).first()


def loadGSTaxes(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	tax_type = request_handler.getPostData('tax_type')
	return HttpResponse(
		content=simplejson.dumps(getEnterpriseTaxDumps(enterprise_id=enterprise_id, tax_type=tax_type)),
		mimetype='application/json')


def getEnterpriseTaxDumps(enterprise_id, tax_type=None):
	"""

	:type enterprise_id: Integer
	:param enterprise_id:
	:param tax_type:
	:return:
	"""
	tax_choices_dump = []
	if tax_type:
		taxes = SQLASession().query(Tax).filter(Tax.enterprise_id == enterprise_id, Tax.type == tax_type).order_by(
			Tax.net_rate).all()
	else:
		taxes = SQLASession().query(Tax).filter(Tax.enterprise_id == enterprise_id).order_by(Tax.net_rate).all()
	for tax in taxes:
		tax_choices_dump.append({"code": tax.code, "rate": tax.net_rate})
	return tax_choices_dump


def approveInvoice(request):
	"""
	Assigns a permanent identification number for the Invoice. Sets the Invoice status as approved.

	:param request:
	:return: JSON dumps to the Ajax call that initiated this approval
	"""
	logger.info("Invoice Approval Triggered...")
	request_handler = RequestHandler(request)
	invoice_id = request_handler.getPostData('invoice_id')
	enterprise_id = request_handler.getPostData('enterprise_id')
	remarks = request_handler.getPostData('remarks')
	user_id = request_handler.getPostData('user_id')
	primary_enterprise_id = request_handler.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
	if not user_id:
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		logger.info('user id: %s' % user_id)
	if enterprise_id is None:
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		logger.info("Approving Invoice - User ID : %s" % user_id)
		logger.info('Readying for approval of Invoice... %s ' % invoice_id)
	isprimary_project = enterprise_id == primary_enterprise_id
	sales_service = InvoiceService()
	approved_invoice = sales_service.approveInvoice(
		invoice_id=invoice_id, remarks=remarks, enterprise_id=enterprise_id, approved_by=user_id, isprimary_project=isprimary_project)
	response = response_code.success()
	if approved_invoice[0]:
		if approved_invoice[1]:
			response['custom_message'] = '%sInvoice No: %s has been approved successfully' % ("" if isprimary_project else "Internal ", approved_invoice[
				0].getCode())
		else:
			response['custom_message'] = '%sInvoice has been already Approved - Invoice No: %s' % ("" if isprimary_project else "Internal ", approved_invoice[
				0].getCode())
		response['code'] = approved_invoice[0].getCode()
		return HttpResponse(simplejson.dumps(response))
	else:
		response = response_code.internalError()
		response['error'] = 'Approval Failed...'
		response['custom_message'] = response['error']
		return HttpResponse(response)


def rejectInvoice(request):
	"""
	Rejects

	:param request:
	:return:
	"""
	logger.debug('Rejecting a Invoice...')
	request_handler = RequestHandler(request)
	inv_id = request_handler.getPostData('invoice_id')
	enterprise_id = request_handler.getPostData('enterprise_id')
	rejection_remarks = request_handler.getPostData('remarks')
	user_id = request_handler.getPostData('user_id')
	invoice_service = InvoiceService()
	if inv_id is None:
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		if request_handler.isSessionActive() and request_handler.isPostRequest():
			inv_id = request_handler.getPostData('inv_id')
			rejection_remarks = request_handler.getPostData('remarks')
			chk_invoice = invoice_service.invoice_dao.checkInvoice(invoice_id=inv_id, enterprise_id=enterprise_id)
			if chk_invoice == 0:
				invoice_rejection_dump = invoice_service.rejectInvoice(
					invoice_id=inv_id, rejection_remarks=rejection_remarks, rejected_by=user_id,
					enterprise_id=enterprise_id)
			else:
				invoice_rejection_dump = -1
			return HttpResponse(simplejson.dumps(invoice_rejection_dump))
		return HttpResponseRedirect(properties.LOGIN_URL)
	else:
		chk_invoice = invoice_service.invoice_dao.checkInvoice(invoice_id=inv_id, enterprise_id=enterprise_id)
		if chk_invoice == 0:
			response = response_code.success()
			invoice_service.rejectInvoice(
				invoice_id=inv_id, rejection_remarks=rejection_remarks, rejected_by=user_id,
				enterprise_id=enterprise_id)
		else:
			response = response_code.internalError()
			response['error'] = 'Approval Failed...'
		return HttpResponse(simplejson.dumps(response))


def sales_report_page(request):
	request_handler = RequestHandler(request)
	user = request_handler.getSessionAttribute(USER_IN_SESSION_KEY)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	primary_enterprise_id = request_handler.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
	master_dao = MasterDAO()
	suppliers = populatePartyChoices(enterprise_id=primary_enterprise_id, populate_all=True, is_customer=True)
	materials = master_dao.getAllMaterialNames(enterprise_id=primary_enterprise_id)
	for material in materials:
		material['name'] = "%s%s" % (material['name'], "[%s]" % material['make_name'] if material['make_name'] else "")
		material['name'] = "%s - %s" % (material['name'], material['drawing_no'])
		material['item_id'] = "%s" % (material['item_id'])
	return TemplateResponse(template='sales/salesreport.html', request=request, context={'suppliers': suppliers,
	  	'materials': materials, TEMPLATE_TITLE_KEY: "Sales Report" if enterprise_id == primary_enterprise_id else "Internal Invoice Report"})


def checkAgainstIssueReturnMaterial(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	invoice_id = request_handler.getPostData('invoice_id')
	make_id = request_handler.getPostData('make_id')
	item_id = request_handler.getPostData('item_id')
	is_faulty = 1
	if request_handler.getPostData('is_faulty') == 'False':
		is_faulty = 0
	return_materials = []
	try:
		if invoice_id:
			query = """ SELECT IFNULL(SUM(acc_qty),0)  
			FROM grn as r, grn_material as grn_mat 
			WHERE r.grn_no=grn_mat.grnNumber AND grn_mat.dc_id={invoice_id} AND r.rec_against='Issues' 
			AND grn_mat.enterprise_id={enterprise_id}  AND grn_mat.item_id = '{item_id}' 
			AND grn_mat.make_id={make_id} AND grn_mat.is_faulty={is_faulty}""".format(
				enterprise_id=enterprise_id, invoice_id=invoice_id, item_id=item_id,
				make_id=make_id, is_faulty=is_faulty)
			return_materials = executeQuery(query)
	except Exception as e:
		logger.exception(e)
		return_materials = []
	return HttpResponse(content=simplejson.dumps(return_materials), mimetype='application/json')


def itemInvoiced(request):
	item_list = []
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		dc_id = request_handler.getPostData('dc_id')
		item_list = InvoiceService().getInvoicedItems(enterprise_id=enterprise_id, dc_id=dc_id)
	except Exception as e:
		logger.info("Fetching Invoice Details are Failed:%s" % e.message)
		logger.debug(e)
	return HttpResponse(content=json.dumps(item_list, cls=DatetimeEncoder), mimetype='application/json')


def getInvoicedSalesEstimateQty(item_id, make_id, enterprise_id, se_id, invoice_id):
	"""

	:param item_id:
	:param make_id:
	:param enterprise_id:
	:param se_id:
	:param invoice_id:
	:return:
	"""
	try:
		query = SQLASession().query(func.sum(InvoiceMaterial.quantity)).outerjoin(InvoiceMaterial.invoice)
		se_filter = [
			InvoiceMaterial.item_id == item_id, InvoiceMaterial.make_id == make_id,
			InvoiceMaterial.enterprise_id == enterprise_id, InvoiceMaterial.se_no == se_id, Invoice.status > -1,
			Invoice.type != 'JIN', InvoiceMaterial.delivered_dc_id.is_(None)]
		if invoice_id:
			se_filter.append(InvoiceMaterial.invoice_id != invoice_id)
		count = query.filter(*se_filter).first()
		se_pending_qty = count[0] if count[0] else 0
	except Exception as e:
		logger.error("Failed fetching Sales Estimate material quantity... %s " % e.message)
		se_pending_qty = 0
	return se_pending_qty


def checkConsignment(request):
	logger.info("The Check Consignment Loaded...")
	rh = RequestHandler(request)
	invoice_id = rh.getPostData('invoice_id')
	consignment_data = []
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		cons_details = SQLASession().query(Invoice).filter(
			Invoice.id == invoice_id, Invoice.enterprise_id == enterprise_id).first()
		is_consignment = ""
		cons_qty = ""
		cons_weight = ""
		if cons_details.is_courier:
			is_consignment = cons_details.is_courier
		if cons_details.no_of_consignment:
			cons_qty = cons_details.no_of_consignment
		if cons_details.weight:
			cons_weight = cons_details.no_of_consignment
		consignment_data.append({"is_consignment":is_consignment, "cons_qty": cons_qty, "cons_weight": cons_weight})
	except Exception as e:
		logger.exception("Consignment Details retrieval fails... %s" % e.message)

	return HttpResponse(content=simplejson.dumps(consignment_data), mimetype='application/json')


def update_enterpriseid_in_session(request):
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getData('enterprise_id')
		request.session[ENTERPRISE_ID_SESSION_KEY] = int(enterprise_id)
		token = request_handler.getSessionAttribute(LOGIN_TOKEN)
		decoded_token = jwt.decode(token, JWT_SECRET)
		decoded_token['enterprise_id'] = int(enterprise_id)
		request.session[LOGIN_TOKEN] = jwt.encode(decoded_token, JWT_SECRET)
		response = response_code.success()
	except Exception as e:
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content = json.dumps(response), mimetype='application/json')


def getTotalCreditDebit(request):
	rh = RequestHandler(request=request)
	primary_enterprise_id = rh.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
	user_id = rh.getSessionAttribute(SESSION_KEY)
	project_id = rh.getData('project_id')
	is_ledger = rh.getData('is_ledger')
	is_root_level = rh.getData('is_root_level')
	try:
		if project_id != None:
			is_ledger = True if str(is_ledger) == "true" else False
			is_root_level = True if str(is_root_level) == "true" else False
			project_enterprise_id = getProjectEnterpriseIdByProjectId(project_id)
			current_month = datetime.now().strftime("%b-%y")
			from_date, to_date = getMonthStartEndDate(current_month)
			response = response_code.success()
			if is_ledger:
				response['data'] = getProjectForecast(enterprise_id=project_enterprise_id, project_id=project_id,
													  primary_enterprise_id=primary_enterprise_id,
													  is_root_level=is_root_level)
			else:
				from_date = getFinancialStartEndDate()
				response['data'] = getTotalCreditDebitValue(enterprise_id=project_enterprise_id,
															from_date=from_date[0], to_date=to_date)
				response['budget_amount'] = float(getTotalExpensesBudgetAmount(from_date=from_date[0],
																			   to_date=to_date, project_id=project_id))
				response['data'][0]["cashflow_amount"] = float(response['data'][0]["total_credit"]) - float(response['data'][0]["total_debit"])
		else:
			response = response_code.paramMissing()
			response['error'] = "Invalid Project ID"
	except Exception as e:
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(simplejson.dumps(response), 'content-type=text/json')


def get_project_wise_cashflow_overview(request):
	response = {"Status_code": 200, "response_message": "Data fetched successfully", "data": []}
	rh = RequestHandler(request=request)
	primary_enterprise_id = rh.getData('primary_enterprise_id')
	start_month = rh.getData('start_month')
	end_month = rh.getData('end_month')
	overview_data = {}
	current_month = datetime.now().strftime("%B-%Y")
	start_date, end_date = get_start_end_date_from_month(start_month=start_month if start_month else current_month,
														 end_month=end_month if end_month else current_month)
	try:
		projects_list = populateAllProjects(enterprise_id=primary_enterprise_id)
		if projects_list:
			for project in projects_list:
				if int(primary_enterprise_id) != project.get("project_enterprise_id"):
					expenses = 0
					revenue = 0
					overview_data["Project_id"] = project.get("id")
					overview_data["project_name"] = project.get("name")
					overview_data["project_code"] = project.get("code")
					overview_data["cash_allocated"] = str(project.get("cash_allocated"))
					actual_cash_flow_data = get_actual_cashflow_record(enterprise_id=project.get("project_enterprise_id"),
																	   start_date=start_date, end_date=end_date)
					for cash_flow in actual_cash_flow_data:
						revenue += cash_flow.get("total_credit")
						expenses += cash_flow.get("total_debit")
					overview_data["actual_cashflow"] = str(revenue - expenses)
					revenue_budget = float(getTotalRevenueBudgetAmount(from_date=start_date, to_date=end_date,
																	   project_id=project.get("id")))
					expenses_budget = float(getTotalExpensesBudgetAmount(from_date=start_date,
																		 to_date=end_date,
																		 project_id=project.get("id")))

					overview_data["budgeted_cashflow"] = float(revenue_budget - expenses_budget)
					overview_data["surplus_or_deficit"] = str(float(overview_data["cash_allocated"]) - float(overview_data["actual_cashflow"]))
					response["data"].append(overview_data)
					overview_data = {}
		else:
			response = {"Status_code": 400, "response_message": "Enter the valid Project"}
			return HttpResponse(json.dumps(response), status=400, mimetype='application/json')
		return HttpResponse(json.dumps(response), status=200, mimetype='application/json')
	except Exception as e:
		logger.info("Failed to generate the project wise cashflow overview: %s", e)
		response = {"Status_code": 500, "response_message": "Internal Server Error"}
		return HttpResponse(json.dumps(response), status=500, mimetype='application/json')


def get_cashflow_overview_template(request):
	response = {"Status_code": 200, "response_message": "Template fetched successfully", "data": [],
				TEMPLATE_TITLE_KEY: "Cashflow Overview"}
	try:
		return TemplateResponse(request=request, template='sales/cashflow_overview.html', context=response)
	except Exception as e:
		logger.info("Cash flow overview failed: %s", e)


def consolidated_cashflow_report(request):
	try:
		return TemplateResponse(request=request, template='sales/consolidated_cashflow.html',
								context={TEMPLATE_TITLE_KEY: "Consolidated Report"})
	except Exception as e:
		logger.info("Consolidated Report failed: %s", e)


def getInternalInvoiceQty(request):
	rh = RequestHandler(request=request)
	primary_enterprise_id = rh.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
	internaloa_id = rh.getData('internaloa_id')
	item_id = rh.getData('item_id')
	try:
		if internaloa_id is not None:
			stores = StoresDAO()
			internal_oa = stores.db_session.query(OA).filter(OA.id == internaloa_id).first()
			if internal_oa:
				oa_code = internal_oa.getSimpleCode()
				enterprise_id = internal_oa.enterprise_id
				inv_qty = 0
				for rec in internal_oa.items:
					if int(item_id) == rec.item_id:
						inv_qty = getInvoicedOAQty(item_id=item_id, make_id=1, enterprise_id=enterprise_id, oa_id=internaloa_id,
													   invoice_id=None)
						if rec.alternate_unit_id:
							scale_factor = helper.getScaleFactor(
								enterprise_id=primary_enterprise_id, item_id=item_id, alternate_unit_id=rec.alternate_unit_id)
							if scale_factor:
								inv_qty = Decimal(inv_qty) / Decimal(scale_factor) if inv_qty != 0 else 0
						break
				response = response_code.success()
				response['code'] = oa_code
				response['invoice_qty'] = inv_qty
			else:
				response = response_code.paramMissing()
				response['error'] = "Invalid Internal OA Id"
		else:
			response = response_code.paramMissing()
			response['error'] = "Invalid Internal OA Id"
	except Exception as e:
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(simplejson.dumps(response), 'content-type=text/json')


def saveProjectCashflow(request):
	rh = RequestHandler(request=request)
	primary_enterprise_id = rh.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
	user_id = rh.getSessionAttribute(SESSION_KEY)
	project_id = rh.getPostData('project_id')
	forecast = rh.getPostData('forecast')
	working_capital = rh.getPostData('working_capital')
	project_owner = rh.getPostData('project_owner')
	email = rh.getPostData('email')
	phone_no = rh.getPostData('phone_no')
	try:
		if project_id is not None and forecast is not None and working_capital is not None:
			project_forecast = ProjectForecastService()
			project_forecast.construct_cash_flow(project_id,json.loads(forecast),primary_enterprise_id,user_id)
			project_forecast.updateProjectsInfo(project_id, working_capital, project_owner, email, phone_no)
			response = response_code.success()
		else:
			response = response_code.paramMissing()
			response['error'] = "Invalid Project Id and Forecast values"
	except Exception as e:
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(simplejson.dumps(response), 'content-type=text/json')


def getForecastVersions(request):
	rh = RequestHandler(request=request)
	primary_enterprise_id = rh.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
	project_id = rh.getData('project_id')
	forecast_version_details = []
	try:
		if project_id:
			collection = 'project_forecast'
			db = MongoDbConnect[collection]
			forecast_data = db.find({"enterprise_id": int(primary_enterprise_id), "project_id": int(project_id)}).sort("created_on", 1)
			for rec in forecast_data:
				user = UserDAO().getUserById(enterprise_id=primary_enterprise_id, user_id=str(rec['created_by']))
				forecast_version = {}
				forecast_version['doc_id'] = str(rec['_id'])
				forecast_version['created_by'] = user.first_name + "%s" %(" " + user.last_name if user.last_name else "")
				forecast_version['created_on'] = rec['created_on'].strftime("%Y-%m-%d %H:%M:%S")
				forecast_version_details.append(forecast_version)
			response = response_code.success()
			response['data'] = forecast_version_details
		else:
			response = response_code.paramMissing()
			response['error'] = "Invalid Project Id"
	except Exception as e:
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getForecastByObjectId(request):
	rh = RequestHandler(request=request)
	primary_enterprise_id = rh.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
	object_id = rh.getData('object_id')
	project_id = rh.getData('project_id')
	forecast_details = {}
	try:
		if object_id and project_id:
			collection = 'project_forecast'
			db = MongoDbConnect[collection]
			forecast_data = db.find({"_id": ObjectId(str(object_id))})
			for rec in forecast_data:
				user = UserDAO().getUserById(enterprise_id=primary_enterprise_id, user_id=str(rec['created_by']))
				budget_flow = rec['forecast']
				budget_flow['months'] = rec['months']
				forecast_details['object_id'] = str(rec['_id'])
				forecast_details['created_by'] = user.first_name + "%s" %(" " + user.last_name if user.last_name else "")
				forecast_details['created_on'] = rec['created_on'].strftime("%Y-%m-%d %H:%M:%S")
				forecast_details['forecast'] = budget_flow
			response = response_code.success()
			response['data'] = forecast_details
		else:
			response = response_code.internalError()
			response['error'] = "Invalid Object Id"
	except Exception as e:
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getLedgerNameByAccountGroupId(request):
	rh = RequestHandler(request=request)
	primary_enterprise_id = rh.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
	account_group_id = rh.getData('account_grp_id')
	ledger_name = {}
	if account_group_id:
		query = """SELECT al.name, al.id FROM account_ledgers AS al JOIN account_groups AS ag ON ag.id = al.group_id 
		and al.enterprise_id = ag.enterprise_id WHERE ag.id = {account_grp_id} AND ag.enterprise_id = {enterprise_id} 
		AND al.status = 1 """.format(account_grp_id=account_group_id, enterprise_id=primary_enterprise_id)
		query_data = executeQuery(query)
		for rec in query_data:
			ledger_name[str(rec[1])] = str(rec[0])

		response = response_code.success()
		response['data'] = ledger_name
	else:
		response = response_code.paramMissing()
		response['error'] = "Invalid Project Id and Account group Id"
	return HttpResponse(json.dumps(response), 'content-type=text/json')