"""
"""
from django.conf.urls import patterns, url

from erp.icd import json_api
from erp.icd.json_api import updateNotePartyStatus, updateNoteStatusforParty, checkPendingAckICD
from erp.icd.views import renderAuditHome, saveAuditNote, loadNoteList, loadNoteTaxes, uploadInvoiceDocument, \
	loadReceiptMaterial, loadGeneralDetails, loadGRNPoDocuments, load_icd_tag, \
	load_grn_tax, renderNote, saveNote, editNote, loadNoteTaxEdit, checkForMatchingInvoice, \
	loadInvoiceDocuments, getNoteEinvoiceList, getEinvoiceJson, cancelIrn

__author__ = 'kalaivanan'

urlpatterns = patterns(
	'',
	url('home/$', renderAuditHome),
	url('icd/$', renderAuditHome),
	url('icd/check_invoice_no/$', checkForMatchingInvoice),
	url('json/icdload/', loadNoteList),
	url('json/loadgrntax/$', load_grn_tax),
	url('json/loadicdtags/$', load_icd_tag),
	url('json/loadcrdrtaxes/$', loadNoteTaxes),
	url('json/icd/loadgeneraldetails/$', loadGeneralDetails),
	url('json/icd/loadgrnmaterial/$', loadReceiptMaterial),
	url('json/icdedit/$', saveAuditNote),
	url('json/icd/upload_invoice/$', uploadInvoiceDocument),
	url('json/icd/load_grn_pos_doc/$', loadGRNPoDocuments),
	url('json/icd/load_invoice_doc/$', loadInvoiceDocuments),
	url('json/icd/getNoteEinvoiceList/$', getNoteEinvoiceList),
	url('json/icd/getEinvoiceJson/$', getEinvoiceJson),
	url('json/icd/cnlIrn/$', cancelIrn),


	# Note Creation
	url('note/$', renderNote),
	url('note/addNote/$', saveNote),
	url('note/edit/$', editNote),
	url('json/note/editTax/$', loadNoteTaxEdit),

	# Audit json API Service
	url('json/pendingGrn/', json_api.getCheckedGRN),
	url('json/grnMaterials/', json_api.grnMaterials),
	url('json/returnGrn/', json_api.returnOnAudit),  # Make as RETURNED
	url('json/verifyNote/', json_api.verifyNote),  # Make as VERIFIED
	url('json/downloadDoc/', json_api.downloadDoc),
	url('json/super_edit_note_code/', json_api.superEditNoteCode),
	url('json/party_status/', updateNotePartyStatus),
	url('json/party_status_update/$', updateNoteStatusforParty),
	url('json/check_icd_pending_ack/$', checkPendingAckICD),
)
