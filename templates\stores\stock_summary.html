{% extends "stores/sidebar.html" %}
{% block stockreport %}
{% if access_level.view %}

<style>
	li.stock_report_menu a{
		outline: none;
		background-color: #e6983c !important;
	}
	#locationList{
	margin-top:0px;
	min-width : 200px;
	}
	.checkbox{
		white-space: normal
	}
	.custom-table tbody tr td{
	width:150px !important;
	}
</style>
<div class="right-content-container download-icon-onTop">
	<div class="page-title-container">
		<span class="page-title">Stock Summary</span>
	</div>
	<div class="container-fluid">
		<div class="row">
			<div class="col-lg-12">
				<div class="content_bg">
					<ul class="resp-tabs-list hor_1"></ul>
					<div class="resp-tabs-container hor_1">
						<div class="row">&nbsp;</div>
						<div class="row">
							<div class="add_table">
								<div class="col-lg-12 add_table_content">
									<div id="Closing_Stock">
										<div class="col-lg-12">
												<div>&nbsp;</div>
												<div align="center">
													<h3 style="margin: 0; margin-bottom: 5px;">Location Wise Closing Stock Report</h3>
												</div>

												<div id="stock_type_div" class="col-sm-2 multiselect_option" style="position: absolute;right: 10%;width: 38px;z-index: 1;top: 29px;width: 7%;">
													<input type="checkbox" class="chkcase" name="case" id="chkfaulty"  value="" />
													<label style="font-size: 13px;" for="chkfaulty">Is Faulty</label>
												</div>

												<div class="col-sm-1" style="position: absolute;right: 4%;width: 38px;z-index: 1;top: 1px;width: 7%;">
													<div class="material_txt">
														<span class="po_title_txt">&nbsp;</span>
														<input type="submit" class="btn btn-save" value="View Report" id="reportview"/>
													</div>
												</div>
											<div class="csv_export_button" style="top:21px;">
											  <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#tcs_report'), 'Location_wise_stock_report.csv']);" data-tooltip="tooltip" title="Download stock summary Report as CSV">
											  <i class="fa fa-download" aria-hidden="true"></i>
											  </a>
										   </div>
											<div id="tcs_report_filter" style="top: 58px;left: 16px;" class="dataTables_filter"><label><input type="search" id="filter_textbox" class="custom-form-control" placeholder="Quick filter.." aria-controls="tcs_report" style="margin-left: 0px;"></label></div>
											<div class="table-responsive" style="max-width: 100%;">
												<table class="table table-bordered custom-table table-striped" id="tcs_report" style="width: 100%;">
												  <thead>
													 <tr>
														<th>Category</th>
														<th>Unit Name</th>
														<th>Drawing No</th>
														<th style="width:200px">Item Name</th>
														<th>Price</th>
														<th >Total</th>
														<th>Closing Stock Qty</th>

													 </tr>
												  </thead>
												  <tbody id="issue_table_list_tbody">
												  </tbody>
											   </table>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						{% block stock_statement %}
					    {% endblock %}
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	$(window).load(function() {
    var oTable;
    var oSettings;
    var hash = window.location.hash;
    hash && $('ul.nav a[href="' + hash + '"]').tab('show');

    $('.nav-tabs a').click(function(e) {
        $(this).tab('show');
        var scrollmem = $('body').scrollTop() || $('html').scrollTop();
        window.location.hash = this.hash;
        $('html,body').scrollTop(scrollmem);
    });

    TableHeaderFixed();
    NavTableRemove();
    $("#loading").hide();
});

function TableHeaderFixed() {
    oTable = $('#tcs_report').DataTable({
        fixedHeader: false,
        "scrollY": Number($(document).height() - 230),
        "scrollX": true,
        "pageLength": 50,
        "search": {
            "smart": false
        },
        "columns": [
            null, null, null, null, null, null, null
        ]
    });
	$('#filter_textbox').on('keyup change', function() {
        oTable.search(this.value).draw();
    });
    oTable.on("draw", function() {
        var keyword = $('#tcs_report_filter > label:eq(0) > input').val();
	   $('#tcs_report').unmark();
	   $('#tcs_report').mark(keyword,{});
	   setHeightForTable();
	   listTableHoverIconsInit('tcs_report');
	});

    oTable.on('page.dt', function() {
        $('html, body').animate({
            scrollTop: $(".dataTables_wrapper").offset().top - 15
        }, 'slow');
        listTableHoverIconsInit('tcs_report');
    });

    oSettings = oTable.settings();
    listTableHoverIconsInit('tcs_report');
    $(window).resize();
}

function NavTableRemove() {
    $('ul.nav-tabs li a').click(function() {
        if ($(this).attr('id') !== "tab_view") {
            if ($("#tcs_report").hasClass('dataTable')) {
                oTable.destroy();
            }
        } else {
            TableHeaderFixed();
        }
    });
}

$("#reportview").click(function() {
	$('#loading').show();
    var is_faulty = $("#chkfaulty").is(':checked') ? 1 : 0;
    var material_details = { "is_faulty": is_faulty, "invoice_id": "", "item_id": "" };

    $.ajax({
        url: '/erp/stores/json/issue/location_wise_closingstock/',
        method: 'POST',
        dataType: 'json',
        data: {
            "enterprise_id": "102",
            "material": JSON.stringify(material_details)
        },
        success: function(response) {
            populateTable(response.data);
            $('#loading').hide();
        }
    });
});

async function populateTable(response) {
	var is_faulty = $("#chkfaulty").is(':checked') ? 1 : 0;
    const locationListAll = await getLocations();
    const fromLocation = "from";
    Locations = locationListAll[fromLocation]
    const closingStockLocations = Locations.map(location => location.name);
    if ($.fn.DataTable.isDataTable('#tcs_report')) {
        oTable.destroy();
    }

    $("#tcs_report tbody").empty();
    const dynamicHeaderRow = `
        <tr>
            <th rowspan="2">Category</th>
            <th rowspan="2">Unit Name</th>
            <th rowspan="2">Drawing No</th>
            <th rowspan="2" style="width:200px">Item Name</th>
            <th rowspan="2">Price</th>
            <th rowspan="2">Total</th>
            <th colspan="${closingStockLocations.length}">Closing Stock</th>
        </tr>
        <tr>
            ${closingStockLocations.map(locationName => `<th>${locationName}</th>`).join('')}
        </tr>
    `;
    $("#tcs_report thead").html(dynamicHeaderRow);
    oTable = $('#tcs_report').DataTable({
        fixedHeader: false,
        "scrollY": Number($(document).height() - 230),
        "scrollX": true,
        "pageLength": 50,
        "search": {
            "smart": true
        },
        "columns": Array(closingStockLocations.length + 6).fill(null)
    });
    response.forEach(function(item) {
    	const closingStockMap = item.closing_stocks.reduce((map, stock) => {
			map[stock.location_name] = stock.qty;
			return map;
		}, {});
    	const totalClosingStocks = closingStockLocations.reduce((total, locationName) => {
			return total + (closingStockMap[locationName] || 0);
		}, 0);
		itemName = is_faulty == '1' ? item.item_name + " -(FAULTY)" : item.item_name
        oTable.row.add([
            item.category,
            item.unit_name,
            item.drawing_no,
            itemName,
            item.price,
            totalClosingStocks,
            ...closingStockLocations.map(locationName => closingStockMap[locationName] || 0)
        ]).draw(false);
    });
}


</script>

{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}

{% endblock %}