<style>
	.table.row-seperator th,
	.table.row-seperator td,
	.table.row-seperator th,
	.table.row-seperator td {
	    border-top: 1px solid #666666 !important;
	    border-bottom: 1px solid #666666 !important;
	}

	.table.column-seperator th,
	.table.column-seperator td,
	.table.column-seperator th,
	.table.column-seperator td {
	    border-right: 1px solid #666666 !important;
	    border-left: 1px solid #666666 !important;
	}

</style>
<table>
	<tr>
		<th>S.No</th>
		<th>Description</th>
		<th>Quantity</th>
		<th>Unit</th>
		<th>Price</th>
		<th>Total Value</th>
	</tr>
	{% for oa_item in oa_item_details %}
		<tr>
			<td>{{forloop.counter}}.</td>
			<td class="description" style="width:450px;">
				<span>{{ oa_item.material_name }}</span>
				{% if oa_item.material_drawing_no != "" %} <br><span>{{ oa_item.material_drawing_no }}</span>{% endif %}
				{% if oa_item.material_make != "" %} <br><span>{{ oa_item.material_make }}</span>{% endif %}
				{% if oa_item.is_faulty == 1 %} <br><span>[Faulty]</span>{% endif %}
				{% if oa_item.hsn_code != "" %} <br>HSN/SAC: <span>{{ oa_item.hsn_code }}</span>{% endif %}
				{% if oa_item.discount > 0 %} <br>Discount: <span>{{ oa_item.discount }}</span>{% endif %}
			</td>
			<td class="align_table">{{ oa_item.material_quantity }}</td>
			<td>{{ oa_item.material_unit }}</td>
			<td class="align_table">{{ oa_item.material_rate }}</td>
			<td class="align_table">{{ oa_item.material_taxable_value }}</td>
		</tr>
	{% endfor %}
	{% for tax in oa_taxes %}
		<tr {% if appendix_pages > 0 %} style="display:none;" {% endif %}>
			<td colspan="4" class="tax_detail">{{ tax.tax_name }} @ {{ tax.tax_rate }} %</td>
			<td colspan="2" class="tax_detail">{{ tax.tax_value }}</td>
		</tr>
	{% endfor %}
	<tr >
		{% if appendix_pages > 0 %}
			<td colspan="4" class="total" >Total</td>
		{% else %}
			<td colspan="4" class="grand_total" >Grand Total</td>
		{% endif %}
		<td colspan="2" class="grand_total">{% if appendix_pages > 0 %} <b>{{ total_value }}</b>  {% else %}{{ source.currency }} <b>{{ source.grand_total }}</b>{% endif %}</td>
	</tr>
	<tr {% if appendix_pages > 0 %} style="display:none;" {% endif %}>
		<td colspan="12" class="total_words"><b>Total Value ({{ source.currency }}) : </b> {{ total_in_words|upper }}</td>
	</tr>
	{% if appendix_pages = 0 and source.special_instructions %}
		<tr>
			<td colspan="12" class="conditions"><b>Special Instructions:</b> {{ source.special_instructions }}
			</td>
		</tr>
	{% endif %}
	<tr>
		<td colspan="12" class="test_environment"><i>For {{ source.enterprise.name }}</i></td>
	</tr>
</table>