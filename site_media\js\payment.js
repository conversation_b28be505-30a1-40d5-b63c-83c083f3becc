$("document").ready(function(){
    $(".toggle-handle").append("<circle></circle>");
    $("select").chosen();
    $("#payment_debtors").val("0");
    $("#payment_creditors").val("0");
    $("#payment_debtors, #payment_creditors").change(function(){
        $("#loading").show();
        generateVoucherBillDetails();
        loadLedgerAndBills($(this).val(), !$("#payment_type").is(":checked"));
    });
    CreateVoucherOrBill();
    transformToDummyForm('tag');
    applyValidation();
    paymentTypeInit();
    dateRangePickerCloseEvent();

    $("#unsettled_advance").click(function(){
        var advance_particulars_json = $("#id-advance_particulars_json").text();
        if (advance_particulars_json !== "") {
            populateUnsettledParticulars(JSON.parse(advance_particulars_json), "Advance");
        }
    });

    $("#unsettled_excess").click(function(){
        ledger_id = $("#payment_type").is(":checked") ? $("#payment_creditors").val(): $("#payment_debtors").val();
        getAgeingDetails(ledger_id, !$("#payment_type").is(":checked"), 'ledger_bills', 0, 0, true);
        $(".toggle_view").click();
    });

    $("#unsettled_billable").click(function(){
        var billable_particulars_json = $("#id-billable_particulars_json").text();
        if (billable_particulars_json !== "") {
            populateUnsettledParticulars(JSON.parse(billable_particulars_json), "Un-Billed");
        }
    });
    $(".toggle").removeClass("off");

    $("#id_download_csv_file").click(function(){
       GeneralExportTableToCSV.apply(this, [$('#bill_past_settlement'), $("#id_csv_download_name").val() + ".csv"]);
    });

    $("#id_print_outstanding_bill, #id_print_details").click(function(){
            $("#loading").show();
            ledger_id = $("#payment_type").is(":checked") ? $("#payment_creditors").val(): $("#payment_debtors").val();
            ledger_name = $("#payment_type").is(":checked") ? $("#payment_creditors option:selected").text(): $("#payment_debtors option:selected").text();
            is_debit = $("#payment_type").is(":checked") == true ? 1:0;
            $(".bill_past_settlement_pdf, .advance_outstanding_pdf").removeClass('hide');
            $(".outstanding_pdf").addClass('hide');
            start_date = moment($("#from_date").val()).format('MMM D, YYYY');
            end_date = moment($("#to_date").val()).format('MMM D, YYYY');
            var summary_date_range = start_date + " - " + end_date
            if($(this).attr('id') == 'id_print_details'){
                data = $("#id_bill_past_settlement_pdf").html()
                show_advance_outsourcing = false
                if($("#id_print_details").attr("data-original-title") == "Print Payment Advice"){
                    section_title = "Payment Advice"
                    subject_title = "payment"
                }else{
                    section_title = "Receipt Acknowledgement"
                    subject_title = "receipt"
                }
                var mail_subject = "Settlement details for the " + subject_title + " transactions made during the period " + summary_date_range
            }else{
                data = $("#id_party-bill-details-container").html()
                show_advance_outsourcing = true
                if($("#id_print_details").attr("data-original-title") == "Print Payment Advice"){
                    section_title = "Payable OutStanding Summary"
                }else{
                    section_title = "Receivable OutStanding Summary"
                }
                var mail_subject = section_title + " from " +$("#id_ledger_name").val() + " during the period " + summary_date_range
            }
            $("#id_mail_subject").val(mail_subject)
            if ($("#id_party_name").val() != "" && $("#id_party_name").val() != null){
                var file_name = $("#id_party_name").val() + "-" + section_title + "-" + start_date + "-" + end_date
            }
            else{
                var file_name = ledger_name + "-" + section_title + "-" + start_date + "-" + end_date
            }
            $("#id_csv_download_name").val(file_name)
            $.ajax({
                url: "/erp/accounts/json/generate_payment_summary/",
                type: "post",
                datatype: "json",
                data: {
                    ledger_id:ledger_id,
                    response_data_type: 'data',
                    data: data,
                    ageing_table: $("#id_ageing_table").html(),
                    outsourcing_value: $("#party_bill_details tbody tr .due_total").text(),
                    advance_outstanding: $("#unsettled_advance").text(),
                    ledger_name: ledger_name,
                    show_advance_outsourcing: show_advance_outsourcing,
                    section_title: section_title,
                    from_date: start_date,
                    to_date: end_date},
                success: function (response) {
                      if (response.response_message == "Success") {
                          $("#bill_outstanding_document_modal").modal("show");
                          var row = '<object id="inv_document" class="document_viewer" data="'+response.url+'">'+getPdfErrorMessage()+'</object>';
                          $("#bill_outstanding_document_container").html(row);
                      }else {
                        swal({title:"OOPS!!", text:response.response_message + ".<br/>" + response.custom_message, type:"warning"});
                      }
                      $("#loading").hide();
                      $(".outstanding_pdf").removeClass('hide');
                      $(".bill_past_settlement_pdf, .advance_outstanding_pdf").addClass('hide');
                },
                error: function(){
                    $("#bill_outstanding_document_modal").modal("hide");
                    $(".outstanding_pdf").removeClass('hide');
                    $(".bill_past_settlement_pdf, .advance_outstanding_pdf").addClass('hide');
                    $("#loading").hide();
                }
            });

    })
});

function paymentTypeInit() {
    $("#bill-switch li").on('click', function(){
        var selected = $(this).data('title');
        if(selected == 1){
            $("#id_print_details").attr("data-original-title", "Print Payment Advice")
        }else{
            $("#id_print_details").attr("data-original-title", "Print Receipt Acknowledgement")
        }
        var toggle = $(this).data('toggle');
        $('#bill_settlement_flag').val(selected);
        paymentTypeChangeFunction();
        $(".bill_type_container .custom-error-message").remove();
    });
}

function paymentTypeChangeFunction(){
    if ($("#bill_settlement_flag").val() =="1") {
        $(".amount_header").text('Pay');
        $(".payment_selector").text('Pay From');
        $("#div_payment_debtors").addClass("hide");
        $("#div_payment_creditors").removeClass("hide");
        $("#payment_type").prop("checked", true);
    }
    else {
        $(".amount_header").text('Receive');
        $(".payment_selector").text('Receive In');
        $("#div_payment_debtors").removeClass("hide");
        $("#div_payment_creditors").addClass("hide");
        $("#payment_type").prop("checked", false);
    }
    clearTables();
    $("#payment_debtors, #payment_creditors, #fund_ledgers").val("0").trigger("chosen:updated");
    $("#id_payment_amt").val("0.00");
    $("#id-narration, #id-instrument_no").val("");  
    $("#id_payment_date").val(moment().format('YYYY-M-D'));
    UpdateSingleDate('id_payment_date');
}

function CreateVoucherOrBill() {
    $("#id_payment_amt").blur(function(){
        if($(this).val() == "") {
            $(this).val('0.00');
        }
        if($(this).val() != 0 || $(this).val() == "") {
            $("#create_payment_voucher, #disabled_payment").removeClass('hide');
            $("#bill_settlement, #disabled_settle").addClass('hide');
        }
        else {
            $("#create_payment_voucher, #disabled_payment").addClass('hide');
            $("#bill_settlement, #disabled_settle").removeClass('hide');
        }
        if($(this).val() > 0) {
            $(".paid_amount").removeAttr('disabled');
        }
        else {
            $(".paid_amount").val("0.00").attr('disabled','disabled');
            $(".current_paid_total").text("0.00");
        }
    });
}

function autoFillPayment() {
    var payment = Number($("#id_payment_amt").val());
    $(".bill_record").each(function(){
        $(this).find(".paid_amount").val("0.00")
        try {
            if (payment > 0) {
                var due = Number($(this).find(".due").text()) - Number($(this).find(".settled_amount").val())
                if (due > 0) {
                    if (due <= payment) {
                        $(this).find(".paid_amount").val(due.toFixed(2));
                        payment -= due;
                    } else {
                        $(this).find(".paid_amount").val(payment.toFixed(2));
                        payment = 0
                    }
                }
            }
            calculatePaymentTotal();
        } catch(e) {
            console.log("Auto filling against payment", e)
        }
    });
}

function clearTables() {
    $("#id-advance_particulars_json").text("")
    $("#id-billable_particulars_json").text("")
    $("#party_bill_details tbody").html("");
    $(".amt_ageing_1, .amt_ageing_2, .amt_ageing_3, .amt_ageing_4, #party_cr_period").text('');
    $("#unsettled_advance, #unsettled_billable, #unsettled_excess, .advance_outstanding_value").text("0.00")
    $(".party_ageing_details, .party_details_header, .bill_outstanding_summary").addClass('hide');
    $(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();
}

function loadLedgerAndBills(ledger_id, is_receivable) {
    clearTables();
    if (parseInt(ledger_id) == 0) {
        return
    }
    $.ajax({
        url: 'erp/accounts/json/load_ledger_bills/',
        type: 'post',
        data: {
            ledger_id: ledger_id,
            is_receivable: is_receivable
        },
        success: function(response) {
            clearTables();
            if (response.response_message == "Success") {
                if(response.ledger.closing_balance == 0) {
                    $("#party_ledger_balance").text(response.ledger.closing_balance.toFixed(2));
                }
                else if(response.ledger.closing_balance < 0) {
                    $("#party_ledger_balance").text((response.ledger.closing_balance * -1).toFixed(2) + " Dr");
                }
                else {
                    $("#party_ledger_balance").text(response.ledger.closing_balance.toFixed(2)+ " Cr");
                }
                var total_advance = -1 * Number(response.ledger.aging.advance);
                var total_excess = -1 * Number(response.ledger.aging.excess);
                $(".amt_ageing_1").text(response.ledger.aging.age1.toFixed(2));
                $(".amt_ageing_2").text(response.ledger.aging.age2.toFixed(2));
                $(".amt_ageing_3").text(response.ledger.aging.age3.toFixed(2));
                $(".amt_ageing_4").text(response.ledger.aging.age4.toFixed(2));
                $(".advance_outstanding_value").text(total_advance.toFixed(2));
                $("#unsettled_excess").text(total_excess.toFixed(2));
                $("#unsettled_advance").text(total_advance.toFixed(2));
                $("#unsettled_billable").text(response.ledger.aging.billable.toFixed(2));
                if (ledger_id in response.unsettled.advance){
                    $("#id-advance_particulars_json").text(JSON.stringify(response.unsettled.advance[ledger_id].particulars));
                }
                if (ledger_id in response.unsettled.billable){
                    $("#id-billable_particulars_json").text(JSON.stringify(response.unsettled.billable[ledger_id].particulars));
                }

                $("#party_name").text(response.ledger.name);
                $("#party_cr_period").text("" + response.ledger.credit_period);
                $(".party_ageing_details, .party_details_header, .bill_outstanding_summary").removeClass('hide');
                var payment = parseFloat($("#id_payment_amt").val());
                if(Object.keys(response.bills).length > 0){
                    $("#id_print_outstanding_bill").removeClass("disabled");
                    $.each(response.bills, function(i, bill) {
                        if (is_receivable) {
                            bill.bill_value = -1 * bill.bill_value;
                            bill.already_adjusted = -1 * bill.already_adjusted;
                            bill.due = -1 * bill.due;
                        }
                        var ledger_bill = new LedgerBill(bill);
                        total_advance = ledger_bill.autoFillSettlement(total_advance);
                        payment = ledger_bill.autoFillPayment(payment);
                        $("#party_bill_details tbody").append(ledger_bill.constructRow(i+1));
                    });

                    $("#party_bill_details tbody").append(new LedgerBill({}, 0).insertTotalBillRow());
                }else{
                    var row = `<tr><td colspan='9' class='text-center' style='font-weight: bold;'>No transaction Found!</td></tr>`;
                    $("#party_bill_details tbody").append(row);
                    $("#id_print_outstanding_bill").addClass("disabled");
                }
                resetPayments();
                calculatePaymentTotal();
                calculateAdvancesClaimed();
                calculateTotalDue();
                validateTextInputs();
                //setContainerSizeFixed("party-bill-details-container", 360);
                enableAdvanceClaims();
                $("#loading").hide();
            } else {
                swal({title:"OOPS!!", text:response.response_message + ".<br/>" + response.custom_message, type:"warning"});
                $("#loading").hide();
            }
        },
        error: function(){
        }
    });
}

function getTags() {
    var tags = [];
    $("#ul-tagit-display li:visible").each(function(){
        tags.push($(this).find("label").text());
    });
    return tags;
}

function dateRangePickerCloseEvent() {
    $('.report-range').on('hide.daterangepicker', function(ev, picker) {
        setTimeout(function(){
            generateVoucherBillDetails("date-change")
        },100);
    });
}

function generateVoucherBillDetails(loadType=""){
    $("#loading").show();
    ledger_id = $("#payment_type").is(":checked") ? $("#payment_creditors").val(): $("#payment_debtors").val();
    ledger_name = $("#payment_type").is(":checked") ? $("#payment_creditors option:selected").text(): $("#payment_debtors option:selected").text();
    doc_title =  $("#payment_type").is(":checked") ? "payment" : "receipt";
    var date_range = moment($("#from_date").val()).format('MMM D, YYYY') + " - " + moment($("#to_date").val()).format('MMM D, YYYY')
    var mail_subject = "Settlement details for the " + doc_title + " transactions made during the period " + date_range
    is_debit = $("#payment_type").is(":checked") == true ? 1:0;
    $.ajax({
        url:"/erp/accounts/json/load_voucher_bill_details/",
        type:'post',
        data:{from_date: $("#from_date").val(), to_date: $("#to_date").val(), ledger_id: ledger_id, is_debit: is_debit},
        success: function(response){
            if(response.response_message == "Success") {
                if($("#bill_past_settlement").hasClass('dataTable')) {
                    oTable.destroy();
                }
                $("#bill_past_settlement tbody").html("");
                $("#bill_past_settlement thead tr.party_header").remove();
                $("#bill_past_settlement_pdf tbody").html("");
                if(Object.keys(response.bill_and_voucher_details).length > 0){
                    $("#id_print_details").removeClass("disabled");
                    $("#id_download_csv_file").removeClass("disabled");
                    var party_details = response.party_details
                    if(party_details != "" && party_details != null && party_details.party_name != ""){
                         $("#id_party_name").val(party_details.party_name);
                         $("#id_party_email").val(party_details.party_email)
                    }else{
                         $("#id_party_name").val(ledger_name);
                         $("#id_party_email").val("")
                    }
                    $("#id_ledger_id").val(ledger_id)
                    $("#id_ledger_name").val(ledger_name)
                    $("#id_mail_subject").val(mail_subject)
                    if(is_debit == 1){
                        section_title = "Payment Advice"
                    }else{
                        section_title = "Receipt Acknowledgement"
                    }
                    var file_name = $("#id_party_name").val() + "-" + section_title + "-" + moment($("#from_date").val()).format('MMM D, YYYY') + "-" + moment($("#to_date").val()).format('MMM D, YYYY')
                    $("#id_csv_download_name").val(file_name)
                    var header_row = `<tr class='party_header'>
                                        <th width="25%" hidden="hidden" colspan='1'>Account Group</th>
                                        <th width="25%" hidden="hidden" colspan='7'>${ledger_name}</th>
                                    </tr>`
                    if(party_details != "" && party_details != null && party_details.party_name != ""){
                        header_row += `<tr class='party_header'>
                                            <th width="25%" hidden="hidden" colspan='1'>Party NAME</th>
                                            <th width="25%" hidden="hidden" colspan='7'>${party_details.party_name}</th>
                                        </tr>`
                    }
                    if(party_details != "" && party_details != null && party_details.party_address != ""){
                        header_row += `<tr class='party_header'>
                                            <th width="25%" hidden="hidden" colspan='1'>Party Address</th>
                                            <th width="25%" hidden="hidden" colspan='7'>${party_details.party_address}</th>
                                        </tr>`
                    }
                    if(party_details != "" && party_details != null && party_details.party_phone_no != ""){
                        header_row += `<tr class='party_header'>
                                            <th width="25%" hidden="hidden" colspan='1'>Party Phone</th>
                                            <th width="25%" hidden="hidden" colspan='7'>${party_details.party_phone_no }</th>
                                        </tr>`
                    }
                    header_row +=`<tr class='party_header'>
                                       <th width="25%" hidden="hidden" colspan='8'>${mail_subject}</th>
                                   </tr>
                                   <tr class='party_header'>
                                       <th hidden="hidden" colspan='8'></th>
                                   </tr>`
                    $(header_row).insertBefore("#bill_past_settlement thead tr");

                    $.each(response.bill_and_voucher_details, function (i, voucher) {
                        var header_pdf = `  <tr>
                                                <td colspan='2' style='font-size: 16px;font-weight:bold; border-right: none;'>Transaction Date: ${voucher[0].txn_date}</td>
                                                <td colspan='2' style='font-size: 16px;font-weight:bold; border-left: none; border-right: none;'>Transaction No.: ${voucher[0].txn_no}</td>
                                                <td colspan='1' style='font-size: 16px;font-weight:bold; border-left: none;'>Transaction Value: ${voucher[0].tnx_value.toFixed(2)}</td>
                                            </tr>`;
                        $("#bill_past_settlement_pdf tbody").append(header_pdf);
                        $.each(voucher, function (i, item) {
                            var row = ` <tr>
                                            <td>${item.bill_no}</td>
                                            <td class='text-center'>${item.bill_date}</td>
                                            <td class='text-right'>${item.bill_value.toFixed(2)}</td>
                                            <td class='text-right'>${item.amount_settled.toFixed(2)}</td>
                                            <td class='text-right'>${item.outstanding.toFixed(2)}</td>
                                            <td class='text-center'>${item.txn_date}</td>
                                            <td>${item.txn_no}</td>
                                            <td class='text-right'>${item.tnx_value.toFixed(2)}</td>
                                        </tr>`;
                            $("#bill_past_settlement tbody").append(row);
                            var row_pdf = ` <tr>
                                            <td>${item.bill_no}</td>
                                            <td class='text-center'>${item.bill_date}</td>
                                            <td class='text-right'>${item.bill_value.toFixed(2)}</td>
                                            <td class='text-right'>${item.amount_settled.toFixed(2)}</td>
                                            <td class='text-right'>${item.outstanding.toFixed(2)}</td>
                                        </tr>`;
                            $("#bill_past_settlement_pdf tbody").append(row_pdf);
                        });
                    });
                    tableHeaderFixed();
                }
                else{
                    var row = `<tr><td colspan='8' class='text-center' style='font-weight: bold;'>No transaction Found!</td></tr>`;
                    $("#bill_past_settlement tbody").append(row);
                    $("#id_print_details").addClass("disabled");
                    $("#id_download_csv_file").addClass("disabled");
                }
                if(loadType == "date-change") {
                    $("#loading").hide();
                }
            }
            else {
                swal({title:"OOPS!!", text:response.response_message + ".<br/>" + response.custom_message, type:"error"});
                $("#loading").hide();
            }
        },
        error: function(){
            $("#loading").hide();
        }
    })
}

function tableHeaderFixed() {
    oTable = $('#bill_past_settlement').DataTable({
    fixedHeader: true,
    "pageLength": 50,
    "search": {
        "smart": false
    },
    "columns": [
        null,
        { "type": "date" },
        null,null,null,
        { "type": "date" },
        null,null
        ]
    });
    oTable.on("draw",function() {
        var keyword = $('#bill_past_settlement_filter > label:eq(0) > input').val();
        $('#bill_past_settlement').unmark();
        $('#bill_past_settlement').mark(keyword,{});
        $( window ).resize();
        setHeightForTable();
    });
    oTable.on('page.dt', function() {
      $('html, body').animate({
        scrollTop: $(".dataTables_wrapper").offset().top - 15
       }, 'slow');
    });
    oSettings = oTable.settings();
    $( window ).resize();
}

function validateCreateVoucherFields() {
    var payment_type = "PAID"
    var payment_ledger_id = $("#payment_creditors").val()
    if ($("#div_payment_creditors").hasClass("hide")) {
        payment_type = "RECEIVED";
        payment_ledger_id = $("#payment_debtors").val();
    }
    var bills_to_settle = {advance:prepareAdvanceSettlements(), paid:[]}

    $(".bill_record").each(function() {
        paid_amount = parseFloat($(this).find(".paid_amount").val());
        if (paid_amount > 0)
            bills_to_settle.paid.push({bill_id: $(this).find("#id-bill_id").val(), amount: paid_amount});
    });
    if (bills_to_settle.paid.length > 0 || bills_to_settle.advance.length > 0) {
        createVoucher(payment_type=payment_type, payment_ledger_id=payment_ledger_id, bills_to_settle=bills_to_settle);
    } else {
        var result = true
        if(bills_to_settle.paid.length == 0 && bills_to_settle.advance.length == 0){
           var ControlCollections = [{
                controltype: 'textbox',
                controlid: 'id_payment_amt',
                isrequired: true,
                mindigit: 0.01,
                mindigiterrormsg: 'Please provide non-zero value for Amount.'
            }];
            var result = JSCustomValidator.JSvalidate(ControlCollections);
        }
        if(result){
            setTimeout(function(){
                swal({
                    title: "Are you sure?",
                    text: "No Bills seem to be adjusted/settled for the Settlement Voucher being created.<br/> Are you sure to proceed with the Voucher Creation?",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#209be1",
                    confirmButtonText: "No, Adjust Bills!",
                    cancelButtonText: "Yes, Create Voucher",
                    closeOnConfirm: true
                },
                function(isConfirm){
                    if (!isConfirm) {
                        createVoucher(payment_type=payment_type, payment_ledger_id=payment_ledger_id, bills_to_settle=bills_to_settle);
                    }
                });
            },500);
        }
    }
}

function createVoucher(payment_type, payment_ledger_id, bills_to_settle) {
    $("#create_payment_voucher, #bill_settlement").val('Processing...').addClass('btn-processing');
    $("#loading").show();
    var data = {
        payment_type: payment_type,
        payment_info: JSON.stringify({
            fund_ledger_id: $("#fund_ledgers").val(),
            payment_ledger_id: payment_ledger_id,
            narration: $("#id-narration").val(),
            instrument_no: $("#id-instrument_no").val(),
            payment_date: $("#id_payment_date").val(),
            amount: parseFloat($("#id_payment_amt").val()),
            tags: getTags()
        }),
        bills_to_settle: JSON.stringify(bills_to_settle)
    }
    $.ajax({
        url: '/erp/accounts/json/make_payment_to_bills/',
        type: 'post',
        data: data,
        success: function(response) {
            setTimeout(function(){
                if(response.response_message == "Success") {
                    $("#loading").hide();
                    // Created voucher with code
                    swal({
                        title: "",
                        text: response.custom_message,
                        closeOnClickOutside: false,
                        type: "success"
                    },
                    function() {
                        $("#id_payment_amt").val("0.00");
                        $("#id-narration, #id-instrument_no").val("");
                        $("#id_payment_date").val(moment().format('YYYY-M-D'));
                        $("#payment_creditors, #payment_debtors, #fund_ledgers").val("0").trigger("chosen:updated");
                        UpdateSingleDate('id_payment_date');
                        location.reload();
                    });
                } else {
                    swal({title:"OOPS!!", text:response.response_message + ".<br/>" + response.custom_message, type:"error"});
                }
            },1000);
        },
        error: function(){
            $("#loading").hide();
        }
    });
    if (bills_to_settle.paid.length > 0)
        ga('send', 'event', 'Voucher', 'Create via Bill Settlements', $('#enterprise_label').val(), bills_to_settle.paid.length);
    if (bills_to_settle.advance.length > 0)
        ga('send', 'event', 'Ledger Bills', 'Create via Bill Settlements', $('#enterprise_label').val(), bills_to_settle.advance.length);
}

function applyValidation() {
    $("#create_payment_voucher, #bill_settlement").click(function() {
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();
        var ControlCollections = [
            {
                controltype: 'textbox',
                controlid: 'id_payment_date',
                isrequired: true,
                errormsg: 'Date is required.'
            }
        ];
        if ($(this).attr('id') == 'create_payment_voucher') {
            ControlCollections[ControlCollections.length] = {
                controltype: 'textbox',
                controlid: 'id_payment_amt',
                isrequired: true,
                mindigit: 0.01,
                mindigiterrormsg: 'Please provide non-zero value for Amount.'
            };
        }
        if (Number($("#id_payment_amt").val()) > 0){
            var control = {
                controltype: 'dropdown',
                controlid: 'fund_ledgers',
                isrequired: true,
                errormsg: $(".payment_selector").text() +' Ledger is required.'
            };
            ControlCollections[ControlCollections.length] = control;
        }
        if ($("#payment_type").is(":checked")) {
            var control = {
                controltype: 'dropdown',
                controlid: 'payment_creditors',
                isrequired: true,
                errormsg: 'Pay To Ledger is required'
            };
            ControlCollections[ControlCollections.length] = control;
        }
        else {
            var control = {
                controltype: 'dropdown',
                controlid: 'payment_debtors',
                isrequired: true,
                errormsg: 'Receive From Ledger is required.'
            };
            ControlCollections[ControlCollections.length] = control;
        }

        var result = JSCustomValidator.JSvalidate(ControlCollections);

        if(Number($("#unsettled_advance").text()) < 0 && Number($("#id_payment_amt").val()) < Number($(".current_paid_total").text())) {
            swal({
                title: "Insufficient Balance & Amount Exceeds",
                text: "You don't have sufficient balance in Advance to fill.<br/>Total amount exceeds the payable amount. <br />Please check!!",
                type: "warning"
            });
            result = false;
         }

        else if(Number($("#unsettled_advance").text()) < 0) {
            swal({
                title: "Insufficient Balance",
                text: "You don't have sufficient balance in Advance to fill.<br />Please check!!",
                type: "warning"
            });
            result = false;
        }

        else if($("#party_bill_details").find('.table-textbox-error-border').length > 0) {
            swal({
                title: "Amount Mismatch",
                text: "Highlighted fields are amount mismatched fields. <br />Total amount exceeds the pending amount.<br />Please check!!",
                type: "warning"
            });
            result = false;
        }

        else if(Number($("#id_payment_amt").val()) < Number($(".current_paid_total").text())) {
            swal({
                title: "Amount Mismatch",
                text: "Total amount exceeds the amount you entered to pay.<br />Please check!!",
                type: "warning"
            });
            result = false;
        }

        else if((Number($("#unsettled_advance").text()) > Number($(".advance_paid_total").text())) && (Number($(".advance_paid_total").text()) < Number($(".due_total").text()))) {
            var curResult = result;
            result = false;
            swal({
                title: "Unused Balance Available",
                text: "You have remaining balance which is paid in advance. <br />Are you willing to use the balance amount?",
                type: "warning",
                confirmButtonText: "No! Create Voucher",
                showCancelButton: true,
                cancelButtonText: "Yes"
            },
            function(){
                if(curResult) {
                    validateCreateVoucherFields();
                }
            });
        }
        if(result) {
             validateCreateVoucherFields();
        }
        return result;
    });
}

function resetPayments() {
    $(".reset_pay_now").click(function(){
        $(".paid_amount").val('0.00');
        $(".current_paid_total").text('0.00');
        $("#party_bill_details").find('.table-textbox-error-border').removeClass('table-textbox-error-border');
    });

    $(".reset_adv_amt").click(function(){
        $(".settled_amount").val('0.00');
        unsettled_advance = Number($(".advance_paid_total").text()) + Number($("#unsettled_advance").text());
        $(".advance_paid_total").text('0.00');
        $("#unsettled_advance").text(unsettled_advance.toFixed(2));
        autoFillPayment();
        $("#party_bill_details").find('.table-textbox-error-border').removeClass('table-textbox-error-border');
    });
}

function calculatePaymentTotalBlur(curId){
    var selectedID = curId.id;
    var dueAmount = Number($("#"+selectedID).closest('tr').find('.due').text());
    var settleAmt = Number($("#"+selectedID).closest('tr').find('.settled_amount').val());
    var paidAmt = Number($("#"+selectedID).closest('tr').find('.paid_amount').val());
    console.log(dueAmount < (settleAmt+paidAmt), dueAmount +"<"+ Number(settleAmt + paidAmt))
    if(dueAmount < Number(settleAmt+paidAmt)) {
        $("#"+selectedID).closest('tr').find('.settled_amount').addClass('table-textbox-error-border');
        $("#"+selectedID).closest('tr').find('.paid_amount').addClass('table-textbox-error-border');
    }
    else {
        $("#"+selectedID).closest('tr').find('.settled_amount').removeClass('table-textbox-error-border');
        $("#"+selectedID).closest('tr').find('.paid_amount').removeClass('table-textbox-error-border');
    }
    //autoFillPayment();
    calculatePaymentTotal();
}

function calculateAdvancesClaimedBlur(curId){
    var selectedID = curId.id;
    var dueAmount = Number($("#"+selectedID).closest('tr').find('.due').text());
    var settleAmt = Number($("#"+selectedID).closest('tr').find('.settled_amount').val());
    var paidAmt = Number($("#"+selectedID).closest('tr').find('.paid_amount').val());
    console.log(dueAmount < (settleAmt+paidAmt), dueAmount +"<"+ Number(settleAmt + paidAmt))
    if(dueAmount < Number(settleAmt+paidAmt)) {
        $("#"+selectedID).closest('tr').find('.settled_amount').addClass('table-textbox-error-border');
        $("#"+selectedID).closest('tr').find('.paid_amount').addClass('table-textbox-error-border');
    }
    else {
        $("#"+selectedID).closest('tr').find('.settled_amount').removeClass('table-textbox-error-border');
        $("#"+selectedID).closest('tr').find('.paid_amount').removeClass('table-textbox-error-border');
    }
    calculatePaymentTotal();
    calculateAdvancesClaimed();
}

function calculatePaymentTotal() {
    var payedSum = 0;
    $(".paid_amount").each(function(){
        payedSum += new Number($(this).val());
    });
    $(".current_paid_total").text(payedSum.toFixed(2));
}

function calculateAdvancesClaimed() {
    var advanceSum = 0;
    var unsettled_advance = (Number($(".advance_paid_total").text()) + Number($("#unsettled_advance").text()));
    $(".settled_amount").each(function(){
        advanceSum += new Number($(this).val());
    });
    $(".advance_paid_total").text(advanceSum.toFixed(2));
    var unsettled_advance_amount = (unsettled_advance - advanceSum).toFixed(2)
    if(unsettled_advance_amount < 0) {
        $("#unsettled_advance").text(unsettled_advance_amount).css({color: "#dd4b39"})
    }
    else {
        $("#unsettled_advance").text(unsettled_advance_amount).css({color: ""})
    }
    //autoFillPayment();
}

function prepareAdvanceSettlements() {
    var advanceSum = 0;
    $(".settled_amount").each(function() {
        advanceSum += new Number($(this).val());
    });
    var advance_particulars = []
    var advance_particulars_json = $("#id-advance_particulars_json").text();
    if (advance_particulars_json !== "") {
        advance_particulars = JSON.parse(advance_particulars_json);
    }
    var advance_settlements = []
    var balanceAdvance = advanceSum
    $(".settled_amount").each(function() {
        var dueAmount = new Number($(this).val());
        if (balanceAdvance > 0 && dueAmount > 0) {
            var bill_id = $(this).parent().parent().find("#id-bill_id").val()
            try {
                // This block can be reused as a separate method in case of settling one bill
                var settled_amount = 0;
                $.each(advance_particulars, function(i, advance_particular) {
                    if (dueAmount > 0 && advance_particular.amount > 0) {
                        if (dueAmount <= advance_particular.amount) {
                            settled_amount += dueAmount;
                            balanceAdvance -= dueAmount;
                            advance_particular.amount -= dueAmount;
                            advance_settlements.push({
                                voucher_id: advance_particular.voucher_id,
                                bill_id: bill_id,
                                amount: dueAmount});
                            dueAmount = 0;
                        } else {
                            settled_amount += advance_particular.amount;
                            dueAmount -= advance_particular.amount;
                            balanceAdvance -= advance_particular.amount;
                            advance_settlements.push({
                                voucher_id: advance_particular.voucher_id,
                                bill_id: bill_id,
                                amount: advance_particular.amount});
                            advance_particular.amount = 0;
                        }
                    }
                });
            } catch(e) {
                console.log("Auto filling against advance", e)
            }
        }
    });
    return advance_settlements;
}

function calculateTotalDue() {
    var total_due = 0;
    var total_bill_value = 0;
    var total_settled = 0;
    $(".due").each(function(){
        total_due += new Number($(this).text());
    });
    $(".bill").each(function(){
        total_bill_value += new Number($(this).text());
    });
    $(".settled").each(function(){
        total_settled += new Number($(this).text());
    });
    $(".due_total").text(total_due.toFixed(2));
    $(".bill_total").text(total_bill_value.toFixed(2));
    $(".settled_total").text(total_settled.toFixed(2));
}

function enableAdvanceClaims() {
    if((Number($(".advance_paid_total").text()) + Number($("#unsettled_advance").text())) > 0) {
        $(".settled_amount").removeAttr('disabled');
    }
}
