import unittest

from erp.purchase.service import PurchaseService


class ApprovePo(unittest.TestCase):

    def test_approve_po_mail(self):
        res = PurchaseService().approve_po(enterprise_id=102,
                                           po_id=2293,
                                           user_id=1,
                                           remarks="mail draft",
                                           project_code=4479)
        self.assertEqual(res, "24-25/PO/000010")

    def test_generate_pdf(self):
        res = PurchaseService().generatePODocument(enterprise_id=102, po_id=2293, document_regenerate="true")
        print(res)
