$(document).ready(function() {
    $("#grn_document_container").html(getPdfLoadingImage());
});

function listenDocumentPopupEvents() {
    $('#approve_grn').click(function () {
		checkValidPOs();
    });

    $('#reject_grn').click(function () {
        if($('#remarks').val() == "") {
            swal({
                title: "<span style='color: #dd4b39;'>Remarks Required.</span>",
                text: "Please enter your purpose of rejecting this GRN as remarks.",
                type: "warning",
                html: true
            },
            function(){
                $('#remarks').focus();
            });
        }
        else {
            verifyAndRejectGrn();
        }
    });
}

function checkValidPOs(){
    var invalid_pos = "";
    var receipt_no = $("#modal_grn_id").val();
    setTimeout(function(){
        $.ajax({
            url: "/erp/stores/json/grn/check_is_valid/",
            type: "POST",
            dataType: "json",
            data: {receipt_no: receipt_no},
            success: function (json) {
                if(json.response_message == "Success") {
                    approveGrn();
                }
                else{
                    invalid_pos = json.invalid_pos;
                    var warning = "<p>Blanket/Open POs - " + invalid_pos + " against which some Items are received in this GRN have expired as of now.</p><br />Do you still want to proceed with the Approval?"
                    confirmAction(message=warning, callback=function(isConfirm){
                        if (isConfirm){
                            approveGrn();
                        }
                    });
                }
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
                $("#loading").hide();
            }
        });
    }, 50);
}

function approveGrn() {
    $("#loading").show();
    var receipt_no = $("#modal_grn_id").val();
    var supplier_id = $("#modal_selected_grn").val();
    var received_against = $("#modal_received_against").val();
    date = new Date();
    setTimeout(function(){
        $.ajax({
            url: "/erp/stores/json/grn/approve/?now=" + date.toString(),
            type: "POST",
            dataType: "json",
            data: {receipt_no: receipt_no, remarks:$("#remarks").val()},
            success: function (json) {
                $("#loading").hide();
                var swal_type = "error";
                var swal_title = "<span style='color: #dd4b39;;'>Unable to Approve the GRN at the moment!</span>";
                if(json.custom_message.indexOf("success") > 0) {
                    ga('send', 'event', 'GRN', "Approve", $('#enterprise_label').val(), 1);
                    swal_type = "success";
                    swal_title = "<span style='color: #44ad6b;'>Approved Successfully!</span>";
                }
                ga('send', 'event', 'GRN', "Approve", $('#enterprise_label').val(), 1);
                swal({
                    title: swal_title,
                    text: json.custom_message,
                    type: swal_type,
                    html: true
                },
                function(){
                    generatePdfFromAjax(receipt_no, 1, supplier_id, received_against);
                    updateTableStatus(1, receipt_no, json.code, supplier_id, received_against, json.invoice_type);
                    getAllMenuPendingCount();
                });
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
                $("#loading").hide();
            }
        });
    }, 50);
}

function rejectGrn(){
    var receipt_no = $("#modal_grn_id").val();
    var supplier_id = $("#modal_selected_grn").val();
    var received_against = $("#modal_received_against").val();
    date = new Date();
    $("#loading").show();
    setTimeout(function(){
        $.ajax({
            url: "/erp/stores/grn/reject/?now=" + date.toString(),
            type: "POST",
            dataType: "json",
            data: {receipt_no: receipt_no, remarks:$("#remarks").val()},
            success: function (json) {
                message = JSON.parse(json)
                $("#loading").hide();
                var swalTitle = "";
                if (message[1] == 1){
                    var swaltype = 'warning';
                    var swalTitle = "";
                }
                else {
                    var swaltype = 'success';
                    if(message[0].indexOf("rejected") > 0) swalTitle = "Successfully Rejected";
                    if(message[0].indexOf("removed") > 0) swalTitle = "Successfully Removed";
                }
                swal({
                    title: swalTitle,
                    text: message[0],
                    type: swaltype,
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    html: true
                },
                function(){
                    getAllMenuPendingCount();
                    if (message[1] == -1){
                        ga('send', 'event', 'GRN', "Reject", $('#enterprise_label').val(), 1);
                        //$('#view_receipt_document').click();
                        generatePdfFromAjax(receipt_no, -1,supplier_id, received_against)
                        updateTableStatus(-1, receipt_no, 0, supplier_id, received_against, 0);
                    } else if(message[1] == 1){
                        return;
                    } else {
                        ga('send', 'event', 'GRN', "Discard", $('#enterprise_label').val(), 1);
                        if (received_against == "Issues") {
                            window.location.href = "/erp/stores/irn_list/";
                        } else if (received_against == "Sales Return") {
                            window.location.href = "/erp/sales/sr_list/";
                        } else {
                            window.location.href = "/erp/stores/grn_list/";
                        }
                    }
                });
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
                $("#loading").hide();
            }
        });
    },50);
}

function verifyAndRejectGrn() {
    var url = "/erp/stores/json/get_dc_receipt_linked_message/";
    if ($("#modal_received_against").val() == "Job In") {
        url= "/erp/stores/json/get_receipt_linked_message/";
    }
    $.ajax({
        url: url,
        method: "POST",
        data:{
            receipt_no: $("#modal_grn_id").val(),
            received_against:trim($("#modal_received_against").val()),
            party_id:$("#modal_selected_grn").val(),
            for_reject: true
        },
        success: function(response) {
            if (response.response_message == "Success") {
                if(response.custom_message == "") {
                   rejectGrn();
                } else {
                    swal({title: "", text:response.custom_message, type: "error", html: true});
                }
             } else {
                swal({title: "", text: response.custom_message, type: "error", html: true});
             }
        },
        error: function (xhr, errmsg, err) {
            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
        }
    });
}

function generatePdfFromAjax(grn_id, grn_status, supplier_id=0, received_against){
    $("#grn_doc_btn a.btn, #remarks").addClass("hide");
    $(".grn_icd_status").text("").css({color: "#28a745"});
    $(".remarks_count_link").css({float: ""});
    $("#grn_document_modal").modal("show");
    $.ajax({
        url: "/erp/auditing/json/downloadDoc/",
        type: "post",
        datatype: "json",
        data: {receipt_no: grn_id, grn_code: grn_id, response_data_type:"data", doc_type:"receipt"},
        success: function (response) {
            if(response.remarks != undefined) {
                if(response.remarks.length > 0){
                    $(".remarks_count_link").removeClass('disabled').find(".remarks_counter").text(response.remarks.length);
                }
                else {
                    $(".remarks_count_link").addClass('disabled').find(".remarks_counter").text("No ");
                }
                $("#grn_document_remarks").val(JSON.stringify(response.remarks));
            }
            var row = '<object id="grn_document" class="document_viewer" data="'+response.url+'">'+getPdfErrorMessage()+'</object>';
            $("#grn_document_container").html(row);
            $("#modal_grn_id").val(grn_id);
            $("#modal_selected_grn").val(supplier_id);
            $("#modal_received_against").val(received_against);
            $("#modal_selected_grn").val(supplier_id);
            $("#display_popup").removeClass('hide');
            if(grn_status == -1) {
                if($("#reject_grn").hasClass("for_grn_approve")) {
                    $("#approve_grn, #remarks").removeClass("hide");
                }
            }
            else if(grn_status == 0) {
                if($("#reject_grn").hasClass("for_grn_approve")) {
                    $("#approve_grn, #remarks, #reject_grn").removeClass("hide");
                }
                else {
                    $("#remarks, #reject_grn").removeClass("hide");
                }
                $("#reject_grn").text("Discard");
            }
            else if(grn_status == 1 || grn_status == 5) {
                if($("#reject_grn").hasClass("for_grn_approve")) {
                    $("#remarks, #reject_grn").removeClass("hide");
                    $("#reject_grn").text("Reject");
                }
                if(grn_status == 5 ) {
                    $(".grn_icd_status").text("ICD Returned").css({color: "#dc3545"});
                }
            }
            else if(grn_status == 2 ) {
                $(".grn_icd_status").text("ICD Checked");
            }
            else if(grn_status == 3 ) {
                $(".grn_icd_status").text("ICD Verified");
            }
            else if(grn_status == 4 ) {
                if($("#reject_grn").hasClass("for_grn_approve")) {
                    $("#remarks, #reject_grn").removeClass("hide");
                    $("#reject_grn").text("Reject");
                }
                $(".grn_icd_status").text("Returned");
            }
            else if(grn_status == 6 ) {
                $(".grn_icd_status").text("Accounted");
            }

            if($("#remarks").hasClass("hide") || $("#remarks").length <= 0) {
                $(".remarks_count_link").css({float: "left"});
            }
            else {
                $(".remarks_count_link").css({float: "right"});
            }
            $("#remarks").val("");
        },
        error: function() {
            swal("","Unable to generate document. Please try again", "error");
            $("#grn_document_modal").modal("hide");
        }
    });
    $("#grn_document_modal footer").remove();
    $('#grn_document_modal').on('hidden.bs.modal', function () {
        $(".remarks_count_link").css({float: "right"});
        $("#grn_document").remove();
        $("#grn_document_container").html(getPdfLoadingImage());
    });
}

function updateTableStatus(status, grn_id, grn_no, supplier_id, received_against, inv_type){
    var editedRow = $("#grnList").find("tr[data-grnid='"+grn_id+"']");
    if(status == 1) {
        editedRow.find("td.td_status").find("a").removeClass('draft rejected').addClass('approved').text("Approved");
        editedRow.find(".edit_link_code").text(grn_no).unwrap();
        $(".header_current_page, title").text(grn_no);
        if (inv_type == 1) {
            $("#dc_against_invoice_div").removeClass('hide');
        }
        else {
            $("#dc_against_invoice_div").addClass('hide');
        }

    }
    if(status == -1) {
        editedRow.find("td.td_status").find("a").removeClass('approved').addClass('rejected').text("Rejected");
        editedRow.find(".edit_link_code").wrap("<s></s>");
    }
    editedRow.find(".inline-icon-document").attr("onclick", `generatePdfFromAjax(${grn_id}, ${status}, ${supplier_id}, '${received_against}')`);
    $("#view_receipt_document").attr("onclick", `generatePdfFromAjax(${grn_id}, ${status}, ${supplier_id}, '${received_against}')`);
}