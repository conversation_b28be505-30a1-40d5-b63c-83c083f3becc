import pymysql
import itertools
from decimal import Decimal
from erp.accounts import logger
from settings import HOST, USER, PASSWORD, DBNAME, PORT
from util.reports_query_builder import GSTR1Report, GSTR2Report
from erp.properties import GSTIN_STATE_CODE, UNIT_NAME_MAPPING, GSTIN_STATE_TITLE


class GSTR1:
	"""
	This class is used to prepare the GSTR1 report modules
	"""
	def __init__(self):
		self.con_var = pymysql.converters.conversions.copy()
		self.con_var[pymysql.constants.FIELD_TYPE.DECIMAL] = str
		self.con_var[pymysql.constants.FIELD_TYPE.NEWDECIMAL] = str
		self.con_var[pymysql.constants.FIELD_TYPE.DATETIME] = str
		self.conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, conv=self.con_var, charset="utf8mb4")
		self.cur = self.conn.cursor(pymysql.cursors.DictCursor)

	def get_query(self, from_date=None, to_date=None, enterprise_id=None, key=None):
		"""
		just returning the query result set
		:param from_date:
		:param to_date:
		:param enterprise_id:
		:param key:
		:return:
		"""
		if key == 'b2b':
			return self.execute(query=GSTR1Report().b2b(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id))
		elif key == 'b2cl':
			return self.execute(query=GSTR1Report().b2cl(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id))
		elif key == 'b2cs':
			return self.execute(query=GSTR1Report().b2cs(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id))
		elif key == 'hsn':
			return self.execute(query=GSTR1Report().hsn(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id))
		elif key == 'exp':
			return self.execute(query=GSTR1Report().exp(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id))

	def get_invoice_total(self, from_date=None, to_date=None, enterprise_id=None, key=None):
		"""
		get the total of invoice
		:param from_date:
		:param to_date:
		:param enterprise_id:
		:param key:
		:return:
		"""
		if key == 'b2b':
			return self.execute(query=GSTR1Report().b2b_invoice_total(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id))
		elif key == 'b2cl':
			return self.execute(query=GSTR1Report().b2cl(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id))
		elif key == 'b2cs':
			return self.execute(query=GSTR1Report().b2cs(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id))
		elif key == 'hsn':
			return self.execute(query=GSTR1Report().hsn(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id))
		elif key == 'exp':
			return self.execute(query=GSTR1Report().exp(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id))

	def execute(self, query=None):
		"""
		This function just execute the query given
		:param query:
		:return:
		"""
		response = {}
		try:
			response = self.cur.execute(query)
			response = self.cur.fetchall()
		except Exception as e:
			logger.exception("Query execution failed: %s" % e)
		return response

	def get_key(self, val=None):
		"""

		:return:
		"""
		for key, value in GSTIN_STATE_CODE.items():
			if (val.replace(" ", "")).lower() == (value.replace(" ", "")).lower():
				return key
		return ""

	def generate_b2b_series_report(self, from_date=None, to_date=None, enterprise_id=None, report_type=None):
		"""

		:param from_date:
		:param to_date:
		:param enterprise_id:
		:param report_type:
		:return:
		"""
		response, except_list = [], []
		taxable_total, is_cess_avail, is_cess_compound, item_tax_total, cess_val = {}, {}, {}, {}, {}
		try:
			result_set = self.get_query(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id, key=report_type)
			invoice_total_result = self.get_invoice_total(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id, key=report_type)
			# CESS calc should be apply for all possibilities only if CESS value appeared
			for key, items in itertools.groupby(result_set, lambda x: x['id']):
				cess_total = 0
				taxable_total[key] = 0.0
				is_cess_avail[key] = False
				is_cess_compound[key] = False
				item_tax_total[key] = 0.0
				for item in items:
					taxable_total[key] += float(item['item_total'])
					if item['cess_value'] > 0:
						is_cess_avail[key] = True
						cess_total += Decimal(item['cess_value'])
						except_list.append(item['rowNumber'])
						is_cess_compound[key] = True if item['is_compound'] > 0 else is_cess_compound[key]
					item_tax_total[key] += float(item['item_tax_total']) if item['item_tax_total'] is not None else 0
				cess_val[key] = cess_total

			for item in result_set:
				if item['rowNumber'] not in except_list:
					if item['place_of_supply'] != '':
						if len(item['place_of_supply']) > 2 and (item['place_of_supply'][:2]).isnumeric():
							item['place_of_supply'] = "%s-%s" % (item['place_of_supply'][:2], GSTIN_STATE_CODE[item['place_of_supply'][:2]])
						elif len(item['place_of_supply']) > 2 and self.get_key(item['place_of_supply']):
							item['place_of_supply'] = "%s-%s" % (self.get_key(item['place_of_supply']), item['place_of_supply'])
						elif len(item['place_of_supply'].replace(" ", "")) == 2 and item['place_of_supply'].upper() in GSTIN_STATE_TITLE:
							item['place_of_supply'] = GSTIN_STATE_TITLE[item['place_of_supply'].upper()]
							item['place_of_supply'] = "%s-%s" % (self.get_key(item['place_of_supply']), item['place_of_supply'])
					item['item_tax_total'] = item['item_tax_total'] if item['item_tax_total'] is not None else 0
					item_data = {
						'id': item['id'], 'item_id': item['item_id'], 'gstno': item['gstno'],
						'party_name': item['party_name'], 'invoice_code': item['invoice_code'], 'invoice_date': item['invoice_date'],
						'invoice_value': item['invoice_value'], 'place_of_supply': item['place_of_supply'],
						'reverse_charge': item['reverse_charge'], 'type': item['invoice_type'] if item['invoice_type'] is not None and item['invoice_type'] != '' else '',
						'ecommerce_gstin': item['ecommerce_gstin'] if item['ecommerce_gstin'] is not None and item['ecommerce_gstin'] != '' else '',
						'gstin': item['place_of_supply'] if item['place_of_supply'] != '' else '',
						'taxable_value': item['taxable_value'], 'cess_value': item['cess_value'], 'rate': item['rate'],
						'enterprise_id': item['enterprise_id'], 'status': item['status'], 'port_code': item['port_code'] if item['port_code'] is not None else '',
						'issued_on': item['issued_on'] ,
						'cgst_value': item['cgst_value'] if 'cgst_value' in item else 0,'sgst_value': item['sgst_value'] if 'sgst_value' in item else 0,
						'igst_value': item['igst_value'] if 'igst_value' in item else 0,
					}
					if is_cess_avail[item['id']] is True:
						if is_cess_compound[item['id']] is True:
							t_total = float(item['item_total']) + float(item['item_tax_total']) + ((float(taxable_total[item['id']]) + float(item_tax_total[item['id']])) * float(item['common_tax_rate'])/100) if item['grouping_count'] > 0 else float(item['item_total']) + float(item['item_tax_total'])
							item_data['cess_value'] = round((t_total * float(cess_val[item['id']]) / 100), 2)
						else:
							item_data['cess_value'] = round((float(item['item_total']) * float(cess_val[item['id']]) / 100), 2)
					response.append(item_data)
			invoice_total = 0
			for item in invoice_total_result:
				if 'invoice_total' in item:
					invoice_total = float(item['invoice_total']) + invoice_total
			self.conn.close()

		except Exception as e:
			logger.exception("%s" % e)
		return response

	def generate_hsn_report(self, from_date=None, to_date=None, enterprise_id=None, report_type=None):
		"""

		:param from_date:
		:param to_date:
		:param enterprise_id:
		:param report_type:
		:return:
		"""
		response = []
		try:
			result_set = self.get_query(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id, key=report_type)

			response = []
			for item in result_set:
				if float(item['cgst_value']) > 0 or float(item['sgst_value']) > 0 or float(item['igst_value']) > 0 or float(item['cess_value']) > 0:
					item_data = {
						'id': item['id'], 'item_id': item['item_id'], 'hsn_code': item['hsn_code'], 'description': item['description'], 'unit_name': UNIT_NAME_MAPPING[(item['unit_name']).upper()] if (item['unit_name']).upper() in UNIT_NAME_MAPPING else 'OTH-OTHERS',
						'qty': item['qty'], 'total_value': item['total_value'], 'taxable_value': item['taxable_value'], 'cgst_value': item['cgst_value'], 'sgst_value': item['sgst_value'],
						'igst_value': item['igst_value'], 'cess_value': item['cess_value'], 'enterprise_id': item['enterprise_id']
					}
					if item['is_cess_compound'] > 0 and float(item['cess_value']) > 0:
						item_data['cess_value'] = str((float(item['taxable_value']) + (float(item['cgst_value']) + float(item['sgst_value']) + float(item['igst_value']))) * float(item['cess_value']) / 100)
					elif float(item['cess_value']) > 0:
						item_data['cess_value'] = str(float(item['taxable_value']) * float(item['cess_value']) / 100)
					response.append(item_data)
			self.conn.close()

		except Exception as e:
			logger.exception("%s" % e)
		return response


class GSTR2:
	"""
	This class is used to prepare the GSTR2 report modules
	"""
	def __init__(self):
		self.con_var = pymysql.converters.conversions.copy()
		self.con_var[pymysql.constants.FIELD_TYPE.DECIMAL] = str
		self.con_var[pymysql.constants.FIELD_TYPE.NEWDECIMAL] = str
		self.con_var[pymysql.constants.FIELD_TYPE.DATETIME] = str
		self.conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, conv=self.con_var, charset="utf8mb4")
		self.cur = self.conn.cursor(pymysql.cursors.DictCursor)

	def get_query(self, from_date=None, to_date=None, enterprise_id=None, key=None):
		"""
		just returning the query result set
		:param from_date:
		:param to_date:
		:param enterprise_id:
		:param key:
		:return:
		"""
		if key == 'b2b':
			return self.execute(query=GSTR2Report().b2b(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id))
		elif key == 'b2b_subquery':
			return self.execute(query=GSTR2Report().b2b_subquery(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id))
		elif key == 'b2burc':
			return self.execute(query=GSTR2Report().b2burc(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id))
		elif key == 'b2burc_subquery':
			return self.execute(query=GSTR2Report().b2burc_subquery(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id))
		elif key == 'hsn':
			return self.execute(query=GSTR2Report().hsn(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id))
		elif key == 'impg':
			return self.execute(query=GSTR2Report().impg(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id))
		elif key == 'imps':
			return self.execute(query=GSTR2Report().imps(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id))

	def execute(self, query=None):
		"""
		This function just execute the query given
		:param query:
		:return:
		"""
		response = {}
		try:
			response = self.cur.execute(query)
			response = self.cur.fetchall()
		except Exception as e:
			logger.exception("Query execution failed: %s" % e)
		return response

	def generate_hsn_report(self, from_date=None, to_date=None, enterprise_id=None, report_type=None):
		"""

		:param from_date:
		:param to_date:
		:param enterprise_id:
		:param report_type:
		:return:
		"""
		response = []
		try:
			result_set = self.get_query(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id, key=report_type)

			response = []
			for item in result_set:
				if float(item['cgst_value']) > 0 or float(item['sgst_value']) > 0 or float(item['igst_value']) > 0 or float(item['cess_value']) > 0:
					item_data = {
						'id': item['grn_no'], 'item_id': item['item_id'], 'hsn_code': item['hsn_code'], 'description': item['description'], 'unit_name': UNIT_NAME_MAPPING[(item['unit_name']).upper()] if (item['unit_name']).upper() in UNIT_NAME_MAPPING else 'OTH-OTHERS',
						'qty': item['qty'], 'total_value': item['total_value'], 'taxable_value': item['taxable_value'], 'cgst_value': item['cgst_value'], 'sgst_value': item['sgst_value'],
						'igst_value': item['igst_value'], 'cess_value': item['cess_value'], 'enterprise_id': item['enterprise_id']
					}
					if item['is_cess_compound'] > 0 and float(item['cess_value']) > 0:
						item_data['cess_value'] = str((float(item['taxable_value']) + (float(item['cgst_value']) + float(item['sgst_value']) + float(item['igst_value']))) * float(item['cess_value']) / 100)
					elif float(item['cess_value']) > 0:
						item_data['cess_value'] = str(float(item['taxable_value']) * float(item['cess_value']) / 100)
					response.append(item_data)
			self.conn.close()

		except Exception as e:
			logger.exception("%s" % e)
		return response

	def generate_impg_report(self, from_date=None, to_date=None, enterprise_id=None, report_type=None):
		"""

		:param from_date:
		:param to_date:
		:param enterprise_id:
		:param report_type:
		:return:
		"""
		response, except_list = [], []
		taxable_total, is_cess_avail, is_cess_compound, item_tax_total, cess_val = {}, {}, {}, {}, {}
		try:
			result_set = self.get_query(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id, key=report_type)
			# CESS calc should be apply for all possibilities only if CESS value appeared
			for key, items in itertools.groupby(result_set, lambda x: x['grn_no']):
				cess_total = 0
				taxable_total[key] = 0.0
				is_cess_avail[key] = False
				is_cess_compound[key] = False
				item_tax_total[key] = 0.0
				for item in items:
					taxable_total[key] += float(item['item_total'])
					if item['cess_value'] > 0:
						is_cess_avail[key] = True
						cess_total += Decimal(item['cess_value'])
						except_list.append(item['rowNumber'])
						is_cess_compound[key] = True if item['is_compound'] > 0 else is_cess_compound[key]
					item_tax_total[key] += float(item['item_tax_total']) if item['item_tax_total'] is not None else 0
				cess_val[key] = cess_total

			for item in result_set:
				if item['rowNumber'] not in except_list:
					item_data = {
						'grn_no': item['grn_no'], 'item_id': item['item_id'],
						'party_name': item['party_name'],  'invoice_date': item['grn_invoice_date'],
						'grn_total': item['grn_total'], 'doc_type': item['gst_type'], 'igst_value': item['igst_value'],
						'gstin': item['party_gstin'] if item['party_gstin'] != '' else '',
						'taxable_value': item['taxable_value'], 'cess_value': item['cess_value'], 'rate': item['rate'],
						'enterprise_id': item['enterprise_id'], 'status': item['status'], 'port_code': item['port_code'],
						'inv_no': item['grn_inv_no']
					}
					if is_cess_avail[item['grn_no']] is True:
						if is_cess_compound[item['grn_no']] is True:
							t_total = float(item['item_total']) + float(item['item_tax_total']) + ((float(taxable_total[item['grn_no']]) + float(item_tax_total[item['grn_no']])) * float(item['common_tax_rate'])/100) if item['grouping_count'] > 0 else float(item['item_total']) + float(item['item_tax_total'])
							item_data['cess_value'] = round((t_total * float(cess_val[item['grn_no']]) / 100), 2)
						else:
							item_data['cess_value'] = round((float(item['item_total']) * float(cess_val[item['grn_no']]) / 100), 2)
					response.append(item_data)
			self.conn.close()

		except Exception as e:
			logger.exception("%s" % e)
		return response

	def generate_imps_report(self, from_date=None, to_date=None, enterprise_id=None, report_type=None):
		"""

		:param from_date:
		:param to_date:
		:param enterprise_id:
		:param report_type:
		:return:
		"""
		response, except_list = [], []
		taxable_total, is_cess_avail, is_cess_compound, item_tax_total, cess_val = {}, {}, {}, {}, {}
		try:
			result_set = self.get_query(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id, key=report_type)
			# CESS calc should be apply for all possibilities only if CESS value appeared
			for key, items in itertools.groupby(result_set, lambda x: x['grn_no']):
				cess_total = 0
				taxable_total[key] = 0.0
				is_cess_avail[key] = False
				is_cess_compound[key] = False
				item_tax_total[key] = 0.0
				for item in items:
					taxable_total[key] += float(item['item_total'])
					if item['place_of_supply'] != '':
						if len(item['place_of_supply']) > 2 and self.get_key(item['place_of_supply']):
							item['place_of_supply'] = "%s-%s" % (self.get_key(item['place_of_supply']), item['place_of_supply'])
						elif len(item['place_of_supply'].replace(" ", "")) == 2 and item['place_of_supply'].upper() in GSTIN_STATE_TITLE:
							item['place_of_supply'] = GSTIN_STATE_TITLE[item['place_of_supply'].upper()]
							item['place_of_supply'] = "%s-%s" % (self.get_key(item['place_of_supply']), item['place_of_supply'])
						elif len(item['place_of_supply'].replace(" ", "")) > 2 and (item['place_of_supply']).strip()[:2] in GSTIN_STATE_CODE:
							item['place_of_supply'] = "%s-%s" % ((item['place_of_supply']).strip()[:2], GSTIN_STATE_CODE[str((item['place_of_supply']).strip()[:2])])
					if item['cess_value'] > 0:
						is_cess_avail[key] = True
						cess_total += Decimal(item['cess_value'])
						except_list.append(item['rowNumber'])
						is_cess_compound[key] = True if item['is_compound'] > 0 else is_cess_compound[key]
					item_tax_total[key] += float(item['item_tax_total']) if item['item_tax_total'] is not None else 0
				cess_val[key] = cess_total

			for item in result_set:
				if item['rowNumber'] not in except_list:
					item_data = {
						'grn_no': item['grn_no'], 'item_id': item['item_id'],
						'party_name': item['party_name'],  'invoice_date': item['grn_invoice_date'],
						'grn_total': item['grn_total'], 'doc_type': item['gst_type'], 'igst_value': item['igst_value'],
						'gstin': item['party_gstin'] if item['party_gstin'] != '' else '',
						'taxable_value': item['taxable_value'], 'cess_value': item['cess_value'], 'rate': item['rate'],
						'enterprise_id': item['enterprise_id'], 'status': item['status'], 'port_code': item['port_code'],
						'inv_no': item['grn_inv_no'], 'place_of_supply': item['place_of_supply']
					}
					if is_cess_avail[item['grn_no']] is True:
						if is_cess_compound[item['grn_no']] is True:
							t_total = float(item['item_total']) + float(item['item_tax_total']) + ((float(taxable_total[item['grn_no']]) + float(item_tax_total[item['grn_no']])) * float(item['common_tax_rate'])/100) if item['grouping_count'] > 0 else float(item['item_total']) + float(item['item_tax_total'])
							item_data['cess_value'] = round((t_total * float(cess_val[item['grn_no']]) / 100), 2)
						else:
							item_data['cess_value'] = round((float(item['item_total']) * float(cess_val[item['grn_no']]) / 100), 2)
					response.append(item_data)
			self.conn.close()

		except Exception as e:
			logger.exception("%s" % e)
		return response

	def get_key(self, val=None):
		"""

		:return:
		"""
		for key, value in GSTIN_STATE_CODE.items():
			if (val.replace(" ", "")).lower() == (value.replace(" ", "")).lower():
				return key
		return ""

	def generate_b2b_report(self, from_date=None, to_date=None, enterprise_id=None, report_type=None):
		"""

		:param from_date:
		:param to_date:
		:param enterprise_id:
		:param report_type:
		:return:
		"""
		try:

			response = self.get_query(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id, key=report_type)
			result = self.get_query(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id, key='b2burc_subquery' if report_type == 'b2burc' else 'b2b_subquery')
			dict_1 = dict()
			res = []
			if len(result) != 0:
				for item in result:
					value = [item['compound'], item['not_compound'], item['Tax']]
					dict_1.setdefault(str(item['grn_no']), value)
				for i in response:
					if i['place_of_supply'] != '':
						if len(i['place_of_supply']) > 2 and self.get_key(i['place_of_supply']):
							i['place_of_supply'] = "%s-%s" % (self.get_key(i['place_of_supply']), i['place_of_supply'])
						elif len(i['place_of_supply'].replace(" ", "")) == 2 and i['place_of_supply'].upper() in GSTIN_STATE_TITLE:
							i['place_of_supply'] = GSTIN_STATE_TITLE[i['place_of_supply'].upper()]
							i['place_of_supply'] = "%s-%s" % (self.get_key(i['place_of_supply']), i['place_of_supply'])
						elif len(i['place_of_supply'].replace(" ", "")) > 2 and (i['place_of_supply']).strip()[:2] in GSTIN_STATE_CODE:
							i['place_of_supply'] = "%s-%s" % ((i['place_of_supply']).strip()[:2], GSTIN_STATE_CODE[str((i['place_of_supply']).strip()[:2])])
					if i['cess'] == '0.00':
						x = str(i['grn_no'])
						if x in dict_1:
							sum = (float(i['taxable_value'])+float(i['cgst_value'])+float(i['sgst_value'])+float(i['igst_value']))
							c_cess = (float(i['taxable_value']) * float(dict_1[x][2])/100 + sum) * (float(dict_1[x][0]))/100
							cess = float(i['taxable_value'])*float(dict_1[x][1])/100
							i['cess'] = round(c_cess+cess, 2)
					if float(i['total_rate']) > 0:
						res.append(i)
			else:
				for i in response:
					if i['place_of_supply'] != '':
						if len(i['place_of_supply']) > 2 and self.get_key(i['place_of_supply']):
							i['place_of_supply'] = "%s-%s" % (self.get_key(i['place_of_supply']), i['place_of_supply'])
						elif len(i['place_of_supply'].replace(" ", "")) == 2 and i['place_of_supply'].upper() in GSTIN_STATE_TITLE:
							i['place_of_supply'] = GSTIN_STATE_TITLE[i['place_of_supply'].upper()]
							i['place_of_supply'] = "%s-%s" % (self.get_key(i['place_of_supply']), i['place_of_supply'])
						elif len(i['place_of_supply'].replace(" ", "")) > 2 and (i['place_of_supply']).strip()[:2] in GSTIN_STATE_CODE:
							i['place_of_supply'] = "%s-%s" % ((i['place_of_supply']).strip()[:2], GSTIN_STATE_CODE[str((i['place_of_supply']).strip()[:2])])
					if float(i['total_rate']) > 0:
						res.append(i)
		except Exception as e:
			logger.exception('%s' % e)
			response = ""
		return res


