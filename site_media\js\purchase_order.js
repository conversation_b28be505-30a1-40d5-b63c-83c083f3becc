$(function () {
    $('#savePOButton').click(function () {
        var initialFormCount = parseInt(document.getElementById('id_form-INITIAL_FORMS').value);
        var newFormCount = parseInt(document.getElementById('newFormCount').value);
        var totalFormsToSave = parseInt(initialFormCount + newFormCount);

        for (i = initialFormCount; i < totalFormsToSave; i++) {
            var material = document.getElementById('id_form-' + i + '-drawing_no').value;
            var quantity = document.getElementById('id_form-' + i + '-purchase_qty').value;
            if (material != "" || quantity != "") {
                document.getElementById('id_form-' + i + '-order').value = document.getElementById('id_po_id').value;
            }
        }

        var new_form_material = document.getElementById('id_form-__prefix__-drawing_no').value;
        if (new_form_material != 'None') {
            $('#add_new_form').click(); // Adding a new form in case the empty_form is filled
        }
        $('#savePO').click();
    })

    $('#add_new_form').click(function () {
        var form_idx = $('#id_form-TOTAL_FORMS').val();
        alert(form_idx);
        new_form = $('#form-__prefix__').html().replace(/__prefix__/g, form_idx);
        new_form_html = "<tr bgcolor=\"#ECECEC\" id=\"form-" + form_idx +
            "\" align=\"center\">" + new_form + "</tr>";
        $(new_form_html).insertBefore('#form-__prefix__');
        $('#id_form-TOTAL_FORMS').val(parseInt(form_idx) + 1);
        copyFromEmptyForm(form_idx);
    });

    $(document).ready(function () {
        var url = window.location.href;
        url = url.substring(7)
        window.urlpath = url.substring(0, url.indexOf('/'));

        $("#search").keyup(function () {
            // Common Search functionality across Materials and Catalogue pages
            if ($(this).val() != "") {
                $("#searchResult tbody>tr").hide();
                $("#searchResult td:contains-ci('" + $(this).val() + "')").parent("tr").show();
            }
            else {
                $("#searchResult tbody>tr").show();
            }
        });

        $('#form_set').ready(function () {
            var initialFormCount = parseInt(document.getElementById('id_material-INITIAL_FORMS').value);
            for (i = 0; i < initialFormCount; i++) {
                var matLabel = document.getElementById('id_material-' + i + '-materialLabel');
                matLabel.innerHTML = $('#id_material-' + i + '-drawing_no option:selected').text();
            }
        });

        //used to format the date displayed
        $("#id_quotation_date").datepicker({
            dateFormat: "yy-mm-dd",
			autoclose: true,
			orientation: 'bottom auto'
        });
        $('#id_quotation_date').datepicker('setDate', '+0');
        //used to format the date displayed
        $("#id_order_date").datepicker({
            dateFormat: "yy-mm-dd",
			autoclose: true,
			orientation: 'bottom auto'
        });
        $('#id_order_date').datepicker('setDate', '+0');
        if ($('#form_errors').val() != '' || ($('#formset_errors').val().search(/[a-z]/ig) >= 0)) {
            document.getElementById('error_messages').style.display = "block";
        }
        $('#error_close').click(function () {
            document.getElementById('error_messages').style.display = "none";
        });
    });
});

function calculateValue() {
    /*
     For any change in the purchase price or quantity the total value is to be recalculated.
     */
    var total = document.getElementById('id_total');
    correct_total = 0;
    var form_count = parseInt(document.getElementById('id_form-TOTAL_FORMS').value);
    for (i = 0; i < form_count; i++) {
        correct_total += calculatePrice(i);
    }
    correct_total += calculatePrice('__prefix__');

    total.value = correct_total;
}

function calculatePrice(form_idx) {
    var purchase_qty = document.getElementById('id_form-' + form_idx + '-purchase_qty');
    var po_price = document.getElementById('id_form-' + form_idx + '-price');
    var value_disabled = document.getElementById('id_form-' + form_idx + '-total_price_disabled');
    var value = document.getElementById('id_form-' + form_idx + '-total_price');
    var is_deleted = document.getElementById('id_form-' + form_idx + '-DELETE').checked;
    price = 0;

    if (purchase_qty.value != '' && po_price.value != '' && !is_deleted) {
        value.value = parseFloat(purchase_qty.value) * parseFloat(po_price.value)
        value_disabled.value = parseFloat(purchase_qty.value) * parseFloat(po_price.value)
        price = parseFloat(purchase_qty.value) * parseFloat(po_price.value)
    }
    return price;
}

function copyFromEmptyForm(form_idx) {
    var added_form_material = document.getElementById('id_form-' + form_idx + '-drawing_no');
    var added_form_quantity = document.getElementById('id_form-' + form_idx + '-purchase_qty');
    var added_form_po_price = document.getElementById('id_form-' + form_idx + '-price');
    var added_form_total_value = document.getElementById('id_form-' + form_idx + '-total_price');
    var added_form_total_value_disabled = document.getElementById('id_form-' + form_idx + '-total_price_disabled');
    var added_form_delete_flag = document.getElementById('id_form-' + form_idx + '-DELETE');

    // Copy data from empty_form to the new form
    added_form_material.value = document.getElementById('id_form-__prefix__-drawing_no').value;
    added_form_quantity.value = document.getElementById('id_form-__prefix__-purchase_qty').value;
    added_form_po_price.value = document.getElementById('id_form-__prefix__-price').value;
    added_form_total_value.value = document.getElementById('id_form-__prefix__-total_price').value;
    added_form_total_value_disabled.value = document.getElementById('id_form-__prefix__-total_price_disabled').value;
    added_form_delete_flag.checked = document.getElementById('id_form-__prefix__-DELETE').checked;

    document.getElementById('id_form-__prefix__-drawing_no').value = 'None';
    document.getElementById('id_form-__prefix__-purchase_qty').value = 0;
    document.getElementById('id_form-__prefix__-price').value = 0;
    document.getElementById('id_form-__prefix__-total_price').value = 0;
    document.getElementById('id_form-__prefix__-total_price_disabled').value = 0;

    calculateValue();
}

function loadMaterialsFromIndent(data) {
    alert(data.po_material_forms);
}