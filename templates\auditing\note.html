{% extends "auditing/sidebar.html" %}
{% block note %}
    <style>
        li.icd_side_menu a{
		    outline: none;
		    background-color: #e6983c !important;
		}

        #cattable {
			width: 550px;
			position: absolute;
			color: #000000;
			background-color: #FFFFFF;
			/* To align popup window at the center of screen*/
			top: 50%;
			left: 50%;
			margin-top: 100px;
			margin-left: 100px;
		}

		.custom-table.custom-table-large td.checkbox-border {
			padding: 14px !important;
		    padding-left: 35px !important;
		}

		.invoice-table-height {
			max-height: 500px;
			overflow: auto;
		}

		#table_audit_note td span {
			display: inline-block;
			width: 90px;
		}

		.tr-alert-danger , .tr-alert-danger input, .tr-alert-danger select {
			color: #a94442 !important;
			background-color: #f2dede !important;
			border-color: #bbb !important;
		}

		.tr-alert-danger td {
			border: solid 1px #aaa !important;
		}

		.checkbox input[type="checkbox"]:checked + label::after {
			background: #32CD32;
			color: #FFFFFF;
		}

		#chkCrDr .slider {
			top: 11px;
			bottom: -10px;
		}

		.custom-error-message {
			position: relative;
		}

		.bootstrap-filestyle {
			display: none !important;
		}

		.textbox-text-right input[type="text"]{
			text-align: right;
		}

		.td-quantity-uom input {
			border-radius: 4px 0 0 4px;
		}

		.td-quantity-uom select {
			border-radius: 0 4px 4px 0;
			margin-left: -1px;
		}

		#id_note-round_off {
			width: 44%;
    		float: right;
		}

		#id_note-value {
			width: 69%;
    		float: right;
    		margin-top: -29px;
		}

		#id_note-value {
    		border: none !important;
		    background-color: transparent !important;
		    outline: none !important;
		    box-shadow: 0 0 !important;
		    color: #838383 !important;
		    letter-spacing: 0.8px !important;
		    font-weight: normal !important;
		    text-transform: uppercase !important;
		    font-size: 20px !important;
		    text-align: right !important;
		    padding: 0 !important;
		}
		.hsn-suggestion-internal {
		    max-width: 200px;
		    width: 160px;
		    margin-top:5px !important;
		}

		#id_note_item-__prefix__-amount_display {
			font-size: 20px;
    		padding: 0;
		}

		#note_item_table .unit_select_box {
			margin-top: 0;
		}
	</style>
	<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
	<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
	<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
	<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
    <link rel="stylesheet" type="text/css" href="/site_media/css/erp_default.css?v={{ current_version }}">
	<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/note.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/tags.js?v={{ current_version }}"></script>
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<script type="text/javascript" src="/site_media/js/Component/projects.js?v={{ current_version }}"></script>

	<div class="right-content-container">
		<div class="page-title-container">
			<span class="page-title">Note Creation</span>
		</div>
		<div class="page-heading_new" style="margin:0 15px; ">
			<span class="page_header"></span>
			<a onclick="window.history.back()" class="btn btn-add-new pull-right view_user" data-tooltip="tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
		</div>
		<div class="col-lg-12">
			<div class="contant-container-nav hide">
				<ul class="nav nav-tabs list-inline">
					<li class="active"><a data-toggle="tab" id="tab_view" href="#tab1">VIEW</a></li>
					<li><a data-toggle="tab" href="#tab2" id="custom-tab2">ADD</a></li>
				</ul>
			</div>
			<div class="tab-content">
				<div id="tab2" class="tab-pane fade in active">
					<div id ="drcr" style="display:block;">
						<form action="" id="gcs_upload_general" name="gcs_upload_note" class="google_upload_form" method="POST" enctype='multipart/form-data'>
							<span id="note_attachment_uploader_container"></span>
							<input type='file' name='file' onChange="convertFileToBase64(this)" style="display:none" />
						    <input type='submit' value='Upload File' style="display:none" />
				        </form>
						<form id="note_add" method="post" action="/erp/auditing/note/addNote/">
						    {% csrf_token %}
							<div class="col-lg-12 search_result_table remove-padding">    
							    <div class="col-sm-7">
									<div class="form-group hide" id="tags"><!-- Tags are hidden on the 2.16.3 release -->
										<label>Tags</label>
										<ul id="ul-tagit-display" class="tagit-display form-control">
											{% for tag_form in tags_formset.initial_forms %}
												<li class="li-tagit-display" id="{{ tag_form.prefix }}">
													<div hidden="hidden">{{ tag_form.tag }}
														{{ tag_form.DELETE }}{{ tag_form.ORDER }}</div>
													<label class="label-tagit-display"  id="id_{{tag_form.prefix}}-tag_label">{{ tag_form.tag.value }}</label>&nbsp;
													<a class="delete_tag" id="{{tag_form.prefix}}-del_button"></a>
													&nbsp;
												</li>
											{% endfor %}
											<li id="tag-__dummy__" hidden="hidden">
												<div hidden="hidden">{{ tags_formset.empty_form.tag }}
												{{ tags_formset.empty_form.ORDER }}
												{{ tags_formset.empty_form.DELETE }}</div>
												<label class="label-tagit-display"  id="id_{{tags_formset.empty_form.prefix}}-tag_label"></label>&nbsp;
												<a class="delete_tag" id="{{tags_formset.empty_form.prefix}}-del_button"></a>
												&nbsp;
											</li>

											<span>
												{{ tags_formset.management_form }}
												<span id="{{ tags_formset.empty_form.prefix }}" class="text_for_tag" >
													{{ tags_formset.empty_form.tag }}
													{{ tags_formset.empty_form.ORDER }}
													<span hidden="hidden">{{ tags_formset.empty_form.DELETE }}</span>
												</span>
											</span>
										</ul>
										<span style="position: absolute; margin-top: -8px; color: #aaa; font-size: 11px;">Type and press enter to add new tag.</span>
									</div>
									<div class="col-sm-2 hide" style="margin-top: 21px;">
										<input id="add_tag" class="btn btn-save btn-margin-1" value="+" type="button">
									</div>
								</div>
							</div>
							<div class="col-lg-12 search_result_table remove-padding">
								<div class="col-sm-2">
									<div id="switch_note" class="switch-radio-button btn-group" style="margin-top: 18px;">
										<a class="toggle_debit noActive" data-toggle="credit_debit_note" data-title= "0" data-tooltip="tooltip" title="Debit">Debit</a>
										<a class="toggle_credit noActive" data-toggle="credit_debit_note" data-title="1" data-tooltip="tooltip" title="Credit">Credit</a>
									</div>
									{{ note.is_credit}}
								</div>
								<div class="form-group col-sm-2">
									<div class="tour_project_tag">
											<div class="component_project" data-id="id_note-project_code" data-name="note-project_code" data-isSuper={{logged_in_user.is_super}} data-isEditDisable=false></div>
									</div>
									<label id="expRev" style="display: block;margin-top: 5px;">Revenue: <span id="revenue" style="margin-right: 10px;">0</span>Expense : <span id="expense" style="margin-right: 10px;">0</span> </label>
<!--									<label>Project/Tag<span class="mandatory_mark"> *</span></label>-->
<!--									{{note.project_code}}-->
								</div>
								<div class="col-lg-3 form-group">
									{{note.id}}
									{{note.code}}
									{{note.enterprise_id}}
									{{note.approved_by}}
									{{note.raised_by}}
									{{note.ledger_bill_id}}
									<input type="hidden" id="receipt_no" >
									<label>Party Name<span class="mandatory_mark"> *</span></label>
									<select class="form-control chosen-select" name="note-supplier_id" id="id_note-supplier_id">
										<span><option value="0" hidden="hidden">Select an option</option></span>
											 <optgroup label="Frequently used">
												  {% for j in frequent_party_list %}
												    {% if j.0|lower == note.supplier_id.value|lower %}
												        <option value="{{ j.0|lower }}" selected="selected">{{ j.1 }}</option>
												    {% else %}
												        <option value="{{ j.0|lower }}">{{ j.1 }}</option>
												    {% endif %}
												  {% endfor %}
											  </optgroup>
											  <optgroup label="All">
												{% for j in party_list %}
													{% if j.0|lower == note.supplier_id.value|lower %}
												        <option value="{{ j.0|lower }}" selected="selected">{{ j.1 }}</option>
												    {% else %}
												        <option value="{{ j.0|lower }}">{{ j.1 }}</option>
												    {% endif %}
												{% endfor %}
											  </optgroup>
									</select>
								</div>
								
								<div class="clearfix"></div>
								<div class="col-lg-2 form-group">
									<label><span>Receipt No:</span></label>
										{{note.receipt_code}}
								</div>
								<div class="col-lg-2 form-group">
									<label><span>Invoice Date:</span></label>
									{{note.invoice_date}}
									<input type="text" class="form-control custom_datepicker full-datepicker" placeholder="Select Date" id="voucher_date">
									<i class="glyphicon glyphicon-calendar custom-calender-icon"></i>
								</div>
								<div class="col-lg-3 form-group">
									<label>Invoice No:<span class="mandatory_mark">*</span></label>
									{{note.invoice_no}}
								</div>
								<div class="col-lg-2 form-group">
									<label>Invoice Value:<span class="mandatory_mark"> *</span></label>
										{{note.invoice_value}}
								</div>
								<div class="col-sm-3 remove-padding btn-margin-1">
									<div class="col-sm-9" style="width: 137px;">
										{{ note.document }}
										<input type="hidden" class="file_upload_json" name="note_attachment_upload_json" value="">
										<div class="btn-browse-container" style="width: 260px;">
											{% if note_attachment != None and note_attachment != "" %}
												<div class="btn-browse active" data-tooltip="tooltip" >
													<input type="hidden" class="base64-file-value base64" data-filename="{{note_attachment.name}}.{{note_attachment.ext}}" data-extension="{{note_attachment.ext}}" value="{{note_attachment.file}}">
													<i class="fa fa-file" aria-hidden="true"></i>
					                                <span class="browsed-text" style="margin-left: 15px;" data-default="Upload Document">{{note_attachment.name}}.{{note_attachment.ext}}</span>
					                                <div id='loadingmessage2_ie' style="margin-left:180px;margin-top:-20px; display: none;">
														<img src='{{ MEDIA_URL }}../images/ajax-loader.gif'/>
													</div>
												</div>
												{% else %}
													<div class="btn-browse " data-tooltip="tooltip" >
														<input type="hidden" class="base64-file-value base64" data-filename="">
														<i class="fa fa-file" aria-hidden="true"></i>
						                                <span class="browsed-text" style="margin-left: 15px;" data-default="Upload Document">Upload Document</span>
						                                <div id='loadingmessage2_ie' style="margin-left:180px;margin-top:-20px; display: none;">
															<img src='{{ MEDIA_URL }}../images/ajax-loader.gif'/>
														</div>
													</div>
											{% endif %}
											<i class="fa fa-times-circle btn-browse-remove" onClick="removeBrowsedFile(this)" aria-hidden="true"></i>
										</div>
									</div>
								</div>
							</div>
							<!-- Addition Note Item Form -->
							<div class="col-lg-12 search_result_table">
								<center><h4><label>Particulars</label></h4></center>
								<!-- This Part is used to display the nonstock Items -->
								<div class="row" id="materials_add">
									<!-- This part is used to add Product to the Sales invoice -->
									<div class="col-lg-3 form-group">
										<label>Description<span class="mandatory_mark"> *</span></label>
										{{ note_item_formset.management_form }}
										<div>
											<input type="text" value="" class="form-control" id="materialrequired"
											       placeholder="Select Material" maxlength="100" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')">
											<input type="text" value="" class="" id="material_id_hidden"
											       placeholder="" hidden="hidden">
										</div>

										<div hidden="hidden">
											{{ note_item_formset.empty_form.item_name }}
											{{ note_item_formset.empty_form.enterprise_id }}
										</div>  <!-- material_list -->
									</div>
									<div class="col-lg-2 form-group">
										<label>Reason<span class="mandatory_mark"> *</span></label>
										{{ note_item_formset.empty_form.reason_id }}
									</div>
									<div class="col-lg-2 form-group hsn-wrapper">
										<label>HSN/SAC<span class="mandatory_mark"> *</span></label>
										{{note_item_formset.empty_form.hsn_code}}
									</div>
									<div class="col-lg-2 form-group">
										<label>Quantity<span class="mandatory_mark"> *</span></label>
										{{note_item_formset.empty_form.quantity}}
										{{note_item_formset.empty_form.unit_id}}
									</div>
									<div class="col-lg-1 form-group">
										<label>Rate<span class="mandatory_mark"> *</span></label>
										{{note_item_formset.empty_form.rate}}
									</div>
									<div class="col-lg-2" hidden="hidden">
										<label>Amount</label>
										{{ note_item_formset.empty_form.amount }}
									</div>
									<div class="col-lg-2">
										<label>Amount</label>
										{{ note_item_formset.empty_form.amount_display }}
									</div>

									<div class="col-lg-2 text-right" style="margin-bottom: 5px;">
										<label>&nbsp;</label>
										<input type="button" class="btn btn-save btn-margin-1" id="add_note_item" value="+"/>
									</div>
								</div>
							</div>
							<div class="col-sm-12">
								<table class="table table-striped custom-table table-bordered custom-table-large text_box_in_table" id="note_item_table" >
									<thead>
										<tr>
											<th style="width: 75px;">S.No</th>
											<th style="width: 250px;">Description</th>
											<th style="width: 250px;">Reason</th>
											<th style="width: 150px;">Quantity (UoM)</th>
											<th style="width: 125px;">HSN/SAC</th>
											<th style="width: 125px;">Rate</th>
											<th style="width: 125px;">Amount</th>
											<!--<th style="width: 125px;">Type</th>-->
											<th style="width: 80px;">CGST % <span class="th-sub-heading bracket-enclosed">Amount</span></th>
											<th style="width: 80px;">SGST % <span class="th-sub-heading bracket-enclosed">Amount</span></th>
											<th style="width: 80px;">IGST % <span class="th-sub-heading bracket-enclosed">Amount</span></th>
										</tr>

									</thead>
									<tbody>
									<!--Dummy Form List-->
									<tr bgcolor="#ececec" id="note_item-__dummy__"
									    hidden="hidden">
										<td hidden="hidden">
											{{ note_item_formset.empty_form.note_id }}
											{{ note_item_formset.empty_form.DELETE }}
											{{ note_item_formset.empty_form.id }}
											{{ note_item_formset.empty_form.enterprise_id }}
											<input name="item_tax" id="id_invoice_mat-__prefix__-item_tax" type="text" hidden="hidden">
										</td>
										<td class="text-center">{% if note.status.value == 0 %}
											<a href="#"
											   id="id_{{ note_item_formset.prefix }}-deleteInvoiceMaterial"
											   onclick="javascript:deleteInvoiceMaterial('{{ note_item_formset.empty_form.prefix }}')">
												<i style="padding-top: 7px;" class="fa fa-trash-o" title="Delete" alt="Delete"></i>
											</a>{% endif %}
										<div id="id_note_item-__prefix__-s_no"></div></td>
										<td>
											{{ note_item_formset.empty_form.item_name }}
										</td>
										<td>
											{{ note_item_formset.empty_form.reason_id }}
										</td>
										<td class="textbox-text-right td-quantity-uom">
											<span class="col-sm-6 remove-padding">{{ note_item_formset.empty_form.quantity }}</span>
											<span class="col-sm-6 remove-padding">{{ note_item_formset.empty_form.unit_id }}</span>
										</td>
										<td class="td-hsn-code hsn-wrapper">
											{{ note_item_formset.empty_form.hsn_code }}
										</td>
										<td class="textbox-text-right">
											{{ note_item_formset.empty_form.rate }}
										</td>
										<td hidden="hidden">
											{{note_item_formset.empty_form.amount}}
										</td>
										<td class="textbox-text-right table_display_amt">
											{{note_item_formset.empty_form.amount_display}}
										</td>
										{{ note_item_formset.empty_form.taxes.management_form }}
										{% for tax in note_item_formset.empty_form.taxes %}
										<td class="taxable">{{ tax.tax_code }}{{ tax.rate }}
											<select id="id_{{tax.prefix}}-rate_drop_down"
													class="form-control"  name="{{tax.tax_type.value}}"
													onchange="javascript:calculateGSTAmount('{{tax.prefix}}', '{{note_item_formset.empty_form.prefix}}');calculateGrandTotal();">
												<option value="{{tax.tax_code.value}}">{% if tax.rate.value %}{{tax.rate.value}}{% else %}0{% endif %}</option>
											</select>
											<input type="text" id="id_{{tax.prefix}}-amount" class="form-control hide" name="{{tax.tax_type.value}}_AMT" value="{{tax.amount.value}}" disabled>
											<span class="td-sub-content bracket-enclosed" id="id_span_{{tax.prefix}}-amount">
												{{tax.amount.value}}
											</span>
											<script type="text/javascript">
												calculateGSTAmount('{{tax.prefix}}', '{{note_item_formset.empty_form.prefix}}');
											</script>
										</td>
										{% endfor %}
									</tr>

									<!-- Item editing List -->
									{% for note_item_form in note_item_formset.initial_forms %}
									<tr bgcolor="#ececec" id="{{note_item_form.prefix}}">
										<td hidden="hidden">
											{{ note_item_form.note_id }}
											{{ note_item_form.DELETE }}
											{{ note_item_form.id }}
											{{ note_item_form.enterprise_id }}
											<input name="item_tax"  type="text" hidden="hidden"
												   id="id_{{note_item_form.prefix}}-item_tax">
										</td>
										<td class="text-center">{% if note.status.value == 1 %}
											<a href="#"
											   id="id_{{ note_item_form.prefix }}-deleteInvoiceMaterial"
											   onclick="javascript:deleteInvoiceMaterial('{{ note_item_form.prefix }}')">
												<i class="fa fa-trash-o"></i>
											</a>{% endif %}
										{{forloop.counter}}.</td>
										<td>
											{{ note_item_form.item_name }}
										</td>
										<td>
											{{ note_item_form.reason_id }}
										</td>
										<td class="textbox-text-right td-quantity-uom">
											<span class="col-sm-6 remove-padding">{{ note_item_form.quantity }}</span>
											<span class="col-sm-6 remove-padding">{{ note_item_form.unit_id }}</span>
										</td>
										<td class="td-hsn-code hsn-wrapper">
											{{ note_item_form.hsn_code }}
										</td>
										<td class="textbox-text-right">
											{{ note_item_form.rate }}
										</td>
										<td hidden="hidden">
											{{note_item_form.amount }}
										</td>
										<td class="textbox-text-right table_display_amt">
											{{note_item_form.amount_display }}
										</td>
										{{ note_item_form.taxes.management_form }}
										{% for tax in note_item_form.taxes %}
										<td class="taxable">{{ tax.tax_code }}{{ tax.rate }}
											<select id="id_{{tax.prefix}}-rate_drop_down"
													class="form-control"  name="{{tax.tax_type.value}}"
													onchange="javascript:calculateGSTAmount('{{tax.prefix}}', '{{note_item_form.prefix}}');calculateGrandTotal();">
												<option value="{{tax.tax_code.value}}">{% if tax.rate.value %}{{tax.rate.value}}{% else %}0{% endif %}</option>
											</select>
											<input type="text" id="id_{{tax.prefix}}-amount" class="form-control hide" name="{{tax.tax_type.value}}_AMT" value="{{tax.amount.value}}" disabled>
											<span class="td-sub-content bracket-enclosed" id="id_span_{{tax.prefix}}-amount">
												{{tax.amount.value}}
											</span>
											<script type="text/javascript">
												calculateGSTAmount('{{tax.prefix}}', '{{note_item_form.prefix}}');
											</script>
										</td>
										{% endfor %}
									</tr>
									{% endfor %}
									</tbody>
									<tr>
										<td colspan="6" class='text-right'><span class="grand-total-text">Total</span></td><td align='right'>
											<input type='text'  id='note_tot' class='grand-total-amount' disabled='disabled' value='0' style="font-size: 0.8vw !important; width: 165px; !important"/>
										</td>
										<td><label id='cgst_total_amt'></label></td>
										<td><label id='sgst_total_amt'></label></td>
										<td><label id='igst_total_amt'></label></td>
									</tr>
								</table>
							</div>
							<div class="clearfix"></div>
							<div class="col-lg-4">
								<div class="col-lg-12 form-group remove-padding">
									<label>Remarks</label>
									{% if remarks_list != None and remarks_list|length > 0 %}
										<div class="chat-container">
										{% for remark in remarks_list %}
											<div class="chat-list">
												<span class="chat-list-name">{{ remark.by }}</span>
												<span class="chat-list-date">{{ remark.date }}</span>
												<span class="chat-list-description" style="word-break:break-all">{{ remark.remarks }}</span>
											</div>

										{% endfor %}
										</div>
									{% endif %}
									<BR/>
									{{note.remarks}}
								</div>

								<div class="col-lg-6 form-group" style="padding-left: 0;">
									<label>
									{% if logged_in_user.is_super %}
										<a class="super_edit_field super_edit_for_load" onclick="SuperEditNoteSelect();" data-tooltip="tooltip" data-placement="left" title="Super Edit">
											<i class="fa fa-pencil super_edit_in_field"></i>
										</a>
									{%else%}
										<a class="super_edit_field super_edit_for_load" onclick="" data-tooltip="tooltip" data-placement="bottom" title="Only Super User can edit this field">
											<i class="fa fa-pencil super_edit_in_field" style=" color: #777;"></i>
										</a>
									{%endif%}
									Currency</label>
									<div class="div-disabled">
									    {{ note.currency_id}}
									</div>
                                    <input type="hidden" name="home_currency" id="id_home_currency" value="{{home_currency}}">
                                    <div id="div_con_rate" class="conversion_rate_container hide">
                                        <span class="currency_convertor_section">
                                            <span>1</span>
                                            <span class="converted_currency_txt"></span>
                                            <span>=</span>
                                        </span>
                                        <span>{{ note.currency_conversion_rate}}</span>
                                        <span class="base_currency_txt"></span>
                                    </div>

								</div>
							</div>
							<div class="col-sm-4">
								<div class="row form-group">
									<div class="col-sm-10 taxable" id="tax_choices">
										{{ tax_list.management_form }}
										<label>Tax</label>
										<select class="form-control chosen-select" name="select" id="note_tax_list">
											{% for tax in load_tax %}
											<option value="{{ tax.0 }}">{{ tax.1 }}</option>
											{% endfor %}
										</select>
									</div>
									<div class="col-sm-2 taxable">
										<input type="button" class="btn btn-save btn-margin-1" id="add_note_tax" value="+"/>
									</div>
								</div>
								<div class="row form-group">
									<div hidden="hidden">
										{{ tax_list.empty_form.DELETE }}
										{{ tax_list.empty_form.tax_code }}
										{{ tax_list.empty_form.enterprise_id }}
										{{ tax_list.empty_form.receipt_no }}
									</div>
								</div>
							</div>
							<div class="col-sm-4" style="margin-top: 15px;">
								<div class="full_txt_width po_taxes_table">
									<table id="note_tax_table" border="0" width="100%" class="taxable">
										<tbody>
										<tr hidden="hidden" id="note_tax-__dummy__">
											<td hidden="hidden">
												{{ tax_list.empty_form.DELETE }}
												{{ tax_list.empty_form.enterprise_id }}
												{{ tax_list.empty_form.receipt_no }}
												{{ tax_list.empty_form.tax_code}}
												<a href="#"
												   id="id_{{ tax_list.empty_form.prefix }}-deleteNoteTax"
												   onclick="javascript:deleteNoteTax('{{ tax_list.empty_form.prefix }}')">
													<img src="/site_media/images/deleteButton.png"
													     title="Delete" alt="Delete"
													     align="center"/></a>
											</td>
										</tr>
										{% for tax_form in tax_list.initial_forms %}
										<tr hidden="hidden" id="{{ tax_form.prefix }}">
											<td>
												{{ tax_form.DELETE }}
												{{ tax_form.enterprise_id }}
												{{ tax_form.receipt_no }}
												{{ tax_form.tax_code }}
												<a href="#"
												   id="id_{{ tax_form.prefix }}-deleteNoteTax"
												   onclick="javascript:deleteNoteTax('{{ tax_form.prefix }}')">
													<img src="/site_media/images/deleteButton.png"
													     title="Delete" alt="Delete"
													     align="center"/></a>
											</td>
										</tr>
										{% endfor %}
										<tr hidden="hidden">
											<td><input type=hidden id="id_edit_data"
											           value="{{note_edit_id}}"/></td>
										</tr>
										</tbody>
									</table>
								</div>
								<div class="col-sm-12 remove-padding form-group">
									<div class="col-lg-12 text-right">
										<table id="note_taxes_table" border="0" width="100%">
										</table>
									</div>
									<div class="col-sm-12 remove-padding" style="margin-top: 15px;">
										<div class="pull-right">
											<label style="margin-top: 10px; width: 185px;">Round Off</label>
											{{note.round_off}}
										</div>
									</div>
									<div class="col-lg-12 remove-padding" style="margin-top: 15px;">
										<div class="pull-right">
											<label class="grand-total-text" style="margin-left: -7px;margin-top:8px;">Grand Total</label>
											{{note.value}}
										</div>
										<!-- <div class="col-sm-6 text-right" style="margin-top: 5px;">
											<span class="grand-total-text">Grand Total</span>
										</div>

										<div class="col-sm-6 remove-padding amt-total-as-label" style='padding-right:0px;'>
											{{note.value}}
										</div> -->
									</div>
								</div>
							</div>
							<div class="clearfix"></div>
							<div class="form-group col-sm-12 text-right">
								{% if access_level.edit %}
									<input type="button" class="btn btn-save" value="Save" id="cmdSave"/>
									<input type="button" class="btn btn-save" value="Approve" id="cmdApprove"/>
									<input type=hidden id="id_approve_status" name="approve_status" value="{{is_super_edit}}">
									<input id="generate_pdf" class="approve_po_button btn btn-save" type="button"
							       value="Approve/Reject PO" hidden="hidden" style="display:none !important"
							       onclick="javascript:document.getElementById('generate_pdf_btn').click();"/>
								{% endif %}
							</div>
						</form>
					</div>
				</div>
	            {% include "masters/add_project_modal.html" %}
			</div>
		</div>
	</div>
{% include "attachment_popup.html" %}

<script>
$(document).ready(function(){
	$("#switch_note a").on('click', function(){
		var selected = $(this).data('title');
		var toggle = $(this).data('toggle');
		$('#'+toggle).prop('value', selected);
		$('a[data-toggle="'+toggle+'"]').not('[data-title="'+selected+'"]').removeClass('active').addClass('noActive');
		$('a[data-toggle="'+toggle+'"][data-title="'+selected+'"]').removeClass('noActive').addClass('active');
		$("#switch_note a").removeClass("error-border");
		if(selected) {
			$("#id_note-is_credit").prop("checked", true);
		}
		else {
			$("#id_note-is_credit").prop("checked", false);
		}
	});
	noteCreationTypeInit();
	FromToDateValidation();
	$(".chosen-select").chosen();
	$("#doc_uploader").click(function(){
        $("#bill_copy_uploader").click();
    });
    NoteSuperEditInit();
});

function noteCreationTypeInit() {
	if($("#id_note-is_credit").is(":checked")) {
		$('#switch_note a[data-title="1"]').removeClass('noActive').addClass('active');
	}
	else {
		$('#switch_note a[data-title="0"]').removeClass('noActive').addClass('active');
	}
}

$("#add_note_tax").click(function(){
	$(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();

    var ControlCollections = [
        {
            controltype: 'dropdown',
            controlid: 'id_note_tax',
            isrequired: true,
            errormsg: 'Tax type is required.'
        }
    ];
    var result = JSCustomValidator.JSvalidate(ControlCollections);
	return result;
});

function FromToDateValidation(){
	$('#fromdate').on('changeDate', function(){
		$('#todate').datepicker('setStartDate', new Date($(this).val()));
		var start = new Date($('#fromdate').val()),
		end   = new Date($('#todate').val()),
		diff  = new Date(end - start),
		days  = diff/1000/60/60/24;
		if(days < 0) {
			$('#todate').datepicker('update', $('#fromdate').val());
		}
	});
}

function convertToBase64() {
	var fileExtension = ['jpeg', 'jpg', 'png', 'pdf'];
	if ($.inArray($("#bill_copy_uploader").val().split('.').pop().toLowerCase(), fileExtension) == -1) {
		swal("Invalid File Format", "Please upload only "+fileExtension.join(', ')+" file format.", "warning");
		setTimeout(function(){
			$("#bill_copy_uploader").val('').clone(true);
			$("#bill_copy_uploader").closest('form').find('.bootstrap-filestyle').find('input.form-control ').val('');
			$("#id_note-document_data").val('');
		},200);
	}
	else {
		var selectedFile = document.getElementById("bill_copy_uploader").files;
		if (selectedFile.length > 0) {
			var fileToLoad = selectedFile[0];
			var fileReader = new FileReader();
			var base64;
			fileReader.onload = function(fileLoadedEvent) {
				base64 = fileLoadedEvent.target.result;
				$("#id_note-document_data").val(base64);
				$("#id_note-document").val($("#bill_copy_uploader").val());

				old_filename = $("#id-display_document").attr('data-filename');
				if (old_filename != null  && old_filename.indexOf(".") > -1) {
					ext = $("#id-display_document").attr('data-filename').split(".").pop().toLowerCase();
					$("#id-display_document").removeClass(ext);
				}
				ext = $("#bill_copy_uploader").val().split(".").pop().toLowerCase();
				$("#id-display_document").removeClass("pdf png jpg jpeg");
				$("#id-display_document").addClass(ext+" document_attachment linked_text base64");
				$("#id-display_document").attr('data-filename', $("#bill_copy_uploader").val());
				$("#id-display_document").attr('data-url', base64).attr('data-extension', ext);
			};
			fileReader.readAsDataURL(fileToLoad);
		}
	}
}

function NoteSuperEditInit(){
	if($("#is_super_user").val().toLowerCase() == 'true') {
		$(".super_edit_field").removeClass("hide");
	}
	else {
<!--		$(".super_edit_field").addClass("hide");-->
	}
}

function SuperEditNoteSelect(){
	$("#id_home_currency").prev("div").removeClass("div-disabled");
	$(".super_edit_field").addClass("hide");
}
</script>

{% endblock %}