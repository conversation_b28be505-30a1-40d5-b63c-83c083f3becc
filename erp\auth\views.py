"""
"""
import datetime
import json
import jwt
from django.contrib import auth
from django.contrib.auth import SESSION_KEY
from django.http import HttpResponseRedirect, HttpResponse
from django.shortcuts import render
from django.template import RequestContext
from django.template.response import TemplateResponse
from django.utils.encoding import smart_str
from sqlalchemy.orm import make_transient

from erp.accounts.backend import AccountService
from erp.admin import enterprise_mandatory_fields
from erp.admin.backend import UserService
from erp.auth import logger, SESSION_USERMAIL_KEY, ENTERPRISE_FORM_KEY, \
	ENTERPRISE_FORM_PREFIX, USER_FCM_ID_KEY, USER_IN_SESSION_KEY, ENTERPRISE_ID_SESSION_KEY
from erp.auth.backend import LoginService, UserVO
from erp.auth.request_handler import RequestHandler
from erp.commons.backend import sendMail
from erp.forms import EnterpriseRegistrationForm
from erp.icd.backend import <PERSON>t<PERSON><PERSON>
from erp.masters.backend import <PERSON><PERSON><PERSON>
from erp.models import Voucher, Receipt, Enterprise, User
from erp.properties import LOGIN_URL, LOGIN_TEMPLATE, CHANGE_PASSWORD_TEMPLATE, TEMPLATE_TITLE_KEY, \
	MANAGE_ENTERPRISE_URL, FORGET_PASSWORD_TEMPLATE, SESSION_EXPIRY_TEMPLATE, HOME_URL, ERP_HOME_TEMPLATE, FEEDBACK_MAIL
from erp.purchase.service import PurchaseDAO
from erp.sales.backend import InvoiceDAO
from erp.stores.backend import StoresDAO
from settings import CURRENT_VERSION, SQLASession, JWT_SECRET, FIRE_BASE_AUTH_KEY, XSASSIST_MAIL_ID, GCS_BUCKET_NAME, \
	FCS_API_KEY, FCS_APP_ID, FCS_PROJECT_ID
from util.api_util import response_code
from util.helper import readFile

__author__ = 'kalaivanan'


def login(request):
	"""
	Application's single-point Login. Once the User's credentials are verified he/she will be allowed access to the app
	and the User's identification and authorization information will be placed in Session, to be referred while
	processing subsequent Application actions.

	:param request:
	:return:
	"""
	login_service = LoginService()
	request_handler = RequestHandler(request)
	enterprise = None
	logged_in_user = None
	if request_handler.isPostRequest():
		user_email = request_handler.getPostData('user_email')
		password = request_handler.getPostData('password')
		(logged_in_user, is_active) = login_service.authenticate(user_email, password)
		if logged_in_user and is_active:
			if SESSION_KEY in request.session:
				if request.session[SESSION_KEY] != logged_in_user.id:
					# To avoid reusing another user's session, create a new, empty
					# session if the existing session corresponds to a different
					# authenticated user.
					request.session.flush()
			else:
				request.session.cycle_key()
			enterprise = next((item for item in logged_in_user.user_enterprise_map if item.status == 1), None).enterprise
			login_service.putUserInfoInSession(request, logged_in_user, enterprise)
			request_handler.setSessionToken(user_id=logged_in_user.id, enterprise_id=enterprise.id)
			logger.info("User Login Success")
		elif logged_in_user:
			logger.info('Login Failed')
			make_transient(logged_in_user)
			return render(
				request, LOGIN_TEMPLATE, {'user_inactive': True, 'current_version': CURRENT_VERSION})
		else:
			logger.info('Login Failed')
			request_handler.setSessionAttribute("wrong_credentials", True)
			return HttpResponseRedirect(LOGIN_URL)
	elif not (request_handler.isGetRequest() and request_handler.isSessionActive()):
		return HttpResponseRedirect(LOGIN_URL)
	if logged_in_user:
		make_transient(logged_in_user)
	if logged_in_user and logged_in_user.is_super and enterprise and not enterprise.isRegistrationComplete():
		request_handler.setSessionAttribute(
			'message', 'Please fill out the below mentioned basic details.!\n %s' % enterprise_mandatory_fields)
		return HttpResponseRedirect(MANAGE_ENTERPRISE_URL)
	elif logged_in_user and logged_in_user.landing_url not in (None, ""):
		return HttpResponseRedirect(logged_in_user.landing_url)
	else:
		return HttpResponseRedirect(HOME_URL)


def logoutSession(request):
	"""
	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		user_id = rh.getSessionAttribute(SESSION_KEY)
		fcm_id = rh.getSessionAttribute(key=USER_FCM_ID_KEY)
		if rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY) and fcm_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
			LoginService().unRegisterFcmId(enterprise_id=enterprise_id, user_id=user_id, fcm_id=fcm_id)
		request.session.flush()
		auth.logout(request)
	except Exception as e:
		logger.exception("Logout Session issue.. %s" % e)


def logout(request):
	"""
	Logs the user out of the application, flushes the Login info from the session

	:param request:
	:return: renders the login screen
	"""
	try:
		logoutSession(request)
	except Exception as e:
		logger.exception("Logout issue.. %s" % str(e))
	return render(
		request, SESSION_EXPIRY_TEMPLATE, {"message": "Successfully Logged Out!", "current_version": CURRENT_VERSION})


def renderLoginPage(request):
	"""
	Renders the Login Page on request triggered by URL hit, referred from the urls.py

	:param request:
	:return:
	"""
	# render_to_response(LOGIN_TEMPLATE, {'wrong_credentials': False}, context_instance=RequestContext(request))
	rh = RequestHandler(request)
	message = rh.getSessionAttribute('message')
	rh.setSessionAttribute('message', "")
	wrong_credentials = rh.getSessionAttribute("wrong_credentials")
	rh.removeSessionAttribute("wrong_credentials")
	enterprise_registration_form = EnterpriseRegistrationForm(prefix=ENTERPRISE_FORM_PREFIX)
	context = {
		'message': message if message else "", 'current_version': CURRENT_VERSION,
		TEMPLATE_TITLE_KEY: "xserp - Login", ENTERPRISE_FORM_KEY: enterprise_registration_form,
		'FIRE_BASE_AUTH_KEY': json.dumps(FIRE_BASE_AUTH_KEY)}
	if wrong_credentials:
		context.update({'wrong_credentials': wrong_credentials})
	return TemplateResponse(template=LOGIN_TEMPLATE, request=request, context=context)


def renderHomePage(request):
	return TemplateResponse(template=ERP_HOME_TEMPLATE, request=request, context={TEMPLATE_TITLE_KEY: "Home"})


def json_login(request):
	"""
	Login Api will result the access map

	:param request:
	:return: Json response
	"""
	login_service = LoginService()
	user_service = UserService()
	user_email = ''
	try:
		rh = RequestHandler(request)
		user_email = rh.getPostData('user_email')
		password = rh.getPostData('password')
		fcm_id = rh.getPostData(USER_FCM_ID_KEY)
		logger.info("Attempting to login user %s" % user_email)
		if user_email is None or password is None:
			response = response_code.paramMissing()
		else:
			(logged_in_user, is_active) = login_service.authenticate(user_email, password)
			if logged_in_user and is_active:
				response = response_code.success()
				response['custom_message'] = 'Login Success'
				enterprise = next((item for item in logged_in_user.user_enterprise_map if item.status == 1), None).enterprise
				enterprise_id = enterprise.id
				logged_in_user.permissions = user_service.generatePermissionsStub(logged_in_user, enterprise_id)
				response.update(user_service.constructUserMap(logged_in_user, enterprise_id))
				enterprise = SQLASession().query(Enterprise).filter(Enterprise.id == enterprise_id).first()
				response['fy_start_day'] = enterprise.fy_start_day
				response['token'] = rh.generateJwtToken(
					user_id=logged_in_user.id, enterprise_id=enterprise_id)
				login_service.registerFcmId(
					enterprise_id=enterprise_id, user_id=logged_in_user.id, fcm_id=fcm_id)
				make_transient(getuserAlerts(logged_in_user, response=response, enterprise_id=enterprise_id))
			elif logged_in_user:
				make_transient(logged_in_user)
				logger.info('Login Failed')
				return render(
					request, LOGIN_TEMPLATE, {'user_inactive': True, 'current_version': CURRENT_VERSION})
			else:
				response = response_code.failure()
				response['custom_message'] = 'Incorrect Username/Password'
	except Exception as e:
		logger.exception("Failed login mobile app user %s due to %s" % (user_email, str(e)))
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def json_userAlerts(request):
	"""
	To get user settings

	:param request:
	:return:
	"""
	try:
		login_service = LoginService()
		user_service = UserService()
		rh = RequestHandler(request)
		user_id = rh.getPostData('user_id')
		enterprise_id = rh.getPostData('enterprise_id')
		if user_id is None:
			response = response_code.paramMissing()
		else:
			logged_in_user = login_service.user_dao.db_session.query(User).filter(user_id == User.id).first()
			enterprise = SQLASession().query(Enterprise).filter(Enterprise.id == enterprise_id).first()
			request.session[USER_IN_SESSION_KEY] = UserVO(user=logged_in_user, enterprise=enterprise)
			logged_ = request.session[USER_IN_SESSION_KEY]
			module_access = logged_.getModuleAccess(enterprise_id, request)
			response = response_code.success()
			response['icd_enabled'] = module_access['icd']
			response['icd_ignore_credit_note'] = module_access['icd_ignore_credit_note']
			response['icd_auto_gen_voucher'] = module_access['icd_auto_gen_voucher']
			response['custom_message'] = 'Login Success'
			response['bucket_name'] = GCS_BUCKET_NAME
			response['fcs_api_key'] = FCS_API_KEY
			response['fcs_api_id'] = FCS_APP_ID
			response['fcs_project_id'] = FCS_PROJECT_ID
			logged_in_user.permissions = user_service.generatePermissionsStub(logged_in_user, int(enterprise_id))
			response.update(user_service.constructUserMap(logged_in_user, int(enterprise_id)))
			response['fy_start_day'] = enterprise.fy_start_day
			response['server_date'] = datetime.datetime.today().strftime('%Y-%m-%d')
			response.update(login_service.prepareEnterpriseSubscriptionInfo(enterprise_id=int(enterprise_id)))
			make_transient(getuserAlerts(logged_in_user, response=response, enterprise_id=int(enterprise_id)))
	except Exception as e:
		logger.exception("Failed setting alerts %s " % str(e))
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getuserAlerts(logged_in_user=None, response=None, enterprise_id=None):
	"""

	:param logged_in_user:
	:param response:
	:param enterprise_id:
	:return:
	"""
	try:
		if logged_in_user.is_super or response['permissions']['ACCOUNTS']['alert']:
			response['pending_voucher_count'] = AccountService().getVoucherCount(
				enterprise_id=enterprise_id, status=Voucher.DRAFT)
		if logged_in_user.is_super or response['permissions']['PURCHASE']['alert']:
			response['pending_po_count_notification'] = PurchaseDAO().getPendingPOCount(
				enterprise_id=enterprise_id)
		if logged_in_user.is_super or response['permissions']['MASTERS']['alert']:
			response['master_material_price'] = MasterDAO().getPendingPriceApprovalCount(
				enterprise_id=enterprise_id)
		if logged_in_user.is_super or response['permissions']['SALES']['alert']:
			invoice_service = InvoiceDAO()
			response['invoice_pending_count'] = invoice_service.getPendingInvoiceCount(
				enterprise_id=enterprise_id)
			response['oa_pending_count'] = invoice_service.getPendingOACount(
				enterprise_id=enterprise_id)
		if logged_in_user.is_super or response['permissions']['ICD']['alert']:
			response['icd_checked_count'] = AuditDAO().getReceiptNoteCount(
				enterprise_id=enterprise_id, status=Receipt.STATUS_ICD_CHECKED)
		if logged_in_user.is_super or response['permissions']['STORES']['alert']:
			response['receipt_save_count'] = StoresDAO().notificationReceiptCount(
				enterprise_id=enterprise_id,
				status=(Receipt.STATUS_DRAFT, Receipt.STATUS_GRN_RETURNED))
		return logged_in_user
	except Exception as e:
		logger.info("Failed getting user details %s " % str(e))


def json_logout(request):
	"""

	:param request:
	:return: Json response
	"""
	login_service = LoginService()
	try:
		rh = RequestHandler(request)
		user_id = rh.getPostData('user_id')
		fcm_id = rh.getPostData(USER_FCM_ID_KEY)
		enterprise_id = rh.getPostData('enterprise_id')
		if user_id is None:
			user_email = rh.getPostData('user_email')
			user = login_service.getLoginUserByEmail(user_email)
			user_id = user.id
			enterprise_id = enterprise_id
		if user_id is None:
			response = response_code.paramMissing()
		else:

			logout_result = LoginService().unRegisterFcmId(enterprise_id=enterprise_id, user_id=user_id, fcm_id=fcm_id)
			response = response_code.success()
			if logout_result:
				response['custom_message'] = 'Logout Success'
				logger.info("Successfully logged out user %s" % user_id)
			else:
				response['custom_message'] = 'Failed to un-register fcm id'
				logger.info("Failed logout user %s. Failed to un-register fcm id" % user_id)
	except Exception as e:
		logger.warn("Failed mobile user logout, due to %s" % str(e))
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def change_password(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		user_email = rh.getSessionAttribute(SESSION_USERMAIL_KEY)
		if not user_email and 'u' in request.GET:
			token = request.GET['u']
		else:
			token = rh.getSessionAttribute("token")
			rh.removeSessionAttribute("token")
		logger.info("Initiated password change process")
		message = rh.getSessionAttribute("message")
		rh.removeSessionAttribute("message")
		return TemplateResponse(template=CHANGE_PASSWORD_TEMPLATE, request=request, context={
			"user_email": user_email, "message": message, "token": token, TEMPLATE_TITLE_KEY: "Change Password"})
	except Exception as e:
		raise e


def forget_password(request):
	"""

	:param request:
	:return:
	"""
	try:
		logoutSession(request)
		rh = RequestHandler(request)
		user_email = rh.getSessionAttribute(SESSION_USERMAIL_KEY)
		if not user_email and 'u' in request.GET:
			token = request.GET['u']
		else:
			token = rh.getSessionAttribute("token")
			rh.removeSessionAttribute("token")
		logger.info("Initiated password change process")
		message = rh.getSessionAttribute("message")
		rh.removeSessionAttribute("message")
		return TemplateResponse(template=FORGET_PASSWORD_TEMPLATE, request=request, context={
			"user_email": user_email, "message": message, "token": token, TEMPLATE_TITLE_KEY: "Forget Password",
			"current_version": CURRENT_VERSION})
	except Exception as e:
		raise e


def registerEnterprise(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		logger.info("Request to register new enterprise")
		enterprise_name = rh.getPostData("enterprise_name")
		first_name = rh.getPostData('firstname')
		last_name = rh.getPostData('lastname')
		email = rh.getPostData('email')
		mobile = rh.getPostData('mobile')
		password = rh.getPostData('password')
		url_base = request.build_absolute_uri().split("/erp/")[0]
		if rh.isGetRequest() and 'r' in request.GET:
			data = jwt.decode(request.GET['r'], JWT_SECRET)
			token, status = LoginService().registerEnterprise(
				enterprise_name=data['name'], first_name=data['first_name'], last_name=data['last_name'],
				enterprise_email=data['email'], enterprise_mobile=data['mobile'], password=data['password'])
			if status is True:
				LoginService().sendRegisteredIntimationMail(
					enterprise_email=data['email'], first_name=data['first_name'], last_name=data['last_name'])
				response = response_code.success()
				response["response_message"] = 'Registration Completed Successfully.'
				response["custom_message"] = 'Your Activation Link has been verified Successfully. Now you can log in to your account using your valid credentials.'
			else:
				response = response_code.failure()
				response["custom_message"] = token
		else:
			registration_status, registration_description, valid_email = LoginService().validateEmailId(
				url_base=url_base, enterprise_name=enterprise_name, first_name=first_name,
				last_name=last_name, enterprise_email=email, enterprise_mobile=mobile, password=password)
			if valid_email:
				mail_body_prefix = 'Enterprise'
				mail_body_suffix = 'registered'
				LoginService().sendSignupIntimationMail(
					enterprise_name=enterprise_name, first_name=first_name, last_name=last_name, enterprise_email=email,
					enterprise_mobile=mobile, mail_body_prefix=mail_body_prefix, mail_body_suffix=mail_body_suffix)
				response = response_code.success()
				logger.info("loading..%s" % registration_description)
				response["response_message"] = registration_status
				response["custom_message"] = registration_description
			else:
				response = response_code.failure()
				response["custom_message"] = registration_description

	except Exception as e:
		logger.exception("Enterprise registration is failed... %s" % str(e))
		response = response_code.internalError()
		response['error'] = "Failed due to internal server issue. Please contact support!"
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def validateAndRegisterEnterprise(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		if rh.isGetRequest() and 'r' in request.GET:
			data = jwt.decode(request.GET['r'], JWT_SECRET)
			token, status = LoginService().registerEnterprise(
				enterprise_name=data['name'], first_name=data['first_name'], last_name=data['last_name'],
				enterprise_email=data['email'], enterprise_mobile=data['mobile'], password=data['password'])
			if status is True:
				LoginService().sendRegisteredIntimationMail(
					enterprise_email=data['email'], first_name=data['first_name'], last_name=data['last_name'])
				rh.setSessionAttribute(
					"message", "<div style='font-weight: normal; font-size: 22px; text-shadow: 0 0;'>"
					"Registration Completed Successfully.</div><br />Your Activation Link has been verified Successfully. "
					"Now you can log in to your account using your valid credentials.")
				return HttpResponseRedirect(LOGIN_URL)
			else:
				registration_status = token
		else:
			registration_status = "Failed! Param missing to register enterprise"
		rh.setSessionAttribute("message", registration_status)
		logger.info(registration_status)
	except Exception as e:
		logger.exception("Enterprise registration is failed... %s" % str(e))
		rh.setSessionAttribute("message", "Failed due to internal server issue. Please contact support!")
	return HttpResponseRedirect(LOGIN_URL)


def validateEmailOnRegistration(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	try:
		enterprise_email = rh.getPostData('enterprise_email')
		user = LoginService().getLoginUserByEmail(email=enterprise_email)
		if user:
			response = response_code.failure()
			response['custom_message'] = "Email address provided is already registered."
		else:
			response = response_code.success()
			response['custom_message'] = ""
	except Exception as e:
		logger.exception("Enterprise registration is failed... %s" % str(e))
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def downloadEnterpriseLogo(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	enterprise_id = rh.getPostData('enterprise_id')
	if not enterprise_id:
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	document_type = rh.getPostData('response_data_type')  # data, file
	if not document_type:
		document_type = "file"
	try:
		enterprise = SQLASession().query(Enterprise).filter(Enterprise.id == enterprise_id).first()
		if enterprise.images.logo and enterprise.images.logo != "":
			document_uri = "enterprise_%s.%s" % (enterprise_id, enterprise.images.ext)
			if document_type == "file":
				response = HttpResponse(enterprise.images.logo, mimetype='application/force-download')
				response['Content-Disposition'] = 'attachment; filename=%s' % smart_str(document_uri)
				return response
			else:
				response = response_code.success()
				response['ext'] = document_uri.split(".")[-1].lower()
				if response['ext'] == 'pdf':
					response['data'] = "data:application/pdf;base64,%s" % enterprise.images.logo.encode('base64')
				else:
					response['data'] = "data:image/%s;base64,%s" % (
						response['ext'], enterprise.images.logo.encode('base64'))
				response['filename'] = document_uri
		else:
			response = response_code.failure()
			response['custom_message'] = "No logo attached"

	except Exception as e:
		logger.exception("Download enterprise logo failed for %s. %s" % (enterprise_id, str(e)))
		response = response_code.internalError()
		response['error'] = '%s' % str(e)
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def send_query_mail(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		service = LoginService()
		user_email = rh.getPostData('user_email')
		query = rh.getPostData('query')
		from_alias = "XSerp"
		from_addr = "<EMAIL>"
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		if user_email is None:
			response = response_code.paramMissing()
		else:
			user = service.user_dao.getUserByEmail(email=user_email)
			enterprise = service.user_dao.db_session.query(Enterprise).filter(Enterprise.id == enterprise_id)
			name = ""
			if user:
				name = user.first_name + user.last_name
				subject = "[Query] from " + name + " - " + enterprise.name
				message = readFile(FEEDBACK_MAIL).replace("{{name}}", name).replace("{{email}}", user_email).replace(
					"{{feedback}}", query).replace("{{enterprise}}", "Enterprise: " + enterprise.name).replace(
					"{{footer_text}}", "Query")
			else:
				subject = "[Query] from " + user_email
				message = readFile(FEEDBACK_MAIL).replace("{{name}}", name).replace("{{email}}", user_email).replace(
						"{{feedback}}", query).replace("{{enterprise}}", "").replace("{{footer_text}}", "Query")

			if sendMail(
				recipients=XSASSIST_MAIL_ID, subject=subject, body=message, from_alias=from_alias,
				from_addr=from_addr, reply_to=(user_email,)):
				response = response_code.success()
				message = "Your mail has been sent successfully."
				response['custom_message'] = message
			else:
				response = response_code.failure()
				message = "Failed to send e-mail"
				response['custom_message'] = message
	except Exception as e:
		logger.exception("Failed to send the e-mail request! %s" % str(e))
		response = response_code.internalError()
		response['custom_message'] = "Could not send e-mail"
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def send_sign_up_mail(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		logger.info("New enterprise sign up initiated")
		enterprise_name = rh.getPostData("enterprise_name")
		first_name = rh.getPostData('firstname')
		last_name = rh.getPostData('lastname')
		email = rh.getPostData('email')
		mobile = rh.getPostData('mobile')
		mail_body_prefix = 'A new Enterprise'
		mail_body_suffix = 'signed up with xserp'
		LoginService().sendSignupIntimationMail(
			enterprise_name=enterprise_name, first_name=first_name, last_name=last_name, enterprise_email=email,
			enterprise_mobile=mobile, mail_body_prefix=mail_body_prefix, mail_body_suffix=mail_body_suffix)
		response = response_code.success()
	except Exception as e:
		logger.exception("Enterprise Sign up is failed... %s" % str(e))
		response = response_code.internalError()
		response['error'] = "Failed due to internal server issue. Please contact support!"
	return HttpResponse(json.dumps(response), 'content-type=text/json')
