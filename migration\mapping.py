"""
"""

__author__ = 'saravanan'

voucher_type_name = {'General': 'Journal', 'Cash': 'Payment', 'Bank': 'Payment', 'Purchase': 'Purchase',
                     'Sales': 'Sales', 'Note': 'Debit Note', 'sales': 'Sales',
                     'dc': "Delivery Note", 'jdc': "Delivery Note", 'GRN': "Purchase", 'GRN DC': "Receipt Note", 'RJ': "Rejections In", 'RO': "Rejections Out",
                     'internal': "Stock Journal", 'Job In': "Material In", 'JDC': "Material Out"}


tm_header_details = {'envelope': {'tally': 'ENVELOPE', 'xserp': ''},
                     'header': {'tally': 'HEADER', 'xserp': ''},
                     'body': {'tally': 'BODY', 'xserp': ''},
                     'tally_request': {'tally': 'TALLYREQUEST', 'xserp': ''},
                     'tally_request_data': {'tally': 'Import Data', 'xserp': ''},
                     'import_data': {'tally': 'IMPORTDATA', 'xserp': ''},
                     'request_desc': {'tally': 'REQUESTDESC', 'xserp': ''},
                     'report_name': {'tally': 'REPORTNAME', 'xserp': ''},
                     'report_name_data': {'tally': 'All Masters', 'xserp': ''},
                     'static_variables': {'tally': 'STATICVARIABLES', 'xserp': ''},
                     'current_company': {'tally': 'SVCURRENTCOMPANY', 'xserp': ''},
                     'company_name': {'tally': 'Test 2.12', 'xserp': ''},
                     'request_data': {'tally': 'REQUESTDATA', 'xserp': ''},
                     'tally_message': {'tally': 'TALLYMESSAGE', 'xserp': ''},
                     }

tm_mapping_master = {
	'1.unit':
		{
			'model': {'tally': 'UNIT', 'xserp': 'UnitMaster'},
			'header_param': {
				'name': {'tally': 'NAME', 'xserp': 'unit_name', 'manipulate': 'string'},
				'reserved_name': {'tally': 'RESERVEDNAME', 'xserp': 'unit_name', 'manipulate': 'string'}},
			'fields': {
				'name': {'tally': 'NAME', 'xserp': 'unit_name', 'unique': True, 'manipulate': 'string'},
				'description': {
					'tally': 'ORIGINALNAME', 'xserp': 'unit_description', 'unique': True, 'manipulate': 'string',
					'criteria': {'key': 'equal', 'value': 'name', 'action': 'append', 'which': 'name'}
				},
				'defaults': {'tally': {'ISSIMPLEUNIT': 'Yes', 'DECIMALPLACES': "2"}, 'xserp': {}}}
		},
	'2.item_category':
		{
			'model': {'tally': 'STOCKCATEGORY', 'xserp': 'Category'},
			'header_param': {
				'name': {'tally': 'NAME', 'xserp': 'name'},
				'reserved_name': {'tally': 'RESERVEDNAME', 'xserp': ''}},
			'fields': {
				'defaults': {'tally': {'PARENT': '', 'ASORIGINAL': 'No'}, 'xserp': {}},
				'namelist': {'tally': 'Yes', 'xserp': ''}
			}
		}
	,
	'3.item_master':
		{
			'model': {'tally': 'STOCKITEM', 'xserp': 'Material'},
			'header_param': {
				'name': {'tally': 'NAME', 'xserp': 'name'},
				'reserved_name': {'tally': 'RESERVEDNAME', 'xserp': ''}},
			'fields': {
				'1.catergory': {'tally': 'CATEGORY', 'xserp': 'category'},
				'2.unit': {'tally': 'BASEUNITS', 'xserp': 'unit', 'manipulate': 'string'},
				'3.opening_balance': {'tally': 'OPENINGBALANCE', 'xserp': 'opening_balance'},
				'4.opening_value': {'tally': 'OPENINGVALUE', 'xserp': 'opening_value'},
				'5.price': {'tally': 'OPENINGRATE', 'xserp': 'price'},
				'defaults': {'tally': {'PARENT': ''}, 'xserp': {}},
				'namelist': {'tally': 'Yes', 'xserp': ''}}
		},
	'4.account_group':
		{
			'model': {'tally': 'GROUP', 'xserp': 'AccountGroup'},
			'header_param': {
				'name': {'tally': 'NAME', 'xserp': 'name'},
				'reserved_name': {'tally': 'RESERVEDNAME', 'xserp': 'name'}},
			'fields': {
				'name': {'tally': 'NAME', 'xserp': 'name'},
				'billable': {'tally': 'ISBILLWISEON', 'xserp': 'billable'},
				'parent': {'tally': 'PARENT', 'xserp': 'parent_name'},
				'affect_stock': {'tally': 'AFFECTSSTOCK', 'xserp': 'affect_stock'},
				'sub_ledger': {'tally': 'ISSUBLEDGER', 'xserp': 'sub_ledger'},
				'defaults': {'tally': {'ISREVENUE': 'No'}, 'xserp': {}}}

		},
	'5.account_ledger':
		{
			'model': {'tally': 'LEDGER', 'xserp': 'Ledger'},
			'header_param': {
				'name': {'tally': 'NAME', 'xserp': 'name'},
				'reserved_name': {'tally': 'RESERVEDNAME', 'xserp': ''}},
			'fields': {
				'group_name': {'tally': 'PARENT', 'xserp': 'group_name'},
				'credit_period': {'tally': 'BILLCREDITPERIOD', 'xserp': 'credit_period'},
				'address': {'tally': 'ADDRESS', 'xserp': 'address'},
				'opening_balance': {'tally': 'OPENINGBALANCE', 'xserp': 'opening_balance'},
				'affect_stock': {'tally': 'AFFECTSSTOCK', 'xserp': 'affect_stock'},
				'is_bill_wise_on': {'tally': 'ISBILLWISEON', 'xserp': 'is_bill_wise_on'},
				'defaults': {'tally': {'TAXTYPE': 'others'}, 'xserp': {}},
				'namelist': {'tally': 'Yes', 'xserp': ''}
			}
		},
	'6.voucher':
		{
			'model': {'tally': 'VOUCHER', 'xserp': 'Voucher'},
			'header_param': {
				'voucher_type': {'tally': 'VCHTYPE', 'xserp': 'type'},
				'obj_view': {'tally': 'OBJVIEW', 'xserp': 'view'},
				'action': {'tally': 'ACTION', 'xserp': ''}},
			'fields': {
				'1.date': {'tally': 'DATE', 'xserp': 'date'},
				'2.narration': {'tally': 'NARRATION', 'xserp': 'narration'},
				'3.party_name': {'tally': 'PARTYNAME', 'xserp': 'party_name'},
				'4.type': {'tally': 'VOUCHERTYPENAME', 'xserp': 'type'},
				'5.voucher_no': {'tally': 'VOUCHERNUMBER', 'xserp': 'voucher_no'},
				'6.party_ledger_name': {'tally': 'PARTYLEDGERNAME', 'xserp': 'party_ledger_name'},
				'7.view': {'tally': 'PERSISTEDVIEW', 'xserp': 'view'},
				'8.voucher_date': {'tally': 'EFFECTIVEDATE', 'xserp': 'voucher_date'},
				'defaults': {'tally': {'DIFFACTUALQTY': 'Yes'}, 'xserp': {}}
			},
			'relationship': {
				'1.voucher_particulars': {
					'model': {'tally': 'ALLLEDGERENTRIES.LIST', 'xserp': 'particulars'},
					'fields': {
						'1.is_debit': {'tally': 'ISDEEMEDPOSITIVE', 'xserp': 'is_debit'},
						'2.ledger': {'tally': 'LEDGERNAME', 'xserp': 'ledger_name'},
						'3.amount': {'tally': 'AMOUNT', 'xserp': 'amount'},
						'4.is_party_ledger': {'tally': 'ISPARTYLEDGER', 'xserp': 'party_ledger'},
						'defaults': {'tally': {'REMOVEZEROENTRIES': 'No'}, 'xserp': {}}
					},
					'relationship': {
						'bill_settlements': {
							'model': {'tally': 'BILLALLOCATIONS.LIST', 'xserp': 'settlements'},
							'fields': {
								'bill_no': {'tally': 'NAME', 'xserp': 'bill_no'},
								'amount': {'tally': 'AMOUNT', 'xserp': 'amount'},
								'bill_type': {'tally': 'BILLTYPE', 'xserp': 'bill_type'},
								'defaults': {'tally': {'TDSDEDUCTEEISSPECIALRATE': 'No'}, 'xserp': {}}
							}
						}
					}
				},
				'2.inventory_particulars': {
					'model': {'tally': 'ALLINVENTORYENTRIES.LIST', 'xserp': 'item_particulars'},
					'fields': {
						'1.item_name': {'tally': 'STOCKITEMNAME', 'xserp': 'item_name'},
						'2.is_deem_positive': {'tally': 'ISDEEMEDPOSITIVE', 'xserp': 'is_deem_positive'},
						'3.rate': {'tally': 'RATE', 'xserp': 'rate'},
						'4.discount': {'tally': 'DISCOUNT', 'xserp': 'discount'},
						'5.amount': {'tally': 'AMOUNT', 'xserp': 'amount'},
						'6.actual_qty': {'tally': 'ACTUALQTY', 'xserp': 'qty'},
						'7.bill_qty': {'tally': 'BILLEDQTY', 'xserp': 'bill_qty'},
						'defaults': {'tally': {'ISSCRAP': 'No'}, 'xserp': {}}

					},
					'relationship': {
						'batch_allocations': {
							'model': {'tally': 'BATCHALLOCATIONS.LIST', 'xserp': 'batch_wise_item_particulars'},
							'fields': {
								'1.order_no': {'tally': 'ORDERNO', 'xserp': 'order_no'},
								'2.del_note_no': {'tally': 'TRACKINGNUMBER', 'xserp': 'dc_no'},
								'3.amount': {'tally': 'AMOUNT', 'xserp': 'amount'},
								'4.actual_qty': {'tally': 'ACTUALQTY', 'xserp': 'qty'},
								'5.bill_qty': {'tally': 'BILLEDQTY', 'xserp': 'bill_qty'},
								'defaults': {'tally': {'GODOWNNAME': 'Main Location', 'BATCHNAME': 'Primary Batch'}, 'xserp': {}}
							}
						},
						'accounting_allocations': {
							'model': {'tally': 'ACCOUNTINGALLOCATIONS.LIST', 'xserp': 'account_wise_allocations'},
							'fields': {
								'1.ledger_name': {'tally': 'LEDGERNAME', 'xserp': 'ledger_name'},
								'2.is_debit': {'tally': 'ISDEEMEDPOSITIVE', 'xserp': 'is_debit'},
								'3.amount': {'tally': 'AMOUNT', 'xserp': 'amount'},
								'defaults': {'tally': {'ISPARTYLEDGER': 'No'}, 'xserp': {}}
							}
						}
					}
				}

			}
		},  # Stock Issue for XSerp application
	'7.stock_issue':
		{
			'model': {'tally': 'VOUCHER', 'xserp': 'Voucher'},
			'header_param': {
				'voucher_type': {'tally': 'VCHTYPE', 'xserp': 'type'},
				'obj_view': {'tally': 'OBJVIEW', 'xserp': 'view'},
				'action': {'tally': 'ACTION', 'xserp': ''}},
			'fields': {
				'1.date': {'tally': 'DATE', 'xserp': 'date'},
				'2.narration': {'tally': 'NARRATION', 'xserp': 'narration'},
				'4.type': {'tally': 'VOUCHERTYPENAME', 'xserp': 'type'},
				'5.voucher_no': {'tally': 'VOUCHERNUMBER', 'xserp': 'voucher_no'},
				'7.view': {'tally': 'PERSISTEDVIEW', 'xserp': 'view'},
				'8.voucher_date': {'tally': 'EFFECTIVEDATE', 'xserp': 'voucher_date'},
				'defaults': {'tally': {'DIFFACTUALQTY': 'No'}, 'xserp': {}}
			},
			'relationship': {
				'1.inventory_particulars': {
					'model': {'tally': 'INVENTORYENTRIESOUT.LIST', 'xserp': 'item_particulars'},
					'fields': {
						'1.item_name': {'tally': 'STOCKITEMNAME', 'xserp': 'item_name'},
						'2.rate': {'tally': 'RATE', 'xserp': 'rate'},
						'4.amount': {'tally': 'AMOUNT', 'xserp': 'amount'},
						'5.actual_qty': {'tally': 'ACTUALQTY', 'xserp': 'qty'},
						'6.bill_qty': {'tally': 'BILLEDQTY', 'xserp': 'bill_qty'},
						'defaults': {'tally': {'ISDEEMEDPOSITIVE': 'No'}, 'xserp': {}}
					},
					'relationship': {
						'batch_allocations': {
							'model': {'tally': 'BATCHALLOCATIONS.LIST', 'xserp': 'batch_wise_item_particulars'},
							'fields': {
								'1.order_no': {'tally': 'ORDERNO', 'xserp': 'order_no'},
								'2.del_note_no': {'tally': 'TRACKINGNUMBER', 'xserp': 'dc_no'},
								'3.amount': {'tally': 'AMOUNT', 'xserp': 'amount'},
								'4.actual_qty': {'tally': 'ACTUALQTY', 'xserp': 'qty'},
								'5.bill_qty': {'tally': 'BILLEDQTY', 'xserp': 'bill_qty'},
								'defaults': {'tally': {'GODOWNNAME': 'Main Location', 'BATCHNAME': 'Primary Batch'}, 'xserp': {}}
							}
						}
					}
				}

			}
		},  # Stock Receipt for XSerp application
	'8.stock_receipt':
		{
			'model': {'tally': 'VOUCHER', 'xserp': 'Voucher'},
			'header_param': {
				'voucher_type': {'tally': 'VCHTYPE', 'xserp': 'type'},
				'obj_view': {'tally': 'OBJVIEW', 'xserp': 'view'},
				'action': {'tally': 'ACTION', 'xserp': ''}},
			'fields': {
				'1.date': {'tally': 'DATE', 'xserp': 'date'},
				'2.narration': {'tally': 'NARRATION', 'xserp': 'narration'},
				'4.type': {'tally': 'VOUCHERTYPENAME', 'xserp': 'type'},
				'5.voucher_no': {'tally': 'VOUCHERNUMBER', 'xserp': 'voucher_no'},
				'7.view': {'tally': 'PERSISTEDVIEW', 'xserp': 'view'},
				'8.voucher_date': {'tally': 'EFFECTIVEDATE', 'xserp': 'voucher_date'},
				'defaults': {'tally': {'DIFFACTUALQTY': 'No'}, 'xserp': {}}
			},
			'relationship': {
				'1.inventory_particulars': {
					'model': {'tally': 'INVENTORYENTRIESIN.LIST', 'xserp': 'item_particulars'},
					'fields': {
						'1.item_name': {'tally': 'STOCKITEMNAME', 'xserp': 'item_name'},
						'2.rate': {'tally': 'RATE', 'xserp': 'rate'},
						'4.amount': {'tally': 'AMOUNT', 'xserp': 'amount'},
						'5.actual_qty': {'tally': 'ACTUALQTY', 'xserp': 'qty'},
						'6.bill_qty': {'tally': 'BILLEDQTY', 'xserp': 'bill_qty'},
						'defaults': {'tally': {'ISDEEMEDPOSITIVE': 'Yes'}, 'xserp': {}}
					},
					'relationship': {
						'batch_allocations': {
							'model': {'tally': 'BATCHALLOCATIONS.LIST', 'xserp': 'batch_wise_item_particulars'},
							'fields': {
								'1.order_no': {'tally': 'ORDERNO', 'xserp': 'order_no'},
								'2.del_note_no': {'tally': 'TRACKINGNUMBER', 'xserp': 'dc_no'},
								'3.amount': {'tally': 'AMOUNT', 'xserp': 'amount'},
								'4.actual_qty': {'tally': 'ACTUALQTY', 'xserp': 'qty'},
								'5.bill_qty': {'tally': 'BILLEDQTY', 'xserp': 'bill_qty'},
								'defaults': {'tally': {'GODOWNNAME': 'Main Location', 'BATCHNAME': 'Primary Batch'}, 'xserp': {}}
							}
						}
					}
				}
			}
		},
	'9.accounting_voucher':
		{
			'model': {'tally': 'VOUCHER', 'xserp': 'Voucher'},
			'header_param': {
				'voucher_type': {'tally': 'VCHTYPE', 'xserp': 'type'},
				'obj_view': {'tally': 'OBJVIEW', 'xserp': 'view'},
				'action': {'tally': 'ACTION', 'xserp': ''}},
			'fields': {
				'1.date': {'tally': 'DATE', 'xserp': 'date'},
				'2.narration': {'tally': 'NARRATION', 'xserp': 'narration'},
				'3.party_name': {'tally': 'PARTYNAME', 'xserp': 'party_name'},
				'4.type': {'tally': 'VOUCHERTYPENAME', 'xserp': 'type'},
				'5.voucher_no': {'tally': 'VOUCHERNUMBER', 'xserp': 'voucher_no'},
				'6.party_ledger_name': {'tally': 'PARTYLEDGERNAME', 'xserp': 'party_ledger_name'},
				'7.view': {'tally': 'PERSISTEDVIEW', 'xserp': 'view'},
				'8.voucher_date': {'tally': 'EFFECTIVEDATE', 'xserp': 'voucher_date'},
				'defaults': {'tally': {'DIFFACTUALQTY': 'Yes'}, 'xserp': {}}
			},
			'relationship': {
				'1.voucher_particulars': {
					'model': {'tally': 'LEDGERENTRIES.LIST', 'xserp': 'particulars'},
					'fields': {
						'1.is_debit': {'tally': 'ISDEEMEDPOSITIVE', 'xserp': 'is_debit'},
						'2.ledger': {'tally': 'LEDGERNAME', 'xserp': 'ledger_name'},
						'3.amount': {'tally': 'AMOUNT', 'xserp': 'amount'},
						'4.is_party_ledger': {'tally': 'ISPARTYLEDGER', 'xserp': 'party_ledger'},
						'defaults': {'tally': {'REMOVEZEROENTRIES': 'No'}, 'xserp': {}}
					},
					'relationship': {
						'bill_settlements': {
							'model': {'tally': 'BILLALLOCATIONS.LIST', 'xserp': 'settlements'},
							'fields': {
								'bill_no': {'tally': 'NAME', 'xserp': 'bill_no'},
								'amount': {'tally': 'AMOUNT', 'xserp': 'amount'},
								'bill_type': {'tally': 'BILLTYPE', 'xserp': 'bill_type'},
								'defaults': {'tally': {'TDSDEDUCTEEISSPECIALRATE': 'No'}, 'xserp': {}}
							}
						}
					}
				},
				'2.inventory_particulars': {
					'model': {'tally': 'ALLINVENTORYENTRIES.LIST', 'xserp': 'item_particulars'},
					'fields': {
						'1.item_name': {'tally': 'STOCKITEMNAME', 'xserp': 'item_name'},
						'2.is_deem_positive': {'tally': 'ISDEEMEDPOSITIVE', 'xserp': 'is_deem_positive'},
						'3.rate': {'tally': 'RATE', 'xserp': 'rate'},
						'4.discount': {'tally': 'DISCOUNT', 'xserp': 'discount'},
						'5.amount': {'tally': 'AMOUNT', 'xserp': 'amount'},
						'6.actual_qty': {'tally': 'ACTUALQTY', 'xserp': 'qty'},
						'7.bill_qty': {'tally': 'BILLEDQTY', 'xserp': 'bill_qty'},
						'defaults': {'tally': {'ISSCRAP': 'No'}, 'xserp': {}}

					},
					'relationship': {
						'batch_allocations': {
							'model': {'tally': 'BATCHALLOCATIONS.LIST', 'xserp': 'batch_wise_item_particulars'},
							'fields': {
								'1.order_no': {'tally': 'ORDERNO', 'xserp': 'order_no'},
								'2.del_note_no': {'tally': 'TRACKINGNUMBER', 'xserp': 'dc_no'},
								'3.amount': {'tally': 'AMOUNT', 'xserp': 'amount'},
								'4.actual_qty': {'tally': 'ACTUALQTY', 'xserp': 'qty'},
								'5.bill_qty': {'tally': 'BILLEDQTY', 'xserp': 'bill_qty'},
								'defaults': {'tally': {'GODOWNNAME': 'Main Location', 'BATCHNAME': 'Primary Batch'}, 'xserp': {}}
							}
						},
						'accounting_allocations': {
							'model': {'tally': 'ACCOUNTINGALLOCATIONS.LIST', 'xserp': 'account_wise_allocations'},
							'fields': {
								'1.ledger_name': {'tally': 'LEDGERNAME', 'xserp': 'ledger_name'},
								'2.is_debit': {'tally': 'ISDEEMEDPOSITIVE', 'xserp': 'is_debit'},
								'3.amount': {'tally': 'AMOUNT', 'xserp': 'amount'},
								'defaults': {'tally': {'ISPARTYLEDGER': 'No'}, 'xserp': {}}
							}
						}
					}
				}

			}
		}
}
