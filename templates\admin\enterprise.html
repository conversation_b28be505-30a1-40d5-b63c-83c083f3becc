{% extends 'admin/sidebar.html' %}
{% block enterprise %}
{% if access_level.view %}
<style>
	#party_failed_import_table,
	#failed_item_table,
	#failed_import_table {
		display: block;
		width: 100%;
		overflow: auto;
	}

	.savingDots {
		color:  green;
		width: 238px;
		display: block;
	}

	.savingDots span { 
		margin-top: -5px;
	}
	
	 #id_enterprise-setting_flags_2 + label,
	 #id_enterprise-setting_flags_3 + label{
		margin-left: 26px;
	}

	.checkbox-block .qtip-question-mark {
	    position: absolute;
    	margin-top: -6px;
    	margin-left: 4px;
	}

	.bulk-import.arrow_box:after, 
	.bulk-import.arrow_box:before {
		left:  169px;
	}

	.awesome-cropper .modal-dialog {
		width: 600px !important;
	}

	.awesome-cropper .cropped_image {
		max-height: 300px !important;
	}

	.awesome-cropper .modal-content,
	.awesome-cropper .modal-dialog {
		height:  auto !important;
	}

	.view-profile-container {
		border:  1px solid transparent;
	}

	.view-profile-container div.col-sm-6 {
		padding:  7px;
	}

	.view-profile-container:hover {
		border: dashed 1px #666;
		opacity: 0.9;
	}

	.hide-permanent {
		display: none !important;
	}

	.change_preference_log .modal-dialog {
		position: relative;
	    right: -360px;
	    margin-top: 120px;
	    width: 450px;
	}
	.arrow_box_log.arrow_box.change_preference_log:after,
	.arrow_box_log.arrow_box.change_preference_log:before {
		left: 78.3%;
    	top: 101px;
	}

	.arrow_box_log.arrow_box.change_log:after,
	.arrow_box_log.arrow_box.change_log:before {
		left: 39.1%;
    	top: 101px;
	}

	#tagsModal .toggle-round {
		width: 90px;
	    text-align: center;
	    display: inline-block;
	    float: left;
	    border-radius: 50px;
	    padding:  4px;
	}

	.chkcase .styled-checkbox + label:before,
	.chkcase .styled-checkbox:checked + label:before {
		border-radius:  50px;
	}

	.daterangepicker.dropdown-menu {
		z-index: 10025;
	}

	/* #tagsModal .toggle-round.on:before {
		content: "Active";
		top: 1px;
	}

	#tagsModal .toggle-round.off:before {
		content: "Inactive";
		top: 1px;
	} */

	#tagsModal input:checked ~ .toggle-round.on {
		background: #004195;
		color: #FFF;
	}

	#tagsModal input:not(:checked) ~ .toggle-round.off {
		background: #004195;
		color: #FFF;
	}

	#tagsModal .modal-body {
		max-height: 500px !important;
	}

	#tagsModal input:disabled ~ .toggle-round {
		opacity: 0.7;
		cursor: not-allowed;
	}

	#tagsModal .fa-pencil {
		padding: 1px 10px;
		border: solid 1px transparent;
		float: right; 
		font-size: 14px; 
		cursor: pointer; 
		margin-top: 2px;
	}

	#tagsModal .fa-pencil:hover {
		border: solid 1px #ddd;
		background: #eee;
		border-radius: 50px;
	}

	.td-tag-name-text input {
		width: 330px;
		float: left;
		margin-right: 10px;
	}	

	.td-tag-name-text .fa-check {
		padding: 6px 6px;
	    font-size: 20px;
	    color: green;
	    cursor: pointer;
	}

	.td-tag-name-text .fa-ban {
		font-size: 20px;
	    color: red;
	    padding: 6px 6px;
	    cursor: pointer;
	}

	.slidecontainer {
	    width: 50%;
	}

	.img-slider {
	    -webkit-appearance: none;
	    width: 100%;
	    height: 14px;
	    background: #d3d3d3;
	    outline: none;
	    opacity: 0.7;
	    -webkit-transition: .2s;
	    transition: opacity .2s;
	}

	.img-slider:hover {
        opacity: 1;
	}

	.img-slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 18px;
        height: 18px;
        background: #4CAF50;
        cursor: pointer;
	}

	.img-slider::-moz-range-thumb {
        width: 18px;
        height: 18px;
        background: #4CAF50;
        cursor: pointer;
	}

</style>

<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/plugin/image_cropper/css/imgareaselect-default.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/plugin/image_cropper/css/jquery.awesome-cropper.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/multi-select.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/registration.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/pt-sans.css?v={{ current_version }}" >
<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/enterprise.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/plugin/ck_editor/js/ckeditor.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/plugin/ck_editor/js/ckeditor_other.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/plugin/image_cropper/js/jquery.imgareaselect.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/plugin/image_cropper/js/jquery.awesome-cropper.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/multi-select.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>
<script async src="https://www.googletagmanager.com/gtag/js?id=AW-722929303"></script>
<script type="text/javascript" src="/site_media/js/Component/projects.js?v={{ current_version }}"></script>
<div class="right-content-container">
	<div class="page-title-container">
        <span class="page-title">Enterprise Profile</span>
    </div>
	<div class="container">
		<div class="page-heading_new col-sm-12 hide" style="margin-bottom: 8px;">
			{% if access_level.edit %}
				<div class="dropdown pull-right" data-tooltip="tooltip" title="Bulk upload via excel or xml">
					<button class="btn btn-add-new dropdown-toggle" id="btn-import" type="button" data-toggle="dropdown" ><i class="fa fa-upload" aria-hidden="true"></i></button>
					<ul class="dropdown-menu arrow_box bulk-import" style="margin-top: 14px; width: 200px; margin-right: -12px;">
						<li role="button" onclick="javascript:showBulkImports();"/><a>From Excel</a></li>
						<li role="button" onclick="javascript:showImportTallyXml();"/><a>From Tally XML</a></li>
					</ul>
				</div>
			{% else %}
				<div class="dropdown pull-right" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in Admin Module. Please contact the administrator.">
					<button class="btn btn-add-new dropdown-toggle disabled" style="pointer-events: none;" id="btn-import" type="button" data-toggle="dropdown" ><i class="fa fa-upload" aria-hidden="true"></i></button>
				</div>
			{% endif %}
		</div>
		<input type="hidden" id="id_page_message" value="{{message}}"/>
		<input type="hidden" id="is_new_registration" value="0"/>
		<input type="hidden" id="admin_edit_access" value="{{access_level.edit}}" />
		<div class="row">
			<div class="col-lg-12">
				<div class="col-sm-4 {% if access_level.edit %}view-profile-container{% endif %}" style="margin-top: 15px;">
					{% if access_level.edit %}
						<i class="fa fa-pencil hide" role="button" onclick="openEditableModal('profile-container')" aria-hidden="true" id="edit-profile" style="font-size: 24px; position: absolute; right: 3px; margin-top: 3px; z-index: 10025; padding: 3px;"></i>
					{% endif %}	
					<div id="enterprise-brand" class="col-sm-12" style="padding-top: 15px;">
						<img src="{{enterprise.logo.value}}" style="max-height: 150px; max-width: 150px;" >
						<h3 style="margin-top: 10px;">{{ enterprise.name.value }} <small>({{enterprise.id.value}})</small></h3>
					</div>
					<div id="cont-details" class="col-sm-12 view-address-container"  style="font-size: 16px; word-break: break-all;">
						{% if enterprise.address_1.value != "" or enterprise.address_2.value != "" %}
							{{ enterprise.address_1.value }}
							{{ enterprise.address_2.value }},<br />
						{% endif %}
						{% if enterprise.city.value != "" %}
							{{ enterprise.city.value }},
						{% endif %}
						{% if enterprise.state.value != "" %}
							{{ enterprise.state.value }},
						{% endif %}
						{% if enterprise.country_code.value != "" %}
							{% for country in country_list %}
								{% if country.country_code == enterprise.country_code.value %}
									{{ country.country_name|upper }}
								{% endif %}
							{% endfor %}
						{% endif %}<br />
						{% if enterprise.pin_code.value != "" %}
							{{ enterprise.pin_code.value }}.<br />
						{% endif %}
						{% for contact_detail in enterprise_contact_detail %}
					        {% if contact_detail.sequence_id == 1 %}
								{% if contact_detail.name != "" %}
									<b style="margin-right: 11px"><i class="fa fa-user" style="font-size:20px;" aria-hidden="true"></i></b>{{ contact_detail.name }} <br />
								{% endif %}

								{% if contact_detail.email != "" %}
									<b style="margin-right: 8px"><i class="fa fa-envelope" aria-hidden="true"></i></b>{{ contact_detail.email }} <br />
								{% endif %}

								{% if contact_detail.phone_no != "" %}
									<b style="margin-right: 12px"><i class="fa {% if contact_detail.is_whatsapp == 1 %}fa-whatsapp whatsapp-enable{% else %} fa-phone{% endif %}" aria-hidden="true"></i></b>{{ contact_detail.phone_no }} <br />
								{% endif %}

								{% if contact_detail.fax_no != "" %}
									<b style="margin-right: 8px"><i class="fa fa-fax" aria-hidden="true"></i></b>{{ contact_detail.fax_no }}
								{% endif %}
					        {% endif %}
					    {% endfor %}
					    <div class="other_contact_details_loop" style="display: none;">
						{% for contact_detail in enterprise_contact_detail %}
					        {% if contact_detail.sequence_id != 1 %}
					        <div class="other_contact_details_indv">
								{% if contact_detail.name != "" %}
									<b style="margin-right: 11px"><i class="fa fa-user" style="font-size:20px;" aria-hidden="true"></i></b>{{ contact_detail.name }} <br />
								{% endif %}

								{% if contact_detail.email != "" %}
									<b style="margin-right: 8px"><i class="fa fa-envelope" aria-hidden="true"></i></b>{{ contact_detail.email }} <br />
								{% endif %}

								{% if contact_detail.phone_no != "" %}
									<b style="margin-right: 12px"><i class="fa {% if contact_detail.is_whatsapp == 1 %}fa-whatsapp whatsapp-enable{% else %} fa-phone{% endif %}" aria-hidden="true"></i></b>{{ contact_detail.phone_no }} <br />
								{% endif %}

								{% if contact_detail.fax_no != "" %}
									<b style="margin-right: 8px"><i class="fa fa-fax" aria-hidden="true"></i></b>{{ contact_detail.fax_no }}
								{% endif %}
							</div>	
					        {% endif %}
					    {% endfor %}
						</div>
						{% if enterprise_contact_detail|length > 1 %}
							<div class="show_more_contact pull-right" onclick="showMoreContact()">Show More <i class="fa fa-chevron-down" aria-hidden="true"></i></div>
						{% endif %}	
					</div>
					<div class="col-sm-12" id="fiscal-config" style="padding-top: 15px;">
						<div {% if enterprise_reg_detail|length <= 6 %} class="registration-container-list" {% endif %}>
							{% if enterprise.fy_start_day.value != "" %}
								<div class="col-sm-12 form-group indv-registration-details" style="padding: 0 4px; margin-bottom: 8px; font-size: 16px;">
									<span class="profile-label"><b>FISCAL YEAR</b><span class="profile-colon"> : </span></span>
									{% for key, value in fiscal_year %}
										{% if key == enterprise.fy_start_day.value %}
											<span class="profile-value">{{ value }}</span>
										{% endif %}
									{% endfor %}
						        </div>
						   	{% endif %}
							{% if enterprise.home_currency_id.value != "" %}
								<div class="col-sm-12 form-group indv-registration-details" style="padding: 0 4px; margin-bottom: 8px; font-size: 16px;">
									<span class="profile-label"><b>BASE CURRENCY</b><span class="profile-colon"> : </span></span>
									<span class="profile-value {{ home_currency|lower }}">{{ home_currency }} {% if currency_symbol %} ( {{currency_symbol}} ) {% endif %} </span>
						        </div>
					       	{% endif %}
					       	<div class="clearfix" style="border-bottom: solid 1px #aaa; margin-bottom: 15px;"></div>
							{% for item in enterprise_reg_detail %}
								<div class="col-sm-12 form-group indv-registration-details" style="padding: 0 4px; margin-bottom: 8px;">
									<span class="hide">{{ item.label_id }}</span>
									<span class="profile-label"><b>{{ item.label }}</b><span class="profile-colon"> : </span></span>
									<span class="profile-value">{{ item.details }}</span>
						        </div>
							{% endfor %}
						</div>
					</div>
				</div>
				<div class="col-sm-8" style="margin-top: 20px; padding: 0;">
					<div class="col-sm-12" style="padding: 0;">
						<i class="fa fa-info pull-right" style="border: solid 1px #999;padding: 2px 6px 3px 6px; border-radius: 49px; margin-top: -8px; cursor: help; font-size: 10px;" data-tooltip="tooltip" data-placement="left" title="Tags shall be used for Document Categorization that may be based on Projects, Cost-Centers, Functions, etc" aria-hidden="true"></i>
						<button class="btn transparent-btn tour_tags" role="button" onclick="openTagModal()" style="float: right; margin-top: -5px; margin-bottom: 5px;">Projects (<span class="tag-count"></span>)</button>

					</div>

					<form id="locationForm" action="/erp/masters/location_list/" method="POST">
						{% csrf_token %}
						<input type="hidden" name="user_id" id="userId" value="">
						<input type="hidden" name="enterprise_id" id="enterpriseId" value="">

						<div class="col-sm-12" style="padding: 0;">
							<button type="button" class="btn transparent-btn tour_tags" style="float: right; margin-top: -38px; margin-bottom: 5px; margin-right: 134px;" onclick="submitFormWithUserAndEnterprise()">Locations (<span id="location-count"></span>)</button>
						</div>
					</form>
					<div {% if access_level.edit %}class='col-sm-12 view-profile-container'{% endif %} {% if access_level.edit %}role="button" onclick="openEditableModal('workflow-container')"{%endif%}  id="preferences">
						{% if access_level.edit %}
							<i class="fa fa-pencil hide"  aria-hidden="true" style="font-size: 24px; position: absolute; right: 6px; margin-top: 6px;"></i>
						{% endif %}	
						{% include "admin/xserp-flow.svg" %}
					</div>
        		</div>
			</div>
		</div>
	</div>

</div>

<div id="tagsModal" class="modal fade" role="dialog" style="z-index: 10025; margin-top: 92px; margin-left: 423px;">
  	<div class="modal-dialog" style="width: 700px;">
    	<div class="modal-content arrow_box arrow_box_tags">
      		<div class="modal-body">
      			<button type="button" class="close" data-dismiss="modal" style="margin-top: -15px;">&times;</button>
		        <table id="table-tags" class="table table-bordered" style="margin: 0;">
      				<tr class="tr-tags-add">
      					<td class="remove-right-border" style="width: 65%;">
      						<input type="text" class="form-control" id="tag_project_name" onkeyup="filterAddTagEvent(this)" placeholder="Search/ Add New Tag" maxlength="50" onkeypress="validateStringOnKeyPress(this,event,'alphaSpecialChar');" onblur="validateStringOnBlur(this,event,'alphaSpecialChar');" />
      					</td>
      					<td class="text-center remove-left-border" style="width: 35%;">
      						{% if logged_in_user|canApprove:'ADMIN' %}
      							<button class="btn btn-save add-new-tag-button" onclick="addNewTag();">+</button>
  							{% else %}
  								<button class="btn btn-save add-new-tag-button disabled" data-tooltip="tooltip" title="You do not have adequate permission to Add Tag in this Module. Please contact the administrator." data-placement="left">+</button>
      						{% endif %}	
      					</td>
      				</tr>
      				{% for project in projects %}
				        <tr class="project_{{ project.id }}" data-active='{% if project.is_active %}1{% endif %}'>
	                        <td class="td-tag-name-label remove-right-border" style="vertical-align: middle;">
	                            <span>{{project.code}} - {{project.name}}</span>
	                            {% if logged_in_user|canApprove:'ADMIN' %}
<!--	                            	<i class="fa fa-pencil hide" data-tooltip="tooltip" title="Edit" onclick="editTag(this)" aria-hidden="true"></i>-->
	                            {% else %}
<!--	                            	<i class="fa fa-pencil hide disabled" data-tooltip="tooltip" title="You do not have adequate permission to Edit Tag in this Module. Please contact the administrator."></i>-->
	                            {% endif %}	
	                        </td>
	                        <td class="td-tag-name-text remove-right-border hide" >
	                            <input type="text" class="form-control" name="project_name" value="" maxlength="50" onkeypress="validateStringOnKeyPress(this,event,'alphaSpecialChar');" onblur="validateStringOnBlur(this,event,'alphaSpecialChar');">
	                            <i class="fa fa-check" data-tooltip="tooltip" title="Update" onclick="updateTag(this)" aria-hidden="true"></i>
	                            <i class="fa fa-ban" data-tooltip="tooltip" title="Cancel" onclick="cancelEditTag(this)" aria-hidden="true"></i>
	                        </td>
	                        {% if logged_in_user|canApprove:'ADMIN' %}
		                        <td class="text-center remove-left-border">
									<label role='button' style="border: solid 1px #004195; border-radius: 50px;">
								  		<input class='hide' type="checkbox" name="is_active"  {% if project.is_active %} checked {% endif %} />
								  		<span class="toggle-round round on" onClick="tagChangeEvent(this)">Active</span>
								  		<span class="toggle-round round off" onClick="tagChangeEvent(this)">Inactive</span>
									</label>
		                        </td>
		                    {% else %}
		                    	 <td class="remove-left-border" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to Enable/ Disable Tag in this Module. Please contact the administrator.">
		                            <label role='button' style="border: solid 1px #004195; border-radius: 50px;">
								  		<input class='hide' type="checkbox" name="is_active" disabled="" {% if project.is_active %} checked {% endif %} />
								  		<span class="toggle-round round on" >Active</span>
								  		<span class="toggle-round round off" >Inactive</span>
									</label>
		                        </td>
		                    {% endif %}    
					        <td hidden="hidden">
						         <input type="hidden" name="project_code" value="{{ project.id }}" />
					        </td>
	                    </tr>
			        {% endfor %}
      			</table>
		  		
      		</div>
    	</div>
  	</div>
</div>
<div id="modal_project_creation" class="modal fade" role="dialog" tabindex='-1'>
	<div class="modal-dialog" >
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">ADD NEW PROJECTS</h4>
			</div>
			<div class="modal-body">
				<div id="account_creation" class="material_txt ">
					<form id="account_group_form" method="POST" action="/erp/accounts/add_group/">
						{% csrf_token %}
						<div class="row">
							<div class="form-group intent_for col-sm-12">
						        <label>Project Head<span class="mandatory_mark"> *</span></label>
								<select class="form-control" id="projectSelect">
									<option value="">Select Project</option>
								</select>
					        </div>
							<div class="form-group col-sm-12">
								<label>Name<span class="mandatory_mark"> *</span></label>
								<input class="form-control" id="id_project_group-name" maxlength="100" onkeypress="return validateStringOnKeyPress(this,event, &quot;alphaSpecialChar&quot;);" onblur="return validateStringOnBlur(this, event, &quot;alphaSpecialChar&quot;);" placeholder="Provide Project Head name" type="text">
							</div>
							<div class="form-group col-sm-12">
								<label>Project code<span class="mandatory_mark"> *</span></label>
								<input class="form-control" id="id_project_code" maxlength="100" onkeypress="return validateStringOnKeyPress(this,event, &quot;alphaSpecialChar&quot;);" onblur="return validateStringOnBlur(this, event, &quot;alphaSpecialChar&quot;);" placeholder="Provide Project Code" type="text">
							</div>
							<div class="form-group col-sm-12">
								<label>Description</label>
								<input class="form-control" id="id_project_description" maxlength="100" onkeypress="return validateStringOnKeyPress(this,event, &quot;alphaSpecialChar&quot;);" onblur="return validateStringOnBlur(this, event, &quot;alphaSpecialChar&quot;);" placeholder="Provide Project Description" type="text">
							</div>
						</div>
						<div class="modal-footer">
							{% if access_level.edit %}
							<input type="button" class="btn btn-save" id="save_project_group" value="Save"/>
							{% endif %}
							<input type="submit" class="btn btn-cancel" data-dismiss="modal" value="Close"/>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>

<div id="bulkImports" class="modal fade" role="dialog" style="z-index: 10025;">
  	<div class="modal-dialog">
    	<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Bulk imports</h4>
      		</div>
      		<div class="modal-body">
		  		<div class="alert alert-warning">
		  			<h4>Warning:</h4>
			  		<h6>1. Workbook must have the following sheet names such as materials,party and ledger.! </h6>
			  		<h6>2. Sheetnames are case sensitive .So all must be in smaller cases ! </h6>
				</div>
		  		<form id="bulk_file" onsubmit="return false;" method="post" enctype="multipart/form-data">{% csrf_token %}
	      			<table id="bulk_import_table" cellpadding="2" align="center" cellspacing="0" border="0" class="table table-bordered custom-table table-striped">
						<tr>
							<td>
								<b>Please Select the bulk import File </b>
								<a class="pull-right" href="/site_media/docs/sample_bulk_import.xlsx" >Download Sample</a>
							</td>
						</tr>
						<tr>
							<td >
								<input class="filestyle col-sm-12" data-buttonBefore="true" required="true" name="bulkfile" type="file" id="bulkfileUpload" style="width: 100%" onchange="validateExcelFileFormat(this);" />
							</td>
						</tr>
		    			<tr>
			    			<td colspan="2" class="text-right">
							    {% if access_level.edit %}
					    		<button id="xlhideUploadButton" class="btn btn-save">Upload</button>
					    		{% else %}
			    				<div data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to import data. Please contact the administrator." class="btn btn-save" style="cursor: not-allowed; opacity: 0.5;">Upload</div>	
							    {% endif %}
								<a href="#" class="btn btn-cancel" id="xlhideUpload" height="40" data-dismiss="modal">Cancel</a>
							</td>
		    			</tr>
					</table>
	    		</form>
      		</div>
    	</div>
  	</div>
</div>

<div id="ImportTallyXml" class="modal fade" role="dialog" style="z-index: 10025;">
  	<div class="modal-dialog">
    	<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Import Tally XML</h4>
      		</div>
      		<div class="modal-body">
		        <div class="form-group col-sm-4 remove-padding pull-right">
			        <label style="width: 160px;">Opening Date</label>
			        <input type="text" name="opening_date" id="opening_date" class="form-control hide" placeholder="Select Date" />
			        <input type="text" class="form-control custom_datepicker full-datepicker fixed-width-medium" placeholder="Select Date" name="opening_date_display" id="opening_date_display" readonly="readonly">
					<i class="glyphicon glyphicon-calendar custom-calender-icon"></i>
				</div>
		        <div class="tallyunprocessfile hide" id="tallyunprocessfile">
				    <table id="tally_xml_un_process_table" cellpadding="2" align="center" cellspacing="0" border="0" class="table table-bordered custom-table table-striped">
	                    <thead>
							<tr>
								<td colspan="2"><b>Unprocessed tally xml files</b></td>
							</tr>
						</thead>
						<tbody>
						</tbody>
					</table>
				</div>
		  		<form id="tally_xml_file" onsubmit="return false;" method="post" enctype="multipart/form-data">{% csrf_token %}
				    <table id="tally_xml_import_table" cellpadding="2" align="center" cellspacing="0" border="0" class="table table-bordered custom-table table-striped">
	      				<thead>
							<tr>
								<td colspan="2"><b>Please select the tally xml import file</b></td>
							</tr>
						</thead>
						<tbody>
							<tr class="tr_tally_import">
								<td>
									<input class="filestyle col-sm-12" data-buttonBefore="true" name="tallyxmlfile[]" type="file" id="tallyxmlfileUpload"  onchange="validateFileFormat(this);" />
								</td>
								<td class="text-center" style="width: 60px;">									
								</td>
							</tr>
						</tbody>
						<tfoot>
			    			<tr class="tr_tally_import_button">
				    			<td colspan="2" class="text-right">
						    		<span onclick="addNewImportTally();" class="btn btn-add-new" style="padding: 2px 8px; font-size: 12px;">+ Add More</span>
								</td>
			    			</tr>
			    		</tfoot>
					</table>
					<div class="text-right">
						{% if access_level.edit %}
			    			<button id="tallyxmlhideUploadButton" class="btn btn-save">Upload</button>
			    		{% else %}
			    			<div data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to import data. Please contact the administrator." class="btn btn-save" style="cursor: not-allowed; opacity: 0.5;">Upload</div>	
						{% endif %}
						<a class="btn btn-cancel" id="tallyhideUpload" onclick="closeTallyxmlUpload();">Cancel</a>
					</div>
	    		</form>
	    		<div class="tallyImportProgress hide">
				    <div class="processing" style="font-size: 20px;"> 
				    	<span class="savingDots" style="color: green;">Processing. Please wait<span>.</span><span>.</span><span>.</span></span>
				    </div>
				    <div class="tallyImportProgreeText">
				    	<textarea class="txttallyImportProgreeText" id="txttallyImportProgreeText" style="width: 100%; height: 150px; resize: none;" readonly></textarea>
				    </div>
				    <div class="text-right">
						<a class="btn btn-save hide" id="tallyhidedone" >Finish</a>
					</div>
				</div>
      		</div>
    	</div>
  	</div>
</div>


<div id="exportTallyData" class="modal fade" role="dialog" style="z-index: 10025;">
  	<div class="modal-dialog modal-lg">
    	<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Export to Tally</h4>
      		</div>
      		<div class="modal-body">	
		        <div>
		        	<h4>Transaction Data</h4>
		        	<div class="col-sm-6 form-group" >
						<label>For Period</label>
						<div id="reportrange" class="report-range form-control" style="max-width: 300px;">
							<i class="glyphicon glyphicon-calendar"></i>&nbsp;
							<span></span> <b class="caret"></b>
							<input type="hidden" class="fromdate" id="from_date" name="from_date" value=""/>
							<input type="hidden" class="todate" id="to_date" name="to_date" value=""/>
						</div>
						<div style="margin-top: 10px;">
								First Transaction Date <b id="first_trasaction_date" style="padding-left: 13px;"></b>
	                        	<br />
	                        	Book Closed on <b id="previous_closure_date" style="padding-left: 50px;"></b>
	                        </div>
					</div>
					<div class="col-sm-6">
						<input type="checkbox" class="chkcase styled-checkbox" name="inventory_trasaction" id="inventory_trasaction" />
						<label for="inventory_trasaction">Include Inventory Trasaction</label><br /><br />
						<input type="checkbox" class="chkcase styled-checkbox" name="closing_balance_as_opening" id="closing_balance_as_opening" />
						<label for="closing_balance_as_opening">Closing Balance As Opening</label>
					</div>
					<div class="col-sm-12">
						<div class='general-warning'>
							<h4>Note</h4>
							<ul>
								<li>Only approved vouchers will be exported</li>
								<li><b>Include Inventory Transaction</b> - When checked, the material stock with the transaction in the selected date range will be exported</li>
								<li><b>Closing Balance As Opening</b> - Stock of the available materials and ledger opening balance before the selected date range will be included</li>
							</ul>
						</div>
					</div>
				</div>
      		</div>
      		<div class="modal-footer">
		        <button type="button" class="btn btn-default" data-dismiss="modal" onClick="cancelExportToTally()">Cancel</button>
		        {% if access_level.edit %}
		        	<button type="button" class="btn btn-save" onClick="exportAsTallyXML()">Export</button>
	        	{% else %}
	        		<button type="button" class="btn btn-save" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to export data. Please contact the administrator." style="cursor: not-allowed; opacity: 0.5;">Export</button>
	        	{% endif %}
	      	</div>
    	</div>
  	</div>
</div>

<div id="exportTallyStatus" class="modal fade" role="dialog" style="z-index: 10025;">
  	<div class="modal-dialog">
    	<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Export Status</h4>
      		</div>
      		<div class="modal-body">
				<div>
					<ul class="export-status-container">
					</ul>
				</div>
      		</div>
      		<div class="modal-footer">
		        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
	      	</div>
    	</div>
  	</div>
</div>


<div id="bulk_import_status_modal" class="modal fade" role="dialog" data-backdrop="static" data-keyboard="false" style="z-index: 10040;">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Bulk import status</h4>
			</div>
			<div class="modal-body">
				<button class="btn btn-add-new pull-right" onclick="tablesToExcelLoad();" data-tooltip="tooltip" data-placement="left" title="Download failed items (as XLS)"><i class="fa fa-download" aria-hidden="true"></i> &nbsp; as xls</button>
				<h4>
					<div>
						<h4 id="import_message"></h4>
					</div>
				</h4>
				<h4>
					<div id="material_status"></div>
				</h4>
					{% include "admin/material_failure.html" %}
					<br>
				<h4>
					<div id="ledger_status"></div>
				</h4>
				{% include "admin/ledger_failure.html" %}
				<br>
				<h4>
					<div id="party_status"></div>
				</h4>
				{% include "admin/party_failure.html" %}
			</div>
		</div>
	</div>
</div>

<div id="edit_enterprise_modal" class="modal fade modal-fullscreen" role="dialog" data-backdrop="static" data-keyboard="false">
  	<div class="modal-dialog" style="width: 100%; margin: 0;">
    	<div class="modal-content" style="height: 100%; border-radius: 0;">
      		<div class="modal-header form-register" style="max-width: 1280px; width: 85%; margin: 0 auto; border: none;">
      			<button type="button" class="enterprice-modal-close close" data-dismiss="modal" style="position: absolute; right: 15px; font-size: 36px; color: #999; opacity: 0.8;">&times;</button>
      		</div>
      		<div class="modal-body" style="max-width: 1280px; width: 85%; margin: 0 auto; max-height: initial; padding-top: 30px;">
      			<div class="row profile-container editable-container">
      				<div class="welcome-text hide" style="margin-bottom: 30px; margin-top: -30px;">Complete your Registration by filling the below details.</div>
      				<div class="col-sm-8" style="margin-top: -15px;">
      					<div class="material_txt">
							<i class="fa fa-times remove_image hide" aria-hidden="true"></i>
							<div class="btn profile_image" role="button" style="width: 100px;height: 100px;border: dashed 1px #ccc; padding-top: 25px;">
								<i class="fa fa-upload" aria-hidden="true"></i>
									<br/>Logo
							</div>
							{{enterprise.logo}}
							<div class="profile_image upload_camera hide">
								<i class="fa fa-camera fa-4" aria-hidden="true"></i>
							</div>
						</div>
      					<span class="enterprise-name-label" style="font-size:24px; cursor: pointer; display: block; margin-bottom: 7px;">
				      		{{ enterprise.name.value }}
				      		<small style="font-size:12px;">(<span class='enterprise-code'>{{ enterprise.id.value }}</span>)</small>
				      		<i class="fa fa-pencil" style="visibility: hidden; font-size: 20px;" aria-hidden="true"></i>
				      	</span>
						<div class="floating-label enterprise-name-container" style="display: none;">      
				      		<span style="position: absolute; right: 0; margin-top: 10px; margin-right: 5px; color: #209be1;">ID: {{ enterprise.id.value }}</span>
				      		{{ enterprise.name }}
				      		{{ enterprise.code }}{{ enterprise.id }}
							<input type="hidden" class="form-control" readonly="readonly" value="{{ enterprise.code.value }}" />
					    </div>
      				</div>
      				<div class="col-sm-4" style="margin-top: -12px;">
	        			<label class="enterprice-item-heading">Preference</label>
						<div class="row" style="margin: 0; padding: 35px 0 0 15px;  border: solid 1px #004195; margin-top: -30px; margin-bottom:15px;">
							<div class="col-sm-6 floating-label" style="padding-left: 0">
								{{ enterprise.fy_start_day }}
								<label>Fiscal Year</label>
							</div>
							<div class="floating-label col-sm-6" style="padding-left:0;">
								{{ enterprise.home_currency_id }}
								<label>Home Currency</label>
							</div>
						</div>
	        		</div>
	        		<div class="clearfix"></div>
	        		<div class="col-sm-8" style="margin-top: 0px;">
					    <label class="enterprice-item-heading">Primary Contact Details</label>
					    <div class="row address-container">
							<div class="floating-label col-sm-12 address-container-address remove-padding">
								{{ enterprise.address_1 }}
								<span class="hide">{{ enterprise.address_2 }}</span>
<!--								{{ enterprise.address }}-->
								<label>Address<span class="mandatory_mark"> *</span></label>
							</div>
							<div class="floating-label col-sm-6 address-container-country" style="padding-left: 0;">
								<input type="hidden" id="enterprise_country_code" value="{{enterprise.country_code.value}}">
								<select class="form-control" id="id_country-__prefix__-make_choices" onchange="updateCountryValue('onchange')" style="border: none; border-bottom: solid 1px #DDD; box-shadow: 0 0; border-radius: 0; padding: 0;">
									{% for country in country_list %}
										<option value="{{ country.country_code }}" {% if country.country_code|upper == enterprise.country_code.value|upper %} selected {% endif %}>{{ country.country_name }}</option>
									{% endfor %}
								</select>
								<label style="top: -18px; color: #777; left: 4px; font-size: 11px;">Country<span class="mandatory_mark"> *</span></label>
							</div>
							<div class="floating-label col-sm-6 address-container-state-list" style="padding-left: 0;">
								<input type="hidden" id="enterprise_state_code" value="{{enterprise.state.value}}" >
								<select class="form-control" id="id_state-__prefix__-make_choices" onchange="updateStateValue()" style="border: none; border-bottom: solid 1px #DDD; box-shadow: 0 0; border-radius: 0; padding: 0;">
									{% for state in state_list %}
										<option value="{{ state.code}}" {% if state.description|upper == enterprise.state.value|upper %} selected {% endif %}>{{ state.description }}</option>
									{% endfor %}
								</select>
								<label style="top: -18px; color: #777; left: 4px; font-size: 11px;">State<span class="mandatory_mark"> *</span></label>
							</div>
							<div class="floating-label col-sm-6 address-container-state-text" style="padding-left: 0;">
								{{ enterprise.state }}
								<label>State<span class="mandatory_mark"> *</span></label>
							</div>
							<div class="floating-label col-sm-6 address-container-city" style="padding-left: 0;">
								{{ enterprise.city }}
								<label>City<span class="mandatory_mark"> *</span></label>
							</div>
							<div class="floating-label col-sm-6 address-container-pin-code" style="padding-left: 0; padding-right: 0;">
								{{ enterprise.pin_code }}
								<label>Pincode<span class="mandatory_mark"> *</span></label>
							</div>
							<div class="col-sm-12 line-divider"></div>
						    {% if enterprise_contact_detail %}
							    {% for contact_detail in enterprise_contact_detail %}
							        {% if contact_detail.sequence_id == 1 %}
										<div class="floating-label col-sm-6" style="padding-left: 0;">
											<input class="form-control floating-input" id="id_enterprise-contact_person" maxlength="50" name="enterprise-contact_person"
											       onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');"
											       placeholder=" " type="text" value="{{ contact_detail.name }}">
											<label>Contact Person<span class="mandatory_mark"> *</span></label>
										</div>
										<div class="floating-label col-sm-6" style="padding-left: 0; padding-right: 0;">
											<input class="form-control floating-input" id="id_enterprise-email" maxlength="75" name="enterprise-email"
											       onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');"
											       placeholder=" " type="text" value="{{ contact_detail.email }}">
											<label>Email Address<span class="mandatory_mark"> *</span></label>
										</div>
										<div class="floating-label col-sm-6" style="padding-left: 0;">
			                                <i class="fa fa-whatsapp {% if contact_detail.is_whatsapp == 1 %}whatsapp-enable{% endif %} primary_whatapp" role="button" data-tooltip="tooltip" title="{% if contact_detail.is_whatsapp == 1 %}Unauthorize{%else%}Authorize{% endif %} this phone number to use XSassist Whatsapp Support" onclick="whatsappContactDetails(this)"></i>
											<input class="form-control floating-input" id="id_enterprise-phone" maxlength="30" name="enterprise-phone" onblur="return validateStringOnBlur(this, event, 'ph_number');"
											       onkeypress="return validateStringOnKeyPress(this,event, 'ph_number');" placeholder=" " type="text" value="{{ contact_detail.phone_no }}">
			                                    <label>Contact Number<span class="mandatory_mark"> *</span></label>
		                                        <span style="font-size: 11px;" class="contact-authorized {% if contact_detail.is_whatsapp == 0 %}hide{% endif %}">Contact authorized to use XSassist Whatsapp support</span>
										</div>
										<div class="floating-label col-sm-6" style="padding-left: 0; padding-right: 0;">
											<input class="form-control floating-input" id="id_enterprise-fax" maxlength="30" name="enterprise-fax" onblur="return validateStringOnBlur(this, event, 'ph_number');"
											       onkeypress="return validateStringOnKeyPress(this,event, 'ph_number');" placeholder=" " type="text" value="{{ contact_detail.fax_no }}">
											<label>Fax</label>
										</div>
							        {% endif %}
							    {% endfor %}
							    <div class="other_contact_details">
						        {% for contact_detail in enterprise_contact_detail %}
							        {% if contact_detail.sequence_id != 1 %}
							            <div class="contact-details" data-sequence-id="{{contact_detail.sequence_id}}">
								            <div class="col-sm-12 line-divider">
								            	<lable style="color: #209be1;font-size: 13px; margin-left: 17px; padding-top: 8px; display: block;">OTHER CONTACT DETAILS</lable>
		                                        <i class="fa fa-minus-circle delete-contact-container" role="button" onclick="deleteContactDetails(this)"></i>
											</div>
											<div class="floating-label col-sm-6" style="padding-left: 0;">
												<input class="form-control floating-input contact-person-name" id="id_enterprise-contact_person_{{ contact_detail.sequence_id }}" maxlength="50" name="enterprise-contact_person"
												       onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');"
												       placeholder=" " type="text" value="{{ contact_detail.name }}">
												<label>Contact Person<span class="mandatory_mark"> *</span></label>
											</div>
											<div class="floating-label col-sm-6" style="padding-left: 0; padding-right: 0;">
												<input class="form-control floating-input contact-person-email" id="id_enterprise-email_{{ contact_detail.sequence_id }}" maxlength="75" name="enterprise-email"
												       onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');"
												       placeholder=" " type="text" value="{{ contact_detail.email }}">
												<label>Email Address</label>
											</div>
											<div class="floating-label col-sm-6" style="padding-left: 0;">
				                                <i class="fa fa-whatsapp  {% if contact_detail.is_whatsapp == 1 %}whatsapp-enable{% endif %}" role="button" data-tooltip="tooltip" title="{% if contact_detail.is_whatsapp == 1 %}Unauthorize{%else%}Authorize{% endif %} this phone number to use XSassist Whatsapp Support"  onclick="whatsappContactDetails(this)" ></i>
												<input class="form-control floating-input contact-person-number" id="id_enterprise-phone_{{ contact_detail.sequence_id }}" maxlength="30" name="enterprise-phone" onblur="return validateStringOnBlur(this, event, 'ph_number');"
												       onkeypress="return validateStringOnKeyPress(this,event, 'ph_number');" placeholder=" " type="text" value="{{ contact_detail.phone_no }}">
				                                    <label>Contact Number<span class="mandatory_mark"> *</span></label>
													<span style="font-size: 11px;" class="contact-authorized {% if contact_detail.is_whatsapp == 0 %}hide{% endif %}">Contact authorized to use XSassist Whatsapp support</span>
											</div>
											<div class="floating-label col-sm-6" style="padding-left: 0; padding-right: 0;">
												<input class="form-control floating-input contact-person-fax" id="id_enterprise-fax_{{ contact_detail.sequence_id }}" maxlength="30" name="enterprise-fax" onblur="return validateStringOnBlur(this, event, 'ph_number');"
												       onkeypress="return validateStringOnKeyPress(this,event, 'ph_number');" placeholder=" " type="text" value="{{ contact_detail.fax_no }}">
												<label>Fax</label>
											</div>
							            </div>
							        {% endif %}
							    {% endfor %}
								</div>
						    {% else %}
						        <div class="floating-label col-sm-6" style="padding-left: 0;">
									<input class="form-control floating-input" id="id_enterprise-contact_person" maxlength="50" name="enterprise-contact_person"
									       onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');"
									       placeholder=" " type="text" value="">
									<label>Contact Person<span class="mandatory_mark"> *</span></label>
								</div>
								<div class="floating-label col-sm-6" style="padding-left: 0; padding-right: 0;">
									<input class="form-control floating-input" id="id_enterprise-email" maxlength="75" name="enterprise-email"
									       onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');"
									       placeholder=" " type="text" value="{{enterprise.email.value}}">
									<label>Email Address<span class="mandatory_mark"> *</span></label>
								</div>
								<div class="floating-label col-sm-6" style="padding-left: 0;">
	                                <i class="fa fa-whatsapp {% if contact_detail.is_whatsapp == 1 %}whatsapp-enable{% endif %} primary_whatapp" role="button" data-tooltip="tooltip" title="{% if contact_detail.is_whatsapp == 1 %}Unauthorize{%else%}Authorize{% endif %} this phone number to use XSassist Whatsapp Support" onclick="whatsappContactDetails(this)"></i>
									<input class="form-control floating-input" id="id_enterprise-phone" maxlength="30" name="enterprise-phone" onblur="return validateStringOnBlur(this, event, 'ph_number');"
									       onkeypress="return validateStringOnKeyPress(this,event, 'ph_number');" placeholder=" " type="text" value="{{enterprise.phone.value}}">
	                                    <label>Contact Number<span class="mandatory_mark"> *</span></label>
										<span style="font-size: 11px;" class="contact-authorized hide">Contact authorized to use XSassist Whatsapp support</span>
								</div>
								<div class="floating-label col-sm-6" style="padding-left: 0; padding-right: 0;">
									<input class="form-control floating-input" id="id_enterprise-fax" maxlength="30" name="enterprise-fax" onblur="return validateStringOnBlur(this, event, 'ph_number');"
									       onkeypress="return validateStringOnKeyPress(this,event, 'ph_number');" placeholder=" " type="text" value="">
									<label>Fax</label>
								</div>
								<div class="other_contact_details">

								</div>
						    {%endif %}
							<div class="col-sm-12 text-right remove-padding">
								<span role="button" onclick="addNewContact(this)"><a>+ Add Contact</a></span>
							</div>
						</div>
						<div class="other_address_details"></div>
						<div class="col-sm-12 text-right remove-padding hide" style="margin-top: -10px;">
							<span role="button" onclick="addNewAddress()"><a>+ Add Address</a></span>
						</div>
	        		</div>
	        		<div class="col-sm-4">
						<div class="row registration-container" style="margin: 0;">
			                <label class="enterprice-item-heading">Registration Details</label>
						    <div class="row" style="margin: 0; padding: 35px 15px 15px; border: solid 1px #209be1; margin-top: -30px; margin-bottom: 15px;">
						        <div class="registration-extra-details">
									{% for reg_detail in enterprise_reg_detail %}
							            {% if reg_detail.label == "GSTIN" %}
									        <div class="col-sm-12 form-group registration-details" style="padding: 0;">
										        <input type="text" class="form-control registration-label-id hide" value="1" />
										        <input type="text" class="form-control registraion-key" maxlength="28"  onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" value="{{ reg_detail.label }}" placeholder="Label" readonly="" />
										        <input type="text" class="form-control registraion-value" id="enterprise_gstin_number" maxlength="30" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" value='{{ reg_detail.details }}'  placeholder="Detail" />
									        </div>
							            {% endif %}
							        {% endfor %}
							        {% for reg_detail in enterprise_reg_detail %}
							            {% if reg_detail.label == "PAN" %}
									        <div class="col-sm-12 form-group registration-details" style="padding: 0;">
										        <input type="text" class="form-control registration-label-id hide" value="2" />
										        <input type="text" class="form-control registraion-key" maxlength="28"  onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" value="{{ reg_detail.label }}" placeholder="Label" readonly="" />
										        <input type="text" class="form-control registraion-value" id="enterprise_pan_number" maxlength="30" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" value='{{ reg_detail.details }}'  placeholder="Detail" />
									        </div>
							            {% endif %}
									{% endfor %}
								    {% for reg_detail in enterprise_reg_detail %}
							            {% if reg_detail.label != "GSTIN" and reg_detail.label != "PAN" %}
									        <div class="col-sm-12 form-group registration-details" style="padding: 0 4px;">
												<input type="text" class="form-control registration-label-id hide" value="{{ reg_detail.label_id }}" />
									            <input type="text" class="form-control registraion-key" maxlength="30"  onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" value="{{ reg_detail.label }}" placeholder="key" />
									            <input type="text" class="form-control registraion-value" maxlength="150" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" value="{{ reg_detail.details }}"  placeholder="value" />
									            <i class="fa fa-minus-circle" role="button" onclick="deleteRegistraionDetails(this)" style="color: #dd4b39;position: absolute; top: 10px; right: 15px;"></i>
									        </div>
							            {% endif %}
								    {% endfor %}
							    </div>
							    <div class="col-sm-12 remove-padding">
							    	<span role="button" style="float: right; margin-right: 12px;" onclick="addNewRegistrationDetails(this)"><a>+ Add</a></span>
							    </div>
				            </div>
			            </div>
	        		</div>

	        		<div class="clearfix"></div>
	        		<div class="col-sm-12 text-right">
	        			<div class="col-sm-12 text-right next-slide-btn hide">
		        			<button type="button" style="padding: 14px 22px; font-size:24px; outline: none;" class="btn btn-next" data-next-container="financial-container" onclick="saveProfile(this);" >
		        				<i class="fa fa-arrow-right" aria-hidden="true"></i>
		        			</button>
		        		</div>
		        		<div class="col-sm-12 text-right save-current-btn hide">
		        			<button type="button" class="btn btn-save" onclick="saveProfile(this);" >
		        				Save
		        			</button>
		        		</div>
	        		</div>
	        	</div>
	        	<div class="row financial-container editable-container" style="margin: 0; display: none;">
	        		{% include "admin/preference_settings.html" %}
	        		<div class="skip-preference-settings pull-right hide" onclick="skipConversation();"><a role="button">Skip this conversation</a></div>
	        	</div>
        		<div class="row workflow-container editable-container" style="margin: 0; display: none;">
        			{% include "admin/xserp-flow.svg" %}
        			<div class="col-sm-12 text-right next-slide-btn hide">
						<button type="button" style="padding: 14px 22px; font-size:24px; outline: none; margin-top: 15px;" class="btn btn-next" data-next-container="import-container" onclick="savePreferenceFromSVG(this);" >
							<i class="fa fa-arrow-right" aria-hidden="true"></i>
						</button>
					</div>
					<div class="col-sm-12 text-right save-current-btn hide">
						<button type="button" class="btn btn-save" onclick="savePreferenceFromSVG(this);">
							Save
						</button>
					</div>

        		</div>
        		<div class="row import-container editable-container" style="margin: 0; display: none;">
        			<div class="text-center">
	        			<h3>You can import your data here</h3>
	        			<div style="margin-top: 50px;">
	        				<span class="overlay-import-btn" style="border: solid 1px #209be1; padding: 15px 45px; font-size: 24px; border-radius: 10px 0 0 10px;" role="button" onclick="javascript:showBulkImports();"/><a>From Excel</a></span>
							<span class="overlay-import-btn" style="border: solid 1px #209be1; padding: 15px 45px; font-size: 24px; border-radius: 0 10px 10px 0; margin-left: -4px;" role="button" onclick="javascript:showImportTallyXml();"/><a>From Tally XML</a></span>
	        			</div>
	        		</div>
	        		<div class="col-sm-12 text-right skip-upload">
						<button type="button" class="btn btn-save" onclick="skipUpload()">
							Finish
						</button>
					</div>
	        	</div>
	        	<div class="row export-container editable-container" style="margin: 0; display: none;">
	        		<div class="">
	        			<div class="text-right">
	        				<span class="overlay-import-btn" style="border: solid 1px #209be1; padding: 8px 30px; font-size: 24px; border-radius: 10px; margin-top: -30px; display: inline-block;" role="button" onclick="javascript:showTallyExport();"/><a>Export to Tally</a></span>
	        			</div>
						<table class="table table-bordered table-striped custom-table" id="export_history" style="width: 100%;">
							<thead>
								<tr>
									<th style="width:60px;">S.No</th>
									<th>Initiated On</th>
									<th>Initiated By</th>
									<th>Export Criteria</th>
									<th style="max-width: 150px;">Status</th>
									<th hidden="hidden"></th>
								</tr>
							</thead>
							<tbody>
							</tbody>
						</table>
					</div>
	        	</div>
	            <div class="row erase-container editable-container" style="margin: 0; padding-bottom: 100px; display: none;">
                    <div class="text-center">
	                    <h2>Erase Your Data</h2>
	                    <div style="margin-top: 50px;">
	                        <span class="overlay-import-btn" data-tooltip="tooltip" title="All transaction data will be removed. All Master profiles will be retained, allowing a Reboot of operations with the Master profiles retained as the base." style="border: solid 1px #209be1; padding: 15px 45px; font-size: 24px; border-radius: 50px 0 0 50px; display: inline-block; min-width: 300px;" role="button" onclick="javascript:clearTransaction('Transaction');"/>
	                            <a>Clear All Transactions</a>
					            {% if erase_log %}
						            {% for data in erase_log %}
			                            {% if data.erase_type == "Transaction" %}
			                                <small style="font-size: 12px;display: block;position: absolute;margin-top: 20px;max-width: 250px;">Transactions last cleared by <b>{{data.modifier.first_name}} {{data.modifier.last_name}}</b> on <b>{{data.erased_on}}</b></small>
			                            {% endif %}
						            {% endfor %}
					            {% endif %} 
	                        </span>
							<span class="overlay-import-btn" data-tooltip="tooltip" title="Any data created or uploaded will be removed & the Enterprise will be restored to a Clean Initial state, allowing a Reboot of setup & operations with entirely fresh feed of Data." style="border: solid 1px #209be1; padding: 15px 45px; font-size: 24px; border-radius: 0 50px 50px 0; margin-left: -4px; display: inline-block; min-width: 300px;" role="button" onclick="javascript:clearTransaction('Reset');"/>
								<a>Reset All Data</a>
					            {% if erase_log %}
						            {% for data in erase_log %}
										{% if data.erase_type == "Reset" %}
											<small style="font-size: 12px;display: block;position: absolute;margin-top: 20px;max-width: 200px;">System was last reset by <b>{{data.modifier.first_name}} {{data.modifier.last_name}}</b> on <b>{{data.erased_on}}</b></small>
										{% endif %}
						            {% endfor %}
					            {% endif %}
							</span>
	                    </div>
	                </div>
	            </div>
      		</div>
    	</div>
  	</div>
</div>
<div class="hide hidden_setting_flags" style="margin-left: 100px;">
	{{ enterprise.setting_flags }}
	{{ enterprise.is_negative_stock_allowed }}
	{{ enterprise.is_gate_inward_no_mandatory }}
	{{ enterprise.gate_inward_no_flags}}
	{{ enterprise.is_purchase_order_mandatory }}
	{{ enterprise.is_delivery_schedule }}
	{{ enterprise.is_multiple_units }}
	{{ enterprise.is_blanket_po }}
	{{ enterprise.is_icd_request_acknowledgement }}
</div>
<div id="exp_configration_modal" class="modal fade modal-fullscreen" role="dialog">
  	<div class="modal-dialog" style="width: 100%; margin: 0;">
    	<div class="modal-content" style="height: 100%; border-radius: 0;">
      		<div class="modal-header form-register" style="max-width: 1380px; width: 90%; margin: 0 auto; border: none;">
      			<button type="button" class="close" data-dismiss="modal" style="position: absolute; right: 15px; font-size: 36px; color: #999; opacity: 0.8;">&times;</button>
      			<h3>Expense Configuration</h3>
                <div class="row form-group">
					<div class="col-sm-6">
						<label>Expense Payable</label>
						{{ enterprise.claim_heads }}
					</div>
					<div class="col-sm-6">
						<label>Expense Accounts</label>
						{{ enterprise.expense_heads }}
					</div>
					<div class="col-sm-12" style="color: #dd4b39; margin-top: 0px; ">
						<small>Note: Click the Left/ Right item to Add/ Remove items.</small>
					</div>
				</div>
				<div class="col-sm-12 text-right">
					{% if access_level.edit %}
						<input type="submit" class="btn btn-save" value="Save" id="save_expense"/>
					{% else %}
		    			<div data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to edit Expense Configuration. Please contact the administrator." class="btn btn-save" style="cursor: not-allowed; opacity: 0.5;">Save</div>	
					{% endif %}
					<a href="/erp/admin/enterprise/" class="btn btn-cancel">Cancel</a>
					<input type="hidden" id="update_status" value="{{ status }}">
				</div>
  			</div>
  		</div>
  	</div>
</div>

<div id="purchase_configration_modal" class="modal fade modal-fullscreen" role="dialog">
  	<div class="modal-dialog" style="width: 100%; margin: 0;">
    	<div class="modal-content" style="height: 100%; border-radius: 0;">
      		<div class="modal-header form-register" style="max-width: 1380px; width: 90%; margin: 0 auto; border: none;">
      			<button type="button" class="close" data-dismiss="modal" style="position: absolute; right: 15px; font-size: 36px; color: #999; opacity: 0.8;">&times;</button>
		        <h3>PO Configuration</h3>
		        <div class="form-group col-sm-12 checkbox">
			        {{enterprise.is_price_modification_disabled}}
			        <label for="id_enterprise-is_price_modification_disabled">Disable Price Modification</label>
			        <span style="display: inline-block; margin-left: 30px; margin-top: 10px;">
				        {{enterprise.is_price_modification_disabled_quick_po}}
	                    <label for="id_enterprise-is_price_modification_disabled_quick_po">Disable Price Modification - Quick PO</label>
	                </span>
		        </div>

      			<h3>Print Configuration</h3>
      			<label class="date-format-label" style="width: 614px;">
      				Date Format
      				<span class="pull-right" style="font-size: 16px;">
      					<label style="font-size: 12px;">Preview: </label>
      					<span class='dateFormat-preview' style="color: #209be1;"></span>
      					<span class='timeFormat-preview' style="color: #209be1;"></span>
      				</span>
      			</label>
      			<br />
      			<table id="dateFormatter" class="form-group" style="max-width: 500px; float: left; border: solid 1px #ccc; padding: 15px; display: block; border-right: none;">
      				<tr>
      					<td>
							<label style="cursor: move;"><i style='font-size: 14px;' class="fa fa-bars" aria-hidden="true"></i> Date</label>
		  					<select class="form-control date-formatter date-formatter-date">
		      					<option value="" data-value="">-</option>
		      					<option value="%d" data-value="D">D</option>
		      				</select>
		      			</td>
		      			<td>
		      				<label></label>
		      				<select class="form-control date-formatter date-formatter-seperator1">
			  					<option value=" " data-value=" "> </option>
			  					<option value="/" data-value="/">/</option>
			  					<option value="-" data-value="-">-</option>
			  					<option value="." data-value=".">.</option>
			  					<option value=", " data-value=", ">,</option>
			  				</select>
			  			</td>
		      			<td>
							<label style="cursor: move;"><i style='font-size: 14px;' class="fa fa-bars" aria-hidden="true"></i> Month</label>
		  					<select class="form-control date-formatter date-formatter-month">
		      					<option value="" data-value="">-</option>
		      					<option value="%m" data-value="MM">MM</option>
		      					<option value="%b" data-value="MMM">MMM</option>
		      					<option value="%B" data-value="MMMM">MMMM</option>
		      				</select>
		      			</td>
		      			<td>
		      				<label></label>
		      				<select class="form-control date-formatter date-formatter-seperator2">
		      					<option value=" " data-value=" "> </option>
			  					<option value="/" data-value="/">/</option>
			  					<option value="-" data-value="-">-</option>
			  					<option value="." data-value=".">.</option>
			  					<option value=", " data-value=", ">,</option>
			  				</select>
			  			</td>
		      			<td>
  							<label style="cursor: move;"><i style='font-size: 14px;' class="fa fa-bars" aria-hidden="true"></i> Year</label>
		  					<select class="form-control date-formatter date-formatter-year">
		      					<option value="" data-value="">-</option>
		      					<option value="%y" data-value="YY">YY</option>
		      					<option value="%Y" data-value="YYYY">YYYY</option>
		      				</select>
		      			</td>
      				</tr>
      			</table>

      			<table id="timeFormatter" style="max-width: 500px; float: left; border: solid 1px #ccc; padding: 15px; display: block;  border-left: none;">
      				<tr>
      					<td>
		      				<label>Hour</label>
		  					<select class="form-control time-formatter time-formatter-hour">
		      					<option value="" data-value="">-</option>
		      					<option value=" %H" data-value="HH">HH</option>
		      					<option value=" %I" data-value="hh">hh</option>
		      				</select>
		      			</td>
		      			<td>
		      				<label>Minutes</label>
		  					<select class="form-control time-formatter time-formatter-minute">
		      					<option value="" data-value="">-</option>
		      					<option value=":%M" data-value="mm">mm</option>
		      				</select>
		      			</td>
		      			<td>
		      				<label>Seconds</label>
		  					<select class="form-control time-formatter time-formatter-second">
		      					<option value="" data-value="">-</option>
		      					<option value=":%S" data-value="ss">ss</option>
		      				</select>
		      			</td>
      				</tr>
      			</table>
      			<div style="float: left; width: calc(100% - 900px); margin-left: 50px; margin-top: -18px;">
      				<label for="id_ith-include_logo" style="float: left;">LOGO Size</label>
					<span class="slidecontainer" style="float: left; margin-left: 20px;">
						<input class="img-slider"  id="id_ith-logo_size" max="100" min="0" name="ith-logo_size" step="1" type="range" value="{{ logo_size }}">
			        </span>
			        <span id="logo_size_value" style="margin-left: 10px;">{{ logo_size }}</span>
		        	<br />
			        <img src="{{enterprise_logo}}" class="pdf_company_logo" style="height: {{ logo_size }}px; margin-left: 82px; margin-top: 10px;">
      			</div>
      			<input type="hidden" id="dateFormatPy" value="{{po_doc_datetime_format}}" />
                <div class="form-group row">
	                 <div class="col-sm-12 remove-padding" style="letter-spacing: 0.8px;">
						<span class="control-label col-sm-12" style="color: #777;text-shadow: 0 0 #777;font-size: 0.9em;">REGISTRATION DETAILS</span>
					 </div>
					 <div class="form-group col-sm-12 po-config_details checkbox" style="border: 1px solid #d1d1d1;padding: 10px 10px;margin-top: 8px;margin-left: 15px;width: 97%;">
					     {{enterprise.po_doc_reg_items}}
						 {% for item in enterprise_reg_detail %}
					        <div class="col-sm-3 remove-padding po-config_details">
					            <input onchange="updatePurchaseDetails()" data-field-id="{{ item.label_id }}" class="required_checkbox" id="id_enterprise-include_in_invoice_template_{{ item.label_id }}" name="enterprise-include_in_invoice_template" type="checkbox"
					                   {% for i in po_doc_reg_items %} {% if item.label == i.label %} checked="checked" {% endif %} {% endfor %}>
					            <label for="id_enterprise-include_in_invoice_template_{{ item.label_id }}" style="margin-bottom:5px;margin-top:5px;">{{ item.label }}</label>
					        </div>
					    {% endfor %}
					</div>
	                <div class="form-group col-sm-3 checkbox">
		                <input {% if include_hsnsac %} checked="checked" {% endif %} class="checkbox customized_checkbox width_calc_checkbox" id="id_enterprise-include_hsnsac" name="enterprise-include_hsnsac" type="checkbox">
		                <label for="id_enterprise-include_hsnsac">Include HSN/SAC Details</label>
		                <span style="display: inline-block; margin-left: 30px; margin-top: 10px;">
		                	<input {% if include_hsnsac_column %} checked="checked" {% endif %} {% if not include_hsnsac %} disabled='disabled' {% endif %} class="checkbox customized_checkbox width_calc_checkbox" id="id_enterprise-include_hsnsac_column" name="enterprise-include_hsnsac_column" type="checkbox">
		                	<label for="id_enterprise-include_hsnsac_column">As Separate Column</label>
		                </span>
	                </div>
	                <div class="form-group col-sm-3 checkbox" style="margin-left: -7px;">
						<input {% if include_annexure %} checked="checked" {% endif %} class="checkbox customized_checkbox width_calc_checkbox" id="id_enterprise-include_annexure" name="enterprise-include_annexure" type="checkbox">
						<label for="id_enterprise-include_annexure">Include Annexure</label>
				   </div>
					<div class="form-group col-sm-12">
						<label>Mail Body</label>
						<input type="text" class="form-control" id="po_mail_subject" maxlength="150" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')"/>
						{{ enterprise.purchase }}
					</div>
					<div class="form-group">
						<div class="col-sm-12">
							<label>Terms</label>
							<div id="purchase_editor">

							</div>
						</div>
					</div>
				</div>
				<div class="col-sm-12 remove-padding text-right">
					{% if access_level.edit %}
						<input type="submit" class="btn btn-save" value="Save" id="save_purchase"/>
					{% else %}
		    			<div data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to edit PO Print Configuration. Please contact the administrator." class="btn btn-save" style="cursor: not-allowed; opacity: 0.5;">Save</div>
					{% endif %}
					<a href="/erp/admin/enterprise/" class="btn btn-cancel">Cancel</a>
					<input type="hidden" id="update_status" value="{{ status }}">
				</div>
  			</div>
  		</div>
  	</div>
</div>
<div id="se_configration_modal" class="modal fade modal-fullscreen" role="dialog">
  	<div class="modal-dialog" style="width: 100%; margin: 0;">
    	<div class="modal-content" style="height: 100%; border-radius: 0;">
      		<div class="modal-header form-register" style="max-width: 1380px; width: 90%; margin: 0 auto; border: none;">
      			<button type="button" class="close" data-dismiss="modal" style="position: absolute; right: 15px; font-size: 36px; color: #999; opacity: 0.8;">&times;</button>
      			<h3>Sales Estimate Print Configuration</h3>
                <div class="form-group row">
	                 <div class="col-sm-12 remove-padding" style="letter-spacing: 0.8px;">
						<span class="control-label col-sm-12" style="color: #777;text-shadow: 0 0 #777;font-size: 0.9em;">REGISTRATION DETAILS</span>
					 </div>
					 <div class="form-group col-sm-12 se-config_details checkbox" style="border: 1px solid #d1d1d1;padding: 10px 10px;margin-top: 8px;margin-left: 15px;width: 97%;">
					     {{enterprise.se_doc_reg_items}}
						 {% for item in enterprise_reg_detail %}
					        <div class="col-sm-3 remove-padding se-config_details">
					            <input onchange="updateSERegDetails()" data-field-id="{{ item.label_id }}" class="required_checkbox" id="id_enterprise-include_in_se_template_{{ item.label_id }}" name="enterprise-include_in_se_template" type="checkbox"
					                   {% for i in se_doc_reg_items %} {% if item.label == i.label %} checked="checked" {% endif %} {% endfor %}>
					            <label for="id_enterprise-include_in_se_template_{{ item.label_id }}" style="margin-bottom:5px;margin-top:5px;">{{ item.label }}</label>
					        </div>
					    {% endfor %}
					</div>
				</div>
				<div class="col-sm-12 remove-padding text-right">
					{% if logged_in_user|canApprove:'SALES' %}
						<input type="submit" class="btn btn-save" value="Save" id="save_sales" onclick="saveSEConfig()"/>
					{% else %}
		    			<div data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to edit Sales Estimate Print Configuration. Please contact the administrator." class="btn btn-save" style="cursor: not-allowed; opacity: 0.5;">Save</div>
					{% endif %}
					<a href="/erp/admin/enterprise/" class="btn btn-cancel">Cancel</a>
					<input type="hidden" id="update_status" value="{{ status }}">
				</div>
  			</div>
  		</div>
  	</div>
</div>
<style type="text/css">
	.history-log-part {
		width: 100%;
	    margin: 0;
	    padding: 8px 15px;
	    cursor: pointer;
	    padding-left: 60px;
	}
	.history-log-part:last-child {
		border-bottom: none;
	}
	.history-log-part:hover {
		background: #e8f0fe;
	}
	.history-log-part:hover .history-log-date {
		font-weight: bold;
	}
	.history-log-date {
	   font-style: italic;
	}
	.history-log-username {
		font-size: 15px;
		padding-top: 3px;
		font-weight: bold;
	}

	.history-log-content {
		margin-top: 8px;
	}

	.history-log-content ul {
		padding: 0px 10px 0px 35px;
	}
	.history-log-content li {
		margin-bottom: 10px;
	}

	.history-log-content li:last-child {
		margin-bottom: 0;
	}

	.history-log-part.active .history-log-date,
	.history-log-part.active .history-log-username {
		color: #004195;
	}

	.history-log-part.active .history-log-date {
	    text-align: right;
		display: block;
	}

	.history-log-part .fa-chevron-down:before {
		content: "\f078";
		color: #999;
	}

	.history-log-part.active .fa-chevron-down:before {
		content: "\f077";
		color: #999;
	}

	#change_log_modal .modal-body {
		overflow: auto;
	}


	ul.timeline {
	    list-style-type: none;
	    position: relative;
		padding: 0px;
	}
	ul.timeline:before {
	    content: ' ';
	    background: #d4d9df;
	    display: inline-block;
	    position: absolute;
	    left: 29px;
	    width: 2px;
	    height: 94%;
	    z-index: 400;
        margin-top: 12px;
	}
	ul.timeline > li {

	}
	ul.timeline > li:before {
	    content: ' ';
	    background: white;
	    display: inline-block;
	    position: absolute;
	    border-radius: 50%;
	    border: 3px solid #209be1;
	    left: 23px;
	    width: 14px;
	    height: 14px;
	    z-index: 400;
	    margin-top: 4px;
	}
	ul.timeline > li:last-child:before {
		background: #209be1;
	}
	body {
		overflow-y: scroll;
	}
</style>
<div id="change_log_modal" class="modal" tabindex="-1" role="dialog" aria-labelledby="" aria-hidden="true">
   <div class="modal-dialog">
	    <div class="modal-content">
		    <div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Activity Log</h4>
	      	</div>
      		<div class="modal-body remove-padding">
		        <div id='loadingmessage_changelog_listing_ie' style="text-align:center">
						<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' width="100">
						<br /> Please wait...
			    </div>
        		<ul class="history-log-container timeline">

        		</ul>
		        <ul class="show-more-log" style="list-style: none; text-align: right; margin-right: 15px; cursor: pointer;">
			        <li onclick="shoMoreLogs(20,20)">Show More</li>
		        </ul>
      		</div>
      		<div class="modal-footer">
      			<a href="#" class="btn btn-cancel" data-dismiss="modal">Close</a>
      		</div>
    	</div>
  	</div>
</div>

<script type="text/javascript">
	function shoMoreLogs(offset, limit){
		if($("#change_log_modal").hasClass("change_log")) {
			$.ajax({
		        url: '/erp/admin/enterprise/getloglist/',
		        type: "POST",
		        dataType: "json",
		        data: {offset: offset, limit: limit},
		        success: function (data) {
		        if (data['response_code'] != 400){
		            x = data['data'];
					var row = "";
					var i2 = offset;
					for(var i = 0; i < x.length; i++) {
					    var obj = x[i].log;
					    var row = `<li class="history-log-part" id="history-part-${i2}" onclick="loadHistoryContent('${data['data'][0].eid}', '${obj.modified_at}', ${i2}, '${obj.preference}')">
				                        <span class="history-log-username">${obj.username[0]+" "+obj.username[1]}</span>
				                        <div class="history-log-content" style="display: none;"></div>
				                        <span class="history-log-date">${moment(obj.modified_at).format("MMM DD, YYYY hh:mm:ss a")}</span>
				                    </li>`;

		                $(".history-log-container").append(row);
		                var displayedCount = $(".history-log-container").find("li").length;
		                i2++;
		                if(x.length < 20){
		                    $(".show-more-log").addClass("hide");
		                }
		                else {
		                    $(".show-more-log").find("li").attr("onclick", "shoMoreLogs("+displayedCount+", 20)");
	                    }
					}
				}
				else {
					$(".show-more-log").addClass("hide");
				}
		        },
		        error: function ($xhr,textStatus,errorThrown) {
			        console.log("ERROR : ", $xhr);
			        if ($xhr.responseText.indexOf("Your session has expired") != -1){
			            location.reload();
			        }
		        }
			});
		}
	}

	function changeLogActivityInit(){
		$("#id-change-log").removeClass("hide");
		$("#btn_change_log").click(function(){
			$("#change_log_modal").addClass("change_log").removeClass("change_preference_log");
			$("#change_log_modal").modal("show");
			$("#loadingmessage_changelog_listing_ie").show();
			$(".history-log-container").html("");
			$.ajax({
		        url: '/erp/admin/enterprise/getloglist/',
		        type: "POST",
		        dataType: "json",
		        data: {offset: 0, limit: 20},
		        success: function (data) {
		        if (data['response_code'] != 400){
		            x = data['data'];
					var row = "";
					for(var i = 0; i < x.length; i++) {
					    var obj = x[i].log;
					    var row = `<li class="history-log-part" id="history-part-${i}" onclick="loadHistoryContent('${data['data'][0].eid}', '${obj.modified_at}', ${i}, '${obj.preference}')">
				                        <span class="history-log-username">${obj.username[0]+" "+obj.username[1]}</span>
				                        <div class="history-log-content" style="display: none;"></div>
				                        <span class="history-log-date">${moment(obj.modified_at).format("MMM DD, YYYY hh:mm:ss a")}</span>
				                    </li>`;
				        $("#loadingmessage_changelog_listing_ie").hide();
		                $(".history-log-container").append(row);
		                var displayedCount = $(".history-log-container").find("li").length;
					}
						if(x.length < 20){
		                    $(".show-more-log").addClass("hide");
		                }
		                else {
		                    $(".show-more-log").removeClass("hide");
		                    $(".show-more-log").find("li").attr("onclick", "shoMoreLogs("+displayedCount+", 20)");
	                    }
				}
				else {
				    var row = `<span style="width: 100%; padding: 15px; text-align:center; display: block;">No Activity Log has been captured.</span>`;
					$("#loadingmessage_changelog_listing_ie").hide();
		            $("#change_log_modal .modal-body").html(row);
				}
		        },
		        error: function ($xhr,textStatus,errorThrown) {
			        console.log("ERROR : ", $xhr);
			        if ($xhr.responseText.indexOf("Your session has expired") != -1){
			            location.reload();
			        }
		        }
			});
		});

		$("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
	}

	function loadHistoryContent(voucher_id, modified_at, i, preference) {
		var currentLogContent = $("#history-part-"+i).find('.history-log-content');
		if(currentLogContent.text() == "") {
			currentLogContent.html(`<img src="/site_media/images/loading_spinner.gif" style="width: 30px;"> Loading, Please wait...`).show();
			currentLogContent.closest(".history-log-part").addClass("active");
			setTimeout(function(){
				$.ajax({
			        url: '/erp/admin/enterprise/getlogdata/',
			        type: "POST",
			        dataType: "json",
			        data: {"voucher_id":voucher_id, "modified_at": modified_at, "preference": preference},
			        success: function (data) {
			        if (data['response_code'] != 400){
			        var row = '';
				        $.each( data['data'], function( key, value ) {
						  row += `<li>` + value + `</li>`;
						});
			        currentLogContent.html(`<ul>` + row + `</ul>`);
					currentLogContent.hide();
					currentLogContent.slideDown();
					}else{
					currentLogContent.html(`log data not available`);
					currentLogContent.hide();
					currentLogContent.slideDown();

					}
			        },
			        error: function () {
			        console.log('Changelog listing failed to load');

			        }
				});

			},100);
		}
		else {
			if(currentLogContent.find("img").length > 0) {
				return;
			}
			if(currentLogContent.is(":visible")) {
				currentLogContent.slideUp('fast', function() {
				   currentLogContent.closest(".history-log-part").removeClass("active");
				});

			}
			else {
				currentLogContent.slideDown();
				currentLogContent.closest(".history-log-part").addClass("active");
			}
		}
		$("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
	}

	function loadPreferenceHistoryContent(voucher_id, modified_at, i) {
		var currentLogContent = $("#history-part-"+i).find('.history-log-content');
		if(currentLogContent.text() == "") {
			currentLogContent.html(`<img src="/site_media/images/loading_spinner.gif" style="width: 30px;"> Loading, Please wait...`).show();
			currentLogContent.closest(".history-log-part").addClass("active");
			setTimeout(function(){
				$.ajax({
			        url: '/erp/admin/enterprise/getpreferencelogdata/',
			        type: "POST",
			        dataType: "json",
			        data: {"voucher_id":voucher_id, "modified_at": modified_at},
			        success: function (data) {
			        if (data['response_code'] != 400){
			        var row = '';
				        $.each( data['data'], function( key, value ) {
						  row += `<li>` + value + `</li>`;
						});
			        currentLogContent.html(`<ul>` + row + `</ul>`);
					currentLogContent.hide();
					currentLogContent.slideDown();
					}else{
					currentLogContent.html(`log data not available`);
					currentLogContent.hide();
					currentLogContent.slideDown();

					}
			        },
			        error: function () {
			        console.log('Changelog listing failed to load');

			        }
				});

			},100);
		}
		else {
			if(currentLogContent.find("img").length > 0) {
				return;
			}
			if(currentLogContent.is(":visible")) {
				currentLogContent.slideUp('fast', function() {
				   currentLogContent.closest(".history-log-part").removeClass("active");
				});

			}
			else {
				currentLogContent.slideDown();
				currentLogContent.closest(".history-log-part").addClass("active");
			}
		}
		$("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
	}
</script>
<script>
	function deleteRegistraionDetails(current) {
		$(current).closest("div").addClass("hide deleted-registration-detail");
	}

	function deleteContactDetails(current) {
		$(current).closest(".contact-details").addClass("hide deleted-contact-detail");
	}

	function whatsappContactDetails(current) {
		 $(current).toggleClass("whatsapp-enable");
		 	if($(current).is(".whatsapp-enable")){
				 $(current).attr('data-original-title', 'Unauthorize this phone number to use XSassist Whatsapp Support');
				 $(current).closest("div").find(".contact-authorized").removeClass("hide");
			 }
			 else{
		   		 $(current).attr('data-original-title', 'Authorize this phone number to use XSassist Whatsapp Support');
				  $(current).closest("div").find(".contact-authorized").addClass("hide");
		 	}
		 TooltipInit();
	 }

	function saveProfile(current) {
		$(".error-border").removeClass('error-border');
	    $(".custom-error-message").remove();

	    var ControlCollections = [
	        {
	            controltype: 'textbox',
	            controlid: 'id_enterprise-name',
	            isrequired: true,
	            errormsg: 'Enterprise Name is Required'
	        },
	        {
	            controltype: 'textbox',
	            controlid: 'id_enterprise-address_1',
	            isrequired: true,
	            errormsg: ''
	        },
	        {
	            controltype: 'textbox',
	            controlid: 'id_enterprise-city',
	            isrequired: true,
	            errormsg: ''
	        },
	        {
	            controltype: 'textbox',
	            controlid: 'id_enterprise-state',
	            isrequired: true,
	            errormsg: ''
	        },
	        {
	            controltype: 'textbox',
	            controlid: 'id_enterprise-pin_code',
	            isrequired: true,
	            errormsg: '',
	            minvalue: 6,
	            minvalerrormsg: 'Pincode should be in 6 digit'
	        },
	        {
	            controltype: 'textbox',
	            controlid: 'id_enterprise-phone',
	            isrequired: true,
	            errormsg: ''
	        },
	        {
	            controltype: 'textbox',
	            controlid: 'id_enterprise-contact_person',
	            isrequired: true,
	            errormsg: ''
	        },
	        {
	            controltype: 'textbox',
	            controlid: 'id_enterprise-email',
	            isrequired: true,
	            errormsg: '',
	            isemail: true,
	            emailerrormsg: 'Invalid Email Address.'
	        }
	    ];

	    $(".other_contact_details").find(".contact-details:visible").each(function(){
	    	var nameValidate = $(this).find(".contact-person-name").attr("id");
	    	var numberValidate = $(this).find(".contact-person-number").attr("id");
	    	var emailValidate = $(this).find(".contact-person-email").attr("id");

	    	var control = {
				controltype: 'textbox',
				controlid: nameValidate,
				isrequired: true,
				errormsg: ''
			};
			ControlCollections[ControlCollections.length] = control;
			var control = {
				controltype: 'textbox',
				controlid: numberValidate,
				isrequired: true,
				errormsg: ''
			};
			ControlCollections[ControlCollections.length] = control;
			var control = {
				controltype: 'textbox',
				controlid: emailValidate,
				isemail: true,
	            emailerrormsg: 'Invalid Email Address.'
			};
			ControlCollections[ControlCollections.length] = control;
	    });


	    var result = JSCustomValidator.JSvalidate(ControlCollections);
        ga('send', 'event', 'Enterprise', 'Update Enterprise Profile', $('#enterprise_label').val(), 1);
        if(result) {
			var enterpriseRegistrationDetailsObj = [];
			var enterpriseAddressDetailsObj = [];
			var enterpriseContactDetailsObj = [];
			var duplicatedEnterpriseDetail = [];
			$(".registration-extra-details").find(".registration-details").each(function(){
				var current = $(this);
				var item = {};
				item ["label_id"] = Number(current.find(".registration-label-id").val());
	        	item ["details"] = current.find(".registraion-value").val();
	        	item ["label"] = current.find(".registraion-key").val();
	        	item ["is_deleted"] = current.hasClass('deleted-registration-detail')?1:0;
	        	if(item ["details"].trim() == "" && item ["label"].trim() == "") {
	        		item ["is_deleted"] = 1;
	        	}
	        	if(!current.hasClass("deleted-registration-detail")) {
		        	if(item ["details"].trim() != "" && item ["label"].trim() == "") {
						swal("","There is an empty value in your Label of Registration Details. Please make sure your Label(s) are correct.", "warning")
						result = false;
					}
				}
	        	enterpriseRegistrationDetailsObj.push(item);
	        	if(!current.hasClass("deleted-registration-detail")) {
        		    duplicatedEnterpriseDetail.push(item ["label"].toLowerCase());
                }
			});

			
			var logo = $(".cropped_image.profile_image").attr("src");
			if (typeof logo == typeof undefined || logo == false) {
				logo = "";
			}
			
			enterpriseAddressDetailsObj = [{
				'code': $(".enterprise-code").text(), 
				'name': $("#id_enterprise-name").val(), 
				'address1': $("#id_enterprise-address_1").val(),
				'city': $("#id_enterprise-city").val(),
				'country_code': $("#id_country-__prefix__-make_choices").val(),
				'state': $("#id_enterprise-state").val(),
				'pin_code': $("#id_enterprise-pin_code").val(), 
				'fy_start_day': $("#id_enterprise-fy_start_day").val(), 
				'currency_id': $("#id_enterprise-home_currency_id").val(), 
				'logo': logo
			}];

			enterpriseContactDetailsObj = [{
				'sequence_id': 1, 
				'name': $("#id_enterprise-contact_person").val(), 
				'email': $("#id_enterprise-email").val(),
				'phone_no': $("#id_enterprise-phone").val(), 
				'fax_no': $("#id_enterprise-fax").val(), 
				'is_whatsapp': $(".primary_whatapp").hasClass("whatsapp-enable")?1:0,
				'is_deleted': 0
			}];


			$(".other_contact_details").find(".contact-details").each(function(){
				var current = $(this);
				var item = {};
				item ["sequence_id"] = Number(current.attr("data-sequence-id"));
	        	item ["name"] = current.find(".contact-person-name").val();
	        	item ["email"] = current.find(".contact-person-email").val();
	        	item ["phone_no"] = current.find(".contact-person-number").val();
	        	item ["fax_no"] = current.find(".contact-person-fax").val();
	        	item ["is_whatsapp"] = current.find(".fa.fa-whatsapp").hasClass("whatsapp-enable")?1:0;
	        	item ["is_deleted"] = current.hasClass('deleted-contact-detail')?1:0;
	        	enterpriseContactDetailsObj.push(item);
			});

			enterpriseRegistrationDetailsObj = JSON.stringify(enterpriseRegistrationDetailsObj);
			enterpriseAddressDetailsObj = JSON.stringify(enterpriseAddressDetailsObj);
			enterpriseContactDetailsObj = JSON.stringify(enterpriseContactDetailsObj);
			var isDuplicate = findDuplicates(duplicatedEnterpriseDetail);
			if(isDuplicate != "") {
				swal("","There is a duplication in your Label of Registration Details. Please make sure your Label(s) are correct.", "warning")
				result = false;
			}
			
			if(result) {
				$.ajax({
			        url: "/erp/admin/enterprise/saveProfile/",
			        type: "post",
			        dataType: "json",
			        data: {enterprise_detail: enterpriseAddressDetailsObj, enterprise_contact_details: enterpriseContactDetailsObj, enterprise_reg_details: enterpriseRegistrationDetailsObj},
			        success: function (response) {
			            if(response.response_message == "Success") {
			            	if($("#is_new_registration").val() == 1) {
					            saveAndNextEdit(current);
					            firstTimeRegistration();
					        }
					        else {
					        	swal({
									title: "",
				                    text: response.custom_message,
				                    type: "success"
			                    },
			                    function() {
						            location.reload();							        
								});
					        }
						}
			        },
			        error: function(xhr,errmsg,err){
			        	location.reload();
			        }
			    });
			}
		}
	}

	function savePreference(current) {
		if($("#switch_btn_1").find("a.active").data("title") == 0) {
			var setting_flags = 0;
			var is_gate_inward_no_mandatory = 0;
			var is_negative_stock_allowed = 0;
			var gate_inward_no_flags = 0;
			var is_purchase_order_mandatory = 0;
		}
		else {
			var setting_flags = 0;
			$(".flag-value").each(function(){
				setting_flags += Number($(this).find("a.active").data("value"));
				if($("#switch_btn_4").find("a.active").data("value") == 1) {
					$("#id_enterprise-setting_flags_0").attr("checked", true)
				}
				else {
					$("#id_enterprise-setting_flags_0").attr("checked", false)
				}
				if($("#switch_btn_6").find("a.active").data("value") == 2) {
					$("#id_enterprise-setting_flags_1").attr("checked", true)
				}
				else {
					$("#id_enterprise-setting_flags_1").attr("checked", false)
				}
				if($("#switch_btn_7").find("a.active").data("value") == 4) {
					$("#id_enterprise-setting_flags_2").attr("checked", true)
				}
				else {
					$("#id_enterprise-setting_flags_2").attr("checked", false)
				}
				if($("#switch_btn_8").find("a.active").data("value") == 8) {
					$("#id_enterprise-setting_flags_3").attr("checked", true)
				}
				else {
					$("#id_enterprise-setting_flags_3").attr("checked", false)
				}
				if($("#switch_btn_9").find("a.active").data("value") == 16) {
					$("#id_enterprise-setting_flags_4").attr("checked", true)
				}
				else {
					$("#id_enterprise-setting_flags_4").attr("checked", false)
				}
			});
			var gate_inward_no_flags = 0;
			$(".gate-inward-flag").each(function(){
				gate_inward_no_flags += Number($(this).find("a.active").data("value"));
				if($("#switch_btn_2").find("a.active").data("value") == 1) {
					$("#id_enterprise-gate_inward_no_flags_0").attr("checked", true)
				}
				else {
					$("#id_enterprise-gate_inward_no_flags_0").attr("checked", false)
				}
				if($("#switch_btn_2a").find("a.active").data("value") == 2) {
					$("#id_enterprise-gate_inward_no_flags_1").attr("checked", true)
				}
				else {
					$("#id_enterprise-gate_inward_no_flags_1").attr("checked", false)
				}
				if($("#switch_btn_2b").find("a.active").data("value") == 4) {
					$("#id_enterprise-gate_inward_no_flags_2").attr("checked", true)
				}
				else {
					$("#id_enterprise-gate_inward_no_flags_2").attr("checked", false)
				}
				if($("#switch_btn_2c").find("a.active").data("value") == 8) {
					$("#id_enterprise-gate_inward_no_flags_3").attr("checked", true)
				}
				else {
					$("#id_enterprise-gate_inward_no_flags_3").attr("checked", false)
				}

			});
			// TODO #7966: Split the gate_inward_no_flags value for 4 checkbox
			if($("#switch_btn_2").find("a.active").data("value") == 1) {
				$("#id_enterprise-is_gate_inward_no_mandatory").attr("checked", true)
			}
			else {
				$("#id_enterprise-is_gate_inward_no_mandatory").attr("checked", false)
			}

			if($("#switch_btn_3").find("a.active").data("value") == 1) {
				$("#id_enterprise-is_negative_stock_allowed").attr("checked", true)
			}
			else {
				$("#id_enterprise-is_negative_stock_allowed").attr("checked", false)
			}

			if($("#switch_btn_5").find("a.active").data("value") == 1) {
				$("#id_enterprise-is_purchase_order_mandatory").attr("checked", true)
			}
			else {
				$("#id_enterprise-is_purchase_order_mandatory").attr("checked", false)
			}
			var is_gate_inward_no_mandatory = $("#switch_btn_2").find("a.active").data("value");
			var is_negative_stock_allowed = $("#switch_btn_3").find("a.active").data("value");
			var is_purchase_order_mandatory = $("#switch_btn_5").find("a.active").data("value");
		}
		svgConfigrationSettingsInit();
		savePreferenceAjaxCall(current, setting_flags, is_negative_stock_allowed, is_gate_inward_no_mandatory, gate_inward_no_flags, is_purchase_order_mandatory,0,0,0,0,0,1);
	}

	function savePreferenceFromSVG(current) {
		var setting_flags = 0;
		var gate_inward_no_flags = 0;
		$(".workflow-container .svg-flag-value").each(function(){
			if(!$(this).hasClass("item-disabled")) {
				setting_flags += Number($(this).data("value"));
			}
		});

		$(".workflow-container .configurable-checkbox-gate-inward").each(function(){
			if(!$(this).hasClass("item-disabled")) {
				gate_inward_no_flags += Number($(this).data("value"));
			}
		});

		var is_gate_inward_no_mandatory = ($(".workflow-container g.gate_inward_number").hasClass("item-disabled"))?0:1;
		var is_negative_stock_allowed = ($(".workflow-container g.negative_invertory").hasClass("item-disabled"))?0:1;
		var is_purchase_order_mandatory = ($(".workflow-container g.mandate_po").hasClass("item-disabled"))?0:1;
		var is_delivery_schedule = ($(".workflow-container g.delivery_schedule").hasClass("item-disabled"))?0:1;
		var is_multiple_units = ($(".workflow-container g.multiple_units").hasClass("item-disabled"))?0:1;
		var is_blanket_po = ($(".workflow-container g.blanket_po").hasClass("item-disabled"))?0:1;
		var is_request_ack = ($(".workflow-container g.request_ack").hasClass("item-disabled"))?0:1;
		var is_price_modification_disabled = ($(".workflow-container g.is_price_modification_disabled").hasClass("item-disabled"))?0:1;
		console.log(setting_flags, is_blanket_po, is_request_ack, is_price_modification_disabled)
		savePreferenceAjaxCall(current, setting_flags, is_negative_stock_allowed, is_gate_inward_no_mandatory, gate_inward_no_flags, is_purchase_order_mandatory, is_delivery_schedule, is_multiple_units, is_request_ack, is_blanket_po, is_price_modification_disabled, $("#is_new_registration").val());
	}

	function savePreferenceAjaxCall(current, setting_flags, is_negative_stock_allowed, is_gate_inward_no_mandatory, gate_inward_no_flags, is_purchase_order_mandatory, is_delivery_schedule, is_multiple_units, is_icd_request_acknowledgement, is_blanket_po, is_price_modification_disabled, isNewRegistration) {
		$.ajax({
	        url: "/erp/admin/enterprise/savePreference/",
	        type: "post",
	        dataType: "json",
        	data: {"setting_flags": setting_flags, "is_negative_stock_allowed": is_negative_stock_allowed,
        			"is_gate_inward_no_mandatory": is_gate_inward_no_mandatory, "gate_inward_no_flags": gate_inward_no_flags,
        			"is_purchase_order_mandatory": is_purchase_order_mandatory,"is_delivery_schedule": is_delivery_schedule,"is_multiple_units": is_multiple_units, "is_icd_request_acknowledgement": is_icd_request_acknowledgement,"is_price_modification_disabled": is_price_modification_disabled, "is_blanket_po": is_blanket_po},
	        success: function (response) {
				if(response.response_message == "Success") {
					if(isNewRegistration == 1) {
						saveAndNextEdit(current);
					}
					else {
						swal({
							title: "",
		                    text: response.custom_message,
		                    type: "success"
	                    },
	                    function() {
							window.location.href = "/erp/admin/enterprise";
						});
					}
				}
				else {
                    swal({title:"OOPS!!", text:response.response_message + ".<br/>" + response.custom_message, type:"error"});
                }
	        },
	        error: function(){
	        	location.reload();
	        }
	    });
	}

	const findDuplicates = (enterprise_detail) => {
	  	let sorted_enterprise_detail = enterprise_detail.slice().sort();
	  	let results = [];
	  	for (let i = 0; i < sorted_enterprise_detail.length - 1; i++) {
	    	if (sorted_enterprise_detail[i + 1] == sorted_enterprise_detail[i]) {
	      	results.push(sorted_enterprise_detail[i]);
	    	}
	  	}
	  	return results;
	}

	function saveAndNextEdit(current) {
		var nextContainer = $(current).attr("data-next-container");
		var currentContainer = $(current).closest(".editable-container");
		if($(current).hasClass("btn-next")) {
			currentContainer.closest(".editable-container").hide();
			setTimeout(function(){
		  		$("."+nextContainer).show('slide', {direction: 'right'}, 500);
		  	},50);
		}
		else {
			currentContainer.closest(".editable-container").hide();	
			setTimeout(function(){
		  		$("."+nextContainer).show('slide', {direction: 'left'}, 500);
		  	},500);
		}
		if(nextContainer.indexOf("profile-container") >=0 ) {
			$(".item-profile-container").addClass("current");
		}
		else if(nextContainer.indexOf("registration-container") >=0 ) {
			$(".item-profile-container, .item-registration-container").addClass("current");
		}
		else if(nextContainer.indexOf("financial-container") >=0 ) {
			$(".item-profile-container, .item-registration-container, .item-financial-container").addClass("current");
		}
	}

	$("#save_expense").click(function(){
		var claim_heads = $("#id_enterprise-claim_heads").val();
		var expense_heads = $("#id_enterprise-expense_heads").val();
		if (claim_heads == null){
			claim_heads = "";
		}
		if (expense_heads == null){
			expense_heads = "";
		}
		console.log(claim_heads)
		console.log(expense_heads)
		$.ajax({
	        url: "/erp/admin/enterprise/saveExpense/",
	        type: "post",
	        dataType: "json",
	        data: {"claim_heads": claim_heads.toString(), "expense_heads": expense_heads.toString()},
	        success: function (response) {
				if(response.response_message == "Success") {
					swal({
						title: "",
	                    text: response.custom_message,
	                    type: "success"
                    },
                    function() {
						window.location.href = "/erp/admin/enterprise";
					});
				}
				else {
                    swal({title:"OOPS!!", text:response.response_message + ".<br/>" + response.custom_message, type:"error"});
                }
	        },
	        error: function(){
	        	location.reload();
	        }
	    });
	});

	function updatePurchaseDetails() {
		var purchaseDetailsObj = [];
		$(".po-config_details").find("input[type='checkbox']").each(function(){
			var current = $(this);
			if(current.is(":checked")) {
				var item = {};
				item ["label_id"] = Number(current.attr("data-field-id"));
                item ["label"] = current.next("label").text();
                purchaseDetailsObj.push(item);
			}
		});
		purchaseDetailsObj = JSON.stringify(purchaseDetailsObj);
		$("#id_enterprise-po_doc_reg_items").val(purchaseDetailsObj)
	}

	function savePurchaseNote() {
		var purchase_notes = $("#id_enterprise-purchase").val();
		var po_doc_reg_items = $("#id_enterprise-po_doc_reg_items").val();
		var include_hsnsac = ($("#id_enterprise-include_hsnsac").is(":checked"))?1:0;
		var include_annexure = ($("#id_enterprise-include_annexure").is(":checked"))?1:0;
		var logo_size = $("#id_ith-logo_size").val();
		var include_hsnsac_column = ($("#id_enterprise-include_hsnsac_column").is(":checked"))?1:0;
		var date_time_format = $("#dateFormatPy").val();
		var is_price_modification_disabled = ($("#id_enterprise-is_price_modification_disabled").is(":checked"))?1:0;
		var is_price_modification_disabled_quick_po = ($("#id_enterprise-is_price_modification_disabled_quick_po").is(":checked"))?1:0;
		console.log(purchase_notes)
		$.ajax({
	        url: "/erp/admin/enterprise/savePurchase/",
	        type: "post",
	        datatype: "json",
	        data: {purchase_notes: purchase_notes, po_doc_reg_items: po_doc_reg_items, include_hsnsac: include_hsnsac, date_time_format: date_time_format,
	        include_annexure: include_annexure, logo_size: logo_size, include_hsnsac_column: include_hsnsac_column,
	        is_price_modification_disabled:is_price_modification_disabled, is_price_modification_disabled_quick_po: is_price_modification_disabled_quick_po },
	        success: function (response) {
	        	if(response.response_message == undefined) {
		        	if((response).indexOf("Your session has expired") != -1) {
		        		location.reload();
		        	}
		        }
		        else {
					if(response.response_message == "Success") {
						swal({
							title: "",
		                    text: response.custom_message,
		                    type: "success"
	                    },
	                    function() {
							window.location.href = "/erp/admin/enterprise";
						});
					}
					else {
	                    swal({title:"OOPS!!", text:response.response_message + ".<br/>" + response.custom_message, type:"error"});
	                }
	            }
	        },
	        error: function(){
	        	location.reload();
	        }
	    });
	}
	function updateSERegDetails() {
		var seRegDetailsObj = [];
		$(".se-config_details").find("input[type='checkbox']").each(function(){
			var current = $(this);
			if(current.is(":checked")) {
				var item = {};
				item ["label_id"] = Number(current.attr("data-field-id"));
                item ["label"] = current.next("label").text();
                seRegDetailsObj.push(item);
			}
		});
		seRegDetailsObj = JSON.stringify(seRegDetailsObj);
		$("#id_enterprise-se_doc_reg_items").val(seRegDetailsObj)
	}
	function saveSEConfig() {
		var se_doc_reg_items = $("#id_enterprise-se_doc_reg_items").val();
		$.ajax({
	        url: "/erp/admin/enterprise/saveSETemplateConfig/",
	        type: "post",
	        datatype: "json",
	        data: {se_doc_reg_items: se_doc_reg_items},
	        success: function (response) {
	        	if(response.response_message == undefined) {
		        	if((response).indexOf("Your session has expired") != -1) {
		        		location.reload();
		        	}
		        }
		        else {
					if(response.response_message == "Success") {
						swal({
							title: "",
		                    text: response.custom_message,
		                    type: "success"
	                    },
	                    function() {
							window.location.href = "/erp/admin/enterprise";
						});
					}
					else {
	                    swal({title:"OOPS!!", text:response.response_message + ".<br/>" + response.custom_message, type:"error"});
	                }
	            }
	        },
	        error: function(){
	        	location.reload();
	        }
	    });
	}
	function editableByHoverInit() {
		$( ".view-profile-container" ).hover(
		  	function() {
		    	$(this).find(".fa-pencil").removeClass("hide");
		  	}, function() {
		    	$(this).find(".fa-pencil").addClass("hide");
		  	}
		);

		$( ".enterprise-name-label" ).hover(
		  	function() {
		    	$(this).find(".fa-pencil").css("visibility", "");
		  	}, function() {
		    	$(this).find(".fa-pencil").css("visibility", "hidden");
		  	}
		);

		$(".enterprise-name-label").click(function(){
			$(this).hide();
			$(".enterprise-name-container").show(500);
			var $initialVal = $("#id_enterprise-name").val();
			$("#id_enterprise-name").val("")
    		$("#id_enterprise-name").focus().val($initialVal);
		})
	}

	function openEditableModal(item) {
		$("#edit_enterprise_modal").modal("show");
		$(".editable-container").hide();
		$("."+item).show();
		//firstTimeRegistration();
	}

	function openTagModal(){
		$("#tag_project_name").val("").trigger("keyup");
		$("#tagsModal").modal("show");
		//$(".modal-backdrop.fade.in").removeClass("in");
	}

	function tagChangeEvent(current) {
		var currentInput = $(current).closest("td").find("input");
		var currentTag = $(current).closest("tr").find("td:first").text().trim();
		var currentTagStatus = currentInput.is(":checked");
		var currentProjectCode = $(current).closest("tr").find("input[name='project_code']").val();
		if(currentTagStatus && $(current).hasClass("on")) {
			currentInput.prop("checked", false);
			return false;
		}
		if(!currentTagStatus && $(current).hasClass("off")) {
			currentInput.prop("checked", true);
			return false;
		}
		if(currentTagStatus) {
			currentInput.prop("checked", false);
			swal({
				title: "Are you sure?",
				text: `Do you want to deactivate the Tag <b>'${currentTag}'</b>?`,
				type: "warning",
				showCancelButton: true,
				confirmButtonColor: "#209be1",
				confirmButtonText: "Yes",
				closeOnConfirm: true
			},
			function(deleteUser) {
		      	if (deleteUser) {
		      		saveTag(currentProjectCode, currentTag, false)
		     	}
		   	});
		}
		else {
			currentInput.prop("checked", true);
			saveTag(currentProjectCode, currentTag, true)
		}
	}

	function saveTag(project_code, project_name, is_active){
		$.ajax({
			url:"/erp/masters/json/projects/save/",
			type: "post",
			datatype: "json",
			data: {
				project_code: project_code,	
				project_name: project_name,
				enterprise_id:$("#enterprise_id").val(),
				is_active:is_active
			},
			success:function(response){
				if(typeof response.message === undefined) {
		            if((response).indexOf("Your session has expired") != -1) {
		                location.reload();
		            }
		        }
		        else {
					if(response.response_message == "Success") {
						swal({
							title: "",
		                    text: response.custom_message,
		                    type: "success"
		                });
		                if(response.custom_message == "Project Added Successfully") {
		                	var row = `<tr class="project_${response.code}" data-active="1">
					                        <td class="td-tag-name-label" style="vertical-align: middle;">
					                            <span>${response.name}</span>
					                            <i class="fa fa-pencil hide" data-tooltip="tooltip" title="" onclick="editTag(this)" aria-hidden="true" data-original-title="Edit"></i>
					                        </td>
					                        <td class="td-tag-name-text hide">
					                            <input type="text" class="form-control" name="project_name" value="" maxlength="50" onkeypress="validateStringOnKeyPress(this,event,'alphaSpecialChar');" onblur="validateStringOnBlur(this,event,'alphaSpecialChar');">
					                            <i class="fa fa-check" data-tooltip="tooltip" title="" onclick="updateTag(this)" aria-hidden="true" data-original-title="Update"></i>
					                            <i class="fa fa-ban" data-tooltip="tooltip" title="" onclick="cancelEditTag(this)" aria-hidden="true" data-original-title="Cancel"></i>
					                        </td>
					                        <td class='text-center'>
												<label role='button' style="border: solid 1px #004195; border-radius: 50px;">
											  		<input class='hide' type="checkbox" name="is_active" checked="" />
											  		<span class="toggle-round round on" onClick="tagChangeEvent(this)">Active</span>
								  					<span class="toggle-round round off" onClick="tagChangeEvent(this)">Inactive</span>
												</label>
					                        </td>
									        <td hidden="hidden">
										         <input type="hidden" name="project_code" value="${response.code}">
									        </td>
					                    </tr>`;
					        $("#table-tags").find(".tr-tags-add").after(row);
					        $("#tag_project_name").val("").trigger("keyup");
					        tagEditHoverInit();
		                }
		                else if(response.custom_message == "Project Updated Successfully") {
		                	var project_code = response.code;
		                	$("#table-tags").find(`tr.project_${project_code}`).find("input[name='is_active']").prop("checked", response.is_active)
		                	$("#table-tags").find(`tr.project_${project_code}`).attr("data-active", response.is_active).find(".td-tag-name-label span").text(response.name);
		                }
		                updateActiveTagCount();
					}
					else {
	                    swal({title:"OOPS!!", text:response.custom_message, type:"error"});
	                }
	            }
	        },
	        error: function(){
	        	location.reload();
	        }
	    });
	}

	function filterAddTagEvent(input) {
		$("#tag_project_name").removeClass("error-border");
		var input, filter, table, tr, td, i, txtValue;
  		filter = input.value.trim().toUpperCase();
	  	table = document.getElementById("table-tags");
	  	tr = table.getElementsByTagName("tr");
		for (i = 1; i < tr.length; i++) {
		    td = tr[i].getElementsByTagName("td")[0];
		    if (td) {
		      	txtValue = td.textContent || td.innerText;
		      	if (txtValue.toUpperCase().indexOf(filter) > -1) {
		        	tr[i].style.display = "";
		      	} 
		      	else {
		        	tr[i].style.display = "none";
		      	}
		    }
		}
	}
const addNewTag = async () => {
	const projects = await getProjects();
    const projectWise = projects ?projects.project_wise : null;
    if(projectWise){
    	$('#tagsModal').modal("hide");
		$("#modal_project_creation").modal("show");
	}
	else{
		 if($("#tag_project_name").val().trim() != "") {
	    	$('#tagsModal').modal("show");
	    	$("#tag_project_name").removeClass("error-border");
			var tagName = $("#tag_project_name").val().trim();
			saveTag("", tagName, "true");
		}
		else {
			$("#tag_project_name").addClass("error-border");
		}
		}
	}

	function editTag(current) {
		var tagName = $(current).closest("tr").find(".td-tag-name-label span").text().trim();
		$(current).closest("tr").find(".td-tag-name-text").find("input").val(tagName);
		$(current).closest("tr").find(".td-tag-name-label").addClass("hide");
		$(current).closest("tr").find(".td-tag-name-text").removeClass("hide");
	}

	function updateTag(current) {
		var updateTagName = $(current).closest("tr").find(".td-tag-name-text").find("input").val().trim();
		var updateTagCode = $(current).closest("tr").find("input[name='project_code']").val();
		var updateTagStatus  = $(current).closest("tr").find("input[name='is_active']").is(":checked");
		if(updateTagName.trim() != "") {
			saveTag(updateTagCode, updateTagName, updateTagStatus);
			cancelEditTag(current);
		}
		else {
			$(current).closest("tr").find(".td-tag-name-text").find("input").addClass("error-border");
			$(current).closest("tr").find(".td-tag-name-text").find("input").keyup(function(){
				$(this).removeClass("error-border");
			})
		}
	}

	function cancelEditTag(current) {
		$(current).closest("tr").find(".td-tag-name-label").removeClass("hide");
		$(current).closest("tr").find(".td-tag-name-text").addClass("hide");
	}

	function tagEditHoverInit() {
        $("#table-tags tbody tr").hover(function(){
            $("#table-tags tbody tr .fa-pencil").addClass('hide');
            $(this).find(".fa-pencil").toggleClass("hide");
        },
        function(){
            $("#table-tags tbody tr .fa-pencil").addClass('hide');
        });
        TooltipInit();
	}

	function updateActiveTagCount() {
		var tagCount = $("#table-tags").find("tr[data-active='1']").length;
		$(".tag-count").text(tagCount);
	}

	function highlightFinancialHeader() {
		$(".financial-config").each(function(){
			if($(this).find("input:checked").length > 0) {
				var divFor = $(this).attr("div-for");
				$("#"+divFor).closest(".financial-content").addClass("activated");
			}
			else {
				var divFor = $(this).attr("div-for");
				$("#"+divFor).closest(".financial-content").removeClass("activated");
			}
		})
	}

	function addNewRegistrationDetails() {
		if($(".registration-extra-details").find(".registration-details").length > 0) {
			var labelId = $(".registration-extra-details").find(".registration-details").last().find(".registration-label-id").val();
		}
		else {
			var labelId = 0;
		}
		labelId = Number(labelId) + 1;
		var row = `<div class="col-sm-12 form-group registration-details" style="padding: 0 4px;">
						<input type="text" class="form-control registration-label-id hide" value="${labelId}">
			    		<input type="text" class="form-control registraion-key" maxlength="30" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');"  placeholder="Label"/>
			    		<input type="text" class="form-control registraion-value" maxlength="150" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" placeholder="Detail"/>
			    		<i class="fa fa-minus-circle" role="button" onclick="deleteRegistraionDetails(this)" style="color: #dd4b39;position: absolute;margin-top: 10px;margin-left: -20px;"></i>
			    	</div>`
		$(".registration-extra-details").append(row);
	}

	function addNewContact(current) {
		var sequenceCount = $(".other_contact_details").find(".contact-details").length;
		if(sequenceCount == 0) {
			sequenceCount = 2;
		}
		else {
			sequenceCount = Number($(".other_contact_details").find(".contact-details").last().attr("data-sequence-id")) + 1;
		}
	  	var row = `<div class="contact-details" data-sequence-id = "${sequenceCount}">
	  					<div class="col-sm-12 line-divider"><lable style="color: #209be1;font-size: 13px; margin-left: 17px; padding-top: 8px; display: block;">OTHER CONTACT DETAILS</lable>
	  						<i class="fa fa-minus-circle delete-contact-container" role="button" onclick="deleteContactDetails(this)"></i>
						</div>
						<div class="floating-label col-sm-6" style="padding-left: 0;">
							<input class="form-control floating-input contact-person-name" id="id_enterprise-contact_person_${sequenceCount}" maxlength="48" name="enterprise-contact_person" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" placeholder=" " type="text">
							<label>Contact Person<span class="mandatory_mark">*</span></label>
						</div>
						<div class="floating-label col-sm-6" style="padding-left: 0; padding-right: 0;">
							<input class="form-control floating-input contact-person-email" id="id_enterprise-email_${sequenceCount}" maxlength="48" name="enterprise-email" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" placeholder=" " type="text" value="">
							<label>Email Address</label>
						</div>
						<div class="floating-label col-sm-6" style="padding-left: 0;">
							  <i class="fa fa-whatsapp" role="button" onclick="whatsappContactDetails(this)" data-tooltip="tooltip" title="Authorize this phone number to use XSassist Whatsapp Support"></i>
							<input class="form-control floating-input contact-person-number" id="id_enterprise-phone_${sequenceCount}" maxlength="18" name="enterprise-phone" onblur="return validateStringOnBlur(this, event, 'ph_number');" onkeypress="return validateStringOnKeyPress(this,event,'ph_number');" placeholder=" " type="text" value="">
							<label>Contact Number<span class="mandatory_mark">*</span></label>
							<span style="font-size: 11px;" class="contact-authorized hide">Contact authorized to use XSassist Whatsapp support</span>
						</div>
						<div class="floating-label col-sm-6" style="padding-left: 0; padding-right: 0;">
							<input class="form-control floating-input contact-person-fax" id="id_enterprise-fax_${sequenceCount}" maxlength="18" name="enterprise-fax" onblur="return validateStringOnBlur(this, event, 'ph_number');" onkeypress="return validateStringOnKeyPress(this,event, 'ph_number');" placeholder=" " type="text" value="">
							<label>Fax</label>
						</div>
					</div>`;

		$(current).closest(".address-container").find(".other_contact_details").append(row);
		TooltipInit();
	}

	function addNewAddress() {
		var row = `<label class="enterprice-item-heading">Other Contact Details</label>
				    <div class="row address-container">
						<div class="floating-label col-sm-12 remove-padding">
							<textarea class="form-control floating-input floating-textarea" placeholder=" " rows="3"></textarea>
							<label>Address</label>
						</div>
						<div class="floating-label col-sm-4" style="padding-left: 0;">
							<input class="form-control floating-input" maxlength="48" name="enterprise-city" onblur="return validateStringOnBlur(this, event, 'alphaSpecialCharMini');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialCharMini');" placeholder=" " type="text" value="">
							<label>City</label>
						</div>
						<div class="floating-label col-sm-4" style="padding-left: 0;">
							<input class="form-control floating-input" maxlength="48" name="enterprise-state" onblur="return validateStringOnBlur(this, event, 'alphaSpecialCharMini');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialCharMini');" placeholder=" " type="text" value="">
							<label>State</label>
						</div>
						<div class="floating-label col-sm-4" style="padding-left: 0; padding-right: 0;">
							<input type="text" name="enterprise-pin_code" class="form-control floating-input" maxlength="6" onfocus="setNumberRangeOnFocus(this,6,0,true)" placeholder=" "  value="">
							<label>Pincode</label>
						</div>
						<div class="col-sm-12 line-divider">
						</div>
						<div class="floating-label col-sm-6" style="padding-left: 0;">
							<input class="form-control floating-input" maxlength="48" name="enterprise-contact_person" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" placeholder=" " type="text">
							<label>Contact Person</label>
						</div>
						<div class="floating-label col-sm-6" style="padding-left: 0; padding-right: 0;">
							<input class="form-control floating-input" maxlength="48" name="enterprise-email" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" placeholder=" " type="text" value="">
							<label>Email Address</label>
						</div>
						<div class="floating-label col-sm-6" style="padding-left: 0;">
							<input class="form-control floating-input" maxlength="18" name="enterprise-phone" onblur="return validateStringOnBlur(this, event, 'ph_number');" onkeypress="return validateStringOnKeyPress(this,event, 'ph_number');" placeholder=" " type="text" value="">
							<label>Contact Number</label>
						</div>
						<div class="floating-label col-sm-6" style="padding-left: 0; padding-right: 0;">
							<input class="form-control floating-input" maxlength="18" name="enterprise-fax" onblur="return validateStringOnBlur(this, event, 'ph_number');" onkeypress="return validateStringOnKeyPress(this,event, 'ph_number');" placeholder=" " type="text" value="">
							<label>Fax</label>
						</div>
						<div class="other_contact_details"></div>
						<div class="col-sm-12 text-right remove-padding">
							<span role="button" onclick="addNewContact(this)"><a>+ Add Contact</a></span>
						</div>
					</div>`;
		$(".other_address_details").append(row);
	}


	$(document).ready(function () {
		const fetchAndPopulateLocations = async () => {
			const locationListAll = await getLocations();
			const fromLocation = "from";
			locationList = locationListAll[fromLocation]
			$('#location-count').text(locationList.length);
		}
		fetchAndPopulateLocations();
		getProjects().then(projects => {
    const projectSelect = document.getElementById("projectSelect");

    function buildSelectOptions(projects, parentId, prefix, level) {
    projects.forEach(project => {
        if (project.parent_id === parentId) {
            const option = document.createElement("option");
            option.value = project.id;
            option.textContent = `|${prefix}  ${project.name}`;
            // Check if it's the third level
            if (level === 4) {
                option.disabled = true;
            }
            projectSelect.appendChild(option);
            buildSelectOptions(projects, project.id, `${prefix}-----`, level + 1);
        }
    });
}

buildSelectOptions(projects.projects, 0, "", 1);

});
		$('#save_project_group').click(function(){
			var ControlCollections = [{
		            controltype: 'dropdown',
		            controlid: 'projectSelect',
		            isrequired: true,
		            errormsg: 'Project Head is required.'
		        },
		        {
		            controltype: 'textbox',
		            controlid: 'id_project_group-name',
		            isrequired: true,
		            errormsg: 'Project Name is required.'
		        },
		        {
		            controltype: 'textbox',
		            controlid: 'id_project_code',
		            isrequired: true,
		            errormsg: 'Project code is required.'
		        }
<!--		        {-->
<!--		            controltype: 'textbox',-->
<!--		            controlid: 'id_project_description',-->
<!--		            isrequired: true,-->
<!--		            errormsg: 'Project description is required.'-->
<!--		        }-->
		    ];
		    var result = JSCustomValidator.JSvalidate(ControlCollections);
		    if(result){
			const parent_project = $('#projectSelect').val()
			const project_name = $('#id_project_group-name').val();
			const project_code = $('#id_project_code').val();
			const project_desc = $('#id_project_description').val();
			const json_project_create = {"project_code": project_code, "project_name": project_name, "description": project_desc, "parent_project_id": parent_project}
			$("#loading").show();
			$.ajax({
				url: " /erp/masters/json/projects/add/",
				type: "POST",
				dataType: "json",
				data: json_project_create,
				success: function(response) {
					if(response.response_code == 200){
					$("#loading").hide();
						swal("","Project created Successfully","success");
						$("#modal_project_creation").modal("hide");
						location.reload();
					}
					else if(response.response_code == 400){
					$("#loading").hide();
                			swal({title: "", text: response.custom_message, type: "error"});
                    }
				}

			});
			}
		});
        $('#id_enterprise-logo').awesomeCropper(
        	{ debug: false, isAspectRatio: false }
        );
        
		$(".profile_image").click(function(){
			$("#id_enterprise-logo").next('.awesome-cropper').find('input').click();
		});
		SetProfileImage();
    	MovableMultiselect();
    	changeLogActivityInit();
    	constructDateFormatField();
    	listTableHoverIconsInit('export_history');
    	$('#id_enterprise-is_negative_stock_allowed').change(function() {
    	    if (!$(this).is(':checked')) {
    	        validateToNotAllowNegativeStock();
    	    }
    	});

    	$(".automate_gate_inward").click(function(){
    		if($(this).hasClass("item-disabled")) {
    			$(".editable_gate_inward").addClass("item-disabled hide");
    		}
    		else {
    			$(".editable_gate_inward").removeClass("hide");
    		}
    	})
        tourInit();
    	$("#id_enterprise-setting_flags_1").next("label").append('<span class="qtip-question-mark" id="label_flag_1"></span>');
    	$("#id_enterprise-setting_flags_2").next("label").append('<span class="qtip-question-mark" id="label_flag_2"></span>');
    	$("#id_enterprise-setting_flags_3").next("label").append('<span class="qtip-question-mark" id="label_flag_3"></span>');
    	$("#id_enterprise-setting_flags_4").next("label").append('<span class="qtip-question-mark" id="label_flag_4"></span>');

		// TODO #7966: Add the label flags for gate inward no flags.
		QTipInit();
		editableByHoverInit();
		$(".modal-fullscreen .modal-dialog, .modal-fullscreen .modal-content").css("height", $(window).height());
		$(window).resize(function(){
			$(".modal-fullscreen .modal-dialog, .modal-fullscreen .modal-content").css("height", $(window).height());
		});
		CKEDITOR.replace( 'purchase_editor', {
			height: '300px',
		});
		var url = window.location.href;
		if(url.indexOf("expense") >= 0) {
			$("#exp_configration_modal").modal("show");
			$("title").text("Expense Configuration");
		}
		else if(url.indexOf("purchase") >= 0) {
			$("#purchase_configration_modal").modal("show");
			$("title").text("PO Print Config");
			logoSliderChangeEvent();
			updateHsnconfig();
		}
		else if(url.indexOf("sales") >= 0) {
			$("#se_configration_modal").modal("show");
			$("title").text("Sales Estimate Print Config");
		}
		else if(url.indexOf("import") >= 0) {
			openEditableModal("import-container");
			$("title").text("Import Data");
		}
		else if(url.indexOf("export") >= 0) {
			openEditableModal("export-container");
			fetchExportDetails();
			$("title").text("Export Data");
			$("#loading").show();
			$( window ).resize();
		}
		else if(url.indexOf("erase") >= 0) {
			openEditableModal("erase-container");
			$("title").text("Erase Data");
		}
		else {
			$(".internal-tour").removeClass("hide");
			$("title").text("Enterprise Profile");
			if($("#enterprise_country_code").val() == "" || $("#enterprise_country_code").val() == "None"){
				$("#id_country-__prefix__-make_choices").val("IN")
			}
			if($("#enterprise_state_code").val() == "" || $("#enterprise_state_code").val() == "None"){
				$("#id_state-__prefix__-make_choices").val("33.0").trigger("change");
			}
			updateCountryValue();
		}
		tourInit();
		tagEditHoverInit();
		updateActiveTagCount();
    });

    function QTipInit() {
    	$('#label_flag_1, #label_flag_11').qtip({
		   	content: {
		        text: "<p>Internal Control feature enables scrutiny of all Purchase Invoices captured through Purchase GRN, highlighting deviations in prices, quantity & quality of every Purchase, by comparing documents & profiles relevant to it (Purchase Order, Supplier Invoice, Approved prices, etc.)</p>",
		        title: 'Internal Control'
		    }
		});
		$('#label_flag_2, #label_flag_22').qtip({
		   	content: {
		        text: "<p>Deviations highlighted while scrutinising Purchase shall take only adverse deviations into account.</p>",
		        title: 'Ignore credit note for purchase'
		    }
		});
		$('#label_flag_3, #label_flag_33').qtip({
		   	content: {
		        text: "<p>System shall automatically create Credit/Debit Note & respective Vouchers for Purchase Invoices only (for now) by comparing documents & profiles relevant to the Purchase.</p>",
		        title: 'Generate Debit & Credit Notes for Purchase automatically'
		    }
		});
		$('#label_flag_4, #label_flag_44').qtip({
		   	content: {
		        text: "<p>System automatically generates Vouchers while capturing Purchase, Sales & Expenses. These transactions may need to be scrutinised for Accounting propriety which can be achieved through this feature. Enabling this feature will generate such Vouchers as drafts, which shall be actually accounted only after Approval of a qualified User.</p>",
		        title: 'Enable scrutiny of auto-generated vouchers'
		    }
		});
    }

	var ImportTallyCount = $("#tally_xml_import_table tbody").find("tr").length;
    function addNewImportTally() {
    	var count = ++ImportTallyCount;
    	var row = '<tr class="tr_tally_import">\
					<td>\
						<input class="filestyle col-sm-12" data-buttonBefore="false" name="tallyxmlfile[]" type="file" id="tallyxmlfileUpload_'+count+'" onchange="validateFileFormat(this);" />\
					</td>\
					<td class="text-center" style="width: 60px;">\
						<i class="fa fa-trash-o" role="button" onclick="deleteImportedTallyFile(this);"></i>\
					</td>\
				</tr>';
				$("#tally_xml_import_table tbody").append(row);
				$("#tallyxmlfileUpload_"+ count).filestyle({buttonBefore: true});
    }

    function deleteImportedTallyFile(evt) {
    	swal({
			title: "Are you sure?",
			text: "Do you want to Delete this file?",
			type: "warning",
			showCancelButton: true,
			confirmButtonColor: "#209be1",
			confirmButtonText: "Yes, delete it!",
			closeOnConfirm: true
		},
		function(){
		    $(evt).closest("tr").remove();
		});
    }

    function saveTallyxmlUpload(){
        $("#tally_xml_file").submit();
    }

    $("#tallyhidedone").click(function(){
		$("#tally_xml_import_table tbody").find("tr:gt(0)").remove();
    	$("#tallyxmlfileUpload").filestyle('clear');
    	$(".tallyImportProgress").addClass("hide");
    	$("#tally_xml_file").removeClass("hide");
    	$("#txttallyImportProgreeText").val("");
		$(this).addClass('hide');
		$("#ImportTallyXml").modal('hide');
	});

	$("#tally_xml_file").submit(function(){
		$("#loading").show();
		var formData = new FormData($(this)[0]);
		setTimeout(function(){
		    $.ajax({
		        url:"/erp/admin/json/tallyxmlImportSave/",
		        type: 'POST',
		        data: formData,
		        async: false,
		        success: function (data) {
			        if (data.response_code == 200) {
			            swal({
							title: "Data Saved Successfully",
							text: "Your Tally Data file has been successfully Saved.<br>Click '<b>Next</b>' to proceed with the Import.",
							type: "success",
							showCancelButton: true,
							confirmButtonColor: "#209be1",
							confirmButtonText: "Next, Import it!",
							closeOnConfirm: true
						},
						function(){
						    $("#tally_xml_file").addClass("hide");
						    $("#tallyunprocessfile").addClass("hide");
						    $(".tallyImportProgress").removeClass("hide");
					        FinalSaveTallyxmlUpload();
						});
					}
					$("#loading").hide();
		        },
		        error : function(xhr,errmsg,err) {
		            console.log(xhr.status + ": " + xhr.responseText);
		            $("#loading").hide();
		        },
		        cache: false,
		        contentType: false,
		        processData: false
		    });
	    },10);

    });
	var XmlStatusInterval;
    function FinalSaveTallyxmlUpload(){
        XmlStatusInterval = setInterval(XmlStatusUpdate, 3000);
        var opening_date = $("#opening_date").val();
        $.ajax({
            url:"/erp/admin/json/tallyxmlImport/",
            type: 'POST',
            data: {opening_date: opening_date},
            async: true,
            success: function (response) {
                textareaProgress.value+=response['response']['Message'];
                textareaProgress.scrollTop = textareaProgress.scrollHeight;
                clearInterval(XmlStatusInterval);
                $("#tallyhidedone").removeClass("hide");
                $(".tallyImportProgress .processing").addClass("hide");
            },
            error : function(xhr,errmsg,err){
                console.log(xhr.status + ": " + xhr.responseText);
            },
        });
    }

	var textareaProgress = document.getElementById('txttallyImportProgreeText');

    function XmlStatusUpdate(){
        $.ajax({
            url:"/erp/admin/json/tallyxmlMessage/",
            type: 'POST',
            async: true,
            success: function (response) {
                if (response.response_code == 200) {
		            if(response.message_list.length > 0) {
		                $.each(response.message_list, function (i, item) {
		                    textareaProgress.value+=item.message;
                            textareaProgress.scrollTop = textareaProgress.scrollHeight;
		                });
		            }
			    }
            },
            error : function(xhr,errmsg,err){
                console.log(xhr.status + ": " + xhr.responseText);
            },
            cache: false,
            contentType: false,
            processData: false
        });
    }

    function closeTallyxmlUpload() {
    	$("#tally_xml_import_table tbody").find("tr:gt(0)").remove();
    	$("#tallyxmlfileUpload").filestyle('clear');
    	$("#ImportTallyXml").modal("hide");
    }

    function validateFileFormat(browsedFile){
    	var fileExtension = ['xml'];
    	browsedfileid = $("#"+browsedFile.id);
    	if ($.inArray(browsedfileid.val().split('.').pop().toLowerCase(), fileExtension) == -1) {
			swal("Invalid File Format", "Please upload only "+fileExtension.join(', ')+" file format.", "warning");
			setTimeout(function(){
				browsedfileid.filestyle('clear');
			},200);
		}
		$(".tr_tally_import").each(function(){
			if(browsedFile.id != $(this).find(".filestyle").attr("id")) {
				if($(this).find(".filestyle").val() == 	browsedfileid.val()) {
					var filename = browsedfileid.val().replace(/C:\\fakepath\\/i, '')
					setTimeout(function(){
						swal({
								title: "",
								text: "The <b>"+filename+"</b> file has already been attached.<br />Please choose another file.",
								type: "warning"
						});
						browsedfileid.filestyle('clear');
					},200);
				}
			}
			
		})
    }

    function validateExcelFileFormat(browsedFile) {
    	var fileExtension = ['xlsx','csv'];
    	browsedfileid = $("#"+browsedFile.id);
    	if ($.inArray(browsedfileid.val().split('.').pop().toLowerCase(), fileExtension) == -1) {
			swal("Invalid File Format", "Please upload only "+fileExtension.join(', ')+" file format.", "warning");
			setTimeout(function(){
				browsedfileid.filestyle('clear');
			},200);
		}
    }

    function MovableMultiselect() {    	
		$("#id_enterprise-claim_heads, #id_enterprise-expense_heads").multi();
	}

	$(".remove_image").click(function(){
		$(".cropped_image, #id_enterprise-logo").removeAttr('src');
		$("#id_enterprise-logo").val("");
		$("#browsed_image_file_name").val('');
		$('.btn.profile_image').removeClass('hide');
		$(this).addClass('hide');
	});
	
	setTimeout(function(){
		$( ".progress" ).next('.profile_image').hover(function() {
			var setWidth = $(this).width();
			setWidth = Number((setWidth/2)+18)+"px";
			var setHeight = $(this).height();
			setHeight = Number((setHeight/2))+"px";
			$( ".progress" ).next('.profile_image').addClass('hover-bg');
			$( ".upload_camera").css({left: setWidth, top: setHeight});
			$(".upload_camera").removeClass('hide');
		}, function() {
			$( ".progress" ).next('.profile_image').removeClass('hover-bg');
			$(".upload_camera").addClass('hide');
		});
		  
		$(".upload_camera").hover(function() {
			$( ".progress" ).next('.profile_image').addClass('hover-bg');
			$(".upload_camera").removeClass('hide');
		}, function() {
			$( ".progress" ).next('.profile_image').removeClass('hover-bg');
			$(".upload_camera").addClass('hide');
		});
	},100);
	  
	function SetProfileImage(){
		var imageDataSrc = $("#id_enterprise-logo").val();
		if(imageDataSrc !="") {
			$(".cropped_image").attr('src',imageDataSrc);
			$('.btn.profile_image').addClass('hide');
			 $(".cropped_image, .remove_image").hover(function(){
			      $(".remove_image").removeClass("hide");
			}, function () {
			      $(".remove_image").addClass("hide");
		  	});
		}
	}	

	function tablesToExcelLoad() {
		$(".downloading-main-container").removeClass('hide').addClass('show');
		setTimeout(function(){
			tablesToExcel(['party_failed_import_table','failed_import_table','failed_item_table'], ['party','materials','ledger'], 'Import_Failed_Summary.xls', 'Excel');
		},100);
	}

	var tablesToExcel = (function() {
	    var uri = 'data:application/vnd.ms-excel;base64,'
	    , tmplWorkbookXML = '<?xml version="1.0"?><?mso-application progid="Excel.Sheet"?><Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet">'
	      + '<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office"><Author>Axel Richter</Author><Created>{created}</Created></DocumentProperties>'
	      + '<Styles>'
	      + '<Style ss:ID="Currency"><NumberFormat ss:Format="Currency"></NumberFormat></Style>'
	      + '<Style ss:ID="Date"><NumberFormat ss:Format="Medium Date"></NumberFormat></Style>'
	      + '</Styles>'
	      + '{worksheets}</Workbook>'
	    , tmplWorksheetXML = '<Worksheet ss:Name="{nameWS}"><Table>{rows}</Table></Worksheet>'
	    , tmplCellXML = '<Cell{attributeStyleID}{attributeFormula}><Data ss:Type="{nameType}">{data}</Data></Cell>'
	    , base64 = function(s) { return window.btoa(unescape(encodeURIComponent(s))) }
	    , format = function(s, c) { return s.replace(/{(\w+)}/g, function(m, p) { return c[p]; }) }
	    return function(tables, wsnames, wbname, appname) {
	      var ctx = "";
	      var workbookXML = "";
	      var worksheetsXML = "";
	      var rowsXML = "";
	       if($('ul.nav-tabs li.active a').attr('id') == 'tab1_view') {
				oSettings[0]._iDisplayLength = oSettings[0].fnRecordsTotal();
		        oTable.draw();
		    }
	      for (var i = 0; i < tables.length; i++) {
	        if (!tables[i].nodeType) tables[i] = document.getElementById(tables[i]);
	        for (var j = 0; j < tables[i].rows.length; j++) {
	          rowsXML += '<Row>'
	          for (var k = 0; k < tables[i].rows[j].cells.length; k++) {
	            var dataType = tables[i].rows[j].cells[k].getAttribute("data-type");
	            var dataStyle = tables[i].rows[j].cells[k].getAttribute("data-style");
	            var dataValue = tables[i].rows[j].cells[k].getAttribute("data-value");
	            dataValue = (dataValue)?dataValue:tables[i].rows[j].cells[k].innerHTML;
	            var dataFormula = tables[i].rows[j].cells[k].getAttribute("data-formula");
	            dataFormula = (dataFormula)?dataFormula:(appname=='Calc' && dataType=='DateTime')?dataValue:null;
	            ctx = {  attributeStyleID: (dataStyle=='Currency' || dataStyle=='Date')?' ss:StyleID="'+dataStyle+'"':''
	                   , nameType: (dataType=='Number' || dataType=='DateTime' || dataType=='Boolean' || dataType=='Error')?dataType:'String'
	                   , data: (dataFormula)?'':dataValue
	                   , attributeFormula: (dataFormula)?' ss:Formula="'+dataFormula+'"':''
	                  };
	            rowsXML += format(tmplCellXML, ctx);
	          }
	          rowsXML += '</Row>'
	        }
	        ctx = {rows: rowsXML, nameWS: wsnames[i] || 'Sheet' + i};
	        worksheetsXML += format(tmplWorksheetXML, ctx);
	        rowsXML = "";
	      }

	      ctx = {created: (new Date()).getTime(), worksheets: worksheetsXML};
	      workbookXML = format(tmplWorkbookXML, ctx);

	      	if($('ul.nav-tabs li.active a').attr('id') == 'tab1_view') {
	      		$("#pagination-select").trigger('change');
	    	}

	      var link = document.createElement("A");
	      link.href = uri + base64(workbookXML);
	      link.download = wbname || 'Workbook.xls';
	      link.target = '_blank';
	      document.body.appendChild(link);
	      link.click();
	      document.body.removeChild(link);
	      $(".downloading-main-container").removeClass('show').addClass('hide');
	    }
	})();

	function validateToNotAllowNegativeStock() {
		$.ajax({
			url: "/erp/admin/json/count_of_negative_stock_materials/",
			method: "POST",
			data:{},
			success: function(response) {
				if (response.response_message =="Success") {
				    if(Number(response.count) > 0) {
				        swal({title: "", text: response.custom_message, type: "warning",
					        showCancelButton: true,
                            confirmButtonColor: "#209be1",
                            confirmButtonText: "Yes, do it!",
                            closeOnConfirm: true,
                            closeOnCancel: true
                        },
                        function(isConfirm){
                            if (!isConfirm) {
								$('#id_enterprise-is_negative_stock_allowed').prop('checked', true);
                            }
                        });
				    }
				 } else {
					swal({title: "", text: response.custom_message, type: "error"});
				 }
			},
			error: function (xhr, errmsg, err) {
	            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
	        }
        });
	}
	$("#xlhideUploadButton").click(function(){
        ga('send', 'event', 'Enterprise', 'Import via Excel', $('#enterprise_label').val(), 100);
	});
	$("#tallyxmlhideUploadButton").click(function(){
        ga('send', 'event', 'Enterprise', 'Import via Tally XML', $('#enterprise_label').val(), 100);
        $("#loading").show();
	});
	$("#save_enterprise").click(function(){

	});
	
	$("#id_enterprise-setting_flags_2, #id_enterprise-setting_flags_3").change(function(){
		if($(this).is(":checked")){
			$("#id_enterprise-setting_flags_1").prop("checked", true);
		}
	});
	
	$("#id_enterprise-setting_flags_1").change(function(){
		if(!$(this).is(":checked")){
			$("#id_enterprise-setting_flags_2, #id_enterprise-setting_flags_3").prop("checked", false);
		}
	});

	var tour = new Tour({
	    storage : false,
	    backdrop: true,
	    steps: [
	    {
	      element: "#enterprise-brand",
	      placement: "right",
	      title: "Enterprise Brand",
	      content: "Organization's Logo, Name and code ",
	    },
	    {
	      element: "#cont-details",
	      placement: "bottom",
	      title: "Contact Details",
	      content: "Contacts profiled are categorized as primary and other.<br>To view other contacts click 'Show More'.",
	    },
	    {
	      element: "#fiscal-config",
	      placement: "right",
	      title: "Fiscal Configurations",
	      content: "Fiscal and Financial profile information of the Enterprise",
	    },
	    {
	      element: "#edit-profile",
	      placement: "bottom",
	      content: "Edit Enterprise Profile",
	    },
	    {
	      element: "#preferences",
	      placement: "left",
	      title: "Preferences",
	      content: "An Overview of how system looks based on the set preferences. Click to Edit",
	    },
	    {
	      element: "#other-config",
	      placement: "bottom",
	      title: "Other Configurations",
	      content: "Module specific preferences can be configured through this menu",
	    },
	 ],
	   onStart: function (tour) {
			$("#edit-profile").addClass("mandate-visible");
	   },
	   onEnd: function (tour) {
			$("#edit-profile").removeClass("mandate-visible");
	   },
	});

	function tourInit(){
		$('#int-tour').click(function() {
		    tour.restart();
	  	});
	}



	function constructDateFormat(){
		var dateFormat = "";
		var timeFormat = "";
		var dateFormatPy = "";
		var timeFormatPy = "";
		var is12HoursFormat = false;
		$(".date-formatter").each(function(){
			dateFormat += $(this).find("option:selected").attr("data-value");
			dateFormatPy += $(this).val();
		});
		$(".time-formatter").each(function(){
			var lastDataValue = $(this).find("option:selected").attr("data-value");
			if(lastDataValue !="") {
				timeFormat += lastDataValue+":";
				timeFormatPy += $(this).val();
				if(lastDataValue == "hh") {
					is12HoursFormat = true;
				}
			}
			if(timeFormatPy.indexOf(":") == 0) {
				timeFormatPy = timeFormatPy.substring(1);
			}
		});
		timeFormat = timeFormat.substring(0, timeFormat.length - 1);

		if(is12HoursFormat) {
			timeFormatPy+= " %p";
			timeFormat+= " A";
		}
		var dateTimeFormatPy = dateFormatPy.trim()+" "+timeFormatPy.trim();
		if(timeFormat == "") timeFormat = " ";
		$(".dateFormat-preview").text(moment().format(dateFormat));
		$(".timeFormat-preview").text(moment().format(timeFormat));
		$("#dateFormatPy").val(dateTimeFormatPy.trim())
	}

	function constructDateFormatField(){
		var dateFormat = $("#dateFormatPy").val();
		var format = "";
		var formattedArray = [];
		for ( var i = 0; i < dateFormat.length; i++ ) {
			format += dateFormat[i];
			if(format == "%" || format == " %" || format == ",") {
				continue;
			}
			formattedArray.push(format);
			format = "";
		}
		var list = "";
		formattedArray.forEach(function(item) {
			if(list.length < 10) {
				if(item == "%d") {
					$(".date-formatter-date").val(item);
					list += "1,"
				}
				else if(item == "%b" || item == "%B" || item == "%m") {
					$(".date-formatter-month").val(item);
					list += "3,"
				}
				else if(item == "%y" || item == "%Y") {
					$(".date-formatter-year").val(item);
					list += "5,"
				}
				else if(["/", ",", "-", ", ",".", " "].indexOf(item) >=0) {
					if(list.indexOf("2,") == -1) {
						list += "2,"
						$('.date-formatter-seperator1').val(item);
					}
					else {
						list += "4,"
						$('.date-formatter-seperator2').val(item);
					}
				}
			}
			if(item == "%H") {
				$(".time-formatter-hour").val(" %H");
			}
			if(item == "%I") {
				$(".time-formatter-hour").val(" %I");
			}
			else if(item == "%M") {
				$(".time-formatter-minute").val(":%M");
			}
			else if(item == "%S") {
				$(".time-formatter-second").val(":%S");
			}
		});
		for (var i = 1; i <= 5; i++) {
			if(list.indexOf(i) == -1) list += i+",";
		}
		var orderArray = list.split(',');
		var listArray = $('#dateFormatter td');
		for (var i = 0; i < orderArray.length; i++) {
	   		$('#dateFormatter tr').append(listArray[orderArray[i]-1]);
		}
		setTimeout(function(){
			$("#dateFormatter").sortable({
			    items: 'tr td',
			    cursor: 'pointer',
			    axis: 'x',
			    dropOnEmpty: false,
			    start: function (e, ui) {
			        ui.item.addClass("selected");
			    },
			    stop: function (e, ui) {
			       constructDateFormat();
			    }
			});
		},2000);

		$(".date-formatter, .time-formatter").change(function(){
			constructDateFormat();
		});
		constructDateFormat();
	}


function logoSliderChangeEvent() {
	var slider = document.getElementById("id_ith-logo_size");
	var output = document.getElementById("logo_size_value");
	output.innerHTML = slider.value;
	slider.oninput = function() {
	  output.innerHTML = this.value;
	  $(".pdf_company_logo").css({height: this.value});
	}
}

function updateHsnconfig() {
	$("#id_enterprise-include_hsnsac").change(function(){
		if($(this).is(":checked")) {
			$("#id_enterprise-include_hsnsac_column").removeAttr("disabled");
		}
		else {
			$("#id_enterprise-include_hsnsac_column").attr("disabled", "disabled").prop("checked", false);
		}
	});
}

function updateStateValue() {
	var selectedState = $("#id_state-__prefix__-make_choices").find("option:selected").text();
	$("#id_enterprise-state").val(selectedState);
}

function updateCountryValue(loadtype='') {
	var selectedCountry = $("#id_country-__prefix__-make_choices").val();
	if(selectedCountry == "IN") {
		$(".address-container-state-text").addClass("hide");
		$(".address-container-state-list").removeClass("hide");
		$("#id_enterprise-state").val($("#id_state-__prefix__-make_choices option:selected").text());
	}
	else {
		$(".address-container-state-text").removeClass("hide");
		$(".address-container-state-list").addClass("hide");
		if(loadtype == "onchange") {
			$("#id_enterprise-state").val("");
		}
	}
}
function submitFormWithUserAndEnterprise() {
    $('#userId').val($('#login_user_id').val());
    $('#enterpriseId').val($('#enterprise_id').val());
    $('#locationForm').submit();
}

</script>

{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
{% endblock %}
