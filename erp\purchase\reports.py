"""
"""
from datetime import datetime
from decimal import Decimal

from erp.masters.backend import LocationService
from erp.models import MRP_Materials, ClosingStock
import simplejson
from django.http import HttpResponse
from django.template.response import TemplateResponse
from sqlalchemy import and_, or_, func

from erp import helper, DEFAULT_MAKE_ID
from erp.auth import ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import RequestHand<PERSON>
from erp.dao import executeQuery
from erp.models import PurchaseOrder, PurchaseOrderMaterial, ReceiptMaterial, Receipt, InvoiceMaterial, Invoice, \
	Material, CatalogueMaterial, Make, MaterialMakeMap, PurchaseOrderMaterialDeliverySchedules, TransferDetails, \
	StockTransfer
from erp.production.backend import ProductionService
from erp.properties import PURCHASE_ORDER_MASTER_REPORT_TEMPLATE, PURCHASE_ORDER_MATERIAL_WISE_REPORT_TEMPLATE, \
	PURCHASE_ORDER_MATERIAL_HISTORY_REPORT_TEMPLATE, SHORTAGE_LIST_REPORT_TEMPLATE, TEMPLATE_TITLE_KEY, \
	SHORTAGE_LIST_PP_REPORT_TEMPLATE
from erp.purchase import logger, POStatus
from erp.purchase.service import PurchaseService
from erp.stores.backend import StoresDAO, StoresService, StockTransferService
from settings import SQLASession
from util.api_util import JsonUtil, response_code

__author__ = 'kalaivanan'

FIELD_SEPARATOR = "[:]"
PACKET_SEPARATOR = "[::]"


class POReportElement(object):
	"""

	"""

	def __init__(
			self, indent_no=None, indent_date=None, po_status="-", draft_po_no=None, draft_date=None, po_no="",
			po_date=None, po_value=None, po_due_on=None, project=None, supplier=None, material_status="-",
			delivery_status="-", delivery_overdue=0):
		self.indent_no = indent_no
		self.indent_date = indent_date
		self.po_status = po_status
		self.draft_po_no = draft_po_no
		self.draft_date = draft_date
		self.po_no = po_no
		self.po_date = po_date
		self.po_value = po_value
		self.po_due_on = po_due_on
		self.project = project
		self.supplier = supplier
		self.material_status = material_status
		self.delivery_status = delivery_status
		self.delivery_overdue = delivery_overdue


class MaterialWisePOReportElement(object):
	"""

	"""

	def __init__(
			self, material_name=None, indent_no=None, indent_date=None, indent_qty=None, po_status="-",
			draft_po_no=None, draft_date=None, draft_qty=None, po_no="", po_date=None, po_qty=None, po_value=None,
			po_due_on=None, project=None, supplier=None, material_status="-", delivery_status="-", delivery_overdue=0):
		self.material_name = material_name
		self.indent_no = indent_no
		self.indent_date = indent_date
		self.indent_qty = indent_qty
		self.po_status = po_status
		self.draft_po_no = draft_po_no
		self.draft_date = draft_date
		self.draft_qty = draft_qty
		self.po_no = po_no
		self.po_date = po_date
		self.po_qty = po_qty
		self.po_value = po_value
		self.po_due_on = po_due_on
		self.project = project
		self.supplier = supplier
		self.material_status = material_status
		self.delivery_status = delivery_status
		self.delivery_overdue = delivery_overdue

	def __repr__(self):
		return "%s" % self.__dict__


class MaterialHistoryReportElement(object):
	"""

	"""

	def __init__(
			self, receipt_no="", receipt_date=None, receipt_qty=0, issue_qty=0, rate=0, receipt_value=0, issue_value=0,
			supplier_name="", project="", receipt_id="", receipt_type="", invoice_type="", edit_link=None, location=None):
		self.receipt_no = receipt_no
		self.receipt_date = receipt_date
		self.receipt_qty = receipt_qty
		self.issue_qty = issue_qty
		self.rate = rate
		self.receipt_value = receipt_value
		self.issue_value = issue_value
		self.supplier_name = supplier_name
		self.project = project
		self.receipt_id = receipt_id
		self.receipt_type = receipt_type
		self.invoice_type = invoice_type
		self.edit_link = edit_link
		self.location = location

	def __repr__(self):
		return "%s" % self.__dict__


class MaterialShortageReportElement(object):
	"""

	"""

	def __init__(
			self, drawing_no="", make="", unit="", rate=0.00, present_qty=0.00, required_qty=0.00,
			shortage_qty=0.00, present_value=0.00, required_value=0.00, shortage_value=0.00, contains=""):
		self.drawing_no = drawing_no
		self.make = make
		self.unit = unit
		self.rate = rate
		self.present_qty = present_qty
		self.required_qty = required_qty
		self.shortage_qty = shortage_qty
		self.present_value = present_value
		self.required_value = required_value
		self.shortage_value = shortage_value
		self.contains = contains

	def __repr__(self):
		return "%s" % self.__dict__


def generatePurchaseMasterReport(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)

	since, till = JsonUtil.getDateRange(rh=request_handler)

	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	purchase_service = PurchaseService()
	purchase_orders = purchase_service.purchase_dao.getPurchaseOrders(
		enterprise_id=enterprise_id, since=since, till=till)
	logger.info(
		"Generating a master report of %s Purchase Orders for Enterprise %s" % (len(purchase_orders), enterprise_id))
	po_report = []
	today = datetime.today()
	for po in purchase_orders:
		po_status = purchase_service.getPurchaseStatus(today=today, purchase_order=po)

		po_report_element = POReportElement(
			indent_no="-" if po.indent is None else po.indent.getCode(),
			indent_date="-" if po.indent is None else po.indent.raised_date, po_status=po.getStatus(),
			draft_po_no=po.po_id, draft_date=po.drafted_on, po_no=po.getInternalCode(), po_date=po.approved_on,
			po_due_on=po.delivery, po_value=po.total, material_status=po_status['material_status'],
			project=po.project.name if po.project_code else "",
			supplier=po.supplier.name if po.supplier else "",
			delivery_status=po_status['delivery_status'], delivery_overdue=po_status['delivery_overdue'])
		po_report.append(po_report_element)

	return TemplateResponse(template=PURCHASE_ORDER_MASTER_REPORT_TEMPLATE, request=request, context={
		'report_rows': po_report, 'from_date': since.strftime("%Y-%m-%d"), 'to_date': till.strftime("%Y-%m-%d"),
		TEMPLATE_TITLE_KEY: "P.O Wise Reports"})


def getValidPOMDeliveryDate(po_id=None, item_id=None, make_id=None, enterprise_id=None, po_delivery=None):
	"""
	get the delivery date also considering with delivery schedule
	:param po_id:
	:param item_id:
	:param make_id:
	:param enterprise_id:
	:param po_delivery:
	:return date:
	"""
	delivery_by_schedule_query = SQLASession().query(PurchaseOrderMaterialDeliverySchedules.due_date).filter(
		PurchaseOrderMaterialDeliverySchedules.po_id == po_id,
		PurchaseOrderMaterialDeliverySchedules.item_id == item_id,
		PurchaseOrderMaterialDeliverySchedules.make_id == make_id,
		PurchaseOrderMaterialDeliverySchedules.enterprise_id == enterprise_id,
		PurchaseOrderMaterialDeliverySchedules.due_date >= datetime.today().strftime('%Y-%m-%d 00:00:00')).order_by(
		PurchaseOrderMaterialDeliverySchedules.due_date).first()
	return delivery_by_schedule_query[0] if delivery_by_schedule_query is not None else po_delivery


def getMaxPOMDeliveryDate(po_id=None, item_id=None, make_id=None, enterprise_id=None):
	"""
	get the delivery date also considering with delivery schedule
	:param po_id:
	:param item_id:
	:param make_id:
	:param enterprise_id:
	:return date:
	"""
	delivery_by_schedule_query = SQLASession().query(PurchaseOrderMaterialDeliverySchedules.due_date).filter(
		PurchaseOrderMaterialDeliverySchedules.po_id == po_id,
		PurchaseOrderMaterialDeliverySchedules.item_id == item_id,
		PurchaseOrderMaterialDeliverySchedules.make_id == make_id,
		PurchaseOrderMaterialDeliverySchedules.enterprise_id == enterprise_id).order_by(
		-PurchaseOrderMaterialDeliverySchedules.due_date).first()
	return delivery_by_schedule_query[0] if delivery_by_schedule_query is not None else None


def generateMaterialWisePOReport(request):
	request_handler = RequestHandler(request)
	from_date, to_date = JsonUtil.getDateRange(rh=request_handler)

	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	po_materials = SQLASession().query(PurchaseOrderMaterial).join(PurchaseOrderMaterial.purchase_order).filter(
		PurchaseOrder.approved_on >= from_date, PurchaseOrder.approved_on <= to_date, PurchaseOrder.status > -1,
		PurchaseOrder.status < 3, PurchaseOrder.enterprise_id == enterprise_id, PurchaseOrder.type.in_(('0', '1'))).order_by(
		PurchaseOrderMaterial.item_id, PurchaseOrderMaterial.po_id).all()

	logger.info("Generating a Material Wise report of %s Purchase Orders for Enterprise %s" % (
		len(po_materials), enterprise_id))
	po_material_report_rows = []
	for po_material in po_materials:
		material_status = POStatus.PENDING
		delivery_status = POStatus.ON_TRACK
		delivery_overdue = 0
		delivery = po_material.purchase_order.delivery \
			if po_material.purchase_order.delivery is True and po_material.purchase_order.delivery != '0000-00-00 00:00:00' \
			else po_material.purchase_order.order_date
		delivery = getValidPOMDeliveryDate(
			po_id=po_material.purchase_order.po_id, item_id=po_material.item_id, make_id=po_material.make_id,
			enterprise_id=po_material.enterprise_id, po_delivery=delivery)
		po_due_on = getMaxPOMDeliveryDate(
			po_id=po_material.purchase_order.po_id, item_id=po_material.item_id, make_id=po_material.make_id,
			enterprise_id=po_material.enterprise_id)
		if po_material.isMaterialSupplied():
			material_status = POStatus.SUPPLIED
			if po_material.materialLastReceivedOn() > delivery:
				delivery_status = POStatus.DELAYED
				delivery_overdue = (po_material.materialLastReceivedOn() - delivery).days
			else:
				delivery_status = POStatus.ON_TIME
		elif len(po_material.material_received_against_po) > 0:
			material_status = POStatus.PARTIAL
			if po_material.materialLastReceivedOn() >= delivery:
				delivery_status = POStatus.DELAYED
				delivery_overdue = (datetime.today() - delivery).days
		else:
			if (datetime.today() - delivery).days > 0:
				delivery_overdue = (datetime.today() - delivery).days
				delivery_status = POStatus.DELAYED
		make = helper.constructDifferentMakeName(po_material.material.makes_json)
		description = po_material.material.name
		if po_material.material.drawing_no:
			description += " - %s" % po_material.material.drawing_no
		if make:
			description += " [%s]" % make
		po_material_report_element = MaterialWisePOReportElement(
			material_name=description,
			indent_no="-" if po_material.purchase_order.indent is None else po_material.purchase_order.indent.getCode(),
			indent_date="-" if po_material.purchase_order.indent is None else po_material.purchase_order.indent.raised_date,
			indent_qty=0 if po_material.purchase_order.indent is None else po_material.getIndentQuantity(),
			po_status=po_material.purchase_order.getStatus(), draft_po_no=po_material.purchase_order.po_id,
			draft_date=po_material.purchase_order.drafted_on, po_no=po_material.purchase_order.getInternalCode(),
			po_date=po_material.purchase_order.approved_on, po_qty=po_material.quantity,
			po_due_on=po_due_on, po_value=po_material.getMaterialValueWithoutTax(),
			material_status=material_status, delivery_status=delivery_status,
			project=po_material.purchase_order.project.name if po_material.purchase_order.project else "",
			supplier=po_material.purchase_order.supplier.name if po_material.purchase_order.supplier else "",
			delivery_overdue=delivery_overdue)
		po_material_report_rows.append(po_material_report_element)

	return TemplateResponse(template=PURCHASE_ORDER_MATERIAL_WISE_REPORT_TEMPLATE, request=request, context={
		'report_rows': po_material_report_rows, 'from_date': from_date.strftime("%Y-%m-%d"),
		'to_date': to_date.strftime("%Y-%m-%d"), TEMPLATE_TITLE_KEY: "Material Reports"})


def generateMaterialWiseHistoryReport(request):
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)

	item_details = request_handler.getPostData('item_details')
	project_id = request_handler.request.POST.getlist('project_id')
	location_id = request_handler.request.POST.getlist('location_id')
	item_id = None
	make_id = 1
	if item_details:
		item_data = item_details.split(",")
		item_id = item_data[0]
		make_id = item_data[1] if len(item_data) > 1 else 1
	approved_only = request_handler.getPostData('approved_only')
	if approved_only is None:
		draft_status = -1
	else:
		draft_status = 0

	is_faulty = 1 if request_handler.getPostData('is_faulty') else 0
	from_date, to_date = JsonUtil.getDateRange(rh=request_handler)
	db_session = SQLASession()
	materials = []
	material_list = db_session.query(
		Material.drawing_no, Material.name, Material.makes_json,
		Material.material_id).filter(
		Material.enterprise_id == enterprise_id, Material.in_use == '1').order_by(
		Material.name).all()
	for material in material_list:
		material_name = "%s" % material.name
		if material.drawing_no:
			material_name = "%s - %s" % (material_name, material.drawing_no)

		if material.makes_json:
			make_name = helper.constructDifferentMakeName(material.makes_json)

			material_name += " [%s]" % make_name if make_name else ""
		materials.append(("%s" % material.material_id, material_name))

	history_material_report_rows = []

	opening_receipt_qty = db_session.query(func.sum(ReceiptMaterial.accepted_qty)).join(ReceiptMaterial.receipt).filter(
		Receipt.inward_date < from_date, Receipt.enterprise_id == enterprise_id, Receipt.status > draft_status,
		ReceiptMaterial.item_id == item_id,
		ReceiptMaterial.is_faulty == is_faulty, ReceiptMaterial.received_grn_id.is_(None))
	# Add the extra filter if project_id is provided
	if project_id:
		if isinstance(project_id, list):
			opening_receipt_qty = opening_receipt_qty.filter(Receipt.project_code.in_(project_id))
		else:
			opening_receipt_qty = opening_receipt_qty.filter(Receipt.project_code == project_id)
	# Add the extra filter if location_id is provided
	if location_id:
		if isinstance(location_id, list):
			opening_receipt_qty = opening_receipt_qty.filter(Receipt.location_id.in_(location_id))
		else:
			opening_receipt_qty = opening_receipt_qty.filter(Receipt.location_id == location_id)

	opening_receipt_qty = opening_receipt_qty.all()
	# Receipt Transfer
	opening_received_transfer_qty = db_session.query(func.sum(TransferDetails.quantity).label('quantity')).join(
		TransferDetails.stock_transfer).filter(
		StockTransfer.approved_on < from_date, StockTransfer.enterprise_id == enterprise_id,
			TransferDetails.item_id == item_id, StockTransfer.status == 3, TransferDetails.is_faulty == is_faulty)
	# Add the extra filter if location_id is provided
	if location_id:
		if isinstance(location_id, list):
			opening_received_transfer_qty = opening_received_transfer_qty.filter(StockTransfer.to_location.in_(location_id))
		else:
			opening_received_transfer_qty = opening_received_transfer_qty.filter(StockTransfer.to_location == location_id)
	opening_received_transfer_qty = opening_received_transfer_qty.all()

	opening_invoice_qty = db_session.query(func.sum(InvoiceMaterial.quantity)).join(InvoiceMaterial.invoice).filter(
		Invoice.issued_on < from_date, Invoice.enterprise_id == enterprise_id,
		Invoice.status > draft_status, InvoiceMaterial.item_id == item_id,
		InvoiceMaterial.is_faulty == is_faulty, InvoiceMaterial.delivered_dc_id.is_(None))
	# Add the extra filter if project_id is provided
	if project_id:
		if isinstance(project_id, list):
			opening_invoice_qty = opening_invoice_qty.filter(Invoice.project_code.in_(project_id))
		else:
			opening_invoice_qty = opening_invoice_qty.filter(Invoice.project_code == project_id)
	# Add the extra filter if location_id is provided
	if location_id:
		if isinstance(location_id, list):
			opening_invoice_qty = opening_invoice_qty.filter(Invoice.location_id.in_(location_id))
		else:
			opening_invoice_qty = opening_invoice_qty.filter(Invoice.location_id == location_id)
	opening_invoice_qty = opening_invoice_qty.all()

	opening_issue_transfer_qty = db_session.query(
		func.sum(TransferDetails.quantity).label('quantity')).join(
		TransferDetails.stock_transfer).filter(
		StockTransfer.approved_on < from_date, StockTransfer.enterprise_id == enterprise_id,
		TransferDetails.item_id == item_id, StockTransfer.status > 1, StockTransfer.status != 4, TransferDetails.is_faulty == is_faulty)
	# Add the extra filter if location_id is provided
	if location_id:
		if isinstance(location_id, list):
			opening_issue_transfer_qty = opening_issue_transfer_qty.filter(StockTransfer.from_location.in_(location_id))
		else:
			opening_issue_transfer_qty = opening_issue_transfer_qty.filter(StockTransfer.from_location == location_id)
	opening_issue_transfer_qty = opening_issue_transfer_qty.all()

	opening_receipt_qty_value = opening_receipt_qty[0][0] if opening_receipt_qty and opening_receipt_qty[0][0] is not None else 0
	opening_invoice_qty_value = opening_invoice_qty[0][0] if opening_invoice_qty and opening_invoice_qty[0][0] is not None else 0
	opening_received_transfer_qty_value = opening_received_transfer_qty[0][0] if opening_received_transfer_qty and opening_received_transfer_qty[0][0] is not None else 0
	opening_issue_transfer_qty_value = opening_issue_transfer_qty[0][0] if opening_issue_transfer_qty and opening_issue_transfer_qty[0][0] is not None else 0
	opening_qty = (opening_receipt_qty_value+opening_received_transfer_qty_value) - (opening_invoice_qty_value + opening_issue_transfer_qty_value)

	history_material_report_element = MaterialHistoryReportElement(
		receipt_no="Opening Balance", receipt_qty=opening_qty)
	history_material_report_rows.append(history_material_report_element)

	# Receipt Details
	receipt_materials = db_session.query(ReceiptMaterial).join(ReceiptMaterial.receipt).filter(
		Receipt.inward_date >= from_date, Receipt.inward_date <= to_date, Receipt.enterprise_id == enterprise_id,
		ReceiptMaterial.item_id == item_id, Receipt.status > draft_status,
		ReceiptMaterial.is_faulty == is_faulty, ReceiptMaterial.received_grn_id.is_(None))
	# Add the extra filter if project_id is provided
	if project_id:
		if isinstance(project_id, list):
			receipt_materials = receipt_materials.filter(Receipt.project_code.in_(project_id))
		else:
			receipt_materials = receipt_materials.filter(Receipt.project_code == project_id)
	# Add the extra filter if location_id is provided
	if location_id:
		if isinstance(location_id, list):
			receipt_materials = receipt_materials.filter(Receipt.location_id.in_(location_id))
		else:
			receipt_materials = receipt_materials.filter(Receipt.location_id == location_id)
	receipt_materials = receipt_materials.all()
	total_rec_qty = 0
	total_rec_qty_value = 0
	for receipt_material in receipt_materials:
		if receipt_material.accepted_qty > 0:
			edit_link = "/erp/stores/grn/"
			if receipt_material.receipt.received_against == "Issues":
				edit_link = "/erp/stores/irn/"
			elif receipt_material.receipt.received_against == "Sales Return":
				edit_link = "/erp/sales/sr/"
			history_material_report_element = MaterialHistoryReportElement(
				receipt_no=receipt_material.receipt.getCode(),
				receipt_date=receipt_material.receipt.inward_date,
				receipt_qty=receipt_material.accepted_qty,
				issue_qty=0,
				rate=round(receipt_material.material.price, 2),
				receipt_value=round((receipt_material.accepted_qty * receipt_material.material.price), 2),
				issue_value=0,
				supplier_name=receipt_material.receipt.supplier.name if receipt_material.receipt.supplier else (receipt_material.po.issue_to if receipt_material.po and receipt_material.receipt.received_against == 'Issues' else ""),
				project=receipt_material.receipt.project.name if receipt_material.receipt.project else "",
				receipt_id=receipt_material.receipt_no,
				receipt_type=0, location=receipt_material.receipt.location.name if receipt_material.receipt.location else "", edit_link=edit_link)

			total_rec_qty = total_rec_qty + receipt_material.accepted_qty
			total_rec_qty_value = total_rec_qty_value + round(
				(receipt_material.accepted_qty * receipt_material.material.price), 2)
			history_material_report_rows.append(history_material_report_element)
	# Issue Details
	invoice_materials_list = db_session.query(InvoiceMaterial).join(InvoiceMaterial.invoice).filter(
		or_(
			and_(Invoice.issued_on >= from_date, Invoice.issued_on <= to_date),
			Invoice.issued_on.is_(None)), Invoice.enterprise_id == enterprise_id,
		InvoiceMaterial.item_id == item_id, Invoice.status > draft_status,
		InvoiceMaterial.is_faulty == is_faulty, InvoiceMaterial.delivered_dc_id.is_(None))
	# Add the extra filter if project_id is provided
	if project_id:
		if isinstance(project_id, list):
			invoice_materials_list = invoice_materials_list.filter(Invoice.project_code.in_(project_id))
		else:
			invoice_materials_list = invoice_materials_list.filter(Invoice.project_code == project_id)
	# Add the extra filter if location_id is provided
	if location_id:
		if isinstance(location_id, list):
			invoice_materials_list = invoice_materials_list.filter(Invoice.location_id.in_(location_id))
		else:
			invoice_materials_list = invoice_materials_list.filter(Invoice.location_id == location_id)
	invoice_materials_list = invoice_materials_list.all()
	total_inv_qty = 0
	total_inv_qty_value = 0
	for invoice_material in invoice_materials_list:
		if invoice_material.quantity > 0:
			history_material_report_element = MaterialHistoryReportElement(
				receipt_no=invoice_material.invoice.getCode(),
				receipt_date=invoice_material.invoice.issued_on if invoice_material.invoice.issued_on else datetime.now(),
				receipt_qty=0,
				issue_qty=invoice_material.quantity,
				rate=round(invoice_material.item.price, 2),
				receipt_value=0,
				issue_value=round((invoice_material.quantity * invoice_material.item.price), 2),
				supplier_name=invoice_material.invoice.customer.name if invoice_material.invoice.customer else invoice_material.invoice.issued_to,
				project=invoice_material.invoice.project.name if invoice_material.invoice.project else "",
				receipt_id=invoice_material.invoice_id, receipt_type=1, location=invoice_material.invoice.location.name if invoice_material.invoice.location else "",
				invoice_type=Invoice.TYPE_KEYS[invoice_material.invoice.type])
			total_inv_qty = total_inv_qty + invoice_material.quantity
			total_inv_qty_value = total_inv_qty_value + round((invoice_material.quantity * invoice_material.item.price), 2)

			history_material_report_rows.append(history_material_report_element)
	# Issue Transfer details
	issue_transfers = db_session.query(TransferDetails).join(
		TransferDetails.stock_transfer).filter(
		StockTransfer.approved_on >= from_date, StockTransfer.approved_on <= to_date, StockTransfer.enterprise_id == enterprise_id,
		TransferDetails.item_id == item_id, StockTransfer.status > 1, StockTransfer.status != 4, TransferDetails.is_faulty == is_faulty)
	# Add the extra filter if location_id is provided
	if location_id:
		if isinstance(location_id, list):
			issue_transfers = issue_transfers.filter(StockTransfer.from_location.in_(location_id))
		else:
			issue_transfers = issue_transfers.filter(StockTransfer.from_location == location_id)
	issue_transfers = issue_transfers.all()
	total_transfer_issue_qty = 0
	for issue_transfer in issue_transfers:
		if issue_transfer.quantity > 0:

			history_material_report_element = MaterialHistoryReportElement(
				receipt_no=issue_transfer.stock_transfer.getCode(),
				receipt_date=issue_transfer.stock_transfer.created_on,
				receipt_qty=0,
				issue_qty=round(issue_transfer.quantity, 2), rate=0, receipt_value=0,issue_value=0,
				supplier_name=issue_transfer.stock_transfer.to_location_rel.name if issue_transfer.stock_transfer.to_location_rel else "",
				project="",
				location=issue_transfer.stock_transfer.from_location_rel.name if issue_transfer.stock_transfer.from_location_rel else  "")

			total_transfer_issue_qty = total_transfer_issue_qty + issue_transfer.quantity
			history_material_report_rows.append(history_material_report_element)

	# Receipt Transfer
	received_transfers = db_session.query(TransferDetails).join(TransferDetails.stock_transfer).filter(
		StockTransfer.approved_on >= from_date, StockTransfer.approved_on <= to_date, StockTransfer.enterprise_id == enterprise_id,
		TransferDetails.item_id == item_id, StockTransfer.status == 3, TransferDetails.is_faulty == is_faulty)
	# Add the extra filter if location_id is provided
	if location_id:
		if isinstance(location_id, list):
			received_transfers = received_transfers.filter(StockTransfer.to_location.in_(location_id))
		else:
			received_transfers = received_transfers.filter(StockTransfer.to_location == location_id)
	received_transfers = received_transfers.all()
	total_transfer_received_qty = 0
	for received_transfer in received_transfers:
		if received_transfer.quantity > 0:

			history_material_report_element = MaterialHistoryReportElement(
				receipt_no=received_transfer.stock_transfer.getCode(),
				receipt_date=received_transfer.stock_transfer.created_on,
				receipt_qty=round(received_transfer.quantity, 2),
				issue_qty=0, rate=0, receipt_value=0,issue_value=0,
				supplier_name=received_transfer.stock_transfer.from_location_rel.name if received_transfer.stock_transfer.from_location_rel else "",
				project="",
				location=received_transfer.stock_transfer.to_location_rel.name if received_transfer.stock_transfer.to_location_rel else  "")

			total_transfer_received_qty = total_transfer_received_qty + received_transfer.quantity
			history_material_report_rows.append(history_material_report_element)

	closing_qty = (opening_qty + total_rec_qty + total_transfer_received_qty) - (total_inv_qty+total_transfer_issue_qty)

	# Sort the history_material_report_rows by receipt_date
	def sort_key(element):
		return element.receipt_date if element.receipt_date else datetime.min

	history_material_report_rows.sort(key=sort_key)

	history_material_report_element = MaterialHistoryReportElement(
		receipt_no="Total", receipt_qty=(opening_qty + total_rec_qty + total_transfer_received_qty), issue_qty=(total_inv_qty+total_transfer_issue_qty),
		receipt_value=total_rec_qty_value, issue_value=total_inv_qty_value)
	history_material_report_rows.append(history_material_report_element)
	history_material_report_element = MaterialHistoryReportElement(
		receipt_no="Closing Balance", receipt_qty=closing_qty)
	history_material_report_rows.append(history_material_report_element)

	return TemplateResponse(template=PURCHASE_ORDER_MATERIAL_HISTORY_REPORT_TEMPLATE, request=request, context={
		'report_rows': history_material_report_rows, 'from_date': from_date.strftime("%Y-%m-%d"), 'to_date': to_date.strftime("%Y-%m-%d"),
		'materials': materials, 'item_details': item_details, 'project_id': simplejson.dumps(project_id), 'approved_only': approved_only,
		'is_faulty': is_faulty, 'location_id': simplejson.dumps(location_id), TEMPLATE_TITLE_KEY: "Material Receipt"})


def generateShortageListReport(request):
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		shortage_material_report_rows = []
		shortage_material_report_elements = {}

		cat_list = request_handler.getPostData('cat_list')
		include_po = request_handler.getPostData('include_po')
		location_id = simplejson.loads(request_handler.getPostData('location_id'))
		cat_data = ""
		bom_count = 0
		catalogue_materials_name = []
		if cat_list is not None:
			cat_data = cat_list.split(FIELD_SEPARATOR)
		j = 0
		db_session = SQLASession()
		if len(cat_data) > 0:
			for i in range(0, (len(cat_data) / 2)):
				cat_code = cat_data[j + 0]
				qty = cat_data[j + 1]
				catalogue_materials = db_session.query(
					CatalogueMaterial,
					func.sum(ClosingStock.qty).label('opening_qty')
				).join(
					ClosingStock,
					(CatalogueMaterial.item_id == ClosingStock.item_id) &
					(CatalogueMaterial.enterprise_id == ClosingStock.enterprise_id) &
					(ClosingStock.is_faulty == 0)
				).filter(
					CatalogueMaterial.enterprise_id == enterprise_id,
					CatalogueMaterial.parent_id == cat_code
				).group_by(
					CatalogueMaterial.item_id,
					CatalogueMaterial.parent_id,
					CatalogueMaterial.enterprise_id
				).all()
				catalogue_materials_name.append(catalogue_materials[0][0].catalogue.name)
				bom_count += 1
				make_qty=[]
				for cat_material, consolidated_pending_qty in catalogue_materials:
					consolidated_make = ""
					consolidated_bom_make = ""
					bom = {}
					description = cat_material.material.name
					required_qty = Decimal(round((Decimal(qty) * Decimal(round(cat_material.quantity, 2))), 2))
					if consolidated_pending_qty < required_qty:
						shortage_qty = Decimal(round(required_qty - consolidated_pending_qty, 2))
					else:
						shortage_qty = 0
					bom_key = 0
					po_qty = "-"
					if include_po:
						po_query = """SELECT (SELECT IFNULL(SUM(pm.pur_qty), 0) as ordered_qty 
							FROM purchase_order as po, purchase_order_material as pm 
							WHERE  po.id = pm.pid AND po.enterprise_id = '{enterprise_id}' AND po.status = 2 
							AND pm.item_id = '{item_id}') -
							(SELECT IFNULL(SUM(gm.acc_qty), 0) as received_qty 
							FROM purchase_order as po, purchase_order_material as pm, grn_material as gm, grn 
							WHERE  po.id = pm.pid AND po.enterprise_id = '{enterprise_id}' AND po.status = 2 
							AND pm.item_id = '{item_id}' AND pm.make_id = gm.make_id AND pm.is_faulty = gm.is_faulty AND 
							gm.po_no = pm.pid AND gm.enterprise_id = '{enterprise_id}' 
							AND gm.item_id = pm.item_id AND gm.rec_grn_id is NULL 
							AND grn.grn_no = gm.grnNumber AND grn.status > -1 AND grn.enterprise_id ='{enterprise_id}')""".format(
							item_id=cat_material.material.material_id, enterprise_id=enterprise_id)
						pending_po = executeQuery(query=po_query)
						po_qty = pending_po[0][0] if pending_po[0][0] is not None else 0

					if description in shortage_material_report_elements.keys():
						existing_material = shortage_material_report_elements[description]
						consolidated_quantity = existing_material["required_qty"] + Decimal(round(required_qty, 2))
						if consolidated_pending_qty > consolidated_quantity:
							consolidated_shortage_quantity = 0
						else:
							consolidated_shortage_quantity = Decimal(round(consolidated_quantity, 2)) - Decimal(
								round(consolidated_pending_qty, 2))

						if Decimal(round(existing_material["required_qty"], 2)) > Decimal(round(consolidated_pending_qty, 2)):
							present_pending_qty = 0
						else:
							present_pending_qty = Decimal(round(consolidated_pending_qty, 2)) - Decimal(
								round(existing_material["required_qty"], 2))
						if present_pending_qty > Decimal(round(required_qty, 2)):
							shortage_quantity = 0
						else:
							shortage_quantity = Decimal(round(required_qty, 2)) - Decimal(round(present_pending_qty, 2))
						bom = existing_material["contains"]
						bom_key = len(bom)
						item_description = cat_material.material.name
						make_name = helper.constructDifferentMakeName(cat_material.material.makes_json)
						if make_name:
							item_description += " [" + make_name + "]"
						bom[bom_key] = {
							"make": consolidated_bom_make, "req_qty": round(required_qty, 2),
							"short_qty": round(shortage_quantity, 2), "bom_material": cat_material.catalogue.name, "cat_code": cat_code }
						shortage_material_report_elements[description] = {
							"drawing_no": cat_material.material.drawing_no if cat_material.material.drawing_no else "",
							"is_stockable": cat_material.material.is_stocked, "make": existing_material["make"],
							"is_service": cat_material.material.is_service,
							"unit": cat_material.material.unit, "rate": cat_material.material.price,
							"present_qty": Decimal(round(consolidated_pending_qty, 2)), "required_qty": Decimal(round(consolidated_quantity, 2)),
							"shortage_qty": Decimal(round(consolidated_shortage_quantity, 2)), "item_name": item_description,
							"contains": bom, "pending_po": Decimal(po_qty), "item_id": cat_material.material.material_id, "cat_code": cat_code, "available_qty": consolidated_pending_qty-Decimal(round(consolidated_quantity, 2)), "make_qty" : make_qty}
					else:
						item_description = cat_material.material.name
						make_name = helper.constructDifferentMakeName(cat_material.material.makes_json)
						if make_name:
							item_description += " [" + make_name + "]"
						bom[bom_key] = {
							"make": consolidated_bom_make, "req_qty": round(required_qty, 2),
							"short_qty": round(shortage_qty, 2), "bom_material": cat_material.catalogue.name, "cat_code": cat_code}
						shortage_material_report_elements[description] = {
							"drawing_no": cat_material.material.drawing_no if cat_material.material.drawing_no else "",
							"is_stockable": cat_material.material.is_stocked, "make": consolidated_make,
							"is_service": cat_material.material.is_service,
							"unit": cat_material.material.unit, "rate": cat_material.material.price,
							"present_qty": Decimal(round(consolidated_pending_qty, 2)), "required_qty":Decimal(round(required_qty, 2)),
							"shortage_qty": Decimal(round(shortage_qty, 2)), "item_name": item_description,
							"contains": bom, "pending_po": Decimal(po_qty), "item_id": cat_material.material.material_id, "cat_code": cat_code, "available_qty": consolidated_pending_qty-Decimal(round(required_qty, 2)), "make_qty" : make_qty}
				j = j + 2
		for key in shortage_material_report_elements.keys():
			material = shortage_material_report_elements[key]
			consolidated_bom_mat = {}
			bom_materials = material["contains"]
			count = 1
			for bom_key in bom_materials:
				bom_item = bom_materials[bom_key]
				consolidated_bom_mat[count] = bom_item
				count = count + 1
			store_dao = StoresDAO()
			catalogues_child = store_dao.getCatalogues(material["item_id"], enterprise_id)
			shortage_material_report_element = {
				"drawing_no": material["drawing_no"] if material["drawing_no"] else "", "make": material["make"],
				"unit": "%s" % material["unit"], "rate": material["rate"],
				"present_qty": material["present_qty"], "required_qty": material["required_qty"],
				"shortage_qty": material["shortage_qty"], "item_name": material["item_name"],
				"contains": consolidated_bom_mat, "pending_po":  material["pending_po"], "is_stockable": material["is_stockable"],
				"item_id": material["item_id"], "cat_code": material["cat_code"], "hasChildren": len(catalogues_child) > 0, "available_qty": material["available_qty"] if (material["available_qty"]) > 0 else 0, "make_qty": material["make_qty"],
				"is_service": material["is_service"]}
			shortage_material_report_rows.append(shortage_material_report_element)
		response = response_code.success()
		response["data"] = shortage_material_report_rows
		response["bom_count"] = bom_count
		response["catalogue_materials_name"] = catalogue_materials_name
	except Exception as e:
		logger.error(e.message)
		response = response_code.internalError()
		response["custom_message"] = "Failed to load Shortage Report. Please contact Admin!"
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def generateMaterialShortageList(request):
	request_handler = RequestHandler(request)
	cat_code = request_handler.getPostData('cat_code')
	include_item_order = request_handler.getPostData('include_item_order')
	include_po_qty = False
	if include_item_order is not None:
		include_po_qty = True
	if request_handler.getPostData('qty') is not None:
		qty = Decimal(request_handler.getPostData('qty'))
	else:
		qty = 0
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	materials = []
	db_session = SQLASession()
	catalogue_material_list = db_session.query(
		CatalogueMaterial.parent_id.label('catalogue_code'), Material.name, Material.drawing_no, Material.is_stocked,
		Material.is_service, Material.makes_json).outerjoin(CatalogueMaterial.catalogue).filter(
		CatalogueMaterial.enterprise_id == enterprise_id).group_by(
		CatalogueMaterial.parent_id).order_by(Material.name).all()
	for material in catalogue_material_list:
		material_name = "%s - %s" % (material.name, material.drawing_no) if material.drawing_no else material.name
		make_name = helper.constructDifferentMakeName(material.makes_json)
		if make_name:
			material_name += " [" + make_name + "]"
		materials.append((material.catalogue_code, material_name, material.is_stocked, material.is_service))

	shortage_material_list_elements = []

	cat_list = request_handler.getPostData('cat_list')
	cat_data = ""
	if cat_list is not None:
		cat_data = cat_list.split(FIELD_SEPARATOR)
	j = 0
	cat_list_details = ""
	if len(cat_data) > 0:
		for i in range(0, (len(cat_data) / 2)):
			cat_code = cat_data[j + 0]
			qty = cat_data[j + 1]
			cat_list_details = cat_list_details + cat_data[j + 0] + FIELD_SEPARATOR + cat_data[j + 1] + PACKET_SEPARATOR
			catalogue_materials = db_session.query(CatalogueMaterial).filter(
				CatalogueMaterial.enterprise_id == enterprise_id,
				CatalogueMaterial.parent_id == cat_code).group_by(CatalogueMaterial.parent_id).all()
			for cat_material in catalogue_materials:
				make_label = ""
				for make in cat_material.catalogue.makes:
					if len(cat_material.catalogue.makes) > 1 or (
							len(cat_material.catalogue.makes) == 1 and make.make_id != DEFAULT_MAKE_ID):
						part_no = helper.getMakePartNumber(
							enterprise_id=enterprise_id, item_id=cat_material.catalogue.material_id, make_id=make.make_id)
						make_mpn = "%s %s" % (make.make.label, ("[%s]" % part_no) if part_no else "")
						if make_label == "":
							make_label = "%s" % make_mpn
						else:
							make_label += ", %s" % make_mpn
					else:
						make_label = "-NA-"
				item_description = cat_material.catalogue.name
				make_name = helper.constructDifferentMakeName(cat_material.catalogue.makes_json)
				if make_name:
					item_description += " [" + make_name + "]"
				shortage_material_element = {
						"drawing_no": cat_material.catalogue.drawing_no if cat_material.catalogue.drawing_no else "",
						"qty": qty, "make": make_label if make_label != "" else "-NA-",
						"material_name": item_description, "cat_code": cat_code,
						"is_stockable": cat_material.catalogue.is_stocked,
						"is_service": cat_material.catalogue.is_service}
				shortage_material_list_elements.append(shortage_material_element)
			j = j + 2

	return TemplateResponse(template=SHORTAGE_LIST_REPORT_TEMPLATE, request=request, context={
		'material_list': shortage_material_list_elements, 'materials': materials, 'cat_code': cat_code, 'qty': qty,
		'cat_list': cat_list_details, TEMPLATE_TITLE_KEY: "Shortage Calculator", "include_item_order": include_po_qty})


def generateShortagePPListReport(request):
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		store_service = StoresService()
		shortage_material_report_rows = []
		shortage_material_report_elements = {}
		item_id_loc_list = []
		item_id_list = []
		child_materials = []
		location_wise_qty = {}
		pp_no_list = request_handler.getPostData('pp_no_list')
		pp_id_list = request_handler.getPostData('pp_id_list')
		cat_list = request_handler.getPostData('cat_list')
		include_po = request_handler.getPostData('include_po')
		location_list = request_handler.getPostData('location_list')
		loc_id = ""
		location_name = []
		if location_list is not None:
			loc_id = [val for val in location_list.split(FIELD_SEPARATOR) if val]
		if loc_id:
			for _loc_id in loc_id:
				if _loc_id:
					location = LocationService().get_location(location_id=_loc_id)
					if location:
						loc_name = location.name
					else:
						location = LocationService().get_default_location(enterprise_id=enterprise_id)
						loc_name = location.name
					location_name.append(loc_name)

		cat_data = ""
		bom_count = 0
		catalogue_materials_name = []
		if cat_list is not None:
			cat_data = cat_list.split(FIELD_SEPARATOR)
		pp_num = ""
		if pp_no_list is not None:
			pp_num = [pp.strip() for pp in pp_no_list.split(FIELD_SEPARATOR) if pp.strip()]
		pp_ids = ""
		if pp_id_list is not None:
			pp_ids = pp_id_list.split(FIELD_SEPARATOR)
		j = 0
		db_session = SQLASession()
		transfer_id_list = {}
		if len(cat_data) > 0:
			for i, pp_number, location_id, pp_id in zip(range(0, (len(cat_data) / 2)),pp_num, loc_id, pp_ids):
				cat_code = cat_data[j + 0]
				qty = cat_data[j + 1]
				catalogue_materials = db_session.query(
										CatalogueMaterial,
										func.sum(ClosingStock.qty).label('opening_qty')
									).join(
										ClosingStock,
										(CatalogueMaterial.item_id == ClosingStock.item_id) &
										(CatalogueMaterial.enterprise_id == ClosingStock.enterprise_id) &
										(ClosingStock.is_faulty == 0)
									).filter(
										CatalogueMaterial.enterprise_id == enterprise_id,
										CatalogueMaterial.parent_id == cat_code
									).group_by(
										CatalogueMaterial.item_id,  # Group by the unique identifier (or any other column you need)
										CatalogueMaterial.parent_id,
										CatalogueMaterial.enterprise_id
									).all()
				if catalogue_materials and catalogue_materials[0]:
					catalogue_materials_name.append(catalogue_materials[0][0].catalogue.name )
					bom_count += 1
					make_qty=[]
					stock_transfer_service = StockTransferService()
					transfer_details = stock_transfer_service.get_transfer_by_pp(pp_id, enterprise_id)
					if transfer_details:
						transfer_id_list[pp_id] = transfer_details
					for cat_material, consolidated_pending_qty in catalogue_materials:
						consolidated_make = ""
						consolidated_bom_make = ""
						bom = {}
						description = cat_material.material.name
						item_id = cat_material.item_id
						item_loc = {"item_id": item_id, "location_id": location_id, "pp_id":pp_id}
						item_id_loc_list.append(item_loc)
						allocated_issued_materials_qty = db_session.query(
							func.coalesce(MRP_Materials.allocated_qty, 0).label('allocated_qty'),
							func.coalesce(MRP_Materials.issued_qty, 0).label('issued_qty'),
							func.coalesce(MRP_Materials.requested_qty, 0).label('requested_qty'),).filter(
							MRP_Materials.enterprise_id == enterprise_id,
							MRP_Materials.item_id == item_id,
							MRP_Materials.pp_id == pp_id).first()
						allocated_qty, issued_qty, requested_qty = allocated_issued_materials_qty or (0, 0, 0)
						required_qty = Decimal(round((Decimal(qty) * Decimal(round(cat_material.quantity, 2))), 2))
						if consolidated_pending_qty < required_qty:
							shortage_qty = Decimal(round(required_qty - allocated_qty, 2))
						else:
							shortage_qty = 0
						bom_key = 0
						po_qty = "-"
						if include_po:
							po_query = """SELECT (SELECT IFNULL(SUM(pm.pur_qty), 0) as ordered_qty 
								FROM purchase_order as po, purchase_order_material as pm 
								WHERE  po.id = pm.pid AND po.enterprise_id = '{enterprise_id}' AND po.status = 2 
								AND pm.item_id = '{item_id}') -
								(SELECT IFNULL(SUM(gm.acc_qty), 0) as received_qty 
								FROM purchase_order as po, purchase_order_material as pm, grn_material as gm, grn 
								WHERE  po.id = pm.pid AND po.enterprise_id = '{enterprise_id}' AND po.status = 2 
								AND pm.item_id = '{item_id}' AND pm.make_id = gm.make_id AND pm.is_faulty = gm.is_faulty AND 
								gm.po_no = pm.pid AND gm.enterprise_id = '{enterprise_id}' 
								AND gm.item_id = pm.item_id AND gm.rec_grn_id is NULL 
								AND grn.grn_no = gm.grnNumber AND grn.status > -1 AND grn.enterprise_id ='{enterprise_id}')""".format(
								item_id=cat_material.material.material_id, enterprise_id=enterprise_id)
							pending_po = executeQuery(query=po_query)
							po_qty = pending_po[0][0] if pending_po[0][0] is not None else 0

						if description in shortage_material_report_elements.keys():
							existing_material = shortage_material_report_elements[description]
							consolidated_quantity = existing_material["required_qty"] + Decimal(round(required_qty, 2))
							if consolidated_pending_qty > consolidated_quantity:
								consolidated_shortage_quantity = 0
							else:
								consolidated_shortage_quantity = (existing_material["shortage_qty"] +
																  Decimal(round(shortage_qty, 2))) - Decimal(round(po_qty, 2))
							if Decimal(round(existing_material["required_qty"], 2)) > Decimal(round(consolidated_pending_qty, 2)):
								present_pending_qty = 0
							else:
								present_pending_qty = Decimal(round(consolidated_pending_qty, 2)) - Decimal(
									round(existing_material["required_qty"], 2))
							if present_pending_qty > Decimal(round(required_qty, 2)):
								shortage_quantity = 0
							else:
								shortage_quantity = Decimal(round(required_qty, 2)) - Decimal(round(allocated_qty, 2))
							bom = existing_material["contains"]
							bom_key = len(bom)
							item_description = cat_material.material.name
							make_name = helper.constructDifferentMakeName(cat_material.material.makes_json)
							if make_name:
								item_description += " [" + make_name + "]"
							bom[bom_key] = {
								"make": consolidated_bom_make, "req_qty": round(required_qty, 2),
								"short_qty": round(shortage_quantity, 2), "bom_material": cat_material.catalogue.name,
								"cat_code": cat_code, "pp_no": pp_number, "pp_id": pp_id,
								"location_id": location_id, "rate": cat_material.material.price,
								"allocated_qty": allocated_qty, "issued_qty": issued_qty, "requested_qty":requested_qty}
							shortage_material_report_elements[description] = {
								"drawing_no": cat_material.material.drawing_no if cat_material.material.drawing_no else "",
								"is_stockable": cat_material.material.is_stocked, "make": existing_material["make"],
								"is_service": cat_material.material.is_service,
								"unit": cat_material.material.unit, "rate": cat_material.material.price,
								"present_qty": Decimal(round(consolidated_pending_qty, 2)), "required_qty": Decimal(round(consolidated_quantity, 2)),
								"shortage_qty": Decimal(round(consolidated_shortage_quantity, 2)), "item_name": item_description,
								"contains": bom, "pending_po": Decimal(po_qty), "item_id": cat_material.material.material_id, "cat_code": cat_code,
								"available_qty": consolidated_pending_qty-Decimal(round(consolidated_quantity, 2)), "make_qty": make_qty, "requested_qty":requested_qty}
						else:
							item_description = cat_material.material.name
							make_name = helper.constructDifferentMakeName(cat_material.material.makes_json)
							if make_name:
								item_description += " [" + make_name + "]"

							bom[bom_key] = {
								"make": consolidated_bom_make, "req_qty": round(required_qty, 2),
								"short_qty": round(shortage_qty, 2), "bom_material": cat_material.catalogue.name,
								"cat_code": cat_code, "pp_no": pp_number, "pp_id": pp_id,
								"location_id": location_id, "rate": cat_material.material.price,
								"allocated_qty": allocated_qty, "issued_qty": issued_qty, "requested_qty":requested_qty}

							shortage_material_report_elements[description] = {
								"drawing_no": cat_material.material.drawing_no if cat_material.material.drawing_no else "",
								"is_stockable": cat_material.material.is_stocked, "make": consolidated_make,
								"is_service": cat_material.material.is_service,
								"unit": cat_material.material.unit, "rate": cat_material.material.price,
								"present_qty": Decimal(round(consolidated_pending_qty, 2)), "required_qty":Decimal(round(required_qty, 2)),
								"shortage_qty": Decimal(round(shortage_qty, 2)), "item_name": item_description,
								"contains": bom, "pending_po": Decimal(po_qty), "item_id": cat_material.material.material_id, "cat_code": cat_code,
								"available_qty": consolidated_pending_qty-Decimal(round(required_qty, 2)), "make_qty" : make_qty, "requested_qty":requested_qty}
				else:
					pp_num = [pp for pp in pp_num if pp not in pp_number]
				j = j + 2
		if not shortage_material_report_elements:
			response = response_code.internalError()
			response["custom_message"] = "There is no GRN for the materials in this production plan."
			return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')

		for key in shortage_material_report_elements.keys():
			material = shortage_material_report_elements[key]
			consolidated_bom_mat = {}
			bom_materials = material["contains"]
			count = 1
			for bom_key in bom_materials:
				bom_item = bom_materials[bom_key]
				consolidated_bom_mat[count] = bom_item
				count = count + 1
			store_dao = StoresDAO()
			catalogues_child = store_dao.getCatalogues(material["item_id"], enterprise_id)
			shortage_material_report_element = {
				"drawing_no": material["drawing_no"] if material["drawing_no"] else "", "make": material["make"],
				"unit": "%s" % material["unit"], "rate": material["rate"],
				"present_qty": material["present_qty"], "required_qty": material["required_qty"],
				"shortage_qty": material["shortage_qty"], "item_name": material["item_name"],
				"contains": consolidated_bom_mat, "pending_po":  material["pending_po"],
				"is_stockable": material["is_stockable"], "item_id": material["item_id"],
				"cat_code": material["cat_code"],
				"hasChildren": len(catalogues_child) > 0,
				"available_qty": material["available_qty"] if (material["available_qty"]) > 0 else 0,
				"make_qty": material["make_qty"], "is_service": material["is_service"], "requested_qty": material['requested_qty']}
			shortage_material_report_rows.append(shortage_material_report_element)
		for item_ids in item_id_loc_list:
			if item_ids["item_id"] not in item_id_list:
				item_id_list.append(item_ids["item_id"])

		material = {"is_faulty": 0, "item_id": item_id_list, "invoice_id": ""}
		closing_stock_list = executeQuery(
			store_service.construct_location_wise_item_stock_query(
				enterprise_id=enterprise_id, material=material), as_dict=True)
		for item in item_id_loc_list:
			for cl_item in closing_stock_list:
				if int(item['item_id']) == int(cl_item["id"]) and int(item['location_id']) == int(cl_item["location_id"]):
					if item['pp_id'] in location_wise_qty:
						location_wise_qty[item['pp_id']].append({"item_id": cl_item['id'], "qty": cl_item['Closing'],
							"location_id": cl_item["location_id"], "pp_id": item['pp_id']})
						break
					else:
						location_wise_qty[item['pp_id']] = [{"item_id": cl_item['id'], "qty": cl_item['Closing'],
							"location_id": cl_item["location_id"], "pp_id": item['pp_id']}]
						break
		for list_pp_id in pp_ids:
			get_child_materials_id = db_session.query(
				func.coalesce(MRP_Materials.item_id, 0).label('item_id'),
				func.coalesce(MRP_Materials.parent_id, 0).label('parent_id'),
				func.coalesce(MRP_Materials.requested_qty, 0).label('requested_qty'),
				func.coalesce(MRP_Materials.allocated_qty, 0).label('allocated_qty'),
			).filter(MRP_Materials.pp_id == list_pp_id, MRP_Materials.parent_id.isnot(None)).all()
			for child in get_child_materials_id:
				if child.requested_qty > 0 or child.allocated_qty > 0:
					child_materials.append({
						list_pp_id: {
							"item_id": child.item_id,
							"parent_id": child.parent_id,
							"requested_qty":child.requested_qty,
						}})

		response = response_code.success()
		response["data"] = shortage_material_report_rows
		response["bom_count"] = bom_count
		response["catalogue_materials_name"] = catalogue_materials_name
		response["pp_no_list"] = pp_num
		response['pp_id'] = pp_ids
		response["transfer_id_list"] = transfer_id_list
		response['location_id'] = location_list
		response['location_name'] = location_name
		response['location_wise_qty'] = location_wise_qty
		response['child_materials'] = child_materials
		response['stock_transfer_req_location_qty'] = closing_stock_list

	except Exception as e:
		logger.error(e.message)
		response = response_code.internalError()
		response["custom_message"] = "Failed to load Shortage Report. Please contact Admin!"
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def generateMaterialShortageListPP(request):
	request_handler = RequestHandler(request)
	cat_code = request_handler.getPostData('cat_code')
	include_item_order = request_handler.getPostData('include_item_order')
	from_date = request_handler.getPostData('from_date')
	to_date = request_handler.getPostData('to_date')
	loc_id = request_handler.getPostData('location_id')
	include_po_qty = False
	if include_item_order is not None:
		include_po_qty = True
	if request_handler.getPostData('qty') is not None:
		qty = Decimal(request_handler.getPostData('qty'))
	else:
		qty = 0
	enterprise_id = int(request_handler.getPostData('enterprise_id'))
	materials = []
	production_service = ProductionService()
	production_plan = production_service.getProductionPlanList(
		enterprise_id=enterprise_id, from_date=from_date, to_date=to_date, status=3, timeliness=0)
	if production_plan:
		for material in production_plan:
			material_name = "%s" % material['item_name']
			materials.append((
				material['item_id'], material_name, 1, 0, material['pp_qty'], material['pp_no'],
				material['pp_id'], material['status']))

	db_session = SQLASession()

	shortage_material_list_elements = []

	cat_list = request_handler.getPostData('cat_list')
	production_plane = request_handler.getPostData('pp_no')
	production_plane_id = request_handler.getPostData('pp_id')
	location_id_ = ""
	if loc_id is not None:
		location_id_ = loc_id.split(FIELD_SEPARATOR)
	pp_num = ""
	if production_plane is not None:
		pp_num = production_plane.split(FIELD_SEPARATOR)
	pp_id_num = ""
	if production_plane_id is not None:
		pp_id_num = production_plane_id.split(FIELD_SEPARATOR)
	cat_data = ""
	if cat_list is not None:
		cat_data = cat_list.split(FIELD_SEPARATOR)
	j = 0
	cat_list_details = ""
	if len(cat_data) > 0:
		for i, pp_no, location_id, pp_id in zip(range(0, (len(cat_data) / 2)),pp_num,location_id_,pp_id_num):
			cat_code = cat_data[j + 0]
			qty = cat_data[j + 1]
			cat_list_details = cat_list_details + cat_data[j + 0] + FIELD_SEPARATOR + cat_data[j + 1] + PACKET_SEPARATOR
			catalogue_materials = db_session.query(CatalogueMaterial).filter(
				CatalogueMaterial.enterprise_id == enterprise_id,
				CatalogueMaterial.parent_id == cat_code).group_by(CatalogueMaterial.parent_id).all()
			for cat_material in catalogue_materials:
				make_label = ""
				for make in cat_material.catalogue.makes:
					if len(cat_material.catalogue.makes) > 1 or (
							len(cat_material.catalogue.makes) == 1 and make.make_id != DEFAULT_MAKE_ID):
						part_no = helper.getMakePartNumber(
							enterprise_id=enterprise_id, item_id=cat_material.catalogue.material_id, make_id=make.make_id)
						make_mpn = "%s %s" % (make.make.label, ("[%s]" % part_no) if part_no else "")
						if make_label == "":
							make_label = "%s" % make_mpn
						else:
							make_label += ", %s" % make_mpn
					else:
						make_label = "-NA-"
				item_description = cat_material.catalogue.name
				make_name = helper.constructDifferentMakeName(cat_material.catalogue.makes_json)
				if make_name:
					item_description += " [" + make_name + "]"
				shortage_material_element = {
					"drawing_no": cat_material.catalogue.drawing_no if cat_material.catalogue.drawing_no else "",
					"qty": qty, "make": make_label if make_label != "" else "-NA-",
					"material_name": item_description, "cat_code": cat_code,
					"is_stockable": cat_material.catalogue.is_stocked,
					"is_service": cat_material.catalogue.is_service,
					"pp_no":pp_no, 'location_id':location_id, "pp_id":pp_id}
				shortage_material_list_elements.append(shortage_material_element)
			j = j + 2

	return TemplateResponse(template=SHORTAGE_LIST_PP_REPORT_TEMPLATE, request=request, context={
		'material_list': shortage_material_list_elements, 'materials': materials, 'cat_code': cat_code, 'qty': qty,
		'cat_list': cat_list_details, TEMPLATE_TITLE_KEY: "Material Requirement Calculator", "include_item_order": include_po_qty})


def appendgenerateShortageListReport(request):
	global parent_name
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		store_service = StoresService()
		shortage_material_report_rows = []
		shortage_material_report_elements = {}
		item_id_loc_list = []
		item_id_list = []
		location_wise_qty = {}
		pp_no_list = request_handler.getPostData('pp_no_list')
		pp_id_list = request_handler.getPostData('pp_id_list')
		cat_code = request_handler.getPostData('cat_code')
		cat_list = request_handler.getPostData('cat_list')
		include_po = request_handler.getPostData('include_po')
		location_list = request_handler.getPostData('location_list')
		loc_id = []
		location_name = []
		if location_list is not None:
			for item in location_list.split(','):
				value = item.split(FIELD_SEPARATOR)[0].strip()
				if value:
					loc_id.append(value)

		if loc_id:
			for _loc_id in loc_id:
				if _loc_id:
					location = LocationService().get_location(location_id=_loc_id)
					if location:
						loc_name = location.name
					else:
						location = LocationService().get_default_location(enterprise_id=enterprise_id)
						loc_name = location.name
					location_name.append(loc_name)

		cat_data = ""
		bom_count = 0
		catalogue_materials_name = []
		if cat_list is not None:
			cat_data = cat_list.split(FIELD_SEPARATOR)
		pp_num = ""
		if pp_no_list is not None:
			pp_num = [pp.strip() for pp in pp_no_list.split(FIELD_SEPARATOR) if pp.strip()]
		pp_ids = ""
		if pp_id_list is not None:
			pp_ids = pp_id_list.split(FIELD_SEPARATOR)
		j = 0
		db_session = SQLASession()
		if len(cat_data) > 0:
			for i, pp_number, location_id, pp_id in zip(range(0, (len(cat_data) / 2)),pp_num, loc_id, pp_ids):
				parent_id = cat_data[j + 0]
				qty = cat_data[j + 1]
				catalogue_materials = db_session.query(
										CatalogueMaterial,
										func.sum(ClosingStock.qty).label('opening_qty')
									).join(
										ClosingStock,
										(CatalogueMaterial.item_id == ClosingStock.item_id) &
										(CatalogueMaterial.enterprise_id == ClosingStock.enterprise_id) &
										(ClosingStock.is_faulty == 0)
									).filter(
										CatalogueMaterial.enterprise_id == enterprise_id,
										CatalogueMaterial.parent_id == cat_code
									).group_by(
										CatalogueMaterial.item_id,  # Group by the unique identifier (or any other column you need)
										CatalogueMaterial.parent_id,
										CatalogueMaterial.enterprise_id
									).all()
				if not catalogue_materials:
					response = response_code.internalError()
					response["custom_message"] = "There is no GRN for the materials in this production plan."
					return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')

				catalogue_materials_name.append(catalogue_materials[0][0].catalogue.name)
				bom_count += 1
				make_qty=[]
				for cat_material, consolidated_pending_qty in catalogue_materials:
					consolidated_make = ""
					consolidated_bom_make = ""
					bom = {}
					description = cat_material.material.name
					item_id = cat_material.item_id
					item_loc = {"item_id": item_id, "location_id": location_id, "pp_id": pp_id}
					item_id_loc_list.append(item_loc)
					allocated_issued_materials_qty = db_session.query(
						func.coalesce(MRP_Materials.allocated_qty, 0).label('allocated_qty'),
						func.coalesce(MRP_Materials.issued_qty, 0).label('issued_qty'),
						func.coalesce(MRP_Materials.requested_qty, 0).label('requested_qty')).filter(
						MRP_Materials.enterprise_id == enterprise_id,
						MRP_Materials.item_id == item_id,
						MRP_Materials.parent_id == cat_code,
						MRP_Materials.pp_id == pp_id).first()
					allocated_qty, issued_qty, requested_qty= allocated_issued_materials_qty if allocated_issued_materials_qty else (0, 0, 0)
					required_qty = Decimal(round((Decimal(qty) * Decimal(round(cat_material.quantity, 2))), 2))
					if consolidated_pending_qty < required_qty:
						shortage_qty = Decimal(round(required_qty - allocated_qty, 2))
					else:
						shortage_qty = 0
					bom_key = 0
					po_qty = "-"
					if include_po:
						po_query = """SELECT (SELECT IFNULL(SUM(pm.pur_qty), 0) as ordered_qty 
							FROM purchase_order as po, purchase_order_material as pm 
							WHERE  po.id = pm.pid AND po.enterprise_id = '{enterprise_id}' AND po.status = 2 
							AND pm.item_id = '{item_id}') -
							(SELECT IFNULL(SUM(gm.acc_qty), 0) as received_qty 
							FROM purchase_order as po, purchase_order_material as pm, grn_material as gm, grn 
							WHERE  po.id = pm.pid AND po.enterprise_id = '{enterprise_id}' AND po.status = 2 
							AND pm.item_id = '{item_id}' AND pm.make_id = gm.make_id AND pm.is_faulty = gm.is_faulty AND 
							gm.po_no = pm.pid AND gm.enterprise_id = '{enterprise_id}' 
							AND gm.item_id = pm.item_id AND gm.rec_grn_id is NULL 
							AND grn.grn_no = gm.grnNumber AND grn.status > -1 AND grn.enterprise_id ='{enterprise_id}')""".format(
							item_id=cat_material.material.material_id, enterprise_id=enterprise_id)
						pending_po = executeQuery(query=po_query)
						po_qty = pending_po[0][0] if pending_po[0][0] is not None else 0

					parent_query = """SELECT name FROM materials WHERE enterprise_id = '{enterprise_id}' AND id = '{item_id}'""" .format(
						item_id=parent_id, enterprise_id=enterprise_id)
					parent_name=""
					for catlogue_name in executeQuery(query=parent_query):
						parent_name = catlogue_name[0]

					if description in shortage_material_report_elements.keys():
						existing_material = shortage_material_report_elements[description]
						consolidated_quantity = existing_material["required_qty"] + Decimal(round(required_qty, 2))
						if consolidated_pending_qty > consolidated_quantity:
							consolidated_shortage_quantity = 0
						else:
							consolidated_shortage_quantity = Decimal(round(consolidated_quantity, 2)) - Decimal(
								round(allocated_qty, 2))

						if Decimal(round(existing_material["required_qty"], 2)) > Decimal(round(consolidated_pending_qty, 2)):
							present_pending_qty = 0
						else:
							present_pending_qty = Decimal(round(consolidated_pending_qty, 2)) - Decimal(
								round(existing_material["required_qty"], 2))
						if present_pending_qty > Decimal(round(required_qty, 2)):
							shortage_quantity = 0
						else:
							shortage_quantity = Decimal(round(required_qty, 2)) - Decimal(round(allocated_qty, 2))
						bom = existing_material["contains"]
						bom_key = len(bom)
						item_description = cat_material.material.name
						make_name = helper.constructDifferentMakeName(cat_material.material.makes_json)
						if make_name:
							item_description += " [" + make_name + "]"

						bom[bom_key] = {
							"make": consolidated_bom_make, "req_qty": round(required_qty, 2),
							"short_qty": round(shortage_quantity, 2), "bom_material": parent_name, "cat_code": parent_id,
							"pp_no": pp_number, "pp_id": pp_id, "location_id": location_id,"rate": cat_material.material.price,
							"allocated_qty": allocated_qty, "issued_qty": issued_qty, "requested_qty":requested_qty}
						shortage_material_report_elements[description] = {
							"drawing_no": cat_material.material.drawing_no if cat_material.material.drawing_no else "",
							"is_stockable": cat_material.material.is_stocked, "make": existing_material["make"],
							"unit": cat_material.material.unit, "rate": cat_material.material.price,  "is_service": cat_material.material.is_service,
							"present_qty": Decimal(round(consolidated_pending_qty, 2)), "required_qty": Decimal(round(consolidated_quantity, 2)),
							"shortage_qty": Decimal(round(consolidated_shortage_quantity, 2)), "item_name": item_description,
							"contains": bom, "pending_po": Decimal(po_qty), "item_id": cat_material.material.material_id, "cat_code": cat_code, "available_qty": consolidated_pending_qty-Decimal(round(consolidated_quantity, 2)), "make_qty" : make_qty , "requested_qty":requested_qty}
					else:
						item_description = cat_material.material.name
						make_name = helper.constructDifferentMakeName(cat_material.material.makes_json)
						if make_name:
							item_description += " [" + make_name + "]"
						bom[bom_key] = {
							"make": consolidated_bom_make, "req_qty": round(required_qty, 2),
							"short_qty": round(shortage_qty, 2), "bom_material": parent_name, "cat_code": parent_id,
							"pp_no": pp_number, "pp_id": pp_id, "location_id": location_id,"rate": cat_material.material.price,
							"allocated_qty": allocated_qty, "issued_qty": issued_qty,"requested_qty":requested_qty}
						shortage_material_report_elements[description] = {
							"drawing_no": cat_material.material.drawing_no if cat_material.material.drawing_no else "",
							"is_stockable": cat_material.material.is_stocked, "make": consolidated_make,
							"unit": cat_material.material.unit, "rate": cat_material.material.price,
							"present_qty": Decimal(round(consolidated_pending_qty, 2)), "required_qty":Decimal(round(required_qty, 2)),
							"shortage_qty": Decimal(round(shortage_qty, 2)), "item_name": item_description, "is_service": cat_material.material.is_service,
							"contains": bom, "pending_po": Decimal(po_qty), "item_id": cat_material.material.material_id, "cat_code": cat_code, "available_qty": consolidated_pending_qty-Decimal(round(required_qty, 2)), "make_qty" : make_qty, "requested_qty":requested_qty}
				j = j + 2
		for key in shortage_material_report_elements.keys():
			material = shortage_material_report_elements[key]
			consolidated_bom_mat = {}
			bom_materials = material["contains"]
			count = 1
			for bom_key in bom_materials:
				bom_item = bom_materials[bom_key]
				consolidated_bom_mat[count] = bom_item
				count = count + 1
			store_dao = StoresDAO()
			catalogues_child = store_dao.getCatalogues(material["item_id"], enterprise_id)
			shortage_material_report_element = {
				"drawing_no": material["drawing_no"] if material["drawing_no"] else "", "make": material["make"],
				"unit": "%s" % material["unit"], "rate": material["rate"],
				"present_qty": material["present_qty"], "required_qty": material["required_qty"],
				"shortage_qty": material["shortage_qty"], "item_name": material["item_name"], "is_service": material["is_service"],
				"contains": consolidated_bom_mat, "pending_po":  material["pending_po"], "is_stockable": material["is_stockable"], "item_id" : material["item_id"], "cat_code": material["cat_code"], "hasChildren": len(catalogues_child) > 0, "available_qty": material["available_qty"] if (material["available_qty"]) > 0 else 0, "make_qty": material["make_qty"] }
			shortage_material_report_rows.append(shortage_material_report_element)
		for item_ids in item_id_loc_list:
			if item_ids["item_id"] not in item_id_list:
				item_id_list.append(item_ids["item_id"])

		material = {"is_faulty": 0, "item_id": item_id_list, "invoice_id": ""}
		closing_stock_list = executeQuery(
			store_service.construct_location_wise_item_stock_query(enterprise_id=enterprise_id,
																   material=material), as_dict=True)
		for item in item_id_loc_list:
			for cl_item in closing_stock_list:
				if int(item['item_id']) == int(cl_item["id"]) and int(item['location_id']) == int(cl_item["location_id"]):
					if item['pp_id'] in location_wise_qty:
						location_wise_qty[item['pp_id']].append({"item_id": cl_item['id'],"qty": cl_item['Closing'],
							"location_id": cl_item["location_id"],"pp_id": item['pp_id']})
						break
					else:
						location_wise_qty[item['pp_id']] = [{"item_id": cl_item['id'], "qty": cl_item['Closing'],
															 "location_id": cl_item["location_id"],"pp_id": item['pp_id']}]
						break
		response = response_code.success()
		response["data"] = shortage_material_report_rows
		response["bom_count"] = bom_count
		response["catalogue_materials_name"] = catalogue_materials_name
		response[ "pp_no_list"] = pp_num
		response['location_id'] = location_list
		response['location_name'] = location_name
		response['location_wise_qty'] = location_wise_qty
		response['stock_transfer_req_location_qty'] = closing_stock_list
	except Exception as e:
		logger.error(e.message)
		response = response_code.internalError()
		response["custom_message"] = "Failed to load Shortage Report. Please contact Admin!"
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def append_generate_Shortage_ListReport(request):
	child_parent_name = ""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		shortage_material_report_rows = []
		shortage_material_report_elements = {}

		cat_code = request_handler.getPostData('cat_code')
		cat_list = request_handler.getPostData('cat_list')
		include_po = request_handler.getPostData('include_po')
		cat_data = ""
		bom_count = 0
		catalogue_materials_name = []
		if cat_list is not None:
			cat_data = cat_list.split(FIELD_SEPARATOR)
		j = 0
		db_session = SQLASession()
		if len(cat_data) > 0:
			for i in range(0, (len(cat_data) / 2)):
				parent_id = cat_data[j + 0]
				qty = cat_data[j + 1]
				catalogue_materials = db_session.query(CatalogueMaterial).filter(
					CatalogueMaterial.enterprise_id == enterprise_id,
					CatalogueMaterial.parent_id == cat_code).all()
				catalogue_materials_name.append(catalogue_materials[0].catalogue.name)
				bom_count += 1
				make_qty=[]
				for cat_material in catalogue_materials:
					consolidated_pending_qty = 0
					consolidated_make = ""
					consolidated_bom_make = ""
					bom = {}
					description = cat_material.material.name
					item_id = cat_material.item_id
					from_date = datetime.today()
					opening_receipt_materials = db_session.query(ReceiptMaterial).join(ReceiptMaterial.receipt).filter(
						Receipt.inward_date <= from_date, Receipt.enterprise_id == enterprise_id,
						ReceiptMaterial.item_id == item_id, Receipt.status > 0,
						ReceiptMaterial.is_faulty == 0,
						ReceiptMaterial.received_grn_id.is_(None)).all()
					opening_rec_qty = 0
					for receipt_material in opening_receipt_materials:
						opening_rec_qty = opening_rec_qty + receipt_material.accepted_qty
					logger.debug("Opening Receipt Qty:%s" % opening_rec_qty)

					opening_invoice_materials_list = db_session.query(InvoiceMaterial).join(
						InvoiceMaterial.invoice).filter(
						and_(or_(Invoice.issued_on <= from_date, Invoice.status == 0), Invoice.enterprise_id == enterprise_id,
						InvoiceMaterial.item_id == item_id, Invoice.status > -1,
						InvoiceMaterial.is_faulty == 0,
						InvoiceMaterial.delivered_dc_id.is_(None))).all()
					opening_inv_qty = 0
					for invoice_material in opening_invoice_materials_list:
						opening_inv_qty += invoice_material.quantity
					logger.debug("Opening Issue Material Qty %s" % opening_inv_qty)
					opening_qty = (opening_rec_qty - opening_inv_qty)
					consolidated_pending_qty = Decimal(round(opening_qty, 2))

					required_qty = Decimal(round((Decimal(qty) * Decimal(round(cat_material.quantity, 2))), 2))
					if consolidated_pending_qty < required_qty:
						shortage_qty = Decimal(round(required_qty - consolidated_pending_qty, 2))
					else:
						shortage_qty = 0
					bom_key = 0
					po_qty = "-"
					if include_po:
						po_query = """SELECT (SELECT IFNULL(SUM(pm.pur_qty), 0) as ordered_qty 
							FROM purchase_order as po, purchase_order_material as pm 
							WHERE  po.id = pm.pid AND po.enterprise_id = '{enterprise_id}' AND po.status = 2 
							AND pm.item_id = '{item_id}') -
							(SELECT IFNULL(SUM(gm.acc_qty), 0) as received_qty 
							FROM purchase_order as po, purchase_order_material as pm, grn_material as gm, grn 
							WHERE  po.id = pm.pid AND po.enterprise_id = '{enterprise_id}' AND po.status = 2 
							AND pm.item_id = '{item_id}' AND pm.make_id = gm.make_id AND pm.is_faulty = gm.is_faulty AND 
							gm.po_no = pm.pid AND gm.enterprise_id = '{enterprise_id}' 
							AND gm.item_id = pm.item_id AND gm.rec_grn_id is NULL 
							AND grn.grn_no = gm.grnNumber AND grn.status > -1 AND grn.enterprise_id ='{enterprise_id}')""".format(
							item_id=cat_material.material.material_id, enterprise_id=enterprise_id)
						pending_po = executeQuery(query=po_query)
						po_qty = pending_po[0][0] if pending_po[0][0] is not None else 0

					parent_query = """SELECT name FROM materials WHERE enterprise_id = '{enterprise_id}' AND id = '{item_id}'""" .format(
						item_id=parent_id, enterprise_id=enterprise_id)
					for catlogue_name in executeQuery(query=parent_query):
						child_parent_name = catlogue_name[0]

					if description in shortage_material_report_elements.keys():
						existing_material = shortage_material_report_elements[description]
						consolidated_quantity = existing_material["required_qty"] + Decimal(round(required_qty, 2))
						if consolidated_pending_qty > consolidated_quantity:
							consolidated_shortage_quantity = 0
						else:
							consolidated_shortage_quantity = Decimal(round(consolidated_quantity, 2)) - Decimal(
								round(consolidated_pending_qty, 2))

						if Decimal(round(existing_material["required_qty"], 2)) > Decimal(round(consolidated_pending_qty, 2)):
							present_pending_qty = 0
						else:
							present_pending_qty = Decimal(round(consolidated_pending_qty, 2)) - Decimal(
								round(existing_material["required_qty"], 2))
						if present_pending_qty > Decimal(round(required_qty, 2)):
							shortage_quantity = 0
						else:
							shortage_quantity = Decimal(round(required_qty, 2)) - Decimal(round(present_pending_qty, 2))
						bom = existing_material["contains"]
						bom_key = len(bom)
						item_description = cat_material.material.name
						make_name = helper.constructDifferentMakeName(cat_material.material.makes_json)
						if make_name:
							item_description += " [" + make_name + "]"

						bom[bom_key] = {
							"make": consolidated_bom_make, "req_qty": round(required_qty, 2),
							"short_qty": round(shortage_quantity, 2), "bom_material": child_parent_name, "cat_code": parent_id}
						shortage_material_report_elements[description] = {
							"drawing_no": cat_material.material.drawing_no if cat_material.material.drawing_no else "",
							"is_stockable": cat_material.material.is_stocked, "make": existing_material["make"],
							"unit": cat_material.material.unit, "rate": cat_material.material.price,  "is_service": cat_material.material.is_service,
							"present_qty": Decimal(round(consolidated_pending_qty, 2)), "required_qty": Decimal(round(consolidated_quantity, 2)),
							"shortage_qty": Decimal(round(consolidated_shortage_quantity, 2)), "item_name": item_description,
							"contains": bom, "pending_po": Decimal(po_qty), "item_id": cat_material.material.material_id, "cat_code": cat_code, "available_qty": consolidated_pending_qty-Decimal(round(consolidated_quantity, 2)), "make_qty" : make_qty }
					else:
						item_description = cat_material.material.name
						make_name = helper.constructDifferentMakeName(cat_material.material.makes_json)
						if make_name:
							item_description += " [" + make_name + "]"
						bom[bom_key] = {
							"make": consolidated_bom_make, "req_qty": round(required_qty, 2),
							"short_qty": round(shortage_qty, 2), "bom_material": child_parent_name, "cat_code": parent_id}
						shortage_material_report_elements[description] = {
							"drawing_no": cat_material.material.drawing_no if cat_material.material.drawing_no else "",
							"is_stockable": cat_material.material.is_stocked, "make": consolidated_make,
							"unit": cat_material.material.unit, "rate": cat_material.material.price,
							"present_qty": Decimal(round(consolidated_pending_qty, 2)), "required_qty":Decimal(round(required_qty, 2)),
							"shortage_qty": Decimal(round(shortage_qty, 2)), "item_name": item_description, "is_service": cat_material.material.is_service,
							"contains": bom, "pending_po": Decimal(po_qty), "item_id": cat_material.material.material_id, "cat_code": cat_code, "available_qty": consolidated_pending_qty-Decimal(round(required_qty, 2)), "make_qty" : make_qty}
				j = j + 2
		for key in shortage_material_report_elements.keys():
			material = shortage_material_report_elements[key]
			consolidated_bom_mat = {}
			bom_materials = material["contains"]
			count = 1
			for bom_key in bom_materials:
				bom_item = bom_materials[bom_key]
				consolidated_bom_mat[count] = bom_item
				count = count + 1
			store_dao = StoresDAO()
			catalogues_child = store_dao.getCatalogues(material["item_id"], enterprise_id)
			shortage_material_report_element = {
				"drawing_no": material["drawing_no"] if material["drawing_no"] else "", "make": material["make"],
				"unit": "%s" % material["unit"], "rate": material["rate"],
				"present_qty": material["present_qty"], "required_qty": material["required_qty"],
				"shortage_qty": material["shortage_qty"], "item_name": material["item_name"], "is_service": material["is_service"],
				"contains": consolidated_bom_mat, "pending_po":  material["pending_po"], "is_stockable": material["is_stockable"], "item_id" : material["item_id"], "cat_code": material["cat_code"], "hasChildren": len(catalogues_child) > 0, "available_qty": material["available_qty"] if (material["available_qty"]) > 0 else 0, "make_qty": material["make_qty"] }
			shortage_material_report_rows.append(shortage_material_report_element)
		response = response_code.success()
		response["data"] = shortage_material_report_rows
		response["bom_count"] = bom_count
		response["catalogue_materials_name"] = catalogue_materials_name
	except Exception as e:
		logger.error(e.message)
		response = response_code.internalError()
		response["custom_message"] = "Failed to load Shortage Report. Please contact Admin!"
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')
