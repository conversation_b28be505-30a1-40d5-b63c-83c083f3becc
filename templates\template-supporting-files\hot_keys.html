<style type="text/css">
	.hotkey_cmd{
		border: 1px solid #000;
		padding:7px;
		background: #000;
		color: #fff;
		text-align:center;
	}
	.hotkey_plus{
		margin-top: -25px;
		margin-left: 77px;
	}

	.hotkeys {
		margin-bottom: 15px;
	}

	.hotkey_combination{
		border:1px solid #209be1;
		padding:7px;
		background:#209be1;
		color:#fff;
		text-align:center;
		max-width: 50%;
	}
	.hotkey_function{
		margin-top: -25px;
		margin-left: 44px;
		width:200%;
	}
</style>
<div id="hotkey_list_modal" class="modal fade " role="dialog">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Hotkeys</h4>
			</div>
			<div class="modal-body">
				<div class="col-lg-12">
					<div class="col-sm-6">
						<h4 style="font-size: 14px;font-weight: bold;">GENERAL</h4><br>
						<div class="col-sm-3">
							<p class="hotkey_cmd">cmd </p>
							<p class="hotkey_plus">+ </p>
						</div>
						<div class="col-sm-3" >
							<div class="hotkeys">
								<p class="hotkey_combination">x</p>
								<p class="hotkey_function">Logout</p>
							</div>
							<div class="hotkeys">
								<p class="hotkey_combination">></p>
								<p class="hotkey_function">Open menu</p>
							</div>
						</div>
					</div>
					<div class="col-sm-6">
						<h4 style="font-size: 14px;font-weight: bold;">CREATE ITEMS</h4><br>
						<div class="col-sm-3">
							<p class="hotkey_cmd">alt </p>
							<p class="hotkey_plus">+ </p>
						</div>
						<div class="col-sm-3" >
							<div class="hotkeys">
								<p class="hotkey_combination">i</p>
								<p class="hotkey_function">Create new Invoice</p>
							</div>
							<div class="hotkeys">
								<p class="hotkey_combination">o</p>
								<p class="hotkey_function">Create new PO</p>
							</div>
							<div class="hotkeys">
								<p class="hotkey_combination">j</p>
								<p class="hotkey_function">Create new Voucher</p>
							</div>
							<div class="hotkeys">
								<p class="hotkey_combination">k</p>
								<p class="hotkey_function">Create new OA</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>