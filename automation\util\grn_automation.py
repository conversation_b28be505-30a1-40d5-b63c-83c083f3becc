import pandas as pd
from util import LOG_HANDLER
from conf import *

"""
requirement installation : pip install pandas=0.24.2
"""

class GRNAutomation:
    def __init__(self,file_stored_path=None,enterprise_id=None,location_id=None):
        self.file_stored_path = file_stored_path
        self.enterprise_id = enterprise_id
        self.location_id = location_id

        self.invalid_materials = []
        self.valid_materials = []
        self.closing_stock_zero = []

        self.logger = logging.getLogger(os.path.splitext(os.path.basename(__file__))[0])
        self.logger.setLevel(logging.DEBUG)
        self.logger.addHandler(LOG_HANDLER)

    def get_item_id(self, drawing_no):
        query = ("SELECT id as item_id FROM materials WHERE drawing_no = '%s' AND enterprise_id =%s;" %
                 (drawing_no, self.enterprise_id))
        results = executeQuery(query, as_dict=True)
        return results[0]['item_id'] if results else None

    def get_receipt_code(self, current_fy):
        query = ("SELECT receipt_no FROM grn WHERE financial_year = '%s' AND enterprise_id = '%s' AND "
                 "rec_against NOT IN ('Issues','Sales Return','Note') AND receipt_no != '0' ORDER BY approved_on DESC limit 1;" %
                 (current_fy,self.enterprise_id))
        result = executeQuery(query,as_dict=True)
        return int(result[0]['receipt_no'])+1

    def create_grn(self, material_list=None):
        db_session = SQLASession()

        try:
            db_session.begin(subtransactions=True)
            current_fy = getFinancialYear(for_date=datetime.now())

            receipt_code = self.get_receipt_code(current_fy)
            grn_date = datetime.now()
            latest_grn_no = self.get_latest_grn()

            grn_query = """INSERT INTO grn (grn_no, receipt_no, grn_date, rec_against, enterprise_id, party_id, invno,
            inward_no, inspector, status, inward_date, financial_year, inv_date, approved_on, location_id)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"""
            grn_query_values =(latest_grn_no, receipt_code, grn_date, 'Others', self.enterprise_id, 0, '', '',
                               '', 1,grn_date, current_fy, datetime.today(), grn_date, self.location_id)

            executeQuery(grn_query, query_data=grn_query_values)

            for index, material in material_list.iterrows():
                faulty = 1 if '(FAULTY)' in material['Name'] else 0
                item_id = self.get_item_id(material['Drawing No'])

                if item_id:
                    insert_grn_material_query = """INSERT INTO grn_material (grnNumber, item_id, rec_qty, acc_qty, dc_qty,
                    is_faulty, enterprise_id, location_id) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)"""
                    grn_material_values = (latest_grn_no, item_id, material['Closing Qty'],
                                           material['Closing Qty'],material['Closing Qty'],
                                           faulty, self.enterprise_id, self.location_id)

                    executeQuery(insert_grn_material_query, query_data=grn_material_values )

            self.logger.info("Successfully created GRN(%s)" % int(self.get_latest_grn()-1))

        except Exception as e:
            self.logger.error("Error creating GRN: %s", e)
            db_session.rollback()
            raise
        finally:
            db_session.close()

    def get_latest_grn(self):
        query = ("SELECT grn_no FROM grn WHERE enterprise_id = %s ORDER BY grn_no DESC LIMIT 1;"
                 % self.enterprise_id)
        result = executeQuery(query, as_dict=True)
        grn_no = result[0]["grn_no"]
        return int(grn_no) + 1


    def check_materials(self, drawing_no):
        query = ("SELECT EXISTS (SELECT 1 FROM materials WHERE drawing_no = '%s' AND enterprise_id = %s) "
                 "AS material_exists;") % (drawing_no, self.enterprise_id)

        result = executeQuery(query, as_dict=True)
        return result[0]["material_exists"] if result else False

    def read_file(self):
        _, file_extension = os.path.splitext(self.file_stored_path)
        if file_extension.lower() == '.xlsx':
            return pd.read_excel(self.file_stored_path)
        elif file_extension.lower() == '.csv':
            return pd.read_csv(self.file_stored_path)
        else:
            self.logger.error("Unsupported file format: %s", file_extension)
            raise ValueError("Unsupported file format. Please use .xlsx or .csv files.")

    def process_file(self):
        df = self.read_file()
        expected_columns = ["Drawing No", "Name", "Closing Qty"]
        missing_columns = [col for col in expected_columns if col not in df.columns]

        if missing_columns:
            raise ValueError("Column(s) missing: %s" % ', '.join(missing_columns))
        else:
            rows_with_empty_values = df.loc[:, ['Drawing No', 'Name', 'Closing Qty']].isna().any(axis=1)
            empty_rows = df[rows_with_empty_values]

            clean_data = df[~rows_with_empty_values]

            for _, material_row in clean_data.iterrows():

                if not self.check_materials(material_row['Drawing No']):
                    self.invalid_materials.append(material_row)
                else:
                    if material_row['Closing Qty'] == 0:
                        self.closing_stock_zero.append(material_row)
                    else:
                        self.valid_materials.append(material_row)

            self.create_grn(material_list=pd.DataFrame(self.valid_materials))

            self.logger.info("GRN(%s)" % self.get_latest_grn())
            self.logger.info("Empty rows: %s", empty_rows)
            self.logger.info("Invalid materials count: %d", len(self.invalid_materials))
            self.logger.info("Invalid material list: %s", self.invalid_materials)
            self.logger.info("Valid materials count: %d", len(self.valid_materials))
            self.logger.info("Valid material list: %s", self.valid_materials)
            self.logger.info("closing qty zero: %d", len(self.closing_stock_zero))
            self.logger.info("closing qty zero list: %s", self.closing_stock_zero)



if __name__ == "__main__":
    file_path = '/home/<USER>/Meyraj/xlsx_and_csv/Closing_Stock_loc_3.csv'
    enterprise_id = 102
    location_id = 14
    grn_automation = GRNAutomation(file_stored_path=file_path, enterprise_id=enterprise_id, location_id=location_id)
    grn_automation.process_file()
