
$(document).ready(function () {
 //used to format the date displayed
    $( "#fromdate" ).datepicker({
        dateFormat: "yy-mm-dd",
		autoclose: true,
		orientation: 'bottom auto'
    });

    $( "#todate" ).datepicker({
        dateFormat: "yy-mm-dd",
		autoclose: true,
		orientation: 'bottom auto'
    });
	$('#todate').datepicker('setStartDate', '-30d');
    var today = new Date();
    var date = new Date(today.getTime() - 30*24*60*60*1000);
    setTimeout(function(){
    if($("#fromdate").val().indexOf('-') <0) {
        $("#fromdate").datepicker("update",date);
    }
    if($("#todate").val().indexOf('-') <0) {
        $("#todate").datepicker("update",today);
    }
    },100);
	$('#search').multiselect({
		includeSelectAllOption: true
	});
	$("#search").next('.btn-group').find('.multiselect-selected-text').text('All Selected');
	FromToDateValidation();
	$(".chosen-select").chosen();

	$("#refresh").click(function(){
        if($('#drawing_no option:selected').val() == 0){
                swal('Please Select Material');
                return;
        }
        $("#loading").show();
    	$("#id_material_history").submit();
	});

});

function FromToDateValidation(){
	$('#fromdate').on('changeDate', function(){
		$('#todate').datepicker('setStartDate', new Date($(this).val()));
		var start = new Date($('#fromdate').val()),
		end   = new Date($('#todate').val()),
		diff  = new Date(end - start),
		days  = diff/1000/60/60/24;
		if(days < 0) {
			$('#todate').datepicker('update', $('#fromdate').val());
		}
	});
}

setTimeout(function(){
	$("#search").next('.btn-group').find(".multiselect-container li:nth-child(2)").each(function(){
		$(this).addClass('li-select-all').find("label").append('<span class="checkmark"></span>');
	});
	$("#search").next('.btn-group').find(".multiselect-container li:gt(1)").each(function(){
		$(this).addClass('li-all-others');
	});
},500);

function FieldRequiredInTable() {
	var totalCount = 0;
	$("#report-table").find('tr').each(function(){
	   $(this).find('td').hide();
	   $(this).find('th').hide();
	   $(this).find('td:nth-child(1)').show();
	   $(this).find('th:nth-child(1)').show();
	});
	var selectedValue="1";
	$(".li-all-others").each(function(){
		totalCount++;
		if($(this).find('input').is(":checked")){
			selectedValue+=","+$(this).find('input').val();
		}
	});
	if(selectedValue != "") {
		var splitSelectedValue = selectedValue.split(',');
		$.each(splitSelectedValue,function(i){
		   $("#report-table").find('tr').each(function(){
			   $(this).find('td:nth-child('+splitSelectedValue[i]+')').show();
				$(this).find('th:nth-child('+splitSelectedValue[i]+')').show();
		   });
		});
	}
	$(".no_match_found, .empty_search_result").attr('colspan',$('#mod_tbl tr th:visible').length);
}