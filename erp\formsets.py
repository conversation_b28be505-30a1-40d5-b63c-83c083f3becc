"""
"""

from django.forms.formsets import formset_factory

from erp.forms import VoucherParticularForm, UserPermissionForm, CatalogueMaterialForm, SubTaxForm, IndentMaterialForm, \
	InvoiceMaterialsForm, InvoiceTaxForm, LedgerBillForm, \
	TagForm, ExpenseParticularsForm, NoteItemForm, NoteTaxForm, MaterialMakeProfileForm, PayStructureDetailsForm, \
	MaterialPartyProfileForm, EmployeePayStructureForm, OAParticularsForm, \
	InvoiceChargeForm, BankReconciliationForm, InvoiceTempBannerForm, \
	MaterialAlternateUnitForm, SEParticularsForm, SETaxForm, SpecificationForm, OATaxForm

__author__ = 'kalaivanan'

CatalogueMaterialFormset = formset_factory(form=CatalogueMaterialForm, extra=0, can_delete=True)

MaterialAlternateUnitFormset = formset_factory(form=MaterialAlternateUnitForm, extra=0, can_delete=True)

IndentMaterialFormset = formset_factory(form=IndentMaterialForm, extra=0, can_delete=True)

SubTaxFormset = formset_factory(form=SubTaxForm, can_delete=True, extra=0)

UserPermissionFormset = formset_factory(form=UserPermissionForm, extra=0)

VoucherParticularFormset = formset_factory(form=VoucherParticularForm, can_delete=True, extra=0)

MaterialPartyProfileFormset = formset_factory(form=MaterialPartyProfileForm, extra=0, can_delete=True)

SpecificationFormset = formset_factory(form=SpecificationForm, can_delete=True, extra=0)

# Invoice FormSet: This form handle the Sales invoice Material Details...
InvoiceMaterialFormset = formset_factory(form=InvoiceMaterialsForm, can_delete=True, extra=0)

InvoiceChargesFormset = formset_factory(form=InvoiceChargeForm, can_delete=True, extra=0)

InvoiceTaxFormset = formset_factory(form=InvoiceTaxForm, can_delete=True, extra=0)

LedgerBillFormset = formset_factory(form=LedgerBillForm, can_delete=True, extra=0)

# Tag formsets
TagFormset = formset_factory(form=TagForm, can_delete=True, extra=0)

# Expense Particulars Formsets
ExpenseParticularsFormset = formset_factory(form=ExpenseParticularsForm, can_delete=True, extra=0)

# This form Handle the sales non stock item Details
NoteItemFormset = formset_factory(form=NoteItemForm, can_delete=True, extra=0)

NoteTaxFormset = formset_factory(form=NoteTaxForm, can_delete=True, extra=0)

PayStructureDetailsFormset = formset_factory(form=PayStructureDetailsForm, can_delete=True, extra=0)

MaterialMakeFormset = formset_factory(form=MaterialMakeProfileForm, can_delete=True, extra=0)

EmployeePayStructureFormset = formset_factory(form=EmployeePayStructureForm, can_delete=True, extra=0)

OAParticularsFormset = formset_factory(form=OAParticularsForm, can_delete=True, extra=0)

OATaxFormset = formset_factory(form=OATaxForm, can_delete=True, extra=0)

BankReconciliationFormset = formset_factory(form=BankReconciliationForm, can_delete=True, extra=0)

InvoiceTemplateHeaderBannerFormset = formset_factory(form=InvoiceTempBannerForm, can_delete=True, extra=0)

InvoiceTemplateFooterBannerFormset = formset_factory(form=InvoiceTempBannerForm, can_delete=True, extra=0)

SEParticularsFormset = formset_factory(form=SEParticularsForm, can_delete=True, extra=0)

SETaxFormset = formset_factory(form=SETaxForm, can_delete=True, extra=0)
