<table style="width: 100% !important;">
	<tbody>
		{% if summary_res.include_hsn_summary %}
			<style>
			sup {
				line-height: 1.6;
			}
			
			</style>
			<tr>
				<td>
					<div class="hsn_summary_contianer">
						<h6 style="margin: 0"><b style="font-size: {{summary_res.hsn_tax_font_size}}pt;">HSN SUMMARY</b></h6>
						<table class="table hsn_table hsn_summary elegant_table{% if item_res.include_row_separator %} row-seperator{% endif %}{% if item_res.include_column_separator %} column-seperator{% endif %}" style="width: 100%; font-size: {{summary_res.hsn_tax_font_size}}pt;">
							<thead>
								<tr class="header_shading">
									<th class="text-center td_sno" rowspan="2" style="width: 6%">S.No</th>
									<th class="text-center" rowspan="2" style="width: 28%">HSN/SAC</th>
									<th class="text-center td_tax td_tax_text" rowspan="2" style="width: 18%">Taxable Value</th>
									<th class="text-center" colspan="2" style="width: 16%; ">CGST</th>
									<th class="text-center" colspan="2" style="width: 16%; ">SGST</th>
									<th class="text-center" colspan="2" style="width: 16%;  border-right-color: #ccc;">IGST</th>
								</tr>
								<tr class="header_shading">
									<th class="text-center" style="width: 7%;">%</th>
									<th class="text-center" style="width: 9%;">{{ source.currency.code }}</th>
									<th class="text-center" style="width: 7%;">%</th>
									<th class="text-center" style="width: 9%;">{{ source.currency.code }}</th>
									<th class="text-center" style="width: 7%;">%</th>
									<th class="text-center" style="width: 9%;">{{ source.currency.code }}</th>
								</tr>
							</thead>
							<tbody>
								{% for summary in hsn_summary %}
									<tr class="item_1">
										<td class="text-center td_sno">{{forloop.counter}}.</td>
										<td class="text-left">{{ summary.hsn_code }}</td>
										<td class="text-right td_tax">{{ summary.consolidated_taxable_value|floatformat:2 }}</td>
										<td class="text-right ">{{ summary.consolidated_cgst_rate|floatformat:2 }}</td>
										<td class="text-right ">{{ summary.consolidated_cgst_value|floatformat:2 }}</td>
										<td class="text-right ">{{ summary.consolidated_sgst_rate|floatformat:2 }}</td>
										<td class="text-right ">{{ summary.consolidated_sgst_value|floatformat:2 }}</td>
										<td class="text-right ">{{ summary.consolidated_igst_rate|floatformat:2 }}</td>
										<td class="text-right" style="border-right-color: #ccc;">{{ summary.consolidated_igst_value|floatformat:2 }}</td>
									</tr>
								{% endfor %}
							</tbody>
						</table>
						{% if config_res.template_id == 3 %}
							<div><hr style="border: 1px solid #666;"> </div>
						{% endif %}
					</div>
				</td>
			</tr>
		{% endif %}
		<tr>
			<td style="border-bottom: solid 1px #666666; padding-bottom: 4px;">
					<div class="col-sm-12 remove-padding" style="padding-bottom: 4px; font-size:{{summary_res.font_size}}pt;">
						{% autoescape off %}{{ source.notes }}{% endautoescape %}
					</div>
			</td>
		</tr>
	</tbody>
</table>

<p style="page-break-before: always"></p>