{% extends "production/sidebar.html" %}
{% block productionPlan %}
{% if logged_in_user|canView:'MANUFACTURE INDENT' %}
    <link rel="stylesheet" href="/site_media/css/manufacturing_indent.css?v={{ current_version }}">
    <script type="text/javascript" src="/site_media/js/load_material.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/manufacturing_indent.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/add_material.js?v={{ current_version }}"></script>
    <div class="right-content-container">
        <div class="page-title-container">
            <span class="page-title">Manufacturing Indent</span>
            <input type="hidden" id="mi_permission" value="{{logged_in_user|canApprove:'MANUFACTURE INDENT'}}" />
        </div>
        <div class="filter-components" style="width: calc(100% - 428px); margin-left: 340px; margin-bottom: -12px; margin-top: 25px;">
            <div class="filter-components-container">
                <div class="dropdown">
                    <button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
                        <i class="fa fa-filter"></i>
                    </button>
                    <span class="dropdown-menu arrow_box arrow_box_filter">
                        <div class="col-sm-12 form-group" >
                            <label>Date Range</label>
                            <div id="reportrange" class="report-range form-control">
                                <i class="glyphicon glyphicon-calendar"></i>&nbsp;
                                <span></span> <b class="caret"></b>
                                <input type="hidden" class="fromdate" id="fromdate" name="fromdate" />
                                <input type="hidden" class="todate" id="todate" name="todate" />
                            </div>
                        </div>
                        <div class="col-sm-12 form-group">
                            <label>Party</label>
                            <select class="form-control chosen-select" name="party_id" id="party" >
                                {% for j in customers %}
                                <option value="{{j.0}}"{% if j.0 == party_id %}selected{% endif %}>{{ j.1 }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-sm-12">
                            <div class="checkbox" style="padding-left: 24px !important;">
                                <input name="mi_not_applicable" id="id_mi_not_applicable" type="checkbox">
                                <label for="id_mi_not_applicable">Include MI Not Applicable</label>
                            </div>
                        </div>
                        <div class="filter-footer">
                            <input type="button" class="btn btn-save" value="Apply" onclick="applyMiFilter()"/>
                        </div>
                    </span>
                </div>
                <span class='filtered-condition filtered-date'>Date: <b></b></span>
                <span class='filtered-condition filtered-party'>Party: <b></b></span>
                <span class='filtered-condition filtered-mi-not-applicable'>MI Not Applicable: <b></b></span>
            </div>
        </div>
        <div class="service_flag" style="margin-top: -15px; user-select: auto; position: absolute; right: 65px;">
            <span class="service-item-flag"></span> - Service
        </div>
        <div class="col-sm-12">
            <div class="csv_export_button">
                <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#mi_list'), 'MI_List.csv']);" data-tooltip="tooltip" title="Download M.I. List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
            </div>
            <table class="table table-bordered custom-table table-striped" id="mi_list" style="width: 100%;">
                <thead>
                    <tr>
                        <th class="exclude_export" colspan="2" style="max-width: 200px; width: 200px;">Purpose</th>
                        <th class="exclude_export" rowspan="2" style="min-width: 150px; width: 150px; max-width: 250px;">Item</th>
                        <th class="exclude_export" rowspan="2" style="min-width: 150px; max-width: 150px; width: 150px;">Required Qty / OA Qty UoM</th>
                        <th class="exclude_export" rowspan="2" style="max-width: 100px; width: 100px;">Delivery Due</th>
                        <th class="exclude_export" rowspan="2" style="max-width: 100px; width: 100px;">MI No<small> MI Date</small></th>
                        <th class="hide">Purpose</th>
                        <th class="hide">Customer</th>
                        <th class="hide">OA NO</th>
                        <th class="hide">OA Date</th>
                        <th class="hide">Item Name</th>
                        <th class="hide">Required Quantity</th>
                        <th class="hide">Oa Qty</th>
                        <th class="hide">Unit Name</th>
                        <th class="hide">Delivery Date</th>
                        <th class="hide">MI NO</th>
                        <th class="hide">MI Date</th>
                    </tr>
                    <tr>
                        <th style="max-width: 60px; width: 100px;" class="exclude_export ">Customer</th>
                        <th style="max-width: 60px; width: 100px;" class="exclude_export ">OA No & Date</th>
                        <th class="hide"></th>
                        <th class="hide"></th>
                        <th class="hide"></th>
                        <th class="hide"></th>
                        <th class="hide"></th>
                        <th class="hide"></th>
                        <th class="hide"></th>
                        <th class="hide"></th>
                        <th class="hide"></th>
                        <th class="hide"></th>
                        <th class="hide"></th>
                    </tr>
                    {% if logged_in_user|canApprove:'MANUFACTURE INDENT' %}
                        <tr class="mi-add-row">
                             <th colspan="2" class='exclude_export text-left'>
                                <input type="text" value="" class="form-control mi_purpose" id="manufacturing_purpose" maxlength="200" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" placeholder="Purpose">
                            </th>
                            <th class="exclude_export text-left">
                                <div class="material_name">
                                    <input type="text" value=""  class="form-control ui-autocomplete-input mi_item" id="material_required"  maxlength="100" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" placeholder="Select Item">
                                    <input type="hidden" value="" class="mi_material_id" id="material_id_hidden" placeholder="" hidden="hidden">
                                    <input type="hidden" value="" class="mi_make_id" id="make_id_hidden" placeholder="" hidden="hidden">
                                    <input type="hidden" value="" class="" id="material_is_service" placeholder="" hidden="hidden">
                                    <span class="material-removal-icon removal-icon-th hide" style="padding-top: 8px">
                                        <i class="fa fa-times"></i>
                                    </span>
                                </div>
                            </th>
                            <th class="exclude_export">
                                <input type="text" id="manufacturing_qty" class="form-control mi_qty" maxlength="16" placeholder="Reqd Qty" value="0.00" onfocus="setNumberRangeOnFocus(this,12,3)"/>
                                <span id="manufacturing_unit_display" class="unit_display pull-right mi_unit" >&nbsp;</span>
                                <div class="alternate_unit_select_box hide" style="position: relative;margin-right: -16px">
                                    <select class="form-control unit_select_box" name="select" id="id_material-alternate_units"  style="max-width: 54px !important;">
                                    </select>
                                </div>
                                <div class="all_units_select_box hide" style="position: relative;margin-right: -16px">
                                    <select class="form-control unit_select_box" name="select" id="id_material-all_units" style="max-width: 54px !important;"></select>
                                </div>
                            </th>
                            <th class="exclude_export">
                                <input type="text" id="manufacturing_delivery_due" class="form-control custom_datepicker set-my-start-date fixed-width-medium mi_delivery_due" data-setdate="" placeholder="Select Date" readonly="readonly">
                                <i class="glyphicon glyphicon-calendar custom-calender-icon"></i>
                            </th>
                           <th class="exclude_export" rowspan="1" colspan="1" style="text-align: center">
                                <button class="btn btn-new-item" id="addmaterials" data-tooltip="tooltip" title="Create Manufacturing Indent" onclick="createManufacturingIndent('', 'forecast', 'add-through-forecast')">
                                    <i class="fa fa-plus" aria-hidden="true"></i>
                                    <span class="btn-new-item-label"></span>
                                </button>
                           </th>
                            <th class="hide"></th>
                            <th class="hide"></th>
                            <th class="hide"></th>
                            <th class="hide"></th>
                            <th class="hide"></th>
                            <th class="hide"></th>
                            <th class="hide"></th>
                            <th class="hide"></th>
                            <th class="hide"></th>
                            <th class="hide"></th>
                            <th class="hide"></th>
                        </tr>
                    {% endif %}
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
<div id="edit_manufacturing_indent" class="modal" role="dialog" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog" style="width: 500px; margin-top: 72px;">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Edit</h4>
            </div>
            <div class="modal-body" style="min-height: 350px;">
                <div class="form-group">
                    <label>Purpose</label>
                    <input type="text" value="" class="form-control mi_purpose" id="edit_manufacturing_purpose" maxlength="200" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" placeholder="Purpose">
                </div>
                <div class="form-group">
                    <label>Item</label>
                    <input type="text" value=""  class="form-control ui-autocomplete-input mi_item" id="edit_material_required" readonly="readonly"  maxlength="100" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" placeholder="Select Item">
                    <input type="hidden" value="" class="mi_material_id" id="edit_material_id_hidden" placeholder="" hidden="hidden">
                    <input type="hidden" value="" class="mi_make_id" id="edit_make_id_hidden" placeholder="" hidden="hidden">
                    <span class="edit_material-removal-icon removal-icon-th" style="padding-top: 8px">
                        <i class="fa fa-times"></i>
                    </span>
                </div>
                <div class="form-group">
                    <label>Required Qty</label>
                    <input type="text" id="edit_manufacturing_qty" class="form-control mi_qty" maxlength="16" placeholder="Reqd Qty" value="0.00" onfocus="setNumberRangeOnFocus(this,12,3)"/>
                    <span id="edit_manufacturing_unit_display" class="unit_display pull-right mi_unit" >&nbsp;</span>
                    <div class="alternate_unit_select_box hide" style="position: relative;margin-right: -16px">
                        <select class="form-control unit_select_box" name="select" id="edit_id_material-alternate_units" style="max-width: 54px !important;"></select>
                    </div>
                </div>
                <div class="form-group">
                    <label>Delivery Due</label>
                    <input type="text" id="edit_manufacturing_delivery_due" class="form-control custom_datepicker set-my-start-date fixed-width-medium mi_delivery_due" data-setdate="" placeholder="Select Date" readonly="readonly">
                    <i class="glyphicon glyphicon-calendar custom-calender-icon"></i>
                </div>

            </div>
            <div class="modal-footer">
                <button id="update_mi" class="btn btn-save" onclick="createManufacturingIndent('', 'forecast', 'edit-through-forecast');">Update</button>
                <span class="btn btn-cancel" data-dismiss="modal">Cancel</span>
            </div>
        </div>
    </div>
</div>
<div id="mi_remarks_modal" class="modal fade" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Remarks</h4>
            </div>
            <div class="modal-body">
                <div class="remarks-add-container">
                    {% if logged_in_user|canApprove:'MANUFACTURE INDENT' %}
                        <div class="row" style="margin-bottom: 15px;">
                            <div class="col-sm-10">
                                <label>Remarks <span class="qtip-question-mark" data-tooltip="tooltip"
                                                    data-placement="right" title="Internal Remarks. Will not be printed in document."></span>
                                </label>
                                <textarea rows="2" maxlength="300" placeholder="Enter Remarks " id="id_mi-remarks"
                                          cols="40" class="form-control"
                                          onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')"
                                          onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" ></textarea>
                            </div>
                            <div class="col-sm-2">
                                <button type="button" class="btn btn-save" style="margin-top: 42px;" onclick="saveNewRemarks(this)">+</button>
                            </div>
                        </div>
                    {% endif %}
                </div>
                <div class="remarks-content-container">

                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
{% include "masters/add_material_modal.html" %}
{% endblock %}