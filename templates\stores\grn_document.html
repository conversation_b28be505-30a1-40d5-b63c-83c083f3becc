<div id="grn_document_modal" class="modal fade" role="dialog">
  	<div class="modal-dialog modal-lg">
		<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Preview Document</h4>
      		</div>
      		<div class="modal-body text-center">
      			<input id="modal_grn_id" name="grn_id" value='' hidden="hidden"/>
		        <input id="modal_selected_grn" name="mod_selected_supplier" value='' hidden="hidden"/>
		        <input id="modal_received_against" name="mod_received_against" value='' hidden="hidden"/>
      			<div class="row" style="text-align: left;">
					<div class="col-lg-12">
						<div class="content_bg">
							<div class="add_table">
								<form id="pdf_generation" method="POST" action="/erp/stores/grn/generate_documents/">{% csrf_token %}
									<input type="submit" hidden="hidden" value="Approve/Reject" id="view_receipt_document"/>
									<input type="hidden" name="receipt_no" id="id_receipt_no" value="{{receipt_no}}" />
									<div class="col-sm-6" style="padding-left: 0;">
										{%if access_level.edit or access_level.approve%}
											<input id="remarks" class="form-control" enterkey="do_nothing" maxlength="300" placeholder="Approval/Rejection Remarks" />
										{% endif %}
								       	<div class="remarks_count_link remarks_count disabled" onclick="remarksInSwal('grn_document_remarks');">
											<span class="remarks_counter">No</span><span> remarks</span>
										</div>
								   	</div>
								   	<div id="grn_doc_btn">
										{% if access_level.approve %}
										<span class="material_txt">
											<a role='button' id='approve_grn' class="btn btn-save">Approve</a>
										</span>
									    {% endif %}
									    {% if access_level.approve %}
											<a role='button' id='reject_grn' class="btn btn-danger for_grn_approve">Reject</a>
										{% else %}
										{%if access_level.edit %}
											<a role='button' id='reject_grn' class="btn btn-danger for_grn_edit">Discard</a>
										{% endif %}
										{% endif %}
									</div>
									<div class="grn_icd_status" style="font-size: 16px; color: #28a745; float: right; margin-right: 30px;"></div>
								</form>
							</div>
						</div>
					</div>
				</div>
				<input type="hidden" id="grn_document_remarks" />
				<div id="grn_document_container"></div>
  			</div>
  			<div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
  		</div>
  	</div>
</div>
<script type="text/javascript" src="/site_media/js/grn-document.js?v={{ current_version }}"></script>
<script type="text/javascript">
	$(document).ready(function(){
		listenDocumentPopupEvents();
	})
</script>