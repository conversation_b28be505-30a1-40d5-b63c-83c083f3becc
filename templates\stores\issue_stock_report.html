{% extends "stores/sidebar.html" %}
{% block stockreport %}
{% if access_level.view %}
    <link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
    <link type="text/css" rel="stylesheet" href="/site_media/css/chosen.css?v={{ current_version }}">
    <link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
    <script type="text/javascript" src="/site_media/js/bootstrap-datepicker.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/report.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/bootstrap-multiselect.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/chosen.jquery.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/jquery.easyResponsiveTabs.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/daterangepicker.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/issue_stock_report.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/load_material.js?v={{ current_version }}"></script>

    <style>
        li.stock_report_menu a {
            outline: none;
            background-color: #e6983c !important;
        }
    </style>
    <div class="right-content-container download-icon-onTop">
        <div class="page-title-container">
            <span class="page-title">{{template_title}}</span>
        </div>
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12">
                    <div class="content_bg">
                        <ul class="resp-tabs-list hor_1"></ul>
                        <div class="resp-tabs-container hor_1">
                            <div class="row">
                                <div class="add_table">
                                    <div class="col-lg-12 add_table_content">
                                        <div class="filter-components">
                                            <div class="filter-components-container">
                                                <div class="dropdown">
                                                    <button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
                                                        <i class="fa fa-filter"></i>
                                                    </button>
                                                    <span class="dropdown-menu arrow_box arrow_box_filter">
                                                        <div class="col-sm-12 form-group" >
                                                            <label>Date Range</label>
                                                            <div id="reportrange" class="report-range form-control">
                                                                <i class="glyphicon glyphicon-calendar"></i>&nbsp;
                                                                <span></span> <b class="caret"></b>
                                                                <input type="hidden" class="fromdate" id="fromdate" name="fromdate" value="{{since}}"/>
                                                                <input type="hidden" class="todate" id="todate" name="todate" value="{{till}}"/>
                                                            </div>
                                                        </div>
                                                        <div class="col-sm-12 form-group pendingdet">
                                                            <label>Issued To</label>
                                                            <select class="form-control chosen-select" name="select"
                                                                    id="id-issued_to">
                                                                <option value="">All</option>
                                                                {% for j in issued_to_list.0 %}
                                                                    {%if j.0 == issued_to %}
                                                                    <option value="{{ j.0 }}" selected="selected">{{ j.0 }}</option>
                                                                    {% else %}
                                                                    <option value="{{ j.0 }}">{{ j.0 }}</option>
                                                                    {% endif %}
                                                                {% endfor %}
                                                            </select>
                                                        </div>
                                                        <div class="col-sm-12 form-group pendingdet">
                                                            <label>Material Name</label>
                                                            <select class="form-control chosen-select" name="select" id="id-material">
                                                            </select>
                                                        </div>
                                                        
                                                        <div class="col-sm-12 form-group">
                                                            <div class="checkbox" style="padding-left: 20px !important;">
                                                                <input name="ignore_items_not_transacted" id="ignore_items_not_transacted" type="checkbox"{% if ignore_items_not_transacted == True %} checked="checked" {%endif%}/>
                                                                <label for="ignore_items_not_transacted">IGNORE ITEMS NOT TRANSACTED DURING THE SET PERIOD</label>
                                                            </div>
                                                        </div>
                                                       <div class="filter-footer">
                                                            <input type="button" class="btn btn-save" value="Apply" id="id-search_isr"/>
                                                        </div>
                                                    </span>
                                                </div>
                                                <span class='filtered-condition filtered-date'>Date: <b></b></span>
                                                <span class='filtered-condition filtered-party'>Issued To: <b></b></span>
                                                <span class='filtered-condition filtered-material'>Material Name: <b></b></span>
                                                <span class='filtered-condition filtered-draft'>Ignore items not transacted during the set period: <b></b></span>
                                            </div>
                                        </div>                                        
										<div class="col-lg-12 search_result_table">
                                            <div class="csv_export_button">
                                                <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#tbl_issue_stock_report'), 'Internal_Stock_Flow.csv']);" data-tooltip="tooltip" data-placement="bottom" title="Download&nbsp;Internal&nbsp;Stock&nbsp;Flow as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
                                            </div>
											<table class="table table-bordered custom-table table-striped grn_tbl" id="tbl_issue_stock_report" style="width: 100%;">
												<thead class="th-vertical-center">
                                                    <tr class="exclude_export">
                                                        <th rowspan="2">S. No</th>
                                                        <th rowspan="2">ISSUED TO</th>
                                                        <th rowspan="2">MATERIAL</th>
                                                        <th rowspan="2">UNIT</th>
                                                        <th rowspan="2">STOCK VALUE</th>
                                                        <th colspan="2">OPENING</th>
                                                        <th colspan="2">ISSUED</th>
                                                        <th colspan="2">RECEIVED/RETURNED</th>
                                                        <th colspan="2">CLOSING</th>
                                                    </tr>
                                                    <tr class="exclude_export">
                                                        <th>Quantity</th>
                                                        <th>Value</th>
                                                        <th>Quantity</th>
                                                        <th>Value</th>
                                                        <th>Quantity</th>
                                                        <th>Value</th>
                                                        <th>Quantity</th>
                                                        <th>Value</th>
                                                    </tr>
                                                    <!-- This <tr> is used for csv download. Don't delete -->
                                                    <tr>
                                                        <th hidden="hidden">S. No</th>
                                                        <th hidden="hidden">ISSUED TO</th>
                                                        <th hidden="hidden">MATERIAL</th>
                                                        <th hidden="hidden">UNIT</th>
                                                        <th hidden="hidden">STOCK VALUE</th>
                                                        <th hidden="hidden">OPENING-Quantity</th>
                                                        <th hidden="hidden">OPENING-Value</th>
                                                        <th hidden="hidden">ISSUED-Quantity</th>
                                                        <th hidden="hidden">ISSUED-Value</th>
                                                        <th hidden="hidden">RECEIVED/RETURNED-Quantity</th>
                                                        <th hidden="hidden">RECEIVED/RETURNED-Value</th>
                                                        <th hidden="hidden">CLOSING-Quantity</th>
                                                        <th hidden="hidden">CLOSING-Value</th>
                                                    </tr>
												</thead>
												<tbody id="isr_tbl_tbody">
                                                    
												</tbody>
											</table>
										</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            {% block stock_statement %}
                            {% endblock %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">

        $(window).load(function(){
            updateFilterText();
        });

        function updateFilterText() {
            $(".filtered-date b").text($("#reportrange").find("span").text());
            $(".filtered-party b").text($("#id-issued_to option:selected").text());
            $(".filtered-material b").text($("#id-material option:selected").text());
            $(".filtered-draft b").text($("#ignore_items_not_transacted").is(":checked")?"Yes":"No");
        }
        var oTable;
        var oSettings;

        $(document).ready(function () {
            $(".nav-pills li").removeClass("active");
            $("#li_issue_stock_report").addClass("active");
            TableHeaderFixed(tableId="tbl_issue_stock_report","tbl_issue_stock_report_filter");
            onPageLoad();
        });

    </script>
{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
{% endblock %}