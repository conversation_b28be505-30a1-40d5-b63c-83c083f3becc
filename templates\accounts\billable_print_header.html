<!DOCTYPE html>
<style type="text/css">
	body{
		color: #000;
		font-family: 'Times New Roman';
	}

	.enterprise_details{
		font-size:15px;
	}

	.enterprise_name{
		font-size:15px;
	}

	.vendor_data{
<!--		width: calc(100% - 1em);-->
		float: left;
		margin-left: 160px;
		margin-top: -16px;
		font-size:14px;
	}

	.vendor_list{
		font-weight:bold;
		font-size:16px;
	}

	hr{
		border: 0;
  		border-top: 1px solid #ddd !important;
	}

	@font-face {
        font-family: 'Times New Roman';
        font-style: normal;
        font-weight: 400;
        src: url(https://www.xserp.in/site_media/fonts/times.ttf) format('truetype');
	}
</style>
<script>
	function subst1() {
		var vars={};
		var x=document.location.search.substring(1).split('&');
		for (var i in x) {
			var z=x[i].split('=',2);
			vars[z[0]] = unescape(z[1]);
		}
		var x=['frompage','topage','page','webpage','section','subsection','subsubsection'];
		for (var i in x) {
			var y = document.getElementsByClassName(x[i]);
			for (var j=0; j<y.length; ++j)
				y[j].textContent = vars[x[i]];
			if(vars['page'] == vars['frompage']){
			   document.getElementById("header_div").style.display = 'block';
			}
			else {
			   document.getElementById("header_div").style.display = 'none';
			}

		}
	}
</script>
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap.css">
<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css">
<body onload="subst1()">
<div id="header_div">
		<div>
			<div class="col-sm-8">
				<img src="{{enterprise_logo}}" style="max-height: 10mm">
				<div class="enterprise_name">{{ enterprise.name }}</div>
				<div class="enterprise_details"> {{ enterprise.address_1 }}, {% if enterprise.address_2 and enterprise.address_2 != "" %}{{ enterprise.address_2 }}, {% endif %}{{ enterprise.city }} - {{ enterprise.pin_code }}, {{ enterprise.state }}<br></div>
				<div class="enterprise_details"><b>Ph:</b>{% if enterprise.primary_contact_details.contact.phone_no %} {{ enterprise.primary_contact_details.contact.phone_no }} {% endif %}<b>E-mail:</b>{% if enterprise.primary_contact_details.contact.email %} {{ enterprise.primary_contact_details.contact.email }} {% endif %}</div>
			</div>
		</div>
		<div>
			<div><br>
				<div class="vendor_list" >Party Name </div>
				<div class="vendor_data">: <b>{{ party_details.party_name }}</b></div>
			</div>
			<div>
				<div class="vendor_list">Party Address</div>
				<div class="vendor_data">: {{ party_details.address_1 }},{% if party_details.address_2 and party_details.address_2 != "" %}{{ party_details.address_2 }},{% endif %}<br></div><br>
				<div class="vendor_data">&nbsp; {{ party_details.city }},{{ party_details.state }}</div>
			</div>
			<div>
				<div class="vendor_list" >Phone No </div>
				<div class="vendor_data">: {{ party_details.phone_no }}</div>
			</div>
		</div>
		<div class="col-sm-12">
			<p> Advance Outstanding - Advances received against bill</p>
		</div>

		<div class= "col-sm-12">
			<p>Dear Sir/Madam,</p>
			<p style="text-align:center"><b>Reg : </b> Outstanding as on {{ current_date }} Value: {{ outsourcing_advance }}</p>
			<p>We wish to inform you having Outstanding  with us as follows :</p><br>
		</div>
	</div>
</body>