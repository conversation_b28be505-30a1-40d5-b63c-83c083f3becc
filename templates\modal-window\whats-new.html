<style>
  .carousel-container {
    display: flex;
    overflow-x: hidden;
    position: relative;
    white-space: nowrap;
  }

  .carousel-wrapper {
    display: flex;
    transition: transform 0.5s ease;
  }

  .slider_content {
    flex: 0 0 100%;
    display: inline-block;
    text-align: center;
    white-space: normal;
  }

  .img-responsive {
    max-width: 100%;
    height: auto;
  }

  .btn-prev, .btn-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.5);
    border: none;
    color: #fff;
    padding: 10px 15px;
    cursor: pointer;
  }
  .btn-prev {
    left: 10px;
  }

  .btn-next {
    right: 10px;
  }
</style>

<div id="whatNewModal" class="modal fade" role="dialog" data-backdrop="static" data-keyboard="false">
  <div class="modal-dialog modal-lg">
    <div class="modal-content" style="background:#2680b3; color:#fff;">
      <div class="modal-header" style="margin-bottom:0px;">
          <h4 class="modal-title text-center whats_new-title"><b>What's New</b></h4>
          <button type="button" class="close" style="margin-top: -23px;margin-right: 12px;opacity:0.8; color: #fff;" onClick="skipSlider();">&times;</button>
        <h6 style="margin-bottom:-7px;"><i>To view this section navigate to <b> Settings > What's New</b></i></h6>
      </div>
      <div class="carousel-container">
        <div class="carousel-wrapper">
<!--          <div class="slider_content text-center active" data-slider="1">-->
<!--            <h4></h4>-->
<!--            <img class="img-responsive img-whats-new" src="/site_media/images/whats_new/Production-Plan-01-10-2025_04_27_PM.png" style="width: 100%" />-->
<!--          </div>-->
          <div class="slider_content text-center" data-slider="2">
            <h4>Production Plan with MRC (Material Requirement Calculator)</h4>
            <img class="img-responsive img-whats-new" src="/site_media/images/whats_new/mrp1.png" style="width: 100%; padding:6% ;border:none !important" />
          </div>
          <div class="slider_content text-center" data-slider="3">
            <h4>New Page: Material Requirement Calculator</h4>
            <img class="img-responsive img-whats-new" src="/site_media/images/whats_new/mrp2.png" style="width: 100%; padding:6% ;border:none !important" />
          </div>
           <div class="slider_content text-center" data-slider="4">
            <h4>Allocation and De-allocation of Materials for Production Plan</h4>
            <img class="img-responsive img-whats-new" src="/site_media/images/whats_new/mrp3.png" style="width: 100%; padding:6% ;border:none !important" />
          </div>
          <div class="slider_content text-center" data-slider="5">
            <h4>Stock Transfers based on Production Plan</h4>
            <img class="img-responsive img-whats-new" src="/site_media/images/whats_new/mrp4.png" style="width: 100%; padding:6% ;border:none !important" />
          </div>
        </div>
      </div>
      <div class="modal-footer" style="border:none;">
        <button type="button" class="btn btn-prev hide" onClick="prevSlider();" style=" background:transparent; font-weight:800; border:1px solid #fff; color:#fff;"> < </button>
        <button type="button" class="btn btn-next" onClick="nextSlider();" style="background:transparent; font-weight:800; border:1px solid #fff; color:#fff"> > </button>
        <button type="button" class="btn btn-more" style="border: 1px solid #fff;background: transparent;color:#fff">
          <a href="/site_media/docs/MRC_release_notes_.pdf" target="_blank" style="color:#fff;text-decoration:none;">To know more Click Here</a>
        </button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
  let currentIndex = 0;

function updateCarousel() {
  const wrapper = document.querySelector(".carousel-wrapper");
  const slides = document.querySelectorAll(".slider_content");
  const slideWidth = slides[0].offsetWidth;
  wrapper.style.transform = `translateX(-${currentIndex * slideWidth}px)`;

  // Handle button visibility
  document.querySelector(".btn-prev").classList.toggle("hide", currentIndex === 0);
  document.querySelector(".btn-next").classList.toggle("hide", currentIndex === slides.length - 1);
}

function nextSlider() {
  const slides = document.querySelectorAll(".slider_content");
  if (currentIndex < slides.length - 1) {
    currentIndex++;
    updateCarousel();
  }
}

function prevSlider() {
  if (currentIndex > 0) {
    currentIndex--;
    updateCarousel();
  }
}

function skipSlider() {
  $("#whatNewModal").modal("hide");
}

function openSlider() {
  currentIndex = 0;
  updateCarousel();
  $("#whatNewModal").modal("show");
}

</script>



