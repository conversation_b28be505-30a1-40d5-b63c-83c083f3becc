GRN_COMMON_COLUMNS = """grn.grn_no AS grn_no,
	grn.invno AS inv_no,
	grn.net_inv_value AS inv_value,
	grn.status AS status,
	grn.rec_against AS rec_against,
	grn.receipt_no AS receipt_code,
	grn.financial_year AS financial_year,
	grn.sub_number AS sub_number,
	DATE_FORMAT(grn.grn_date, '%d-%m-%Y') AS grn_date,
	DATE_FORMAT(grn.inv_date, '%d-%m-%Y') AS inv_date,
	grn.party_id AS supplier_id,
    IFNULL(CONCAT(projects.name,' (', projects.code ,')'), "-NA-") AS project,
	party_master.party_name AS supplier,
	IF(grn.receipt_no != 0, CONCAT(
	grn.financial_year, '/',
	IF(grn.rec_against = "Sales Return", "SR", IF(rec_against = "Issues", "IRN", "GRN")),
	'/', LPAD(grn.receipt_no, 6, 0), 
	IFNULL(grn.sub_number, '')), 
	CONCAT(IF(grn.rec_against = "Sales Return", "TMP#SR-", ""), grn.grn_no)) AS code,
	grn_material.dc_id AS dc_id,
	COUNT(grn_material.item_id) AS material_count,
	SUM(grn_material.acc_qty) AS material_accepted,
	materials.name AS consolidated_material,
	materials.makes_json AS make_name"""

GRN_NOT_OTHER_COLUMNS = """, invoice.issued_to AS supplier_name,
	GROUP_CONCAT(DISTINCT IFNULL(invoice.invoice_code, invoice.id)) AS invoice_codes,
	GROUP_CONCAT(
	DISTINCT(
	CONCAT(
	IFNULL(purchase_order.financial_year, 'DRAFT#'), IF(purchase_order.type = 1, '/JO/', '/PO/'),
	LPAD(purchase_order.orderno, 6, 0), IFNULL(purchase_order.sub_number, '')))) AS po_codes,
	GROUP_CONCAT(
	DISTINCT(
	CONCAT(
	order_acknowledgement.financial_year, "/", LEFT(UPPER(order_acknowledgement.type), 1), 
	LPAD(order_acknowledgement.oa_no, 6, 0), IFNULL(order_acknowledgement.sub_number, "")))) AS oa_codes, 
	purchase_order.issue_to AS pp_issue_to, purchase_order.type AS po_type
	"""

GRN_COMMON_QUERY = """
	FROM grn
	LEFT OUTER JOIN
		projects ON projects.id = grn.project_code
		AND projects.enterprise_id = grn.enterprise_id
	LEFT OUTER JOIN
		party_master ON party_master.party_id = grn.party_id
	LEFT OUTER JOIN
		grn_material ON grn_material.`grnNumber` = grn.grn_no
	LEFT OUTER JOIN
		materials ON grn_material.item_id = materials.id
		AND grn_material.enterprise_id = materials.enterprise_id"""

GRN_NOT_OTHER_QUERY = """ LEFT OUTER JOIN
		grn_issue_map ON grn_issue_map.grn_no = grn.grn_no
		AND grn_issue_map.enterprise_id = grn.enterprise_id
	LEFT OUTER JOIN
		invoice ON invoice.id = grn_issue_map.issue_id
		AND invoice.enterprise_id = grn_issue_map.enterprise_id
		OR invoice.id = grn_material.dc_id
		AND invoice.enterprise_id = grn_material.enterprise_id
	LEFT OUTER JOIN
		purchase_order ON purchase_order.id > 0
		AND grn_material.po_no = purchase_order.id
		AND grn_material.enterprise_id = purchase_order.enterprise_id
	LEFT OUTER JOIN
		order_acknowledgement ON order_acknowledgement.id > 0
		AND grn_material.oa_id = order_acknowledgement.id
		AND grn_material.enterprise_id = order_acknowledgement.enterprise_id"""

INDENT_MATERIALS_QUERY = """SELECT * FROM (
		SELECT b.name, a.request_qty AS quantity, b.drawing_no, IFNULL(
			(SELECT price FROM recent_valid_price_profiles
				WHERE a.item_id=item_id AND supp_id=%s AND make_id=a.make_id
					AND is_service=%s AND enterprise_id=a.enterprise_id					
					AND status = 1 ORDER BY effect_since DESC LIMIT 0, 1), 0) AS price,
			IFNULL((SELECT moq FROM recent_valid_price_profiles
				WHERE a.item_id=item_id AND supp_id=%s AND make_id=a.make_id
					AND is_service=%s AND enterprise_id=a.enterprise_id					
					AND status = 1 ORDER BY effect_since DESC LIMIT 0, 1), 0) AS moq, 
			(SELECT IFNULL(SUM(m.pur_qty),0) FROM purchase_order_material AS m, purchase_order AS o
				WHERE o.id=m.pid AND o.indent_no = a.indent_no AND o.enterprise_id=a.enterprise_id
					AND m.item_id=a.item_id AND m.enterprise_id=a.enterprise_id
					AND m.make_id=a.make_id AND o.status <> 3 %s) AS pur_qty, IFNULL(
			(SELECT sum(acc_qty) FROM grn_material b, purchase_order AS t,grn as g 
				WHERE a.item_id=b.item_id AND a.make_id=b.make_id
					AND b.enterprise_id=a.enterprise_id AND t.enterprise_id=a.enterprise_id
					AND b.po_no=t.id AND a.indent_no=t.indent_no AND g.grn_no=b.grnNumber AND g.status >-1 %s), 0) AS rec_qty, IFNULL(
			(SELECT make_id FROM indent_material
				WHERE item_id=a.item_id AND enterprise_id=a.enterprise_id
					AND indent_no=a.indent_no AND make_id=a.make_id LIMIT 1), 1) as make_id, IFNULL(
			b.makes_json, "") AS make_name, 
			IFNULL(b.standard_packing_quantity, "") AS spq,
			b.unit AS unit, c.unit_name as unit_name,
			a.item_id as item_id, IFNULL(a.alternate_unit_id, 0) as alternate_unit_id, b.is_service as is_service 
		FROM indent_material AS a, materials AS b,unit_master as c
		WHERE b.id=a.item_id AND a.enterprise_id=b.enterprise_id AND indent_no ='%s' AND a.enterprise_id='%s'
		AND c.unit_id = b.unit GROUP BY a.item_id,a.make_id) AS indent_indent_materials
	WHERE indent_indent_materials.quantity > 0 ORDER BY price DESC"""