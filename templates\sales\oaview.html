{% extends "sales/sidebar.html" %}
{% block oa %}
<style xmlns="http://www.w3.org/1999/html">
	li.sales_side_menu a{
	outline: none;
	background-color: #484848 !important;
	}
	li.sales_side_menu {
	margin-bottom: 5px;
	}
	#cattable {
		width: 550px;
		position: absolute;
		color: #000000;
		background-color: #FFFFFF;
		/* To align popup window at the center of screen*/
		top: 50%;
		left: 50%;
		margin-top: 100px;
		margin-left: 100px;
	}

	#cattable_2 input,
	#cattable_2 select{
		padding: 0 1px;
		font-size: 12px;
	}

	#cattable_2 td {
		padding: 8px 4px;
	}
	#cattable_2 tr[data-toggle='open'] td:nth-child(13){
		opacity: 0.5;
		background: #ddd;
		pointer-events: none;
	}

	#cattable_2 tr[data-toggle='open'] input,
	#cattable_2 tr[data-toggle='open'] select {
	    opacity: 0.5;
	    background: #ddd;
	}

	.empty-error-border {
	    border: 1px solid #dd4b39 !important;
	}
</style>

<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/notify.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/mailer.js?v={{ current_version }}"></script>

<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">Order Acknowledgement</span>
	</div>
	<div class="col-lg-12">
		{% if access_level.edit %}	
			<div class="page-heading_new for-primary-ent">
				<a href="/erp/sales/oa/" class="btn btn-new-item pull-right" data-tooltip="tooltip" title="Create New OA"><i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span></a>
			</div>
		{% else %}	
			<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in Sales Module. Please contact the administrator." style="margin-right: 15px;">
				<a class="btn btn-new-item pull-right disabled"><i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span></a>
			</div>
		{% endif %}	
		<div class="filter-components">
			<div class="filter-components-container">
				<div class="dropdown">
					<button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
						<i class="fa fa-filter"></i>
					</button>
					<span class="dropdown-menu arrow_box arrow_box_filter">
				  		<input type="hidden" id="last_created_order_code" value="{{last_created_order}}"/>
						<form id="oa_search" method="post" action="/erp/sales/oa/view/">
							{%csrf_token %}
							<div class="col-sm-12 form-group" >
								<label>Date Range</label>
								<div id="reportrange" class="report-range form-control">
									<i class="glyphicon glyphicon-calendar"></i>&nbsp;
									<span></span> <b class="caret"></b>
									{{ search.from_date }}
									{{search.to_date }}
								</div>
							</div>
							<div class="col-sm-12 form-group">
								<label>Party Name</label>
								{{ search.party_name }}
							</div>
							<div class="col-sm-12 form-group">
								<label>Status</label>
								{{search.status}}
							</div>
							<div class="checkbox" style="display: inline-block; margin-top: -15px;">
	                            <input name="sales_status" id="id_sales_status" type="checkbox"/>
	                            <label for="id_sales_status">Include Sales Status</label>
								<input type="hidden" id="id_exclude_value"   value="{{exclude_value}}"/>
	                        </div>
							<div class="filter-footer">
								<input type="submit" class="btn btn-save" value="Apply" id="id_search_submit"/>
	      					</div>
						</form>
					</span>
				</div>
				<span class='filtered-condition filtered-date'>Date: <b></b></span>
				<span class='filtered-condition filtered-party'>Party Name: <b></b></span>
				<span class='filtered-condition filtered-status'>Status: <b></b></span>
				<span class='filtered-condition filtered-sales-status'>Sales Status: <b></b></span>
			</div>
		</div>
		<div id="view_icd">
			<div class="clearfix"></div>
			<div class="col-lg-12" id="search_result">
				<div class="csv_export_button">
	                <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#oa_list'), 'OA_List.csv']);" data-tooltip="tooltip" title="Download {% if isprimary_project %}OA{% else %}IWO{% endif %} List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
	            </div>
				<table class="table table-bordered custom-table table-striped" id="oa_list" style="width: 100%;">
					<thead>
					<tr>
						<th style="width: 30px;"> S.No. </th>
						<th style="width: 100px; min-width: 80px;" id="for_sec_ent_no"> OA No</th>
						<th style="width: 60px;"> Type</th>
						<th style="width: 120px;"> Prepared On</th>
						<th style="width: 120px;" id="for_sec_ent_date"> OA Date</th>
						<th style="width: 300px;"> Customer</th>
						<th style="width: 80px;" id="for_sec_ent_val"> OA Value</th>
						{% if exclude_value == 1 %}
							<th style="width: 80px;" id="for_sec_ent_invoice_val"> Invoice Value</th>
							<th style="width: 80px;"> Pending Value</th>
						{% endif %}
						<th style="width: 100px; min-width: 100px;"> Status</th>
					</tr>
					</thead>
					<tbody>
					{% for in_list in oa_list %}
					<tr align="center" data-oaid='{{ in_list.id }}'>
						<td>{{forloop.counter}}.</td>
                        <td>
							<a role="button" class="edit_link_code" data-orderAckId="{{ in_list.id }}" onclick="editOaRow('{{ in_list.id }}');">
								{{in_list|getInternalCode}}
							</a>
						</td>
						<td align="center"> {{in_list.type}}</td>
						<td> {{in_list.prepared_on|date:'M d, Y H:i:s'}}</td>
                        <td class="td-oa-date"> {{in_list.approved_on|date:'M d, Y H:i:s'}}</td>
                        <td align="left"> {{in_list.customer.name }}{% if isprimary_project != True %} ({{in_list.customer.code }}){% endif %}</td>
                        <td align="right"> {{in_list.grand_total|floatformat:2 }}</td>
						{% if exclude_value == 1 %}
							<td align="right"> {{in_list.getInvoiceValue|floatformat:2 }}</td>
							<td align="right"> {{in_list.getPendingValue|floatformat:2 }}</td>
						{% endif %}
                        <td align="center" class='td_status' style="padding: 0;" data-sort='{{in_list|getStatusName}}'>
							<a role="button" class='table-inline-icon-bg {{in_list|getStatusName}}'>
								{{in_list|getStatusName|capfirst}}
							</a>
							{% if in_list.document != None and in_list.document != "" %}
								<span class='table-inline-icon document-viewer hide' data-tooltip='tooltip' data-placement='left' title='Attachment' onclick="viewAttachedDocumentByWindow(this)" data-extension="pdf" data-url="{{ in_list.document }}">
	                                <i class="fa fa-paperclip"></i>
	                            </span>
							{% endif %}
							<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Preview' onclick="javascript:{% if access_level.view %}generate_pdf_ajax({{in_list.id}},{{in_list.status}});{% else %}alert('You don\'t have sufficient permission to Process this OA');{% endif %}">
                                <i class="fa fa-file-text"></i>
                            </span>
                            <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit' role='button' onclick="editOaRow('{{ in_list.id }}');">
                                <i class='fa fa-pencil'></i>
                            </span>
                            <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit in New Tab' role='button' onclick="editOaRow('{{ in_list.id }}', '_blank');">
                                <i class='fa fa-external-link'></i>
                            </span>
						</td>
					</tr>
					{% endfor %}
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>
{% include "attachment_popup.html" %}
<div id="oa_document_modal" class="modal fade" role="dialog">
  	<div class="modal-dialog modal-lg">
		<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Preview Document</h4>
      		</div>
      		<div class="modal-body text-center">
      			<input id="modal_oa_id" name="oa_id" value='' hidden="hidden"/>
      			<div class="row" style="text-align: left;">
					<div class="col-lg-12">
						<div class="content_bg">
							<div class="add_table" id="oa_doc_btn">
								<form id="oa_approval_form" method="POST" action="/erp/sales/oa/generateOADoc/">
									{% csrf_token %}
									<div class="col-sm-6 add_table_content" style="padding-left: 0;">
										<input id="oa_id" name="oa_no" value='' hidden="hidden"/>
										<input type="submit" value="Edit" id="pdfGeneration_{{ oa_id }}" hidden="hidden"/>
										<input id="remarks" class="form-control" enterkey="do_nothing" maxlength="248" placeholder="Approval/Rejection Remarks"/>
										<div class="remarks_count_link remarks_count disabled" onclick="remarksInSwal('oa_document_remarks');">
											<span class="remarks_counter">No</span><span> remarks</span>
										</div>
									</div>
									{% if access_level.approve %}
										<a role='button' id='approve_oa' class="btn btn-save">Approve</a>
										<a role='button' id='regenerate' class="btn btn-save" onclick="regenerate('regenerate');">Regenerate</a>
								    {% endif %}
								    {% if access_level.approve %}
										<a role='button' id='reject_oa' class="btn btn-danger for_oa_approve">Reject</a>
									{% else %}
									{% if access_level.edit %}
										<a role='button' id='reject_oa' class="btn btn-danger for_oa_edit">Discard</a>
									{% endif %}
									{% endif %}
									<div style="display: inline-block; margin-right: 30px; float: right;" >
										<a role="button" id='display_popup' onclick="openMailPopup()" class="btn transparent-btn" data-tooltip="tooltip" data-placement="left" title="Email OA"><i class="fa fa-envelope" style="font-size: 14px;" aria-hidden="true"></i></a>
									</div>
									<div style="display: inline-block; float: right;">
										<a href="/erp/sales/oa/view/" class="btn transparent-btn" data-tooltip="tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
									</div>
								</form>
							</div>

						</div>
					</div>
				</div>
				<input type="hidden" id="oa_document_remarks" />
				<div id="oa_document_container"></div>
      		</div>
			<div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
      	</div>
  	</div>
</div>
<div class="hide">
	<form id="oa_edit_form" method="post" action="/erp/sales/oa/editOA/">
		{%csrf_token%}
		<input type="hidden" id="edit_oa_id" class="oa_id_in_class" name="oa_no"  value=""/>
	</form>
</div>
<script>
	$(document).ready(function(){
		var project = JSON.parse(localStorage.getItem('project'));
    	if(project && project.type == 'Secondary'){
        	$('.page-title').text('Internal Work Order');
        	$('#for_sec_ent_no').text('IWO NO');
        	$('#for_sec_ent_date').text('IWO Date');
        	$('#for_sec_ent_val').text('IWO Value');
        	$('#for_sec_ent_invoice_val').text('Internal Invoice Value');
    	}
		TableHeaderFixed();
        $(".chosen-select").chosen();
        if($("#last_created_order_code").val()!="") {
        	if($("#last_created_order_code").val().indexOf("success") > 0) {
        		swaltype = "success"
        	}
        	else {
        		swaltype = "warning"	
        	}
			swal({
			  title: "",
			  text: $("#last_created_order_code").val(),
			  type: swaltype
			});
		}
		$("#last_created_order_code").val("");
		$("#id_search_submit").click(function(){
			$("#loading").show();
		});
		$("#oa_document_container").html(getPdfLoadingImage());
		updateDocumentExtension();
        $("#template_configuration").removeClass('hide');
        $("#template_configuration").click(function(){
            window.open('/erp/admin/oa_template','_blank');
        });
	});

	$(window).load(function(){
		updateFilterText();
	})
 	$('.nav-pills li').removeClass('active');
    $("#li_oa").addClass('active');
    $(".slide_container_part").removeClass('selected');
    $("#menu_sales").addClass('selected');

	function openMailPopup() {
		new Mailer().prepareEmailPopup().getSupplierMailID(id=$("#oa_id").val(), type= "oa").show();
		return false;
	}

	function viewAttachedDocumentByWindow(current){
		var attachment_data = $(current).attr('data-url');
		$('#attachmentPreview .modal-body').prepend('<span id="loading" class="text-center">Loading... Please wait</span>');
		$.ajax({
            url: "/erp/commons/json/get_gcs_attachmemt/",
            type: "POST",
            dataType: "json",
            data: {'attachment_data':attachment_data},
            success: function (json) {
		    var fileName = JSON.parse(attachment_data)['name'];
		    var extension = JSON.parse(attachment_data)['ext'];
		    var url = json;
		    $('#attachmentPreview .modal-body #loading').remove();
		    $("#attachmentPreviewSrc, #attachmentPreviewSrcPdf").removeAttr('src');
		    if (extension == "pdf") {
		        $("#attachmentPreviewSrc").addClass("hide");
		        $("#attachmentPreviewSrcPdf").attr('height', Number($( window ).height()-220));
		        $("#attachmentPreviewSrcPdf").attr('src', url).removeClass("hide");
		    } else {
		        $("#attachmentPreviewSrcPdf").addClass("hide");
		        $("#attachmentPreviewSrc").attr('src', url).removeClass("hide");
		    }
		    $(".attachment-description").text(fileName);
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });
        $("#attachmentPreview").modal('show');
	}

    function updateFilterText() {
		$(".filtered-date b").text($("#reportrange").find("span").text());
		$(".filtered-party b").text($("#id_party_name option:selected").text());
		$(".filtered-status b").text($("#id_status option:selected").text());
		if($("#id_exclude_value").val()==1)
		{
			$("#id_sales_status").attr("checked",true);
		}else{
			$("#id_sales_status").attr("checked",false);
		}
		$(".filtered-sales-status b").text(($("#id_sales_status").is(":checked")? "Included":"Excluded"));

	}

	function updateDocumentExtension(){
	    $(".document-viewer").each(function() {
	        var self = $(this);
	        filename = self.attr('data-url');
	        if (filename != null) {
	            var extension = filename.replace(/^.*\./, '').toLowerCase();
	            self.attr('data-extension', extension).addClass(extension);
	        }
	    });
	}

    var oTable;
	var oSettings;
	function TableHeaderFixed() {
		if($("#id_exclude_value").val()==1){
			columns = [
		        null,null,null,
		        { "type": "date" },
		        { "type": "date" },
		        null,null,null,null,null
		        ]
	    } else{
	        columns = [
		        null,null,null,
		        { "type": "date" },
		        { "type": "date" },
		        null,null,null
		        ]
	    }
	    oTable = $('#oa_list').DataTable({
	    fixedHeader: false,
        "scrollY": Number($(document).height() - 230),
        "scrollX": true,
	    "pageLength": 50,
	    "search": {
	        "smart": false
	    },
	    "columns": columns
	    });
	    updateOAListJson();
	    oTable.on("draw",function() {
	        var keyword = $('#oa_list_filter > label:eq(0) > input').val();
	        $('#oa_list').unmark();
	        $('#oa_list').mark(keyword,{});
	        updateOAListJson();
	        setHeightForTable();
	        listTableHoverIconsInit('oa_list');
	    });
	    oTable.on('page.dt', function() {
	      $('html, body').animate({
	        scrollTop: $(".dataTables_wrapper").offset().top - 15
	       }, 'slow');
	      listTableHoverIconsInit('oa_list');
	    });
	    oSettings = oTable.settings();
	    $( window ).resize();
	    listTableHoverIconsInit('oa_list');
	}

	function updateOAListJson(){
		setTimeout(function(){
	    	OAListjsonObj = [];
	    	var rows = oTable.rows( { page : 'current'} ).data();
	        for(var i=0;i<rows.length;i++) {
	        	var selectedRow = $(rows[i][1]);
	        	oa = {}
		        oa ["oaId"] = selectedRow.attr("data-orderAckId");
		        oa ["oaNumber"] = selectedRow.text().trim();
	            OAListjsonObj.push(oa);
	        }
			localStorage.setItem('oaListNav', JSON.stringify(OAListjsonObj));			
		},10);
	}

	function generate_pdf_ajax(oa_id, oa_status, document_regenerate=false) {
	    $("#oa_doc_btn a.btn, #remarks").addClass("hide");
	    $("#oa_id").val(oa_id);
	    $("#oa_document_modal").modal("show");
	    sessionStorage.oa_id = oa_id
        sessionStorage.oa_status = oa_status
	    $.ajax({
	        url: "/erp/sales/json/oa_doc/",
	        type: "post",
	        datatype: "json",
	        data: {oa_id:oa_id, response_data_type:"data", document_regenerate:document_regenerate},
	        success: function (response) {
	        	console.log('response', response)
	        	if(response.remarks != undefined) {
	                if(response.remarks.length > 0){
	                    $(".remarks_count_link").text(response.remarks.length+" remarks").removeClass('disabled')
	                }
	                else {
	                    $(".remarks_count_link").text("No remarks").addClass('disabled')  
	                }
	                $("#oa_document_remarks").val(JSON.stringify(response.remarks));
	            }
	            $("#modal_oa_id").val(oa_id);
	            var row = '<object id="oa_document" class="document_viewer" data="'+response.url+'">'+getPdfErrorMessage()+'</object>';
            	$("#oa_document_container").html(row);
	            $("#display_popup").removeClass('hide')
	            if(oa_status == -1) {
	                if($("#reject_oa").hasClass("for_oa_approve")) {
	                    $("#approve_oa, #remarks").removeClass("hide");
	                }
	            }
	            else if(oa_status == 0) {
	                if($("#reject_oa").hasClass("for_oa_approve")) {
	                    $("#approve_oa, #remarks, #reject_oa").removeClass("hide");
	                }
	                else if($("#reject_oa").hasClass("for_oa_edit")){
	                    $("#remarks, #reject_oa").removeClass("hide");
	                }
	                $("#reject_oa").text("Discard");
	            }
	            else if(oa_status == 1 ) {
	                if($("#reject_oa").hasClass("for_oa_approve")) {
	                    $("#remarks, #reject_oa, #regenerate").removeClass("hide");
	                    $("#reject_oa").text("Reject");
	                }
	            }
	            if($("#remarks").hasClass("hide") || $("#remarks").length <= 0) {
	                $(".remarks_count_link").css({float: "left"});
	            }
	            else {
	                $(".remarks_count_link").css({float: "right"});
	            }
	            $("#remarks").val("");
	        },
	       error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
	    });
	    $("#oa_document_modal footer").remove();
	    $('#oa_document_modal').on('hidden.bs.modal', function () {
	        $(".remarks_count_link").css({float: "right"});
	        $("#oa_document").remove();
	        $("#oa_document_container").html(getPdfLoadingImage());
	    });
	}

	$("#approve_oa").click(function () {
        oa_id = $("#oa_id").val() ;
        approved_remarks= $("#remarks").val();
        date = new Date();
        $("#loading").show();
        $.ajax({
            url: "/erp/sales/oa/approve/",
            type: "POST",
            dataType: "json",
            data: {oa_id: oa_id, approved_remarks:approved_remarks},
            success: function (json) {
            	$("#loading").hide();
            	var swalType = "warning";
            	if(json.response_message == "Success") {
	            	swal({
		                title: "<span style='color: #44ad6b;'>Approved Successfully</span>",
		                text: 'OA: <b>' + json.code + '</b> has been Approved Successfully',
		                type: 'success',
	  		            allowEscapeKey: false
		            },
		            function(){
	                    $('#oa_id').val(oa_id);
	                    generate_pdf_ajax(oa_id, 1);
	                    updateTableStatus(1, oa_id, json.code);
	            		//$('#pdfGeneration_' + oa_id).click();
	                });
	                ga('send', 'event', 'Order Acknowledgement', 'Approve', $('#enterprise_label').val(), 1);
	            }
	            else {
	            	swal({
		                title: "<span style='color: #44ad6b;'>Approval Failed</span>",
		                text: 'OA Draft: <b>' + oa_id + '</b> approval has been failed',
		                type: 'error',
	  		            allowEscapeKey: false
		            });
	            }
            },
            error: function (xhr, errmsg, err) {
            	$("#loading").hide();
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });
        return false;
    });

    $("#reject_oa").click(function () {
    var project = JSON.parse(localStorage.getItem('project'));
        if($('#remarks').val() != ""){
	        oa_id = $("#oa_id").val();
	        rejection_remarks = $("#remarks").val()
	        date = new Date();
	        $("#loading").show();
	        $.ajax({
				url: "/erp/sales/json/oa/checkoainvoice_qty/",
				type: "post",
				datatype:"json",
				data: {oa_id: oa_id},
				success: function(response){
					$("#loading").hide();
					if(response.response_message != 'Success') {
				        swal("Unable to Reject!", "Order cannot be Rejected, as Items have been Issued/Received against this Order!", "warning");
				        return false;
				    }else {
				    	$("#loading").show();
				        $.ajax({
				            url: "/erp/sales/oa/reject/",
				            type: "POST",
				            dataType: "json",
				            data: {oa_id:oa_id, remarks:rejection_remarks},
				            success: function (json) {
				            	$("#loading").hide();
				                if (json == '0'){
				                	swal({
						                title: "Successfully Removed",
						                text: "OA Draft No: <b>" + oa_id + "</b> has been permanently removed.",
						                type: "success",
					  		            allowEscapeKey: false
						            },
						            function(){
					                    window.location.assign('/erp/sales/oa/view/');
					                });
	                                ga('send', 'event', 'Order Acknowledgement', 'Discard', $('#enterprise_label').val(), 1);
				                } 
				                else {
				                	swal({
						                title: "Successfully Rejected",
						                text: "OA No: <b>" + json + "</b> has been successfully rejected!",
						                type: "success",
					  		            allowEscapeKey: false
						            },
						            function(){
					                    $('#oa_id').val(oa_id);
					                    generate_pdf_ajax(oa_id, -1)
					                    updateTableStatus(-1, oa_id, 0)
				                    	//$('#pdfGeneration_' + oa_id).click();
					                });
	                                ga('send', 'event', 'Order Acknowledgement', 'Reject', $('#enterprise_label').val(), 1);
				                }
				            },
				            error: function (xhr, errmsg, err) {
				            	$("#loading").hide();
				                console.log(xhr.status + ": " + xhr.responseText);
				            }
				        });
				        return false;
					}
				},
				error: function(){
					$("#loading").hide();
				}
			});
        }
        else {
        	errMsg="Please enter your purpose of rejecting this Order Acknowledgement as remarks."
    		if(project && project.type == 'Secondary'){
    			errMsg="Please enter your purpose of rejecting this Internal Work Order as remarks."
    			}
        	swal({
                title: "<span style='color: #dd4b39;'>Remarks Required.</span>",
                text: errMsg,
                type: "warning"
            },
            function(){
            	$('#remarks').focus();	
            });
            return;
        }
    });

    function updateTableStatus(status, oa_id, oa_no){
	    var editedRow = $("#oa_list").find("tr[data-oaid='"+oa_id+"']");
	    if(status == 1) {
	        editedRow.find("td.td_status").find("a").removeClass('pending cancelled').addClass('approved').text("Approved");
	        editedRow.find(".edit_link_code").text(oa_no);
	        editedRow.find(".td-oa-date").text(moment().format("MMM DD, YYYY HH:mm:ss"))
	    }
	    if(status == -1) {
	        editedRow.find("td.td_status").find("a").removeClass('approved').addClass('cancelled').text("Cancelled");
	    }
	    editedRow.find(".pdf_genereate").attr("onclick", "generate_pdf_ajax("+oa_id+", "+status+")");
	}

	function editOaRow(oaId, openTarget="") {
		$("#edit_oa_id").val(oaId);
		$("#oa_edit_form").attr("target", openTarget).submit();
	}

	function regenerate(action){
	console.log('regenerate')
	if(action == 'regenerate'){
	console.log('if')
        generate_pdf_ajax(sessionStorage.oa_id, sessionStorage.oa_status, true);
    }

	}
</script>
{% endblock %}
