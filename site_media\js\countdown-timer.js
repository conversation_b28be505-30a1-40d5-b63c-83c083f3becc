(function ( $ ) {
	function pad(n) {
		if(n<0) {
			 n = n * -1; 
		}
	    return (n < 10) ? ("0" + n) : n;
	}

	$.fn.showclock = function(time) {
	    var currentDate=new Date(time);
     	var dateTimeField=$(this).data('date').split(' ');
	    var fieldDate=dateTimeField[0].split('-');
	    var fieldTime=[0,0];
   		fieldTime=dateTimeField[1].split(':');
	    var futureDate=new Date(fieldDate[0],fieldDate[1]-1,fieldDate[2],fieldTime[0],fieldTime[1]);
	    var seconds=futureDate.getTime() / 1000 - currentDate.getTime() / 1000;
	    if(seconds == 0 || isNaN(seconds)){
	    	this.hide();
	    	//return this;
	    }
	    else {
	    	this.show();
	    }

	    var days=Math.floor(seconds/86400);
	    seconds=seconds%86400;
	    
	    var hours=Math.floor(seconds/3600);
	    seconds=seconds%3600;

	    var minutes=Math.floor(seconds/60);
	    seconds=Math.floor(seconds%60);

	    if(days<=0 && hours<=0 && minutes<=0 && seconds<=0){
	    	days = Number(days+1)
	    	hours = Number(hours+1)
	    	minutes = Number(minutes+1)
	    	seconds = Number(seconds)
	    	$(".after-expired-msg").text('Your Subscription expired since');
	    	$(".expired-before-txt").text('before');
	    	$(".subscription-bar").addClass('expired')
	    }
	    
	    var html="";

	    if(days!=0){
		    html+="<div class='countdown-container days'>"
		    html+="<span class='countdown-value days-bottom'>"+pad(days)+ " <span class='count-down-heading'>Days</span> </span>";
		    html+="</div>";
		}

	    html+="<div class='countdown-container hours'>"
	    	html+="<span class='countdown-value hours-bottom'>"+pad(hours)+ " <span class='count-down-heading'>Hrs</span> :</span>";
	    html+="</div>";

	    html+="<div class='countdown-container minutes'>"
	    	html+="<span class='countdown-value minutes-bottom'>"+pad(minutes)+ " <span class='count-down-heading'>Mins</span> :</span>";
	    html+="</div>";

	    html+="<div class='countdown-container seconds'>"
	    	html+="<span class='countdown-value seconds-bottom'>"+pad(seconds)+ " <span class='count-down-heading'>Secs</span></span>";
	    html+="</div>";

	    this.html(html);
	};

	$.fn.countdown = function() {
		var el=$(this);
		var curTime = $("#current_server_time").val()
		el.showclock(curTime);
		var counter=0;
		setInterval(function(){
			el.showclock(moment(curTime).add(counter++, 'seconds').format("YYYY-MM-DD HH:mm:ss"));	
		},1000);
		
	}

}(jQuery));

jQuery(document).ready(function(){
	if(jQuery(".countdown").length>0){
		jQuery(".countdown").each(function(){
			jQuery(this).countdown();	
		})
		
	}
})