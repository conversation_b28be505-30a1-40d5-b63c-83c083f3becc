import ast
import collections
import datetime
import json
import re

import pymysql
from django.http import HttpResponse
from django.template.response import TemplateResponse

from erp.auth import SESSION_KEY, ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.dao import executeQuery
from erp.properties import TEMPLATE_TITLE_KEY
from erp.reports import logger
from erp.reports.backend import CustomReportDAO
from util.custom_report_properties import PurchaseReportProperties, SalesReportProperties
from util.reports_query_builder import ReportsQueryBuilder

conv = pymysql.converters.conversions.copy()
conv[pymysql.constants.FIELD_TYPE.DECIMAL] = str
conv[pymysql.constants.FIELD_TYPE.NEWDECIMAL] = str
conv[pymysql.constants.FIELD_TYPE.DATETIME] = str


def renderCustomSalesReport(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	report_list = CustomReportDAO().getReportList(enterprise_id=enterprise_id, user_id=user_id, report='sales')
	return TemplateResponse(
		template='reports/sales.html', context={TEMPLATE_TITLE_KEY: "Custom Sales Report", 'report_list': report_list},
		request=request)


def renderCustomPurchaseReport(request):
	"""

	:param request:
	:return:
	"""

	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	report_list = CustomReportDAO().getReportList(enterprise_id=enterprise_id, user_id=user_id, report='purchase')
	return TemplateResponse(
		template='reports/purchase.html', context={TEMPLATE_TITLE_KEY: "Custom Purchase Report", 'report_list': report_list},
		request=request)


def groupby_generate(fields_approved, prime_fields, type):
	"""
	TODO: groupby need to be extended because if product name comes we should be add make_id by default for grouping (need to confim it) so dont delete this this method
	:param fields_approved:
	:param prime_fields:
	:return:
	"""
	response = [f for f in prime_fields if f in fields_approved]
	if type == 'date':
		response.append('DAY(invoice_approved_on)')
	elif type == 'month':
		response.append('MONTH(invoice_approved_on)')
	elif type == 'year':
		response.append('YEAR(invoice_approved_on)')
	return response


def orderby_generate(prime_fields):
	"""

	:param prime_fields:
	:return:
	"""
	response = []
	if "invoice_approved_on" in prime_fields:
		response.append(tuple(('invoice_approved_on', 'desc')))
	if "materials_name" in prime_fields:
		response.append(tuple(('materials_name', 'asc')))
	if "make_name" in prime_fields:
		response.append(tuple(('make_name', 'asc')))
	if "invoice_materials_is_faulty" in prime_fields:
		response.append(tuple(('invoice_materials_is_faulty', 'asc')))

	return response


def saveReportTemplate(request):
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	try:
		report_name = request.POST['name'].encode('utf-8')
		group_id = request.POST['group'].encode('utf-8')
		report_code = request.POST['report'].encode('utf-8')
		adv_filter = request.POST['adv_filter'].encode('utf-8')
		report_values = [item.encode('utf-8') for item in request.POST.getlist('values[]')]
		values = ",".join(report_values)
		code = report_name.replace(" ", "_")
		response = CustomReportDAO().insertReport(
			report_code=report_code, report_name=report_name, code=code, values=values,
			enterprise_id=enterprise_id, user_id=user_id, group_id=group_id, adv_filter=adv_filter)
	except Exception as e:
		logger.exception("Failed to save the report. %s" % e.message)
		response = {'error': "%s" % e.message}
	return HttpResponse(content=response, mimetype='application/json')


def deleteReportTemplate(request):
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	code = request.POST['code'].encode('utf-8')
	report_code = request.POST['report_code'].encode('utf-8')
	response = CustomReportDAO().deleteReport(
		report_code=report_code, enterprise_id=enterprise_id, user_id=user_id, code=code)
	return HttpResponse(content=response, mimetype='application/json')


def loadReportTemplate(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	code = request.POST['code'].encode('utf-8')
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	response = CustomReportDAO().getReport(code=code, enterprise_id=str(enterprise_id), user_id=user_id)
	return HttpResponse(content=json.dumps(response), mimetype='application/json')


def isValidDate(date):
	"""
	function defined to check given date fields is valid
	:param date:
	:return:
	"""
	try:
		datetime.datetime.strptime(date, '%Y-%m-%d')
		return True
	except ValueError:
		return False


def isValidString(string):
	"""

	:param string:
	:return:
	"""
	try:
		return re.search('^[A-Za-z_][A-Za-z0-9_]*', string)
	except ValueError:
		return False


def generateCustomSalesReport(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	grouping = []
	where_ext = ''
	try:
		# ast.literal_eval used to remove u' from list
		fields = ast.literal_eval(json.dumps(request.POST.getlist('fields[]')))
		adv_filter = json.loads(request.POST.get('adv_filter')) if request.POST.get('adv_filter') != '' else None
		from_date, to_date = (
			request.POST['from'].encode('utf-8') + ' 00:00:00', request.POST['to'].encode('utf-8') + ' 23:59:00')\
			if request.POST['from'].encode('utf-8') != '' else (None, None)
		# Prime Querying table values
		base_table = 'invoice'

		# order_by = [('invoice_approved_on', 'desc')]
		where = {'invoice.enterprise_id': enterprise_id, 'invoice.type': ('GST', 'Trading', 'Service', 'BoS', 'Excise'),
			'invoice.status': 1}

		if from_date and len(from_date.strip()) > 0:
			where['invoice.approved_on'] = {'from': from_date, 'to': to_date}

		limit = None
		order_by = orderby_generate(prime_fields=fields)
		join = SalesReportProperties().constructSalesQueryJoins(fields=fields)
		primary_fields, where_fields, having_fields = SalesReportProperties().salesreport_get_select(fields=fields)

		if adv_filter is not None:
			for filterItem in adv_filter:
				# Date field consolidation
				if filterItem['conditionName'] == 'date':
					grouping.append('DATE(%s)' % SalesReportProperties.selector_field_calc[filterItem['fieldName']])
				if filterItem['conditionName'] == 'month':
					grouping.append('MONTH(%s)' % SalesReportProperties.selector_field_calc[filterItem['fieldName']])
				if filterItem['conditionName'] == 'year':
					grouping.append('YEAR(%s)' % SalesReportProperties.selector_field_calc[filterItem['fieldName']])
				# other fields
				if where_fields[fields.index(filterItem['fieldName'])] != '':
					if filterItem['conditionName'] == 'begins_with':
						where_ext += """ AND TRIM({0}) LIKE '{1}%%' """.format(where_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0])
					if filterItem['conditionName'] == 'equal_to':
						where_ext += ' AND DATE({0}) = \'{1}\''.format(where_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0]) if isValidDate(filterItem['valueName'][0]) else ' AND TRIM({0}) = \'{1}\''.format(where_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0])
					if filterItem['conditionName'] == 'equal_to_case':
						where_ext += ' AND BINARY TRIM({0}) = "{1}"'.format(where_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0])
					if filterItem['conditionName'] == 'contains':
						where_ext += """ AND TRIM({0}) LIKE '%%{1}%%'""".format(where_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0])
					if filterItem['conditionName'] == 'ends_with':
						where_ext += """AND TRIM({0}) LIKE '%%{1}'""".format(where_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0])
					# date or number
					if filterItem['conditionName'] == 'between':
						where_ext += ' AND DATE({0}) BETWEEN \'{1}\' AND \'{2}\''.format(where_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0], filterItem['valueName'][1]) if isValidDate(filterItem['valueName'][0]) else ' AND {0} BETWEEN \'{1}\' AND \'{2}\''.format(where_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0], filterItem['valueName'][1])
					if filterItem['conditionName'] == 'after':
						where_ext += ' AND DATE({0}) > \'{1}\''.format(where_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0]) if isValidDate(filterItem['valueName'][0]) else ' AND {0} > \'{1}\''.format(where_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0])
					if filterItem['conditionName'] == 'before':
						where_ext += ' AND DATE({0}) < \'{1}\''.format(where_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0]) if isValidDate(filterItem['valueName'][0]) else ' AND {0} < \'{1}\''.format(where_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0])
					if filterItem['conditionName'] == 'greater_than':
						where_ext += ' AND {0} > {1}'.format(where_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0])
					if filterItem['conditionName'] == 'lesser_than':
						where_ext += ' AND {0} < {1}'.format(where_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0])
					if filterItem['conditionName'] == 'greater_than_equal':
						where_ext += ' AND {0} >= {1}'.format(where_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0])
					if filterItem['conditionName'] == 'lesser_than_equal':
						where_ext += ' AND {0} <= {1}'.format(where_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0])

		having = []
		if adv_filter is not None:
			for filterItem in adv_filter:
				if having_fields[fields.index(filterItem['fieldName'])] != '':
					if filterItem['conditionName'] == 'begins_with':
						having.append(""" {0} LIKE '{1}%%'""".format(having_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0]))
					if filterItem['conditionName'] == 'equal_to':
						having.append(' {0} = "{1}"'.format(having_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0]))
					if filterItem['conditionName'] == 'equal_to_case':
						having.append(' BINARY {0} = "{1}"'.format(having_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0]))
					if filterItem['conditionName'] == 'contains':
						having.append(""" {0} LIKE '%%{1}%%'""".format(having_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0]))
					if filterItem['conditionName'] == 'ends_with':
						having.append(""" {0} LIKE '%%{1}'""".format(having_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0]))
					# date or number
					if filterItem['conditionName'] == 'between':
						having.append(' {0} BETWEEN \'{1}\' AND \'{2}\''.format(having_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0], filterItem['valueName'][1]))
					if filterItem['conditionName'] == 'after':
						having.append(' {0} > {1}'.format(having_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0]))
					if filterItem['conditionName'] == 'before':
						having.append(' {0} < {1}'.format(having_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0]))
					if filterItem['conditionName'] == 'greater_than':
						having.append(' {0} > {1}'.format(having_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0]))
					if filterItem['conditionName'] == 'lesser_than':
						having.append(' {0} < {1}'.format(having_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0]))
					if filterItem['conditionName'] == 'greater_than_equal':
						having.append(' {0} >= {1}'.format(having_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0]))
					if filterItem['conditionName'] == 'lesser_than_equal':
						having.append(' {0} <= {1}'.format(having_fields[fields.index(filterItem['fieldName'])], filterItem['valueName'][0]))

		for field in fields:
			for val in SalesReportProperties.gfields[field]:
				if val not in grouping:
					grouping.append(val)
		query_builder = ReportsQueryBuilder()
		result_query = query_builder.buildQuery(fields=primary_fields, table=base_table, join=join, where=where,
		                                        where_ext=where_ext, groupby=grouping, having=having, order=order_by, limit=limit)
		where2 = where.copy()
		for idx, val in where.items():
			if isinstance(val, dict):
				where2['from'] = where[idx]['from']
				where2['to'] = where[idx]['to']
				del where2[idx]
		response = executeQuery(query=result_query, query_data=where2, as_dict=True, conversions=conv)
	except Exception as e:
		logger.exception("Failed constructing Sales Custom Reports. %s" % e.message)
		response = {'error': "%s" % e.message}
	return HttpResponse(content=json.dumps(response), mimetype='application/json')


def constructPOQueryJoins(fields=None, grn_rej_status=None):
	"""

	:param fields:
	:param grn_rej_status:
	:return:
	"""
	joins = collections.OrderedDict()
	join_field_constraint = PurchaseReportProperties().getJoinConstraintFields(fields=fields)
	logger.info('%s' % join_field_constraint)
	if 'indents' in join_field_constraint:
		joins['indents'] = ('LEFT', 'indents.indent_no', """purchase_order.indent_no AND indents.enterprise_id 
		= purchase_order.enterprise_id""")
	if 'purchase_order_material' in join_field_constraint:
		joins["po_material_received_abstract AS purchase_order_material"] = (
			'LEFT', 'purchase_order_material.po_id', """purchase_order.id  AND purchase_order_material.enterprise_id
			= purchase_order.enterprise_id""")
	if 'po_delivery' in join_field_constraint:
		joins["""purchase_order_material_delivery_schedules AS po_delivery_schedule"""] = (
			'LEFT', 'po_delivery_schedule.item_id', """
					purchase_order_material.item_id AND po_delivery_schedule.po_id =  purchase_order_material.po_id 
					%s
					AND po_delivery_schedule.enterprise_id = purchase_order_material.enterprise_id"""
			% ('AND po_delivery_schedule.make_id = purchase_order_material.make_id' if any(
				elem in fields for elem in ['material_make']) else ''))
	if 'indent_material' in join_field_constraint:
		joins["""(SELECT indent_no, item_id, '' AS description, request_qty, make_id, enterprise_id FROM 
		indent_material) AS indent_material"""] = (
			'LEFT', 'indent_material.indent_no', """indents.indent_no  AND ((indent_material.item_id = purchase_order_material.item_id) OR (
			indent_material.description = purchase_order_material.item_name AND indent_material.item_id = '' 
			AND purchase_order_material.drawing_no = NULL)) AND indent_material.enterprise_id = indents.enterprise_id""")
	if 'grn_material' in join_field_constraint:
		joins["""grn_material_received_abstract AS grn_material"""] = (
			'LEFT', 'grn_material.po_no', """
			purchase_order.id AND (grn_material.item_id = purchase_order_material.item_id %s) 			 
			AND grn_material.enterprise_id = purchase_order_material.enterprise_id AND grn_material.dc_id IS NULL AND 
			grn_material.rec_grn_id is NULL"""
			% ('AND grn_material.make_id = purchase_order_material.make_id' if any(elem in fields for elem in ['material_make']) else ''))
	if 'grn' in join_field_constraint:
		joins['grn'] = ('LEFT', 'grn.grn_no', 'grn_material.grn_no AND grn_material.enterprise_id = grn.enterprise_id %s' % (' AND grn.status != -1' if grn_rej_status is True else ''))
	if 'materials' in join_field_constraint:
		joins['materials'] = (
			'LEFT', 'materials.id',
			'purchase_order_material.item_id AND materials.enterprise_id = purchase_order.enterprise_id')
	if 'material_make_map' in join_field_constraint:
		joins["material_make_map"] = (
				'LEFT', 'material_make_map.item_id',
				"""purchase_order_material.item_id AND material_make_map.make_id = purchase_order_material.make_id 
				AND material_make_map.enterprise_id = purchase_order_material.enterprise_id""")
	if 'make' in join_field_constraint:
		joins["make"] = (
			'LEFT', 'make.id',
			'material_make_map.make_id AND make.enterprise_id = material_make_map.enterprise_id')
	if 'unit_master' in join_field_constraint:
		joins['unit_master'] = (
			'LEFT', 'unit_master.enterprise_id', """purchase_order.enterprise_id AND CASE 
			WHEN purchase_order_material.unit <> '' THEN unit_master.unit_id = purchase_order_material.unit 
			ELSE unit_master.unit_id = materials.unit END""")
	if 'billing' in join_field_constraint:
		joins['ledger_bills'] = (
			'LEFT', 'ledger_bills.id', 'grn.ledger_bill_id AND ledger_bills.enterprise_id = grn.enterprise_id')
	if 'projects' in join_field_constraint:
		joins['projects'] = (
			'INNER', 'projects.id', 'purchase_order.project_code AND projects.enterprise_id = purchase_order.enterprise_id')
	if 'currency' in join_field_constraint:
		joins['currency'] = ('INNER', 'currency.id', 'purchase_order.currency_code')
	if 'party_master' in join_field_constraint:
		joins['party_master'] = (
			'INNER', 'party_master.party_id',
			'purchase_order.supplier_id  AND party_master.enterprise_id = purchase_order.enterprise_id')
	if 'enterprise' in join_field_constraint:
		joins['enterprise'] = ('INNER', 'enterprise.id', 'purchase_order.enterprise_id')
	return joins


def baseWhereClauseBuilder(enterprise_id=None, fields=None, from_date=None, to_date=None):
	"""
	Create where clause fields for filter query and here the return response_arg => where clause fields and response_val => field data
	ex. WHERE %enterprise(response_arg) = %s(response_val)
	:param enterprise:
	:param fields:
	:param from_date:
	:param to_date:
	:return: dict, dict
	"""
	response_arg, response_val = dict(), dict()
	if from_date != '' and to_date != '':
		response_arg['purchase_order.drafted_on'] = {'from': from_date, 'to': to_date}
		response_val['from'], response_val['to'] = from_date + ' 00:00:00', to_date + ' 23:59:59'
	if enterprise_id:
		response_arg['purchase_order.enterprise_id'], response_val['purchase_order.enterprise_id'] = enterprise_id, enterprise_id
	if not any([key in fields and 'purchase_order.id' in elem for key, elem in PurchaseReportProperties.grouping_fields.items()]):
		response_arg['purchase_order.status'] = 3 # dummy will be replaced in where clause
	if any(elem in fields for elem in PurchaseReportProperties.join_constraint['grn']) or any(elem in fields for elem in PurchaseReportProperties.join_constraint['grn_material']):
		response_arg['grn.status'], response_val['grn.status'] = (0, 1, 2, 3, 4, 5), (0, 1, 2, 3, 4, 5)
	return response_arg, response_val


def groupingAndConstraintBuilder(filter_data=None, field_consolidation_data=None, fields=None):
	"""
	This function helps to construct grouping and where clause module and returns all the constraints have been executed
	in the query
	:param filter_data:
	:param field_consolidation_data:
	:param fields:
	:return: list, list, string
	"""
	grouping = []
	having = []
	where_extended = ''
	grouping_constraints = PurchaseReportProperties().grouping_fields
	consolidation_type = PurchaseReportProperties().type_of_consolidation

	try:
		if filter_data:
			date_consolidation_data = {
				'date': 'DATE(%s), MONTH(%s), YEAR(%s)', 'month': 'MONTH(%s), YEAR(%s)', 'year': 'YEAR(%s)'}
			data_datetype_constraints = {
				'equal_to': [""" AND DATE({0}) = '{1}'""", """ DATE({0}) = '{1}'"""],
				'between': [""" AND DATE({0}) BETWEEN '{1}' AND '{2}'""", """ DATE({0}) BETWEEN '{1}' AND '{2}'"""],
				'after': [""" AND DATE({0}) > '{1}'""", """ DATE({0}) > '{1}'"""],
				'before': [""" AND DATE({0}) < '{1}'""", """ DATE({0}) < '{1}'"""]
			}
			data_stringtype_constraints = {
				'begins_with': [""" AND TRIM({0}) LIKE '{1}%%' """, """ TRIM({0}) LIKE '{1}%%' """],
				'equal_to': [""" AND TRIM({0}) = '{1}'""", """ TRIM({0}) = '{1}'"""],
				'equal_to_case': [""" AND BINARY TRIM({0}) = '{1}'""", """ BINARY TRIM({0}) = '{1}'"""],
				'contains': [""" AND TRIM({0}) LIKE '%%{1}%%'""", """ TRIM({0}) LIKE '%%{1}%%'"""],
				'ends_with': [""" AND TRIM({0}) LIKE '%%{1}'""", """ TRIM({0}) LIKE '%%{1}'"""]
			}
			data_integertype_constraints = {
				'equal_to': [""" AND {0} = {1}""", """ {0} = {1}"""],
				'between': [""" AND {0} BETWEEN {1} AND {2}""", """ {0} BETWEEN {1} AND {2}"""],
				'greater_than': [""" AND {0} > {1}""", """ {0} > {1}"""],
				'lesser_than': [""" AND {0} < {1}""", """ {0} < {1}"""],
				'greater_than_equal': [""" AND {0} >= {1}""", """ {0} >= {1}"""],
				'lesser_than_equal': [""" AND {0} <= {1}""", """ {0} <= {1}"""]
			}

			for item in filter_data:
				if item['conditionName'] not in date_consolidation_data:
					data_constraints = data_datetype_constraints if item['fieldName'] in consolidation_type['date'] \
						else data_stringtype_constraints if item['fieldName'] in consolidation_type['string'] \
						else data_integertype_constraints
					if field_consolidation_data[item['fieldName']]['where_clause'] != '':
						if str(item['conditionName']) == 'between':
							where_extended = ' '.join([data_constraints[item['conditionName']][0].format(field_consolidation_data[item['fieldName']]['where_clause'], item['valueName'][0], item['valueName'][1])])
						else:
							where_extended = ' '.join([data_constraints[item['conditionName']][0].format(field_consolidation_data[item['fieldName']]['where_clause'], item['valueName'][0])])
					if field_consolidation_data[item['fieldName']]['having_clause'] != '':
						if str(item['conditionName']) == 'between':
							having.append(data_constraints[item['conditionName']][1].format(field_consolidation_data[item['fieldName']]['having_clause'], item['valueName'][0], item['valueName'][1]))
						else:
							having.append(data_constraints[item['conditionName']][1].format(field_consolidation_data[item['fieldName']]['having_clause'], item['valueName'][0]))
				else:
					if str(item['conditionName']) == 'date':
						grouping.append(date_consolidation_data[item['conditionName']] % (item['fieldName'], item['fieldName'], item['fieldName']))
					if str(item['conditionName']) == 'month':
						grouping.append(date_consolidation_data[item['conditionName']] % (item['fieldName'], item['fieldName']))
					if str(item['conditionName']) == 'year':
						grouping.append(date_consolidation_data[item['conditionName']] % (item['fieldName']))

	except Exception as e:
		logger.exception("Failed to constructing po reports constraints and process continue with normal field grouping. %s" % e.message)
		logger.info({'error': "%s" % e.message})
	finally:
		# Assign grouping for fields
		[[grouping.append(grouping_list) for grouping_list in grouping_constraints[field]
			if grouping_list not in grouping] for field in fields if field in grouping_constraints]
		logger.info('Field level grouping assigned.')

	return grouping, having, where_extended


def generateCustomPurchaseReport(request):
	"""
	This is the main function for the PO custom reports module and it returns the final report value as list
	:param request:
	:return list:
	"""
	# Variable declarations
	enterprise_id = RequestHandler(request).getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	fields = request.POST.getlist('fields[]')
	limit = request.POST.getlist('limit[]')
	adv_filter = request.POST.get('adv_filter')
	from_date, to_date = str(request.POST['from']), str(request.POST['to'])
	field_consolidation_data = PurchaseReportProperties().column_calc_data

	where_clause_arg, where_clause_val = baseWhereClauseBuilder(
		enterprise_id=enterprise_id, fields=fields, from_date=from_date, to_date=to_date)

	# This is used to implode or explode the join condition for getting the rejected grn
	grn_rej_status = not any([
		key in fields and 'grn.grn_no' in elem for key, elem in PurchaseReportProperties().grouping_fields.items()])
	# To strike through rejected GRN
	fields.append('status')
	fields_consolidated_data = [field_consolidation_data[item]['column_data'] for item in fields]
	try:
		# Construct grouping and constraints
		grouping, having, where_extended = groupingAndConstraintBuilder(
			filter_data=(json.loads(adv_filter) if adv_filter != '' else None),
			field_consolidation_data=field_consolidation_data, fields=fields)

		# Build joins for PO
		joins = constructPOQueryJoins(fields=fields, grn_rej_status=grn_rej_status)
		# joins = constructPurchaseQueryJoins(fields=fields, grn_rej_status=grn_rej_status)

		# finally construct and execute the query
		logger.info('%s' % where_clause_val)
		response = executeQuery(query=ReportsQueryBuilder().buildQuery(
			fields=fields_consolidated_data, table='purchase_order', where=where_clause_arg, where_ext=where_extended, having=having,
			join=joins, groupby=grouping, limit=limit), query_data=where_clause_val, as_dict=True, conversions=conv)
	except Exception as e:
		logger.exception("Failed constructing Purchase Custom Reports. %s" % e.message)
		response = {'error': "%s" % e.message}
	return HttpResponse(content=json.dumps(response), mimetype='application/json')
