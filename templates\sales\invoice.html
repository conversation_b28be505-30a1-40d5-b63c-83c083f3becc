{% extends "sales/sidebar.html" %}
{% block sales %}
<style xmlns="http://www.w3.org/1999/html">
	li.sales_side_menu a{
		outline: none;
		background-color: #484848 !important;
	}
	li.sales_side_menu {
		margin-bottom: 5px;
	}
	#cattable {
		width: 550px;
		position: absolute;
		color: #000000;
		background-color: #FFFFFF;
		top: 50%;
		left: 50%;
		margin-top: 100px;
		margin-left: 100px;
	}

	#cattable_2 select{
		padding: 0 1px;
		font-size: 12px;
		max-width: 100px;
		display: inline-block;
	}

	#cattable_2 td {
		padding: 8px 4px;
	}
	#cattable_2 tr[data-toggle='open'] td:nth-child(13){
		opacity: 0.5;
		background: #ddd;
		pointer-events: none;
	}

	#cattable_2 tr[data-toggle='open'] input,
	#cattable_2 tr[data-toggle='open'] select {
	    opacity: 0.5;
	    background: #ddd;
	}

	.tr-alert-danger , .tr-alert-danger input, .tr-alert-danger select {
		color: #a94442 !important;
		background-color: #f2dede !important;
	}

	.tr-alert-danger td {
		border: solid 1px #aaa !important;
	}

	.empty-error-border,
	.error-border-stock,
	.error-border-po {
	    border: 1px solid #dd4b39 !important;
	    background: #fdeded;
	}

	#statusrow .selected label{
		color: #209be1;
	}

	.loading-main-container {
		display: block;
	}

	.enable_edit_option {
	    color: #004195;
	    margin-top: 45px;
	    position: absolute;
	    right: 7px;
	    z-index: 1000;
	    cursor: pointer;
	    float: right;
	    background: transparent;
	}

	.total_tax_label {
	    font-size: 12px;
	    color: #838383;
	    margin-top: 4px;
	    display: inline-block;
	}

	#id_invoice-deliver_to {
		height: 65px;
	}

	#id_invoice_mat-__prefix__-amount {
		font-size: 20px;
		padding: 0;
	}

	.item_particulars_add {
		position: absolute;
	    right: 22px;
	    text-align: right;
	    bottom: 39px;
	}

	.cke_textarea_inline {
	    pointer-events: none;
	    border: dashed 1px #999;
	    border-radius: 3px;
	    padding: 6px;
	}

	.cke_combopanel {
		z-index: 10100 !important;
	}

	#invoice_materials_table .s_no input {
		border:none;
		box-shadow: 0 0;
		background: transparent;
	}

	.remove-cursorEvent {
		pointer-events: none;
	}

	#div_dc_no_display .multiselect {
		width: 50%;
	}

	#id_invoice_fin_year ~ div .multiselect {
		border-radius: 4px 0 0 4px;
	}

	#id_invoice-dc_no_display ~ div .multiselect {
		border-radius: 0 4px 4px 0;
	}

	.remove-cursorEvent {
		pointer-events: none;
	}

	.custom-table td.consolidated_price_column {
		vertical-align: top !important;
	}

	.consolidated_row td.consolidated-link {
		cursor: pointer;
		color: #209be1 !important;
	}

	.consolidated_row .consolidated_material_name {
	    display: inline-block;
	    float: left;
	    width: 80%;
	    margin-left: 10px;
	    margin-top: -3px;
	}

	.consolidated_row .consolidated_material_name:hover,
	.consolidated_row_dc .consolidated_material_name:hover	{
		text-decoration: underline;
	}
	.side-content-datepicker .custom-error-message {
		position: relative;
	}

	.side-content-datepicker .error-border.form-control {
		background: #fdeded;
	}

	select[multiple], 
	select[size] {
		max-height: 34px;
	}

	.sales-receipt-arrow_box:before {
		left: 218px;
	}

	.sales-receipt-arrow_box:after {
		left: 219px;
	}

	#id_invoice-type[readonly] {
		pointer-events: none;
	}

	.item-particulars-add-container .hsn-suggestion {
		position:relative !important;
	}

	#id_invoice-ship_to_name {
		width: calc(100% - 240px);
    	float: right;
	}
	.txt_conversion_rate{
		height: 20px;
	    font-size: 10px;
	    width: 65px;
	    float: left;
	}

	.validate_qty{
		text-align: left !important;
	}

	.text_box_label{
		white-space: nowrap;
	    text-overflow: ellipsis;
	    overflow: hidden;
	}
	.unit_display, .td-unit-id{
		background: #CCC;
		float: right;
		margin-top: -26px !important;
		height: 26px !important;
		padding-top:2px !important;
		width: 45px !important;
		padding-right: 0px !important;
		border-radius: 0 4px 4px 0;
		white-space: nowrap;
	    text-overflow: ellipsis;
	    overflow: hidden;
	}

	.unit_select_box{
		padding-right: 0px !important;
        padding-left: 0px !important;
		position: relative !important;
		margin-top: -26px !important;
        width: 50px !important;
        right: 0px !important;
        left: -15px;
		top: -8px;
	}

	.all_units_select_box{
		margin-top: 8px;
		margin-right: -15px;
	}

	.table > tbody > tr > td{
		vertical-align: top !important;
	}

    #add_invoice_item {
        display: block;
        position: absolute;
        right: -2px;
        margin-top: 5px;
        border-radius: 50px;
    }
    #add_invoice_item.add_issue {
        right: -27px;
    }

    .switch-radio-button {
      width: 210px;
      border: solid 1px #004195;
      border-radius: 50px;
    }

    .switch-radio-button a.noActive ~ a.noActive {
      border-left: 1px solid #004195;
    }

    .switch-radio-button a {
      width: 50%;
      display: inline-block;
      float: left;
      text-align: center;
      padding: 7px;
      cursor: pointer;
      text-decoration: none;
      transition: all 0.35s;
    }

    .switch-radio-button .noActive {
      color: #004195;
    }

    .switch-radio-button .active {
      color: #fff !important;
      background-color: #004195;
      border-radius: 50px;
    }

    .switch-radio-button .noActive:hover {
      color: #209be1;
      background: rgba(32, 155, 225, 0.05);
    }

    .switch-radio-button .active:hover {
      background: #209be1;
    }

    .switch-radio-button.disabled {
      opacity: 0.6;
      pointer-events: none;
    }
    .switch-radio-button .noActive {
      color: #004195;
    }

	#cattable_2 tbody tr td .tree-view {
		position: relative;
	}

	#cattable_2 tbody tr td .tree-view:before {
	    border-bottom: 1px solid #bbb;
	    content: "";
	    left: 6px;
	    position: absolute;
	    top: 6px;
	    width: 1.5em;
	}

	#cattable_2 tbody tr td .tree-view:after {
	    border-left: 1px solid #bbb;
	    content: "";
	    left: 6px;
	    position: absolute;
	    top: -25px;
	    height: 4em;
	}

	#cattable_2 .fa-plus:before,
	#cattable_2 .fa-minus:before {
		border: solid 1px #333;
	    padding: 2px 3px 2px 3px;
	    border-radius: 3px;
	    font-size: 10px;
	}

  </style>


<link rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
<link rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link rel="stylesheet" href="/site_media/css/bootstrap-datetimepicker.css?v={{ current_version }}" >
<link rel="stylesheet" href="/site_media/css/quality-inspection-report.css?v={{ current_version }}" >
<link rel="stylesheet" type="text/css" href="/site_media/css/erp_default.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>

<script type="text/javascript" src="/site_media/js/bootstrap-multiselect.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-datetimepicker.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/invoice.js?v={{ current_version }}_1"></script>
<script type="text/javascript" src="/site_media/js/issue-document.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/notify.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/party_list.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/make.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/tags.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/print.min.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/mailer.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/load_material.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/quality-inspection-report.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/autoResize.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/Component/projects.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/Component/locations.js?v={{ current_version }}"></script>

<script>
	$('.nav-pills li').removeClass('active');
	{% if type == 'sales' %}
		$('#li_invoice').addClass('active');
		$("#menu_sales").addClass('selected');
	{% endif %}
	{% if type == 'dc' %}
		$('#li_dc').addClass('active');
		$("#menu_stores").addClass('selected');
	{% endif %}
	{% if type == 'internal' %}
		$('#li_issue').addClass('active');
		$("#menu_production_planning").addClass('selected');
	{% endif %}
</script>
<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">
			{% if type == 'sales' %}Invoice{% endif %}
			{% if type == 'dc' %}Delivery Challan{% endif %}
			{% if type == 'internal' %}Issue{% endif %}
		</span>
	</div>
	<div class="container-fluid">
		<input type="hidden"  id="cgst_tax" name="cgst_tax"/>
		<input type="hidden"  id="sgst_tax" name="sgst_tax"/>
		<input type="hidden"  id="igst_tax" name="igst_tax"/>
		<input type="hidden" value="{{selected_project_code}}" id="selected_project_code" hidden="hidden">
		<input type="hidden" value="{{se_project_code}}" id="se_project_code" hidden="hidden">
		<div>
			<div class="page-heading_new">
				<span class="page_header_invoice"></span>
				{% if type == 'sales' %}
					<a href="/erp/sales/invoice_list/" class="btn btn-add-new pull-right view_invoice" data-tooltip="tooltip" title="Back" style="margin-bottom: 8px"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
				{% endif %}
				{% if type == 'dc' %}
					<a href="/erp/stores/dc_list/" class="btn btn-add-new pull-right view_invoice" data-tooltip="tooltip" title="Back" style="margin-bottom: 8px"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
				{% endif %}
				{% if type == 'internal' %}
					<a href="/erp/stores/issue_list/"  class="btn btn-add-new pull-right view_invoice" data-tooltip="tooltip" title="Back" style="margin-bottom: 8px"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
				{% endif %}
				<span class="prev_next_container" style=""></span>
				{% if type == 'sales' and invoice.status.value == 1 %}
				{% if set_einvoice == True %}
				<div id="e-invoice_btn">
					<span class="btn transparent-btn pull-right tour_edit_e-invoice" style="margin-right: 8px;" onclick="generateEinvoiceSingle({% if invoice.id.value %}{{invoice.id.value}}{% endif %});">Generate e-Invoice Json</span>
				</div>
				{% endif %}
				{% if cnl_einvoice == True %}
				<div id="e-invoice_cncl_btn">
				<span class="btn transparent-btn pull-right" style="margin-right: 8px;" onclick="cancelEinvoiceJson({% if invoice.id.value %}{{invoice.id.value}}{% endif %});">Cancel IRN</span>
				</div>
				{% endif %}
				{% endif %}
				{% if logged_in_user.is_super %}
					{% if type == 'internal' %}
						<a class="btn super_user_icon hide" onclick="EditIssueNumber();">
							<i class="fa fa-pencil"></i>
						</a>
						<div class="xsid_number_edit hide">
							<form class="form-inline" style="display: inline-block;" action="">
							    <div class="form-group">
							      	<input type="text" class="form-control" id="inv_financial_year" name="inv_financial_year" maxlength="5">
							    </div>
							    {% if type == 'internal'%}
							    <div class="form-group">
								    <input type="text" class="form-control" id="inv_type" name="inv_type" maxlength="3" readonly="true">
							    </div>
							    {% else %}
							    <div class="form-group">
							    	<select class="form-control" id="inv_type" name="inv_type">
							    		<option value="DC" data-val="D">&nbsp;D &nbsp;&nbsp;&nbsp;(Delivery Challan)</option>
							    		<option value="JDC" data-val="J">&nbsp;J &nbsp;&nbsp;&nbsp;(Job Out)</option>
									    <option value="JIN" data-val="JIN">&nbsp;J &nbsp;&nbsp;&nbsp;(Job In - Return)</option>
							    	</select>
							    </div>
							    {% endif %}
							    <div class="form-group">
							      <input type="text" id="inv_number" name="inv_number" class="form-control" maxlength="6" onfocus="setNumberRangeOnFocus(this,6,0,true)" >
							    </div>
							    <div class="form-group">
							      	<input type="text" class="form-control text-lowercase" onkeypress="validateStringOnKeyPress(this,event,'character');" onblur="validateStringOnBlur(this,event,'character');" id="inv_number_division" name="inv_number_division" maxlength="1" >
							    </div>
							    <div class="form-group super_edit_submit_icon">
							    	<span role='button' data-tooltip="tooltip" title="Save" class="super_edit_save" onclick="SaveInvoiceNumber();"><i class="fa fa-check"  aria-hidden="true" style="color: green;"></i></span>
							    	<span role='button' data-tooltip="tooltip" title="Cancel" class="super_edit_cancel" onclick="DiscardEditInvoiceNumber();"><i class="fa fa-times" aria-hidden="true" style="color: red;"></i></span>
							    </div>
					  		</form>
					  		<div class="save_xsid_error_format hide">Required. Please check help tool for valid format.</div>
					  	</div>
					  	<div class="super_user_tool_tip hide"><span class="question-mark"></span></div>
					{% endif %}

					{% if type == 'sales' or   type == 'dc' %}
						<a class="btn super_user_icon hide" onclick="EditInvoiceNumber(true);" data-tooltip="tooltip" data-placement="bottom" data-title="Super User"  style="">
							<i class="fa fa-pencil"></i>
						</a>
						<div class="xsid_number_edit hide">
							<form class="form-inline" style="display: inline-block;" action="">
						        <div class="form-group">
							      	<input type="text" class="form-control" id="number_format" name="number_format" style="width:265px;border-radius:4px;" maxlength="20" data-default-value="{{ invoice.invoice_no.value }}" value="{{ invoice.code.value }}">
								    <div class="super_user_tool_tip hide"><span class="question-mark"></span></div>
							    </div>
							    <div class="form-group super_edit_submit_icon">
							    	<span role='button' data-tooltip="tooltip" title="Save" class="super_edit_save" onclick="SaveInvoiceNumber();"><i class="fa fa-check"  aria-hidden="true" style="color: green;"></i></span>
							    	<span role='button' data-tooltip="tooltip" title="Cancel" class="super_edit_cancel" onclick="DiscardEditInvoiceNumber();"><i class="fa fa-times" aria-hidden="true" style="color: red;"></i></span>
							    </div>
								<div style="font-size:11px;">Next Approved {% if type == 'sales' %} Invoice {% else %} DC {% endif %}Serial No. will be <b>
									<span id="id_inv_no" ></span> </b>
									<a class="btn edit-icon" onclick="EditSerialNumber(true);" style="margin-top: -5px; margin-left: -6px;">
										<i class="fa fa-pencil"></i>
									</a>
								</div>
								<div class="serial_no_edit hide" style="width:35%;font-size:11px;">
									<div style="font-size:11px;"><b>Serial No. of this {% if type == 'sales' %} Invoice {% else %} DC {% endif %}</b>
							      	    <input type="text" style="width: 60px;border-radius: 4px;margin-left: 5px;padding: 2px;border: 1px solid #ccc;" id="invoice_serial_number"  name="invoice_serial_number" maxlength="6" onkeypress="validateNumberOnKeyPress(this,event,0)" onblur="validateNumberOnBlur(this,event,0)" style="width:100px;border-radius:4px;margin-left: 5px;" data-default-value="{{ invoice.invoice_no.value }}"  value="{{ invoice.invoice_no.value }}" /><br>
										Change this {% if type == 'sales' %} Invoice{% else %}DC{% endif %}'s Serial No. to a number from which the Next {% if type == 'sales' %} Invoice {% else %} DC {% endif %} Serial No. will have to be rolled on.
										i.e., if the field says 100, the next {% if type == 'sales' %} Invoice {% else %} DC {% endif %} serial number will be 101.
							        </div>
							    </div>
					  		</form>
					  		<div class="save_xsid_error_format hide">Required. Please check help tool for valid format.</div>
					  	</div>
					{% endif %}
					{%else%}
						<a class="btn super_user_icon hide" style="color:#777;" onclick="" data-tooltip="tooltip" data-placement="right" data-title="Only Super User can edit this field">
							<i class="fa fa-pencil"></i>
						</a>
				{% endif %}
			</div>
			<div>
				<div class="content_bg">
					<div class="page-heading">
						<input type="hidden" value="{{ type }}" id="id_dc_type" name="invoice_type"/>
						<input type="hidden" value="oa" id="id_issue_type" name="issue_type"/>
					</div>
					<div class="tab-content {% if type == 'internal' %}container{% endif %}">
						<div>
							<div class="add_table">
								<form id="invoice_add" method="post" action="/erp/sales/issue/add/">
									{%csrf_token %}
									<input type="hidden" id="mrs_type" name="mrs_type"/>
									{% if invoice.code.value != None and invoice.code.value != '' and invoice.code.value != 'PF#000000' %}
										{% if type == 'sales' %}
	                                        <script type="text/javascript">
	                                        	$(".page_header_invoice").html('<span class="header_current_page"> {{ invoice.code.value }}</span>');
	                                        </script>
                                       	{% endif %}
										{% if type == 'dc' %}
										    <script type="text/javascript">
	                                            $(".page_header_invoice").html('<span class="header_current_page"> {{ invoice.code.value }}</span>');
	                                        </script>
										{%endif%}
										{% if type == 'internal' %}
											<script type="text/javascript">
	                                            $(".page_header_invoice").html('<span class="header_current_page"> {{ invoice.code.value }}</span>');
	                                        </script>
										{%endif%}
                                    {% else %}
                                    	{% if type == 'sales' %}
											<script type="text/javascript">$(".page-title").text('Invoice');</script>
										{% endif %}
										{% if type == 'dc' %}
											<script type="text/javascript">$(".page-title").text('Delivery Challan');</script>
										{%endif%}
										{% if type == 'internal' %}
											<script type="text/javascript">$(".page-title").text('Issue');</script>
										{%endif%}
                                    {% endif %}
									<input type="hidden" id="search_invoice_id" name="invoice_no" value="{% if invoice.id.value %}{{invoice.id.value}}{% endif %}">
									<div class="clearfix"></div>
									<div class="col-md-8 remove-padding">
										<div class="col-md-6 hide" id="tags"><!-- Tags are hidden on the 2.16.3 release -->
											<label>Tags</label>
											<ul id="ul-tagit-display" class="tagit-display form-control">
												{% for tag_form in tags_formset.initial_forms %}
													<li class="li-tagit-display" id="{{ tag_form.prefix }}">
														<div hidden="hidden">{{ tag_form.tag }}
															{{ tag_form.DELETE }}{{ tag_form.ORDER }}</div>
														<label class="label-tagit-display"  id="id_{{tag_form.prefix}}-tag_label">{{ tag_form.tag.value }}</label>&nbsp;
														<a class="delete_tag" id="{{tag_form.prefix}}-del_button"></a>
														&nbsp;
													</li>
												{% endfor %}
												<li id="tag-__dummy__" hidden="hidden">
													<div hidden="hidden">{{ tags_formset.empty_form.tag }}
													{{ tags_formset.empty_form.ORDER }}
													{{ tags_formset.empty_form.DELETE }}</div>
													<label class="label-tagit-display"  id="id_{{tags_formset.empty_form.prefix}}-tag_label"></label>&nbsp;
													<a class="delete_tag" id="{{tags_formset.empty_form.prefix}}-del_button"></a>
													&nbsp;
												</li>

												<span>
													{{ tags_formset.management_form }}
													<span id="{{ tags_formset.empty_form.prefix }}" class="text_for_tag" >
														{{ tags_formset.empty_form.tag }}
														{{ tags_formset.empty_form.ORDER }}
														<span hidden="hidden">{{ tags_formset.empty_form.DELETE }}</span>
													</span>
												</span>
											</ul>
											<span style="position: absolute; margin-top: -8px; color: #aaa; font-size: 11px;">Type and press enter to add new tag.</span>
										</div>
										<div class="col-md-2 hide" style="margin-top: 21px;">
											<input id="add_tag" class="btn btn-save btn-margin-1" value="+" type="button">
										</div>
										<div class="col-md-6 for-primary-ent">
											<div class="component_project" data-id="id_invoice-project_code" data-name="invoice-project_code" data-value={{invoice.project_code.value}} data-isSuper={{logged_in_user.is_super}} ></div>
											{{ invoice.project_id }}
											<label id="expRev" style="display: block;margin-top: 5px;">Revenue: <span id="revenue" style="margin-right: 10px;">0</span>Expense : <span id="expense" style="margin-right: 10px;">0</span> </label>
											<div style="margin-top:10px;margin-bottom:12px;">
												<label>Location <span class="mandatory_mark">*</span></label>
												{{ invoice.location_id }}
											</div>
										</div>
										{% if type == 'sales' %}
											<div class="col-md-3 checkbox chk-margin for-primary-ent" style="margin-bottom: 16px; margin-top: 12px;">
												{{ invoice.tax_payable_on_reverse_charge }}
												<label for="id_invoice-tax_payable_on_reverse_charge">TAX PAYABLE ON REVERSE CHARGE</label>
												{{ invoice.goods_already_supplied }}
												<label for="id_invoice-goods_already_supplied">GOODS ALREADY SUPPLIED</label>
											</div>
										{% endif %}
										{% if type == 'internal' %}
										<div class="col-md-6 form-group for_issue remove-padding">
											<div class="col-md-12 form-group for_issue" id="id_txt_wo_id_div">
												<label>Issue Against.</label>
													{{ invoice.job_po_id }}
											</div>
										</div>
										{% endif %}
										<div class="col-md-6 form-group for_issue" style="margin-top:-10px;">
											{{ invoice.id }}
											{{ invoice.enterprise_id }}
											<label>
												<span id="id_issued_to_label">Issued To<span class="mandatory_mark">*</span></span>
											</label>
											<select class="form-control chosen-select" name="invoice-issued_to" id="id_invoice-issued_to" >
												<span><option value="0" hidden="hidden">Select an option</option></span>
												      <option value="add_new_issued_to" id="add_new_issued_to" class="bold-option">+ Add new </option>
													  <optgroup label="Frequently used">
														  {% for j in issued_to.0 %}
														    {% if j.0|lower == invoice.issued_to.value|lower %}
														    <option value="{{ j.0|lower }}" selected="selected">{{ j.0 }}</option>
														    {% else %}
														    <option value="{{ j.0|lower }}">{{ j.0 }}</option>
														    {% endif %}
														  {% endfor %}
													  </optgroup>
													  <optgroup label="All">
														{% for j in issued_to.1 %}
															{% if j.0|lower == invoice.issued_to.value|lower %}
														    <option value="{{ j.0|lower }}" selected="selected">{{ j.0 }}</option>
														    {% else %}
														    <option value="{{ j.0|lower }}">{{ j.0 }}</option>
														    {% endif %}
														{% endfor %}
													  </optgroup>
											</select>
										</div>
										<div class="col-md-6 for_issue" id="id_txt_issue_for_div">
											<label id="lbl_issue_for">Issued For<span class="mandatory_mark"> *</span></label>
											<select class="form-control chosen-select" name="invoice-issued_for" id="id_invoice-issued_for" >
												<span>
													<option value="0" hidden="hidden">Select an option</option></span>
									    		  	<option value="add_new_issued_for" id="add_new_issued_for" class="bold-option">+ Add new </option>
												  	<optgroup label="Frequently used">
													  	{% for j in issued_for.0 %}
														    {% if j.0|lower == invoice.issued_for.value|lower %}
														    <option value="{{ j.0|lower }}" selected="selected">{{ j.0 }}</option>
														    {% else %}
														    <option value="{{ j.0|lower }}">{{ j.0 }}</option>
														    {% endif %}
													  	{% endfor %}
												  	</optgroup>
												  	<optgroup label="All">
														{% for j in issued_for.1 %}
															{% if j.0|lower == invoice.issued_for.value|lower %}
														    <option value="{{ j.0|lower }}" selected="selected">{{ j.0 }}</option>
														    {% else %}
														    <option value="{{ j.0|lower }}">{{ j.0 }}</option>
														    {% endif %}
														{% endfor %}
												  	</optgroup>
											</select>
										</div>
										<div class="clearfix"></div>
										<div class="col-md-2 form-group for_invoice for_dc for-primary-ent" id="id_jdc_div">
											<label>
											{% if logged_in_user.is_super %}
												<a class="super_edit_field super_edit_for_draft hide" id="super_edit_for_project" onclick="SuperEditInvoiceSelect(this);" data-tooltip="tooltip" data-placement="left" title="Super Edit">
													<i class="fa fa-pencil super_edit_in_field"></i>
												</a>
											{%else%}
												<a class="super_edit_field super_edit_for_draft hide" style="color:#777;" id="super_edit_for_project" onclick="" data-tooltip="tooltip" data-placement="top" title="Only Super User can edit this field">
													<i class="fa fa-pencil super_edit_in_field"></i>
												</a>
											{%endif%}
												Type<span class="mandatory_mark"> *</span>
											</label>
											<div class="{% if invoice.id.value != None %} div-disabled {% endif %}">
											{{invoice.type}}
											<input type="hidden" id="current_type" value="{{invoice.type.value}}">
											<input type="hidden" name="type" value="{{type}}">
											</div>
										</div>

										<div class="col-md-3 form-group for_invoice for_dc">
											<label>
												<span id="id_party_label">Party<span class="mandatory_mark">*</span></span>
											</label>{% if type != "internal" %}{{invoice.party_id}}{% endif %}
											<input type="hidden" value="" id="se_party"/>
										</div>
										<div class="col-md-3 form-group for_dc" id="return_date">
											<label>Return Date</label>
											{{invoice.return_date}}
											<input type="text" class="form-control custom_datepicker set-empty-date erasable-date" id="invoice_return_date" placeholder="Select Date" readonly="readonly">
											<i class="glyphicon glyphicon-calendar custom-calender-icon"></i>
										</div>
										<div class="col-md-3 form-group for_invoice for-primary-ent">
											<label>Sale Account<span class="mandatory_mark"> *</span></label>
											{{ invoice.sale_account_id }}
										</div>
										{% if se_no == None or se_no == ""%}
											{% if promote_inv_se_code == None or promote_inv_se_code == "" %}
												<div class="col-md-3 form-group for_invoice for_dc multiselect_option" id="div_oa_no_display">
													<label> <span class="oa_party_dc_no">OA</span> No</label>
													{{invoice.order_accept_no}}
													<select class="form-control" name="invoice-order_accept_no_display" id="id_invoice-order_accept_no_display" multiple="multiple">
														<option value="0">--select--</option>
													</select>
												</div>
											{% endif %}
										{% endif %}
										<div class="col-md-4 form-group for_invoice hide multiselect_option" id="div_dc_no_display">
											<label>SUPPLIED DCs</label>
											<select class="form-control" name="invoice_fin_year" id="id_invoice_fin_year" multiple="multiple"></select>
											{{invoice.dc_no}}
											<select class="form-control" name="invoice-dc_no_display" id="id_invoice-dc_no_display" multiple="multiple">
												<option value="0">--select--</option>
											</select>
										</div>
										<div class="col-md-9 form-group for_invoice for_dc for-primary-ent">
											<label data-default-label="Delivery Address" data-template-label="{{inv_config_details.template_header_details.shippingaddress_label}}" style="padding-top: 7px; margin-bottom: 15px;word-break: break-all;">Delivery Address</label> {{invoice.ship_to_name}} <span class="enable_edit_option" onclick="EnableGSTEdit();" data-tooltip="tooltip" title="Edit">
												<i class="fa fa-pencil super_edit_in_field" style="margin-top: -3px; margin-right: 15px;"></i>
											</span>
											{{invoice.deliver_to}}
											<div style="margin-top: 5px;"></div>
											<div class="col-sm-8 remove-padding">
												{{invoice.gstin}}
											</div>
											<div class="col-sm-4 remove-padding-right">
												{{invoice.ecommerce_gstin}}
											</div>
											{{invoice.enterprise_id}}
										</div>
										<div class="col-md-3 form-group for_dc for_invoice remove-padding">
											{% if type != 'internal' %}
												<div class="col-md-12 form-group for_dc" id="id_txt_po_id_div">
													<label>JOB PO NO<span class="mandatory_mark"> *</span></label>
														{{ invoice.job_po_id }}
												</div>
											{% endif %}
											<div class="col-md-12 form-group for_invoice for_dc for-primary-ent" id="id_txt_po_no_div">
												<label style="word-break: break-all;" id="lbl_prepared_dt"  data-default-label="Po No" data-template-label="{{inv_config_details.template_header_details.pono_label}}">PO No</label>
												{{invoice.po_no}}
											</div>
											<div class="col-md-12 form-group for_invoice for_dc for-primary-ent" id="lbl_po_date">
												<label style="word-break: break-all;" data-default-label="Po Date" data-template-label="{{inv_config_details.template_header_details.podate_label}}">PO Date</label>
												{{invoice.po_date}}
												<input type="text" class="form-control custom_datepicker from-beginning till-today set-empty-date" id="invoice_po_date" placeholder="Select Date" readonly="readonly">
												<i class="glyphicon glyphicon-calendar custom-calender-icon"></i>
											</div>
										</div>

										<div class="col-md-2 col-md-2 form-group" id="id_dc_div">
											<label>Type<span class="mandatory_mark"> *</span></label>
											{{invoice.dc_type}}
											<input type="hidden" value="{{invoice.status.value}}" id="invoice_status"/>
										</div>
										<div class="col-md-2 for_issue for_dc" id="lbl_issue_date " hidden="hidden">
											<label>Approved on <span class="mandatory_mark"> *</span></label>
											{{invoice.approved_on}}
										</div>
										{% if type != 'internal' %}
											{% if invoice.remarks.value|length > 0 %}
												<div class="col-md-4 for_issue">
													<label>Remarks</label>
													<input class="form-control" disabled value="{{ invoice.remarks.value }}"/>
												</div>
											{% endif %}
										{% endif %}
										<div class="col-md-3 form-group for_invoice" hidden="hidden">
											<label>OA Date</label>
											{{invoice.order_accept_date}}
											<div id="invoice_oa_date" class="report-date form-control full-date">
												<span></span>&nbsp;
												<i class="glyphicon glyphicon-calendar" style="float:right"></i>
											</div>
										</div>
									</div>
									<div class="form-group col-md-4">
										{{ invoice.se_id }}
										{{ invoice.se_date }}
										<table border="0" class="side-table" style="float: right;">
											{% if invoice.id.value != '' and invoice.id.value != None %}
												{% if se_no != None and se_no != '' %}
													<tr class="se_no_row">
														<td class="side-header" style="width: 150px;">
															Estimate No.</td>
														<td class="side-content">{{ se_no }}</td>
													</tr>
												{% endif %}
											{% else %}
												{% if promote_inv_se_code %}
													<tr class="se_no_row">
														<td class="side-header" style="width: 150px;">
															Estimate No.</td>
														<td class="side-content">{{ promote_inv_se_code }}</td>
													</tr>
												{% endif %}
											{% endif %}
											<tr>
												<td class="side-header" style="width: 150px;">
													{% if type == 'sales' %}
														<span  data-default-label="Invoice" data-template-label="{{inv_config_details.template_header_details.invoiceno_label}}" style="word-break: break-all;">Invoice</span>
													{% endif %}
													{% if type == 'dc' %}
														<span  data-default-label="DC" data-template-label="{{inv_config_details.template_header_details.dcno_label}}" style="word-break: break-all;">DC</span>{%endif%}
													{% if type == 'internal' %} Issue {%endif%}
												No.</td>
												<td class="side-content">{{ invoice.code.value }}</td>
											</tr>
											<tr>
												<td class="side-header" style="width: 150px;word-break: break-all;"><span data-default-label="Issued Date" data-template-label="{{inv_config_details.template_header_details.invoicedate_label}}"></span>
													<span class="for_issue mandatory_mark"> *</span>
												</td>
												<td class="side-content side-content-datepicker ">
													{{invoice.issued_on}}
													{% if logged_in_user|canEdit:'SALES' or logged_in_user|canEdit:'STORES' %}
														<input type="text" class="form-control custom_datetimepicker till-today" id="invoice_issued_on_date" autocomplete="off" placeholder="Select Date" readonly="readonly">
														<i class="glyphicon glyphicon-calendar custom-calender-icon pull-right"></i>
													{% else %}
														<input type="text" class="form-control custom_datetimepicker till-today remove-cursorEvent" id="invoice_issued_on_date" autocomplete="off" readonly="readonly">
													{% endif %}
												</td>
											</tr>
											{% if type != 'internal' %}
												<tr>
													<td class="side-header" style="width: 150px;">Approved Date</td>
													<td class="side-content">
														{% if invoice.approved_on.value %}
															{{invoice.approved_on.value|date:'M d, Y H:i:s'}}
														{% endif %}
													</td>
												</tr>
											{% endif %}
										</table>
									</div>

									<div class="col-md-4 for_invoice for_dc excise_fields for-primary-ent" style="margin-top: -6px;">
										<table border="0" class="side-table" style="float: right;">
											<tr>
												<td class="side-header"  data-default-label="Transport Mode" data-template-label="{{inv_config_details.template_header_details.transportmode_label}}" style="width: 150px;word-break: break-all;" >
													Transport Mode
												</td>
												<td class="side-content">
													{{invoice.transport_mode}}
												</td>
											</tr>
											<tr>
												<td class="side-header" data-default-label="Road Permit No." data-template-label="{{ inv_config_details.template_header_details.roadpermitno_label }}"  style="width: 150px;word-break: break-all;">
													Road Permit No.
												</td>
												<td class="side-content">
													{{invoice.road_permit_no}}
												</td>
											</tr>
											<tr>
												<td class="side-header" data-default-label="LR No. & Date" data-template-label="{{ inv_config_details.template_header_details.lrnodate_label }}" style="width: 150px;word-break: break-all;">
													LR No. & Date
												</td>
												<td class="side-content">
													{{invoice.lr_no}}
												</td>
											</tr>
											<tr>
												<td class="side-header" data-default-label="Packing Slip No." data-template-label="{{ inv_config_details.template_header_details.packingslipno_label }}" style="width: 150px;word-break: break-all;">
													Packing Slip No.
												</td>
												<td class="side-content">
													{{invoice.packing_slip_no}}
												</td>
											</tr>
											<tr>
												<td class="side-header" data-default-label="Packing Description" data-template-label="{{ inv_config_details.template_header_details.packingdescription_label }}" style="width: 150px;word-break: break-all;">
													Packing Description
												</td>
												<td class="side-content">
													{{invoice.packing_description}}
												</td>
											</tr>
										</table>
									</div>

									<div class="clearfix"></div>
									{{ inv_material_formset.management_form }}
									{% if invoice.status.value == 1 %}
										<div class="form-group  for_dc for_issue" style="position: absolute; z-index: 100; right: 85px; margin-top: -24px;">
											{% if is_returnable == 1 or type == 'internal' %}
												{% if is_item_returned == 1 %}
													<a class="btn btn-save btn-margin-2" onclick="javascript:showReport('Item_Returned');">Items Returned </a>
												{% else %}
													<a class="btn btn-save btn-margin-2 disabled" >Items Not Returned </a>
												{% endif%}
											{% endif%}
											{% if is_not_returnable == 1  and  type != 'internal' and is_job_returned == 0 %}
												{% if is_item_invoice == 1 %}
													<a class="btn btn-save btn-margin-2" onclick="javascript:showReport('Item_Invoiced');">Items Invoiced </a>
												{% else %}
													<a class="btn btn-save btn-margin-2 disabled" >Items Not Invoiced </a>
												{% endif%}
											{% endif%}
										</div>
									{% endif%}
									<div class="col-md-12 search_result_table" style="margin-bottom:18px;margin-top:15px;">
										<ul class="nav nav-tabs">
										  	<li id="item_particulars" class="materials_add active"><a role="button">Item Particulars</a></li>
											<li class="checkbox for-primary-ent">
										  		<input type="checkbox" id="invoice_quick_add" onChange='updateUserSettingsFlag()' />
										  		<label for="invoice_quick_add" data-tooltip="tooltip" title="Stock availability will still be validated while saving the Invoice in a consolidated manner." style="margin-top:3px">Quick Add <small>(Skip Stock Availability Check)</small></label>
									  		</li>
											<li class="service_flag" style="margin-top: 12px"><span class='service-item-flag'></span> - Service</li>
										</ul>

										<div class="col-md-12 search_result_table item-particulars-add-container">
											<div id="stockable">
												<table class="table table-bordered table-hover table-striped custom-table custom-table-large table-fixed tableWithText quality-inpection-table" id="invoice_materials_table">
													<thead class="th-vertical-center">
														<tr>
														<th rowspan="2" style="width: 9%;" data-default-label="S.No" data-template-label="{{inv_config_details.template_item_details.sno_label}}">S.No</th>
														<th rowspan="2" hidden="hidden">Entry Order</th>
														<th rowspan="2" style="width:8%;" class="for_invoice for_dc th-oa-no" data-default-label="{% if isprimary_project %}OA No {% else %} IWO No {% endif %}" data-template-label="{% if isprimary_project %}OA No {% else %} IWO No {% endif %}">OA No</th>
														<th rowspan="2" style="width:30%;"  data-default-label="Description" data-template-label="{{inv_config_details.template_item_details.itemdetails_label}}">Description</th>
														<th rowspan="2" style="width:10%;" class="for_dc">To Be Returned</th>
														<th rowspan="2" style="width:10%;" class="for_invoice for_dc" data-default-label="HSN/SAC" data-template-label="{{inv_config_details.template_item_details.hsnsac_label}}">HSN/SAC</th>
														<th rowspan="2" style="width:12%;" data-default-label="Quantity" data-template-label="{{inv_config_details.template_item_details.quantity_label}}">Quantity</th>
														<th rowspan="2" style="width:5%;" class="for_issue" hidden="hidden">Stock</th>
<!--														<th rowspan="2" style="width:8%;" data-default-label="Units" data-template-label="{{inv_config_details.template_item_details.units_label}}">Units</th>-->
														<th rowspan="2" style="width:12%;" class="for_invoice for_dc" data-default-label="Unit Rate" data-template-label="{{inv_config_details.template_item_details.unit_price_label}}">Unit Rate</th>
														<th rowspan="2" style="width:12%;" id="tbl_head_discount" class="for_invoice for_dc" data-default-label="Disc. (%)" data-template-label="{{inv_config_details.template_item_details.discount_label}}">Disc. (%)</th>
														<th rowspan="2" style="width:12%;" class="sales_net_value for_invoice for_dc" data-default-label="Amount" data-template-label="{{inv_config_details.template_item_details.taxable_amount_label}}">Amount</th>
														<th rowspan="2" style="width:8%;" class="taxable for_invoice for_dc for-primary-ent">CGST (%)<span class="th-sub-heading bracket-enclosed">Amount</span></th>
														<th rowspan="2" style="width:8%;" class="taxable for_invoice for_dc for-primary-ent">SGST (%)<span class="th-sub-heading bracket-enclosed">Amount</span></th>
														<th rowspan="2" style="width:8%;" class="taxable for_invoice for_dc for-primary-ent">IGST (%)<span class="th-sub-heading bracket-enclosed">Amount</span></th>
													</tr>
												</thead>
												<tbody class='item-for-goods hide'>

													<!-- Item editing List -->
													{% for inv_material_form in inv_material_formset.initial_forms %}
													{% if inv_material_form.is_service.value == "0" %}
														<tr class="invoice_item_row edited_item_row material_list" id="{{inv_material_form.prefix}}">
														<td hidden="hidden">
															{{ inv_material_form.invoice_id }}
															{{ inv_material_form.id }}
															{{ inv_material_form.ORDER }}
															{{ inv_material_form.DELETE }}
															{{ inv_material_form.enterprise_id }}
															{{ inv_material_form.remarks}}
															{{ inv_material_form.make }}
															{{ inv_material_form.make_id }}
															{{ inv_material_form.is_faulty }}
															{{ inv_material_form.is_service }}
															{{ inv_material_form.item_id }}
															{{ inv_material_form.alternate_unit_id }}
															{{ inv_material_form.scale_factor }}
															{{ inv_material_form.alternate_unit_list }}
															<span class="is_stock_material">{{ inv_material_form.material_type }}</span>
															<span class="is_service_material">{{ inv_material_form.is_service }}</span>

															<span class="td-oa_no">{{ inv_material_form.oa_no }} </span>
															<span class="td-dc_no">{{ inv_material_form.dc_no }} </span>
															<span class="td-grn_no">{{ inv_material_form.grn_no }} </span>
															<input name="item_tax"  type="text" hidden="hidden" id="id_{{inv_material_form.prefix}}-item_tax">
															<input type="hidden" class="quantity_inspection_headers" value=''>
															{{ inv_material_form.inspection_log }}
														</td>
														<td class="text-center">
															<span class="table-inline-icon-bg s_no" data-order="{{ inv_material_form.entry_order.value }}"></span>
                                                                <span class="table-inline-icon hide">
                                                                    <a data-tooltip='tooltip' data-placement='right' title='Quality Inspection Report'  onclick= "openQuantityInspectionReport(this)">
                                                                        <img src='/site_media/images/quality-inspection.png' style="width: 20px; margin-top: -8px;" />
                                                                    </a>
                                                                </span>
                                                                <span class="table-inline-icon hide">
                                                                    {% if invoice.status.value == 0 or  type == 'internal' or logged_in_user.is_super %}
                                                                        <a id="id_{{ inv_material_form.prefix }}-deleteInvoiceMaterial" data-tooltip='tooltip' data-placement='right' title='Delete Item'
                                                                           onclick="javascript:deleteInvoiceMaterial('{{ inv_material_form.prefix }}')">
                                                                            <i class="fa fa-trash-o" title="Delete" alt="Delete"></i>
                                                                        </a>
                                                                    {% endif %}
                                                                </span>
														</td>
														<td class="text-center s_no_hidden" hidden="hidden">{{ inv_material_form.entry_order }}</td>
														<td class=" for_invoice for_dc">
															{% if inv_material_form.oa_code.value %}
																<span class="visible-block">
																	<b>{% if isprimary_project %}OA No {% else %} IWO No {% endif %} : </b> {{ inv_material_form.oa_code.value }}
																</span>
															{%endif%}
															{% if inv_material_form.dc_code.value %}
																<span class="visible-block">
																	<b>DC No:</b> {{ inv_material_form.dc_code.value }}
																</span>
															{%endif%}
															{% if inv_material_form.grn_code.value %}
																<span class="visible-block">
																	<b>GRN No:</b> {{ inv_material_form.grn_code.value }}
																</span>
															{%endif%}
														</td>
														<td class="td-item-code">
															{{ inv_material_form.item_code }}
															<span class="item_description">
																{{ inv_material_form.item_name.value }}
															</span>
                                                            <span class="form-group non-form-element">
                                                                <span class="item_remarks_section {% if inv_material_form.remarks.value == '' %}hide{% endif %}">{{ inv_material_form.remarks }}</span>
                                                                <span class="add_item_remarks {% if inv_material_form.remarks.value != '' %}hide{% endif %}" onclick="showItemRemarks(this)">+ Add Remarks</span>
															</span>
														</td>
														<td hidden="hidden" class='text-left'>
															<div id="id_{{ inv_material_form.prefix }}-makeLabel"></div>
														</td>
														<td class="text-center for_dc {% if invoice.type.value == 'JDC'  %} div-disabled {% endif %}"><div>{{ inv_material_form.is_returnable }}</div></td>
														<td class="for_invoice for_dc td-hsn-code hsn-wrapper">
															<div>{{ inv_material_form.hsn_code }}</div>
														</td>
														<td class="for_issue received_qty hidden">
															<div>{{ inv_material_form.received_qty }}</div>
														</td>
														<td class="td_textbox_right">
															<span class="td_inv_qty">
																<div class=" for_invoice for_dc for_issue">
																	  {{ inv_material_form.quantity }}
																      <p style="color: #dd4b39;" class="available-qty-container hide">Avail:
																		<span class="currently_available_stock hide">0.000</span>
																		<span class="currently_available_stock_vsb">0.000</span>
																		<span class='is_msl hide'></span>
																	    <span class="currently_available_pending hide">0.000</span>
																	</p>
																	<span id="old_qty" class="hide">
																		 {{ inv_material_form.quantity }}
																	</span>
																</div>
															</span>
															<span class="td_textbox_center td-unit-id td_textbox_center">
																{{ inv_material_form.unit_id }}
															</span>

														</td>
														<td class="text-right for_issue td-stock-qty" hidden="hidden">
															<div id="id_{{ inv_material_form.prefix }}-stock_div">
																<span id="id_{{ inv_material_form.prefix }}-stock_qty"
																      item_id="{{ inv_material_form.item_id.value }}"
																      item_code="{{ inv_material_form.item_code.value }}"
																      make_id="{{ inv_material_form.make_id.value }}"
																      is_faulty="{{ inv_material_form.is_faulty.value }}"
																      class="stock_qty"></span>
															</div>
														</td>
														<td class="td_textbox_right for_invoice for_dc td-unit-rate">
															<div>
																{{ inv_material_form.rate }}
															</div>
														</td>
														<td class="td_textbox_right for_invoice for_dc td-unit-discount">
															<div>
																{{ inv_material_form.discount }}
															</div>
														</td>
														<td class="td_textbox_right for_invoice for_dc td-unit-total">
															<div>
																{{inv_material_form.amount }}
															</div>
														</td>
														{{ inv_material_form.taxes.management_form }}
														{% for tax in inv_material_form.taxes %}
														<td class="taxable for_invoice for_dc td-tax-rate for-primary-ent">{{ tax.tax_code }}{{ tax.rate }}
															<select id="id_{{tax.prefix}}-rate_drop_down"
																	class="form-control"  name="{{tax.tax_type.value}}"
																	onchange="javascript:calculateGSTAmount('{{tax.prefix}}', '{{inv_material_form.prefix}}');calculateGrandTotal();">
																<option value="{{tax.tax_code.value}}">{% if tax.rate.value %}{{tax.rate.value}}{% else %}0{% endif %}</option>
															</select>
															<span class="td-sub-content bracket-enclosed" id="id_span_{{tax.prefix}}-amount" name="{{tax.tax_type.value}}_AMT" >
																{{tax.amount.value}}
															</span>
															<script type="text/javascript">
																calculateGSTAmount('{{tax.prefix}}', '{{inv_material_form.prefix}}');
															</script>
														</td>
														{% endfor %}
													</tr>
													{% endif %}
													{% endfor %}
												</tbody>
												<tbody class='item-for-service hide'>
													{% for inv_material_form in inv_material_formset.initial_forms %}
													{% if inv_material_form.is_service.value == "1" %}
													<tr class="invoice_item_row edited_item_row material_list" id="{{inv_material_form.prefix}}">
														<td hidden="hidden">
															{{ inv_material_form.invoice_id }}
															{{ inv_material_form.id }}
															{{ inv_material_form.ORDER }}
															{{ inv_material_form.DELETE }}
															{{ inv_material_form.enterprise_id }}
															{{ inv_material_form.remarks}}
															{{ inv_material_form.make }}
															{{ inv_material_form.make_id }}
															{{ inv_material_form.is_faulty }}
															{{ inv_material_form.is_service }}
															{{ inv_material_form.item_id }}
															{{ inv_material_form.alternate_unit_id }}
															{{ inv_material_form.scale_factor }}
															{{ inv_material_form.alternate_unit_list }}
															<span class="is_stock_material">{{ inv_material_form.material_type }}</span>
															<span class="is_service_material">{{ inv_material_form.is_service }}</span>

															<span class="td-oa_no">{{ inv_material_form.oa_no }} </span>
															<span class="td-dc_no">{{ inv_material_form.dc_no }} </span>
															<span class="td-grn_no">{{ inv_material_form.grn_no }} </span>
															<input name="item_tax"  type="text" hidden="hidden" id="id_{{inv_material_form.prefix}}-item_tax">
															<input type="hidden" class="quantity_inspection_headers" value=''>
															{{ inv_material_form.inspection_log }}
														</td>
														<td class="text-center">
															<span class="table-inline-icon-bg s_no" data-order="{{ inv_material_form.entry_order.value }}"></span>
                                                            <span class="table-inline-icon hide">
                                                                <a data-tooltip='tooltip' data-placement='right' title='Quality Inspection Report' onclick= "openQuantityInspectionReport(this)">
                                                                    <img src='/site_media/images/quality-inspection.png' style="width: 20px; margin-top: -8px;" />
                                                                </a>
                                                            </span>
                                                            <span class="table-inline-icon hide">
                                                                {% if invoice.status.value == 0 or  type == 'internal' or logged_in_user.is_super %}
                                                                    <a id="id_{{ inv_material_form.prefix }}-deleteInvoiceMaterial" data-tooltip='tooltip' data-placement='right' title='Delete Item'
                                                                       onclick="javascript:deleteInvoiceMaterial('{{ inv_material_form.prefix }}')">
                                                                        <i class="fa fa-trash-o" title="Delete" alt="Delete"></i>
                                                                    </a>
                                                                {% endif %}
                                                            </span>
														</td>
														<td class="text-center s_no_hidden" hidden="hidden">{{ inv_material_form.entry_order }}</td>
														<td class=" for_invoice for_dc">
															{% if inv_material_form.oa_code.value %}
																<span class="visible-block">
																	<b>OA No:</b> {{ inv_material_form.oa_code.value }}
																</span>
															{%endif%}
															{% if inv_material_form.dc_code.value %}
																<span class="visible-block">
																	<b>DC No:</b> {{ inv_material_form.dc_code.value }}
																</span>
															{%endif%}
															{% if inv_material_form.grn_code.value %}
																<span class="visible-block">
																	<b>GRN No:</b> {{ inv_material_form.grn_code.value }}
																</span>
															{%endif%}
														</td>
														<td class="td-item-code">
															{{ inv_material_form.item_code }}
															<span class="item_description">
																{{ inv_material_form.item_name.value }}
																{% if inv_material_form.is_service.value == '1' %}
																	<span class='service-item-flag'></span>
																{% endif %}
															</span>
                                                            <span class="form-group non-form-element">
                                                                <span class="item_remarks_section {% if inv_material_form.remarks.value == '' %}hide{% endif %}">{{ inv_material_form.remarks }}</span>
                                                                <span class="add_item_remarks {% if inv_material_form.remarks.value != '' %}hide{% endif %}" onclick="showItemRemarks(this)">+ Add Remarks</span>
															</span>
														</td>
														<td hidden="hidden" class='text-left'>
															<div id="id_{{ inv_material_form.prefix }}-makeLabel"></div>
														</td>
														<td class="text-center for_dc {% if invoice.type.value == 'JDC'  %} div-disabled {% endif %}"><div>{{ inv_material_form.is_returnable }}</div></td>
														<td class="for_invoice for_dc td-hsn-code hsn-wrapper">
															<div>{{ inv_material_form.hsn_code }}</div>
														</td>
														<td class="for_issue received_qty hidden">
															<div>{{ inv_material_form.received_qty }}</div>
														</td>
														<td class="td_textbox_right">
															<span class="td_inv_qty">
																<div class=" for_invoice for_dc for_issue">
																	  {{ inv_material_form.quantity }}
																      <p style="color: #dd4b39;" class="available-qty-container hide">Avail:
																		<span class="currently_available_stock hide">0.000</span>
																		<span class="currently_available_stock_vsb">0.000</span>
																		<span class='is_msl hide'></span>
																	    <span class="currently_available_pending hide">0.000</span>
																	</p>
																	<span id="old_qty" class="hide">
																		 {{ inv_material_form.quantity }}
																	</span>
																</div>
															</span>
															<span class="td_textbox_center td-unit-id td_textbox_center">
																{{ inv_material_form.unit_id }}
															</span>
														</td>
														<td class="text-right for_issue td-stock-qty" hidden="hidden">
															<div id="id_{{ inv_material_form.prefix }}-stock_div">
																<span id="id_{{ inv_material_form.prefix }}-stock_qty"
																      item_id="{{ inv_material_form.item_id.value }}"
																      item_code="{{ inv_material_form.item_code.value }}"
																      make_id="{{ inv_material_form.make_id.value }}"
																      is_faulty="{{ inv_material_form.is_faulty.value }}"
																      class="stock_qty"></span>
															</div>
														</td>
														<td class="td_textbox_right for_invoice for_dc td-unit-rate">
															<div>
																{{ inv_material_form.rate }}
															</div>
														</td>
														<td class="td_textbox_right for_invoice for_dc td-unit-discount">
															<div>
																{{ inv_material_form.discount }}
															</div>
														</td>
														<td class="td_textbox_right for_invoice for_dc td-unit-total">
															<div>
																{{inv_material_form.amount }}
															</div>
														</td>
														{{ inv_material_form.taxes.management_form }}
														{% for tax in inv_material_form.taxes %}
														<td class="taxable for_invoice for_dc td-tax-rate for-primary-ent">{{ tax.tax_code }}{{ tax.rate }}
															<select id="id_{{tax.prefix}}-rate_drop_down"
																	class="form-control"  name="{{tax.tax_type.value}}"
																	onchange="javascript:calculateGSTAmount('{{tax.prefix}}', '{{inv_material_form.prefix}}');calculateGrandTotal();">
																<option value="{{tax.tax_code.value}}">{% if tax.rate.value %}{{tax.rate.value}}{% else %}0{% endif %}</option>
															</select>
															<span class="td-sub-content bracket-enclosed" id="id_span_{{tax.prefix}}-amount" name="{{tax.tax_type.value}}_AMT" >
																{{tax.amount.value}}
															</span>
															<script type="text/javascript">
																calculateGSTAmount('{{tax.prefix}}', '{{inv_material_form.prefix}}');
															</script>
														</td>
														{% endfor %}
													</tr>
													{% endif %}
													{% endfor %}
												</tbody>
												<tbody class="add_item_details invoice_item_table">
													<tr bgcolor="#ececec" id="invoice_mat-__dummy__" hidden="hidden">
														<td hidden="hidden">
															{{ inv_material_formset.empty_form.invoice_id }}
															{{ inv_material_formset.empty_form.DELETE }}
															{{ inv_material_formset.empty_form.id }}
															{{ inv_material_formset.empty_form.ORDER }}
															{{ inv_material_formset.empty_form.enterprise_id }}
															{{ inv_material_formset.empty_form.make }}
															{{ inv_material_formset.empty_form.make_id }}
															{{ inv_material_formset.empty_form.is_faulty }}
															{{ inv_material_formset.empty_form.is_service }}
															{{ inv_material_formset.empty_form.item_id }}
															<span class="td-oa_no">{{ inv_material_formset.empty_form.oa_no }}</span>
															<span class="td-dc_no">{{ inv_material_formset.empty_form.dc_no }}</span>
															<span class="td-grn_no">{{ inv_material_formset.empty_form.grn_no }}</span>
															<input name="item_tax" id="id_invoice_mat-__prefix__-item_tax" type="text" hidden="hidden">
															{{ inv_material_formset.empty_form.alternate_unit_id }}
															{{ inv_material_formset.empty_form.scale_factor }}
															{{ inv_material_formset.empty_form.alternate_unit_list }}
															<span class="is_stock_material">{{ inv_material_formset.empty_form.material_type }}</span>
															<span class="is_service_material">{{ inv_material_formset.empty_form.is_service }}</span>
															<input type="hidden" class="quantity_inspection_headers" value=''>
															{{ inv_material_formset.empty_form.inspection_log }}
														</td>
														<td class="text-center">
                                                            <span class="table-inline-icon-bg s_no" data-order="{{ inv_material_formset.empty_form.entry_order.value }}"></span>
                                                            <span class="table-inline-icon hide">
                                                                <a data-tooltip='tooltip' data-placement='right' title='Quality Inspection Report' onclick= "openQuantityInspectionReport(this)">
                                                                    <img src='/site_media/images/quality-inspection.png' style="width: 20px; margin-top: -8px;" />
                                                                </a>
                                                            </span>
                                                            <span class="table-inline-icon hide">
                                                                {% if invoice.status.value == 0 or logged_in_user.is_super %}
                                                                    <a id="id_{{ inv_material_formset.prefix }}-deleteInvoiceMaterial" data-tooltip='tooltip' data-placement='right' title='Delete Item' onclick="javascript:deleteInvoiceMaterial('{{ inv_material_formset.empty_form.prefix }}')">
                                                                        <i class="fa fa-trash-o" title="Delete" alt="Delete"></i>
                                                                    </a>
                                                                {% endif %}
                                                            </span>
														</td>
														<td class="hide text-center s_no_hidden">
															{{ inv_material_formset.empty_form.entry_order }}
														</td>
														<td class="text-left for_invoice for_dc">
															<span class="visible-block oa_code_container"></span>
															<span class="visible-block dc_code_container"></span>
															<span class="visible-block grn_code_container"></span>
															{{ inv_material_formset.empty_form.oa_code }}
															{{ inv_material_formset.empty_form.dc_code }}
															{{ inv_material_formset.empty_form.grn_code }}
														</td>
														<td class="td-item-code">
															{{ inv_material_formset.empty_form.item_code }}
															<span class="item_description">{{ inv_material_formset.empty_form.item_name }}</span>
                                                            <span class="form-group non-form-element">
                                                                <span class="item_remarks_section hide">{{inv_material_formset.empty_form.remarks}}</span>
                                                                <span class="add_item_remarks" onclick="showItemRemarks(this)">+ Add Remarks</span>
															</span>
														</td>
														<td hidden="hidden" class='text-left'>
															<div id="id_invoice_mat-__prefix__-makeLabel"></div>
														</td>
														<td class="for_dc text-center">
															<div>{{ inv_material_formset.empty_form.is_returnable }}</div>
														</td>
														<td class=" for_invoice for_dc td-hsn-code hsn-wrapper">
															<div>{{ inv_material_formset.empty_form.hsn_code }} </div>
														</td>
														<td class="td_textbox_right">
															<div >
																<span class="td_inv_qty">
																	{{ inv_material_formset.empty_form.quantity }}
																	<p style="color: #dd4b39;" class="available-qty-container hide">Avail:
																		<span class="currently_available_stock hide">0.000</span>
																		<span class="currently_available_stock_vsb">0.000</span>
																		<span class='is_msl hide'></span>
																		<span class="currently_available_pending hide">0.000</span><span></span>
																		<span class="allocated_qty hide">0.000</span>
																	</p>
																</span>
																<span class="td_textbox_center td-unit-id td_textbox_center">
																	{{ inv_material_formset.empty_form.unit_id }}
																</span>
															</div>
														</td>
														<td class=" for_issue" hidden="hidden">
															<div>
																<span id="id_invoice_mat-__prefix__-stock_qty" style="float:right;"></span>
															</div>
														</td>
														<td class=" for_invoice for_dc td_textbox_right td-unit-rate">
															<div>
																{{ inv_material_formset.empty_form.rate }}
															</div>
														</td>
														<td class=" for_invoice for_dc td_textbox_right td-unit-discount">
															<div>
																{{ inv_material_formset.empty_form.discount }}
															</div>
														</td>
														<td class=" for_invoice for_dc td_textbox_right td-unit-total">
															<div>
																{{inv_material_formset.empty_form.amount}}
															</div>
														</td>
														{{ inv_material_formset.empty_form.taxes.management_form }}
														{% for tax in inv_material_formset.empty_form.taxes %}
														<td class="taxable for_invoice for_dc td-tax-rate for-primary-ent">{{ tax.tax_code }}{{ tax.rate }}
															<select id="id_{{tax.prefix}}-rate_drop_down"
																class="form-control sel_indv_{{tax.tax_type.value|lower}}"  name="{{tax.tax_type.value}}"
																onchange="javascript:calculateGSTAmount('{{tax.prefix}}', '{{inv_material_formset.empty_form.prefix}}');calculateGrandTotal();">
																<option value="{{tax.tax_code.value}}">{% if tax.rate.value %}{{tax.rate.value}}{% else %}0{% endif %}</option>
															</select>
															<span class="td-sub-content bracket-enclosed" id="id_span_{{tax.prefix}}-amount" name="{{tax.tax_type.value}}_AMT" >
																{{tax.amount.value}}
																</span>
															<script type="text/javascript">
																calculateGSTAmount('{{tax.prefix}}', '{{inv_material_formset.empty_form.prefix}}');
															</script>
														</td>
														{% endfor %}
													</tr>
													<tr class="item-particulars-add-container for-primary-ent" id="materials_add">
														<div class="hide">
															<input id="id_invoice_mat-__prefix__-make_id" hidden="hidden" type="text" class="form-control" name="ind_material-__prefix__-make_id" />
															<input id="id_invoice_mat-__prefix__-is_faulty" hidden="hidden" type="text" class="form-control" name="ind_material-__prefix__-is_faulty" />
															<input id="id_invoice_mat-__prefix__-is_service" hidden="hidden" type="text" class="form-control" name="ind_material-__prefix__-is_service" />
														</div>
														<td style="text-align:center;">
															<div id='loadingmessage_changelog_listing_ie' style="text-align:center;position: absolute;z-index: 1;left: 50%;display:none">
																<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' width="100">
																<br /> Please wait...
														</div>
															<button type="button" class="btn btn-save btn-margin-1 {% if type == 'internal' %} add_issue{%endif%}" id="add_invoice_item" data-placement="left" data-tooltip="tooltip" title="Add to the List" >+</button>
															<button type='button' id="explode_bom" class="btn btn-save btn-margin-1" style="display: block;position: absolute;right: -89px;margin-top: 5px;">BoM</button>
														</td>
														<td class="td_issue"></td>
														<td>
															<div class="non-form-element material_name">
																<input type="text" value="" class="form-control" id="materialrequired" maxlength="100" placeholder="Item profiled against a Drawing No/Item Code" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar');">
																<input type="text" value="" class="" id="material_id_hidden" placeholder="" hidden="hidden">
																<input type="hidden" value="" class="" id="material_is_service" placeholder="" hidden="hidden">
																<input type="text" value="" class="" id="material_id" placeholder="" hidden="hidden">
																<input type="text" value="" class="" id="is_stock" placeholder="" hidden="hidden">
																<input type="text" value="" class="" id="cat_code" placeholder="" hidden="hidden">
																<span class="material-removal-icon hide" style="margin-top: -26px;height: 25px;padding-top: 5px;">
																	<i class="fa fa-times"></i>
																</span>
																<div hidden="hidden">
																	{{ inv_material_formset.empty_form.item_id }}
																	{{ inv_material_formset.empty_form.item_code }}
																	{{ inv_material_formset.empty_form.item_name }}
																	{{ inv_material_formset.empty_form.enterprise_id }}
																	{{ inv_material_formset.empty_form.oa_no }}
																	{{ inv_material_formset.empty_form.oa_code }}
																	{{ inv_material_formset.empty_form.dc_no }}
																	{{ inv_material_formset.empty_form.dc_code }}
																	{{ inv_material_formset.empty_form.grn_no }}
																	{{ inv_material_formset.empty_form.grn_code }}
																	{{ inv_material_formset.empty_form.alternate_unit_id }}
																	{{ inv_material_formset.empty_form.scale_factor }}
																	{{ inv_material_formset.empty_form.alternate_unit_list }}
																	{{ inv_material_formset.empty_form.material_type }}
																	{{ inv_material_formset.empty_form.is_service }}
																</div>  <!-- material_list -->
															</div>
															<span class="form-group non-form-element" id="div_remarks">
                                                                <span class="item_remarks_section hide">{{inv_material_formset.empty_form.remarks}}</span>
                                                                <span class="add_item_remarks" onclick="showItemRemarks(this)">+ Add Remarks</span>
                                                                <span class="item_faulty_check checkbox chk-margin item_particulars_chk non-form-element" style="margin-top:16px;margin-bottom:4px;">
                                                                    <input type="checkbox" class="chkcase" name="case" id="chkfaulty"  value="" />
                                                                    <label for="chkfaulty" style="min-height: 0;">Faulty</label>
                                                                </span>
															</span>
														</td>
														<td class="td_to-return hide text-center">
															<div class="checkbox chk-margin item_particulars_chk non-form-element" style="margin-left: -10px;">
																<span class="for_dc" style="display: inline-block;">
																	<input type="checkbox" class="chkcase" name="case" id="chkreturn"  value="" />
																	<label for="chkreturn"></label>
																</span>
															</div>
														</td>
														<td class="td_issue">
															<div class="for_invoice for_dc non-form-element hsn-wrapper">
																{{ inv_material_formset.empty_form.hsn_code }}
															</div>
														</td>
														<td>
															<div class="non-form-element">
																<input type="hidden" id="Avl_Qty" class="form-control"/>
																<input type="hidden" id="msl_qty" class="form-control"/>
																<input type="hidden" id="primary_closing_qty" class="form-control"/>
																<input type="hidden" id="primary_Avl_Qty" class="form-control"/>
																<input type="hidden" id="primary_msl_qty" class="form-control"/>
																<input type="hidden" id="material_rate_hidden" class="form-control"/>
																{{inv_material_formset.empty_form.quantity}}
																<div>
																	<span id="unit_display" class="unit_display pull-right td-unit-id" style="">&nbsp;</span>
																</div>
																<div class="alternate_unit_select_box hide" style="position: relative;margin-top: 8px;margin-right: -16px;">
																	{{ inv_material_formset.empty_form.alternate_units }}
																</div>
																<div class="all_units_select_box hide">
																	{{ inv_material_formset.empty_form.all_units }}
																</div>
																<label style="display: block;"><span id="closing_qty">Max issue:0</span> </label>
															</div>
														</td>
														<td class="hide">
															{{ inv_material_formset.empty_form.unit_id}}
														</td>
														<td class="td_issue td_textbox_right">
															<div class="for_invoice for_dc non-form-element">
																{{inv_material_formset.empty_form.rate}}
															</div>
														</td>
														<td class="td_issue td_textbox_right">
															<div class="for_invoice for_dc non-form-element" id="div_inv_mat_discount">
																{{ inv_material_formset.empty_form.discount}}
															</div>
														</td>
														<td class="td_issue td_textbox_right">
															<div class="for_invoice for_dc">
																{{ inv_material_formset.empty_form.amount }}
															</div>
														</td>
														<td class="td_issue for-bos"></td>
														<td class="td_issue for-bos"></td>
														<td class="td_issue for-bos">
<!--															<button type="button" class="btn btn-save btn-margin-1" id="add_invoice_item"data-tooltip="tooltip" title="Add to the List" style="display: block;position: absolute;right:-3px;" >+</button><br>-->
<!--															<button type='button' id="explode_bom" class="btn btn-save btn-margin-1" style="z-index: 1000000;display: block;position: absolute;right: -14px;margin-top: 48px;">BoM</button>-->
														</td>
													</tr>
												</tbody>
													<tfoot>
													<tr class=" for_invoice" id="packing_diff_line">
														<td colspan="15" class="packing_diff_line" bgcolor="#ccc" style="padding:2px 0px !important;">
															{{ invoice_charges.management_form }}
														</td>
													</tr>
													{% for charge in invoice_charges.initial_forms %}
													<tr class="invoice_charges_row for_invoice for-primary-ent">
														<td class="td-packing-forwaring" colspan="2" align="right">{{ charge.item_name.value }}
															<div class="hide">
																{{ charge.item_name }}{{ charge.is_percent }}
																{{ charge.DELETE }}{{ charge.ORDER }}
																{{ charge.enterprise_id }}{{ charge.id }}
																{{ charge.invoice_id }}
															</div>
															<input type="hidden" id="id_{{charge.prefix}}-quantity"
															       name="{{charge.prefix}}-quantity" value="1"/>
														</td>

														<td class="td_hsn_code hsn-wrapper">
															{{ charge.hsn_code }}
														</td>
														<td colspan="">
															<select class="form-control hide" id="id_{{charge.prefix}}_rate_mode">
																<option value="TOTAL">Total</option>
																<option value="PERCENT">%</option>
															</select>
															<script type="text/javascript">
																$("#id_{{charge.prefix}}_rate_mode").change(function(){
																	if($("#id_{{charge.prefix}}_rate_mode option:selected").val()=="PERCENT"){
																		$("#id_{{charge.prefix}}_is_percent").attr("checked", "checked");
																	} else {
																		$("#id_{{charge.prefix}}_is_percent").removeAttr("checked");
																	}
																});
															</script>
														</td>
														<td>{{ charge.rate }}</td>
														<td>{{ charge.discount }}</td>
														<td>{{ charge.amount }}</td>
														{{ charge.taxes.management_form }}
														{% for charge_tax in charge.taxes %}
														<td class="taxable for_invoice for_dc for-primary-ent">{{ charge_tax.tax_code }}{{ charge_tax.rate }}
																<input type="hidden" id="id_{{charge_tax.prefix}}-enterprise_id"
																       name="{{charge_tax.prefix}}-enterprise_id"
																		value="{{ charge.enterprise_id.value }}"/>
															<select id="id_{{charge_tax.prefix}}-rate_drop_down"
																	class="form-control"  name="{{charge_tax.tax_type.value}}"
																	onchange="javascript:calculateGSTAmount('{{charge_tax.prefix}}', '{{charge.prefix}}'); calculateGrandTotal();">
																<option value="{{charge_tax.tax_code.value}}">{% if charge_tax.rate.value %}{{charge_tax.rate.value}}{% else %}0{% endif %}</option>
															</select>
															<span class="td-sub-content bracket-enclosed" id="id_span_{{charge_tax.prefix}}-amount" name="{{charge_tax.tax_type.value}}_AMT">
																{{charge_tax.amount.value}}
															</span>
															<script type="text/javascript">
																calculateGSTAmount('{{charge_tax.prefix}}', '{{charge.prefix}}');
															</script>
														</td>
														{% endfor %}
														<td class="delete_others hide"></td>
													</tr>
													{% endfor %}
													<tr class="for_invoice for_dc">
														<td align="right" class="total_span_column">
															<span style="font-size: 16px; color: #838383;">TOTAL<br /><small class="for_dc" style="text-transform: lowercase;">(Indicative Value)</small></span>
														</td>
														<td>
															<input type='text' class='form-control text_box_label text-right' style="font-size: 18px !important; color: #838383;" id='invoice_tot' name="invoice_tot" disabled/>
														</td>

														<td class="taxable for_invoice for_dc for-primary-ent">
															<input type="text" class='form-control text_box_label text-right' style="font-size: 16px !important; color: #838383;" disabled id="net_cgst_value"/>
														</td>

														<td class="taxable for_invoice for_dc for-primary-ent">
															<input type="text" class='form-control text_box_label text-right' style="font-size: 16px !important; color: #838383;" disabled id="net_sgst_value"/>
														</td>

														<td class="taxable for_invoice for_dc for-primary-ent">
															<input type="text" class='form-control text_box_label text-right' style="font-size: 16px !important; color: #838383;" disabled id="net_igst_value"/>
								                       </td>
								                       <td class="delete_others hide"></td>
													</tr>
												<!-- Item Editing End Here -->
												</tfoot>
												</table>
											</div>
										</div>
									</div>

									<div class="grand-total-container">
										<div class="col-md-8 remove-padding">
											<div class="col-md-12 remove-padding for-primary-ent">
												<div class="col-md-4 for_invoice for_dc remove-padding-right">
													<label>
														{% if logged_in_user.is_super %}
															<a class="super_edit_field super_edit_for_load" onclick="SuperEditInvoiceSelect(this)" data-tooltip="tooltip" data-placement="left" title="Super Edit">
																<i class="fa fa-pencil super_edit_in_field" style="margin-right: -20px;"></i>
															</a>
														{%else%}
															<a class="super_edit_field super_edit_for_load" style="color:#777;" onclick="" data-tooltip="tooltip" data-placement="bottom" title="Only Super User can edit this field">
																<i class="fa fa-pencil super_edit_in_field" style="margin-right: -20px;"></i>
															</a>
														{% endif %}
														Currency
													</label>
													<div class="div-disabled">
														{{ invoice.currency_id}}
													</div>
                                                    <div id="div_con_rate" class="conversion_rate_container hide">
                                                        <span class="currency_convertor_section">
                                                            <span>1</span>
                                                            <span class="converted_currency_txt"></span>
                                                            <span>=</span>
                                                        </span>
                                                        <span>{{ invoice.currency_conversion_rate}}</span>
                                                        <span class="base_currency_txt"></span>
                                                    </div>
												</div>
												<div class="col-md-4 form-group for_invoice for_dc">
													<label data-default-label="Payment Terms" data-template-label="{{inv_config_details.template_header_details.paymentterms_label}}" style="word-break: break-all;">Payment Terms</label>
													{{invoice.payment_terms}}
												</div>
												<div hidden="hidden">
													{{ tax_list.empty_form.tax_code }}
													{{ tax_list.empty_form.enterprise_id }}
													{{ tax_list.empty_form.invoice_id }}
												</div>

												<div class="col-md-4">
													<div class="row form-group for_invoice for_dc">
														<div class="col-md-10 taxable non-form-element" id="tax_choices" style="padding-left: 0;">
															<label>Taxes</label>
															{{ tax_list.management_form }}
															<select class="form-control chosen-select" name="select" id="invoice_tax_list">
																{% for tax in load_tax %}
																<option value="{{ tax.0 }}">{{ tax.1 }}</option>
																{% endfor %}
															</select>
														</div>
														<div class="col-md-2 taxable remove-padding-left">
															<span class="btn btn-add-tax" id="add_invoice_tax" data-tooltip="tooltip" title="Add tax">
																<i class="fa fa-angle-double-right" aria-hidden="true"></i>
															</span>
														</div>
													</div>
												</div>
											</div>
											<div class="col-sm-12 for_dc">
												<div id="switch_consignment" class="switch-radio-button pull-left btn-group" style="margin-top: 18px;">
													<a class="toggle_courier noActive" data-toggle="courier" data-title= "0" data-tooltip="tooltip" title="Courier">Courier</a>
													<a class="toggle_own_transport active" data-toggle="own_transport" data-title="1" data-tooltip="tooltip" title="Own transport">Own transport</a>
												</div>
												{{ invoice.is_courier}}
												<div class="consignment-container pull-left hide">
													<div class="form-group col-md-6 for_dc">
														<label>No Of Consignment/Packages</label>
														{{ invoice.no_of_consignment}}
													</div>
													<div class="form-group col-md-6 for_dc">
														<label id="lbl_cons_weight" data-default-label="Consignment Weight in Kg"  style="word-break: break-all;">Weight</label>
														{{ invoice.weight}}
													</div>
												</div>
											</div>

											<div class="form-group col-md-6">
												<label>Remarks</label>
											    <div class="remarks_count_link remarks_count disabled" onclick="remarks_history();">
													<span class="remarks_counter">No</span><span> remarks</span>
												</div>
												{{invoice.remarks}}
											</div>
											<div class="form-group col-md-6 for_invoice for_dc">
												<label id="lbl_special_instruction" data-default-label="Special Instruction" data-template-label="{{inv_config_details.template_header_details.splinstructions_label}}" style="word-break: break-all;">Special Instruction</label>
												{{invoice.special_instruction}}
											</div>
											{% if type != 'internal' %}
												<div class="form-group col-md-12 for-primary-ent">
													<label>Notes</label>
												    <div style="float: right; margin-top: -1px; background: #FFF; margin-right: -8px; color: #004195; cursor: pointer;" onclick="openNotesOverlay();">
														<i class="fa fa-pencil super_edit_in_field" style="margin-top: -3px; margin-right: 15px;"></i>
													</div>
													{{invoice.notes}}
												</div>
											{% endif %}
										</div>
										<div class="col-md-4 remove-padding">
											<div id="invoice_taxes" style="padding-left: 0;">
												<div class="for_invoice for_dc" style="padding-right: 0;">
													<div class="col-md-12 po_taxes_table">
														<table id="invoice_tax_table" border="0" width="100%" class="taxable ">
															<tbody>
															<tr id="invoice_tax-__dummy__">
																<td hidden="hidden">
																	{{ tax_list.empty_form.DELETE }}
																	{{ tax_list.empty_form.enterprise_id }}
																	{{ tax_list.empty_form.invoice_id }}
																	{{ tax_list.empty_form.tax_code}}
																	<a href="#"
																	   id="id_{{ tax_list.empty_form.prefix }}-deleteInvoiceTax"
																	   onclick="javascript:deleteInvoiceTax('{{ tax_list.empty_form.prefix }}')">
																		<img src="/site_media/images/deleteButton.png"
																		     title="Delete" alt="Delete"
																		     align="center"/></a>
																</td>
															</tr>
															{% for tax_form in tax_list.initial_forms %}
															<tr id="{{ tax_form.prefix }}">
																<td hidden="hidden">
																	{{ tax_form.DELETE }}
																	{{ tax_form.enterprise_id }}
																	{{ tax_form.invoice_id }}
																	{{ tax_form.tax_code }}
																	<a href="#"
																	   id="id_{{ tax_form.prefix }}-deleteInvoiceTax"
																	   onclick="javascript:deleteInvoiceTax('{{ tax_form.prefix }}')">
																		<img src="/site_media/images/deleteButton.png"
																		     title="Delete" alt="Delete"
																		     align="center"/></a>
																</td>
															</tr>
															{% endfor %}
															<tr hidden="hidden">
																<td><input type=hidden id="id_edit_data"
																           value="{{inv_edit_id}}"/></td>
																<td><input type=hidden id="id_promote_se_data"
																           value="{{promote_inv_se_id}}"/></td>
															</tr>
															</tbody>
														</table>
														<table class="table" id="invoice_tot_table" border="0">
															<tbody>
																<tr>
		                                                            <td class="col-sm-4" style="border: none; "></td>
		                                                            <td class="col-sm-8 checkbox"  style="border: none; padding-right: 16px; margin-top: 6px; font-size: 13px !important;">

		                                                            </td>
	                                                            </tr>
	                                                            <tr>
	                                                                <td class="col-md-12" style="border: none; text-align: right; padding-right: 16px; float: right; margin-top: 6px; font-size: 13px !important;">
	                                                                    <label>Round off</label>
	                                                                </td>
	                                                                <td style="border: none; padding-right:0;" align="right">
	                                                                    {{ invoice.round_off }}
	                                                                    <span class='checkbox pull-left' style="margin-left: -15px;">
		                                                                    <input id="id_auto-calculate" type="checkbox" onchange="calculateGrandTotal(); updateUserSettingsFlag();" />
											                                <label for="id_auto-calculate">Auto-Suggest</label>
											                            </span>
	                                                                </td>
	                                                            </tr>
																<tr>
																	<td class="col-md-12" style="border: none; text-align: right; padding-right: 16px; float: right; margin-top: 6px; font-size: 13px !important;">
																		<label style="font-size: 16px;">Grand Total</label>
																	</td>
																	<td class="col-md-6" style="border: none; padding-right:0;">
																		<input type="text" class="form-control text-right grand-total-amount"
																		   disabled id="grand_total_value"/>
																		{{ invoice.grand_total }}
																	</td>
																</tr>
															</tbody>
														</table>
													</div>
												</div>
												<div class="clearfix"></div>
											</div>
										</div>
									</div>

									<div class="col-md-12 material_txt text-right">
										<input type="hidden" name="type" value="{{type}}">
										{% if access_level.edit %}
											{% if invoice.status.value == 0 or type == "internal" %}
												<input type="button" class="btn btn-save" value="Save" id="cmdSaveInvoice"/>
												<input type="hidden" value="0" name="invoice-super_edit"/>
												{% if type == "internal" %}
													{% if invoice.code.value != None and invoice.code.value != '' and invoice.code.value != 'PF#000000' %}
														<a role='button' id='cmdPreviewInvoice' class='btn btn-save' onclick="generate_issue_pdf($('#id_invoice-id').val(),$('#id_invoice-type').val(),'1','',true,false);">Preview</a>
													{% endif %}
												{% endif %}
											{% else %}
												{% if logged_in_user.is_super or access_level.approve %}
													{% if logged_in_user.is_super %}
														<input type="hidden" value="1" name="invoice-super_edit"/>
														<input type="button" class="btn btn-save" value="Update" id="cmdSaveInvoice"/>
													{% endif %}
												{% else %}
													Approved! No further Changes Allowed.
												{% endif %}
											{% endif %}
										{% endif %}
									</div>
								</form>
								<form method="post" action="{{list_link}}">{%csrf_token%}
									<div class="col-md-12 hide">
										<input type="hidden" name="type" value="{{type}}">
										<input type="submit" class="btn btn-cancel" id="cancel_issue" value="Back"/>
									</div>
								</form>
								<div class="clearfix"></div>
							</div>
						</div>
					</div>
				    {% include "masters/add_party_modal.html" %}
					<div id="table_tab">
						<div class="resp-tabs-container hor_1">
							<div>
								<div class="clearfix"></div>
							</div>
							<div class="add_table">
								<div id="error_messages" width="50%" style="display:none" class="error_panel">
									<table class="error_display"
									       style="margin:100px 0px 0px 470px;color:#FF0000; background:#00FFFF;"
									       cellpadding="2"
									       cellspacing="2" border="0">
										<tr>
											<td colspan="3" align="center">

											</td>
										</tr>
										<tr>
											<td colspan="3">
												<ul>
													{% if invoice.errors %}
													<h4>Invoice Form Errors:</h4>
													{% endif %}
													{% for field in invoice.hidden_fields %}
													{% for error in field.errors %}
													<li>{{field.label_tag}} : {{error}}</li>
													{% endfor %}
													{% endfor %}
													{% for field in invoice.visible_fields %}
													{% for error in field.errors %}
													<li>{{field.label_tag}} : {{error}}</li>
													{% endfor %}
													{% endfor %}
												</ul>
											</td>
										</tr>
										<tr style="float:left">
											<td colspan="4">
												<ul>
													{% if inv_material_formset.errors|length > 0 %}
													<h4>Invoice Materials Errors:</h4>
													{% for error in inv_material_formset.errors %}
													General Material error: {{error}}
													{% endfor %}
													{% endif %}
													{% for form in inv_material_formset.initial_forms %}
													{% for field in form.hidden_fields %}
													{% for error in field.errors %}
													<li>Material {{form.prefix}}:{{field.label_tag}}:{{error}}</li>
													{%endfor%}
													{% endfor %}
													{% for field in form.visible_fields %}
													{% for error in field.errors %}
													<li>Material {{form.prefix}}:{{field.label_tag}}:{{error}}</li>
													{%endfor%}
													{% endfor %}
													{% endfor %}

													{% if tax_list.errors|length > 0 %}
													<h4>Invoice Tax Errors:</h4>
													{%for error in tax_list.errors %}
													General Tax error: {{error}}
													{% endfor %}
													{% endif %}
													{%for form in tax_list.initial_forms %}
													{% for field in form.hidden_fields %}
													{% for error in field.errors %}
													<li>Invoice Tax {{form.prefix}}:{{field.label_tag}}:{{error}}
													</li>
													{%endfor%}
													{% endfor %}
													{% for field in form.visible_fields %}
													{% for error in field.errors %}
													<li>Invoice Tax {{form.prefix}}:{{field.label_tag}}:{{error}}
													</li>
													{%endfor%}
													{% endfor %}
													{% endfor %}

												</ul>
											</td>
										</tr>
										<tr>
											<td>
												<input type="text" value='{{ invoice.errors }}' id="form_errors"
												       hidden="hidden"/>
												<input type="text"
												       value='{{inv_material_formset.errors }} {{tax_list.errors}}'
												       id="formset_errors" hidden="hidden"/>
											</td>
										</tr>
										<tr align="center">
											<td><a href="#" id="error_close" class="update1">Close</a></td>
										</tr>
									</table>
								</div>
							</div>
						</div>
					</div>
					<!-- /.row -->
				</div>
				<!-- /.container-fluid -->
			</div>
		</div>
	</div>
</div>

<div id="returnMaterial" class="modal fade" role="dialog" >
  	<div class="modal-dialog modal-lg">
   		<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Items <span class="item_invoiced_header">Returned</span></h4>
      		</div>
      		<div class="modal-body">
            	<input type="hidden" id="id_job_po" name="job_po_id" value="">
            	<table class="table table-bordered custom-table table-striped" id="return_material_list">
                    <thead>
	                    <tr>
	                        <th width="5%"> S.No. </th>
	                        <th width="20%"> <span class="item_invoiced">Grn</span> No</th>
		                    <th width="12%"> <span class="item_invoiced">Grn</span> Date</th>
	                        <th width="42%"> Item Details</th>
	                        <th width="12%"> Quantity</th>
	                        <th width="9%"> Unit</th>
	                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
      		</div>
		   <div class='modal-footer'>
			    <span class="pull-left nonStock-text">  <span class=" nonStock-text non_stock-flag"></span> - NON-STOCKABLE</span>
			    <span class="pull-left nonStock-text">  <span class=" nonStock-text service-item-flag"></span> - SERVICE</span>
			    <a class="btn btn-cancel" data-dismiss="modal">Close</a>
		   </div>
		</div>
  	</div>
</div>

<div id="catalogueModal" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg" style="width: 80%; max-width: 1250px;">
	    <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">{% if type == 'dc' %}PO Materials{% else %}BoM Materials {% endif %}</h4>
            </div>
            <div class="modal-body" style="overflow-x: auto;">
                <table id="cattable_2" class="table table-bordered custom-table table-striped tableWithText">
					<thead>
					<tr>
						<th style="width: 50px;">S.No</th>
                        <th class="hide">Drawing No</th>
                        <th class="for_wo_no">Material</th>
						<th style="width: 100px;" class="for_dc">HSN/SAC</th>
						<th class="hide">Make</th>
						<th style="width: 280px;" class="for_wo_no">Qty</th>
						<th style="width: 110px;" class="for_wo_no">
							{% if type == 'internal' %}
								<span data-tooltip="tooltip" data-placement="left" title="Based on available stock on Issue Date and MSL">Max Issue</span>
							{% else %}
								<span data-tooltip="tooltip" data-placement="left" title="Based on available stock on Issue Date and MSL">Max Qty</span>
							{% endif %}
						</th>
						<th style="width: 80px;" class="for_wo_no">Unit</th>
						<th style="width: 100px;" class="for_dc">Unit rate</th>
						<th style="width: 100px;" class="for_dc">Disc.(%)</th>
						<th style="width: 100px;" class="for_dc">Amount</th>
						<th style="width: 60px;">Delete</th>
					</tr>
					</thead>
					<tbody>
 				<div id='loadingmessage_changelog_listing_ie1' style="text-align:center;position: absolute;z-index: 1;left: 40%;top: 30%;display: none;">
						<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' width="100">
						<br /> Please wait...
			    </div>
					</tbody>
				</table>
            </div>
            <div id="catButton"></div>
        </div>
    </div>
</div>

<div id="add_issued_for"  class="modal fade"  role="dialog">
	<div class="modal-dialog">
        <!-- Modal content-->
	    <div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Add Issue For</h4>
			</div>
			<div class="modal-body">
				<form class="form-horizontal" method="post"  id="add_issue_for_form" action="">{% csrf_token %}
					<div class="form-group">
						<label class="control-label col-md-3" >Issue For<span class="mandatory_mark"> *</span> </label>
						<div class="col-md-6">
							<input  class="form-control" autocomplete="off" id="id_issued_for" placeholder="Enter name" name="issued_for" maxlength="30" onKeyPress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar')">
							<div class="empty_issued_for" hidden="hidden" style="font-size: 12px; color: #dd4b39;  position: absolute; letter-spacing: 0.30px; line-height: 20px;">Issue for is required.</div>
							<div class="duplicate_issued_for" hidden="hidden" style="font-size: 12px; color: #dd4b39;  position: absolute; letter-spacing: 0.30px; line-height: 20px;">Already exists</div>
						</div>
						<button id="add_issued_for_button" type="button" class="btn btn-save">Add</button>
					</div>
			    </form>
			</div>
		</div>
	</div>
</div>
<!--TODO separate to reuse -->
<div id="OAMaterial" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg" style="width: 60%;">
	    <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title"><span class="oa_dc_no_heading"> View OA Materials </span></h4>
            </div>
            <div class="modal-body" style="overflow-x: auto;">
                <table id="OAMaterialtable" class="table table-bordered custom-table custom-table-large tableWithText table-striped">
					<thead>
						<tr align="center">
							<th>Action</th>
							<th class="hide">S.No</th>
							<th class="td_oa_dc hide" style="width: 100px;"><span class="oa_grn_no">OA</span> No</th>
							<th class="hide">Drawing No/Material</th>
							<th>Item Details</th>
							<th>HSN/SAC</th>
							<th>Remarks</th>
							<th style="min-width: 60px;">Pending<br>(<span class="oa_grn_no">OA</span> Qty) </th>
							<th style="min-width: 60px;">Qty</th>
							<th style="min-width: 50px;" class="for_oa">Max issue</th>
							<th style="min-width: 50px;">Unit</th>
							<th>Unit rate</th>
							<th>Disc.(%)</th>
							<th>Amount</th>
						</tr>
					</thead>
					<tbody></tbody>
				</table>
            </div>
		    <input type="hidden" value="" id="oa_taxes"/>
            <div id="OAButton"></div>
        </div>
    </div>
</div>

<div id="inv_document_modal" class="modal fade" role="dialog">
  	<div class="modal-dialog modal-lg">
		<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Preview Document</h4>
      		</div>
      		<div class="modal-body text-center">
      			<input type="hidden" name="type" value="" id="modal_inv_type"/>
				<input hidden="hidden" id="modal_inv_id" name="invoice_no" value='' />
		        <input type="hidden" name="download_headers[]" value=" " id="id_header_text"/>
				<div class="row" style="display: inline-block; text-align: left;">
					<div class="col-lg-12" style="width: 898px;">
						<div class="content_bg">
							<div class="add_table" id="inv_doc_btn">
								<div class="col-sm-6 add_table_content remove-padding">
									<input id="remarks" class="form-control" enterkey="do_nothing" maxlength="248" value='{{ approval_remarks }}' placeholder="Approval/Rejection Remarks"/>
									<div class="remarks_count_link remarks_count disabled" onclick="remarksInSwal('inv_document_remarks');">
										<span class="remarks_counter">No</span><span> remarks</span>
									</div>
								</div>
								<div class="col-sm-3">
									<div>
										{% if logged_in_user|canApprove:'SALES' %}
										<span class="material_txt">
											<a role='button' id='approve_invoice' class="btn btn-save">Approve</a>
										</span>
									    {% endif %}
									    {% if logged_in_user|canApprove:'SALES' %}
											<a role='button' id='reject_invoice' class="btn btn-danger for_inv_approve">Reject</a>
										{% else %}
										{%if logged_in_user|canEdit:'SALES' %}
											<a role='button' id='reject_invoice' class="btn btn-danger for_inv_edit">Discard</a>
										{% endif %}
										{% endif %}
										{% if logged_in_user|canApprove:'SALES' %}
											<a role='button' id='regenerate_invoice' class="btn btn-save">Regenerate</a>
										{% endif %}
                                    </div>
								</div>
								<div class="col-sm-3 remove-padding">
									<div class="dropdown pull-right">
									    <div style="display: inline-block;">
											<a role="button" id='display_popup' onclick="openMailPopup()" class="btn transparent-btn" data-tooltip="tooltip" data-placement='left' title="Email"><i class="fa fa-envelope" style="font-size: 14px;" aria-hidden="true"></i></a>
										</div>
										{% if type|lower != 'internal' and type|lower != 'issue' %}
	                                        <button class="btn transparent-btn dropdown-toggle" id="download_copy" type="button" data-toggle="dropdown">
										    	<i class="fa fa-print" aria-hidden="true" data-tooltip="tooltip" title="Print" style="font-size: 18px; padding-right: 4px;"></i>
											    <span class="caret"></span>
											</button>
										{% endif %}
									    <div class="custom-dropdown-menu arrow_box sales-receipt-arrow_box" role="menu" id="download_copy_list" aria-labelledby="download_copy" style="width: 270px;margin-top: 13px; margin-right: -5px;">
									      <span role="button" class="checkbox" style="padding: 5px;">
									      	<input type="checkbox" class="chkcase" id="print_original" value="original" />
									      	<label for="print_original">Original For Recipient</label>
									      </span>
									      <span role="button" class="checkbox" style="padding: 5px;">
									      	<input type="checkbox" class="chkcase" id="print_buyer" value="transport" />
									      	<label for="print_buyer"> Duplicate For Transporter</label>
									      </span>
									      <span role="button" class="checkbox" style="padding: 5px;">
									      	<input type="checkbox" class="chkcase" id="print_transport" value="supplier" />
									      	<label for="print_transport">Triplicate For Supplier</label>
									      </span>
									      <span role="button" class="checkbox" style="padding: 5px;">
									      	<input type="checkbox" class="chkcase" id="print_extra" value="extra_copy" />
									      	<label for="print_extra"> Extra Copy</label>
									      </span>
									      <span style="text-align: center; display: inline-block; width: 100%; margin-bottom: 15px;">
									      	<span class="btn transparent-btn" onclick="generateCustomDropdown();">Print</span>
									      	<span class="btn transparent-btn" onclick="closeCustomDropdown();">Close</span>
									      </span>
									    </div>
								  	</div>
							  	</div>
							</div>
						</div>
					</div>
				</div>
				<input type="hidden" id="inv_document_remarks" />
				<div id="inv_document_container"></div>
   			</div>
   			<div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
   		</div>
  	</div>
</div>

<div id="notesEditableContainer" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg" style="width: 80%;">
	    <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Notes</h4>
            </div>
            <div class="modal-body">
                <div id="notes_editor">
                	<b>{{invoice.notes}}</b>
				</div>
            </div>
            <div class="modal-footer">
            	<button  id="add_invoice_notes" type="button" class="btn btn-save" onclick="addInvoiceNote();">Add</button>
            	<button  id="cancel_invoice_notes" data-dismiss="modal" type="button" class="btn btn-cancel">Cancel</button>
            </div>
        </div>
    </div>
</div>
<div id="change_log_modal" class="modal" tabindex="-1" role="dialog" aria-labelledby="" aria-hidden="true">
   <div class="modal-dialog">
	    <div class="modal-content">
		    <div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Activity Log</h4>
	      	</div>
      		<div class="modal-body remove-padding">
		        <div id='loadingmessage_changelog_listing_ie' style="text-align:center">
						<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' width="100">
						<br /> Please wait...
			    </div>
        		<ul class="history-log-container timeline">

        		</ul>
		         <ul class="show-more-log" style="list-style: none; text-align: right; margin-right: 15px; cursor: pointer;">
			        <li onclick="shoMoreLogs(20,20)">Show More</li>
		        </ul>
      		</div>
      		<div class="modal-footer">
      			<a href="#" class="btn btn-cancel" data-dismiss="modal">Close</a>
      		</div>
    	</div>
  	</div>
</div>
{% include "modal-window/add_issue_to.html" %}
{% include "masters/add_project_modal.html" %}
{% include "masters/add_material_modal.html" %}
{% include "modal-window/eInvoice-generator.html" %}
{% include "modal-window/quality-inspection.html" %}
<div class="hide">
	<form id="id-edit_receipt_form" target="_blank" method="POST" action="/erp/stores/grn/">{% csrf_token %}
		<input type="hidden" name="receipt_no" id="id-edit_receipt_no" value="" />
	</form>
</div>
<div class="hide">
	<form id="id-edit_invoice_form" target="_blank" method="post" action="/erp/sales/invoice/">
		{% csrf_token %}
		<input type="hidden" value="" id="id-edit_invoice_no" name="invoice_no">
		<input type="hidden" value="" id="id-edit_invoice_type" name="edit_dc_type">
	</form>
</div>
<style type="text/css">
	.history-log-part {
		width: 100%;
	    margin: 0;
	    padding: 8px 15px;
	    cursor: pointer;
	    padding-left: 60px;
	}
	.history-log-part:last-child {
		border-bottom: none;
	}
	.history-log-part:hover {
		background: #e8f0fe;
	}
	.history-log-part:hover .history-log-date {
		font-weight: bold;
	}
	.history-log-date {
	   font-style: italic;
	}
	.history-log-username {
		font-size: 15px;
		padding-top: 3px;
		font-weight: bold;
	}

	.history-log-content {
		margin-top: 8px;
	}

	.history-log-content ul {
		padding: 0px 10px 0px 35px;
	}
	.history-log-content li {
		margin-bottom: 10px;
	}

	.history-log-content li:last-child {
		margin-bottom: 0;
	}

	.history-log-part.active .history-log-date,
	.history-log-part.active .history-log-username {
		color: #004195;
	}

	.history-log-part.active .history-log-date {
	    text-align: right;
		display: block;
	}

	.history-log-part .fa-chevron-down:before {
		content: "\f078";
		color: #999;
	}

	.history-log-part.active .fa-chevron-down:before {
		content: "\f077";
		color: #999;
	}

	#change_log_modal .modal-body {
		overflow: auto;
	}


	ul.timeline {
	    list-style-type: none;
	    position: relative;
		padding: 0px;
	}
	ul.timeline:before {
	    content: ' ';
	    background: #d4d9df;
	    display: inline-block;
	    position: absolute;
	    left: 29px;
	    width: 2px;
	    height: 94%;
	    z-index: 400;
        margin-top: 12px;
	}
	ul.timeline > li {

	}
	ul.timeline > li:before {
	    content: ' ';
	    background: white;
	    display: inline-block;
	    position: absolute;
	    border-radius: 50%;
	    border: 3px solid #209be1;
	    left: 23px;
	    width: 14px;
	    height: 14px;
	    z-index: 400;
	    margin-top: 4px;
	}
	ul.timeline > li:last-child:before {
		background: #209be1;
	}
	body {
		overflow-y: scroll;
	}
</style>
<script type="text/javascript" src="/site_media/plugin/ck_editor/js/ckeditor.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/plugin/ck_editor/js/ckeditor_other.js?v={{ current_version }}"></script>
<script type="text/javascript">
$( document ).ready(function() {
	if ($("#id_dc_type").val() == "internal"){
	 	$(".td_issue").addClass('hide');
		$(".invoice_item_table").addClass('hide');
	}
	if($("#id_dc_type").val() == "dc"){
		$(".td_to-return").removeClass('hide');
	}
	$("#id_invoice-deliver_to").prop("readonly", true);
	$("#id_invoice-ship_to_name").prop("readonly", true);
	$("#id_invoice-gstin").prop("readonly", true);
    $('#delivery_editable').click(function(){
         if ( $('#id_invoice-deliver_to').is('[readonly]') ) {
            $("#id_invoice-deliver_to").prop("readonly", false);
			$("#id_invoice-gstin").prop("readonly", false);
			$("#id_invoice-ship_to_name").prop("readonly", false);
         }
    });

	if($("#invoice_materials_table tbody.item-for-goods tr").length >= 1){
        $(".item-for-goods").removeClass('hide');
	}
	const text = $("#id_invoice-job_po_id option:selected").text();
	if (text.includes('PP')) {
		if (localStorage.getItem("PPStockDetails")) {
			var PPStockDetails = JSON.parse(localStorage.getItem("PPStockDetails"));
			var chilld_items = PPStockDetails.child_items;
			$("#invoice_materials_table tbody.item-for-goods tr").each(function() {
				var item_id = $(this).find('td input.item_id').val();
				var qty = $(this).find('td span.td_inv_qty div input').val();
				if (item_id in chilld_items) {
					var issue_qty = chilld_items[item_id]['issue_qty'];
					var pro_qty = issue_qty - qty
					if (pro_qty >= 0){
						chilld_items[item_id]['issue_qty'] = pro_qty
						PPStockDetails.child_items = chilld_items
					}
				}
			});
			localStorage.setItem("PPStockDetails",JSON.stringify(PPStockDetails));
		}
	}
	else {
		if (localStorage.getItem("MRSStockDetails")) {
			var MRSStockDetails = JSON.parse(localStorage.getItem("MRSStockDetails"));
			$("#invoice_materials_table tbody.item-for-goods tr").each(function() {
				var item_id = $(this).find('td input.item_id').val();
				var qty = $(this).find('td span.td_inv_qty div input').val();
				if (MRSStockDetails.length > 0) {
					const matchingObject = MRSStockDetails.filter(obj => obj.cat_code == item_id);
					if(matchingObject.length >0){
					  const indexOfMatchingObject = MRSStockDetails.findIndex(obj => obj.cat_code == item_id);
					  var issue_qty = matchingObject[0].issue_qty;
					  var pro_qty = issue_qty - qty;
					  if (pro_qty >= 0) {
						MRSStockDetails[indexOfMatchingObject].issue_qty = pro_qty;
					  }
					}
				}
			});
			localStorage.setItem("MRSStockDetails",JSON.stringify(MRSStockDetails));
		}
	}



	 if($("#invoice_materials_table tbody.item-for-service tr").length >= 1){
        $(".item-for-service").removeClass('hide');
	}

	if($(".header_current_page").text().indexOf("New") >=0 || $(".header_current_page").length == 0) {
		$(".super_user_icon").addClass('hide');
		$(".super_edit_for_confirm, .super_edit_for_draft").addClass('hide');
	}
	else if($(".header_current_page").text().indexOf("PF") >=0) {
			$(".super_user_icon").addClass('hide');
			$(".super_edit_for_confirm, .super_edit_for_draft").removeClass('hide');
	}
	else{
		$(".super_user_icon").removeClass('hide');
		$(".super_edit_for_confirm, .super_edit_for_draft").removeClass('hide');
	}
    if ($("#remarks_list_json") != null) {
		displayRemarksHistory('remarks_list', $("#remarks_list_json").val(), "remarks_count")
	}
	$(".new-tour").removeClass('hide');
	var inv_type = $('#id_invoice-type').val()
	if($('#id_edit_data').val() != "" && inv_type != 'DC' && inv_type != 'JDC' && inv_type != 'JIN' && inv_type != 'Issue'){
		changeLogActivityInit();
	}
	tourInit();
	IssueDateTimePicker();
	downloadInvoiceCopy();
	printFormNameConfigInit();
	$("#id_search_submit").click(function(){
		$("#loading").show();
	});
	var editorElement = CKEDITOR.document.getById( 'id_invoice-notes' );
	editorElement.setAttribute( 'contenteditable', 'false' );
	CKEDITOR.inline( 'id_invoice-notes' );
	CKEDITOR.replace( 'notes_editor' );
	CKEDITOR.config.height = 300;
	var editorElement1 = CKEDITOR.document.getById( 'notes_editor' );
	var htmlText = $("#id_invoice-notes").text();
	editorElement1.setHtml(
		htmlText
	);
	$("#inv_document_container").html(getPdfLoadingImage());
	var url = window.location.href;
	if($("#id_invoice-id").val() =="" && $("#promote_inv_se_code").val() == "") {
		$("#id_invoice_mat-TOTAL_FORMS").val(0);
	}
	woForIssue();


	$("#switch_consignment a").on('click', function(){
		var selected = $(this).data('title');
		var toggle = $(this).data('toggle');
		$('#'+toggle).prop('value', selected);

		$("#switch_consignment a").removeClass("error-border").removeClass('active').addClass('noActive');
		$('#switch_consignment a[data-toggle="'+toggle+'"]').addClass('active');

		if(selected) {
			$("#id_invoice-is_courier").prop("checked", false);
			$(".consignment-container").addClass("hide");
		}
		else {
			$("#id_invoice-is_courier").prop("checked", true);
			$(".consignment-container").removeClass("hide");
		}
	});

	if($("#id_invoice-is_courier").is(":checked")) {
		$(".consignment-container").removeClass("hide");
		$("#switch_consignment a").removeClass("error-border").removeClass('active').addClass('noActive');
		$('#switch_consignment a[data-toggle="courier"]').addClass('active');
	}
	else {
		$(".consignment-container").addClass("hide");
		$("#switch_consignment a").removeClass("error-border").removeClass('active').addClass('noActive');
		$('#switch_consignment a[data-toggle="own_transport"]').addClass('active');
	}
});
function shoMoreLogs(offset, limit){
		if($("#change_log_modal").hasClass("change_log")) {
			var se_id = $("#id_invoice-id").val();
			$.ajax({
		        url: '/erp/sales/json/invoice/getinvoiceloglist/',
		        type: "POST",
		        dataType: "json",
		        data: {'se_id': se_id, offset: offset, limit: limit},
		        success: function (data) {
		        if (data['response_code'] != 400){
		            x = data['data'];
					var row = "";
					var i2 = offset;
					for(var i = 0; i < x.length; i++) {
					    var obj = x[i].log;
					    var row = `<li class="history-log-part" id="history-part-${i2}" onclick="loadHistoryContent('${data['data'][0].eid}', '${obj.modified_at}', ${i2}, '${obj.preference}')">
				                        <span class="history-log-username">${obj.username[0]+" "+obj.username[1]}</span>
				                        <div class="history-log-content" style="display: none;"></div>
				                        <span class="history-log-date">${moment(obj.modified_at).format("MMM DD, YYYY hh:mm:ss a")}</span>
				                    </li>`;

		                $(".history-log-container").append(row);
		                var displayedCount = $(".history-log-container").find("li").length;
		                i2++;
		                if(x.length < 20){
		                    $(".show-more-log").addClass("hide");
		                }
		                else {
		                    $(".show-more-log").find("li").attr("onclick", "shoMoreLogs("+displayedCount+", 20)");
	                    }
					}
				}
				else {
					$(".show-more-log").addClass("hide");
				}
		        },
		        error: function ($xhr,textStatus,errorThrown) {
			        console.log("ERROR : ", $xhr);
			        if ($xhr.responseText.indexOf("Your session has expired") != -1){
			            location.reload();
			        }
		        }
			});
		}
	}

$(window).load(function(){
	actualProjectsBudget($('#id_invoice-project_code').val(),$('#id_invoice-project_code').find(':selected').attr('project-type'));
    $('#id_invoice-project_code').change(function() {
            actualProjectsBudget($(this).val(),$('#id_invoice-project_code').find(':selected').attr('project-type'));
    });
    var project = JSON.parse(localStorage.getItem('project'));
    if(project && project.type == 'Secondary'){
		$('span[data-default-label="Invoice"]').text('Internal Invoice');
	}
	updateFilterText();
	autosize($(".auto-expandable"));
})
function changeLogActivityInit(){
		$("#id-change-log").removeClass("hide");
		$("#btn_change_log").click(function(){
			$("#change_log_modal").addClass("change_log").removeClass("change_preference_log");
			$("#change_log_modal").modal("show");
			$("#loadingmessage_changelog_listing_ie").show();
			$(".history-log-container").html("");
			var se_id = $("#id_invoice-id").val();
			$.ajax({
		        url: '/erp/sales/json/invoice/getinvoiceloglist/',
		        type: "POST",
		        dataType: "json",
		        data: {'se_id': se_id, 'offset': 0, 'limit': 20},
		        success: function (data) {
		        if (data['response_code'] != 400){
		            x = data['data'];
					var row = "";
					for(var i = 0; i < x.length; i++) {
					    var obj = x[i].log;
					    var row = `<li class="history-log-part" id="history-part-${i}" onclick="loadHistoryContent('${data['data'][0].inv_id}', '${obj.modified_at}', ${i})">
				                        <span class="history-log-username">${obj.username[0]+" "+obj.username[1]}</span>
				                        <div class="history-log-content" style="display: none;"></div>
				                        <span class="history-log-date">${moment(obj.modified_at).format("MMM DD, YYYY hh:mm:ss a")}</span>
				                    </li>`;
				        $("#loadingmessage_changelog_listing_ie").hide();
		                $(".history-log-container").append(row);
		                var displayedCount = $(".history-log-container").find("li").length;
					}
						if(x.length < 20){
		                    $(".show-more-log").addClass("hide");
		                }
		                else {
		                    $(".show-more-log").removeClass("hide");
		                    $(".show-more-log").find("li").attr("onclick", "shoMoreLogs("+displayedCount+", 20)");
	                    }
				}
				else {
				    var row = `<span style="width: 100%; padding: 15px; text-align:center; display: block;">No Activity Log has been captured.</span>`;
					$("#loadingmessage_changelog_listing_ie").hide();
		            $("#change_log_modal .modal-body").html(row);
				}
		        },
		        error: function ($xhr,textStatus,errorThrown) {
			        console.log("ERROR : ", $xhr);
			        if ($xhr.responseText.search("Session Expired!") != -1){
			            location.reload();
			        }
		        }
			});
		});

		$("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
}

function loadHistoryContent(se_id, modified_at, i) {
		var currentLogContent = $("#history-part-"+i).find('.history-log-content');
		if(currentLogContent.text() == "") {
			currentLogContent.html(`<img src="/site_media/images/loading_spinner.gif" style="width: 30px;"> Loading, Please wait...`).show();
			currentLogContent.closest(".history-log-part").addClass("active");
			setTimeout(function(){
				$.ajax({
			        url: '/erp/sales/json/invoice/getinvoicelogdata/',
			        type: "POST",
			        dataType: "json",
			        data: {"se_id":se_id, "modified_at": modified_at},
			        success: function (data) {
			        if (data['response_code'] != 400){
			        var row = '';
				        $.each( data['data'], function( key, value ) {
						  row += `<li>` + value + `</li>`;
						});
			        currentLogContent.html(`<ul>` + row + `</ul>`);
					currentLogContent.hide();
					currentLogContent.slideDown();
					}else{
					currentLogContent.html(`log data not available`);
					currentLogContent.hide();
					currentLogContent.slideDown();

					}
			        },
			        error: function () {
			        console.log('Changelog listing failed to load');

			        }
				});

			},100);
		}
		else {
			if(currentLogContent.find("img").length > 0) {
				return;
			}
			if(currentLogContent.is(":visible")) {
				currentLogContent.slideUp('fast', function() {
				   currentLogContent.closest(".history-log-part").removeClass("active");
				});

			}
			else {
				currentLogContent.slideDown();
				currentLogContent.closest(".history-log-part").addClass("active");
			}
		}
		$("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
}

function updateFilterText() {
	$(".filtered-date b").text($("#reportrange").find("span").text());
	$(".filtered-party b").text($("#id_party_name option:selected").text());
	$(".filtered-project b").text($("#id_project option:selected").text());
	$(".filtered-status b").text($("#id_status option:selected").text());
}
	function appendMaterial(cat_code, dataChild, dataP, alternate_unit_id, location_id){
		var dataParent = dataP;
		var currentCatChild = $("#"+cat_code + "_" + dataChild).attr('id');
	    var current_bom_sno = $("#"+dataParent).find(".bom-sno").text().trim();
	    var constructedRow = '';
		if($("#"+currentCatChild).attr('data-toggle') == 'open') {
			$("#loadingmessage_changelog_listing_ie1").hide();
			var curPadding = $("#"+currentCatChild).attr('data-padding');
			$("#"+currentCatChild).nextAll('tr').each(function(){
				if(Number($(this).attr('data-padding')) > Number(curPadding)) {
					$(this).remove();
				}
				else {
					return false;
				}
			});
			$("#cattable_2 #"+cat_code + "_" + dataChild).find('.fa.fa-for-arrow').removeClass('fa-minus').addClass('fa-plus');
			$("#cattable_2 #"+cat_code + "_" + dataChild).attr('data-toggle','close');
			$(`#${currentCatChild}`).find(".setUnitValue").removeAttr("readonly");
		}
		else {
		$("#loadingmessage_changelog_listing_ie1").show();
			$("#cattable_2 #"+cat_code + "_" + dataChild).find('.fa.fa-for-arrow').removeClass('fa-plus').addClass('fa-minus');
			$("#cattable_2 #"+cat_code + "_" + dataChild).attr('data-toggle','open');
			$(`#${currentCatChild}`).find(".setUnitValue").attr("readonly", "readonly");
			$.ajax({
				url: "/erp/stores/json/catalogue_materials/",
				type: "post",
				datatype: "json",
				data: {'cat_code': cat_code, 'alternate_unit_id': alternate_unit_id, 'location_id': location_id},
				success: function (response) {
					var ChildItems = {};
					if (localStorage.getItem('PPStockDetails') !== null) {
						var PPStockDetails = JSON.parse(localStorage.getItem("PPStockDetails"));
						ChildItems = PPStockDetails['child_items'];
					}
					var catqty = $("#id_ind_material-__prefix__-quantity").val();
					$.each(response, function (i, item) {
					var ppQty = 0;
					var issueQty = 0;
					if (item.item_id in ChildItems){
						ppQty = ChildItems[item.item_id].po_qty;
						issueQty =ChildItems[item.item_id].issue_qty;

					}
					if(typeof(Storage) !== "undefined") {
					    if (sessionStorage.clickcount) {
					        sessionStorage.clickcount = Number(sessionStorage.clickcount) + 1;
				        } else {
					        sessionStorage.clickcount = 1;
				        }
				    }
					var childArrow = "";
					dataP = item.item_id+ "_" + sessionStorage.clickcount
					var dataPadding = Number($("#"+cat_code + "_" + dataChild).data('padding') + 30);
					var materialName = item.name;
					if(item.drawing_no != "" && item.drawing_no !=null) {
						materialName = `${materialName} - ${item.drawing_no}`;
					}
					if(item.make_name != "") {
						materialName = `${materialName} [${item.make_name}]`;
					}

					if(item.hasChildren) {
						childArrow = `<i class='fa fa-plus fa-for-arrow' role='button' onclick='return appendMaterial("${item.item_id}", "${sessionStorage.clickcount}", "${dataP}", "0", "${location_id}")'>
										<a style='padding-left: 26px; display: block; margin-top: -12px;' >
											${materialName}
										</a>
									</i>`;
					} else {
						childArrow = `<i></i>${materialName}`;
					}
					stock_qty = item.stock_qty;
					var makes = "-NA-";
					if (item.makes.length > 0) {
						makes = "<select class='form-control set-make-qty parent_bom_delete'  name='make' id='txtmake" + i + "'>";
						for (j = 0; j <= item.makes.length - 1 ; j++) {
							if (j == 0) {
								stock_qty = item.makes[j].stock;
							}
							makes = `${makes}<option value="${item.makes[j].make_id}" data-quantity="${item.makes[j].stock}">${item.makes[j].make_name}</option>`;
						}
						makes += "</select>";
						// childArrow += ` - [ ${makes} ]`;
					}
				    var hsn_code = `<input id="id_catmaterial_hsn_code_${dataChild}_${i}" maxlength="9" type="text"  class="form-control text-left" name="catmaterial_hsn_code" autocomplete="off" onchange="validateHsnWithSuggestion(this, 'id_catmaterial_qty_${dataChild}_${i}')" onkeypress="validateStringOnKeyPress(this, event, 'hsn_specialChar');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" value="${item.tariff_no}">`;
                    var qty = `<input type="text" id="id_catmaterial_qty_${dataChild}_${i}" name="catmaterial_qty" class="form-control text-right bom_po_qty bom_amt_calc mandatory_field setUnitValue" maxlength="16" onchange="validateHsnWithSuggestion($('#id_catmaterial_hsn_code_${dataChild}_${i}'), 'id_catmaterial_qty_${dataChild}_${i}')" onblur="checkQtyChild('id_catmaterial_qty_${dataChild}_${i}', '${item.item_id}');" onfocus="setNumberRangeOnFocus(this,12,3);removeErrorBorder('id_catmaterial_qty_${dataChild}_${i}');" data-unitvalue="${item.quantity}" value="${item.quantity}" autocomplete="off">`;
                    var item_id = '<input name="catmaterial_item_id" type="text" value="'+ item.item_id +'" >';
                    var alternate_unit_id = '<input id="id_catmaterial_-'+i+'-alternate_unit_id" type="text"  name="catmaterial_alternate_unit_id" value='+ item.alternate_unit_id +'>';
                    var scale_factor = '<input id="id_catmaterial_-'+i+'-scale_factor" type="text"  name="catmaterial_scale_factor" value='+ item.scale_factor+'>';
                    var material_type = '<input hidden="hidden" id="id_catmaterial_material_type_'+i+'" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="catmaterial_material_type" value='+item.material_type+' >';
                    var is_service = '<input hidden="hidden" id="id_catmaterial_is_service_'+i+'" type="text"  name="catmaterial_is_service" value='+item.is_service+' >';
                    var hiddenField = `{%if type = 'internal'%}hidden='hidden'{%endif%}`;
                    var hiddenQtySpan = (issueQty === 0 && ppQty === 0) ? "hidden='hidden'" : '';
                    var stockable = ""
                    if (item.material_type==1) {
                        stockable = 'stockable'
                    }
                    var bom_description = " ";
                    if(!item.material_type) {
                        bom_description = `<span class="non_stock-flag"></span>`;
                    }
					if(item.is_service == true && !item.material_type){
						bom_description = `<span class="service-item-flag"></span>`;
					}
                    var row = `	<tr data-toggle='close' data-padding='${dataPadding}' data-parent='${item.item_id}_${sessionStorage.clickcount}' data-child='${dataParent}' id="${item.item_id}_${sessionStorage.clickcount}">
                    				<td hidden=hidden>${item.drawing_no} - ${item.name}</td>
                    				<td class='text-left bom-sno'>${current_bom_sno}.${(i+1)}</td>
                    				<td class="td_item_description" style='padding-left:${dataPadding-20}px'><span style='padding-left:30px; display: block;' class='tree-view'>${childArrow} ${bom_description}</span></td>
                    				<td class='hide'>${item.name}</td>
                    				<td ${hiddenField} class='hsn-wrapper'>${hsn_code}</td>
                    				<td class='hide'></td>
                    				<td>${qty}
                    				 <span ${hiddenQtySpan} class='qty-label hide' style="background: rgba(0, 0, 0,0.1) !important;border-radius: 50px;padding: 4px 8px 2px;float: right;margin-top: 5px;font-weight: 500;">${Number(issueQty).toFixed(3)} / ${Number( ppQty).toFixed(3)}</span></td>
                    				<td class='stock_qty text-right stockable'>${stock_qty}</td>
                    				<td class='text-center'>${item.unit_name}</td>
                    				<td ${hiddenField}>
                    					<input type='text' id='txtcatunitrate' name='catmaterial_unitrate' class='form-control text-right bom_po_unit_rate bom_amt_calc {%if type != 'internal'%}mandatory_field{%endif%}' maxlength='16' onfocus="setNumberRangeOnFocus(this,10,5)" value='${item.price}'>
                					</td>
                					<td ${hiddenField}>
                        				<input type='text' id='txtcatdiscount' name='catmaterial_disc' class='form-control text-right bom_po_disc bom_amt_calc' maxlength='6' onfocus='setNumberRangeOnFocus(this,3,2)' onblur='validatePercentage(this, event)' value='0' />
                    				</td>
                    				<td ${hiddenField}>
                        				<input type='text' name='catmaterial_amount' onkeypress ='return validateFloatKeyPress(this,event);' value='0.00' class='form-control text-right textbox-as-label bom_po_total' readonly id='txtcatamount' maxlength='12'>
                    				</td>
                    				<td class='text-center'>
                        				<a href='#' onclick='deleteCatRow(this)'>
                        					<i class='fa fa-trash-o'></i>
                    					</a>
                					</td>
                					<td hidden=hidden>${item.drawing_no}</td>
                					<td hidden=hidden>${alternate_unit_id}</td>
                					<td hidden=hidden>${scale_factor}</td>
                					<td hidden=hidden>
                						<input type='text' name ='catmaterial_unit_id' value='${item.unit_id}'>
            						</td>
            						<td hidden=hidden>${item.name}</td>
            						<td hidden=hidden>${item_id}</td>
            						<td hidden=hidden>${material_type}</td>
            						<td hidden=hidden>${is_service}</td>
        						</tr>`;
					constructedRow = constructedRow+row
					});
					$('#cattable_2 #'+cat_code.replace(/\./g,'\\.') + "_" + dataChild).after(constructedRow);
					if($('#id_invoice-type').val() == "Issue"){
                		$(".qty-label").removeClass('hide');
            		}
					$("#loadingmessage_changelog_listing_ie1").hide();
					BOMTotalCalculation();
					AssignMakeQty();
					$("#"+currentCatChild).find('.setUnitValue').focus();
					setTimeout(function(){
						$("#"+currentCatChild).find('.setUnitValue').blur();
					},500);
					$(".setUnitValue").blur(function(){
						var setID = $(this).closest('tr').attr('id');
						var setValue = $(this).val();
						$('#cattable_2').find("tr[data-child=\""+setID+"\"]").each(function(){
							var setUnitValue = $(this).find('.setUnitValue').data('unitvalue');
							var QtyCheck = $(this).find('.setUnitValue').siblings('span.qty-label')[0].outerText.split("/");
							var isValidQty = !(Number(QtyCheck[1]) < 1 && Number(QtyCheck[0]) < 1);
							var newVal = isValidQty ? Math.min(setValue * setUnitValue, Number(QtyCheck[1]) - Number(QtyCheck[0])) : setValue * setUnitValue;
							$(this).find('.setUnitValue').val(Number(newVal));
						});
					});
				}
			});
		}
    }

    function AssignMakeQty() {
        $(".set-make-qty").change(function(){
            var currentVal = $(this).find("option:selected").data('quantity');
            $(this).closest('tr').find('td.stock_qty').text(currentVal);
        });
    }

    function BOMTotalCalculation() {
		$('.bom_amt_calc').blur(function(){
			var qty = Number($(this).closest('tr').find('.bom_po_qty').val());
			var rate = Number($(this).closest('tr').find('.bom_po_unit_rate').val());
			var disc = Number($(this).closest('tr').find('.bom_po_disc').val());
			var discAmt = ((rate * qty) * disc)/100;
			var total = (rate * qty) - discAmt;
			$(this).closest('tr').find('.bom_po_total').val(total);
		});
	}

	function deleteCatRow(currentRow) {
	    try {
	        if (window.confirm('Do you want delete this row?')) {
	            var table = document.getElementById("cattable_2");
	            var rowCount = table.rows.length;
	            for (var i = 0; i < rowCount; i++) {
	                var row = table.rows[i];
	                if (row == currentRow.parentNode.parentNode) {
	                    if (rowCount <= 1) {
	                        alert("Cannot delete all the rows.");
	                        break;
	                    }
	                    table.deleteRow(i);
	                    rowCount--;
	                    i--;
	                }
	            }
	        } else {
	        }
	    } catch (e) {
	        alert(e);
	    }
	}
	function deleteoaRow(currentRow) {
	    try {
	    	swal({
	            title: "Are you sure?",
	            text: "Do you want to delete this Item!",
	            type: "warning",
	            showCancelButton: true,
	            confirmButtonColor: "#209be1",
	            confirmButtonText: "Yes, delete it!",
	            closeOnConfirm: true
	        },
	        function(){
	            var table = document.getElementById("OAMaterialtable");
	            var rowCount = table.rows.length;
	            for (var i = 0; i < rowCount; i++) {
	                var row = table.rows[i];
	                if (row == currentRow.parentNode.parentNode) {
	                    if (rowCount <= 1) {
	                        swal("","Cannot delete all the rows.","warning");
	                        break;
	                    }
	                    table.deleteRow(i);
	                    rowCount--;
	                    i--;
	                }
	            }
	            isAllconsolidatedRowDeletedOA(currentRow);
	        });

	    } catch (e) {
	        alert(e);
	    }
	}

	function isAllconsolidatedRowDeletedOA(currentRow) {
		var consolidatedFor = $(currentRow).closest("tr").attr("data-row");
		if($("#OAMaterialtable ."+consolidatedFor).length <= 0) {
	        $("#OAMaterialtable tr[consolidated-for='"+consolidatedFor+"']").remove();
	    }
	    else {
	    	$("#OAMaterialtable").find(".consolidated_row").each(function(){
		        var consolidatedTxt = $(this).attr("consolidated-for");
		        var consolidatedLength = $("#OAMaterialtable").find("."+consolidatedTxt).length;
		        $(this).find(".consolidated_price_column").attr("data-rowspan", Number(consolidatedLength+1));
		        if($("#OAMaterialtable").find("."+consolidatedTxt).is(":visible")) {
		            $(this).find(".consolidated_price_column").attr("rowspan", Number(consolidatedLength+1));
		        }
		    })
	    }
	}

	function deleteoaGroupRow(currentRow) {
		swal({
	            title: "Are you sure?",
	            text: "Do you want to delete all consolidated Items!",
	            type: "warning",
	            showCancelButton: true,
	            confirmButtonColor: "#209be1",
	            confirmButtonText: "Yes, delete it!",
	            closeOnConfirm: true
	        },
	        function(){
	            var consolidatedFor = $(currentRow).closest("tr").attr("consolidated-for");
	            $("#OAMaterialtable").find("."+consolidatedFor).remove();
	            $(currentRow).closest("tr").remove();
	        });
	}

	$("#id_invoice-party_id").change(function () {
    	if ($("#id_invoice-party_id").val()=='add_new_party') {
            $(".error-border").removeClass('error-border');
            $(".custom-error-message").remove();
            $('#modalPartyDetails').modal('show');
        }
    });

	$("#id_invoice-type").change(function () {
         if($('#id_invoice-type option:selected').val() == "JDC"){
		    $("#div_oa_no_display").hide();
         }
         else{
            if ($("#id_invoice-se_id").val() == "") {
                $("#div_oa_no_display").show();
            }
         }
    });

    function setParty(value,text) {
        $('#id_invoice-party_id').append($('<option>', {
        value: value.split('[')[0],
        text: text
        }));
        $("#id_invoice-party_id").val(value.split('[')[0]).trigger("chosen:updated");
        return false;
   	}

   	var isFormChanged = false;
   	$(window).load(function(){
   		formChangeEvent();
   	});

   	function formChangeEvent() {
   		$("#invoice_add input, #invoice_add select, #invoice_add textarea").change(function(){
   			if(!$(this).closest("div").hasClass("non-form-element")){
	        	isFormChanged = true;
	        }
        })
        $(".delete_tag, #add_tag").click(function(){
        	isFormChanged = true;
        });
   	}

   	$(window).load(function(){
   		$("#loading").hide();
   		sortTable();
   		setTimeout(function(){
            assignSerialNumber();
        },100);
   	});

   	function assignSerialNumber() {
   		var i = 1;
   		$(".item-for-goods tr:visible, .item-for-service tr:visible").each(function(){
   			$(this).find(".s_no").text(i++);
   		})
   	}

   	function sortTable() {
 	 	var table, rows, switching, i, x, y, shouldSwitch;
	  	table = document.getElementById("invoice_materials_table");
	  	switching = true;
	  	while (switching) {
	    	switching = false;
	    	rows = table.rows;
	    	for (i = 3; i < (rows.length - 5); i++) {
	      		shouldSwitch = false;
	      		x = rows[i].getElementsByTagName("TD")[1].getAttribute("data-order");
	      		y = rows[i + 1].getElementsByTagName("TD")[1].getAttribute("data-order");
	      		if (Number(x) > Number(y)) {
	        		shouldSwitch = true;
	        		break;
	      		}
	    	}
	    	if (shouldSwitch) {
	      		rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
	      		switching = true;
	    	}
	  	}
	}

   	$(window).load(function(){
		InvoiceFieldSpilt();
		goodsSuppliedChange();
		InvoicePrevNextPaging();
		currencyChangeEvent("onload");
		var url = window.location.href;
	    if($("#id_invoice-id").val() !="") {
			if($("#id_dc_type").val() == "internal"){
	        	IssueSuperEditInit();
	        	if($('#id_invoice-type option:selected').val() == 'JIN'){
	        	    $('#div_order_no').removeClass('hide');
	        	    $(".oaGrn_text").text("GRN");
                    $("#OAMaterial .modal-title").text("View GRN Materials")
	        	}
	        }
	        else if($("#id_dc_type").val() == "sales" || $("#id_dc_type").val() == "dc") {
	        	InvoiceSuperEditInit();
	        }
	    }
	    else{
	    const sessionProject = localStorage.getItem("project");
	        if($("#id_dc_type").val() == "sales") {
	        if(sessionProject) {
				const project = JSON.parse(sessionProject);
				$(".page-title").text(project.type === "Primary" ? 'New Invoice' : 'New Internal Invoice');
			}
			}
			else if($("#id_dc_type").val() == "dc") {
			    $(".page-title").text('New Delivery Challan');
			}
			else if($("#id_dc_type").val() == "internal") {
			    $(".page-title").text('New Issue');
			}
			if($("#id_invoice-se_id").val() == "") {
                $("#id_invoice-party_id").trigger("change");
            }
		}

	    if($('#id_invoice-type option:selected').val() == "JDC" || $('#id_invoice-type option:selected').val() == "DC" || $('#id_invoice-type option:selected').val() == "JIN"){
	        $(".total_span_column").attr("colspan","8");
	    }else{
	        $(".total_span_column").attr("colspan","7");
	    }

	    if($('#id_invoice-type option:selected').val() == "JDC"){
	        $("#id_txt_po_no_div").hide();
	        $("#id_txt_po_id_div").show();
	    }
	    else {
	    	$("#id_txt_po_no_div").show();
			$("#id_txt_po_id_div").hide();
	    }

	    if($("#id_dc_type").val()) {
	    	setReturnStartDate();
	    }
	    if ($("#id_invoice-type").val()=='Issue' || $('#id_invoice-type option:selected').val() == "JDC"){
	        $("#div_oa_no_display").addClass('hide');
	    }
	});

	function InvoiceFieldSpilt() {
		$(".for_invoice, .for_dc, .for_issue").addClass('hide');
		if($("#id_dc_type").val().toLowerCase() == "sales") {
			$(".for_invoice").removeClass('hide');
		}
		else if($("#id_dc_type").val().toLowerCase() == "dc") {
			$(".for_dc").removeClass('hide');
			$(".item_particulars_chk").css({marginTop: "6px"});
		}
		else if($("#id_dc_type").val().toLowerCase() == "internal") {
			$(".for_issue").removeClass('hide');
		}
		$("#individual_item_description").addClass("hide");
	}
var issue_to_list = [];
var issue_for_list = [];
	function onReadyStatements() {
		setupNewIssueToPopup();
		setupNewIssueForPopup();
	}
	function setupNewIssueToPopup() {
		//-----------------// Add new "issue to"
		issue_to_list = [];
	    $("#id_invoice-issued_to").change(function () {
			if ($("#id_invoice-issued_to").val()=='add_new_issued_to') {
				$("#id_issued_to").val('');
				$('#add_issued_to').modal('show');

			}
		});


		$("#id_issued_to" ).keypress(function (e) {
	        if (e.which == 13) {
	            $('#add_issued_to_button').click();
	        return false;
	        }
		});
		//-----------------// Adding new option
	    $('#id_invoice-issued_to optgroup[label="All"] option').each(function () {
	        issue_to_list.push($(this).text().toLowerCase())
	    });
	    $("#id_issued_to").keyup( function() {
	        if(issue_to_list.indexOf($('#id_issued_to').val().toLowerCase()) > -1) {
	            $(".duplicate_issued_to").show();
	        }
	        else {
	            $(".duplicate_issued_to").hide();
	        }
	    });
	    $("#add_issued_to_button").click(function() {
	        $(".error-border").removeClass('error-border');
		    $(".custom-error-message").remove();

		    var ControlCollections = [
		        {
		            controltype: 'textbox',
		            controlid: 'id_issued_to',
		            isrequired: true,
		            errormsg: 'Issue to "Person name" is required.'
		        }
		    ];
		    var result = JSCustomValidator.JSvalidate(ControlCollections);
			if (result) {
		        if($('#id_issued_to').val() != "") {
		            if(issue_to_list.indexOf($('#id_issued_to').val().toLowerCase()) > -1) {
		                $(".duplicate_issued_to").show();
		            }
		            else {
		                $(".duplicate_issued_to").hide();
		                $("#id_invoice-issued_to").append(new Option($('#id_issued_to').val(), $('#id_issued_to').val()));
		                $("#id_invoice-issued_to").val($('#id_issued_to').val()).trigger("chosen:updated");
		                $('#add_issued_to').modal('hide');
		                $('#loading').hide();
		            }
		        }
	        }
	    });
	}

	function setupNewIssueForPopup() {
		//-----------------//Add new  and "issue for"
		issue_for_list = [];
		$("#id_invoice-issued_for").change(function () {
			if ($("#id_invoice-issued_for").val()=='add_new_issued_for') {
				$("#id_issued_for").val('');
				$('#add_issued_for').modal('show');
			}
		});

		$("#id_issued_for" ).keypress(function (e) {
	        if (e.which == 13) {
	            $('#add_issued_for').click();
	        return false;
	        }
		});
		//-----------------// Adding new option
	    $('#id_invoice-issued_for optgroup[label="All"] option').each(function () {
	        issue_for_list.push($(this).text().toLowerCase())
	    });
	    $("#id_issued_for").keyup( function() {
	    	$(".empty_issued_for").hide();
	    	$("#id_issued_for").css({border: "1px solid #ccc"});
	        if(issue_for_list.indexOf($('#id_issued_for').val().toLowerCase()) > -1) {
	            $(".duplicate_issued_for").show();
	        }
	        else {
	            $(".duplicate_issued_for").hide();
	        }
	    });
	     $("#add_issued_for_button").click(function() {
            $(".error-border").removeClass('error-border');
		    $(".custom-error-message").remove();

		    var ControlCollections = [
		        {
		            controltype: 'textbox',
		            controlid: 'id_issued_for',
		            isrequired: true,
		            errormsg: 'Issue for is required.'
		        }
		    ];
		    var result = JSCustomValidator.JSvalidate(ControlCollections);
	        if (result) {
		        if($('#id_issued_for').val() != "") {
		            if(issue_for_list.indexOf($('#id_issued_for').val().toLowerCase()) > -1) {
		                $(".duplicate_issued_for").show();
		            }
		            else {
		                $(".duplicate_issued_for").hide();
		                $("#id_invoice-issued_for").append(new Option($('#id_issued_for').val(), $('#id_issued_for').val()));
		                $("#id_invoice-issued_for").val($('#id_issued_for').val()).trigger("chosen:updated");
		                $('#add_issued_for').modal('hide');
		                $('#loading').hide();
		            }
		        }
	        }
	    });
	}

	$('#add_issued_to').on('hidden.bs.modal', function () {
        if($('#id_issued_to').val() == "") {
            $("#id_invoice-issued_to").val(0).trigger("chosen:updated");
        }
        else {
            $("#id_invoice-issued_to").val($('#id_issued_to').val()).trigger("chosen:updated");
        }
    });

    $('#add_issued_for').on('hidden.bs.modal', function () {
		if($('#id_issued_for').val() == "") {
			$("#id_invoice-issued_for").val(0).trigger("chosen:updated");
		}
		else {
			$("#id_invoice-issued_for").val($('#id_issued_for').val()).trigger("chosen:updated");
		}
	});

	function customDateChanged(ev) {
		UdateCustomDatePicker($(this).attr('id'));
		validateDcIssuePoDate();
		listTableHoverIconsInit('invoice_materials_table');
	}

	function setReturnStartDate() {
		setTimeout(function(){
			if($("#id_invoice-issued_on").val() != "") {
				$("#invoice_return_date").datepicker("setStartDate", new Date($("#id_invoice-issued_on").val()));
			}
			else {
				$("#invoice_return_date").datepicker("setStartDate", new Date());
			}
		},2500);
	}

	function validateDcIssuePoDate(){
		if($("#id_dc_type").val() == "dc" || $("#id_dc_type").val() == "sales") {
			if(new Date($("#invoice_po_date").val()) > new Date($("#invoice_issued_on_date").val())) {
				swal({
					title: "Do you want to Reset?",
					text: "Issue Date seems to be earlier than PO date! <br/><br/> Do you want to reset <b>ISSUED ON</b> date to today?",
					type: "warning",
					showCancelButton: true,
					confirmButtonColor: "#209be1",
					confirmButtonText: "Reset",
					cancelButtonText: "No",
					closeOnConfirm: true
				},
				function(inputValue){
					if(inputValue === true) {
						$(".datetimepicker-days tfoot").find(".today").click();
						setReturnStartDate();
						setTimeout(function(){
							customIssuedOnChanged();
						},250);
					}
				});
			}
		}
	}

	function InvoiceSuperEditInit(){
		if($("#is_super_user").val().toLowerCase() == 'true') {
			if($(".header_current_page").text().indexOf("New") >=0 || $(".header_current_page").length == 0) {
				$(".super_user_icon, .xsid_number_edit, .super_user_tool_tip").remove();
				$(".super_edit_for_confirm, .super_edit_for_draft").remove();
				$(".super_edit_for_load").removeClass("hide");
			}
			else if($(".header_current_page").text().indexOf("PF") >=0) {
				$(".super_user_icon, .xsid_number_edit, .super_user_tool_tip").remove();
				$(".super_edit_for_confirm").remove();
				$(".super_edit_for_load, .super_edit_for_draft").removeClass("hide");
			}
			else {
				$(".super_user_icon, .super_user_tool_tip").removeClass("hide");
				$(".super_edit_for_load, .super_edit_for_draft, .super_edit_for_confirm").removeClass("hide");
			}
			$('.super_user_tool_tip span').qtip({
			   content: {
			        text: "<ul class='ul_for_tool_tip'><li>Invoice number has to be unique for the Financial Year.</li>\
			        		   <li>Invoice number cannot be more than 30 characters long.</li>\
			        		   <li>On updating the Invoice number, kindly regenerate the Invoice PDF to reflect this change.</li></ul>",
			        title: 'Super-Edit'
			    }
			});
		}
	}

	function IssueSuperEditInit(){
		if($("#is_super_user").val().toLowerCase() == 'true') {
			if($(".header_current_page").text().indexOf("New") >=0 || $(".header_current_page").length == 0) {
				$(".super_user_icon, .xsid_number_edit, .super_user_tool_tip").remove();
				$(".super_edit_for_confirm, .super_edit_for_draft").remove();
				$(".super_edit_for_load").removeClass("hide");
			}
			else if($(".header_current_page").text().indexOf("PF") >=0) {
				$(".super_user_icon, .xsid_number_edit, .super_user_tool_tip").remove();
				$(".super_edit_for_confirm").remove();
				$(".super_edit_for_load, .super_edit_for_draft").removeClass("hide");
			}
			else {
<!--				$(".super_user_icon, .super_user_tool_tip").removeClass("hide");-->
<!--				$(".super_edit_for_load, .super_edit_for_draft, .super_edit_for_confirm").removeClass("hide");-->
			}
			if($("#id_edit_dc_type").val() == "internal") {var type = "Issue"; var typec = "I";}
			if($("#id_edit_dc_type").val() == "dc") {var type = "DC"; var typec = "D";}
			$('.super_user_tool_tip span').qtip({
			   content: {
			        text: "<ul class='ul_for_tool_tip'><li>Super-edit enables modifying the "+type+" Code subject to Duplication Check.</li>\
			        		   <li>Code format will be 'FY-FY/"+typec+"NNNNNNx', <br />eg. '18-19/"+typec+"000731b'.<br />\
			        		   FY details - 5 characters (max), <br />"+type+" Type - 1 characters, <br />Number - 6 digits (max), <br />Sub-number - 1 alphabet (max).</li>\
			        		   <li>Subsequent numbering of "+type+" will pick from the highest of the "+type+" Code, <br/> i.e., gaps will not be filled automatically.</li></ul>",

			        title: 'Super-Edit'
			    }
			});
		}
	}

	function EditIssueNumber() {
		var issueNumber = $(".header_current_page").text().trim();
		var issueNumberSplit = issueNumber.split("/");
		$("#inv_financial_year").val(issueNumberSplit[0]);
		if($("#id_dc_type").val() == "internal") {
			$("#inv_type").val(issueNumberSplit[1].charAt(0));
		}
		if($("#id_edit_dc_type").val() == "dc") {
			$("#inv_type option[data-val="+issueNumberSplit[1].charAt(0)+"]").attr("selected", true)
		}
		if($.isNumeric(issueNumberSplit[1].substr(-1))){
			$("#inv_number").val(issueNumberSplit[1].slice(1));
		}
		else {
			$("#inv_number").val(issueNumberSplit[1].slice(1, -1));
			$("#inv_number_division").val(issueNumberSplit[1].substr(-1));
		}
		$(".xsid_number_edit").removeClass("hide");
		$(".super_user_icon, .header_current_page").addClass("hide")
	}

	function EditSerialNumber(editable){
		if (editable) {
			$(".serial_no_edit").removeClass("hide");
			$("#invoice_serial_number").removeClass("error-border");
			$(".edit-icon").addClass("hide");
		}
	}

	function EditInvoiceNumber(editable) {
		if (editable) {
			$(".xsid_number_edit").removeClass("hide");
			$(".super_user_icon, .header_current_page").addClass("hide");
			$(".serial_no_edit").addClass("hide");
			$.ajax({
				url: "/erp/sales/json/get_latest_invoice_serial_no/",
				method: "POST",
				data:{invoice_id: $("#id_invoice-id").val()},
				success: function(response) {
					if (response.response_message == "Success") {
						$("#id_inv_no").text(response.next_invoice_no);
					} else {
						swal({title: "", text: response.custom_message, type: "warning"});
					}
				},
				error: function (xhr, errmsg, err) {
		            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
		        }
			});
		}
	}

	function DiscardEditInvoiceNumber(){
		$(".super_edit_submit_icon").find(".super_edit_save").removeClass("div-disabled");
		$(".super_edit_submit_icon").find(".super_edit_cancel").removeClass("div-disabled");
		$(".edit-icon").removeClass("hide");
		$(".xsid_number_edit").addClass("hide");
		$("#number_format").val($(".header_current_page").text());
		$(".super_user_icon, .header_current_page").removeClass("hide");
		$("#invoice_serial_number").val($("#invoice_serial_number").attr('data-default-value'));
		$("#number_format").val($("#number_format").attr('data-default-value'));
		if ("Issue" == $("#id_invoice-type").val()) {
			$("#inv_financial_year, #inv_number, #inv_number_division").val("");
			$("#inv_type option").removeAttr("selected");
		}
	}

	function SaveInvoiceNumber() {
		$(".error-border").removeClass("error-border");
		$(".super_edit_submit_icon").find(".super_edit_save").addClass("div-disabled");
		$(".super_edit_submit_icon").find(".super_edit_cancel").addClass("div-disabled");
		if ("Issue" == $("#id_invoice-type").val()) {
			updateIssueNumber();
		} else {
			if($("#number_format").val().trim() == "" || $("#invoice_serial_number").val().trim() == ""){
				swal({title: "", text: "Invoice number/ Invoice serial number should not be empty", type: "error"});
				$(".super_edit_submit_icon").find(".super_edit_save").removeClass("div-disabled");
				$(".super_edit_submit_icon").find(".super_edit_cancel").removeClass("div-disabled");
			}
			else if(Number($("#invoice_serial_number").val()) < Number($("#invoice_serial_number").attr('data-default-value'))){
				swal("",`Next Serial number should not be less than current Serial number.<br /><br />Current Serial Number: ${$("#invoice_serial_number").attr('data-default-value')}`,"warning")
				$("#invoice_serial_number").addClass('error-border');
				$(".super_edit_submit_icon").find(".super_edit_save").removeClass("div-disabled");
				$(".super_edit_submit_icon").find(".super_edit_cancel").removeClass("div-disabled");
			}

			else{
				var url = "/erp/sales/json/get_invoice_linked_message/";
				if (["DC", "JDC", "JIN"].includes($("#id_invoice-type").val())) {
					url = "/erp/stores/json/get_dc_linked_message/";
				}
				$.ajax({
					url: url, method: "POST",
					data:{invoice_id: $("#id_invoice-id").val(), invoice_type: $("#id_invoice-type").val()},
					success: function(response) {
						if (response.response_message == "Success") {
							if(response.custom_message == "") {
								updateInvoiceNumber();
							} else {
								swal({title: "", text: response.custom_message, type: "warning",
									showCancelButton: true,
									confirmButtonColor: "#209be1",
									confirmButtonText: "Yes, do it!",
									closeOnConfirm: true,
									closeOnCancel: true},
									function(isConfirm) {
										if (isConfirm) {
											setTimeout(function(){updateInvoiceNumber();}, 200);
										}  else {
											DiscardEditInvoiceNumber();
										}
								});
							}
						} else {
							swal({title: "", text: response.custom_message, type: "error"});
							$(".super_edit_submit_icon").find(".super_edit_save").removeClass("div-disabled");
							$(".super_edit_submit_icon").find(".super_edit_cancel").removeClass("div-disabled");
						}
					},
					error: function (xhr, errmsg, err) {
						swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
						$(".super_edit_submit_icon").find(".super_edit_save").removeClass("div-disabled");
						$(".super_edit_submit_icon").find(".super_edit_cancel").removeClass("div-disabled");
					}
				});
			}
		}
	}

	function updateIssueNumber(){
		if($("#inv_financial_year").val() =="" || $("#inv_number").val() == "" || $("#inv_type").val() == "" || $("#inv_number").val() == 0) {
			$(".save_xsid_error_format").removeClass("hide");
			if($("#inv_financial_year").val() == "") $("#inv_financial_year").addClass("super_edit_error_border");
			if($("#inv_number").val() == "" || $("#inv_number").val() == 0) $("#inv_number").addClass("super_edit_error_border");
			if($("#inv_type").val() == "") $("#inv_type").addClass("super_edit_error_border");
			$(".super_edit_submit_icon").find(".super_edit_save").removeClass("div-disabled");
			$(".super_edit_submit_icon").find(".super_edit_cancel").removeClass("div-disabled");
		}
		else {
			$(".super_edit_submit_icon").find(".super_edit_save").addClass("div-disabled");
			$(".super_edit_submit_icon").find(".super_edit_cancel").addClass("div-disabled");
			$(".save_xsid_error_format").addClass("hide");
			var new_invoice_type = $("#id_invoice-type").val();
			if ("Issue" != new_invoice_type) {
				new_invoice_type = $("#inv_type").val();
			}
			$("#inv_number_division").val($("#inv_number_division").val().toLowerCase());
			$.ajax({
				url: "/erp/sales/json/super_edit_issue_code/",
				method: "POST",
				data:{
					invoice_id: $("#id_invoice-id").val(),
					new_financial_year: $("#inv_financial_year").val().trim(),
					new_invoice_type: new_invoice_type,
					new_invoice_no: $("#inv_number").val().trim(),
					new_sub_number: $("#inv_number_division").val().trim()
				},
				success: function(response) {
					if (response.response_message == "Success") {
						swal({
							title: "",
							text: response.custom_message,
							type: "success",
                			allowEscapeKey: false
                		},
						function(){
							DiscardEditInvoiceNumber();
							$(".header_current_page").text(response.code);
							$("#invoice_resubmit").click();
						});
						var event_labels = {'internal': 'Issue', 'dc': 'Delivery Challan', 'sales': 'Invoice'};
                        ga('send', 'event', event_labels[$("#id_edit_dc_type").val()], 'Super-Edit Code', $('#enterprise_label').val(), 1);
					} else {
						swal({title: "", text: response.custom_message, type: "warning"});
					}
					$(".super_edit_submit_icon").find(".super_edit_save").removeClass("div-disabled");
					$(".super_edit_submit_icon").find(".super_edit_cancel").removeClass("div-disabled");
				},
				error: function (xhr, errmsg, err) {
		            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
		            $(".super_edit_submit_icon").find(".super_edit_save").removeClass("div-disabled");
					$(".super_edit_submit_icon").find(".super_edit_cancel").removeClass("div-disabled");
		        }
			});
		}
	}

	function updateInvoiceNumber(){
		if($("#number_format").val() =="" || $("#invoice_serial_number").val() == "") {
			$(".save_xsid_error_format").removeClass("hide");
			$(".super_edit_submit_icon").find(".super_edit_save").removeClass("div-disabled");
			$(".super_edit_submit_icon").find(".super_edit_cancel").removeClass("div-disabled");
		}
		else {
			$(".super_edit_submit_icon").find(".super_edit_save").addClass("div-disabled");
			$(".super_edit_submit_icon").find(".super_edit_cancel").addClass("div-disabled");
			$(".save_xsid_error_format").addClass("hide");
			$.ajax({
				url: "/erp/sales/json/super_edit_invoice_code/",
				method: "POST",
				data:{
					invoice_id: $("#id_invoice-id").val(),
					new_invoice_code: $("#number_format").val().trim(),
					new_invoice_no: $("#invoice_serial_number").val().trim(),
				},
				success: function(response) {
					if (response.response_message == "Success") {
						$("#number_format").attr('data-default-value', $("#number_format").val().trim());
						$("#invoice_serial_number").attr('data-default-value', $("#invoice_serial_number").val().trim());
						swal({
							title: "",
							text: response.custom_message,
							type: "success",
                			allowEscapeKey: false
                		},
						function(){
							DiscardEditInvoiceNumber();
							$(".header_current_page").text(response.code);
							$("#invoice_resubmit").click();
						});
						var event_labels = {'internal': 'Issue', 'dc': 'Delivery Challan', 'sales': 'Invoice'};
                        ga('send', 'event', event_labels[$("#id_edit_dc_type").val()], 'Super-Edit Code', $('#enterprise_label').val(), 1);
					} else {
						swal({title: "", text: response.custom_message, type: "warning"});
					}
					$(".super_edit_submit_icon").find(".super_edit_save").removeClass("div-disabled");
					$(".super_edit_submit_icon").find(".super_edit_cancel").removeClass("div-disabled");
				},
				error: function (xhr, errmsg, err) {
		            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
		            $(".super_edit_submit_icon").find(".super_edit_save").removeClass("div-disabled");
					$(".super_edit_submit_icon").find(".super_edit_cancel").removeClass("div-disabled");
		        }
			});
		}
	}

	function SuperEditInvoiceSelect(field){
		$(field).closest("label").next("div").removeClass("div-disabled").find("select").removeAttr("readonly");
		$(field).hide();
	}

	var LoadSubTotal = 0;
	var LoadGrandTotal = 0;
	$(window).load(function(){
		LoadSubTotal = $("#invoice_tot").attr("value");
		LoadGrandTotal = $("#grand_total_value").attr("value") - $("#id_invoice-round_off").val();
	});

	function RequestInvTypeChange() {
		if($("#inv_type").val() == "BoS"){
			var subTotal = $("#invoice_tot").attr("value");
			var grandTotal = $("#grand_total_value").attr("value") - $("#id_invoice-round_off").val();
			if(Number(subTotal) != Number(grandTotal)) {
				swal({
					title: "Unable to change Type",
					text: "Bill of Supply should not have taxes. Please remove the taxes and Save/ Update the changes to change the type.",
					type: "warning",
					allowEscapeKey:false
				}, function(){
					DiscardEditInvoiceNumber();
				});
			}
			else if(Number(LoadSubTotal) != Number(LoadGrandTotal)) {
				swal({
					title: "Unable to change Type",
					text: "Please Save/ Update your changes to change the type.",
					type: "warning",
					allowEscapeKey:false
				}, function(){
					DiscardEditInvoiceNumber();
				});
			}
		}
	}

	function EnableGSTEdit(){
		$("#id_invoice-deliver_to, #id_invoice-gstin, #id_invoice-ship_to_name").removeAttr("readonly");
		$(".enable_edit_option").hide();
	}

	function IssueDateTimePicker() {
		var enterprise_next_closure_date = moment($("#enterprise_previous_closure_date").val()).add(1, 'days');
		if($("#enterprise_previous_closure_date").val() == undefined) enterprise_next_closure_date = "01-01-1900";
		if($("#id_invoice-issued_on").val() != ""){
			var sDate = $("#id_invoice-issued_on").val();
			if(sDate.length < 12) sDate = sDate+" 00:00:00";
		}
		else {
			var sDate = new Date();
		}

		if($("#id_invoice-issued_on").val() != ""){
			var setDate = moment(new Date(sDate)).format("MMM DD, YYYY HH:mm:ss");
			if(setDate != 'Invalid date') {
				$("#invoice_issued_on_date").val(setDate).attr("data-default-date", sDate);
			}
			else {
				setDate = moment(new Date()).format("MMM DD, YYYY HH:mm:ss");
				$("#invoice_issued_on_date").val(setDate).attr("data-default-date", sDate);
			}
		}
		setTimeout(function(){
			$("#invoice_issued_on_date").datetimepicker({
			    autoclose: true,
			    todayBtn: true,
			    minuteStep: 1,
			    todayHighlight: true,
			    format: "M d, yyyy hh:ii:ss",
		        linkField: "id_invoice-issued_on",
		        linkFormat: "yyyy-mm-dd hh:ii:ss",
		        startDate: new Date(enterprise_next_closure_date),
		        endDate: new Date(),
		        initialDate: sDate,
		        showMeridian: true
			}).change(function(){
				setReturnStartDate();
				showAllClosingStocks(1);
				validateDcIssuePoDate();
				clearMaterialAddContainer();
				customIssuedOnChanged();
			});
		},50);
	}

	function downloadInvoiceCopy(){
		$("#download_copy").click(function(){
			$("#download_copy_list").show();
		});
	}

	function closeCustomDropdown() {
		$("#download_copy_list input[type='checkbox']").prop("checked", false);
		$("#download_copy_list").hide();
	}

	function generateCustomDropdown(){
		document_header= []
		$(".custom-dropdown-menu").find("input").each(function(){
			if($(this).is(":checked")) {
				document_header.push($(this).val())
			}
		});
		generate_pdf_ajax($("#modal_inv_id").val(), $("#modal_inv_type").val(), $("#modal_invoice_status").val(), document_header, true)
	}

	function openNotesOverlay() {
		$("#notesEditableContainer").modal("show");
	}

	function addInvoiceNote() {
		var notes = CKEDITOR.instances.notes_editor.getData();
		$("#id_invoice-notes").html(notes);
		$("#id_invoice-notes").next("div").html(notes);
		$("#notesEditableContainer").modal("hide");
		isFormChanged = true;
	}

function tableHoverIconsInit(){
    $("#invoice_materials_table tbody tr").hover(function(){
        $("#invoice_materials_table tbody tr .hover-icon").addClass('hide');
        $(this).find(".hover-icon").toggleClass("hide");
    },
    function(){
        $("#invoice_materials_table tbody tr .hover-icon").addClass('hide');
    });
    TooltipInit();
}
function changelabel(){
	 if($("#template").is(":checked")) {
		$("[data-template-label]").each(function(){
   		var ss=  $(this).attr("{{inv_config_details.template_header_details.invoiceno_label}}");
    	 $(this).text(ss)
});
}
}
var tour = new Tour({
	    storage : false,
	    backdrop: true,
	    steps: [
	        {
				element: ".tour_edit_e-invoice",
				placement: "left",
				title:"GST eInvoice & IRN",
				content: "Now, you can directly generate GST eInvoices & IRNs here.",
			},
		],
});

function tourInit(){
	$('#int-tour').click(function() {
	    tour.restart();
    });
}


function printFormNameConfigInit() {
	if ($("#id_dc_type").val() != "internal"){
		$("#show_template_label").removeClass('hide');
		$("#show_template_label").click(function(){
			$("#show_template_label").toggleClass('on');
			if($("#show_template_label").is(".on")){
				$("#show_template_label").attr("data-original-title", "Show Default Label")
				$("[data-default-label]").each(function() {
					var change_label_name=$(this).attr("data-template-label")
					$(this).text(change_label_name);
				});
			}
			else {
				$("#show_template_label").attr("data-original-title", "Show Invoice Template Label")
				$("[data-default-label]").each(function() {
					var change_label_name=$(this).attr("data-default-label")
					$(this).text(change_label_name);
				});
			}
			updateUserSettingsFlag();
		});
	}
}

function showItemRemarks(current) {
    $(current).addClass("hide");
    $(current).prev(".item_remarks_section").removeClass("hide");
}
function checkQtyChild(current, qtyId)
    {
		var ChildItems = {};
		if (localStorage.getItem('PPStockDetails') !== null) {
			var PPStockDetails = JSON.parse(localStorage.getItem("PPStockDetails"));
			ChildItems = PPStockDetails['child_items'];
		}
		if ( qtyId in ChildItems){
			po_qty = ChildItems[qtyId].po_qty;
			issue_qty = ChildItems[qtyId].issue_qty;
			if($("#"+current).val().trim() > (po_qty - issue_qty))
			{
				swal({title: "", text: "Issue Quantity should not be greater than PP Qty", type: "error"});
				$("#"+current).addClass("error-border");
			}

		}

    }
	function removeErrorBorder(current)
	{
	if ($("#"+current).hasClass("error-border")) {
	$("#"+current).removeClass("error-border");
	}
}
</script>
{% endblock %}
