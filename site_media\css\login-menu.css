﻿.main__menu {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 10;
  -webkit-transform: translate(0, 50px);
  -moz-transform: translate(0, 50px);
  -ms-transform: translate(0, 50px);
  -o-transform: translate(0, 50px);
  transform: translate(0, 50px);
  -webkit-transition: all 0.5s,-webkit-transform .7s ease;
  -moz-transition: all 0.5s,-moz-transform .7s ease;
  transition: all 0.5s,transform .7s ease;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.main__menu--static {
  position: static;
  left: 0;
  top: 0;
}

.main__menu--scroll {
  background-color: #2F2E2F;
  box-shadow: 1px 2px 5px rgba(0,0,0,0.2);
  -webkit-transform: translate(0, 0);
  -moz-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  -o-transform: translate(0, 0);
  transform: translate(0, 0);
}

.main__menu--scroll .main__menu__logo img,.main__menu--scroll .main-logo img {
  display: none !important;
}

.main__menu--scroll .main__menu__logo img.alt__logo,.main__menu--scroll .main-logo img.alt__logo {
  display: block !important;
}

.main__menu--boxed .main__menu__inner__wrap {
  max-width: 1170px;
}

.main__menu--full__width .main__menu__inner__wrap {
  max-width: 100%;
}

.main__menu__inner__wrap {
  position: relative;
  padding: 0 15px;
  margin: 0 auto;
  display: -webkit-box;
  display: -moz-box;
  display: box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -moz-box-pack: end;
  box-pack: end;
  -webkit-justify-content: flex-end;
  -moz-justify-content: flex-end;
  -ms-justify-content: flex-end;
  -o-justify-content: flex-end;
  justify-content: flex-end;
  -ms-flex-pack: end;
  -webkit-box-align: center;
  -moz-box-align: center;
  box-align: center;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  -o-align-items: center;
  align-items: center;
  -ms-flex-align: center;
}

.main__menu__wrap {
  margin: 0 0 0 auto;
  display: -webkit-box;
  display: -moz-box;
  display: box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -moz-box-pack: end;
  box-pack: end;
  -webkit-justify-content: flex-end;
  -moz-justify-content: flex-end;
  -ms-justify-content: flex-end;
  -o-justify-content: flex-end;
  justify-content: flex-end;
  -ms-flex-pack: end;
  -webkit-box-align: center;
  -moz-box-align: center;
  box-align: center;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  -o-align-items: center;
  align-items: center;
  -ms-flex-align: center;
  -webkit-flex-grow: 2;
  -moz-flex-grow: 2;
  flex-grow: 2;
  -ms-flex-positive: 2;
}

.main__menu .main__menu__logo {
  margin: 5px auto 5px 0;
}

.main__menu .main__menu__logo img {
  width: auto;
  height: 50px;
}

.main__menu .main__menu__logo img,.main__menu .main-logo img {
  display: block;
}

.main__menu .main__menu__logo img.alt__logo,.main__menu .main-logo img.alt__logo {
  display: none;
}

.main__menu .menu__item .select-wrapper {
  line-height: 65px;
  width: 65px;
  background: transparent;
  display: inline-block;
  vertical-align: middle;
}

.main__menu .menu__item .select-wrapper input {
  background: transparent;
  border: 1px solid;
  margin: 0;
  padding: 0;
  text-align: center;
  vertical-align: middle;
  display: inline-block;
  width: 50px;
}

.main__menu .menu__item .select-wrapper .caret {
  display: none;
}

.main__menu .menu__item .select-wrapper::before {
  content: " ";
  line-height: 65px;
  width: 0;
  vertical-align: middle;
  display: inline-block;
}

.main__menu .main__menu__navbar {
  padding: 0;
  margin: 0;
  list-style: none;
  background-color: #2F2E2F;
  -webkit-transition: 0.5s;
  -moz-transition: 0.5s;
  transition: 0.5s;
}

.main__menu .main__menu__navbar li {
  position: relative;
  float: left;
  color: #fff;
}

.main__menu .main__menu__navbar li>a:hover,.main__menu .main__menu__navbar li>a:focus {
  opacity: 0.6;
}

.main__menu .main__menu__navbar li a {
  position: relative;
  display: block;
  padding: 0 15px;
  color: inherit;
  font-size: 13px;
  font-weight: 300;
  line-height: 65px;
  letter-spacing: 1.3px;
  cursor: pointer;
  opacity: 1;
  will-change: opacity, color;
  -webkit-transition: opacity 0.5s,color 0.5s;
  -moz-transition: opacity 0.5s,color 0.5s;
  transition: opacity 0.5s,color 0.5s;
}

.main__menu .main__menu__navbar li a i {
  display: inline-block;
  font-size: 1.4em;
  margin: -3px 5px 6px 0;
  vertical-align: middle;
}

.main__menu .main__menu__navbar li a i.bi-carret-down {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 7px;
  width: 18px;
  height: 18px;
  margin: auto;
  line-height: 13px;
  text-align: center;
  vertical-align: middle;
  -webkit-transform-origin: center;
  -moz-transform-origin: center;
  -ms-transform-origin: center;
  -o-transform-origin: center;
  transform-origin: center;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  -o-transform: rotate(0deg);
  transform: rotate(0deg);
}

.main__menu .main__menu__navbar li a:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #EE3F54;
  opacity: 0;
  -webkit-transition: opacity 0.5s;
  -moz-transition: opacity 0.5s;
  transition: opacity 0.5s;
}

.main__menu .main__menu__navbar li.forit__narrow .dropdown__content li .no_link {
  font-weight: 300;
  padding-bottom: 10px;
}

.main__menu .main__menu__navbar .dropdown__content {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 999;
  min-width: 100px;
  padding-left: 0;
  opacity: 0;
  box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16),0 2px 10px 0 rgba(0,0,0,0.12);
  list-style: none;
  overflow: visible;
  white-space: nowrap;
  background-color: #EE3F54;
  text-align: left;
  border-radius: 4px;
  -webkit-transform-origin: top;
  -moz-transform-origin: top;
  -ms-transform-origin: top;
  -o-transform-origin: top;
  transform-origin: top;
  -webkit-transform: scaleY(0);
  -moz-transform: scaleY(0);
  -ms-transform: scaleY(0);
  -o-transform: scaleY(0);
  transform: scaleY(0);
  -webkit-transition: -webkit-transform .3s ease-in-out,opacity 0.5s;
  -moz-transition: -moz-transform .3s ease-in-out,opacity 0.5s;
  transition: transform .3s ease-in-out,opacity 0.5s;
}

.main__menu .main__menu__navbar .dropdown__content .dropdown__content {
  top: 0;
  left: 100%;
  -webkit-transform-origin: left;
  -moz-transform-origin: left;
  -ms-transform-origin: left;
  -o-transform-origin: left;
  transform-origin: left;
  -webkit-transform: scaleX(0);
  -moz-transform: scaleX(0);
  -ms-transform: scaleX(0);
  -o-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-transition: -webkit-transform .3s ease-in-out,opacity 0.5s;
  -moz-transition: -moz-transform .3s ease-in-out,opacity 0.5s;
  transition: transform .3s ease-in-out,opacity 0.5s;
}

.main__menu .main__menu__navbar .dropdown__content .dropdown__content.active {
  -webkit-transform: scaleX(1);
  -moz-transform: scaleX(1);
  -ms-transform: scaleX(1);
  -o-transform: scaleX(1);
  transform: scaleX(1);
}

.main__menu .main__menu__navbar .dropdown__content.active {
  opacity: 1 !important;
  -webkit-transform: scaleY(1);
  -moz-transform: scaleY(1);
  -ms-transform: scaleY(1);
  -o-transform: scaleY(1);
  transform: scaleY(1);
}

.main__menu .main__menu__navbar .dropdown__content li {
  float: none;
  color: #fff;
}

.main__menu .main__menu__navbar .dropdown__content li a {
  position: relative;
  display: block;
  padding: 10px 15px;
  padding-right: 50px;
  font-size: 13px;
  line-height: 22px;
  font-weight: 300;
  color: inherit;
  background-color: transparent;
}

.main__menu .main__menu__navbar .dropdown__content li a:before {
  height: 100%;
  width: 3px;
}

.main__menu .main__menu__navbar .dropdown__content li.theme__dropdown__arrow--left .dropdown__content {
  left: auto;
  right: 100%;
  -webkit-transform-origin: right;
  -moz-transform-origin: right;
  -ms-transform-origin: right;
  -o-transform-origin: right;
  transform-origin: right;
}

.main__menu .main__menu__navbar .dropdown__content .menu__badge {
  margin-left: 10px;
}

.main__menu .main__menu__navbar .dropdown__content .theme__widget h4 {
  display: block;
  padding: 10px 15px 15px;
  margin: 0;
  font-size: 13px;
  line-height: 1.4em;
  font-weight: 400;
  color: inherit;
  background-color: transparent;
  text-transform: uppercase;
}

.main__menu .main__menu__navbar .dropdown__content .theme__widget img {
  display: block;
  width: 100%;
  height: auto;
  margin: auto;
}

.main__menu .main__menu__navbar .dropdown__content i.bi-carret-down {
  display: block;
  top: 0;
  right: 15px;
  -webkit-transform: rotate(-90deg);
  -moz-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  -o-transform: rotate(-90deg);
  transform: rotate(-90deg);
}

.main__menu .main__menu__navbar .dropdown__content .hide_elem>a {
  display: none;
}

.main__menu .main__menu__navbar .dropdown__content .hide_elem>.dropdown__content li a {
  padding-left: 15px !important;
}

.main__menu .main__menu__navbar .dropdown__content .hide_elem>.dropdown__content .dropdown__content li a {
  padding-left: 30px !important;
}

.main__menu .main__menu__navbar .dropdown__content .no_link {
  padding-bottom: 15px;
  font-weight: 400;
  cursor: default;
}

.main__menu .main__menu__navbar .dropdown__content .no_link+.dropdown__content li a {
  padding-left: 15px !important;
}

.main__menu .main__menu__navbar .dropdown__content .no_link+.dropdown__content .dropdown__content li a {
  padding-left: 30px !important;
}

@media (min-width: 1025px) {
  .main__menu.--header__content__left .main__menu__inner__wrap {
    -webkit-box-orient: horizontal;
    -moz-box-orient: horizontal;
    box-orient: horizontal;
    -webkit-box-direction: reverse;
    -moz-box-direction: reverse;
    box-direction: reverse;
    -webkit-flex-direction: row-reverse;
    -moz-flex-direction: row-reverse;
    flex-direction: row-reverse;
    -ms-flex-direction: row-reverse;
  }

  .main__menu.--header__content__left .main__menu__inner__wrap .main__menu__wrap {
    -webkit-box-orient: horizontal;
    -moz-box-orient: horizontal;
    box-orient: horizontal;
    -webkit-box-direction: reverse;
    -moz-box-direction: reverse;
    box-direction: reverse;
    -webkit-flex-direction: row-reverse;
    -moz-flex-direction: row-reverse;
    flex-direction: row-reverse;
    -ms-flex-direction: row-reverse;
  }

  .main__menu.--menu__list__left .main__menu__inner__wrap .main__menu__wrap {
    justify-content: space-between;
  }

  .main__menu.--menu__list__left .main__menu__inner__wrap .main__menu__wrap .menu.menu__extra .menu__item__shop .submenu {
    left: 0;
    right: auto;
  }

  .main__menu.--menu__list__center .main__menu__inner__wrap .main__menu__wrap {
    justify-content: flex-end;
  }

  .main__menu.--menu__list__center .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar {
    margin: auto;
  }

  .main__menu.--menu__list__right .main__menu__inner__wrap .main__menu__wrap {
    justify-content: flex-end;
  }

  .main__menu.--menu__list__right .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar {
    margin: 0;
  }

  .main__menu.-normal__menu.main__menu--scroll .main__menu__navbar {
    background-color: transparent;
  }

  .main__menu.-normal__menu.main__menu--scroll .menu.menu__extra {
    background-color: transparent;
  }

  .main__menu.-center__logo__menu .main__menu__wrap {
    -webkit-box-pack: center !important;
    -moz-box-pack: center !important;
    box-pack: center !important;
    -webkit-justify-content: center !important;
    -moz-justify-content: center !important;
    -ms-justify-content: center !important;
    -o-justify-content: center !important;
    justify-content: center !important;
    -ms-flex-pack: center !important;
    max-width: 100%;
  }

  .main__menu.-center__logo__menu.main__menu--scroll .main__menu__navbar {
    background-color: transparent;
  }

  .main__menu.-center__logo__menu .main__menu__logo {
    display: none;
  }

  .main__menu.-center__logo__menu.--header__content__left .menu.menu__extra {
    right: auto;
    left: 15px;
  }

  .main__menu.-center__logo__menu.--header__content__left .menu.menu__extra .menu__item__shop .submenu {
    right: auto;
    left: 0;
  }

  .main__menu.-center__logo__menu .main__menu__navbar {
    display: -webkit-box;
    display: -moz-box;
    display: box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -moz-box-align: center;
    box-align: center;
    -webkit-align-items: center;
    -moz-align-items: center;
    -ms-align-items: center;
    -o-align-items: center;
    align-items: center;
    -ms-flex-align: center;
  }

  .main__menu.-center__logo__menu .menu.menu__extra {
    position: absolute;
    right: 15px;
    top: 0;
    bottom: 0;
    height: 65px;
    margin: auto;
    background-color: transparent !important;
  }

  .main__menu.-center__logo__menu .menu.menu__extra .menu__item__shop .submenu {
    right: 0;
    left: auto;
  }

  .main__menu.-aside__menu.main__menu {
    top: 0;
  }

  .main__menu.-aside__menu.main__menu .container-fluid {
    margin-right: 0;
    max-width: calc(1140px + ((100vw - 1140px)/2));
  }

  .main__menu.-aside__menu.main__menu .navbar-nav {
    position: absolute;
    top: 0;
    left: 100%;
    width: 270px;
    height: 100vh;
    padding: 0;
  }

  .main__menu.-aside__menu.main__menu .navbar-nav li {
    float: none;
    text-align: right;
  }

  .main__menu.-aside__menu.main__menu .navbar-nav li a {
    padding: 0 20px;
  }

  .main__menu.-aside__menu.main__menu .dropdown-menu {
    position: relative;
    float: none;
    border-radius: 0;
  }

  .main__menu.-aside__menu .open__menu__btn {
    display: block;
  }

  .main__menu.-aside__menu.main__menu {
    background-color: #2F2E2F;
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap {
    min-height: 50px;
    max-width: 100%;
    -webkit-box-pack: justify;
    -moz-box-pack: justify;
    box-pack: justify;
    -webkit-justify-content: space-between;
    -moz-justify-content: space-between;
    -ms-justify-content: space-between;
    -o-justify-content: space-between;
    justify-content: space-between;
    -ms-flex-pack: justify;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__logo {
    margin: 5px;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__logo img {
    max-height: 60px;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .open__menu__btn {
    position: relative;
    display: block;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap {
    position: absolute;
    top: 0;
    left: 100%;
    width: 250px;
    height: 100vh;
    overflow: auto;
    padding: 0;
    -webkit-box-pack: justify;
    -moz-box-pack: justify;
    box-pack: justify;
    -webkit-justify-content: space-between;
    -moz-justify-content: space-between;
    -ms-justify-content: space-between;
    -o-justify-content: space-between;
    justify-content: space-between;
    -ms-flex-pack: justify;
    -webkit-box-orient: vertical;
    -moz-box-orient: vertical;
    box-orient: vertical;
    -webkit-box-direction: normal;
    -moz-box-direction: normal;
    box-direction: normal;
    -webkit-flex-direction: column;
    -moz-flex-direction: column;
    flex-direction: column;
    -ms-flex-direction: column;
    -webkit-box-align: start;
    -moz-box-align: start;
    box-align: start;
    -webkit-align-items: flex-start;
    -moz-align-items: flex-start;
    -ms-align-items: flex-start;
    -o-align-items: flex-start;
    align-items: flex-start;
    -ms-flex-align: start;
    -webkit-flex-grow: 0;
    -moz-flex-grow: 0;
    flex-grow: 0;
    -ms-flex-positive: 0;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__logo {
    display: none;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar {
    width: 100%;
    background-color: transparent;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar>li a {
    line-height: 45px;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li {
    float: none;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li.main-logo {
    display: none;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li a {
    padding: 0 20px;
    white-space: normal;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li a:before {
    width: 3px;
    height: 100%;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li a.dropdown__menu {
    padding-right: 40px !important;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li a.dropdown__menu i.bi-carret-down {
    display: block;
    right: 15px;
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li .dropdown__content {
    position: relative !important;
    top: 0 !important;
    left: 0 !important;
    display: none;
    max-height: 100% !important;
    box-shadow: none;
    opacity: 1 !important;
    background-color: transparent;
    -webkit-transform: scaleY(1) !important;
    -moz-transform: scaleY(1) !important;
    -ms-transform: scaleY(1) !important;
    -o-transform: scaleY(1) !important;
    transform: scaleY(1) !important;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li .dropdown__content li a {
    padding: 7px 5px 7px 30px;
    line-height: 1.5em;
    color: inherit;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li .dropdown__content .dropdown__content a {
    padding-left: 50px;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li .dropdown__content .hide_elem>.dropdown__content {
    display: block !important;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li .dropdown__content .hide_elem>.dropdown__content li a {
    padding-left: 30px !important;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li .dropdown__content .no_link+.dropdown__content li a {
    padding-left: 50px !important;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap .menu.menu__extra {
    width: 100%;
    background-color: transparent !important;
    text-align: center;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap .menu.menu__extra .menu__item {
    display: inline-block;
    float: none;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap .menu.menu__extra .menu__item__shop {
    position: static;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap .menu.menu__extra .menu__item__shop>a {
    position: relative;
  }

  .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap .menu.menu__extra .menu__item__shop .submenu {
    position: absolute !important;
    bottom: 70px !important;
    width: 250px;
    top: auto;
    right: 25px;
  }
}

@media (min-width: 1025px) and (min-width: 1025px) {
  .main__menu.-aside__menu.--menu__list__fullscreen.main__menu--scroll {
    box-shadow: none;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .main__menu__wrap {
    transition: transform 0.2s ease-out, opacity 0.5s ease-out;
    display: -webkit-box;
    display: -moz-box;
    display: box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -moz-box-orient: vertical;
    box-orient: vertical;
    -webkit-box-direction: normal;
    -moz-box-direction: normal;
    box-direction: normal;
    -webkit-flex-direction: column;
    -moz-flex-direction: column;
    flex-direction: column;
    -ms-flex-direction: column;
    width: 100% !important;
    top: 60px !important;
    height: calc(100vh - 60px) !important;
    overflow: hidden;
    opacity: 0;
    -webkit-transform: scale(0.5);
    -moz-transform: scale(0.5);
    -ms-transform: scale(0.5);
    -o-transform: scale(0.5);
    transform: scale(0.5);
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .main__menu__wrap .menu.menu__extra .menu__item__shop>a:hover+.submenu {
    position: absolute !important;
    left: 100% !important;
    bottom: 0;
    width: 260px !important;
    max-height: none;
    height: auto;
    top: auto;
    right: auto !important;
    opacity: 1;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .main__menu__wrap .menu.menu__extra .menu__item__shop {
    position: relative !important;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .main__menu__navbar {
    -webkit-box-orient: vertical;
    -moz-box-orient: vertical;
    box-orient: vertical;
    -webkit-box-direction: normal;
    -moz-box-direction: normal;
    box-direction: normal;
    -webkit-flex-direction: column;
    -moz-flex-direction: column;
    flex-direction: column;
    -ms-flex-direction: column;
    display: -webkit-box;
    display: -moz-box;
    display: box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-lines: single;
    -moz-box-lines: single;
    box-lines: single;
    -webkit-flex-wrap: nowrap;
    -moz-flex-wrap: nowrap;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    margin: auto !important;
    -webkit-box-flex: auto;
    -moz-box-flex: auto;
    box-flex: auto;
    -webkit-flex: auto 0 1;
    -moz-flex: auto 0 1;
    -ms-flex: auto 0 1;
    flex: auto 0 1;
    overflow-y: auto;
    overflow-x: hidden;
    text-align: center;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .main__menu__navbar .depth-0:before {
    content: "";
    position: absolute;
    top: 0;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 10%;
    height: 1px;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .main__menu__navbar .depth-0:after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 10%;
    height: 1px;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .main__menu__navbar .depth-1:before {
    content: "";
    position: absolute;
    top: 0;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 20%;
    height: 1px;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .main__menu__navbar .depth-1:after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 20%;
    height: 1px;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .main__menu__navbar .depth-2:before {
    content: "";
    position: absolute;
    top: 0;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 30%;
    height: 1px;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .main__menu__navbar .depth-2:after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 30%;
    height: 1px;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .main__menu__navbar .depth-3:before {
    content: "";
    position: absolute;
    top: 0;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 40%;
    height: 1px;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .main__menu__navbar .depth-3:after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 40%;
    height: 1px;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .main__menu__navbar .depth-4:before {
    content: "";
    position: absolute;
    top: 0;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 50%;
    height: 1px;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .main__menu__navbar .depth-4:after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 50%;
    height: 1px;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .main__menu__navbar .depth-5:before {
    content: "";
    position: absolute;
    top: 0;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 60%;
    height: 1px;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .main__menu__navbar .depth-5:after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 60%;
    height: 1px;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .main__menu__navbar li a:before {
    visibility: hidden;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .main__menu__navbar .bi-carret-down {
    visibility: hidden;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .main__menu__navbar li .dropdown__content li a {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .menu__extra {
    text-align: left;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen .main__menu__wrap .main__menu__navbar li a.dropdown__menu,.main__menu.-aside__menu.--menu__list__fullscreen .main__menu__wrap .main__menu__navbar li .dropdown__content li.dropdown .no_link+.dropdown__content li a,.main__menu.-aside__menu.--menu__list__fullscreen .main__menu__navbar li.dropdown .dropdown__content .no_link+.dropdown__content li a,.main__menu.-aside__menu.--menu__list__fullscreen .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li a.dropdown__menu,.main__menu.-aside__menu.--menu__list__fullscreen .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li .dropdown__content .dropdown .dropdown__content a,.main__menu.-aside__menu.--menu__list__fullscreen .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li .dropdown__content li a {
    padding-right: 0 !important;
    padding-left: 0 !important;
    text-align: center;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen.-menu_is_open {
    -webkit-transform: translate(0, 0) !important;
    -moz-transform: translate(0, 0) !important;
    -ms-transform: translate(0, 0) !important;
    -o-transform: translate(0, 0) !important;
    transform: translate(0, 0) !important;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen.-menu_is_open .main__menu__wrap {
    margin: auto !important;
    -webkit-box-flex: auto;
    -moz-box-flex: auto;
    box-flex: auto;
    -webkit-flex: auto 0 1;
    -moz-flex: auto 0 1;
    -ms-flex: auto 0 1;
    flex: auto 0 1;
    transition: transform 0.2s ease-out, opacity 0.5s ease-out;
    opacity: 1;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
    left: 0;
    top: 60px;
    height: calc(100vh - 60px);
    overflow: hidden;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen.-menu_is_open .main__menu__wrap>li a:before {
    background-color: transparent;
  }

  .main__menu.-aside__menu.--menu__list__fullscreen.-menu_is_open .main__menu .main__menu__navbar li.current-menu-item>a {
    opacity: 0.6;
  }
}

@media (min-width: 1025px) {
  .main__menu.-normal__menu .main__menu__navbar>li.forit__wide,.main__menu.-center__logo__menu .main__menu__navbar>li.forit__wide,.main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide,.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide {
    position: static;
  }

  .main__menu.-normal__menu .main__menu__navbar>li.forit__wide i.bi-carret-down,.main__menu.-center__logo__menu .main__menu__navbar>li.forit__wide i.bi-carret-down,.main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide i.bi-carret-down,.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide i.bi-carret-down {
    display: none !important;
  }

  .main__menu.-normal__menu .main__menu__navbar>li.forit__wide.forit__count__childrens_1>.dropdown__content>li,.main__menu.-center__logo__menu .main__menu__navbar>li.forit__wide.forit__count__childrens_1>.dropdown__content>li,.main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide.forit__count__childrens_1>.dropdown__content>li,.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide.forit__count__childrens_1>.dropdown__content>li {
    width: 100%;
  }

  .main__menu.-normal__menu .main__menu__navbar>li.forit__wide.forit__count__childrens_2>.dropdown__content>li,.main__menu.-center__logo__menu .main__menu__navbar>li.forit__wide.forit__count__childrens_2>.dropdown__content>li,.main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide.forit__count__childrens_2>.dropdown__content>li,.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide.forit__count__childrens_2>.dropdown__content>li {
    width: 50%;
  }

  .main__menu.-normal__menu .main__menu__navbar>li.forit__wide.forit__count__childrens_3>.dropdown__content>li,.main__menu.-center__logo__menu .main__menu__navbar>li.forit__wide.forit__count__childrens_3>.dropdown__content>li,.main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide.forit__count__childrens_3>.dropdown__content>li,.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide.forit__count__childrens_3>.dropdown__content>li {
    width: 33.33333%;
  }

  .main__menu.-normal__menu .main__menu__navbar>li.forit__wide.forit__count__childrens_4>.dropdown__content>li,.main__menu.-center__logo__menu .main__menu__navbar>li.forit__wide.forit__count__childrens_4>.dropdown__content>li,.main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide.forit__count__childrens_4>.dropdown__content>li,.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide.forit__count__childrens_4>.dropdown__content>li {
    width: 25%;
  }

  .main__menu.-normal__menu .main__menu__navbar>li.forit__wide.forit__count__childrens_5>.dropdown__content>li,.main__menu.-center__logo__menu .main__menu__navbar>li.forit__wide.forit__count__childrens_5>.dropdown__content>li,.main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide.forit__count__childrens_5>.dropdown__content>li,.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide.forit__count__childrens_5>.dropdown__content>li {
    width: 20%;
  }

  .main__menu.-normal__menu .main__menu__navbar>li.forit__wide>.dropdown__content,.main__menu.-center__logo__menu .main__menu__navbar>li.forit__wide>.dropdown__content,.main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content,.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    left: 15px !important;
    width: calc(100% - 30px);
    padding: 10px 0;
  }

  .main__menu.-normal__menu .main__menu__navbar>li.forit__wide>.dropdown__content::after,.main__menu.-center__logo__menu .main__menu__navbar>li.forit__wide>.dropdown__content::after,.main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content::after,.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content::after {
    clear: both;
    content: "";
    display: table;
  }

  .main__menu.-normal__menu .main__menu__navbar>li.forit__wide>.dropdown__content>li,.main__menu.-center__logo__menu .main__menu__navbar>li.forit__wide>.dropdown__content>li,.main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content>li,.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content>li {
    display: block !important;
    width: 20%;
    float: left !important;
    clear: initial !important;
    border-left: 1px solid rgba(255,255,255,0.3);
  }

  .main__menu.-normal__menu .main__menu__navbar>li.forit__wide>.dropdown__content>li:nth-child(5n+1),.main__menu.-center__logo__menu .main__menu__navbar>li.forit__wide>.dropdown__content>li:nth-child(5n+1),.main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content>li:nth-child(5n+1),.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content>li:nth-child(5n+1) {
    border-left: none;
  }

  .main__menu.-normal__menu .main__menu__navbar>li.forit__wide>.dropdown__content>li>a.no_link,.main__menu.-center__logo__menu .main__menu__navbar>li.forit__wide>.dropdown__content>li>a.no_link,.main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content>li>a.no_link,.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content>li>a.no_link {
    padding-bottom: 20px;
  }

  .main__menu.-normal__menu .main__menu__navbar>li.forit__wide>.dropdown__content li a,.main__menu.-center__logo__menu .main__menu__navbar>li.forit__wide>.dropdown__content li a,.main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content li a,.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content li a {
    padding-right: 60px !important;
    white-space: normal;
  }

  .main__menu.-normal__menu .main__menu__navbar>li.forit__wide>.dropdown__content .menu__badge,.main__menu.-center__logo__menu .main__menu__navbar>li.forit__wide>.dropdown__content .menu__badge,.main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content .menu__badge,.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content .menu__badge {
    position: absolute;
    right: 10px;
    top: 8px;
  }

  .main__menu.-normal__menu .main__menu__navbar>li.forit__wide>.dropdown__content .dropdown__content,.main__menu.-center__logo__menu .main__menu__navbar>li.forit__wide>.dropdown__content .dropdown__content,.main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content .dropdown__content,.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content .dropdown__content {
    position: relative !important;
    left: 0 !important;
    opacity: 1 !important;
    box-shadow: none;
    background-color: transparent !important;
    -webkit-transform: scaleX(1) !important;
    -moz-transform: scaleX(1) !important;
    -ms-transform: scaleX(1) !important;
    -o-transform: scaleX(1) !important;
    transform: scaleX(1) !important;
  }

  .main__menu.-normal__menu .main__menu__navbar>li.forit__wide>.dropdown__content .dropdown__content li a,.main__menu.-center__logo__menu .main__menu__navbar>li.forit__wide>.dropdown__content .dropdown__content li a,.main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content .dropdown__content li a,.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content .dropdown__content li a {
    padding-left: 30px;
  }

  .main__menu.-normal__menu .main__menu__navbar>li.forit__wide>.dropdown__content .dropdown__content .dropdown__content li a,.main__menu.-center__logo__menu .main__menu__navbar>li.forit__wide>.dropdown__content .dropdown__content .dropdown__content li a,.main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content .dropdown__content .dropdown__content li a,.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content .dropdown__content .dropdown__content li a {
    padding-left: 45px;
  }

  .main__menu.-side__bar__menu__left,.main__menu.-side__bar__menu__right {
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    transform: translateY(0);
  }

  .main__menu.-side__bar__menu__left .main__menu__wrap.main__menu__inner .main__menu__logo img,.main__menu.-side__bar__menu__right .main__menu__wrap.main__menu__inner .main__menu__logo img {
    display: block !important;
  }

  .main__menu.-side__bar__menu__left.main__menu--boxed .main__menu__inner__wrap,.main__menu.-side__bar__menu__right.main__menu--boxed .main__menu__inner__wrap {
    max-width: 100%;
  }

  .main__menu.-side__bar__menu__left.--logo__left .main__menu__wrap .main__menu__logo,.main__menu.-side__bar__menu__right.--logo__left .main__menu__wrap .main__menu__logo {
    -webkit-align-self: flex-start;
    -moz-align-self: flex-start;
    align-self: flex-start;
    -ms-flex-item-align: start;
  }

  .main__menu.-side__bar__menu__left.--logo__center .main__menu__wrap .main__menu__logo,.main__menu.-side__bar__menu__right.--logo__center .main__menu__wrap .main__menu__logo {
    -webkit-align-self: center;
    -moz-align-self: center;
    align-self: center;
    -ms-flex-item-align: center;
  }

  .main__menu.-side__bar__menu__left.--logo__right .main__menu__wrap .main__menu__logo,.main__menu.-side__bar__menu__right.--logo__right .main__menu__wrap .main__menu__logo {
    -webkit-align-self: flex-end;
    -moz-align-self: flex-end;
    align-self: flex-end;
    -ms-flex-item-align: end;
  }

  .main__menu.-side__bar__menu__left.--header__content__left .main__menu__inner__wrap .main__menu__wrap,.main__menu.-side__bar__menu__right.--header__content__left .main__menu__inner__wrap .main__menu__wrap {
    -webkit-box-orient: vertical;
    -moz-box-orient: vertical;
    box-orient: vertical;
    -webkit-box-direction: normal;
    -moz-box-direction: normal;
    box-direction: normal;
    -webkit-flex-direction: column;
    -moz-flex-direction: column;
    flex-direction: column;
    -ms-flex-direction: column;
    -webkit-box-align: start;
    -moz-box-align: start;
    box-align: start;
    -webkit-align-items: flex-start;
    -moz-align-items: flex-start;
    -ms-align-items: flex-start;
    -o-align-items: flex-start;
    align-items: flex-start;
    -ms-flex-align: start;
  }

  .main__menu.-side__bar__menu__left.--header__content__center .main__menu__inner__wrap .main__menu__wrap,.main__menu.-side__bar__menu__right.--header__content__center .main__menu__inner__wrap .main__menu__wrap {
    -webkit-box-orient: vertical;
    -moz-box-orient: vertical;
    box-orient: vertical;
    -webkit-box-direction: normal;
    -moz-box-direction: normal;
    box-direction: normal;
    -webkit-flex-direction: column;
    -moz-flex-direction: column;
    flex-direction: column;
    -ms-flex-direction: column;
    -webkit-box-align: center;
    -moz-box-align: center;
    box-align: center;
    -webkit-align-items: center;
    -moz-align-items: center;
    -ms-align-items: center;
    -o-align-items: center;
    align-items: center;
    -ms-flex-align: center;
  }

  .main__menu.-side__bar__menu__left.--header__content__right .main__menu__inner__wrap .main__menu__wrap,.main__menu.-side__bar__menu__right.--header__content__right .main__menu__inner__wrap .main__menu__wrap {
    -webkit-box-orient: vertical;
    -moz-box-orient: vertical;
    box-orient: vertical;
    -webkit-box-direction: normal;
    -moz-box-direction: normal;
    box-direction: normal;
    -webkit-flex-direction: column;
    -moz-flex-direction: column;
    flex-direction: column;
    -ms-flex-direction: column;
    -webkit-box-align: end;
    -moz-box-align: end;
    box-align: end;
    -webkit-align-items: flex-end;
    -moz-align-items: flex-end;
    -ms-align-items: flex-end;
    -o-align-items: flex-end;
    align-items: flex-end;
    -ms-flex-align: end;
  }

  .main__menu.-side__bar__menu__left.--menu__list__left .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar,.main__menu.-side__bar__menu__right.--menu__list__left .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar {
    margin: 0;
  }

  .main__menu.-side__bar__menu__left.--menu__list__left .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar>li>a,.main__menu.-side__bar__menu__right.--menu__list__left .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar>li>a {
    text-align: left;
  }

  .main__menu.-side__bar__menu__left.--menu__list__left.main__menu--expand__dropdown .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar,.main__menu.-side__bar__menu__right.--menu__list__left.main__menu--expand__dropdown .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar {
    margin: 0;
  }

  .main__menu.-side__bar__menu__left.--menu__list__left.main__menu--expand__dropdown .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li>a,.main__menu.-side__bar__menu__right.--menu__list__left.main__menu--expand__dropdown .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li>a {
    text-align: left;
  }

  .main__menu.-side__bar__menu__left.--menu__list__center .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar,.main__menu.-side__bar__menu__right.--menu__list__center .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar {
    margin: 0;
  }

  .main__menu.-side__bar__menu__left.--menu__list__center .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar>li>a,.main__menu.-side__bar__menu__right.--menu__list__center .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar>li>a {
    text-align: center;
  }

  .main__menu.-side__bar__menu__left.--menu__list__center.main__menu--expand__dropdown .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar,.main__menu.-side__bar__menu__right.--menu__list__center.main__menu--expand__dropdown .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar {
    margin: 0;
  }

  .main__menu.-side__bar__menu__left.--menu__list__center.main__menu--expand__dropdown .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li>a,.main__menu.-side__bar__menu__right.--menu__list__center.main__menu--expand__dropdown .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li>a {
    text-align: center;
  }

  .main__menu.-side__bar__menu__left.--menu__list__right .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar,.main__menu.-side__bar__menu__right.--menu__list__right .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar {
    margin: 0;
  }

  .main__menu.-side__bar__menu__left.--menu__list__right .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar>li>a,.main__menu.-side__bar__menu__right.--menu__list__right .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar>li>a {
    text-align: right;
  }

  .main__menu.-side__bar__menu__left.--menu__list__right.main__menu--expand__dropdown .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar,.main__menu.-side__bar__menu__right.--menu__list__right.main__menu--expand__dropdown .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar {
    margin: 0;
  }

  .main__menu.-side__bar__menu__left.--menu__list__right.main__menu--expand__dropdown .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li>a,.main__menu.-side__bar__menu__right.--menu__list__right.main__menu--expand__dropdown .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li>a {
    text-align: right;
  }

  .main__menu.-side__bar__menu__left.main__menu--expand__dropdown .main__menu__wrap,.main__menu.-side__bar__menu__right.main__menu--expand__dropdown .main__menu__wrap {
    overflow: auto;
  }

  .main__menu.-side__bar__menu__left.main__menu--expand__dropdown .main__menu__wrap .main__menu__navbar .dropdown__content,.main__menu.-side__bar__menu__right.main__menu--expand__dropdown .main__menu__wrap .main__menu__navbar .dropdown__content {
    position: relative !important;
    top: 0 !important;
    left: 0 !important;
    display: none;
    max-height: 100% !important;
    box-shadow: none;
    opacity: 1 !important;
    background-color: transparent;
    -webkit-transform: scaleY(1) !important;
    -moz-transform: scaleY(1) !important;
    -ms-transform: scaleY(1) !important;
    -o-transform: scaleY(1) !important;
    transform: scaleY(1) !important;
  }

  .main__menu.-side__bar__menu__left.main__menu--expand__dropdown .main__menu__wrap .main__menu__navbar .dropdown__content li a,.main__menu.-side__bar__menu__right.main__menu--expand__dropdown .main__menu__wrap .main__menu__navbar .dropdown__content li a {
    padding: 7px 30px 7px 30px;
    line-height: 1.5em;
    color: inherit;
  }

  .main__menu.-side__bar__menu__left.main__menu--expand__dropdown .main__menu__wrap .main__menu__navbar .dropdown__content i.bi-carret-down,.main__menu.-side__bar__menu__right.main__menu--expand__dropdown .main__menu__wrap .main__menu__navbar .dropdown__content i.bi-carret-down {
    right: 8px;
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  .main__menu.-side__bar__menu__left.main__menu--expand__dropdown .main__menu__wrap .main__menu__navbar .dropdown__content .dropdown__content a,.main__menu.-side__bar__menu__right.main__menu--expand__dropdown .main__menu__wrap .main__menu__navbar .dropdown__content .dropdown__content a {
    padding-left: 50px;
  }

  .main__menu.-side__bar__menu__left.main__menu--expand__dropdown .main__menu__wrap .main__menu__navbar .dropdown__content .hide_elem>.dropdown__content,.main__menu.-side__bar__menu__right.main__menu--expand__dropdown .main__menu__wrap .main__menu__navbar .dropdown__content .hide_elem>.dropdown__content {
    display: block !important;
  }

  .main__menu.-side__bar__menu__left.main__menu--expand__dropdown .main__menu__wrap .main__menu__navbar .dropdown__content .hide_elem>.dropdown__content li a,.main__menu.-side__bar__menu__right.main__menu--expand__dropdown .main__menu__wrap .main__menu__navbar .dropdown__content .hide_elem>.dropdown__content li a {
    padding-left: 30px !important;
  }

  .main__menu.-side__bar__menu__left.main__menu--expand__dropdown .main__menu__wrap .main__menu__navbar .dropdown__content .no_link+.dropdown__content li a,.main__menu.-side__bar__menu__right.main__menu--expand__dropdown .main__menu__wrap .main__menu__navbar .dropdown__content .no_link+.dropdown__content li a {
    padding-left: 50px !important;
  }

  .main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__wrap .main__menu__navbar>li.forit__wide,.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__wrap .main__menu__navbar>li.forit__wide {
    position: relative;
  }

  .main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__wrap .main__menu__navbar>li.forit__wide>.dropdown__content,.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__wrap .main__menu__navbar>li.forit__wide>.dropdown__content {
    width: calc(100vw - 250px);
  }

  .main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__wrap .main__menu__navbar>li.forit__wide>.dropdown__content>li,.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__wrap .main__menu__navbar>li.forit__wide>.dropdown__content>li {
    width: 25% !important;
  }

  .main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__wrap .main__menu__navbar>li.forit__wide>.dropdown__content>li:nth-child(4n+1),.main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__wrap .main__menu__navbar>li.forit__wide>.dropdown__content>li:nth-child(4n+1) {
    border-left: none;
  }

  .main__menu.-side__bar__menu__left .main__menu__inner__wrap>.main__menu__logo,.main__menu.-side__bar__menu__right .main__menu__inner__wrap>.main__menu__logo {
    display: none;
  }

  .main__menu.-side__bar__menu__left .main__menu__wrap,.main__menu.-side__bar__menu__right .main__menu__wrap {
    position: absolute;
    top: 0;
    width: 250px;
    height: 100vh;
    padding: 0;
    -webkit-box-orient: vertical;
    -moz-box-orient: vertical;
    box-orient: vertical;
    -webkit-box-direction: normal;
    -moz-box-direction: normal;
    box-direction: normal;
    -webkit-flex-direction: column;
    -moz-flex-direction: column;
    flex-direction: column;
    -ms-flex-direction: column;
    -webkit-box-pack: flex-start !important;
    -moz-box-pack: flex-start !important;
    box-pack: flex-start !important;
    -webkit-justify-content: flex-start !important;
    -moz-justify-content: flex-start !important;
    -ms-justify-content: flex-start !important;
    -o-justify-content: flex-start !important;
    justify-content: flex-start !important;
    -ms-flex-pack: flex-start !important;
    -webkit-box-align: start;
    -moz-box-align: start;
    box-align: start;
    -webkit-align-items: flex-start;
    -moz-align-items: flex-start;
    -ms-align-items: flex-start;
    -o-align-items: flex-start;
    align-items: flex-start;
    -ms-flex-align: start;
  }

  .main__menu.-side__bar__menu__left .main__menu__wrap .main__menu__logo,.main__menu.-side__bar__menu__right .main__menu__wrap .main__menu__logo {
    display: block;
    margin: 30px;
  }

  .main__menu.-side__bar__menu__left .main__menu__wrap .main__menu__navbar,.main__menu.-side__bar__menu__right .main__menu__wrap .main__menu__navbar {
    width: 100%;
    margin-top: 0;
    background-color: transparent;
  }

  .main__menu.-side__bar__menu__left .main__menu__wrap .main__menu__navbar>li>a,.main__menu.-side__bar__menu__right .main__menu__wrap .main__menu__navbar>li>a {
    padding-left: 30px;
    padding-right: 30px;
  }

  .main__menu.-side__bar__menu__left .main__menu__wrap .main__menu__navbar li,.main__menu.-side__bar__menu__right .main__menu__wrap .main__menu__navbar li {
    display: block;
    float: none;
  }

  .main__menu.-side__bar__menu__left .main__menu__wrap .main__menu__navbar li a,.main__menu.-side__bar__menu__right .main__menu__wrap .main__menu__navbar li a {
    white-space: normal;
  }

  .main__menu.-side__bar__menu__left .main__menu__wrap .main__menu__navbar li a:before,.main__menu.-side__bar__menu__right .main__menu__wrap .main__menu__navbar li a:before {
    height: 100%;
    width: 3px;
  }

  .main__menu.-side__bar__menu__left .main__menu__wrap .main__menu__navbar .dropdown__content,.main__menu.-side__bar__menu__right .main__menu__wrap .main__menu__navbar .dropdown__content {
    left: 100%;
    top: 0;
    width: 100%;
    -webkit-transform: scaleX(0);
    -moz-transform: scaleX(0);
    -ms-transform: scaleX(0);
    -o-transform: scaleX(0);
    transform: scaleX(0);
  }

  .main__menu.-side__bar__menu__left .main__menu__wrap .main__menu__navbar .dropdown__content.active,.main__menu.-side__bar__menu__right .main__menu__wrap .main__menu__navbar .dropdown__content.active {
    -webkit-transform: scaleX(1);
    -moz-transform: scaleX(1);
    -ms-transform: scaleX(1);
    -o-transform: scaleX(1);
    transform: scaleX(1);
  }

  .main__menu.-side__bar__menu__left .main__menu__wrap .main__menu__navbar .dropdown__content .hide_elem .dropdown__content,.main__menu.-side__bar__menu__right .main__menu__wrap .main__menu__navbar .dropdown__content .hide_elem .dropdown__content {
    left: 0;
    opacity: 1;
    -webkit-transform: scaleX(1);
    -moz-transform: scaleX(1);
    -ms-transform: scaleX(1);
    -o-transform: scaleX(1);
    transform: scaleX(1);
  }

  .main__menu.-side__bar__menu__left .main__menu__wrap .menu.menu__extra,.main__menu.-side__bar__menu__right .main__menu__wrap .menu.menu__extra {
    position: relative;
    width: 100%;
    margin: auto 0 0;
    text-align: center;
    background-color: transparent !important;
  }

  .main__menu.-side__bar__menu__left .main__menu__wrap .menu.menu__extra .menu__item,.main__menu.-side__bar__menu__right .main__menu__wrap .menu.menu__extra .menu__item {
    display: inline-block;
    float: none;
  }

  .main__menu.-side__bar__menu__left .main__menu__wrap .menu.menu__extra .menu__item__shop .submenu,.main__menu.-side__bar__menu__right .main__menu__wrap .menu.menu__extra .menu__item__shop .submenu {
    position: fixed;
    z-index: 9999;
    left: 0;
    right: auto;
    top: 0;
    width: 250px;
    -webkit-transform-origin: right;
    -moz-transform-origin: right;
    -ms-transform-origin: right;
    -o-transform-origin: right;
    transform-origin: right;
    position: absolute !important;
    bottom: 70px !important;
    width: 250px;
    top: auto !important;
  }

  .main__menu.-side__bar__menu__left .main__menu__inner__wrap .main__menu__wrap {
    left: 0;
  }

  .main__menu.-side__bar__menu__left .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar {
    left: 100%;
    right: auto;
  }

  .main__menu.-side__bar__menu__left .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar .dropdown__content {
    -webkit-transform-origin: left;
    -moz-transform-origin: left;
    -ms-transform-origin: left;
    -o-transform-origin: left;
    transform-origin: left;
  }

  .main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__wrap .main__menu__navbar>li.forit__wide>.dropdown__content {
    left: 100% !important;
    right: auto;
  }

  .main__menu.-side__bar__menu__right .main__menu__inner__wrap .main__menu__wrap {
    right: 0;
    left: auto;
  }

  .main__menu.-side__bar__menu__right .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar .dropdown__content {
    left: auto;
    right: 100%;
    -webkit-transform-origin: right;
    -moz-transform-origin: right;
    -ms-transform-origin: right;
    -o-transform-origin: right;
    transform-origin: right;
  }

  .main__menu.-side__bar__menu__right .main__menu__inner__wrap .main__menu__wrap .menu.menu__extra .menu__item__shop .submenu {
    position: fixed;
    left: auto;
    right: 0;
    top: 0;
    width: 250px;
    -webkit-transform-origin: right;
    -moz-transform-origin: right;
    -ms-transform-origin: right;
    -o-transform-origin: right;
    transform-origin: right;
  }

  .main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__wrap .main__menu__navbar>li.forit__wide>.dropdown__content {
    left: auto !important;
    right: 100%;
  }

  .main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__wrap .main__menu__navbar li:not(.forit__wide) .dropdown__content li a {
    padding-left: 40px;
    padding-right: 15px;
  }

  .main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__wrap .main__menu__navbar li:not(.forit__wide) .dropdown__content i.bi-carret-down {
    right: auto;
    left: 8px;
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg);
  }

  .main__menu.-full__menu {
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
  }

  .main__menu.-full__menu .main__menu__wrap {
    flex-direction: column;
  }

  .main__menu.-full__menu .main__menu__wrap .main__menu__navbar {
    position: absolute;
    top: 0;
    width: 100%;
    max-height: 80vh;
    text-align: center;
    opacity: 0;
    transition: opacity 0.5s 0.5s;
    overflow: auto;
    -webkit-transform: translateY(100vh);
    -moz-transform: translateY(100vh);
    -ms-transform: translateY(100vh);
    -o-transform: translateY(100vh);
    transform: translateY(100vh);
  }

  .main__menu.-full__menu .main__menu__wrap .main__menu__navbar li {
    display: block;
    float: none;
  }

  .main__menu.-full__menu .main__menu__wrap .main__menu__navbar li a {
    padding: 0 15px !important;
    white-space: normal;
  }

  .main__menu.-full__menu .main__menu__wrap .main__menu__navbar li a i.bi-carret-down {
    display: none !important;
  }

  .main__menu.-full__menu .main__menu__wrap .main__menu__navbar li a:before {
    display: none !important;
  }

  .main__menu.-full__menu .main__menu__wrap .main__menu__navbar li a:after {
    content: '';
    position: absolute;
    top: auto;
    bottom: 0;
    left: 0;
    right: 0;
    width: 30%;
    height: 3px;
    margin: auto;
    background-color: #EE3F54;
    opacity: 0;
    transition: opacity 0.5s;
  }

  .main__menu.-full__menu .main__menu__wrap .main__menu__navbar li a.active:after {
    opacity: 1;
  }

  .main__menu.-full__menu .main__menu__wrap .main__menu__navbar .dropdown__content {
    position: relative !important;
    opacity: 1;
    top: 100% !important;
    left: 0 !important;
    right: auto !important;
    display: none;
    width: 100%;
    background-color: transparent !important;
    -webkit-transform: scale(1) !important;
    -moz-transform: scale(1) !important;
    -ms-transform: scale(1) !important;
    -o-transform: scale(1) !important;
    transform: scale(1) !important;
  }

  .main__menu.-full__menu .main__menu__wrap .main__menu__navbar .dropdown__content .hide_elem .dropdown__content {
    left: 0;
    opacity: 1;
  }

  .main__menu.-full__menu .main__menu__wrap .main__menu__navbar .dropdown__content li a {
    padding: 15px !important;
  }

  .main__menu.-full__menu .main__menu__wrap .main__menu__navbar .dropdown__content li a:after {
    width: 50%;
  }

  .main__menu.-full__menu .main__menu__wrap .menu.menu__extra {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    margin: auto 0 0;
    text-align: center;
    -webkit-transform: translateY(100vh);
    -moz-transform: translateY(100vh);
    -ms-transform: translateY(100vh);
    -o-transform: translateY(100vh);
    transform: translateY(100vh);
    background-color: transparent !important;
  }

  .main__menu.-full__menu .main__menu__wrap .menu.menu__extra .menu__item {
    display: inline-block;
    float: none;
  }

  .main__menu.-full__menu .main__menu__wrap .menu.menu__extra .menu__item__shop .submenu {
    position: fixed;
    bottom: 65px;
    left: 0;
    right: auto;
    top: auto;
    width: 250px;
    -webkit-transform-origin: bottom;
    -moz-transform-origin: bottom;
    -ms-transform-origin: bottom;
    -o-transform-origin: bottom;
    transform-origin: bottom;
  }

  .main__menu.-full__menu .open__menu__btn {
    display: block;
  }

  .main__menu.-full__menu.-menu_is_open {
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    transform: translateY(0);
    width: 100vw;
    height: 100vh;
    background-color: #000;
  }

  .main__menu.-full__menu.-menu_is_open .main__menu__wrap {
    height: 100vh;
  }

  .main__menu.-full__menu.-menu_is_open .main__menu__wrap .main__menu__navbar {
    -webkit-transform: translateY(50px);
    -moz-transform: translateY(50px);
    -ms-transform: translateY(50px);
    -o-transform: translateY(50px);
    transform: translateY(50px);
    opacity: 1;
  }

  .main__menu.-full__menu.-menu_is_open .main__menu__logo {
    display: none;
  }

  .main__menu.-full__menu.-menu_is_open .open__menu__btn {
    right: 50%;
    transform: translateX(50%);
  }

  .main__menu.-full__menu.-menu_is_open .menu.menu__extra {
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
  }
}

.main__menu.-menu_is_open:not(.-full__menu) {
  -webkit-transform: translate(-250px, 0);
  -moz-transform: translate(-250px, 0);
  -ms-transform: translate(-250px, 0);
  -o-transform: translate(-250px, 0);
  transform: translate(-250px, 0);
}

.open__menu__btn--wrapper {
  display: -webkit-box;
  display: -moz-box;
  display: box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: auto;
  -moz-box-flex: auto;
  box-flex: auto;
  -webkit-flex: auto 1 0;
  -moz-flex: auto 1 0;
  -ms-flex: auto 1 0;
  flex: auto 1 0;
  -webkit-box-pack: end;
  -moz-box-pack: end;
  box-pack: end;
  -webkit-justify-content: flex-end;
  -moz-justify-content: flex-end;
  -ms-justify-content: flex-end;
  -o-justify-content: flex-end;
  justify-content: flex-end;
  -ms-flex-pack: end;
}

@media (min-width: 1025px) {
  .main__menu:not(.-aside__menu) .open__menu__btn--wrapper {
    display: none;
  }
}

.open__menu__btn {
  position: absolute;
  z-index: 999;
  display: none;
  padding: 15px;
  cursor: pointer;
}

.open__menu__btn span {
  position: relative;
  top: 0;
  display: block;
  width: 25px;
  height: 3px;
  margin-top: 4px;
  background-color: #fff;
  box-shadow: none;
  -webkit-transition: 0.5s;
  -moz-transition: 0.5s;
  transition: 0.5s;
}

.open__menu__btn span:first-child {
  margin: 0;
}

.open__menu__btn.-menu_is_open span {
  -webkit-transition: 0.5s;
  -moz-transition: 0.5s;
  transition: 0.5s;
}

.open__menu__btn.-menu_is_open span:nth-child(1) {
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
  top: 5px;
}

.open__menu__btn.-menu_is_open span:nth-child(2) {
  opacity: 0;
}

.open__menu__btn.-menu_is_open span:nth-child(3) {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
  top: -9px;
}

.main__menu .menu.menu__extra {
  list-style: none;
  padding: 0;
  margin: 0;
  background-color: #2F2E2F;
  -webkit-transition: background-color 0.5s;
  -moz-transition: background-color 0.5s;
  transition: background-color 0.5s;
}

.main__menu .menu.menu__extra::after {
  clear: both;
  content: "";
  display: table;
}

.main__menu .menu.menu__extra ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.main__menu .menu.menu__extra .menu__item {
  float: left;
  margin-bottom: 0;
}

.main__menu .menu.menu__extra .menu__item>div {
  position: relative;
  opacity: 1;
}

.main__menu .menu.menu__extra .menu__item>div>a {
  display: block;
  -webkit-transition: opacity 0.5s;
  -moz-transition: opacity 0.5s;
  transition: opacity 0.5s;
  opacity: 1;
}

.main__menu .menu.menu__extra .menu__item>div>a:hover {
  opacity: 0.6;
}

.main__menu .menu.menu__extra .menu__item>div>a i {
  color: #fff;
  font-size: 24px;
  line-height: 64px;
  -webkit-transition: color 0.5s;
  -moz-transition: color 0.5s;
  transition: color 0.5s;
}

.main__menu .menu.menu__extra .menu__item__wishlist {
  height: 65px;
  width: 65px;
  text-align: center;
}

.main__menu .menu.menu__extra .menu__item__wishlist>a {
  height: 65px;
  line-height: 65px;
}

.main__menu .menu.menu__extra .menu__item__wishlist>a span {
  display: none;
}

.main__menu .menu.menu__extra .menu__item__shop {
  width: 65px;
  text-align: center;
}

.main__menu .menu.menu__extra .menu__item__shop>a {
  height: 65px;
  line-height: 65px;
}

.main__menu .menu.menu__extra .menu__item__shop>a .shopping-cart-items-count {
  position: absolute;
  right: 10px;
  top: 10px;
  min-width: 15px;
  height: 15px;
  padding: 0 2px;
  line-height: 13px;
  background-color: transparent;
  color: #EE3F54;
  border-radius: 20px;
  border: 1px solid #fff;
  font-size: 9px;
  overflow: hidden;
  text-align: center;
}

.main__menu .menu.menu__extra .menu__item__shop>a .shopping-cart-items-count.no-items {
  display: none;
}

.main__menu .menu.menu__extra .menu__item__shop .submenu {
  position: absolute;
  top: 100%;
  right: 0;
  width: 260px;
  background-color: #fff;
  box-shadow: 0 0 1px 1px #eee inset;
  max-height: 0;
  opacity: 0;
  overflow: hidden;
  -webkit-transform-origin: top;
  -moz-transform-origin: top;
  -ms-transform-origin: top;
  -o-transform-origin: top;
  transform-origin: top;
  -webkit-transform: translateY(10px);
  -moz-transform: translateY(10px);
  -ms-transform: translateY(10px);
  -o-transform: translateY(10px);
  transform: translateY(10px);
  -webkit-transition: -webkit-transform .3s ease-in-out,opacity 0.5s,max-height 0s 0.5s;
  -moz-transition: -moz-transform .3s ease-in-out,opacity 0.5s,max-height 0s 0.5s;
  transition: transform .3s ease-in-out,opacity 0.5s,max-height 0s 0.5s;
}

.main__menu .menu.menu__extra .menu__item__shop .submenu .cart_list {
  padding-bottom: 20px;
  max-height: calc(100vh - 325px);
  overflow: hidden;
}

.main__menu .menu.menu__extra .menu__item__shop .submenu .cart_list li.empty {
  padding-top: 20px;
}

.main__menu .menu.menu__extra .menu__item__shop .submenu .mini_cart_item {
  position: relative;
  padding: 0 20px;
  margin: 20px 0;
  min-height: 60px;
  line-height: 1.4em;
  text-align: left;
}

.main__menu .menu.menu__extra .menu__item__shop .submenu .mini_cart_item:last-child {
  margin-bottom: 0;
}

.main__menu .menu.menu__extra .menu__item__shop .submenu .mini_cart_item .remove {
  position: absolute;
  right: 10px;
  top: 0;
  bottom: 0;
  z-index: 9;
  width: 30px;
  height: 30px;
  margin: auto;
  line-height: 30px;
  text-align: center;
  color: #EE3F54;
  -webkit-transition: opacity 0.5s;
  -moz-transition: opacity 0.5s;
  transition: opacity 0.5s;
}

.main__menu .menu.menu__extra .menu__item__shop .submenu .mini_cart_item .remove:hover {
  opacity: 0.6;
}

.main__menu .menu.menu__extra .menu__item__shop .submenu .mini_cart_item a:not(.remove) {
  position: relative;
  display: block;
  padding-left: 75px;
  margin-bottom: 5px;
  font-size: 14px;
  color: #000;
  font-weight: 300;
}

.main__menu .menu.menu__extra .menu__item__shop .submenu .mini_cart_item a:not(.remove) img {
  position: absolute;
  width: 60px;
  height: 60px;
  top: 0;
  left: 0;
}

.main__menu .menu.menu__extra .menu__item__shop .submenu .mini_cart_item .variation {
  padding-left: 75px;
}

.main__menu .menu.menu__extra .menu__item__shop .submenu .mini_cart_item .variation .forit__variation__row div {
  display: inline-block;
  margin: 0;
  vertical-align: middle;
  font-size: 10px;
  text-transform: uppercase;
  font-weight: 300;
}

.main__menu .menu.menu__extra .menu__item__shop .submenu .mini_cart_item .variation .forit__variation__row p {
  margin-bottom: 0;
}

.main__menu .menu.menu__extra .menu__item__shop .submenu .mini_cart_item .quantity {
  display: block;
  padding-left: 75px;
  margin-top: 5px;
  font-weight: 300;
  font-size: 14px;
  color: #EE3F54;
}

.main__menu .menu.menu__extra .menu__item__shop .submenu .total {
  text-align: left;
  margin: 0;
  padding: 10px 20px;
  font-size: 14px;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
  text-transform: uppercase;
}

.main__menu .menu.menu__extra .menu__item__shop .submenu .total span {
  float: right;
  color: #EE3F54;
}

.main__menu .menu.menu__extra .menu__item__shop .submenu .btn {
  margin-top: 30px;
  margin-bottom: 0;
}

.main__menu .menu.menu__extra .menu__item__shop .submenu .btn.checkout {
  margin-top: 15px;
  margin-bottom: 20px;
}

.main__menu .menu.menu__extra .menu__item__shop:hover .submenu {
  opacity: 1;
  max-height: 100vh;
  -webkit-transition: -webkit-transform .3s ease-in-out,opacity 0.5s,max-height 0s 0s;
  -moz-transition: -moz-transform .3s ease-in-out,opacity 0.5s,max-height 0s 0s;
  transition: transform .3s ease-in-out,opacity 0.5s,max-height 0s 0s;
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}

.main__menu .menu.menu__extra .menu__item__search {
  width: 65px;
  height: 65px;
  text-align: center;
}

.main__menu .menu.menu__extra .menu__item__search a {
  height: 65px;
  line-height: 65px;
}

.main__menu .menu.menu__extra .menu__item__search form {
  display: none;
}

.main__menu .menu.menu__extra .menu__item h4 {
  text-align: left;
  color: #fff;
}

.main__menu .menu.menu__extra .menu__item ul li.recentcomments {
  width: 220px;
  text-align: left;
  color: #fff;
}

.main__menu .menu.menu__extra .menu__item ul li.recentcomments a {
  color: inherit;
}

.main__menu .menu.menu__extra .menu__item .theme__recent__posts {
  width: 250px;
}

.main__menu .menu.menu__extra .menu__item form {
  width: 220px;
  text-align: left;
}

.main__menu .menu.menu__extra .menu__item .input-field input:not([type]),.main__menu .menu.menu__extra .menu__item .input-field input[type=text],.main__menu .menu.menu__extra .menu__item .input-field input[type=password],.main__menu .menu.menu__extra .menu__item .input-field input[type=email],.main__menu .menu.menu__extra .menu__item .input-field input[type=url],.main__menu .menu.menu__extra .menu__item .input-field input[type=time],.main__menu .menu.menu__extra .menu__item .input-field input[type=date],.main__menu .menu.menu__extra .menu__item .input-field input[type=datetime-local],.main__menu .menu.menu__extra .menu__item .input-field input[type=tel],.main__menu .menu.menu__extra .menu__item .input-field input[type=number],.main__menu .menu.menu__extra .menu__item .input-field input[type=search],.main__menu .menu.menu__extra .menu__item .input-field textarea.materialize-textarea {
  border-color: #fff;
  background-color: transparent !important;
}

.main__menu .menu.menu__extra .menu__item .input-field label {
  color: #fff;
}

.main__menu .menu.menu__extra .menu__item .tagcloud {
  width: 220px;
}

.main__menu .menu.menu__extra .menu__item .tagcloud a {
  border-color: #fff;
  color: #fff;
  background-color: transparent;
}

.main__menu .menu.menu__extra .menu__item .social__widget {
  text-align: center;
}

.main__menu .menu.menu__extra .menu__item .social__widget a {
  display: inline-block;
  height: 65px;
  width: 65px;
  margin: 0;
}

.main__menu .menu.menu__extra .menu__item .textwidget {
  text-align: left;
  color: #fff;
  padding: 14px;
}

.menu__badge {
  display: inline-block;
  min-width: 10px;
  padding: 0 3px;
  font-size: 13px;
  line-height: 20px;
  font-weight: 300;
  color: inherit;
  text-align: center;
  white-space: nowrap;
  text-transform: uppercase;
  background-color: rgba(255,255,255,0.3);
}

@media (min-width: 1025px) {
  body.side__bar__menu__left .main__theme__wrap {
    width: calc(100vw - 250px);
    -webkit-transform: translateX(250px);
    -moz-transform: translateX(250px);
    -ms-transform: translateX(250px);
    -o-transform: translateX(250px);
    transform: translateX(250px);
  }

  body.side__bar__menu__left footer.main__footer {
    left: 250px;
    width: calc(100vw - 250px);
  }

  body.side__bar__menu__left .parallax__covers .parallax__wrap {
    left: 250px;
  }

  body.side__bar__menu__right .main__theme__wrap {
    width: calc(100vw - 250px);
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    -ms-transform: translateX(0);
    -o-transform: translateX(0);
    transform: translateX(0);
  }

  body.side__bar__menu__right footer.main__footer {
    left: auto;
    width: calc(100vw - 250px);
  }

  body.side__bar__menu__right .parallax__covers .parallax__wrap {
    left: auto;
    right: 250px;
  }
}

@media (max-width: 1024px) {
  .main__menu {
    background-color: #2F2E2F;
    box-shadow: 1px 2px 5px rgba(0,0,0,0.2);
    -webkit-transform: translate(0, 0);
    -moz-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
  }

  .main__menu .main__menu__inner__wrap {
    min-height: 50px;
    max-width: 100%;
    -webkit-box-pack: justify;
    -moz-box-pack: justify;
    box-pack: justify;
    -webkit-justify-content: space-between;
    -moz-justify-content: space-between;
    -ms-justify-content: space-between;
    -o-justify-content: space-between;
    justify-content: space-between;
    -ms-flex-pack: justify;
  }

  .main__menu .main__menu__inner__wrap .main__menu__logo {
    margin: 5px;
  }

  .main__menu .main__menu__inner__wrap .main__menu__logo img {
    max-height: 60px;
  }

  .main__menu .main__menu__inner__wrap .open__menu__btn {
    position: relative;
    display: block;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap {
    position: absolute;
    top: 0;
    left: 100%;
    width: 250px;
    height: 100vh;
    overflow: auto;
    padding: 0;
    -webkit-box-pack: justify;
    -moz-box-pack: justify;
    box-pack: justify;
    -webkit-justify-content: space-between;
    -moz-justify-content: space-between;
    -ms-justify-content: space-between;
    -o-justify-content: space-between;
    justify-content: space-between;
    -ms-flex-pack: justify;
    -webkit-box-orient: vertical;
    -moz-box-orient: vertical;
    box-orient: vertical;
    -webkit-box-direction: normal;
    -moz-box-direction: normal;
    box-direction: normal;
    -webkit-flex-direction: column;
    -moz-flex-direction: column;
    flex-direction: column;
    -ms-flex-direction: column;
    -webkit-box-align: start;
    -moz-box-align: start;
    box-align: start;
    -webkit-align-items: flex-start;
    -moz-align-items: flex-start;
    -ms-align-items: flex-start;
    -o-align-items: flex-start;
    align-items: flex-start;
    -ms-flex-align: start;
    -webkit-flex-grow: 0;
    -moz-flex-grow: 0;
    flex-grow: 0;
    -ms-flex-positive: 0;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__logo {
    display: none;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar {
    width: 100%;
    background-color: transparent;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar>li a {
    line-height: 45px;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li {
    float: none;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li.main-logo {
    display: none;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li a {
    padding: 0 20px;
    white-space: normal;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li a:before {
    width: 3px;
    height: 100%;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li a.dropdown__menu {
    padding-right: 40px !important;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li a.dropdown__menu i.bi-carret-down {
    display: block;
    right: 15px;
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li .dropdown__content {
    position: relative !important;
    top: 0 !important;
    left: 0 !important;
    display: none;
    max-height: 100% !important;
    box-shadow: none;
    opacity: 1 !important;
    background-color: transparent;
    -webkit-transform: scaleY(1) !important;
    -moz-transform: scaleY(1) !important;
    -ms-transform: scaleY(1) !important;
    -o-transform: scaleY(1) !important;
    transform: scaleY(1) !important;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li .dropdown__content li a {
    padding: 7px 5px 7px 30px;
    line-height: 1.5em;
    color: inherit;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li .dropdown__content .dropdown__content a {
    padding-left: 50px;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li .dropdown__content .hide_elem>.dropdown__content {
    display: block !important;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li .dropdown__content .hide_elem>.dropdown__content li a {
    padding-left: 30px !important;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li .dropdown__content .no_link+.dropdown__content li a {
    padding-left: 50px !important;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .menu.menu__extra {
    width: 100%;
    background-color: transparent !important;
    text-align: center;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .menu.menu__extra .menu__item {
    display: inline-block;
    float: none;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .menu.menu__extra .menu__item__shop {
    position: static;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .menu.menu__extra .menu__item__shop>a {
    position: relative;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .menu.menu__extra .menu__item__shop .submenu {
    position: fixed;
    left: 100%;
    right: auto;
    width: 250px;
  }
}

footer.main__footer .footer__copyright .footer__col .theme__widget:first-child {
  padding-top: 0px;
}

footer.main__footer {
  position: relative;
  z-index: 2;
  width: 100%;
  color: #2F2E2F;
  font-weight: 300;
  text-align: left;
  background-color: #fff;
  -webkit-flex-grow: 0;
  -moz-flex-grow: 0;
  flex-grow: 0;
  -ms-flex-positive: 0;
  -webkit-flex-shrink: 0;
  -moz-flex-shrink: 0;
  flex-shrink: 0;
  -ms-flex-negative: 0;
  -webkit-flex-basis: auto;
  -moz-flex-basis: auto;
  flex-basis: auto;
  -ms-flex-preferred-size: auto;
}

footer.main__footer .footer__row {
  position: relative;
  display: -webkit-box;
  display: -moz-box;
  display: box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -moz-box-orient: horizontal;
  box-orient: horizontal;
  -webkit-box-direction: normal;
  -moz-box-direction: normal;
  box-direction: normal;
  -webkit-flex-direction: row;
  -moz-flex-direction: row;
  flex-direction: row;
  -ms-flex-direction: row;
  -webkit-box-lines: multiple;
  -moz-box-lines: multiple;
  box-lines: multiple;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

footer.main__footer .footer__col {
  color: inherit;
}

footer.main__footer .footer__col .theme__widget {
  padding: 0 14px;
}

footer.main__footer .footer__col .theme__widget:first-child {
  padding-top: 50px;
}

footer.main__footer .footer__col .theme__widget:first-child h4 {
  position: relative;
  padding-bottom: 40px;
  color: inherit;
  font-weight: 300;
  letter-spacing: 2px;
}

footer.main__footer .footer__col .theme__widget ul:not(.product_list_widget) li {
  color: inherit;
  padding: 2px 0;
}

footer.main__footer .footer__col .theme__widget ul:not(.product_list_widget) li a {
  color: inherit;
}

footer.main__footer .footer__col .theme__widget ul:not(.product_list_widget) li:hover a {
  border-color: inherit;
  opacity: 0.6;
}

footer.main__footer .footer__col .theme__widget .wp-caption .wp-caption-text {
  color: #2F2E2F;
}

footer.main__footer .footer__col .theme__widget .social__widget a {
  color: inherit;
}

footer.main__footer .footer__col h4,footer.main__footer .footer__col h5,footer.main__footer .footer__col caption {
  color: inherit;
}

footer.main__footer .footer__col h4 {
  margin-top: 0px;
  margin-bottom: 10px;
  font-size: 20px;
  letter-spacing: 1px;
  font-weight: 300;
}

footer.main__footer .footer__col address>div {
  position: relative;
  padding: 9px 0px 9px 30px;
  font-size: 14px;
  line-height: 24px;
}

footer.main__footer .footer__col address>div:first-child {
  padding-top: 0;
}

footer.main__footer .footer__col address>div:first-child i {
  margin-top: 0;
}

footer.main__footer .footer__col address>div i {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 25px;
  height: 25px;
  margin: auto;
  color: inherit;
  font-size: 27px;
  line-height: 25px;
  text-align: center;
}

footer.main__footer .input-field label {
  color: inherit;
}

footer.main__footer .select-wrapper,footer.main__footer input:not([type]),footer.main__footer input[type=text],footer.main__footer input[type=password],footer.main__footer input[type=email],footer.main__footer input[type=url],footer.main__footer input[type=time],footer.main__footer input[type=date],footer.main__footer input[type=datetime-local],footer.main__footer input[type=tel],footer.main__footer input[type=number],footer.main__footer input[type=search],footer.main__footer textarea.materialize-textarea {
  background-color: transparent;
  color: inherit;
}

footer.main__footer .select-wrapper input.select-dropdown {
  padding-left: 0;
}

footer.main__footer .select-wrapper .dropdown-content {
  color: #2F2E2F;
}

footer.main__footer .footer__copyright {
  background-color: #2F2E2F;
}

footer.main__footer .footer__copyright .theme__widget {
  margin: 15px 0;
  color: #fff;
  font-size: 14px;
}

footer.main__footer .forit-devider {
  position: absolute;
  z-index: 10;
  bottom: 10%;
  right: 0;
  display: block;
  height: 78%;
  width: 1px;
}

@media (max-width: 992px) {
  footer.main__footer .theme__container__left {
    padding: 0 15px;
  }

  footer.main__footer .theme__google__map__block {
    position: relative;
    height: 300px;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .rent__list__item {
    width: auto;
  }

  .theme__team-column {
    margin-bottom: 30px;
  }

  .theme__pricing__item__title {
    font-size: 18px;
  }
}

.main__menu .main__menu__navbar {
  font-family: "Roboto", sans-serif;
  font-weight: 700;
  font-style: normal;
}

.main__menu .main__menu__navbar li a,
.main__menu .main__menu__navbar .dropdown__content li a {
  font-weight: 700;
  font-style: normal;
  font-family: 'Open Sans', sans-serif;
}

.main__footer {
  font-family: "Roboto", sans-serif;
  font-weight: 700;
  font-style: normal;
}

.main__menu .main__menu__navbar {
  font-size: 14px;
}

.main__menu .main__menu__navbar li a,
        .main__menu .main__menu__navbar .dropdown__content li a {
  font-size: 14px;
}

.main__menu .main__menu__navbar .dropdown__content li a {
  line-height: 1.4em;
}

.main__menu .main__menu__logo svg,
        .main__menu .main__menu__logo img,
        .main__menu .main-logo svg,
        .main__menu .main-logo img {
  height: 45px;
}

.main__menu .main__menu__navbar {
  background-color: rgba(47, 46, 47, 0.01);
}

.menu__item .select-wrapper input {
  color: #FFF;
}

.menu__item .select-wrapper .dropdown-content li {
  background-color: #fff;
  color: #4a90e2 !important;
}

.main__menu--scroll .menu__item .select-wrapper input {
  color: #FFF;
}

.main__menu .main__menu__navbar > li {
  color: #FFF;
}

.main__menu.-normal__menu.main__menu--scroll .main__menu__navbar > li,
        .main__menu.-normal__menu.main__menu--scroll .menu.menu__extra .menu__item > div > a i {
  color: #FFF;
}
        /* active */
        
.main__menu .main__menu__navbar > li a:before {
  background-color: #FFF;
}

.main__menu .main__menu__navbar .dropdown__content > li a:before {
  background-color: #4a90e2;
}
        /* dropdown */
        
.main__menu .main__menu__navbar li > .dropdown__content {
  background-color: #fff;
}

.main__menu .main__menu__navbar .dropdown__content li {
  color: #4a90e2;
}

.open__menu__btn span {
  background-color: #FFF;
}

@media (min-width: 1025px) {
  .main__menu.-normal__menu.main__menu--scroll,
            .main__menu.-center__logo__menu.main__menu--scroll,
            .main__menu.-aside__menu.main__menu.main__menu--scroll .main__menu__inner__wrap .main__menu__wrap,
            .main__menu.-aside__menu.main__menu.main__menu--scroll {
    box-shadow: 1px 2px 5px rgba(0, 0, 0, 0.5);
  }

  .main__menu.-normal__menu,
            .main__menu.-center__logo__menu,
            .main__menu.-aside__menu.main__menu .main__menu__inner__wrap .main__menu__wrap,
            .main__menu.-aside__menu.main__menu {
    background-color: ;
                box-shadow: 1px 2px 5px rgba(255, 255, 255, 0.01);
  }

  .main__menu.-aside__menu [class*="depth"]:after,
            .main__menu.-aside__menu [class*="depth"]:before {
    background: #FFF;
  }
            /* menu scroll */
  .main__menu.-normal__menu.main__menu--scroll,
            .main__menu.-center__logo__menu.main__menu--scroll {
    background-color: #4a90e2;
  }

  .main__menu.-normal__menu.main__menu--scroll .menu.menu__extra .menu__item__shop > a .shopping-cart-items-count,
            .main__menu.-center__logo__menu.main__menu--scroll .menu.menu__extra .menu__item__shop > a .shopping-cart-items-count {
    color: #FFF;
    border-color: #FFF;
  }

  .main__menu.-normal__menu.main__menu--scroll .main__menu__navbar > li a:before,
            .main__menu.-center__logo__menu.main__menu--scroll .main__menu__navbar > li a:before {
    background-color: #FFF;
  }

  .main__header .-aside__menu.--menu__list__fullscreen.-menu_is_open,
            .main__header .main__menu.-aside__menu.--menu__list__fullscreen.-menu_is_open .main__menu__wrap {
    background-color: rgba(0, 0, 0, 0);
  }
            /* offset */
  .main__menu.-normal__menu,
            .main__menu.-center__logo__menu {
    -webkit-transform: translate(0, 0px);
    -moz-transform: translate(0, 0px);
    -ms-transform: translate(0, 0px);
    -o-transform: translate(0, 0px);
    transform: translate(0, 0px);
  }
            /* offset  scroll */
  .main__menu.-normal__menu.main__menu--scroll,
            .main__menu.-center__logo__menu.main__menu--scroll {
    -webkit-transform: translate(0, 0px);
    -moz-transform: translate(0, 0px);
    -ms-transform: translate(0, 0px);
    -o-transform: translate(0, 0px);
    transform: translate(0, 0px);
  }

  .main__menu.-normal__menu .main__menu__navbar>li.forit__wide>.dropdown__content>li,
            .main__menu.-center__logo__menu .main__menu__navbar>li.forit__wide>.dropdown__content>li,
            .main__menu.-side__bar__menu__left.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content>li,
            .main__menu.-side__bar__menu__right.main__menu--outside__dropdown .main__menu__navbar>li.forit__wide>.dropdown__content>li {
    border-color: rgba(74, 144, 226, 0.3);
  }
}
        /* extra menu */
        
.main__menu .menu.menu__extra .menu__item h4,
        .main__menu .menu.menu__extra .menu__item .textwidget {
  color: #FFF;
}

.main__menu .menu.menu__extra .menu__item .input-field input:not([type]),
        .main__menu .menu.menu__extra .menu__item .input-field input[type=text],
        .main__menu .menu.menu__extra .menu__item .input-field input[type=password],
        .main__menu .menu.menu__extra .menu__item .input-field input[type=email],
        .main__menu .menu.menu__extra .menu__item .input-field input[type=url],
        .main__menu .menu.menu__extra .menu__item .input-field input[type=time],
        .main__menu .menu.menu__extra .menu__item .input-field input[type=date],
        .main__menu .menu.menu__extra .menu__item .input-field input[type=datetime-local],
        .main__menu .menu.menu__extra .menu__item .input-field input[type=tel],
        .main__menu .menu.menu__extra .menu__item .input-field input[type=number],
        .main__menu .menu.menu__extra .menu__item .input-field input[type=search],
        .main__menu .menu.menu__extra .menu__item .input-field textarea.materialize-textarea {
  border-color: #FFF;
}

.main__menu .menu.menu__extra .menu__item .input-field label {
  color: #FFF;
}

.main__menu .menu.menu__extra .menu__item .tagcloud a,
        .main__menu .menu.menu__extra .menu__item ul li.recentcomments {
  color: #FFF;
  border-color: #FFF;
}

.menu__badge {
  color: #fff;
  background-color: #4a90e2;
}
        /* menu widget styles */
        
.main__menu .menu.menu__extra {
  background-color: rgba(47, 46, 47, 0.01);
}

.main__menu .menu.menu__extra .menu__item > div > a i {
  color: #FFF;
}
        /* menu cart styles */
        
.main__menu .menu.menu__extra .menu__item__shop > a .shopping-cart-items-count {
  color: #FFF;
  border-color: #FFF;
}

.main__menu .menu.menu__extra .menu__item__shop .submenu .mini_cart_item .quantity {
  color: #4a90e2;
}

.main__menu .menu.menu__extra .menu__item__shop .submenu .mini_cart_item .remove {
  color: #4a90e2;
}

.main__menu .menu.menu__extra .menu__item__shop .submenu .total span {
  color: #4a90e2;
}
        /* aside menu left and right */
        
@media (min-width: 1025px) {
  .main__menu.-side__bar__menu__left .main__menu__wrap,
            .main__menu.-side__bar__menu__right .main__menu__wrap {
    background-color: rgba(47, 46, 47, 1);
  }

  .main__menu.-side__bar__menu__left.main__menu--expand__dropdown .menu__badge,
            .main__menu.-side__bar__menu__right.main__menu--expand__dropdown .menu__badge {
    color: rgba(47, 46, 47, 1);
  }
}
        /* RESPONSIVE mobile menu */
        
@media (max-width: 1024px) {
            /* header background color */
  .main__menu {
    background-color: #4a90e2;
  }
            /* burger btn color */
  .main__menu .open__menu__btn span {
    background-color: #FFF;
  }
            /* bage color */
  .main__menu .menu__badge {
    color: #fff;
    background-color: #4a90e2;
  }
            /* active item color */
  .main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar > li a:before,
            .main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar .dropdown__content > li a:before {
    background-color: #4a90e2;
  }
            /* menu bakground */
  .main__menu .main__menu__inner__wrap .main__menu__wrap {
    background-color: #fff;
  }
            /* links color */
  .main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar > li,
            .main__menu .main__menu__inner__wrap .main__menu__wrap .main__menu__navbar li .dropdown__content li {
    color: #4a90e2;
  }
            /* menu widgets */
  .main__menu .main__menu__inner__wrap .main__menu__wrap .menu.menu__extra .menu__item > div > a i {
    color: #4a90e2;
  }

  .main__menu .main__menu__inner__wrap .main__menu__wrap .menu.menu__extra .menu__item__shop > a .shopping-cart-items-count {
    color: #4a90e2;
    border-color: #4a90e2;
  }
}
        /* ---------------- ---------------- ---------------- ---------------- ---------------- ---------------- */
        /* ---------------- ---------------- ---------------- ---------------- ---------------- ---------------- */
        /* ---------------- ---------------- ---------------- ---------------- ---------------- ---------------- */
        /* layouts */
        /* ---------------- ---------------- ---------------- ---------------- ---------------- ---------------- */
        
        
footer.main__footer {
  background-color: #545768;
}

footer.main__footer .footer__col .theme__widget:first-child h4:after {
  background-color: #4a90e2;
}

footer.main__footer .footer__col {
  color: #ffffff;
}

footer .theme__widget .post-date {
  color: #ffffff;
}

footer.main__footer .tagcloud a {
  color: #ffffff;
  border-color: #ffffff;
}

footer.main__footer .tagcloud a:hover {
  color: #545768;
  border-color: #ffffff;
  background-color: #ffffff;
}

footer.main__footer .select-wrapper,
        footer.main__footer input:not([type]),
        footer.main__footer input[type=text],
        footer.main__footer input[type=password],
        footer.main__footer input[type=email],
        footer.main__footer input[type=url],
        footer.main__footer input[type=time],
        footer.main__footer input[type=date],
        footer.main__footer input[type=datetime-local],
        footer.main__footer input[type=tel],
        footer.main__footer input[type=number],
        footer.main__footer input[type=search],
        footer.main__footer textarea.materialize-textarea {
  border-color: #ffffff;
}

footer.main__footer [type="radio"]:not(:checked) + label:before {
  border: 2px solid #ffffff;
}

footer.main__footer [type="radio"]:checked + label:after {
  border: 2px solid #ffffff;
  background-color: #ffffff;
}

footer.main__footer .select-wrapper span.caret {
  border-right: 1px solid #ffffff;
  border-bottom: 1px solid #ffffff;
}

footer.main__footer .footer__col .theme__widget ul:not(.product_list_widget) li a {
  border-color: #ffffff;
}

footer.main__footer .forit-devider {
  background: rgba(255, 255, 255, .5);
}

footer.main__footer .footer__copyright {
  background-color: #545768;
  color: #FFFFFF;
}

footer.main__footer .footer__copyright .theme__widget {
  color: #FFFFFF;
}

.theme__container__full:before,
.theme__container__full:after,
.theme__container:before,
.theme__container:after,
.theme__container__left:before,
.theme__container__left:after,
.theme__container__right:before,
.theme__container__right:after {
  content: '';
  display: table;
  width: 100%;
  clear: both;
}

.theme__container .theme__container,
.theme__container .theme__container__left,
.theme__container .theme__container__right,
.theme__container__left .theme__container,
.theme__container__left .theme__container__left,
.theme__container__left .theme__container__right,
.theme__container__right .theme__container,
.theme__container__right .theme__container__left,
.theme__container__right .theme__container__right {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

@media (min-width: 768px) {
  .theme__container {
    padding-left: calc((100vw - 750px) / 2);
    padding-right: calc((100vw - 750px) / 2);
  }

  .theme__container__left {
    padding-left: calc((100vw - 750px) / 2);
  }

  .theme__container__right {
    padding-right: calc((100vw - 750px) / 2);
  }

  .theme__container__full .theme__container {
    padding-left: calc((100vw - 720px) / 2);
    padding-right: calc((100vw - 720px) / 2);
  }

  .theme__container__full .theme__container__left {
    padding-left: calc((100vw - 720px) / 2);
  }

  .theme__container__full .theme__container__right {
    padding-right: calc((100vw - 720px) / 2);
  }

  .theme__container__full .theme__container__full .theme__container {
    padding-left: calc((100vw - 750px) / 2);
    padding-right: calc((100vw - 750px) / 2);
  }

  .theme__container__full .theme__container__full .theme__container__left {
    padding-left: calc((100vw - 750px) / 2);
  }

  .theme__container__full .theme__container__full .theme__container__right {
    padding-right: calc((100vw - 750px) / 2);
  }
}

@media (min-width: 992px) {
  .theme__container {
    padding-left: calc((100vw - 970px) / 2);
    padding-right: calc((100vw - 970px) / 2);
  }

  .theme__container__left {
    padding-left: calc((100vw - 970px) / 2);
  }

  .theme__container__right {
    padding-right: calc((100vw - 970px) / 2);
  }

  .theme__container__full .theme__container {
    padding-left: calc((100vw - 940px) / 2);
    padding-right: calc((100vw - 940px) / 2);
  }

  .theme__container__full .theme__container__left {
    padding-left: calc((100vw - 940px) / 2);
  }

  .theme__container__full .theme__container__right {
    padding-right: calc((100vw - 940px) / 2);
  }

  .theme__container__full .theme__container__full .theme__container {
    padding-left: calc((100vw - 970px) / 2);
    padding-right: calc((100vw - 970px) / 2);
  }

  .theme__container__full .theme__container__full .theme__container__left {
    padding-left: calc((100vw - 970px) / 2);
  }

  .theme__container__full .theme__container__full .theme__container__right {
    padding-right: calc((100vw - 970px) / 2);
  }
}

@media (min-width: 1200px) {
  .theme__container {
    padding-left: calc((100vw - 1170px) / 2);
    padding-right: calc((100vw - 1170px) / 2);
  }

  .theme__container__left {
    padding-left: calc((100vw - 1170px) / 2);
  }

  .theme__container__right {
    padding-right: calc((100vw - 1170px) / 2);
  }

  .theme__container__full .theme__container {
    padding-left: calc((100vw - 1140px) / 2);
    padding-right: calc((100vw - 1140px) / 2);
  }

  .theme__container__full .theme__container__left {
    padding-left: calc((100vw - 1140px) / 2);
  }

  .theme__container__full .theme__container__right {
    padding-right: calc((100vw - 1140px) / 2);
  }

  .theme__container__full .theme__container__full .theme__container {
    padding-left: calc((100vw - 1170px) / 2);
    padding-right: calc((100vw - 1170px) / 2);
  }

  .theme__container__full .theme__container__full .theme__container__left {
    padding-left: calc((100vw - 1170px) / 2);
  }

  .theme__container__full .theme__container__full .theme__container__right {
    padding-right: calc((100vw - 1170px) / 2);
  }
}

@media (min-width: 1025px) {
  .side__bar__menu__left .theme__container,
    .side__bar__menu__right .theme__container {
    padding-left: calc(((100vw - 250px) - 1170px) / 2);
    padding-right: calc(((100vw - 250px) - 1170px) / 2);
  }

  .side__bar__menu__left .theme__container__left,
    .side__bar__menu__right .theme__container__left {
    padding-left: calc(((100vw - 250px) - 1170px) / 2);
  }

  .side__bar__menu__left .theme__container__right,
    .side__bar__menu__right .theme__container__right {
    padding-right: calc(((100vw - 250px) - 1170px) / 2);
  }

  .side__bar__menu__left .theme__container__full .theme__container,
    .side__bar__menu__right .theme__container__full .theme__container {
    padding-left: calc(((100vw - 250px) - 1140px) / 2);
    padding-right: calc(((100vw - 250px) - 1140px) / 2);
  }

  .side__bar__menu__left .theme__container__full .theme__container__left,
    .side__bar__menu__right .theme__container__full .theme__container__left {
    padding-left: calc(((100vw - 250px) - 1140px) / 2);
  }

  .side__bar__menu__left .theme__container__full .theme__container__right,
    .side__bar__menu__right .theme__container__full .theme__container__right {
    padding-right: calc(((100vw - 250px) - 1140px) / 2);
  }

  .side__bar__menu__left .theme__container__full .theme__container__full .theme__container,
    .side__bar__menu__right .theme__container__full .theme__container__full .theme__container {
    padding-left: calc(((100vw - 250px) - 1170px) / 2);
    padding-right: calc(((100vw - 250px) - 1170px) / 2);
  }

  .side__bar__menu__left .theme__container__full .theme__container__full .theme__container__left,
    .side__bar__menu__right .theme__container__full .theme__container__full .theme__container__left {
    padding-left: calc(((100vw - 250px) - 1170px) / 2);
  }

  .side__bar__menu__left .theme__container__full .theme__container__full .theme__container__right,
    .side__bar__menu__right .theme__container__full .theme__container__full .theme__container__right {
    padding-right: calc(((100vw - 250px) - 1170px) / 2);
  }
}

footer.main__footer .footer__col .theme__widget:first-child h4:after {
  background-color: #4a90e2;
}

footer.main__footer .footer__col .theme__widget:first-child h4:after {
  background-color: #4a90e2;
}

.theme__widget h4:after, .theme__widget h5:after {
  background-color: #4a90e2;
}

.theme__widget h4:after, .theme__widget h5:after, .menu.menu__extra .menu__item h4:after, .menu.menu__extra .menu__item h5:after {
  content: '';
  position: absolute;
  bottom: 20px;
  left: 0;
  height: 1px;
  width: 100%;
}

.theme__widget .social__widget a, .menu.menu__extra .menu__item .social__widget a {
  display: inline-block;
  margin-right: 20px;
  font-size: 18px;
  color: inherit;
}

.theme__widget .social__widget a i, .menu.menu__extra .menu__item .social__widget a i {
  width: 1.8em;
  height: 1.8em;
  color: inherit;
  vertical-align: middle;
  line-height: 1.7em;
  text-align: center;
  -webkit-transition: color 0.5s;
  -moz-transition: color 0.5s;
  transition: color 0.5s;
}

.theme__widget .textwidget {
  font-family: "", sans-serif;
  font-weight: 400;
  font-style: normal;
}

.theme__widget .textwidget, .menu.menu__extra .menu__item .textwidget {
  line-height: 1.4em;
  font-weight: 300;
}

a:hover, a:active, a:focus {
  color: inherit;
  text-decoration: none;
}

footer a {
  color: #FFF;
  font-weight: normal;
}

.theme__widget ul:not(.product_list_widget), .menu.menu__extra .menu__item ul:not(.product_list_widget) {
  padding: 0;
  list-style: none;
}

.theme__widget ul {
  padding: 0;
  list-style: none;
}

.theme__widget ul:not(.product_list_widget) li, .menu.menu__extra .menu__item ul:not(.product_list_widget) li {
  position: relative;
  font-size: 14px;
  color: inherit;
  font-weight: 300;
  padding: 5px 0;
}

.theme__widget ul:not(.product_list_widget)>li:first-child>a, .menu.menu__extra .menu__item ul:not(.product_list_widget)>li:first-child>a {
  padding-top: 0;
}

.theme__widget ul:not(.product_list_widget) li a, .menu.menu__extra .menu__item ul:not(.product_list_widget) li a {
  position: relative;
  display: block;
  padding: 4px 0;
  font-size: 14px;
  color: inherit;
  font-weight: 300;
  line-height: 23px;
}

.theme__widget ul:not(.product_list_widget) li::after, .menu.menu__extra .menu__item ul:not(.product_list_widget) li::after {
  clear: both;
  content: "";
  display: table;
}