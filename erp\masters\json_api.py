"""
"""
import datetime
import json

import pymysql
import simplejson
from django.contrib.auth import SESSION_KEY
from django.http import HttpResponse

from erp import helper
from erp.auth import USER_IN_SESSION_KEY, ENTERPRISE_IN_SESSION_KEY, ENTERPRISE_ID_SESSION_KEY, PRIMARY_ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from erp.dao import executeQuery
from erp.helper import saveAttachment, getMaterialFrequents
from erp.masters import logger
from erp.masters.backend import MasterDAO, MasterService
from erp.models import Party, Employee, Material, Tax, SubTax, MaterialSupplierProfile
from erp.sales.backend import InvoiceService
from settings import TEMP_DIR, HOST, USER, PASSWORD, DBNAME, PORT, SQLASession, GCS_PUBLIC_URL
from util.api_util import response_code, json_util

__author__ = 'nandha'


def listParty(request):
	"""
	Masters Party

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		response = response_code.success()
		logger.info("Processing list access of module Master.")
		response['parties'] = MasterDAO().getAllPartiesArrayMap(enterprise_id=enterprise_id)
	except Exception as e:
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getLastAddedParty(request):
	"""
	Masters Party

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		response = response_code.success()
		party_detail = MasterDAO().getLastAddedParty(enterprise_id=enterprise_id)
		response['code'] = party_detail['code']
		response['name'] = party_detail['name']
		response['id'] = party_detail['id']
		response['response_message'] = "Success"
	except Exception as e:
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def listPartyNames(request):
	"""
	Masters Party

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		response = response_code.success()
		logger.info("Accessing Party Names list from Master Service.")
		response['party_names'] = MasterDAO().getAllPartyNames(enterprise_id=enterprise_id)
	except Exception as e:
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getPartyDetail(request):
	"""
	Masters.Party

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		party_id = rh.getPostData('party_id')
		enterprise_id = rh.getPostData('enterprise_id')
		if not party_id:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')

		logger.info("Fetching details for Master.Party")
		response = response_code.success()
		response.update(MasterService().getPartyDetail(party_id=party_id, enterprise_id=enterprise_id))
	except Exception as e:
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def deleteParty(request):
	"""
	Masters.Party

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		party_code = rh.getPostData('party_code')
		enterprise_id = rh.getPostData('enterprise_id')
		if not party_code:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')

		logger.info("Processing delete access of Master.Party")
		if MasterDAO().deleteParty(party_code=party_code, enterprise_id=enterprise_id):
			response = response_code.success()
		else:
			response = response_code.databaseError().copy()
			response['custom_message'] = 'Could not delete party'
	except Exception as e:
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def saveParty(request):
	"""
	Masters.Party Save / Insert / Update

	:param request:
	:return:
	"""
	master_dao = MasterDAO()
	master_dao.db_session.begin(subtransactions=True)
	try:
		rh = RequestHandler(request)
		data = rh.getPostData('party')
		if data is None:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')

		party_json = json.loads(data)
		enterprise_id = json_util.getValue(party_json, 'enterprise_id')
		party = master_dao.getPartyByCode(json_util.getValue(party_json, 'code'), enterprise_id)
		if party is None:
			party = Party()
		party.code = json_util.getValue(party_json, 'code')
		party.name = json_util.getValue(party_json, 'name')
		party.enterprise_id = json_util.getValue(party_json, 'enterprise_id')
		party.address_1 = json_util.getValue(party_json, 'address_1')
		party.address_2 = json_util.getValue(party_json, 'address_2')

		party.city = json_util.getValue(party_json, 'city')
		party.state = json_util.getValue(party_json, 'state')
		party.phone = json_util.getValue(party_json, 'phone')
		party.email = json_util.getValue(party_json, 'email')
		party.contact = json_util.getValue(party_json, 'contact')

		party.cst_no = json_util.getValue(party_json, 'cst_no')
		party.gst_no = json_util.getValue(party_json, 'gst_no')
		party.tin_no = json_util.getValue(party_json, 'tin_no')
		party.cin_no = json_util.getValue(party_json, 'cin_no')

		party.ecc_no = json_util.getValue(party_json, 'ecc_no')
		party.tan_no = json_util.getValue(party_json, 'tan_no')
		party.pan_no = json_util.getValue(party_json, 'pan_no')

		party.config_flags = json_util.getValue(party_json, 'config_flags')

		master_dao.db_session.add(party)
		response = response_code.success()
		master_dao.db_session.commit()
	except Exception as e:
		master_dao.db_session.rollback()
		logger.exception(e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def listEmployee(request):
	"""
	Masters Employee

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		response = response_code.success()
		logger.info("Processing list access of module Master.")
		response['employees'] = MasterDAO().getAllEmployeesArrayMap(enterprise_id=enterprise_id)
	except Exception as e:
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def deleteEmployee(request):
	"""
	Masters.Employee

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		employee_code = rh.getPostData('employee_code')
		enterprise_id = rh.getPostData('enterprise_id')
		if not employee_code:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')

		logger.info("Processing delete access of Master.Employee")
		if MasterDAO().deleteEmployee(emp_code=employee_code, enterprise_id=enterprise_id):
			response = response_code.success()
		else:
			response = response_code.databaseError().copy()
			response['custom_message'] = 'Could not delete employee'
	except Exception as e:
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def saveEmployee(request):
	"""
	Masters.Employee Save / Insert / Update

	:param request:
	:return:
	"""
	master_dao = MasterDAO()
	master_dao.db_session.begin(subtransactions=True)
	try:
		rh = RequestHandler(request)
		data = rh.getPostData('employee')
		if not data:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')

		employee_json = json.loads(data)
		emp_code = json_util.getValue(employee_json, 'emp_code')
		enterprise_id = json_util.getValue(employee_json, 'enterprise_id')
		employee = master_dao.getEmployeeByCode(emp_code=emp_code, enterprise_id=enterprise_id)
		if employee is None:
			employee = Employee()

		employee.emp_code = emp_code
		employee.enterprise_id = enterprise_id
		employee.emp_name = json_util.getValue(employee_json, 'first_name')
		employee.department_id = json_util.getValue(employee_json, 'department_id')

		employee.address1 = json_util.getValue(employee_json, 'address1')
		employee.address2 = json_util.getValue(employee_json, 'address2')
		employee.email = json_util.getValue(employee_json, 'email')
		employee.phone_no = json_util.getValue(employee_json, 'phone_no')

		employee.city = json_util.getValue(employee_json, 'city')
		employee.state = json_util.getValue(employee_json, 'state')

		if master_dao.saveEmployee(employee):
			response = response_code.success()
		else:
			response = response_code.databaseError().copy()
		master_dao.db_session.commit()
	except Exception as e:
		master_dao.db_session.rollback()
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def listMaterial(request):
	"""
	Masters Material

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		response = response_code.success()
		logger.info("Processing list access of Master.Material")
		response['materials'] = MasterDAO().getAllMaterialsArrayMap(enterprise_id=enterprise_id)
	except Exception as e:
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def loadMaterials(request):
	"""
	Loads All Material's primary information.

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	try:
		enterprise_id = request_handler.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
		last_accessed_on = request_handler.getPostData("last_accessed_on")
		response = response_code.success()
		service = MasterService()
		response['materials'] = service.getMaterials(enterprise_id=enterprise_id)
		if last_accessed_on and last_accessed_on != "":
			response['materials_not_in_use'] = service.getMaterials(
				enterprise_id=enterprise_id, modified_after=last_accessed_on, in_use=0)

		response['last_accessed_on'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
	except Exception as e:
		logger.info("Loading Materials failed - %s" % e)
		from erp.middleware import EXCEPTION_RESPONSE_KEY
		request.session[EXCEPTION_RESPONSE_KEY] = HttpResponse(simplejson.dumps(""), mimetype='application/json')
		raise
	response = HttpResponse(content=simplejson.dumps(response), mimetype='application/json')
	return response


def loadFrequentMaterials(request):
	"""
	Loads All Material's primary information.

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	app_module = request_handler.getPostData('module')
	enterprise_id = request_handler.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
	try:
		if app_module == 'grn':
			received_against = request_handler.getPostData('received_against')
			if received_against == 'issue':
				query = """SELECT item_id, make_id, enterprise_id, COUNT(1) AS item_count FROM issue_material_details
					WHERE enterprise_id='%s'
					GROUP BY item_id, make_id  ORDER BY item_count DESC LIMIT 0, 5""" % enterprise_id
			elif received_against in ('Issues', 'PurchaseOrder', 'Note', 'Others', 'Job work'):
				received_against = 'Others'
				if received_against == 'Issues':
					received_against = 'Issues'
				elif received_against == 'PurchaseOrder':
					received_against = 'Purchase Order'
				query = """SELECT gm.item_id as item_id, gm.make_id as make_id, gm.enterprise_id as enterprise_id, COUNT(1) AS item_count
							FROM grn g JOIN grn_material gm
								ON g.enterprise_id = gm.enterprise_id AND g.grn_no = gm.grnNumber
							WHERE g.enterprise_id = '%s' AND g.rec_against = '%s'
							GROUP BY gm.item_id , gm.make_id , g.enterprise_id , g.rec_against
							ORDER BY item_count DESC LIMIT 0, 5""" % (enterprise_id, received_against)
			else:
				query = """
						SELECT 
							indent_material.item_id as item_id,
							indent_material.make_id as make_id,
							indent_material.enterprise_id as enterprise_id,
							COUNT(1) AS item_count
						FROM
							indent_material
								JOIN
						materials ON materials.id = indent_material.item_id and 
						materials.enterprise_id= indent_material.enterprise_id
							AND materials.in_use = 1								
						WHERE indent_material.enterprise_id = '%s'
						GROUP BY item_id , make_id
						ORDER BY item_count DESC LIMIT 0,5""" % enterprise_id
			result_list = executeQuery(query, as_dict=True)
		else:
			result_list = getMaterialFrequents(
				table=str(request_handler.getPostData('module')), enterprise_id=enterprise_id,
				type=str(request_handler.getPostData('type')),
				party_id=str(request_handler.getPostData('party_id')),
				particulars=str(request_handler.getPostData('particulars')))
		criteria = []
		for row in result_list:
			criteria.append("(m.id = {item_id})".format(**row))
		if len(criteria) > 0:
			frequent_list = MasterService().getMaterials(
				enterprise_id=enterprise_id, criteria=" AND (%s)" % (" OR ".join(criteria)))
		else:
			frequent_list = []
		response = response_code.success()
		response['materials'] = frequent_list
	except Exception as e:
		logger.exception("Loading Frequent Materials failed - %s" % e)
		from erp.middleware import EXCEPTION_RESPONSE_KEY
		request.session[EXCEPTION_RESPONSE_KEY] = HttpResponse(simplejson.dumps(""), mimetype='application/json')
		raise
	response = HttpResponse(content=simplejson.dumps(response), mimetype='application/json')
	return response


def listMaterialNames(request):
	"""
	Masters Material

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		response = response_code.success()
		logger.info("Loading material names with make")
		response['material_names'] = MasterDAO().getAllMaterialNames(enterprise_id=enterprise_id)
		logger.info("Loaded %s material names" % len(response['material_names']))
	except Exception as e:
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getMaterialDetail(request):
	"""
	Masters.Party

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		item_id = rh.getPostData('item_id')
		enterprise_id = rh.getPostData('enterprise_id')
		if not item_id:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')

		logger.info("Fetching details for Master.Material")
		response = response_code.success()
		response.update(MasterDAO().getMaterialDetail(item_id=item_id, enterprise_id=enterprise_id))
	except Exception as e:
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def deleteMaterial(request):
	"""
	Masters.Material

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		drawing_no = rh.getPostData('drawing_no')
		enterprise_id = rh.getPostData('enterprise_id')
		if not drawing_no:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')

		logger.info("Processing delete access of Master.Material")
		if MasterDAO().deleteMaterial(drawing_no=drawing_no, enterprise_id=enterprise_id):
			response = response_code.success()
		else:
			response = response_code.databaseError().copy()
			response['custom_message'] = 'Could not delete material'
	except Exception as e:
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def saveMaterial(request):
	"""
	Masters.Material Save / Insert / Update

	:param request:
	:return:
	"""
	master_dao = MasterDAO()
	master_dao.db_session.begin(subtransactions=True)
	try:
		rh = RequestHandler(request)
		data = rh.getPostData('material')
		logger.info('Material save: %s')
		if not data:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')

		material_json = json.load(data)
		drawing_no = json_util.getValue(material_json, 'drawing_no')
		enterprise_id = json_util.getValue(material_json, 'enterprise_id')

		material = master_dao.getMaterialByDrawingNumber(drawing_no=drawing_no, enterprise_id=enterprise_id)
		if material is None:
			material = Material()
			material.created_on = datetime.datetime.now()

		material.drawing_no = drawing_no
		material.enterprise_id = enterprise_id
		material.catalogue_code = json_util.getValue(material_json, 'catalogue_code')
		material.name = json_util.getValue(material_json, 'name')
		material.description = json_util.getValue(material_json, 'description')

		material.price = json_util.getValue(material_json, 'price')
		material.category_id = json_util.getValue(material_json, 'category')
		material.unit_id = json_util.getValue(material_json, 'unit_id')
		material.is_stocked = json_util.getValue(material_json, 'is_stocked')
		material.in_use = json_util.getValue(material_json, 'in_use')
		material.minimum_stock_level = json_util.getValue(material_json, 'minimum_stock_level')

		if master_dao.saveMaterial(material):
			response = response_code.success()
		else:
			response = response_code.databaseError().copy()
			response['custom_message'] = 'Could not save Material'
		master_dao.db_session.commit()
	except Exception as e:
		master_dao.db_session.rollback()
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def uploadAttachmentMaterial(request):
	"""
	Upload attachment for material
	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise
		uploaded_by = request_handler.getSessionAttribute(SESSION_KEY)
		uploaded_on = datetime.datetime.now()
		label = request_handler.getPostData('label')
		item_id = request_handler.getPostData('item_id')
		gcs_key = request_handler.getPostData('gcs_key')
		logger.info("Item ID :%s & label: %s" % (item_id, label))
		file_to_be_saved = request.FILES.getlist('file')
		fil_ext = file_to_be_saved[0].name.split(".")[-1].lower()
		doc = "%s.%s" % (gcs_key, fil_ext)
		attachment_id = saveAttachment(
			enterprise_id=enterprise.id, label=label, file_to_be_saved=doc, ext=fil_ext,
			uploaded_by=uploaded_by, uploaded_on=uploaded_on, gcs_key=gcs_key)
		insert_mapping_tuple = (item_id, enterprise.id, attachment_id)
		sql_insert_mapping_query = """
		INSERT INTO material_attachment_map (item_id, enterprise_id, attachment_id) VALUES (
		'%s','%s','%s')""" % insert_mapping_tuple
		executeQuery(sql_insert_mapping_query)
		logger.info("Attachment inserted succesfully")
		response = response_code.success()
	except Exception as e:
		logger.exception("Import data %s" % e)
		response = response_code.internalError()
	return HttpResponse(
		content=json.dumps(response), mimetype='application/json')


def getAllAttachmentMaterial(request):
	"""
	list all the attachments for each material
	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
		item_id = request_handler.getPostData('item_id')
		query = """
					SELECT 
					    materials.drawing_no,
					    material_attachment_map.attachment_id,
					    material_attachment_map.enterprise_id, 
					    attachment.description,
					    attachment.file,
					    attachment.ext,					    
					    material_attachment_map.item_id,
					    attachment.gcs_key
					FROM
					    material_attachment_map
					        INNER JOIN
					    materials ON materials.id = material_attachment_map.item_id
					        AND materials.enterprise_id = material_attachment_map.enterprise_id
					        INNER JOIN
					    attachment ON attachment.id = material_attachment_map.attachment_id
					        AND attachment.enterprise_id = material_attachment_map.enterprise_id
					WHERE
					    material_attachment_map.item_id = '%s'
					    AND material_attachment_map.enterprise_id = '%s'""" % (item_id, enterprise_id)
		response = []
		for row in executeQuery(query):
			if row[7] is not None or row[7] != '' or row[7] != 'NULL':
				gcs_url = "{url}/{key}".format(url=GCS_PUBLIC_URL, key=row[7])
				result = {
					'drawing_no': row[0], 'attachment_id': row[1], 'enterprise_id': row[2], 'description': row[3]
					, 'file': row[4], 'ext': row[5], 'item_id': row[6], 'gcs_id': row[7], 'file_base64': gcs_url}
				response.append(result)
		# response = response_code.success()
	except Exception as e:
		logger.exception("Import data %s" % e)
		response = response_code.internalError()
	return HttpResponse(
		content=json.dumps(response), mimetype='application/json')


def deleteAttachmentMaterial(request):
	"""
	Delete attachment material
	:param request:
	:return:
	"""
	db_connection = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
	cursor = db_connection.cursor()
	try:
		request_handler = RequestHandler(request)
		enterprise = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise
		item_id = request_handler.getPostData('item_id')
		attachment_id = request_handler.getPostData('attachment_id').encode("utf-8")
		response = response_code.success()
		queryAttachmentMap = """
				DELETE FROM material_attachment_map
			WHERE 
				material_attachment_map.item_id = '%s'
				AND material_attachment_map.attachment_id = '%s'
				AND material_attachment_map.enterprise_id = '%s'""" % (item_id, attachment_id, enterprise.id)
		if cursor.execute(queryAttachmentMap):
			query_attachment = """DELETE FROM attachment WHERE attachment.id='%s' AND attachment.enterprise_id='%s'""" % (
			attachment_id, enterprise.id)
			cursor.execute(query_attachment)
			db_connection.commit()
		else:
			db_connection.rollback()
			response = response_code.failure()
	except Exception as e:
		logger.exception("Delete attachment data %s" % e)
		db_connection.rollback()
		response = response_code.internalError()
	finally:
		db_connection.close()
	logger.info("response code: %s" % json.dumps(response))
	return HttpResponse(
		content=json.dumps(response), mimetype='application/json')


def listTaxes(request):
	"""
	Masters Tax

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		if enterprise_id is None:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')

		response = response_code.success()
		logger.info("Processing list access of Master.Tax")
		response['taxes'] = MasterDAO().getAllTaxesArrayMap(enterprise_id=enterprise_id)
	except Exception as e:
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def deleteTax(request):
	"""
	Masters.Tax

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		tax_code = rh.getPostData('tax_code')
		enterprise_id = rh.getPostData('enterprise_id')
		if not tax_code:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')

		logger.info("Processing delete access of Master.Tax")
		if MasterDAO().deleteTax(tax_code=tax_code, enterprise_id=enterprise_id):
			response = response_code.success()
		else:
			response = response_code.databaseError().copy()
			response['custom_message'] = 'Could not delete tax'
	except Exception as e:
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def saveTax(request):
	"""
	Masters.Tax Save / Insert / Update

	:param request:
	:return:
	"""
	master_dao = MasterDAO()
	master_dao.db_session.begin(subtransactions=True)
	try:
		rh = RequestHandler(request)
		data = rh.getPostData('tax')
		if not data:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')

		tax_json = json.load(data)
		code = json_util.getValue(tax_json, 'code')
		enterprise_id = json_util.getValue(tax_json, 'enterprise_id')

		tax = master_dao.getTaxByCode(code=code, enterprise_id=enterprise_id)
		if tax is None:
			tax = Tax()

		tax.code = code
		tax.enterprise_id = enterprise_id
		tax.assess_rate = json_util.getValue(tax_json, 'assess_rate')
		tax.base_rate = json_util.getValue(tax_json, 'base_rate')

		tax.is_compound = json_util.getValue(tax_json, 'is_compound')
		tax.net_rate = json_util.getValue(tax_json, 'net_rate')
		tax.name = json_util.getValue(tax_json, 'name')

		sub_taxes = json_util.getValue(tax_json, 'sub_taxes')
		if sub_taxes is None or sub_taxes == []:
			tax.sub_taxes = []
		else:
			for sub_tax_json in sub_taxes:
				sub_tax = tax.getSubTax(json_util.getValue(sub_tax_json, 'name'))
				if sub_tax is None:
					sub_tax = SubTax()
					tax.sub_taxes.append(sub_tax)
				# No need of else here
				sub_tax.name = json_util.getValue(sub_tax_json, 'name')
				sub_tax.rate = json_util.getValue(sub_tax_json, 'rate')
		# Close of for and else
		if master_dao.saveTax(tax):
			response = response_code.success()
		else:
			response = response_code.databaseError().copy()
			response['custom_message'] = 'Could not save tax!'
		master_dao.db_session.commit()
	except Exception as e:
		master_dao.db_session.rollback()
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getSupplierPrices(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		response = response_code.success()
		response['supplier_prices'] = MasterDAO().getSupplierPrices(enterprise_id=enterprise_id)
	except Exception as e:
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def approveRate(request):
	"""

	:param request:
	:return:
	"""

	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		item_id = rh.getPostData('item_id')
		supplier_id = rh.getPostData('supplier_id')
		price = rh.getPostData('price')
		user_id = rh.getPostData('user_id')
		make_id = rh.getPostData('make_id')
		effect_since = rh.getPostData('effect_since')
		updated_since = rh.getPostData('updated_since')
		updated_till = rh.getPostData('updated_till')
		effect_till = rh.getPostData('effect_till')
		remarks = rh.getPostData('remarks')
		rate_approval_id = rh.getPostData('rate_approval_id')
		if effect_till == "":
			effect_till = None
		if updated_till == "":
			updated_till = None
		if updated_since == "":
			updated_since = None
		if MasterService().updateRateApprovalStatus(
				status=MaterialSupplierProfile.APPROVED,
				enterprise_id=enterprise_id, item_id=item_id, supplier_id=supplier_id, price=price,
				effect_since=effect_since, updated_since=updated_since, effect_till=effect_till,
				updated_till=updated_till, approved_by=user_id, remarks=remarks, make_id=make_id):
			response = response_code.success()
			response['rate_approval_id'] = rate_approval_id
		else:
			response = response_code.failure()
	except Exception as e:
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def rejectRate(request):
	"""

	:param request:
	:return:
	"""

	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		item_id = rh.getPostData('item_id')
		supplier_id = rh.getPostData('supplier_id')
		price = rh.getPostData('price')
		user_id = rh.getPostData('user_id')
		reject_remarks = rh.getPostData('reject_remarks')
		make_id = rh.getPostData('make_id')
		effect_since = rh.getPostData('effect_since')
		updated_since = rh.getPostData('updated_since')
		updated_till = rh.getPostData('updated_till')
		effect_till = rh.getPostData('effect_till')
		remarks = rh.getPostData('remarks')
		rate_approval_id = rh.getPostData('rate_approval_id')
		if effect_till == "":
			effect_till = None
		if updated_till == "":
			updated_till = None
		if updated_since == "":
			updated_since = None
		if MasterService().updateRateApprovalStatus(
				status=MaterialSupplierProfile.REJECTED, reject_remarks=reject_remarks,
				enterprise_id=enterprise_id, item_id=item_id, supplier_id=supplier_id, price=price,
				effect_since=effect_since, updated_since=updated_since, effect_till=effect_till,
				updated_till=updated_till, approved_by=user_id, remarks=remarks, make_id=make_id):
			response = response_code.success()
			response['rate_approval_id'] = rate_approval_id
		else:
			response = response_code.failure()
	except Exception as e:
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def listProjects(request):
	"""
	Masters Material

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		response = response_code.success()
		logger.info("Processing list access of Master.Project")
		response['projects'] = MasterDAO().getAllProjects(enterprise_id=enterprise_id)
	except Exception as e:
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getCurrentDate(request):
	"""
	:return:
	"""
	try:
		response = response_code.success()
		logger.info("Processing list access of Master.Tag")
		response['server_date'] = datetime.datetime.today().strftime('%Y-%m-%d')
	except Exception as e:
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getNumberOfMaterialWithNegativeStock(request):
	"""
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		response = response_code.success()
		count = helper.getNumberOfMaterialWithNegativeStock(enterprise_id=enterprise_id)
		response['count'] = count
		response['custom_message'] = """Negative minimum stock value has been configured for %s material(s). 
			Proceeding with the change, the minimum stock value will be set to 0.00 for those materials""" % count
		logger.info("Checking Negative stock materials %s" % response['custom_message'])
	except Exception as e:
		logger.exception('. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getMaterialSpecifications(request):
	"""
	Fetches the Material Details related to Quality Inspection, & Specifications

	:param request: HttpRequest with a parameter item_id - PK referring the materials table
	:return: JSON of the format
			{
				"sample_size": <sample_size>,
				"lot_size": <lot_size>,
				"reaction_plan": <item_reaction_plan>,
				"qc_method": <item_qc_method>,
				"specifications": [{
					"parameter": <param_desc>,
					"instruction": <instruction>,
					"tool": <tool_or_method>,
					"min_value": <min_value>,
					"max_value": <max_value>,
					"reaction_plan": <reaction_plan>
				},
				.
				.
				.]
			}
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		item_id = rh.getPostData('item_id')
		material = MasterDAO().getMaterialByItemId(item_id=item_id, enterprise_id=enterprise_id)
		specifications = []
		for spec in material.specifications:
			specifications.append({
				"parameter": spec.parameter, "instruction": spec.comments, "tool": spec.inspection_method,
				"min_value": float(spec.min_value) if spec.min_value else "",
				"max_value": float(spec.max_value) if spec.max_value else "",
				"unit": spec.unit, "reaction_plan": spec.reaction_plan, "qc_critical": spec.qc_critical})
		response = {
			"sample_size": float(material.sample_size) if material.sample_size else "",
			"lot_size": float(material.lot_size) if material.lot_size else "",
			"reaction_plan": material.reaction_plan, "qc_method": material.qc_method,
			"specifications": specifications}
		logger.info("Extracting information from specification and material")
	except Exception as e:
		logger.exception("Fetching Material Specifications failed - %s" % e)
		response = ""
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def loadTax(request):
		"""
		Loads the Tax and Sub-tax information upon any ajax call to add a Tax Profile to a Invoice.
		:param request:
		:return:
		"""
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getPostData('enterprise_id')
		if enterprise_id is None:
			enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_IN_SESSION_KEY).id
		try:
			tax_json = []
			taxes = InvoiceService().invoice_dao.db_session.query(
				Tax.code, Tax.name, Tax.base_rate, Tax.assess_rate, Tax.net_rate).filter(
				Tax.enterprise_id == enterprise_id).all()
			response = response_code.success()
			for tax in taxes:
				tax_json.append({
					'tax_code': tax.code, 'name': tax.name, 'base_rate': str(tax.base_rate),
					'assess_rate': str(tax.assess_rate), 'net_rate': str(tax.net_rate)})
			response['tax_list'] = tax_json
		except Exception as e:
			response = response_code.internalError()
			response['error'] = '%s' % e.message
			logger.exception('Failed to construct JSON dump for Tax...')
			raise e
		return HttpResponse(json.dumps(response), 'content-type=text/json')


def fetchFrequentlyUsedPartyAndProjects(request):
	"""
	Fetch the frequently used parties and frequently used projects to a Invoice.
	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getPostData('enterprise_id')
		frequently_used_project_list = helper. getProjectFrequents(
				table='invoice', enterprise_id=enterprise_id, condition="AND type IN ('Excise', 'BoS', 'Service', 'Trading', 'GST', 'Others')")
		frequently_used_projects=[]
		for frequently_used_project in frequently_used_project_list:
			frequently_used_projects.append({'project_code': frequently_used_project[0], 'project_name': frequently_used_project[1]})
		frequently_used_party_list = helper.getPartyFrequents(
				table='invoice', enterprise_id=enterprise_id, condition="")
		frequently_used_parties = []
		for frequently_used_party in frequently_used_party_list:
			frequently_used_parties.append({'party_id': frequently_used_party[0], 'party_name': frequently_used_party[1]})
			logger.info(frequently_used_party)
		response = response_code.success()
		response['frequently_used_projects'] = frequently_used_projects
		response['frequently_used_parties'] = frequently_used_parties
	except Exception as e:
		response = response_code.internalError()
		response['error'] = '%s' % e.message
		logger.exception('Failed to fetch frequently used party and projects...')
		raise e
	return HttpResponse(json.dumps(response), 'content-type=text/json')