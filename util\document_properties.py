from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_JUSTIFY
from reportlab.lib.fonts import tt2ps
from reportlab.lib.styles import StyleSheet1, ParagraphStyle

from util import helper

__author__ = 'ka<PERSON>vanan'

DUMMY_DOC_PATH = helper.getAbsolutePath("/site_media/tmp/dummy_%s.pdf")
DEFAULT_TARGET_FILE = helper.getAbsolutePath("/site_media/docs/doc.pdf")
CANCEL_WATERMARK_DOC_PATH = '/site_media/docs/cancelled.pdf'
HEADER_Label_Original_DOC_PATH = '/site_media/docs/original_watermark.pdf'
HEADER_Label_Supplier_DOC_PATH = '/site_media/docs/supplier_watermark.pdf'
HEADER_Label_Transport_DOC_PATH = '/site_media/docs/transport_watermark.pdf'
HEADER_Label_Extra_Copy_DOC_PATH = '/site_media/docs/extra_copy_watermark.pdf'
ELEGANT_BACKGROUND_DOC_PATH = '/site_media/docs/elegant_background.pdf'


def getStyleSheet(base_font='times'):
	"""
	Generate a Stylesheet for the specified Font, that prepares printing of various format of texts

	:param base_font:
	:return:
	"""
	stylesheet = StyleSheet1()

	stylesheet.add(ParagraphStyle(
		name='REGULAR', fgetStyleSheetontName=base_font, fontName=tt2ps(base_font, 0, 0), fontSize=10, spaceAfter=0,
		bulletFontName=tt2ps(base_font, 0, 0), bulletFontSize=10, allowWidows=1, splitLongWords=1,
		alignment=TA_JUSTIFY, bulletanchor='numeric'))
	stylesheet.add(ParagraphStyle(
		name='REGULAR_BOLD', parent=stylesheet['REGULAR'], fontName=tt2ps(base_font, 1, 0),
		bulletFontName=tt2ps(base_font, 1, 0)))
	stylesheet.add(ParagraphStyle(name='REGULAR_L5', parent=stylesheet['REGULAR'], leading=5))
	stylesheet.add(ParagraphStyle(name='REGULAR_L10', parent=stylesheet['REGULAR'], leading=10))
	stylesheet.add(ParagraphStyle(name='REGULAR_L15', parent=stylesheet['REGULAR'], leading=15))
	stylesheet.add(ParagraphStyle(
		name='REGULAR_CENTER_L5', parent=stylesheet['REGULAR'], alignment=TA_CENTER, leading=5))
	stylesheet.add(ParagraphStyle(
		name='REGULAR_RIGHT_L10', parent=stylesheet['REGULAR'], alignment=TA_RIGHT, leading=10))
	stylesheet.add(ParagraphStyle(name='REGULAR_BOLD_L10', parent=stylesheet['REGULAR_BOLD'], leading=10))
	stylesheet.add(ParagraphStyle(
		name='REGULAR_BOLD_CENTER_L5', parent=stylesheet['REGULAR_BOLD'], alignment=TA_CENTER, leading=5))

	stylesheet.add(ParagraphStyle(
		name='SMALL', fgetStyleSheetontName=base_font, fontName=tt2ps(base_font, 0, 0), fontSize=9,
		bulletFontName=tt2ps(base_font, 0, 0), bulletFontSize=9, allowWidows=1, splitLongWords=1,
		alignment=TA_JUSTIFY, bulletanchor='numeric', wordWrap='CJK'))
	stylesheet.add(ParagraphStyle(
		name='SMALL_BOLD', parent=stylesheet['SMALL'], fontName=tt2ps(base_font, 1, 0),
		bulletFontName=tt2ps(base_font, 1, 0)))
	stylesheet.add(ParagraphStyle(name='SMALL_L7', parent=stylesheet['SMALL'], leading=7))
	stylesheet.add(ParagraphStyle(name='SMALL_L9', parent=stylesheet['SMALL'], leading=9))
	stylesheet.add(ParagraphStyle(name='SMALL_CENTER', parent=stylesheet['SMALL'], alignment=TA_CENTER))
	stylesheet.add(ParagraphStyle(name='SMALL_CENTER_L9', parent=stylesheet['SMALL'], leading=9, alignment=TA_CENTER))
	stylesheet.add(ParagraphStyle(name='SMALL_CENTER_L10', parent=stylesheet['SMALL'], leading=10, alignment=TA_CENTER))
	stylesheet.add(ParagraphStyle(name='SMALL_RIGHT', parent=stylesheet['SMALL'], alignment=TA_RIGHT))
	stylesheet.add(ParagraphStyle(name='SMALL_RIGHT_L10', parent=stylesheet['SMALL'], leading=10, alignment=TA_RIGHT))
	stylesheet.add(ParagraphStyle(name='SMALL_BOLD_L8', parent=stylesheet['SMALL_BOLD'], leading=8))
	stylesheet.add(ParagraphStyle(name='SMALL_BOLD_CENTER', parent=stylesheet['SMALL_BOLD'], alignment=TA_CENTER))
	stylesheet.add(ParagraphStyle(name='SMALL_BOLD_RIGHT', parent=stylesheet['SMALL_BOLD'], alignment=TA_RIGHT))

	stylesheet.add(ParagraphStyle(
		name='TINY', fgetStyleSheetontName=base_font, fontName=tt2ps(base_font, 0, 0), fontSize=8, bulletFontSize=8,
		bulletFontName=tt2ps(base_font, 0, 0), allowWidows=1, splitLongWords=1, bulletanchor='numeric', leading=0,
		alignment=TA_JUSTIFY, wordWrap='CJK'))
	stylesheet.add(ParagraphStyle(
		name='TINY_BOLD', parent=stylesheet['TINY'], fontName=tt2ps(base_font, 1, 0), bulletFontName=tt2ps(base_font, 1, 0)))
	stylesheet.add(ParagraphStyle(name='TINY_L8', parent=stylesheet['TINY'], leading=8))
	stylesheet.add(ParagraphStyle(name='TINY_L10', parent=stylesheet['TINY'], leading=10))
	stylesheet.add(ParagraphStyle(name='TINY_L15', parent=stylesheet['TINY'], leading=15))
	stylesheet.add(ParagraphStyle(name='TINY_RIGHT_L10', parent=stylesheet['TINY'], alignment=TA_RIGHT, leading=10))
	stylesheet.add(ParagraphStyle(name='TINY_CENTER', parent=stylesheet['TINY'], alignment=TA_CENTER))
	stylesheet.add(ParagraphStyle(name='TINY_RIGHT_L8', parent=stylesheet['TINY'], alignment=TA_RIGHT, leading=8))
	stylesheet.add(ParagraphStyle(name='TINY_CENTER_L10', parent=stylesheet['TINY'], alignment=TA_CENTER, leading=10))
	stylesheet.add(ParagraphStyle(name='TINY_CENTER_L15', parent=stylesheet['TINY'], alignment=TA_CENTER, leading=15))

	stylesheet.add(ParagraphStyle(
		name='MICRO', fgetStyleSheetontName=base_font, fontName=tt2ps(base_font, 0, 0), fontSize=6, spaceAfter=0,
		bulletFontName=tt2ps(base_font, 0, 0), bulletFontSize=6, allowWidows=1, splitLongWords=1,
		alignment=TA_JUSTIFY, bulletanchor='numeric'))
	stylesheet.add(ParagraphStyle(name='MICRO_L6', parent=stylesheet['MICRO'], leading=6))

	stylesheet.add(ParagraphStyle(
		name='H2', fgetStyleSheetontName=base_font, fontName=tt2ps(base_font, 1, 0), fontSize=14, leading=0,
		bulletFontName=tt2ps(base_font, 1, 0), bulletFontSize=14, bulletanchor='numeric', spaceBefore=0, spaceAfter=0),
		alias='h2')
	stylesheet.add(ParagraphStyle(
		name='H2_RIGHT_L10', parent=stylesheet['H2'],  alignment=TA_RIGHT, leading=10), alias='h2r')
	stylesheet.add(ParagraphStyle(name='H2_CENTER', parent=stylesheet['H2'], alignment=TA_CENTER), alias='h2c')
	stylesheet.add(ParagraphStyle(
		name='H3', fgetStyleSheetontName=base_font, fontName=tt2ps(base_font, 1, 0), fontSize=12, leading=0,
		bulletFontName=tt2ps(base_font, 1, 0), bulletFontSize=12, bulletanchor='numeric', spaceBefore=0, spaceAfter=0),
		alias='h3')
	stylesheet.add(ParagraphStyle(name='H4', parent=stylesheet['REGULAR_BOLD'], leading=5), alias='h4')
	stylesheet.add(ParagraphStyle(name='H5', parent=stylesheet['SMALL_BOLD']), alias='h5')

	return stylesheet


def getInvoiceStyleSheet(base_font='roboto', font_size=10):
	"""
	Generate a Stylesheet for the specified Font, that prepares printing of various format of texts

	:param base_font:
	:param font_size:
	:return:
	"""
	stylesheet = StyleSheet1()

	stylesheet.add(ParagraphStyle(
		name='REGULAR', fgetStyleSheetontName=base_font, fontName=tt2ps(base_font, 0, 0), fontSize=font_size, spaceAfter=0,
		bulletFontName=tt2ps(base_font, 0, 0), bulletFontSize=10, allowWidows=1, splitLongWords=1,
		alignment=TA_JUSTIFY, bulletanchor='numeric'))
	stylesheet.add(ParagraphStyle(
		name='REGULAR_BOLD', parent=stylesheet['REGULAR'], fontName=tt2ps(base_font, 1, 0),
		bulletFontName=tt2ps(base_font, 1, 0)))
	stylesheet.add(ParagraphStyle(name='REGULAR_L5', parent=stylesheet['REGULAR'], leading=font_size + 2))
	stylesheet.add(ParagraphStyle(name='REGULAR_L10', parent=stylesheet['REGULAR'], leading=font_size + 2))
	stylesheet.add(ParagraphStyle(name='REGULAR_L15', parent=stylesheet['REGULAR'], leading=font_size + 2))
	stylesheet.add(ParagraphStyle(
		name='REGULAR_CENTER_L5', parent=stylesheet['REGULAR'], alignment=TA_CENTER, leading=font_size + 2))
	stylesheet.add(ParagraphStyle(
		name='REGULAR_RIGHT_L10', parent=stylesheet['REGULAR'], alignment=TA_RIGHT, leading=font_size + 2))
	stylesheet.add(ParagraphStyle(name='REGULAR_BOLD_L10', parent=stylesheet['REGULAR_BOLD'], leading=font_size + 2))
	stylesheet.add(ParagraphStyle(
		name='REGULAR_BOLD_CENTER_L5', parent=stylesheet['REGULAR_BOLD'], alignment=TA_CENTER, leading=font_size + 2))

	stylesheet.add(ParagraphStyle(
		name='SMALL', fgetStyleSheetontName=base_font, fontName=tt2ps(base_font, 0, 0), fontSize=font_size,
		bulletFontName=tt2ps(base_font, 0, 0), bulletFontSize=9, allowWidows=1, splitLongWords=1,
		alignment=TA_JUSTIFY, bulletanchor='numeric', wordWrap='CJK'))
	stylesheet.add(ParagraphStyle(
		name='SMALL_BOLD', parent=stylesheet['SMALL'], fontName=tt2ps(base_font, 1, 0),
		bulletFontName=tt2ps(base_font, 1, 0)))
	stylesheet.add(ParagraphStyle(name='SMALL_L7', parent=stylesheet['SMALL'], leading=font_size + 2))
	stylesheet.add(ParagraphStyle(name='SMALL_L9', parent=stylesheet['SMALL'], leading=font_size + 2))
	stylesheet.add(ParagraphStyle(name='SMALL_CENTER', parent=stylesheet['SMALL'], alignment=TA_CENTER))
	stylesheet.add(ParagraphStyle(name='SMALL_CENTER_L9', parent=stylesheet['SMALL'], leading=font_size + 2, alignment=TA_CENTER))
	stylesheet.add(ParagraphStyle(name='SMALL_CENTER_L10', parent=stylesheet['SMALL'], leading=font_size + 2, alignment=TA_CENTER))
	stylesheet.add(ParagraphStyle(name='SMALL_RIGHT', parent=stylesheet['SMALL'], alignment=TA_RIGHT))
	stylesheet.add(ParagraphStyle(name='SMALL_RIGHT_L10', parent=stylesheet['SMALL'], leading=font_size + 2, alignment=TA_RIGHT))
	stylesheet.add(ParagraphStyle(name='SMALL_BOLD_L8', parent=stylesheet['SMALL_BOLD'], leading=font_size + 2))
	stylesheet.add(ParagraphStyle(name='SMALL_BOLD_CENTER', parent=stylesheet['SMALL_BOLD'], alignment=TA_CENTER))
	stylesheet.add(ParagraphStyle(name='SMALL_BOLD_RIGHT', parent=stylesheet['SMALL_BOLD'], alignment=TA_RIGHT))

	stylesheet.add(ParagraphStyle(
		name='TINY', fgetStyleSheetontName=base_font, fontName=tt2ps(base_font, 0, 0), fontSize=font_size, bulletFontSize=8,
		bulletFontName=tt2ps(base_font, 0, 0), allowWidows=1, splitLongWords=1, bulletanchor='numeric', leading=font_size + 2,
		alignment=TA_JUSTIFY, wordWrap='CJK'))
	stylesheet.add(ParagraphStyle(
		name='TINY_BOLD', parent=stylesheet['TINY'], fontName=tt2ps(base_font, 1, 0), bulletFontName=tt2ps(base_font, 1, 0)))
	stylesheet.add(ParagraphStyle(name='TINY_L8', parent=stylesheet['TINY'], leading=font_size + 2))
	stylesheet.add(ParagraphStyle(name='TINY_L10', parent=stylesheet['TINY'], leading=font_size + 2))
	stylesheet.add(ParagraphStyle(name='TINY_L15', parent=stylesheet['TINY'], leading=font_size + 2))
	stylesheet.add(ParagraphStyle(name='TINY_RIGHT_L10', parent=stylesheet['TINY'], alignment=TA_RIGHT, leading=font_size + 2))
	stylesheet.add(ParagraphStyle(name='TINY_CENTER', parent=stylesheet['TINY'], alignment=TA_CENTER))
	stylesheet.add(ParagraphStyle(name='TINY_RIGHT_L8', parent=stylesheet['TINY'], alignment=TA_RIGHT, leading=font_size + 2))
	stylesheet.add(ParagraphStyle(name='TINY_CENTER_L10', parent=stylesheet['TINY'], alignment=TA_CENTER, leading=font_size + 2))
	stylesheet.add(ParagraphStyle(name='TINY_CENTER_L15', parent=stylesheet['TINY'], alignment=TA_CENTER, leading=font_size + 2))

	stylesheet.add(ParagraphStyle(
		name='MICRO', fgetStyleSheetontName=base_font, fontName=tt2ps(base_font, 0, 0), fontSize=font_size, spaceAfter=0,
		bulletFontName=tt2ps(base_font, 0, 0), bulletFontSize=6, allowWidows=1, splitLongWords=1,
		alignment=TA_JUSTIFY, bulletanchor='numeric'))
	stylesheet.add(ParagraphStyle(name='MICRO_L6', parent=stylesheet['MICRO'], leading=font_size + 2))

	stylesheet.add(ParagraphStyle(
		name='H2', fgetStyleSheetontName=base_font, fontName=tt2ps(base_font, 1, 0), fontSize=font_size, leading=font_size + 2,
		bulletFontName=tt2ps(base_font, 1, 0), bulletFontSize=14, bulletanchor='numeric', spaceBefore=0, spaceAfter=0),
		alias='h2')
	stylesheet.add(ParagraphStyle(
		name='H2_RIGHT_L10', parent=stylesheet['H2'],  alignment=TA_RIGHT, leading=font_size + 2), alias='h2r')
	stylesheet.add(ParagraphStyle(name='H2_CENTER', parent=stylesheet['H2'], alignment=TA_CENTER), alias='h2c')
	stylesheet.add(ParagraphStyle(
		name='H3', fgetStyleSheetontName=base_font, fontName=tt2ps(base_font, 1, 0), fontSize=font_size, leading=font_size + 2,
		bulletFontName=tt2ps(base_font, 1, 0), bulletFontSize=12, bulletanchor='numeric', spaceBefore=0, spaceAfter=0),
		alias='h3')
	stylesheet.add(ParagraphStyle(name='H4', parent=stylesheet['REGULAR_BOLD'], leading=font_size + 2), alias='h4')
	stylesheet.add(ParagraphStyle(name='H5', parent=stylesheet['SMALL_BOLD']), alias='h5')

	return stylesheet
