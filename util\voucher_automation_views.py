import json
import os
import logging

from django.http import HttpResponse

from erp.auth.request_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from erp.accounts import GENERAL_VOUCHER, CASH_VOUCHER, BANK_VOUCHER, PURCHASE_VOUCHER, SALES_VOUCHER, NOTE_VOUCHER
from util import LOG_HANDLER
from util.voucher_automation_backend import VoucherService

# Set up logger
Voucher_logger = logging.getLogger(os.path.splitext(os.path.basename(__file__))[0])
Voucher_logger.setLevel(logging.DEBUG)
Voucher_logger.addHandler(LOG_HANDLER)


def clone_service(request):
    response = {"Status_code": 200, "response_message": "successfully cloned"}
    request_handler = RequestHandler(request)
    voucher_service = VoucherService()
    voucher_id = request_handler.getPostData("voucher_id")
    voucher_type = int(request_handler.getPostData("type"))
    enterprise_id = int(request_handler.getPostData("enterprise_id"))
    Voucher_logger.info("Cloning initiated for the Voucher: %s and type: %s" % (voucher_id, voucher_type))
    try:
        if voucher_service.child_voucher_is_exist(voucher_id=voucher_id, enterprise_id=enterprise_id) is False:
            if voucher_type in (PURCHASE_VOUCHER, SALES_VOUCHER, NOTE_VOUCHER):
                is_cloned = voucher_service.clone_sales_or_purchase_voucher(voucher_id=voucher_id,
                                                                            enterprise_id=enterprise_id)
            elif voucher_type in (CASH_VOUCHER, BANK_VOUCHER):
                is_cloned = voucher_service.clone_cash_or_bank_voucher(voucher_id=voucher_id, enterprise_id=enterprise_id)
            elif voucher_type == GENERAL_VOUCHER:
                is_cloned = voucher_service.clone_general_voucher(voucher_id=voucher_id, enterprise_id=enterprise_id)
            else:
                response = {"Status_code": 400, "response_message": "Invalid Voucher Type"}
                return HttpResponse(json.dumps(response), status=400)
            if is_cloned:
                response["data"] = {"clone_status": is_cloned}
                return HttpResponse(json.dumps(response), status=200)
            else:
                response = {"Status_code": 400, "response_message": "Bad Request", "data": {"clone_status": is_cloned}}
                return HttpResponse(json.dumps(response), status=400)
        else:
            response = {"Status_code": 400, "response_message": "Already Exists"}
            return HttpResponse(json.dumps(response), status=400)
    except Exception as e:
        response = {"Status_code": 500, "response_message": "Internal Server Error"}
        Voucher_logger.info("failed to clone the [sales, purchase, general, note] vouchers: %s", e)
        return HttpResponse(json.dumps(response), status=500)


def update_child_voucher(request):
    response = {"Status_code": 200, "response_message": "successfully Updated"}
    request_handler = RequestHandler(request)
    voucher_service = VoucherService()
    parent_voucher_id = request_handler.getPostData("parent_voucher_id")
    try:
        update_status = voucher_service.update_voucher(voucher_id=parent_voucher_id)
        if update_status:
            return HttpResponse(json.dumps(response), status=200)
        else:
            response = {"Status_code": 400, "response_message": "Bad Request"}
            return HttpResponse(json.dumps(response), status=400)
    except Exception as e:
        response = {"Status_code": 500, "response_message": "Internal Server Error"}
        Voucher_logger.info("failed to update the child vouchers: %s", e)
        return HttpResponse(json.dumps(response), status=500)


def get_pending_vouchers(request):
    response = {"Status_code": 200, "response_message": "Data fetched successfully"}
    request_handler = RequestHandler(request)
    voucher_service = VoucherService()
    parent_enterprise_id = request_handler.getData("parent_enterprise_id")
    voucher_date = request_handler.getData("voucher_date")
    project_code = request_handler.getData("project_code")
    try:
        pending_vouchers_list = voucher_service.fetch_clone_pending_vouchers(parent_enterprise_id=parent_enterprise_id,
                                                                             voucher_date=voucher_date,
                                                                             project_code=project_code)
        response["data"] = pending_vouchers_list
        return HttpResponse(json.dumps(response), status=200)
    except Exception as e:
        response = {"Status_code": 500, "response_message": "Internal Server Error"}
        Voucher_logger.info("failed to get the pending vouchers: %s", e)
        return HttpResponse(json.dumps(response), status=500)
