import base64
import datetime
from decimal import Decimal
from math import ceil

import pdfkit
from django.template import Context, Template
from django.template.loader import get_template

from document_properties import *
from erp import helper, DEFAULT_MAKE_ID
from erp.admin.backend import EnterpriseProfileService
from erp.admin.backend import PurchaseTemplateConfigService
from erp.masters.backend import MasterService
from erp.models import PurchaseOrder
from erp.purchase import logger
from util.document_compiler import PDFGenerator
from util.document_properties import getStyleSheet
from util.helper import getAbsolutePath, writeFile

PO_DOC_FOOTER_PATH = '/site_media/tmp/po_footer_%s.html'
PO_GENERAL_TEMPLATE = 'general_res'
PO_HEADER_TEMPLATE = 'header_res'
PO_ITEM_TEMPLATE = 'item_res'
PO_SUMMARY_TEMPLATE = 'summary_res'
PO_MISC_TEMPLATE = 'misc_res'

__author__ = 'kalaivanan'

styles = getStyleSheet()


class PoPDFGenerator(PDFGenerator):
	"""

	"""

	_PAYMENT_MODES = {0: "", 1: 'PDC', 2: 'Cheque', 3: 'Cash', 4: 'DD', 5: 'Bank Transfer'}
	_PAYMENT_TYPES = {0: "", 1: 'No of Days', 2: 'Proforma Invoice', 3: 'Cash on Delivery', 4: 'Advance'}

	def __init__(self, purchase_order=None, target_file_path=TARGET_PATH):
		super(PoPDFGenerator, self).__init__(target_file_path)
		self.purchase_order = purchase_order

	def getPOMaterialDetails(self, source=None):
		available_materials = []
		available_taxes = []
		ds_details = None
		material_make = ""
		part_no = ""
		total_qty = Decimal(0)
		for material in source.items:
			material_drawing_no = ""
			if material.material.drawing_no:
				material_drawing_no = material.material.drawing_no
			material_name = material.material.name
			if material.material and material.material.makes_json != "":
				make_name = helper.constructDifferentMakeName(material.material.makes_json)
				if make_name:
					material_name = material_name + " [" + make_name + "]"
			material_description = material.material.description
			if material.make_id != DEFAULT_MAKE_ID:
				part_no = helper.getMakePartNumber(
					enterprise_id=material.enterprise_id, item_id=material.item_id, make_id=material.make_id)
				material_make = material.make.__repr__()
			hsn_code = material.material.tariff_no if material.material.tariff_no and material.material.tariff_no != "" else ""
			material_quantity = material.quantity
			material_unit = material.material.unit.unit_name if material.material.unit else ""
			material_rate = material.rate
			if material.alternate_unit_id:
				scale_factor = helper.getScaleFactor(
					enterprise_id=material.enterprise_id, item_id=material.item_id,
					alternate_unit_id=material.alternate_unit_id)
				if scale_factor:
					material_unit = helper.getUnitName(enterprise_id=material.enterprise_id, unit_id=material.alternate_unit_id)
					material_quantity = material.quantity / scale_factor
					material_rate = Decimal(material.rate) * Decimal(scale_factor)

			total_qty += Decimal(material_quantity)
			material_discount = material.discount
			material_taxable_value = "{:0.2f}".format(Decimal(material_rate) * material_quantity * (100 - material.discount) / 100)

			cgst_rate, sgst_rate, igst_rate = 0, 0, 0
			cgst_value, sgst_value, igst_value = 0, 0, 0

			if material.getTaxesOfType("CGST")[0]:
				cgst_rate = material.getTaxesOfType("CGST")[0].tax.net_rate
				cgst_value = Decimal(material_quantity) * Decimal(material_rate) * Decimal(
					100 - material.discount) * Decimal(cgst_rate) / (100 * 100)
			if material.getTaxesOfType("SGST")[0]:
				sgst_rate = material.getTaxesOfType("SGST")[0].tax.net_rate
				sgst_value = Decimal(material_quantity) * Decimal(material_rate) * Decimal(
					100 - material.discount) * Decimal(sgst_rate) / (100 * 100)
			if material.getTaxesOfType("IGST")[0]:
				igst_rate = material.getTaxesOfType("IGST")[0].tax.net_rate
				igst_value = Decimal(material_quantity) * Decimal(material_rate) * Decimal(
					100 - material.discount) * Decimal(igst_rate) / (100 * 100)
			if material.make_id:
				ds_details = helper.getPOMDeliverySchedules(
					po_id=source.po_id, enterprise_id=material.enterprise_id, item_id=material.item_id, make_id=material.make_id)

			po_material = {
				"material_drawing_no": material_drawing_no, "material_name": material_name, "material_make": material_make,
				"part_no": part_no, "material_description": material_description, "hsn_code": hsn_code, "material_quantity": material_quantity, "material_unit": material_unit,
				"material_rate": "%0.5f" % material_rate, "material_discount": "%0.2f" % material_discount,
				"cgst_rate": "%0.2f" % cgst_rate, "sgst_rate": "%0.2f" % sgst_rate, "igst_rate": "%0.2f" % igst_rate,
				"cgst_value": "%0.2f" % cgst_value, "sgst_value": "%0.2f" % sgst_value, "igst_value": "%0.2f" % igst_value,
				"material_taxable_value": material_taxable_value, "ds_material_details": ds_details}
			available_materials.append(po_material)

		sorted_taxes = self.purchase_order.getTaxes()

		# Tax Calculation
		tax_values = source.getTaxValues()
		for po_tax in sorted_taxes:
			taxes = {'tax_name': po_tax.tax.name, 'tax_rate': po_tax.tax.net_rate, 'tax_value': round(tax_values[po_tax.tax.code], 2)}
			available_taxes.append(taxes)
		return available_materials, total_qty, available_taxes

	def generatePDF(self, source=None, template_config=None, doc_status="", logged_in_user=None, updated_banner_image=None):
		try:
			profile_service = EnterpriseProfileService()
			countries = MasterService().getCountries()
			po_template_service = PurchaseTemplateConfigService()
			logger.info('Generating PDF for PO: %s' % self.purchase_order)

			template_general_details = template_config["general_config"]
			template_header_details = template_config["header_config"]
			template_item_details = template_config["items_config"]
			template_summary_details = template_config["summary_config"]
			template_misc_details = template_config["misc_config"]
			po_template = template_config["print_template"]

			po_doc_datetime_formatter = template_general_details["datetime"]

			if source.enterprise.images and source.enterprise.images.logo and source.enterprise.images.logo != "":
				logo = "data:image/%s;base64,%s" % (source.enterprise.images.ext, base64.encodestring(source.enterprise.images.logo))
			else:
				logo = ""
			if self.purchase_order.type:
				po_name = template_header_details["form_name"]["label"]["jo"]
				mail_subject = JOB_MAIL_SUBJECT
				body = "We are happy to place Job-order for the following on your organisation. Please comply to our Specifications/Requirements given to you, if any."
			else:
				po_name = template_header_details["form_name"]["label"]["po"]
				mail_subject = template_misc_details["sub"]
				body = template_misc_details["mail_body"]

			country_list = []
			enterprise_country = ""
			for country in countries:
				if source.enterprise.country_code == country.code:
					enterprise_country = country.name
				country_list.append({"country_code": country.code, "country_name": country.name})

			if source.enterprise.address_2 and source.enterprise.address_2 != "":
				address = source.enterprise.address_1 + ", " + source.enterprise.address_2
			else:
				address = source.enterprise.address_1

			enterprise_address = "{address}, {city} - {pin_code}, {state}, {country}".format(
				address=address, city=source.enterprise.city, pin_code=source.enterprise.pin_code, state=source.enterprise.state,
				country=enterprise_country.upper())
			included_reg_items = template_header_details["include_reg_items"]
			po_registration_details = []
			for item in included_reg_items:
				reg_detail = profile_service.profile_dao.getEnterpriseRegistrationDetail(
					enterprise_id=source.enterprise.id, label=item["label"])
				po_registration_details.append(reg_detail)

			po_date = str(datetime.datetime.strptime(str(source.last_modified_on), '%Y-%m-%d %H:%M:%S').strftime(str(po_doc_datetime_formatter))) if source.last_modified_on is not None and source.last_modified_on != '0000-00-00 00:00:00' else source.last_modified_on
			if source.status == PurchaseOrder.STATUS_APPROVED:
				po_date = str(datetime.datetime.strptime(str(source.approved_on), '%Y-%m-%d %H:%M:%S').strftime(str(po_doc_datetime_formatter))) if source.approved_on is not None and source.approved_on != '0000-00-00 00:00:00' else source.approved_on
			quotation_date = datetime.datetime.strptime(str(source.quotation_date).replace(' 00:00:00', ""), '%Y-%m-%d').date() if source.quotation_date is not None else ""
			valid_since = str(datetime.datetime.strptime(str(source.valid_since), '%Y-%m-%d %H:%M:%S').strftime(str(po_doc_datetime_formatter))) if source.valid_since is not None and source.valid_since != '0000-00-00 00:00:00' else source.valid_since
			valid_till = str(datetime.datetime.strptime(str(source.valid_till), '%Y-%m-%d %H:%M:%S').strftime(str(po_doc_datetime_formatter))) if source.valid_till is not None and source.valid_till != '0000-00-00 00:00:00' else source.valid_till
			if source.indent:
				indent_date = str(datetime.datetime.strptime(str(source.indent.raised_date), '%Y-%m-%d %H:%M:%S').strftime(str(po_doc_datetime_formatter))) if source.indent.raised_date is not None and source.indent.raised_date != '0000-00-00 00:00:00' else source.indent.raised_date
			else:
				indent_date = None
			if self.purchase_order.type == 1:
				po_item_desc_header_1 = "Job/Service Order for"
				po_item_desc_header_2 = "Drawing No./Description"
			else:
				po_item_desc_header_1 = DRAWING_NO_LABEL
				po_item_desc_header_2 = DESCRIPTION_LABEL
			po_item_details, total_quantity, po_taxes = self.getPOMaterialDetails(source=source)

			tax_summary = source.getTaxSummary(po_materials=source.items)
			total_in_words = PDFGenerator.getTotalInWords(value=abs(source.total), currency=source.currency)

			po_delivery_materials = []
			if source.enterprise.is_delivery_schedule:
				for ds_item in po_item_details:
					for item in ds_item['ds_material_details']:
						due_date = datetime.datetime.strptime(str(item['due_date']), '%Y-%m-%d').date() if item['due_date'] is not None else ""
						updated_delivery_materials = {
							'material_name': '%s %s' % (ds_item['material_name'], ds_item['material_make']),
							'qty': item['qty'], 'due_date': due_date}

						po_delivery_materials.append(updated_delivery_materials)

			total_value = '%0.2f' % source.getTotalMaterialValue()
			total_cgst_value = '%0.2f' % source.getNetMaterialTaxForType("CGST")
			total_sgst_value = '%0.2f' % source.getNetMaterialTaxForType("SGST")
			total_igst_value = '%0.2f' % source.getNetMaterialTaxForType("IGST")

			item_count = len(source.items)
			logger.info('No of Materials: %s' % item_count)
			appendix_pages = 0
			tax_count = ceil(len(source.taxes) / 3.0)
			logger.info("The Total Tax Count %s:" % tax_count)
			if (item_count + tax_count) >= 4:
				# Approximate space estimate to decide if appendix page is necessary
				appendix_pages = ceil(item_count / 15.0)

			payment_terms = "%s%% %s through %s" % (
				self.purchase_order.payment,
				'in %s days' % self.purchase_order.pay_in_days if self.purchase_order.pay_against == 1
				else 'against %s' % self._PAYMENT_TYPES[self.purchase_order.pay_against],
				self._PAYMENT_MODES[self.purchase_order.pay_through])

			po_delivery = datetime.datetime.strptime(str(self.purchase_order.delivery).replace(' 00:00:00', ""), '%Y-%m-%d').date() if self.purchase_order.delivery is not None else ""
			po_terms = template_misc_details["notes"]
			po_drafter = source.drafter.__repr__()
			if source.drafter and source.drafter.images and source.drafter.images.signature and source.drafter.images.signature != "":
				drafter_signature = "data:image/%s;base64,%s" % (source.drafter.images.signature_ext, base64.encodestring(source.drafter.images.signature))
			else:
				drafter_signature = ""
			po_approver = '-NA-' if self.purchase_order.approver is None else self.purchase_order.modified_by.__repr__()
			if source.approver and source.approver.images and source.approver.images.signature and source.approver.images.signature != "":
				approver_signature = "data:image/%s;base64,%s" % (source.approver.images.signature_ext, base64.encodestring(source.approver.images.signature))
			else:
				approver_signature = ""
			hsn_summary = []

			if template_summary_details["hsn_summary"]["print"]:
				consolidated_hsn_summary = self.getPOTemplateHSNSummary(source=source)

				for summary_key in consolidated_hsn_summary:
					hsn_summary.append(consolidated_hsn_summary[summary_key])

			header_left_image = ""
			header_center_image = ""
			header_right_image = ""
			footer_left_image = ""
			footer_center_image = ""
			footer_right_image = ""
			header_image_height = template_misc_details["banner_header"]["height"]
			footer_image_height = template_misc_details["banner_footer"]["height"]
			is_header_banner_image = False
			is_footer_banner_image = False
			header_left_image_width = 0
			header_center_image_width = 0
			header_right_image_width = 0
			footer_left_image_width = 0
			footer_center_image_width = 0
			footer_right_image_width = 0

			if doc_status == "PREVIEW":
				if updated_banner_image:
					for banner in updated_banner_image:
						if banner['section'] == 'Header':
							if banner['banner_image']:
								is_header_banner_image = True
							if banner['position'] == 'left':
								header_left_image = banner['banner_image']
								header_left_image_width = banner['width']
							if banner['position'] == 'center':
								header_center_image = banner['banner_image']
								header_center_image_width = banner['width']
							if banner['position'] == 'right':
								header_right_image = banner['banner_image']
								header_right_image_width = banner['width']

						if banner['section'] == 'Footer':
							if banner['banner_image']:
								is_footer_banner_image = True
							if banner['position'] == 'left':
								footer_left_image = banner['banner_image']
								footer_left_image_width = banner['width']
							if banner['position'] == 'center':
								footer_center_image = banner['banner_image']
								footer_center_image_width = banner['width']
							if banner['position'] == 'right':
								footer_right_image = banner['banner_image']
								footer_right_image_width = banner['width']
			else:
				for attachment in template_misc_details["banner_header"]["attachment"]:
					banner_image, attachments = po_template_service.purchase_template_dao.getBannerAttachment(
								attachment_id=attachment["attachment_id"], enterprise_id=source.enterprise.id)
					banner_image_base64 = "data:image/%s;base64,%s" % (
								attachments.file_ext, banner_image.encode('base64')) if banner_image else ""
					if banner_image:
						is_header_banner_image = True
					if attachment["position"] == 'left':
						header_left_image = banner_image_base64
						header_left_image_width = attachment['width']
					if attachment["position"] == 'center':
						header_center_image = banner_image_base64
						header_center_image_width = attachment['width']
					if attachment["position"] == 'right':
						header_right_image = banner_image_base64
						header_right_image_width = attachment['width']

				for attachment in template_misc_details["banner_footer"]["attachment"]:
					banner_image, attachments = po_template_service.purchase_template_dao.getBannerAttachment(
								attachment_id=attachment["attachment_id"], enterprise_id=source.enterprise.id)
					banner_image_base64 = "data:image/%s;base64,%s" % (
								attachments.file_ext, banner_image.encode('base64')) if banner_image else ""
					if banner_image:
						is_footer_banner_image = True
					if attachment["position"] == 'left':
						footer_left_image = banner_image_base64
						footer_left_image_width = attachment['width']
					if attachment["position"] == 'center':
						footer_center_image = banner_image_base64
						footer_center_image_width = attachment['width']
					if attachment["position"] == 'right':
						footer_right_image = banner_image_base64
						footer_right_image_width = attachment['width']
			if not is_header_banner_image:
				header_image_height = 0

			if is_footer_banner_image:
				banner_margin = (int(footer_image_height) * 0.2645833333)
			else:
				banner_margin = 0
				footer_image_height = 0

			context = Context({
				'logged_in_user': logged_in_user, 'form_name': po_name, 'enterprise_logo': logo,
				'enterprise_address': enterprise_address, 'source': source, 'doc_status': doc_status,
				'po_reg_details': po_registration_details, 'po_date': po_date, 'quotation_date': quotation_date,
				'indent_date': indent_date, 'subject': mail_subject, 'body': body, "po_item_desc_header_1": po_item_desc_header_1,
				"po_item_desc_header_2": po_item_desc_header_2, 'po_item_details': po_item_details,
				'po_delivery_materials': po_delivery_materials, 'total_quantity': total_quantity,
				'total_value': total_value, 'total_cgst_value': total_cgst_value, 'total_sgst_value': total_sgst_value,
				'total_igst_value': total_igst_value, 'po_taxes': po_taxes, 'payment_terms': payment_terms,
				'po_delivery': po_delivery, 'po_terms': po_terms, 'po_drafter': po_drafter, 'po_approver': po_approver,
				'appendix_pages': appendix_pages, 'valid_since': valid_since, 'valid_till': valid_till,
				PO_GENERAL_TEMPLATE: template_general_details, PO_HEADER_TEMPLATE: template_header_details,
				PO_ITEM_TEMPLATE: template_item_details, PO_SUMMARY_TEMPLATE: template_summary_details,
				PO_MISC_TEMPLATE: template_misc_details, 'tax_summary': tax_summary, 'hsn_summary': hsn_summary,
				'header_left_image': header_left_image, 'header_center_image': header_center_image,
				'header_right_image': header_right_image, 'footer_left_image': footer_left_image,
				'footer_center_image': footer_center_image, 'footer_right_image': footer_right_image,
				'header_image_size': header_image_height, 'footer_image_size': footer_image_height,
				'header_left_image_width': header_left_image_width, 'header_center_image_width': header_center_image_width,
				'header_right_image_width': header_right_image_width, 'footer_left_image_width': footer_left_image_width,
				'footer_center_image_width': footer_center_image_width, 'footer_right_image_width': footer_right_image_width,
				'country_list': country_list, 'drafter_signature': drafter_signature, 'approver_signature': approver_signature,
				'total_in_words': total_in_words, 'template_title': "PO Document", 'round_off': source.round_off,
				'shipping_name': source.shipping_name, 'shipping_address': source.shipping_address})

			if self.purchase_order.type != 2:
				footer_data = get_template(getAbsolutePath('/templates/admin/print_template/po/document/common/po_template_footer.html')).render(context)
				footer_file_name = PO_DOC_FOOTER_PATH % source.po_id
				writeFile(footer_data, footer_file_name)
			else:
				footer_file_name = PO_DOC_FOOTER_PATH % source.po_id
				writeFile("", footer_file_name)

			bottom_margin = int(template_general_details["margin"]["bottom"]) + 30 + banner_margin

			page_title = "Preview" if doc_status == "PREVIEW" else source.getCode()

			if template_misc_details["print_pageno"]:
				options = {
					'page-size': 'A4', 'orientation': 'Portrait',
					'title': page_title,
					'margin-top': '%smm' % template_general_details["margin"]["top"],
					'margin-right': '%smm' % template_general_details["margin"]["right"],
					'margin-bottom': '%smm' % bottom_margin,
					'margin-left': '%smm' % template_general_details["margin"]["left"], 'encoding': "UTF-8",
					'--header-right': 'Page [page] of [topage]',
					'--header-font-size': '8', 'footer-html': getAbsolutePath(footer_file_name)
				}
			else:
				options = {
					'page-size': 'A4', 'orientation': 'Portrait',
					'title': page_title,
					'margin-top': '%smm' % template_general_details["margin"]["top"],
					'margin-right': '%smm' % template_general_details["margin"]["right"],
					'margin-bottom': '%smm' % bottom_margin,
					'margin-left': '%smm' % template_general_details["margin"]["left"], 'encoding': "UTF-8",
					'footer-html': getAbsolutePath(footer_file_name)
				}

			css = [
			getAbsolutePath('/site_media/css/bootstrap.css'), getAbsolutePath('/site_media/css/po_template_preview.css'),
			getAbsolutePath('/site_media/css/po_template.css')]

			template = Template(po_template).render(context)
			data = pdfkit.from_string(template, False, options=options, css=css)
			return data
		except Exception as e:
			logger.exception('Something went wrong while creating PDF self.canvas - %s' % e)

	def getPOTemplateHSNSummary(self, source=None):
		available_materials = {}
		try:
			for material in source.items:

				cgst_rate, sgst_rate, igst_rate = 0, 0, 0
				if material.getTaxesOfType("CGST")[0]:
					cgst_rate = material.getTaxesOfType("CGST")[0].tax.net_rate

				if material.getTaxesOfType("SGST")[0]:
					sgst_rate = material.getTaxesOfType("SGST")[0].tax.net_rate

				if material.getTaxesOfType("IGST")[0]:
					igst_rate = material.getTaxesOfType("IGST")[0].tax.net_rate
				hsn_code = material.material.tariff_no if material.material.tariff_no and material.material.tariff_no != "" else ""
				item_key = "%s,%s,%s,%s" % (
				round(cgst_rate, 2), round(sgst_rate, 2), round(igst_rate, 2), hsn_code)
				consolidated_quantity = material.quantity
				if item_key in available_materials.keys():
					consolidated_cgst_rate = cgst_rate
					consolidated_sgst_rate = sgst_rate
					consolidated_igst_rate = igst_rate
					consolidated_cgst_value = available_materials[item_key][
												  "consolidated_cgst_value"] + source.getConsolidatedTaxValue(
						rate=cgst_rate, item_quantity=consolidated_quantity, item_rate=material.rate,
						item_discount=material.discount)
					consolidated_sgst_value = available_materials[item_key][
												  "consolidated_sgst_value"] + source.getConsolidatedTaxValue(
						rate=sgst_rate, item_quantity=consolidated_quantity, item_rate=material.rate,
						item_discount=material.discount)
					consolidated_igst_value = available_materials[item_key][
												  "consolidated_igst_value"] + source.getConsolidatedTaxValue(
						rate=igst_rate, item_quantity=consolidated_quantity, item_rate=material.rate,
						item_discount=material.discount)
					consolidated_taxable_value = available_materials[item_key]["consolidated_taxable_value"] + (
							material.rate * material.quantity * (100 - material.discount) / 100)
					hsn_summary = {
						"consolidated_taxable_value": consolidated_taxable_value,
						"hsn_code": hsn_code,
						"consolidated_cgst_rate": consolidated_cgst_rate,
						"consolidated_sgst_rate": consolidated_sgst_rate,
						"consolidated_igst_rate": consolidated_igst_rate,
						"consolidated_cgst_value": consolidated_cgst_value,
						"consolidated_sgst_value": consolidated_sgst_value,
						"consolidated_igst_value": consolidated_igst_value}
					available_materials[item_key] = hsn_summary
				else:
					consolidated_cgst_rate = cgst_rate
					consolidated_sgst_rate = sgst_rate
					consolidated_igst_rate = igst_rate
					consolidated_cgst_value = source.getConsolidatedTaxValue(
						rate=cgst_rate, item_quantity=consolidated_quantity, item_rate=material.rate,
						item_discount=material.discount)
					consolidated_sgst_value = source.getConsolidatedTaxValue(
						rate=sgst_rate, item_quantity=consolidated_quantity, item_rate=material.rate,
						item_discount=material.discount)
					consolidated_igst_value = source.getConsolidatedTaxValue(
						rate=igst_rate, item_quantity=consolidated_quantity, item_rate=material.rate,
						item_discount=material.discount)
					consolidated_taxable_value = material.rate * material.quantity * (
								100 - material.discount) / 100
					hsn_summary = {
						"consolidated_taxable_value": consolidated_taxable_value,
						"hsn_code": hsn_code,
						"consolidated_cgst_rate": consolidated_cgst_rate,
						"consolidated_sgst_rate": consolidated_sgst_rate,
						"consolidated_igst_rate": consolidated_igst_rate,
						"consolidated_cgst_value": consolidated_cgst_value,
						"consolidated_sgst_value": consolidated_sgst_value,
						"consolidated_igst_value": consolidated_igst_value}
					available_materials[item_key] = hsn_summary

			return available_materials
		except Exception as e:
			logger.exception("Failed to consolidate hsn summary... %s" % e.message)
