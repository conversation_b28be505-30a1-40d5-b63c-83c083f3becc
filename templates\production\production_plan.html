{% extends "production/sidebar.html" %}
{% block manufacturingIndent %}
<style xmlns="http://www.w3.org/1999/html">
#cattable_3 tbody tr td .tree-view {
	position: relative;
}

#cattable_3 tbody tr td .tree-view:before {
	border-bottom: 1px solid #bbb;
	content: "";
	left: 6px;
	position: absolute;
	top: 6px;
	width: 1.5em;
}

#cattable_3 tbody tr td .tree-view:after {
	border-left: 1px solid #bbb;
	content: "";
	left: 6px;
	position: absolute;
	top: -25px;
	height: 4em;
}

#cattable_3 .fa-plus:before,
#cattable_3 .fa-minus:before {
	border: solid 1px #333;
	padding: 2px 3px 2px 3px;
	border-radius: 3px;
	font-size: 10px;
}

table {
    border-collapse: collapse;
}

tr.strikeout td:not(.bom-sno) {
    text-decoration: line-through !important;
    opacity: 0.5;
}
    .chosen-container .chosen-drop{
        min-width:160px !important;
    }
    .component_location label{
        display: none;
    }
    	.disabled-field {
    pointer-events: none;
	}

</style>

    {% if logged_in_user|canView:'PRODUCTION' %}
    <link rel="stylesheet" href="/site_media/css/production_plan.css?v={{ current_version }}">
    <script type="text/javascript" src="/site_media/js/usage_report.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/production_plan.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/Component/locations.js?v={{ current_version }}"></script>
    <div class="right-content-container">
        <div class="page-title-container">
            <span class="page-title">Production Plan</span>
            <input type="hidden" id="pp_permission" value="{{logged_in_user|canApprove:'PRODUCTION'}}" />
            <input type="hidden" id="stores_permission" value="{{logged_in_user|canApprove:'STORES'}}" />
            <input type="hidden" id="purchase_permission" value="{{logged_in_user|canEdit:'PURCHASE'}}" />
        </div>
        <div class="filter-components" style="width: calc(100% - 428px); margin-left: 340px; margin-bottom: -12px; margin-top: 25px;">
            <div class="filter-components-container">
                <div class="dropdown">
                <button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
                        <i class="fa fa-filter"></i>
                    </button>
                    <span class="dropdown-menu arrow_box arrow_box_filter">
                        <div class="col-sm-12 form-group" >
                            <label>Date Range</label>
                            <div id="reportrange" class="report-range form-control">
                                <i class="glyphicon glyphicon-calendar"></i>&nbsp;
                                <span></span> <b class="caret"></b>
                                <input type="hidden" class="fromdate" id="fromdate" name="fromdate" />
                                <input type="hidden" class="todate" id="todate" name="todate" />
                            </div>
                        </div>
                        <div class="col-sm-12 form-group">
                            <label>Assigned To</label>
                            <select class="form-control chosen-select" name="select" id="filter_assigned_to">
                                <option value="all">ALL</option>
                                <optgroup label="Internal"></optgroup>
                                <optgroup label="External"></optgroup>
                            </select>
                        </div>
                        <div class="col-sm-6 form-group filter-status">
                            <h4 style="margin: 0">Status</h4>
                            <div class="checkbox" style="padding-left: 22px !important;">
                                <input name="yet_to_start" id="yet_to_start" type="checkbox" checked data-value="1" />
                                <label for="yet_to_start">Yet to Start</label>
                            </div>
                            <div class="checkbox" style="padding-left: 22px !important;">
                                <input name="in_progress" id="in_progress" type="checkbox" checked data-value="2" />
                                <label for="in_progress">In Progress</label>
                            </div>
                            <div class="checkbox" style="padding-left: 22px !important;">
                                <input name="completed" id="completed" type="checkbox" data-value="4" />
                                <label for="completed">Completed</label>
                            </div>
                        </div>
                        <div class="col-sm-6 form-group filter-timeliness">
                            <h4 style="margin: 0">Timeliness</h4>
                            <div class="checkbox" style="padding-left: 22px !important;">
                                <input name="overdue" id="overdue" type="checkbox" data-value="1" />
                                <label for="overdue">Overdue</label>
                            </div>
                            <div class="checkbox" style="padding-left: 22px !important;">
                                <input name="on_time" id="on_time" type="checkbox" data-value="2" />
                                <label for="on_time">On Time</label>
                            </div>
                        </div>
                        <div class="filter-footer">
                            <input type="button" class="btn btn-save" value="Apply" onclick="applyPpFilter();"/>
                        </div>
                    </span>
                </div>
                <span class='filtered-condition filtered-date'>Date: <b></b></span>
                <span class='filtered-condition filtered-assigned-to'>Assigned To: <b></b></span>
                <span class='filtered-condition filtered-status'>Status: <b></b></span>
                <span class='filtered-condition filtered-timeliness'>Timeliness: <b></b></span>
            </div>
        </div>
        <div class="service_flag" style="margin-top: -15px; user-select: auto; position: absolute; right: 300px;">
             <span class="service-item-flag"></span> - Service

        </div>
        <div class="service_flag" style="margin-top: -30px; user-select: auto; position: absolute; right: 65px;">
            <button type="button" class="btn btn-save" id="save_material_list">Material requirement calculator</button>
        </div>
        <div class="col-sm-12">
            <input hidden="hidden" value="{{ csrf_token }}" id="csrf_token">
            <div class="csv_export_button">
                <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#pp_list'), 'PP_List.csv']);" data-tooltip="tooltip" title="Download P.P. List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
            </div>
            <table class="table table-bordered custom-table table-striped" id="pp_list">
                <thead>
                    <tr>
                        <th rowspan="2" style="max-width: 20px; width: 20px;">select</th>
                        <th rowspan="2" class="exclude_export" style="max-width: 100px; width: 100px;"><span data-tooltip="tooltip" title="Manufacture Indent Number"># MI No</span></th>
                        <th rowspan="2" class="exclude_export" style="min-width: 120px; width: 120px; max-width: 250px;">Item</th>
                        <th colspan="2" class="exclude_export" style="min-width: 200px; max-width: 200px; width: 200px;">Material Status</th>
                        <th rowspan="2" class="exclude_export" style="min-width: 100px; max-width: 100px; width: 100px;">Progress</th>
                        <th rowspan="2" class="exclude_export" style="max-width: 150px; width: 150px; min-width: 150px;">Schedule</th>
                        <th rowspan="2" class="exclude_export" style="max-width: 100px; width: 100px;">Assigned To</th>
                        <th rowspan="2" class="exclude_export" style="max-width: 100px; width: 100px;"><span data-tooltip="tooltip" title="Production Plan Number"># PP No</span></th>
                         <th rowspan="2" class="hide"># MI NO</th>
                        <th rowspan="2" class="hide">MI Date</th>
                        <th rowspan="2" class="hide">Item</th>
                        <th rowspan="2" class="hide">Qty</th>
                        <th rowspan="2" class="hide">Completed Qty</th>
                        <th rowspan="2" class="hide">Unit</th>
                        <th rowspan="2" class="hide">Start Date</th>
                        <th rowspan="2" class="hide">End Date</th>
                        <th rowspan="2" class="hide">Assigned To</th>
                        <th rowspan="2" class="hide"># PP ID</th>
                        <th rowspan="2" class="hide">PP Date</th>
                    </tr>
                    <tr>
                        <th class="exclude_export" style="min-width: 120px; max-width: 120px; width: 120px;">Allocated</th>
                        <th class="exclude_export" style="min-width: 120px; max-width: 120px; width: 120px;">Issued</th>
                    </tr>
                    {% if logged_in_user|canApprove:'PRODUCTION' %}
                        <tr class="pp-add-row">
                            <th></th>
                            <th class="exclude_export">
                                <select class="form-control chosen-select mi_select" id="mi_select">

                                </select>
                            </th>
                            <th class="exclude_export" style="text-align: left;">
                                <span class="for-new-pp-post hide">
                                    <select class="form-control chosen-select item_select" id="item_select">
                                        <option value="0">-- Select Item --</option>
                                        <optgroup label="MI Items">

                                        </optgroup>
                                    </select>
                                </span>
                            </th>
                            <th class="exclude_export">
                                <span class="for-new-pp hide">
                                    <input type="text" class="form-control mi_qty text-right" id="mi_qty" maxlength="16" onfocus="setNumberRangeOnFocus(this,12,3)" placeholder="Plan Qty" value="0.00">
                                    <span id="unit_display" class="unit_display pull-right" style="padding: 8px;">&nbsp;</span>
                                    <label class="pull-right mi_max_qty_container hide ">MI Qty: <span id="mi_max_qty" class="mi_max_qty">0</span></label>
                                    <span id="mi_completed_qty" class="mi_completed_qty hide">0</span>
                                </span>
                            </th>
                            <th colspan="2" class="exclude_export" style="text-align: left;">
                                <span class="for-new-pp hide">
                                    <select class="form-control chosen-select location_select" id="location_select">
                                        <option value="">-- Select Location --</option>
                                        <optgroup label="Location">

                                        </optgroup>
                                    </select>
                                </span>
                            </th>
                            <th class="exclude_export">
                                <span class="for-new-pp hide">
                                    <span id="reportrange_add" class="report-range_add form-control">
                                        <i class="glyphicon glyphicon-calendar"></i>&nbsp;
                                        <span></span> <b class="caret" style="float: right; margin-top: 8px;"></b>
                                        <input type="hidden" class="fromdate_add" id="fromdate_add" name="fromdate_add" />
                                        <input type="hidden" class="todate_add" id="todate_add" name="todate_add" />
                                    </span>
                                </span>
                            </th>
                            <th class="exclude_export">
                                <span class="for-new-pp hide">
                                    <select class="form-control chosen-select mi_assigned" id="mi_assigned">
                                        <option value="0">-- Select Assigned To --</option>
                                        <option value="add_new_issue_to"><b>+ Add New</b></option>
                                        <optgroup label="Internal"></optgroup>
                                        <optgroup label="External"></optgroup>
                                    </select>
                                </span>
                            </th>
                            <th class="exclude_export">
                                <button class="btn btn-new-item" data-tooltip="tooltip" title="Create Production Plan" onclick="saveProductionPlan('add')">
                                    <i class="fa fa-plus" aria-hidden="true"></i>
                                    <span class="btn-new-item-label"></span>
                                </button>
                            </th>
                            <th class="hide"></th>
                            <th class="hide"></th>
                            <th class="hide"></th>
                            <th class="hide"></th>
                            <th class="hide"></th>
                            <th class="hide"></th>
                            <th class="hide"></th>
                            <th class="hide"></th>
                            <th class="hide"></th>
                            <th class="hide"></th>
                            <th class="hide"></th>
                        </tr>
                    {% endif %}
                </thead>
                <tbody></tbody>
            </table>
        </div>
    </div>
    <div id="edit_production_plan" class="modal" role="dialog" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog" style="width: 500px; margin-top: 72px;">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Edit</h4>
                </div>
                <div class="modal-body" style="min-height: 350px;">
                    <div class="form-group col-sm-6 remove-padding-left">
                        <label># MI No<span class="mandatory_mark"> *</span></label>
                        <span class="enable-mi-details pull-right" style="font-size: 16px; margin-top: -5px;" data-tooltip="tooltip" data-placement="left" title="Edit Mi Details" role="button" onclick="enableMiDetails()">
                            <i class="fa fa-pencil"></i>
                        </span>
                        <select class="form-control chosen-select mi_select" id="edit_mi_select">

                        </select>
                        <div class="edit_mi_text hide"></div>

                    </div>
                    <div class="form-group col-sm-6 remove-padding-right">
                        <label>Assigned To</label>
                        <select class="form-control chosen-select mi_assigned" id="edit_mi_assigned">
                            <option value="0">-- Select Assigned To --</option>
                            <optgroup label="Internal"></optgroup>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Item</label>
                        <select class="form-control chosen-select item_select" id="edit_item_select">
                            <option value="0">-- Select Item --</option>
                            <optgroup label="MI Items">

                            </optgroup>
                        </select>
                        <div class="edit_item_text hide"></div>
                    </div>
                    <div class="form-group col-sm-6 remove-padding-right" style="padding-left:0px !important">
                        <label>Plan Qty</label>
                        <input type="text" class="form-control mi_qty text-right" id="edit_mi_qty" maxlength="16" onfocus="setNumberRangeOnFocus(this,12,3)" placeholder="Plan Qty" >
                        <span id="edit_unit_display" class="unit_display pull-right" style="padding: 8px;">&nbsp;</span>
                        <label class="pull-right mi_max_qty_container hide ">MI Qty: <span id="edit_mi_max_qty" class="mi_max_qty">0</span></label>
                        <span id="edit_mi_completed_qty" class="mi_completed_qty hide">0</span>
                    </div>
                    <div class="form-group col-sm-6 remove-padding-right">
                        <label>Location</label>
                        <span class="mandatory_mark"> *</span>
                        <select class="form-control chosen-select edit_location_select" id="edit_location_select">
                            <option value="">-- Select Location --</option>
                            <optgroup label="Locations">

                            </optgroup>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Schedule</label>
                        <span id="reportrange_edit" class="report-range_add form-control">
                            <i class="glyphicon glyphicon-calendar"></i>&nbsp;
                            <span></span> <b class="caret" style="float: right; margin-top: 8px;"></b>
                            <input type="hidden" class="fromdate_add" id="edit_fromdate_add" name="fromdate_edit" />
                            <input type="hidden" class="todate_add" id="edit_todate_add" name="todate_edit" />
                        </span>
                    </div>

                </div>
                <div class="modal-footer">
                    <button id="update_pp" onclick="saveProductionPlan('edit');" class="btn btn-save">Update</button>
                    <span class="btn btn-cancel" data-dismiss="modal">Cancel</span>
                </div>
            </div>
        </div>
    </div>
    <div id="pp_remarks_modal" class="modal fade" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Remarks</h4>
                </div>
                <div class="modal-body">
                    <div class="remarks-add-container">
                        <div class="row" style="border-bottom: 1px solid #ccc; padding-bottom: 15px;">
                            <div class="{% if logged_in_user|canApprove:'PRODUCTION' %}col-sm-10{% else %}col-sm-12{% endif %}">
                                <label>Special Instruction</label>
                                <textarea rows="2" maxlength="300" placeholder="Enter Special Instruction "
                                          id="id_pp-instruction" cols="40" class="form-control"
                                          onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')"
                                          onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')"></textarea>
                            </div>
                            <div class="col-sm-2">
                                {% if logged_in_user|canApprove:'PRODUCTION' %}
                                    <button type="button" class="btn btn-save" style="margin-top: 42px;" onclick="saveInstructions()">Save</button>
                                {% endif %}
                            </div>
                        </div>
                        {% if logged_in_user|canApprove:'PRODUCTION' %}
                            <div class="row" style="margin-top: 7px; margin-bottom: 15px;">
                                <div class="col-sm-10">
                                    <label>Remarks <span class="qtip-question-mark" data-tooltip="tooltip"
                                                         title="Internal Remarks. Will not be printed in document."></span>
                                    </label>
                                    <textarea rows="2" maxlength="300" placeholder="Enter Remarks " id="id_pp-remarks"
                                              cols="40" class="form-control"
                                              onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')"
                                              onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" ></textarea>
                                </div>
                                <div class="col-sm-2">
                                    <button type="button" class="btn btn-save" style="margin-top: 42px;" onclick="saveNewRemarks()">+</button>
                                </div>
                            </div>
                        {% endif %}

                    </div>
                    <div class="remarks-content-container" style="margin-top: 7px;">

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="change_log_modal" class="modal" tabindex="-1" role="dialog" aria-labelledby="" aria-hidden="true">
       <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Activity Log</h4>
                </div>
                <div class="modal-body remove-padding">
                    <div id='loadingmessage_changelog_listing_ie' style="text-align:center">
                            <img src='{{ MEDIA_URL }}../images/loading_spinner.gif' width="100">
                            <br /> Please wait...
                    </div>
                    <ul class="history-log-container timeline">

                    </ul>
                    <ul class="show-more-log" style="list-style: none; text-align: right; margin-right: 15px; cursor: pointer;">
                        <li onclick="shoMoreLogs(20,20,{{pp_id}})">Show More</li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <a href="#" class="btn btn-cancel" data-dismiss="modal">Close</a>
                </div>
            </div>
        </div>
    </div>


    <div id="pp_production_log" class="modal fade" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Production Log</h4>
                    <div class="modal-description" style="color: #666;"></div>
                    <input type="hidden" class="pp_details_log" />
                </div>
                <div class="modal-body">
                    <table class="table table-bordered custom-table table-striped" id="pp_log_list">
                        <thead>
                            <tr>
                                <th >IN Date</th>
                                <th>Qty in <span class="pp-log-unit"></span></th>
                                <th class="receipt_type_name" style="min-width: 150px;">GRN / IRN No</th>
                            </tr>
                            {% if logged_in_user|canApprove:'PRODUCTION' %}
                                <tr class="add-pp-log hide">
                                    <th>
                                        <input type="text" id="pp_log_date"
                                               class="form-control custom_datepicker set-my-start-date fixed-width-medium"
                                               data-setdate="" placeholder="Select Date" readonly="readonly">
                                        <i class="glyphicon glyphicon-calendar custom-calender-icon"></i>
                                    </th>
                                    <th>
                                        <div>
                                            <input type="text" id="pp_log_qty" class="form-control pp_log_qty text-right" placeholder="Enter Qty" onfocus="setNumberRangeOnFocus(this,12,3)" />
                                        </div>
                                        <div class="component_location" style="margin: 10px 0px 10px 0px;"  data-id="id_prod-log-location" data-name="prod-log-location" > </div>
                                    </th>
                                    <th class="text-center">
                                        <button class="btn btn-save" onclick="saveProductionLog('add')">+</button>
                                    </th>
                                </tr>
                            {% endif %}
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
<div id="dcUsageReport" class="modal fade" role="dialog">
  	<div class="modal-dialog modal-lg">
    	<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Material Usage</h4>
		         <span>
	                <a role="button" class="btn btn-add-new pull-right" style="margin-top: -23px;margin-right: 30px;" onclick="GeneralExportTableToCSV.apply(this, [$('#cattable_3'), 'Material_Usage_Report.csv']);"   data-tooltip="tooltip" title="" data-original-title="Download Material Usage List as CSV" data-placement="left">
				        <i class="fa fa-download" aria-hidden="true"></i>
			        </a>
		         </span>
      		</div>
      		<div class="modal-body">
		         <table id="cattable_3" class="table table-bordered custom-table table-striped tableWithText no-sorting">
					<thead>
					<tr>
						<th style="width: 50px;" >S.No</th>
                        <th >Item Name</th>
						<th style="width: 100px;"  class="for_dc">Expected <span class="th-sub-heading">(Based on BoM & JO)</span></th>
						<th style="width: 100px;" class="for_dc" >Supplied</th>
						<th style="width: 100px;" class="for_dc" >Returned</th>
						<th style="width: 100px;" class="for_dc" >To Be Supplied</th>
						<th style="width: 100px;" class="for_dc" >Excess Supplied</th>
						<th style="width: 100px;" class="for_dc exclude_export" >Return Item</th>
					</tr>
					</thead>
				</table>
		        <div id='loadingmessage' class="text-center" style='display:none;margin-top:95px'>
					<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' style="width:75px"/>
				    <br>
				    Please wait...
		        </div>
      		</div>
		    <div class='modal-footer'>
			    <span class="pull-left nonStock-text">  <span class=" nonStock-text non_stock-flag"></span> - NON-STOCKABLE</span>
				<span class="pull-left nonStock-text">  <span class=" nonStock-text service-item-flag"></span> - SERVICE</span>
			    {% if logged_in_user|canEdit:'STORES' %}
                    <a class='btn btn-save' id='SaveMU' data-dismiss='modal' onclick="javascript:saveReturnItem();">Save</a>
                {% else %}
                    <a class='btn btn-save' id='SaveMU' data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ update in Stores Module. Please contact the administrator." style="opacity: 0.5; cursor: not-allowed">Save</a>
                {% endif %}
			    <a class='btn btn-cancel' id='saveCancel' data-dismiss='modal'>Close</a></div>
    	</div>
  	</div>
</div>
    <div class="hide">
		<form id="id-edit_receipt_form" method="POST" action="">{% csrf_token %}
			<input type="hidden" name="receipt_no" id="id-edit_receipt_no" value="" />
		</form>
        <form id="id-edit_po_form" method="POST" action="/erp/purchase/jo/">{% csrf_token %}
			<input type="hidden" name="po_no" id="id-edit_po_id" value="" />
			<input type="hidden" name="po_status" id="id-edit_po_status" value="" />
		</form>
	</div>
<div>
    <form class="hide" action="/erp/stores/materialshortage_pp/" method="POST" id="shortageCalPPForm">{% csrf_token %}
        <input type="hidden" name="cat_list" id="cat_list" class="form-control" placeholder="Enter Qty" value="">
         <input type="hidden" name="enterprise_id" id="enterprise_id" class="form-control" placeholder="Enter Qty" value="">
        <input type="hidden" name="pp_no" id="pp_no" class="form-control" placeholder="Enter Qty" value="">
        <input type="hidden" name="pp_id" id="pp_id" class="form-control" placeholder="Enter Qty" value="">
        <input type="hidden" name="from_date" id="from_date" class="form-control" placeholder="Enter Qty">
        <input type="hidden" name="to_date" id="to_date" class="form-control" placeholder="Enter Qty">
        <input type="hidden" name="location_id" id="location_id" value='' />
    </form>
</div>
<script>
    $('#save_material_list').on('click', function() {
        if($('#pp_list tbody input[type="checkbox"]:checked').length <= 0){
        swal('', 'Please Select Atleast one PP to calculate the shortage.', 'error');
        }
        else{
            let concatenatedCatList = '';
            let concatenatedPPList = '';
            let concatenatedLocationList = '';
            let concatenatedPPIdsList = '';
            let enterprise_id = $('#enterprise_id').val();
            $('#pp_list tbody input[type="checkbox"]:checked').each(function() {
                let row = $(this).closest('tr');
                let catList = row.find('input[name="cat_list"]').val();
                let pp_no_list = row.find('input[name="pp_no"]').val();
                let location_id = row.find('input[name="location_id"]').val();
                let pp_id = row.find('input[name="pp_id"]').val();
                concatenatedCatList += catList;
                concatenatedPPList += pp_no_list;
                concatenatedLocationList += location_id;
                concatenatedPPIdsList += pp_id;
            });

            let form = $('#shortageCalPPForm');
            form.find('input[name="from_date"]').val($('#fromdate').val());
            form.find('input[name="to_date"]').val($('#todate').val());
            form.find('input[name="cat_list"]').val(concatenatedCatList);
            form.find('input[name="pp_no"]').val(concatenatedPPList);
            form.find('input[name="location_id"]').val(concatenatedLocationList);
            form.find('input[name="pp_id"]').val(concatenatedPPIdsList);
            form.find('input[name="enterprise_id"]').val(enterprise_id);
            form.attr('target', '_blank').submit();
        }
});
</script>
    {% include "modal-window/add_issue_to.html" %}
    {% include "purchase/po_document.html" %}
{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
{% endblock %}