.report-range_add {
    cursor: pointer;
    padding: 7px 10px;
}

table.dataTable thead tr.pp-add-row th {
    font-weight: normal;
    letter-spacing: 0;
    font-size: 12px;
}

table.dataTable thead tr.pp-add-row th .form-control,
table.dataTable thead tr.pp-add-row th .chosen-single,
table.dataTable thead tr.pp-add-row th .chosen-container {
    font-size: 12px;
    background: #fff;
}

table.dataTable thead tr.pp-add-row th .chosen-container {
    background: transparent;
}


#pp_list .custom-error-message,
#pp_log_list .custom-error-message {
    position: relative;
    text-align: left;
    text-transform: initial;
}
.component_location .custom-error-message{
    text-align: center !important;
}
#pp_list td,
#pp_list tr.pp-add-row th {
    border-right: none;
    border-left: none;
}

#pp_list td {
    border: none;
}

#pp_list .pp-content {
    vertical-align: top !important;
    padding-top: 18px;
}

#pp_list .table-inline-icon-container {
    margin-top: 5px;
    width: 200px;
}

.status-container {
    display: inline-block;
    width: 100%;
    padding: 5px;
    border-radius: 6px;
}

.progress_yet_to_start .status-container {
    background: rgba(0, 0, 0, 0.02);
}

.progress_in_progress .status-container {
    background: rgba(255, 255, 0, 0.8);
}

.progress_completed .status-container {
    background: rgba(144, 238, 144, 0.5);
}

.timeliness_overdue .status-container {
    background: rgba(255, 0, 0, 0.7);
}

label[for="in_progress"],
 label[for="in_progress"]:after {
    color:orange;
}

label[for="completed"],
label[for="on_time"],
label[for="completed"]:after,
label[for="on_time"]:after {
    color:green;
}

label[for="overdue"],
label[for="overdue"]:after {
    color:red;
}

.table-inline-icon i {
    font-size: 16px
}

table-inline-icon span {
    padding: 4px;
}

#reportrange_add {
    padding-top: 7px;
    text-align: left;
}

#pp_list {
    width: 100%;
    table-layout: fixed;
}

.mi-content,
.pp-content {
    text-align: left !important;
}

#edit_mi_assigned_chosen .chosen-drop {
    margin-left: -188px;
}

.edit_mi_text,
.edit_item_text {
    font-size: 14px;
    border: solid 1px #CCC;
    padding: 6px;
    border-radius: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #666;
    background: #FFFEEE;
}

.sa-input-error.show {
    display: none !important;
}

.chat-container {
    max-height: none;
}

.qty_update_button {
    position: absolute;
    margin-top: -34px;
    margin-left: 180px;
    opacity: 0.85;
    width: 100px;
    z-index: 1000;
}

.qty-content .qty-label {
    background: rgba(255,255,255,0.6);
    width: fit-content;
    border-radius: 50px;
    padding: 2px 6px 0px;
}

.progress-inline-icon {
    position: absolute;
    margin-left: -35px;
    margin-top: -11px;
}

.mi_qty.text-right {
    width: calc(100% - 44px);
}



/* Change Log Styles */

#change_log_modal .modal-body {
	overflow: auto;
}

.history-log-part {
	width: 100%;
	margin: 0;
	padding: 8px 15px;
	cursor: pointer;
	padding-left: 60px;
}
.history-log-part:last-child {
	border-bottom: none;
}
.history-log-part:hover {
	background: #e8f0fe;
}
.history-log-part:hover .history-log-date {
	font-weight: bold;
}
.history-log-date {
   font-style: italic;
}
.history-log-username {
	font-size: 15px;
	padding-top: 3px;
	font-weight: bold;
}

.history-log-content {
	margin-top: 8px;
}

.history-log-content ul {
	padding: 0px 10px 0px 35px;
}
.history-log-content li {
	margin-bottom: 10px;
}

.history-log-content li:last-child {
	margin-bottom: 0;
}

.history-log-part.active .history-log-date,
.history-log-part.active .history-log-username {
	color: #004195;
}

.history-log-part.active .history-log-date {
	text-align: right;
	display: block;
}

.history-log-part .fa-chevron-down:before {
	content: "\f078";
	color: #999;
}

.history-log-part.active .fa-chevron-down:before {
	content: "\f077";
	color: #999;
}

#change_log_modal .modal-body {
	overflow: auto;
}

ul.timeline {
	list-style-type: none;
	position: relative;
	padding: 0px;
}
ul.timeline:before {
	content: ' ';
	background: #d4d9df;
	display: inline-block;
	position: absolute;
	left: 29px;
	width: 2px;
	height: 94%;
	z-index: 400;
	margin-top: 12px;
}
ul.timeline > li {

}
ul.timeline > li:before {
	content: ' ';
	background: white;
	display: inline-block;
	position: absolute;
	border-radius: 50%;
	border: 3px solid #209be1;
	left: 23px;
	width: 14px;
	height: 14px;
	z-index: 400;
	margin-top: 4px;
}
ul.timeline > li:last-child:before {
	background: #209be1;
}