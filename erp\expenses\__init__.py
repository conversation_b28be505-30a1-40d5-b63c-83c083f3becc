"""
"""
import logging

__author__ = 'saravanan'

logger = logging.getLogger(__name__)

# list in table
EXPENSE_LIST_KEY = 'my_expenses'
EXPENSE_OTHER_LIST_KEY = 'other_expenses'

# Expense Form, Tags, Particulars (Item)
EXPENSE_FORM_HEAD_KEY = 'exp_form'
EXPENSE_FORMSET_ITEM_KEY = 'exp_item_formset'
EXPENSE_FORMSET_TAG_KEY = 'tags_formset'

# Prefix in form elements for Expense Form, Tags, Particulars (Item)
EXPENSE_HEAD_PREFIX = 'exp'
EXPENSE_ITEM_PREFIX = 'exp_item'
EXPENSE_TAG_PREFIX = 'tag'

# Exclude list: captured based on relationship variables in model
EXPENSE_EXCLUDE_FIELD_LIST = (
	'enterprise', 'created_user', 'approved_user', 'checked_user', 'verified_user',
	'claim_head_ledger', 'expense_tags', 'particulars', '_sa_instance_state')

EXPENSE_PARTICULAR_EXCLUDE_FIELD_LIST = (
	'expense', 'enterprise', 'expense_head_ledger', 'sublist', '_sa_instance_state')

STATUS_DRAFT = 0
STATUS_CONFIRMED = 1
STATUS_APPROVED = 2
STATUS_ICD_CHECKED = 3
STATUS_ICD_VERIFIED = 4
STATUS_ACCOUNTED = 5
STATUS_DISPLAY = ('Draft', 'Confirmed', 'Approved', 'Checked', 'Verified', 'Accounted')
