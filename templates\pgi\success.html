{% extends "template.html" %}
{% block home %}
<style>
	.message {
	        margin-top: 100px;
	}

	.success-tick-mark .fa-thumbs-up {
		background: #24b663;
	    color: #fff;
	    font-size: 55px;
	    padding: 18px 21px;
	    border-radius: 60px;
	}

	.subscription_id {
		font-size: 18px;
	    background: #eee;
	    padding: 10px 40px;
	    border-radius: 50px;
	    margin-bottom: 30px;
	}

	.subscription_text {
		font-size: 16px;
	}

	.supporting_text {
		color: #666;
		font-size: 12px;
	}

	.success-ref-value tr:nth-child(odd) {
		background: rgba(32, 155, 225, 0.1)
	}

	.success-ref-value td {
		padding:  10px;
	}
</style>
<script async src="https://www.googletagmanager.com/gtag/js?id=AW-722929303"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'AW-722929303');
</script>
<div style="display:none">
   	<input type="hidden" name="csrfmiddlewaretoken" value="{% csrf_token %}">
</div>
<div class="message">
	<div class="text-center success-tick-mark">
		<i class="fa fa-thumbs-up" aria-hidden="true"></i>
	</div>
	<h1 class="text-center" style="color: #24b663">Payment Success!</h1>
	<div class="text-center" style="margin: 30px 0 30px;">
		<span class="subscription_id">Transaction# {{ transaction_id }}</span>
	</div>
	<div class="text-center">
		<span class="subscription_text">
			Thank you for subscribing to the {{paid_item}} Plan! We've successfully processed your payment of <b>₹ {{ amount|floatformat:2 }}</b>.  
			<br />
			<br />
			<table class="success-ref-value" style="width: 600px;margin: 0 auto; border:solid 1px rgba(32, 155, 225, 0.3); margin-bottom: 0;">
				<tr>
					<td class="text-center"><label>Invoice Number</label></td>
					<td class="text-left">{{invoice_code}}</td>
				</tr>
				<tr>
					<td class="text-center"><label>Transaction Time</label></td>
					<td class="text-left">{{datetime}}</td>
				</tr>
			</table>			
			<br />
			Please <a href='/erp/logout'>Login</a> again for applying changes.
		</span>
	</div>
	<div class="text-center" style="margin-top: 30px;">
		<span class="supporting_text">
			If you have any questions or concerns regarding this transaction, please contact us at <b><EMAIL></b>
		</span>
	</div>
</div>
<script type="text/javascript">
	$(document).ready(function(){
		$(".subscription-bar").addClass("hide");
		$("body").removeClass("with_subscription_bar");
		gtag('event', 'conversion', {'send_to': 'AW-722929303/h33BCPHHsssCEJeN3NgC'});
	});

</script>
{% endblock %}