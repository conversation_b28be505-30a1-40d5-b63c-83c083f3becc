﻿var IsTabClicked = 0;
if (typeof JSCustomValidator == "undefined") {
    var JSCustomValidator = {
        JSvalidate: function () {
            // Define option defaults
            var options = [];
            var validationResult = true;
			
            if (arguments[0] && typeof arguments[0] === "object") {
                var args = arguments[0];
                for (var argumentsI = 0; argumentsI < args.length; argumentsI++) {
                    options[argumentsI] = jQuery.extend({
                        controltype: 'none',
                        controlid: 'none',
                        controlclass: 'none',
                        isrequired: false,
						isemail: false,
						ishsn: false,
                        minvalue: null,
                        maxvalue: null,
						mindigit: null,
                        errormsg: 'none',
                        minvalerrormsg: 'none',
                        maxvalerrormsg: 'none',
						emailerrormsg: 'none',
						hsnerrormsg: 'none',
						suggestioncontainer: 'hsn-wrapper',
						mindigiterrormsg: 'none',
						appendin: 'none',
						marginleft: '0px',
						margintop: '0px'
                    }, args[argumentsI]);
					
					//Key Press Event
					if(options[argumentsI].controltype == "textbox" || options[argumentsI].controltype == "dropdown" ){
						var currentControl = document.getElementById(options[argumentsI].controlid);
						if(currentControl == null){
						
						}
						else {
							currentControl.onkeyup = function () {
								$(this).siblings('.error-auto-hide').remove();
								if ($(this).val() != "") {
									$(this).removeClass('error-border');
									$(this).siblings('.custom-error-message').removeClass('show').addClass('hide');
									$(this).siblings('.suggestion-container').remove();
								}
							};
							if(options[argumentsI].controltype != "textbox") {
								currentControl.onchange = function () {
									if ($(this).val() == "" || $(this).val() == "0") {
										$(this).addClass('error-border');
										$(this).siblings('.custom-error-message').removeClass('show').addClass('hide');
										$(this).siblings('.custom-error-message:first').removeClass('hide').addClass('show');
									} else {
										$(this).removeClass('error-border');
										$(this).closest('div').find('.error-border').removeClass('error-border');
										$(this).siblings('.custom-error-message').removeClass('show').addClass('hide');
									}
								};
							}
						}
					}
					if(options[argumentsI].controltype == "file" ){
						var currentControl = document.getElementById(options[argumentsI].controlid);
						if(currentControl == null){
						
						}
						else {
							currentControl.onchange = function () {
								if ($(this).val() == "" || $(this).val() == "0") {
									$(this).closest("div").find(".bootstrap-filestyle").find("input").addClass('error-border');
									$(this).siblings('.custom-error-message').removeClass('show').addClass('hide');
									$(this).siblings('.custom-error-message:first').removeClass('hide').addClass('show');
								} else {
									$(this).removeClass('error-border');
									$(this).closest("div").find(".bootstrap-filestyle").find("input").removeClass('error-border');
									$(this).siblings('.custom-error-message').removeClass('show').addClass('hide');
								}
							};
						}
					}
					//On Submit Event
					//Required
					if(currentControl == null && (options[argumentsI].controltype == "textbox" || options[argumentsI].controltype == "dropdown" )){

					}
					else {
					if(options[argumentsI].isrequired){
						if (options[argumentsI].controltype == "textbox") {
							if($("#" + options[argumentsI].controlid).val().trim() == ""){
								var suggestion = ``;
								if(options[argumentsI].ishsn) {
									suggestion  = `<div class="suggestion-container hsn-suggestion hsn-suggestion-internal">
	                                                    ${setHsnSuggestion(options[argumentsI].controlid, options[argumentsI].suggestioncontainer)}
	                                                </div>`
								}
								$("#" + options[argumentsI].controlid).addClass('error-border');
								$("#" + options[argumentsI].controlid).parent().append('<span class="custom-error-message">' + options[argumentsI].errormsg + '</span>'+ suggestion);
								validationResult = false;
							}
							else {
								$("#" + options[argumentsI].controlid).parent().append('<span class="custom-error-message hide">' + options[argumentsI].errormsg + '</span>');	
							}
						}
						else if (options[argumentsI].controltype == "file") {
							if($("#" + options[argumentsI].controlid).val().trim() == ""){
								$("#" + options[argumentsI].controlid).closest("div").find(".bootstrap-filestyle").find("input").addClass('error-border');
								$("#" + options[argumentsI].controlid).parent().append('<span class="custom-error-message">' + options[argumentsI].errormsg + '</span>');
								validationResult = false;
							}
							else {
								$("#" + options[argumentsI].controlid).parent().append('<span class="custom-error-message hide">' + options[argumentsI].errormsg + '</span>');	
							}
						}
						else if (options[argumentsI].controltype == "datePicker") {
							$("#dateselector_day, #dateselector_month, #dateselector_year").css({border: "1px solid #ccc"});
							if($("#dateselector_day").val()){
								if($("#dateselector_day").val().trim() == 0|| $("#dateselector_month").val().trim() == 0 || $("#dateselector_year").val().trim() == 0 ) {
									$("#dateselector_day, #dateselector_month, #dateselector_year").css({border: "1px solid #dd4b39"});
									$("#" + options[argumentsI].controlid).parent().append('<span class="custom-error-message" style="display: block; margin-top: 5px;">' + options[argumentsI].errormsg + '</span>');
									validationResult = false;
								}
								else if($(".invalid-date-format").hasClass('invalid-date-format')) {
									$("#dateselector_day, #dateselector_month, #dateselector_year").css({border: "1px solid #dd4b39"});
									validationResult = false;
									}
								else {
									$("#" + options[argumentsI].controlid).parent().append('<span class="custom-error-message hide">' + options[argumentsI].errormsg + '</span>');	
								}
							}
							else if($(".invalid-date-format").hasClass('invalid-date-format')) {
								$("#dateselector_day, #dateselector_month, #dateselector_year").css({border: "1px solid #dd4b39"});
								validationResult = false;
								}
							else {
								$("#" + options[argumentsI].controlid).parent().append('<span class="custom-error-message hide">' + options[argumentsI].errormsg + '</span>');	
							}
						}
						else if(options[argumentsI].controltype == "dropdown"){
							if($("#" + options[argumentsI].controlid).val() == "" || $("#" + options[argumentsI].controlid).val() == null || $("#" + options[argumentsI].controlid).val() == "null" || $("#" + options[argumentsI].controlid).val() == "0" || $("#" + options[argumentsI].controlid).val() == "None" || $("#" + options[argumentsI].controlid).val() == "undefined"){
								if($("#" + options[argumentsI].controlid).hasClass('chosen-select') || $("#" + options[argumentsI].controlid).hasClass('chosen-dropdown')) {
									$("#" + options[argumentsI].controlid).next('.chosen-container').find('.chosen-single').addClass('error-border');
								}
								else {
									$("#" + options[argumentsI].controlid).addClass('error-border');
								}
								$("#" + options[argumentsI].controlid).parent().append('<span class="custom-error-message" style="display: block; margin-top:'+options[argumentsI].margintop+'">' + options[argumentsI].errormsg + '</span>');
								validationResult = false;
							}
							else {
								$("#" + options[argumentsI].controlid).parent().append('<span class="custom-error-message hide" style="margin-top:'+options[argumentsI].margintop+'">' + options[argumentsI].errormsg + '</span>');
							}
						}
						else if(options[argumentsI].controltype == "checkbox"){
							if(!$("#"+options[argumentsI].controlid).is(":checked")){
								if(options[argumentsI].appendin != 'none'){
									$("#" + options[argumentsI].appendin).html('<span class="custom-error-message">' + options[argumentsI].errormsg + '</span>');
								}
								else {
									$("#" + options[argumentsI].controlid).parent().append('<span class="custom-error-message">' + options[argumentsI].errormsg + '</span>');
								}
								validationResult = false;
							}
						}
						else if(options[argumentsI].controltype == "radio"){
							if($("."+options[argumentsI].controlclass+":checked").val() == undefined){
								if(options[argumentsI].appendin != 'none'){
									$("." + options[argumentsI].appendin).html('<span class="custom-error-message" style="top: 55px; left: 7px; position: initial; margin-left:'+options[argumentsI].marginleft+'; margin-top:'+options[argumentsI].margintop+'">' + options[argumentsI].errormsg + '</span>');
								}
								else {
									$("." + options[argumentsI].controlclass).parent().append('<span class="custom-error-message" style="top: 55px; left: 7px;margin-left:'+options[argumentsI].marginleft+'; margin-top:'+options[argumentsI].margintop+'">' + options[argumentsI].errormsg + '</span>');
								}
								validationResult = false;
							}
						}
					}
					//Minimum Value
					if (options[argumentsI].minvalue) {
                        if ((options[argumentsI].isrequired && $("#" + options[argumentsI].controlid).val() != "") || (!options[argumentsI].isrequired)) {
                            if ($("#" + options[argumentsI].controlid).val() != "") {
                                if ($("#" + options[argumentsI].controlid).val().trim().length < options[argumentsI].minvalue) {
                                    $("#" + options[argumentsI].controlid).addClass('error-border');
                                    $("#" + options[argumentsI].controlid).parent().append('<span class="custom-error-message">' + options[argumentsI].minvalerrormsg + '</span>');
                                    validationResult = false;
                                }
                            } 
                        }
                    }
					//Maximum Value
					if (options[argumentsI].maxvalue) {
                        if ((options[argumentsI].isrequired && $("#" + options[argumentsI].controlid).val() != "") || (!options[argumentsI].isrequired)) {
                            if ($("#" + options[argumentsI].controlid).val() != "") {
                                if ($("#" + options[argumentsI].controlid).val().trim().length > options[argumentsI].maxvalue) {
                                    $("#" + options[argumentsI].controlid).addClass('error-border');
                                    $("#" + options[argumentsI].controlid).parent().append('<span class="custom-error-message">' + options[argumentsI].maxvalerrormsg + '</span>');
                                    validationResult = false;
                                }
                            } 
                        }
                    }
					//Maximum Value
					if (options[argumentsI].mindigit) {
                        if ((options[argumentsI].isrequired && $("#" + options[argumentsI].controlid).val() != "") || (!options[argumentsI].isrequired)) {
                            if ($("#" + options[argumentsI].controlid).val() != "") {
                                if ($("#" + options[argumentsI].controlid).val().trim() < options[argumentsI].mindigit) {
                                    $("#" + options[argumentsI].controlid).addClass('error-border');
                                    $("#" + options[argumentsI].controlid).parent().append('<span class="custom-error-message error-auto-hide">' + options[argumentsI].mindigiterrormsg + '</span>');
                                    validationResult = false;
                                }
                            } 
                        }
                    }
					if (options[argumentsI].isemail) {
                        if ((options[argumentsI].isrequired && $("#" + options[argumentsI].controlid).val() != "") || (!options[argumentsI].isrequired)) {
                            if ($("#" + options[argumentsI].controlid).val() != "") {
                                var emailRegex = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
                                var isEmailCorrect = emailRegex.test($("#" + options[argumentsI].controlid).val());
                                if (!isEmailCorrect) {
                                    $("#" + options[argumentsI].controlid).addClass('error-border');
                                    $("#" + options[argumentsI].controlid).parent().append('<span class="custom-error-message error-auto-hide">' + options[argumentsI].emailerrormsg + '</span>');
                                    validationResult = false;
                                }
                            }
                        }
					}
					if (options[argumentsI].ishsn) {
                        if ((options[argumentsI].isrequired && $("#" + options[argumentsI].controlid).val() != "") || (!options[argumentsI].isrequired)) {
                            if ($("#" + options[argumentsI].controlid).val() != "") {
                            	var validHsn = true;
                            	if (!isNaN($("#" + options[argumentsI].controlid).val())) {
									validHsn = false
                            	}
                            	else if(["nil-rated", "exempt", "non-gst"].indexOf($("#" + options[argumentsI].controlid).val().toLowerCase()) != -1) {
                        			validHsn = false;
                            	}
                                if (validHsn) {
                                	var hsnSuggestion = `<div class="suggestion-container hsn-suggestion hsn-suggestion-internal">
                                							${setHsnSuggestion(options[argumentsI].controlid, options[argumentsI].suggestioncontainer)}
		                                                </div>`;
                                    $("#" + options[argumentsI].controlid).addClass('error-border');
                                    $("#" + options[argumentsI].controlid).parent().append('<span class="custom-error-message error-auto-hide">' + options[argumentsI].hsnerrormsg + '</span>'+hsnSuggestion);
                                    validationResult = false;
                                }
                            }
                        }
					} }
				}
            }
            return validationResult;
        }
    };
}
