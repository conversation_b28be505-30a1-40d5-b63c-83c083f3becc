{% extends "accounts/sidebar.html" %}
{% block gstr2 %}
    <link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}" />
    <link type="text/css" rel="stylesheet" href="/site_media/css/chosen.css?v={{ current_version }}">
    <link type="text/css" rel="stylesheet" href="/site_media/css/datatables.css?v={{ current_version }}">
    <link type="text/css" rel="stylesheet" href="/site_media/css/fixedHeader.bootstrap.css?v={{ current_version }}">
    <link type="text/css" rel="stylesheet" href="/site_media/css/dataTables.searchHighlight.css?v={{ current_version }}">
    <link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
    <script type="text/javascript" src="/site_media/js/bootstrap-datepicker.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/bootstrap-multiselect.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/chosen.jquery.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/jquery.easyResponsiveTabs.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/daterangepicker.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/datatables.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/dataTables.fixedHeader.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/dataTables.searchHighlight.js?v={{ current_version }}"></script>
    <style type="text/css">
        .dataTables_scrollBody {
            overflow: auto hidden !important;
        }

        .dataTables_scrollBody table{
            margin-bottom: 20px;
        }
    </style>
    <div class="right-content-container download-icon-onTop">
        <div class="page-title-container">
            <span class="page-title">Purchase Register <span class="super-markable-text">GST Related</span></span>
        </div>
        <div class="container-fluid">
            <!-- Page Heading -->
            <div class="row">
                <div class="col-lg-12">
                    <div class="content_bg">
                        <ul class="resp-tabs-list hor_1"></ul>
                        <div class="resp-tabs-container hor_1">
                            <div class="row">
                                <div class="add_table">
                                    <div class="col-lg-12 add_table_content">
                                        <div class="filter-components">
                                            <div class="filter-components-container">
                                                <div class="dropdown">
                                                    <button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
                                                        <i class="fa fa-filter"></i>
                                                    </button>
                                                    <span class="dropdown-menu arrow_box arrow_box_filter">
                                                        <div class="col-sm-12 form-group" >
                                                            <label>Date Range</label>
                                                            <div id="reportrange" class="report-range form-control">
                                                                <i class="glyphicon glyphicon-calendar"></i>&nbsp;
                                                                <span></span> <b class="caret"></b>
                                                                <input type="hidden" class="fromdate" id="fromdate"
                                                                       name="fromdate"/>
                                                                <input type="hidden" class="todate" id="todate" name="todate"/>
                                                            </div>
                                                        </div>
                                                        <div class="filter-footer">
                                                            <button type="submit" class="btn btn-save" id="grnreportview">Apply</button>
                                                        </div>
                                                    </span>
                                                </div>
                                                <span class='filtered-condition filtered-date'>Date: <b></b></span>
                                            </div>
                                        </div>
                                        <div class="col-lg-12 search_result_table">
                                            <div class="csv_export_button">
                                                <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#tablesorter'), 'GSTR-2_Purchase_Report.csv']);" data-tooltip="tooltip" title="Download GSTR-2&nbsp;Purchase&nbsp;Report as CSV" data-placement="bottom"><i class="fa fa-download" aria-hidden="true"></i></a>
                                            </div>
                                            <table class="table table-bordered custom-table table-striped grn_tbl" id="tablesorter" style="width: 100%;">
                                                <thead class="th-vertical-center">
                                                    <tr class="exclude_export">
                                                        <th style="width: 80px;">S. No</th>
                                                        <th style="width: 80px;">Company Code</th>
                                                        <th style="width: 80px;">CE Register Date</th>
                                                        <th style="width: 80px;">Supplier GST No</th>
                                                        <th style="width: 160px;">Supplier Name</th>
                                                        <th style="width: 160px;">Bill No</th>
                                                        <th style="width: 160px;">Bill Date</th>
                                                        <th style="width: 160px;">Bill CGST %</th>
                                                        <th style="width: 160px;">Bill CGST Value</th>
                                                        <th style="width: 160px;">Bill SGST %</th>
                                                        <th style="width: 160px;">Bill SGST Value</th>
                                                        <th style="width: 160px;">Bill IGST %</th>
                                                        <th style="width: 160px;">Bill IGST Value</th>
                                                        <th style="width: 160px;">Taxable Value</th>
                                                        <th style="width: 160px;">Trading</th>
                                                    </tr>
                                                    <!-- This <tr> is used for csv download. Don't delete -->
                                                    <tr>
                                                        <th hidden="hidden" class="exclude_export">S. No</th>
                                                        <th hidden="hidden">CompanyCode</th>
                                                        <th hidden="hidden">CERegisterDate</th>
                                                        <th hidden="hidden">SupplierGSTNo</th>
                                                        <th hidden="hidden">SupplierName</th>
                                                        <th hidden="hidden">BillNo</th>
                                                        <th hidden="hidden">BillDate</th>
                                                        <th hidden="hidden">BillCGSTPer</th>
                                                        <th hidden="hidden">BillCGSTValue</th>
                                                        <th hidden="hidden">BillSGSTPer</th>
                                                        <th hidden="hidden">BillSGSTValue</th>
                                                        <th hidden="hidden">BillIGSTPer</th>
                                                        <th hidden="hidden">BillIGSTValue</th>
                                                        <th hidden="hidden">TaxableValue</th>
                                                        <th hidden="hidden">Trading</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="grn_tbl_tbody">
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
    <script>
        $(document).ready(function () {
            setTimeout(function () {
                $("#grnreportview").trigger('click');
            }, 10);
        });

        $(window).load(function(){
            updateFilterText();
        });

        function updateFilterText() {
            $(".filtered-date b").text($("#reportrange").find("span").text());
        }

        function loopTblData(data){
            var row = '';
            if(data.length != 0){
                $.each(data, function (key, value) {
                if(value[3] !="") {
                    var setDateTime = value[3].split(' ');
                    var setdateFormat = setDateTime[0].split('-');
                    setdateFormat = setdateFormat[1]+"/"+setdateFormat[2]+"/"+setdateFormat[0];
                }
                else {
                    var setdateFormat="";
                }

                if(value[8] !="") {
                    var setDateTime1 = value[8].split(' ');
                    var setdateFormat1 = setDateTime1[0].split('-');
                    setdateFormat1 = setdateFormat1[1]+"/"+setdateFormat1[2]+"/"+setdateFormat1[0];
                }
                else {
                    var setdateFormat1="";
                }
                row += "<tr bgcolor='#ececec' border='0' align='center' style='font-size:16px; font-weight:normal;'>" +
                    "<td class='exclude_export'>"+(key+1)+"</td>" +
                    "<td>"+"001"+"</td>" +
                    "<td>" + moment(setdateFormat).format('DD/MMM/YYYY') +"</td>" +
                    "<td class='text-left'>" + value[4] + "</td>" +
                    "<td class='text-left'>" + value[5] + "</td>" +
                    "<td class='text-left'>" + value[7] + "</td>" +
                    "<td>" + moment(setdateFormat1).format('DD/MMM/YYYY') + "</td>" +
                    "<td class='text-right'>" + (value[12] == undefined ? "0" : value[12]) + "</td>" +
                    "<td class='text-right'>" + (value[13] == undefined ? "0" : value[13]) + "</td>" +
                    "<td class='text-right'>" + (value[14] == undefined ? "0" : value[14]) + "</td>" +
                    "<td class='text-right'>" + (value[15] == undefined ? "0" : value[15]) + "</td>" +
                    "<td class='text-right'>" + (value[16] == undefined ? "0" : value[16]) + "</td>" +
                    "<td class='text-right'>" + (value[17] == undefined ? "0" : value[17]) + "</td>" +
                    "<td class='text-right'>" + value[11] + "</td><td></td>" +
                    "</tr>";
                });
            }
            $('#tablesorter').DataTable().clear();
            $('#tablesorter').DataTable().destroy();
            $('#grn_tbl_tbody').html(row);
            TableHeaderFixed();
            updateFilterText();
            closeFilterOption();
            $("#loading").addClass('hide').removeClass('show');
        }

        $("#grnreportview").click(function () {
            $("#loading").addClass('show').removeClass('hide');
            from_date = $('.fromdate').val();
            to_date = $('.todate').val();
            $.ajax({
                url : "/erp/accounts/json/gstr1purchasereport/",
                type : "POST",
                dataType: "json",
                data : {
                    from_date: from_date,
                    to_date: to_date,
                    type: "gstr2",
                },
                success : function(data) {
                    loopTblData(data)
                },error : function(xhr,errmsg,err) {
                    console.log(xhr.status + ": " + xhr.responseText);
                    $("#loading").addClass('hide').removeClass('show');
                }
            });
            ga('send', 'event', 'Accounts', 'View GSTR1 Purchase Report', $('#enterprise_label').val(), 1);
        });

        $('.nav-pills li').removeClass('active');
        $("#li_gstr_purchase_report").addClass('active');

        var oTable;
        var oSettings;
        function TableHeaderFixed(){
            oTable = $('#tablesorter').DataTable({
                fixedHeader: false,
                "pageLength": 50,
                "scrollY": 200,
                "scrollX": true,
                "search": {
                    "smart": false
                },
                "orderCellsTop": true,
                "columns": [
                    null,
                    null,
                    { "type": "date" },
                    null,null,null,
                    { "type": "date" },
                    null,null,null,null,null,null,null,null
                    ]
            });
            oTable.on("draw",function() {
                var keyword = $('#tablesorter_filter > label:eq(0) > input').val();
                $('#tablesorter').unmark();
                $('#tablesorter').mark(keyword,{});
            });
            oTable.on('page.dt', function() {
              $('html, body').animate({
                scrollTop: $(".dataTables_wrapper").offset().top - 15
               }, 'slow');
            });
            oSettings = oTable.settings();
            $( window ).resize();
        }
    </script>
{% endblock %}