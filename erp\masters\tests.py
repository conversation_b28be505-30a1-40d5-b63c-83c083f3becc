from django.test import TestCase

from erp.masters.backend import MasterService, MasterDAO


class MaterialTests(TestCase):
	def setUp(self):
		pass

	def tearDown(self):
		pass

	def test_something(self):
		self.assertEqual(True, False)

	def test_load_materials(self):
		last_accessed_on = "2020-12-01 00:00:00"
		service = MasterService()
		materials = service.getMaterials(enterprise_id=102, modified_after=last_accessed_on, in_use=0)
		print("Loaded materials %s " % len(materials))
		self.assertNotEqual(len(materials), 0)

	def test_getAllMaterialNames(self):
		master_dao = MasterDAO()
		materials = master_dao.getAllMaterialNames(enterprise_id=102)
		print("Loaded materials %s " % len(materials))
		self.assertNotEqual(len(materials), 0)

	def test_getRecBomMaterialList(self):
		service = MasterService()
		bom_materials_list = []
		materials = service.recursive_bom_material_list(item_id=74083, enterprise_id=102, bom_materials_list=bom_materials_list)
		print("Loaded materials %s " % materials)
		self.assertNotEqual(len(materials), 0)
