{% extends "template.html" %}
{% block home %}
<style>
	li.dashboard_menu a{
		background-color:#fff;
		color:#306082 !important;
		text-decoration:none;
	}
	
	.main-menu-container {
		margin-left: 0 !important;
	}

	.error_panel {
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		display: none;
		position: absolute;
		background-color: rgba(204, 204, 204, 0.5);
		color: #222222;
	}
	#error_display{
		color:#FF0000;
		background:#00FFFF;
		font-size:12px;
		font-weight:normal;
		float:center;
		display:show;
		position: absolute;
		top: 50%;
		left: 50%;
		margin-top: 100px;
		margin-left: 100px;
	}

	ul,li{
		list-style:none;
	}

	.dropdown-menu-large.home-dropdown-menu {
	 	width: 500px;
		right: 0;
		margin: 0 auto;
	}

	.dropdown-menu-home{
		float: none;
		margin-top: 214px;
		box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
		border: 1px solid rgba(0, 0, 0, .15);
		background-clip: padding-box;
		font-size: 14px;
		text-align: left;
		list-style: none;
		background-color: #fff;
	}

	.dropdown-menu-home li:hover a {
	  color: #209be1;
	}

	.divider {
		height: 1px;
		margin: 9px 0;
		overflow: hidden;
		background-color: #e5e5e5;
	}

	@keyframes cssload-spin {
		100%{ transform: rotate(360deg); transform: rotate(360deg); }
	}

	@-o-keyframes cssload-spin {
		100%{ -o-transform: rotate(360deg); transform: rotate(360deg); }
	}

	@-ms-keyframes cssload-spin {
		100%{ -ms-transform: rotate(360deg); transform: rotate(360deg); }
	}

	@-webkit-keyframes cssload-spin {
		100%{ -webkit-transform: rotate(360deg); transform: rotate(360deg); }
	}

	@-moz-keyframes cssload-spin {
		100%{ -moz-transform: rotate(360deg); transform: rotate(360deg); }
	}

	@media only screen and (max-width: 1366px) {
		.navbar-inverse .navbar-nav > li > a {
			padding: 16px 15px 16px;
		}
	}

	@media only screen and (max-width: 1280px) {
		.navbar-inverse .navbar-nav > li > a {
			padding: 16px 10px 16px;
			font-size: 0.9em;
		}

		.span-profile-name {
			font-size: 1em;
		}
	}

	@media only screen and (max-width: 1100px) {
		.navbar-inverse .navbar-nav > li > a {
			padding: 10px 10px 12px;
		}

		.navbar-inverse .navbar-nav.navbar-left {
			width: 25%;
		}
		.navbar-inverse .navbar-nav.navbar-left > li {
			width: 100%;
		}

		#horizontal-menu {
			margin-top: 60px;
		}
	}

	.no-acitve-link {
		color: #FFF;
		font-size: 16px;
		letter-spacing: 1px;
	}

	.navbar-inverse .navbar-nav > li > a:hover {
		color: #FFF;
		background: transparent;
	}

	.navbar-inverse .navbar-nav > li > div:hover {
		background: transparent;
	 }

	.acitve-settings-link:hover {
		background: red;
	}

	.rotate{
		-moz-transition: all 0.3s linear;
		-webkit-transition: all 0.3s linear;
		transition: all 0.3s linear;
	}

	.rotate.down{
		-moz-transform:rotate(90deg);
		-webkit-transform:rotate(90deg);
		transform:rotate(90deg);
	}
</style>

<script>
var myCalendar;
	function doOnLoad() {
		myCalendar = new dhtmlXCalendarObject(["calendar","calendar1"]);
		myCalendar.setDateFormat("%d/%m/%Y");
	}

</script>
<script type="text/javascript" src="/site_media/js/jquery.easyResponsiveTabs.js?v={{ current_version }}"></script>
<script type="text/javascript">
    jQuery(document).ready(function () {
    	menuLoading();
		$('#table_tab').easyResponsiveTabs({
            type: 'default', //Types: default, vertical, accordion
            width: 'auto', //auto or any width like 600px
            fit: true, // 100% fit in a container
            closed: 'accordion', // Start closed if in accordion view
            tabidentify: 'hor_1', // The tab groups identifier
            activate: function (event) { // Callback function if tab is switched
                var $tab = $(this);
                var $info = $('#nested-tabInfo');
                var $name = $('span', $info);
                $name.text($tab.text());
                $info.show();
            }
        });
 });
async function menuLoading() {
    const sessionProject = localStorage.getItem("project");
    if (sessionProject) {
        const project = JSON.parse(sessionProject);
        if (project.type == "Secondary") {
            $('#for-sec-ent-inv').text('Internal Invoice');
            $('#for-sec-ent-inv').attr('href', '/erp/sales/invoice_list/');
            $('#for-sec-ent-oa').text('Internal Work Order');
            $('#for-sec-ent-oa').attr('href', '/erp/sales/oa/view/');
        }
    } else {
        const projectList = await getProjects();
        if (projectList.projects && projectList.projects.length > 0) {
        const rootProjects = projectList.projects.filter(project => project.parent_id === 0);
        if(rootProjects.length >0){
        updateProject(rootProjects[0].id,rootProjects[0].parent_id,rootProjects[0].name,rootProjects[0].project_enterprise_id);}
        }
    }
}

if (jQuery(this).width() <= 767 ) {
	jQuery(document).ready(function () {
		$('#table_tab h2:first').click(); 
 });
}

</script>
<script>

messaging.requestPermission()
   .then(function() {
     console.log('Notification permission granted.');
     // TODO(developer): Retrieve an Instance ID token for use with FCM.
     // ...
     // Get Instance ID token. Initially this makes a network call, once retrieved
     // subsequent calls to getToken will return from cache.
     messaging.getToken()
     .then(function(currentToken) {
       if (currentToken) {
         sendTokenToServer(currentToken);
       } else {
         // Show permission request.
         console.log('No Instance ID token available. Request permission to generate one.');
         // Show permission UI.
         // updateUIForPushPermissionRequired();
         setTokenSentToServer(false);
       }
     })
     .catch(function(err) {
       console.log('An error occurred while retrieving token. ', err);
       // showToken('Error retrieving Instance ID token. ', err);
       // setTokenSentToServer(false);
     });

   })
   .catch(function(err) {
     console.log('Unable to get permission to notify.', err);
});

function sendTokenToServer(token){
   var enterprise_id = $("#enterprise_id").val()
   $.ajax({
		url: "/erp/commons/json/reg_fcm_id/",
		type: "POST",
		dataType: "json",
		data: {'fcm_id':token},
		success: function (json) {
		console.log('Notification Registration Success');
		},
		error: function (xhr, errmsg, err) {
			console.log(xhr.status + ": " + xhr.responseText);
		}
	});
	return false;
}

</script>

<div class="main-header">
	<ul class="nav navbar-nav" style="float: none;">
		<li class="dropdown dropdown-large main-header-search hide" style="float: none;">
			<!--<a href="#" class="dropdown-toggle" data-toggle="dropdown" style="padding: 8px 12px; "><i class="fa fa-search" aria-hidden="true"></i></a>-->
		</li>
		<li class="dropdown-menu-home dropdown-large main-header-plus" style="float: none; margin-top: 150px;padding-bottom:20px;">
			<!--<a href="#" class="dropdown-toggle dropdown-header-plus" ><i class="fa fa-plus rotate" aria-hidden="true"></i></a>-->
			<ul class="home-dropdown-menu dropdown-menu-large row arrow_box arrow_box_menu">
				<li class="col-sm-6">
					<ul>
						<li class="dropdown-header for-primary-ent">Finance</li>
						{% if logged_in_user|canEdit:'ACCOUNTS' %}
							<li class="for-primary-ent"><a href="/erp/accounts/voucher/">Voucher</a></li>
						{% else %}
							<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Accounts Module. Please contact the administrator." style="cursor: not-allowed;">
								<a class="disabled-link">Voucher</a>
							</li>
						{% endif %}
						<li class="divider for-primary-ent"></li>
						<li class="dropdown-header for-primary-ent">Purchase</li>
						{% if logged_in_user|canEdit:'PURCHASE INDENT' %}
							<li class="for-primary-ent"><a href="/erp/stores/indent/">Purchase Indent</a></li>
						{% else %}
							<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Indent Module. Please contact the administrator." style="cursor: not-allowed;">
								<a class="disabled-link">Purchase Indent</a>
							</li>
						{% endif %}
						{% if logged_in_user|canEdit:'PURCHASE' %}
							<li class="for-primary-ent"><a href="/erp/purchase/po/?id=custom-tab2">Purchase Order</a></li>
							<li class="for-primary-ent"><a href="/erp/purchase/jo/?id=custom-tab2 for-primary-ent">Job Order</a></li>
						{% else %}
							<li data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Purchase Module. Please contact the administrator." style="cursor: not-allowed;">
								<a class="disabled-link">Purchase Order</a>
							</li>
							<li data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Purchase Module. Please contact the administrator." style="cursor: not-allowed;">
								<a class="disabled-link">Job Order</a>
							</li>
						{% endif %}

						<li class="divider for-primary-ent"></li>

						<li class="dropdown-header">Sales</li>
						{% if logged_in_user|canEdit:'SALES' %}
							<li class="for-primary-ent"><a href="/erp/sales/sales_estimate/">Sales Estimate</a></li>
							<li><a id="for-sec-ent-oa" href="/erp/sales/oa/">Order Acknowledgement</a></li>
							<li><a id="for-sec-ent-inv" href="/erp/sales/invoice/">Invoice</a></li>
							<li class="for-primary-ent"><a href="/erp/sales/sr/">Sales Return</a></li>
						{% else %}
							<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Sales Module. Please contact the administrator." style="cursor: not-allowed;">
								<a class="disabled-link">Sales Estimate</a>
							</li>
							<li data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Sales Module. Please contact the administrator." style="cursor: not-allowed;">
								<a class="disabled-link">Order Acknowledgement</a>
							</li>
							<li data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Sales Module. Please contact the administrator." style="cursor: not-allowed;">
								<a class="disabled-link">Invoice</a>
							</li>
							<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Sales Module. Please contact the administrator." style="cursor: not-allowed;">
								<a class="disabled-link">Sales Return</a>
							</li>
						{% endif %}

					</ul>
				</li>
				<li class="col-sm-6">
					<ul>
						<li class="dropdown-header for-primary-ent">Audit</li>
						{% if logged_in_user|canEdit:'EXPENSES' %}
							<li class="for-primary-ent"><a href="/erp/expenses/home/<USER>">Expense</a></li>
						{% else %}
							<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Expense Module. Please contact the administrator." style="cursor: not-allowed;">
								<a class="disabled-link">Expense</a>
							</li>
						{% endif %}
						{% if logged_in_user|canEdit:'ICD' %}
							<li class="for-primary-ent"><a href="/erp/auditing/note/">Credit/Debit Note</a></li>
						{% else %}
							<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in ICD Module. Please contact the administrator." style="cursor: not-allowed;">
								<a class="disabled-link">Credit/Debit Note</a>
							</li>
						{% endif %}
						<li class="divider for-primary-ent"></li>
						<li class="dropdown-header for-primary-ent">Stores</li>
						{% if logged_in_user|canEdit:'STORES' %}
							<li class="for-primary-ent"><a href="/erp/stores/grn/">Goods Receipt</a></li>
							<li class="for-primary-ent"><a href="/erp/stores/dc/">Delivery Challan</a></li>
							<li class="for-primary-ent"><a href="/erp/stores/material_requisition/">Material Requisition</a></li>
						{% else %}
							<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Stores Module. Please contact the administrator." style="cursor: not-allowed;">
								<a class="disabled-link">Goods Receipt</a>
							</li>
							<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Stores Module. Please contact the administrator." style="cursor: not-allowed;">
								<a class="disabled-link">Delivery Challan</a>
							</li>
							<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Stores Module. Please contact the administrator." style="cursor: not-allowed;">
								<a class="disabled-link">Material Requisition</a>
							</li>
						{% endif %}
						<li class="divider for-primary-ent"></li>
						<li class="dropdown-header for-primary-ent">Production</li>
						<li class="for-primary-ent"><a href="/erp/production/manufacturing_indent/">Manufacturing Indent</a></li>
						<li class="for-primary-ent"><a href="/erp/production/production_plan/">Production Plan</a></li>
						{% if logged_in_user|canEdit:'STORES' %}
							<li class="for-primary-ent"><a href="/erp/stores/issue/">Issue</a></li>
							<li class="for-primary-ent"><a href="/erp/stores/irn/">Issue Return</a></li>
						{% else %}
							<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Stores Module. Please contact the administrator." style="cursor: not-allowed;">
								<a class="disabled-link">Issue</a>
							</li>
							<li class="for-primary-ent" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create/ upload in Stores Module. Please contact the administrator." style="cursor: not-allowed;">
								<a class="disabled-link">Issue Return</a>
							</li>
						{% endif %}
					</ul>
				</li>
			</ul>
		</li>
	</ul>
</div>
{% endblock %}
