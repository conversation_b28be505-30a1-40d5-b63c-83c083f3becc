$(function () {
    $(document).ready(function(){
        var ledger_choices = [];
        var account_group_choices = [];
        $.ajax({
            url:"/erp/accounts/json/ledger/populate_choices/",
            type: "POST",
            datatype: "JSON",
            data: null,
            success: function(response){
                for (i = 0; i <= response.length - 1; i++) {
                    ledger_choices.push({value: response[i][0],
                                        label: "[" + response[i][1] + "] " + response[i][2]});
                }
            }
        });

        $("#search").keyup(function(){
            if( $(this).val() != ""){
                $("#ledgerList tbody>tr").hide();
                $("#ledgerList td:contains-ci('" + $(this).val() + "')").parent("tr").show();
            }
            else{
                $("#ledgerList tbody>tr").show();
            }
        });

        $.extend($.expr[":"], {
            "contains-ci": function(elem, i, match, array) {
                return (elem.textContent || elem.innerText || $(elem).text() || "").toLowerCase().indexOf((match[3] || "").toLowerCase()) >= 0;
            }
        });

    });

    function enable_billable_checkbox(group_id, old_group_id){
        $.ajax({
            url: "/erp/accounts/json/check_is_billable/",
            type: "POST",
            dataType: "json",
            data: {"group_id": group_id},
            success: function(response) {
                if(response["is_billable"][0] == true){
                    $(".account-billable").removeClass('hide');
                    if($("#id_edit_ledger_id").val() == undefined){
                        if(response["is_default_billable"] == true){
                            $("#id_billable").prop('checked', true);
                        }
                        else{
                            $("#id_billable").prop('checked', false);
                        }
                    }
                    else{
                        if($("#is_ledger_billable").val() == "True"){
                            $("#id_billable").prop('checked', true);
                        }
                    }
                }
                else{
                    if($("#id_edit_ledger_id").val() != undefined){
                        if ($("#id_billable").is(":checked")){
                            swal({
                                title: 'Are You Sure!',
                                text: "You are moving a Billable Ledger under a non-billable Account Group. You will not be able to work with bills for this ledger. Do you want to continue?",
                                type: "warning",
                                showCancelButton: true,
                                confirmButtonColor: "#DD6B55",
                                confirmButtonText: "Continue",
                                closeOnConfirm: true,
                            }, function (isConfirm) {
                                if (isConfirm) {
                                    $("#id_billable").prop('checked', false);
                                    $(".account-billable").addClass('hide');
                                }
                                else{
                                    $("#id_group_select").val(old_group_id);
                                }
                            });
                        }
                        else{
                            $("#id_billable").prop('checked', false);
                            $(".account-billable").addClass('hide');
                        }
                    }
                    else{
                        $("#id_billable").prop('checked', false);
                        $(".account-billable").addClass('hide');
                    }
                }
            }
        });
    }

    $("#id_billable").change(function(){
        if($("#id_edit_ledger_id").val() != undefined && !$("#id_billable").is(":checked")){
            if ($("#is_ledger_billable").val() == "True"){
                swal({
                    title: 'Are You Sure!',
                    text: "You are moving a Billable Ledger to a non-billable Ledger. You will not be able to work with bills for this ledger. Do you want to continue?",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: "Continue",
                    closeOnConfirm: true,
                    closeOnCancel: true,
                }, function (isConfirm) {
                    if (isConfirm) {
                        $("#id_billable").prop('checked', false);
                    }
                    else{
                        $("#id_billable").prop('checked', true);
                    }
                });
            }
        }
    });

    $("#id_group_select").ready(function(){
        populateAccountGroupOptionsAsTree($("#id_enterprise_id").val(), $("#id_group_id").val(), "id_group_select", true, true, false);
        if($("#id_group_id").val() != undefined && $("#id_group_id").val() != ""){
            enable_billable_checkbox($("#id_group_id").val());
        }
    });

    $("#id_account_group-parent_group_select").ready(function(){
        populateAccountGroupOptionsAsTree($("#id_enterprise_id").val(), $("#id_account_group-parent_id").val(),
                                    "id_account_group-parent_group_select", false, false, true);
    });

    $("#id_group_select").change(function(){
        var old_group_id =  $("#id_group_id").val();
        $("#id_group_id").val($("#id_group_select option:selected").val());
        if($("#id_group_id").val() == "add_account_group"){
           $(".account-billable").addClass('hide');
           $("#modal_account_creation").modal('show');
        }
        else{
            enable_billable_checkbox($("#id_group_id").val(), old_group_id);
        }
    });

    $("#id_account_group-parent_group_select").change(function(){
        $("#id_account_group-parent_id").val($("#id_account_group-parent_group_select option:selected").val());
        $.ajax({
            url: "/erp/accounts/json/check_is_billable/",
            type: "POST",
            dataType: "json",
            data: {"group_id": $("#id_account_group-parent_id").val()},
            success: function(response) {
                if(response["is_billable"][0] == true){
                    $("#id_account_group-billable").prop('checked', true);
                }
                else{
                    $("#id_account_group-billable").prop('checked', false);
                }
            }
        });
    });

    $("#book_of_entries").ready(function(){
        var entry_count = parseInt($("#entry_count").val());
        for(var i=1; i<=entry_count;i++){
            if($("#is_debit_" + i).is(":checked")){
                $("#balance_" + i + "_debit").val((parseFloat($("#balance_" + parseInt(i-1) + "_debit").val()) + parseFloat($("#amount_" + i).val())).toFixed(2));
                $("#balance_" + i + "_credit").val((parseFloat($("#balance_" + parseInt(i-1) + "_credit").val())).toFixed(2));
            }else{
                $("#balance_" + i + "_credit").val((parseFloat($("#balance_" + parseInt(i-1) + "_credit").val()) + parseFloat($("#amount_" + i).val())).toFixed(2));
                $("#balance_" + i + "_debit").val((parseFloat($("#balance_" + parseInt(i-1) + "_debit").val())).toFixed(2));
            }
            var net_balance = (parseFloat($("#balance_" + i + "_credit").val()) - parseFloat($("#balance_" + i + "_debit").val())).toFixed(2);
            if(net_balance > 0){
                var cr_dr = "Cr";
            } else {
                var cr_dr = "Dr";
                net_balance = net_balance * parseFloat(-1.00);
            }
            $("#balance_" + i).html(net_balance + " " + cr_dr);
        }
        if (parseFloat($("#id_total_debit").val()) >= parseFloat($("#id_total_credit").val())){
            attribute = parseFloat($("#id_total_debit").val()) == parseFloat($("#id_total_credit").val()) ? "" : " Dr";
            $("#period_closing").html((parseFloat($("#id_total_debit").val()) - parseFloat($("#id_total_credit").val())).toFixed(2) + attribute);
        } else {
            $("#period_closing").html((parseFloat($("#id_total_credit").val()) - parseFloat($("#id_total_debit").val())).toFixed(2) + " Cr");
        }
    });
});

function populateAccountGroupOptionsAsTree(enterprise_id, selected_group_id, target_field_id, need_add_option,
                                    for_ledger_creation, for_subgroup_creation){
    $.ajax({
        url: "/erp/accounts/json/populate_account_group_tree/",
        type: "POST",
        dataType: "json",
        data: {
            "enterprise_id": enterprise_id, "selected_group_id": selected_group_id, "need_add_option": need_add_option,
            "for_subgroup_creation": for_subgroup_creation, "for_ledger_creation": for_ledger_creation},
        success: function(group_options) {
            option_html = "";
            for(i=0; i < group_options.length; i++){
                option_html += "<option value=\"" + group_options[i]["value"] + "\"" + group_options[i]["attrs"] + ">"
                                + group_options[i]["label"] + "</option>";
            }
            $("#" + target_field_id).html(option_html);
        }
    });
}