# Create your views here.

import json

import simplejson
from django.contrib.auth.models import User
from django.contrib.sessions.models import Session
from django.http import HttpResponse
from django.utils.encoding import smart_str

from erp import logger
from erp.admin.backend import EnterpriseProfileService
from erp.auth import ENTERPRISE_IN_SESSION_KEY, USER_IN_SESSION_KEY, ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.commons.backend import sendMail
from erp.dao import DataAccessObject
from erp.helper import populateMakes
from erp.properties import CONTACT_MAIL, USER_RESTRICT_CONTACT_MAIL, FEEDBACK_MAIL
from erp.tags import populateTags
from settings import XSASSIST_MAIL_ID
from util.api_util import response_code
from util.ftp_helper import FTPUtil
from util.helper import readFile


def getUsername(session_id):
	"""
	Returns the name of the user associated with the active session.

	:param session_id:
	:return:
	"""
	try:
		session = Session.objects.get(session_key=session_id)
		uid = session.get_decoded().get('_auth_user_id')
		user = User.objects.get(pk=uid)
		return [uid, user]
	except Exception as e:
		logger.info("Fetching Username Failed: %s" % e)
		return [-1, -1]


def isAuthenticSession(session_id):
	"""
	Verifies if the session is still active

	:param session_id:
	:return:
	"""
	if getUsername(session_id)[0] != -1:
		return True
	return False


def keepSessionAlive(request):
	"""
	Dummy Call to keep Session alive.
	To be used to consider relevant Browser-client side actions to determine if the User is active or idle.

	:param request:
	:return:
	"""
	logger.info("Dummy call to Keep User Logged In")
	return HttpResponse()


def populateTagOptions(request):
	"""
	
	:param request: 
	:return: 
	"""
	request_handler = RequestHandler(request)
	try:
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		tag_list = [tag.tag for tag in populateTags(enterprise_id)]
	except Exception as e:
		logger.exception("Tag option populating failed %s" % e)
		raise
	return HttpResponse(content=simplejson.dumps(tag_list), mimetype='application/json')


def populateMakeOptions(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	try:
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		make_list = populateMakes(enterprise_id)
		logger.info("Populating %s Makes for enterprise %s" % (len(make_list), enterprise_id))
	except Exception as e:
		logger.exception("Make option populating failed %s" % e)
		raise
	return HttpResponse(content=simplejson.dumps(make_list), mimetype='application/json')


def getPreviousClosureDate(request):
	"""
	
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	try:
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		_closure_date = None
		dao = DataAccessObject()
		_previous_closure_voucher = dao.getPreviousBookClosureVoucher(enterprise_id=enterprise_id)
		if _previous_closure_voucher:
			_closure_date = _previous_closure_voucher.voucher_date if _previous_closure_voucher else None
		logger.debug("Querying Previous Book Closure Date - %s for enterprise %s" % (_closure_date, enterprise_id))
	except Exception as e:
		logger.exception("Fetching Closure Date failed %s" % e)
		raise
	return HttpResponse(
		content=simplejson.dumps(_closure_date.strftime("%Y-%m-%d") if _closure_date else ""), mimetype='application/json')


def downloadDocument(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	file_name = request_handler.getData('file_name')
	file_type = file_name.split('.')[-1]
	mime_type = 'application/pdf'
	file_path = "/release_notes/"
	if file_type == 'pdf':
		mime_type = 'application/pdf'
	elif file_type == 'html':
		mime_type = 'text/json'
	elif file_type == 'webm':
		mime_type = 'video/webm'
		file_path = "/release_notes/videos/"
	elif file_type in ('png', 'gif', 'jpg', 'jpeg'):
		mime_type = 'image/%s' % file_type
		file_path = "/release_notes/images/"
	try:
		data = FTPUtil().download(file_path=file_path, filename=file_name)
		if file_type == 'html':
			response = HttpResponse(json.dumps(data), mimetype=mime_type)
		else:
			response = HttpResponse(data, mimetype=mime_type)
		response['Content-Disposition'] = 'filename=%s' % smart_str(file_name)
		return response
	except Exception as e:
		raise e


def send_contact_us_mail(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		service = EnterpriseProfileService()
		user = rh.getSessionAttribute(USER_IN_SESSION_KEY)
		enterprise_id = rh.getPostData('enterprise_id')
		name = rh.getPostData('name')
		contact_no = rh.getPostData('contact_no')
		user_email = rh.getPostData('user_email')
		feedback = rh.getPostData('feedback')
		best_time_to_call = rh.getPostData('best_time_to_call')
		subject = rh.getPostData('subject')
		enterprise_primary_contact_name = ""
		enterprise_primary_contact_no = ""
		enterprise_primary_contact_email = ""
		from_alias = "XSerp"
		from_addr = "<EMAIL>"
		if user_email is None:
			response = response_code.paramMissing()
		else:
			if enterprise_id:
				enterprise = rh.getSessionAttribute(ENTERPRISE_IN_SESSION_KEY)
				enterprise_contact_detail = service.getContactDetails(enterprise_id=enterprise.id, sequence_id=1)
				subject = "[Request] [%s - %s] User Accounts - reg" % (enterprise.code, enterprise.name)
				for contact in enterprise_contact_detail:
					enterprise_primary_contact_name = contact["name"]
					enterprise_primary_contact_no = contact["phone_no"]
					enterprise_primary_contact_email = contact["email"]

				message = readFile(USER_RESTRICT_CONTACT_MAIL).replace("{{enterprise_name}}", enterprise.name).replace(
					"{{enterprise_code}}", enterprise.code).replace(
					"{{enterprise_primary_contact_name}}", enterprise_primary_contact_name).replace(
					"{{enterprise_primary_contact_no}}", enterprise_primary_contact_no).replace(
					"{{enterprise_primary_contact_email}}", enterprise_primary_contact_email).replace(
					"{{user_first_name}}", user.first_name).replace("{{user_last_name}}", user.last_name).replace(
					"{{user_email}}", user.email)
			else:
				if subject.find("[Feedback]") == -1:
					message = readFile(CONTACT_MAIL).replace("{{name}}", name).replace("{{email}}", user_email).replace(
						"{{mobile-no}}", contact_no).replace("{{text Best-time-to-call}}", best_time_to_call).replace(
						"{{subject}}", rh.getPostData('user_subject'))
				else:
					message = readFile(FEEDBACK_MAIL).replace("{{name}}", name).replace("{{email}}", user_email).replace(
						"{{feedback}}", feedback).replace("{{enterprise}}", "").replace("{{footer_text}}", "Feedback")

			if sendMail(
				recipients=XSASSIST_MAIL_ID, subject=subject, body=message, from_alias=from_alias,
				from_addr=from_addr, reply_to=(user_email,)):
				response = response_code.success()
				message = "Your mail has been sent successfully."
				response['custom_message'] = message
			else:
				response = response_code.failure()
				message = "Failed to send e-mail"
				response['custom_message'] = message
	except Exception as e:
		logger.exception("Failed to send the e-mail request! %s" % e.message)
		response = response_code.internalError()
		response['custom_message'] = "Could not send e-mail"
	return HttpResponse(json.dumps(response), 'content-type=text/json')
