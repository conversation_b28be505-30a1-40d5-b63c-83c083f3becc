{% extends "accounts/sidebar.html" %}
{% block gstr2 %}
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}"/>
<link type="text/css" rel="stylesheet" href="/site_media/css/chosen.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/datatables.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/fixedHeader.bootstrap.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/dataTables.searchHighlight.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/tabs-vertical.css?v={{ current_version }}">

<script type="text/javascript" src="/site_media/js/bootstrap-datepicker.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-multiselect.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/chosen.jquery.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/jquery.easyResponsiveTabs.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/daterangepicker.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/datatables.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/dataTables.fixedHeader.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/dataTables.searchHighlight.js?v={{ current_version }}"></script>
<style type="text/css">
        .dataTables_scrollBody {
            overflow: auto hidden !important;
        }

        .dataTables_scrollBody table{
            margin-bottom: 20px;
        }

        .tabs{
            width: 200px;
            text-align: center
        }

        #matched_bill tbody td:nth-child(4),
        #matched_bill tbody td:nth-child(7),
        #matched_bill tbody td:nth-child(8),
        #matched_bill tbody td:nth-child(9),
        #matched_bill tbody td:nth-child(10){
            text-align: right;

        }

         #matched_bill tbody td:nth-child(1),
         #matched_bill tbody td:nth-child(2),
         #matched_bill tbody td:nth-child(3),
         #matched_bill tbody td:nth-child(5),
         #matched_bill tbody td:nth-child(6){
             text-align: center;
         }

        #gstr2bonly tbody td:nth-child(4),
        #gstr2bonly tbody td:nth-child(7),
        #gstr2bonly tbody td:nth-child(8),
        #gstr2bonly tbody td:nth-child(9),
        #gstr2bonly tbody td:nth-child(10){
            text-align: right;
        }

         #gstr2bonly tbody td:nth-child(1),
         #gstr2bonly tbody td:nth-child(2),
         #gstr2bonly tbody td:nth-child(3),
         #gstr2bonly tbody td:nth-child(5),
         #gstr2bonly tbody td:nth-child(6){
             text-align: center;
         }

        #pronly tbody td:nth-child(4),
        #pronly tbody td:nth-child(7),
        #pronly tbody td:nth-child(8),
        #pronly tbody td:nth-child(9),
        #pronly tbody td:nth-child(10){
            text-align: right;

        }

         #pronly tbody td:nth-child(1),
         #pronly tbody td:nth-child(2),
         #pronly tbody td:nth-child(3),
         #pronly tbody td:nth-child(5),
         #pronly tbody td:nth-child(6){
             text-align: center;
         }

         #matchable_bill tbody td:nth-child(4),
         #matchable_bill tbody td:nth-child(7),
         #matchable_bill tbody td:nth-child(8),
         #matchable_bill tbody td:nth-child(9),
         #matchable_bill tbody td:nth-child(10){
            text-align: right;
         }

		#matchable_bill tbody td:nth-child(1),
		#matchable_bill tbody td:nth-child(2),
		#matchable_bill tbody td:nth-child(3),
		#matchable_bill tbody td:nth-child(5),
		#matchable_bill tbody td:nth-child(6){
			text-align: center;
		}

		#matchable_bill_tbl_tbody tr:nth-child(1){
			border-top: 1px solid #ddd;
		}

		.matchable_default_data td{
			font-size:13px;
			font-weight: bold;
		}
		.matchable_default_data{
			border-top: 2px solid #999;
		}
		.matchable_partial{
			background: #FFFFCC !important;
			border: 1px solid #FFFFCC !important;
			font-style: italic;
		}
		.matchable_partial td,
		.matchable_probable td,
		.matchable_unmatched td{
			font-size: 11px;
		}
		.matchable_partial_text{
			color: #00994C !important;
			text-align: center;
		}
		.matchable_probable{
			background:#FFE5CC !important;
			border: 1px solid #FFE5CC !important;
			font-style: italic;
		}

		.matchable_probable_text{
			color: #7736bd !important;
			text-align: center;
		}
		.matchable_unmatched{
			background : #ebe0f7 !important;
			border: 1px solid #CCCCFF !important;
			font-style: italic;
		}

		.matchable_unmatched_text{
			color: #FF6666 !important;
			text-align: center;
		}

		.exact_match_bg{
			background: #8fee90;
		}
		.partial_match_bg{
			background: #fff44e;
		}
		.probable_match_bg{
			background: #fafad2;
		}
		.mismatch_bg{
			background: #ffccca;
		}
		.unmatch_bg{
			background: #fff;
		}
		.total_bg{
			 background: #e9ecef;
			 font-weight: bold;
		}
		.info_icon{
			padding: 3px 6px 3px 6px;
			border-radius: 50px;
			font-size: 8px;
			background: #666;
			color: #fff;
			margin-left: 8px;
		}
		.status_indication{
		    position: absolute;
		    right: 150px;
		    top: 75px;
		}
		.matchable_inv{
			text-align: left;
			float:left;
			margin-left: -15px;
		}
		.status_data{
			margin-right:20px;
			top: -5px;
			position: relative;
		}
		.status_partial{
			height: 20px;
			width: 20px;
			background-color: #ffc;
			border-radius: 50%;
			display: inline-block;
			border: 1px solid #e6e631;
		}

		.status_probable{
			height: 20px;
			width: 20px;
			background-color: #FFE5CC;
			border-radius: 50%;
			display: inline-block;
			border: 1px solid #e0a871;
		}

		.status_unmatched{
			height: 20px;
			width: 20px;
			background-color: #ebe0f7;
			border-radius: 50%;
			display: inline-block;
			border:1px solid #b387e4;
		}

		.btn_tbl_excel{
			margin-top: -50px;
			background: #FFF;
			 color: #337ab7;
			 margin-right: 16px;
		}

		.gst-api-steps{
			background: #FFF;
		    padding: 15px;
		    border-radius: 0 0 15px 15px;
		    border: solid 1px #ccc;
		    margin-top: 15px;
		    line-height: 26px;
		}

</style>
<div class="right-content-container download-icon-onTop">
	<div class="page-title-container">
		<span class="page-title">GSTR-3B <span style="font-size:12px;">Data Generation</span></span>
	</div>

	<div class="container-fluid">
		<div class="row">
			<div class="col-lg-12">
				<div class="content_bg">
					<ul class="resp-tabs-list hor_1"></ul>
					<div class="resp-tabs-container hor_1">
						<div class="row">
							<div class="add_table">
								<div class="col-lg-12 add_table_content">
									<input type="hidden" id="txt-matched-taxval"/>
									<input type="hidden" id="txt-matched-totalval"/>
									<div class="filter-components"
									     style="width: 100%; margin-left: 0; margin-bottom: 0;">
										<div class="filter-components-container">
											<div class="dropdown">
												<button class="btn dropdown-toggle btn-filter" type="button"
												        data-toggle="dropdown" data-tooltip="tooltip"
												        title="Modify&nbsp;filter">
													<i class="fa fa-filter"></i>
												</button>
												<span class="dropdown-menu arrow_box arrow_box_filter">
	                                                    <div class="col-sm-12 form-group">
	                                                        <label>Date Range</label>
                                                            <input type="text" id="gstr3b_month"
                                                                   class="form-control select_month"
                                                                   placeholder="Select Month" value=""
                                                                   style="background: #FFF; font-size: 14px !important;"
                                                                   readonly>
	                                                    </div>
	                                                    <div class="col-sm-12 form-group">
	                                                        <label>Tolerance for match</label>
	                                                        <div>
	                                                            <select class="form-control chosen-select" name="select"
	                                                                    id="tolerance_match">
										                            <option value="0">0</option>
										                            <option value="1">1</option>
										                            <option value="2">2</option>
										                            <option value="3">3</option>
										                            <option value="4">4</option>
										                            <option value="5">5</option>
										                            <option value="6">6</option>
										                            <option value="7">7</option>
										                            <option value="8">8</option>
										                            <option value="9">9</option>
										                            <option value="10">10</option>
								                                </select>
	                                                        </div>
	                                                    </div>
														<div class="col-sm-12 form-group">
	                                                        <label>Approximation</label>
	                                                        <div>
	                                                            <select class="form-control chosen-select" name="select"
	                                                                    id="approximation_match">
										                            <option value="false">False</option>
		                                                            <option value="true">True</option>
								                                </select>
	                                                        </div>
	                                                    </div>
	                                                    <div class="filter-footer">
	                                                        <button type="submit" class="btn btn-save"
	                                                                id="gstr3breportview">Apply</button>
	                                                    </div>
                                                    </span>
											</div>
											<span class='filtered-condition filtered-date'>Date: <b></b></span>
											<span class='filtered-condition filtered-tolerance'>Tolerance: <b></b></span>
											<span class='filtered-condition filtered-approximation'>Approximation: <b></b></span>
											<button class="btn btn-add-new pull-right" style="width:130px;"
											        onclick="reportGstr3b();">GSTR3B
											</button>
										</div>
									</div>

									<div style="float: right;margin-top: -18px;">
											<span class="status_indication">
												<span class="status_partial"></span>
												<span class="status_data"> - Partial</span>
												<span class="status_probable"></span>
												<span class="status_data"> - Probable</span>
												<span class="status_unmatched"></span>
												<span class="status_data"> - Unmatched</span>
											</span>
										<span>
			                                    <button class="btn btn-save btn-margin-1"
			                                            onclick="saveGstr3b();">SAVE</button>
			                                    <button class="btn btn-save btn-margin-1"
			                                            onclick="clearGstr3b();">CLEAR</button>
			                                </span>
									</div>
									<ul class="nav nav-tabs" id="ul-report-header" role="tablist"
									    style="margin-bottom: 60px;">
										<li role="presentation" class="active">
											<a href="#tab1" id="tab-matched-bill" role="tab" data-toggle="tab">Matched
											                                                                   Bills</a>
										</li>
										<li role="presentation">
											<a href="#tab2" role="tab" data-toggle="tab">Matchable Bills</a>
										</li>
										<li role="presentation">
											<a href="#tab3" role="tab" data-toggle="tab" id="in_gstr2b_tab">In GSTR2B
											                                                                Only</a>
										</li>
										<li role="presentation">
											<a href="#tab4" role="tab" data-toggle="tab" id="tab_in-pr">In PR Only</a>
										</li>
									</ul>
									<button class="btn btn-add-new pull-right btn_tbl_excel"
									        onclick="tablesToExcelLoad();" data-tooltip="tooltip"
									        title="Download Entire Statement in XLS"><i class="fa fa-download"
									                                                    aria-hidden="true"></i> &nbsp;
									                                                                            as xls
									</button>

									<div class="tab-content report-tab-content"
									     style="margin-top: -62px;width:100%;padding-top: 55px;">
										<div role="tabpanel" class="tab-pane active" id="tab1">
											<table class="table table-bordered custom-table table-striped"
											       id="matched_bill" style="width: 100%;">
												<thead class="th-vertical-center">
												<tr class='total-header'>
													<th data-style="tableHeader" style="width: 5%;">S. No</th>
													<th data-style="tableHeader" style="width: 10%;">Bill No</th>
													<th data-style="tableHeader" style="width: 10%;">GSTIN</th>
													<th data-style="tableHeader" style="width: 10%;">Taxable Value</th>
													<th data-style="tableHeader" style="width: 10%;">Date</th>
													<th data-style="tableHeader" style="width: 7%;">Type<i class="fa fa-info info_icon"
													                              title="R - Regular, SEZWP - SEZ supplies with payment, SEZWOP - SEZ supplies without payment,DE - Deemed Exp, CBW - Intra-State supplies attracting IGST"></i>
													</th>
													<th data-style="tableHeader" style="width: 10%;">CGST</th>
													<th data-style="tableHeader" style="width: 10%;">SGST</th>
													<th data-style="tableHeader" style="width: 10%;">IGST</th>
													<th data-style="tableHeader" style="width: 10%;">CESS</th>
													<th data-style="tableHeader">STATUS</th>
												</tr>
												</thead>
												<tbody id="matched_bill_tbl_tbody" class="gstr-report-body"></tbody>
											</table>
										</div>

										<div role="tabpanel" class="tab-pane" id="tab2">
											<table class="table table-bordered custom-table table-striped"
											       id="matchable_bill" style="width: 100%;">
												<thead class="th-vertical-center">
												<tr class='total-header'>
													<th data-style="tableHeader" style="width: 5%;">S. No</th>
													<th data-style="tableHeader" style="width: 10%;">Bill No</th>
													<th data-style="tableHeader" style="width: 10%;">GSTIN</th>
													<th data-style="tableHeader" style="width: 10%;">Taxable Value</th>
													<th data-style="tableHeader" style="width: 10%;">Date</th>
													<th data-style="tableHeader" style="width: 7%;">Type<i class="fa fa-info info_icon"
													                              title="R - Regular, SEZWP - SEZ supplies with payment, SEZWOP - SEZ supplies without payment,DE - Deemed Exp, CBW - Intra-State supplies attracting IGST"></i>
													</th>
													<th data-style="tableHeader" style="width: 10%;">CGST</th>
													<th data-style="tableHeader" style="width: 10%;">SGST</th>
													<th data-style="tableHeader" style="width: 10%;">IGST</th>
													<th data-style="tableHeader" style="width: 10%;">CESS</th>
													<th data-style="tableHeader" style="width: 10%;">STATUS</th>
												</tr>
												</thead>
												<tbody id="matchable_bill_tbl_tbody" class="gstr-report-body"></tbody>
											</table>
										</div>

										<div role="tabpanel" class="tab-pane" id="tab3">
											<table class="table table-bordered custom-table table-striped"
											       id="gstr2bonly" style="width: 100%;">
												<thead class="th-vertical-center">
												<tr class='total-header'>
													<th data-style="tableHeader" style="width: 5%;">S. No</th>
													<th data-style="tableHeader" style="width: 10%;">Bill No</th>
													<th data-style="tableHeader" style="width: 10%;">GSTIN</th>
													<th data-style="tableHeader" style="width: 10%;">Taxable Value</th>
													<th data-style="tableHeader" style="width: 10%;">Date</th>
													<th data-style="tableHeader" style="width: 7%;">Type<i class="fa fa-info info_icon"
													                              title="R - Regular, SEZWP - SEZ supplies with payment, SEZWOP - SEZ supplies without payment,DE - Deemed Exp, CBW - Intra-State supplies attracting IGST"></i>
													</th>
													<th data-style="tableHeader" style="width: 10%;">CGST</th>
													<th data-style="tableHeader" style="width: 10%;">SGST</th>
													<th data-style="tableHeader" style="width: 10%;">IGST</th>
													<th data-style="tableHeader" style="width: 10%;">CESS</th>
												</tr>
												</thead>
												<tbody id="gstr2bonly_tbl_tbody" class="gstr-report-body"></tbody>
											</table>
										</div>

										<div role="tabpanel" class="tab-pane" id="tab4">
											<table class="table table-bordered custom-table table-striped" id="pronly"
											       style="width: 100%; padding-right: 15px;">
												<thead class="th-vertical-center">
												<tr class='total-header'>
													<th data-style="tableHeader" style="width: 5%;">S. No</th>
													<th data-style="tableHeader" style="width: 10%;">Bill No</th>
													<th data-style="tableHeader" style="width: 10%;">GSTIN</th>
													<th data-style="tableHeader" style="width: 10%;">Taxable Value</th>
													<th data-style="tableHeader" style="width: 10%;">Date</th>
													<th data-style="tableHeader" style="width: 7%;">Type<i class="fa fa-info info_icon"
													                              title="R - Regular, SEZWP - SEZ supplies with payment, SEZWOP - SEZ supplies without payment,DE - Deemed Exp, CBW - Intra-State supplies attracting IGST"></i>
													</th>
													<th data-style="tableHeader" style="width: 10%;">CGST</th>
													<th data-style="tableHeader" style="width: 10%;">SGST</th>
													<th data-style="tableHeader" style="width: 10%;">IGST</th>
													<th data-style="tableHeader" style="width: 10%;">CESS</th>
												</tr>
												</thead>
												<tbody id="pronly_tbl_tbody" class="gstr-report-body"></tbody>
											</table>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						{% block stock_statement %}
						{% endblock %}
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<div id="report_gstr3b" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg" style="width: 60%;">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title"><span
						class="oa_dc_no_heading"> <b>Summary of GSTR-2B and Purchase Register(PR)</b> </span></h4>
			</div>
			<div class="modal-body" style="overflow-x: auto;">
				<table id="report_gstr-3b" class="table table-bordered custom-table tableWithText table-striped">
					<thead>
					<tr align="center">
						<th rowspan="2">Matching Result</th>
						<th colspan="2">No.of documents</th>
						<th rowspan="2">Total Taxable Value(₹)</th>
						<th rowspan="2">Total Tax Amount(₹)</th>
					</tr>
					<tr>
						<th>GSTR-2B</th>
						<th>Purchase Register</th>
					</tr>
					<tr class="exact_match_bg">
						<td>Exact Match(All 7 parametrs match)<i class="fa fa-info info_icon" data-tooltip="tooltip"
						                                         data-placement="right"
						                                         data-title="All the parameters matchin records of both Form GSTR-2B and purchase register"></i>
						</td>
						<td class="text-right total_exactmatch"></td>
						<td class="text-right total_purchase_exactmatch">-</td>
						<td class="text-right matched-totalval"></td>
						<td class="text-right matched-taxval"></td>
					</tr>
					<tr class="partial_match_bg">
						<td>Partial Match<i class="fa fa-info info_icon" data-tooltip="tooltip" data-placement="right"
						                    data-title="Records that have matched partially"></i></td>
						<td class="text-right total_partialmatch"></td>
						<td class="text-right total_purchase_partialmatch">-</td>
						<td class="text-right partial_total_inv_value"></td>
						<td class="text-right partial_total_inv_tax">-</td>
					</tr>
					<tr class="probable_match_bg">
						<td>Probable match
							<i class="fa fa-info info_icon" data-tooltip="tooltip" data-placement="right"
							   data-title="Mismatch in between GSTIN and Document type of details in Form GSTR-2B and purchase register and complete match in all other parameters"></i>
						</td>
						<td class="text-right total_probablematch"></td>
						<td class="text-right total_purchase_probablematch">-</td>
						<td class="text-right probable_total_inv_value"></td>
						<td class="text-right probable_total_inv_tax">-</td>
					</tr>
					<tr class="mismatch_bg">
						<td>Mismatch(Few parametrs do not match or record do not exsit in GSTR-2B/PR)</td>
						<td class="text-right total_mismatched_gstr2b" style="font-weight: bold;"></td>
						<td class="text-right total-mismatchedpurchase" style="font-weight: bold;"></td>
						<td class="text-right total-mismatchinv" style="font-weight: bold;"></td>
						<td class="text-right total-miscmatchtax" style="font-weight: bold;"></td>
					</tr>
					<tr class="unmatch_bg">
						<td>
							<ul>
								<li>Unmatched(2 or more parameters no match)</li>
							</ul>
						</td>
						<td class="text-right total_unmatched"></td>
						<td class="text-right total_purchase_unmatched">-</td>
						<td class="text-right unmatched_total_inv_value"></td>
						<td class="text-right unmatched_total_inv_tax"></td>
					</tr>
					<tr class="unmatch_bg">
						<td>
							<ul>
								<li>In GSTR-2B not in PR <i class="fa fa-info info_icon" data-tooltip="tooltip"
								                            data-placement="right"
								                            data-title="The records that are present in GSTR-2B but not available in the purchase register."></i>
								</li>
							</ul>
						</td>
						<td class="text-right total_gstr2b">-</td>
						<td class="text-right">0</td>
						<td class="text-right gstr2b_total_value"></td>
						<td class="text-right gstr2b_total_tax"></td>
					</tr>
					<tr class="unmatch_bg">
						<td>
							<ul>
								<li>In PR not in GSTR-2B<i class="fa fa-info info_icon" data-tooltip="tooltip"
								                           data-placement="right"
								                           data-title="The records that are present in the purchase register but not available in GSTR-2B."></i>
								</li>
							</ul>
						</td>
						<td class="text-right ">0</td>
						<td class="text-right total_pr">-</td>
						<td class="text-right pr_total_value"></td>
						<td class="text-right pr_total_tax"></td>
					</tr>
					<tr class="total_bg">
						<td align='center'>Total</td>
						<td class="text-right overall_total_gstr2b"></td>
						<td class="text-right total_purchase_register"></td>
						<td class="text-right total_invoice_value"></td>
						<td class="text-right total_taxable_value"></td>
					</tr>

					</thead>
				</table>
				<div class="modal-footer">
					<a href="#" class="btn btn-cancel" data-dismiss="modal">Close</a>
				</div>
			</div>
		</div>
	</div>
</div>
<div id="gstr3b_login" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog">
	<div class="modal-dialog" style="width: 450px;">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">GST LOGIN</h4>
			</div>
			<div class="modal-body">
				<div class="col-sm-12 general-warning" style="margin-bottom: 0;">
					<b>Note</b>
					<ul>
						<li>Kindly Authenticate GST API access.</li>
						<li>We understand your privacy. We Encrypt your credential before saving. </li>
					</ul>
					<a role='button' onclick='toggleAPISteps();'>Steps to get GST API access <i class="fa fa-chevron-down" aria-hidden="true"></i> </a>
					<div class="gst-api-steps" style="display: none;">
						<h2 style="margin-top: 0;font-size:24px;">GST API Request</h2>
						<div class="api-steps">
							<i>Step 1:</i> Login to <a target='_blank' href='https://services.gst.gov.in/services/login'> https://services.gst.gov.in/services/login</a><br />
							<i>Step 2:</i> Once you successfully logged in, go to my API access<br />
							<i>Step 3:</i> Enable the API access and select the duration for 30 days and click confirm.
						</div>
					</div>
				</div>

				<div id="gstr_login" style="padding: 15px;">
					<label style="padding-bottom:5px;margin-top:8px;">User name</label>
					<input id="gstr_username" type="text" class="form-control" placeholder="Enter username"
					       name="gstr_username" style="width: 100%;" maxlength="50" autocomplete="off">
				</div>
				<div id="gstr_otp" style="padding: 15px;" class="hide">
					<label style="padding-bottom:5px;">OTP</label>
					<input id="gstr3b_otp" type="password" class="form-control" placeholder="Enter OTP" name="otp"
					       style="width: 100%;" autocomplete="off">
				</div>
			</div>
			<div class="modal-footer">
				<a role="button" class="btn btn-cancel" id="gstr3b_cancel" data-dismiss="modal">Cancel</a>
				<a role="button" class="btn btn-save" id="gstr3b_username_login">Send OTP Request</a>
				<a role="button" class="btn btn-save hide" id="gstr3b_password">Ok</a>
			</div>
		</div>
	</div>
</div>

<script>
        $(document).ready(function () {
			var sDate = new Date();
		    var setDate = moment(new Date(sDate)).subtract(1,'months').format("MMM, YYYY");
			$('.select_month').val(setDate);
			$('.select_month').datepicker({
				autoclose: true,
				minViewMode: 1,
				format: 'M, yyyy',
				startDate: 'July, 2017',
			});
			$(".filtered-date b").text($('.select_month').val())
			$(".filtered-tolerance b").text($("#tolerance_match").val());
			$(".filtered-approximation b").text($("#approximation_match option:selected").val());
            setTimeout(function () {
                grnReportView();
            }, 10);
			$("#tab-matched-bill").click(function(){
				setTimeout(function(){
					$(window).resize();
				},300);
			});
        });

        function showGstOtp() {
            $("#gstr3b_login").modal('show');
            $("#gstr_login").addClass('hide')
            $("#gstr_otp").removeClass('hide');
			$("#gstr3b_password").removeClass('hide');
			$("#gstr3b_username_login").addClass("hide");
        }

		$("#gstr3b_username_login").click(function(){
			$(".error-border").removeClass('error-border');
	   		$(".custom-error-message").remove();

			var ControlCollections = [
				{
					controltype: 'textbox',
					controlid: 'gstr_username',
					isrequired: true,
					errormsg: 'User Name is required.'
				}
			];
			var result = JSCustomValidator.JSvalidate(ControlCollections);
			if(result) {
				sendOtpRequest();
			}
		});


		 function sendOtpRequest() {
            gst_username = $('#gstr_username').val();
            $.ajax({
                url : "/erp/accounts/json/gstr3b_save_gst_username/",
                type : "POST",
                dataType: "json",
                data : {
                    gst_username: gst_username
                },
                success : function(data) {
                    if(data.error != undefined){
                        if(data.error.error_cd == "AUTH4037") {
                            swal("", "Username is invalid or expired.", "warning");
                        }
                        else {
					        swal("", data.error.message, "warning");
				        }
					}
					else {
						showGstOtp();
                    }

                },error : function(xhr,errmsg,err) {
                    console.log(xhr.status + ": " + xhr.responseText);
                    $("#loading").addClass('hide').removeClass('show');
                }
            });
		 }

		$("#gstr3b_password").click(function(){
			$(".error-border").removeClass('error-border');
	   		$(".custom-error-message").remove();

			var ControlCollections = [
				{
					controltype: 'textbox',
					controlid: 'gstr3b_otp',
					isrequired: true,
					errormsg: 'OTP is required.'
				}
			];
			var result = JSCustomValidator.JSvalidate(ControlCollections);
			if(result) {
				grnReportView();
			}
		});

        function updateFilterText() {
			$("#gstr3breportview").click(function(){
				$(".filtered-date b").text($(".select_month").val());
				$(".filtered-tolerance b").text($("#tolerance_match option:selected").val());
				$(".filtered-approximation b").text($("#approximation_match option:selected").val());
				 setTimeout(function(){
					$(".filter-components-container").find(".dropdown").removeClass('open');
					$(".filter-components-container").find(".dropdown-menu").removeClass('show');
				 },100);
				grnReportView();
			})
		}

		function formatDate(input) {
		  var datePart = input.match(/\d+/g),
		  day = datePart[0];
		  month = datePart[1],
		  year = datePart[2];
		  return month+'/'+day+'/'+year;
		}

        function loopTblData(data){
            var pr_json = {
                    "b2b": []
                };
            var list = [];
            for(var i = 0; i < data.length; i++) {
                var item = data[i];
                if(list.indexOf(item[4]) === -1)
                {
                    list.push(item[4]);
                    var inv_list = [];

                    for(var j = 0; j < data.length; j++){
                        var item_new = data[j];
                        if(item[4]==item_new[4]){
                            if(item_new[8] !="") {
                            var setDateTime = item_new[8].split(' ');
                            var setdateFormat = setDateTime[0].split('-');
                            setdateFormat = setdateFormat[2]+"-"+setdateFormat[1]+"-"+setdateFormat[0];
                            }
                            else{
                            setdateFormat="";
                            }
                            var inv_detail = ({
                                "dt": setdateFormat,
                                "inum" : item_new[7],
                                "typ" : item_new[18],
                                "pos" : item_new[6],
                                "items" : [{
                                        "txval": item_new[11],
                                        "cgst": item_new[13],
                                        "sgst": item_new[15],
                                        "igst": item_new[17]
                                    }]
                                });
                            inv_list.push(inv_detail)
                        }
                    }
                    pr_json.b2b.push({
                        "inv" : inv_list,
                        "ctin" : item[4],
                        "trdnm" : item[5]
                    });
                }

            }
            console.clear();
            var pr_json_string = JSON.stringify(pr_json);

            tolerance = $("#tolerance_match").val();
            approximation = $("#approximation_match option:selected").val();
            gst_username = $("#gstr_username").val();
            otp = $("#gstr3b_otp").val();
            $("#loading").removeClass("hide");
            date = new Date("01 "+$("#gstr3b_month").val());
            var lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
			from_date = moment(date).format(`YYYY-MM-DD`);
            to_date = moment(lastDay).format(`YYYY-MM-DD`);
            $.ajax({
                url : "/erp/accounts/json/gstr3b_reconciliation/",
                type : "POST",
                dataType: "json",
                async: false,
                data : {
                    from_date: from_date,
                    to_date: to_date,
                    tolerance: tolerance,
                    approximation: approximation,
                    pr_json: pr_json_string,
                    gst_username: gst_username,
                    otp: otp
                },
                success : function(data) {
                	if(data.error != undefined){
                	    $("#loading").addClass("hide").removeClass("show");
                	    if(data.error.error_cd == "XS101"){
							$("#gstr3b_login").modal('show');
		                    return false;
						}
                	    else if(data.error.error_cd == "AUTH4033"){
                	        swal("","Invalid OTP.","warning")
                	        $("#gstr3b_otp").val("");
		                    return false;
						}
						else if(data.error.error_cd == "XS115"){
							$("#gstr_username").val(data.error.gst_username);
							sendOtpRequest();
		                    return false;
						}
						else {
							swal("", data.error.message, "error");
							$("#gstr3b_login").modal('hide');
		                    return false;
						}
                	}
                	else {
                	    $("#gstr3b_login").modal('hide');
		                if(data.length == 0){
		                    swal("","OOPS! There is no record to display","info");
		                    $("#loading").addClass("hide").removeClass("show");
							noTableRecordsFound();
		                    return false;
		                }

	                    var csrf_token = '<input type="hidden" name="csrfmiddlewaretoken" value="'+ getCookie('csrftoken') +'">';
	                    var matchedBillsList = [];
	                    var matchableBillsList = [];
	                    var gstr2BOnlyList = [];
	                    var gstr2BOnlyPR = [];
						var matchedTotalInvValue = 0;
						var matchedTaxValue = 0;
	                    $.each(data.b2b.matched_bills, function (i, item) {
	                        if(item.igst == "" || item.igst == null){
								item.igst = 0.00
							}
							if(item.sgst == "" || item.sgst == null){
								item.sgst = 0.00
							}
							if(item.cgst == "" || item.cgst == null){
								item.cgst = 0.00
							}
							if(item.cess == "" || item.cess == null){
								item.cess = 0.00
							}
							if(item.txval == "" || item.txval == null){
								item.inv.val = 0.00
							}
							if(item.dt == "" || item.dt == null){
			                    var modDate = "";
			                }
			                else {
			                    var modDate =  moment(formatDate(item.dt)).format('MMM D, YYYY ');
			                }
			                matchedTotalInvValue += item.txval;

							matchedTaxValue += item.cgst + item.sgst + item.igst + item.cess;

		                    $("#txt-matched-totalval").val(matchedTotalInvValue.toFixed(2));
		                    $("#txt-matched-taxval").val(matchedTaxValue.toFixed(2));
			                matchedBillsList.push( [ i+1, item.inum, item.ctin, item.txval.toFixed(2), modDate, item.typ, item.cgst.toFixed(2), item.sgst.toFixed(2), item.igst.toFixed(2), item.cess.toFixed(2) ] );
		                });

		                if ($("#matched_bill tr").length == 1){
			                var row = "";
			                $('#matched_bill').append(row).addClass('tbl');
			            }

			            oTable = $('#matched_bill').DataTable( {
			                data: matchedBillsList,
			                deferRender: true,
			                fixedHeader: false,
			                "scrollY": Number($(document).height() - 230),
				            "scrollX": true,
				            "pageLength": 50,
			                "search": {
			                    "smart": false
			                },
			                "columns": [null,null, null,null,{ "type": "date" },null,null,null,null,null,null],
			            } );
			            oTable.on('page.dt', function() {
						  $('html, body').animate({
							scrollTop: $(".dataTables_wrapper").offset().top - 15
						   }, 'slow');
						});
						oSettings = oTable.settings();
						oSettings[0]._iDisplayLength = oSettings[0].fnRecordsTotal();
			            oTable.draw();
						oTable.destroy();
						$( window ).resize();

						var gstr2bonlyTotalInvValue = 0;
						var gstrTotalTax = 0;
			            $.each(data.b2b.only_in_2b, function (i, item) {
							if(item.igst == "" || item.igst == null){
								item.igst = 0.00
							}
							if(item.sgst == "" || item.sgst == null){
								item.sgst = 0.00
							}
							if(item.cgst == "" || item.cgst == null){
								item.cgst = 0.00
							}
							if(item.cess == "" || item.cess == null){
								item.cess = 0.00
							}
							if(item.txval == "" || item.txval == null){
								item.inv.val = 0.00
							}

							if(item.dt == "" || item.dt == null){
								var modDate = "";
							}

							else {
								var modDate =  moment(formatDate(item.dt)).format('MMM D, YYYY ');
							}

							gstr2BOnlyList.push( [ i+1, item.inum, item.ctin, item.txval.toFixed(2), modDate,item.typ, item.cgst.toFixed(2), item.sgst.toFixed(2), item.igst.toFixed(2), item.cess.toFixed(2) ] );

							gstr2bonlyTotalInvValue += item.txval;
							$(".gstr2b_total_value").text(gstr2bonlyTotalInvValue.toFixed(2));
							gstrTotalTax += item.cgst + item.sgst + item.igst + item.cess;
							$(".gstr2b_total_tax").text(gstrTotalTax.toFixed(2));

						});

						if ($("#gstr2bonly tr").length == 1){
			                var row = "";
			                $('#gstr2bonly').append(row).addClass('tbl');
			            }

			            oTable1 = $('#gstr2bonly').DataTable( {
			                data: gstr2BOnlyList,
			                deferRender: true,
			                fixedHeader: false,
			                "scrollY": Number($(document).height() - 230),
				            "scrollX": true,
				            "pageLength": 50,
			                "search": {
			                    "smart": false
			                },
			                "columns": [null,null, null,null,{ "type": "date" },null,null,null,null,null],
			            } );

			            oTable1.on('page.dt', function() {
						  $('html, body').animate({
							scrollTop: $(".dataTables_wrapper").offset().top - 15
						   }, 'slow');
						});
						oSettings1 = oTable1.settings();
						oSettings1[0]._iDisplayLength = oSettings1[0].fnRecordsTotal();
			            oTable1.draw();
						oTable1.destroy();
						$( window ).resize();
						var prTotalInvValue = 0;
						var prTotalTaxValue = 0;

			            $.each(data.b2b.only_in_pr, function (i, item) {

							if(item.igst == "" || item.igst == null){
								item.igst = 0.00
							}
							if(item.sgst == "" || item.sgst == null){
								item.sgst = 0.00
							}
							if(item.cgst == "" || item.cgst == null){
								item.cgst = 0.00
							}
							if(item.cess == "" || item.cess == null){
								item.cess = 0.00
							}
							if(item.txval == "" || item.txval == null){
								item.inv.val = 0.00
							}

							if(item.dt == "" || item.dt == null){
								var modDate = "";
							}

							else {
								var modDate =  moment(formatDate(item.dt)).format('MMM D, YYYY ');
							}
							prTotalInvValue += item.txval;
							$(".pr_total_value").text(prTotalInvValue.toFixed(2))
							gstr2BOnlyPR.push( [ i+1, item.inum, item.ctin, item.txval.toFixed(2), modDate, item.typ, item.cgst.toFixed(2), item.sgst.toFixed(2), item.igst.toFixed(2), item.cess.toFixed(2)] );

							prTotalTaxValue += item.cgst + item.sgst + item.igst + item.cess
							$('.pr_total_tax').text(prTotalTaxValue.toFixed(2))

						});

						if ($("#pronly_tbl_tbody tr").length == 1){
			                var row = "";
			                $('#pronly_tbl_tbody').append(row).addClass('tbl');
			            }

			            oTable2 = $('#pronly').DataTable( {
			                data: gstr2BOnlyPR,
			                deferRender: true,
			                fixedHeader: false,
			                "scrollY": Number($(document).height() - 230),
				            "scrollX": true,
				            "pageLength": 50,
			                "search": {
			                    "smart": false
			                },
			                "columns": [null,null, null,null,{ "type": "date" },null,null,null,null,null],
			            } );
			            oTable2.on('page.dt', function() {
						  $('html, body').animate({
							scrollTop: $(".dataTables_wrapper").offset().top - 15
						   }, 'slow');
						});
						oSettings2 = oTable2.settings();
						oSettings2[0]._iDisplayLength = oSettings2[0].fnRecordsTotal();
			            oTable2.draw();
						oTable2.destroy();
						$( window ).resize();


						$.each(data.b2b.matchable_bills, function (i, item) {

							if(item.inv.val == "" || item.inv.val == null){
	                            item.inv.val = 0.00
	                        }

	                        if(item.inv.items[0].cgst =="" || item.inv.items[0].cgst == null){
	                            item.inv.items[0].cgst =0.00
	                        }

							if(item.inv.items[0].igst =="" || item.inv.items[0].igst == null){
								item.inv.items[0].igst =0.00
							}

							if(item.inv.items[0].sgst =="" || item.inv.items[0].sgst == null){
								item.inv.items[0].sgst =0.00
							}
							if(item.inv.items[0].cess =="" || item.inv.items[0].cess == null){
								item.inv.items[0].cess =0.00
							}
							var totalPartialInvoiceValue = 0;
							var totalProbableInvoiceValue = 0;
							var totalUnmatchedInvoiceValue = 0;
							var totalUnmatchedTax = 0;

			                row = ` <tr class='text-center matchable_default_data' data-uid='${item.inv.uid}'>
			                            <td data-style="reportDefault" class='text-center'>${i+1}</td>
			                            <td data-style="reportDefault" style='text-align:left;'>${item.inv.inum}</td>
					                    <td data-style="reportDefault" class='text-right'>${item.inv.ctin}</td>
					                    <td data-style="reportDefault" class='text-right'>${item.inv.txval.toFixed(2)}</td>
					                    <td data-style="reportDefault" class='text-center'>${moment(formatDate(item.inv.dt)).format('MMM D, YYYY ')}</td>
					                    <td data-style="reportDefault">${item.inv.typ}</td>
					                    <td data-style="reportDefault" class='text-right'>${item.inv.items[0].cgst.toFixed(2)}</td>
					                    <td data-style="reportDefault" class='text-right'>${item.inv.items[0].sgst.toFixed(2)}</td>
					                    <td data-style="reportDefault" class='text-right'>${item.inv.items[0].igst.toFixed(2)}</td>
					                    <td data-style="reportDefault" class='text-right'>${item.inv.items[0].cess.toFixed(2)}</td>
					                    <td data-style="reportDefault"></td>
				                    </tr>`;

				                    if(item.data.partial_match.length >= 1) {
					                    row += ` <tr class="matchable_partial" data-uid='${item.data.partial_match[0].uid}'>
							                        <td data-style="reportPartial"></td>
													<td data-style="reportPartial" data-value='${item.data.partial_match[0].inum}'>
														<span class="checkbox" style="margin: 0px;">
															<input type="checkbox"  class="chk_moveto_matched" id="for-partial_${i}">
								                            <label for="for-partial_${i}" style='float: left;left: -15px;'></label>
								                            <span class="matchable_inv">${item.data.partial_match[0].inum}</span>
							                            </span>
							                        </td>
													<td data-style="reportPartial">${item.data.partial_match[0].ctin}</td>
													<td data-style="reportPartial" class="td-inv-value">${item.data.partial_match[0].txval.toFixed(2)}</td>
													<td data-style="reportPartial">${moment(formatDate(item.data.partial_match[0].dt)).format('MMM D, YYYY ')}</td>
													<td data-style="reportPartial">${item.data.partial_match[0].typ}</td>
													<td data-style="reportPartial" class="td-inv-tax">${item.data.partial_match[0].items[0].hasOwnProperty('cgst')?item.data.partial_match[0].items[0].cgst:'-'}</td>
													<td data-style="reportPartial" class="td-inv-tax">${item.data.partial_match[0].items[0].hasOwnProperty('sgst')?item.data.partial_match[0].items[0].sgst:'-'}</td>
													<td data-style="reportPartial" class="td-inv-tax">${item.data.partial_match[0].items[0].hasOwnProperty('igst')?item.data.partial_match[0].items[0].igst:'-'}</td>
													<td data-style="reportPartial" class="td-inv-tax">${item.data.partial_match[0].items[0].hasOwnProperty('cess') && item.data.partial_match[0].items[0].hasOwnProperty('cess') == '0'?item.data.partial_match[0].items[0].cess:'-'}</td>
													<td data-style="reportPartial" class="matchable_partial_text matchable_bill_status">Partial</td>
					                    </tr>`;
					                    totalPartialInvoiceValue += item.data.partial_match[0].val;
										$(".partial_total_inv_value").text(totalPartialInvoiceValue.toFixed(2))
				                    }
				                    if(item.data.probable_match.length >= 1) {
					                    row += `<tr class="matchable_probable" data-uid='${item.data.probable_match[0].uid}'>
							                        <td data-style="reportProbable" data-style="reportProbable"></td>
													<td data-style="reportProbable" data-value='${item.data.probable_match[0].inum}'>
														<span class="checkbox" style="margin: 0px;">
															<input type="checkbox" class="chk_moveto_matched" id="for-matchable_bills_${i}">
							                                <label for="for-matchable_bills_${i}" style='float: left;left: -15px;'></label>
							                                <span class="matchable_inv">${item.data.probable_match[0].inum}</span>
							                            </span>
						                            </td>
													<td data-style="reportProbable">${item.data.probable_match[0].ctin}</td>
													<td data-style="reportProbable" class="td-inv-value">${item.data.probable_match[0].txval.toFixed(2)}</td>
													<td data-style="reportProbable">${moment(formatDate(item.data.probable_match[0].dt)).format('MMM D, YYYY ')}</td>
													<td data-style="reportProbable">${item.data.probable_match[0].typ}</td>
													<td data-style="reportProbable" class="td-inv-tax">${item.data.probable_match[0].items[0].hasOwnProperty('cgst')?item.data.probable_match[0].items[0].cgst:'-'}</td>
													<td data-style="reportProbable" class="td-inv-tax">${item.data.probable_match[0].items[0].hasOwnProperty('sgst')?item.data.probable_match[0].items[0].sgst:'-'}</td>
													<td data-style="reportProbable" class="td-inv-tax">${item.data.probable_match[0].items[0].hasOwnProperty('igst')?item.data.probable_match[0].items[0].igst:'-'}</td>
													<td data-style="reportProbable" class="td-inv-tax">${item.data.probable_match[0].items[0].hasOwnProperty('cess') && item.data.probable_match[0].items[0].hasOwnProperty('cess') == '0'?item.data.probable_match[0].items[0].cess:'-'}</td>
													<td data-style="reportProbable" class="matchable_probable_text matchable_bill_status">Probable</td>
				                        </tr>`;
				                        totalProbableInvoiceValue += item.data.probable_match[0].val;
										$(".probable_total_inv_value").text(totalProbableInvoiceValue.toFixed(2))
				                    }
				                    if(item.data.unmatched.length >= 1) {
					                    row += `<tr class="matchable_unmatched" data-uid='${item.data.unmatched[0].uid}'>
							                        <td data-style="reportUnmatched"></td>
													<td data-style="reportUnmatched" data-value='${item.data.unmatched[0].inum}'>
														<span class="checkbox" style="margin: 0px;">
															<input type="checkbox" class="chk_moveto_matched" id="for-matched_bills_${i+1}">
							                                <label for="for-matched_bills_${i+1}" style='float: left;left: -15px;'></label>
							                                <span class="matchable_inv">${item.data.unmatched[0].inum}</span>
							                            </span>
						                            </td>
													<td data-style="reportUnmatched">${item.data.unmatched[0].ctin}</td>
													<td data-style="reportUnmatched" class="td-inv-value">${item.data.unmatched[0].txval.toFixed(2)}</td>
													<td data-style="reportUnmatched">${moment(formatDate(item.data.unmatched[0].dt)).format('MMM D, YYYY ')}</td>
													<td data-style="reportUnmatched">${item.data.unmatched[0].typ}</td>
													<td data-style="reportUnmatched" class="td-inv-tax">${item.data.unmatched[0].items[0].hasOwnProperty('cgst')?item.data.unmatched[0].items[0].cgst:'-'}</td>
													<td data-style="reportUnmatched" class="td-inv-tax">${item.data.unmatched[0].items[0].hasOwnProperty('sgst')?item.data.unmatched[0].items[0].sgst:'-'}</td>
													<td data-style="reportUnmatched" class="td-inv-tax">${item.data.unmatched[0].items[0].hasOwnProperty('igst')?item.data.unmatched[0].items[0].igst:'-'}</td>
													<td data-style="reportUnmatched" class="td-inv-tax">${item.data.unmatched[0].items[0].hasOwnProperty('cess') && item.data.unmatched[0].items[0].hasOwnProperty('cess') == '0'?item.data.unmatched[0].items[0].cess:'-'}</td>
													<td data-style="reportUnmatched" class="matchable_unmatched_text matchable_bill_status">Unmatched</td>
								       </tr>`;
								       /*totalUnmatchedInvoiceValue += item.data.unmatched[0].val;
									   $(".unmatched_total_inv_value").text(totalUnmatchedInvoiceValue.toFixed(2))

									   totalUnmatchedTax += ${item.data.unmatched[0].items[0].hasOwnProperty('cgst')?item.data.unmatched[0].items[0].cgst:0.00;
									   totalUnmatchedTax += ${item.data.unmatched[0].items[0].hasOwnProperty('sgst')?item.data.unmatched[0].items[0].sgst:0.00;
									   totalUnmatchedTax += ${item.data.unmatched[0].items[0].hasOwnProperty('igst')?item.data.unmatched[0].items[0].igst:0.00;
									   totalUnmatchedTax += ${item.data.unmatched[0].items[0].hasOwnProperty('cess')?item.data.unmatched[0].items[0].cess:0.00;
				                       $(".unmatched_tax_value").text(totalUnmatchedTax.toFixed(2))*/
				                    }
			                $('#matchable_bill_tbl_tbody').append(row);
		                });
		                matchableTableToMatchedTable();
		                $("#loading").addClass('hide').removeClass('show');
		            }
                },
                error : function(xhr,errmsg,err) {
                    console.log(xhr.status + ": " + xhr.responseText);
                    $("#loading").addClass('hide').removeClass('show');
                }
            });
        }

        function matchableTableToMatchedTable(){
	        $("#matchable_bill_tbl_tbody .chk_moveto_matched").change(function(){
	            if($(this).is(":checked")){
	           		var movedItemCode = $(this).closest("tr").find(".matchable_inv").text();
	           		var movedItemCodeStatus = $(this).closest("tr").find(".matchable_bill_status").text();
					var matchableBg = $(this).closest('tr').attr('class');
                    var movetoMatchableTable = $(this).closest('tr').html();
					var currentId = $(this).closest("tr").attr("data-uid");
                    $('#matched_bill_tbl_tbody').prepend(`<tr class="newly_matched_record ${matchableBg} " data-uid='${currentId}'>${movetoMatchableTable}</tr>`);
                    $('#matched_bill_tbl_tbody').find(".chk_moveto_matched").prop("checked",true);
				    $("#matchable_bill").find("tr[data-uid='"+currentId+"']").addClass("hide");
				    swal("",`<b>${movedItemCode} (${movedItemCodeStatus})</b> has been moved to Matched Bills`,`info`)
                }
				$('#matched_bill tbody tr').find('.chk_moveto_matched').change(function(){
					var movedItemCode = $(this).closest("tr").find("td:nth-child(2)").text();
					var currentRemoveId = $(this).closest("tr").attr("data-uid");
					$(this).closest(".newly_matched_record").remove();
					$("#matchable_bill_tbl_tbody").find(".chk_moveto_matched").prop("checked",false);
					$("#matchable_bill_tbl_tbody").find("tr[data-uid='"+currentRemoveId+"']").removeClass("hide");
					swal("",`<b>${movedItemCode}</b> has been moved to Matchable Bills`,`info`)
				});
			});
		}

        function grnReportView() {
            $("#loading").addClass('show').removeClass('hide');
            date = new Date("01 "+$("#gstr3b_month").val());
            var lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
			from_date = moment(date).format(`YYYY-MM-DD`);
            to_date = moment(lastDay).format(`YYYY-MM-DD`);
            $.ajax({
                url : "/erp/accounts/json/gstr1purchasereport/",
                type : "POST",
                dataType: "json",
                data : {
                    from_date: from_date,
                    to_date: to_date,
                    type: "gstr2",
                },
                success : function(data) {
                    loopTblData(data)
                },error : function(xhr,errmsg,err) {
                    console.log(xhr.status + ": " + xhr.responseText);
                    $("#loading").addClass('hide').removeClass('show');
                }
            });
            $("#matchable_bill tbody tr").remove();
		}

		function saveGstr3b() {
			var matchedDataJson = constructMatchedJsonData();
            if(matchedDataJson.data.length > 0) {
                var matchedDataJsonString = JSON.stringify(matchedDataJson);
                $.ajax({
                    url : "/erp/accounts/json/gstr3_save_matched/",
                    type : "POST",
                    dataType: "json",
                    data : {
                        data: matchedDataJsonString,
                    },
                    success : function(data) {
                        if(data['response_code']== 200){
                            swal({
                                title: "",
                                text: "Report saved successfully",
                                type: "success",
                                allowOutsideClick: false,
                                allowEscapeKey: false,
                            },
                            function(){
                                grnReportView();
                            });
                        }else{
                            swal({title: "", text: "Report not saved. Contact Administrator", type: "error"});
                        }
                    },error : function(xhr,errmsg,err) {
                        console.log(xhr.status + ": " + xhr.responseText);
                        $("#loading").addClass('hide').removeClass('show');
                    }
                });
            }
            else {
                swal({title: "", text: "There is no new matched bill(s) to Save.", type: "warning"});
            }

		}

		function constructMatchedJsonData() {
		    var data = [];
			$("#matched_bill_tbl_tbody").find(".newly_matched_record").each(function(){
				var newMatchedData = ({
			            "ctin" : $(this).find("td:nth-child(3)").text(),
	                    "inv" : [{
	                            "inum" : $(this).find("td:nth-child(2)").find(".matchable_inv").text(),
	                            "cat" : $(this).find("td:nth-child(10)").text(),
	                    }]
				})
                data.push(newMatchedData)
			})
			var newMatchedJson = {
				 "name":$(".select_month").val(),
                "tag":$(".select_month").val().replace(", ", "_").toLowerCase(),
				"filters":{
			      "date": $(".select_month").val(),
			      "tolerance": $("#tolerance_match").val(),
			      "approximation": $("#approximation_match option:selected").text(),
			    },
			    "data": data
			};
			return newMatchedJson;
		}

		function clearGstr3b(){
		    swal({
                title: "Are you sure!",
                text: "All Partial, Probable & Umatched PR-GSTR2 pairs marked as Matched for " +$(".select_month").val()+" will be reset!",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#209be1",
                confirmButtonText: "Yes, do it!",
                closeOnConfirm: true,
                closeOnCancel: true
            },
            function(){
            setTimeout(function(){
                    $.ajax({
                        url : "/erp/accounts/json/gstr3_clear_matched/",
                        type : "POST",
                        dataType: "json",
                        data : {},
                        success : function(data) {
                            if(data['response_code']== 200){
                                 swal({
                                    title: "",
                                    text: "Report cleared successfully",
                                    type: "success",
                                    allowOutsideClick: false,
                                    allowEscapeKey: false,
                                },
                                function(){
                                    grnReportView();
                                });
                            }else if(data['response_code'] == 109){
                                swal({title: "", text: "No data has been saved.", type: "warning"});
                            }
                            else{
                                swal({title: "", text: "Report not removed. Contact Administrator", type: "error"});
                            }
                        },error : function(xhr,errmsg,err) {
                            console.log(xhr.status + ": " + xhr.responseText);
                            $("#loading").addClass('hide').removeClass('show');
                        }
                    });
                },500);
            });
		}

		function reportGstr3b() {
			$("#report_gstr3b").modal('show');
			$(".total_exactmatch, .total_purchase_exactmatch").text($("#matched_bill_tbl_tbody tr").length);
            $(".total_partialmatch, .total_purchase_partialmatch").text($("#matchable_bill_tbl_tbody").find(".matchable_partial:not('.hide')").length);
            $(".total_probablematch, .total_purchase_probablematch").text($("#matchable_bill_tbl_tbody").find(".matchable_probable:not('.hide')").length);
            $(".total_unmatched, .total_purchase_unmatched").text($("#matchable_bill_tbl_tbody").find(".matchable_unmatched:not('.hide')").length);
            $(".total_gstr2b").text($("#gstr2bonly_tbl_tbody tr").length);
            $(".total_pr").text($("#pronly_tbl_tbody tr").length);

			var partialInvValue = 0, partialInvTax = 0;
			$("#matchable_bill").find("tr.matchable_partial:not('.hide')").each(function(){
				partialInvValue += Number($(this).find(".td-inv-value").text());
				$(this).find(".td-inv-tax").each(function(){
					if(!isNaN($(this).text())) {
						partialInvTax += Number($(this).text());
					}
				})
			});
			$(".partial_total_inv_value").text(partialInvValue.toFixed(2));
			$(".partial_total_inv_tax").text(partialInvTax.toFixed(2));

			var portableInvValue = 0 , portableInvTax = 0;
			$("#matchable_bill").find("tr.matchable_probable:not('.hide')").each(function(){
				portableInvValue += Number($(this).find(".td-inv-value").text());
				$(this).find(".td-inv-tax").each(function(){
					if(!isNaN($(this).text())) {
						portableInvTax += Number($(this).text());
					}
				});
			});
			$(".probable_total_inv_value").text(portableInvValue.toFixed(2))
			$(".probable_total_inv_tax").text(portableInvTax.toFixed(2))

			var unmatchedInvValue = 0, unmatchedInvTax = 0;
			$("#matchable_bill").find("tr.matchable_unmatched:not('.hide')").each(function(){
				unmatchedInvValue += Number($(this).find(".td-inv-value").text());
				$(this).find(".td-inv-tax").each(function(){
					if(!isNaN($(this).text())) {
						unmatchedInvTax += Number($(this).text());
					}
				});
			});
			$(".unmatched_total_inv_value").text(unmatchedInvValue.toFixed(2));
			$(".unmatched_total_inv_tax").text(unmatchedInvTax.toFixed(2));

			var matchedTotalValue = 0, matchedTotalTax = 0;
			$("#matched_bill").find(".newly_matched_record").each(function(){
				matchedTotalValue += Number($(this).find(".td-inv-value").text());
				$(this).find(".td-inv-tax").each(function(){
					if(!isNaN($(this).text())) {
						matchedTotalTax += Number($(this).text());
					}
				});
			});

			$(".matched-totalval").text(Number($("#txt-matched-totalval").val()) + Number(matchedTotalValue));
			$(".matched-taxval").text(Number($("#txt-matched-taxval").val()) + Number(matchedTotalTax));
			$('.total-miscmatchtax').text((Number($(".gstr2b_total_tax").text()) +  Number($(".pr_total_tax").text()) + Number($(".unmatched_total_inv_tax").text())).toFixed(2));
			$('.total-mismatchinv').text((Number($(".unmatched_total_inv_value").text()) + Number($(".gstr2b_total_value").text()) + Number($(".pr_total_value").text())).toFixed(2));
			$('.total-mismatchedpurchase').text(Number($(".total_purchase_unmatched").text()) + Number($(".total_pr").text()));
			$('.total_mismatched_gstr2b').text(Number($(".total_gstr2b").text()) + Number($(".total_unmatched").text()));
			$('.overall_total_gstr2b').text(Number($('.total_mismatched_gstr2b').text()) + Number($(".total_probablematch").text()) + Number($('.total_partialmatch').text()) + + Number($('.total_exactmatch').text()));
			$('.total_purchase_register').text(Number($('.total-mismatchedpurchase').text()) + Number($(".total_purchase_probablematch").text()) + Number($('.total_purchase_partialmatch').text()) + + Number($('.total_purchase_exactmatch').text()));
			$('.total_invoice_value').text((Number($('.total-mismatchinv').text()) + Number($(".probable_total_inv_value").text()) + Number($('.partial_total_inv_value').text()) + + Number($('.matched-totalval').text())).toFixed(2));
			$('.total_taxable_value').text((Number($('.total-miscmatchtax').text()) + Number($(".probable_total_inv_tax").text()) + Number($('.partial_total_inv_tax').text()) + + Number($('.matched-taxval').text())).toFixed(2));

		}

		$('#gstr3b_cancel').click(function(){
			noTableRecordsFound();
		});

		function noTableRecordsFound(){
			var row = `<tr>
							<td colspan="11" class="text-center font-bold">No Records Found!</td>
					   </tr>`;
					   $('.gstr-report-body').append(row).addClass('tbl');
		}

		function toggleAPISteps() {
			$(".gst-api-steps").slideToggle();
		}

		function tablesToExcelLoad() {
			setTimeout(function(){
				tablesToExcel(['matched_bill','matchable_bill','gstr2bonly','pronly'], ['Matched_Bill','Matchable_Bill','In_GSTR2B_Only','In_PR_Only'], 'GSTR3B_Report.xls', 'Excel');
			},100);
		}

	var tablesToExcel = (function() {
    var uri = 'data:application/vnd.ms-excel;base64,'
    , tmplWorkbookXML = '<?xml version="1.0" encoding="windows-1252"?><?mso-application progid="Excel.Sheet"?>'
	  + '	<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel"  xmlns:html="http://www.w3.org/TR/REC-html40">'
    + '		<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">'
	  + '			<Author>Qompare</Author>'
	  + '			<Created>{created}</Created>'
	  + '		</DocumentProperties>'
    + '		<Styles>'
	  + '			<Style ss:ID="Default" ss:Name="Normal">'
	  + '				<NumberFormat ss:Format=""/>'
	  + '			</Style>'
	  + '			<Style ss:ID="reportPartial">'
	  + '				<Alignment ss:WrapText="1" />'
    + '				<Borders>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Right"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Left"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Top"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Bottom"/>'
    + '				</Borders>'
	  + '				<Font ss:Color="#000000"/>'
	  + '				<Interior ss:Color="#FFFFCC" ss:Pattern="Solid" />'
	  + '				<NumberFormat/>'
	  + '				<Protection/>'
	  + '			</Style>'
	+ '			<Style ss:ID="reportProbable">'
	  + '				<Alignment ss:WrapText="1" />'
    + '				<Borders>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Right"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Left"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Top"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Bottom"/>'
    + '				</Borders>'
	  + '				<Font ss:Color="#000000"/>'
	  + '				<Interior ss:Color="#FFE5CC" ss:Pattern="Solid" />'
	  + '				<NumberFormat/>'
	  + '				<Protection/>'
	  + '			</Style>'
	  + '			<Style ss:ID="reportUnmatched">'
	  + '				<Alignment ss:WrapText="1" />'
    + '				<Borders>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Right"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Left"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Top"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Bottom"/>'
    + '				</Borders>'
	  + '				<Font ss:Color="#000000"/>'
	  + '				<Interior ss:Color="#ebe0f7" ss:Pattern="Solid" />'
	  + '				<NumberFormat/>'
	  + '				<Protection/>'
	  + '			</Style>'
	  + '			<Style ss:ID="reportDefault">'
	  + '				<Alignment ss:WrapText="1" />'
    + '				<Borders>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Right"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Left"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Top"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Bottom"/>'
    + '				</Borders>'
	  + '				<Font ss:Color="#000000"/>'
	  + '				<Interior ss:Color="#ffffff" ss:Pattern="Solid" />'
	  + '				<NumberFormat/>'
	  + '				<Protection/>'
	  + '			</Style>'
	  + '			<Style ss:ID="tableHeader">'
	  + '				<Alignment ss:WrapText="1" />'
    + '				<Borders>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Right"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Left"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Top"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Bottom"/>'
    + '				</Borders>'
	  + '				<Font ss:Bold="1" ss:Color="#000000"/>'
	  + '				<Interior ss:Color="#cccccc" ss:Pattern="Solid" />'
	  + '				<NumberFormat/>'
	  + '				<Protection/>'
	  + '			</Style>'
    + '			<Style ss:ID="Changed">'
	  + '				<Borders/>'
	  + '				<Font ss:Color="#ff0000"></Font>'
	  + '				<Interior ss:Color="#99CC00" ss:Pattern="Solid"></Interior>'
	  + '				<NumberFormat/>'
	  + '				<Protection/>'
	  + '			</Style>'
    + '			<Style ss:ID="Missed">'
	  + '				<Borders/>'
	  + '				<Font ss:Color="#ff0000"></Font>'
	  + '				<Interior ss:Color="#ff0000" ss:Pattern="Solid"></Interior>'
	  + '				<NumberFormat/>'
	  + '				<Protection/>'
	  + '			</Style>'
    + '			<Style ss:ID="Decimals">'
	  + '				<NumberFormat ss:Format="Fixed"/>'
	  + '			</Style>'
    + '	</Styles>'
    + '	{worksheets}'
	  + '</Workbook>'
    , tmplWorksheetXML = '<Worksheet ss:Name="{nameWS}">'
	  + '	<ss:Table ss:DefaultColumnWidth="60">'
	  + '		{rows}'
	  + '	</ss:Table>'
	  + '</Worksheet>'
    , tmplCellXML = '			<ss:Cell{attributeStyleID}{attributeFormula}>'
	  + '				<ss:Data ss:Type="{nameType}">{data}</ss:Data>'
	  + '			</ss:Cell>'
    , base64 = function(s) { return window.btoa(unescape(encodeURIComponent(s))) }
    , format = function(s, c) { return s.replace(/{(\w+)}/g, function(m, p) { return c[p]; }) }
	    return function(tables, wsnames, wbname, appname) {
      var ctx = "";
      var workbookXML = "";
      var worksheetsXML = "";
      var rowsXML = "";

      for (var i = 0; i < tables.length; i++) {
        if (!tables[i].nodeType) tables[i] = document.getElementById(tables[i]);
        for (var j = 0; j < tables[i].rows.length; j++) {
         if(tables[i].rows[j].getAttribute("class").indexOf("hide") == -1) {
        	if(tables[i].rows[j].classList.value.indexOf("exclude-export") == -1) {
        		if(tables[i].rows[j].classList.value.indexOf("total-header") != -1) {
	          		rowsXML += '<ss:Row ss:Height="50">'
	          	}
	          	else {
	          		rowsXML += '<ss:Row>'
	          	}
	          	for (var k = 0; k < tables[i].rows[j].cells.length; k++) {
	            	var dataType = tables[i].rows[j].cells[k].getAttribute("data-type");
	            	var dataStyle = tables[i].rows[j].cells[k].getAttribute("data-style");
	            	var dataValue = tables[i].rows[j].cells[k].getAttribute("data-value");
	            	dataValue = (dataValue)?dataValue:tables[i].rows[j].cells[k].innerHTML;

	            	var dataFormula = tables[i].rows[j].cells[k].getAttribute("data-formula");
	            	dataFormula = (dataFormula)?dataFormula:(appname=='Calc' && dataType=='DateTime')?dataValue:null;
	            	ctx = {  attributeStyleID: (dataStyle=='Changed' || dataStyle=='Missed'|| dataStyle=='Header'|| dataStyle=='reportPartial' || dataStyle=='reportUnmatched' || dataStyle=='reportProbable'|| dataStyle=='reportDefault'|| dataStyle=='tableHeader')?' ss:StyleID="'+dataStyle+'"':''
	                   , nameType: (dataType=='Number' || dataType=='DateTime' || dataType=='Boolean' || dataType=='Error')?dataType:'String'
	                   , data: (dataFormula)?'':dataValue
	                   , attributeFormula: (dataFormula)?' ss:Formula="'+dataFormula+'"':''
	                  };
	            	rowsXML += format(tmplCellXML, ctx);
	          	}
	          	rowsXML += '</ss:Row>'
          	}
          }
        }
        ctx = {rows: rowsXML, nameWS: wsnames[i] || 'Sheet' + i};
        worksheetsXML += format(tmplWorksheetXML, ctx);
        rowsXML = "";
      }

	      ctx = {created: (new Date()).getTime(), worksheets: worksheetsXML};
	      workbookXML = format(tmplWorkbookXML, ctx);

	      	if($('ul.nav-tabs li.active a').attr('id') == 'tab1_view') {
	      		$("#pagination-select").trigger('change');
	    	}

	      var link = document.createElement("A");
	      link.href = uri + base64(workbookXML);
	      link.download = wbname || 'Workbook.xls';
	      link.target = '_blank';
	      document.body.appendChild(link);
	      link.click();
	      document.body.removeChild(link);
	      $(".downloading-main-container").removeClass('show').addClass('hide');
	    }
	  })();

</script>
{% endblock %}