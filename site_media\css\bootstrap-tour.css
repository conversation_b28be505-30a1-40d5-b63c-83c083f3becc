/* ========================================================================
 * bootstrap-tour - v0.11.0
 * http://bootstraptour.com
 * ========================================================================
 * Copyright 2012-2015 Ulrich Sossou
 *
 * ========================================================================
 * Licensed under the MIT License (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://opensource.org/licenses/MIT
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * ========================================================================
 */

.tour-backdrop {
  position: absolute;
  z-index: 1100;
  background-color: #000;
  opacity: 0.8;
  filter: alpha(opacity=80);
}
.popover[class*="tour-"] {
  z-index: 1102;
  background: #4285f4;
  color: #fff;
}
.popover[class*="tour-"] .popover-navigation {
  padding: 9px 14px;
  overflow: hidden;
  float: right;
}
.popover[class*="tour-"] .popover-navigation *[data-role="end"] {
  float: right;
}
.popover[class*="tour-"] .popover-navigation *[data-role="prev"],
.popover[class*="tour-"] .popover-navigation *[data-role="next"],
.popover[class*="tour-"] .popover-navigation *[data-role="end"] {
  cursor: pointer;
  border: none;
  background: transparent;
  color:#fff;
  font-weight: bold;
}
.popover[class*="tour-"] .popover-navigation *[data-role="prev"].disabled,
.popover[class*="tour-"] .popover-navigation *[data-role="next"].disabled,
.popover[class*="tour-"] .popover-navigation *[data-role="end"].disabled {
  cursor: default;
  border: none;
  background: transparent;
}
.popover[class*="tour-"].orphan {
  position: fixed;
  margin-top: 0;
}
.popover[class*="tour-"].orphan .arrow {
  display: none;
}

.tour_button{
   float: right;
    margin-top: -43px;
    border: none;
    background: transparent;
    color: #fff;
    font-size: 14px;
    font-weight: bold;
}

.popover-title {
    padding: 8px 14px;
    margin: 0;
    font-size: 16px;
    border-radius: 5px 5px 0 0;
    padding-top: 20px;
    background-color: transparent;
    border: none;
    font-weight:bold;
}

.popover{
    max-width:400px;
}

.popover.left > .arrow:after {
  right: 1px;
  bottom: -10px;
  content: " ";
  border-right-width: 0;
  border-left-color: #4285f4;
  }

  .popover.bottom > .arrow:after {
    top: 1px;
    margin-left: -10px;
    content: " ";
    border-top-width: 0;
    border-bottom-color: #4285f4;
}

.popover.right > .arrow:after {
    bottom: -10px;
    left: 1px;
    content: " ";
    border-right-color:#4285f4;
    border-left-width: 0;
}