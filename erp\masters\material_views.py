import ast
import csv
import json
import re
from datetime import datetime

import pymysql
import simplejson
import xlrd
from django.http import HttpResponseRedirect, HttpResponse
from django.template.response import TemplateResponse
from sqlalchemy import func

from erp import properties, helper, DEFAULT_MAKE_ID
from erp.auth import ENTERPRISE_IN_SESSION_KEY, SESSION_KEY, ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.dao import executeQuery
from erp.forms import MaterialForm
from erp.formsets import CatalogueMaterialFormset, MaterialMakeFormset, MaterialPartyProfileFormset, \
	MaterialAlternateUnitFormset, SpecificationFormset
from erp.helper import populateMaterialCategoryChoices, validateFileHeaders, getUser, populateUnit, getStateList, constructDifferentMakeName
from erp.masters import logger
from erp.masters.backend import MasterService, MaterialVO, MATERIAL_FORM_PREFIX, CATALOGUE_MATERIAL_FORMSET_PREFIX, \
	SUPPLIER_PRICE_MATERIAL_FORMSET_PREFIX, MATERIAL_MAKE_FORMSET_PREFIX, MasterDAO, \
	MATERIAL_ALTERNATE_UNIT_FORMSET_PREFIX, SPECIFICATION_FORMSET_PREFIX, LocationService
from erp.models import Category, Material, Receipt, Currency, UnitMaster, Project
from erp.properties import MANAGE_MATERIAL_TEMPLATE, TEMPLATE_TITLE_KEY
from erp.stores.backend import StoresDAO, StoresService
from erp.tasks import update_closing_stock
from settings import HOST, USER, PASSWORD, DBNAME, PORT, SQLASession, GCS_BUCKET_NAME
from util.api_util import response_code
from util.helper import getFinancialYear, remove_non_ascii, convertStringToDate

__author__ = 'kalaivanan'

MATERIALS_KEY = 'materials'
MATERIAL_FORM_KEY = 'material_form'
BILL_OF_MATERIALS_FORMSET_KEY = 'bill_of_materials_formset'
PARTICIPATING_BOM_LIST_KEY = 'participating_bom_of_materials'
SPECIFICATION_FORMSET_KEY = 'specifications_formset'
PRICE_OF_SUPP_MATERIALS_FORMSET_KEY = 'materials_party_price_formset'
MATERIAL_MAKE_FORMSET_KEY = 'material_makes_formset'
UNIT_LIST_KEY = 'unit_list'
ALTERNATE_UNIT_FORMSET_KEY = 'alternate_unit_formset'

MATERIAL_FIELD_KEY = 'material_id'
ITEM_ID_FIELD_KEY = 'item_id'
CODE_FIELD_KEY = 'drawing_no'
DELETE_CODE_FIELD_KEY = 'delete_drawing_no'
MATERIAL_TYPE_KEY = 'material_type'


def manageService(request):
	return manageCatalogues(request, material_type="service")


def manageCatalogues(request, material_type='goods'):
	"""
	Renders the view to manage Catalogues. Aids in mapping catalogue & material associations.

	:param request:
	:param material_type:
	:return:
	"""
	master_service = MasterService()
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		material_vo = master_service.constructMaterialVO(enterprise_id=enterprise_id)
		query = """SELECT unit_id,unit_name FROM unit_master where enterprise_id=%s""" % enterprise_id
		unit_list = executeQuery(query)
		currency = SQLASession().query(Currency).all()
		last_saved_item_name = request_handler.getSessionAttribute("last_saved_item_name")
		request_handler.removeSessionAttribute("last_saved_item_name")
		return TemplateResponse(
			template=MANAGE_MATERIAL_TEMPLATE, request=request, context={
				"last_saved_item_name": last_saved_item_name,
				MATERIAL_FORM_KEY: material_vo.material_form,
				BILL_OF_MATERIALS_FORMSET_KEY: material_vo.material_formset, 'currency': currency,
				ALTERNATE_UNIT_FORMSET_KEY: material_vo.alternate_unit_formset,
				SPECIFICATION_FORMSET_KEY: material_vo.specification_formset,
				PRICE_OF_SUPP_MATERIALS_FORMSET_KEY: material_vo.supplier_price_material_formset,
				MATERIAL_MAKE_FORMSET_KEY: material_vo.material_make_formset, "make_list": [],
				MATERIAL_TYPE_KEY: material_type,
				TEMPLATE_TITLE_KEY: "Goods" if material_type == 'goods' else "Service", UNIT_LIST_KEY: list(unit_list)})
	except Exception as e:
		logger.exception("The Error Raised... %s" % e.message)
	return HttpResponse("#")


def editCatalogue(request):
	"""
	Renders the view to collect Catalogue information.

	:param request:
	:return:
	"""
	master_service = MasterService()
	store_service = StoresService()
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	item_id = request_handler.getPostData(ITEM_ID_FIELD_KEY)
	material = master_service.master_dao.getMaterialByItemId(item_id=item_id, enterprise_id=enterprise_id)
	material_type = material.is_service
	catalogue_vo = master_service.constructMaterialVO(item_id=item_id, enterprise_id=enterprise_id)
	participating_bom_material_list = master_service.master_dao.getPBOMMaterialList(
		item_id=item_id, enterprise_id=enterprise_id)
	gst_category = master_service.getGSTCategory()
	countries = master_service.getCountries()
	bom_materials = []
	gst_category_list = []
	gst_country_list = []
	for category in gst_category:
		gst_category_list.append({"category_id": category.id, "category_name": category.name, "category_desc": category.description})

	for country in countries:
		gst_country_list.append({"country_code": country.code, "country_name": country.name})
	for item in participating_bom_material_list:
		make_name = constructDifferentMakeName(item[4])
		material_name = item[2] + " [" + make_name + "]" if make_name else item[2]
		bom_materials.append(
			{"drawing_no": item[1] if item[1] else "", "qty": item[0], "name": material_name, "description": item[3],
				"make_name": item[4], "item_id": item[5], "is_stock": item[6], "is_service":item[7]})
	item = master_service.master_dao.getMaterialByItemId(item_id=item_id, enterprise_id=enterprise_id)
	stock_qty = 0.00
	faulty_qty = 0.00
	if item.is_stocked:
		stock_details = store_service.getClosingStock(mat_list=[int(item_id)], enterprise_id=enterprise_id, is_faulty=0)
		for item in stock_details:
			stock_qty = item['closing_qty']
		stock_details = store_service.getClosingStock(mat_list=[int(item_id)], enterprise_id=enterprise_id, is_faulty=1)
		for item in stock_details:
			faulty_qty = item['closing_qty']

		logger.info("Stock details:%s" % [stock_qty, faulty_qty])
		# stock_qty = StoresDAO().getClosingStock(enterprise_id=enterprise_id, item_id=item_id, is_faulty=0, till=datetime.now())
		# faulty_qty = StoresDAO().getClosingStock(enterprise_id=enterprise_id, item_id=item_id, is_faulty=1, till=datetime.now())

	currency = SQLASession().query(Currency).all()
	return TemplateResponse(template=MANAGE_MATERIAL_TEMPLATE, request=request, context={
		MATERIAL_FORM_KEY: catalogue_vo.material_form, BILL_OF_MATERIALS_FORMSET_KEY: catalogue_vo.material_formset,
		PARTICIPATING_BOM_LIST_KEY: bom_materials, SPECIFICATION_FORMSET_KEY: catalogue_vo.specification_formset,
		PRICE_OF_SUPP_MATERIALS_FORMSET_KEY: catalogue_vo.supplier_price_material_formset,
		MATERIAL_MAKE_FORMSET_KEY: catalogue_vo.material_make_formset,
		ALTERNATE_UNIT_FORMSET_KEY: catalogue_vo.alternate_unit_formset,
		"make_list": [[1,'__NA__', 0]],
		"GCS_BUCKET_NAME": GCS_BUCKET_NAME,
		'gst_category_list': gst_category_list, 'gst_country_list': gst_country_list, 'state_list': getStateList(),
		MATERIAL_TYPE_KEY: 'service' if material_type else 'goods',
		TEMPLATE_TITLE_KEY: catalogue_vo.material_form.initial['name'], 'currency': currency,
		"stock_qty": round(stock_qty, 3) if stock_qty else 0, "faulty_qty": round(faulty_qty, 2) if faulty_qty else 0})


def checkStockQty(request):
	"""

	:param request:
	:return:
	"""

	request_handler = RequestHandler(request)
	store_service = StoresService()
	item_id = request_handler.getPostData(ITEM_ID_FIELD_KEY)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	# stock_qty = StoresDAO().getClosingStock(enterprise_id=enterprise_id, item_id=item_id)
	stock_qty = 0
	stock_details = store_service.getClosingStock(mat_list=[int(item_id)], enterprise_id=enterprise_id, is_faulty=0)
	for item in stock_details:
		stock_qty = item['closing_qty']

	p_bom_material = MasterService().master_dao.getPBOMMaterialList(item_id=item_id, enterprise_id=enterprise_id)
	p_bom_count = len(p_bom_material) if p_bom_material else 0
	material_details = {"stock_qty": stock_qty, "p_bom_count": p_bom_count}

	return HttpResponse(content=simplejson.dumps(material_details), mimetype='application/json')


def saveCatalogue(request):
	"""
	Persists the Catalogue information collected in the Edit view to the Database - It includes dissociating
	Catalogue-Material associations by selectively deleting the CatalogueMaterials.

	:param request:
	:return:
	"""
	master_service = MasterService()
	request_handler = RequestHandler(request)
	logger.debug('POST Information: %s' % request_handler.getPostData())
	enterprise = request_handler.getSessionAttribute(ENTERPRISE_IN_SESSION_KEY)
	enterprise_id = enterprise.id

	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	catalogue_vo_to_be_saved = MaterialVO(
		material_form=MaterialForm(
			data=request_handler.getPostData(), prefix=MATERIAL_FORM_PREFIX, enterprise_id=enterprise_id,
			is_negative_stock_allowed=enterprise.is_negative_stock_allowed),
		material_formset=CatalogueMaterialFormset(
			data=request_handler.getPostData(), prefix=CATALOGUE_MATERIAL_FORMSET_PREFIX),
		specification_formset=SpecificationFormset(
			data=request_handler.getPostData(), prefix=SPECIFICATION_FORMSET_PREFIX),
		supplier_price_material_formset=MaterialPartyProfileFormset(
			data=request_handler.getPostData(), prefix=SUPPLIER_PRICE_MATERIAL_FORMSET_PREFIX),
		material_make_formset=MaterialMakeFormset(
			data=request_handler.getPostData(), prefix=MATERIAL_MAKE_FORMSET_PREFIX),
		alternate_unit_formset=MaterialAlternateUnitFormset(
			data=request_handler.getPostData(), prefix=MATERIAL_ALTERNATE_UNIT_FORMSET_PREFIX))
	is_valid = catalogue_vo_to_be_saved.is_valid()
	user = getUser(enterprise_id=enterprise_id, user_id=user_id)
	material_type = catalogue_vo_to_be_saved.material_form.cleaned_data['is_service']
	master_service.saveCatalogue(catalogue_vo_to_be_saved, user=user, material_type=material_type)
	# TODO catalogue_vo_to_be_saved.is_valid() is False after calling master_service.saveCatalogue for new materials
	if request.GET.get('add_material_ajax'):
		drawing_no = catalogue_vo_to_be_saved.material_form.cleaned_data['drawing_no']
		material_name = catalogue_vo_to_be_saved.material_form.cleaned_data['name']
		enterprise_id = catalogue_vo_to_be_saved.material_form.cleaned_data['enterprise_id']
		is_stockable = catalogue_vo_to_be_saved.material_form.cleaned_data['is_stocked']
		is_service = catalogue_vo_to_be_saved.material_form.cleaned_data['is_service']
		material_detail = MasterDAO().getMaterialByMaterialName(material_name, enterprise_id)
		material_data_dump = {
			"drawing_no": drawing_no, "name": material_name, "make_id": DEFAULT_MAKE_ID
			, "enterprise_id": enterprise_id, "unit_id": catalogue_vo_to_be_saved.material_form.cleaned_data['unit_id']
			, "unit_name": material_detail.unit.unit_name, "item_id": material_detail.material_id
			, "alternate_unit_count": len(material_detail.alternate_units), "hsn_code": material_detail.tariff_no
			, "price": material_detail.price, "is_stockable": 1 if is_stockable else 0, "is_service": 1 if is_service else 0}
		return HttpResponse(content=simplejson.dumps(material_data_dump), mimetype='application/json')
	request_handler.setSessionAttribute("last_saved_item_name", catalogue_vo_to_be_saved.material_form.cleaned_data[
			'name'] if is_valid else "")
	return HttpResponseRedirect(
		properties.MANAGE_SERVICE_MATERIAL_SEARCH_URL if material_type else properties.MANAGE_MATERIAL_SEARCH_URL)


def deleteCatalogue(request):
	"""
	Deletes the chosen Catalogue.

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	MasterService().master_dao.deleteMaterial(
		drawing_no=request_handler.getPostData(DELETE_CODE_FIELD_KEY),
		enterprise_id=request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY))
	return HttpResponseRedirect(properties.MANAGE_MATERIAL_SEARCH_URL)


def deleteMaterial(request):
	"""
	Deletes the Material chosen in the Manage Materials page

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	logger.info('Item id of Material to be deleted: %s' % request_handler.getPostData('delete_item_id'))
	MasterService().deleteMaterial(
		request_handler.getPostData('delete_item_id'),
		request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY))
	return HttpResponseRedirect(properties.MANAGE_MATERIAL_SEARCH_URL)


def importMaterials(request):
	"""
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	enterprise = request_handler.getSessionAttribute(ENTERPRISE_IN_SESSION_KEY)
	conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
	m_type = request_handler.getPostData('m_type')
	cur = conn.cursor()
	grn_materials = []
	dict = {}
	try:
		modifying_user = getUser(enterprise_id=enterprise.id, user_id=user_id)
		mat_list = request.POST.getlist('mat_list[]')
		if request.FILES:
			bulk_file = request.FILES['bulkfile']
			bulk_file.seek(0)
			book = xlrd.open_workbook(file_contents=bulk_file.read())
			material_sheet = book.sheet_by_name('materials')
			logger.debug('Sheet name: %s' % material_sheet.name)
			mat_list = []
			for row_idx in range(0, material_sheet.nrows):  # Iterate through rows
				logger.debug('-' * 40)
				if row_idx == 0:
					item_dict = {}
				else:
					item_dict = {
						'drawing_no': str(int(material_sheet.cell(row_idx, 0).value)) if isinstance(
							material_sheet.cell(row_idx, 0).value, float) else str(material_sheet.cell(row_idx, 0).value),
						'name': str(material_sheet.cell(row_idx, 1).value),
						'category': str(material_sheet.cell(row_idx, 2).value),
						'description': str(material_sheet.cell(row_idx, 3).value),
						'hsn_sac': str(material_sheet.cell(row_idx, 4).value),
						'price': str(material_sheet.cell(row_idx, 5).value),
						'in_use': int(material_sheet.cell(row_idx, 6).value),
						'unit': int(material_sheet.cell(row_idx, 7).value),
						'faultless': str(material_sheet.cell(row_idx, 8).value) if isinstance(
							material_sheet.cell(row_idx, 8).value) else 0,
						'faulty': str(material_sheet.cell(row_idx, 9).value) if isinstance(
							material_sheet.cell(row_idx, 9).value) else 0}
				mat_list.append(item_dict)
			mat_list.append({})
			do_update = True
		else:
			if m_type == 'goods':
				is_valid_file_headers = validateFileHeaders(
					module='masters_goods', title_row={k: re.sub('\W+', '', v) for k, v in ast.literal_eval(mat_list[0]).items()})
			elif m_type == 'service':
				is_valid_file_headers = validateFileHeaders(
					module='masters_service', title_row={k: re.sub('\W+', '', v) for k, v in ast.literal_eval(mat_list[0]).items()})
			if is_valid_file_headers is False:
				return HttpResponse(
					content=simplejson.dumps({
						'message': 'Please upload the valid  file or check proper column names',
						'is_valid_file_headers': False}),
					mimetype='application/json')
			do_update = 'is_user_update_accept' in request.POST
		n = len(mat_list) - 1
		db_session = SQLASession()
		materials_dict = []
		if not do_update:
			drawing_numbers = []
			material_names = []
			for material_item in mat_list[1:n]:
				material_dict = ast.literal_eval(material_item)
				material_dict['drawing_no'] = re.sub(
					"""[^A-z0-9. '\"@$#%:?!*&,()+/._-]""", '', material_dict['drawing_no'].strip()).upper()
				if material_dict['drawing_no']:
					drawing_numbers.append(material_dict['drawing_no'])
				if material_dict['name']:
					material_names.append(material_dict['name'])

				materials_dict.append(material_dict)
			materials = db_session.query(Material.drawing_no).filter(
				Material.drawing_no.in_(drawing_numbers), Material.enterprise_id == enterprise.id).all()
			material_name_duplicate = db_session.query(Material.name).filter(
				Material.name.in_(material_names), Material.enterprise_id == enterprise.id).all()
			duplicate_items = []
			if materials.__len__() > 0:
				for material in materials:
					duplicate_items.append(material.drawing_no)
			if material_name_duplicate.__len__() > 0:
				for material in material_name_duplicate:
					duplicate_items.append(material.name)
			if len(duplicate_items) > 0:
				logger.warn("Import materials failed! %s duplicates are captured" % len(duplicate_items))
				logger.debug("Duplicates materials are %s" % duplicate_items)
				message = "%s Item codes already profiled:\n%s%s" % (
					len(duplicate_items), duplicate_items[:10], "..." if len(duplicate_items) > 10 else "")
				logger.info(message)
				json = {"message": str(message), 'is_valid_file_headers': True}
				return HttpResponse(content=simplejson.dumps(json), mimetype='application/json')
		if request.FILES:
			do_import = True
		else:
			do_import = 'is_user_accept' in request.POST
		failed_items = []
		failed_item_list = []
		added_count = 0
		updated_count = 0
		non_existing_categories = []
		is_new_categories = False
		unit_id_list = []
		cur.execute("SELECT * FROM unit_master WHERE enterprise_id=%s", enterprise.id)
		unit_master_obj = cur.fetchall()
		for row in unit_master_obj:
			unit_id_list.append(row[0])

		for material_item in mat_list[1:n]:
			if request.FILES:
				material_dict = material_item
			else:
				material_dict = ast.literal_eval(material_item)
			cur.execute("SELECT * FROM material_category WHERE name =%s AND enterprise_id=%s", (
				material_dict['category'], enterprise.id,))
			results = cur.fetchall()
			if not results:
				if material_dict['category'] != "":
					logger.info("Category doesn't exists need to create new...")
					non_existing_categories.append(material_dict['category'])
					is_new_categories = True

		if not do_import:
			if is_new_categories:
				message = "%s New categories found!" % len(non_existing_categories)
				logger.info(message)
				dict["message"] = str(message)
				dict["new_categories"] = list(set(non_existing_categories))
				dict["is_valid_file_headers"] = True
				return HttpResponse(content=simplejson.dumps(dict), mimetype='application/json')
		for material_item in mat_list[1:n]:
			if request.FILES:
				material_dict = material_item
			else:
				material_dict = ast.literal_eval(material_item)
			material_dict['drawing_no'] = str(material_dict['drawing_no']).upper()
			drawing_no = re.sub("""[^A-z0-9. '\"@$#%:?!*&,()+/._-]""", '', material_dict['drawing_no'].strip()).upper()
			material_dict['name'] = re.sub('\\\|\^|\}|\{|\/|\;|\:|\<|\>', '', remove_non_ascii(str(material_dict['name'])))
			material_name = str(material_dict['name'])
			material_dict['description'] = re.sub('\\\|\^|\}|\{|\/|\;|\:|\<|\>', '', remove_non_ascii(str(material_dict['description'])))
			material = db_session.query(Material).filter_by(
				name=material_name, enterprise_id=enterprise.id).first()
			try:
				category = db_session.query(Category).filter_by(
					name=material_dict['category'], enterprise_id=enterprise.id).first()
				if not category:
					logger.info("Category <%s> doesn't exists need to create new..." % material_dict['category'])
					category = Category(name=material_dict['category'], enterprise_id=enterprise.id)
					db_session.begin(subtransactions=True)
					db_session.add(category)
					db_session.commit()
					category = db_session.query(Category).filter_by(
						name=material_dict['category'], enterprise_id=enterprise.id).first()
				category_id = category.id
			except Exception as e:
				db_session.rollback()
				logger.exception("Import failed: %s" % e.message)

			if material:
				logger.debug("Material exists already %s" % (material_dict['name']))
				# UPDATE MATERIAL
				try:
					db_session.begin(subtransactions=True)
					validation_errors = __validateMaterialRow(material_dict=material_dict, unit_id_list=unit_id_list, m_type=m_type)
					if len(validation_errors) > 0:
						raise Exception(validation_errors)
					db_session.query(Material).filter(
						Material.name == material_name, Material.enterprise_id == enterprise.id).update({
							Material.drawing_no: material_dict['drawing_no'] if material_dict['drawing_no'] else 'null',
							Material.tariff_no: material_dict['hsn_sac'],
							Material.category_id: category_id,
							Material.description: material_dict['description'],
							Material.name: material_dict['name'],
							Material.price: material_dict['price'] or 0,
							Material.in_use: int(material_dict['in_use']),
							Material.unit_id: material_dict['unit'] or 1}, synchronize_session='fetch')
					db_session.commit()
					updated_count += 1
				except Exception as e:
					db_session.rollback()
					failed_items.append("%s %s" % (material_dict['drawing_no'], material_dict['name']))
					logger.error("Skipped Updating Material {enterprise:%s, drawing_no:%s, name:%s} due to: %s" % (
						enterprise.id, material_dict['drawing_no'], material_dict['name'], e.message))
					if m_type == 'goods':
						failed_item_list.append((
							material_dict['drawing_no'], material_dict['name'], material_dict['category'],
							material_dict['description'], material_dict['hsn_sac'],
							material_dict['price'], material_dict['in_use'], material_dict['unit'],
							material_dict['faultless'], material_dict['faulty'], e.message))
					else:
						failed_item_list.append((
							material_dict['drawing_no'], material_dict['name'], material_dict['category'],
							material_dict['description'], material_dict['hsn_sac'],
							material_dict['price'], material_dict['in_use'], material_dict['unit'], e.message))

			else:
				logger.debug("Material doesn't exists")
				# INSERT MATERIAL
				try:
					db_session.begin(subtransactions=True)
					validation_errors = __validateMaterialRow(material_dict=material_dict, unit_id_list=unit_id_list)
					if len(validation_errors) > 0:
						raise Exception(validation_errors)
					import_material_details = Material(
						drawing_no=str(drawing_no)[:20] if str(drawing_no)[:20] != "" else None, name=material_dict['name'], category_id=category_id,
						unit_id=material_dict['unit'], description=material_dict['description'],
						price=material_dict['price'] or 0, in_use=int(material_dict['in_use']),
						tariff_no=material_dict['hsn_sac'], enterprise_id=enterprise.id, last_modified_on=datetime.now(),
						is_service=False if m_type == 'goods' else True, is_stocked=True if m_type == 'goods' else False)
					db_session.add(import_material_details)
					db_session.commit()
					added_count += 1
					if m_type == 'goods':
						if material_dict['faultless'] and float(material_dict['faultless']) > 0:
							grn_materials.append([
								material_dict['name'], material_dict['price'] or 0,
								float(material_dict['faultless']) or 0, 0])
						if material_dict['faulty'] and float(material_dict['faulty']) > 0:
							grn_materials.append(
								[material_dict['name'], material_dict['price'] or 0, float(material_dict['faulty']) or 0, 1])

				except Exception as e:
					db_session.rollback()
					if material_dict['drawing_no'] == "" and material_dict['name'] == "" and material_dict[
						'category'] == "" and material_dict['description'] == "" and material_dict['price'] == "" and \
									material_dict['in_use'] == "" and material_dict['unit'] == "" and material_dict[
						'faultless'] == "" and material_dict['faulty'] == "":
						logger.error('Skipped adding the empty row as its empty')
					else:
						failed_items.append("%s" % material_dict['drawing_no'])
						if m_type == 'goods':
							failed_item_list.append((
								material_dict['drawing_no'], material_dict['name'], material_dict['category'],
								material_dict['description'], material_dict['hsn_sac'], material_dict['price'],
								material_dict['in_use'], material_dict['unit'], material_dict['faultless'],
								material_dict['faulty'], e.message))
						elif m_type == 'service':
							failed_item_list.append((
								material_dict['drawing_no'], material_dict['name'], material_dict['category'],
								material_dict['description'], material_dict['hsn_sac'], material_dict['price'],
								material_dict['in_use'], material_dict['unit'], e.message))
						logger.exception("Skipped adding Material {enterprise:%s, drawing_no:%s} due to: %s" % (
							enterprise.id, material_dict['drawing_no'], e.message))
		location = LocationService().get_default_location(enterprise_id=enterprise.id)
		if len(grn_materials) > 0:
			constructReceipt(
				grn_materials=grn_materials, db_session=db_session, conn=conn, cur=cur, modifying_user=modifying_user,
				enterprise=enterprise, import_type='CSV')
		if m_type == 'goods' and len(grn_materials) > 0:
			for grn_mat in grn_materials:
				item = MasterDAO().getMaterialByMaterialName(name=grn_mat[0], enterprise_id=enterprise.id)
				item_id = item.material_id
				if grn_mat[3] == 0:
					update_closing_stock.delay(enterprise_id=enterprise.id, item_id=item_id,
											   is_sales=False,
											   quantity=int(grn_mat[2]), is_faulty=0, location_id=location.id)
				if grn_mat[3] == 1:
					update_closing_stock.delay(enterprise_id=enterprise.id, item_id=item_id,
											   is_sales=False,
											   quantity=int(grn_mat[2]), is_faulty=1, location_id=location.id)
		message = " No new material imported."
		if added_count > 0:
			message = "Successfully imported %s Material(s)." % added_count
		if len(failed_item_list) > 0:
			message = "%s Failed to import/update %s Material(s): " % (message, len(failed_item_list))
		if updated_count > 0:
			message = "%s Successfully updated %s Material(s)." % (message, updated_count)
		json = {"message": str(message), "failed_items": tuple(failed_item_list), 'is_valid_file_headers': True}
	except Exception as e:
		logger.exception(e.message)
		json = simplejson.dumps(str("Something went wrong !!" + e.message))
	finally:
		conn.close()
	if request.FILES:
		return json
	else:
		return HttpResponse(content=simplejson.dumps(json), mimetype='application/json')


def importBomMaterials(request):
	conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
	try:
		master_dao = MasterDAO()
		bom_file = request.FILES['bomfile']
		rh = RequestHandler(request)
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		accepted_file_types = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv',
		                       'application/vnd.ms-excel', 'application/octet-stream']
		if bom_file.content_type not in accepted_file_types:
			response = "Import Proper csv or xlsx file instead you uploaded " + bom_file.content_type
			logger.info(response)
			return HttpResponse(simplejson.dumps(str(response)))
		cur = conn.cursor()
		parent_id = rh.getPostData("parent_id")
		selected_field = rh.getPostData("select_field")
		validated_csv_list = []
		validated_excel_list = []
		not_in_use_materials = []
		if bom_file.content_type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
			try:
				book = xlrd.open_workbook(file_contents=bom_file.read())
			except xlrd.XLRDError:
				response = "Exception:Cannot read the file csv or xlsx"
				logger.info(response)
				return HttpResponse(simplejson.dumps(str(response)))
			sheet = book.sheet_by_index(0)
			bom_list_xl = []
			dublicate_entries_xl = []
			list_of_columns = [str(selected_field), 'QTY']
			col_list_from_doc = [str(sheet.row(0)[0].value).upper().strip(), str(sheet.row(0)[1].value).upper().strip()]
			if not col_list_from_doc == list_of_columns:
				response = "Columns are mismatched please enter proper column name, available columns :" + str(
					list_of_columns) + ".But entered columns names are:" + str(col_list_from_doc)
				return HttpResponse(simplejson.dumps(str(response)))
			for r in range(1, sheet.nrows):
				if sheet.row(r)[0].value in bom_list_xl:
					dublicate_entries_xl.append(sheet.row(r)[0].value if str(sheet.row(r)[0].value) else '')
				bom_list_xl.append(sheet.row(r)[0].value)
			if len(dublicate_entries_xl) > 0:
				response = "Duplicate entries found for the following material:" + str(list(set(dublicate_entries_xl)))
				return HttpResponse(simplejson.dumps(str(response)))
			if len(bom_list_xl) <= 0:
				response = "Empty data at your file"
				return HttpResponse(simplejson.dumps(str(response)))
			cur.execute("DELETE FROM cat_materials WHERE parent_id=%s AND enterprise_id=%s", (parent_id, enterprise_id))
			for r in range(1, sheet.nrows):
				column_values = str(sheet.row(r)[0].value)
				qty = sheet.row(r)[1].value
				results = ()
				if sheet.row(r)[0].value and str(selected_field) == "MATERIAL NAME"  :
					query = " SELECT m.drawing_no, m.name , m.in_use , um.unit_name , m.enterprise_id, m.id " \
							" from materials m JOIN unit_master um ON m.unit = um.unit_id AND m.enterprise_id = um.enterprise_id " \
							"	where m.enterprise_id = '%s' And m.name='%s' " % (enterprise_id, column_values)
					cur.execute(query)
					result = cur.fetchall()
					results += tuple(result)
				else:
					query = " SELECT m.drawing_no, m.name , m.in_use , um.unit_name , m.enterprise_id, m.id " \
							" from materials m JOIN unit_master um ON m.unit = um.unit_id AND m.enterprise_id = um.enterprise_id " \
							"	where m.enterprise_id = '%s' And m.drawing_no='%s' " % (enterprise_id, column_values)
					cur.execute(query)
					result = cur.fetchall()
					results += tuple(result)
				if results:
					unit_master_name = results[0][3]
					res_material_name = results[0][1]
					item_id = results[0][5]
					try:
						qty == float(qty)
					except:
						qty = 0
					selected_makes = []
					selected_ids = []
					if str(selected_field) == "MATERIAL NAME":
						material = master_dao.getMaterialByMaterialName(name=column_values, enterprise_id=enterprise_id)
						for make in material.makes:
							selected_makes.append(make.make.label)
							selected_ids.append(str(make.make_id))
						material_dict = {
							'drawing_no_selected': results[0][0],
							'material_selected': res_material_name + '-' + results[0][0] if results[0][
								0] else res_material_name,
							'unit_display': unit_master_name, 'id_bill_material-__prefix__-quantity': qty,
							'selected_makes': ", ".join(selected_makes), 'selected_id': ",".join(selected_ids),
							'item_id': item_id
						}
					else:
						material = master_dao.getMaterialBydrawing_no(drawing_no=column_values, enterprise_id=enterprise_id)
						for make in material.makes:
							selected_makes.append(make.make.label)
							selected_ids.append(str(make.make_id))
						material_dict = {
							'drawing_no_selected': results[0][0],
							'material_selected': res_material_name + '-' + results[0][0] if results[0][
								0] else res_material_name,
							'unit_display': unit_master_name, 'id_bill_material-__prefix__-quantity': qty,
							'selected_makes': ", ".join(selected_makes), 'selected_id': ",".join(selected_ids),
							'item_id': item_id
						}
				else:
					field_capitalized = selected_field.capitalize()
					response = "%s does not exist for the given %s: %s" % (field_capitalized, field_capitalized, column_values)
					return HttpResponse(simplejson.dumps(str(response)))
				if float(material_dict['id_bill_material-__prefix__-quantity']) > 0:
					validated_excel_list.append(material_dict)
					material_in_use = results[0][2]
					if not material_in_use:
						not_in_use_materials.append(results[0][1])
			conn.commit()
			response = simplejson.dumps(str("Bill of Material's Added Successfully using Excel file"))
			logger.info(response)
			material_dict = {}
			if not_in_use_materials:
				material_dict['not_in_use_materials'] = not_in_use_materials
			else:
				material_dict['not_in_use_materials'] = []
			material_dict['validated_objects'] = validated_excel_list
			return HttpResponse(simplejson.dumps(material_dict))

		elif bom_file.content_type == 'text/csv' or bom_file.content_type == 'application/vnd.ms-excel' or bom_file.content_type == 'application/octet-stream':
			reader = csv.DictReader(bom_file)
			bom_list_csv = []
			dublicate_entries = []
			for row in reader:
				keys = row.keys()
				list_of_columns = [str(selected_field), 'QTY']
				if not [x.upper() for x in keys if x is not None] == list_of_columns:
					response = "Columns are mismatched please enter proper column name, available columns :" + str(
						list_of_columns) + ".But entered columns names are:" + str(keys)
					return HttpResponse(simplejson.dumps(str(response)))
				if row[keys[0]] in bom_list_csv:
					dublicate_entries.append(str(row[keys[0]]) if str(row[keys[0]]) else '')
				bom_list_csv.append(row[keys[0]])

			if len(dublicate_entries) > 0:
				response = "Duplicate entries found for the following material:" + str(list(set(dublicate_entries)))
				return HttpResponse(simplejson.dumps(str(response)))
			if len(bom_list_csv) <= 0:
				response = "Empty data at your file"
				return HttpResponse(simplejson.dumps(str(response)))
			cur.execute("DELETE FROM cat_materials WHERE parent_id=%s AND enterprise_id=%s", (parent_id, enterprise_id))
			reader = csv.DictReader(bom_file)
			for row in reader:
				column_values = row[keys[0]]
				qty = row[keys[1]]
				results = ()
				if str(selected_field) == "MATERIAL NAME":
					query = " SELECT m.drawing_no, m.name , m.in_use , um.unit_name , m.enterprise_id, m.id " \
							" from materials m JOIN unit_master um ON m.unit = um.unit_id AND m.enterprise_id = um.enterprise_id " \
							"	where m.enterprise_id = '%s' And m.name='%s' " % (enterprise_id, column_values)
					cur.execute(query)
					result = cur.fetchall()
					results += tuple(result)

				else:
					query = " SELECT m.drawing_no, m.name , m.in_use , um.unit_name , m.enterprise_id, m.id " \
							" from materials m JOIN unit_master um ON m.unit = um.unit_id AND m.enterprise_id = um.enterprise_id " \
							"	where m.enterprise_id = '%s' And m.drawing_no ='%s' " % (enterprise_id, column_values)
					cur.execute(query)
					result = cur.fetchall()
					results += tuple(result)

				if results:
					unit_master_name = results[0][3]
					res_material_name = results[0][1]
					item_id = results[0][5]
					try:
						qty == float(qty)
					except:
						qty = 0
					selected_makes = []
					selected_ids = []
					if str(selected_field) == "MATERIAL NAME":
						selected_makes = []
						selected_ids = []
						material = master_dao.getMaterialByMaterialName(name=column_values, enterprise_id=enterprise_id)
						for make in material.makes:
							selected_makes.append(make.make.label)
							selected_ids.append(str(make.make_id))
						material_dict = {
							'drawing_no_selected': results[0][0],
							'material_selected': res_material_name + '-' + results[0][0] if results[0][
								0] else res_material_name,
							'unit_display': unit_master_name, 'id_bill_material-__prefix__-quantity': qty,
							'selected_makes': ", ".join(selected_makes), 'selected_id': ",".join(selected_ids),
							'item_id': item_id
						}

					else:
						material = master_dao.getMaterialBydrawing_no(drawing_no=column_values, enterprise_id=enterprise_id)
						for make in material.makes:
							selected_makes.append(make.make.label)
							selected_ids.append(str(make.make_id))
						material_dict = {
							'drawing_no_selected': results[0][0],
							'material_selected': res_material_name + '-' + results[0][0] if results[0][0] else res_material_name,
							'unit_display': unit_master_name, 'id_bill_material-__prefix__-quantity': qty,
							'selected_makes': ", ".join(selected_makes), 'selected_id': ",".join(selected_ids), 'item_id': item_id
						}

				else:
					field_capitalized = selected_field.capitalize()
					response = "%s does not exist for the given %s: %s" % (field_capitalized, field_capitalized, column_values)
					return HttpResponse(simplejson.dumps(str(response)))
				if float(material_dict['id_bill_material-__prefix__-quantity']) > 0:
					validated_csv_list.append(material_dict)
					material_in_use = results[0][2]
					if not material_in_use:
						not_in_use_materials.append(results[0][0])
			conn.commit()
			logger.info("Bill of Material's Added Successfully using csv file")
			material_dict = {}
			if not_in_use_materials:
				material_dict['not_in_use_materials'] = not_in_use_materials
			else:
				material_dict['not_in_use_materials'] = []
			material_dict['validated_objects'] = validated_csv_list
			return HttpResponse(simplejson.dumps(material_dict))
		else:
			logger.info("Import Proper csv or xlsx file instead you uploaded " + bom_file.content_type)
	except Exception as e:
		conn.rollback()
		logger.exception(e)
		response = "Oops exception occurred due to :" + str(e)
		response = simplejson.dumps(str(response))
		return HttpResponse(response)
	finally:
		conn.close()


def saveMaterialCategory(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	name = request_handler.getPostData('name')
	is_service = True if request_handler.getPostData('is_service') == "true" else False
	db_session = SQLASession()
	db_session.begin(subtransactions=True)
	try:
		category = Category(name=name, is_service=is_service, enterprise_id=enterprise_id)
		db_session.add(category)
		db_session.commit()
		db_session.refresh(category)
		json = simplejson.dumps({"new_category_id": category.id, "message": "Material Category Added Successfully"})
		logger.info(json)
	except Exception as e:
		db_session.rollback()
		logger.exception(e)
		json = simplejson.dumps({"new_category_id": None, "message": "%s" % e})
	return HttpResponse(json)


def checkMaterialUnit(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	unit_name = request_handler.getPostData('unit_name')
	try:
		query = """SELECT unit_name FROM unit_master where enterprise_id={enterprise_id} and unit_name='{unit_name}'""".format(
			enterprise_id=enterprise_id, unit_name=unit_name)
		unit_list = executeQuery(query)
		response = response_code.success()
		if unit_list is ():
			response['custom_message'] = "New unit can be add"
		else:
			response['custom_message'] = "Unit already exists"
	except Exception as e:
		response = response_code.internalError()
		response['custom_message'] = "Unit verification failed"
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def saveMaterialUnit(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	unit_name = request_handler.getPostData('unit_name')
	description = request_handler.getPostData('unit_desc')
	db_session = SQLASession()
	db_session.begin(subtransactions=True)
	master_dao = MasterDAO()
	try:
		unit_id = master_dao.db_session.query(func.max(UnitMaster.unit_id)).filter_by(
				enterprise_id=enterprise_id).first()
		unit_id = int(unit_id[0])
		unit_master = UnitMaster(unit_id=unit_id + 1, unit_name=unit_name, unit_description=description, enterprise_id=enterprise_id)
		db_session.add(unit_master)
		db_session.commit()
		db_session.refresh(unit_master)
		json_data = simplejson.dumps({"new_unit_name": unit_master.unit_name, "new_unit_id": unit_master.unit_id, "message": "Material Unit Added Successfully"})
	except Exception as e:
		db_session.rollback()
		logger.exception(e)
		json_data = simplejson.dumps({"new_unit_name": None, "message": "%s" % e})
	return HttpResponse(json_data)


def populateUnitChoices(request):
	"""
	Loads the Material Category Select field with choices appropriate for the enterprise in context.

	:param request:
	:return:
	"""
	logger.info("Populating Unit choices for Material...")
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	unit_choices = populateUnit(enterprise_id=enterprise_id)
	logger.debug("Units - %s" % unit_choices)
	return HttpResponse(content=simplejson.dumps(unit_choices), mimetype='application/json')


def populateMaterialChoices(request):
	"""
	Loads All Material's primary information.

	:param request:
	:return:
	"""
	logger.info("Populating Material choices to be listed in Choice fields...")
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	material_query = """SELECT m.drawing_no, m.name, u.unit_name,m.id,m.is_service as is_service,m.makes_json as make_name
					FROM materials AS m, unit_master AS u 
					WHERE m.in_use=1 AND m.enterprise_id='%s' AND m.is_stocked=1 AND m.unit=u.unit_id 
					AND m.enterprise_id=u.enterprise_id order by m.name""" % enterprise_id
	materials_data_dump = executeQuery(material_query, as_dict=True)
	for item in materials_data_dump:
		item['make_name'] = helper.constructDifferentMakeName(item['make_name'])
	return HttpResponse(content=simplejson.dumps(materials_data_dump), mimetype='application/json')





def getSupplierPrice(request):
	"""
	Get the supplier price by supplier id
	:param request:
	:return:
	"""
	logger.info("getting specific supplier price by material ...")
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	supplier_id = request_handler.getPostData('supplier_id')
	item_id = request_handler.getPostData('item_id')
	make_id = request_handler.getPostData('make_id')
	material_supplier_price_query = """SELECT round(price, 2) as price FROM supplier_materials_price WHERE 
										item_id = '%s' AND make_id = '%s' AND supp_id = '%s' AND status = 1 AND enterprise_id = '%s'""" % (item_id, make_id, supplier_id, enterprise_id)
	materials_supplier_price_dump = executeQuery(material_supplier_price_query)
	return HttpResponse(content=simplejson.dumps(materials_supplier_price_dump), mimetype='application/json')


def maskSupplierPrice(request):
	"""
	Get the supplier price by supplier id
	:param request:
	:return:
	"""
	logger.info("mask supplier price profile by material ...")
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	supplier_id = request_handler.getPostData('supp_id')
	item_id = request_handler.getPostData('item_id')
	make_id = request_handler.getPostData('make_id')
	price = request_handler.getPostData('price')
	status = request_handler.getPostData('status')
	effect_since = request_handler.getPostData('effect_since')
	effect_till = request_handler.getPostData('effect_till')
	mask_type = 0
	if request_handler.getPostData('type') == 'mask':
		mask_type = 1

	if effect_till:
		material_supplier_price_query = """UPDATE supplier_materials_price SET is_mask_price = '%s' WHERE 
											item_id = '%s' AND make_id = '%s' AND supp_id = '%s' AND status = '%s' 
											AND enterprise_id = '%s' AND effect_since = '%s' AND effect_till = '%s'
											AND price='%s' """ % (
			mask_type, item_id, make_id, supplier_id, status, enterprise_id, effect_since, effect_till, price)
	else:
		material_supplier_price_query = """UPDATE supplier_materials_price SET is_mask_price = '%s' WHERE 
											item_id = '%s' AND make_id = '%s' AND supp_id = '%s' AND status = '%s' 
											AND enterprise_id = '%s' AND effect_since = '%s' AND price='%s' """ % (
			mask_type, item_id, make_id, supplier_id, status, enterprise_id, effect_since, price)
	materials_supplier_price_dump = executeQuery(material_supplier_price_query)
	if materials_supplier_price_dump is ():
		response = response_code.success()
	else:
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def populateCategoryChoices(request):
	"""
	Loads the Material Category Select field with choices appropriate for the enterprise in context.

	:param request:
	:return:
	"""
	logger.info("Populating Category choices for Material...")
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	data = simplejson.loads(simplejson.dumps(request_handler.getPostData()))
	try:
		is_service_data = json.loads(request.body)
	except ValueError as e:
		is_service_data = data
	is_service = 1 if is_service_data['is_service'] == 'true' or is_service_data['is_service'] == True else 0
	category_id = None
	if 'category_id' in is_service_data:
		category_id = is_service_data['category_id'] if is_service_data['category_id'] else None
	category_choices = populateMaterialCategoryChoices(
		enterprise_id=enterprise_id, chosen_category_id=category_id, is_service=is_service)
	return HttpResponse(content=simplejson.dumps(category_choices), mimetype='application/json')


def populateMaterials(request):
	"""
	Loads the Material Select field with choices appropriate for the enterprise in context.

	:param request:
	:return:
	"""
	logger.debug("Populating Materials...")
	master_service = MasterService()
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	pending_status = request_handler.getPostData('filter') if request_handler.getPostData('filter') == '1' else None
	price_profile = request_handler.getPostData('price_profile') if request_handler.getPostData('price_profile') == '1' else None
	is_service = int(request_handler.getPostData('is_service')) if request_handler.getPostData('is_service') else 0

	in_use_items = request_handler.getPostData('in_use_items') if request_handler.getPostData('in_use_items') == '1' else None
	not_in_use_items = request_handler.getPostData('not_in_use_items') if request_handler.getPostData('not_in_use_items') == '1' else None
	all_items = request_handler.getPostData('all_items') if request_handler.getPostData('all_items') == '1' else None
	non_stock_in_use_items = request_handler.getPostData('non_stock_in_use_items') if request_handler.getPostData('non_stock_in_use_items') == '1' else None
	non_stock_not_in_use_items = request_handler.getPostData('non_stock_not_in_use_items') if request_handler.getPostData('non_stock_not_in_use_items') == '1' else None
	non_stock_all_items = request_handler.getPostData('non_stock_all_items') if request_handler.getPostData('non_stock_all_items') == '1' else None

	materials = []
	non_stock_materials = []
	if in_use_items or not_in_use_items or all_items:
		if not is_service:
			materials = master_service.getAllMaterials(
				enterprise_id=enterprise_id, pending_only=pending_status, price_profile=price_profile, is_service=is_service,
				stock_material=1, in_use_items=in_use_items, not_in_use_items=not_in_use_items, all_items=all_items)

	if non_stock_in_use_items or non_stock_not_in_use_items or non_stock_all_items:
		non_stock_materials = master_service.getAllMaterials(
			enterprise_id=enterprise_id, pending_only=pending_status, price_profile=price_profile, is_service=is_service,
			stock_material=0, in_use_items=non_stock_in_use_items, not_in_use_items=non_stock_not_in_use_items,
			all_items=non_stock_all_items)

	materials_list = []
	for material in materials:
		m = material._asdict()
		if m["last_modified_on"]:
			m["last_modified_on"] = m["last_modified_on"].strftime('%Y-%m-%d %H:%M')
			m['part_number'] = helper.constructMpnMakeName(m['part_number'])
		materials_list.append(m)
	for material in non_stock_materials:
		m = material._asdict()
		if m["last_modified_on"]:
			m["last_modified_on"] = m["last_modified_on"].strftime('%Y-%m-%d %H:%M')
			m['part_number'] = helper.constructMpnMakeName(m['part_number'])
		materials_list.append(m)
	return HttpResponse(content=simplejson.dumps(materials_list), mimetype='application/json')


def checkDrawingNumber(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	drawing_no = request_handler.getPostData('drawing_no').replace("'", "\\'").replace('"', '\\"')
	item_id = request_handler.getPostData('item_id')
	material_name = request_handler.getPostData('material_name').replace("'", "\\'").replace('"', '\\"')
	try:
		drawing_number_dup_count = 0
		material_name_dup_count = 0
		if drawing_no:
			drawing_number_query = """SELECT COUNT(1) FROM materials WHERE enterprise_id='%s' AND drawing_no = '%s' 
				AND id != '%s' """ % (enterprise_id, drawing_no, item_id)
			drawing_number_count = executeQuery(drawing_number_query)

			drawing_number_dup_count = simplejson.dumps(int(drawing_number_count[0][0]))
		if material_name:
			material_name_query = """SELECT COUNT(1) FROM materials WHERE enterprise_id='%s' AND name = '%s' 
				AND id != '%s' """ % (enterprise_id, material_name, item_id)
			material_name_count = executeQuery(material_name_query)
			material_name_dup_count = simplejson.dumps(int(material_name_count[0][0]))
		duplicate_count = {'drawing_no': drawing_number_dup_count, 'name': material_name_dup_count}
	except Exception as e:
		logger.exception(e.message)
		duplicate_count = simplejson.dumps(str(e))
	return HttpResponse(content=simplejson.dumps(duplicate_count), mimetype='application/json')


def checkAlternateUnit(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	alternate_unit_id = request_handler.getPostData('alternate_unit_id')
	try:
		alternate_unit_query = "Select count(1) from indent_material where enterprise_id='%s' and alternate_unit_id = '%s'" % (
			enterprise_id, alternate_unit_id)
		alternate_unit_count = executeQuery(alternate_unit_query)
		if int(alternate_unit_count[0][0]) == 0:
			alternate_unit_query = "Select count(1) from purchase_order_material where enterprise_id='%s' and alternate_unit_id = '%s'" % (
				enterprise_id, alternate_unit_id)
			alternate_unit_count = executeQuery(alternate_unit_query)
		if int(alternate_unit_count[0][0]) == 0:
			alternate_unit_query = "Select count(1) from grn_material where enterprise_id='%s' and alternate_unit_id = '%s'" % (
				enterprise_id, alternate_unit_id)
			alternate_unit_count = executeQuery(alternate_unit_query)
		if int(alternate_unit_count[0][0]) == 0:
			alternate_unit_query = "Select count(1) from crdr_details where enterprise_id='%s' and alternate_unit_id = '%s'" % (
				enterprise_id, alternate_unit_id)
			alternate_unit_count = executeQuery(alternate_unit_query)
		if int(alternate_unit_count[0][0]) == 0:
			alternate_unit_query = "Select count(1) from oa_particulars where enterprise_id='%s' and alternate_unit_id = '%s'" % (
				enterprise_id, alternate_unit_id)
			alternate_unit_count = executeQuery(alternate_unit_query)
		if int(alternate_unit_count[0][0]) == 0:
			alternate_unit_query = "Select count(1) from invoice_materials where enterprise_id='%s' and alternate_unit_id = '%s'" % (
				enterprise_id, alternate_unit_id)
			alternate_unit_count = executeQuery(alternate_unit_query)
		alternate_unit_used_count = simplejson.dumps(int(alternate_unit_count[0][0]))
	except Exception as e:
		logger.exception(e.message)
		alternate_unit_used_count = simplejson.dumps(str(e))
	return HttpResponse(alternate_unit_used_count)


def __validateMaterialRow(material_dict={}, unit_id_list=None, m_type=None):
	errors = ""
	try:
		if not (int(material_dict['in_use']) == 0 or int(material_dict['in_use']) == 1):
			logger.debug("Invalid boolean value for in_use")
			errors = "%s %s" % (errors, "in_use - Column should have 0 or 1 !")
	except ValueError as e:
		errors = "%s %s" % (errors, "in_use - Column should have 0 or 1 !")
	try:
		if int(material_dict['unit']) not in unit_id_list:
			logger.debug("Invalid unit id")
			errors = "%s %s" % (errors, "Invalid unit id ! ")
	except ValueError as e:
		errors = "%s %s" % (errors, "unit - %s!" % e.message)
	if m_type == 'goods':
		try:
			if material_dict['faultless'] is not '':
				if int(float(material_dict['faultless']))  < 0:
					logger.debug("faultless column should have a positive number")
					errors = "%s %s" % (errors, "Faultless column should have a positive number !")
		except Exception as e:
			errors = "%s %s" % (errors, "Faultless column should have a positive number !")
		try:
			if material_dict['faulty'] is not '':
				if int(float(material_dict['faulty'])) < 0:
					logger.debug("faulty column should have a positive number")
					errors = "%s %s" % (errors, "Faulty column should have a positive number !")
		except Exception as e:
			errors = "%s %s" % (errors, "Faulty - column should have a positive number ! ")
	if len(material_dict['name']) == 0:
		errors = "%s %s" % (errors, "drawing_name cannot be empty!")
	if len(material_dict['drawing_no']) > 20:
		errors = "%s %s" % (errors, "drawing_no cannot be more than 20 characters long!")
	if len(material_dict['category']) == 0:
		errors = "%s %s" % (errors, "Category  cannot be empty!")
	return errors


def constructReceipt(
		grn_materials=None, db_session=None, conn=None, cur=None, modifying_user=None, enterprise=None,
		receipt_date=None, import_type=None):
	try:
		location = LocationService().get_default_location(enterprise_id=enterprise.id)
		current_fy = getFinancialYear(
			for_date=convertStringToDate(receipt_date) if receipt_date else datetime.now(),
			fy_start_day=enterprise.fy_start_day)
		grn_types = [key if Receipt.TYPE_CODE[key] == 'GRN' else None for key in Receipt.TYPE_CODE]
		project_code = db_session.query(Project.id).filter(
			Project.enterprise_id == enterprise.id, Project.name == 'DEFAULT').first()
		latest_receipt_no = db_session.query(Receipt.receipt_code).filter(
			Receipt.financial_year == current_fy,
			Receipt.enterprise_id == enterprise.id,
			Receipt.received_against.in_(grn_types),
			Receipt.receipt_code != '0').order_by(Receipt.approved_on.desc()).first()
		receipt_code = "1" if not ('%s' % latest_receipt_no).isdigit() or latest_receipt_no == "0" else int(
			'%s' % latest_receipt_no) + 1
		logger.info("Import Materials - Current FY: %s" % current_fy)
		grn_date = receipt_date if receipt_date else datetime.now()
		save_grn_query = """INSERT INTO grn (receipt_no, grn_date, rec_against, enterprise_id, project_code, 
			party_id, invno, inward_no, inspector, status, inward_date, financial_year, inv_date, approved_on, location_id)
			VALUES ('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')""" % (
			receipt_code, grn_date, 'Issues', enterprise.id, project_code[0], 0, '', '', '', 1, grn_date, current_fy,
			datetime.today(), grn_date, location.id)
		logger.debug(save_grn_query)
		cur.execute(save_grn_query)
		conn.commit()
		cur.execute("SELECT MAX(grn_no) FROM grn")
		grn_no = cur.fetchall()[0][0]
		db_session.begin(subtransactions=True)
		try:
			receipt = db_session.query(Receipt).filter(
				Receipt.enterprise_id == enterprise.id, Receipt.receipt_no == grn_no).first()
			receipt.updateRemarks(remarks="STOCK UPDATE THROUGH IMPORT FROM " + import_type, user=modifying_user)
			db_session.add(receipt)
			db_session.commit()
		except Exception as e:
			logger.warn("Receipt remark update failed %s" % e.message)
			db_session.rollback()
		for grn_mat in grn_materials:
			item = MasterDAO().getMaterialByMaterialName(name=grn_mat[0], enterprise_id=enterprise.id)
			item_id = item.material_id
			insert_grn_material_query = """INSERT INTO grn_material (
				grnNumber, item_id,rec_qty, acc_qty, inv_rate, is_faulty, enterprise_id, location_id) VALUES(
				'%s', '%s', '%s','%s', '%s', '%s', '%s', '%s') """ % (
				grn_no, item_id, grn_mat[2], grn_mat[2], grn_mat[1], grn_mat[3], enterprise.id, location.id)
			logger.debug(insert_grn_material_query)
			cur.execute(insert_grn_material_query)
			conn.commit()
		logger.info("Created Receipt with %s materials" % len(grn_materials))
	except Exception as e:
		logger.info(e)
		conn.rollback()
