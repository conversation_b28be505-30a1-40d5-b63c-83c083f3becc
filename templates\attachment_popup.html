<style type="text/css">
  iframe {
    border:  none;
  }
</style>
<div id="attachmentPreview" class="modal fade" role="dialog">
  	<div class="modal-dialog modal-lg">
    	<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title attachment-description">Attachment</h4>
      		</div>
      		<div class="modal-body text-center">
        		<img id="attachmentPreviewSrc" src="" style="max-width: 850px;">
				    <iframe id="attachmentPreviewSrcPdf" src="" type="application/pdf" width="850" height="500"></iframe>
		    </div>
      		<div class="modal-footer">
        		<button type="button" class="btn btn-cancel" data-dismiss="modal">Close</button>
      		</div>
    	</div>
  	</div>
</div>

<script type="text/javascript">
  function viewAttachedDocument(self){
    var self = $(self);
    var filePath = self.attr('data-url');
    var self_desc = self.closest(".document_attachment_part").data("original-title");
    var base64_data = self.closest("form").find(".base64-file-value").val();
    if(base64_data == undefined || base64_data == ""){
    base64_data = filePath;
    }
    var extension = self.attr("data-extension");
    if(["jpg", "jpeg", "png", "gif", "svg", "pdf"].indexOf(extension) == -1) {
    self.closest("form").submit();
      return;
    }
    if(self.hasClass('base64')) {
      var url = base64_data;
    }
    else {
      var url = `/erp/commons/json/document/?document_uri=${filePath}`;
    }
    if(self.hasClass('base64')) {
        $("#upload_material_modal").modal("hide");
    }
    $("#attachmentPreviewSrc, #attachmentPreviewSrcPdf").removeAttr('src');
        if (extension == "pdf") {
            $("#attachmentPreviewSrcPdf").attr('height', Number($( window ).height()-220));
            $("#attachmentPreviewSrcPdf").attr('src', url).removeClass("hide");
        } else {
            $("#attachmentPreviewSrcPdf").addClass("hide");
            $("#attachmentPreviewSrc").attr('src', url);
        }
        $(".attachment-description").text(self_desc)
    if(self.hasClass('base64')) {
        $("#attachmentPreview").modal('show');
    }
    $('#attachmentPreview').on('hidden.bs.modal', function() {
      $("#upload_material_modal").modal("show");
      $("body").css({paddingRight: ""});
    });
  }
</script>