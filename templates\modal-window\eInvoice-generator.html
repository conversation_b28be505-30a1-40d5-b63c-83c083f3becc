<style>
	#eInvoice-list .checkbox {
		margin: 0;
	}

	#eInvoice-list .checkbox input[type="checkbox"]:checked + label::after {
		background: #209be1;
		color: #FFFFFF;
	}

	.gst-credential-steps {
		background: #FFF;
	    padding: 15px 30px;
	    border-radius: 0 0 15px 15px;
	    border: solid 1px #ccc;
	    margin-top: 15px;
	    line-height: 26px;
	}

</style>

<div id="eInvoiceModal" class="modal fade" role="dialog">
  	<div class="modal-dialog modal-lg">
    	<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Generate e-Invoice Json</h4>
      		</div>
      		<div class="modal-body">
		        {% if is_gst_credentials != True %}
				 <fieldset class="custom-fieldset" id="gst-auth-fieldset">
					 <legend>GST Authentication</legend>
					 <div class="col-sm-4">
						 <input type="text" class="form-control" id="gstUsername" name="gstUsername" placeholder="Username">
					 </div>
					 <div class="col-sm-4">
					 	<input type="password" class="form-control" id="gstPassword" name="gstUsername" placeholder="Password" autocomplete='new-password'>
					 </div>
					 <div class="col-sm-4">
					  	<input type="submit" class="btn btn-save" value="Authenticate" onclick="validateGstCredential()">
					 </div>
					 <br />
					 <div class="col-sm-12 general-warning" style="margin-bottom: 0; margin-top: 15px;">
						<b>Note</b>
						 <ul>
							 <li>Kindly Authenticate GST API access to proceed with the e-Invoicing.</li>
							 <li>We understand your privacy. We Encrypt your credential before saving. </li>
						 </ul>
						 <a role='button' onclick='toggleCredentialSteps();'>Steps to get Credential <i class="fa fa-chevron-down" aria-hidden="true"></i> </a>
						 <div class="gst-credential-steps" style="display: none;">
						 	<h2 style="margin-top: 0;">e-Invoicing Credentials Creation</h2>
						 	<div class="credential-steps">
						 		<i>Step 1:</i> Login to <a target='_blank' href='https://einvoice1.gst.gov.in/'>https://einvoice1.gst.gov.in/</a><br />
						 		<i>Step 2:</i> After logged in to the above website on the left side click on API registration.<br />
						 		API Registration <i class="fa fa-arrow-right" aria-hidden="true"></i> User credentials <i class="fa fa-arrow-right" aria-hidden="true"></i> Create API user<br />
						 		After clicking on Create API user end user will receive OTP from e-Invoice portal.<br />
						 		After entering the OTP click on through GSP and in the drop down list select you GSP name <b>"TERA SOFTWARE LIMITED"</b><br />
						 		Once you select TERA SOFTWARE LIMITED, You can create the user name and password.<br />
						 		User name already given by portal as API_<br />
						 		So always user name starts with API_
						 	</div>
						 </div>
					 </div>
				 </fieldset>
		        {% endif %}
        		<table class="table table-bordered custom-table table-striped" id="eInvoice-list">
					<thead>
						<tr>
							<th class="hide">
								<span class="checkbox">
									<input id="eInvoice-select-all" onchange="eInvoiceAllChangeEvent(this)" type="checkbox" />
									<label for="eInvoice-select-all"></label>
								</span>
							</th>
							<th class="document-type">Invoice No</th>
							<th>Party Name</th>
							<th>Value</th>
							<th>Generate</th>
							<th>Upload</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td colspan="4" class="text-center">Loading... Please wait</td>
						</tr>
					</tbody>
				</table>
      		</div>
      		<div class="modal-footer hide">
		        <input type="text" class="hide" id="eInvoice-tag" />
				<button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
				<button type="button" class="btn btn-save" {% if is_gst_credentials != True %} disabled="disabled" {% endif %} id="btnGenerateJson" onclick="generateEinvoiceJson()">Generate</button>
      		</div>
		</div>
  	</div>
</div>

<div id="uploadEInvoiceModal" class="modal fade" role="dialog">
  	<div class="modal-dialog modal-md">
    	<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Upload e-Invoice Json</h4>
      		</div>
      		<div class="modal-body">
				<div id="uploadIrns" class="col-sm-12" style="padding: 15px 0;">
					<form id="form_upload_irns" onsubmit="return false;" method="post" enctype="multipart/form-data">
						{% csrf_token %}
						<input type="hidden" id="upload_json_invoice_id" name="invoice_id" />
						<input type="file" class="filestyle" data-buttonBefore="true" required="true" name="uploadIrnRequest" id="uploadIrnRequest" onchange="validateIrnFileFormat(this);" />
					</form>
				</div>
      		</div>
      		<div class="modal-footer">
				<button type="button" class="btn btn-save" onclick="uploadIrnsJson()">Upload</button>
				<button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
      		</div>
		</div>
  	</div>
</div>

<script type="text/javascript">
	$(document).ready(function(){
		saveIrnsFile();
		canceluploadIrnsEvent();
	});

	function canceluploadIrnsEvent() {
		$('#uploadEInvoiceModal').on('hidden.bs.modal', function () {
			$("#uploadIrnRequest").filestyle('clear');
		});
	}

	function generateEInvoiceRequest(requestType, pageType){
		if(pageType == "invoice") {
			$("#eInvoice-list").find("th.document-type").text("Invoice No");
			$("#eInvoice-tag").val("invoice");
			einvoiceReqJsonForInvoice(requestType);
		}else if(pageType == "note") {
			$("#eInvoice-list").find("th.document-type").text("Note No");
			$("#eInvoice-tag").val("note");
			einvoiceReqJsonForNote(requestType);
		}
		$("#eInvoiceModal").modal("show");
		$("#eInvoice-list").removeClass("hide");
		$("#eInvoiceModal").find(".modal-title").text("Generate e-Invoice Json");
		$("#eInvoice-list").find("input[type='checkbox']").prop("checked", false);
		$("#eInvoice-list").find("label[for='eInvoice-select-all']").removeClass('partial');
	}

	function uploadIrnsJson(invoiceId="") {
		if($("#uploadIrnRequest").val() != "") {
			//$("#upload_json_invoice_id").val(invoiceId);
			$("#form_upload_irns").submit();
		}
		else {
			swal("","Please select a file to upload!", "warning")
		}
	}

	function validateIrnFileFormat(browsedFile) {
		var fileExtension = ['json'];
		browsedfileid = $("#"+browsedFile.id);
		if ($.inArray(browsedfileid.val().split('.').pop().toLowerCase(), fileExtension) == -1) {
			swal("Invalid File Format", "Please upload only "+fileExtension.join(', ')+" file format.", "warning");
			setTimeout(function(){
				browsedfileid.filestyle('clear');
			},200);
		}
	}

	function saveIrnsFile(){
		$("form#form_upload_irns").submit(function(){
			var regex = /^([a-zA-Z0-9\s_\\.\-:])+(.json)$/;
			if (regex.test($("#uploadIrnRequest").val().toLowerCase())) {
				var formData = new FormData($(this)[0]);
				$.ajax({
					url:"/erp/sales/json/invoice/uploadEinvoiceAcknowledgement/",
					type: 'POST',
					data: formData,
					async: false,
					success: function (data) {
						console.log("DATA", data)
						if (data[0].result == 'GSTR request succeeds') {
							swal("Success", "IRN Uploaded Successfully", "success");
							$("#eInvoiceModal").modal("hide");
							$("#uploadEInvoiceModal").modal("hide");
							$("#uploadIrnRequest").filestyle('clear');
						}
					},
					error : function(xhr,errmsg,err)
					 {
						console.log(xhr.status + ": " + xhr.responseText);
					},
					cache: false,
					contentType: false,
					processData: false
				});
			}
			else {
			   swal('','please upload the  json file', 'warning');
			   return false;
			}
		});
	}

	function einvoiceReqJsonForNote(requestType){
		$.ajax({
	        url: "/erp/auditing/json/icd/getNoteEinvoiceList/",
	        type: "POST",
	        dataType: "json",
	        data:{},
	        success: function (json) {
	            var row = "";
	            if(requestType == "multiple") {
					json.forEach(function(item){
						row = `<tr>
									<td class="text-center hide">
										<span class="checkbox">
											<input id="eInvoice-`+ item[0] +`" type="checkbox" value="`+ item[0] +`" onchange='eInvoiceListChangeEvent()'/>
											<label for="eInvoice-`+ item[0] +`"></label>
										</span>
									</td>
									<td class="text-center">`+ (item[2] != null ? item[2] : '') +`</td>
									<td>`+ item[1] +`</td>
									<td class="text-right">`+ item[3].toFixed(2) +`</td>
									<td class="text-center">
										<input type="submit" id="generate-eInvoice-`+ item[0] +`" class="btn btn-add-new" value="Generate JSON" onclick="generateIrn(${item[0]})">
									</td>
									<td class="text-center">
										<input type="submit" id="eInvoice-`+ item[0] +`" class="btn btn-add-new" value="Upload" onclick="uploadIrn(${item[0]})">
									</td>
								</tr>` + row;
					});
				}
				if(row == ""){
					row = `<tr><td colspan="4" class="text-center">No Record found!</td></tr>`;
				}
	            $("#eInvoice-list tbody").html(row);
	        },
	        error: function (xhr, errmsg, err) {
	            swal("","Server side error.","error");
	            console.log(xhr.status + ": " + xhr.responseText);
	        }
	    });
	}

	function einvoiceReqJsonForInvoice(requestType){
		$.ajax({
	        url: "/erp/sales/json/invoice/getInvoiceEinvoiceList/",
	        type: "POST",
	        dataType: "json",
	        data:{},
	        success: function (json) {
	            var row = "";
	            if(requestType == "multiple") {
					json.forEach(function(item){
						row = `<tr>
									<td class="text-center hide">
										<span class="checkbox">
											<input id="eInvoice-`+ item[0] +`" type="checkbox" value="`+ item[0] +`" onchange='eInvoiceListChangeEvent()'/>
											<label for="eInvoice-`+ item[0] +`"></label>
										</span>
									</td>
									<td class="text-center">`+ (item[2] != null ? item[2] : '') +`</td>
									<td>`+ item[1] +`</td>
									<td class="text-right">`+ item[3].toFixed(2) +`</td>
									<td class="text-center">
										<input type="submit" id="generate-eInvoice-`+ item[0] +`" class="btn btn-add-new" value="Generate JSON" onclick="generateIrn(${item[0]})">
									</td>
									<td class="text-center">
										<input type="submit" id="eInvoice-`+ item[0] +`" class="btn btn-add-new" value="Upload" onclick="uploadIrn(${item[0]})">
									</td>
								</tr>` + row;
					});
				}
				if(row == ""){
					row = `<tr><td colspan="4" class="text-center">No Record found!</td></tr>`;
				}
	            $("#eInvoice-list tbody").html(row);
	        },
	        error: function (xhr, errmsg, err) {
	            swal("","Server side error.","error");
	            console.log(xhr.status + ": " + xhr.responseText);
	        }
	    });
	}

	function uploadIrn(invoiceId) {
		$("#uploadEInvoiceModal").modal("show");
		$("#upload_json_invoice_id").val(invoiceId);
	}

	function generateIrn(invoiceId) {
		generateEinvoiceJson(invoiceId);
	}

	function downloadObjectAsJson(exportObj, exportName){
	    var dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(exportObj));
	    var downloadAnchorNode = document.createElement('a');
	    downloadAnchorNode.setAttribute("href",     dataStr);
	    downloadAnchorNode.setAttribute("download", exportName + ".json");
	    document.body.appendChild(downloadAnchorNode); // required for firefox
	    downloadAnchorNode.click();
	    downloadAnchorNode.remove();
	  }

	function generateEinvoiceJson(invoiceId="") {
		var tag = $("#eInvoice-tag").val();
		if(tag == "invoice"){
			var invoice_list = [];
			if(invoiceId == "") {
				$("#eInvoice-list input:checkbox[type=checkbox]:checked").each(function(){
					invoice_list.push($(this).val());
				});
			}
			else {
				invoice_list.push(invoiceId);
			}
			if(invoice_list.length != 0) {
				$("#loading").show();
				$.ajax({
					url: "/erp/sales/json/invoice/getEinvoiceJson/",
					type: "POST",
					dataType: "json",
					data:{ invoice_list : invoice_list},
					success: function (json) {
						$("#loading").hide();
						var error_message = [];
						var success_message = [];
						if (json.length > 0){
							json.forEach(function(val) {
								if(val['message'] != 'success'){
				                    var json_parse = (Array.isArray(val['result']) ? val['result'] : JSON.parse(val['result']));
			                        json_parse.forEach(function(res) {
										error_message.push(val['invoice_code'] + ": " + res['ErrorMessage']);
									});
								}else{
									success_message.push("e-invoice json file for " + val['invoice_code'] + " is downloaded.");
									file_name = val['invoice_code'] + ".json";
									download("["+ val['result'] + "]", file_name, "text/plain");
									swal("Success", success_message, "success");
								}
							});

							if(error_message.length > 0){
								swal("Something went wrong", error_message, "warning");
								einvoiceReqJsonForInvoice("multiple");
							}else{
								einvoiceReqJsonForInvoice("multiple");
							}
						}
						else{
							swal("Something went wrong", "Please contact Admnistrator", "warning");
							einvoiceReqJsonForInvoice("multiple");
						}
					},
					error: function (xhr, errmsg, err) {
						swal("","Server side error.","error");
						console.log(xhr.status + ": " + xhr.responseText);
					}
				});
			}
			else {
				swal("","Please select atlease one Invoice to Generate", "warning");
			}
		}else{
			var invoice_list = [];
			if(invoiceId == "") {
				$("#eInvoice-list input:checkbox[type=checkbox]:checked").each(function(){
					invoice_list.push($(this).val());
				});
			}
			else {
				invoice_list.push(invoiceId);
			}
			if(invoice_list.length != 0) {
				$("#loading").show();
			    $.ajax({
			        url: "/erp/auditing/json/icd/getEinvoiceJson/",
			        type: "POST",
			        dataType: "json",
			        data:{ invoice_list : invoice_list},
			        success: function (json) {
			        	console.log("JSON", json)
			            $("#loading").hide();
							var error_message = [];
							var success_message = [];
							$("#loading").hide();
							if (json.length > 0){
								json.forEach(function(val) {
									if(val['message'] != 'success'){
					                    var json_parse = (Array.isArray(val['result']) ? val['result'] : JSON.parse(val['result']));
				                        json_parse.forEach(function(res) {
											error_message.push(val['invoice_code'] + ": " + res['ErrorMessage']);
										});
									}else{
										success_message.push("e-invoice json file for " + val['invoice_code'] + " is downloaded.");
										file_name = val['invoice_code'] + ".json";
										download("["+ val['result'] + "]", file_name, "text/plain");
										swal("Success", success_message, "success");
									}
								});

								if(error_message.length > 0){
									swal("Something went wrong", error_message, "warning");
									einvoiceReqJsonForInvoice("multiple");
								}else{
									einvoiceReqJsonForInvoice("multiple");
								}
							}else{
								swal("Something went wrong", "Please contact Admnistrator", "warning");
								einvoiceReqJsonForNote("multiple");
							}
			        },
			        error: function (xhr, errmsg, err) {
			            swal("","Server side error.","error");
			            console.log(xhr.status + ": " + xhr.responseText);
			        }
			    });
			}
			else {
				swal("","Please select atlease one Note to Generate", "warning");
			}
		}
	}

	function cancelEinvoiceJson(invoiceId) {

		$("#loading").show();
		    $.ajax({
		        url: "/erp/sales/json/invoice/cnlIrn/",
		        type: "POST",
		        dataType: "json",
		        data:{ invoice_id : invoiceId},
		        success: function (json) {
		            $("#loading").hide();
		            console.log(json);
						var error_message = [];
						var success_message = [];
						$("#loading").hide();
		            	json.forEach(function(val) {
		            	    if(val['result'] != 'GSTR request succeeds'){
		            	        if(Array.isArray(val['result'])){
		            	            var json_parse = val['result'];
		            	        }else{
		            	            var json_parse = JSON.parse(val['result']);
		            	        }
								json_parse.forEach(function(res) {
									error_message.push(val['invoice_code'] + ": " + res['ErrorMessage']);
								});
							}else{
								success_message.push("Cancel IRN for " + val['invoice_code'] + " is succeed")
							}
						});
						if(error_message.length > 0){
							swal("Something went wrong", error_message, "warning");
							einvoiceReqJsonForInvoice("multiple");
						}else{
							swal("Success", success_message, "success");
							einvoiceReqJsonForInvoice("multiple");
						}
		        },
		        error: function (xhr, errmsg, err) {
		            swal("","Server side error.","error");
		            console.log(xhr.status + ": " + xhr.responseText);
		        }
		    });
	}


	function cancelEinvoiceNoteJson(invoiceId="") {
		var note_id = $("#id-note_id").val();
		$("#loading").show();
		    $.ajax({
		        url: "/erp/auditing/json/icd/cnlIrn/",
		        type: "POST",
		        dataType: "json",
		        data:{ note_id : note_id},
		        success: function (json) {
		            $("#loading").hide();
		            console.log(json);
						var error_message = [];
						var success_message = [];
						$("#loading").hide();
		            	json.forEach(function(val) {
		            	    if(val['result'] != 'GSTR request succeeds'){
		            	        if(Array.isArray(val['result'])){
		            	            var json_parse = val['result'];
		            	        }else{
		            	            var json_parse = JSON.parse(val['result']);
		            	        }
								json_parse.forEach(function(res) {
									error_message.push(val['invoice_code'] + ": " + res['ErrorMessage']);
								});
							}else{
								success_message.push("Cancel IRN for " + val['invoice_code'] + " is succeed")
							}
						});
						if(error_message.length > 0){
							swal("Something went wrong", error_message, "warning");
							einvoiceReqJsonForNote("multiple");
						}else{
							swal("Success", success_message, "success");
							einvoiceReqJsonForNote("multiple");
						}
		        },
		        error: function (xhr, errmsg, err) {
		            swal("","Server side error.","error");
		            console.log(xhr.status + ": " + xhr.responseText);
		        }
		    });
	}

	function eInvoiceAllChangeEvent(current) {
		var selectAll = $(current).is(":checked");
		$("#eInvoice-list tbody").find("input[type='checkbox']").prop("checked", selectAll);
		$("#eInvoice-select-all").next("label").removeClass("partial");
	}

	function eInvoiceListChangeEvent() {
		var total_eInvoice = $("#eInvoice-list tbody").find("input[type='checkbox']").length;
		var selected_eInvoice = $("#eInvoice-list tbody").find("input[type='checkbox']:checked").length;
		if(total_eInvoice == selected_eInvoice){
			$("#eInvoice-select-all").prop("checked", true);
			$("#eInvoice-select-all").next("label").removeClass("partial");
		}
		else if(selected_eInvoice >= 1) {
			$("#eInvoice-select-all").prop("checked", false);
			$("#eInvoice-select-all").next("label").addClass("partial");
		}
		else {
			$("#eInvoice-select-all").prop("checked", false);
			$("#eInvoice-select-all").next("label").removeClass("partial");
		}
	}

	function generateEinvoiceSingle(invoiceId=""){
		$("#eInvoice-tag").val('invoice');
		if (`{{ is_gst_credentials }}` == 'True'){
			generateEinvoiceJson(invoiceId);
		}else{
			$("#eInvoiceModal").modal("show");
			$("#eInvoice-list").addClass("hide");
			$("#eInvoiceModal .modal-footer").addClass("hide");
		}
	}

	function generateEinvoiceNoteSingle(invoiceId=""){
		$("#eInvoice-tag").val('note');
		var note_id = $("#id-note_id").val();
		generateEinvoiceJson(note_id);
	}


	function validateGstCredential() {
		$(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();
        $(".suggestion-container").remove();
        var ControlCollections = [
            {
                controltype: 'textbox',
                controlid: 'gstUsername',
                isrequired: true,
                errormsg: 'Username is required.'
            },
            {
                controltype: 'textbox',
                controlid: 'gstPassword',
                isrequired: true,
                errormsg: 'Password is required.'
            }
        ];
        var result = JSCustomValidator.JSvalidate(ControlCollections);
        if(result) {
			var username = $('#gstUsername').val();
			var password = $('#gstPassword').val();
			$('#loading').show();
			$.ajax({
		        url: "/erp/admin/json/gst_authentication/",
		        type: "POST",
		        dataType: "json",
		        data:{ username : username, password: password},
		        success: function (response) {
		            var error_message = [];
		            console.log(response);
		            $('#loading').hide();
                    if (response['result'] == "success") {
                        $('#gst-auth-fieldset').hide();
                        $('#btnGenerateJson').prop('disabled', false);
                        $("#eInvoiceModal").modal("hide");
						$("#eInvoice-list").removeClass("hide");
						$("#eInvoiceModal .modal-footer").removeClass("hide");
                        swal({title: "", text: "GST credentials added successfully!", type: "success"});
                    }else{
                        var error_list = JSON.parse(response['error']);
                        error_list.forEach(function (error){
                            error_message.push(error['ErrorMessage']);
                        });
                        swal({title: "", text: error_message, type: "warning"});
                    }
		        },
		        error: function (xhr, errmsg, err) {
		            swal("","Server side error.","error");
		            console.log(xhr.status + ": " + xhr.responseText);
		        }
		    });
        }
	}

	function toggleCredentialSteps() {
		$(".gst-credential-steps").slideToggle();
	}

	function download(content, fileName, contentType) {
		 const a = document.createElement("a");
		 const file = new Blob([content], { type: contentType });
		 a.href = URL.createObjectURL(file);
		 a.download = fileName;
		 a.click();
	}
</script>