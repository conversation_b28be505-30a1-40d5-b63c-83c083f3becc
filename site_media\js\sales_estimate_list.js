$(document).ready(function() {
    TableHeaderFixed();
    $(".chosen-select").chosen();
    if($("#last_created_order_code").val()!="") {
        if($("#last_created_order_code").val().indexOf("success") > 0) {
            swaltype = "success"
        }
        else {
            swaltype = "warning"
        }
        swal({
          title: "",
          text: $("#last_created_order_code").val(),
          type: swaltype
        });
    }
    $("#last_created_order_code").val("");
    $("#id_search_submit").click(function(){
        $("#loading").show();
    });
    $("#se_document_container").html(getPdfLoadingImage());
});

function updateFilterText() {
    $(".filtered-date b").text($("#reportrange").find("span").text());
    $(".filtered-party b").text($("#id_party_name option:selected").text());
    $(".filtered-project b").text($("#id_project option:selected").text());
    $(".filtered-status b").text($("#id_status option:selected").text());
}

function updateSEListJson(){
    setTimeout(function(){
        SEListjsonObj = [];
        var rows = oTable.rows( { page : 'current'} ).data();
        for(var i=0;i<rows.length;i++) {
            var selectedRow = $(rows[i][1]);
            se = {}
            se ["seId"] = selectedRow.attr("data-salesEstimateId");
            se ["seNumber"] = selectedRow.text().trim();
            SEListjsonObj.push(se);
        }
        localStorage.setItem('seListNav', JSON.stringify(SEListjsonObj));
    },10);
}

var oTable;
var oSettings;
function TableHeaderFixed() {
    oTable = $('#se_list').DataTable({
    fixedHeader: false,
    "scrollY": Number($(document).height() - 230),
    "scrollX": true,
    "pageLength": 50,
    "search": {
        "smart": false
    },
    "columns": [
        null,null,
        { "type": "date" },
        { "type": "date" },
        { "type": "date" },
        null,null,null,null,null
        ]
    });
    updateSEListJson();
    oTable.on("draw",function() {
        var keyword = $('#se_list_filter > label:eq(0) > input').val();
        $('#se_list').unmark();
        $('#se_list').mark(keyword,{});
        updateSEListJson();
        setHeightForTable();
        listTableHoverIconsInit('se_list');
    });
    oTable.on('page.dt', function() {
      $('html, body').animate({
        scrollTop: $(".dataTables_wrapper").offset().top - 15
       }, 'slow');
      listTableHoverIconsInit('se_list');
    });
    oSettings = oTable.settings();
    $( window ).resize();
    listTableHoverIconsInit('se_list');
}

$(window).load(function(){
		updateFilterText();
})
	$('.nav-pills li').removeClass('active');
    $("#li_sales_estimate").addClass('active');
    $(".slide_container_part").removeClass('selected');
    $("#menu_sales").addClass('selected');

function openMailPopup() {
    new Mailer().prepareEmailPopup().getSupplierMailID(id=$("#se_id").val(), type= "se").show();
    return false;
}