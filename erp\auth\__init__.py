"""
"""
import logging

__author__ = 'kalaivanan'

logger = logging.getLogger(__name__)

# Session variable keys
SESSION_KEY = '_auth_user_id'
USER_IN_SESSION_KEY = 'user_in_session'
USER_FCM_ID_KEY = 'fcm_id'
SESSION_USERNAME_KEY = 'USERNAME'
SESSION_USERMAIL_KEY = 'USERMAIL'
ENTERPRISE_IN_SESSION_KEY = 'ENTERPRISE'
ENTERPRISE_GST_SESSION_KEY = 'ENTERPRISE_GST'
ENTERPRISE_ID_SESSION_KEY = 'ENTERPRISE_ID'
PRIMARY_ENTERPRISE_ID_SESSION_KEY = 'PRIMARY_ENTERPRISE_ID'
HOME_CURRENCY_KEY = 'HOME_CURRENCY'
LOGIN_TOKEN = 'LOGIN_TOKEN'
ENTERPRISE_CONFIGURATION_CHANGED = 'ENTERPRISE_CONFIGURATION_CHANGED'
ENTERPRISE_SUBSCRIPTION = 'ENTERPRISE_SUBSCRIPTION'
BASIC_MATERIAL_PRICE_KEY = 'BASIC_MATERIAL_PRICE'
STANDARD_MATERIAL_PRICE_KEY = 'STANDARD_MATERIAL_PRICE'

# Indices denoting permission bit
VIEW_FLAG_POSITION = 0
EDIT_FLAG_POSITION = 1
APPROVE_FLAG_POSITION = 2
DELETE_FLAG_POSITION = 3
NOTIFY_FLAG_POSITION = 4

# Enterprise registration form
ENTERPRISE_FORM_KEY = 'enterprise_form'
ENTERPRISE_FORM_PREFIX = 'enterprise'
