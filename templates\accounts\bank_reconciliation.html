{% extends "accounts/sidebar.html" %}
{% block brs %}
<style type="text/css">
	.table input[type="text"] {
		padding: 3px;
		height: 26px;
		box-shadow: 0 0;
	}

	.tr-file-upload .bootstrap-filestyle {
		width:  600px;
		float: left;
	}

	.date_disabled,
	.date_disabled ~ i{
		cursor: not-allowed;
		background: #eee;
		pointer-events: none;
	}
</style>
<script type="text/javascript" src="/site_media/js/brs.js?v={{ current_version }}"></script>
<div class="right-content-container" style="margin-top: 105px;">
	<div class="page-title-container">
		<span class="page-title">Bank Reconciliation</span>
	</div>
	<div class="container-fluid">
		<form id="load_statement_form" action="/erp/accounts/brs/edit/" method="post">{% csrf_token %}
		<div class="col-sm-2 form-group" style="width: 20%;">
			<label>Date Range</label>
			<div id="reportrange" class="report-range form-control">
				<i class="glyphicon glyphicon-calendar"></i>&nbsp;
				<span></span> <b class="caret"></b>
				<input type="hidden" class="fromdate" id="from_date"  name="from_date" value="{{from_date}}"/>
				<input type="hidden" class="todate" id="to_date"   name="to_date" value="{{to_date}}"/>
			</div>
		</div>
		<div class="col-sm-2 form-group">
			<label>Ledger</label>
			{{ brs_search_form.ledger_id }}
			<input type="hidden" id="search_ledger_no" value="{{save_ledger_id}}"/>
		</div>
		<div class="col-sm-2 checkbox">
			<input type="checkbox" hidden class="chkcase" {% if already_reconciled %} checked="checked" {% endif %} name="chk_reconciled" id="chk_reconciled" value="{{already_reconciled}}">
			<label for="chk_reconciled" class="btn-margin-1">Include Reconciled Transactions</label>
		</div>
		<div class="col-sm-2">
			<input type="button" class="btn btn-save btn-margin-1" value="Load Statement" id="load_statement"/>
			{% if access_level.edit %}
				<a id="a-import-party" class="btn btn-add-new btn-margin-1 pull-right import_csv" style="float:right; margin-right: 8px;" onclick="javascript:showImportBrs();" data-tooltip="tooltip" title="Import Bank Statement"><i class="fa fa-upload" aria-hidden="true"></i></a>
			{% else %}
				<a id="a-import-party" class="btn btn-add-new btn-margin-1 pull-right import_csv" style="float:right; margin-right: 8px; opacity: 0.5;" data-tooltip="tooltip" title="You do not have adequate permission to create/ upload in Accounts Module. Please contact the administrator.">
					<i class="fa fa-upload" aria-hidden="true"></i>
				</a>
			{% endif %}	
		</div>
		<div class="form-group col-sm-4" style="margin-top: -55px; margin-bottom: 0; width: 30%;">
				<table border="0" class="side-table">
					<tr>
						<th colspan="2"></th>
						<th class="text-center">Dr</th>
						<th class="text-center">Cr</th>
					</tr>
					<tr>
						<td class="side-header text-center" rowspan="2" style="width: 150px">Closing Balance <i class="fa fa-info-circle" style="color: #fff; margin-left: 4px; cursor: pointer;" aria-hidden="true" data-tooltip="tooltip" data-placement="left" title="Statements for the period shall be considered Reconciled, if the Closing Balance from Bank Statement & the Bank Ledger are the same"></i></td>
						<td class="side-header" style="width: 100px;">Ledger</td>
						<td class="side-content text-right" id="id_dr_total_amount">
							{% if opening_type == 'Dr' %} {{ledger_opening}} {%else%} 0.00 {% endif %}
						</td>
						<td class="side-content text-right" id="id_cr_total_amount">
							{% if opening_type == 'Cr' %} {{ledger_opening}} {%else%} 0.00 {% endif %}
						</td>
					</tr>
					<tr>
						<td class="side-header" style="width: 100px;">Bank</td>
						<td class="side-content text-right" id="id_ref_dr_total_amount">0.00</td>
						<td class="side-content text-right" id="id_ref_cr_total_amount">0.00</td>
					</tr>
					<tr>
						<td colspan="2" class="side-header text-center" style="width: 250px;">Yet to be reconciled <i class="fa fa-info-circle" style="color: #fff; margin-left: 4px; cursor: pointer;" aria-hidden="true" data-tooltip="tooltip" title="Total Un-reconciled Debit and Credit Amount (see table below)"></i></td>
						<td class="side-content text-right" id="id_not_ref_dr_total_amount"></td>
						<td class="side-content text-right" id="id_not_ref_cr_total_amount"></td>
					</tr>
				</table>
			</div>
		</form>

		<form id="save_brs_form" method="POST" action="/erp/accounts/brs/save/">{% csrf_token %}
		<input type="hidden" id="id_save_ledger_id"  name="save_ledger_id" value="{{save_ledger_id}}"/>
		<div class="col-sm-12 voucher-list-container">
			{{ bank_reconciliation_formset.management_form }}
			<table class="table table-bordered custom-table table-striped" id ="voucherList" style="width: 100%; padding-right: 15px;" >
				<thead>
					<tr>
						<th style="width: 5%;">S. No</th>
						<th style="width: 10%;">Transaction Date</th>
						<th style="width: 10%;">Transaction Type</th>
						<th style="width: 10%;">Instrument No.</th>
						<th style="width: 10%;">Voucher Date</th>
						<th style="width: 10%;">Voucher No</th>
						<th style="width: 25%;" class="textbox_header_amt_md">Particulars</th>
						<th style="width: 10%;">Debit<br><span style="font-weight: normal; text-transform: initial;">Total:</span> <span id="th_not_ref_dr_total_amount"</span></th>
						<th style="width: 10%;">Credit <br><span style="font-weight: normal; text-transform: initial;">Total:</span> <span id="th_not_ref_cr_total_amount"</span></th>
						<th style="width: 10%;" class="manual_brs hide">Action</th>
					</tr>
				</thead>
				<tbody>
					<div id="form_set">
						{% for bankreconciliationForm in bank_reconciliation_formset.forms %}
						<tr bgcolor="#ECECEC" id="{{ bankreconciliationForm.prefix }}" align="center">
							<td hidden="hidden">
								{{ bankreconciliationForm.DELETE }}
								{{ bankreconciliationForm.voucher_id }}
								{{ bankreconciliationForm.item_no }}
								{{ bankreconciliationForm.amount }}
								{{ bankreconciliationForm.enterprise_id }}
								{{ bankreconciliationForm.is_debit }}
								{{ bankreconciliationForm.ledger_id}}
								{{ bankreconciliationForm.voucher_no}}
								{{ bankreconciliationForm.ledger_label}}
								{{ bankreconciliationForm.voucher_date}}
							</td>
							<td>{{ forloop.counter }}. </td>
							<td>
								{{ bankreconciliationForm.bank_reconciliation_date}}
								<input onblur="calcTotal();" autocomplete="off" type="text" class="form-control single_datePicker single_datePicker_table single_datePicker_till_date" id="bankreconciliationDate_{{forloop.counter}}" placeholder="Select Date">
								<i class="glyphicon glyphicon-calendar custom-datepicker-icon-table"></i>
							</td>
							<td class="text-left">
							   {% if bankreconciliationForm.is_debit.value == 0 %}
								   Receipt
							   {% else %}
									Payment
							   {% endif %}</td>
							<td>{{ bankreconciliationForm.instrument_no}} </td>
							<td>{{ bankreconciliationForm.voucher_date.value}} </td>
							<td>{{ bankreconciliationForm.voucher_no.value}} </td>
							<td class="text-left">{{ bankreconciliationForm.ledger_label.value }}</td>
							<td><input type="text" id="id_{{ bankreconciliationForm.prefix }}-debit_amount"
							   name="{{ bankreconciliationForm.prefix }}-debit_amount"
							   class='form-control text-right' onKeyPress = "return validateFloatKeyPress(this,event);"
							           disabled="disabled" is_debit="{{bankreconciliationForm.is_debit.value}}"
							   {% if bankreconciliationForm.is_debit.value == '1' or bankreconciliationForm.is_debit.value == 1%}
							           value="{{ bankreconciliationForm.amount.value }}"
							   {% else %}
							           value="0.00"
							   {% endif %}/></td>

							<td><input type="text" id="id_{{ bankreconciliationForm.prefix }}-credit_amount"
							   name="{{ bankreconciliationForm.prefix }}-credit_amount"
							   class='form-control text-right' onKeyPress = "return validateFloatKeyPress(this,event);"
							           disabled="disabled" is_debit="{{bankreconciliationForm.is_debit.value}}" deb_value="{{bankreconciliationForm.amount.value}}"
							   {% if bankreconciliationForm.is_debit.value == '0' or bankreconciliationForm.is_debit.value == 0 %}
							           value="{{ bankreconciliationForm.amount.value }}"
							   {% else %}
							           value="0.00"
							   {% endif %}/>
							</td>
						</tr>
						{% endfor %}
					</div>
				</tbody>
			</table>
		</div>
		<div class="col-sm-12 text-right" style="margin-top: 15px;">
			{% if access_level.edit %}
			<input type="button" id="id_save_reconcile" name="save_reconcile" class="btn btn-save" value="Reconcile">
			<input type="submit" hidden="hidden" id="saveVoucher" value="Save">
			{% endif %}
			<input type="button" id="id_cancel_reconcile" name="cancel_reconcile" class="btn btn-cancel" value="Cancel">
		</div>
	</form>
	</div>

</div>

<div id="modal_importBRS" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg" style="width: 64%">
		<!-- Modal content-->
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Import Bank Statement</h4>
			</div>
			<div class="modal-body">
				<div id="importsupplier">
					<table id="importtable" class="table-bordered table-striped custom-table custom-table-large"
					       style="table-layout: fixed; width: 100%;">
						<thead>
						<tr>
							<th>S.No</th>
							<th>Transaction Date</th>
							<th>Value Date</th>
							<th>Transaction/Cheque No</th>
							<th>Particulars/Remarks</th>
							<th>Debit</th>
							<th>Credit</th>
							<th>Balance AMount</th>
						</tr>
						</thead>
						<tr>
							<td align="center">1</td>
							<td align="center">02-04-2018</td>
							<td align="center">01-04-2018</td>
							<td>SRKV Post</td>
							<td>PNKPalayam</td>
							<td align="right"></td>
							<td align="right">10000.00</td>
							<td align="right">1510000.00</td>
						</tr>
						<tr>
							<td align="center" style="font-size:10px">Number</td>
							<td align="center" style="font-size:10px">Date<br>(dd-mm-yyyy)<br></td>
							<td align="center" style="font-size:10px">Date<br>(dd-mm-yyyy)<br></td>
							<td align="center" style="font-size:10px">Text</td>
							<td align="center" style="font-size:10px">Text</td>
							<td align="center" style="font-size:10px">Number</td>
							<td align="center" style="font-size:10px">Number</td>
							<td align="center" style="font-size:10px">Number</td>
						</tr>
						<tr>
							<td colspan="5"><b>Please Select the File for Import Bank Statement</b></td>
							<td colspan="3" style="border: none;" class="text-right"><a href="/site_media/docs/brs_sample.csv" >Download Sample</a> </td>
						</tr>
						<tr class="tr-file-upload">
							<td colspan="8">
								<label>Date Range: </label><span class="mycolor-blue-2" style="font-size: 16px;" id="brs_selected_daterange"></span><br />
								<label>Ledger: </label><span class="mycolor-blue-2" style="font-size: 16px;" id="brs_selected_ledger"></span>

			                </td>
						</tr>
						<tr class="tr-file-upload">
							<td colspan="8">
								<input type="file" class="load-map filestyle" id="fileUpload" data-buttonBefore="true" accept=".csv" />
			                    <a href="#" class="btn btn-save" id="cmdUpload" value="Add" style="margin-left: 15px;">Upload</a>
			                </td>
						</tr>
					</table>
				</div>
			</div>
		</div>

	</div>
</div>


<script type="text/javascript">
	$("document").ready(function(){
		$(".chosen-select").chosen();
		calcTotal();
		LocalDatePickerKeyUp();
		setContainerSizeFixed("voucher-list-container", 260);
		$("title").text("Bank Reconciliation");
	});

	$(window).load(function(){
		$("#loading").hide();
	});

	function dateChanged(ev) {
		UdateSingleDatePicker($(this).attr('id'));
		calcTotal();
	}

	function LocalDatePickerKeyUp() {
		$(".single_datePicker").keyup(function(event){
			var key = window.event ? event.keyCode : event.which;
			if(event.keyCode == 8) {
				$(this).datepicker("setDate", new Date());
				$(this).val("");
				UdateSingleDatePicker($(this).attr('id'));
				calcTotal();
			}
		});
	}
	
	$('.nav-pills li').removeClass('active');
	$('#li_brs').addClass('active');
</script>
{% endblock %}