import json
import requests
import urllib
from datetime import datetime

import simplejson
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.template.response import TemplateResponse
from pyPdf import PdfFileWriter, PdfFileReader

from erp import properties
from erp.admin.backend import User<PERSON><PERSON>
from erp.auth import USER_IN_SESSION_KEY, SESSION_KEY, ENTERPRISE_IN_SESSION_KEY, ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.commons.backend import push_notification
from erp.dao import executeQuery
from erp.forms import NoteSearchForm, NoteForm, ItemTaxFormset
from erp.formsets import NoteItemFormset, NoteTaxFormset
from erp.helper import populatePartyChoices, populateTaxChoices, generateTaxJSONDump, updateRemarks, getUser, \
	getPartyFrequents, EinvoiceJsonMaker, saveEInvoiceAckData
from erp.icd import logger
from erp.icd.backend import NoteService, NoteVO, NOTE_TAX_PREFIX, NOTE_FORM_PREFIX, NOTE_ITEM_PREFIX, \
	NOTE_ITEM_TAX_PREFIX, ICDService
from erp.models import Receipt, NoteTag, PurchaseOrder, NoteReceiptMap, \
	Enterprise
from erp.models import Tax, NoteTax, CreditDebitNote, NoteItem, NoteItemNonStock, NoteReason, \
	NoteItemTax, NoteItemNonStockTax
from erp.notifications import PUSH_NOTIFICATION
from erp.properties import TEMPLATE_TITLE_KEY
from erp.purchase.document_properties import ICD_PO_PDF_PATH, ICD_INVOICE_PDF_PATH
from erp.purchase.service import PurchaseService
from erp.sales.backend import InvoiceService
from erp.stores.backend import StoresService
from erp.tags import generateTagFormset, getEntityTagMaps
from settings import SQLASession, GCS_PUBLIC_URL, CIPHER_KEY, GST_CLIENT_ID, GST_CLIENT_SECRET_KEY, GST_EMAIL
from util.api_util import JsonUtil, response_code
from util.helper import getAbsolutePath, getFinancialYear, getFormattedDocPath, convertStringToDate, icdEinvoiceQuery, \
	icdEinvoiceItemQuery, icdEinvoiceItemCes, icdEinvoiceNonItemQuery, icdEinvoiceNonItemCes
from cryptography.fernet import Fernet

__author__ = 'saravanan'

RECEIPT_KEY = 'receipt'
NOTE_KEY = 'note'
NOTE_PK_FIELD_KEY = 'receipt_no'
NOTE_ITEM_FORMSET_KEY = 'note_item_formset'
NOTE_LIST = 'note_list'
NOTE_TAX_LIST = 'tax_list'
NOTE_TAX_LOAD = 'load_tax'
NOTE_SEARCH = 'search'
EDIT_NOTE_ID = 'note_edit_id'
PARTY_KEY = 'party'
NOTE_TAG = 'tag'
NOTE_TAGS_FORMSET = 'tags_formset'

# TODO @Kalaivanan Proper method for creating tax rows
# JSON Dump literals
TAX_ROW_DUMP = """<tr name='tr_%s'><th><a role='button' title='Remove Tax' onclick=\"javascript:removePoTax('%s');\" >
				<input type='hidden' class='hnd_text_id' name='po_tax_code' value='%s'/>
				<i class='fa fa-window-close' aria-hidden='true'></i>
				</a></th><th><b>%s</b> <i>(Net Rate)</i></th>
				<td class='form-group'><b>%s %%</b><input type='hidden' name='net_rate%s' id='net_%s' value='%s'/><td width='10px'>&nbsp;</td></td>
				<td class='col-sm-6' style='padding-right:0px;'><input type='text' class='form-control' name='tax%s' style='text-align:right;' readonly='readonly' tabindex='-1'/></td></tr>"""
SUB_TAX_ROW_DUMP = "<tr name='tr_%s'><td colspan='2'>%s</td><td>%s %%</td><td></td></tr>"
TAX_BASE_RATE_ROW_DUMP = "<tr name='tr_%s'><td colspan='2'><i>%s</i></td><td>%s %%</td><td></td></tr>"

DATA_BLOCK_SEPARATOR = "%2C%40%2C"  # ",@,"
DATA_SEPARATOR = "[::]"  # "%5B%3A%3A%5D"
FIELD_SEPARATOR = "[:]"  # "%5B%3A%5D"
COMMA_SEPARATOR = "%2C"  # ","


def renderAuditHome(request):
	"""

	:param request:
	:return:
	"""
	logger.info("The renderAuditHome Loading Triggered...")
	request_handler = RequestHandler(request)
	icd_service = ICDService()
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	enterprise = icd_service.audit_dao.db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
	from_date, to_date = JsonUtil.getDateRange(rh=request_handler, since_session_key="icd.since", till_session_key="icd.till")
	icd_request_acknowledgement = enterprise.is_icd_request_acknowledgement
	# for listing badge count values

	if request_handler.getPostData('icd_fromdate'):
		from_date = str(request_handler.getPostData('icd_fromdate')) + ' 00:00:00'
		cstatus = request_handler.getPostData('icd_status')
		from_date = datetime.strptime(from_date, '%Y-%m-%d %H:%M:%S')
	elif request_handler.getPostData('credit_fromdate'):
		from_date = str(request_handler.getPostData('credit_fromdate')) + ' 00:00:00'
		cstatus = request_handler.getPostData('credit_status')
		from_date = datetime.strptime(from_date, '%Y-%m-%d %H:%M:%S')
	elif request.GET.get('is_credit_debit'):
		cstatus = request_handler.getSessionAttribute(key="credit.status")
	else:
		cstatus = request_handler.getSessionAttribute(key="icd.status")

	projects = executeQuery("""SELECT a.id, CONCAT(a.name,' (', a.code ,')') as name FROM projects as a, enterprise as b 
		WHERE b.id = a.enterprise_id and a.enterprise_id='%s' and is_active = 1 ORDER BY a.name""" % enterprise_id)
	taxes = SQLASession().query(Tax).filter(
		Tax.enterprise_id == request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)).order_by(Tax.name).all()

	if request.GET.get('is_credit_debit'):
		request.session['module_access']['icd'] = True
		request.session['is_credit_debit'] = request.GET.get('is_credit_debit')
	else:
		if request.session.get('is_credit_debit'):
			del request.session['is_credit_debit']
	query = """SELECT gst_username, gst_password from enterprise WHERE id='%s'""" % enterprise_id
	enterprise_credentials = executeQuery(query)
	is_gst_credentials = True if enterprise_credentials[0][0] and len(enterprise_credentials[0][0]) > 0 else False
	return TemplateResponse(
		template='auditing/icd.html', request=request, context={
			'is_credit_debit': True if request.GET.get('is_credit_debit') else False,
			'projects': projects, 'taxes': taxes, 'from_date': from_date.strftime('%Y-%m-%d'), 'to_date': to_date.strftime('%Y-%m-%d'), 'cstatus': cstatus,
			'party': populatePartyChoices(enterprise_id, True, True, True),
			'icd_request_acknowledgement': icd_request_acknowledgement, 'is_gst_credentials': is_gst_credentials,
			TEMPLATE_TITLE_KEY: "Credit/Debit Note" if request.GET.get('is_credit_debit') else "Internal Control"})


def loadNoteList(request):
	logger.info("The Icd List Loading Triggered...")
	request_handler = RequestHandler(request)
	enterprise = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise
	frmdate = request_handler.getAndCacheData(key="from_date", session_key="icd.since" if str(request_handler.getPostData('type')) == 'icd' else "credit.since")
	frmdate = frmdate + " 00:00:00"
	todate = request_handler.getAndCacheData(key="to_date", session_key="icd.till" if str(request_handler.getPostData('type')) == 'icd' else "credit.till")
	todate = todate + " 23:59:59"
	party_id = request_handler.getAndCacheData(key="party_id", session_key="icd.party_id" if str(request_handler.getPostData('type')) == 'icd' else "credit.party_id")
	if not party_id:
		party_id = -1
	status = request_handler.getAndCacheData(key="status", session_key="icd.status" if str(request_handler.getPostData('type')) == 'icd' else "credit.status")
	if not status:
		status = 0
	project_code = str(request_handler.getAndCacheData(key="project", session_key="icd.project" if str(request_handler.getPostData('type')) == 'icd' else "credit.project"))
	if not project_code:
		project_code = '0'
	module_access = request.session['module_access']['dummy_icd']
	credit_debit = request.session.get('is_credit_debit')
	icd_request_acknowledgement = True if enterprise.is_icd_request_acknowledgement else False
	json_receipts = []
	try:
		if str(request_handler.getPostData('type')) == 'icd':
			json_receipts = ICDService().getICDList(
				enterprise_id=enterprise.id, module_access=module_access, credit_debit=credit_debit, frmdate=frmdate, todate=todate,
				party_id=int(party_id), project_code=project_code, status=int(status),
				icd_request_acknowledgement=icd_request_acknowledgement)
		else:
			json_receipts = ICDService().getManualNoteList(
				enterprise_id=enterprise.id, frmdate=frmdate, todate=todate, party_id=int(party_id),
				project_code=project_code, status=int(status))
	except Exception as e:
		logger.exception("ICD fetching error - %s" % e)
	return HttpResponse(content=simplejson.dumps(json_receipts), mimetype='application/json')


def loadReceiptMaterial(request):
	"""
	:param request:
	:return: JSON dumps holding the details of Materials associated with the Receipt queried
	"""
	grn_no = str(request.POST['grn_id'])
	rec_against = str(request.POST['rec_against'])
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	grn_materials = ICDService().getIcdMaterials(grn_no=grn_no, received_against=rec_against, enterprise_id=enterprise_id)
	get_icd_remarks_query = "SELECT remarks FROM crdrnote WHERE grn_no = %s" % grn_no
	query_data = executeQuery(get_icd_remarks_query)
	remarks = ""
	if query_data:
		remarks = query_data[0][0]
	return HttpResponse(content=simplejson.dumps({ "grn_materials" : grn_materials, "icd_remarks" : remarks }) , mimetype='application/json')


def loadGeneralDetails(request):
	"""
	:param request:
	:return: JSON dumps holding the details of Materials associated with the Receipt queried
	"""
	grn_no = RequestHandler(request).getPostData("receipt_no")
	query_icd_details_by_grn_no = "SELECT po_no, description, nr.reason, qty, rate, amount,is_credit," \
	                              "IFNULL((SELECT tax_code FROM crdr_details_nonstock_material_tax as mt ,tax as t WHERE mt.grn_no = nnsm.grn_no and mt.description = nnsm.description and mt.po_no is null and mt.tax_code=t.code and mt.reason_id=nr.id and t.type='CGST' AND mt.enterprise_id = t.enterprise_id), '0') as CGST," \
	                              "IFNULL((SELECT tax_code FROM crdr_details_nonstock_material_tax as mt ,tax as t WHERE mt.grn_no = nnsm.grn_no and mt.description = nnsm.description and mt.po_no is null and mt.tax_code=t.code and mt.reason_id=nr.id and t.type='SGST' AND mt.enterprise_id = t.enterprise_id), '0') as SGST," \
	                              "IFNULL((SELECT tax_code FROM crdr_details_nonstock_material_tax as mt ,tax as t WHERE mt.grn_no = nnsm.grn_no and mt.description = nnsm.description and mt.po_no is null and mt.tax_code=t.code and mt.reason_id=nr.id and t.type='IGST' AND mt.enterprise_id = t.enterprise_id), '0') as IGST  FROM " \
	                              "crdr_details_nonstock_material AS nnsm, note_reason AS nr WHERE nnsm.grn_no=%s AND " \
	                              "nnsm.reason_id=nr.id and po_no is null and (nnsm.dc_id = 0 or nnsm.dc_id is null) " % grn_no
	try:
		logger.debug("General Option Query:%s" % query_icd_details_by_grn_no)
		grn_materials = executeQuery(query_icd_details_by_grn_no)
	except Exception as e:
		logger.exception(e)
		grn_materials = ""
	return HttpResponse(content=simplejson.dumps(grn_materials), mimetype='application/json')


def saveAuditNote(request):
	"""
	:param request:
	:return: JSON dump either success or not
	"""
	request_handler = RequestHandler(request)
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	enterprise_in_session = request_handler.getSessionAttribute(ENTERPRISE_IN_SESSION_KEY)
	enterprise_id = enterprise_in_session.id
	user = UserDAO().getUserById(enterprise_id=enterprise_id, user_id=user_id)
	try:
		receipt_details = simplejson.loads(request_handler.getPostData('note_header'))
		note_item_details = request_handler.getPostData('note_details')
		note_item_details = note_item_details.split(FIELD_SEPARATOR) if len(note_item_details) > 0 else []
		note_taxes = request_handler.getPostData('tax_data')[:-4]
		note_taxes = note_taxes.split(DATA_SEPARATOR) if len(note_taxes) > 0 else []
		gen_details = request_handler.getPostData('general_details')
		gen_details = gen_details.split(FIELD_SEPARATOR) if len(gen_details) > 0 else []
		grn_tags = request_handler.getPostData('tag_data')
		grn_tags = grn_tags.split(DATA_SEPARATOR) if len(grn_tags) > 0 else []
		is_super_edit = request_handler.getPostData('is_super_edit') == 'true'
		data_count = request_handler.getPostData('dataCount')
		time_now = datetime.now()
		receipt_no = None
		note_id = None
		if receipt_details['receipt_no']:
			receipt_no = int(receipt_details['receipt_no'])
		if receipt_details['note_id']:
			note_id = int(receipt_details['note_id'])
		status = int(receipt_details['status'])
		remarks = urllib.unquote(receipt_details['remarks']).decode('utf8')
		icd_service = ICDService()
		db_session = icd_service.audit_dao.db_session
		net_value = receipt_details['net_value']
		logger.info("Net Value:%s" % receipt_details['net_value'])
		try:
			if note_id:
				if float(net_value) == 0:
					icd_service.deleteCrDrNoteDetails(
						enterprise_id=enterprise_id, note_id=note_id, include_note=True)
				else:
					icd_service.deleteCrDrNoteDetails(
						enterprise_id=enterprise_id, note_id=note_id, include_note=False)
		except Exception as e:
			raise e
		try:
			db_session.begin(subtransactions=True)
			receipt = icd_service.audit_dao.getReceipt(enterprise_id=enterprise_id, receipt_no=receipt_no)
			receipt_note = icd_service.audit_dao.getNote(enterprise_id=enterprise_id, note_id=note_id)
			received_against = None
			success_message = None
			if float(net_value) > 0:
				note = receipt_note
				if receipt_note is None:
					financial_year = getFinancialYear(fy_start_day=enterprise_in_session.fy_start_day)
					note_no = db_session.query(CreditDebitNote.code).filter(
						CreditDebitNote.enterprise_id == enterprise_id,
						CreditDebitNote.financial_year == financial_year,
						CreditDebitNote.code != 0).order_by(CreditDebitNote.code.desc()).first()
					logger.info("Note no to be assigned for the new Note: %s" % note_no.code if note_no else 0)
					note = CreditDebitNote(value=receipt_details['net_value'], is_credit=(
								("%s" % receipt_details['change_dr_type']).lower() == "cr"),
						code=1 if (note_no is None) else note_no.code + 1, financial_year=financial_year,
						created_on=datetime.now(), enterprise_id=enterprise_id, round_off=receipt_details['rounded_off'])
				else:
					note.value = receipt_details['net_value']
					note.round_off = receipt_details['rounded_off']
					note.is_credit = 1 if (receipt_details['change_dr_type']).lower() == "cr" else 0
				note.party_id = receipt.supplier_id if receipt else note.party_id
				note.inv_no = receipt.invoice_no if receipt else note.inv_no
				note.inv_date = receipt.invoice_date if receipt else note.inv_date
				note.inv_value = receipt.invoice_value if receipt else note.inv_value
				note.project_code = receipt.project_code if receipt else note.project_code
				note.currency_id = receipt.currency_id if receipt else note.currency_id
				note.updateRemarks(remarks=receipt_details['remarks'], user=user)
				note.raised_on = time_now
				note.raised_by = user_id
				note.approved_by = user_id
				note.approved_on = time_now
				items = []
				non_stock_items = []
				if receipt_no:
					received_against = db_session.query(Receipt.received_against).filter(Receipt.receipt_no == receipt_no).first()
					received_against = received_against[0]
				note_id = note.id
				j = 0
				for i in range(0, int(data_count) - 1):
					note_reason = db_session.query(NoteReason).filter(
						NoteReason.reason == ("%s" % note_item_details[j + 2]).upper()).first()
					po_id = int("%s" % note_item_details[j + 9]) if note_item_details[j + 9] != "" and note_item_details[j + 9] != 'null' and received_against != 'Delivery Challan' else None
					dc_id = note_item_details[j + 15] if note_item_details[j + 15] != "" and note_item_details[j + 15] != 'null' else 0
					item_id = int("%s" % note_item_details[j + 16]) if note_item_details[j + 16] != "" and note_item_details[j + 16] != 'null' else 0
					alternate_unit_id = int("%s" % note_item_details[j + 17]) if note_item_details[j + 17] != "" and note_item_details[j + 17] != 'null' else 0
					hsn_code = "%s" % note_item_details[j + 18] if note_item_details[j + 18] != "" and note_item_details[j + 18] != 'null' else ""
					is_faulty = "%s" % note_item_details[j + 19] if note_item_details[j + 19] != "" and note_item_details[j + 19] != 'null' else 0
					if float(note_item_details[j + 6]) > 0:
						if note_item_details[j + 1] != "" and note_item_details[j + 1] != 'null':
							item = db_session.query(NoteItem).filter(
								NoteItem.enterprise_id == enterprise_id, NoteItem.note_id == note_id,
								NoteItem.po_no == po_id, NoteItem.item_id == item_id,
								NoteItem.reason_id == note_reason.id, NoteItem.dc_id == dc_id,
								NoteItem.is_faulty == is_faulty).first()
							item = item if item else NoteItem(
								note_id=note.id, item_id=item_id, po_no=po_id,
								reason_id=note_reason.id, dc_id=dc_id, enterprise_id=enterprise_id)
							item.quantity = note_item_details[j + 3]
							item.rate = note_item_details[j + 4]
							item.is_credit = (("%s" % note_item_details[j + 7]).lower() == "cr")
							item.amount = note_item_details[j + 6]
							if note_item_details[j + 13] != "":
								make_id = note_item_details[j + 13]
							else:
								make_id = None
							item.make_id = make_id
							item.alternate_unit_id = alternate_unit_id
							item.hsn_code = hsn_code
							item.is_faulty = is_faulty
							note_item_taxes = []
							for k in range(0, 3):
								if note_item_details[j + k + 10] != "0":
									note_item_tax = db_session.query(NoteItemTax).filter(
										NoteItemTax.note_id == note_id,
										NoteItemTax.item_id == item_id,
										NoteItemTax.enterprise_id == enterprise_id, NoteItemTax.po_no == po_id,
										NoteItemTax.reason_id == note_reason.id,
										NoteItemTax.tax_code == note_item_details[j + k + 10],
										NoteItemTax.make_id == make_id, NoteItemTax.dc_id == dc_id,
										NoteItemTax.is_faulty == is_faulty).first()
									note_item_taxes.append(
										note_item_tax if note_item_tax else NoteItemTax(
											note_id=note.id, item_id=item_id,
											enterprise_id=enterprise_id, po_no=po_id, reason_id=note_reason.id,
											tax_code=note_item_details[j + k + 10], make_id=make_id, dc_id=dc_id,is_faulty=is_faulty))

							tax_to_be_removed = set(item.taxes) - set(note_item_taxes)
							for item_tax in tax_to_be_removed:
								db_session.delete(item_tax)
							item.taxes = note_item_taxes

							items.append(item)
						else:
							non_stock_item = db_session.query(NoteItemNonStock).filter(
								NoteItemNonStock.enterprise_id == enterprise_id,
								NoteItemNonStock.note_id == note_id,
								NoteItemNonStock.po_no == po_id,
								NoteItemNonStock.description == note_item_details[j + 8],
								NoteItemNonStock.reason_id == note_reason.id,
								NoteItemNonStock.dc_id == dc_id).first()
							if not non_stock_item:
								non_stock_item = NoteItemNonStock(
									note_id=note_id, description=note_item_details[j + 8], po_no=po_id,
									reason_id=note_reason.id, enterprise_id=enterprise_id, dc_id=dc_id)
							non_stock_item.quantity = note_item_details[j + 3]
							non_stock_item.rate = note_item_details[j + 4]
							non_stock_item.is_credit = (("%s" % note_item_details[j + 7]).lower() == "cr")
							non_stock_item.amount = note_item_details[j + 6]
							if note_item_details[j + 14] != "0":
								unit_id = note_item_details[j + 14]
							else:
								unit_id = 1
							non_stock_item.unit_id = unit_id
							non_stock_item.hsn_code = hsn_code
							non_stock_item_taxes = []
							for k in range(0, 3):
								if note_item_details[j + k + 10] != "0":
									note_ns_item_tax = db_session.query(NoteItemNonStockTax).filter(
										NoteItemNonStockTax.note_id == note_id,
										NoteItemNonStockTax.description == note_item_details[j + 8],
										NoteItemNonStockTax.enterprise_id == enterprise_id,
										NoteItemNonStockTax.po_no == po_id, NoteItemNonStockTax.reason_id == note_reason.id,
										NoteItemNonStockTax.tax_code == note_item_details[j + k + 10]).first()
									non_stock_item_taxes.append(
										note_ns_item_tax if note_ns_item_tax else NoteItemNonStockTax(
											note_id=note_id, description=note_item_details[j + 8],
											enterprise_id=enterprise_id, po_no=po_id, reason_id=note_reason.id,
											tax_code=note_item_details[j + k + 10]))

							ns_tax_to_be_removed = set(non_stock_item.taxes) - set(non_stock_item_taxes)
							for item_tax in ns_tax_to_be_removed:
								db_session.delete(item_tax)
							non_stock_item.taxes = non_stock_item_taxes
							non_stock_items.append(non_stock_item)
					j = j + 19

				j = 1
				logger.info("Length of General Details %s" % int(gen_details[0]))
				if len(gen_details) != 0:
					note_reason = db_session.query(NoteReason).filter(NoteReason.reason == "Others".upper()).first()
					for i in range(0, int(gen_details[0])):
						non_stock_item = db_session.query(NoteItemNonStock).filter(
							NoteItemNonStock.enterprise_id == enterprise_id,
							NoteItemNonStock.note_id == note_id,
							NoteItemNonStock.po_no == 0,
							NoteItemNonStock.description == gen_details[j + 0],
							NoteItemNonStock.reason_id == note_reason.id).first()
						if not non_stock_item:
							non_stock_item = NoteItemNonStock(
								note_id=note_id, description=gen_details[j + 0], reason_id=note_reason.id,
								quantity=gen_details[j + 2], rate=gen_details[j + 3], amount=gen_details[j + 4],
								is_credit=gen_details[j + 5], enterprise_id=enterprise_id)
						hsn_code = "%s" % gen_details[j + 9] if gen_details[j + 9] != "" and gen_details[j + 9] != 'null' else ""
						non_stock_item.quantity = gen_details[j + 2]
						non_stock_item.rate = gen_details[j + 3]
						non_stock_item.amount = gen_details[j + 4]
						non_stock_item.is_credit = gen_details[j + 5]
						non_stock_item.hsn_code = hsn_code
						non_stock_item_taxes = []
						for k in range(0, 3):
							if gen_details[j + k + 6] != "0":
								note_ns_item_tax = db_session.query(NoteItemNonStockTax).filter(
									NoteItemNonStockTax.note_id == note_id,
									NoteItemNonStockTax.description == gen_details[j + 0],
									NoteItemNonStockTax.enterprise_id == enterprise_id,
									NoteItemNonStockTax.po_no == 0, NoteItemNonStockTax.reason_id == note_reason.id,
									NoteItemNonStockTax.tax_code == gen_details[j + k + 6]).first()
								non_stock_item_taxes.append(
									note_ns_item_tax if note_ns_item_tax else NoteItemNonStockTax(
										note_id=note_id, description=gen_details[j + 0],
										enterprise_id=enterprise_id, reason_id=note_reason.id,
										tax_code=gen_details[j + k + 6]))
						ns_tax_to_be_removed = set(non_stock_item.taxes) - set(non_stock_item_taxes)
						for item_tax in ns_tax_to_be_removed:
							if '_sa_instance_state' in item_tax.__dict__:
								db_session.delete(item_tax)
						non_stock_item.taxes = non_stock_item_taxes
						non_stock_items.append(non_stock_item)
						j = j + 10
				taxes = []
				logger.debug("Note Taxes for Receipt - %s" % receipt_no)
				if len(note_taxes) != 0:
					for tax in note_taxes:
						note_tax_data = tax.split(FIELD_SEPARATOR)
						note_tax = db_session.query(NoteTax).filter(
							NoteTax.note_id == note_id, NoteTax.tax_code == note_tax_data[0],
							NoteTax.enterprise_id == enterprise_id).first()
						if not note_tax:
							note_tax = NoteTax(
								note_id=note_id, tax_code=note_tax_data[0], enterprise_id=enterprise_id)
						note_tax.tax_order = int(note_tax_data[1])
						taxes.append(note_tax)
				tags = []
				if len(grn_tags) > 0:
					for tag in grn_tags:
						if grn_tags.index(tag) % 2 != 0:
							tags.append(tag)
				note.items = items
				note.non_stock_items = non_stock_items
				note.taxes = taxes
				note.tags = getEntityTagMaps(
					enterprise_id=enterprise_id, tags=tags, tag_map_class=NoteTag, db_session=db_session)
				if receipt_note is None:
					note_receipt_map = NoteReceiptMap(note_id=note_id, receipt_no=receipt_no, enterprise_id=enterprise_id)
					note_receipt_map.note = note
				note.status = status
				if len(note.items) > 0 or len(note.non_stock_items) > 0:
					db_session.add(note)

			if receipt:
				if float(net_value) > 0 or received_against != "Note":
					# Updates header if note value presents are it is not a manual note
					user = getUser(enterprise_id=enterprise_id, user_id=user_id)
					if is_super_edit:
						receipt.super_modified_by = user_id
						receipt.super_modified_on = time_now
					receipt.last_modified_by = user_id
					receipt.last_modified_on = time_now
					receipt.dr_note_amt = receipt_details['net_value']
					receipt.audit_remarks = updateRemarks(
						json_remarks_list=receipt.audit_remarks, remarks_text=remarks, user=user)
					shall_notify_count = False
					if (receipt.status < Receipt.STATUS_ICD_CHECKED and status == Receipt.STATUS_ICD_CHECKED) or receipt.status == Receipt.STATUS_ICD_RETURNED:
						logger.info("Checking the Note for Receipt - %s" % receipt_no)
						receipt.status = status
						receipt.checked_by = user_id
						receipt.checked_on = time_now
						success_message = simplejson.dumps("Receipt - %s checked by ICD successfully!" % receipt.getCode())
						shall_notify_count = True
					elif receipt.status < Receipt.STATUS_ICD_VERIFIED and status == Receipt.STATUS_ICD_VERIFIED:
						logger.info("Verifying the Note for Receipt - %s" % receipt_no)
						receipt.status = status
						receipt.verified_by = user_id
						receipt.verified_on = time_now
						success_message = simplejson.dumps("Note verified Successfully")
						shall_notify_count = True
					else:
						success_message = simplejson.dumps("Note saved Successfully")
					db_session.commit()
					db_session.refresh(receipt)
					if shall_notify_count:
						icd_service.notifyReceiptNoteCount(
							enterprise_id=enterprise_id, sender_id=user_id, status=status)
					if receipt.received_against == 'Note':
						message = PUSH_NOTIFICATION['icd_saved'] % receipt.audit_note.getCode() if receipt.audit_note else receipt.getCode()
						push_notification(
							enterprise_id=enterprise_id, sender_id=user_id, module_code="ICD", message=message,code=receipt.audit_note.getCode())
			else:
				db_session.commit()
				success_message = simplejson.dumps("Note removed Successfully")
			return HttpResponse(success_message)
		except Exception as e:
			db_session.rollback()
			raise e
	except Exception as e:
		logger.exception("Save Audit Note Failed %s" % e.message)
		json = simplejson.dumps("ICD Failed to Success")
		return HttpResponse(json)


def loadNoteTaxes(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	taxes = []
	receipt_no = request_handler.getPostData("receipt_no")
	try:
		po_taxes = SQLASession().query(NoteTax).filter(NoteTax.note_id == receipt_no).all()
		for po_tax in po_taxes:
			taxes.append(__generateTaxJSONDump(po_tax.tax))
	except Exception as e:
		logger.exception('Fetching of GRN Taxes failed... %s' % e.message)
	return HttpResponse(content=simplejson.dumps(taxes), mimetype='application/json')


def __generateTaxJSONDump(tax):
	"""

	:param tax:
	:return:
	"""
	# TODO @Kalaivanan Proper method for creating tax rows
	compound_suffix = '_compound' if tax.is_compound else ''
	tax_json_dump = [
		TAX_ROW_DUMP % (
			tax.code, tax.code, tax.code, tax.name, tax.net_rate, compound_suffix, tax.code, tax.net_rate,
			compound_suffix), TAX_BASE_RATE_ROW_DUMP % (tax.code, 'Base Rate', tax.base_rate)]
	for sub_tax in tax.sub_taxes:
		tax_json_dump.append(SUB_TAX_ROW_DUMP % (tax.code, sub_tax.name, sub_tax.rate))
	return tax_json_dump


def uploadInvoiceDocument(request):
	"""
	View invoked to upload a scanned/soft-copy document of an Invoice document against the receipt in the context

	:param request:
	:return: a JSON containing the success message.
	"""
	rh = RequestHandler(request)
	enterprise_id = rh.getPostData('enterprise_id')
	if not enterprise_id:
		enterprise_id = rh.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	user_id = rh.getPostData('user_id')
	if not user_id:
		user_id = rh.getSessionAttribute(SESSION_KEY)

	receipt_no = rh.getPostData("receipt_no")
	document_data = rh.getPostData("document_data")
	document_ext = rh.getPostData("document_ext")
	try:
		logger.info('Uploading Invoice for Receipt No: %s File Type: %s' % (receipt_no, document_ext))
		StoresService().saveReceiptNoteDocument(
			receipt_no=receipt_no, enterprise_id=enterprise_id, doc_ext=document_ext, doc_data=document_data,
			user_id=user_id)
	except Exception as e:
		logger.exception('Failed to upload Invoice... %s' % e.message)
		return HttpResponse(
			content=simplejson.dumps("Invoice Upload failed for Receipt - %s" % receipt_no),
			mimetype='application/json')
	return HttpResponse(
		content=simplejson.dumps("Invoice Uploaded for Receipt - %s" % receipt_no), mimetype='application/json')


def loadGRNPoDocuments(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	grn_no = request_handler.getPostData("receipt_no")
	query = """SELECT distinct(po_no) FROM grn_material WHERE grnNumber = %s AND po_no is not null """ % grn_no
	db_session = SQLASession()
	try:
		po_list_dump = executeQuery(query)

		grn_po_doc_writer = PdfFileWriter()
		for po_no in po_list_dump:
			if po_no[0]:
				if int(po_no[0]) > 0:
					po_id = int(po_no[0])
					po_obj = db_session.query(PurchaseOrder).filter(
						PurchaseOrder.enterprise_id == enterprise_id, PurchaseOrder.po_id == po_id).first()
					temp_po_doc_path = getFormattedDocPath(code=po_obj.getCode(), id=po_id)
					PurchaseService().generatePODocument(enterprise_id=enterprise_id, po_id=po_id)
					po_pdf = PdfFileReader(file(getAbsolutePath(temp_po_doc_path), "rb"))
					for page in po_pdf.pages:
						grn_po_doc_writer.addPage(page)
		logger.info("Writing GRN PO documents for grn: %s" % grn_no)
		grn_pos_op_stream = file(getAbsolutePath(ICD_PO_PDF_PATH % grn_no), "wb")
		grn_po_doc_writer.write(grn_pos_op_stream)
		grn_pos_op_stream.close()
		return HttpResponse(content=simplejson.dumps(ICD_PO_PDF_PATH % grn_no), mimetype='application/json')
	except Exception as e:
		logger.info('Error: %s' % e)
		logger.exception('Error: %s' % e)

def getGSTCred(enterprise_id=None):
	"""

	:param enterprise_id:
	:return:
	"""
	response = {}
	try:
		query = """SELECT e.gst_username, e.gst_password, e.gst_auth_token, erd.details as gstin from \
						enterprise as e LEFT JOIN enterprise_registration_detail as erd ON erd.enterprise_id = e.id AND \
						erd.label='GSTIN' WHERE id= '%s' GROUP BY e.id""" % enterprise_id
		result = executeQuery(query, as_dict=True)
		response['gst_username'] = result[0]['gst_username'] if result[0]['gst_username'] is not None and len(result[0]['gst_username']) > 0 else None
		response['gst_password'] = result[0]['gst_password'] if result[0]['gst_password'] is not None and len(result[0]['gst_password']) > 0 else None
		response['gst_auth_token'] = result[0]['gst_auth_token'] if result[0]['gst_auth_token'] is not None and len(result[0]['gst_auth_token']) > 0 else None
		response['gstin'] = result[0]['gstin'] if result[0]['gstin'] is not None and len(result[0]['gstin']) > 0 else None
		return response if all(response.values()) else False
	except Exception as e:
		logger.exception(e)
		return False


def cancelIrn(request):
	"""

	:param request:
	:return:
	"""
	response = []
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		note_id = request_handler.getPostData('note_id')
		gst_credentials = getGSTCred(enterprise_id=enterprise_id)
		query = """SELECT CONCAT(note.financial_year, '/IAN/', LPAD(note.note_no, 6, 0), IF(note.sub_number is not NULL, note.sub_number, '')) note_code, irn_ack_json FROM crdrnote as note WHERE irn_ack_json is not NULL and irn_ack_json != 'null' AND grn_no = %s AND enterprise_id = %s""" % (note_id, enterprise_id)
		result = executeQuery(query, as_dict=True)
		if len(result) > 0 and gst_credentials:
			cipher_suite = Fernet(CIPHER_KEY)
			byte_string = b'%s' % str(gst_credentials['gst_password'])
			gst_username = str(gst_credentials['gst_username'])
			gst_password = cipher_suite.decrypt(byte_string)
			auth_token = str(gst_credentials['gst_auth_token'])
			email = GST_EMAIL
			gstin = str(gst_credentials['gstin'])
			data = """{"Irn": "%s", "CnlRsn": "1", "CnlRem": "Wrong entry"}""" % str(simplejson.loads(result[0]['irn_ack_json'])['Irn'])
			note_code = str(result[0]['note_code'])
			ip_address = request.get_host()
			if not len(auth_token) > 0:
				auth_token = getGSTAuthToken(username=gst_username, password=gst_password, ip_addr=ip_address, gstin=gstin, email=email, enterprise_id=enterprise_id)
			res = postIrnRequest(gst_username=gst_username, auth_token=auth_token, email=email, gstin=gstin, data=data, ip_address=ip_address)
			if len(res.content) > 0:
				if res.json()['status_cd'] != '0':
					response_data = {'invoice_code': note_code, 'result': "GSTR request succeeds"}
					response.append(response_data)
				else:
					for error in json.loads(res.json()['status_desc']):
						if error['ErrorCode'] == '1005':
							auth_token = getGSTAuthToken(username=gst_username, password=gst_password, ip_addr=ip_address, gstin=gstin, email=email, enterprise_id=enterprise_id)
							res = postIrnRequest(gst_username=gst_username, auth_token=auth_token, email=email, gstin=gstin, data=data, ip_address=ip_address)
							if len(res.content) > 0:
								if res.json()['status_cd'] != '0':
									response_data = {'invoice_code': note_code, 'result': "GSTR request succeeds"}
									response.append(response_data)
						else:
							response_data = {'invoice_code': note_code, 'result': res.json()['status_desc']}
							response.append(response_data)
			else:
				auth_token = getGSTAuthToken(username=gst_username, password=gst_password, ip_addr=ip_address, gstin=gstin, email=email, enterprise_id=enterprise_id)
				res = postIrnRequest(gst_username=gst_username, auth_token=auth_token, email=email, gstin=gstin, data=data, ip_address=ip_address)
				response = response_code.success() if len(res.content) > 0 and res.json()['status_cd'] != '0' else response
	except Exception as e:
		response = response_code.failure()
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def postIrnRequest(gst_username=None, auth_token=None, email=None, gstin=None, data=None, ip_address=None):
	"""

	:param gst_username:
	:param auth_token:
	:param data:
	:return:
	"""
	try:
		irn_headers = {
				'ip_address': ip_address, 'client_id': GST_CLIENT_ID,
				'client_secret': GST_CLIENT_SECRET_KEY, 'username': gst_username,
				'auth-token': '%s' % auth_token, 'gstin': gstin, 'Content-type': 'application/json'}
		url = 'https://api.mastergst.com/einvoice/type/CANCEL/version/V1_03?email=%s' % email
		response = requests.post(url, headers=irn_headers, data=data)
	except Exception as e:
		logger.exception(e)
	return response


def getNoteEinvoiceList(request):
	"""

	:param request:
	:return:
	"""
	response = {}
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		query = """
				select  
					note.grn_no,
					p.party_name,
					CONCAT(note.financial_year, '/IAN/', LPAD(note.note_no, 6, 0), IF(note.sub_number is not NULL, note.sub_number, '')) AS note_code,
					note.amount 
				FROM crdrnote note 
				LEFT JOIN party_master p ON p.party_id = note.party_id
					AND p.enterprise_id = note.enterprise_id
				WHERE note.enterprise_id = '%s' AND note.approved_on  > now() - interval 288 hour
				AND note.status = 3 AND (note.irn_ack_json IS NULL OR TRIM(note.irn_ack_json) = 'null') ORDER BY note.grn_no DESC""" % enterprise_id
		result = executeQuery(query)
		logger.debug("Load Invoice Query:%s" % query)
		response = result
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getValidInvoiceList(invoice_list=None):
	"""

	:param invoice_list:
	:return:
	"""
	if 'on' in invoice_list:
		invoice_list.remove('on')
	response = ','.join(invoice_list)
	return response


def getEinvoiceJson(request):
	"""

	:param request:
	:return:
	"""
	response = []
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		invoice_list = getValidInvoiceList(request.POST.getlist('invoice_list[]'))
		query = icdEinvoiceQuery(invoice_list=invoice_list)
		invoice_data = executeQuery(query, as_dict=True)
		for invoice in invoice_data:
			query = icdEinvoiceItemQuery(invoice_id=invoice['invoice_id'])
			item_data = executeQuery(query, as_dict=True)
			query = icdEinvoiceItemCes(invoice_id=invoice['invoice_id'], enterprise_id=enterprise_id)
			common_tax_data = executeQuery(query, as_dict=True)
			if item_data[0]['TotAmt'] is None:
				query = icdEinvoiceNonItemQuery(invoice_id=invoice['invoice_id'])
				item_data = executeQuery(query, as_dict=True)
				query = icdEinvoiceNonItemCes(invoice_id=invoice['invoice_id'], enterprise_id=enterprise_id)
				common_tax_data = executeQuery(query, as_dict=True)
			result = EinvoiceJsonMaker().validateEInvoiceJson(invoice=invoice, item_data=item_data, common_tax_data=common_tax_data)
			if not result['error']:
				logger.info("JSON: %s" % simplejson.dumps(result['response']))
				ip_addr = request.get_host()
				user_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).user_id
				irn_response = getIRNResponse(enterprise_id=enterprise_id, data=result['response'], invoice_id=invoice['invoice_id'], user_id=user_id, ip_addr=ip_addr)
				response_data = {'invoice_code': invoice['No'], 'result': irn_response}
				response.append(response_data)
			else:
				response_data = {'invoice_code': invoice['No'], 'result': result['error']}
				response.append(response_data)

	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getIRNResponse(enterprise_id=None, data=None, invoice_id=None, user_id=None, ip_addr=None):
	"""

	:param enterprise_id:
	:param data:
	:param invoice_id:
	:param ip_addr:
	:return:
	"""
	response = ""

	try:
		enterprise_credentials = getGSTCred(enterprise_id=enterprise_id)
		gst_username = enterprise_credentials['gst_username']
		cipher_suite = Fernet(CIPHER_KEY)
		byte_string = b'%s' % str(enterprise_credentials['gst_password'])
		gst_password = cipher_suite.decrypt(byte_string)
		auth_token = str(enterprise_credentials['gst_auth_token'])
		gstin = str(enterprise_credentials['gstin'])
		email = GST_EMAIL

		if not len(auth_token) > 0:
			auth_token = getGSTAuthToken(username=gst_username, password=gst_password, ip_addr=ip_addr, gstin=gstin, email=email, enterprise_id=enterprise_id)
		params = simplejson.dumps(data)
		irn_headers = {
			'ip_address': ip_addr, 'client_id': GST_CLIENT_ID,
			'client_secret': GST_CLIENT_SECRET_KEY, 'username': gst_username,
			'auth-token': '%s' % auth_token, 'gstin': gstin, 'Content-type': 'application/json'}
		url = 'https://api.mastergst.com/einvoice/type/GENERATE/version/V1_03?email=%s' % email
		res = requests.post(url, headers=irn_headers, data=params)

		if len(res.content) > 0:
			if res.json()['status_cd'] != '0':
				invoice_service = NoteService()
				invoice = invoice_service.note_dao.getNote(
				enterprise_id=enterprise_id, note_id=invoice_id if invoice_id != "" else None)
				db_session = invoice_service.note_dao.db_session
				data = res.json()['data']
				saveEInvoiceAckData(entity=invoice, ack_data=data, user_id= user_id,db_session=db_session)
				response = "GSTR request succeeds"

			else:
				if is_json(res.json()['status_desc']):
					for error in json.loads(res.json()['status_desc']):
						if error['ErrorCode'] == '1005':
							auth_token = getGSTAuthToken(username=gst_username, password=gst_password, ip_addr=ip_addr, gstin=gstin, email=email, enterprise_id=enterprise_id)
							irn_response = getIRNResponse(enterprise_id=enterprise_id, data=data, invoice_id=invoice_id, user_id=user_id, ip_addr=ip_addr)
							response = irn_response
						else:
							response = res.json()['status_desc']
				else:
					response = [{'ErrorCode': res.json()['status_cd'], 'ErrorMessage': res.json()['status_desc']}]
		logger.info("auth token: %s" % auth_token)
	except Exception as e:
		logger.exception(e)
	return response


def getGSTAuthToken(username=None, password=None, ip_addr=None, gstin=None, email=None, enterprise_id=None):
	"""

	:param username:
	:param password:
	:param ip_addr:
	:param enterprise_id:
	:return:
	"""
	auth_token = ""
	try:
		headers = {
				'username': username, 'password': password, 'ip_address': ip_addr,
				'client_id': GST_CLIENT_ID, 'client_secret': GST_CLIENT_SECRET_KEY, 'gstin': gstin}
		url = 'https://api.mastergst.com/einvoice/authenticate?email=%s' % email
		r = requests.get(url, headers=headers)
		auth_token = r.json()['data']['AuthToken']
		query = """UPDATE enterprise SET gst_auth_token='%s' WHERE id='%s'""" % (str(auth_token), enterprise_id)
		executeQuery(query)
	except Exception as e:
		logger.exception(e)
	return auth_token


def loadInvoiceDocuments(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	grn_no = request_handler.getPostData('receipt_no')

	invoice_list_query = """SELECT distinct(dc_id) FROM grn_material where grnNumber = %s""" % grn_no
	try:
		invoice_list_dump = executeQuery(invoice_list_query)

		grn_invoice_doc_writer = PdfFileWriter()
		for invoice_no in invoice_list_dump:
			if invoice_no[0]:
				if int(invoice_no[0]) > 0:
					invoice_id = int(invoice_no[0])
					invoice_document, invoice_code = InvoiceService().generateInvoiceDocument(
						enterprise_id=enterprise_id, invoice_id=invoice_id, invoice_type='Sales',
						user=request_handler.getSessionAttribute(USER_IN_SESSION_KEY))
					temp_po_doc_path = getFormattedDocPath(code=invoice_code, id=invoice_id)
					invoice_pdf = PdfFileReader(file(getAbsolutePath(temp_po_doc_path), "rb"))

					for page in invoice_pdf.pages:
						grn_invoice_doc_writer.addPage(page)
		logger.info("Writing GRN INVOICE documents for grn: %s" % grn_no)
		grn_invoices_op_stream = file(getAbsolutePath(ICD_INVOICE_PDF_PATH % grn_no), "wb")
		grn_invoice_doc_writer.write(grn_invoices_op_stream)
		grn_invoices_op_stream.close()
		return HttpResponse(content=simplejson.dumps(ICD_INVOICE_PDF_PATH % grn_no), mimetype='application/json')
	except Exception as e:
		logger.info('Error: %s' % e)
		logger.exception('Error: %s' % e)


def load_icd_tag(request):  # load GRN and Note Tags
	tag_list = ""
	try:
		grn_no = RequestHandler(request).getPostData("receipt_no")
		query = """SELECT b.tag, a.tag_id FROM note_tags as a,tags as b
						WHERE b.id=a.tag_id and a.note_id=%s""" % grn_no
		tag_list = executeQuery(query)
		if len(tag_list) == 0:
			query = """SELECT b.tag, a.tag_id FROM receipt_tags as a, tags as b
							WHERE b.id=a.tag_id and a.receipt_id= %s""" % grn_no
			tag_list = executeQuery(query)
	except Exception as e:
		logger.exception(e)
	response = HttpResponse(content=simplejson.dumps(tag_list), mimetype='application/json')
	return response


def load_grn_tax(request):
	receipt_no = RequestHandler(request).getPostData("receipt_no")
	grn = SQLASession().query(Receipt).filter(Receipt.receipt_no == receipt_no).first()

	tax_values = grn.getTaxValues()
	logger.debug("Tax Details%s" % tax_values)
	tax_json = []
	for tax_key in tax_values.keys():
		tax = SQLASession().query(Tax).filter(Tax.code == tax_key, Tax.enterprise_id == grn.enterprise_id).first()
		tax_json.append({'tax_code': tax.type, 'rate': tax.net_rate, 'value': tax_values[tax_key]})

	response = HttpResponse(content=simplejson.dumps(tax_json), mimetype='application/json')
	return response


# Note Creation View Page
def renderNote(request):
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	party_id = request_handler.getPostData('party_name')
	status = request_handler.getPostData('status')
	if party_id is None: party_id = 0
	if status is None: status = ""

	from_date, to_date = JsonUtil.getDateRange(rh=request_handler)

	note_service = NoteService()

	projects = executeQuery(
		"""SELECT a.id, a.name FROM projects as a, enterprise as b 
		WHERE b.id = a.enterprise_id and a.enterprise_id='%s' and a.is_active = True ORDER BY a.name""" % enterprise_id)

	taxes = SQLASession().query(Tax).filter(
		Tax.enterprise_id == request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)).all()
	note_obj = CreditDebitNote()
	note_vo = note_service.constructNoteVO(note=note_obj, enterprise_id=enterprise_id)
	logger.debug("Note Tag Formset %s" % note_vo.note_tag_formset)
	logger.debug("Note Header Form %s" % note_vo.note_form)

	remarks_list = note_obj.remarks if note_obj.remarks else []
	frequent_party_list = getPartyFrequents(
		table='grn', enterprise_id=enterprise_id, condition="AND rec_against='Note'", need_configs=True)
	return TemplateResponse(template='auditing/note.html', request=request, context={
		'projects': projects, 'taxes': taxes, 'from_date': from_date, 'to_date': to_date,
		'party_list': populatePartyChoices(enterprise_id), 'note_search': NoteSearchForm(initial={
			'to_date': to_date, 'from_date': from_date, 'party_name': party_id, 'status': status}),
		'frequent_party_list': frequent_party_list,
		NOTE_TAGS_FORMSET: note_vo.note_tag_formset, NOTE_KEY: note_vo.note_form,
		NOTE_TAX_LIST: note_vo.note_tax_formset, NOTE_TAX_LOAD: populateTaxChoices(enterprise_id),
		NOTE_ITEM_FORMSET_KEY: note_vo.note_item_formset, TEMPLATE_TITLE_KEY: "Note", "remarks_list": remarks_list})


def is_json(myjson):
	"""

	:param myjson:
	:return:
	"""
	try:
		json_object = json.loads(myjson)
	except ValueError as e:
		return False
	return True


def editNote(request):
	try:
		note_service = NoteService()
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		note_id = request_handler.getPostData("note_id")
		is_super_edit = request_handler.getPostData("is_super_edit")
		if not is_super_edit:
			is_super_edit = 0
		note_to_edit = note_service.note_dao.getNote(note_id, enterprise_id)
		logger.info("Note To Edit : %s" % note_to_edit)

		note_vo = note_service.constructNoteVO(note=note_to_edit, enterprise_id=enterprise_id)
		logger.debug("Note form Constructed - %s\nNote Item Formset - %s" % (
			note_vo.note_form.as_table(), note_vo.note_item_formset.as_p()))
		note_tax_list = populateTaxChoices(enterprise_id)

		frequent_party_list = getPartyFrequents(
			table='grn', enterprise_id=enterprise_id, condition="AND rec_against='Note'", need_configs=True)
		remarks_list = note_to_edit.remarks if note_to_edit.remarks else []
		attachment_data = {}
		if note_to_edit.attachment and note_to_edit.attachment != 'null':
			json_data = json.loads(json.dumps(note_to_edit.attachment))
			gcs_url = "{url}/{key}".format(url=GCS_PUBLIC_URL, key="{0}/{1}".format(enterprise_id, json_data['uid']))
			attachment_data = {'name': json_data['name'], 'ext': json_data['ext'], 'file': gcs_url}

		return TemplateResponse(template='auditing/note.html', request=request, context={
			"is_super_edit": is_super_edit,
			'note_attachment': attachment_data if bool(attachment_data) else "",
			NOTE_KEY: note_vo.note_form,
			'party_list': populatePartyChoices(enterprise_id),
			'frequent_party_list': frequent_party_list,
			NOTE_ITEM_FORMSET_KEY: note_vo.note_item_formset, TEMPLATE_TITLE_KEY: note_to_edit.getCode(),
			NOTE_TAX_LIST: note_vo.note_tax_formset, NOTE_TAX_LOAD: note_tax_list, EDIT_NOTE_ID: note_id,
			NOTE_TAGS_FORMSET: note_vo.note_tag_formset, 'remarks_list': remarks_list})
	except:
		logger.exception("Note Rendering for Edit Failed...")
		raise


def saveNote(request):
	logger.info("Save Note Triggered...")
	try:
		request_handler = RequestHandler(request)
		enterprise_in_session = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise
		enterprise_id = enterprise_in_session.id
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		attachment_json = request_handler.getPostData('note_attachment_upload_json') if request_handler.getPostData('note_attachment_upload_json') != "" else None
		icd_request_acknowledgement = enterprise_in_session.is_icd_request_acknowledgement
		logger.info("The Enterprise ID: %s" % enterprise_id)
		logger.debug("POST Data - %s" % request_handler.getPostData())
		note_vo = NoteVO(
			note_form=NoteForm(request_handler.getPostData(), prefix=NOTE_FORM_PREFIX, enterprise_id=enterprise_id),
			note_item_formset=NoteItemFormset(request_handler.getPostData(), prefix=NOTE_ITEM_PREFIX),
			note_tax_formset=NoteTaxFormset(request_handler.getPostData(), prefix=NOTE_TAX_PREFIX),
			note_tag_formset=generateTagFormset(request_handler.getPostData()))

		logger.debug("Just before save is called: %s" % note_vo.note_tax_formset)
		i = 0
		for note_item_form in note_vo.note_item_formset:
			note_item_form.taxes = ItemTaxFormset(request_handler.getPostData(), prefix=NOTE_ITEM_TAX_PREFIX % i)
			i += 1
		status = request_handler.getPostData('approve_status')
		if not status or status == "":
			status = 0
		logger.info("Status :%s" % status)
		financial_year = getFinancialYear(fy_start_day=enterprise_in_session.fy_start_day)
		logger.debug("Financial Year:%s" % financial_year)

		user = UserDAO().getUserById(enterprise_id=enterprise_id, user_id=user_id)
		note_service = NoteService()
		saved_note_vo = note_service.saveNote(
			note_vo, status=int(status), financial_year=financial_year, user=user, attachment_json=attachment_json,
			icd_request_acknowledgement=icd_request_acknowledgement, enterprise_id=enterprise_id)
		logger.info("The SaveNote Event Triggered...")
		note_tax_list = populateTaxChoices(enterprise_id)
		messages.success(request, "Note Saved Successfully....")
	except:
		logger.error("Saving Note Failed...")
		raise
	if not saved_note_vo.is_valid():
		return TemplateResponse(template='auditing/note.html', request=request, context={
			NOTE_KEY: saved_note_vo.note_form, NOTE_ITEM_FORMSET_KEY: saved_note_vo.note_item_formset,
			NOTE_TAX_LIST: saved_note_vo.note_tax_formset, NOTE_TAX_LOAD: note_tax_list,
			NOTE_TAGS_FORMSET: saved_note_vo.note_tag_formset})
	return HttpResponseRedirect(properties.MANAGE_NOTE_URL)


def loadNoteTaxEdit(request):
	logger.info("The Edit Tax Loaded...")
	request_handler = RequestHandler(request)
	tax_json_dump_res = []
	try:
		if request_handler.isPostRequest():
			note_id = request_handler.getPostData('note_id')
			enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
			tax_li = SQLASession().query(NoteTax.tax_code).filter(
				NoteTax.note_id == note_id, NoteTax.enterprise_id == enterprise_id).all()

			for res_tax in tax_li:
				tax = (generateTaxJSONDump(SQLASession().query(Tax).filter(
					Tax.code == res_tax.tax_code, Tax.enterprise_id == enterprise_id).first()))
				tax_json_dump_res.append(tax)
	except:
		logger.exception("Tax retrieval fails...")
		from erp.middleware import EXCEPTION_RESPONSE_KEY
		request.session[EXCEPTION_RESPONSE_KEY] = HttpResponse(simplejson.dumps(""), mimetype='application/json')
		raise
	return HttpResponse(content=simplejson.dumps(tax_json_dump_res), mimetype='application/json')


def checkForMatchingInvoice(request):
	logger.info("Checking Invoice No for any GRNs profiled earlier")
	request_handler = RequestHandler(request)
	try:
		data = simplejson.loads(simplejson.dumps(request_handler.getPostData()))
		enterprise = request_handler.getSessionAttribute(ENTERPRISE_IN_SESSION_KEY)

		matched_note = StoresService().getNoteForPurchaseInvoice(
			invoice_no=data["invoice_no"], invoice_date=datetime.strptime(data["invoice_date"], "%Y-%m-%d"),
			fy_start_day=enterprise.fy_start_day, supplier_id=data["party_id"], enterprise_id=enterprise.id,
			current_note_no=data["current_note_no"])
		supplier = executeQuery("SELECT party_name FROM party_master where party_id ='%s' and enterprise_id='%s'" % (
					data['party_id'], enterprise.id))
		supplier_name = supplier[0][0]
		response = response_code.success()
		if matched_note:
			custom_message = """<BR/>A Purchase Invoice with Invoice No <b>%s</b> has already been received from 
				<b>%s</b> during the Financial Year <b>%s</b> Kindly check & correct either of the Invoices No to save the GRN.
				""" % (
				data['invoice_no'], supplier_name, getFinancialYear(
					convertStringToDate(data['invoice_date']), fy_start_day=enterprise.fy_start_day))
			response['custom_message'] = custom_message
			response['warning_type'] = "Hard_warning"
			response['bill_id'] = matched_note.ledger_bill_id
		else:
			matched_note = StoresService().getNoteForPurchaseInvoice(
				invoice_no=data["invoice_no"], invoice_date=datetime.strptime(data["invoice_date"], "%Y-%m-%d"),
				fy_start_day=enterprise.fy_start_day, supplier_id=data["party_id"], enterprise_id=enterprise.id,
				current_note_no=data["current_note_no"], consider_date_validation=False)
			if matched_note:
				custom_message = """<BR/>A Purchase Invoice with Invoice No <b>%s</b> has already been received from 
				<b>%s</b> during the Financial Year <b>%s</b><BR/>Do you want to continue? 
				""" % (
					data['invoice_no'], supplier_name, getFinancialYear(
						convertStringToDate(data['invoice_date']), fy_start_day=enterprise.fy_start_day))
				response['custom_message'] = custom_message
				response['warning_type'] = "Soft_warning"
				response['bill_id'] = matched_note.ledger_bill_id
	except Exception as e:
		response = response_code.internalError()
		response['custom_message'] = e.message
		logger.exception("Failed to check for matching Invoice nos - %s" % e)
	return HttpResponse(json.dumps(response), 'content-type=text/json')
