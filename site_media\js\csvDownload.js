function GeneralExportTableToCSV($table, filename){
	if(($table.find('tbody tr:first-child').text() == 'No Matching Results Found!') || ($table.find('tbody tr:first-child').text() == 'No matching records found')) {	
		swal("No Record Found","Sorry!!! No record found to download.","warning");
		return;
	}
	var thisDownloadVal  = $(this);
	if(thisDownloadVal.hasClass("disable_download")) {
		return;
	}
	$(".downloading-main-container").removeClass('hide').addClass('show');
	setTimeout(function(){
		if (typeof(oTable) != "undefined" && !$table.hasClass("no-sorting")) {
		    oSettings[0]._iDisplayLength = oSettings[0].fnRecordsTotal();
		    oTable.draw();
		}
		if(thisDownloadVal.hasClass('csv_download_hidden')) {
			var $headers = $table.find('tr:has(th)')
				,$rows = $table.find('tr:has(td)')
				,tmpColDelim = String.fromCharCode(11) // vertical tab character
				,tmpRowDelim = String.fromCharCode(0) // null character
				,colDelim = '","'
				,rowDelim = '"\r\n"';
				var csv = '"';
				csv += formatRows($headers.map(grabRow));
				csv += rowDelim;
				csv += formatRows($rows.map(grabRow))+ '"';
		}
		else {
			var $headers = $table.find('tr:has(th):visible')
				,$rows = $table.find('tr:has(td):visible')
				,tmpColDelim = String.fromCharCode(11) // vertical tab character
				,tmpRowDelim = String.fromCharCode(0) // null character
				,colDelim = '","'
				,rowDelim = '"\r\n"';
				var csv = '"';
				csv += formatRows($headers.map(grabRow));
				csv += rowDelim;
				csv += formatRows($rows.map(grabRow))+ '"';	
		}
		
		var csvData = 'data:application/csv;charset=utf-8,' + encodeURIComponent(csv);

		thisDownloadVal.after( "<a class='a_hidden_download hide'><span class='span_hidden_download_text'>Download</span></a>");
		thisDownloadVal.next('.a_hidden_download').attr({
			'download': filename
			,'href': csvData
		});
		thisDownloadVal.next('.a_hidden_download').find('.span_hidden_download_text').click();
		thisDownloadVal.next('.a_hidden_download').remove();
		$(".downloading-main-container").removeClass('show').addClass('hide');
		function formatRows(rows){
			return rows.get().join(tmpRowDelim)
				.split(tmpRowDelim).join(rowDelim)
				.split(tmpColDelim).join(colDelim);
		}
		// Grab and format a row from the table
		function grabRow(i, row) {
            var $row = $(row);
            if (!$row.hasClass('exclude_export')) {
                var $cols = $row.find('td');
                if (!$cols.length) $cols = $row.find('th');

                // Get the headers for each column (assuming headers are in a separate row)
                var headers = $row.closest('table').find('thead th').map(function() {
                    return $(this).text().trim();
                }).get();

                return $cols.map(function(j, col) {
                    var headerText = headers[j] || "";
                    return grabCol(j, col, headerText);
                }).get().join(tmpColDelim);
            }
        }
		// Grab and format a column from the table
		function grabCol(j, col, headerText){
            var $col = $(col);
            if (!$col.hasClass('exclude_export')) {
                var $text = $col.text().trim();

                // Check if the column header is 'drawing no'
                if (headerText.toLowerCase() === 'drawing no') {
                        $text = '"' + $text + '"';
                }

                $text = $text.replace(/,/g, " ");
                return $text.replace(/"/g, "''");
            }
        }
		$(".dataTables_length").find("select").trigger('change');
	},100);
}