import os
import re
import uuid
from datetime import datetime, date
from decimal import Decimal

import pymongo
from concurrent.futures import ThreadPoolExecutor
from dateutil.relativedelta import relativedelta
from lxml import etree
from erp import logger, dao, DEFAULT_MAKE_ID, helper
from erp.accounts import OTHER_DIRECT_INCOME_GROUP_ID, OTHER_INDIRECT_EXPENSES_GROUP_ID, SALES_ACCOUNT_GROUP_ID, \
	SALES_ACCOUNT_GROUP_NAME, OTHER_DIRECT_EXPENSES_GROUP_ID, PACKING_ACCOUNT_NAME, TRANSPORT_ACCOUNT_NAME, \
	OTHER_CHARGES_ACCOUNT_NAME, PURCHASE_ACCOUNT_GROUP_NAME, ROUND_OFF_ACCOUNT_NAME
from erp.dao import DataAccessObject
from erp.helper import populateAccountLedgerChoices
from erp.models import UnitMaster, Account<PERSON>roup, Ledger, Voucher, Category, Invoice, InvoiceMaterial, Receipt, \
	ReceiptMaterial
import xml.etree.ElementTree as xml_et

from erp.sales.backend import InvoiceService
from erp.stores.backend import StoresDAO
from migration import material_query
from migration.mapping import tm_mapping_master, tm_header_details, voucher_type_name

__author__ = 'saravanan'

from settings import bucket, TEMP_DIR, MongoDbConnect, GCS_PUBLIC_URL

from bson.objectid import ObjectId

from util.helper import getAbsolutePath, copyDictToDict

default_accounts_group_dict = {
	"Branch / Divisions", "Capital Account", "Current Assets", "Current Liabilities", "Direct Expenses",
	"Direct Incomes",
	"Fixed Assets", "Indirect Expenses", "Indirect Incomes", "Investments", "Loans (Liability)",
	"Misc. Expenses (ASSET)",
	"Purchase Accounts", "Sales Accounts", "Suspense A/c", "Capital Accounts", "Default Tax Unit"
}

default_accounts_group_parent_name_dict = {
	"Reserves & Surplus": "Capital Account", "Loans & Advances (Asset)": "Current Assets",
	"Bank Accounts": "Current Assets", "Duties & Taxes": "Current Liabilities",
	"Provisions": "Current Liabilities", "Bank OD A/c": "Loans (Liability)",
	"Secured Loans": "Loans (Liability)", "Unsecured Loans": "Loans (Liability)",
	"Deposits (Asset)": "Current Assets", "Stock-in-hand": "Current Assets"
}

default_accounts_group = {
	"Purchase Account": "Purchase Accounts", "Sales Account": "Sales Accounts", "Bank Account": "Bank Accounts"}


class ExportDAO(DataAccessObject):

	def __init__(self):
		super(ExportDAO, self).__init__()


class ExportService:

	def __init__(self, enterprise_id=None):
		self.dao = ExportDAO()
		self.voucher_ids = []
		self.invalid_vouchers = []
		self.invalid_vouchers_status = True

	def getUnitDetails(self, enterprise_id=None, unique_id=None):
		try:
			unit_details = self.dao.db_session.query(
				UnitMaster.unit_name.label('unit_name'),
				UnitMaster.unit_description.label('unit_description')).filter(
				UnitMaster.enterprise_id == enterprise_id).all()
			if len(unit_details) > 0:
				message = "%s Units Exported" % len(unit_details)
				self.updateExportDetails(enterprise_id=enterprise_id, unique_id=unique_id, message=message, level='Info')
			return unit_details
		except Exception as e:
			logger.exception("Error from Getting Unit Details %s" % e.message)
		return None

	def getAccountGroups(self, enterprise_id=None, unique_id=None):
		try:
			account_group = self.dao.db_session.query(AccountGroup).filter(
				AccountGroup.enterprise_id == enterprise_id, AccountGroup.id == 0).first()
			account_groups_dict = self.constructOptionTree(root=account_group, enterprise_id=enterprise_id)
			if len(account_groups_dict) > 0:
				message = "%s Account Groups Exported" % len(account_groups_dict)
				self.updateExportDetails(enterprise_id=enterprise_id, unique_id=unique_id, message=message, level='Info')
			return account_groups_dict
		except Exception as e:
			logger.exception("Error from Getting Account Groups %s" % e.message)
		return None

	def getInventoryAccountGroups(self, enterprise_id=None):
		try:
			account_group_dict = []
			_sale_group_ids = helper.getAccountGroupIDs(
				enterprise_id=enterprise_id, names=[AccountGroup.SALES_GROUP_NAME, AccountGroup.PURCHASE_GROUP_NAME])
			account_group_names = self.dao.db_session.query(AccountGroup.name.label("name")).filter(
				AccountGroup.enterprise_id == enterprise_id, AccountGroup.id.in_(_sale_group_ids)).all()
			for group in account_group_names:
				account_group_dict.append(group.name)
			return account_group_dict
		except Exception as e:
			logger.exception("Error from geting inventory account group %s" % e.message)
		return None

	def getAccountLedgers(self, enterprise_id=None, unique_id=None, is_closing_balance_as_opening=None, from_date=None):
		try:
			account_ledgers_dict = []
			account_ledgers = self.dao.db_session.query(Ledger).filter(
				Ledger.enterprise_id == enterprise_id).all()
			inventory_account_group_dict = self.getInventoryAccountGroups(enterprise_id=enterprise_id)
			if account_ledgers:
				for ledger in account_ledgers:
					address = None
					credit_period = 0
					if ledger.party_map and ledger.party_map.party:
						address = ledger.party_map.party.address_1
						credit_period = ledger.party_map.party.payment_credit_days
					if ledger.group.name in default_accounts_group:
						group_name = default_accounts_group[ledger.group.name]
						affect_stock = 'Yes'
					else:
						if ledger.group.name in inventory_account_group_dict:
							affect_stock = 'Yes'
							group_name = ledger.group.name
						else:
							group_name = ledger.group.name
							affect_stock = 'No'
					is_bill_wise_on = 'No'
					if ledger.group.name == 'Sundry Debtors' or ledger.group.name == 'Sundry Creditors':
						is_bill_wise_on = 'Yes'
					account_ledgers_dict.append({
						'name': "%s-%s" % (ledger.name, group_name),
						'group_name': group_name,
						'opening_balance': ledger.getOpeningBalance(
							as_on=datetime.strptime(from_date, "%Y-%m-%d")) if is_closing_balance_as_opening == 'true' else 0,
						'address': address, 'credit_period': str(credit_period),
						'affect_stock': affect_stock, 'is_bill_wise_on': is_bill_wise_on})
			if len(account_ledgers_dict) > 0:
				message = "%s Account Ledgers Exported" % len(account_ledgers_dict)
				self.updateExportDetails(enterprise_id=enterprise_id, unique_id=unique_id, message=message, level='Info')
			return account_ledgers_dict
		except Exception as e:
			logger.exception("Error from Getting Account Groups %s" % e.message)
		return None

	def updateAccountLedgers(
			self, enterprise_id=None, is_closing_balance_as_opening=None, from_date=None,
			existing_account_ledger_dict=None):
		try:
			account_ledgers = self.dao.db_session.query(Ledger).filter(
				Ledger.enterprise_id == enterprise_id).all()
			inventory_account_group_dict = self.getInventoryAccountGroups(enterprise_id=enterprise_id)
			if account_ledgers:
				for ledger in account_ledgers:
					address = None
					credit_period = 0
					if ledger.party_map and ledger.party_map.party:
						address = ledger.party_map.party.address_1
						credit_period = ledger.party_map.party.payment_credit_days
					if ledger.group.name in default_accounts_group:
						group_name = default_accounts_group[ledger.group.name]
						affect_stock = 'Yes'
					else:
						if ledger.group.name in inventory_account_group_dict:
							affect_stock = 'Yes'
							group_name = ledger.group.name
						else:
							group_name = ledger.group.name
							affect_stock = 'No'
					is_bill_wise_on = 'No'
					if ledger.group.name == 'Sundry Debtors' or ledger.group.name == 'Sundry Creditors':
						is_bill_wise_on = 'Yes'
					name = "%s-%s" % (ledger.name, group_name)
					is_exists = False
					for item in existing_account_ledger_dict:
						if name in item['name']:
							is_exists = True
							break
					if not is_exists:
						existing_account_ledger_dict.append({
							'name': "%s-%s" % (ledger.name, group_name),
							'group_name': group_name, 'opening_balance': ledger.getOpeningBalance(
								as_on=datetime.strptime(from_date, "%Y-%m-%d")) if is_closing_balance_as_opening == 'true' else 0,
							'address': address, 'credit_period': str(credit_period),
							'affect_stock': affect_stock, 'is_bill_wise_on': is_bill_wise_on})
			return existing_account_ledger_dict
		except Exception as e:
			logger.exception("Error from Getting Account Groups %s" % e.message)
		return None

	def getVouchers(self, enterprise_id=None, from_date=None, to_date=None, all_ledger_entries=None, unique_id=None):
		try:
			_vouchers = []
			if all_ledger_entries:
				voucher_type = (1, 2, 3, 6,)
			else:
				voucher_type = (4, 5,)
			invalid_voucher = ""
			for voucher in self.dao.db_session.query(Voucher).filter(
					Voucher.enterprise_id == enterprise_id, Voucher.voucher_date >= from_date,
					Voucher.voucher_date <= to_date, Voucher.status > 0,
					Voucher.type_id.in_(voucher_type),
					Voucher.id.notin_(self.voucher_ids)).order_by(Voucher.voucher_date).all():
				_voucher = self.getVoucherHeaderDetails(voucher=voucher)

				if voucher.isValidParticulars():
					_vouchers.append(self.getVoucherParticulars(voucher=voucher, _voucher=_voucher))
					_voucher['item_particulars'] = []
				else:
					invalid_voucher = voucher.getCode()
					if invalid_voucher not in self.invalid_vouchers:
						self.invalid_vouchers.append(invalid_voucher)
			if invalid_voucher != "":
				self.invalid_vouchers_status = False
				return None
			elif len(_vouchers) > 0 and self.invalid_vouchers_status:
				message = "%s Accounting Vouchers Exported" % len(_vouchers)
				self.updateExportDetails(enterprise_id=enterprise_id, unique_id=unique_id, message=message, level='Info')
				return _vouchers
		except Exception as e:
			logger.exception("Error from Getting Vouchers %s" % e.message)
		return None

	def getVoucherHeaderDetails(self, voucher=None, module=None, dc_or_invoice=None, received_against=None):
		try:
			_voucher = copyDictToDict(source=voucher.__dict__, exclude_keys=('_sa_instance_state',))
			if module and dc_or_invoice:
				_voucher['type'] = self.getVoucherTypeName(
					module=module, dc_or_invoice=dc_or_invoice, received_against=received_against)
			else:
				_voucher['type'] = voucher_type_name[voucher.type.name]
			_voucher['date'] = _voucher['voucher_date']
			_voucher['particulars'] = []
			return _voucher
		except Exception as e:
			logger.exception("Error from get voucher header details %s" % e.message)
		return None

	def getVoucherTypeName(self, module=None, dc_or_invoice=None, received_against=None):
		try:
			vou_type_name = None
			if module == 'receipt' and dc_or_invoice == 1:
				vou_type_name = 'Receipt Note'
			elif module == 'receipt' and dc_or_invoice == 2:
				if received_against == 'Delivery Challan':
					vou_type_name = 'Receipt Note'
				else:
					vou_type_name = 'Purchase'
			elif module == 'receipt' and dc_or_invoice == '0':
				if received_against == 'Issues':
					vou_type_name = 'Stock Journal'
				else:
					vou_type_name = 'Receipt Note'
			elif module == 'issue' and dc_or_invoice == 1:
				vou_type_name = 'Delivery Note'
			elif module == 'issue' and dc_or_invoice == 2:
				vou_type_name = 'Sales'
			return vou_type_name
		except Exception as e:
			logger.exception("Error from get voucher header details %s" % e.message)
		return None

	def getVoucherParticulars(self, voucher=None, _voucher=None, account_ledger_name=None):
		try:
			for particular in voucher.particulars:
				if particular.ledger.group.name in default_accounts_group:
					group_name = default_accounts_group[particular.ledger.group.name]
				else:
					group_name = particular.ledger.group.name
				if account_ledger_name != particular.ledger.name:
					_particular = {
						'is_debit': 'Yes' if particular.is_debit else 'No',
						'ledger_name': "%s-%s" % (particular.ledger.name, group_name),
						'party_ledger': 'Yes' if particular.ledger.group.name in (
							particular.ledger.group.SUNDRY_DEBTOR_GROUP_NAME,
							particular.ledger.group.SUNDRY_CREDITOR_GROUP_NAME) else 'No',
						'amount': round(particular.amount, 2) * -1 if particular.is_debit else round(particular.amount, 2),
						'settlements': []
					}
					for settlement in voucher.bill_settlements:
						if settlement.bill and particular.ledger_id == settlement.bill.ledger_id:
							if settlement.cr_value != 0:
								amount = settlement.cr_value * -1 if particular.is_debit else settlement.cr_value
							else:
								amount = settlement.dr_value * -1 if particular.is_debit else settlement.dr_value
							_particular['settlements'].append({
								'bill_no': settlement.bill.bill_no,
								'amount': round(amount, 2),
								'bill_type': 'New Ref' if settlement.bill.voucher_id == voucher.id else 'Agst Ref'})
					_voucher['particulars'].append(_particular)
			_voucher['particulars'] = sorted(_voucher['particulars'], key=lambda i: i['party_ledger'], reverse=True)
			return _voucher
		except Exception as e:
			logger.exception("Error from Getting Vouchers particulars %s" % e.message)
		return None

	def getItemCategory(self, enterprise_id=None, unique_id=None):
		try:
			item_category = self.dao.db_session.query(Category.name.label('name')).filter(
				Category.enterprise_id == enterprise_id).all()
			if len(item_category) > 0:
				message = "%s Item Category Exported" % len(item_category)
				self.updateExportDetails(enterprise_id=enterprise_id, unique_id=unique_id, message=message, level='Info')
			return item_category
		except Exception as e:
			logger.exception("Error from Getting Item Category %s" % e.message)
		return None

	def getItemMaster(self, enterprise_id=None, unique_id=None, is_closing_balance_as_opening=None, from_date=None):
		try:
			items = dao.executeQuery(material_query.format(enterprise_id=enterprise_id), as_dict=True)
			if is_closing_balance_as_opening == 'true':
				store_dao = StoresDAO()
				for item in items:
					opening_stock_qty = store_dao.getClosingStock(
						enterprise_id=enterprise_id, item_id=item['item_id'], till=datetime.strptime(from_date, "%Y-%m-%d"))
					unit_name = self.manipulateUnitName(unit=item['unit'])
					item['opening_balance'] = "%s %s" % (opening_stock_qty, unit_name)
					item['opening_value'] = round(Decimal(opening_stock_qty) * Decimal(item['item_unit_rate']), 2) if opening_stock_qty > 0 else 0
					item['price'] = "%s /%s" % (Decimal(item['item_unit_rate']), unit_name) if item['price'] > 0 else 0
			if len(items) > 0:
				message = "%s Items Exported" % len(items)
				self.updateExportDetails(enterprise_id=enterprise_id, unique_id=unique_id, message=message, level='Info')
			return items
		except Exception as e:
			logger.exception("Error from Getting Item Master %s" % e.message)
		return None

	def getInvoices(
			self, enterprise_id=None, from_date=None, to_date=None, invoice_type=None,
			is_accounting_voucher=None, dc_or_invoice=None, unique_id=None):
		try:
			_vouchers = []
			from_date = from_date + ' 00:00:00'
			to_date = to_date + ' 23:59:00'
			invoices = self.dao.db_session.query(Invoice).filter(
				Invoice.enterprise_id == enterprise_id, Invoice.approved_on >= from_date,
				Invoice.approved_on <= to_date, Invoice.type.in_(invoice_type),
				Invoice.status == Invoice.STATUS_APPROVED).order_by(Invoice.approved_on).all()
			invoice_service = InvoiceService()
			for invoice in invoices:
				if invoice.ledger_bill:
					self.getLedgerBillLinkedVoucher(invoice=invoice, _vouchers=_vouchers, module='issue', dc_or_invoice=dc_or_invoice)
					if invoice.ledger_bill.voucher_id not in self.voucher_ids:
						self.voucher_ids.append(invoice.ledger_bill.voucher_id)
				else:
					self.constructVoucher(
						invoice=invoice, _vouchers=_vouchers, is_accounting_voucher=is_accounting_voucher,
						invoice_type=Invoice.TYPE_KEYS[invoice.type], module='issue', dc_or_invoice=dc_or_invoice,
						invoice_service=invoice_service)
			if len(self.invalid_vouchers) > 0:
				self.invalid_vouchers_status = False
				return None

			elif len(_vouchers) > 0 and self.invalid_vouchers_status:
				message = ""
				if dc_or_invoice == 1 or dc_or_invoice is None:
					message = "%s Delivery Notes Exported" % len(_vouchers)
				elif dc_or_invoice == 2:
					message = "%s Sales Vouchers Exported" % len(_vouchers)

				self.updateExportDetails(enterprise_id=enterprise_id, unique_id=unique_id, message=message, level='Info')
				return _vouchers
		except Exception as e:
			logger.exception("Error from Getting Vouchers %s" % e.message)
		return None

	def getReceipts(
			self, enterprise_id=None, from_date=None, to_date=None, received_against=None,
			is_accounting_voucher=None, dc_or_invoice=None, unique_id=None):
		try:
			_vouchers = []
			from_date = from_date + ' 00:00:00'
			to_date = to_date + ' 23:59:00'
			receipts = self.dao.db_session.query(Receipt).filter(
				Receipt.enterprise_id == enterprise_id, Receipt.approved_on >= from_date,
				Receipt.approved_on <= to_date, Receipt.received_against.in_(received_against),
				Receipt.invoice_type == dc_or_invoice,
				Receipt.status.notin_(
					[Receipt.STATUS_DRAFT, Receipt.STATUS_GRN_RETURNED, Receipt.STATUS_REJECTED])
			).order_by(Receipt.approved_on).all()
			invoice_service = InvoiceService()
			for receipt in receipts:
				if receipt.ledger_bill:
					self.getLedgerBillLinkedVoucher(
						invoice=receipt, _vouchers=_vouchers, module='receipt', dc_or_invoice=dc_or_invoice)
					if receipt.voucher_id not in self.voucher_ids:
						self.voucher_ids.append(receipt.voucher_id)
				else:
					self.constructVoucher(
						invoice=receipt, _vouchers=_vouchers, is_accounting_voucher=is_accounting_voucher,
						dc_or_invoice=dc_or_invoice, module='receipt', invoice_service=invoice_service)
			if len(self.invalid_vouchers) > 0:
				self.invalid_vouchers_status = False
				return None
			elif len(_vouchers) > 0 and self.invalid_vouchers_status:
				message = ""
				if dc_or_invoice == 1 or dc_or_invoice == '0':
					message = "%s Receipt Notes Exported" % len(_vouchers)
				elif dc_or_invoice == 2:
					message = "%s Purchase Vouchers Exported" % len(_vouchers)
				self.updateExportDetails(enterprise_id=enterprise_id, unique_id=unique_id, message=message, level='Info')
				return _vouchers
		except Exception as e:
			logger.exception("Error from Getting Vouchers %s" % e.message)
		return None

	def getLedgerBillLinkedVoucher(self, invoice=None, _vouchers=None, module=None, dc_or_invoice=None):
		try:
			voucher = invoice.ledger_bill.original_settlement.voucher if module == 'issue' else invoice.voucher
			if voucher:
				_voucher = self.getVoucherHeaderDetails(
					voucher=voucher, module=module, dc_or_invoice=dc_or_invoice,
					received_against=invoice.received_against if module == 'receipt' else None)
				_voucher['voucher_no'] = invoice.invoice_no if module == 'issue' else invoice.receipt_code
				_voucher['view'] = "Invoice Voucher View"
				_voucher['party_name'] = ""
				_voucher['party_ledger_name'] = ""
				if invoice.ledger_bill.ledger:
					_voucher['party_name'] = self.getLedgerName(ledger_id=invoice.ledger_bill.ledger.id)
					_voucher['party_ledger_name'] = _voucher['party_name']
				if voucher and voucher.isValidParticulars():
					if module == 'issue':
						if invoice.sale_account_ledger:
							account_ledger_name = invoice.sale_account_ledger.name
						else:
							account_ledger_name = default_accounts_group['Sales Account']
					elif module == 'receipt':
						if invoice.purchase_account_ledger:
							account_ledger_name = invoice.purchase_account_ledger.name
						else:
							account_ledger_name = default_accounts_group['Purchase Account']
						if invoice.received_against == 'Sales Return':
							sales_ledgers = populateAccountLedgerChoices(
								enterprise_id=invoice.enterprise_id, group_names=(SALES_ACCOUNT_GROUP_NAME,),
								with_group_name=False)
							account_ledger_name = sales_ledgers[0][1]

					_vouchers.append(self.getVoucherParticulars(
						voucher=voucher, _voucher=_voucher, account_ledger_name=account_ledger_name))
					self.getItemParticulars(invoice=invoice, _voucher=_voucher, module=module, dc_or_invoice=dc_or_invoice)
				else:
					invalid_voucher = voucher.getCode()
					if invalid_voucher not in self.invalid_vouchers:
						self.invalid_vouchers.append(invalid_voucher)

				return _voucher
		except Exception as e:
			logger.exception("Error from Getting Vouchers %s" % e.message)
		return None

	def constructVoucher(
			self, invoice=None, _vouchers=None, is_accounting_voucher=None, invoice_type=None, module=None,
			dc_or_invoice=None, invoice_service=None):
		try:
			if module and dc_or_invoice and module == 'receipt':
				vou_type_name = self.getVoucherTypeName(
					module=module, dc_or_invoice=dc_or_invoice,
					received_against=invoice.received_against if module == 'receipt' else None)
			else:
				vou_type_name = voucher_type_name[invoice_type]
			_voucher = {
				'type': vou_type_name,
				'voucher_no': invoice.invoice_no if module == 'issue' else invoice.receipt_code,
				'voucher_date': invoice.approved_on, 'date': invoice.approved_on}

			_voucher['particulars'] = []
			if module == 'issue':
				if is_accounting_voucher and invoice.customer:
					invoice_service.generateRequiredLedgers(
						invoice=invoice, db_session=invoice_service.invoice_dao.db_session)
					if invoice.type == 'JDC':
						_voucher['party_name'] = self.getLedgerName(
							ledger_id=invoice.customer.supplier_ledger_map.ledger.id)
					else:
						_voucher['party_name'] = self.getLedgerName(
							ledger_id=invoice.customer.customer_ledger_map.ledger.id)
					_voucher['party_ledger_name'] = _voucher['party_name']
					_voucher['view'] = "Invoice Voucher View"
				else:
					_voucher['view'] = "Consumption Voucher View"
			else:
				if is_accounting_voucher and invoice.supplier:
					invoice_service.generateRequiredLedgersForSupplier(
						receipt=invoice, db_session=invoice_service.invoice_dao.db_session)
					_voucher['party_name'] = self.getLedgerName(
						ledger_id=invoice.supplier.supplier_ledger_map.ledger.id)
					_voucher['party_ledger_name'] = _voucher['party_name']
					_voucher['view'] = "Invoice Voucher View"
				else:
					_voucher['view'] = "Consumption Voucher View"

			narration = "[%s (%s)]" % (invoice.getCode(), invoice.approved_on.strftime("%d-%b-%Y %H:%M:%S"))
			if module == 'issue':
				items = self.dao.db_session.query(InvoiceMaterial).filter(
					InvoiceMaterial.invoice_id == invoice.id).order_by(
					InvoiceMaterial.quantity.desc(), InvoiceMaterial.rate.desc()).limit(2)
			else:
				items = self.dao.db_session.query(ReceiptMaterial).filter(
					ReceiptMaterial.receipt_no == invoice.receipt_no).order_by(
					ReceiptMaterial.quantity.desc(), ReceiptMaterial.rate.desc()).limit(2)
			for item in items:
				narration = "%s %s %s %s @ %s," % (
					narration, item.item.name, item.quantity, item.item.unit, round(item.rate, 2))
			if module == 'issue':
				narration = "%s TO: %s" % (narration, invoice.customer.name if invoice.customer else invoice.issued_to)
			else:
				narration = "%s TO: %s" % (narration, invoice.supplier.name if invoice.supplier else "")
			_voucher['narration'] = narration
			if is_accounting_voucher:
				_voucher['particulars'] = self.__constructVoucherParticulars(
					invoice=invoice, db_session=self.dao.db_session, module=module)
			_voucher['item_particulars'] = []
			self.getItemParticulars(invoice=invoice, _voucher=_voucher, module=module, dc_or_invoice=dc_or_invoice)
			_vouchers.append(_voucher)

		except Exception as e:
			logger.exception("Error from Getting Vouchers %s" % e.message)
		return None

	def __constructVoucherParticulars(
			self, invoice=None, db_session=None, for_rejection=False, module=None):
		try:
			voucher_particulars = []
			if module == 'issue':
				for _charge in invoice.charges:
					_charge_ledger = db_session.query(Ledger).filter(
						Ledger.group_id == OTHER_DIRECT_INCOME_GROUP_ID, Ledger.name == _charge.item_name,
						Ledger.enterprise_id == invoice.enterprise_id).first()
					_charge_voucher_value = Decimal(round(_charge.rate * (100 - _charge.discount) / 100, 2)) * Decimal(
						invoice.currency_conversion_rate)
					if _charge_voucher_value > 0:
						ledger_name = self.getLedgerName(ledger_id=_charge_ledger.id)
						_particular = {
							'is_debit': 'No' if for_rejection else 'Yes', 'ledger_name': ledger_name,
							'amount': round(_charge_voucher_value, 2), 'party_ledger': 'No', 'settlements': []
						}
						voucher_particulars.append(_particular)
			if module == 'receipt':
				if invoice.packing_charges > 0:
					package_ledger = db_session.query(Ledger).filter(
						Ledger.group_id == OTHER_DIRECT_EXPENSES_GROUP_ID, Ledger.enterprise_id == invoice.enterprise_id,
						Ledger.name == PACKING_ACCOUNT_NAME).first()
					ledger_name = self.getLedgerName(ledger_id=package_ledger.id)
					_particular = {
						'is_debit': 'No' if for_rejection else 'Yes', 'ledger_name': ledger_name,
						'amount': round(invoice.packing_charges * invoice.currency_conversion_rate, 2) * -1,
						'party_ledger': 'No', 'settlements': []
					}
					voucher_particulars.append(_particular)
				if invoice.transport_charges > 0:
					transport_ledger = db_session.query(Ledger).filter(
						Ledger.group_id == OTHER_DIRECT_EXPENSES_GROUP_ID, Ledger.enterprise_id == invoice.enterprise_id,
						Ledger.name == TRANSPORT_ACCOUNT_NAME).first()
					ledger_name = self.getLedgerName(ledger_id=transport_ledger.id)
					_particular = {
						'is_debit': 'No' if for_rejection else 'Yes', 'ledger_name': ledger_name,
						'amount': round(invoice.transport_charges * invoice.currency_conversion_rate, 2) * -1,
						'party_ledger': 'No', 'settlements': []
					}
					voucher_particulars.append(_particular)
				if invoice.other_charges > 0:
					others_ledger = db_session.query(Ledger).filter(
						Ledger.group_id == OTHER_INDIRECT_EXPENSES_GROUP_ID, Ledger.enterprise_id == invoice.enterprise_id,
						Ledger.name == OTHER_CHARGES_ACCOUNT_NAME).first()
					ledger_name = self.getLedgerName(ledger_id=others_ledger.id)
					_particular = {
						'is_debit': 'No' if for_rejection else 'Yes', 'ledger_name': ledger_name,
						'amount': round(invoice.other_charges * invoice.currency_conversion_rate, 2) * -1,
						'party_ledger': 'No', 'settlements': []
					}
					voucher_particulars.append(_particular)
				if invoice.round_off != 0:
					round_off_ledger = db_session.query(Ledger).filter(
						Ledger.group_id == OTHER_INDIRECT_EXPENSES_GROUP_ID, Ledger.enterprise_id == invoice.enterprise_id,
						Ledger.name == ROUND_OFF_ACCOUNT_NAME).first()
					ledger_name = self.getLedgerName(ledger_id=round_off_ledger.id)
					_particular = {
						'is_debit': 'No' if for_rejection else 'Yes', 'ledger_name': ledger_name,
						'amount': round(invoice.round_off * invoice.currency_conversion_rate, 2) * -1,
						'party_ledger': 'No', 'settlements': []
					}
					voucher_particulars.append(_particular)

			tax_values = invoice.getTaxValues()
			for tax in invoice.getConsolidatedTaxList():
				tax_ledger_id = tax.getOutputLedgerId() if module == 'issue' else tax.getInputLedgerId()
				tax_value = Decimal(tax_values[tax.code]) * Decimal(invoice.currency_conversion_rate) if tax_values else 0
				ledger_name = self.getLedgerName(ledger_id=tax_ledger_id)
				if module == 'issue':
					is_debit = 'Yes' if for_rejection else 'No'
					amount = round(tax_value, 2)
				else:
					is_debit = 'No' if for_rejection else 'Yes'
					amount = round(tax_value, 2) * -1
				_particular = {
					'is_debit': is_debit, 'ledger_name': ledger_name,
					'amount': amount, 'party_ledger': 'No', 'settlements': []
				}
				voucher_particulars.append(_particular)

			ledger_name = self.getLedgerName(
				ledger_id=invoice.customer.getCustomerLedger().id if module == 'issue' else invoice.supplier.getSupplierLedger().id)

			if module == 'issue':
				is_debit = 'Yes' if not for_rejection else 'No'
				grand_total = round(Decimal(invoice.grand_total) * Decimal(invoice.currency_conversion_rate), 2) * -1
			else:
				is_debit = 'No' if not for_rejection else 'Yes'
				grand_total = round(Decimal(invoice.invoice_value) * Decimal(invoice.currency_conversion_rate), 2)
			_particular = {
				'is_debit': is_debit, 'ledger_name': ledger_name,
				'amount': grand_total,
				'party_ledger': 'Yes', 'settlements': []
			}
			_particular['settlements'].append({
				'bill_no': invoice.getCode(), 'amount': grand_total, 'bill_type': 'New Ref'})
			voucher_particulars.append(_particular)

			if invoice.round_off != 0:
				round_off_ledger = db_session.query(Ledger).filter(
					Ledger.group_id == OTHER_INDIRECT_EXPENSES_GROUP_ID, Ledger.enterprise_id == invoice.enterprise_id,
					Ledger.name == "Round-off").first()
				ledger_name = self.getLedgerName(ledger_id=round_off_ledger.id)
				if module == 'issue':
					is_debit = 'Yes' if ((invoice.round_off > 0) == for_rejection) else 'No'
					amount = abs(round(Decimal(invoice.round_off) * Decimal(invoice.currency_conversion_rate), 2))
				else:
					is_debit = 'No' if ((invoice.round_off > 0) == for_rejection) else 'Yes'
					amount = abs(round(Decimal(invoice.round_off) * Decimal(invoice.currency_conversion_rate), 2)) * -1
				_particular = {
					'is_debit': is_debit, 'ledger_name': ledger_name,
					'amount': amount, 'party_ledger': 'No', 'settlements': []
				}
			voucher_particulars = sorted(voucher_particulars, key=lambda i: i['party_ledger'], reverse=True)
			return voucher_particulars
		except Exception as e:
			logger.exception("Error from Construct Vouchers particulars %s" % e.message)
		return None

	def getLedgerName(self, ledger_id=None):
		try:
			ledger = self.dao.db_session.query(Ledger).filter(Ledger.id == ledger_id).first()
			if ledger.group.name in default_accounts_group:
				group_name = default_accounts_group[ledger.group.name]
			else:
				group_name = ledger.group.name
			ledger_name = "%s-%s" % (ledger.name, group_name)
			return ledger_name
		except Exception as e:
			logger.exception("Error from get ledger name %s" % e.message)
			raise e

	def getItemParticulars(self, invoice=None, _voucher=None, module=None, dc_or_invoice=None):
		try:
			_voucher['item_particulars'] = []
			for particular in invoice.items:
				amount, inventory_amount, qty = self.getParticularAmount(particular=particular, module=module)
				unit_name = self.manipulateUnitName(unit=particular.item.unit.unit_name)
				item_name = '%s-%s' % (
					particular.item.name,
					particular.item.drawing_no) if particular.item.drawing_no else particular.item.name
				if particular.make_id != DEFAULT_MAKE_ID:
					part_no = helper.getMakePartNumber(
						enterprise_id=particular.enterprise_id, item_id=particular.item_id, make_id=particular.make_id)
					material_make = "[%s%s]" % (particular.make.__repr__(), (" - %s" % part_no) if part_no else "")
					item_name = '%s %s' % (item_name, material_make)
				_particular = {
					'item_name': item_name,
					'is_deem_positive': 'No' if module == 'issue' else 'Yes',
					'rate': "%s /%s" % (particular.rate, unit_name) if particular.rate else 0,
					'qty': qty, 'bill_qty': qty, 'discount': round(particular.discount, 2) if particular.discount else "",
					'amount': round(inventory_amount, 2),
					'batch_wise_item_particulars': [], 'account_wise_allocations': []
				}
				order_no = ""
				dc_no = ""
				if module == 'issue':
					account_ledger = particular.invoice.sale_account_ledger
					order_no = particular.inv_oa.getInternalCode() if particular.inv_oa else ""
					if dc_or_invoice == 1:
						dc_no = particular.invoice.getCode() if particular.invoice else ""
					elif dc_or_invoice == 2:
						if particular.delivered_dc:
							dc_no = particular.delivered_dc.getInternalCode() if particular.delivered_dc else ""
				else:
					account_ledger = particular.receipt.purchase_account_ledger
					order_no = particular.po.getInternalCode() if particular.po else ""
					if dc_or_invoice == 1:
						dc_no = particular.receipt.getCode() if particular.receipt else ""
					elif dc_or_invoice == 2:
						if particular.received_grn_id:
							dc_no = particular.receipt_dc.getCode() if particular.receipt_dc else ""
				_particular['batch_wise_item_particulars'].append({
					'order_no': order_no, 'dc_no': dc_no, 'amount': round(inventory_amount, 2), 'qty': qty, 'bill_qty': qty})
				ledger_name = ""
				if account_ledger:
					if account_ledger.group.name in default_accounts_group:
						group_name = default_accounts_group[account_ledger.group.name]
						ledger_name = "%s-%s" % (account_ledger.name, group_name)
					else:
						ledger_name = "%s-%s" % (account_ledger.name, account_ledger.group.name)
				else:
					if module == 'receipt' and particular.receipt.received_against != 'Sales Return':
						logger.info("Receipt:%s" % [particular.receipt, module])
						purchase_ledgers = populateAccountLedgerChoices(
							enterprise_id=particular.receipt.enterprise_id,
							group_names=(PURCHASE_ACCOUNT_GROUP_NAME,), with_group_name=False)
						account_ledger_name = purchase_ledgers[0][1]
						group_name = default_accounts_group['Purchase Account']
					else:
						sales_ledgers = populateAccountLedgerChoices(
							enterprise_id=particular.invoice.enterprise_id,
							group_names=(SALES_ACCOUNT_GROUP_NAME,), with_group_name=False)
						account_ledger_name = sales_ledgers[0][1]
						group_name = default_accounts_group['Sales Account']
					ledger_name = "%s-%s" % (account_ledger_name, group_name) if account_ledger_name else ""

				if module == 'receipt' and particular.receipt.received_against == 'Sales Return':
					sales_ledgers = populateAccountLedgerChoices(
						enterprise_id=particular.receipt.enterprise_id,
						group_names=(SALES_ACCOUNT_GROUP_NAME,), with_group_name=False)
					account_ledger_name = sales_ledgers[0][1]
					group_name = default_accounts_group['Sales Account']
					ledger_name = "%s-%s" % (account_ledger_name, group_name) if account_ledger_name else ""

				_particular['account_wise_allocations'].append({
					'ledger_name': ledger_name,
					'is_debit': "No" if module == 'issue' else "Yes", 'amount': round(amount, 2)})
				_voucher['item_particulars'].append(_particular)
			return _voucher
		except Exception as e:
			logger.exception("Error from Getting Vouchers particulars %s" % e.message)
		return None

	def getParticularAmount(self, particular=None, module=None):
		try:
			if module == 'receipt':
				conversion_rate = particular.receipt.currency_conversion_rate if particular.receipt else 1
			else:
				conversion_rate = particular.invoice.currency_conversion_rate if particular.invoice else 1

			if particular.discount != 0 and particular.quantity and particular.rate:
				amount = (particular.quantity * particular.rate * (100 - particular.discount) / 100) * conversion_rate
				if module == 'receipt':
					inventory_amount = (particular.accepted_qty * particular.rate * (100 - particular.discount) / 100) * conversion_rate
				else:
					inventory_amount = amount
			elif particular.discount == 0 and particular.quantity and particular.rate:
				amount = (particular.quantity * particular.rate) * conversion_rate
				if module == 'receipt':
					inventory_amount = (particular.accepted_qty * particular.rate) * conversion_rate
				else:
					inventory_amount = amount
			else:
				amount = 0
				inventory_amount = 0
			amount = amount if module == 'issue' else amount * -1
			unit_name = self.manipulateUnitName(unit=particular.item.unit.unit_name)
			if module == 'receipt':
				qty = "%s %s" % (
					particular.accepted_qty, unit_name) if particular.accepted_qty else 0
			else:
				qty = "%s %s" % (
					particular.quantity, unit_name) if particular.quantity else 0
			return amount, inventory_amount, qty
		except Exception as e:
			logger.info("Error grom getting particular amount %s" % e.message)
		return None

	def generateTallyXML(
			self, enterprise_id=None, from_date=None, to_date=None, unique_id=None,
			is_inventory_transaction=None, is_closing_balance_as_opening=None):
		try:
			if is_inventory_transaction == 'true':
				data = {
					'1.unit': self.getUnitDetails(enterprise_id=enterprise_id, unique_id=unique_id),
					'2.item_category': self.getItemCategory(enterprise_id=enterprise_id, unique_id=unique_id),
					'3.item_master': self.getItemMaster(
						enterprise_id=enterprise_id, unique_id=unique_id,
						is_closing_balance_as_opening=is_closing_balance_as_opening, from_date=from_date),
					'4.account_group': self.getAccountGroups(enterprise_id=enterprise_id, unique_id=unique_id),
					'5.account_ledger': self.getAccountLedgers(
						enterprise_id=enterprise_id, unique_id=unique_id,
						is_closing_balance_as_opening=is_closing_balance_as_opening, from_date=from_date),
					'6.voucher': [
						self.getInvoices(
							enterprise_id=enterprise_id, from_date=from_date, to_date=to_date,
							invoice_type=Invoice.TYPES['dc'], is_accounting_voucher=True, dc_or_invoice=1,
							unique_id=unique_id),
						self.getReceipts(
							enterprise_id=enterprise_id, from_date=from_date, to_date=to_date,
							received_against=('Purchase Order', 'Job Work', 'Delivery Challan', 'Sales Return'),
							is_accounting_voucher=True, dc_or_invoice=1, unique_id=unique_id),
						self.getReceipts(
							enterprise_id=enterprise_id, from_date=from_date, to_date=to_date,
							received_against=('Job In',),
							is_accounting_voucher=True, dc_or_invoice=1, unique_id=unique_id),
						self.getReceipts(
							enterprise_id=enterprise_id, from_date=from_date, to_date=to_date,
							received_against=('Others',),
							is_accounting_voucher=True, dc_or_invoice='0', unique_id=unique_id),
						self.getVouchers(
							enterprise_id=enterprise_id, from_date=from_date, to_date=to_date, all_ledger_entries=True,
							unique_id=unique_id)],
					'9.accounting_voucher': [
						self.getInvoices(
							enterprise_id=enterprise_id, from_date=from_date, to_date=to_date,
							invoice_type=Invoice.TYPES['sales'], is_accounting_voucher=True, dc_or_invoice=2,
							unique_id=unique_id),
						self.getReceipts(
							enterprise_id=enterprise_id, from_date=from_date, to_date=to_date,
							received_against=('Purchase Order', 'Job Work', 'Delivery Challan', 'Sales Return'),
							is_accounting_voucher=True, dc_or_invoice=2, unique_id=unique_id),
						self.getVouchers(
							enterprise_id=enterprise_id, from_date=from_date, to_date=to_date, all_ledger_entries=False,
							unique_id=unique_id)],
					'7.stock_issue': self.getInvoices(
						enterprise_id=enterprise_id, from_date=from_date, to_date=to_date,
						invoice_type=Invoice.TYPES['internal'],
						is_accounting_voucher=False, unique_id=unique_id),
					'8.stock_receipt': self.getReceipts(
						enterprise_id=enterprise_id, from_date=from_date, to_date=to_date,
						received_against=('Issues',), dc_or_invoice='0',
						is_accounting_voucher=False, unique_id=unique_id), }
			else:
				data = {
					'4.account_group': self.getAccountGroups(enterprise_id=enterprise_id, unique_id=unique_id),
					'5.account_ledger': self.getAccountLedgers(
						enterprise_id=enterprise_id, unique_id=unique_id,
						is_closing_balance_as_opening=is_closing_balance_as_opening, from_date=from_date),
					'6.voucher': self.getVouchers(
						enterprise_id=enterprise_id, from_date=from_date, to_date=to_date, all_ledger_entries=True,
						unique_id=unique_id),
					'9.accounting_voucher': self.getVouchers(
						enterprise_id=enterprise_id, from_date=from_date, to_date=to_date, all_ledger_entries=False,
						unique_id=unique_id)
				}
			if self.invalid_vouchers_status:
				root, tally_message = self.getGenerateRoot()
				data['5.account_ledger'] = self.updateAccountLedgers(
					enterprise_id=enterprise_id,
					is_closing_balance_as_opening=is_closing_balance_as_opening, from_date=from_date,
					existing_account_ledger_dict=data['5.account_ledger'])
				for key in sorted(data.keys()):
					self.generateNodeXml(
						entities=data[key], parent_node=tally_message,
						mapping_dict=tm_mapping_master[key])
				self.saveXMLFile(enterprise_id=enterprise_id, tree=xml_et.ElementTree(root), unique_id=unique_id)
				return True
			else:
				if len(self.invalid_vouchers) > 0:
					voucher_code = ""
					for code in self.invalid_vouchers:
						voucher_code += "%s," % code
					message = "%s Vouchers debit and credit values are not equal" % voucher_code
					self.updateExportDetails(enterprise_id=enterprise_id, unique_id=unique_id, message=message, level='Error')
					collection = 'export_details'
					db = MongoDbConnect[collection]
					db.find_one_and_update({'enterprise_id': enterprise_id, '_id': ObjectId(unique_id)}, {'$set': {
						'status': 'Failed'}})
				return None
		except Exception as e:
			logger.exception("Error from Generate XML Creation %s" % e.message)
		return None

	def saveXMLFile(self, enterprise_id=None, tree=None, unique_id=None):
		try:
			filename = "%s.%s" % (uuid.uuid4(), 'xml')
			temp_file = "/site_media/tmp/%s" % filename
			with open(getAbsolutePath(temp_file), "wb") as files:
				tree.write(files)
			doc = etree.parse(getAbsolutePath(temp_file))
			with open(getAbsolutePath(temp_file), "w") as f:
				f.write(etree.tostring(doc, encoding='UTF-8', xml_declaration=True, pretty_print=True))
				f.close()
			self.upload_blob(
				source_file_name="%s/%s" % (TEMP_DIR, filename),
				destination_blob_name="{enterprise_id}/{filename}".format(enterprise_id=enterprise_id, filename=filename))
			collection = 'export_details'
			db = MongoDbConnect[collection]
			db.find_one_and_update({'enterprise_id': enterprise_id, '_id': ObjectId(unique_id)}, {'$set': {
				'gcs_key': filename}})
			os.remove(getAbsolutePath(temp_file))
			logger.info("Removed temp file..  %s" % temp_file)
		except Exception as e:
			logger.exception("Error for Save XML File:%s" % e.message)
		return None

	def upload_blob(self, source_file_name=None, destination_blob_name=None):
		"""Uploads a file to the bucket."""
		blob = bucket.blob(destination_blob_name)
		blob.upload_from_filename(source_file_name)
		blob.content_disposition = 'attachment'
		blob.patch()
		return True

	def getGenerateRoot(self):
		try:
			root = xml_et.Element(tm_header_details['envelope']['tally'])
			header = xml_et.Element(tm_header_details['header']['tally'])
			root.append(header)
			tally_request = xml_et.SubElement(header, tm_header_details['tally_request']['tally'])
			tally_request.text = tm_header_details['tally_request_data']['tally']
			body = xml_et.Element(tm_header_details['body']['tally'])
			import_data = xml_et.SubElement(body, tm_header_details['import_data']['tally'])
			request_description = xml_et.SubElement(import_data, tm_header_details['request_desc']['tally'])
			root.append(body)
			report_name = xml_et.SubElement(request_description, tm_header_details['report_name']['tally'])
			report_name.text = tm_header_details['report_name_data']['tally']
			static_variables = xml_et.SubElement(request_description, tm_header_details['static_variables']['tally'])
			current_company = xml_et.SubElement(static_variables, tm_header_details['current_company']['tally'])
			current_company.text = tm_header_details['company_name']['tally']
			request_data = xml_et.SubElement(import_data, tm_header_details['request_data']['tally'])
			tally_message = xml_et.SubElement(request_data, tm_header_details['tally_message']['tally'], UDF='TallyUDF')
			return root, tally_message
		except Exception as e:
			logger.exception("Error from generate root function %s" % e.message)
		return None

	def manipulateData(self, value=None, _map=None):
		try:
			if 'manipulate' in _map:
				if _map['manipulate'] == "string":
					value = re.sub('[^A-Za-z0-9]+', '', value)
					value = re.sub(r'[0-9]', "", value)
			elif type(value) == datetime:
				value = value.strftime('%Y%m%d')
			elif type(value) == date:
				value = value.strftime('%Y%m%d')
			elif type(value) in (int, float, Decimal, long):
				value = str(value)
			elif value:
				value = str(value.encode("ascii", "ignore"))
			return value
		except Exception as e:
			logger.exception("Error from manipulate data function %s" % e.message)
		return None

	def manipulateUnitName(self, unit=None):
		try:
			unit = re.sub('[^A-Za-z0-9]+', '', unit)
			unit = re.sub(r'[0-9]', "", unit)
			return unit
		except Exception as e:
			logger.exception("Error from manipulate unit name function %s" % e.message)
		return None

	def validateCriteria(self, _map=None, key=None, value=None, source=None):
		try:
			criteria = _map[key]['criteria']
			if criteria['key'] == "equal":
				existing_map = _map[criteria['value']]
				existing_value = self.manipulateData(value=source[existing_map['xserp']], _map=existing_map)
				if existing_value.lower() == value.lower():
					if criteria['action'] == 'append':
						which_map = _map[criteria['which']]
						which = self.manipulateData(value=source[which_map['xserp']], _map=which_map)
						value = "%s %s" % (value, which)
			return value
		except Exception as e:
			logger.exception("Error from validate criteria function %s" % e.message)
		return None

	def getLanguageList(self, node_object_data=None, source=None, mapping_dict=None):
		try:
			parent_node = xml_et.SubElement(node_object_data, "LANGUAGENAME.LIST")
			node = xml_et.SubElement(xml_et.SubElement(parent_node, "NAME.LIST"), "NAME")
			value = str(source[mapping_dict['header_param']['name']['xserp']].encode("ascii", "ignore"))
			node.text = value
			node = xml_et.SubElement(parent_node, "LANGUAGEID")
			node.text = "1033"
		except Exception as e:
			logger.exception("Error from Get Language List %s" % e.message)
		return None

	def generateNodeXml(self, entities=None, parent_node=None, mapping_dict=None):
		try:
			for entity in entities:
				if not entity:
					continue
				if type(entity) is list:
					self.generateNodeXml(entities=entity, parent_node=parent_node, mapping_dict=mapping_dict)
					continue
				erp_node_name = mapping_dict['model']['tally']
				if "erp.models" in str(type(entity)):  # if type(entity) == Base:
					source = entity.__dict__
				elif type(entity) is dict:
					source = entity
				else:
					source = entity._asdict()
				header_params = self.generateHeaderParams(mapping_dict=mapping_dict, source=source)
				current_node = xml_et.SubElement(parent_node, erp_node_name, **header_params)
				self.generateItems(mapping_dict=mapping_dict, parent_node=current_node, source=source)
				if 'relationship' in mapping_dict:
					relatives = mapping_dict['relationship']
					for key in relatives:
						_map = relatives[key]
						if _map['model']['xserp'] in source:
							child_source = source[_map['model']['xserp']]
						else:
							child_source = entity.__getattribute__(_map['model']['xserp'])
						self.generateNodeXml(entities=child_source, parent_node=current_node, mapping_dict=_map)
		except Exception as e:
			logger.exception("Error from Generate Node Xml %s" % e.message)

	def generateHeaderParams(self, mapping_dict=None, source=None):
		try:
			header_params = {}
			if 'header_param' in mapping_dict:
				_map = mapping_dict['header_param']
				for key in _map:
					t, x = _map[key]['tally'], _map[key]['xserp']
					if type(x) is str and x in source:
						header_params[t] = self.manipulateData(value=source[x], _map=_map[key])
			return header_params
		except Exception as e:
			logger.exception("Error from Generate Header Params %s" % e.message)
		return None

	def generateItems(self, mapping_dict=None, parent_node=None, source=None):
		try:
			_map = mapping_dict['fields']
			for key in sorted(_map):
				xserp_key = _map[key]['xserp']
				unique = None
				if 'unique' in _map[key]:
					unique = _map[key]['unique']
				# 	TODO unique check required with existing xml
				if type(xserp_key) is str and xserp_key in source:
					value = self.manipulateData(value=source[xserp_key], _map=_map[key])
					if 'criteria' in _map[key]:
						value = self.validateCriteria(_map=_map, key=key, value=value, source=source)
					node = xml_et.SubElement(parent_node, _map[key]['tally'])
					node.text = value
			if 'defaults' in _map:
				for key in _map['defaults']['tally']:
					node = xml_et.SubElement(parent_node, key)
					node.text = _map['defaults']['tally'][key]
			if 'namelist' in _map and _map['namelist']['tally'] == 'Yes':
				self.getLanguageList(node_object_data=parent_node, source=source, mapping_dict=mapping_dict)
		except Exception as e:
			logger.exception("Error from Generate Items %s" % e.message)
		return None

	def constructOptionTree(self, root=None, enterprise_id=None):
		try:
			account_groups = []
			inventory_account_group_dict = self.getInventoryAccountGroups(enterprise_id=enterprise_id)
			if root:
				if root.parent and root.id != 0 and root.name not in default_accounts_group_dict:
					parent_name = root.parent.name if root.parent_id != 0 else ""
					if root.name in default_accounts_group_parent_name_dict:
						parent_name = default_accounts_group_parent_name_dict[root.name]
					if root.name in inventory_account_group_dict:
						sub_ledger = 'Yes'
						affect_stock = 'Yes'
					else:
						sub_ledger = 'No'
						affect_stock = 'No'
					account_groups.append({
						'name': root.name, 'parent_name': parent_name if parent_name else "",
						'billable': 'Yes' if root.billable else 'No',
						'sub_ledger': sub_ledger, 'affect_stock': affect_stock
						})
				for child in root.children:
					if len(child.children) > 0:
						account_groups.extend(self.constructOptionTree(root=child, enterprise_id=enterprise_id))
					else:
						parent_name = None
						if child.parent and child.id != 0 and child.name not in default_accounts_group_dict:
							parent_name = child.parent.name if child.parent_id != 0 else ""
							if child.name in default_accounts_group_parent_name_dict:
								parent_name = default_accounts_group_parent_name_dict[child.name]
						if child.name in inventory_account_group_dict:
							sub_ledger = 'Yes'
							affect_stock = 'Yes'
						else:
							sub_ledger = 'No'
							affect_stock = 'No'
						account_groups.append({
							"name": child.name, "parent_name": parent_name if parent_name else "",
							'billable': 'Yes' if root.billable else 'No',
							'sub_ledger': sub_ledger, 'affect_stock': affect_stock
							})
				return account_groups
		except Exception as e:
			logger.exception("Error from Construct Option Tree for Account groups %s" % e.message)
		return None

	def fetchExportDetails(self, enterprise_id=None):
		try:
			result = []
			collection = 'export_details'
			db = MongoDbConnect[collection]
			query = db.find({'enterprise_id': enterprise_id}).sort('export_request_date', pymongo.DESCENDING)
			for fetched_data in query:
				fetch_data = {}
				fetch_data['initiated_on'] = fetched_data[
					'export_request_date'] if 'export_request_date' in fetched_data else ""
				fetch_data['from_date'] = fetched_data['from_date'].strftime(
					"%Y-%m-%d") if 'from_date' in fetched_data else ""
				fetch_data['to_date'] = fetched_data['to_date'].strftime(
					"%Y-%m-%d") if 'to_date' in fetched_data else ""
				fetch_data['initiated_by'] = fetched_data['user_name'] if 'user_name' in fetched_data else ""
				fetch_data['status'] = fetched_data['status'] if 'status' in fetched_data else ""
				fetch_data['id'] = str(fetched_data['_id']) if '_id' in fetched_data else ""
				fetch_data['is_inventory_transaction'] = fetched_data[
					'is_inventory_transaction'] if 'is_inventory_transaction' in fetched_data else ''
				fetch_data['is_closing_balance_as_opening'] = fetched_data[
					'is_closing_balance_as_opening'] if 'is_closing_balance_as_opening' in fetched_data else ''
				fetch_data['gcs_key'] = fetched_data['gcs_key'] if 'gcs_key' in fetched_data else ""
				fetch_data['download_file_name'] = "{url}/{enterprise_id}/{filename}".format(
					url=GCS_PUBLIC_URL, enterprise_id=enterprise_id, filename=fetch_data['gcs_key'])
				result.append(fetch_data)
			return result
		except Exception as e:
			logger.info("Error for fetch export details:%s" % e.message)
		return None

	def updateExportDetails(self, enterprise_id=None, unique_id=None, message=None, level=None):
		try:
			collection = 'export_details'
			db = MongoDbConnect[collection]
			db.find_one_and_update({'enterprise_id': enterprise_id, '_id': ObjectId(unique_id)}, {'$push': {
				'log': {
					'logged_on': datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 'message': message, 'level': level}}})
			return True
		except Exception as e:
			logger.exception(e)

	def deleteExportDetails(self, enterprise_id=None, unique_id=None, level=None):
		try:
			collection = 'export_details'
			db = MongoDbConnect[collection]
			db.find_one_and_update({'enterprise_id': enterprise_id, '_id': ObjectId(unique_id)}, {'$set': {
				'status': 'Aborted'}})
			db.find_one_and_update({'enterprise_id': enterprise_id, '_id': ObjectId(unique_id)}, {'$push': {
					'log': {'logged_on': datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 'message': 'Aborted', 'level': level, 'status': 'Aborted'}}})

			return True
		except Exception as e:
			logger.exception(e)

	def fetchExportLogdetails(self, unique_id=None, enterprise_id=None):
		try:
			collection = 'export_details'
			result = []
			db = MongoDbConnect[collection]
			export_infos = db.find({'enterprise_id': enterprise_id, '_id': ObjectId(unique_id)}, {'log': 1})
			export_info = export_infos.next()
			for log in export_info['log']:
				result.append(log)
			return result
		except Exception as e:
			logger.exception(e)

	def fetchExportStatus(self, unique_id=None, enterprise_id=None):
		try:
			collection = 'export_details'
			db = MongoDbConnect[collection]
			export_status = db.find({'enterprise_id': enterprise_id, '_id': ObjectId(unique_id)}, {'status': 1, 'gcs_key': 1, '_id': 0})
			export_info = export_status.next()
			status = export_info['status']
			gcs_key = export_info['gcs_key']
			download_file_name = "{url}/{enterprise_id}/{filename}".format(
				url=GCS_PUBLIC_URL, enterprise_id=enterprise_id, filename=gcs_key)
			return status, download_file_name
		except Exception as e:
			logger.exception(e)

	def insertExportDetails(
			self, user=None, enterprise_id=None, from_date=None, to_date=None,
			is_inventory_transaction=None, is_closing_balance_as_opening=None, status=None):
		try:
			collection = 'export_details'
			export_details = {}
			export_details['user_id'] = user.id
			export_details['user_name'] = user.first_name
			export_details['enterprise_id'] = enterprise_id
			export_details['from_date'] = datetime.strptime(from_date, "%Y-%m-%d")
			export_details['to_date'] = datetime.strptime(to_date, "%Y-%m-%d")
			export_details['is_inventory_transaction'] = is_inventory_transaction
			export_details['is_closing_balance_as_opening'] = is_closing_balance_as_opening
			export_details['status'] = status
			export_details['export_request_date'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
			export_details['gcs_key'] = ''
			export_details['modified_on'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
			export_details['modified_by'] = user.id
			export_details['type'] = 'TallyXML'
			export_details['log'] = []
			db = MongoDbConnect[collection]
			db.insert(export_details)
			return True
		except Exception as e:
			logger.exception(e)


def initiate_export_thread(status=None):
	try:
		values = []
		collection = 'export_details'
		db = MongoDbConnect[collection]
		query = db.find({'status': status})
		for fetched_data in query:
			fetch_data = {}
			fetch_data['from_date'] = fetched_data['from_date'].strftime(
				"%Y-%m-%d") if 'from_date' in fetched_data else ""
			fetch_data['to_date'] = fetched_data['to_date'].strftime("%Y-%m-%d 23:59:59") if 'to_date' in fetched_data else ""
			fetch_data['id'] = str(fetched_data['_id']) if '_id' in fetched_data else ""
			fetch_data['is_inventory_transaction'] = fetched_data[
				'is_inventory_transaction'] if 'is_inventory_transaction' in fetched_data else ''
			fetch_data['is_closing_balance_as_opening'] = fetched_data[
				'is_closing_balance_as_opening'] if 'is_closing_balance_as_opening' in fetched_data else ''
			fetch_data['enterprise_id'] = fetched_data['enterprise_id'] if 'enterprise_id' in fetched_data else ""
			values.append(fetch_data)
		call_export_thread(values=values)
	except Exception as e:
		logger.exception(e)


def call_export_thread(values=None):
	try:
		with ThreadPoolExecutor(max_workers=3) as executor:
			results = executor.map(execute_generateXML, values)
		for result in results:
			print(result)
	except Exception as e:
		logger.exception(e.message)


def execute_generateXML(data):
	try:
		export_service = ExportService()
		collection = 'export_details'
		db = MongoDbConnect[collection]
		db.find_one_and_update({'enterprise_id': data['enterprise_id'], '_id': ObjectId(data['id'])}, {'$set': {
			'status': 'In Progress'}})
		xml = export_service.generateTallyXML(
			enterprise_id=data['enterprise_id'], from_date=data['from_date'], to_date=data['to_date'],
			unique_id=data['id'],
			is_inventory_transaction=data['is_inventory_transaction'],
			is_closing_balance_as_opening=data['is_closing_balance_as_opening'])
		if xml:
			db.find_one_and_update({'enterprise_id': data['enterprise_id'], '_id': ObjectId(data['id'])}, {'$set': {
				'status': 'Completed'}})
		return xml
	except Exception as e:
		logger.exception(e.message)
