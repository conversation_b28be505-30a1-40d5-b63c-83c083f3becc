var isTagChanged = false;
function create_delete_tag_button() {
    $(".delete_tag").each(function(index, value) {
        $(this).click(function(){
            $(this).closest('li').find("input[type='checkbox']").attr("checked", true);
            $(this).closest('li').hide();
            isTagChanged = true
        });
    });
}

$(function () {

	$("#id_tag-__prefix__-tag").blur(function(){
		setTimeout(function(){
			$(".ui-autocomplete").hide();
		},300);
	})

    $('#add_tag').click(function () {

        /* */
        refreshSessionPerNActions(10);

        if (trim($("#id_tag-__prefix__-tag").val())== "")  {
                return;
        } else{
			$(".li-tagit-display").removeClass('flash');
			var currentFieldName = $("#id_tag-__prefix__-tag").val();
			$("#ul-tagit-display .li-tagit-display").each(function(){
				if($(this).is(":visible")) {
					if(currentFieldName.toLowerCase().trim() == $(this).find('.label-tagit-display').text().toLowerCase().trim()){
						$('#id_tag-__prefix__-tag').val('');
						$(this).addClass('flash');
						setTimeout(function(){
							$('#id_tag-__prefix__-tag').focus();
						},500);
						return;
					}
				}
			});
			if (trim($("#id_tag-__prefix__-tag").val())!= "") {
				generateFormsetForm('tag');
				var index = parseInt(parseInt($('#id_tag-TOTAL_FORMS').val()) - 1);
				$('#id_tag-' + index + '-tag_label').html($('#id_tag-__prefix__-tag').val());
				$('#id_tag-' + index + '-tag').val($('#id_tag-__prefix__-tag').val().trim());
				$('#id_tag-__prefix__-tag').val("");
				setTimeout(function(){
					$('#id_tag-__prefix__-tag').focus();
				},500);
				create_delete_tag_button();
				isTagChanged = true;
			}
        }
    });
});

$(function () {
	var tag_list = [];
	$.ajax({
		url: "/erp/json/populate_tags/",
		type: "post",
		datatype:"json",
		data: "",
		success: function(response){
		    for (i = 0; i <= response.length - 1 ; i++) {
		        tag_list.push({value: " " + response[i]});
		    }
		},
        error: function (xhr, errmsg, err) {
            console.log(xhr.status + ": " + xhr.responseText);
        }
	});
    $("#id_tag-__prefix__-tag").val(" ");
    $("#id_tag-__prefix__-tag").autocomplete({
        source: function(request, response) {
            var results = $.ui.autocomplete.filter(tag_list, request.term);
            response(results.slice(0, 100));
        },
        minLength: 0,
        focus: function (event, ui) {
            event.preventDefault();
        },
        select: function (event, ui) {
            event.preventDefault();
            $("#id_tag-__prefix__-tag").val(ui.item.value);
            $("#add_tag").click();
        }
    }).focus(function () {
    	var that = $(this)
    	$("#id_tag-__prefix__-tag").addClass('ui-autocomplete-loading');
    	setTimeout(function(){
	        that.autocomplete("search");
	    },300);
    });

    $("#id_tag-__prefix__-tag").keydown(function(event,ui){
	    if(event.keyCode == 13) {
	      if($(this).val() != "") {
	      	$("#add_tag").click();					
			setTimeout(function(){
				$(this).focus();
			},10);
	      }
	      event.preventDefault();
	    }
	});
});

setTimeout(function(){
   $("#id_tag-__prefix__-tag").val("");
},500);