$(document).ready(function(){
    if ($("#search_ledger_no").val() !=""){
        $("#id_ledger_id").val($("#search_ledger_no").val());
    }
});
var ledgerdetails = [];
var BRS = class{
    constructor(bank_data) {
        this.s_no = bank_data[0]
        this.bank_reconciliation_date = bank_data[1]
        this.voucher_date = bank_data[2]
        this.instrument_no = bank_data[3]
        this.narration = bank_data[4]
        this.dr_amt = bank_data[5]
        this.cr_amt = bank_data[6]
        this.bal_amt = bank_data[7]
        this.is_matched = false
        this.is_already_matched = false
        if (bank_data[5] > 0 && bank_data[5] !=""){
            this.is_debit = 1
            this.amount = bank_data[5]
        }else{
            this.is_debit = 0
            this.amount = bank_data[6]
        }
    }
    isMatchedToInstrument(key, amount) {
        var ins = this.instrument_no +"[::]" + this.narration
        if (parseFloat(amount) == parseFloat(this.amount)){
            console.log( key , amount, ins, ins.indexOf(key) > -1)
            return ins.indexOf(key) > -1
        }

    }
    static tableRow(d) {
        var row =""
        if (d.is_debit == true){
            row = "<tr><td></td><td>" + d.bank_reconciliation_date  + "</td><td></td><td>"+d.instrument_no+"</td><td>" + d.voucher_date  + "</td><td>"+d.voucher_no+"</td><td>"+d.narration+"</td><td>"+ d.amount+"</td><td></td></tr>"
        }else{
            row = "<tr><td></td><td>" + d.bank_reconciliation_date  + "</td><td></td><td>"+d.instrument_no+"</td><td>" + d.voucher_date  + "</td><td>"+d.voucher_no+"</td><td>"+d.narration+"</td><td></td><td>"+ d.amount+"</td></tr>"
        }
        return row
    }
}
var form_count=0


function showImportBrs() {
    $(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();
    var ControlCollections = [
        {
            controltype: 'dropdown',
            controlid: 'id_ledger_id',
            isrequired: true,
            errormsg: 'Bank Ledger is required.'
        },

    ];
    var result = JSCustomValidator.JSvalidate(ControlCollections);
    if(result) {
        $("#brs_selected_ledger").text($("#id_ledger_id option:selected").text());
        $("#brs_selected_daterange").text($("#reportrange span").text());
        $("#modal_importBRS").modal('show');
    }
}

$(function() {

    $("#id_save_reconcile").click(function(){
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();
        var ControlCollections = [
            {
                controltype: 'dropdown',
                controlid: 'id_ledger_id',
                isrequired: true,
                errormsg: 'Bank Ledger is required.'
            },

        ];
        var result = JSCustomValidator.JSvalidate(ControlCollections);
        var table = document.getElementById('voucherList')
        var rowcount = document.getElementById('voucherList').rows.length;
        if (rowcount == 1) {
            swal("", "Please Add BRS Details", "error");
            return;
        }
        if(result){
            var form_count = parseInt(document.getElementById('id_bank_reconciliation-TOTAL_FORMS').value);
            for (i=0; i<form_count; i++){
                var voucher_no = $('#id_bank_reconciliation-'+i+'-voucher_no').val();
                if (voucher_no=="" ||voucher_no=="None"){
                    swal("","Please Create Voucher Before Reconciliation","warning");
                    return;
                }
            }
            $("#id_save_reconcile").attr('value','Processing...').addClass('btn-processing');
            $("#loading").show();
            $("#save_brs_form").submit();
            ga('send', 'event', 'Accounts', 'BRS - Reconciliation', $('#enterprise_label').val(), i);
        }
        return result;
    });
    $("#chk_reconciled").click(function(){
        if(this.checked==true){
            $("#chk_reconciled").val(1)
        }else{
            $("#chk_reconciled").val(0)
        }
    });

    $('#id_cancel_reconcile').click(function () {
        window.location.assign('/erp/accounts/brs/');
	});

	$("#load_statement").click(function(){
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();
        var ControlCollections = [
            {
                controltype: 'dropdown',
                controlid: 'id_ledger_id',
                isrequired: true,
                errormsg: 'Bank Ledger is required.'
            },

        ];
        var result = JSCustomValidator.JSvalidate(ControlCollections);
	    if (result){
            $("#loading").show();
            $("#load_statement_form").submit();
        }
	});

	$("#cmdUpload").bind("click", function () {
        if(!$('#fileUpload').val()){
                swal({
				title:"File is required",
				text: "Please select CSV file. ",
				type: "warning",
				showCancelButton: false,
				confirmButtonColor: "#209be1",
				confirmButtonText: "OK",
				closeOnConfirm: true
			});
            return false;
        }
        var regex = /^([a-zA-Z0-9\s_\\.\-:])+(.csv|.txt)$/;
        if (regex.test($("#fileUpload").val().toLowerCase())) {

            ledger_id = $("#id_ledger_id").val()
            $("#id_save_ledger_id").val(ledger_id);
            if (typeof (FileReader) != "undefined") {
                loadBRS()

            } else {
                alert("This browser does not support HTML5.");
            }
        } else {
			swal({
				title:"Invalid File Format",
				text: "Please upload a valid CSV file. ",
				type: "warning",
				showCancelButton: false,
				confirmButtonColor: "#209be1",
				confirmButtonText: "OK",
				closeOnConfirm: true
			});
        }
    });
});

function loadBRS (){
    $("#voucherList").find("tr:gt(0)").remove();
    $('#id_bank_reconciliation-TOTAL_FORMS').val(0)
    $('.manual_brs').removeClass("hide");
    var reader = new FileReader();
    reader.onload = function (e) {
        var table = $("<table />");
        var lines = this.result.split('\n');
        var bankData = []
        var processedBankData = []
        for(var line = 0; line < lines.length; line++){
            if (lines[line].trim() == "") {
                break;
            }
            bankData.push(new BRS(lines[line].splitCSV()))
        }

        list_of_columns = ['S.No','TRANSACTION DATE','VALUE DATE','TRANSACTION/CHEQUE NO','PARTICULARS/REMARKS','DEBIT','CREDIT']
        col_list_from_doc = [bankData[0]['s_no'],bankData[0]['bank_reconciliation_date'],bankData[0]['voucher_date'],bankData[0]['instrument_no'],bankData[0]['narration'],bankData[0]['dr_amt'],bankData[0]['cr_amt']]

        for (var line = 1; line < bankData.length; line++) {
            if (bankData[line].instrument_no == "" || bankData[line].instrument_no == "None")
            {
                swal("Invalid Instrument/Cheque No","warning");
                is_valid = false
                return false;
            }
        }

        if (JSON.stringify(list_of_columns).toLowerCase() === JSON.stringify(col_list_from_doc).toLowerCase())
        {

            $.ajax({
                url : "/erp/accounts/json/brs/ledgeropening/",
                type : "POST",
                dataType: "json",
                data : {'ledger_id': ledger_id},
                success : function(ledgeropening) {
                     if (ledgeropening[0].opening_type == "Dr"){
                        $('#id_dr_total_amount').text(ledgeropening[0].opening)
                        $('#id_cr_total_amount').text(0)
                      }else{
                        $('#id_dr_total_amount').text(0)
                        $('#id_cr_total_amount').text(ledgeropening[0].opening)
                      }

                }, error : function(xhr,errmsg,err) {
                    console.log(xhr.status + ": " + xhr.responseText);
                }
            });


            $('#loading').show();
            var is_valid = true
            $.ajax({
                url : "/erp/accounts/json/brs/ledgerdata/",
                type : "POST",
                dataType: "json",
                data : {ledger_id: ledger_id, from_date: $('#from_date').val(), to_date: $('#to_date').val() },
                success : function(response) {
                    $.each(response.voucherParticulars, function(i, item) {
                        if(item.bank_reconciliation_date != "") {
                            item.is_already_matched = true
                            item.is_matched = true
                        }
                        for (var line = 1; line < bankData.length; line++) {
                            var today = new Date();
                            var transDate;
                            var from = bankData[line].bank_reconciliation_date.split("-")
                            var f = new Date(from[2], from[1] - 1, from[0])
                            transDate = Date.parse(f);

                            if (!dateChecktwo(today,f)){
                                swal("","Transaction date should not be greater than current date","warning");
                                is_valid = false
                                return false;
                            }

                            if(isNaN(transDate)==true){
                                swal("Invalid Transaction date ","warning");
                                is_valid = false
                                return false;
                            }
                            if (bankData[line].voucher_date !="" && bankData[line].voucher_date !="None")
                            {
                                var valueDate;
                                var from = bankData[line].voucher_date.split("-")
                                var f = new Date(from[2], from[1] - 1, from[0])
                                valueDate = Date.parse(f);
                                if(isNaN(valueDate)==true){
                                    swal("Invalid Value date ","warning");
                                    is_valid = false
                                    return false;
                                }
                            }
                            if (bankData[line].is_debit == 1)
                            {
                                if(isNaN(bankData[line].dr_amt)==true){
                                    swal("Invalid Amount","warning");
                                    is_valid = false
                                    return false;
                                }
                            }
                            if (bankData[line].is_debit == 0)
                            {
                                if(isNaN(bankData[line].cr_amt)==true){
                                    swal("Invalid Amount","warning");
                                    is_valid = false
                                    return false;
                                }
                            }
                            if (item.instrument_no !="" && item.instrument_no !="None")
                            {
                                if (bankData[line].isMatchedToInstrument(item.instrument_no,item.amount)){
                                    item.is_matched = true
                                    item.bank_reconciliation_date = bankData[line].bank_reconciliation_date
                                    item.instrument_no = bankData[line].instrument_no
                                    bankData[line].is_matched = true
                                    break
                                }
                            }
                        }
                    });
                    if (is_valid){
                        addRowsVoucher(response.voucherParticulars, true, false);
                        addRowsVoucher(response.voucherParticulars, false, true);
                        addRowsBankSt(bankData, false, true);
                        CustomAutocomplete();
                        calcTotal()
                    }
                    $("#modal_importBRS").modal('hide');
                    $("#fileUpload").text("");
                    $('#loading').hide();
                }, error : function(xhr,errmsg,err) {
                    console.log(xhr.status + ": " + xhr.responseText);
                }
            });
            ga('send', 'event', 'Accounts', 'BRS - Bank Statement Upload', $('#enterprise_label').val(), 10);
        }else{
            alert("Columns are mismatched please enter proper column name");
            return;
        }

    }
    reader.readAsText($("#fileUpload")[0].files[0]);
}



function addRows(brsData, is_matched) {
    var form_count = parseInt(document.getElementById('id_bank_reconciliation-TOTAL_FORMS').value);
    for (var line = 1; line < brsData.length; line++) {
        if (brsData[line].is_matched == is_matched) {
            $("#voucherList").append(BRS.tableRow(brsData[line]))
            console.log("Matched", line)
        }
    }
}

function addRowsVoucher(brsData, is_matched, in_line_save) {
    form_count = parseInt(document.getElementById('id_bank_reconciliation-TOTAL_FORMS').value);
    for (var line = 0; line < brsData.length; line++) {
        if (!brsData[line].is_already_matched && brsData[line].is_matched == is_matched) {
            var enterprise_id = brsData[line].enterprise_id
            var voucher_id = brsData[line].voucher_id
            var amount = brsData[line].amount
            var is_debit = brsData[line].is_debit
            var ledger_id = brsData[line].ledger_id
            var ledger_label = brsData[line].ledger_label
            var voucher_no = brsData[line].voucher_no
            if (brsData[line].voucher_date !="") {
                voucher_date =  moment(brsData[line].voucher_date).format('MMM D, YYYY')
            }else{
                var voucher_date =  ""
            }

            var instrument_no = brsData[line].instrument_no
            var voucher_type
            var debit_amount
            var credit_amount
            if (brsData[line].is_debit == 1){
                voucher_type = "Payment"
                debit_amount = brsData[line].amount
                credit_amount = 0
            }else{
                voucher_type = "Receipt"
                credit_amount = brsData[line].amount
                debit_amount = 0
            }
            var bank_reconciliation_date =""
            if (brsData[line].is_matched == true){
                var dateSplit = brsData[line].bank_reconciliation_date.split("-");
                var splittedDate = dateSplit[1]+"-"+dateSplit[0]+"-"+dateSplit[2];
                bank_reconciliation_date =  moment(splittedDate).format('MMM D, YYYY')
            }
            var s_no = parseInt(form_count) + 1
            var in_line_save_button = "<td style='color: #3c763d !important; font-size: 16px;'><i data-tooltip='tooltip' title='Matched' class='fa fa-check' aria-hidden='true'></i></td>";
            if (in_line_save == true){
                var selectedVoucher = 'bank_reconciliation-' + form_count;
                in_line_save_button = '<td><i class="fa fa-save" role="button" data-tooltip="tooltip" style="font-size: 1.7em;" title="save" aria-hidden="true" id="id_bank_reconciliation-' + form_count + '-in_line_save" name="bank_reconciliation-' + form_count + '-in_line_save" onclick="saveVoucherInBRS(\'bank_reconciliation-' + form_count+'\');"></i></td>';
            }


            var row = "<tr bgcolor='#ECECEC' id='bank_reconciliation-" + form_count +"' align='center'><td hidden='hidden'><input type='checkbox' name='bank_reconciliation-" + form_count + "-DELETE' id='id_bank_reconciliation-" + form_count + "-DELETE'>" +
            "<input id='id_bank_reconciliation-" + form_count + "-voucher_id' type='text' class='form-control' value='"+voucher_id+"' name='bank_reconciliation-" + form_count + "-voucher_id'>" +
            "<input readonly='' name='bank_reconciliation-" + form_count + "-amount' id='id_bank_reconciliation-" + form_count + "-amount' type='text' class='form-control text_box_label oa_price_ns textbox_amt text-right' value='"+amount+"' tabindex='-1'>" +
            "<input hidden='hidden' type='text' name='bank_reconciliation-" + form_count + "-enterprise_id' value='"+enterprise_id+"' id='id_bank_reconciliation-" + form_count + "-enterprise_id'>" +
            "<input name='bank_reconciliation-" + form_count + "-is_debit' enterkey='do_nothing' value='"+is_debit+"' class='form-control is_debit' type='text' id='id_bank_reconciliation-" + form_count + "-is_debit'>" +
            "<input id='id_bank_reconciliation-" + form_count + "-ledger_id' type='text' class='form-control ledger_select' value='"+ledger_id+"' name='bank_reconciliation-" + form_count + "-ledger_id'>" +
            "<input name='bank_reconciliation-" + form_count + "-voucher_no' enterkey='do_nothing' value="+voucher_no+" class='form-control voucher_no' type='text' id='id_bank_reconciliation-" + form_count + "-voucher_no'>" +
            "<input name='bank_reconciliation-" + form_count + "-ledger_label' enterkey='do_nothing' value="+ledger_label+" class='form-control ledger_select_label' type='text' id='id_bank_reconciliation-" + form_count + "-ledger_label'>" +
            "<input name='bank_reconciliation-" + form_count + "-voucher_date' enterkey='do_nothing' value='"+voucher_date+"' class='form-control voucher_date' type='text' id='id_bank_reconciliation-" + form_count + "-voucher_date'></td><td>"+ s_no +" </td><td>" +
            "<input id='id_bank_reconciliation-" + form_count + "-bank_reconciliation_date' onblur='calcTotal();' type='text' class='form-control rec_bank_date hide' name='bank_reconciliation-" + form_count + "-bank_reconciliation_date' value = '"+bank_reconciliation_date+"'>" +
            "<input onblur='calcTotal();' autocomplete='off' type='text' class='form-control single_datePicker single_datePicker_table single_datePicker_till_date' id='bankreconciliationDate_" + form_count+"' placeholder='Select Date' ><i class='glyphicon glyphicon-calendar custom-datepicker-icon-table'></i></td><td class='text-left'>"+voucher_type+"</td><td>" +
            "<input id='id_bank_reconciliation-" + form_count + "-instrument_no' type='text' class='form-control instrument_no' name='bank_reconciliation-" + form_count + "-instrument_no' enterkey='do_nothing' value='"+instrument_no+"' > </td><td>"+voucher_date+"</td><td>"+voucher_no+"</td><td class='text-left'>"+ledger_label+"</td><td>" +
            "<input type='text' id='id_bank_reconciliation-" + form_count + "-debit_amount' name='bank_reconciliation-" + form_count + "-debit_amount' class='form-control text-right' onkeypress='return validateFloatKeyPress(this,event);' disabled='disabled'  value='"+ debit_amount +"'></td><td>" +
            "<input type='text' id='id_bank_reconciliation-" + form_count + "-credit_amount' name='bank_reconciliation-" + form_count + "-credit_amount' class='form-control text-right' onkeypress='return validateFloatKeyPress(this,event);' disabled='disabled'  value='"+ credit_amount +"'></td>" + in_line_save_button + "</tr>"
            $("#voucherList").append(row)

            $('#id_bank_reconciliation-TOTAL_FORMS').val(++form_count);
        }
    }
    SingleNewDatePickerInit();
    TooltipInit();
}

function addRowsBankSt(brsData, is_matched, in_line_save) {
    form_count = parseInt(document.getElementById('id_bank_reconciliation-TOTAL_FORMS').value);
    for (var line = 1; line < brsData.length; line++) {
        if (brsData[line].is_matched == is_matched) {
            var enterprise_id = $("#enterprise_id").val()
            var voucher_id = brsData[line].voucher_id
            var amount = brsData[line].amount
            var is_debit = brsData[line].is_debit
            var ledger_id = brsData[line].ledger_id
            var ledger_label = "<input name='bank_reconciliation-" + form_count + "-ledger_label' value='' class='form-control ledger_select_label' type='text' id='id_bank_reconciliation-" + form_count + "-ledger_label'>"
            var voucher_no = ""
            if (brsData[line].voucher_date !="") {
                var dateSplit = brsData[line].voucher_date.split("-");
                var splittedDate = dateSplit[1]+"-"+dateSplit[0]+"-"+dateSplit[2];
                voucher_date =  moment(splittedDate).format('MMM D, YYYY')
            }else{
                var dateSplit = brsData[line].bank_reconciliation_date.split("-");
                var splittedDate = dateSplit[1]+"-"+dateSplit[0]+"-"+dateSplit[2];
                voucher_date =  moment(splittedDate).format('MMM D, YYYY')
            }
            var instrument_no = brsData[line].instrument_no
            var voucher_type
            var debit_amount
            var credit_amount
            if (brsData[line].is_debit == 1){
                voucher_type = "Payment"
                debit_amount = brsData[line].amount
                credit_amount = 0
            }else{
                voucher_type = "Receipt"
                credit_amount = brsData[line].amount
                debit_amount = 0
            }
            var bank_reconciliation_date =""
            var dateSplit = brsData[line].bank_reconciliation_date.split("-");
            var splittedDate = dateSplit[1]+"-"+dateSplit[0]+"-"+dateSplit[2];
            bank_reconciliation_date =  moment(splittedDate).format('MMM D, YYYY')
            var s_no = parseInt(form_count) + 1

            var in_line_save_button = "<td style='color: #3c763d !important; font-size: 16px;'><i data-tooltip='tooltip' title='Matched' class='fa fa-check' aria-hidden='true'></i></td>";
            if (in_line_save == true){
                var selectedVoucher = 'bank_reconciliation-' + form_count;
                in_line_save_button = '<td><i class="fa fa-save" role="button" data-tooltip="tooltip" style="font-size: 1.7em;" title="save" aria-hidden="true" id="id_bank_reconciliation-' + form_count + '-in_line_save" name="bank_reconciliation-' + form_count + '-in_line_save" onclick="saveVoucherInBRS(\'bank_reconciliation-' + form_count+'\')"></i></td>';
            }

            var row = "<tr bgcolor='#ECECEC' id='bank_reconciliation-" + form_count +"' align='center'><td hidden='hidden'><input type='checkbox' name='bank_reconciliation-" + form_count + "-DELETE' id='id_bank_reconciliation-" + form_count + "-DELETE'>" +
            "<input id='id_bank_reconciliation-" + form_count + "-voucher_id' type='text' class='form-control' value='"+voucher_id+"' name='bank_reconciliation-" + form_count + "-voucher_id'>" +
            "<input readonly='' name='bank_reconciliation-" + form_count + "-amount' id='id_bank_reconciliation-" + form_count + "-amount' type='text' class='form-control text_box_label oa_price_ns textbox_amt text-right' value='"+amount+"' tabindex='-1'>" +
            "<input hidden='hidden' type='text' name='bank_reconciliation-" + form_count + "-enterprise_id' value='"+enterprise_id+"' id='id_bank_reconciliation-" + form_count + "-enterprise_id'>" +
            "<input name='bank_reconciliation-" + form_count + "-is_debit' enterkey='do_nothing' value='"+is_debit+"' class='form-control is_debit' type='text' id='id_bank_reconciliation-" + form_count + "-is_debit'>" +
            "<input id='id_bank_reconciliation-" + form_count + "-ledger_id' type='text' class='form-control ledger_select' value='"+ledger_id+"' name='bank_reconciliation-" + form_count + "-ledger_id'>" +
            "<input name='bank_reconciliation-" + form_count + "-voucher_no' enterkey='do_nothing' value='"+voucher_no+"' class='form-control voucher_no' type='text' id='id_bank_reconciliation-" + form_count + "-voucher_no'>" +
            "<input name='bank_reconciliation-" + form_count + "-ledger_label' enterkey='do_nothing' value="+ledger_label+" class='form-control ledger_select_label' type='text' id='id_bank_reconciliation-" + form_count + "-ledger_label'>" +
            "<input name='bank_reconciliation-" + form_count + "-voucher_date' enterkey='do_nothing' value='"+voucher_date+"' class='form-control voucher_date' type='text' id='id_bank_reconciliation-" + form_count + "-voucher_date'></td><td>"+ s_no +" </td><td>" +
            "<input id='id_bank_reconciliation-" + form_count + "-bank_reconciliation_date' onblur='calcTotal();' type='text' class='form-control rec_bank_date hide' name='bank_reconciliation-" + form_count + "-bank_reconciliation_date' value = '"+bank_reconciliation_date+"'>" +
            "<input onblur='calcTotal();' autocomplete='off' type='text' class='form-control single_datePicker single_datePicker_table single_datePicker_till_date date_disabled' id='bankreconciliationDate_" + form_count+"' placeholder='Select Date'  ><i class='glyphicon glyphicon-calendar custom-datepicker-icon-table'></i></td><td class='text-left'>"+voucher_type+"</td><td>" +
            "<input id='id_bank_reconciliation-" + form_count + "-instrument_no' type='text' class='form-control instrument_no' name='bank_reconciliation-" + form_count + "-instrument_no' enterkey='do_nothing' value='"+instrument_no+"' disabled='disabled' > </td><td>"+voucher_date+"</td><td>"+voucher_no+"</td><td class='text-left'>"+ledger_label+"</td><td>" +
            "<input type='text' id='id_bank_reconciliation-" + form_count + "-debit_amount' name='bank_reconciliation-" + form_count + "-debit_amount' class='form-control text-right' onkeypress='return validateFloatKeyPress(this,event);' disabled='disabled'  value='"+ debit_amount +"'></td><td>" +
            "<input type='text' id='id_bank_reconciliation-" + form_count + "-credit_amount' name='bank_reconciliation-" + form_count + "-credit_amount' class='form-control text-right' onkeypress='return validateFloatKeyPress(this,event);' disabled='disabled'  value='"+ credit_amount +"'></td>"+in_line_save_button+"</tr>"
            $("#voucherList").append(row)

            $('#id_bank_reconciliation-TOTAL_FORMS').val(++form_count);
        }
    }
    SingleNewDatePickerInit();
    TooltipInit();
}


function saveVoucherInBRS(FormId) {
    var voucher = document.getElementById('id_' + FormId + '-voucher_id');
    var voucher_id =  voucher.value
    var is_debit = document.getElementById('id_' + FormId + '-is_debit').value;
    var instrument = document.getElementById('id_' + FormId + '-instrument_no');
    var instrument_no =  instrument.value
    var bank_reconciliation = document.getElementById('id_' + FormId + '-bank_reconciliation_date');
    var bank_reconciliation_date =  bank_reconciliation.value;
    var ledger = document.getElementById('id_' + FormId + '-ledger_id');
    var ledger_id =  ledger.value;

    var amount = document.getElementById('id_' + FormId + '-amount');
    var amount_value =  amount.value;

    var bank_ledger_id = $("#id_ledger_id").val();

    if(voucher_id != "undefined" && voucher_id != ""  ){
        if (instrument_no == "" && bank_reconciliation_date ==""){
            swal("","Please Enter Instrument Number/Bank Reconciliation Date","warning");
        }else{
            $.ajax({
                data: {"voucher_id": voucher_id, "instrument_no": instrument_no,
                        "bank_reconciliation_date": bank_reconciliation_date},
                type: "POST",
                url: "/erp/accounts/json/brs/brsvouchersave/",
                success: function(json){
                    loadBRS();
                }
            });
        }
    }else{
        if (ledger_id != "" && ledger_id !="undefined") {
            $.ajax({
                data: { "instrument_no": instrument_no,"ledger_id": ledger_id, "amount":amount_value,"is_debit":is_debit,
                        "bank_reconciliation_date": bank_reconciliation_date, "bank_ledger_id": bank_ledger_id, },
                type: "POST",
                url: "/erp/accounts/json/brs/brsvouchercreate/",
                success: function(json){
                    loadBRS();
                }
            });
        }
        else {
            swal("","Please Select Ledger","warning");
            ledger.focus()
        }
    }
}



function calcTotal()
{
	var form_count = parseInt(document.getElementById('id_bank_reconciliation-TOTAL_FORMS').value);
	var crTotal=0;
	var drTotal=0;
	var ref_crTotal=0;
	var ref_drTotal=0;
	var not_ref_crTotal=0;
	var not_ref_drTotal=0;
	var from_date;
	var to_date;
	var balance_amount =0
	for (i=0; i<form_count; i++){
        var amount = $('#id_bank_reconciliation-'+i+'-amount').val();
        if(document.getElementById('id_bank_reconciliation-'+i+'-is_debit').value == '0'){
            var credit_amount = document.getElementById('id_bank_reconciliation-'+i+'-credit_amount').value;
            if(/[0-9.]/g.test(credit_amount)){
                crTotal = parseFloat(crTotal) + parseFloat(amount);
                if(document.getElementById('id_bank_reconciliation-'+i+'-bank_reconciliation_date').value != ''){

                    if (!dateChecktwo(document.getElementById('id_bank_reconciliation-'+i+'-bank_reconciliation_date').value, document.getElementById('id_bank_reconciliation-'+i+'-voucher_date').value)){
                        swal("","Transaction date should not be less than voucher date","warning");
                        $('#id_bank_reconciliation-'+i+'-bank_reconciliation_date').val('')
                        $('#id_bank_reconciliation-'+i+'-bank_reconciliation_date').next('input').val('');
                    }else{
                        if(dateCheck($('#from_date').val(),$('#to_date').val(),document.getElementById('id_bank_reconciliation-'+i+'-bank_reconciliation_date').value)){
                            ref_crTotal = parseFloat(ref_crTotal) + parseFloat(amount);
                        }else{
                            not_ref_crTotal = parseFloat(not_ref_crTotal) + parseFloat(amount);
                        }
                    }
                }else{
                    not_ref_crTotal = parseFloat(not_ref_crTotal) + parseFloat(amount);
                }
            }
        }
        if(document.getElementById('id_bank_reconciliation-'+i+'-is_debit').value == '1'){
            var debit_amount = document.getElementById('id_bank_reconciliation-'+i+'-debit_amount').value
            if(/[0-9.]/g.test(debit_amount)){
                drTotal = parseFloat(drTotal) + parseFloat(amount);
                if(document.getElementById('id_bank_reconciliation-'+i+'-bank_reconciliation_date').value != ''){

                    if (!dateChecktwo(document.getElementById('id_bank_reconciliation-'+i+'-bank_reconciliation_date').value, document.getElementById('id_bank_reconciliation-'+i+'-voucher_date').value)){
                        swal("","Transaction date should not be less than voucher date","warning");
                        $('#id_bank_reconciliation-'+i+'-bank_reconciliation_date').val('')
                        $('#id_bank_reconciliation-'+i+'-bank_reconciliation_date').next('input').val('');
                    }else{
                        if(dateCheck($('#from_date').val(),$('#to_date').val(),document.getElementById('id_bank_reconciliation-'+i+'-bank_reconciliation_date').value)){
                            ref_drTotal = parseFloat(ref_drTotal) + parseFloat(amount);
                        }else{
                            not_ref_drTotal = parseFloat(not_ref_drTotal) + parseFloat(amount);
                        }
                    }
                }else{
                    not_ref_drTotal = parseFloat(not_ref_drTotal) + parseFloat(amount);
                }
            }
        }
    }

    $('#id_not_ref_cr_total_amount, #th_not_ref_cr_total_amount').text(not_ref_crTotal.toFixed(2));
    $('#id_not_ref_dr_total_amount, #th_not_ref_dr_total_amount').text(not_ref_drTotal.toFixed(2));
    if ($('#id_dr_total_amount').text() >0){
        balance_amount =  (parseFloat(not_ref_crTotal.toFixed(2)) + parseFloat($('#id_dr_total_amount').text())) - parseFloat(not_ref_drTotal.toFixed(2))
        if (balance_amount > 0) {
             $('#id_ref_dr_total_amount').text(balance_amount.toFixed(2));
             $('#id_ref_cr_total_amount').text('0.00');
         } else{
             $('#id_ref_cr_total_amount').text((balance_amount *-1).toFixed(2));
             $('#id_ref_dr_total_amount').text('0.00');
         }

    }
    if ($('#id_cr_total_amount').text() >0){
        balance_amount =  (parseFloat(not_ref_drTotal.toFixed(2)) + parseFloat($('#id_cr_total_amount').text())) - parseFloat(not_ref_crTotal.toFixed(2))
        if (balance_amount > 0) {
        $('#id_ref_cr_total_amount').text(balance_amount.toFixed(2));
        $('#id_ref_dr_total_amount').text('0.00');
        } else {
        $('#id_ref_dr_total_amount').text((balance_amount *-1).toFixed(2))
         $('#id_ref_cr_total_amount').text('0.00');
        }
    }
}

function dateCheck(from,to,check) {

    var fDate,lDate,cDate;
    fDate = Date.parse(from);
    lDate = Date.parse(to);
    cDate = Date.parse(check)

    if((cDate <= lDate && cDate >= fDate)) {
        return true;
    }
    return false;
}

function dateChecktwo(from,to) {
    var fDate,lDate;
    fDate = Date.parse(from);
    lDate = Date.parse(to);
    console.log("Date:",fDate + "-" + lDate)
    if((fDate >= lDate)) {
        return true;
    }
    return false;
}

$(function () {
    var zid =  ""
    $.ajax({
        url: "/erp/accounts/json/voucher/loadledgerdetails/",
        type: "post",
        datatype:"json",
        data: zid,
        success: function(response){
            for (i = 0; i <= response.length - 1 ; i++) {
               ledgerdetails.push({value:response[i][0],label:"["+response[i][2]+"] "+response[i][1]+" ",group:""+response[i][2]+""});
            }
        }
    });
});


function CustomAutocomplete(){
    $(".ledger_select_label").autocomplete({
        source: ledgerdetails,
        minLength: 0,
        focus: function (event, ui) {
            event.preventDefault();
        },
        select: function (event, ui) {
            event.preventDefault();
            $(this).closest('tr').find('.ledger_select').val(ui.item.value);
            $(this).closest('tr').find('.ledger_select_label').val(ui.item.label);
            $(this).closest('tr').find('.ledger_select_group').val(ui.item.group);
        }
    }).focus(function () {
        $(this).autocomplete("search");
    });
}