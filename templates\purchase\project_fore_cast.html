{% extends "purchase/sidebar.html" %}
{% block project_fore_cast %}
    <style>
        .tr-header td {
            font-weight: bold;
            font-size: 16px;
            border-bottom: 2px solid #666
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        th, td {
            border: 1px solid #dddddd;
            text-align: left;
            padding: 8px;
            position: relative;
        }

        th {
            background-color: #f2f2f2;
        }

        tr:hover {
            background-color: #f5f5f5;
        }

        .edit-icon {
            display: none;
            position: absolute;
            transform: translateY(-50%);
            cursor: pointer;
        }

        td:hover .edit-icon {
            display: inline-block;
        }
        .working_capital .edit-icon  {
            display: inline-block;
         }
        .table-container {
        overflow-x: auto;
        max-width: 100%;
        overflow-y: auto;
        max-height: 635px;
        }
        #revenueTable th,
        #revenueTable td {
            white-space: nowrap;
            width:150px !important;
        }
        #revenueTable th:first-child, #revenueTable td:first-child {
          position: sticky;
          left: 0;
          background-color: #f2f2f2;
          z-index: 1;
        }
        .totalRevenue
        {
        font-size:14px;
        }
        .totalExpense
        {
        background-color:#f2f2f2;
        }
        .totalCashFlow{
        background-color:#f2f2f2;
        }
        .reJson{
        font-size:14px;
        }
        .invalid-row{
            border:2px solid red !important;
        }
        	.history-log-part {
		width: 100%;
	    margin: 0;
	    padding: 8px 15px;
	    cursor: pointer;
	    padding-left: 60px;
	}
	.history-log-part:last-child {
		border-bottom: none;
	}
	.history-log-part:hover {
		background: #e8f0fe;
	}
	.history-log-part:hover .history-log-date {
		font-weight: bold;
	}
	.history-log-date {
	   font-style: italic;
	}
	.history-log-username {
		font-size: 15px;
		padding-top: 3px;
		font-weight: bold;
	}

	.history-log-content {
		margin-top: 8px;
	}

	.history-log-content ul {
		padding: 0px 10px 0px 35px;
	}
	.history-log-content li {
		margin-bottom: 10px;
	}

	.history-log-content li:last-child {
		margin-bottom: 0;
	}

	.history-log-part.active .history-log-date,
	.history-log-part.active .history-log-username {
		color: #004195;
	}

	.history-log-part.active .history-log-date {
	    text-align: right;
		display: block;
	}

	.history-log-part .fa-chevron-down:before {
		content: "\f078";
		color: #999;
	}

	.history-log-part.active .fa-chevron-down:before {
		content: "\f077";
		color: #999;
	}

	#change_log_modal .modal-body {
		overflow: auto;
	}
ul.timeline {
	    list-style-type: none;
	    position: relative;
		padding: 0px;
	}
	ul.timeline:before {
	    content: ' ';
	    background: #d4d9df;
	    display: inline-block;
	    position: absolute;
	    left: 29px;
	    width: 2px;
	    height: 94%;
	    z-index: 400;
        margin-top: 12px;
	}
	ul.timeline > li {

	}
	ul.timeline > li:before {
	    content: ' ';
	    background: white;
	    display: inline-block;
	    position: absolute;
	    border-radius: 50%;
	    border: 3px solid #209be1;
	    left: 23px;
	    width: 14px;
	    height: 14px;
	    z-index: 400;
	    margin-top: 4px;
	}
	ul.timeline > li:last-child:before {
		background: #209be1;
	}
	body {
		overflow-y: scroll;
	}
       .totalRevenue_head{
        position: sticky;
        top: 73px;
        background-color: #f2f2f2;
        z-index: 100;
        }
        .totalExpense_head{
        position: sticky;
        top: 119px;
        background-color: #f2f2f2;
        z-index: 100;
        }
    </style>
{% if logged_in_user|canView:'ACCOUNTS' %}
    <script type="text/javascript" src="/site_media/js/Component/projects.js?v={{ current_version }}"></script>
    <div class="right-content-container">
        <div class="page-title-container">
            <span class="page-title">{{ template_title }}</span>
        </div>
        <div class="container" style="margin-top: 20px;">
            <div class="project_forecast hide" style="overflow-y: scroll;">
                <div style="padding:20px">
                    <h4>ACTIVITY LOG</h4>
                </div>
                <button style="margin-right:25px; margin-top:-53px;" type="button" onClick="close()" class="closeBtn pull-right">×</button>
                <ul class="history-project_forecast history-log-container timeline"></ul>
            </div>
            <div class="col-sm-4 pull-right hide" style="margin-bottom:2%;">
                        <input type="submit" class="btn btn-save btn-margin-1" style="width:49%" onclick="consolidatedCashflow('/erp/sales/get_consolidated_cashflow/', '_blank')" value="Consolidated Report">
                        <input type="submit" class="btn btn-save btn-margin-1" style="width:50%" onclick="cashFlowOverView()" value="CashFlow Overview">
                            </div>
            <div class="row">
                <div class="col-lg-12 remove-padding">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="component_project proj_forcast" data-id="project" data-name="project" style="max-width: 300px"></div>
                            <label id="expRev" style="display: block;margin-top: 5px;">Revenue: <span id="revenue" style="margin-right: 10px;">0</span>Expense : <span id="expense" style="margin-right: 10px;">0</span> </label>
                        </div>
                        <div class="col-sm-3">
                            <label>Project Owner Name</label>
                               <input class="form-control" type="text" id="project_owner">
                        </div>
                        <div class="col-sm-3">
                            <label>Email</label>
                               <input class="form-control" type="text" id="project_owner_email" onblur="validationEmail(this)">
                        </div>
                        <div class="col-sm-3">
                            <label>Contact No.</label>
                               <input class="form-control" type="text" id="project_owner_no">
                        </div>
                        <input type="hidden" id="isSuper" value="{{logged_in_user.is_super}}">
                         <h2 style="text-align:center;margin-top:7%">Budgeted Cashflow</h2>
                           <div class="text-center">Cash Allocated:<b style="font-size: 18px;margin-left: 10px;">-</b><span style="font-size: 20px" class="working_capital">
                                <span id="workingCapitalValue" style="margin-right:10px;">0.00</span>
                                {% if logged_in_user.is_super %}
                                    <i id="editCA" class="fa fa-pencil-square-o"></i>
                               {% endif %}
                                </span>
                                <div class="csv_export_button pull-right" style="margin-bottom:10px;">
                                    <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#revenueTable'), 'Project_forecast.csv']);" data-tooltip="tooltip" title="Download Project Forecast as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
                                </div>
                               <div class="material_txt col-sm-1 pull-right" style="left:6%;margin-bottom:10px;">
                                   <input type="submit" id="forecastSave" class="btn btn-save btn-margin-1" onclick="checkInputsForZero()" value="Save" {% if not logged_in_user|canEdit:'ACCOUNTS' %} disabled {% endif %} >
                                </div>
                           </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="table-container" style="width: 96%;text-align: center;margin-left: 2%;">
            <table class="table table-bordered" id="revenueTable"></table>
        </div>
        <span class="hide" id="versionChangeNote" style="margin-top: 1%;background-color:#f9e7e4;padding:10px;margin-left: 5%;font-size: 15px;"><b>Note</b> : This is the Data for Version changes. We can't modify this.</span>
    </div>
<div class="hide">
	<form id="consolidated_cashflow_form" method="post" action="">
		{%csrf_token%}
		<input type="hidden" id="project_id" name="project_id" value="" />
	</form>
</div>
    <script>
        $(document).ready(function () {
            const cashAllocatedValue = $('#workingCapitalValue').val();
            addEditFunctionality($('#workingCapitalValue'), cashAllocatedValue? cashAllocatedValue : 0);
            setTimeout(projectIdFetch, 2000);
            $("#id-change-log").removeClass("hide");
            $("#id-change-log").click(function() {
                changeLogActivityInit();
                $('.project_forecast').removeClass("hide");
           });
        });
        const workingCapitalValue = $("#workingCapitalValue");
        const editIcon = $("#editCA");
        editIcon.click(toggleEdit);
        function toggleEdit() {
            const currentValue = workingCapitalValue.text();
            const inputField = $("<input>").attr({
                type: "number",
                value: currentValue
            });
            workingCapitalValue.replaceWith(inputField);
            editIcon.hide();
            inputField.focus().select();
            inputField.blur(function() {
                workingCapitalValue.text(inputField.val().replace(/^0+/, ''));
                inputField.replaceWith(workingCapitalValue);
                editIcon.show();
            });
        }

        function projectIdFetch(){
            let projectId = $('#project').val();
            const project = JSON.parse(localStorage.getItem('project'));
            $('#li_project_forecast').addClass('active');
            $("#menu_accounts").addClass("selected");
            if(project && project.type == 'Secondary'){
                $("#project").val(project.id).attr("disabled", true).trigger('chosen:updated');
                projectId = project.id
            }
            actualProjectsBudget(projectId);
            $('#project').change(function() {
                    actualProjectsBudget($(this).val());
            });
            fetchAnotherTableData(projectId);
            $("select#project").change(function() {
                const projectId = $(this).val();
                fetchAnotherTableData(projectId);
                $('#versionChangeNote').addClass('hide');
                $('#forecastSave').prop('disabled', false);
                $('.project_forecast').addClass('hide');
            });
        }

    const addEditFunctionality = ($cell, value) => {
        const isSuperUser = $('#isSuper').val();
        if( isSuperUser == 'True'){
            $cell.addClass('editable');
            $cell.html(`<span>${value}</span><i class="fa fa-pencil-square-o edit-icon"></i>`);
            $cell.on('click', function () {
                if ($(this).find('input.editable-textbox').length === 0) {
                    const type = $cell.closest('tr').find('td:first').hasClass('revenueHead') ? 'revenue' : 'expense';
                    const $span = $(this).find('span').first();
                    const text = $span.text();
                    const $input = $(`<input class="editable-textbox" onblur="addwithTotal(event, '${type}')">`).attr('type', 'number').val(text);
                    $span.hide();
                    $(this).append($input);
                    $input.focus();
                    $(this).find('.edit-icon').hide();
                }
            });
        }
    };

    const fetchAnotherTableData = async (projectId) => {
        projectId = $('#project').val();
        try {
            const actualProjectsBudget = await $.ajax({
                url: '/erp/sales/json/total_credit_debit/',
                type: "GET",
                data: {'project_id': projectId, 'is_ledger': true}
            });
            populateTable(actualProjectsBudget, "#revenueTable");
        }
        catch (error) {
            console.error("Error fetching another table data:", error);
        }
    };

    let responseData;;
    const populateTable = (data, tableId) => {
        responseData = data;
        $("#loading").show();
        $(tableId).empty();

        if (!data.data || data.data.months.length === 0) {
            $("#loading").hide();
            const emptyTable = `<tr><td width='100%' class='text-center'>No data to display</td><tr>`;
            $(tableId).append(emptyTable);
            return;
        }

        const defaultRow = `<thead style='position: sticky;top: 0;background-color: #f2f2f2;z-index: 100'>
        <tr></tr></thead><tbody></tbody>`;
        $(tableId).append(defaultRow);

        const months = data.data.months;

        const revenueData = data.data.revenue;
        const revenueTypes = Object.keys(revenueData);

        const expensesData = data.data.expenses;
        const expensesTypes = Object.keys(expensesData);

        const $tableHead = $(tableId + ' thead');
        const $tableBody = $(tableId + ' tbody');

        const $row = $('<tr> <th style="border-bottom:none">');
        months.forEach(month => {
            const $headerCell = $('<th colspan=2 class="text-center">').text(month);
            const $csv_header = $('<th hidden="hidden" class="text-center reJson">').text(month);
            $row.append($headerCell);
            $row.append($csv_header);
        });
        $tableHead.append($row);

        const $rowNew = $('<tr> <th style="border-top:none">');
        months.forEach(month => {
            const $rowSplitId = [$('<th class="text-center reJson">').text("Actual"),$('<th class="text-center reJson">').text("Budget")];
            $rowNew.append($rowSplitId);
        });

        $tableHead.append($rowNew);


        const totalRevenueBudget = {};
        const totalRevenueActual = {};
        const revenueListBudget = [];
        const revenueListActual = [];
        months.forEach(month => {
            totalRevenueActual[month] = 0;
            totalRevenueBudget[month] = 0;
            revenueTypes.forEach(incomeType => {
                totalRevenueBudget[month] += revenueData[incomeType][month].budget ? revenueData[incomeType][month].budget : 0.00;
                 totalRevenueActual[month] += revenueData[incomeType][month].actual ? revenueData[incomeType][month].actual : 0.00;
            });
        });

        const $totalRowRevenue = $('<tr class="tr-header td totalRevenue_head">');
        const $totalHeaderRevenue = $('<td class="reJson totalRevenue">').text('Revenue');
        $totalHeaderRevenue.append($('<button class="pull-right" onclick="addNewRow(&quot;revenue&quot;)">+</button>'));
        $totalRowRevenue.append($totalHeaderRevenue);

        months.forEach(month => {
            const $totalCell = [$('<td class="text-right reJson totalRevenue">').text(parseFloat(totalRevenueActual[month]).toFixed(2)),$('<td class="text-right reJson totalRevenue">').text(parseFloat(totalRevenueBudget[month]).toFixed(2))];
            revenueListBudget.push(totalRevenueBudget[month] ? parseFloat(totalRevenueBudget[month]).toFixed(2) : 0.00);
            revenueListActual.push(totalRevenueActual[month] ? parseFloat(totalRevenueActual[month]).toFixed(2) : 0.00);
            $totalRowRevenue.append($totalCell);
        });

        $tableBody.append($totalRowRevenue);

        const isSuperUser = $('#isSuper').val();
        revenueTypes.forEach(incomeType => {
            const $newRow = $('<tr>');
            const $incomeTypeCell = $('<td class="revenueHead">').text(incomeType);
            $newRow.append($incomeTypeCell);

                months.forEach(month => {
                    const actualValue = revenueData[incomeType][month].actual ? parseFloat(revenueData[incomeType][month].actual).toFixed(2) : '0.00';
                    const budgetValue = revenueData[incomeType][month].budget ? parseFloat(revenueData[incomeType][month].budget).toFixed(2) : '0.00';

                    const $incomeCell = [
                        $('<td class="text-right actual">').text(actualValue),
                        $('<td class="text-right budget">').text(budgetValue)
                    ];

                    $incomeCell.forEach(cell => {
                        if (cell.hasClass('budget')) {
                            addEditFunctionality(cell, budgetValue);
                        }
                    });

                    $newRow.append($incomeCell);
                });

                $tableBody.append($newRow);
        });

        const totalExpensesActual = {};
        const totalExpensesBudget = {};
        const expensesListBudget = [];
        const expensesListActual = [];
        months.forEach(month => {
            totalExpensesActual[month] = 0;
            totalExpensesBudget[month] = 0;
            expensesTypes.forEach(incomeType => {
                totalExpensesActual[month] += expensesData[incomeType][month].actual ? expensesData[incomeType][month].actual : 0.00;
                 totalExpensesBudget[month] += expensesData[incomeType][month].budget ? expensesData[incomeType][month].budget: 0.00;
            });
        });

        const $totalRowExpenses = $('<tr class="tr-header td totalExpense_head">');
        const $totalHeaderExpenses = $('<td class="reJson totalExpense">').text('Expenses');
        $totalHeaderExpenses.append($('<button class="pull-right" onclick="addNewRow(&quot;expense&quot;)">+</button>'));
        $totalRowExpenses.append($totalHeaderExpenses);

        months.forEach(month => {
            const $totalCell = [$('<td class="text-right reJson totalExpense">').text(parseFloat(totalExpensesActual[month]).toFixed(2)),$('<td class="text-right reJson totalExpense">').text(parseFloat(totalExpensesBudget[month]).toFixed(2))];
            expensesListActual.push(totalExpensesActual[month] ? parseFloat(totalExpensesActual[month]).toFixed(2) : 0.00)
             expensesListBudget.push(totalExpensesBudget[month] ? parseFloat(totalExpensesBudget[month]).toFixed(2) : 0.00)
            $totalRowExpenses.append($totalCell);
        });

        $tableBody.append($totalRowExpenses);

        expensesTypes.forEach(expenseType => {
            const $newRow = $('<tr>');
            const $expenseTypeCell = $('<td class="expenseHead">').text(expenseType);
            $newRow.append($expenseTypeCell);

                months.forEach(month => {
                    const actualValue = expensesData[expenseType][month].actual ? parseFloat(expensesData[expenseType][month].actual).toFixed(2) : '0.00';
                    const budgetValue = expensesData[expenseType][month].budget ? parseFloat(expensesData[expenseType][month].budget).toFixed(2) : '0.00';

                    const $expenseCell = [
                        $('<td class="text-right side-content actual">').text(actualValue),
                        $('<td class="text-right side-content budget">').text(budgetValue)
                    ];

                    $expenseCell.forEach(cell => {
                        if (cell.hasClass('budget')) {
                            addEditFunctionality(cell, budgetValue);
                        }
                    });

                    $newRow.append($expenseCell);
                });
                $tableBody.append($newRow);
        });


        let cumulativeSumBudget = 0;
        let cumulativeSumActual = 0;
        const $totalRow = $('<tr class="tr-header td totalCashFlow_head">');
        const $totalHeader = $('<td class="reJson cashFlowHead">').text('Cash Flow');
        $totalRow.append($totalHeader);

        for (let i = 0; i < revenueListBudget.length; i++) {
            const diffBudget = revenueListBudget[i] - expensesListBudget[i];
            const diffActual = revenueListActual[i] - expensesListActual[i];
            cumulativeSumBudget += diffBudget;
            cumulativeSumActual += diffActual;
            const $totalCell = [$('<td class="text-right reJson totalCashFlow">').text(cumulativeSumActual.toFixed(2)),$('<td class="text-right reJson totalCashFlow">').text(cumulativeSumBudget.toFixed(2))];
            $totalRow.append($totalCell);
        }
        $tableBody.append($totalRow);
        $("#workingCapitalValue").text(data.data.working_capital);
        $("#project_owner").val(data.data.project_owner);
        $("#project_owner_email").val(data.data.email);
        $("#project_owner_no").val(data.data.phone_no);
        $("#loading").hide();
    };

    const tableToJson = (tableId) => {
        const table = document.getElementById(tableId);
        const headerCells = Array.from(table.querySelectorAll('thead th:not(.reJson)')).map(cell => cell.textContent.trim());
        const jsonData = {
            date: headerCells.slice(1),
            expenses: {},
            revenue: {}
        };
        const inputs = document.querySelectorAll('td input[type="number"]');
        const selects = document.querySelectorAll('select');

        inputs.forEach(input => {
            let textContent = input.value.trim();
            textContent = textContent.replace(/^0+/, '');
            const tdChange = input.parentElement;
            tdChange.removeChild(input);
            tdChange.textContent = textContent;
            tdChange.classList.add('budget');
            if ($(tdChange).hasClass('budget')) {
               addEditFunctionality($(tdChange), textContent? textContent : 0);
            }
        });

        let validRowsRevenue = [];
        let validRowsExpense = [];
        let isEmpty = false;
        let containsInvalidRowExpense = false;
        let containsInvalidRowRevenue = false;

        const revenueHead = document.querySelector('.totalRevenue_head');
        const revenueHeadindex = Array.from(revenueHead.parentNode.children).indexOf(revenueHead);

        const expenseHead = document.querySelector('.totalExpense_head');
        const expenseHeadindex = Array.from(expenseHead.parentNode.children).indexOf(expenseHead);

        const rowsToCheckRevenue = table.querySelectorAll('tbody tr td.revenueHead');

<!--        const rowsToCheck = table.querySelectorAll('tbody tr td.revenueHead,tbody tr td.expenseHead');-->

        rowsToCheckRevenue.forEach((row, rowIndex) => {
            let isValidRevenue = true;
            validRowsRevenue[rowIndex] = 1;
            const rowData = getData(rowIndex,rowsToCheckRevenue);
            rowsToCheckRevenue.forEach((checkRow, checkIndex) => {
                if (rowIndex != checkIndex) {
                    const checkData = getData(checkIndex,rowsToCheckRevenue);
                    if (rowData == checkData) {
                        validRowsRevenue[rowIndex] = 0;
                        return;
                    }
                }
            });
        });

        const rowsToCheckExpense = table.querySelectorAll('tbody tr td.expenseHead');

        rowsToCheckExpense.forEach((row, rowIndex) => {
            let isValidExpense = true;
            validRowsExpense[rowIndex] = 1;
            const rowData = getData(rowIndex,rowsToCheckExpense);

            rowsToCheckExpense.forEach((checkRow, checkIndex) => {
                if (rowIndex != checkIndex) {
                    const checkData = getData(checkIndex,rowsToCheckExpense);
                    if (rowData == checkData) {
                        validRowsExpense[rowIndex] = 0;
                        return;
                    }
                }
            });
        });

        function getData(index,rowsToCheck) {
            const selectElement = rowsToCheck[index].querySelector('select[id^="prpjectForcast_"]');
            if (selectElement) {
                if(selectElement.selectedOptions.length > 0 ){
                    isEmpty = false;
                    return selectElement.selectedOptions[0].textContent.trim().replace(/\s+/g, '');
                }
                else{
                    isEmpty = true;
                    return isEmpty;

                }
            } else {
                return rowsToCheck[index].textContent.trim().replace(/\s+/g, '');
            }
        }
        rowsToCheckRevenue.forEach((row, index) => {
            if (validRowsRevenue[index] === 0) {
                row.classList.add('invalid-row');
                containsInvalidRowRevenue = true;
            }
            else{
                row.classList.remove('invalid-row');
            }
        });

        rowsToCheckExpense.forEach((row, index) => {
            if (validRowsExpense[index] === 0) {
                row.classList.add('invalid-row');
                containsInvalidRowExpense = true;
            }
            else{
                row.classList.remove('invalid-row');
            }
        });
        if(isEmpty){
            swal("", "Ledger Name Cannot be empty. Please select valid one", "error");
        }
        else if(!isEmpty && !containsInvalidRowExpense && !containsInvalidRowRevenue) {
                rowsToCheckRevenue.forEach((row, index) => {
                    if (row.classList.contains('invalid-row')) {
                        row.classList.remove('invalid-row');
                    }
                    });

                    rowsToCheckExpense.forEach((row, index) => {
                    if (row.classList.contains('invalid-row')) {
                        row.classList.remove('invalid-row');
                    }
                    });
                document.querySelectorAll('select[id^="prpjectForcast_"]').forEach(select => {
                        const textContent = select.options[select.selectedIndex].text;
                        const selectChange = select.parentElement.parentElement;
                        selectChange.removeChild(select.parentElement);
                        selectChange.textContent = textContent;

                        const inputElement = document.createElement('input');
                        inputElement.setAttribute('class', 'revenueHead');
                        inputElement.setAttribute('id', 'prpjectForcast');
                        inputElement.setAttribute('type', 'hidden');
                        inputElement.setAttribute('value', select.value);
                        selectChange.appendChild(inputElement);
                        const button = selectChange.querySelector('button');
                        if (button) {
                            button.style.display = 'none';
                        }
                    });


        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            const cells = row.querySelectorAll('td:not(.expenseHead):not(.revenueHead)');
            const firstCells = row.querySelectorAll('td');
            const cellvalues = [];
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('td:not(.expenseHead):not(.revenueHead)');
                const firstCells = row.querySelectorAll('td');
                const firstCell = firstCells[0];
                let category = '';
                if (firstCell.classList.contains('expenseHead')) {
                    category = 'Expenses';
                } else if (firstCell.classList.contains('revenueHead')) {
                    category = 'Income';
                }

                const cellValues = {
                    budget: [],
                    actual: []
                };
                row.querySelectorAll('td.budget').forEach(cell => {
                    const textBox = cell.querySelector('input[type="text"]');
                    cellValues.budget.push(textBox ? textBox.value.trim() : cell.textContent.trim());
                });
                row.querySelectorAll('td.actual').forEach(cell => {
                    const textBox = cell.querySelector('input[type="text"]');
                    cellValues.actual.push(textBox ? textBox.value.trim() : cell.textContent.trim());
                });
                if (cellValues.budget.length || cellValues.actual.length) {
                    cells.forEach((cell, index) => {
                        if (index >= 0 && index < 12 && !cell.classList.contains('')) {
                            const month = headerCells[index + 1];
                            const valueBudget = cellValues.budget[index] || '';
                            const valueActual = cellValues.actual[index] || '';

                            if (category === 'Expenses') {
                                let key = $(firstCell).text();
                                key = key.replace("Primary Budget Head", "");
                                if (!jsonData.expenses[key]) {
                                    jsonData.expenses[key] = {};
                                }
                                jsonData.expenses[key][month] = {
                                    budget: valueBudget,
                                    actual: valueActual
                                };
                            }
                            else if (category === 'Income') {
                                let key = $(firstCell).text();
                                key = key.replace("Primary Budget Head", "");
                                if (!jsonData.revenue[key]) {
                                    jsonData.revenue[key] = {};
                                }
                                jsonData.revenue[key][month] = {
                                    budget: valueBudget,
                                    actual: valueActual
                                };
                            }
                        }
                    });
                }
            });
        });
        const saveData = {"data" : jsonData}
        const projectId = $('#project').val();
        const workingCapitalValue = $('#workingCapitalValue').text();
        const projectOwner = $('#project_owner').val().trim();
        const projectOwnerEmail = $('#project_owner_email').val().trim();
        const projectOwnerNo = $('#project_owner_no').val().trim();

            try {
                var forecast_value = { "revenue": jsonData.revenue, "expenses": jsonData.expenses };
                $("#loading").show();
                $.ajax({
                    url: '/erp/sales/json/save_forecast/',
                    type: "POST",
                    data: {
                        "project_id": projectId,
                        "forecast": JSON.stringify(forecast_value),
                        "working_capital": workingCapitalValue,
                        "project_owner": projectOwner,
                        "email": projectOwnerEmail,
                        "phone_no": projectOwnerNo
                    },
                    success: function(response) {
                        if (response.response_code === 200) {
                            $("#loading").hide();
                            swal("", "Changes are added successfully.", "success");
                        } else if (response.response_code === 400) {
                            swal("", "Not able to update values. Please try again.", "error");
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error(xhr.responseText);
                        swal("", "An error occurred. Please try again later.", "error");
                    }
                });
            }
            catch (error) {
                console.error(error);
                swal("", "An error occurred. Please try again later.", "error");
            }
        }
        else{
            swal({title: "", text: "Already Primary budget head added. You can edit the respective row", type: "error"});
        }
        }

let selectBoxAccountGroup;
let rowNumber = 1;

    function addNewRow(types) {
        const currentRowNumber = rowNumber++;
        const newRow = document.createElement('tr');
        let className = '';
        let selectBoxValue = '';
        if (types === "revenue") {
            className = 'revenueHead';
            type="revenue";
            selectBoxValue =responseData.data.revenue_accounts_options;
        }
        else {
            className = 'expenseHead';
            type="expense";
            selectBoxValue = responseData.data.expeses_accounts_options;
        }
        const selectBoxAccountGroup = $(`<select id="prpjectForcast_${currentRowNumber}" class="form-control" style="display: inline-block; width: 80%;" value="">`);
        const selectBox = $(`<select id="prpjectForcastAccount_${currentRowNumber}" class="form-control" style="display: inline-block; width: 80%;margin-top:5px" value="">`);
        const existingValues = [];
        $('td input.' + className).each(function() {
            existingValues.push($(this).val());
        });
        let disabledCount = 0;
        const pleaseSelectOption = $('<option>');
        pleaseSelectOption.val('');
        pleaseSelectOption.text('Please Select');
        selectBox.append(pleaseSelectOption);
        for (let i = 0; i < selectBoxValue.length; i++) {
        const option = $('<option>');
        option.val(selectBoxValue[i][0]);
        option.text(selectBoxValue[i][1]);
        if (existingValues.includes(selectBoxValue[i][0])) {
            option.prop('disabled', true);
            disabledCount++;
        }
        selectBox.append(option);
    }

    const htmlContent = `
        <td class="${className}"><div style="width:250px"><label class="reJson">Primary Budget Head</label><br/>${selectBox.prop('outerHTML')}<button style="display: inline-block; width: 16%; margin: 5px;" class="pull-right" onclick="deleteRow(&quot;revenue&quot;)">-</button><br/><label class="reJson">Secondary Budget Head</label><div id="revHead_${currentRowNumber}">${selectBoxAccountGroup.prop('outerHTML')}</div></td>
        <td class="text-right actual">0.00</td>
        <td class="text-right budget editable"><input type="text" class="editable-textbox" value='0' onblur="addwithTotal(event,'${type}')"></td>
        <td class="text-right actual">0.00</td>
        <td class="text-right budget editable"><input type="text" class="editable-textbox" value='0' onblur="addwithTotal(event,'${type}')"></td>
        <td class="text-right actual">0.00</td>
        <td class="text-right budget editable"><input type="text" class="editable-textbox" value='0' onblur="addwithTotal(event,'${type}')"></td>
        <td class="text-right actual">0.00</td>
        <td class="text-right budget editable"><input type="text" class="editable-textbox" value='0' onblur="addwithTotal(event,'${type}')"></td>
        <td class="text-right actual">0.00</td>
        <td class="text-right budget editable"><input type="text" class="editable-textbox" value='0' onblur="addwithTotal(event,'${type}')"></td>
        <td class="text-right actual">0.00</td>
        <td class="text-right budget editable"><input type="text" class="editable-textbox" value='0' onblur="addwithTotal(event,'${type}')"></td>
        <td class="text-right actual">0.00</td>
        <td class="text-right budget editable"><input type="text" class="editable-textbox" value='0' onblur="addwithTotal(event,'${type}')"></td>
        <td class="text-right actual">0.00</td>
        <td class="text-right budget editable"><input type="text" class="editable-textbox" value='0' onblur="addwithTotal(event,'${type}')"></td>
        <td class="text-right actual">0.00</td>
        <td class="text-right budget editable"><input type="text" class="editable-textbox" value='0' onblur="addwithTotal(event,'${type}')"></td>
        <td class="text-right actual">0.00</td>
        <td class="text-right budget editable"><input type="text" class="editable-textbox" value='0' onblur="addwithTotal(event,'${type}')"></td>
        <td class="text-right actual">0.00</td>
        <td class="text-right budget editable"><input type="text" class="editable-textbox" value='0' onblur="addwithTotal(event,'${type}')"></td>
        <td class="text-right actual">0.00</td>
        <td class="text-right budget editable"><input type="text" class="editable-textbox" value='0' onblur="addwithTotal(event,'${type}')"></td>
    `;

    newRow.innerHTML = htmlContent;

    const rows = document.querySelectorAll('tr.tr-header');
    let revenueRow;
    rows.forEach(row => {
        row.querySelectorAll('th,td').forEach(th => {
            if (types == "revenue" && th.textContent.trim() === 'Expenses+') {
                revenueRow = row;
                return;
            }
            else if (types == "expense" && th.textContent.trim() === 'Cash Flow') {
                revenueRow = row;
                return;
            }
        });
    });
    if (revenueRow) {
        revenueRow.insertAdjacentElement('beforebegin', newRow);
    }
    $('#prpjectForcastAccount_' + currentRowNumber).on('change', function() {
        fetchData();
    });

    function fetchData() {
        $.ajax({
            url: "/erp/sales/json/getledgername/",
            type: "GET",
            data: {"project_id": $('#project').val(), "account_grp_id": $('#prpjectForcastAccount_' + currentRowNumber).val()},
            success: function(response) {
                populateDropdown(response);
            },
            error: function(xhr, status, error) {
                console.error(error);
            }
        });
    }

    function populateDropdown(data) {
        const selectBoxAccountGroupValue = data.data;
        selectBoxAccountGroup.empty();
        for (let key in selectBoxAccountGroupValue) {
            const option = $('<option>');
            option.val(key);
            option.text(selectBoxAccountGroupValue[key]);
            selectBoxAccountGroup.append(option);
        }
        $('#revHead_' + currentRowNumber).empty();
        $('#revHead_' + currentRowNumber).append(selectBoxAccountGroup.prop('outerHTML'));
        }
    }
    function deleteRow(type) {
    const button = event.target;
    const row = button.closest('tr');
    if (row) {
        row.remove();
    }
    }
    function checkInputsForZero() {
            tableToJson('revenueTable');
    }
    function addwithTotal(event, type) {
        const input = event.target;
        const td = input.parentElement;
        const inputs = document.querySelectorAll('td input[type="text"]');
        const columnIndex = Array.from(td.parentElement.children).indexOf(td);
        const revenueRows = type === 'revenue' ? document.querySelectorAll('.totalRevenue') : document.querySelectorAll('.totalExpense');
        let revenueHead, revenueHeadindex, expenseHead, expenseHeadindex;
        if (type === 'revenue') {
            revenueHead = document.querySelector('.totalRevenue_head');
            revenueHeadindex = Array.from(revenueHead.parentNode.children).indexOf(revenueHead);
            expenseHead = document.querySelector('.totalExpense_head');
            expenseHeadindex = Array.from(expenseHead.parentNode.children).indexOf(expenseHead);
        }
        else if (type === 'expense') {
            revenueHead = document.querySelector('.totalExpense_head');
            revenueHeadindex = Array.from(revenueHead.parentNode.children).indexOf(revenueHead);
            expenseHead = document.querySelector('.totalCashFlow_head');
            expenseHeadindex = Array.from(expenseHead.parentNode.children).indexOf(expenseHead);
        }

        const rows = document.querySelectorAll('tbody tr');
        let sum = 0;

        for (let i = revenueHeadindex + 1; i < expenseHeadindex; i++) {
            const cells = rows[i].querySelectorAll('td');
            const cellContent = cells[columnIndex].textContent.trim();
            const value = cells[columnIndex].querySelector('input') ? cells[columnIndex].querySelector('input').value.trim() : cellContent;
            if (!isNaN(value)) {
                sum += parseFloat(value);
            }
        }

        const targetCell = revenueRows[columnIndex];
        targetCell.textContent = sum;
        updateTable();
        function updateTable(){
        for(i=columnIndex;i<=24;i++){
            revenueHead = document.querySelector('.totalRevenue_head');
            revenueHeadindex = Array.from(revenueHead.parentNode.children).indexOf(revenueHead);

            expenseHead = document.querySelector('.totalExpense_head');
            expenseHeadindex = Array.from(expenseHead.parentNode.children).indexOf(expenseHead);


            const revenueTotal = rows[revenueHeadindex].querySelectorAll('th,td');
            const revenueTotalVal = parseFloat(revenueTotal[i].textContent.trim());

            const expenseTotal = rows[expenseHeadindex].querySelectorAll('td,th');
            const expenseTotalVal = parseFloat(expenseTotal[i].textContent.trim());

            cashFlowHead = document.querySelector('.totalCashFlow_head');
            cashFlowHeadindex = Array.from(cashFlowHead.parentNode.children).indexOf(cashFlowHead);

            const cashFlowTotal = rows[cashFlowHeadindex].querySelectorAll('td,th');
            <!--    const cashFlowTotalVal = cashFlowTotal[i].textContent.trim();-->

            if(i > 2 ){
            const pastCashFlowVal = parseFloat(cashFlowTotal[i-2].textContent.trim());
            cashFlowTotal[i].textContent = parseFloat((pastCashFlowVal + revenueTotalVal) - expenseTotalVal);
            }
            else{
                cashFlowTotal[i].textContent = parseFloat(revenueTotalVal- expenseTotalVal);
            }
        }
    }
    }

    function changeLogActivityInit(){
        const projectId = $('#project').val();
        $("#loading").show();
        $.ajax({
            url: "/erp/sales/json/forecast_versions/",
            type: "GET",
            data : { "project_id" : projectId },
            success: function (forcastVersions) {
            $("#loading").hide();
                if (forcastVersions['response_code'] == 200)
                {
                    x = forcastVersions['data'];
                    if (x.length > 0){
                        var row = "";
                        for(var i = 0; i < x.length; i++) {
                            var obj = x[i];
                            var row = "";
                            for (var i = 0; i < x.length; i++) {
                                var obj = x[i];
                                row += `<li class="history-log-part" id="history-part-${i}">
                                            <span class="history-log-username">${obj.created_by}</span><br/>
                                            <a class="history-log-date" onclick="getForecastData('${obj.doc_id}','${obj.created_by}','${obj.created_on}')">${obj.created_on}</a>
                                        </li>`;
                            }
                            $("#loadingmessage_changelog_listing_ie").hide();

                        }
                    }
                    else {
                        var row = `<span style="width: 100%;display: block;margin-left: 15%;">No change detected. </span>`;

                    }
                    $(".history-project_forecast").empty();
                    $(".history-project_forecast").append(row);
                }
            }
        });
    }
    function getForecastData(docId,name,dateTime){
        const date = new Date(dateTime);
        const options = { month: 'short', day: 'numeric', year: 'numeric', hour: 'numeric', minute: 'numeric', hour12: true };
        $('.project_forecast').addClass('hide');
        $('#forecastSave').prop('disabled', true);
         const projectId = $('#project').val();
         $.ajax({
                url: "/erp/sales/json/getforecastdata/",
                type: "GET",
                data : { "project_id" :  projectId, "object_id" : docId },
                success: function (forcastData) {
                    var varsions_cash_flow  = { 'data' : forcastData['data']['forecast'] }
                    populateTable(varsions_cash_flow, "#revenueTable");
                }
        });
        $('#versionChangeNote').removeClass('hide');
       $('#versionChangeNote').html('<b> Note: </b> <i>The budget value cannot be updated. It was updated by <b>' + name +'</b>' + ' on ' + date.toLocaleString('en-US', options) + '</i>');
    }
    $(document).on("click", ".closeBtn", function() {
    close();
    });
    function close() {
    $('.project_forecast').addClass('hide');
    }
    function validationEmail(input) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        var email = input.value.trim();
        if (email !='' && !emailRegex.test(email)) {
            swal("", "Enter Valid Email address","error");
            input.classList.add('error-border');
        } else {
            input.setCustomValidity("");
            input.classList.remove('error-border');
        }
    }
    function cashFlowOverView(){
       window.location.href = '/erp/sales/get_project_wise_cashflow_overview/';
    }
    function consolidatedCashflow(url,openTarget){
        $("#consolidated_cashflow_form").attr("action", url);
		$("#project_id").val($('#project').val());
		$("#consolidated_cashflow_form").attr("target", openTarget).submit();
        }

</script>
{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
{% endblock %}
