<link rel="stylesheet" type="text/css" href='https://xserp.in/site_media/css/bootstrap.css'>


<style type="text/css">
	.po_template_view{
		color: #000;
		font-family: 'Times New Roman';
	}

	.po_template_view table {
		width:100%;
	}
	.po_template_view table, th, td {
		border-collapse: collapse;
	}

	.po_template_view .table.row-seperator th,
	.po_template_view .table.row-seperator td,
	.po_template_view .table.row-seperator th,
	.po_template_view .table.row-seperator td {
	    border-top: 1px solid #666666 !important;
	    border-bottom: 1px solid #666666 !important;
	}

	.po_template_view .table.column-seperator th,
	.po_template_view .table.column-seperator td,
	.po_template_view .table.column-seperator th,
	.po_template_view .table.column-seperator td {
	    border-right: 1px solid #666666 !important;
	    border-left: 1px solid #666666 !important;
	}

	.po_template_view th{
		text-align: center;
		line-height: 25px;
		font-size:10pt;
	}
	.po_template_view td{
		line-height: 25px;
		font-size:10pt;
	}
	.po_template_view th {
		font-size:11pt;
	}

	.po_template_view .enterprise_details{
		font-size:12pt;
	}
	.po_template_view .vendors{
		font-weight:bold;
	}
	.po_template_view .page_title{
		font-size: 16pt;
		margin-left: 5px;
	}
	.po_template_view .po_details{
		font-weight: bold;
		font-size:12pt;
	}
	.po_template_view .total_price{
		font-size:11pt;
		font-weight: bold;
		margin-top: 4px;
		margin-bottom: 3px;
		text-align:right;
		margin-right:-295px;
	}
	.po_template_view .total_gst{
		text-align: right;
		border-bottom:hidden;
		border-left:hidden;
		border-right:hidden;
		line-height:25px;
	}
	.po_template_view .amt{
		font-size: 11pt;
		font-weight: bold;
		margin-top: 4px;
		float:right;
		margin-right:-14px;
	}
	.po_template_view .sub_total th ,
	.po_template_view .sub_total td{
		text-align:right;
		font-weight: normal;
		padding-right:3px;
		font-size:11pt;
	}
	.po_template_view .total th ,
	.po_template_view .total td{
		text-align:right;
	}

	.po_template_view .terms_condition{
		margin-top: 50px;
		font-size: 11pt;
		line-height:25px;
	}
	.po_template_view .terms_condition_bottom{
		margin-bottom:20px;
	    margin-top:20px;
	}
	.po_template_view .notes{
		font-size:11pt;
	}
	.po_template_view .notes_bottom{
		margin-bottom:10px;
	    margin-top:5px;
	}
	.po_template_view .acknowledge_details{
		font-size:10pt;
	}
	.po_template_view .payment_term {
	  	width:20%;
	  	float: left;
	}
	.po_template_view .payment_details{
	 	width: calc(60% - 1em);
	}

	.po_template_view .reg_details{
			font-size:11pt;
	}
	.po_template_view .grand_total {
		border-left:hidden;
		border-right:hidden;
		border-bottom:hidden;
		font-size:11pt;
	}

	.po_template_view .purchase_order{
		margin-left: -4px;
		font-size:10pt;
	}

	.po_template_view .subject{
		text-align: justified;
		margin-top: 6px;
		font-size: 12pt;
	}

	.po_template_view .vendor_list {
		width:30%;
		float: left;
		font-weight:bold;
		font-size:12pt;

	}

	.po_template_view .vendor_data{
		width: calc(60% - 1em);
		font-size:10pt;

	}

	.po_template_view .po_list{
		width: 50%;
		float: left;
		font-weight: bold;
		margin-left:40px;
		font-size:12pt;
	}

	.po_template_view .po_data{
		width: calc(60% - 1em);
		float: left;
		margin-left:170px;
		margin-top:-18px;
		font-size:12pt;
	}

	.po_template_view .payment_term {
		width:20%;
		float: left;
	}

	.po_template_view .payment_details{
		width: calc(60% - 1em);
	}

	.po_template_view .annexture_po,
	.po_template_view .delivery_schedules_po{
		font-size:11pt;
		text-align:center;
	 	margin-bottom:30px;
	 	margin-top:30px;
	}

	@font-face {
        font-family: 'Times New Roman';
        font-style: normal;
        font-weight: 400;
        src: url(https://www.xserp.in/site_media/fonts/times.ttf) format('truetype');
	}

	.pp-document .for-non-pp {
	    display: none;
	}
</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/bootstrap.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">

	<div class="po_template_view [% if source.type == 2 %] pp-document [% endif %]" style="border-color: transparent; width: 100%;">
		<div>
			<div class="header-banner-container col-sm-12">
				<div class="text-left pull-left misc_header-left_banner" style="padding-left: 0px; width: 34%;">
					<img class="image_uploader-left img-responsive">
				</div>
				<div class="text-center pull-left misc_header-center_banner" style="width: 33%;">
					<img class="image_uploader-center img-responsive" style="margin:0 auto;">
				</div>
				<div class="text-right pull-left misc_header-right_banner" style="padding-right: 0;">
					<img class="image_uploader-right img-responsive" style="float: right;">
				</div>
			</div>
	        <div class="col-sm-8">
	            <img src="{{enterprise_logo}}" class="pdf_company_logo" style="height: 84px;"><br>
	            {% if header_res.company.company_name.print %}
	            	 <span class="pdf_company_name" style="font-size: 24px; font-family: Roboto;">{{source.enterprise.name}}</span><br>
            	{% endif %}
            	{% if header_res.company_info.print_address %}
		            <div class="pdf_company_address pdf_company_address_container" style="font-size: 12px;">
                        {{ enterprise_address }}
		            </div>
	            {% endif %}
	            {% if header_res.company_info.print_phone_no %}
					<span class="pdf_company_contact pdf_company_address_container" style="font-size: 12px;">
						<b>Ph:</b> {% if source.enterprise.primary_contact_details.contact.phone_no %} {{ source.enterprise.primary_contact_details.contact.phone_no }} {% endif %}
					</span>
				{% endif %}
				{% if header_res.company_info.print_email %}
					<span class="pdf_company_email pdf_company_address_container" style="font-size: 12px;">
						<b>Email:</b>{% if source.enterprise.primary_contact_details.contact.email %} {{ source.enterprise.primary_contact_details.contact.email }} {% endif %}
					</span>
				{% endif %}
				{% if header_res.company_info.print_fax %}
					<span class="pdf_company_fax pdf_company_address_container" style="font-size: 12px;">
						<b>Fax:</b>{% if source.enterprise.primary_contact_details.contact.fax_no %} {{ source.enterprise.primary_contact_details.contact.fax_no }} {% endif %}
					</span>
				{% endif %}
	        </div>

	        <div class="col-sm-4 pdf_company_address_container" style="font-size: 12px;">
	            <div class="page_title pdf_form_name_text" style="font-size: 21px;">{{form_name}}</div>
	            {% for reg_detail in po_reg_details %}
					<span class="pdf_registration_" style="font-size:{{header_res.font_size}}px;">
						<b class="pdf_enterprise_details">{{ reg_detail.label }}:</b>
						<span>{{ reg_detail.details }}</span>
						<br>
					</span>
				{% endfor %}
	        </div>
	    </div>

	    <div class="clearfix"></div><br>

	    <div>
                <div class="col-sm-7  pdf_company_details_container for-non-pp" style="font-size: 12px;">
	            {% if header_res.vendor.name.print %}
		             <div class="pdf_vendor_to_enterprise_name">
                        <span class="vendor_list pdf_company_details_container vendorname_to_text" style="font-size: 12px;">Vendor Name</span>
						<span class="vendor_data pdf_company_details_container" style="font-size: 12px;">: <b>{{ source.supplier.name }}</b></span>
		            </div>
		        {% endif %}
		        {% if header_res.vendor.address.print %}
		             <div class="pdf_vendor_to_address pdf_company_details_container" style="font-size: 12px;">
	                    <span class="vendor_list pdf_company_details_container vendoraddress_to_text" style="font-size: 12px;">Address</span>
			            <span class="vendor_data">
	                        <span class="pdf_company_details_container" style="font-size: 12px;">: {{ source.supplier.address_1 }}, {{ source.supplier.address_2 }} </span><br>
			                <span class="pdf_company_details_container" style="margin-left: 155px; font-size: 12px;">
			                	{% if source.supplier.city != '' %}
				                    {{ source.supplier.city }},
				                {% endif %}
				                {% if source.supplier.state != '' %}
				                    {{ source.supplier.state }},
				                {% endif %}
				                {% if source.supplier.pin_code != '' and source.supplier.pin_code != 'None' %}
				                    {{ source.supplier.pin_code }},
				                {% endif %}
			                	{% for country in country_list %}
									{% if country.country_code == source.supplier.country_code %}
										{{ country.country_name|upper }}
									{% endif %}
								{% endfor %}
			                </span>
				        </span>
		            </div>
	            {% endif %}
	            {% if header_res.vendor.code.print %}
		            <div class="pdf_vendor_to_enterprise_code pdf_company_details_container" style="font-size: 12px;">
                        <span class="vendor_list pdf_company_details_container vendorcode_to_text" style="font-size: 12px;">Vendor Code</span>
		                <span class="vendor_data pdf_company_details_container" style="font-size: 12px;">: {{ source.supplier.code }}
			               {% if reg_detail in source.supplier.registration_details != '' %}
			                    <b style="font-size: 12px;" class="pdf_company_details_container">GSTIN</b> :
			                    {% for reg_detail in source.supplier.registration_details %}
									{% if reg_detail.label == "GSTIN" %}
										{{ reg_detail.details }}
									{% endif %}
								{% endfor %}
                            {% endif %}

		                </span>
		            </div>
	            {% endif %}
	            {% if header_res.vendor.print_phone_no and source.supplier.primary_contact_details.contact.phone_no != "" %}
		             <div class="pdf_vendor_to_contact pdf_company_details_container" style="font-size: 12px;">
						<span class="vendor_list pdf_company_details_container" style="font-size: 12px;">Phone No </span>
						<span class="vendor_data pdf_company_details_container" style="font-size: 12px;">: {{ source.supplier.primary_contact_details.contact.phone_no }}</span>
		             </div>
	            {% endif %}
	            {% if header_res.vendor.print_email and source.supplier.primary_contact_details.contact.email != "" %}
			        <div class="pdf_vendor_to_email pdf_company_details_container" style="font-size: 12px;">
		               <span class="vendor_list pdf_company_details_container" style="font-size: 12px;">E-Mail </span>
		               <span class="vendor_data pdf_company_details_container" style="font-size: 12px;">: {{ source.supplier.primary_contact_details.contact.email }}</span>
		            </div>
	            {% endif %}
	        </div>

	        <div class="col-sm-5 pdf_company_details_container" style="font-size: 12px;">
				<span class="pdf_po_no "><b class="pdf_others_details pdf_po_no_txt po_list pdf_company_details_container" style="font-size: 12px;">P.O No</b> <span class="po_data pdf_company_details_container" style="font-size: 12px;"> : {{ source.getCode }} <i>{% if source.revisions.length > 0 %} (#{{ source.revisions.length }}) {% endif %}</i></span><br></span>
		        <span class="pdf_podate_span"><b class="pdf_others_details pdf_podate_txt po_list pdf_company_details_container" style="font-size: 12px;">P.O Date</b> <span class="pdf_po_date po_data pdf_company_details_container" style="font-size: 12px;">12/07/2021</span><br></span>
		        {% if header_res.field_name.indent_no.print %}
		            <span class="pdf_indent_no"><b class="pdf_others_details pdf_indent_no_txt po_list pdf_company_details_container" style="font-size: 12px;">Indent No</b> <span class="po_data pdf_company_details_container" style="font-size: 12px;">: {% if source.indent %} {{ source.indent.getCode }} {% else %} -NA- {% endif %}</span><br></span>
		        {% endif %}
		        {% if header_res.field_name.currency.print %}
			        <span class="pdf_transport_currency"><b class="pdf_others_details pdf_currency_txt po_list pdf_company_details_container" style="font-size: 12px;">Currency</b> <span class="po_data pdf_company_details_container" style="font-size: 12px;">: {{ source.currency.code }}</span><br></span>
		        {% endif %}
		        {% if header_res.field_name.quotn_ref_no.print %}
			        <span class="pdf_quotn_ref hide"><b class="pdf_others_details pdf_quotn_ref_txt po_list pdf_company_details_container for-non-pp" style="font-size: 12px;">Quotn Ref No</b><span class="po_data pdf_company_details_container" style="font-size: 12px;">: {{ source.quotation_ref_no }}</span><br></span>
		        {% endif %}
		        {% if header_res.field_name.quotn_date.print %}
			       <span class="pdf_quotn_date hide"><b class="pdf_others_details pdf_quotn_date_txt po_list pdf_company_details_container for-non-pp" style="font-size: 12px;">Quotn Date</b> <span class="po_data pdf_company_details_container" style="font-size: 12px;">: {{ quotation_date }}</span><br></span>
		        {% endif %}
		        {% if source.is_blanket_po %}
			        <span>
				        <span class="po_list"><b>Validity Period</b></span>
			        	<span class="po_data">: {{ valid_since }} - {{ valid_till }}</span><br>
			        </span>
		        {% endif %}
	        </div>
	    </div> <br>


		<div>
		    <div class="col-sm-12 greeting_subject for-non-pp" style="font-size:16px">
								    Dear Sir/Madam,<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sub: Job Order - Reg

							</div>
		    <div class="col-sm-12 greeting_mailbody for-non-pp" style="font-size:16px">
								    Certified that the particulars given above are true and correct and the amount indicated represents the price actually charged.

							</div>
				{% if misc_res.print_summary_first_page and appendix_pages > 0 %}
					<table border="1" bordercolor="#A9A9A9" class="row-seperator column-seperator">
						<tr>
							{% if item_res.item_table.sno.print %}
								<th rowspan="2">{{ item_res.item_table.sno.label }}</th>
							{% endif %}
							<th rowspan="2">{{ po_item_desc_header_1 }}<br>{{ po_item_desc_header_2 }}</th>
							{% if item_res.item_table.hsnsac.print and not item_res.item_table.hsnsac.print_in_itemdetails %}
								<th rowspan="2">{{ item_res.item_table.hsnsac.label }}</th>
							{% endif %}
							{% if item_res.item_table.quantity.print %}
								<th rowspan="2">{{ item_res.item_table.quantity.label }}</th>
							{% endif %}
							{% if item_res.item_table.units.print %}
								<th rowspan="2">{{ item_res.item_table.units.label }}</th>
							{% endif %}
							{% if source.type != 2 %}
								{% if item_res.item_table.unit_price.print %}
									<th rowspan="2">{{ item_res.item_table.unit_price.label }}<br>{{ source.currency.code }}</th>
								{% endif %}
								{% if item_res.item_table.discount.print %}
									<th rowspan="2">{{ item_res.item_table.discount.label }}<br>(%)</th>
								{% endif %}
								{% if item_res.item_table.taxable_amount.print %}
									<th rowspan="2">{{ item_res.item_table.taxable_amount.label }}<br>{{ source.currency.code }}</th>
								{% endif %}
								<th colspan="2">CGST</th>
								<th colspan="2">SGST</th>
								<th colspan="2">IGST</th>
							{% endif %}
						</tr>
						{% if source.type != 2 %}
							<tr>
								<th>%</th>
								<th>{{ source.currency.code }}</th>
								<th>%</th>
								<th>{{ source.currency.code }}</th>
								<th>%</th>
								<th>{{ source.currency.code }}</th>
							</tr>
						{% endif %}
						<tr class="sub_total for-non-pp">
							<td colspan="13" style="text-align: center; line-height:30px; font-size:11px;"><b>AS PER ANNEXURE TO PO NO:<br>{{ source.getCode }}</b>
							</td>
						</tr>
						<tr class="sub_total for-non-pp">
							<td colspan="{% if item_res.item_table.hsnsac.print and not item_res.item_table.hsnsac.print_in_itemdetails %}3{% else %}2{% endif %}">Sub Total</td>
							<td colspan="2">{{ total_quantity }}</td>
							<td>&nbsp;</td>
							<td colspan="2">{{ total_value}}</td>
							<td colspan="2">{{ total_cgst_value }}</td>
							<td colspan="2">{{ total_sgst_value }}</td>
							<td colspan="2">{{ total_igst_value }}</td>
						</tr>
						{% for tax in po_taxes %}
							<tr>
								<td colspan="2" class="total_gst for-non-pp">{{ tax.tax_name }}</td>
								<td colspan="3" class="total_gst for-non-pp">{{ tax.tax_rate }}</td>
								<td class="total_gst for-non-pp" style="text-align:left">%</td>
								<td class="total_gst for-non-pp">{{ tax.tax_value }}</td>
								<td class="total_gst for-non-pp"></td>
							</tr>
						{% endfor %}
						{% if source.type != 2 %}
							{% if po_taxes %}
								<tr>
									<td style=":border-top:1px solid #666666;"></td>
								</tr>
							{% endif %}
						{% endif %}
						<tr>
							<td colspan="9" class="grand_total for-non-pp"><b>Grand Total</b></td>
							<td colspan="2" class="grand_total for-non-pp">{{ source.currency.code }}</td>
							<td colspan="2" class="grand_total for-non-pp">{{ source.total }}</td>
						</tr>
						{% if source.type != 2 %}
							<tr>
								<td colspan="7" class="grand_total"></td>
								<td colspan="6" style=":border-top:1px solid #666666;"></td>
							</tr>
						{% endif %}
					</table>
				{% else %}
					<style>
	.po_template_view .table.row-seperator th,
	.po_template_view .table.row-seperator td,
	.po_template_view .table.row-seperator th,
	.po_template_view .table.row-seperator td {
	    border-top: 1px solid #666666 !important;
	    border-bottom: 1px solid #666666 !important;
	}

	.po_template_view .table.column-seperator th,
	.po_template_view .table.column-seperator td,
	.po_template_view .table.column-seperator th,
	.po_template_view .table.column-seperator td {
	    border-right: 1px solid #666666 !important;
	    border-left: 1px solid #666666 !important;
	}

</style>
<table class="table item_table table-bordered row-seperator column-seperator" id="item_table" style="font-size:11px; width: 100%">
    <thead style="font-size: 12px;">
        <tr class="row_seperator column_seperator header_shading shaded">
            <th class="text-center td_sno td_sno_text" rowspan="2" scope="col" style="width: 6%;">S.NO</th>
            <th class="text-center td_description td_description_text" rowspan="2" style="width: 31%;">Description</th>
            <th class="text-center pdf_item_hsn_code_txt td_hsn_code for-non-pp" rowspan="2" style="width: 7%;">HSN/SAC</th>
            <th class="text-center td_qty td_qty_text" rowspan="2" scope="col" style="width: 6%;">Qty</th>
            <th class="text-center td_uom td_uom_text hide" rowspan="2" scope="col" style="width: 6%;">Uom</th>
            <th class="text-center td_price td_price_text for-non-pp" rowspan="2" scope="col" style="width: 6%;">Price</th>
            <th class="text-center td_disc td_disc_text for-non-pp" rowspan="2" scope="col" style="width: 6%;">Disc (%)</th>
            <th class="text-center td_tax td_tax_text for-non-pp" rowspan="2" scope="col" style="width: 8%;">Total</th>
			<th class="text-center td_cgst tax_rate_column for-non-pp" colspan="2" style="width: 12%; ">CGST</th>
			<th class="text-center td_sgst tax_rate_column for-non-pp" colspan="2" style="width: 12%; ">SGST</th>
			<th class="text-center td_igst tax_rate_column for-non-pp" colspan="2" style="width: 12%;  border-right-color: #ccc;">IGST</th>
			<th class="text-center td_tax_amt tax_one_column hide for-non-pp" rowspan="2" style="width: 5%;">CGST</th>
			<th class="text-center td_tax_amt tax_one_column hide for-non-pp" rowspan="2" style="width: 5%;">SGST</th>
			<th class="text-center td_tax_amt tax_one_column hide for-non-pp" rowspan="2" style="width: 5%;">IGST</th>
			</tr>
			<tr class="row_seperator column_seperator tax_rate_column header_shading tr_second_row shaded for-non-pp">
			<th class="text-center td_cgst_rate td_tax_rate td_gst_rate" style="width: 5%;">%</th>
			<th class="text-center td_cgst_amt td_tax_amt td_gst_amt" style="width: 5%;">INR</th>
			<th class="text-center td_sgst_rate td_tax_rate td_gst_rate" style="width: 5%;">%</th>
			<th class="text-center td_sgst_amt td_tax_amt td_gst_amt" style="width: 5%;">INR</th>
			<th class="text-center td_igst_rate td_tax_rate td_gst_rate" style="width: 5%;">%</th>
			<th class="text-center td_igst_amt td_tax_amt td_gst_amt" style="width: 5%;">INR</th>
		</tr>
	</thead>
	<tbody style="font-size: 12px;">
		{% for material in po_item_details %}
			<tr class="row_seperator column_seperator row_shading item_2 shaded">
				<td class="text-center td_sno" style="width: 6%;">{{forloop.counter}}</td>
				<td><span class="pdf_item_name"><span class="pdf_item_name_txt"></span>{{ material.material_name }}<br></span>
					<span class="pdf_item_drawing_number"><span class="pdf_item_drawing_number_txt">Code:</span>{{ material.material_drawing_no }}<br></span>
					<span class="pdf_item_hsn_code hide for-non-pp"><i class="pdf_item_hsn_code_txt">HSN/SAC</i> {{ material.hsn_code }}<br></span>
					<span class="tax_in_description hide for-non-pp">{CGST @ 2.5: 6.65 - SGST @ 2.5: 6.65 - IGST @ 5: 12.96}</span>
				</td>
				<td class="text-center td_hsn_code for-non-pp" style="width: 7%;">{{ material.hsn_code }}</td>
				<td class="text-right td_qty">{{ material.material_quantity|floatformat:2 }}<br><span class="pdf_unit_in_price">({{ material.material_unit }})</span></td>
				<td class="text-center td_uom hide">{{ material.material_unit }}</td>
				<td class="text-right td_price for-non-pp">{{ material.material_rate|floatformat:2 }}</td>
				<td class="text-right td_disc for-non-pp">{{ material.material_discount }}</td>
				<td class="text-right td_tax for-non-pp">{{ material.material_taxable_value }}</td>
				<td class="text-right td_cgst tax_rate_column td_gst_rate for-non-pp">{{ material.cgst_rate }}</td>
				<td class="text-right td_cgst tax_rate_column td_gst_amt for-non-pp">{{ material.cgst_value }}</td>
				<td class="text-right td_sgst tax_rate_column td_gst_rate for-non-pp">{{ material.sgst_rate }}</td>
				<td class="text-right td_sgst tax_rate_column td_gst_amt for-non-pp">{{ material.sgst_value }}</td>
				<td class="text-right td_igst tax_rate_column td_gst_rate for-non-pp">{{ material.igst_rate }}</td>
				<td class="text-right td_igst tax_rate_column td_gst_amt for-non-pp" style="border-right-color: #ccc;">{{ material.igst_value }}</td>
				<td class="text-center tax_one_column tax_one_column_csgt hide for-non-pp">2.70<br><small>@ 2.50%</small></td>
				<td class="text-center tax_one_column tax_one_column_ssgt hide for-non-pp">2.70<br><small>@ 2.50%</small></td>
				<td class="text-center tax_one_column tax_one_column_isgt hide for-non-pp" style="border-right-color: #ccc;">-</td>
			</tr>
		{% endfor %}
	</tbody>
	<tfoot>
		<tr class="row_seperator column_seperator total_section sub_total_section for-non-pp">
			<td colspan="3" class="text-right total_section_1"><b>Total</b></td>
			<td class="total_section_2">{{ total_quantity }}</td>
			<td colspan="3" class="text-right total_section_3">{{ total_value}}</td>
			<td class="tax_rate_column td_gst_rate"></td>
			<td class="tax_rate_column text-right td_gst_amt">{{ total_cgst_value }}</td>
			<td class="tax_rate_column td_gst_rate"></td>
			<td class="tax_rate_column text-right td_gst_amt">{{ total_sgst_value }}</td>
			<td class="tax_rate_column td_gst_rate"></td>
			<td class="tax_rate_column text-right td_gst_amt" style="border-right-color: #ccc;">{{ total_igst_value }}</td>
			<td class="text-center tax_one_column tax_one_column_csgt_total hide">9.35</td>
			<td class="text-center tax_one_column tax_one_column_ssgt_total hide">9.35</td>
			<td class="text-center tax_one_column tax_one_column_isgt_total hide" style="border-right-color: #ccc;">-</td>
		</tr>
		{% for tax in po_taxes %}
			<tr style="{% if misc_res.print_summary_first_page and appendix_pages > 0 %}display:none;{% endif %}" class="row_seperator column_seperator other_tax_column for-non-pp">
				<td colspan="3" class="text-right total_section_1">{{ tax.tax_name }} @ {{ tax.tax_rate }}%</td>
				<td colspan="10" class="text-right total_section_3">{{ tax.tax_value }}</td>
			</tr>
		{% endfor %}
		{% if source.round_off != None %}
		<atr class="row_seperator column_seperator total_section for-non-pp">
			<atd colspan="5" class="text-right total_section_1"><b>Round Off</b></atd>
			<atd colspan="2" class="text-right total_section_3">{[ source.round_off ]}</atd>
		</atr>
		{% endif %}
		<tr style="{% if misc_res.print_summary_first_page and appendix_pages > 0 %}display:none;{% endif %}" class="row_seperator column_seperator total_section for-non-pp">
			<td colspan="3" class="text-right total_section_1"><b>Grand Total</b><span class="total_inclusice_tax hide"> (inclusive tax)</span></td>
			<td colspan="10" class="text-right total_section_3">{{ source.currency.code }} <b>{{ source.total }}</b></td>
		</tr>
		<tr style="{% if misc_res.print_summary_first_page and appendix_pages > 0 %}display:none;{% endif %}" class="tr_total_in_words row_seperator show_total_in_words total_in_words for-non-pp">
			<td colspan="13" class="full-length-td"><b>Total Value (INR):</b> THREE HUNDRED AND NINETY-SIX POINT SIX TWO</td>
		</tr>
	</tfoot>
</table>
					{% if summary_res.hsn_summary.print %}

<div class="hsn_summary_contianer for-non-pp">
	<h6 style="margin: 0;"><b class="hsn_summary_title" style="font-size: 12px;">HSN SUMMARY</b></h6>
	<table class="table table-bordered row-seperator column-seperator hsn_table hsn_summary" style="width: 100%; font-size: 12px;">
		<thead>
			<tr class="row_seperator column_seperator header_shading shaded">
				<th class="text-center td_sno" rowspan="2" style="width: 6%">S.No</th>
				<th class="text-center" rowspan="2" style="width: 28%">HSN/SAC</th>
				<th class="text-center td_tax" rowspan="2" style="width: 18%">Taxable Value</th>
				<th class="text-center" colspan="2" style="width: 16%; ">CGST</th>
				<th class="text-center" colspan="2" style="width: 16%; ">SGST</th>
				<th class="text-center" colspan="2" style="width: 16%;  border-right-color: #ccc;">IGST</th>
			</tr>
			<tr class="row_seperator column_seperator header_shading shaded">
				<th class="text-center" style="width: 7%;">%</th>
				<th class="text-center" style="width: 9%;">{{ source.currency.code }}</th>
				<th class="text-center" style="width: 7%;">%</th>
				<th class="text-center" style="width: 9%;">{{ source.currency.code }}</th>
				<th class="text-center" style="width: 7%;">%</th>
				<th class="text-center" style="width: 9%;">{{ source.currency.code }}</th>
			</tr>
		</thead>
		<tbody>
			{% for summary in hsn_summary %}
				<tr class="row_seperator column_seperator item_1">
					<td class="text-center td_sno" style="width: 6%;">{{forloop.counter}}.</td>
					<td class="text-center">{{ summary.hsn_code }}</td>
					<td class="text-right td_tax">{{ summary.consolidated_taxable_value|floatformat:2 }}</td>
					<td class="text-right ">{{ summary.consolidated_cgst_rate|floatformat:2 }}</td>
					<td class="text-right ">{{ summary.consolidated_cgst_value|floatformat:2 }}</td>
					<td class="text-right ">{{ summary.consolidated_sgst_rate|floatformat:2 }}</td>
					<td class="text-right ">{{ summary.consolidated_sgst_value|floatformat:2 }}</td>
					<td class="text-right ">{{ summary.consolidated_igst_rate|floatformat:2 }}</td>
					<td class="text-right" style="border-right-color: #ccc;">{{ summary.consolidated_igst_value|floatformat:2 }}</td>
				</tr>
			{% endfor %}
		</tbody>
	</table>
</div>
					{% endif %}
					{% if po_delivery_materials|length >= 1 %}
						<div id="purchase_ds_table">
							<div class="delivery_schedule_contianer for-non-pp">
   <h6 style="margin: 0;"><b class="delivery_schedule_title" style="font-size: 12px;">DELIVERY SCHEDULES</b></h6>
   <table class="table table-bordered  hsn_table delivery_schedule row-seperator column-seperator" id="delivery_table" style="font-size: 12px;">
        <thead>
            <tr class="row_seperator column_seperator header_shading shaded">
                <th class="text-center td_s_no td_s_no_text" style="width: 6%;">S.No</th>
                <th class="text-center td_delivery_description_text td_delivery_description" style="width: 66%;">Material</th>
                <th class="text-center td_delivery_due_text td_delivery_due" style="width: 18%;">Delivery Due On</th>
                <th class="text-center td_delivery_qty_text td_delivery_qty" style="width: 10%;">Qty</th>
            </tr>
        </thead>
		<tbody>
			{% for schedule in po_delivery_materials %}
				<tr class="row_seperator column_seperator item_1">
					<td class="text-center td_s_no" style="text-align: center; width: 6%;">{{forloop.counter}}.</td>
					<td class="td_delivery_description" style="width: 66%;">{{ schedule.material_name }}</td>
					<td class="text-center td_delivery_due pdf_item_dc_date" style="width: 18%;">12/07/2021 13:13:12</td>
					<td style="text-align:right" class="text-center td_delivery_qt">{{ schedule.qty }}</td>
				</tr>
			{% endfor %}
		</tbody>
   </table>
</div>
						</div>
					{% endif %}

				{% endif %}
			</div>

			<div id="terms" class="for-non-pp">

				<div class="row">
			        <div class="col-sm-12" style="line-height:2.428571">
			            <h6>Terms and Conditions</h6>
			            <hr style="margin:0;border: 1px solid #666; width:20%">
			            {% if header_res.terms_conditions.payment_terms.print %}
				            <div class="pdf_payment_term">
					            <span class="payment_term" style="width:20% !important;">{{ header_res.terms_conditions.payment_terms.label }}</span>
					            <span class="payment_details">: {{ payment_terms }}</span><br>

				            </div>
				        {% endif %}
			            <div>
			                <span class="payment_term" style="font-size:12px;">Delivery Due on </span>
			                <span class="payment_details" style="font-size:12px;">:  {{ po_delivery }}</span>
			            </div>
			            {% if header_res.terms_conditions.special_instructions.print %}
				            <div class="po_special_instruction">
				                <span class="payment_term" style="width:20% !important;">{{ header_res.terms_conditions.special_instructions.label }}</span>
				                <span class="payment_details">:  {{ source.closing_remarks }}</span>
				            </div>
			            {% endif %}
			        </div>
			    </div>

			    <div class="notes_bottom">
			        <hr style="margin:0;border: 1px solid #666666;">
			    </div>
			    <div class="row">
			        <div class="col-sm-12"><strong>Notes:</strong></div>
				    <div style="margin-left: 15px !important; margin-top: 0px !important; font-size: 12px; line-height: 12px;" class="notes notes_section">
								    1. Hello A Test certificate should be furnished along with the despatched material,guarantee certificate should be sent along with invoice.&nbsp;<br>
2. Please quote PO no and date in all your correspondance.&nbsp;<br>
3. In case of Excisable goods, Exise gate pass should be sent along with material&nbsp;<br>
4. Our Item Code as in first line is must in your invoice for speedy operation&nbsp;<br>
5. The description of the commercial invoice should be in line with the description mentioned in the purchase order&nbsp;<br>
6. Supplier should ensure appropriate packing to eliminate the transit damages.&nbsp;<br>
7. Supplier should acknowledge receipt of PO and agreement of terms mentioned in PO unless other wise specified in writing by the supplier with in seven days, it is considered that all PO terms are accepted.<br>
8. Material acceptance subject to quality standard. The verdict of our inspection dept,will be final. Rejection any, will have to be replaced by you free of cost.

							</div>
			    </div>

				<div class="notes_bottom">
					<hr style="margin:0;border: 1pt solid #666666; ">
				</div>

				<div class="row">
			        <div class="col-sm-4">
			            <span>{{header_res.billing_address.label}}</span><br>
			            <div> {{ source.enterprise.name }} <br>{{ source.enterprise.address_1 }}, <br>{{ source.enterprise.address_2 }} {{ source.enterprise.city }},</div>
						<div>E-mail:{{ source.enterprise.email }}</div>
			        </div>
			        <div class="col-sm-4">
			            <span>{{header_res.shipping_address.label}}</span><br>
			            <div> {{ source.enterprise.name }}<br> {{ source.enterprise.address_1 }}, <br>{{ source.enterprise.address_2 }} {{ source.enterprise.city }},</div>
			        </div>
			    </div>
			</div>
			{% if misc_res.print_summary_first_page and appendix_pages > 0 %}
				<div style="page-break-after: always"></div>
			{% endif %}
			{% if misc_res.print_summary_first_page and appendix_pages > 0 %}
				<div class="annexture_po">ANNEXURE TO PURCHASE ORDER {{ source.getCode }} DATED {{ order_date }}</div>
				<div class="col-sm-12" id="purchase_material_table">
					<style>
	.po_template_view .table.row-seperator th,
	.po_template_view .table.row-seperator td,
	.po_template_view .table.row-seperator th,
	.po_template_view .table.row-seperator td {
	    border-top: 1px solid #666666 !important;
	    border-bottom: 1px solid #666666 !important;
	}

	.po_template_view .table.column-seperator th,
	.po_template_view .table.column-seperator td,
	.po_template_view .table.column-seperator th,
	.po_template_view .table.column-seperator td {
	    border-right: 1px solid #666666 !important;
	    border-left: 1px solid #666666 !important;
	}

</style>
<table class="table item_table table-bordered row-seperator column-seperator" id="item_table" style="font-size:11px; width: 100%">
    <thead style="font-size: 12px;">
        <tr class="row_seperator column_seperator header_shading shaded">
            <th class="text-center td_sno td_sno_text" rowspan="2" scope="col" style="width: 6%;">S.NO</th>
            <th class="text-center td_description td_description_text" rowspan="2" style="width: 31%;">Description</th>
            <th class="text-center pdf_item_hsn_code_txt td_hsn_code for-non-pp" rowspan="2" style="width: 7%;">HSN/SAC</th>
            <th class="text-center td_qty td_qty_text" rowspan="2" scope="col" style="width: 6%;">Qty</th>
            <th class="text-center td_uom td_uom_text hide" rowspan="2" scope="col" style="width: 6%;">Uom</th>
            <th class="text-center td_price td_price_text for-non-pp" rowspan="2" scope="col" style="width: 6%;">Price</th>
            <th class="text-center td_disc td_disc_text for-non-pp" rowspan="2" scope="col" style="width: 6%;">Disc (%)</th>
            <th class="text-center td_tax td_tax_text for-non-pp" rowspan="2" scope="col" style="width: 8%;">Total</th>
			<th class="text-center td_cgst tax_rate_column for-non-pp" colspan="2" style="width: 12%; ">CGST</th>
			<th class="text-center td_sgst tax_rate_column for-non-pp" colspan="2" style="width: 12%; ">SGST</th>
			<th class="text-center td_igst tax_rate_column for-non-pp" colspan="2" style="width: 12%;  border-right-color: #ccc;">IGST</th>
			<th class="text-center td_tax_amt tax_one_column hide for-non-pp" rowspan="2" style="width: 5%;">CGST</th>
			<th class="text-center td_tax_amt tax_one_column hide for-non-pp" rowspan="2" style="width: 5%;">SGST</th>
			<th class="text-center td_tax_amt tax_one_column hide for-non-pp" rowspan="2" style="width: 5%;">IGST</th>
			</tr>
			<tr class="row_seperator column_seperator tax_rate_column header_shading tr_second_row shaded for-non-pp">
			<th class="text-center td_cgst_rate td_tax_rate td_gst_rate" style="width: 5%;">%</th>
			<th class="text-center td_cgst_amt td_tax_amt td_gst_amt" style="width: 5%;">INR</th>
			<th class="text-center td_sgst_rate td_tax_rate td_gst_rate" style="width: 5%;">%</th>
			<th class="text-center td_sgst_amt td_tax_amt td_gst_amt" style="width: 5%;">INR</th>
			<th class="text-center td_igst_rate td_tax_rate td_gst_rate" style="width: 5%;">%</th>
			<th class="text-center td_igst_amt td_tax_amt td_gst_amt" style="width: 5%;">INR</th>
		</tr>
	</thead>
	<tbody style="font-size: 12px;">
		{% for material in po_item_details %}
			<tr class="row_seperator column_seperator row_shading item_2 shaded">
				<td class="text-center td_sno" style="width: 6%;">{{forloop.counter}}</td>
				<td><span class="pdf_item_name"><span class="pdf_item_name_txt"></span>{{ material.material_name }}<br></span>
					<span class="pdf_item_drawing_number"><span class="pdf_item_drawing_number_txt">Code:</span>{{ material.material_drawing_no }}<br></span>
					<span class="pdf_item_hsn_code hide for-non-pp"><i class="pdf_item_hsn_code_txt">HSN/SAC</i> {{ material.hsn_code }}<br></span>
					<span class="tax_in_description hide for-non-pp">{CGST @ 2.5: 6.65 - SGST @ 2.5: 6.65 - IGST @ 5: 12.96}</span>
				</td>
				<td class="text-center td_hsn_code for-non-pp" style="width: 7%;">{{ material.hsn_code }}</td>
				<td class="text-right td_qty">{{ material.material_quantity|floatformat:2 }}<br><span class="pdf_unit_in_price">({{ material.material_unit }})</span></td>
				<td class="text-center td_uom hide">{{ material.material_unit }}</td>
				<td class="text-right td_price for-non-pp">{{ material.material_rate|floatformat:2 }}</td>
				<td class="text-right td_disc for-non-pp">{{ material.material_discount }}</td>
				<td class="text-right td_tax for-non-pp">{{ material.material_taxable_value }}</td>
				<td class="text-right td_cgst tax_rate_column td_gst_rate for-non-pp">{{ material.cgst_rate }}</td>
				<td class="text-right td_cgst tax_rate_column td_gst_amt for-non-pp">{{ material.cgst_value }}</td>
				<td class="text-right td_sgst tax_rate_column td_gst_rate for-non-pp">{{ material.sgst_rate }}</td>
				<td class="text-right td_sgst tax_rate_column td_gst_amt for-non-pp">{{ material.sgst_value }}</td>
				<td class="text-right td_igst tax_rate_column td_gst_rate for-non-pp">{{ material.igst_rate }}</td>
				<td class="text-right td_igst tax_rate_column td_gst_amt for-non-pp" style="border-right-color: #ccc;">{{ material.igst_value }}</td>
				<td class="text-center tax_one_column tax_one_column_csgt hide for-non-pp">2.70<br><small>@ 2.50%</small></td>
				<td class="text-center tax_one_column tax_one_column_ssgt hide for-non-pp">2.70<br><small>@ 2.50%</small></td>
				<td class="text-center tax_one_column tax_one_column_isgt hide for-non-pp" style="border-right-color: #ccc;">-</td>
			</tr>
		{% endfor %}
	</tbody>
	<tfoot>
		<tr class="row_seperator column_seperator total_section sub_total_section for-non-pp">
			<td colspan="3" class="text-right total_section_1"><b>Total</b></td>
			<td class="total_section_2">{{ total_quantity }}</td>
			<td colspan="3" class="text-right total_section_3">{{ total_value}}</td>
			<td class="tax_rate_column td_gst_rate"></td>
			<td class="tax_rate_column text-right td_gst_amt">{{ total_cgst_value }}</td>
			<td class="tax_rate_column td_gst_rate"></td>
			<td class="tax_rate_column text-right td_gst_amt">{{ total_sgst_value }}</td>
			<td class="tax_rate_column td_gst_rate"></td>
			<td class="tax_rate_column text-right td_gst_amt" style="border-right-color: #ccc;">{{ total_igst_value }}</td>
			<td class="text-center tax_one_column tax_one_column_csgt_total hide">9.35</td>
			<td class="text-center tax_one_column tax_one_column_ssgt_total hide">9.35</td>
			<td class="text-center tax_one_column tax_one_column_isgt_total hide" style="border-right-color: #ccc;">-</td>
		</tr>
		{% for tax in po_taxes %}
			<tr style="{% if misc_res.print_summary_first_page and appendix_pages > 0 %}display:none;{% endif %}" class="row_seperator column_seperator other_tax_column for-non-pp">
				<td colspan="3" class="text-right total_section_1">{{ tax.tax_name }} @ {{ tax.tax_rate }}%</td>
				<td colspan="10" class="text-right total_section_3">{{ tax.tax_value }}</td>
			</tr>
		{% endfor %}
		{% if source.round_off != None %}
		<atr class="row_seperator column_seperator total_section for-non-pp">
			<atd colspan="5" class="text-right total_section_1"><b>Round Off</b></atd>
			<atd colspan="2" class="text-right total_section_3">{[ source.round_off ]}</atd>
		</atr>
		{% endif %}
		<tr style="{% if misc_res.print_summary_first_page and appendix_pages > 0 %}display:none;{% endif %}" class="row_seperator column_seperator total_section for-non-pp">
			<td colspan="3" class="text-right total_section_1"><b>Grand Total</b><span class="total_inclusice_tax hide"> (inclusive tax)</span></td>
			<td colspan="10" class="text-right total_section_3">{{ source.currency.code }} <b>{{ source.total }}</b></td>
		</tr>
		<tr style="{% if misc_res.print_summary_first_page and appendix_pages > 0 %}display:none;{% endif %}" class="tr_total_in_words row_seperator show_total_in_words total_in_words for-non-pp">
			<td colspan="13" class="full-length-td"><b>Total Value (INR):</b> THREE HUNDRED AND NINETY-SIX POINT SIX TWO</td>
		</tr>
	</tfoot>
</table>
				</div>
				{% if summary_res.hsn_summary.print %}

<div class="hsn_summary_contianer for-non-pp">
	<h6 style="margin: 0;"><b class="hsn_summary_title" style="font-size: 12px;">HSN SUMMARY</b></h6>
	<table class="table table-bordered row-seperator column-seperator hsn_table hsn_summary" style="width: 100%; font-size: 12px;">
		<thead>
			<tr class="row_seperator column_seperator header_shading shaded">
				<th class="text-center td_sno" rowspan="2" style="width: 6%">S.No</th>
				<th class="text-center" rowspan="2" style="width: 28%">HSN/SAC</th>
				<th class="text-center td_tax" rowspan="2" style="width: 18%">Taxable Value</th>
				<th class="text-center" colspan="2" style="width: 16%; ">CGST</th>
				<th class="text-center" colspan="2" style="width: 16%; ">SGST</th>
				<th class="text-center" colspan="2" style="width: 16%;  border-right-color: #ccc;">IGST</th>
			</tr>
			<tr class="row_seperator column_seperator header_shading shaded">
				<th class="text-center" style="width: 7%;">%</th>
				<th class="text-center" style="width: 9%;">{{ source.currency.code }}</th>
				<th class="text-center" style="width: 7%;">%</th>
				<th class="text-center" style="width: 9%;">{{ source.currency.code }}</th>
				<th class="text-center" style="width: 7%;">%</th>
				<th class="text-center" style="width: 9%;">{{ source.currency.code }}</th>
			</tr>
		</thead>
		<tbody>
			{% for summary in hsn_summary %}
				<tr class="row_seperator column_seperator item_1">
					<td class="text-center td_sno" style="width: 6%;">{{forloop.counter}}.</td>
					<td class="text-center">{{ summary.hsn_code }}</td>
					<td class="text-right td_tax">{{ summary.consolidated_taxable_value|floatformat:2 }}</td>
					<td class="text-right ">{{ summary.consolidated_cgst_rate|floatformat:2 }}</td>
					<td class="text-right ">{{ summary.consolidated_cgst_value|floatformat:2 }}</td>
					<td class="text-right ">{{ summary.consolidated_sgst_rate|floatformat:2 }}</td>
					<td class="text-right ">{{ summary.consolidated_sgst_value|floatformat:2 }}</td>
					<td class="text-right ">{{ summary.consolidated_igst_rate|floatformat:2 }}</td>
					<td class="text-right" style="border-right-color: #ccc;">{{ summary.consolidated_igst_value|floatformat:2 }}</td>
				</tr>
			{% endfor %}
		</tbody>
	</table>
</div>
				{% endif %}
				{% if po_delivery_materials|length >= 1 %}
					<div id="purchase_ds_table">
						<div class="delivery_schedule_contianer for-non-pp">
   <h6 style="margin: 0;"><b class="delivery_schedule_title" style="font-size: 12px;">DELIVERY SCHEDULES</b></h6>
   <table class="table table-bordered  hsn_table delivery_schedule row-seperator column-seperator" id="delivery_table" style="font-size: 12px;">
        <thead>
            <tr class="row_seperator column_seperator header_shading shaded">
                <th class="text-center td_s_no td_s_no_text" style="width: 6%;">S.No</th>
                <th class="text-center td_delivery_description_text td_delivery_description" style="width: 66%;">Material</th>
                <th class="text-center td_delivery_due_text td_delivery_due" style="width: 18%;">Delivery Due On</th>
                <th class="text-center td_delivery_qty_text td_delivery_qty" style="width: 10%;">Qty</th>
            </tr>
        </thead>
		<tbody>
			{% for schedule in po_delivery_materials %}
				<tr class="row_seperator column_seperator item_1">
					<td class="text-center td_s_no" style="text-align: center; width: 6%;">{{forloop.counter}}.</td>
					<td class="td_delivery_description" style="width: 66%;">{{ schedule.material_name }}</td>
					<td class="text-center td_delivery_due pdf_item_dc_date" style="width: 18%;">12/07/2021 13:13:12</td>
					<td style="text-align:right" class="text-center td_delivery_qt">{{ schedule.qty }}</td>
				</tr>
			{% endfor %}
		</tbody>
   </table>
</div>
					</div>
				{% endif %}

			{% endif %}
		</div>
