<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Success</title>
    <style>
        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 70%;
            height: auto;

        }

        .modal-content {
            background-color: #fefefe;
            padding: 20px;
            width: 100%;
            height: 100%;
            overflow: auto;
            text-align: center;
        }

        .close {
            font-family: inherit;
            background: #4a90e2;
            color: #fff;
            float: right;
            font-size: 20px;
            font-weight: normal;


            position: absolute;
            bottom: 0;
            left: 80%;
            transform: translateX(-50%);

        }

        .close:hover,
        .close:focus {

            text-decoration: none;
            cursor: pointer;
            background: #4a90e2;
            color: #fff;
        }

        .title_content {
            font-family: inherit;
            text-align: center;
            background: #4a90e2;
            color: #fff;
            box-shadow: 0px 2px 23px 1px #ccc;
            border-radius: 9px 9px 0px 0px;
            font-size: 20px;
            padding: 20px 0 26px;
        }

        /* Add styles for the table */
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th {
         padding: 10px;
         text-align: right;
        }

         td {
            padding: 10px;
            text-align: left;
        }

<!--        th,td {-->
<!--            background-color: #f0f0f0;-->
<!--        }-->
    </style>
</head>
<body>
    <!-- The modal window -->
    <div class="modal" id="payment-success-modal">
        <div class="modal-content" style="box-shadow: 10px 10px 10px 10px gray;padding-bottom: 50px;">
            <h1 class="title_content">Payment Successful!</h1>
            <p>Thank you for your payment. Here are the details:</p>

            <table>
                <tr>
                    <th>Amount</th>
                    <td>₹{{ amount }}</td>
                </tr>
                <tr>
                    <th>Payment ID</th>
                    <td>{{ payment_id }}</td>
                </tr>
                <tr>
                    <th>Order ID</th>
                    <td>{{ order_id }}</td>
                </tr>
                <tr>
                    <th>Transaction Date</th>
                    <td>{{ transaction_date }}</td>
                </tr>
                <tr>
                    <th>Status</th>
                    <td>{{ status }}</td>
                </tr>
                <tr>
                    <th>Gateway</th>
                    <td>{{ mode }}</td>
                </tr>
                <tr>
                    <th>Mode</th>
                    <td>{{ type }}</td>
                </tr>
            </table>

            <button class="close" id="ok-button" style="margin-bottom: 24px;">LOGIN AGAIN</button>
        </div>
    </div>

    <script>
        // Get the modal window and the OK button
        var modal = document.getElementById("payment-success-modal");
        var okButton = document.getElementById("ok-button");

        // Show the modal window when the page loads
        window.onload = function() {
            modal.style.display = "block";
        };

        // Close the modal window when the OK button is clicked
        okButton.onclick = function() {
            modal.style.display = "none";
            window.location.href = "/erp/";
        };
    </script>
</body>
</html>