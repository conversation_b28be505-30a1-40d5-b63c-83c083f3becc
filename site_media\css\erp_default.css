 body {
	font-family: '<PERSON><PERSON>', sans-serif;
	/*background: #eee;*/
	font-size: 13px;
 }

 body.with_subscription_bar {
 	margin-top: 40px;
 }

 body.with_subscription_bar .left-menu-container,
 body.with_subscription_bar .slide_container,
 body.with_subscription_bar .right-side-menu {
 	top: 40px;
 }

 body.with_subscription_bar .menu-backdrop {
 	top: 90px;
 }

body.with_subscription_bar .right-side-menu-items-bottom{
	bottom:90px;
}

body.with_subscription_bar .page-title-container {
	top: 55px;
}

body.with_subscription_bar .loading-main-container,
body.with_subscription_bar .downloading-main-container {
	top: -40px;
}


.body-container {
	background: #FFF;
}

#page-wrapper {
	min-height: calc(100vh - 30px);
}

.with_subscription_bar #page-wrapper {
	min-height: calc(100vh - 68px);
}

 .left-menu-container {
	 width: 300px;
	 float: left;
	 position: fixed; 
	 left: 0; 
	 top: 0;
	 background: #fff; 
	 z-index: 10000; 
	 height: 100%; 
	 opacity: 0.97; 
	 display: none;
	 margin-left: 85px;
     margin-top: 50px;
 }
 
 .left-menu-child {
	 margin-left: 15px !important;
 }
 
 .left-menu-container ul li a,
 .left-menu-container ul li a:hover {
	color: #666;
    text-transform: capitalize;
    padding: 4px 24px;
    font-size: 14px;
    letter-spacing: 0.5px;
	font-weight: normal;
	background: transparent;
 }
 
 .left-menu-container ul li a,
 .left-menu-container ul li a:hover {
	color: #666;
    text-transform: capitalize;
    padding: 4px 24px;
    font-size: 14px;
    letter-spacing: 0.5px;
	font-weight: normal;
 }
 
 .left-menu-container ul li a:hover,
 .left-menu-container ul li a:active {
	text-decoration: none; 
	background-color: #eee !important;
 }
 
 .left-menu-container ul li a.active {
    color: #3bbce3;
    border: none;
    background-color: #FFF !important;
 }

 .left-menu-container ul li a.active:hover {
 	background-color: #eee !important;
 }
 
 .no_link:hover {
	text-decoration: none !important; 
 }
 
 .menu-item-header {
	 background: #209be1;
	 color: #FFF;
	 padding: 17.5px;
	 font-size: 24px;
	 margin-bottom: 20px;
 }
 
 .menu-item-header a ,
 .menu-item-header a:active,
 .menu-item-header a:focus {
	 color: #FFF !important;
	 font-size: 24px !important;
	 padding: 0 !important;
 }

 .right-content-container {
	width: 100%;
	float: left;
	border-left: 1px solid #ddd;
	box-shadow: inset 50px 0px 46px -56px #ddd;
	-webkit-box-shadow: inset 50px 0px 46px -56px #ddd;
	-moz-box-shadow: inset 50px 0px 46px -51px #ABABAB;
	-o-box-shadow: inset 50px 0px 46px -51px #ABABAB;
	padding-bottom: 0;
	padding-left: 85px;
	padding-right: 50px;
	margin-top: 55px;
 }

 .right-side-menu {
 	position: fixed;
    right: 0;
    z-index: 1000;
    cursor: pointer;
    top: 0;
    width: 50px;
    height: 100%;
    background: #209be1;
    color: #FFF;
    margin-top: 50px;
    box-shadow: inset 1px 0px 2px 0px rgba(60,64,67,0.3);
    background-color: white;
 }

.right-side-menu-items {
	font-size: 16px;
    transition: all .3s cubic-bezier(.4,0,.2,1);
    color: #004195;
    border-radius: 50px;
    margin: 15px 5px;
}

.right-side-menu-items-bottom {
	position: absolute;
    bottom: 50px;
    border-top: 2px solid #ddd;
}

.right-side-menu-items i,
.right-side-menu-items img {
	font-size: 18px;
    padding: 10px;
}

.right-side-menu-items a {
	font-size: 18px;
    padding: 10px 9px 5px 10px;
}

.right-side-menu-items:hover i,
.right-side-menu-items:hover img {
	color: #000;
	border-radius: 50px;
	background: #ddd;
}

.right-side-menu-items:hover a {
	color: #000;
	border-radius: 50px;
	background: #e6e6e6;
}

 .contant-container-nav {
	/* box-shadow: inset 0px -39px 2px -38px #ddd;
	-webkit-box-shadow: inset 0px -39px 2px -38px #ddd;
	-moz-box-shadow: inset 0px -39px 2px -38px #ABABAB;
	-o-box-shadow: inset 0px -39px 2px -38px #ABABAB;*/
	padding: 15px 20px;
 }

 .form-contianer {
	 padding: 0 35px;
 }

 .sub-heading-content {
	 font-size: 1.1em;
	 padding: 10px 0;
 }

 .mycolor-blue-1 {
	 color: #004195;
 }
 .mycolor-blue-2 {
	 color: #209be1;
 }
 .mycolor-blue-3 {
	 color: #3bbce3;
 }

 .mycolor-grey-6 {
	 color: #666;
 }

 .myfont-number {
	 font-family: 'Kameron', serif;
	 letter-spacing: 1.2px;
 }

 .font-bold {
	 font-weight: bold;
 }

.btn.btn-small {
	margin-top: 8px;
    padding: 4px 12px;
    font-size: 12px;
}

.btn.btn-new-item {
	background: #fff;
	box-shadow:  0 1px 2px 0 rgba(60,64,67,0.302),0 1px 3px 1px rgba(60,64,67,0.149);
    border-radius: 50px;
    padding: 6px 20px 6px;
    font-size: 20px;
    color: #004195;
    margin-top: 8px;
    margin-right: 0;
}

.btn.btn-new-item .fa{
    font-size: 20px;
    vertical-align: middle;
}

.btn.btn-new-item .btn-new-item-label,
.btn.btn-new-item .btn-new-upload-label {
	padding-left: 10px;
	font-size: 20px;
}

.btn.btn-new-item:hover {
	background: #ebf8ff;
	box-shadow:  0 1px 2px 0 rgba(60,64,67,0.302),0 1px 3px 1px rgba(60,64,67,0.149);
}

.btn.btn-new-item:active {
    box-shadow:  0 1px 2px 0 rgba(60,64,67,0.302),0 1px 3px 1px rgba(60,64,67,0.149);
    transform: scale(0.95);
}

.btn-new-item-label:after {
	content:  'Create';
}

.btn-new-upload-label:after {
	content: 'Upload';
}

.btn.btn-add-new,
.btn.transparent-btn {
	 border:solid 1px #004195;
	 color: #004195;
	 background: transparent;
}

.btn.btn-add-new:hover,
.btn.transparent-btn:hover {
	color: #000;
}

.btn.transparent-btn.btn-success {
    border: solid 1px #4cae4c;
    color: #4cae4c;
    background: transparent;
}

.btn.transparent-btn.btn-succes:hover {
    color: #000;
    background: transparent;
}

.btn.transparent-btn.btn-danger {
    border: solid 1px #dd4b39;
    color: #dd4b39;
    background: transparent;
}

.btn.transparent-btn.btn-danger:hover {
    color: #000;
    background: transparent;
}

.btn.btn-add-tax {
	padding: 3px 12px;
    color: #fff;
    background: #209be1;
    margin-top: 18px;
    font-size: 18px;
}

.btn.btn-delete {
	 background: #dd4b39;
	 color: #FFF;
}

.btn.btn-delete:hover {
	 background: orange;
	 color: #FFF;
}

.btn-close {
	background: #fff;
	color: #004195;
}

.btn-close:hover {
	background: #eee;
	border-radius: 6px;
}

.btn.btn-save {
	 background: #209be1;
	 color: #FFF;
}

.btn.btn-preview {
	 background: #209be1;
	 color: #FFF;
}

.btn-warning-border {
    background: transparent;
    border: solid 1px #ff7f50;
    color: #ff7f50;
}

.btn-warning-border:hover {
    text-shadow: 0 0 #ff7f50;
    color: #ff7f50;
    background: rgba(255, 127, 80, 0.2)
}

.btn.btn-save:hover {
	 background: #44ad6b;
	 color: #FFF;
}

.btn.btn-cancel {
	 background: #004195;
	 color: #FFF;
}

.btn.btn-cancel:hover {
	 background: #666;
	 color: #FFF;
}

.btn-container-margin {
	margin-top: 25px;
}

.btn-textbox {
	font-size: 14px;
	color: #555;
	border: 1px solid #ccc;
	width: 80%;
	line-height: 27px;
	height: 28px;
	text-align: right;
	padding: 0px 12px;
	border-radius: 4px;
	background-color: #ffffff;
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}

.a-profile-container {
	padding: 0 !important;
    margin-right: 15px;
    display: flex;
    align-items: center;
    height: 44px;
    cursor: pointer;
}

.btn-setting:focus {
	outline: none !important;
	box-shadow: 0 0 !important;
	background: #fff !important;
}

.span-profile-name {
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #209be1;
    text-shadow: 0 0 #209be1;
    text-align: right;
    font-size: 14px;
    padding-right: 10px;
}

.span-profile-img {
    display: inline-block;
    padding: 3px 0px 9px 0px;
    background: #209be1;
    height: 28px;
    border-radius: 50px;
    width: 28px;
    color: #fff;
    font-size: 16px;
    margin-top: -2.5px;
}

.user_profile_image {
	display: inline-block;
    padding: 2px 0px 0px 11px;
    background: #209be1;
    height: 40px;
    border-radius: 50px;
    width: 40px;
    color: #fff;
    font-size: 26px;
    margin-right: -20px;
    margin-top: -7px;
    float: left;
}

.img-user-profile {
	width: 34px;
	height: 34px;
	margin: 0;
	border-radius: 50px;
}

.project_list {
	width: 350px;
    height: 100vh;
    position: absolute;
    right: -15px;
    top: 50px;
    background-color: white;
    border: solid 1px #ccc;
    border-top: none;
}
.project_forecast{
    width: 300px;
    height: 100vh;
    position: absolute;
    right: 25px;
    top: 50px;
    background-color: white;
    border: solid 1px #ccc;
    border-top: none;
    z-index:1;
}

.project_list ul li {
    list-style: none;
    padding: 10px 20px;
    margin-left: -40px;
}
.project_list ul li a{
font-size:15px;
font-weight:500;
color:#000;
text-decoration:none !important
}
.project_list li:hover {
cursor: pointer;
}
.project_list ul li a:hover{
background: #eee;
    cursor: pointer;
}

.span-profile-name-arrow {
    color: #209be1;
    font-size: 12px;
}
.project_list ul {
    position: relative;
}

.project_list ul li {
    position: relative;
}
.project_list ul li:before {
	    border-bottom: 1px solid #bbb;
	    content: "";
	    left: 6px;
	    position: absolute;
	    top: 6px;
	    width: 1.5em;
	    margin-top:14px;
	}
.project_list ul li:after{
    border-left: 1px solid #bbb;
    content: "";
    left: 6px;
    position: absolute;
    top: 9px;
    height: 4em;
	}
.project_list ul li:last-child:after {
    display: none; /* Hide the line after the last child */
}
.login-page-container {
	width: 100%;
	height: 100%;
	margin: 0 auto;
	margin-top: 120px;
	/*background: rgba(32,154,225,1);
	background: -moz-linear-gradient(top, rgba(32,154,225,1) 0%, rgba(36,166,226,1) 47%, rgba(36,166,226,1) 100%);
	background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(32,154,225,1)), color-stop(47%, rgba(36,166,226,1)), color-stop(100%, rgba(36,166,226,1)));
	background: -webkit-linear-gradient(top, rgba(32,154,225,1) 0%, rgba(36,166,226,1) 47%, rgba(36,166,226,1) 100%);
	background: -o-linear-gradient(top, rgba(32,154,225,1) 0%, rgba(36,166,226,1) 47%, rgba(36,166,226,1) 100%);
	background: -ms-linear-gradient(top, rgba(32,154,225,1) 0%, rgba(36,166,226,1) 47%, rgba(36,166,226,1) 100%);
	background: linear-gradient(to bottom, rgba(32,154,225,1) 0%, rgba(36,166,226,1) 47%, rgba(36,166,226,1) 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#209ae1', endColorstr='#24a6e2', GradientType=0 );*/
}

.login-container {
	width: 95%;
	max-width: 500px;
	text-align: center;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -65%);
	padding: 20px 40px;
    border: 1px solid #dadce0;
    border-radius: 8px;
    background: linear-gradient(to bottom, rgba(32,154,225,1) 0%, rgba(36,166,226,1) 47%, rgba(36,166,226,1) 100%);
    z-index: 1;
}

/*@media screen and (max-height: 800px) {
  .login-container {
    transform: translate(-50%, -55%);
  }
  .login-logo, .login-header {
		margin: 5px 0;
	}
}

@media screen and (max-height: 610px) {
 	.login-container {
    	transform: translate(-50%, -56%);
    	padding: 0 10px;
	}
	.login-logo, .login-header {
		margin: 5px 0;
	}
}

@media screen and (max-height: 600px) {
	#id_user_email,
	#id_password,
	#id_old_password,
	#id_user_email,
	#id_new_password,
	#id_confirm_password,
	#loading_change_password {
		height: 30px;
    	padding: 5px 10px;
	}

	#sign_in_form,
	#change_password,
	#cancel_change_password {
		font-size: 12px;
		height: 30px;
	}
}*/

.login-forms input {
	font-family: arial;
	letter-spacing: 1.5px;
	font-size: 16px;
	-webkit-box-shadow: 2px 2px 5px -2px rgba(102,102,102,1);
-moz-box-shadow: 2px 2px 5px -2px rgba(102,102,102,1);
box-shadow: 2px 2px 5px -2px rgba(102,102,102,1);

}

.btn-login {
	width: 100%;
	font-size: 20px;
	letter-spacing: 6px;
	background: #3bbce3;
	color: #FFF !important;
	-webkit-box-shadow: 3px 3px 8px -2px rgba(102,102,102,1);
	-moz-box-shadow: 3px 3px 8px -2px rgba(102,102,102,1);
	box-shadow: 3px 3px 8px -2px rgba(102,102,102,1);
}

.btn-login:hover,
.btn-login:focus {
	transform: scale(1.02);
	color: #FFF;
}

.btn-login:active {
	-webkit-box-shadow:  0 0;
	-moz-box-shadow: 0 0;
	box-shadow: 0 0;
	transform: scale(0.99);
	color: #FFF;
}

/* Custom Table */
.table-fixed {
	table-layout: fixed;
}

.custom-table {

}

.custom-table-bordered th,
.custom-table-bordered td{
	border: solid 1px #ddd;
}

.custom-table td {
	vertical-align: middle !important;
	font-size: 12px;
}

.custom-table.text-center th  {
	text-align: center;
	vertical-align: top;
}

.custom-table.vertical-center th  {
	text-align: center;
	vertical-align: middle;
}

.custom-table thead {
	background: #e5e5e5;
	color: #555;
	text-transform: uppercase;
}

.custom-table thead tr th{
	text-align: center;
	/*background-color: #605d5b !important;
    color: #eee !important;*/
	background-color: #ddd !important;
	color: #444 !important;
    font-size: 12px !important;
	letter-spacing: 1px;
	vertical-align: middle;
	border-color: #bbb;
}

.custom-table tbody tr {
	/*background: #FFFFFF !important;*/
	color: #333;
	transition: background 0.35s;
	/*text-transform: capitalize;*/
}

.custom-table tbody tr:hover {
	background: rgba(0, 0, 0,0.1) !important;
}

.custom-table tbody tr:hover td {

}

.custom-table tbody td {
	word-break: break-word;
	letter-spacing: 0.2px;
	color: #444;
}

.custom-table .th-vertical-center th {
	vertical-align: middle;
}

.custom-table.remove-center-border td{
	border: none;
	border-top: 1px solid #ddd
}

/*custom-table-large*/

.custom-table.custom-table-large th {
	font-size: 10px !important;
	padding: 8px 1px !important;
}

.custom-table.custom-table-large td {
	padding: 8px 4px !important;
	font-size: 11px !important;
}

.custom-table.custom-table-large .form-control {
	padding: 3px !important;
	font-size: 11px !important;
}

.custom-table a.edit_link_code {
	color: #209be1;
    text-shadow: 0px 0px 0px #209be1;
    font-size: 13px;
    letter-spacing: 0;
    cursor: pointer;
}

.custom-table a.pending,
.custom-table a.draft,
.custom-table a.reviewed,
.custom-table span.pending,
.custom-table span.draft,
.custom-table span.reviewed {
	color:  #ff9c1b;
	text-shadow: 0 0 0 #ff9c1b;
	font-size: 13px;
}

.custom-table a.approved,
.custom-table a.verified,
.custom-table a.acknowledged,
.custom-table span.approved,
.custom-table span.verified,
.custom-table span.received { 
	color:  #28a745;
	text-shadow: 0 0 0 #28a745;
	font-size: 13px;
}

.custom-table a.cancelled,
.custom-table a.rejected,
.custom-table a.returned,
.custom-table span.cancelled,
.custom-table span.rejected,
.custom-table span.returned {
	color:  #dc3545;
	text-shadow: 0 0 0 #dc3545;
	font-size: 13px;
}

.custom-table a.checked,
.custom-table a.confirmed,
.custom-table span.confirmed,
.custom-table span.checked {
	color:  #004195;
	text-shadow: 0 0 0 #004195;
	font-size: 13px;
}

.custom-table span.on-time {
    color:  #e0ce1d;
	text-shadow: 0 0 0 #e0ce1d;
	font-size: 13px;
	text-transform: capitalize;
}

.table > thead:first-child > tr:first-child > th {
	border-bottom: none;
}

.table_disabled input,
.table_disabled select{
	pointer-events: none !important;
	background: #eee !important;
}

.td-empty-result {
	font-size: 12px !important;
	font-weight: bold !important;
	text-align: center !important;
}

#po_materials_table tr th:nth-child(2) {
	width: 25%;
}

 /* Bootstrap Overwrite */
 .navbar-inverse {
	background: transparent;
	margin-bottom: 10px;
	padding: 0;
    border: none;
	border-radius: 0;
	height: 45px;
	z-index: 1001 !important;
	background: #f5f5f5 !important;
	border: none;
	box-shadow: inset 0px -0.5px 2px 0px rgba(60,64,67,0.3);
 }

 .navbar-inverse .navbar-nav > li > a {
	color: #FFF;
	letter-spacing: 1.5px;
	font-size: 1em;
	padding: 16px 24px 16px;
	font-family: 'Roboto', sans-serif;
 }

  .navbar-inverse .navbar-nav > li > a:hover {
	color: #FFF;
	background: #004195;
  }

  .navbar-inverse .navbar-nav > li.active-settings-link > div:hover {
	  border-radius: 0 0 5px 0;
  }

  .navbar-inverse .navbar-nav > li:first-child > a:hover {
	border-radius: 0 0 0 5px;
  }

  .navbar-inverse .navbar-nav > li:first-child.active a {
	  border-radius: 0 0 0 5px;
  }

 .navbar-inverse .navbar-nav > .active > a,
 .navbar-inverse .navbar-nav > .active > a:hover,
 .navbar-inverse .navbar-nav > .active > a:focus {
	 color: #FFF;
	 background: #004195;
 }

 .navbar-inverse .navbar-nav > li > div {
	color: #FFF;
    letter-spacing: 1.5px;
    font-size: 1.2em;
    padding: 4px;
    margin-top: 0.5px;
    text-shadow: 2px 2px 6px #888;
    font-family: 'Roboto', sans-serif;
 }

 .navbar-inverse .navbar-nav > li > div:hover {
	background: transparent;
 }

 .navbar-inverse .navbar-nav > li  button {
    border: none;
    background: none;
	color: #FFF;
 }

.navbar-right .dropdown-menu {
	width: 260px;
	z-index: 10000;
}

.custom-left-nav > li.small > a {
	margin-left: 20px;
	padding: 6px 10px;
}

.dropdown-menu > li  {
    border-bottom: 1px solid #CCC;
    text-shadow: 0 0;
    transition: background 0.5s;
 }

 .dropdown-menu > li:last-child {
    border: none;
 }

 .dropdown-menu > li:hover,
 .dropdown-menu > li a:hover  {
    background: #eee;
 }

 .dropdown-menu li a {
	padding: 0 6px;
	color: #666;
	padding: 12px 24px;
	transition: color 0.5s;
}

.dropdown-menu li:hover a {
	color: #209be1;
}

 .contant-container-nav a {
	 font-size: 1.2em;
	 color: #209be1;
	 padding: 14px;
	 text-decoration: none;
	 text-shadow: 0 0 #209be1;
	 letter-spacing: 1.0px;
 }

  .contant-container-nav li.active a {
	 color: #c7c7c7;
 }

/*.input-group-addon:last-child */
 .input-group-addon {
	position: absolute;
    top: 24px;
    right: 16px;
    z-index: 3;
    border-left-width: 2px;
    border-radius: 0 4px 4px 0 !important;
    padding: 5px 20px 8px 10px;
 }

 .navbar-brand {
	 padding: 5px 15px;
 }

 label {
	color: #777;
	letter-spacing: 0.8px;
    font-weight: normal;
	text-transform: uppercase;
	font-size: 0.9em;
	text-shadow: 0px 0px 0px #777;
	margin-bottom: 0;
 }

 .margin-top-15 {
    margin-top: 15px;
 }

.checkbox {
	padding-left: 35px !important;
}

.checkbox-margin {
	margin-top: 35px;
}

.btn-margin {
	margin-top: 20px;
}

.btn-margin-1 {
	margin-top: 18px;
}

.btn-margin-2 {
	margin-top: 22px;
}

.chk-margin {
	margin-top: 27px;
}

.multi-checkbox-table {
	width: 100%;
}

.multi-checkbox-table td {
	width: 250px;
}

.row-with-padding {
	padding: 0 30px;
}

.checkbox-border {
	padding: 14px !important;
    margin: 0;
    padding-left: 35px !important;
    border-bottom: none !important;
    border-left: none !important;
    border-right: none !important;
}

.maring-tb-10 {
	margin-top: 10px;
	margin-bottom: 10px;
}

.custom-label {
    color: #838383;
    display: block;
    max-width: 100%;
    margin-bottom: 5px;
    font-weight: bold;
}

.margin-left-minus-20 {
	margin-left: -20px;
}

.remove-padding {
	padding: 0 !important;
}

.remove-margin {
	margin: 0 !important;
}

.remove-left-padding {
	padding-left: 0 !important;
}

.remove-right-padding {
	padding-right: 0 !important;
}

.remove-chk-padding {
	padding-left: 20px !important;
}

.store-bg-red {
	color: #f95755;
	font-size: 16px;
	border: none !important;
	border-bottom: 3px solid #f95755 !important;
	text-align: left !important;
	background: #FFF;
}

.store-bg-green {
	color: #21b9aa;
	font-size: 16px;
	border: none !important;
	border-bottom: 3px solid #21b9aa !important;
	text-align: left !important;
	background: #FFF;
}

.store-bg-orange {
	color: #e6983c;
	font-size: 16px;
	border: none !important;
	border-bottom: 3px solid #e6983c !important;
	text-align: left !important;
	background: #FFF;
}

.total-font-style {
	font-size: 18px;
	font-weight: bold;
}

.custom-table .table-total-col {
	background: none !important;
}

.custom-table .table-total-col:hover {
	background: none !important;
}

.custom-table .table-total-col td{
	border: none !important;
}

.dhtmlxcalendar_dhx_skyblue {
	display: none !important;
}

.datepicker[readonly] {
	background: #FFF;
}

.mandatory_mark{
	color: #dd4b39;
	font-size: 0.95em;
	position: absolute;
	margin-left: 3px;
}

.fa.fa-trash-o {
	font-size: 18px;
}

.ui-autocomplete {
	max-height: 232px;
	max-width: 600px;
	overflow-y: auto;
	z-index: 10000 !important;
}

.datepicker-dropdown {
	z-index: 10000 !important;
}

.datepicker.datepicker-dropdown {
	z-index: 10040 !important;
}

.ui-autocomplete .ui-menu-item {
	padding: 5px 10px;
	border-bottom: 1px solid #CCC;
}

.accordion{margin-bottom:20px;}
.accordion-group{margin-bottom:6px;border:2px solid #e5e5e5;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;}
.accordion-heading{border-bottom:0;color: #333;background-color: #f5f5f5;border-color: #ddd;}
.accordion-heading .accordion-toggle{display:block;padding:8px 15px; color: #333; font-size: 18px;}
.accordion-toggle{cursor:pointer;}
.accordion-inner{padding:9px 15px;border-top:1px solid #e5e5e5;}



.input-group-addon, .delete_tag {
	cursor: pointer;
}

.div-disable {
	pointer-events: none;
	opacity: 0.8 !important;
	display: inline-block !important;
	background: #eee;
	padding: 15px 0;
	width:  100%;
}

.div-disable input[type="text"],
.div-disable select,
.div-disable textarea,
.div-disable div {
	background: #eee !important;
}

.slide_container_part .menu-icon{
	width: 32px;
}

.slide_container_part .menu-text{
	font-size: 10px;
	text-transform: uppercase;
	display: block;
	margin-top: 2px;
}

.slide_container {
	position: fixed;
	left: 0;
	z-index: 10010;
	cursor:pointer;
	top: 0;
	width: 85px;
	height: 100%;
	background: #004195;
	color: #FFF;
	margin-top: 50px;
}

.slide_container_part {
	margin: 10px 0;
	padding: 8px 0;
	text-align: center;
}

.slide_container .slide_container_part:first-child {
	margin-top: 0;
	padding-top: 13px;
}

.slide_container_part.active,
.slide_container_part.selected {
	background: #209be1;
}

.slide_container_part.selected.active {
	background: #209be1 !important;
}

.page-heading {
    width: calc(100% - 85px);
	display: flex;
	position: absolute;
	align-items: center;
	justify-content: center;
}

.main-menu-container {
	padding: 0;
    margin: 0;
}

.align-middle {
	vertical-align: middle !important;
}

.span-notification {
    position: absolute;
    top: 6px;
    left: 36px;
    font-size: 12px;
}
.span-notification[data-badge]:after {
	content: attr(data-badge);
    background: #ff4500;
    color: #FFF;
    border-radius: 4px;
    padding: 3px 6px;
    letter-spacing: 0;
    box-shadow: 2px 2px 4px -1px #333;
}

.notification-count {
    position: absolute;
    margin-top: -53px;
    right: 10px;
    font-size: 11px;    
    opacity: 0.9;
}

.notification-count[data-badge]:after {
	content: attr(data-badge);
    background: #ff7f50;
    color: #FFF;
    border-radius: 2px;
    padding: 2px 3px 2px 4px;
    box-shadow: 2px 2px 4px -1px #333;
}

.inline-notification-count {
    font-size: 11px;
    opacity: 0.9;
    display: inline-block;
    vertical-align: top;
    margin-top: 2px;
    margin-left: 7px;
    margin-right: 0;
}

.inline-notification-count[data-badge]:after {
	content: attr(data-badge);
    background: #ff7f50;
    color: #FFF;
    border-radius: 3px;
    padding: 4px 6px 4px 7px;
}

.sub-notification-count {
    margin-top: -25px;
    margin-right: 15px;
    font-size: 11px;
    opacity: 0.9;
    float: right;
}

.sub-notification-count[data-badge]:after {
	content: attr(data-badge);
    background: #ff7f50;
    color: #FFF;
    border-radius: 3px;
    padding: 3px 4px 3px 4px;
    min-width: 20px;
    display: inline-block;
    text-align: center;
}
.sub-notification-count[data-badge]:hover:after {
	content: attr(data-badge);
    background: #ff4500;
    color: #FFF;
    border-radius: 4px;
    padding: 3px 4px 3px 4px;
    box-shadow: 2px 2px 4px -1px #333;
    min-width: 20px;
    display: inline-block;
    text-align: center;
    cursor:pointer;
}

.heading-margin {
	margin-top: 60px;
}

.table-notification {
	position: absolute;
    background: #FFF;
    color: #444;
    right: -54px;
	top: 45px;
    width: 350px;
    z-index: 10000;
	display: block;
    max-height: 320px;
    height: 320px;
	overflow-y: auto;
    min-width: 350px;
	font-size: 0.7em;
	letter-spacing: 0;
	border: solid 1px #aaa;
	text-shadow: 0 0;
}

.table-notification .delete-all {
	position: fixed;
    top: 369px;
	margin-left: -1px;
	cursor: pointer;
    background: #ddd;
    width: 350px;
    border: solid 1px #aaa;
    border-top: none;
    border-radius: 0 0 4px 4px;
    padding: 0 0 0 130px;    
}

.table-notification .delete-all:hover td {
	color: #209be1;
	text-decoration: underline;
}

.table-notification .delete-all td{
	border: none;
}

.table-notification .deleteNotification {
	float:right;
	cursor: pointer;
}

.table-notification .tdNotification {
	line-height: 1.2;
    word-break: break-all;
    width: 100%;
}

.unit_display {
	margin-top: -34px;
    background: #CCC;
    padding: 8px;
    border-radius: 0 4px 4px 0;
    margin-right: 1px;
    min-width: 45px;
    max-width: 45px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    height: 34px;
}

.unit_display.unit_display-small {
    margin-top: -25px;
    padding: 5px 8px;
    height: 26px;
}

.text_for_tag {
	display: block;
	float: left;
	width: 250px;
	background:  #eee;
}

.text_for_tag input {
	border: none;
	box-shadow: 0 0 !important;
	padding: 4px;
	margin-top: -2px;
}

ul.tagit-display {
	padding: 0px;
    overflow: auto;
	min-height: 34px;
	border-radius: 4px;
	height: auto;
	background: transparent;
}

ul.tagit-display .li-tagit-display {
	border-radius: 4px;
	cursor: pointer;
    -webkit-border-radius: 4px;
    border: 1px solid #ddd;
    background: none;
    background: #eee;
	padding: .1em .4em;
	display: block;
    float: left;
    margin: 4px 2px 2px 4px;
}

ul.tagit-display .li-tagit-display:hover {
	background: #ddd;
}

ul.tagit-display .li-tagit-display label {
	margin: 0;
	text-transform: initial;
	font-size: 12px;
	color: #777;
	cursor: pointer;
}

.flash {
	animation-name: flash;
	animation-duration: 0.7s;
	animation-timing-function: linear;
	animation-iteration-count: 1;
	animation-direction: alternate;
	animation-play-state: running;
 }

 @keyframes flash {
	 from {background:#ff9696;}
	 to {background: #DEE7F8;}
 }

 .delete_tag {
	background-image: url(../images/ui-icons_666666_256x240.png);
	background-position: -80px -126px;
    display: block;
    background-repeat: no-repeat;
    width: 16px;
    height: 16px;
    float: right;
}

.accordion-group>.accordion-heading a:after {
  content: "";
  position: relative;
  top: 6px;
  font-size: 14px;
  display: inline-block;
  font-family: 'Glyphicons Halflings';
  font-style: normal;
  font-weight: 400;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  float: right;
  transition: transform .25s linear;
  -webkit-transition: -webkit-transform .25s linear;
}

.accordion-group>.accordion-heading a[aria-expanded="true"] {
  background-color: #eee;
}

.accordion-group>.accordion-heading a[aria-expanded="true"]:after {
  content: "\2212";
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
  float: right;
}

.accordion-group>.accordion-heading a[aria-expanded="false"]:after {
  content: "\002b";
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
  float: right;
}

.fa.fa-inr.fa-black {
	color: #000;
}

.fa.fa-inr.fa-4 {
	font-size: 15px;
}

.report-range,
.report-range_2,
.report-date,
.report-full-date {
	cursor: pointer;
    padding: 7px 10px;
}

.report-range .caret,
.report-range_2 .caret,
.report-date .caret,
.report-full-date .caret {
    float: right;
    margin-top: 8px;
}

.table.sm-padding td {
	padding: 5px;
}


.bill-copy,
.document_attachment {
	display: inline-block;
	width: 32px;
	height: 32px;
}

.bill-copy.png, .document_attachment.png,
.bill-copy.jpg, .document_attachment.jpg,
.bill-copy.jpeg, .document_attachment.jpeg,
.bill-copy.gif, .document_attachment.gif,
.bill-copy.pdf, .document_attachment.pdf {
	cursor: pointer;
}

.bill-copy.png,
.document_attachment.png {
	background: url(../images/document_icons/icon-png.png);
}

.bill-copy.jpg, .document_attachment.jpg,
.bill-copy.jpeg, .document_attachment.jpeg {
	background: url(../images/document_icons/icon-jpg.png);
}

.bill-copy.gif,
.document_attachment.gif {
	background: url(../images/document_icons/icon-gif.png);
}

.bill-copy.pdf,
.document_attachment.pdf {
	background: url(../images/document_icons/icon-pdf.png);
}

.bill-copy.xls,
.document_attachment.xls {
	background: url(../images/document_icons/icon-xls.png);
}

.bill-copy.xlsx,
.document_attachment.xlsx {
	background: url(../images/document_icons/icon-xlsx.png);
}

.bill-copy.csv,
.document_attachment.csv {
 	background: url(../images/document_icons/icon-csv.png);
}

.bill-copy.doc, .document_attachment.doc,
.bill-copy.docx, .document_attachment.docx {
	background: url(../images/document_icons/icon-doc.png);
}

.bill-copy.json,
.document_attachment.json {
	background: url(../images/document_icons/icon-json.png);
}

.bill-copy.log,
.document_attachment.log {
	background: url(../images/document_icons/icon-log.png);
}

.bill-copy.md,
.document_attachment.md {
	background: url(../images/document_icons/icon-md.png);
}

.bill-copy.svg,
.document_attachment.svg {
	background: url(../images/document_icons/icon-svg.png);
}

.bill-copy.txt,
.document_attachment.txt {
	background: url(../images/document_icons/icon-txt.png);
}

.bill-copy, .document_attachment.attached-file {
	background: url(../images/document_icons/file_format.png);
	display: inline-block;
    width: 60px;
    height: 60px;
    color: #FFF;
    letter-spacing: 2px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    padding: 41px 3px 0;
}

.table-inline-icon {
    cursor: pointer;
    padding: 12px 7px 9px 11px;
    color: #666;
    border-radius: 50px;
    background: transparent;
    text-decoration: none;
    transition: all 0.6s;
    margin: -3px
}

.table-inline-icon .fa {
    color: #000;
}

.table-inline-icon.internal {
	padding:  10px 9px 7px 12px;
	margin: -9px 0;
    position: absolute;
    right: 50px;
}

.table-inline-icon.internal.edit-in-new {
	right: 15px;
}

.table-inline-icon:hover {
	background: rgba(204, 204, 204, 0.5);
	color: #000;
}

.table-inline-icon .fa.fa-file-text,
.table-inline-icon .fa.fa-download,
.table-inline-icon .fa.fa-envelope,
.table-inline-icon .fa.fa-bars,
.table-inline-icon .fa.fa-times,
.table-inline-icon .fa.fa-refresh {
    font-size: 16px;
    width: 18px;
    height: 18px;
}

.table-inline-icon .fa.fa-external-link {
    font-size: 17px;
    width: 18px;
    height: 18px;
}

.table-inline-icon .fa.fa-pencil,
.table-inline-icon .fa.fa-paperclip,
.table-inline-icon .fa.fa-trash-o,
.table-inline-icon .fa.fa-pencil-square-o {
    font-size: 18px;
    width: 18px;
    height: 18px;
}

@media only screen and (max-width: 1600px) {
  	.table-inline-icon {
  		padding: 10px 5px 7px 9px;
  	}

  	.table-inline-icon .fa {
  		font-size: 16px;
  		width: 16px;
	    height: 16px;
  	}
}

.table_txt_padding {
	padding: 2px;
}

.table_txt {
	border: solid #ddd 1px;
	padding: 3px 6px;
	border-radius: 4px;
	width: 100px;
}

input.disabled {
	cursor: not-allowed;
	background: #eee;
}

.disable_download {
	cursor: not-allowed;
	opacity: .65;
}

.checkbox-block label{
	display: block;
}

.div-disabled {
    pointer-events: none;
    background-color: #eee !important;
}

.div-disabled input[type="text"],
.div-disabled select,
.div-disabled textarea,
.div-disabled div,
.div-disabled ul {
	background-color: #eee;
	cursor: not-allowed;
}

.checkbox-block .sub-checkbox-block{
	padding: 0;
	margin:0;
	margin-top: 6px;
}

.modal-multiselect .checkbox {
	/*padding: 15px 5px;
    border: 1px solid #d8d8d8;
    border-radius: 4px;*/
}

.modal-multiselect .multiselect-header {
	padding: 6px 35px !important;
	margin: 10px 0;
	background: #eee;
	color: #838383;
	border: 1px solid #d8d8d8;
    border-radius: 4px;
}

.modal-multiselect .multiselect-header span {
	padding-left: 20px;
	color: #000;
}

.modal-multiselect input[type="text"] {
	height: 22px;
	padding: 2px 12px;
}

.modal-multiselect .checkbox_text {
	margin: 6px 0px;
}

.modal-body {
    max-height: calc(100vh - 180px);
    overflow-y: auto;
}



.chat-container {
	width: 100%;
	background: #eee;
	border-radius: 4px;
	padding: 6px;
	max-height: 350px;
	overflow-y: auto;
	text-align: left;
}

.chat-list {
	margin: 6px;
	border: solid 1px #ccc;
	background:#FFF;
	padding: 6px;
	border-radius: 4px;
}

.chat-list-description {
	display: block;
	font-size: 12px;
	word-break: break-word;
}

.chat-list-name {
	color: #004195;
	font-size: 14px;
	text-shadow: 0 0 #004195;
}

.chat-list-date {
	float: right;
	padding-left: 10px;
	font-size: 12px;
	margin-top: 2px;
}

.sweet-alert.showSweetAlert.swal-remarks {
	max-height: 500px;
    width: 500px;
    left: 45.3%;
}

/* ERP Styled Checkbox */
.styled-checkbox {
	position: absolute;
	opacity: 0;
}

.styled-checkbox + label {
	position: relative;
	cursor: pointer;
	padding: 0;
}

.styled-checkbox + label:before {
	content: '';
	margin-right: 10px;
	display: inline-block;
	vertical-align: text-top;
	width: 18px;
	height: 18px;
	background: white;
	border: 1px solid #cccccc;
	border-radius: 3px;
}

.styled-checkbox.chk-wo-margin + label:before {
	margin-right: 0;
}


.styled-checkbox:hover + label:before {
	background: #209be1;
}

.styled-checkbox:focus + label:before {
	box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.12);
}

.styled-checkbox:checked + label:before {
	background: #209be1;
	border-radius: 2px;
}

.styled-checkbox:disabled + label {
	color: #b8b8b8;
	cursor: auto;
}

.styled-checkbox:disabled + label:before {
	box-shadow: none;
	background: #ddd;
}

.styled-checkbox:checked + label:after {
	content: '';
	position: absolute;
	left: 4px;
	top: 9px;
	background: white;
	width: 2px;
	height: 2px;
	box-shadow:
	  2px 0 0 white,
	  4px 0 0 white,
	  4px -2px 0 white,
	  4px -4px 0 white,
	  4px -6px 0 white,
	  4px -8px 0 white;
	transform: rotate(45deg);
}

.styled-checkbox:checked + label.partial:after {
	content: '';
    position: absolute;
    left: 4px;
    top: 5px;
    background: transparent;
    width: 2px;
    height: 2px;
    box-shadow: 2px 0 0 transparent, 4px 0 0 white, 4px -2px 0 white, 4px -4px 0 white, 4px -6px 0 white, 4px -8px 0 white;
    transform: rotate(90deg);
}

/* End ERP Styled Checkbox */

.chosen-container,
.form-control {
	box-shadow: 3px 3px 6px #eaeaea;
}

.form-group {
	margin-bottom: 18px;
}

.logo_in_menu {
	width: 40px;
	margin-left: 10px;
	margin-top:4px;
	margin-bottom: 8px;
}

.menu-disable {
	position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1040;
    background-color: #000;
	opacity: .5;
}

.menu-backdrop {
    position: fixed;
    top: 50px;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1040;
    background-color: #000;
	transition: opacity 1s linear;
	opacity: .5;
}

.erp-logo-container img {
	width: 40px;
	margin: 4px 7px;
	position: fixed;
}

.td-ellipsis-text-200 {
	width: 200px;
    text-overflow: ellipsis;
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
}

.anchor-ellipsis,
.anchor-ellipsis:hover {
	color: #444;
	text-decoration: none;
}

.ranges li.custom-range-disable,
.ranges li.custom-range-disable:hover {
	background: #ddd;
    color: #999;
    text-decoration: none;
    cursor: auto;
    border: solid #ddd 1px;
    pointer-events: none;
}

.ranges li.custom-range-disable.active {
	background-color: #08c;
    border: 1px solid #08c;
    color: #fff;
	border: solid #08c 1px;
}

@keyframes blink {
    0% {
      opacity: 0;
    }
    20% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
}

.saving span {
    animation-name: blink;
    animation-duration: 1.4s;
    animation-iteration-count: infinite;
    animation-fill-mode: both;
}

.saving span:nth-child(2) {
    animation-delay: .2s;
}

.saving span:nth-child(3) {
    animation-delay: .4s;
}

.processing {
	cursor: auto;
	pointer-events: none;
}

.btn.btn-processing {
	cursor: auto;
	pointer-events: none;
	background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent) !important;
	animation: progress-bar-stripes 2s linear infinite;
	background-color: #209be1 !important;
	-webkit-background-size: 40px 40px !important;
    background-size: 40px 40px !important;
    opacity: 0.7;
}

.btn.btn-theme-processing {
	cursor: not-allowed;
	pointer-events: none;
	background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent) !important;
	animation: progress-bar-stripes 2s linear infinite;
	background-color: rgba(136, 159, 250, 1) !important;
	-webkit-background-size: 40px 40px !important;
    background-size: 40px 40px !important;
    opacity: 0.7;
    box-shadow: 0 0 !important;
}

.btn.btn-processing-others {
	cursor: auto;
	pointer-events: none;
	opacity: 0.7;
}

.loading-main-container {
	z-index: 100000;
	width: 100%;
	height: 100%;
	background-color: rgb(0, 0, 0);
	opacity: 0.8;
	cursor: progress;
	position: fixed;
	display: none;
}

.loading-sub-container {
	z-index: 1011;
	position: absolute;
	top: 0;
	left: 0;
	right:0;
	bottom:0;
	margin: auto;
	cursor: wait;
	background: linear-gradient( rgba(0, 0, 0, 1),rgba(0, 0, 0, 1),rgb(255, 255, 255), rgb(255,255,255));
    -webkit-border-radius:50px;
    -moz-border-radius:50px;
    width:45px;
    height:45px;
	animation: cssload-spin 690ms infinite linear;
	-o-animation: cssload-spin 690ms infinite linear;
	-ms-animation: cssload-spin 690ms infinite linear;
	-webkit-animation: cssload-spin 690ms infinite linear;
	-moz-animation: cssload-spin 690ms infinite linear;
}

.loading-sub-container:after {
	  position: absolute;
	  display: block;
	  top: 5px;
	  left: 5px;
	  width: 35px;
	  height: 35px;
	  content: "";
	  background-color: #000;
	  background-repeat: no-repeat;
	  background-size: cover;
	  border-radius: 50%;
	  overflow: hidden;
}

.loading-sub-text {
	color: #fff;
    height: 100%;
    width: 100%;
    display: flex;
    position: fixed;
    align-items: center;
    justify-content: center;
    margin-top: 60px;
    font-size: 26px;
    letter-spacing: 1.5px;
    color: #fff;
    font-weight: 100;

}

@keyframes cssload-spin {
	100%{ transform: rotate(360deg); transform: rotate(360deg); }
}

@-o-keyframes cssload-spin {
	100%{ -o-transform: rotate(360deg); transform: rotate(360deg); }
}

@-ms-keyframes cssload-spin {
	100%{ -ms-transform: rotate(360deg); transform: rotate(360deg); }
}

@-webkit-keyframes cssload-spin {
	100%{ -webkit-transform: rotate(360deg); transform: rotate(360deg); }
}

@-moz-keyframes cssload-spin {
	100%{ -moz-transform: rotate(360deg); transform: rotate(360deg); }
}

.loading-sub-text span {
	animation: text-color-change 1s infinite;
}

@keyframes text-color-change {
    0% { color: white; }
    50% { color: black; }
    100% { color: white; }
}

.downloading-main-container {
	z-index: 100000;
	width: 100%;
	height: 100%;
	background-color: rgb(255, 255, 255);
	opacity: 0.7;
	cursor: wait;
	position: fixed;
	display: none;
}

.downloading-sub-container {
	z-index: 1011;
	position: absolute;
	top: 0;
	left: 0;
	right:0;
	bottom:0;
	margin: auto;
	cursor: wait;
	background-image: url('/site_media/images/downloading.gif');
	background-repeat: no-repeat;
	background-attachment: fixed;
	background-position: center; 
	background-size: 80px;
}

.downloading-sub-container:after {
	  position: absolute;
	  display: block;
	  top: 5px;
	  left: 5px;
	  width: 50px;
	  height: 50px;
	  content: "";
	  background-color: #fff;
	  background-repeat: no-repeat;
	  background-size: cover;
	  border-radius: 50%;
	  overflow: hidden;
}

table.side-table .side-header {
	background: #209be1;
	color: #FFF;
	max-width: 160px;
	width: 160px;
}

table.side-table {
	width: 100%;
	border-collapse: collapse;
}

table.side-table tr td {
	padding: 6px;
	border: solid 1px #ccc;
	vertical-align: middle;
	font-size: 12px;
}

table.side-table .side-content input {
	padding: 0;
    border: none;
    border-radius: 0;
    box-shadow: 0 0;
    height: 18px;
    font-size: 12px;
    color: #000;
}

table.side-table .bootstrap-datetimepicker-widget tr td {
	border:  none;
}

.text_box_label  {
 	border: none;
 	background-color: transparent !important;
 	outline: none;
 	box-shadow: 0 0;
}

.textbox_amt,
.textbox_header_amt {
	width: 100px;
	max-width: 100px;
}

.textbox_header_amt_md {
	width: 150px;
	max-width: 150px;
}

.textbox_amt_md {
	width: 120px;
	max-width: 120px;
}

.textbox_amt_sm,
.textbox_header_amt_sm {
	width: 100px;
	max-width: 100px;
}

.td_for_icon {
	width: 60px;
	max-width: 60px;
}

/*Processing dot animation */
.savingDots {
	color: #FFFFFF;
}

.savingDots span {
	font-size: 24px;
    float: right;
    margin-top: -10px;
    margin-bottom: -8px;
}

.savingDots span:nth-child(3) {
	opacity: 0;
	-webkit-animation: dot 1.0s infinite;
	-webkit-animation-delay: 0.0s;
	animation: dot 1.0s infinite;
	animation-delay: 0.0s;
}

.savingDots span:nth-child(2) {
	opacity: 0;
	-webkit-animation: dot 1.0s infinite;
	-webkit-animation-delay: 0.1s;
	  animation: dot 1.0s infinite;
	  animation-delay: 0.1s;
}

.savingDots span:nth-child(1) {
	opacity: 0;
	-webkit-animation: dot 1.0s infinite;
	-webkit-animation-delay: 0.2s;
	 animation: dot 1.0s infinite;
	 animation-delay: 0.2s;
}

@-webkit-keyframes dot {
	  0% { opacity: 0; }
	 50% { opacity: 0; }
	100% { opacity: 1; }
}

@keyframes dot {
	  0% { opacity: 0; }
	 50% { opacity: 0; }
	100% { opacity: 1; }
}

.sweet-alert.showSweetAlert {
	max-height: 400px;
	overflow: auto;
	margin-top: -200px !important;
	margin-left: -200px !important;
	word-break: break-word;
}

.div_download_csv {
	margin-bottom: 8px;
	text-align: right;
}

.table-bordered > thead > tr > th.remove-th-border,
.table-bordered > tbody > tr > td.remove-td-border {
	border-left: none;
	border-right: none;
}

.page-heading_new {
    /*border-bottom: 1px solid #e4e9ec;*/
    margin-top: 4px;
    padding: 0 15px;
}

.page_header,
.page_header_issue,
.page_header_indent,
.page_header_grn,
.page_header_invoice {
	font-size: 26px;
	float: left;
	/*margin-bottom: 15px;*/
	margin-top: 2px;
}


.header_current_page,
.header_current_page_revision {
	font-size: 20px;
}

.remove-padding-right {
	padding-right: 0;
}

.remove-padding-left {
	padding-left: 0;
}

footer {
    text-align: center;
    width: 100%;
    background: #fff;
    display: inline-block;
    color: #aaa;
    font-size: 0.82em;
    letter-spacing: 0.3px;
    margin-top: 12px;
}

footer a {
	color: #aaa;
}

footer a:hover {
	color: #209be1;
}

@keyframes blink {
    0% {
      opacity: .2;
    }
    20% {
      opacity: 1;
    }
    100% {
      opacity: .2;
    }
}

.loading span {
	font-size: 26px;
    animation-name: blink;
    animation-duration: 1.4s;
    animation-iteration-count: infinite;
    animation-fill-mode: both;
}

.loading span:nth-child(2) {
    animation-delay: .2s;
}

.loading span:nth-child(3) {
    animation-delay: .4s;
}

.checkbox.theme_checkbox input[type="checkbox"]:checked + label::after {
	background: #209be1;
    border-radius: 2px;
    color: #FFF;
}

#tags .ui-autocomplete-loading,
#po_tags_table .ui-autocomplete-loading {
	background:url('/site_media/images/ajax-loader.gif') no-repeat right center;
	background-position: 97%;
}

.custom-datepicker-icon-table {
    margin-top: -20px;
    float: right;
    margin-right: 10px;
    cursor: pointer;
}

.custom-datepicker-icon {
    margin-top: -24px;
    float: right;
    margin-right: 10px;
    cursor: pointer;
}

.single_datePicker_table {
	padding: 3px;
	height: 26px;
}
/* Toggle button Style

.toggle.btn {
	width: 100% !important;
	margin-top: -8px;
	background: #209be1;
	border-radius: 25px;
	margin-top: 8px;
}

.toggle .toggle-handle {
	border-radius: 50px;
    margin-left: -35px;
    height: 25px;
    margin-top: 3px;
}

.toggle .toggle-handle circle {
    display: block;
    margin-left: -9px;
    margin-top: 5px;
    font-weight: bold;
    width: 20px;
    background: url(/site_media/images/left_arrow.png) no-repeat;
    height: 14px;
}

.toggle.off .toggle-handle circle {
    background: url(/site_media/images/right_arrow.png) no-repeat;
}

.toggle.off .toggle-handle {
    margin-left: 35px;
}

.toggle .btn-primary {
	background: #004195;
	border-color: transparent;
}

.toggle .btn-primary:hover {
	background: #004195;
	border-color: transparent;
	opacity: 0.8;
}

.toggle .btn-default {
	background: #209be1;
	border-color: transparent;
	color: #FFF;
}

.toggle .btn-default:hover {
	background: #209be1;
	border-color: transparent;
	color: #FFF;
	opacity: 0.8;
}

.toggle .toggle-handle.btn-default {
	background: #FFF;
}
*/
.table .table_text_box,
.table td.table_text_box input[type="text"],
.table.tableWithText td input[type="text"],
.table.tableWithText td select {
	padding: 6px;
	height: 26px;
	box-shadow: 0 0;
	font-size: 12px;
}

.table td.table_text_box select {
	padding: 3px 6px;
	height: 26px;
	box-shadow: 0 0;
	font-size: 12px;
}

.table.text_box_in_table td input[type="text"],
.table.text_box_in_table td select {
	padding: 4px 6px;
	height: 26px;
	box-shadow: 0 0;
	font-size: 12px;
}

.table.text_box_in_table .btn {
	padding: 2px 8px;
}

.alert-badge-disable {
	opacity: 0.5;
	pointer-events: none;
}

@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
@-o-keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
@keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}

.table-textbox-error-border {
	border: 1px solid #dd4b39 !important;
	color:  #dd4b39 !important;
}
.table-textbox-error-border:focus:not([readonly]) {
	border: 1px solid #dd4b39 !important;
	box-shadow: 0 0 !important;
	color:  #dd4b39 !important;
}

.custom-table td.total_amount_field {
	color: #838383;
	font-size: 16px;
    letter-spacing: 0.8px;
    font-weight: normal;
    text-transform: uppercase;
}

.textbox-as-label {
	border: none !important;
    background-color: transparent !important;
    outline: none !important;
    box-shadow: 0 0 !important;
}
.textbox-as-text {
	border: none !important;
    background-color: transparent !important;
    outline: none !important;
    box-shadow: 0 0 !important;
    padding: 0 !important;
}

.custom-table .form-control.amt-total-as-label,
.amt-total-as-label {
	border: none !important;
    background-color: transparent !important;
    outline: none !important;
    font-size: 16px !important;
    color: #838383;
    letter-spacing: 0.8px;
}

.text-total-as-label {
	color: #838383 !important;
    letter-spacing: 0.8px !important;
    font-weight: normal !important;
    text-transform: uppercase !important;
    max-width: 100% !important;
    font-size: 16px !important;
    padding-right: 30px !important;
}

.text-totalamt-as-label {
	color: #838383 !important;
    letter-spacing: 0.8px !important;
    font-weight: normal !important;
    text-transform: uppercase !important;
    font-size: 16px !important;    
} 

.textbox-total-amt {
	font-size: 16px !important;
}


.grand-total-text {
	color: #838383 !important;
    letter-spacing: 0.8px !important;
    font-weight: normal !important;
    text-transform: uppercase !important;
    max-width: 100% !important;
    font-size: 16px !important;
    padding-right: 30px !important;
}

.td-grand-total {
	color: #838383 !important;
    letter-spacing: 0.8px !important;
    font-weight: normal !important;
    font-size: 0.8vw !important;
}

.custom-table.custom-table-large td.grand-total-text {
	padding-right: 30px !important;
	font-size: 0.9vw !important;
}

.grand-total-amount {
	border: none !important;
    background-color: transparent !important;
    outline: none !important;
    box-shadow: 0 0 !important;
	color: #838383 !important;
    letter-spacing: 0.8px !important;
    font-weight: normal !important;
    text-transform: uppercase !important;
    font-size: 20px !important;  
    text-align: right !important;
    width: 100%;
    padding:  0 !important;
}

.info_message {
    color: #8a6d3b;
    background-color: #f5e79e;
    border-color: red;
    padding: 10px 15px;
    margin-bottom: 10px;
    border: 1px solid transparent;
    border-radius: 4px;
    text-shadow: 0px 0px red;
    margin-top: 10px;
}

.info_text {
	color: #8a6d3b;
	text-shadow: 0px 0px red;
}

 .pagination-warning-for-all {
 	color: #8a6d3b;
    background-color: #f5e79e;
    border-color: red;
    padding: 10px 15px;
    margin-bottom: 10px;
    border: 1px solid transparent;
    border-radius: 4px;
    text-shadow: 0px 0px red;
    margin-top: 10px;
    position: absolute;
    margin-left: 190px;
    margin-top: -33px;
 }

 .general-warning {
    color: #8a6d3b;
    background-color: #fcf8e3;
    border-color: #faebcc;
    padding: 15px;
    margin-bottom: 20px;
    border: 2px solid #faebcc;
    border-radius: 6px;
 }

/* XS ID EDIT */
.xsid_number_edit {
	display: inline-block;
    float: left;
    margin-top: -1px;
    margin-left: 5px;
}

.xsid_number_edit div select,
.xsid_number_edit div input {
	border-radius: 0;
	margin-left: -5px;
	text-align: center;
}

.xsid_number_edit div:first-child input {
	border-radius: 4px 0 0 4px;
	width: 65px;
}

.xsid_number_edit div:nth-child(2) select,
.xsid_number_edit div:nth-child(2) input {
	width: 70px;
}

.xsid_number_edit input:focus,
.xsid_number_edit select:focus {
	outline: none !important;
  	box-shadow: none;
  	border-color: #ccc;
}

.xsid_number_edit div:nth-child(3) input {
	width: 80px;
}

.xsid_number_edit div:nth-child(4) input {
	border-radius: 0 4px 4px 0;
	width: 35px;
}

.super_user_icon {
	padding: 0 8px;
    float: left;
    font-size: 20px;
}

.super_user_icon img {
	width: 26px;
	margin-top: 8px;
}

.super_edit_submit_icon {
	width: 95px;
	margin-top: 2px;
	margin-left: 8px;
}

.super_edit_submit_icon span {
	border: solid 1px #ccc;
    padding: 0;
    font-size: 24px;
    padding: 1px 7px;
    margin-right: 5px;
}

.super_edit_submit_icon .super_edit_save {
	border-radius: 4px 0 0 4px;
}

.super_edit_submit_icon .super_edit_cancel {
	border-radius: 0 4px 4px 0;
	margin-left: -9px;
	padding: 1px 10px;
}

.super_edit_error_border {
	border-bottom: solid 2px #dd4b39;
}

.save_xsid_error_format {
	color:  #dd4b39;
	font-size: 12px;
	margin:  5px -10px;
}

.super_user_tool_tip {
	display: inline-block;
    position: relative;
}

.super_user_tool_tip .question-mark {
    border-radius: 50%;
    background-color: #fff;
    background: url(/site_media/images/help_tooltip.png);
    color: #333;
    width: 16px;
    height: 16px;
    display: block;
    margin: -16px 0px 0 0px;
    cursor: pointer;    
}
/*END  XS ID EDIT */

.prev_next_container {
	display: inline-block; 
	float: right;
}

.prev_next_container form{
	display: inline-block;
}

.prev_next_container a.disabled{
    border-color: #aaa;
    color: #aaa;
    cursor: not-allowed;
    pointer-events: auto;
}

@-moz-document url-prefix() {
	.super_edit_field {
		height: 32px !important;
	}
}

.super_edit_field {
    color: #004195;
	margin-top: 18px;
	position: absolute;
	right: 16px;
	z-index: 1000;
	cursor: pointer;
	float: right;
	background: #eee;
	height: 30px;
	padding: 8px 8px;
	border-radius: 0 3px 3px 0;
}

.super_edit_inline_field {
	color: #004195;
    margin-top: 0px;
    position: absolute;
    margin-left: 362px;
    z-index: 1000;
    cursor: pointer;
    float: right;
    background: #eee;
}

.ul_for_tool_tip li {
	padding: 6px 0; 
	margin-left: -15px;
}

.qtip-question-mark {
  	border-radius: 50%;
    background-color: #fff;
    background: url(/site_media/images/help_tooltip.png);
    color: #333;
    width: 16px;
    height: 16px;
    display: inline-block;
    margin: 2px;
    cursor: pointer;    
}

.td_textbox_right input {
	text-align: right;
}

.td_textbox_center input {
	text-align: center;
}

.remarks_count_link {
    float: right;
    color: #209be1;
    cursor: pointer;
    font-size: 14px;
}

.remarks_count_link.disabled {
	pointer-events: none;
	color: #838383;
	font-style: italic;
}

.remarks_count_link:hover {
	text-decoration: underline;
}

.super_edit_in_field {
    margin-top: -2px;    
    font-size: 20px;
}

.switch_radio_btn {
	width:  100%;
}

.switch_radio_btn .noActive{
	color: #3276b1;
	background-color: #fff;
	width:  50%;
}

.switch_radio_btn .active {
	color: #fff;
	background-color: #004195;
	width:  50%;
}

.switch_radio_btn .noActive:hover {
	color: #fff;
	background-color: rgba(0, 65, 149,0.5);
}

.switch_radio_btn.disabled {
	opacity: 0.6;
	pointer-events: none;
}

textarea {
	resize: vertical;
}

.custom-dropdown-menu {
	right: 0;
    left: auto;
    position: absolute;
    top: 100%;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    font-size: 14px;
    text-align: left;
    list-style: none;
    background-color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    border: 1px solid #ccc;
    border: 1px solid rgba(0, 0, 0, .15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
    box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
}

.modal_download_link {
	position: absolute;
    top: 10px;
    right: 40px;
}

.linked_text {
	cursor: pointer;
}

.arrow_box:after, .arrow_box:before {
    bottom: 100%;
    left: 22px;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
}

.arrow_box:after {
	border-color: rgba(136, 183, 213, 0);
    border-bottom-color: #fff;
    border-width: 10px;
    margin-left: -11px;
}
.arrow_box:before {
    border-color: rgba(194, 225, 245, 0);
    border-width: 11px;
    margin-left: -12px;
    border-bottom-color: rgba(0, 0, 0, .55);
}

#tableNotificationContainer.arrow_box:after, 
#tableNotificationContainer.arrow_box:before {
	bottom: -18%;
	left: 51%;
	z-index: 10000;
}

.arrow_box_setting.arrow_box:after,
.arrow_box_setting.arrow_box:before {
	left: 90.5%;
}

.arrow_box_tags.arrow_box:after,
.arrow_box_tags.arrow_box:before {
	left: 93.5%;
}

.arrow_box_menu.arrow_box:after,
.arrow_box_menu.arrow_box:before {
	left: 50%;
}

.arrow_box_log.arrow_box:after,
.arrow_box_log.arrow_box:before {
	left: 89.1%;
    top: 82px;
}

.arrow_box_profile.arrow_box:after,
.arrow_box_profile.arrow_box:before {
	left: 93%;
}

.span-profile-img,
.user_profile_image {
	text-transform: uppercase
}

.txt-part-number {
	margin-bottom: -10px !important;
}

.csv_export_button {
	position: absolute;
    right: 15px;
    bottom: 1px;
    width: 38px;
    z-index: 1;
}

.csv_export_button a {
	padding: 5px 12px;
}

.table-with-upload-button .dataTables_info,
.table-with-upload-button #filter_textbox {
	/*margin-right: 100px;*/
}

.tr-alert-danger , .tr-alert-danger input, .tr-alert-danger select {
	color: #a94442 !important;
	background-color: #f2dede !important;
	border-color: #bbb !important;
}

.tr-alert-danger td {
	border: solid 1px #aaa !important;
}

.main-header {
	text-align: center;
}

.dropdown-header-plus {
	padding: 10px 60px !important;
    border-radius: 0 0 6px 6px;
    box-shadow:  0 1px 2px 0 rgba(60,64,67,0.302),0 1px 3px 1px rgba(60,64,67,0.149);
    font-size: 20px !important;
    border-radius: 0 0 50px 50px;
    background: #fff !important;
    transition: all 0.2s;
}

.dropdown-header-plus:hover {
	background: #ebf8ff !important;
	border-radius: 0 0 50px 50px !important;
	box-shadow:  0 1px 2px 0 rgba(60,64,67,0.302),0 1px 3px 1px rgba(60,64,67,0.149);
}

.dropdown-header-plus[aria-expanded="true"] {
	box-shadow: 0px 0px 5px 0 rgba(60,64,67,0.302), 0 0px 1px 1px rgba(60,64,67,0.149)
}


.main-header .main-header-plus,
.main-header .main-header-search {
    color: #004195;
    display: inline-block;
    margin: -2px;
}

.main-header-plus-container {
	position: absolute;
	left: 0;
	right: 0;
}

.main-header-plus a i.fa-plus{
	color: #004195;
}


.main-header-plus.open .fa-plus:before {
	content: "\f00d";
}

.main-header-search {
	border-radius: 0 0 0 6px;
}

.dropdown-large {
  position: static !important;
}
.dropdown-menu-large {
  margin-left: 16px;
  margin-right: 16px;
  padding: 20px 0px;
}
.dropdown-menu-large > li > ul {
  padding: 0;
  margin: 0;
}
.dropdown-menu-large > li > ul > li {
  list-style: none;
}
.dropdown-menu-large > li > ul > li > a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: normal;
  line-height: 1.428571429;
  color: #333333;
  white-space: normal;
}
.dropdown-menu-large > li ul > li > a:hover,
.dropdown-menu-large > li ul > li > a:focus {
  text-decoration: none;
  color: #262626;
  background-color: #f5f5f5;
}
.dropdown-menu-large .disabled > a,
.dropdown-menu-large .disabled > a:hover,
.dropdown-menu-large .disabled > a:focus {
  color: #999999;
}
.dropdown-menu-large .disabled > a:hover,
.dropdown-menu-large .disabled > a:focus {
  text-decoration: none;
  background-color: transparent;
  background-image: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  cursor: not-allowed;
}
.dropdown-menu-large .dropdown-header {
  color: #428bca;
  font-size: 18px;
}

.dropdown-large .dropdown-menu > li {
	border-bottom: none;
}


.dropdown-menu-large.dropdown-menu {
    left: 0;
    width: 500px;
    right: 0;
    margin: 0 auto;
    margin-top: 13px !important;
}

.dropdown-menu-large li:hover {
	background: #fff;
}

.filter-components {
   	width: calc(100% - 500px);
    margin-left: 320px;
    margin-bottom: -12px;
    margin-top: 25px;
    min-height: 34px;
}

.filtered-condition {
	background: rgba(32, 155, 225, 0.1);
    border: solid 1px rgba(32, 155, 225, 0.4);
    padding: 4px 12px;
    border-radius: 50px;
    cursor: pointer;
    margin-right: 8px;
    display: inline-block;
    margin-bottom: 5px;
}

.filter-components .btn-filter {
    border: none;
    padding: 2px 12px;
    background: #fff;
}

.filter-components .arrow_box_filter {
	margin-top: 2px;
    padding: 8px;
    width: 450px;
}

.filter-components .dropdown {
	display: inline-block;
	padding-bottom: 10px;
}

.filter-components .filter-footer {
	border-top: solid 1px #ccc;
    width: 450px;
    margin-left: -9px;
    padding: 15px 24px 7px 0;
    text-align: right;
    display: inline-block;
}

.filter-components .btn-filter i.fa-filter {
	font-size: 24px;
}

.filtered-condition:hover {
	background:  rgba(32, 155, 225, 0.2);
}

.daterangepicker.dropdown-menu {
	z-index: 10020;
}

.filter-components-container .fa-filter {
	color: rgba(32, 155, 225, 1);
}

.filter-components-container .dropdown.open button.dropdown-toggle {
	background-color: rgba(32, 155, 225, 0.2) !important;
}

.page-title-container {
	margin: 0;
	position: fixed	;
	top: 15px;
	z-index: 10000;
	width: auto;
	left: 84px;
	box-shadow: 1px -7px 2px 0px rgba(60,64,67,0.302), 0px 0px 0px 0px rgba(60,64,67,0.149);
	border-top: 2px solid gray;
	height: 40px;
}

.page-title {
	padding: 8px 30px 0;
    background: #fff;
    height: 44px;
    border: solid 1px #ccc;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
    font-size: 26px;
    float: left;
    margin-top: -11px;
    border-left: 0.5px solid lightgrey;
}

.disabled-upload-container {
	float: right;
	cursor: not-allowed;
}

a.disabled-link {
	pointer-events: none;
	opacity: 0.5;
}

.remove_image {
    font-size: 11px;
    color: #dd4b39;
    position: absolute;
    left: 7px;
    top: -7px;
    border: 2px solid #dd4b39;
    padding: 2px 3px;
    border-radius: 50px;
    z-index: 10;
    cursor: pointer;
    background: #fff;
}

.visible-block {
	display: block;
}

.warning-text {
    color: #ffa500;
}

/* FLOATING TEXT */
.floating-label { 
    position:relative; 
    margin-bottom: 22px;
}
.floating-input , .floating-select {
    font-size: 14px;
    padding: 6px 12px;
    display: block;
    width: 100%;
    height: 34px;
    background-color: transparent;
    border: navajowhite;
    box-shadow: 0 0 !important;
    border-bottom: 1px solid #ccc;
    border-radius: 0;
    padding-left: 3px;
    outline: none;
}

.floating-input:focus , .floating-select:focus {
    outline:none;
}

.floating-label label {
   	margin-top: 6px;
    color: #999;
    font-weight: normal;
    position: absolute;
    pointer-events: none;
    left: 4px;
    top: 9px;
    font-size: 14px;
    letter-spacing: 1px;
    transition:0.2s ease all; 
    -moz-transition:0.2s ease all; 
    -webkit-transition:0.2s ease all;
}

.floating-input:focus ~ label, .floating-input:not(:placeholder-shown) ~ label,
.floating-select:focus ~ label , .floating-select:not([value=""]):valid ~ label {
    top: -18px;
    color: #777;
    left: 4px;
    font-size: 11px;
}

/* active state */
.floating-input:focus ~ .bar:before, .floating-input:focus ~ .bar:after, .floating-select:focus ~ .bar:before, .floating-select:focus ~ .bar:after {
    width:50%;
}

.unit_select_box {
	position: absolute;
    width: auto;
    margin-top: -34px;
    float: right;
    right: 16px;
    border-radius: 0 4px 4px 0;
    background: #eee;
    max-width: 75px;
    padding: 5px;
    text-overflow: ellipsis;
    word-break: break-all;
}

.document_viewer {
	width: 100%;
	height: calc(100vh - 275px);
}

.document_preview_viewer {
	width: 100%;
	height: calc(100vh - 285px);
}

.bracket-enclosed:before {
    content: '(';
}

.bracket-enclosed:after {
    content: ')';
}

.th-sub-heading,
.td-sub-content {
	display: block;
	font-size: 75%;
	text-align: right;
	letter-spacing: 0.5px;
}

.popover[class*="tour-"] {
    z-index: 10020 !important;
}

.material-child-link {
	padding-left: 15px;
    line-height: 16px;
    display: block;
    margin-top: -13px;
    cursor: pointer;
    text-decoration: none;
}

.material-removal-icon,
.supplier-removal-icon {
	float: right;
    background: #555;
    padding: 7px 15px;
    margin-top: -33px;
    border-radius: 0 4px 4px 0;
    display: block;
    color: #fff;
    cursor: pointer;
}

.item-particulars-add-container {
    border: solid 1px #ddd;
    margin: 0;
    padding: 15px;
    border-top: none;
    margin-bottom: 20px;
}

.confirm-box-info {
	position: absolute;
    right: 15px;
    bottom: -54px;
    font-size: 12px;
    color: #004195;
    border: solid 1px #004195;
    padding: 3px 7px;
    border-radius: 50px;
    cursor: help;
}

.nonStock-mark {
    background: url('/site_media/images/star.png');
    background-repeat: no-repeat;
    padding-left: 20px;
}

.nonStock-text {
	font-size:  11px;
	color:  #666;
	letter-spacing: 1px;
}

.header-textbox {
	font-size: 18px;
	border: none;
	border-bottom: 2px solid #209be1;
	border-radius: 0;
	padding:  0;
	box-shadow: 0 0;
}

.header-textbox:focus {
	outline: none !important;
    box-shadow: none;
}

.header-label {
	font-size: 18px;
	padding: 10px 0px;
}

.header-label {
  	-webkit-transform: translateZ(0);
  	transform: translateZ(0);
  	-webkit-backface-visibility: hidden;
  	backface-visibility: hidden;
  	-moz-osx-font-smoothing: grayscale;
  	position: relative;
  	overflow: hidden;
  	cursor: pointer;
}
.header-label:before {
  	content: "";
  	position: absolute;
  	z-index: -1;
  	left: 50%;
  	right: 50%;
  	bottom: 0;
  	background: #209be1;
  	height: 2px;
  	-webkit-transition-property: left right;
  	transition-property: left right;
  	-webkit-transition-duration: 0.3s;
  	transition-duration: 0.3s;
  	-webkit-transition-timing-function: ease-out;
  	transition-timing-function: ease-out;
}
.header-label:hover:before {
  	left: 0;
  	right: 0;
}

.header-label:hover > i {
	display: inline-block !important;
}

.xs-styled-checkbox {
	border: solid 1px #209be1;
    display: inline-block;
    padding-left: 35px !important;
    color: #FFF;
    background: #209be1;
    border-radius: 4px;
}

.xs-styled-checkbox label {
    background: #FFF;
    padding: 7px 12px;
    margin-left: 16px;
    border-radius: 0 4px 4px 0;
}

.xs-styled-checkbox-slim label {
	padding: 6px 12px;
}

.xs-styled-checkbox label:before {
	background: transparent;
	outline: none !important;
	width: 60px;
    height: 34px;
    margin-left: -60px;
    margin-top: -9px;
    border: none;
}

.xs-styled-checkbox label:after {
    font-size: 18px;
    margin-left: -37px;
    margin-top: 4px;
    color: #fff;
}

.xs-styled-checkbox-slim label:after {
	margin-top: 2px;
}

.checkbox.xs-styled-checkbox-slim input[type="checkbox"]:disabled + label::before {
	background: transparent;
}

.xs-styled-checkbox label:focus:after {
	outline: none;
}

.xs-styled-checkbox.disabled {
	background: #ccc;
	border: solid 1px #ccc;
	pointer-events: none;
	cursor: not-allowed;
}

.xs-styled-checkbox input[type="checkbox"] + label::after {
	font-family: 'FontAwesome';
    content: "\f096";
}

.link-addon {
	cursor: pointer;
	color: #0066cc;
}

.link-addon:hover {
	text-decoration: underline;
}

.star-mark:before {
	content: '*';
}

.serial-number-info {
    color: #666;
    font-size: 10px;
    border: solid 1px #666;
    padding: 3px 7px;
    border-radius: 50px;
    vertical-align: text-bottom;
}

.btn-browse {
	border: dashed 1px #ccc;
    border-radius: 4px;
    background: rgba(204, 204, 204,0.3);
    height: 34px;
    text-align: center;
    color: #555;
    font-size: 14px;
    padding: 7px;
    cursor: pointer;
    white-space: nowrap;
  	overflow: hidden;
  	text-overflow: ellipsis;
}

.btn-browse.active {
	border: dashed 1px #209be1;
	background: rgba(32, 155, 225,0.1);
	color: #209be1;
}

.btn-browse .fa:before {
	content: "\f055";
}

.btn-browse.active .fa:before {
	content: "\f15b";
}

.btn-browse-remove {
    font-size: 20px;
    float: right;
    background: #fff;
    border-radius: 25px;
    color: #209be1;
    cursor: pointer;
    display: none;
    margin-top: -43px;
    margin-right: -8px;
}

.btn-browse.active ~ .btn-browse-remove {
	display: block;
}

.img-whats-new {
	border-radius: 6px; 
	border: solid 1px #ccc; 
	padding: 7px;
	margin: 0 auto;
}

.btn-spq {
	display: inline-block;
    padding: 5px;
    margin-bottom: 0;
    font-size: 18px;
    cursor: pointer;
    margin-top: 3px;
}

.btn-spq:hover {
	background: #fff;
    border-radius: 50px;
}

.popover-navigation button.disabled {
    display: none;
}

.navbar-nav .open .dropdown-menu.arrow_box_setting,
.navbar-nav .open .dropdown-menu.arrow_box_profile {
    margin-top: 2px;
    padding: 0;
    position: absolute;
    right: 0;
    left: auto;
    padding: 5px 0;
    margin: 2px 0 0;
    background-color: #fff;
    -webkit-background-clip: padding-box;
    border: 1px solid #ccc;
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
    box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
}

.navbar-nav .open .dropdown-menu.arrow_box_setting li a:hover,
.navbar-nav .open .dropdown-menu.arrow_box_profile li a:hover {
	color:  #209be1;
}


@media only screen and (min-width: 1280px) {
	.navbar-mobile,
	.mobile-sidenav {
		display: none !important;
	}
}

@media only screen and (max-width: 1279px) {
	.slide_container,
	.left-menu-container,
	.main-header-plus-container,
	.erp-logo-container,
	.enterprise-profile-container,
	.notification-container,
	.right-side-menu {
		display: none !important;
	}

	.right-content-container {
		padding:  0;
	}
}


.remove-left-border {
	border-left:  none !important;
}

.remove-right-border {
	border-right:  none !important;
}

.hsn-btn,
.hsn-suggestion-btn {
    font-size: 9px;
    width: 55px;
    background: transparent;
    color: #209be1;
    padding: 4px;
    margin: 5px 1px;
    border: 1px solid #209be1;
}

.hsn-btn:hover,
.hsn-suggestion-btn:hover {
    background: #209be1;
    color: #FFF;
    border: solid 1px #fff;
}

.super-markable-text {
    background: #ff7f50;
    color: #FFF;
    border-radius: 30px;
    padding: 3px 10px 3px 10px;
    display: inline-block;
    font-size: 10px;
    vertical-align: super;
}

.statement_type_txt.large {
    font-size: 14px;
    padding: 5px 20px;
    min-width: 100px;
    text-align: center;
}

.hsn-suggestion-internal {
    position: relative;
    margin-top: 20px;
    z-index: 10;
}

.custom-fieldset {
    border: solid 1px #ccc;
    padding: 15px;
    margin-bottom: 20px;
}

.custom-fieldset legend {
    width: auto;
    border-bottom: none;
    padding: 0 8px;
    margin-bottom: 0;
}

.ui-menu .ui-menu-item a.add_new_material_link {
    color: #209be1;
    font-weight: bold;
    font-size: 14px;
    background: rgba(32, 155, 225,0.2)
}

.ui-menu .ui-menu-item a.add_new_material_link:hover {
    background: #209be1;
    color: #fff;
}

.service-item-flag:after {
	content: 'S';
    color: #fff;
    padding: 2px 6px 2px 6px;
    background-color: #209be1;
    border-radius: 50px;
    font-size: 12px;
    position: static;
    margin-left: 8px;
}
.non_stock-flag:after {
	content: 'N';
    color: #fff;
    padding: 2px 6px 2px 6px;
    background-color: #209be1;
    border-radius: 50px;
    font-size: 12px;
    position: static;
    margin-left: 8px;
}
.issue-item-flag:after{
    content: 'I';
    color: #fff;
    padding: 2px 6px 2px 6px;
    background-color: #209be1;
    border-radius: 50px;
    font-size: 9px;
    position: static;
    margin-left: 8px;
}
.allocated-item-flag:after{
    content: 'A';
    color: #fff;
    padding: 2px 6px 2px 6px;
    background-color: #209be1;
    border-radius: 50px;
    font-size: 9px;
    position: static;
    margin-left: 8px;
}
.required-item-flag:after{
    content: 'R';
    color: #fff;
    padding: 2px 6px 2px 6px;
    background-color: #209be1;
    border-radius: 50px;
    font-size: 9px;
    position: static;
    margin-left: 8px;
}

.item-for-goods {
	border-bottom: 3px solid #999;
}

.table-inline-icon-container {
	position: absolute;
    right: 15px;
    margin-top: -10px;
    display: block ruby;
    text-align: right;
}

.table-inline-icon-container-left {
    position: absolute;
    left: 15px;
    margin-top: -10px;
    display: block ruby;
    text-align: left;
}

textarea.auto-expandable {
    padding: 5px !important;
    height: 26px;
    border: none;
    border-top: solid 1px #ccc;
    border-radius: initial;
    background: transparent;
    box-shadow:0 0;
}

textarea.auto-expandable:focus,
textarea.auto-expandable:empty {
   border: solid 1px #ccc;
   background: #FFF;
   border-radius: 4px;
   box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%);
}

.add_item_remarks {
    text-align: right;
    color: #004195;
    cursor: pointer;
    float: right;
}

.item_remarks_section {
    float: left;
    width: 100%;
    margin-top: 5px;
}

.item_faulty_check {
    margin: 0;
    margin-left: -15px;
    display: inline;
    margin-top: 5px;
    float: left;
}

.add_item_remarks:hover {
    text-decoration: underline;
}

.base_currency_txt {
    float: left;
    padding-left: 7px;
}

.currency_convertor_section {
    float:left;
    padding-right:5px;
}

.conversion_rate_container {
    margin-top: 7px;
    margin-bottom: 7px;
}

#div_con_rate .txt_conversion_rate {
    height: 20px;
    font-size: 10px;
    width: 75px;
    float: left;
    padding: 5px;
    margin-top: -2px;
}

.disabled-field {
    opacity: 0.4;
}

.enabled-field {
    opacity: 1;
}

.anchorTag-with-child {
    padding-left: 26px;
    display: block;
    margin-top: -12px;
}

.anchorTag-without-child {
    padding-left: 8px;
}

.inline-unit-display{
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 46px;
    border: none;

}

.enable_edit_option {
    color: #004195;
    margin-top: 45px;
    position: absolute;
    right: 7px;
    z-index: 1000;
    cursor: pointer;
    float: right;
    background: transparent;
}


.secondary-enterprise .for-primary-ent {
    display: none !important;
}