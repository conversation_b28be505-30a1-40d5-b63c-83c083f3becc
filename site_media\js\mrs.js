	$(window).load(function(){
		$("#loading").hide();
		loadMaterial("onload");
		$("#cmdshow").hide();
		var mrs_id = $('#mrs_id').val();
		if(mrs_id == 'None'){
			$(".page-title").text('New Material Request');
			$(".header_current_page").addClass("hide");
		}
		else{
			var requisitionBy = $('#requisitionBy').val();
			var projectTag = $('#projectTag').val();
			$('#id_invoice-issue_to option[value ="' + requisitionBy + '"]').prop('selected', true);
			$('#id_invoice-issue_to').trigger('chosen:updated');
			$('#id_project_select option[data-id="' + projectTag + '"]').prop('selected', true);
			$('#id_project_select').trigger('chosen:updated');
			var remarksText = $('#remarksLength').text();
			if(remarksText != '')
			{
			    var parsedData = JSON.parse(remarksText);
                if(parsedData.length > 0)
                {
                    $('.remarks_counter').text(parsedData.length);
                    $('.remarks_count').removeClass('disabled');
//                    displayRemarksHistory('remarks_list', parsedData, parsedData.length)
                }
            }
		}
	});

	function loadMaterial(loadType = "") {
        var dataToSend = {
            'type': "indents",
            'module': 'indents',
            'particulars':'indent_material'
        }
        var local_material_list  = JSON.parse(localStorage.getItem('goods_service_master_data'));
        if(loadType == "onchange" && local_material_list) {
            var material_choices = local_material_list[0].material;
        }
        else {
            var material_choices = generateMaterialAsAutoComplete(dataToSend);
        }
        var frequently_material_choices = generateFrequentlyMaterialAsAutoComplete(dataToSend);

        $("#materialrequired").materialAutocomplete({
            source: frequently_material_choices.concat(material_choices),
            minLength:2,
            response: function( event, ui ) {
                ui.content.unshift({value:"add_new_item", label:"+ ADD NEW MATERIAL ", category: ""});
            },
            open: function(event, ui) {
                $("ul.ui-autocomplete.ui-menu a:contains('+ ADD NEW MATERIAL')").addClass("add_new_material_link");
            },
            select: function (event, ui) {
                event.preventDefault();
                if(ui.item.value != "add_new_item") {
                    var itemName = ui.item.label.replace("<span class='service-item-flag'></span>", '');
                    $("#material_id_hidden").val(ui.item.id);
                    $("#materialrequired").val(itemName).attr("readonly", true)
                    $("#material_id").val(ui.item.id);
                    $("#cat_code").val(ui.item.id);


                    $('#id_ind_material-__prefix__-alternate_units').html("");
                    if($('#is_multiple_units').val()=="True" ){
                        if (ui.item.alt_uom > 0){
                            loadAlternateUnits(ui.item.id, ui.item.unit, '#id_ind_material-__prefix__-alternate_units');
                            $(".alternate_unit_select_box").removeClass("hide");
                        }
                        else {
                            $(".alternate_unit_select_box").addClass("hide");
                        }
                    }
                    $("#id_ind_material-__prefix__-drawing_no").val(ui.item.id);
                    $("#id_ind_material-__prefix__-is_service").val(ui.item.is_service);
                    $("#id_ind_material-__prefix__-item_id").val(ui.item.id);
                    $("#id_ind_material-__prefix__-description").val(ui.item.label);
                    $("#id_ind_material-__prefix__-enterprise_id").val($("#id_indent-enterprise_id").val());
                    $("#id_ind_material-__prefix__-units").val(ui.item.unit) ;
                    $("#id_ind_material-__prefix__-make_label").val(ui.item.mid);
                    $("#ind_unit_display").text(ui.item.unit);
                    var selected_unit_name = ui.item.unit
                    $('#unit_id option').filter(function() {
                      return $(this).text().trim() === selected_unit_name;
                    }).prop('selected', true);
                    $('#unit_id').trigger('change');
                    if (parseInt(ui.item.bom) > 0 ){
                        $("#cmdshow").show()
                    }
                    else{
                        $("#cmdshow").hide()
                    }
                    $(".material-removal-icon").removeClass('hide')
                    setTimeout(function(){
                        $("#id_ind_material-__prefix__-quantity").focus();
                    },250);
                }
                else {
                    openMaterialAddModal();
                }
            }
        }).on('focus', function() { $(this).keydown(); });
    }
	function remarks_show()
	{
		var remarksText = $('#remarksLength').text();
		var parsedData = JSON.parse(remarksText);
		remarkList = JSON.parse(remarksText);
        if (remarkList.length > 0) {
        	$('.countContainer').html(remarkList.length);
        	$('countContainer').removeClass("disabled");
            var history = '<div class="chat-container">';
            $.each(remarkList, function(i, remark) {
            	var sDate = moment(remark.date).format('MMM D, YYYY');
            	if(sDate == 'Invalid date') sDate =' ';
                history += '<div class="chat-list">';
                history += '<span class="chat-list-name">' + remark.by + '</span>';
                history += '<span class="chat-list-date">' + sDate + '</span>';
                history += '<span class="chat-list-description">' + remark.remarks + '</span>';
                history += '</div>';
            });
            history += '</div><BR/>';
            $('#remarks_list').html(history);
             $("#show_remarks_history_modal").modal('show');
        }
	}

    function getStockQty(item_id, alternate_unit_id) {
        var item_json = {"item_id": item_id, "make_id": 1,
         "is_faulty": 0, "invoice_id": "", "issued_on": null, "alternate_unit_id": 0 };
         var stock_qty = 0;
        $.ajax({
            url : "/erp/stores/json/issue/closingstock/",
            type : "POST",
            dataType: "json",
            data :  item_json,
            async: false,
            success : function(response) {
                $.each(response['closing_stock'], function(i, item){
                    stock_qty = item['closing_qty'];
                });
            },
            error : function(xhr,errmsg,err) {
                console.warning(xhr.status + ": " + xhr.responseText);
            }
        });
        return stock_qty
    }

	function showCatalogues1() {
        var catqty = $("#id_ind_material-__prefix__-quantity").val();
        $.ajax({
            url: "/erp/stores/json/catalogue_materials/",
            type: "post",
            datatype: "json",
            data: {'cat_code': $("#cat_code").val(), "alternate_unit_id":$('#unit_id').val()},
            success: function (response) {
            if (response.response_code != 400) {
                $("#cattable").find("tr:gt(0)").remove();
                $("#catButton").find('.modal-footer').remove();
                $.each(response, function (i, item) {
                    var childArrow = "";
                    var makes = "-NA-";
                    var drawing_no = "";
                    var description = "";
                    var is_service_value = item.is_service==true?1:0;
                    var is_service = '<input hidden="hidden" name="catmaterial_is_service" value='+is_service_value+' >';
                    var itemTypeFlag = "";
                    if(item.is_service == true){
                        itemTypeFlag = `<span class="service-item-flag"></span>`;
                    }
                    if(!item.material_type && item.is_service != true) {
                        itemTypeFlag = `<span class="non_stock-flag"></span>`;
                    }
                    var item_name = item.name;
                    if (item.make_name !="" && item.make_name !=null) {
                        item_name += " [" + item.make_name +"]";
                    }
                    if(item.hasChildren) {
                        childArrow = "<i class='fa fa-plus fa-for-arrow' role='button' onclick='return appendMaterial(\""+ item.item_id +"\", \""+ sessionStorage.clickcount +"\", \""+item.item_id+ "_" + sessionStorage.clickcount +"\")'><a style='padding-left: 26px; display: block; margin-top: -12px;' >"+item_name+" "+itemTypeFlag+"</a></i>";
                    } else { childArrow = item_name+" "+itemTypeFlag }

                    if (item.makes.length > 0) {
                        makes = "<select class='form-control' name='make' id='txtmake" + i + "'>";
                        for (j = 0; j <= item.makes.length - 1 ; j++) {
                            makes = `${makes}<option value="${item.makes[j].make_id}" data-quantity="${item.makes[j].stock}">${item.makes[j].make_name}</option>`;
                        }
                        makes += "</select>";
                    }
                    description = item.name;
                    if (item.drawing_no !="" && item.drawing_no!=null) {
                        drawing_no = item.drawing_no;
                        description += " - " + item.drawing_no;
                    }
                    var is_service_value = item.is_service==true?1:0;
                    var row = "<tr data-toggle='close' data-padding='0' data-parent='"+item.item_id+ "_" + sessionStorage.clickcount + "' id=\""+ item.item_id + "_" + sessionStorage.clickcount +"\" bgcolor='#ececec' border='1' align='left' style='font-size:12px; font-weight:normal;'><td hidden=hidden>" +
                        description +"</td><td class='text-left bom-sno'>" +
                        (i+1) + "</td><td>"+childArrow+"</td><td>" +
                        drawing_no + "</td><td hidden='hidden'>" +
                        makes + "</td><td>" +
                        "<input type='text' name ='catmaterial' value='" + (item.quantity * catqty).toFixed(2) + "' class='form-control setUnitValue text-right' id='txtcatqty' maxlength='16' onfocus='setNumberRangeOnFocus(this,12,3)' /></td><td>" +
                        item.unit_name + "</td><td hidden='hidden'>" +
                        item.unit_id + "</td><td class='text-center'>" +
                        "<a href='#' onclick='deleteCatRow(this)'><i class='fa fa-trash-o'></a>" + "</td><td hidden=hidden>" +
                        drawing_no + "</td><td hidden=hidden><input type='text' name ='catmaterial_item_id' value='" +
                        item.item_id + "' ></td><td hidden=hidden><input type='text' name ='catmaterial_is_service' value='" + is_service_value + "' ></td><td hidden=hidden><input type='text' name ='make_name' value='" + item.make_name + "' ></td><td hidden=hidden><input type='text' name ='stock_qty' value='" + item.stock_qty + "' ></td></tr>";
                    $('#cattable').append(row).addClass('tbl');
                });
                    var row = "<div class='modal-footer'><span class='pull-left nonStock-text'>  <span class='nonStock-text non_stock-flag'></span> - NON-STOCKABLE</span><span class='pull-left nonStock-text'>  <span class='nonStock-text service-item-flag'></span> - SERVICE</span><input type='button' onclick='addMrs()' class='btn btn-save' id='catAdd' value='Add' /><a onclick='closeCatalogues()' class='btn btn-cancel' id='catCancel'>Close</a></div>"
                    $('#catButton').append(row).addClass('tbl');
                    $("#catalogueModal").modal('show');
                    $("#loadingmessage_changelog_listing_ie").hide();
            }
            else {
                // TODO why should I login again here
                swal(response.response_message, "Kindly <a href='/erp/login/'>Login</a> again.", "warning");
                document.location.href="/erp/login/";
                return;
            }
            $(".setUnitValue").blur(function(){
                var setID = $(this).closest('tr').attr('id');
                var setValue = $(this).val();
                $('#cattable').find("tr[data-child=\""+setID+"\"]").each(function(){
                    var setUnitValue = $(this).find('.setUnitValue').data('unitvalue');
                    $(this).find('.setUnitValue').val(Number(setValue * setUnitValue));
                });
            });
            }
        });
    }
    function addMrs() {
        var materialtable = document.getElementById("MrsMaterial");
        var match =false;
        var rowIndexMrsTable = [];
        var rowIndexCattable = [];

        $('#cattable tbody tr').each(function(index, row) {
            $('#MrsMaterial tbody.item-for-goods tr').each(function(index1, row1) {
                var existingValue = $(row1).find('td:eq(2)').text().trim();
                var name = "catmaterial_item_id";
                var catmaterial_item_id =  $(row).find('td input[name="' + name + '"]').val();
                if (existingValue === catmaterial_item_id) {
                    rowIndexMrsTable =index1;
                    rowIndexCattable =index;
                    match = true;
                    return false;
                }
                else
                {
                match = false;
                }
            });
            if(match == false &&  $(row).data('toggle') === "close"){
                var serialNumber = materialtable.rows.length;
                const makeText = $(row).find('td input[name="make_name"]').val();
                var materialName = $(row).find('td:eq(0)').text();
                if(makeText != ''){
                    var materialrequired = materialName +" " + '[' + makeText + ']'
                }
                else{
                    materialrequired = materialName;
                }
                if( $(row).find('td input[name="catmaterial_is_service"]').val() == 1 ){
                    serviceFlsg= "<span class='service-item-flag'></span>";
                }
                else{
                    serviceFlsg = "";
                }
                var newQtyField = $(row).find('td input#txtcatqty').val();
                var unitText = $(row).find('td:eq(6)').text();
                var unitId = $(row).find('td:eq(7)').text();
                var name = "catmaterial_item_id";
                var catmaterial_item_id =  $(row).find('td input[name="' + name + '"]').val();
                var load_received_qty = $('#load_received_qty').is(':checked');
                var load_stock_qty = $('#load_stock_qty').is(':checked');
                var stock_qty =  $(row).find('td input[name="stock_qty"]').val();
                var row = "<tr><td class='text-center'>" + serialNumber + ".</td><td>" + materialrequired + serviceFlsg + "</td><td hidden='hidden'>" + catmaterial_item_id + "</td><td class='td_textbox_right'><input type='text' id='mrs_qty' class='form-control' onblur='validationWithIssueQty(this)' onfocus='setNumberRangeOnFocus(this,12,3)' value='" + newQtyField + "'></td><td hidden='hidden'>" + newQtyField + "</td><td class='table_received_qty text-right " + (load_received_qty ? '' : 'hide') + "' id='issue_qtyForVali'>0.000</td><td class='table_stock_in_hand text-right " + (load_stock_qty ? '' : 'hide') + "' >"+ stock_qty +"</td><td class='text-center'>"+ unitText +"</td><td hidden='hidden'>"+ unitId +"</td><td class='text-center'><a href='#'><i class='fa fa-trash-o delete-row-btn'></i></a></td></tr>";
                 $("#MrsMaterial tbody.item-for-goods").append(row);
//                $("#MrsMaterial tbody.item-for-goods").append("<tr><td class='text-center'>" + serialNumber + ".</td><td>" + materialrequired + serviceFlsg + "</td><td hidden='hidden'>" + catmaterial_item_id + "</td><td class='td_textbox_right'><input type='text' id='mrs_qty' class='form-control' onblur='validationWithIssueQty()' onfocus='setNumberRangeOnFocus(this,12,3)' value='" + newQtyField + "'></td><td class='text-right hide' id='issue_qtyForVali'>0.00</td><td class='text-center'>"+ unitText +"</td><td hidden='hidden'>"+ unitId +"</td><td class='text-center'><a href='#'><i class='fa fa-trash-o delete-row-btn'></i></a></td></tr>");
            }
            else if(match == true &&  $(row).data('toggle') === "close"){

                var MrsMaterialValue = Number($('#MrsMaterial tbody tr:eq(' + rowIndexMrsTable + ')').find('td input#mrs_qty').val());
                var cattableValue = Number($('#cattable tbody tr:eq(' + index + ')').find('td input#txtcatqty').val());
                var newValue = MrsMaterialValue + cattableValue;
                $('#MrsMaterial tbody tr:eq(' + rowIndexMrsTable + ')').find('td input').val(newValue.toFixed(2));

            }
            $("#catalogueModal").modal('hide');
            $('#materialrequired, #id_ind_material-__prefix__-quantity').val('');
            $("#ind_unit_display").html('&nbsp;');
            $('#material_id_hidden').val('');
            $('#materialr_id').val('');
            $("#cmdshow").hide();
            $(".material-removal-icon").click();

         });
    }

    function closeCatalogues() {
        $("#catalogueModal").modal('hide');
    }
    function deleteCatRow(currentRow) {
        try {
            if (window.confirm('Do you want delete this row?')) {
                var table = document.getElementById("cattable");
                var rowCount = table.rows.length;
                for (var i = 0; i < rowCount; i++) {
                    var row = table.rows[i];
                    if (row == currentRow.parentNode.parentNode) {
                        if (rowCount <= 1) {
                            alert("Cannot delete all the rows.");
                            break;
                        }
                        table.deleteRow(i);
                        rowCount--;
                        i--;
                    }
                }
            }
        }
        catch (e) {
            alert(e);
        }
    }

    function appendMaterial(cat_code, dataChild, dataP) {
        var currentCatChild = $("#"+cat_code + "_" + dataChild).attr('id');
        var current_bom_sno = $("#"+cat_code + "_" + dataChild).find(".bom-sno").text().trim();
        var constructedRow = '';
        if($("#"+currentCatChild).attr('data-toggle') == 'open') {
            var curPadding = $("#"+currentCatChild).attr('data-padding');
            $("#"+currentCatChild).nextAll('tr').each(function(){
                if(Number($(this).attr('data-padding')) > Number(curPadding)) {
                    $(this).remove();
                }
                else {
                    return false;
                }
            });
            $("#cattable #"+cat_code + "_" + dataChild).find('.fa.fa-for-arrow').removeClass('fa-minus').addClass('fa-plus');
            $("#cattable #"+cat_code + "_" + dataChild).attr('data-toggle','close');
        }
        else {
            $("#cattable #"+cat_code + "_" + dataChild).find('.fa.fa-for-arrow').removeClass('fa-plus').addClass('fa-minus');
            $("#cattable #"+cat_code + "_" + dataChild).attr('data-toggle','open');
            $.ajax({
                url: "/erp/stores/json/catalogue_materials/",
                type: "post",
                dataType: "json",
                data: {'cat_code': cat_code},
                success: function (response) {
                    var catqty = $("#id_ind_material-__prefix__-quantity").val();
                    $.each(response, function (i, item) {
                    if(typeof(Storage) !== "undefined") {
                        if (sessionStorage.clickcount) {
                            sessionStorage.clickcount = Number(sessionStorage.clickcount)+1;
                        } else {
                            sessionStorage.clickcount = 1;
                        }
                    }
                    var childArrow ="";
                    var makes = "-NA-";
                    var dataParent = dataP;
                    var dataPadding = Number($("#"+cat_code + "_" + dataChild).data('padding') + 30);
                    var drawing_no = "";
                    var isStockableIcon = "";
                    var isServiceIcon = "";
                    var item_description = item.name;
                    var is_service = '<input hidden="hidden" name="catmaterial_is_service" value='+item.is_service+' >';
                    if (item.drawing_no !="" && item.drawing_no!=null) {
                        drawing_no = item.drawing_no;
                    }
                    if (item.make_name != ""){
                        item_description += " [" + item.make_name + "]";
                    }

                    if(item.is_service == true){
                        isServiceIcon = `<span class="service-item-flag"></span>`;
                    }
                    if(!item.material_type && !item.is_service){
                        isStockableIcon = `<span class="non_stock-flag"></span>`;
                    }
                    if(item.hasChildren) {
                        childArrow = "<i class='fa fa-plus fa-for-arrow' role='button' onclick='return appendMaterial(\""+ item.item_id +"\", \""+ sessionStorage.clickcount +"\", \""+ dataParent +"\")'><a style='padding-left: 26px; display: block; margin-top: -12px;'>"+item_description+" "+isServiceIcon+" "+isStockableIcon+"</a></i>";
                    } else {
                        childArrow = item_description +" "+isServiceIcon+" "+isStockableIcon;
                    }
                    if (item.makes.length > 0){
                        makes = "<select class='form-control' name='make' id='txtmake" + i + "'>";
                        for (j = 0; j <= item.makes.length - 1 ; j++) {
                            makes = `${makes}<option value="${item.makes[j].make_id}" data-quantity="${item.makes[j].stock}">${item.makes[j].make_name}</option>`;
                        }
                        makes += "</select>";
                    }
                    if(item.drawing_no != null){
                        var item_name = item.name + " - " + item.drawing_no
                    }else{
                        var item_name = item.name
                    }
                    var is_service_value = item.is_service==true?1:0;
                    var row = "<tr data-toggle='close' data-padding='"+dataPadding+"' data-parent='"+dataParent+"' id=\""+ item.item_id + "_" + sessionStorage.clickcount +"\" data-child=\""+ cat_code + "_" +dataChild +"\" bgcolor='#ececec' border='1' align='left' style='font-size:12px; font-weight:normal;'><td hidden=hidden>" +
                            item_name + "</td><td class='text-left bom-sno'>" +
                           current_bom_sno +"."+(i+1)+ "</td><td style='padding-left:"+Number(dataPadding-20)+"px'><span style='padding-left:30px; display: block;' class='tree-view'>"+childArrow+"</span></td><td>" +
                           drawing_no + "</td><td hidden='hidden'>" +
                           makes + "</td><td>" +
                           "<input type='text' id='txtcatqty' name ='catmaterial' class='form-control text-right setUnitValue' maxlength='16' onfocus='setNumberRangeOnFocus(this,12,3)' value='" + item.quantity * catqty + "' data-unitvalue='"+item.quantity+"' /></td><td>" +
                           item.unit_name + "</td><td hidden='hidden'>" +
                            item.unit_id + "</td><td class='text-center'>" +
                           "<a href='#' onclick='deleteCatRow(this)'><i class='fa fa-trash-o'></i></a>" + "</td><td hidden=hidden>" +
                           item.drawing_no + "</td><td hidden=hidden><input type='text' name ='catmaterial_item_id' value='" + item.item_id + "' ></td><td hidden=hidden><input type='text' name ='catmaterial_is_service' value='" + is_service_value + "' ></td><td hidden=hidden><input type='text' name ='make_name' value='" + item.make_name + "' ></td><td hidden=hidden><input type='text' name ='stock_qty' value='" + item.stock_qty + "' ></td></tr>";

                     constructedRow = constructedRow+row
                    });
                    $('#cattable #'+cat_code + "_" + dataChild).after(constructedRow);
                    $("#"+currentCatChild).find('.setUnitValue').focus();
                    setTimeout(function(){
                        $("#"+currentCatChild).find('.setUnitValue').blur();
                    },500);

                    $(".setUnitValue").blur(function(){
                        var setID = $(this).closest('tr').attr('id');
                        var setValue = $(this).val();
                        $('#cattable').find("tr[data-child=\""+setID+"\"]").each(function(){
                            var setUnitValue = $(this).find('.setUnitValue').data('unitvalue');
                            $(this).find('.setUnitValue').val(Number(setValue * setUnitValue));
                        });
                    });
                }
            });
        }
    }

$(document).ready(function(){

	$("#add_new_material_req").click(function(){
		$(".error-border").removeClass('error-border');
	    $(".custom-error-message").remove();

	    var ControlCollections = [
	        {
	            controltype: 'textbox',
	            controlid: 'materialrequired',
	            isrequired: true,
	            errormsg: 'Material Name is required.'
	        },

			{
				controltype: 'textbox',
				controlid: 'id_ind_material-__prefix__-quantity',
				isrequired: true,
				errormsg: 'Quantity is required.',
                mindigit: 0.01,
                mindigiterrormsg: 'Quantity cannot be 0.'
			}
	    ];
	    var result = JSCustomValidator.JSvalidate(ControlCollections);
		var newQtyField = document.getElementById('id_ind_material-__prefix__-quantity');
        if ($('#material_id').val()!="" && newQtyField.value != "" && newQtyField.value >0) {
            var materialtable = document.getElementById("MrsMaterial");
            var materialrowCount = materialtable.rows.length;
            var materialrequired = document.getElementById("materialrequired");
            var serialNumber = materialtable.rows.length;
            var unitText = $("#ind_unit_display").text();
            var unit_id = $('#unit_id').val();
            var material_id = $("#material_id_hidden").val();
            var load_received_qty = $('#load_received_qty').is(':checked');
            var load_stock_qty = $('#load_stock_qty').is(':checked');
            var match = false;
            var stock_qty = 0;
            var rowIndex = 0;
             if($('#id_ind_material-__prefix__-is_service').val() == 1){
                    var serviceFlsg= "<span class='service-item-flag'></span>";
             }
             else{
                var serviceFlsg = "";
             }
			$('#MrsMaterial tbody tr').each(function(index, row) {
				var existingValue = $(row).find('td:eq(2)').text().trim();
				if (existingValue === material_id) {
					rowIndex = index;
					match = true;
					return false;
				}
				return rowIndex;
    		});
    		if(match == false)
				{
				    $('#loading').show();
				    var item_json = {"item_id": material_id, "make_id": 1,
                                     "is_faulty": 0, "invoice_id": "", "issued_on": null, "alternate_unit_id": 0 };
                    $.ajax({
                        url : "/erp/stores/json/issue/closingstock/",
                        type : "POST",
                        dataType: "json",
                        data :  item_json,
                        success : function(response) {
                            $.each(response['closing_stock'], function(i, item){
                                stock_qty = item['closing_qty'];
                            });

                            $("#MrsMaterial tbody.item-for-goods").append("<tr><td class='text-center'>" + serialNumber + ".</td><td>" + materialrequired.value + serviceFlsg + "</td><td hidden='hidden' id='table_material_id'>" + material_id + "</td><td class='td_textbox_right'><input type='text' id='mrs_qty' class='form-control' onblur='validationWithIssueQty(this)' onfocus='setNumberRangeOnFocus(this,12,3)' value='" + newQtyField.value + "'></td><td hidden='hidden'>" + newQtyField + "</td><td class='table_received_qty text-right " + (load_received_qty ? '' : 'hide') + "' id='issue_qtyForVali'>0.000</td><td class='table_stock_in_hand text-right " + (load_stock_qty ? '' : 'hide') + "' >"+ stock_qty +"</td><td class='text-center'>"+ unitText +"</td><td hidden='hidden'>" + unit_id +"</td><td class='text-center'><a href='#'><i class='fa fa-trash-o delete-row-btn'></i></a></td></tr>");
                            $("#materialrequired").val("");
                            $("#materialrequired").removeAttr("readonly");
                            $(".material-removal-icon").addClass("hide");
                            $('#id_ind_material-__prefix__-quantity').val("");
                            $('#loading').hide();
                        },
                        error : function(xhr,errmsg,err) {
                            console.warning(xhr.status + ": " + xhr.responseText);
                            $('#loading').hide();
                        }
                    });

			}
  			else {
                var existingQty  = $('#id_ind_material-__prefix__-quantity');
                swal({
                    title: "Material already Exists!",
                    text: "Do you still want to add its Quantity?",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#209be1",
                    confirmButtonText: "Yes, do it!",
                    closeOnConfirm: true,
                    closeOnCancel: true
                    },
                    function(isConfirm){
                        newValue = Number($('#MrsMaterial tbody tr:eq(' + rowIndex + ')').find('td input').val()) + Number(existingQty.val());
                        $('#MrsMaterial tbody tr:eq(' + rowIndex + ')').find('td input').val(newValue.toFixed(2));
                        $("#materialrequired").val("");
                        $("#materialrequired").removeAttr("readonly");
                        $(".material-removal-icon").addClass("hide");
                        existingQty.val("");
                        }
                );
            }
      	}
	});

	$('#edit_catalogue_form').submit(function() {
       $.ajax({
           data: $(this).serialize(),
           type: $(this).attr('method'),
           url: $(this).attr('action'),
           success: function(response) {
               var item_name = ""
               $('#loading').hide();
               $("#material_id_hidden").val(response['item_id']);
               $("#materialrequired").val(response['name']).attr("readonly","readonly");
               $("#materialrequired").closest("div").find(".material-removal-icon").removeClass("hide");
               $("#material_id").val(response['item_id']);
               $("#id_ind_material-__prefix__-drawing_no").val(response['drawing_no']);
               $("#id_ind_material-__prefix__-is_service").val(response['is_service']);
               item_name = response['name']
               if(response['drawing_no'] !="" && response['drawing_no'] != null) {
                   item_name +=  " - "  + response['drawing_no']
               }
               if(response['is_service'] == 1){
                   item_name += `<span class="service-item-flag"></span>`;
               }
               $("#id_ind_material-__prefix__-description").val(item_name);
               $("#id_ind_material-__prefix__-enterprise_id").val(response['enterprise_id']);
               $("#id_ind_material-__prefix__-item_id").val(response['item_id']);
               $('#id_ind_material-__prefix__-alternate_units').html("");
               if($('#is_multiple_units').val()=="True" ){
                   if (response['alternate_unit_count'] > 0){
                       loadAlternateUnits(response['item_id'], response['unit_name'], '#id_ind_material-__prefix__-alternate_units');
                       $(".alternate_unit_select_box").removeClass("hide");
                   }
                   else {
                       $(".alternate_unit_select_box").addClass("hide");
                   }
               }
               $("#id_ind_material-__prefix__-units").val(response['unit_name']) ;
               $("#ind_unit_display").text(response['unit_name']);
               //loadMakeList();
               if(!$('#add_material_modal').hasClass('in')) {
                   $("#add_new_ind_material").trigger("click");
               }
               $('#add_material_modal').modal('hide')
               setTimeout(function(){
                   $("#id_ind_material-__prefix__-quantity").focus();
               },250);

           }
       });
       return false;
   });


 	$('#cmdshow').click(function () {
        if ($('#materialrequired').val()=="") {
            swal("","Please Select Material","warning");
            return;
        }
        var newQtyField = document.getElementById('id_ind_material-__prefix__-quantity');
        if (newQtyField.value == "" || newQtyField.value =="0.00") {
            swal("","Please Enter Qty","warning");
            return;
        }
        else {

            $("#loadingmessage_changelog_listing_ie").show();
            showCatalogues1();
        }
    });

	$("#MrsMaterial").on("click", ".delete-row-btn", function () {
        var row = $(this).closest("tr");
        swal({
            title: "Are you sure?",
            text: "Do you want to delete this Material",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#209be1",
            confirmButtonText: "Yes, delete it!",
            closeOnConfirm: true
        },
        function(){
            row.remove();
            $("#MrsMaterial tbody.item-for-goods tr:visible, #MrsMaterial tbody.item-for-service tr:visible").each(function(i) {
               $(this).find('td:eq(0)').text(i+1+".")
            })
        });
      });

	$("#save_mrs_button").click(function(){
	    if(!$('#projectheader').val()){
            swal('','Please Select Project!','error');
        }
		$(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();
        var mrs_id = $('#mrs_id').val();

        var ControlCollections = [
           {
                controltype: 'textbox',
                controlid: 'id_material_request_req_for',
                isrequired: true,
                errormsg: 'Requisition for is required.'
            },
            {
				 	controltype: 'dropdown',
					controlid: 'id_invoice-issue_to',
					isrequired: true,
					errormsg: 'Requisition By is required.'
				},
                       {
                controltype: 'dropdown',
                controlid: 'projectheader',
                isrequired: true,
                errormsg: 'Project is required.'
            }
        ];
        var result = JSCustomValidator.JSvalidate(ControlCollections);
        if(result){
            var item_count = $("#MrsMaterial  tbody.item-for-goods tr").length;
			if(item_count == 0) {
                swal('','Please add atleast one material for request','error');
            }
            else {
				var HeaderData = {};
				var mrsNoValue = $("#mrs_no").text().trim();
				var requisitionBy = $("#id_invoice-issue_to option:selected");
				var requisitionByVal = requisitionBy.val();
				var requisitionFor = $("#id_material_request_req_for").val();
				var projectSelect = $("#projectheader option:selected");
				var projectSelectId = projectSelect.val();

				jsonData = {
					"mrs_no": mrsNoValue,
					"material_req_for": requisitionFor,
					"issue_id": requisitionByVal,
					"project_id": projectSelectId
				};
				var jsonHeaderData = JSON.stringify(jsonData);
				var remarks = $("#id_mrs-instructions").val().trim();
				jsonRemarks = {
					"remarks": remarks,
				};
				var jsonRemarksData = JSON.stringify(jsonRemarks);
				var tableData = [];
				$("#MrsMaterial tbody.item-for-goods tr").each(function() {
					var row = $(this);
					var material_id_selected = row.find("td:eq(2)").text().trim();
					var serialNumber = row.find("td:eq(0)").text().trim();
					var materialRequired = row.find("td:eq(1)").text().trim();
					var newQtyField = row.find("td input#mrs_qty").val().trim();
					var unitText = row.find("td:eq(7)").text().trim();
					var unitId = row.find("td:eq(8)").text().trim();
					var rowData = {
						   "item_id" : material_id_selected,
						   "quantity": newQtyField,
						   "alternate_unit_id": unitId
					};
						tableData.push(rowData);
				});
				$.ajax({
					url: "/erp/stores/material_requsition/save/",
					type: "post",
					datatype: "json",
					data: {
					   mrs_id : mrs_id,
					   headers: jsonHeaderData,
					   materials: JSON.stringify(tableData),
					   remarks : remarks
					},
					success: function (response) {
					    if (response.response_code == 200){
                            var text_message = (mrs_id == "None") ? "Material Requisition has been created successfully." :
                            "Material Requisition has been modified successfully.";
                            swal({
                                title: "",
                                text: text_message,
                                type: "success"
                              },
                              function(){
                                if (mrs_id == "None"){
                                    window.location.href = "/erp/stores/material_requsition_list/"
                                }
                                else{
                                    window.location.reload();
                                }
                             }
                            );
					    }
					    else{
                            swal({
                                title: "",
                                text: "Failed to saved Material Requisition",
                                type: "error"
                            });
					    }
					}
				});
				var jsonTableData = JSON.stringify(tableData);
            	$("html, body").animate({ scrollTop: 0 }, "fast");
        	}
       	}
	});

	$(".material-removal-icon").click(function(){
		$("#cmdshow").hide();
		$('#materialrequired').removeAttr("readonly").focus();
		$(this).addClass("hide");
		$('#materialrequired').val('');
		$('#description_display').html('');
        $(".alternate_unit_select_box, .all_units_select_box").addClass("hide");
	});

});


    function short_close(){

        $("#MrsMaterial tbody.item-for-goods tr").each(function() {
		    var row = $(this);
		    var issue_qty = row.find("td#issue_qtyForVali").text();
			row.find("td input#mrs_qty").val(issue_qty);
		});
    }

    function showStockQty(){
        if($("#load_stock_qty").is(":checked")) {
            $(".table_stock_in_hand").removeClass('hide')
        }
        else{
            $(".table_stock_in_hand").addClass('hide')
        }
    }

     function showIssueQty(){
        if($("#load_received_qty").is(":checked")) {
            $(".table_received_qty").removeClass('hide')
        }
        else{
            $(".table_received_qty").addClass('hide')
        }
     }


    function validationWithIssueQty(element) {
    var row = $(element).closest('tr');
    var issue_qty = row.find("td#issue_qtyForVali").text();
    var mrs_qty = row.find("td input#mrs_qty").val();
    if (Number(mrs_qty) < Number(issue_qty)) {
        swal({title: "", text: "MRS Quantity should not be lesser than issue qty", type: "error"});
        row.find("td input#mrs_qty").addClass("error-border");
    } else {
        row.find("td input#mrs_qty").removeClass("error-border");
    }
}


    $('#approve_mrs').click(function () {
		approveMrs();
    });
    $('#reject_mrs').click(function () {
		rejectMrs();
    });


function approveMrs() {
    $("#loading").show();
    var mrs_id = $("#modal_mrs_id").val();
    setTimeout(function(){
        $.ajax({
            url: "/erp/stores/material_requisition/approve/",
            type: "POST",
            dataType: "json",
            data: {mrs_id: mrs_id, remarks:$("#remarks").val()},
            success: function (json) {
                $("#loading").hide();
                var swal_type = "error";
                var swal_title = "<span style='color: #dd4b39;;'>Unable to Approve the MRS at the moment!</span>";
                if(json.custom_message.indexOf("success") > 0) {
                    swal_type = "success";
                    swal_title = "<span style='color: #44ad6b;'>Approved Successfully!</span>";
                }
                swal({
                    title: swal_title,
                    text: json.custom_message,
                    type: swal_type,
                    html: true
                },
                function(){
                    var receipt_code = json.code
                    generate_pdf_ajax(mrs_id, 1);
                    updateTableStatus(1, mrs_id , receipt_code);
                }
                );
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
                $("#loading").hide();
            }
        });
    }, 50);
}

function rejectMrs(){
    var mrs_id = $("#modal_mrs_id").val();
    if($('#reject_mrs').text() == 'Reject'  && $('#remarks').val() == "") {
            swal({
                title: "<span style='color: #dd4b39;'>Remarks Required.</span>",
                text: "Please enter your purpose of rejecting this MRS as remarks.",
                type: "warning",
                html: true
            },
            function(){
                $('#remarks').focus();
            });
    }
    else{
        setTimeout(function(){
            $.ajax({
                url: "/erp/stores/material_requisition/reject/",
                type: "POST",
                dataType: "json",
                data: {mrs_id: mrs_id, remarks:$("#remarks").val()},
                success: function (json) {
                    $("#loading").hide();
                    var swal_type = "error";
                    var swal_title = "<span style='color: #dd4b39;;'>Unable to Reject the MRS at the moment!</span>";
                    if(json.response_message.indexOf("success") > 0) {
                        swal_type = "success";
                        swal_title = "<span style='color: #44ad6b;'>Rejected</span>";
                        swal({
                            title: swal_title,
                            text: json.response_message,
                            type: swal_type,
                            html: true
                            },
                            function(){
                                generate_pdf_ajax(mrs_id, -2);
                                updateTableStatus(-2, mrs_id);
                        });
                    }
                    else if(json.response_message.indexOf("success") == -1)
                    {
                        swal_title = "<span style='color: #44ad6b;'>Rejected</span>";
                        swal({
                            title: swal_title,
                            text: json.response_message,
                            type: swal_type,
                            html: true
                            },
                            function(){
                                $("#mrs_document_modal").modal("show");
                                location.reload();
                        });
                    }

                },
                  error: function (xhr, errmsg, err) {
                    console.log(xhr.status + ": " + xhr.responseText);
                    $("#loading").hide();
                  }
            });
        },50);
    }
}
function updateTableStatus(status, mrs_id, receipt_code=null){
    var editedRow = $("#MrsList").find("tbody tr td a[data-mrsid='"+mrs_id+"']").closest("tr");
    if(status == 1) {
        editedRow.find("td.td_status").find("a").removeClass('draft rejected').addClass('approved').text("Approved");
        if(receipt_code != null){
            editedRow.find("td").find("a.edit_link_code").text(receipt_code);
        }
    }
    if(status == -2) {
        editedRow.find("td.td_status").find("a").removeClass('approved').addClass('rejected').text("Rejected");
    }
   editedRow.find("td span.pdf_genereate").attr("onclick", "generate_pdf_ajax("+mrs_id+", "+status+")");
}

function TableHeaderFixed(){
	oTable = $('#MrsList').DataTable({
			fixedHeader: false,
	        "scrollY": Number($(document).height() - 230),
	        "scrollX": true,
			"pageLength": 50,
			"search": {
				"smart": false
			},
			"columns": [
				null,null,null,null,
				{ "type": "date" },
				null,null
				]
		});
	updateMrsListJson();
	oTable.on("draw",function() {
		var keyword = $('#MrsList_filter > label:eq(0) > input').val();
		$('#MrsList').unmark();
		$('#MrsList').mark(keyword,{});
		updateMrsListJson();
		setHeightForTable();
		listTableHoverIconsInit('MrsList');
	});
	oTable.on('page.dt', function() {
	  $('html, body').animate({
		scrollTop: $(".dataTables_wrapper").offset().top - 15
	   }, 'slow');
	   listTableHoverIconsInit('MrsList');
	});
	oSettings = oTable.settings();
	listTableHoverIconsInit('MrsList');
	$( window ).resize();
}

function updateMrsListJson(){
	setTimeout(function(){
    	MrsListjsonObj = [];
    	var rows = oTable.rows( { page : 'current'} ).data();
        for(var i=0;i<rows.length;i++) {
        	var selectedRow = $(rows[i][1]);
        	mrs = {}
	        mrs ["mrsId"] = selectedRow.attr("data-MrsId")
	        mrs ["mrsNumber"] = selectedRow.text().trim();
            MrsListjsonObj.push(mrs);
        }
		localStorage.setItem('MrsListav', JSON.stringify(MrsListjsonObj));
	},10);
}

function NavTableRemove() {
	$('ul.nav-tabs li a').click(function() {
		if($(this).attr('id') != "tab_view") {
			if($("#MrsList").hasClass('dataTable')) {
				oTable.destroy();
			}
		}
		else {
			TableHeaderFixed();
		}
	});
}

	function editMRSRow(mrsId, mrsUrl, openTarget="") {
		$("#edit_mrs_id").val(mrsId);
		$("#edit_mrs_form").attr("action", mrsUrl);
		$("#edit_mrs_form").attr("target", openTarget).submit();
	}


function generate_pdf_ajax(mrs_id, status){
    $("#mrs_doc_btn a.btn, #remarks").addClass("hide");
    $(".remarks_count_link").css({float: ""});
    $("#mrs_document_modal").modal("show");
//    $("#loading").show();
    $.ajax({
        url: "/erp/stores/material_requsition/document/",
        type: "get",
        datatype: "json",
        data: {mrs_id: mrs_id, status: status},
        success: function (response) {
//            $("#loading").hide();
            if(response.remarks != undefined) {
                if(response.remarks.length > 0){
                    $(".remarks_count_link").removeClass('disabled').find(".remarks_counter").text(response.remarks.length);
                }
                else {
                    $(".remarks_count_link").addClass('disabled').find(".remarks_counter").text("No ");
                }
                $("#mrs_document_remarks").val(JSON.stringify(response.remarks));
            }
            var row = '<object id="mrs_document" class="document_viewer" data="'+response.url+'">'+getPdfErrorMessage()+'</object>';
            $("#mrs_document_container").html(row);
            $("#modal_mrs_id").val(mrs_id);
            $("#display_popup").removeClass('hide');
            if(status == -2) {
                if($("#reject_mrs").hasClass("for_mrs_approve")) {
                    $("#approve_mrs, #remarks").removeClass("hide");
                }
            }
            else if(status == 0) {
                if($("#reject_mrs").hasClass("for_mrs_approve")) {
                    $("#approve_mrs, #remarks, #reject_mrs").removeClass("hide");
                }
                else {
                    $("#remarks, #reject_mrs").removeClass("hide");
                }
                $("#reject_mrs").text("Discard");
            }
            else if(status == 1) {
                if($("#reject_mrs").hasClass("for_mrs_approve")) {
                    $("#remarks, #reject_mrs").removeClass("hide");
                    $("#approve_mrs").addClass("hide");
                    $("#reject_mrs").text("Reject");
                }

            }

            if($("#remarks").hasClass("hide") || $("#remarks").length <= 0) {
                $(".remarks_count_link").css({float: "left"});
            }
            else {
                $(".remarks_count_link").css({float: "right"});
            }
            $("#remarks").val("");
        },
        error: function() {
            swal("","Unable to generate document. Please try again", "error");
            $("#mrs_document_modal").modal("hide");
        }
    });
    $('#mrs_document_modal').on('hidden.bs.modal', function () {
        $(".remarks_count_link").css({float: "right"});
        $("#mrs_document").remove();
        $("#mrs_document_container").html(getPdfLoadingImage());
    });
}