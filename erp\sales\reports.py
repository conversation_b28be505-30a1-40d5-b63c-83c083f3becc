from decimal import Decimal
from operator import and_, or_

from django.template.response import TemplateResponse
from sqlalchemy.orm import make_transient

from erp import helper
from erp.accounts import PACKING_ACCOUNT_NAME, TRANSPORT_ACCOUNT_NAME
from erp.auth import ENTERPRISE_ID_SESSION_KEY, PRIMARY_ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.models import Invoice, PartyRegistrationDetail, InvoiceMaterial, OA
from erp.properties import SALES_REPORT_TEMPLATE, MATERIAL_TAX_REPORT_TEMPLATE, INVOICE_TAX_REPORT_TEMPLATE, \
	TEMPLATE_TITLE_KEY, OA_REPORT_TEMPLATE
from erp.sales import logger
from settings import SQLASession
from util.api_util import JsonUtil


class SalesReportElement(object):
	"""

	"""

	def __init__(self, inv_no=None, inv_date=None, oa_no=None, oa_date=None, inv_value=None, project=None,
	             customer=None,gstin=None, inv_type=None, payment_status=None, tax=None, cgst=0, sgst=0, igst=0, description=None,
	             qty=0, rate=0, value=0, cumulative_total=0, packing_charges=0, transport_charges=0):
		self.inv_no = inv_no
		self.inv_date = inv_date
		self.oa_no = oa_no
		self.oa_date = oa_date
		self.inv_value = inv_value
		self.project = project
		self.customer = customer
		self.gstin = gstin
		self.inv_type = inv_type
		self.payment_status = payment_status
		self.tax = tax
		self.cgst = cgst
		self.sgst = sgst
		self.igst = igst
		self.description = description
		self.qty = qty
		self.rate = rate
		self.value = value
		self.cumulative_total = cumulative_total
		self.packing_charges = packing_charges
		self.transport_charges = transport_charges

	def __repr__(self):
		return "%s" % self.__dict__


def generateSalesReport(request):
	request_handler = RequestHandler(request)
	party_id = request_handler.getPostData('party_id')
	from_date, to_date = JsonUtil.getDateRange(rh=request_handler)

	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)

	customer_list = helper.populatePartyChoices(enterprise_id=enterprise_id, populate_all=True, is_customer=True)
	invoices_query = SQLASession().query(Invoice).filter(
		Invoice.enterprise_id == enterprise_id,
		Invoice.issued_on >= from_date,
		Invoice.issued_on <= to_date,
		Invoice.type.in_(Invoice.TYPES["sales"]),
		Invoice.status == Invoice.STATUS_APPROVED)
	if party_id and int(party_id) != -1:
		invoices_query = invoices_query.filter(Invoice.party_id == party_id)
	invoices = invoices_query.all()
	# TODO performance enhancement
	sales_report = []
	for inv in invoices:
		logger.debug("Bill Ledger Bill ID Value: %s" % (inv.ledger_bill.net_value == 0) if inv.ledger_bill else False)
		payment_status = "Pending"
		if inv.ledger_bill:
			payment_status = "Received" if inv.ledger_bill.net_value == 0 else ("%s (%s)" % (inv.ledger_bill.net_value,
			                                                                                 "Dr" if inv.ledger_bill.is_debit else "Cr") if inv.ledger_bill.net_value < inv.grand_total and not inv.ledger_bill.is_debit else "Pending")
		sales_report_element = SalesReportElement(
			inv_no=inv.getInternalCode(), inv_date=inv.approved_on, oa_no=inv.getInvoiceOACodes(),
			oa_date=inv.order_accept_date, inv_value=inv.grand_total, project=inv.project.name if inv.project else "",
			customer=inv.customer.name, inv_type=inv.type, payment_status=payment_status)
		sales_report.append(sales_report_element)
		make_transient(inv)

	return TemplateResponse(template=SALES_REPORT_TEMPLATE, request=request, context={
		'report_rows': sales_report, 'customers': customer_list, 'from_date': from_date.strftime("%Y-%m-%d"),
		'to_date': to_date.strftime("%Y-%m-%d"),
		'party_id': int(party_id) if party_id else -1, 'type': 'sales', TEMPLATE_TITLE_KEY: "Status Report"})


def generateInvoiceTaxReport(request):
	request_handler = RequestHandler(request)
	from_date, to_date = JsonUtil.getDateRange(rh=request_handler)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	db_session = SQLASession()
	customer_list = helper.populatePartyChoices(enterprise_id=enterprise_id, is_customer=True, is_supplier=True)
	sales_report = []
	party_id = -1
	grand_total = 0
	cgst_total = 0
	sgst_total = 0
	igst_total = 0
	value_total = 0
	cumulative_grand_total = 0
	packing_charges_total = 0
	transport_charges_total = 0
	try:
		party_id = request_handler.getPostData('party_id')
		invoices_query = db_session.query(Invoice).filter(
			Invoice.enterprise_id == enterprise_id, Invoice.issued_on >= from_date, Invoice.issued_on <= to_date,
			Invoice.type.in_(Invoice.TYPES["sales"]),
			Invoice.status.notin_((Invoice.STATUS_REJECTED, Invoice.STATUS_CANCELLED)))
		if party_id and int(party_id) != -1:
			invoices_query = invoices_query.filter(Invoice.party_id == party_id)
		invoices = invoices_query.order_by(Invoice.approved_on).all()
		# TODO performance enhancement
		for inv in invoices:
			grand_qty = 0
			for item in inv.items:
				grand_qty += item.quantity
			for item in inv.non_stock_items:
				grand_qty += item.quantity
			cgst = round(inv.getNetMaterialTaxForType("CGST"), 2)
			sgst = round(inv.getNetMaterialTaxForType("SGST"), 2)
			igst = round(inv.getNetMaterialTaxForType("IGST"), 2)
			cumulative_total = round(inv.getNetMaterialTaxForType(""), 2)
			value = round(inv.getTotalTaxableValue(), 2)
			packing_charges = round(inv.getTotalChargeValueByType(charge_type=PACKING_ACCOUNT_NAME), 2)
			transport_charges = round(inv.getTotalChargeValueByType(charge_type=TRANSPORT_ACCOUNT_NAME), 2)
			gstin = db_session.query(PartyRegistrationDetail.details).filter(
				PartyRegistrationDetail.party_id == inv.customer.id,
				PartyRegistrationDetail.enterprise_id == inv.customer.enterprise_id,
				PartyRegistrationDetail.label == "GSTIN").first()
			sales_report_element = SalesReportElement(inv_no=inv.getInternalCode(), inv_date=inv.approved_on,
				oa_no=inv.getInvoiceOACodes(), oa_date=inv.order_accept_date, inv_value=inv.grand_total,
				project=inv.project.name, customer=inv.customer.name, gstin=gstin[0] if len(gstin) > 0 else '', inv_type=inv.type, cgst=cgst, sgst=sgst,
				igst=igst, qty=Decimal("%0.2f" % grand_qty), value=value, cumulative_total=cumulative_total, packing_charges=packing_charges,
				transport_charges=transport_charges)
			sales_report.append(sales_report_element)
			grand_total += inv.grand_total
			cgst_total += cgst
			sgst_total += sgst
			igst_total += igst
			value_total += value
			cumulative_grand_total += cumulative_total
			packing_charges_total += packing_charges
			transport_charges_total += transport_charges
			make_transient(inv)
	except Exception as e:
		logger.exception("Failed constructing Sales Custom Reports. %s" % e.message)
	return TemplateResponse(template=INVOICE_TAX_REPORT_TEMPLATE, request=request, context={
		'report_rows': sales_report, 'customers': customer_list, 'from_date': from_date.strftime("%Y-%m-%d"),
		'to_date': to_date.strftime("%Y-%m-%d"), 'party_id': int(party_id) if party_id else -1,
		'total_row': SalesReportElement(
			description="Total", inv_value=grand_total, cgst=cgst_total, sgst=sgst_total, igst=igst_total,
			cumulative_total=cumulative_grand_total, value=value_total, packing_charges=packing_charges_total,
			transport_charges=transport_charges_total
		), 'type': 'sales', TEMPLATE_TITLE_KEY: "Tax Report"})


def generateMaterialWiseTaxReport(request):
	request_handler = RequestHandler(request)
	party_id = request_handler.getPostData('party_id')
	from_date, to_date = JsonUtil.getDateRange(rh=request_handler)

	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	db_session = SQLASession()

	customer_list = helper.populatePartyChoices(enterprise_id=enterprise_id,populate_all=True,is_customer=True)

	invoices_query = db_session.query(Invoice).filter(
		Invoice.enterprise_id == enterprise_id,
		Invoice.issued_on >= from_date,
		Invoice.issued_on <= to_date, Invoice.type.in_(Invoice.TYPES["sales"]),
		Invoice.status.notin_((Invoice.STATUS_REJECTED, Invoice.STATUS_CANCELLED)))
	if party_id and int(party_id) != -1:
		invoices_query = invoices_query.filter(Invoice.party_id == party_id)
	invoices = invoices_query.all()
	# TODO performance enhancement
	sales_report = []
	grand_total = 0
	cgst_total = 0
	sgst_total = 0
	igst_total = 0
	value_total = 0
	for inv in invoices:
		invoices_items = inv.items

		for inv_item in invoices_items:
			cgst = round(inv_item.getTaxValueForType("CGST"), 2)
			sgst = round(inv_item.getTaxValueForType("SGST"), 2)
			igst = round(inv_item.getTaxValueForType("IGST"), 2)
			value = round((inv_item.quantity * inv_item.rate), 2)

			total = round(inv_item.getTaxValueForType("CGST"), 2) + round(inv_item.getTaxValueForType("SGST"),
			                                                              2) + round(
				inv_item.getTaxValueForType("IGST"), 2) + round((inv_item.quantity * inv_item.rate), 2)
			make_name = helper.constructDifferentMakeName(inv_item.item.makes_json)
			project = ""
			if inv.project:
				project_name = inv.project.name if inv.project.name else ""
				project_code = inv.project.code if inv.project.code else ""
				if project_code:
					project = project_name + " (" + project_code + ")"
				else:
					project = project_name
			sales_report_element = SalesReportElement(inv_no=inv.getInternalCode(), inv_date=inv.approved_on,
			                                          oa_no=inv.getInvoiceOACodes() if inv_item.oa_no else "", oa_date=inv.order_accept_date,
			                                          inv_value=total, project=project,
			                                          customer=inv.customer.name, inv_type=inv.type,
			                                          cgst=cgst,
			                                          sgst=sgst,
			                                          igst=igst,
			                                          description="%s %s %s" %(inv_item.item.name,"[%s]" % make_name if make_name  else "","[%s]" % "Faulty" if inv_item.is_faulty ==1 else ""),
			                                          qty="%0.2f" % inv_item.quantity,
			                                          rate=round(inv_item.rate, 2), value=value)
			sales_report.append(sales_report_element)
			grand_total += total
			cgst_total += cgst
			sgst_total += sgst
			igst_total += igst
			value_total += value
			make_transient(inv)
		invoices_ns_items = inv.non_stock_items
		for inv_item in invoices_ns_items:
			cgst = round(inv_item.getTaxValueForType("CGST"), 2)
			sgst = round(inv_item.getTaxValueForType("SGST"), 2)
			igst = round(inv_item.getTaxValueForType("IGST"), 2)
			value = round((inv_item.quantity * inv_item.rate), 2)

			total = round(inv_item.getTaxValueForType("CGST"), 2) + round(inv_item.getTaxValueForType("SGST"),
			                                                              2) + round(
				inv_item.getTaxValueForType("IGST"), 2) + round((inv_item.quantity * inv_item.rate), 2)
			make_name = helper.constructDifferentMakeName(inv_item.item.makes_json)
			sales_report_element = SalesReportElement(inv_no=inv.getInternalCode(), inv_date=inv.approved_on,
			                                          oa_no=inv.getInvoiceOACodes() if inv_item.oa_no else "", oa_date=inv.order_accept_date,
			                                          inv_value=total, project=inv.project.name + " ("+ inv.project.code  + ")" if inv.project is not None else "",
			                                          customer=inv.customer.name, inv_type=inv.type,
			                                          cgst=cgst,
			                                          sgst=sgst,
			                                          igst=igst,
			                                          description="%s %s" %(inv_item.item_name, "[%s]" % make_name if make_name else ""),
			                                          qty="%0.2f" % inv_item.quantity,
			                                          rate=round(inv_item.rate, 2), value=value)
			sales_report.append(sales_report_element)
			grand_total += total
			cgst_total += cgst
			sgst_total += sgst
			igst_total += igst
			value_total += value
			make_transient(inv)

	return TemplateResponse(template=MATERIAL_TAX_REPORT_TEMPLATE, request=request, context={
		'report_rows': sales_report, 'customers': customer_list, 'from_date': from_date.strftime("%Y-%m-%d"),
		'to_date': to_date.strftime("%Y-%m-%d"), 'party_id': int(party_id) if party_id else -1,
		'total_row': SalesReportElement(
			description="Total", inv_value=grand_total, cgst=cgst_total, sgst=sgst_total, igst=igst_total,
			value=value_total), 'type': 'sales',
		TEMPLATE_TITLE_KEY: "Material Report"})

class OAReportElement(object):
	"""

	"""

	def __init__(
			self, inv_no=None, inv_date=None, customer=None, drawing_no=None, description=None, unit_id=None, po_no=None,
			oa_no=None, oa_date=None, oa_qty=None, inv_qty=None, pending_qty=None, oa_value=None, inv_value=None, pending_value=None):
		self.inv_no = inv_no
		self.inv_date = inv_date
		self.customer = customer
		self.drawing_no = drawing_no
		self.description = description
		self.unit_id = unit_id
		self.po_no = po_no
		self.oa_no = oa_no
		self.oa_date = oa_date
		self.oa_qty = oa_qty
		self.inv_qty = inv_qty
		self.pending_qty = pending_qty
		self.oa_value = oa_value
		self.inv_value = inv_value
		self.pending_value = pending_value

	def __repr__(self):
		return "%s" % self.__dict__


def generateOAReport(request):
	request_handler = RequestHandler(request)
	party_id = request_handler.getPostData('party_id')
	from_date, to_date = JsonUtil.getDateRange(rh=request_handler)

	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	primary_enterprise_id = request_handler.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
	db_session = SQLASession()

	customer_list = helper.populatePartyChoices(
		enterprise_id=enterprise_id, populate_all=True, is_customer=True, is_supplier=True)
	oa_query = db_session.query(OA).filter(
		OA.enterprise_id == enterprise_id,
		OA.approved_on >= from_date,
		OA.approved_on <= to_date,
		OA.status == OA.STATUS_APPROVED,
		OA.type != 'MRS'
	)
	if party_id and int(party_id) != -1:
		oa_query = oa_query.filter(OA.party_id == party_id)
	oas = oa_query.all()
	oa_report = []
	for oa in oas:
		po_no = oa.po_no if oa.po_no else ""
		for oa_item in oa.items:
			make_name = helper.constructDifferentMakeName(oa_item.item.makes_json)
			if oa_item.alternate_unit_id:
				unit_name = helper.getUnitName(enterprise_id=oa_item.enterprise_id, unit_id=oa_item.alternate_unit_id)
				scale_factor = helper.getScaleFactor(
					enterprise_id=enterprise_id, item_id=oa_item.item_id, alternate_unit_id=oa_item.alternate_unit_id)
				if scale_factor:
					oa_qty = Decimal(oa_item.quantity) / Decimal(scale_factor)
			else:
				oa_qty = "%0.2f" % oa_item.quantity
				unit_name = oa_item.item.unit.unit_name
			invoice_details = getInvoiceDetails(
				db_session=db_session, enterprise_id=enterprise_id, oa_id=int(oa.id), item_id=oa_item.item_id)
			oa_report_element = OAReportElement(
				inv_no="", inv_date="",
				oa_no=oa.getCode() if oa.id else "",
				oa_date=oa.approved_on if oa.approved_on else "",
				customer=oa.customer.name,
				po_no=po_no,
				drawing_no=oa_item.item.drawing_no if oa_item.item.drawing_no else "",
				description="%s %s %s" % (
					oa_item.item.name,
					"[%s]" % make_name if make_name else "",
					"[%s]" % "Faulty" if oa_item.is_faulty == 1 else ""),
				unit_id=unit_name,
				inv_value="", inv_qty="",
				oa_qty=oa_qty,
				oa_value=round(oa_item.quantity * oa_item.price, 2) * (100 - float(oa_item.discount)) / 100,
				pending_qty="" if invoice_details else oa_qty,
				pending_value="" if invoice_details else round(oa_item.quantity * oa_item.price, 2) * (100 - float(oa_item.discount)) / 100
			)
			oa_report.append(oa_report_element)
			if invoice_details is not None:
				tot_inv_qty = 0
				tot_inv_value = 0
				for item in invoice_details:
					tot_inv_qty = Decimal(tot_inv_qty) + Decimal(item.inv_qty)
					tot_inv_value = Decimal(tot_inv_value) + Decimal(item.inv_value)
					item.pending_qty = Decimal(oa_qty) - tot_inv_qty
					item.pending_value = Decimal(round(oa_item.quantity * oa_item.price, 2) * (100 - float(oa_item.discount)) / 100) - Decimal(round(tot_inv_value, 2))
					item.po_no = po_no
					oa_report.append(item)

	return TemplateResponse(template=OA_REPORT_TEMPLATE, request=request, context={
		'report_rows': oa_report, 'customers': customer_list, 'from_date': from_date.strftime("%Y-%m-%d"),
		'to_date': to_date.strftime("%Y-%m-%d"), 'party_id': int(party_id) if party_id else -1, 'type': 'sales',
		TEMPLATE_TITLE_KEY: "OA Report" if enterprise_id == primary_enterprise_id else "IWO Report",
		"is_primary_project" : enterprise_id == primary_enterprise_id})


def getInvoiceDetails(db_session=None, enterprise_id=None, oa_id=None, item_id=None):
	invoices_query = db_session.query(Invoice).join(InvoiceMaterial, and_(
		Invoice.enterprise_id == enterprise_id, Invoice.id == InvoiceMaterial.invoice_id)).filter(
		or_(Invoice.type.in_(Invoice.TYPES["sales"]), Invoice.type.in_(Invoice.TYPES["dc"])),
		Invoice.status.notin_((Invoice.STATUS_REJECTED, Invoice.STATUS_CANCELLED)),
		InvoiceMaterial.oa_no != 0, InvoiceMaterial.oa_no == oa_id,
		InvoiceMaterial.delivered_dc_id.is_(None),
		InvoiceMaterial.item_id == item_id
	).group_by(Invoice.id, InvoiceMaterial.item_id, InvoiceMaterial.oa_no)
	invoices = invoices_query.all()
	# TODO performance enhancement
	sales_report = []
	grand_total = 0
	value_total = 0
	for inv in invoices:
		invoices_items = inv.items
		for inv_item in invoices_items:
			if inv_item.item_id == item_id and inv_item.oa_no == oa_id:
				if inv_item.alternate_unit_id:
					unit_name = helper.getUnitName(enterprise_id=inv_item.enterprise_id, unit_id=inv_item.alternate_unit_id)
					scale_factor = helper.getScaleFactor(
						enterprise_id=enterprise_id, item_id=inv_item.item_id, alternate_unit_id=inv_item.alternate_unit_id)
					if scale_factor:
						inv_qty = Decimal(inv_item.quantity) / Decimal(scale_factor)
				else:
					inv_qty = "%0.2f" % inv_item.quantity
					unit_name = inv_item.item.unit.unit_name
				value = round((inv_item.quantity * inv_item.rate), 2)
				total = round((inv_item.quantity * inv_item.rate), 2) * (100 - float(inv_item.discount)) / 100
				make_name = helper.constructDifferentMakeName(inv_item.item.makes_json)
				sales_report_element = OAReportElement(
					inv_no=inv.getInternalCode(), inv_date=inv.approved_on,
					oa_no=inv_item.inv_oa.getCode() if inv_item.oa_no else "",
					oa_date=inv_item.inv_oa.approved_on if inv_item.oa_no else "",
					inv_value=total, customer=inv.customer.name,
					unit_id=unit_name,
					drawing_no=inv_item.item.drawing_no if inv_item.item.drawing_no else "",
					description="%s %s %s" % (
						inv_item.item.name, "[%s]" % make_name if make_name else "", "[%s]" % "Faulty" if inv_item.is_faulty ==1 else ""),
					inv_qty=inv_qty, oa_value="", oa_qty="", po_no="")
				sales_report.append(sales_report_element)
				grand_total += total
				value_total += value
				make_transient(inv)
		invoices_ns_items = inv.non_stock_items
		for inv_item in invoices_ns_items:
			if inv_item.item_id == item_id and inv_item.oa_no == oa_id:
				value = round((inv_item.quantity * inv_item.rate), 2)
				total = round((inv_item.quantity * inv_item.rate), 2) * (100 - float(inv_item.discount)) / 100

				sales_report_element = OAReportElement(
					inv_no=inv.getInternalCode(), inv_date=inv.approved_on,
					oa_no=inv_item.inv_oa.getCode() if inv_item.oa_no else "",
					oa_date=inv_item.inv_oa.approved_on if inv_item.oa_no else "",
					inv_value=total, customer=inv.customer.name,
					unit_id=inv_item.item.unit.unit_name,
					drawing_no=inv_item.item.drawing_no if inv_item.item.drawing_no else "",
					description="%s %s %s" % (
						inv_item.item.name, "[%s]" % inv_item.make.label if inv_item.make_id !=1 else "", "[%s]" % "Faulty" if inv_item.is_faulty ==1 else ""),
					inv_qty="%0.2f" % inv_item.quantity)
				sales_report.append(sales_report_element)
				grand_total += total
				value_total += value
				make_transient(inv)
	return sales_report
