"""
"""
from datetime import datetime

from django.test import TestCase

from erp.models import Invoice
from migration.backend import ExportService

__author__ = 'saravanan'


class TestExport(TestCase):

	def setUp(self):
		pass

	def tearDown(self):
		pass

	def test_exportUnit(self):
		export_service = ExportService()
		unit_details = export_service.getUnitDetails(enterprise_id=102)
		self.assertNotEquals(unit_details, None)

	def test_exportAccountGroup(self):
		export_service = ExportService()
		groups = export_service.getAccountGroups(enterprise_id=102)
		self.assertNotEquals(groups, None)

	def test_exportAccountLedger(self):
		export_service = ExportService()
		ledgers = export_service.getAccountLedgers(enterprise_id=102)
		self.assertNotEquals(ledgers, None)

	def test_exportVoucher(self):
		export_service = ExportService()
		vouchers = export_service.getVouchers(enterprise_id=156, from_date=datetime(2020, 04, 01), to_date=datetime(2021, 03, 31))
		self.assertNotEquals(vouchers, None)

	def test_generateRoot(self):
		export_service = ExportService()
		root = export_service.getGenerateRoot()
		self.assertNotEquals(root, None)

	def test_generateXML(self):
		export_service = ExportService()
		xml = export_service.generateTallyXML(
			file_name='XMLTest.xml', enterprise_id=102, from_date=datetime(2020, 04, 01), to_date=datetime(2021, 03, 31))
		self.assertEquals(xml, True)

	def test_getItemMaster(self):
		export_service = ExportService()
		xml = export_service.getItemMaster(enterprise_id=156)
		self.assertNotEquals(xml, True)

	def test_getItemCategory(self):
		export_service = ExportService()
		xml = export_service.getItemCategory(enterprise_id=156)
		self.assertNotEquals(xml, True)

	def test_exportInvoices(self):
		export_service = ExportService()
		invoices = export_service.getInvoices(
			enterprise_id=156, from_date=datetime(2020, 04, 01), to_date=datetime(2021, 03, 31), invoice_type=Invoice.TYPES['sales'])
		self.assertNotEquals(invoices, None)
