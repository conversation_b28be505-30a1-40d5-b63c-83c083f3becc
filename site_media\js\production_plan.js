$( document ).ready(function() {
    $("#li_production_plan").addClass('active');
    $("#menu_production_planning").addClass('selected');
    dataRangePickerInit();
    dataTableInit();
    itemDropDownChangeEvent();
    getManufactureIndentList();
    getIssueTo();
    getProductionPlanList();
    getLocationList();
    miAssignedSaveEventInit();
    productionLogModalCloseEvent();
    miAssignedChangeEvent();
    $( window ).scroll(function() {
      $( "body" ).click();
      $(".daterangepicker.show-calendar").hide();
    });
});

function dataRangePickerInit() {
    var today = moment();
    $('.report-range_add').daterangepicker({
        startDate: today,
        endDate: today,
        showDropdowns: true,
        autoApply: true
    }, onSelect);
    onSelect(today, today);

    $('.report-range_add').on('hide.daterangepicker', function(ev, picker) {
		$(".daterangepicker.show-calendar").hide();
	});
}

function onSelect(start, end) {
    $('.report-range_add span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $(".fromdate_add").val(start.format('YYYY-M-D'))
    $(".todate_add").val(end.format('YYYY-M-D'))
    $(".daterangepicker.show-calendar").hide();
}

function applyPpFilter() {
    updateFilterText();
    getProductionPlanList();
    closeFilterOption();
}

function updateFilterText() {
    var statusFilter = "";
    var timelinessFilter = "";
    $(".filter-status").find(".checkbox").each(function(){
        if($(this).find("input").is(":checked")) {
            statusFilter += $(this).find("label").text() +", ";
        }
    });
    $(".filter-timeliness").find(".checkbox").each(function(){
        if($(this).find("input").is(":checked")) {
            timelinessFilter += $(this).find("label").text() +", ";
        }
    });
    $(".filtered-date b").text($("#reportrange").find("span").text());
    $(".filtered-assigned-to b").text($("#filter_assigned_to option:selected").text());
    $(".filtered-status b").text((statusFilter == "")? '-None-' : statusFilter.slice(0, -2));
    $(".filtered-timeliness b").text((timelinessFilter == "")? '-None-' : timelinessFilter.slice(0, -2));
}

function itemDropDownChangeEvent() {
    $(".item_select").change(function() {
        if($(this).attr("id") == "item_select") {
            miItemChangeEvent('add');
        }
        else {
            miItemChangeEvent('edit')
        }
    });
}

function getManufactureIndentList() {
    $.ajax({
        url: '/erp/production/get_manufacture_indents/',
        type: "POST",
        dataType: "json",
        data: {},
        success: function (response) {
            $('.mi_select').empty().chosen('destroy');
            $('.mi_select').append(`<option value="0">-- Select MI Number --</option>`);
            $.each(response.manufacture_indents, function(i, item) {
                var option = $('<option/>');
                if(item.oa_no != "" && item.oa_no != null) {
                    option.attr({ 'value': item.mi_id }).text(`${item.mi_no}|~|${item.oa_no}|~|${item.party_name}`);
                }
                else {
                    option.attr({ 'value': item.mi_id }).text(`${item.mi_no}`);
                }
                $('.mi_select').append(option);
            });
            $(".mi_select").chosen();
            customizedChosenConfiguration();
        },
        error: function (xhr, errmsg, err) {
            checkSessionExpired(xhr);
        }
    });
}

function customizedChosenConfiguration() {
    $('.mi_select').on('change', function(){
        var selectedText = $(this).find("option:selected").text().split(`|~|`);
        var currentId = $(this).attr("id");
        $("#"+currentId+"_chosen").find(".chosen-single span").text(selectedText[0]);
        if($(this).val() != 0) {
            getMiMaterials(currentId);
            if(currentId == 'mi_select') {
                $(".for-new-pp-post").removeClass("hide");
            }
        }
        else {
            resetMiDetails();
        }
    });
    $("#mi_select_chosen, #edit_mi_select_chosen").click(function(){
        $(this).find(".chosen-results").find("li").each(function(){
            var miList = $(this).html().split('|~|');
            miOrganizedList = `<span style='font-size: 14px'>${miList[0]}</span>`;
            if(miList[1]) {
                miOrganizedList += `<br /><small>${miList[1]} <span> (${miList[2]})</span></small>`
            }
            $(this).html(miOrganizedList);
        });
    });

    $(".chosen-search-input").keyup(function(){
        $(this).closest(".chosen-drop").find(".chosen-results").find("li").each(function(){
            var miList = $(this).html().split('|~|');
            miOrganizedList = `<span style='font-size: 14px'>${miList[0]}</span>`;
            if(miList[1]) {
                miOrganizedList += `<br /><small>${miList[1]} <span> (${miList[2]})</span></small>`
            }
            $(this).html(miOrganizedList);
        });
    });
}

function getMiMaterials(currentId) {
    var selectedMi = $(`#${currentId}`).val();
    $.ajax({
        url: '/erp/production/get_mi_material/',
        type: "POST",
        dataType: "json",
        async: false,
        data: { mi_id: selectedMi },
        success: function (response) {
            $(".item_select optgroup").empty();
            $(".item_select").chosen('destroy');
             $.each(response.mi_material, function(i, item){
                var option = $('<option/>');
                var itemName = item.item_name;
                if(item.is_service) {
                    itemName += `<span class='service-item-flag'></span>`;
                }
                option.attr({
                                'value': item.item_id,
                                'data-make_id':item.make_id,
                                'data-qty': item.request_qty,
                                'data-unit': item.unit_name,
                                'data-alternate_unit_id': item.alternate_unit_id,
                                'data-scale_factor': item.scale_factor
                            }).html(`${itemName}`);
                $('.item_select optgroup').append(option);
                var scheduleStartDate = moment();
                var scheduleEndDate = moment(item.delivery_date);
                if(scheduleEndDate > scheduleStartDate) {
                    $( "#reportrange_add #todate_add" ).val(moment(scheduleEndDate).format("YYYY-M-D"));
                    var displayDateFormat = scheduleStartDate.format('MMM D, YYYY') + ' - ' + scheduleEndDate.format('MMM D, YYYY')
                    $('#reportrange_add span').html(displayDateFormat);
                    $( "#reportrange_add" ).data('daterangepicker').startDate = scheduleStartDate;
                    $( "#reportrange_add" ).data('daterangepicker').endDate = scheduleEndDate;
                    $( "#reportrange_add" ).data('daterangepicker').updateView();
                    $( "#reportrange_add" ).data('daterangepicker').updateCalendars();
                }
            });
            $(".item_select").chosen();
            resetMiItemDetails();

            if(response.mi_material.length == 0) {
                $(".edit_item_text").removeClass("hide");
                $("#edit_item_select_chosen").addClass("hide");
            }
            else {
                $(".edit_item_text").addClass("hide");
                $("#edit_item_select_chosen").removeClass("hide");
            }
        },
        error: function (xhr, errmsg, err) {
            checkSessionExpired(xhr);
        }
    });
}

function miItemChangeEvent(type) {
    if(type == 'add') {
        var selectedItem = $("#item_select option:selected");
    }
    else {
        var selectedItem = $("#edit_item_select option:selected");
    }

    if(selectedItem.val() != 0) {
        var alternateUnitQty =  Number(selectedItem.attr("data-qty")/selectedItem.attr("data-scale_factor")).toFixed(3);
        $(".mi_max_qty").text(alternateUnitQty);
        $(".mi_qty").val(alternateUnitQty);
        $(".unit_display").text(selectedItem.attr("data-unit"));
        $(".mi_max_qty_container").removeClass("hide");
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();
        $(".for-new-pp").removeClass("hide");
    }
    else {
        resetMiItemDetails();
    }
}

function getIssueTo() {
    $.ajax({
        url: '/erp/production/get_issue_to/',
        type: "POST",
        dataType: "json",
        data: {  },
        success: function (response) {
            $.each(response.issue_to[0], function(i, item) {
                if(item[0] != "" && item[0] != null) {
                    var option = $('<option/>');
                    option.attr({ 'value': item[0] }).text(`${item[0]}`);
                    $('.mi_assigned optgroup[label="Internal"], #filter_assigned_to optgroup[label="Internal"]').append(option);
                }
            });
            $.each(response.issue_to[1], function(i, item) {
                if(item[0] != "" && item[0] != null) {
                    var option = $('<option/>');
                    option.attr({ 'value': item[0] }).text(`${item[1]}`);
                    $('.mi_assigned optgroup[label="External"], #filter_assigned_to optgroup[label="External"]').append(option);
                }
            });
            $('.mi_assigned, #filter_assigned_to').chosen();
            $("#mi_assigned").val(0).trigger("chosen:updated");
        },
        error: function (xhr, errmsg, err) {
            checkSessionExpired(xhr);
        }
    });
}

function getProductionPlanList() {
    if(typeof(oTable) !== 'undefined') {
        oTable.destroy();
    }
    $("#pp_list").find("tbody").empty();
    var pp_type = 'all';
    if($(`#filter_assigned_to`).val() != 'all') {
        pp_type = $(`#filter_assigned_to option:selected`).closest("optgroup").attr("label").toLowerCase();
    }
    var data = [];
    var status = 0;
    var timeliness = 0;
    $(".filter-status").find("input[type='checkbox']").each(function(){
        if($(this).is(":checked")) {
            status += Number($(this).attr("data-value"));
        }
    });
    $(".filter-timeliness").find("input[type='checkbox']").each(function(){
        if($(this).is(":checked")) {
            timeliness += Number($(this).attr("data-value"));
        }
    });

    $.ajax({
        url: '/erp/production/get_production_plan_list/',
        type: "POST",
        dataType: "json",
        async: true,
        data: {
            from_date: $(".fromdate").val(),
            to_date: $(".todate").val(),
            assigned_to: $("#filter_assigned_to").val(),
            status: status,
            timeliness: timeliness,
            pp_type: pp_type
        },
        success: function (response) {
            $.each(response.production_plan, function(i, item) {
                createPpListRow(item);
            });
            dataTableInit();
            listTableHoverIconsInit("pp_list")
        },
        error: function (xhr, errmsg, err) {
            checkSessionExpired(xhr);
        }
    });
}

function createPpListRow(item) {
    var alternateCompletedQty = item.completed_qty/item.scale_factor;
    var alternatePPQty = item.pp_qty/item.scale_factor;
    var alternateAllotedQty = (item.allocated_qty / item.scale_factor) > 0 ?  item.allocated_qty / item.scale_factor : 0 ;
    var requiredQty = item.required_qty/item.scale_factor;
    var issuedQty = item.issued_qty/item.scale_factor;
    var issuedQtyPercentage = (issuedQty * 100) / alternateAllotedQty;
    var completedAllotedPercentage = (alternateAllotedQty * 100 ) / requiredQty > 0 ? (alternateAllotedQty * 100 ) / requiredQty : 0 ;
    var progressedDate = (item.completed_date != "") ? "Progress as on: "+moment(item.completed_date).format('MMM DD, YYYY') : "";
    var pp_status = item.status.replace(/ /g, '_').toLowerCase();
    var pp_timeliness = item.timeliness.replace(/ /g, '_').toLowerCase();
    var pp_permission = $("#pp_permission").val().toLowerCase();
    var purchase_permission = $("#purchase_permission").val().toLowerCase();
    var editIcon = "" , shortCloseIcons = "", updateProgressIcon = "";
    var completedQtyPercentage = issuedQty.toFixed(3) * 100  / alternateAllotedQty.toFixed(3)
    var completedPPQtyPercentage = alternateCompletedQty.toFixed(3) * 100  / alternatePPQty.toFixed(3)
    if(pp_permission == "true") {
        if(item.po_type == 2) {
            editIcon = `<span class="table-inline-icon hide" data-tooltip="tooltip" data-placement="left" title="Edit" role="button" onclick="editProductionPlan(this)">
                            <i class="fa fa-pencil"></i>
                        </span>`;
        }
        if(pp_status != "completed" && alternatePPQty != 0) {
            shortCloseIcons = `<span class="table-inline-icon hide" data-tooltip="tooltip" data-placement="left" title="Short Close" role="button" onclick="shortCloseConfirmationEvt(this)">
                                    <i class="fa fa-stop-circle"></i>
                                </span>`;
        }
        updateProgressIcon = ` <span class="table-inline-icon progress-inline-icon hide" data-tooltip="tooltip" data-placement="left" title="Update Progress" role="button" >
                                    <i class="fa fa-tasks"></i>
                                </span>`
    }
    if(purchase_permission == "true") {
        if(item.po_type == 1) {
            editIcon = `<span class="table-inline-icon hide" data-tooltip="tooltip" data-placement="left" title="Edit" role="button" onclick="editPo(${item.pp_id}, '${item.order_status}', 'target')">
                            <i class="fa fa-pencil"></i>
                        </span>`;
        }
    }
    var shortageIcon = "";
    var checkBox = "";
    if(item.po_type == 2) {
            shortageIcon = `<span class="table-inline-icon hide ${completedPPQtyPercentage == 100 ? 'disabled-field' : ''}" data-tooltip="tooltip" data-placement="left" title="Material Requirement Calculator" role="button" onclick="shortageCalculatorPP(${item.pp_id})">
                                <i class="fa fa-calculator"></i>
                            </span>`;
            checkBox = `<input type="checkbox" class="${completedPPQtyPercentage == 100 ? 'disabled-field' : ''}" id="material_${item.item_id}-${item.cat_code} " value="${item.item_id}">`
        }
    var allotedStatus = "";
    var issuedStatus = "";
    if(item.po_type != 1){
            allotedStatus = `<span class='status-container' data-placement="left" data-tooltip="tooltip" title="${progressedDate}">
                            <span>
                                <span class="progress" style="margin-bottom:6px; display: block;">
                                    <span class="progress-bar progress-bar-success" role="progressbar" style="width:${completedAllotedPercentage}%; max-width: 100%" id="alloted_status">
                                        ${Math.round(completedAllotedPercentage)}%
                                    </span>
                                </span>
                                <span class='qty-label'>${Number(alternateAllotedQty).toFixed(3)} / ${Number(requiredQty).toFixed(3)}</span>
                            </span>
                        </span>`
            issuedStatus = `<span class='status-container' data-placement="left" data-tooltip="tooltip" title="${progressedDate}">
                            <span>
                                <span class="progress" style="margin-bottom:6px; display: block;">
                                    <span class="progress-bar progress-bar-success" role="progressbar" style="width:${issuedQtyPercentage}%; max-width: 100%">
                                        ${Math.round(issuedQtyPercentage)}%
                                    </span>
                                </span>
                                <span class='qty-label'>${Number(issuedQty).toFixed(3)} / ${Number(alternateAllotedQty).toFixed(3)}</span>
                            </span>
                        </span>`
    }else{
         allotedStatus = `<span class='status-container disabled-progress disabled-field' data-placement="left" data-tooltip="tooltip" title="${progressedDate}">
                            <span>
                                <span class="progress" style="margin-bottom:6px; display: block;">
                                    <span class="progress-bar progress-bar-success" role="progressbar" style="width:${completedAllotedPercentage}%; max-width: 100%">
                                        ${Math.round(completedAllotedPercentage)}%
                                    </span>
                                </span>
                                <span class='qty-label'>${Number(alternateAllotedQty).toFixed(3)} / ${Number(requiredQty).toFixed(3)}</span>
                            </span>
                        </span>`
            issuedStatus = `<span class='status-container disabled-progress disabled-field' data-placement="left" data-tooltip="tooltip" title="${progressedDate}">
                            <span>
                                <span class="progress" style="margin-bottom:6px; display: block;">
                                    <span class="progress-bar progress-bar-success" role="progressbar" style="width:${completedQtyPercentage}%; max-width: 100%">
                                        ${Math.round(completedQtyPercentage)}%
                                    </span>
                                </span>
                                <span class='qty-label'>${Number(issuedQty).toFixed(3)} / ${Number(requiredQty).toFixed(3)}</span>
                            </span>
                        </span>`
    }
    const csrftoken =$('#csrf_token').val()
    const enterprise_id =$('#enterprise_id').val()
    var row = ` <tr id="${item.pp_id}">
                    <td>${checkBox}</td>
                    <td class="exclude_export text-center mi-content" data-mi-id="${item.mi_id}">
                        <span class="mi-number">${item.mi_no}</span><br>
                        <span class="mi-date"><small>${moment(item.mi_date).format("MMM DD, YYYY")}</small></span>
                    </td>
                    <td class="exclude_export item-content" data-item-id="${item.item_id}" data-make-id="${item.make_id}" data-unit="${item.unit_name}" data-location-id="${item.location_id}">
                        ${item.item_name}
                        ${item.is_service == "1" ? `<span class="service-item-flag"></span>` : ''}
                        <input type="hidden" class="item_remarks" value='${item.remarks}' />
                        <input type="hidden" class="item_instruction" value='${item.instructions}' />
                    </td>
                    <td class="exclude_export text-right qty-content progress_${pp_status} timeliness_${pp_timeliness}"
                            data-status="${pp_status}"
                            data-scale_factor="${item.scale_factor}"
                            data-alternate_unit_id="${item.alternate_unit_id}"
                            data-default-qty="${Number(alternatePPQty).toFixed(3)}"
                            data-completed-qty="${Number(alternateCompletedQty).toFixed(3)}"
                            data-required-qty="${Number(requiredQty).toFixed(3)}"
                            data-default-unit="${item.unit_name}"
                            data-po_type="${item.po_type}"
                            data-order_status="${item.order_status}">
                        ${allotedStatus}
                    </td>
                    <td class="exclude_export text-right qty-content progress_${pp_status} timeliness_${pp_timeliness}"
                            data-status="${pp_status}"
                            data-scale_factor="${item.scale_factor}"
                            data-alternate_unit_id="${item.alternate_unit_id}"
                            data-default-qty="${Number(alternatePPQty).toFixed(3)}"
                            data-completed-qty="${Number(alternateCompletedQty).toFixed(3)}"
                            data-required-qty="${Number(requiredQty).toFixed(3)}"
                            data-default-unit="${item.unit_name}"
                            data-po_type="${item.po_type}"
                            data-order_status="${item.order_status}">
                        ${issuedStatus}
                    </td>
                    <td class="exclude_export text-right qty-content progress_${pp_status} timeliness_${pp_timeliness}" role="button"
                            data-status="${pp_status}"
                            data-scale_factor="${item.scale_factor}"
                            data-alternate_unit_id="${item.alternate_unit_id}"
                            data-default-qty="${Number(alternatePPQty).toFixed(3)}"
                            data-completed-qty="${Number(alternateCompletedQty).toFixed(3)}"
                            data-required-qty="${Number(requiredQty).toFixed(3)}"
                            data-default-unit="${item.unit_name}"
                            data-po_type="${item.po_type}"
                            data-order_status="${item.order_status}">
                        <span class='status-container' id="pp_status" onclick='showProductionLog(this)' data-placement="left" data-tooltip="tooltip" title="${progressedDate}">
                            <span >
                                <span class="progress" style="margin-bottom:6px; display: block;">
                                    <span class="progress-bar progress-bar-success" role="progressbar" id="pp_status_perc" style="width:${completedPPQtyPercentage}%; max-width: 100%">
                                        ${Math.round(completedPPQtyPercentage)}%
                                    </span>
                                   ${updateProgressIcon}
                                </span>
                                <span class='qty-label'>${Number(alternateCompletedQty).toFixed(3)} / ${Number(alternatePPQty).toFixed(3)} ${item.unit_name}</span>
                            </span>
                        </span>
                    </td>
                    <td class="exclude_export schedule-content text-center" data-sort="${item.start_date.replace(/-/g, '')}">
                        ${moment(item.start_date).format("MMM DD, YYYY")} - ${moment(item.end_date).format("MMM DD, YYYY")}
                    </td>
                    <td class="exclude_export assigned-to-content" data-assigned-to-id="${item.assigned_to}">${item.assigned_to}</td>
                    <td class="exclude_export text-center pp-content" data-pp-id='${item.pp_id}'>
                        <form class="hide" action="/erp/stores/materialshortage_pp/" method="post" id="shortageCalPP">
                            <div>
                                <input type="hidden" name="csrfmiddlewaretoken" value="${csrftoken}" />
                                <input type="hidden" name="enterprise_id" value="${enterprise_id}" />
                                <input type="hidden" name="pp_id" value="${item.pp_id}[:]"
                                  <input hidden="hidden" name="include_item_order" id="include_item_order" />
                              <input type="hidden" name="cat_list" id="cat_list" class="form-control" placeholder="Enter Qty" value="${item.item_id}[:]${item.pp_qty}[:]">
                              <input type="hidden" name="pp_no" id="pp_no" class="form-control" placeholder="Enter Qty" value="${item.pp_no}[:]">
                              <input type="hidden" name="from_date" id="from_date" class="form-control" placeholder="Enter Qty">
                              <input type="hidden" name="to_date" id="to_date" class="form-control" placeholder="Enter Qty">
                              <input type="hidden" name="location_id" id="location_id" value="${item.location_id}[:]" />
                            </div>
                        </form>

                        <span class="pp-number">${item.pp_no}</span><br>
                        <span class="table-inline-icon-bg">
                            <span class="pp-date"><small>${moment(item.pp_date).format("MMM DD, YYYY")}</small></span>
                        </span>
                        <span class="table-inline-icon-container">
                            ${editIcon}
                            ${shortageIcon}
                            <span class="table-inline-icon hide" data-tooltip="tooltip" data-placement="left" title="Preview" role="button" onclick="generate_pdf_ajax(${item.pp_id}, ${item.po_type}, ${item.order_status})">
                                <i class="fa fa-file-text"></i>
                            </span>
                            <span class="table-inline-icon remarks-icon hide" data-tooltip="tooltip" data-placement="left" title="Remarks" role="button" onclick="showRemarksModal(this)">
                                <i class="fa fa-quote-right"></i>
                            </span>
                            <span class="table-inline-icon hide" data-tooltip="tooltip" data-placement="left" title="Change Log" role="button" onclick="showChangeLogModal(this)">
                                <i class="fa fa-clock-o"></i>
                            </span>
                            <span class="table-inline-icon hide" data-tooltip="tooltip" data-placement="left" title="Usage Report" role="button" onclick="javascript:generateUsageReport(this, ${item.pp_id});">
                                <i class="fa fa-exchange"></i>
                            </span>
                            ${shortCloseIcons}
                        </span>
                    </td>
                    <td class="hide">${item.mi_no}</td>
                    <td class="hide">${moment(item.mi_date).format("MMM DD, YYYY")}</td>
                    <td class="hide">${item.item_name}</td>
                    <td class="hide">${Number(alternatePPQty).toFixed(3)}</td>
                    <td class="hide">${Number(alternateCompletedQty).toFixed(3)}</td>
                    <td class="hide">${item.unit_name}</td>
                    <td class="hide">${moment(item.start_date).format("MMM DD, YYYY")}</td>
                    <td class="hide">${moment(item.end_date).format("MMM DD, YYYY")}</td>
                    <td class="hide">${item.assigned_to}</td>
                    <td class="hide">${item.pp_no}</td>
                    <td class="hide">${moment(item.pp_date).format("MMM DD, YYYY")}</td>
                </tr>`;
    $("#pp_list tbody").append(row);
}

function createMiOption(current) {
    var currentRow = $(current).closest("tr");
    var option = $('<option/>');
    var mi_id = currentRow.find(".mi-content").attr("data-mi-id");
    var mi_no = currentRow.find(".mi-number").text();
    option.attr({ 'value': mi_id, 'class': 'temp-add' }).text(`${mi_no}`);
    $('#edit_mi_select').append(option);
    $("#edit_mi_select").val(mi_id).trigger("chosen:updated");
    $(".edit_mi_text").text(mi_no)
}

function createItemOption(current) {
    var currentRow = $(current).closest("tr");
    var option = $('<option/>');
    var item_id = currentRow.find(".item-content").attr("data-item-id");
    var make_id = currentRow.find(".item-content").attr("data-make-id");
    var unit_id = currentRow.find(".item-content").attr("data-unit");
    var alternate_unit_id = currentRow.find(".qty-content").attr("data-alternate_unit_id");
    var scale_factor = currentRow.find(".qty-content").attr("data-scale_factor");
    var item_name = currentRow.find(".item-content").text().trim();
    option.attr({
                    'value': item_id,
                    'data-make_id': make_id,
                    'data-qty': 0,
                    'data-unit': unit_id,
                    'data-alternate_unit_id': alternate_unit_id,
                    'data-scale_factor': scale_factor,
                    'class': 'temp-add'
                }).text(`${item_name}`);
    $('#edit_item_select').append(option);
    $("#edit_item_select").val(item_id).trigger("chosen:updated");
    $(".edit_item_text").text(item_name)
}

function enableMiDetails() {
    $(".edit_mi_text").addClass("hide");
    $("#edit_mi_select_chosen").removeClass("hide");
    $(".edit_item_text").addClass("hide");
    $("#edit_item_select_chosen").removeClass("hide");
    $("#edit_mi_select, #edit_item_select").find(".temp-add").remove();
    $("#edit_mi_select, #edit_item_select").trigger("chosen:updated");
    $(".enable-mi-details").addClass("hide");
}

function checkMiNumberAvailability(current) {
    var currentRow = $(current).closest("tr");
    if($("#edit_mi_select").val() == null) {
        createMiOption(current);
        $(".edit_mi_text").removeClass("hide");
        $("#edit_mi_select_chosen").addClass("hide");
        if(currentRow.find(".qty-content").attr("data-status") == "yet_to_start") {
            $(".enable-mi-details").removeClass("hide");
        }
        else {
            $(".enable-mi-details").addClass("hide");
        }
    }
    else {
        $(".edit_mi_text").addClass("hide");
        $("#edit_mi_select_chosen").removeClass("hide");
        $(".enable-mi-details").addClass("hide");
    }
}

function checkItemAvailability(current) {
    if($("#edit_item_select").val() == null) {
        createItemOption(current);
        $(".edit_item_text").removeClass("hide");
        $("#edit_item_select_chosen").addClass("hide");
    }
    else {
        $(".edit_item_text").addClass("hide");
        $("#edit_item_select_chosen").removeClass("hide");
    }
}


function editProductionPlan(current) {
    var currentRow = $(current).closest("tr");
    $("#edit_production_plan .modal-title").text(`Edit PP - ${currentRow.find(".pp-number").text()}`);
    $("#edit_production_plan .modal-title").attr("data-edit-id", currentRow.attr("id"));

    $("#edit_mi_select").val(currentRow.find(".mi-content").attr("data-mi-id")).trigger("chosen:updated");
    checkMiNumberAvailability(current);
    $("#edit_mi_select").trigger("change");

    $("#edit_item_select").val(currentRow.find(".item-content").attr("data-item-id")).trigger("chosen:updated");
    checkItemAvailability(current);
    $("#edit_item_select").trigger("change");

    $("#edit_location_select").val(currentRow.find(".item-content").attr("data-location-id")).trigger("chosen:updated");
    $("#edit_location_select").trigger("change");

    $("#edit_mi_assigned").val(currentRow.find(".assigned-to-content").attr("data-assigned-to-id")).trigger("chosen:updated");
    $("#edit_mi_qty").val(currentRow.find(".qty-content").attr("data-default-qty"));
    $(".mi_max_qty").text(parseFloat(Number($("#edit_mi_qty").val()) + Number($(".mi_max_qty:first").text())).toFixed(3));
    $("#edit_mi_completed_qty").text(currentRow.find(".qty-content").attr("data-completed-qty"));
    $("#edit_unit_display").text(currentRow.find(".qty-content").attr("data-default-unit"));
    var dateRangeValue = currentRow.find(".schedule-content").text();
    setEditDateRangePickerValue(current);
    if(currentRow.find(".qty-content").find('#alloted_status').val() != '0%') {
        $("#edit_mi_select_chosen, #edit_item_select_chosen").addClass("div-disabled");
    }
    else {
        $("#edit_mi_select_chosen, #edit_item_select_chosen").removeClass("div-disabled");
    }
    $("#edit_production_plan").modal("show");
}

function setEditDateRangePickerValue(current) {
    var currentRow = $(current).closest("tr");
    var scheduleDateText = currentRow.find(".schedule-content").text().split(" - ");
    var scheduleStartDate = moment(scheduleDateText[0]).format("YYYY-MM-DD");
    var scheduleEndDate = moment(scheduleDateText[1]).format("YYYY-MM-DD");
    $( "#reportrange_edit #edit_fromdate_add" ).val(scheduleStartDate);
    $( "#reportrange_edit #edit_todate_add" ).val(scheduleEndDate);
    $('#reportrange_edit span').html(currentRow.find(".schedule-content").text());
    $( "#reportrange_edit" ).data('daterangepicker').startDate = moment(scheduleStartDate);
    $( "#reportrange_edit" ).data('daterangepicker').endDate = moment(scheduleEndDate);
    $( "#reportrange_edit" ).data('daterangepicker').updateView();
    $( "#reportrange_edit" ).data('daterangepicker').updateCalendars();
}

var oTable;
var oSettings;
function dataTableInit(){
    oTable = $('#pp_list').DataTable({
        fixedHeader: true,
        "pageLength": 50,
        "bSortCellsTop": true,
        "order": [ 5, "asc" ],
        "search": {
            "smart": false
        }
    });
    oTable.on("draw",function() {
        var keyword = $('#pp_list_filter > label:eq(0) > input').val();
        $('#pp_list').unmark();
        $('#pp_list').mark(keyword,{});
        listTableHoverIconsInit("pp_list");
    });
    oTable.on('page.dt', function() {
      $('html, body').animate({
        scrollTop: $(".dataTables_wrapper").offset().top - 15
       }, 'slow');
    });
    oSettings = oTable.settings();
}

function miAssignedChangeEvent() {
    $("#mi_assigned").change(function(){
        if($("#mi_assigned").val() == "add_new_issue_to") {
            $("#add_issued_to").modal("show");
        }
    });
    $('#add_issued_to').on('hidden.bs.modal', function () {
        if($("#mi_assigned").val() == "add_new_issue_to") {
            $("#mi_assigned").val(0).trigger("chosen:updated");
        }
    });
}

function miAssignedSaveEventInit() {
    $("#add_issued_to_button").click(function() {
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();

        var ControlCollections = [
            {
                controltype: 'textbox',
                controlid: 'id_issued_to',
                isrequired: true,
                errormsg: 'Issue to "Person name" is required.'
            }
        ];
        var result = JSCustomValidator.JSvalidate(ControlCollections);
        if (result) {
            var issue_to_list = [];
            $('#mi_assigned option').each(function () {
                issue_to_list.push($(this).text().toLowerCase())
            });
            if($('#id_issued_to').val() != "") {
                if(issue_to_list.indexOf($('#id_issued_to').val().toLowerCase()) > -1) {
                    $(".duplicate_issued_to").show();
                }
                else {
                    $(".duplicate_issued_to").hide();
                    $(".mi_assigned optgroup[label='Internal']").append(new Option($('#id_issued_to').val(), $('#id_issued_to').val(), true, true));
                    $(".mi_assigned").trigger("chosen:updated");
                    $('#id_issued_to').val("");
                    $('#add_issued_to').modal('hide');
                }
            }
        }
    });
}

function saveReturnItem() {
    po_id=$("#dcUsageReport .modal-title").attr("data-edit-id");
    var material_list = []
    $('#cattable_3 tbody tr').each(function(){
        return_to_qty = $(this).find('.po-return-qty').val();
        if (return_to_qty > 0) {
            item_id = $(this).find(".item-id-content").attr("data-item-id");
            make_id = $(this).find(".make-id-content").attr("data-make-id");
            if (make_id == "undefined" || make_id == ""){
                make_id = "1"
            }
            material = {
                quantity: parseFloat(return_to_qty).toFixed(3),
                item_id: item_id,
                make_id: make_id }
            material_list.push(material)
        }
    });
    var jo_dict = {
                pl_id: "",
                pp_id: po_id,
                mi_id: "",
                mi_no: "",
                mi_date: "",
                inward_date: moment($("#pp_log_date").val()).format("YYYY-MM-DD"),
                items: material_list
        }
    $("#loading").show();
    $.ajax({
        url: "/erp/purchase/json/po/save_return_qty/",
        type: "post",
        datatype: "json",
        data: {jo_detail:JSON.stringify(jo_dict) },
        success:
            function(response){
                if(response.response_message.toLowerCase() == "success" && response.production_log_details != false) {
                    swal("SUCCESS", `IRN has been created successfully<br /><br /><b>Draft No: TMP#IRN-${response.grn_no}</b>`, "success");
                }
                else {
                    swal("Failure", `Due to technical issue, unable to request the process.<br />Please contact support.`, "warning")
                }
                $("#loading").hide();
            },
        error: function (xhr, errmsg, err) {
            checkSessionExpired(xhr);
            $("#loading").hide();
        }
    });
}

function generateUsageReport(current, po_id) {
    var currentRow = $(current).closest("tr");
    jop_po_id = po_id
    $("#cattable_3 tbody tr").remove();
    $("#loadingmessage").show();
    $("#dcUsageReport").modal('show');
    $("#dcUsageReport .modal-title").attr("data-edit-id", currentRow.attr("id"));
    var pp_number = $("#pp_list").find(`tr#${jop_po_id}`).find(".pp-number").text();
    $("#dcUsageReport").find(".modal-title").text("Material Usage - "+ pp_number);
    setTimeout(function(){
        $.ajax({
            url: "/erp/stores/json/dc/job_dc_usage/",
            type: "post",
            datatype: "json",
            data: {po_id:jop_po_id},
            async: false,
            success: function(response){
                if (response.response_code!=400 && response.length != 0){
                    $("#cattable_3").find("tbody").remove();
                    $("#catButton").find('.modal-footer').remove();
                    $.each(response, function (i, item) {
                        if(typeof(Storage) !== "undefined") {
                            sessionStorage.clickcount = (sessionStorage.clickcount ? Number(sessionStorage.clickcount)+1 : 1)
                        }
                        var childArrow = "";
                        var is_service_value = item.is_service==true?1:0;
                        var is_service = '<input hidden="hidden" name="catmaterial_is_service" value='+is_service_value+' >';
                        var itemTypeFlag = "";
                        var material_name = item.name;
                        if (item.material_type==1){
                            classname='no-bg';
                            material_name = childArrow;
                        } else{ classname='tr-alert-danger' }
                        if(item.is_service == true){
                            itemTypeFlag = `<span class="service-item-flag"></span>`;
                        }
                        if(!item.material_type && item.is_service != true) {
                            itemTypeFlag = `<span class="non_stock-flag"></span>`;
                        }
                        if(item.child.length > 0) {
                            childArrow = "<i class='fa fa-plus fa-for-arrow' role='button' onclick='return appendMaterial(\""+ item.item_id +"\", \""+ sessionStorage.clickcount +"\", \""+item.item_id+ "_" + sessionStorage.clickcount +"\", \""+jop_po_id +"\")'><a style='padding-left: 26px; display: block; margin-top: -12px;' >"+item.name+" "+itemTypeFlag+"</a></i>";
                        } else { childArrow = item.name+" "+itemTypeFlag
                        }
                        var toBeSupplied = (item.qty + item.returned) - item.supplied
                        var excessSupplied = (item.supplied - item.returned) - item.qty
                        if (excessSupplied < 0) {
                            excessSupplied = excessSupplied * -1
                        }
                        if (toBeSupplied > 0 ){
                            excessSupplied = 0
                        }
                        if (excessSupplied > 0 ){
                            toBeSupplied = 0
                        }
                        if(item.drawing_no == null ){
                           item.drawing_no = "";
                        }
                        if(item.material_type == 0 && item.is_service != true) {
                           itemTypeFlag += `<span class="non_stock-flag"></span>`;
                        }
                        var makes = "";
                        if (item.makes.length > 0){
                            makes = ``;
                            for (j = 0; j <= item.makes.length - 1 ; j++) {
                                if (item.makes[j][1] == "") { item.makes[j][1] = "-NA-"}
                                makes += `<span> ${item.makes[j][1]}</span>`;
                            }
                        }
                        else{
                            makes = item.make_name;
                        }
                        var description = item.cat_code +" - " +item.drawing_no
                        var row = "<tr data-toggle='close' data-padding='0' data-parent='"+item.item_id+ "_" + sessionStorage.clickcount + "' id=\""+ item.item_id + "_" + sessionStorage.clickcount +
                                            "\" bgcolor='#ececec' border='1' align='left' style='font-size:12px; font-weight:normal;'>"+
                                        "<td hidden=hidden class='exclude_export item-id-content' data-item-id ='"+item.item_id+"'> "+item.item_id+" </td>" +
                                        "<td hidden=hidden class='exclude_export make-id-content' data-make-id ='"+item.make_id+"'> "+item.make_id+" </td>" +
                                        "<td hidden=hidden class='exclude_export'>" + description +"</td>"+
                                        "<td class='text-center bom-sno'>" + (i+1) + "</td>"+
                                        "<td width='59%'>"+ childArrow +"</td>"+
                                        "<td width='5%' class='bom-expected-qty'>" + item.qty.toFixed(3) + "</td>"+
                                        "<td width='5%' class='bom-supplied-qty'>" + item.supplied.toFixed(3) + "</td>"+
                                        "<td width='5%' class='bom-returned-qty'>" + item.returned.toFixed(3) + "</td>"+
                                        "<td width='5%' class='bom-toBeSupplied-qty'>" + toBeSupplied.toFixed(3) + "</td>"+
                                        "<td width='5%' class='bom-excessSupplied-qty'>" + excessSupplied.toFixed(3) + "</td>"+
                                        "<td width='10%' class='exclude_export bom-return-to-qty'>"+"<input type='textbox' name='returnQty' value=0 class='po-return-qty form-control'>"+"</td>" +
                                    "</tr   >";
                        $('#cattable_3').append(row).addClass('tbl');
                    });
                    $("#loadingmessage").hide();
                    TooltipInit();
                }
            }
        });
    },200);
}

function appendMaterial(cat_code, dataChild, dataP, jop_po_id) {
    var currentCatChild = $("#"+cat_code + "_" + dataChild).attr('id');
    var current_bom_sno = $("#"+cat_code + "_" + dataChild).find(".bom-sno").text().trim();
    var constructedRow = '';
    var currentRow = $("#cattable_3").find(`tr#${cat_code}_${dataChild}`);
    if($("#"+currentCatChild).attr('data-toggle') == 'open') {
        $(currentRow).removeClass("strikeout");
        $("#return_to_qty").addClass('hide');
        var curPadding = $("#"+currentCatChild).attr('data-padding');
        $("#"+currentCatChild).nextAll('tr').each(function(){
            if(Number($(this).attr('data-padding')) > Number(curPadding)) {
                $(this).remove();
            }
            else {
                return false;
            }
        });
        $("#cattable_3 #"+cat_code + "_" + dataChild).find('.fa.fa-for-arrow').removeClass('fa-minus').addClass('fa-plus');
        $("#cattable_3 #"+cat_code + "_" + dataChild).attr('data-toggle','close');
    }
    else {
        $("#cattable_3 #"+cat_code + "_" + dataChild).find('.fa.fa-for-arrow').removeClass('fa-plus').addClass('fa-minus');
        $("#cattable_3 #"+cat_code + "_" + dataChild).attr('data-toggle','open');
        $(currentRow).addClass("strikeout");
        $.ajax({
            url: "/erp/stores/json/catalogue_materials/",
            type: "post",
            dataType: "json",
            data: {'cat_code': cat_code},
            success: function (response) {
                $.each(response, function (i, item) {
                var currentRow = $("#cattable_3").find(`tr#${cat_code}_${dataChild}`);
                var baseExpectedQty = currentRow.find(".bom-expected-qty").text();
                var suppliedQty = 0
                var returnedQty = 0
                var toBeSuppliedQty = 0
                var excessSuppliedQty = 0
                if(typeof(Storage) !== "undefined") {
                    if (sessionStorage.clickcount) {
                        sessionStorage.clickcount = Number(sessionStorage.clickcount)+1;
                    } else {
                        sessionStorage.clickcount = 1;
                    }
                }
                $.ajax({
                    url: "/erp/purchase/json/po/get_dc_materials/",
                    type: "post",
                    dataType: "json",
                    async: false,
                    data: {'po_id': jop_po_id, 'item_id': item.item_id},
                    success: function (response) {
                        suppliedQty = response["supplied_qty"];
                        returnedQty = response["returned_qty"];
                    }
                });
                var childArrow ="";
                var makes = "-NA-";
                var dataParent = dataP;
                var dataPadding = Number($("#"+cat_code + "_" + dataChild).data('padding') + 30);
                var drawing_no = "";
                var isStockableIcon = "";
                var isServiceIcon = "";
                var itemDescription = item.name;
                var is_service = '<input hidden="hidden" name="catmaterial_is_service" value='+item.is_service+' >';

                if (item.drawing_no !="" && item.drawing_no!=null) {
                    drawing_no = item.drawing_no;
                }

                if(item.is_service == true){
                    isServiceIcon = `<span class="service-item-flag"></span>`;
                }
                if(!item.material_type && !item.is_service){
                    isStockableIcon = `<span class="non_stock-flag"></span>`;
                }
                if (item.make_name != ""){
                    itemDescription += " [" + item.make_name + "]";
                }
                if(item.hasChildren) {
                    childArrow = "<i class='fa fa-plus fa-for-arrow' role='button' onclick='return appendMaterial(\""+ item.item_id +"\", \""+ sessionStorage.clickcount +"\", \""+ dataParent +"\", \""+jop_po_id +"\")'><a style='padding-left: 26px; display: block; margin-top: -12px;'>"+itemDescription+" "+isServiceIcon+" "+isStockableIcon+"</a></i>";
                } else {
                    childArrow = itemDescription+" "+isServiceIcon+" "+isStockableIcon;
                }
                if (item.makes.length > 0){
                    makes = "<select class='form-control' name='make' id='txtmake" + i + "'>";
                    for (j = 0; j <= item.makes.length - 1 ; j++) {
                        makes = `${makes}<option value="${item.makes[j].make_id}" data-quantity="${item.makes[j].stock}">${item.makes[j].make_name}</option>`;
                    }
                    makes += "</select>";
                }
                if(item.drawing_no != null){
                    var item_name = item.drawing_no + " - " + item.name
                }else{
                    var item_name = item.name
                }
                var is_service_value = item.is_service==true?1:0;
                var description = item.cat_code +" - " +item.drawing_no
                baseExpectedQty = item.quantity * baseExpectedQty
                toBeSuppliedQty = (baseExpectedQty) + returnedQty - suppliedQty
                excessSuppliedQty = (suppliedQty>(baseExpectedQty) ? (baseExpectedQty) - suppliedQty : 0)
                var row = "<tr data-toggle='close' data-padding='"+dataPadding+"' data-parent='"+item.item_id+ "_" + sessionStorage.clickcount + "' id=\""+ item.item_id + "_" + sessionStorage.clickcount +
                                            "\" bgcolor='#ececec' border='1' align='left' style='font-size:12px; font-weight:normal;'>"+
                                        "<td hidden=hidden class='exclude_export item-id-content' data-item-id ='"+item.item_id+"'> "+item.item_id+" </td>" +
                                        "<td hidden=hidden class='exclude_export make-id-content' data-make-id ='"+item.make_id+"'> "+item.make_id+" </td>" +
                                        "<td hidden=hidden class='exclude_export'>" + description +"</td>"+
                                        "<td class='text-center bom-sno'>" +current_bom_sno+ "."+(i+1) + "</td>"+
                                        "<td width='59%' style='padding-left:"+Number(dataPadding-20)+"px'><span style='padding-left:30px; display: block;' class='tree-view'>"+ childArrow +"</span></td>"+
                                        "<td width='5%' class='bom-expected-qty'>" + (baseExpectedQty).toFixed(3) + "</td>"+
                                        "<td width='5%' class='bom-supplied-qty'>" + (suppliedQty).toFixed(3) + "</td>"+
                                        "<td width='5%' class='bom-returned-qty'>" + (returnedQty).toFixed(3) + "</td>"+
                                        "<td width='5%' class='bom-toBeSupplied-qty'>" + (toBeSuppliedQty).toFixed(3) + "</td>"+
                                        "<td width='5%' class='bom-excessSupplied-qty'>" + (excessSuppliedQty).toFixed(3) + "</td>"+
                                        "<td width='10%' class='exclude_export bom-return-to-qty'>"+"<input type='textbox' name='returnQty' value=0 class='po-return-qty form-control'>"+"</td>" +

                                    "</tr>";
                 constructedRow = constructedRow+row
                });
                $('#cattable_3 #'+cat_code + "_" + dataChild).after(constructedRow);
            }
        });
    }
}
function saveProductionPlan(type, current="") {
    var validateProductionPlan = validateProductionPlanField(type);

    if(validateProductionPlan) {
        var productionPlanObj = generateProductionPlanObj(type);
        $("#loading").show();
        $.ajax({
            url: "/erp/production/save_prod_plan/",
            type: "POST",
            dataType: "json",
            data: { 'production_plan': productionPlanObj },
            success: function (response) {
                var response_plan_details = response.production_plan_details[0];
                var make_name = constructDifferentMakeName(response_plan_details.make_name);
                var itemDescription = response_plan_details.item_name;
                if (make_name != ""){
                  itemDescription += " [" + make_name + "]";
                }
                response_plan_details.item_name =itemDescription;
                if(response.response_message.toLowerCase() == "success") {
                    if(typeof(oTable) !== 'undefined') {
                        oTable.destroy();
                    }
                    if(type == "edit") {
                        $(`tr#${response_plan_details.pp_id}`).remove();
                        var messageType = "Updated";
                    }
                    else {
                        var messageType = "Created";
                    }
                    createPpListRow(response_plan_details);
                    dataTableInit();
                    listTableHoverIconsInit("pp_list");
                    resetMiDetails();

                    swal({
                        title: '<span style="color: #44ad6b;">SUCCESS</span>',
                        text: `Production Plan has been ${messageType} successfully.<br /> <br />Production Plan No.: <b>${response_plan_details.pp_no}</b>`,
                        type: "success"
                    });
                    $("#edit_production_plan").modal("hide");
                    getManufactureIndentList();
                    $("#loading").hide();
                }
                else {
                    swal("Failure", `Due to technical issue, unable to ${messageType} Production Plan. <br />Please contact support.`, "warning");
                    $("#loading").hide();
                }
            },
            error: function (xhr, errmsg, err) {
                checkSessionExpired(xhr);
            }
        });
    }
}

function validateProductionPlanField(type) {
    $(".error-border").removeClass('error-border');
	$(".custom-error-message").remove();
	if(type == "add") {
	    var validatorField = ['mi_select', 'item_select', 'mi_qty', 'mi_assigned','location_select'];
	    var mi_max_qty = Number($("#mi_max_qty").text());
	    var mi_completed_qty = Number($("#mi_completed_qty").text());
	    var mi_qty = Number($("#mi_qty").val());
	}
	else {
	    var validatorField = ['edit_mi_select', 'edit_item_select', 'edit_mi_qty', 'edit_mi_assigned','edit_location_select'];
	    var mi_max_qty = Number($("#edit_mi_max_qty").text());
	    var mi_completed_qty = Number($("#edit_mi_completed_qty").text());
	    var mi_qty = Number($("#edit_mi_qty").val());
	}
	var ControlCollections = [
		{
			controltype: 'dropdown',
			controlid: validatorField[0],
			isrequired: true,
			errormsg: 'MI Number is required.'
		},
		{
			controltype: 'dropdown',
			controlid: validatorField[1],
			isrequired: true,
			errormsg: 'Item is required.'
		},
		{
			controltype: 'textbox',
			controlid: validatorField[2],
			isrequired: true,
			errormsg: 'Quantity is required.',
			mindigit: 0.001,
            mindigiterrormsg: 'Quantity is required.'
		},
		{
			controltype: 'dropdown',
			controlid: validatorField[3],
			isrequired: true,
			errormsg: 'Assigned to is required.'
		},
		{
			controltype: 'dropdown',
			controlid: validatorField[4],
			isrequired: false,
			errormsg: 'Location is required.'
		},
	];
	var locationSelector = $("#" + validatorField[3]);
	var selectedLocation = locationSelector.find("option:selected");
	var selectedOptGroup = selectedLocation.closest("optgroup").attr("label");
	if(selectedOptGroup === "Internal"){
	    ControlCollections[4].isrequired = true;
	}

	var result = JSCustomValidator.JSvalidate(ControlCollections);
	if(result) {
	    if(mi_qty > mi_max_qty) {
	        swal("Max Quantity Exceeded","Production Plan Quantity cannot exceed the Manufacturing Indent Quantity. ","warning");
	        result = false;
	    }
	    else if(mi_completed_qty > mi_qty) {
	        swal("PP Quantity Exceeded",`Cannot update the Production Plan Quantity below the Completed Quantity<br /><br /><b>Completed Qty: ${$("#edit_mi_completed_qty").text()}</b>`,"warning");
	        result = false;
	    }
	}
	return result;
}

function generateProductionPlanObj(type) {
    var idPrefix = "";
    if(type == "edit") {
        idPrefix = 'edit_';
        var pp_id = $("#edit_production_plan .modal-title").attr("data-edit-id");
    }
    else if(type == "add") {
        var pp_id = "";
    }
    var productionPlan = [
       {
           pp_id: pp_id,
           mi_id: $(`#${idPrefix}mi_select`).val(),
           item_id: $(`#${idPrefix}item_select`).val(),
           item_name: $(`#${idPrefix}item_select option:selected`).text(),
           quantity: $(`#${idPrefix}mi_qty`).val(),
           completed_quantity: $(`#${idPrefix}mi_completed_qty`).text(),
           start_date: $(`#${idPrefix}fromdate_add`).val(),
           end_date: $(`#${idPrefix}todate_add`).val(),
           assigned_to: $(`#${idPrefix}mi_assigned`).val(),
           make_id: $(`#${idPrefix}item_select option:selected`).attr("data-make_id"),
           unit_name: $(`#${idPrefix}item_select option:selected`).attr("data-unit"),
           alternate_unit_id: $(`#${idPrefix}item_select option:selected`).attr("data-alternate_unit_id"),
           pp_type: $(`#${idPrefix}mi_assigned option:selected`).closest("optgroup").attr("label").toLowerCase(),
           location_id: $(`#${idPrefix}location_select`).val(),
       }
    ];
    return JSON.stringify(productionPlan);
}

function shortCloseConfirmationEvt(current) {
    var currentRow = $(current).closest("tr");
    var ppNumber = currentRow.find(".pp-content").find(".pp-number").text();
    var progress = currentRow.find("#pp_status_perc").text().trim();
    var alloted_status = currentRow.find("#alloted_status").text().trim();
    if((progress == "0%" && alloted_status == "0%") || (alloted_status == "0%" && progress != "0%")){
        swal({
            title: "Are You Sure!",
            text: `You are about to discontinue Plan <b>${ppNumber}</b> that has <b>${progress}</b> Progress.?`,
            type: "input",
            showCancelButton: true,
            closeOnConfirm: false,
            inputPlaceholder: "Enter Short Close Remarks"
        }, function (remarks) {
            if (remarks === false) return false;
            if (remarks === "") {
                swal.showInputError("Remarks is required");
                return false
            }
            shortCloseEvent(current, remarks);
        });
    }
    else if(alloted_status != "0%" && progress == "0%"){
        swal({title: "", text: `<b>${alloted_status}</b> materials are allocated for this <b>${ppNumber}</b><br/>You have to Deallocate the materials for short close this <b>${ppNumber}</b> `, type: "error"});
    }
    else{
        swal({title: "", text: `<b>${alloted_status}</b> materials are allocated for this <b>${ppNumber}</b><br/>You have to Deallocate required materials for short close this <b>${ppNumber}</b> `, type: "error"});
    }
}

function shortCloseEvent(current, remarks) {
    var currentRow = $(current).closest("tr");
    var ppNumber = currentRow.find(".pp-content").find(".pp-number").text();
    var pp_id = currentRow.find(".pp-content").attr("data-pp-id");
    var shortCloseData = [
       {
           pp_id: pp_id,
           item_id: currentRow.find(".item-content").attr("data-item-id"),
           make_id: currentRow.find(".item-content").attr("data-make-id"),
           quantity: currentRow.find(".qty-content").attr("data-completed-qty"),
           remarks: "<b><i>Short Close Remarks:</i></b> "+remarks,
           qty:remarks
       }
    ];
    $("#loading").show();
    $.ajax({
        url: "/erp/production/short_close_prod_plan/",
        type: "POST",
        dataType: "json",
        data: { 'pp_dict': JSON.stringify(shortCloseData) },
        success: function (response) {
            if(response.response_message.toLowerCase() == "success" && response.pp_details != false) {
                reInitializePpDetails(pp_id, response.pp_details[0])
                swal({
                    title: '<span style="color: #44ad6b;">SUCCESS</span>',
                    text: `Production Plan <b>${ppNumber}</b> has been short closed successfully.`,
                    type: "success"
                });
            }
            else {
                swal("Failure", `Due to technical issue, unable to request the process.<br />Please contact support.`, "warning")
            }
            $("#loading").hide();
        },
        error: function (xhr, errmsg, err) {
            checkSessionExpired(xhr);
            $("#loading").hide();
        }
    });
}

function showChangeLogModal(current) {
    var currentRow = $(current).closest("tr");
    $("#change_log_modal").modal("show");
    $("#change_log_modal .modal-title").text(`Activity Log - ${currentRow.find(".pp-number").text()}`);

    var ppNumber = currentRow.find(".pp-content").find(".pp-number").text();
    var pp_id = currentRow.find(".pp-content").attr("data-pp-id");
    changeLogActivityInit(pp_id);
}

function showRemarksModal(current) {
    $(".error-border").removeClass('error-border');
	$(".custom-error-message").remove();
	$("#id_pp-remarks").val("");

    loadItemRemarks(current);
    loadItemInstruction(current);
    $("#pp_remarks_modal").modal("show");
    var currentRow = $(current).closest("tr");
    $("#pp_remarks_modal .modal-title").text(`Remarks - ${currentRow.find(".pp-number").text()}`);
    $("#pp_remarks_modal .modal-title").attr("data-edit-id", currentRow.attr("id"));
}

function loadItemInstruction(current) {
    var instruction = $(current).closest("tr").find(".item_instruction").val();
    $("#id_pp-instruction").val(instruction);
}

function loadItemRemarks(current) {
    var remarks = $(current).closest("tr").find(".item_remarks").val();
    remarkList = JSON.parse(remarks);
    if (remarkList != null && remarkList.length > 0) {
        var history = '<div class="chat-container">';
        $.each(remarkList, function(i, remark) {
            var sDate = moment(remark.date).format('MMM D, YYYY');
            if(sDate == 'Invalid date') sDate =' ';
            history += ` <div class="chat-list">
                            <span class="chat-list-name">${remark.by}</span>
                            <span class="chat-list-date">${sDate}</span>
                            <span class="chat-list-description">${remark.remarks}</span>
                        </div>`;
        });
        history += '</div>';
        $(".remarks-content-container").html(history);
    }
    else {
        var history = `<div class="chat-container">
                            <div class="chat-list text-center">
                                <span class="chat-list-name">No Remarks</span>
                            </div>
                        </div>`;
        $(".remarks-content-container").html(history);
    }
}

function saveNewRemarks() {
    $(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();

    var ControlCollections = [
        {
            controltype: 'textbox',
            controlid: 'id_pp-remarks',
            isrequired: true,
            errormsg: 'Remarks is required.'
        }
    ];
    var result = JSCustomValidator.JSvalidate(ControlCollections);
    if (result) {
        var remarksData = [
           {
               pp_id: $("#pp_remarks_modal .modal-title").attr("data-edit-id"),
               remarks: $("#id_pp-remarks").val(),
           }
        ];
        $("#loading").show();
        $.ajax({
            url: '/erp/production/remarks_prod_plan/',
            type: "POST",
            dataType: "json",
            data: { 'pp_dict': JSON.stringify(remarksData) },
            success: function (response) {
                if(response.response_message.toLowerCase() == "success") {
                    swal({
                        title: '<span style="color: #44ad6b;">SUCCESS</span>',
                        text: `Remarks Added Successfully`,
                        type: "success"
                    });
                    var updatedRemarks = JSON.stringify(response.remarks)
                    $(`#pp_list tr#${remarksData[0]['pp_id']}`).find(".item_remarks").val(updatedRemarks);
                    var currentEle = $(`#pp_list tr#${remarksData[0]['pp_id']}`).find(".remarks-icon");
                    loadItemRemarks(currentEle);
                    $("#id_pp-remarks").val("");
                }
                else {
                    swal("Failure", `Due to technical issue, unable to request the process.<br />Please contact support.`, "warning")
                }
                $("#loading").hide();
            },
            error: function (xhr, errmsg, err) {
                checkSessionExpired(xhr);
                $("#loading").hide();
            }
        });
    }
}

function saveInstructions() {
    $(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();

    var ControlCollections = [
        {
            controltype: 'textbox',
            controlid: 'id_pp-instruction',
            isrequired: true,
            errormsg: 'Instruction is required.'
        }
    ];
    var result = JSCustomValidator.JSvalidate(ControlCollections);
    if (result) {
        var pp_id = $("#pp_remarks_modal .modal-title").attr("data-edit-id");
        var pp_instruction = $("#id_pp-instruction").val();
        var remarksData = [
           {
               pp_id: pp_id,
               pp_instruction: pp_instruction
           }
        ];
        $("#loading").show();
        $.ajax({
            url: '/erp/production/instruction_prod_plan/',
            type: "POST",
            dataType: "json",
            data: { 'pp_dict': JSON.stringify(remarksData) },
            success: function (response) {
                if(response.response_message.toLowerCase() == "success") {
                    swal({
                        title: '<span style="color: #44ad6b;">SUCCESS</span>',
                        text: `Instruction Added Successfully`,
                        type: "success"
                    });
                    $(`#pp_list tr#${pp_id}`).find(".item_instruction").val(pp_instruction);
                }
                else {
                    swal("Failure", `Due to technical issue, unable to request the process.<br />Please contact support.`, "warning")
                }
                $("#loading").hide();
            },
            error: function (xhr, errmsg, err) {
                checkSessionExpired(xhr);
                $("#loading").hide();
            }
        });
    }
}

function showProductionLog(current) {
    $(".error-border").removeClass('error-border');
	$(".custom-error-message").remove();
    var currentRow = $(current).closest("tr");
    var pp_id = currentRow.find(".pp-content").attr("data-pp-id");
    var pp_max_qty = currentRow.find(".qty-content").attr("data-default-qty");
    var pp_unit = currentRow.find(".qty-content").attr("data-default-unit");
    var pp_item_name = currentRow.find(".item-content").text().trim();
    var po_type = currentRow.find(".qty-content").attr("data-po_type")
    if(po_type == 2){
        $(".add-pp-log").removeClass('hide')
        $(".pp_log_qty").removeClass('disabled')
    }else{
        $(".add-pp-log").addClass('hide')
        $(".pp_log_qty").addClass('disabled')
    }
    $("#pp_production_log").modal("show");
    $("#pp_production_log .modal-title").attr({ "data-max-qty": pp_max_qty });
    var pp_details_log = [{
                            "pp_id": pp_id,
                            "mi_id": currentRow.find(".mi-content").attr("data-mi-id"),
                            "mi_no": currentRow.find(".mi-number").text(),
                            "mi_date": currentRow.find(".mi-date").text(),
                            "item_id": currentRow.find(".item-content").attr("data-item-id"),
                            "make_id": currentRow.find(".item-content").attr("data-make-id"),
                            "alternate_unit_id": currentRow.find(".qty-content").attr("data-alternate_unit_id"),
                            "scale_factor": currentRow.find(".qty-content").attr("data-scale_factor")
                        }];
    $("#pp_production_log .pp_details_log").val(JSON.stringify(pp_details_log))
    $("#pp_production_log .modal-title").html(`Production Log - ${currentRow.find(".pp-number").text()}`);
    $("#pp_production_log .modal-description").html(`${pp_item_name} <small>(${pp_max_qty} ${pp_unit})</small>`);
    $(".pp-log-unit").text(pp_unit);
    getProductionLogDetails(pp_id, po_type);
    resetProductionLogFields();
}

async function getProductionLogDetails(ppId, po_type) {
    const locationListAll = await getLocations();
    const fromLocation = "from";
    locationList = locationListAll[fromLocation]
    $.ajax({
        url: '/erp/production/get_prod_log/',
        type: "POST",
        dataType: "json",
        data: { 'pp_id': ppId },
        success: function (response) {
            $("#pp_log_list tbody").empty();
            if(response.production_log_details.length > 0) {
                var pp_permission = "";
                if($("#pp_permission").val().toLowerCase() != "true" && $("#stores_permission").val().toLowerCase() != "true") {
                    pp_permission = "disabled";
                }
                var pp_log_qty_permission = ""
                if(po_type==1){
                    pp_log_qty_permission = "disabled";
                }

                $.each(response.production_log_details, function(i, item) {
                    var pp_status = "";
                    if(item.status >= 1 && $("#stores_permission").val().toLowerCase() != "true") {
                        pp_status = "disabled";
                    }
                    var alternateUnitQty = item.quantity / item.scale_factor;
                    var row = `<tr data-id="${item.pl_id}">
                                    <td>${moment(item.inward_date).format("MMM DD, YYYY")}</td>
                                    <td>
                                        <input type="text" class="form-control pp_log_qty text-right" value="${alternateUnitQty.toFixed(3)}"
                                            data-default-value="${alternateUnitQty.toFixed(3)}"
                                            onkeyup="togglePpLogUpdateButton(this)"
                                            onfocus="setNumberRangeOnFocus(this,12,3)" ${pp_log_qty_permission} ${pp_permission} ${pp_status} />
                                        <div class="location_update_button hide" style="margin: 10px 0px 10px 0px;" data-name="prod-log-location">
                                            <select class="form-control location_select_dropdown" id="prod_log_location-${i}" name="prod-log-location">
                                            </select>
                                        </div>
                                        <span class="qty_update_button hide">
                                            <button class="btn btn-save" data-tooltip="tooltip" title="Update Qty" onclick="saveProductionLog('edit', this)">
                                                <i class="fa fa-check" aria-hidden="true"></i>
                                            </button>
                                            <button class="btn btn-delete" data-tooltip="tooltip" title="Cancel " onclick="restoreDefaultQuantity(this)">
                                                <i class="fa fa-close" aria-hidden="true" style="color: #FFF;"></i>
                                            </button>
                                        </span>
                                    </td>
                                    <td class='text-center'>
                                        <a role="button" onclick="editReceipt(${item.pl_id}, '${item.rec_against}', '_blank')">${item.pl_no}</a>
                                    </td>
                                </tr>`;
                    $("#pp_log_list tbody").append(row);
                    const locationSelect = $(`#prod_log_location-${i}`);
                    locationList.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = location.name;
                    option.selected = location.id === item.location_id;
                    locationSelect.append(option);
                });
                });
                TooltipInit();
            }
            else {
                var row = `<tr class="empty-notification">
                                <td class="text-center" colspan="3">No Log Recorded</td>
                            </tr>`;
                $("#pp_log_list tbody").append(row);
            }
            if(po_type == 1) {
                $(".receipt_type_name").text("GRN No");
            }
            else {
                $(".receipt_type_name").text("IRN No");
            }
        },
        error: function (xhr, errmsg, err) {
            checkSessionExpired(xhr);
        }
    });
}

function restoreDefaultQuantity(current) {
    var currentTr = $(current).closest("tr");
    var default_quantity = currentTr.find(".pp_log_qty").attr("data-default-value");
    currentTr.find(".pp_log_qty").val(default_quantity);
    currentTr.find(".qty_update_button").addClass('hide');
    currentTr.find(".location_update_button").addClass('hide');
}

function saveProductionLog(type, current="") {
    var checkProductionLogQuantity = validateProductionLogQuantity(type);
    if(checkProductionLogQuantity) {
        var material_list = []
        var pp_details_log = $("#pp_production_log .pp_details_log").val();
        pp_details_log = JSON.parse(pp_details_log)[0];
        var pp_id = pp_details_log.pp_id;
        var currentRow = $(`#pp_list tr#${pp_id}`);
        var po_type = currentRow.find(".qty-content").attr("data-po_type")
        var pl_id = "";
        if(type == "edit") {
            pl_id = $(current).closest("tr").attr("data-id");
            var pl_quantity = $(current).closest("tr").find(".pp_log_qty").val()
            var pl_location = $(current).closest("tr").find(".location_select_dropdown").val()
        }
        else {
            var pl_quantity = $("#pp_log_qty").val();
            var pl_location = $("#id_prod-log-location").val();
        }
        material = {
            quantity: pl_quantity,
            item_id: currentRow.find(".item-content").attr("data-item-id"),
            make_id: currentRow.find(".item-content").attr("data-make-id"),
            alternate_unit_id: currentRow.find(".qty-content").attr("data-alternate_unit_id"),
            location_id : pl_location
        }
        material_list.push(material)

        var production_log_details = {
            pl_id: pl_id,
            pp_id: pp_id,
            mi_id: pp_details_log.mi_id,
            mi_no: pp_details_log.mi_no,
            mi_date: pp_details_log.mi_date,
            inward_date: moment($("#pp_log_date").val()).format("YYYY-MM-DD"),
            items: material_list
        }
        $("#loading").show();
        $.ajax({
            url: '/erp/production/save_prod_log/',
            type: "POST",
            dataType: "json",
            data: { 'production_plan': JSON.stringify(production_log_details) },
            success: function (response) {
                if(response.response_message.toLowerCase() == "success" && response.production_log_details != false) {
                    var messageType = (type == "edit") ? "Updated": "Created";
                    swal("SUCCESS", `IRN has been ${messageType} successfully`, "success");
                    resetProductionLogFields();
                    getProductionLogDetails(pp_id, po_type);
                    getPpDetails(pp_id);
                }
                else {
                    swal("Failure", `Due to technical issue, unable to request the process.<br />Please contact support.`, "warning")
                }
                $("#loading").hide();
            },
            error: function (xhr, errmsg, err) {
                checkSessionExpired(xhr);
                $("#loading").hide();
            }
        });
    }
}

function resetProductionLogFields() {
    $("#pp_log_date").datepicker("setDate", new Date());
    $("#pp_log_qty").val("");
    $("#id_prod-log-location").val("").trigger("chosen:updated");
}

function validateProductionLogQuantity(type) {
    if(type == "add") {
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();
        var ControlCollections = [
            {
                controltype: 'textbox',
                controlid: 'pp_log_qty',
                isrequired: true,
                errormsg: 'Quantity is required.',
                mindigit: 0.001,
                mindigiterrormsg: 'Quantity is required.'
            },
            {
                controltype: 'dropdown',
                controlid: 'id_prod-log-location',
                isrequired: true,
                errormsg: 'Location is required.'
            }
        ];
        var result = JSCustomValidator.JSvalidate(ControlCollections);
    }
    else {
        var result = true;
    }
    if (result) {
        var pp_max_qty = $("#pp_production_log .modal-title").attr("data-max-qty");
        var loggedQty = 0;
        $("#pp_log_list .pp_log_qty").each(function(){
            loggedQty += Number($(this).val());
        });
        if(loggedQty > pp_max_qty) {
             swal("Max Quantity Exceeded",`Total Logged Quantity cannot exceed the Production Plan Quantity. <br /><br /><b>PP Qty: ${pp_max_qty}</b>`,"warning");
             return false;
        }
        else {
            return true;
        }
    }
    else {
        return false;
    }
}

function getPpDetails(ppId) {
    $.ajax({
        url: '/erp/production/get_prod_plan/',
        type: "POST",
        dataType: "json",
        data: { 'pp_id': ppId },
        success: function (response) {
            if(response.response_message.toLowerCase() == "success" && response.production_plan_details != false) {
                reInitializePpDetails(ppId, response.production_plan_details[0]);
            }
            else {
                swal("Failure", `Due to technical issue, unable to request the process.<br />Please contact support.`, "warning")
            }
        },
        error: function (xhr, errmsg, err) {
            checkSessionExpired(xhr);
        }
    });
}

function reInitializePpDetails(ppId, item) {
    if(typeof(oTable) !== 'undefined') {
        oTable.destroy();
    }
    $(`#pp_list tr#${ppId}`).remove();
    createPpListRow(item);
    dataTableInit();
    listTableHoverIconsInit("pp_list");
}

function productionLogModalCloseEvent() {
    $('#pp_production_log').on('hidden.bs.modal', function () {
        $("#pp_production_log .modal-title").removeAttr("data-pp-id data-mi-id data-max-qty").text("");
        $("#pp_log_list tbody").empty();
        resetProductionLogFields();
    });

    $('#edit_production_plan').on('hidden.bs.modal', function () {
        resetMiDetails();
        $("#edit_mi_select, #edit_item_select").find(".temp-add").remove();
        $("#edit_mi_select, #edit_item_select").chosen();
    });
}

function togglePpLogUpdateButton(current) {
    var currentRow = $(current).closest("tr");
    var default_quantity = currentRow.find(".pp_log_qty").attr("data-default-value");
    var enteredQuantity = currentRow.find(".pp_log_qty").val();
    if(Number(default_quantity) != Number(enteredQuantity)) {
        currentRow.find('.qty_update_button').removeClass("hide");
        currentRow.find('.location_update_button').removeClass("hide");
    }
    else {
        currentRow.find('.qty_update_button').addClass("hide");
        currentRow.find('.location_update_button').addClass("hide");
    }
}

function shoMoreLogs(offset, limit, pp_id){
	if($("#change_log_modal").hasClass("change_log")) {
		var po_id = pp_id;
		$.ajax({
			url: '/erp/purchase/json/po/getpologlist/',
			type: "POST",
			dataType: "json",
			data: {'po_id': pp_id, offset: offset, limit: limit},
			success: function (data) {
			if (data['response_code'] != 400){
				x = data['data'];
				var row = "";
				var i2 = offset;
				for(var i = 0; i < x.length; i++) {
					var obj = x[i].log;
					var row = `<li class="history-log-part" id="history-part-${i2}" onclick="loadHistoryContent('${data['data'][0].po_id}', '${obj.modified_at}', ${i2})">
									<span class="history-log-username">${obj.username[0]+" "+obj.username[1]}</span>
									<div class="history-log-content" style="display: none;"></div>
									<span class="history-log-date">${moment(obj.modified_at).format("MMM DD, YYYY hh:mm:ss a")}</span>
								</li>`;

					$(".history-log-container").append(row);
					var displayedCount = $(".history-log-container").find("li").length;
					i2++;
					if(x.length < 20){
						$(".show-more-log").addClass("hide");
					}
					else {
						$(".show-more-log").find("li").attr("onclick", "shoMoreLogs("+displayedCount+", 20)");
					}
				}
			}
			else {
				$(".show-more-log").addClass("hide");
			}
			},
			error: function ($xhr,textStatus,errorThrown) {
				console.log("ERROR : ", $xhr);
				checkSessionExpired($xhr);
			}
		});
	}
}

function changeLogActivityInit(po_id){
	if (po_id != ""){
		$("#change_log_modal").addClass("change_log").removeClass("change_preference_log");
		$("#change_log_modal").modal("show");
		$("#loadingmessage_changelog_listing_ie").show();
		$(".history-log-container").html("");
		$.ajax({
			url: '/erp/purchase/json/po/getpologlist/',
			type: "POST",
			dataType: "json",
			data: {'po_id': po_id, 'offset': 0, 'limit': 20},
			success: function (data) {
			if (data['response_code'] != 400){
				x = data['data'];
				var row = "";
				for(var i = 0; i < x.length; i++) {
					var obj = x[i].log;
					var row = `<li class="history-log-part" id="history-part-${i}" onclick="loadHistoryContent('${data['data'][0].po_id}', '${obj.modified_at}', ${i})">
									<span class="history-log-username">${obj.username[0]+" "+obj.username[1]}</span>
									<div class="history-log-content" style="display: none;"></div>
									<span class="history-log-date">${moment(obj.modified_at).format("MMM DD, YYYY hh:mm:ss a")}</span>
								</li>`;
					$("#loadingmessage_changelog_listing_ie").hide();
					$(".history-log-container").append(row);
					var displayedCount = $(".history-log-container").find("li").length;
				}
					if(x.length < 20){
						$(".show-more-log").addClass("hide");
					}
					else {
						$(".show-more-log").removeClass("hide");
						$(".show-more-log").find("li").attr("onclick", "shoMoreLogs("+displayedCount+", 20)");
					}
			}
			else {
				var row = `<span style="width: 100%; padding: 15px; text-align:center; display: block;">No Activity Log has been captured.</span>`;
				$("#loadingmessage_changelog_listing_ie").hide();
				$("#change_log_modal .modal-body").html(row);
			}
			},
			error: function ($xhr,textStatus,errorThrown) {
				console.log("ERROR : ", $xhr);
				if ($xhr.responseText.search("Session Expired!") != -1){
					location.reload();
				}
			}
		});
	}
	$("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
}

function loadHistoryContent(po_id, modified_at, i) {
    var currentLogContent = $("#history-part-"+i).find('.history-log-content');
    if(currentLogContent.text() == "") {
        currentLogContent.html(`<img src="/site_media/images/loading_spinner.gif" style="width: 30px;"> Loading, Please wait...`).show();
        currentLogContent.closest(".history-log-part").addClass("active");
        setTimeout(function(){
            $.ajax({
                url: '/erp/purchase/json/po/getpologdata/',
                type: "POST",
                dataType: "json",
                data: {"po_id":po_id, "modified_at": modified_at},
                success: function (data) {
                if (data['response_code'] != 400){
                    var row = '';
                        $.each( data['data'], function( key, value ) {
                          row += `<li>` + value + `</li>`;
                        });
                    currentLogContent.html(`<ul>` + row + `</ul>`);
                    currentLogContent.hide();
                    currentLogContent.slideDown();
                }
                else {
                    currentLogContent.html(`log data not available`);
                    currentLogContent.hide();
                    currentLogContent.slideDown();
                    }
                },
                error: function () {
                    console.log('Changelog listing failed to load');
                }
            });
        },100);
    }
    else {
        if(currentLogContent.find("img").length > 0) {
            return;
        }
        if(currentLogContent.is(":visible")) {
            currentLogContent.slideUp('fast', function() {
               currentLogContent.closest(".history-log-part").removeClass("active");
            });
        }
        else {
            currentLogContent.slideDown();
            currentLogContent.closest(".history-log-part").addClass("active");
        }
    }
    $("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
}

function resetMiDetails() {
    $('.mi_select').val(0).trigger("chosen:updated");
    $('.mi_assigned').val(0).trigger("chosen:updated");
    $(".item_select optgroup").empty();
    $(".item_select").chosen('destroy');
    $(".for-new-pp-post").addClass("hide");
    $( "#reportrange_add #todate_add" ).val(moment().format("YYYY-M-D"));
    var displayDateFormat = moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY')
    $('#reportrange_add span').html(displayDateFormat);
    $( "#reportrange_add" ).data('daterangepicker').startDate = moment();
    $( "#reportrange_add" ).data('daterangepicker').endDate = moment();
    $( "#reportrange_add" ).data('daterangepicker').updateView();
    $( "#reportrange_add" ).data('daterangepicker').updateCalendars();
    resetMiItemDetails();
}

function resetMiItemDetails() {
    $(".mi_max_qty").text("");
    $(".mi_qty").val(0.00);
    $(".unit_display").html("&nbsp;")
    $(".mi_max_qty_container").addClass("hide");
    $(".for-new-pp").addClass("hide");
}

function editReceipt(receipt_no, rec_against, openTarget="") {
    if(rec_against.trim().toLowerCase() == 'job work') {
        var url = `/erp/stores/grn/`;
    }
    else {
        var url = `/erp/stores/irn/`;
    }
    $("#id-edit_receipt_no").val(receipt_no);
    $("#id-edit_receipt_form").attr("action", url);
    $("#id-edit_receipt_form").attr("target", openTarget).submit();
}

function editPo(po_id, status, openTarget="") {
    $("#id-edit_po_id").val(po_id);
    $("#id-edit_po_status").val(status);
    $("#id-edit_po_form").attr("target", openTarget);
    $("#id-edit_po_form").submit();
    $("#loading").hide();
}

function checkSessionExpired(xhr) {
    if (xhr.responseText.indexOf("Your session has expired") != -1){
        location.reload();
    }
    else {
        swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
    }
}
function shortageCalculatorPP(pp_id){
    var form = $(`#${pp_id} #shortageCalPP`);
    $('#shortageCalPP #from_date').val($('#fromdate').val());
    $('#shortageCalPP #to_date').val($('#todate').val());
    form.attr('target', '_blank');
    form.submit();
}
async function getLocationList() {
    const locationListAll = await getLocations();
    const fromLocation = "from";
    locationList = locationListAll[fromLocation]
    const optgroup = $("#location_select optgroup");
    const editoptgroup = $("#edit_location_select optgroup");
    optgroup.empty();
    editoptgroup.empty();
    locationList.forEach(location => {
        optgroup.append(`<option value="${location.id}">${location.name}</option>`);
        editoptgroup.append(`<option value="${location.id}">${location.name}</option>`);
    });
    $("#location_select").trigger("chosen:updated");
    $("#edit_location_select").trigger("chosen:updated");
}
