"""
"""
__author__ = 'saravanan'


getOADetailsQuery = """
		SELECT 
		IF(o.status != 0,
			(CONCAT(o.financial_year,'/', SUBSTR(o.type, 1, 1), LPAD(o.oa_no, 5, 0), IFNULL(o.sub_number, ''))),
			(CONCAT('TMP#MI-', o.id))) AS oa_no,
		DATE_FORMAT(o.prepared_on,'%Y-%m-%d') AS oa_date,
		o.id as oa_id,
		pa.party_id AS party_id,
		pa.party_name AS party_name,
		CONCAT(m.name, ' ',CASE 
		WHEN m.drawing_no IS NOT NULL AND m.drawing_no != "" THEN CONCAT(" - ", m.drawing_no, " ") 
		ELSE '' END) AS item_name,
		m.id as item_id,
		m.is_service as is_service,
		oap.make_id as make_id,				
		oap.quantity AS oa_qty,
		IF(ind.expected_date !="",DATE_FORMAT(ind.expected_date,'%Y-%m-%d'), DATE_FORMAT(o.delivery_due_date,'%Y-%m-%d')) AS delivery_date,
		IFNUll(im.request_qty,0) as quantity,
		IFNULL(IF(ind.indent_id IS NOT NULL,
		(CONCAT(ind.financial_year, '/MI/', LPAD(ind.indent_id, 6, 0), IFNULL(ind.sub_number, ''))),
		(CONCAT('TMP#OA-', ind.indent_no))), "") AS mi_no,
		IFNULL(IF(ind.indent_id IS NOT NULL, DATE_FORMAT(ind.raised_date,'%Y-%m-%d'), ''), '') as mi_date,
		ind.indent_no as mi_id,
		IF(oap.alternate_unit_id != "", aum.unit_name ,um.unit_name) AS unit_name,
		IFNULL(mau.scale_factor, 1) as scale_factor,
		IFNULL(oap.alternate_unit_id, "") as alternate_unit_id,
		'' AS purpose,
		oap.mi_not_applicable as mi_not_applicable,
		IFNULL(ind.sp_instructions, "") AS remarks,
		(SELECT IFNULL(SUM(pom.pur_qty), 0)
		FROM
			purchase_order AS po
		JOIN purchase_order_material AS pom ON po.id = pom.pid					
			AND po.enterprise_id = pom.enterprise_id					
		WHERE
			po.indent_no = im.indent_no AND im.item_id = pom.item_id 
			AND im.make_id = pom.make_id) AS item_pur_qty,
		m.makes_json as make_name			
		FROM
			order_acknowledgement AS o
		JOIN
			oa_particulars AS oap ON o.id = oap.oa_id
			AND oap.enterprise_id = o.enterprise_id
		JOIN
			party_master AS pa ON o.party_id = pa.party_id
			AND pa.enterprise_id = o.enterprise_id
		JOIN
			materials AS m ON oap.item_id = m.id
			AND oap.enterprise_id = m.enterprise_id
		JOIN
			unit_master AS um ON um.unit_id = m.unit
			AND m.enterprise_id= um.enterprise_id
		LEFT JOIN
			materials_alternate_units as mau ON mau.alternate_unit_id = oap.alternate_unit_id
			AND mau.item_id = oap.item_id AND oap.enterprise_id=mau.enterprise_id
		LEFT JOIN
			unit_master AS aum ON aum.unit_id = oap.alternate_unit_id
			AND oap.enterprise_id = aum.enterprise_id		
		LEFT JOIN
			oa_indent_map AS oai ON oai.oa_id = o.id
			AND oai.enterprise_id = o.enterprise_id
		LEFT JOIN
			indent_material AS im ON im.indent_no = oai.indent_no
			AND im.enterprise_id = oai.enterprise_id
			AND im.item_id = oap.item_id
		LEFT JOIN
			indents AS ind ON oai.indent_no = ind.indent_no
			AND ind.enterprise_id= oai.enterprise_id
			AND ind.indent_module_id = 1 
		WHERE
			o.status > 0 AND o.enterprise_id = {enterprise_id} {oa_date_condition}
			{party_condition} {mi_not_applicable_condition} {mi_condition}
		UNION
		SELECT 
		IF(o.status != 0,
			(CONCAT(o.financial_year,'/', SUBSTR(o.type, 1, 1), LPAD(o.oa_no, 5, 0), IFNULL(o.sub_number, ''))),
			(CONCAT('TMP#MI-', o.id))) AS oa_no,
		DATE_FORMAT(o.prepared_on,'%Y-%m-%d') AS oa_date,
		o.id as oa_id,
		pa.party_id AS party_id,
		pa.party_name AS party_name,
		CONCAT(m.name, ' ',CASE 
		WHEN m.drawing_no IS NOT NULL AND m.drawing_no != "" THEN CONCAT(" - ", m.drawing_no, " ") 
		ELSE '' END) AS item_name,		
		m.id as item_id,
		m.is_service as is_service,
		im.make_id as make_id,				
		IFNULL(oap.quantity,0) AS oa_qty,
		DATE_FORMAT(ind.expected_date,'%Y-%m-%d') AS delivery_date,
		IFNUll(im.request_qty,0) as quantity,
		IFNULL(IF(ind.indent_id IS NOT NULL,
		(CONCAT(ind.financial_year, '/MI/', LPAD(ind.indent_id, 6, 0), IFNULL(ind.sub_number, ''))),
		(CONCAT('TMP#OA-', ind.indent_no))), "") AS mi_no,
		IFNULL(IF(ind.indent_id IS NOT NULL, DATE_FORMAT(ind.raised_date,'%Y-%m-%d'), ''), '') as mi_date,
		ind.indent_no as mi_id,
		IF(im.alternate_unit_id != "",aum.unit_name ,um.unit_name) AS unit_name,
		IFNULL(mau.scale_factor, 1) as scale_factor,
		IFNULL(im.alternate_unit_id, "") as alternate_unit_id,
		'' AS purpose,
		oap.mi_not_applicable as mi_not_applicable,
		IFNULL(ind.sp_instructions, "") AS remarks,
		(SELECT IFNULL(SUM(pom.pur_qty), 0)
		FROM
			purchase_order AS po
		JOIN purchase_order_material AS pom ON po.id = pom.pid					
			AND po.enterprise_id = pom.enterprise_id					
		WHERE
			po.indent_no = im.indent_no AND im.item_id = pom.item_id 
			AND im.make_id = pom.make_id) AS item_pur_qty,
		m.makes_json as make_name			
		FROM
			indents AS ind 
		JOIN
			indent_material AS im ON im.indent_no = ind.indent_no
			AND im.enterprise_id = ind.enterprise_id					
		JOIN
			materials AS m ON im.item_id = m.id
			AND im.enterprise_id = m.enterprise_id
		JOIN
			unit_master AS um ON um.unit_id = m.unit
			AND m.enterprise_id= um.enterprise_id
		LEFT JOIN
			materials_alternate_units as mau ON mau.alternate_unit_id = im.alternate_unit_id
			AND mau.item_id = im.item_id AND im.enterprise_id=mau.enterprise_id
		LEFT JOIN
			unit_master AS aum ON aum.unit_id = im.alternate_unit_id
			AND im.enterprise_id = aum.enterprise_id	
		LEFT JOIN
			oa_indent_map AS oai ON oai.indent_no = ind.indent_no
			AND oai.enterprise_id = ind.enterprise_id
		LEFT JOIN
			order_acknowledgement AS o ON o.id = oai.oa_id   
			AND o.enterprise_id = oai.enterprise_id
		LEFT JOIN
			oa_particulars AS oap ON o.id = oap.oa_id
			AND oap.enterprise_id = o.enterprise_id  
			AND im.item_id = oap.item_id
		JOIN
			party_master AS pa ON o.party_id = pa.party_id
			AND pa.enterprise_id = o.enterprise_id    
		WHERE
			ind.indent_module_id = 1 AND
			o.status > 0 AND o.enterprise_id = {enterprise_id} {oa_date_condition}
			{party_condition} {mi_not_applicable_condition} {mi_condition}
		UNION	 
		SELECT 
		'' AS oa_no,
		'' AS oa_date,
		'' as oa_id,
		'' AS party_id,
		'' AS party_name,
		CONCAT(m.name, ' ',CASE 
		WHEN m.drawing_no IS NOT NULL AND m.drawing_no != "" THEN CONCAT(" - ", m.drawing_no, " ") 
		ELSE '' END) AS item_name,
		m.id as item_id,
		m.is_service as is_service,
		im.make_id as make_id,				
		'' AS oa_qty,
		DATE_FORMAT(ind.expected_date,'%Y-%m-%d') AS delivery_date,
		IFNUll(im.request_qty,0) as quantity,
		IFNULL(IF(ind.indent_id IS NOT NULL,
		(CONCAT(ind.financial_year, '/MI/', LPAD(ind.indent_id, 6, 0), IFNULL(ind.sub_number, ''))),
		(CONCAT('TMP#MI-', ind.indent_no))), "") AS mi_no,
		IFNULL(IF(ind.indent_id IS NOT NULL, DATE_FORMAT(ind.raised_date,'%Y-%m-%d'), ''), '') as mi_date,
		ind.indent_no as mi_id,
		IF(im.alternate_unit_id != "",aum.unit_name ,um.unit_name) AS unit_name,
		IFNULL(mau.scale_factor, 1) as scale_factor,
		IFNULL(im.alternate_unit_id, "") as alternate_unit_id,
		ind.purpose AS purpose,
		0 as mi_not_applicable,
		IFNULL(ind.sp_instructions, "") AS remarks,
		(SELECT IFNULL(SUM(pom.pur_qty), 0)
		FROM
			purchase_order AS po
		JOIN purchase_order_material AS pom ON po.id = pom.pid					
			AND po.enterprise_id = pom.enterprise_id					
		WHERE
			po.indent_no = im.indent_no AND im.item_id = pom.item_id 
			AND im.make_id = pom.make_id) AS item_pur_qty,
		m.makes_json as make_name			
		FROM
			indents AS ind
		JOIN
			indent_material AS im ON im.indent_no = ind.indent_no
			AND im.enterprise_id = ind.enterprise_id			
		JOIN
			materials AS m ON im.item_id = m.id
			AND im.enterprise_id = m.enterprise_id
		JOIN
			unit_master AS um ON um.unit_id = m.unit
			AND m.enterprise_id= um.enterprise_id
		LEFT JOIN
			materials_alternate_units as mau ON mau.alternate_unit_id = im.alternate_unit_id
			AND mau.item_id = im.item_id AND im.enterprise_id=mau.enterprise_id
		LEFT JOIN
			unit_master AS aum ON aum.unit_id = im.alternate_unit_id
			AND im.enterprise_id = aum.enterprise_id	
		LEFT JOIN 
			oa_indent_map AS oai ON oai.indent_no = ind.indent_no 
			AND oai.enterprise_id = ind.enterprise_id					 
		WHERE
			ind.enterprise_id = {enterprise_id} AND ind.indent_module_id = 1
			AND ind.indent_no not in (select indent_no from oa_indent_map where enterprise_id={enterprise_id}) 
			{indent_date_condition} {mi_condition} """

production_plan_staus_query = """
	SELECT * FROM (SELECT 
	pp_no, pp_id, pp_date, item_name, item_id, make_id, unit_name, scale_factor, alternate_unit_id, pp_qty, completed_qty, 
	delivery_date, assigned_to, mi_no, mi_date, mi_id, start_date, end_date, completed_date, remarks, instructions, 
	is_service,	po_type, order_status, party_id, location_id, allocated_qty, required_qty, issued_qty,
	CASE
		WHEN completed_qty = 0 AND pp_qty >= 0 THEN "Yet to Start"					
		WHEN completed_qty >= pp_qty THEN "Completed"		
		WHEN completed_qty > 0 AND pp_qty > 0 THEN "In Progress"		
	END AS status,
	CASE
		WHEN completed_date = 0 AND completed_qty = 0 AND pp_qty = 0 THEN "ON Time"
		WHEN completed_date != 0 AND completed_date <= end_date AND completed_qty >= pp_qty THEN "ON Time"
		WHEN completed_date != 0 AND completed_date <= end_date AND completed_qty > 0 AND pp_qty > 0					 
		AND end_date > current_date() THEN 'ON Time'
		WHEN completed_date = 0 AND end_date > current_date() THEN 'ON Time' 
		WHEN completed_date != 0 AND completed_date <= end_date AND completed_qty > 0 AND pp_qty > 0 
		AND end_date < current_date() THEN 'Overdue'
		WHEN completed_date != 0 AND completed_date > end_date THEN 'Overdue' 
		WHEN completed_date = 0 AND end_date < current_date() THEN 'Overdue'  
		ELSE ""
	END AS timeliness,make_name
	FROM
	(SELECT 
	IF(po.status > 1,
	(CONCAT(po.financial_year, IF(po.type = 2,'/PP/', '/JO/'), LPAD(po.orderno, 6, 0), IFNULL(po.sub_number, ''))),
	(CONCAT( IF(po.type = 2,'TMP#PP-','TMP#JO-'), po.id))) AS pp_no,
	po.id AS pp_id,
	DATE_FORMAT(po.drafted_on, '%Y-%m-%d') AS pp_date,    
	CONCAT(m.name, ' ',CASE 
	WHEN m.drawing_no IS NOT NULL AND m.drawing_no != "" THEN CONCAT(" - ", m.drawing_no, " ") 
	ELSE '' END) AS item_name,
	m.id AS item_id,
	m.is_service as is_service,
	IF(pom.alternate_unit_id != "",aum.unit_name ,um.unit_name) AS unit_name,
	IFNULL(mau.scale_factor, 1) as scale_factor,
	IFNULL(pom.alternate_unit_id, "") as alternate_unit_id,
	pom.pur_qty AS pp_qty,
	pom.make_id AS make_id,
	IFNULL((SELECT SUM(acc_qty) FROM grn_material AS gm, grn AS g 
	WHERE g.grn_no=gm.grnNumber AND g.status >-1 AND pom.item_id=gm.item_id AND gm.po_no=pom.pid 
		AND gm.make_id=pom.make_id AND gm.dc_id IS NULL AND gm.enterprise_id=pom.enterprise_id  
		AND gm.rec_grn_id is NULL), 0) AS completed_qty,				
	IFNULL((SELECT DATE_FORMAT(g.inward_date, '%Y-%m-%d') FROM grn_material AS gm, grn AS g
	WHERE g.grn_no = gm.grnNumber AND g.status > - 1 AND pom.item_id = gm.item_id AND gm.po_no = pom.pid 
	AND gm.make_id = pom.make_id AND gm.dc_id IS NULL AND gm.enterprise_id = pom.enterprise_id 
	AND gm.rec_grn_id IS NULL order by g.inward_date DESC LIMIT 1), "") AS completed_date,				
	DATE_FORMAT(po.delivery, '%Y-%m-%d') AS delivery_date,
	IF(po.type = 2, po.issue_to, party.party_name) as assigned_to,
	IFNULL(IF(ind.indent_id IS NOT NULL,
	(CONCAT(ind.financial_year, '/MI/', LPAD(ind.indent_id, 6, 0), IFNULL(ind.sub_number, ''))),
	(CONCAT('TMP#OA-', ind.indent_no))), '') AS mi_no,
	ind.indent_no as mi_id,
	IFNULL(DATE_FORMAT(ind.raised_date, '%Y-%m-%d'), "") as mi_date,
	IF(po.type = 2,IFNULL((SELECT DATE_FORMAT(due_date, '%Y-%m-%d') from purchase_order_material_delivery_schedules 
		where item_id = pom.item_id AND make_id=pom.make_id AND po_id = pom.pid AND qty = 0 limit 1), 
		DATE_FORMAT(po.drafted_on, '%Y-%m-%d')) , DATE_FORMAT(po.drafted_on, '%Y-%m-%d')) as start_date,
	IF(po.type = 2,IFNULL((SELECT DATE_FORMAT(due_date, '%Y-%m-%d') from purchase_order_material_delivery_schedules 
		where item_id = pom.item_id AND make_id=pom.make_id AND po_id = pom.pid AND qty != 0 limit 1), "")
		, DATE_FORMAT(po.delivery, '%Y-%m-%d')) as end_date,
	IFNULL(po.remarks, "null") as remarks,
	IFNULL(po.sp_instructions, "") as instructions,
	IFNULL(po.type, "") as po_type,
	IFNULL(po.status, "") as order_status,
	IF(po.type = 2, 0, party.party_id) as party_id,
	m.makes_json as make_name,
	po.location_id as location_id,
	(SELECT COALESCE(SUM(allocated_qty), 0)  
    FROM 
        mrp_materials AS mrp where mrp.pp_id = po.id and mrp.is_expand = 0) AS allocated_qty, 
    (SELECT COALESCE(SUM(required_qty), 0)  
	FROM 
		mrp_materials AS mrp where mrp.pp_id = po.id and mrp.is_expand = 0) AS required_qty,
	(SELECT COALESCE(SUM(issued_qty), 0)  
	FROM 
		mrp_materials AS mrp where mrp.pp_id = po.id and mrp.is_expand = 0) AS issued_qty	
	FROM
		purchase_order AS po
	JOIN
		purchase_order_material AS pom ON po.id = pom.pid
		AND pom.enterprise_id = po.enterprise_id
	LEFT JOIN
		party_master AS party ON party.party_id = po.supplier_id
		AND party.enterprise_id = po.enterprise_id	
	JOIN
		materials AS m ON pom.item_id = m.id
		AND pom.enterprise_id = m.enterprise_id
	JOIN
		unit_master AS um ON um.unit_id = m.unit
		AND m.enterprise_id= um.enterprise_id
	LEFT JOIN
		materials_alternate_units as mau ON mau.alternate_unit_id = pom.alternate_unit_id
		AND mau.item_id = pom.item_id AND pom.enterprise_id=mau.enterprise_id
	LEFT JOIN
		unit_master AS aum ON aum.unit_id = pom.alternate_unit_id
		AND pom.enterprise_id = aum.enterprise_id		
	LEFT JOIN
		indents as ind ON ind.indent_no = po.indent_no
		AND po.enterprise_id = ind.enterprise_id
	WHERE	
		po.status > -1 AND po.type in(1,2) AND ind.indent_module_id = 1 
		AND po.enterprise_id = {enterprise_id} {po_id_condition} 
		{date_condition}) AS PPLIST) AS PLANLIST {filter_condition} """

getProductionLogQuery = """
			SELECT 
			pom.item_id,
			IFNUll(gm.acc_qty,0) as quantity,
			mat.name AS item_name,
			gm.location_id as location_id,
			IF(pom.alternate_unit_id != "",aum.unit_name ,um.unit_name) AS unit_name,
			IFNULL(mau.scale_factor, 1) as scale_factor,
			IFNULL(pom.alternate_unit_id, "") as alternate_unit_id,
			pom.make_id,			
			DATE_FORMAT(g.inward_date, '%Y-%m-%d') AS inward_date,
			IF(g.status != 0,
				(CONCAT(g.financial_year, IF(g.rec_against = 'Job Work','/GRN/','/IRN/'), LPAD(g.receipt_no, 6, 0), IFNULL(g.sub_number, ''))),
				(CONCAT('TMP#PL-', g.grn_no))) AS pl_no,
			IFNULL(g.grn_no, "") AS pl_id,
			g.status as status,
			mat.is_service as is_service,
			IFNULL(g.rec_against, "") as rec_against,
			mat.makes_json AS make_name				 
			FROM
				purchase_order AS po
			JOIN
				purchase_order_material AS pom ON po.id = pom.pid
				AND po.enterprise_id = pom.enterprise_id
			JOIN
				materials AS mat ON mat.id = pom.item_id
				AND pom.enterprise_id = mat.enterprise_id
			JOIN
				unit_master AS um ON um.unit_id = mat.unit
				AND mat.enterprise_id= um.enterprise_id
			LEFT JOIN
				materials_alternate_units as mau ON mau.alternate_unit_id = pom.alternate_unit_id
				AND mau.item_id = pom.item_id AND pom.enterprise_id=mau.enterprise_id
			LEFT JOIN
				unit_master AS aum ON aum.unit_id = pom.alternate_unit_id
				AND pom.enterprise_id = aum.enterprise_id		
			LEFT JOIN	
				grn_material AS gm ON pom.pid = gm.po_no
				AND pom.enterprise_id = gm.enterprise_id
				AND pom.item_id = gm.item_id
				AND pom.make_id = gm.make_id
				AND gm.dc_id is NULL   	
			JOIN
				grn AS g ON g.grn_no = gm.grnNumber
				AND g.enterprise_id = gm.enterprise_id
				AND g.status > -1
			WHERE
				pom.enterprise_id = {enterprise_id} AND pom.pid = {pp_id}"""

getMIMaterialQuery = """
			SELECT item_id, request_qty-pp_qty as request_qty, item_name, unit_name, make_id, is_service, 
			scale_factor, alternate_unit_id, delivery_date, make_name FROM (SELECT 
			im.item_id AS item_id,
			im.request_qty AS request_qty,
			mat.name AS item_name,
			IF(im.alternate_unit_id != "",aum.unit_name ,um.unit_name) AS unit_name,
			IFNULL(mau.scale_factor, 1) as scale_factor,
			IFNULL(im.alternate_unit_id, "") as alternate_unit_id,
			im.make_id AS make_id, 
			IFNULL(sum(pom.pur_qty), 0) AS pp_qty,
			mat.is_service as is_service,
			IFNULL(DATE_FORMAT(ind.expected_date, '%Y-%m-%d'), "") AS delivery_date,
			mat.makes_json as make_name 
			FROM
				indent_material AS im
			JOIN
				indents AS ind ON im.indent_no = ind.indent_no
				AND im.enterprise_id = ind.enterprise_id	  		
			JOIN
				materials AS mat ON mat.id = im.item_id
				AND im.enterprise_id = mat.enterprise_id
			JOIN
				unit_master AS um ON um.unit_id = mat.unit
				AND mat.enterprise_id= um.enterprise_id
			LEFT JOIN
				materials_alternate_units as mau ON mau.alternate_unit_id = im.alternate_unit_id
				AND mau.item_id = im.item_id AND im.enterprise_id=mau.enterprise_id
			LEFT JOIN
				unit_master AS aum ON aum.unit_id = im.alternate_unit_id
				AND im.enterprise_id = aum.enterprise_id	
			LEFT JOIN
				purchase_order AS po ON im.indent_no = po.indent_no
				AND im.enterprise_id = po.enterprise_id
			LEFT JOIN	
				purchase_order_material AS pom ON po.id = pom.pid
				AND po.enterprise_id = pom.enterprise_id
				AND pom.item_id = im.item_id
				AND pom.make_id = im.make_id						        
			WHERE
				im.enterprise_id = {enterprise_id} AND im.indent_no = {mi_id} 
				AND im.request_qty > 0 group by im.indent_no, im.item_id,im.item_id
				) as MI WHERE MI.request_qty - MI.pp_qty > 0 """


getManufactureIndentListQuery = """
				SELECT 
				oa_no, party_name, mi_no, mi_id, indent_date, indent_qty, pur_qty
				FROM
				(SELECT 
				IF(o.status != 0, (CONCAT(o.financial_year,'/', SUBSTR(o.type, 1, 1), LPAD(o.oa_no, 5, 0), IFNULL(o.sub_number, ''))), 
				(CONCAT('TMP#OA-', o.id))) AS oa_no,
				pa.party_name AS party_name,
				IFNULL(IF(ind.indent_id IS NOT NULL, (CONCAT(ind.financial_year, '/MI/', LPAD(ind.indent_id, 6, 0), 
				IFNULL(ind.sub_number, ''))), (CONCAT('TMP#OA-', ind.indent_no))), '') AS mi_no,
				IFNULL(IF(ind.indent_id IS NOT NULL, DATE_FORMAT(ind.raised_date, '%Y-%m-%d'), ''), '') AS indent_date,
				ind.indent_no AS mi_id,
				ind.enterprise_id AS enterprise_id,
				IFNULL(SUM(im.request_qty), 0) AS indent_qty,
				(SELECT IFNULL(SUM(pom.pur_qty), 0)
				FROM
					purchase_order AS po
				JOIN purchase_order_material AS pom ON po.id = pom.pid
					AND po.enterprise_id = pom.enterprise_id
				WHERE
					po.indent_no = ind.indent_no) AS pur_qty,					
				IFNULL(ind.sp_instructions, "") AS remarks	
				FROM
					indents AS ind
				JOIN indent_material AS im ON ind.indent_no = im.indent_no
					AND ind.enterprise_id = im.enterprise_id
				LEFT JOIN oa_indent_map AS oai ON oai.indent_no = ind.indent_no
					AND oai.enterprise_id = ind.enterprise_id
				LEFT JOIN order_acknowledgement AS o ON o.id = oai.oa_id
					AND oai.enterprise_id = o.enterprise_id
				LEFT JOIN party_master AS pa ON o.party_id = pa.party_id
					AND pa.enterprise_id = o.enterprise_id
				WHERE
					ind.enterprise_id = {enterprise_id}
					AND ind.indent_module_id = 1
				GROUP BY oa_no , party_name , mi_no , mi_id) AS ind
				WHERE
					indent_qty > pur_qty"""

getIndentCompletedStatusQuery = """SELECT 

    IFNULL(SUM(gm.acc_qty), 0) as grn_qty
FROM
    indents AS i
        LEFT JOIN
    indent_material AS im ON im.indent_no = i.indent_no
        AND im.enterprise_id = i.enterprise_id
        LEFT JOIN
    purchase_order AS p ON p.indent_no = i.indent_no
        AND p.enterprise_id = i.enterprise_id
        LEFT JOIN
    purchase_order_material AS pm ON pm.pid = p.id
        AND pm.item_id = im.item_id
        AND pm.enterprise_id = im.enterprise_id
        LEFT JOIN
    grn_material AS gm ON gm.po_no = p.id
        AND gm.item_id = pm.item_id
WHERE
    i.enterprise_id = {enterprise_id}
        AND i.indent_module_id = 1
        AND i.indent_no = {indent_no}
        AND im.item_id = {item_id}
GROUP BY i.indent_no , im.item_id;"""
