"""
"""
from django.conf.urls import patterns, url

from erp.purchase.reports import generateMaterialWiseHistoryReport, generateShortageListReport, \
	generateMaterialShortageList, appendgenerateShortageListReport, generateMaterialShortageListPP, \
	generateShortagePPListReport, append_generate_Shortage_ListReport
from erp.sales.views import jobDcSupplied, jobDcUsage, loadIssueList, loadDcList, manageDc, manageIssue
from erp.stores import json_api
from erp.stores.grn_views import manageAllReceipts, loadAllReceipts, loadPurchaseOrderNumbers, loadGRNIssue, \
	loadReceiptMaterial, saveReceipt, approveReceipt, loadReceipt, loadReceiptTaxes, \
	loadInvoiceDocument, loadReceiptDocument, fetchChargeInclusionFlags, rejectReceipt, loadPOList, \
	load_grn_tag, loadDeliveryChallans, loadDcMaterials, load_issue_material, loadReceiptMaterialAgainstDC, loadDCList, \
	lastUsedSupplierDetails, loadGoodsAlreadyReceivedNumbers, checkStockAvailability, loadInvoiceByGrn, \
	manageInternalReceipts, manageInternalReceipt, manageReceipt, invoicedItems, checkGateInwardNo, generateInwardNo, \
	fetchRejectionProfiles, loadReceiptPreview, loadNoteHeader, checkValidPOs, loadOpenPONumbers, getWOMaterials, \
	getPoQty
from erp.stores.indent_views import manageIndents, saveIndent, manageIndent, deleteIndent, loadIndentMaterial, \
	loadIndent, loadCatalogueMaterials, populateIndentTypeChoices, \
	checkIndentPo, checkIndentPoMaterial, loadSupplierList, loadMaterialPricedSuppliers, \
	getTagDetails, loadPO, loadStockQty, loadReceivedQty, getSelectedProject
from erp.stores.json_api import loadAlternateUnits, loadAllUnits, loadAllProjects
from erp.stores.views import renderDashboard, stock_report_page, stock_report_view_generate, \
	po_report_view_generate, indent_report_view_generate, grn_report_page, generateDCStoreReport, fetchClosingStock, \
	issueStockReport, jobInReport, jobOutReport, checkClosingStockValue, manageMaterialRequisition, \
	saveMaterialRequsition, \
	manageMaterialRequisitionList, deleteMaterialRequsition, getMaterialRequisitionDocument, \
	approveMaterialRequest, rejectMaterialRequest, create_stock_transfer, stock_transfer_template, \
	stock_transfer_listing_template, populate_stock_transfer_list, populate_transfer_item_list, \
	update_stock_transfer_template, get_stock_transfer_document, nonmoving_stock_report_view_generate, \
	get_location_wise_closing_stock, location_wise_stock_summary_template, pp_blocked_report_view_generate, \
	msl_report_view_generate

__author__ = 'kalaivanan'

urlpatterns = patterns(
	'',
	url('home/$', renderDashboard),
	url('indent_list/$', manageIndents),
	url('indent/load_selected_project/', getSelectedProject),
	url('indent_list/#table_tab2$', manageIndents),
	url('indent/save/$', saveIndent),
	url('indent/save/#table_tab2$', saveIndent),
	url('indent/$', manageIndent),
	url('indent_list/delete/$', deleteIndent),
	url('indent_list/loadindents/$', manageIndents),
	url('json/catalogue_materials/$', loadCatalogueMaterials),
	url('json/indent/loadsupplierlist/$', loadSupplierList),
	url('json/indent/loadMaterialPricedSuppliers', loadMaterialPricedSuppliers),
	url('json/indent/loadPoList', loadPO),
	url('json/indent/loadindentheader/$', loadIndent),
	url('json/indent/loadindentmaterial/$', loadIndentMaterial),
	url('json/indent/populate_types/$', populateIndentTypeChoices),
	url('json/indent/checkpomaterial/$', checkIndentPoMaterial),
	url('json/indent/checkpo/$', checkIndentPo),
	url('json/indent/getTagDetails/$', getTagDetails),
	url('json/indent/loadalternateunits/$', loadAlternateUnits),
	url('json/indent/loadallunits/$', loadAllUnits),
	url('json/indent/load_stock_qty/$', loadStockQty),
	url('json/indent/load_received_qty/$', loadReceivedQty),
	url('json/indent/loadallprojectlist/$', loadAllProjects),


	# GRN
	url('grn_list/$', manageAllReceipts),
	url('grn/$', manageReceipt),
	url('irn_list/$', manageInternalReceipts),
	url('irn/$', manageInternalReceipt),
	url('issue_stock_report/$', issueStockReport),
	url('grn/reject/$', rejectReceipt),
	url('json/grn/loadgrnPDF/$', loadReceiptDocument),
	url('json/grn/loadgrnlist/$', loadAllReceipts),
	url('json/grn/load_invoice_doc/$', loadInvoiceDocument),
	url('json/grn/load_grn_tags/$', load_grn_tag),
	url('json/grn/loadgrntaxes/$', loadReceiptTaxes),
	url('json/grn/loadpolist/$', loadPOList),
	url('json/grn/loadinvoicegrnfetch/$', loadInvoiceByGrn),
	url('json/grn/loadgrnmaterial/$', loadReceiptMaterial),
	url('json/grn/loadgrnheader/$', loadReceipt),
	url('json/grn/loadnoteheader/$', loadNoteHeader),
	url('json/grn/fetch_charge_flags/$', fetchChargeInclusionFlags),
	url('json/receipt/$', saveReceipt),  # POST to save receipt, GET to get receipt
	url('json/grn/po_numbers/$', loadPurchaseOrderNumbers),
	url('json/party_oa_financial_years/$', json_api.loadPartyOaFinancialYears),
	url('json/pending_job_oa_numbers/$', json_api.loadPendingJobOaNumbers),
	url('json/last_used_supplier_detail/$', lastUsedSupplierDetails),
	url('json/grn/check_is_valid/$', checkValidPOs),
	url('json/grn/approve/$', approveReceipt),
	url('json/grn/loaddcnumber/$', loadDeliveryChallans),
	url('json/grn/loadissuenumber/$', loadGRNIssue),
	url('json/grn/load_dc_materials/$', loadDcMaterials),
	url('json/grn/invoiced_items/$', invoicedItems),
	url('json/grn/usage_report/$', json_api.materialUsageReport),
	url('json/grn/loadissuematerial/$', load_issue_material),
	url('json/grn/loadgrnmaterialagainstdc/$', loadReceiptMaterialAgainstDC),
	url('json/grn/load_selected_dc_list/$', loadDCList),
	url('json/grn/goods_already_received_numbers/$', loadGoodsAlreadyReceivedNumbers),
	url('json/grn/goods_returned_status/$', json_api.goodsReturnedStatus),
	url('json/grn/check_stock_availability/$', checkStockAvailability),
	url('json/grn/check_gate_inward_no/$', checkGateInwardNo),
	url('json/grn/generate_gate_inward_no/$', generateInwardNo),
	url('json/grn/fetch_rejection_profiles/$', fetchRejectionProfiles),
	url('json/grn/loadgrnnotepreview/$', loadReceiptPreview),
	url('json/grn/open_po_numbers/$', loadOpenPONumbers),
	url('json/grn/get_wo_materials/$', getWOMaterials),
	url('json/po_qty/$', getPoQty),

	url('issue_list/$', loadIssueList),
	url('dc_list/$', loadDcList),
	url('issue/$', manageIssue),
	url('dc/$', manageDc),
	url('json/issue/closingstock/$', fetchClosingStock),
	url('json/issue/location_wise_closingstock/$', get_location_wise_closing_stock),
	url('location_wise_stock_summary/$', location_wise_stock_summary_template),
	url('json/issue/check_closing_stock/$', checkClosingStockValue),
	url('json/dc/job_dc_supplied/$', jobDcSupplied),
	url('json/dc/job_dc_usage/$', jobDcUsage),
	url('stock-statement/$', stock_report_page),

	# GRN report page
	url('grn-report-statement/$', grn_report_page),

	# Stock Report
	url('json/stockreport/$', stock_report_view_generate),

	# Po Pending Report
	url('json/poreport/$', po_report_view_generate),

	# Ind Pending Report
	url('json/indreport/$', indent_report_view_generate),

	# Non Moving Stock Report
	url('json/nmsstockreport/$', nonmoving_stock_report_view_generate),

	# PP blocked Report
	url('json/blockedreport/$',pp_blocked_report_view_generate),

	# Minimum Stock Level Report
	url('json/msl_report/$',msl_report_view_generate),

	url('materialhistorypage/$', generateMaterialWiseHistoryReport),
	url('materialhistory/$', generateMaterialWiseHistoryReport),

	url('materialshortagelist/$', generateShortageListReport),
	url('materialshortagepplist/$', generateShortagePPListReport),
	url('appendmaterialshortagelistdata/$', appendgenerateShortageListReport),
	url('append_generate_Shortage_ListReport/$', append_generate_Shortage_ListReport),
	url('materialshortage/$', generateMaterialShortageList),
	url('materialshortage_pp/$', generateMaterialShortageListPP),
	url('json/job_in_register/$', json_api.getJobInRegisterReport),
	url('jobinreport/$', jobInReport),
	url('json/job_out_register/$', json_api.getJobOutRegisterReport),
	url('joboutreport/$', jobOutReport),

	# Store Reports
	url('dc-report/$', generateDCStoreReport),
	url('json/isr/$', json_api.getIssueStockReport),

	# Store module urls here
	url('json/dashboard_data/', json_api.dashboard),
	url('json/get_stock_statement/', json_api.getStockStatement),
	url('json/list_stock_statement/', json_api.getListStockStatement),
	url('json/get_indent_statement/', json_api.getIndentStatement),
	url('json/stockStatement/', json_api.stockStatement),
	url('json/grnStatus/', json_api.grnStatus),
	url('json/indentStatus/', json_api.indentStatus),
	url('json/grn_draft/', json_api.fetchDraftGrn),
	url('json/stockCheck/', json_api.stockCheck),
	url('json/listAllIssues/', json_api.listAllIssues),  # not used
	url('json/listAllIndents/', json_api.listAllIndents),  # not used
	url('json/grnreport/', json_api.grnReportTbl),
	url('json/salesreport/', json_api.salesReportTbl),
	url('json/dcreport/', json_api.getDCReport),
	url('json/getjobpo/', json_api.getJobPO),
	url('json/getsupplierlist/', json_api.getSupplierList),
	url('json/getpartyoa/', json_api.getPartyOA),
	url('json/pending_grn_oa/', json_api.getPartyOAAgainstGRN),
	url('json/material_stock/', json_api.materialStock),
	url('json/invoiceLastUsedSupplierDetails/$', json_api.lastUsedSupplierDetailsInvoice),
	url('json/smart_populate_tax/$', json_api.smartPopulateSalesTax),
	url('json/selected_oa_numbers/', json_api.getInvoiceOA),
	url('json/selected_grn_numbers/', json_api.getInvoiceGRN),
	url('json/loadpolist/', json_api.getInvoicePO),
	# url('grn/save_uploaded_file/', save_uploaded_file),  # Auto Load Invoice Details OCR method is deleted
	url('json/super_edit_indent_code/', json_api.superEditIndentCode),
	url('json/super_edit_receipt_code/', json_api.superEditReceiptCode),
	url('json/get_dc_linked_message/', json_api.getDCLinkedMessage),
	url('json/get_receipt_linked_message/', json_api.getReceiptLinkedMessage),
	url('json/get_dc_receipt_linked_message/', json_api.getDCReceiptLinkedMessage),

	# Material Requisition
	url('material_requisition/$', manageMaterialRequisition),
	url('material_requsition/save/$', saveMaterialRequsition),
	url('material_requsition_list/$', manageMaterialRequisitionList),
	url('material_requsition/delete/$', deleteMaterialRequsition),
	url('material_requsition/document/$', getMaterialRequisitionDocument),
	url('material_requisition/approve/$', approveMaterialRequest),
	url('material_requisition/reject/$', rejectMaterialRequest),


	# Stock Transfer
	url('stock_transfer_template/$', stock_transfer_template),
	url('update_stock_transfer/$', update_stock_transfer_template),
	url('json/stock_transfer/$', create_stock_transfer),
	url('stock_transfer_listing_template/$', stock_transfer_listing_template),
	url('json/stock_transfer_list/$', populate_stock_transfer_list),
	url('json/transfer_item_list/$', populate_transfer_item_list),
	url('stock_transfer/document/$', get_stock_transfer_document)
)
