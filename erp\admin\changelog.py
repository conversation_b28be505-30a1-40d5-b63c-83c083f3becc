"""

"""
import re
from datetime import datetime
from deepdiff import DeepDiff

from erp.accounts import logger
from erp.helper import getUser
from erp.models import Currency
from settings import SQLASession
from util.api_util import response_code
from util.changelog import ChangeLog

__author__ = 'charlesmichel'


class EnterpriseProfileChangelog(ChangeLog):
	"""
	Changelog to do mongodb operations
	"""
	voucher = {}
	collection = 'enterprise'
	uid = "eid"

	def __init__(self):
		super(EnterpriseProfileChangelog, self).__init__(collection=self.collection, uid=self.uid)
		self.response_log = []

		self.enterprise_detail_set = dict(
			city='City', name='Name', address1='Address', currency_id='Currency', state='State',
			fy_start_day='Fiscal year', pin_code='Pincode', primary_contact='Contact Detail', registration_detail='Registration Detail')
		self.enterprise_contact_detail_set = dict(name='Contact Name', phone_no='Phone No', fax_no='Fax No', email='Email')
		self.enterprise_reg_detail_set = dict(label='Label', details='Details')
		self.enterprise_fiscal_yr = {
			"01/01": "January - December",
			"01/02": "February - January",
			"01/03": "March - February",
			"01/04": "April - March",
			"01/05": "May - April",
			"01/06": "June - May",
			"01/07": "July - June",
			"01/08": "August - July",
			"01/09": "September - August",
			"01/10": "October - September",
			"01/11": "November - October",
			"01/12": "December - November"
		}
		self.enterprise_currency = {}
		currency = SQLASession().query(Currency).all()
		for key, val in enumerate(currency):
			self.enterprise_currency[int(val.id)] = str(val.code)
		logger.info('EnterpriseProfileChangelog __init__')

	def buildEnterpriseProfileData(self, user=None, data=None):
		"""

		:param user:
		:param data:
		:return:
		"""
		response = {'modified_at': datetime.now(), 'primary_contact': [], 'registration_detail': []}
		try:
			# primary contact
			response['username'] = [user.first_name, user.last_name]
			response['name'] = str(data['enterprise_detail'][0]['name'])
			response['address1'] = str(data['enterprise_detail'][0]['address1'])
			response['state'] = str(data['enterprise_detail'][0]['state'])
			response['city'] = str(data['enterprise_detail'][0]['city'])
			response['pin_code'] = str(data['enterprise_detail'][0]['pin_code'])
			response['fy_start_day'] = str(self.enterprise_fiscal_yr[data['enterprise_detail'][0]['fy_start_day']])
			response['currency_id'] = str(self.enterprise_currency[int(data['enterprise_detail'][0]['currency_id'])])

			if len(data['enterprise_contact_detail']) > 0:
				for contact_detail in data['enterprise_contact_detail']:
					item = dict()
					item['name'] = str(contact_detail['name'])
					item['phone_no'] = str(contact_detail['phone_no'])
					item['fax_no'] = str(contact_detail['fax_no'])
					item['email'] = str(contact_detail['email'])
					if contact_detail['is_deleted'] != 1:
						response['primary_contact'].append(item)

			if len(data['enterprise_reg_detail']) > 0:
				for reg_detail in data['enterprise_reg_detail']:
					item = dict()
					item['label'] = str(reg_detail['label'])
					item['details'] = str(reg_detail['details'])
					if reg_detail['is_deleted'] != 1:
						response['registration_detail'].append(item)

		except Exception as e:
			logger.exception(e)
		return response

	def buildEnterprisePreferenceData(self, user=None, data=None):
		"""

		:param user:
		:param data:
		:return:
		"""
		response = {'modified_at': datetime.now(), 'preference': []}
		try:
			# primary contact
			response['username'] = [user.first_name, user.last_name]
			item = dict()
			item['setting_flags'] = str(data['setting_flags'])
			item['is_negative_stock_allowed'] = str(data['is_negative_stock_allowed'])
			item['is_gate_inward_no_mandatory'] = str(data['is_gate_inward_no_mandatory'])
			item['is_purchase_order_mandatory'] = str(data['is_purchase_order_mandatory'])
			response['preference'].append(item)
		except Exception as e:
			logger.exception(e)
		return response

	def queryInsert(self, user_id=None, enterprise_id=None, data=None):
		"""
		:param user_id:
		:param enterprise_id:
		:param data: enterprise data
		:return:
		"""
		response = {}
		try:
			user = getUser(enterprise_id=enterprise_id, user_id=user_id)
			dataset = self.buildEnterpriseProfileData(user=user, data=data) \
				if 'setting_flags' not in data.keys() else self.buildEnterprisePreferenceData(user=user, data=data)
			response = self.insert(id=enterprise_id, enterprise_id=enterprise_id, data=dataset)
		except Exception as e:
			logger.exception(e)
		return response

	def fetchLogList(self, eid=None, enterprise_id=None, offset=None, limit=None):
		"""
		get listed voucher log data
		:param voucher_id:
		:param enterprise_id:
		:return:
		"""
		response = {}
		result = []
		try:
			query = self.fetchLog(id=int(eid), enterprise_id=int(enterprise_id), offset=offset, limit=limit)
			for voucher in query:
				voucher['created_at'] = str(voucher['created_at'])
				voucher['log']['modified_at'] = str(voucher['log']['modified_at'])
				voucher['log']['preference'] = 1 if 'preference' in voucher['log'] else 0
				result.append(voucher)
			response = response_code.success() if len(result) > 0 else response_code.failure()
			response['data'] = result
		except Exception as e:
			logger.exception(e)
		return response

	def fetchLogData(self, eid=None, enterprise_id=None, modified_at=None, preference=None):
		"""
		get individual log data
		:param voucher_id:
		:param enterprise_id:
		:param modified_at:
		:return:
		"""
		response = response_code.failure()
		result = []
		try:
			query = self.fetchLogDetails(id=int(eid), enterprise_id=int(enterprise_id), identifier=modified_at, marker={
				'preference': preference})
			for enterprise in query:
				del enterprise['log']['modified_at']
				result.append(enterprise)
			response = response_code.success() if len(result) > 0 else response
			if len(result) > 1:
				response['data'] = self.buildLogData(result[1]['log'], result[0]['log'])
			elif len(result) > 0:
				response['data'] = self.buildLogData(None, result[0]['log'])
			response['data'] = response['data'] if len(response['data']) > 0 else ['No change detected']
		except Exception as e:
			logger.exception(e)
			response = response_code.failure()
		return response

	def fieldLogData(self, key=None, title=None, old=None, new=None):
		"""

		:param key:
		:param title:
		:param old:
		:param new:
		:return:
		"""
		if old is not None and key in old:
			diff = DeepDiff(old[key], new[key], ignore_order=True) if type(new[key]) == list and len(old[key]) != len(new[key]) else DeepDiff(old[key], new[key])
			if 'iterable_item_added' in diff:
				for k, item in diff['iterable_item_added'].items():
					if key == 'registration_detail':
						self.response_log.append('%s <b>%s: %s</b> added' % (title, str(item['label']), str(item['details'])))
					else:
						self.response_log.append('%s <b>%s</b> with values <b>%s %s %s</b> has been added' % (
							title, item['name'], item['email'], item['phone_no'], item['fax_no']))
			if 'iterable_item_removed' in diff:
				for k, item in diff['iterable_item_removed'].items():
					if key == 'registration_detail':
						self.response_log.append('%s <b>%s: %s</b> removed' % (title, str(item['label']), str(item['details'])))
					else:
						self.response_log.append('%s <b>%s</b> with values <b>%s %s %s</b> has been removed' % (
							title, item['name'], item['email'], item['phone_no'], item['fax_no']))
			if 'values_changed' in diff:
				if key in ['primary_contact', 'registration_detail']:
					if 'iterable_item_added' in diff:
						for item in diff['iterable_item_added'].items():
							self.response_log.append('%s <b>%s</b> of value <b>%s %s</b> added' % (title,
							item[1]['name'], item[1]['amount'], item[1]['type']))
					if 'iterable_item_removed' in diff:
						for item in diff['iterable_item_removed'].items():
							self.response_log.append('%s <b>%s</b> of value <b>%s %s</b> was removed' % (
								title, item[1]['name'], item[1]['phone_no'], item[1]['fax_no']))
					if 'values_changed' in diff:
						for item in diff['values_changed'].keys():
							var_chg = re.findall("\[(.*?)\]", item.replace('\'', ''))
							if var_chg[1] in self.enterprise_contact_detail_set:
								self.response_log.append(
									'<b>%s</b> of registration <b>%s</b> has changed to <b>%s</b>' % (
										self.enterprise_contact_detail_set[var_chg[1]], old[key][int(var_chg[0])][var_chg[1]], new[key][int(var_chg[0])][var_chg[1]]))
							elif var_chg[1] in self.enterprise_detail_set:
								self.response_log.append(
									'<b>%s</b> of registration <b>%s</b> has changed to <b>%s</b>' % (
										self.enterprise_detail_set[var_chg[1]], old[key][int(var_chg[0])][var_chg[1]], new[key][int(var_chg[0])][var_chg[1]]))
							elif var_chg[1] in self.enterprise_reg_detail_set:
								self.response_log.append(
									'Registration Details of <b>%s</b> has changed from <b>%s</b> to <b>%s</b>' % (
										new[key][int(var_chg[0])]['label'], old[key][int(var_chg[0])][var_chg[1]], new[key][int(var_chg[0])][var_chg[1]]))
							else:
								self.response_log.append(
									'<b>%s</b> of registration <b>%s</b> has changed to <b>%s</b>' % (
										var_chg[1].capitalize(), old[key][int(var_chg[0])][var_chg[1]], new[key][int(var_chg[0])][var_chg[1]]))
				else:
					if str(diff['values_changed']['root']['old_value']) == '' and str(
							diff['values_changed']['root']['new_value']) != '':
						self.response_log.append(
							'%s <b>%s</b> added' % (title, diff['values_changed']['root']['new_value']))
					elif str(diff['values_changed']['root']['new_value']) == '' and str(
							diff['values_changed']['root']['old_value']) != '':
						self.response_log.append(
							'%s <b>%s</b> removed' % (title, diff['values_changed']['root']['new_value']))
					else:
						self.response_log.append('%s changed from <b>%s</b> to <b>%s</b>' % (
							title,
							diff['values_changed']['root']['old_value'],
							diff['values_changed']['root']['new_value']))
		elif key in new:
			if type(new[key]) == list:
				for item in new[key]:
					if type(item) == dict and key == 'primary_contact':
						self.response_log.append('%s <b>%s</b> with values <b>%s %s %s</b> added' % (title, item['name'], item['phone_no'], item['fax_no'], item['email']))
					else:
						self.response_log.append('%s <b>%s: %s</b> added' % (title, item['label'], item['details']))
			elif new[key] != '':
				if key in ['fy_start_day', 'currency_id']:
					self.response_log.append('%s <b>%s</b> selected' % (title, new[key]))
				else:
					self.response_log.append('%s <b>%s</b> added' % (title, new[key]))
		logger.info('tags LOG DATA updated %s' % self.response_log)
		return False

	def buildLogData(self, old=None, new=None):
		"""

		:param old:
		:param new:
		:return:
		"""
		if 'preference' not in new.keys():
			for key, title in self.enterprise_detail_set.items():
				self.fieldLogData(key=key, title=title, old=old, new=new)
			return self.response_log
		else:
			return EnterprisePreferenceChangelog().fieldLogData(key='preference', title='Preference', old=old, new=new)


class EnterprisePreferenceChangelog(ChangeLog):
	"""
	Changelog to do mongodb operations
	"""
	voucher = {}
	collection = 'enterprise'
	uid = "eid"

	def __init__(self):
		super(EnterprisePreferenceChangelog, self).__init__(collection=self.collection, uid=self.uid)
		self.response_log = []
		self.setting_flag = {
			1: 'Indents',
			2: 'ICD',
			4: 'Ignore Credit Notes',
			8: 'Automate',
			16: 'Verify Auto-Gen Vouchers'

		}
		self.gin_setting_flag = {
			1: 'GRN Inward No Mandatory',
			2: 'GRN Inward No Unique in a Fiscal Year',
			4: 'GRN Inward No Automated',
			8: 'GRN Inward No Editable'
		}
		self.enterprise_setting_flags = {
			1: [1], 2: [2], 3: [1, 2], 4: [4], 5: [1, 4],
			6: [2, 4], 7: [1, 2, 4], 8: [8], 9: [1, 8],
			10: [2, 8], 11: [1, 2, 8], 12: [4, 8],
			13: [1, 4, 8], 14: [2, 4, 8],
			15: [1, 2, 4, 8], 16: [16], 17: [1, 16],
			18: [2, 16], 19: [1, 2, 16],
			20: [4, 16], 21: [1, 4, 16],
			22: [2, 4, 16],
			23: [1, 2, 4, 16],
			24: [8, 16], 25: [1, 8, 16],
			26: [2, 8, 16],
			27: [1, 2, 8, 16],
			28: [4, 8, 16],
			29: [1, 4, 8, 16],
			30: [2, 4, 8, 16],
			31: [1, 2, 4, 8, 16]}
		self.enterprise_gate_inward_no_setting_flags = {
			0: [],
			1: [1], 2: [2], 3: [1, 2], 4: [4], 5: [1, 4],
			6: [2, 4], 7: [1, 2, 4], 8: [8], 9: [1, 8],
			10: [2, 8], 11: [1, 2, 8], 12: [4, 8],
			13: [1, 4, 8], 14: [2, 4, 8],
			15: [1, 2, 4, 8]}
		self.enterprise_preference = dict(
			setting_flags=self.enterprise_setting_flags, is_negative_stock_allowed='Negative Inventory',
			gate_inward_no_flags=self.enterprise_gate_inward_no_setting_flags,
			is_purchase_order_mandatory='Mandate PO for all purchase', is_multiple_units='Multiple Units of Measurements',
			is_delivery_schedule='Delivery Schedule', is_icd_request_acknowledgement='Request Acknowledgement',
			is_blanket_po='Blanket PO')

		logger.info('EnterprisePreferenceChangelog __init__')

	def buildEnterprisePreferenceData(self, user=None, data=None):
		"""

		:param user:
		:param data:
		:return:
		"""
		response = {'modified_at': datetime.now(), 'preference': []}
		try:
			# primary contact
			response['username'] = [user.first_name, user.last_name]
			item = dict()
			item['setting_flags'] = str(data['setting_flags'])
			item['is_negative_stock_allowed'] = str(data['is_negative_stock_allowed'])
			item['is_delivery_schedule'] = str(data['is_delivery_schedule'])
			item['is_multiple_units'] = str(data['is_multiple_units'])
			item['gate_inward_no_flags'] = str(data['gate_inward_no_flags'])
			item['is_purchase_order_mandatory'] = str(data['is_purchase_order_mandatory'])
			item['is_icd_request_acknowledgement'] = str(data['is_icd_request_acknowledgement'])
			item['is_blanket_po'] = str(data['is_blanket_po'])
			response['preference'].append(item)
		except Exception as e:
			logger.exception(e)
		return response

	def queryInsert(self, user_id=None, enterprise_id=None, data=None):
		"""
		:param user_id:
		:param enterprise_id:
		:param data: enterprise data
		:return:
		"""
		response = {}
		try:
			user = getUser(enterprise_id=enterprise_id, user_id=user_id)
			dataset = self.buildEnterprisePreferenceData(user=user, data=data)
			response = self.insert(id=enterprise_id, enterprise_id=enterprise_id, data=dataset)
		except Exception as e:
			logger.exception(e)
		return response

	def fieldLogData(self, key=None, title=None, old=None, new=None):
		"""

		:param key:
		:param title:
		:param old:
		:param new:
		:return:
		"""
		if old is not None and key in old:
			diff = DeepDiff(old[key], new[key])
			if 'values_changed' in diff:
				if 'values_changed' in diff:
					for item in diff['values_changed'].keys():
						var_chg = re.findall("\[(.*?)\]", item.replace('\'', ''))
						if var_chg[1] == 'setting_flags':
							no_change_list = list(set(self.enterprise_setting_flags[int(old[key][int(var_chg[0])][var_chg[1]])]).intersection(self.enterprise_setting_flags[int(new[key][int(var_chg[0])][var_chg[1]])]))
							if int(old[key][int(var_chg[0])][var_chg[1]]) > 0:
								for old_item in self.enterprise_setting_flags[int(old[key][int(var_chg[0])][var_chg[1]])]:
									if old_item not in no_change_list:
										self.response_log.append('<b>%s</b> is %s' % (self.setting_flag[old_item], 'UnChecked'))
							if int(new[key][int(var_chg[0])][var_chg[1]]) > 0:
								for new_item in self.enterprise_setting_flags[int(new[key][int(var_chg[0])][var_chg[1]])]:
									if new_item not in no_change_list:
										self.response_log.append('<b>%s</b> is %s' % (self.setting_flag[new_item], 'Checked'))
						elif var_chg[1] == 'gate_inward_no_flags':
							no_change_list = list(set(self.enterprise_gate_inward_no_setting_flags[int(old[key][int(var_chg[0])][var_chg[1]])]).intersection(self.enterprise_gate_inward_no_setting_flags[int(new[key][int(var_chg[0])][var_chg[1]])]))
							if int(old[key][int(var_chg[0])][var_chg[1]]) > 0:
								for old_item in self.enterprise_gate_inward_no_setting_flags[int(old[key][int(var_chg[0])][var_chg[1]])]:
									if old_item not in no_change_list:
										self.response_log.append('<b>%s</b> is %s' % (self.gin_setting_flag[old_item], 'UnChecked'))
							if int(new[key][int(var_chg[0])][var_chg[1]]) > 0:
								for new_item in self.enterprise_gate_inward_no_setting_flags[int(new[key][int(var_chg[0])][var_chg[1]])]:
									if new_item not in no_change_list:
										self.response_log.append('<b>%s</b> is %s' % (self.gin_setting_flag[new_item], 'Checked'))
						else:
							self.response_log.append('<b>%s</b> is %s' % (self.enterprise_preference[var_chg[1]], 'Checked' if new[key][int(var_chg[0])][var_chg[1]] == '1' else 'UnChecked'))
		elif key in new:
			if type(new[key]) == list:
				for item in new[key]:
					if type(item) == dict:
						for k, data in item.items():
							if k == 'setting_flags':
								if int(item[k]) > 0:
									for flag in self.enterprise_setting_flags[int(item[k])]:
										self.response_log.append('<b>%s</b> is %s' % (self.setting_flag[flag], 'Checked'))
								else:
									for flag in self.enterprise_setting_flags[31 - int(item[k])]:
										self.response_log.append('<b>%s</b> is %s' % (self.setting_flag[flag], 'UnChecked'))
							elif k == 'gate_inward_no_flags':
								if int(item[k]) > 0:
									for flag in self.enterprise_gate_inward_no_setting_flags[int(item[k])]:
										self.response_log.append('<b>%s</b> is %s' % (self.gin_setting_flag[flag], 'Checked'))
								else:
									for flag in self.enterprise_gate_inward_no_setting_flags[31 - int(item[k])]:
										self.response_log.append('<b>%s</b> is %s' % (self.gin_setting_flag[flag], 'UnChecked'))
							else:
								self.response_log.append('<b>%s</b> is %s' % (
									self.enterprise_preference[k], 'Checked' if int(item[k]) == 1 else 'UnChecked'))
		logger.info('tags LOG DATA updated %s' % self.response_log)
		return self.response_log


class InvoiceTemplateChangelog(ChangeLog):
	"""
	Changelog to do mongodb operations
	"""
	voucher = {}
	collection = 'invoice_template_config_data'
	uid = "config_id"

	def __init__(self):
		super(InvoiceTemplateChangelog, self).__init__(collection=self.collection, uid=self.uid)
		self.response_log = []
		self.tax_types = {
			'1': 'COLUMNS',
			'2': 'CONSOLIDATED SUMMARY',
			'3': 'PART OF ITEM DETAILS',
			'4': 'RATE & AMOUNT IN ONE COLUMN'
		}
		self.sort_order = {
			'1': 'ORDER OF ENTRY',
			'2': 'ITEM NAME',
			'3': 'ITEM CODE/ DRAWING NO'
		}
		self.tax_payable_on_reverse_charge = {
			'1': 'PRINT ALWAYS',
			'2': 'PRINT ONLY WHEN YES'
		}
		self.scan_option = {
			'1': 'NONE',
			'2': 'QR CODE',
			'3': 'BAR CODE'
		}
		self.template_id = {
			'1': 'Compact',
			'2': 'Simple',
			'3': 'Elegant'
		}
		self.module_data_set = {
			'checkbox_fields': {
				'title': 'Check Box',
				'include_logo': 'Company Logo',
				'include_name': 'Company Name',
				'include_address': 'Company Address',
				'include_email': 'Company Email',
				'include_phone_no': 'Company Phone_no',
				'include_fax': 'Company Fax',
				'include_partycode': 'Party Code',
				'include_partyname': 'Party Name',
				'include_billingaddress': 'Billing Address',
				'include_shippingaddress': 'Shipping Address',
				'include_party_email': 'Party Email',
				'include_party_phoneno': 'Party Phone_no',
				'include_paymentterms': 'Payment Terms',
				'include_pono': 'Po No.',
				'include_podate': 'Po Date',
				'include_transportmode': 'Transport Mode',
				'include_splinstructions': 'Special Instructions',
				'include_roadpermitno': 'Road Permit No',
				'include_lrnodate': 'Lrno & Date',
				'include_packingslipno': 'Packing Slip No',
				'include_packingdescription': 'Packing Description',
				'include_authorizedsign': 'Authorized Signatory',
				'include_approver_signature': 'Approver Signature',
				'include_estimate_nodate': 'Estimate No. & Date',
				'include_sno': 'S.No',
				'include_itemcode': 'Item Code',
				'include_make': 'Make',
				'include_partno': 'Part No',
				'include_oano': 'Oa No',
				'include_dc_no': 'Dc No',
				'include_dc_qty': 'Display Dc Qty',
				'include_dc_date': 'Display Dc Date',
				'include_description': 'Description',
				'include_remarks': 'Remarks',
				'include_hsnsac': 'Hsn/Sac',
				'hsnsac_part_of_itemdetails': 'Include Hsn/Sac As A Part Of Item Details',
				'include_quantity': 'Quantity',
				'include_units': 'Units',
				'include_unit_price': 'Unit Price',
				'units_in_quantity_column': 'Include Units In QTY Column',
				'include_primary_qty': 'Display Primary Qty',
				'include_discount': 'Discount',
				'include_taxable_amount': 'Taxable Amount',
				'include_tax': 'Tax',
				'include_taxrate': 'Tax Rate',
				'include_taxamount': 'Tax Amount',
				'include_row_separator': 'Row Separator',
				'include_column_separator': 'Column Separator',
				'include_alternate_row_shading': 'Alternate Row Shading',
				'include_total': 'Show Total Section',
				'include_subtotal': 'Sub Total',
				'include_qty_total': 'Quantity Total',
				'include_total_in_words': 'Total Value In Words',
				'include_hsn_summary': 'Hsn Summary',
				'include_page_no_in_footer': 'Page Number',
				'include_first_page_summary': 'Summary In First Page',
				'show_tax_for_dc': 'Show Tax For Dc',
				'include_preparedsign': 'Prepared By',
			},
			'label_fields': {
				'title': 'Labels',
				'base_font': 'Base Font',
				'margin_top': 'Page Margin Top',
				'margin_bottom': 'Page Margin Bottom',
				'margin_right': 'Page Margin Right',
				'margin_left': 'Page Margin Left',
				'datetime':'Date Time Format',
				'logo_size': 'Logo Size',
				'name_font': 'Name Font',
				'name_font_size': 'Name Font Size',
				'header_font_size': 'Header Font Size',
				'form_name_font_size': 'Form Name Font Size',
				'billingaddress_label': 'Billing Address Label',
				'shippingaddress_label': 'Shipping Address Label',
				'gst_label': 'Gst Label',
				'service_label': 'Service Label',
				'trading_label': 'Trading Label',
				'billofsupply_label': 'Bill of supply Label',
				'others_label': 'Excise Label',
				'invoiceno_label': 'Invoice No Label',
				'invoicedate_label': 'Issue Date Label',
				'paymentterms_label': 'Payment Terms Label',
				'pono_label': 'Po No. Label',
				'podate_label': 'Po Date Label',
				'transportmode_label': 'Transport Mode Label',
				'splinstructions_label': 'Special Instructions Label',
				'roadpermitno_label': 'Road Permit No Label',
				'lrnodate_label': 'Lr No & Date Label',
				'packingslipno_label': 'Packing Slip No Label',
				'packingdescription_label': 'Packing Description Label',
				'authorizedsign_label': 'Authorized Signatory Label',
				'dc_label': 'Delivery Challan Label',
				'dcno_label': 'Dc No Label',
				'estimate_nodate_label': 'Estimate No. & Date Label',
				'inv_number_format': 'Number Format',
				'misc_font_size': 'Misc Font Size',
				'notes': 'Notes',
				'foot_note': 'Foot Note',
				'summary_font_size': 'Font Size',
				'hsn_tax_font_size': 'Hsn Summary Font Size',
				'item_font_size': 'Item Table Font Size',
				'sno_label': 'S.No Label',
				'sno_width': 'S.No Width',
				'itemdetails_label': 'Item Details Label',
				'itemdetails_width': 'Item Details Width',
				'itemcode_label': 'Item Code Label',
				'name_label': 'Name Label',
				'make_label': 'Make Label',
				'partno_label': 'Part No Label',
				'oano_label': 'OA No Label',
				'dc_no_label': 'Dc No Label',
				'description_label': 'Description Label',
				'remarks_label': 'Remarks Label',
				'hsnsac_label': 'Hsn/Sac Label',
				'hsnsac_width': 'Hsn/Sac Width',
				'quantity_label': 'Quantity Label',
				'quantity_width': 'Quantity Width',
				'units_label': 'Units Label',
				'units_width': 'Units Width',
				'unit_price_label': 'Unit Price Label',
				'unit_price_width': 'Unit Price Width',
				'discount_label': 'Discount Label',
				'discount_width': 'Discount Width',
				'taxable_amount_label': 'Taxable Amount Label',
				'taxable_amount_width': 'Taxable Amount Width',
				'taxrate_width': 'Tax Rate Width',
				'taxamount_width': 'Tax Amount Width',
				'invoice_number_font_size': 'Invoice Number Font Size',
				'preparedsign_label': 'Prepared By Label',
			},
			'tax': {
				'title': 'Tax Type',
				'tax_type': 'Tax type'
			},
			'item_sort_order': {
				'title': 'Sort Order',
				'sort_order': 'Item Sort Order'
			},
			'tax_payable_on_reverse_charge': {
				'title': 'Tax Applicable on Reverse Charges',
				'tax_payable_on_reverse_charge': 'Tax Applicable on Reverse Charges'
			},
			'template_view': {
				'title': 'Template view',
				'template_name': 'Template view'
			},
			'scan_code_option': {
				'title': 'Scan Code',
				'scan_option': 'Scan code option'
			}
			# 'included_reg_items': {'title': 'Included_reg_items'}
		}
		logger.info('InvoiceTemplateChangelog __init__')

	def buildInsertDataStructure(self, user=None, data=None, is_db_data=False):
		"""

		:param user:
		:param data:
		:return:
		"""
		response = dict()
		response['modified_at'] = datetime.now()
		response['checkbox_fields'] = []
		response['label_fields'] = []
		response['tax'] = []
		response['item_sort_order'] = []
		response['tax_payable_on_reverse_charge'] = []
		response['scan_code_option'] = []
		response['template_view'] = []
		try:
			# primary contact
			response['username'] = [user.first_name, user.last_name]
			item = dict()
			item['template_name'] = self.template_id[
				data.cleaned_data['template_id']] if is_db_data is False else self.template_id[str(data.template_id)]
			response['template_view'].append(item)

			item = dict()
			item['base_font'] = str(data.invoice_template_generalconfig_form.cleaned_data['base_font']) if is_db_data is False else str(data.template_general_details.base_font)
			item['margin_top'] = str(data.invoice_template_generalconfig_form.cleaned_data['margin_top']) if is_db_data is False else str(data.template_general_details.margin_top)
			item['margin_bottom'] = str(data.invoice_template_generalconfig_form.cleaned_data['margin_bottom']) if is_db_data is False else str(data.template_general_details.margin_bottom)
			item['margin_right'] = str(data.invoice_template_generalconfig_form.cleaned_data['margin_right']) if is_db_data is False else str(data.template_general_details.margin_right)
			item['margin_left'] = str(data.invoice_template_generalconfig_form.cleaned_data['margin_left']) if is_db_data is False else str(data.template_general_details.margin_left)
			inv_date_time_format = str(data.invoice_template_generalconfig_form.cleaned_data['inv_doc_datetime_format']) if is_db_data is False else str(data.template_general_details.inv_doc_datetime_format)
			char_replace={'%d': 'D','%m':'MM','%b':'MMM','%B':'MMMM','%y':'YY','%Y':'YYYY','%H':'HH','%I':'hh','%M':'mm','%S':'ss'}
			for key, value in char_replace.items():
				inv_date_time_format = inv_date_time_format.replace(key, value)
			item['datetime'] = inv_date_time_format
			item['logo_size'] = str(data.invoice_template_headerconfig_form.cleaned_data['logo_size']) if is_db_data is False else str(int(data.template_header_details.logo_size))
			item['name_font'] = str(data.invoice_template_headerconfig_form.cleaned_data['name_font']) if is_db_data is False else str(data.template_header_details.name_font)
			item['name_font_size'] = str(data.invoice_template_headerconfig_form.cleaned_data['name_font_size']) if is_db_data is False else str(int(data.template_header_details.name_font_size))
			item['header_font_size'] = str(data.invoice_template_headerconfig_form.cleaned_data['font_size']) if is_db_data is False else str(int(data.template_header_details.font_size))
			item['form_name_font_size'] = str(data.invoice_template_headerconfig_form.cleaned_data['form_name_font_size']) if is_db_data is False else str(int(data.template_header_details.form_name_font_size))
			item['billingaddress_label'] = str(data.invoice_template_headerconfig_form.cleaned_data['billingaddress_label']) if is_db_data is False else str(data.template_header_details.billingaddress_label)
			item['shippingaddress_label'] = str(data.invoice_template_headerconfig_form.cleaned_data[
				'shippingaddress_label']) if is_db_data is False else str(data.template_header_details.shippingaddress_label)
			item['gst_label'] = str(data.invoice_template_headerconfig_form.cleaned_data['gst_label']) if is_db_data is False else str(data.template_header_details.gst_label)
			item['service_label'] = str(data.invoice_template_headerconfig_form.cleaned_data['service_label']) if is_db_data is False else str(data.template_header_details.service_label)
			item['trading_label'] = str(data.invoice_template_headerconfig_form.cleaned_data['trading_label']) if is_db_data is False else str(data.template_header_details.trading_label)
			item['billofsupply_label'] = str(data.invoice_template_headerconfig_form.cleaned_data['billofsupply_label']) if is_db_data is False else str(data.template_header_details.billofsupply_label)
			item['others_label'] = str(data.invoice_template_headerconfig_form.cleaned_data['others_label']) if is_db_data is False else str(data.template_header_details.others_label)
			item['invoiceno_label'] = str(data.invoice_template_headerconfig_form.cleaned_data['invoiceno_label']) if is_db_data is False else str(data.template_header_details.invoiceno_label)
			item['invoicedate_label'] = str(data.invoice_template_headerconfig_form.cleaned_data['invoicedate_label']) if is_db_data is False else str(data.template_header_details.invoicedate_label)
			item['paymentterms_label'] = str(data.invoice_template_headerconfig_form.cleaned_data['paymentterms_label']) if is_db_data is False else str(data.template_header_details.paymentterms_label)
			item['pono_label'] = str(data.invoice_template_headerconfig_form.cleaned_data['pono_label']) if is_db_data is False else str(data.template_header_details.pono_label)
			item['podate_label'] = str(data.invoice_template_headerconfig_form.cleaned_data['podate_label']) if is_db_data is False else str(data.template_header_details.podate_label)
			item['transportmode_label'] = str(data.invoice_template_headerconfig_form.cleaned_data['transportmode_label']) if is_db_data is False else str(data.template_header_details.transportmode_label)
			item['splinstructions_label'] = str(data.invoice_template_headerconfig_form.cleaned_data[
				'splinstructions_label']) if is_db_data is False else str(data.template_header_details.splinstructions_label)
			item['roadpermitno_label'] = str(data.invoice_template_headerconfig_form.cleaned_data['roadpermitno_label']) if is_db_data is False else str(data.template_header_details.roadpermitno_label)
			item['lrnodate_label'] = str(data.invoice_template_headerconfig_form.cleaned_data['lrnodate_label']) if is_db_data is False else str(data.template_header_details.lrnodate_label)
			item['packingslipno_label'] = str(data.invoice_template_headerconfig_form.cleaned_data['packingslipno_label']) if is_db_data is False else str(data.template_header_details.packingslipno_label)
			item['packingdescription_label'] = str(data.invoice_template_headerconfig_form.cleaned_data[
				'packingdescription_label']) if is_db_data is False else str(data.template_header_details.packingdescription_label)
			item['authorizedsign_label'] = str(data.invoice_template_headerconfig_form.cleaned_data['authorizedsign_label']) if is_db_data is False else str(data.template_header_details.authorizedsign_label)
			item['preparedsign_label'] = str(data.invoice_template_headerconfig_form.cleaned_data['preparedsign_label']) if is_db_data is False else str(data.template_header_details.preparedsign_label)
			item['dc_label'] = str(data.invoice_template_headerconfig_form.cleaned_data['dc_label']) if is_db_data is False else str(data.template_header_details.dc_label)
			item['dcno_label'] = str(data.invoice_template_headerconfig_form.cleaned_data['dcno_label']) if is_db_data is False else str(data.template_header_details.dcno_label)
			item['inv_number_format'] = str(data.invoice_template_headerconfig_form.cleaned_data['inv_number_format']) if is_db_data is False else str(data.template_header_details.inv_number_format)
			item['estimate_nodate_label'] = str(data.invoice_template_headerconfig_form.cleaned_data[
				'estimate_nodate_label']) if is_db_data is False else str(data.template_header_details.estimate_nodate_label)
			item['item_font_size'] = str(data.invoice_template_itemsconfig_form.cleaned_data['font_size']) if is_db_data is False else str(int(data.template_item_details.font_size))
			item['sno_label'] = str(data.invoice_template_itemsconfig_form.cleaned_data['sno_label']) if is_db_data is False else str(data.template_item_details.sno_label)
			if data.invoice_template_itemsconfig_form.cleaned_data['sno_width'] is not None if is_db_data is False else str(int(data.template_item_details.sno_width)) is not None:
				item['sno_width'] = str(data.invoice_template_itemsconfig_form.cleaned_data['sno_width']) if is_db_data is False else str(int(data.template_item_details.sno_width))
			item['itemdetails_label'] = str(data.invoice_template_itemsconfig_form.cleaned_data['itemdetails_label']) if is_db_data is False else str(data.template_item_details.itemdetails_label)
			if data.invoice_template_itemsconfig_form.cleaned_data['itemdetails_width_hidden_field'] is not None if is_db_data is False else str(int(data.template_item_details.itemdetails_width)) is not None:
				item['itemdetails_width'] = str(data.invoice_template_itemsconfig_form.cleaned_data['itemdetails_width_hidden_field']) if is_db_data is False else str(int(data.template_item_details.itemdetails_width))
			item['itemcode_label'] = str(data.invoice_template_itemsconfig_form.cleaned_data['itemcode_label']) if is_db_data is False else str(data.template_item_details.itemcode_label)
			item['name_label'] = str(data.invoice_template_itemsconfig_form.cleaned_data['name_label']) if is_db_data is False else str(data.template_item_details.name_label)
			item['make_label'] = str(data.invoice_template_itemsconfig_form.cleaned_data['make_label']) if is_db_data is False else str(data.template_item_details.make_label)
			item['partno_label'] = str(data.invoice_template_itemsconfig_form.cleaned_data['partno_label']) if is_db_data is False else str(data.template_item_details.partno_label)
			item['oano_label'] = str(data.invoice_template_itemsconfig_form.cleaned_data['oano_label']) if is_db_data is False else str(data.template_item_details.oano_label)
			item['dc_no_label'] = str(data.invoice_template_itemsconfig_form.cleaned_data['dc_no_label']) if is_db_data is False else str(data.template_item_details.dc_no_label)
			item['description_label'] = str(data.invoice_template_itemsconfig_form.cleaned_data['description_label']) if is_db_data is False else str(data.template_item_details.description_label)
			item['remarks_label'] = str(data.invoice_template_itemsconfig_form.cleaned_data['remarks_label']) if is_db_data is False else str(data.template_item_details.remarks_label)
			item['hsnsac_label'] = str(data.invoice_template_itemsconfig_form.cleaned_data['hsnsac_label']) if is_db_data is False else str(data.template_item_details.hsnsac_label)
			if data.invoice_template_itemsconfig_form.cleaned_data['hsnsac_width'] is not None if is_db_data is False else str(int(data.template_item_details.hsnsac_width)) is not None:
				item['hsnsac_width'] = str(data.invoice_template_itemsconfig_form.cleaned_data['hsnsac_width']) if is_db_data is False else str(int(data.template_item_details.hsnsac_width))
			item['quantity_label'] = str(data.invoice_template_itemsconfig_form.cleaned_data['quantity_label']) if is_db_data is False else str(data.template_item_details.quantity_label)
			if data.invoice_template_itemsconfig_form.cleaned_data['quantity_width'] is not None if is_db_data is False else str(int(data.template_item_details.quantity_width)) is not None:
				item['quantity_width'] = str(data.invoice_template_itemsconfig_form.cleaned_data['quantity_width']) if is_db_data is False else str(int(data.template_item_details.quantity_width))
			item['units_label'] = str(data.invoice_template_itemsconfig_form.cleaned_data['units_label']) if is_db_data is False else str(data.template_item_details.units_label)
			if data.invoice_template_itemsconfig_form.cleaned_data['units_width'] is not None if is_db_data is False else str(int(data.template_item_details.units_width)) is not None:
				item['units_width'] = str(data.invoice_template_itemsconfig_form.cleaned_data['units_width']) if is_db_data is False else str(int(data.template_item_details.units_width))
			item['unit_price_label'] = str(data.invoice_template_itemsconfig_form.cleaned_data['unit_price_label']) if is_db_data is False else str(data.template_item_details.unit_price_label)
			if data.invoice_template_itemsconfig_form.cleaned_data['unit_price_width'] is not None if is_db_data is False else str(data.template_item_details.unit_price_width) is not None:
				item['unit_price_width'] = str(data.invoice_template_itemsconfig_form.cleaned_data['unit_price_width']) if is_db_data is False else str(int(data.template_item_details.unit_price_width))
			item['discount_label'] = str(data.invoice_template_itemsconfig_form.cleaned_data['discount_label']) if is_db_data is False else str(data.template_item_details.discount_label)
			if data.invoice_template_itemsconfig_form.cleaned_data['discount_width'] is not None if is_db_data is False else str(int(data.template_item_details.discount_width)) is not None:
				item['discount_width'] = str(data.invoice_template_itemsconfig_form.cleaned_data['discount_width']) if is_db_data is False else str(int(data.template_item_details.discount_width))
			item['taxable_amount_label'] = str(data.invoice_template_itemsconfig_form.cleaned_data['taxable_amount_label']) if is_db_data is False else str(data.template_item_details.taxable_amount_label)
			if data.invoice_template_itemsconfig_form.cleaned_data['taxable_amount_width'] is not None if is_db_data is False else str(int(data.template_item_details.taxable_amount_width)) is not None:
				item['taxable_amount_width'] = str(data.invoice_template_itemsconfig_form.cleaned_data['taxable_amount_width']) if is_db_data is False else str(int(data.template_item_details.taxable_amount_width))
			if data.invoice_template_itemsconfig_form.cleaned_data['taxrate_width'] is not None if is_db_data is False else str(data.template_item_details.taxrate_width) is not None:
				item['taxrate_width'] = str(data.invoice_template_itemsconfig_form.cleaned_data['taxrate_width']) if is_db_data is False else str(int(data.template_item_details.taxrate_width))
			if data.invoice_template_itemsconfig_form.cleaned_data['taxamount_width'] is not None if is_db_data is False else str(int(data.template_item_details.taxamount_width)) is not None:
				item['taxamount_width'] = str(data.invoice_template_itemsconfig_form.cleaned_data['taxamount_width']) if is_db_data is False else str(int(data.template_item_details.taxamount_width))
			item['summary_font_size'] = str(data.invoice_template_summaryconfig_form.cleaned_data['font_size']) if is_db_data is False else str(int(data.template_summary_details.font_size))
			item['hsn_tax_font_size'] = str(data.invoice_template_summaryconfig_form.cleaned_data['hsn_tax_font_size']) if is_db_data is False else str(int(data.template_summary_details.hsn_tax_font_size))
			item['misc_font_size'] = str(data.invoice_template_miscconfig_form.cleaned_data['font_size']) if is_db_data is False else str(int(data.template_misc_details.font_size))
			item['notes'] = str(data.invoice_template_miscconfig_form.cleaned_data['notes']) if is_db_data is False else str(data.template_misc_details.notes)
			item['foot_note'] = str(data.invoice_template_miscconfig_form.cleaned_data['foot_note']) if is_db_data is False else str(data.template_misc_details.foot_note)
			item['invoice_number_font_size'] = str(data.invoice_template_headerconfig_form.cleaned_data['invoice_no_font_size']) if is_db_data is False else str(int(data.template_header_details.invoice_number_font_size))
			response['label_fields'].append(item)

			item = dict()
			item['include_logo'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_logo']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_logo) is 1 else 'UnChecked')
			item['include_name'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_name']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_name) is 1 else 'UnChecked')
			item['include_address'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_address']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_address) is 1 else 'UnChecked')
			item['include_email'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_email']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_email) is 1 else 'UnChecked')
			item['include_phone_no'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_phone_no']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_phone_no) is 1 else 'UnChecked')
			item['include_fax'] = ('Checked' if bool(
				data.invoice_template_headerconfig_form.cleaned_data['include_fax']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_fax) is 1 else 'UnChecked')
			item['include_partycode'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_partycode']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_partycode) is 1 else 'UnChecked')
			item['include_partyname'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_partyname']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_partyname) is 1 else 'UnChecked')
			item['include_billingaddress'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_billingaddress']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_billingaddress) is 1 else 'UnChecked')
			item['include_shippingaddress'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_shippingaddress']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_shippingaddress) is 1 else 'UnChecked')
			item['include_party_email'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_party_email']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_party_email) is 1 else 'UnChecked')
			item['include_party_phoneno'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_party_phoneno']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_party_phoneno) is 1 else 'UnChecked')
			item['include_paymentterms'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_paymentterms']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_paymentterms) is 1 else 'UnChecked')
			item['include_pono'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_pono']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_pono) is 1 else 'UnChecked')
			item['include_podate'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_podate']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_podate) is 1 else 'UnChecked')
			item['include_transportmode'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_transportmode']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_transportmode) is 1 else 'UnChecked')
			item['include_splinstructions'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_splinstructions']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_splinstructions) is 1 else 'UnChecked')
			item['include_roadpermitno'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_roadpermitno']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_roadpermitno) is 1 else 'UnChecked')
			item['include_lrnodate'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_lrnodate']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_lrnodate) is 1 else 'UnChecked')
			item['include_packingslipno'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_packingslipno']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_packingslipno) is 1 else 'UnChecked')
			item['include_packingdescription'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_packingdescription']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_packingdescription) is 1 else 'UnChecked')
			item['include_autorizedsign'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_authorizedsign']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_authorizedsign) is 1 else 'UnChecked')
			item['include_approver_signature'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_approver_signature']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_approver_signature) is 1 else 'UnChecked')
			item['include_preparedsign'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_preparedsign']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_preparedsign) is 1 else 'UnChecked')
			item['include_estimate_nodate'] = ('Checked' if bool(data.invoice_template_headerconfig_form.cleaned_data['include_estimate_nodate']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_header_details.include_estimate_nodate) is 1 else 'UnChecked')
			item['include_sno'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_sno']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_sno) is 1 else 'UnChecked')
			item['include_itemcode'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_itemcode']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_itemcode) is 1 else 'UnChecked')
			item['include_make'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_make']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_make) is 1 else 'UnChecked')
			item['include_partno'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_partno']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_partno) is 1 else 'UnChecked')
			item['include_oano'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_oano']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_oano) is 1 else 'UnChecked')
			item['include_dc_no'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_dc_no']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_dc_no) is 1 else 'UnChecked')
			item['include_dc_qty'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_dc_qty']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_dc_qty) is 1 else 'UnChecked')
			item['include_dc_date'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_dc_date']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_dc_date) is 1 else 'UnChecked')
			item['include_description'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_description']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_description) is 1 else 'UnChecked')
			item['include_remarks'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_remarks']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_remarks) is 1 else 'UnChecked')
			item['include_hsnsac'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_hsnsac']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_hsnsac) is 1 else 'UnChecked')
			item['hsnsac_part_of_itemdetails'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data[
				'hsnsac_part_of_itemdetails']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.hsnsac_part_of_itemdetails) is 1 else 'UnChecked')
			item['include_quantity'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_quantity']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_quantity) is 1 else 'UnChecked')
			item['include_units'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_units']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_units) is 1 else 'UnChecked')
			item['units_in_quantity_column'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['units_in_quantity_column']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.units_in_quantity_column) is 1 else 'UnChecked')
			item['include_primary_qty'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_primary_qty']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_primary_qty) is 1 else 'UnChecked')
			item['include_unit_price'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_unit_price']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_unit_price) is 1 else 'UnChecked')
			item['include_discount'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_discount']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_discount) is 1 else 'UnChecked')
			item['include_taxable_amount'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_taxable_amount']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_taxable_amount) is 1 else 'UnChecked')
			item['include_tax'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_tax']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_tax) is 1 else 'UnChecked')
			item['include_taxrate'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_taxrate']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_taxrate) is 1 else 'UnChecked')
			item['include_taxamount'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_taxamount']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_taxamount) is 1 else 'UnChecked')
			item['show_tax_for_dc'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['show_tax_for_dc']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.show_tax_for_dc) is 1 else 'UnChecked')
			item['include_row_separator'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_row_separator']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_row_separator) is 1 else 'UnChecked')
			item['include_column_separator'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_column_separator']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_column_separator) is 1 else 'UnChecked')
			item['include_alternate_row_shading'] = ('Checked' if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_alternate_row_shading']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_item_details.include_alternate_row_shading) is 1 else 'UnChecked')
			item['include_total'] = ('Checked' if bool(data.invoice_template_summaryconfig_form.cleaned_data['include_total']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_summary_details.include_total) is 1 else 'UnChecked')
			item['include_subtotal'] = ('Checked' if bool(data.invoice_template_summaryconfig_form.cleaned_data['include_subtotal']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_summary_details.include_subtotal) is 1 else 'UnChecked')
			item['include_qty_total'] = ('Checked' if bool(data.invoice_template_summaryconfig_form.cleaned_data['include_qty_total']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_summary_details.include_qty_total) is 1 else 'UnChecked')
			item['include_total_in_words'] = ('Checked' if bool(data.invoice_template_summaryconfig_form.cleaned_data['include_total_in_words']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_summary_details.include_total_in_words) is 1 else 'UnChecked')
			item['include_hsn_summary'] = ('Checked' if bool(data.invoice_template_summaryconfig_form.cleaned_data['include_hsn_summary']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_summary_details.include_hsn_summary) is 1 else 'UnChecked')
			item['include_page_no_in_footer'] = ('Checked' if bool(data.invoice_template_miscconfig_form.cleaned_data['include_page_no_in_footer']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_misc_details.include_page_no_in_footer) is 1 else 'UnChecked')
			item['include_first_page_summary'] = ('Checked' if bool(data.invoice_template_miscconfig_form.cleaned_data['include_first_page_summary']) is True else 'UnChecked') if is_db_data is False else ('Checked' if int(data.template_misc_details.include_first_page_summary) is 1 else 'UnChecked')
			response['checkbox_fields'].append(item)

			item = dict()
			item['sort_order'] = self.sort_order[data.invoice_template_itemsconfig_form.cleaned_data['item_sort_order']] if is_db_data is False else self.sort_order[str(data.template_item_details.item_sort_order)]
			response['item_sort_order'].append(item)

			item = dict()
			item['tax_payable_on_reverse_charge'] = self.tax_payable_on_reverse_charge[data.invoice_template_itemsconfig_form.cleaned_data['tax_payable_on_reverse_charge']] if is_db_data is False else self.tax_payable_on_reverse_charge[str(data.template_item_details.tax_payable_on_reverse_charge)]
			response['tax_payable_on_reverse_charge'].append(item)

			item = dict()
			item['scan_option'] = self.scan_option[data.invoice_template_headerconfig_form.cleaned_data['scan_code_option']] if is_db_data is False else self.scan_option[str(data.template_header_details.scan_code_option)]
			response['scan_code_option'].append(item)

			if is_db_data is False:
				if bool(data.invoice_template_itemsconfig_form.cleaned_data['include_tax']) is True:
					item = dict()
					item['tax_type'] = self.tax_types[
						data.invoice_template_itemsconfig_form.cleaned_data['tax_type']] if is_db_data is False else \
					self.tax_types[str(data.template_item_details.tax_type)]
					response['tax'].append(item)
			else:
				if int(data.template_item_details.include_tax) is 1:
					item = dict()
					item['tax_type'] = self.tax_types[
						data.invoice_template_itemsconfig_form.cleaned_data['tax_type']] if is_db_data is False else \
						self.tax_types[str(data.template_item_details.tax_type)]
					response['tax'].append(item)
			# item['included_reg_items'] = data.invoice_template_headerconfig_form.cleaned_data['included_reg_items']
		except Exception as e:
			logger.exception(e)
		return response

	def queryInsert(self, user_id=None, enterprise_id=None, data=None, is_db_data =False):
		"""
		:param user_id:
		:param enterprise_id:
		:param data:
		:return:
		"""
		response = {}
		try:
			user = getUser(enterprise_id=enterprise_id, user_id=user_id)
			formatted_data = self.buildInsertDataStructure(user=user, data=data, is_db_data=is_db_data)
			response = self.insert(id=enterprise_id, enterprise_id=enterprise_id, data=formatted_data)
		except Exception as e:
			logger.exception(e)
		return response

	def fetchLogList(self, eid=None, enterprise_id=None, offset=None, limit=None):
		"""
		get listed voucher log data
		:param voucher_id:
		:param enterprise_id:
		:return:
		"""
		response = {}
		result = []
		try:
			query = self.fetchLog(id=int(eid), enterprise_id=int(enterprise_id), offset=offset, limit=limit)
			for voucher in query:
				voucher['created_at'] = str(voucher['created_at'])
				voucher['log']['modified_at'] = str(voucher['log']['modified_at'])
				voucher['log']['preference'] = 1 if 'preference' in voucher['log'] else 0
				result.append(voucher)
			response = response_code.success() if len(result) > 0 else response_code.failure()
			response['data'] = result
		except Exception as e:
			logger.exception(e)
		return response

	def fetchLogData(self, eid=None, enterprise_id=None, modified_at=None):
		"""
		get individual log data
		:param eid:
		:param enterprise_id:
		:param modified_at:
		:return:
		"""
		response = response_code.failure()
		result = []
		try:
			query = self.fetchLogDetails(id=int(eid), enterprise_id=int(enterprise_id), identifier=modified_at)
			for template_config in query:
				del template_config['log']['modified_at']
				result.append(template_config)
			response = response_code.success() if len(result) > 0 else response
			if len(result) > 1:
				response['data'] = self.buildLogData(result[1]['log'], result[0]['log'])
			elif len(result) > 0:
				response['data'] = self.buildLogData(None, result[0]['log'])
			response['data'] = response['data'] if len(response['data']) > 0 else ['No change detected']
		except Exception as e:
			logger.exception(e)
			response = response_code.failure()
		return response

	def fieldLogData(self, key=None, title=None, old=None, new=None):
		"""

		:param key:
		:param title:
		:param old:
		:param new:
		:return:
		"""
		if old is not None and key in old:
			if type(new[key]) == list and type(old[key]) == list:
				diff = DeepDiff(old[key], new[key], ignore_order=True) if type(new[key]) == list and len(old[key]) != len(new[key]) else DeepDiff(old[key], new[key])
				if 'iterable_item_added' in diff:
					for item_added in diff['iterable_item_added'].items():
						if type(self.module_data_set[key]) is not str:
							log = '<b>%s</b> added with following values: <br />' % self.module_data_set[key]['title']
							for i, elem in self.module_data_set[key].items():
								log += ' <b>%s</b>: <i>%s</i>' % (elem, ', '.join(str(l_data) for l_data in item_added[1][i]) if type(item_added[1][i]) == list else str(item_added[1][i])) if i is not 'title' else ''
							self.response_log.append('%s' % log)
						else:
							self.response_log.append('%s named <b>%s</b> was added' % (self.module_data_set[key], item_added[1]))

				if 'iterable_item_removed' in diff:
					for item_removed in diff['iterable_item_removed'].items():
						if type(self.module_data_set[key]) is not str:
							log = '<b>%s</b> removed with following values: <br />' % self.module_data_set[key]['title']
							for i, elem in self.module_data_set[key].items():
								log += ' <b>%s</b>: <i>%s</i>' % (elem, ', '.join(
									str(l_data) for l_data in item_removed[1][i]) if type(
									item_removed[1][i]) == list else str(item_removed[1][i])) if i is not 'title' else ''
							self.response_log.append('%s' % log)
						else:
							self.response_log.append(
								'%s named <b>%s</b> was removed' % (self.module_data_set[key], item_removed[1]))
				if 'values_changed' in diff:
					for item in diff['values_changed'].keys():
						var_chg = re.findall("\[(.*?)\]", item.replace('\'', ''))
						self.response_log.append('<b>%s</b> has changed from <b>%s</b> to <b>%s</b>' % (self.module_data_set[key][var_chg[1]].capitalize(), old[key][int(var_chg[0])][var_chg[1]], new[key][int(var_chg[0])][var_chg[1]]))
			else:
				diff = DeepDiff(old[key], new[key])
				if 'values_changed' in diff:
					if str(diff['values_changed']['root']['old_value']) == '' and str(
							diff['values_changed']['root']['new_value']) != '':
						self.response_log.append(
							'%s <b>%s</b> added' % (title, diff['values_changed']['root']['new_value']))
					elif str(diff['values_changed']['root']['new_value']) == '' and str(
							diff['values_changed']['root']['old_value']) != '':
						self.response_log.append(
							'%s <b>%s</b> removed' % (title, diff['values_changed']['root']['new_value']))
					else:
						self.response_log.append('%s changed from <b>%s</b> to <b>%s</b>' % (
							title,
							diff['values_changed']['root']['old_value'],
							diff['values_changed']['root']['new_value']))
		elif key in new:
			if type(new[key]) == list:
				if type(title) == dict:
					for item in new[key]:
						log = '<b>%s</b> added with following values: <br />' % self.module_data_set[key]['title']
						for i, elem in self.module_data_set[key].items():
							log += ' <b>%s</b>: <i>%s</i>' % (elem, ', '.join(str(l_data) for l_data in item[i]) if type(item[i]) == list else str(item[i])) if i is not 'title' else ''
						self.response_log.append('%s' % log)
				else:
					self.response_log.append('%s <b>%s</b> added' % (title, ', '.join(str(l_data) for l_data in new[key])))
			elif new[key] != '':
				self.response_log.append('%s <b>%s</b> added' % (title, new[key]))
		logger.info('Template LOG DATA updated %s' % self.response_log)
		return False

	def buildLogData(self, old=None, new=None):
		"""

		:param old:
		:param new:
		:return:
		"""
		for key, title in self.module_data_set.items():
			self.fieldLogData(key=key, title=title, old=old, new=new)
		return self.response_log


class PurchaseTemplateChangelog(ChangeLog):
	"""
	Changelog to do mongodb operations
	"""
	voucher = {}
	collection = 'purchase_template_config_data'
	uid = "config_id"

	def __init__(self):
		super(PurchaseTemplateChangelog, self).__init__(collection=self.collection, uid=self.uid)
		self.response_log = []
		self.tax_types = {
			'1': 'COLUMNS',
			'2': 'CONSOLIDATED SUMMARY',
			'3': 'PART OF ITEM DETAILS',
			'4': 'RATE & AMOUNT IN ONE COLUMN'
		}
		self.template_id = {
			'1': 'Compact',
			'2': 'Simple',
		}
		# self.sort_order = {
		# 	'1': 'ORDER OF ENTRY',
		# 	'2': 'ITEM NAME',
		# 	'3': 'ITEM CODE/ DRAWING NO'
		# }
		self.module_data_set = {
			'checkbox_fields': {
				'title': 'Check Box',
				'include_logo': 'Company Logo',
				'include_name': 'Company Name',
				'include_address': 'Company Address',
				'include_email': 'Company Email',
				'include_phone_no': 'Company Phone_no',
				'include_fax': 'Company Fax',
				'include_partycode': 'Party Code',
				'include_partyname': 'Party Name',
				'include_partyaddress': 'Party Address',
				'include_party_email': 'Party Email',
				'include_party_phoneno': 'Party Phone_no',
				'include_billingaddress': 'Billing Address',
				'include_shippingaddress': 'Shipping Address',
				'include_party_acknowledgement': 'Party Acknowledgement',
				'include_indentno': 'Indent No',
				'include_indentdate': 'Indent Date',
				'include_currency': 'Currency',
				'include_quotn_refno': 'Quotn Ref_No',
				'include_quotn_date': 'Quotn Date',
				'include_paymentterms': 'Payment Terms',
				'include_splinstructions': 'Special Instructions',
				'include_preparedby': 'Prepared By',
				'include_authorized_sign': 'Authorized Sign',
				'include_approver_signature': 'Approver Signature',
				'include_preparedby_signature': 'Prepared By Signature',
				'include_sno': 'S.No',
				'include_itemcode': 'Item Code',
				'include_make': 'Make',
				'include_partno': 'Part No',
				'include_description': 'Item Description',
				'include_hsnsac': 'Hsn/Sac',
				'hsnsac_part_of_itemdetails': 'Include Hsn/Sac As A Part Of Item Details',
				'include_quantity': 'Quantity',
				'include_units': 'Units',
				'units_in_quantity_column': 'Include Units In QTY Column',
				'include_unit_price': 'Unit Price',
				'include_discount': 'Discount',
				'include_taxable_amount': 'Taxable Amount',
				'include_tax': 'Tax',
				'include_taxrate': 'Tax Rate',
				'include_taxamount': 'Tax Amount',
				'include_row_separator': 'Row Separator',
				'include_column_separator': 'Column Separator',
				'include_alternate_row_shading': 'Alternate Row Shading',
				'include_total': 'Show Total Section',
				'include_subtotal': 'Sub Total',
				'include_qty_total': 'Quantity Total',
				'include_total_in_words': 'Total Value In Words',
				'include_hsn_summary': 'Hsn Summary',
				'include_delivery_schedule': 'Delivery Schedule',
				'include_delivery_schedule_sno': 'Sno In Delivery Schedule',
				'include_page_no': 'Page Number',
				'include_summary': 'Summary In First Page',
			},
			'label_fields': {
				'title': 'Labels',
				'base_font': 'Base Font',
				'margin_top': 'Page Margin Top',
				'margin_bottom': 'Page Margin Bottom',
				'margin_left': 'Page Margin Left',
				'margin_right': 'Page Margin Right',
				'datetime':'Date Time Format',
				'logo_size': 'Logo Size',
				'name_font': 'Name Font',
				'name_font_size': 'Name Font Size',
				'form_name_font_size': 'Form Name Font Size',
				'po_formnamelabel': 'PO Form Name Label',
				'jo_formnamelabel': 'JO Form Name Label',
				'basic_info_font_size': 'Basic Info Font Size',
				'billingaddress_label': 'Billing Address Label',
				'shippingaddress_label': 'Shipping Address Label',
				'party_name_label': 'Party Name Label',
				'party_address_label': 'Party Address Label',
				'party_code_label': 'Party Code Label',
				'party_acknowledgement': 'Party Acknowledgement Label',
				'pono_label': 'PO No Label',
				'jono_label': 'JO No Label',
				'po_date_label': 'PO Date Label',
				'indent_no_label': 'Indent No Label',
				'indent_date_label': 'Indent Date Label',
				'currency_label': 'Currency Label',
				'quotn_refno_label': 'Quotn Ref No Label',
				"qoutn_date_label":"Quotn Date Label",
				"payments_terms_label":"Payments Terms Label",
				"special_instructions_label":"Special Instructions Label",
				"preparedby_sign_label":"Prepared By Sign Label",
				"authorized_sign_label":"Authorized Sign Label",
				'item_font_size': 'Item Table Font Size',
				'sno_label': 'S.No Label',
				'sno_width': 'S.No Width',
				'itemdetails_label': 'Item Details Label',
				'itemdetails_width': 'Item Details Width',
				'itemcode_label': 'Item Code Label',
				'item_name_label': 'Item Name Label',
				'make_label': 'Make Label',
				'partno_label': 'Part No Label',
				'item_description_label': 'Item Description Label',
				'hsnsac_label': 'Hsn/Sac Label',
				'hsnsac_width': 'Hsn/Sac Width',
				'quantity_label': 'Quantity Label',
				'quantity_width': 'Quantity Width',
				'units_label': 'Units Label',
				'units_width': 'Units Width',
				'unit_price_label': 'Unit Price Label',
				'unit_price_width': 'Unit Price Width',
				'discount_label': 'Discount Label',
				'discount_width': 'Discount Width',
				'taxable_amount_label': 'Taxable Amount Label',
				'taxable_amount_width': 'Taxable Amount Width',
				'taxrate_width': 'Tax Rate Width',
				'taxamount_width': 'Tax Amount Width',
				'total_font_size':'Total Font Size',
				'hsn_summary_font_size':'HSN Summary Font Size',
				'delivery_schedule_font_size':'Delivery Schedule Font Size',
				'delivery_schedule_sno_label':'Delivery Schedule Sno Label',
				'delivery_schedule_sno_width':'Delivery Schedule Sno Width',
				'delivery_schedule_itemdetails_label':'Delivery Schedule Itemdetails Label',
				'delivery_schedule_itemdetails_width':'Delivery Schedule Itemdetails Width',
				'delivery_schedule_qty_label':'Delivery Schedule Qty Label',
				'delivery_schedule_qty_width':'Delivery Schedule Qty Width',
				'delivery_schedule_date_label':'Delivery Schedule Date Label',
				'delivery_schedule_date_width':'Delivery Schedule Date Width',
				'mail_body':'Mail Body',
				'sub':'Subject',
				'notes':'Notes',
				'foot_font_size':'Foot Font Size',
				'foot_note':'Foot Note'
			},
			'tax': {
				'title': 'Tax Type',
				'tax_type': 'Tax type'
			},
			'template_view': {
				'title': 'Template view',
				'template_name': 'Template view'
			}
			# 'item_sort_order': {
			# 	'title': 'Sort Order',
			# 	'sort_order': 'Item Sort Order'
			# },
			# 'included_reg_items': {'title': 'Included_reg_items'}
		}
		logger.info('PuechaseTemplateChangelog __init__')

	def buildInsertDataStructure(self, user=None, data=None):
		"""

		:param user:
		:param data:
		:return:
		"""
		response = dict()
		response['modified_at'] = datetime.now()
		response['checkbox_fields'] = []
		response['label_fields'] = []
		response['tax'] = []
		response['template_view'] = []
		# response['item_sort_order'] = []
		try:
			# primary contact
			response['username'] = [user.first_name, user.last_name]
			item = dict()
			item['template_name'] = self.template_id[str(data['template_id'])]
			response['template_view'].append(item)

			item = dict()
			item['base_font'] = str(data['general_config']['font_family'])
			item['margin_top'] = str(data['general_config']['margin']['top'])
			item['margin_bottom'] = str(data['general_config']['margin']['bottom'])
			item['margin_right'] = str(data['general_config']['margin']['right'])
			item['margin_left'] = str(data['general_config']['margin']['left'])
			po_date_time_format = str(data['general_config']['datetime'])
			char_replace={'%d': 'D','%m':'MM','%b':'MMM','%B':'MMMM','%y':'YY','%Y':'YYYY','%H':'HH','%I':'hh','%M':'mm','%S':'ss'}
			for key, value in char_replace.items():
				po_date_time_format = po_date_time_format.replace(key, value)
			item['datetime'] = po_date_time_format
			item['logo_size'] = str(data['header_config']['company']['logo']['size'])
			item['name_font'] = str(data['header_config']['company']['company_name']['font_family'])
			item['name_font_size'] = str(data['header_config']['company']['company_name']['font_size'])
			item['form_name_font_size'] = str(data['header_config']['form_name']['font_size'])
			item['po_formnamelabel'] = str(data['header_config']['form_name']['label']['po'])
			item['jo_formnamelabel'] = str(data['header_config']['form_name']['label']['jo'])
			item['basic_info_font_size'] = str(data['header_config']['font_size'])
			item['billingaddress_label'] = str(data['header_config']['billing_address']['label'])
			item['shippingaddress_label'] = str(data['header_config']['shipping_address']['label'])
			item['party_name_label'] = str(data['header_config']['vendor']['name']['label'])
			item['party_address_label'] = str(data['header_config']['vendor']['address']['label'])
			item['party_code_label'] = str(data['header_config']['vendor']['code']['label'])
			item['party_acknowledgement'] = str(data['header_config']['vendor']['acknowledge']['label'])
			item['pono_label'] = str(data['header_config']['field_name']['pono_label'])
			item['jono_label'] = str(data['header_config']['field_name']['jono_label'])
			item['po_date_label'] = str(data['header_config']['field_name']['po_datelabel'])
			item['indent_no_label'] = str(data['header_config']['field_name']['indent_no']['label'])
			item['indent_date_label'] = str(data['header_config']['field_name']['indent_date']['label'])
			item['currency_label'] = str(data['header_config']['field_name']['currency']['label'])
			item['quotn_refno_label'] = str(data['header_config']['field_name']['quotn_ref_no']['label'])
			item['qoutn_date_label'] = str(data['header_config']['field_name']['quotn_date']['label'])
			item['payments_terms_label'] = str(data['header_config']['terms_conditions']['payment_terms']['label'])
			item['special_instructions_label'] = str(data['header_config']['terms_conditions']['special_instructions']['label'])
			item['preparedby_sign_label'] = str(data['header_config']['field_name']['prepared_by']['label'])
			item['authorized_sign_label'] = str(data['header_config']['field_name']['authorized_sign']['label'])
			item['item_font_size'] = str(data['items_config']['item_table']['font_size'])
			item['sno_label'] = str(data['items_config']['item_table']['sno']['label'])
			item['sno_width'] = str(data['items_config']['item_table']['sno']['width'])
			item['itemdetails_label'] = str(data['items_config']['item_table']['item_details']['label'])
			item['itemdetails_width'] = str(data['items_config']['item_table']['item_details']['width'])
			item['itemcode_label'] = str(data['items_config']['item_table']['item_details']['itemcode']['label'])
			item['item_name_label'] = str(data['items_config']['item_table']['item_details']['itemname_label'])
			item['make_label'] = str(data['items_config']['item_table']['item_details']['make']['label'])
			item['partno_label'] = str(data['items_config']['item_table']['item_details']['part_no']['label'])
			item['item_description_label'] = str(data['items_config']['item_table']['item_details']['description']['label'])
			item['hsnsac_label'] = str(data['items_config']['item_table']['hsnsac']['label'])
			item['hsnsac_width'] = str(data['items_config']['item_table']['hsnsac']['width'])
			item['quantity_label'] = str(data['items_config']['item_table']['quantity']['label'])
			item['quantity_width'] = str(data['items_config']['item_table']['quantity']['width'])
			item['units_label'] = str(data['items_config']['item_table']['units']['label'])
			item['units_width'] = str(data['items_config']['item_table']['units']['width'])
			item['unit_price_label'] = str(data['items_config']['item_table']['unit_price']['label'])
			item['unit_price_width'] = str(data['items_config']['item_table']['unit_price']['width'])
			item['discount_label'] = str(data['items_config']['item_table']['discount']['label'])
			item['discount_width'] = str(data['items_config']['item_table']['discount']['width'])
			item['taxable_amount_label'] = str(data['items_config']['item_table']['taxable_amount']['label'])
			item['taxable_amount_width'] = str(data['items_config']['item_table']['taxable_amount']['width'])
			item['taxrate_width'] = str(data['items_config']['item_table']['tax']['taxrate']['width'])
			item['taxamount_width'] = str(data['items_config']['item_table']['tax']['taxamount']['width'])
			item['total_font_size'] = str(data['summary_config']['totals']['font_size'])
			item['hsn_summary_font_size'] = str(data['summary_config']['hsn_summary']['font_size'])
			item['delivery_schedule_font_size'] = str(data['summary_config']['delivery_schedules_fields']['delivery_schedules']['font_size'])
			item['delivery_schedule_sno_label'] = str(data['summary_config']['delivery_schedules_fields']['sno']['label'])
			item['delivery_schedule_sno_width'] = str(data['summary_config']['delivery_schedules_fields']['sno']['width'])
			item['delivery_schedule_itemdetails_label'] = str(data['summary_config']['delivery_schedules_fields']['description']['label'])
			item['delivery_schedule_itemdetails_width'] = str(data['summary_config']['delivery_schedules_fields']['description']['width'])
			item['delivery_schedule_qty_label'] = str(data['summary_config']['delivery_schedules_fields']['quantity']['label'])
			item['delivery_schedule_qty_width'] = str(data['summary_config']['delivery_schedules_fields']['quantity']['width'])
			item['delivery_schedule_date_label'] = str(data['summary_config']['delivery_schedules_fields']['delivery_date']['label'])
			item['delivery_schedule_date_width'] = str(data['summary_config']['delivery_schedules_fields']['delivery_date']['width'])
			item['mail_body'] = str(data['misc_config']['mail_body'])
			item['sub'] = str(data['misc_config']['sub'])
			item['notes'] = str(data['misc_config']['notes'])
			item['foot_font_size'] = str(data['misc_config']['foot_note']['font_size'])
			item['foot_note'] = str(data['misc_config']['foot_note']['notes'])
			response['label_fields'].append(item)

			item = dict()
			item['include_logo'] = ('Checked' if bool(data['header_config']['company']['logo']['print']) is True else 'UnChecked')
			item['include_name'] = ('Checked' if bool(data['header_config']['company']['company_name']['print']) is True else 'UnChecked')
			item['include_address'] = ('Checked' if bool(data['header_config']['company_info']['print_address']) is True else 'UnChecked')
			item['include_email'] = ('Checked' if bool(data['header_config']['company_info']['print_email']) is True else 'UnChecked')
			item['include_phone_no'] = ('Checked' if bool(data['header_config']['company_info']['print_phone_no']) is True else 'UnChecked')
			item['include_fax'] = ('Checked' if bool(data['header_config']['company_info']['print_fax']) is True else 'UnChecked')
			item['include_partycode'] = ('Checked' if bool(data['header_config']['vendor']['code']['print']) is True else 'UnChecked')
			item['include_partyname'] = ('Checked' if bool(data['header_config']['vendor']['name']['print']) is True else 'UnChecked')
			item['include_partyaddress'] = ('Checked' if bool(data['header_config']['vendor']['address']['print']) is True else 'UnChecked')
			item['include_party_email'] = ('Checked' if bool(data['header_config']['vendor']['print_email']) is True else 'UnChecked')
			item['include_party_phoneno'] = ('Checked' if bool(data['header_config']['vendor']['print_phone_no']) is True else 'UnChecked')
			item['include_billingaddress'] = ('Checked' if bool(data['header_config']['billing_address']['print']) is True else 'UnChecked')
			item['include_shippingaddress'] = ('Checked' if bool(data['header_config']['shipping_address']['print']) is True else 'UnChecked')
			item['include_party_acknowledgement'] = ('Checked' if bool(data['header_config']['vendor']['acknowledge']['print']) is True else 'UnChecked')
			item['include_indentno'] = ('Checked' if bool(data['header_config']['field_name']['indent_no']['print']) is True else 'UnChecked')
			item['include_indentdate'] = ('Checked' if bool(data['header_config']['field_name']['indent_date']['print']) is True else 'UnChecked')
			item['include_currency'] = ('Checked' if bool(data['header_config']['field_name']['currency']['print']) is True else 'UnChecked')
			item['include_quotn_refno'] = ('Checked' if bool(data['header_config']['field_name']['quotn_ref_no']['print']) is True else 'UnChecked')
			item['include_quotn_date'] = ('Checked' if bool(data['header_config']['field_name']['quotn_date']['print']) is True else 'UnChecked')
			item['include_paymentterms'] = ('Checked' if bool(data['header_config']['terms_conditions']['payment_terms']['print']) is True else 'UnChecked')
			item['include_splinstructions'] = ('Checked' if bool(data['header_config']['terms_conditions']['special_instructions']['print']) is True else 'UnChecked')
			item['include_preparedby'] = ('Checked' if bool(data['header_config']['field_name']['prepared_by']['print']) is True else 'UnChecked')
			item['include_authorized_sign'] = ('Checked' if bool(data['header_config']['field_name']['prepared_by']['print']) is True else 'UnChecked')
			item['include_approver_signature'] = ('Checked' if bool(data['header_config']['field_name']['authorized_sign']['print']) is True else 'UnChecked')
			item['include_preparedby_signature'] = ('Checked' if bool(data['header_config']['field_name']['prepared_by']['print_approversign']) is True else 'UnChecked')
			item['include_sno'] = ('Checked' if bool(data['items_config']['item_table']['sno']['print']) is True else 'UnChecked')
			item['include_itemcode'] = ('Checked' if bool(data['items_config']['item_table']['item_details']['itemcode']['print']) is True else 'UnChecked')
			item['include_make'] = ('Checked' if bool(data['items_config']['item_table']['item_details']['make']['print']) is True else 'UnChecked')
			item['include_partno'] = ('Checked' if bool(data['items_config']['item_table']['item_details']['part_no']['print']) is True else 'UnChecked')
			item['include_description'] = ('Checked' if bool(data['items_config']['item_table']['item_details']['description']['print']) is True else 'UnChecked')
			item['include_hsnsac'] = ('Checked' if bool(data['items_config']['item_table']['hsnsac']['print']) is True else 'UnChecked')
			item['hsnsac_part_of_itemdetails'] = ('Checked' if bool(data['items_config']['item_table']['hsnsac']['print_in_itemdetails']) is True else 'UnChecked')
			item['include_quantity'] = ('Checked' if bool(data['items_config']['item_table']['quantity']['print']) is True else 'UnChecked')
			item['include_units'] = ('Checked' if bool(data['items_config']['item_table']['units']['print']) is True else 'UnChecked')
			item['units_in_quantity_column'] = ('Checked' if bool(data['items_config']['item_table']['units']['units_in_qty']) is True else 'UnChecked')
			item['include_unit_price'] = ('Checked' if bool(data['items_config']['item_table']['unit_price']['print']) is True else 'UnChecked')
			item['include_discount'] = ('Checked' if bool(data['items_config']['item_table']['discount']['print']) is True else 'UnChecked')
			item['include_taxable_amount'] = ('Checked' if bool(data['items_config']['item_table']['taxable_amount']['print']) is True else 'UnChecked')
			item['include_tax'] = ('Checked' if bool(data['items_config']['item_table']['tax']['print']) is True else 'UnChecked')
			item['include_taxrate'] = ('Checked' if bool(data['items_config']['item_table']['tax']['taxrate']['print']) is True else 'UnChecked')
			item['include_taxamount'] = ('Checked' if bool(data['items_config']['item_table']['tax']['taxamount']['print']) is True else 'UnChecked')
			item['include_row_separator'] = ('Checked' if bool(data['items_config']['field_alignment']['separator']['row_separator']) is True else 'UnChecked')
			item['include_column_separator'] = ('Checked' if bool(data['items_config']['field_alignment']['separator']['column_separator']) is True else 'UnChecked')
			item['include_alternate_row_shading'] = ('Checked' if bool(data['items_config']['field_alignment']['separator']['alternative_row_shading']) is True else 'UnChecked')
			item['include_total'] = ('Checked' if bool(data['summary_config']['totals']['print']) is True else 'UnChecked')
			item['include_subtotal'] = ('Checked' if bool(data['summary_config']['totals']['print_subtotal']) is True else 'UnChecked')
			item['include_qty_total'] = ('Checked' if bool(data['summary_config']['totals']['print_qtytotal']) is True else 'UnChecked')
			item['include_total_in_words'] = ('Checked' if bool(data['summary_config']['totals']['print_total_in_words']) is True else 'UnChecked')
			item['include_hsn_summary'] = ('Checked' if bool(data['summary_config']['hsn_summary']['print']) is True else 'UnChecked')
			item['include_delivery_schedule'] = ('Checked' if bool(data['summary_config']['delivery_schedules_fields']['delivery_schedules']['print']) is True else 'UnChecked')
			item['include_delivery_schedule_sno'] = ('Checked' if bool(data['summary_config']['delivery_schedules_fields']['sno']['print']) is True else 'UnChecked')
			item['include_page_no'] = ('Checked' if bool(data['misc_config']['print_pageno']) is True else 'UnChecked')
			item['include_summary'] = ('Checked' if bool(data['misc_config']['print_summary_first_page']) is True else 'UnChecked')
			response['checkbox_fields'].append(item)

			# item = dict()
			# item['sort_order'] = self.sort_order[data['items_config']['field_alignment']['item_sorter']]
			# response['item_sort_order'].append(item)

			if bool(data['items_config']['item_table']['tax']['print']) is True:
				item = dict()
				item['tax_type'] = self.tax_types[data['items_config']['item_table']['tax']['field_type']]
				response['tax'].append(item)
			# item['included_reg_items'] = data.invoice_template_headerconfig_form.cleaned_data['included_reg_items']
		except Exception as e:
			logger.exception(e)
		return response

	def queryInsert(self, user_id=None, enterprise_id=None, data=None):
		"""
		:param user_id:
		:param enterprise_id:
		:param data:
		:return:
		"""
		response = {}
		try:
			user = getUser(enterprise_id=enterprise_id, user_id=user_id)
			formatted_data = self.buildInsertDataStructure(user=user, data=data)
			response = self.insert(id=enterprise_id, enterprise_id=enterprise_id, data=formatted_data)

		except Exception as e:
			logger.exception(e)
		return response

	def fetchLogList(self, eid=None, enterprise_id=None, offset=None, limit=None):
		"""
		get listed voucher log data
		:param voucher_id:
		:param enterprise_id:
		:return:
		"""
		response = {}
		result = []
		try:
			query = self.fetchLog(id=int(eid), enterprise_id=int(enterprise_id), offset=offset, limit=limit)
			for voucher in query:
				voucher['created_at'] = str(voucher['created_at'])
				voucher['log']['modified_at'] = str(voucher['log']['modified_at'])
				voucher['log']['preference'] = 1 if 'preference' in voucher['log'] else 0
				result.append(voucher)
			response = response_code.success() if len(result) > 0 else response_code.failure()
			response['data'] = result
		except Exception as e:
			logger.exception(e)
		return response

	def fetchLogData(self, eid=None, enterprise_id=None, modified_at=None):
		"""
		get individual log data	:param eid:
		:param enterprise_id:
		:param modified_at:
		:return:
		"""
		response = response_code.failure()
		result = []
		try:
			query = self.fetchLogDetails(id=int(eid), enterprise_id=int(enterprise_id), identifier=modified_at)
			for template_config in query:
				del template_config['log']['modified_at']
				result.append(template_config)
			response = response_code.success() if len(result) > 0 else response
			if len(result) > 1:
				response['data'] = self.buildLogData(result[1]['log'], result[0]['log'])
			elif len(result) > 0:
				response['data'] = self.buildLogData(None, result[0]['log'])
			response['data'] = response['data'] if len(response['data']) > 0 else ['No change detected']
		except Exception as e:
			logger.exception(e)
			response = response_code.failure()
		return response

	def fieldLogData(self, key=None, title=None, old=None, new=None):
		"""

		:param key:
		:param title:
		:param old:
		:param new:
		:return:
		"""
		if old is not None and key in old:
			if type(new[key]) == list and type(old[key]) == list:
				diff = DeepDiff(old[key], new[key], ignore_order=True) if type(new[key]) == list and len(old[key]) != len(new[key]) else DeepDiff(old[key], new[key])
				if 'iterable_item_added' in diff:
					for item_added in diff['iterable_item_added'].items():
						if type(self.module_data_set[key]) is not str:
							log = '<b>%s</b> added with following values: <br />' % self.module_data_set[key]['title']
							for i, elem in self.module_data_set[key].items():
								log += ' <b>%s</b>: <i>%s</i>' % (elem, ', '.join(str(l_data) for l_data in item_added[1][i]) if type(item_added[1][i]) == list else str(item_added[1][i])) if i is not 'title' else ''
							self.response_log.append('%s' % log)
						else:
							self.response_log.append('%s named <b>%s</b> was added' % (self.module_data_set[key], item_added[1]))

				if 'iterable_item_removed' in diff:
					for item_removed in diff['iterable_item_removed'].items():
						if type(self.module_data_set[key]) is not str:
							log = '<b>%s</b> removed with following values: <br />' % self.module_data_set[key]['title']
							for i, elem in self.module_data_set[key].items():
								log += ' <b>%s</b>: <i>%s</i>' % (elem, ', '.join(
									str(l_data) for l_data in item_removed[1][i]) if type(
									item_removed[1][i]) == list else str(item_removed[1][i])) if i is not 'title' else ''
							self.response_log.append('%s' % log)
						else:
							self.response_log.append(
								'%s named <b>%s</b> was removed' % (self.module_data_set[key], item_removed[1]))
				if 'values_changed' in diff:
					for item in diff['values_changed'].keys():
						var_chg = re.findall("\[(.*?)\]", item.replace('\'', ''))
						self.response_log.append('<b>%s</b> has changed from <b>%s</b> to <b>%s</b>' % (self.module_data_set[key][var_chg[1]].capitalize(), old[key][int(var_chg[0])][var_chg[1]], new[key][int(var_chg[0])][var_chg[1]]))
			else:
				diff = DeepDiff(old[key], new[key])
				if 'values_changed' in diff:
					if str(diff['values_changed']['root']['old_value']) == '' and str(
							diff['values_changed']['root']['new_value']) != '':
						self.response_log.append(
							'%s <b>%s</b> added' % (title, diff['values_changed']['root']['new_value']))
					elif str(diff['values_changed']['root']['new_value']) == '' and str(
							diff['values_changed']['root']['old_value']) != '':
						self.response_log.append(
							'%s <b>%s</b> removed' % (title, diff['values_changed']['root']['new_value']))
					else:
						self.response_log.append('%s changed from <b>%s</b> to <b>%s</b>' % (
							title,
							diff['values_changed']['root']['old_value'],
							diff['values_changed']['root']['new_value']))
		elif key in new:
			if type(new[key]) == list:
				if type(title) == dict:
					for item in new[key]:
						log = '<b>%s</b> added with following values: <br />' % self.module_data_set[key]['title']
						for i, elem in self.module_data_set[key].items():
							log += ' <b>%s</b>: <i>%s</i>' % (elem, ', '.join(str(l_data) for l_data in item[i]) if type(item[i]) == list else str(item[i])) if i is not 'title' else ''
						self.response_log.append('%s' % log)
				else:
					self.response_log.append('%s <b>%s</b> added' % (title, ', '.join(str(l_data) for l_data in new[key])))
			elif new[key] != '':
				self.response_log.append('%s <b>%s</b> added' % (title, new[key]))
		logger.info('Template LOG DATA updated %s' % self.response_log)
		return False

	def buildLogData(self, old=None, new=None):
		"""

		:param old:
		:param new:
		:return:
		"""
		for key, title in self.module_data_set.items():
			self.fieldLogData(key=key, title=title, old=old, new=new)
		return self.response_log
