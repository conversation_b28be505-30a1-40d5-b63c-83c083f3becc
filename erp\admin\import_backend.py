from decimal import Decimal

from erp.admin import logger
from erp.dao import DataAccessObject, executeQuery
from erp.models import LedgerBillSettlement, ImportMessage, ImportDocuments
from settings import bucket
from util.ftp_helper import FTPUtil

__author__ = 'saravanan'


class TallyDAO(DataAccessObject):
	"""
	All Voucher related Database operations are defined here.
	"""

	def getMessages(self, enterprise_id=None, user_id=None):
		"""

		:param enterprise_id:
		:param user_id:
		:return:
		"""
		try:
			messages = self.db_session.query(ImportMessage).filter(
				ImportMessage.enterprise_id == enterprise_id, ImportMessage.created_by == user_id,
				ImportMessage.is_process == 0).order_by(ImportMessage.created_on.desc()).all()
			return messages
		except Exception as e:
			logger.exception("Could not fetch messages %s" % e.message)


class TallyService:
	"""
	Service class that handles the business logic of the User Profile management module
	"""

	def __init__(self):
		"""

		"""
		self.import_dao = TallyDAO()

	@staticmethod
	def constructLedgerBillSettlement(
			enterprise_id=None, ledger_bill=None, voucher_id=None, dr_value=None, cr_value=None, db_session=None):
		"""

		:param enterprise_id:
		:param ledger_bill:
		:param voucher_id:
		:param dr_value:
		:param cr_value:
		:param db_session:
		:return:
		"""
		try:
			net_debit = 0
			net_credit = 0
			for settlement in ledger_bill.settlements:
				net_debit += Decimal(settlement.dr_value).quantize(Decimal('1.00'))
				net_credit += Decimal(settlement.cr_value).quantize(Decimal('1.00'))
			logger.debug(
				"Settlement: Debit - %s | Credit - %s" % (round(Decimal(dr_value), 2), round(Decimal(cr_value), 2)))
			ledger_bill_settlement = None
			if ledger_bill.id and voucher_id:
				ledger_bill_settlement = db_session.query(LedgerBillSettlement).filter(
					LedgerBillSettlement.bill_id == ledger_bill.id,
					LedgerBillSettlement.voucher_id == voucher_id).first()
			if round(Decimal(dr_value), 2) > 0 or round(Decimal(cr_value), 2) > 0:
				if not ledger_bill_settlement:
					ledger_bill_settlement = LedgerBillSettlement()
					ledger_bill_settlement.voucher_id = voucher_id
					ledger_bill_settlement.bill = ledger_bill
				else:
					net_debit -= ledger_bill_settlement.dr_value
					net_credit -= ledger_bill_settlement.cr_value
				ledger_bill_settlement.dr_value = round(Decimal(dr_value), 2)
				ledger_bill_settlement.cr_value = round(Decimal(cr_value), 2)
				ledger_bill_settlement.enterprise_id = enterprise_id
				net_debit += Decimal(dr_value).quantize(Decimal('1.00'))
				net_credit += Decimal(cr_value).quantize(Decimal('1.00'))
			elif ledger_bill_settlement:
				net_debit -= Decimal(ledger_bill_settlement.dr_value)
				net_credit -= Decimal(ledger_bill_settlement.cr_value)
				db_session.delete(ledger_bill_settlement)
			ledger_bill.net_value = round(abs(net_debit - net_credit), 2)
			ledger_bill.is_debit = (net_debit >= net_credit)
			return ledger_bill_settlement
		except:
			raise

	def getTallyMessage(self, enterprise_id=None, user_id=None):
		"""

		:param enterprise_id:
		:param user_id:
		:return:
		"""
		message_list = []
		try:
			messages = self.import_dao.getMessages(enterprise_id=enterprise_id, user_id=user_id)
			for message in messages:
				date = ""
				if message.created_on:
					date = message.created_on.strftime('%Y-%m-%d %H:%M')
				if message.message:
					message_list.append({
						'id': message.id, 'message': message.message,
						'created_on': date})
		except Exception as e:
			logger.exception("Could not fetch notification %s" % e.message)
		return message_list

	def deleteTallyMessage(self, enterprise_id=None, user_id=None):
		"""
		:param enterprise_id:
		:param user_id:
		:return:
		"""
		self.import_dao.db_session.begin(subtransactions=True)
		try:
			sent_messages = self.import_dao.db_session.query(ImportMessage).filter(
				ImportMessage.enterprise_id == enterprise_id, ImportMessage.created_by == user_id).all()
			for msg in sent_messages:
				self.import_dao.db_session.delete(msg)
			logger.info("Deleted %s messages for user_id %s" % (len(sent_messages), user_id))
			self.import_dao.db_session.commit()
			return True
		except Exception as e:
			self.import_dao.db_session.rollback()
			logger.error("Could not fetch message %s " % e)
		return False

	def processTallyDocument(self, enterprise_id=None, user_id=None, process=None):
		"""
		:param enterprise_id:
		:param user_id:
		:return:
		"""
		self.import_dao.db_session.begin(subtransactions=True)
		try:
			self.import_dao.db_session.query(ImportDocuments).filter(
				ImportDocuments.enterprise_id == enterprise_id, ImportDocuments.uploaded_by == user_id).update(
				{ImportDocuments.process: process})
			logger.info("Processed documents for user_id %s" % user_id)
			logger.debug("Document Dirty: %s" % self.import_dao.db_session.dirty)
			self.import_dao.db_session.commit()
			return True
		except Exception as e:
			self.import_dao.db_session.rollback()
			logger.error("Could not fetch documents %s " % e)
		return False

	def unprocessedTallyDocument(self, enterprise_id=None, user_id=None, process=None):
		"""
		:param enterprise_id:
		:param user_id:
		:return:
		"""
		try:
			un_process_files = self.import_dao.db_session.query(
				ImportDocuments.file_name.label('file_name'), ImportDocuments.uploaded_on.label('uploaded_on'),
				ImportDocuments.id.label('id')).filter(
				ImportDocuments.enterprise_id == enterprise_id, ImportDocuments.uploaded_by == user_id,
				ImportDocuments.process == process).all()

		except Exception as e:
			logger.error("Could not fetch documents %s " % e)
			un_process_files = ""
		return un_process_files

	def updateLedgerBillNetValue(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		logger.info("Update Query:")
		try:
			query = """UPDATE ledger_bills lb
					JOIN
				(SELECT
					bill_id, SUM(cr_value) AS credit, SUM(dr_value) AS debit
				FROM
					ledger_bill_settlements
				GROUP BY bill_id) ubl ON ubl.bill_id = lb.id
					AND lb.net_value <> ABS(ubl.credit - ubl.debit)
				SET
					lb.net_value = ABS(credit - debit),
					lb.is_debit = debit > credit where lb.enterprise_id=%s""" % enterprise_id
			executeQuery(query)
			return True
		except Exception as e:
			logger.error("Could not fetch documents %s " % e)
		return False

	def updateLedgerBillIdsInReceipts(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		try:
			query = """UPDATE grn
				JOIN
				party_ledger_map AS p ON p.party_id = grn.party_id
					AND p.enterprise_id = grn.enterprise_id
					AND p.is_supplier = 1
				JOIN
					ledger_bills AS lb ON grn.invno = lb.bill_no
					AND DATE(grn.inv_date) = lb.bill_date
					AND grn.enterprise_id = lb.enterprise_id
					AND p.ledger_id = lb.ledger_id 
				SET 
					grn.ledger_bill_id = lb.id
				WHERE
					grn.enterprise_id = %s""" % enterprise_id
			executeQuery(query)
			return True
		except Exception as e:
			logger.error("Could not update data %s " % e)
		return False

	def updateLedgerBillIdsInInvoices(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		try:
			query = """UPDATE invoice AS i
				JOIN
					party_ledger_map AS p ON p.party_id = i.party_id
					AND p.enterprise_id = i.enterprise_id
					AND p.is_supplier = 0
				JOIN
					ledger_bills AS lb ON CONCAT(i.invoice_no, '') = lb.bill_no
					AND DATE(i.approved_on) = lb.bill_date
					AND i.`type` = 'GST'
					AND i.enterprise_id = lb.enterprise_id
					AND p.ledger_id = lb.ledger_id 
				SET 
					i.ledger_bill_id = lb.id
				WHERE
					i.enterprise_id = %s""" % enterprise_id
			executeQuery(query)
			return True
		except Exception as e:
			logger.error("Could not update data %s " % e)
		return False

	def deleteTallyDocument(self, enterprise_id=None, file_id=None):
		"""

		:param enterprise_id:
		:param file_id:
		:return:
		"""
		self.import_dao.db_session.begin(subtransactions=True)
		try:
			documents = self.import_dao.db_session.query(ImportDocuments).filter(
				ImportDocuments.enterprise_id == enterprise_id, ImportDocuments.id == file_id).all()
			for doc in documents:
				blob_name = "{enterprise_id}/{file}".format(enterprise_id=enterprise_id, file=doc.attachment.file)
				blob = bucket.blob(blob_name)
				blob.delete()
				self.import_dao.db_session.delete(doc)
			self.import_dao.db_session.commit()
			return True
		except Exception as e:
			self.import_dao.db_session.rollback()
			logger.error("Could not fetch documents %s " % e)
		return False
