"""
"""
import json
import os
from datetime import datetime

import simplejson
from django.http import HttpResponse
from django.utils.encoding import smart_str

from erp.auth import USER_IN_SESSION_KEY, SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.purchase import logger
from erp.purchase.service import PurchaseService
from util.api_util import response_code, JsonUtil
from util.helper import getFormattedDocPath
from erp.dao import executeQuery

__author__ = 'nandha'


def getDashboardData(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		user_id = rh.getPostData('user_id')

		logger.info('Fetching purchase dashboard entries...')
		response = response_code.success()
		from erp.admin.backend import UserService
		# Fetching Indent Status
		from util.helper import getFYDateRange
		enterprise = UserService().user_dao.getEnterprise(enterprise_id=enterprise_id)
		since, till = getFYDateRange(for_date=datetime.today(), fy_start_day=enterprise.fy_start_day)

		from erp.stores.backend import StoresService
		response.update(StoresService().getEnterpriseIndentStatus(enterprise_id=enterprise_id))

		purchase_service = PurchaseService()
		# Fetching Purchase Order status
		response.update(purchase_service.getPOStatus(enterprise_id=enterprise_id, since=since, till=till))
		# Fetching Purchase performance records
		response['performance'] = purchase_service.getPurchasePerformance(enterprise_id=enterprise_id)

	except Exception as e:
		logger.info(e)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def purchasePerformance(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')

		logger.info('Fetching purchase performance')
		response = response_code.success()
		response['po_monthly_data'] = PurchaseService().getPurchasePerformance(enterprise_id=enterprise_id)
	except Exception as e:
		logger.info(e)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def searchPO(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		po_no = rh.getPostData('po_no')
		supplierId = rh.getPostData('supplierId')
		project_code = rh.getPostData('project_code')
		item_id = rh.getPostData('item_id')
		status = rh.getPostData('status')
		since, till = JsonUtil.getDateRange(rh)
		enterprise_id = rh.getPostData('enterprise_id')
		finance_year = rh.getPostData('finance_year')
		response = response_code.success()
		response['po_list'] = PurchaseService().searchPO(
			enterprise_id=enterprise_id, since=since, till=till, po_no=po_no,
			supplier_id=supplierId, item_id=item_id, project_code=project_code, status=status, finance_year=finance_year)
		logger.info("Fetching %s purchase orders." % len(response['po_list']))
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def fetchDraftPO(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		response = response_code.success()
		response['po_list'] = PurchaseService().fetchDraftPO(enterprise_id=enterprise_id)
		logger.info("Fetching %s purchase orders." % len(response['po_list']))
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def poDraft(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		po_id = rh.getPostData('po_id')
		enterprise_id = rh.getPostData('enterprise_id')
		response = response_code.success()
		response.update(PurchaseService().getPoDraft(enterprise_id=enterprise_id, po_id=po_id))
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(simplejson.dumps(response), 'content-type=text/json')


def poMaterialDetails(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		item_id = rh.getPostData('item_id')
		enterprise_id = rh.getPostData('enterprise_id')
		party_id = rh.getPostData('party_id')
		po_date = rh.getPostData('po_date')
		po_type = rh.getPostData('po_type')
		is_job = po_type == "true"  # PO = 0/false, JO = 1/true
		logger.info("Loading Purchase Order material details for %s" % [enterprise_id, item_id, party_id, po_type])
		response = response_code.success()
		response.update(PurchaseService().getPoMaterialDetails(
			enterprise_id=enterprise_id, item_id=item_id, party_id=party_id, po_date=po_date, is_job=is_job))
		logger.info("Loaded Purchase Order material details")
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def poMaterialOverDue(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		item_id = rh.getPostData('item_id')
		po_party_id = rh.getPostData('po_party_id')
		po_date = rh.getPostData('po_date')
		po_type = rh.getPostData('po_type')
		is_job = po_type == 'true'
		response = response_code.success()
		logger.info(
			"Loading Purchase Order over due details for %s" % [enterprise_id, item_id, po_party_id, po_type])
		response["outstanding"] = PurchaseService().getMaterialOverDue(
			enterprise_id=enterprise_id, item_id=item_id, include_party=(po_party_id,),
			po_date=po_date, is_job=is_job)
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getPoFinanceYear(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		response = response_code.success()
		response["financial_years"] = PurchaseService().fetchPoYear(enterprise_id=enterprise_id)
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def downloadPODocument(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	po_id = rh.getPostData('po_id')
	try:
		if not po_id:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')
		document_type = rh.getPostData('response_data_type')  # data, file
		document_regenerate = rh.getPostData('document_regenerate')
		if not document_type:
			document_type = 'file'
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		logger.info("Downloading PO Document for {enterprise: %s, id: %s}" % (enterprise_id, po_id))
		purchase_service = PurchaseService()
		logger.info("Source:%s" % rh.getPostData('source'))
		po_document = purchase_service.generatePODocument(
			enterprise_id=enterprise_id, po_id=po_id, document_regenerate=document_regenerate)
		po_need_to_load = purchase_service.purchase_dao.getPurchaseOrder(enterprise_id=enterprise_id, po_id=po_id)
		po_code = po_need_to_load.getCode().replace("/", "_")
		filename = smart_str("%s(%s).pdf" % (po_code, po_id))
		if document_type == "file":
			# mimetype is replaced by content_type for django 1.7
			response = HttpResponse(po_document.document, mimetype='application/force-download')
			response['Content-Disposition'] = 'attachment; filename=%s' % filename
			return response
		if not po_document:
			response = response_code.failure()
			return HttpResponse(json.dumps(response), 'content-type=text/json')
		response = response_code.success()
		response['ext'] = "pdf"
		response['data'] = "data:application/pdf;base64,%s" % po_document.document.encode('base64')
		response['url'] = os.path.join(os.path.dirname(__file__), (
			getFormattedDocPath(code=po_need_to_load.getCode(), id=po_id)).replace("#", "%23"))
		response['filename'] = filename
		response['remarks'] = po_need_to_load.remarks if po_need_to_load.remarks else []
	except Exception as e:
		logger.exception("Download po document failed for id %s. %s" % (po_id, e.message))
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def superEditPOCode(request):
	"""
	Super user can edit indent code through this api
	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		po_id = request_handler.getPostData("po_id")

		new_financial_year = request_handler.getPostData("new_financial_year")
		new_po_type = request_handler.getPostData("new_po_type")
		new_po_no = int(request_handler.getPostData("new_po_no"))
		new_sub_number = request_handler.getPostData("new_sub_number")
		response = PurchaseService().superEditPOCode(
			enterprise_id=enterprise_id, user_id=user_id, po_id=po_id, new_financial_year=new_financial_year,
			new_po_type=new_po_type == "JO", new_po_no=new_po_no, new_sub_number=new_sub_number)
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getPOLinkedMessage(request):
	"""

	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		po_id = request_handler.getPostData("po_id")
		receipt_codes = PurchaseService().purchase_dao.getReceiptCodes(
			enterprise_id=enterprise_id, po_id=po_id)
		response = response_code.success()
		if receipt_codes:
			if len(receipt_codes) > 0:
				receipt_string = " GRN - <b><i>%s</i></b> are" % (", ".join(receipt_codes))
			else:
				receipt_string = " GRN - <b><i>%s</i></b> is" % (", ".join(receipt_codes))
			response['custom_message'] = """ %s linked with this PO. 
						Unfortunately system will not handle the perpetuation of these changes to the linked documents, 
						hence need to be addressed manually.<br/> Do you want to continue?""" % (receipt_string)

			logger.warn("Trying to edit expense %s that is linked to %s" % (po_id, receipt_codes))
		else:
			response['custom_message'] = ""
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def latestUsedSupplierDetailsPurchase(request):
	"""
	lastUsedSupplierDetailsInvoice
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getPostData('enterprise_id')
	if not enterprise_id:
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	party_details_dict = ""
	try:
		party_details_dict = party_delivery_details(enterprise_id)
		response = response_code.success()
		response['party_details'] = party_details_dict
	except Exception as e:
		response = response_code.internalError()
		response['error'] = '%s' % e.message
		logger.exception("Frequently dealt suppliers list cannot be fetched...\n%s" % e)
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def party_delivery_details(enterprise_id):
	party_details_dict = ""
	enterprise_address_query = """
	SELECT name, address1, address2, city, state, pin_code FROM enterprise WHERE id='%s'""" % enterprise_id
	enterprise_query = executeQuery(enterprise_address_query)
	if len(enterprise_query) > 0:
		enterprise_address = enterprise_query[0]
		address = ''
		address += " " + enterprise_address[1] if enterprise_address[1] else ' '
		address += " " + enterprise_address[2] if enterprise_address[2] else ' '
		address += " " + enterprise_address[3] if enterprise_address[3] else ' '
		address += " " + enterprise_address[4] if enterprise_address[4] else ' '
		address += " " + enterprise_address[5] if enterprise_address[5] else ' '
		party_details_dict = {
			'shipping_name': enterprise_address[0] if enterprise_address[0] else '',
			'shipping_address': address}
	return party_details_dict
