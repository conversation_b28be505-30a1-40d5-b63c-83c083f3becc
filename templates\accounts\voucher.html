{% extends "accounts/sidebar.html" %}
{% block voucher %}
<style>
	.manual-gly.glyphicon.glyphicon-calendar {
		top: -12px;
		left: 13px;
		float: left;
	}
	
	.single-datepicker,
	.single-datepicker-manual	{
		padding-left: 30px;
		margin-bottom: -11px;
		cursor: pointer;
	}

	.dataTables_wrapper {
		margin-top: -20px;
	}

	.daterangepicker.dropdown-menu {
		z-index: 10020;
	}

	.loading-main-container {
		display: block;
	}

	.bill-list {
	  	text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
    	width: 100px;
    	display: block;
	}

	#bill_list .single-datepicker-manual,
	#bill_list .single-datepicker {
		padding-left: 30px;
	}

	#bill_list .manual-gly.glyphicon.glyphicon-calendar {
		top:  -8px;
	}

	.text-right input {
		text-align: right;
	}

	#enable_edit_voucher {
		position: absolute;
	    right: 30px;
	    margin-top: 50px;
	    z-index: 100;
	}

	#id_voucher-narration {
		resize: vertical;
	}

	.create_voucher {
		margin-right: 8px; 
		background: #004195 !important;
		color: #fff !important; 
		width: 75px; 
		border-radius: 50px;
	}
</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/voucher.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/tags.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/Component/projects.js?v={{ current_version }}"></script>

	<div class="right-content-container">
		<div class="page-title-container">
			<span class="page-title">Voucher</span>
		</div>
		<div>
			<div class="col-sm-12">
				<div class="page-heading_new">
					<span class="page_header" style="margin-top: 2px;">
						<span class="header_current_page hide">New</span>
					</span>
					<input hidden="hidden" id="dr_cr_group_ids" value="{{ dr_cr_group_ids }}"/>
					<input type="hidden" value="{{selected_project_code}}" id="selected_project_code" hidden="hidden">
					{% if logged_in_user.is_super %}
						<a class="btn super_user_icon" onclick="EditVoucherNumber(true);">
							<i class="fa fa-pencil"></i>
						</a>
						<div class="xsid_number_edit hide">
							<form class="form-inline" style="display: inline-block;" action="">
							    <div class="form-group">
							      	<input type="text" class="form-control" id="voc_financial_year" name="voc_financial_year" maxlength="5">
							    </div>
							    <div class="form-group">
								    <select class="form-control" name="voc_type" id="voc_type">
									    {% for type in voucher_type %}
									        <option value="{{type.0}}" data-val="{{type.2}}">&nbsp;{{type.2}} &nbsp;&nbsp;&nbsp;({{type.1}})</option>
									    {% endfor %}
								    </select>
							    </div>
							    <div class="form-group">
							      <input type="text" id="voc_number" name="voc_number" class="form-control" maxlength="6" onfocus="setNumberRangeOnFocus(this,6,0,true)" />
							    </div>
							    <div class="form-group">
							      	<input type="text" class="form-control text-lowercase" onkeypress="validateStringOnKeyPress(this,event,'character');" onblur="validateStringOnBlur(this,event,'character');" id="voc_number_division" name="voc_number_division" maxlength="1" >
							    </div>
							    <div class="form-group super_edit_submit_icon">
							    	<span role='button' data-tooltip="tooltip" title="Save" class="super_edit_save" onclick="SaveVoucherNumber();"><i class="fa fa-check"  aria-hidden="true" style="color: green;"></i></span>
							    	<span role='button' data-tooltip="tooltip" title="Cancel" class="super_edit_cancel" onclick="DiscardEditVoucherNumber();"><i class="fa fa-times" aria-hidden="true" style="color: red;"></i></span>
							    </div>
					  		</form>
					  		<div class="save_xsid_error_format hide">Required. Please check help tool for valid format.</div>
					  	</div>
					  	<div class="super_user_tool_tip hide"><span class="question-mark"></span></div>
						<!--<span class="super_user_tool_tip hide"><img src="/site_media/images/tooltip.png" style="vertical-align: top;" /></span>-->
					{%else%}
						<a class="btn super_user_icon" style="color:#777;" onclick="" data-tooltip="tooltip" data-placement="right" data-title="Only Super User can edit this field">
							<i class="fa fa-pencil"></i>
						</a>
					{% endif %}
					<span class="right-side-icon">
						<a href="/erp/accounts/voucher/list/" class="btn btn-add-new pull-right view_voucher" data-tooltip="tooltip" title="Back to Voucher&nbsp;List"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
						{% if access_level.edit %}
							<a class="btn btn-add-new pull-right hide" id="enable_edit_voucher" data-tooltip="tooltip" title="Edit"><i class="fa fa-pencil" aria-hidden="true"></i></a>
						{% endif %}
						<span class="prev_next_container" style=""></span>
						{% if access_level.edit %}
							<a href="/erp/accounts/voucher/" class="btn btn-add-new pull-right create_voucher" data-tooltip="tooltip" title="Create New Voucher"><i class="fa fa-plus" aria-hidden="true"></i></a>
							<a class="btn btn-add-new pull-right create_voucher"  data-tooltip="tooltip" title="Clone Voucher" onclick='cloneVoucher()'><i class="fa fa-clone" aria-hidden="true"></i></a>
						{% else %}
							<a role="button" class="btn btn-add-new pull-right create_voucher" style="opacity: 0.7; cursor: not-allowed;" data-tooltip="tooltip" title="You do not have adequate permission to create/ upload in Accounts Module. Please contact the administrator."><i class="fa fa-plus" aria-hidden="true"></i></a>
						{% endif %}	

					</span>
				</div>
			</div>
			<div class="col-lg-12">
				<div class="content_bg">
					<div>
						<div id="tab2">
							<input type="hidden" id="last_created_voucher_code" value="{{last_created_voucher}}"/>
							<input type="hidden" id="last_saved_voucher_error" value="{{last_saved_voucher_error}}"/>
							<input type="hidden" value="{{ clone_view }}" id="hidden_clone_status" name="clone_status"/>
							<div class=" view_list_table ">
								<div class="">
									<form action="/erp/accounts/voucher/save/"  method="POST" id="addVoucher">{% csrf_token %}
										<div class="row">
											<div class="col-sm-3">
												{% if voucherForm.id.value != '' and voucherForm.id.value != None and clone_view != "enable" %}
													<script type="text/javascript">$(".page_header").html('<span class="header_current_page"> {{ voucherForm.code.value }}</span>'); $(".page-title").text("Voucher");</script>
												{% else %}
													<script type="text/javascript">$(".page-title").text("New Voucher");</script>
												{% endif %}
												{{ voucherForm.id }}
												<input type="text" value="{{ voucherForm.id.value }}"
													   id="id_hidden_voucher_id" name="voucher-voucher_id" hidden="hidden"/>
											</div>
										</div>
										<div class="row remove-margin">
											<div class="col-sm-6 hide"><!-- Tags are hidden on the 2.16.3 release -->
												<div class="col-sm-12 form-group" style="padding: 0;" id="tags">
													<label>Tags</label>
													<ul id="ul-tagit-display" class="tagit-display form-control">
														{% for tag_form in tags_formset.initial_forms %}
															<li class="li-tagit-display" id="{{ tag_form.prefix }}">
																<div hidden="hidden">{{ tag_form.tag }}
																	{{ tag_form.DELETE }}{{ tag_form.ORDER }}</div>
																<label class="label-tagit-display"  id="id_{{tag_form.prefix}}-tag_label">{{ tag_form.tag.value }}</label>&nbsp;
																<a class="delete_tag" id="{{tag_form.prefix}}-del_button"></a>
																&nbsp;
															</li>
														{% endfor %}
														<li id="tag-__dummy__" hidden="hidden">
															<div hidden="hidden">{{ tags_formset.empty_form.tag }}
															{{ tags_formset.empty_form.ORDER }}
															{{ tags_formset.empty_form.DELETE }}</div>
															<label class="label-tagit-display"  id="id_{{tags_formset.empty_form.prefix}}-tag_label"></label>&nbsp;
															<a class="delete_tag" id="{{tags_formset.empty_form.prefix}}-del_button"></a>
															&nbsp;
														</li>

														<span>
															{{ tags_formset.management_form }}
															<span id="{{ tags_formset.empty_form.prefix }}" class="text_for_tag" >
																{{ tags_formset.empty_form.tag }}
																{{ tags_formset.empty_form.ORDER }}
																<span hidden="hidden">{{ tags_formset.empty_form.DELETE }}</span>
															</span>
														</span>
													</ul>
													<span style="position: absolute; margin-top: -8px; color: #aaa; font-size: 11px;">Type and press enter to add new tag.</span>
												</div>
												<div class="col-sm-2 hide" style="margin-top: 21px;">
													<input id="add_tag" class="btn btn-save btn-margin-1" value="+" type="button">
												</div>
											</div>
										</div>
										<div class="col-sm-3 form-group">
											<label>
												{% if logged_in_user.is_super %}
												<a class="super_edit_field super_edit_for_draft" data-tooltip="tooltip" data-placement="left" title="Super Edit" onclick="SuperEditVoucherDetails(this);">
													<i class="fa fa-pencil super_edit_in_field"></i>
												</a>
												{%else%}
												<a class="super_edit_field super_edit_for_draft" style="color:#777;" data-tooltip="tooltip" data-placement="left" title="Only Super User can edit this field" onclick="">
													<i class="fa fa-pencil super_edit_in_field"></i>
												</a>
												{% endif %}
												Voucher Type<span class="mandatory_mark"> *</span></label>
											<div class="{% if voucherForm.id.value != '' and voucherForm.id.value != None and clone_view != 'enable' %}div-disabled{% endif %}">
											{{ voucherForm.type_id }}
											<input type="hidden" id="current_type_id" value="{{ voucherForm.type_id.value }}"/>
											</div>
										</div>
										<div hidden="hidden" class="col-sm-3">
											<label>Voucher No</label>
											<!-- Render the field in non-editable for Edit and editable for Add -->
											{% if voucherForm.voucher_no.value != None %}
											<input hidden="hidden" type="text" class="purpose_txt_box" disabled
											       value="{{ voucherForm.voucher_no.value }}" id="id_voucher-voucher_no"
											       voucher_no="voucher-voucher_no_display"/>
											<!-- Disabled fields are not read in POST, hence the hidden field -->
											<input type="text" value="{{ voucherForm.voucher_no.value }} "
											       id="id_hidden_voucher_no" name="voucher-voucher_no" hidden="hidden"/>
											{% else %}
											{{ voucherForm.voucher_no }}
											{% endif %}
											{{ voucherForm.enterprise_id }}
											{{ voucherForm.status }}
											<input type="hidden" id="id_voucher-original_status"
											       value="{{voucherForm.status.value}}"/>
										</div>
										<div class="col-sm-3 form-group">
											<label>Date<span class="mandatory_mark"> *</span></label>
												{{ voucherForm.voucher_date }}
												<input type="text" class="form-control custom_datepicker editable-date till-today" placeholder="mm/dd/yyyy" id="voucher_date" readonly="readonly" autocomplete="off">
												<i class="glyphicon glyphicon-calendar custom-calender-icon"></i>
												<span class="date-format-error" style="display: none;">Invalid Date format: mm/dd/yyyy</span>
										</div>
										<div id ="ledger_bank_div" hidden="hidden" class="col-sm-3 form-group">
											<label>Bank Ledger<span class="mandatory_mark"> *</span></label>
											<select class="form-control chosen-select" name="bank_ledger" id="id_bank_ledger">

											</select>
										</div>
										<div id="ledger_cash_div" hidden="hidden" class="col-sm-3 form-group">
											<label>Cash Ledger<span class="mandatory_mark"> *</span></label>
											<select class="form-control chosen-select" name="cash_ledger" id="id_cash_ledger">

											</select>
										</div>

										<div class="clearfix"></div>
										<div class="col-sm-3 form-group">
											<div class="tour_project_tag">
												<div class="component_project" data-id="id_voucher-project_code" data-value="{% if selected_project_code %}{{ selected_project_code }}{% else %}none{% endif %}" data-name="voucher-project_code"></div>
											</div>
											<label id="expRev" style="display: block;margin-top: 20px;">Revenue: <span id="revenue" style="margin-right: 10px;">0</span>Expense : <span id="expense" style="margin-right: 10px;">0</span>Budget : <span id="budgetAmount" style="margin-right: 10px;">0</span> </label>
											<span hidden="hidden" id="cashflow_amount" style="margin-right: 10px;">0</span><span hidden="hidden" id="cash_allocated" style="margin-right: 10px;">0</span>
<!--											<label>Project/Tag<span class="mandatory_mark"> *</span></label>-->
<!--											{{ voucherForm.project_code }}-->
										</div>

										<div class="col-sm-3 form-group">
											<label>Transaction InstrumentNo. </label>
											{{ voucherForm.transaction_instrument_no }}
										</div>
										<div class="col-sm-3 form-group">
											<label class="po_title_txt">Transaction Description</label>
											{{ voucherForm.transaction_description }}
										</div>
										<div class="clearfix"></div>
										<div class="col-sm-1 form-group">
											<label>Dr/ Cr</label>
											<select class="form-control" name="select" id="is_debit">
												<option value=0>Cr</option>
												<option value=1>Dr</option>
											</select>
										</div>
										<div class="col-sm-5 form-group">
											<label>Ledger<span class="mandatory_mark"> *</span></label>
											<div class="material_txt">
											<input type="text" value="" class="form-control" id="ledgername" placeholder ="Select Ledger" data-enterkey-submit="add_new_ledger" maxlength="100">
											<input type="text" value="" class="textbox2" id="ledgercode" maxlength="10" hidden="hiddden">
											<input type="text" value="" class="textbox2" id="group" maxlength="10" hidden="hiddden">
											<input type="text" value="" class="textbox2" id="billable" hidden="hiddden">
											</div>
										</div>
										<div class="col-sm-3 form-group">
											<label>Amount<span class="mandatory_mark"> *</span></label>
											<input type="text" id="amount" class="form-control" placeholder="Enter Amount" data-enterkey-submit="add_new_ledger" maxlength="16" onfocus="setNumberRangeOnFocus(this,13,2)" />
										</div>
										<div class="col-sm-3"><span class="po_title_txt">&nbsp;</span>
											<input type="button" id="add_new_ledger"  class="btn btn-save btn-margin-1"  value="+"/>
										</div>
										<div class="col-lg-12 view_table_content">
											<div class="table-responsive">
												{{ voucher_particular_formset.management_form }}
												<table class="table table-striped custom-table-bordered custom-table text_box_in_table" id="materialtable" style="overflow: hidden; margin-bottom: 0">
													<thead>
														<tr>
															<th width="5%"> S.No</th>
															<th> Ledger</th>
															<th width="15%"> Opening as on
																<span id="id_opening_date_label" style="display: block;">{{voucherForm.voucher_date.value|date:'M d, Y'}}</span></th>
															<th width="18%" style="width: 150px;"> Debit</th>
															<th width="18%" style="width: 150px;"> Credit</th>
															<th width="5%"> Bill</th>
															<th width="5%"> Delete</th>
														</tr>
													</thead>
													<tbody>
													<div id="form_set">
														{% for voucherparticularForm in voucher_particular_formset.forms %}
														<tr bgcolor="#ECECEC" id="{{ voucherparticularForm.prefix }}" align="center">
															<td hidden="hidden">
																{{ voucherparticularForm.DELETE }}
																{{ voucherparticularForm.voucher_id }}
																{{ voucherparticularForm.item_no }}
																{{ voucherparticularForm.amount }}
																{{ voucherparticularForm.enterprise_id }}
																{{ voucherparticularForm.is_debit }}
																{{ voucherparticularForm.ledger_id}}
																{{ voucherparticularForm.ledger_group}}
																{{ voucherparticularForm.billable}}
															</td>
															<td>{{ forloop.counter }}. </td>
															<td class="text-left">{{ voucherparticularForm.ledger_label }}</td>
															<td class="text-right">
																<div id="id_{{ voucherparticularForm.prefix }}-opening">
																	<div id="id_opening-{{ voucherparticularForm.ledger_id.value }}">
																		{{ voucherparticularForm.ledger_opening.value }}</div>
																</div>
															</td>
															<td>
																<input type="text" 
																id="id_{{ voucherparticularForm.prefix }}-debit_amount" 
																name="{{ voucherparticularForm.prefix }}-debit_amount" 
																class='form-control text-right' 
																maxlength="18" onfocus="setNumberRangeOnFocus(this,15,2)" 
															    {% if voucherparticularForm.is_debit.value != 1 %}
																   value="0.00" class='form-control text-right' disabled="disabled"
															    {% else %}
																   value="{{ voucherparticularForm.amount.value }}"
															    {% endif %} />
															</td>

															<td>
																<input type="text" 
																id="id_{{ voucherparticularForm.prefix }}-credit_amount" 
																name="{{ voucherparticularForm.prefix }}-credit_amount" 
																class='form-control text-right' 
																maxlength="18" onfocus="setNumberRangeOnFocus(this,15,2)" 
															   {% if voucherparticularForm.is_debit.value != 0 %}
																   value="0.00" class='form-control text-right' disabled="disabled"
															   {% else %}
																   value="{{ voucherparticularForm.amount.value }}"
															   {% endif %}/>
															</td>
															<td class="remove-padding td_bill_list" style="overflow: hidden;"><a role="button" id="id_{{ voucherparticularForm.prefix }}-bill_details"
																   onclick="javascript:showBill('{{ voucherparticularForm.prefix }}');">
																	{% if voucherparticularForm.billable.value == True %}
																		{% if voucherparticularForm.bills.value|length == 0 %}
																			<input type="button" class="btn btn-save" value="+" />
																		{% else %}
																			<span class="bill-list" data-tooltip="tooltip" title="{{ voucherparticularForm.bills.value }}">
																				{{ voucherparticularForm.bills.value }}
																			</span>
																		</a>
																		{% endif %}
																	{% endif %}
															</td>
															<td><a href="#" id="id_{{ voucherparticularForm.prefix }}-delete_v_particular"
																   onclick="javascript:deleteVoucherForm('{{ voucherparticularForm.prefix }}');">
																 <i class="fa fa-trash-o" title="Delete" alt="Delete"></i></a>
															</td>
															<script language='javascript'>
																$('#id_{{ voucherparticularForm.prefix }}-is_debit').change(function(){
																	loadAptAmount('{{ voucherparticularForm.prefix }}');
																});
																$('#id_{{ voucherparticularForm.prefix }}-credit_amount').blur(function(){
																	$('#id_{{ voucherparticularForm.prefix }}-amount').val($('#id_{{ voucherparticularForm.prefix }}-credit_amount').val());
																	calcTotal();
																});
																$('#id_{{ voucherparticularForm.prefix }}-debit_amount').blur(function(){
																	$('#id_{{ voucherparticularForm.prefix }}-amount').val($('#id_{{ voucherparticularForm.prefix }}-debit_amount').val());
																	calcTotal();
																});

																$('#id_{{ voucherparticularForm.prefix }}-credit_amount, #id_{{ voucherparticularForm.prefix }}-debit_amount').change(function(){
																		VoucherAndBillAmountCrossVerify(this);
																});
															</script>
														</tr>
														{% endfor %}
													</div>
													<div id="new_forms">
													</div>
													<div id="empty_form">
														<tr bgcolor="#ECECEC" id="v_particular-__prefix__" align="center" hidden="hidden">
															<td hidden="hidden">
																{{ voucher_particular_formset.empty_form.DELETE }}
																{{ voucher_particular_formset.empty_form.voucher_id }}
																{{ voucher_particular_formset.empty_form.item_no }}
																{{ voucher_particular_formset.empty_form.amount }}
																{{ voucher_particular_formset.empty_form.enterprise_id }}
																{{ voucher_particular_formset.empty_form.is_debit }}
																{{ voucher_particular_formset.empty_form.ledger_id }}
																{{ voucher_particular_formset.empty_form.ledger_group }}
																{{ voucher_particular_formset.empty_form.billable }}

															</td>
															<td><div id="id_v_particular-__prefix__-item_no_label"></div></td>
															<td><div hidden="hidden" id="id_v_particular-__prefix__-label"></div>{{ voucher_particular_formset.empty_form.ledger_label }}</td>
															<td class="text-right"><div id="id_{{ voucher_particular_formset.empty_form.prefix }}-opening"></div></td>
															<td>
																<input type="text" 
																id="id_{{ voucher_particular_formset.empty_form.prefix }}-debit_amount" 
																name="{{ voucher_particular_formset.empty_form.prefix }}-debit_amount" 
																class='form-control text-right' 
																maxlength="18" onfocus="setNumberRangeOnFocus(this,15,2)" 
															   {% if voucher_particular_formset.empty_form.is_debit.value != 0 %}
																   value="" disabled="disabled"
															   {% else %}
																   value="{{ voucher_particular_formset.empty_form.amount.value }}"
															   {% endif %} />
															</td>

															<td>
																<input type="text" 
																id="id_{{ voucher_particular_formset.empty_form.prefix }}-credit_amount" 
																name="{{ voucher_particular_formset.empty_form.prefix }}-credit_amount" 
																class='form-control text-right' 
																maxlength="18" onfocus="setNumberRangeOnFocus(this,15,2)" 
															   {% if voucherparticularForm.is_debit.value != 0 %}
																   value="" disabled="disabled"
															   {% else %}
																   value="{{ voucher_particular_formset.empty_form.amount.value }}"
															   {% endif %}/>
															</td>
															<td class="td_bill_list"><a role="button" id="id_{{ voucher_particular_formset.empty_form.prefix }}-bill_details"
																   onclick="javascript:showBill('{{ voucher_particular_formset.empty_form.prefix }}');">
																 <input type="button"  class="btn btn-save"  value="+"/></a>
															</td>
															<td><a href="#" id="id_{{ voucher_particular_formset.empty_form.prefix }}-delete_v_particular"
																   onclick="javascript:deleteVoucherForm('{{ voucher_particular_formset.empty_form.prefix }}');">
																	 <i class="fa fa-trash-o" title="Delete" alt="Delete"></i></a>
															</td>

															<script language='javascript'>
																$('#id_{{voucher_particular_formset.empty_form.prefix}}-is_debit').change(function(){
																	loadAptAmount('{{voucher_particular_formset.empty_form.prefix}}');
																});
																$('#id_{{ voucher_particular_formset.empty_form.prefix }}-credit_amount').blur(function(){
																	$('#id_{{ voucher_particular_formset.empty_form.prefix }}-amount').val($('#id_{{ voucher_particular_formset.empty_form.prefix }}-credit_amount').val());
																	calcTotal();
																});

																$('#id_{{ voucher_particular_formset.empty_form.prefix }}-credit_amount, #id_{{ voucher_particular_formset.empty_form.prefix }}-debit_amount').change(function(){
																		VoucherAndBillAmountCrossVerify(this);
																});
																$('#id_{{ voucher_particular_formset.empty_form.prefix }}-debit_amount').blur(function(){
																	$('#id_{{ voucher_particular_formset.empty_form.prefix }}-amount').val($('#id_{{ voucher_particular_formset.empty_form.prefix }}-debit_amount').val());
																	calcTotal();
																});
															</script>
														</tr>
													</div>
													<tr>
														<td colspan="2" style="border-right: none;" class="remove-left-padding">
														</td>
														<td style="vertical-align: top !important; text-align: center; border-left: none;">
															<span class="text-center" style="padding-top: 10px;">
																<label style="font-size: 16px;">Total (in {{home_currency}})</label>
															</span>
														</td>
														<td style="vertical-align:top !important;">
															<input type="text" class="form-control text-right text_box_label" disabled
															value="{{ voucherForm.dr_amount.value }}" id="id_dr_total_amount" style="font-size: 16px;" />
														</td>
														<td style="vertical-align:top !important;">
															<input type="text" class="form-control text-right text_box_label" disabled
															value="{{ voucherForm.cr_amount.value }}" id="id_cr_total_amount" style="font-size: 16px;"/>
														</td>
														<td colspan="2"></td>
													</tr>
													</tbody>
												</table>
											</div>
										</div>
										<div style="padding: 7px;">
											<table class="table">
												<tr>
													<td class="col-sm-8" style="border: none;">
														<label class="po_title_txt">Narration</label>
														{{ voucherForm.narration }}
													</td>
													<td class="col-sm-4" style="border: none; vertical-align: bottom; text-align: right;">
														<div class="material_txt" id="voucher_save_container">
														{% if access_level.edit %}
														<a href="#" id="save_voucher_button" class="btn btn-save" >Save</a>
														<input type="submit" hidden="hidden" id="saveVoucher" value="Save">
														{% if access_level.approve and clone_view != "enable"%}
														<a href="#" id="approve_voucher_button" class="btn btn-save" >Approve</a>
														{% endif %}
														{% endif %}
													</div>
													</td>
												</tr>
											</table>
										</div>
										<div id="ledger_bill_div" class="modal fade" data-backdrop="static" role="dialog" data-keyboard="false">
											<div class="modal-dialog modal-lg">
												<div class="modal-content" >
													<div class="modal-header">
														<button type="button" id="close_button" class="close">&times;</button>
														<h4 class="modal-title">Bill</h4>
													</div>
													<div class="modal-body" style="display: inline-block;">
														<div id="new_ledger_bill_div">
															<div class="col-sm-3 form-group">
																<label>Bill No<span class="mandatory_mark"> *</span></label>
																<input type="text" value="" class="form-control" id="bill_no" maxlength="30" placeholder="Bill No." data-enterkey-submit='add_new_ledger_bill' onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" >
																<input type="text" value="" class="purpose_txt_box" id="bill_ledger_id" maxlength="30"  hidden="hidden">
															</div>
															<div class="col-sm-3 form-group">
																<!--<input type="text" id="todate" name="todate" class="po_txt_box required_date" placeholder="Select Date" />	-->
																<label>Date<span class="mandatory_mark"> *</span></label>
																<input type="text" class="form-control daterange-single hide" placeholder="Select Date" id="bill_date" name="bill_date" >
																<div id="voucher_bill_date" class="report-date form-control">
																	<i class="glyphicon glyphicon-calendar"></i>&nbsp;
																	<span></span>
																</div>
																<!--<div class="input-group-addon">
																	<span class="glyphicon glyphicon-calendar"></span>
																</div>-->
															</div>
															<div class="col-sm-3 form-group">
																<label>Amount<span class="mandatory_mark"> *</span></label>
																<input type="text" id="ledger_bill_amount" class="form-control" placeholder="Enter Amount" maxlength="18" onfocus="setNumberRangeOnFocus(this,15,2)" data-enterkey-submit='add_new_ledger_bill' />
															</div>
															<div hidden="hidden">
																<input type="text" id="ledger_is_debit"/>
															</div>

															<div class="col-sm-1 form-group">
																<input type="button" id="add_new_ledger_bill"  class="btn btn-save btn-margin-1"  value="+"/>
															</div>
															<div class="clearfix"></div>
															<div class="col-sm-12">
																<div class="sub-heading-content col-sm-5">
																	<span class="mycolor-grey-6">Voucher Amount:</span><span class="mycolor-blue-2 font-bold myfont-number">
																		<input id="ledger_amount" disabled="disabled"  style="border: none; background: none;" />
																	</span>
																</div>
																<div class="sub-heading-content col-sm-5">
																	<span class="mycolor-grey-6">Balance:</span><span class="mycolor-blue-2 font-bold myfont-number">
																		<input id="balance_amount" disabled="disabled" value="0.00"  style="border: none; background: none;" />
																	</span>
																</div>
																<div class="col-sm-2 text-right remove-padding">
																	<input type="button" class="btn btn-save" style="margin-top: 8px; padding: 4px 12px; font-size: 12px;" id="buttonautofill" value="Auto Fill" />
																	<a class="btn btn-add-new pull-right export_csv"
																			style="position: absolute; right: 90px; top: 8px; height: 27px; font-size: 12px; cursor: pointer;"
																			onclick="GeneralExportTableToCSV.apply(this, [$('#bill_list'), '{{ voucherForm.code.value }}.csv']);">
																		<i class="fa fa-download" aria-hidden="true"></i>
																	</a>
																</div>
															</div>


															<div class="col-lg-12 view_table_content" style="height: 350px; overflow-y: auto;">
																<div class="table-responsive">
																	{{ ledger_bill_formset.management_form }}
																	<table class="table table-striped table-bordered custom-table without-table-fixed text_box_in_table" id ="bill_list" >
																		<thead>
																			<tr><th colspan="5"><span> Voucher No : {{ voucherForm.code.value }} </span><th colspan="4"> Voucher Amount : <span id="ledger_amounts" disabled="disabled"  style="border: none; background: none;"></span></th></tr>
																			<tr id="bill_header">
																				<th> Bill No </th>
																				<th> Bill Date </th>
																				<th> Project </th>
																				<th> Debit</th>
																				<th> Credit</th>
																				<th> Value </th>
																				<th class="exclude_export"> Debit</th>
																				<th class="exclude_export"> Credit</th>
																				<th class="exclude_export"> Delete</th>
																			</tr>
																		</thead>
																		<tbody>
																			<div id="ledger_bill_form_set">
																				{% for ledgerbillForm in ledger_bill_formset.forms %}
																				<tr bgcolor="#ECECEC" id="{{ ledgerbillForm.prefix }}" align="center">
																					<td hidden="hidden">
																						{{ ledgerbillForm.DELETE }}
																						{{ ledgerbillForm.bill_date }}
																						{{ ledgerbillForm.bill_no }}
																						{{ ledgerbillForm.bill_id }}
																						{{ ledgerbillForm.net_value }}
																						{{ ledgerbillForm.enterprise_id }}
																						{{ ledgerbillForm.is_debit }}
																						{{ ledgerbillForm.ledger_id}}
																					</td>
																					<td>{{ ledgerbillForm.bill_no }} </td>
																					<td>{{ ledgerbillForm.bill_date }}</td>
																					<td></td>
																					<td>{{ ledgerbillForm.debit_settlement }}</td>
																					<td>{{ ledgerbillForm.credit_settlement }}</td>
																					<td>
																						<input type="text" 
																						id="id_{{ ledgerbillForm.prefix }}-amount" 
																					    name="{{ ledgerbillForm.prefix }}-amount" 
																					    class='form-control' 
																					    maxlength="18" 
																					    onfocus="setNumberRangeOnFocus(this,15,2)" 
																					    value="{{ ledgerbillForm.net_value.value }}" />
																					</td>
																					<td> {{ ledgerbillForm.dr_cr_label.value }}</td>
																					<td><a href="#" class="delete_ledger_bill" id="id_{{ ledgerbillForm.prefix }}-delete_ledger_bill">
																						<i class="fa fa-trash-o" title="Delete" alt="Delete"></i>
																						 </a>
																					</td>
																				</tr>
																				{% endfor %}
																			</div>
																			<div id="ledger_bill_new_forms">
																			</div>
																			<div id="ledger_bill_empty_form">
																				<tr bgcolor="#ECECEC" id="ledger_bill-__prefix__" name="ledger_bill_prefix" align="center" hidden="hidden">
																					<td hidden="hidden">
																						{{ ledger_bill_formset.empty_form.DELETE }}
																						{{ ledger_bill_formset.empty_form.enterprise_id }}
																						{{ ledger_bill_formset.empty_form.is_debit }}
																						{{ ledger_bill_formset.empty_form.ledger_id }}
																						{{ ledger_bill_formset.empty_form.bill_id }}
																					</td>
																					<td><div id="id-__prefix__-bill_no">{{ ledger_bill_formset.empty_form.bill_no }}</div></td>
																					<td><div id="id-__prefix__-bill_date">
																						{{ ledger_bill_formset.empty_form.bill_date }}
																						<input type='text' class='form-control single-datepicker-manual' />
																						<i class='manual-gly glyphicon glyphicon-calendar'></i>
																						</div></td>
																					<td></td>
																					<td>-</td>
																					<td>-</td>
																					<td>
																						<input type="text" hidden="hidden" 
																						id="id_{{ ledger_bill_formset.empty_form.prefix }}-net_value"
																						name="{{ ledger_bill_formset.empty_form.prefix }}-net_value"
																						class='form-control text-right' 
																						maxlength="18" onfocus="setNumberRangeOnFocus(this,15,2)" data-enterkey-submit='save_button' value="{{ledger_bill_formset.empty_form.amount.value}}" />
																						<div hidden="hidden" id="id_ledger_bill-__prefix__-dr_cr_label"></div>
																					</td>
																					<td class="exclude_export">{{ ledger_bill_formset.empty_form.debit_settlement }}</td>
																					<td class="exclude_export">{{ ledger_bill_formset.empty_form.credit_settlement }}</td>
																					<td class="exclude_export"><a href="#" class="delete_ledger_bill" id="id_{{ ledger_bill_formset.empty_form.prefix }}-delete_ledger_bill">

																							 <i class="fa fa-trash-o" title="Delete" alt="Delete"></i>
																						 </a>
																					</td>
																				</tr>
																			</div>
																		</tbody>
																		<tr class="table-total-col">
																			<td colspan="5"></td>
																			<td>
																				<input type="text" id="total_debit_amount" disabled="disabled" class="form-control" value="0.00" style="text-align:right;" />
																				</td>
																			<td>
																				<input type="text" id="total_credit_amount" disabled="disabled" class="form-control" value="0.00" style="text-align:right;" />
																			</td>
																			<td></td>
																		</tr>
																	</table>
																</div>
																<div class="material_txt">
																</div>
															</div>
														</div>
													</div>
													<div class="modal-footer">
														<div class="col-lg-12 text-right">
															<input type="button" id="save_button"  class="btn btn-save"  value="Save "/>
															<input type="button" id="cancel_button"  class="btn btn-cancel"  value="Reset"/>
														</div>
													</div>
												</div>
											</div>
										</div>
									</form>
								</div>
							</div>
							{% include "masters/add_project_modal.html" %}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	{% include "activity_log.html" %}
<!-- /#wrapper -->
<br clear="all"/>
<div id="error_messages" width="50%" style="display:none" class="error_panel">
	<table class="error_display" style="margin:100px 0px 0px 470px;color:#000000; background:#FFFFFF;" cellpadding="1"
	       cellspacing="1" border="0">
		<tr>
			<td style="align:center">
				<div>
					{% if voucherForm.errors %}
					<h4>Errors in Voucher Creation:</h4>
					{% endif %}
					{% for field in voucherForm.visible_fields %}
					{% for error in field.errors %}
					<li style="list-style-type: none;">{{field.label_tag}} :{{error}}</li>
					{% endfor %}
					{% endfor %}
				</div>
				<div>
					{% if voucher_particular_formset.errors %}
					<h4>Errors in Voucher Particulars:</h4>
					{% endif %}
					{% for v_particular_form in voucher_particular_formset.forms %}
					{% if v_particular_form.errors %}
					<b>{{ v_particular_form.item_no.value }}</b>:
					{% endif %}
					{% for field in v_particular_form.visible_fields %}
					{% if field.errors%}
					<br><i>{{field.label_tag}}</i>:
					{% endif %}
					<ul>
						{% for error in field.errors %}
						<li style="list-style-type: none;">{{error}}</li>
						{% endfor %}
					</ul>
					{% endfor %}
					{% endfor %}
				</div>
			</td>
		</tr>
		<tr>
			<td>
				<input type="text" value='{{ voucherForm.errors }}' id="form_errors" hidden="hidden"/>
				<input type="text" value='{{ voucher_particular_formset.errors }}' id="formset_errors" hidden="hidden"/>
			</td>
		</tr>
		<tr align="center">
			<td><a href="#" id="error_close" class="update1">Close</a></td>
		</tr>
	</table>
	<div style="display:none">
		<form action="/erp/accounts/voucher/edit/" method="post">{% csrf_token %}
			<input type="hidden" value="{{voucherForm.id.value}}" class="voucher_id_in_class" name="voucher_no">
			<input type="submit" value="Edit" id="resubmit_edit_voucher" hidden="hidden">
		</form>
	</div>
	<div class="hide">
		<form target='_blank' action='/erp/accounts/voucher/edit/' method='post' id="voucher_clone_form">
			{% csrf_token %}
			<input type='hidden' id='edit_voucher_id' name='voucher_no' value='{{' />
			<input type='hidden' id='clone_voucher' name='cloneStatus' value='enable' />
		</form>
	</div>
</div>
<script language="javascript">
$( window ).load(function() {
	$("#loading").hide();
	setTimeout(function(){
	actualProjectsBudget($('#id_voucher-project_code').val(),$('#id_voucher-project_code').find(':selected').attr('project-type'));
    $('#id_voucher-project_code').change(function() {
            actualProjectsBudget($(this).val(),$('#id_voucher-project_code').find(':selected').attr('project-type'));
    });
    },1000);
});
$(document).ready(function(){
	if($(".header_current_page").text().indexOf("New") >=0 || $(".header_current_page").length == 0){
		$(".super_user_icon, .super_edit_for_draft").addClass('hide');
	}
	else{
		$(".super_user_icon, .super_edit_for_draft").removeClass('hide');
	}
	$.extend($.expr[":"], {
		"contains-ci": function(elem, i, match, array) {
			return (elem.textContent || elem.innerText || $(elem).text() || "").toLowerCase().indexOf((match[3] || "").toLowerCase()) >= 0;
		}
	});
	$(".chosen-select").chosen();
	SetDatePickerValue();
	enterKeySubmitEvent();
	EditPageFunctionality();
	$("#voucher_type").val($("#search_voucher_type").val());
	EditFunctionalityByClosureDate();
	TagitFocus();
	loadOpeningBalance();
	VoucherPrevNextPaging();
	
	if($("#is_super_user").val().toLowerCase() == 'true') {
		VoucherSuperEditInit();
	}
	if($('#hidden_clone_status').val() == "enable"){
		$('#id_hidden_voucher_id').val('');
		$('#id_voucher-id').val('');
		$('#voc_number').val('');
		$("#tab2").removeClass('div-disable');
		$('#enable_edit_voucher').addClass('hide');
	}
	changeLogActivityInit();

});

function cloneVoucher() {
	$("#edit_voucher_id").val($('#id_voucher-id').val());
	$("#voucher_clone_form").attr("target", "_blank").submit();
}

function VoucherPrevNextPaging() {
	if($(".header_current_page").text().indexOf("New") >=0) {
		$(".prev_next_container").remove();
		$(".create_voucher, .change_log").hide();
	}
	else {
		var VoucherListNav = JSON.parse(localStorage.getItem('voucherListNav'));
		if(VoucherListNav != null) {
			var curVoucherId = $("#id_hidden_voucher_id").val();
			for (var i = 0; i < VoucherListNav.length; i++){
			  if (VoucherListNav[i].voucherId == curVoucherId){
			  	if(i != 0) {
				    var prevVoucherId = VoucherListNav[i-1].voucherId;
				    var prevVoucherNo = VoucherListNav[i-1].voucherNumber;
				    var prevVoucherType = VoucherListNav[i-1].voucherType;
				}
				if(i != Number(VoucherListNav.length - 1)) {
			     	var NextVoucherId = VoucherListNav[i+1].voucherId;
			     	var NextVoucherNo = VoucherListNav[i+1].voucherNumber;
			     	var NextVoucherType = VoucherListNav[i+1].voucherType;
			     }
			  }
			}
			var PrevNextVoucher = "";
			if(prevVoucherId) {
				PrevNextVoucher += '<form action="/erp/accounts/voucher/edit/" method="post" id="edit_'+prevVoucherId+'"><div style="display:none"><input type="hidden" name="csrfmiddlewaretoken" value="'+getCookie('csrftoken')+'"></div> <a role="button" onclick="javascript:clickButton(&quot;edit_voucher_'+prevVoucherId+'&quot;);" class="btn btn-add-new pull-right btn-data-tooltip" data-tooltip="tooltip" title="Prev. Voucher: '+prevVoucherNo+'" style="margin-right: 7px;"><i class="fa fa-backward" aria-hidden="true"></i></a><input type="hidden" value="'+prevVoucherId+'" name="voucher_no"><input type="hidden" value="'+prevVoucherType+'" name="voucher_type"><input type="submit" value="Edit" id="edit_voucher_'+prevVoucherId+'" hidden="hidden"></form>';
			}
			else {
				PrevNextVoucher += '<form><a class="btn btn-add-new pull-right disabled btn-data-tooltip" data-tooltip="tooltip" title="Next/ Prev will appear only for the current page" style="padding: 5px 12px; margin-right: 7px;"><i class="fa fa-backward" aria-hidden="true"></i></a></form>';
			}
			if(NextVoucherId) {
				PrevNextVoucher += '<form action="/erp/accounts/voucher/edit/" method="post" id="edit_'+NextVoucherId+'"><div style="display:none"><input type="hidden" name="csrfmiddlewaretoken" value="'+getCookie('csrftoken')+'"></div> <a role="button" onclick="javascript:clickButton(&quot;edit_voucher_'+NextVoucherId+'&quot;);" class="btn btn-add-new pull-right btn-data-tooltip" data-tooltip="tooltip" title="Next Voucher: '+NextVoucherNo+'" style="margin-right: 7px;"><i class="fa fa-forward" aria-hidden="true"></i></a><input type="hidden" value="'+NextVoucherId+'" name="voucher_no"><input type="hidden" value="'+NextVoucherType+'" name="voucher_type"><input type="submit" value="Edit" id="edit_voucher_'+NextVoucherId+'" hidden="hidden"></form>';
			}
			else {
				PrevNextVoucher += '<form><a class="btn btn-add-new pull-right disabled btn-data-tooltip" data-tooltip="tooltip" title="Next/ Prev will appear only for the current page" style="padding: 5px 12px; margin-right: 7px;"><i class="fa fa-forward" aria-hidden="true"></i></a></form>';
			}

			$(".prev_next_container").html(PrevNextVoucher)
			TooltipInit();
		}
	}
}

function loadOpeningBalance() {
	try {
		if ($("#materialtable tr").length > 0) {
			ledger_ids = []
			for (i=0; i< $("#materialtable tr").length;i++) {
				ledger_id = $("#id_v_particular-"+ i +"-ledger_id").val();
				if (ledger_id != null && ledger_id != "") {
					ledger_ids.push($("#id_v_particular-"+ i +"-ledger_id").val());
				}
			}
			ledger_opening_criteria = {ledger_ids: ledger_ids.join(','), opening_on: $('#id_voucher-voucher_date').val()};
			loadLedgerOpening(ledger_opening_criteria);
		}
	} catch(e) {
		console.log("Opening balance", e);
	}
}

function EditFunctionalityByClosureDate() {
	$.ajax({
        url: '/erp/json/get_previous_closure_date/',
        type: "POST",
        dataType: "json",
        data: {
            enterprise_id: $("#enterprise_id").val()
        },
        success: function (data) {
    		if(Date.parse(data)>=Date.parse($("#id_voucher-voucher_date").val())) {
				$("#voucher_save_container").remove();
			}        
        }
    });	
}

function deleteLedger(voucherCode){
	var confirmDelete = confirm('Do you want to delete Ledger: ' + voucherCode);
	if (confirmDelete == true)
		clickButton('deleteLedger_'+voucherCode);
}

$(function(){
  	var hash = window.location.hash;
  	hash && $('ul.nav a[href="' + hash + '"]').tab('show');
  	$('.nav-tabs a').click(function (e) {
    	$(this).tab('show');
    	var scrollmem = $('body').scrollTop() || $('html').scrollTop();
    	window.location.hash = this.hash;
    	$('html,body').scrollTop(scrollmem);
  	});
});

$("#save_voucher_button").click(function(){
	if(!$('#id_voucher-project_code').val()){
        swal('','Please Select Project!','error');
        }
	$(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();
    var ControlCollections = [
        {
            controltype: 'dropdown',
            controlid: 'id_voucher-type_id',
            isrequired: true,
            errormsg: 'Voucher Type is required.'
        },
        {
            controltype: 'textbox',
            controlid: 'id_voucher-voucher_date',
            isrequired: true,
            errormsg: 'Date is required.'
        },
        {
            controltype: 'dropdown',
            controlid: 'id_voucher-project_code',
            isrequired: true,
            errormsg: 'Project Name is required.'
        }
    ];
	if ($("#id_voucher-type_id option:selected").text()== "Bank" && parseFloat($("#id_dr_total_amount").val()) != parseFloat($("#id_cr_total_amount").val())){
	    var control = {
	        controltype: 'dropdown',
	        controlid: 'id_bank_ledger',
	        isrequired: true,
	        errormsg: 'Please select bank name.'
	    };
	}
	ControlCollections[ControlCollections.length] = control;
	if($("#id_voucher-type_id option:selected").text()== "Cash" && parseFloat($("#id_dr_total_amount").val()) != parseFloat($("#id_cr_total_amount").val())) {
	    var control = {
	        controltype: 'dropdown',
	        controlid: 'id_cash_ledger',
	        isrequired: true,
	        errormsg: 'Please select cash type.'
	    };
	}
    ControlCollections[ControlCollections.length] = control;
    var result = JSCustomValidator.JSvalidate(ControlCollections);
	if(result) {
		saveVoucher();
	}
	return result;
});


function EditPageFunctionality() {
	if((window.location.pathname).indexOf('edit') >=0){
		$("#tab2").addClass('div-disable');
		$("#enable_edit_voucher").removeClass('hide');
		if($("#id_voucher-type_id").val() == 3) {
			$("#voucher_date").addClass('financial-date-end').removeClass("till-today");
		}
	}

	$("#enable_edit_voucher").click(function(){
		if(Date.parse($("#enterprise_previous_closure_date").val())>=Date.parse($("#id_voucher-voucher_date").val())) {
		    swal("","Accounts book has been closed for this voucher date. Please re-open the book to edit this voucher.","warning");
		}
		else {
			$("#tab2").removeClass('div-disable');
			$(this).addClass('hide');
		}
	});

	$("#tab1_view").click(function(){
		$("#tab2").removeClass('div-disable');
		$("#enable_edit_voucher").addClass('hide');
		window.location.assign('/erp/accounts/voucher/')
	});
}

function TagitFocus() {
    $(".tagit-display").click(function(){
        $(this).find('input[type="text"]').focus();
    });
}

function VoucherAndBillAmountCrossVerify(changedId) {
	setTimeout(function(){
		var bill_list = $(changedId).closest('tr').find('.td_bill_list');
		if(bill_list.text().trim() != "" || bill_list.hasClass('edited_bill_list')) {
			bill_list.find('a').click();
			$("#ledger_bill_div .modal-title").after("<p class='info_message text-center'><label>There was a change in your voucher amount. Please verify your bill amount too.</label></p>");
		}
	},100);
}

function VoucherSuperEditInit(){
	if($(".header_current_page").text().indexOf("New") >=0) {
		$(".super_user_icon, .xsid_number_edit, .super_user_tool_tip").remove();
		$(".super_edit_for_confirm, .super_edit_for_draft").remove();
		$(".super_edit_for_load").removeClass("hide");
	}
	else if($(".header_current_page").text().indexOf("TMP") >=0) {
		$(".super_user_icon, .xsid_number_edit, .super_user_tool_tip").remove();
		$(".super_edit_for_confirm").remove();
		$(".super_edit_for_load, .super_edit_for_draft").removeClass("hide");
	}
	else {
		$(".super_user_icon, .super_user_tool_tip").removeClass("hide");	
		$(".super_edit_for_load, .super_edit_for_draft, .super_edit_for_confirm").removeClass("hide");
	}
	$('.super_user_tool_tip span').qtip({
	   content: {
	        text: "<ul class='ul_for_tool_tip'><li>Super-edit enables modifying the Voucher Code subject to Duplication Check.</li>\
	        		   <li>Code format will be 'FY-FY/VT/NNNNNNx', <br />eg. '18-19/BV/000731b'.<br />\
	        		   FY details - 5 characters (max), <br />Voucher Type - 2 characters, <br />Number - 6 digits (max), <br />Sub-number - 1 alphabet (max).</li>\
	        		   <li>Subsequent numbering of Voucher will pick from the highest of the Voucher Code, <br/> i.e., gaps will not be filled automatically.</li></ul>",

	        title: 'Super-Edit'
	    }
	});
}

function EditVoucherNumber(editable) {
	var voucherNumber = $(".header_current_page").text().trim();
	var voucherNumberSplit = voucherNumber.split("/");
	$("#voc_financial_year").val(voucherNumberSplit[0]);
	$("#voc_type").val($("#voc_type option[data-val="+voucherNumberSplit[1]+"]").val())
	if($.isNumeric( voucherNumberSplit[2].substr(-1) )){
		$("#voc_number").val(voucherNumberSplit[2]);	
	}
	else {
		$("#voc_number").val(voucherNumberSplit[2].slice(0, -1));
		$("#voc_number_division").val(voucherNumberSplit[2].substr(-1));
	}
	if (editable) {
		$(".xsid_number_edit").removeClass("hide");
		$(".super_user_icon, .header_current_page").addClass("hide")
	}
}

function DiscardEditVoucherNumber(){
	$(".xsid_number_edit").addClass("hide");
	$(".super_user_icon, .header_current_page").removeClass("hide")
}

function SaveVoucherNumber(){
	if($("#voc_financial_year").val() =="" || $("#voc_number").val() == "" || $("#voc_type").val() == "") {
		$(".save_xsid_error_format").removeClass("hide");
		if($("#voc_financial_year").val() == "") $("#voc_financial_year").addClass("super_edit_error_border");
		if($("#voc_number").val() == "") $("#voc_number").addClass("super_edit_error_border");
		if($("#voc_type").val() == "") $("#voc_type").addClass("super_edit_error_border");
	}
	else {
		$(".save_xsid_error_format").addClass("hide");
		$("#voc_number_division").val($("#voc_number_division").val().toLowerCase());
		var start = new Date($("#voucher_date").val()),
            end   = new Date(),
            diff  = new Date(end - start),
            days  = diff/1000/60/60/24;
        if($("#voc_type").val() != 3) {
        	if(days < 0) {
        		var voucherName = $("#voc_type option:selected").text().slice(8,-1);
                swal({
                    title: "",
                    text: "<b>Note: </b>"+voucherName+" voucher cannot be set to future date. <br />So voucher date will be set to <b><i>Today</i></b>.",
                    type: "info",
            		allowEscapeKey: false,
            		showCancelButton: true,
                    confirmButtonColor: "#209be1",
                    confirmButtonText: "Yes, do it!",
                    closeOnConfirm: true,
                    closeOnCancel: true
                }, function(isConfirm){
                	if(isConfirm){
                		SaveVoucherNumberAjax();
                	}
                });
            }
            else {
            	SaveVoucherNumberAjax();
            }
        }
        else {
        	SaveVoucherNumberAjax();
        }
    }
}

function SaveVoucherNumberAjax(){
	$.ajax({
		url: "erp/accounts/json/super_edit_voucher_code/",
		method: "POST",
		data:{
			voucher_id: $("#id_hidden_voucher_id").val(),
			new_financial_year: $("#voc_financial_year").val(),
			new_voucher_type_id: $("#voc_type").val(),
			new_voucher_no: $("#voc_number").val(),
			new_sub_number: $("#voc_number_division").val().trim()
		},
		success: function(response) {
			if (response.response_message == "Success") {
				swal({
					title: "", 
					text: response.custom_message, 
					type: "success", 
            		allowEscapeKey: false
            	},
				function(){
					$("#resubmit_edit_voucher").click();
				});
                ga('send', 'event', 'Voucher', 'Super-Edit Code', $('#enterprise_label').val(), 1);
			} else {
				swal({title: "", text: response.custom_message, type: "warning"});
			}
		},
		error: function (xhr, errmsg, err) {
            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
        }
	});
}

function SuperEditVoucherDetails(field){
	$(field).closest("label").next(".div-disabled").removeClass("div-disabled");
	$(field).addClass("hide");
}

$('.nav-pills li').removeClass('active');
$('#li_voucher').addClass('active');

$("#addVoucher").submit(function(){
    ga('send', 'event', 'Voucher', {% if voucherForm.id.value != '' and voucherForm.id.value != None and  clone_view != 'enable' %}'Edit'{%else%}'Create'{%endif%},
    $('#enterprise_label').val(), 1);
});
</script>
{% endblock %}