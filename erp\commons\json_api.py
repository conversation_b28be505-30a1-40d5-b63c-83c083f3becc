"""
"""
import datetime
import json
import os
import uuid
from currency_converter import CurrencyConverter, SINGLE_DAY_ECB_URL
from django.http import HttpResponse, HttpResponseRedirect
from sqlalchemy.orm import make_transient

from erp.admin.backend import EnterpriseProfileService, PurchaseTemplateConfigService
from erp.auth import SESSION_KEY, ENTERPRISE_IN_SESSION_KEY, USER_FCM_ID_KEY, USER_IN_SESSION_KEY, \
	ENTERPRISE_CONFIGURATION_CHANGED, ENTERPRISE_SUBSCRIPTION, ENTERPRISE_ID_SESSION_KEY
from erp.auth.backend import LoginService
from erp.auth.request_handler import RequestHandler
from erp.commons import logger
from erp.commons.backend import NotificationService, BadgeService, getMailBody
from erp.commons.backend import sendMail
from erp.icd.backend import ICDService
from erp.models import Purchase<PERSON>rder, Invoice, OA, SalesEstimate, CreditDebitNote, PartyContactMap
from erp.sales.document_compiler import SalesEstimatePDFGenerator
from erp.sales.se_backend import SalesEstimateService
from settings import ANDROID_APP_VERSION, FORCE_UPDATE_ANDROID_APP, GCS_BUCKET_NAME
from settings import TEMP_DIR, SQLASession
from settings import storage_client, bucket, GCS_PUBLIC_URL
from util.api_util import response_code
from util.helper import readFile, getAbsolutePath

__author__ = 'nandha'


def registerFcmId(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)

		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		user_id = rh.getPostData('user_id')
		if not user_id:
			user_id = rh.getSessionAttribute(SESSION_KEY)
		fcm_id = rh.getPostData(USER_FCM_ID_KEY)
		if enterprise_id and user_id and fcm_id:
			rh.setSessionAttribute(USER_FCM_ID_KEY, fcm_id)
			logger.info("Registering device for notification...")
			if LoginService().registerFcmId(enterprise_id=enterprise_id, user_id=user_id, fcm_id=fcm_id):
				response = response_code.success()
			else:
				response = response_code.failure()
		else:
			response = response_code.paramMissing()
			logger.error("Invalid params")
	except Exception as e:
		logger.exception("Could not register device for notification de to >>> %s" % e)
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getNotificationCount(request):
	"""

	:param request:
	:return:
	"""
	try:

		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		service = EnterpriseProfileService()
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		user_id = rh.getPostData('user_id')
		if not user_id:
			user_id = rh.getSessionAttribute(SESSION_KEY)

		# Patch work to reflect enterprise configuration change to all users of the enterprise;
		if rh.getSessionAttribute(ENTERPRISE_SUBSCRIPTION)['shall_enable_expiry_timer'] is True:
			request.session[ENTERPRISE_SUBSCRIPTION] = LoginService().prepareEnterpriseSubscriptionInfo(
				enterprise_id=enterprise_id)
		# 	TODO have to update permission stub
		if rh.getSessionAttribute(ENTERPRISE_CONFIGURATION_CHANGED) == "YES":
			updated_enterprise = service.profile_dao.getProfile(enterprise_id=enterprise_id)
			make_transient(updated_enterprise)
			rh.setSessionAttribute(ENTERPRISE_IN_SESSION_KEY, updated_enterprise)
			rh.removeSessionAttribute(ENTERPRISE_CONFIGURATION_CHANGED)

		# Original scope of this function
		notification_count = NotificationService().dao.getCount(enterprise_id=enterprise_id, user_id=user_id)
		if notification_count is None:
			response = response_code.failure()
		else:
			response = response_code.success()
			response['notification_count'] = notification_count
	except Exception as e:
		logger.info("Fetch Notification Failed: %s" % e)
		response = response_code.internalError()
	logger.debug("Details %s" % response)
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getNotifications(request):
	"""

	:param request:
	:return:
	"""

	try:
		# FIXME 2.8.1 #NEW get count only on load page, on click load notifications
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		user_id = rh.getPostData('user_id')
		if not user_id:
			user_id = rh.getSessionAttribute(SESSION_KEY)
		if enterprise_id and user_id:
			data = NotificationService().getNotifications(enterprise_id=enterprise_id, user_id=user_id)
			if data is None:
				response = response_code.failure()
			else:
				response = response_code.success()
				response['notification_list'] = data
		else:
			response = response_code.paramMissing()
			logger.error("Invalid params")
	except Exception as e:
		logger.exception("Could not get notification list due to >>> %s" % e)
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def deleteNotifications(request):
	"""

	:param request:
	:return:
	"""

	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		user_id = rh.getPostData('user_id')
		if not user_id:
			user_id = rh.getSessionAttribute(SESSION_KEY)

		notification_ids = rh.getPostData('notification_ids')
		if notification_ids:
			notification_ids = notification_ids.split(",")

		ids = [int(n_id) for n_id in notification_ids]
		logger.info("Deleting %s notifications for user %s" % (len(ids), user_id))
		notification_service = NotificationService()
		if user_id and notification_ids:
			data = notification_service.deleteNotifications(user_id=user_id, nm_ids=ids)
			if data:
				response = response_code.success()
			else:
				response = response_code.failure()
			response['notification_count'] = (notification_service.dao.getCount(
				enterprise_id=enterprise_id, user_id=user_id))
		else:
			response = response_code.paramMissing()
			logger.error("Invalid params")
	except Exception as e:
		logger.exception("Could not update notification as read list due to >>> %s" % e)
		response = response_code.internalError()
	logger.info("Response:%s" % response)
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def broadCastNotification(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	message = rh.getPostData('message')
	collapse_key = rh.getPostData('collapse_key')
	if not message:
		message = "Default message %s " % datetime.datetime.now()
	NotificationService().pushNotificationToAll(message=message, collapse_key=collapse_key, action="broadcast")
	return HttpResponse(json.dumps({"Notification": "Success"}), 'content-type=text/json')


def versionInfo(request):
	"""

	:param request:
	:return:
	"""
	response = response_code.success()
	response["app_name"] = "XSerp"
	response["version"] = ANDROID_APP_VERSION
	response["force_update"] = FORCE_UPDATE_ANDROID_APP
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def fire_base_messaging_sw(request):
	return HttpResponse(readFile('/site_media/js/firebase-messaging-sw.js'), content_type='application/javascript')


def downloadFromFTP(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	enterprise_id = rh.getPostData('enterprise_id')
	if not enterprise_id:
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	document_uri = rh.getData('document_uri')
	document_from = rh.getData('document_from')
	document_name = rh.getData('document_name')
	grn_code = rh.getData('grn_code')
	attachment_file = document_uri.split("/")[1]
	blob_name = document_uri.split(".")[0]
	gcs_temp_loc = "%s/tmp/%s" % (getAbsolutePath('/site_media'), attachment_file)
	response = {}
	try:

		blob = bucket.blob(blob_name)
		blob.download_to_filename(gcs_temp_loc)
		if os.path.exists(gcs_temp_loc):
			with open(gcs_temp_loc, 'rb') as fh:
				response = HttpResponse(fh.read(), content_type="application/force-download")
				response['Content-Disposition'] = 'inline; filename=' + os.path.basename(document_name)
		return response
	except Exception as e:
		logger.exception("Download document failed for %s. %s" % (document_uri, e.message))
	return HttpResponseRedirect("<File not found URL>")


def getBadgeCount(request):
	"""
	get all the notification count for menu badges
	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	user_id = rh.getSessionAttribute(USER_IN_SESSION_KEY).user_id
	status = rh.getPostData('status')
	service = BadgeService()
	try:
		response = response_code.success()
		response['result'] = service.fetchBadgeCount(enterprise_id=enterprise_id, status=status, user_id=user_id)
	except Exception as e:
		logger.exception("fetch notification count failed because of. %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getInitialDate(request):
	"""
	Get starting date of each module which requested
	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	module_list = request.POST.getlist('list[]')
	status = rh.getPostData('status')
	service = BadgeService()
	try:
		response = response_code.success()
		response['result'] = service.fetchInitialDate(
			enterprise_id=enterprise_id, module_list=module_list, status=status)
	except Exception as e:
		logger.exception("fetch notification count failed because of. %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getMailID(request):
	logger.info('Enter into Get Mail Id Method...')
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	doc_id = request_handler.getData('id')
	doc_type = request_handler.getData('type')
	response = ""
	logger.info("The {type} ID and Enterprise ID : {id} , {enterprise_id}".format(
		type=doc_type, id=doc_id, enterprise_id=enterprise_id))
	try:
		if doc_type == "po":
			po = SQLASession().query(PurchaseOrder).filter(PurchaseOrder.po_id == doc_id).first()
			cc_email = getPartyEmail(enterprise_id=enterprise_id, party_id=po.supplier.id)
			response = {
				"code": po.getInternalCode(), "mail_id": po.supplier.primary_contact_details.contact.email
				if po.supplier.primary_contact_details is not None else "", "mail_id_cc": cc_email}
		elif doc_type == "sales" or doc_type == "dc" or doc_type == "internal":
			invoice = SQLASession().query(Invoice).filter(Invoice.id == doc_id).first()
			cc_email = ""
			if invoice.party_id:
				cc_email = getPartyEmail(enterprise_id=enterprise_id, party_id=invoice.customer.id)
			response = {
				"code": invoice.getCode(), "issuer": invoice.issued_to if invoice.issued_to else "",
				"mail_id": invoice.customer.primary_contact_details.contact.email
				if invoice.party_id and invoice.customer.primary_contact_details is not None else "", "mail_id_cc": cc_email}
		elif doc_type == "oa":
			oa = SQLASession().query(OA).filter(OA.id == doc_id).first()
			cc_email = getPartyEmail(enterprise_id=enterprise_id, party_id=oa.customer.id)
			response = {
				"code": oa.getInternalCode(), "mail_id": oa.customer.primary_contact_details.contact.email
				if oa.customer.primary_contact_details is not None else "", "mail_id_cc": cc_email}
		elif doc_type == "se":
			se = SQLASession().query(SalesEstimate).filter(SalesEstimate.id == doc_id).first()
			cc_email = getPartyEmail(enterprise_id=enterprise_id, party_id=se.customer.id)
			response = {
				"code": se.getInternalCode(), "mail_id": se.customer.primary_contact_details.contact.email
				if se.customer.primary_contact_details is not None else "", "mail_id_cc": cc_email}
		elif doc_type == "note" or doc_type == "icd":
			note = SQLASession().query(CreditDebitNote).filter(CreditDebitNote.id == doc_id).first()
			cc_email = getPartyEmail(enterprise_id=enterprise_id, party_id=note.party.id)
			response = {
				"code": note.getCode(), "mail_id": note.party.primary_contact_details.contact.email
				if note.party.primary_contact_details is not None else "", "mail_id_cc": cc_email,
				"receipt_no": note.note_receipt_map.receipt_no if doc_type == "icd" else "",
				"receipt_code": note.note_receipt_map.receipt.getCode() if doc_type == "icd" else ""}
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=json.dumps(response), mimetype='application/json')


def getPartyEmail(enterprise_id=None, party_id=None):
	"""

	:param enterprise_id:
	:param party_id:
	:return:
	"""
	logger.info("Fetch the party mail...")
	party_contact_details = ""
	try:
		party_contact_map = SQLASession().query(
			PartyContactMap).filter(
			PartyContactMap.enterprise_id == enterprise_id, PartyContactMap.party_id == party_id,
			PartyContactMap.sequence_id != 1).order_by(
			PartyContactMap.sequence_id).all()
		for contact_map in party_contact_map:
			party_contact_details += contact_map.contact.email if party_contact_details == "" else ", " + contact_map.contact.email
	except Exception as e:
		logger.exception("Failed to fetch Party contact details - %s" % e.message)
		raise
	return party_contact_details


def mailPDF(request):
	"""
	Sends a copy of the now generated document to any recepient intended
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	data = json.loads(request.body)
	_sender = request_handler.getSessionAttribute(USER_IN_SESSION_KEY)
	_enterprise_name = _sender.enterprise.name
	enterprise_id = _sender.enterprise.id
	sent_status = False
	try:
		path = TEMP_DIR + "/" + str(data['file_name'])
		url_base = request.build_absolute_uri().split("/erp/")[0]
		pdf_generator = None
		if data['type'] == "se":
			se_id = data['se_id']
			source = SalesEstimateService().sales_estimate_dao.getSalesEstimate(se_id=se_id)
			template_config = PurchaseTemplateConfigService().purchase_template_dao.getTemplateConfig(
				enterprise_id=enterprise_id, collection='sales_estimate_template_config')
			if template_config["print_template"] is None or template_config["print_template"].strip() == "":
				return None
			pdf_generator = SalesEstimatePDFGenerator().generatePDF(source=source, template_config=template_config, se_mail=True)
		mail_body = getMailBody(
			enterprise_id=enterprise_id, data=data, url_base=url_base,
			se_template=pdf_generator, icd_service=ICDService())
		logger.info("The PDF File Path : %s and Param %s" % (TEMP_DIR, data['file_name']))
		# create html email
		# TODO move the HTML code in to the file
		_mail_body = """<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
						"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
						<html xmlns="http://www.w3.org/1999/xhtml">
						<body style="font-size:12px;font-family:Arial">
							{body}
							{mail_body}
							{footer}
							<p>Regards<br/>{first_name} {last_name},<br/>{enterprise_name}</p></p>
						</body>
						</html>""".format(
			code=data['code'], first_name=_sender.first_name, last_name=_sender.last_name,
			enterprise_name=_enterprise_name, body=data['body'], mail_body=mail_body, footer=data['footer'])
		logger.debug(_mail_body)
		sent_status = sendMail(
			recipients=(data['send_to'],), cc_list=tuple(data['cc_to']), body=_mail_body,
			reply_to=(_sender.email,), subject="{subject} from {enterprise_name} - Reg".format(
				subject=data['subject'], enterprise_name=_enterprise_name),
			files=({"name": '%s.pdf' % data['code'], "path": path},), from_addr=_sender.email,
			from_alias="{first_name} {last_name}[{enterprise_name}]".format(
				first_name=_sender.first_name, last_name=_sender.last_name, enterprise_name=_enterprise_name))
		return HttpResponse(json.dumps("Success!!"))
	except Exception as e:
		logger.exception("Something went wrong while sending mail...%s" % e.message)
		pass
	return HttpResponse(
		json.dumps("Mail Delivered!!" if sent_status else "Some trouble with Mail delivery! Kindly Try again!"))


def get_gcs_bucket():
	"""
	get the GCS bucket stored
	:return:
	"""
	try:
		bucket = storage_client.bucket(GCS_BUCKET_NAME)
	except Exception as e:
		logger.info('No bucket available creating a new one %s' % e)
	return bucket


def generate_signed_post_policy_v4(bucket_name=None, blob_name=None):
	"""

	:param bucket_name:
	:param blob_name:
	:return:
	"""
	try:
		policy = storage_client.generate_signed_post_policy_v4(
			bucket_name,
			blob_name,
			expiration=datetime.timedelta(minutes=10),
			fields={'x-goog-meta-test': 'data'}
		)
		logger.info('GCS policy value: %s' % policy)
	except Exception as e:
		logger.exception(e)
	return policy


def getGCSSecurityKey(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	response = {'policy': []}
	try:
		ran_name = uuid.uuid4()
		blob_name = "{enterprise_id}/{uuid}".format(uuid=str(ran_name), enterprise_id=enterprise_id)
		response['policy'] = generate_signed_post_policy_v4(bucket_name=GCS_BUCKET_NAME, blob_name=blob_name)
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=json.dumps(response), mimetype='application/json')


def getGCSAttachmentById(request):
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	attachment_data = json.loads(request_handler.getPostData('attachment_data'))
	response = {}
	try:
		response = "{url}/{key}".format(url=GCS_PUBLIC_URL, key="{prefix}/{uid}".format(prefix=enterprise_id, uid=attachment_data['uid']))
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=json.dumps(response), mimetype='application/json')


def currencyConverter(request):
	"""
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	from_cur = request_handler.getPostData('from_currency')
	to_cur = request_handler.getPostData('to_currency')
	try:
		c = CurrencyConverter(SINGLE_DAY_ECB_URL, fallback_on_wrong_date=True, fallback_on_missing_rate=True)
		c = c.convert(1, str(from_cur), str(to_cur))
		c = format(c, '.5f')
	except Exception as e:
		logger.info("No Currency Value found for Currency:%s" % from_cur, )
		logger.info("Error:%s" % e.message)
		from_cur = "INR"
		c = CurrencyConverter(SINGLE_DAY_ECB_URL, fallback_on_wrong_date=True, fallback_on_missing_rate=True)
		c = c.convert(1, str(from_cur), str(to_cur))
		c = format(c, '.5f')
	return HttpResponse(content=json.dumps({"result": c}), mimetype='application/json')
