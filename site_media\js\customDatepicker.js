$(window).load(function(){
	CustomDatePickerInit();
	$(".custom-calender-icon").click(function(){
		$(this).prev('input').focus();
	});
});

function CustomDatePickerInit(){ 
	$(".custom_datepicker").datepicker({
		format: "M d, yyyy",
		autoclose: true,
		startDate: new Date("1900-01-01"),
		endDate: new Date("2900-12-31"),
		orientation: 'bottom auto'
	}).on('changeDate', customDateChanged);
	CustomDatePickerSettings();
}

function customDateChanged(ev) {
	UdateCustomDatePicker($(this).attr('id'));
}

function UdateCustomDatePicker(dateid) {
	if($("#"+dateid).val() != "") {
		var curDate = moment($("#"+dateid).val()).format('YYYY-MM-DD');
		$("#"+dateid).prev('input').val(curDate);
	}
	else {
		$("#"+dateid).prev('input').val("");
	}
}

function CustomDatePickerSettings(){
	var financialStart = $("#id_enterprise_fy_start_day").val().split('/');
	var financialStartMonth = financialStart[1] <= 12 ? financialStart[1] : 1;
	var currentYear = (new Date).getFullYear();
	var months = [ "Dec", "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec" ];
	var monthsDays = [ "31", "31", "29", "31", "30", "31", "30", "31", "31", "30", "31", "30", "31" ];
	if(moment().year(currentYear).month(months[financialStartMonth-1]).date(monthsDays[financialStartMonth-1]) < moment()){
		var NextFinanacialYear = moment().year(currentYear+1).month(months[financialStartMonth-1]).date(monthsDays[financialStartMonth-1]);
	}
	else {
		var NextFinanacialYear = moment().year(currentYear).month(months[financialStartMonth-1]).date(monthsDays[financialStartMonth-1])
	}

	$(".erasable-date").keyup(function(event){
		var key = window.event ? event.keyCode : event.which;
		if(event.keyCode == 8) {
			//$(this).datepicker("setDate", new Date());
			$(this).val("");
			UdateCustomDatePicker($(this).attr('id'));
		}
	});

	$(".editable-date").blur(function(){
		var that = $(this);
		UdateCustomDatePicker($(this).attr('id'));
		setTimeout(function(){
			if(moment(that.val()).format("MMM DD, YYYY") == "Invalid date") {
				that.closest("div").find(".date-format-error").show();
				that.closest("div").find(".date-format-error").delay(5000).fadeOut(1000);
			}
		},500);
	})

	$(".custom_datepicker").keypress(function(evt){
		if($(this).is(':not(.editable-date)')) {
			event.preventDefault();
		}
		else {
			var regex = new RegExp("^[0-9, \/-]+$");
		    var key = String.fromCharCode(!evt.charCode ? evt.which : evt.charCode);
		    var keyChar = evt.charCode ? evt.which : evt.charCode;
		    if(keyChar != 0){
		        if (!regex.test(key)) {
		            evt.preventDefault();
		            return false;
		        }
		    }
		}
  	});

	$(".custom_datepicker").each(function(){
		var dateval = $(this).prev('input').val();
		if(dateval == "") {
			if($(this).hasClass('set-empty-date')) {
				$(this).datepicker("setDate", "");
			}
			else {
				$(this).datepicker("setDate", new Date());
				UdateCustomDatePicker($(this).attr('id'));
			}
		}
		else {
			var newdate = moment(new Date(dateval)).format('MM-DD-YYYY');
			$(this).datepicker("setDate", newdate);
			UdateCustomDatePicker($(this).attr('id'));
		}

		if($(this).hasClass('set-my-start-date')) {
			var startDate = $(this).attr("data-setdate");
			if(startDate != ""){
				$(this).datepicker("setStartDate", new Date(startDate));
			}
		}

		if(!$(this).hasClass('full-datepicker')) {
			if($(this).hasClass('till-today')) {
				$(this).datepicker("setEndDate", new Date());
			}
			else if($(this).hasClass('financial-date-end')) {
				$(this).datepicker("setEndDate", new Date(NextFinanacialYear));
			}
		}
		
		if(!$(this).hasClass('conditional-date') && !$(this).hasClass('full-datepicker') && !$(this).hasClass('set-my-start-date')) {
			if($(this).hasClass('from-today')) {
				$(this).datepicker("setStartDate", new Date());
			}
			else if($(this).hasClass('from-beginning')) {
				$(this).datepicker("setStartDate", new Date("1900-01-01"));
			}
			else if($("#enterprise_previous_closure_date").val()) {
				var  enterprise_next_closure_date = moment($("#enterprise_previous_closure_date").val()).add(1, 'days');
				$(this).datepicker("setStartDate", new Date(enterprise_next_closure_date));
			}
		}
		
		if($(this).hasClass('erasable-date') || $(this).hasClass('editable-date')) {
			$(this).removeAttr('readonly');
		}
	});
}