from copy import copy
from datetime import datetime

import simplejson
import operator
from itertools import groupby
from dateutil.relativedelta import relativedelta
from django.http import HttpResponseRedirect, HttpResponse
from django.template.response import TemplateResponse

from erp import helper
from erp.accounts import logger, TRIAL_BALANCE, ACCOUNT_NOTES, ACCOUNT_BOOK, STOCK_TYPE_FG, STOCK_TYPE_WIP, \
	STATEMENTS_URL
from erp.accounts.backend import AccountService, AccountNoteVO
from erp.auth import ENTERPRISE_IN_SESSION_KEY, SESSION_KEY, ENTERPRISE_ID_SESSION_KEY, USER_IN_SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.forms import AccountGroupForm, AccountStatementInclusionsForm
from erp.models import AccountGroup, AccountNote, AccountStatement, Enterprise, Project
from erp.properties import LOGOUT_URL, TEMPLATE_TITLE_KEY, MANAGE_LEDGER_URL
from settings import SQLASession, MongoDbConnect
from util.api_util import response_code
from util.helper import copyFormToEntity, getFinancialYear, getFYStartDate, getFYDateRange

__author__ = 'saravanan, charles'


def runTrialBalance(request):
	"""
	Renders the Home page for Accounts Module. This will hold a Dashboard in near future.
	
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	accounts_service = AccountService()
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	closure_voucher = accounts_service.accounts_dao.getPreviousBookClosureVoucher(enterprise_id=enterprise_id)
	earliest_transaction_date = accounts_service.accounts_dao.getEarliestTransactionDate(
			enterprise_id=enterprise_id)
	previous_closure_date = closure_voucher.voucher_date if closure_voucher else earliest_transaction_date
	if previous_closure_date.date() == datetime.today().date():
		next_opening_date = closure_voucher.voucher_date if closure_voucher else earliest_transaction_date
	else:
		next_opening_date = closure_voucher.voucher_date + relativedelta(
			days=1) if closure_voucher else earliest_transaction_date
	closing_on = datetime.strptime(request_handler.getPostData(
		'closing_on'), '%Y-%m-%d') if request_handler.getPostData('closing_on') else datetime.now()
	opening_on = datetime.strptime(request_handler.getPostData(
		'opening_on'), '%Y-%m-%d') if request_handler.getPostData('opening_on') else next_opening_date
	is_provisional = request_handler.getPostData("is_provisional") if request_handler.getPostData(
		"is_provisional") else "true"
	projects = request_handler.getPostData("project_details")[:-1] if request_handler.getPostData(
		"project_details") else helper.populateAllProjectIds(enterprise_id)
	logger.info("Trial Balance generation began @ %s|Is Provisional: %s" % (datetime.now(), request_handler.getPostData(
		"is_provisional")))
	trial_balance = []
	inclusions = {}
	closure_voucher_code = request_handler.getSessionAttribute("message")
	request_handler.setSessionAttribute("message", "")
	if request_handler.getPostData('opening_on') or request_handler.getPostData('closing_on'):
		fg_opening = accounts_service.getStockValue(enterprise_id=enterprise_id, as_on=opening_on, type=STOCK_TYPE_FG)
		fg_closing = accounts_service.getStockValue(
			enterprise_id=enterprise_id, type=STOCK_TYPE_FG, as_on=closing_on + relativedelta(days=1))
		wip_opening = accounts_service.getStockValue(enterprise_id=enterprise_id, as_on=opening_on, type=STOCK_TYPE_WIP)
		wip_closing = accounts_service.getStockValue(
			enterprise_id=enterprise_id, type=STOCK_TYPE_WIP, as_on=closing_on + relativedelta(days=1))
		trial_balance = accounts_service.prepareTrialBalance(
			enterprise_id=enterprise_id, closing_on=closing_on)
		request_handler.setSessionAttribute(TRIAL_BALANCE, trial_balance)
		inclusions = {
			"consumables_opening": accounts_service.getStockValue(enterprise_id=enterprise_id, as_on=opening_on),
			"consumables_closing": accounts_service.getStockValue(
				enterprise_id=enterprise_id, as_on=closing_on + relativedelta(days=1)),
			"fg_opening": fg_opening, "fg_closing": fg_closing,
			"wip_opening": wip_opening, "wip_closing": wip_closing,
			"goods_opening": fg_opening + wip_opening, "goods_closing": fg_closing + wip_closing,
			"sales_tax": accounts_service.getSalesTaxesValue(
				enterprise_id=enterprise_id, since=opening_on, till=closing_on + relativedelta(days=1), projects=projects)}
	logger.info("Trial Balance generation ended @ %s" % datetime.now())
	return TemplateResponse(
		template='accounts/statements.html', context={
			"notes": [], "trial_balance": trial_balance, "profit_and_loss": [], "assets": [], "liabilities": [],
			"inclusions": AccountStatementInclusionsForm(initial=inclusions), "opening_on": opening_on,
			"closing_on": closing_on, "is_provisional": is_provisional, "previous_closure_date": previous_closure_date,
			"closure_voucher_code": closure_voucher_code, "next_opening_date": next_opening_date,
			"closed_books": accounts_service.getBooks(enterprise_id=enterprise_id), TEMPLATE_TITLE_KEY: "P&L Statements",
			"projects": projects},
		request=request)


def generateStatements(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	accounts_service = AccountService()
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	opening_on = datetime.strptime(request_handler.getPostData('opening_on'), '%Y-%m-%d')
	closing_on = datetime.strptime(request_handler.getPostData('closing_on'), '%Y-%m-%d')
	is_provisional = request_handler.getPostData("is_provisional") if request_handler.getPostData(
		"is_provisional") else "true"
	projects = request_handler.getPostData("statement_project_details")[:-1] if request_handler.getPostData(
		"statement_project_details") else helper.populateAllProjectIds(enterprise_id)
	trial_balance = request_handler.getSessionAttribute(TRIAL_BALANCE)
	inclusions_form = AccountStatementInclusionsForm(request_handler.getPostData())
	notes = dict()
	pnl_statement, assets_statement, liabilities_statement = [None, None, None]
	if inclusions_form.is_valid():
		logger.info("Provisional Statements generation began @ %s" % datetime.now())
		pnl_notes = accounts_service.prepareNotes(
			note_type=AccountNote.PNL_TYPE, enterprise_id=enterprise_id, opening_on=opening_on, closing_on=closing_on)
		bs_notes = accounts_service.prepareNotes(
			note_type=AccountNote.BS_TYPE, enterprise_id=enterprise_id, closing_on=closing_on)
		notes["bs"] = bs_notes[0]
		notes["pl"] = pnl_notes[0]
		note_values = pnl_notes[1].copy()
		note_values.update(bs_notes[1])
		notes = accounts_service.addInclusionsAndExclusions(
			account_note_vo_dicts=notes, note_values=note_values, inclusion_values=inclusions_form.clean())
		pnl_statement = accounts_service.prepareStatement(note_values=note_values, note_type=AccountNote.PNL_TYPE)
		assets_statement = accounts_service.prepareStatement(
			note_type=AccountNote.BS_TYPE, group=AccountStatement.ASSET_GROUP, note_values=note_values)
		liabilities_statement = accounts_service.prepareStatement(
			note_type=AccountNote.BS_TYPE, group=AccountStatement.LIABILITY_GROUP, note_values=note_values)
		request_handler.setSessionAttribute(ACCOUNT_BOOK, AccountNoteVO(ACCOUNT_BOOK, children=[
			AccountNoteVO(TRIAL_BALANCE, children=copy(trial_balance)), pnl_statement, assets_statement,
			liabilities_statement, AccountNoteVO(label=ACCOUNT_NOTES, children=notes)]))
	logger.info("Provisional Statements generation ended @ %s" % datetime.now())
	return TemplateResponse(
		template='accounts/statements.html', context={
			"notes": notes, "trial_balance": trial_balance,
			"profit_and_loss": pnl_statement.children if pnl_statement else [],
			"assets": assets_statement if assets_statement else [],
			"liabilities": liabilities_statement if liabilities_statement else [], "inclusions": inclusions_form,
			"opening_on": opening_on, "closing_on": closing_on, "is_provisional": is_provisional,
			"previous_closure_date": datetime.strptime(request_handler.getPostData('previous_closure_date'), '%Y-%m-%d'),
			"next_opening_date": datetime.strptime(request_handler.getPostData('next_opening_date'), '%Y-%m-%d'),
			"closed_books": accounts_service.getBooks(enterprise_id=enterprise_id), TEMPLATE_TITLE_KEY: "Statements",
			"projects": projects},
		request=request)


def closeBook(request):
	"""
	
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise = request_handler.getSessionAttribute(ENTERPRISE_IN_SESSION_KEY)
	accounts_service = AccountService()
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	opening_on = datetime.strptime(request_handler.getPostData('opening_on'), '%Y-%m-%d')
	closing_on = datetime.strptime(request_handler.getPostData('closing_on'), '%Y-%m-%d')
	is_provisional = request_handler.getPostData("is_provisional") if request_handler.getPostData(
		"is_provisional") else "true"
	inclusions_form = AccountStatementInclusionsForm(request_handler.getPostData())
	account_book = request_handler.getSessionAttribute(ACCOUNT_BOOK)
	book_closure = accounts_service.closeBook(
		account_book_vo=account_book, enterprise_id=enterprise.id, financial_year=getFinancialYear(
			for_date=opening_on, fy_start_day=enterprise.fy_start_day), since=opening_on, till=closing_on,
		created_by=user_id, inclusions=inclusions_form.clean() if inclusions_form.is_valid() else {})
	if book_closure["success"]:
		request_handler.setSessionAttribute("message", "<h3>Statement Finalized!</h3>Closure Voucher: <b>%s</b>" % (
			book_closure["voucher"].getCode() if book_closure["voucher"] else "None"))
		request_handler.removeSessionAttribute("opening_on")
		request_handler.removeSessionAttribute("closing_on")
		return HttpResponseRedirect(STATEMENTS_URL)
	return TemplateResponse(
		template='accounts/statements.html', context={
			"notes": account_book.getChild(label=ACCOUNT_NOTES).children,
			"trial_balance": account_book.getChild(label=TRIAL_BALANCE).children,
			"profit_and_loss": account_book.getChild(label="PL").children,
			"assets": account_book.getChild(label="Assets"),
			"liabilities": account_book.getChild(label="Equities and Liabilities"), "inclusions": inclusions_form,
			"opening_on": opening_on, "closing_on": closing_on, "is_provisional": is_provisional,
			"previous_closure_date": datetime.strptime(request_handler.getPostData('previous_closure_date'), '%Y-%m-%d'),
			"next_opening_date": datetime.strptime(request_handler.getPostData('next_opening_date'), '%Y-%m-%d'),
			"closed_books": accounts_service.getBooks(enterprise_id=enterprise.id), TEMPLATE_TITLE_KEY: "Statements"},
		request=request)


def viewBook(request):
	"""
	View a Frozen Account Book, stored as a JSON
	
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	accounts_service = AccountService()
	since = datetime.strptime(request_handler.getPostData('since'), '%Y-%m-%d')
	till = datetime.strptime(request_handler.getPostData('till'), '%Y-%m-%d')
	account_book = accounts_service.generateBookVO(enterprise_id=enterprise_id, since=since, till=till)
	return TemplateResponse(
		template='accounts/statements.html', context={
			"notes": account_book.getChild(label=ACCOUNT_NOTES).children,
			"trial_balance": account_book.getChild(label=TRIAL_BALANCE).children,
			"profit_and_loss": account_book.getChild(label="PL").children,
			"assets": account_book.getChild(label="Assets"),
			"liabilities": account_book.getChild(label="Equities and Liabilities"),
			"opening_on": since, "closing_on": till, "is_provisional": "false",
			"previous_closure_date": since - relativedelta(days=1), "next_closure_date": since, "view_book": "true",
			"closed_books": accounts_service.getBooks(enterprise_id=enterprise_id), TEMPLATE_TITLE_KEY: "Statements"},
		request=request)


def reOpenBooks(request):
	"""
	Requests re-opening of Account Books for the criteria sent through the request
	
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	accounts_service = AccountService()
	since = datetime.strptime(request_handler.getPostData("since"), "%Y-%m-%d") if request_handler.getPostData(
				"since") else datetime.today()
	if accounts_service.reOpenBooks(enterprise_id=enterprise_id, sender_id=user_id, since=since):
		request_handler.setSessionAttribute(
			"message", "Account Books Finalized since %s has been Re-opened!" % since.strftime("%B %d, %Y"))
	else:
		request_handler.setSessionAttribute(
			"message", "Unable to Re-Open Books since %s" % since.strftime("%B %d, %Y"))
	return HttpResponseRedirect(STATEMENTS_URL)


def renderAccountsDashboard(request):
	"""

	:param request:
	:return: TemplateResponse
	"""
	logger.debug("Get the account dashboard details")
	try:
		response = {}
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		logger.info('Fetching dashboard data for enterprise %s' % enterprise_id)
		since, till = AccountService().getSelectFyDateRange(enterprise_id=enterprise_id)
		logger.debug("Dashboard entries %s " % response)
	except Exception as e:
		logger.exception("Failed fetching account dashboard entries. %s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message

	return TemplateResponse(template='accounts/dashboard.html', context={"fiscal_start_date": since,
		TEMPLATE_TITLE_KEY: "Finance"}, request=request)


def getTopCustomers(request):
	"""

	:param request:
	:return:
	"""
	try:
		response = {}
		request_handler = RequestHandler(request)
		since = request.POST['since'].encode("ascii", "replace")
		till = request.POST['till'].encode("ascii", "replace")
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		response["sales_top_customers"] = AccountService().getSalesTopCustomers(
			enterprise_id=enterprise_id, since=datetime.strptime(since, "%Y-%m-%d"),
			till=datetime.strptime(str(till + " 23:59:59"), "%Y-%m-%d %H:%M:%S"))
		response["sales_top_customers_total"] = sum(d['closing_balance'] for d in response["sales_top_customers"])
		for item in response['sales_top_customers']:
			item['AccountGroup'] = str(item['AccountGroup'])
			item['name'] = str(item['name'])
			item['closing_balance'] = int(item['closing_balance'])
	except Exception as e:
		logger.exception("Failed fetching account dashboard entries. %s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message

	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getSaveMatchedGstr3b(request):
	"""

	:param request:
	:return:
	"""
	response = response_code.failure()
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		data = simplejson.loads(request_handler.getPostData('data'))
		db = MongoDbConnect['gstr3b']
		db_insert_query = dict()
		db_insert_query['uid'] = str(enterprise_id)
		db_insert_query['created_at'] = datetime.now()
		db_insert_query['return_month'] = '072017'
		# for key, items in groupby(data['data'], key=operator.itemgetter("ctin")):
		# 	print("%s: %s" % (key, list(items)))
		data['uid'] = enterprise_id
		if len(data) > 0:
			if db.count_documents({'uid': int(enterprise_id)}) == 0:
				db.insert(data)
			else:
				for insert_data in data['data']:
					db.update_one({'uid': int(enterprise_id)}, {'$push': {'data': insert_data}})
			response = response_code.success()
	except Exception as e:
		logger.exception("Failed to save GSTR3b report %s" % e.message)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getClearMatchedGstr3b(request):
	"""

	:param request:
	:return:
	"""
	response = response_code.failure()
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		db = MongoDbConnect['gstr3b']
		if db.count_documents({'uid': int(enterprise_id)}) > 0:
			db.remove({'uid': int(enterprise_id)})
			response = response_code.success()
		else:
			response = response_code.databaseError()
	except Exception as e:
		logger.exception("Failed to save GSTR3b report %s" % e.message)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def gstr1_purchase_report_page(request):
	return TemplateResponse(template='accounts/gstr1_purchase_report.html', request=request, context={
		TEMPLATE_TITLE_KEY: "Purchase Register - GST Related"})


def gstr2_purchase_report_page(request):
	return TemplateResponse(template='accounts/gstr2_purchase_report.html', request=request, context={
		TEMPLATE_TITLE_KEY: "GSTR-2 Purchase Report"})


def gstr1_sales_report_page(request):
	return TemplateResponse(template='accounts/gstr1_sales_report.html', request=request, context={
		TEMPLATE_TITLE_KEY: "GSTR-1 Sales Report"})


def gstr3b_reconciliation_page(request):
	return TemplateResponse(template='accounts/gstr3b_reconcillation.html', request=request, context={
		TEMPLATE_TITLE_KEY: "GSTR 3B - GST Related"})


def tcs_report_page(request):
	return TemplateResponse(template='accounts/tcs_report.html', request=request, context={
		TEMPLATE_TITLE_KEY: "TCS Related"})


def gvbv_report_page(request):
	return TemplateResponse(template='accounts/gvbv_report.html', request=request, context={
		TEMPLATE_TITLE_KEY: "GV and BV GSTR"})


def sales_item_wise_report_page(request):
	return TemplateResponse(template='accounts/sales_item_wise_report.html', request=request, context={
		TEMPLATE_TITLE_KEY: "Sales Item wise Report"})


def outstanding_report_page(request):
	return TemplateResponse(template='accounts/outstanding_report.html', request=request, context={
		TEMPLATE_TITLE_KEY: "Outstanding of Payables and Receivables"})


def createAccountGroup(request):
	"""

	:param request:
	:return:
	"""
	logger.debug("Creating an Account Group")
	request_handler = RequestHandler(request)
	enterprise_id_in_session = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	if request_handler.isSessionActive():
		db_session = SQLASession()
		db_session.begin(subtransactions=True)
		try:
			account_form = AccountGroupForm(data=request_handler.getPostData(), prefix='account_group')
			account_group = AccountGroup(
				enterprise_id=enterprise_id_in_session, created_by=request_handler.getSessionAttribute(SESSION_KEY))
			if account_form.is_valid():
				logger.debug("Saving the new Group")
				copyFormToEntity(account_form, account_group)
				account_group.config_flags = sum(int(i) for i in account_form.cleaned_data['config_flags'])
				parent_id = account_form.cleaned_data['parent_id']
				account_group_note_no = db_session.query(AccountGroup.note_no).filter(
					AccountGroup.enterprise_id == enterprise_id_in_session, AccountGroup.parent_id == parent_id).first()
				if account_group_note_no:
					account_group.note_no = int(account_group_note_no.note_no)
				latest_group_id = db_session.query(AccountGroup.id).filter(
					AccountGroup.enterprise_id == enterprise_id_in_session).order_by(AccountGroup.id.desc()).first()
				account_group.id = int(latest_group_id.id) + 1
				db_session.add(account_group)
			db_session.commit()
			logger.debug("Account Group form errors: %s %s" % (
				account_form.errors, "VALID" if account_form.is_valid() else "INVALID"))
		except Exception as e:
			db_session.rollback()
			logger.exception("Could not create Account Group %s " % e)
		return HttpResponseRedirect(MANAGE_LEDGER_URL)
	return HttpResponseRedirect(LOGOUT_URL)


def loadProjects(request):
	"""
	Loads All Project information.

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	project_choices = []
	project_list = SQLASession().query(Project).filter(Project.enterprise_id == enterprise_id, Project.is_active == True)
	for project in project_list.order_by(Project.name):
		project_choices.append((project.id, project.name))
	return HttpResponse(content=simplejson.dumps(project_choices), mimetype='application/json')
