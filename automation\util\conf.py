import os
import pymysql
import email
import smtplib
from logging import handlers
import logging
from datetime import datetime
from dateutil.relativedelta import relativedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import scoped_session, sessionmaker
from email.mime.text import MIMEText

# Logs
PATH = os.path.dirname(os.path.realpath(__file__))
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log_path = "/var/log/django/voucher_bulk_clone.log"
logHandler = handlers.TimedRotatingFileHandler(log_path, when='midnight', interval=1, backupCount=100)
logHandler.setFormatter(formatter)
logger = logging.getLogger(os.path.splitext(os.path.basename(__file__))[0])
# Setting the threshold of logger to DEBUG
logger.setLevel(logging.DEBUG)
logger.addHandler(logHandler)

# XSERP credentials
API_BASE_URL = "http://localhost:8000"
CREDENTIALS = {u'fcm_id': ['TestFromPostman'], u'csrfmiddlewaretoken': ['2085bdf9f70dcf986b0b542863cfb3aa'],
               u'password': ['schnell@369'], u'user_email': ['<EMAIL>']}
HEADER = {'Content-type': 'application/x-www-form-urlencoded',
          'Accept': '*/*',
          "cookie": "csrftoken=2085bdf9f70dcf986b0b542863cfb3aa"}
PARENT_ENTERPRISE_ID = 102
PROJECT_CODE = 4550
VOUCHER_DATE = "2024-05-09"

DB_PORT = 25394
DB_HOST = "localhost"
DB_USER = "xdevops"
DB_PASSWORD = "D3v$EnIq!^3"
DB_NAME = "migutharavu"

# SMTP credentials
SMTP_USER = "<EMAIL>"
SMTP_USER_PASSWORD = "mythili@12345"
SMTP_SERVER = "smtp.gmail.com"
SMTP_PORT = 587


DATABASES = {
	'default': {
		'ENGINE': 'django.db.backends.mysql',
		# Add 'postgresql_psycopg2', 'postgresql', 'mysql', 'sqlite3' or 'oracle'.
		'NAME': DB_NAME,  # Or path to database file if using sqlite3.
		'USER': DB_USER,  # Not used with sqlite3.
		'PASSWORD': DB_PASSWORD,  # Not used with sqlite3.
		'HOST': DB_HOST,  # Set to empty string for localhost. Not used with sqlite3.
		'PORT': DB_PORT,  # Set to empty string for default. Not used with sqlite3.
	}
}
ENGINE = create_engine(
	'mysql+pymysql://{USER}:{PASSWORD}@{HOST}:{PORT}/{NAME}?charset=utf8mb4&binary_prefix=true'.format(
		**DATABASES['default']),
	echo=True, pool_recycle=3600, isolation_level="READ COMMITTED")
ORMSessionFactory = sessionmaker(bind=ENGINE, autoflush=False, autocommit=True)
SQLASession = scoped_session(ORMSessionFactory)



def executeQuery(query, as_dict=False, query_data=None, conversions=None):
    """
    Executes a query & returns a result-set, as a list of either indexed tuples or dicts

    :param query:
    :param as_dict: Flag to specify the type of result-set, if True return a list of dicts, else a list of tuples
    :param query_data: Query parameters
    :param conversions
    :return:
    """
    if conversions is None:
        conversions = pymysql.converters.conversions.copy()
    db_connection = pymysql.connect(host=DB_HOST, user=DB_USER, passwd=DB_PASSWORD, db=DB_NAME, port=DB_PORT,
                                    conv=conversions, charset="utf8mb4", binary_prefix=True)
    if as_dict:
        db_connection.cursorclass = pymysql.cursors.DictCursor
    cursor = db_connection.cursor()
    try:
        cursor.execute(query, query_data)
        query_result = cursor.fetchall()
        db_connection.commit()
        return query_result

    except Exception as e:
        logger.exception("Query cannot be executed - %s" % e)
        db_connection.rollback()
        raise
    finally:
        db_connection.close()


def sendMail(
        recipients=(), cc_list=(), subject="", body="", files=(), from_alias="", from_addr=SMTP_USER, reply_to=(),
        sender_mail=SMTP_USER, sender_password=SMTP_USER_PASSWORD, mail_server=SMTP_SERVER, mail_port=SMTP_PORT):
    """
    Utility to send an SMTP mail from Server

    :param recipients:
    :param cc_list:
    :param subject:
    :param body:
    :param files:
    :param from_alias:
    :param from_addr:
    :param reply_to:
    :param sender_mail:
    :param sender_password:
    :param mail_server:
    :param mail_port:
    :return:
    """
    try:
        mail_content = email.MIMEMultipart.MIMEMultipart('alternative')
        mail_content['subject'] = subject
        mail_content['from'] = "%s <%s>" % (from_alias, from_addr)
        mail_content['to'] = ",".join(recipients)
        mail_content['reply-to'] = ", ".join(reply_to)
        if len(cc_list) > 0:
            mail_content['Cc'] = ",".join(cc_list)
        mail_content.attach(email.mime.text.MIMEText(body, 'html'))
        # now attach the file
        for _file_item in files:
            attachment = email.mime.base.MIMEBase('application', 'octet-stream')
            attachment.set_payload(file(_file_item["path"], "rb").read())
            email.encoders.encode_base64(attachment)
            attachment.add_header('Content-Disposition', 'attachment;filename=' + _file_item["name"])
            mail_content.attach(attachment)

        to_addrs = recipients + cc_list
        mailer = smtplib.SMTP(mail_server, mail_port)
        mailer.ehlo()
        mailer.starttls()
        mailer.login(sender_mail, sender_password)
        mailer.sendmail(from_addr=sender_mail, to_addrs=to_addrs, msg=mail_content.as_string())
        mailer.quit()
        return True
    except Exception as e:
        logger.exception("Sending Mail Failed: %s" % e)
        pass
    return False


def getFinancialYear(for_date=None, fy_start_day='01/04'):
        if not for_date:
            for_date = datetime.now()
        if fy_start_day == '01/01':
            return '%s' % for_date.year
        fy_start = fy_start_day.split('/')
        fy_start_date = datetime(day=int(fy_start[0]), month=int(fy_start[1]), year=for_date.year)
        fy_start_date += relativedelta(years=0 if for_date >= fy_start_date else -1)
        fy_end_date = fy_start_date + relativedelta(years=+1) + relativedelta(microseconds=-1)
        return '%s-%s' % (('%s' % fy_start_date.year)[2:4], ('%s' % fy_end_date.year)[2:4])