<!DOCTYPE html>
<style type="text/css">
	body{
		color: #000;
		font-family: 'Times New Roman';
	}
	table{
		width:100%;
		border-color:#000;
	}
	td {
		text-align: left;
		line-height: 25px;
		font-size:10pt;
		padding:5px !important;
	}
	th{
		font-size:11pt;
		text-align:center !important;
		padding: 0px 5px !important;
	}
	.container{
		width:900px !important;
	}
	.enterprise_details{
		font-size:9pt;
	}
	.enterprise_name{
		font-size:11pt
	}
	.page_title{
		font-size: 15pt;
		margin-left: -112px;
	}

	.tax_data {
	    width: calc(60% - 1em);
	    float: left;
	    font-size:9pt;
	}
	.text_bold{
		font-weight:bold;
	}
	.text_center{
		text-align:center;
	}

	.pay_description_earnings{
		text-align:left !important;
		border-bottom:1px solid #000;
		padding:8px 4px !important;
		border-right:hidden;
	}
	.pay_description_deduction{
		border-bottom:1px solid #000;
		padding:8px 6px!important;
	}

	.pay_earnings{
		border-bottom:hidden;
		border-right:hidden;
	}
	@font-face {
        font-family: 'Times New Roman';
        font-style: normal;
        font-weight: 400;
        src: url(https://www.xserp.in/site_media/fonts/times.ttf) format('truetype');
	}
</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/bootstrap.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<body>
<div class="container">
	<div>
		<div class="col-sm-8">
			<img src="{{enterprise_logo}}" style="max-height: 10mm">
			<div class="enterprise_name">{{ enterprise.name }}</div>
			<div class="enterprise_details"> {{ enterprise_address }}</div>
			<div class="enterprise_details"><b>Ph:</b>{% if enterprise.primary_contact_details.contact.phone_no %} {{ enterprise.primary_contact_details.contact.phone_no }}   {% endif %}<b>E-mail:</b>{% if enterprise.primary_contact_details.contact.email %} {{ enterprise.primary_contact_details.contact.email }} {% endif %}</div>
		</div>

		<div class="col-sm-4" style="text-align:center;!important;">
			<div class="col-sm-6"></div>
			<div class="col-sm-6" style="margin-top:20px;">
				<span class="page_title" >{{ form_name }}</span>
			</div>
			<div style="margin-left:20px;">{{ month }}</div>
		</div>
	</div>
	<div class="col-sm-12">
		<table border=1 bordercolor="#000" class="row-seperator column-seperator" style="width:100% !important;">
			<tr>
				<td class="text_bold" style="width:100px;">Emp code</td>
				<td colspan="2" style="width:100px;">{{ employee_details.emp_code }}</td>
				<td class="text_bold" style="width:100px;">Emp Name</td>
				<td colspan="5">{{ employee_details.emp_name }}</td>
			</tr>
			<tr>
				<td class="text_bold" style="width:100px;">DOJ</td>
				<td colspan="2" style="width:100px;">{% if employee_details.date_of_joining %} {{ employee_details.date_of_joining|date:'d/m/Y' }} {% endif %}</td>
				<td class="text_bold" style="width:100px;">S/O,D/O,W/O</td>
				<td colspan="5">{{ employee_details.father_name }}</td>
			</tr>
			<tr>
				<td class="text_bold" style="width:100px;">ESI NO</td>
				<td colspan="2" style="width:100px;">{% if employee_details.esi_no %} {{ employee_details.esi_no }} {% endif %}</td>
				<td class="text_bold" style="width:100px;">Department</td>
				<td colspan="5">{% if employee_details.department %} {{ employee_details.department.name }} {% else %} - {% endif %}</td>
			</tr>
			<tr>
				<td class="text_bold" style="width:100px;">PF NO</td>
				<td colspan="2" style="width:100px;">{% if employee_details.pf_no %}{{ employee_details.pf_no }}{% endif %}</td>
				<td class="text_bold" style="width:100px;">Designation</td>
				<td colspan="5">{% if employee_details.designation %} {{ employee_details.designation }}  {% else %} - {% endif %}</td>
			</tr>
			<tr>
				<td class="text_bold text_center">DAYS WD</td>
				<td class="text_bold text_center">WO</td>
				<td class="text_bold text_center">HD</td>
				<td class="text_bold text_center">EL</td>
				<td class="text_bold text_center">CL</td>
				<td class="text_bold text_center">CPL</td>
				<td class="text_bold text_center">LOP</td>
				<td class="text_bold text_center">Total</td>
				<td class="text_center"><b>EL Bal:</b>{{ employee_details.no_of_el }}</td>
			</tr>

			<tr>
				<td class="text_center" style="width:90px;">{{ attendance_log.description.WD }}</td>
				<td class="text_center" style="width:90px;">{{ attendance_log.description.WO }}</td>
				<td class="text_center" style="width:90px;">{{ attendance_log.description.HD }}</td>
				<td class="text_center" style="width:90px;">{{ attendance_log.description.EL }}</td>
				<td class="text_center" style="width:90px;">{{ attendance_log.description.CL }}</td>
				<td class="text_center" style="width:90px;">{{ attendance_log.description.CPL }}</td>
				<td class="text_center" style="width:90px;">{{ attendance_log.description.LOP }}</td>
				<td class="text_center" style="width:90px;">{{ attendance_log.description.TSD }}</td>
				<td class="text_center" style="width:90px;"><b>CL Bal:</b>{{ employee_details.no_of_cl }}</td>
			</tr>
		</table>

		<table border=1 bordercolor="#000" class="row-seperator column-seperator" style="width:100%;">
			<tr style="height:35px;">
				<td style="border-top:hidden;border-bottom:hidden;width:51%;"></td>
				<td style="border-top:hidden;border-bottom:hidden;"></td>
			</tr>
		</table>

		<table border=1 bordercolor="#000" class="row-seperator column-seperator" style="width:100%;">
			<tr>
				<th class="pay_description_earnings" style="text-align:left !important;width:170px;border-left:1px solid #000;border-bottom:hidden;">Description</th>
				<th class="pay_description_earnings" style="text-align:right !important;width:109px;border-bottom:hidden;">Fixed</th>
				<th class="pay_description_earnings" style="text-align:right !important;width:147px;border-right:1px solid;border-right:1px solid #000;border-bottom:hidden;">Earned</th>
				<th class="pay_description_deduction" style="text-align:left !important;border-right:hidden;border-bottom:hidden;">Description</th>
				<th class="pay_description_deduction" style="text-align:right !important;border-right:1px solid #000;border-bottom:hidden;">Amount</th>
			</tr>
		</table>

		<table border=1 bordercolor="#000" class="row-seperator column-seperator" style="width:100%;">
			<tr>
				<td  style="width:51%;vertical-align:top;">
					<table>
						{% for item in earnings %}
							<tr>
								<td style="width:170px !important;">{{ item.description }}</td>
								<td style="text-align:right;width:109px !important;">{{ item.fixed }}</td>
								<td style="text-align:right;width:140px !important;">{{ item.earned }}</td>
							</tr>
						{% endfor %}
					</table>
				</td>
				<td style="vertical-align:top; padding: 0 !important;">
					<table>
						{% for item in deductions %}
							<tr style="{% if forloop.counter != 1 %}border-top:1px solid #000;{% endif %} padding: 0;">
								<td style="border-right:hidden;text-align:left; padding: 0;">{{ item.description }}</td>
								<td style="border-right:hidden;text-align:right; padding: 0;">{{ item.amount }}</td>
							</tr>
						{% endfor %}
					</table>
				</td>
			</tr>
		</table>

		<table border=1 bordercolor="#000" class="row-seperator column-seperator" style="width:100%;">

			<tr style="border-bottom:1px solid #000;">
				<td style="width:170px;border-right:1px solid #000;border-top:hidden;" class="text_bold">Gross Earnings</td>
				<td style="width:109px;border-right:1px solid #000;text-align:right;border-top:hidden;">{{ earning_summary.total_fixed }}</td>
				<td style="width:147px;text-align:right;border-top:hidden;">{{ earning_summary.total_earnings }}</td>
				<td class="text_bold" style="border-top:hidden;border-right:hidden;">Total Deduction</td>
				<td style="text-align:right;border-top:hidden;">{{ deductions_summary.total_deduction }}</td>
			</tr>
			<tr>
				<td class="text_bold" style="border-right:1px solid #000;width:170px;">NET PAY</td>
				<td style="border-right:1px solid #000;text-align:right;width:109px;"></td>
				<td style="text-align:right;width:147px;">{{ earning_summary.net_pay }}</td>
				<td>PREVIOUS BALANCE: {{ deductions_summary.previous_balance }}</td>
				<td style="text-align:right;border-left:hidden;">UNPAID CHANGES: {{ deductions_summary.unpaid_changes }}</td>
			</tr>
		</table>

		<table border=1 bordercolor="#000" class="row-seperator column-seperator" style="width:100%;">
			<tr>
				<td style="width:51%;border-top:hidden;">Signature of the Manager:</td>
				<td style="border-top:hidden;">Signature of the Employee:</td>
			</tr>
		</table>
	</div>
</div>
</body>
