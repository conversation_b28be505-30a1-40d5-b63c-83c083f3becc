.login-header {
	font-size: 40px;
	font-weight: 100;
	letter-spacing: 1px;
	margin-top: 30px;
	margin-bottom: 35px;
}
.signup-header {
	font-size: 24px;
	font-weight: 100;
	letter-spacing: 1px;
	margin: 0;
	margin-bottom: 40px;
}
.signup-header small {
    font-size: 64%;
}
.password-header {
	font-size: 45px;
	font-weight: 100;
	letter-spacing: 1px;
}
.contact-header {
	font-size:  38px;
	font-weight: 100;
    letter-spacing: 1px;
    margin-top: 35px;
    margin-bottom: 35px;
}
.login-erp-logo-text {
	width: 150px;
}
.login-erp-logo {
	width: 80px;
}
.login-signup-container {
	width: 40%;
	max-width: 585px;
	min-height: 596px;
	border-radius: 0 10px 10px 0;
}

.contact-container {
	width: 100%;
	border-radius: 10px;
}

.testimonial-container {
	width: 60%;
	max-width: 702px;
	border-radius: 10px 0 0 10px;
	background: linear-gradient(to right, rgba(150, 138, 251, 0.6), rgba(126, 173, 248, 0.6));
	transition: all 0.2s
}
.login-testimonial-container {
	background-color: transparent;
	box-shadow: 0 10px 16px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19) !important;
	padding: 0;
	border-radius: 10px;
}
.login-testimonial-container .login-signup-container,
.login-testimonial-container .contact-container {
	background-color: rgba(255, 255, 255, 0.9);
}
.xserp-login .btn.btn-lg,
.xserp-contact .btn.btn-lg {
	border-radius: 70px;
	box-shadow: 0px 2px 3px #9c9c9c;
	outline: transparent;
	background: #889ffa;
	border-color: #889ffa;
	transition:  all 0.3s;
}

.xserp-login .btn.btn-lg.btn-signup {
	color: #889ffa;
	border-color: #889ffa;
	background: #FFF;
	box-shadow: 0 0;
}

.xserp-login .btn.btn-lg.btn-signup:hover {
	color: #6f8bf7;
	background: #FFF;
	box-shadow: 0px 2px 3px #9c9c9c;
}

.xserp-login .btn.btn-lg:hover,
.xserp-contact .btn.btn-lg:hover {
	background: #6f8bf7;
	border-color: #6f8bf7;
}

.xserp-login .btn.btn-lg:active,
.xserp-contact .btn.btn-lg:active,
.xserp-login .btn.btn-lg.btn-signup:active {
	box-shadow:  0 0;
}

.xserp-container {
	margin-top: 97px;
	margin-bottom: 97px;
}
.xserp-panel-right {
	/*background-color: #ffffff;
			-webkit-box-shadow: 0px 0px 4px 2px #ccc;
			box-shadow: 0px 0px 4px 2px #ccc;*/
	font-family: roboto;
	border-radius: 14px;
}
.xserp-login {
	height: 520px;
	position: relative;
	top: 0;
	left: 0;
	overflow: hidden;
}
.xserp-panel {
	padding: 20px 30px;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	transition: all .5s ease;
	opacity: 0;
	transform: translateX(-100%);
	-webkit-transform: translateX(-100%);
}
.xserp-panel .xserp-heading {
	padding-bottom: 30px;
}
.xserp-login .auth-title {
	font-weight: 700;
}
.xserp-login .xserp-panel.active {
	opacity: 1;
	transform: translateX(0);
	-webkit-transform: translateX(0);
}
.panel-login .remember-row {
	margin-bottom: 10px;
}
.panel-login .remember-row label {
	font-weight: normal;
	position: relative;
	cursor: pointer;
	color: #666;
	margin-top: 0;
	font-size: 14px;
	text-transform: capitalize;
}
.xserp-login .forgotPwd {
	text-align: right;
	margin-top: 10px;
}
.panel-login .form-control,
.panel-signup .form-control,
.panel-forgot .form-control,
.xserp-contact .form-control {
	color: #333333;
	font-size: 16px;
	height: 42px;
	padding: 12px 4px;
	outline: none;
	-webkit-box-shadow: none;
	box-shadow: none;
}
.wrap-input {
	width: 100%;
	position: relative;
	text-align: left;
	margin-bottom: 26px;
}
.xserp-panel.panel-forgot {
	padding-top: 40px;
}
.pwdMask {
	position: relative;
}
.pwdMask .pwd-toggle {
	color: #cccccc;
	position: absolute;
	z-index: 2;
}
.panel-login .pwdMask .pwd-toggle,
.panel-signup .pwdMask .pwd-toggle {
	top: 18px;
	right: 15px;
}
.pwdMask .pwd-toggle:hover,
.pwdMask .pwd-toggle:focus {
	cursor: pointer;
}
.pwdMask .pwd-toggle:hover,
.pwdMask .pwd-toggle:focus,
.pwdMask .pwd-toggle.fa-eye {
	color: #4f77ff;
}
.panel-signup .term-policy a {
	text-decoration: underline;
}
/*--------------------*/

/* 04. Check box UI CSS */

/*--------------------*/

.checkbox input[type="checkbox"] {
	position: absolute;
	right: 9000px;
}
.checkbox input[type="checkbox"] + .label-text:before,
.checkbox input[type="checkbox"] + .label-text:after {
	font-size: 11px;
	display: inline-block;
	width: 17px;
	height: 17px;
	padding: 1px 2px 2px 3px;
	margin-left: 0;
	position: absolute;
	top: -1px;
	left: 0;
}
.checkbox input[type="checkbox"] + .label-text:before {
	content: "";
	background-color: #ffffff;
	border: 1px solid #ced4da;
	border-radius: 3px;
	cursor: pointer;
	-webkit-transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
	transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
}
.checkbox input[type="checkbox"] + .label-text:after {
	background-color: #4f77ff;
	color: #ffffff;
	border-radius: 2px;
	border-color: #4f77ff;
	line-height: 1.4;
}
.checkbox input[type="checkbox"] {
	opacity: 0;
	z-index: 1;
	cursor: pointer;
}
.checkbox input[type="checkbox"]:focus + .label-text:before {
	outline: thin dotted;
	outline: 5px auto -webkit-focus-ring-color;
	outline-offset: -2px;
}
.checkbox input[type="checkbox"]:checked + .label-text:after {
	font-family: "FontAwesome";
	content: "\f00c";
}
.btn.btn-lg {
	padding: 8px 40px;
	font-size: 18px;
	line-height: 30px;
}
.xserp-login input[type='text'],
.xserp-login input[type='password'],
.xserp-contact input[type='text'],
.xserp-contact select,
.xserp-contact textarea {
	border-radius: 0;
	letter-spacing: 1px;
	font-size: 14px;
	padding-left: 5px;
	border-bottom: 2px solid #d9d9d9;
}
.xserp-login input.error-border,
.xserp-contact input.error-border,
.xserp-contact textarea.error-border {
	border: none !important;
	border-bottom: 2px solid #dd4b39 !important;
}
.xserp-login input.error-border:focus:not([readonly]),
.xserp-contact input.error-border:focus:not([readonly]),
.xserp-contact textarea.error-border:focus:not([readonly]) {
	border: none !important;
	border-bottom: 2px solid #dd4b39 !important;
}
.xserp-login .custom-error-message {
	top: 11px;
    right: 0;
    background: #fdeded;
    padding: 0 5px;
}
/*--------------------*/

/* 05. Responsive CSS */

/*--------------------*/

@media (min-width: 768px) {
	.xserp-panel {
		padding: 15px 20px;
	}
}
@media (max-width: 767px) {
	.xserp-panel-right {
		box-shadow: none;
	}
	.xserp-panel-right {
		border-top-left-radius: 0px;
		border-top-right-radius: 0px;
		border-bottom-left-radius: 4px;
		border-bottom-right-radius: 4px;
	}
	.term-policy {
		font-size: 75%;
	}
}
@media (min-width: 320px) and (max-width: 375px) {
	.xserp-login .xserp-panel {
		padding: 20px;
	}
	.panel-login .remember-row {
		margin-bottom: 10px;
		font-size: 14px;
	}
}
@media (max-width: 320px) {
	.xserp-login {
		height: 480px;
	}
	.xserp-heading p {
		font-size: 14px;
	}
	.remember-row .col-sm-6 {
		width: 100%;
	}
	.xserp-login .forgotPwd {
		text-align: left;
		margin-top: 0px;
	}
}

.menu-item a[title='Sign In'], .menu-item a[title='Sign Up'] {
    background-color: #fff;
    border: 1px solid #fff;
    margin: 12px 0px;
    height: 38px;
    line-height: 38px !important;
    line-height: 36px !important;
    color: #4a90e2 !important;
}

.menu-item a[title='Sign In'] {
    border-radius: 20px 0 0 20px;
    border-right: 1px solid #ccc;
    padding: 0 15px 0 20px !important;
}

.menu-item a[title='Sign Up'] {
    border-radius: 0 20px 20px 0;
    padding: 0 20px 0 15px !important;
}


.testimonial_slider_2 {
	width: 100%;
	background: rgba(0, 0, 0, 0.00);
	border-radius: 10px;
	overflow: hidden;
	box-sizing: border-box;
	padding-bottom: 45px;
	margin-top: 45px;
}
.testimonial_slider_2 input {
	display: none;
}
.testimonial_slider_2 #slide_2_1:checked ~ .testimonial_inner {
	margin-left: 0;
}
.testimonial_slider_2 #slide_2_2:checked ~ .testimonial_inner {
	margin-left: -100%;
}
.testimonial_slider_2 #slide_2_3:checked ~ .testimonial_inner {
	margin-left: -200%;
}
.testimonial_slider_2 #slide_2_4:checked ~ .testimonial_inner {
	margin-left: -300%;
}
.testimonial_slider_2 #slide_2_5:checked ~ .testimonial_inner {
	margin-left: -400%;
}
.testimonial_slider_2 .testimonial_inner {
	width: 500%;
	-webkit-transform: translateZ(0);
	-webkit-transition: all 800ms cubic-bezier(0.770, 0.000, 0.175, 1.000);
	-moz-transition: all 800ms cubic-bezier(0.770, 0.000, 0.175, 1.000);
	-ms-transition: all 800ms cubic-bezier(0.770, 0.000, 0.175, 1.000);
	-o-transition: all 800ms cubic-bezier(0.770, 0.000, 0.175, 1.000);
	transition: all 800ms cubic-bezier(0.770, 0.000, 0.175, 1.000);
	/* easeInOutQuart */
	-webkit-transition-timing-function: cubic-bezier(0.770, 0.000, 0.175, 1.000);
	-moz-transition-timing-function: cubic-bezier(0.770, 0.000, 0.175, 1.000);
	-ms-transition-timing-function: cubic-bezier(0.770, 0.000, 0.175, 1.000);
	-o-transition-timing-function: cubic-bezier(0.770, 0.000, 0.175, 1.000);
	transition-timing-function: cubic-bezier(0.770, 0.000, 0.175, 1.000);
	/* easeInOutQuart */
}
.testimonial_slider_2 .slide_content {
	width: 20%;
	float: left;
}
.testimonial_slider_2 #controls {
	text-align: center;
	margin-top: 15px;
}
.testimonial_slider_2 #controls label {
	width: 8px;
	height: 8px;
	margin: 0 2px;
	display: inline-block;
	cursor: pointer;
	background: transparent;
	/* Change controls background color */
	border: solid 1px #fff;
	border-radius: 4px;
	-moz-border-radius: 4px;
	-webkit-border-radius: 4px;
}
.testimonial_slider_2 #slide_2_1:checked ~ #controls label:nth-child(1),
.testimonial_slider_2 #slide_2_2:checked ~ #controls label:nth-child(2),
.testimonial_slider_2 #slide_2_3:checked ~ #controls label:nth-child(3),
.testimonial_slider_2 #slide_2_4:checked ~ #controls label:nth-child(4),
.testimonial_slider_2 #slide_2_5:checked ~ #controls label:nth-child(5) {
	background: #fff;
	/* Change controls background color when mouse click */
}
.testimonial_2 {
	font-size: 16px !important;
	color: #fff;
	/* Change testimonial paragraph text color */
	padding: 20px 30px 20px;
	border-bottom: 2px solid #fff;
}
.content_2 {
	position: relative;
	margin-bottom: 30px;
	font-size: 18px;
	font-family: 'PT Sans';
}
.testimonial_2 p {
	display: inline;
}
.content_2 p:before {
	content: '\201C';
	font-size: 6rem;
	line-height: 1px;
	position: relative;
	bottom: -26px;
	padding-right: 5px;
}
.content_2 p:after {
	content: '\201D';
	font-size: 6rem;
	line-height: 1px;
	position: relative;
	bottom: -26px;
	padding-left: 5px;
}
.testimonials-name {
	font-size: 20px;
	font-family: 'PT Sans';
	color: #FFF;
	font-style: italic;
	font-weight: 700;
	letter-spacing: 1px;
	margin-bottom: 8px;
	display: inline-block;
}
.testimonials-signature {
	margin-top: -40px;
}
.testimonial-profile {
	width: 75px;
	border-radius: 100px;
	border: solid 2px #FFF;
}
.testimonial-logo {
	width: 100px;
	display: block;
	margin: 0 auto;
	background: #FFF;
	padding: 10px;
}
.checkbox .custom-error-message {
	position: relative;
	top: 4px;
	display: block;
}

.floating-label.prefix-input label,
.floating-label.prefix-input input {
	padding-left: 45px;
	margin-top: 2px;
}


.prefix-input .floating-input:focus ~ label,
.prefix-input .floating-input:not(:placeholder-shown) ~ label{
	padding-left: 0 !important;
}

@media only screen and (min-height: 100px) {
	.login-page-container {
		margin-top: 75px;
	}

	.signup-header {
		margin-bottom: -12px;
	}
}
@media only screen and (min-height: 730px) {
	.login-page-container {
		margin-top: 90px;
	}
	.signup-header {
		margin-bottom: 20px;
	}
}
@media only screen and (min-height: 760px) {
	.login-page-container {
		margin-top: 120px;
	}
	.signup-header {
		margin-bottom: 20px;
	}
}
@media only screen and (min-height: 800px) {
	.login-page-container {
		margin-top: 150px;
	}
	.signup-header {
		margin-bottom: 20px;
	}
}

@media only screen and (max-height: 730px) {
	.panel-login .form-control, 
	.panel-signup .form-control, 
	.panel-forgot .form-control, 
	.xserp-contact .form-control {
		/* height: 30px; */
	}

	.xserp-contact select.form-control {
		padding: 0;
	}

	.floating-label label {
		top:  3px;
		font-size: 12px;
	}

	.xserp-login .custom-error-message {
		top:  7px;
	}

	.panel-login .pwdMask .pwd-toggle, 
	.panel-signup .pwdMask .pwd-toggle {
		top: 10px;
	}

	.testimonial-container,
	.xserp-login,
	.login-signup-container {
		min-height:  545px !important;
		height:  545px !important;
	}

	.testimonial_slider_2 #controls {
		margin-top: 0;
	}

	.terms-checkbox {
		margin-top: -10px !important;
	}
}
@media only screen and (max-width: 1200px) {
	.login-header {
		font-size: 30px;
	}
	.login-erp-logo-text {
		width: 135px;
	}
	.login-erp-logo {
		width: 60px;
	}
	.signup-header {
		font-size: 22px;
	}
}
@media only screen and (max-width: 1000px) {
	.login-testimonial-container {
		width: 95%;
	}
	.content_2 {
		font-size: 16px;
	}
	.login-signup-container {
		min-height: 596px;
	}
	.login-header {
		font-size: 25px;
		margin-top: 25px;
	}
	.signup-header {
		font-size: 20px;
	}
	.password-header {
		font-size: 30px;
	}
}
@media only screen and (max-width: 900px) {
	.login-signup-container {
		min-height: 623px;
	}
	.signup-header {
		font-size: 17px;
	}
}
@media only screen and (max-width: 850px) {
	.testimonial-container {
		display: none;
	}
	.login-signup-container {
		width: 100%;
		margin: 0 auto;
		max-width: 100%;
		border-radius: 10px;
	}
	.main__menu__logo img[alt="xserp"] {
		display: inline-block !important;
	}
	.login-header,
	.password-header {
		font-size: 40px;
	}
	.signup-header {
		font-size: 26px;
	}
}
@media only screen and (max-width: 600px) {
	.login-header,
	.password-header {
		font-size: 30px;
	}

	.signup-header {
		font-size: 20px;
	}
}
@media only screen and (max-width: 500px) {
	.login-header,
	.password-header {
		font-size: 25px;
	}

	.signup-header {
		font-size: 18px;
	}
}

@media only screen and (max-width: 420px) {
	.login-header,
	.password-header {
		font-size: 20px;
	}
	.signup-header {
		font-size: 18px;
	}
}

.terms-checkbox .custom-error-message {
    position: absolute;
    font-size: 8px;
    top: 12px;
    left: 34px;
}