$(document).ready(function() {

});

function reviewSE(){
    se_id = $("#modal_se_id").val() ;
    expiry_date = $("#expiry_date").val();
    app_remarks = $("#remarks").val()
    if (app_remarks ==""){
        setTimeout(function(){
                swal({
                    title: "<span style='color: #dd4b39;'>Remarks Required.</span>",
                    text: "Please enter your purpose of reviewing this Sales Estimate as remarks.",
                    type: "warning"
                },
                function(){
                    $('#remarks').focus();
                });
                return;
        },250)
    }
    date = new Date();
    $('#loading').show();
    $.ajax({
        url: "/erp/sales/sales_estimate/review/?now=" + date.toString(),
        type: "POST",
        dataType: "json",
        data: {se_id: se_id, remarks: app_remarks},
        success: function (response) {
            $('#loading').hide();
            if (response.response_message == "Success") {
                swal({
                    title: "Review Successfully",
                    text: "SE Draft: " + se_id + " has been Reviewed.",
                    type: "success"
                },
                function() {
                    if (response.se_no != '0') {
                        window.location.assign('/erp/sales/sales_estimate_list/');
                    } else {
                        generate_pdf_ajax(se_id, 1, expiry_date);
                        updateTableStatus(1, se_id, $("#modal_se_type").val(), 0, expiry_date);
                    }
                });
            } else {
                swal({title: "Review", text:"Review failed!", type:"error"});
            }
            ga('send', 'event', 'Sales Estimate', 'Review', $('#enterprise_label').val(), 1);
        },
        error: function (xhr, errmsg, err) {
            console.log(xhr.status + ": " + xhr.responseText);
            $('#loading').hide();
        }
    });
    return false;
}

function approveSE(){
    se_id = $("#se_id").val() ;
    expiry_date = $("#expiry_date").val();
    approved_remarks= $("#remarks").val();
    date = new Date();
    $("#loading").show();
    $.ajax({
        url: "/erp/sales/sales_estimate/approve/",
        type: "POST",
        dataType: "json",
        data: {se_id: se_id, approved_remarks:approved_remarks},
        success: function (json) {
            $("#loading").hide();
            $("#remarks, #reject_se", "#client_approve_se", "#client_reject_se", "regenerate").removeClass("hide");
            var swalType = "warning";
            if(json.response_message == "Success") {
                swal({
                    title: "<span style='color: #44ad6b;'>Approved Successfully</span>",
                    text: 'Sales Estimate: <b>' + json.code + '</b> has been Approved Successfully',
                    type: 'success',
                    allowEscapeKey: false
                },
                function(){
                    $('#se_id').val(se_id);
                    generate_pdf_ajax(se_id, 2, expiry_date);
                    updateTableStatus(2, se_id, json.code, expiry_date);
                    //$('#pdfGeneration_' + se_id).click();
                });
                ga('send', 'event', 'Sales Estimate', 'Approve', $('#enterprise_label').val(), 1);
            }
            else {
                swal({
                    title: "<span style='color: #44ad6b;'>Approval Failed</span>",
                    text: 'Sales Estimate Draft: <b>' + se_id + '</b> approval has been failed',
                    type: 'error',
                    allowEscapeKey: false
                });
            }
        },
        error: function (xhr, errmsg, err) {
            $("#loading").hide();
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });
    return false;
}

function rejectSE(){
    if($('#remarks').val() != ""){
        se_id = $("#se_id").val();
        expiry_date = $("#expiry_date").val();
        rejection_remarks = $("#remarks").val()
        date = new Date();
        $("#loading").show();
        $.ajax({
            url: "/erp/sales/sales_estimate/reject/",
            type: "POST",
            dataType: "json",
            data: {se_id:se_id, remarks:rejection_remarks},
            success: function (json) {
                $("#loading").hide();
                if (json == '0'){
                    swal({
                        title: "Successfully Removed",
                        text: "SE Draft No: <b>" + se_id + "</b> has been permanently removed.",
                        type: "success",
                        allowEscapeKey: false
                    },
                    function(){
                        window.location.assign('/erp/sales/sales_estimate/view/');
                    });
                    ga('send', 'event', 'Sales Estimate', 'Discard', $('#enterprise_label').val(), 1);
                }
                else {
                    swal({
                        title: "Successfully Rejected",
                        text: "SE No: <b>" + json + "</b> has been successfully rejected!",
                        type: "success",
                        allowEscapeKey: false
                    },
                    function(){
                        $('#se_id').val(se_id);
                        generate_pdf_ajax(se_id, -1, expiry_date)
                        updateTableStatus(-1, se_id, 0, expiry_date)
                        //$('#pdfGeneration_' + se_id).click();
                    });
                    ga('send', 'event', 'Sales Estimate', 'Reject', $('#enterprise_label').val(), 1);
                }
            },
            error: function (xhr, errmsg, err) {
                $("#loading").hide();
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });
        return false;
    }
    else {
        setTimeout(function(){
                swal({
                    title: "<span style='color: #dd4b39;'>Remarks Required.</span>",
                    text: "Please enter your purpose of rejecting this Sales Estimate as remarks.",
                    type: "warning"
                },
                function(){
                    $('#remarks').focus();
                });
                return;
        },250)
    }
}

function clientStatusSE(status){
    se_id = $("#se_id").val() ;
    remarks= $("#remarks").val();
    if(status == 'reject' && $('#remarks').val() == ""){
        setTimeout(function(){
                swal({
                    title: "<span style='color: #dd4b39;'>Remarks Required.</span>",
                    text: "Please enter your purpose of rejecting this Sales Estimate as remarks.",
                    type: "warning"
                },
                function(){
                    $('#remarks').focus();
                });
                return;
        },250)
    }
    else{
            $("#loading").show();
            $.ajax({
                url: "/erp/sales/sales_estimate/update_client_status/",
                type: "POST",
                dataType: "json",
                data: {se_id: se_id, status: status, remarks:remarks},
                success: function (json) {
                    $("#loading").hide();
                    var swalType = "warning";
                    if(json.response_message == "Success") {
                        if (status == 'approve'){
                            swal({
                                title: "<span style='color: #44ad6b;'>Approved Successfully</span>",
                                text: 'Sales Estimate: <b>' + json.code + '</b> has been Approved Successfully',
                                type: 'success',
                                allowEscapeKey: false
                            },
                            function(){
                                updateTableStatus(3, se_id, json.code);
                            });
                            ga('send', 'event', 'Sales Estimate', 'Approve', $('#enterprise_label').val(), 1);
                        }
                        else{
                            swal({
                                title: "Successfully Rejected",
                                text: "Sales Estimate: <b>" + json.code + "</b> has been Rejected.",
                                type: "success",
                                allowEscapeKey: false
                            },
                            function(){
                                updateTableStatus(4, se_id, json.code);
                            });
                            ga('send', 'event', 'Sales Estimate', 'Reject', $('#enterprise_label').val(), 1);
                        }
                    }
                    else {
                        if (status == 'approve'){
                            swal({
                                title: "<span style='color: #44ad6b;'>Approval Failed</span>",
                                text: 'Sales Estimate: <b>' + json.code + '</b> client approval has been failed',
                                type: 'error',
                                allowEscapeKey: false
                            });
                        }
                        else{
                             swal({
                                title: "<span style='color: #44ad6b;'>Rejection Failed</span>",
                                text: 'Sales Estimate: <b>' + json.code + '</b> client rejection has been failed',
                                type: 'error',
                                allowEscapeKey: false
                            });
                        }
                    }
                },
                error: function (xhr, errmsg, err) {
                    $("#loading").hide();
                    console.log(xhr.status + ": " + xhr.responseText);
                }
            });
    }
    return false;
}

function generate_pdf_ajax(se_id, se_status, expiry_date, document_regenerate=false) {
	    $("#se_doc_btn a.btn, #remarks").addClass("hide");
	    $("#se_id").val(se_id);
	    $("#se_document_modal").modal("show");
	    sessionStorage.se_id = se_id
        sessionStorage.se_status = se_status
        sessionStorage.expiry_date = expiry_date
	    $.ajax({
	        url: "/erp/sales/json/se_doc/",
	        type: "post",
	        datatype: "json",
	        data: {se_id:se_id, response_data_type:"data", document_regenerate:document_regenerate},
	        success: function (response) {
	        	if(response.remarks != undefined) {
	                if(response.remarks.length > 0){
	                    $(".remarks_count_link").text(response.remarks.length+" remarks").removeClass('disabled')
	                }
	                else {
	                    $(".remarks_count_link").text("No remarks").addClass('disabled')
	                }
	                $("#se_document_remarks").val(JSON.stringify(response.remarks));
	            }
	            $("#modal_se_id").val(se_id);
                $("#expiry_date").val(expiry_date);
                var row = '<object id="se_document" class="document_viewer" data="'+response.url+'">'+getPdfErrorMessage()+'</object>';
            	$("#se_document_container").html(row);
	            $("#display_popup").removeClass('hide')
	            if(se_status == -1) {
	                if($("#reject_se").hasClass("for_se_approve")) {
	                    $("#approve_se, #remarks").removeClass("hide");
	                }
	                $("#send_mail_div").addClass("hide");
	            }
	            else if(se_status == 0) {
	                $("#review_se, #remarks").removeClass("hide");
	                $("#reject_se").text("Discard");
	                if($("#reject_se").hasClass("for_se_approve")) {
	                    $("#approve_se, #remarks, #reject_se").removeClass("hide");
	                }
	                else if($("#reject_se").hasClass("for_se_edit")){
	                    $("#remarks, #reject_se").removeClass("hide");
	                }
	                $("#send_mail_div").addClass("hide");
	            }
	            else if(se_status == 0 || se_status == 1) {
	                if ($("#reject_se").hasClass("for_se_edit") || $("#reject_se").hasClass("for_se_approve")) {
                            $("#reject_se").text("Discard").removeClass("hide");
                            $("#remarks").removeClass("hide");
                            $("#send_mail_div").addClass("hide");
	                }
	            }
	            else if(se_status == 2 ) {
	                if($("#reject_se").hasClass("for_se_approve")) {
	                    $("#reject_se").text("Reject").removeClass("hide");
	                    $("#remarks").removeClass("hide");
	                    $("#send_mail_div").removeClass("hide");
	                }
	                if($("#client_reject_se").hasClass("for_se_approve")) {
	                    $("#client_reject_se").text("Client Reject").removeClass("hide");
	                    $("#client_approve_se").removeClass("hide");
	                    $("#regenerate").removeClass("hide");


	                }
	            }
	            if($("#remarks").hasClass("hide") || $("#remarks").length <= 0) {
	                $(".remarks_count_link").css({float: "left"});
	            }
	            else {
	                $(".remarks_count_link").css({float: "right"});
	            }
	            $("#remarks").val("");
	        },
	       error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
	    });
	    $("#se_document_modal footer").remove();
	    $('#se_document_modal').on('hidden.bs.modal', function () {
	        $(".remarks_count_link").css({float: "right"});
	        $("#se_document").remove();
	        $("#se_document_container").html(getPdfLoadingImage());
	    });
	}

function updateTableStatus(status, se_id, se_no, expiry_date){
    var editedRow = $("#se_list").find("tr[data-seid='"+se_id+"']");
    if(status == 1) {
        editedRow.find("td.td_status").find("a.pdf_genereate").removeClass('draft').addClass('reviewed').text("Reviewed");
    }
    else if(status == 2) {
        editedRow.find("td.td_status").find("a.pdf_genereate").removeClass('draft cancelled rejected').addClass('approved').text("Approved");
        editedRow.find(".edit_link_code").text(se_no);
        $(".header_current_page, title").text(se_no);
        if($("#client_reject_se").hasClass("for_se_approve")) {
            $("#client_reject_se").text("Client Reject").removeClass("hide");
            $("#client_approve_se").removeClass("hide");
            $("#regenerate").removeClass("hide");
        }
        editedRow.find(".td-se-date").text(moment().format("MMM DD, YYYY HH:mm:ss"))
        if(editedRow.find(".td-se-date").text() == "-") {
            editedRow.find(".td-se-date").text(moment().format("MMM D, YYYY"));
        }
    }
    else if(status == 3) {
        editedRow.find("td.td_status").find("a.pdf_genereate").removeClass('approved').addClass('approved').text("Client Approved");
    }
    else if(status == 4) {
        editedRow.find("td.td_status").find("a.pdf_genereate").removeClass('approved').addClass('rejected').text("Client Rejected");
    }
    else if(status == -1) {
        editedRow.find("td.td_status").find("a.pdf_genereate").removeClass('approved').addClass('rejected').text("Cancelled");
    }
    editedRow.find(".pdf_genereate").attr("onclick", "generate_pdf_ajax("+se_id+", "+status+", '"+expiry_date+"')");
    editedRow.find(".pdf_genereate_icon").attr("onclick", "generate_pdf_ajax("+se_id+", "+status+", '"+expiry_date+"')");
    $("#generate_pdf").attr("data-status", status);
}

function validateExpiryDate(action){
    var expiryDate = $('#expiry_date').val() + " 23:59:59"
    var formated_expiryDate = new Date(expiryDate)
    var currentDate = new Date();
    console.log('action', action);
    if(action == 'regenerate'){
        generate_pdf_ajax(sessionStorage.se_id, sessionStorage.se_status, sessionStorage.expiry_date, true);
    }
    if(action !='regenerate' && (currentDate > formated_expiryDate)){
        swal({
            title: "Are you sure?",
            text: "Sales Estimate seems to have expired on <b>"+ $('#expiry_date').val() +"</b>. Do you still want to proceed with this action?",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#209be1",
            confirmButtonText: "Yes, do it!",
            closeOnConfirm: true
        },
        function(isConfirm){
            if(isConfirm){
                if(action == 'update'){
                    SaveSEClick();
                }else if(action == 'amend'){
                    saveSalesEstimate('amend');
                }else if(action == 'submit_for_approval'){
                    submitForApprovalClick();
                }else if(action == 'approve'){
                    approveSE();
                }else if(action == 'reject'){
                    rejectSE();
                }else if(action == 'review'){
                    reviewSE();
                }else if(action == 'client_approve'){
                    clientStatusSE('approve');
                }else if(action == 'client_reject'){
                    clientStatusSE('reject');
                }else if(action == 'promote_to_oa'){
                    promoteToOA();
                }else if(action == 'promote_to_invoice'){
                    promoteToInvoice();
                }
            }
        });
    }else{
        if(action == 'update'){
            SaveSEClick();
        }else if(action == 'amend'){
            saveSalesEstimate('amend');
        }else if(action == 'submit_for_approval'){
            submitForApprovalClick();
        }else if(action == 'approve'){
            approveSE();
        }else if(action == 'reject'){
            rejectSE();
        }else if(action == 'review'){
            reviewSE();
        }else if(action == 'client_approve'){
            clientStatusSE('approve');
        }else if(action == 'client_reject'){
            clientStatusSE('reject');
        }else if(action == 'promote_to_oa'){
            promoteToOA();
        }else if(action == 'promote_to_invoice'){
            promoteToInvoice();
        }
    }

}