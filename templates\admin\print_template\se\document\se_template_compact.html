<style>
    .pdf_others_details {
        width: 85px;
        display: inline-table;
        word-break: break-word;
    }
    invoice_other_details_list {
        width: 40%;
        font-weight: bold;
        font-size: 12px;
        display: inline-table;
        word-break: break-word;
    }
    .invoice_other_details_data {
         width: calc(60% - 1em);
         font-size: 12px;
         display: inline-table;
         vertical-align:top;
         word-break:break-all;
         width: 100px;height: 34px;
    }
	.se_template_view{
		color: #000;
		font-family: pdf_{{general_res.font_family}};
	}

	.se_template_view table {
		width:100%;
	}
	.se_template_view table, th, td {
		border-collapse: collapse;
	}

	.se_template_view .table.row-seperator th,
	.se_template_view .table.row-seperator td,
	.se_template_view .table.row-seperator th,
	.se_template_view .table.row-seperator td {
	    border-top: 1px solid #666666 !important;
	    border-bottom: 1px solid #666666 !important;
	}

	.se_template_view .table.column-seperator th,
	.se_template_view .table.column-seperator td,
	.se_template_view .table.column-seperator th,
	.se_template_view .table.column-seperator td {
	    border-right: 1px solid #666666 !important;
	    border-left: 1px solid #666666 !important;
	}

	.se_template_view th{
		text-align: center;
		line-height: 25px;
	}
	.se_template_view td{
		line-height: 25px;
	}
	
	.se_template_view .enterprise_details{
		font-size:12pt;
	}
	.se_template_view .page_title{
		font-size: 16pt;
		margin-left: 5px;
	}
	.se_template_view .se_details{
		font-weight: bold;
		font-size:12pt;
	}
	.se_template_view .total_price{
		font-size:11pt;
		font-weight: bold;
		margin-top: 4px;
		margin-bottom: 3px;
		text-align:right;
		margin-right:-295px;
	}
	.se_template_view .total_gst{
		text-align: right;
		border-bottom:hidden;
		border-left:hidden;
		border-right:hidden;
		line-height:25px;
	}
	.se_template_view .amt{
		font-size: 11pt;
		font-weight: bold;
		margin-top: 4px;
		float:right;
		margin-right:-14px;
	}
	.se_template_view .sub_total th ,
	.se_template_view .sub_total td{
		text-align:right;
		font-weight: normal;
		padding-right:3px;
		font-size:11pt;
	}
	.se_template_view .total th ,
	.se_template_view .total td{
		text-align:right;
	}
	.se_template_view .notes{
		font-size:11pt;
	}
	.se_template_view .notes_bottom{
		margin-bottom:10px;
	    margin-top:5px;
	}
	.se_template_view .reg_details{
			font-size:11pt;
	}
	.se_template_view .grand_total {
		border-left:hidden;
		border-right:hidden;
		border-bottom:hidden;
		font-size:11pt;
	}

	.se_template_view .sales_estimate{
		margin-left: -4px;
		font-size:10pt;
	}
	.se_template_view .se_list {
	    width: 86px;
	    font-weight: bold;
	    font-size: 12px;
	    display: inline-table;
	    word-break: break-all;
	}

	.se_template_view .se_data {
	    width: 240px;
	    font-size: 12px;
	    display: inline-table;
	    vertical-align:top;
	     word-break: break-all;
	}

	.se_template_view .payment_term {
		width:150px;
		display:inline-block;
	}

	.se_template_view .payment_details{
		width: 700px;
		display: inline-block;
		word-break:break-all;
		vertical-align:top;
	}

	.se_template_view .annexture_se, {
		font-size:11pt;
		text-align:center;
	 	margin-bottom:30px;
	 	margin-top:30px;
	}
	.pdf_enterprise_details{
	    width:140px !important;
	}

	.se_template_view .registration_label{
		width: 50px;
		display: inline-block;
		word-break: break-word;
		vertical-align: top
	}
	.se_template_view  .registration_data{
		width: 270px;
		display: inline-table;
		word-break: break-word;
		vertical-align: top;
	}

	.bold {
        font-weight: bold;
    }

	@font-face {
        font-family: 'Times New Roman';
        font-style: normal;
        font-weight: 400;
        src: url(https://www.xserp.in/site_media/fonts/times.ttf) format('truetype');
	}
	@font-face {
	        font-family: 'pdf_Roboto';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/roboto.regular.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Times New Roman';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/times.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Helvetica';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/roboto.regular.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Ubuntu';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/Ubuntu-Regular.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Comic Sans';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/comicsansms3.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_DejaVu Sans';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/DejaVuSans.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Courier New';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/cour.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Verdana';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/verdana.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Open Sans';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/open-sans-Regular.ttf) format('truetype');
	}


</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/bootstrap.css?v={[ current_version ]}">
<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={[ current_version ]}">
<div class="se_template_view" style="border-color: transparent; width: 100%; font-family: 'pdf_{[general_res.font_family]}'">
	<div class="row">
        <div class="header-banner-container col-sm-12">
            <div class="col-sm-4 text-left  pull-left misc_header-left_banner" style="padding-left: 0;">
                <img class="image_uploader-left img-responsive"  src="{[header_left_image]}">
            </div>
            <div class="col-sm-4 text-center pull-left misc_header-center_banner">
                <img class="image_uploader-center img-responsive" style="margin:0 auto;" src="{[header_center_image]}">
            </div>
            <div class="col-sm-4 text-right pull-right misc_header-right_banner" style="padding-right: 0;">
                <img class="image_uploader-right img-responsive" style="float: right;" src="{[header_right_image]}">
            </div>
        </div>
        <div style="clear: both;"></div>
        <div class="col-sm-12">
            <div class="col-sm-5 pdf_company_address_container" style="padding-left:1px !important;">
                 <img src="{[enterprise_logo]}" class="pdf_company_logo" style="height:{[header_res.company.logo.size]}px;"><br>
                    [% if header_res.company.company_name.print %]
                        [% if header_res.company.company_name.font_size != '0' %]
                            <span class="pdf_company_name" style="font-size:12px;font-family: 'pdf_{[header_res.company.company_name.font_family]}'">{[source.enterprise.name]}</span><br>
                        [% endif %]
                    [% endif %]
            </div>
            <div class="col-sm-7 text-right" style="display: inline-block;">
                <div class="col-sm-12" style="margin-top: 0px;">
                    <span class="pdf_form_name text-right pdf_form_name_text_size" style="font-size: 14px;word-break: break-all;"><b class="pdf_form_name_text">{[ header_res.form_name.label.se ]}</b></span>
                    [% if header_res.font_size != '0' %]
                        <span class="pdf_sales_estimate text-left pdf_company_details_container" style="font-size: 12px; padding: 0;word-break: break-all;"><b class="pdf_sales_estimate_prefix">#</b> <span class="pdf_sales_estimate preview_sales_estimate_number">{[ source.getCode ]} </span></span><br>
                    [% endif %]
                </div>
                <div class="col-sm-12 text-right" style="margin-top: 3px;padding-left:54px">
                     [% if header_res.font_size != '0' %]
                        <span class="pdf_indent_date" style="font-size: 10px;">
                            <span class="pdf_indent_date_txt pdf_company_details_container" style="width:40% !important">{[ header_res.field_name.se_date.label ]}</span> <b>
                            <span class="pdf_company_details_container" style="word-break: break-all">{[ se_date ]}</span></b>
                        </span>
                    [% endif %]
                </div>
            </div>
        </div>
        <div class="clearfix"></div>
		<div class="notes_bottom">
            <hr style="margin:0;border: 1pt solid #000; " />
        </div>
        <div class="col-sm-6 pdf_company_address_container">
            [% if header_res.font_size != '0' %]
                [% if header_res.company_info.print_address %]
                    <span class="pdf_company_address" style="word-break: break-word;">
                        {[ enterprise_address ]}
                    </span>
                [% endif %]
                [% if header_res.company_info.print_phone_no %]
                    <span class="pdf_company_contact pdf_company_address_container" style="word-break:break-word;">
                        <br> <b>Ph:</b> [% if source.enterprise.primary_contact_details.contact.phone_no %] {[ source.enterprise.primary_contact_details.contact.phone_no ]} [% endif %]
                    </span>
                [% endif %]
                [% if header_res.company_info.print_email %]
                    <span class="pdf_company_email pdf_company_address_container" style="word-break:break-word;">
                        <b>E-mail:</b>[% if source.enterprise.primary_contact_details.contact.email %] {[ source.enterprise.primary_contact_details.contact.email ]} [% endif %]
                    </span>
                [% endif %]
                [% if header_res.company_info.print_fax %]
                    <span class="pdf_company_fax pdf_company_address_container" style="word-break:break-word;font-size:{[header_res.font_size]}px;">
                        <b>Fax:</b>[% if source.enterprise.primary_contact_details.contact.fax_no %] {[ source.enterprise.primary_contact_details.contact.fax_no ]} [% endif %]
                    </span>
                [% endif %]
            [% endif %]
        </div>
        <div class="col-sm-6 pdf_company_address_container" style="padding: 3px 15px; font-size: 12px;word-break: break-all;">
			{% for reg_detail in enterprise_reg %}
                [% if header_res.font_size != '0' %]
			        <span class="pdf_registration_{{ reg_detail.label_id }}"><b class="pdf_enterprise_details registration_label">{{ reg_detail.label }}</b><span class="registration_data">:  {{ reg_detail.details }}</span><br /></span>
                [% endif %]
			{% endfor %}
		</div>
        <div class="clearfix"></div>
		<div class="col-sm-12 notes_bottom">
            <hr style="margin:0;border: 1pt solid #000; " />
        </div>
		<div class="col-sm-6  pdf_bill_to_address pdf_company_details_container">
            <span style="font-size:14pt;word-break: break-word;">
                [% if header_res.font_size != '0' %]
                    <b> {[header_res.billing_address.label]}:</b>
                    <span class="notes_bottom">
                        <hr style="margin:0;border: 1px solid #000; "/>
                    </span>
                [% endif %]
            </span>
            <div style="word-break: break-word">
                 [% if header_res.font_size != '0' %]
                    <b>
                    [% if  source.customer.name %]
                        {[ source.customer.name ]}<br>
                    [% endif %]
                    </b>
                    [% if  source.customer.address_1 %]
                        {[ source.customer.address_1 ]},
                    [% endif %]
                    [% if  source.customer.address_2 %]
                        {[ source.customer.address_2 ]}<br>
                    [% endif %]
                    [% if source.customer.city %]
                        {[ source.customer.city ]},
                    [% endif %]
                    [% if source.customer.state %]
                        {[ source.customer.state ]},
                    [% endif %]
                    [% if source.customer.pin_code %]
                        {[ source.customer.pin_code ]},
                    [% endif %]
                    [% for country in country_list %]
                        [% if country.country_code == source.customer.country_code %]
                            {[ country.country_name|upper ]}
                        [% endif %]
                    [% endfor %]
                    [% if source.customer.primary_contact_details.contact.email %]
                       <br> <b>E-mail:</b>{[ source.customer.primary_contact_details.contact.email ]}
                    [% endif %]
                    [% if source.customer.primary_contact_details.contact.phone_no %]<b>Ph:</b> {[ source.customer.primary_contact_details.contact.phone_no ]} <br> [% endif %]
                    [% for reg_detail in source.customer.registration_details %]
                        [% if reg_detail.label == "GSTIN" and reg_detail.details != "" %]
                            <b>GSTIN:</b> {[ reg_detail.details ]}
                        [% endif %]
                    [% endfor %]
                    [% for reg_detail in source.customer.registration_details %]
                        [% if reg_detail.label != "GSTIN" and reg_detail.details != "" %]
                            <b>{[ reg_detail.label ]}:</b> {[ reg_detail.details ]}
                        [% endif %]
                    [% endfor %]
                [% endif %]
            </div>
        </div>
		<div class="col-sm-6 pdf_company_details_container">
            [% if header_res.field_name.payment_terms.print %]
                [% if header_res.font_size != '0' %]
                    <div class="pdf_payment_term">
                        <span class="pdf_payment_term_txt" style="font-weight: bold;word-break:break-all;display: inline-block;vertical-align: top;"><b>{[ header_res.field_name.payment_terms.label ]}</b></span>
                        <span style="word-break:break-all;display: inline-block;">: {[ payment_terms ]}</span>
                    </div>
                [% endif %]
            [% endif %]
            [% if header_res.field_name.expiry_date.print %]
                [% if header_res.font_size != '0' %]
                    <div class="pdf_expiry_date">
                        <span class="pdf_expiry_date_txt pdf_company_details_container" style="font-size:12px;font-weight: bold;word-break:break-all;display: inline-block;vertical-align: top;"><b>{[header_res.field_name.expiry_date.label]}</b></span>
                        <span class="pdf_company_details_container" style="font-size:12px;display: inline-block;">: {[ se_expiry_date ]}</span>
                    </div>
                [% endif %]
            [% endif %]
		</div>
        <div class="clearfix"></div><br>
        [% if misc_res.print_summary_first_page and appendix_pages > 0 %]
            <atable class="table  item_table table-bordered row-seperator column-seperator" id="item_table" style="font-size:11px;margin-left: 14px;width: 96%;">
                 <athead>
                    <atr class="row_seperator column_seperator header_shading">
                        <ath class="text-center td_sno td_sno_text" rowspan="2" scope="col" style="width: 6%;">S.no</ath>
                        <ath class="text-center td_description td_description_text" rowspan="2" style="width:30%;">Description</ath>
                        <ath class="text-center pdf_item_hsn_code_txt td_hsn_code" rowspan="2">HSN/SAC</ath>
                        <ath class="text-center td_qty td_qty_text" rowspan="2" scope="col">Qty</ath>
                        <ath class="text-center td_uom td_uom_text" rowspan="2" scope="col">UOM</ath>
                        <ath class="text-center td_price td_price_text" rowspan="2" scope="col">Price<br>{[ source.currency.code ]}</ath>
                        <ath class="text-center td_disc td_disc_text" rowspan="2" scope="col">Disc<br>%</ath>
                        <ath class="text-center td_tax td_tax_text" rowspan="2" scope="col">Total<br>{[ source.currency.code ]}</ath>
                    </atr>
                </athead>
                <atbody>
                    <atr class="sub_total">
                        <atd colspan="13" class="full-length-td" style="text-align: center; line-height:30px; font-size:11px;"><b>AS PER ANNEXURE TO SE NO:<br>{[ source.getCode ]}</b>
                        </atd>
                    </atr>
                </atbody>
                <atfoot>
                    <atr class="row_seperator column_seperator total_section sub_total_section">
                        <atd colspan="3" class="text-right total_section_1" style="font-size: 12px; line-height: 12px;"><b>Sub-Total</b></atd>
                        <atd class="total_section_2" style="font-size: 12px; line-height: 12px;">{[ total_quantity ]}</atd>
                        <atd colspan="4" class="text-right total_section_3" style="font-size: 12px; line-height: 12px;">{[ total_value | floatformat:2 ]}</atd>
                    </atr>
                    [% for tax in se_taxes %]
                        <atr class="row_seperator column_seperator other_tax_column for-non-pp">
                            <atd colspan="3" class="text-right total_section_1">{[ tax.tax_name ]} @ {[ tax.tax_rate ]}%</atd>
                            <atd colspan="5" class="text-right total_section_3">{[ tax.tax_value ]}</atd>
                        </atr>
                    [% endfor %]
                    [% if source.round_off != 0 %]
                        <atr class="row_seperator column_seperator total_section" id="round-off-container">
                            <atd colspan="3" class="text-right total_section_1" style="font-size: 12px; line-height: 12px;"><b>Round Off</b></atd>
                            <atd colspan="5" class="text-right total_section_3" style="font-size: 12px; line-height: 12px;">{[ source.round_off ]}</atd>
                        </atr>
                    [% endif %]
                    <atr class="row_seperator column_seperator total_section">
                        <atd colspan="3" class="text-right total_section_1"><b>Grand Total</b><span class="total_inclusice_tax hide"> (inclusive tax)</span></atd>
                        <atd colspan="5" class="text-right total_section_3">{[ source.currency.code ]} <b>{[ source.grand_total ]}</b></atd>
                    </atr>
                    <atr class="tr_total_in_words row_seperator show_total_in_words total_in_words">
                        <atd colspan="13" class="full-length-td"><b>Total Value ({[ source.currency.code ]}):</b>  {[ total_in_words|upper ]}</atd>
                    </atr>
                    [% if header_res.field_name.special_instructions.print %]
                        [% if special_instructions %]
                            <atr class="se_special_instruction">
                                <atd colspan="8" class="full-length-td"><b class="se_special_instruction_txt">{[ header_res.field_name.special_instructions.label ]}</b>:  {[ special_instructions ]}</atd>
                            </atr>
                        [% endif %]
                    [% endif %]
                    [% if source.notes != "" %]
                        <atr>
                            <atd class="full-length-td">
                                <strong>Notes:</strong>
                                [% autoescape off %] {[ source.notes ]} [% endautoescape %]
                            </atd>
                        </atr>
                    [% endif %]
                </atfoot>
            </atable>
        [% else %]
            {% include "admin/print_template/se/document/common/se_template_item_table.html" %}
            [% if source.type != 2 %]
                [% if summary_res.hsn_summary.print %]
                    {% include "admin/print_template/se/document/common/se_template_hsn_summary.html" %}
                [% endif %]
            [% endif %]
        [% endif %]
		[% if misc_res.print_summary_first_page and appendix_pages > 0 %]
            <div style="page-break-after: always"></div>
        [% endif %]
        [% if misc_res.print_summary_first_page and appendix_pages > 0 %]
            <div class="annexture_se" style="text-align: center">ANNEXURE TO SALES ESTIMATE {[ source.getCode ]} DATED {[ order_date ]}</div>
            <div class="col-sm-12" id="salesestimate_material_table">
                {% include "admin/print_template/se/document/common/se_template_item_table.html" %}
            </div>
            [% if source.type != 2 %]
                [% if summary_res.hsn_summary.print %]
                    {% include "admin/print_template/se/document/common/se_template_hsn_summary.html" %}
                [% endif %]
            [% endif %]
        [% endif %]
	</div>
</div>