"""
"""

import json

import pymysql
from django.http import HttpResponse

from erp.dao import DataAccessObject, executeQuery
from erp.reports import logger
from settings import HOST, USER, PASSWORD, DBNAME, PORT

__author__ = 'charles'


class CustomReportDAO(DataAccessObject):
	"""
	
	"""

	@staticmethod
	def insertReport(
			report_code=None, report_name=None, code=None, values=None,
			enterprise_id=None,	user_id=None, group_id=None, adv_filter=None):
		"""

		:param report_code:
		:param report_name:
		:param code:
		:param values:
		:param enterprise_id:
		:param user_id:
		:param group_id:
		:param adv_filter:
		:return:
		"""
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
		cur = conn.cursor()
		try:
			cur.execute(
				"""SELECT code from custom_report WHERE code='%s'""" % code)
			result = cur.fetchall()
			if len(result) <= 0:
				cus_report_insert_query = """INSERT INTO custom_report (report_code, code, name, filter_keys, 
				enterprise_id, user_id, group_id, adv_filter) VALUES ('%s', '%s', '%s', '%s', '%s', '%s', '%s',
				NULLIF('%s', ''))""" % (report_code, code, report_name, values, enterprise_id, user_id, group_id, adv_filter)
				cur.execute(cus_report_insert_query)
				conn.commit()
				response = json.dumps("success")
			else:
				response = json.dumps("duplicate")
			conn.close()
		except Exception as e:
			logger.exception('Error : %s' % e)
			conn.rollback()
			conn.close()
			response = json.dumps("failed")
		return HttpResponse(response)

	def deleteReport(self, report_code=None, enterprise_id=None, user_id=None, code=None):
		"""

		:param report_code:
		:param enterprise_id:
		:param user_id:
		:param code:
		:return:
		"""
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
		cur = conn.cursor()
		try:
			delete_template_query = """DELETE FROM custom_report
										WHERE
											code='%s'
											AND enterprise_id='%s'
											AND user_id='%s'
											AND report_code='%s'""" % (
				code, enterprise_id, user_id, report_code)
			cur.execute(delete_template_query)
			conn.commit()
			response = json.dumps("success") if cur.rowcount > 0 else json.dumps("no_permission")
		except Exception as e:
			logger.exception('Error : %s' % e)
			conn.rollback()
			response = json.dumps("failed")
		conn.close()
		return HttpResponse(response)

	def getReportList(self, enterprise_id=None, user_id=None, report=None):
		"""

		:param enterprise_id:
		:param user_id:
		:param report:
		:return:
		"""
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
		cur = conn.cursor()
		response = ""
		try:
			cur.execute(
				"""SELECT code, name FROM custom_report
					WHERE
						IF(group_id = 1, user_id = '%s'
						OR group_id=0,group_id=0)
						AND enterprise_id='%s'
						AND code<>''
						AND report_code = '%s'
					ORDER BY name""" % (
					user_id, enterprise_id, report))
			response = cur.fetchall()
		except Exception as e:
			logger.exception('Error : %s' % e)
			conn.rollback()
		conn.close()
		return response

	def getReport(self, code=None, enterprise_id=None, user_id=None):
		"""

		:param code:
		:param enterprise_id:
		:param user_id:
		:return:
		"""
		res = ""
		try:
			res = {}
			for item in executeQuery("""SELECT * FROM custom_report 
				LEFT JOIN auth_user ON auth_user.id = custom_report.user_id 
				WHERE custom_report.enterprise_id='%s' AND custom_report.code='%s'
				ORDER BY custom_report.name""" % (enterprise_id, code)):
				res['code'] = item[2]
				res['type'] = item[4]
				res['values'] = item[7]
				res['adv_filter'] = item[8]
				res['owner'] = item[15] + ' ' + item[16]
				res['is_myrecord'] = 1 if user_id == item[10] else 0
		except Exception as e:
			logger.exception('Error : %s' % e)
		return res
