function showHideOtherContacts(current) {
    var currentStatus = $(current).text().trim();
    if(currentStatus == "Show More") {
        $(".other_contact_details").slideDown(500);
        var row = `<a>Show Less <i class="fa fa-angle-up" aria-hidden="true" style="font-weight: bold;"></i></a>`;
    }
    else {
        var row = `<a>Show More <i class="fa fa-angle-down" aria-hidden="true" style="font-weight: bold;"></i></a>`;
        $(".other_contact_details").slideUp(500);
    }
    $(current).html(row);
    var otherContactCount = $(".other_contact_details").find(".contact-details:visible").length;
    $(".other-contact-count").text(`(${otherContactCount})`);
}

function addNewContact(current) {
	var sequenceCount = $(".other_contact_details").find(".contact-details").length;
	if(sequenceCount == 0) {
		sequenceCount = 2;
	}
	else {
		sequenceCount = Number($(".other_contact_details").find(".contact-details").last().attr("data-sequence-id")) + 1;
	}
  	var row = `<div class="contact-details" data-sequence-id = "${sequenceCount}">
  					<div class="col-sm-12 line-divider">
  						<i class="fa fa-minus-circle delete-contact-container" role="button" onclick="deleteContactDetails(this)"></i>
					</div>
					<div class="floating-label col-sm-6" style="padding-left: 0;">
						<input class="form-control floating-input contact-person-name" id="id_party-contact_person_${sequenceCount}" maxlength="50" name="party-contact_person" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" placeholder=" " type="text">
						<label>Name</label>
					</div>
					<div class="floating-label col-sm-6" style="padding-left: 0; padding-right: 0;">
						<input class="form-control floating-input contact-person-email" id="id_party-email_${sequenceCount}" maxlength="50" name="party-email" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" placeholder=" " type="text" value="">
						<label>Email</label>
					</div>
					<div class="floating-label col-sm-6" style="padding-left: 0;">
						<input class="form-control floating-input contact-person-number" id="id_party-phone_${sequenceCount}" maxlength="30" name="party-phone" onblur="return validateStringOnBlur(this, event, 'ph_number');" onkeypress="return validateStringOnKeyPress(this,event,'ph_number');" placeholder=" " type="text" value="">
						<label>Contact Number</label>
					</div>
					<div class="floating-label col-sm-6" style="padding-left: 0; padding-right: 0;">
						<input class="form-control floating-input contact-person-fax" id="id_party-fax_${sequenceCount}" maxlength="30" name="party-fax" onblur="return validateStringOnBlur(this, event, 'ph_number');" onkeypress="return validateStringOnKeyPress(this,event, 'ph_number');" placeholder=" " type="text" value="">
						<label>Fax</label>
					</div>
				</div>`;
	$(current).closest(".address-container").find(".other_contact_details").append(row);
    if($(".other_contact_details").find(".contact-details:visible").length >= 1) {
        $(".other_contact_details-text").removeClass("hide");
    }
    var otherContactCount = $(".other_contact_details").find(".contact-details:visible").length;
    $(".other-contact-count").text(`(${otherContactCount})`);
    if(!$(".other_contact_details").is(":visible")) {
        $(".show-hide-contacts").click();
        $(".other_contact_details").show();
    }
}

function deleteContactDetails(current) {
	$(current).closest(".contact-details").addClass("hide deleted-contact-detail");
    if($(".other_contact_details").find(".contact-details:visible").length <= 0) {
        $(".other_contact_details-text").addClass("hide");
        $(".show-hide-contacts").addClass("hide");
    }
    var otherContactCount = $(".other_contact_details").find(".contact-details:visible").length;
    $(".other-contact-count").text(`(${otherContactCount})`);
}

function deleteRegistraionDetails(current) {
	$(current).closest("div").addClass("hide deleted-registration-detail");
}

function deletePaymentDetails(current, type) {
	$(current).closest("div").addClass("hide deleted-payment-detail");
}

function addNewRegistrationDetails() {
	if($(".registration-extra-details").find(".registration-details").length > 0) {
		var labelId = $(".registration-extra-details").find(".registration-details").last().find(".registration-label-id").val();
	}
	else {
		var labelId = 0;
	}
	labelId = Number(labelId) + 1;
	var row = `<div class="col-sm-12 form-group registration-details" style="padding: 0;">
					<input type="text" class="form-control registration-label-id hide" value="${labelId}">
		    		<input type="text" class="form-control registraion-key" maxlength="30" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');"  placeholder="Label"/>
		    		<input type="text" class="form-control registraion-value" maxlength="150" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" placeholder="Detail"/>
		    		<i class="fa fa-minus-circle" role="button" onclick="deleteRegistraionDetails(this)" style="color: #dd4b39;position: absolute;margin-top: 10px;margin-left: -20px;"></i>
		    	</div>`
	$(".registration-extra-details").append(row);
}

function savePartyDetails(type) {
    $(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();

    var ControlCollections = [
        {
            controltype: 'textbox',
            controlid: 'id_party-name',
            isrequired: true,
            errormsg: 'Party Name is Required'
        },
        {
            controltype: 'textbox',
            controlid: 'id_party-state',
            isrequired: true,
            errormsg: 'State is Required'
        }
    ];

    var gstCategory = $("#id_gst_category-__prefix__-make_choices").val();
    if(["3", "4", "5"].indexOf(gstCategory) == -1){
        var control = {
            controltype: 'textbox',
            controlid: 'party_gstin_number',
            isrequired: true,
            errormsg: 'GSTIN is Required.'
        };
        ControlCollections[ControlCollections.length] = control;
    }

    if($(".other_contact_details").find(".contact-details:not(.hide)").length >= 1 || $("#id_party-contact_person").val().trim() !="" || $("#id_party-email").val().trim() !="" || $("#id_party-phone").val().trim() !="" || $("#id_party-fax").val().trim() !="") {
        var control = {
            controltype: 'textbox',
            controlid: 'id_party-contact_person',
            isrequired: true,
            errormsg: 'Name is Required.'
        };
        ControlCollections[ControlCollections.length] = control;
        var control = {
            controltype: 'textbox',
            controlid: 'id_party-email',
            isrequired: false,
            errormsg: 'Email is Required.',
            isemail: true,
            emailerrormsg: 'Invalid Email Address'
        };
        ControlCollections[ControlCollections.length] = control;
    }

    $(".other_contact_details").find(".contact-details:not(.hide)").each(function(){
        var nameValidate = $(this).find(".contact-person-name").attr("id");
        var emailValidate = $(this).find(".contact-person-email").attr("id");

        var control = {
            controltype: 'textbox',
            controlid: nameValidate,
            isrequired: true,
            errormsg: 'Name is Required.'
        };
        ControlCollections[ControlCollections.length] = control;
        var control = {
            controltype: 'textbox',
            controlid: emailValidate,
            isrequired: false,
            errormsg: 'Email is Required.',
            isemail: true,
            emailerrormsg: 'Invalid Email Address.'
        };
        ControlCollections[ControlCollections.length] = control;
    });
    var result = JSCustomValidator.JSvalidate(ControlCollections);
    if(!result && $(".other_contact_details").find(".error-border").length >= 1 && !$(".other_contact_details").is(":visible")) {
        $(".show-hide-contacts").click();
    }

    if(result){
        if(["5", "6", "7", "9"].indexOf(gstCategory) == -1){
			$("#id_party-port").val("");
		}
        $(".btn-party-save").val('Processing...').addClass('btn-processing');
        var config_flags =  parseInt($("#duty_pass_reminder").is(":checked") ? 1 : 0) +
                            parseInt($("#id_is_supplier").is(":checked") ? 2 : 0) + 
                            parseInt($("#id_is_customer").is(":checked") ? 4 : 0);

        partyDetails = {
                            code: $("#id_party-code").val(), 
                            name: $("#id_party-name").val(), 
                            address_1: $("#id_party-address").val(),
                            address_2:"", 
                            city: $("#id_party-city").val(), 
                            state: $("#id_party-state").val(), 
                            config_flags:config_flags, 
                            payment_credit_days: $("#pay_cre_days").val(),
                            receipt_credit_days: $("#rec_cre_days").val(), 
                            currency: $("#id_currency option:selected").val(),
                            pin_code: $("#id_party-pincode").val(),
                            country_code: $("#id_country-__prefix__-make_choices").val(),
                            category_id: $("#id_gst_category-__prefix__-make_choices").val(),
                            port: $("#id_party-port").val()
                        }

        
        partyContactDetailsObj = [{
            'sequence_id': 1,
            'name': $("#id_party-contact_person").val(),
            'email': $("#id_party-email").val(),
            'phone_no': $("#id_party-phone").val(),
            'fax_no': $("#id_party-fax").val(),
            'is_whatsapp': 0,
            'is_deleted': 0
        }];

        $(".other_contact_details").find(".contact-details").each(function(){
            var current = $(this);
            var item = {};
            item ["sequence_id"] = Number(current.attr("data-sequence-id"));
            item ["name"] = current.find(".contact-person-name").val();
            item ["email"] = current.find(".contact-person-email").val();
            item ["phone_no"] = current.find(".contact-person-number").val();
            item ["fax_no"] = current.find(".contact-person-fax").val();
            item ["is_whatsapp"] = 0;
            item ["is_deleted"] = current.hasClass('deleted-contact-detail')?1:0;
            partyContactDetailsObj.push(item);
        });

        var partyRegistrationDetailsObj = [];
        var duplicatedRegistrationDetail = [];
        $(".registration-extra-details").find(".registration-details").each(function(){
            var current = $(this);
            var item = {};
            item ["label_id"] = Number(current.find(".registration-label-id").val());
            item ["details"] = current.find(".registraion-value").val();
            item ["label"] = current.find(".registraion-key").val();
            item ["is_deleted"] = current.hasClass('deleted-registration-detail')?1:0;
            if(item ["details"].trim() == "" && item ["label"].trim() == "") {
                item ["is_deleted"] = 1;
            }
            if(!current.hasClass("deleted-registration-detail")) {
                if(item ["details"].trim() != "" && item ["label"].trim() == "") {
                    swal("","There is an empty 'LABEL' in the Registration Details. Please make sure to add 'LABEL' for each Registraion Details.", "warning")
                    result = false;
                }
            }
            partyRegistrationDetailsObj.push(item);
            if(!current.hasClass("deleted-registration-detail")) {
                duplicatedRegistrationDetail.push(item ["label"].toLowerCase());
            }
        });

        var isDuplicate = findDuplicates(duplicatedRegistrationDetail);
        if(isDuplicate != "") {
            swal("","There is a duplicate 'LABEL' in the Registration Details. <br />Please make sure that Label(s) are correct.", "warning")
            result = false;
        }

        if(type == "save") {
            var url = '/erp/masters/json/web/party/save/'
        }
        else {
            var url = '/erp/masters/json/party/update/';
            partyDetails['party_id'] = $("#id_party-id").val();
        }

        if(result) {
            $.ajax({
                url : url,
                type : "POST",
                dataType: "json",
                data :  {party_details:JSON.stringify(partyDetails), party_contact_details: JSON.stringify(partyContactDetailsObj), party_reg_details: JSON.stringify(partyRegistrationDetailsObj), inline_update: false},
                success : function(json) {
                    if(json['response_message'] == "Session Timeout") {
                        location.reload();
                        return;
                    }
                    $(".btn-party-save").val('Save').removeClass('btn-processing');
                    if(json['message'].indexOf("1062") != -1){
                        swal("","Party Code/ Name already exists","warning");
                    }
                    else {
                        swal({
                            title:"",
                            text:json['message'],
                            type: "success",
                            showCancelButton: false,
                            confirmButtonColor: "#209be1",
                            confirmButtonText: "OK",
                            closeOnConfirm: true
                        },
                        function(){
                            window.location.href = "/erp/masters/party_list/";
                        });
                    }
                    //ga('send', 'event', 'Party', event_action, $('#enterprise_label').val(), 1);
                },
                error : function(xhr,errmsg,err) {
                    console.log(xhr.status + ": " + xhr.responseText);
                    $(".btn-party-save").val('Save').removeClass('btn-processing');
                }
            });
        }
        else {
            $(".btn-party-save").val('Save').removeClass('btn-processing');
        }
    }
}

const findDuplicates = (party_detail) => {
    let sorted_party_detail = party_detail.slice().sort();
    let results = [];
    for (let i = 0; i < sorted_party_detail.length - 1; i++) {
        if (sorted_party_detail[i + 1] == sorted_party_detail[i]) {
        results.push(sorted_party_detail[i]);
        }
    }
    return results;
}

function constructSupplierCode(){
    setTimeout(function(){
        var supplier_code = $('#id_party-code').val().replace(/ /g,"_").toUpperCase();
        $('#id_party-code').val(supplier_code);
    },10);
}