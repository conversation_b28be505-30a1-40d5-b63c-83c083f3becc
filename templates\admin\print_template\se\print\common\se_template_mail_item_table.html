
<table id="item_table" style="font-size:11px;width: 100%;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
     <thead>
        <tr style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
            {% if item_res.item_table.sno.print %}
                <th rowspan="2" scope="col" style="width: 6%;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">S.no</th>
            {% endif %}
            <th rowspan="2" style="width:30%;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">Description</th>
            {% if item_res.item_table.hsnsac.print and not item_res.item_table.hsnsac.print_in_itemdetails %}
                <th rowspan="2" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">HSN/SAC</th>
            {% endif %}
            {% if item_res.item_table.quantity.print %}
                <th rowspan="2" scope="col" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">Qty</th>
            {% endif %}
            {% if item_res.item_table.units.print and not item_res.item_table.units.units_in_qty %}
                <th rowspan="2" scope="col" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">UOM</th>
            {% endif %}
            {% if item_res.item_table.unit_price.print %}
                <th rowspan="2" scope="col" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">Price<br>USD</th>
            {% endif %}
            {% if item_res.item_table.discount.print %}
                <th rowspan="2" scope="col" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">Disc<br>%</th>
            {% endif %}
            {% if item_res.item_table.taxable_amount.print %}
                <th rowspan="2" scope="col" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">Total<br>USD</th>
            {% endif %}
        </tr>
    </thead>
    <tbody style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
        {% for material in se_item_details %}
            {%  if forloop.counter|divisibleby:2 %}
               <tr style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
            {% else %}
			    <tr style="background: #ffffff;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
			{% endif %}
            {% if item_res.item_table.sno.print %}
                <td style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">{{forloop.counter}}</td>
            {% endif %}
                <td style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
                    <span class="pdf_item_name"><span class="pdf_item_name_txt">{{ item_res.item_table.item_details.itemname_label }}</span> {{ material.material_name }}<br></span>
                    {% if material.material_drawing_no %}
                        <span class="pdf_item_drawing_number">
                            <span class="pdf_item_drawing_number_txt">{{ item_res.item_table.item_details.itemcode.label }}</span>
                            {{ material.material_drawing_no }}<br />
                        </span>
                    {% endif %}
                    {% if item_res.item_table.item_details.make.print and item_res.item_table.item_details.part_no.print %}
						{% if material.material_make and material.material_make != "" %}
							<span class="pdf_item_make">
								<i class="pdf_item_make_txt">{{ item_res.item_table.item_details.make.label }}</i>{{ material.material_make }}
							</span>
							{% if material.part_no and material.part_no != "" %}
								<span class="pdf_item_part">
									<i class="pdf_item_part_txt">{{ item_res.item_table.item_details.part_no.label }}</i>
								</span>
								<span>{{ material.part_no }}<br></span>
							{% else %}
								 <br>
							{% endif %}
						{% endif %}
					{% elif item_res.item_table.item_details.make.print or item_res.item_table.item_details.part_no.print %}
						{% if item_res.item_table.item_details.make.print %}
							{% if material.material_make and material.material_make != "" %}
								<span class="pdf_item_make">
									<i class="pdf_item_make_txt">{{ item_res.item_table.item_details.make.label }}</i>{{ material.material_make }}<br>
								</span>
							{% endif %}
						{% else %}
							{% if material.part_no and material.part_no != "" %}
								<span class="pdf_item_part">
									<i class="pdf_item_part_txt">{{ item_res.item_table.item_details.part_no.label }}</i>
								</span>
								<span>{{ material.part_no }}<br></span>
							{% endif %}
						{% endif %}
					{% endif %}
                    {% if material.hsn_code %}
                         {% if item_res.item_table.hsnsac.print_in_itemdetails %}
					        <span class="pdf_item_hsn_code"><i class="pdf_item_hsn_code_txt">{{ item_res.item_table.hsnsac.label }}</i> {{ material.hsn_code }}<br /></span>
                         {% endif %}
                    {% endif %}
                    {% if material.material_description %}
					    <span class="pdf_item_desc"><span class="pdf_item_desc_txt">{{ item_res.item_table.item_details.description.label }}</span>{{ material.material_description }}<br></span>
                    {% endif %}
                </td>
                {% if item_res.item_table.hsnsac.print and not item_res.item_table.hsnsac.print_in_itemdetails %}
                    <td style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">{{ material.hsn_code }}</td>
                {% endif %}
                {% if item_res.item_table.quantity.print %}
                    <td style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: right">{{ material.material_quantity|floatformat:2 }}<br />
                       {% if item_res.item_table.units.units_in_qty %}
                        <span class="pdf_unit_in_price hide">({{ material.material_unit }})</span>
                        {% endif %}
                    </td>
                {% endif %}
                {% if item_res.item_table.units.print and not item_res.item_table.units.units_in_qty %}
                    <td style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">{{ material.material_unit }}</td>
                {% endif %}
                {% if item_res.item_table.unit_price.print %}
                    <td style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: right">{{ material.material_rate|floatformat:2 }}</td>
                {% endif %}
                {% if item_res.item_table.discount.print %}
                    <td style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: right">{{ material.material_discount }}</td>
                {% endif %}
                {% if item_res.item_table.taxable_amount.print %}
                    <td style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: right">{{ material.material_taxable_value }}</td>
                {% endif %}
            </tr>
        {% endfor %}
    </tbody>
    <tfoot>
        <tr style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
            {% if summary_res.totals.print_subtotal.print %}
                <td colspan="3" style="font-size: 12px; line-height: 12px;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: right;"><b>Sub-Total</b></td>
            {% endif %}
            {% if summary_res.totals.print_qtytotal.print %}
                <td style="font-size: 12px; line-height: 12px;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">{{ total_quantity }}</td>
            {% endif %}
            {% if summary_res.totals.print_total_in_words.print %}
                <td colspan="4" style="font-size: 12px; line-height: 12px;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;text-align: right;">{{ total_value | floatformat:2}}</td>
            {% endif %}
        </tr>
         {% for tax in se_taxes %}
            <tr style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;{% if misc_res.print_summary_first_page and appendix_pages > 0 %}display:none;{% endif %}">
                <td colspan="3" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: right;">{{ tax.tax_name }} @ {{ tax.tax_rate }}%</td>
                <td colspan="5" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: right;">{{ tax.tax_value }}</td>
            </tr>
        {% endfor %}
        {% if source.round_off != 0 %}
            <tr id="round-off-container" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
                <td colspan="3" style="font-size: 12px; line-height: 12px;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;text-align: right;"><b>Round Off</b></td>
                <td colspan="5" style="font-size: 12px; line-height: 12px;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;text-align: right;">{{ source.round_off }}</td>
            </tr>
        {% endif %}
        <tr style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;{% if misc_res.print_summary_first_page and appendix_pages > 0 %}display:none;{% endif %}">
            <td colspan="3" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;text-align: right;"><b>Grand Total</b><span class="total_inclusice_tax hide"> (inclusive tax)</span></td>
            <td colspan="5" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;text-align: right;">{{ source.currency.code }} <b>{{ source.grand_total }}</b></td>
        </tr>
        <tr style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;{% if misc_res.print_summary_first_page and appendix_pages > 0 %}display:none;{% endif %}">
            <td colspan="8" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;"><b>Total Value ({{ source.currency.code }}):</b> {{ total_in_words|upper }}</td>
        </tr>
        {% if header_res.field_name.special_instructions.print %}
            {% if special_instructions %}
                <tr style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
                    <td colspan="8" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;"><b class="se_special_instruction_txt">{{ header_res.field_name.special_instructions.label }}</b>:  {{ special_instructions }}</td>
                </tr>
            {% endif %}
        {% endif %}
        {% if source.notes != "" %}
            <tr style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
                <td colspan="8" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;{% if misc_res.print_summary_first_page and appendix_pages > 0 %}display:none;{% endif %}">
                    <strong>Notes:</strong>
                    {% autoescape off %} {{ source.notes }} {% endautoescape %}
                </td>
            </tr>
         {% endif %}
    </tfoot>
</table>