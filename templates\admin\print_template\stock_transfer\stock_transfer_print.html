<!DOCTYPE html>
<style type="text/css">
	body{
		color: #000;
		font-family: 'Times New Roman';
		margin: 0;
		padding: 0;
		display: flex;
		flex-direction: column;
		min-height: 100vh;
	}
	.container {
		flex: 1;
	}
	td {
		text-align: left;
		line-height: 25px;
		font-size: 11pt !important;
		padding:5px !important;
	}
	th{
		font-size:12pt;
		text-align:center !important;
		padding: 0px 5px !important;
	}
	.enterprise_details{
		font-size:12pt;
	}
	.enterprise_name{
		font-size:14pt
	}
	.page_title{
		font-size: 15pt;
	}
</style>
<body>
    <div class="container">
        <div class="pdf_header">
            <div class="row">
                <div class="col-sm-5">
                    <img src="{{enterprise_logo}}" class="pdf_company_logo" style="height: 65px;" alt="Company Logo"><br>
                    <span class="pdf_company_name" style="display: inline-block; padding: 3px 0px; font-size: 14px; font-family: 'Times New Roman';"><b>{{ from_location.name }}</b></span><br>
                </div>
                <div class="col-sm-7 text-right" style="display: inline-block;">
                    <div class="col-sm-12" style="margin-top: 15px;">
                        <span class="text-right qrcode hide"><img src="" style="margin-top: -35px;" alt="QR Code"> </span><br>
                        <span class="text-right barcode hide"><img src="" style="margin-top:-19px;" alt="Barcode"></span><br>
                        <span class="pdf_form_name text-right" style="font-size: 16px; padding-left: 0; padding-right: 3px;"><b class="pdf_form_name_text" style="font-size: 19px;">DELIVERY CHALLAN</b></span>
                        <span class="pdf_invoice_no text-left" style="font-size: 12px; padding: 0px;"><b class="pdf_invoice_prefix" style="font-size: 10px;"></b> <b class="pdf_invoice_no preview_invoice_number" style="font-size: 10px;">{{idc_no}}</b></span><br>
                        {% if from_location.gst_label %}<span><b>GSTIN:</b> {{ from_location.gst_label }} {% endif %}</span>
                    </div>
                    <div class="col-sm-12 text-right" style="margin-top: 12px;">
                        <span class="pdf_issue_date_time" style="font-size: 14px;"></span>
                        <span class="pdf_invoice_datetime">Issue Date</span> <b>
                        <span class="issue_date_time">{{ prepared_on }}</span></b>
                    </div>
                </div>
                <div class="col-sm-12 pdf_complete_address">
                    <hr style="margin:0;border: 0.8px solid #666; ">
                </div>
                <div class="col-sm-12 pdf_company_address_container" style="padding: 3px 15px; font-size: 14px;">
                    <span class="pdf_company_address">
                         {{ from_location.address1 }}
                    </span>
                    <span><br/>
                            {% if from_location.phone_no %}<b>Ph:</b> {{ from_location.phone_no }} {% endif %}<br/>
                            {% if from_location.email %}<b>E-mail:</b> {{ from_location.email }} {% endif %}<br/>
                    </span>
                </div>
                <div class="col-sm-12 pdf_complete_address">
                    <hr style="margin:0;border: 0.8px solid #666; ">
                </div>
                <div class="col-sm-8 remove-padding">
                    <div class="col-sm-6 pdf_bill_to_address pdf_company_details_container" style="font-size: 14px;">
                        <span>
                            <b class="bill_to_text" style="border-bottom: 1px solid #666;text-decoration: none!important;">BILL TO</b>
                            <b class="pdf_bill_to_enterprise_detail">{{ to_location.name }}</b>
                        </span>
                        <span><br>
                            {{ to_location.address1 }}<br>
                            {% if to_location.phone_no %}<b>Ph:</b> {{ to_location.phone_no }} {% endif %}<br/>
                            {% if to_location.email %}<b>E-mail:</b> {{ to_location.email }} {% endif %}<br/>
                            {% if to_location.gst_label %}<b>GSTIN:</b> {{ to_location.gst_label }} {% endif %}
                        </span>
                    </div>
                    <div class="col-sm-6 pdf_bill_to_address pdf_company_details_container" style="font-size: 14px;">
                        <span>
                            <b class="bill_to_text" style="border-bottom: 1px solid #666;text-decoration: none!important;">SHIP TO</b>
                            <b class="pdf_bill_to_enterprise_detail">{{ to_location.name }}</b>
                        </span>
                        <span><br>
                            {{ to_location.address1 }}<br>
                            {% if to_location.phone_no %}<b>Ph:</b> {{ to_location.phone_no }} {% endif %}<br/>
                            {% if to_location.email %}<b>E-mail:</b> {{ to_location.email }} {% endif %}<br/>
                            {% if to_location.gst_label %}<b>GSTIN:</b> {{ to_location.gst_label }} {% endif %}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="pdf_table">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th style="width: 10%;">S.No</th>
                        <th style="width: 50%;">Material</th>
                        <th style="width: 20%;">Quantity</th>
                        <th style="width: 10%;">Unit Price</th>
                        <th style="width: 10%;">Amount</th>
                    </tr>
                </thead>
                <tbody>
                {% for item in transfer_details %}
                    <tr>
                        <td class="item_description text-center">{{ forloop.counter }}.</td>
                        <td class="item_quantity text-center">{{ item.item.name }} {% if item.make_name %}[{{ item.make_name }}]{% endif %}  -  {% if item.item.drawing_no %}{{item.item.drawing_no}}{% endif %} {{item.item.unit.unit_name}}</td>
                        <td class="item_unit_price text-right">{{ item.quantity }}</td>
                        <td class="item_tax text-right">{{ item.rate }}</td>
                        <td class="item_total text-right">{{ item.quantity|multiply:item.rate }}</td>
                    </tr>
                {% endfor %}
                    <tr>
                        <td colspan="4" style="text-align:right; font-weight:bold;">Total</td>
                        <td class='text-right' style="font-weight:bold;">{{ total_value }}</td>
                    </tr>
                    <tr class="row_seperator column_seperator total_section">
                        <td colspan="2" class="text-right total_section_1"><b>Grand Total</b></td>
                        <td colspan="8" class="text-right total_section_3">INR <b>{{ total_value }}</b></td>
				    </tr>
                    <tr class="tr_total_in_words row_seperator show_total_in_words total_in_words">
                        <td colspan="10" class="full-length-td"><b>Total Value (INR):</b> {{total_in_words}} Rupees Only</td>
                    </tr>
                </tbody>
            </table>
            <div class="notes notes_section" style="border-bottom: 1px solid rgb(102, 102, 102);font-size: 11px; line-height: 11px;">Certified that the particulars given above are true and correct and the amount indicated represents the price actually charged and that there is no flow or additional consideration directly or indirectly from the buyer.<br>
Certified that the particulars given above are true and correct and the amount indicated is provisional, as additional consideration will be received from the buyer on account of.</div>
        </div>
    </div>
</body>

</html>
