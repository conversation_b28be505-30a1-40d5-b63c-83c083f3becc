import ast
from datetime import datetime
from pprint import pprint

from django.http import HttpResponse
from django.template.response import TemplateResponse
import simplejson
import json
import os

from sqlalchemy import or_
from erp import helper
from erp.auth import USER_IN_SESSION_KEY, ENTERPRISE_ID_SESSION_KEY, SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.dao import executeQuery
from erp.helper import validate_payload, CustomJSONEncoder, model_to_dict, updateRemarks
from erp.models import StockTransfer, TransferDetails, Material, User
from erp.properties import TEMPLATE_TITLE_KEY, MANAGE_MRS_TEMPLATES, MANAGE_MRS_TEMPLATE, EDIT_MRS_URL, \
    MANAGE_MRS_LIST_URL
from erp.stores import logger
from erp.stores.backend import StoresService, MaterialRequisitionService, StockTransferService, StoresDAO
from erp.stores.document_compiler import StockTransferPDFGenerator
from settings import SQLASession
from util.api_util import response_code, JsonUtil
from util.helper import getFinancialYear, getF<PERSON>attedDocPath, writeFile
from erp.admin.backend import UserDAO
from erp.masters.backend import MasterService, LocationService
from erp.tasks import process_inventory_update

MRS_NO_FIELD_KEY = 'mrs_no'
DELETE_MRS_KEY = 'mrs_id'
MATERIAL_FORM_KEY = 'material_form'
BILL_OF_MATERIALS_FORMSET_KEY = 'bill_of_materials_formset'
SPECIFICATION_FORMSET_KEY = 'specifications_formset'
MATERIAL_MAKE_FORMSET_KEY = 'material_makes_formset'
ALTERNATE_UNIT_FORMSET_KEY = 'alternate_unit_formset'
PRICE_OF_SUPP_MATERIALS_FORMSET_KEY = 'materials_party_price_formset'

__author__ = 'saravanan'

STOCK_TRANSFER_STATUS = {1: "Draft", 2: "Approved", 3: "Received", 4: "Rejected"}


def renderDashboard(request):
    request_handler = RequestHandler(request)
    since, till = JsonUtil.getDateRange(
        rh=request_handler, since_session_key='stores.home.since', till_session_key='stores.home.till')
    logger.info('Accessing store Dashboard for date range: %s - %s' % (since, till))

    return TemplateResponse(request=request, template='stores/home.html', context={
        'from_date': since.strftime("%Y-%m-%d"), 'to_date': till.strftime("%Y-%m-%d"),
        TEMPLATE_TITLE_KEY: "Stores"})


def generateDCStoreReport(request):
    rh = RequestHandler(request)
    try:
        stores_service = StoresService()
        enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
        since, till = JsonUtil.getDateRange(rh=rh, since_session_key="dcr.since", till_session_key="dcr.till")
        drawing_no = rh.getAndCacheData(key="drawing_no", session_key="dcr.drawing_no")
        if not drawing_no:
            drawing_no = "All"
        make_id = rh.getAndCacheData(key="make_id", session_key="dcr.make_id")
        is_faulty = rh.getAndCacheData(key="is_faulty", session_key="dcr.is_faulty")
        supplier = rh.getAndCacheData(key="suppliers", session_key="dcr.supplier")
        approved_only = rh.getAndCacheData(key="approved_only", session_key="dcr.approved_only") == "true"
        is_returnable = rh.getAndCacheData(key="returnable", session_key="dcr.returnable") != "false"
        is_non_returnable = rh.getAndCacheData(key="non_returnable", session_key="dcr.non_returnable") != "false"
        suppliers = stores_service.getSuppliersData(enterprise_id=enterprise_id)
        if not supplier:
            supplier = "All"
        return TemplateResponse(template='stores/dcreport.html', request=request, context={
            TEMPLATE_TITLE_KEY: "DC Report",
            "since": since.strftime("%Y-%m-%d"), "till": till.strftime("%Y-%m-%d"),
            "drawing_no": drawing_no, "make_id": make_id, "is_faulty": is_faulty,
            "supplier": supplier, "suppliers": suppliers, "approved_only": approved_only,
            "is_returnable": is_returnable, "is_non_returnable": is_non_returnable})
    except Exception as e:
        logger.exception("Could not load page DC Report... %s" % e.message)
        raise e


def stock_report_page(request):
    rh = RequestHandler(request)
    stores_service = StoresService()
    enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
    suppliers = stores_service.getSuppliersData(enterprise_id=enterprise_id)
    since, till = JsonUtil.getDateRange(rh=rh, since_session_key="csr.since", till_session_key="csr.till")
    drawing_no = rh.getAndCacheData(key="drawing_no", session_key="csr.drawing_no")
    if not drawing_no:
        drawing_no = "All"
    make_id = rh.getAndCacheData(key="make_id", session_key="csr.make_id")
    is_faulty = rh.getAndCacheData(key="is_faulty", session_key="csr.is_faulty")

    return TemplateResponse(template='stores/stockreport.html', request=request, context={
        "since": since.strftime("%Y-%m-%d"), "till": till.strftime("%Y-%m-%d"),
        "drawing_no": drawing_no, "make_id": make_id, "is_faulty": is_faulty,
        'suppliers': suppliers, TEMPLATE_TITLE_KEY: "Stock Report"})


def grn_report_page(request):
    stores_service = StoresService()
    request_handler = RequestHandler(request)
    enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
    suppliers = stores_service.getSuppliersData(enterprise_id=enterprise_id)
    materials = stores_service.getMaterialData(enterprise_id=enterprise_id)

    return TemplateResponse(
        template='stores/grnreport.html', request=request, context={
            'suppliers': suppliers, 'materials': materials, TEMPLATE_TITLE_KEY: "GRN Report"})


# ******************************************  Closing Stock Report *****************************************************
def stock_report_view_generate(request):
    """
	Session Prefix csr for Closing Stock Report
	:param request:
	:return:
	"""
    request_handler = RequestHandler(request)
    enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
    since, till = JsonUtil.getDateRange(rh=request_handler, since_session_key="csr.since", till_session_key="csr.till")
    stock_type = request_handler.getAndCacheData(key="stock_type", session_key="csr.stock_type")
    location_id = request_handler.getPostData("location")
    try:
        make_wise_material_stock_query = StoresService().getStockQuery(
            enterprise_id=enterprise_id, from_date=since, to_date=till, stock_type=stock_type, location_id=location_id)
        result = executeQuery(make_wise_material_stock_query)

    except Exception as e:
        logger.exception(e)
        result = ""
    return HttpResponse(content=simplejson.dumps(result), mimetype='application/json')


# ******************************************  Pending PO Report ********************************************************
def po_report_view_generate(request):
    request_handler = RequestHandler(request)
    enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
    since, till = JsonUtil.getDateRange(rh=request_handler, since_session_key="csr.since", till_session_key="csr.till")
    item_id = request_handler.getAndCacheData(key="item_id", session_key="csr.item_id")
    if not item_id:
        item_id = "All"
    make_id = request_handler.getAndCacheData(key="make_id", session_key="csr.make_id")
    is_faulty = request_handler.getAndCacheData(key="is_faulty", session_key="csr.is_faulty")
    party_id = request_handler.getAndCacheData(key="party_id", session_key="csr.party_id")
    reptype = request_handler.getAndCacheData(key="reptype", session_key="csr.reptype")

    condition = ""
    reportname = request_handler.getAndCacheData(key="repname", session_key="csr.repname")  # report type

    if reportname == "PO Pending Report":
        if party_id == "":
            condition += ""
        else:
            condition += " and a.supplier_id='" + party_id + "'"
        if item_id == "All":
            condition += ""
        else:
            condition += " and c.id='" + item_id + "' " \
                                                   "AND b.make_id= " + make_id + " AND b.is_faulty = " + is_faulty + ""

        if str(reptype) == "1":
            condition += """ and (b.pur_qty-ifnull((select sum(acc_qty) from grn_material as g,grn as gr where 
							g.po_no=a.id and g.item_id=b.item_id and g.make_id=b.make_id and g.is_faulty=b.is_faulty 
							and gr.grn_no=g.grnNumber AND g.rec_grn_id is NULL and gr.status > -1),0)) >0 """
        if str(reptype) == "2":
            condition += """ and (b.pur_qty-ifnull((select sum(acc_qty) from grn_material as g,grn as gr where 
							g.po_no=a.id and g.item_id=b.item_id and g.make_id=b.make_id and g.is_faulty=b.is_faulty 
							and gr.grn_no=g.grnNumber AND g.rec_grn_id is NULL and gr.status > -1),0)) = 0 """

    try:
        isql = """SELECT 
				a.orderno,
				DATE_FORMAT(a.order_date,'%%d/%%m/%%Y'),
				p.party_name,
				IFNULL(c.drawing_no,"") as itemno,
				c.name AS item_name,
				IF (a.is_mask_po = 0, b.po_price, 'N/A') as itemprice,
				u.unit_name,
				b.pur_qty,
				ifnull((select sum(acc_qty) 
					FROM 
					grn_material as g,grn as gr 
					WHERE g.po_no=a.id and g.item_id=b.item_id and g.make_id=b.make_id and g.is_faulty=b.is_faulty and 
						gr.grn_no=g.grnNumber AND g.rec_grn_id is NULL and gr.status > -1 AND ((b.make_id = g.make_id AND 
						b.enterprise_id = g.enterprise_id) OR (b.make_id IS NULL AND g.make_id IS NULL))),0),
						(b.pur_qty-ifnull((select sum(acc_qty) from grn_material as g,grn as gr 
						WHERE g.po_no=a.id and g.item_id=b.item_id and g.make_id=b.make_id and 
							g.is_faulty=b.is_faulty and gr.grn_no=g.grnNumber AND g.rec_grn_id is NULL and gr.status > -1 AND 
							((b.make_id = g.make_id AND b.enterprise_id = g.enterprise_id) OR 
							(b.make_id IS NULL AND g.make_id IS NULL))),0)) as pendqty,
				a.type,a.financial_year,
				c.makes_json as make_name 
				FROM 
				purchase_order as a,purchase_order_material as b,materials as c,unit_master as u,party_master as p 
				WHERE 
					a.order_date between '%s' and '%s' and b.pid=a.id and b.item_id=c.id and p.party_id=a.supplier_id 
					and c.enterprise_id='%s' and u.unit_id=c.unit AND u.enterprise_id=c.enterprise_id and 
					p.enterprise_id='%s' and b.enterprise_id = a.enterprise_id %s and a.status = 2 ORDER by orderno """ % (
            since, till, enterprise_id, enterprise_id, condition)
        result = executeQuery(isql)
    except Exception as e:
        logger.exception(e)
        result = ""
    return HttpResponse(content=simplejson.dumps(result), mimetype='application/json')


# ******************************************  Pending Ind Report *******************************************************
def indent_report_view_generate(request):
    request_handler = RequestHandler(request)
    enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
    since, till = JsonUtil.getDateRange(rh=request_handler, since_session_key="csr.since", till_session_key="csr.till")
    item_id = request_handler.getAndCacheData(key="item_id", session_key="csr.item_id")
    if not item_id:
        item_id = "All"
    make_id = request_handler.getAndCacheData(key="make_id", session_key="csr.make_id")
    is_faulty = request_handler.getAndCacheData(key="is_faulty", session_key="csr.is_faulty")
    reptype = request_handler.getAndCacheData(key="reptype", session_key="csr.reptype")

    condition = ""
    pending_condition = ""
    reportname = request_handler.getAndCacheData(key="repname", session_key="csr.repname")  # report type
    if reportname == "Indent Pending Report":
        if item_id == "All":
            condition += ""
        else:
            condition += " and pom.item_id ='" + item_id + "' and pom.make_id = " + make_id + " " \
                                                                                              "and pom.is_faulty = " + is_faulty + ""

        if str(reptype) == "1":
            pending_condition += " WHERE indent_report.pen_qty > 0 "
        if str(reptype) == "2":
            pending_condition += " WHERE indent_report.pen_qty = 0 "
    try:
        isql = """SELECT * FROM (SELECT 
				pom.indent_code,
				DATE_FORMAT(pom.tDate, '%%d/%%m/%%Y %%T') AS tDate,
				pom.project AS project,
				IFNULL(pom.drawing_no, "") as drawing_no,
				pom.item_name AS item_name,
				pom.price as price,
				pom.unit_name as unit_name,
				pom.ind_qty AS ind_qty,
				IFNULL(ia.pur_qty,0) AS pur_qty,
				IFNULL(sum(gm.acc_qty),0) as grn_qty,
				pom.financial_year AS financial_year,
				pom.ind_qty - IFNULL(sum(gm.acc_qty),0) as pen_qty,pom.po_id,
				pom.make_name
			FROM
			indent_po_pending_views AS pom
				LEFT JOIN
			indent_material_abstract AS ia ON ia.indent_no = pom.indent_no
				AND ia.enterprise_id= pom.enterprise_id
				AND ia.item_id = pom.item_id
				AND ia.make_id= pom.make_id
				AND ia.is_faulty = pom.is_faulty
				LEFT JOIN
			grn_material gm ON pom.po_id = gm.po_no
				AND gm.enterprise_id = pom.enterprise_id
				AND gm.item_id = pom.item_id
				AND gm.make_id = pom.make_id
				AND gm.is_faulty = pom.is_faulty
				AND gm.rec_grn_id IS NULL				
			WHERE
			pom.tDate BETWEEN '%s' AND '%s' AND    
			pom.enterprise_id = '%s' %s GROUP BY  pom.indent_code, pom.item_id , pom.make_id , pom.is_faulty) 
			as indent_report %s """ % (since, till, enterprise_id, condition, pending_condition)

        result = executeQuery(isql)
    except Exception as e:
        logger.exception(e)
        result = ""
    return HttpResponse(content=simplejson.dumps(result), mimetype='application/json')

#******************************************  Production plan blocked Report ***************************************************
def pp_blocked_report_view_generate(request):
    request_handler = RequestHandler(request)
    enterprise_id = request_handler.getPostData('enterprise_id')
    since = request_handler.getPostData('since')
    till = request_handler.getPostData('till')
    try:
        query = """
            SELECT 
                m.id as item_id, 
                m.drawing_no as drawing_no, 
                m.name as name, 
                mrp.pp_id as pp_id,
                mrp.required_qty as required_qty, 
                mrp.allocated_qty as allocated_qty, 
                mrp.issued_qty as issued_qty,
                (mrp.allocated_qty - mrp.issued_qty) AS blocked_qty,
                IF(
                    po.status > 1, 
                    CONCAT(
                        po.financial_year, 
                        IF(po.type = 2, '/PP/', '/JO/'), 
                        LPAD(po.orderno, 6, '0'), 
                        IFNULL(po.sub_number, '')
                    ), 
                    CONCAT(
                        IF(po.type = 2, 'TMP#PP-', 'TMP#JO-'), 
                        po.id
                    )
                ) AS pp_no
            FROM
                mrp_materials AS mrp
            LEFT JOIN
                materials AS m
            ON 
                mrp.item_id = m.id 
                AND mrp.enterprise_id = m.enterprise_id
            LEFT JOIN 
                purchase_order AS po
            ON 
                mrp.pp_id = po.id 
                AND mrp.enterprise_id = po.enterprise_id
            WHERE
                po.order_date BETWEEN %s AND %s
                AND mrp.enterprise_id = %s
                AND mrp.required_qty != 0 
                AND mrp.allocated_qty != 0;
        """
        parameters = (since, till, enterprise_id)
        result = executeQuery(query=query, query_data=parameters, as_dict=True)

    except Exception as e:
        logger.exception(e)
        result = ""
    return HttpResponse(content=simplejson.dumps(result), mimetype='application/json')

#******************************************  Minimum Stock Level Report ***************************************************
def msl_report_view_generate(request):
    request_handler = RequestHandler(request)
    enterprise_id = request_handler.getPostData('enterprise_id')
    since = request_handler.getPostData('since')
    till = request_handler.getPostData('till')
    try:
        query = """
        SELECT
            m.drawing_no AS drawing_no,
            m.name AS name,
            u.unit_name AS unit,
            m.minimum_stock_level AS minimum_stock_level,
            cs.qty AS qty,
            (m.minimum_stock_level - cs.qty) AS purchased
        FROM
            materials AS m
        JOIN
            closing_stock AS cs
        ON
            m.id = cs.item_id AND m.enterprise_id = cs.enterprise_id
        JOIN
            unit_master AS u
        ON
            u.unit_id = m.unit AND u.enterprise_id = m.enterprise_id
        WHERE
            cs.is_faulty = 0 AND
            m.minimum_stock_level > 0
            AND cs.last_updated_on BETWEEN %s AND %s AND cs.enterprise_id = %s group by item_id;
        """
        parameters = (since, till, enterprise_id)
        result = executeQuery(query=query, query_data=parameters, as_dict=True)

    except Exception as e:
        logger.exception(e)
        result = ""
    return HttpResponse(content=simplejson.dumps(result), mimetype='application/json')


def fetchClosingStock(request):
    try:
        request_handler = RequestHandler(request)
        data = simplejson.loads(simplejson.dumps(request_handler.getPostData()))
        enterprise_id = request_handler.getPostData('enterprise_id')
        location_id = request_handler.getPostData('location_id')
        is_faulty = request_handler.getPostData('is_faulty') if request_handler.getPostData('is_faulty') else 0
        store_service = StoresService()
        if not enterprise_id:
            enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
        mat_list = []
        # TODO : optimize this function for better performance
        if request.POST.getlist('mat_list[]'):
            material_list = request.POST.getlist('mat_list[]')
            for item in material_list:
                mat_list.append(ast.literal_eval(item)['item_id'])
        else:
            mat_list.append(int(request_handler.getPostData('item_id')))
        logger.info("material_list:%s" % mat_list)
        response = response_code.success()
        response['closing_stock'] = store_service.getClosingStock(
            mat_list=mat_list, enterprise_id=enterprise_id, location_id=location_id, is_faulty=is_faulty)
        print("Stock:%s", response['closing_stock'])
        # if 'issued_on' in request.POST and request.POST['issued_on']:
        #     issued_on = datetime.strptime(str(request.POST['issued_on']), '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
        #     exact_issued_on_date = datetime.strptime(str(request.POST['issued_on']), '%Y-%m-%d %H:%M:%S')
        # else:
        #     issued_on = datetime.now().strftime('%Y-%m-%d')
        #     exact_issued_on_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        # closing_stock = getClosingStock(
        #     mat_list=mat_list, data=data, issued_on=issued_on, enterprise_id=enterprise_id,
        #     exact_issued_on_date=exact_issued_on_date, location_id=location_id)
        # response = response_code.success()
        # response['closing_stock'] = closing_stock
        # if not request.POST.getlist('mat_list[]') and 'exact_issued_on_date' != datetime.now().strftime(
        #         '%Y-%m-%d %H:%M:%S'):
        #     present_closing_stock = getClosingStock(
        #         mat_list=mat_list, data=data, issued_on=datetime.now().strftime('%Y-%m-%d'), enterprise_id=enterprise_id,
        #         exact_issued_on_date=datetime.now().strftime('%Y-%m-%d %H:%M:%S'), location_id=location_id)
        #     if present_closing_stock[0]["closing_qty"] < closing_stock[0]["closing_qty"]:
        #         response['closing_stock'] = present_closing_stock
    except Exception as e:
        logger.exception(e)
        response = response_code.internalError()
        response['error'] = '%s' % e.message
    return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getClosingStockOldMethod(mat_list=None, data=None, issued_on=None, enterprise_id=None, exact_issued_on_date=None,
                    location_id=None):
    try:
        all_closing_stock = []
        store_service = StoresService()
        if not issued_on:
            issued_on = datetime.now().strftime('%Y-%m-%d')
        if not exact_issued_on_date:
            exact_issued_on_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        if mat_list:
            make_wise_material_stock_query = store_service.getStockQuery(
                enterprise_id=enterprise_id, from_date=issued_on + " 00:00:00", to_date=issued_on + " 23:59:59",
                material_list=mat_list, location_id=location_id)
        else:
            mat_list = [str(data)]
            make_wise_material_stock_query = store_service.getStockQuery(
                enterprise_id=enterprise_id, from_date=issued_on + " 00:00:00", to_date=issued_on + " 23:59:59",
                material=data, location_id=location_id)
        make_wise_material_stock_all = executeQuery(make_wise_material_stock_query)
        for material in mat_list:
            material = ast.literal_eval(material)
            is_edit = False
            edit_add = False
            if material["invoice_id"] != "":
                is_edit = True
            closing_stock = executeQuery(
                store_service.current_stock_query(enterprise_id=enterprise_id, location_id=location_id,
                                                  material=material, exact_issued_on_date=exact_issued_on_date))
            for mat in make_wise_material_stock_all:
                if material['item_id'] == mat[0] and str(material["is_faulty"]) == str(mat[-1]):
                    make_wise_material_stock = mat
                    if is_edit and closing_stock[0][1] > make_wise_material_stock[4]:
                        edit_add = True
                    else:
                        edit_add = False
                    if closing_stock[0][1] > make_wise_material_stock[4]:
                        closing_stock = (
                            (make_wise_material_stock[0], make_wise_material_stock[6], make_wise_material_stock[12]),)
            closing_stock_qty = closing_stock[0][1]
            item_msl = closing_stock[0][2]
            if material["alternate_unit_id"] and material["alternate_unit_id"] != 0:
                scale_factor = helper.getScaleFactor(
                    enterprise_id=enterprise_id, item_id=material['item_id'],
                    alternate_unit_id=material["alternate_unit_id"])
                if scale_factor:
                    closing_stock_qty = closing_stock[0][1] / scale_factor
                    item_msl = closing_stock[0][2] / scale_factor

            all_closing_stock.append(
                {
                    'item_id': closing_stock[0][0], 'make_id': material["make_id"], 'is_faulty': material["is_faulty"],
                    'closing_qty': closing_stock_qty, 'msl_qty': item_msl, 'inward_date': data['issued_on'],
                    'invoice_id': material["invoice_id"], 'edit_add': edit_add})

        if mat_list:
            all_closing_stock = all_closing_stock
        else:
            all_closing_stock = (
                (make_wise_material_stock_all[0][0], make_wise_material_stock_all[0][6],
                 make_wise_material_stock_all[0][12]),)
    except Exception as e:
        logger.exception(e)
        all_closing_stock = ""
    return all_closing_stock


@validate_payload(["enterprise_id", "material"])
def get_location_wise_closing_stock(request):
    """
    The View is used to get the location wise closing stock
    """
    processed_items = {}
    try:
        request_handler = RequestHandler(request)
        store_service = StoresService()
        enterprise_id = request_handler.getData("enterprise_id")
        material = json.loads(request_handler.getData("material"))
        exact_issued_on_date = request_handler.getData("exact_issued_on_date")
        closing_stock_list = executeQuery(
            store_service.construct_location_wise_item_stock_query(
                enterprise_id=enterprise_id, material=material, exact_issued_on_date=exact_issued_on_date),
            as_dict=True)
        for item in closing_stock_list:
            item_id = item['id']
            if item_id not in processed_items:
                # Create a new entry if the item id doesn't exist in the output
                processed_items[item_id] = {
                    "category": item['category'],
                    "unit_name": item['unit_name'],
                    "drawing_no": item['drawing_no'],
                    "closing_stocks": [{"location_name":item['location_name'],"qty":item['Closing'], "location_id": item["location_id"]}],
                    "item_name": item['name'],
                    "price": item['price'],
                    "minimum_stock_level": item['minimum_stock_level'],
                    "id": item['id']
                }
            else:
                # Add to the closing_stocks if the item id already exists
                processed_items[item_id]["closing_stocks"].append({"location_name":item['location_name'], "qty":item['Closing'],"location_id":item['location_id']})
        for item in processed_items.values():
            item['closing_stocks'] = [stock for stock in item['closing_stocks'] if stock["qty"] != 0]
        # Convert processed_items back to list format
        output_data = [item for item in processed_items.values() if item['closing_stocks']]
        response = response_code.success()
        response["data"] = output_data
    except Exception as e:
        response = response_code.internalError()
        logger.info("Failed to get the location wise stock:%s", e)
    return HttpResponse(json.dumps(response, cls=CustomJSONEncoder), status=response.get("response_code"),
                        content_type='application/json')


def location_wise_stock_summary_template(request):
    return TemplateResponse(template='stores/stock_summary.html', request=request, context={
        TEMPLATE_TITLE_KEY: "Stock Summary"})


def issueStockReport(request):
    """
	Loads stock issue report filters
	[since, till, issued_to, material(drawing_no, make_id, is_faulty), ignore_transaction_period]
	:param request:
	:return:
	"""
    rh = RequestHandler(request)
    try:
        enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
        since, till = JsonUtil.getDateRange(rh=rh, since_session_key="isr.since", till_session_key="isr.till")
        drawing_no = rh.getAndCacheData(key="drawing_no", session_key="isr.drawing_no")
        if not drawing_no:
            drawing_no = "All"
        make_id = rh.getAndCacheData(key="make_id", session_key="isr.make_id")
        is_faulty = rh.getAndCacheData(key="is_faulty", session_key="isr.is_faulty")
        issued_to = rh.getAndCacheData(key="issued_to", session_key="isr.issued_to")
        issued_to_list = [
            helper.listUniqueEntriesFromDB(enterprise_id=enterprise_id, table='invoice', column='issued_to')]
        if not issued_to:
            issued_to = "All"
        ignore_items_not_transacted = rh.getAndCacheData(
            key="ignore_items_not_transacted", session_key="isr.ignore_items_not_transacted")
        if ignore_items_not_transacted in (None, "true"):
            ignore_items_not_transacted = True
        return TemplateResponse(template='stores/issue_stock_report.html', request=request, context={
            TEMPLATE_TITLE_KEY: "Internal Stock Flow",
            "since": since.strftime("%Y-%m-%d"), "till": till.strftime("%Y-%m-%d"),
            "drawing_no": drawing_no, "make_id": make_id, "is_faulty": is_faulty, "issued_to_list": issued_to_list,
            "issued_to": issued_to, "ignore_items_not_transacted": ignore_items_not_transacted})
    except Exception as e:
        logger.exception("Could not load page Stock Issue Report... %s" % e.message)
        raise e


def jobInReport(request):
    """
	Loads stock issue report filters
	[since, till, party_id, material(drawing_no, make_id, is_faulty)]
	:param request:
	:return:
	"""
    rh = RequestHandler(request)
    try:
        enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
        since, till = JsonUtil.getDateRange(rh=rh, since_session_key="jnr.since", till_session_key="jnr.till")
        drawing_no = rh.getAndCacheData(key="drawing_no", session_key="jnr.drawing_no")
        if not drawing_no:
            drawing_no = "All"
        make_id = rh.getAndCacheData(key="make_id", session_key="jnr.make_id")
        is_faulty = rh.getAndCacheData(key="is_faulty", session_key="jnr.is_faulty")
        party_id = rh.getAndCacheData(key="party_id", session_key="jnr.party_id")
        party_id_list = [helper.populatePartyChoices(
            enterprise_id=enterprise_id, populate_all=False, is_customer=True, is_supplier=True)]
        if not party_id:
            party_id = "All"
        return TemplateResponse(template='stores/job_in_register_report.html', request=request, context={
            TEMPLATE_TITLE_KEY: "Job In Stock Register",
            "since": since.strftime("%Y-%m-%d"), "till": till.strftime("%Y-%m-%d"),
            "drawing_no": drawing_no, "make_id": make_id, "is_faulty": is_faulty, "party_id_list": party_id_list,
            "party_id": party_id})
    except Exception as e:
        logger.exception("Could not load page Stock Issue Report... %s" % e.message)
        raise e


def jobOutReport(request):
    """
	Loads stock issue report filters
	[since, till, party_id, material(drawing_no, make_id, is_faulty)]
	:param request:
	:return:
	"""
    rh = RequestHandler(request)
    try:
        enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
        since, till = JsonUtil.getDateRange(rh=rh, since_session_key="jor.since", till_session_key="jor.till")
        drawing_no = rh.getAndCacheData(key="drawing_no", session_key="jor.drawing_no")
        if not drawing_no:
            drawing_no = "All"
        make_id = rh.getAndCacheData(key="make_id", session_key="jor.make_id")
        is_faulty = rh.getAndCacheData(key="is_faulty", session_key="jor.is_faulty")
        party_id = rh.getAndCacheData(key="party_id", session_key="jor.party_id")
        party_id_list = [helper.populatePartyChoices(
            enterprise_id=enterprise_id, populate_all=False, is_customer=True, is_supplier=True)]
        if not party_id:
            party_id = "All"
        return TemplateResponse(template='stores/job_out_register_report.html', request=request, context={
            TEMPLATE_TITLE_KEY: "Job Out Stock Register",
            "since": since.strftime("%Y-%m-%d"), "till": till.strftime("%Y-%m-%d"),
            "drawing_no": drawing_no, "make_id": make_id, "is_faulty": is_faulty, "party_id_list": party_id_list,
            "party_id": party_id})
    except Exception as e:
        logger.exception("Could not load page Stock Issue Report... %s" % e.message)
        raise e


def checkClosingStockValue(request):
    try:
        request_handler = RequestHandler(request)
        data = {}
        enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
        mat_list = request.POST.getlist('mat_list[]')
        new_inward_date = request.POST['new_inward_date']
        old_inward_date = request.POST['old_inward_date']
        data["issued_on"] = old_inward_date
        closing_stock_1 = getClosingStockOldMethod(
            mat_list=mat_list, data=data, issued_on=old_inward_date, enterprise_id=enterprise_id,
            exact_issued_on_date=old_inward_date + " 23:59:59")
        data["issued_on"] = new_inward_date
        closing_stock_2 = getClosingStockOldMethod(
            mat_list=mat_list, data=data, issued_on=new_inward_date, enterprise_id=enterprise_id,
            exact_issued_on_date=new_inward_date + " 23:59:59")
        response = response_code.success()
        response["closing_stock_1"] = closing_stock_1
        response["closing_stock_2"] = closing_stock_2
    except Exception as e:
        logger.exception(e)
        response = response_code.internalError()
        response["custom_message"] = "Failed to validate stock availability. Please contact Admin!"
    return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def manageMaterialRequisition(request):
    """
	Renders the view to manage Material Requisition.
	:param request: a Http-URL-request asking for Material Requisition Webservice
	"""
    materials = []
    alternate_units = {}
    project_id = purpose = issue_id = None
    remarks = None
    header_master = {}
    prepared_on = ""
    try:
        request_handler = RequestHandler(request)
        mrs_id = request_handler.getPostData(MRS_NO_FIELD_KEY)
        mrs_service = MaterialRequisitionService()
        enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
        issue_to = mrs_service.getIssuesByList(enterprise_id=enterprise_id)
        project_choices = helper.populateProjectChoices(
            enterprise_id=enterprise_id, assort_frequent_choices=True,
            module_table_name='indents', frequents_condition="")
        financial_year = getFinancialYear()
        fetch_units = helper.populateAlternateUnits(enterprise_id=enterprise_id)
        for unit in fetch_units:
            alternate_units[str(unit['unit_name'])] = str(unit['alternate_unit_id'])
        master_service = MasterService()
        material_vo = master_service.constructMaterialVO(enterprise_id=enterprise_id)
        if mrs_id is None:
            mrs_code = mrs_service.getCode(financial_year=financial_year)
            title_key = "Material Request"
        else:
            mrs_obj = mrs_service.getMaterialRequisition(enterprise_id=enterprise_id, mrs_no=mrs_id)
            remarks = mrs_obj.remarks
            purpose = mrs_obj.document_description
            project_id = mrs_obj.project_code
            issue_id = mrs_obj.issue_id
            prepared_on = mrs_obj.prepared_on
            mrs_code = mrs_obj.getMRSCode() if mrs_obj.status != 0 else str(mrs_obj.id) + " (Draft No)"
            title_key = mrs_obj.getMRSCode() if mrs_obj.status != 0 else str(mrs_obj.id)
            materials = mrs_service.getMaterialRequisitionItems(enterprise_id=enterprise_id, mrs_id=mrs_id)
        header_master = {"issue_id": issue_id, "purpose": purpose, "project_id": project_id,
                         "remarks": simplejson.dumps(remarks), "prepared_on": prepared_on}
        return TemplateResponse(template=MANAGE_MRS_TEMPLATES, request=request, context={
            TEMPLATE_TITLE_KEY: title_key, "list_link": MANAGE_MRS_LIST_URL, "issue_to": issue_to, "mrs_code": mrs_code,
            "mrs_id": mrs_id,
            "project_choices": project_choices, "materials": materials, "all_units": alternate_units,
            "header_master": header_master,
            MATERIAL_FORM_KEY: material_vo.material_form, BILL_OF_MATERIALS_FORMSET_KEY: material_vo.material_formset,
            SPECIFICATION_FORMSET_KEY: material_vo.specification_formset,
            PRICE_OF_SUPP_MATERIALS_FORMSET_KEY: material_vo.supplier_price_material_formset,
            MATERIAL_MAKE_FORMSET_KEY: material_vo.material_make_formset,
            ALTERNATE_UNIT_FORMSET_KEY: material_vo.alternate_unit_formset,
        })
    except Exception as e:
        logger.exception("Failed to Process MRS - %s" % str(e))


def saveMaterialRequsition(request):
    contents = {}
    try:
        request_handler = RequestHandler(request)
        mrs_service = MaterialRequisitionService()
        enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
        user_id = request_handler.getSessionAttribute(SESSION_KEY)
        user = UserDAO().getUserById(enterprise_id=enterprise_id, user_id=user_id)
        mrs_id = request_handler.getPostData('mrs_id').encode()
        mrs_headers = json.loads(request_handler.getPostData('headers'))
        materials = json.loads(request_handler.getPostData('materials'))
        remarks = request_handler.getPostData('remarks').encode()
        contents = {
            "mrs_id": mrs_id if mrs_id != "None" else "",
            "mrs_headers": mrs_headers,
            "materials": materials,
            "remarks": remarks
        }
        mrs_to_be_saved = mrs_service.constructMaterialRequisition(enterprise_id=enterprise_id, contents=contents,
                                                                   user=user)
        if mrs_to_be_saved:
            response = response_code.success()
        else:
            response = response_code.failure()
    except Exception as e:
        logger.exception(e)
        response = response_code.internalError()
        response['error'] = '%s' % e.message
    return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def manageMaterialRequisitionList(request):
    mrs_list = []
    project_choices = []
    filter = {}
    try:
        request_handler = RequestHandler(request)
        receipt_title = "Material request"
        from_date, to_date = JsonUtil.getDateRange(
            rh=request_handler, since_session_key="%s.since" % receipt_title,
            till_session_key="%s.till" % receipt_title)
        project_code = request_handler.getAndCacheData(key="projects", session_key="Material request.project_code")
        project_code = project_code if project_code and project_code != 'None' and project_code != '0' else None
        project_id = ast.literal_eval(project_code)[0] if project_code else 0
        mrs_service = MaterialRequisitionService()
        enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
        mrs_list = mrs_service.getMaterialRequisitionList(enterprise_id=enterprise_id, since=from_date, till=to_date,
                                                          project_code=project_id)
        project_choices = helper.populateProjectChoices(
            enterprise_id=enterprise_id, assort_frequent_choices=True,
            module_table_name='indents', frequents_condition="")
        filter = {
            "from_date": from_date,
            "to_date": to_date,
            "project_id": project_id
        }
    except Exception as e:
        logger.exception(e)
        response = response_code.internalError()
        response['error'] = '%s' % e.message
    return TemplateResponse(template=MANAGE_MRS_TEMPLATE, request=request, context={
        TEMPLATE_TITLE_KEY: "Material Requisition", "mrs_list": mrs_list, "edit_link": EDIT_MRS_URL,
        "project_choices": project_choices, "filter": filter
    })


def deleteMaterialRequsition(request):
    """
	Requests the backend to delete a chosen MRS

	:param request: a Http-URL-request asking to delete a chosen MRS and its associated MRS Materials
	:return: a Http response redirected to the Material Requisition Management page
	"""
    try:
        request_handler = RequestHandler(request)
        enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
        mrs_service = MaterialRequisitionService()
        mrs_id = request_handler.getPostData(DELETE_MRS_KEY)
        logger.info("Deleting indent for Material requisition ID %s" % mrs_id)
        entity = mrs_service.getMaterialRequisition(enterprise_id=enterprise_id, mrs_no=mrs_id)
        can_delete_mrs = mrs_service.isIssueQtyAgainstMRS(enterprise_id=enterprise_id, mrs_id=mrs_id)
        if not can_delete_mrs:
            delete_response = mrs_service.deleteMaterialRequisition(enterprise_id=enterprise_id, mrs_id=mrs_id)
            if delete_response:
                response = response_code.success()
                response["response_message"] = 'Material Request  %s has been removed permanently' % entity.getMRSCode()
            else:
                response = response_code.failure()
                response["response_message"] = 'Attempt to delete the MRS - %s failed!' % entity.getMRSCode()
        else:
            response = response_code.failure()
            response["response_message"] = 'Some Issues have been raised against this MRS - %s' % entity.getMRSCode()
    except Exception as e:
        logger.exception("Failed deleting MRS... %s" % e.message)
        response = response_code.internalError()
        response['error'] = '%s' % e.message
    return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getMaterialRequisitionDocument(request):
    try:
        request_handler = RequestHandler(request)
        mrs_service = MaterialRequisitionService()
        mrs_id = request_handler.getData("mrs_id")
        enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
        mrs_service.generateMRSDocument(enterprise_id=enterprise_id, mrs_id=mrs_id)
        response = response_code.success()
        mrs_service = MaterialRequisitionService()
        entity = mrs_service.getMaterialRequisition(enterprise_id=enterprise_id, mrs_no=mrs_id)
        receipt_code = entity.getMRSCode()
        temp_doc_path = getFormattedDocPath(code=receipt_code, id=mrs_id)
        response['url'] = os.path.join(os.path.dirname(__file__), (temp_doc_path).replace("#", "%23"))
        response['remarks'] = entity.remarks
    except Exception as e:
        logger.exception("Failed to Generate MRS... %s" % e.message)
        response = response_code.internalError()
        response['error'] = '%s' % e.message
    return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def approveMaterialRequest(request):
    try:
        request_handler = RequestHandler(request)
        user_id = request_handler.getSessionAttribute(SESSION_KEY)
        mrs_service = MaterialRequisitionService()
        if user_id is None:
            user_id = request_handler.getPostData('user_id')
            enterprise_id = request_handler.getPostData('enterprise_id')
        else:
            enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
        mrs_id = request_handler.getPostData('mrs_id')
        remarks = request_handler.getPostData('remarks')
        status = mrs_service.approveMaterialRequisition(enterprise_id=enterprise_id, mrs_id=mrs_id, user_id=user_id,
                                                        remarks=remarks)
        if status:
            entity = mrs_service.getMaterialRequisition(enterprise_id=enterprise_id, mrs_no=mrs_id)
            receipt_code = entity.getMRSCode()
            response = response_code.success()
            response['code'] = receipt_code
            response['custom_message'] = "MRS No:<b> %s </b>has been successfully approved" % (receipt_code)
        else:
            response = response_code.internalError()
            response['custom_message'] = "MRS Failed to Approved"
    except Exception as e:
        logger.exception(e)
        response = response_code.internalError()
        response['custom_message'] = "MRS Failed to Approved"
    return HttpResponse(json.dumps(response), 'content-type=text/json')


def rejectMaterialRequest(request):
    try:
        request_handler = RequestHandler(request)
        user_id = request_handler.getSessionAttribute(SESSION_KEY)
        mrs_service = MaterialRequisitionService()
        if user_id is None:
            user_id = request_handler.getPostData('user_id')
            enterprise_id = request_handler.getPostData('enterprise_id')
        else:
            enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
        mrs_id = request_handler.getPostData('mrs_id')
        remarks = request_handler.getPostData('remarks')
        response = mrs_service.rejectMaterialRequisition(enterprise_id=enterprise_id, mrs_id=mrs_id, user_id=user_id,
                                                         remarks=remarks)
    except Exception as e:
        logger.exception(e)
        response = response_code.internalError()
        response['custom_message'] = "MRS Failed to Reject - %s" % (e)
    return HttpResponse(json.dumps(response), 'content-type=text/json')


@validate_payload([
    'enterprise_id', 'total_qty', 'total_value', 'last_modified_by',
    'from_location', 'to_location', 'status'
])
def create_stock_transfer(request):
    """
    The view is used to create or update a stock transfer and its associated transfer details.
    """
    print(request.POST)
    try:
        rh = RequestHandler(request)
        store_service = StoresService()
        response = store_service.create_stock_transfer(rh=rh)
        return HttpResponse(json.dumps(response, cls=CustomJSONEncoder), status=response.get("response_code"),
                            content_type='application/json')
    except Exception as e:
        logger.info("Internal Stock transfer Failed %s", e)
        return HttpResponse(json.dumps(response_code.internalError()),
                            status=response_code.internalError().get("response_code"),
                            content_type='application/json')

def stock_transfer_template(request):
    data = {"code": "%s/TMP/" % (getFinancialYear(for_date=datetime.now()))}
    if data.get("created_on") is None:
        data["created_on"] = datetime.now().strftime('%Y-%m-%d')
    return TemplateResponse(template='stores/stock_transfer.html', request=request, context={
        TEMPLATE_TITLE_KEY: "Internal Stock Transfer", "data": data})


def update_stock_transfer_template(request):
    context_data = request.POST.dict()
    if 'transfer_details' in context_data:
        context_data['transfer_details'] = json.loads(context_data['transfer_details'])
    if 'transfer_remarks' in context_data:
        context_data['transfer_remarks'] = json.loads(context_data['transfer_remarks'])
    return TemplateResponse(template='stores/stock_transfer.html', request=request, context={
        TEMPLATE_TITLE_KEY: context_data["code"], "data": context_data})


def stock_transfer_listing_template(request):
    return TemplateResponse(template='stores/stock_transfer_list.html', request=request, context={
        TEMPLATE_TITLE_KEY: "Internal Stock Transfer List"})


def populate_stock_transfer_list(request):
    """
    Loads the Stock Transfer List with choices appropriate for the enterprise in context.

    :param request:
    :return:
    """
    try:
        user_locations = []
        request_handler = RequestHandler(request)
        db_session = SQLASession()
        from_date, to_date = JsonUtil.getDateRange(rh=request_handler)
        status = request_handler.getData('status') if request_handler.getData('status') != '' else None
        enterprise_id = request_handler.getData('enterprise_id')
        user_id = request_handler.getData('user_id')
        user = db_session.query(User).filter(User.id == user_id).first()
        if user.is_super:
            location_list = LocationService().get_all_locations(enterprise_id=enterprise_id)
        else:
            location_list = LocationService().get_user_locations(enterprise_id=enterprise_id, user_id=user_id)
        for location in location_list:
            user_locations.append(location.id)
        filter = {
            "from_date": from_date,
            "to_date": to_date,
            "status": status,
            "user_locations": user_locations
        }
        stock_rec = (db_session.query(StockTransfer).filter(
            StockTransfer.enterprise_id == enterprise_id,
            StockTransfer.created_on >= from_date,
            StockTransfer.created_on <= to_date,
            or_(StockTransfer.to_location.in_(user_locations),StockTransfer.
                from_location.in_(user_locations))).order_by(StockTransfer.created_on.desc()))
        if status:
            stock_transfer_list = stock_rec.filter(StockTransfer.status == status).all()
        else:
            stock_transfer_list = stock_rec.all()
        response = response_code.success()
        response["data"] = []
        for stock_data in stock_transfer_list:
            stock_dict = model_to_dict(stock_data)
            stock_dict["code"] = stock_data.getCode()
            stock_dict["from_location"] = stock_data.from_location_rel.name
            stock_dict["from_location_id"] = stock_data.from_location_rel.id
            stock_dict["to_location"] = stock_data.to_location_rel.name
            stock_dict["to_location_id"] = stock_data.to_location_rel.id
            stock_dict["status"] = STOCK_TRANSFER_STATUS.get(stock_data.status)
            stock_dict["status_id"] = stock_data.status
            stock_dict["created_on"] = stock_data.created_on.strftime('%Y-%m-%d ')
            stock_dict["remarks"] = json.dumps(stock_dict["remarks"]) if stock_dict["remarks"] else []
            response["data"].append(stock_dict)
    except Exception as e:
        logger.info("Failed to populate the stock Transfer list %s", e)
        response = response_code.internalError()
    return TemplateResponse(template='stores/stock_transfer_list.html', request=request, context={
        TEMPLATE_TITLE_KEY: "Internal Stock Transfer List", "data": response, "filter": filter})


@validate_payload(['transfer_id'])
def populate_transfer_item_list(request):
    """
    Loads the  Transfer item List with choices appropriate for the transfer_id in context.

    :param request:
    :return:
    """
    try:
        request_handler = RequestHandler(request)
        stock_transfer_service = StockTransferService()
        transfer_id = request_handler.getData('transfer_id')
        transfer_list = stock_transfer_service.get_transfer_details(transfer_id=transfer_id)
        response = response_code.success()
        response["data"] = []
        for transfer_data in transfer_list:
            _data = model_to_dict(transfer_data)
            _data['unit_name'] = transfer_data.item.unit.unit_name
            response["data"].append(_data)
    except Exception as e:
        logger.info("Failed to populate the Transfer list %s", e)
        response = response_code.internalError()
    return HttpResponse(json.dumps(response, cls=CustomJSONEncoder),
                        status=response.get("response_code"), mimetype='application/json')


@validate_payload(['transfer_id', 'enterprise_id'])
def get_stock_transfer_document(request):
    try:
        request_handler = RequestHandler(request)
        stock_transfer_service = StockTransferService()
        transfer_id = request_handler.getData("transfer_id")
        enterprise_id = request_handler.getData("enterprise_id")
        source = stock_transfer_service.get_stock_transfer_entity(transfer_id=transfer_id, enterprise_id=enterprise_id)
        temp_doc_path = getFormattedDocPath(code=source.getCode(), id=source.transfer_id)
        _document_generator = StockTransferPDFGenerator(target_file_path=temp_doc_path)
        document_pdf = _document_generator.generatePDF(source)
        writeFile(document_pdf, temp_doc_path)
        if source.status == 4:
            logger.info("Adding Cancelled Watermark")
            _document_generator.addCancelWaterMark(temp_doc_path)
        response = response_code.success()
        response['url'] = os.path.join(os.path.dirname(__file__), temp_doc_path.replace("#", "%23"))
        response['remarks'] = source.remarks
    except Exception as e:
        logger.exception("Failed to Generate stock transfer document... %s" % e.message)
        response = response_code.internalError()
        response['error'] = '%s' % e.message
    return HttpResponse(content=json.dumps(response), mimetype='application/json')


# ******************************************  Nms Stock Report *****************************************************
def nonmoving_stock_report_view_generate(request):
    try:
        request_handler = RequestHandler(request)
        enterprise_id = request_handler.getPostData('enterprise_id')
        selected_option = request_handler.getPostData('selected_option')

        if selected_option is None:
            raise ValueError("selected_option is None")

        days_dict = dict({
            '30': 30,
            '60': 60,
            '3': 90,
            '6': 180,
            '9': 270,
            '12': 365,
            '1': 366
        })
        days = days_dict.get(selected_option, 30)
        if days == 366:
            result = StoresService().getnmsStockQuery366(enterprise_id=enterprise_id, days=days)
        else:
            result = StoresService().getnmsStockQuery(enterprise_id=enterprise_id, days=days)
        return HttpResponse(content=simplejson.dumps(result), mimetype='application/json')
    except Exception as e:
        logger.exception("Error processing the request: %s", e)
        error_response = {'status': 'error', 'message': 'An error occurred while processing the request'}
    return HttpResponse(json.dumps(error_response), content_type='application/json', status=500)
