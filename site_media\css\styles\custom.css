/*
	<PERSON><PERSON><PERSON>
 */
.grn_qty_txtbox {
	width:50px !important;
	height:25px !important;
	padding:0 !important;
	border:1px solid #c7c7c7 !important;
	line-height:20px !important;
	color:#908e8e !important;
	text-align:right !important;
}

.po_qty_cell_input {
	width:40px !important;
	height:20px !important;
	padding:0 !important;
	border:1px solid #c7c7c7 !important;
	line-height:20px !important;
	color:#908e8e !important;
}

.po_qty_cell_input:disabled{background-color:#eaeaea !important;}

.po_price_cell_input {
	width:45px !important;
	height:20px !important;
	padding:0 !important;
	border:1px solid #c7c7c7 !important;
	line-height:20px !important;
	color:#908e8e !important;
}

.po_price_cell_input:disabled{background-color:#eaeaea !important;}

.approve_po_button {
	background-color: #39658B;
	font-size: 14px;
	font-weight: 400;
	color: #FFF;
	display: inline-block;
	padding: 10px 18px;
	border: medium none;
	cursor: pointer;
	margin-top: 0px;
}

.approve_po_button:hover {
    background-color: #49759B;
    color: #FFF;
}

.reject_po_button {
	background-color: #59456B;
	font-size: 14px;
	font-weight: 400;
	color: #FFF;
	display: inline-block;
	padding: 10px 18px;
	border: medium none;
	cursor: pointer;
	margin-top: 0px;
}

.reject_po_button:hover {
    background-color: #69557B;
    color: #FFF;
}

.close_btn {
	background-color:#7b7a79;
	margin-left:2px;
	font-size:14px;
	font-weight:400;
	font-family: 'open_sansbold';
	color:#fff;
	display:inline-block;
	padding:10px 18px;
	border:none;
	cursor:pointer;
}

.close_btn:hover {
	background-color:#5b5a59;
	color:#fff;
}

.approver_remarks {
	border:1px
	solid #c7c7c7;
	width:100%;
	height:40px;
	line-height:40px;
	color:#908e8e;
	padding-left:10px;
	padding-right:5px;
	margin-bottom:15px;
}

.net_rate_txtbox {
	border:0px solid #c7c7c7 !important;
	width:80px !important;
	height:25px !important;
	line-height:20px !important;
	color:#908e8e !important;
	padding-left:10px !important;
	padding-right:3px !important;
	margin-bottom:3px !important;
}

.invoice_txt_box {
	border:1px solid #c7c7c7 !important;
	width:95% !important;
	height:30px !important;
	line-height:30px !important;
	color:#908e8e !important;
	padding-left:5px !important;
	padding-right:5px !important;
	margin-bottom:5px !important;
	disabled=true;

}

.invoice_net_val_txt_box {
	border:1px solid #c7c7c7 !important;
	width:100% !important;
	height:30px !important;
	line-height:30px !important;
	color:#908e8e !important;
	padding-left:5px !important;
	padding-right:5px !important;
	margin-bottom:5px !important;
	disabled=true;

}


.invoice_txt_box.full_textbox {
	/*width: 97.5% !important;*/
}

.invoice_txt_half_box {
	border:1px solid #c7c7c7 !important;
	width:50% !important;
	height:30px !important;
	line-height:30px !important;
	color:#908e8e !important;
	padding-left:5px !important;
	padding-right:5px !important;
	margin-bottom:5px !important;
	disabled=true;
}


.inv_material_txt_box {
	border:1px solid #c7c7c7 !important;
	width:100% !important;
	height:30px !important;
	line-height:30px !important;
	color:#908e8e !important;
	padding-left:5px !important;
	padding-right:5px !important;
	margin-bottom:5px !important;
}

.po_txt_box {
	border:1px solid #c7c7c7 !important;
	width:100% !important;
	height:30px !important;
	line-height:30px !important;
	color:#908e8e !important;
	padding-left:5px !important;
	padding-right:5px !important;
	margin-bottom:5px !important;
}
.po_txt_box:disabled{ background-color: #eaeaea;}

.po_title_txt {
	font-family: 'open_sansregular' !important;
	font-size:12px !important;
	color:#393836 !important;
	display:block !important;
	margin-bottom:5px !important;
}

.po-styled-select select {
	background: transparent;
	width: 100%;
	padding: 0px;
	font-size: 13px;
	line-height: 1;
	border: 0;
	border-radius: 0;
	height: 30px;
	border:none;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
}

.po-styled-select {
	overflow: hidden;
	background:url(/site_media/images/arw_new_select.png) no-repeat;
	background-position:100% center;
	border:1px solid #c7c7c7;
	width:100%;
	height:30px;
	line-height:30px;
	padding-right:15px;
	margin-bottom:5px;
}
.po-styled-select select:disabled {background-color: #eaeaea !important;}
.add_table .full_width_txt .po-styled-select {  background-position:98.5% center; }

.add_tax_btn { background-color:#39658b; font-size:14px; font-weight:bold; font-family: 'open_sansbold'; color:#fff; display:inline-block; padding:6px 6px 6px 6px; border:none; cursor:pointer; }
.add_tax_btn:hover { background-color:#284d6c; color:#fff; }

.view_table_content .po_taxes_table tr th { background-color:#fff !important; color: #000 !important; text-align:left;/* font-size:12px;*/ font-weight:400; border-bottom-width:1px !important;   }
.view_table_content .po_taxes_table tr td { background-color:#fff; text-align:right; font-size:11px;}

.search_result_table tr th {
    text-align: center !important;
    background-color: #605d5b !important;
    color: #eee !important;
    font-size:12px !important;
}
.search_result_table tr td {
	font-size:10px !important;
}

.button {
    background-color: #39658b;
    font-size: 13px;
    font-weight: 700;
    font-family: 'open_sansbold';
    color: #fff;
    display: inline-block;
    padding: 6px 15px;
    border: none;
    cursor: pointer;
}

.add_table .text_box {
    border: 1px solid #c7c7c7;
    width: 100%;
    height: 30px;
    font-size: 13px;
    line-height: 30px;
    color: #908e8e;
    padding-left: 5px;
    padding-right: 5px;
    margin-bottom: 10px;
}
.add_table .text_box:disabled{background-color: #eaeaea !important;}

.add_table .text_box_small {
    border: 1px solid #c7c7c7;
    width: 60%;
    height: 25px;
    font-size: 13px;
    line-height: 30px;
    color: #908e8e;
    padding-left: 5px;
    padding-right: 5px;
    margin-bottom: 10px;
}

.arrow_select {
    overflow: hidden;
    background: url(/site_media/images/arw_new_select.png) no-repeat;
    background-position: right top;
    border: 1px solid #c7c7c7;
    width: 100%;
    height: 30px;
    line-height: 30px;
    padding-right: 15px;
    margin-bottom: 5px;
    font-size: 13px;
}

.arrow_select select {
    background: transparent;
    width: 100%;
    padding: 5px;
    line-height: 1;
    border: 0;
    border-radius: 0;
    height: 30px;
    border: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    font-size: 12px;
    color: #908e8e;
}

.search_large_txt_box {
    border: 1px solid #c7c7c7;
    width: 100%;
    height: 30px;
    line-height: 30px;
    color: #908e8e;
    padding-left: 10px;
    padding-right: 5px;
    margin-bottom: 15px;
}

.text_box {
    border: 1px solid #c7c7c7;
    width: 100%;
    height: 30px;
    font-size: 13px;
    line-height: 30px;
    color: #908e8e;
    padding-left: 5px;
    padding-right: 5px;
    margin-bottom: 10px;
}

.select_box {
	border:0px solid #c7c7c7 !important;
	width:100% !important;
	height:30px !important;
	line-height:14px !important;
	color:#908e8e !important;
	padding-left:5px !important;
	padding-right:5px !important;
	margin-bottom:5px !important;
}
.select_box:disabled{background-color: #eaeaea !important;}

.text_box:disabled{background-color: #eaeaea !important;}


.custom-checkbox{
        width: 16px;
        height: 16px;
        display: inline-block;
        position: relative;
        z-index: 1;
        top: 3px;
        background: url("checkbox.png") no-repeat;
}
.custom-checkbox:hover{
        background: url("checkbox-hover.png") no-repeat;
}
.custom-checkbox.selected{
        background: url("checkbox-selected.png") no-repeat;
}
.custom-checkbox input[type="checkbox"]{
        margin: 0;
        position: absolute;
        z-index: 2;
        cursor: pointer;
        outline: none;
        opacity: 0;
        /* CSS hacks for older browsers */
        _noFocusLine: expression(this.hideFocus=true);
        -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
        filter: alpha(opacity=0);
        -khtml-opacity: 0;
        -moz-opacity: 0;
}

.file_upload_button{ background-color:#ef8905; font-size:14px; font-weight:400; color:#fff; display:inline-block; padding:8px 8px; border:none; cursor:pointer;  margin-top:0px; }
.file_upload_button:hover { background-color:#7b7a79; color:#fff; }
.file_upload_cancel{ background-color:#39658b; font-size:14px; font-weight:400; color:#fff; display:inline-block; padding:8px 8px; border:none; cursor:pointer;  margin-top:0px; }
.file_upload_cancel:hover { background-color:#7b7a79; color:#fff; }

/* Invoice Form Css */

.invoice_title_txt {
	font-family: 'open_sansregular' !important;
	font-size:12px !important;
	color:#393836 !important;
	display:block !important;
	margin-bottom:5px !important;
}

.invoice_txt_box {
	border:1px solid #c7c7c7 !important;
	width:100% !important;
	height:30px !important;
	line-height:30px !important;
	color:#908e8e !important;
	padding-left:5px !important;
	padding-right:5px !important;
	margin-bottom:5px !important;
}
.invoice_txt_box:disabled{ background-color: #eaeaea;}

.remove-padding-right {
	padding-right: 0;
}


.span-invoice-total  {
	text-align: right;
	display: inline-block !important;
	margin-right: 10px;
}

/*#invoice_materials_table input[type="text"] {
	text-align: right;
}*/

#invoice_materials_table input.text-center[type="text"] {
    text-align: center;
}

#invoice_materials_table input.text-left[type="text"] {
	text-align: left;
}

#invoice_materials_table input.text-right[type="text"] {
	text-align: right;
}

/* Non excise - css */
.div_non_excise_category span {
	padding-right: 25px;
}

.rdo_inv_excise_type span {
    margin: 0 20px 20px 0;
    display: inline-block;
}

.rdo_inv_excise_type label {
	padding-left: 5px;
}

.voucher_add_container.resp-tab-content-active {
        display: inline-block !important;
        width: 100% !important;
}

.ui-autocomplete {
	max-height: 250px;
	overflow-y: scroll;
}
.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.switch input {display:none;}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: .2s;
  transition: .2s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  -webkit-transition: .2s;
  transition: .2s;
}

input:checked + .slider {
  background-color: #209be1;
}

input:focus + .slider {
  box-shadow: 0 0 1px #4CAF50;
}

input:checked + .slider:before {
  -webkit-transform: translateX(22px);
  -ms-transform: translateX(22px);
  transform: translateX(20px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}