import simplejson
from django.http import HttpResponseRedirect, HttpResponse
from django.template.response import TemplateResponse

from erp import properties, dao
from erp.auth import USER_IN_SESSION_KEY, ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from erp.forms import PayStructureForm
from erp.formsets import PayStructureDetailsFormset
from erp.hr import logger
from erp.models import PayStructure, PayStructureItem, Employee
from erp.properties import MANAGE_PAY_TEMPLATE, TEMPLATE_TITLE_KEY
from settings import SQLASession
from util.api_util import response_code

__author__ = 'saravanan'


def managePay(request):
	"""
	Renders the main page to manage pay structure.
	:param request:
	:return:
	"""
	logger.info('Inside Manage Pay Structure... ')
	rh = RequestHandler(request)
	enterprise_id = rh.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	pay_structure_formset = PayStructureDetailsFormset(prefix="pay_structure")
	pay_structure_forms = SQLASession().query(PayStructure).filter(PayStructure.enterprise_id == enterprise_id).all()
	pay_form = PayStructureForm(prefix="pay")
	response_message = rh.getSessionAttribute("response_message")
	rh.setSessionAttribute("response_message", "")
	if not response_message:
		response_message = ""
	return TemplateResponse(template=MANAGE_PAY_TEMPLATE, request=request, context={
		'paystructureForms': pay_structure_forms, 'payForm': pay_form,
		'pay_structure_formset': pay_structure_formset, 'response_message': response_message,
		TEMPLATE_TITLE_KEY: "Pay Structure"})


def savePay(request):
	"""
	Save pay structure
	Persists them in appropriate tables after validation.

	:param request:
	:return:
	"""
	logger.info('Inside Save Pay Structure...')
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	db_session = SQLASession()
	db_session.begin(subtransactions=True)
	try:
		pay_form = PayStructureForm(data=request.POST, prefix="pay")
		pay_item_formset = PayStructureDetailsFormset(request.POST, prefix="pay_structure")
		if pay_form.is_valid() and pay_item_formset.is_valid():
			description = pay_form.cleaned_data['description'].strip()
			pay_id = pay_form.cleaned_data['id']
			pay_to_be_saved = db_session.query(PayStructure).filter(
				PayStructure.id == pay_id, PayStructure.enterprise_id == enterprise_id).first()
			if pay_to_be_saved:
				pay_to_be_saved.description = description
			else:
				pay_to_be_saved = PayStructure(description=description, enterprise_id=enterprise_id)
			db_session.add(pay_to_be_saved)

			items = []
			for pay_item_form in pay_item_formset:
				if pay_item_form.cleaned_data['DELETE']:
					continue
				pay_structure_item = db_session.query(PayStructureItem).filter(
					PayStructureItem.description == pay_item_form.cleaned_data['description'].strip(),
					PayStructureItem.pay_structure_id == pay_to_be_saved.id,
					PayStructureItem.type == pay_item_form.cleaned_data['type'].strip(),
					PayStructureItem.enterprise_id == pay_to_be_saved.enterprise_id).first()
				if not pay_structure_item:
					pay_structure_item = _getEntityFromPayStructureDetailForm(pay_item_form, pay_form)
				items.append(pay_structure_item)
			pay_to_be_saved.items = items
			logger.info('Pay structure: Committing all transactions...')
			db_session.commit()
			message = "Pay Structure has saved successfully!"
		else:
			logger.info('Pay Structure Form errors: %s\nPay Structure Formset Errors: %s' % (
				pay_form.errors, pay_item_formset.errors))
			message = "Failed saving Pay Structure!"
			db_session.rollback()
	except Exception as e:
		db_session.rollback()
		logger.exception(e)
		raise
	request_handler.setSessionAttribute("response_message", message)
	return HttpResponseRedirect(properties.MANAGE_PAY_URL)


def editPay(request):
	logger.info('Editing a Pay Structure...')
	if request.method == 'POST':
		pay_code = request.POST['code']
		enterprise_id = request.session[ENTERPRISE_ID_SESSION_KEY]
		pay_under_edit = SQLASession().query(PayStructure).filter(
			PayStructure.id == pay_code, PayStructure.enterprise_id == enterprise_id).first()
		pay_form = PayStructureForm(initial=_getPayFormInitializerFromEntity(pay_under_edit), prefix="pay")
		pay_structure_items = SQLASession().query(PayStructureItem).filter(
			PayStructureItem.pay_structure_id == pay_code,
			PayStructureItem.enterprise_id == enterprise_id).all()
		logger.info(len(pay_structure_items))
		pay_structure_item_forms = []
		for pay_structure_item in pay_structure_items:
			pay_structure_item_forms.append(_getPayStructureDetailFormInitializerFromEntity(pay_structure_item))
		pay_structure_formset = PayStructureDetailsFormset(initial=pay_structure_item_forms, prefix="pay_structure")

		pay_structure_forms = SQLASession().query(PayStructure).filter(
			PayStructure.enterprise_id == enterprise_id).all()
		edit_flag = "edit"

		return TemplateResponse(template=MANAGE_PAY_TEMPLATE, request=request, context={
			'paystructureForms': pay_structure_forms, 'payForm': pay_form,
			'pay_structure_formset': pay_structure_formset, 'edit': edit_flag, TEMPLATE_TITLE_KEY: pay_under_edit})
	else:
		HttpResponseRedirect(properties.MANAGE_PAY_URL)


def deletePay(request):
	"""
	Soft Delete of pay structure - setting the status to 0
	:param request:
	:return:
	"""
	logger.info('Deleting a Pay Structure...')
	db_session = SQLASession()
	enterprise_id = request.session[ENTERPRISE_ID_SESSION_KEY]
	try:
		db_session.begin(subtransactions=True)
		db_session.query(PayStructure).filter(
			PayStructure.id == request.POST['deleteCode'], PayStructure.enterprise_id == enterprise_id).delete()
		db_session.commit()
	except Exception as e:
		db_session.rollback()
		logger.exception(e)
	return HttpResponseRedirect(properties.MANAGE_PAY_URL)


def checkPay(request):
	"""
	Check pay structure are using any employee
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	pay_structure_id = request_handler.getPostData("pay_structure_id")
	pay_count = 0
	try:
		pay_count = SQLASession().query(Employee).filter(
			Employee.pay_structure_id == pay_structure_id, Employee.enterprise_id == enterprise_id).all()
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(len(pay_count)), mimetype='application/json')


def isValidPayStructureDescription(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		pay_count_query = "Select count(1) from pay_structure where enterprise_id='%s' and description = '%s'" % (
			enterprise_id, rh.getPostData("description"))
		number_count = dao.executeQuery(pay_count_query)
		response = response_code.success()
		response["is_valid"] = number_count[0][0] == 0
	except Exception as e:
		logger.exception("Failed validating pay structure name.\n" % e.message)
		response = response_code.internalError()
		response['custom_message'] = "Could not create pay structure due to internal server error! Please contact admin."
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def isValidPayStructureItem(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		pay_count_query = """SELECT type, description FROM pay_structure_items 
			WHERE enterprise_id='%s' and description='%s' LIMIT 1""" % (enterprise_id, rh.getPostData("description"))
		items = dao.executeQuery(pay_count_query, as_dict=True)
		response = response_code.success()
		response["is_valid"] = len(items) == 0 or items[0]["type"] == rh.getPostData("item_type")
	except Exception as e:
		response = response_code.internalError()
		response["custom_message"] = "Failed validating pay structure item. Please try later"
		logger.exception("Failed validating pay structure item for enterprise %s.\n%s" % (enterprise_id, e.message))
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def _getPayFormInitializerFromEntity(entity):
	return {
		'id': entity.id,
		'description': entity.description,
		'enterprise_id': entity.enterprise_id}


def _getPayStructureDetailFormInitializerFromEntity(entity):
	return {
		'pay_structure_id': entity.pay_structure_id,
		'description': entity.description,
		'type': entity.type,
		'enterprise_id': entity.enterprise_id
	}


def _getEntityFromPayStructureDetailForm(pay_structure_form, pay_form):
	pay_structure_detail_entity = PayStructureItem(
		pay_structure_id=pay_form.cleaned_data['id'],
		type=pay_structure_form.cleaned_data['type'].strip(),
		description=pay_structure_form.cleaned_data['description'].strip(),
		enterprise_id=pay_form.cleaned_data['enterprise_id'])
	return pay_structure_detail_entity
