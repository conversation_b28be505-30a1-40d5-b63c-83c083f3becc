<!DOCTYPE html>
<style type="text/css">
	body{
		color: #000;
		font-family: 'Times New Roman';
	}
	td {
		text-align: left;
		line-height: 25px;
		font-size: 11pt !important;
		padding:5px !important;
	}
	th{
		font-size:12pt;
		text-align:center !important;
		padding: 0px 5px !important;
	}
	.enterprise_details{
		font-size:10.5pt;
	}
	.enterprise_name{
		font-size:13pt
	}
	.page_title{
		font-size: 15pt;
	}
	.mrs_details{
		width: 105px;
	    font-weight: bold;
	    font-size: 12pt;
	    display: inline-table;
	    word-break: break-all;
	    margin-left: 60px;
	}
	.mrs_data{
		width: 180px;
		font-size: 12pt;
		display: inline-table;
		vertical-align:top;
		word-break: break-all;
	}
	.register_data {
	    width: calc(100% - 100px);
	    float: left;
	    font-size:10.5pt;
	    margin-left:26px;
	}
	.register_details {
	    float: left;
	    margin-left: 26px;
	    font-size:10.5pt;
	}
	.vendor_data{
		width: 180px;
		font-size: 12pt;
		display: inline-table;
		vertical-align:top;
		word-break: break-all;
	}
	.vendor_details {
		width: 115px;
	    font-weight: bold;
	    font-size: 12pt;
	    display: inline-table;
	    word-break: break-all;
	}
	@font-face {
		font-family: 'Times New Roman';
		font-style: normal;
		font-weight: 400;
		src: url(https://www.xserp.in/site_media/fonts/times.ttf) format('truetype');
	}
</style>


<body>
<div class="container">
	<div>
		<div class="col-sm-8" style="padding: 0;">
			<img src="{{enterprise_logo}}" style="max-height: 10mm">
			<div class="enterprise_name">{{ source.enterprise.name }}</div>
			<div class="enterprise_details"> {{ enterprise_address }}</div>
			<div class="enterprise_details"><b>Ph:</b>{% if source.enterprise.primary_contact_details.contact.phone_no %} {{ source.enterprise.primary_contact_details.contact.phone_no }} {% endif %}<b>E-mail:</b>{% if source.enterprise.primary_contact_details.contact.email %} {{ source.enterprise.primary_contact_details.contact.email }} {% endif %}</div>
		</div>

		<div class="col-sm-4">
			<span class="page_title" ><b>{{ form_name }}</b></span>
		</div>
	</div>

	<div>
		<div class="col-sm-7" style="padding: 0;"><br>
			<div class="vendor_details">Requisition By</div>
			<div class="vendor_data">: {{ requisition_by }}</div><br/>
			<div class="vendor_details"><b>Requisition For</b></div>
			<div class="vendor_data">: {{ requisition_for }} </div>
		</div>
		<div class="col-sm-5"><br>
			<div class="mrs_details">MRS No.</div>
			<div class="mrs_data"><b>:</b> {{ source.getMRSCode }} </div>
			<div class="mrs_details">MRS Date</div>
			<div class="mrs_data"><b>:</b> {{ prepared_on }}</div>
		</div>
	</div>


	<table border=1 bordercolor="#A9A9A9" class="row-seperator column-seperator" style="width:100% !important;">
		<thead>
			<tr style="height:40px; border-top: solid 1px #000; border-bottom: solid 1px #000;">
				<th>S.No</th>
				<th>Material</th>
				<th>Quantity</th>
				<th>Unit</th>
			</tr>
		</thead>
		<tbody>
			{% for item in mrs_materials %}
				<tr>
					<td style="text-align:center; width:7%">{{ forloop.counter }}. </td>
					<td style="width:65%">{{ item.item_description }}</td>
					<td class='text-right' style="width:15%">{{ item.material_quantity }}</td>
					<td class='text-center' style="width:13%">{{ item.material_unit }}</td>
				</tr>
			{% endfor %}
		</tbody>
	</table>
</div>

<br>
</body>
<script>
</script>