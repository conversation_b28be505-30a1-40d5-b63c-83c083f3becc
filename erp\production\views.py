"""

"""
import json

import simplejson
from django.http import HttpResponse
from django.template.response import TemplateResponse
from erp import properties, helper, logger
from erp.auth import USER_IN_SESSION_KEY, ENTERPRISE_ID_SESSION_KEY, SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.masters.backend import MasterService
from erp.production.backend import ProductionService
from erp.properties import TEMPLATE_TITLE_KEY

MATERIAL_FORM_KEY = 'material_form'
BILL_OF_MATERIALS_FORMSET_KEY = 'bill_of_materials_formset'
SPECIFICATION_FORMSET_KEY = 'specifications_formset'
PRICE_OF_SUPP_MATERIALS_FORMSET_KEY = 'materials_party_price_formset'
MATERIAL_MAKE_FORMSET_KEY = 'material_makes_formset'
ALTERNATE_UNIT_FORMSET_KEY = 'alternate_unit_formset'

__author__ = 'saravanan'

from util.api_util import JsonU<PERSON>, response_code


def manageManufactureIndents(request):
	"""
	Renders the view to manage indents.

	:param request: a Http-URL-request asking for Manufacture Indent Webservice
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	master_service = MasterService()
	material_vo = master_service.constructMaterialVO(enterprise_id=enterprise_id)
	customer_list = helper.populatePartyChoices(
		enterprise_id=enterprise_id, populate_all=True, is_customer=True, is_supplier=True)

	return TemplateResponse(template=properties.MANAGE_MANUFACTURE_INDENT_TEMPLATES, request=request, context={
		TEMPLATE_TITLE_KEY: "Manufacturing Indent", 'customers': customer_list, MATERIAL_FORM_KEY:  material_vo.material_form,
		BILL_OF_MATERIALS_FORMSET_KEY: material_vo.material_formset,
		SPECIFICATION_FORMSET_KEY: material_vo.specification_formset,
		PRICE_OF_SUPP_MATERIALS_FORMSET_KEY: material_vo.supplier_price_material_formset,
		MATERIAL_MAKE_FORMSET_KEY: material_vo.material_make_formset,
		ALTERNATE_UNIT_FORMSET_KEY: material_vo.alternate_unit_formset,
	})


def manageProductionPlan(request):
	"""
	Renders the view to manage indents.

	:param request: a Http-URL-request asking for Production Plan Webservice
	"""
	return TemplateResponse(template=properties.MANAGE_PRODUCTION_PLAN_TEMPLATES, request=request, context={
		TEMPLATE_TITLE_KEY: "Production Plan"
	})


def getIssueTo(request):
	"""

	:param request: a Http-URL-request asking for get Issue To details
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		issue_to = helper.listUniqueEntriesFromDB(
				enterprise_id=enterprise_id, table='invoice', column='issued_to')
		issue_to.extend(helper.listUniqueEntriesFromDB(
				enterprise_id=enterprise_id, table='purchase_order', column='issue_to'))
		party_choices = helper.populatePartyChoices(
			enterprise_id=enterprise_id, populate_all=False, is_supplier=True, is_customer=True, frequents_condition="")
		response = response_code.success()
		response['issue_to'] = [issue_to, party_choices]
	except Exception as e:
		logger.exception(e)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getOADetails(request):
	"""
	Renders the view to manage indents.

	:param request: a Http-URL-request asking for Manufacture Indent Webservice
	"""
	try:
		production_service = ProductionService()
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		from_date, to_date = JsonUtil.getDateRange(rh=request_handler)
		party = request_handler.getPostData('party')
		mi_not_applicable = request_handler.getPostData('mi_not_applicable')
		oa_details = production_service.getOADetails(
			enterprise_id=enterprise_id, from_date=from_date, to_date=to_date, party=party, mi_not_applicable=mi_not_applicable)
		response = response_code.success()
		response['oa_details'] = oa_details
	except Exception as e:
		logger.exception(e)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getProductionPlanList(request):
	"""
	Renders the view to manage indents.

	:param request: a Http-URL-request asking for Production Plan Webservice
	"""
	try:
		production_service = ProductionService()
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		from_date, to_date = JsonUtil.getDateRange(rh=request_handler)
		status = request_handler.getPostData('status')
		timeliness = request_handler.getPostData('timeliness')
		assigned_to = request_handler.getPostData('assigned_to')
		pp_type = request_handler.getPostData('pp_type')
		production_plan = production_service.getProductionPlanList(
			enterprise_id=enterprise_id, from_date=from_date, to_date=to_date, status=status,
			timeliness=timeliness, assigned_to=assigned_to, pp_type=pp_type)
		response = response_code.success()
		response['production_plan'] = production_plan
	except Exception as e:
		logger.exception(e)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getManufactureIndents(request):
	"""
	Renders the view to manage indents.

	:param request: a Http-URL-request asking for Production Plan Webservice
	"""
	try:
		production_service = ProductionService()
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		manufacture_indents = production_service.getManufactureIndentList(enterprise_id=enterprise_id)
		response = response_code.success()
		response['manufacture_indents'] = manufacture_indents
	except Exception as e:
		logger.exception(e)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getMIMaterial(request):
	"""
	Renders the view to manage indents.

	:param request: a Http-URL-request asking for Manufacture Indent Webservice
	"""
	try:
		production_service = ProductionService()
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		mi_id = request_handler.getPostData('mi_id')
		response = response_code.success()
		response['mi_material'] = ""
		if mi_id and mi_id is not 'null':
			mi_material = production_service.getMIMaterial(enterprise_id=enterprise_id, mi_id=mi_id)
			response['mi_material'] = mi_material

	except Exception as e:
		logger.exception(e)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getProductionLog(request):
	"""
	Renders the view to manage indents.

	:param request: a Http-URL-request asking for Production Log Webservice
	"""
	try:
		production_service = ProductionService()
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		pp_id = request_handler.getPostData('pp_id')
		response = response_code.success()
		response['production_log_details'] = ""
		if pp_id and pp_id is not 'null':
			production_log_details = production_service.getProductionLog(enterprise_id=enterprise_id, pp_id=pp_id)
			response['production_log_details'] = production_log_details

	except Exception as e:
		logger.exception(e)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getProductionPlan(request):
	"""
	Renders the view to manage indents.

	:param request: a Http-URL-request asking for Production Log Webservice
	"""
	try:
		production_service = ProductionService()
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		pp_id = request_handler.getPostData('pp_id')
		response = response_code.success()
		response['production_plan_details'] = ""
		if pp_id and pp_id is not 'null':
			production_plan_details = production_service.getProductionPlanStatus(enterprise_id=enterprise_id, pp_id=pp_id)
			response['production_plan_details'] = production_plan_details

	except Exception as e:
		logger.exception(e)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def saveManufactureIndent(request):
	"""

	"""
	try:
		production_service = ProductionService()
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		indent_dict = json.loads(request.POST.get('indent_dict'))
		mi_details = production_service.saveManufactureIndent(
			indent=indent_dict[0], user_id=user_id, enterprise_id=enterprise_id)
		response = response_code.success()
		response['mi_details'] = mi_details

	except Exception as e:
		logger.exception(e)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def removeManufactureIndent(request):
	"""

	"""
	try:
		production_service = ProductionService()
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		indent_dict = json.loads(request.POST.get('indent_dict'))
		mi_details = production_service.removeManufactureIndent(
			indent=indent_dict[0], enterprise_id=enterprise_id)
		response = response_code.success()
		response['mi_details'] = mi_details

	except Exception as e:
		logger.exception(e)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def saveProductionPlan(request):
	"""

	"""
	try:
		production_service = ProductionService()
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		production_plan_dict = json.loads(request.POST.get('production_plan'))
		production_plan_details = production_service.saveProductionPlan(
			production_plan=production_plan_dict[0], user_id=user_id, enterprise_id=enterprise_id)
		response = response_code.success()
		response['production_plan_details'] = production_plan_details
	except Exception as e:
		logger.exception(e)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def saveProductionLog(request):
	"""
	Renders the view to manage indents.

	:param request: a Http-URL-request asking for Save Production Log Webservice
	"""
	try:
		production_service = ProductionService()
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		user = request_handler.getSessionAttribute(USER_IN_SESSION_KEY)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		production_log_dict = json.loads(request.POST.get('production_plan'))
		logger.info("production_log_dict:%s" % production_log_dict)
		production_log_details = production_service.saveProductionLog(
			production_log=production_log_dict, enterprise_id=enterprise_id, user=user, user_id=user_id)
		response = response_code.success()
		response['production_log_details'] = production_log_details

	except Exception as e:
		logger.exception(e)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def shortCloseProductionPlan(request):
	"""

	"""
	try:
		production_service = ProductionService()
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		pp_dict = json.loads(request.POST.get('pp_dict'))
		pp_details = production_service.shortCloseProductionPlan(
			production_plan=pp_dict[0], enterprise_id=enterprise_id, user_id=user_id)
		response = response_code.success()
		response['pp_details'] = pp_details

	except Exception as e:
		logger.exception(e)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def remarksProductionPlan(request):
	"""

	"""
	try:
		production_service = ProductionService()
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		pp_dict = json.loads(request.POST.get('pp_dict'))
		remarks = production_service.remarksProductionPlan(
			production_plan=pp_dict[0], enterprise_id=enterprise_id, user_id=user_id)
		response = response_code.success()
		response['remarks'] = remarks

	except Exception as e:
		logger.exception(e)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def instructionProductionPlan(request):
	"""

	"""
	try:
		production_service = ProductionService()
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		pp_dict = json.loads(request.POST.get('pp_dict'))
		instruction = production_service.instructionProductionPlan(
			production_plan=pp_dict[0], enterprise_id=enterprise_id)
		response = response_code.success()
		response['instruction'] = instruction

	except Exception as e:
		logger.exception(e)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def remarksManufactureIndent(request):
	"""

	"""
	try:
		production_service = ProductionService()
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		user = request_handler.getSessionAttribute(USER_IN_SESSION_KEY)
		mi_dict = json.loads(request.POST.get('mi_dict'))
		remarks = production_service.remarksManufactureIndent(
			indent_dict=mi_dict[0], enterprise_id=enterprise_id, user=user)
		response = response_code.success()
		response['remarks'] = remarks

	except Exception as e:
		logger.exception(e)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def allocated_material_quantity(request):
	production_service = ProductionService()
	request_handler = RequestHandler(request)
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	allocated_materials = request_handler.getPostData('allocated_materials')
	enterprise_id = int(request_handler.getPostData('enterprise_id'))
	status = request_handler.getPostData('status')
	return_response = production_service.allocation_deallocation(allocated_materials=allocated_materials,
																 user_id=user_id, enterprise_id=enterprise_id, status=status)
	if return_response['status'] == 'success':
		response = response_code.success()
	else:
		response = response_code.failure()
		logger.error("Failure in allocation or deallocation: %s", return_response)

	return HttpResponse(content=simplejson.dumps(response), content_type='application/json')