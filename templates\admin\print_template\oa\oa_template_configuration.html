{% extends 'admin/sidebar.html' %}
{% block oa_template %}
<script type="text/javascript" src="/site_media/js/bootstrap-input-spinner.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/jquery.slimscroll.js?v={{ current_version }}"></script>
<link type="text/css" rel="stylesheet" href="/site_media/css/oa_template_preview.css">
<link type="text/css" rel="stylesheet" href="/site_media/plugin/image_cropper/css/imgareaselect-default.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/plugin/image_cropper/css/jquery.awesome-cropper.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/plugin/ck_editor/js/ckeditor.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/plugin/ck_editor/js/ckeditor_other.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/plugin/image_cropper/js/jquery.imgareaselect.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/plugin/image_cropper/js/jquery.awesome-cropper.js?v={{ current_version }}"></script>
<style>
	body {
		overflow-y: scroll;
	}
	.div_tax_types label{
		width: 100%;
	}
	.oa_template_editor select.form-control {
		padding: 0;
	}
	#id_oath-name_font {
		width: auto;
		margin-bottom: 4px;
	}
	.bill_to_text {
		width: 100%;
		display: block;
    	margin-bottom: 4px;
    	padding: 2px;
	}
	.oa_pdf_editor {
        width: 210mm;
        padding: 2mm;
        float: left;
        min-height: 257px;
        margin-left: 3px;
        margin-bottom: 10px;
        box-shadow: 0 0 3px #000;
        border-radius: 3px;
        line-height: 1.2;
	}
	.banner-image {
		width: 100px;
		height: 100px;
		border: dashed 1px #ccc;
		padding: 0;
		margin-right: 7px;
		margin-top: 8px;
	}
	.banner-image .uploader {
	    margin-top: 25px;
	    display: inline-block;
	    text-align: center;
	}
	.banner-container {
		padding: 0;
	}
	.banner-image.image-uploaded {
		border-style: solid;
		padding: 0;
	}
	.banner-image.image-uploaded:hover .uploaded-banner-image {
		opacity: 0.3;
	}
	.banner-image.image-uploaded:hover .uploader {
		display: inline-block !important;
	    position: absolute;
	    margin-left: 12px;
	    color: #000;
	}
	.banner-image .uploaded-banner-image {
		width: 100%;
		height: 100%;
		margin: 0;
		padding: 3px;
    	border-radius: 6px;
	}
	.uploaded-banner-image {
		width:100px;
		height: 100px;
		margin-top: 10px;
	}
	.remove-banner-image {
		position: absolute;
	    left: 90px;
	    top: -1px;
	    font-size: 13px;
	    color: red;
	    border: solid 1px red;
	    border-radius: 50px;
	    padding: 1px 2px 1px 3px;
	}
	.enterprise_total_details label {
         width: 124px;
        margin-bottom: 5px;
        float: left;
        font-size: 10px;
    }
    .progress + .cropped_image {
        display: none;
	}
</style>
<div class="right-content-container">
	<div class="container" style="border: solid 1px #ccc; padding-right: 0">
		<h2 style="color: #113344; margin: 15px 0;">
			<span class="page_header_grn" style="margin-bottom: 15px;">Order Acknowledgement Template
				<small class="last-modified-user-container">Last modified by <b id="last-modified-user" style="font-size: 12px;"> {{modified_by.first_name}} {{modified_by.last_name}}</b> on <b>{{modified_on}}</b></small>
			</span>
			{% if logged_in_user|canApprove:'SALES' %}
			<input type="button" class="btn btn-save pull-right" style="margin-left: 8px; margin-right: 15px;" value="Save" onclick='saveTemplateChanges()'/>
			{% else %}
			<input type="button" class="btn btn-save pull-right disabled" data-tooltip="tooltip" title="You do not have adequate permission to modify the Order Acknowledgement Template" style="margin-left: 8px; margin-right: 15px; background: #999" value="Save" />
			{% endif %}
			<input type="button" class="btn btn-warning pull-right" id="pdf_preview" value="PDF Preview" onclick='previewTemplateChanges("/erp/admin/oa_template/preview/")' />
		</h2>
		<div class="clearfix"></div>
		<div class="oa_template_container row" style="width: 100%;">
			<div class="oa_template_side_bar">
				<ul class="nav nav-stacked nav_order_acknowledgement_template">
					<li data-field="select_appearence" data-tab="1" class="active">General</li>
					<li data-field="select_header" data-tab="2">Header</li>
					<li data-field="select_table" data-tab="3">Item Table</li>
					<li data-field="select_total" data-tab="4">Totals</li>
					<li data-field="select_misc" data-tab="5">Misc</li>
				</ul>
			</div>
			<div class="oa_template_editor">
				<div class="oa_template_edit select_appearence">
					<div class="form-horizontal">
						<div class="form-group">
							<label class="control-label col-sm-5">Base Font</label>
							<div class="col-sm-6">
								<select class="form-control chosen-select" id="id_oatg-base_font" name="oatg-base_font">
									{% for font in base_fonts %}
									<option value="{{ font.font_value }}" {% if font.font_value|upper == general_res.font_family|upper %} selected {% endif %}>{{ font.font_name }}</option>
									{% endfor %}
								</select>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-sm-5">Page Margin</label>
							<div class="col-sm-7 document-margin">
								<div class="col-sm-12 remove-padding">
									<label class="spinner-label">TOP</label>
									<input class="input_spinner" id="id_oatg-margin_top" max="100" min="0" name="oatg-margin_top" spinner_for="page_margin_top" step="1" type="number" value="{{ general_res.margin.top }}" style="display: none;">
									<span class="doc-margin-prefix"> mm</span>
								</div>
								<div class="col-sm-12 remove-padding">
									<label class="spinner-label">LEFT</label>
									<input class="input_spinner" id="id_oatg-margin_left" max="100" min="0" name="oatg-margin_left" spinner_for="page_margin_left" step="1" type="number" value="{{ general_res.margin.left }}" style="display: none;">
									<span class="doc-margin-prefix"> mm</span>
								</div>
								<div class="col-sm-12 remove-padding">
									<label class="spinner-label">BOTTOM</label>
									<input class="input_spinner" id="id_oatg-margin_bottom" max="100" min="0" name="oatg-margin_bottom" spinner_for="page_margin_bottom" step="1" type="number" value="{{ general_res.margin.bottom }}" style="display: none;">
									<span class="doc-margin-prefix"> mm</span>
								</div>
								<div class="col-sm-12 remove-padding">
									<label class="spinner-label">RIGHT</label>
									<input class="input_spinner" id="id_oatg-margin_right" max="100" min="0" name="oatg-margin_right" spinner_for="page_margin_right" step="1" type="number" value="{{ general_res.margin.right }}" style="display: none;">
									<span class="doc-margin-prefix"> mm</span>
								</div>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-sm-5">Page Size</label>
							<div class="col-sm-6" style="margin-top: 9px;">
								A4 (210mm x 297mm) <i class="fa fa-info-circle" style="color: #666;" aria-hidden="true" data-tooltip="tooltip" title="" data-original-title="Other page size will be available in future release."></i>
							</div>
						</div>
						<div class="form-group">
							<label class="date-format-label" style="margin-left: 15px;">Date Format</label>
							<br />
							<div class="container" style="width:327px;border: 1px solid #ccc;">
								<table id="dateFormatter" class="form-group" style="max-width: 383px; float: left;display: block;width:319px;padding-top: 10px;padding-left: 14px;">
									<tr>
										<td style="padding-right: 10px;">
											<label style="cursor: move;"><i style='font-size: 14px;' class="fa fa-bars" aria-hidden="true"></i> Date</label>
											<select class="form-control date-formatter date-formatter-date">
												<option value="" data-value="">-</option>
												<option value="%d" data-value="D">D</option>
											</select>
										</td>
										<td style="padding-right: 10px;">
											<label></label>
											<select class="form-control date-formatter date-formatter-seperator1">
												<option value=" " data-value=" "> </option>
												<option value="/" data-value="/">/</option>
												<option value="-" data-value="-">-</option>
												<option value="." data-value=".">.</option>
												<option value=", " data-value=", ">,</option>
											</select>
										</td>
										<td style="padding-right: 10px;">
											<label style="cursor: move;"><i style='font-size: 14px;' class="fa fa-bars" aria-hidden="true"></i> Month</label>
											<select class="form-control date-formatter date-formatter-month">
												<option value="" data-value="">-</option>
												<option value="%m" data-value="MM">MM</option>
												<option value="%b" data-value="MMM">MMM</option>
												<option value="%B" data-value="MMMM">MMMM</option>
											</select>
										</td>
										<td style="padding-right: 10px;">
											<label></label>
											<select class="form-control date-formatter date-formatter-seperator2">
												<option value=" " data-value=" "> </option>
												<option value="/" data-value="/">/</option>
												<option value="-" data-value="-">-</option>
												<option value="." data-value=".">.</option>
												<option value=", " data-value=", ">,</option>
											</select>
										</td>
										<td style="padding-right: 10px;">
											<label style="cursor: move;"><i style='font-size: 14px;' class="fa fa-bars" aria-hidden="true"></i> Year</label>
											<select class="form-control date-formatter date-formatter-year">
												<option value="" data-value="">-</option>
												<option value="%y" data-value="YY">YY</option>
												<option value="%Y" data-value="YYYY">YYYY</option>
											</select>
										</td>
									</tr>
								</table>

								<table id="timeFormatter" style="margin-bottom: 10px;">
									<tr>
										<td style="padding-right:9px;">
											<label>Hour</label>
											<select class="form-control time-formatter time-formatter-hour" style="width: 60px;">
												<option value="" data-value="">-</option>
												<option value=" %H" data-value="HH">HH</option>
												<option value=" %I" data-value="hh">hh</option>
											</select>
										</td>
										<td style="padding-right:5px;">
											<label>Minutes</label>
											<select class="form-control time-formatter time-formatter-minute" style="width: 60px;">
												<option value="" data-value="">-</option>
												<option value=":%M" data-value="mm">mm</option>
											</select>
										</td>
										<td style="padding-left:7px;">
											<label>Seconds</label>
											<select class="form-control time-formatter time-formatter-second" style="width: 60px;";>
												<option value="" data-value="">-</option>
												<option value=":%S" data-value="ss">ss</option>
											</select>
										</td>
									</tr>
								</table>
								<span style="font-size: 10px;">* This date format will apply for all the dates in Order Acknowledgement document.</span>
							</div>
							<input id="id_oatg-oa_doc_datetime_format" name="oatg-oa_doc_datetime_format" type="hidden" value="{{general_res.datetime}}">
						</div>
					</div>
				</div>
				<div class="oa_template_edit select_header hide">
					<div class="form-horizontal">
						<div class="form-group form-group-small">
							<label class="control-label col-sm-3" style="color: #209be1; text-shadow: 0 0 #000;">Company</label>
							<div class="col-sm-12 checkbox">
								<input checkboxfor="pdf_company_logo" {% if header_res.company.logo.print %} checked="checked"  {% endif %}  class="checkbox required_checkbox" id="id_oath-include_logo" name="oath-include_logo" type="checkbox">
								<label for="id_oath-include_logo" style="float: left;">LOGO</label>
								<span class="slidecontainer" style="float: left; margin-left: 15px;">
										<input class="slider" id="id_oath-logo_size" max="100" min="0" name="oath-logo_size" step="1" type="range" value="{{header_res.company.logo.size}}">
									</span>
								<span id="logo_size_value" style="margin-left: 10px;">{{header_res.company.logo.size}}</span>
							</div>
						</div>
						<div class="form-group form-group-small" style="border-bottom: solid 1px #eee;">
							<div class=" checkbox">
								<input checkboxfor="pdf_company_name" {% if header_res.company.company_name.print %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_oath-include_name" name="oath-include_name" type="checkbox">
								<label for="id_oath-include_name">Name</label>
							</div>
							<div class="form-horizontal">
								<div class="col-sm-12" style="margin-left: 40px;">
									<label class="spinner-label" style="width: 100px; font-size: 10px;">FONT</label>
									<select class="form-control chosen-select" id="id_oath-name_font" name="oath-name_font" style="width: 53%;">
										{% for font in base_fonts %}
										<option value="{{ font.font_value }}" {% if font.font_value|upper == header_res.company.company_name.font_family|upper %} selected {% endif %}>{{ font.font_name }}</option>
										{% endfor %}
									</select>
									<div class="document-margin">
										<label class="spinner-label" style="width: 100px; font-size: 10px;">Font Size</label>
										<input class="input_spinner" id="id_oath-name_font_size" max="100" min="0" name="oath-name_font_size" spinner_for="pdf_company_name" step="1" type="number" value="{{header_res.company.company_name.font_size}}" style="display: none;">
										<span class="doc-margin-prefix"> px</span>
									</div>
								</div>
							</div>
						</div>
						<div class="form-group form-group-small">
							<label class="control-label col-sm-12" style="color: #209be1; text-shadow: 0 0 #000;">Form Name</label>
							<div class="form-horizontal">
								<div class="col-sm-12 document-margin">
									<label class="spinner-label" style="width: 100px;">Font Size</label>
									<input class="input_spinner" id="id_oath-form_name_font_size" max="100" min="0" name="oath-form_name_font_size" spinner_for="pdf_form_name" step="1" type="number" value="{{header_res.form_name.font_size}}" style="display: none;">
									<span class="doc-margin-prefix"> px</span>
								</div>
							</div>
						</div>
						<div class="form-group form-group-small form-medium-text" style="border-bottom: solid 1px #eee;">
							<div class="col-sm-12 enterprise_form_details" style="margin-top: 10px; padding-left: 30px;">
								<div class="form-group" style="margin-bottom: 12px;">
									<label style="width:175px !important">Order Acknowledgement</label>
									<input class="form-control form-alternate-text" id="id_oath-order_acknowledgement_label" maxlength="30" name="oath-gst_label" textboxfor="pdf_form_name_text" type="text" value="{{header_res.form_name.label.oa}}">
								</div>
							</div>
						</div>
						<div class="form-group form-group-small">
							<div class="form-horizontal">
								<div class="col-sm-12 document-margin">
									<label class="spinner-label" style="width: 100px;">Font Size</label>
									<input class="input_spinner" id="id_oath-font_size" max="100" min="0" name="oath-font_size" spinner_for="pdf_company_details" step="1" type="number" value="{{header_res.font_size}}" style="display: none;">
									<span class="doc-margin-prefix"> px</span>
								</div>
							</div>
							<div class="col-sm-12 checkbox enterprise_header_details">
								<div class="col-sm-12 remove-padding checkbox">
									<div class="col-sm-6 remove-padding">
										<input checkboxfor="pdf_company_address" {% if header_res.company_info.print_address %} checked="checked" {% endif %} class="required_checkbox" id="id_oath-include_address" name="oath-include_address" type="checkbox">
										<label for="id_oath-include_address">Address</label>
									</div>
									<div class="col-sm-6 remove-padding">
										<input checkboxfor="pdf_company_contact" {% if header_res.company_info.print_phone_no %} checked="checked" {% endif %} class="required_checkbox" id="id_oath-include_phone_no" name="oath-include_phone_no" type="checkbox">
										<label for="id_oath-include_phone_no">Phone</label>
									</div>
									<div class="col-sm-6 remove-padding">
										<input checkboxfor="pdf_company_email" {% if header_res.company_info.print_email %} checked="checked" {% endif %} class="required_checkbox" id="id_oath-include_email" name="oath-include_email" type="checkbox">
										<label for="id_oath-include_email">Email</label>
									</div>
									<div class="col-sm-6 remove-padding">
										<input checkboxfor="pdf_company_fax" {% if header_res.company_info.print_fax %} checked="checked" {% endif %} class="required_checkbox" id="id_oath-include_fax" name="oath-include_fax" type="checkbox">
										<label for="id_oath-include_fax">Fax</label>
									</div>
								</div>
								<div class="col-sm-12 remove-padding" style="margin-left: -34px; margin-bottom: 7px; letter-spacing: 0.8px;">
									<span class="control-label col-sm-12" style="color: #209be1; text-shadow: 0 0 #000;">REGISTRATION DETAILS</span>
								</div>
								<div class="col-sm-12 remove-padding checkbox registration_details">
									{% for item in enterprise_reg %}
									<div class="col-sm-6 remove-padding">
										<input checkboxfor="pdf_registration_{{ item.label_id }}" data-field-id="{{ item.label_id }}" class="required_checkbox" id="id_oath-include_in_order_acknowledgement_template_{{ item.label_id }}" name="oath-include_in_order_acknowledgement_template" type="checkbox"
										       {% for i in included_reg_items %} {% if item.label_id|add:"0" == i.label_id|add:"0" %} checked="checked" {% endif %} {% endfor %}>
										<label for="id_oath-include_in_order_acknowledgement_template_{{ item.label_id }}" style="width: 125px;">{{ item.label }}</label>
									</div>
									{% endfor %}
								</div>
								<div class="col-sm-12 remove-padding checkbox">
									<div class="form-group form-group-small form-medium-text">
										<div class="enterprise_details">
											<div class="checkbox form-group">
												<input checkboxfor="pdf_bill_to_address" {% if header_res.billing_address.print %} checked="checked" {% endif %}  class="checkbox required_checkbox"  style="margin-top:1px" id="id_oath-include_billingaddress" name="oath-include_billingaddress" type="checkbox">
												<label for="id_oath-include_billingaddress" style="width: 148px;margin-top:9px">Billing Address</label>
												<input class="form-control form-alternate-text" style="margin-top:1px" id="id_oath-billingaddress_label" maxlength="30" name="oath-billingaddress_label" textboxfor="bill_to_text" type="text" value="{{header_res.billing_address.label}}">
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="form-group form-medium-text" style="margin-bottom: 0;">
							<label class="control-label col-sm-12" style="color: #209be1; text-shadow: 0 0 #000;">Field Name</label>
						</div>
						<div class="form-group form-group-small form-medium-text">
							<label class="control-label col-sm-12"></label>
							<div class="col-sm-12 enterprise_details enterprise_details_packing_details">
								<div class="form-group" style="margin-bottom: 12px;padding-left:30px">
									<label>OA DATE</label>
									<input class="form-control form-alternate-text" id="id_oath-date_label" maxlength="30" name="oath-indent_date_label" textboxfor="pdf_oa_date_txt" type="text" value="{{ header_res.field_name.oa_date.label }}">
								</div>
                                <div class="checkbox form-group checkbox">
									<input checkboxfor="pdf_po_no" {% if header_res.field_name.po_no.print %} checked="checked" {% endif %}   class="required_checkbox" id="id_oath-include_po_no" name="oath-include_po_no" type="checkbox">
									<label for="id_oath-include_po_no">PO No. & Date</label>
									<input class="form-control form-alternate-text" id="id_oath-po_no_label" maxlength="30" name="oath-po_no_label" textboxfor="pdf_po_no_txt" type="text" value="{{ header_res.field_name.po_no.label }}">
								</div>
                                <div class="checkbox form-group checkbox">
									<input checkboxfor="pdf_estimation_date" {% if header_res.field_name.estimation_date.print %} checked="checked" {% endif %}   class="required_checkbox" id="id_oath-include_estimation_date" name="oath-include_estimation_date" type="checkbox">
									<label for="id_oath-include_estimation_date">Estimate No. & Date</label>
									<input class="form-control form-alternate-text" id="id_oath-estimation_date_label" maxlength="30" name="oath-estimation_date_label" textboxfor="pdf_estimation_date_txt" type="text" value="{{ header_res.field_name.estimation_date.label }}">
								</div>
								<div class="checkbox form-group checkbox">
									<input checkboxfor="pdf_payment_term"  {% if header_res.field_name.payment_terms.print %} checked="checked" {% endif %} class="required_checkbox" id="id_oath-include_paymentterms" name="oath-include_paymentterms" type="checkbox">
									<label for="id_oath-include_paymentterms">Payment Terms</label>
									<input class="form-control form-alternate-text" id="id_oath-paymentterms_label" maxlength="30" name="oath-paymentterms_label" textboxfor="pdf_payment_term_txt" type="text" value="{{ header_res.field_name.payment_terms.label }}">
								</div>
								<div class="checkbox form-group checkbox">
									<input checkboxfor="pdf_delivery_due_date" {% if header_res.field_name.delivery_due_date.print %} checked="checked" {% endif %}   class="required_checkbox" id="id_oath-include_delivery_due_date" name="oath-include_expiry_date" type="checkbox">
									<label for="id_oath-include_delivery_due_date">Delivery Due Date</label>
									<input class="form-control form-alternate-text" id="id_oath-delivery_due_label" maxlength="30" name="oath-paymentterms_label" textboxfor="pdf_delivery_due_date_txt" type="text" value="{{ header_res.field_name.delivery_due_date.label }}">
								</div>
								<div class="checkbox form-group checkbox">
									<input checkboxfor="oa_special_instruction"  {% if header_res.field_name.special_instructions.print %} checked="checked" {% endif %}  class="required_checkbox" id="id_oath-include_splinstructions" name="oath-include_splinstructions" type="checkbox">
									<label for="id_oath-include_splinstructions">Special Instruction</label>
                                        <input class="form-control form-alternate-text" id="id_oath-splinstructions_label" maxlength="30" name="oath-splinstructions_label" textboxfor="oa_special_instruction_txt" type="text" value="{{ header_res.field_name.special_instructions.label }}">
								</div>
								<div class="checkbox form-group checkbox">
									<input checkboxfor="pdf_auth_sign" {% if header_res.field_name.authorized_sign.print %} checked="checked" {% endif %} class="required_checkbox" id="id_oath-include_authorizedsign" name="oath-include_authorizedsign" type="checkbox">
									<label for="id_oath-include_authorizedsign"> AUTHORISED SIGN</label>
									<input class="form-control form-alternate-text" id="id_oath-authorizedsign_label" maxlength="30" name="oath-authorizedsign_label" textboxfor="pdf_auth_sign_txt" type="text" value="{{ header_res.field_name.authorized_sign.label }}">
								</div>
								<div class="checkbox form-group">
									<input checkboxfor="show_approver_signature" {% if header_res.field_name.authorized_sign.print_approversign %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_oath-include_approver_signature" name="oath-include_approver_signature" type="checkbox">
									<label for="id_oath-include_approver_signature" style="width: 100%;"> include Approver Signatory</label>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="oa_template_edit select_table hide">
					<div class="form-horizontal">
						<div class="col-sm-12 document-margin" style="padding: 0;">
							<label class="spinner-label" style="width: 100px;">Font Size</label>
							<input class="input_spinner" id="id_oati-font_size" max="100" min="0" name="oati-font_size" spinner_for="table_font_size" step="1" type="number" value="{{ item_res.item_table.font_size }}" style="display: none;">
							<span class="doc-margin-prefix"> px</span>
						</div>
						<div class="col-sm-12 enterprise_details" style="padding: 0;">
							<div style="display: inline-block;">
								<label>Column</label>
								<label>Label</label>
								<label style="width: 60px; text-align: center;">Width%</label>
							</div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="td_sno" {% if item_res.item_table.sno.print %} checked="checked" {% endif %}   class="checkbox required_checkbox width_calc_checkbox" id="id_oati-include_sno" name="oati-include_sno" type="checkbox">
								<label for="id_oati-include_sno">S. No</label>
								<input class="form-control form-alternate-text" id="id_oati-sno_label" maxlength="30" name="oati-sno_label" textboxfor="td_sno_text" type="text" value="{{ item_res.item_table.sno.label }}">
								<input class="input_spinner input_spinner_set_width" id="id_oati-sno_width" max="100" min="0" name="oati-sno_width" spinner_for="td_sno" step="1" type="number" value="{{ item_res.item_table.sno.width }}" disabled="disabled" style="display: none;">
							</div>
							<div class="input_width_spinner form-group" style="margin-left: 20px; margin-bottom: -6px;">
								<label style="padding-left: 6px;">Item Details</label>
								<input class="form-control form-alternate-text" id="id_oati-itemdetails_label" maxlength="30" name="oati-itemdetails_label" textboxfor="td_description_text" type="text" value="{{ item_res.item_table.item_details.label }}">
								<input class="input_spinner input_spinner_set_width" disabled="disabled" id="id_oati-itemdetails_width" max="100" min="0" name="oati-itemdetails_width" spinner_for="td_description" step="1" type="number" value="{{ item_res.item_table.item_details.width }}" style="display: none;">
								<input id="id_oati-itemdetails_width_hidden_field" name="oati-itemdetails_width_hidden_field" type="hidden" value="76">
							</div>
							<div class="form-group input_width_spinner input_sub_item" style="margin-top: 6px; margin-bottom: 5px;">
								<label style="padding-left: 5px;">Name</label>
								<input class="form-control form-alternate-text" id="id_oati-name_label" maxlength="30" name="oati-name_label" textboxfor="pdf_item_name_txt" type="text" value="{{ item_res.item_table.item_details.itemname_label }}">
							</div>
							<div class="checkbox form-group input_width_spinner input_sub_item">
								<input checkboxfor="pdf_item_drawing_number" {% if item_res.item_table.item_details.itemcode.print %} checked="checked" {% endif %}  class="checkbox required_checkbox" id="id_oati-include_itemcode" name="oati-include_itemcode" type="checkbox">
								<label for="id_oati-include_itemcode">Item Code</label>
								<input class="form-control form-alternate-text" id="id_oati-itemcode_label" maxlength="30" name="oati-itemcode_label" textboxfor="pdf_item_drawing_number_txt" type="text" value="{{ item_res.item_table.item_details.itemcode.label }}">
							</div>
							<div class="checkbox form-group input_width_spinner input_sub_item" style="display:none">
								<input checkboxfor="pdf_item_make"  {% if item_res.item_table.item_details.make.print %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_oati-include_make" name="oati-include_make" type="checkbox">
								<label for="id_oati-include_make">Make</label>
								<input class="form-control form-alternate-text" id="id_oati-make_label" maxlength="50" name="oati-make_label" textboxfor="pdf_item_make_txt" type="text" value="{{ item_res.item_table.item_details.make.label }}">
							</div>
							<div class="checkbox form-group input_width_spinner input_sub_item" style="display:none">
								<input checkboxfor="pdf_item_part" {% if item_res.item_table.item_details.part_no.print %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_oati-include_partno" name="oati-include_partno" type="checkbox">
								<label for="id_oati-include_partno">Part No.</label>
								<input class="form-control form-alternate-text" id="id_oati-partno_label" maxlength="50" name="oati-partno_label" textboxfor="pdf_item_part_txt" type="text" value="{{ item_res.item_table.item_details.part_no.label }}">
							</div>
							<div class="checkbox form-group input_width_spinner input_sub_item">
								<input checkboxfor="pdf_item_desc" {% if item_res.item_table.item_details.description.print %} checked="checked" {% endif %}  class="checkbox required_checkbox" id="id_oati-include_description" name="oati-include_description" type="checkbox">
								<label for="id_oati-include_description">Description</label>
								<input class="form-control form-alternate-text" id="id_oati-description_label" maxlength="30" name="oati-description_label" textboxfor="pdf_item_desc_txt" type="text" value="{{ item_res.item_table.item_details.description.label }}">
							</div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="pdf_item_hsn_code" {% if item_res.item_table.hsnsac.print %} checked="checked" {% endif %} class="checkbox customized_checkbox width_calc_checkbox" id="id_oati-include_hsnsac" name="oati-include_hsnsac" type="checkbox">
								<label for="id_oati-include_hsnsac">HSN/SAC</label>
								<input class="form-control form-alternate-text" id="id_oati-hsnsac_label" maxlength="30" name="oati-hsnsac_label" textboxfor="pdf_item_hsn_code_txt" type="text" value="{{ item_res.item_table.hsnsac.label }}">
								<input class="input_spinner input_spinner_set_width" id="id_oati-hsnsac_width" max="100" min="0" name="oati-hsnsac_width" spinner_for="td_hsn_code" step="1" type="number" value="{{ item_res.item_table.hsnsac.width }}" disabled="disabled" style="display: none;">
								<input checkboxfor="pdf_hsn_in_description" {% if item_res.item_table.hsnsac.print_in_itemdetails %} checked="checked" {% endif %}  class="checkbox customized_checkbox" id="id_oati-hsnsac_part_of_itemdetails" name="oati-hsnsac_part_of_itemdetails" type="checkbox">
								<label for="id_oati-hsnsac_part_of_itemdetails" style="margin-left: 125px; width: calc(100% - 125px);">Part of Item Details</label>
							</div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="td_qty" {% if item_res.item_table.quantity.print %} checked="checked" {% endif %}  class="checkbox customized_checkbox width_calc_checkbox" id="id_oati-include_quantity" name="oati-include_quantity" type="checkbox">
								<label for="id_oati-include_quantity">Quantity</label>
								<input class="form-control form-alternate-text" id="id_oati-quantity_label" maxlength="20" name="oati-quantity_label" textboxfor="td_qty_text" type="text" value="{{ item_res.item_table.quantity.label }}">
								<input class="input_spinner input_spinner_set_width" id="id_oati-quantity_width" max="100" min="0" name="oati-quantity_width" spinner_for="td_qty_text" step="1" type="number" value="{{ item_res.item_table.quantity.width }}" style="display: none;">
							</div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="td_uom"  {% if item_res.item_table.units.print %} checked="checked" {% endif %} class="checkbox customized_checkbox width_calc_checkbox" id="id_oati-include_units" name="oati-include_units" type="checkbox">
								<label for="id_oati-include_units">Units</label>
								<input class="form-control form-alternate-text" id="id_oati-units_label" maxlength="20" name="oati-units_label" textboxfor="td_uom_text" type="text" value="{{ item_res.item_table.units.label }}">
								<input class="input_spinner input_spinner_set_width" id="id_oati-units_width" max="100" min="0" name="oati-units_width" spinner_for="td_uom_text" step="1" type="number" value="{{ item_res.item_table.units.width }}" disabled="disabled" style="display: none;">
								<input checkboxfor="pdf_unit_in_price" {% if item_res.item_table.units.units_in_qty %} checked="checked" {% endif %} class="checkbox customized_checkbox" id="id_oati-units_in_quantity_column" name="oati-units_in_quantity_column" type="checkbox" disabled="disabled">
								<label for="id_oati-units_in_quantity_column" style="margin-left: 125px; width: calc(100% - 125px);">In Qty Column</label>
							</div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="td_price" {% if item_res.item_table.unit_price.print %} checked="checked" {% endif %}  class="checkbox required_checkbox width_calc_checkbox" id="id_oati-include_unit_price" name="oati-include_unit_price" type="checkbox">
								<label for="id_oati-include_unit_price">Unit Price</label>
								<input class="form-control form-alternate-text" id="id_oati-unit_price_label" maxlength="30" name="oati-unit_price_label" textboxfor="td_price_text" type="text" value="{{ item_res.item_table.unit_price.label }}">
								<input class="input_spinner input_spinner_set_width" id="id_oati-unit_price_width" max="100" min="0" name="oati-unit_price_width" spinner_for="td_price_text" step="1" type="number" value="{{ item_res.item_table.discount.width }}" style="display: none;">
							</div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="td_disc" {% if item_res.item_table.discount.print %} checked="checked" {% endif %} class="checkbox required_checkbox width_calc_checkbox" id="id_oati-include_discount" name="oati-include_discount" type="checkbox">
								<label for="id_oati-include_discount">Discount</label>
								<input class="form-control form-alternate-text" id="id_oati-discount_label" maxlength="30" name="oati-discount_label" textboxfor="td_disc_text" type="text" value="{{ item_res.item_table.discount.label }}">
								<input class="input_spinner input_spinner_set_width" id="id_oati-discount_width" max="100" min="0" name="oati-discount_width" spinner_for="td_disc_text" step="1" type="number" value="{{ item_res.item_table.discount.width }}" style="display: none;">
							</div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="td_tax"  {% if item_res.item_table.taxable_amount.print %} checked="checked" {% endif %} class="checkbox customized_checkbox width_calc_checkbox" id="id_oati-include_taxable_amount" name="oati-include_taxable_amount" type="checkbox">
								<label for="id_oati-include_taxable_amount">Taxable Amount</label>
								<input class="form-control form-alternate-text" id="id_oati-taxable_amount_label" maxlength="30" name="oati-taxable_amount_label" textboxfor="td_tax_text" type="text" value="{{ item_res.item_table.taxable_amount.label }}">
								<input class="input_spinner input_spinner_set_width" id="id_oati-taxable_amount_width" max="100" min="0" name="oati-taxable_amount_width" spinner_for="td_tax_text" step="1" type="number" value="{{ item_res.item_table.taxable_amount.width }}" style="display: none;">
							</div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="row_seperator"  {% if item_res.field_alignment.separator.row_separator %} checked="checked" {% endif %} class="checkbox customized_checkbox" id="id_oati-include_row_separator" name="oati-include_row_separator" type="checkbox">
								<label for="id_oati-include_row_separator">Row Separator</label>
							</div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="column_seperator" {% if item_res.field_alignment.separator.column_separator %} checked="checked" {% endif %} class="checkbox customized_checkbox" id="id_oati-include_column_separator" name="oati-include_column_separator" type="checkbox">
								<label for="id_oati-include_column_separator">Column Separator</label>
							</div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="row_shading" {% if item_res.field_alignment.separator.alternative_row_shading %} checked="checked" {% endif %}  class="checkbox customized_checkbox" id="id_oati-include_alternate_row_shading" name="oati-include_alternate_row_shading" type="checkbox">
								<label for="id_oati-include_alternate_row_shading" style="width: 177px;">Alternative Row Shading</label>
							</div>
						</div>
					</div>
				</div>
				<div class="oa_template_edit select_total hide">
					<div class="form-horizontal">
						<div class="col-sm-12 document-margin remove-padding">
							<label class="spinner-label" style="width: 100px;">Font Size</label>
							<input class="input_spinner" id="id_oats-font_size" max="100" min="0" name="oats-font_size" spinner_for="total_font_size" step="1" type="number" value="{{ summary_res.totals.font_size }}" style="display: none;">
							<span class="doc-margin-prefix"> px</span>
						</div>
						<div class="col-sm-12 enterprise_total_details remove-padding">
							<div class="checkbox form-group">
								<input checkboxfor="show_total_section" {% if summary_res.totals.print %} checked="checked" {% endif %}  class="checkbox customized_checkbox" id="id_oats-include_total" name="oats-include_total" type="checkbox">
								<label for="id_oats-include_total">Show	Total Section</label>
							</div>
							<div class="checkbox form-group" style="margin-left: 15px;">
								<input checkboxfor="show_sub_total_section" {% if summary_res.totals.print_subtotal %} checked="checked" {% endif %} class="checkbox customized_checkbox" id="id_oats-include_subtotal" name="oats-include_subtotal" type="checkbox">
								<label for="id_oats-include_subtotal">Sub Total</label>
							</div>
							<div class="checkbox form-group" style="margin-left: 15px;">
								<input checkboxfor="show_quantity_total" {% if summary_res.totals.print_qtytotal %} checked="checked" {% endif %}   class="checkbox customized_checkbox" id="id_oats-include_qty_total" name="oats-include_qty_total" type="checkbox" disabled="disabled">
								<label for="id_oats-include_qty_total">Quantity Total</label>
							</div>
							<div class="checkbox form-group">
								<input checkboxfor="show_total_in_words" {% if summary_res.totals.print_total_in_words %} checked="checked" {% endif %}  class="checkbox required_checkbox" id="id_oats-include_total_in_words" name="oats-include_total_in_words" type="checkbox">
								<label for="id_oats-include_total_in_words" style="width:180px">Total Value in Words</label>
							</div>
							<hr style="margin: 10px -13px 20px; border-color: #ccc;">
							<div class="checkbox form-group">
								<input checkboxfor="hsn_summary_contianer" {% if summary_res.hsn_summary.print %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_oats-include_hsn_summary" name="oats-include_hsn_summary" type="checkbox">
								<label for="id_oats-include_hsn_summary">HSN Summary</label>
							</div>
							<div class="document-margin">
								<label class="spinner-label" style="width: 100px; font-size: 0.9em;">Font Size</label>
								<input class="input_spinner" id="id_oats-hsn_tax_font_size" max="100" min="0" name="oats-hsn_tax_font_size" spinner_for="hsn_tax_font_size" step="1" type="number" value="{{ summary_res.hsn_summary.font_size }}" style="display: none;">
								<span class="doc-margin-prefix"> px</span>
							</div>
						</div>
					</div>
				</div>
				<div class="oa_template_edit select_misc hide">
					<div class="form-horizontal">
						<div class="checkbox form-group">
							<input checkboxfor="page_number" {% if misc_res.print_pageno %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_oatm-include_page_no_in_footer" name="oatm-include_page_no_in_footer" type="checkbox">
							<label for="id_oatm-include_page_no_in_footer">Page Number</label>
						</div>
						<div class="form-group input_width_spinner hide" style="margin-left: 24px;">
							<ul id="id_oatm-footer_page_no_alignment">
								<label for="id_oatm-footer_page_no_alignment_0"><input checked="checked" id="id_oatm-footer_page_no_alignment_0" name="oatm-footer_page_no_alignment" type="radio" value="1"> Left</label>
								<label for="id_oatm-footer_page_no_alignment_1"><input id="id_oatm-footer_page_no_alignment_1" name="oatm-footer_page_no_alignment" type="radio" value="2"> Right</label>
							</ul>
						</div>
                        <div  class="form-group" style="padding: 0 15px; margin-top: 15px;">
							<label>Notes</label>
							<div style="float: right; margin-top: -1px; background: #FFF; margin-right: -8px; color: #004195; cursor: pointer;" data-toggle="modal" data-target="#notesEditableContainer">
								<i class="fa fa-pencil super_edit_in_field" style="margin-top: -3px; margin-right: 15px;"></i>
							</div>
							<textarea class="form-control" cols="40" id="id_oatm-notes" name="oatm-notes" rows="10" style="min-height: 75px; display: none;" textboxfor="notes" contenteditable="false">
                                {% autoescape off %}
								    {{misc_res.notes}}
								{% endautoescape %}
							</textarea>
						</div>
						<label>Multi Page Settings <i class="fa fa-info-circle" style="color: #666;" aria-hidden="true" data-tooltip="tooltip" title="" data-original-title="Can be viewed in PDF Preview"></i></label>
						<div class="checkbox form-group">
							<input checkboxfor="print_summary_first_page" {% if misc_res.print_summary_first_page %} checked="checked" {% endif %} class="checkbox" id="id_oatm-include_first_page_summary" name="oatm-include_first_page_summary" type="checkbox">
							<label for="id_oatm-include_first_page_summary">Summary in First Page</label>
						</div>
						<hr style="margin: 10px -13px 20px; border-color: #ccc;">
						<div class="document-margin" style="margin-top: 15px;">
							<label class="spinner-label" style="width: 100px; padding-top: 5px;">Font Size</label>
							<input class="input_spinner" id="id_oatm-font_size" max="100" min="0" name="oatm-font_size" spinner_for="misc_font_size" step="1" type="number" value="{{ misc_res.foot_note.font_size }}" style="display: none;">
							<span class="doc-margin-prefix"> px</span>
						</div>
						<div class="form-group" style="padding: 0 15px;">
							<label>Foot Note</label>
							<textarea class="form-control" cols="40" id="id_oatm-foot_note" maxlength="140" name="oatm-foot_note" onkeypress="return validateStringOnKeyPress(this,event, &quot;alphaSpecialChar&quot;);" onblur="return validateStringOnBlur(this, event, &quot;alphaSpecialChar&quot;);" rows="10" style="min-height : 120px" textboxfor="footer_notes">{{ misc_res.foot_note.notes }}</textarea>
						</div>
					</div>
                    <div class="document-margin" style="margin-top: 15px;margin-bottom: 15px;">
                        <label class="spinner-label" style="color: #209be1; text-shadow: 0 0 #000;width: 100%; padding-top: 5px;margin-bottom: 10px;">Banner Images</label>
                        <label class="spinner-label" style="width:35%;margin-top:-7px;">Header</label>
                        <label style="float: left;margin-top: 2px;">Size</label>

                        <span class="slidecontainer" style="float: left; margin-left: 11px;margin-top:3px;margin-bottom: 11px;width: 43%;">
                            <input class="slider" id="header_banner_size" max="100" min="0" name="header_size" step="1" type="range" value="{{ misc_res.banner_header.height }}">
                        </span>
                        <span id="header_banner_value" style="margin-left: 10px;">{{ misc_res.banner_header.height }}</span>
                        <div class="col-sm-4 banner-container input_width_spinner" role="button">
                            <div class="btn banner-image banner-header">
                                <span class="uploader">
                                    <i class="fa fa-upload" aria-hidden="true"></i>
                                    <br>Left Image
                                </span>
                                <input id="id_sethb-0-config_id" name="sethb-0-config_id" type="hidden" value="2">
                                <input id="id_sethb-0-section" name="sethb-0-section" type="hidden" value="Header">
                                <input id="id_sethb-0-position" name="sethb-0-position" type="hidden" value="Left">
                                <input class="banner-image-size" id="id_sethb-0-size" name="sethb-0-size" type="hidden" value="14">
                                <input id="id_sethb-0-attachment_id" name="sethb-0-attachment_id" type="hidden">
                                <input class="hidden-attachment" id="id_sethb-0-uploaded_banner_image" name="sethb-0-uploaded_banner_image" type="hidden">
                                <input class="remove-attachment" id="id_sethb-0-is_removed" name="sethb-0-is_removed" type="hidden">
                                <input id="id_sethb-0-banner_image" name="sethb-0-banner_image" type="hidden">
                                {% for banner in template_header_banner %}
                                    {% if banner.position == 'left' %}
                                        <img class="uploaded-banner-image hide" src="{{ banner.banner_image }}">
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <i class="fa fa-close remove-banner-image hide" aria-hidden="true"></i>
                           {% for banner in template_header_banner %}
                                {% if banner.position == 'left' %}
                                    <input class="header-banner" id="image_uploader-left" value="{{ banner.banner_image }}" type="hidden">
                                {% endif %}
                            {% endfor %}
                            <br>
                            <input class="input_spinner input_spinner_set_width_header_banner" id="id_oatm-header-left_banner_width" max="100" min="0" name="oatm-header-left_banner_width" spinner_for="misc_header-left_banner" step="1" type="number" value="" disabled="disabled" style="display: none;">
                            <span class="doc-margin-prefix"> %</span>
                        </div>
                        <div class="col-sm-4 banner-container input_width_spinner" role="button">
                            <div class="btn banner-image banner-header">
                                <span class="uploader">
                                    <i class="fa fa-upload" aria-hidden="true"></i>
                                    <br>Center Image
                                </span>
                                <input id="id_sethb-1-config_id" name="sethb-1-config_id" type="hidden" value="2">
                                <input id="id_sethb-1-section" name="sethb-1-section" type="hidden" value="Header">
                                <input id="id_sethb-1-position" name="sethb-1-position" type="hidden" value="Center">
                                <input class="banner-image-size" id="id_sethb-1-size" name="sethb-1-size" type="hidden" value="14">
                                <input id="id_sethb-1-attachment_id" name="sethb-1-attachment_id" type="hidden">
                                <input class="hidden-attachment" id="id_sethb-1-uploaded_banner_image" name="sethb-1-uploaded_banner_image" type="hidden">
                                <input class="remove-attachment" id="id_sethb-1-is_removed" name="sethb-1-is_removed" type="hidden">
                                <input id="id_sethb-1-banner_image" name="sethb-1-banner_image" type="hidden">
                                {% for banner in template_header_banner %}
                                    {% if banner.position == 'center' %}
                                        <img class="uploaded-banner-image hide" src="{{ banner.banner_image }}">
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <i class="fa fa-close remove-banner-image hide" aria-hidden="true"></i>
                           {% for banner in template_header_banner %}
                                {% if banner.position == 'center' %}
                                    <input class="header-banner" id="image_uploader-center" value="{{ banner.banner_image }}" type="hidden">
                                {% endif %}
                            {% endfor %}
                            <br>
                            {% for res in misc_res.banner_header.attachment %}
                                {% if res.position == "center" %}
                                    <input class="input_spinner input_spinner_set_width_header_banner" id="id_oatm-header-center_banner_width" max="100" min="0" name="oatm-header-center_banner_width" spinner_for="misc_header-center_banner" step="1" type="number" value="{{res.width}}" >
                                {% endif %}
                            {% endfor %}
                            <span class="doc-margin-prefix"> %</span>
                        </div>
                        <div class="col-sm-4 banner-container input_width_spinner" role="button">
                            <div class="btn banner-image banner-header">
                                <span class="uploader">
                                    <i class="fa fa-upload" aria-hidden="true"></i>
                                    <br>Right Image
                                </span>
                                <input id="id_sethb-2-config_id" name="sethb-2-config_id" type="hidden" value="2">
                                <input id="id_sethb-2-section" name="sethb-2-section" type="hidden" value="Header">
                                <input id="id_sethb-2-position" name="sethb-2-position" type="hidden" value="Right">
                                <input class="banner-image-size" id="id_sethb-2-size" name="sethb-2-size" type="hidden" value="14">
                                <input id="id_sethb-2-attachment_id" name="sethb-2-attachment_id" type="hidden">
                                <input class="hidden-attachment" id="id_sethb-2-uploaded_banner_image" name="sethb-2-uploaded_banner_image" type="hidden">
                                <input class="remove-attachment" id="id_sethb-2-is_removed" name="sethb-2-is_removed" type="hidden">
                                <input id="id_sethb-2-banner_image" name="sethb-2-banner_image" type="hidden">
                                {% for banner in template_header_banner %}
                                    {% if banner.position == 'right' %}
                                        <img class="uploaded-banner-image hide" src="{{ banner.banner_image }}">
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <i class="fa fa-close remove-banner-image hide" aria-hidden="true"></i>
                            {% for banner in template_header_banner %}
                                {% if banner.position == 'right' %}
                                    <input class="header-banner" id="image_uploader-right" value="{{ banner.banner_image }}" type="hidden">
                                {% endif %}
                            {% endfor %}
                            <br>

                            {% for res in misc_res.banner_header.attachment %}
                                {% if res.position == "right" %}
                                    <input class="input_spinner input_spinner_set_width_header_banner" id="id_oatm-header-right_banner_width" max="100" min="0" name="oatm-header-right_banner_width" spinner_for="misc_header-right_banner" step="1" type="number" value="{{res.width}}" >
                                {% endif %}
                            {% endfor %}
                            <span class="doc-margin-prefix"> %</span>
                        </div>
                        <div class="col-sm-12"><hr style="margin: 12px -27px; border-color: #ccc;"></div>
                        <div style="margin-top: 30px;">
                            <label class="spinner-label" style="width:35%; margin-top:-9px;">Footer</label>
                            <label style="float: left;margin-top: 2px;">Size</label>
                            <span class="slidecontainer" style="float: left; margin-left: 11px;margin-top: 3px;margin-bottom:11px;width: 43%;">
                                <input class="slider" id="footer_banner_size" max="100" min="0" name="footer_size" step="1" type="range" value="{{ misc_res.banner_footer.height }}">
                            </span>
                            <span id="footer_banner_value" style="margin-left: 10px;">{{ misc_res.banner_footer.height }}</span>
                            <div class="col-sm-4 banner-container input_width_spinner" role="button">
                                <div class="btn banner-image banner-footer">
                                    <span class="uploader">
                                        <i class="fa fa-upload" aria-hidden="true"></i>
                                        <br>Left Image
                                    </span>
                                    <input id="id_setfb-0-config_id" name="setfb-0-config_id" type="hidden" value="2">
                                    <input id="id_setfb-0-section" name="setfb-0-section" type="hidden" value="Footer">
                                    <input id="id_setfb-0-position" name="setfb-0-position" type="hidden" value="Left">
                                    <input class="banner-image-size" id="id_setfb-0-size" name="setfb-0-size" type="hidden" value="14">
                                    <input id="id_setfb-0-attachment_id" name="setfb-0-attachment_id" type="hidden">
                                    <input class="hidden-attachment" id="id_setfb-0-uploaded_banner_image" name="setfb-0-uploaded_banner_image" type="hidden">
                                    <input class="remove-attachment" id="id_setfb-0-is_removed" name="setfb-0-is_removed" type="hidden">
                                    <input id="id_setfb-0-banner_image" name="setfb-0-banner_image" type="hidden">
                                     {% for banner in template_footer_banner %}
                                        {% if banner.position == 'left' %}
                                            <img class="uploaded-banner-image hide" src="{{ banner.banner_image }}">
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <i class="fa fa-close remove-banner-image hide" aria-hidden="true"></i>
                                {% for banner in template_footer_banner %}
                                    {% if banner.position == 'left' %}
                                         <input class="header-banner" id="footer_uploader-left" value="{{ banner.banner_image }}" type="hidden">
                                    {% endif %}
                                {% endfor %}
                                <br>
                                <input class="input_spinner input_spinner_set_width_footer_banner" id="id_oatm-footer-left_banner_width" max="100" min="0" name="oatm-footer-left_banner_width" spinner_for="misc_footer-left_banner" step="1" type="number" value="" disabled="disabled" style="display: none;" >
                                <span class="doc-margin-prefix"> %</span>
                            </div>
                            <div class="col-sm-4 banner-container input_width_spinner" role="button">
                                <div class="btn banner-image banner-footer">
                                    <span class="uploader">
                                        <i class="fa fa-upload" aria-hidden="true"></i>
                                        <br>Center Image
                                    </span>
                                    <input id="id_setfb-1-config_id" name="setfb-1-config_id" type="hidden" value="2">
                                    <input id="id_setfb-1-section" name="setfb-1-section" type="hidden" value="Footer">
                                    <input id="id_setfb-1-position" name="setfb-1-position" type="hidden" value="Center">
                                    <input class="banner-image-size" id="id_setfb-1-size" name="setfb-1-size" type="hidden" value="14">
                                    <input id="id_setfb-1-attachment_id" name="setfb-1-attachment_id" type="hidden">
                                    <input class="hidden-attachment" id="id_setfb-1-uploaded_banner_image" name="setfb-1-uploaded_banner_image" type="hidden">
                                    <input class="remove-attachment" id="id_setfb-1-is_removed" name="setfb-1-is_removed" type="hidden">
                                    <input id="id_setfb-1-banner_image" name="setfb-1-banner_image" type="hidden">
                                   {% for banner in template_footer_banner %}
                                        {% if banner.position == 'center' %}
                                            <img class="uploaded-banner-image hide" src="{{ banner.banner_image }}">
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <i class="fa fa-close remove-banner-image hide" aria-hidden="true"></i>
                                {% for banner in template_footer_banner %}
                                    {% if banner.position == 'center' %}
                                        <input class="header-banner" id="footer_uploader-center" value="{{ banner.banner_image }}" type="hidden">
                                    {% endif %}
                                {% endfor %}
                                <br>
                                {% for res in misc_res.banner_footer.attachment %}
                                    {% if res.position == "center" %}
                                        <input class="input_spinner input_spinner_set_width_footer_banner" id="id_oatm-footer-center_banner_width" max="100" min="0" name="oatm-footer-center_banner_width" spinner_for="misc_footer-center_banner" step="1" type="number" value="{{res.width}}" >
                                     {% endif %}
                                {% endfor %}
                                <span class="doc-margin-prefix"> %</span>
                            </div>
                            <div class="col-sm-4 banner-container input_width_spinner" role="button">
                                <div class="btn banner-image banner-footer">
                                    <span class="uploader">
                                        <i class="fa fa-upload" aria-hidden="true"></i>
                                        <br>Right Image
                                    </span>
                                    <input id="id_setfb-2-config_id" name="setfb-2-config_id" type="hidden" value="2">
                                    <input id="id_setfb-2-section" name="setfb-2-section" type="hidden" value="Footer">
                                    <input id="id_setfb-2-position" name="setfb-2-position" type="hidden" value="Right">
                                    <input class="banner-image-size" id="id_setfb-2-size" name="setfb-2-size" type="hidden" value="14">
                                    <input id="id_setfb-2-attachment_id" name="setfb-2-attachment_id" type="hidden">
                                    <input class="hidden-attachment" id="id_setfb-2-uploaded_banner_image" name="setfb-2-uploaded_banner_image" type="hidden">
                                    <input class="remove-attachment" id="id_setfb-2-is_removed" name="setfb-2-is_removed" type="hidden">
                                    <input id="id_setfb-2-banner_image" name="setfb-2-banner_image" type="hidden">
                                   {% for banner in template_footer_banner %}
                                        {% if banner.position == 'right' %}
                                            <img class="uploaded-banner-image hide" src="{{ banner.banner_image }}">
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <i class="fa fa-close remove-banner-image hide" aria-hidden="true"></i>
                                {% for banner in template_footer_banner %}
                                    {% if banner.position == 'right' %}
                                        <input class="header-banner" id="footer_uploader-right" value="{{ banner.banner_image }}" type="hidden">
                                    {% endif %}
                                {% endfor %}
                                <br>
                                {% for res in misc_res.banner_footer.attachment %}
                                    {% if res.position == "right" %}
                                        <input class="input_spinner input_spinner_set_width_footer_banner" id="id_oatm-footer-right_banner_width" max="100" min="0" name="oatm-footer-right_banner_width" spinner_for="misc_footer-right_banner" step="1" type="number" value="{{res.width}}" >
                                       {% endif %}
                                {% endfor %}
                                <span class="doc-margin-prefix"> %</span>
                            </div>
                        </div>
                    </div>
				</div>
			</div>
			<div class="order_acknowledgement_template_default" style="float: right; margin-right: -3px; margin-top: -56px;">
				{% include "admin/print_template/oa/preview/oa_template_compact_preview.html" %}
			</div>
			<div class="oa_container_save hide" style="float: right; margin-right: -3px; margin-top: -56px;">
				{% include "admin/print_template/oa/document/oa_template_compact.html" %}
			</div>
		</div>
	</div>
	<div id="preview_modal" class="modal fade" role="dialog" tabindex="-1">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">&times;</button>
					<h4 class="modal-title">PDF Preview</h4>
				</div>
				<div class="modal-body">
					<div id="preview_document_container" class="full_txt_width">
						<div class="contant-container-nav" style="padding-top: 0;">
							<ul class="nav nav-tabs list-inline" style="margin-left: 0;">
								<li class="active"><a data-toggle="tab" href="#tab1" id="single-page-invoice">Single Page OA</a></li>
								<li><a data-toggle="tab" href="#tab2">Multi-Page OA</a></li>
							</ul>
							<span class="text-warning" style="position: absolute; color: #f0ad4e; right: 15px; top: 15px; width: 280px; text-align: right;">
									* Data populated below are for representation & does not depict any actual OA
								</span>
						</div>
						<div class="tab-content" style="border: 1px solid #ccc; padding: 10px; margin-top: -16px;">
							<div id="tab1" class="tab-pane fade in active">
							</div>
							<div id="tab2" class="tab-pane">
							</div>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
				</div>
			</div>
		</div>
	</div>
     <div id="notesEditableContainer" class="modal fade" role="dialog">
        <div class="modal-dialog modal-lg" style="width: 80%;">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Notes</h4>
                </div>
                <div class="modal-body">
                    <div id="notes_editor">
                        {{ misc_res.notes }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button  id="add_order_acknowledgement_notes" type="button" class="btn btn-save" onclick="addOrderAcknowledgementNote();">Add</button>
                    <button  id="cancel_order_acknowledgement_notes" data-dismiss="modal" type="button" class="btn btn-cancel">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
$(document).ready(function(){
    $("#loading").show();
	$('.header-banner').awesomeCropper(
        { debug: false }
	);
	applyCroppedImage();
	$("input[type='number']").inputSpinner();
	$('.spinner-textbox').on("cut copy paste",function(e) {
      e.preventDefault();
   });

	tabSelection();
	setCurrentDateTime();
	constructDateFormatField();
	logoSliderChangeEvent();
	headerSliderChangeEvent();
	footerSliderChangeEvent();
	inputSpinnerChangeEvent();
	dropDownChangeEvent();
	textboxBlurEvent();
	checkboxChangeEvent();
	imageClickEvent();
	removeBannerImageInit();
	leftContainerScrollInit();
	$(".form-control.input_spinner").attr({maxLength: "3", onfocus: "setNumberRangeOnFocus(this,3,0)"});
	if($("#last-modified-user").text().trim() != "") {
		$(".last-modified-user-container").removeClass("hide");
	}
	var editorElement = CKEDITOR.document.getById( 'id_oatm-notes' );
	editorElement.setAttribute( 'contenteditable', 'false' );
	CKEDITOR.inline( 'id_oatm-notes' );
	CKEDITOR.replace( 'notes_editor' );
	CKEDITOR.config.height = 300;
	var editorElement1 = CKEDITOR.document.getById( 'notes_editor' );
	var htmlText = $("#id_oatm-notes").text();
	editorElement1.setHtml(
		htmlText
	);
});

function updateRegistrationDetails() {
	var registrationDetailsObj = [];
	$(".registration_details").find("input[type='checkbox']").each(function(){
		var current = $(this);
		if(current.is(":checked")) {
			var item = {};
			item ["label_id"] = Number(current.attr("data-field-id"));
            item ["label"] = current.next("label").text();
            registrationDetailsObj.push(item);
		}
	});
	registrationDetailsObj = JSON.stringify(registrationDetailsObj);
	$("#id_oath-included_reg_items").val(registrationDetailsObj)
}

$(window).load(function(){
	$(".oa_template_editor").find("input[type='number']").trigger("change");
	$(".oa_template_editor").find("select").trigger("change");
	$(".oa_template_editor").find("input[type='text'], textarea").trigger("blur");
	$(".oa_template_editor").find("input[type='checkbox']").trigger("change");
	var output = $("#logo_size_value").text();
	$(".pdf_company_logo").css({height: output});
	updateBannerImagesOnLoad();
	$("#loading").hide();
});

function tabSelection() {
	$(".nav_order_acknowledgement_template li").click(function(){
		$(".nav_order_acknowledgement_template li").removeClass("active");
		var selectedLi = $(this).data("field");
		var selectedTab = $(this).data("tab");
		$(this).addClass("active");
		$(".oa_template_edit").addClass("hide");
		$("."+selectedLi).removeClass("hide");
		$("#id_oatg-tab_retain").val(selectedTab)
	});
}

function setCurrentDateTime() {
	var dt = new Date();
	var datetime = moment(dt).format("YYYY-MM-DD hh:mm:ss")
	var date = moment(dt).format("YYYY-MM-DD")
		$(".issue_date_time").text(datetime);
		$(".pdf_oa_date").text(date);
		$(".pdf_estimate_date").text(date);
}

function logoSliderChangeEvent() {
	var slider = document.getElementById("id_oath-logo_size");
	var output = document.getElementById("logo_size_value");
	output.innerHTML = slider.value;
	slider.oninput = function() {
	  output.innerHTML = this.value;
	  $(".pdf_company_logo").css({height: this.value});
	}
}

function inputSpinnerChangeEvent() {
	$("input[type='number']").on("change", function (event) {
		var spinner_for = $(this).attr("spinner_for");
		var spinnerValue = $(this).val();
		if(spinnerValue == "") spinnerValue = 0;
		if(spinner_for == "pdf_company_name") {
		    $(".pdf_company_name").css({fontSize: spinnerValue+"px"});
		}
		else if(spinner_for == "pdf_company_details") {
			$(".pdf_company_address_container, .pdf_company_details_container").css({fontSize: spinnerValue+"px"});
			$(".pdf_issue_date_time").css({fontSize: spinnerValue+"px"});
		}
		else if(spinner_for == "pdf_order_acknowledgement_number") {
			$(".pdf_order_acknowledgement_prefix, .pdf_order_acknowledgement_no").css({fontSize: spinnerValue+"px"});
		}
		else if(spinner_for == "pdf_form_name") {
			$(".pdf_form_name_text_size").css({fontSize: spinnerValue+"px"});
			if(spinnerValue == 0) {
				$(".oa_template_view .pdf_form_name_text_size").addClass("hide");
			}
			else {
				$(".oa_template_view .pdf_form_name_text_size").removeClass("hide");
			}
		}
		else if(spinner_for == "page_margin_top") {
			$(".oa_pdf_editor").css({paddingTop: spinnerValue+"mm"});
		}
		else if(spinner_for == "page_margin_bottom") {
			$(".oa_pdf_editor").css({paddingBottom: spinnerValue+"mm"});
		}
		else if(spinner_for == "page_margin_left") {
			$(".oa_pdf_editor").css({paddingLeft: spinnerValue+"mm"});
		}
		else if(spinner_for == "page_margin_right") {
			$(".oa_pdf_editor").css({paddingRight: spinnerValue+"mm"});
		}
		else if(spinner_for == "table_font_size") {
			$(".item_table thead th, .item_table tbody td, .item_table athead ath, .item_table atbody atd").css("font-size", spinnerValue+"px");
		}
		else if(spinner_for == "total_font_size") {
			$(".item_table tfoot td, .item_table atfoot atd, .notes_section, .signatory_content").css({fontSize: spinnerValue+"px", lineHeight: spinnerValue+"px"});
		}
		else if(spinner_for == "hsn_tax_font_size") {
			$(".hsn_summary, .hsn_summary_title").css("font-size", spinnerValue+"px");
		}
		else if(spinner_for == "misc_font_size") {
			$(".footer_notes").css({fontSize: spinnerValue+"px"});
		}
		else {
			calculateWidthforItemTable();
			calculateWidthforBanner();
		}
	});
}

function dropDownChangeEvent(){
	$("select").change(function(){
		var selectField = $(this).attr("id");
		var selectValue = $(this).val();
		if(selectField == "id_oatg-base_font") {
			$(".oa_pdf_editor").css({fontFamily: selectValue });
		}
		else if(selectField == "id_oath-name_font") {
			$(".oa_pdf_editor .pdf_company_name").css({fontFamily: selectValue });
		}
	});
}

function textboxBlurEvent(){
	$("input[type='text'], textarea").keyup(function(){
		var checkbox_for = $(this).attr("textboxfor");
		$("."+checkbox_for).text($(this).val());
		if(checkbox_for == "bill_to_text") {
			if($(this).val().trim() == "") {
				$("."+checkbox_for).addClass("hide");
			}
			else {
				$("."+checkbox_for).removeClass("hide");
			}
		}
	});
	$("input[type='text'], textarea").blur(function(){
		var checkbox_for = $(this).attr("textboxfor");
		$("."+checkbox_for).html($(this).val());
		if(checkbox_for == "bill_to_text") {
			if($(this).val().trim() == "") {
				$("."+checkbox_for).addClass("hide");
			}
			else {
				$("."+checkbox_for).removeClass("hide");
			}
		}
	});
}

function checkboxChangeEvent() {
        $('#id_oati-include_make').prop('checked', true);
	    $('#id_oati-include_partno').prop('checked', true);
	$(".required_checkbox").change(function(){
		var checkbox_for = $(this).attr("checkboxfor");
		if($(this).is(":checked")) {
			$("."+checkbox_for).removeClass("hide")
		}
		else {
			$("."+checkbox_for).addClass("hide")
		}
		if(checkbox_for == 'pdf_item_make' || checkbox_for == 'pdf_item_part') {
			var isMakeChecked = $("#id_oati-include_make").is(":checked");
			var isPartChecked = $("#id_oati-include_partno").is(":checked");
			if( isMakeChecked && isPartChecked) {
				$(".pdf_item_make_start").html("[");
				$(".pdf_item_part_start").html("");
				$(".pdf_item_make_value").html("NEW - ");
				$(".pdf_item_part_value").html("PN534]<br>");
			}
			else if( isMakeChecked || isPartChecked) {
				if(isMakeChecked) {
					$(".pdf_item_make_start").html("[");
					$(".pdf_item_part_start").html("");
					$(".pdf_item_make_value").html("NEW]<br>");
				}
				else {
					$(".pdf_item_part_start").html("[");
					$(".pdf_item_make_start").html("");
					$(".pdf_item_part_value").html("PN534]<br>");
				}
			}
			else {
				$(".pdf_item_make_value").html("");
				$(".pdf_item_part_value").html("");
			}
		}
		if(checkbox_for == 'pdf_bill_to_enterprise_code' || checkbox_for == 'pdf_bill_to_enterprise_name') {
			var isPartyCodeChecked = $("#id_oath-include_partycode").is(":checked");
			var isPartyNameChecked = $("#id_oath-include_partyname").is(":checked");
			if( isPartyCodeChecked && isPartyNameChecked) {
				$(".pdf_bill_to_enterprise_detail").html("ABC SOFTWARE SOLUTION (ABC001)");
			}
			else if( isPartyCodeChecked || isPartyNameChecked) {
				if(isPartyCodeChecked) {
					$(".pdf_bill_to_enterprise_detail").html("ABC001");
				}
				else {
					$(".pdf_bill_to_enterprise_detail").html("ABC SOFTWARE SOLUTION");
				}
			}
			else {
				$(".pdf_bill_to_enterprise_detail").html("");
			}
		}
	});

	$(".customized_checkbox").change(function(){
		var checkbox_for = $(this).attr("checkboxfor");
		if(checkbox_for == "row_seperator") {
			if($(this).is(":checked")) {
				$(".item_table, .hsn_table").addClass("row-seperator");
			}
			else {
				$(".item_table, .hsn_table").removeClass("row-seperator");
			}
		}
		if(checkbox_for == "column_seperator") {
			if($(this).is(":checked")) {
				$(".item_table, .hsn_table").addClass("column-seperator");
			}
			else {
				$(".item_table, .hsn_table").removeClass("column-seperator");
			}
		}
		if(checkbox_for == "row_shading") {
			if($(this).is(":checked")) {
				$(".row_shading").addClass("shaded");
				$(".header_shading").addClass("shaded");
			}
			else {
				$(".row_shading").removeClass("shaded");
				$(".header_shading").removeClass("shaded");
			}
		}
		if(checkbox_for == "show_total_section") {
			if($(this).is(":checked")) {
				$(".total_section").removeClass("hide");
				$("#id_oats-include_subtotal").removeAttr("disabled");
				if($("#id_oats-include_subtotal").is(":checked")) {
					$("#id_oats-include_qty_total").removeAttr("disabled");
				}
				else {
					$("#id_oats-include_qty_total").attr("disabled", "disabled");
				}
			}
			else {
				$(".total_section").addClass("hide");
				$("#id_oats-include_subtotal").attr("disabled", "disabled");
				$("#id_oats-include_qty_total").attr("disabled", "disabled");
			}
			$("#id_oats-include_subtotal").trigger("change");
		}

		if(checkbox_for == "show_sub_total_section") {
			if($(this).is(":checked") && $(this).is(":enabled")) {
				$(".sub_total_section").removeClass("hide");
				if($("#id_oats-include_total").is(":checked")) {
					$("#id_oats-include_qty_total").removeAttr("disabled");
				}
				else {
					$("#id_oats-include_qty_total").attr("disabled", "disabled");
				}
			}
			else {
				$(".sub_total_section").addClass("hide");
				$("#id_oats-include_qty_total").attr("disabled", "disabled");
			}
		}

		if(checkbox_for == "td_tax"){
			if($(this).is(":checked")) {
				$(".td_tax").removeClass("hide");
			}
			else {
				$(".td_tax").addClass("hide");
			}
			colspanManipulation();
			calculateWidthforItemTable();
		}
		if(checkbox_for == "show_quantity_total"){
			if($(this).is(":checked")) {
				$(".total_section_2").removeClass("hide");
			}
			else {
				$(".total_section_2").addClass("hide");
			}
			colspanManipulation();
			calculateWidthforItemTable();
		}

		if(checkbox_for == "pdf_item_hsn_code" || checkbox_for == "pdf_hsn_in_description") {
			var isHsnEnabled = $("#id_oati-include_hsnsac").is(":checked");
			var isHsnDescEnabled = $("#id_oati-hsnsac_part_of_itemdetails").is(":checked");
			if(isHsnEnabled && isHsnDescEnabled) {
				$(".pdf_item_hsn_code").removeClass("hide");
				$(".td_hsn_code").addClass('hide');
				$("#id_oati-hsnsac_width").attr("disabled", "disabled");
			}
			else if(isHsnEnabled || isHsnDescEnabled) {
				if(isHsnEnabled) {
					$(".pdf_item_hsn_code").addClass("hide");
					$(".td_hsn_code").removeClass('hide');
					$("#id_oati-hsnsac_width").removeAttr("disabled")
				}
				else {
					$(".td_hsn_code").addClass('hide');
					$(".pdf_item_hsn_code").addClass("hide");
					$("#id_oati-hsnsac_width").attr("disabled", "disabled")
				}

			}
			else {
				$(".pdf_item_hsn_code").addClass("hide");
				$(".td_hsn_code").addClass('hide');
				$("#id_oati-hsnsac_width").attr("disabled", "disabled")
			}
			if(isHsnEnabled) {
				$("#id_oati-hsnsac_part_of_itemdetails").removeAttr("disabled")
			}
			else {
				$("#id_oati-hsnsac_part_of_itemdetails").attr("disabled", "disabled")
			}
			setTimeout(function(){
				calculateWidthforItemTable();
				colspanManipulation();
			},10);
		}
		if(checkbox_for == "td_uom" || checkbox_for == "pdf_unit_in_price") {
			var isUnitEnabled = $("#id_oati-include_units").is(":checked");
			var isUnitInQtyEnabled = $("#id_oati-units_in_quantity_column").is(":checked");
			if(isUnitEnabled && isUnitInQtyEnabled) {
				$(".pdf_unit_in_price").removeClass("hide");
				$(".td_uom").addClass('hide');
				$("#id_oati-units_width").attr("disabled", "disabled");
			}
			else if(isUnitEnabled || isUnitInQtyEnabled) {
				if(isUnitEnabled) {
					$(".pdf_unit_in_price").addClass("hide");
					$(".td_uom").removeClass('hide');
					$("#id_oati-units_width").removeAttr("disabled")
				}
				else {
					$(".td_uom").addClass('hide');
					$(".pdf_unit_in_price").addClass("hide");
					$("#id_oati-units_width").attr("disabled", "disabled")
				}
			}
			else {
				$(".pdf_unit_in_price").addClass("hide");
				$(".td_uom").addClass('hide');
				$("#id_oati-units_width").attr("disabled", "disabled")
			}
			if(isUnitEnabled) {
				$("#id_oati-units_in_quantity_column").removeAttr("disabled")
			}
			else {
				$("#id_oati-units_in_quantity_column").attr("disabled", "disabled")
			}
			setTimeout(function(){
				calculateWidthforItemTable();
				colspanManipulation();
			},10);
		}

		if(checkbox_for == "td_qty" || checkbox_for == "show_quantity_total") {
			var isQtyEnabled = $("#id_oati-include_quantity").is(":checked");
			var isQtyInTotalEnabled = $("#id_oats-include_qty_total").is(":checked");
			if(isQtyEnabled && isQtyInTotalEnabled) {
				$(".total_section_2").removeClass("hide");
				$(".td_qty").removeClass("hide")
			}
			else if(isQtyEnabled || isQtyInTotalEnabled) {
				$(".total_section_2").addClass("hide")
				if(isQtyEnabled) {
					$(".td_qty").removeClass("hide");
				}
				else {
					$(".td_qty").addClass("hide");
				}
			}
			else {
				$(".total_section_2").addClass("hide");
				$(".td_qty").addClass("hide");
			}
			setTimeout(function(){
				calculateWidthforItemTable();
				colspanManipulation();
			},10);
		}
	});
}

$(".width_calc_checkbox").change(function(){
	if($(this).is(":checked") && $(this).is(":enabled")) {
		$(this).closest("div").find(".input_spinner").removeAttr("disabled");
	}
	else {
		$(this).closest("div").find(".input_spinner").attr("disabled", "disabled");
	}
	setTimeout(function(){
		calculateWidthforItemTable();
		colspanManipulation();
	},10);
});

function colspanManipulation() {
	var totalVisibleColumn = $(".item_table").find("tbody").find("tr:first-child").find("td:visible").length;
	var totalVisibleColumnForPrice = $(".item_table").find("thead").find("tr:first-child").find("th:lt(3):visible").length;
	var totalVisibleColumnForPrice_1 = $(".item_table").find("thead").find("tr:first-child").find("th:lt(3):visible").length;
	var isRateEnabled = $("#id_oati-include_taxrate").is(":checked") && $("#id_oati-include_taxrate").is(":enabled");
	var isAmtEnabled = $("#id_oati-include_taxamount").is(":checked") && $("#id_oati-include_taxamount").is(":enabled");

	var totalAmtVisibleColumn, totalTaxVisibleColumn = 0;
	if($(".td_uom_text").is(":visible")) {
		totalTaxVisibleColumn++;
	}
	if($(".td_price_text").is(":visible")) {
		totalTaxVisibleColumn++;
	}
	if($(".td_disc_text").is(":visible")) {
		totalTaxVisibleColumn++;
	}
	if($(".td_tax_text").is(":visible")) {
		totalTaxVisibleColumn++;
	}
	totalAmtVisibleColumn = totalTaxVisibleColumn;
	if($(".td_qty_text").is(":visible") && !$("#id_oats-include_qty_total").is(":checked")) {
		totalAmtVisibleColumn++;
	}
	if($(".td_qty_text").is(":visible")) {
		totalTaxVisibleColumn++;
	}
	if(isRateEnabled) {
		totalTaxVisibleColumn += 3;
	}
	if(isAmtEnabled) {
		totalTaxVisibleColumn += 3;
	}
	if(isRateEnabled && isAmtEnabled) {
		$(".total_section_1").attr("colspan", totalVisibleColumnForPrice_1);
		$(".total_section_3").attr("colspan", Number(totalVisibleColumn - totalVisibleColumnForPrice_1));
	}
	else {
		$(".total_section_1").attr("colspan", totalVisibleColumnForPrice);
		$(".total_section_3").attr("colspan", Number(totalVisibleColumn - totalVisibleColumnForPrice));
	}
	$(".sub_total_section .total_section_1").attr("colspan", totalVisibleColumnForPrice);
	$(".full-length-td").attr("colspan", totalVisibleColumn);
	$(".sub_total_section .total_section_3").attr("colspan", totalAmtVisibleColumn);
}

function calculateWidthforItemTable() {
	var totalWidthValue = 0;
	$(".form-control.input_spinner_set_width:enabled").each(function() {
		if($(this).closest(".input_width_spinner").find(".width_calc_checkbox").attr("id") == "id_oati-include_taxrate" && $(this).closest(".input_width_spinner").find(".width_calc_checkbox").is(":checked")) {
			totalWidthValue += Number($(this).val() * 3);
		}
		else if($(this).closest(".input_width_spinner").find(".width_calc_checkbox").attr("id") == "id_oati-include_taxamount" && $(this).closest(".input_width_spinner").find(".width_calc_checkbox").is(":checked")) {
			totalWidthValue += Number($(this).val() * 3);
		}
		else {
			totalWidthValue += Number($(this).val());
		}
		var widthForDescription = 100 - totalWidthValue;
		$("input[spinner_for='td_description']").val(widthForDescription);
		$("#id_oati-itemdetails_width_hidden_field").val(widthForDescription);
	});
	setWidthToItemTable();
}

function calculateWidthforBanner() {
	var totalWidthHeaderBanner = 0;
	var totalWidthFooterBanner = 0;
	$(".form-control.input_spinner_set_width_header_banner:enabled").each(function() {
		totalWidthHeaderBanner += Number($(this).val());
	});
	$(".form-control.input_spinner_set_width_footer_banner:enabled").each(function() {
		totalWidthFooterBanner += Number($(this).val());
	});

	var widthForHeaderBanner = 100 - totalWidthHeaderBanner;
	var widthForFooterBanner = 100 - totalWidthFooterBanner;

	$("input[spinner_for='misc_header-left_banner']").val(widthForHeaderBanner);
	$("input[spinner_for='misc_footer-left_banner']").val(widthForFooterBanner);
	setWidthToBannerImage();
}

function setWidthToItemTable() {
	$(".form-control.input_spinner_set_width").each(function() {
		var spinner_for = $(this).closest(".input_width_spinner").find("input[type='number']").attr('spinner_for');
		var spinner_value = $(this).val();
		$("."+spinner_for).css({width: spinner_value+"%" });
		if(spinner_for == "td_tax_rate") {
			var total = Number($("#id_oati-taxrate_width").val()) + Number($("#id_oati-taxamount_width").val());
			$(".item_table").find("th.td_cgst, th.td_sgst, th.td_igst").css({width: total+"%" });
		}
	});
}

function setWidthToBannerImage() {
	$(".form-control.input_spinner_set_width_header_banner").each(function() {
		var spinner_for = $(this).closest(".input_width_spinner").find("input[type='number']").attr('spinner_for');
		var spinner_value = $(this).val();
		$("."+spinner_for).css({width: spinner_value+"%" });
		if(spinner_value <= 0) {
			$("."+spinner_for).addClass("hide");
		}
		else {
			$("."+spinner_for).removeClass("hide");
		}
	});
	$(".form-control.input_spinner_set_width_footer_banner").each(function() {
		var spinner_for = $(this).closest(".input_width_spinner").find("input[type='number']").attr('spinner_for');
		var spinner_value = $(this).val();
		$("."+spinner_for).css({width: spinner_value+"%" });
		if(spinner_value <= 0) {
			$("."+spinner_for).addClass("hide");
		}
		else {
			$("."+spinner_for).removeClass("hide");
		}
	});
}

$("#pdf_preview").click(function(){
	$(this).val("Generating PDF...").attr("disabled", "disabled");
});

function openNotesOverlay() {
	$("#notesEditableContainer").modal("show");
}

function addOrderAcknowledgementNote() {
	var notes = CKEDITOR.instances.notes_editor.getData();
	$("#id_oatm-notes").html(notes);
	$("#id_oatm-notes").next("div").html(notes);
	$(".notes").html(notes);
	$("#notesEditableContainer").modal("hide");
}

function imageClickEvent() {
	$(".banner-image").click(function(){
  		$(this).closest(".banner-container").find("input[type='file']").click();
  	});
}

function applyCroppedImage() {
	$(".apply-crop").click(function(){
		var currentContainer = $(this).closest(".banner-container");
		setTimeout(function(){
			var currentBase64Value = currentContainer.find(".header-banner").val();
			var currentImageId = currentContainer.find(".header-banner").attr("id");
			currentContainer.find("img.uploaded-banner-image").attr("src", currentBase64Value).removeClass("hide");
			currentContainer.find(".hidden-attachment").val(currentBase64Value);
			currentContainer.find(".uploader").addClass("hide");
			currentContainer.find(".remove-banner-image").removeClass("hide");
			currentContainer.find(".remove-attachment").val("False");
			$(".oa_pdf_editor ."+currentImageId).attr("src",currentBase64Value);
			if(currentImageId.indexOf("footer") >= 0) {
				var imageSize = $("#footer_banner_value").text().trim();
			}
			else {
				var imageSize = $("#header_banner_value").text().trim();
			}
			currentContainer.find(".banner-image-size").val(imageSize);
			$("."+currentImageId).css({maxHeight: imageSize+"px"});
		},100);
	});
}

function updateBannerImagesOnLoad() {
	$(".uploaded-banner-image").each(function () {
		if($(this).attr("src") != "") {
			var currentContainer = $(this).closest(".banner-container");
			var currentBase64Value = $(this).attr("src");
			var currentImageId = currentContainer.find(".header-banner").attr("id");
			currentContainer.find("img.uploaded-banner-image").removeClass("hide");
			currentContainer.find(".uploader").addClass("hide");
			currentContainer.find(".remove-banner-image").removeClass("hide");
			currentContainer.find(".remove-attachment").val("False");
			$(".oa_pdf_editor ."+currentImageId).attr("src",currentBase64Value);
			if(currentImageId.indexOf("footer") >= 0) {
				var imageSize = $("#footer_banner_value").text().trim();
			}
			else {
				var imageSize = $("#header_banner_value").text().trim();
			}
			currentContainer.find(".banner-image-size").val(imageSize);
			$("."+currentImageId).css({maxHeight: imageSize+"px"});
		}
	})
}

function headerSliderChangeEvent() {
	var slider = document.getElementById("header_banner_size");
	var output = document.getElementById("header_banner_value");
	output.innerHTML = slider.value;
	slider.oninput = function() {
	   	output.innerHTML = this.value;
	   	$(".banner-header .banner-image-size").val(this.value);
	   	$(".header-banner-container img").css({maxHeight:this.value+"px"});
	}
}

function footerSliderChangeEvent() {
	var slider = document.getElementById("footer_banner_size");
	var output = document.getElementById("footer_banner_value");
	output.innerHTML = slider.value;
	slider.oninput = function() {
	   	output.innerHTML = this.value;
	   	$(".banner-footer .banner-image-size").val(this.value);
    	$(".footer-banner-container img").css({maxHeight:this.value+"px"});
	}
}

function removeBannerImageInit() {
    $(".remove-banner-image").click(function(){
    	var currentContainer = $(this).closest(".banner-container");
       	currentContainer.find("img.uploaded-banner-image").removeAttr("src").addClass("hide");
       	currentContainer.find(".banner-image").removeClass("image-uploaded");
       	currentContainer.find(".uploader").removeClass("hide");
	   	currentContainer.find(".remove-banner-image").addClass("hide");
	   	currentContainer.find(".remove-attachment").val("True");
	   	currentContainer.find(".header-banner").val("");
	    var currentId = $(this).next("input.header-banner").attr("id");
	    $(".oa_pdf_editor ."+currentId).removeAttr("src");
	})
}

function closeCropModal(current) {
    $(current).closest(".modal").modal("hide")
}

function constructDateFormat(){
	var dateFormat = "";
	var timeFormat = "";
	var dateFormatPy = "";
	var timeFormatPy = "";
	var is12HoursFormat = false;
	$(".date-formatter").each(function(){
		dateFormat += $(this).find("option:selected").attr("data-value");
		dateFormatPy += $(this).val();
	});
	$(".time-formatter").each(function(){
		var lastDataValue = $(this).find("option:selected").attr("data-value");
		if(lastDataValue !="") {
			timeFormat += lastDataValue+":";
			timeFormatPy += $(this).val();
			if(lastDataValue == "hh") {
				is12HoursFormat = true;
			}
		}
		if(timeFormatPy.indexOf(":") == 0) {
			timeFormatPy = timeFormatPy.substring(1);
		}
	});
	timeFormat = timeFormat.substring(0, timeFormat.length - 1);

	if(is12HoursFormat) {
		timeFormatPy+= " %p";
		timeFormat+= " A";
	}
	var dateTimeFormatPy = dateFormatPy.trim()+" "+timeFormatPy.trim();
	if(timeFormat == "") timeFormat = " ";
	$(".dateFormat-preview").text(moment().format(dateFormat));
	$(".timeFormat-preview").text(moment().format(timeFormat));
	$(".issue_date_time").text(moment().format(dateFormat) + " " + moment().format(timeFormat));
	$(".pdf_oa_date").text(moment().format(dateFormat) + " " + moment().format(timeFormat));
	$(".pdf_estimate_date").text(moment().format(dateFormat));
	$(".pdf_item_dc_date").text(moment().format(dateFormat));
	$("#id_oatg-oa_doc_datetime_format").val(dateTimeFormatPy.trim())
}

function constructDateFormatField(){
	var dateFormat = $("#id_oatg-oa_doc_datetime_format").val();
	var format = "";
	var formattedArray = [];
	for ( var i = 0; i < dateFormat.length; i++ ) {
		format += dateFormat[i];
		if(format == "%" || format == " %" || format == ",") {
			continue;
		}
		formattedArray.push(format);
		format = "";
	}
	var list = "";
	formattedArray.forEach(function(item) {
		if(list.length < 10) {
			if(item == "%d") {
				$(".date-formatter-date").val(item);
				list += "1,"
			}
			else if(item == "%b" || item == "%B" || item == "%m") {
				$(".date-formatter-month").val(item);
				list += "3,"
			}
			else if(item == "%y" || item == "%Y") {
				$(".date-formatter-year").val(item);
				list += "5,"
			}
			else if(["/", ",", "-", ", ",".", " "].indexOf(item) >=0) {
				if(list.indexOf("2,") == -1) {
					list += "2,"
					$('.date-formatter-seperator1').val(item);
				}
				else {
					list += "4,"
					$('.date-formatter-seperator2').val(item);
				}
			}
		}
		if(item == "%H") {
			$(".time-formatter-hour").val(" %H");
		}
		if(item == "%I") {
			$(".time-formatter-hour").val(" %I");
		}
		else if(item == "%M") {
			$(".time-formatter-minute").val(":%M");
		}
		else if(item == "%S") {
			$(".time-formatter-second").val(":%S");
		}
	});
	for (var i = 1; i <= 5; i++) {
		if(list.indexOf(i) == -1) list += i+",";
	}
	var orderArray = list.split(',');
	var listArray = $('#dateFormatter td');
	for (var i = 0; i < orderArray.length; i++) {
   		$('#dateFormatter tr').append(listArray[orderArray[i]-1]);
	}
	setTimeout(function(){
		$("#dateFormatter").sortable({
		    items: 'tr td',
		    cursor: 'pointer',
		    axis: 'x',
		    dropOnEmpty: false,
		    start: function (e, ui) {
		        ui.item.addClass("selected");
		    },
		    stop: function (e, ui) {
		       constructDateFormat();
		    }
		});
	},2000);

	$(".date-formatter, .time-formatter").change(function(){
		constructDateFormat();
	});
	constructDateFormat();
}

function leftContainerScrollInit() {
	var windowHeight = Number($( window ).height() - 200);
		$('.oa_template_editor').slimScroll({
		height: windowHeight,
		alwaysVisible: true,
		size : '4px'
	});
}

function previewTemplateChanges(url){
	if(url.indexOf("preview") > 0){
		event.preventDefault();
		oaTemplateDate = constructJSON();
		var oaTemplateHeader = {'header_banner': [{'position': 'left', 'width': $("#id_oatm-header-left_banner_width").val(), 'banner_image': $("#image_uploader-left").val()},
								{'position': 'right', 'width': $("#id_oatm-header-right_banner_width").val(), 'banner_image': $("#image_uploader-right").val()},
								{'position': 'center', 'width': $("#id_oatm-header-center_banner_width").val(), 'banner_image': $("#image_uploader-center").val()}]}

		var oaTemplateFooter = {'footer_banner': [{'position': 'left', 'width': $("#id_oatm-footer-left_banner_width").val(), 'banner_image': $("#footer_uploader-left").val()},
								{'position': 'right', 'width': $("#id_oatm-footer-right_banner_width").val(), 'banner_image': $("#footer_uploader-right").val()},
								{'position': 'center', 'width': $("#id_oatm-footer-center_banner_width").val(), 'banner_image': $("#footer_uploader-center").val()}]}
		var oa_data = JSON.stringify(oaTemplateDate);
		var oa_header = JSON.stringify(oaTemplateHeader);
		var oa_footer = JSON.stringify(oaTemplateFooter);
		$.ajax({
            url : url,
			type: "POST",
		    dataType: "json",
		    data: {'data': oa_data, 'header_banner': oa_header, 'footer_banner': oa_footer},
            success: function (response){
                if(response.response_message =="Success"){
                    $("#preview_document_container").html();
					var row1 = '<object class="document_preview_viewer" data="'+response.single_page_data+'"></object>';
					var row2 = '<object class="document_preview_viewer" data="'+response.multi_page_data+'"></object>';
	                $("#preview_document_container #tab1").html(row1);
	                $("#preview_document_container #tab2").html(row2);
					$("#preview_modal").modal("show");
					$("#single-page-invoice").trigger("click");
				}
				else{
                    swal({title: "", text: "Something went wrong!<br> Please try again", type: "error"});
                }
                $("#pdf_preview").val("PDF Preview").removeAttr("disabled");
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
                $("#pdf_preview").val("PDF Preview").removeAttr("disabled");
                if(xhr.responseText.indexOf('Your session has expired') != -1) {
                	location.reload();
                	return;
                }
            }
        });
    }
}
function constructJSON(){
	var oa_html_code = `<link rel="stylesheet" type="text/css" href='https://xserp.in/site_media/css/bootstrap.css'>`;
	    oa_html_code = oa_html_code+" "+$(".oa_container_save").html();
		oa_html_code = oa_html_code.replace(/[\[]+/g,'{');
		oa_html_code = oa_html_code.replace(/[\[\]]+/g,'}');
		oa_html_code = oa_html_code.replace(/atable/g, "table");
		oa_html_code = oa_html_code.replace(/athead/g, "thead");
		oa_html_code = oa_html_code.replace(/atbody/g, "tbody");
		oa_html_code = oa_html_code.replace(/atfoot/g, "tfoot");
		oa_html_code = oa_html_code.replace(/ath/g, "th");
		oa_html_code = oa_html_code.replace(/atr/g, "tr");
		oa_html_code = oa_html_code.replace(/atd/g, "td");
		oa_html_code = oa_html_code.replace(/&gt;/g, ">");
		oa_html_code = oa_html_code.replace(/&lt;/g, "<");
		oa_html_code = oa_html_code.replace(/&quot;/g, "'");
		oa_html_code = oa_html_code.replace(/~lsqb;/g, "[");
		oa_html_code = oa_html_code.replace(/~rsqb;/g, "]");
		oa_html_code = oa_html_code.replace(/=""/g, "");
		console.log(oa_html_code);
		var notes = $("#id_oatm-notes").html();
	    notes = notes.replace(/&lt;/g, '<');
	    notes = notes.replace(/&gt;/g, '>');
	    notes = notes.replace(/\t    /g, '');
	    notes = notes.replace(/\t/g, '');
	    notes = notes.replace(/\n/g, '');

	var registrationDetails = [];
	$(".registration_details").find("div").each(function(){
		if($(this).find("input").is(":checked")) {
			registrationDetails.push({
				"label_id":$(this).find("input").attr("data-field-id"),
				"label":$(this).find("label").text()
			});
		}
	});
	var oaDateTimeFormat = "";
	var oaTo12Hrs = false;
	$("#dateFormatter td").each(function(){
		oaDateTimeFormat += $(this).find("select").val();
	});
	$("#timeFormatter td").each(function(){
		oaDateTimeFormat += $(this).find("select").val();
		if($(this).find("select").val() == " %I") {
			oaTo12Hrs = true;
		}
	});
	if(oaTo12Hrs) {
		oaDateTimeFormat += " %p";
	}
	var oaTemplateDate = {
		"general_config":{
			"font_family": $("#id_oatg-base_font").val(),
			"margin":{
				"top" : $("#id_oatg-margin_top").val(),
				"left" : $("#id_oatg-margin_left").val(),
				"right" : $("#id_oatg-margin_right").val(),
				"bottom" : $("#id_oatg-margin_bottom").val(),
			},
			"page_size":"A4",
			"orientation":"portrait",
			"datetime": oaDateTimeFormat
		},
		"header_config": {
			"company": {
				"logo": {
					"print": $("#id_oath-include_logo").is(":checked"),
					"size": $("#id_oath-logo_size").val(),
				},
				"company_name": {
					"print": $("#id_oath-include_name").is(":checked"),
					"font_family": $("#id_oath-name_font").val(),
					"font_size": $("#id_oath-name_font_size").val(),
				}
			},
			"form_name":{
				'font_size' : $("#id_oath-form_name_font_size").val() ,
				"label":{
					"oa": $("#id_oath-order_acknowledgement_label").val(),
				}
			},
			"font_size":$("#id_oath-font_size").val(),
			"company_info":{
				"print_address":$("#id_oath-include_address").is(":checked"),
				"print_phone_no":$("#id_oath-include_phone_no").is(":checked"),
				"print_email":$("#id_oath-include_email").is(":checked"),
				"print_fax":$("#id_oath-include_fax").is(":checked"),
			},
			"include_reg_items": registrationDetails,
			"field_name":{
				"oa_date":{
					'label' : $("#id_oath-date_label").val()
				},
				"po_no":{
					'print' : $("#id_oath-include_po_no").is(":checked"),
					'label' : $("#id_oath-po_no_label").val()
				},
				"estimation_date":{
					'print' : $("#id_oath-include_estimation_date").is(":checked"),
					'label' : $("#id_oath-estimation_date_label").val()
				},
				"payment_terms":{
					'print' : $("#id_oath-include_paymentterms").is(":checked"),
					'label' : $("#id_oath-paymentterms_label").val()
				},
				"delivery_due_date":{
					'print' : $("#id_oath-include_delivery_due_date").is(":checked"),
					'label' : $("#id_oath-delivery_due_label").val()
				},
				"special_instructions":{
					'print' : $("#id_oath-include_splinstructions").is(":checked"),
					'label' : $("#id_oath-splinstructions_label").val()
				},
				"authorized_sign":{
					'print' : $("#id_oath-include_authorizedsign").is(":checked"),
					'label' : $("#id_oath-authorizedsign_label").val() ,
					'print_approversign' : $("#id_oath-include_approver_signature").is(":checked"),
				},
				"prepared_by":{
					'print' : $("#id_oath-include_preparedsign").is(":checked"),
					'label' : $("#id_oath-preparedsign_label").val() ,
					'print_approversign' : $("#id_oath-include_prepared_signature").is(":checked"),
				},
			},
			"billing_address":{
				'print' : $("#id_oath-include_billingaddress").is(":checked"),
				'label' : $("#id_oath-billingaddress_label").val()
			}
		},
		"items_config":{
			"item_table":{
				"font_size":$("#id_oati-font_size").val(),
				"sno":{
					'print' : $("#id_oati-include_sno").is(":checked"),
					'label' : $("#id_oati-sno_label").val(),
					'width' : $("#id_oati-sno_width").val()
				},
				"item_details":{
					'label' : $("#id_oati-itemdetails_label").val(),
					'width' : $("#id_oati-itemdetails_width").val(),
					'itemcode' : {
						'print' : $("#id_oati-include_itemcode").is(":checked"),
						'label' : $("#id_oati-itemcode_label").val()
					},
					'itemname_label' : $("#id_oati-name_label").val(),
					'make' : {
						'print' : $("#id_oati-include_make").is(":checked"),
						'label' : $("#id_oati-make_label").val()
					},
					'part_no' : {
						'print' : $("#id_oati-include_partno").is(":checked"),
						'label' : $("#id_oati-partno_label").val()
					},
					'description' :{
						'print' : $("#id_oati-include_description").is(":checked"),
						'label' : $("#id_oati-description_label").val(),
					},
				},
				"hsnsac":{
					'print' : $("#id_oati-include_hsnsac").is(":checked"),
					'label' : $("#id_oati-hsnsac_label").val(),
					'width' : $("#id_oati-hsnsac_width").val() ,
					'print_in_itemdetails' :$("#id_oati-hsnsac_part_of_itemdetails").is(":checked"),
				},
				"quantity":{
					'print' : $("#id_oati-include_quantity").is(":checked"),
					'label' : $("#id_oati-quantity_label").val() ,
					'width' : $("#id_oati-quantity_width").val()
				},
				"units":{
					'print' : $("#id_oati-include_units").is(":checked"),
					'label' : $("#id_oati-units_label").val() ,
					'width' : $("#id_oati-units_width").val() ,
					'units_in_qty' : $("#id_oati-units_in_quantity_column").is(":checked")
				},
				"unit_price":{
					'print' : $("#id_oati-include_unit_price").is(":checked"),
					'label' : $("#id_oati-unit_price_label").val(),
					'width' : $("#id_oati-unit_price_width").val()
				},
				"discount":{
					'print' : $("#id_oati-include_discount").is(":checked"),
					'label' : $("#id_oati-discount_label").val(),
					'width' : $("#id_oati-discount_width").val()
				},
				"taxable_amount":{
					'print' : $("#id_oati-include_taxable_amount").is(":checked"),
					'label' : $("#id_oati-taxable_amount_label").val() ,
					'width' : $("#id_oati-taxable_amount_width").val() ,
				}
			},
			"field_alignment":{
				"separator":{
					'row_separator' : $("#id_oati-include_row_separator").is(":checked"),
					'column_separator' : $("#id_oati-include_column_separator").is(":checked"),
					'alternative_row_shading' : $("#id_oati-include_alternate_row_shading").is(":checked")
				},
			}
		},
		"summary_config":{
			"totals":{
				'print' : $("#id_oats-include_total").is(":checked"),
				'font_size' : $("#id_oats-font_size").val() ,
				'print_subtotal' : $("#id_oats-include_subtotal").is(":checked"),
				'print_qtytotal' : $("#id_oats-include_qty_total").is(":checked"),
				'print_total_in_words' : $("#id_oats-include_total_in_words").is(":checked")
			},
			"hsn_summary":{
				'print' : $("#id_oats-include_hsn_summary").is(":checked"),
				'font_size' : $("#id_oats-hsn_tax_font_size").val()
			}
		},
		"misc_config":{
			"banner_header" : {
				"attachment" : [
					{
						"attachment_id" : "",
						"uploaded_on" : "",
						"last_modified_by" : "",
						"width" : "",
						"uploaded_by" : "",
						"position" : "left",
						"last_modified_on" : ""
					},
					{
						"attachment_id" : "",
						"uploaded_on" : "",
						"last_modified_by" : "",
						"width" : "",
						"uploaded_by" : "",
						"position" : "center",
						"last_modified_on" : ""
					},
					{
						"attachment_id" : "",
						"uploaded_on" : "",
						"last_modified_by" : "",
						"width" : "",
						"uploaded_by" : "",
						"position" : "right",
						"last_modified_on" : ""
					}
				],
				"height" : $("#header_banner_size").val()
			},
			"banner_footer" : {
				"attachment" : [
					{
						"attachment_id" : "",
						"uploaded_on" : "",
						"last_modified_by" : "",
						"width" : "",
						"uploaded_by" : "",
						"position" : "left",
						"last_modified_on" : ""
					},
					{
						"attachment_id" : "",
						"uploaded_on" : "",
						"last_modified_by" : "",
						"width" : "",
						"uploaded_by" : "",
						"position" : "center",
						"last_modified_on" : ""
					},
					{
						"attachment_id" : "",
						"uploaded_on" : "",
						"last_modified_by" : "",
						"width" : "",
						"uploaded_by" : "",
						"position" : "right",
						"last_modified_on" : ""
					}
				],
				"height" : $("#footer_banner_size").val()
			},
			"print_pageno": $("#id_oatm-include_page_no_in_footer").is(":checked"),
			"notes":notes,
			"print_summary_first_page": $("#id_oatm-include_first_page_summary").is(":checked"),
			"foot_note":{
				'font_size' : $("#id_oatm-font_size").val(),
				'notes' : $("#id_oatm-foot_note").val(),
			}
		},
		"print_template": oa_html_code,
	}
	return oaTemplateDate;
}
function saveTemplateChanges(){
	oaTemplateDate = constructJSON();
	var oaTemplateHeader = {'header_banner': [{'position': 'left', 'width': $("#id_oatm-header-left_banner_width").val(), 'banner_image': $("#image_uploader-left").val()},
							{'position': 'right', 'width': $("#id_oatm-header-right_banner_width").val(), 'banner_image': $("#image_uploader-right").val()},
							{'position': 'center', 'width': $("#id_oatm-header-center_banner_width").val(), 'banner_image': $("#image_uploader-center").val()}]}

	var oaTemplateFooter = {'footer_banner': [{'position': 'left', 'width': $("#id_oatm-footer-left_banner_width").val(), 'banner_image': $("#footer_uploader-left").val()},
							{'position': 'right', 'width': $("#id_oatm-footer-right_banner_width").val(), 'banner_image': $("#footer_uploader-right").val()},
							{'position': 'center', 'width': $("#id_oatm-footer-center_banner_width").val(), 'banner_image': $("#footer_uploader-center").val()}]}
	var oa_data = JSON.stringify(oaTemplateDate);
	var oa_header =  JSON.stringify(oaTemplateHeader);
	var oa_footer = JSON.stringify(oaTemplateFooter);
	$.ajax({
		url:"/erp/admin/oa_template/save/",
		type:"POST",
		datatype:"json",
		async: true,
		data: {'data': oa_data, 'header_banner': oa_header, 'footer_banner': oa_footer},
		success:function(response){
			if (typeof response.custom_message === 'undefined') {
				location.reload();
			}
			else if(response.response_message == 'Success') {
                var oaText = "OA configuration saved successfully";
                swal({
                        title: "",
                        text: oaText,
                        type: "success"
                      },
                      function(){
                        window.location.reload();
                });
			}
			else {
				swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
			}
		},
		error: function (xhr, errmsg, err) {
            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
        }
    });
}

</script>
{% endblock %}