import datetime
from conf import *
import os

# Set up logger
logger = logging.getLogger(os.path.splitext(os.path.basename(__file__))[0])
logger.setLevel(logging.DEBUG)
logger.addHandler(logHandler)


def create_folder():
    if not os.path.exists("tnp"):
        os.makedirs("tnp")
        return os.path.abspath("tnp")
    else:
        return os.path.abspath("tnp")


def dc_reminder_email(to_email, return_date, invoice_code, file_path):
    body = """
    Hi, <br>
    <br>
    Just a friendly reminder that today,(<b>Date : %s</b>) is the deadline to return delivery challan (<b>No : %s</b>) Please ensure it is returned by the end of the day.<br>
    Please find the attached DC PDF for your reference.<br>
    <br>
    Thank you.""" % (return_date, invoice_code)

    _mail_body = """<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
    <html xmlns="http://www.w3.org/1999/xhtml">
        <body style="font-size:12px;font-family:Arial">
            <p>{body}</p>
        </body>
    </html>""".format(
        body=body)
    recipients = [to_email]
    subject = "Delivery Challan(%s)-Reminder" % invoice_code
    _file_list = []
    dc_file = {"name": 'DC(%s).pdf' % invoice_code, "path": file_path}
    _file_list.append(dc_file)
    res = sendMail(recipients=recipients, body=_mail_body, subject=subject, cc_list=[], files=_file_list)
    return res


def create_pdf_from_blob(blob_data, output_file_path):
    with open(output_file_path, 'wb') as pdf_file:
        pdf_file.write(blob_data)


def pdf_remove(file_path):
    os.remove(file_path)


def verify_prepare_and_approval_email(prepared_email, approved_email, return_date, invoice_code, file_path):
    if prepared_email == approved_email and dc_reminder_email(prepared_email, return_date, invoice_code,
                                                              file_path):
        pdf_remove(file_path)
    elif not prepared_email and not approved_email:
        pass
    elif not prepared_email and dc_reminder_email(approved_email, return_date, invoice_code, file_path):
        pdf_remove(file_path)

    elif not approved_email and dc_reminder_email(prepared_email, return_date, invoice_code, file_path):
        pdf_remove(file_path)

    elif dc_reminder_email(prepared_email, return_date, invoice_code, file_path) and dc_reminder_email(
            approved_email, return_date, invoice_code, file_path):
        pdf_remove(file_path)


def fetch_returnable_status():
    return_date = datetime.datetime.today().strftime('%Y-%m-%d')
    query = """
    SELECT DISTINCT
        i.invoice_code,
        i.id,
        prepared_by_user.email AS prepared_email,
        approved_by_user.email AS approved_email
    FROM
        invoice_materials im
    JOIN 
        invoice i ON im.invoice_id = i.id
    JOIN
        auth_user prepared_by_user ON i.prepared_by = prepared_by_user.id
    JOIN
        auth_user approved_by_user ON i.approved_by = approved_by_user.id
    WHERE 
        i.return_date = '%s' AND im.is_returnable = 1 AND i.status = 1;
    """ % (return_date,)
    try:
        dc_data = executeQuery(query=query, as_dict=True)
        if dc_data:
            for row in dc_data:
                invoice_code = row['invoice_code']
                invoice_id = row['id']
                prepared_email = row['prepared_email']
                approved_email = row['approved_email']
                doc_query = """
                            SELECT
                                document_pdf
                            FROM
                                invoice_document
                            WHERE
                                invoice_id = '%s'
                            """ % (invoice_id,)
                document_pdf = executeQuery(query=doc_query)
                logger.info(" return_date : %s, invoice_code : %s, invoice_id : %s, prepared email : %s, approved email : %s",
                            return_date, invoice_code, invoice_id, prepared_email, approved_email)
                blob_data = document_pdf[0][0]
                dc_invoice_code = invoice_code.replace('/', '_')
                folder_dir = create_folder()
                file_path = '%s/DC(%s).pdf' % (folder_dir, dc_invoice_code)
                create_pdf_from_blob(blob_data, file_path)

                verify_prepare_and_approval_email(prepared_email, approved_email, return_date, invoice_code,
                                                  file_path)

    except Exception as e:
        logger.error("An error occurred while fetching data from MySQL: %s", e)


if __name__ == "__main__":
    fetch_returnable_status()
