import ast
import json
from datetime import datetime
from decimal import Decimal

import pymysql
import pymysql.connections
import pymysql.converters
import simplejson
import xlrd
import itertools
import requests
from django.http import HttpResponse
from django.http import HttpResponseRedirect
from django.template.response import TemplateResponse
from sqlalchemy import and_
from sqlalchemy.orm import make_transient

from erp import properties, helper
from erp.accounts import logger, BANK_VOUCHER
from erp.accounts.backend import AccountsDAO, AccountService, GeneralVouchersBulkUpload
from erp.accounts.changelog import VoucherChangelog
from erp.accounts.reports import GSTR1, GSTR2
from erp.auth import USER_IN_SESSION_KEY, ENTERPRISE_IN_SESSION_KEY, SESSION_KEY, HOME_CURRENCY_KEY, \
	ENTERPRISE_ID_SESSION_KEY, PRIMARY_ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.dao import executeQuery
from erp.forms import VoucherForm
from erp.formsets import VoucherParticularFormset, LedgerBillFormset, TagFormset
from erp.helper import validateFileHeaders, populateAccountLedgerChoices, populateBankLedgerChoices, \
	populateCashLedgerChoices, getAccountGroupIDs, CustomJSONEncoder, validate_payload_with_file
from erp.masters.party_views import OpeningVoucherConstructionFailure
from erp.models import Voucher, VoucherParticulars, LedgerBill, VoucherTag, Ledger, \
	AccountGroup, VoucherType, LedgerBillSettlement, Project
from erp.properties import MANAGE_VOUCHER_TEMPLATE, TEMPLATE_TITLE_KEY, MANAGE_PAYMENT_TEMPLATE, \
	MANAGE_VOUCHER_LIST_TEMPLATE, GSTIN_STATE_CODE
from erp.tags import extractEntityTagMapsFromFormset, generateTagFormset
from settings import HOST, USER, PASSWORD, DBNAME, PORT, SQLASession, GST_EMAIL, GST_API_EMAIL, GST_API_CLIENT_ID, GST_API_CLIENT_SECRET_ID
from util.api_util import JsonUtil, response_code
from util.helper import constructFormInitializer, getFinancialYear, set_datetime, compare_string
from util.reports_query_builder import GSTR1Report, GSTR2Report
from erp.accounts.reconciliation_engine import ReconciliationEngine
from erp.properties import GSTIN_STATE_TITLE
from erp.sales.backend import InternalInvoiceService

__author__ = 'saravanan'

PROJECTS_KEY = 'projects'


def manageVoucher(request):
	"""
	Renders the main page to manage Voucher.
	:param request:
	:return:
	"""
	logger.info('Inside Manage Voucher... ')
	request_handler = RequestHandler(request)
	try:
		from_date, to_date = JsonUtil.getDateRange(
			rh=request_handler, since_session_key="voucher_since", till_session_key="voucher_till")
		logger.info("From Date:%s to Date:%s" % (from_date, to_date))
		type_id = request_handler.getPostData('voucher_type_no')
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		last_created_voucher_code = request_handler.getSessionAttribute("last_created_voucher")
		request_handler.removeSessionAttribute("last_created_voucher")
		last_saved_voucher_error = request_handler.getSessionAttribute("last_saved_voucher_error")
		request_handler.removeSessionAttribute("last_saved_voucher_error")
		dr_cr_group_ids = getAccountGroupIDs(enterprise_id=enterprise_id, names=[
			AccountGroup.SUNDRY_DEBTOR_GROUP_NAME, AccountGroup.SUNDRY_CREDITOR_GROUP_NAME])
		return TemplateResponse(template=MANAGE_VOUCHER_TEMPLATE, request=request, context={
			'projects': helper.populateProjectChoices(),
			'voucher_type': helper.populateVoucherType(exclude_none=False),
			'voucherForm': VoucherForm(enterprise_id=enterprise_id, prefix='voucher', initial={
				'enterprise_id': enterprise_id, 'voucher_date': datetime.today()}),
			'voucher_particular_formset': VoucherParticularFormset(initial=[], prefix='v_particular'),
			'ledger_bill_formset': LedgerBillFormset(initial=[], prefix='ledger_bill'),
			'last_created_voucher': last_created_voucher_code,
			'last_saved_voucher_error': last_saved_voucher_error,
			'tags_formset': TagFormset(initial=[], prefix='tag'),
			'home_currency': request_handler.getSessionAttribute(HOME_CURRENCY_KEY),
			'to_date': to_date.strftime("%Y-%m-%d"), 'from_date': from_date.strftime("%Y-%m-%d"),
			'search_voucher_type': type_id,
			'dr_cr_group_ids': ",".join(["%s" % gid for gid in dr_cr_group_ids]),
			TEMPLATE_TITLE_KEY: 'Voucher'})
	except Exception as e:
		logger.exception('Voucher Error : %s' % e)
		logger.info("Error %s" % Exception)


def manageVoucherList(request):
	"""
	Renders the main page to manage Voucher.
	:param request:
	:return:
	"""
	logger.info('Inside Manage Voucher List... ')
	request_handler = RequestHandler(request)
	post_from_date = str(request_handler.getPostData('voucher_fromdate'))
	view_type = 'pending' if request_handler.getPostData('voucher_fromdate') is not None else None
	try:
		from_date, to_date = JsonUtil.getDateRange(
			rh=request_handler, since_session_key="voucher_since", till_session_key="voucher_till")
		logger.info("From Date:%s to Date:%s" % (from_date, to_date))
		from_date = datetime.strptime(post_from_date, "%Y-%m-%d") if post_from_date != 'None' else from_date
		type_id = request_handler.getPostData('voucher_type_no')
		return TemplateResponse(template=MANAGE_VOUCHER_LIST_TEMPLATE, request=request, context={
			'projects': helper.populateProjectChoices(), 'voucher_type': helper.populateVoucherType(exclude_none=True),
			'to_date': to_date.strftime("%Y-%m-%d"), 'from_date': from_date.strftime("%Y-%m-%d"),
			'search_voucher_type': type_id, 'view_type': view_type,
			TEMPLATE_TITLE_KEY: 'Voucher'})
	except Exception as e:
		logger.exception('Voucher Error : %s' % e)
		logger.info("Error %s" % Exception)


def populateVoucherList(request):
	"""
	Loads the Voucher List with choices appropriate for the enterprise in context.

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	from_date, to_date = JsonUtil.getDateRange(
		rh=request_handler, since_session_key="voucher_since", till_session_key="voucher_till")
	logger.info("From Date:%s to Date:%s" % (from_date, to_date))
	type_id = request_handler.getPostData('voucher_type_no')
	status = request_handler.getPostData('view_type') if request_handler.getPostData('view_type') != '' else None
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	bank_group_ids = getAccountGroupIDs(enterprise_id=enterprise_id, names=[
		AccountGroup.BANK_GROUP_NAME, AccountGroup.LT_BORROWINGS_GROUP_NAME, AccountGroup.ST_BORROWINGS_GROUP_NAME])

	vouchers = SQLASession().query(
		Voucher.id.label('id'),
		Voucher.voucher_no.label('voucher_no'),
		Voucher.sub_number.label('sub_number'),
		Voucher.financial_year.label('financial_year'),
		Voucher.narration.label('narration'),
		Voucher.type_id.label('type_id'),
		Voucher.voucher_date.label('voucher_date'),
		VoucherType.code.label('type_code'),
		VoucherType.name.label('type_name'),
		Ledger.name.label("ledger_name"),
		Voucher.approved_on.label('approved_on'),
		Project.name.label("project_name"),
		Project.code.label("project_label")
	).outerjoin(Voucher.type).outerjoin(
		VoucherParticulars, and_(
			VoucherParticulars.enterprise_id == Voucher.enterprise_id, VoucherParticulars.voucher_id == Voucher.id,
			Voucher.type_id == BANK_VOUCHER)
	).outerjoin(
		Ledger, and_(
			VoucherParticulars.ledger_id == Ledger.id, VoucherParticulars.enterprise_id == Ledger.enterprise_id,
			Ledger.group_id.in_(bank_group_ids))
	).outerjoin(Project, and_(Project.id == Voucher.project_code)).filter(
		Voucher.voucher_date >= from_date, Voucher.voucher_date <= to_date, Voucher.enterprise_id == enterprise_id)
	if status is not None:
		vouchers = vouchers.filter(Voucher.status == 0)
	if type_id and (type_id != "None"):
		vouchers = vouchers.filter(Voucher.type_id == type_id)
	vouchers = vouchers.group_by(Voucher.id).order_by(Voucher.voucher_date, Voucher.type_id, Voucher.voucher_no).all()
	vouchers_list = []
	for voucher in vouchers:
		vouchers_list_dict = dict()
		vouchers_list_dict["id"] = voucher.id
		vouchers_list_dict["narration"] = voucher.narration
		vouchers_list_dict["type_id"] = voucher.type_id
		vouchers_list_dict["getCode"] = Voucher.generateCode(
			voucher_id=voucher.id, voucher_no=voucher.voucher_no, sub_number=voucher.sub_number,
			financial_year=voucher.financial_year, voucher_type=voucher.type_code)
		vouchers_list_dict["voucher_date"] = voucher.voucher_date.strftime('%Y-%m-%d')
		vouchers_list_dict["name"] = voucher.type_name
		if voucher.approved_on and (voucher.approved_on != "None"):
			vouchers_list_dict["approved_on"] = voucher.approved_on.strftime('%Y-%m-%d')
		if voucher.type_id == BANK_VOUCHER and voucher.ledger_name is not None:
			vouchers_list_dict["name"] = "%s || [%s]" % (voucher.type_name, voucher.ledger_name)
		if voucher.project_name and voucher.project_label:
			vouchers_list_dict["project_name"] = voucher.project_name + "(" + voucher.project_label + ")"
		else:
			vouchers_list_dict["project_name"] = "-NA-"
		vouchers_list.append(vouchers_list_dict)
	return HttpResponse(content=simplejson.dumps(vouchers_list), mimetype='application/json')


def saveVoucher(request):
	"""
	Save Voucher
	Persists them in appropriate tables after validation.

	:param request:
	:return:
	"""
	logger.info('Inside Save Vouchers...%s' % request.session.session_key)
	request_handler = RequestHandler(request)
	account_service = AccountService()
	db_session = account_service.accounts_dao.db_session
	db_session.begin(subtransactions=True)
	pre_voucher_approval = False
	primary_enterprise_id = request_handler.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
	is_voucher_approve_first = False
	voucher_id = None
	try:
		enterprise = request_handler.getSessionAttribute(ENTERPRISE_IN_SESSION_KEY)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		voucher_form = VoucherForm(data=request_handler.getPostData(), prefix='voucher', enterprise_id=enterprise.id)
		voucher_particulars_formset = VoucherParticularFormset(request_handler.getPostData(), prefix='v_particular')
		voucher_tag_formset = generateTagFormset(request_handler.getPostData())
		ledger_bill_formset = LedgerBillFormset(request_handler.getPostData(), prefix='ledger_bill')
		if voucher_form.is_valid() and (voucher_particulars_formset and voucher_particulars_formset.is_valid()) and (
				not voucher_tag_formset or voucher_tag_formset.is_valid()):
			logger.info("Valid Voucher Form")
			voucher_id = voucher_form.cleaned_data['id']
			if voucher_id is not None and voucher_id != '':
				voucher_to_be_saved = account_service.accounts_dao.getVoucher(
					enterprise_id=enterprise.id, voucher_id=voucher_id)
				if voucher_to_be_saved.status != 0 and voucher_to_be_saved.type_id != int(
						voucher_form.cleaned_data['type_id']):
					response = account_service.superEditVoucherCode(
						enterprise_id=enterprise.id, user_id=user_id, voucher_id=voucher_id,
						new_financial_year=voucher_to_be_saved.financial_year,
						new_voucher_type_id=voucher_form.cleaned_data['type_id'],
						new_voucher_no=voucher_to_be_saved.voucher_no,
						new_sub_number=voucher_to_be_saved.sub_number)
					if response['response_message'] != "Success":
						request_handler.setSessionAttribute("last_saved_voucher_error", response['custom_message'])
						request_handler.setSessionAttribute("voucher_no", voucher_to_be_saved.id)
						return HttpResponseRedirect(properties.MANAGE_VOUCHER_EDIT_URL)
				account_service.accounts_dao.db_session.refresh(voucher_to_be_saved)
				_copyVoucherFormToEntity(voucher_form, voucher_to_be_saved)
				if voucher_to_be_saved.project_automation_status in (1,-1):
					voucher_to_be_saved.project_automation_status = 2
			else:
				voucher_to_be_saved = _getEntityFromVoucherForm(voucher_form)
			logger.info('Saving Particulars... %d' % len(voucher_particulars_formset))
			voucher_to_be_saved.particulars = _saveVoucherParticulars(
				voucher_particulars_formset, voucher_form.cleaned_data['id'], voucher_to_be_saved, db_session)
			logger.info("Form Valid %s" % (ledger_bill_formset.is_valid()))
			# Ledger Bill Save
			_saveledgerBills(ledger_bill_formset, voucher_to_be_saved, db_session)
			_saveTags(voucher_tag_formset, voucher_to_be_saved, db_session)
			time_now = datetime.now()
			if not voucher_to_be_saved.created_by:
				voucher_to_be_saved.created_on = time_now.strftime('%Y-%m-%d %H:%M:%S')
				voucher_to_be_saved.created_by = request_handler.getSessionAttribute(SESSION_KEY)
			logger.info("Voucher no: %s | Voucher Status: %s | Approval: %s" % (
				voucher_to_be_saved.voucher_no, voucher_to_be_saved.status,
				(voucher_to_be_saved.voucher_no is None or int(
					voucher_to_be_saved.voucher_no) == 0 or voucher_to_be_saved.voucher_no == '')
				and (voucher_to_be_saved.status == 1)))
			if ((voucher_to_be_saved.voucher_no is None) or (int(
					voucher_to_be_saved.voucher_no) == 0) or (voucher_to_be_saved.voucher_no == '')) and (
					voucher_to_be_saved.status == 1):
				account_service.prepareVoucherApproval(
					voucher_to_be_saved=voucher_to_be_saved, enterprise=enterprise,
					approved_by=request_handler.getSessionAttribute(SESSION_KEY))
				pre_voucher_approval = True
				is_voucher_approve_first = True
				voucher_id = voucher_to_be_saved.id
			db_session.add(voucher_to_be_saved)
			logger.debug('Committing all transactions after successful addition of entities... %s' % db_session.dirty)
			db_session.commit()
			if voucher_form.cleaned_data['id'] is None:
				AccountService().notifyPendingVoucherCount(
					enterprise_id=enterprise.id, sender_id=user_id,
					code=voucher_to_be_saved.getCode(),type=voucher_to_be_saved.type)
			if pre_voucher_approval is True:
				AccountService().notifyPendingVoucherCount(
					enterprise_id=enterprise.id, sender_id=voucher_to_be_saved.approved_by,
					code=voucher_to_be_saved.getCode(), voucher_count=1)

			logger.info("Voucher No: %s" % voucher_to_be_saved.getCode())
			VoucherChangelog().queryVoucherInsert(
				user_id=user_id, enterprise_id=enterprise.id, data=voucher_to_be_saved)
			request_handler.setSessionAttribute("last_created_voucher", voucher_to_be_saved.getCode())
			request_handler.setSessionAttribute("voucher_no", voucher_to_be_saved.id)
		else:
			logger.info(
				"Voucher Form Validation Failed! Voucher Errors: %s Particulars Formset Errors: %s Tags Formset Errors: %s" % (
					voucher_form.errors, voucher_particulars_formset.errors, voucher_tag_formset.errors))
			db_session.rollback()
			return TemplateResponse(template=MANAGE_VOUCHER_TEMPLATE, request=request, context={
				'voucherForm': voucher_form, 'home_currency': request_handler.getSessionAttribute(HOME_CURRENCY_KEY),
				'voucher_particular_formset': voucher_particulars_formset, 'tags_formset': voucher_tag_formset})
		if is_voucher_approve_first and enterprise.id == primary_enterprise_id and voucher_id:
			internal_invoice_service = InternalInvoiceService()
			internal_invoice_service.approveInternalVoucher(voucher_id=voucher_id, user_id=user_id)
	except Exception as e:
		db_session.rollback()
		logger.exception(e)
	return HttpResponseRedirect(properties.MANAGE_VOUCHER_EDIT_URL)


def getVoucherLogList(request):
	"""
	get Specified voucher log list
	:return:
	"""
	response = {}
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	voucher_id = request_handler.getPostData('voucher_id')
	try:
		response = VoucherChangelog().fetchLogList(voucher_id=voucher_id, enterprise_id=enterprise_id)
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getVoucherLogData(request):
	"""
	get changelog data from a specified voucher timeset
	:param request:
	:return:
	"""
	response = {}
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	voucher_id = request_handler.getPostData('voucher_id')
	modified_at = request_handler.getPostData('modified_at')
	try:
		response = VoucherChangelog().fetchLogData(voucher_id=voucher_id, enterprise_id=enterprise_id, modified_at=modified_at)
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def _saveVoucherParticulars(voucher_particulars, voucher_id, voucher_to_be_saved, db_session):
	"""
	Save the Voucher Particulars Formset Details
	:param voucher_particulars:
	:return:
	"""
	particulars_to_be_saved = []
	for particulars_form in voucher_particulars:
		logger.info('Voucher Particular Voucher ID: %s Item_no: %s Ledger ID: %s ' % (
			particulars_form.cleaned_data['voucher_id'], particulars_form.cleaned_data['item_no'],
			particulars_form.cleaned_data['ledger_id']))
		voucher_particular = db_session.query(VoucherParticulars).filter(
			VoucherParticulars.voucher_id == voucher_id,
			VoucherParticulars.item_no == particulars_form.cleaned_data['item_no'],
			VoucherParticulars.ledger_id == particulars_form.cleaned_data['ledger_id']).first()
		if not particulars_form.cleaned_data['DELETE']:
			logger.info("Delete Flag : %s" % particulars_form.cleaned_data['DELETE'])
			if voucher_particular is None:
				voucher_particular = VoucherParticulars(
					voucher_id=voucher_id, item_no=particulars_form.cleaned_data['item_no'],
					ledger_id=particulars_form.cleaned_data['ledger_id'],
					enterprise_id=particulars_form.cleaned_data['enterprise_id'])

			voucher_particular.amount = particulars_form.cleaned_data['amount']
			voucher_particular.is_debit = particulars_form.cleaned_data['is_debit']
			particulars_to_be_saved.append(voucher_particular)
	particulars_to_be_removed = set(voucher_to_be_saved.particulars) - set(particulars_to_be_saved)
	logger.info("Voucher Particulars to be removed: %s" % particulars_to_be_removed)
	for particular in particulars_to_be_removed:
		db_session.delete(particular)
		if particular:
			for bill in particular.ledger.bills:
				if bill.voucher_id == particular.voucher_id:
					db_session.delete(bill)
				else:
					for settlement in bill.settlements:
						if settlement.voucher_id == particular.voucher_id:
							db_session.delete(settlement)
	return particulars_to_be_saved


def _saveledgerBills(ledger_bill_formset, voucher_to_be_saved, db_session):
	"""
	Save to Ledger bills to specific voucher

	:param ledger_bill_formset:
	:param voucher_to_be_saved:
	:param db_session:
	:return:
	"""
	if ledger_bill_formset is not None and ledger_bill_formset.is_valid():
		logger.info('Saving Ledger Bill... %d' % ledger_bill_formset.__len__())
		for ledger_bill_form in ledger_bill_formset:
			logger.debug("Ledger Bill ID:%s DELETE:%s" % (
				ledger_bill_form.cleaned_data['bill_id'], ledger_bill_form.cleaned_data['DELETE']))
			if not ledger_bill_form.cleaned_data['DELETE']:
				if ledger_bill_form.cleaned_data['bill_id'] and ledger_bill_form.cleaned_data['bill_id'] != 0:
					ledger_bill = db_session.query(LedgerBill).filter(
						LedgerBill.id == ledger_bill_form.cleaned_data['bill_id']).first()
					ledger_bill.bill_date = ledger_bill_form.cleaned_data['bill_date']
					ledger_bill.bill_no = ledger_bill_form.cleaned_data['bill_no'].encode("utf-8")
				else:
					ledger_bill = LedgerBill(
						bill_no=ledger_bill_form.cleaned_data['bill_no'],
						bill_date=ledger_bill_form.cleaned_data['bill_date'],
						ledger_id=ledger_bill_form.cleaned_data['ledger_id'],
						net_value=Decimal(ledger_bill_form.cleaned_data['net_value']),
						is_debit=ledger_bill_form.cleaned_data['is_debit'],
						enterprise_id=ledger_bill_form.cleaned_data['enterprise_id'])
					ledger_bill.voucher = voucher_to_be_saved
				net_debit = 0
				net_credit = 0
				for settlement in ledger_bill.settlements:
					net_debit += settlement.dr_value
					net_credit += settlement.cr_value
				logger.debug("Settlement: Debit - %s | Credit - %s" % (
					Decimal(ledger_bill_form.cleaned_data['debit_settlement']),
					Decimal(ledger_bill_form.cleaned_data['credit_settlement'])))
				ledger_bill_settlement = None
				if ledger_bill.id and voucher_to_be_saved.id:
					ledger_bill_settlement = db_session.query(LedgerBillSettlement).filter(
						LedgerBillSettlement.bill_id == ledger_bill.id,
						LedgerBillSettlement.voucher_id == voucher_to_be_saved.id).first()
				if Decimal(ledger_bill_form.cleaned_data['debit_settlement']) > 0 or Decimal(
						ledger_bill_form.cleaned_data['credit_settlement']) > 0:
					if not ledger_bill_settlement:
						ledger_bill_settlement = LedgerBillSettlement()
						ledger_bill_settlement.voucher = voucher_to_be_saved
						ledger_bill_settlement.bill = ledger_bill
					else:
						net_debit -= ledger_bill_settlement.dr_value
						net_credit -= ledger_bill_settlement.cr_value
					ledger_bill_settlement.dr_value = Decimal(ledger_bill_form.cleaned_data['debit_settlement'])
					ledger_bill_settlement.cr_value = Decimal(ledger_bill_form.cleaned_data['credit_settlement'])
					net_debit += Decimal(ledger_bill_form.cleaned_data['debit_settlement'])
					net_credit += Decimal(ledger_bill_form.cleaned_data['credit_settlement'])
					ledger_bill_settlement.enterprise_id = ledger_bill.enterprise_id
				elif ledger_bill_settlement:
					net_debit -= ledger_bill_settlement.dr_value
					net_credit -= ledger_bill_settlement.cr_value
					db_session.delete(ledger_bill_settlement)
				ledger_bill.net_value = round(abs(net_debit - net_credit), 2)
				ledger_bill.is_debit = (net_debit >= net_credit)


def _saveTags(voucher_tag_formset, voucher_to_be_saved, db_session):
	"""

	:param voucher_to_be_saved:
	:param db_session:
	:return:
	"""
	[db_session.delete(tag) for tag in voucher_to_be_saved.tags]
	voucher_to_be_saved.tags = extractEntityTagMapsFromFormset(
		tag_formset=voucher_tag_formset, enterprise_id=voucher_to_be_saved.enterprise_id, map_class=VoucherTag)


def editVoucher(request):
	"""
	Renders the Voucher Form loaded with the details of the Voucher chosen for edit identified by the voucher_no

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	logger.info("Editing a Voucher... Voucher Id selected Type: %s" % request_handler.getPostData())
	voucher_id = request_handler.getPostData('voucher_no')
	clone = request_handler.getPostData('cloneStatus')
	if not voucher_id:
		voucher_id = request_handler.getSessionAttribute('voucher_no')
	request_handler.removeSessionAttribute('voucher_no')
	last_created_voucher = request_handler.getSessionAttribute("last_created_voucher")
	request_handler.removeSessionAttribute("last_created_voucher")
	last_saved_voucher_error = request_handler.getSessionAttribute("last_saved_voucher_error")
	request_handler.removeSessionAttribute("last_saved_voucher_error")
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	dr_cr_group_ids = getAccountGroupIDs(enterprise_id=enterprise_id, names=[
		AccountGroup.SUNDRY_DEBTOR_GROUP_NAME, AccountGroup.SUNDRY_CREDITOR_GROUP_NAME])
	try:
		voucher_under_edit = SQLASession().query(Voucher).filter(Voucher.id == voucher_id).first()
		template_title = voucher_under_edit.getCode()
		voucher_form = VoucherForm(
			initial=_getVoucherFormInitializerFromEntity(voucher_under_edit), prefix='voucher',
			enterprise_id=voucher_under_edit.enterprise_id)
		voucher_particular_forms = []
		ledger_bill_forms = []
		time_now = datetime.now()
		clone_view = "disable"
		if clone == "enable":
			template_title = 'Voucher'
			change_clone_fields = _getVoucherFormInitializerFromEntity(voucher_under_edit)
			change_clone_fields['voucher_date'] = time_now.strftime('%Y-%m-%d %H:%M:%S')
			change_clone_fields['code'] = ''
			change_clone_fields['transaction_instrument_no'] = ''
			change_clone_fields['narration'] = ''
			clone_view = "enable"
			voucher_form = VoucherForm(
				initial=change_clone_fields, prefix='voucher',
				enterprise_id=voucher_under_edit.enterprise_id)
			for voucher_particular in voucher_under_edit.particulars:
				voucher_particular_list = _getVoucherParticularsFormInitializerFromEntity(voucher_particular)
				voucher_particular_list['voucher_id'] = ''
				if voucher_particular_list.get('billable'):
					voucher_particular_list['bills'] = []
				voucher_particular_forms.append(voucher_particular_list)
		else:
			for voucher_particular in voucher_under_edit.particulars:
				voucher_particular_forms.append(_getVoucherParticularsFormInitializerFromEntity(voucher_particular))
		voucher_particular_formset = VoucherParticularFormset(
			initial=voucher_particular_forms, prefix='v_particular')
		ledger_bill_formset = LedgerBillFormset(initial=ledger_bill_forms, prefix='ledger_bill')
		logger.debug('Readying template for rendering...')
		response = TemplateResponse(template=MANAGE_VOUCHER_TEMPLATE, request=request, context={
			'last_created_voucher': last_created_voucher, 'last_saved_voucher_error': last_saved_voucher_error,
			'voucher_type': helper.populateVoucherTypeWithCode(exclude_header=True),
			'vouchers': [], 'voucherForm': voucher_form, 'voucher_particular_formset': voucher_particular_formset,
			'ledger_bill_formset': ledger_bill_formset, 'home_currency': voucher_under_edit.enterprise.home_currency,
			'tags_formset': generateTagFormset(tags=voucher_under_edit.tags),
			'selected_project_code': None if not voucher_id else voucher_under_edit.project_code,
			'clone_view': clone_view,
			'dr_cr_group_ids': ",".join(["%s" % gid for gid in dr_cr_group_ids]),
			TEMPLATE_TITLE_KEY: template_title})
		make_transient(voucher_under_edit)
		return response
	except Exception as e:
		logger.exception("Could not edit voucher with id %s due to %s" % (voucher_id, e.message))
	return HttpResponseRedirect(properties.MANAGE_VOUCHER_URL)


def deleteVoucher(request):
	"""
	Soft Delete of Voucher - setting the status to 0

	:param request:
	:return:
	"""
	logger.info('Deleting a Voucher...')
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	db_session = SQLASession()
	db_session.begin(subtransactions=True)
	try:
		delete_voucher_no = request_handler.getPostData('deleteCode')
		voucher_to_be_deleted = db_session.query(Voucher).filter(
			Voucher.voucher_no == delete_voucher_no, Voucher.enterprise_id == enterprise_id).first()
		db_session.delete(voucher_to_be_deleted)
		db_session.commit()
	except Exception as e:
		db_session.rollback()
		logger.exception('Failed deleting a Voucher: %s' % e.message)
	return HttpResponseRedirect(properties.MANAGE_VOUCHER_URL)


def _copyVoucherFormToEntity(form, entity):
	entity.id = form.cleaned_data['id']
	entity.type_id = form.cleaned_data['type_id']
	entity.voucher_date = form.cleaned_data['voucher_date']
	entity.narration = form.cleaned_data['narration']
	entity.transaction_instrument_no = form.cleaned_data['transaction_instrument_no']
	entity.transaction_description = form.cleaned_data['transaction_description']
	entity.project_code = form.cleaned_data['project_code']
	entity.enterprise_id = form.cleaned_data['enterprise_id']
	entity.status = form.cleaned_data['status']


def _getEntityFromVoucherForm(form):
	logger.info("Entity Voucher No '%s'" % form.cleaned_data['voucher_no'])
	entity = Voucher(
		type_id=form.cleaned_data['type_id'], voucher_date=form.cleaned_data['voucher_date'],
		narration=form.cleaned_data['narration'], project_code=form.cleaned_data['project_code'],
		transaction_instrument_no=form.cleaned_data['transaction_instrument_no'],
		transaction_description=form.cleaned_data['transaction_description'],
		enterprise_id=form.cleaned_data['enterprise_id'], status=form.cleaned_data['status'])
	return entity


def _getVoucherFormInitializerFromEntity(entity):
	initializer = constructFormInitializer(entity)
	initializer['code'] = entity.getCode()
	return initializer


def _getVoucherParticularsFormInitializerFromEntity(entity):
	initializer = constructFormInitializer(entity)
	initializer["ledger_opening"] = "0 Cr"
	initializer["ledger_label"] = "%s" % entity.ledger
	initializer["ledger_group"] = "%s" % entity.ledger.group.name
	initializer["billable"] = entity.ledger.billable
	bills = ""
	for bill in entity.voucher.bill_settlements:
		if bill.bill.ledger_id == entity.ledger_id:
			bills = "%s %s" % (bills, bill.bill.bill_no)
	initializer["bills"] = bills
	return initializer


def _copyVoucherParticularsFormToEntity(voucher_particulars_form, voucher_particulars_entity):
	logger.info('Voucher Particulars CLEANED DATA: %s' % voucher_particulars_form.cleaned_data)
	if voucher_particulars_entity is not None:
		voucher_particulars_entity.voucher_id = voucher_particulars_form.cleaned_data['voucher_id']
		voucher_particulars_entity.ledger_id = voucher_particulars_form.cleaned_data['ledger_id']
		voucher_particulars_entity.item_no = voucher_particulars_form.cleaned_data['item_no']
		voucher_particulars_entity.is_debit = voucher_particulars_form.cleaned_data['is_debit']
		voucher_particulars_entity.amount = voucher_particulars_form.cleaned_data['amount']
		voucher_particulars_entity.enterprise_id = voucher_particulars_form.cleaned_data['enterprise_id']

	else:
		voucher_particulars_entity = _getEntityFromVoucherParticularsForm(voucher_particulars_form)
	return voucher_particulars_entity


def _getEntityFromVoucherParticularsForm(voucher_particulars_form):
	voucher_particulars_entity = VoucherParticulars(
		voucher_id=voucher_particulars_form.cleaned_data['voucher_id'],
		item_no=voucher_particulars_form.cleaned_data['item_no'],
		ledger_id=voucher_particulars_form.cleaned_data['ledger_id'],
		is_debit=voucher_particulars_form.cleaned_data['is_debit'],
		amount=voucher_particulars_form.cleaned_data['amount'],
		enterprise_id=voucher_particulars_form.cleaned_data['enterprise_id'])
	return voucher_particulars_entity


def _getEntityFromLedgerBillForm(ledger_bill_form):
	ledger_bill_entity = LedgerBill(
		bill_no=ledger_bill_form.cleaned_data['bill_no'], enterprise_id=ledger_bill_form.cleaned_data['enterprise_id'],
		bill_date=ledger_bill_form.cleaned_data['bill_date'], ledger_id=ledger_bill_form.cleaned_data['ledger_id'])
	return ledger_bill_entity


def load_voucher_no(request, voucher_type):
	"""
	Fetches the latest voucher_no for the particular type of Voucher

	:param request:
	:param voucher_type:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		voucher_no_query = "select Max(voucher_no) FROM voucher where enterprise_id='%s' and type ='%s' " % (
			enterprise_id, voucher_type)

		latest_voucher_no = executeQuery(voucher_no_query)
		logger.info('Latest Voucher No: %s' % latest_voucher_no)
	except Exception as e:
		logger.exception('%s' % e)
		latest_voucher_no = ""
	return HttpResponse(content=simplejson.dumps(latest_voucher_no), mimetype='application/json')


def gstr1PurchaseReportTbl(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	from_date = request_handler.getPostData('from_date') + ' 00:00:00'
	to_date = request_handler.getPostData('to_date') + ' 23:59:00'
	rep_type = request_handler.getPostData('type')
	if rep_type == 'gstr2':
		condition = "ORDER BY g_enterprise_id, p.party_name, grn_date, receipt_code, id, net_rate"
	else:
		condition = "ORDER BY g_enterprise_id, receipt_code, id, net_rate"
	gstr1_report_query = """SELECT
			g_enterprise_id, id, receipt_code, grn_date, prd.details, p.party_name, SUBSTR(prd.details, 1, 2) AS state_code, invno,
			inv_date, net_inv_value, IF(igst_rate = 0, ROUND(SUM(item_value) / 2, 2), SUM(item_value)) AS invoice_value,
			IF(igst_rate = 0, ROUND(SUM(taxable_value) / 2, 2), SUM(taxable_value)) AS taxable_value, cgst_rate,
			SUM(cgst) AS CGST_VALUE, sgst_rate, SUM(sgst) AS SGST_VALUE, igst_rate, SUM(igst) AS IGST_VALUE, gst_party_type
		FROM (
			SELECT
				g.grn_no AS id, g.enterprise_id AS g_enterprise_id, g.party_id, CONCAT(SUBSTR(
					e.code, - 3, 3), \'/\', g.financial_year, \'/GRN/\', LPAD(g.receipt_no, 6, 0)) AS receipt_code,
				g.approved_on, g.grn_date, m.drawing_no, m.name AS item_name, \'\' AS hsn_code, gm.po_no, gm.dc_qty,
				gm.inv_rate, gm.discount, (gm.dc_qty * gm.inv_rate) AS item_value, ROUND(
					(gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100), 2) AS taxable_value, IF(
					t.type <> \'IGST\', t.net_rate, 0) AS cgst_rate, IF(t.type = \'CGST\', ROUND(
						(gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100) * (t.net_rate / 100), 2), 0) AS cgst, IF(
					t.type <> \'IGST\', t.net_rate, 0) AS sgst_rate, IF(t.type = \'SGST\', ROUND(
						(gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100) * (t.net_rate / 100), 2), 0) AS sgst, IF(
					t.type = \'IGST\', t.net_rate, 0) AS igst_rate, IF(t.type = \'IGST\', ROUND(
						(gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100) * (t.net_rate / 100), 2), 0) AS igst,
				gmt.tax_code, t.type AS tax_type, t.net_rate, g.invno, g.inv_date, g.net_inv_value,
				case 
					when gst_cat.name in ('Registered Business','Registered Business - Composition','Unregistered Business', 'Consumer') THEN 'R'
					when gst_cat.name in ('Overseas','Special Economic Zone (SEZ)','SEZ Developer') THEN 'SEZWOP'
					when gst_cat.name in( 'Deemed Export') THEN 'DE'
					ELSE ''
					END as gst_party_type
			FROM grn g
				JOIN grn_material gm ON g.grn_no = gm.grnNumber
				JOIN enterprise e ON g.enterprise_id = e.id
				JOIN materials m ON gm.item_id = m.id AND gm.enterprise_id = m.enterprise_id
				JOIN grn_material_tax gmt ON gm.item_id = gmt.item_id AND g.grn_no = gmt.grn_no
					AND (gm.po_no = gmt.po_id or (gm.po_no is null and gmt.po_id is null))
				JOIN tax t ON gmt.tax_code = t.code AND g.enterprise_id = t.enterprise_id
				JOIN party_master pym ON g.party_id = pym.party_id
				JOIN gst_category gst_cat ON pym.category_id = gst_cat.id
			WHERE g.grn_date BETWEEN \'%s\' AND \'%s\' AND g.enterprise_id = \'%d\' AND g.status >= 1 AND
				g.rec_against IN ('Purchase Order','Job Work')			
			UNION SELECT
				g.grn_no AS id, g.enterprise_id AS g_enterprise_id, g.party_id, CONCAT(SUBSTR(
					e.code, - 3, 3), \'/\', g.financial_year, \'/GRN/\', LPAD(g.receipt_no, 6, 0)) AS receipt_code,
				g.approved_on, g.grn_date, m.drawing_no, m.name AS item_name, \'\' AS hsn_code, gm.po_no, gm.dc_qty,
				gm.inv_rate, gm.discount, (gm.dc_qty * gm.inv_rate) AS item_value, ROUND(
					(gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100), 2) AS taxable_value, IF(
					t.type <> \'IGST\', t.net_rate, 0) AS cgst_rate, IF(t.type = \'CGST\', ROUND(
						(gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100) * (t.net_rate / 100), 2), 0) AS cgst, IF(
					t.type <> \'IGST\', t.net_rate, 0) AS sgst_rate, IF(t.type = \'SGST\', ROUND(
						(gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100) * (t.net_rate / 100), 2), 0) AS sgst, IF(
					t.type = \'IGST\', t.net_rate, 0) AS igst_rate, IF(t.type = \'IGST\', ROUND(
						(gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100) * (t.net_rate / 100), 2), 0) AS igst,
				gmt.tax_code, t.type AS tax_type, t.net_rate, g.invno, g.inv_date, g.net_inv_value,
				case 
					when gst_cat.name in ('Registered Business','Registered Business - Composition','Unregistered Business', 'Consumer') THEN 'R'
					when gst_cat.name in ('Overseas','Special Economic Zone (SEZ)','SEZ Developer') THEN 'SEZWOP'
					when gst_cat.name in( 'Deemed Export') THEN 'DE'
					ELSE ''
					END as gst_party_type
			FROM grn g
				JOIN grn_material gm ON g.grn_no = gm.grnNumber
				JOIN enterprise e ON g.enterprise_id = e.id
				JOIN materials m ON gm.item_id = m.id AND gm.enterprise_id = m.enterprise_id
				JOIN grn_tax gmt ON g.grn_no = gmt.grn_no
				JOIN tax t ON gmt.tax_code = t.code AND g.enterprise_id = t.enterprise_id
				JOIN party_master pym ON g.party_id = pym.party_id
				JOIN gst_category gst_cat ON pym.category_id = gst_cat.id
			WHERE g.grn_date BETWEEN \'%s\' AND \'%s\' AND g.enterprise_id = \'%d\' AND g.status >= 1
				AND g.rec_against IN ('Purchase Order','Job Work')			
			UNION SELECT
				g.grn_no AS id, g.enterprise_id AS g_enterprise_id, g.party_id, CONCAT(SUBSTR(
					e.code, - 3, 3), \'/\', g.financial_year, \'/GRN/\', LPAD(g.receipt_no, 6, 0)) AS receipt_code,
				g.approved_on, g.grn_date, \'\', \'Packing & Forwarding\' AS item_name, \'\' AS hsn_code,
				\'-\' AS po_no, \'0\' AS dc_qty, g.packing_charges AS inv_rate, \'0\' AS discount,
				g.packing_charges AS item_value, g.packing_charges AS taxable_value, IF(
					t.type <> \'IGST\', t.net_rate, 0) AS cgst_rate, IF(t.type = \'CGST\', ROUND(
						g.packing_charges * (t.net_rate / 100), 2), 0) AS cgst, IF(
					t.type <> \'IGST\', t.net_rate, 0) AS sgst_rate, IF(t.type = \'SGST\', ROUND(
						g.packing_charges * (t.net_rate / 100), 2), 0) AS sgst, IF(
					t.type = \'IGST\', t.net_rate, 0) AS igst_rate, IF(t.type = \'IGST\', ROUND(
						g.packing_charges * (t.net_rate / 100), 2), 0) AS igst,
				gmt.tax_code, t.type AS tax_type, t.net_rate, g.invno, g.inv_date, g.net_inv_value,
				case 
					when gst_cat.name in ('Registered Business','Registered Business - Composition','Unregistered Business', 'Consumer') THEN 'R'
					when gst_cat.name in ('Overseas','Special Economic Zone (SEZ)','SEZ Developer') THEN 'SEZWOP'
					when gst_cat.name in( 'Deemed Export') THEN 'DE'
					ELSE ''
					END as gst_party_type
			FROM grn g
				JOIN enterprise e ON g.enterprise_id = e.id
				JOIN grn_tax gmt ON g.grn_no = gmt.grn_no
				JOIN tax t ON gmt.tax_code = t.code AND g.enterprise_id = t.enterprise_id				
				JOIN party_master pym ON g.party_id = pym.party_id
				JOIN gst_category gst_cat ON pym.category_id = gst_cat.id
			WHERE g.grn_date BETWEEN \'%s\' AND \'%s\' AND g.enterprise_id = \'%d\'  AND g.status >= 1
				AND g.rec_against IN ('Purchase Order','Job Work') AND g.packing_charges <> 0) AS GSTR1
		INNER JOIN party_master p ON GSTR1.party_id = p.party_id
		INNER JOIN party_registration_detail prd ON GSTR1.party_id = prd.party_id and prd.label_id = 1
		GROUP BY id, net_rate  %s
		""" % (
		from_date, to_date, enterprise_id, from_date, to_date, enterprise_id, from_date, to_date, enterprise_id, condition)
	try:
		conv = pymysql.converters.conversions.copy()
		conv[pymysql.constants.FIELD_TYPE.DECIMAL] = str
		conv[pymysql.constants.FIELD_TYPE.NEWDECIMAL] = str
		conv[pymysql.constants.FIELD_TYPE.DATETIME] = str
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, conv=conv, charset="utf8mb4")
		cur = conn.cursor()
		cur.execute(gstr1_report_query)
		response = cur.fetchall()
		conn.close()
	except Exception as e:
		logger.exception('%s' % e)
		response = ""
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def gstr1SalesReportTbl(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	from_date = request_handler.getPostData('from_date') + ' 00:00:00'
	to_date = request_handler.getPostData('to_date') + ' 23:59:00'
	gstr1_report_query = """
		SELECT 
			invoice_code, issued_on, p.gstno, p.party_name, SUBSTR(p.gstno, 1, 2) AS state_code, hsn_code, tax_type, 
			net_rate, SUM(item_value), ROUND((SUM(taxable_value) / COUNT(DISTINCT tax_type)), 2) AS taxable_value,
			SUM(cgst_2_5) AS CGST2_5, SUM(cgst_6) AS CGST_6, SUM(cgst_9) AS CGST_9, SUM(cgst_14) AS CGST_14,
			SUM(sgst_2_5) AS SGST2_5, SUM(sgst_6) AS SGST_6, SUM(sgst_9) AS SGST_9, SUM(sgst_14) AS SGST_14,
			SUM(igst_5) AS IGST_5, SUM(igst_12) AS IGST_12, SUM(igst_18) AS IGST_18, SUM(igst_28) AS IGST_28 
		FROM (
			SELECT 
				i.id AS id, i.enterprise_id AS i_enterprise_id, i.party_id, 
				IFNULL(i.invoice_code, CONCAT(i.financial_year, \'/\', SUBSTR(i.type, 1, 1), LPAD(
					i.invoice_no, 6, 0), IFNULL(i.sub_number, ''))) AS invoice_code, IFNULL(i.issued_on, NOW()) AS issued_on, i.type, m.drawing_no,
				m.name AS item_name, im.hsn_code, im.qty, im.unit_rate, im.discount, (im.qty * im.unit_rate) AS item_value,
				ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100), 2) AS taxable_value,
				IF(t.code = \'CGST2_5\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS cgst_2_5, 
				IF(t.code = \'CGST6\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS cgst_6, 
				IF(t.code = \'CGST9\', ROUND((
					im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS cgst_9, 
				IF(t.code = \'CGST14\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS cgst_14, 
				IF(t.code = \'SGST2_5\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS sgst_2_5, 
				IF(t.code = \'SGST6\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS sgst_6, 
				IF(t.code = \'SGST9\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS sgst_9, 
				IF(t.code = \'SGST14\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS sgst_14, 
				IF(t.code = \'IGST5\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS igst_5, 
				IF(t.code = \'IGST12\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS igst_12, 
				IF(t.code = \'IGST18\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS igst_18, 
				IF(t.code = \'IGST28\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS igst_28, 
				iit.tax_code, t.type AS tax_type, t.net_rate 
			FROM invoice i 
				JOIN invoice_materials im ON i.id = im.invoice_id 
				JOIN materials m ON im.item_id = m.id AND im.enterprise_id = m.enterprise_id 
				JOIN enterprise e ON i.enterprise_id = e.id 
				JOIN invoice_item_tax iit ON im.item_id = iit.item_id AND i.id = iit.invoice_id 
				JOIN tax t ON iit.tax_code = t.code AND i.enterprise_id = t.enterprise_id 
			WHERE IFNULL(i.issued_on, NOW()) BETWEEN \'%s\' AND \'%s\' AND i.enterprise_id = \'%d\' AND i.status > 0 
			and i.type IN('GST','Trading','Service','BoS','Excise')
			UNION
			SELECT 
				i.id AS id, i.enterprise_id AS i_enterprise_id, i.party_id, 
				IFNULL(i.invoice_code, CONCAT(i.financial_year, \'/\', SUBSTR(i.type, 1, 1), LPAD(
					i.invoice_no, 6, 0), IFNULL(i.sub_number, ''))) AS invoice_code, IFNULL(i.issued_on, NOW()) AS issued_on, i.type, m.drawing_no,
				m.name AS item_name, im.hsn_code, im.qty, im.unit_rate, im.discount, (im.qty * im.unit_rate) AS item_value,
				ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100), 2) AS taxable_value,
				IF(t.code = \'CGST2_5\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS cgst_2_5, 
				IF(t.code = \'CGST6\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS cgst_6, 
				IF(t.code = \'CGST9\', ROUND((
					im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS cgst_9, 
				IF(t.code = \'CGST14\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS cgst_14, 
				IF(t.code = \'SGST2_5\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS sgst_2_5, 
				IF(t.code = \'SGST6\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS sgst_6, 
				IF(t.code = \'SGST9\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS sgst_9, 
				IF(t.code = \'SGST14\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS sgst_14, 
				IF(t.code = \'IGST5\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS igst_5, 
				IF(t.code = \'IGST12\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS igst_12, 
				IF(t.code = \'IGST18\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS igst_18, 
				IF(t.code = \'IGST28\', ROUND(
					(im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2), 0) AS igst_28, 
				iit.tax_code, t.type AS tax_type, t.net_rate 
			FROM invoice i 
				JOIN invoice_materials im ON i.id = im.invoice_id 
				JOIN materials m ON im.item_id = m.id AND im.enterprise_id = m.enterprise_id 
				JOIN enterprise e ON i.enterprise_id = e.id 
				JOIN invoice_tax iit ON i.id = iit.invoice_id 
				JOIN tax t ON iit.tax_code = t.code AND i.enterprise_id = t.enterprise_id 
			WHERE IFNULL(i.issued_on, NOW()) BETWEEN \'%s\' AND \'%s\' AND i.enterprise_id = \'%d\' AND i.status > 0 
			and i.type IN('GST','Trading','Service','BoS','Excise')
			) AS GSTR1 
			INNER JOIN party_master p ON GSTR1.party_id = p.party_id GROUP BY id , hsn_code , net_rate 
			ORDER BY i_enterprise_id , id , hsn_code , net_rate; """ % (
		from_date, to_date, enterprise_id, from_date, to_date, enterprise_id,)
	try:
		conv = pymysql.converters.conversions.copy()
		conv[pymysql.constants.FIELD_TYPE.DECIMAL] = str
		conv[pymysql.constants.FIELD_TYPE.NEWDECIMAL] = str
		conv[pymysql.constants.FIELD_TYPE.DATETIME] = str
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, conv=conv, charset="utf8mb4")
		cur = conn.cursor()
		cur.execute(gstr1_report_query)
		response = cur.fetchall()
		conn.close()
	except Exception as e:
		logger.exception('%s' % e)
		response = ""
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def gstr2HSNReportTbl(request):
	"""
	Get the report deduced from Sales Invoices made for Parties under the Registered Business GST category
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	response = []
	try:
		from_date = set_datetime(old_date=request_handler.getPostData('from_date'), format_="from_date")
		to_date = set_datetime(old_date=request_handler.getPostData('to_date'), format_="to_date")
		report_type = request_handler.getPostData('report_type')
		response = GSTR2().generate_hsn_report(
			from_date=from_date, to_date=to_date, enterprise_id=enterprise_id, report_type=report_type)

	except Exception as e:
		logger.exception('%s' % e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def gstr2EXEMPReportTbl(request):
	"""
	Credit/ Debit Notes/Refund vouchers issued to the unregistered persons against interstate invoice value is more than Rs 2.5 lakh
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	from_date = request_handler.getPostData('from_date') + ' 00:00:00'
	to_date = request_handler.getPostData('to_date') + ' 23:59:00'
	exemp_cat = ['NIL-RATED', 'EXEMPT', 'NON-GST']
	condition_clause = {
		'inter_reg': """AND (IF(TRIM(REPLACE(p.state, ' ', '')) != TRIM(REPLACE(e.state, ' ', '')), TRUE , FALSE) OR IF(TRIM(REPLACE(p.state, ' ', '')) = '' OR TRIM(REPLACE(e.state, ' ', '')) = '',IF(LEFT(TRIM(prd.details), 2) != LEFT(TRIM(erd.details), 2), TRUE, FALSE), FALSE)) AND gc.id NOT IN (3, 4, 5)""",
		'intra_reg': """AND (IF(TRIM(REPLACE(p.state, ' ', '')) = TRIM(REPLACE(e.state, ' ', '')), TRUE , FALSE) OR IF(TRIM(REPLACE(p.state, ' ', '')) = '' OR TRIM(REPLACE(e.state, ' ', '')) = '',IF(LEFT(TRIM(prd.details), 2) = LEFT(TRIM(erd.details), 2), TRUE, FALSE), FALSE)) AND gc.id NOT IN (3, 4, 5)"""
	}

	response = {}
	conv = pymysql.converters.conversions.copy()
	conv[pymysql.constants.FIELD_TYPE.DECIMAL] = str
	conv[pymysql.constants.FIELD_TYPE.NEWDECIMAL] = str
	conv[pymysql.constants.FIELD_TYPE.DATETIME] = str
	conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, conv=conv, charset="utf8mb4")
	cur = conn.cursor()
	try:
		for k, cat in enumerate(exemp_cat):
			result = {}
			for key, con in condition_clause.items():
				if cat == 'EXEMPT':
					conditional_statment = """ AND TRIM(hsn_code) != 'NIL-RATED' AND TRIM(hsn_code) != 'NOT-GST' AND TRIM(hsn_code) = 'EXEMPT' %s""" % (condition_clause[key])
				else:
					conditional_statment = """ AND TRIM(hsn_code) = '%s' %s""" % (cat, condition_clause[key])
				query = GSTR2Report().exemp(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id, conditions=conditional_statment)
				cur.execute(query)
				query_set = cur.fetchall()
				result[key] = query_set
			response[k] = result
		conn.close()

		res = {
			0: ["Inter-State supplies",  Decimal(response[0]['inter_reg'][0][6] if response[0]['inter_reg'][0][6] is not None else 0) + Decimal(response[1]['inter_reg'][0][6]) if response[1]['inter_reg'][0][6] is not None else 0 + Decimal(response[2]['inter_reg'][0][6] if response[2]['inter_reg'][0][6] is not None else 0), response[0]['inter_reg'][0][1] if len(response[0]['inter_reg']) > 0 else '0.00',
			    response[1]['inter_reg'][0][1] if len(response[1]['inter_reg']) > 0 else '0.00', response[2]['inter_reg'][0][1] if len(response[2]['inter_reg']) > 0 else '0.00', response[2]['inter_reg'][0][1] if len(response[2]['inter_reg']) > 0 else '0.00'],
			1: ["Intra-State supplies", Decimal(response[0]['intra_reg'][0][6] if response[0]['intra_reg'][0][6] is not None else 0) + Decimal(response[1]['intra_reg'][0][6]) if response[1]['intra_reg'][0][6] is not None else 0 + Decimal(response[2]['intra_reg'][0][6] if response[2]['intra_reg'][0][6] is not None else 0), response[0]['intra_reg'][0][1] if len(response[0]['intra_reg']) > 0 else '0.00',
			    response[1]['intra_reg'][0][1] if len(response[1]['intra_reg']) > 0 else '0.00', response[2]['intra_reg'][0][1] if len(response[2]['intra_reg']) > 0 else '0.00']
		}

	except Exception as e:
		logger.exception('%s' % e)
		conn.close()
		response = []
	return HttpResponse(content=simplejson.dumps(res), mimetype='application/json')


def gstr2IMPGReportTbl(request):
	"""
	Get the report deduced from Sales Invoices made for Parties under the Registered Business GST category
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	response = []
	try:
		from_date = set_datetime(old_date=request_handler.getPostData('from_date'), format_="from_date")
		to_date = set_datetime(old_date=request_handler.getPostData('to_date'), format_="to_date")
		report_type = request_handler.getPostData('report_type')
		response = GSTR2().generate_impg_report(
			from_date=from_date, to_date=to_date, enterprise_id=enterprise_id, report_type=report_type)

	except Exception as e:
		logger.exception('%s' % e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def gstr2IMPSReportTbl(request):
	"""
	Get the report deduced from Sales Invoices made for Parties under the Registered Business GST category
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	response = []
	try:
		from_date = set_datetime(old_date=request_handler.getPostData('from_date'), format_="from_date")
		to_date = set_datetime(old_date=request_handler.getPostData('to_date'), format_="to_date")
		report_type = request_handler.getPostData('report_type')
		response = GSTR2().generate_imps_report(
			from_date=from_date, to_date=to_date, enterprise_id=enterprise_id, report_type=report_type)

	except Exception as e:
		logger.exception('%s' % e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def gstr2B2BReportTbl(request):
	"""
	Get the report deduced from Sales Invoices made for Parties under the Registered Business GST category
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	response = []
	try:
		from_date = set_datetime(old_date=request_handler.getPostData('from_date'), format_="from_date")
		to_date = set_datetime(old_date=request_handler.getPostData('to_date'), format_="to_date")
		report_type = request_handler.getPostData('report_type')
		response = GSTR2().generate_b2b_report(
			from_date=from_date, to_date=to_date, enterprise_id=enterprise_id, report_type=report_type)

	except Exception as e:
		logger.exception('%s' % e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def gstr2B2BURCReportTbl(request):
	"""
	Get the report deduced from Sales Invoices made for Parties under the Registered Business GST category
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	response = []
	try:
		from_date = set_datetime(old_date=request_handler.getPostData('from_date'), format_="from_date")
		to_date = set_datetime(old_date=request_handler.getPostData('to_date'), format_="to_date")
		report_type = request_handler.getPostData('report_type')
		response = GSTR2().generate_b2b_report(
			from_date=from_date, to_date=to_date, enterprise_id=enterprise_id, report_type=report_type)

	except Exception as e:
		logger.exception('%s' % e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def gstr2CDNRReportTbl(request):
	"""
	Credit/ Debit Notes/Refund vouchers issued to the Registered taxpayers during the tax period. Debit or credit note
	issued against invoice will be reported here against original invoice, hence fill the details of original invoice
	also which was furnished in B2B, B2CL section of earlier/current period tax period.
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	from_date = request_handler.getPostData('from_date') + ' 00:00:00'
	to_date = request_handler.getPostData('to_date') + ' 23:59:00'
	gstr1_report_query = GSTR2Report().cdnr(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id)
	try:
		conv = pymysql.converters.conversions.copy()
		conv[pymysql.constants.FIELD_TYPE.DECIMAL] = str
		conv[pymysql.constants.FIELD_TYPE.NEWDECIMAL] = str
		conv[pymysql.constants.FIELD_TYPE.DATETIME] = str
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, conv=conv, charset="utf8mb4")
		cur = conn.cursor()
		cur.execute(gstr1_report_query)
		query_set = cur.fetchall()
		result = []
		# convert query set to tuple to list
		for item in query_set:
			data = list(item)
			result.append(data)

		cess_val = {}
		extra_cess_val = []

		#  CESS calc should be apply for all possibilities only if CESS value appeared
		for key, group in itertools.groupby(sorted(result), lambda x: x[0]):
			val_list = list(group)
			cess_total = 0
			for item in val_list:
				if item[15] is not None:
					cess_total = Decimal(item[15]) + cess_total
					extra_cess_val.append(item[21])
					if key not in cess_val and cess_total != 0:
						cess_val[key] = cess_total

		response = []
		for item in result:
			if item[21] not in extra_cess_val:
				if item[24] != '':
					if len(item[24]) > 2 and get_key(item[24]):
						item[24] = "%s-%s" % (get_key(item[24]), item[24])
					elif len(item[24].replace(" ", "")) == 2 and item[24].upper() in GSTIN_STATE_TITLE:
						item[24] = GSTIN_STATE_TITLE[item[24].upper()]
						item[24] = "%s-%s" % (get_key(item[24]), item[24])
				item_data = {
					'id': item[0], 'gstin': item[1], 'party_name': item[2],
					'inv_no': item[3], 'note_no': item[6], 'note_date': item[5], 'pre_gst': 'N' if datetime.strptime(item[23], '%Y-%m-%d %H:%M:%S') >= datetime.strptime('2017-07-01 00:00:00', '%Y-%m-%d %H:%M:%S') else 'Y',
					'doc_type': 'C' if item[25] is not None and int(item[25]) != 0 else 'D', 'supply_type': item[24], 'total_amount': item[22], 'taxable_value': item[7], 'igst_value': item[14], 'sgst_value': item[12], 'cgst_value': item[10],
					'cess_value': item[15], 'rate': item[8], 'status': item[16], 'inv_date': item[23]
				}
				if item[0] in cess_val and cess_val[item[0]] > 0:
					item_data['cess_value'] = str((float(item[7])+float(item[10]) + float(item[12]) + float(item[14])) * float(cess_val[item[0]]) / 100)
				response.append(item_data)
		conn.close()
	except Exception as e:
		logger.exception('%s' % e)
		response = []
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def gstr2CDNURReportTbl(request):
	"""
	Credit/ Debit Notes/Refund vouchers issued to the unregistered persons against interstate invoice value is more than Rs 2.5 lakh
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	from_date = request_handler.getPostData('from_date') + ' 00:00:00'
	to_date = request_handler.getPostData('to_date') + ' 23:59:00'
	gstr1_report_query = GSTR2Report().cdnur(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id)
	try:
		conv = pymysql.converters.conversions.copy()
		conv[pymysql.constants.FIELD_TYPE.DECIMAL] = str
		conv[pymysql.constants.FIELD_TYPE.NEWDECIMAL] = str
		conv[pymysql.constants.FIELD_TYPE.DATETIME] = str
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, conv=conv, charset="utf8mb4")
		cur = conn.cursor()
		cur.execute(gstr1_report_query)
		query_set = cur.fetchall()
		result = []
		# convert query set to tuple to list
		for item in query_set:
			data = list(item)
			result.append(data)

		cess_val = {}
		extra_cess_val = []

		#  CESS calc should be apply for all possibilities only if CESS value appeared
		for key, group in itertools.groupby(sorted(result), lambda x: x[0]):
			val_list = list(group)
			cess_total = 0
			for item in val_list:
				if item[15] is not None:
					cess_total = Decimal(item[15]) + cess_total
					extra_cess_val.append(item[21])
				if key not in cess_val:
					cess_val[key] = cess_total

		response = []
		for item in result:
			if item[21] not in extra_cess_val:
				if item[24] != '':
					if len(item[24]) > 2 and get_key(item[24]):
						item[24] = "%s-%s" % (get_key(item[24]), item[24])
					elif len(item[24].replace(" ", "")) == 2 and item[24].upper() in GSTIN_STATE_TITLE:
						item[24] = GSTIN_STATE_TITLE[item[24].upper()]
						item[24] = "%s-%s" % (get_key(item[24]), item[24])
				item_data = {
					'id': item[0], 'gstin': item[1], 'party_name': item[2],
					'inv_no': item[3], 'note_no': item[6], 'note_date': item[5], 'pre_gst': 'N' if datetime.strptime(item[23], '%Y-%m-%d %H:%M:%S') >= datetime.strptime('2017-07-01 00:00:00', '%Y-%m-%d %H:%M:%S') else 'N',
					'doc_type': 'D' if int(item[25]) != 1 else 'C', 'supply_type': item[24], 'total_amount': item[22], 'taxable_value': item[7], 'igst_value': item[14], 'sgst_value': item[12], 'cgst_value': item[10],
					'cess_value': item[15], 'rate': item[8], 'status': item[16], 'inv_date': item[23]
				}
				if item[0] in cess_val and cess_val[item[0]] > 0:
					item_data['cess_value'] = str(float(item[7])+(float(item[10]) + float(item[12]) + float(item[14])) * float(cess_val[item[0]]) / 100)
				response.append(item_data)
		conn.close()
	except Exception as e:
		logger.exception('%s' % e)
		response = []
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def authenticate(func):
	def inner_method(ip_address=None, ret_period=None, otp=None, gst_username=None, auth_response=None,
	                 enterprise_id=None):
		response = dict()
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
		cur = conn.cursor(pymysql.cursors.DictCursor)
		try:
			query = '''SELECT gst_auth_username, gst_txn_no, erd.details AS gstin FROM enterprise e \
					LEFT JOIN enterprise_registration_detail erd ON e.id = erd.enterprise_id \
					AND trim(erd.label) = 'GSTIN' WHERE e.id = %s''' % enterprise_id
			cur.execute(query)
			result = cur.fetchall()
			if len(result) > 0 and (
					result[0]['gst_auth_username'] is None or result[0]['gst_txn_no'] is None) and otp is None:
				response['error'] = {'error_cd': 'XS101', 'message': 'GST Username is not found.'}
			elif len(result) > 0 and gst_username is not None and result[0][
				'gst_txn_no'] is not None and otp is not None:
				query_parameters = {'email': GST_API_EMAIL, 'otp': otp}
				headers = {
					'gst_username': str(gst_username), 'state_cd': str(result[0]['gstin'][:2]),
					'ip_address': ip_address, 'txn': str(result[0]['gst_txn_no']), 'client_id': GST_API_CLIENT_ID,
					'client_secret': GST_API_CLIENT_SECRET_ID}
				url = 'https://api.mastergst.com/authentication/authtoken'
				r = requests.get(url, params=query_parameters, headers=headers)
				response_data = r.json()
				if response_data['status_cd'] == "1":
					query = '''UPDATE enterprise SET gst_auth_username = '%s', gst_txn_no = '%s' WHERE enterprise.id = %s''' % (
						gst_username, response_data['header']['txn'], enterprise_id)
					cur.execute(query)
					conn.commit()
					if cur.rowcount > 0:
						response['success'] = {'success_cd': 'XS202', 'message': 'Authentication success'}
				else:
					response['error'] = response_data['error']

			elif len(result) > 0 and result[0]['gst_auth_username'] is not None and result[0][
				'gst_txn_no'] is not None and otp is None:
				query_parameters = {'email': GST_API_EMAIL}
				headers = {
					'gst_username': str(result[0]['gst_auth_username']), 'state_cd': str(result[0]['gstin'][:2]),
					'ip_address': ip_address, 'txn': str(result[0]['gst_txn_no']), 'client_id': GST_API_CLIENT_ID,
					'client_secret': GST_API_CLIENT_SECRET_ID}
				url = 'https://api.mastergst.com/authentication/refreshtoken'
				r = requests.get(url, params=query_parameters, headers=headers)
				response_data = r.json()
				if response_data['status_cd'] == "1":
					query = '''UPDATE enterprise SET gst_auth_username = '%s', gst_txn_no = '%s' WHERE enterprise.id = %s''' % (
						result[0]['gst_auth_username'], response_data['header']['txn'], enterprise_id)
					cur.execute(query)
					conn.commit()
					if cur.rowcount > 0:
						response['success'] = {'success_cd': 'XS200', 'message': 'Username and txn we have'}
				else:
					response['error'] = {'error_cd': 'XS115', 'gst_username': result[0]['gst_auth_username'], 'message': 'Transaction is expired initiate authentication'}
		except Exception as e:
			logger.exception(e)
		return func(ip_address=ip_address, ret_period=ret_period, auth_response=response, enterprise_id=enterprise_id)

	return inner_method


@authenticate
def get_master_gst_2b_data(ip_address=None, ret_period=None, otp=None, gst_username=None, auth_response=None,
                           enterprise_id=None):
	"""

	:param ip_address:
	:param ret_period:
	:param auth_response:
	:param enterprise_id
	:return:
	"""
	conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
	cur = conn.cursor(pymysql.cursors.DictCursor)
	response = dict()
	try:
		if 'error' not in auth_response:
			query = '''SELECT gst_auth_username, gst_txn_no, erd.details AS gstin FROM enterprise e 
					LEFT JOIN enterprise_registration_detail erd ON e.id = erd.enterprise_id 
					AND trim(erd.label) = 'GSTIN' WHERE e.id = %s''' % enterprise_id
			cur.execute(query)
			result = cur.fetchall()
			if len(result) > 0 and result[0]['gst_auth_username'] is not None:
				payload = {
					'gstin': str(result[0]['gstin']), 'rtnprd': ret_period, 'email': GST_API_EMAIL}
				headers = {
					'gst_username': str(result[0]['gst_auth_username']), 'state_cd': str(result[0]['gstin'][:2]),
					'txn': str(result[0]['gst_txn_no']), 'ip_address': ip_address, 'client_id': GST_API_CLIENT_ID,
					'client_secret': GST_API_CLIENT_SECRET_ID}
				url = 'https://api.mastergst.com/gstr2b/all'
				r = requests.get(url, params=payload, headers=headers)
				response_data = r.json()
				if response_data['status_cd'] == "1":
					response['success'] = {'success_cd': 'XS103', 'message': '2B data fetched successfully'}
					response['data'] = response_data['data']
				else:
					response['error'] = response_data['error']
		else:
			response = auth_response
	except Exception as e:
		response = response_code.failure()
		logger.info(e)
	return response


def gstr3bSaveGSTUsername(request):
	"""
	This method helps us to save gst username on db.
	:param request:
	:return:
	"""
	response = response_code.failure()
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
	cur = conn.cursor(pymysql.cursors.DictCursor)
	ip_address = request.get_host()
	gst_username = request_handler.getPostData('gst_username')
	try:
		response = dict()
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
		cur = conn.cursor(pymysql.cursors.DictCursor)
		query = '''SELECT gst_auth_username, gst_txn_no, erd.details AS gstin FROM enterprise e \
				LEFT JOIN enterprise_registration_detail erd ON e.id = erd.enterprise_id \
				AND trim(erd.label) = 'GSTIN' WHERE e.id = %s''' % enterprise_id
		cur.execute(query)
		result = cur.fetchall()
		if len(result) > 0 and result[0]['gstin'] is not None:
			headers = {
				'gst_username': str(gst_username), 'state_cd': str(result[0]['gstin'][:2]),
				'ip_address': ip_address, 'client_id': GST_API_CLIENT_ID,
				'client_secret': GST_API_CLIENT_SECRET_ID}
			url = 'https://api.mastergst.com/authentication/otprequest?email=%s' % GST_EMAIL
			r = requests.get(url, headers=headers)
			logger.info("%s" % r.json())
			response_data = r.json()
			if response_data['status_cd'] == "1":
				query = '''UPDATE enterprise SET gst_txn_no = '%s' WHERE enterprise.id = %s''' % (
					response_data['header']['txn'], enterprise_id)
				cur.execute(query)
				conn.commit()
				if cur.rowcount > 0:
					response['success'] = {'success_cd': 'XS101', 'message': 'GST Username is found and otp initiated'}
			else:
				response['error'] = response_data['error']
		else:
			response['error'] = {'error_cd': 'XS201', 'message': 'GSTIN is not found.'}
		conn.close()
	except Exception as e:
		conn.close()
		logger.info("ERROR: saving gstr3b username %s" % e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def gstr3bReconciliation(request):
	"""

	:param request:
	:return:
	"""
	response = []
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	try:
		ip_address = request.get_host()
		ret_period = datetime.strftime(datetime.strptime(str(request_handler.getPostData('from_date')), '%Y-%m-%d'),
		                               '%m%Y')
		get_pr_data = simplejson.loads(str(request_handler.getPostData('pr_json'))) if str(request_handler.getPostData('pr_json')) is not None and str(request_handler.getPostData('pr_json')) != '' else None
		otp = str(request_handler.getPostData('otp')) if request_handler.getPostData(
			'otp') is not None and request_handler.getPostData('otp') != '' else None
		gst_username = str(request_handler.getPostData('gst_username')) if request_handler.getPostData(
			'gst_username') is not None and request_handler.getPostData('gst_username') != '' else None
		tolerance = int(request_handler.getPostData('tolerance'))
		approximation = True if str(request_handler.getPostData('approximation')) == 'true' else False
		get_2b_data = get_master_gst_2b_data(
			ip_address=ip_address, ret_period=ret_period, auth_response=dict(), otp=otp, gst_username=gst_username,
			enterprise_id=enterprise_id)
		if 'error' not in get_2b_data:
			re = ReconciliationEngine(gst_2b_data=get_2b_data['data'], gst_pr_data=get_pr_data)
			response = re.engine(tolerance=tolerance, approximation=approximation, enterprise_id=enterprise_id)
		else:
			response = get_2b_data

	except Exception as e:
		logger.exception("%s" % e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def gstr1B2BSeriesReportTbl(request):
	"""
	Get the report deduced from Sales Invoices made for Parties under the Registered Business GST category
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	response = []
	try:
		from_date = set_datetime(old_date=request_handler.getPostData('from_date'), format_="from_date")
		to_date = set_datetime(old_date=request_handler.getPostData('to_date'), format_="to_date")
		report_type = request_handler.getPostData('report_type')
		response = GSTR1().generate_b2b_series_report(
			from_date=from_date, to_date=to_date, enterprise_id=enterprise_id, report_type=report_type)

	except Exception as e:
		logger.exception('%s' % e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def gstr1HSNReportTbl(request):
	"""
	HSN wise summary of goods /services supplied during the tax period
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	response = []
	try:
		from_date = set_datetime(old_date=request_handler.getPostData('from_date'), format_="from_date")
		to_date = set_datetime(old_date=request_handler.getPostData('to_date'), format_="to_date")
		report_type = request_handler.getPostData('report_type')
		response = GSTR1().generate_hsn_report(
			from_date=from_date, to_date=to_date, enterprise_id=enterprise_id, report_type=report_type)

	except Exception as e:
		logger.exception('%s' % e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def get_key(val=""):
	"""

	:return:
	"""
	for key, value in GSTIN_STATE_CODE.items():
		if val.replace(" ", "") == value.replace(" ", ""):
			return key
	return ""


def gstr2CDNRCall(query=None):
	"""

	:param query:
	:return:
	"""
	conv = pymysql.converters.conversions.copy()
	conv[pymysql.constants.FIELD_TYPE.DECIMAL] = str
	conv[pymysql.constants.FIELD_TYPE.NEWDECIMAL] = str
	conv[pymysql.constants.FIELD_TYPE.DATETIME] = str
	conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, conv=conv, charset="utf8mb4")
	cur = conn.cursor()
	cur.execute(query)
	query_set = cur.fetchall()
	result = []
	# convert query set to tuple to list
	for item in query_set:
		data = list(item)
		result.append(data)

	cess_val = {}
	extra_cess_val = []

	#  CESS calc should be apply for all possibilities only if CESS value appeared
	for key, group in itertools.groupby(sorted(result), lambda x: x[0]):
		val_list = list(group)
		cess_total = 0
		for item in val_list:
			if item[15] is not None:
				cess_total = Decimal(item[15]) + cess_total
				extra_cess_val.append(item[21])
				if key not in cess_val and cess_total != 0:
					cess_val[key] = cess_total

	response = []
	for item in result:
		if item[21] not in extra_cess_val and ((float(item[10]) > 0 if item[10] is not None else True) or (float(item[12]) > 0 if item[12] is not None else True) or (float(item[14]) > 0 if item[14] is not None else True)):
			if item[24] != '':
				if len(item[24]) > 2 and get_key(item[24]):
					item[24] = "%s-%s" % (get_key(item[24]), item[24])
				elif len(item[24].replace(" ", "")) == 2 and item[24].upper() in GSTIN_STATE_TITLE:
					item[24] = GSTIN_STATE_TITLE[item[24].upper()]
					item[24] = "%s-%s" % (get_key(item[24]), item[24])
			item_data = {
				'id': item[0], 'gstin': item[1], 'party_name': item[2],
				'inv_no': item[3], 'note_no': item[6], 'note_date': item[5], 'pre_gst': 'N' if datetime.strptime(item[23], '%Y-%m-%d %H:%M:%S') >= datetime.strptime('2017-07-01 00:00:00', '%Y-%m-%d %H:%M:%S') else 'Y',
				'doc_type': 'C' if item[25] is not None and int(item[25]) != 0 else 'D', 'supply_type': item[24], 'total_amount': item[22], 'taxable_value': item[7], 'igst_value': item[14], 'sgst_value': item[12], 'cgst_value': item[10],
				'cess_value': item[15], 'rate': item[8], 'status': item[16], 'inv_date': item[23]
			}
			if item[0] in cess_val and cess_val[item[0]] > 0:
				item_data['cess_value'] = str((float(item[7])+float(item[10]) + float(item[12]) + float(item[14])) * float(cess_val[item[0]]) / 100)
			response.append(item_data)
	conn.close()
	return response


def gstr2CDNURCall(query=None):
	"""

	:param query:
	:return:
	"""
	conv = pymysql.converters.conversions.copy()
	conv[pymysql.constants.FIELD_TYPE.DECIMAL] = str
	conv[pymysql.constants.FIELD_TYPE.NEWDECIMAL] = str
	conv[pymysql.constants.FIELD_TYPE.DATETIME] = str
	conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, conv=conv, charset="utf8mb4")
	cur = conn.cursor()
	cur.execute(query)
	query_set = cur.fetchall()
	result = []
	# convert query set to tuple to list
	for item in query_set:
		data = list(item)
		result.append(data)

	cess_val = {}
	extra_cess_val = []

	#  CESS calc should be apply for all possibilities only if CESS value appeared
	for key, group in itertools.groupby(sorted(result), lambda x: x[0]):
		val_list = list(group)
		cess_total = 0
		for item in val_list:
			if item[15] is not None:
				cess_total = Decimal(item[15]) + cess_total
				extra_cess_val.append(item[21])
			if key not in cess_val:
				cess_val[key] = cess_total

	response = []
	for item in result:
		if item[21] not in extra_cess_val and ((float(item[10]) > 0 if item[10] is not None else True) or (float(item[12]) > 0 if item[12] is not None else True) or (float(item[14]) > 0 if item[14] is not None else True)):
			if item[24] != '':
				if len(item[24]) > 2 and get_key(item[24]):
					item[24] = "%s-%s" % (get_key(item[24]), item[24])
				elif len(item[24].replace(" ", "")) == 2 and item[24].upper() in GSTIN_STATE_TITLE:
					item[24] = GSTIN_STATE_TITLE[item[24].upper()]
					item[24] = "%s-%s" % (get_key(item[24]), item[24])

			item_data = {
				'id': item[0], 'gstin': 'B2CL' if item[26] in (3, 4) else 'EXPWP', 'party_name': item[2],
				'inv_no': item[3], 'inv_date': item[23], 'sr_no': item[6], 'grn_date': item[5],
				'doc_type': 'D' if int(item[25]) != 1 else 'C', 'place_of_supply': item[24], 'net_inv_value': item[22], 'taxable_value': item[7],
				'cess_value': item[15], 'rate': item[8], 'status': item[16], 'pre_gst': 'N' if datetime.strptime(item[23], '%Y-%m-%d %H:%M:%S') >= datetime.strptime('2017-07-01 00:00:00', '%Y-%m-%d %H:%M:%S') else 'Y'
			}

			if item[0] in cess_val and cess_val[item[0]] > 0:
				item_data['cess_value'] = str(float(item[7])+(float(item[10]) + float(item[12]) + float(item[14])) * float(cess_val[item[0]]) / 100)
			response.append(item_data)
	conn.close()
	return response


def gstr1CDNRReportTbl(request):
	"""
	Credit/ Debit Notes/Refund vouchers issued to the Registered taxpayers during the tax period. Debit or credit note
	issued against invoice will be reported here against original invoice, hence fill the details of original invoice
	also which was furnished in B2B, B2CL section of earlier/current period tax period.
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	from_date = request_handler.getPostData('from_date') + ' 00:00:00'
	to_date = request_handler.getPostData('to_date') + ' 23:59:00'
	gstr2_report_query = GSTR2Report().cdnr_grn(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id)
	res = gstr2CDNRCall(query=gstr2_report_query)
	try:
		response = []
		for item in res:
			item_data = {
				'id': item['id'], 'gstin': item['gstin'], 'party_name': item['party_name'],
				'inv_no': item['inv_no'], 'inv_date': item['inv_date'], 'sr_no': item['note_no'], 'grn_date': item['note_date'],
				'doc_type': item['doc_type'], 'place_of_supply': item['supply_type'], 'net_inv_value': item['total_amount'], 'taxable_value': item['taxable_value'],
				'cess_value': item['cess_value'], 'rate': item['rate'], 'status': item['status'], 'pre_gst': item['pre_gst']
			}
			response.append(item_data)
	except Exception as e:
		logger.exception('%s' % e)
		response = []
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def gstr1CDNURReportTbl(request):
	"""
	Credit/ Debit Notes/Refund vouchers issued to the unregistered persons against interstate invoice value is more than Rs 2.5 lakh
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	from_date = request_handler.getPostData('from_date') + ' 00:00:00'
	to_date = request_handler.getPostData('to_date') + ' 23:59:00'
	gstr2_report_query = GSTR2Report().cdnur_grn(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id)
	res = gstr2CDNURCall(query=gstr2_report_query)
	try:
		response = []
		for item in res:
			item_data = {
				'id': item['id'], 'gstin': item['gstin'], 'party_name': item['party_name'],
				'inv_no': item['inv_no'], 'inv_date': item['inv_date'], 'sr_no': item['sr_no'], 'grn_date': item['grn_date'],
				'doc_type': item['doc_type'], 'place_of_supply': item['place_of_supply'], 'net_inv_value': item['net_inv_value'], 'taxable_value': item['taxable_value'],
				'cess_value': item['cess_value'], 'rate': item['rate'], 'status': item['status'], 'pre_gst': item['pre_gst']
			}
			response.append(item_data)
	except Exception as e:
		logger.exception('%s' % e)
		response = []
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def gstr1EXPReportTbl(request):
	"""
	HSN wise summary of goods /services supplied during the tax period
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	response = []
	try:
		from_date = set_datetime(old_date=request_handler.getPostData('from_date'), format_="from_date")
		to_date = set_datetime(old_date=request_handler.getPostData('to_date'), format_="to_date")
		report_type = request_handler.getPostData('report_type')
		response = GSTR1().generate_b2b_series_report(
			from_date=from_date, to_date=to_date, enterprise_id=enterprise_id, report_type=report_type)

	except Exception as e:
		logger.exception('%s' % e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def compareStateByState(p_state=None, e_state=None):
	"""

	:param p_state:
	:param e_state:
	:return:
	"""
	try:
		if len(p_state.strip()) > 2 and len(e_state.strip()) > 2:
			return compare_string(p_state.strip(), e_state.strip())
		elif len(p_state.strip()) > 2 and len(e_state.strip()) == 2:
			return compare_string(p_state.strip(), GSTIN_STATE_TITLE[e_state.strip()])
		elif len(p_state.strip()) == 2 and len(e_state.strip()) > 2:
			return compare_string(GSTIN_STATE_TITLE[p_state.strip()], e_state.strip())
		else:
			return compare_string(GSTIN_STATE_TITLE[p_state.strip()], GSTIN_STATE_TITLE[e_state.strip()])
	except Exception as e:
		logger.exception('%s' % e)
	return False


def stateCompare(p_state=None, p_gstin=None, e_state=None, e_gstin=None):
	"""

	:param p_state:
	:param p_gstin:
	:param e_state:
	:param e_gstin:
	:return:
	"""
	try:
		if (p_state is not None and p_state.strip() != "") and (e_state is not None and e_state.strip() != ""):
			return compareStateByState(p_state, e_state)
		elif (p_state is not None and p_state.strip() != "") and (e_state is None or e_state.strip() == "") and (e_gstin is not None or e_gstin.strip() == ""):
			return compareStateByState(p_state, GSTIN_STATE_CODE[e_gstin[:2]])
		elif (p_state is None and p_state.strip() == "") and (e_state is not None or e_state.strip() != "") and (p_gstin is not None or p_gstin.strip() == ""):
			return compareStateByState(GSTIN_STATE_CODE[p_gstin[:2]], e_state)
		elif (p_state is None and p_state.strip() == "") and (e_state is None or e_state.strip() == ""):
			return compareStateByState(GSTIN_STATE_CODE[p_gstin[:2]], GSTIN_STATE_CODE[e_gstin[:2]])
	except Exception as e:
		logger.exception('%s' % e)
	return False


def gstr1EXEMPReportTbl(request):
	"""
	Credit/ Debit Notes/Refund vouchers issued to the unregistered persons against interstate invoice value is more than Rs 2.5 lakh
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	from_date = request_handler.getPostData('from_date') + ' 00:00:00'
	to_date = request_handler.getPostData('to_date') + ' 23:59:00'
	exemp_cat = ['NIL-RATED', 'EXEMPT', 'NON-GST']
	condition_clause = {
		'inter_reg': """AND (IF(TRIM(REPLACE(p.state, ' ', '')) != TRIM(REPLACE(e.state, ' ', '')), TRUE , FALSE) OR IF(TRIM(REPLACE(p.state, ' ', '')) = '' OR TRIM(REPLACE(e.state, ' ', '')) = '',IF(LEFT(TRIM(prd.details), 2) != LEFT(TRIM(erd.details), 2), TRUE, FALSE), FALSE)) AND gc.id NOT IN (3, 4, 5)""",
		'intra_reg': """AND (IF(TRIM(REPLACE(p.state, ' ', '')) = TRIM(REPLACE(e.state, ' ', '')), TRUE , FALSE) OR IF(TRIM(REPLACE(p.state, ' ', '')) = '' OR TRIM(REPLACE(e.state, ' ', '')) = '',IF(LEFT(TRIM(prd.details), 2) = LEFT(TRIM(erd.details), 2), TRUE, FALSE), FALSE)) AND gc.id NOT IN (3, 4, 5)""",
		'inter_non_reg': """AND (IF(TRIM(REPLACE(p.state, ' ', '')) != TRIM(REPLACE(e.state, ' ', '')), TRUE , FALSE) OR IF(TRIM(REPLACE(p.state, ' ', '')) = '' OR TRIM(REPLACE(e.state, ' ', '')) = '',IF(LEFT(TRIM(prd.details), 2) != LEFT(TRIM(erd.details), 2), TRUE, FALSE), FALSE)) AND gc.id IN (3, 4, 5)""",
		'intra_non_reg': """AND (IF(TRIM(REPLACE(p.state, ' ', '')) = TRIM(REPLACE(e.state, ' ', '')), TRUE , FALSE) OR IF(TRIM(REPLACE(p.state, ' ', '')) = '' OR TRIM(REPLACE(e.state, ' ', '')) = '',IF(LEFT(TRIM(prd.details), 2) = LEFT(TRIM(erd.details), 2), TRUE, FALSE), FALSE)) AND gc.id IN (3, 4, 5)"""}
	response = {}
	conv = pymysql.converters.conversions.copy()
	conv[pymysql.constants.FIELD_TYPE.DECIMAL] = str
	conv[pymysql.constants.FIELD_TYPE.NEWDECIMAL] = str
	conv[pymysql.constants.FIELD_TYPE.DATETIME] = str
	conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, conv=conv, charset="utf8mb4")
	cur = conn.cursor()
	try:
		for k, cat in enumerate(exemp_cat):
			result = {}
			for key, con in condition_clause.items():
				conditional_statment = """ AND TRIM(hsn_code) = '%s' %s""" % (cat, condition_clause[key])
				query = GSTR1Report().exemp(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id, conditions=conditional_statment)
				cur.execute(query)
				query_set = cur.fetchall()
				result[key] = query_set
			response[k] = result
		conn.close()
		res = {
			0: ["Inter-State supplies to registered persons", response[0]['inter_reg'][0][1] if len(response[0]['inter_reg']) > 0 else '0.00',
			    response[1]['inter_reg'][0][1] if len(response[1]['inter_reg']) > 0 else '0.00', response[2]['inter_reg'][0][1] if len(response[2]['inter_reg']) > 0 else '0.00'],
			1: ["Intra-State supplies to registered persons", response[0]['intra_reg'][0][1] if len(response[0]['intra_reg']) > 0 else '0.00',
			    response[1]['intra_reg'][0][1] if len(response[1]['intra_reg']) > 0 else '0.00', response[2]['intra_reg'][0][1] if len(response[2]['intra_reg']) > 0 else '0.00'],
			2: ["Inter-State supplies to unregistered persons", response[0]['inter_non_reg'][0][1] if len(response[0]['inter_non_reg']) > 0 else '0.00',
			    response[1]['inter_non_reg'][0][1] if len(response[1]['inter_non_reg']) > 0 else '0.00', response[2]['inter_non_reg'][0][1] if len(response[2]['inter_non_reg']) > 0 else '0.00'],
			3: ["Intra-State supplies to unregistered persons", response[0]['intra_non_reg'][0][1] if len(response[0]['intra_non_reg']) > 0 else 0,
			    response[1]['intra_non_reg'][0][1] if len(response[1]['intra_non_reg']) > 0 else '0.00', response[2]['intra_non_reg'][0][1] if len(response[2]['intra_non_reg']) > 0 else '0.00']
		}

	except Exception as e:
		logger.exception('%s' % e)
		conn.close()
		response = []
	return HttpResponse(content=simplejson.dumps(res), mimetype='application/json')


def tcsReportTbl(request):
	"""
	Get the report for sales and purchase of greater than 500000 amount
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	try:
		from_date = request_handler.getPostData('from_date') + ' 00:00:00'
		to_date = request_handler.getPostData('to_date') + ' 23:59:00'
		is_sales = request_handler.getPostData('is_sales')
		financial_year = getFinancialYear(datetime.strptime(from_date, '%Y-%m-%d %H:%M:%S'), fy_start_day='01/04')
		if is_sales:
			tcs_query = """SELECT * from (SELECT 
						pm.party_name, sum(grand_total) as total
						FROM invoice AS i, party_master as pm					
						WHERE pm.party_id = i.party_id AND pm.enterprise_id= i.enterprise_id AND i.type = 'GST' AND i.status = 1 
						AND i.enterprise_id = %s AND i.issued_on between '%s' and '%s' 
						AND financial_year = '%s'
						GROUP BY i.party_id) as details where details.total > 5000000""" % (enterprise_id, from_date, to_date, financial_year)
		else:
			tcs_query = """SELECT * from (SELECT 
						pm.party_name, sum(net_inv_value) as total FROM grn AS i, 
						party_master as pm 
						WHERE pm.party_id = i.party_id AND pm.enterprise_id= i.enterprise_id 
						AND i.rec_against = 'Purchase Order' AND i.status > 1 
						AND i.enterprise_id = %s AND i.inward_date between '%s' and '%s' 
						AND financial_year = '%s'
						GROUP BY i.party_id) as details where details.total > 5000000""" % (enterprise_id, from_date, to_date, financial_year)
		data_dump = executeQuery(tcs_query)
	except Exception as e:
		logger.exception(e.message)
		data_dump = ""
	return HttpResponse(content=simplejson.dumps(data_dump), mimetype='application/json')


def gvandbvReportTbl(request):
	"""
	Get the report for GV and BV tax details
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	try:
		from_date = request_handler.getPostData('from_date') + ' 00:00:00'
		to_date = request_handler.getPostData('to_date') + ' 23:59:00'
		gvbv_query = """SELECT CONCAT(v.financial_year,'/',vt.code, '/', LPAD(v.voucher_no, 6, '0')) AS voucher_code,
				al.name, v.voucher_date, vp.amount, vp.is_debit				
				FROM voucher v JOIN voucher_particulars vp ON v.id = vp.voucher_id 
				JOIN account_ledgers al ON al.id = vp.ledger_id 
				AND al.name IN ('INPUT CGST 2.5 %' , 'INPUT CGST 6%', 'INPUT CGST 9%', 'INPUT CGST 14%','INPUT SGST 2.5%',
				'INPUT SGST 6%','INPUT SGST 9%','INPUT SGST 14%','INPUT IGST 5%','INPUT IGST 12%','INPUT IGST 18%','INPUT IGST 28%') 
				AND v.type NOT IN (4 , 6) JOIN voucher_type vt ON v.type = vt.id WHERE v.voucher_date 
				BETWEEN '{from_date}' AND '{to_date}' and v.enterprise_id = '{enterprise_id}' ORDER BY name , 
				voucher_date """.format(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id)
		data_dump = executeQuery(gvbv_query, as_dict=True)
	except Exception as e:
		logger.exception(e.message)
		data_dump = ""
	return HttpResponse(content=json.dumps(data_dump, cls=CustomJSONEncoder), mimetype='application/json')


def salesItemWiseReportTbl(request):
	"""
	Get the report for GV and BV tax details
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	try:
		from_date = request_handler.getPostData('from_date') + ' 00:00:00'
		to_date = request_handler.getPostData('to_date') + ' 23:59:00'
		sales_item_wise_query = """SELECT
				invoice_code,
				approved_on,
				p.gstno,
				p.party_name,
				SUBSTR(p.gstno, 1, 2) AS state_code,
				hsn_code,
				item_name,
				qty,
				tax_type,
				net_rate,
				sum(item_value) AS sum_amount,
				ROUND((SUM(taxable_value) / COUNT(DISTINCT tax_type)), 2) as taxable_value, 
				SUM(cgst_2_5) as CGST2_5, SUM(cgst_6) as CGST_6, SUM(cgst_9) as CGST_9, SUM(cgst_14) as CGST_14, SUM(sgst_2_5) as SGST2_5, SUM(sgst_6) as SGST_6,
				SUM(sgst_9) AS SGST_9, SUM(sgst_14) AS SGST_14, SUM(igst_5) AS IGST_5, SUM(igst_12) AS IGST_12, SUM(igst_18) AS IGST_18, SUM(igst_28) AS IGST_28	
				FROM (
				SELECT
				i.id as id,
				i.enterprise_id as i_enterprise_id,
				i.party_id,
				CONCAT(SUBSTR(e.code, -3, 3), '/', i.financial_year, '/', SUBSTR(i.type, 1, 1), LPAD(i.invoice_no, 6, 0)) as invoice_code,
				i.approved_on,
				i.type,
				m.drawing_no,
				m.name as item_name,
				im.hsn_code,
				im.qty,
				im.unit_rate,
				im.discount,
				(im.qty * im.unit_rate) as item_value,
				ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100), 2) AS taxable_value,
				IF(t.code='CGST2_5', ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate/100), 2), 0) as cgst_2_5,
				IF(t.code='CGST6', ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate/100), 2), 0) as cgst_6,
				IF(t.code='CGST9', ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate/100), 2), 0) as cgst_9,
				IF(t.code='CGST14', ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate/100), 2), 0) as cgst_14,
				IF(t.code='SGST2_5', ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate/100), 2), 0) as sgst_2_5,
				IF(t.code='SGST6', ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate/100), 2), 0) as sgst_6,
				IF(t.code='SGST9', ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate/100), 2), 0) as sgst_9,
				IF(t.code='SGST14', ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate/100), 2), 0) as sgst_14,
				IF(t.code='IGST5', ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate/100), 2), 0) as igst_5,
				IF(t.code='IGST12', ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate/100), 2), 0) as igst_12,
				IF(t.code='IGST18', ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate/100), 2), 0) as igst_18,
				IF(t.code='IGST28', ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate/100), 2), 0) as igst_28,	
				iit.tax_code, t.type as tax_type,
				t.net_rate
				FROM
				invoice i 
				JOIN
				invoice_materials im ON i.id = im.invoice_id 
				JOIN
				materials m ON im.item_id = m.id
				AND im.enterprise_id = m.enterprise_id
				JOIN
				enterprise e ON i.enterprise_id = e.id
					JOIN
				invoice_item_tax iit on im.item_id = iit.item_id and i.id = iit.invoice_id
				JOIN tax t on iit.tax_code=t.code and i.enterprise_id=t.enterprise_id
				WHERE
				i.issued_on BETWEEN '{from_date}' AND '{to_date}'  and i.status > 0  and i.enterprise_id={enterprise_id} and i.type='GST') as GSTR1 inner join party_master p on GSTR1.party_id = p.party_id
				group by id, item_name, net_rate order by i_enterprise_id, id, hsn_code, net_rate""".format(from_date=from_date, to_date=to_date, enterprise_id=enterprise_id)
		data_dump = executeQuery(sales_item_wise_query, as_dict=True)
	except Exception as e:
		logger.exception(e.message)
		data_dump = ""
	return HttpResponse(content=json.dumps(data_dump, cls=CustomJSONEncoder), mimetype='application/json')


def outstandingReportTbl(request):
	"""
	Get the report for Payables and Receivables
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	try:
		is_receivables = request_handler.getPostData('is_receivables')
		if is_receivables == 'true':
			out_standing_query = """SELECT 
				name,
				SUM(CASE WHEN age_days >= 120 THEN net_value ELSE 0 END) as greater_120,
				SUM(CASE WHEN age_days < 120 THEN net_value ELSE 0 END) as below_120
				FROM
				(SELECT lb.ledger_id, lb.is_debit, al.group_id, al.name, lb.net_value, 
				DATEDIFF(CURRENT_DATE(), lb.bill_date) AS age_days FROM ledger_bills AS lb
				JOIN account_ledgers AS al ON al.id = lb.ledger_id 
				AND al.enterprise_id = lb.enterprise_id WHERE lb.enterprise_id = '{enterprise_id}' 
				AND al.group_id = 23 AND lb.net_value <> 0) AS bq GROUP BY ledger_id, is_debit, group_id, name
				ORDER BY ledger_id""".format(enterprise_id=enterprise_id)
		else:
			out_standing_query = """SELECT 
				name,
				SUM(CASE WHEN age_days >= 45 THEN net_value ELSE 0 END) as greater_45,
				SUM(CASE WHEN age_days < 45 THEN net_value ELSE 0 END) as below_45
				FROM
				(SELECT lb.ledger_id, lb.is_debit, al.group_id, al.name, lb.net_value, 
				DATEDIFF(CURRENT_DATE(), lb.bill_date) AS age_days FROM ledger_bills AS lb
				JOIN account_ledgers AS al ON al.id = lb.ledger_id AND al.enterprise_id = lb.enterprise_id
				WHERE lb.enterprise_id = '{enterprise_id}' AND al.group_id = 24 
				AND lb.net_value <> 0) AS bq GROUP BY ledger_id, is_debit, group_id, name
				ORDER BY ledger_id""".format(enterprise_id=enterprise_id)
		data_dump = executeQuery(out_standing_query, as_dict=True)
	except Exception as e:
		logger.exception(e.message)
		data_dump = ""
	return HttpResponse(content=json.dumps(data_dump, cls=CustomJSONEncoder), mimetype='application/json')


def loadLedger(request):
	"""
	Loads All Material's primary information.

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	ledger_query = """SELECT a.id, a.name, b.name,b.id, a.billable FROM account_ledgers AS a, account_groups AS b 
		WHERE a.group_id=b.id AND a.enterprise_id=b.enterprise_id AND a.enterprise_id='%s' and a.status = 1 """ % enterprise_id
	try:
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
		cur = conn.cursor()
		cur.execute(ledger_query)
		ledger_data_dump = cur.fetchall()
		conn.close()
	except Exception as e:
		logger.exception(e)
		ledger_data_dump = ""
	response = HttpResponse(content=simplejson.dumps(ledger_data_dump), mimetype='application/json')
	return response


def checkLedgerBillDetails(request):
	"""
	Loads Ledger Bill Count  information.

	:param request:
	:return:
	"""

	request_handler = RequestHandler(request)
	bill_no = request_handler.getPostData('bill_no')
	ledger_id = request_handler.getPostData('ledger_id')
	bill_date = request_handler.getPostData('bill_date')
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	ledger_bill_query = """SELECT count(1)			
		FROM ledger_bills AS a
		WHERE a.ledger_id='%s' AND  a.bill_no='%s' AND bill_date='%s' AND a.enterprise_id='%s'""" % (
		ledger_id, bill_no, bill_date, enterprise_id)
	try:
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
		cur = conn.cursor()
		cur.execute(ledger_bill_query)
		ledger_bill_count = cur.fetchall()
		json = simplejson.dumps(int(ledger_bill_count[0][0]))
		conn.close()
	except Exception as e:
		logger.info("Error %s", e.message)
		logger.exception(e)
	return HttpResponse(json)


def getLedgerOpening(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	ledger_ids = request_handler.getPostData('ledger_ids')
	opening_on = request_handler.getPostData('opening_on')
	if ledger_ids == "":
		ledger_ids = "-1"
	else:
		ledger_ids = "-1,%s" % ledger_ids
	ledger_opening_query = """
		SELECT al.id, SUM(IFNULL(vp.amount, 0) * IF(vp.is_debit > 0, -1, 1)) 
		FROM account_ledgers al
			JOIN voucher_particulars vp ON al.id = vp.ledger_id AND al.enterprise_id = vp.enterprise_id
			JOIN voucher v ON v.id = vp.voucher_id AND v.enterprise_id = vp.enterprise_id
		WHERE DATE(v.voucher_date) <= DATE('%s') AND al.id IN (%s) AND al.enterprise_id = %s GROUP BY al.id""" % (
		opening_on, ledger_ids, enterprise_id)
	logger.debug("Ledger Opening query: %s" % ledger_opening_query)
	ledger_opening_values = []
	try:
		db_connection = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
		query_cursor = db_connection.cursor()
		query_cursor.execute(ledger_opening_query)
		ledger_opening_results = query_cursor.fetchall()
		for item in ledger_opening_results:
			ledger_opening_dict = dict()
			ledger_opening_dict["ledger_id"] = item[0]
			ledger_opening_dict["opening"] = item[1]
			ledger_opening_values.append(ledger_opening_dict)
		db_connection.close()
	except Exception as e:
		logger.exception(e)
	logger.debug("Ledger Opening - %s" % ledger_opening_values)
	response = HttpResponse(content=simplejson.dumps(ledger_opening_values), mimetype='application/json')
	return response


def loadBankLedger(request):
	"""
	Loads All Material's primary information.

	:param request:
	:return:
	"""

	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	logger.info("Loading Bank ledgers")
	return HttpResponse(content=simplejson.dumps(
		populateBankLedgerChoices(enterprise_id=enterprise_id)), mimetype='application/json')


def loadCashLedger(request):
	"""
	Loads All Material's primary information.

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	logger.info("Loading Cash ledgers")
	return HttpResponse(content=simplejson.dumps(
		populateCashLedgerChoices(enterprise_id=enterprise_id)), mimetype='application/json')


def loadLedgerBillDetails(request):
	"""
	Loads All Material's primary information.

	:param request:
	:return:
	"""

	request_handler = RequestHandler(request)
	ledger_id = request_handler.getPostData('ledger_id')
	voucher_id = request_handler.getPostData('voucher_id') if request_handler.getPostData('voucher_id') else 0
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	ledger_bill_query = """SELECT
			a.bill_no, DATE_FORMAT(a.bill_date,'%%Y-%%m-%%d') AS iDate, IFNULL((
				SELECT SUM(dr_value) FROM ledger_bill_settlements WHERE bill_id=a.id and voucher_id<>%s),0) AS dr_amt,
			IFNULL((
				SELECT SUM(cr_value) FROM ledger_bill_settlements  WHERE bill_id=a.id and voucher_id<>%s),0) AS cr_amt,
			a.id, a.voucher_id, IFNULL((
				SELECT dr_value FROM ledger_bill_settlements WHERE voucher_id='%s' and bill_id=a.id),0.00) AS dr_value,
			IFNULL((
				SELECT cr_value FROM ledger_bill_settlements WHERE voucher_id=%s and bill_id=a.id),0.00) AS cr_value,
			IFNULL((
				SELECT concat(p.name,"-(",p.code,")") FROM voucher as v JOIN projects as p ON p.id=v.project_code and v.enterprise_id=p.enterprise_id 
				WHERE v.id=a.voucher_id and v.enterprise_id=a.enterprise_id),"") AS project,
				a.voucher_id as voucher_id
		FROM ledger_bills AS a
		WHERE a.ledger_id='%s' AND a.enterprise_id='%s'
		ORDER BY a.bill_date, a.bill_no asc, cr_value desc, dr_value desc""" % (
		voucher_id, voucher_id, voucher_id, voucher_id, ledger_id, enterprise_id)
	try:
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
		cur = conn.cursor()
		cur.execute(ledger_bill_query)
		ledger_bill_data_dump = cur.fetchall()
		conn.close()
	except Exception as e:
		logger.info("Error %s", e.message)
		logger.exception(e)
		ledger_bill_data_dump = ""
	response = HttpResponse(content=simplejson.dumps(ledger_bill_data_dump), mimetype='application/json')
	return response


def importVoucher(request):
	"""
	Renders the main page to manage Voucher.
	:param request:
	:return:
	"""
	logger.info('Inside Import Voucher... ')
	request_handler = RequestHandler(request)
	user = request_handler.getSessionAttribute(USER_IN_SESSION_KEY)
	enterprise = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise
	failed_items = []
	valid_items = []
	db_session = SQLASession()
	failed_voucher_count = 0
	total_credit = 0
	total_debit = 0
	is_valid_csv = True
	try:
		trial_balance_list = request.POST.getlist('trial_balance_list[]')
		if request.FILES:
			bulk_file = request.FILES['bulkfile']
			bulk_file.seek(0)
			book = xlrd.open_workbook(file_contents=bulk_file.read())
			ledger_sheet = book.sheet_by_name('ledger')
			logger.debug('Sheet name: %s' % ledger_sheet.name)
			trial_balance_list = []
			for row_idx in range(0, ledger_sheet.nrows):  # Iterate through rows
				logger.debug('-' * 40)
				if row_idx == 0:
					dict = {}
				else:
					dict = {
						'ledger_name': str(ledger_sheet.cell(row_idx, 0).value),
						'account_group_id': str(int(ledger_sheet.cell(row_idx, 1).value)) if isinstance(
							ledger_sheet.cell(row_idx, 1).value, float) else str(ledger_sheet.cell(row_idx, 1).value),
						"opening_debit": str(ledger_sheet.cell(row_idx, 2).value),
						'opening_credit': str(ledger_sheet.cell(row_idx, 3).value)}
				logger.info(dict)
				trial_balance_list.append(dict)
			if len(trial_balance_list) <= 1:
				return {
					'message': 'No trial balance imported.', 'failed_items': [], 'failed_items_name_list': []}
			trial_balance_list.append({})
			do_import = True
			voucher_date = datetime.now()
		else:
			is_valid_file_headers = validateFileHeaders(
				module='accounts', title_row=ast.literal_eval(trial_balance_list[0]))
			if not is_valid_file_headers:
				return HttpResponse(content=simplejson.dumps({
					'message': 'Please upload the valid  file or check proper column names',
					'is_valid_file_headers': False}), mimetype='application/json')
			do_import = 'is_user_accept' in request.POST
			voucher_date = str(request.POST['voucher_date'])

		is_new_ledgers, new_ledgers = __checkNewLedgers(
			trial_balance_list=trial_balance_list, enterprise_id=enterprise.id, new_ledgers=[], request=request)
		if not do_import:
			if is_new_ledgers:
				logger.info("%s New Ledgers  found!" % len(new_ledgers))
				dict = {
					"message": str("%s New Ledgers  found!" % len(new_ledgers)), "status": 1,
					"new_ledgers": new_ledgers, 'is_valid_file_headers': True}
				return HttpResponse(content=simplejson.dumps(dict), mimetype='application/json')
		for trial_balance in trial_balance_list[1:-1]:
			if request.FILES:
				trial_balance_dict = trial_balance
			else:
				trial_balance_dict = ast.literal_eval(trial_balance)
			try:
				validation_errors = __validateRow(trial_balance_dict=trial_balance_dict, enterprise_id=enterprise.id)

				if len(validation_errors) > 0:
					is_valid_csv = False
					raise Exception(validation_errors)
				else:
					valid_items.append(trial_balance_dict)

			except Exception as e:
				trial_balance_dict['error'] = "%s %s" % (
					trial_balance_dict['error'] if 'error' in trial_balance_dict else "", e.message)
				failed_items.append(trial_balance_dict)
				logger.exception("Skipped inserting ledger trail balance {enterprise:%s } due to: %s" % (
					enterprise.id, e.message))
		if is_valid_csv:
			for item in valid_items:
				total_debit += float(item['opening_debit']) if item['opening_debit'] else 0
				total_credit += float(item['opening_credit']) if item['opening_credit'] else 0

			if total_credit != total_debit:
				dict = {
					"message": "Imbalanced credit debit in your file.", "status": 3, 'failed_items': [],
					'failed_items_name_list': []}  # status 3 = In balanced credit debit
				if request.FILES:
					return dict
				else:
					return HttpResponse(content=simplejson.dumps(dict), mimetype='application/json')

		if is_valid_csv:
			__constructVoucher(
				enterprise=enterprise, user=user, db_session=db_session, valid_items=valid_items,
				voucher_date=voucher_date)
		else:
			dict = {
				"message": "Improper data in your file ", "status": 2, "new_ledgers": new_ledgers,
				'failed_items_name_list': [each['ledger_name'] for each in failed_items], 'failed_items': failed_items,
				'is_valid_file_headers': True}
			if request.FILES:
				return dict
			else:
				return HttpResponse(content=simplejson.dumps(dict), mimetype='application/json')

	except OpeningVoucherConstructionFailure as e:
		logger.exception("Opening Voucher Creation Failed due to %s" % e.message)
		trial_balance_dict['error'] = "%s %s" % (
			trial_balance_dict['error'] if 'error' in trial_balance_dict else "",
			"Opening Voucher Creation Failed due to %s" % e.message)
		failed_voucher_count += 1
		failed_items.append(trial_balance_dict)
	except Exception as e:
		logger.exception('Exception occurred in trial balance import : %s' % e)
		logger.info("Error %s" % Exception)

	if request.FILES:
		return {
			'message': 'Successfully imported ledger trial balance.', 'failed_items': failed_items,
			'failed_items_name_list': [each['ledger_name'] for each in failed_items]}
	else:
		return HttpResponse(content=simplejson.dumps({
			'message': 'Successfully imported', 'failed_items': failed_items,
			'failed_items_name_list': [each['ledger_name'] for each in failed_items], 'is_valid_file_headers': True}),
			mimetype='application/json')


def __validateRow(trial_balance_dict={}, enterprise_id=None):
	errors = ""
	try:
		if trial_balance_dict['ledger_name'] == '':
			raise Exception("Empty ledger name ")
		else:
			if len(trial_balance_dict['ledger_name']) > 100:
				raise Exception("Maximum length of the Ledger name should be 100.")
	except Exception as e:
		errors = "%s %s" % (errors, "%s" % e.message)

	try:
		account_group = SQLASession().query(AccountGroup).filter(
			AccountGroup.id == int(trial_balance_dict['account_group_id']) if trial_balance_dict[
				'account_group_id'].isdigit() else None,
			AccountGroup.enterprise_id == enterprise_id).first()
		if not account_group:
			raise Exception("Invalid Account group or doesn't exists .")
	except Exception as e:
		errors = "%s %s" % (errors, "%s" % e.message)

	try:
		if len(trial_balance_dict['opening_debit']) != 0 and len(trial_balance_dict['opening_credit']) != 0:
			raise Exception("Ambigous opening credit/debit .")
	except Exception as e:
		errors = "%s %s" % (errors, "%s" % e.message)

	try:
		if len(trial_balance_dict['opening_credit']) != 0 and len(trial_balance_dict['opening_debit']) == 0:
			if not float(trial_balance_dict['opening_credit']):
				raise Exception('Invalid number for opening_credit. ')
	except Exception as e:
		errors = "%s %s" % (errors, "%s" % "Invalid number for opening_credit" + e.message)

	try:
		if len(trial_balance_dict['opening_debit']) != 0 and len(trial_balance_dict['opening_credit']) == 0:
			if not float(trial_balance_dict['opening_debit']):
				raise Exception('Invalid number for opening_debit. ')
	except Exception as e:
		errors = "%s %s" % (errors, "%s" % "Invalid number for opening_debit" + e.message)

	try:
		if len(trial_balance_dict['opening_debit']) != 0 or len(trial_balance_dict['opening_credit']) != 0:
			if trial_balance_dict['opening_debit']:
				d = Decimal(trial_balance_dict['opening_debit'])
			else:
				d = Decimal(trial_balance_dict['opening_credit'])
			if -d.as_tuple().exponent > 2:
				raise Exception('Only two numbers allowed after decimal point. ')
	except Exception as e:
		errors = "%s %s" % (errors, "%s" % e.message)

	try:
		if len(trial_balance_dict['opening_debit']) >= 14 or len(trial_balance_dict['opening_credit']) >= 14:
			raise Exception('credit/debit value is out of range.')
	except Exception as e:
		errors = "%s %s" % (errors, "%s" % e.message)

	try:
		if trial_balance_dict['opening_debit'] == '' and trial_balance_dict['opening_credit'] == '':
			raise Exception('Empty opening credit/debit. ')
	except Exception as e:
		errors = "%s %s" % (errors, "%s" % e.message)

	try:
		if trial_balance_dict['opening_debit'].isdigit() and trial_balance_dict['opening_credit'].isdigit():
			if int(trial_balance_dict['opening_debit']) == 0 and int(trial_balance_dict['opening_credit']) == 0:
				raise Exception('Both opening credit/debit are zero ')
	except Exception as e:
		errors = "%s %s" % (errors, "%s" % e.message)
	return errors


def __checkNewLedgers(trial_balance_list=None, enterprise_id=None, new_ledgers=None, request=None):
	is_new_ledgers = False
	for item in trial_balance_list[1:len(trial_balance_list) - 1]:
		if request.FILES:
			trial_balance_dict = item
		else:
			trial_balance_dict = ast.literal_eval(item)
		ledger = SQLASession().query(Ledger).filter(
			Ledger.name == trial_balance_dict['ledger_name'], Ledger.group_id == trial_balance_dict['account_group_id'],
			Ledger.enterprise_id == enterprise_id).first()
		if not ledger:
			if trial_balance_dict['ledger_name'] != "":
				logger.info("Ledger doesn't exists need to create new...")
				new_ledgers.append(trial_balance_dict['ledger_name'])
				is_new_ledgers = True
	return is_new_ledgers, new_ledgers


def __constructVoucher(enterprise=None, user=None, db_session=None, valid_items=None, voucher_date=None):
	voucher_id = ""
	try:
		try:
			db_session.begin(subtransactions=True)
			if isinstance(voucher_date, basestring):
				fy = getFinancialYear(
					for_date=datetime.strptime(voucher_date, '%Y-%m-%d'), fy_start_day=enterprise.fy_start_day)
			else:
				fy = getFinancialYear(for_date=datetime.now(), fy_start_day=enterprise.fy_start_day)
			voucher = Voucher(
				enterprise_id=enterprise.id, voucher_date=voucher_date, financial_year=fy, status=1,
				approved_on=datetime.now(), approved_by=user.user_id,
				voucher_no=AccountsDAO().getLatestVoucherNo(
					financial_year=fy, type_id=1, enterprise_id=enterprise.id),
				narration="Trial balance imported on %s by %s %s" % (
					datetime.now(), user.first_name, user.last_name), created_by=user.user_id)
			db_session.add(voucher)
			db_session.commit()
			VoucherChangelog().queryVoucherInsert(user_id=user.user_id, enterprise_id=enterprise.id, data=voucher)
			db_session.refresh(voucher)
		except Exception as e:
			db_session.rollback()
			logger.exception("Opening Voucher creation Failure due to - %s" % e.message)
			raise Exception(e.message)
		item_no = 0
		for item in valid_items:
			try:
				db_session.begin(subtransactions=True)
				ledger = db_session.query(Ledger).filter(
					Ledger.group_id == item['account_group_id'], Ledger.name == item[
						'ledger_name'], Ledger.enterprise_id == enterprise.id).first()
				if not ledger:
					try:
						db_session.begin(subtransactions=True)
						is_billable = db_session.query(AccountGroup.billable).filter(
							AccountGroup.id == item['account_group_id'], Ledger.enterprise_id == enterprise.id).first()
						ledger = Ledger(
							group_id=item['account_group_id'], name=item['ledger_name'], created_by=user.user_id,
							description=item['ledger_name'], enterprise_id=enterprise.id, billable=is_billable[0])
						db_session.add(ledger)
						db_session.commit()
						db_session.refresh(ledger)
					except:
						db_session.rollback()
						raise
				voucher_id = voucher.id
				voucher_particular = VoucherParticulars(
					enterprise_id=enterprise.id, item_no=item_no, voucher_id=voucher.id, is_debit=1 if item[
						'opening_debit'] else 0, amount=item['opening_debit'] if item['opening_debit'] else item[
						'opening_credit'], ledger_id=ledger.id)
				db_session.add(voucher_particular)
				db_session.commit()
				item_no += 1
			except Exception as e:
				db_session.rollback()
	except Exception as e:
		logger.exception("Failed to persist Voucher entry for ledger - %s\nDue to - %s" % (
			item['ledger_name'], e.message))
		raise OpeningVoucherConstructionFailure(e.message)
	return voucher_id


def managePayment(request):
	"""
	Renders the main page to manage Payment.
	:param request:
	:return:
	"""
	logger.info('Manage Payment... ')
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	return TemplateResponse(template=MANAGE_PAYMENT_TEMPLATE, request=request, context={
		TEMPLATE_TITLE_KEY: 'Bill Settlements',
		'debtors': populateAccountLedgerChoices(enterprise_id=enterprise_id, group_names=(
			AccountGroup.SUNDRY_DEBTOR_GROUP_NAME, AccountGroup.OTHER_CURRENT_ASSETS_GROUP_NAME), is_debtor_creditor=True),
		'creditors': populateAccountLedgerChoices(enterprise_id=enterprise_id, group_names=(
			AccountGroup.SUNDRY_CREDITOR_GROUP_NAME,
			AccountGroup.OTHER_CURRENT_LIABILITIES_GROUP_NAME), is_debtor_creditor=True),
		'fund_ledgers': populateAccountLedgerChoices(enterprise_id=enterprise_id, group_names=[
			AccountGroup.CASH_GROUP_NAME, AccountGroup.BANK_GROUP_NAME, AccountGroup.LT_BORROWINGS_GROUP_NAME,
			AccountGroup.ST_BORROWINGS_GROUP_NAME]),
		'tags_formset': TagFormset(initial=[], prefix='tag')})


@validate_payload_with_file(required_fields=['user_id', 'enterprise_id'])
def salary_voucher(request):
	"""
	Process the salary voucher upload for an employee.
	"""
	try:
		request_handler = RequestHandler(request)

		file_path = request.FILES['file']
		user_id = int(request_handler.getPostData('user_id'))
		enterprise_id = int(request_handler.getPostData('enterprise_id'))

		processor = GeneralVouchersBulkUpload(file_stored_path=file_path, login_employee_id=user_id,
		                                      enterprise_id=enterprise_id)

		errors = processor.process()

		if errors:
			response = response_code.failure()
			response['response'] = errors
			return HttpResponse(json.dumps(response), content_type='application/json', status=400)
		else:
			response = response_code.success()
			response['response'] = "Salary voucher processed successfully."
			return HttpResponse(json.dumps(response), content_type='application/json')

	except ValueError as ve:
		logger.error("Validation error: %s", str(ve))
		response = response_code.failure()
		response['error'] = str(ve)
		return HttpResponse(json.dumps(response), content_type='application/json', status=400)
	except Exception as e:
		logger.error("While processing the salary voucher: %s", str(e), exc_info=True)
		response = response_code.internalError()
		response['error'] = str(e)
		return HttpResponse(json.dumps(response), content_type='application/json', status=500)