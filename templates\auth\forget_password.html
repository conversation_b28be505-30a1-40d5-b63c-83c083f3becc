<html lang="en">
<head>
  <title>xserp</title>
  <meta charset="utf-8">
  <link rel="icon" href="/site_media/images/xs-logo-with-border.png" type="image/png">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" type="text/css" href="/site_media/css/bootstrap.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/erp_default.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/font-awesome.min.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/JSCustomValidator.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/sweetalert.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/roboto-font.css?v={{ current_version }}" >
  <link rel="stylesheet" type="text/css" href="/site_media/css/login.css?v={{ current_version }}" >
  <link rel="stylesheet" type="text/css" href="/site_media/css/login-menu.css?v={{ current_version }}" >

  <script type="text/javascript" src="/site_media/js/jquery-3.1.1.min.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/bootstrap.min.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/jquery-ui.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/JSCustomValidator.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/sweetalert.min.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/field-validation.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/cookies.js?v={{ current_version }}"></script>
  <style type="text/css">
    @media only screen and (max-height: 700px) {
    .contact-header {
        font-size: 32px;
        margin-top: 20px;
        margin-bottom: 20px;
      }

      .xserp-contact {
        margin-bottom: 30px !important;
      }
    }
  </style>
</head>
<body>
	{% include "public/theme-header.html" %}
	<div class="main__theme__wrap contact-main-container">
    	<div class="container login-testimonial-container" style="max-width: 600px;">
		  	<div class="col-sm-12 contact-container">
			    <div class="col-sm-12 xserp-contact panel-login" style="margin-bottom: 45px;">
			    	<div class="xserp-heading">
	                	<div class="contact-header text-center">
	            			Set Your New Password
	          			</div>
	             	</div>
          			{% if token != None  %}
						<div class="form-group login-forms">
							<input id="id_token" name="token" type="password" class="form-control input-lg hide"
							       value="{{ token }}">
							<input id="id_old_password" name="old_password" type="password" class="form-control input-lg hide"
								   placeholder="Old password">
							<input id="id_user_email" name="user_email" type="password" class="form-control input-lg hide"
							       value="{{ user_email }}">
						</div>
					{% else %}
						{% if user_email != None  %}
			          		<div class="form-group wrap-input floating-label hide">
			             		<input type="text" class="form-control floating-input" name="user_email" id="id_user_email" placeholder=" " value="{{ user_email }}">
			             		<label>Username *</label>
			          		</div>
			          	{% else %}
			          		<div class="form-group wrap-input floating-label">
			             		<input type="text" class="form-control floating-input" name="user_email" id="id_user_email" placeholder=" " value="">
			             		<label>Username *</label>
			          		</div>
		          		{% endif %}
		          		<div class="form-group wrap-input floating-label">
		          			<div class="pwdMask">
			          			<input id="id_token" name="token" type="password" class="form-control input-lg hide">
			             		<input type="password" class="form-control floating-input" name="old_password" id="id_old_password" placeholder=" ">
			             		<label>Old Password *</label>
			             		<span class="fa fa-eye-slash pwd-toggle"></span>
			             	</div>
		          		</div>
					{% endif %}
					<div class="form-group wrap-input floating-label">
	          			<div class="pwdMask">
		             		<input type="password" class="form-control floating-input" name="new_password" id="id_new_password" placeholder=" " maxlength="25" >
		             		<label>Password *</label>
		             		<span class="fa fa-eye-slash pwd-toggle"></span>
		             	</div>
	          		</div>
	          		<div class="form-group wrap-input floating-label">
	          			<div class="pwdMask">
		             		<input type="password" class="form-control floating-input" name="confirm_password" id="id_confirm_password" placeholder=" " maxlength="25" >
		             		<label>Confirm Password *</label>
		             		<span class="fa fa-eye-slash pwd-toggle"></span>
		             	</div>
	          		</div>

              		<div class="form-group" style="margin-top: 40px;">
                     	<button class="btn btn-lg btn-primary btn-block" id="change_password" type="button">Change Password</button>
                  	</div>
           	 	</div>
	  	 	</div> 
	  	</div>
    </div>
	{% include "public/theme-footer.html" %}
<script type="text/javascript" src="/site_media/js/jquery.js.download"></script>
<script type="text/javascript" src="/site_media/js/login-menu.js?v={{ current_version }}"></script>
<script type="text/javascript">
	$(document).ready(function(){
		ChangePasswordValidation();
	});

	function ChangePasswordValidation(){
		$("#change_password").click(function(){
			$(".error-border").removeClass('error-border');
			$(".custom-error-message").remove();
			var ControlCollections = [
				{
					controltype: 'textbox',
					controlid: 'id_new_password',
					isrequired: true,
					errormsg: 'Password is required.',
					minvalue: 6,
					minvalerrormsg: 'Password should be atleast 6 character.'
				},
				{
					controltype: 'textbox',
					controlid: 'id_confirm_password',
					isrequired: true,
					errormsg: 'Confirm Password is required.',
					minvalue: 6,
					minvalerrormsg: 'Password should be atleast 6 character.'
				}
			];
			var result = JSCustomValidator.JSvalidate(ControlCollections);
			if (result && $('#id_token').val() == "") {
				var ControlCollections = [
					{
						controltype: 'textbox',
						controlid: 'id_old_password',
						isrequired: true,
						errormsg: 'Old password is required.'
					}
				];
				result = JSCustomValidator.JSvalidate(ControlCollections);
			}
			if ($('#id_new_password').val() != $('#id_confirm_password').val()) {
				$('#id_new_password, #id_confirm_password').addClass('error-border');
				$("#id_confirm_password").parent().find(".custom-error-message").addClass("hide");
				$("#id_confirm_password").parent().append('<span class="custom-error-message">Password and Confirm Password does not match.</span>');
				result = false;
			}

			if(result){
				if($("#id_old_password").val() != "") {
					if($("#id_old_password").val() ==  $("#id_new_password").val()){
						swal("Sorry","You old password and new password must not be same. Please try another password. ","warning")
					}
					else {
						changePassword();
					}
				}
				else {
					changePassword();
				}
			}
		});
	}

	function changePassword() {
		$("#id_new_password, #id_confirm_password").attr("type", "password");
		$("#change_password").addClass("btn-theme-processing").text("Processing...");
		$('#id_new_password').removeClass('error-border');
		$.ajax({
			url: "/erp/auth/json/change_password/",
			type: "post",
			data: {
				cp_token: $("#id_token").val(),
				user_email: $("#id_user_email").val(),
				old_password: $("#id_old_password").val(),
				new_password: $("#id_new_password").val()
			},
			success: function(response) {
				$("#change_password").removeClass("btn-theme-processing").text("Change Password");
				if (response.response_message === "Success") {
					swal({
					  title: "",
					  text: "Your Password has been changed successfully.",
					  type: "success"
					}, function () {
					    location.href = "/erp/";
					});

				} else if (response.response_message === "Session Timeout") {
					swal('Activation Link has Expired','Please regenerate it by clicking forgot password link again...','error');
	                ga('send', 'event', 'Users', 'Change Password', $('#enterprise_label').val(), 1);
				}else{
					if (response.custom_message === "Token expired!"){
						swal('Activation Link is Invalid','Please click your email for the most recently generated activation link.','error');
					}
	                ga('send', 'event', 'Users', 'Change Password', $('#enterprise_label').val(), 1);
				}
			},
			error : function(xhr,errmsg,err) {
				$("#change_password").removeClass("btn-theme-processing").text("Change Password");
				console.log(xhr.status + ": " + xhr.responseText);
			}
		});
	}

	function password_cancel() {
		history.back();
	}

	$(document).ready(function(){
          var pageHeight = (window.innerHeight-65);
          var contactContainerHeight = $(".contact-container").height();
          console.log(pageHeight, contactContainerHeight)
          var margin = 100;
          if(pageHeight > contactContainerHeight) {
            margin = (pageHeight - contactContainerHeight) / 2;
          }
          if(pageHeight <= 600) {
            margin = 35;
          }
          if(margin <= 35) {
            margin = 35;
          }
          $(".contact-main-container").css({marginTop: (margin+65)+"px"});
          $(".contact-main-container").css({marginBottom: (margin)+"px"});
      });
</script>

</body>
</html>