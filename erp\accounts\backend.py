"""
"""
import json
import pymysql
import simplejson
import sys
import traceback
import pandas as pd
from collections import OrderedDict
from copy import copy
from datetime import datetime, date
from dateutil.relativedelta import relativedelta
from decimal import Decimal
from itertools import groupby
from sqlalchemy import func, and_, desc, case
from sqlalchemy.orm import make_transient

from erp.accounts import logger, TRIAL_BALANCE, STOCK_TYPE_WIP, STOCK_TYPE_FG
from erp.accounts.changelog import VoucherChangelog
from erp.commons.backend import push_notification
from erp.dao import DataAccessObject, executeQuery
from erp.helper import getAccountGroupIDs
from erp.models import AccountNote, AccountGroup, AccountStatement, Ledger, Voucher, VoucherParticulars, AccountBook, \
	Enterprise, VoucherType, LedgerBill, Party, PartyLedgerMap, LedgerBillSettlement, VoucherTag, Project
from erp.notifications import PUSH_NOTIFICATION
from erp.properties import REQUIRED_ROW_NAMES, REQUIRED_COLUMN_NAMES
from erp.tags import getEntityTagMaps
from settings import HOST, USER, PASSWORD, DBNAME, PORT
from util import helper
from util.api_util import response_code, JsonUtil
from util.helper import getFinancialYear, getFYStartDate, getFYDateRange
from django.core.files.uploadedfile import InMemoryUploadedFile

__author__ = 'kalaivanan'


class AccountNoteVO(object):
	"""
	Value Object to hold Account Notes.
	"""

	def __init__(
			self, label="", debit=Decimal(0.00), credit=Decimal(0.00), children=[], is_negative=False, add_ons=None,
			display_debit_positive=False):
		self.label = label
		self.debit = debit
		self.credit = credit
		self.children = children
		self.is_negative = is_negative
		self.add_ons = add_ons
		self.display_debit_positive = display_debit_positive

	def __repr__(self):
		return "%s-%s-%s" % (self.label, self.debit, self.credit)

	def getBalance(self, deep=True):
		net_value = Decimal(self.debit - self.credit) if self.is_negative else Decimal(self.credit - self.debit)
		if deep:
			for _child in self.children:
				net_value += _child.getBalance()
		elif net_value == 0:
			# TODO: [UGLY-FIXIT] Some times the Parent Element has net value populated, other times it is not
			for _child in self.children:
				net_value += _child.getBalance(deep=deep)
		logger.debug("%s: %s" % (self.label, net_value))
		return net_value

	def jsonify(self):
		vo_dict = copy(self.__dict__)
		vo_dict["children"] = []
		if self.children:
			for child in self.children:
				vo_dict["children"].append(child.jsonify())
		vo_dump_string = simplejson.dumps(vo_dict)
		vo_json = simplejson.loads(vo_dump_string)
		return vo_json

	def getChild(self, label=""):
		if self.children:
			for _child in self.children:
				if _child.label == label:
					return _child
		return None


class AccountsDAO(DataAccessObject):
	"""
	DAO Object for Accounts related querying & data-processing
	"""

	def getStatementHeads(self, note_type=None, group=None):
		"""

		:param note_type:
		:param group:
		:return:
		"""
		statement_head_query = self.db_session.query(AccountStatement)
		if note_type:
			statement_head_query = statement_head_query.filter(AccountStatement.type == note_type)
		if group:
			statement_head_query = statement_head_query.filter(AccountStatement.group == group)
		return statement_head_query.order_by(AccountStatement.group.desc(), AccountStatement.item_no).all()

	def getAccountNoteHeads(self, note_type=None):
		"""

		:param note_type:
		:return:
		"""
		note_head_query = self.db_session.query(AccountNote)
		if note_type:
			note_head_query = note_head_query.filter(AccountNote.type == note_type)
		return note_head_query.all()

	def getLedgers(self, enterprise_id=None, group_ids=[], names=[]):
		"""

		:param enterprise_id:
		:param group_ids:
		:param names:
		:return:
		"""
		ledger_query = self.db_session.query(Ledger.id, Ledger.name, AccountGroup.name.label('group_name')).join(
			Ledger.group).filter(Ledger.enterprise_id == enterprise_id)
		if group_ids and len(group_ids) > 0:
			ledger_query = ledger_query.filter(Ledger.group_id.in_(group_ids))
		if names and len(names) > 0:
			ledger_query = ledger_query.filter(Ledger.name.in_(names))
		return ledger_query.all()

	def getGroups(self, names=(), enterprise_id=None):
		"""

		:param names:
		:param enterprise_id:
		:return:
		"""
		account_group_query = self.db_session.query(AccountGroup).filter(AccountGroup.enterprise_id == enterprise_id)
		if len(names) > 0:
			account_group_query = account_group_query.filter(AccountGroup.name.in_(names))
		return account_group_query.all()

	def getVoucher(
			self, enterprise_id=None, voucher_id=None,
			financial_year=None, voucher_type_id=None, voucher_no=None, sub_number=None):
		if voucher_id:
			return self.db_session.query(Voucher).filter(
				Voucher.enterprise_id == enterprise_id, Voucher.id == voucher_id).first()
		else:
			return self.db_session.query(Voucher).filter(
				Voucher.enterprise_id == enterprise_id, Voucher.financial_year == financial_year,
				Voucher.type_id == voucher_type_id,
				Voucher.voucher_no == voucher_no, Voucher.sub_number == sub_number).first()


	def getVoucherValueForLedgers(self, enterprise_id=None, voucher_id=None, ledger_ids=[], ledger_names=[]):
		"""

		:param enterprise_id:
		:param voucher_id:
		:param ledger_ids:
		:param ledger_names:
		:return:
		"""
		value = 0
		if len(ledger_ids) == 0:
			if len(ledger_names) == 0:
				return value
			ledgers = self.db_session.query(Ledger.id).filter(
				Ledger.enterprise_id == enterprise_id, Ledger.name.in_(ledger_names)).all()
			ledger_ids = [ledger.id for ledger in ledgers]
		if len(ledger_ids) == 0 or not voucher_id:
			return value
		voucher_items = self.db_session.query(VoucherParticulars.amount, VoucherParticulars.is_debit).filter(
			VoucherParticulars.enterprise_id == enterprise_id, VoucherParticulars.voucher_id == voucher_id,
			VoucherParticulars.ledger_id.in_(ledger_ids)).all()
		logger.info("Ledger Particulars Queried: %s" % [(item.amount, item.is_debit) for item in voucher_items])
		for item in voucher_items:
			if item.is_debit:
				value -= item.amount
			else:
				value += item.amount
		return value

	def getLedgersBalance(
			self, enterprise_id=None, ledger_ids=[], ledger_names=[], as_on=datetime.now(), approved_only=True,
			is_debit_positive=False):
		"""

		:param enterprise_id:
		:param ledger_ids:
		:param ledger_names:
		:param as_on:
		:param approved_only:
		:param is_debit_positive:
		:return:
		"""
		value = 0
		if len(ledger_ids) == 0:
			if len(ledger_names) == 0:
				return value
			ledgers = self.db_session.query(Ledger).filter(
				Ledger.enterprise_id == enterprise_id, Ledger.name.in_(ledger_names)).all()
		else:
			ledgers = self.db_session.query(Ledger).filter(
				Ledger.enterprise_id == enterprise_id, Ledger.id.in_(ledger_ids)).all()
		for _ledger in ledgers:
			value += _ledger.getBalance(till=as_on, approved_only=approved_only, debit_positive=is_debit_positive)
		return value

	def getBooks(self, enterprise_id=None, since=None, till=None, count=None):
		"""
		Queries the List of AccountBooks for the enterprise mentioned for the period specified.

		:param enterprise_id:
		:param since:
		:param till:
		:param count:
		:return:
		"""
		since = since if since else datetime(1900, 1, 1)
		till = till if till else datetime.today()
		book_query = self.db_session.query(AccountBook).filter(
			AccountBook.enterprise_id == enterprise_id, AccountBook.since >= since, AccountBook.till <= till).order_by(
			AccountBook.till.desc())
		if count:
			book_query = book_query.limit(count)
		return book_query.all()

	def getLatestVoucherNo(self, financial_year=None, type_id=None, enterprise_id=None):
		"""
		Useful in Voucher Code generation. Fetches the latest 'voucher_no' for criteria collected as Parameters.
		:param financial_year:
		:param type_id:
		:param enterprise_id:
		:return:
		"""
		latest_voucher = self.db_session.query(func.max(Voucher.voucher_no).label("voucher_no")).filter(
			Voucher.financial_year == financial_year, Voucher.type_id == type_id,
			Voucher.enterprise_id == enterprise_id).first()
		return latest_voucher.voucher_no + 1 if latest_voucher and latest_voucher.voucher_no else 1

	def getLedgerBill(self, enterprise_id=None, bill_id=None):
		"""

		:param enterprise_id:
		:param bill_id:
		:return:
		"""
		return self.db_session.query(LedgerBill).filter(
			LedgerBill.enterprise_id == enterprise_id, LedgerBill.id == bill_id).first()

	def isReceivableLedger(self, enterprise_id=None, ledger_id=None):
		"""

		:param enterprise_id:
		:param ledger_id:
		:return:
		"""
		_ledger = self.db_session.query(Ledger.group_id).join(
			Ledger.group).filter(Ledger.enterprise_id == enterprise_id, Ledger.id == ledger_id).first()
		return _ledger.group_id in getAccountGroupIDs(
			enterprise_id=enterprise_id,
			names=(AccountGroup.SUNDRY_DEBTOR_GROUP_NAME, AccountGroup.OTHER_CURRENT_ASSETS_GROUP_NAME,))

	def getIsBillableAccountGroup(self, enterprise_id=None, group_id=None):
		"""

		:param enterprise_id:
		:param group_id:
		:return:
		"""
		is_billable = self.db_session.query(AccountGroup.billable).filter(
			AccountGroup.enterprise_id == enterprise_id, AccountGroup.id == group_id).first()
		return is_billable

	def getDefaultProjectID(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		project_id = self.db_session.query(Project.id).filter(
			Project.enterprise_id == enterprise_id, Project.name == 'DEFAULT').first()
		return int(project_id[0])


class AccountService:
	"""
	Serves Financial Accounts related services like - Statement generation, Book Closure, etc.
	"""

	def __init__(self):
		self.accounts_dao = AccountsDAO()

	def getBooks(self, enterprise_id=None, since=None, till=None, count=None):
		"""
		Fetches list of count number of Account Book objects that contains a Snapshot of the Frozen Books for an
		enterprise, for the period specified

		:param enterprise_id:
		:param since:
		:param till:
		:param count: no of recent books to be fetched
		:return:
		"""
		since = since if since else datetime(1900, 1, 1)
		till = till if till else datetime.today()
		return self.accounts_dao.getBooks(enterprise_id=enterprise_id, since=since, till=till, count=count)

	def reOpenBooks(self, enterprise_id=None, sender_id=None, since=None):
		"""
		Service to re-open Books for an enterprise, that were closed earlier since a given date

		:param enterprise_id:
		:param sender_id:
		:param since:
		:return:
		"""
		since = since if since else datetime(1900, 1, 1)
		_books_to_be_reopened = self.accounts_dao.getBooks(enterprise_id=enterprise_id, since=since)
		_db_session = self.accounts_dao.db_session
		_db_session.begin(subtransactions=True)
		try:
			for _book in _books_to_be_reopened:
				_db_session.delete(_book)
			_db_session.commit()
			message = """Financial statements for the period %s - %s has been finalised and the Account book
			has been reopend for the said period""" % (
				since.strftime("%b %d, %Y"), datetime.today().strftime("%b %d, %Y"))
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, message=message)
			return True
		except Exception as e:
			logger.exception("Re-opening Book for Enterprise - %s since - %s failed! %s" % (enterprise_id, since, e))
			_db_session.rollback()
			return False

	def generateBookVO(self, enterprise_id=None, since=None, till=None):
		"""
		Fetch Closed Book for the given criteria & Construct a VO presentable to UI

		:param enterprise_id:
		:param since:
		:param till:
		:return:
		"""
		since = since if since else datetime(1900, 1, 1)
		till = till if till else datetime.today()
		_book = self.getBooks(enterprise_id=enterprise_id, since=since, till=till, count=1)[0]
		_book_vo = self._noteJSON2noteVO(_book.snapshot)
		return _book_vo

	def _noteJSON2noteVO(self, note_json={}):
		"""
		Helper method to construct AccounNoteVo from AccountNote JSON

		:param note_json:
		:return:
		"""
		_note_vo = AccountNoteVO(
			label=note_json["label"], debit=note_json["debit"], credit=note_json["credit"],
			is_negative=note_json["is_negative"], add_ons=note_json["add_ons"], children=[],
			display_debit_positive=note_json["display_debit_positive"])
		for _child in note_json["children"]:
			_note_vo.children.append(self._noteJSON2noteVO(note_json=_child))
		return _note_vo

	def closeBook(
			self, account_book_vo=None, enterprise_id=None, since=datetime(1900, 1, 1), till=datetime.today(),
			financial_year=None, created_by=None, inclusions={}):
		"""
		Finalize a Financial Statement specified for an enterprise executed for the period mentioned.
		Close all pending P&L Accounts for the period, Carrying forward the Profit earned or Loss incurred during the
		specified period into the future, by creating a Book Closure Voucher into the '[Reserves and Surpluses] Profit
		and Loss Account'.

		:param account_book_vo:
		:param enterprise_id:
		:param since:
		:param till:
		:param financial_year:
		:param created_by:
		:param inclusions:
		:return:
		"""
		if not account_book_vo:
			return False
		_db_session = self.accounts_dao.db_session
		_db_session.begin(subtransactions=True)
		try:
			_account_book = AccountBook(
				enterprise_id=enterprise_id, financial_year=financial_year, since=since, till=till,
				created_on=datetime.now(), created_by=created_by, snapshot=account_book_vo.jsonify())
			closure_voucher = Voucher(
				enterprise_id=enterprise_id, voucher_date=till, type_id=Voucher.CLOSURE_TYPE, created_by=created_by,
				approved_on=_account_book.created_on, status=Voucher.APPROVED, created_on=_account_book.created_on,
				approved_by=created_by, financial_year=financial_year,
				narration="[P&L Book Closure] For period between %s and %s. Closed on %s" % (
					since, till, _account_book.created_on))
			closure_voucher.project_code = self.accounts_dao.getDefaultProjectID(enterprise_id=enterprise_id)
			closure_voucher.particulars = self._generateClosureVoucherParticulars(
				enterprise_id=enterprise_id, trial_balance=account_book_vo.getChild(TRIAL_BALANCE),
				inclusions=inclusions)
			closure_voucher.voucher_no = self.accounts_dao.getLatestVoucherNo(
				financial_year=financial_year, type_id=Voucher.CLOSURE_TYPE, enterprise_id=enterprise_id)
			_account_book.closure_voucher = closure_voucher
			_db_session.add(_account_book)
			logger.info("Closure Voucher after populating P&L Value - Code: %s | FY: %s" % (
				closure_voucher.voucher_no, closure_voucher.financial_year))
			_db_session.commit()
			VoucherChangelog().queryVoucherInsert(user_id=created_by, enterprise_id=enterprise_id, data=closure_voucher)
			message = """Financial statements for the period %s - %s has been finalised and the Account book
			has been closed for the said period""" % (since.strftime("%b %d, %Y"), till.strftime("%b %d, %Y"))
			push_notification(
				enterprise_id=enterprise_id, sender_id=created_by, message=message)
			return {"success": True, "voucher": closure_voucher}
		except Exception as e:
			logger.exception("Account Book Closure failed due to %s!" % e.message)
			_db_session.rollback()
			return {"success": False, "voucher": None}

	def _generateClosureVoucherParticulars(self, enterprise_id=None, trial_balance=None, inclusions={}):
		"""
		Generate the Closure Voucher that carries forward the said period's Profit/Loss into the Future.

		:param enterprise_id:
		:param trial_balance:
		:param inclusions:
		:return:
		"""
		_pnl_group = self.accounts_dao.getGroups(names=["Profit and Loss"], enterprise_id=enterprise_id)
		_pnl_ledgers = _pnl_group[0].getAllLedgersInAndUnder()
		_closure_voucher_particulars = []
		net_value = 0
		i = 0
		logger_str = ""
		for _ledger in _pnl_ledgers:
			_ledger_key = "[%s] %s" % (_ledger.group.name, _ledger.name)
			_ledger_in_trial_balance = trial_balance.getChild(label=_ledger_key)
			_value = 0
			if _ledger_in_trial_balance and (_ledger_in_trial_balance.credit != _ledger_in_trial_balance.debit):
				_value = _ledger_in_trial_balance.getBalance()
			if _ledger.group.name == "Cost of Raw Materials Consumed" and _ledger.name == "Closing Stock (Components)":
				_value += Decimal(inclusions["consumables_closing"])
			elif _ledger.group.name == "Change In Stock":
				if _ledger.name == "Closing Stock (Finished Goods)":
					_value += Decimal(inclusions["fg_closing"])
				elif _ledger.name == "Closing Stock (WIP)":
					_value += Decimal(inclusions["wip_closing"])
			if _value != 0:
				net_value += _value
				_closure_voucher_particulars.append(
					VoucherParticulars(
						amount=abs(round(_value, 2)), is_debit=(_value > 0), item_no=i + 1, ledger_id=_ledger.id,
						enterprise_id=enterprise_id))
				i += 1
				logger_str = "%s%s, %s, %s\n" % (logger_str, _ledger.id, _ledger, _value)
		logger.info("P&L Ledger Values:\n%s" % logger_str)
		if net_value != 0:
			_pnl_reserve = self.accounts_dao.getLedgers(
				enterprise_id=enterprise_id, group_ids=getAccountGroupIDs(
					names=["Reserves and Surpluses"], enterprise_id=enterprise_id), names=["Profit and Loss Account"])
			_closure_voucher_particulars.append(
				VoucherParticulars(
					amount=abs(round(net_value, 2)), is_debit=(net_value < 0), ledger_id=_pnl_reserve[0].id,
					item_no=i + 1, enterprise_id=enterprise_id))
			logger.info("P&L To Reserves - %s: %s" % (_pnl_reserve[0].name, net_value))
		return _closure_voucher_particulars

	def prepareStatement(self, note_type=None, group=None, note_values={}):
		"""
		Prepare an AccountNoteVO containing the Statement details. Updating the same into the note_values Dictionary

		:param note_type:
		:param group:
		:param note_values:
		:return:
		"""
		logger.info("Notes for the Statement - %s" % note_type)
		statement_heads = self.accounts_dao.getStatementHeads(note_type=note_type, group=group)
		logger.debug("Note Statements: %s" % statement_heads)
		statement_note_header = AccountNoteVO(
			label="%s" % (group if group else note_type), credit=0, debit=0, children=[])
		statement_values = {}
		for head in statement_heads:
			particulars = []
			net_value = 0
			logger.debug("Inclusions: %s - %s" % (head.inclusion, head.note_inclusion))
			if head.inclusion and len(head.inclusion) > 0:
				if "inclusions" in head.inclusion:
					for inclusion in head.inclusion["inclusions"]:
						net_value += statement_values["%s:%s" % (inclusion["type"], inclusion["id"])]
				if "exclusions" in head.inclusion:
					for exclusion in head.inclusion["exclusions"]:
						net_value -= statement_values["%s:%s" % (exclusion["type"], exclusion["id"])]

			for note_header in head.notes:
				note_value = note_values[note_header.description] if note_header.description in note_values else 0
				note_node = AccountNoteVO(
					label="%s [%s]" % (note_header.description, note_header.id),
					debit=note_value * -1 if note_value < 0 else 0, credit=note_value if note_value > 0 else 0,
					display_debit_positive=head.display_debit_positive)
				particulars.append(note_node)
				net_value += note_value
			statement_values["%s:%s" % (head.type, head.item_no)] = net_value
			if head.note_inclusion and len(head.note_inclusion) > 0:
				if "notes" in head.note_inclusion:
					debit = 0
					credit = 0
					for note in head.note_inclusion["notes"]:
						key = "%s" % note["id"]
						if key in note_values:
							debit += abs(note_values[key]) if note_values[key] < 0 else 0
							credit += note_values[key] if note_values[key] > 0 else 0
					logger.info("Note Inclusion: %s Included: %s" % (
						head.note_inclusion,
						head.note_inclusion["condition"] == '0' or (
								head.note_inclusion["condition"] == '1' and credit >= debit) or (
								head.note_inclusion["condition"] == '2' and credit <= debit)))
					if head.note_inclusion["condition"] == '0' or (
							head.note_inclusion["condition"] == '1' and credit >= debit) or (
							head.note_inclusion["condition"] == '2' and credit <= debit):
						logger.info(
							"\n---------\n%s. %s\n----------" % (statement_heads.index(head), head.note_inclusion))
						particulars.append(
							AccountNoteVO(label=head.note_inclusion["label"], debit=debit, credit=credit))
						net_value += credit - debit
			statement_note_header.children.append(
				AccountNoteVO(
					label="%s" % head.title, children=particulars, debit=abs(net_value) if net_value < 0 else 0,
					credit=net_value if net_value > 0 else 0, display_debit_positive=head.display_debit_positive))
		return statement_note_header

	def prepareTrialBalance(self, enterprise_id=None, closing_on=None):
		"""
		Generate  the TrialBalance as a list of AccountNoteVOs for the Enterprise mentioned as on the date specified.

		:param enterprise_id:
		:param closing_on:
		:return:
		"""
		closing_on = closing_on if closing_on else datetime.now()
		trial_notes = []
		net_credit = 0
		net_debit = 0
		try:
			get_ledger_value_query = """SELECT  an.id, an.description, ag.name, an.add_ons, an.type,
						al.name AS ledger_name, SUM(CASE vp.is_debit WHEN 0 THEN vp.amount ELSE 0 END) AS credit,
						SUM(CASE vp.is_debit WHEN 1 THEN vp.amount ELSE 0 END) AS debit
					FROM
						account_groups ag
							LEFT JOIN account_ledgers al ON al.group_id = ag.id AND al.enterprise_id = ag.enterprise_id
								AND ag.enterprise_id = %s
							LEFT JOIN voucher_particulars vp ON vp.ledger_id = al.id
								AND vp.enterprise_id=al.enterprise_id
							LEFT JOIN voucher v ON vp.voucher_id = v.id
								AND vp.enterprise_id=v.enterprise_id
								AND DATE(v.voucher_date) <= DATE('%s')
							LEFT JOIN account_notes an ON ag.note_no = an.id 
					WHERE
						ag.id != 0 AND v.status = 1 
					GROUP BY al.id ORDER BY an.id""" % (enterprise_id, closing_on.strftime('%Y-%m-%d'))
			logger.debug("Trial Balance Query: %s" % get_ledger_value_query)
			conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
			cur = conn.cursor()
			cur.execute(get_ledger_value_query)
			names = [desc[0] for desc in cur.description]
			ledgers = [dict(zip(names, row)) for row in cur.fetchall()]
			conn.close()
			for ledger in ledgers:
				balance = ledger['credit'] - ledger['debit']
				trial_notes.append(AccountNoteVO(
					label="%s" % "[" + ledger['name'] + "] " + ledger['ledger_name'],
					debit=(balance * -1) if balance < 0 else 0, credit=balance if balance >= 0 else 0))
				if balance > 0:
					net_credit += balance
				else:
					net_debit += (balance * -1)

			trial_notes.insert(0, AccountNoteVO(
				label="<center><b>Net Balance</b></center>", credit=net_credit, debit=net_debit))
		except Exception as e:
			logger.exception('%s' % e)
		return trial_notes

	def prepareNotes(
			self, enterprise_id=None, note_type="", opening_on=datetime(1900, 1, 1), closing_on=datetime.now()):
		"""
		Construct the Notes as a list of AccountNoteVOs, that will be used to construct Financial Statements for the
		Enterprise mentioned, for the period specified.

		:param enterprise_id:
		:param note_type:
		:param opening_on:
		:param closing_on:
		:return:
		"""
		note_values = {}
		try:
			get_note_value_query = """SELECT an.id, an.description , ag.name, an.add_ons, an.type,
						ag.id as account_group_id, al.name as ledger_name,
						SUM(CASE vp.is_debit WHEN 0 THEN vp.amount ELSE 0 END) AS credit,
						SUM(CASE vp.is_debit WHEN 1 THEN vp.amount ELSE 0 END) AS debit,
						stmt.display_debit_positive AS display_debit_positive, an.alternate_note_id as alternate_note_id,
						an.alternate_note_id_description as alternate_note_id_description
					FROM
						account_ledgers al
							INNER JOIN voucher_particulars vp ON vp.ledger_id = al.id
							INNER JOIN voucher v ON (vp.voucher_id = v.id 
								AND DATE(v.voucher_date) >= DATE('{opening_date}')
								AND DATE(v.voucher_date) <= DATE('{closing_date}') AND v.status = 1)
							RIGHT JOIN account_groups ag ON al.group_id = ag.id AND al.enterprise_id = ag.enterprise_id
								AND ag.enterprise_id = {enterprise_id}
							RIGHT JOIN account_notes an ON an.id = ag.note_no AND ag.enterprise_id = {enterprise_id}
							JOIN account_statements stmt ON an.statement_no = stmt.item_no AND an.type = stmt.type
					WHERE
						ag.id != 0 AND an.id IS NOT NULL AND an.type='{type}'
					GROUP BY al.id, ag.id, an.id
					ORDER BY an.id, ag.id, SUM(CASE vp.is_debit
							WHEN 1 THEN vp.amount
							ELSE 0
						END) - SUM(CASE vp.is_debit
							WHEN 0 THEN vp.amount
							ELSE 0
						END)""".format(
				opening_date=opening_on.strftime('%Y-%m-%d'), closing_date=closing_on.strftime('%Y-%m-%d'),
				enterprise_id=enterprise_id, type=note_type)
			logger.debug("Notes Query - %s" % get_note_value_query)
			conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
			cur = conn.cursor()
			cur.execute(get_note_value_query)
			names = [description[0] for description in cur.description]
			notes = [dict(zip(names, row)) for row in cur.fetchall()]
			conn.close()
			account_note_dict, account_note_description_dict, account_note_group_dict = self.generateAccountNotes(notes=notes)
			account_note_dict, note_values = self.splitNoteValues(
				account_note_dict=account_note_dict, notes=notes, account_note_group_dict=account_note_group_dict,
				account_note_description_dict=account_note_description_dict)
			account_note_vo_list = account_note_dict.values()
		except Exception as e:
			account_note_vo_list = []
			logger.exception('%s' % e)
		return account_note_vo_list, note_values

	def generateAccountNotes(self, notes=None):
		account_note_dict = {}
		account_note_description_dict = {}
		account_note_group_dict = {}
		try:
			for key1, note_group in groupby(notes, lambda n: n["id"]):
				note_group = list(note_group)
				account_note = AccountNoteVO(
					label=note_group[0]['description'], children=[],
					add_ons=note_group[0]['add_ons'], display_debit_positive=note_group[0]['display_debit_positive'])
				account_note_dict[key1] = account_note
				account_note_description_dict[key1] = note_group[0]['description']
			for key1, note_group in groupby(notes, lambda n: n["id"]):
				note_group = list(note_group)
				account_note = account_note_dict[key1]
				for key2, group in groupby(note_group, lambda n: n["account_group_id"]):
					group = list(group)
					key = "%s:%s" % (key1, group[0]['name'])
					account_group = AccountNoteVO(
						label="%s%s" % (" " * 2, group[0]['name']), children=[],
						display_debit_positive=group[0]['display_debit_positive'])
					account_note_group_dict[key] = account_group
					account_note.children.append(account_group)
				if note_group[0]['alternate_note_id']:
					alternate_note_account = account_note_dict[note_group[0]['alternate_note_id']]
					key = "%s:%s" % (note_group[0]['alternate_note_id'], note_group[0]['alternate_note_id_description'])
					alternate_account_group = AccountNoteVO(
						label="%s%s" % (" " * 2, note_group[0]['alternate_note_id_description']), children=[],
						display_debit_positive=1 if int(note_group[0]['display_debit_positive']) == 0 else 0)
					account_note_group_dict[key] = alternate_account_group
					alternate_note_account.children.append(alternate_account_group)
		except Exception as e:
			logger.exception('%s' % e)
		return account_note_dict, account_note_description_dict, account_note_group_dict

	def splitNoteValues(
			self, account_note_dict=None, notes=None, account_note_group_dict=None, account_note_description_dict=None):
		note_values = {}
		try:
			for key1, note_group in groupby(notes, lambda n: n["id"]):
				note_group = list(note_group)
				account_note = account_note_dict[key1]
				note_group_debit, note_group_credit = 0, 0
				alternate_note_group_debit, alternate_note_group_credit = 0, 0
				alternate_account_note = None
				if note_group[0]['alternate_note_id']:
					alternate_account_note = account_note_dict[note_group[0]['alternate_note_id']]
				for key2, group in groupby(note_group, lambda n: n["account_group_id"]):
					group = list(group)
					group_debit, group_credit = 0, 0
					alternate_group_debit, alternate_group_credit = 0, 0
					key = "%s:%s" % (key1, group[0]['name'])
					account_group = account_note_group_dict[key]
					alternate_account_group = None
					if alternate_account_note:
						key = "%s:%s" % (note_group[0]['alternate_note_id'], note_group[0]['alternate_note_id_description'])
						alternate_account_group = account_note_group_dict[key]
					for ledger in group:
						if ledger['ledger_name']:
							if alternate_account_note and ((int(ledger['debit']) > int(ledger['credit'])) != group[0]['display_debit_positive']):
								alternate_account_group.children.append(
									AccountNoteVO(
										label="%s%s" % (" " * 4, ledger['ledger_name']), children=[], debit=ledger['debit'],
										credit=ledger['credit'],
										display_debit_positive=1 if int(ledger['display_debit_positive']) == 0 else 0))
								alternate_note_group_debit += ledger['debit'] if ledger['debit'] else 0
								alternate_note_group_credit += ledger['credit'] if ledger['credit'] else 0
								alternate_group_debit += ledger['debit'] if ledger['debit'] else 0
								alternate_group_credit += ledger['credit'] if ledger['credit'] else 0
							else:
								account_group.children.append(
									AccountNoteVO(
										label="%s%s" % (" " * 4, ledger['ledger_name']), children=[], debit=ledger['debit'],
										credit=ledger['credit'], display_debit_positive=ledger['display_debit_positive']))
								note_group_debit += ledger['debit'] if ledger['debit'] else 0
								note_group_credit += ledger['credit'] if ledger['credit'] else 0
								group_debit += ledger['debit'] if ledger['debit'] else 0
								group_credit += ledger['credit'] if ledger['credit'] else 0

					account_group.credit += Decimal(group_credit)
					account_group.debit += Decimal(group_debit)
					if alternate_account_note:
						logger.debug("account_note_alternate_children:%s" % [alternate_group_credit, alternate_group_debit])
						alternate_account_group.credit += Decimal(alternate_group_credit)
						alternate_account_group.debit += Decimal(alternate_group_debit)
				if note_group[0]['description'] not in note_values:
					note_values[note_group[0]['description']] = Decimal(0.0)
				note_values[note_group[0]['description']] += note_group_credit - note_group_debit
				account_note.debit += Decimal(note_group_debit)
				account_note.credit += Decimal(note_group_credit)
				if alternate_account_note:
					description = account_note_description_dict.get(note_group[0]['alternate_note_id'], "None")
					if description not in note_values:
						note_values[description] = Decimal(0.0)
					note_values[description] += alternate_note_group_credit - alternate_note_group_debit
					alternate_account_note.debit += Decimal(alternate_note_group_debit)
					alternate_account_note.credit += Decimal(alternate_note_group_credit)
		except Exception as e:
			logger.exception('%s' % e)
		return account_note_dict, note_values

	def addInclusionsAndExclusions(self, account_note_vo_dicts={}, note_values={}, inclusion_values={}):
		"""
		Places Note & Statement Inclusions & Exclusions [Values external to the Vouchers & Ledgers so Far, but can be fetched
		by querying the System] at appropriate

		:param account_note_vo_dicts:
		:param note_values:
		:param inclusion_values:
		:return:
		"""
		try:
			pnl_notes = account_note_vo_dicts["pl"]
			for account_note in account_note_vo_dicts["pl"]:
				note_add_on = json.loads(account_note.add_ons) if account_note.add_ons else None
				if note_add_on is not None:
					if "inclusions" in note_add_on:
						for inclusion in note_add_on["inclusions"]:
							inclusion_value = inclusion_values[inclusion["key"]]
							debit = inclusion_value * (-1 if inclusion_value < 0 else 0)
							credit = inclusion_value if inclusion_value > 0 else 0
							account_note.children.append(
								AccountNoteVO(
									label="%s%s" % (" " * 2, "%s" % inclusion["label"]), children=[], debit=debit,
									credit=credit))
							note_values[account_note.label] += Decimal(credit - debit)
							account_note.credit += Decimal(credit)
							account_note.debit += Decimal(debit)
							if inclusion["key"] in ("goods_closing", "consumables_closing"):
								# FIXME: Work to remove this Hard-coded reversal of values
								note_values[inclusion["key"]] = Decimal(debit - credit)
					if "exclusions" in note_add_on:
						for exclusion in note_add_on["exclusions"]:
							exclusion_value = inclusion_values[exclusion["key"]]
							debit = exclusion_value * (-1 if exclusion_value < 0 else 0)
							credit = exclusion_value if exclusion_value > 0 else 0
							account_note.children.insert(0, AccountNoteVO(
								label="%s%s" % (" " * 2, "%s" % exclusion["label"]), debit=debit, credit=credit,
								is_negative=True, display_debit_positive=True))
							note_values[account_note.label] -= Decimal(credit - debit)
							account_note.credit -= Decimal(credit)
							account_note.debit -= Decimal(debit)
					if "info" in note_add_on:
						for info in note_add_on["info"]:
							info_value = inclusion_values[info["key"]]
							account_note.children.append(AccountNoteVO(
								label="<b><i>%s%s</i>: %s</b>" % (" " * 2, "%s" % info["label"], info_value),
								children=[], debit=0, credit=0, is_negative=True))
			inclusion_values["period_profit"] = self.getProfitForGivenPeriod(
				pnl_notes=pnl_notes, note_values=note_values)
			for account_note in account_note_vo_dicts["bs"]:
				note_add_on = json.loads(account_note.add_ons) if account_note.add_ons else None
				if note_add_on is not None:
					if "inclusions" in note_add_on:
						for inclusion in note_add_on["inclusions"]:
							inclusion_value = inclusion_values[inclusion["key"]]
							debit = inclusion_value * (-1 if inclusion_value < 0 else 0)
							credit = inclusion_value if inclusion_value > 0 else 0
							account_note.children.append(AccountNoteVO(
								label="%s%s" % (" " * 2, "%s" % inclusion["label"]), children=[], debit=debit,
								credit=credit, is_negative=False))
							note_values[account_note.label] += Decimal(credit - debit)
							account_note.credit += Decimal(credit)
							account_note.debit += Decimal(debit)
					if "exclusions" in note_add_on:
						for inclusion in note_add_on["exclusions"]:
							inclusion_value = inclusion_values[inclusion["key"]]
							debit = inclusion_value * (-1 if inclusion_value < 0 else 0)
							credit = inclusion_value if inclusion_value > 0 else 0
							account_note.children.append(AccountNoteVO(
								label="%s%s" % (" " * 2, "%s" % inclusion["label"]), children=[], debit=debit,
								credit=credit, is_negative=False))
							note_values[account_note.label] += Decimal(credit - debit)
							account_note.credit += Decimal(credit)
							account_note.debit += Decimal(debit)
			account_note_vo_dicts = account_note_vo_dicts["bs"] + account_note_vo_dicts["pl"]
			logger.debug("Note Values: %s" % note_values)
		except Exception as e:
			logger.exception('%s' % e)
			traceback.print_exc(file=sys.stdout)
		return account_note_vo_dicts

	def getStockValue(self, enterprise_id=None, as_on=None, type=None, **kwargs):
		"""

		:param enterprise_id:
		:param as_on:
		:param type:
		:param kwargs:
		:return:
		"""
		as_on = as_on if as_on else datetime.now()
		stock_value = 0
		if type:
			if type == STOCK_TYPE_WIP:
				categories_condition = " IN (\"Semi Finished Goods\") "
				stock_ledger_names = ["Closing Stock (WIP)"]
			elif type == STOCK_TYPE_FG:
				categories_condition = " IN (\"Finished Goods\") "
				stock_ledger_names = ["Closing Stock (Finished Goods)"]
			else:
				categories_condition = " NOT IN (\"Finished Goods\", \"Semi Finished Goods\") "
				stock_ledger_names = ["Closing Stock (Components)"]
		else:
			categories_condition = " NOT IN (\"Finished Goods\", \"Semi Finished Goods\") "
			stock_ledger_names = ["Closing Stock (Components)"]
		previous_closing_voucher = self.accounts_dao.getPreviousBookClosureVoucher(
			enterprise_id=enterprise_id, before=as_on)
		opening_date = self.accounts_dao.getEarliestTransactionDate(enterprise_id=enterprise_id)
		if previous_closing_voucher:
			opening_date = previous_closing_voucher.voucher_date
			stock_value -= self.accounts_dao.getLedgersBalance(
				enterprise_id=enterprise_id, approved_only=True, as_on=opening_date, ledger_names=stock_ledger_names)

		logger.debug(
			"Previous Book Closure Voucher: %s | Opening Value for %s: %s | Opening date: %s | As On date: %s" % (
				previous_closing_voucher, stock_ledger_names, stock_value, opening_date, as_on))
		if (as_on - relativedelta(days=1)) == opening_date:
			# If as_on is exactly a day after the Book closure voucher then the closure value must be the Opening value.
			return round(stock_value, 2)
		try:
			get_stock_value_query = """SELECT SUM(IFNULL(stock_value, 0)) AS final_stock_value
				FROM (SELECT SUM(IFNULL(stockvalue, 0)) AS stock_value
					FROM (SELECT SUM(IFNULL(grnm.acc_qty, 0)) * m.price AS stockvalue
						FROM grn_material grnm
								JOIN grn g ON g.grn_no = grnm.grnNumber AND g.enterprise_id=grnm.enterprise_id
									AND g.inward_date < '%s'
									AND g.status > -1
									AND g.enterprise_id = %s
								JOIN materials m ON m.id = grnm.item_id
									AND m.enterprise_id = grnm.enterprise_id
									AND m.in_use=1 AND m.is_stocked=1 AND m.price != 0
								JOIN material_category mc ON m.category = mc.id
									AND m.enterprise_id = mc.enterprise_id AND mc.name %s
					GROUP BY m.id, m.enterprise_id) AS RECEIVED_T
					UNION SELECT SUM(IFNULL(stockvalue, 0)) * -1 AS stock_value
						FROM (SELECT SUM(IFNULL(im.qty, 0)) * m.price AS stockvalue
							FROM invoice_materials im
									JOIN invoice i ON i.id = im.invoice_id AND i.enterprise_id=im.enterprise_id
										AND i.status > 0 AND i.prepared_on < '%s' AND i.enterprise_id = %s
									JOIN materials m ON m.id = im.item_id
										AND im.enterprise_id = m.enterprise_id
										AND m.in_use=1 AND m.is_stocked=1 AND m.price != 0
									JOIN material_category mc ON m.category = mc.id
										AND m.enterprise_id = mc.enterprise_id AND mc.name %s
					GROUP BY m.id, m.enterprise_id) AS SOLD_T) AS STOCK""" % (
				as_on.strftime('%Y-%m-%d'), enterprise_id, categories_condition,
				as_on.strftime('%Y-%m-%d'), enterprise_id, categories_condition)
			logger.debug("Stock query: %s" % get_stock_value_query)
			conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
			cur = conn.cursor()
			cur.execute(get_stock_value_query)
			names = [description[0] for description in cur.description]
			stock_values = [dict(zip(names, row)) for row in cur.fetchall()]
			conn.close()
			stock_value = stock_values[0]['final_stock_value']
		except Exception as e:
			logger.exception('%s' % e)

		return round(stock_value, 2)

	def getSalesTaxesValue(self, enterprise_id=None, since=None, till=None, projects=None):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:param projects:
		:return:
		"""
		project_condition = " WHERE i.project_code in (%s)" % projects if projects else ""
		since = since if since else datetime(1900, 1, 1)
		till = till if till else datetime.today()
		sales_tax = 0
		net_sales_tax_query = """SELECT SUM(IFNULL(sale_item_tax, 0)) AS net_sales_tax
		FROM (SELECT
				SUM(ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2)) AS sale_item_tax
			FROM invoice i
			JOIN invoice_materials im ON i.id = im.invoice_id
				AND i.enterprise_id = %s AND i.status > 0 AND i.approved_on BETWEEN '%s' AND '%s'
			LEFT JOIN invoice_tax it ON it.invoice_id = i.id
				AND it.enterprise_id=i.enterprise_id
			LEFT JOIN invoice_item_tax iit ON im.item_id = iit.item_id
				AND i.id = iit.invoice_id
				AND iit.enterprise_id = i.enterprise_id
			JOIN tax t ON (iit.tax_code = t.code
				AND i.enterprise_id = t.enterprise_id)
				OR (it.tax_code = t.code
					AND it.enterprise_id = t.enterprise_id)
			JOIN materials m ON im.item_id = m.id
				AND im.enterprise_id = m.enterprise_id %s ) AS sales_tax_value""" % (
			enterprise_id, since, till, project_condition)
		try:
			logger.debug("Tax query: %s" % net_sales_tax_query)
			conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
			cur = conn.cursor()
			cur.execute(net_sales_tax_query)
			names = [desc[0] for desc in cur.description]
			stock_values = [dict(zip(names, row)) for row in cur.fetchall()]
			conn.close()
			sales_tax = stock_values[0]['net_sales_tax']
		except Exception as e:
			logger.exception('%s' % e)
		return sales_tax

	def getProfitForGivenPeriod(self, pnl_notes=(), note_values={}):
		"""

		:param pnl_notes:
		:param note_values:
		:return:
		"""
		logger.info("Calculating Profit begin @ %s", datetime.now())
		profit = 0
		for _note_item in pnl_notes:
			profit = profit + note_values[_note_item.label]
		logger.info("Profit calculated @ %s | Profit - %s" % (datetime.now(), profit))
		return profit

	@staticmethod
	def getLedgerSnapShotAsMap(
			since=datetime(1, 1, 1), till=datetime.now(), ledger=None, approved_only=True, debit_positive=False):
		"""

		:param since:
		:param till:
		:param ledger:
		:param approved_only:
		:param debit_positive:
		:return:
		"""
		try:
			balance = float(ledger.getBalance(
				since=since, till=till, approved_only=approved_only, debit_positive=debit_positive))
			return {'id': ledger.id, 'name': ledger.name, 'group_name': ledger.group.name, 'closing_balance': balance}
		except Exception:
			raise

	def notifyPendingVoucherCount(self, enterprise_id=None, sender_id=None,code=None,type=None,voucher_count=0):
		"""

		:param enterprise_id:
		:param sender_id:
		:param code:
		:param type:
		:param voucher_count:
		:return:
		"""
		try:
			count = self.getVoucherCount(enterprise_id=enterprise_id, status=Voucher.DRAFT)
			count = count-voucher_count
			message = None
			if count == 1:
				message = "1 voucher is pending for approval"
			elif count > 1:
				message = "%s Vouchers are pending for approval" % count

			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="ACCOUNTS", message=message,
				collapse_key="voucher_count", include_sender=True,code=code)
		except Exception as e:
			logger.exception("ICD Pending notification failed %s" % e.message)

	def getVoucherCount(self, enterprise_id=None, status=None):
		"""

		:param enterprise_id:
		:param status:
		:return:
		"""

		try:
			query = self.accounts_dao.db_session.query(Voucher).filter(Voucher.enterprise_id == enterprise_id)
			if status is not None:
				query = query.filter(Voucher.status == status)
			logger.debug("Query %s" % query)
			return query.count()
		except Exception as e:
			logger.exception(e)
			raise

	def getVoucherAbstract(self, enterprise_id=None, voucher_ids=()):
		"""

		:param enterprise_id:
		:param voucher_ids:
		:return:
		"""
		_voucher_abstracts = []
		try:
			vouchers = self.accounts_dao.db_session.query(Voucher).filter(
				Voucher.enterprise_id == enterprise_id, Voucher.id.in_(voucher_ids))
			for _voucher in vouchers:
				_voucher_abstracts.append({
					"voucher_id": _voucher.id, "code": _voucher.getCode(), "type_id": _voucher.type_id,
					"narration": _voucher.narration, "date": _voucher.voucher_date.strftime("%d %b, %Y")})
		except Exception as e:
			logger.exception(e)
			raise
		return _voucher_abstracts

	def getLedgerNames(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		ledgers = []
		try:
			ls = self.accounts_dao.db_session.query(
				Ledger.id.label('id'), Ledger.name.label('name'), AccountGroup.name.label('group_name')).outerjoin(
				AccountGroup, and_(AccountGroup.id == Ledger.group_id)
			).filter(Ledger.enterprise_id == enterprise_id).order_by(Ledger.name).group_by(Ledger.id).all()
			for l in ls:
				ledgers.append(l._asdict())
			logger.info("Fetched %s ledger names" % len(ledgers))
		except Exception:
			raise
		return ledgers

	def getLedger(self, enterprise_id=None, ledger_id=None, since=None, till=None):
		"""

		:param enterprise_id:
		:param ledger_id:
		:param since:
		:param till:
		:return:
		"""
		ledger = {}
		till = till if till else datetime.now()
		try:
			lq = self.accounts_dao.db_session.query(Ledger).filter(
				Ledger.enterprise_id == enterprise_id, Ledger.id == ledger_id)
			l = lq.first()
			if not l:
				ledger = response_code.databaseError()
				ledger['error'] = "No ledger found for ledger_id %s" % ledger_id
				return ledger
			ledger.update(self.getLedgerSnapShotAsMap(since=since, till=till, ledger=l))
			ledger['aging_allowed'] = True if l.group.billable == 1 else False
			logger.info("Since: %s" % (since - relativedelta(seconds=1)))
			ledger['opening_balance'] = float(l.getBalance(till=since - relativedelta(seconds=1), approved_only=True))
			ledger['closing_balance'] = ledger['opening_balance']

			ledger['vouchers'] = []

			for v in l.getVoucherEntries(since=since, till=till, approved_only=True):
				ledger['vouchers'].append({
					'code': v.voucher.getCode(), 'date': v.voucher.voucher_date.strftime('%Y-%m-%d'),
					'is_debit': bool(v.is_debit), 'value': float(v.amount), 'narration': v.voucher.narration})
				amount = float(v.amount) * -1 if v.is_debit else float(v.amount)
				ledger['closing_balance'] = ledger['closing_balance'] + amount
		except Exception:
			raise
		return ledger

	def getTaxLiability(self, enterprise_id=None, since=None, till=None):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:return:
		"""
		since = since if since else datetime(1900, 1, 1)
		till = till if till else datetime.now()
		tax_liability = []
		tax_liability_value_total = 0.0
		try:
			_group_ids = getAccountGroupIDs(
				enterprise_id=enterprise_id, names=(AccountGroup.DUTIES_AND_TAXES_GROUP_NAME,))
			tax_liabilities = self.accounts_dao.db_session.query(
				AccountGroup.name.label("group_name"), Ledger.name.label("name"), Ledger.id.label("id"), (
						func.sum(func.IF(and_(
							VoucherParticulars.is_debit == 0, Voucher.voucher_date.isnot(None)),
							VoucherParticulars.amount, 0)) -
						func.sum(func.IF(and_(
							VoucherParticulars.is_debit == 1, Voucher.voucher_date.isnot(None)),
							VoucherParticulars.amount, 0))).label("closing_balance")).outerjoin(
				Ledger, and_(
					AccountGroup.enterprise_id == Ledger.enterprise_id, AccountGroup.id == Ledger.group_id)).outerjoin(
				VoucherParticulars, VoucherParticulars.ledger_id == Ledger.id).outerjoin(
				Voucher, Voucher.id == VoucherParticulars.voucher_id).filter(
				AccountGroup.enterprise_id == enterprise_id,
				AccountGroup.id.in_(_group_ids),
				Voucher.status == Voucher.APPROVED,
				Voucher.voucher_date >= since, Voucher.voucher_date <= till,
				).group_by(Ledger.id).all()

			for row in tax_liabilities:
				temp = row._asdict()
				temp['closing_balance'] = float(temp['closing_balance'])

				if temp['closing_balance'] != 0.0:
					tax_liability.append(temp)

			for ledger_value in tax_liability:
				tax_liability_value_total += ledger_value['closing_balance']
			tax_liability = sorted(tax_liability, key=lambda a: a['closing_balance'], reverse=True)

		except Exception as e:
			logger.exception(e)
			raise
		return float(tax_liability_value_total), tax_liability

	def getMonthWiseGroupBalance(
			self, enterprise_id=None, since=None, till=datetime.today(), account_group="Direct Income"):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:param account_group:
		:return:
		"""
		month_wise_balance_data = {}
		try:
			get_account_groups_query = """SELECT id, name, parent_id FROM
											(SELECT * FROM account_groups ORDER BY parent_id , id) groups_sorted,
											(SELECT @pv:=(
												SELECT id FROM account_groups
													WHERE account_groups.name = '%s' AND enterprise_id = %s)
											) initialisation
										WHERE FIND_IN_SET(parent_id, @pv)
											AND LENGTH(@pv:=CONCAT(@pv, ',', id))
											AND enterprise_id = %s""" % (account_group, enterprise_id, enterprise_id)
			result = executeQuery(get_account_groups_query)
			groups = [row[0] for row in result]
			# FIXME query results random result; But the same query results static result in work bench

			query = self.accounts_dao.db_session.query(
				AccountGroup.name.label("group_name"), Ledger.name.label("name"), func.concat_ws(
					" '", func.substr(func.monthname(Voucher.voucher_date), 1, 3),
					func.substr(func.year(Voucher.voucher_date), 3, 4)).label("month"), Ledger.id.label("id"), (
						func.sum(func.IF(
							and_(VoucherParticulars.is_debit == 0, Voucher.voucher_date.isnot(None)),
							VoucherParticulars.amount, 0)) -
						func.sum(func.IF(
							and_(VoucherParticulars.is_debit == 1, Voucher.voucher_date.isnot(None)),
							VoucherParticulars.amount, 0))).label("closing_balance")
			).outerjoin(
				Ledger, and_(
					AccountGroup.enterprise_id == Ledger.enterprise_id, AccountGroup.id == Ledger.group_id)
			).outerjoin(
				VoucherParticulars, VoucherParticulars.ledger_id == Ledger.id
			).outerjoin(
				Voucher, and_(
					VoucherParticulars.voucher_id == Voucher.id, Voucher.voucher_date >= since,
					Voucher.voucher_date <= till)
			).filter(
				AccountGroup.enterprise_id == enterprise_id, Voucher.status == 1, AccountGroup.id.in_(groups)
			).group_by(
				func.year(Voucher.voucher_date), func.monthname(Voucher.voucher_date)
			).order_by(desc(Voucher.voucher_date))

			for row in query:
				temp = row._asdict()
				temp['closing_balance'] = float(temp['closing_balance'])
				month_wise_balance_data[row.month] = temp
		except Exception as e:
			logger.exception(e.message)
		return month_wise_balance_data

	def getSalesRevenue(self, enterprise_id=None, since=None, till=None):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:return:
		"""
		till = till if till else datetime.today()
		fy_start_day = self.accounts_dao.db_session.query(Enterprise.fy_start_day).filter(
			Enterprise.id == enterprise_id).first()
		since = helper.getFYStartDate(for_date=till, fy_start_day=fy_start_day.fy_start_day) if since is None else since
		_sale_group_ids = getAccountGroupIDs(enterprise_id=enterprise_id, names=[AccountGroup.SALES_GROUP_NAME])
		sales_revenue_total = 0

		sales_revenue = self.accounts_dao.db_session.query(AccountGroup.name.label("name"), (
				func.sum(func.IF(and_(
					VoucherParticulars.is_debit == 0, Voucher.voucher_date is not None),
					VoucherParticulars.amount, 0)) -
				func.sum(func.IF(and_(
					VoucherParticulars.is_debit == 1, Voucher.voucher_date is not None),
					VoucherParticulars.amount, 0))).label("closing_balance")).outerjoin(
			Ledger, and_(
				AccountGroup.enterprise_id == Ledger.enterprise_id, AccountGroup.id == Ledger.group_id)).outerjoin(
			VoucherParticulars, VoucherParticulars.ledger_id == Ledger.id).outerjoin(
			Voucher, VoucherParticulars.voucher_id == Voucher.id).filter(
			AccountGroup.enterprise_id == enterprise_id, AccountGroup.id.in_(_sale_group_ids),
			Voucher.voucher_date >= since, Voucher.voucher_date <= till, Voucher.status == Voucher.APPROVED,
			Voucher.type_id != 7).group_by(AccountGroup.id).all()
		for row in sales_revenue:
			sales_revenue_total += row.closing_balance
		return 0.0 if sales_revenue is None else sales_revenue_total

	def getPurchaseExpenseTotal(self, enterprise_id=None, since=None, till=None):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:return:
		"""
		till = till if till else datetime.today()
		fy_start_day = self.accounts_dao.db_session.query(Enterprise.fy_start_day).filter(
			Enterprise.id == enterprise_id).first()
		since = helper.getFYStartDate(for_date=till, fy_start_day=fy_start_day.fy_start_day) if since is None else since
		_purchase_group_ids = getAccountGroupIDs(enterprise_id=enterprise_id, names=[AccountGroup.PURCHASE_GROUP_NAME])
		purchase_expense_total = 0

		purchase_expense = self.accounts_dao.db_session.query(AccountGroup.name.label("name"), (
				func.sum(func.IF(and_(
					VoucherParticulars.is_debit == 1, Voucher.voucher_date is not None),
					VoucherParticulars.amount, 0)) -
				func.sum(func.IF(and_(
					VoucherParticulars.is_debit == 0, Voucher.voucher_date is not None),
					VoucherParticulars.amount, 0))).label("closing_balance")).outerjoin(
			Ledger, and_(
				AccountGroup.enterprise_id == Ledger.enterprise_id, AccountGroup.id == Ledger.group_id)).outerjoin(
			VoucherParticulars, VoucherParticulars.ledger_id == Ledger.id).outerjoin(
			Voucher, VoucherParticulars.voucher_id == Voucher.id).filter(
			AccountGroup.enterprise_id == enterprise_id, AccountGroup.id.in_(_purchase_group_ids),
			Voucher.voucher_date >= since, Voucher.voucher_date <= till, Voucher.status == Voucher.APPROVED).group_by(
			AccountGroup.id).all()
		for row in purchase_expense:
			purchase_expense_total += row.closing_balance
		return 0.0 if purchase_expense is None else purchase_expense_total

	def getSalesTopCustomers(self, enterprise_id=None, since=None, till=None):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:return:
		"""
		since = since if since else datetime(1900, 1, 1)
		till = till if till else datetime.today()
		sales = []
		try:
			voucher_types = self.accounts_dao.db_session.query(VoucherType).filter(
				VoucherType.name.in_([VoucherType.SALES_VOUCHER_TYPE, VoucherType.NOTES_VOUCHER_TYPE]))
			voucher_types = [voucher_type.id for voucher_type in voucher_types]
			_debtor_group_ids = getAccountGroupIDs(
				enterprise_id=enterprise_id, names=[AccountGroup.SUNDRY_DEBTOR_GROUP_NAME])
			rowset = self.accounts_dao.db_session.query(AccountGroup, Party.name.label("name"), (
					func.sum(func.IF(and_(
						VoucherParticulars.is_debit == 1, Voucher.voucher_date is not None),
						VoucherParticulars.amount, 0)) -
					func.sum(func.IF(and_(
						VoucherParticulars.is_debit == 0, Voucher.voucher_date is not None),
						VoucherParticulars.amount, 0))).label("closing_balance")).outerjoin(
				Ledger, and_(
					AccountGroup.enterprise_id == Ledger.enterprise_id, AccountGroup.id == Ledger.group_id)).outerjoin(
				PartyLedgerMap, and_(
					PartyLedgerMap.enterprise_id == Ledger.enterprise_id, PartyLedgerMap.ledger_id == Ledger.id)
			).outerjoin(
				Party, and_(
					Party.enterprise_id == PartyLedgerMap.enterprise_id, Party.id == PartyLedgerMap.party_id)
			).outerjoin(
				VoucherParticulars, VoucherParticulars.ledger_id == Ledger.id).outerjoin(
				Voucher, and_(
					VoucherParticulars.voucher_id == Voucher.id,
					Voucher.type_id.in_(voucher_types))).filter(
				AccountGroup.enterprise_id == enterprise_id,
				Voucher.voucher_date >= since, Voucher.voucher_date <= till, Voucher.status == Voucher.APPROVED,
				AccountGroup.id.in_(_debtor_group_ids)).group_by(Ledger.id).all()
			if rowset is not None:
				for row in rowset:
					sales.append(row._asdict())
				sales = sorted(sales, key=lambda a: a['closing_balance'], reverse=True)
		except Exception as e:
			logger.exception('%s', e.message)

		return [] if sales is None else sales

	def getReceivablesOrPayables(
			self, enterprise_id=None, account_group_names=AccountGroup.SUNDRY_DEBTOR_GROUP_NAME, debit_positive=False):
		"""

		:param enterprise_id:
		:param account_group_names:
		:param debit_positive:
		:return:
		"""
		net_value_in_positive = -1 if debit_positive else 1
		_group_ids = getAccountGroupIDs(enterprise_id=enterprise_id, names=account_group_names)
		rowset = self.accounts_dao.db_session.query(
			AccountGroup.name.label("group_name"), Ledger.name.label("name"), Ledger.id.label("id"),
			(net_value_in_positive * (
					func.sum(func.IF(and_(
						VoucherParticulars.is_debit == 0, Voucher.voucher_date.isnot(None)),
						VoucherParticulars.amount, 0)) -
					func.sum(func.IF(and_(
						VoucherParticulars.is_debit == 1, Voucher.voucher_date.isnot(None)),
						VoucherParticulars.amount, 0)))).label("closing_balance")).outerjoin(
			Ledger, and_(
				AccountGroup.enterprise_id == Ledger.enterprise_id, AccountGroup.id == Ledger.group_id, Ledger.billable != 0)).outerjoin(
			VoucherParticulars, VoucherParticulars.ledger_id == Ledger.id).outerjoin(
			Voucher, Voucher.id == VoucherParticulars.voucher_id).filter(
			AccountGroup.enterprise_id == enterprise_id, AccountGroup.id.in_(_group_ids),
			Voucher.status == Voucher.APPROVED).group_by(Ledger.id)
		logger.debug("Receivables Query: %s" % rowset)
		receivables = []
		for row in rowset.all():
			receivable = row._asdict()
			receivable['closing_balance'] = float(receivable['closing_balance'])
			receivables.append(receivable)

		ledger_value_total = 0.0

		for ledger_value in receivables:
			ledger_value_total += ledger_value['closing_balance']
		receivables = sorted(receivables, key=lambda a: a['closing_balance'], reverse=True)

		return float(ledger_value_total), receivables

	def getDashboardEntries(self, enterprise_id=None, since=None, till=None):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:return:
		"""
		till = till if till else datetime.today()
		dashboard_data = {}
		try:
			# bank balance
			dashboard_data['bank'], dashboard_data['bank_balance'] = self.cashBalance(
				enterprise_id=enterprise_id,type="bank_balance")
			# Cash in hand
			dashboard_data['cash'], dashboard_data['cash_in_hand'] = self.cashBalance(
				enterprise_id=enterprise_id,type="cash_in_hand")
			# Sundry Debtors - Receivables
			dashboard_data['receivable'], dashboard_data['receivables'] = self.getReceivablesOrPayables(
				enterprise_id=enterprise_id,
				account_group_names=[AccountGroup.SUNDRY_DEBTOR_GROUP_NAME, AccountGroup.OTHER_CURRENT_ASSETS_GROUP_NAME],
				debit_positive=True)

			dashboard_data['receivables'] = dashboard_data['receivables'][0:10]

			# Sundry Debtors - Sales Revenue
			opening_date = getFYStartDate()
			dashboard_data['sales_revenue'] = float(
				self.getSalesRevenue(enterprise_id, since=since, till=till))

			# Sundry Creditors - Payable
			dashboard_data['payable'], dashboard_data['payables'] = self.getReceivablesOrPayables(
				enterprise_id=enterprise_id,
				account_group_names=[AccountGroup.SUNDRY_CREDITOR_GROUP_NAME, AccountGroup.OTHER_CURRENT_LIABILITIES_GROUP_NAME],
				debit_positive=False)

			dashboard_data['payables'] = dashboard_data['payables'][0:10]

			# Duties and Taxes - Tax liable
			dashboard_data['tax_liability'], dashboard_data['tax_liable_items'] = self.getTaxLiability(
				enterprise_id=enterprise_id, since=since, till=till)

		except Exception as e:
			logger.exception('%s' % e)
			traceback.print_exc(file=sys.stdout)
			raise
		return dashboard_data

	def __getAgeClassQuery(self, is_debit, filters, age_class):
		"""

		:param is_debit:
		:param filters:
		:param age_class:
		:return:
		"""
		filters.append(age_class >= -10)
		return self.accounts_dao.db_session.query(
			AccountGroup, func.sum(LedgerBill.net_value).label("net_value"), (case([
				(and_(age_class >= -10, age_class <= -1), "to_be_due"),
				(age_class >= 0, "due")], else_="null")).label("age")
		).join(
			Ledger, and_(AccountGroup.enterprise_id == Ledger.enterprise_id, AccountGroup.id == Ledger.group_id)
		).join(LedgerBill, and_(LedgerBill.ledger_id == Ledger.id, LedgerBill.is_debit == is_debit)).join(
			Voucher, Voucher.id == LedgerBill.voucher_id).outerjoin(
			PartyLedgerMap,
			and_(PartyLedgerMap.ledger_id == Ledger.id, PartyLedgerMap.enterprise_id == Ledger.enterprise_id)
		).outerjoin(Party, Party.id == PartyLedgerMap.party_id).filter(*filters).group_by("age")

	def getAgeWiseDue(
			self, enterprise_id, account_group_names, ledger_id=None, list_unsettled_particulars=False,
			is_receivable=None):
		"""

		:param enterprise_id:
		:param account_group_names:
		:param ledger_id:
		:param list_unsettled_particulars:
		:param is_receivable:
		:return:
		"""
		age1, age2, age3, age4, excess = 0.0, 0.0, 0.0, 0.0, 0.0
		total_due, overdue, advance, billable, due_shortly = 0.0, 0.0, 0.0, 0.0, 0.0
		advance_particulars, billable_particulars, _unsettled_particulars = {}, {}, {}
		today = date.today()
		try:
			if is_receivable is not None:
				_group_ids = getAccountGroupIDs(enterprise_id=enterprise_id, names=account_group_names)
				filters = [
					Voucher.status == Voucher.APPROVED, AccountGroup.enterprise_id == enterprise_id,
					Ledger.enterprise_id == enterprise_id, AccountGroup.id.in_(_group_ids), Ledger.billable != 0]
				if ledger_id:
					filters.append(Ledger.id == ledger_id)
				age_in_days = func.datediff(today, LedgerBill.bill_date)
				__excess_condition = LedgerBill.is_debit != is_receivable
				__age1_condition = age_in_days <= 30
				__age2_condition = and_(age_in_days >= 31, age_in_days <= 60)
				__age3_condition = and_(age_in_days >= 61, age_in_days <= 90)
				__age4_condition = age_in_days >= 91
				total_dues_by_age = self.accounts_dao.db_session.query(
					AccountGroup, (func.sum(LedgerBill.net_value)).label("net_value"), (case([
						(__excess_condition, "excess"), (__age1_condition, "<30"), (__age2_condition, "30-60"),
						(__age3_condition, "60-90"), (__age4_condition, ">90")], else_='null')).label("age")
				).join(
					Ledger, and_(AccountGroup.enterprise_id == Ledger.enterprise_id, AccountGroup.id == Ledger.group_id)
				).join(LedgerBill, LedgerBill.ledger_id == Ledger.id).join(
					Voucher, Voucher.id == LedgerBill.voucher_id
				).filter(*filters).group_by(LedgerBill.is_debit, 'age')
				logger.debug(total_dues_by_age)
				for due_by_age in total_dues_by_age:
					if due_by_age.age == '<30':
						age1 = float(due_by_age.net_value)
					elif due_by_age.age == '30-60':
						age2 = float(due_by_age.net_value)
					elif due_by_age.age == '60-90':
						age3 = float(due_by_age.net_value)
					elif due_by_age.age == '>90':
						age4 = float(due_by_age.net_value)
					elif due_by_age.age == 'excess':
						excess -= float(due_by_age.net_value)
				_unsettled_particulars = self.getUnsettledParticulars(
					enterprise_id=enterprise_id, ledger_id=ledger_id, is_receivable=is_receivable == 1,
					list_particulars=list_unsettled_particulars)
				if not ledger_id:
					ledger_id = "ALL"
					advance_particulars = json.dumps(_unsettled_particulars['advance'])
					billable_particulars = json.dumps(_unsettled_particulars['billable'])
				else:
					if ledger_id in _unsettled_particulars['advance']:
						advance_particulars = json.dumps(_unsettled_particulars['advance'][ledger_id]['particulars'])
					if ledger_id in _unsettled_particulars['billable']:
						billable_particulars = json.dumps(_unsettled_particulars['billable'][ledger_id]['particulars'])
				advance -= _unsettled_particulars['advance'][ledger_id][
					'total'] if ledger_id in _unsettled_particulars['advance'] else 0
				billable += _unsettled_particulars['billable'][ledger_id][
					'total'] if ledger_id in _unsettled_particulars['billable'] else 0

				logger.debug("Probables: %s" % billable)
				total_due = float(Decimal(advance).quantize(Decimal('.01')) + \
				                  Decimal(excess).quantize(Decimal('.01')) + \
				                  Decimal(age1).quantize(Decimal('.01')) + \
				                  Decimal(age2).quantize(Decimal('.01')) + \
				                  Decimal(age3).quantize(Decimal('.01')) + \
				                  Decimal(age4).quantize(Decimal('.01')) + \
				                  Decimal(billable).quantize(Decimal('.01')))

				if is_receivable == 1:
					age_class = func.datediff(
						today, func.adddate(LedgerBill.bill_date, func.ifnull(Party.receipt_credit_days, 0)))
					over_due_by_age = self.__getAgeClassQuery(
						is_debit=is_receivable == 1, filters=filters, age_class=age_class)
				else:
					age_class = func.datediff(
						today, func.adddate(LedgerBill.bill_date, func.ifnull(Party.payment_credit_days, 0)))
					over_due_by_age = self.__getAgeClassQuery(
						is_debit=is_receivable == 1, filters=filters, age_class=age_class)
				for due in over_due_by_age:
					if due.age == 'to_be_due':
						due_shortly = float(due.net_value)
					elif due.age == 'due':
						overdue = float(due.net_value)
			return dict(
				advance=advance, excess=excess, billable=billable, age1=age1, age2=age2, age3=age3, age4=age4,
				age5=due_shortly, overdue=overdue, total=total_due, advance_particulars=advance_particulars,
				billable_particulars=billable_particulars)
		except Exception as e:
			logger.exception('Ageing %s' % e.message)
			raise

	def getAging(self, enterprise_id=None, list_unsettled_particulars=False):
		"""

		:param enterprise_id:
		:param list_unsettled_particulars:
		:return:
		"""
		aging = {}
		try:
			# Sundry Debtors - Receivables
			aging['receivable_aging'] = self.getAgeWiseDue(
				enterprise_id=enterprise_id,
				account_group_names=[AccountGroup.SUNDRY_DEBTOR_GROUP_NAME, AccountGroup.OTHER_CURRENT_ASSETS_GROUP_NAME],
				list_unsettled_particulars=list_unsettled_particulars, is_receivable=1)

			# Sundry Creditors - Payable
			aging['payable_aging'] = self.getAgeWiseDue(
				enterprise_id=enterprise_id,
				account_group_names=[AccountGroup.SUNDRY_CREDITOR_GROUP_NAME, AccountGroup.OTHER_CURRENT_LIABILITIES_GROUP_NAME],
				list_unsettled_particulars=list_unsettled_particulars, is_receivable=0)
		except Exception:
			raise
		return aging

	def getReceivableAging(self, enterprise_id=None, list_unsettled_particulars=False):
		"""

		:param enterprise_id:
		:param list_unsettled_particulars:
		:return:
		"""
		aging = {}
		try:
			# Sundry Debtors - Receivables
			aging['receivable_aging'] = self.getAgeWiseDue(
				enterprise_id=enterprise_id,
				account_group_names=[AccountGroup.SUNDRY_DEBTOR_GROUP_NAME, AccountGroup.OTHER_CURRENT_ASSETS_GROUP_NAME],
				list_unsettled_particulars=list_unsettled_particulars, is_receivable=1)
		except Exception:
			raise
		return aging

	def getPayableAging(self, enterprise_id=None, list_unsettled_particulars=False):
		"""

		:param enterprise_id:
		:param list_unsettled_particulars:
		:return:
		"""
		aging = {}
		try:
			# Sundry Creditors - Payable
			aging['payable_aging'] = self.getAgeWiseDue(
				enterprise_id=enterprise_id,
				account_group_names=[AccountGroup.SUNDRY_CREDITOR_GROUP_NAME, AccountGroup.OTHER_CURRENT_LIABILITIES_GROUP_NAME],
				list_unsettled_particulars=list_unsettled_particulars, is_receivable=0)
		except Exception:
			raise
		return aging

	def getLedgerAging(self, enterprise_id=None, ledger_id=None):
		"""

		:param enterprise_id:
		:param ledger_id:
		:return:
		"""
		try:
			ledger = self.accounts_dao.db_session.query(Ledger).filter(
				Ledger.enterprise_id == enterprise_id, Ledger.id == ledger_id).first()
			aging = self.getLedgerSnapShotAsMap(ledger=ledger)

			parent_group = ledger.group.getRootParent(end_parent=(
				AccountGroup.SUNDRY_CREDITOR_GROUP_NAME, AccountGroup.SUNDRY_DEBTOR_GROUP_NAME,
				AccountGroup.OTHER_CURRENT_ASSETS_GROUP_NAME, AccountGroup.OTHER_CURRENT_LIABILITIES_GROUP_NAME))
			is_receivable = None
			if parent_group.name == AccountGroup.SUNDRY_DEBTOR_GROUP_NAME or parent_group.name == AccountGroup.OTHER_CURRENT_ASSETS_GROUP_NAME:
				is_receivable = 1
			elif parent_group.name == AccountGroup.SUNDRY_CREDITOR_GROUP_NAME or parent_group.name == AccountGroup.OTHER_CURRENT_LIABILITIES_GROUP_NAME:
				is_receivable = 0
			aging['aging'] = self.getAgeWiseDue(enterprise_id, [ledger.group.name], ledger.id,
			                                    is_receivable=is_receivable)
			party_ledger_map = self.accounts_dao.db_session.query(PartyLedgerMap).filter(
				PartyLedgerMap.enterprise_id == enterprise_id,
				PartyLedgerMap.ledger_id == ledger_id).first()
			if not party_ledger_map or not party_ledger_map.party:
				aging['credit_period'] = int(0)
			else:
				party = party_ledger_map.party
				if ledger.group.id in getAccountGroupIDs(
						enterprise_id=enterprise_id,
						names=[AccountGroup.SUNDRY_CREDITOR_GROUP_NAME, AccountGroup.OTHER_CURRENT_ASSETS_GROUP_NAME]):
					aging['credit_period'] = int(party.payment_credit_days)
				elif ledger.group.id in getAccountGroupIDs(
						enterprise_id=enterprise_id,
						names=[AccountGroup.SUNDRY_DEBTOR_GROUP_NAME, AccountGroup.OTHER_CURRENT_LIABILITIES_GROUP_NAME]):
					aging['credit_period'] = int(party.receipt_credit_days)
			return aging
		except Exception:
			raise

	def getAgeingLedgerBills(
			self, enterprise_id=None, ledger_id=None, start_days=None, end_days=None, is_receivable=None,
			is_advance=False):
		"""

		:param enterprise_id:
		:param ledger_id:
		:param start_days:
		:param end_days:
		:param is_receivable:
		:param is_advance:
		:return:
		"""
		today = date.today()

		query = self.accounts_dao.db_session.query(LedgerBill).join(LedgerBill.ledger).join(LedgerBill.voucher)
		if ledger_id:
			is_receivable = self.accounts_dao.isReceivableLedger(enterprise_id=enterprise_id, ledger_id=ledger_id)
			filters = [LedgerBill.ledger_id == ledger_id]
		else:
			group = [AccountGroup.SUNDRY_DEBTOR_GROUP_NAME, AccountGroup.OTHER_CURRENT_ASSETS_GROUP_NAME] \
				if is_receivable else [AccountGroup.SUNDRY_CREDITOR_GROUP_NAME, AccountGroup.OTHER_CURRENT_LIABILITIES_GROUP_NAME]
			_group_ids = getAccountGroupIDs(enterprise_id=enterprise_id, names=group)
			filters = [Ledger.group_id.in_(_group_ids)]
		filters.extend([
			LedgerBill.enterprise_id == enterprise_id, LedgerBill.net_value != Decimal(0.0),
			LedgerBill.is_debit == (is_receivable != is_advance), Voucher.status == Voucher.APPROVED, Ledger.billable != 0])
		query = query.filter(*filters)
		if start_days and end_days:
			# 30 - 60 days: today-30 > bill_date >= today-60
			query = query.filter(
				LedgerBill.bill_date < (today + relativedelta(days=-start_days)),
				LedgerBill.bill_date >= (today + relativedelta(days=-end_days)))
		elif end_days:
			# <30 days: bill_date >= today-30
			query = query.filter(LedgerBill.bill_date >= (today + relativedelta(days=-end_days)))
		elif start_days:
			# >90 days: bill_date < today-90
			query = query.filter(LedgerBill.bill_date < (today + relativedelta(days=-start_days)))
		return query.all()

	def getAgingLedgersBase(
			self, enterprise_id=None, start_days=None, end_days=None, is_receivable=True, is_advance=False,
			with_bills=False, ledger_id=None):
		"""

		:param enterprise_id:
		:param start_days:
		:param end_days:
		:param is_receivable:
		:param is_advance:
		:param with_bills:
		:param ledger_id:
		:return:
		"""
		try:
			ledger_bills = self.getAgeingLedgerBills(
				enterprise_id=enterprise_id, start_days=start_days, end_days=end_days, is_receivable=is_receivable,
				is_advance=is_advance, ledger_id=ledger_id)
			result = {}
			logger.info("Number of ledger bills are %s" % len(ledger_bills))
			for bill in ledger_bills:
				ledger = bill.ledger
				if ledger.id in result:
					ledger_record = result[ledger.id]
				else:
					ledger_record = {
						'id': ledger.id, 'name': ledger.name, 'group_name': ledger.group.name, 'due': float(0),
						'bills': []}
				due = ledger_record['due'] + float(bill.net_value * (-1 if bill.is_debit else 1))
				ledger_record['due'] = due
				result[ledger.id] = ledger_record
			result_list = []
			for key in result:
				result_list.append(result[key])
			result_list = sorted(result_list, key=lambda a: a['name'])
			return result_list
		except Exception as e:
			logger.exception("Fetching Ledger Ageing Failed: %s" % e.message)
			raise

	def getAgingLedgers(
			self, enterprise_id=None, start_days=None, end_days=None, is_receivable=True, is_advance=False,
			with_bills=False, ledger_id=None):
		"""

		:param enterprise_id:
		:param start_days:
		:param end_days:
		:param is_receivable:
		:param is_advance:
		:param with_bills:
		:param ledger_id:
		:return:
		"""
		try:
			ledger_bills = self.getAgeingLedgerBills(
				enterprise_id=enterprise_id, start_days=start_days, end_days=end_days, is_receivable=is_receivable,
				is_advance=is_advance, ledger_id=ledger_id)
			result = {}
			logger.info("Number of ledger bills are %s" % len(ledger_bills))
			for bill in ledger_bills:
				ledger = bill.ledger
				if ledger.id in result:
					ledger_record = result[ledger.id]
				else:
					ledger_record = {
						'id': ledger.id, 'name': ledger.name, 'group_name': ledger.group.name, 'due': float(0),
						'bills': []}
					result[ledger.id] = ledger_record
				if with_bills:
					credit_days = int(0)
					party_ledger_map = self.accounts_dao.db_session.query(PartyLedgerMap).filter(
						PartyLedgerMap.enterprise_id == enterprise_id,
						PartyLedgerMap.ledger_id == ledger.id).first()
					if party_ledger_map and party_ledger_map.party:
						party = party_ledger_map.party
						if is_receivable is True:
							credit_days = int(party.receipt_credit_days)
						else:
							credit_days = int(party.payment_credit_days)

					ledger_record['bills'].append({
						'ledger_id': ledger.id, 'party_name': ledger.name, 'bill_no': bill.bill_no,
						'credit_period': credit_days, 'bill_date': "%s" % bill.bill_date,
						'due': float(bill.net_value * (-1 if bill.is_debit else 1)),
						'age_days': (
								date.today() - bill.bill_date).days if bill.is_debit == is_receivable else int(0)})
				ledger_record['due'] += float(bill.net_value * (-1 if bill.is_debit else 1))
			result_list = []
			for key in result:
				result_list.append(result[key])
			result_list = sorted(result_list, key=lambda a: a['name'])
			return result_list
		except Exception as e:
			logger.exception("Fetching Ledger Ageing Failed: %s" % e.message)
			raise

	def getLedgerBills(self, enterprise_id=None, ledger_id=None):
		"""

		:param enterprise_id:
		:param ledger_id:
		:return:
		"""
		try:
			party = self.accounts_dao.db_session.query(
				PartyLedgerMap, AccountGroup.name.label('group_name'), AccountGroup.name.label('group_id'),
				Party.receipt_credit_days, Party.payment_credit_days
			).join(PartyLedgerMap.ledger).join(PartyLedgerMap.party).join(Ledger.group).filter(
				PartyLedgerMap.enterprise_id == enterprise_id, PartyLedgerMap.ledger_id == ledger_id).first()
			ledger_bills = self.getAgeingLedgerBills(enterprise_id=enterprise_id, ledger_id=ledger_id)
			if party:
				if party.group_id in getAccountGroupIDs(
						enterprise_id=enterprise_id,
						names=(AccountGroup.SUNDRY_DEBTOR_GROUP_NAME, AccountGroup.OTHER_CURRENT_ASSETS_GROUP_NAME)):
					credit_period = int(party.receipt_credit_days)
					is_receivable = True
				else:
					credit_period = int(party.payment_credit_days)
					is_receivable = False
			else:
				credit_period = 0
				is_receivable = None if len(
					ledger_bills) == 0 else (ledger_bills[0].ledger.group.name == AccountGroup.SUNDRY_DEBTOR_GROUP_NAME)
			today = date.today()

			result = {}

			for bill in ledger_bills:
				due = float(bill.net_value * (-1 if bill.is_debit else 1))
				settlement = bill.original_settlement
				bill_value = 0
				if settlement:
					bill_value = float(settlement.cr_value - settlement.dr_value)  # credit-positive value
				result[bill.id] = dict(
					bill_id=bill.id, bill_no=bill.bill_no, bill_date="%s" % bill.bill_date, due=due,
					bill_value=bill_value, already_adjusted=bill_value - due)

				result[bill.id]['age_days'] = (today - bill.bill_date).days
				result[bill.id]['credit_period'] = credit_period
			result_list = []
			for key in result:
				result_list.append(result[key])
			result_list = sorted(result_list, key=lambda a: a['bill_date'])
			logger.info("Number of ledger bills are %s" % len(result_list))
			return result_list
		except Exception:
			raise

	def getUnsettledParticulars(self, enterprise_id=None, ledger_id=None, is_receivable=False, list_particulars=True):
		"""

		:param enterprise_id:
		:param ledger_id:
		:param is_receivable:
		:param list_particulars:
		:return:
		"""
		_unsettled = dict(advance=dict(), billable=dict())
		_group_name = [AccountGroup.SUNDRY_DEBTOR_GROUP_NAME, AccountGroup.OTHER_CURRENT_ASSETS_GROUP_NAME] \
			if is_receivable else [AccountGroup.SUNDRY_CREDITOR_GROUP_NAME, AccountGroup.OTHER_CURRENT_LIABILITIES_GROUP_NAME]
		_group_ids = "'-1'"
		for _id in getAccountGroupIDs(enterprise_id=enterprise_id, names=_group_name):
			_group_ids = "%s, '%s'" % (_group_ids, _id)
		_condition = "al.enterprise_id='%s'" % enterprise_id

		if ledger_id:
			_condition = "%s %s" % (_condition, "AND al.id='%s'" % ledger_id)
		unsettled_query = """
			SELECT
				vp.voucher_id AS voucher_id, al.id AS ledger_id, al.enterprise_id AS enterprise_id,
				(vp.dr_value - IFNULL(lbs.dr_value, 0)) AS unsettled_dr,
				(vp.cr_value - IFNULL(lbs.cr_value, 0)) AS unsettled_cr, al.name AS ledger_name,
				CONCAT(v.financial_year, '/', vt.code, '/', LPAD(v.voucher_no, 6, '0'), IFNULL(v.sub_number, '')) AS voucher_code,
				v.narration AS narration, DATE_FORMAT(v.voucher_date, '%%d %%b, %%Y') AS voucher_date, v.type AS type_id
			FROM
				account_ledgers al
				JOIN account_groups ag ON al.group_id = ag.id
					AND al.enterprise_id = ag.enterprise_id
					AND ag.id IN (%s)
				JOIN (
					SELECT
						voucher_id, ledger_id, SUM(IF(is_debit = 1, amount, 0)) AS dr_value,
						SUM(IF(is_debit = 0, amount, 0)) AS cr_value
					FROM voucher_particulars
					GROUP BY voucher_id , ledger_id) AS vp ON al.id = vp.ledger_id
				JOIN voucher v ON v.id=vp.voucher_id
				JOIN voucher_type vt ON v.type=vt.id
				LEFT JOIN (
					SELECT
						lb.ledger_id, ls.voucher_id, SUM(cr_value) AS cr_value, SUM(dr_value) AS dr_value
					FROM
						ledger_bill_settlements ls
						JOIN ledger_bills lb ON lb.id = ls.bill_id
					GROUP BY lb.ledger_id , ls.voucher_id) AS lbs ON lbs.ledger_id = al.id
						AND lbs.voucher_id = vp.voucher_id
			WHERE %s AND v.status=1 AND al.billable != 0
			GROUP BY vp.voucher_id , vp.ledger_id
			HAVING unsettled_dr <> 0 OR unsettled_cr <> 0""" % (_group_ids, _condition)
		_unsettled_particulars = executeQuery(query=unsettled_query, as_dict=True)
		_total_advance, _total_billable = 0.0, 0.0
		for _particular in _unsettled_particulars:
			if is_receivable:
				_advance_amount = float(_particular["unsettled_cr"])
				_billable_amount = float(_particular["unsettled_dr"])
			else:
				_advance_amount = float(_particular["unsettled_dr"])
				_billable_amount = float(_particular["unsettled_cr"])
			_total_billable += float(_billable_amount)
			_total_advance += float(_advance_amount)
			if _particular["ledger_id"] in _unsettled["advance"]:
				_unsettled["advance"][_particular["ledger_id"]]["total"] += float(_advance_amount)
			else:
				_unsettled["advance"][_particular["ledger_id"]] = dict(
					particulars=[], total=float(_advance_amount), name=_particular["ledger_name"])
			if _particular["ledger_id"] in _unsettled["billable"]:
				_unsettled["billable"][_particular["ledger_id"]]["total"] += float(_billable_amount)
			else:
				_unsettled["billable"][_particular["ledger_id"]] = dict(
					particulars=[], total=float(_billable_amount), name=_particular["ledger_name"])
			if list_particulars:
				if _advance_amount > 0:
					_unsettled["advance"][_particular["ledger_id"]]["particulars"].append(dict(
						voucher_id=_particular["voucher_id"], voucher_code=_particular["voucher_code"],
						narration=_particular["narration"], voucher_date=_particular["voucher_date"],
						type_id=_particular["type_id"], amount=_advance_amount))
				if _billable_amount > 0:
					_unsettled["billable"][_particular["ledger_id"]]["particulars"].append(dict(
						voucher_id=_particular["voucher_id"], voucher_code=_particular["voucher_code"],
						narration=_particular["narration"], voucher_date=_particular["voucher_date"],
						type_id=_particular["type_id"], amount=_billable_amount))
		if not ledger_id:
			_unsettled["advance"]["ALL"] = dict(total=_total_advance, particulars=[], name="TOTAL")
			_unsettled["billable"]["ALL"] = dict(total=_total_billable, particulars=[], name="TOTAL")
		logger.debug("Unsettled: %s " % _unsettled)
		return _unsettled

	def createPaymentSettlement(self, enterprise_id=None, is_receivable=True, advance_bills=()):
		"""

		:param enterprise_id:
		:param is_receivable:
		:param advance_bills:
		:return:
		"""
		try:
			logger.info("Payment: Settling %s bills against Advance" % len(advance_bills))
			self.accounts_dao.db_session.begin(subtransactions=True)
			for bill in advance_bills:
				amount = float(bill['amount'])
				settlement = self.accounts_dao.db_session.query(LedgerBillSettlement).filter(
					LedgerBillSettlement.bill_id == bill['bill_id'],
					LedgerBillSettlement.voucher_id == bill['voucher_id']).first()
				if settlement:
					if is_receivable:
						settlement.cr_value = float(settlement.cr_value) + amount
					else:
						settlement.dr_value = float(settlement.dr_value) + amount
				else:
					settlement = LedgerBillSettlement(bill_id=bill['bill_id'], voucher_id=bill['voucher_id'])
					if is_receivable:
						settlement.cr_value = amount
					else:
						settlement.dr_value = amount
				settlement.enterprise_id = enterprise_id
				self.accounts_dao.db_session.add(settlement)
				ledger_bill = self.accounts_dao.getLedgerBill(enterprise_id=enterprise_id, bill_id=bill['bill_id'])
				ledger_bill.net_value = float(ledger_bill.net_value) - float(amount)
				self.accounts_dao.db_session.add(ledger_bill)
			self.accounts_dao.db_session.commit()
		except Exception:
			self.accounts_dao.db_session.rollback()
			raise

	def createPaymentVoucher(
			self, enterprise_id=None, user_id=None, payment_dict=None, paid_bills=(), is_receivable=False):
		"""

		:param enterprise_id:
		:param user_id:
		:param payment_dict:
		:param paid_bills:
		:param is_receivable:
		:return:
		"""
		amount = payment_dict['amount']
		if amount <= 0:
			return None
		try:
			logger.info("Payment: Creating voucher and settling %s bills" % len(paid_bills))
			self.accounts_dao.db_session.begin(subtransactions=True)
			narration = payment_dict['narration']
			payment_date = datetime.strptime(payment_dict['payment_date'], "%Y-%m-%d")

			voucher = Voucher(
				enterprise_id=enterprise_id, created_by=user_id, approved_by=user_id, narration=narration,
				status=Voucher.APPROVED, created_on=datetime.now(), approved_on=datetime.now(),
				transaction_instrument_no=payment_dict['instrument_no'], voucher_date=payment_date)
			fund_ledger_group = self.accounts_dao.db_session.query(AccountGroup.name).join(Ledger, and_(
				Ledger.group_id == AccountGroup.id, Ledger.enterprise_id == AccountGroup.enterprise_id)
			                                                                               ).filter(Ledger.id == payment_dict['fund_ledger_id'], Ledger.enterprise_id == enterprise_id).first()
			if fund_ledger_group.name == AccountGroup.BANK_GROUP_NAME:
				voucher.type_id = 3
			elif fund_ledger_group.name == AccountGroup.CASH_GROUP_NAME:
				voucher.type_id = 2

			latest_vou_no = self.accounts_dao.db_session.query(Voucher.voucher_no).filter(
				Voucher.financial_year == voucher.financial_year, Voucher.type_id == voucher.type_id,
				Voucher.enterprise_id == enterprise_id, Voucher.voucher_no != '0').order_by(
				Voucher.voucher_no.desc()).first()
			logger.debug('Recent Vou No: %s' % latest_vou_no)
			voucher.voucher_no = '%s' % (
				1 if not ('%s' % latest_vou_no).isdigit() or latest_vou_no == '0' else int(
					'%s' % latest_vou_no) + 1)

			voucher.tags = getEntityTagMaps(
				enterprise_id=enterprise_id, tags=payment_dict['tags'] if 'tags' in payment_dict else [],
				tag_map_class=VoucherTag, db_session=self.accounts_dao.db_session)
			voucher.particulars = [
				VoucherParticulars(
					enterprise_id=enterprise_id, voucher_id=voucher.id, item_no=1, is_debit=not is_receivable,
					amount=amount, ledger_id=payment_dict['payment_ledger_id']),
				VoucherParticulars(
					enterprise_id=enterprise_id, voucher_id=voucher.id, item_no=2, is_debit=is_receivable,
					amount=amount, ledger_id=payment_dict['fund_ledger_id'])]
			self.accounts_dao.db_session.add(voucher)

			for bill in paid_bills:
				settlement = LedgerBillSettlement(bill_id=bill['bill_id'])
				settlement.voucher = voucher
				if is_receivable:
					settlement.cr_value = bill['amount']
				else:
					settlement.dr_value = bill['amount']
				settlement.enterprise_id = enterprise_id
				self.accounts_dao.db_session.add(settlement)
				ledger_bill = self.accounts_dao.getLedgerBill(enterprise_id=enterprise_id, bill_id=bill['bill_id'])
				ledger_bill.net_value = float(ledger_bill.net_value) - float(bill['amount'])
				self.accounts_dao.db_session.add(ledger_bill)
			self.accounts_dao.db_session.commit()
			VoucherChangelog().queryVoucherInsert(user_id=user_id, enterprise_id=enterprise_id, data=voucher)
			self.accounts_dao.db_session.refresh(voucher)
			self.notifyPendingVoucherCount(enterprise_id=enterprise_id, sender_id=user_id,code=voucher.getCode(),type=voucher.type)
			return voucher
		except Exception:
			self.accounts_dao.db_session.rollback()
			raise

	def constructIncomeVsExpensesData(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		income_expense = []
		try:
			till = datetime.today()
			since = till - relativedelta(months=12)
			since = datetime(since.year, since.month, 1)
			income = self.getMonthWiseGroupBalance(
				enterprise_id=enterprise_id, since=since, till=till, account_group="Direct Income")
			expense = self.getMonthWiseGroupBalance(
				enterprise_id=enterprise_id, since=since, till=till, account_group="Direct Expenses")

			months = [(till - relativedelta(months=i)).strftime("%b '%y") for i in sorted(range(12), reverse=True)]
			for month_year in months:
				monthly_income_expense = dict(month=month_year, income=0, expense=0)
				if month_year in income:
					income_value = income[month_year]
					if income_value['closing_balance'] > 0:
						monthly_income_expense['income'] += income_value['closing_balance']
					else:
						monthly_income_expense['expense'] += income_value['closing_balance']

				if month_year in expense:
					expense_value = expense[month_year]
					if expense_value['closing_balance'] < 0:
						monthly_income_expense['expense'] += expense_value['closing_balance']
					else:
						monthly_income_expense['income'] += expense_value['closing_balance']
				# invert expense into positive BUG:8941
				monthly_income_expense['expense'] = monthly_income_expense['expense'] * (-1)
				income_expense.append(monthly_income_expense)
		except Exception as e:
			logger.exception(e.message)
		return income_expense

	def cashBalance(self, enterprise_id=None, since=None, till=None, type=None):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:param type:
		:return:
		"""
		since = since if since else date(1, 1, 1)
		till = till if till else date.today()
		fund = Decimal('0.00').quantize(Decimal('.01'))
		ledgers_list = []

		try:
			bank_groups = getAccountGroupIDs(enterprise_id=enterprise_id, names=[
				AccountGroup.CASH_GROUP_NAME] if type == 'cash_in_hand' else [
				AccountGroup.BANK_GROUP_NAME, AccountGroup.ST_BORROWINGS_GROUP_NAME, AccountGroup.LT_BORROWINGS_GROUP_NAME
			] if type is not None else [
				AccountGroup.CASH_GROUP_NAME, AccountGroup.BANK_GROUP_NAME, AccountGroup.ST_BORROWINGS_GROUP_NAME,
				AccountGroup.LT_BORROWINGS_GROUP_NAME])
			_group_ids = []
			for item in bank_groups:
				_group_ids.append(item)
			bank_groups = '%s' % ', '.join(map(repr, tuple(_group_ids)))
			cash_balance_query = """SELECT account_groups.name AS group_name, account_ledgers.name AS name, account_ledgers.id AS id, 
							SUM(IFNULL(voucher_particulars.amount, 0) * IF(voucher_particulars.is_debit = 1, 1, -1)) AS closing_balance
							FROM  voucher_particulars JOIN 
							voucher ON voucher_particulars.voucher_id = voucher.id 
							AND voucher.voucher_date >= '{since}' 
							AND voucher.voucher_date <= '{till}'
							RIGHT OUTER JOIN	
							account_ledgers ON voucher_particulars.ledger_id = account_ledgers.id 
							RIGHT OUTER JOIN 
							account_groups ON account_groups.enterprise_id = account_ledgers.enterprise_id 
							AND account_groups.id = account_ledgers.group_id 
							WHERE account_groups.enterprise_id = {enterprise_id} 
							AND account_groups.id IN ({bank_groups}) 
							GROUP BY account_ledgers.id
							ORDER BY account_ledgers.id""" .format(
				enterprise_id=enterprise_id, bank_groups=bank_groups, since=since, till=till)
			rowset = executeQuery(cash_balance_query, as_dict=True)
			for row in rowset:
				if row['id'] is not None:
					row['closing_balance'] = format(float(row['closing_balance']), '.2f')
					fund += Decimal(row['closing_balance']).quantize(Decimal('.01'))
					ledgers_list.append(row)
		except Exception as e:
			logger.exception("Failed fetching account dashboard entries. %s" % e.message)
		return float(fund), ledgers_list

	def getSelectFyDateRange(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		fy_start_query = "select fy_start_day from enterprise where id=%s" % enterprise_id
		fy_start_day = executeQuery(fy_start_query)
		date_range = getFYDateRange(fy_start_day=str(fy_start_day[0][0]))
		return date_range

	def cashBalanceChartRender(self, enterprise_id):
		"""

		:param enterprise_id:
		:return:
		"""
		render_val = OrderedDict()
		r_val = []
		v = 0

		val_title = []
		val_title.append([])
		val_title[0].append("Month")
		val_title[0].append("Total")
		val_title_id = []
		# get all the values of cash balance
		for i in sorted(range(4), reverse=True):
			today = datetime.today()
			since, till = JsonUtil.getDateRangeByMonth(today.year, today.month - i)
			mon = since.strftime("%b '%y")
			render_val[mon] = {}
			render_val[mon]["fund"], render_val[mon]["fund_position"] = self.cashBalance(
				enterprise_id=enterprise_id, till=till)
			for val in render_val[mon]["fund_position"]:
				if val.get("id") not in val_title_id:
					val_title_id.append(val.get("id"))
					val_title[0].append(val.get("name"))
		for idex, item in render_val.items():
			r_val.append([])
			r_val[v].append(idex)
			r_val[v].append(float(item["fund"]))
			for idx, val in enumerate(val_title_id):
				try:
					r_val[v].append(float(item["fund_position"][idx].get("closing_balance"))) \
						if val == item["fund_position"][idx].get("id") else r_val[v].append(float(0))
				except IndexError:
					r_val[v].append(float(0))
					pass
				continue
			v = v + 1
		val_title.extend(r_val)
		r_return = [x for x in val_title if x]
		return r_return

	def salesRevenueChartRender(self, enterprise_id):
		"""

		:param enterprise_id:
		:return:
		"""
		render_val = OrderedDict()
		r_title = []
		r_val = []
		r_title.append([])
		r_title[0].append("Month")
		r_title[0].append("Current Year")
		r_title[0].append("Previous Year")
		v = 0
		fy_start_day = self.accounts_dao.db_session.query(Enterprise.fy_start_day.label('fy_start_day')).filter(
			Enterprise.id == enterprise_id).first()
		fiscal_yr = helper.getFYDateRange(fy_start_day=str(fy_start_day[0]))
		# get all the values of sales revenue
		for i in sorted(range(12), reverse=True):
			today = fiscal_yr[1]
			cur_since, cur_till = JsonUtil.getDateRangeByMonth(today.year, today.month - i)
			prev_since, prev_till = JsonUtil.getDateRangeByMonth(today.year - 1, today.month - i)
			cur_till = datetime.strptime(datetime.strftime(cur_till, "%Y-%m-%d") + " 23:59:59", "%Y-%m-%d %H:%M:%S")
			prev_till = datetime.strptime(datetime.strftime(prev_till, "%Y-%m-%d") + " 23:59:59", "%Y-%m-%d %H:%M:%S")
			mon = cur_since.strftime("%b")
			render_val[mon] = {}
			render_val[mon]["current_year"] = float(self.getSalesRevenue(enterprise_id, since=cur_since, till=cur_till))
			render_val[mon]["previous_year"] = float(
				self.getSalesRevenue(enterprise_id, since=prev_since, till=prev_till))

		for index, item in render_val.items():
			r_val.append([])
			r_val[v].append(index)
			r_val[v].append(item["current_year"])
			r_val[v].append(item["previous_year"])
			v = v + 1
		r_title.extend(r_val)

		r_return = [x for x in r_title if x]

		return r_return

	def superEditVoucherCode(
			self, enterprise_id=None, user_id=None, voucher_id=None,
			new_financial_year=None, new_voucher_type_id=None, new_voucher_no=None, new_sub_number=None):
		"""

		:return: json_response from response_code
		"""
		existing_voucher = self.accounts_dao.getVoucher(
			enterprise_id=enterprise_id, financial_year=new_financial_year, voucher_type_id=new_voucher_type_id,
			voucher_no=new_voucher_no, sub_number=new_sub_number)
		response = response_code.failure()
		if not existing_voucher:
			try:
				self.accounts_dao.db_session.begin(subtransactions=True)
				voucher_to_be_modified = self.accounts_dao.getVoucher(enterprise_id=enterprise_id,
				                                                      voucher_id=voucher_id)
				old_code = voucher_to_be_modified.getCode()
				if new_voucher_type_id != 3 and voucher_to_be_modified.voucher_date > datetime.today():  # Bank Voucher
					voucher_to_be_modified.voucher_date = datetime.today()
				voucher_to_be_modified.financial_year = new_financial_year
				voucher_to_be_modified.voucher_no = new_voucher_no
				voucher_to_be_modified.type_id = new_voucher_type_id
				voucher_to_be_modified.sub_number = new_sub_number
				voucher_to_be_modified.super_modified_on = datetime.now()
				voucher_to_be_modified.super_modified_by = user_id
				self.accounts_dao.db_session.add(voucher_to_be_modified)
				self.accounts_dao.db_session.commit()
				self.accounts_dao.db_session.refresh(voucher_to_be_modified)
				response = response_code.success()
				response['custom_message'] = "Successfully changed the Voucher Code from <br />'%s' to '%s'!" % (
					old_code, voucher_to_be_modified.getCode())
				response['code'] = voucher_to_be_modified.getCode()
				VoucherChangelog().queryVoucherInsert(user_id=user_id, enterprise_id=enterprise_id, data=voucher_to_be_modified)
				message = PUSH_NOTIFICATION['voucher_code'] % (old_code, voucher_to_be_modified.getCode())
				push_notification(
					enterprise_id=enterprise_id, sender_id=user_id, module_code="ACCOUNTS", message=message,code=voucher_to_be_modified.getCode())
			except:
				self.accounts_dao.db_session.rollback()
				raise
		else:
			if existing_voucher.id == voucher_id:
				response['custom_message'] = "No changes detected in Voucher code to save!"
			else:
				response['custom_message'] = """A Voucher with Code '%s' already exists.
										Please assign a different Code!""" % existing_voucher.getCode()
			make_transient(existing_voucher)
		return response

	def prepareVoucherApproval(self, voucher_to_be_saved=None, enterprise=None, approved_by=None):
		time_now = datetime.now()
		logger.info("Approved Date '%s' Voucher Date '%s'" % (time_now, voucher_to_be_saved.voucher_date))
		current_fy = getFinancialYear(
			for_date=voucher_to_be_saved.voucher_date, fy_start_day=enterprise.fy_start_day)
		logger.debug("Financial Year '%s'" % current_fy)
		voucher_to_be_saved.financial_year = current_fy
		voucher_to_be_saved.status = 1
		if not voucher_to_be_saved.approved_by:
			voucher_to_be_saved.approved_on = time_now.strftime('%Y-%m-%d %H:%M:%S')
			voucher_to_be_saved.approved_by = approved_by
		voucher_to_be_saved.voucher_no = self.accounts_dao.getLatestVoucherNo(
			financial_year=current_fy, type_id=voucher_to_be_saved.type_id, enterprise_id=enterprise.id)
		return voucher_to_be_saved

	def getPurchaseAcountLedgerId(self, enterprise_id):
		"""
		Get Purchase Account Ledger Id
		"""
		return self.accounts_dao.db_session.query(Ledger.id).filter(Ledger.enterprise_id == enterprise_id, Ledger.group_id == 7,
		                                                            Ledger.name == 'Purchase').first()

	def getAccountGroupTree(self, enterprise_id=None, group_name=None, level=0, parent_groups=None):
		"""
		Recursively retrieves the parent groups of a specified account group up to a certain level.

		:param enterprise_id: ID of the enterprise to which the group belongs.
		:param group_id: ID of the group whose parent groups are to be fetched.
		:param level: Current level of recursion depth.
		:param parent_groups: List to accumulate parent groups.
		:return: List of parent groups.
		"""
		if parent_groups is None:
			parent_groups = []

		try:
			group = self.accounts_dao.db_session.query(AccountGroup).filter(
				AccountGroup.enterprise_id == enterprise_id,
				AccountGroup.name == group_name
			).first()
			logger.info("group:%s" % group)
			if group is None or group.parent is None or level > 2:
				return parent_groups[::-1]

			parent_group = group.parent
			parent_group.name = group.parent.name

			print(parent_group)  # Consider using logging instead of print for production code
			parent_groups.append(parent_group)
			self.getAccountGroupTree(enterprise_id=enterprise_id, group_name=parent_group.name, level=level + 1, parent_groups=parent_groups)

		except Exception as e:
			logger.exception("Failed fetching account group tree. %s", str(e))

		return parent_groups[::-1]


class GeneralVouchersBulkUpload:

	def __init__(self, file_stored_path, login_employee_id, enterprise_id):
		self.logger = logger
		self.accounts_dao = AccountsDAO()
		self.file_path = file_stored_path
		self.login_employee = login_employee_id
		self.enterprise_id = enterprise_id
		self.date_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

		self.Debit = None
		self.Credit = None
		self.bill_code = None
		self.clean_df = None
		self.month = None
		self.Bill_Date = None
		self.invalid_projects_list = {}
		self.invalid_ledgers_list = {}
		self.invalid_ledgers_names = {}
		self.invalid_columns_name = {}
		self.empty_bill_code_date = {}

		self.error_summary = {"invalid_columns_names": [],
		                      "invalid_ledgers_names": [],
		                      "invalid_ledgers_list": [],
		                      "invalid_projects_list": [],
		                      "empty_bill_code_date": []
		                      }

		self.total = 0

	def process(self):

		dc = self.read_file(self.file_path, sheet_name=None)
		for sheet_name, df in dc.items():

			col_values = [str(x).lower() for x in df.columns]
			missing_cols = [value for value in REQUIRED_COLUMN_NAMES if value.lower() not in col_values]

			if missing_cols:
				for value in missing_cols:
					if sheet_name not in self.invalid_projects_list:
						self.invalid_columns_name[sheet_name] = []
					self.invalid_columns_name[sheet_name].append(value)
				continue

			last_values = [str(x).lower() for x in df['S.No'].tail(5).values]
			missing_values = [value for value in REQUIRED_ROW_NAMES if value.lower() not in last_values]

			if missing_values:
				for value in missing_values:
					if sheet_name not in self.invalid_projects_list:
						self.invalid_ledgers_names[sheet_name] = []
					self.invalid_ledgers_names[sheet_name].append(value)

			for project_code in df['Project Code'][:-6]:
				if self.check_projects_list(project_code) == 0:
					if sheet_name not in self.invalid_projects_list:
						self.invalid_projects_list[sheet_name] = []
					self.invalid_projects_list[sheet_name].append(project_code)

			for index, row in df.tail(5).iterrows():
				s_no_value = row['S.No']

				if pd.isna(s_no_value):
					continue

				s_no_lower = str(s_no_value).lower()
				project_code = row['Project Code']

				if s_no_lower in ['debit', 'credit']:
					result = self.get_account_ledger_id(project_code)
					if not result:
						if sheet_name not in self.invalid_ledgers_list:
							self.invalid_ledgers_list[sheet_name] = []
						self.invalid_ledgers_list[sheet_name].append(project_code)

				elif s_no_lower in ['month', 'bill_code', 'bill_date']:
					if pd.isna(project_code):
						if sheet_name not in self.empty_bill_code_date:
							self.empty_bill_code_date[sheet_name] = []
						self.empty_bill_code_date[sheet_name].append("%s: %s" % (s_no_lower.capitalize(), project_code))

		if (len(self.invalid_ledgers_list) == 0 and len(self.invalid_projects_list) == 0 and
				len(self.empty_bill_code_date) == 0 and len(self.invalid_ledgers_names) == 0 and
				len(self.invalid_columns_name) ==0):

			for sheet_name, df in dc.items():

				self.logger.info("Sheet name: '%s'", sheet_name)
				self.clean_df = df[REQUIRED_COLUMN_NAMES].dropna(subset=['S.No'])

				for index, row in self.clean_df.tail(5).iterrows():
					if row['S.No'].lower() == 'debit':
						self.Debit = row['Project Code']
					elif row['S.No'].lower() == 'credit':
						self.Credit = row['Project Code']
					elif row['S.No'].lower() == 'month':
						self.month = row['Project Code']
					elif row['S.No'].lower() == 'bill_code':
						self.bill_code = row['Project Code']
					elif row['S.No'].lower() == 'bill_date':
						self.Bill_Date = row['Project Code']
					self.clean_df.drop(index, inplace=True)

				for _, row in self.clean_df.iterrows():
					project_details = self.get_project_id(row["Project Code"])
					if project_details:
						project_id = project_details[0]['id']
						project_name = project_details[0]['name']
						salary = row['Net Salary']
						voucher_no = self.accounts_dao.getLatestVoucherNo(financial_year=getFinancialYear(), type_id=1,
						                                                  enterprise_id=self.enterprise_id)
						bill_num = str(self.bill_code) + "/" + str(row["Project Code"])
						self.logger.info("voucher: %s Sheet: %s - Processing project: %s (ID: %d) with salary: %f",
						                 voucher_no, sheet_name, project_name, project_id, salary)
						self.total += int(salary)
						self.voucher_creation(project_name, voucher_no, project_id, salary, bill_num)

				self.logger.info("Process completed for sheet: %s, and total Salary: %s\n", sheet_name, self.total)
				self.total = 0

		if self.invalid_columns_name:
			message = "Mismatch columns:\n%s" % self.invalid_columns_name
			self.logger.error(message)
			self.error_summary["invalid_columns_names"] = list(self.invalid_columns_name.values())
		if self.invalid_ledgers_names:
			message = "Mismatch ledgers:\n%s" % self.invalid_ledgers_names
			self.logger.error(message)
			self.error_summary["invalid_ledgers_names"] = list(self.invalid_ledgers_names.values())

		if self.invalid_ledgers_list:
			message = "Invalid ledgers list:\n%s" % self.invalid_ledgers_list
			self.logger.error(message)
			self.error_summary["invalid_ledgers_list"] = list(self.invalid_ledgers_list.values())

		if self.invalid_projects_list:
			message = "Invalid project list:\n%s" % self.invalid_projects_list
			self.logger.error(message)
			self.error_summary["invalid_projects_list"] = list(self.invalid_projects_list.values())

		if self.empty_bill_code_date:
			message = "Empty bill list:\n%s" % self.empty_bill_code_date
			self.logger.error(message)
			self.error_summary["empty_bill_code_date"] = list(self.empty_bill_code_date.values())

		if (self.error_summary["invalid_columns_names"] or self.error_summary["invalid_ledgers_names"] or self.error_summary["invalid_ledgers_list"] or
				self.error_summary["invalid_projects_list"] or self.error_summary["empty_bill_code_date"]):
			return self.error_summary

	def read_file(self, file_path, **kwargs):
		if isinstance(file_path, InMemoryUploadedFile):
			return pd.read_excel(file_path, **kwargs)

	def check_projects_list(self, project_code):
		query = "SELECT EXISTS (SELECT 1 FROM projects WHERE code = '%s' AND enterprise_id = %s) AS project_exists;" % (project_code, self.enterprise_id)
		result = executeQuery(query, as_dict=True)
		return result[0]["project_exists"] if result else False

	def get_project_id(self, project_code):
		query = "SELECT id, name  FROM projects WHERE code = '%s' AND enterprise_id = %s ;" % (project_code, self.enterprise_id)
		result = executeQuery(query, as_dict=True)
		return result

	def get_account_ledger_id(self,ledger_name):
		query = "SELECT id FROM account_ledgers WHERE name = '%s' AND enterprise_id = %s;" % (ledger_name, self.enterprise_id)
		result = executeQuery(query, as_dict=True)
		ledger_id = result[0]['id']if result and result[0]['id'] else False
		return  ledger_id

	def voucher_creation(self,project_name, voucher_no, project_id, salary, bill_num):
		dao = DataAccessObject()
		db_session = dao.db_session
		try:
			if not db_session.is_active:
				db_session.begin()
				db_session.begin(subtransactions=True)

			voucher = Voucher(
				enterprise_id=self.enterprise_id,
				voucher_date=self.Bill_Date,
				voucher_no=voucher_no,
				type_id=1,
				narration="%s of the month: %s and Project name: %s" % (self.Debit,self.month, project_name),
				created_by=self.login_employee,
				created_on=self.date_time,
				status=1,
				financial_year=getFinancialYear(),
				project_code=project_id,
				project_automation_status=0
			)
			db_session.add(voucher)
			db_session.commit()

			self.logger.info("Voucher created successfully project : %s with number: %d", project_name, voucher_no)
			self.voucher_particular_arg_creation(voucher, salary, bill_num)

		except Exception as e:
			self.logger.error("Error during voucher creation: %s", e)
			db_session.rollback()
			raise ValueError("Failed to create voucher: %s" % e)
		finally:
			db_session.close()

	def voucher_particular_arg_creation(self,voucher, salary,bill_num):

		ledger_id = self.get_account_ledger_id(self.Credit)
		self.voucher_particular(item_no=1, ledger_id=ledger_id, is_debit=False, salary=salary, voucher=voucher,bill_num=bill_num)

		ledger_id = self.get_account_ledger_id(self.Debit)
		self.voucher_particular(item_no=2, ledger_id=ledger_id, is_debit=True, salary=salary, voucher=voucher, bill_num=bill_num)

	def voucher_particular(self, item_no=None, ledger_id=None, is_debit=None, salary=None, voucher=None, bill_num=None):
		dao = DataAccessObject()
		db_session = dao.db_session

		try:
			db_session.begin(subtransactions=True)

			vp = VoucherParticulars(
				voucher_id=voucher.id,
				item_no=item_no,
				ledger_id=ledger_id,
				is_debit=is_debit,
				amount=int(salary),
				enterprise_id=self.enterprise_id
			)
			db_session.add(vp)
			db_session.commit()

			if is_debit is False:
				_lb = LedgerBill(
					bill_no=bill_num,
					bill_date=self.Bill_Date,
					is_debit=is_debit,
					ledger_id=ledger_id,
					enterprise_id=self.enterprise_id,
					voucher_id=voucher.id,
					net_value=float(salary)
				)
				db_session.add(_lb)
				db_session.commit()

				db_session.begin(subtransactions=True)
				lbs = LedgerBillSettlement(
					bill_id=_lb.id,
					voucher_id=voucher.id,
					dr_value=0.00,
					cr_value=float(salary),
					enterprise_id=self.enterprise_id
				)
				db_session.add(lbs)
				db_session.commit()
				self.logger.info("Successfully committed transaction for voucher ID: %d", voucher.id)

		except Exception as e:
			self.logger.error("Error during voucher particulars processing: %s", e)
			db_session.rollback()
			raise
		finally:
			db_session.close()