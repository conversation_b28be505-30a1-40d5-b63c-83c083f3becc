"""
"""

from django.conf.urls import patterns, url
from django.contrib.staticfiles.urls import staticfiles_urlpatterns

from erp.commons import json_api

__author__ = 'nandha'

urlpatterns = patterns(
    '',
    url('json/reg_fcm_id/$', json_api.registerFcmId),
    url('json/nm_count/$', json_api.getNotificationCount),
    url('json/nm_list/$', json_api.getNotifications),
    url('json/del_nm/$', json_api.deleteNotifications),
    url('broadcast/$', json_api.broadCastNotification),
    url('version_info/$', json_api.versionInfo),
    url('json/document/$', json_api.downloadFromFTP),
    url('json/get_badge_count/$', json_api.getBadgeCount),
    url('json/get_initial_date/$', json_api.getInitialDate),
    url('json/party/mail_detail/$', json_api.getMailID),
    url('json/send_mail/$', json_api.mailPDF),
    url('json/get_gcs_security_key/$', json_api.getGCSSecurityKey),
    url('json/get_gcs_attachmemt/$', json_api.getGCSAttachmentById),
    url('json/currency_converter/$', json_api.currencyConverter)
)
urlpatterns += staticfiles_urlpatterns()
