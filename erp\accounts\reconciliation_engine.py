import datetime
import pprint
import re
import collections
import uuid
from erp.accounts import logger
from settings import MongoDbConnect

DATE_FORMAT = """%d-%m-%Y"""
TaxItems = collections.namedtuple('TaxItems', ['cgst', 'sgst', 'igst', 'cess', 'txval'])


def get_tax_items(_adict=None):
	"""

	:param _adict:
	:return:
	"""
	try:
		if 'items' in _adict and len(_adict['items']) > 0:
			return TaxItems(
						sum(map(lambda x: float(x['cgst']) if 'cgst' in x else 0, _adict['items'])),
						sum(map(lambda x: float(x['sgst']) if 'sgst' in x else 0, _adict['items'])),
						sum(map(lambda x: float(x['igst']) if 'igst' in x else 0, _adict['items'])),
						sum(map(lambda x: float(x['cess']) if 'cess' in x else 0, _adict['items'])),
						sum(map(lambda x: float(x['txval'] if 'txval' in x else 0), _adict['items'])))
		else:
			return None
	except Exception as e:
		logger.exception(e)


def decorate_sum_of_items(func):
	"""

	:rtype: object
	"""
	def inner_method(match_case=None, dict_pr=None, dict_2b=None, tolerance=None, approximation=None):
		"""

		:param match_case:
		:param dict_pr:
		:param dict_2b:
		:param tolerance:
		:param approximation:
		:return:
		"""
		try:
			dict_2b.update({key: item for key, item in get_tax_items(_adict=dict_2b)._asdict().iteritems()})
			dict_pr.update({key: item for key, item in get_tax_items(_adict=dict_pr)._asdict().iteritems()})
			return func(match_case=match_case, dict_pr=dict_pr, dict_2b=dict_2b, tolerance=tolerance, approximation=approximation)
		except Exception as e:
			logger.exception("Something went wrong on the decorator : %s" % e)
			raise
	return inner_method


class MatchingPoint(object):
	"""
	This class used to match the invoice with PR and wise versa.
	"""

	def __init__(self):
		pass

	def get_total_tax_match(self, is_igst=False, tolerance=0, total_tax_pr=None, total_tax_2b=None):
		"""
		This method used to get the total is matched
		:param is_igst:
		:param tolerance:
		:param total_tax_pr:
		:param total_tax_2b:
		:return boolean:
		"""
		ret = False
		try:
			if tolerance is not None and tolerance != 0:
				if is_igst:
					if (float(total_tax_2b) - (int(tolerance) * 2)) <= float(total_tax_pr) <= (float(total_tax_2b) + (int(tolerance) * 2)):
						ret = True
				else:
					if (float(total_tax_2b) - (int(tolerance) * 3)) <= float(total_tax_pr) <= (float(total_tax_2b) + (int(tolerance) * 3)):
						ret = True
			else:
				if total_tax_2b == total_tax_pr:
					ret = True
		except Exception as e:
			logger.exception(e)
			raise
		return ret

	def exact_match(self, dict_pr=None, dict_2b=None, tolerance=0, approximation=None):
		"""

		:return:
		"""
		count = 0
		try:
			doc_no_2b = re.sub('[^A-Za-z0-9]+', '', dict_2b['inum']) if approximation is True else dict_2b['inum']
			doc_no_pr = re.sub('[^A-Za-z0-9]+', '', dict_pr['inum']) if approximation is True else dict_pr['inum']
			if str(dict_2b['ctin']).strip() == str(dict_pr['ctin']).strip() and dict_2b['typ'] == dict_pr['typ'] and doc_no_2b == doc_no_pr:
				total_tax_2b = dict_2b['cgst'] + dict_2b['sgst'] + dict_2b['igst'] + dict_2b['cess']
				total_tax_pr = dict_pr['cgst'] + dict_pr['sgst'] + dict_pr['igst'] + dict_pr['cess']
				if datetime.datetime.strptime(dict_2b['dt'], DATE_FORMAT) == datetime.datetime.strptime(dict_pr['dt'], DATE_FORMAT):
					count += 1
				if dict_2b['txval'] == dict_pr['txval']:
					count += 1
				if self.get_total_tax_match(
						is_igst=True if float(dict_2b['igst']) > 0 and float(dict_pr['igst']) > 0 else False,
						tolerance=tolerance, total_tax_pr=total_tax_pr, total_tax_2b=total_tax_2b):
					count += 1
		except Exception as e:
			logger.info("ERROR: EXACT MATCH %s" % e)
		return True if count == 3 else False

	def partial_match(self, dict_pr=None, dict_2b=None, tolerance=0, approximation=None):
		"""
		Partial is nothing but having
		:return:
		"""
		count = 0
		doc_no_2b = re.sub('[^A-Za-z0-9]+', '', dict_2b['inum']) if approximation is True else dict_2b['inum']
		doc_no_pr = re.sub('[^A-Za-z0-9]+', '', dict_pr['inum']) if approximation is True else dict_pr['inum']
		try:
			if str(dict_2b['ctin']).strip() == str(dict_pr['ctin']).strip() and dict_2b['typ'] == dict_pr['typ']:
				if doc_no_2b == doc_no_pr:
					count += 1

				if datetime.datetime.strptime(dict_2b['dt'], DATE_FORMAT) == datetime.datetime.strptime(dict_pr['dt'], DATE_FORMAT):
					count += 1

				if dict_2b['txval'] == dict_pr['txval']:
					count += 1

				total_tax_2b = dict_2b['cgst'] + dict_2b['sgst'] + dict_2b['igst'] + dict_2b['cess']
				total_tax_pr = dict_pr['cgst'] + dict_pr['sgst'] + dict_pr['igst'] + dict_pr['cess']
				if self.get_total_tax_match(
						is_igst=True if float(dict_2b['igst']) > 0 and float(dict_pr['igst']) > 0 else False,
						tolerance=tolerance, total_tax_pr=total_tax_pr, total_tax_2b=total_tax_2b):
					count += 1
		except Exception as e:
			logger.info("ERROR: Partial match method %s" % e)
		return True if count == 3 else False

	def probable_match(self, dict_pr=None, dict_2b=None, tolerance=0, approximation=None):
		"""

		:return:
		"""
		count = 0
		probable_count = 0
		try:
			probable_count += 1 if dict_2b['ctin'] != dict_pr['ctin'] else False
			probable_count += 1 if dict_2b['typ'] != dict_pr['typ'] else False
			if probable_count == 1:
				# Check for the Document No are equal
				if approximation is True:
					if re.sub('[^A-Za-z0-9]+', '', dict_2b['inum']) == re.sub('[^A-Za-z0-9]+', '', dict_pr['inum']):
						count += 1
				else:
					if dict_2b['inum'] == dict_pr['inum']:
						count += 1
				# Check for the Document dates are equal
				if datetime.datetime.strptime(dict_2b['dt'], DATE_FORMAT) == datetime.datetime.strptime(dict_pr['dt'], DATE_FORMAT):
					count += 1
				# Check for the taxable values are equal
				if dict_2b['txval'] == dict_pr['txval']:
					count += 1
				# Check for the total tax value is equal
				total_tax_2b = dict_2b['cgst'] + dict_2b['sgst'] + dict_2b['igst'] + dict_2b['cess']
				total_tax_pr = dict_pr['cgst'] + dict_pr['sgst'] + dict_pr['igst'] + dict_pr['cess']
				if self.get_total_tax_match(
						is_igst=True if float(dict_2b['igst']) > 0 and float(dict_pr['igst']) > 0 else False,
						tolerance=tolerance, total_tax_pr=total_tax_pr, total_tax_2b=total_tax_2b):
					count += 1
		except Exception as e:
			logger.info("%s" % e)
			return False
		return True if count == 4 else False

	def unmatched(self, dict_pr=None, dict_2b=None, tolerance=0, approximation=None):
		"""

		:param dict_pr:
		:param dict_2b:
		:param tolerance:
		:param approximation:
		:return:
		"""
		count = 0
		try:
			doc_no_2b = re.sub('[^A-Za-z0-9]+', '', dict_2b['inum']) if approximation is True else dict_2b['inum']
			doc_no_pr = re.sub('[^A-Za-z0-9]+', '', dict_pr['inum']) if approximation is True else dict_pr['inum']
			if doc_no_2b == doc_no_pr:
				if str(dict_2b['ctin']).strip() == str(dict_pr['ctin']).strip() \
						and dict_2b['typ'] == dict_pr['typ'] \
						and datetime.datetime.strptime(
						dict_2b['dt'], '%d-%m-%Y') == datetime.datetime.strptime(dict_pr['dt'], '%d-%m-%Y'):
					count += 1
					# Check for the taxable values are equal
					if dict_2b['txval'] == dict_pr['txval']:
						count += 1
					# Check for the total tax value is equal
					total_tax_2b = dict_2b['cgst'] + dict_2b['sgst'] + dict_2b['igst'] + dict_2b['cess']
					total_tax_pr = dict_pr['cgst'] + dict_pr['sgst'] + dict_pr['igst'] + dict_pr['cess']
					if self.get_total_tax_match(
						is_igst=True if float(dict_2b['igst']) > 0 and float(dict_pr['igst']) > 0 else False,
						tolerance=tolerance, total_tax_pr=total_tax_pr, total_tax_2b=total_tax_2b):
						count += 1
				return True if count >= 1 else False
			else:
				return False
		except ValueError as e:
			logger.info("Some value had error: %s" % e)
			return False

	@staticmethod
	@decorate_sum_of_items
	def match_point(match_case=None, dict_pr=None, dict_2b=None, tolerance=0, approximation=None):
		"""
		This function returns True if and only 2b invoice matches with all the below fields in PR
			1. GSTIN
			2. Document type
			3. Document number
			4. Document date
			5. Total taxable value
			6. Total tax amount <Sum of IGST+CGST+SGST+CESS>
			7. Tax amount head wise
		:param match_case:
		:param dict_pr:
		:param dict_2b:
		:param tolerance:
		:param approximation:
		:return Boolean:
		"""
		count = 0
		mp = MatchingPoint()
		try:
			if match_case == 'exact_match':
				return mp.exact_match(dict_pr=dict_pr, dict_2b=dict_2b, tolerance=tolerance, approximation=approximation)
			elif match_case == 'partial_match':
				return mp.partial_match(dict_pr=dict_pr, dict_2b=dict_2b, tolerance=tolerance, approximation=approximation)
			elif match_case == 'probable_match':
				return mp.probable_match(dict_pr=dict_pr, dict_2b=dict_2b, tolerance=tolerance, approximation=approximation)
			elif match_case == 'unmatched':
				return mp.unmatched(dict_pr=dict_pr, dict_2b=dict_2b, tolerance=tolerance, approximation=approximation)

		except ValueError as e:
			logger.exception(e)
		return True if count >= 4 else False


class ReconciliationEngine(MatchingPoint):
	"""

	"""

	def __init__(self, gst_2b_data=None, gst_pr_data=None):
		super(ReconciliationEngine, self).__init__()
		self.tolerance = 0
		self.approximation = False
		self.response_data = dict()
		self.validation = dict()
		self.validation['2b'] = dict()
		self.validation['2b']['b2b'] = dict()
		self.validation['2b']['b2b']['is_available'] = False
		self.validation['pr'] = dict()
		self.validation['pr']['b2b'] = dict()
		self.validation['pr']['b2b']['is_available'] = False
		self.gst_2b_data = gst_2b_data
		self.gst_pr_data = gst_pr_data
		self.validation['2b']['b2b']['is_available'] = True \
			if ('b2b' in self.gst_2b_data['data']['docdata'] and len(self.gst_2b_data['data']['docdata']['b2b']) > 0) else False
		self.validation['pr']['b2b']['is_available'] = True \
			if ('b2b' in self.gst_pr_data and len(self.gst_pr_data['b2b']) > 0) else False

		if self.validation['pr']['b2b']['is_available']:
			# check if PR data available
			self.response_data['b2b'] = {}
			self.response_data['b2b']['matched_bills'] = []
			self.response_data['b2b']['matchable_bills'] = []
			self.response_data['b2b']['only_in_2b'] = []
			self.response_data['b2b']['only_in_pr'] = []
		self.response_pr_list = []
		self.only_2b_list = []

		self.all_bills_index = set()
		self.matched_bills_index = set()
		self.stored_inv_list = []

	def matching_point(self, invoice=None):
		"""

		:param invoice:
		:return:
		inv_match_count: this variable helps to find that any of po is matched with this invoice given.
		"""
		data = dict()
		data['partial_match'] = []
		data['probable_match'] = []
		data['unmatched'] = []
		inv_match_count = 0
		uid = invoice['uid']
		is_exact_match_set = 0
		try:
			for idx, pr_list in enumerate(self.gst_pr_data['b2b']):
				find = False
				for i, purchase in enumerate(pr_list['inv']):
					self.all_bills_index.add(purchase['inum'])
					purchase['ctin'] = pr_list['ctin']
					if self.match_point(
							match_case='exact_match', dict_pr=purchase, dict_2b=invoice, tolerance=self.tolerance,
							approximation=self.approximation) or (
							purchase['inum'] in self.stored_inv_list if self.stored_inv_list else False):
						inv_match_count += 1
						self.matched_bills_index.add(purchase['inum'])
						self.response_data['b2b']['matched_bills'].append(purchase)
						del self.gst_pr_data['b2b'][idx]['inv'][i]
						is_exact_match_set += 1
						find = True
						break
					else:
						if self.match_point(
							match_case='partial_match', dict_pr=purchase, dict_2b=invoice, tolerance=self.tolerance,
							approximation=self.approximation):
							inv_match_count += 1
							self.matched_bills_index.add(purchase['inum'])
							purchase['uid'] = uid
							data['partial_match'].append(purchase)
							del self.gst_pr_data['b2b'][idx]['inv'][i]
						elif self.match_point(
								match_case='probable_match', dict_pr=purchase, dict_2b=invoice, tolerance=self.tolerance,
								approximation=self.approximation):
							inv_match_count += 1
							self.matched_bills_index.add(purchase['inum'])
							purchase['uid'] = uid
							data['probable_match'].append(purchase)
							del self.gst_pr_data['b2b'][idx]['inv'][i]
						elif self.match_point(
								match_case='unmatched', dict_pr=purchase, dict_2b=invoice, tolerance=self.tolerance,
								approximation=self.approximation):
							inv_match_count += 1
							self.matched_bills_index.add(purchase['inum'])
							purchase['uid'] = uid
							data['unmatched'].append(purchase)
							del self.gst_pr_data['b2b'][idx]['inv'][i]
				if find:
					break
			if (len(data['partial_match']) > 0 or len(data['probable_match']) > 0 or len(data['unmatched']) > 0) and is_exact_match_set == 0:
				self.response_data['b2b']['matchable_bills'].append({'inv': invoice, 'data': data})
		except Exception as e:
			logger.info("ERROR ON GSTR3B %s" % e)
		return True if inv_match_count > 0 else False

	def engine(self, tolerance=0, approximation=False, enterprise_id=None):
		"""
		:param tolerance:
		:param approximation:
		:param enterprise_id:
		:return:
		"""
		self.tolerance = tolerance
		self.approximation = approximation
		doc_type = []
		count = 0
		try:
			db = MongoDbConnect['gstr3b']
			query = db.find({'uid': int(enterprise_id)})
			data_set = list(query)
			if len(data_set) > 0:
				for i_list in data_set[0]['data']:
					for i_inv in i_list['inv']:
						self.stored_inv_list.append(str(i_inv['inum']))
			logger.info("::STORED PURCHASE ADDED ARE %s" % self.stored_inv_list)
			if self.validation['2b']['b2b']['is_available']:
				# check if B2B 2B data available
				for inv_list in self.gst_2b_data['data']['docdata']['b2b']:
					for invoice in inv_list['inv']:
						count = count + 1
						invoice['uid'] = str(count)
						doc_type.append(invoice['typ'])
						invoice['ctin'] = inv_list['ctin']
						if not self.matching_point(invoice=invoice):
							self.response_data['b2b']['only_in_2b'].append(invoice)
				for pr_list in self.gst_pr_data['b2b']:
					for purchase in pr_list['inv']:
						match_count = 0
						for inv_list in self.gst_2b_data['data']['docdata']['b2b']:
							for invoice in inv_list['inv']:
								if purchase['inum'] == invoice['inum']:
									match_count += 1
						if match_count == 0:
							self.response_data['b2b']['only_in_pr'].append(purchase)
		except Exception as e:
			logger.info("Something went wrong on GSTR3B engine fn: %s" % e)
		return self.response_data
