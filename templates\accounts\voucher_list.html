{% extends "accounts/sidebar.html" %}
{% block voucher %}

<style>
	#voucherList tbody td:nth-child(3),
	#voucherList tbody td:nth-child(1),
	#voucherList tbody td:nth-child(2) {
		text-align: center;
	}
</style>

<div class="right-content-container" style="padding-bottom: 0;">
	<div class="page-title-container">
		<span class="page-title">Voucher</span>
	</div>
	<div class="col-lg-12">
		<div class="page-heading_new">
			<input type='hidden' id='voucher_edit_access' value='{{access_level.edit}}' />
			{% if access_level.edit %}
				<a href="/erp/accounts/voucher/" class="btn btn-new-item pull-right create_voucher" data-tooltip="tooltip" title="Create New Voucher">
					<i class="fa fa-plus" aria-hidden="true"></i>
					<span class="btn-new-item-label"></span>
				</a>
			{% else %}
				<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in Accounts Module. Please contact the administrator.">
					<a role="button" class="btn btn-new-item pull-right disabled">
						<i class="fa fa-plus" aria-hidden="true"></i>
						<span class="btn-new-item-label"></span>
					</a>
				</div>
			{% endif %}	
		</div>
		<div class="filter-components">
			<div class="filter-components-container">
				<div class="dropdown">
					<button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;Filter">
						<i class="fa fa-filter"></i>
					</button>
					<span class="dropdown-menu arrow_box arrow_box_filter">
				  		<form action="/erp/accounts/voucher/list/" method="post" id="form-voucher-list">{% csrf_token %}
							<div class="col-sm-12 form-group" >
								<label>Date Range</label>
								<div id="reportrange" class="report-range form-control">
									<i class="glyphicon glyphicon-calendar"></i>&nbsp;
									<span></span> <b class="caret"></b>
									<input type="hidden" class="fromdate" id="from_date"  name="from_date" value="{{from_date}}"/>
									<input type="hidden" class="todate" id="to_date"   name="to_date" value="{{to_date}}"/>
								</div>
							</div>
							<div class="col-sm-12 form-group">
								<label>Type</label>
								<select class="form-control" name="voucher_type_no" id="voucher_type">
									{% for j in voucher_type %}
									<option value="{{ j.0 }}">{{ j.1 }}</option>
									{% endfor %}
								</select>
								<input type="hidden" id="search_voucher_type" value="{{search_voucher_type}}"/>
								<input type="hidden" id="search_voucher_view" value="{{view_type}}"/>
							</div>
							<div class="filter-footer">
								<button type="submit" class="btn btn-save" id="id-filter-voucher" onclick="$('#form-voucher-list').submit();">Apply</button>
	      					</div>
						</form>
					</span>
				</div>
				<span class='filtered-condition filtered-date'>Date: <b></b></span>
				<span class='filtered-condition filtered-type'>Type: <b></b></span>
			</div>
		</div>
		<div class="clearfix"></div>
	</div>
	<div class="col-lg-12">
		<div class="col-lg-12">
			<div class="csv_export_button">
				<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#voucherList'), 'Voucher_List.csv']);" data-tooltip="tooltip" title="Download Voucher&nbsp;List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
			</div>
			<table class="table table-bordered custom-table table-striped" id ="voucherList" style="width: 100%;" >
				<thead>
					<tr>
						<th style='width: 50px; max-width: 50px;'> S.No </th>
						<th style='width: 100px;'> Date </th>
						<th style='width: 120px;'> Voucher No </th>
						<th style='width: 100px;'> Type </th>
						<th style='width: 100px;'> Approved On </th>
						<th style='width: 250px;'> Project </th>
						<th> Narration </th>
					</tr>
				</thead>
				<tbody>
				</tbody>
			</table>
		</div>
	</div>
</div>
<div class="hide">
	<form target='_blank' action='/erp/accounts/voucher/edit/' method='post' id="voucher_edit_form">
		{% csrf_token %} 
	 	<input type='hidden' id='edit_voucher_id' name='voucher_no' value='' />
	 	<input type='hidden' id='edit_voucher_type' name='voucher_type' value='' />
		<input type='hidden' id='clone_voucher' name='cloneStatus' value='' />
 	</form>
 </div>
<script type="text/javascript">
	$(document).ready(function(){
		$("#voucher_type").val($("#search_voucher_type").val());
		populateVouchers();
	});
	
	$('.nav-pills li').removeClass('active');
	$('#li_voucher').addClass('active');

	function updateFilterText() {
		$(".filtered-date b").text($("#reportrange").find("span").text());
		$(".filtered-type b").text($("#voucher_type option:selected").text());
	}

	function editVoucherRow(voucherId, voucherType, enableClone="disable", openTarget="") {
		$("#edit_voucher_id").val(voucherId);
		$("#edit_voucher_type").val(voucherType);
		$("#clone_voucher").val(enableClone);
		$("#voucher_edit_form").attr("target", openTarget).submit();
	}

var oTable;
var oSettings;
function populateVouchers(){
    $('#loading').show();
    var voucher_type_no=$("#voucher_type option:selected").val();
    var view_type=($("#search_voucher_view").val() == 'None'? null:$("#search_voucher_view").val());
    var from_date=$("#from_date").val();
    var data = [];
    $.ajax({
        url: "/erp/accounts/json/voucher/populate_voucher_lists/",
        type: "POST",
        dataType: "json",
        data: {"voucher_type_no": voucher_type_no, "view_type": view_type, "from_date": from_date},
        success: function(response) {
			var csrf_token = '<input type="hidden" name="csrfmiddlewaretoken" value="'+ getCookie('csrftoken') +'">';
			VoucherListjsonObj = [];
            $.each(response, function (i, item) {
                var voucherEditAccess = $("#voucher_edit_access").val();
                var clone_icon = "";
				var edit_name = `${clone_icon} <a class='edit_link_code' onclick='editVoucherRow("${item.id}", "${item.type_id}", "_blank");'>
								     ${item.getCode}
							 	  </a>`;
				if(voucherEditAccess == "True") {
                    var voucher_narration = `<span class='table-inline-icon internal hide' style='margin:-9px 38px;' data-tooltip='tooltip' data-placement='bottom' data-title='Clone' onclick='editVoucherRow("${item.id}", "${item.type_id}", "enable", "_blank");' role="button" >
												<i class="fa fa-clone" aria-hidden="true" style="font-size: 16px;"></i>
									        </span>`;
			    }
				voucher_narration += `<span class='inline-icon-content' style='text-align: justify;'>${item.narration}</span>
										<span class='table-inline-icon internal hide' data-tooltip='tooltip' data-placement='bottom' title='Edit'
										onclick='editVoucherRow("${item.id}", "${item.type_id}");'>
											<i class='fa fa-pencil'></i>
										</span>
										<span class='table-inline-icon internal edit-in-new hide' data-tooltip='tooltip' data-placement='bottom' title='Edit in New Tab' onclick='editVoucherRow("${item.id}", "${item.type_id}", "_blank");'>
											<i class='fa fa-external-link'></i>
										</span>`;
				if(item.voucher_date == "" || item.voucher_date == null){
                    var modDate = "";
                }
                else {
                    var modDate = moment(item.voucher_date).format('MMM D, YYYY ');
                }
                if(item.approved_on == "" || item.approved_on == null){
                    var aprDate = "";
                }
                else {
                    var aprDate = moment(item.approved_on).format('MMM D, YYYY ');
                }
                var project_name = `${item.project_name}`;
                voucher = {}
		        voucher ["voucherId"] = item.id;
		        voucher ["voucherNumber"] = item.getCode;
		        voucher ["voucherType"] = item.type_id;
		        VoucherListjsonObj.push(voucher);
                data.push( [ i+1, modDate,edit_name,item.name,aprDate,project_name,voucher_narration ] );
            });
			if ($("#voucherList tr").length == 1){
                var row = "";
                $('#voucherList').append(row).addClass('tbl');
            }
            localStorage.setItem('voucherListNav', JSON.stringify(VoucherListjsonObj));
            oTable = $('#voucherList').DataTable( {
                data: data,
                deferRender: true,
                fixedHeader: false,
                "scrollY": Number($(document).height() - 230),
	            "scrollX": true,
	            "pageLength": 50,
                "search": {
                    "smart": false
                },
                "columns": [
				null,{ "type": "date" },null,null,{ "type": "date" },null,null],
                "columnDefs": [{
                "targets": 6,
                    "orderable": false
                }]
            } );
            oTable.on("draw",function() {
                var keyword = $('#voucherList_filter > label:eq(0) > input').val();
                $('#voucherList').unmark();
                $('#voucherList').mark(keyword,{});
                UpdateVoucherJson();
                setHeightForTable();
                listTableHoverIconsInit('voucherList');
            });
            oTable.on('page.dt', function() {
              $('html, body').animate({
                scrollTop: $(".dataTables_wrapper").offset().top - 15
               }, 'slow');
              listTableHoverIconsInit('voucherList');
            });
            oSettings = oTable.settings();
            $( window ).resize();
            listTableHoverIconsInit('voucherList');
            $('#loading').hide();
        }
    });

    function UpdateVoucherJson(){
    	setTimeout(function(){
	    	VoucherListjsonObj = [];
	    	var rows = oTable.rows( { page : 'current'} ).data();
	        for(var i=0;i<rows.length;i++) {
	        	var selectedRow = $(rows[i][2]);
	        	voucher = {}
		        voucher ["voucherId"] = selectedRow.find(".voucher_id_in_class").val();
		        voucher ["voucherNumber"] = selectedRow.text();
		        voucher ["voucherType"] = selectedRow.find(".voucher_type_in_class").val();
	            VoucherListjsonObj.push(voucher);
	        }
			localStorage.setItem('voucherListNav', JSON.stringify(VoucherListjsonObj));			
		},10);
    }

    setTimeout(function(){
        $("#filter_textbox").on("focus", function() {
            if(oTable != undefined && $("#pagination-select").val() == -1) {
                $("#pagination-select").val(50).trigger('change');
                $("#pagination-select").after("<p class='pagination-warning-for-all'>For search speed, paging entries changed to 50. But your search result will show all your filtered data through pagination.</p>");
                setTimeout(function(){
                    $(".pagination-warning-for-all").fadeOut(1000);
                },10000)
            }
        });
    },1000);
}
</script>
{% endblock %}