.left-menu-container-mobile {
	width: 300px;
	 float: left;
	 position: fixed; 
	 left: 0; 
	 top: 0;
	 background: #fff; 
	 z-index: 10010; 
	 height: 100%; 
	 opacity: 0.97; 
	 display: none;
	 margin-left: 0;
     margin-top: 0;
     padding-top: 10px;
     overflow: auto;
}

.mobile-sidenav li{
	list-style-type: none;
}

.mobile-sidenav ul.mobile-sidenav-parent {
	padding: 8px 16px;
  	text-decoration: none;
  	font-size: 16px;
  	color: #616161;
  	display: block;
  	transition: 0.3s;
  	cursor: pointer;
  	margin: 0;
}

.mobile-sidenav ul.mobile-sidenav-child {
    margin-top: 10px;
    display: none;
    margin-left: -30px;
    margin-right: -15px;
    padding-left: 0;
}

.mobile-sidenav ul.mobile-sidenav-child li a{
	padding: 6px 4px;
  	text-decoration: none;
  	font-size: 14px;
  	color: #616161;
  	display: block;
  	transition: 0.3s;
  	cursor: pointer;
  	margin: 0;
  	padding-left: 60px;
}

.mobile-sidenav ul.mobile-sidenav-parent.active,
.mobile-sidenav ul.mobile-sidenav-parent:hover {
	background: #ccc;
}

.mobile-sidenav ul.mobile-sidenav-parent.active {
	padding-bottom: 0;
}

.mobile-sidenav ul.mobile-sidenav-parent:hover .mobile-sidenav-child,
.mobile-sidenav ul.mobile-sidenav-parent.active .mobile-sidenav-child {
	background: #eee;
}

.mobile-sidenav ul.mobile-sidenav-child li a:hover {
	background: #ddd;
}

.mobile-sidenav ul.mobile-sidenav-parent li.mobile-sidenav-header:after {
	font: normal normal normal 14px/1 FontAwesome;
	content: "\f054";
	float: right;
	margin-top: 5px;
}