<!DOCTYPE html>
<style type="text/css">
	body{
		color: #000;
		font-family: 'Times New Roman';
	}
	td {
		text-align: left;
		line-height: 25px;
		font-size: 11pt !important;
		padding:5px !important;
	}
	th{
		font-size:12pt;
		text-align:center !important;
		padding: 0px 5px !important;
	}
	.enterprise_details{
		font-size:10.5pt;
	}
	.enterprise_name{
		font-size:13pt
	}
	.page_title{
		font-size: 18pt;
	}
	.grn_details{
		width: 85px;
	    font-weight: bold;
	    font-size: 12pt;
	    display: inline-table;
	    word-break: break-all;
	    margin-left: 40px;
	}
	.grn_data{
		width: 200px;
		font-size: 12pt;
		display: inline-table;
		vertical-align:top;
		word-break: break-all;
	}
	.register_data {
	    width: calc(100% - 100px);
	    float: left;
	    font-size:10.5pt;
	    margin-left:26px;
	}
	.register_details {
	    float: left;
	    margin-left: 26px;
	    font-size:10.5pt;
	}
	.vendor_data{
		width: calc(100% - 100px);
		margin-left: 100px;
		font-size:11pt;
	}
	.vendor_details {
		float: left;
		font-size:12pt;
		font-weight:bold;
	}
	@font-face {
		font-family: 'Times New Roman';
		font-style: normal;
		font-weight: 400;
		src: url(https://www.xserp.in/site_media/fonts/times.ttf) format('truetype');
	}
</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/bootstrap.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">

<body>
<div class="container">
	<div>
		<div class="col-sm-8" style="padding: 0;">
			<img src="{{enterprise_logo}}"style="max-height: 10mm">
			<div class="enterprise_name">{{ source.enterprise.name }}</div>
			<div class="enterprise_details"> {{ enterprise_address }}</div>
			<div class="enterprise_details"><b>Ph:</b>{% if source.enterprise.primary_contact_details.contact.phone_no %} {{ source.enterprise.primary_contact_details.contact.phone_no }} {% endif %}<b>E-mail:</b>{% if source.enterprise.primary_contact_details.contact.email %} {{ source.enterprise.primary_contact_details.contact.email }} {% endif %}</div>
		</div>

		<div class="col-sm-4">
            <span class="page_title" >{{ form_name }}</span>
            {% if cin_detail %}
                <div class=" register_details">{{ cin_label }}</div>
                <div class=" register_data"> {{ cin_detail }}</div>
            {% endif %}
		</div>
	</div>

	<div>
		<div class="col-sm-7" style="padding: 0;"><br>
			<div class="vendor_details">Vendor Name</div>
			<div class="vendor_data"><b>: {{ source.supplier.name }}</b><br>{% if source.supplier.address_1 %}{{ source.supplier.address_1 }}, {% endif %}{% if source.supplier.address_2 %}{{ source.supplier.address_2 }}<br>{% endif %}
			  {% if source.supplier.city %}{{ source.supplier.city }}, {% endif %}{% if source.supplier.state %}{{ source.supplier.state }}, {% endif %}
				{% for country in country_list %}
					{% if country.country_code == source.supplier.country_code %}
						{{ country.country_name|upper }}
					{% endif %}
				{% endfor %}
			</div>

			<div class="vendor_details">Vendor Code</div>
			<div class="vendor_data"><b>: </b>{{ source.supplier.code }}<b style="font-size:12pt;"> GSTIN</b> : {% for reg_detail in source.supplier.registration_details %}
																										{% if reg_detail.label == "GSTIN" %}
																											{{ reg_detail.details }}
																										{% endif %}
																									{% endfor %}</div>

			<div class="vendor_details">Phone No </div>
			<div class="vendor_data"><b>:</b> {{ source.supplier.primary_contact_details.contact.phone_no }}</div>
		</div>
		<div class="col-sm-5"><br>
				<div class="grn_details">GRN No.</div>
				<div class="grn_data"><b>:</b> {{ source.getCode }} </div>

				<div class="grn_details">GRN Date</div>
				<div class="grn_data"><b>:</b> {{ receipt_date }}</div>
				{% if source.invoice_type = 1 %}
					<div class="grn_details">D.C. No</div>
					<div class="grn_data"><b>:</b> {{ source.invoice_no }} </div>

					<div class="grn_details">D.C. Date</div>
					<div class="grn_data"><b>:</b> {{ invoice_date }}</div>
				{% else %}
					<div class="grn_details">Invoice No</div>
					<div class="grn_data"><b>:</b> {{ source.invoice_no }} </div>

					<div class="grn_details">Invoice Date</div>
					<div class="grn_data"><b>:</b> {{ invoice_date }}</div>
				{% endif %}

				{% if source.ecommerce_gstin %}
					<div class="grn_details">E-Commerce GSTIN</div>
					<div class="grn_data"><b>:</b> {{ source.ecommerce_gstin }}</div>
				{% endif %}
		</div>
	</div>

	{% if no_of_items_received > 0 %}
		<table border=1 bordercolor="#A9A9A9" class="row-seperator column-seperator" style="width:100% !important;">
			<thead>
				<tr style="padding-left:8px; border-top: solid 1px #000; border-bottom: solid 1px #000;">
					<th rowspan="2">S.No</th>
					<th rowspan="2">{% if source.received_against == "Job In" %} OA No. {% else %} PO No <br>PO Date {% endif %}</th>
					<th rowspan="2">Items Received <br>Drawing No./Description</th>
					<th rowspan="2">Unit</th>
					<th rowspan="2">DC Qty</th>
					<th rowspan="2">Received Qty</th>
					<th rowspan="2">Accepted Qty</th>
					<th rowspan="2">Short Qty</th>
					<th rowspan="2">Reject Qty</th>
				</tr>
			</thead>
			<tbody>
				{% for grn_material in grn_materials_received %}
					<tr>
						<td>{{ grn_material.item_index }}</td>
						<td style="width:15%">{{ grn_material.ref_code }} <br>{{ grn_material.ref_date_string }}</td>
						<td style="width:280px; word-break: break-word;;">{% if grn_material.material_drawing_no %} {{ grn_material.material_drawing_no }} <br> {% endif %} {{ grn_material.material_name }} {% if grn_material.material_make %} {{ grn_material.material_make }} {% endif %} {% if grn_material.is_faulty %} [Faulty] {% endif %} <br> HSN/ SAC: {{ grn_material.material_hsn_code }}</td>
						<td>{{ grn_material.material_unit }}</td>
						<td>{{ grn_material.quantity }}</td>
						<td>{{ grn_material.received_qty }}</td>
						<td>{{ grn_material.accepted_qty }}</td>
						<td>{{ grn_material.shortage }}</td>
						<td>{{ grn_material.rejected_qty }}</td>
					</tr>
				{% endfor %}
			</tbody>
		</table>
	{% endif %}
	{% if no_of_items_returned > 0 %}
		<table border=1 bordercolor="#A9A9A9" class="row-seperator column-seperator" style="width:100% !important;">
			<thead>
				<tr style="padding-left:8px; border-top: solid 1px #000; border-bottom: solid 1px #000;">
					<th rowspan="2">S.No</th>
					<th rowspan="2">DC/Inv. No.<br>Iss. Date</th>
					<th rowspan="2">Items Returned<br> Drawing No./Description</th>
					<th rowspan="2">Unit</th>
					<th rowspan="2">DC Qty</th>
					<th rowspan="2">Received Qty</th>
					<th rowspan="2">Accepted Qty</th>
					<th rowspan="2">Short Qty</th>
					<th rowspan="2">Reject Qty</th>
				</tr>
			</thead>
			<tbody>
				{% for grn_material in grn_materials_returned %}
					<tr>
						<td>{{ grn_material.item_index }}</td>
						<td style="width:15%">{{ grn_material.ref_code }} <br>{{ grn_material.ref_date_string }}</td>
						<td style="width:280px;">{% if grn_material.material_drawing_no %} {{ grn_material.material_drawing_no }} <br> {% endif %} {{ grn_material.material_name }} {% if grn_material.material_make %} {{ grn_material.material_make }} {% endif %} {% if grn_material.is_faulty %} [Faulty] {% endif %} <br> HSN/ SAC: {{ grn_material.material_hsn_code }}</td>
						<td>{{ grn_material.material_unit }}</td>
						<td>{{ grn_material.quantity }}</td>
						<td>{{ grn_material.received_qty }}</td>
						<td>{{ grn_material.accepted_qty }}</td>
						<td>{{ grn_material.shortage }}</td>
						<td>{{ grn_material.rejected_qty }}</td>
					</tr>
				{% endfor %}
			</tbody>
		</table>
	{% endif %}
</div>
{% if has_qir %}
	<div style="page-break-after: always"></div>
	{% include "stores/grn_qir_report_print.html" %}
{% endif %}
{% if rejection_count > 0 %}
	<div style="page-break-after: always"></div>
	{% include "stores/grn_rejection_print.html" %}
{% endif %}
<br>
</body>