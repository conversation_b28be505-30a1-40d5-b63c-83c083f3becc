.multiselect-container {
  position: absolute;
  list-style-type: none;
  margin: 0;
  padding: 0
}

.multiselect-container .input-group {
  margin: 5px
}

.multiselect-container>li {
  padding: 0
}

.multiselect-container>li>a.multiselect-all label {
  font-weight: 700
}

.multiselect-container>li.multiselect-group label {
  margin: 0;
  padding: 3px 20px 3px 20px;
  height: 100%;
  font-weight: 700
}

.multiselect-container>li.multiselect-group-clickable label {
  cursor: pointer
}

.multiselect-container>li>a {
  padding: 0
}

.multiselect-container>li>a>label {
  margin: 0;
  height: 100%;
  cursor: pointer;
  font-weight: 400;
  padding: 3px 20px 3px 40px
}

.multiselect-container>li>a>label.radio,
.multiselect-container>li>a>label.checkbox {
  margin: 0
}

.multiselect-container>li>a>label>input[type=checkbox] {
  margin-bottom: 5px
}

.btn-group>.btn-group:nth-child(2)>.multiselect.btn {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px
}

.form-inline .multiselect-container label.checkbox,
.form-inline .multiselect-container label.radio {
  padding: 3px 20px 3px 40px
}

.form-inline .multiselect-container li a label.checkbox input[type=checkbox],
.form-inline .multiselect-container li a label.radio input[type=radio] {
  margin-left: -20px;
  margin-right: 0
}

.multiselect-all label {
  color: #209be1 !important;
  font-weight: normal !important;
}

.multiselect-container.dropdown-menu {
  z-index: 1001 !important;
  width: 100%;
  margin-top: 34px;
  max-height: 316px;
  overflow: auto;
  min-width: 272px;
}

.multiselect-container.dropdown-menu>li {
  padding: 6px 0 !important;
  font-size: 13px;
}

.multiselect-container.dropdown-menu li a {
  padding: 0 6px;
  margin-left: 4px;
}

.multiselect-container.dropdown-menu>li>a,
.multiselect-container.dropdown-menu>li>a:hover {
  background: transparent !important;
}

.multiselect-container label {
  text-transform: none;
}

select[multiple]+.btn-group {
  display: block;
}

.multiselect.dropdown-toggle {
  width: 100%;
  text-align: left;
}

.multiselect.dropdown-toggle .caret {
  float: right;
  margin-top: 8px;
}

.multiselect-container .checkbox input[type="checkbox"] {
  opacity: 1;
  margin-top: 1px;
  margin-left: -30px;
}

ul.multiselect-container li a:focus {
  outline-color: transparent;
}

.multiselect-container .input-group-addon {
  position: initial;
  border: 1px solid #ccc;
  border-radius: 4px 0 0 4px !important;
  padding: 6px 12px;
}

.multiselect-container label.checkbox {
  padding-left: 35px !important
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 20px;
  width: 20px;
  background-color: #fff;
  border-radius: 3px;
  border: solid 1px #ccc;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.multiselect-container input:checked~.checkmark:after {
  display: block;
  border-color: #000;
}

.multiselect-container input:checked~.checkmark {
  border-color: #000;
}

.multiselect-container li.active {
  background: lightgoldenrodyellow;
}

.multiselect-container li.active label {
  color: #000;
}

.multiselect-container .checkmark:after {
  left: 6px;
  top: 2px;
  width: 6px;
  height: 11px;
  border: solid #333;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}