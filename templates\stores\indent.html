{% extends "stores/sidebar.html" %}
{% block indent %}

{% if logged_in_user|canView:'PURCHASE INDENT' %}
<style>

	li.indent_side_menu a{
	outline: none;
	background-color: #e6983c !important;
	}

	#id_indent-instructions {
		height: 100px;
	}

	#cattable tr[data-toggle='open'] td:nth-child(8){
		opacity: 0.5;
		background: #ddd;
		pointer-events: none;
	}

	#cattable tr[data-toggle='open'] input,
	#cattable tr[data-toggle='open'] select	{
		opacity: 0.5;
		background: #ddd;
	}
	
	.side-content.div-disabled {
		padding: 0;
		background-color: transparent !important;
	}
	
	.side-content.div-disabled a,
	.side-content.div-disabled div,
	.side-content.div-disabled input {
		border: none;
		outline: none;
		box-shadow: 0 0;
		height: 30px;
		padding-left: 3px;
		background-color: transparent !important;
		font-size: 13px;
		color: #333;
	}
	
	.side-content.div-disabled input {
		padding-left: 6px;
	}
	
	.side-content.div-disabled .chosen-single div {
		display: none;
	}

	.side-content.div-disabled .chosen-single span {
		margin-top: 2px;
	}

	.table.text_box_in_table .chosen-single {
		height: 26px;
	    font-size: 13px;
	    line-height: 25px;
	    padding: 0 0 0 3px;
	}

	.table.text_box_in_table .chosen-container-single .chosen-single div b {
		margin-top: 2px;
	}

	.supplier_total_amt {
		font-size: 20px; 
		float: right; 
		margin-top: 15px;
	}

	.parent-supplier-container {
	    padding: 6px 12px;
	    border: solid 1px #ccc;
	    border-top: none;
	    margin-bottom: 15px;
	}

	.supplier_name_tab a{
		color:  #004195 !important;
		font-size: 16px;
	}

	.chosen-select[readonly='readonly']+div  {
		pointer-events: none;
	}

	.supplier_type_select {
		width:  100%;
		border-radius: 4px;
    	border-color: #ccc;
	}

	.disabled_material {
		text-decoration: line-through;
	}

	.disabled_material td {
		background: rgba(0,0,0,0.1);
	    opacity: 0.7;
	    pointer-events: none;
	}

	.disabled_material td.td_disable_indent_material {
		opacity: 1;
		pointer-events: auto;
		background: #fff;
	}

	.disabled_material input {
		background: transparent;
	}

	.disabled_material td:last-child {
		background: transparent;
		opacity: 1;
		pointer-events: inherit;
		color: #000 !important;
	}

	.td_for_icon .fa.fa-ban, 
	.td_for_icon .fa.fa-plus {
		font-size: 18px;
	    margin-top: 3px;
	}

	.side-content .custom-error-message {
		margin-top: -24px;
    	right: 26px;
	}

	.indent_add .fixedHeader-floating {
		display: none;
	}

	.error-duplicate-supplier {
		background: rgba(249, 255, 81,0.35);
	}

	.error-duplicate-supplier .chosen-container {
		box-shadow: 0 0;
	}

	.error-duplicate-supplier input,
	.error-duplicate-supplier select,
	.error-duplicate-supplier a span{
		background: rgba(221, 75, 57,0.00);
	}

	.add_split_container .chosen-disabled {
		opacity: 1 !important;
	    box-shadow: 0 0;
	}

	.add_split_container .chosen-disabled a {
		border:  none;
	}

	.add_split_container .chosen-disabled b {
		display: none !important;
	}

	.add_split_container select[disabled] {
		-webkit-appearance: none;
	    -moz-appearance: none;
	    text-indent: 1px;
	    text-overflow: '';
	    background: transparent;
	}

	.loading-main-container {
		display: block;
	}

	.payment-terms-container {
		float: left;
	    width: 460px;
	    margin-left: 15px;
	    margin-top: 9px;
	}

	.payment-terms-container div {
		float: left;
	}

	.payment-terms-container div:nth-child(2) input {
		border-radius: 4px 0 0 4px !important;
	}

	.payment-terms-container div:last-child select{
		border-radius: 0 4px 4px 0 !important;
	}

	.payment-terms-container input {
		width: 90px;
	}

	.payment-terms-container select {
		width: 140px;
	}

	.table .payment-terms-container div input,
	.table .payment-terms-container div select {
		padding: 4px 12px 6px;
	    height: 34px;
	    font-size: 14px;
	    border-radius: 0;
	    margin-left: -1px;
	}

	.delivery_due_container {
		float: right;
	    margin-bottom: 5px;
	    margin-top: -5px;
	}

	.delivery_due_container input {
		padding: 10px;
	    height: 26px;
	    font-size: 12px;
	    padding-left: 45px;
	}

	.delivery_due_container i {
		font-size: 12px;
	    padding: 0px 12px;
	    height: 24px;
	    margin-top: -26px;
	    margin-left: 1px;
	}

	.delivery_due_container i:before {
		margin-top: 6px !important;
	}

	.indent-po-checkbox {
		padding-left: 20px !important; 
		margin-bottom: 4px; 
		margin-top: 4px; 
		width: calc(100% - 920px); 
		float: left;
	}

	.indent-po-checkbox label {
		color: #209be1;
		font-size: 14px;
	}

	.indent-po-checkbox label:before {
		border: 1px solid #209be1;
		background: transparent;
	}

	.indent-po-checkbox label:after {
		color: #ffffff;
	}

	.indent-po-checkbox.checkbox input[type="checkbox"]:checked + label::before {
		background: #209be1;
	}

	.tour_bucket{
		background:transparent;
	}
	#cattable tbody tr td .tree-view {
		position: relative;
	}

	#cattable tbody tr td .tree-view:before {
	    border-bottom: 1px solid #bbb;
	    content: "";
	    left: 6px;
	    position: absolute;
	    top: 6px;
	    width: 1.5em;
	}

	#cattable tbody tr td .tree-view:after {
	    border-left: 1px solid #bbb;
	    content: "";
	    left: 6px;
	    position: absolute;
	    top: -25px;
	    height: 4em;
	}

	#cattable .fa-plus:before,
	#cattable .fa-minus:before {
	    border: solid 1px #333;
	    padding: 2px 3px 2px 3px;
	    border-radius: 3px;
	    font-size: 10px;
	}

</style>

<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/utility.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/indent.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/make.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/tags.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/load_material.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/Component/projects.js?v={{ current_version }}"></script>

{% if module_access.indent %}
	<div class="right-content-container">
		<div id='loadingmessage_changelog_listing_ie' style="text-align:center;position: absolute;z-index: 1;top: 50%;left: 50%;display:none">
						<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' width="100">
						<br /> Please wait...
		</div>
		<div class="page-title-container">
			<span class="page-title">Purchase Indent</span>
		</div>
		<div class="page-heading_new" style="padding: 0 30px;">
			<input type="hidden" class="indent_module_access" id="indent_module_access" name="indent_module_access" value="{{ module_access.indent }}" />
			<input type="hidden"  id="is_purchase_approve_permission" value="{{logged_in_user|canApprove:'PURCHASE'}}" />
			<input type="hidden" value="{{selected_project_code}}" id="selected_project_code" hidden="hidden">
			<input type="hidden" id="is_price_modification_disabled" value="{{is_price_modification_disabled}}" />
			<span class="page_header_indent"></span>
			{% if logged_in_user.is_super %}
				<a class="btn super_user_icon" onclick="EditIndentNumber();" style="">
					<i class="fa fa-pencil"></i>
				</a>
				<div class="xsid_number_edit hide">
					<form class="form-inline" style="display: inline-block;" action="">
						<div class="form-group">
							<input type="text" class="form-control" id="ind_financial_year" name="ind_financial_year" maxlength="5">
						</div>
						<div class="form-group">
							<input type="text" class="form-control" id="ind_type" name="ind_type" maxlength="3" readonly="true">
						</div>
						<div class="form-group">
						  <input type="text" id="ind_number"  name="ind_number" class="form-control" maxlength="6" onfocus="setNumberRangeOnFocus(this,6,0,true)" />
						</div>
						<div class="form-group">
							<input type="text" class="form-control text-lowercase" onkeypress="validateStringOnKeyPress(this,event,'character');" onblur="validateStringOnBlur(this,event,'character');" id="ind_number_division" name="ind_number_division" maxlength="1" >
						</div>
						<div class="form-group super_edit_submit_icon">
							<span role='button' data-tooltip="tooltip" title="Save" class="super_edit_save" onclick="SaveIndentNumber();"><i class="fa fa-check"  aria-hidden="true" style="color: green;"></i></span>
							<span role='button' data-tooltip="tooltip" title="Cancel" class="super_edit_cancel" onclick="DiscardEditIndentNumber();"><i class="fa fa-times" aria-hidden="true" style="color: red;"></i></span>
						</div>
					</form>
					<div class="save_xsid_error_format hide">Required. Please check help tool for valid format.</div>
				</div>
				<div class="super_user_tool_tip hide"><span class="question-mark"></span></div>
				<!--<span class="super_user_tool_tip hide"><img src="/site_media/images/tooltip.png" style="vertical-align: top;" /></span>-->
			{%else%}
				<a class="btn super_user_icon" onclick="" style="color:#777;" data-tooltip="tooltip" data-placement="right" data-title="Only Super User can edit this field">
					<i class="fa fa-pencil"></i>
				</a>
			{% endif %}
			<a role="button" href="{{list_link}}" class="btn btn-add-new pull-right view_indent" data-tooltip="tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
			<span class="prev_next_container"></span>
		</div>
		<div class="container-fluid" id="container-fluid">
			<div>
				<div class="col-lg-12">
					<div class="content_bg">
						<div class="contant-container-nav hide">
							<ul class="nav nav-tabs list-inline">
								<li class="active"><a id="tab_view" href="/erp/stores/indent_list/">VIEW</a></li>
								<li><a id="tab_add" data-toggle="tab" href="#tab2">ADD</a></li>
								<li><a id="tab_add_3" data-toggle="tab" href="#tab3">ADD</a></li>
							</ul>
						</div>
						<div class="tab-content">
							<div>
								<div hidden="hidden">
									<form action="/erp/stores/indent/" method="post"
										id="edit_{{ indent_form.indent_no.value }}">{% csrf_token %}
										<a id="edit_indent_click_button" role="button" onclick="javascript:clickButton('editIndent_{{ indent_form.indent_no.value }}');">
											{{ indent.2 }}/IND/{{ indent.1 }} {{ indent_form.indent_code.value }}</a>
										<input type="hidden" value="{{ indent_form.indent_no.value }}"
											   id="id_indent_no" name="indent_no"/>
										<input type="submit" value="Edit"
											   id="editIndent_{{ indent_form.indent_no.value }}"
											   hidden="hidden"/>
									</form>
								</div>
								<form action="/erp/stores/indent/save/#tab2" method="post" id="edit_indent_form">{% csrf_token %}
										{% if indent_form.indent_no.value != '' and indent_form.indent_no.value != None %}
											<script type="text/javascript">$(".page_header_indent").html('<span class="header_current_page"> {{ indent_form.indent_code.value }}</span>');</script>
										{% else %}
											<script type="text/javascript">$(".page-title").text('Indent');</script>
										{% endif %}

								<input type="hidden" value= "{{ indent_form.project_code.value }}"  id="po_project_code" placeholder="" hidden="hidden">
								<input type="hidden" value= "{{ indent_form.indent_no.value }}"  id="po_indent_no" name="po_indent_no" placeholder="" hidden="hidden">
								<input type="hidden" value= "{{ indent_form.purpose.value }}"  id="po_purpose" placeholder="" hidden="hidden">
								<input type="hidden" value= "{{ indent_form.purchase_account_id.value }}"  id="po_intent_type" placeholder="" hidden="hidden">
								<input type="hidden" value= "{{ indent_form.instructions.value }}"  id="po_instructions" placeholder="" hidden="hidden">
								<input type="hidden" value='{{most_used_project}}' id="most_used_project_code" placeholder="" hidden="hidden">

								<div class="row">
									<div style="display:none;">
										{{ indent_form.indent_no }}{{ indent_form.indent_id }}
										{{ indent_form.enterprise_id }}{{ indent_form.financial_year }}
									</div>

									<div class="col-sm-6">
										<table border="0" class="side-table table text_box_in_table">
											<tr>
												<td class="side-header" style="width: 190px;">Indent No. & Date</td>
												<td class="side-content">{{ indent_form.indent_code.value }} - {{ indent_form.raised_date.value|date:'M d, Y' }} </td>
											</tr>
											<tr>
												<td class="side-header" style="width: 190px;">Purchase Account (Indent Type)</td>
												<td class="side-content ">{{indent_form.purchase_account_id}}</td>
											</tr>
											<tr>
												<td class="side-header" style="width: 190px;">Indent For *</td>
												<td class="side-content ">
												{% if indent_form.purpose.value != '' and indent_form.purpose.value != None %}
													<input type="text" class="form-control"
													value="{{ indent_form.purpose.value }}" id="id_indent-purpose"
													name="indent-purpose" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" />
												{% else %}
													{{ indent_form.purpose }}
												{% endif %}
												<datalist id="frequents_purpose_list">
												{% for purpose in frequents_purpose %}
													<option value="{{purpose}}">
												{% endfor %}
												</datalist>
												</td>
											</tr>
											<tr>
												<td class="side-header" style="width: 190px;">Project</td>
<!--												<td class="side-content ">{{ indent_form.project_code }}</td>-->
												<td class="side-content ">
													<div class="tour_project_tag">
														<div class="component_project" data-id="id_indent-project_code" data-name="indent-project_code" data-isSuper={{logged_in_user.is_super}} data-value="{{selected_project_code}}" data-isEditDisable="{% if status == 3 %}true{% else %}false{% endif %}"></div>
														<label id="expRev" style="display: block;margin-top: 5px;">Revenue: <span id="revenue" style="margin-right: 10px;">0</span>Expense : <span id="expense" style="margin-right: 10px;">0</span>Budget : <span id="budgetAmount" style="margin-right: 10px;">0</span></label>
														<span hidden="hidden" id="cashflow_amount" style="margin-right: 10px;">0</span><span hidden="hidden" id="cash_allocated" style="margin-right: 10px;">0</span>
													</div>
												</td>
											</tr>
											{% if indent_form.indent_no.value != '' and indent_form.indent_no.value != None %}
											<tr>
												<td class="side-header" style="width: 190px;">Last Modified</td>
												<td class="side-content ">{{ indent_form.modifier_name.value }} , {{ indent_form.modified_date.value }}</td>
											</tr>
											{% endif %}
										</table>
									</div>
									<div class="col-sm-6">
										<table border="0" class="side-table">
											<tr>
												<td class="side-header" style="width: 400px; max-width: 400px; text-align: center; font-size: 14px;">Total Purchase price (Basic Value Without Tax)</td>
												<td class="side-content text-right indent_grand_total" style="font-size: 22px;">Rs.{{ total_indent_value }} </td>
											</tr>
										</table><br><!--a href="" style="position: absolute; right: 15px; top: 46px;">Show Credit Timeline</a-->
									</div>
									<div class="col-sm-4 hide" id="tags"><!-- Tags are hidden on the 2.16.3 release -->
										<label>Tags</label>
										<label class="ui-autocomplete-loading hide">&nbsp;</label>
										<ul id="ul-tagit-display" class="tagit-display form-control">
											{% for tag_form in tags_formset.initial_forms %}
											<li class="li-tagit-display" id="{{ tag_form.prefix }}">
												<div hidden="hidden">{{ tag_form.tag }}
													{{ tag_form.DELETE }}{{ tag_form.ORDER }}</div>
												<label class="label-tagit-display"  id="id_{{tag_form.prefix}}-tag_label">{{ tag_form.tag.value }}</label>&nbsp;
												<a class="delete_tag" id="{{tag_form.prefix}}-del_button"></a>
												&nbsp;
											</li>
										{% endfor %}
											<li id="tag-__dummy__" hidden="hidden">
												<div hidden="hidden">{{ tags_formset.empty_form.tag }}
												{{ tags_formset.empty_form.ORDER }}
												{{ tags_formset.empty_form.DELETE }}</div>
												<label class="label-tagit-display"  id="id_{{tags_formset.empty_form.prefix}}-tag_label"></label>&nbsp;
												<a class="delete_tag" id="{{tags_formset.empty_form.prefix}}-del_button"></a>
												&nbsp;
											</li>

											<span>
												{{ tags_formset.management_form }}
												<span id="{{ tags_formset.empty_form.prefix }}" class="text_for_tag" >
													{{ tags_formset.empty_form.tag }}
													{{ tags_formset.empty_form.ORDER }}
													<span hidden="hidden">{{ tags_formset.empty_form.DELETE }}</span>
												</span>
											</span>
										</ul>
										<span style="position: absolute; margin-top: -8px; color: #aaa; font-size: 11px;">Type and press enter to add new tag.</span>
									</div>
									<div class="col-sm-2 hide" style="margin-top: 21px;">
										<input id="add_tag" class="btn btn-save btn-margin-1" value="+" type="button">
									</div>
									<div class="col-sm-2">
										<div class="col-sm-12" style="padding: 0;">
											<label>Required Date<span class="mandatory_mark"> *</span></label>
											{{ indent_form.expected_date }}
											{% if indent_form.raised_date.value|date:'Y-m-d' == "" %}
												<input type="text" class="form-control custom_datepicker set-my-start-date" data-setDate = "{{ indent_form.raised_date.value }}" placeholder="Select Date" id="indent_req_date" readonly="readonly">
											{% else %}
												<input type="text" class="form-control custom_datepicker set-my-start-date" data-setDate = "{{ indent_form.raised_date.value|date:'Y-m-d' }}" placeholder="Select Date" id="indent_req_date" readonly="readonly">
											{% endif %}
											<i class="glyphicon glyphicon-calendar custom-calender-icon"></i>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="form-group checkbox" style="margin-left: 13px; margin-top: -11px; position: absolute;">
										<input type="checkbox" class="chkcase" id="select_all_indent" checked="true">
										<label for="select_all_indent">Select All</label>
									</div>
									<div class="text-right" style="position: absolute; right: 15px; z-index: 100; margin-top: -10px;">
										{% if indent_form.indent_no.value != '' and indent_form.indent_no.value != None %}
										<div class="table_view_selector btn btn-default" data-view="bucket" role="button">
											<i class="fa fa-shopping-cart fa-4 b-view hide" aria-hidden="true" data-toggle="tooltip" title="Purchase View"></i>
											<i class="fa fa-list fa-4 g-view" aria-hidden="true" data-toggle="tooltip" title="Indent View"></i>
										</div>
										{% endif %}
									</div>
									<div id="indent_bucket_view" class="hide">
										<div class="col-sm-12 append_supplier_table" style="margin-top: 15px;">
											{% for supplier in supplier_buckets reversed %}
												{% if supplier.0 == "no-supplier" %}
												<ul id="other_suppliers_tab" class="nav nav-tabs">
													<li class="supplier_name_tab active"><a role="button">Others</a></li>
													<li class="info_text" style="margin: 14px 0 0 14px;">Hint: Materials without active Price Profiles will be listed here</li>
													<div class="supplier_total_amt col-sm-6 text-right remove-padding" >
														<a role="button" id="poList_{{ supplier.0}}" data-po-list-id={{ supplier.0}}  onclick="javascript:showPoList(this)"  >{{ other_po_value }} </a>
													</div>
												</ul>
												{% else %}
												<ul class="nav nav-tabs">
													<li class="supplier_name_tab active"><a role="button">{{ supplier.1 }}({{ supplier.2.0.party_code }})</a></li>
													<div class="supplier_total_amt col-sm-6 text-right remove-padding" >
														<a role="button" id="poList_{{ supplier.0}}" data-po-list-id={{ supplier.0}}  onclick="javascript:showPoList(this)"  >{{ supplier.6 }} {{ supplier.4 }}</a>
													</div>
												</ul>
												{% endif %}
												<div id={{ supplier.0}} data-supplier={{ supplier.0 }} class="temp-bom parent-supplier-container"                  data-currency={{ supplier.3 }}>
													<div class="clearfix"></div>
														{% if supplier.5.0 %}
														<div class="container_for_jo type_order_container">
															{% if supplier.1 != "NO SUPPLIERS PRICE" %}
																<div class="form-group checkbox chk-margin indent-po-checkbox">
																	<input type="checkbox" class="chkcase is_po_checked" id="job_po_creation_{{ supplier.0}}" value="1" checked="true">
																	<label for="job_po_creation_{{ supplier.0}}">&nbsp;&nbsp;Job Order</label>
																</div>
																<div class="delivery_due_container">
																	<label>Delivery Due On</label>
																	<input type="text" class="form-control hide" placeholder="Select Date" />
																	<input type="text" class="form-control custom_datepicker set-my-start-date fixed-width-medium delivery-due-date" data-setdate="" placeholder="Select Date" readonly="readonly">
																	<i class="glyphicon glyphicon-calendar custom-calender-icon" style=""></i>
																</div>
															{% endif %}

															<table class="table custom-table table-bordered tableWithText individual-supplier-bucket">
																<thead>
																	<tr>
																		<th style="width: 80px;">S.No.</th>
																		<th>MATERIAL NAME</th>
																		<th>PRICE / UNIT</th>
																		<th>PO QTY</th>
																		<th>UNIT</th>
																		<th>PRICE</th>
																		<th>Indent QTY</th>
																		<th>Prev PO QTY</th>
																		<th colspan="2">ACTIONS</th>
																	</tr>
																</thead>
																<tbody>
																  {% for material in supplier.5.0 %}
																		{% if supplier.1 == "NO SUPPLIERS PRICE" %}
																			{% if  forloop.counter == 1 %}
																				<div class="form-group col-sm-2 hide">
																					<label>Supplier<span class="mandatory_mark"> *</span></label>
																					<select class="form-control chosen-select" name="select" id="supplier">
																						<option value="">--Select Supplier--</option>
																						{% for supplier in suppliers %}
																						<option value="{{ supplier.0 }}">{{ supplier.1 }}</option>
																						{% endfor %}
																						<option id="cmdSupAdd" value="0" >+ Add new Supplier</option>
																					</select>
																				</div>
																			{% endif %}
																		{% endif %}
																		{% if material.drawing_no == ""  %}
																			<tr class="temp-bom-row" id="{{ supplier.0}}_{{ forloop.counter }}_{{material.make_id}}_jo">
																		{% else %}
																			<tr class="temp-bom-row" id="{{ supplier.0}}_{% if material.item_id %}{{ material.item_id }}{% else %}{{ material.name }}{% endif %}_{{material.make_id}}_jo">
																		{% endif %}
																			<td class="text-center">{{ forloop.counter }}.</td>
																			<td class="indent_material_name">
                                                                                {{ material.name }}{% if material.drawing_no %} - {{ material.drawing_no }} {% endif %} {% if material.make_name %} [{{ material.make_name }}] {% endif %}
                                                                                {% if material.is_service  == 1 %}
                                                                                    <span class='service-item-flag'></span>
                                                                                {% endif %}
                                                                            </td>
																			 {% if material.drawing_no == "" %}
																				<td class="text-field indent_material_no_id hidden">{{ forloop.counter }}__non_stock__</td>
																			{% else %}
																				<td class="text-field indent_material_no_id hidden">{{ material.cleaned_drawing_no }}</td>
																			{% endif %}
																			<td class="text-field indent_material_no hidden">{{ material.drawing_no }}</td>
																			<td class="text-field indent_material_make_id hidden">{{ material.make_id }}</td>
																			<td class="text-field indent_material_unit_id hidden">{{ material.unit_id }}</td>
																	  		<td class="text-field indent_material_item_id hidden">{{ material.item_id }}</td>
																	        <td class="text-field indent_material_spq hidden">{{ material.spq }}</td>
																	        <td class="text-field indent_material_moq hidden">{{ material.moq }}</td>
																			<td class="td_po_unit_price indent_unit_price textbox_amt_md">
																				<input type='text' name='purchaseprice' class='form-control text-right txt_po_unitrate' {% if is_price_modification_disabled %} disabled="disabled" {% endif %} maxlength="19" onfocus="setNumberRangeOnFocus(this,13,5)" value="{{ material.supplier_price }}" />
																			</td>
																			<td class="indent_po_qty textbox_amt_md">
																				<input type='text' id='po_quantity_{{ supplier.0}}_{{ forloop.counter }}_{{material.make_id}}_jo' name='po_quantity' 
																				class='form-control text-right txt_po_quantity' 
																				maxlength="16"
																				onfocus="setNumberRangeOnFocus(this,12,3)"
																				onblur="ValidateQuantityExceed(this,event);" onchange="validateInlineSpq(this);" 
																				data-qty="{{ material.request_qty|subtract:material.purqty }}"
																				value="{{ material.request_qty|subtract:material.purqty }}" />
																			</td>
																			<td class="text-center indent_unit textbox_amt_md">{{ material.unit_name }}</td>
																			<td class="text-right indent_total_price textbox_amt_md">
																				<input type="text" id="txtpurvalue0" name="purchasevalue" class="form-control txt_po_price text-right" value="" disabled="true" />
																			</td>
																			<td class="text-right indent_indent_qty textbox_amt_md">{{ material.request_qty }}</td>
																			<td class="text-right indent_prev_po_qty textbox_amt_md">{{ material.purqty }}</td>
																			<td class="text-center td_for_icon">
																				<img role="button" src="/site_media/images/split.png" data-tooltip="tooltip" title="Split / Move" onclick="SplitMaterial(this)" />
																			</td>
																			<td class="text-center td_disable_indent_material td_for_icon">
																				<i role="button" class="fa fa-ban" data-tooltip="tooltip" title="Exclude" onclick="disableIndentMaterial(this)" aria-hidden="true"></i>
																			</td>
																            <td class="text-field indent_material_alternate_unit_id hidden">{{ material.alternate_unit_id }}</td>
																	        <td class="text-field indent_material_scale_factor hidden">{{ material.scale_factor }}</td>
																		</tr>
																	{% endfor %}
																</tbody>
																{% if supplier.0 != "no-supplier" %}
																	<tfoot>
																		<tr>
																			<td colspan="10">
																				<div style="width: 300px; float: left; margin-right: 15px;">
																					<label>Instructions <span class="qtip-question-mark" data-tooltip="tooltip" title="Special Instructions for Party. Will be printed in the PO"></span></label>
																					<textarea rows="3" name="po-instruction" maxlength="298" id="instructions" cols="40" class="form-control" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" spellcheck="false"></textarea>
																				</div>
																				<div style="width: 300px; float: left;">
																					<label for="id_po-remarks">Remarks <span class="qtip-question-mark" data-tooltip="tooltip" title="Internal Remarks. Will not be printed in document."></span></label>
																					<textarea rows="3" name="po-remarks" maxlength="390" id="id_po-remarks" cols="40" class="form-control" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" spellcheck="false"></textarea>
																				</div>
																				<div class="payment-terms-container">
																					<label style="display: block; margin-bottom: 1px;">
																						Payment Terms
																					</label>
																					<div class="remove-left-padding">
																						<input name="txtpayment" class="form-control" maxlength="6" onfocus="setNumberRangeOnFocus(this,3,2)" onblur="validatePercentage(this, event)" placeholder="%" default="100"/>
																						(%)
																					</div>
																					<div class="remove-left-padding">
																						<input name="no_of_days" class="form-control" maxlength="6" onfocus="setNumberRangeOnFocus(this,6,0)"  placeholder="days" />
																						(in)
																					</div>
																					<div class="remove-left-padding">
																						<select class="form-control" name="pay_type" onChange="payTypeChangeEvent(this)">
																							<option value="1">No of Days</option>
																							<option value="2">Proforma Invoice</option>
																							<option value="3">Cash On Delivery</option>
																							<option value="4">Advance</option>
																						</select>

																						(against)
																					</div>
																					<div class="remove-left-padding remove-right-padding">
																						<select class="form-control" name="pay_mode">
																							<option value="1">PDC</option>
																							<option value="2">Cheque</option>
																							<option value="3">Cash</option>
																							<option value="4">DD</option>
																							<option value="5">Bank Transfer</option>
																						</select>
																						(through)
																					</div>
																				</div>
																			</td>
																		</tr>
																	</tfoot>
																{% endif %}
															</table>
														</div>
														{% endif %}


														{% if supplier.5.1 %}
														<div class="container_for_po type_order_container">
														{% if supplier.1 != "NO SUPPLIERS PRICE" %}
															<div class="form-group checkbox chk-margin indent-po-checkbox">
																<input type="checkbox" class="chkcase is_po_checked" id="purchase_po_creation_{{ supplier.0}}" value="0" checked="true">
																<label for="purchase_po_creation_{{ supplier.0}}">&nbsp;&nbsp;Purchase Order</label>
															</div>
															<div class="delivery_due_container">
																<label>Delivery Due On</label>
																<input type="text" class="form-control hide" placeholder="Select Date" />
																<input type="text" class="form-control custom_datepicker set-my-start-date fixed-width-medium delivery-due-date" data-setdate="" placeholder="Select Date" readonly="readonly">
																<i class="glyphicon glyphicon-calendar custom-calender-icon" style=""></i>
															</div>
															<table class="table custom-table table-bordered tableWithText individual-supplier-bucket">
																<thead>
																	<tr>
																		<th style="width: 80px;">S.No.</th>
																		<th>MATERIAL NAME</th>
																		<th>PRICE / UNIT</th>
																		<th>PO QTY</th>
																		<th>UNIT</th>
																		<th>PRICE</th>
																		<th>Indent QTY</th>
																		<th>Prev PO QTY</th>
																		<th colspan="2">ACTIONS</th>
																	</tr>
																</thead>
																<tbody>
																  {% for material in supplier.5.1 %}
																		{% if supplier.1 == "NO SUPPLIERS PRICE" %}
																			{% if  forloop.counter == 1 %}
																				<div class="form-group col-sm-2 hide">
																					<label>Supplier<span class="mandatory_mark"> *</span></label>
																					<select class="form-control chosen-select" name="select" id="supplier">
																						<option value="">--Select Supplier--</option>
																						{% for supplier in suppliers %}
																						<option value="{{ supplier.0 }}">{{ supplier.1 }}</option>
																						{% endfor %}
																						<option id="cmdSupAdd" value="0" >+ Add new Supplier</option>
																					</select>
																				</div>
																			{% endif %}
																		{% endif %}

																		<tr class="temp-bom-row" id="{{ supplier.0}}_{{ material.item_id }}_{{material.make_id}}_po">
																			<td class="text-center">{{ forloop.counter }}.</td>
																			<td class="indent_material_name">
                                                                                {{ material.name }} {% if material.drawing_no %} - {{ material.drawing_no }} {% endif %} {% if material.make_name %} [{{ material.make_name }}] {% endif %}
                                                                                {% if material.is_service  == 1 %}
                                                                                    <span class='service-item-flag'></span>
                                                                                {% endif %}
                                                                            </td>
																			<td class="text-field indent_material_no_id hidden">{{ material.cleaned_drawing_no }}</td>
																			<td class="text-field indent_material_no hidden">{{ material.drawing_no }}</td>
																			<td class="text-field indent_material_make_id hidden">{{ material.make_id }}</td>
																			<td class="text-field indent_material_unit_id hidden">{{ material.unit_id }}</td>
																			<td class="text-field indent_material_item_id hidden">{{ material.item_id }}</td>
																			<td class="text-field indent_material_spq hidden">{{ material.spq }}</td>
																			<td class="text-field indent_material_moq hidden">{{ material.moq }}</td>
																			<td class="td_po_unit_price indent_unit_price textbox_amt_md">
																				<input type='text' name='purchaseprice' 
																				class='form-control text-right txt_po_unitrate'
																		        {% if is_price_modification_disabled %} disabled="disabled" {% endif %}
																				maxlength="19" onfocus="setNumberRangeOnFocus(this,13,5)"
																				value="{{ material.supplier_price }}" />
																			</td>
																			<td class="indent_po_qty textbox_amt_md">
																				<input type='text' id='po_quantity_{{ supplier.0}}_{{ material.item_id }}_{{material.make_id}}_po' name='po_quantity' 
																				class='form-control text-right txt_po_quantity' 
																				data-qty={{ material.request_qty|subtract:material.purqty }}
																				maxlength="16" onfocus="setNumberRangeOnFocus(this,12,3)" 
																				onblur="ValidateQuantityExceed(this,event);" onchange="validateInlineSpq(this);" 
																				value={{ material.request_qty|subtract:material.purqty}} />
																			</td>
																			<td class="text-center indent_unit textbox_amt_md">{{ material.unit_name }}</td>
																			<td class="text-right indent_total_price textbox_amt_md">
																				<input type="text" class="form-control txt_po_price" 
																				name="purchasevalue" id="txtpurvalue0" value="" 
																				disabled="true" style="text-align:right;" />
																			</td>
																			<td class="text-right indent_indent_qty textbox_amt_md">{{ material.request_qty }}</td>
																			<td class="text-right indent_prev_po_qty textbox_amt_md">{{ material.purqty }}</td>
																			<td class="text-center td_for_icon">
																				<img role="button" src="/site_media/images/split.png"  data-tooltip="tooltip" title="Split / Move"  onclick="SplitMaterial(this)" />
																			</td>
																			<td class="text-center td_disable_indent_material td_for_icon">
																				<i role="button" class="fa fa-ban" data-tooltip="tooltip" title="Exclude" onclick="disableIndentMaterial(this)" aria-hidden="true"></i>
																			</td>
																			<td class="text-field indent_material_alternate_unit_id hidden">{{ material.alternate_unit_id }}</td>
																			<td class="text-field indent_material_scale_factor hidden">{{ material.scale_factor }}</td>
																		</tr>
																  {% endfor %}
																</tbody>
																<tfoot class="tour_bucket">
																	<tr>
																		<td colspan="10">
																			<div style="width: 300px; float: left; margin-right: 15px;">
																				<label>Instructions <span class="qtip-question-mark" data-tooltip="tooltip" title="Special Instructions for Party. Will be printed in the PO"></span></label>
																				<textarea rows="3" name="po-instruction" maxlength="298" id="instructions" cols="40" class="form-control" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" spellcheck="false"></textarea>
																			</div>
																			<div style="width: 300px; float: left;">
																				<label for="id_po-remarks">Remarks <span class="qtip-question-mark" data-tooltip="tooltip" title="Internal Remarks. Will not be printed in document."></span></label>
																				<textarea rows="3" name="po-remarks" maxlength="390" id="id_po-remarks" cols="40" class="form-control" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" spellcheck="false"></textarea>
																			</div>
																			<div class="payment-terms-container">
																				<label style="display: block; margin-bottom: 1px;">
																					Payment Terms
																				</label>
																				<div class="remove-left-padding">
																					<input name="txtpayment" class="form-control" maxlength="6" onfocus="setNumberRangeOnFocus(this,3,2)" onblur="validatePercentage(this, event)" placeholder="%" default="100"/>
																					(%)
																				</div>
																				<div class="remove-left-padding">
																					<input name="no_of_days" class="form-control" maxlength="6" onfocus="setNumberRangeOnFocus(this,6,0)"  placeholder="days" />
																					(in)
																				</div>
																				<div class="remove-left-padding">
																					<select class="form-control" name="pay_type" onChange="payTypeChangeEvent(this)">
																						<option value="1">No of Days</option>
																						<option value="2">Proforma Invoice</option>
																						<option value="3">Cash On Delivery</option>
																						<option value="4">Advance</option>
																					</select>

																					(against)
																				</div>
																				<div class="remove-left-padding remove-right-padding">
																					<select class="form-control" name="pay_mode">
																						<option value="1">PDC</option>
																						<option value="2">Cheque</option>
																						<option value="3">Cash</option>
																						<option value="4">DD</option>
																						<option value="5">Bank Transfer</option>
																					</select>
																					(through)
																				</div>
																			</div>
																		</td>
																	</tr>
																</tfoot>
															</table>
															
															{% endif %}
															</div>
														{% endif %}
												</div>
											{% endfor %}

											{% if not is_no_supplier_bucket %}
												{% if other_po_value != 0  %}
													<ul id="other_suppliers_tab_dummy" class="nav nav-tabs">
														<li class="supplier_name_tab active"><a role="button">Others</a></li>
														<li class="info_text" style="margin: 14px 0 0 14px;">Hint: Click the below link to view the list of PO's generated against the Non profiled suppliers.</li>
														<div class="supplier_total_amt col-sm-6 text-right remove-padding" >
														<a role="button" id="poList" data-po-list-id="others_drafts"  onclick="javascript:showPoList(this)"  >{{ other_po_value }} </a>
														</div>
													</ul>
												{% endif %}
											{% endif %}
										</div>
									</div>
									<div>
										<div class="col-sm-8"></div>
										<div class="col-sm-4"  id="bucket_view_buttons" style="margin-top: 30px; text-align: right;">
											{% if logged_in_user|canEdit:'PURCHASE' %}
												<button class="btn btn-save" id="create_po" type="button" >Purchase</button>
											{% else %}
												<button class="btn btn-save" type="button" style="opacity: 0.7; cursor:  not-allowed" data-tooltip="tooltip" data-title="You do not have adequate permission to do Purchase. Please contact the administrator." >Purchase</button>	
											{% endif %}
										</div>
									</div>
									<div id="indent_list_view">
										<div class="col-sm-6 checkbox" style="display:none">
												<input type="checkbox" class="chkcase" name="case" id="chknonstockable" value="" />
												<label for="chknonstockable">Non Stockable</label>
												{{indent_form.is_stockable}}
										</div>

										<div class="col-lg-12 search_result_table">
											<ul class="nav nav-tabs">
											  <li id="item_particulars" class="materials_add active"><a role="button">Item Particulars</a></li>
											  <li class="service_flag" style="margin-top: 12px"><span class='service-item-flag'></span> - Service</li>
											</ul>
											<div class="row item-particulars-add-container" id="materials_add" style="margin-bottom:0px !important;">
												<div class="col-sm-3 form-group">
													<label>Materials Requested<span class="mandatory_mark"> *</span></label>
													<input type="text" class="form-control" id="materialrequired" placeholder="Select Material" maxlength="100">
													<input type="hidden" value="" class="" id="material_id_hidden" placeholder="" hidden="hidden">
													<input type="hidden" value="" class="" id="id_ind_material-__prefix__-is_service" placeholder="" hidden="hidden">
													<input type="hidden" value="" class="" id="material_id" placeholder="" hidden="hidden">
													<input type="hidden" value="" class="" id="cat_code" placeholder="" hidden="hidden">
													<div hidden="hidden">
														{{ materials_formset.empty_form.indent_no }}
														{{ materials_formset.empty_form.enterprise_id }}
														<input type="text" value="0" hidden="hidden" id="newFormCount"/>
														{{ materials_formset.empty_form.drawing_no }}
														{{ materials_formset.empty_form.item_id }}
														{{ materials_formset.empty_form.is_service }}
														{{ materials_formset.empty_form.description }}
														{{ materials_formset.empty_form.make_label}}
													</div>
													<div class="duplicate_material_name"></div>
													<span class="material-removal-icon hide">
														<i class="fa fa-times"></i>
													</span>
												</div>

												<div class="col-sm-3">
													<label>Quantity<span class="mandatory_mark"> *</span></label>
													{{ materials_formset.empty_form.quantity }}
													<div>
														<label id="ind_unit_display" class="unit_display pull-right">&nbsp;</label>
														<div style="display:none">{{ materials_formset.empty_form.units }}</div>
													</div>
													<div class="alternate_unit_select_box hide">
														{{ materials_formset.empty_form.alternate_units }}
													</div>
													<div class="all_units_select_box hide">
														{{ materials_formset.empty_form.all_units }}
													</div>
												</div>
												<div class="col-sm-3">
													<div class="material_txt">
														<input type='button' id="add_new_ind_material" class="btn btn-save btn-margin-1" value="+"/>
														<input type='button' id="cmdshow" class="btn btn-save btn-margin-1" value="BOM"/>
													</div>
												</div>
												<div class="clearfix"></div>
											</div>
										</div>

										<div class="col-lg-12">
											<div class="row">
												<div class="table-responsive full_width_txt" id="indent_materials">
													<div class="col-sm-12">
														<a role="button" id="a-export-indent-material" class="btn btn-add-new pull-right export_ind_mat_details" style="margin-top: 18px;" onclick="GeneralExportTableToCSV.apply(this, [$('#indentMaterial'), 'Indent_Materials.csv']);" data-tooltip="tooltip" data-placement="bottom" title="Download Indent Materials List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
														<a role="button" style="margin-right: 15px; margin-bottom: 5px;margin-top: 18px;" id="short_close" class="btn btn-save download_stock_report pull-right" data-tableid="tablesorter">Short Close</a>
														<div class=" col-sm-9 form-group checkbox chk-margin chk_qty_stock pull-right hide" style="margin-bottom: 2px;">
															<div class="col-sm-8"></div>
															<div class="col-sm-4">
																<div class="col-sm-6">
																	<input type="checkbox" class="chkcase" id="load_received_qty">
																	<label for="load_received_qty">Received Qty </label>
																</div>
																<div class="col-sm-6">
																	<input type="checkbox" class="chkcase" id="load_stock_qty">
																	<label for="load_stock_qty" class="">Stock In Hand</label>
																</div>
															</div>
														</div>
														<table class="table table-bordered table-striped custom-table text_box_in_table" id="indentMaterial">
															<thead>
															<tr>
																<th style="width: 80px;">S No.</th>
																<th>Material</th>
																<th hidden="hidden" class="exclude_export">Make/Brand</th>
																<th style="width: 240px;">Quantity</th>
																<th style="width: 180px;">PO Qty</th>
																<th style="width: 180px;" class="table_received_qty hide">Received Qty</th>
																<th style="width: 180px;" class="table_stock_in_hand hide">Stock In Hand</th>
																<th style="width: 180px;">Unit</th>
																<th style="width: 80px;" class="exclude_export">Delete</th>
															</tr>
															</thead>
															<tbody class="item-for-goods">
															{{ materials_formset.management_form }}
															<tr bgcolor="#ececec" class="exclude_export" align="center" id="ind_material-__dummy__" hidden="hidden">
																<td hidden="hidden">
																	{{ materials_formset.empty_form.indent_no }}
																	{{ materials_formset.empty_form.DELETE }}
																	{{ materials_formset.empty_form.id}}
																	{{ materials_formset.empty_form.ORDER }}
																	{{ materials_formset.empty_form.enterprise_id }}
																	{{ materials_formset.empty_form.description }}
																	{{ materials_formset.empty_form.po_qty }}
																	{{ materials_formset.empty_form.make_label}}
																	{{ materials_formset.empty_form.make_id }}
																	{{ materials_formset.empty_form.item_id }}
																	{{ materials_formset.empty_form.is_service }}
																	{{ materials_formset.empty_form.alternate_unit_id }}
																	{{ materials_formset.empty_form.scale_factor }}
																<div>{{ materials_formset.empty_form.drawing_no }}</div>
																</td>
																<td class="s_no" style="text-align: center;"> <div id="id_ind_material-__prefix__-s_no"></div></td>
																<td class='text-left'><div id="id_ind_material-__prefix__-materialLabel"></div></td>
																<td hidden="hidden" class='text-left'><div id="id_ind_material-__prefix__-makeLabel"></div></td>
																<td hidden="hidden"><div id="id_ind_material-__prefix__-quantityLabel"></div></td>
																<td class="td_textbox_right">{{ materials_formset.empty_form.quantity }}</td>
																<td></td>
																<td class="table_received_qty text-right hide"></td>
																<td class="table_stock_in_hand text-right hide"></td>
																<td><div id="id_ind_material-__prefix__-units_label"></div></td>
																<td class="text-center"><a href="#" id="id_{{ materials_formset.empty_form.prefix }}-deleteIndMaterial"
																	   onclick="javascript:deleteIndMaterial('{{ materials_formset.empty_form.prefix }}')">
																	<i class="fa fa-trash-o"></i></a>
																</td>
															</tr>
															{% for material_form in materials_formset.initial_forms %}
															{% if material_form.is_service.value  = 0 %}
																<tr align="center" bgcolor="#ececec" id="{{ material_form.prefix }}" class="{{ material_form.item_id.value }}_{{ material_form.make_id.value }}" >
																	<td hidden="hidden" class="exclude_export">
																		{{ material_form.indent_no }}{{ material_form.drawing_no }}
																		{{ material_form.description }}
																		{{ material_form.po_qty }}
																		{{ material_form.units }}
																		{{ material_form.id}}{{ material_form.enterprise_id}}
																		{{ material_form.ORDER }}{{ material_form.DELETE }}
																		{{ material_form.make_id }}
																		{{ material_form.make_label}}
																		{{ material_form.item_id }}
																		{{ material_form.is_service }}
																		{{ material_form.alternate_unit_id }}
																		{{ material_form.scale_factor }}
																	</td>
																	<td class="s_no"><div id="id_{{ material_form.prefix }}-s_no">{{ forloop.counter }}</div></td>
																	<td class='text-left'><div id="id_{{ material_form.prefix }}-materialLabel">{{ material_form.description.value }} </div></td>
																	<td hidden="hidden" class='text-left exclude_export'><div id="id_{{ material_form.prefix }}-makeLabel"></div></td>
																	<td hidden="hidden"><div id="id_{{ material_form.prefix }}-quantityLabel">{{ material_form.quantity.value }}</div></td>
																	<td class="td_textbox_right exclude_export">{{ material_form.quantity }}</td>
																	<td class="text-right"><div id="id_{{ material_form.prefix }}-po_qtyLabel">{{ material_form.po_qty.value }}</div></td>
																	<td class="table_received_qty text-right hide"></td>
																	<td class="table_stock_in_hand text-right hide"></td>
																	<td><div id="id_{{ material_form.prefix }}-units_label">{{ material_form.units.value }}</div></td>
																	<td class="text-center"><a href="#" id="id_{{ material_form.prefix }}-deleteIndMaterial"
																		   onclick="javascript:deleteIndMaterial('{{ material_form.prefix }}')">
																		<i class="fa fa-trash-o"></i></a>
																	</td>
																</tr>
																{% endif %}
																{% endfor %}
																</tbody>
																<tbody class="item-for-service">
																{% for material_form in materials_formset.initial_forms %}
																{% if material_form.is_service.value  = 1 %}
																<tr align="center" bgcolor="#ececec" id="{{ material_form.prefix }}" class="{{ material_form.item_id.value }}_{{ material_form.make_id.value }}" >
																	<td hidden="hidden" class="exclude_export">
																		{{ material_form.indent_no }}{{ material_form.drawing_no }}
																		{{ material_form.description }}
																		{{ material_form.po_qty }}
																		{{ material_form.units }}
																		{{ material_form.id}}{{ material_form.enterprise_id}}
																		{{ material_form.ORDER }}{{ material_form.DELETE }}
																		{{ material_form.make_id }}
																		{{ material_form.make_label}}
																		{{ material_form.item_id }}
																		{{ material_form.is_service }}
																		{{ material_form.alternate_unit_id }}
																		{{ material_form.scale_factor }}
																	</td>
																	<td class="s_no"><div id="id_{{ material_form.prefix }}-s_no">{{ forloop.counter }}</div></td>
																	<td class='text-left'><span id="id_{{ material_form.prefix }}-materialLabel">{{ material_form.description.value }} </span>
																		{% if material_form.is_service.value  == 1 %}
																			<span class='service-item-flag'></span>
																		{% endif %}

																	</td>
																	<td hidden="hidden" class='text-left exclude_export'><div id="id_{{ material_form.prefix }}-makeLabel"></div></td>
																	<td hidden="hidden"><div id="id_{{ material_form.prefix }}-quantityLabel">{{ material_form.quantity.value }}</div></td>
																	<td class="td_textbox_right exclude_export">{{ material_form.quantity }}</td>
																	<td class="text-right"><div id="id_{{ material_form.prefix }}-po_qtyLabel">{{ material_form.po_qty.value }}</div></td>
																	<td class="table_received_qty text-right hide"></td>
																	<td class="table_stock_in_hand text-right hide"></td>
																	<td><div id="id_{{ material_form.prefix }}-units_label">{{ material_form.units.value }}</div></td>
																	<td class="text-center"><a href="#" id="id_{{ material_form.prefix }}-deleteIndMaterial"
																		   onclick="javascript:deleteIndMaterial('{{ material_form.prefix }}')">
																		<i class="fa fa-trash-o"></i></a>
																	</td>
																</tr>
																{% endif %}
																{% endfor %}
																</tbody>
															</table>
														</div>
													</div>
												</div>
											</div>

											<div class="clearfix"></div>
											<div class="col-sm-3">
												<label>Remarks</label>
												<div class="remarks_count_link remarks_count disabled" onclick="remarks_history();">
													<span class="remarks_counter">No</span><span> remarks</span>
												</div>
												{{ indent_form.instructions }}
											</div>
											<div class="col-sm-9" style="display:none;">
												{{ indent_form.raised_by }}
												{{ indent_form.raised_date }}
												{{ indent_form.modified_date }}
											</div>

											<div class="material_txt col-sm-12 btn-margin text-right" style="margin-top: -34px;">
												{% if logged_in_user|canEdit:'PURCHASE INDENT' %}
												<input type="button" class="btn btn-save" id="save_indent_button" value="Save"/>
												{% endif  %}
												<a href="{{list_link}}" class="btn btn-cancel">Back</a>
											</div>

										</div>
									</div>
								</form>
							</div>
						</div>
						<div class="row add_table" >
							<div id="catalogueModal" class="modal fade" role="dialog">
								<div class="modal-dialog modal-lg">
									<div class="modal-content">
										<div class="modal-header">
											<button type="button" class="close" data-dismiss="modal">&times;</button>
											<h4 class="modal-title">View BOM</h4>
										</div>
										<div class="modal-body">
											<table id="cattable" class="table table-bordered custom-table table-striped tableWithText">
												<thead>
													<tr align="center">
														<th>S.No</th>
														<th>Material</th>
														<th>Drawing No</th>
														<!-- <th>Make</th> -->
														<th>Qty</th>
														<th>Units</th>
														<th>Delete</th>
													</tr>
												</thead>
											</table>
										</div>
										<div id="catButton"></div>
									</div>
								</div>
							</div>
							<div class="clearfix"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
{% else %}
	<div class="right-content-container">
		<h3 class="text-center">Indent module is not available for your Enterprise. To avail this functionality, enable this section in Configurations.</h3>
	</div>
{% endif %}

<div id="error_messages" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Indent Validation</h4>
			</div>
			<div class="modal-body">
				<table cellpadding="2" cellspacing="2" border="0">
					<tr>
						<td colspan="3">
							<ul>
							{% for field in indent_form.hidden_fields %}
								{% for error in field.errors %}
									<li>{{field.label_tag}} : {{error}}</li>
								{% endfor %}
							{% endfor %}
							{% for field in indent_form.visible_fields %}
								{% for error in field.errors %}
									<li>{{field.label_tag}} : {{error}}</li>
								{% endfor %}
							{% endfor %}
							</ul>
						</td>
					</tr>
					<tr>
						<td colspan="4">
							<ul>
							{% for form in materials_formset.initial_forms %}
								{% for field in form.hidden_fields %}
									{% for error in field.errors %}
										<li>Material {{form.prefix}}:{{field.label_tag}}:{{error}}</li>
									{%endfor%}
								{% endfor %}
								{% for field in form.visible_fields %}
									{% for error in field.errors %}
										<li>Material {{form.prefix}}:{{field.label_tag}}:{{error}}</li>
									{%endfor%}
								{% endfor %}
							{% endfor %}
							{% for error in materials_formset.errors %}
								{{error}}
							{% endfor %}
							</ul>
						</td>
					</tr>
					<tr>
						<td>
							<input type="text" value='{{ indent_form.errors.as_text }}' id="form_errors" hidden="hidden"/>
							<input type="text" value='{{ materials_formset.errors.as_text }}' id="formset_errors" hidden="hidden"/>
						</td>
					</tr>
				</table>
			</div>
		</div>
	</div>
</div>

<div id="splitMaterial" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<h4  class="modal-title">
					<span id="party_name" style="display: inline-block; color: #777;"></span>
					<span id="party_name_make_id" class="hide"></span>
					<span style="font-size:16px;float: right;">
						<span id="avail_mat_supplier_name" style="padding-right: 15px;"></span>
						For <span id='avail_mat_type'></span>:
						<span id='avail_mat_qty' class="split-material-total"></span>
						<span id='avail_mat_unit'></span>
					</span>
				</h4>
			</div>
			<div class="modal-body" style="height: calc(100vh - 210px);">
				<div class="hidden_element_tag_id hide"></div>
				<table class="table custom-table table-bordered text_box_in_table" id="split_supplier_table" style="margin-bottom: 0px;">
					<thead>
						<tr>
							<th>Supplier Name *</th>
							<th style="width: 150px;">Type</th>
							<th style="width: 150px;">Price *</th>
							<th style="width: 150px;">Quantity *</th>
							<th style="width: 80px;">Action</th>
						</tr>
					</thead>
					<tbody class="add_split_container">
						<tr data-added="newly_added_supplier">
							<td style="width: 400px;">
								<select id="split_supplier_name" onchange="constructSupplierId(this);" class="chosen-select supplier_name_select" style="width: 30%; float: left; margin-right: 15px;" >
									<option value="None">--Select Supplier--</option>
								</select>
							</td>
							<td>
								<select id="supplier_type_0" onchange="constructSupplierId(this);" class="supplier_type_select">
									<option value='po'>PO</option>
        						    <option value='jo'>JO</option>
								</select>
							</td>
							<td>
								<input type="text" name="supp_material_price" class="form-control text-right split_supplier_price split_input_validation" placeholder ="Enter Price" maxlength="16" onfocus="setNumberRangeOnFocus(this,10,5)" autocomplete="off" value="">
							</td>

							<td>
								<input type="text" name="material_qty" class="form-control text-right split_supplier_total split_input_validation"  placeholder="Enter Quantity" maxlength="16" onfocus="setNumberRangeOnFocus(this,12,3)" autocomplete="off" value="" />
							</td>
							<td></td>
						</tr>
					</tbody>
				</table>
				<div style="color: #dd4b39; float: left;">* required field</div>
				<div hidden="hidden">
					<select id="other_suppliers" class="form-control chosen-select hidden" style="width: 30%; float: left; margin-right: 15px;" >
						<option value="None">--</option>
					</select>
				</div>
				<div class="text-right"><button class="btn btn-save btn-small" id="addSupplier" style="margin-bottom: 180px;">+ Add Supplier</button></div>
			</div>
			<div class="modal-footer ">
				<button type="button" class="btn btn-save" id="btn_save_split_supplier" onclick="validateSplitSupplier();">Split</button>
				<button type="button" class="btn btn-cancel" data-dismiss="modal">Cancel</button>
			</div>
		</div>
	</div>
</div>

<div id="poListmodal" class="modal fade" role="dialog" >
  	<div class="modal-dialog modal-lg">
    	<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Show PO list</h4>
      		</div>
      		<div class="modal-body">
		        <table class="table table-bordered custom-table table-striped" id ="po_list" >
						<thead>
							<tr>
								<th> S. No </th>
								<th> Draft No </th>
								<th hidden="hidden"></th>
								<th style="max-width:70px;"> Draft Date </th>
								<th> PO No </th>
								<th> PO Date </th>
								<th> Supplier </th>
								<th> PO Value </th>
								<th> Project/Tag </th>
								<th> Indent No </th>
								<th hidden="hidden" class="exclude_export"></th>
								<th> Status </th>
								<!--<th>Action</th>-->
							</tr>
						</thead>
						<tbody>
						</tbody>
				</table>
      		</div>
      		<div class="modal-footer">
      			<a class="btn btn-cancel" data-dismiss="modal">Close</a>
      		</div>
    	</div>
  	</div>
</div>

{% include "masters/add_material_modal.html" %}
{% include "masters/add_project_modal.html" %}

<script type="text/javascript">
$( document ).ready(function() {
	if($(".header_current_page").text().indexOf("New") >=0 || $(".header_current_page").length == 0){
		$(".super_user_icon").addClass('hide');
	}
	else{
		$(".super_user_icon").removeClass('hide');
	}
	if($("#id_indent_no").val() == '' || $("#id_indent_no").val() == "None" ){
		$("#indent_bucket_view").addClass('hide');
		$("#indent_list_view").removeClass('hide');
		$("#bucket_view_buttons").addClass("hide");
		$(this).addClass('hide');
		$(".export_csv").addClass('hide');
		$(".view_indent").removeClass('hide');
		$(".page-title").html('New Purchase Indent');
		$(".side-table").find('.div-disabled').removeClass('div-disabled');
		$("body").addClass("indent_add");
		$("#a-export-indent-material").remove();
	}
	if ($("#remarks_list_json") != null && $("#remarks_list_json").val() != "None") {
		displayRemarksHistory('remarks_list', $("#remarks_list_json").val(), "remarks_count")
	}
	$(".new-tour").addClass('hide');
	indentPrevNextPaging();
	nonStockUnitForDownload();
	selectAllIndent();
});

$(window).load(function(){
	actualProjectsBudget($('#id_indent-project_code').val(),$('#id_indent-project_code').find(':selected').attr('project-type'));
    $('#id_indent-project_code').change(function() {
            actualProjectsBudget($(this).val(),$('#id_indent-project_code').find(':selected').attr('project-type'));
    });
	$('.component_project label').hide();
	updateFilterText();
	$("#loading").hide();
});

function selectAllIndent() {
	$("#select_all_indent").change(function(){
		if($("#select_all_indent").is(":checked")) {
			$(".is_po_checked").prop("checked", true);
			$(".individual-supplier-bucket").find("tfoot").removeClass("hide");
		}
		else {
			$(".is_po_checked").prop("checked", false);
			$(".individual-supplier-bucket").find("tfoot").addClass("hide");
		}
		selectAllCheckboxFlag();
	});

	$(".is_po_checked").change(function(){
		selectAllCheckboxFlag();
	})
}

function selectAllCheckboxFlag(){
	if($(".is_po_checked").length == $(".is_po_checked:checked").length) {
		$("#select_all_indent").prop("checked",true);
		$("#select_all_indent").next("label").removeClass("partial");
	}
	else if($(".is_po_checked:checked").length >= 1) {
		$("#select_all_indent").next("label").addClass("partial");
	}
	else {
		$("#select_all_indent").prop("checked",false);
		$("#select_all_indent").next("label").removeClass("partial");
	}
}

function updateFilterText() {
	$(".filtered-date b").text($("#reportrange").find("span").text());
	$(".filtered-project b").text($("#projectmain option:selected").text());
}

function nonStockUnitForDownload() {
	$(".non_stock_unit_get").each(function(){
		var selectedText = $(this).find("select option:selected").text();
		var selectedUnit = selectedText.indexOf("(") + 1;
		$(this).closest("tr").find(".non_stock_unit_post").text(selectedText.slice(selectedUnit, -1));
	})
}

	function SplitMaterial(selectedItem){
	    var selectedElementTag = $(selectedItem).closest('tr').attr('id');
	    var selectedParentId = $(selectedItem).closest('.parent-supplier-container').attr('id');
	    var avail_price = $(selectedItem).closest('tr').find('.txt_po_unitrate').val();
	    var avail_po_qty = $(selectedItem).closest('tr').find('.txt_po_quantity').val();
	    var avail_unit = $(selectedItem).closest('tr').find('.indent_unit').text();
	    var avail_type = $(selectedItem).closest('.type_order_container');
	    var avail_make_id = $(selectedItem).closest("tr").find(".indent_material_make_id").text();
	    var avail_supplier_name = $(selectedItem).closest('.parent-supplier-container').prev("ul").find(".supplier_name_tab").text();
	    if(avail_type.hasClass("container_for_jo")) avail_type = "JO"; else avail_type = "PO";
	    if(selectedElementTag.indexOf("no-supplier") < 0) {
	    	$("#avail_mat_supplier_name").text(avail_supplier_name);
	    	$("#avail_mat_type").text(avail_type);
	    }
	    else {
	    	$("#avail_mat_supplier_name").text("");
	    	$("#avail_mat_type").text("");
	    }
	    $('#party_name').text($(selectedItem).closest('.temp-bom-row').find('.indent_material_name').text());
	    $('#party_name_make_id').text(avail_make_id);
	    $('#avail_mat_qty').text(avail_po_qty);
	    $("#avail_mat_unit").text(avail_unit);
	    $(".split_supplier_price").val(avail_price);
	    $('.split_supplier_total').val("0.00");
	    var drawing_no = $(selectedItem).closest('.temp-bom-row').find('.indent_material_no').text();
	    if(drawing_no.indexOf("__non_stock__") > 0) drawing_no = "";
	    var item_id = $(selectedItem).closest('.temp-bom-row').find('.indent_material_item_id').text();
	    if(drawing_no.indexOf("__non_stock__") > 0) item_id = "";
	    var make_id = $(selectedItem).closest('tr').find('.indent_material_make_id').text();
	    var type = $(selectedItem).closest('.type_order_container');
   		if(type.hasClass("container_for_po")) type = ""; else type = "JO";
        party_id =  $(selectedItem).closest('.temp-bom').attr('data-supplier');
	    $.ajax({
	        url: "/erp/stores/json/indent/loadMaterialPricedSuppliers",
	        type: "POST",
	        dataType: "json",
	        data: {'indent_no': $('#po_indent_no').val(),'item_id':item_id,'drawing_no': drawing_no,'make_id': make_id,'type':type ,'party_id':party_id},
	        success: function (response) {
	            $('#loading').hide();
	            $(".add_split_container").find("tr:gt(0)").remove();
				$("#split_supplier_name").val("").trigger("chosen:updated");
	            if(response[0].length != 0){
	                var select = document.getElementById("split_supplier_name");
	                $(".add_split_container tr:first").css("display", "none");
	                $(".add_split_container tr:gt(1)").remove();
	                select.options.length = 0;
	                var option = document.createElement('option');
	                    option.text = "--Select Supplier--";
	                    option.value = "";
	                    select.add(option, 0);
	                $.each(response[0], function (i, item) {
	                    var option = document.createElement('option');
	                    option.text = item['supplier_name']+'('+item['supplier_code']+')';
	                    option.setAttribute('data-price', item['supp_mat_price']);
	                    option.value = item['supp_id'];
	                    select.add(option, 0);
	                });
	                $('#split_supplier_name').trigger("chosen:updated");
	                $.each(response[0], function (i, item) {
	                    var supplierList = $("#split_supplier_name").html();
	                    var length = $("#split_supplier_table tbody tr").length;

	                    var select = '<select id="split_supplier_name_'+length+'" disabled="disabled" class="chosen-select supplier_name_select">'+supplierList+'</select>';

	                    var supp_price = item['supp_mat_price']
	                    console.log(item['is_service']);
	                    var is_service = item['is_service']
	                    if(is_service){
	                        is_service = 'JO'
	                    }
	                    else {
	                        is_service = 'PO'
	                    }
	                    if(is_service == "JO") {
	                    	var supplierTypeOption = "<option value='po'>PO</option>\
	                    						  <option value='jo' selected>JO</option>";
	                    }
	                    else {
	                    	var supplierTypeOption = "<option value='po' selected>PO</option>\
	                    						  <option value='jo'>JO</option>";
	                    }

	                    var supplierType = '<select id="supplier_type_'+length+'" readonly="readonly" class="supplier_type_select" >'+supplierTypeOption+'</select>';


	                    var newRow = `<tr data-currency="${item['supplier_currency']}">
	                        			<td>${select}</td>
	                        			<td>${supplierType}</td>
	                        			<td>
	                            			<input type="text" name="supp_material_price" 
	                            				class="form-control text-right split_supplier_price split_input_validation" maxlength="19"
	                            				onfocus="setNumberRangeOnFocus(this,13,5)" placeholder ="Enter Price"
	                            				autocomplete="off" value='${supp_price}' />
	                        			</td>
	                        			<td>
	                            			<input type="text" name="material_qty" 
	                            				class="form-control text-right split_supplier_total split_input_validation" maxlength="16" 
	                            				onfocus="setNumberRangeOnFocus(this,12,3)" placeholder ="Enter Quantity" 
	                            				autocomplete="off" value="0" />
	                        			</td>
	                        			<td class="text-center">
	                        				<i class="fa fa-times remove_supplier" role="button" aria-hidden="true"></i>
                        				</td>
	                    			</tr>`;

	                    $("#order_type"+length).val(is_service.toLowerCase());
	                    $(".add_split_container").append(newRow);
	                    setTimeout(function(){
	                    	if(selectedParentId == $("#split_supplier_name_"+length).val()) {
	                    		$("#split_supplier_name_"+length).closest("tr").find(".supplier_type_select").attr("disabled", "disabled");
	                    	}
	                    },10);
	                    $("#split_supplier_name_"+length).val(item['supp_id']);
	                    RemoveSupplier();
	                    $(".chosen-select").chosen();
	                });
	            }
	            else {
	            	$(".add_split_container tr:first").css("display", "");
	            	$('#split_supplier_name').html("");
	                var div_data="<option value=''>--Select Supplier--</option>";
	                $(div_data).appendTo('#split_supplier_name');
	                $.each(response[1], function (i, item) {
	                    var div_data="<option value='"+item[0]+"'>"+item[1]+'('+item[2]+')'+"</option>";
	                    $(div_data).appendTo('#split_supplier_name');
	                });
	                $('#split_supplier_name').trigger("chosen:updated");
	            }
	            if(response[2].length != 0){
	            	$('#other_suppliers').html("");
	                var div_data_others="<option value=''>--Select Supplier--</option>";
	                $(div_data_others).appendTo('#other_suppliers');
	                $.each(response[2], function (i, item) {
	                    var div_data_others="<option value='"+item[0]+"'>"+item[1]+'('+item[2]+')'+"</option>";
	                    $(div_data_others).appendTo('#other_suppliers');
	                });
	                $('#other_suppliers').trigger("chosen:updated");
	            }
	        },
	        error: function (xhr, errmsg, err) {
	            console.log(xhr.status + ": " + xhr.responseText);
	            $('#loading').hide();
	        }
	    });
	    $(".hidden_element_tag_id").text(selectedElementTag)
		$("#splitMaterial").modal('show');
	}

	function disableIndentMaterial(selectedItem){
		if($(selectedItem).closest("tr").hasClass("disabled_material")) {
			$(selectedItem).closest("tr").removeClass("disabled_material");
			$(selectedItem).attr({class: "fa fa-ban", title: 'Exclude', 'data-original-title': 'Exclude'});
			var prevVal = $(selectedItem).closest("tr").find(".txt_po_quantity").attr("disabled-value");
			$(selectedItem).closest("tr").find(".txt_po_quantity").val(prevVal).trigger("blur");
		}
		else {
			$(selectedItem).closest("tr").addClass("disabled_material");
			$(selectedItem).attr({class: "fa fa-plus", title: 'Include', 'data-original-title': 'Include'});
			var prevVal = $(selectedItem).closest("tr").find(".txt_po_quantity").val();
			$(selectedItem).closest("tr").find(".txt_po_quantity").val("0.00").attr("disabled-value", prevVal).trigger("blur");
		}
		$('[data-tooltip="tooltip"]').tooltip('destroy');
		setTimeout(function(){
			TooltipInit();
		},500);
	}

	function validateSplitSupplier(){
		$("#split_supplier_table").find(".error-border").removeClass("error-border");
		var selectlist = [];
		$("#split_supplier_table tr[data-added]").find(".supplier_name_select").each(function(){
			if($(this).closest("tr").find(".split_supplier_total ").val() > 0) {
				var classAttr = $(this).closest("tr").attr("class");
				if (typeof classAttr !== typeof undefined && classAttr !== false) {
					selectlist.push($(this).closest("tr").attr("class"));
				}
			}
		});
		var object = {};
        var result = [];

        selectlist.forEach(function (item) {
          if(!object[item])
              object[item] = 0;
            object[item] += 1;
        })

        for (var prop in object) {
           if(object[prop] >= 2) {
               result.push(prop);
           }
        }
        if(result.length >= 1){
        	swal({
                title: "Duplicate Supplier Exists!",
                text: "Do you still want to add its Quantity?",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#209be1",
                confirmButtonText: "Yes, do it!",
                closeOnConfirm: true,
                closeOnCancel: true
            },
            function(isConfirm){
                if (isConfirm) {
                	setTimeout(function(){
                		saveSplitSupplier();
                	},500);
                }
                else {
                 	$.each( result, function( index, value ){
					    $("."+value).addClass("error-duplicate-supplier");
					});
                    return;
                }
            });
        }
        else {
        	saveSplitSupplier();
        }
    }

    function saveSplitSupplier() {
        var totalQty = 0;
		var idFormValid = true;
		$("#split_supplier_table").find(".split_supplier_total:visible").each(function(){
			totalQty += Number($(this).val());
		})
		if(totalQty == 0) {
			swal("Total Quantity is Zero", "Please add quantity to any of the supplier and click SAVE.","warning");
			idFormValid = false;
		}
		else if(totalQty > new Number($("#avail_mat_qty").text())) {
			swal("","Total number of quantity is greater than available quantity. Please Check !!!","warning");
			$(".add_split_container").find("tr:visible").each(function() {
				if($(this).find(".split_supplier_total").val() > 0) {
					if($(this).find(".supplier_name_select").val() == "None" || $(this).find(".supplier_name_select").val() == "") {
						$(this).find(".supplier_name_select").next("div").find("a").addClass("error-border");
					}
				}
			});
		}
		else {
			$(".add_split_container").find("tr:visible").each(function() {
				if($(this).find(".split_supplier_total").val() > 0) {
					if($(this).find(".supplier_name_select").val() == "None" || $(this).find(".supplier_name_select").val() == "") {
						$(this).find(".supplier_name_select").next("div").find("a").addClass("error-border");
						swal("", "Supplier is required. Please fill the marked field.","warning");
						idFormValid = false;
					}
					else {
						$(this).find(".supplier_name_select").next("div").find("a").removeClass("error-border");
						var supplierId = $(this).find("select").val();
						var supplierName = $(this).find(".supplier_name_select option:selected").text();
						var supplierType = $(this).find(".supplier_type_select").val();
						var supplierPrice = $(this).find(".split_supplier_price").val();
						var supplierQty = $(this).find(".split_supplier_total").val();
						var supplierMake = $("#party_name_make_id").text();

						var split_element_id = $(".hidden_element_tag_id").text();
						var split_item_id = $("#"+split_element_id).find('.indent_material_item_id').text();
						var currently_shifted = $("#"+supplierId+"_"+split_item_id+"_"+supplierMake+"_"+supplierType);

						if($(".append_supplier_table").find("#"+supplierId).length > 0){
							if($("#"+supplierId).find(".container_for_"+supplierType).length > 0){
								$(".append_supplier_table").find("#"+supplierId).removeClass("hide");
								$("#"+supplierId).find(".container_for_"+supplierType).removeClass("hide");
								$("#"+supplierId).prev("ul").removeClass("hide");

								if($(".container_for_"+supplierType).find("#"+supplierId+"_"+split_item_id+"_"+supplierMake+"_"+supplierType).length > 0) {
									var currently_shifted = $(".container_for_"+supplierType).find("#"+supplierId+"_"+split_item_id+"_"+supplierMake+"_"+supplierType);
									var existing_po_qty = currently_shifted.find(".txt_po_quantity").val();
									var updated_po_qty = parseFloat(existing_po_qty) + parseFloat(supplierQty);
									currently_shifted.find(".txt_po_quantity").val(updated_po_qty).attr("data-qty", updated_po_qty);
									//currently_shifted.find(".indent_material_moq").text();
									setTimeout(function(){
										currently_shifted.find(".txt_po_quantity").trigger("blur");
										currently_shifted.find(".txt_po_quantity").removeClass("spq-validated");
									},10);
								}
								else {
									var content = $('#'+split_element_id).html();
									var newRow = `  <tr class='temp-bom-row' id='${supplierId}_${split_item_id}_${supplierMake}_${supplierType}'> 
														${content}
													</tr>`;
									$("#"+supplierId).find(".container_for_"+supplierType).find(".individual-supplier-bucket").find("tbody").append(newRow);
									var currently_shifted = $(".container_for_"+supplierType).find("#"+supplierId+"_"+split_item_id+"_"+supplierMake+"_"+supplierType);
									currently_shifted.find(".txt_po_unitrate").val(supplierPrice);
									currently_shifted.find(".txt_po_quantity").val(supplierQty).attr("data-qty", supplierQty);
									currently_shifted.find(".txt_po_quantity").trigger("blur");
									//currently_shifted.find(".indent_material_moq").text();
									setTimeout(function(){
										currently_shifted.find(".txt_po_quantity").trigger("blur");
										currently_shifted.find(".txt_po_quantity").removeClass("spq-validated");
										constructSno();
									},10);
								}
							}
							else {
								var OrderType;
								var OrderTypeId;
								var Ordercontainer;
								if(supplierType == 'jo'){
									OrderType = "Job Order";
									OrderTypeId = "job_po_creation_"+supplierId;
									Ordercontainer = "container_for_jo type_order_container";
									OrderValue = "1";
								}
								else {
									OrderType = "Purchase Order";
									OrderTypeId = "purchase_po_creation_"+supplierId;
									Ordercontainer = "container_for_po type_order_container";
									OrderValue = "0";
								}
								var paymentTermsRow = constructDeliveryDueDate(supplierId);
								var remarksRow = contructRemarkTextarea();
								var newOrderBucket = `<div class='${Ordercontainer}'>
														<div class="form-group checkbox chk-margin indent-po-checkbox">
								                            <input type='checkbox' class='chkcase is_po_checked' id='${OrderTypeId}' value='${OrderValue}' checked='true'>
								                            <label for='${OrderTypeId}'>&nbsp;&nbsp;${OrderType}</label>
								                        </div>
								                        ${paymentTermsRow}
													    <table class='table custom-table table-bordered tableWithText individual-supplier-bucket'>
													        <thead>
													            <tr>
													                <th style='width: 80px;'>S.No.</th>
													                <th>MATERIAL NAME</th>
													                <th>PRICE / UNIT</th>
													                <th>PO QTY</th>
													                <th>UNIT</th>
													                <th>PRICE</th>
													                <th>Indent QTY</th>
													                <th>Prev PO QTY</th>
													                <th colspan='2'>ACTIONS</th>
													            </tr>
													        </thead>
													        <tbody></tbody>
													        <tfoot>${remarksRow}</tfoot>
													    </table>
													</div>`;
								$("#"+supplierId).append(newOrderBucket);								
								var content = $('#'+split_element_id).html();
								var newRow = `	<tr class='temp-bom-row' id='${supplierId}_${split_item_id}_${supplierMake}_${supplierType}'>
													${content}
												</tr>`;
								$("#"+supplierId).find(".container_for_"+supplierType).find(".individual-supplier-bucket").find("tbody").append(newRow);
									var currently_shifted = $(".container_for_"+supplierType).find("#"+supplierId+"_"+split_item_id+"_"+supplierMake+"_"+supplierType);
									currently_shifted.find(".txt_po_unitrate").val(supplierPrice);
									currently_shifted.find(".txt_po_quantity").val(supplierQty).attr("data-qty", supplierQty);
									console.log("currently_shifted", currently_shifted)
									//currently_shifted.find(".indent_material_moq").text();
									setTimeout(function(){
										currently_shifted.find(".txt_po_quantity").trigger("blur");
										currently_shifted.find(".txt_po_quantity").removeClass("spq-validated");
										constructSno();
										CustomDatePickerInit();
										setDeliveryDueStartDate();
										TooltipInit();
										supplierBucketFooterInit();
									},10);
							}
						}
						else {
							var OrderType;
							var OrderTypeId;
							var Ordercontainer;
							var sup_currency = $(this).attr("data-currency");
							if(supplierType == 'jo'){
								OrderType = "Job Order";
								OrderTypeId = "job_po_creation_"+supplierId;
								Ordercontainer = "container_for_jo type_order_container";
								OrderValue = "1";
							}
							else {
								OrderType = "Purchase Order";
								OrderTypeId = "purchase_po_creation_"+supplierId;
								Ordercontainer = "container_for_po type_order_container";
								OrderValue = "0";
							}
							var paymentTermsRow = constructDeliveryDueDate(supplierId);
							var remarksRow = contructRemarkTextarea();
							var newBucket = `<ul class='nav nav-tabs'>
												<li class='supplier_name_tab active'><a role='button'>${supplierName}</a></li>
											</ul>
											<div id='${supplierId}' data-supplier='${supplierId}' class='temp-bom parent-supplier-container' data-currency='${sup_currency}'>
								                <div class='clearfix'></div>
							                	<div class='${Ordercontainer}'>
														<div class="form-group checkbox chk-margin indent-po-checkbox">
								                            <input type='checkbox' class='chkcase is_po_checked' id='${OrderTypeId}' value='${OrderValue}' checked='true'>
								                            <label for='${OrderTypeId}'>&nbsp;&nbsp;${OrderType}</label>
								                        </div>
								                        ${paymentTermsRow}
												    <table class='table custom-table table-bordered tableWithText individual-supplier-bucket'>
												        <thead>
												            <tr>
												                <th style='width: 80px;'>S.No.</th>
												                <th>MATERIAL NAME</th>
												                <th>PRICE / UNIT</th>
												                <th>PO QTY</th>
												                <th>UNIT</th>
												                <th>PRICE</th>
												                <th>Indent QTY</th>
												                <th>Prev PO QTY</th>
												                <th colspan='2'>ACTIONS</th>
												            </tr>
												        </thead>
												        <tbody></tbody>
												        <tfoot>${remarksRow}</tfoot>
												    </table>
											    </div>
											</div>`;
							if($("#no-supplier").length <= 0)  {
								if($("#other_suppliers_tab_dummy").length > 0) {
									$(newBucket).insertBefore("#other_suppliers_tab_dummy");
								}
								else if($("#other_suppliers_tab").length > 0) {
									$(newBucket).insertBefore("#other_suppliers_tab");
								}
								else {
									$(".append_supplier_table").append(newBucket);
								}
							}
							else {
								if($("#other_suppliers_tab_dummy").length > 0) {
									$(newBucket).insertBefore("#other_suppliers_tab_dummy");
								}
								else {
									$(newBucket).insertBefore("#other_suppliers_tab");
								}
							}
							var content = $('#'+split_element_id).html();				
							var newRow = `	<tr class='temp-bom-row' id='${supplierId}_${split_item_id}_${supplierMake}_${supplierType}'>
												${content}
											</tr>`;
							$("#"+supplierId).find(".container_for_"+supplierType).find(".individual-supplier-bucket").find("tbody").append(newRow);
							var currently_shifted = $(".container_for_"+supplierType).find("#"+supplierId+"_"+split_item_id+"_"+supplierMake+"_"+supplierType);
							currently_shifted.find(".txt_po_unitrate").val(supplierPrice);
							currently_shifted.find(".txt_po_quantity").val(supplierQty).attr("data-qty", supplierQty);
							console.log("currently_shifted", currently_shifted)
							//currently_shifted.find(".indent_material_moq").text();
							setTimeout(function(){
								currently_shifted.find(".txt_po_quantity").trigger("blur");
								currently_shifted.find(".txt_po_quantity").removeClass("spq-validated");
								constructSno();
								CustomDatePickerInit();
								setDeliveryDueStartDate();
								TooltipInit();
								supplierBucketFooterInit();
							},10);
						}

						var remainingPoQty = $("#"+split_element_id).find(".txt_po_quantity").attr('data-qty');
						var updated_po_qty = parseFloat(remainingPoQty) - parseFloat(supplierQty);
						if(updated_po_qty <= 0) {
							$("#"+split_element_id).remove();
						}
						else {
							$("#"+split_element_id).find(".txt_po_quantity").val(updated_po_qty.toFixed(3)).attr('data-qty', updated_po_qty);
							$("#"+split_element_id).find(".txt_po_unitrate").trigger('blur');
							$("#"+split_element_id).find(".txt_po_quantity").removeClass("spq-validated");
						}
					}
				}
			});
			if(idFormValid){
				$(".hidden_element_tag").html('');
				$(".parent-supplier-container").each(function(){
					if($(this).find(".temp-bom-row").length <= 0){
						$(this).addClass("hide");
						$(this).prev("ul").addClass("hide");
					}
					$(this).find(".type_order_container").each(function(){
						if($(this).find(".temp-bom-row").length <= 0){
							$(this).addClass("hide");
						}
					});
				});
				$("#splitMaterial").modal('hide');
				$(".add_split_container").find("tr:gt(0)").remove();
				$("#split_supplier_name").val("").trigger("chosen:updated");;
				PoQuantityPriceUpdate();
			}
		}
	}

	function constructSno() {
		$(".append_supplier_table .individual-supplier-bucket").each(function(){
			var sno = 1;
			$(this).find("tbody tr").each(function(){
				$(this).find("td:first-child").text(sno++);
			});
		});
	}

	function constructDeliveryDueDate(supplierId) {
		var requiredDate = $("#id_indent-expected_date").val();
		var row = `<div class="delivery_due_container">
						<label>Delivery Due On</label>
						<input type="text" class="form-control daterange-single hide" placeholder="Select Date" value="${requiredDate}" />
						<input type="text" id="delivery_due_${supplierId}" class="form-control custom_datepicker set-my-start-date fixed-width-medium delivery-due-date" data-setdate="" placeholder="Select Date" readonly="readonly">
						<i class="glyphicon glyphicon-calendar custom-calender-icon" style=""></i>
					</div>`;
		return row;			
	}

	function contructRemarkTextarea() {
		var row = `	<tfoot>
						<tr>
							<td colspan="10" class="purchase_bucket_view">
								<div style="width: 300px; float: left; margin-right: 15px;">
									<label>Instructions <span class="qtip-question-mark" data-tooltip="tooltip" title="Special Instructions for Party. Will be printed in the PO"></span></label>
									<textarea rows="3" name="po-instruction" maxlength="298" id="instructions" cols="40" class="form-control" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" spellcheck="false"></textarea>
								</div>
								<div style="width: 300px; float: left;">
									<label for="id_po-remarks">Remarks <span class="qtip-question-mark" data-tooltip="tooltip" title="Internal Remarks. Will not be printed in document."></span></label>
									<textarea rows="3" name="po-remarks" maxlength="390" id="id_po-remarks" cols="40" class="form-control" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" spellcheck="false"></textarea>
								</div>
								<div class="payment-terms-container">
									<label style="display: block; margin-bottom: 1px;">	
										Payment Terms
									</label>
									<div class="remove-left-padding">
										<input name="txtpayment" class="form-control" maxlength="6" onfocus="setNumberRangeOnFocus(this,3,2)" onblur="validatePercentage(this, event)" placeholder="%" default="100"/>
										(%)
									</div>
									<div class="remove-left-padding">
										<input name="no_of_days" class="form-control" maxlength="6" onfocus="setNumberRangeOnFocus(this,6,0)"  placeholder="days" />
										(in)
									</div>
									<div class="remove-left-padding">
										<select class="form-control" name="pay_type" onChange="payTypeChangeEvent(this)">
											<option value="1">No of Days</option>
											<option value="2">Proforma Invoice</option>
											<option value="3">Cash On Delivery</option>
											<option value="4">Advance</option>
										</select>
										
										(against)
									</div>
									<div class="remove-left-padding remove-right-padding">
										<select class="form-control" name="pay_mode">
											<option value="1">PDC</option>
											<option value="2">Cheque</option>
											<option value="3">Cash</option>
											<option value="4">DD</option>
											<option value="5">Bank Transfer</option>
										</select>
										(through)
									</div>
								</div>
							</td>
						</tr>
					</tfoot>`;
		return row;			
	}

	function supplierBucketFooterInit() {
		$(".is_po_checked").change(function(){
			$(this).is(":checked")
			if($(this).is(":checked")) {
				$(this).closest(".type_order_container").find(".individual-supplier-bucket").find("tfoot").removeClass("hide")	
			}
			else {
				$(this).closest(".type_order_container").find(".individual-supplier-bucket").find("tfoot").addClass("hide")		
			}
		})
	}

	function setDeliveryDueStartDate(){
		var currentDate = new Date();
		$(".delivery-due-date").each(function(){
			$(this).attr("data-setdate", currentDate);
		})
	}


</script>
<script>
	$(window).load(function(){
	var oTable;
	var oSettings;
	var hash = window.location.hash;
	hash && $('ul.nav a[href="' + hash + '"]').tab('show');

	$('.nav-tabs a').click(function (e) {
		$(this).tab('show');
		var scrollmem = $('body').scrollTop() || $('html').scrollTop();
		window.location.hash = this.hash;
		$('html,body').scrollTop(scrollmem);
	});
	var url = window.location.href;
	$(".view_indent").removeClass('hide');
	if($(".header_current_page").text() == "") {
		$("#indent_bucket_view, #bucket_view_buttons").addClass('hide');
		$("#indent_list_view").removeClass('hide');
		$(".export_ind_mat_details").addClass("hide");
		$("#select_all_indent").closest(".checkbox").addClass('hide');
	}
	else {
		$("#indent_bucket_view").removeClass('hide');
		$("#indent_list_view").addClass('hide');
		$("#select_all_indent").closest(".checkbox").removeClass('hide');
	}
	$(".create_indent, .export_csv").addClass('hide');
	if($("#is_super_user").val().toLowerCase() == 'true') {
		IndentSuperEditInit();
	}
	});
	$("#add_new_ind_material, #cmdshow").click(function(){
		$(".error-border").removeClass('error-border');
	    $(".custom-error-message").remove();

	    var ControlCollections = [
	        {
	            controltype: 'textbox',
	            controlid: 'materialrequired',
	            isrequired: true,
	            errormsg: 'Material Name is required.'
	        },
	       
			{
				controltype: 'textbox',
				controlid: 'id_ind_material-__prefix__-quantity',
				isrequired: true,
				errormsg: 'Quantity is required.',
                mindigit: 0.01,
                mindigiterrormsg: 'Quantity cannot be 0.'
			}
	    ];
	    var result = JSCustomValidator.JSvalidate(ControlCollections);
		//return result;
	});


</script>
<script>
	$(function() {
    $('#cattable').on('click', '.toggle', function () {
        //Gets all <tr>'s  of greater depth
        //below element in the table
        var findChildren = function (tr) {
            var depth = tr.data('depth');
            return tr.nextUntil($('tr').filter(function () {
                return $(this).data('depth') <= depth;
            }));
        };

        var el = $(this);
        var tr = el.closest('tr'); //Get <tr> parent of toggle button
        var children = findChildren(tr);

        //Remove already collapsed nodes from children so that we don't
        //make them visible.
        //(Confused? Remove this code and close Item 2, close Item 1
        //then open Item 1 again, then you will understand)
        var subnodes = children.filter('.expand');
        subnodes.each(function () {
            var subnode = $(this);
            var subnodeChildren = findChildren(subnode);
            children = children.not(subnodeChildren);
        });

        //Change icon and hide/show children
        if (tr.hasClass('collapse')) {
            tr.removeClass('collapse').addClass('expand');
            children.hide();
        } else {
            tr.removeClass('expand').addClass('collapse');
            children.show();
        }
        return children;
    });
});

function indentPrevNextPaging() {
	if($(".header_current_page").text().indexOf("New") >=0 || $(".header_current_page").text() == "") {
		$(".prev_next_container").remove();
	}
	else {
		var IndentListNav = JSON.parse(localStorage.getItem('indentListNav'));
		if(IndentListNav != null) {
			var curIndentId = $("#id_indent_no").val();
			for (var i = 0; i < IndentListNav.length; i++){
			  if (IndentListNav[i].indentId == curIndentId){
			  	if(i != 0) {
				    var prevIndentId = IndentListNav[i-1].indentId;
				    var prevIndentNo = IndentListNav[i-1].indentNumber;
				}
				if(i != Number(IndentListNav.length - 1)) {
			     	var nextIndentId = IndentListNav[i+1].indentId;
			     	var nextIndentNo = IndentListNav[i+1].indentNumber;
			     }
			  }
			}
			var PrevNextIndent = "";
			if(prevIndentId) {
				PrevNextIndent += '<form id="indent_edit_'+prevIndentId+'" method="post" action="/erp/stores/indent/">\
										<div style="display:none"><input type="hidden" name="csrfmiddlewaretoken" value="'+getCookie('csrftoken')+'"></div>\
										<a role="button" onclick="javascript:clickButton(\'editIndent_'+prevIndentId+'\');" class="btn btn-add-new pull-right btn-data-tooltip" data-tooltip="tooltip" title="Prev. Indent: '+prevIndentNo+'" style="margin-right: 7px;"><i class="fa fa-backward" aria-hidden="true"></i></a>\
										<input type="hidden" value="'+prevIndentId+'" name="indent_no">\
										<input type="submit" value="Edit" id="editIndent_'+prevIndentId+'" hidden="hidden">\
									</form>';
			}
			else {
				PrevNextIndent += '<form><a class="btn btn-add-new pull-right disabled btn-data-tooltip" data-tooltip="tooltip" title="Next/ Prev will appear only for the current page" style="padding: 5px 12px; margin-right: 7px;"><i class="fa fa-backward" aria-hidden="true"></i></a></form>';
			}
			if(nextIndentId) {
				PrevNextIndent += '<form id="indent_edit_'+nextIndentId+'" method="post" action="/erp/stores/indent/">\
										<div style="display:none"><input type="hidden" name="csrfmiddlewaretoken" value="'+getCookie('csrftoken')+'"></div>\
										<a role="button" onclick="javascript:clickButton(\'editIndent_'+nextIndentId+'\');" class="btn btn-add-new pull-right btn-data-tooltip" data-tooltip="tooltip" title="Next Indent: '+nextIndentNo+'" style="margin-right: 7px;"><i class="fa fa-forward" aria-hidden="true"></i></a>\
										<input type="hidden" value="'+nextIndentId+'" name="indent_no">\
										<input type="submit" value="Edit" id="editIndent_'+nextIndentId+'" hidden="hidden">\
									</form>';
			}
			else {
				PrevNextIndent += '<form><a class="btn btn-add-new pull-right disabled btn-data-tooltip" data-tooltip="tooltip" title="Next/ Prev will appear only for the current page" style="padding: 5px 12px; margin-right: 7px;"><i class="fa fa-forward" aria-hidden="true"></i></a></form>';
			}
			$(".prev_next_container").html(PrevNextIndent);
			TooltipInit();
		} 
	}
}

$(document).ready(function(){
	$(".chosen-select").chosen();
	supplierBucketFooterInit();
	setDeliveryDueStartDate();
	$('[data-toggle="tooltip"]').tooltip({
		trigger : 'hover'
	}); 
});

$("#supplier_name_1").chosen().change(function() {
	$("#indent_qty_1").focus();
});

$("#indent_qty_1").blur(function(){
	var cUnitPrice = $('.indent_unitPrice').text();
	var cQty = $('.indent_qty').find('input').val();
	$('.indent_price').text(Number(cUnitPrice*cQty).toFixed(2));
});

$(".table_view_selector").click(function(){
	if($(this).attr('data-view') == 'bucket') {
		$(this).attr('data-view', 'grid');
		$(this).find('.g-view').addClass("hide");
		$(this).find('.b-view').removeClass("hide");
		$("#indent_bucket_view").addClass('hide');
		$("#indent_list_view").removeClass('hide');
		$("#bucket_view_buttons").addClass("hide");
		$("#select_all_indent").closest(".checkbox").addClass('hide');
		$('.chk_qty_stock').removeClass('hide');
	}
	else if($(this).attr('data-view') == 'grid') {
		$(this).attr('data-view', 'bucket');
		$(this).find('.b-view').addClass("hide");
		$(this).find('.g-view').removeClass("hide");
		$("#indent_bucket_view").removeClass('hide');
		$("#indent_list_view").addClass('hide');
		$("#bucket_view_buttons").removeClass("hide");
		$("#select_all_indent").closest(".checkbox").removeClass('hide');
	}
});

$("#create_po").click(function(){
	var project_type = $('#id_indent-project_code').find(':selected').attr('project-type');
	if (project_type == "Secondary"){
<!--		if (parseFloat($('#expense').text()) >= parseFloat($('#budgetAmount').text())) {-->
<!--			swal("", "Budget Amount Exceeds.", "error");-->
<!--			return;-->
<!--		}-->
		if (parseFloat($('#cashflow_amount').text()) < parseFloat($('#cash_allocated').text())) {
			swal("", "* Actual Cash Flow Amount Exceeds. ", "error");
			return;
		}
    }
	$("#loading").show();
	setTimeout(function(){
   		constructPO();
   	},25);
});

function constructSupplierId(selectedSup) {
	var supName = $(selectedSup).closest("tr").find(".supplier_name_select").val();
	var supType = $(selectedSup).closest("tr").find(".supplier_type_select").val();
	if(supName != "") {
		$(selectedSup).closest("tr").attr("class", supName+"_"+supType);
		$.ajax({
            url: "/erp/purchase/json/po/load_party_currency/",
            type: "post",
            datatype: "json",
            data: {party_id:supName},
            success: function(response) {
                $(selectedSup).closest('tr').attr('data-currency',response);
            }
        });
	}
	else {
		$(selectedSup).closest("tr").removeAttr("class");
		$(selectedSup).closest('tr').removeAttr('data-currency');
	}
}

$("#addSupplier").click(function(){
	var supplierList = $("#other_suppliers").html();
	var length = $("#split_supplier_table tbody tr").length;
	var selectSupplier = '<select id="other_suppliers_'+length+'" onchange="constructSupplierId(this);" class="chosen-select supplier_name_select">'+supplierList+'</select>';
	var selectType = '<select id="order_type_'+length+'" style="width: 100%;" onchange="constructSupplierId(this);" class="supplier_type_select"><option value="po">PO</option><option value="jo">JO</option></select>';
	var newRow =`<tr data-added="newly_added_supplier">
					<td>${selectSupplier}</td>
					<td>${selectType}</td>
					<td>
						<input type="text" name="supp_material_price" class="form-control text-right split_supplier_price split_input_validation" 
						maxlength="16" onfocus="setNumberRangeOnFocus(this,10,5)" placeholder ="Enter Price" autocomplete="off" value="0.00" />
					</td>
					<td>
						<input type="text" name="material_qty" class="form-control text-right split_supplier_total split_input_validation" 
						maxlength="16" onfocus="setNumberRangeOnFocus(this,12,3)" placeholder ="Enter Quantity" autocomplete="off" value="0.00" />
					</td>
					<td class="text-center">
						<i class="fa fa-times remove_supplier" role="button" aria-hidden="true"></i>
					</td>
				</tr>`;
	$(".add_split_container").append(newRow);
	$(".chosen-select").chosen();
	RemoveSupplier();
});

function RemoveSupplier(){
	$(".remove_supplier").click(function(){
		$(this).closest("tr").remove();
	})
}

function ValidateQuantityExceed(el, evt){
if((el.value) < 1)
{
swal({title: "", text: "PO Quantity should not be zero", type: "error"});
}
else if((Number(el.value)) > Number(el.getAttribute("data-qty"))) {
	swal({title: "", text: "PO Quantity ("+ el.value + ") is greater than Indent Quantity", type: "error"});
<!--		el.value = Number(el.getAttribute("data-qty")).toFixed(3);-->
el.value = Number(el.getAttribute("data-qty")).toFixed(3);
		$(el).removeClass('spq-validated');
	}
}

function validateInlineSpq(current){
if((current.value) < 1 )
{
swal({title: "", text: "PO Quantity should not be zero", type: "error"});
}
else if((current.value) < Number(current.getAttribute('data-qty')))
	{
		setTimeout(function(){
		$(current).closest("tr").find(`.btn-spq`).remove();
	    var currentSpqValue = $(current).closest("tr").find(`.indent_material_spq`).text();
	    var currentMoqValue = $(current).closest("tr").find(`.indent_material_moq`).text();
	    var currentQty = $(current).val();
	    var currentUnit = $(current).closest("tr").find(".indent_unit").text();
	    var currentElementId = $(current).attr("id");
	    var currentTrId = $(current).closest("tr").attr("id");
	    confirmSpqValueFromClient(currentSpqValue, currentMoqValue, currentQty, currentUnit, currentElementId, currentTrId, 'edit');
	},10);
}
}

function confirmSpqValueFromClient(spqValue, moqValue, qty, unit, current, currentTr, type){
	if($("#"+currentTr).find(".indent_material_alternate_unit_id").text().trim() != 0)
	 {
		alternateUnitValue = $("#"+currentTr).find(".indent_material_scale_factor").text().trim();
		unit = $("#"+currentTr).find(".indent_unit").text().trim();
		spqValue = spqValue / alternateUnitValue;
	}
	var checkMod = (qty / spqValue).toFixed(3);
	var ceilValue = spq_round_ceil(spqValue, qty);
	var floorValue = spq_round_floor(spqValue, qty);
	if (!Number.isInteger(Number(checkMod)) && spqValue > 0){
		
		var swalText = `<span class='btn-custom-swal' onclick="setSpqValue(${floorValue}, ${qty}, '${current}', '${currentTr}', '${type}');">
							${floorValue}<small>(Round-down to previous SQP)</small>
						</span>`;
		{% if logged_in_user|canApprove:'PURCHASE' %}
			swalText += `<span class='btn-custom-swal' onclick="setSpqValue(${qty}, ${qty}, '${current}', '${currentTr}', '${type}');">
							${Number(qty).toFixed(3)} <small>(Use As-Is)</small>
						</span>`;
		{% endif %}
		swalText += `<span class='btn-custom-swal' onclick="setSpqValue(${ceilValue}, ${ceilValue}, '${current}', '${currentTr}', '${type}');">
						${ceilValue} <small>(Round-up to next SQP)</small>
					</span>`;
					console.log(ceilValue, moqValue)
		if(Number(ceilValue) < Number(moqValue) ) {
			swalText += `<span class='btn-custom-swal' onclick="setSpqValue(${moqValue}, ${moqValue}, '${current}', '${currentTr}', '${type}');">
							${moqValue}<small>(MOQ Value)</small>
						</span>`;
		}
        orderQtyCheckSwal(swalText, qty, unit);
    }
    else if(Number(qty) < Number(moqValue)) {
    	var swalText = `<span class='btn-custom-swal' onclick="setSpqValue(${qty}, ${qty}, '${current}', '${currentTr}', '${type}');">${Number(qty).toFixed(3)}<small>(Use As-Is)</small></span>`;
		swalText += `<span class='btn-custom-swal' onclick="setSpqValue(${moqValue}, ${moqValue}, '${current}', '${currentTr}', '${type}');">${moqValue}<small>(MOQ Value)</small></span>`;
		orderQtyCheckSwal(swalText, qty, unit);
    }
}

function orderQtyCheckSwal(text, qty, unit) {
	swal({
        title: `<h4 style='line-height:23px;'>Order Quantity specified <b>(${Number(qty).toFixed(3)} ${unit})</b> does not match with at least one constraint!<br /></h4>
        	    <h5 style='margin-top: 15px;'>Choose Appropriate Quantity</h5>`,
        text: text,
        type: "warning",
        showCancelButton: false,
        showConfirmButton: false,
        closeOnConfirm: true,
        allowOutsideClick: false,
        allowEscapeKey: false,
        customClass: 'swal-wide'
    });
}

function setSpqValue(value, maxValue, current, currentTr, type) {
if(value < 1)
{
swal({title: "", text: "PO Quantity should not be zero", type: "error"});
}
else if((value) < Number($("#"+current).attr('data-qty')))
{
 	swal.close();
	if(type == "edit") {
		setTimeout(function(){
        	$("#"+currentTr).find("#"+current).val(Number(value).toFixed(3)).addClass("spq-validated");
        },10);
	}
}
	else
	{
	swal({title: "", text: "PO Quantity ("+ value + ") is greater than Indent Quantity", type: "error"});

	}

}

function spq_round_ceil(spq_value, input_value) {
    return (Math.ceil(input_value / spq_value) * spq_value).toFixed(3);
}

function spq_round_floor(spq_value, input_value) {
    return (Math.floor(input_value / spq_value) * spq_value).toFixed(3);
}

function IndentSuperEditInit(){
	if($(".header_current_page").text() == ""  || $(".header_current_page").text().indexOf("New") >=0) {
		$(".super_user_icon, .xsid_number_edit, .super_user_tool_tip").remove();
	}
	else {
		$(".super_user_icon, .super_user_tool_tip").removeClass("hide");
		$(".side-table").find('.div-disabled').removeClass('div-disabled');
		$("#id_indent-purpose").removeAttr("disabled");
	}
	$('.super_user_tool_tip span').qtip({
	   content: {
	        text: "<ul class='ul_for_tool_tip'><li>Super-edit enables modifying the Indent Code subject to Duplication Check.</li>\
	        		   <li>Code format will be 'FY-FY/IND/NNNNNNx', <br />eg. '18-19/IND/000731b'.<br />\
	        		   FY details - 5 characters (max), <br />Indent Type - 3 characters, <br />Number - 6 digits (max), <br />Sub-number - 1 alphabet (max).</li>\
	        		   <li>Subsequent numbering of Indent will pick from the highest of the Indent Code, <br/> i.e., gaps will not be filled automatically.</li></ul>",

	        title: 'Super-Edit'
	    }
	});
}

function EditIndentNumber() {
	var indentNumber = $(".header_current_page").text().trim();
	var indentNumberSplit = indentNumber.split("/");
	$("#ind_financial_year").val(indentNumberSplit[0]);
	$("#ind_type").val(indentNumberSplit[1]);
	if($.isNumeric( indentNumberSplit[2].substr(-1) )){
		$("#ind_number").val(indentNumberSplit[2]);	
	}
	else {
		$("#ind_number").val(indentNumberSplit[2].slice(0, -1));
		$("#ind_number_division").val(indentNumberSplit[2].substr(-1));
	}
	$(".xsid_number_edit").removeClass("hide");
	$(".super_user_icon, .header_current_page").addClass("hide")
}

function DiscardEditIndentNumber(){
	$(".xsid_number_edit").addClass("hide");
	$(".super_user_icon, .header_current_page").removeClass("hide")
}

function SaveIndentNumber(){
	if($("#ind_financial_year").val() =="" || $("#ind_number").val() == "") {
		$(".save_xsid_error_format").removeClass("hide");
		if($("#ind_financial_year").val() == "") $("#ind_financial_year").addClass("super_edit_error_border");
		if($("#ind_number").val() == "") $("#ind_number").addClass("super_edit_error_border");
		//if($("#ind_type").val() == "") $("#ind_type").addClass("super_edit_error_border");
	}
	else {
		$(".save_xsid_error_format").addClass("hide");
		$("#ind_number_division").val($("#ind_number_division").val().toLowerCase());
		$.ajax({
			url: "erp/stores/json/super_edit_indent_code/",
			method: "POST",
			data:{
				indent_no: $("#id_indent_no").val(),
				new_financial_year: $("#ind_financial_year").val().trim(),
				new_indent_id: $("#ind_number").val().trim(),
				new_sub_number: $("#ind_number_division").val().trim()
			},
			success: function(response) {
				if (response.response_message == "Success") {
					ga('send', 'event', 'Indent', 'Super-Edit Code', $('#enterprise_label').val(), 1);
					swal({title: "", text: response.custom_message, type: "success"});
					DiscardEditIndentNumber();
					$(".header_current_page, title").text(response.code);
				} else {
					swal({title: "", text: response.custom_message, type: "warning"});
				}
			},
			error: function (xhr, errmsg, err) {
	            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
	        }
		});
	}
}

function payTypeChangeEvent(current) {
	if ($(current).val() == 1){
        $(current).closest(".payment-terms-container").find("input[name='no_of_days']").attr("disabled", false);
    }
    else {
       	$(current).closest(".payment-terms-container").find("input[name='no_of_days']").val("").attr("disabled", true);
    }
}
</script>
{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
{% endblock %}