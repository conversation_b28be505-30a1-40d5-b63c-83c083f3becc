{% extends "stores/sidebar.html" %}
{% block indent %}
{% if logged_in_user|canView:'STORES' %}

<link rel="stylesheet" href="/site_media/css/manufacturing_indent.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/load_material.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/add_material.js?v={{ current_version }}"></script>
<style>
	#id_transfer-remarks {
		height: 100px;
	}

	#cattable tr[data-toggle='open'] td:nth-child(11){
		opacity: 0.5;
		background: #ddd;
		pointer-events: none;
	}

	#cattable tr[data-toggle='open'] input,
	#cattable tr[data-toggle='open'] select	{
		opacity: 0.5;
		background: #ddd;
	}

	.side-content.div-disabled {
		padding: 0;
		background-color: transparent !important;
	}

	.side-content.div-disabled a,
	.side-content.div-disabled div,
	.side-content.div-disabled input {
		border: none;
		outline: none;
		box-shadow: 0 0;
		height: 30px;
		padding-left: 3px;
		background-color: transparent !important;
		font-size: 13px;
		color: #333;
	}

	.side-content.div-disabled input {
		padding-left: 6px;
	}

	.side-content.div-disabled .chosen-single div {
		display: none;
	}

	.side-content.div-disabled .chosen-single span {
		margin-top: 2px;
	}

	.table.text_box_in_table .chosen-single {
		height: 26px;
	    font-size: 13px;
	    line-height: 25px;
	    padding: 0 0 0 3px;
	}

	.table.text_box_in_table .chosen-container-single .chosen-single div b {
		margin-top: 2px;
	}

	.supplier_total_amt {
		font-size: 20px;
		float: right;
		margin-top: 15px;
	}

	.parent-supplier-container {
	    padding: 6px 12px;
	    border: solid 1px #ccc;
	    border-top: none;
	    margin-bottom: 15px;
	}

	.supplier_name_tab a{
		color:  #004195 !important;
		font-size: 16px;
	}

	.chosen-select[readonly='readonly']+div  {
		pointer-events: none;
	}

	.supplier_type_select {
		width:  100%;
		border-radius: 4px;
    	border-color: #ccc;
	}

	.disabled_material {
		text-decoration: line-through;
	}

	.disabled_material td {
		background: rgba(0,0,0,0.1);
	    opacity: 0.7;
	    pointer-events: none;
	}

	.disabled_material input {
		background: transparent;
	}

	.disabled_material td:last-child {
		background: transparent;
		opacity: 1;
		pointer-events: inherit;
		color: #000 !important;
	}

	.td_for_icon .fa.fa-ban,
	.td_for_icon .fa.fa-plus {
		font-size: 18px;
	    margin-top: 3px;
	}

	.side-content .custom-error-message {
		margin-top: -24px;
    	right: 26px;
	}

	.error-duplicate-supplier {
		background: rgba(249, 255, 81,0.35);
	}

	.error-duplicate-supplier .chosen-container {
		box-shadow: 0 0;
	}

	.error-duplicate-supplier input,
	.error-duplicate-supplier select,
	.error-duplicate-supplier a span{
		background: rgba(221, 75, 57,0.00);
	}

	.add_split_container .chosen-disabled {
		opacity: 1 !important;
	    box-shadow: 0 0;
	}

	.add_split_container .chosen-disabled a {
		border:  none;
	}

	.add_split_container .chosen-disabled b {
		display: none !important;
	}

	.add_split_container select[disabled] {
		-webkit-appearance: none;
	    -moz-appearance: none;
	    text-indent: 1px;
	    text-overflow: '';
	    background: transparent;
	}

	.loading-main-container {
		display: block;
	}

	.payment-terms-container {
		float: left;
	    width: 460px;
	    margin-left: 15px;
	    margin-top: 9px;
	}

	.payment-terms-container div {
		float: left;
	}

	.payment-terms-container div:nth-child(2) input {
		border-radius: 4px 0 0 4px !important;
	}

	.payment-terms-container div:last-child select{
		border-radius: 0 4px 4px 0 !important;
	}

	.payment-terms-container input {
		width: 90px;
	}

	.payment-terms-container select {
		width: 140px;
	}

	.table .payment-terms-container div input,
	.table .payment-terms-container div select {
		padding: 4px 12px 6px;
	    height: 34px;
	    font-size: 14px;
	    border-radius: 0;
	    margin-left: -1px;
	}

	.delivery_due_container {
		float: right;
	    margin-bottom: 5px;
	    margin-top: -5px;
	}

	.delivery_due_container input {
		padding: 10px;
	    height: 26px;
	    font-size: 12px;
	    padding-left: 45px;
	}

	.delivery_due_container i {
		font-size: 12px;
	    padding: 0px 12px;
	    height: 24px;
	    margin-top: -26px;
	    margin-left: 1px;
	}

	.delivery_due_container i:before {
		margin-top: 6px !important;
	}

	.tour_bucket{
		background:transparent;
	}
	#cattable tbody tr td .tree-view {
		position: relative;
	}

	#cattable tbody tr td .tree-view:before {
	    border-bottom: 1px solid #bbb;
	    content: "";
	    left: 6px;
	    position: absolute;
	    top: 6px;
	    width: 1.5em;
	}

	#cattable tbody tr td .tree-view:after {
	    border-left: 1px solid #bbb;
	    content: "";
	    left: 6px;
	    position: absolute;
	    top: -25px;
	    height: 4em;
	}

	#cattable .fa-plus:before,
	#cattable .fa-minus:before {
	    border: solid 1px #333;
	    padding: 2px 3px 2px 3px;
	    border-radius: 3px;
	    font-size: 10px;
	}
	.disabled {
		opacity: 0.5;
		pointer-events: none;
	}
<!--	#enable_edit_voucher {-->
<!--		position: absolute;-->
<!--	    right: 30px;-->
<!--	    margin-top: 50px;-->
<!--	    z-index: 100;-->
<!--	}-->

</style>

<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/utility.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/make.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/tags.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/stocktransfer.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/Component/locations.js?v={{ current_version }}"></script>


	<div class="right-content-container">

	<!-- loading message ----- along with loading process image -->
		<div id='loadingmessage_changelog_listing_ie' style="text-align:center;position: absolute;z-index: 1;top: 50%;left: 50%;display:none">
			<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' width="100">
			<br /> Please wait...
		</div>

	<!--  heading of the page -->
		<div class="page-title-container">
			<span class="page-title">Internal Stock Transfer</span>
		</div>

		<div class="page-heading_new" style="padding: 0 30px;" id="transfer_id_header">
			<input type="hidden" value="{{data.id}}" id="transfer_id" name="transfer_no"/>
			<input type="hidden" value="{{data.status}}" id="status" name="status"/>
			<form id="stock-transfer-form" method="post" action="/erp/stores/json/stock_transfer_list/">
				{% csrf_token %}
				<input type="hidden" name="enterprise_id" id="enterprise_id_input" value="">
				<input type="hidden" name="enterprise_id" id="user_id_input" value=""> <!-- Hidden field for enterprise_id -->
			</form>

			<a role="button" href="#" class="btn btn-add-new pull-right" data-tooltip="tooltip" title="Back" onclick="submitStockTransferForm()">
				<i class="fa fa-arrow-left" aria-hidden="true"></i>
			</a>
			<span class="page_header_transfer">
				<span class="header_current_page"> {{ data.code }}</span>
			</span>
		</div>
		<div class="container-fluid" id="container-fluid">
<!--			<a class="btn btn-add-new pull-right" id="enable_edit_voucher" style="right: 78px !important" data-tooltip="tooltip" title="Edit"><i class="fa fa-pencil" aria-hidden="true"></i></a>-->
			<div class="" style="margin-top: 20px " id="tab2">
			<div>
				<div class="col-lg-12">
					<div class="content_bg">
						<div class="tab-content">
							<div>
								<div class="row">
									<div class="col-sm-6">
										<table border="0" class="side-table table text_box_in_table">
											<tr>
												<td class="side-header" style="width: 190px;">Stock No. & Date</td>
												<td class="side-content" id="transfer_code">{{data.code}} & {{data.created_on}}</td>
											</tr>
											<tr>
												<td class="side-header" style="width: 190px;">From Location *</td>
												<td class="side-content ">
													<div class="tour_location_tag">
														<div class="component_location" data-id="id_stock_transfer_from" data-name="from_location" data-value="{{data.from_location}}"></div>
													</div>
													<label id="fromLocationLabel" style="display: none;">From Location: 1</label>
												</td>
											</tr>
											<tr>
												<td class="side-header" style="width: 190px;">To location *</td>
												<td class="side-content ">
													<div class="tour_location_tag">
														<div class="component_location" data-id="id_stock_transfer_to" data-name="to_location" data-value="{{data.to_location}}"></div>
														<input type="hidden" value="{{data.pp_id}}" id="pp_id" />
													</div>
												</td>
											</tr>
										</table>
									</div>
								</div>
								<div class="row">
									<div id="stockTransfer_list_view">
										<div class="col-lg-12 search_result_table">
											<ul class="nav nav-tabs">
											  <li id="item_particulars" class="materials_add active"><a role="button">Item Particulars</a></li>
											  <li class="service_flag" style="margin-top: 12px"><span class='service-item-flag'></span> - Service</li>
											</ul>
											<div class="row item-particulars-add-container" id="materials_add" style="margin-bottom:0px !important;">
												<div class="col-sm-3 form-group">
													<label>Materials Requested<span class="mandatory_mark"> *</span></label>
													<input type="text" class="form-control" id="materialrequired" placeholder="Select Material" maxlength="100">
													<span class="item_faulty_check checkbox chk-margin item_particulars_chk non-form-element" style="margin-top:20px;">
                                                                    <input type="checkbox" class="chkcase" name="case" id="chkfaulty"  value="" />
                                                                    <label for="chkfaulty" style="min-height: 0;">Faulty</label>
                                                                </span>
													<input type="hidden" value="" class="" id="material_id_hidden" placeholder="">
													<input type="hidden" value="" class="" id="id_ind_material-__prefix__-is_service" placeholder="" >
													<input type="hidden" value="" class="" id="material_id" placeholder="" >
													<input type="hidden" value="" class="" id="cat_code" placeholder="">
													<input type="hidden" value="" class="" id="id_material-material_id" placeholder="" >
													<input type="hidden" value="" class="" id="id_material-enterprise_id" placeholder="">
													<input type="hidden" value="" class="" id="closingStkQty" placeholder="">
													<input type="hidden" value="" class="" id="store_price" placeholder="">
													<input type="hidden" value="" class="" id="alt_uom" placeholder="">
													<div class="duplicate_material_name"></div>
														<span class="material-removal-icon hide">
															<i class="fa fa-times"></i>
														</span>
												</div>
												<div class="col-sm-3">
													<label>Quantity<span class="mandatory_mark"> *</span></label><label id="stkQty" class="hide" style="margin-left:60px;">Available qty: <span id="availableQty"></span></label>
													<input class="form-control" id="id_ind_material-__prefix__-quantity" maxlength="16" name="ind_material-__prefix__-quantity" onfocus="setNumberRangeOnFocus(this,12,3)" placeholder="Quantity" type="text">
													<div>
														<label id="ind_unit_display" class="unit_display pull-right">&nbsp;</label>
													</div>
													<div class="alternate_unit_select_box hide">
																<select class="form-control unit_select_box" id="id_ind_material-__prefix__-alternate_units" name="ind_material-__prefix__-alternate_units" placeholder="Units">
																</select>
													</div>
													<div class="all_units_select_box hide">

													</div>
													<div class="hide">
														<select id="unit_id" name="selected_unit">
															{% for unit,id in all_units.items %}
																<option value="{{ id }}">{{ unit }}</option>
															{% endfor %}
														</select>
													</div>
													<input type="hidden" id="unit_rate" name="unit_rate" value="{{data.rate}}">
												</div>

												<div class="col-sm-3">
													<div class="material_txt">
														<input type='button' id="add_new_stockTransfer" class="btn btn-save btn-margin-1" value="+"/>
														<input type='button' id="cmdshow" class="btn btn-save btn-margin-1" value="BOM"/>
													</div>
												</div>
												<div class="clearfix"></div>
											</div>
										</div>
									</div>
								</div>

								<div class="row" style="margin-top:50px">
									<div class="table-responsive full_width_txt" id="stockTransfer_materials">
										<div class="col-sm-12">
											<a role="button" id="a-export-stock_transfer-material" class="btn btn-add-new pull-right export_ind_mat_details" style="margin-top: 18px;" onclick="GeneralExportTableToCSV.apply(this, [$('#stockTransferMaterial'), 'Stock_Transfer_Request.csv']);" data-tooltip="tooltip" data-placement="bottom" title="Download Internal Stock Transfer List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
											<table class="table table-bordered table-striped custom-table text_box_in_table" id="stockTransferMaterial">
												<thead>
												<tr>
													<th style="width: 80px;">S No.</th>
													<th>Material</th>
													<th style="width: 240px;">Quantity</th>
													<th>Unit Price</th>
													<th>Amount</th>
													<th style="width: 180px;">Unit</th>
													<th style="width: 80px;" class="exclude_export">Delete</th>
												</tr>
												</thead>
												<tbody class="item-for-goods">
													{% for rec in data.transfer_details %}
													  <tr>
															<td class='text-center'>{{ forloop.counter }}</td>
															<td class='text-center item-name' data-item-id="{{ rec.item_id }}" data-is_faulty="{{rec.is_faulty}}">Loading...</td>
															<td class='text-center hide exclude_export' id='table_material_id'>{{ rec.item_id }}</td>
														    <td hidden='hidden'>{{ rec.quantity }}</td>
															<td class='td_textbox_right exclude_export'>
																<input type='text' id="stockTransfer_qty" class='form-control stockTransfer_qty' onfocus='setNumberRangeOnFocus(this, 12, 3)' value="{{ rec.quantity }}" onchange="calculateItemAmount(),calculateTotalAmount()">
																<label style="display: none;float:right">Max issue:<span id="closing_qty"></span> </label>
															</td>
														    <td hidden='hidden'>{{ rec.rate }}</td>
															<td class="text-right exclude_export"><input type='text' id="unit_price" class='form-control text-right' onfocus='setNumberRangeOnFocus(this, 12, 3)' onchange="calculateItemAmount(),calculateTotalAmount()" value="{{ rec.rate }}"></td>
															<td class='text-right rec_amount' id="rec_amount_{{ forloop.counter }}">
															</td>
															<td class='text-center' id="unit_name">{{ rec.unit_name }}</td>
															{% if data.status == "Approved" %}
																<td class='text-center exclude_export'><a href='#' title="The item can not be delete"><i class='fa fa-trash-o delete-row-btn disabled'></i></a></td>
															{% else %}
																<td class='text-center exclude_export'><a href='#'><i class='fa fa-trash-o delete-row-btn'></i></a></td>
															{% endif %}
														  <td hidden='hidden' class='exclude_export' id="is_faulty">{{ rec.is_faulty }}</td>
													   </tr>
													{% endfor %}
												</tbody>
												<tbody class="item-for-service">

												</tbody>
												<tr id="total">
														<td align="right" colspan="4" class="total_span_column">
															<span style="font-size: 16px; color: #838383;">TOTAL</span>
														</td>
														<td align="right" id="total_amount"></td>
													</tr>
											</table>
										</div>
									</div>
								</div>


								<div class="clearfix"></div>
								<div class="col-sm-3">
									<label>Remarks</label>
									<div class="remarks_count_link remarks_count disabled" onclick="remarks_show();">
										<span class="countContainer remarks_counter" id="remarks_counter">No</span><span> remarks</span>
										<p id="remarksLength" hidden="hidden">{{ data.transfer_remarks }}</p>
									</div>
									<textarea class="form-control" cols="40" id="id_transfer-remarks" maxlength="300" name="id_transfer-remarks" onkeypress="return validateStringOnKeyPress(this,event, &quot;alphaSpecialChar&quot;);" onblur="return validateStringOnBlur(this, event, &quot;alphaSpecialChar&quot;);" rows="5" value="{{ data.remarks }}">{{ data.remarks }}</textarea>
								</div>

								<div class="material_txt col-sm-12 btn-margin text-right" style="margin-top: -34px;">
									{% if logged_in_user|canEdit:'STORES' and data.status != 'Received' and data.status != '3' %}
										<input type="button" class="btn btn-save" id="save_stockTransfer_button" value="Save"/>
									{% endif %}
<!--									<a class="btn btn-cancel" onclick="submitStockTransferForm()">Back</a>-->
								</div>
							</div>
						</div>
					</div>
					<div class="row add_table" >
							<div id="catalogueModal" class="modal fade" role="dialog">
								<div class="modal-dialog modal-lg">
									<div class="modal-content">
										<div class="modal-header">
											<button type="button" class="close" data-dismiss="modal">&times;</button>
											<h4 class="modal-title">View BOM</h4>
										</div>
										<div class="modal-body">
											<table id="cattable" class="table table-bordered custom-table table-striped tableWithText">
												<thead>
													<tr align="center">
														<th>S.No</th>
														<th>Material</th>
														<th>Drawing No</th>
														<!-- <th>Make</th> -->
														<th>Qty</th>
														<th>Unit Price</th>
														<th>Amount</th>
														<th>Units</th>
														<th>Delete</th>
													</tr>
												</thead>
											</table>
										</div>
										<div id="catButton"></div>
									</div>
								</div>
							</div>
								<div class="clearfix"></div>
					</div>
				</div>
			</div>
		</div>
		</div>
	</div>
<div id="show_remarks_history_modal" class="modal fade"  role="dialog">
		<div class="modal-dialog modal-md">
		    <div class="modal-content">
	            <div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">&times;</button>
					<h4 class="modal-title">Remarks</h4>
				</div>
				<input type="hidden" id="remarks_list_json" value="{{data.transfer_remarks}}">
			    <div class="modal-body" id="remarks_list">

				</div>
				<div class="modal-footer" style="border-top: none;"></div>
		    </div>
		</div>
	</div>

<script>
	$(window).load(function(){
		if($('#transfer_id').val()){
			var fromLocation = '{{data.from_location_name}}';
			var fromLocation_id = '{{data.from_location}}';
			console.log("fromLocation",fromLocation,fromLocation_id);
			var found = false;
			$('#id_stock_transfer_from option').each(function() {
				if ($(this).val() == fromLocation_id) {
					found = true;
					return false;
				}
			});
			if (found) {
				$('#from_location_id').val(fromLocation_id);
				$("#tab2").removeClass('div-disable');
				$(this).addClass('hide');
				$('#fromLocationLabel').hide();
				$('.component_location[data-id="id_stock_transfer_from"]').show();
			}
			else{
				$('#tab2').addClass('div-disable');
				$('#fromLocationLabel').text(fromLocation).show();
				$('.component_location[data-id="id_stock_transfer_from"]').hide();
				$('#save_stockTransfer_button').hide();
			}
		}
		$("#id_stock_transfer_from").change(function() {
			$('#materialrequired, #id_ind_material-__prefix__-quantity').val('');
			$("#ind_unit_display").html('&nbsp;');
			$('#material_id_hidden').val('');
			$('#materialr_id').val('');
			$("#cmdshow").hide();
			$(".material-removal-icon").click();
			$('#stkQty').addClass('hide');
    });
		$('.component_location label').hide();
		const itemNameElements = document.querySelectorAll('.item-name');

		itemNameElements.forEach(function(element) {
			const itemId = parseInt(element.getAttribute('data-item-id'), 10);
			const is_faulty = element.getAttribute('data-is_faulty')
			const label = fetchLabelById(itemId);
			if(is_faulty == "True"){
				element.innerHTML = label+"[Faulty]";
			}
			else{
				element.innerHTML = label;
			}
		});
		var oTable;
		var oSettings;
		var hash = window.location.hash;
		hash && $('ul.nav a[href="' + hash + '"]').tab('show');

		$('.nav-tabs a').click(function (e) {
			$(this).tab('show');
			var scrollmem = $('body').scrollTop() || $('html').scrollTop();
			window.location.hash = this.hash;
			$('html,body').scrollTop(scrollmem);
		});
		calculateTotalAmount();
		calculateItemAmount();
	});

	function updateToLocationOptions() {
		var fromLocation = $("#from_location").data("value");
   		var toLocationSelect = $("#to_location");
      	var toLocationOptions = toLocationSelect.find('option');
		toLocationOptions.prop('disabled', false); // Enable all options initially

	  	if (fromLocation) {
		 toLocationOptions.each(function() {
			if ($(this).val() === fromLocation) {
			   $(this).prop('disabled', true);
			}
		 });
	  }
   }
$("#from_location").on('change', function() {
      fromLocation = $(this).data("value");
      updateToLocationOptions();
   });
function fetchLabelById(itemId) {
    const storedData = localStorage.getItem('goods_service_master_data');
    if (storedData) {
        const parsedData = JSON.parse(storedData);
        const materials = parsedData[0].material;
        for (const item of materials) {
            if (item.id === itemId) {
                return item.label;
            }
        }
    }
    return 'Label not found';
}
 function submitStockTransferForm() {
			var enterpriseId = $('#enterprise_id').val();
			var userId = $('#login_user_id').val();
			$('#enterprise_id_input').val(enterpriseId);
			$('#user_id_input').val(userId);
			$('#stock-transfer-form').submit();
    }
</script>
{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
{% include "masters/add_material_modal.html" %}
{% endblock %}