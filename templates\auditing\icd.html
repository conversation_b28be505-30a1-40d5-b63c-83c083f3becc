{% extends "auditing/sidebar.html" %}
{% block icd %}
<style>
    li.icd_side_menu a{
    outline: none;
    background-color: #e6983c !important;
	}

    #cattable {
		width: 550px;
		position: absolute;
		color: #000000;
		background-color: #FFFFFF;
		/* To align popup window at the center of screen*/
		top: 50%;
		left: 50%;
		margin-top: 100px;
		margin-left: 100px;
	}

	.custom-table.custom-table-large td.checkbox-border {
		padding: 12px !important;
	    padding-left: 35px !important;
	}

	.invoice-table-height {
		max-height: 500px;
		overflow: auto;
	}

	#table_audit_note td span {
		display: inline-block;
		width: 90px;
	}

	.tr-alert-danger , .tr-alert-danger input, .tr-alert-danger select {
		color: #a94442 !important;
		background-color: #f2dede !important;
		border-color: #bbb !important;
	}
	
	.checkbox input[type="checkbox"]:checked + label::after {
		background: #32CD32;
		color: #FFFFFF;
		margin-top: 3px;
	}

	.checkbox input[type="checkbox"] + label::before {
		margin-top: -2px;
	}

	.checkbox input[type="checkbox"] + label {
		padding-top: 5px;
	}

	input.empty-error-border,
	input.error-border-stock,
	input.error-border-po {
	    border: 1px solid #dd4b39 !important;
	    background: #fdeded;
	}

	.sa-input-error {
		display: none !important;
	}

	.account-type {
		float: right;
    	margin-top: 6px;
    	margin-right: 3px;
	}
</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/icd.js?v={{ current_version }}"></script>
<meta name="viewport" content="width=device-width, initial-scale=1">
<script type="text/javascript" src="/site_media/js/mailer.js?v={{ current_version }}"></script>

{% if module_access.icd %}
<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">{% if not is_credit_debit %} Internal Control {% else %} Credit/Debit Note {% endif %}</span>
	</div>
	<div class="col-lg-12">
		<div class="col-lg-12">
			<div class="page-heading_new" style="padding: 0;">
				<span class="page_header"></span>
				<input type="text" hidden="hidden" id="id_icd_ignore_credit_note" name="icd_ignore_credit_note" value={{module_access.icd_ignore_credit_note}}>
				{% if logged_in_user.is_super %}
					<a class="btn super_user_icon hide" onclick="EditICDNumber();" style="">
						<i class="fa fa-pencil"></i>
					</a>
					<div class="xsid_number_edit hide">
						<form class="form-inline" style="display: inline-block;" action="">
						    <div class="form-group">
						      	<input type="text" class="form-control" id="icd_financial_year" name="icd_financial_year" maxlength="5">
						    </div>
						    <div class="form-group">
							    <input type="text" class="form-control" id="icd_type" readonly="true" name="icd_type" maxlength="5">
						    </div>
						    <div class="form-group">
						      <input type="text" id="icd_number" name="icd_number" class="form-control" maxlength="6" onfocus="setNumberRangeOnFocus(this,6,0,true)">
						    </div>
						    <div class="form-group">
						      	<input type="text" class="form-control text-lowercase" onkeypress="validateStringOnKeyPress(this,event,'character');" onblur="validateStringOnBlur(this,event,'character');" id="icd_number_division" name="icd_number_division" maxlength="1" >
						    </div>
						    <div class="form-group super_edit_submit_icon">
						    	<span role='button' data-tooltip="tooltip" title="Save" class="super_edit_save" onclick="updateNoteNumber();"><i class="fa fa-check"  aria-hidden="true" style="color: green;"></i></span>
						    	<span role='button' data-tooltip="tooltip" title="Cancel" class="super_edit_cancel" onclick="DiscardEditICDNumber();"><i class="fa fa-times" aria-hidden="true" style="color: red;"></i></span>
						    </div>
				  		</form>
				  		<div class="save_xsid_error_format hide">Required. Please check help tool for valid format.</div>
				  	</div>
				  	<div class="super_user_tool_tip hide"><span class="question-mark"></span></div>
				{% endif %}	

				{% if is_credit_debit %}
					{% if logged_in_user|canEdit:'ICD' %}	
						<a href="/erp/auditing/note/" id="note_creation" class="btn btn-new-item pull-right" data-tooltip="tooltip" title="Create New Note" ><i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span></a>
					{% else %}	
						<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in Audit Module. Please contact the administrator.">
							<a id="note_creation" class="btn btn-new-item pull-right disabled"><i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span></a>
						</div>
					{% endif  %}
				{% endif  %}
				<a role="button" class="btn btn-add-new pull-right view_icd hide" id="cmdcancel_up" onclick="CancelButtonClick();" data-tooltip="tooltip" title="Back">
					<i class="fa fa-arrow-left" aria-hidden="true"></i>
				</a>
				{% if is_credit_debit and logged_in_user|canEdit:'ICD' %}
				<input type="hidden" id="eivoice_note_id" />
					<div class="multiple-eInvoice">
						<div class="e-invoice_mod">
							<input type="hidden" id="isValidEinvoice" value="" />
							<span class="btn transparent-btn pull-right eInvoice-generate" style="margin-right: 8px; margin-top: 15px;" onclick="generateEInvoiceRequest('multiple', 'note');">Generate e-Invoice</span>
						</div>
					</div>
					<div class="single-eInvoice hide">
						<div class="e-invoice_mod">
							<span class="btn transparent-btn pull-right eInvoice-generate" style="margin-right: 8px;" onclick="generateEinvoiceNoteSingle()">Generate e-Invoice</span>
						</div>
						<div id="icd_cnl_irn">
							<span class="btn transparent-btn pull-right eInvoice-upload" style="margin-right: 8px;" onclick="cancelEinvoiceNoteJson();">Cancel IRN</span>
						</div>
					</div>
				{% endif  %}
			</div>
			<div id="view_icd">
				<div class="row">
					<div class="filter-components">
						<div class="filter-components-container">
							<div class="dropdown">
								<button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
									<i class="fa fa-filter"></i>
								</button>
								<span class="dropdown-menu arrow_box arrow_box_filter">
									<div class="col-sm-12 form-group" >
										<label>Date Range</label>
										<div id="reportrange" class="report-range form-control">
											<i class="glyphicon glyphicon-calendar"></i>&nbsp;
											<span></span> <b class="caret"></b>
											<input type="hidden" class="fromdate" id="fromdate" name="fromdate" value="{{ from_date }}" />
											<input type="hidden" class="todate" id="todate" name="todate" value="{{ to_date }}" />
											<input type="hidden" class="cstatus" id="cstatus" name="cstatus" value="{{ cstatus }}" />
										</div>
									</div>
									<div class="col-sm-12 form-group">
										<label>Project/Tag</label>
										<select class="form-control chosen-select" name="select" id="project">
											<option value="0" >ALL</option>
											{% for j in projects %}
											<option value="{{ j.0 }}">{{ j.1 }}</option>
											{% endfor %}
										</select>
									</div>
									<div class="col-sm-12 form-group">
										<input hidden="hidden" id="id-received_against"/>
										<label>Received From</label>
										<select class="form-control chosen-select" name="select" id="party">
											{% for j in party %}
											<option value="{{ j.0 }}">{{ j.1 }}</option>
											{% endfor %}
										</select>
									</div>
									<div class="col-sm-12 form-group">
										<label>Status</label>
										<select class="form-control chosen-select" name="status" id="search_status">
											<option value="0">All</option>
											<option value="1">Pending</option>
											<option value="2">Checked/Party Acknowledgement Pending</option>
											<option value="3">Verified</option>
											<option value="5">Returned/Party Rejected</option>
											<option value="7">Party Acknowledged</option>
										</select>
									</div>
									<div class="filter-footer">
										<input type="submit" class="btn btn-save" value="Apply" id="loadgrns"/>
			      					</div>
								</span>
							</div>
							<span class='filtered-condition filtered-date'>Date: <b></b></span>
							<span class='filtered-condition filtered-project'>Project/Tag: <b></b></span>
							<span class='filtered-condition filtered-received'>Received From: <b></b></span>
							<span class='filtered-condition filtered-status'>Status: <b></b></span>
						</div>
					</div>
				</div>
				<div class="col-lg-12 remove-padding">
					<div class="csv_export_button" style="right: 0">
						<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#icdList'), 'ICD_List.csv']);" data-tooltip="tooltip" title="Download ICD List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
					</div>
					<table class="table custom-table table-striped table-bordered" id="icdList" style="width: 100%;">
						<thead>
							<tr>
								<th style="max-width: 40px;"> S. No </th>								 
								 <th style="max-width: 80px;"> Date </th>
								 <th style="max-width: 125px; min-width: 125px"> {% if is_credit_debit %} Note {% else  %} GRN  {% endif  %} No </th>
								 <th  class="td_ref_no hide" style="max-width: 125px; min-width: 125px"> Ref No </th>
								 <th> Project </th>
								 <th> Received From </th>
								 <th> DC/Inv No </th>
								 <th style="max-width: 80px;"> DC/Inv Date </th>
								 <th style="max-width: 80px;"> Invoice Value</th>
								 <th style="max-width: 80px;"> Note Value</th>
								 <th style="min-width: 100px;"> Status </th>
							</tr>
						</thead>
						<tbody>
						</tbody>
					</table>
				</div>
			</div>
		</div>	
		<div class="add_table" id="edit_icd" style="display: none;">
			<div class="col-lg-12">
				<div class="accordion" id="accordion" >
					<div class="text-right" style="margin-bottom: 15px;">
						<span id="id-display_document" class='modal-part' data-base64='' data-filename=''></span>
						{% if access_level.edit %}
							<input type="button" class="btn btn-save" value="Checked Ok" id="cmdupdate_up"/>
							<input type="button" class="btn btn-save" value="Return" onclick="returnRemarks();" id="cmdgrnreturn_up"/>
						{% endif  %}
					</div>
					<div class="accordion-group accordion-heading-invoice">
						<input hidden="hidden" id="id-grn_currency_code"/>
						<div class="accordion-heading">
							<a id="id-collapse_document" class="accordion-toggle"  aria-expanded="false" data-toggle="collapse" data-target="#collapseOne">
								Party Invoice
							</a>
						</div>
						<div id="collapseOne" class=" collapse">
							<div class="accordion-inner">
								<div id="invoice" style="height: 500px;margin">
									<div class="col-sm-6">
										<img class="invoice-table-height" style="width:100%;" id="invoice_doc" />
										<iframe width="100%" height="450" id="invoice_doc_pdf" src="" ></iframe>
									</div>
									<div id="id-invoice_data" class="full_txt_width col-sm-6 invoice-table-height">
										<table border="0" width="100%" >
											<tr>
												<td>&nbsp;</td>
												<td></td>
											</tr>
											<tr>
												<td>Party Name:</td>
												<td><label id="party_name_inv"  align="left"></label></td>
												<td>Receipt No:</td>
												<td><label id="grn_no_inv" align="left"></label></td>
												<td colspan="2"><span class="checkbox checkbox-border pull-right" style="margin-top: 0px;">
										            <input  name="is_checked"  type="checkbox" class="multi_check" id="id_is_checked">
													<label for="id_is_checked"></label></span>
												</td>
											</tr>
											<tr>
												<td>Invoice No:</td>
												<td><label id="inv_no_inv" align="left"></label></td>
												<td>Invoice Date:</td>
												<td><label id="inv_date_inv" align="left"></label></td>
												<td>Invoice Value:</td>
												<td><label id="inv_value_inv"  align="left"></label></td>
											</tr>
										</table>

										<table class="table custom-table table-striped table-bordered custom-table-large " id="grn_table" width="100%">
											<thead>
												<tr>
													<th rowspan="2" width="3%">S. No</th>
													<th rowspan="2">Item Received</th>
													<th colspan="3" width="18%">Quantity</th>
													<th rowspan="2" width="8%">Rate /<br> Unit</th>
													<th rowspan="2" width="5%">Disc.(%)</th>
													<th rowspan="2" width="6%">CGST</th>
													<th rowspan="2" width="6%">SGST</th>
													<th rowspan="2" width="6%">IGST</th>
													<th rowspan="2" width="6%"></th>
												</tr>
												<tr>
													<th>DC</th>
													<th>Rec.</th>
													<th>Acc.</th>
												</tr>
											</thead>
										</table>
										<div class="col-sm-12 text-center instant_invoice_loading_message" style="font-size: 16px;"><div class="loading">Loading <span>.</span><span>.</span><span>.</span></div><small></small></div>

										<div class="col-lg-12 text-right">
											<span class="po_title_txt">&nbsp;</span>
											<table id="grn_taxes_table" border="0" width="100%" >
											</table>
										</div>
										<div class="row">
											<div class="col-sm-8"><label>Receipt Remarks</label></div>
											<div class="col-sm-8 form-group" id="GRNremarksList"></div>
										</div>
									</div>
								</div>
							</div>
					  	</div>
					</div>

					<div class="accordion-group accordion-heading-audit">
				  		<div class="accordion-heading">
							<a class="accordion-toggle" id="auditNoteID" aria-expanded="true" data-toggle="collapse" data-target="#collapseTwo">
					  			Audit Note
							</a>
							<div class="audit_note_total_div_main" style="float: right; margin-top: -30px; margin-right: 15px;">
				        		<div id="audit_note_total_div" style="display: inline-block; margin-right: 15px;">
					        		Grand Total:
					        		<label id="grand_total" align="left"></label>
				        		</div>
								<span class="checkbox checkbox-border pull-right" style="margin-top: -15px;">
				            		<input  name="is_checked"  type="checkbox" class="multi_check" id="auditNoteCheck">
									<label for="auditNoteCheck"></label>
								</span>
							</div>
				  		</div>
				  		<div id="collapseTwo" class=" collapse in">
							<div class="accordion-inner">
								<div id ="drcr" style="display:none;">
									<table border="0" width="90%" id="table_audit_note" >
										<tr>
											<td>&nbsp;</td>
											<td></td>
										</tr>
										<tr>
											<td><span>Party Name:</span> <label id="party_name"  align="left"></label></td>
											<td><span>Receipt No:</span> <label id="grn_no" align="left" style="text-transform: inherit;"></label>
												<input type="hidden" id="receipt_no" >
											</td>
										</tr>
										<tr>
											<td><span>Invoice No:</span> <label id="inv_no" align="left"></label></td>
											<td><span>Invoice Date:</span> <label id="inv_date" align="left"></label></td>
											<td><span>Invoice Value:</span> <label id="inv_value"  align="left"></label></td>
											{% if logged_in_user.is_super %}
												<td style="position: absolute; right: 30px;">
													<a href="#" id="super_note_edit" data-tooltip="tooltip" title="Super Edit"><i class="fa fa-pencil" style="font-size: 20px;"></i> </a>
												</td>
											{% endif %}
										</tr>
									</table>
									<table class="table table-striped custom-table table-bordered custom-table-large tableWithText" id="drcrtable" >
										<thead>
											<tr>
												<th style="width: 75px;">S.No</th>
												<th>Description</th>
												<th>Reason</th>
												<th style="width: 125px;">Quantity</th>
												<th style="width: 125px;">HSN/SAC</th>
												<th style="width: 125px;">Rate <br> <span style="font-size: 8px;">(Smart Suggest)</span></th>
												<th style="width: 125px;">Value <br> <span style="font-size: 8px;">(Smart Suggest)</span></th>
												<th style="width: 80px;">CGST %</th>
												<th style="width: 80px;">SGST %</th>
												<th style="width: 80px;">IGST %</th>
											</tr>
										</thead>
										<tbody>
										</tbody>
											<tr>
												<td></td>
												<td></td>
												<td></td>
												<td></td>
												<td>Total</td>
												<td><label id="auto_total_amt" align="left"></label></td>
												<td><input type="text"  id="txttotamount" class="grn_qty_txtbox" disabled="disabled" align="right" value="0"/></td>
												<td></td><td></td><td></td><td></td><td></td><td></td><td></td>
											</tr>
									</table>
									<div class="col-sm-12 text-center instant_audit_loading_message" style="font-size: 16px;"><div class="loading">Loading <span>.</span><span>.</span><span>.</span></div><small></small></div>

									<div class="clearfix"></div>
									<div class="row" id ="taxrow">
										<div class="col-sm-6">
											<div class="form-group row">
												<div class="col-lg-6">
													<label>Taxes<span class="mandatory_mark"> *</span></label>
													<div class="arrow-select">
													<select class="form-control chosen-select" name="po_taxes" id="id_note_tax">
														<option value=""> --Select Tax-- </option>
														{% for tax in taxes %}
														<option value="{{ tax.code }}">{{ tax.name }}</option>
														{% endfor %}
													</select></div>
												</div>
												<div class="col-sm-3">
													<input type="button" class="add_tax_btn btn btn-save btn-margin-1" id="add_note_tax" value="+"/>
												</div>
											</div>
											<div class="row hide" style="margin-top: 15px;">
												<div class="col-lg-6"><label><span class="po_title_txt">Tags</span></label>
													<input type="text" value="" class="form-control" name="grn_tags" id="id_grn_tag" placeholder ="Select Tags" >
													<input type="text" value="" class="textbox2" id="grn_tag_value" maxlength="10" hidden="hidden">
												</div>
												<div class="col-sm-1 btn-margin-1">
													<input type="button" class="btn btn-save" id="add_grn_tag" value="+"/>
												</div>
			
												<div class="form-group col-sm-5">
													<span class="po_title_txt">&nbsp;</span>
													<ul id="grn_tags_table" class="tagit-display">
													<!-- Loaded through AJAX call or u-->
													</ul>
												</div>	
											</div>
											<div class="row">
												<div class="col-lg-6 form-group">
													<label>Remarks</label>
													<div class="remarks_count_link remarks_count disabled" onclick="remarks_history();">
														<span class="remarks_counter">No</span><span> remarks</span>
													</div>
													<textarea id="txtremarks" placeholder="Enter Remarks" class="form-control" maxlength="300" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" rows="3"></textarea>
													
												</div>
											</div>	
										</div>
										<div class="col-sm-6">
											<div class="col-lg-12 text-right">
												<span class="po_title_txt">&nbsp;</span>
												<table id="note_taxes_table" border="0" width="100%">
												</table>
											</div>
											<div class="col-sm-12" style="padding-top: 15px;">
												<div class="col-sm-6 text-right">
													<span class="title_txt">Round off </span>
												</div>
												<div class="col-sm-6" style='padding-right:0px;'>
													<input id="txtround_off" class="form-control"
														style="text-align:right;" value="0.00"
														onblur="Calculate()"
														maxlength="8" onfocus="setNumberRangeOnFocus(this,5,2,false,true)" />
												</div>
											</div>	
											<div class="col-lg-12" style="padding-top: 15px;">
												<div class="col-sm-6 text-right" style="margin-top: 5px;">
													<span class="title_txt">Grand Total</span>
												</div>
												<div class="col-sm-6" style='padding-right:0px;'>
													<input id="txtnetvalue" class="form-control" readonly style="text-align:right;" />
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
				  		</div>
					</div>
					<div class="accordion-group accordion-heading-receipt">
					  	<div class="accordion-heading">
							<a class="accordion-toggle" aria-expanded="false" data-toggle="collapse" id="receipt_tap" data-target="#collapseThree">
						  		Receipt
							</a>
					  	</div>
					  	<div id="collapseThree" class=" collapse">
							<div class="accordion-inner">
								<object width="700" height="800" id="grn" data=""></object>
							</div>
					  	</div>
					</div>
					<div class="accordion-group accordion-heading-po po_tap hide">
					  	<div class="accordion-heading">
							<a class="accordion-toggle" aria-expanded="false" data-toggle="collapse" id="po_tap" data-target="#collapseFour">
						  	Purchase Orders
							</a>
					  	</div>
					  	<div id="collapseFour" class=" collapse">
							<div class="accordion-inner">
								<object width="700" height="800" id="po" data=""></object>
							</div>
					  	</div>
					</div>
					<div class="accordion-group accordion-heading-inv invoice_tap hide">
					  	<div class="accordion-heading">
							<a class="accordion-toggle" aria-expanded="false" data-toggle="collapse" id="invoice_tap" data-target="#collapseFive">
						  	Invoice
							</a>
					  	</div>
					  	<div id="collapseFive" class=" collapse">
							<div class="accordion-inner">
								<object width="700" height="800" id="invoice_data" data=""></object>
							</div>
					  	</div>
					</div>
		  		</div>
		  		<div class="text-right">
					<input type="text" value="" class="textbox2" name="receipt_no"
					   id="txtgrn_id" maxlength="10" hidden="hiddden">
				    <input type="text" value="" class="textbox2" name="note_id"
					   id="id-note_id"  hidden="hiddden">
				    {% if logged_in_user.is_super %}
				        <input type="button" class="btn btn-save" value="Save" id="cmdSuperUpdate" />
				    {%else%}
				        <input type="button" class="btn btn-save disabled" value="Save" id="cmdSuperUpdate " data-tooltip="tooltip" data-title="Only Super User can edit this field"/>
				    {% endif  %}
					{% if access_level.edit or logged_in_user.is_super %}
						<input type="button" class="btn btn-save" value="Checked Ok" id="cmdupdate" />
				        <input type="button" class="btn btn-save" value="Return" onclick="returnRemarks();" id="cmdgrnreturn" />
					{% endif  %}
					<input type="button" class="btn btn-save" value="View Note" id="view_note" />
				</div>
			</div>
		</div>
		<div class="add_table" id="edit_icd1">
			<div class="col-lg-12 view_table_content">
				<div class="material_txt">
					<div class="col-lg-8 ">
					</div>
					<div class="col-lg-4 ">
						<table border="0" width="80%"  id="totaltable" hidden="hidden">
							<tr>
								<td>Total </td>
								<td><input type="text"  id="txttotamount" class="grn_qty_txtbox" disabled="disabled" align="right" placeholder="Enter Quantity" /></td>
								<td><input type="text"  id="dr_type" align="right" /></td><td>&nbsp;</td><td>Total </td><td><input type="text"  id="txtchangetotamount" class="grn_qty_txtbox" value="0" /></td>
								<td>
									<input type="text"  id="change_dr_type" align="right" />
									<input type="text"  id="tot_auto_amount" align="right" />
								</td>
							</tr>
						</table>
					</div>
					<div class="clearfix"></div>
				</div>
			</div>
			<div class="clearfix"></div>
		</div>			
	</div>
</div>
{% include "modal-window/note_preview.html" %}
{% include "modal-window/eInvoice-generator.html" %}
<input type="hidden" id="data-current-id" value="" />
<input type="hidden" id="data-note-id" value="" />
<input type="hidden" id="note_status" value="" />
<input type="hidden" id="icd_request_acknowledgement" value={{ icd_request_acknowledgement }} />
{% else %}
	<div class="right-content-container" id="right_container">
		<h3 style="color: #666;" class="text-center">ICD module is not available for your Enterprise. <br /><br />To avail this functionality, enable this section in Configurations.</h3>
	</div>
{% endif %}
{% include "attachment_popup.html" %}
<script type="text/javascript">
$(document).ready(function(){
	function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for(var i=0;i < ca.length;i++)
        {
            var c = ca[i];
            while (c.charAt(0)==' ') c = c.substring(1,c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length,c.length);
        }
        return null;
    }


	$("#li_internal_control").click(function() {
	   function eraseCookie(name) {
	        document.cookie = name+'=; Max-Age=-99999999;';
	    }
	    eraseCookie('from_date');
		eraseCookie('to_date');
		eraseCookie('party_id');
		eraseCookie('project_id');
	});

	$("#nav_auditing").click(function() {
		function eraseCookie(name) {
	        document.cookie = name+'=; Max-Age=-99999999;';
	    }
		eraseCookie('from_date');
		eraseCookie('to_date');
		eraseCookie('party_id');
		eraseCookie('project_id');
	});
	$(".chosen-select").chosen();

    /*
    var status = $("#cstatus").val();
    if(status == "undefined" || status == "None"){
      status = $("#status option:selected").val();
    }
    setCookie($("#fromdate").val(), $("#todate").val(), $("#party option:selected").val(), $("#project option:selected").val(), status);
    */
});

function updateFilterText() {
	$(".filtered-date b").text($("#reportrange").find("span").text());
	$(".filtered-project b").text($("#project option:selected").text());
	$(".filtered-received b").text($("#party option:selected").text());
	$(".filtered-status b").text($("#search_status option:selected").text());
}
	
$(window).load(function(){
	loadgrns();
	updateFilterText();
});

$("#add_note_tax").click(function(){
	$(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();

    var ControlCollections = [
        {
            controltype: 'dropdown',
            controlid: 'id_note_tax',
            isrequired: true,
            errormsg: 'Tax type is required.'
        }
    ];
    var result = JSCustomValidator.JSvalidate(ControlCollections);
	return result;
});

function FromToDateValidation(){
	$('#fromdate').on('changeDate', function(){
		$('#todate').datepicker('setStartDate', new Date($(this).val()));
		var start = new Date($('#fromdate').val()),
		end   = new Date($('#todate').val()),
		diff  = new Date(end - start),
		days  = diff/1000/60/60/24;
		if(days < 0) {
			$('#todate').datepicker('update', $('#fromdate').val());
		}
	});
}

function ICDSuperEditInit(){
	if($("#is_super_user").val().toLowerCase() == 'true') {
		if($(".header_current_page").text().indexOf("IAN") >=0) {
			$(".super_user_icon, .super_user_tool_tip").removeClass("hide");	
		}
		else {
			$(".super_user_icon, .super_user_tool_tip").addClass("hide");	
		}
		$('.super_user_tool_tip span').qtip({
	   		content: {
		        text: "<ul class='ul_for_tool_tip'><li>Super-edit enables modifying the ICD Code subject to Duplication Check.</li>\
		        		   <li>Code format will be 'FY-FY/IAN/NNNNNNx', <br />eg. '18-19/IAN/000731b'.<br />\
		        		   FY details - 5 characters (max), <br />ICD Type - 3 characters, <br />Number - 6 digits (max), <br />Sub-number - 1 alphabet (max).</li>\
		        		   <li>Subsequent numbering of ICD will pick from the highest of the ICD Code, <br/> i.e., gaps will not be filled automatically.</li></ul>",

		        title: 'Super-Edit'
		    }
		});
	}
}

function EditICDNumber() {
	var icdNumber = $(".header_current_page").text().trim();
	var icdNumberSplit = icdNumber.split("/");
	$("#icd_financial_year").val(icdNumberSplit[0]);
	$("#icd_type").val(icdNumberSplit[1]);
	if($.isNumeric(icdNumberSplit[2].substr(-1))){
		$("#icd_number").val(icdNumberSplit[2]);	
	}
	else {
		$("#icd_number").val(icdNumberSplit[2].slice(0, -1));
		$("#icd_number_division").val(icdNumberSplit[2].substr(-1));
	}
	$(".xsid_number_edit").removeClass("hide");
	$(".super_user_icon, .header_current_page").addClass("hide");
}

function DiscardEditICDNumber(){
	$(".xsid_number_edit").addClass("hide");
	$(".super_user_icon, .header_current_page").removeClass("hide");
	$("#icd_financial_year, #icd_number, #icd_number_division").val("");
}

function updateNoteNumber() {
	$.ajax({
        url: "/erp/stores/json/get_receipt_linked_message/",
        method: "POST",
        data:{receipt_no: $("#receipt_no").val()},
        success: function(response) {
            if (response.response_message =="Success") {
                if(response.custom_message == "") {
                     saveNoteNumber();
                } else {
                    swal({title: "", text: response.custom_message, type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#209be1",
                    confirmButtonText: "Yes, do it!",
                    closeOnConfirm: true,
                    closeOnCancel: true},
                    function(isConfirm) {
                        if (isConfirm) {
                            saveNoteNumber();
                        }
                    });
                }
            } else {
                swal({title: "", text: response.custom_message, type: "error"});
            }
        },
        error: function (xhr, errmsg, err) {
            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
        }
    });
}

function saveNoteNumber() {
	if($("#icd_financial_year").val() =="" || $("#icd_number").val() == "" || $("#icd_type").val() == "") {
		$(".save_xsid_error_format").removeClass("hide");
		if($("#icd_financial_year").val() == "") $("#icd_financial_year").addClass("super_edit_error_border");
		if($("#icd_number").val() == "") $("#icd_number").addClass("super_edit_error_border");
	}
	else {
		$(".save_xsid_error_format").addClass("hide");
		$("#icd_number_division").val($("#icd_number_division").val().toLowerCase());
		$.ajax({
			url: "erp/purchase/json/super_edit_note_code/",
			method: "POST",
			data:{
				receipt_no: $("#receipt_no").val(),
				new_financial_year: $("#icd_financial_year").val().trim(),
				new_icd_type: $("#icd_type").val().trim(),
				new_code: $("#icd_number").val().trim(),
				new_sub_number: $("#icd_number_division").val(),
			},
			success: function(response) {
			   if (response.response_message == "Success") {
					swal({title: "", text: response.custom_message, type: "success"});
					DiscardEditICDNumber();
					$(".header_current_page, title").text(response.code);
				} else {
					swal({title: "", text: response.custom_message, type: "warning"});
				}
	            ga('send', 'event', 'Cr/Dr Note', 'Super-Edit Code', $('#enterprise_label').val(), mat_list.length-json.failed_items.length);
			},
			error: function (xhr, errmsg, err) {
		       swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
	        }
		});
	}
}

function generate_pdf_ajax(icd_id, status, note_id){
    $("#icd_note_returned, #icd_note_verified, #icd_note_non_returned, #icd_note_returned_text, #checked_note").addClass("hide");
    $("#id-receipt_no").val(icd_id);
    $("#id-note_id").val(note_id);
    var icd_request_acknowledgement = $("#icd_request_acknowledgement").val();
    $("#loading").show();
    $.ajax({
        url: "/erp/auditing/json/downloadDoc/",
        type: "post",
        datatype: "json",
        data: {receipt_no: icd_id, note_id:note_id, grn_code: icd_id, response_data_type:"data", doc_type:"note"},
        success: function (response) {
			$("#loading").hide();
            if (response.response_message == "Internal server error") {
                swal("",response.custom_message,"warning");
                return;
            }
        	if(response.remarks != undefined) {
                if(response.remarks.length > 0){
                    $(".remarks_count_link").text(response.remarks.length+" remarks").removeClass('disabled')
                }
                else {
                    $(".remarks_count_link").text("No remarks").addClass('disabled')  
                }
                $("#icd_document_remarks").val(JSON.stringify(response.remarks));
            }
            $("#icd_document_modal").modal("show");
            $("#modal_icd_id").val(icd_id);
            if(response.data != ""){
            	var row = '<object id="icd_document" class="document_viewer" data="'+response.url+'">'+getPdfErrorMessage()+'</object>';
            	$("#icd_document_container").html(row);
            	$(".no_doc_available").addClass("hide").text('');
            } else {
            	$("#icd_document").addClass("hide");
            	$(".no_doc_available").removeClass("hide").text('Note is not generated for this Receipt.');
            }
            $("#display_popup").removeClass('hide')
            if (icd_request_acknowledgement != "True"){
	            if(status == 3) {
					$("#icd_note_verified").removeClass("hide");
					$("#icd_note_non_returned").removeClass("hide");
					$("#checked_note").addClass("hide");
					$("#icd_note_party_acknowledged").addClass("hide");
					$("#icd_note_party_rejected").addClass("hide");
				}
				else if(status == 2 || status == 6 || status == 7) {
					$("#icd_note_returned").removeClass("hide");
					$("#icd_note_non_returned").addClass("hide");
					$("#checked_note").addClass("hide");
					$("#icd_note_party_acknowledged").addClass("hide");
					$("#icd_note_party_rejected").addClass("hide");
				}
				else if(status == 5 || status == -2) {
					$("#icd_note_returned_text").removeClass("hide");
					$("#icd_note_non_returned").removeClass("hide");
					$("#checked_note").addClass("hide");
					$("#icd_note_party_acknowledged").addClass("hide");
					$("#icd_note_party_rejected").addClass("hide");
				}
				else {
					$("#icd_note_non_returned").removeClass("hide");
				}
			}
			else{
				if(status == 3) {
					$("#icd_note_verified").removeClass("hide");
					$("#icd_note_non_returned").removeClass("hide");
					$("#checked_note").addClass("hide");
					$("#icd_note_party_acknowledged").addClass("hide");
					$("#icd_note_party_rejected").addClass("hide");
				}
				else if(status == 2) {
					$("#icd_note_returned").removeClass("hide");
					$("#icd_note_non_returned").addClass("hide");
					$("#checked_note").addClass("hide");
					$("#icd_note_party_acknowledged").addClass("hide");
					$("#icd_note_party_rejected").addClass("hide");
				}
				else if(status == 5) {
					$("#icd_note_returned_text").removeClass("hide");
					$("#icd_note_non_returned").removeClass("hide");
					$("#checked_note").addClass("hide");
					$("#icd_note_party_acknowledged").addClass("hide");
					$("#icd_note_party_rejected").addClass("hide");
				}
				else if(status == 6) {
					$("#icd_note_party_acknowledged").addClass("hide");
					$("#icd_note_party_rejected").addClass("hide");
					$("#icd_note_returned").addClass("hide");
					$("#checked_note").removeClass("hide");
				}
				else if(status == 7) {
					$("#icd_note_returned").removeClass("hide");
					$("#icd_note_party_acknowledged").removeClass("hide");
					$("#icd_note_party_rejected").addClass("hide");
					$("#checked_note").addClass("hide");
				}
				else if(status == -2) {
					$("#icd_note_party_rejected").removeClass("hide");
					$("#icd_note_non_returned").removeClass("hide");
					$("#checked_note").addClass("hide");
					$("#icd_note_party_acknowledged").addClass("hide");
				}
				else {
					$("#icd_note_non_returned").removeClass("hide");
				}
			}
            $("#id-remarks").val("");
            $("#id-acknowledgement").val("");
            if($(".header_current_page").text() != "") {
            	if(($("#data-current-id").val() != "" && $("#data-current-id").val() != "null" && $("#data-current-id").val() != null) && ($("#data-note-id").val() != "")) {
            		editrow($("#data-current-id").val(), $("#data-note-id").val(), "note", $("#id-note_id").val(), status);
            	}
            }
        }
    });
    $("#icd_document_modal footer").remove();
    $('#icd_document_modal').on('hidden.bs.modal', function () {
        $(".remarks_count_link").css({float: "right"});
        $("#icd_document").remove();
    });
}

function returnNote(){
	if($('#id-remarks').val() == ""){
        swal({
            title: "<span style='color: #dd4b39;'>Remarks Required.</span>",
            text: "Please enter your purpose of returning this as remarks.",
            type: "warning"
        },
        function(){
            $('#id-remarks').focus();  
        });
        return false;
    }
    else {
    	$("#loading").show();
    	var icd_id = $("#id-receipt_no").val();
    	var note_id = $("#id-note_id").val();
    	$.ajax({
	        url: "/erp/auditing/json/returnGrn/",
	        type: "post",
	        datatype: "json",
	        data: {grn_number: icd_id, note_id:note_id, audit_remarks: $("#id-remarks").val(), return_status: 5},
	        success: function (response) {
	        	$("#loading").hide();
	            var message_type = 'warning';
	            if (response.response_message == "Success") {
	            	message_type = 'success';
		            	swal({title: "", text: "Returned Successfully", type: message_type,
	                        showCancelButton: false,
	                        confirmButtonColor: "#209be1",
	                        confirmButtonText: "Ok",
	                        closeOnConfirm: true,
	                        closeOnCancel: true,
	                        allowEscapeKey : false,
       						allowOutsideClick: false
	                },
	                function() {
	                	updateTableStatus(5, icd_id, note_id);
	                    generate_pdf_ajax(icd_id, 5, note_id);
	                });
	            } 
	            else {
	            	swal("", response.custom_message, "warning")
	            }
	        }
	    });
    }
}

function openMailPopup() {
    var type = "icd";
    if($("#id-receipt_no").val() == "") {
        type= 'note';
    }
    new Mailer().prepareEmailPopup().getSupplierMailID(id=$("#id-note_id").val(), type= type).show();
    return false;
}

function partyStatusICD(status){
    note_id = $("#id-note_id").val() ;
    remarks= $("#id-acknowledgement").val();
    var icd_id = $("#id-receipt_no").val();
    if(status == 'party_rejected' && $('#id-acknowledgement').val() == ""){
        setTimeout(function(){
                swal({
                    title: "<span style='color: #dd4b39;'>Remarks Required.</span>",
                    text: "Please enter your purpose of rejecting this Note as remarks.",
                    type: "warning"
                },
                function(){
                    $('#id-acknowledgement').focus();
                });
                return;
        },250)
    }
    else{
        $("#loading").show();
        $.ajax({
            url: "/erp/auditing/json/party_status_update/",
            type: "POST",
            dataType: "json",
            data: {grn_number: icd_id, note_id: note_id, status: status, remarks:remarks},
            success: function (json) {
                $("#loading").hide();
                var swalType = "warning";
                if(json.response_message == "Success") {
                    if (status == 'party_acknowledged'){
                        swal({
                            title: "<span style='color: #44ad6b;'>Acknowledged Successfully</span>",
                            text: 'Note <b>' + json.code + '</b> has been Acknowledged Successfully',
                            type: 'success',
                            allowEscapeKey: false
                        },
                        function(){
                            updateTableStatus(json.status, json.receipt_no, note_id);
                            generate_pdf_ajax(json.receipt_no, json.status, note_id);
                        });
                        ga('send', 'event', 'Note', 'Acknowledge', $('#enterprise_label').val(), 1);
                    }
                    else{
                        swal({
                            title: "Successfully Rejected",
                            text: "Note <b>" + json.code + "</b> has been Rejected.",
                            type: "success",
                            allowEscapeKey: false
                        },
                        function(){
                            updateTableStatus(json.status, json.receipt_no, note_id);
                            generate_pdf_ajax(json.receipt_no, json.status, note_id);
                        });
                        ga('send', 'event', 'Note', 'Reject', $('#enterprise_label').val(), 1);
                    }
                }
                else {
                    if (status == 'party_acknowledged'){
                        swal({
                            title: "<span style='color: #44ad6b;'>Acknowledgement Failed</span>",
                            text: 'Note <b>' + json.code + '</b> party acknowledgement has been failed',
                            type: 'error',
                            allowEscapeKey: false
                        });
                    }
                    else{
                         swal({
                            title: "<span style='color: #44ad6b;'>Rejection Failed</span>",
                            text: 'Note <b>' + json.code + '</b> party rejection has been failed',
                            type: 'error',
                            allowEscapeKey: false
                        });
                    }
                }
            },
            error: function (xhr, errmsg, err) {
                $("#loading").hide();
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });
    }
    return false;
}

</script>

{% endblock %}