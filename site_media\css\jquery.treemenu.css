/* For treemenu.js */

.tree li {list-style: none;}

.toggler {
    width: 1em;
    height: 1em;
    display: inline-block;
    cursor: default;
    font-size: 0.8em;
    text-align: center;
    padding: 0.1em;
    margin-left: -1.4em;
    margin-right: 0.6em;
    border: 1px solid transparent;
    padding: 0px 18px 28px 8px;
    border-radius: 5px;
    font-size: 20px;
    margin-left: 6px;
}

.level-2 {
	display: block;
    padding: 0px 0 0 16px;
    margin-bottom: 5px;
}

.level-2 li {
	border: none !important;
    padding-left: 20px !important;
}

.level-2 li.tree-empty > .toggler {
    margin-left: -14px;
}

li.tree-opened > .toggler:hover,
li.tree-closed > .toggler:hover {
    border-color: #999;
    cursor: pointer;
}

li.tree-empty > .toggler { color: transparent; }
li.tree-empty > span.toggler:before { content: "\2212"; }
li.tree-closed > span.toggler:before { content: "+"; }
li.tree-opened > span.toggler:before { content: "\2212"; }
