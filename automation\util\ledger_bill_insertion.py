import os
import pymysql
from logging import handlers
import logging


"""
"""
__author__ = 'saravanan'


# Logs
PATH = os.path.dirname(os.path.realpath(__file__))
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log_path = "/var/log/django/insert_ledger_bill.log"
logHandler = handlers.TimedRotatingFileHandler(log_path, when='midnight', interval=1, backupCount=100)
logHandler.setFormatter(formatter)
logger = logging.getLogger(os.path.splitext(os.path.basename(__file__))[0])
# Setting the threshold of logger to DEBUG
logger.setLevel(logging.DEBUG)
logger.addHandler(logHandler)

DB_PORT = 25394
DB_HOST = "localhost"
DB_USER = "xdevops"
DB_PASSWORD = "D3v$EnIq!^3"
DB_NAME = "migutharavu"


def executeQuery(query, as_dict=False, query_data=None, conversions=None):
	"""
	Executes a query & returns a result-set, as a list of either indexed tuples or dicts

	:param query:
	:param as_dict: Flag to specify the type of result-set, if True return a list of dicts, else a list of tuples
	:param query_data: Query parameters
	:param conversions
	:return:
	"""
	if conversions is None:
		conversions = pymysql.converters.conversions.copy()
	db_connection = pymysql.connect(host=DB_HOST, user=DB_USER, passwd=DB_PASSWORD, db=DB_NAME, port=DB_PORT,
	                                conv=conversions, charset="utf8mb4", binary_prefix=True)
	if as_dict:
		db_connection.cursorclass = pymysql.cursors.DictCursor
	cursor = db_connection.cursor()
	try:
		cursor.execute(query, query_data)
		db_connection.commit()
		return cursor

	except Exception as e:
		logger.info("Query cannot be executed %s" % e)
		db_connection.rollback()
	finally:
		db_connection.close()

# Data to insert

data = [
	('23-24/GV/003318','2024-02-12 00:00:00',722,41.3,0,102,311935),
	('23-24/GV/003319','2024-02-12 00:00:00',722,1411.28,0,102,311936),
	('23-24/GV/003320','2024-02-12 00:00:00',722,470.82,0,102,311937),
	('23-24/GV/003321','2024-02-12 00:00:00',722,9320.82,0,102,311938),
	('23-24/GV/003416','2024-02-19 00:00:00',722,41.3,0,102,312224),
	('23-24/GV/003417','2024-02-19 00:00:00',722,41.3,0,102,312225),
	('23-24/GV/003418','2024-02-19 00:00:00',722,41.3,0,102,312226),
	('23-24/GV/003419','2024-02-19 00:00:00',722,41.3,0,102,312227),
	('23-24/GV/003420','2024-02-19 00:00:00',722,41.3,0,102,312228),
	('23-24/GV/003422','2024-02-19 00:00:00',722,41.3,0,102,312230),
	('23-24/GV/003424','2024-02-19 00:00:00',722,11559.28,0,102,312232),
	('23-24/GV/003425','2024-02-19 00:00:00',722,11559.28,0,102,312233),
	('23-24/GV/003429','2024-02-19 00:00:00',722,11559.28,0,102,312237),
	('23-24/GV/003430','2024-02-19 00:00:00',722,41.3,0,102,312238),
	('23-24/GV/003431','2024-02-19 00:00:00',722,41.3,0,102,312239),
	('23-24/GV/003432','2024-02-19 00:00:00',722,41.3,0,102,312240),
	('23-24/GV/003433','2024-02-19 00:00:00',722,41.3,0,102,312241),
	('23-24/GV/003434','2024-02-19 00:00:00',722,41.3,0,102,312242),
	('23-24/GV/004070','2024-03-30 00:00:00',722,15688.1,0,102,316135),
	('23-24/GV/004071','2024-03-30 00:00:00',722,3230.72,0,102,316138),
	('23-24/GV/004072','2024-03-30 00:00:00',722,1411.28,0,102,316141),
	('23-24/GV/004073','2024-03-30 00:00:00',722,15622.02,0,102,316143),
	('23-24/GV/004074','2024-03-30 00:00:00',722,3510.5,0,102,316144),
	('23-24/GV/004075','2024-03-30 00:00:00',722,15688.1,0,102,316145),
	('23-24/GV/004076','2024-03-30 00:00:00',722,11559.28,0,102,316149),
	('23-24/GV/004077','2024-03-30 00:00:00',722,41.3,0,102,316152),
	('23-24/GV/004078','2024-03-30 00:00:00',722,41.3,0,102,316153),
	('23-24/GV/004079','2024-03-30 00:00:00',722,41.3,0,102,316155),
	('23-24/GV/004080','2024-03-30 00:00:00',722,41.3,0,102,316158),
	('23-24/GV/004081','2024-03-30 00:00:00',722,41.3,0,102,316159),
	('23-24/GV/004082','2024-03-30 00:00:00',722,41.3,0,102,316160),
	('23-24/GV/004083','2024-03-30 00:00:00',722,41.3,0,102,316161),
	('23-24/GV/004084','2024-03-30 00:00:00',722,41.3,0,102,316164),
	('23-24/GV/004085','2024-03-30 00:00:00',722,15621.44,0,102,316166),
	('23-24/GV/004086','2024-03-30 00:00:00',722,41.3,0,102,316167),
	('23-24/GV/004087','2024-03-30 00:00:00',722,41.3,0,102,316168),
	('23-24/GV/004088','2024-03-30 00:00:00',722,11559.28,0,102,316169),
	('23-24/GV/004089','2024-03-30 00:00:00',722,15688.1,0,102,316170),
	('23-24/GV/004090','2024-03-30 00:00:00',722,3510.5,0,102,316171),
	('23-24/GV/004091','2024-03-30 00:00:00',722,9320.82,0,102,316172),
	('23-24/GV/004092','2024-03-30 00:00:00',722,470.82,0,102,316173),
	('23-24/GV/004093','2024-03-30 00:00:00',722,1411.28,0,102,316174),
	# Add all other rows
]

for row in data:
	# Insert into ledger_bills
	query = """INSERT INTO ledger_bills (bill_no, bill_date, ledger_id, net_value, is_debit, enterprise_id, voucher_id)
        VALUES ('%s', '%s', %s, %s, %s, %s, %s)
    """ % row
	print query
	cursor = executeQuery(query=query)

	if cursor:
		# Get the last inserted id
		bill_id = cursor.lastrowid

		# Determine dr_value and cr_value based on is_debit
		if row[4] == 1:
			dr_value = row[3]
			cr_value = 0.00
		else:
			dr_value = 0.00
			cr_value = row[3]

		# Insert into ledger_bill_settlements
		query = """
	        INSERT INTO ledger_bill_settlements (bill_id, voucher_id, item_no, dr_value, cr_value, enterprise_id)
	        VALUES (%s, %s, %s, %s, %s, %s)
	    """ % (bill_id, row[6], 1, dr_value, cr_value, row[5])
		executeQuery(query=query)
	else:
		logger.info("Failed ledger bills: %s" % str(row))
