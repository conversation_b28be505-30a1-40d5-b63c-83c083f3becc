{% extends "stores/sidebar.html" %}
{% block stockreport %}
{% if access_level.view %}
    <link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
    <link type="text/css" rel="stylesheet" href="/site_media/css/chosen.css?v={{ current_version }}">
    <link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
    <script type="text/javascript" src="/site_media/js/bootstrap-datepicker.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/report.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/bootstrap-multiselect.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/chosen.jquery.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/jquery.easyResponsiveTabs.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/daterangepicker.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/dc_report.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/load_material.js?v={{ current_version }}"></script>

    <style>
        li.stock_report_menu a {
            outline: none;
            background-color: #e6983c !important;
        }

        .td-width-4 {
            min-width: 40px;
            width: 40px !important;
        }
        .td-width-6 {
            min-width: 60px;
            width: 60px !important;
        }
        .td-width-8 {
            min-width: 80px;
            width: 80px !important;
        }
        .td-width-10 {
            min-width: 100px;
            width: 100px !important;
        }
        .td-width-12 {
            min-width: 120px;
            width: 120px !important;
        }
    </style>
    <div class="right-content-container download-icon-onTop">
        <div class="page-title-container">
            <span class="page-title">DC Report</span>
        </div>
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12">
                    <div class="content_bg">
                        <div>
                            <ul class="resp-tabs-list hor_1"></ul>
                            <div class="resp-tabs-container hor_1">
                                <div class="row">
                                    <div class="add_table">
                                        <div class="col-lg-12 add_table_content">
                                            <div class="filter-components">
                                                <div class="filter-components-container">
                                                    <div class="dropdown">
                                                        <button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
                                                            <i class="fa fa-filter"></i>
                                                        </button>
                                                        <span class="dropdown-menu arrow_box arrow_box_filter">
                                                            <div class="col-sm-12 form-group" >
                                                                <label>Date Range</label>
                                                                <div id="reportrange" class="report-range form-control">
                                                                    <i class="glyphicon glyphicon-calendar"></i>&nbsp;
                                                                    <span></span> <b class="caret"></b>
                                                                    <input type="hidden" class="fromdate" id="fromdate" name="fromdate" value="{{since}}"/>
                                                                    <input type="hidden" class="todate" id="todate" name="todate" value="{{till}}"/>
                                                                </div>
                                                            </div>
                                                            <div class="col-sm-12 form-group">
                                                                <label>Supplier</label>
                                                                <select class="form-control chosen-select" name="select" id="cmbsupplier">
                                                                    <option value="">All</option>
                                                                    {% for j in suppliers %}
                                                                        <option value="{{ j.id }}">{{ j.name }} ({{ j.code }})</option>
                                                                    {% endfor %}
                                                                </select>
                                                            </div>
                                                            <div class="col-sm-12 form-group">
                                                                <label>Material Name</label>
                                                                <select class="form-control chosen-select" name="select" id="id-material">
                                                                </select>
                                                            </div>
                                                            <div class="col-sm-12 form-group">
                                                                <div class="checkbox">
                                                                    <input name="approved_only" id="id_approved_only" type="checkbox"{% if approved_only == True %} checked="checked" {%endif%}/>
                                                                    <label for="id_approved_only">Exclude Drafts</label>
                                                                </div>
                                                                <div class="checkbox">
                                                                    <input name="returnable" id="id_returnable" type="checkbox" {% if is_returnable == True %} checked="checked" {%endif%}/>
                                                                    <label for="id_returnable">Returnable</label>
                                                                </div>
                                                                <div class="checkbox">
                                                                    <input name="non_returnable" id="id_non_returnable" type="checkbox" {% if is_non_returnable == True %} checked="checked" {%endif%}/>
                                                                    <label for="id_non_returnable">Non Returnable</label>
                                                                </div>
                                                            </div>
                                                            <div class="filter-footer">
                                                                <input type="button" class="btn btn-save" value="Apply" id="dcreportview"/>
                                                            </div>
                                                        </span>
                                                    </div>
                                                    <span class='filtered-condition filtered-date'>Date: <b></b></span>
                                                    <span class='filtered-condition filtered-party'>Supplier Name: <b></b></span>
                                                    <span class='filtered-condition filtered-material'>Material Name: <b></b></span>
                                                    <span class='filtered-condition filtered-draft'>Draft Excluded: <b></b></span>
                                                    <span class='filtered-condition filtered-return'>Returnable: <b></b></span>
                                                    <span class='filtered-condition filtered-nonreturn'>Non Returnable: <b></b></span>
                                                </div>
                                            </div>
                                            
                                            <div id="grn_report">
												<div class="col-lg-12 search_result_table">
                                                    <div class="csv_export_button">
                                                        <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#tablesorter'), 'DC_Report.csv']);" data-placement="bottom" data-tooltip="tooltip" title="Download&nbsp;DC&nbsp;Report as&nbsp;CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
                                                    </div>
													<table class="table table-bordered custom-table table-striped grn_tbl" id="tablesorter" style="margin-right: 80px; display: inline-table;">
														<thead>
    														<tr>
    															<th class="td-width-4">S. No</th>
    															<th class="td-width-8">DC No</th>
    															<th class="td-width-6">DC Date</th>
                                                                <th class="td-width-6">Type</th>
    															<th class="td-width-8">Ref</th>
    															<th class="td-width-10">Party Name</th>
    															<th class="td-width-12">Material</th>
    															<th class="td-width-6">Qty</th>
    															<th class="td-width-6">Unit</th>
    															<th class="td-width-6">Rate</th>
    															<th class="td-width-6">Value</th>
															    <th class="td-width-6">Return <br/>Due On</th>
															    <th class="td-width-6">Return <br/>GRNS</th>
															    <th class="td-width-6">Pending <br />Qty</th>
															    <th class="td-width-6">Delivery Status<br />(Last Received On)</th>
															    <th class="td-width-6">Overdue<br />(in Days)</th>
    														</tr>
														</thead>
														<tbody id="DueDetailsTBody">
														</tbody>
														<tfoot class="cs_tfoot">
														</tfoot>
													</table>
												</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                {% block stock_statement %}
                                {% endblock %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        $(document).ready(function () {
            onPageLoad();
        });

        $(window).load(function(){
            updateFilterText();
        });

        function updateFilterText() {
            $(".filtered-date b").text($("#reportrange").find("span").text());
            $(".filtered-party b").text($("#cmbsupplier option:selected").text());
            $(".filtered-material b").text($("#id-material option:selected").text());
            $(".filtered-draft b").text($("#id_approved_only").is(":checked")?"Yes":"No");
            $(".filtered-return b").text($("#id_returnable").is(":checked")?"Yes":"No");
            $(".filtered-nonreturn b").text($("#id_non_returnable").is(":checked")?"Yes":"No");
        }

        var oTable;
        var oSettings;
        function TableHeaderFixed() {
           oTable = $('#tablesorter').DataTable({
				fixedHeader: true,
				"pageLength": 50,
				"search": {
					"smart": false
				},
				"columns": [
					null,null,
					{ "type": "date" },
					null,null,null,null,null,null,null,null,null,null,null,null,null
					]
			});
			oTable.on("draw",function() {
				var keyword = $('#tablesorter_filter > label:eq(0) > input').val();
				$('#tablesorter').unmark();
				$('#tablesorter').mark(keyword,{});
			});
			oTable.on('page.dt', function() {
			  $('html, body').animate({
				scrollTop: $(".dataTables_wrapper").offset().top - 15
			   }, 'slow');
			});
            oSettings = oTable.settings();
            $( window ).resize();
        }

		
		$('.nav-pills li').removeClass('active');
		$("#li_dc_report").addClass('active');
    </script>
{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
{% endblock %}