<div id="po_document_modal" class="modal fade" role="dialog">
  	<div class="modal-dialog modal-lg">
		<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Preview Document</h4>
      		</div>
      		<div class="modal-body text-center">
      			<input id="modal_po_id" name="po_id" value='' hidden="hidden"/>
				<input id="modal_po_type" name="po_type" value='' hidden="hidden"/>
		        <input id="modal_mask_po" name="mask_po_type" value='' hidden="hidden"/>
      			<div class="row" style="text-align: left;">
					<div class="content_bg">
						<div class="add_table">

							<div style="width: 300px; float: left; padding: 0 15px;">
								<input type="submit" id="generate_pdf" hidden="hidden"/>
								<input id="po_remarks" class="form-control" enterkey="do_nothing" maxlength="248" name="po_remarks" placeholder="Approval/Rejection Remarks"/>
						       	<div class="remarks_count_link remarks_count disabled" onclick="remarksInSwal('po_document_remarks');">
									<span class="remarks_counter">No</span><span> remarks</span>
								</div>
							</div>
							<div id="po_doc_btn">
								{% if access_level.approve %}
									<a role='button' id='approve_po_doc' class="btn btn-save">Approve</a>
									<a role='button' id='review_po' class="btn btn-warning">Review</a>
								{% endif %}
								{% if access_level.approve %}
									<a role='button' id='reject_po' class="btn btn-danger for_po_approve">Reject</a>
								{% else %}
									{%if access_level.edit %}
										<a role='button' id='reject_po' class="btn btn-danger for_po_edit">Discard</a>
									{% endif %}
								{% endif %}
								{% if access_level.approve %}
									<a role='button' id='regenerate_po' class="btn btn-save">Regenerate</a>
								{% endif %}
								<div style="float: right; margin-right: 15px;">
									{% if logged_in_user.is_super %}
									<a role='button' id='mask_po' class="btn transparent-btn" data-tooltip="tooltip" data-placement="left" title="">
										<i class="fa" style="font-size: 14px;" aria-hidden="true"></i>
									</a>
									{% endif %}
									<a role="button" id='display_popup' class="btn transparent-btn" data-tooltip="tooltip" data-placement="left" title="Email P.O." onclick="openMailPopup()"><i class="fa fa-envelope" style="font-size: 14px;" aria-hidden="true"></i></a>
								</div>
							</div>
						</div>
					</div>
				</div>
				<input type="hidden" id="po_document_remarks" />
				<div id="po_document_container"></div>
      		</div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
    	</div>
  	</div>
</div>

<script type="text/javascript">
	var isAmend = false;
	$('#approve_po, #approve_po_doc').click(function () {
        if($(this).attr("id") == "approve_po_doc") {
            po_id = $("#modal_po_id").val();
            modal_po_type = $("#modal_po_type").val();
        }
        else {
            po_id = $("#po_id").val();
            modal_po_type = $("#po_order").val();
        }
        app_remarks=$("#po_remarks").val()
        date = new Date();
        $('#loading').show();
        $.ajax({
            url: "/erp/purchase/json/po/approve/?now=" + date.toString(),
            type: "POST",
            dataType: "json",
            data: {po_id: po_id, remarks:app_remarks},
            success: function (json) {
                $('#loading').hide();
                var even_action = "Approve";
                var swalMessage;
                var swalStatus;
                var po_type;
                var approved_po_no = 0;
                if (isAmend==true){
                    if(json.response_message == 'Success'){
                        swalTitle = "<span style='color: #44ad6b;'>Amended Successfully</span>";
                        swalMessage = 'PO No: ' + $('#orderno').text() + ' ' + 'has been amended successfully';
                        swalStatus = 'success';
                        var po_type = $('#orderno').text().indexOf('J') > -1 ? "Job" : "Purchase";
                        modal_status = 2;
                        ga('send', 'event', po_type + ' Order', "Amend", $('#enterprise_label').val(), 1);
                    }
                    else {
                        swalTitle = "Amended Failed";
                        swalMessage = 'PO No- ' + $('#orderno').text() + ' - ' + 'Amend Failed';
                        swalStatus = 'warning';
                        modal_status = 2;
                    }
                }else{
                    if(json.response_message == 'Success'){
                        swalTitle = "<span style='color: #44ad6b;'>Approved Successfully</span>";
                        swalMessage = 'P.O. Draft: ' + po_id + ' ' + 'has been approved successfully';
                        swalStatus = 'success';
                        var po_type = $('#orderno').text().indexOf('J') > -1 ? "Job" : "Purchase";
                        modal_status = 2;
                        approved_po_no = json.code;
                        ga('send', 'event', po_type + ' Order', "Approve", $('#enterprise_label').val(), 1);
                    }
                    else {
                        swalTitle = "Approval Failed";
                        swalMessage = 'PO Draft - ' + po_id + ' - ' + 'Approval Failed';
                        swalStatus = 'warning';
                        modal_status = 0;
                    }
                }
                swal({
                    title: swalTitle,
                    text: swalMessage,
                    type: swalStatus
                }, function(){
                    generate_pdf_ajax(po_id, modal_po_type, modal_status);
                    updateTableStatus(modal_status, po_id, modal_po_type, approved_po_no);
                    //$('#generate_pdf').click();
                });
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
                $('#loading').hide();
            }
        });
        return false;
    });

    $("#generate_pdf").click(function(){
        var po_id = $("#po_id").val();
        var po_status = $("#generate_pdf").attr("data-status");
        var po_type = $("#po_order").val();
        generate_pdf_ajax(po_id, po_type, po_status);
    });

    $('#reject_po').click(function () {
        po_id = $("#modal_po_id").val() ;
        var po_type ="Purchase";
        var po_page = "po";
        if ($("#id_order_type").val() == "jo"){
            po_type ="Job";
            po_page = "jo";
        }
        date = new Date();
        rejection_remarks = $("#po_remarks").val()
        if ($(this).text() == "Reject"){
            if(rejection_remarks == ""){
                swal({
                    title: "<span style='color: #dd4b39;'>Remarks Required.</span>",
                    text: "Please provide a comment/remark to reject this "+po_type+" Order.",
                    type: "warning"
                });
                $("#remarks").focus();
            }
            else {
                discardPoAjax(po_id, date, po_page);
            }
        }
        else if($(this).text() == "Discard") {
            swal({
                title: "<span>Are you sure!</span>",
                text: "Discarded " + po_type + " Order will be deleted permanently. <br />Do you want to discard?",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#209be1",
                confirmButtonText: "Yes, discard it!",
                closeOnConfirm: true
            },
            function(){
                discardPoAjax(po_id, date, po_page);
            });
        }
    });

    function discardPoAjax(po_id, date, po_page) {
        $('#loading').show();
        var po_type ="Purchase";
        $.ajax({
            url: "/erp/purchase/json/po/checkpogrn/",
            type: "post",
            datatype:"json",
            data: {po_id:po_id},
            success: function(response){
                $('#loading').hide();
                if(response['length'] > 0) {
                    swal("Unable to Reject ",response['custom_message'],"warning");
                    return false;
                }
                else if(response['length']==-1) {
                    swal("OOPS!!", "Failed to complete the process!! Please contact administrator.", "error");
                    return false;
                }
                else {
                    $.ajax({
                        url: "/erp/purchase/json/po/reject/?now=" + date.toString(),
                        type: "POST",
                        dataType: "json",
                        data: {po_id: po_id, remarks: rejection_remarks},
                        success: function (rejected_po_code) {
                            if (rejected_po_code.po_type == "JO"){po_type = "Job";}
                            if (rejected_po_code.response_message == 'Delete') {
                                swal({
                                    title: "Removed Successfully",
                                    text: rejected_po_code.po_type +" (Draft no - "+ po_id +") is permanently removed",
                                    type: "warning"
                                },
                                function(){
                                    ga('send', 'event', po_type + ' Order', 'Discard', $('#enterprise_label').val(), 1);
                                    window.location.assign(`/erp/purchase/${po_page}_list/`);
                                });
                            }
                            else if (rejected_po_code.response_message == 'Success') {
                                swal({
                                    title: "Successfully Rejected",
                                    text: " "+ rejected_po_code.po_type +" No: " + rejected_po_code.po_no + " has been successfully rejected",
                                    type: "success"
                                },
                                function(){
                                    ga('send', 'event', po_type + ' Order', "Reject", $('#enterprise_label').val(), 1);
                                    generate_pdf_ajax(po_id, $("#modal_po_type").val(), 3);
                                    updateTableStatus(3, po_id, $("#modal_po_type").val(), 0);
                                    //$('#generate_pdf').click();
                                });
                            }
                            else{
                                swal("Failed", ""+ rejected_po_code.po_type +" No - " + rejected_po_code.po_no   + " Reject Failed", "warning");
                            }
                                    },
                        error: function (xhr, errmsg, err) {
                            console.log(xhr.status + ": " + xhr.responseText);
                            $('#loading').hide();
                        }
                    });

                    return false;
                }
            }
        });
    }

    $('#review_po').click(function () {
        po_id = $("#modal_po_id").val() ;
        if ($("#modal_po_type").val() == 1){
            po_type ="Job"
        }else{
            po_type ="Purchase"
        }
        app_remarks = $("#po_remarks").val()
        if (app_remarks ==""){
            swal({
                title: "<span style='color: #dd4b39;'>Remarks Required.</span>",
                text: "Please enter your purpose of reviewing this "+po_type+" Order as remarks.",
                type: "warning"
            });
            $("#remarks").focus();
            return;
        }
        date = new Date();
        $('#loading').show();
        $.ajax({
            url: "/erp/purchase/json/po/review/?now=" + date.toString(),
            type: "POST",
            dataType: "json",
            data: {po_id: po_id, remarks: app_remarks},
            success: function (response) {
                $('#loading').hide();
                if (response.response_message == "Success") {
                    swal({
                        title: "Review Successfully",
                        text: "PO Draft: " + po_id + " has been Reviewed.",
                        type: "success"
                    },
                    function() {
                        if (response.po_no != '0') {
                            window.location.assign('/erp/purchase/po_list/');
                        } else {
                            generate_pdf_ajax(po_id, $("#modal_po_type").val(), 1);
                            updateTableStatus(1, po_id, $("#modal_po_type").val(), 0);
                            //$('#generate_po_pdf_' + po_id).click();
                        }
                    });
                } else {
                    swal({title: "Review", text:"Review failed!", type:"error"});
                }
                ga('send', 'event', po_type + ' Order', 'Review', $('#enterprise_label').val(), 1);
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
                $('#loading').hide();
            }
        });
        return false;
    });
    $('#mask_po').click(function () {
        po_id = $("#modal_po_id").val() ;
        is_mask = $("#modal_mask_po").val() ;
        date = new Date();
        $('#loading').show();
        $.ajax({
            url: "/erp/purchase/json/po/mask/?now=" + date.toString(),
            type: "POST",
            dataType: "json",
            data: {po_id: po_id, is_mask: is_mask},
            success: function (response) {
                $('#loading').hide();
                if (response.response_message == "Success") {
                    if (is_mask==0)
                    {
                        title = "PO Masked Successfully"
                        text = "PO : " + po_id + " has been Masked."
                    }else {
                        title = "PO Unmasked Successfully"
                        text = "PO : " + po_id + " has been Unmasked."
                    }
                    swal({
                        title: title,
                        text: text,
                        type: "success"
                    },
                    function() {
                        window.location.assign('/erp/purchase/po_list/');

                    });
                } else {
                    swal({title: "Mask", text:"Mask failed!", type:"error"});
                }
                ga('send', 'event', po_type + ' Order', 'Mask', $('#enterprise_label').val(), 1);
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
                $('#loading').hide();
            }
        });
        return false;
    });
    $("#regenerate_po").click(function () {
        var po_id = $("#modal_po_id").val();
        var po_type = $("#modal_po_type").val();
        console.log("generate_pdf", po_id, po_type);
        generate_pdf_ajax(po_id, po_type, 2, true);
    });
    $('#amend_po').click(function () {
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();
        var ControlCollections = [
            {
                controltype: 'dropdown',
                controlid: 'supplier',
                isrequired: true,
                errormsg: 'Supplier Name is required.'
            }
        ];
        var result = JSCustomValidator.JSvalidate(ControlCollections);
        if(result){
            if($("#po_form").isChanged() || isDateChanged) {
                pur_quantities = document.getElementsByName('purchase');
                var data_count = Number($('#po_materials_table tbody.item-for-goods tr:visible').length + $('#po_materials_table tbody.item-for-service tr:visible').length);
                var table = document.getElementById('po_materials_table')
                for (i = 0; i < data_count; i++) {
                    if (parseFloat(pur_quantities[i].value) < parseFloat($(table.rows[i+2]).find('.td_received_qty').text()) && parseFloat($(table.rows[i+2]).find('.td_received_qty').text()) != 0 ) {
                        swal("", "Order quantity cannot be reduced below quantity of items received!", "warning");
                        return;
                    }
                }
                isAmend=true
                insert_Details(3);
            }
            else{
                swal("", "Amending this PO is not necessary, as no change has been made for the PO!", "warning");
            }
        }
        else {
            $("html, body").animate({ scrollTop: 0 }, "fast");
            $("#loading").hide();
            return result;
        }
    });

    function updateTableStatus(status, po_id, po_type, po_no){
	    var editedRow = $("#po_list").find("tr[data-poid='"+po_id+"']");
        editedRow.find("td.td_status").find("a.pdf_genereate").removeClass('draft reviewed approved rejected');
	    if(status == 1) {
	        editedRow.find("td.td_status").find("a.pdf_genereate").removeClass('draft').addClass('reviewed').text("Reviewed");
	    }
	    else if(status == 2) {
	        editedRow.find("td.td_status").find("a.pdf_genereate").removeClass('draft').addClass('approved').text("Approved");
	        editedRow.find(".edit_link_code").text(po_no);
	        $(".header_current_page, title").text(po_no)
	        if(editedRow.find(".td_po_date").text() == "-") {
	            editedRow.find(".td_po_date").text(moment().format("MMM D, YYYY"));
	        }
	    }

	    else if(status == 3) {
	        editedRow.find("td.td_status").find("a.pdf_genereate").removeClass('approved').addClass('rejected').text("Rejected");

	    }
	    editedRow.find(".edit_link_code").attr("onclick", "generate_pdf_ajax("+po_id+", "+po_type+", "+status+")");
        editedRow.find(".inline-icon-document").attr("onclick", "generate_pdf_ajax("+po_id+", "+po_type+", "+status+")");
        $("#generate_pdf").attr("data-status", status);
	}

	function generate_pdf_ajax(po_id, po_type, status, document_regenerate=false, is_mask_po=0){
	    $("#po_doc_btn a.btn, #po_remarks").addClass("hide");
	    $("#po_id").val(po_id);
		$("#po_document_modal").modal("show");
	    $.ajax({
	        url: "/erp/purchase/json/po_doc/",
	        type: "post",
	        datatype: "json",
	        data: {po_id: po_id, response_data_type: 'data', document_regenerate:document_regenerate},
	        success: function (response) {
	            if(response.response_code==400){
	                $("#po_document_modal").modal("hide");
                    window.location.replace("/erp/admin/po_template/?editRequired=true");
	            }
	            else {
		            if(response.remarks != undefined) {
		                if(response.remarks.length > 0){
		                    $(".remarks_count_link").text(response.remarks.length+" remarks").removeClass('disabled')
		                }
		                else {
		                    $(".remarks_count_link").text("No remarks").addClass('disabled')
		                }
		                $("#po_document_remarks").val(JSON.stringify(response.remarks));
		            }
		            else {
		                $(".remarks_count_link").text("No remarks").addClass('disabled')
		            }
		            $("#modal_po_id").val(po_id);
		            $("#modal_po_type").val(po_type);
		            $("#modal_mask_po").val(is_mask_po);
		            var row = '<object id="po_document" class="document_viewer" data="'+response.url+'">'+getPdfErrorMessage()+'</object>';
		            if(document_regenerate == true) {
		                $("#po_document_container").html('');
		            }
		            $("#po_document_container").html(row);
		            $("#display_popup").removeClass('hide')
		            if($("#is_super_user").val().toLowerCase() == 'true') {
		                $("#mask_po").removeClass("hide");
		                if(is_mask_po == '0'){
		                    $("#mask_po").attr("data-original-title", "Mask PO").find(".fa").removeClass("fa-eye-slash hide").addClass("fa-eye");
		                }else{
		                    $("#mask_po").attr("data-original-title", "Unmask PO").find(".fa").removeClass("fa-eye hide").addClass("fa-eye-slash");
		                }
		            }
		            if(status == 3 ) {
		                $("#approve_po_doc").removeClass("hide");
		                $("#po_remarks").removeClass("hide");
		            }
		            if(status == 0 ) {
		                $("#approve_po_doc").removeClass("hide");
		                $("#review_po, #po_remarks").removeClass("hide");
		            }
		            if(status <= 1 && ($("#reject_po").hasClass("for_po_edit") || $("#reject_po").hasClass("for_po_approve"))) {
		                $("#reject_po").text("Discard").removeClass("hide");
		                $("#po_remarks").removeClass("hide");
		            }
		            if(status == 2 && $("#reject_po").hasClass("for_po_approve")) {
		                $("#reject_po").text("Reject").removeClass("hide");
		                $("#po_remarks").removeClass("hide");
		                $("#regenerate_po").removeClass("hide");
		            }
		            if($("#po_remarks").hasClass("hide")) {
		                $(".remarks_count_link").css({float: "left"});
		            }
		            else {
		                $(".remarks_count_link").css({float: "right"});
		            }
					if(po_type==2){
		                $("#reject_po").addClass('hide');
		                $("#po_remarks").addClass("hide");
		                $("#display_popup").addClass("hide");
		                $(".remarks_count_link").css({float: "left"});
		                $("#regenerate_po").css({"margin-bottom": "8px"});
		            }
		            $("#po_remarks").val("");
		            //$("#po_id").val("");
	            }
	        }
	    });
	    $("#po_document_modal footer").remove();
	    $('#po_document_modal').on('hidden.bs.modal', function () {
	        $(".remarks_count_link").css({float: "right"});
	        $("#po_document").remove();
	        $("#po_document_container").html(getPdfLoadingImage());
	    });
	}

	function openMailPopup() {
	    new Mailer().prepareEmailPopup().getSupplierMailID(id=$("#modal_po_id").val(), type= "po").show();
	    return false;
	}
</script>