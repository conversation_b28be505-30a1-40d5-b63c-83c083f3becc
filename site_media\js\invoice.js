$(document).ready(function() {
      if ($("#id_invoice-type").val() == "Issue" && $('#id_invoice-job_po_id').val() !== "" && $('#id_invoice-job_po_id').val() !== "0") {
        $("#add_invoice_item").addClass('hide');
        $('#materials_add').addClass('div-disabled');
      }
      var project = JSON.parse(localStorage.getItem('project'));
      if(project && project.type == 'Secondary'){
        $('.page-title').text('Internal Invoice');
        $('#div_oa_no_display label').text('IWO No');
        $(document).on('click', '.chosen-container a', function(){
        $('#id_invoice_party_id_chosen .chosen-results li[data-option-array-index="1"]:contains("+ Add New")').hide();
//        $('#id_invoice-party_id').addClass('div-disabled');
    });
      }
      $('#id_invoice-location_id').on('change', function() {
        $("#invoice_materials_table tbody.item-for-service tr, #invoice_materials_table tbody.item-for-goods tr").remove();
        $('#id_invoice_mat-TOTAL_FORMS').val(0)
        $('#materialrequired, #id_invoice_mat-__prefix__-hsn_code, #id_invoice_mat-__prefix__-item_id').val('');
        $('#id_invoice_mat-__prefix__-quantity,#id_invoice_mat-__prefix__-rate,#id_invoice_mat-__prefix__-discount,#id_invoice_mat-__prefix__-amount').val('0.00');
        $("#unit_display").html('&nbsp;');
        $("#individual_item_description").addClass("hide");
        $("#description_display").html('') ;
        $('#material_id_hidden').val('');
        $('#material_id').val('');
        $("#explode_bom").hide();
        $(".material-removal-icon").click();
        $('.material-removal-icon').click();
        $("#closing_qty").text("MAX ISSUE:0");
    });
        $('#old_qty input').prop('disabled',true)
        const text = $("#id_invoice-job_po_id option:selected").text();
        if( text.includes('/MRS/')){
	        $('#mrs_type').val(1);
	    }
	    else{
	        $('#mrs_type').val(0);
	    }
});

$(function () {
	$('#explode_bom').click(function () {
        if ($('#materialrequired').val()=="") {
            swal("","Please Select Material","warning");
            return;
        }
        var newQtyField = document.getElementById('id_invoice_mat-__prefix__-quantity');

        if (newQtyField.value == "" || newQtyField.value =="0.00") {
            swal("","Please Enter Qty","warning");
            return;
        } else {
            $("#loadingmessage_changelog_listing_ie").show();
            showCatalogues();
        }
    });

	function showCatalogues() {
	    localStorage.removeItem('PPStockDetails');
	    localStorage.removeItem('MRSStockDetails');
        var catqty = parseFloat($("#id_invoice_mat-__prefix__-quantity").val());
        makes="";
        $.ajax({
            url: "/erp/sales/json/invoice/get_catalogue_materials/",
            type: "post",
            datatype: "json",
            data: {"item_id": $("#material_id").val(), "issued_on":$('#id_invoice-issued_on').val(),
             "alternate_unit_id":$('#id_invoice_mat-__prefix__-alternate_units').val()},
            success: function (response) {
                if (response.response_code!=400){
                    $("#cattable_2").find("tr:gt(0)").remove();
                    $("#catButton").find('.modal-footer').remove();
                    $.each(response, function (i, item) {
                        if(typeof(Storage) !== "undefined") {
                            if (sessionStorage.clickcount) {
                                sessionStorage.clickcount = Number(sessionStorage.clickcount) + 1;
                            } else {
                                sessionStorage.clickcount = 1;
                            }
                        }
                        var childArrow ="";
                        var materialName = item.name;
                        if(item.drawing_no != "" && item.drawing_no !=null) {
                            materialName = `${materialName} - ${item.drawing_no}`;
                        }

                        var makes = "-NA-";
                        stock_qty = item.stock_qty;
                        if (item.makes.length > 0){
                            makes = "<select class='form-control set-make-qty parent_bom_delete'  name='make' id='txtmake" + i + "'>";
                            for (j = 0; j <= item.makes.length - 1 ; j++) {
                                if (j==0){ stock_qty = item.makes[j][2] }
                                makes += "<option value='" + item.makes[j][0] + "' data-quantity='" + item.makes[j][2] + "'> " + item.makes[j][1] + " </option>";
                            }
                            makes += "</select>";
                        }
                        var hsn_code = `<input id="id_catmaterial_-${i}-hsn_code" type="text" class="form-control text-left td-hsn-code" name="catmaterial_hsn_code" maxlength="9" autocomplete="off" onchange="validateHsnWithSuggestion(this, 'id_catmaterial_-${i}-qty');" onkeypress="validateStringOnKeyPress(this, event, 'hsn_specialChar');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');"  value="">`;
                        var qty = `<input type="text" id="id_catmaterial_-${i}-qty" name="catmaterial_qty" class="form-control setUnitValue text-right bom_po_qty bom_amt_calc mandatory_field" maxlength="16" onchange="validateHsnWithSuggestion($('#id_catmaterial_-${i}-hsn_code'), 'id_catmaterial_-${i}-qty')" onfocus="setNumberRangeOnFocus(this,12,3)" autocomplete="off" value="${(item.quantity * catqty).toFixed(2)}">`;
                        var unit_id = '<input hidden="hidden" id="id_catmaterial_unit_id_'+i+'" type="text" class="form-control text-left " name="catmaterial_unit_id" value='+item.unit_id+' >';
                        var item_id = '<input id="id_catmaterial_-'+i+'-item_id" type="text"  name="catmaterial_item_id" value='+ item.item_id +'>';
                        var alternate_unit_id = '<input id="id_catmaterial_-'+i+'-alternate_unit_id" type="text"  name="catmaterial_alternate_unit_id" value='+ item.alternate_unit_id +'>';
                        var scale_factor = '<input id="id_catmaterial_-'+i+'-scale_factor" type="text"  name="catmaterial_scale_factor" value='+ item.scale_factor +'>';
                        var is_service = '<input id="id_catmaterial_-'+i+'-is_service" type="text"  name="catmaterial_is_service" value='+ item.is_service +'>';
                        var material_type = '<input hidden="hidden" id="id_catmaterial_material_type_'+i+'" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="catmaterial_material_type" value='+item.material_type+' >';
                        var is_stocked = ""
                        var stock_show = 0
                        if (item.material_type==true){
                            is_stocked = 'stockable';
                            classname='';
                            stock_show = `<span data-placement='left' data-tooltip='tooltip' title='MSL: ${item.minimum_stock_level}'> ${stock_qty} </span>`;
                        }
                        var bom_description = makes;
                        var itemTypeFlag ="";
                        if(item.is_service == true){
                            itemTypeFlag = `<span class="service-item-flag"></span>`;
                        }
                        if (item.material_type!=true && item.is_service != true){
                            itemTypeFlag = `<span class="non_stock-flag"></span>`;
                        }
                        var make_name = constructDifferentMakeName(item.make_name);
                        if (make_name){
                            materialName = `${materialName} [${make_name}]`;
                        }
                        if(item.child == 1)
                            childArrow = `<i class='fa fa-plus fa-for-arrow' role='button' onclick='return appendMaterial("${item.item_id}", "${sessionStorage.clickcount}", "${item.item_id}_${sessionStorage.clickcount}")'>
                                            <a style='padding-left: 26px; display: block; margin-top: -12px;'>
                                                ${materialName}
                                            </a>
                                          </i>`;
                        else
                            childArrow = `${materialName}`;
                        var hsn_code = `<input id="id_catmaterial_-${i}-hsn_code" type="text" class="form-control text-left td-hsn-code" name="catmaterial_hsn_code" maxlength="9" autocomplete="off" onchange="validateHsnWithSuggestion(this, 'id_catmaterial_-${i}-qty');" onkeypress="validateStringOnKeyPress(this, event, 'hsn_specialChar');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');"  value="">`;
                        var qty = `<input type="text" id="id_catmaterial_-${i}-qty" name="catmaterial_qty" class="form-control setUnitValue text-right bom_po_qty bom_amt_calc mandatory_field" maxlength="16" onchange="validateHsnWithSuggestion($('#id_catmaterial_-${i}-hsn_code'), 'id_catmaterial_-${i}-qty')" onfocus="setNumberRangeOnFocus(this,12,3)" autocomplete="off" value="${(item.quantity * catqty).toFixed(2)}">`;
                        var unit_id = '<input hidden="hidden" id="id_catmaterial_unit_id_'+i+'" type="text" class="form-control text-left " name="catmaterial_unit_id" value='+item.unit_id+' >';
                        var item_id = '<input id="id_catmaterial_-'+i+'-item_id" type="text"  name="catmaterial_item_id" value='+ item.item_id +'>';
                        var alternate_unit_id = '<input id="id_catmaterial_-'+i+'-alternate_unit_id" type="text"  name="catmaterial_alternate_unit_id" value='+ item.alternate_unit_id +'>';
                        var scale_factor = '<input id="id_catmaterial_-'+i+'-scale_factor" type="text"  name="catmaterial_scale_factor" value='+ item.scale_factor +'>';
                        var material_type = '<input hidden="hidden" id="id_catmaterial_material_type_'+i+'" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="catmaterial_material_type" value='+item.material_type+' >'

                        // var row = ` <tr data-toggle='close' data-padding='0' data-parent='${item.item_id}_${sessionStorage.clickcount}' id="${item.item_id}_${sessionStorage.clickcount}">
                        //                 <td hidden=hidden>${item.cat_code} - ${item.drawing_no}</td>
                        //                 <td class='text-left bom-sno'>${(i+1)}</td>
                        //                 <td>
                        //                     ${childArrow} ${bom_description} ${itemTypeFlag}
                        //                 </td>
                        //                 <td class='hide'>${item.name}</td>
                        //                 <td class='hsn-wrapper' hidden='hidden'>${hsn_code}</td>
                        //                 <td class='hide'></td>
                        //                 <td>${qty}</td>
                        //                 <td class='stock_qty text-right ${is_stocked}'>
                        //                     ${stock_show}
                        //                 </td>
                        //                 <td class='text-center'>${item.unit}</td>
                        //                 <td hidden='hidden'>
                        //                     <input type='text' id='txtcatunitrate' name ='catmaterial_unitrate' class='form-control text-right bom_po_unit_rate bom_amt_calc mandatory_field' value='${item.price}' maxlength='16' onfocus='setNumberRangeOnFocus(this,10,5)'>
                        //                 </td>
                        //                 <td hidden='hidden'>
                        //                     <input type='text' id='txtcatdiscount' name ='catmaterial_disc' class='form-control text-right bom_po_disc bom_amt_calc' value='0.00' maxlength='6' onfocus='setNumberRangeOnFocus(this,3,2)' onblur='validatePercentage(this, event)'>
                        //                 </td>
                        //                 <td hidden='hidden'>
                        //                     <input type='text' name ='catmaterial_amount' onkeypress ='return validateFloatKeyPress(this,event);' value='0.00' class='form-control text-right textbox-as-label bom_po_total' readonly id='txtcatamount' maxlength='13'>
                        //                 </td>
                        //                 <td class='text-center'>
                        //                     <a href='#' onclick='deleteCatRow(this)'><i class='fa fa-trash-o'></i></a>
                        //                     <a class='edit_quality_inspection table-inline-icon hide' data-tooltip='tooltip' title='Quality Inspection Report' onclick= 'openQuantityInspectionReport(this)'><img src='/site_media/images/quality-inspection.png' /></a>
                        //                 </td>
                        //                 <td hidden=hidden>${item.drawing_no}</td>
                        //                 <td hidden=hidden>${item.enterprise_id}</td>
                        //                 <td hidden=hidden>${item.cat_code}</td>
                        //                 <td hidden=hidden>${alternate_unit_id}</td>
                        //                 <td hidden=hidden>${scale_factor}</td>
                        //                 <td hidden=hidden>${unit_id}</td>
                        //                 <td class='minimum_stock_level hide'>${item.minimum_stock_level}</td>
                        //                 <script type='text/javascript'>
                        //                     $('#id_catmaterial_-${i}-qty').blur(function(){
                        //                         if(parseFloat($('#id_catmaterial_-${i}-qty').val()) > 0) {
                        //                             $('#id_catmaterial_-${i}-row').css({backgroundColor:'#ffe'});
                        //                         }
                        //                         else {
                        //                             $('#id_catmaterial_-${i}-row').css({backgroundColor:''});
                        //                         }
                        //                     });
                        //                 </script>
                        //                 <td hidden=hidden>${item_id}</td>
                        //                 <td hidden=hidden>${material_type}</td>
                        //                 <td hidden=hidden>${is_service}</td>
                        //             </tr>`;
                        var row = ` <tr data-toggle='close' data-padding='0' data-parent='${item.item_id}_${sessionStorage.clickcount}' id="${item.item_id}_${sessionStorage.clickcount}">
                        <td hidden=hidden>${item.cat_code} - ${item.drawing_no}</td>
                        <td class='text-left bom-sno'>${(i+1)}</td>
                        <td>
                            ${childArrow} ${itemTypeFlag}
                        </td>
                        <td class='hide'>${item.name}</td>
                        <td class='hsn-wrapper' hidden='hidden'>${hsn_code}</td>
                        <td class='hide'></td>
                        <td>${qty}</td>
                        <td class='stock_qty text-right ${is_stocked}'>
                            ${stock_show}
                        </td>
                        <td class='text-center'>${item.unit}</td>
                        <td hidden='hidden'>
                            <input type='text' id='txtcatunitrate' name ='catmaterial_unitrate' class='form-control text-right bom_po_unit_rate bom_amt_calc mandatory_field' value='${item.price}' maxlength='16' onfocus='setNumberRangeOnFocus(this,10,5)'>
                        </td>
                        <td hidden='hidden'>
                            <input type='text' id='txtcatdiscount' name ='catmaterial_disc' class='form-control text-right bom_po_disc bom_amt_calc' value='0.00' maxlength='6' onfocus='setNumberRangeOnFocus(this,3,2)' onblur='validatePercentage(this, event)'>
                        </td>
                        <td hidden='hidden'>
                            <input type='text' name ='catmaterial_amount' onkeypress ='return validateFloatKeyPress(this,event);' value='0.00' class='form-control text-right textbox-as-label bom_po_total' readonly id='txtcatamount' maxlength='13'>
                        </td>
                        <td class='text-center'>
                            <a href='#' onclick='deleteCatRow(this)'><i class='fa fa-trash-o'></i></a>
                            <a class='edit_quality_inspection table-inline-icon hide' data-tooltip='tooltip' title='Quality Inspection Report' onclick= 'openQuantityInspectionReport(this)'><img src='/site_media/images/quality-inspection.png' /></a>
                        </td>
                        <td hidden=hidden>${item.drawing_no}</td>
                        <td hidden=hidden>${item.enterprise_id}</td>
                        <td hidden=hidden>${item.cat_code}</td>
                        <td hidden=hidden>${alternate_unit_id}</td>
                        <td hidden=hidden>${scale_factor}</td>
                        <td hidden=hidden>${unit_id}</td>
                        <td class='minimum_stock_level hide'>${item.minimum_stock_level}</td>
                        <script type='text/javascript'>
                            $('#id_catmaterial_-${i}-qty').blur(function(){
                                if(parseFloat($('#id_catmaterial_-${i}-qty').val()) > 0) {
                                    $('#id_catmaterial_-${i}-row').css({backgroundColor:'#ffe'});
                                }
                                else {
                                    $('#id_catmaterial_-${i}-row').css({backgroundColor:''});
                                }
                            });
                        </script>
                        <td hidden=hidden>${item_id}</td>
                        <td hidden=hidden>${material_type}</td>
                        <td hidden=hidden>${is_service}</td>
                    </tr>`;
                        $('#cattable_2 tbody').append(row);
                    });
                    var row = ` <div class='modal-footer'>
                                   <span class="pull-left nonStock-text">  <span class=" nonStock-text non_stock-flag"></span> - NON-STOCKABLE</span>
                                   <span class="pull-left nonStock-text">  <span class=" nonStock-text service-item-flag"></span> - SERVICE</span>
                                    <input type='button' onclick='addInvoice()' class='btn btn-save' id='catAdd' value='Add' />
                                    <a class='btn btn-cancel' id='catCancel' data-dismiss='modal'>Close</a>
                                </div>`;
                    $('#catButton').append(row).addClass('tbl');
                    $("#catalogueModal").modal('show');
                    BOMTotalCalculation();
                    AssignMakeQty();
                    TooltipInit();
                    $("#loadingmessage_changelog_listing_ie").hide();
                }else{

                    // TODO why should I login again here
                    swal(response.response_message, "Kindly <a href='/erp/login/'>Login</a> again.","warning");
                    document.location.href="/erp/login/";
                    return;
                }
				$(".setUnitValue").blur(function(){
					var setID = $(this).closest('tr').attr('id');
					var setValue = $(this).val();
					$('#cattable_2').find("tr[data-child=\""+setID+"\"]").each(function(){
						var setUnitValue = $(this).find('.setUnitValue').data('unitvalue');
						$(this).find('.setUnitValue').val(Number(setValue * setUnitValue));
					});
				});
            }
        });
    }

	$('#invoice_tax_list').change(function(){
		$("#id_invoice_tax-__prefix__-tax_code").val($(this).find("option:selected").attr("value"));
		$("#id_invoice_tax-__prefix__-invoice_id").val($("#id_invoice-id").val());
		$("#id_invoice_tax-__prefix__-enterprise_id").val($("#id_invoice-enterprise_id").val());
	});

	$('#id_invoice-currency_id').change(function(){
        currencyChangeEvent("onchange");
    });

	$('#id_invoice_mat-__prefix__-quantity, #id_invoice_mat-__prefix__-rate, #id_invoice_mat-__prefix__-discount').blur(function () {
		calc_item_value();
	});

	$('#invoiceView').click(function () {
        if($('#id_dc_type').val() == 1){
            $('#template_title').text('Delivery Challan');
        } else{
            $('#template_title').text('Invoice');
        }
	});
    onReadyStatements();
});

$.extend($.expr[":"], {
    "contains-ci": function (elem, i, match, array) {
        return (elem.textContent || elem.innerText || $(elem).text() || "").toLowerCase().indexOf((match[3] || "").toLowerCase()) >= 0;
    }
});

//Calculate the Material Total Amount before adding the main table..
function calc_item_value(){
	var discount =0;
	var amount =0;
	var discount=0;
	var discountValue=0;
	var net_value=0;
	if (parseFloat($("#id_invoice_mat-__prefix__-quantity").val()) !=0 &&  parseFloat($("#id_invoice_mat-__prefix__-rate").val()) !=0)
	{
		if (parseFloat($("#id_invoice_mat-__prefix__-discount").val()) !=0)
		{
			discount = parseFloat($("#id_invoice_mat-__prefix__-discount").val());
			amount =  parseFloat($("#id_invoice_mat-__prefix__-quantity").val()) * parseFloat($("#id_invoice_mat-__prefix__-rate").val())
			discountValue = (amount * discount)/100;
			net_value = parseFloat(amount)-parseFloat(discountValue);

		}else{
			discount = 0
			amount =  parseFloat($("#id_invoice_mat-__prefix__-quantity").val()) * parseFloat($("#id_invoice_mat-__prefix__-rate").val())
			net_value = parseFloat(amount);
		 }
		$("#id_invoice_mat-__prefix__-amount").val(net_value.toFixed(2));
	}
	else
	{
		$("#id_invoice_mat-__prefix__-amount").val(0);
	}
}

function openMailPopup() {
    new Mailer().prepareEmailPopup().getSupplierMailID(id=sessionStorage.invoiceid, type= "invoice").show();
    return false;
}

// This Function is used to calculate the net amount at the time of edit button clicked...
function edit_netamount_calculate(){
	var row_count =$("#invoice_materials_table  > tbody > tr").length;
    for(var i=0;i<row_count;i++){
        $("#id_invoice_mat-"+i+"-amount").val(parseFloat($("#id_invoice_mat-"+i+"-quantity").val()) * parseFloat($("#id_invoice_mat-"+i+"-rate").val()));
	}
}

// Delete The Invoice Material list Items
function deleteInvoiceMaterial(catMatFormId) {
    if($("#id_invoice-id").val() !="" && "Issue" == $("#id_invoice-type").val()) {
        var item_json = {"item_id": document.getElementById('id_' + catMatFormId + '-item_id').value,
                        "make_id": document.getElementById('id_' + catMatFormId + '-make_id').value,
                        "is_faulty": document.getElementById('id_' + catMatFormId + '-is_faulty').value,
                        "invoice_id": $("#id_invoice-id").val()};
        $.ajax({
            url : "/erp/sales/json/invoice/check_against_issue_return_material/",
            type : "POST",
            dataType: "json",
            data :  item_json,
            success : function(response) {
                    if (response > 0){
                        swal({title: "", text: "Material has been returned against this issue. <br /> So this material can't be delete.", type: "error"});
                    } else{
                        swal({
                            title: "Are you sure?",
                            text: "Do you want to delete this Item!",
                            type: "warning",
                            showCancelButton: true,
                            confirmButtonColor: "#209be1",
                            confirmButtonText: "Yes, delete it!",
                            closeOnConfirm: true
                        },
                        function(){
                            var deleteFlag = document.getElementById('id_' + catMatFormId + '-DELETE');
                            var deleteRow = document.getElementById(catMatFormId);
                            deleteFlag.checked = true;
                            deleteRow.style.display = 'none';
                            calculateGrandTotal();
                            assignSerialNumber();
                            isFormChanged = true;
                        });
                    }
            },
            error : function(xhr,errmsg,err) {
                console.warning(xhr.status + ": " + xhr.responseText);
            }
        });
    }
    else {
        swal({
            title: "Are you sure?",
            text: "Do you want to delete this Item!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#209be1",
            confirmButtonText: "Yes, delete it!",
            closeOnConfirm: true
        },
        function(){
            var deleteFlag = document.getElementById('id_' + catMatFormId + '-DELETE');
            var deleteRow = document.getElementById(catMatFormId);
            deleteFlag.checked = true;
            deleteRow.style.display = 'none';
            deleteRow.classList.add("deleted_invoice");
            if($("#id_invoice-goods_already_supplied").is(":checked")) {
                updateInvoiceTableColspan();
                $("#id_"+catMatFormId+"-quantity").blur();
                isAllconsolidatedRowDeleted(catMatFormId);
            }
            calculateGrandTotal();
            assignSerialNumber();

            isFormChanged = true;
        });
    }
}

function isAllconsolidatedRowDeleted(catMatFormId) {
    var currentElement = $("#invoice_materials_table").find("#"+catMatFormId).attr("data-row");
    if($("#invoice_materials_table ."+currentElement).not('.deleted_invoice').length <= 0) {
        $("#invoice_materials_table tr[consolidated-for='"+currentElement+"']").remove();
    }
}

function updateInvoiceTableColspan() {
    $("#invoice_materials_table").find(".consolidated_row").each(function(){
        var consolidatedTxt = $(this).attr("consolidated-for");
        var consolidatedLength = $("#invoice_materials_table").find("."+consolidatedTxt).not(".deleted_invoice").length;
        $(this).find(".consolidated_price_column").attr("data-rowspan", Number(consolidatedLength+1));
        if($("#invoice_materials_table").find("."+consolidatedTxt).is(":visible")) {
            $(this).find(".consolidated_price_column").attr("rowspan", Number(consolidatedLength+1));
        }
    })
}

function deleteInvoiceTax(catTaxFormId) {
	var deleteFlag = document.getElementById('id_' + catTaxFormId + '-DELETE');
	var deleteRow = document.getElementById(catTaxFormId);
	deleteFlag.checked = true;
	deleteRow.style.display = 'none';
	isFormChanged = true;
}

function removeInvoiceTax(tax_code){
	var data_count = document.getElementById('invoice_tax_table').rows.length - 1;
	var row_id=0;
	var param_tax;
	for(i=0;i<=data_count;i++){
		if($("#id_invoice_tax-"+i+"-tax_code").val()== tax_code)
			row_id = i;
    }
	tax_rows = document.getElementsByName('tr_' + tax_code);
	for(i=tax_rows.length - 1; i>=0; i--){
		// Removed in reverse order as the removal renumbers the RowIndices immediately
		document.getElementById("invoice_tax_table").deleteRow(tax_rows[i].rowIndex);
	}
	$("#invoice_tax_list option[value='"+ tax_code +"']").show();
	$('.chosen-select').trigger('chosen:updated');
	param_tax ="invoice_tax-"+row_id;

	deleteInvoiceTax(param_tax);
	calculateGrandTotal();
	isFormChanged = true;
    // Re-show the tax in the Tax list
}

function non_excise_fields(){
    $(".excise_fields").hide();
    $("#lbl_special_instruction").text("Other Reference")
    $("#div_inv_mat_discount").show();
    $("#div_remarks").show();
    $("#tbl_head_discount").show();
    $("#id_assessable_val").hide();
    $("#item_type").attr('checked', false);
}

function excise_fields(){
    $(".excise_fields").show();
    $("#lbl_special_instruction").text("Special Instructions");
    $("#div_inv_mat_discount").show();
    $("#div_remarks").show();
    $("#tbl_head_discount").show();
    $("#id_assessable_val").show();
    $("#item_type").attr('checked', false);
}

/*************************** Remove Non Excise Tax Start Here ***********************************/
function removeNonExciseInvoiceTax(tax_code){
	var data_count = document.getElementById('non_excise_invoice_tax_table').rows.length - 1;
	var row_id=0;
	var param_tax;
	for(i=0;i<=data_count;i++){
		if($("#id_invoice_tax-"+i+"-tax_code").val()== tax_code)
			row_id = i;
		}
	tax_rows = document.getElementsByName('tr_' + tax_code);
	for(i=tax_rows.length - 1; i>=0; i--){
		// Removed in reverse order as the removal renumbers the RowIndices immediately
		document.getElementById("invoice_tax_table").deleteRow(tax_rows[i].rowIndex);
	}
	$("#invoice_tax_list option[value='"+ tax_code +"']").show();
	param_tax ="invoice_tax-"+row_id;

	deleteInvoiceTax(param_tax);
	calculateGrandTotal();
    // Re-show the tax in the Tax list
}/*************************** Remove Non Excise Tax End Here ***********************************/

/* This Method is used to add Material and update existing quantity of the material and price */
$(document).ready(function () {
    if($('#id_invoice-type').val()==='GST'){
        $('#id_is_customer').attr('checked', true);
        $("#div_customer").removeClass("hide");
    }
    if($('#id_invoice-type').val()==='DC'){
        $('#id_is_supplier').attr('checked', true);
        $("#div_supplier").removeClass("hide");
    }
    $('#explode_bom').hide();
    materialListBlurEvent('materialrequired');
    if($("#id_invoice-party_id").val()=='None'){
        //$("#id_invoice-party_id").val($('#id_invoice-party_id optgroup:eq(0) option:eq(0)').val()).trigger("chosen:updated");
    }

    var url = window.location.href;
    url = url.substring(7);
    window.urlpath = url.substring(0, url.indexOf('/'));
    transformToDummyForm('invoice_mat');
    transformToDummyForm('invoice_tax');
    populateUnitOptions("",'id_invoice_mat-__prefix__-all_units');
    loadMaterial("onload");
    checkUserSettingsFlag();
    $("#id_invoice-type").attr('readonly','readonly');
    $("#id_invoice-type").attr('title','Loading Please Wait');
    $('#id_invoice_party_id_chosen').addClass('div-disabled');
    if($('#id_invoice-type option:selected').val() == "JDC"){
        $("#id_txt_po_no_div").hide();
        $("#id_txt_po_id_div").show();
    }
    if($('#id_invoice-type').val() == "Issue" || $('#id_invoice-type').val() == "JDC" || $('#id_invoice-type').val() == "DC" ) {
        $('#div_order_no').addClass('hide');
    }else{
        $('#div_order_no').removeClass('hide');
        if ($('#id_invoice-type').val() == "JIN"){
            $(".oaGrn_text").text("GRN");
            $('.oa_grn_no').text('GRN');
            $(".oa_party_dc_no").text('Party DC ')
            $("#OAMaterial .modal-title").text("View GRN Materials")
            $('.invoice_item_table').addClass('hide');
        }else{
            $(".oaGrn_text").text("OA");
            $(".oa_party_dc_no").text('OA ')
            $('.oa_grn_no').text('OA');
            $("#OAMaterial .modal-title").text("View OA Materials");
            $('.invoice_item_table').removeClass('hide');
        }
    }
    $("#se_party").val($("#id_invoice-party_id").val());
    $("#id_invoice-goods_already_supplied").change(function(){
        goodsSuppliedChange();
    });
	$("#id_invoice-type").change(function(){
		InvoiceTypeChangeEvent();
		if ($("#id_dc_type").val().toLowerCase() == "dc"){
    		InvoicePartyChangeEvent();
        }
		if($("#id_invoice-goods_already_supplied").is(":checked")) {
            $("#invoice_materials_table").find(".consolidated_row").each(function(){
                var element = $(this).closest('tr').attr('consolidated-for');
                deleteInvoiceMaterials(element);
            });
        }else{
            deleteInvoiceMaterials(null);
        }
	});
	if ($("#id_dc_type").val() != "internal"){
            if($("#id_invoice-currency_id option:selected").text()!=$("#home_currency_id").val()){
                $('#div_con_rate').removeClass("hide");
            }else{
                $('#div_con_rate').addClass("hide");
		        $('#id_invoice-currency_conversion_rate').val('1.0');
            }
    }
    var item_json = [];
    $(".stock_qty").each(function(){
        if($(this).closest("tr").find(".is_stock_material").find("input").val() != "0") {
            var item = {"item_id": $(this).attr("item_id"), "make_id": $(this).attr("make_id"),
             "is_faulty": $(this).attr("is_faulty"),  "invoice_id": "", "alternate_unit_id": 0};
            item_json.push(JSON.stringify(item))
        }
    });
    if (item_json.length > 0){
        $.ajax({
            url : "/erp/stores/json/issue/closingstock/",
            type : "POST",
            dataType: "json",
            data :  {"mat_list[]": item_json, "issued_on": null, "location_id":$('#id_invoice-location_id option:selected').val()},
            success : function(response) {
                $.each(response['closing_stock'], function(i, item){
                    $(".stock_qty[item_id='" + item.item_id + "']").html(item.closing_qty);
                });
            },
            error : function(xhr,errmsg,err) {
                console.warning(xhr.status + ": " + xhr.responseText);
            }
        });
    }


	$("#id_invoice-job_po_id").change(function() {
        $("#loading").show();
	    removeRows();
	    var po_id = $("#id_invoice-job_po_id").val();
	    var issue_id = $("#search_invoice_id").val();
	    const text = $("#id_invoice-job_po_id option:selected").text();
	    if( text.includes('/MRS/')){
	        $('#mrs_type').val(1);
	    }
	    else{
	        $('#mrs_type').val(0);
	    }
	    if (po_id !="" && po_id != 0){
            $.ajax({
                url: "/erp/sales/json/invoice/load_po_tags/",
                type: "post",
                datatype: "json",
                data: {po_id:po_id},
                success: function (response) {
                    $("#ul-tagit-display .li-tagit-display").remove();
                    $.each(response, function (i, item) {
                        generateFormsetForm('tag');
                        var index = parseInt(parseInt($('#id_tag-TOTAL_FORMS').val()) - 1);
                        $('#id_tag-' + index + '-tag_label').html(item.tag_name);
                        $('#id_tag-' + index + '-tag').val(item.tag_name);
                        create_delete_tag_button();
                    });
                }
            });
            $.ajax({
                url: "/erp/sales/json/invoice/load_po_date/",
                type: "post",
                datatype: "json",
                data: {po_id:po_id},
                success: function (response) {
                    const text = $("#id_invoice-job_po_id option:selected").text();
                    $.each(response, function (i, item) {
                        $('#invoice_po_date').val(moment(item.po_date).format('MMM DD, YYYY'));
                        UdateCustomDatePicker('invoice_po_date');
                        if (text.includes('/MRS/')) {
                            $('#id_invoice-project_code').val(item.project)
                            $('#id_invoice-project_code').trigger("chosen:updated");
                            $("#id_invoice_issued_to_chosen").addClass('div-disabled');
                        }
                        $("#id_invoice-issued_to").val(item.issue_to.toLowerCase()).trigger("chosen:updated");
                    });
                }
            });
            localStorage.removeItem('PPStockDetails');
            localStorage.removeItem('MRSStockDetails');
            $.ajax({
                    url: "/erp/sales/json/invoice/getPPStockqty/",
                    type: "post",
                    datatype: "json",
                    data: {po_id:po_id,issue_id:issue_id},
                    async: false,
                    success: function (response) {
                            localStorage.setItem("PPStockDetails",JSON.stringify(response));
                    }
            });
            $.ajax({
                url: "/erp/sales/json/invoice/getMRSMaterials/",
                type: "post",
                datatype: "json",
                data: {po_id:po_id},
                async: false,
                success: function (response) {
                        localStorage.setItem("MRSStockDetails",JSON.stringify(response));
                }
            });
                const text = $("#id_invoice-job_po_id option:selected").text();
                var issue_type = "oa"
                if (text.includes('/MRS/')) {
                    urlString = "/erp/sales/json/invoice/getMRSMaterials/";
                    issue_type = "mrs"
                    $("#id_issue_type").val("mrs");
                    $('#id_invoice_location_id_chosen').removeClass('div-disabled');
                    $('id_invoice_project_code_chosen').addClass('div-disabled');
                } else {
                    urlString = "/erp/sales/json/invoice/getPOMaterials/";
                    issue_type = "oa"
                    $("#id_issue_type").val("oa");
//                    $('#id_invoice_location_id_chosen').addClass('div-disabled');
                    $('id_invoice_project_code_chosen').removeClass('div-disabled');
                }
            location_id = $('#id_invoice-location_id option:selected').val()
            $.ajax({
                url: urlString,
                type: "post",
                datatype: "json",
                data: {po_id:po_id,'issued_on':$('#id_invoice-issued_on').val(),'invoice_id':$("#id_invoice-id").val(),
                'location_id':location_id,'type': $('#id_invoice-type option:selected').val()},
                success: function (response) {
                    if (response.response_code!=400){
                        $("#cattable_2").find("tr:gt(0)").remove();
                        $("#catButton").find('.modal-footer').remove();
                        const text = $("#id_invoice-job_po_id option:selected").text();
                        if (text.includes('/MRS/')) {
                            if (response){
                                var project_id = response[0]['project_id'];
                                $("#id_invoice-project_code").val(project_id).trigger("chosen:updated");

                            }
                        }
                        if(response.length > 0){
                            $.each(response, function (i, item) {
                                if(typeof(Storage) !== "undefined") {
                                    if (sessionStorage.clickcount) {
                                        sessionStorage.clickcount = Number(sessionStorage.clickcount)+1;
                                    } else {
                                        sessionStorage.clickcount = 1;
                                    }
                                }
                                var childArrow ="";
                                var materialName = item.name;
                                if(item.drawing_no != "" && item.drawing_no !=null) {
                                    materialName = `${materialName} - ${item.drawing_no}`;
                                }
                                var bom_description= ""
                                if(item.is_service == true){
                                    bom_description = `<span class="service-item-flag"></span>`;
                                }

                            var makes = "-NA-";
                            stock_qty = 0;
                            var selected_make ="";
                            if (item.makes.length > 0){
                                makes = "<select class='form-control set-make-qty parent_bom_delete'  name='make' id='txtmake" + i + "'>";
                                for (j = 0; j <= item.makes.length - 1 ; j++) {
                                    if (j==0){ stock_qty = item.makes[j][2] }
                                    makes += "<option value='" + item.makes[j][0] + "' data-quantity='" + item.makes[j][2] + "'> " + item.makes[j][1] + " </option>";
                                    selected_make = constructDifferentMakeName(item.makes[j][1]);
                                }
                                makes += "</select>";
                            }
                            var item_id = "";
                            var is_stocked = ""
                            item_id = item.item_id
                            if (selected_make !=""){
                                materialName = materialName + " ["+selected_make+"]";
                            }
                            if (item.material_type==1){
                                classname='';
                                is_stocked = "stockable";
                            }
                            if(item.child == 1) {
                                childArrow = `<i class='fa fa-caret-right fa-for-arrow'>
                                                <a class='material-child-link' onclick='return appendMaterial("${item.cat_code}", "${sessionStorage.clickcount}", "${item.cat_code}_${sessionStorage.clickcount}", "${item.alternate_unit_id}", "${location_id}")'>
                                                    ${materialName}
                                                </a>
                                              </i>`;
                            }
                            else {
                                childArrow = materialName;
                            }
                            materialName = childArrow;
                            var autofill_qty = 0
                            if (issue_type=='oa'){
                                var pp_against_issue_qty = 0;
                                var production_plan_qty = 0;
                                var allocation_qty = 0

                                if ($('#id_invoice-type option:selected').val() != 'JDC') {
                                    pp_against_issue_qty = item.issued_qty;
                                    production_plan_qty = item.required_qty;
                                    allocation_qty = item.allocated_qty;
                                    autofill_qty = item.allocated_qty - item.issued_qty
                                    stock_qty = item.allocated_qty - item.issued_qty
                                }else{
                                    if (localStorage.getItem('PPStockDetails') !== null) {
                                        var PPStockDetails = JSON.parse(localStorage.getItem("PPStockDetails"));
                                        pp_against_issue_qty = PPStockDetails.issue_qty;
                                        production_plan_qty = PPStockDetails.po_qty;
                                        autofill_qty = production_plan_qty - pp_against_issue_qty
                                    }
                                }
                                if(item.location_id){
                                    $('#id_invoice-location_id').val(item.location_id).trigger("chosen:updated");
                                }
                                }else{
                                    var mrs_against_issue_qty = 0;
                                    var mrs_qty = 0;
                                    mrs_against_issue_qty = item.issue_qty
                                    mrs_qty = item.quantity
                                    autofill_qty = mrs_qty - mrs_against_issue_qty
                                    $("#id_invoice-issued_to").val(item.issue_to.toLowerCase()).trigger("chosen:updated");
                                    $("#id_invoice_issued_to_chosen").addClass('div-disabled');

                            }
                            const po_number = $("#id_invoice-job_po_id option:selected").text();
                            if (po_number.includes('JO')) {
                                autofill_qty = item.qty
                            }
                            var hsn_code = `<input id="id_catmaterial_-${i}-hsn_code" type="text" class="form-control text-left" maxlength="9" name="catmaterial_hsn_code" autocomplete="off" onchange="validateHsnWithSuggestion(this, 'id_catmaterial_-${i}-qty');"  onkeypress ="validateStringOnKeyPress(this,event,'hsn_specialChar');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" value="${item.hsn_code}">`;
                            var qty = `<input type="text" id="id_catmaterial_-${i}-qty" name="catmaterial_qty" class="form-control text-right setUnitValue text-left bom_po_qty bom_amt_calc mandatory_field" maxlength="16" onchange="validateHsnWithSuggestion($('#id_catmaterial_-${i}-hsn_code'), 'id_catmaterial_-${i}-qty');" onblur="checkQty(this, 'id_catmaterial_-${i}-qty');" onfocus="setNumberRangeOnFocus(this,12,3);removeErrorBorder('id_catmaterial_-${i}-qty');" autocomplete="off" value=${autofill_qty}>`;
                            var unit_id = '<input hidden="hidden" id="id_pomaterial_unit_id_'+i+'" type="text" class="form-control text-left " name="pomaterial_unit_id" value='+item.unit_id+' >';
                            var item_id_field = '<input id="id_catmaterial_-'+i+'-item_id" type="text"  name="catmaterial_item_id" value='+ item_id +'>';
                            var alternate_unit_id= '<input hidden="hidden" id="id_catmaterial_alternate_unit_id_'+i+'" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="catmaterial_alternate_unit_id" value='+item.alternate_unit_id+' >';
                            var scale_factor= '<input hidden="hidden" id="id_catmaterial_scale_factor_'+i+'" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="catmaterial_scale_factor" value='+item.scale_factor+' >';
                            var material_type = '<input hidden="hidden" id="id_catmaterial_material_type_'+i+'" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="catmaterial_material_type" value='+item.material_type+' >';
                            var is_service = '<input hidden="hidden"  type="text"  name="catmaterial_is_service" value='+ item.is_service +'>';
                            var oa_id = '<input hidden="hidden"  type="text"  name="catmaterial_oa_id" value='+ item.oa_id +'>';
                            var itemTypeFlag ="";
                            if(item.is_service == 1){
                               itemTypeFlag += `<span class="service-item-flag"></span>`;
                            }
                            if (item.material_type!=1 && item.is_service != 1){
                               itemTypeFlag += `<span class="non_stock-flag"></span>`;
                            }
                            if(issue_type=='oa'){
                                qty_label = `<span class='qty-label hide' style="background: rgba(0, 0, 0,0.1) !important;border-radius: 50px;padding: 4px 8px 2px;float: right;margin-top: 5px;font-weight: 500;"><span id="issued_qty">${Number(pp_against_issue_qty).toFixed(3)}</span><span class=" nonStock-text issue-item-flag"></span>&nbsp;/<span class="allocation-qty">${Number(allocation_qty).toFixed(3)}</span><span class=" nonStock-text allocated-item-flag"></span>&nbsp;/${Number(production_plan_qty).toFixed(3)}<span class=" nonStock-text required-item-flag"></span>&nbsp;<input type='hidden' id="allocated_qty" value='${Number(allocation_qty).toFixed(3)}' /></span>`
                            }
                            else {
                                qty_label = `<span class='qty-label hide' style="background: rgba(0, 0, 0,0.1) !important;border-radius: 50px;padding: 4px 8px 2px;float: right;margin-top: 5px;font-weight: 500;">${Number(mrs_against_issue_qty).toFixed(3)} / <span id="mrs_qty">${Number(mrs_qty).toFixed(3)}</span></span>`
                            }
                            var row = ` <tr data-toggle='close' data-padding='0' data-parent='${item.cat_code}_${sessionStorage.clickcount}' id="${item.cat_code}_${sessionStorage.clickcount}">
                                            <td hidden=hidden>${item.cat_code} - ${item.drawing_no}</td>
                                            <td class='text-center bom-sno'>${(i+1)}</td>
                                            <td class="for_wo_no"> ${materialName} ${itemTypeFlag}</td>
                                            <td class='hide'>${item.name}</td>
                                            <td class="td-hsn-code hsn-wrapper for_dc">${hsn_code}</td>
                                            <td class='hide'>${makes}</td>
                                            <td class="for_wo_no">${qty} ${qty_label}</td>
                                            <td class='stock_qty ${is_stocked} text-right'>
                                                <span data-placement='left' data-tooltip='tooltip' title='MSL: ${item.minimum_stock_level}'>
                                                    ${stock_qty}
                                                <span>
                                            </td>
                                            <td class='text-center for_wo_no'>${item.unit}</td>
                                            <td class="for_dc">
                                                <input type='text' id='txtcatunitrate' name='catmaterial_unitrate' class='form-control text-right bom_po_unit_rate bom_amt_calc mandatory_field' value='${item.price}' maxlength='16' onfocus="setNumberRangeOnFocus(this,10,5)">
                                            </td>
                                            <td class="for_dc">
                                                <input type='text' id='txtcatdiscount' name='catmaterial_disc' class='form-control text-right bom_po_disc bom_amt_calc' value='0.00' maxlength='6' onfocus='setNumberRangeOnFocus(this,3,2)' onblur='validatePercentage(this, event)'>
                                            </td>
                                            <td class="for_dc">
                                                <input type='text' id='txtcatamount' name='catmaterial_amount' class='form-control text-right textbox-as-label bom_po_total' value='0.00' readonly='readonly' >
                                            </td>
                                            <td class='text-center'>
                                                <a href='#' onclick='deleteCatRow(this)'>
                                                    <i class='fa fa-trash-o'></i>
                                                </a>
                                            </td>
                                            <td hidden=hidden>${item.item_id}</td>
                                            <td hidden=hidden>${item.enterprise_id}</td>
                                            <td hidden=hidden>${item.cat_code}</td>
                                            <td hidden=hidden>${alternate_unit_id}</td>
                                            <td hidden=hidden>${scale_factor}</td>
                                            <td hidden=hidden>
                                                <input type='text' name='catmaterial_unit_id' value='${item.unit_id}'>
                                            </td>
                                            <td class='minimum_stock_level hide'>${item.minimum_stock_level}</td>
                                            <script type='text/javascript'>
                                            if($('#id_invoice-type').val() == "Issue"){
                                               $(".qty-label").removeClass('hide');
                                               }
                                                $('#id_catmaterial_-${i}-qty').blur(function(){
                                                    if(parseFloat($('#id_catmaterial_-${i}-qty').val()) > 0) {
                                                        $('#id_catmaterial_-${i}-hsn_code').addClass('mandatory_field');
                                                        $('#id_catmaterial_-${i}-qty').closest('tr').css({backgroundColor:'#ffe'});
                                                    }
                                                    else {
                                                        $('#id_catmaterial_-${i}-hsn_code').removeClass('mandatory_field error-border');
                                                        $('#id_catmaterial_-${i}-qty').closest('tr').css({backgroundColor:''});
                                                    }
                                                });
                                            </script>
                                            <td hidden=hidden>${item_id_field}</td>
                                            <td hidden=hidden>${material_type}</td>
                                            <td hidden=hidden>${item.drawing_no}</td>
                                            <td hidden=hidden>${is_service}</td>
                                            <td hidden=hidden>${oa_id}</td>
                                        </tr>`;
                            $('#cattable_2 tbody').append(row);
                            woForIssue();
                        });

                        var row = ` <div class='modal-footer'>
                                        <span class="pull-left nonStock-text">  <span class=" nonStock-text non_stock-flag"></span> - NON-STOCKABLE</span>
                                        <span class="pull-left nonStock-text">  <span class=" nonStock-text service-item-flag"></span> - SERVICE</span>
                                        <span class="pull-left nonStock-text">  <span class=" nonStock-text issue-item-flag"></span> - ISSUE</span>
                                        <span class="pull-left nonStock-text">  <span class=" nonStock-text allocated-item-flag"></span> - ALLOCATED</span>
                                        <span class="pull-left nonStock-text">  <span class=" nonStock-text required-item-flag"></span> - REQUIRED</span>
                                        <input type='button' onclick='addInvoice()' class='btn btn-save' id='catAdd' value='Add' />
                                        <a class='btn btn-cancel' id='catCancel' data-dismiss='modal'>Close</a>
                                    </div>`;
                        $('#catButton').append(row);
                        $("#catalogueModal").modal('show');
                        BOMTotalCalculation();
                        listTableHoverIconsInit('invoice_materials_table');
                        AssignMakeQty();
                        TooltipInit();
                    }
                    else{
                        swal("", "There are no materials allocated for this PP Number", "error")
                    }
                        $("#loading").hide();
                    }
                }
            });
            if($("#id_invoice-type").val() == "Issue" ){
                $("#add_invoice_item").addClass('hide');
                $('#materials_add').addClass('div-disabled');
            }
        } else {
            $("#loading").hide();
            $("#id_invoice_issued_to_chosen").removeClass('div-disabled');
            $('#materials_add').removeClass('div-disabled');
            $("#add_invoice_item").removeClass('hide');
        }
	});

	function BOMTotalCalculation() {
		$('.bom_amt_calc').blur(function(){
			var qty = Number($(this).closest('tr').find('.bom_po_qty').val());
			var rate = Number($(this).closest('tr').find('.bom_po_unit_rate').val());
			var disc = Number($(this).closest('tr').find('.bom_po_disc').val());
			var discAmt = ((rate * qty) * disc)/100;
			var total = (rate * qty) - discAmt;
			$(this).closest('tr').find('.bom_po_total').val(total.toFixed(2));
		});
	}

    function InvoiceTypeChangeEvent(){
        if($("#id_invoice-type").val() == "BoS" && $("#id_invoice-type option[selected]").val() != "BoS"){
            $(".for-bos").addClass('hide');
			var subTotal = $("#invoice_tot").attr("value");
			var grandTotal = $("#grand_total_value").attr("value") - $("#id_invoice-round_off").val();
			if(Number(subTotal) != Number(grandTotal)) {
				swal({
					title: "Unable to change Type",
					text: "Bill of Supply should not have taxes. Please remove the taxes to change the type.",
					type: "warning",
					allowEscapeKey:false
				}, function(){
					var prevValue = $("#id_invoice-type option[selected]").val();
					$("#id_invoice-type").val(prevValue);
				});
				return false;
			}
		}
		else{
		    $(".for-bos").removeClass('hide');
		}
        if($('#id_invoice-type option:selected').val() == "Issue" || $('#id_invoice-type option:selected').val() == "JDC" || $('#id_invoice-type option:selected').val() == "DC" ) {
            $('#div_order_no').addClass('hide');
            $(".oaGrn_text").text("OA");
            $(".oa_party_dc_no").text('OA ')
            $('.oa_grn_no').text('OA');
            $("#OAMaterial .modal-title").text("View OA Materials");
            $('.invoice_item_table').removeClass('hide');
        }else{
            $('#div_order_no').removeClass('hide');
           if ($('#id_invoice-type option:selected').val() == "JIN"){
                $(".oaGrn_text").text("GRN");
                $('.oa_grn_no').text('GRN');
                $(".oa_party_dc_no").text('Party DC ')
                $("#OAMaterial .modal-title").text("View GRN Materials")
                $('.invoice_item_table').addClass('hide');
            }else{
                $(".oaGrn_text").text("OA");
                $(".oa_party_dc_no").text('OA ')
                $('.oa_grn_no').text('OA');
                $("#OAMaterial .modal-title").text("View OA Materials");
                $('.invoice_item_table').removeClass('hide');
            }
        }
		if($('#id_invoice-type option:selected').val() == "JDC") {
            $('#chkreturn').prop('checked', true).attr('disabled','disabled');
            $('#chkreturnothers').prop('checked', true).attr('disabled','disabled');
        }
        else{
            $('#chkreturn').attr('checked', false).removeAttr('disabled');
            $('#chkreturnothers').attr('checked', false).removeAttr('disabled');
        }
	    if($('#id_invoice-type option:selected').val() == "Excise" || $('#id_invoice-type option:selected').val() == "Trading"){
			excise_fields();
			$("#materials_add").show();
			$(".taxable").show();
			$(".packing_diff_line").attr("colspan","15");
		}
        else if($('#id_invoice-type option:selected').val() == "Service"){
            non_excise_fields();
			$("#materials_add").show();
			$(".taxable").show();
			$(".packing_diff_line").attr("colspan","15");
		}
		else{
			excise_fields();
            $("#materials_add").show();
            if($('#id_invoice-type option:selected').val() == "BoS"){
                $(".taxable").hide();
                $(".packing_diff_line").attr("colspan","9")
            }else{
                $(".taxable").show();
                $(".packing_diff_line").attr("colspan","15");
            }
        }
        if($('#id_invoice-type option:selected').val() == "JDC"){
			$("#id_txt_po_no_div").hide();
			$("#id_txt_po_id_div").show();
			$('#id_party_label').html("Supplier<span class='mandatory_mark'>*</span>");
		}else{
		    $("#id_txt_po_no_div").show();
			$("#id_txt_po_id_div").hide();
			if($('#id_invoice-type option:selected').val() == "DC"){
			    $('#id_party_label').html("Party");
			}else{
			    $('#id_party_label').html("Customer<span class='mandatory_mark'>*</span>");
			}
		}
		$('#id_invoice-job_po_id').html('');
        $('#id_invoice-job_po_id').trigger('chosen:updated');
		type = $('#id_invoice-type option:selected').val();
    }

    function lastUsedSupplierDetails(){
        if($("#search_invoice_id").val()=="None" || $("#search_invoice_id").val()==""){
            var selected_supplier_value = null;
            if ($("#id_invoice-party_id option:selected").val() != undefined){
                selected_supplier_value = $("#id_invoice-party_id option:selected").val().split("[::]")[0];
            }
            if (selected_supplier_value != null){
                $.ajax({
                    url: "/erp/stores/json/invoiceLastUsedSupplierDetails/",
                    type: "post",
                    datatype:"json",
                    data: {party_id: selected_supplier_value},
                    success: function(response){
                        party_details = response['party_details']
                        if(party_details['packing_description']){
                                remarks = JSON.parse(party_details['packing_description'])
                        }
                        if(Array.isArray(remarks)){
                            remarks.sort(function(a, b){
                                var dateA=new Date(a.date), dateB=new Date(b.date)
                                return dateA-dateB //sort by date ascending
                            });
                            remarks =remarks[remarks.length - 1];
                            $("#id_invoice-remarks").val(remarks['remarks']);
                        }
                        else if(typeof remarks === 'string' || remarks instanceof String){
                            $("#id_invoice-remarks").val(remarks);
                        }else{
                                $("#id_invoice-remarks").val('');
                        }
                        $("#id_invoice-transport_mode").val(party_details['transport_mode'])
                        $("#id_invoice-deliver_to").val(party_details['deliver_to'])
                        $("#id_invoice-gstin").val(party_details['gstin'])
                        if($("#promote_inv_se_code").val() == "" ) {
                            $("#id_invoice-payment_terms").val(party_details['payment_terms'])
                        }
                    }
                });
                //ChangePartyCurrency($("#id_invoice-party_id option:selected").val(),"id_invoice-currency_id");
                $("#id_currency_choosen").text($('#id_invoice-currency_id').find("option:selected").text());
                if ($("#id_invoice-party_id").val() !="add_new_party"){
                    $("#id_invoice-ship_to_name").val($("#id_invoice-party_id option:selected").text())
                }
            }
        }
    }

    $("select#id_invoice-party_id").change(function(){
        lastUsedSupplierDetails();
        var party_id = $("#id_invoice-party_id").val();
        if (party_id !="add_new_party"){
            $("#id_invoice-ship_to_name").val($("#id_invoice-party_id option:selected").text())
        }
     });

    lastUsedSupplierDetails();
    function InvoicePartyChangeEvent() {
        var party_id = $("#id_invoice-party_id").val();
        var invoice_id = $("#search_invoice_id").val()
        $("#id_invoice-type").attr('readonly','readonly');
        $("#id_invoice-type").attr('title','Loading Please Wait');
        $('#id_invoice_party_id_chosen').addClass('div-disabled');
        if($('#id_invoice-type option:selected').val() == "JDC"){
            $("#id_invoice-job_po_id").next(".chosen-container").addClass("div-disabled").attr("title", "Loading... Please wait");
            $.ajax({
                url: "/erp/stores/json/getjobpo/",
                type: "post",
                datatype: "json",
                data: {party_id: party_id,invoice_id:invoice_id, issued_on: $('#id_invoice-issued_on').val()},
                success: function (data) {
                    $("#id_invoice-type").removeAttr('readonly');
                    $("#id_invoice-type").removeAttr('title');
                    $('#id_invoice_party_id_chosen').removeClass('div-disabled');
                    $('select[name=invoice-job_po_id]').html('');
                    $('select[name=invoice-job_po_id]').append($('<option></option>').val("").html("--select--"));
                    $.each(data, function(index, text){
                        $('select[name=invoice-job_po_id]').append(
                            $('<option></option>').val(text.value).html(text.id)
                        );
                    });

                    if($("#search_invoice_id").val()!="None"){
                        $.ajax({
                            url: "/erp/stores/json/loadpolist/",
                            type: "post",
                            datatype:"json",
                            data: {invoice_id: invoice_id},
                            success: function(response){
                                try{
                                    if (response.po_codes != null && response.po_codes.length != 0){
                                         setTimeout(function(){
                                            $("#id_invoice-job_po_id").val(response.po_codes[0].po_id);
                                            $('#id_invoice-job_po_id').trigger("chosen:updated");
                                        },500);
                                    }
                                } catch (e) {
                                    console.log(e)
                                }
                            }
                        });
                    }
                    $('.chosen-select').trigger('chosen:updated');
                    $("#id_invoice-job_po_id").next(".chosen-container").removeClass("div-disabled").attr("title", "");
                    },
                    error: function() {
                        $("#id_invoice-job_po_id").next(".chosen-container").removeClass("div-disabled").attr("title", "");
                        $("#id_invoice-type").removeAttr('readonly');
                        $("#id_invoice-type").removeAttr('title');
                        $('#id_invoice_party_id_chosen').removeClass('div-disabled');
                    }
            });
        } else if($('#id_invoice-type option:selected').val() != "JDC" && !$("#id_invoice-goods_already_supplied").is(":checked")) {
            $("#id_invoice-order_accept_no_display").next(".btn-group").find(".multiselect").addClass("disabled").attr("title", "Loading... Please wait");
            invoice_id = $("#search_invoice_id").val()
            if ($('#id_invoice-type option:selected').val()=="JIN"){
                url = "/erp/stores/json/pending_grn_oa/";
            }else{
                url = "/erp/stores/json/getpartyoa/";
            }
            $.ajax({
                url: url,
                type: "post",
                datatype: "json",
                data: {party_id: party_id, invoice_id: invoice_id},
                success: function (response) {
                    $("#id_invoice-type").removeAttr('readonly');
                    $("#id_invoice-type").removeAttr('title');
                    $('#id_invoice_party_id_chosen').removeClass('div-disabled');
                    $('#id_invoice-order_accept_no_display').multiselect('destroy');
                    $('select[name=invoice-order_accept_no_display]').html('');
                    if (response.response_message == "Success") {
                        $.each(response.oa_list, function(index, oa) {
                            $('select[name=invoice-order_accept_no_display]').append(
                                $(`<option value="${oa.id}" data-invoice_against="${oa.invoice_against}">${oa.code}</option>`)
                            );
                        });
                    } else {
                        // TODO show error if necessary
                        swal("", response.response_message, "error");
                    }
                    if($("#search_invoice_id").val() != "") {
                        if ($('#id_invoice-type option:selected').val()=="JIN"){
                            url = "/erp/stores/json/selected_grn_numbers/";
                        }else{
                            url = "/erp/stores/json/selected_oa_numbers/";
                        }
                        $.ajax({
                            url: url,
                            type: "post",
                            datatype:"json",
                            data: {invoice_id: invoice_id},
                            success: function(response){
                                try{
                                    if (response.response_message == "Success") {
                                        setTimeout(function() {
                                            $.each(response.oa_list, function(index, oa) {
                                                $("#id_invoice-order_accept_no_display").multiselect('select', oa.oa_id, true);
                                            });
                                        },500);
                                    }
                                } catch (e) {
                                    console.log(e)
                                }
                            }
                        });
                    }

                    $('#id_invoice-order_accept_no_display').multiselect({
                        onDropdownHide: function(event){
                            load_oa_details();
                        }
                    });
                    removeOAMaterials();
                    $("#id_invoice-order_accept_no_display").next(".btn-group").find(".multiselect").removeClass("disabled").attr("title", "None selected");
                },
                error: function() {
                    $("#id_invoice-order_accept_no_display").next(".btn-group").find(".multiselect").removeClass("disabled").attr("title", "None selected");
                    $("#id_invoice-type").removeAttr('readonly');
                    $("#id_invoice-type").removeAttr('title');
                    $('#id_invoice_party_id_chosen').removeClass('div-disabled');
                }
            });
        } else{
            if($("#id_invoice-goods_already_supplied").is(":checked")){
                loadPartyFinancialYear(party_id,invoice_id);
            }else{
                loadPartyOA(party_id,invoice_id);
            }
            $("#id_invoice-type").removeAttr('readonly');
            $("#id_invoice-type").removeAttr('title');
            $('#id_invoice_party_id_chosen').removeClass('div-disabled');
        }
        return false;
    }

	$("#id_invoice-party_id").change(function(){
        InvoicePartyChangeEvent(); //TODO ABU for loading time of OA NO
        var selected_oa_nos = "";
        var multi_select_oa =  $("select#id_invoice-order_accept_no_display").next('.btn-group').find('ul').find('li input:checked');
        multi_select_oa.each(function () {
            selected_oa_nos += $(this).val() + ",";
        });
        var oa_ids = selected_oa_nos;
        oa_id = oa_ids.split(",");
        if (oa_id.length > 1){
            if($("#id_invoice-se_id").val() == "" ) {
                if($("#id_invoice-goods_already_supplied").is(":checked")) {
                    $("#invoice_materials_table").find(".consolidated_row").each(function(){
                        var element = $(this).closest('tr').attr('consolidated-for');
                        deleteInvoiceMaterials(element);
                    });
                }else{
                    deleteInvoiceMaterials(null);
                }
            }
        }
        ChangePartyCurrency($("#id_invoice-party_id option:selected").val(),"id_invoice-currency_id");
        $("#ul-tagit-display .li-tagit-display").remove();

	});

    $('#catCancel').click(function () {
        document.getElementById("catcss").style.display = "none";
        $("#catalogueModal").modal('hide');
    });

    /* This method is used to add Invoice material form's data to the table $( "#myselect option:selected" ).text() */
	$("#add_invoice_tax").click(function(){
		$(".error-border").removeClass('error-border');
		$(".custom-error-message").remove();

		var ControlCollections = [
			{
				controltype: 'dropdown',
				controlid: 'invoice_tax_list',
				isrequired: true,
				errormsg: 'Tax type is required.'
			}
		];
		var result = JSCustomValidator.JSvalidate(ControlCollections);
		if(result) {
			AddInvoiceTaxClick();
		}
		return result;
	});

	function AddInvoiceTaxClick() {
		var selected_item =$("#invoice_tax_list option:selected").val();
		enterprise_id = $("#id_invoice-enterprise_id").val();
        if($("#invoice_tax_table").find("tr[name*='"+selected_item+"']").length){
            swal("Tax Already Added...");
        }
        else{
            /* Fetching Assessment Value */
            generateFormsetFormRowAtIndex('invoice_tax', '#packing_diff_line');
			copyTaxEmptyForm('invoice_tax',parseInt($('#id_invoice_tax-TOTAL_FORMS').val()) - 1);
            invoiceTaxListAdding();
            isFormChanged = true;
        }
        calculateGrandTotal();
	}

    transformToDummyForm('tag');
    create_delete_tag_button();

    $('#indMaterialForms').ready(function () {
        var initialFormCount = parseInt(document.getElementById('id_invoice_mat-INITIAL_FORMS').value);
        for (i = 0; i < initialFormCount; i++) {
            var makeLabel = document.getElementById('id_invoice_mat-' + i + '-makeLabel');
            makeLabel.innerHTML = $('#id_invoice_mat-' + i + '-make').val();
        }
    });

	$("#materialrequired").keyup(function () {
        if ($(this).val() != "") {
            $("#stocktable tbody>tr").hide();
            $("#stocktable td:contains-ci('" + $(this).val() + "')").parent("tr").show();
        } else {
            $("#stocktable tbody>tr").show();
        }
    });

	$("#add_invoice_item").click(function(){
		$(".error-border").removeClass('error-border');
		$(".custom-error-message").remove();
        $(".suggestion-container").remove();

		var ControlCollections = [
			{
				controltype: 'textbox',
				controlid: 'materialrequired',
				isrequired: true,
				errormsg: 'Material Name is required.'
			},
			{
				controltype: 'textbox',
				controlid: 'id_invoice_mat-__prefix__-quantity',
				isrequired: true,
				errormsg: 'Quantity is required.',
				mindigit: 0.001,
				mindigiterrormsg: 'Quantity is required.'
			}
		];

		if($("#id_dc_type").val().toLowerCase() == "sales" || $("#id_dc_type").val().toLowerCase() == "dc") {
		    var control = {
				controltype: 'textbox',
				controlid: 'id_invoice_mat-__prefix__-rate',
				isrequired: true,
				errormsg: 'Unit Rate is required.'
			};
			ControlCollections[ControlCollections.length] = control;
			var control = {
				controltype: 'textbox',
				controlid: 'id_invoice_mat-__prefix__-hsn_code',
				isrequired: true,
				errormsg: 'HSN/SAC is required.',
                ishsn: true,
                hsnerrormsg: 'Invalid HSN Code.'
			}
			ControlCollections[ControlCollections.length] = control;
		}

		if($('#id_invoice_mat-__prefix__-discount').val()==''){
		    $('#id_invoice_mat-__prefix__-discount').val('0.00');
		}
		var result = JSCustomValidator.JSvalidate(ControlCollections);
		if(result) {
		    $(".custom-error-message").remove();
            if ($('#material_id_hidden').val() != "") {
                if(parseInt($('#is_stock').val())== 1 && !$("#invoice_quick_add").is(":checked")) {
                    validateAndAddItemToTable(0, 1);
                }
                else {
                    addItemToTable(1);
                }
            }
            else {
                if ($('#material_id_hidden').val() == "" && $('#materialrequired').val() != ""){
                    var materialName = $("#materialrequired").val();
                    var materialUnit = $('#id_invoice_mat-__prefix__-all_units').val();
                    var materialHsn = $('#id_invoice_mat-__prefix__-hsn_code').val();
                    var materialPrice = $('#id_invoice_mat-__prefix__-rate').val();
                    addNewMaterial(materialName, materialUnit, materialHsn, materialPrice);
                }
            }
		}
        populateUnitOptions();
		return result;
	});

    function validateAndAddItemToTable(stock, validation_step) {
	    var stock_to_be_issued = $('#id_invoice_mat-__prefix__-quantity').val();
        var current_stock_name = $('#id_invoice_mat-__prefix__-item_name').val();
        if ($('#materialrequired').val() != "" && $('#material_id_hidden').val() != "" && stock_to_be_issued != "" && parseFloat(stock_to_be_issued) > 0) {
            stock = parseFloat($("#Avl_Qty").val()) - (stock + parseFloat(stock_to_be_issued));
            var msl = Number($('#msl_qty').val());
            var faulty_in_error_message =  "";
            if($("#chkfaulty").is(':checked')){
                faulty_in_error_message = "- [Faulty]"
            }
            if (Math.max(0, msl) > stock && stock >= Math.min(0, msl)) {
                var error_message = "";
                if (stock < 0) {
                    error_message = `Stock of <b>${current_stock_name} ${faulty_in_error_message}</b> will be negative once this Invoice is made. Minimum Stock Level for the item is <b>${msl}</b>!`;
                }
                else {
                    error_message = `Stock of <b>${current_stock_name} ${faulty_in_error_message}</b> will be less than the Minimum Stock Level <b>${msl}</b> once this Invoice is made!`;
                }
                swal({
                    title: "",
                    text: error_message,
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#209be1",
                    confirmButtonText: "Yes, do it!",
                    closeOnConfirm: true,
                    closeOnCancel: true
                },
                function(isConfirm){
                    if (isConfirm) {
                        addItemToTable(validation_step);
                    }
                });
            }
            else if (Math.min(0, msl) > stock) {
                if($("#is_super_user").val().toLowerCase() == 'true'){
                    swal({
                        title: "",
                        text: `Stock <b>${current_stock_name} ${faulty_in_error_message}</b> is not available. <br /><br /> Do you want to continue? `,
                        type: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#209be1",
                        confirmButtonText: "Yes, do it!",
                        closeOnConfirm: true,
                        closeOnCancel: true
                    },
                    function(isConfirm){
                        if (isConfirm) {
                            addItemToTable(validation_step);
                        }
                    });
                }
                else {
                    swal({
                        title: "",
                        text: `Stock <b>${current_stock_name} ${faulty_in_error_message}</b> is not available.`,
                        type: "error"
                    });
                }
            }
            else {
                addItemToTable(validation_step);
            }
		}
		else {
			swal("", "Please Select a Material and Enter a Qty", "error");
		}
    }

	function addItemToTable(validation_step) {
        $( "#id_invoice_mat-__prefix__-enterprise_id").val($("#id_invoice-enterprise_id").val());
        var faulty = "False";
        var oa_no = "";
        if($("#chkfaulty").is(':checked')){ faulty ="True" }
        var row_id = -1;
        var materialrowCount = parseInt($('#id_invoice_mat-TOTAL_FORMS').val());
        var materialtable = document.getElementById("invoice_materials_table");
        var materialrowCount1 = materialtable.rows.length - 1;

        // Update the repeated values in the invoice material table...
        for(j=0; j<materialrowCount; j++){
            if($('#material_id_hidden').val() == $('#id_invoice_mat-'+ j +'-item_id').val() &&
              $('#id_invoice_mat-__prefix__-make_id').val() == $('#id_invoice_mat-'+ j +'-make_id').val() &&
              faulty == $('#id_invoice_mat-'+ j +'-is_faulty').val() ) {
               row_id = j;
               break
            }
        }

        if(row_id >= 0) {
            if (!document.getElementById("id_invoice_mat-"+row_id+"-DELETE").checked && parseInt($("#id_invoice_mat-"+row_id+"-quantity").val()) > 0){
                var stock = parseFloat($("#id_invoice_mat-"+row_id+"-quantity").val());
                if (validation_step == 2) {
                    if ($("#id_invoice_mat-__prefix__-alternate_units:visible").length >= 1){
                        if ($("#id_invoice_mat-__prefix__-alternate_units option:selected").attr('data-val') !=0) {
                            var alternate_unit_id = 0
                            var scale_factor =1
                            if ($("#id_invoice_mat-"+row_id+"-scale_factor").val() !="" && $("#id_invoice_mat-"+row_id+"-scale_factor").val()!='undefined'){
                                var scale_factor = $("#id_invoice_mat-"+row_id+"-scale_factor").val()
                            }
                            var existing_qty = parseFloat($("#id_invoice_mat-"+row_id+"-quantity").val()) * scale_factor;
                            var current_qty = parseInt($("#id_invoice_mat-__prefix__-quantity").val()) * $("#id_invoice_mat-__prefix__-alternate_units option:selected").attr('data-val')
                            var rate = parseFloat($("#id_invoice_mat-"+row_id+"-rate").val()) / scale_factor;
                            $("#id_invoice_mat-"+row_id+"-quantity").val(existing_qty + current_qty)
                            $("#id_invoice_mat-"+row_id+"-rate").val(rate)
                            $("#id_invoice_mat-"+row_id+"-alternate_unit_id").val("");
                            $("#id_invoice_mat-"+row_id+"-scale_factor").val("1.000");
                            $("#id_invoice_mat-"+row_id+"-unit_id").val($('#id_invoice_mat-__prefix__-alternate_units option[value="0"]').text())

                            var msl = Number($('#primary_msl_qty').val());
                            if(msl < 0){
                                total_avl_stock = Number($("#primary_Avl_Qty").val()) + Number(msl);
                                $("#id_invoice_mat-"+row_id+"-quantity").next("p").find(".is_msl").removeClass('hide').text("(MSL: "+msl+")").attr("data-value", +msl);;
                            }
                            else {
                                if(msl != 0){
                                    total_avl_stock = Number($("#primary_Avl_Qty").val()) - Number(msl);
                                    $("#id_invoice_mat-"+row_id+"-quantity").next("p").find(".is_msl").removeClass('hide').text("(MSL: "+msl+")").attr("data-value", msl);
                                }
                                else {
                                    total_avl_stock = Number($("#primary_Avl_Qty").val());
                                    $("#id_invoice_mat-"+row_id+"-quantity").next("p").find(".is_msl").addClass('hide').text("").attr("data-value", 0);
                                }
                            }
                            $("#id_invoice_mat-"+row_id+"-quantity").next("p").find(".currently_available_stock").text(Number(total_avl_stock).toFixed(3));
                            $("#id_invoice_mat-"+row_id+"-quantity").next("p").find(".currently_available_stock_vsb").text(Number($("#primary_Avl_Qty").val()).toFixed(3));
                        }
                    }else{
                            stock = stock + parseFloat($("#id_invoice_mat-__prefix__-quantity").val());
                            $("#id_invoice_mat-"+row_id+"-quantity").val(stock);
                            $('#id_invoice_mat-'+row_id+'-qtyLabel').text(stock);
                            $("#id_invoice_mat-"+row_id+"-amount").val(stock * parseFloat($("#id_invoice_mat-"+row_id+"-rate").val()));
                    }
                } else {
                    setTimeout(function() {
                        swal({title: "", text: "Item has already been added to the Invoice. <br /><br />Do you still want to add to its quantity?", type: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#209be1",
                        confirmButtonText: "Yes, do it!",
                        closeOnConfirm: true,
                        closeOnCancel: true
                        },
                        function(isConfirm){
                            if (isConfirm) {
                                setTimeout(function() {validateAndAddItemToTable(stock, 2);}, 500);
                            }
                        });
                    }, 500);
                    return;
                }
            } else {
                var deleteFlag = document.getElementById("id_invoice_mat-"+row_id+"-DELETE");
                var deleteRow = document.getElementById("invoice_mat-"+row_id);
                deleteFlag.checked = false;
                deleteRow.style.display = '';
                $("#id_invoice_mat-"+row_id+"-quantity").val(parseInt($("#id_invoice_mat-__prefix__-quantity").val()));
                var qty_label = document.getElementById('id_invoice_mat-'+row_id+'-quantity');
                qty_label.value = parseFloat($('#id_invoice_mat-'+row_id+'-quantity').val());
                $("#id_invoice_mat-"+row_id+"-amount").val(parseFloat($("#id_invoice_mat-"+row_id+"-quantity").val()) *parseFloat($("#id_invoice_mat-"+row_id+"-rate").val()));
                $("#id_invoice_mat-"+row_id+"-rate").val(parseFloat($("#id_invoice_mat-__prefix__-rate").val()));
                if ($('#id_invoice_mat-__prefix__-alternate_units').value == 0 ) {
                    $("#id_invoice_mat-"+row_id+"-unit_id").val($('#id_invoice_mat-__prefix__-alternate_units option:selected').text());
                    $("#id_invoice_mat-"+row_id+"-alternate_unit_id").val("");
                    $("#id_invoice_mat-"+row_id+"-scale_factor").val("1.000");
                } else{
                    $("#id_invoice_mat-"+row_id+"-alternate_unit_id").val($('#id_invoice_mat-__prefix__-alternate_units option:selected').val());
                    $("#id_invoice_mat-"+row_id+"-unit_id").text($('#id_invoice_mat-__prefix__-alternate_units option:selected').text());
                    $("#id_invoice_mat-"+row_id+"-unit_id").val($('#id_invoice_mat-__prefix__-alternate_units option:selected').text());
                    $("#id_invoice_mat-"+row_id+"-scale_factor").val($('#id_invoice_mat-__prefix__-alternate_units option:selected').attr('data-val'));
                }
            }
        } else {
//          generateFormsetFormRowAtIndex('invoice_mat', '#packing_diff_line');
            if($("#material_is_service").val() == 1) {
                generateFormsetFormRowAppend('invoice_mat', ".item-for-service");
                $(".item-for-service").removeClass('hide');
            }
            else {
                generateFormsetFormRowAppend('invoice_mat', ".item-for-goods");
                $(".item-for-goods").removeClass('hide');
            }
            copyInvoiceEmptyForm('invoice_mat', parseInt($('#id_invoice_mat-TOTAL_FORMS').val()) - 1, 'invoice_mat-invoice_id', 'invoice_id');
            $("#material_list").val("None");
            var index = parseInt(parseInt($('#id_invoice_mat-TOTAL_FORMS').val()) - 1);
            material_index = getMaterialIndexCount();
            $("#id_invoice_mat-"+index+"-entry_order").val(material_index);
            $("#id_invoice_mat-"+index+"-material_type").val($('#is_stock').val());
            $("#invoice_mat-"+index).addClass("invoice_item_row material_list");
            var curStock = $("#closing_qty").text().split(":");


            var enteredQty = Number($(this).find(".td_inv_qty").find("input").val());
            var msl = Number($('#msl_qty').val());
            var oa_pending_qty = Number($(this).find(".oa_dc_pending_qty").find("input").val());
            if(msl < 0){
            	total_avl_stock = Number(curStock[1]) + Number(msl);
            	$("#id_invoice_mat-"+index+"-quantity").next("p").find(".is_msl").removeClass('hide').text("(MSL: "+msl+")").attr("data-value", +msl);;
            }
            else {
            	if(msl != 0){
	            	total_avl_stock = Number(curStock[1]) - Number(msl);
	            	$("#id_invoice_mat-"+index+"-quantity").next("p").find(".is_msl").removeClass('hide').text("(MSL: "+msl+")").attr("data-value", msl);
	            }
	            else {
	            	total_avl_stock = Number(curStock[1]);
	            	$("#id_invoice_mat-"+index+"-quantity").next("p").find(".is_msl").addClass('hide').text("").attr("data-value", 0);
	            }
            }
            $("#id_invoice_mat-"+index+"-quantity").next("p").find(".currently_available_stock").text(Number(total_avl_stock).toFixed(3));
            $("#id_invoice_mat-"+index+"-quantity").next("p").find(".currently_available_stock_vsb").text(Number(curStock[1]).toFixed(3));
            $("#id_invoice_mat-"+index+"-quantity").next("p").find(".currently_available_pending").text(Number(oa_pending_qty).toFixed(3));
        }

        var item_count = parseFloat($("#id_invoice_mat-TOTAL_FORMS").val());
        var temp = item_count-1;
        var cgst = $("#id_inv_mat_tax_"+temp+"-0-rate_drop_down")
        var sgst = $("#id_inv_mat_tax_"+temp+"-1-rate_drop_down")
        var igst = $("#id_inv_mat_tax_"+temp+"-2-rate_drop_down")
        if($("#id_invoice_mat-"+temp+"-remarks").val() != "") {
            $("#id_invoice_mat-"+temp+"-remarks").closest("td").find(".item_remarks_section").removeClass("hide");
            $("#id_invoice_mat-"+temp+"-remarks").closest("td").find(".add_item_remarks").addClass("hide");
        }
        var material_type = 1 //'stockable'
        smartPopulateTax(cgst,sgst,igst,material_type,temp);
	    calculateGrandTotal();
	    clearMaterialAddContainer();
        assignSerialNumber();
	    isFormChanged = true;
        $("#materialrequired").removeAttr("readonly");
        $(".material-removal-icon").addClass("hide");
        $("#materials_add").find(".item_remarks_section").addClass("hide");
        $("#materials_add").find(".add_item_remarks").removeClass("hide");
		setTimeout(function(){
			$(".error-border").removeClass('error-border');
		},100);
		$(".custom-error-message").remove();
		listTableHoverIconsInit('invoice_materials_table');
	}

    $("#cmdSaveInvoice").click(function(){
		$(".error-border").removeClass('error-border');
		$(".custom-error-message, .hsn-suggestion").remove();
		$("#id_invoice-project_id").val($("#id_invoice-project_code").val());
		if ($("#id_dc_type").val() == "sales" || $("#id_dc_type").val() == "dc"){
			var ControlCollections = [
				{
					controltype: 'dropdown',
					controlid: 'id_invoice-type',
					isrequired: true,
					errormsg: 'Invoice Type is required.'
				},
				{
					controltype: 'dropdown',
					controlid: 'id_invoice-project_code',
					isrequired: true,
					errormsg: 'Project is required.'
				},
				{
					controltype: 'dropdown',
					controlid: 'id_invoice-location_id',
					isrequired: true,
					errormsg: 'Location is required.'
				},
				{
					controltype: 'dropdown',
					controlid: 'id_invoice-party_id',
					isrequired: true,
					errormsg: 'Customer/Supplier is required.'
				},
				{
                    controltype: 'textbox',
                    controlid: 'id_invoice-ecommerce_gstin',
                    isrequired: false,
                    minvalue: 15,
    	            minvalerrormsg: 'GSTIN number should be 15 characters.'
                }
			];

            $("#invoice_materials_table .invoice_item_row").not(".deleted_invoice").each(function(){
                var currentHsn = $(this).find(".inv_hsn_code");
                var currentElementId = currentHsn.attr("id");
                var control = {
                    controltype: 'textbox',
                    controlid: currentElementId,
                    isrequired: true,
                    errormsg: 'HSN/SAC is required',
                    ishsn: true,
                    hsnerrormsg: 'Invalid HSN Code',
                    suggestioncontainer: "hsn-wrapper"
                }
                ControlCollections[ControlCollections.length] = control;
            });
            if($("#id_dc_type").val() == "sales") {
                $("#invoice_materials_table .invoice_charges_row").each(function(j){
                    if($("#id_invoice_charge-"+j+"-rate").val() > 0) {
                        var currentHsn = $(this).find(".charge_hsn_code");
                        var currentElementId = currentHsn.attr("id");
                        var control = {
                            controltype: 'textbox',
                            controlid: currentElementId,
                            isrequired: true,
                            errormsg: 'Required',
                            ishsn: true,
                            hsnerrormsg: 'Invalid',
                            suggestioncontainer: "hsn-wrapper"
                        }
                        ControlCollections[ControlCollections.length] = control;
                    }
                });
            }
		}
		else if ($("#id_dc_type").val() == "internal"){
			var ControlCollections = [
				{
				 	controltype: 'dropdown',
					controlid: 'id_invoice-issued_to',
					isrequired: true,
					errormsg: 'Issue to is required.'
				},
				{
					controltype: 'dropdown',
					controlid: 'id_invoice-issued_for',
					isrequired: true,
					errormsg: 'Issue for is required.'
				},
				{
					controltype: 'dropdown',
					controlid: 'id_invoice-project_code',
					isrequired: true,
					errormsg: 'Project is required.'
				},
				{
					controltype: 'dropdown',
					controlid: 'id_invoice-location_id',
					isrequired: true,
					errormsg: 'Location is required.'
				},
				{
					controltype: 'textbox',
					controlid: 'id_invoice-issued_on',
					isrequired: true,
					errormsg: 'Date is required.'
				}
			];
		}

		if ($("#id_invoice-type option:selected").val()== "JDC"){
            var control = {
                controltype: 'dropdown',
                controlid: 'id_invoice-job_po_id',
                isrequired: true,
                errormsg: 'Please select JOB PO type.'
            };
            ControlCollections[ControlCollections.length] = control;
        }

        if ($("#div_con_rate").is(":visible")){
            var control = {
                controltype: 'textbox',
                controlid: 'id_invoice-currency_conversion_rate',
                isrequired: true,
                errormsg: 'Required.',
                mindigit: 0.00001,
                mindigiterrormsg: 'Rate cannot be 0.'
            };
            ControlCollections[ControlCollections.length] = control;
        }

        var anyChecked = false;
        $("input[id^='id_invoice_mat-'][id$='-is_returnable']").each(function() {
             if ($(this).is(':checked') || ($('#materialrequired').val()!= '' && $('#chkreturn').is(':checked'))) {
                 anyChecked = true;
                 return false;
             }
        });
        if (anyChecked && $("#id_dc_type").val() == "dc") {
             ControlCollections.push({
                 controltype: 'textbox',
                 controlid: 'invoice_return_date',
                 isrequired: true,
                 errormsg: 'Return Date is required.'
             });
             $("#return_date label").append('<span class="required-mark" style="color:red;"> *</span>');
         }
         else
         {
              $("#return_date label .required-mark").remove();
         }

		var result = JSCustomValidator.JSvalidate(ControlCollections);
        if($("#invoice_materials_table").find(".suggestion-container").length > 0) {
            $("#invoice_materials_table").find(".suggestion-container").each(function(){
                var isConsolidated = $(this).closest("tr").attr("data-row");
                if(isConsolidated){
                    if($(this).closest("tr").hasClass("hide")) {
                        $("#invoice_materials_table").find(`tr[consolidated-for='${isConsolidated}']`).find(".consolidated-link").click();
                    }
                }
            })
        }
		if(result) {
		    var isValidated = false;
		    if($("#materialrequired").val() != "") {
                if($("#id_invoice_mat-__prefix__-rate").val() == 0 || $("#id_invoice_mat-__prefix__-rate").val() == "" || $("#id_invoice_mat-__prefix__-quantity").val() == "" || $("#id_invoice_mat-__prefix__-quantity").val() == 0 ||  $("#materialrequired").val() < 0) {
                    $("#add_invoice_item").trigger('click');
                }
                else {
                    $("#add_invoice_item").trigger('click');
                    isValidated = true;
                }
             }
            else {
                isValidated = true;
            }
            if(isValidated) {
                var checkClosingStock = showAllClosingStocks(0);
                setTimeout(function(){
                    var exceeds_pending = validatePendingQty()
                    if(exceeds_pending > 0){
                        if(validationResult.exceeds_pending > 1) {
                            swal("", "Quantity of " + exceeds_pending + " materials are greater than Pending Quantity. Please check!!", "error");
                        } else {
                            swal("", "Quantity of a material is greater than Pending Quantity. Please check!!", "error");
                        }
                        return;
                    }

                    if(!$("#id_invoice-goods_already_supplied").is(":checked")) {
                        if($("#id_issue_type").val() == "oa" || $("#id_issue_type").val() == "mrs") {
                            var pp_result = validatePPQty();
                            var isZeroError = pp_result[0];
                            if (isZeroError){
                                swal({title: "", text:"Issue quantity should not be Zero", type: "error"});
                                return;
                            }
                            var isPPQtyError = pp_result[1];
                            if (isPPQtyError){
                                  swal({title: "", text: $("#id_issue_type").val() == "oa"
                                    ? "Issue quantity  should not exceed the Allocated quantity"
                                    : "Issue quantity for MRS should not exceed the MRS quantity",
                                type: "error",
                                 type: "error"});
                                 return;
                            }
                        }

                        var msl_result = validateEachMSLValue();
                        result = msl_result[0];
                        isQtyWarning = msl_result[1];
                        isQtyError = msl_result[2];
                        if(!result) {
                            var swalMsg = "";
                            if(isQtyError) {
                                swalMsg += "Issue quantity of at least one of the Items listed exceeds the maximum possible Issue quantity.<br /><br />";
                            }
                            if(isQtyWarning) {
                                swalMsg += "Issue quantity of at least one of the Items listed exceeds the minimum stock level quantity.<br /><br />";
                            }
                            if($("#is_super_user").val().toLowerCase() == 'true' || (isQtyWarning && !isQtyError)) {
                                swalMsg += "Do you still want to continue?"
                                swal({
                                    title: "Out of Stock",
                                    text: swalMsg,
                                    type: "warning",
                                    showCancelButton: true,
                                    confirmButtonColor: "#209be1",
                                    confirmButtonText: "Yes, do it!",
                                    closeOnConfirm: true,
                                    closeOnCancel: true
                                },
                                function(isConfirm) {
                                    if (isConfirm) {
                                        setTimeout(function(){validateDcLinkedInvoice();}, 200);
                                    }
                                });
                            }
                            else {
                                swalMsg += "Kindly correct it to Save the changes!"
                                swal({
                                    title: 'Out of Stock',
                                    text: swalMsg,
                                    type: 'warning'
                                });
                            }
                        }
                        else {
                            var result = validateEachGrnAgainstValue();
                            if(!result) {
                                    var swalMsg = "";
                                    if($("#is_super_user").val().toLowerCase() == 'true') {
                                        swalMsg += "Do you still want to continue?"
                                        swal({
                                            title: "Material has been returned against this issue.So this material Qty cannot be reduced below the returned Qty!",
                                            text: swalMsg,
                                            type: "warning",
                                            showCancelButton: true,
                                            confirmButtonColor: "#209be1",
                                            confirmButtonText: "Yes, do it!",
                                            closeOnConfirm: true,
                                            closeOnCancel: true
                                        },
                                        function(isConfirm) {
                                            if (isConfirm) {
                                                setTimeout(function(){validateDcLinkedInvoice();}, 200);
                                            }
                                        });
                                    }
                                    else {
                                        swalMsg += "Material has been returned against this issue.So this material Qty cannot be reduced below the returned Qty!"
                                        swal({
                                            title: 'Qty cannot be reduced',
                                            text: swalMsg,
                                            type: 'warning'
                                        });
                                    }
                                }else{
                                    validateDcLinkedInvoice();
                                }
                        }
                    }else{
                        validateInvoiceForm();
                    }
                },50);
            }
		}
		else {
			$("html, body").animate({ scrollTop: 0 }, "fast");
		}
		const text = $("#id_invoice-job_po_id option:selected").text();
	    if( text.includes('/MRS/')){
	        $('#mrs_type').val(1);
	    }
	    else{
	        $('#mrs_type').val(0);
	    }
		return result;
	});

	function validateInvoiceForm(){
        var current_currency = $("#id_invoice-currency_id option:selected").text();
        var home_currency = $("#home_currency_id").val();
		var currency_conversion_reminder_message = `Rate of Currency Conversion from ${current_currency} to ${home_currency} is mentioned as 1.00`;
		var confirm_message =  "Do you confirm?";
		var allow_save_flag = true;
		if ($("#id_dc_type").val() == "sales"){
            if($("#id_invoice-currency_id option:selected").text()!=$("#home_currency_id").val()){
                if(parseFloat($("#id_invoice-currency_conversion_rate").val())==parseFloat(1)){
                    confirm_message = currency_conversion_reminder_message + '\n' + confirm_message;
                    allow_save_flag = false;
                }
            }
        }

        var invalid_quantity=false;
        $("#invoice_materials_table tbody tr.item-for-goods, #invoice_materials_table tbody tr.item-for-service").not(".deleted_invoice").find(".validate_qty").each(function(){
            if($(this).attr("id").indexOf("dummy") == -1) {
                if($(this).val() == 0) {
                    $(this).addClass("error-border");
                    invalid_quantity = true;
                }
                else {
                    $(this).removeClass("error-border");
                }
            }
        });

        if(invalid_quantity) {
            swal({title: "", text:"Quantity value cannot be zero", type: "error"});
            return false;
        }
        if(!allow_save_flag){
            allow_save_flag = window.confirm(confirm_message);
            result = allow_save_flag;
        }
		if(allow_save_flag) {
			checkAndSaveInvoice();
		}
	}

    /**
    * Validate for edits
    **/
    function checkAndSaveInvoice() {

        if ($("#id_invoice-id").val() == "" || "Issue" == $("#id_invoice-type").val()) {
            // Issue save/edit, new Invoice/DC will be saved directly after form validation
            saveInvoice();
        } else {
            // Invoice/DC will be validated to thy associated documents before edit
            var url = "/erp/sales/json/get_invoice_linked_message/";
            if (["DC", "JDC", "JIN"].includes($("#id_invoice-type").val())) {
                url = "/erp/stores/json/get_dc_linked_message/";
            }
            $.ajax({
                url: url, method: "POST",
                data:{invoice_id: $("#id_invoice-id").val(), invoice_type: $("#id_invoice-type").val()},
                success: function(response) {
                    if (response.response_message =="Success") {
                        if(response.custom_message == "") {
                            saveInvoice();
                        } else {
                            swal({
                                title: "", text: response.custom_message, type: "warning",
                                showCancelButton: true,
                                confirmButtonColor: "#209be1",
                                confirmButtonText: "Yes, do it!",
                                closeOnConfirm: true,
                                closeOnCancel: true
                            },
                            function(isConfirm) {
                                if (isConfirm) {
                                    setTimeout(function(){saveInvoice();}, 200);
                                }
                            });
                        }
                    } else {
                        swal({title: "", text: response.custom_message, type: "error"});
                    }
                },
                error: function (xhr, errmsg, err) {
                    swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
                }
            });
        }
	}

    function submitInvoiceToSave() {
        calculateGrandTotal();
        const text = $("#id_invoice-job_po_id option:selected").text();
        $("#loading").show();
        $("#cmdSaveInvoice").val('Processing...').addClass('btn-processing');
        $("#invoice_add").submit();
        var event_labels = {'internal': 'Issue', 'dc': 'Delivery Challan', 'sales': 'Invoice'};
        ga('send', 'event', event_labels[$("#id_edit_dc_type").val()], 'Create', $('#enterprise_label').val(), 1);
    }

    /**
    * Validate Invoice type and save invoice
    * If Invoice type is changeable then saves the invoice type and then calls saveInvoice() automatically.
    **/
	function validateTypeAndSaveInvoice() {
	    if((!isFormChanged && $("#promote_inv_se_code").val()!="" && $("#search_invoice_id").val() != "") || (!isFormChanged && $("#promote_inv_se_code").val()=="" && $("#search_invoice_id").val() != "")) {
    		swal("","There are no changes detected in this form to update.","warning")
    	}
    	else {
            if ($("#id_invoice-id").val() == "" || $("#invoice_status").val() == "0" || $("#id_invoice-type").val() == $("#current_type").val()) {
                submitInvoiceToSave();
            } else {
                EditInvoiceNumber(false);
                $.ajax({
                    url: "/erp/sales/json/super_edit_invoice_code/",
                    method: "POST",
                    data:{
                        invoice_id: $("#id_invoice-id").val(),
                        new_financial_year: $("#inv_financial_year").val().trim(),
                        new_invoice_type: $("#id_invoice-type").val(),
                        new_invoice_no: $("#inv_number").val().trim(),
                        new_sub_number: $("#inv_number_division").val().trim()
                    },
                    success: function(response) {
                        if (response.response_message =="Success") {
                            submitInvoiceToSave();
                        } else {
                            swal({title: "", text: response.custom_message, type: "error"});
                        }
                    },
                    error: function (xhr, errmsg, err) {
                        swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
                    }
                });
            }
        }
	}

    function validateDcLinkedInvoice() {
        var invoice_id = $("#id_invoice-id").val();
        if(invoice_id != ""){
            $.ajax({
                url: "/erp/sales/json/get_dc_linked_message/",
                method: "POST",
                data:{
                    invoice_id: invoice_id,
                },
                success: function(response) {
                    if (response.response_message =="Success") {
                        if (response.custom_message == "") {
                            validateInvoiceForm();
                        } else {
                            confirmAction(message=response.custom_message, callback=function (isConfirm) {
                                if (isConfirm) {
                                    setTimeout(function () {
                                        validateInvoiceForm();
                                    }, 500);
                                 }
                            });
                        }
                    }
                    else {
                        swal({title: "", text: response.custom_message, type: "error"});
                    }
                }, error: function (xhr, errmsg, err) {
                    swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
                }
            });
        }else{
            validateInvoiceForm();
        }
    }

	/**
	* Save Invoice after validation.
	**/
	function saveInvoice() {
	//console.log("func called");
        var item_count = parseFloat($("#id_invoice_mat-TOTAL_FORMS").val());
        var charge_item_count = parseFloat($("#id_invoice_charge-TOTAL_FORMS").val());
        var invoice_tax_count = parseFloat($("#id_invoice_tax-TOTAL_FORMS").val());
        var item_list =0
        var tax_list = 0

        if($('#id_invoice-party_id option:selected').val() == "None"){
			swal('Please Select a Party');
			return;
        }
        if ($("#se_party").val() != $("#id_invoice-party_id").val()){
            $("#id_invoice-se_id").val("");
            $("#id_invoice-se_date").val("");
        }
        var check_tax = false
        for(i=0;i<item_count;i++){
			if(!document.getElementById("id_invoice_mat-"+i+"-DELETE").checked){
				item_list = item_list+1;
				if (document.getElementById("id_inv_mat_tax_"+i+"-0-rate").value==0 && document.getElementById("id_inv_mat_tax_"+i+"-1-rate").value==0 && document.getElementById("id_inv_mat_tax_"+i+"-2-rate").value==0){
                    check_tax = true
                }
			}
		}

        for(i=0;i<charge_item_count;i++) {
			if(parseFloat($("#id_invoice_charge-"+i+"-rate").val()) == 0){
				document.getElementById("id_invoice_charge-"+i+"-DELETE").checked = true;
			}
		}

		for(i=0;i<invoice_tax_count;i++){
			if(!document.getElementById("id_invoice_tax-"+i+"-DELETE").checked){
				tax_list = tax_list+1;
			}
		}

        var issue_title = "Issue";
        if($("#id_dc_type").val()=="sales"){
            issue_title = "Invoice";
        } else if($("#id_dc_type").val()=="dc") {
            issue_title = "Delivery Challan";
        }
        if(item_list == 0) {
			swal('','Please add atleast one material to the ' + issue_title,'error');
			return;
		}
		else {
		    if((tax_list == 0 && check_tax && ($("#id_dc_type").val().toLowerCase() == "sales" || $("#id_dc_type").val().toLowerCase() == "dc") && $("#id_invoice-type").val() != "BoS") && $('body').hasClass('primary-enterprise')){
                swal({
                    title: "Please Confirm",
                    text: "One or more item has no tax attached!",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#209be1",
                    confirmButtonText: "Yes, do it!",
                    closeOnConfirm: true,
                    closeOnCancel: true
                },
                function() {
                    setTimeout(function(){validateTypeAndSaveInvoice();}, 200);
                });
            }
            else {
                validateTypeAndSaveInvoice();
            }
		}
    }

    $('#id_invoice_number').keyup(function() {
        var s = new RegExp((this.value).toLowerCase());
        $('#invoice_list tbody tr').each(function() {
            if(s.test((this.innerHTML).toLowerCase())) $(this).show();
            else $(this).hide();
        });
    });

	generateTaxList($("#id_edit_data").val());
	edit_netamount_calculate();
	customAutoComplete();
	calculateGrandTotal();
    var url = window.location.href;
  //   if($("#id_invoice-id").val() != "") {
  //       $(window).load(function(){
		// 	setTimeout(function(){
  //               if(!$("#id_invoice-goods_already_supplied").is(":checked")){
  //                   showAllClosingStocks(0);
		// 	    }
		// 	},500);
		// })
  //   }
  //   else{
  //       if ($("#promote_inv_se_code").val() != ""){
  //           $(window).load(function(){
  //               setTimeout(function(){
  //                   showAllClosingStocks(0);
  //               },500);
  //           })
  //       }
  //   }

	$(".chosen-select").chosen();
    $('#id_dc_div').hide();

    $("#chkfaulty").change(function(){
        if(parseInt($('#is_stock').val())== 1 && !$("#invoice_quick_add").is(":checked")){
            showstock();
        }
    });
    if($("#search_invoice_id").val()!="None"){
        $("#id_invoice-order_accept_no_display option:selected").text($("#id_invoice-order_accept_no").val())
    }

    if($("#id_invoice-project_code").val()==='None') {
        $("#id_invoice-project_code").val($('#id_invoice-project_code optgroup:eq(0) option:eq(0)').val()).trigger("chosen:updated");
    }
    $("#id_invoice-project_id").val($("#id_invoice-project_code").val());
//    $("#id_invoice-location").val();


   	$('#edit_catalogue_form').submit(function() {
   	//console.log("Form submitted");
        $.ajax({

            data: $(this).serialize(),
            type: $(this).attr('method'),
            url: $(this).attr('action'),

            success: function(response) {
                $('#loading').hide();
                $("#individual_item_description").addClass("hide");
                $("#description_display").html('');
                $("#material_id_hidden").val(response['item_id']);
                $("#materialrequired").val(response['name']).attr("readonly","readonly");
                $("#materialrequired").closest("div").find(".material-removal-icon").removeClass("hide");
                $("#is_stock").val(response['is_stockable']);
                $("#material_id").val(response['item_id']);
                $("#id_invoice_mat-__prefix__-item_id").val(response['item_id']);
                $("#id_invoice_mat-__prefix__-item_code").val(response['drawing_no']);
                $("#id_invoice_mat-__prefix__-item_name").val(response['name']);
                $("#id_invoice_mat-__prefix__-unit_id").val(response['unit_name']) ;
                $("#id_invoice_mat-__prefix__-hsn_code").val(response['hsn_code']);
                $("#id_invoice_mat-__prefix__-rate").val(response['price'].toFixed(5));
                $("#id_invoice_mat-__prefix__-material_type").val(response['is_stockable']);
                $("#material_is_service").val(response['is_service']);
                $('#id_invoice_mat-__prefix__-alternate_units').html("");
                $("#is_stock").val(response['is_stockable']);
                $("#unit_display").html(response['unit_name']);
                if(!$('#add_material_modal').hasClass('in')) {
                    $("#add_invoice_item").trigger("click");
                }
                $('#add_material_modal').modal('hide');
                setTimeout(function(){
                    $("#id_invoice_mat-__prefix__-hsn_code").focus();
                },250);

            }
        });
        return false;
    });

    pdfDocumentBtnEvent();
    if ($("#se_project_code").val() != "None" && $("#se_project_code").val() != ""){
        updateProjectChosen($("#se_project_code").val(), "id_invoice-project_code");
        $("#id_invoice-project_code").prop("disabled", true).trigger("chosen:updated");
    }
    else{
        updateProjectChosen($("#selected_project_code").val(), "id_invoice-project_code");
    }
});

function clearMaterialAddContainer() {
	$('#msl_qty').val(0.00);
    $("#closing_qty").text("");
    $('#primary_closing_qty').val(0);
    $('#primary_Avl_qty').val(0);
    $('#primary_msl_qty').val(0);
    $(".alternate_unit_select_box").addClass("hide");
    $('#id_invoice_mat-__prefix__-quantity').val("0.00");
    $('#id_invoice_mat-__prefix__-rate').val("0.00");
    $('#id_invoice_mat-__prefix__-discount').val("0.00");
    $("#id_invoice_mat-__prefix__-unit_id").val(1);
    $("#id_invoice_mat-__prefix__-amount").val("0.00") ;
    $("#unit_display").html('&nbsp;') ;
    $("#individual_item_description").addClass("hide");
    $("#description_display").html('') ;
    $('#materialrequired, #material_id_hidden, #id_invoice_mat-__prefix__-hsn_code, #id_invoice_mat-__prefix__-remarks').val("");
    //$('#materialrequired').focus();
    $('#chkfaulty').attr('checked', false);
    $("#explode_bom").hide();
}

function validatePPQty() {
	var isZeroError = false;
	var isPPQtyError = false;
	if($('#id_invoice-type').val() == "Issue"){
        $("#invoice_materials_table").find(".invoice_item_row:visible").each(function(){
            var item_id = $(this).find('td input.item_id').val();
            if($(this).find(".td_inv_qty").find("input").val() < 1)
            {
                $(this).find(".td_inv_qty").find("input").addClass("error-border");
               isZeroError = true;
            }
            else{
                if (localStorage.getItem('PPStockDetails')) {
                    var PPStockDetails = JSON.parse(localStorage.getItem("PPStockDetails"));
                    if (PPStockDetails.hasOwnProperty('child_items')) {
                      var childItemsLength = Object.keys(PPStockDetails.child_items).length;
                      if(childItemsLength > 0){
                        var chilld_items = PPStockDetails.child_items;
                        if (item_id in chilld_items) {
                            var allocated_qty = chilld_items[item_id]['allocated_qty'];
                            var issue_qty = chilld_items[item_id]['issued_qty'];
                            if($(this).find(".td_inv_qty").find("input").val() > (allocated_qty - issue_qty))
                            {
                                $(this).find(".td_inv_qty").find("input").addClass("error-border");
                                isPPQtyError = true;
                            }
                        }
                      }
                    }
                }
                if (localStorage.getItem('MRSStockDetails')) {
                    var MRSStockDetails = JSON.parse(localStorage.getItem("MRSStockDetails"));
                    if(MRSStockDetails.length > 0){
                        if (MRSStockDetails.some(obj => obj.cat_code == item_id)) {
                            const matchingObject = MRSStockDetails.find(obj => obj.cat_code == item_id);
                            var mrs_qty = matchingObject.quantity;
                            var issue_qty = matchingObject.issue_qty;
                            if($(this).find(".td_inv_qty").find("input").val() > (mrs_qty - issue_qty))
                                {
                                    $(this).find(".td_inv_qty").find("input").addClass("error-border");
                                    isPPQtyError = true;

                            }
                        }
                    }
                }
            }
        });
    }
    var ppqtyresult = [];
	ppqtyresult.push(isZeroError);
	ppqtyresult.push(isPPQtyError);
	return ppqtyresult;
}

function validateEachMSLValue() {
	var isQtyError = false;
	var isQtyWarning = false;
	var mslStatus = true;
	const text = $("#id_invoice-job_po_id option:selected").text();
	$(".td_inv_qty").find("input").removeClass('error-border warning-border');
	$("#invoice_materials_table").find(".invoice_item_row:visible").each(function(){
		$(this).find(".td_inv_qty").find("p").addClass('hide');
        if($(this).find(".is_stock_material").find("input").val() != "0" && !text.includes('/PP/')) {
            if($(this).find(".td_inv_qty").find(".currently_available_stock").text() != "") {
                var enteredQty = Number($(this).find(".td_inv_qty").find("input").val());
                var availableQty = Number($(this).find(".td_inv_qty").find(".currently_available_stock_vsb").text());
                var mslQty = Number($(this).find(".td_inv_qty").find(".is_msl").attr("data-value"));
                if(mslQty <=0) {
                    var allowedQty = Number(availableQty);
                    var afterMslQty = Number(availableQty) - Number(mslQty);
                }
                else {
                    var allowedQty = Number(availableQty) - Number(mslQty);
                    var afterMslQty = Number(availableQty);
                }

                if(enteredQty <= allowedQty || enteredQty <= $('#old_qty input').val()) {

                }
                else if(enteredQty > allowedQty && enteredQty <= afterMslQty) {
                    $(this).find(".td_inv_qty").find("input").addClass('warning-border');
                    $(this).find(".td_inv_qty").find("p").removeClass('hide').css("color", "#ffbf00");
                    isQtyWarning = true;
                    mslStatus = false;
                }
                else {
                    $(this).find(".td_inv_qty").find("input").addClass('error-border');
                    $(this).find(".td_inv_qty").find("p").removeClass('hide').css("color", "#dd4b39");
                    $(this).find(".td_inv_qty").find(".currently_available_stock_vsb").removeClass('hide').css("color", "#dd4b39");
                    isQtyError = true;
                    mslStatus = false;
                }

            }
        }
	});
	var mslresult = [];
	mslresult.push(mslStatus);
	mslresult.push(isQtyWarning);
	mslresult.push(isQtyError);

	return mslresult;
}

function validatePendingQty(){
    var exceeds_pending = 0
    $("#invoice_materials_table").find(".invoice_item_row:visible").each(function(){
         if($(this).find(".oa_code_container").text() != ""){
            if(Number($(this).find(".td_inv_qty").find("input").val()) > $(this).find(".td_inv_qty").find(".currently_available_pending").text()){
                exceeds_pending++;
                $(this).find(".td_inv_qty").find("input").addClass('error-border');
				$(this).find(".td_inv_qty").find("p").removeClass('hide').css("color", "#dd4b39");
				$(this).find(".td_inv_qty").find(".currently_available_stock_vsb").addClass('hide').css("color", "#dd4b39");
				$(this).find(".td_inv_qty").find(".currently_available_pending").removeClass('hide').css("color", "#dd4b39");
            }
         }else if($(this).find(".dc_code_container").text() != ""){
             if(Number($(this).find(".td_inv_qty").find("input").val()) > $(this).find(".td_inv_qty").find(".currently_available_pending").text()){
                exceeds_pending++;
                $(this).find(".td_inv_qty").find("input").addClass('error-border');
				$(this).find(".td_inv_qty").find("p").removeClass('hide').css("color", "#dd4b39");
				$(this).find(".td_inv_qty").find(".currently_available_stock_vsb").addClass('hide').css("color", "#dd4b39");
				$(this).find(".td_inv_qty").find(".currently_available_pending").removeClass('hide').css("color", "#dd4b39");
            }
         }
    });
    return exceeds_pending
}

function validateEachGrnAgainstValue(){
	var validationResult = true;
	$("#invoice_materials_table").find(".invoice_item_row:visible").each(function(){
		var enteredQty = Number($(this).find(".td_inv_qty").find("input").val());
		var received_qty = Number($(this).find(".received_qty").find("input").val());
		if(enteredQty < received_qty){
			validationResult = false;
			return validationResult;
		}else{
			validationResult = true;
		}
	});
	return validationResult;
}

function showReport(report_name) {
    dc_id=$("#search_invoice_id").val()
    if (report_name == 'Item_Invoiced'){
        url =  "/erp/sales/json/invoice/item_invoiced/";
        $('.item_invoiced_header').text('Invoiced');
        $('.item_invoiced').text('Invoice');
    }else {
        url = "/erp/sales/json/invoice/dc_return/";
        $('.item_invoiced_header').text('Returned');
        $('.item_invoiced').text('GRN');
    }
    $.ajax({
        url: url,
        type: "post",
        datatype: "json",
        data: {dc_id:dc_id},
        success: function(response) {
            $("#return_material_list").find("tbody").html('');
            var link_id = "";
            var link_type = "";
            var link = "";
            if(response.length > 0) {
	            $.each(response, function (i, item) {
                    if(report_name == "Item_Invoiced") {
                        link_id = item["invoice_no"];
                        link_type = item["invoice_type"].toLowerCase();
                        link = `<a role='button' onclick='javascript:editInvoice(${link_id},"${link_type}");'>${item["doc_no"]}</a>`;
                    }
                    else {
                        link_id = item["receipt_no"];
                        link_type = item['receipt_type'];
                        link = `<a role='button' onclick='javascript:editReceipt(${link_id},"${link_type}");'>${item["doc_no"]}</a>`;
                    }
                    var item_name = item["item_details"];
                    var itemTypeFlag ="";
                    if(item.is_service == true){
                        itemTypeFlag = `<span class="service-item-flag"></span>`;
                    }
                    if (item.is_stock != 1 && item.is_service != true){
                        itemTypeFlag = `<span class='non_stock-flag'></span>`;
                    }
	                row=`<tr align='center'>
	                        <td width='5%' class='text-center'>${(i+1)}</td>
	                        <td width='12%'>
                                ${link}
                            </td>
                            <td width='15%' class='text-center'>${moment(item["doc_date"]).format("MMM D, YYYY")}</td>
	                        <td width='15%' class='text-left'></span>${item_name} ${itemTypeFlag}</td>
	                        <td width='20%' class='text-right'>${item['quantity']}</td>
                            <td width='9%' class='text-center'>${item['unit']}</td>
                        </tr>`;
	                $('#return_material_list').append(row);
	            });
           	}
           	else {
           		row = "<tr><td colspan='6' class='text-center font-bold'>No Records Found!</tr>";
           		$('#return_material_list').append(row);
           	}
        }
    });
    $("#returnMaterial").modal('show');
}

function editReceipt(receipt_no, receipt_type) {
    if(receipt_type.toLowerCase() == "issues") {
        $("#id-edit_receipt_form").attr("action", "/erp/stores/irn/");
    }
    else {
        $("#id-edit_receipt_form").attr("action", "/erp/stores/grn/");
    }
    $("#id-edit_receipt_no").val(receipt_no);
    $("#id-edit_receipt_form").submit();
}

function editInvoice(invoice_no, invoice_type) {
    $("#id-edit_invoice_no").val(invoice_no);
    $("#id-edit_invoice_type").val(invoice_type);
    $("#id-edit_invoice_form").submit();
}

function generateTaxList(invoice_id){
	se_id = $("#id_promote_se_data").val()
	if (invoice_id != ""){
        $.ajax({
            url: "/erp/sales/json/invoice/editTax/",
            type: "post",
            datatype: "json",
            data: { inv_id: invoice_id},
            success: function (response) {
                $.each(response, function (i, inv_tax_detail_as_row) {
                    $('#invoice_tax_table').append(inv_tax_detail_as_row).addClass('tbl');
                    calculateGrandTotal();
                });
                $("#invoice_tax_table").find('.hnd_text_id').each(function(){
                    var selectedTax = $(this).val();
                    $("#invoice_tax_list option[value='"+selectedTax+"']").hide();
                });
                $('.chosen-select').trigger('chosen:updated');
            }
		});
	}
	else{
	    if (se_id != ""){
            $.ajax({
                url: "/erp/sales/json/invoice/editTax/",
                type: "post",
                datatype: "json",
                data: { se_id: se_id},
                success: function (response) {
                    $.each(response, function (i, inv_tax_detail_as_row) {
                        $('#invoice_tax_table').append(inv_tax_detail_as_row).addClass('tbl');
                        calculateGrandTotal();
                    });
                    $("#invoice_tax_table").find('.hnd_text_id').each(function(){
                        var selectedTax = $(this).val();
                        $("#invoice_tax_list option[value='"+selectedTax+"']").hide();
                    });
                    $('.chosen-select').trigger('chosen:updated');
                }
            });
        }
	}

}

/* Invoice tax list adding */
function invoiceTaxListAdding(selected_tax){
	if (selected_tax){
            tax_code = selected_tax;
        }
        else{
            tax_code = $("#invoice_tax_list option:selected").val();
        }
	$.ajax({
        url: "/erp/sales/json/invoice/getTax/",
        type: "post",
        datatype:"json",
        data: {tax_code: tax_code},
        success: function(response){
			$.each(response, function(i, tax_detail_as_row){
				$('#invoice_tax_table').append(tax_detail_as_row);
            });
			// Calculate the Total Price after applying new Tax profile
            calculateGrandTotal();
        },
        error: function (xhr, errmsg, err) {
           console.log(xhr.status + ": " + xhr.responseText);
        }
    });
	$("#invoice_tax_list option:selected").hide(); // Remove the added Tax from the Tax-list to avoid multiple addition
	$("#invoice_tax_list").val('None');// Reset the Tax Select input non_excise_invoice_tax_table
	$('.chosen-select').trigger('chosen:updated');
}

function copyInvoiceEmptyForm(form_prefix, form_idx, fk_field_name, new_form_fk_field_name) {

    var new_form_item_code = document.getElementById('id_' + form_prefix + '-' + form_idx + '-item_code');
    var new_form_item_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-item_id');
    var new_form_item_name = document.getElementById('id_' + form_prefix + '-' + form_idx + '-item_name');
    var new_form_qty = document.getElementById('id_' + form_prefix + '-' + form_idx + '-quantity');
    var new_form_units = document.getElementById('id_' + form_prefix + '-' + form_idx + '-unit_id');
    var new_form_unit_rate = document.getElementById('id_' + form_prefix + '-' + form_idx + '-rate');
    var new_form_discount = document.getElementById('id_' + form_prefix + '-' + form_idx + '-discount');
    var new_form_amount = document.getElementById('id_' + form_prefix + '-' + form_idx + '-amount');
    var new_form_remarks = document.getElementById('id_' + form_prefix + '-' + form_idx + '-remarks');
    var new_form_item_tax = document.getElementById('id_'+ form_prefix +'-'+form_idx+'-item_tax');
    var new_form_enterprise_id= document.getElementById('id_'+ form_prefix +'-'+form_idx+'-enterprise_id');
    var new_form_hsn_code = document.getElementById('id_' + form_prefix + '-' + form_idx + '-hsn_code');
    var new_form_is_faulty = document.getElementById('id_' + form_prefix + '-' + form_idx + '-is_faulty');
    var new_form_is_service = document.getElementById('id_' + form_prefix + '-' + form_idx + '-is_service');
    var new_form_qty_label = document.getElementById('id_' + form_prefix + '-' + form_idx + '-qtyLabel');
    var new_form_is_returnable = document.getElementById('id_' + form_prefix + '-' + form_idx + '-is_returnable');
    var new_form_alternate_unit_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-alternate_unit_id');
    var new_form_scale_factor = document.getElementById('id_' + form_prefix + '-' + form_idx + '-scale_factor');
    var new_form_material_type = document.getElementById('id_' + form_prefix + '-' + form_idx +  '-material_type');


    new_form_item_code.value = document.getElementById('id_' + form_prefix + '-__prefix__-item_code').value;
    new_form_item_id.value = document.getElementById('id_' + form_prefix + '-__prefix__-item_id').value;
    new_form_qty.value = document.getElementById('id_' + form_prefix + '-__prefix__-quantity').value;

    new_form_unit_rate.value = document.getElementById('id_' + form_prefix + '-__prefix__-rate').value;
    new_form_discount.value = document.getElementById('id_' + form_prefix + '-__prefix__-discount').value;
    new_form_amount.value = document.getElementById('id_'+form_prefix + '-__prefix__-amount').value;
    new_form_remarks.value = document.getElementById('id_' + form_prefix + '-__prefix__-remarks').value;
    new_form_enterprise_id.value = document.getElementById('id_'+form_prefix +'-__prefix__-enterprise_id').value;
    new_form_hsn_code.value = document.getElementById('id_'+form_prefix +'-__prefix__-hsn_code').value;
    if (document.getElementById('id_' + form_prefix + '-__prefix__-alternate_units').value == 0 ) {
        new_form_units.value = document.getElementById('id_'+form_prefix +'-__prefix__-unit_id').value;
    } else{
        new_form_units.value = $('#id_invoice_mat-__prefix__-alternate_units option:selected').text();
        new_form_alternate_unit_id.value = $('#id_invoice_mat-__prefix__-alternate_units option:selected').val();
        new_form_scale_factor.value = $('#id_invoice_mat-__prefix__-alternate_units option:selected').attr('data-val');
    }
    new_form_material_type.value = $('#id_invoice_mat-__prefix__-material_type').val()

    var item_name = document.getElementById('materialrequired').value
    if ($('#id_invoice_mat-__prefix__-make_id').val() != 1){
        var new_form_make_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-make_id');
        new_form_make_id.value = $('#id_invoice_mat-__prefix__-make_id').val();
    }else{
        var new_form_make_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-make_id');
        new_form_make_id.value = 1;
    }

    if($("#chkfaulty").is(':checked')){
       new_form_is_faulty.value = "True";
       item_name = item_name + "[Faulty]"
    }else{ new_form_is_faulty.value = "False"; }

    if($("#chkreturn").is(':checked')){
       new_form_is_returnable.checked=true;
       if ($("#id_invoice-type option:selected").val()=="JDC"){
            new_form_is_returnable.classList.add("div-disabled");
       }
    }else{
        new_form_is_returnable.checked=false;
    }
    if($("#material_is_service").val() == 1) {
        item_name += `<span class="service-item-flag"></span>`;
    }
    //new_form_item_name.value = item_name;
    $(new_form_item_name).closest(".item_description").html(item_name)

    document.getElementById('id_' + form_prefix + '-__prefix__-item_code').value = '';
    document.getElementById('id_' + form_prefix + '-__prefix__-item_id').value = '';
    document.getElementById('id_' + form_prefix + '-__prefix__-quantity').value = '';
    document.getElementById('id_' + form_prefix + '-__prefix__-rate').value = '';
    document.getElementById('id_' + form_prefix + '-__prefix__-discount').value = '';
    document.getElementById('id_' + form_prefix + '-__prefix__-amount').value = '';
    document.getElementById('id_' + form_prefix + '-__prefix__-remarks').value = '';
    document.getElementById('id_' + form_prefix + '-__prefix__-material_type').value = '';
    if(($("#id_invoice-type").find('option:selected').text())=='SERVICE/LABOUR')
        $('#id_'+form_prefix +'-__prefix__-unit_id').val(1);
} /* Copy Form End Here */

/*Tax Copy Processing Start here...*/
function copyTaxEmptyForm(form_prefix, form_idx) {

	var new_form_tax_code = document.getElementById('id_' + form_prefix + '-' + form_idx + '-tax_code');
	var new_form_enterprise_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-enterprise_id');
	var new_form_invoice_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-invoice_id');

	new_form_tax_code.value = document.getElementById('id_' + form_prefix + '-__prefix__-tax_code').value;
	new_form_enterprise_id.value = document.getElementById('id_' + form_prefix + '-__prefix__-enterprise_id').value;
	new_form_invoice_id.value = document.getElementById('id_' + form_prefix + '-__prefix__-invoice_id').value;

	document.getElementById('id_' + form_prefix + '-__prefix__-tax_code').value = '';
	document.getElementById('id_' + form_prefix + '-__prefix__-enterprise_id').value = '';
	document.getElementById('id_' + form_prefix + '-__prefix__-invoice_id').value = '';
}/*Tax Copy Processing End here...*/

function sum_Invoicematerialvalue(){
	var sum = 0;
	//iterate through each textboxes and add the values
	$(".txt").each(function() {
	    //add only if the value is number
		if(!isNaN(this.value) && this.value.length!=0) {
			sum += parseFloat(this.value);
		}
	});
}

function clear_txt() {
	$('#material_name').val("");
	$('#m_qty').val("");
	$('#m_unit_rate').val("");
	$('#acc_rate').val("");
	$('#net_value').val("");
	$('#m_desc').val("");
	$('#material_name').focus();
}

// request permission on page load
document.addEventListener('DOMContentLoaded', function () {
    if (Notification.permission !== "granted")
        Notification.requestPermission();
});

function showpopup(){
   $("#popup_box").fadeToggle();
   $("#popup_box").css({"visibility":"visible","display":"block"});
}

function smartPopulateTax(cgst,sgst,igst,material_type,index){
    if($('#id_invoice_mat-__prefix__-make_id').val() !=null){
        make_id = $('#id_invoice_mat-__prefix__-make_id').val();
    }else{
        make_id =1;
    }
    is_faulty = $('#id_invoice_mat-'+index+'-is_faulty').val();
    var party_id=$("#id_invoice-party_id option:selected").val();
    if(material_type == 1){
        item_id = $('#material_id').val();
    }
    $.ajax({
        url: "/erp/stores/json/smart_populate_tax/",
        type: "post",
        datatype:"json",
        data: {"item_id": item_id,"make_id": make_id, "is_faulty":is_faulty, "party_id": party_id, "material_type" :material_type},
        success: function(response){
            cgst.val(response['CGST']);
            sgst.val(response['SGST']);
            igst.val(response['IGST']);
            calculateGrandTotal();
        }
    });

}

function showstock() {
    if ($('#materialrequired').val() != "" && $('#material_id_hidden').val() != "" ){
        $("#loading").show();
        var invoice_id = "";
        if($('#id_invoice_mat-__prefix__-make_id').val() !=null && $('#id_invoice_mat-__prefix__-make_id').val() != "" && $('#id_invoice_mat-__prefix__-make_id').val() != undefined){
            make_id = $('#id_invoice_mat-__prefix__-make_id').val();
        }else{
            make_id =1;
        }
        if($("#chkfaulty").is(':checked')){
            faulty = 1;
        }else{
            faulty = 0;
        }
        if($("#search_invoice_id").val()!="None"){
            invoice_id = $("#search_invoice_id").val()
        }
        $.ajax({
            url : "/erp/stores/json/issue/closingstock/",
            type : "POST",
            dataType: "json",
            data :  {"item_id": $('#material_id_hidden').val(), "make_id": make_id, "is_faulty": faulty, "invoice_id": invoice_id,
                                 "issued_on": $('#id_invoice-issued_on').val(), "alternate_unit_id": 0, "location_id": $('#id_invoice-location_id option:selected').val() },
            success: function(response) {
                if (response['closing_stock'] && response['closing_stock'].length > 0) {
                    $.each(response['closing_stock'], function(i, item) {
                        document.getElementById("primary_closing_qty").value = item['closing_qty'];
                        document.getElementById("primary_Avl_Qty").value = item['closing_qty'];
                        document.getElementById("primary_msl_qty").value = item['msl_qty'];
                        document.getElementById("closing_qty").innerText = "MAX ISSUE:" + item['closing_qty'];
                        document.getElementById("Avl_Qty").value = item['closing_qty'];
                        document.getElementById("msl_qty").value = item['msl_qty'];
                        if ($("#id_invoice_mat-__prefix__-alternate_units:visible").length > 0) {
                            calculateStockValue($("#id_invoice_mat-__prefix__-alternate_units"));
                        }
                    });
                } else {
                    document.getElementById("primary_closing_qty").value = 0;
                    document.getElementById("primary_Avl_Qty").value = 0;
                    document.getElementById("primary_msl_qty").value = 0;
                    document.getElementById("closing_qty").innerText = "MAX ISSUE: 0";
                    document.getElementById("Avl_Qty").value = 0;
                    document.getElementById("msl_qty").value = 0;
                }
                $("#loading").hide();
            },
            error : function(xhr,errmsg,err) {
                console.warning(xhr.status + ": " + xhr.responseText);
                $("#loading").hide();
            }
        });
    } else {
        swal("","Please select a material","warning");
        return;
    }
}

function showAllClosingStocks(is_edit_changed) {
    var mat_list = [];
    var invoice_id = "";
     var currentQuantity;
    if($("#search_invoice_id").val()!="None"){
        invoice_id = $("#search_invoice_id").val()
    }
    $("#invoice_materials_table").find(".invoice_item_row:visible").each(function(){
        if($(this).find(".is_faulty").val() === 'False'){
            faulty = 0;
        }else{
            faulty = 1;
        }
        currentQuantity = $(this).find(".td_inv_qty").find("input");
        if($(this).attr("id").indexOf("non_stock") < 0) {
            if($(this).find(".is_stock_material").find("input").val() != "0") {
                var material_obj =  {   "drawing_no":$(this).find(".item_code").val(),
                                        "item_id":$(this).find(".item_id").val(),
                                        "make_id":$(this).find(".make_id").val(),
                                        "is_faulty":faulty,
                                        "invoice_id": invoice_id,
                                        "alternate_unit_id": $(this).find(".alternate_unit_id").val()
                                    };
                var materialJSON = JSON.stringify(material_obj);
                mat_list.push(materialJSON);
            }
        }
    });
    if (mat_list.length < 1){
        return;
    }
    $("#loading").show().find(".loading-sub-text").html(`Validating Quantity, please wait<span>...</span>`);
    setTimeout(function(){
        $.ajax({
            url : "/erp/stores/json/issue/closingstock/",
            type : "POST",
            dataType: "json",
            async: false,
            data : {'mat_list[]': mat_list, "issued_on": $('#id_invoice-issued_on').val(),"location_id":$('#id_invoice-location_id option:selected').val()},
            success : function(response) {
             	var json = response['closing_stock']
            	Object.keys(json).forEach(function(key){
            		$("#invoice_materials_table").find(".item_code").each(function(){
            			var closestRow = $(this).closest("tr");
            			var isFaulty = (closestRow.find(".is_faulty").val() == "True") ? "1" : "0";
            			var localItemDetails = closestRow.find(".item_id").val() +"_"+ isFaulty;
            			var jsonItemDetails = json[key].item_id+"_"+json[key].is_faulty;
            			if(localItemDetails == jsonItemDetails) {
            				var currentQuantity = $(this).closest("tr").find(".td_inv_qty input");
            				var currentQuantityCount = currentQuantity.val();
            				var msl = json[key].msl_qty;
            				var total_avl_stock;
    			            if(msl < 0){
    			            	total_avl_stock = Number(json[key].closing_qty) + Number(msl);
    			            	currentQuantity.next("p").find(".is_msl").removeClass('hide').text("(MSL: "+msl+")").attr("data-value", +msl);
    			            }
    			            else {
    			            	if(msl != 0){
    				            	total_avl_stock = Number(json[key].closing_qty) - Number(msl);
    				            	currentQuantity.next("p").find(".is_msl").removeClass('hide').text("(MSL: "+msl+")").attr("data-value", msl);
    				            }
    				            else {
    				            	total_avl_stock = Number(json[key].closing_qty);
    				            	currentQuantity.next("p").find(".is_msl").addClass('hide').text("").attr("data-value", 0);
    				            }
    			            }
    			            var totalClosingStock = json[key].closing_qty;
    			            if(is_edit_changed != 1) {
    				            var url = window.location.href;
    				            if($("#id_invoice-id").val()!= "") {
    				            	if(json[key].edit_add) {
    				            		total_avl_stock = Number(total_avl_stock) + Number(currentQuantityCount);
    				            		totalClosingStock = Number(totalClosingStock) + Number(currentQuantityCount);
    				            	}
    				            }
    			            }
    				        currentQuantity.next("p").find(".currently_available_stock").text(Number(total_avl_stock).toFixed(3));
    			            currentQuantity.next("p").find(".currently_available_stock_vsb").text(Number(totalClosingStock).toFixed(3));
    			            validateEachMSLValue();
    			            /*if(currentQuantity.val() > total_avl_stock) {
    			            	currentQuantity.addClass('error-border').next("p").removeClass("hide");
    			            }
    			            else {
    			            	currentQuantity.removeClass('error-border').next("p").addClass("hide");
    			            }*/
            			}
            		});
    			});
                $("#loading").hide().find(".loading-sub-text").html(`Processing, please wait<span>...</span>`);
            },
            error: function(){
                $("#loading").hide().find(".loading-sub-text").html(`Processing, please wait<span>...</span>`);
            }
        });
    },10);
}

function loadMaterial(loadType = "") {
    var project = JSON.parse(localStorage.getItem('project'));
    var dataToSend = {
        'type': $("#id_invoice-type option:selected").val(),
        'party_id': $("#id_invoice-party_id option:selected").val(),
        'module': 'invoice',
        'particulars':'invoice_materials'
    }
    var local_material_list  = JSON.parse(localStorage.getItem('goods_service_master_data'));
    if(loadType == "onchange" && local_material_list) {
        var material_choices = local_material_list[0].material;
    }
    else {
        var material_choices = generateMaterialAsAutoComplete(dataToSend);
    }
    var frequently_material_choices = generateFrequentlyMaterialAsAutoComplete(dataToSend);
    $("#materialrequired").materialAutocomplete({
        source: frequently_material_choices.concat(material_choices),
        minLength:1,
        response: function( event, ui ) {
            ui.content.unshift({value:"add_new_item", label:"+ ADD NEW MATERIAL ", category: ""});
        },
        open: function(event, ui) {
            $("ul.ui-autocomplete.ui-menu a:contains('+ ADD NEW MATERIAL')").addClass("add_new_material_link");
            if(project && project.type == 'Secondary'){
            $("ul.ui-autocomplete.ui-menu a:contains('+ ADD NEW MATERIAL')").addClass("hide");
            }
        },
        select: function (event, ui) {
            event.preventDefault();
            if(ui.item.value != "add_new_item") {
                var itemName = ui.item.label.replace("<span class='service-item-flag'></span>", '');
                $("#individual_item_description").addClass("hide");
                $("#description_display").html('');
                $("#material_id_hidden").val(ui.item.id);
                $("#material_id").val(ui.item.id);
                $("#is_stock").val(ui.item.stock);
                $("#material_is_service").val(ui.item.is_service);
                $("#materialrequired").val(itemName).attr("readonly", true);
                $("#id_invoice_mat-__prefix__-item_id").val(ui.item.id);
                $("#id_invoice_mat-__prefix__-item_code").val(ui.item.id);
                $("#id_invoice_mat-__prefix__-make_id").val(ui.item.mid);
                $("#id_invoice_mat-__prefix__-is_faulty").val(ui.item.is_faulty);
                $("#id_invoice_mat-__prefix__-material_type").val(ui.item.stock);
                if ($("#id_invoice-type option:selected").val()!="DC"){
                    $("#id_invoice_mat-__prefix__-rate").val(ui.item.rate);
                } else {
                    $("#id_invoice_mat-__prefix__-rate").val("0.00");
                }
                $("#id_invoice_mat-__prefix__-item_name").val(ui.item.label);
                $("#id_invoice_mat-__prefix__-unit_id").val(ui.item.unit);
                $(".unit_display").html(ui.item.unit);
                //$("#individual_item_description").removeClass("hide");
                //$("#description_display").html(ui.item.description);
                $('#id_invoice_mat-__prefix__-alternate_units').html("");
                $("#id_invoice_mat-__prefix__-hsn_code").val(ui.item.hsn);
                if($('#is_multiple_units').val()=="True" ){
                    if (ui.item.alt_uom > 0){
                        loadAlternateUnits(ui.item.id, ui.item.unit, '#id_invoice_mat-__prefix__-alternate_units');
                        $(".alternate_unit_select_box").removeClass("hide");
                    }
                    else {
                        $(".alternate_unit_select_box").addClass("hide");
                    }
                }

                if(parseInt($('#is_stock').val())== 1 && !$("#invoice_quick_add").is(":checked")) {
                    $("#closing_qty").text("MAX ISSUE:0");
                    showstock();
                }
                else {
                    $("#closing_qty").text("");
                }
                loadPartyRate();
                if (parseInt(ui.item.bom) > 0 && $("#id_dc_type").val() == "internal"){
                    $("#explode_bom").show();
                }else{
                    $("#explode_bom").hide();
                }
                $(".material-removal-icon").removeClass('hide');
                $(".error-border").removeClass('error-border');
                $(".custom-error-message").remove();
                $(".suggestion-container").remove();
            }
            else {
                $("#materialrequired").val('');
                openMaterialAddModal();
            }
        }
    }).on('focus', function() { $(this).keydown(); });
}

$(function () {
    $("select#id_invoice-party_id").change(function(){
        if($("#id_invoice-se_id").val() != "" ) {
            $('.se_no_row').addClass('hide');
        }
        var party_id = $("#id_invoice-party_id").val();
        if (party_id !="add_new_party"){
            $("#id_invoice-ship_to_name").val($("#id_invoice-party_id option:selected").text())
            ChangePartyCurrency(party_id,"id_invoice-currency_id","id_invoice-currency_conversion_rate");
            currencyChangeEvent("onchange")
            loadMaterial("onchange");
        }
    });
});

$(function(){
    var hash = window.location.hash;
    hash && $('ul.nav a[href="' + hash + '"]').tab('show');

    $('.nav-tabs a').click(function (e) {
        $(this).tab('show');
        var scrollmem = $('body').scrollTop() || $('html').scrollTop();
        window.location.hash = this.hash;
        $('html,body').scrollTop(scrollmem);
    });
});

function InvoicePrevNextPaging() {
	if($(".header_current_page").text().indexOf("New") >=0 || $(".header_current_page").text() == "") {
		$(".prev_next_container").remove();
	}
	else {
		if($("#id_dc_type").val() == "sales") {
			var InvoiceListNav = JSON.parse(localStorage.getItem('invoiceListNav'));
			var pageName = "Invoice";
		}
		else if($("#id_dc_type").val() == "dc") {
			var InvoiceListNav = JSON.parse(localStorage.getItem('dcListNav'));
			var pageName = "DC";
		}
		else if($("#id_dc_type").val() == "internal") {
			var InvoiceListNav = JSON.parse(localStorage.getItem('issueListNav'));
			var pageName = "Issue";
		}
		if(InvoiceListNav != null) {
			var curInvoiceId = $("#search_invoice_id").val();
			for (var i = 0; i < InvoiceListNav.length; i++){
			  if (InvoiceListNav[i].invoiceId == curInvoiceId){
			  	if(i != 0) {
				    var prevInvoiceId = InvoiceListNav[i-1].invoiceId;
				    var prevInvoiceNo = InvoiceListNav[i-1].invoiceNumber;
				    var prevInvoiceType = InvoiceListNav[i-1].invoiceType;
				}
				if(i != Number(InvoiceListNav.length - 1)) {
			     	var nextInvoiceId = InvoiceListNav[i+1].invoiceId;
			     	var nextInvoiceNo = InvoiceListNav[i+1].invoiceNumber;
			     	var nextInvoiceType = InvoiceListNav[i+1].invoiceType;
			     }
			  }
			}
			var PrevNextInvoice = "";
			url_link = '/erp/sales/invoice/'
            if ($("#id_invoice-type").val() == "internal"){
                url_link = '/erp/stores/issue/'
            }
            else if($("#id_invoice-type").val() == "dc"){
                url_link = '/erp/stores/dc/'
            }
			if(prevInvoiceId) {
                PrevNextInvoice +=  `<form id="invoice_edit_${prevInvoiceId}" method="post" action="${url_link}">
                                        <input type="hidden" name="csrfmiddlewaretoken" value="${getCookie('csrftoken')}">
                                        <a role="button" class="edit_link_code btn btn-add-new pull-right btn-data-tooltip" data-tooltip="tooltip" title="Prev. ${pageName}: ${prevInvoiceNo}" style="margin-right: 7px;" onclick="javascript:clickButton('editInvoice_${prevInvoiceId}');"><i class="fa fa-backward" aria-hidden="true"></i></a>
                                        <input type="hidden" value="${prevInvoiceId}" id="id_invoice_no_${prevInvoiceId}" name="invoice_no" >
                                        <input type="hidden" value="${prevInvoiceType}" id="id_edit_dc_type_${prevInvoiceId}" name="edit_dc_type" >
                                        <input type="submit" value="Edit" id="editInvoice_${prevInvoiceId}" hidden="hidden">
                                    </form>`
			}
			else {
				PrevNextInvoice += '<form><a class="btn btn-add-new pull-right disabled btn-data-tooltip" data-tooltip="tooltip" title="Next/ Prev will appear only for the current page" style="padding: 5px 12px; margin-right: 7px;"><i class="fa fa-backward" aria-hidden="true"></i></a></form>';
			}
			if(nextInvoiceId) {
                PrevNextInvoice +=  `<form id="invoice_edit_${nextInvoiceId}" method="post" action="${url_link}">
                                        <input type="hidden" name="csrfmiddlewaretoken" value="${getCookie('csrftoken')}">
                                        <a role="button" class="edit_link_code btn btn-add-new pull-right btn-data-tooltip" data-tooltip="tooltip" title="Next ${pageName}: ${nextInvoiceNo}" style="margin-right: 7px;" onclick="javascript:clickButton('editInvoice_${nextInvoiceId}');"><i class="fa fa-forward" aria-hidden="true"></i></a>
                                        <input type="hidden" value="${nextInvoiceId}" id="id_invoice_no_${nextInvoiceId}" name="invoice_no" >
                                        <input type="hidden" value="${nextInvoiceType}" id="id_edit_dc_type_${nextInvoiceId}" name="edit_dc_type" >
                                        <input type="submit" value="Edit" id="editInvoice_${nextInvoiceId}" hidden="hidden">
                                    </form>`
			}
			else {
				PrevNextInvoice += '<form><a class="btn btn-add-new pull-right disabled btn-data-tooltip" data-tooltip="tooltip" title="Next/ Prev will appear only for the current page" style="padding: 5px 12px; margin-right: 7px;"><i class="fa fa-forward" aria-hidden="true"></i></a></form>';
			}

			$(".prev_next_container").html(PrevNextInvoice)
			$('.btn-data-tooltip').tooltip();
		}
	}
}

setTimeout(function(){
	$('ul.nav-tabs li a').click(function() {
		if($(this).attr('id') != "invoiceView") {
			if($("#invoice_list").hasClass('dataTable')) {
				$("#invoice_list").addClass('hide');
			}
		}
		else {
			$("#invoice_list").removeClass('hide');
		}
	});
},100);

function customAutoComplete(){
    var cgst_options = "<option value=''>--</option>";
    var sgst_options = "<option value=''>--</option>";
    var igst_options = "<option value=''>--</option>";
    $.ajax({
        url: "/erp/sales/json/invoice/load_gst_choices/",
        type: "post",
        datatype: "json",
        data: {tax_type: "CGST" },
        success: function (response) {
            populateGSTOptions("CGST", cgst_options, response);
        }
    });

    $.ajax({
        url: "/erp/sales/json/invoice/load_gst_choices/",
        type: "post",
        datatype: "json",
        data: {tax_type: "SGST" },
        success: function (response) {
            populateGSTOptions("SGST", sgst_options, response);
        }
    });

    $.ajax({
        url: "/erp/sales/json/invoice/load_gst_choices/",
        type: "post",
        datatype: "json",
        data: {tax_type: "IGST" },
        success: function (response) {
            populateGSTOptions("IGST", igst_options, response);
        }
    });
}

function populateGSTOptions(type, options, response){
    for (i = 0; i < response.length; i++) {
        options = options + "<option value='" + response[i]["code"] + "'>" + response[i]["rate"] + "</option>";
    }
    gst_tax_fields = document.getElementsByName(type);
    for(i=0; i < gst_tax_fields.length; i++){
        gst_tax_fields[i].innerHTML = gst_tax_fields[i].innerHTML + options;
    }
}

function calculateGSTAmount(tax_form_prefix, item_form_prefix){
    var rate = $("#id_" + tax_form_prefix + "-rate_drop_down option:selected").text();
    if(!isNaN(rate) && rate!=""){
        $("#id_" + tax_form_prefix + "-rate").val(parseFloat(rate));
    } else {
        $("#id_" + tax_form_prefix + "-rate").val('0.00');
    }
    $("#id_" + tax_form_prefix + "-tax_code").val($("#id_" + tax_form_prefix + "-rate_drop_down option:selected").val());
    var item_price = parseFloat($("#id_"+item_form_prefix+"-rate").val());
    var discount = parseFloat($("#id_"+item_form_prefix+"-discount").val());
    var quantity = parseFloat($("#id_"+item_form_prefix+"-quantity").val());
    $("#id_" + tax_form_prefix + "-amount").val((parseFloat($("#id_" + tax_form_prefix + "-rate").val()) * (item_price * quantity * (100 - discount)/(100 * 100))).toFixed(2));
    $("#id_span_inv_mat_tax_" + tax_form_prefix + "-amount").text((parseFloat($("#id_" + tax_form_prefix + "-rate").val()) * (item_price * quantity * (100 - discount)/(100 * 100))).toFixed(2));
    $("#id_span_" + tax_form_prefix + "-amount").text((parseFloat($("#id_" + tax_form_prefix + "-rate").val()) * (item_price * quantity * (100 - discount)/(100 * 100))).toFixed(2));
}

function calculateGSTTotals(){
    var cgst_values = document.getElementsByName("CGST_AMT");
    $("#net_cgst_value").val(0);
    for(i=0; i<cgst_values.length; i++){
        if(!isNaN(parseFloat(cgst_values[i].innerHTML)))
            $("#net_cgst_value").val((parseFloat($("#net_cgst_value").val()) + parseFloat(cgst_values[i].innerHTML)).toFixed(2));
    }

    var sgst_values = document.getElementsByName("SGST_AMT");
    $("#net_sgst_value").val(0);
    for(i=0; i<sgst_values.length; i++){
        if(!isNaN(parseFloat(sgst_values[i].innerHTML)))
            $("#net_sgst_value").val((parseFloat($("#net_sgst_value").val()) + parseFloat(sgst_values[i].innerHTML)).toFixed(2));
    }

    var igst_values = document.getElementsByName("IGST_AMT");
    $("#net_igst_value").val(0);
    for(i=0; i<igst_values.length; i++){
        if(!isNaN(parseFloat(igst_values[i].innerHTML)))
            $("#net_igst_value").val((parseFloat($("#net_igst_value").val()) + parseFloat(igst_values[i].innerHTML)).toFixed(2));
    }
}

function addInvoice() {
//    $(".error-border").removeClass("error-border");
//    $(".custom-error-message, .suggestion-container").remove();
    var quantityCheck = true;
    var ControlCollections = [];
    var allocateQtyCheck = true;
    const text = $("#id_invoice-job_po_id option:selected").text();
    console.log("text",text);
    $("#cattable_2 tbody").find("tr[data-toggle='close']").each(function(){
        if($('#id_invoice-type').val() == "Issue"){
            var currentQty = $(this).find("input[name='catmaterial_qty']").val();
            if(!text.includes('/PP/') && $("#id_invoice-job_po_id").val() != 0){
                var maxQty = $(this).find('.stock_qty').text();
                if (Number(maxQty) < Number(currentQty)){
                    quantityCheck = false
                }
            }
        }
        if(currentQty > parseFloat($(this).find(".allocation-qty").text() - (parseFloat($(this).find("#issued_qty").text()))) && text.includes('PP')){
            allocateQtyCheck = false;
            $(this).find("input[name='catmaterial_qty']").addClass("error-border")
        }
        if(currentQty > parseFloat($(this).find("#mrs_qty").text())){
            allocateQtyCheck = false;
            $(this).find("input[name='catmaterial_qty']").addClass("error-border")
        }

        if(currentQty > 0 && $(this).attr("data-toggle") != "open" && $("#id_dc_type").val() != "internal") {
            var currentHsn = $(this).find("input[name*='hsn_code']");
            var currentElementId = currentHsn.attr("id");
            var control = {
                controltype: 'textbox',
                controlid: currentElementId,
                isrequired: true,
                errormsg: 'HSN/SAC is required.',
                ishsn: true,
                hsnerrormsg: 'Invalid HSN Code',
                suggestioncontainer: "hsn-wrapper"
            }
            ControlCollections[ControlCollections.length] = control;
        }
        if($("#id_dc_type").val() == "internal"){
             $('input[name="catmaterial_hsn_code"]').removeClass('mandatory_field error-border');
        }
    });
    if (!allocateQtyCheck) {
        if ($("#id_invoice-job_po_id").val() != 0) {
            let errorMessage = "";

            if (text.includes('/PP/')) {
                errorMessage = `Issue Qty cannot be greater than Allocated Qty.`;
            } else if (text.includes('/MRS/')) {
                errorMessage = `Issue Qty cannot be greater than MRS Qty.`;
            }

            if (errorMessage) {
                swal({
                    title: "",
                    text: errorMessage,
                    type: "error"
                });
            }
        }
    }
    else{
        if(quantityCheck) {
            var result = JSCustomValidator.JSvalidate(ControlCollections);
            var validationResult = BOMValidationCheck();

            if (!result || !validationResult.isValid) {
                return;
            }
            var warning_message = "";
            if(validationResult.exceeds_stock == 0
             && validationResult.exceeds_pending == 0
             && validationResult.exceeds_msl == 0
             && validationResult.exceeds_zero == 0) {
                addInvoiceMaterial();
            }
            else if(validationResult.exceeds_stock > 0) {
                if(validationResult.exceeds_stock > 1) {
                        swal("", "Quantity of " + validationResult.exceeds_stock + " materials are greater than Stock Quantity. Please check!!", "error");
                }
                else {
                        swal("", "Quantity of a material is greater than Stock Quantity. Please check!!", "error");
                }
            }
            else if (validationResult.exceeds_pending > 0) {
                if(validationResult.exceeds_pending > 1) {
                        swal("", "Quantity of " + validationResult.exceeds_pending + " materials are greater than Pending Quantity. Please check!!", "error");
                } else {
                        swal("", "Quantity of a material is greater than Pending Quantity. Please check!!", "error");
                }
            }
            else {
                if(validationResult.exceeds_msl > 0) {
                    if(validationResult.exceeds_msl > 1) {
                        warning_message = "Stock of " + validationResult.exceeds_msl + " materials will be less than the Minimum Stock Level.";
                    }else {
                        warning_message = "Stock of a material will be less than the Minimum Stock Level.";
                    }
                }
                if(validationResult.exceeds_zero > 0) {
                    if(validationResult.exceeds_zero > 1) {
                        warning_message =  warning_message + "</br></br>Stock of " + validationResult.exceeds_zero + " materials will be negative once this Invoice is made.";
                    }
                    else {
                        warning_message =  warning_message + "</br></br>Stock of a material will be negative once this Invoice is made.";
                    }
                }
                warning_message = warning_message + "</br></br> Do you still want to add materials ?";
                swal({
                    title: "",
                    text: warning_message,
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#209be1",
                    confirmButtonText: "Yes, do it!",
                    closeOnConfirm: true,
                    closeOnCancel: true
                },
                function(isConfirm){
                    if (isConfirm) {
                        addInvoiceMaterial();
                        listTableHoverIconsInit('invoice_materials_table');
                    }
                });
            }
            listTableHoverIconsInit('invoice_materials_table');
        }

        else{
            if($("#is_super_user").val().toLowerCase() == 'true'){
                swal({
                    title: "",
                    text: `Stock  is not available. <br /><br /> Do you want to continue? `,
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#209be1",
                    confirmButtonText: "Yes, do it!",
                    closeOnConfirm: true,
                    closeOnCancel: true
                },
                function(isConfirm){
                    if (isConfirm) {
                        addInvoiceMaterial();
                        listTableHoverIconsInit('invoice_materials_table');
                    }
                });
            }
            else{
                swal({
                    title: "",
                    text: `Material Qty cannot be greater than Stock Qty.`,
                    type: "error"
                });
                return false
            }
        }
    }
}

function addInvoiceMaterial() {
    var table = document.getElementById("cattable_2");
    var rowCount = table.rows.length;
    var input_obj = document.getElementsByName('catmaterial_qty');
    var input_obj_hsn_code = document.getElementsByName('catmaterial_hsn_code');
    var input_obj_item_id = document.getElementsByName('catmaterial_item_id');
    var input_obj_unit_rate = document.getElementsByName('catmaterial_unitrate');
    var input_obj_disc = document.getElementsByName('catmaterial_disc');
    var input_obj_amount = document.getElementsByName('catmaterial_amount');
    var input_obj_alternate_unit_id = document.getElementsByName('catmaterial_alternate_unit_id');
    var input_obj_scale_factor = document.getElementsByName('catmaterial_scale_factor');
    var input_obj_unit_id = document.getElementsByName('catmaterial_unit_id');
    var input_obj_material_type = document.getElementsByName('catmaterial_material_type');
    var input_obj_is_service = document.getElementsByName('catmaterial_is_service');
    var input_obj_oa_id = document.getElementsByName('catmaterial_oa_id');
    var match = false;
    for (i = 1; i < rowCount; i++) {
        match = false;
        if($(table.rows[i]).data('toggle') == 'close') {
            if (parseFloat(input_obj[i - 1].value) > 0) {
                var materialtable = document.getElementById("invoice_materials_table");
                var materialrowCount = materialtable.rows.length;

                for (j = 1; j < materialrowCount; j++) {
                    if ($(input_obj[i-1]).closest('tr').find(".td_item_description").find('select option:selected').text() !="") {
                        if ( $('#id_invoice_mat-' + parseInt(j - 1) + '-make_id').val() == $(input_obj[i-1]).closest('tr').find(".td_item_description").find('select').val() && $('#id_invoice_mat-' + parseInt(j - 1) + '-item_id').val() == input_obj_item_id[i - 1].value && !document.getElementById("id_invoice_mat-"+parseInt(j - 1)+"-DELETE").checked) {
                            match = true;
                            document.getElementById('id_invoice_mat-' + parseInt(j - 1) + '-quantity').value = parseFloat($('#id_invoice_mat-' + parseInt(j - 1) + '-quantity').val()) + parseFloat(input_obj[i - 1].value);
                            var qty_label = document.getElementById('id_invoice_mat-' + parseInt(j - 1) + '-quantity');
                            qty_label.value = parseFloat($('#id_invoice_mat-' + parseInt(j - 1) + '-quantity').val());
                        }
                    } else {
                        if ($('#id_invoice_mat-' + parseInt(j - 1) + '-item_id').val() == input_obj_item_id[i - 1].value && !document.getElementById("id_invoice_mat-"+parseInt(j - 1)+"-DELETE").checked) {
                            match = true;
                            document.getElementById('id_invoice_mat-' + parseInt(j - 1) + '-quantity').value = parseFloat($('#id_invoice_mat-' + parseInt(j - 1) + '-quantity').val()) + parseFloat(input_obj[i - 1].value);
                            var qty_label = document.getElementById('id_invoice_mat-' + parseInt(j - 1) + '-quantity');
                            qty_label.value = parseFloat($('#id_invoice_mat-' + parseInt(j - 1) + '-quantity').val());
                        }
                    }
                }

                if (match == false) {
                    var is_non_stock_material = !$(table.rows[i]).find(".stock_qty").hasClass('stockable');
                    invoice_item_form_prefix = 'id_invoice_mat-';
                    document.getElementById(invoice_item_form_prefix + '__prefix__-item_name').value = $(table.rows[i].cells[3]).text();
                    document.getElementById(invoice_item_form_prefix + '__prefix__-item_id').value = input_obj_item_id[i - 1].value;
                    document.getElementById(invoice_item_form_prefix + '__prefix__-hsn_code').value = input_obj_hsn_code[i - 1].value;
                    document.getElementById(invoice_item_form_prefix + '__prefix__-quantity').value = parseFloat(input_obj[i - 1].value);
                    document.getElementById(invoice_item_form_prefix + '__prefix__-unit_id').value = $(table.rows[i].cells[8]).text();
                    document.getElementById(invoice_item_form_prefix + '__prefix__-rate').value = parseFloat(input_obj_unit_rate[i - 1].value);
                    document.getElementById(invoice_item_form_prefix + '__prefix__-discount').value = (isNaN(parseFloat(input_obj_disc[i - 1].value)) != true) ? parseFloat(input_obj_disc[i - 1].value):0;
                    document.getElementById(invoice_item_form_prefix + '__prefix__-amount').value = parseFloat(input_obj_amount[i - 1].value);
                    document.getElementById(invoice_item_form_prefix + '__prefix__-enterprise_id').value = document.getElementById('enterprise_id').value
                    document.getElementById("id_invoice_mat-__prefix__-is_service").value = $(table.rows[i]).find("input[name='catmaterial_is_service']").val()
                    var item_name = $(table.rows[i].cells[3]).text();

                    document.getElementById(invoice_item_form_prefix + '__prefix__-item_code').value = $(table.rows[i].cells[13]).text();
                    if ($(table.rows[i].cells[22]).text() !="" && $(table.rows[i].cells[22]).text() !="null"){
                        item_name = item_name + "-" + $(table.rows[i].cells[22]).text()
                    }
//                    generateFormsetFormRowAtIndex('invoice_mat', '#packing_diff_line');
                    if($("#id_invoice_mat-__prefix__-is_service").val() == 1) {
                        generateFormsetFormRowAppend('invoice_mat', ".item-for-service");
                        $(".item-for-service").removeClass('hide');
                    }
                    else {
                        generateFormsetFormRowAppend('invoice_mat', ".item-for-goods");
                        $(".item-for-goods").removeClass('hide');
                    }
                    var index = parseInt(parseInt($('#' + invoice_item_form_prefix + 'TOTAL_FORMS').val()) - 1);
                    $("#invoice_mat-"+index).addClass("invoice_item_row");
                    var new_form_item_code = document.getElementById(invoice_item_form_prefix + index + '-item_code');
                    var qty_label = document.getElementById(invoice_item_form_prefix + index + '-quantity');

                    new_form_item_code.value = document.getElementById(invoice_item_form_prefix + '__prefix__-item_code').value;
                    qty_label.value = parseFloat(input_obj[i - 1].value);
                    document.getElementById(invoice_item_form_prefix + '__prefix__-alternate_unit_id').value = (isNaN(parseFloat(input_obj_alternate_unit_id[i - 1].value)) != true) ? parseFloat(input_obj_alternate_unit_id[i - 1].value):0;
                    document.getElementById(invoice_item_form_prefix + '__prefix__-scale_factor').value = (isNaN(parseFloat(input_obj_scale_factor[i - 1].value)) != true) ? parseFloat(input_obj_scale_factor[i - 1].value):1.000;
                    document.getElementById(invoice_item_form_prefix + '__prefix__-material_type').value = (isNaN(parseFloat(input_obj_material_type[i - 1].value)) != true) ? parseFloat(input_obj_material_type[i - 1].value):1;

                    if($('#id_issue_type').val().toLowerCase() == "mrs"){
                        document.getElementById("id_invoice_mat-__prefix__-oa_no").value = (isNaN(parseFloat(input_obj_oa_id[i - 1].value)) != true) ? parseFloat(input_obj_oa_id[i - 1].value):0;
                    }
                    material_index = getMaterialIndexCount();
                    var index = parseInt(parseInt($('#' + invoice_item_form_prefix + 'TOTAL_FORMS').val()) - 1);
                    var entry_order = document.getElementById(invoice_item_form_prefix + index + '-entry_order');
                    var new_form_item_name = document.getElementById(invoice_item_form_prefix + index + '-item_name');
                    var new_form_item_id = document.getElementById(invoice_item_form_prefix + index + '-item_id');
                    var new_form_item_hsn_code = document.getElementById(invoice_item_form_prefix + index + '-hsn_code');
                    var new_form_quantity = document.getElementById(invoice_item_form_prefix + index + '-quantity');
                    var new_form_rate = document.getElementById(invoice_item_form_prefix + index + '-rate');
                    var new_form_discount = document.getElementById(invoice_item_form_prefix + index + '-discount');
                    var new_form_unit_id = document.getElementById(invoice_item_form_prefix + index + '-unit_id');
                    var new_form_amount = document.getElementById(invoice_item_form_prefix + index + '-amount');
                    var new_form_item_enterprise_id = document.getElementById(invoice_item_form_prefix + index + '-enterprise_id');
                    var new_form_item_is_returnable = document.getElementById(invoice_item_form_prefix + index + '-is_returnable');
                    var new_form_alternate_unit_id = document.getElementById(invoice_item_form_prefix + index + '-alternate_unit_id');
                    var new_form_scale_factor = document.getElementById(invoice_item_form_prefix + index + '-scale_factor');
                    var new_form_material_type = document.getElementById(invoice_item_form_prefix + index + '-material_type');
                    var new_form_oa_no = document.getElementById(invoice_item_form_prefix + index + '-oa_no');

                    new_form_item_hsn_code.value = document.getElementById(invoice_item_form_prefix + '__prefix__-hsn_code').value;

                    new_form_item_id.value = document.getElementById(invoice_item_form_prefix + '__prefix__-item_id').value;
                    new_form_alternate_unit_id.value = document.getElementById(invoice_item_form_prefix + '__prefix__-alternate_unit_id').value;
                    new_form_scale_factor.value = document.getElementById(invoice_item_form_prefix + '__prefix__-scale_factor').value;

                    new_form_quantity.value = document.getElementById(invoice_item_form_prefix + '__prefix__-quantity').value;
                    var allocated_qty = $(table.rows[i].cells[6]).find('.qty-label #allocated_qty').val();
                    $(new_form_quantity).closest('div').find('.allocated_qty').text(allocated_qty);

                    new_form_rate.value = document.getElementById(invoice_item_form_prefix + '__prefix__-rate').value;
                    new_form_discount.value = document.getElementById(invoice_item_form_prefix + '__prefix__-discount').value;
                    new_form_unit_id.value = document.getElementById(invoice_item_form_prefix + '__prefix__-unit_id').value;
                    new_form_amount.value = document.getElementById(invoice_item_form_prefix + '__prefix__-amount').value;
                    new_form_item_enterprise_id.value = document.getElementById(invoice_item_form_prefix + '__prefix__-enterprise_id').value;
                    new_form_material_type.value = document.getElementById(invoice_item_form_prefix + '__prefix__-material_type').value;
                    new_form_oa_no.value = document.getElementById(invoice_item_form_prefix + '__prefix__-oa_no').value;

                    new_form_item_is_returnable.checked = true
                    new_form_item_is_returnable.classList.add("div-disabled");
                    entry_order.value = material_index
                    if ($(table.rows[i].cells[5]).text() != "-NA-"){
                        var new_form_make_id = document.getElementById(invoice_item_form_prefix + index + '-make_id');
                        var makeLabel = document.getElementById(invoice_item_form_prefix + index + '-makeLabel');
                        var selectedMakeVal = $(input_obj[i-1]).closest('tr').find('select[name="make"]').first().find('option:selected').val();
                        var selectedMakeText = $(input_obj[i-1]).closest('tr').find('select[name="make"]').first().find('option:selected').text()
                        makeLabel.innerHTML = selectedMakeText;
//                        new_form_make_id.value = selectedMakeVal;
                        new_form_make_id.value = "1";
//                        if (selectedMakeVal != 1){
//                            item_name = item_name + "[" + selectedMakeText + "]"
//                        }
                    }
                    else {
                        var new_form_make_id = document.getElementById(invoice_item_form_prefix + index + '-make_id');
                        new_form_make_id.value = "1";
                    }
                    item_name= $(table.rows[i].cells[2]).text();
                    new_form_item_name.value = item_name;
                     if($("#id_invoice_mat-__prefix__-is_service").val() == 1) {
                        item_name += `<span class="service-item-flag"></span>`;
                    }
                    $(new_form_item_name).closest(".item_description").html(item_name)
                    //new_form_item_name.innerHTML = item_name;

                }
                assignSerialNumber();
                //showAllClosingStocks(0);
                isFormChanged = true;
            }
        }
    }
    $("#catalogueModal").modal('hide');
    $('#materialrequired, #id_invoice_mat-__prefix__-hsn_code, #id_invoice_mat-__prefix__-item_id').val('');
    $('#id_invoice_mat-__prefix__-quantity,#id_invoice_mat-__prefix__-rate,#id_invoice_mat-__prefix__-discount,#id_invoice_mat-__prefix__-amount').val('0.00');
    $("#unit_display").html('&nbsp;');
    $("#individual_item_description").addClass("hide");
    $("#description_display").html('') ;
    $('#material_id_hidden').val('');
    $('#material_id').val('');
    $("#explode_bom").hide();
    $(".material-removal-icon").click();
    setTimeout(function(){
        $('#materialrequired').focus();
        calculateGrandTotal();
    },250);
}

// OA Material Add Function

function addOA() {
	var isQtyExceed = false;
	isFormChanged = true;
	$(".error-border").removeClass("error-border");
	$(".error-border-stock").removeClass("error-border-stock");
    $(".custom-error-message, .suggestion-container").remove();

    var ControlCollections = [];
    $("#OAMaterialtable tbody").find("tr").each(function(){
        var currentQty = $(this).find("input[name='oamaterial_qty']").val();
        if(currentQty > 0) {
            var currentHsn = $(this).find("input[name*='hsn_code']");
            var currentElementId = currentHsn.attr("id");
            var control = {
                controltype: 'textbox',
                controlid: currentElementId,
                isrequired: true,
                errormsg: 'HSN/SAC is required.',
                ishsn: true,
                hsnerrormsg: 'Invalid HSN Code',
                suggestioncontainer: "hsn-wrapper"
            }
            ControlCollections[ControlCollections.length] = control;
        }
    });
    var result = JSCustomValidator.JSvalidate(ControlCollections);
    if($("#OAMaterialtable").find(".suggestion-container").length > 0) {
        $("#OAMaterialtable").find(".suggestion-container").each(function(){
            var isConsolidated = $(this).closest("tr").attr("data-row");
            if(isConsolidated){
                if($(this).closest("tr").hasClass("hide")) {
                    $("#OAMaterialtable").find(`tr[consolidated-for='${isConsolidated}']`).find(".consolidated-link").click();
                }
            }
        })
    }
    var validationResult = BOMValidationCheck();
    if (!result || !validationResult.isValid) {
        return;
    }

    var warning_message = "";
    const sessionProject = localStorage.getItem("project");
    const project = JSON.parse(sessionProject);
	if(sessionProject && project.type == "Secondary") {
	    if(validationResult.exceeds_stock == 0
            && validationResult.exceeds_pending == 0
            && validationResult.exceeds_msl == 0
            && validationResult.exceeds_zero == 0) {
                addOAMaterial();
        }
        else if (validationResult.exceeds_pending > 0) {
            if(validationResult.exceeds_pending > 1) {
                    swal("", "Quantity of " + validationResult.exceeds_pending + " materials are greater than Pending Quantity. Please check!!", "error");
            } else {
                    swal("", "Quantity of a material is greater than Pending Quantity. Please check!!", "error");
            }
        }
        else {
            if(validationResult.exceeds_stock > 0) {
                if(validationResult.exceeds_stock > 1) {
                    warning_message = "Quantity of " + validationResult.exceeds_stock + " materials are greater than Stock Quantity. Please check!!";
                } else {
                     warning_message = "Quantity of a material is greater than Stock Quantity. Please check!!";
                }
            }
            else if(validationResult.exceeds_msl > 0){
                if(validationResult.exceeds_msl > 1) {
                    warning_message = "Stock of " + validationResult.exceeds_msl + " materials will be less than the Minimum Stock Level.";
                } else {
                    warning_message = "Stock of a material will be less than the Minimum Stock Level.";
                }
            }
            if(validationResult.exceeds_zero > 0) {
                if(validationResult.exceeds_zero > 1) {
                    warning_message =  warning_message + "</br></br>Stock of " + validationResult.exceeds_zero + " materials will be negative once this Invoice is made.";
                } else {
                    warning_message =  warning_message + "</br></br>Stock of a material will be negative once this Invoice is made.";
                }
            }
            warning_message = warning_message + "</br></br> Do you still want to add materials ?";
            swal({
                title: "",
                text: warning_message,
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#209be1",
                confirmButtonText: "Yes, do it!",
                closeOnConfirm: true,
                closeOnCancel: true
            },
            function(isConfirm){
                if (isConfirm) {
                    addOAMaterial();
                }
            });
        }
    }
	else {
        if(validationResult.exceeds_stock == 0
            && validationResult.exceeds_pending == 0
            && validationResult.exceeds_msl == 0
            && validationResult.exceeds_zero == 0) {
                addOAMaterial();
        }
        else if(validationResult.exceeds_stock > 0) {
            if(validationResult.exceeds_stock > 1) {
                    swal("", "Quantity of " + validationResult.exceeds_stock + " materials are greater than Stock Quantity. Please check!!", "error");
            } else {
                    swal("", "Quantity of a material is greater than Stock Quantity. Please check!!", "error");
            }
        }
        else if (validationResult.exceeds_pending > 0) {
            if(validationResult.exceeds_pending > 1) {
                    swal("", "Quantity of " + validationResult.exceeds_pending + " materials are greater than Pending Quantity. Please check!!", "error");
            } else {
                    swal("", "Quantity of a material is greater than Pending Quantity. Please check!!", "error");
            }
        }
        else {
            if(validationResult.exceeds_msl > 0){
                if(validationResult.exceeds_msl > 1) {
                    warning_message = "Stock of " + validationResult.exceeds_msl + " materials will be less than the Minimum Stock Level.";
                } else {
                    warning_message = "Stock of a material will be less than the Minimum Stock Level.";
                }
            }
            if(validationResult.exceeds_zero > 0) {
                if(validationResult.exceeds_zero > 1) {
                    warning_message =  warning_message + "</br></br>Stock of " + validationResult.exceeds_zero + " materials will be negative once this Invoice is made.";
                } else {
                    warning_message =  warning_message + "</br></br>Stock of a material will be negative once this Invoice is made.";
                }
            }
            warning_message = warning_message + "</br></br> Do you still want to add materials ?";
            swal({
                title: "",
                text: warning_message,
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#209be1",
                confirmButtonText: "Yes, do it!",
                closeOnConfirm: true,
                closeOnCancel: true
            },
            function(isConfirm){
                if (isConfirm) {
                    addOAMaterial();
                }
            });
        }
    }
}

function addOAMaterial() {
    if ($('#id_invoice-type option:selected').val()=="JIN"){
        removeOARows("grn_no");
    }else{
        removeOARows("oa_no");
    }
    var table = document.getElementById("OAMaterialtable");
    var rowCount = $("#OAMaterialtable tbody").find("tr").length;
    var input_obj = document.getElementsByName('oamaterial_qty');
    var input_obj_item_id = document.getElementsByName('oamaterial_item_id');
    var input_obj_hsn_code = document.getElementsByName('oamaterial_hsn_code');
    var input_obj_unit_rate = document.getElementsByName('oamaterial_unitrate');
    var input_obj_disc = document.getElementsByName('oamaterial_disc');
    var input_obj_amount = document.getElementsByName('oamaterial_amount');
    var input_obj_make_id = document.getElementsByName('oamaterial_make_id');
    var input_obj_oa_id = document.getElementsByName('oamaterial_oa_id');
    var input_obj_material_type = document.getElementsByName('oamaterial_material_type');
    var input_obj_remarks = document.getElementsByName('oamaterial_remarks');
    var input_obj_dc_id = document.getElementsByName('oamaterial_dc_id');
    var input_obj_is_faulty = document.getElementsByName('oamaterial_is_faulty');
    var input_obj_is_service = document.getElementsByName('oamaterial_is_service');
    var input_obj_oa_code = document.getElementsByName('oamaterial_oa_code');
    var input_obj_grn_id = document.getElementsByName('oamaterial_grn_id');
    var input_obj_grn_code = document.getElementsByName('oamaterial_grn_code');
    var input_obj_alternate_unit_id = document.getElementsByName('oamaterial_alternate_unit_id');
    var input_obj_scale_factor = document.getElementsByName('oamaterial_scale_factor');
    var match = false;
    var i = 1;
    var is_service
    var is_faulty
    var oa_nos = ""
    for (iCount = 1; iCount <= rowCount; iCount++) {
        match = false;
        if(!$(table.rows[iCount]).hasClass("consolidated_row")) {
            if (parseFloat(input_obj[i-1].value) > 0) {
                var materialtable = document.getElementById("invoice_materials_table");
                var materialrowCount = materialtable.rows.length;
                var oa_no = 0, item_id = 0
                for (j = 1; j < materialrowCount; j++) {
                    if(input_obj_oa_id[i - 1].value == null || input_obj_oa_id[i - 1].value == "null" || input_obj_oa_id[i - 1].value == 0 || input_obj_oa_id[i - 1].value == "") {
                        oa_no = 0;
                    }else{
                        oa_no = input_obj_oa_id[i - 1].value
                    }
                    if(input_obj_grn_id[i - 1].value == null || input_obj_grn_id[i - 1].value == "null" || input_obj_grn_id[i - 1].value == 0 || input_obj_grn_id[i - 1].value == "") {
                        grn_no = 0;
                    }else{
                        grn_no = input_obj_grn_id[i - 1].value
                    }
                    if(input_obj_item_id[i - 1].value == null || input_obj_item_id[i - 1].value == "null" || input_obj_item_id[i - 1].value == 0 || input_obj_item_id[i - 1].value == "") {
                        item_id = 0;
                    }else{
                        item_id = input_obj_item_id[i - 1].value
                    }
                    is_service = 0
                    if (input_obj_is_service[i - 1].value != 0){
                        is_service = 1
                    }
                    is_faulty = 'False'
                    if (input_obj_make_id[i - 1].value !="") {
                        if (input_obj_is_faulty[i - 1].value != 0){
                            is_faulty = 'True'
                        }
                        if ( $('#id_invoice_mat-' + parseInt(j - 1) + '-make_id').val() == input_obj_make_id[i - 1].value
                            && $('#id_invoice_mat-' + parseInt(j - 1) + '-item_id').val() == item_id
                            && $('#id_invoice_mat-' + parseInt(j - 1) + '-oa_no').val() == oa_no
                            && $('#id_invoice_mat-' + parseInt(j - 1) + '-is_faulty').val() == is_faulty
                            && $('#id_invoice_mat-' + parseInt(j - 1) + '-grn_no').val() == grn_no
                            && !document.getElementById("id_invoice_mat-"+parseInt(j - 1)+"-DELETE").checked) {
                            if ($("#id_invoice-goods_already_supplied").is(":checked")){
                                if ($('#id_invoice_mat-' + parseInt(j - 1) + '-dc_no').val() == input_obj_dc_id[i - 1].value){
                                    match = true;
                                }
                            }else{
                                match = true;
                            }
                            if (match==true){
                                document.getElementById('id_invoice_mat-' + parseInt(j - 1) + '-quantity').value = parseFloat($('#id_invoice_mat-' + parseInt(j - 1) + '-quantity').val()) + parseFloat(input_obj[i - 1].value);
                                var qty_label = document.getElementById('id_invoice_mat-' + parseInt(j - 1) + '-quantity');
                                qty_label.value = parseFloat($('#id_invoice_mat-' + parseInt(j - 1) + '-quantity').val());
                            }

                        }
                    } else {
                        if ($('#id_invoice_mat-' + parseInt(j - 1) + '-item_id').val() == item_id
                            && $('#id_invoice_mat-' + parseInt(j - 1) + '-oa_no').val() == oa_no
                            && $('#id_invoice_mat-' + parseInt(j - 1) + '-grn_no').val() == grn_no
                            && !document.getElementById("id_invoice_mat-"+parseInt(j - 1)+"-DELETE").checked) {
                            if ($("#id_invoice-goods_already_supplied").is(":checked")){
                                if ($('#id_invoice_mat-' + parseInt(j - 1) + '-dc_no').val() == input_obj_dc_id[i - 1].value){
                                    match = true;
                                }
                            }else{
                                match = true;
                            }
                            if (match==true){
                                document.getElementById('id_invoice_mat-' + parseInt(j - 1) + '-quantity').value = parseFloat($('#id_invoice_mat-' + parseInt(j - 1) + '-quantity').val()) + parseFloat(input_obj[i - 1].value);
                                var qty_label = document.getElementById('id_invoice_mat-' + parseInt(j - 1) + '-quantity');
                                qty_label.value = parseFloat($('#id_invoice_mat-' + parseInt(j - 1) + '-quantity').val());
                            }
                        }
                    }

                }

                if (match == false) {

                    var rowItem = $(table.rows[iCount]).attr("data-row");
                    document.getElementById('id_invoice_mat-__prefix__-item_name').value = $(table.rows[iCount].cells[3]).text();
                    document.getElementById('id_invoice_mat-__prefix__-item_id').value = input_obj_item_id[i - 1].value;
                    document.getElementById('id_invoice_mat-__prefix__-hsn_code').value = input_obj_hsn_code[i - 1].value;
                    document.getElementById("id_invoice_mat-__prefix__-quantity").value = parseFloat(input_obj[i - 1].value);
                    document.getElementById("id_invoice_mat-__prefix__-unit_id").value = $(table.rows[iCount].cells[9]).text();
                    document.getElementById("id_invoice_mat-__prefix__-rate").value = parseFloat(input_obj_unit_rate[i - 1].value);
                    document.getElementById("id_invoice_mat-__prefix__-discount").value = (isNaN(parseFloat(input_obj_disc[i - 1].value)) != true) ? parseFloat(input_obj_disc[i - 1].value):0;
                    document.getElementById("id_invoice_mat-__prefix__-amount").value = parseFloat(input_obj_amount[i - 1].value);
                    document.getElementById("id_invoice_mat-__prefix__-item_code").value = $(table.rows[iCount]).find(".td_item_drawing_no").text();
                    document.getElementById("id_invoice_mat-__prefix__-enterprise_id").value = document.getElementById("enterprise_id").value
                    document.getElementById("id_invoice_mat-__prefix__-remarks").value = input_obj_remarks[i - 1].value
                    document.getElementById('id_invoice_mat-__prefix__-make_id').value = input_obj_make_id[i - 1].value;
                    if($('#id_dc_type').val().toLowerCase() == "sales" || $('#id_dc_type').val().toLowerCase() == "dc"){
                        document.getElementById("id_invoice_mat-__prefix__-oa_no").value = (isNaN(parseFloat(input_obj_oa_id[i - 1].value)) != true) ? parseFloat(input_obj_oa_id[i - 1].value):0;
                        document.getElementById('id_invoice_mat-__prefix__-oa_code').value = $(table.rows[i].cells[2]).text();
                        if($('#id_invoice-type option:selected').val()=="JIN"){
                            document.getElementById("id_invoice_mat-__prefix__-grn_no").value = parseFloat(input_obj_grn_id[i - 1].value);
                            document.getElementById('id_invoice_mat-__prefix__-grn_code').value = $(table.rows[i].cells[2]).text();
                        }
                    }
                    document.getElementById('id_invoice_mat-__prefix__-is_faulty').value = is_faulty;
                    document.getElementById('id_invoice_mat-__prefix__-is_service').value = is_service;
                    var rowItem = $(table.rows[iCount]).find('.td_item_id').find('input').val()+"__"+input_obj_make_id[i - 1].value+"__"+input_obj_is_faulty[i - 1].value+"__"+generateUniqueCode(input_obj_hsn_code[i - 1].value.trim());
                    if($("#invoice_materials_table ."+rowItem).length == 0) {
                        if(is_service == 1) {
                            generateFormsetFormRowAppend('invoice_mat', '.item-for-service');
                            $('.item-for-service').removeClass('hide');
                        }
                        else {
                            generateFormsetFormRowAppend('invoice_mat', '.item-for-goods');
                           $('.item-for-goods').removeClass('hide');
                        }
                    }
                    else {
                        var index = $("#invoice_materials_table").find("."+rowItem).first();
                        generateFormsetFormRowAtIndex('invoice_mat', index);
                    }
                    document.getElementById('id_invoice_mat-__prefix__-alternate_unit_id').value = (isNaN(parseFloat(input_obj_alternate_unit_id[i - 1].value)) != true) ? parseFloat(input_obj_alternate_unit_id[i - 1].value):0;
                    document.getElementById('id_invoice_mat-__prefix__-scale_factor').value = (isNaN(parseFloat(input_obj_scale_factor[i - 1].value)) != true) ? parseFloat(input_obj_scale_factor[i - 1].value):1.000;
                    document.getElementById('id_invoice_mat-__prefix__-material_type').value = (isNaN(parseFloat(input_obj_material_type[i - 1].value)) != true) ? parseFloat(input_obj_material_type[i - 1].value):0;
                    material_index = getMaterialIndexCount();
                    var index = parseInt(parseInt($('#id_invoice_mat-TOTAL_FORMS').val()) - 1);
                    if($('#id_dc_type').val().toLowerCase() == "sales" || $('#id_dc_type').val().toLowerCase() == "dc"){
                        document.getElementById('id_invoice_mat-__prefix__-oa_no').value =  (isNaN(parseFloat(input_obj_oa_id[i - 1].value)) != true) ? parseFloat(input_obj_oa_id[i - 1].value):0;
                        document.getElementById('id_invoice_mat-__prefix__-grn_no').value =  (isNaN(parseFloat(input_obj_grn_id[i - 1].value)) != true) ? parseFloat(input_obj_grn_id[i - 1].value):0;
                        if($("#id_invoice-goods_already_supplied").is(":checked")){
                            document.getElementById('id_invoice_mat-__prefix__-dc_no').value = parseFloat(input_obj_dc_id[i - 1].value);
                            document.getElementById('id_invoice_mat-__prefix__-dc_code').value = $(table.rows[iCount].cells[2]).find(".dc-code-block").text();
                            $('#id_invoice_mat-' + index + '-quantity').closest("tr").find(".dc_code_container").html("<b>DC No:</b> "+$(table.rows[iCount].cells[2]).find(".dc-code-block").text());
                            if(input_obj_oa_code[i - 1].value != "" && input_obj_oa_code[i - 1].value != null) {
                                $('#id_invoice_mat-' + index + '-quantity').closest("tr").find(".oa_code_container").html("<b>OA No:</b> "+input_obj_oa_code[i - 1].value);
                            }
                        }else{
                            if($(table.rows[i].cells[2]).text() != "" && $(table.rows[i].cells[2]).text() != null) {

                                if($('#id_invoice-type option:selected').val()=="JIN"){
                                    $('#id_invoice_mat-' + index + '-quantity').closest("tr").find(".oa_code_container").html("<b>GRN No:</b> "+$(table.rows[i].cells[2]).text());
                                }else{
                                    $('#id_invoice_mat-' + index + '-quantity').closest("tr").find(".oa_code_container").html("<b>OA No:</b> "+$(table.rows[i].cells[2]).text());
                                }
                            }
                        }
                    }

                    if($("#id_invoice-goods_already_supplied").is(":checked")){
                         var consolidated_hsn_sac =  generateUniqueCode(input_obj_hsn_code[i - 1].value.trim())
                        var dataConsolidation = $(table.rows[iCount]).find(".td_item_id").find("input").val()+"__"+input_obj_make_id[i - 1].value+"__"+input_obj_is_faulty[i - 1].value+"__"+consolidated_hsn_sac;
                        $("#invoice_mat-"+index).addClass("invoice_item_row "+ dataConsolidation);
                        $("#invoice_mat-"+index).attr("data-row", dataConsolidation);
                    }
                    else{
                        $("#invoice_mat-"+index).addClass("invoice_item_row material_list");
                    }

                    var total_avl_stock = $(table.rows[iCount].cells[8]).text();
                    if($("#id_invoice-goods_already_supplied").is(":checked")){
                        var oa_pending_qty = $(table.rows[iCount]).find('.invd_pending_qty').text().split('(')[0]
                    }else{
                        var oa_pending_qty = $(table.rows[iCount]).find('.oa_dc_pending_qty').text().split('(')[0]
                    }
                    var msl = $(table.rows[iCount].cells[8]).find("span").attr("data-original-title").split(":")[1].trim();

                    $("#id_invoice_mat-"+index+"-quantity").closest("td").find(".available-qty-container").find(".currently_available_stock").text(Number(total_avl_stock).toFixed(3));
                    $("#id_invoice_mat-"+index+"-quantity").closest("td").find(".available-qty-container").find(".currently_available_stock_vsb").text(Number(total_avl_stock).toFixed(3));
                    $("#id_invoice_mat-"+index+"-quantity").closest("td").find(".available-qty-container").find(".currently_available_pending").text(Number(oa_pending_qty).toFixed(3)).attr("data-default-value",Number(oa_pending_qty).toFixed(3));
                    $("#id_invoice_mat-"+index+"-quantity").closest("td").find(".available-qty-container").find(".is_msl").removeClass('hide').text("(MSL: "+msl+")").attr("data-value", +msl);
                    var qty_label = document.getElementById('id_invoice_mat-' + index + '-quantity');
                    var entry_order = document.getElementById('id_invoice_mat-' + index + '-entry_order');
                    var new_form_item_name = document.getElementById('id_invoice_mat-' + index + '-item_name');
                    var new_form_item_id = document.getElementById('id_invoice_mat-' + index + '-item_id');
                    var new_form_item_hsn_code = document.getElementById('id_invoice_mat-' + index + '-hsn_code');
                    var new_form_quantity = document.getElementById('id_invoice_mat-' + index + '-quantity');
                    var new_form_unit_id = document.getElementById('id_invoice_mat-' + index + '-unit_id');
                    var new_form_rate = document.getElementById('id_invoice_mat-' + index + '-rate');
                    var new_form_discount = document.getElementById('id_invoice_mat-' + index + '-discount');
                    var new_form_amount = document.getElementById('id_invoice_mat-' + index + '-amount');
                    var new_form_item_code = document.getElementById('id_invoice_mat-' + index + '-item_code');
                    var new_form_item_enterprise_id = document.getElementById('id_invoice_mat-' + index + '-enterprise_id');
                    var new_form_oa_no = document.getElementById('id_invoice_mat-' + index + '-oa_no');
                    var new_form_oa_code = document.getElementById('id_invoice_mat-' + index + '-oa_code');
                    var new_form_grn_no = document.getElementById('id_invoice_mat-' + index + '-grn_no');
                    var new_form_grn_code = document.getElementById('id_invoice_mat-' + index + '-grn_code');
                    var new_form_alternate_unit_id = document.getElementById('id_invoice_mat-' + index + '-alternate_unit_id');
                    var new_form_scale_factor = document.getElementById('id_invoice_mat-' + index + '-scale_factor');
                    var new_form_material_type = document.getElementById('id_invoice_mat-' + index + '-material_type');
                    if($("#id_invoice-goods_already_supplied").is(":checked")){
                        var new_form_dc_no = document.getElementById('id_invoice_mat-' + index + '-dc_no');
                        var new_form_dc_code = document.getElementById('id_invoice_mat-' + index + '-dc_code');
                    }
                    var new_form_remarks = document.getElementById('id_invoice_mat-' + index + '-remarks');
                    var new_form_make_id = document.getElementById('id_invoice_mat-' + index + '-make_id');
                    var new_form_is_faulty = document.getElementById('id_invoice_mat-' + index + '-is_faulty');
                    var new_form_is_service = document.getElementById('id_invoice_mat-' + index + '-is_service');
                    //new_form_item_name.value = document.getElementById('id_invoice_mat-__prefix__-item_name').value;
                    if(is_service == 1){
                        $(new_form_item_name).closest(".item_description").html(document.getElementById('id_invoice_mat-__prefix__-item_name').value + '<span class="service-item-flag"></span>')
                    }
                    else{
                          $(new_form_item_name).closest(".item_description").html(document.getElementById('id_invoice_mat-__prefix__-item_name').value)
                    }
                    new_form_item_id.value = document.getElementById('id_invoice_mat-__prefix__-item_id').value;
                    new_form_item_hsn_code.value = document.getElementById('id_invoice_mat-__prefix__-hsn_code').value;
                    new_form_quantity.value = document.getElementById('id_invoice_mat-__prefix__-quantity').value;
                    new_form_unit_id.value = document.getElementById('id_invoice_mat-__prefix__-unit_id').value;
                    new_form_rate.value = document.getElementById('id_invoice_mat-__prefix__-rate').value;
                    new_form_discount.value = document.getElementById('id_invoice_mat-__prefix__-discount').value;
                    new_form_amount.value = document.getElementById('id_invoice_mat-__prefix__-amount').value;
                    new_form_item_code.value = document.getElementById('id_invoice_mat-__prefix__-item_code').value;
                    new_form_item_enterprise_id.value = document.getElementById('id_invoice_mat-__prefix__-enterprise_id').value;
                    new_form_oa_no.value = document.getElementById('id_invoice_mat-__prefix__-oa_no').value;
                    new_form_grn_no.value = document.getElementById('id_invoice_mat-__prefix__-grn_no').value;
                    new_form_alternate_unit_id.value = document.getElementById('id_invoice_mat-__prefix__-alternate_unit_id').value;
                    new_form_scale_factor.value = document.getElementById('id_invoice_mat-__prefix__-scale_factor').value;
                    new_form_material_type.value = document.getElementById('id_invoice_mat-__prefix__-material_type').value;
                    if($("#id_invoice-goods_already_supplied").is(":checked")){
                        new_form_dc_no.value = document.getElementById('id_invoice_mat-__prefix__-dc_no').value;
                        new_form_dc_code.value = document.getElementById('id_invoice_mat-__prefix__-dc_code').value;
                        $('#id_invoice_mat-' + index + '-quantity').closest("tr").find(".dc_code_container").html("<b>DC No:</b> "+document.getElementById('id_invoice_mat-__prefix__-dc_code').value);
                    }
                    new_form_remarks.value = document.getElementById('id_invoice_mat-__prefix__-remarks').value;
                    new_form_make_id.value = document.getElementById('id_invoice_mat-__prefix__-make_id').value;
                    new_form_is_faulty.value = document.getElementById('id_invoice_mat-__prefix__-is_faulty').value;
                    new_form_is_service.value = document.getElementById('id_invoice_mat-__prefix__-is_service').value;

                    if(new_form_remarks.value != "") {
                        $("#id_invoice_mat-"+index+"-remarks").closest("td").find(".item_remarks_section").removeClass("hide");
                        $("#id_invoice_mat-"+index+"-remarks").closest("td").find(".add_item_remarks").addClass("hide");
                    }

                    var item_name = $(table.rows[iCount].cells[3]).text();
                    qty_label.value = parseFloat(input_obj[i - 1].value);
                    $(qty_label).attr("data-default-value", parseFloat(input_obj[i - 1].value));
                    $(new_form_alternate_unit_id).attr("data-default-value", new_form_alternate_unit_id.value);
                    $(new_form_rate).attr("data-default-value", new_form_rate.value);
                    $(new_form_discount).attr("data-default-value", new_form_discount.value);
                    entry_order.value = material_index;
                    assignSerialNumber();
//                    showAllClosingStocks(0);
                    isFormChanged = true;
                }
                if (oa_no != 0){
                    oa_nos = oa_nos + "," + oa_no;
                }
            }
            i++;
        }
    }
    if (oa_nos != ""){
        getOATaxList(oa_nos);
    }
    $("#OAMaterial").modal('hide');
    $('#materialrequired, #id_invoice_mat-__prefix__-item_id, #id_invoice_mat-__prefix__-hsn_code, #id_invoice_mat-__prefix__-remarks, #id_invoice_mat-__prefix__-hsn_code').val('');
    $('#id_invoice_mat-__prefix__-quantity,#id_invoice_mat-__prefix__-rate,#id_invoice_mat-__prefix__-discount,#id_invoice_mat-__prefix__-amount').val('0.00');
    $("#unit_display").html('&nbsp;');
    $("#individual_item_description").addClass("hide");
    $("#description_display").html('');
    $('#material_id_hidden').val('');
    $('#material_id').val('');
    setTimeout(function(){
        $('#materialrequired').focus();
        calculateGrandTotal();
        autosize($(".auto-expandable"));
        listTableHoverIconsInit('invoice_materials_table');
        if($("#id_invoice-goods_already_supplied").is(":checked")) {
            constructConsolidatedRow();
        }
    },250);
}

function AddOATax(selected_item) {
    enterprise_id = $("#id_invoice-enterprise_id").val();
    /* Fetching Assessment Value */
    generateFormsetFormRowAtIndex('invoice_tax', '#packing_diff_line');
    copyTaxEmptyForm('invoice_tax',parseInt($('#id_invoice_tax-TOTAL_FORMS').val()) - 1);
    invoiceTaxListAdding(selected_item);
    isFormChanged = true;
    calculateGrandTotal();
}


function getOATaxList(oa_ids){
    var oa_taxes = null;
    if($('#oa_taxes').val() != ''){
        oa_taxes = JSON.parse($('#oa_taxes').val());
    }
    var lookup = {};
    oa_array = oa_ids.split(",");
    if (oa_taxes != null){
        for (j = 0; j <= oa_taxes.length - 1; j++) {
            for (i = 0; i <= oa_array.length - 1; i++){
                if (oa_taxes[j].oa_id == oa_array[i]){
                    var tax_code = oa_taxes[j].oa_tax_code;
                    if (!(tax_code in lookup)) {
                        lookup[tax_code] = 1;
                        $("#id_invoice_tax-__prefix__-tax_code").val(oa_taxes[j].oa_tax_code);
                        $("#id_invoice_tax-__prefix__-invoice_id").val($("#id_invoice-id").val());
                        $("#id_invoice_tax-__prefix__-enterprise_id").val($("#id_invoice-enterprise_id").val());
                        $("#invoice_tax_list option[value='"+oa_taxes[j].oa_tax_code+"']").hide();
                        AddOATax(oa_taxes[j].oa_tax_code);
                    }
                }
            }
        }
    }

}

function resetConsolidationItems() {
    if($("#id_dc_type").val() != "internal") {
        $(".th-oa-no, .td-item-code").removeClass("hide");
        $(".td-unit-total, .td-unit-rate, .td-unit-discount, .td-tax-amount, .td-tax-rate").removeClass("hide");
        $(".invoice_item_row").removeClass("hide");
        $(".s_no").css({color: "#000"})
        $(".packing_diff_line").attr("colspan", "16");
        $(".total_span_column").attr("colspan", "7");
        $(".td-packing-forwaring").attr("colspan", "3");
    }
}

function constructConsolidatedRow(){
    $("#loading").show();
    $(".th-oa-no, .td-item-code").addClass("hide");
    $(".td-unit-total, .td-unit-rate, .td-unit-discount, .td-tax-amount, .td-tax-rate").addClass("hide");
    $(".invoice_item_row").addClass("hide");
    $(".s_no").css({color: "transparent"})
    $(".packing_diff_line").attr("colspan", "15");
    $(".total_span_column").attr("colspan", "6");
    $(".td-packing-forwaring").attr("colspan", "2");
    $("#invoice_materials_table tr[consolidated-for]").remove();
    var counter = 1;
    setTimeout(function(){
        $("#invoice_materials_table tbody").find(".invoice_item_row").not(".deleted_invoice").each(function(index){
            var itemId = $(this).find(".item_id").val();
            var itemHsnCode = $(this).find(".td-hsn-code").find("input").val();
            var itemQty = $(this).find(".td_inv_qty").find("input").val();
            var itemUnitRate = $(this).find(".td-unit-rate").find("input").val();
            var itemUnitDiscount = $(this).find(".td-unit-discount").find("input").val();
            var currentScaleFactor = $(this).find("input[name*='scale_factor']").val();
            var currentAlternaterUnitID = $(this).find("input[name*='alternate_unit_id']").val();
            $(this).find("input[name*='alternate_unit_id']").attr("data-default-value", currentAlternaterUnitID);

            $(this).find(".td-unit-rate").find("input").attr("data-default-value", Number(itemUnitRate).toFixed(3))
            $(this).find(".td_inv_qty").find("input").attr("data-default-value", (itemQty*currentScaleFactor).toFixed(3))
            $(this).find(".td_inv_qty").find("currently_available_pending").attr("data-default-value", ($(this).find(".td_inv_qty").find("currently_available_pending ").text()*currentScaleFactor).toFixed(3))
            $(this).find(".td-unit-discount ").find("input").attr("data-default-value", Number(itemUnitDiscount).toFixed(2))

            var itemDescription = $(this).find(".item_description").text();
            var itemUnit = $(this).find(".td-unit-id").find("input").val();

            var makeId = $(this).find(".make_id").val();
            var isFaulty = $(this).find(".is_faulty").val();
            var isService = $(this).find(".is_service").val();
            if(isService == 1){
                itemDescription += `<span class="service-item-flag"></span>`;
            }
            if(isFaulty.toLowerCase() == "true" || isFaulty.toLowerCase() == "false") {
                isFaulty = isFaulty == "False" ? 0:1;
            }
            var consolidated_hsn_sac =  generateUniqueCode(itemHsnCode.trim());
            var consolidatedTxt = `${itemId}__${makeId}__${isFaulty}__${consolidated_hsn_sac}`;

            if($("#OAMaterial").find('tr[consolidated-for="'+consolidatedTxt+'"]').find("select[name='alternate_units']").length > 0) {
                itemUnit = $("#OAMaterial").find('tr[consolidated-for="'+consolidatedTxt+'"]').find("select[name='alternate_units']").html();
                itemUnit = `<select class="form-control" name="alternate_units" onchange="updateConsolidatedUnit(this)">${itemUnit}</select>`
            }
            else if($(this).find("input[name*='alternate_unit_list']").val() != "" && typeof $(this).find("input[name*='alternate_unit_list']").val() != "undefined") {
                if($(this).find("input[name*='alternate_unit_list']").val().length > 2) {
                    var jsonObj = JSON.parse($(this).find("input[name*='alternate_unit_list']").val());
                    var option = "";
                    for(i=0;i<jsonObj.length;i++) {
                        option = `${option}<option value="${jsonObj[i].alternate_unit_id}" data-val="${jsonObj[i].scale_factor}">${jsonObj[i].unit_name}</option>`;
                    }
                    itemUnit = `<select class="form-control" name="alternate_units" onchange="updateConsolidatedUnit(this)">${option}</select>`;
                }
            }

            var gstTaxValue = [];
            var gstTaxes = [];
            $(this).find(".td-tax-rate").each(function(){
                gstTaxValue.push($(this).find("select").val());
                gstTaxes.push($(this).find("select").html())
            });
            var itemCgstTax = gstTaxValue[0];
            var itemSgstTax = gstTaxValue[1];
            var itemIgstTax = gstTaxValue[2];
            if($("#invoice_materials_table tr[consolidated-for='"+consolidatedTxt+"']").length <= 0 ) {
                var consolidatedRow = ` <tr consolidated-for='${consolidatedTxt}' class='consolidated_row'>
                                            <td><span class='text-center'>${counter++}</span>
                                             <a role="button" class="table-inline-icon" onclick="javascript:deleteConsolidatedInvoiceMaterial('${consolidatedTxt}')">
                                                    <i style="padding-top: 7px;" class="fa fa-trash-o" title="Delete" alt="Delete"></i>
                                                </a>
                                            </td>
                                            <td onclick="consolidatedRowToggle(this)" class="consolidated-link" style="min-width: 200px;">
                                                <i class="fa consolidated_fa fa-chevron-right" style="float: left; width: 12px;" aria-hidden="true"></i>
                                                <span class="consolidated_material_name">${itemDescription}</span>
                                            </td>
                                            <td>${itemHsnCode}</td>
                                            <td class='text-right'><span class='total_qty'></span> ${itemUnit}</td>
                                            <td class='consolidated_price_column'>
                                                <input type="text" class="form-control txt_consolidated_price text-right" value="${itemUnitRate}" maxlength="16" onfocus="setNumberRangeOnFocus(this,10,5)" onblur="updateInvoiceConsolidatedPrice(this);">
                                            </td>
                                            <td class='consolidated_price_column'>
                                                <input type="text" class="form-control txt_consolidated_disc text-right" value="${itemUnitDiscount}" maxlength="6" onfocus="setNumberRangeOnFocus(this,3,2)" onblur="validatePercentage(this, event); updateInvoiceConsolidatedPrice(this);">
                                            </td>
                                            <td class='consolidated_price_column text-right'>
                                                <span class='txt_consolidated_total'></span>
                                            </td>
                                            <td class='consolidated_price_column'>
                                                <select class="form-control sel_consolidated_cgst" onchange="updateInvoiceConsolidatedPrice(this)">
                                                    ${gstTaxes[0]}
                                                </select>
                                                <span class="td-sub-content bracket-enclosed txt_consolidated_cgst text-right">0.00</span>
                                            </td>

                                            <td class='consolidated_price_column'>
                                                <select class="form-control sel_consolidated_sgst" onchange="updateInvoiceConsolidatedPrice(this)">
                                                    ${gstTaxes[1]}
                                                </select>
                                                <span class="td-sub-content bracket-enclosed txt_consolidated_sgst text-right">0.00</span>
                                            </td>

                                            <td class='consolidated_price_column'>
                                                <select class="form-control sel_consolidated_igst" onchange="updateInvoiceConsolidatedPrice(this)">
                                                    ${gstTaxes[2]}
                                                </select>
                                                <span class="td-sub-content bracket-enclosed txt_consolidated_igst text-right">0.00</span>
                                            </td>

                                        </tr>`;
                $(consolidatedRow).insertBefore($("#invoice_materials_table tr."+consolidatedTxt).not(".deleted_invoice").first());
            }
            var rowSpanCount = $("#invoice_materials_table ."+consolidatedTxt).not(".deleted_invoice").length;
            var updateRow = $("#invoice_materials_table tr[consolidated-for='"+consolidatedTxt+"'").find(".consolidated_price_column").attr("data-rowspan", Number(rowSpanCount+1));
            updateConsolidatedHeaderUnit($(this).find('.td_inv_qty').find("input"));
        });
        consolidationOnchangeEvent();
        $(".td_inv_qty input").blur();
        $(".txt_consolidated_price").blur();
        $("#loading").hide();
    },500);
}

function deleteConsolidatedInvoiceMaterial(element) {
    swal({
        title: "Are you sure?",
        text: "Do you want to delete all the consolidated Item!",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#209be1",
        confirmButtonText: "Yes, delete it!",
        closeOnConfirm: true
    },
    function(){
       deleteInvoiceMaterials(element)
    });
}

function deleteInvoiceMaterials(element) {
    var currentElement = "";
    if(element != null){
        $("#invoice_materials_table").find("."+element).each(function(){
            currentElement = $(this).attr("id");
            var deleteFlag = document.getElementById('id_' + currentElement + '-DELETE');
            var deleteRow = document.getElementById(currentElement);
            deleteFlag.checked = true;
            deleteRow.style.display = 'none';
            deleteRow.classList.add("deleted_invoice");
        });
    }else{
        $("#invoice_materials_table .invoice_item_row").each(function(){
            currentElement = $(this).attr("id");
            var deleteFlag = document.getElementById('id_' + currentElement + '-DELETE');
            var deleteRow = document.getElementById(currentElement);
            deleteFlag.checked = true;
            deleteRow.style.display = 'none';
            deleteRow.classList.add("deleted_invoice");
        });
    }
    if($("#id_invoice-goods_already_supplied").is(":checked")) {
        //updateInvoiceTableColspan();
        $("#id_"+currentElement+"-quantity").blur();
    }
    $("tr[consolidated-for='"+element+"']").remove();
    calculateGrandTotal();
    assignSerialNumber();
    isFormChanged = true;
}

function consolidationOnchangeEvent() {
    $(".td_inv_qty input").blur(function(){
        var currentClass = $(this).closest('tr').attr('data-row');
        var curQty = 0;
        if(currentClass != undefined){
            currentClass = currentClass.trim()
        }
        $("#invoice_materials_table ."+currentClass).each(function(){
            if(!$(this).hasClass("deleted_invoice")) {
                curQty += Number($(this).find(".td_inv_qty").find("input").val());
            }
        });
        $("#invoice_materials_table").find("tr[consolidated-for='"+currentClass+"'] .total_qty").text(curQty.toFixed(3));
        calculateInvoiceConsolidatedPrice($("tr[consolidated-for='"+currentClass+"']").find(".txt_consolidated_price"));
    })
}

function updateInvoiceConsolidatedPrice(curr) {
    var currentClass = $(curr).closest('tr').attr('consolidated-for');
    var closestTR = $(curr).closest('tr');
    setTimeout(function(){
        $("#invoice_materials_table ."+currentClass).each(function(){
            $(this).find('.td-unit-rate').find("input").val(closestTR.find('.txt_consolidated_price').val());
            $(this).find('.td-unit-discount').find("input").val(closestTR.find('.txt_consolidated_disc').val());
            $(this).find('.sel_indv_cgst').val(closestTR.find('.sel_consolidated_cgst').val());
            $(this).find('.sel_indv_sgst').val(closestTR.find('.sel_consolidated_sgst').val());
            $(this).find('.sel_indv_igst').val(closestTR.find('.sel_consolidated_igst').val());
        });
        calculateInvoiceConsolidatedPrice(curr);
        isFormChanged = true;
    }, 10);
}

function calculateInvoiceConsolidatedPrice(curr){
    var closestTR = $("#invoice_materials_table").find(curr).closest('tr');
    var currentClass = $("#invoice_materials_table").find(curr).closest('tr').attr("consolidated-for");
    var currDCQty = closestTR.find('.total_qty').text();
    var currPrice = closestTR.find('.txt_consolidated_price').val();
    var currDisc = closestTR.find('.txt_consolidated_disc').val()/100;
    var calcDisc = Number((currDCQty * currPrice) * currDisc);
    var currTotal = Number((currDCQty * currPrice) - calcDisc);
    closestTR.find('.txt_consolidated_total').text(Number(currTotal).toFixed(2));

    var currCgst = Number(closestTR.find('.sel_consolidated_cgst option:selected').text())/100;
    var currSgst = Number(closestTR.find('.sel_consolidated_sgst option:selected').text())/100;
    var currIgst = Number(closestTR.find('.sel_consolidated_igst option:selected').text())/100;
    var currCgstRate = Number(closestTR.find('.sel_consolidated_cgst option:selected').text());
    var currSgstRate = Number(closestTR.find('.sel_consolidated_sgst option:selected').text());
    var currIgstRate = Number(closestTR.find('.sel_consolidated_igst option:selected').text());
    if(!isNaN(currCgstRate) && currCgstRate!=""){
        closestTR.find('.txt_consolidated_cgst').text(Number(currTotal * currCgst).toFixed(2));
    } else {
        closestTR.find('.txt_consolidated_cgst').text('0.00');
    }
    if(!isNaN(currSgstRate) && currSgstRate!=""){
        closestTR.find('.txt_consolidated_sgst').text(Number(currTotal * currSgst).toFixed(2));
    } else {
        closestTR.find('.txt_consolidated_sgst').text('0.00');
    }
    if(!isNaN(currIgstRate) && currIgstRate!=""){
        closestTR.find('.txt_consolidated_igst').text(Number(currTotal * currIgst).toFixed(2));
    } else {
        closestTR.find('.txt_consolidated_igst').text('0.00')
    }
    calculateGrandTotal();
    listTableHoverIconsInit('invoice_materials_table');
}

function calculateGrandTotal(current = "") {
    var i;
    var round_off =0;
    var simple_rates = document.getElementsByName('net_rate');
    var compound_rates = document.getElementsByName('net_rate_compound');
    var tax_wise_subtotal = document.getElementsByName('tax');
    var compound_tax_wise_subtotal = document.getElementsByName('tax_compound');
    var table = document.getElementById('invoice_materials_table');
    var simple_assess_rates = document.getElementsByName('asses_rate');
    var compound_assess_rates = document.getElementsByName('asses_rate_compound');
    var item_taxes = document.getElementsByName('item_tax');
    if(!isNaN(parseFloat($("#id_invoice-round_off").val()))){
		round_off = parseFloat(document.getElementById("id_invoice-round_off").value);
	}else{
		round_off =0;
	}

    //Calculate The Total with out Tax
	var item_count = parseFloat($("#id_invoice_mat-TOTAL_FORMS").val());
    var charges_count = parseFloat($("#id_invoice_charge-TOTAL_FORMS").val());
	var i, total= 0,discount_val =0,assess_total=0,net_value_for_tax=0;
    //	Calculate The Net Value
	for (i=0; i<simple_rates.length; i++){tax_wise_subtotal[i].value=0;}
    for (i=0; i<compound_rates.length; i++){compound_tax_wise_subtotal[i].value=0;}
    var net_tax = 0;
    var gst_amt = 0;

	for(i=0;i<item_count;i++){
        // This if condition is used to avoid deleted item in calculation
		if(!document.getElementById("id_invoice_mat-"+i+"-DELETE").checked){
		    $("#id_invoice_mat-"+i+"-amount").val(calculateAssessValue($("#id_invoice_mat-"+i+"-quantity").val(), $("#id_invoice_mat-"+i+"-rate").val(), $("#id_invoice_mat-"+i+"-discount").val(), 0));
            total+= parseFloat($("#id_invoice_mat-"+i+"-amount").val());
            calculateGSTAmount("inv_mat_tax_"+i+"-0","invoice_mat-"+i);
            calculateGSTAmount("inv_mat_tax_"+i+"-1","invoice_mat-"+i);
            calculateGSTAmount("inv_mat_tax_"+i+"-2","invoice_mat-"+i);
            item_taxes[i].value = 0;
            if(!isNaN(parseFloat($("#id_invoice_mat-"+i+"-quantity").val())) && parseFloat($("#id_invoice_mat-"+i+"-quantity").val()) > 0){
                var item_simple_tax = calculateItemTax($("#id_invoice_mat-"+i+"-quantity").val(), $("#id_invoice_mat-"+i+"-rate").val(), $("#id_invoice_mat-"+i+"-discount").val(), simple_rates, simple_assess_rates, tax_wise_subtotal, false, 0);
                gst_amt =  parseFloat($("#id_span_inv_mat_tax_"+i+"-0-amount").text())  + parseFloat($("#id_span_inv_mat_tax_"+i+"-1-amount").text()) +parseFloat($("#id_span_inv_mat_tax_"+i+"-2-amount").text());
                item_simple_tax = item_simple_tax + gst_amt;
                item_taxes[i].value = parseFloat(item_taxes[i].value) + parseFloat(item_simple_tax) +
                        parseFloat(calculateItemTax($("#id_invoice_mat-"+i+"-quantity").val(), $("#id_invoice_mat-"+i+"-rate").val(), $("#id_invoice_mat-"+i+"-discount").val(), compound_rates, compound_assess_rates, compound_tax_wise_subtotal, true, parseFloat(item_simple_tax)));
                net_tax = parseFloat(net_tax) + parseFloat(item_taxes[i].value);
            }
		}else{
		    $("#id_span_inv_mat_tax_"+i+"-0-amount").text(0);
		    $("#id_span_inv_mat_tax_"+i+"-1-amount").text(0);
		    $("#id_span_inv_mat_tax_"+i+"-2-amount").text(0);
		}
	}


	for(i=0;i<charges_count;i++){
        // This if condition is used to avoid deleted item in calculation
		if(!document.getElementById("id_invoice_charge-"+i+"-DELETE").checked){
		    $("#id_invoice_charge-"+i+"-amount").val(calculateAssessValue(1, $("#id_invoice_charge-"+i+"-rate").val(), $("#id_invoice_charge-"+i+"-discount").val(), 0));
            total+= parseFloat($("#id_invoice_charge-"+i+"-amount").val());
            calculateGSTAmount("invoice_charge_tax_"+i+"-0","invoice_charge-"+i);
            calculateGSTAmount("invoice_charge_tax_"+i+"-1","invoice_charge-"+i);
            calculateGSTAmount("invoice_charge_tax_"+i+"-2","invoice_charge-"+i);

            var item_simple_tax = calculateItemTax(1, $("#id_invoice_charge-"+i+"-rate").val(), $("#id_invoice_charge-"+i+"-discount").val(), simple_rates, simple_assess_rates, tax_wise_subtotal, false, 0);
            gst_amt =  parseFloat($("#id_span_invoice_charge_tax_"+i+"-0-amount").text()) + parseFloat($("#id_span_invoice_charge_tax_"+i+"-1-amount").text()) +parseFloat($("#id_span_invoice_charge_tax_"+i+"-2-amount").text());
            item_simple_tax = item_simple_tax + gst_amt;
            var currentTax = parseFloat(item_simple_tax) +
                    parseFloat(calculateItemTax(1, $("#id_invoice_charge-"+i+"-rate").val(), $("#id_invoice_charge-"+i+"-discount").val(), compound_rates, compound_assess_rates, compound_tax_wise_subtotal, true, parseFloat(item_simple_tax)));
            net_tax = parseFloat(net_tax) + parseFloat(currentTax);
		}else{
		    $("#id_span_invoice_charge_tax_"+i+"-0-amount").text(0);
		    $("#id_span_invoice_charge_tax_"+i+"-1-amount").text(0);
		    $("#id_span_invoice_charge_tax_"+i+"-2-amount").text(0);
		}
	}
	var currentElementId = "";
	if(current != "") {
	    currentElementId = current.id;
	}

	if($("#id_auto-calculate").is(":checked") && currentElementId != "id_invoice-round_off"){
	    round_off = 0;
	}

    if ($("#id_invoice-type").val() != "BoS"){
       calculateGSTTotals();
       var grand_total = parseFloat(total) + parseFloat(net_tax) + parseFloat(round_off);
	}
	else{
	    var grand_total = parseFloat(total) + parseFloat(round_off);
	}
    total = total.toFixed(2);
	$("#invoice_tot").val(total).attr("value", total);

	if($("#id_auto-calculate").is(":checked") && currentElementId != "id_invoice-round_off"){
	    var roundoff_difference = (Math.round(grand_total) - grand_total).toFixed(2);
	    $("#id_invoice-round_off").val(roundoff_difference);
        grand_total = Number(grand_total) + Number(roundoff_difference);
    }

    $("#id_invoice-grand_total").val(grand_total.toFixed(2));
    $("#grand_total_value").val(grand_total.toFixed(2)).attr("value", grand_total.toFixed(2));
}

function calculateAssessValue(quantity, price, discount, assess_rate){
    var value = (parseFloat(quantity) * parseFloat(price)).toFixed(2);
    if (( 100-parseFloat(discount)) > parseFloat(assess_rate)){
        return (value * (100- parseFloat(discount))/100).toFixed(2);
    }else{
        return (value * parseFloat(assess_rate)/100).toFixed(2);
    }
}

function calculateItemTax(quantity, unit_rate, discount, tax_rates, assess_rates, tax_subtotal, is_compound, cascading_item_tax){
    var item_tax = 0;
    var tax_count = tax_rates.length;
    // Calculating the net Taxes
    for (i=0; i<tax_count; i++){
        var item_assess_value = calculateAssessValue(quantity, unit_rate, discount, assess_rates[i].value);
        if (is_compound){
            var sub_total = Math.round((parseFloat(item_assess_value) + parseFloat(cascading_item_tax)) * parseFloat(tax_rates[i].value))/100;
            cascading_item_tax = parseFloat(cascading_item_tax) + parseFloat(sub_total);
        } else {
            var sub_total = Math.round(parseFloat(item_assess_value) * parseFloat(tax_rates[i].value))/100;
        }
        tax_subtotal[i].value = (parseFloat(tax_subtotal[i].value) + parseFloat(sub_total)).toFixed(2);
        item_tax = parseFloat(item_tax) + parseFloat(sub_total);
    }
    return item_tax;
}

function CheckNo(sender){
    if(!isNaN(sender.value)){
        if(sender.value > 100 )
            sender.value = 100;
        if(sender.value < 0 )
            sender.value = 0;
    }else{
          sender.value = 0;
    }
    calculateGrandTotal();
}

function BOMValidationCheck() {
    var errorCount = true;
    $(".mandatory_field").each(function(){
    	if($(this).css('opacity') >=1 ) {
	        if($(this).val().trim() == "") {
	            if($(this).closest("tr").find(`input[name="catmaterial_qty"]`).val() > 0) {
	                $(this).addClass('error-border');
	                errorCount = false;
	            }
	        } else {
	            $(this).removeClass('error-border');
	        }
	    } else {
    	 	$(this).removeClass('error-border');
	    }
    });
    validationResult = initiateStockValidation();
    validationResult.isValid = errorCount
    return validationResult;
}

function removeOARows(matchRow){
    if(!$("#id_invoice-goods_already_supplied").is(":checked")) {
        var item_count = parseFloat($("#id_invoice_mat-TOTAL_FORMS").val());
        var tag_count = parseFloat($("#id_tag-TOTAL_FORMS").val());

        var selected_oa_nos= "";
        var new_oa_no_selected= "";
        var multi_select_oa =  $("select#id_invoice-order_accept_no_display").next('.btn-group').find('ul').find('li input:checked');
        multi_select_oa.each(function () {
            selected_oa_nos += $(this).val() + ",";
        });

        var oa_ids = selected_oa_nos;
        oa_id = oa_ids.split(",")

        for(i=0;i<item_count;i++){
            if ($("#id_invoice_mat-"+i+"-"+matchRow).val()!=0 ){
                var match = false
                for(var line = 0; line < oa_id.length-1; line++){
                    if ($("#id_invoice_mat-"+i+"-"+matchRow).val()==oa_id[line]){
                        match = true
                    }
                }
                if (match==false){
                    var deleteFlag = document.getElementById("id_invoice_mat-"+i+"-DELETE");
                    var deleteRow = document.getElementById("invoice_mat-"+i);
                    deleteFlag.checked = true;
                    deleteRow.style.display = 'none';
                }
            }
        }
        calculateGrandTotal();
    }
}

function loadPartyRate(){
    var item_id=trim($('#material_id').val()).replace( /(^.*\[|\].*$)/g, '' );
    var party_id =$("#id_invoice-party_id option:selected").val();
    var make_id = $('#id_invoice_mat-__prefix__-make_id').val();
    $("#material_rate_hidden").val("0.00000");
    if ($('#material_id').val()!="") {
        $.ajax({
            url: "/erp/sales/json/invoice/loadPartyRate/",
            type: "post",
            datatype: "json",
            data: {party_id:party_id,item_id:item_id,make_id:make_id},
            success: function(response) {
                $("#id_invoice_mat-__prefix__-rate").val(response.item_rate.price);
                $("#material_rate_hidden").val(response.item_rate.price);
                $("#id_invoice_mat-__prefix__-hsn_code").val(response.hsn_code)
            }
        });
    }
}

function initiateStockValidation() {
    validationResult = {isValid:true, exceeds_pending:0, exceeds_stock:0, exceeds_msl:0, exceeds_zero:0}
    $(".bom_po_qty").each(function(){
        var stockCount = Number($(this).closest("tr").find(".stock_qty").text());
        var msl = Number($(this).closest("tr").find(".minimum_stock_level").text());
        var stock_to_be_issued = Number($(this).val());
        var stock = stockCount - stock_to_be_issued;

        if(stock_to_be_issued > 0) {
            if($(this).closest("tr").find(".stock_qty").hasClass('stockable')) {
                if($(this).css('opacity') >=1) {
                    if(Math.max(0, msl) > stock && stock >= Math.min(0, msl)) {
                        if(stock < 0){
                            validationResult.exceeds_zero++;
                        }
                        else{
                            validationResult.exceeds_msl++;
                        }
                        $(this).removeClass('error-border-stock');
                        $(this).addClass('warning-border');
                    } else if(Math.min(0, msl) > stock) {
                        validationResult.exceeds_stock++;
                        $(this).addClass('error-border-stock');
                    }
                } else {
                    $(this).removeClass('error-border-stock');
                    $(this).removeClass('warning-border');
                }
            }
            if($('#id_dc_type').val() != "internal") {
                var pendingCount = Number($(this).closest("tr").find(".pending_qty").text());
                if($(this).css('opacity') >=1 ) {
                   if($('#id_invoice-type option:selected').val() != "JDC" && stock_to_be_issued > pendingCount) {
                       validationResult.exceeds_pending++;
                       $(this).addClass('error-border-stock');
                   }
                } else {
                    $(this).removeClass('error-border-stock');
                    $(this).removeClass('warning-border');
                }
            }
        }
        else {
            $(this).removeClass('error-border-stock');
            $(this).removeClass('warning-border');
        }
    });
    return validationResult;
}

function getMaterialIndexCount(){
    var iValue =[];
    $(".s_no_hidden").each(function() {
        var value = $(this).find("input").val();
        iValue.push(value)
    });
    return Number(Math.max(...iValue) + 1)
}

function goodsSuppliedChange() {
    var party_id = $("#id_invoice-party_id").val();
    var invoice_id = $("#search_invoice_id").val()
    $("#id_invoice-type").attr('readonly','readonly');
    $("#id_invoice-type").attr('title');
    $('#id_invoice_party_id_chosen').addClass('div-disabled');
    if($("#id_invoice-goods_already_supplied").is(":checked")){
        $("#div_dc_no_display").removeClass('hide');
        $("#div_oa_no_display").addClass('hide');
        $('.oa_dc_no_heading').text('Items to be invoiced');
        $(".td_oa_dc").addClass("hide");
        $('.invoice_item_table').addClass('hide');
        var multi_select_oa =  $("select#id_invoice-order_accept_no_display").next('.btn-group').find('ul').find('li input:checked');
        if(multi_select_oa.length > 0){
            deleteInvoiceMaterials(null);
        }
        loadPartyFinancialYear(party_id, invoice_id);
    }
    else {
        $("#div_dc_no_display").addClass('hide');
        $("#div_oa_no_display").removeClass('hide');
        $('.oa_dc_no_heading').text('View OA Material');
        $(".td_oa_dc").removeClass("hide");
        if ($('#id_invoice-type option:selected').val() == "JIN"){
            $('.invoice_item_table').addClass('hide');
        }else{
            $('.invoice_item_table').removeClass('hide');
        }
        var multi_select_dc =  $("select#id_invoice-dc_no_display").next('.btn-group').find('ul').find('li input:checked');
        if(multi_select_dc.length > 0){
            $("#invoice_materials_table").find(".consolidated_row").each(function(){
                var element = $(this).closest('tr').attr('consolidated-for');
                deleteInvoiceMaterials(element);
            });
        }
        resetConsolidationItems();
        loadPartyOA(party_id, invoice_id)
    }
}

function loadPartyOA(party_id,invoice_id){
    $("#id_invoice-order_accept_no_display").next(".btn-group").find(".multiselect").addClass("disabled").attr("title", "Loading... Please wait");
    if ($('#id_invoice-type option:selected').val()=="JIN"){
        url = "/erp/stores/json/pending_grn_oa/";
    }else{
        url = "/erp/stores/json/getpartyoa/";
    }
    $.ajax({
        url: url,
        type: "post",
        datatype: "json",
        data: {party_id: party_id,invoice_id:invoice_id},
        success: function (data) {
            $("#loading").hide();
            $("#id_invoice-type").removeAttr('readonly');
            $("#id_invoice-type").removeAttr('title');
            $('#id_invoice_party_id_chosen').removeClass('div-disabled');
            $('#id_invoice-order_accept_no_display').multiselect('destroy');
            $('select[name=invoice-order_accept_no_display]').html('');
            $.each(data.oa_list, function(index, oa){
                $('select[name=invoice-order_accept_no_display]').append(
                    $(`<option value="${oa.id}" data-invoice_against="${oa.invoice_against}">${oa.code}</option>`)
                );
            });
            if($("#search_invoice_id").val()!="None"){
                if ($('#id_invoice-type option:selected').val()=="JIN"){
                    url = "/erp/stores/json/selected_grn_numbers/";
                }else{
                    url = "/erp/stores/json/selected_oa_numbers/";
                }
                $.ajax({
                    url: url,
                    type: "post",
                    datatype:"json",
                    data: {invoice_id: invoice_id},
                    success: function(response){
                        try{
                            oa_codes = response;
                            if (oa_codes != null){
                                setTimeout(function(){
                                    for (j = 0; j < oa_codes.length; j++) {
                                        $("#id_invoice-order_accept_no_display").multiselect('select', oa_codes[j].oa_id, true);
                                    }
                                },500);
                            }
                        } catch (e) {
                            console.log(e)
                        }
                    }
                });
            }
            $('#id_invoice-order_accept_no_display').multiselect({
                onDropdownHide: function(event){
                    load_oa_details();
                }
            });
            removeOAMaterials();
            $("#id_invoice-order_accept_no_display").next(".btn-group").find(".multiselect").removeClass("disabled").attr("title", "None selected");
        },
        error: function() {
            $("#id_invoice-order_accept_no_display").next(".btn-group").find(".multiselect").removeClass("disabled").attr("title", "None selected");
        }
    });
}

function loadPartyFinancialYear(partyId, invoiceId){
    $("#id_invoice_fin_year").next(".btn-group").find(".multiselect").addClass("disabled").attr("title", "Loading... Please wait");
    $.ajax({
        url: "/erp/sales/json/get_party_financial_years/",
        type: "post",
        datatype: "json",
        data: {party_id: partyId, invoice_id:invoiceId},
        success: function (response) {
            $("#id_invoice-type").removeAttr('readonly');
            $("#id_invoice-type").removeAttr('title');
            $('#id_invoice_party_id_chosen').removeClass('div-disabled');
            $('#id_invoice-dc_no_display').multiselect('destroy');
            $('#id_invoice_fin_year').multiselect('destroy');
            $('select[name=invoice-dc_no_display]').html('');
            $('select[name=invoice_fin_year]').html('');
            if (response.response_message == "Success") {
                $.each(response.financial_years, function(index, fy){
                    $('select[name=invoice_fin_year]').append($('<option></option>').val(fy).html(fy));
                });
                $('#id_invoice_fin_year').multiselect({
                    onDropdownHide: function(event){
                        onInvoiceFinYearChange();
                    }
                });
                $('#id_invoice-dc_no_display').multiselect();
                removeInvoiceMaterials();
                if (invoiceId != "") {
                    loadSelectedFinancialYear(invoiceId);
                }
            }
            $("#id_invoice_fin_year").next(".btn-group").find(".multiselect").removeClass("disabled").attr("title", "None selected");
        },
        error: function() {
            $("#id_invoice_fin_year").next(".btn-group").find(".multiselect").removeClass("disabled").attr("title", "None selected");
        }
    });
}

function loadSelectedFinancialYear(invoiceId) {
    $.ajax({
        url: "/erp/sales/json/load_selected_dc_numbers/",
        type: "post",
        datatype:"json",
        data: {invoice_id: invoiceId},
        success: function(response) {
            try{
                if (response.response_message == "Success") {
                    setTimeout(function(){
                        for (j = 0; j < response.financial_years.length; j++) {
                            $("#id_invoice_fin_year").multiselect('select', response.financial_years[j], true);
                        }
                        onInvoiceFinYearChange();
                    }, 100);
                }
            } catch (e) {
                console.log(e)
            }
        }
    });
}

function loadPartyDC(partyId, invoiceId, financialYears) {
    $("#id_invoice-dc_no_display").next(".btn-group").find(".multiselect").addClass("disabled").attr("title", "Loading... Please wait");
    $.ajax({
        url: "/erp/sales/json/getpartydc/",
        type: "post",
        datatype: "json",
        data: {party_id: partyId, invoice_id:invoiceId, financial_years:financialYears},
        success: function (response) {

            $('#id_invoice-dc_no_display').multiselect('destroy');
            $('select[name=invoice-dc_no_display]').html('');
            if (response.response_message == "Success") {
                $.each(response.dc_numbers, function(index, dc) {
                    $('select[name=invoice-dc_no_display]').append(
                        $('<option></option>').val(dc.id).html(dc.code)
                    );
                });
                $('#id_invoice-dc_no_display').multiselect({
                    onDropdownHide: function(event){
                        loadDcDetails();
                    }
                });
                removeInvoiceMaterials();
                if (invoiceId != "") {
                    loadSelectedDcNumbers(invoiceId);
                }
            }
            $("#id_invoice-dc_no_display").next(".btn-group").find(".multiselect").removeClass("disabled").attr("title", "None selected");
        },
        error: function() {
            $("#id_invoice-dc_no_display").next(".btn-group").find(".multiselect").removeClass("disabled").attr("title", "None selected");
        }
    });
}

function loadSelectedDcNumbers(invoiceId) {
    $.ajax({
        url: "/erp/sales/json/load_selected_dc_numbers/",
        type: "post",
        datatype:"json",
        data: {invoice_id: invoiceId},
        success: function(response) {
            try{
                if (response.response_message == "Success") {
                    setTimeout(function(){
                        for (j = 0; j < response.dc_codes.length; j++) {
                            $("#id_invoice-dc_no_display").multiselect('select', response.dc_codes[j].dc_id, true);
                        }
                    }, 500);
                }
            } catch (e) {
                console.log(e)
            }
            if($(".header_current_page").text() != "" && $("#id_invoice-goods_already_supplied").is(":checked")) {
                createConsolidateRows();
            }
        }
    });
}

function loadDcDetails(){
    if($('#id_dc_type').val() == "sales"){

        var selected_dc_nos= "";
        var new_dc_no_selected= "";
        var multi_select_dc =  $("select#id_invoice-dc_no_display").next('.btn-group').find('ul').find('li input:checked');
        multi_select_dc.each(function () {
            selected_dc_nos += $(this).val() + ",";
        });

        var dc_ids = selected_dc_nos;
        dc_id = dc_ids.split(",")
        dc_id =dc_id[0]
        if (dc_id !=""){
            dc_no = $("#id_invoice-dc_no_display option:selected").text()
            $.ajax({
                url: "/erp/sales/json/invoice/getDCHeader/",
                type: "post",
                datatype: "json",
                data: {dc_id:dc_id},
                success: function (response) {
                    if (response.response_code!=400){
                        $.each(response, function (i, item) {
                            $('#id_invoice-po_no').val(item.po_no)
                            $('#id_invoice-location_id').val(item.location_id).trigger("chosen:updated");
                            $('#id_invoice-payment_terms').val(item.payment_terms)
                            $('#id_invoice-special_instruction').val(item.special_instructions)
                            $('#id_invoice-po_date').val(item.po_date)
                            UpdateSingleDate('id_invoice-po_date');
                        });
                    }
                }
            });

            $.ajax({
                url: "/erp/sales/json/invoice/getDCMaterials/",
                type: "post",
                datatype: "json",
                data: {dc_ids:dc_ids, invoice_id:$("#id_invoice-id").val()},
                success: function (response) {
                    if (response.response_code!=400){
                    $("#OAMaterialtable").find("tr:gt(0)").remove();
                        $("#OAButton").find('.modal-footer').remove();
                        var s_no = 0
                        $.each(response, function (i, item) {
                            var item_description ="";
                            var stock_qty = 0;
                            var is_non_stockable="for_oa";
                            var isStockable = '';
                            item_description = item.name;
                            var make_name = constructDifferentMakeName(item.make_name);
                            if (item.drawing_no!="" && item.drawing_no!=null){
                                item_description += " - " + item.drawing_no;
                            }
                            if (make_name != ""){
                                item_description += " [" + make_name + "]";
                            }
                            if (item.is_faulty == 1){
                                item_description += " [Faulty]";
                            }

                            if (item.mat_type == 1){
                                stock_qty = item.stock;
                                is_non_stockable = "stock_qty stockable for_oa";
                            }
                            else {
                                isStockable = 'non_stock-flag';
                            }

                            $("#invoice_materials_table").find(".invoice_item_row").not(".deleted_invoice").each(function(){
                                if($(this).attr("id").indexOf("non_stock") == -1 ) {
                                    var isFaulty = $(this).find(".is_faulty").val();
                                    if(isFaulty == "True" || isFaulty == "False") {
                                        isFaulty = isFaulty == "False" ? 0:1;
                                    }
                                    if(item.drawing_no == $(this).find(".item_code").val() &&
                                        item.dc_id == $(this).find(".td-dc_no").find("input").val() &&
                                        ((item.oa_id=='undefined' || item.oa_id==null) ? 0: item.oa_id) == $(this).find(".td-oa_no").find("input").val() &&
                                        item.make_id == $(this).find(".make_id").val() &&
                                        item.is_faulty == isFaulty){
                                        item.dc_pen_qty = item.dc_pen_qty - $(this).find(".td_inv_qty").find("input").val()
                                    }
                                }
                                else {
                                    if($(this).find(".td-item-code").find("input").val().trim() == item.name.trim() &&
                                        item.dc_id == $(this).find(".td-dc_no").find("input").val() &&
                                        ((item.oa_id=='undefined' || item.oa_id==null) ? 0: item.oa_id) == $(this).find(".td-oa_no").find("input").val()){
                                        item.dc_pen_qty = item.dc_pen_qty - $(this).find(".td_inv_qty").find("input").val()
                                    }
                                }
                            });

                            var hsn_sac = (item.hsn_code=='undefined' || item.hsn_code==null) ? '': item.hsn_code;
                            var grn_no = (item.grn_no=='undefined' || item.grn_no==null) ? '': item.grn_no;
                            var item_id = '<input id="id_oamaterial_-'+i+'-item_id" type="text" name="oamaterial_item_id" value="' + item.item_id + '"/>';
                            var hsn_code = `<input id="id_oamaterial_-${i}-hsn_code" type="text" class="form-control text-left" name="oamaterial_hsn_code" maxlength="10" onchange="validateHsnWithSuggestion(this, 'id_oamaterial_-${i}-qty');" onkeypress="validateStringOnKeyPress(this, event, 'hsn_specialChar');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');"  autocomplete="off" value="${hsn_sac}" />`;
                            var remarks = `<input id="id_oamaterial_-${i}-remarks" type="text" class="form-control text-left" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" maxlength="300" name="oamaterial_remarks" value="${item.remarks}" />`;
                            var qty = `<input type="text" id="id_oamaterial_-${i}-qty" name="oamaterial_qty" class="form-control text-right bom_po_qty bom_amt_calc mandatory_field invd_qty setUnitValue" maxlength="16" onchange="validateHsnWithSuggestion($("#id_oamaterial_-${i}-hsn_code"), 'id_oamaterial_-${i}-qty');" onfocus="setNumberRangeOnFocus(this,12,3)" onblur="updateConsolidatedQty(this);" autocomplete="off" data-default-value="${(item.dc_pen_qty * item.scale_factor).toFixed(3)}" value="${(item.dc_pen_qty * item.scale_factor).toFixed(3)}">`;
                            var make_id = '<input hidden="hidden" id="id_oamaterial_make_id_'+i+'" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="oamaterial_make_id" value='+item.make_id+' >';
                            var oa_id = '<input hidden="hidden" id="id_oamaterial_oa_id_'+i+'" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="oamaterial_oa_id" value='+item.oa_id+' >';
                            var dc_id = '<input hidden="hidden" id="id_oamaterial_dc_id_'+i+'" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="oamaterial_dc_id" value='+item.dc_id+' >';
                            var material_type = '<input hidden="hidden" id="id_oamaterial_material_type_'+i+'" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="oamaterial_material_type" value='+item.mat_type+' >';
                            var unit_id = '<input hidden="hidden" id="id_oamaterial_unit_id_'+i+'" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="oamaterial_unit_id" value='+item.unit_id+' >';
                            var is_faulty = '<input hidden="hidden" id="id_oamaterial_is_faulty_'+i+'"  name="oamaterial_is_faulty" value='+item.is_faulty+' >';
                            var oa_code = '<input hidden="hidden" id="id_oamaterial_oa_code_'+i+'" type="text"  class="form-control text-left " name="oamaterial_oa_code" value='+item.oa_code+' >';
                            var grn_id = '<input hidden="hidden" id="id_oamaterial_grn_id_'+i+'" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="oamaterial_grn_id" value='+ grn_no +' >';
                            var alternate_unit_id= '<input hidden="hidden" id="id_oamaterial_alternate_unit_id_'+i+'" type="text" data-default-value='+item.alternate_unit_id+' onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="oamaterial_alternate_unit_id" value='+item.alternate_unit_id+' >';
                            var scale_factor= '<input hidden="hidden" id="id_oamaterial_scale_factor_'+i+'" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="oamaterial_scale_factor" value='+item.scale_factor+' >';
                            var is_service = '<input hidden="hidden" id="id_oamaterial_is_service_'+i+'"   name="oamaterial_is_service" value='+item.is_service+' >';
                            var itemTypeFlag ="";
                            if (item.is_service == 1){
                                itemTypeFlag += `<span class="service-item-flag"></span>`;
                            }
                            if (item.mat_type != 1 && item.is_service != 1){
                                itemTypeFlag += `<span class="non_stock-flag"></span>`;
                            }
                            if (item.dc_pen_qty > 0 && item.is_returnable == 0 ){
                                s_no = s_no +1;
                                var rowClass = item.id+"_"+item.make_id;
                                var consolidated_hsn_sac =  generateUniqueCode(hsn_sac.trim());
                                var consolidatedPlaceHolder = item.item_id+"__"+item.make_id+"__"+item.is_faulty+"__"+consolidated_hsn_sac;
                                if($("#OAMaterialtable ."+consolidatedPlaceHolder).length <= 0 ) {
                                    var options="";
                                    $.each(item.alternate_unit_list, function(i, unit) {
                                        options = `${options}<option value="${unit.alternate_unit_id}" data-val="${unit.scale_factor}">${unit.unit_name}</option>`;
                                    });
                                    if (item.alternate_unit_list.length > 1) {
                                        options = `<select class='form-control' name='alternate_units' onChange='updateConsolidatedUnit(this)'>${options}</select>`;
                                    } else {
                                        options = item.unit;
                                    }
                                    var consolidatedRow = ` <tr data-toggle='close' data-padding='0' consolidated-for='${consolidatedPlaceHolder}' class='consolidated_row'>
                                                                <td class='text-center'><a href='#' onclick='deleteoaGroupRow(this)'>
                                                                    <i style='padding-top: 7px;' class='fa fa-trash-o' title='Delete' alt='Delete'></i>
                                                                </td>
                                                                <td onclick="consolidatedRowToggle(this)" class="consolidated-link" style="min-width: 200px;">
                                                                    <i class="fa consolidated_fa fa-chevron-right" style="float: left; width: 12px;" aria-hidden="true"></i>
                                                                    <span class="consolidated_material_name">${item_description} ${itemTypeFlag}</span>
                                                                </td>
                                                                <td>${hsn_sac}</td>
                                                                <td></td>
                                                                <td class='total_pending_qty text-right'></td>
                                                                <td class='total_qty text-right'></td>
                                                                <td>${options}</td>
                                                                <td class='consolidated_price_column'>
                                                                    <input type="text" class="form-control txt_consolidated_price text-right" value= '${item.price}' maxlength="16" onfocus="setNumberRangeOnFocus(this,10,5)" onblur="updateConsolidatedPrice(this);">
                                                                </td>
                                                                <td class='consolidated_price_column'>
                                                                    <input type="text" class="form-control txt_consolidated_disc text-right" value='${item.discount}' maxlength="6" onfocus="setNumberRangeOnFocus(this,3,2)" onblur="validatePercentage(this, event); updateConsolidatedPrice(this);">
                                                                </td>
                                                                <td class='consolidated_price_column text-right'><span class='txt_consolidated_total'></span></td>
                                                           </tr>`;
                                    $('#OAMaterialtable tbody').append(consolidatedRow);
                                }
                                var code = "";
                                if(item.oa_code != "") {
                                    code += `<span class='visible-block'><b>OA No:</b> <span class='oa-code-block'>${item.oa_code}</span></span>`;
                                }
                                if(item.dc_code != "") {
                                    code += `<span class='visible-block'><b>DC No:</b> <span class='dc-code-block'>${item.dc_code}</span><span>`;
                                }

                                var row = `<tr class='${consolidatedPlaceHolder} hide' id='id_oamaterial_-${i}-row' data-description='${rowClass}' data-row='${consolidatedPlaceHolder}'>
                                                <td hidden=hidden>${item.cat_code} - ${item.drawing_no}</td>
                                                <td class='text-center'>
                                                    <a href='#' onclick='deleteoaRow(this)'>
                                                        <i style='padding-top: 7px;' class='fa fa-trash-o' title='Delete' alt='Delete'></i>
                                                    </a>
                                                </td>
                                                <td style="padding-left: 45px !important;"><span>${code}</span></td>
                                                <td class='hide'>${item_description} ${itemTypeFlag}</td>
                                                <td class="td-hsn-code hsn-wrapper">${hsn_code}</td>
                                                <td>${remarks}</td>
                                                <td class='invd_pending_qty text-right' data-default-pending-qty = '${(Number(item.dc_pen_qty) * Number(item.scale_factor)).toFixed(3)}' data-pending-qty = '${(Number(item.dc_pen_qty) * Number(item.scale_factor)).toFixed(3)}' data-default-dc-qty = '${(item.DC_qty * item.scale_factor)}' data-dc-qty = '${(item.DC_qty * item.scale_factor)}'>${(Number(item.dc_pen_qty) * Number(item.scale_factor)).toFixed(3)} (${(Number(item.DC_qty) * Number(item.scale_factor)).toFixed(3)})</td>
                                                <td>${qty}</td>
                                                <td class = '${is_non_stockable}'>
                                                    <span data-placement='left' data-tooltip='tooltip' title='MSL: ${item.minimum_stock_level}'>${stock_qty}</span>
                                                </td>
                                                <td class='td-unit-name'>${item.unit}</td>
                                                <td class='hide'>
                                                    <input type='text' id='id_oa_qty-${i}' name='oamaterial_unitrate' class='form-control bom_po_unit_rate bom_amt_calc txt_indv_price mandatory_field' maxlength='16' onfocus="setNumberRangeOnFocus(this,10,5)" data-default-value='${item.price / item.scale_factor}' value='${item.price / item.scale_factor}' />
                                                </td>
                                                <td class='hide'>
                                                    <input type='text' id='id_oa_discount-${i}' name='oamaterial_disc' class='form-control bom_po_disc txt_indv_disc bom_amt_calc' maxlength='6' onfocus='setNumberRangeOnFocus(this,3,2)' onblur='validatePercentage(this, event)' data-default-value='${item.discount}' value='${item.discount}' >
                                                </td>
                                                <td class='hide'>
                                                    <input type='text' id='id_oa_qty-${i}' name ='oamaterial_amount' class='form-control bom_po_total' value='' tabindex='-1' readonly='readonly' />
                                                </td>
                                                <td class="td_item_drawing_no" hidden=hidden>${item.drawing_no}</td>
                                                <td hidden=hidden>${item.enterprise_id}</td>
                                                <td hidden=hidden>${make_id}</td>
                                                <td hidden=hidden>${oa_id}</td>
                                                <td hidden=hidden>${oa_code}</td>
                                                <td hidden=hidden>${dc_id}</td>
                                                <td hidden=hidden>${is_faulty}</td>
                                                <td hidden=hidden>${material_type}</td>
                                                <td hidden=hidden>${is_service}</td>
                                                <td hidden=hidden>${grn_id}</td>
                                                <td class='pending_qty' data-default-value='${(Number(item.dc_pen_qty) * Number(item.scale_factor)).toFixed(3)}' hidden=hidden>${(Number(item.dc_pen_qty) * Number(item.scale_factor)).toFixed(3)}</td>
                                                <td class='unit_id' hidden=hidden>${item.unit_id}</td>
                                                <td class='minimum_stock_level hide'>${item.minimum_stock_level}</td>
                                                <td hidden=hidden class='td_item_id'>${item_id}</td>
                                                <td hidden=hidden>${alternate_unit_id}</td>
                                                <td hidden=hidden>${scale_factor}</td>
                                                <script type='text/javascript'>
                                                    $('#id_oamaterial_-${i}-qty').blur(function(){
                                                        if(parseFloat($('#id_oamaterial_-${i}-qty').val()) > 0){
                                                            $('#id_oamaterial_-${i}-hsn_code').addClass('mandatory_field');
                                                            $('#id_oamaterial_-${i}-row').css({backgroundColor:'#ffe'});
                                                        }
                                                        else {
                                                            $('#id_oamaterial_-${i}-hsn_code').removeClass('mandatory_field error-border');
                                                            $('#id_oamaterial_-${i}-row').css({backgroundColor:''})
                                                        }
                                                    });
                                                </script>
                                            </tr>`;
                                if($(`#OAMaterialtable tbody tr.${consolidatedPlaceHolder}`).length > 0) {
                                    $(row).insertAfter($("#OAMaterialtable tr[consolidated-for='"+consolidatedPlaceHolder+"'"));
                                } else {
                                    $("#OAMaterialtable tbody").append(row);
                                }

                                var rowSpanCount = $("#OAMaterialtable ."+consolidatedPlaceHolder).not(".deleted_invoice").length;
                                var updateRow = $("#OAMaterialtable tr[consolidated-for='"+consolidatedPlaceHolder+"'").find(".consolidated_price_column").attr("data-rowspan", Number(rowSpanCount+1));
                                $("#id_invoice-currency_id ").val(item.currency_id).trigger("chosen:updated");
                                if($("#id_invoice-currency_id option:selected").text()!=$("#home_currency_id").val()){
                                    $('#div_con_rate').removeClass("hide");
                                }else {
                                    $('#div_con_rate').addClass("hide");
                                }
                            }
                            updateConsolidatedQty($("."+consolidatedPlaceHolder+" .invd_qty"));
                            updateConsolidatedHeaderUnit($("."+consolidatedPlaceHolder+" .invd_qty"));
                        });
                        var row = "<div class='modal-footer'><span class='pull-left nonStock-text'>  <span class='nonStock-text non_stock-flag'></span> - NON-STOCKABLE</span><span class='pull-left nonStock-text'>  <span class='nonStock-text service-item-flag'></span> - SERVICE</span> <input type='button' onclick='addOA()' class='btn btn-save' id='OAAdd' value='Add' /><a class='btn btn-cancel' id='OACancel' data-dismiss='modal'>Close</a></div>"
                        $('#OAButton').append(row).addClass('tbl');
                        $("#OAMaterial").modal('show');
                        $('.for_oa').addClass('hide');
                        BOMTotalCalculation();
                        AssignMakeQty();
                        TooltipInit();
                    }
                }
            });
        }else {
            $("#ul-tagit-display .li-tagit-display").remove();
        }
    }
}

function updateConsolidatedHeaderUnit(element) {
    setTimeout(function() {
        var currRow = $(element).closest('tr');
        var currentClass = $(currRow).attr('data-row');
        var currentTable = $(currRow).closest('table').attr('id');
        var unitList = [];

        $("#"+currentTable+" ."+currentClass).each(function(){
            unitList.push($(this).find("input[name*='alternate_unit_id']").val());
        });
        if(!unitList.every( (val, i, arr) => val === arr[0] )) {
            $("#"+currentTable).find("tr[consolidated-for='"+currentClass+"']").find("select[name='alternate_units']").val("0");
        }
        else {
            $("#"+currentTable).find("tr[consolidated-for='"+currentClass+"']").find("select[name='alternate_units']").val(unitList[0]);
        }
        $("#"+currentTable).find("tr[consolidated-for='"+currentClass+"']").find("select[name='alternate_units']").trigger("change");
    },100);
}

function updateConsolidatedUnit(element) {
    var currentClass = $(element).closest('tr').attr('consolidated-for');
    var currentTable = $(element).closest('table').attr('id');
    var currentUnitValue = $(element).val();
    var currentUnitText = $(element).find("option:selected").text();
    var currentUnitScaleFactor = $(element).find("option:selected").attr("data-val");
    var lastItem = "";
    var isPriceAvailable = false;
    $("#"+currentTable+" ."+currentClass).each(function(){
        $(this).find('input[name*="alternate_unit_id"]').val(currentUnitValue);
        $(this).find('input[name*="scale_factor"]').val(currentUnitScaleFactor);

        if(currentTable == 'invoice_materials_table') {
            $(this).find(".td-unit-id").find("input").val(currentUnitText);
            var updatedQty = ($(this).find(`.td_inv_qty`).find("input").attr("data-default-value") / currentUnitScaleFactor).toFixed(3)
            var updatedMSLQty = ($(this).find(`.td_inv_qty`).find(".currently_available_pending").attr("data-default-value") / currentUnitScaleFactor).toFixed(3)
            $(this).find(`.td_inv_qty`).find("input").val(updatedQty);
            $(this).find(`.td_inv_qty`).find(".currently_available_pending").text(updatedMSLQty)
            $(this).find(`.td_inv_qty`).find("input").trigger("blur");
            if($(this).find('input[name*="alternate_unit_id"]').attr('data-default-value') == currentUnitValue) {
                var currentPrice = $(this).find('.td-unit-rate input').attr('data-default-value');
                var currentDiscount = $(this).find('.td-unit-discount input').attr('data-default-value');
                $(element).closest('tr').find('input.txt_consolidated_price').val(currentPrice).trigger('blur');
                $(element).closest('tr').find('input.txt_consolidated_disc ').val(currentDiscount).trigger('blur');
                isPriceAvailable = true;
            }
            if(!isPriceAvailable) {
                $(element).closest('tr').find('input.txt_consolidated_price').val("0.00").trigger('blur');
                $(element).closest('tr').find('input.txt_consolidated_disc ').val("0.00").trigger('blur');
            }
        }
        else {
            $(this).find(".td-unit-name").text(currentUnitText);
            var updatedDcPendingQty = ($(this).find(`.invd_pending_qty`).attr("data-default-pending-qty") / currentUnitScaleFactor).toFixed(3)
            var updatedDCQty = ($(this).find(`.invd_pending_qty`).attr("data-default-dc-qty") / currentUnitScaleFactor).toFixed(3)
            var updatedQty = ($(this).find(`input[name="oamaterial_qty"]`).attr("data-default-value") / currentUnitScaleFactor).toFixed(3)
            var updatedPendingQtyTxt = ($(this).find(`.pending_qty`).attr("data-default-value") / currentUnitScaleFactor).toFixed(3)
            $(this).find(`.invd_pending_qty`).attr('data-pending-qty',updatedDcPendingQty)
            $(this).find(`.invd_pending_qty`).attr('data-dc-qty',updatedDcPendingQty)
            $(this).find(`.invd_pending_qty`).text(updatedDcPendingQty+" ("+updatedDcPendingQty+")");
            $(this).find(`input[name="oamaterial_qty"]`).val(updatedQty);
            $(this).find(`.pending_qty`).text(updatedPendingQtyTxt);
            lastItem = $(this).find(`input[name="oamaterial_qty"]`);


            var currentPrice = $(this).find('input[name="oamaterial_unitrate"]').attr('data-default-value');
            var currentDiscount = $(this).find('input[name="oamaterial_disc"]').attr('data-default-value');
            $(element).closest('tr').find('input.txt_consolidated_price').val(currentPrice * currentUnitScaleFactor).trigger('blur');
            $(element).closest('tr').find('input.txt_consolidated_disc ').val(currentDiscount).trigger('blur');
            if(lastItem != "") {
                updateConsolidatedQty(lastItem);
            }
        }
    });
}

function updateConsolidatedQty(curr) {
    setTimeout(function(){
        var currentClass = $(curr).closest('tr').attr('data-row');
        var curQty = 0;
        var curPendingQty = 0;
        var curDcQty = 0;
        $("#OAMaterial ."+currentClass).each(function(){
            curQty += Number($(this).find(".invd_qty").val());
            curPendingQty += Number($(this).find(".invd_pending_qty").attr("data-pending-qty").trim());
            curDcQty += Number($(this).find(".invd_pending_qty").attr("data-dc-qty").trim());
        });
        $("#OAMaterial").find("tr[consolidated-for='"+currentClass+"'] .total_qty").text(curQty.toFixed(3));
        $("#OAMaterial").find("tr[consolidated-for='"+currentClass+"'] .total_pending_qty").text(Number(curPendingQty).toFixed(3)+" ("+ Number(curDcQty).toFixed(3)+")");
        calculateConsolidatedPrice($("#OAMaterial tr[consolidated-for='"+currentClass+"']").find(".txt_consolidated_price"));
    }, 100);
}

function updateConsolidatedPrice(curr) {
    var currentClass = $(curr).closest('tr').attr('consolidated-for');
    var closestTR = $(curr).closest('tr');
    setTimeout(function(){
        $("#OAMaterial ."+currentClass).each(function(){
            $(this).find('.txt_indv_price').val(closestTR.find('.txt_consolidated_price').val());
            $(this).find('.txt_indv_disc').val(closestTR.find('.txt_consolidated_disc').val());
        });
        calculateConsolidatedPrice(curr);
    }, 10);
}

function calculateConsolidatedPrice(curr){
    var closestTR = $(curr).closest('tr');
    var currDCQty = closestTR.find('.total_qty').text();
    var currPrice = closestTR.find('.txt_consolidated_price').val();
    var currDisc = closestTR.find('.txt_consolidated_disc').val()/100;

    var calcDisc = Number((currDCQty * currPrice) * currDisc);
    var currTotal = Number((currDCQty * currPrice) - calcDisc);
    closestTR.find('.txt_consolidated_total').text(Number(currTotal).toFixed(2));
}

function consolidatedRowToggle(self){
    var consolidated_row = $(self).closest("tr").attr("consolidated-for");
    var table_name = $(self).closest("table").attr("id");
    var rowspanCount = $(self).closest("tr").find(".consolidated_price_column").attr("data-rowspan");
    if($(self).find("i").hasClass("fa-chevron-right")){
        $("#"+table_name).find("."+consolidated_row).removeClass("hide");
        $(self).closest("tr").find("i.consolidated_fa").addClass("fa-chevron-down").removeClass("fa-chevron-right");
        $(self).closest("tr").find(".consolidated_price_column").attr("rowspan", rowspanCount);
    }
    else {
        $("#"+table_name).find("."+consolidated_row).addClass("hide");
        $(self).closest("tr").find("i.consolidated_fa").addClass("fa-chevron-right").removeClass("fa-chevron-down");
        $(self).closest("tr").find(".consolidated_price_column").removeAttr("rowspan");
    }
}

function onInvoiceFinYearChange() {
    var partyId = $("#id_invoice-party_id").val();
    var invoiceId = $("#search_invoice_id").val()
    var multi_select_year = $("select#id_invoice_fin_year").next('.btn-group').find('ul').find('li input:checked');
    var multi_select_dc =  $("select#id_invoice-dc_no_display").next('.btn-group').find('ul').find('li input:checked');
    if(multi_select_dc.length > 0){
        $("#invoice_materials_table").find(".consolidated_row").each(function(){
            var element = $(this).closest('tr').attr('consolidated-for');
            deleteInvoiceMaterials(element);
        });
    }
    $("#id_invoice-dc_no_display").html("");
    if(multi_select_year.length <= 0){
        $('#id_invoice-dc_no_display').multiselect('destroy');
        $('#id_invoice-dc_no_display').multiselect();
        removeInvoiceMaterials();
    }
    var financialYears = [];
    multi_select_year.each(function () {
        financialYears.push($(this).val());
    });
    if(multi_select_year.length > 0){
        loadPartyDC(partyId, invoiceId, financialYears.join(","));
    }
}

function createConsolidateRows() {
    $("#invoice_materials_table").find(".invoice_item_row").each(function(){
        var itemId = $(this).find(".item_id").val();
        var makeId = $(this).find(".td-stock-qty").find("span").attr("make_id");
        var isFaulty = $(this).find(".td-stock-qty").find("span").attr("is_faulty");
        var hsnValue = $(this).find(".td-hsn-code").find("input").val();
        isFaulty = isFaulty == "False" ? 0:1;
        var consolidated_hsn_sac =  generateUniqueCode(hsnValue.trim());
        var consolidatedTxt = itemId+"__"+makeId+"__"+isFaulty+"__"+consolidated_hsn_sac;
        $(this).addClass(consolidatedTxt);
        $(this).attr("data-row", consolidatedTxt);
    });
    setTimeout(function() {
        constructConsolidatedRow();
    }, 10)
}

function removeOAMaterials(){
    $("#id_invoice-order_accept_no_display").next("div").find("input").change(function(){
        if(!$(this).is(":checked")){
            selected_type = $("#id_invoice-order_accept_no_display").find("option[value='"+$(this).val()+"']").attr('data-invoice_against')
           if ($('#id_invoice-type option:selected').val()=="JIN" || selected_type == "GRN"){
                removeOARows("grn_no");
            }else{
                removeOARows("oa_no");
            }
        }
    });
}

function removeInvoiceMaterials(){
    $("#id_invoice-dc_no_display").next("div").find("input").change(function(){
        if(!$(this).is(":checked")){
           removeInvoiceRows();
        }
    });
}

function removeInvoiceRows(){
    var item_count = parseFloat($("#id_invoice_mat-TOTAL_FORMS").val());
    var tag_count = parseFloat($("#id_tag-TOTAL_FORMS").val());

    var selected_dc_nos= "";
    var multi_select_dc =  $("select#id_invoice-dc_no_display").next('.btn-group').find('ul').find('li input:checked');
    multi_select_dc.each(function () {
        selected_dc_nos += $(this).val() + ",";
    });

    var dc_ids = selected_dc_nos;
    dc_id = dc_ids.split(",")

    for(i=0;i<item_count;i++){
        if ($("#id_invoice_mat-"+i+"-dc_no").val()!=0 ){
            var match = false
            for(var line = 0; line < dc_id.length-1; line++){
                if ($("#id_invoice_mat-"+i+"-dc_no").val()==dc_id[line]){
                    match = true
                }
            }
            if (match==false){
                var deleteFlag = document.getElementById("id_invoice_mat-"+i+"-DELETE");
                var deleteRow = document.getElementById("invoice_mat-"+i);
                deleteFlag.checked = true;
                deleteRow.style.display = 'none';
                deleteRow.classList.add("deleted_invoice");
                deleted_row_id = $("#invoice_mat-"+i).attr('data-row').trim()
                if($("#invoice_materials_table").find("."+deleted_row_id).not(".deleted_invoice").length <=0) {
                   $("#invoice_materials_table").find("tr[consolidated-for='"+deleted_row_id+"']").remove();
                }
            }
        }
    }
    updateInvoiceTableColspan();
    calculateGrandTotal();
}

function pdfDocumentBtnEvent(){
    $("#regenerate_invoice").click(function () {
        if(sessionStorage.invoicetype == "Issue"){
            generate_issue_pdf(sessionStorage.invoiceid,sessionStorage.invoicetype,sessionStorage.invoicestatus,sessionStorage.invoicedocheader,sessionStorage.print_without_header,true);
        }else{
            generate_pdf_ajax(sessionStorage.invoiceid,sessionStorage.invoicetype,sessionStorage.invoicestatus,sessionStorage.invoicedocheader,sessionStorage.print_without_header,true);
        }
    });
}

function load_oa_details(){
    if($('#id_dc_type').val() == "sales" || $('#id_dc_type').val() == "dc"){
        var selected_oa_nos= "";
        var new_oa_no_selected= "";
        var multi_select_oa =  $("select#id_invoice-order_accept_no_display").next('.btn-group').find('ul').find('li input:checked');
        var oaIds = []; var receiptIds = [];
        multi_select_oa.each(function () {
            selected_oa_nos += $(this).val() + ",";
            selected_type = $("#id_invoice-order_accept_no_display").find("option[value='"+$(this).val()+"']").attr('data-invoice_against')
            if (selected_type == "OA") {
                oaIds.push($(this).val());
            } else {
                receiptIds.push($(this).val());
            }
        });

            var oa_ids = selected_oa_nos;
            oa_id = oa_ids.split(",")
            oa_id =oa_id[0]
            if (oa_id !=""){
                if ($('#id_invoice-type option:selected').val()!="JIN"){oa_no = $("#id_invoice-order_accept_no_display option:selected").text()
                $("#id_invoice-order_accept_no").val(oa_no)
                $.ajax({
                    url: "/erp/sales/json/invoice/getOAHeader/",
                    type: "post",
                    datatype: "json",
                    data: {oa_id:oa_id},
                    success: function (response) {
                        if (response.response_code!=400){
                            $.each(response, function (i, item) {
                                $('#id_invoice-po_no').val(item.po_no)
                                $('#id_invoice-payment_terms').val(item.payment_terms)
                                $('#id_invoice-special_instruction').val(item.special_instructions)
                                $('#id_invoice-po_date').val(item.po_date)
                                $('#id_invoice-order_accept_date').val(item.oa_date)
                                var curDate = moment($('#id_invoice-po_date').val()).format('MMM D, YYYY');
		                    $('#id_invoice-po_date').next('input').val(curDate);
                                UpdateSingleDate('id_invoice-order_accept_date');
                            });
                        }
                    }
                });

                    $.ajax({
                        url: "/erp/sales/json/invoice/load_oa_tags/",
                        type: "post",
                        datatype: "json",
                        data: {oa_ids:oa_ids},
                        success: function (response) {
                            $("#ul-tagit-display .li-tagit-display").remove();
                            $.each(response, function (i, item) {
                                generateFormsetForm('tag');
                                var index = parseInt(parseInt($('#id_tag-TOTAL_FORMS').val()) - 1);
                                $('#id_tag-' + index + '-tag_label').html(item.tag_name);
                                $('#id_tag-' + index + '-tag').val(item.tag_name);
                                create_delete_tag_button();
                            });
                        }
                    });
                    $.ajax({
                        url: "/erp/sales/json/get_oa_tax/",
                        type: "post",
                        datatype: "json",
                        data: { oa_ids: oa_ids},
                        success: function(response){
                            try{
                                var oa_taxes = JSON.stringify(response);
                                if (oa_taxes != null){
                                        $('#oa_taxes').val(oa_taxes);
                                }
                            } catch (e) {
                                console.log(e)
                            }
                        }
                    });
                }
                location_id = $('#id_invoice-location_id option:selected').val()
                if ($('#id_invoice-type option:selected').val()=="JIN"){
                    url = "/erp/sales/json/invoice/getGRNMaterials/"
                }else{
                    url = "/erp/sales/json/oa_materials/"
                }
                $.ajax({
                    url: url,
                    type: "post",
                    datatype: "json",
                    data: {oa_ids:oa_ids, oa_ids: oaIds.join(","), receipt_ids: receiptIds.join(","),
                            'issued_on':$('#id_invoice-issued_on').val(),'invoice_id':$("#id_invoice-id").val(),
                            oa_against_type: 'Invoice',location_id:location_id},
                    success: function (response) {
                        if (response.response_code!=400){
                        $("#OAMaterialtable").find("tr:gt(0)").remove();
                            $("#OAButton").find('.modal-footer').remove();
                            var s_no = 0
                            $.each(response, function (i, item) {
                                var item_description ="";
                                var stock_qty = 0;
                                var is_non_stockable="";
                                var isStockable = '';
                                item_description = item.name
                                if (item.drawing_no!="" && item.drawing_no!=null){
                                    item_description += " - " + item.drawing_no;
                                }
                                var make_name = constructDifferentMakeName(item.make_name);
                                if (make_name != ""){
                                    item_description += " [" + make_name + "]";
                                }
                                if (item.is_faulty == 1){
                                    item_description += " [Faulty]";
                                }
                                if (item.mat_type == 1){
                                    stock_qty = item.stock;
                                    is_non_stockable = "stock_qty stockable for_oa";
                                }
                                var hsn_sac = (item.hsn_code=='undefined' || item.hsn_code==null) ? '': item.hsn_code;
                                var oa_no = (item.oa_id=='undefined' || item.oa_id==null) ? 0: item.oa_id;
                                var grn_no = (item.grn_no=='undefined' || item.grn_no==null) ? 0: item.grn_no;
                                var hsn_code = `<input id="id_oamaterial_-${i}-hsn_code" type="text" class="form-control text-left" name="oamaterial_hsn_code" maxlength="10" onchange="validateHsnWithSuggestion(this, 'id_oamaterial_-${i}-qty');" onkeypress="validateStringOnKeyPress(this, event, 'hsn_specialChar');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" autocomplete="off" value="${hsn_sac}"/>`;
                                var item_id = `<input id="id_oamaterial_-${i}-item_id" type="text" name="oamaterial_item_id" value="${item.item_id}"/>`;
                                var remarks = `<input id="id_oamaterial_-${i}-remarks" type="text" class="form-control text-left" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" maxlength="300" name="oamaterial_remarks" value="${item.remarks}"/>`;
                                var qty = `<input type="text" id="id_oamaterial_-${i}-qty" name="oamaterial_qty" class="form-control text-right bom_po_qty bom_amt_calc mandatory_field setUnitValue" maxlength="16" onchange="validateHsnWithSuggestion($('#id_oamaterial_-${i}-hsn_code'), 'id_oamaterial_-${i}-qty');" onfocus="setNumberRangeOnFocus(this,12,3)" autocomplete="off" value="0">`;
                                var make_id = '<input hidden="hidden" id="id_oamaterial_make_id_'+i+'" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="oamaterial_make_id" value='+item.make_id+' >';
                                var is_faulty = '<input hidden="hidden" id="id_oamaterial_is_faulty_'+i+'"  name="oamaterial_is_faulty" value='+item.is_faulty+' >';
                                var oa_id = '<input hidden="hidden" id="id_oamaterial_oa_id_'+i+'" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="oamaterial_oa_id" value='+ oa_no +'  >';
                                var material_type = '<input hidden="hidden" id="id_oamaterial_material_type_'+i+'" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="oamaterial_material_type" value='+item.mat_type+' >';
                                var unit_id = '<input hidden="hidden" id="id_oamaterial_unit_id_'+i+'" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="oamaterial_unit_id" value='+item.unit_id+' >';
                                var alternate_unit_id= '<input hidden="hidden" id="id_oamaterial_alternate_unit_id_'+i+'" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="oamaterial_alternate_unit_id" value='+item.alternate_unit_id+' >';
                                var scale_factor= '<input hidden="hidden" id="id_oamaterial_scale_factor_'+i+'" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="oamaterial_scale_factor" value='+item.scale_factor+' >';
                                var grn_id = '<input hidden="hidden" id="id_oamaterial_grn_id_'+i+'" type="text" onkeypress ="return validateFloatKeyPress(this,event);" class="form-control text-left " name="oamaterial_grn_id" value='+ grn_no +' >';
                                var is_service = '<input hidden="hidden" id="id_oamaterial_is_service_'+i+'"   name="oamaterial_is_service" value='+item.is_service+' >';
                                var itemTypeFlag ="";
                                if(item.is_service == 1) {
                                    itemTypeFlag += `<span class="service-item-flag"></span>`;
                                }
                                if (item.mat_type!=1 && item.is_service != 1){
                                    itemTypeFlag += `<span class='non_stock-flag'></span>`;
                                }
                                $("#invoice_materials_table").find(".invoice_item_row").not(".deleted_invoice").each(function(){
                                    if($(this).attr("id").indexOf("non_stock") == -1 ) {
                                        var isFaulty = $(this).find(".is_faulty").val();
                                        if(isFaulty == "True" || isFaulty == "False") {
                                            isFaulty = isFaulty == "False" ? 0:1;
                                        }
                                        if(item.drawing_no == $(this).find(".item_code").val() &&
                                            oa_no == $(this).find(".td-oa_no").find("input").val() &&
                                            item.make_id == $(this).find(".make_id").val() &&
                                            item.is_faulty == isFaulty &&
                                            grn_no == $(this).find(".td-grn_no").find("input").val()){
                                            item.oa_pending_qty = item.oa_pending_qty - $(this).find(".td_inv_qty").find("input").val()
                                        }
                                    }
                                    else {
                                        if($(this).find(".td-item-code").find("input").val().trim() == item.name.trim() &&
                                            oa_no == $(this).find(".td-oa_no").find("input").val() &&
                                            grn_no == $(this).find(".td-grn_no").find("input").val()){
                                            item.oa_pending_qty = item.oa_pending_qty - $(this).find(".td_inv_qty").find("input").val()
                                        }
                                    }
                                });
                                if (item.oa_pending_qty > 0){
                                    s_no = s_no +1;
                                    var rowClass = item.item_id+"_"+item.make_id+"-"+item.is_faulty;
                                    var row = ` <tr id='id_oamaterial_-${i}-row' data-description='${rowClass}'>
                                                    <td hidden=hidden>${item.cat_code} - ${item.drawing_no}</td>
                                                    <td class='text-center bom-sno'>
                                                        <a href='#' onclick='deleteoaRow(this)'>
                                                            <i class='fa fa-trash-o' title='Delete' alt='Delete'></i>
                                                        </a>
                                                    </td>
                                                    <td>${item.oa_code}</td>
                                                    <td>${item_description} ${itemTypeFlag}</td>
                                                    <td class="td-hsn-code hsn-wrapper">
                                                        ${hsn_code}
                                                    </td>
                                                    <td>${remarks}</td>
                                                    <td class='oa_dc_pending_qty text-right'>${item.oa_pending_qty.toFixed(3)}<br />(${item.OA_qty.toFixed(3)})</td>
                                                    <td>${qty}</td>
                                                    <td class='${is_non_stockable} text-right'>
                                                        <span data-placement='left' data-tooltip='tooltip' title='MSL: ${item.minimum_stock_level}'>
                                                            ${stock_qty}
                                                        </span>
                                                    </td>
                                                    <td>${item.unit}</td>
                                                    <td>
                                                        <input type='text' id='id_oa_qty-${i}' name='oamaterial_unitrate' class='form-control text-right bom_po_unit_rate bom_amt_calc mandatory_field' maxlength='16' onfocus='setNumberRangeOnFocus(this,10,5)' value='${item.price}'>
                                                    </td>
                                                    <td>
                                                        <input type='text' id='id_oa_discount-${i}' name='oamaterial_disc' class='form-control text-right bom_po_disc bom_amt_calc' maxlength='6' onfocus='setNumberRangeOnFocus(this,3,2)' onblur='validatePercentage(this, event)' value='${item.discount}'>
                                                    </td>
                                                    <td>
                                                        <input type='text' name ='oamaterial_amount' value='' class='form-control text-right bom_po_total' tabindex='-1' readonly id='id_oa_qty-${i}'>
                                                    </td>
                                                    <td class='td_item_drawing_no' hidden=hidden>${item.drawing_no}</td>
                                                    <td hidden=hidden>${item.enterprise_id}</td>
                                                    <td hidden=hidden>${make_id}</td>
                                                    <td hidden=hidden>${is_faulty}</td>
                                                    <td hidden=hidden>${oa_id}</td>
                                                    <td hidden=hidden>${grn_id}</td>
                                                    <td hidden=hidden>${material_type}</td>
                                                    <td hidden=hidden>${is_service}</td>
                                                    <td class='pending_qty' hidden=hidden>${item.oa_pending_qty}</td>
                                                    <td class='unit_id' hidden=hidden>${item.unit_id}</td>
                                                    <td class='alternate_unit_id' hidden=hidden>${alternate_unit_id}</td>
                                                    <td class='scale_factor' hidden=hidden>${scale_factor}</td>
                                                    <td class='minimum_stock_level hide'>${item.minimum_stock_level}</td>
                                                    <td hidden=hidden class='td_item_id'>${item_id}</td>
                                                    <script type='text/javascript'>
                                                        $('#id_oamaterial_-${i}-qty').blur(function() {
                                                            if(parseFloat($('#id_oamaterial_-${i}-qty').val()) > 0){
                                                                $('#id_oamaterial_-${i}-hsn_code').addClass('mandatory_field');
                                                                $('#id_oamaterial_-${i}-row').css({backgroundColor:'#ffe'});
                                                            }
                                                            else {
                                                                $('#id_oamaterial_-${i}-hsn_code').removeClass('mandatory_field error-border');
                                                                $('#id_oamaterial_-${i}-row').css({backgroundColor:''})
                                                            }
                                                        });
                                                    </script>
                                                </tr>`;
                                    $('#OAMaterialtable tbody').append(row);
                                    $("#id_invoice-currency_id ").val(item.currency_id).trigger("chosen:updated");
                                    if($("#id_invoice-currency_id option:selected").text()!=$("#home_currency_id").val()){
                                        $('#div_con_rate').removeClass("hide");
                                        $("#id_invoice-currency_conversion_rate").val(item.conversion_rate)
                                    }else {
                                        $('#div_con_rate').addClass("hide");
                                        $('#id_invoice-currency_conversion_rate').val('1.0');
                                    }
                                }
                            });// css for update the table row color and border line
                            var row = "<div class='modal-footer'><span class='pull-left nonStock-text'>  <span class='nonStock-text non_stock-flag'></span> - NON-STOCKABLE</span><span class='pull-left nonStock-text'>  <span class='nonStock-text service-item-flag'></span> - SERVICE</span> <input type='button' onclick='addOA()' class='btn btn-save' id='OAAdd' value='Add' /><a class='btn btn-cancel' id='OACancel' data-dismiss='modal'>Close</a></div>"
                            $('#OAButton').append(row).addClass('tbl');
                            $("#OAMaterial").modal('show');
                            $('.for_oa').removeClass('hide');
                            BOMTotalCalculation();
                            AssignMakeQty();
                            TooltipInit();
                        }
                    }
                });
            }else {
                $("#ul-tagit-display .li-tagit-display").remove();
            }
        }
	}



    function calculateStockValue(selectedUnits) {
        if(!$("#invoice_quick_add").is(":checked")) {
            var scale_factor = $(selectedUnits).find("option:selected").data("val");
            var closing_qty = $("#primary_closing_qty").val() / scale_factor;
            var Avl_Qty = $("#primary_Avl_Qty").val() / scale_factor;
            console.log("Avl_Qty",Avl_Qty);
            var msl_qty = $("#primary_msl_qty").val() / scale_factor;
            $("#closing_qty").text("MAX ISSUE:" + closing_qty.toFixed(2));
            $("#Avl_Qty").val(Avl_Qty);
            $("#msl_qty").val(msl_qty);
        }
        loadAlternateUnitPrice()
    }

    function loadAlternateUnitPrice(){
        if($("#id_invoice_mat-__prefix__-alternate_units option:selected").attr('data-val') !=0 && $("#material_rate_hidden").val() != 0 && $("#material_rate_hidden").val() != ""){
            $("#id_invoice_mat-__prefix__-rate").val($("#id_invoice_mat-__prefix__-alternate_units option:selected").attr('data-val') * $("#material_rate_hidden").val());
        }else{
            $("#id_invoice_mat-__prefix__-rate").val("0.00000")
        }
    }

    function customIssuedOnChanged() {
        issued_on = $("#id_invoice-issued_on").val();
        inv_type = $('#id_invoice-type').val();
        if (inv_type == "JDC" ){
            params = {party_id: $("#id_invoice-party_id").val(), invoice_id: $("#search_invoice_id").val(), issued_on: issued_on};
            $.ajax({
                url: "/erp/stores/json/getjobpo/",
                type: "post",
                datatype:"json",
                data: params,
                async: false,
                success: function(response) {
                    var selectedPo = Number($("#id_invoice-job_po_id").val());
                    var selectedPoText = $("#id_invoice-job_po_id option:selected").text();
                    var isExistingPoAvailable = false;
                    var newPoList = "<option value=''>--select--</option>";
                    $.each(response, function (i, item) {
                        newPoList += `<option value="${item.value}">${item.id}</option>`
                        if(item.value === selectedPo) {
                            isExistingPoAvailable = true;
                        }
                    });
                    if(!isExistingPoAvailable && selectedPo != "") {
                        swal({
                            title: "Are you sure?",
                            text: `Issued Date falls out of the validity period of the JO -<br /> <b>${selectedPoText}</b>, hence related items will be removed from the Item Particulars.
                                    <br /><br />Do you still want to proceed?<span class="hide" id='jdc_old_date'>${issued_on}</span>`,
                            type: "warning",
                            showCancelButton: true,
                            confirmButtonColor: "#209be1",
                            confirmButtonText: "Yes, do it!",
                            closeOnConfirm: true
                        },
                        function(isConfirm){
                            if(isConfirm){
                                $(`#id_invoice-job_po_id`).html(newPoList);
                                $("#id_invoice-job_po_id").val("").trigger('chosen:updated');
                                $("#invoice_issued_on_date").attr("data-default-date", issued_on)
                                removeRows();
                            }
                            else {
                                $("#id_invoice-issued_on").val(moment(new Date($("#invoice_issued_on_date").attr("data-default-date"))).format("YYYY-MM-DD hh:mm:ss"));
                                $("#invoice_issued_on_date").datetimepicker("remove");
                                IssueDateTimePicker();
                                setReturnStartDate();
                            }
                        });
                    }
                    else {
                        $(`#id_invoice-job_po_id`).html(newPoList);
                        if(isExistingPoAvailable) {
                            $("#id_invoice-job_po_id").val(selectedPo).trigger('chosen:updated');
                        }
                        else {
                            $("#id_invoice-job_po_id").val("").trigger('chosen:updated');
                        }
                        $("#invoice_issued_on_date").attr("data-default-date", issued_on);
                    }
                }
            });
        }
    }

    function removeRows(){
        var item_count = parseFloat($("#id_invoice_mat-TOTAL_FORMS").val());
        var tag_count = parseFloat($("#id_tag-TOTAL_FORMS").val());

        for(i=0;i<item_count;i++){
            var deleteFlag = document.getElementById("id_invoice_mat-"+i+"-DELETE");
            var deleteRow = document.getElementById("invoice_mat-"+i);
            deleteFlag.checked = true;
            deleteRow.style.display = 'none';
        }

        for(i=0;i<tag_count;i++){
            var deleteFlag = document.getElementById("id_tag-"+i+"-DELETE");
            var deleteRow = document.getElementById("tag-"+i);
            if (deleteFlag){
                deleteFlag.checked = true;
                deleteRow.style.display = 'none';
            }
        }
        calculateGrandTotal();
    }

    function updateUserSettingsFlag() {
        var currentUserId = $(".user_profile_login").text().trim();
        var current_enterprise_id = $("#enterprise_id").val();
        var local_user_settings = JSON.parse(localStorage.getItem('user_settings'));
        var is_new_user = true;
        if(local_user_settings) {
            for (var i = 0; i < local_user_settings.length; i++){
                if(local_user_settings[i].user_id == currentUserId+"-"+current_enterprise_id) {
                    local_user_settings[i].invoice_quick_add = $("#invoice_quick_add").is(":checked");
                    local_user_settings[i].invoice_round_off = $("#id_auto-calculate").is(":checked");
                    local_user_settings[i].default_label = $("#show_template_label").is(".on");
                    is_new_user = false
                    break;
                }
            }
        }
        if(is_new_user || !local_user_settings) {
            if(!local_user_settings) {
                local_user_settings = [];
            }
            var userSettings = {}
            userSettings ["user_id"] = currentUserId+"-"+current_enterprise_id;
            userSettings ["invoice_quick_add"] = $("#invoice_quick_add").is(":checked");
            userSettings ["invoice_round_off"] = $("#id_auto-calculate").is(":checked");
            userSettings ["default_label"] = $("#show_template_label").is(".on");
            local_user_settings.push(userSettings);
        }
        localStorage.setItem("user_settings", JSON.stringify(local_user_settings));
    }

    function checkUserSettingsFlag() {
        var currentUserId = $(".user_profile_login").text().trim();
        var current_enterprise_id = $("#enterprise_id").val();
        var local_user_settings = JSON.parse(localStorage.getItem('user_settings'));
        var checkQuickAddFlag = false;
        var checkRoundOffFlag = false;
        var checkDefaultChangeLabelFlag = false;
        if(local_user_settings) {
            for (var i = 0; i < local_user_settings.length; i++){
                if(local_user_settings[i].user_id == currentUserId+"-"+current_enterprise_id) {
                    if(local_user_settings[i].invoice_quick_add) {
                        checkQuickAddFlag = true;
                    }
                    if(local_user_settings[i].invoice_round_off) {
                        checkRoundOffFlag = true;
                    }
                    if(local_user_settings[i].default_label) {
                        checkDefaultChangeLabelFlag = true;
                    }
                    break;
                }
            }
        }
        $("#invoice_quick_add").prop("checked", checkQuickAddFlag);
        $("#id_auto-calculate").prop("checked", checkRoundOffFlag);
        $("#show_template_label").is(".on", checkDefaultChangeLabelFlag);
        if(checkDefaultChangeLabelFlag) {
            $("#show_template_label").addClass("on");
            $("#show_template_label").attr("data-original-title", "Show Default Label")
			$("[data-default-label]").each(function() {
            	var change_label_name=$(this).attr("data-template-label")
            	$(this).text(change_label_name);
            });
        }
        else {
            $("#show_template_label").removeClass("on");
            $("#show_template_label").attr("data-original-title", "Show Invoice Template Label")
			$("[data-default-label]").each(function() {
            	var change_label_name=$(this).attr("data-default-label")
            	$(this).text(change_label_name);
            });
        }
    }

    function invoiceMaterialTableSerialNumberInit() {
        $("#invoice_materials_table tbody.item-for-goods tr:visible, #invoice_materials_table tbody.item-for-service tr:visible").each(function(i) {
           $(this).find(".s_no").text(i+1+".")
        })
    }

    function woForIssue(){
        $(".for_wo_no, for_dc").addClass('hide');
        if($("#id_dc_type").val() == "internal"){
            $(".for_wo_no").removeClass('hide');
            $(".for_dc").addClass('hide');
        }
        else{
            $(".for_wo_no").removeClass('hide');
             $(".for_dc").removeClass('hide');
        }
    }
    function checkQty(current, qtyId = ""){
       const text = $("#id_invoice-job_po_id option:selected").text();
       if($('#id_invoice-type').val() == "Issue" && text.includes('MRS')){
            if (localStorage.getItem('MRSStockDetails')) {
                var item_id = $(current).closest('tr').find('td input[name="catmaterial_item_id"]').val();
                var MRSStockDetails = JSON.parse(localStorage.getItem("MRSStockDetails"));
                if(MRSStockDetails.length > 0){
                            if (MRSStockDetails.some(obj => obj.cat_code == item_id)) {
                                const matchingObject = MRSStockDetails.find(obj => obj.cat_code == item_id);
                                var mrs_qty = matchingObject.quantity;
                                var issue_qty = matchingObject.issue_qty;
                                if($("#"+qtyId).val().trim() > (mrs_qty - issue_qty))
                                    {
                                    swal({title: "", text: "Issue Quantity should not be greater than MRS Qty", type: "error"});
                                    $("#"+qtyId).addClass("error-border");
                                 }
                            }
                }
            }
       }
    }
    function removeErrorBorder(current)
	{
	if ($("#"+current).hasClass("error-border")) {
	$("#"+current).removeClass("error-border");
	}
}

