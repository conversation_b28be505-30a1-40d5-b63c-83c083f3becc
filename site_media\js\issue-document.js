function generate_issue_pdf(inv_id, inv_type, inv_status, document_header=[], print_without_header=false, document_regenerate=false){
    $("#inv_doc_btn a.btn, #remarks").addClass("hide");
    $("#inv_document_modal").modal("show");
    $("#loading").show();
    sessionStorage.invoiceid = inv_id
    sessionStorage.invoicetype = inv_type
    sessionStorage.invoicestatus = inv_status
    sessionStorage.invoicedocheader = document_header
    sessionStorage.print_without_header = print_without_header
    $.ajax({
        url: "/erp/sales/json/inv_doc/",
        type: "post",
        datatype: "json",
        data: {invoice_id:inv_id, response_data_type: 'data', document_header: document_header, inv_type:inv_type, document_regenerate:document_regenerate},
        success: function (response) {
            if(response.remarks != undefined) {
                if(response.remarks.length > 0){
                    $(".remarks_count_link").text(response.remarks.length+" remarks").removeClass('disabled')
                }
                else {
                    $(".remarks_count_link").text("No remarks").addClass('disabled')
                }
                $("#inv_document_remarks").val(JSON.stringify(response.remarks));
            }
            $("#modal_inv_id").val(inv_id);
            $("#modal_inv_type").val(inv_type);
            var row = '<object id="inv_document" class="document_viewer" data="'+response.url+'">'+getPdfErrorMessage()+'</object>';
            if(document_regenerate == true) {
                $("#inv_document_container").html('');
            }
            $("#inv_document_container").html(row);
            $("#display_popup").removeClass('hide');
            $("#regenerate_invoice").removeClass("hide");
            if($("#remarks").hasClass("hide") || $("#remarks").length <= 0) {
                $(".remarks_count_link").css({float: "left"});
            }
            else {
                $(".remarks_count_link").css({float: "right"});
            }
            $("#remarks").val("");
            closeCustomDropdown();
            $("#loading").hide();
        },
        error: function(){
            $("#inv_document_modal").modal("hide");
            $("#loading").hide();
        }
    });
    $("#inv_document_modal footer").remove();
    $('#inv_document_modal').on('hidden.bs.modal', function () {
        $(".remarks_count_link").css({float: "right"});
        $("#inv_document").remove();
        $("#inv_document_container").html(getPdfLoadingImage());
    });
}