<html lang="en">
<head>
  <title>xserp</title>
  <meta charset="utf-8">
  <link rel="icon" href="/site_media/images/xs-logo-with-border.png" type="image/png">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" type="text/css" href="/site_media/css/bootstrap.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/font-awesome.min.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/erp_default.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/JSCustomValidator.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/sweetalert.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/roboto-font.css?v={{ current_version }}" >
  <link rel="stylesheet" type="text/css" href="/site_media/css/Fonts.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/login.css?v={{ current_version }}" >
  <link rel="stylesheet" type="text/css" href="/site_media/css/login-menu.css?v={{ current_version }}" >
  <link type="text/css" rel="stylesheet" href="/site_media/css/pt-sans.css?v={{ current_version }}" >

  <script type="text/javascript" src="/site_media/js/jquery-3.1.1.min.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/bootstrap.min.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/jquery-ui.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/JSCustomValidator.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/sweetalert.min.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/cookies.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/utility.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/field-validation.js?v={{ current_version }}"></script>
  <script async src="https://www.googletagmanager.com/gtag/js?id=AW-*********"></script>
  <script>
	  window.dataLayer = window.dataLayer || [];
	  function gtag(){dataLayer.push(arguments);}
	  gtag('js', new Date());
	  gtag('config', 'AW-*********');
  </script>
  <style type="text/css">
  	.disabled-otp {
  		cursor: not-allowed;
  		opacity: 0.7;
  	}

  	.grecaptcha-badge {
  		z-index: 100;
  	}

  	.pay_button {
	    color: #FFF;
	    border: #4a90e2;
	    background-color: #4a90e2;
	    border-radius: 5px;
	    font-size: 16px;
	    text-align: center;
	    padding: 10px 35px 11px 35px;
	    line-height: 50px;
	    text-decoration: none !important;
	    font-weight:  400;
	}
  </style>
</head>

<body style="background: url('/site_media/images/login-bg.png'); background-size: cover;">
    {% include "public/theme-header.html" %}
    <!-- main menu -->
    <div class="main__theme__wrap login-page-container">
    	<div class="container login-testimonial-container">
	       	<div class="col-sm-6 testimonial-container">
	       		<div class="col-sm-12">
	       			<div class="testimonial_slider_2 text-center">
						<input type="radio" name="slider_2" id="slide_2_1" checked />
						<input type="radio" name="slider_2" id="slide_2_2" />
						<input type="radio" name="slider_2" id="slide_2_3" />
						<input type="radio" name="slider_2" id="slide_2_4" />
						<input type="radio" name="slider_2" id="slide_2_5" />
						<div class="testimonial_inner clearfix">
							<div class="slide_content hide">
								<div class="testimonial_2">
									<div class="content_2">
										<div style="font-size: 30px; font-weight: 100; font-family: roboto;">Now you can Pay the Subscription fee directly from the Application!<br /><br /><br /></div>
									</div>
								</div>
								<div class="testimonials-signature text-center">
		       						<img src="/site_media/images/testimonials/Payment.png" class="img-responsive" style="width: 300px; margin: 0 auto;"><br />
			       				</div>
							</div>

							<div class="slide_content">
								<div class="testimonial_2 testimonials-signature text-center" style="margin-top: -35px">
									<div style="font-size: 45px; font-weight: 100; font-family: roboto; margin-bottom: 20px;">
											ACCOUNTS
										</div>
									<div>
			       						<img src="/site_media/images/testimonials/accounts.png" class="img-responsive" style="width: 200px; margin: 0 auto;">
			       					</div>
			       				</div>
								<div class="testimonial_2" style="border-bottom: none;">
									<div class="content_2 col-sm-6" style="margin-top: 15px;">
										<div style="font-size: 20px; font-weight: 100; font-family: 'Permanent Marker', cursive; text-align: left;">
<!--											<span style="font-size:40px; margin-left: 10px;">5499</span>-->
										</div>

										<div style="font-size: 20px; font-weight: 100; font-family: roboto; text-align: left; margin-top: -15px;">
<!--											₹ <span style="font-size:40px; text-decoration: line-through">10000</span> + Taxes /year-->
										</div>
									</div>	
									<div class="content_2 col-sm-6">
										<ul style="list-style: none; line-height: 32px;text-align: right;">
											<li>Dashboards</li>
											<li>Financial Entries</li>
											<li>Bill Settlements</li>
<!--											<li>Limited Support</li>-->
										</ul>
									</div>
								</div>
							</div>
							<div class="slide_content">
								<div class="testimonial_2 testimonials-signature text-center" style="margin-top: -35px">
									<div style="font-size: 45px; font-weight: 100; font-family: roboto; margin-bottom: 20px;">
											INVENTORY & PURCHASE
										</div>
									<div>
			       						<img src="/site_media/images/testimonials/purchase.png" class="img-responsive" style="width: 200px; margin: 0 auto;">
			       					</div>
			       				</div>
								<div class="testimonial_2" style="border-bottom: none;">
									<div class="content_2 col-sm-6">
<!--										<div style="font-size: 20px; font-weight: 100; font-family: 'Permanent Marker', cursive; text-align: left;">-->
<!--											<span style="font-size:40px; margin-left: 15px;">9999</span>-->
<!--										</div>-->

<!--										<div style="font-size: 20px; font-weight: 100; font-family: roboto; text-align: left; margin-top: -15px;">-->
<!--											₹ <span style="font-size:40px; text-decoration: line-through">18000</span> + Taxes /year-->
<!--										</div>-->
										<ul style="list-style: none; line-height: 29px;text-align: left;margin-left: -50px">
											<li>Invoice Processing</li>
											<li>Purchase Order (PO) Creation</li>
											<li>Supplier Management</li>
											<li>PO Approval Workflow</li>
										</ul>
									</div>	
									<div class="content_2 col-sm-6">
										<ul style="list-style: none; line-height: 29px;text-align: right;">
											<li>Inventory Tracking</li>
											<li>Replenishment Planning</li>
											<li>Inventory Adjustments</li>
											<li>Reporting and Analytics</li>
										</ul>
									</div>
								</div>
							</div>
							<div class="slide_content">
								<div class="testimonial_2 testimonials-signature text-center" style="margin-top: -35px">
									<div style="font-size: 45px; font-weight: 100; font-family: roboto; margin-bottom: 20px;">
											AUDIT
										</div>
									<div>
			       						<img src="/site_media/images/testimonials/audit.png" class="img-responsive" style="width: 200px; margin: 0 auto;">
			       					</div>
			       				</div>
								<div class="testimonial_2" style="border-bottom: none;">
									<div class="content_2 col-sm-6" style="margin-top: 15px;">
<!--										<div style="font-size: 20px; font-weight: 100; font-family: roboto; text-align: left; margin-top: -15px;">-->
<!--											₹ <span style="font-size:40px; ">_ _ _ _ _</span> + Taxes /year<br /><br />-->
<!--											<div class="text-center">-->
<!--												<a role="button" class="pay_button" data-toggle="modal" data-target="#contact-form-modal">REACH SALES</a>-->
<!--											</div>-->
<!--										</div>-->
									</div>
									<div class="content_2 col-sm-6">
										<ul style="list-style: none; line-height: 29px;text-align: right;">
											<li>Credit and debit Note</li>
											<li>Internal Note Creation</li>
											<li>Expense track</li>
<!--											<li>Dedicated Support</li>-->
										</ul>
									</div>
								</div>
							</div>
							<div class="slide_content">
								<div class="testimonial_2 testimonials-signature text-center" style="margin-top: -35px">
									<div style="font-size: 45px; font-weight: 100; font-family: roboto; margin-bottom: 20px;">
											INVOICE
										</div>
									<div>
			       						<img src="/site_media/images/testimonials/sales.png" class="img-responsive" style="width: 200px; margin: 0 auto;">
			       					</div>
			       				</div>
								<div class="testimonial_2" style="border-bottom: none;">
									<div class="content_2 col-sm-6" style="margin-top: 15px;">
<!--										<div style="font-size: 20px; font-weight: 100; font-family: roboto; text-align: left; margin-top: -15px;">-->
<!--											₹ <span style="font-size:40px; ">_ _ _ _ _</span> + Taxes /year<br /><br />-->
<!--											<div class="text-center">-->
<!--												<a role="button" class="pay_button" data-toggle="modal" data-target="#contact-form-modal">REACH SALES</a>-->
<!--											</div>-->
<!--										</div>-->
									</div>
									<div class="content_2 col-sm-6">
										<ul style="list-style: none; line-height: 29px;text-align: right;">
											<li>Create Sales Estimate</li>
											<li>Order Acknowledgement</li>
											<li>E-invoice</li>
											<li>GST Portal Access</li>
										</ul>
									</div>
								</div>
							</div>
							<div class="slide_content">
								<div class="testimonial_2">
									<div class="content_2">
										<div style="font-size: 30px; font-weight: 100; font-family: roboto;">We still do accommodate special considerations! 😉<br />
											<div class="text-center" style="margin-top: 10px;">
												<a role="button" class="pay_button" data-toggle="modal" data-target="#contact-form-modal">REACH OUT TO US</a>
											</div>
										</div><br />
										<small>* We offer discounts & waivers based on Merits of Case arbitrated by us.</small>
									</div>
								</div>
								<div class="testimonials-signature text-center">
									<div>
			       						<img src="/site_media/images/testimonials/Reward.png" class="img-responsive" style="width: 350px; margin: 0 auto;"><br />
			       					</div>
			       				</div>
							</div>
						</div>
						<div id="controls">
							<label class="slider-label" for="slide_2_1"></label>
							<label class="slider-label" for="slide_2_2"></label>
							<label class="slider-label" for="slide_2_3"></label>
							<label class="slider-label" for="slide_2_4"></label>
							<label class="slider-label" for="slide_2_5"></label>
						</div>
					</div>
	       		</div>
		  	</div>
		  	<div class="col-sm-6 login-signup-container">
			    <div class="col-sm-12 xserp-panel-right">
			        <div class="xserp-login" style="height: 575px;">
			          	<div class="xserp-panel panel-login text-center active">
			             	<div class="xserp-heading">
			                	<div class="login-header">
			            			Login to Your Office
			          			</div>
			             	</div>
		             		<div class="row">
			                	<div class="col-xs-12 col-sm-12">
			                  		<input type="hidden" id="id_submit_response" value="{{message}}"/>
		                  			<form name="thisForm" method="post" action="/erp/login/" onsubmit="return validateform()">
		                    			{% csrf_token %}
			                    		{% if wrong_credentials %}
			              					<div class="alert alert-danger text-center" style="margin-top: -55px; margin-bottom: 35px;">
			                					<span class="xs_error_message">Invalid Login Credentials.</span>
			              					</div>
			              				{% endif %}
			              				{% if user_inactive %}
				              				<div class="alert alert-danger text-center" style="margin-top: -45px; margin-bottom: 35px;">
				                				<span class="xs_error_message">Your User ID (Email) is Inactive.<br/>Please contact your administrator.</span>
				              				</div>
			              				{% endif %}
			                      		<div class="form-group wrap-input floating-label">
			                         		<input type="text" class="form-control floating-input" name="user_email" id="id_user_email" placeholder=" ">
			                         		<label>USERNAME</label>
			                      		</div>
			                      		<div class="form-group wrap-input floating-label" style="margin-bottom: 0;">
			                         		<div class="pwdMask">
			                            		<input type="password" class="form-control password floating-input" id="id_password" name="password" placeholder=" " value="" autocomplete="off">
			                            		<label>PASSWORD</label>
			                            		<span class="fa fa-eye-slash pwd-toggle"></span>
			                            
			                        		</div>
			                     	 	</div>
				                      	<div class="row remember-row">
				                         	<div class="col-xs-6 col-sm-6">
				                            	<label class="checkbox text-left hide">
				                            		<input type="checkbox" value="remember-me">
				                            		<span class="label-text"> Remember Me</span>
				                            	</label>
				                         	</div>
				                        	 <div class="col-xs-6 col-sm-6">
				                            	<p class="forgotPwd">
				                               		<a class="lnk-toggler" data-panel=".panel-forgot" href="#" style="font-size: 12px;">Forgot password?</a>
				                            	</p>
				                         	</div>
				                      	</div>
				                      	<!-- ./remember-row -->
			                      		<div class="form-group">
				                         	<button class="btn btn-lg btn-primary btn-block" id="sign_in_form" type="submit">Login</button>
				                         	<div class="col-sm-12 text-center" style="margin: 20px 0; color: #777; ">
				                         		<span style="text-decoration: line-through; letter-spacing: 16px; margin-right: 8px;">&nbsp;&nbsp;</span>Don't have a <span style="font-family: 'Expletus Sans', cursive; font-size: 18px;"> xs<b><u>erp</u></b></span> Account?<span style="text-decoration: line-through; letter-spacing: 16px; margin-left: 8px;">&nbsp;&nbsp;</span></div>
				                         	<button style="text-shadow: 1px 0px #889ffa; letter-spacing: 1px; font-size: 22px;" class="btn btn-lg btn-signup btn-block lnk-toggler" data-panel=".panel-signup" href="#">Sign Up! It's Free!</button>
				                      	</div>
				                   	</form>
			                   		<p class="hide">Don’t have an account? <a class="lnk-toggler" data-panel=".panel-signup" href="#">Sign Up Free!</a></p>
			               	 	</div>
			             	</div>
			          </div>
			          <!-- ./panel-login -->
			          <!-- panel-signup start -->
			          <div class="xserp-panel panel-signup text-center">
			             	<div class="row">
			                	<div class="col-xs-12 col-sm-12 remove-padding registration-screen">
			                   		<div class="xserp-heading" style="padding-bottom: 0; margin-bottom: 20px;">
			                      		<div class="signup-header">
											<span style="font-weight: normal">Get Started with our free trial</span><br />
											<small>Please fill the fields and get a free ERP Consulting Session.</small>
		              					</div>
			                   		</div>
		                            {% csrf_token %}
		                            <div class="form-group wrap-input floating-label">
		                                {{enterprise_form.name}}
		                                <label>COMPANY NAME *</label>
		                            </div>
		                            <div class="form-group wrap-input floating-label" style="width: 49%; display: inline-block; float: left;">
		                                {{enterprise_form.firstname}}
		                                <label>FIRST NAME *</label>
		                            </div>
		                            <div class="form-group wrap-input floating-label" style="width: 49%; display: inline-block; margin-left: 2%;">
		                                {{enterprise_form.lastname}}
		                                <label>LAST NAME *</label>
		                            </div>
		                            <div class="form-group wrap-input floating-label">
		                                {{enterprise_form.email}}
		                                <label>BUSINESS EMAIL ADDRESS *</label>
		                            </div>
		                            <div class="form-group wrap-input floating-label" style="width: 49%; display: inline-block; float: left;">
		                                <div class="pwdMask">
			                                {{enterprise_form.password}}
			                                <label>PASSWORD *</label>
			                            </div>
		                            </div>
		                            <div class="form-group wrap-input floating-label" style="width: 49%; display: inline-block; margin-left: 2%;">
		                                <div class="pwdMask">
			                                <input autocomplete="new-password" class="form-control floating-input" id="id_enterprise-confirm-password" maxlength="16" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" placeholder=" " type="password">
			                                <label>CONFIRM PASSWORD *</label>
			                                <span class="fa fa-eye-slash pwd-toggle" style="right: 0;"></span>
			                            </div>
		                            </div>
		                            <div class="form-group wrap-input floating-label prefix-input">
		                                <span style="color: #999; font-weight: normal; position: absolute; top: -4px; font-size: 16px; padding: 13px 12px;">+91</span>
		                                {{enterprise_form.mobile}}
		                                <label>MOBILE NUMBER *</label>
		                            </div>
	                                <div class="form-group">
	                                    <label class="checkbox text-left terms-checkbox" style="margin-top: 0;">
		                                    <input type="checkbox" id="privacy_policy">
		                                    <span class="label-text" style="font-size: 11px;"> I agree to the <a href='/erp/public/privacy/' target="_blank">privacy policy</a> and <a href='/erp/public/terms' target="_blank">Terms & Conditions.</a></span>
		                                </label>
	                                </div>

	                                <input type="hidden" id="id-g-firebase-auth-key" value="{{FIRE_BASE_AUTH_KEY}}"/>
	                                <div class="form-group">
	                                    <input type="button" id="company_register" class="btn btn-lg btn-primary btn-block" value="Sign up" name="send_email" />
	                                </div>
		                   	  		<a class="lnk-toggler" data-panel=".panel-login" href="#" style="display: inline-block; margin-top: -10px;">Already have an account?</a>
			                	</div>
			                	<div class="cols-sm-12 otp-screen hide" style="padding-top: 130px;">
			                		<div class="xserp-heading" style="padding-bottom: 0; margin-bottom: 20px;">
	                      		<div class="signup-header" style='margin-bottom: 40px;'>
			                				Verification
		              					</div>
		              					<div>
		              						We just sent you a verification code by text message to your registered mobile number.
		              					</div>
		              					<br /><br /><br />
		              					<div class="form-group wrap-input floating-label">
	                        		<div class="form-group wrap-input floating-label">
		                        		<input class="form-control floating-input" id="id_otp" maxlength="48" name="otp" onblur="return validateStringOnBlur(this, event, 'number');" onkeypress="return validateStringOnKeyPress(this,event, 'number');" placeholder=" " type="text">
		                        		<label>OTP *</label>
		                      		</div>
	                      		</div>
	                      		<div class="form-group">
                        			<input type="button" id="btn-opt" class="btn btn-lg btn-primary btn-block" value="Verify" name="verify-otp" />
                        			<br />
                        			<a role="button" class="resend-otp disabled-otp">Resend OTP <span id="counter-container"> in <span id="counter"></span></span></a>
                      			</div>
		                   		</div>
			                	</div>
			             	</div>
			          	</div>
				        <!-- ./panel-signup -->
				        <!-- panel-forget start -->
			          	<div class="xserp-panel panel-forgot text-center">
			             	<div class="row">
			                	<div class="col-xs-12 col-sm-12">
				                   	<div class="xserp-heading">
				                      	<div class="password-header">
				                			Forgot Password
				              			</div>
				                      	<p style="font-size: 16px; color: #888;">Don't worry! We just need your registered Email address to send the instruction to reset your password.</p>
				                   	</div>
				            		<div class="alert alert-success hide" id="reset_success" style="font-size: 16px; text-align: center;">
				              			<span>Success!</span> Reset password instruction has been sent to your email address.
				            		</div>
				            		<div class="alert alert-danger hide" id="reset_failure">
				              			<span>Error!</span> This Email Address doesn't match with our database. Please check again.
				            		</div>
			                     	<div class="form-group wrap-input floating-label">
			                         	<input type="text" class="form-control floating-input" id="id_forgot_email" name="id_forgot_email" placeholder=" ">
			                          	<label>EMAIL ADDRESS *</label>
			                      	</div>
			                      	<div class="form-group">
			                         	<button class="btn btn-lg btn-primary btn-block" id="password_reset" name="send_email" type="submit">Recover your password</button>
			                         	<div class="btn btn-lg btn-primary btn-block hide" id="loading_password_reset" style="letter-spacing: 0; cursor: not-allowed; transform: scale(1);">Please Wait<span>.</span><span>.</span><span>.</span></div>
			                      	</div>
			                      	<div class="form-group pull-left">
			                         	<a class="lnk-toggler" data-panel=".panel-login" href="#">Already have an account?</a>
			                      	</div>
			                      	<div class="form-group pull-right">
			                         	<a class="lnk-toggler" data-panel=".panel-signup" href="#">Don’t have an account?</a>
			                      	</div>
			                	</div>
			             	</div>
			          	</div>
			       	</div>
			    </div>
		  	</div> 
		</div>
    </div>
    <div id="recaptcha-container"></div>
	<div id="contact-form-modal" class="modal fade" role="dialog">
			<div class="modal-dialog" style="width: 700px;">
			<div class="modal-content" style="margin-top: 150px;">
  				<div class="modal-header">
    				<button type="button" class="close" data-dismiss="modal">&times;</button>
    				<h4 class="modal-title">Contact Us</h4>
  				</div>
  				<div class="modal-body">
    				<div class="form-group">
					  	<label for="contact_email_address">Email Address<span class="mandatory_mark"> *</span></label>
					  	<input type="text" class="form-control" id="contact_email_address" name="contact_email_address" maxlength="100" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');">
					</div>
					<div class="form-group">
					  	<label for="contact_query">Query<span class="mandatory_mark"> *</span></label>
					  	<textarea rows="3" style="max-height: 280px;" class="form-control" id="contact_query" class="contact_query" maxlength="500" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');"></textarea>
					</div>
  				</div>
  				<div class="modal-footer">
    				<button type="button" id="send_contact_mail_query" onclick="sendContactMailQuery()" class="btn btn-save">Send Mail</button>
    				<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
  				</div>
			</div>
		</div>
	</div>
    <!-- footer -->
    {% include "public/theme-footer.html" %}
    <script type="text/javascript" src="/site_media/js/jquery.js.download"></script>
    <script type="text/javascript" src="/site_media/js/login-menu.js?v={{ current_version }}"></script>
    <script src="https://www.gstatic.com/firebasejs/6.3.3/firebase-app.js"></script>
  	<script src="https://www.gstatic.com/firebasejs/6.3.3/firebase-auth.js"></script>
    <script type="text/javascript">
	    $(document).ready(function(){
	    	testimonialSliderInit();
			pageResolutoinSet();
			registrationResponse();
			var url = window.location.href;
			if(url.indexOf("signup") >= 0 ) {
				$(".btn-signup").trigger("click");
			}
			$(".main__menu").css({background: ""});
			localStorage.removeItem("mobileAlert");
			localStorage.removeItem('goods_service_master_data');
			localStorage.removeItem('project');
		});


		var firebaseConfig = JSON.parse($("#id-g-firebase-auth-key").val());
	    firebase.initializeApp(firebaseConfig);
	    window.recaptchaVerifier = new firebase.auth.RecaptchaVerifier(
	        "recaptcha-container",
	        {
	          size: "invisible"
	        }
      	);
      	recaptchaVerifier.render().then(function(widgetId) {
	        window.recaptchaWidgetId = widgetId;
      	});
	
		function sendContactMailQuery() {
			$(".error-border").removeClass('error-border');
		    $(".custom-error-message").remove();

		    var ControlCollections = [
		        {
		            controltype: 'textbox',
		            controlid: 'contact_email_address',
		            isrequired: true,
		            errormsg: 'Email Address is required.',
		            isemail: true,
  		  	        emailerrormsg: 'Invalid Email Address.'
		        },
		        {
		            controltype: 'textbox',
		            controlid: 'contact_query',
		            isrequired: true,
		            errormsg: 'Query is required.'
		        }
		    ];
		    var result = JSCustomValidator.JSvalidate(ControlCollections);

		    if (result) {
		    	$("#send_contact_mail_query").addClass("btn-theme-processing").text("Sending mail...");
				var email = $("#contact_email_address").val();
				var query = $("#contact_query").val();
				$.ajax({
					url: "/erp/auth/send_query_mail/",
					type: "post",
					data: {
						user_email: email, query: query
					},
					success: function(response) {
						if (response.response_message === "Success") {
             				$("#send_contact_mail_query").removeClass("btn-theme-processing").text("Send Mail");
							swal({
								title: "Mail Status",
								text: response.custom_message,
								type: "success",
								allowOutsideClick: false,
		                      	allowEscapeKey: false
							},
							function() {
								$("#contact_email_address").val("");
								$("#contact_query").val("");
								$("#contact-form-modal").find(".close").click()
							});
						} 
						else {
							swal({title: "", text: response.custom_message, type: "error"});
              				$("#send_contact_mail_query").removeClass("btn-theme-processing").text("Send Mail");
						}
					},
					error: function(xhr, errmsg, err) {
						console.log(xhr.status + ": " + xhr.responseText);
						$("#send_contact_mail_query").removeClass("btn-theme-processing").text("Send Mail");
					}
				});
			}
			else {
				$("#send_contact_mail_query").removeClass("btn-theme-processing").text("Send Mail");
			}
		}

		function submitPhoneNumberAuthCode() {
        	var code = $("#id_otp").val();
	        confirmationResult
          	.confirm(code)
          	.then(function(result) {
            	var user = result.user;
            	console.log(user);
            	console.log("CODE SUCCESS");
            	registerEnterprise();
          	})
          	.catch(function(error) {
            	console.log(error);
            	if(error.code == "auth/invalid-verification-code") {
            		var row = `<span class="custom-error-message invalid-otp">Incorrect OTP.</span>`;
            		$("#id_otp").addClass("error-border").next("label").after(row);
              		console.log("CODE FAILURE")
            	}
            	else if(error.code == "auth/code-expired") {
            		var row = `<span class="custom-error-message invalid-otp">OTP Expired. Try Again.</span>`;
            		$("#id_otp").addClass("error-border").next("label").after(row);
              		console.log("CODE FAILURE")
            	}
            	$("#btn-opt").removeClass("btn-theme-processing").val("Verify");
          	});
      	}


		function registerEnterprise(){
			$.ajax({
				url:"/erp/auth/register_enterprise/",
				type: "post",
				data:{
					enterprise_name:$("#id_enterprise-name").val(),
					firstname:$("#id_enterprise-firstname").val(),
					lastname:$("#id_enterprise-lastname").val(),
					email:$("#id_enterprise-email").val(),
					mobile:$("#id_enterprise-mobile").val(),
					password:$("#id_enterprise-password").val(),
				},
				success: function(response){
					if (response.response_code == 200) {
						swal({
							title: response.response_message,
							text: response.custom_message,
							type: "success",
							allowOutsideClick: false,
              allowEscapeKey: false
						},
						function() {
							window.location.replace("/erp/");
						});
						gtag('event', 'conversion', {'send_to': 'AW-*********/DLTICK-usMsCEJeN3NgC'});
					}
					else{
						swal({title: "", text: response.custom_message, type: "error"});
					}
				},
				error: function(xhr, errmsg, err){
					console.log(xhr.status + ": " + xhr.responseText);
				}
			});
		}

		function sendSignupMail(){
			$.ajax({
				url:"/erp/auth/send_sign_up_mail/",
				type: "post",
				data:{
					enterprise_name:$("#id_enterprise-name").val(),
					firstname:$("#id_enterprise-firstname").val(),
					lastname:$("#id_enterprise-lastname").val(),
					email:$("#id_enterprise-email").val(),
					mobile:$("#id_enterprise-mobile").val(),
				},
				success: function(response){
					if (response.response_code == 200) {
						console.log("Sign up initiated");
					}
					else{
						swal({title: "", text: response.custom_message, type: "error"});
					}
				},
				error: function(xhr, errmsg, err){
					console.log(xhr.status + ": " + xhr.responseText);
				}
			});
		}

		function submitPhoneNumberAuth() {
	        var phoneNumber = "+91" + $("#id_enterprise-mobile").val();
	        if(phoneNumber != "" && phoneNumber != undefined) {
	          var appVerifier = window.recaptchaVerifier;
	            firebase
	            .auth()
                .signInWithPhoneNumber(phoneNumber, appVerifier)
                .then(function(confirmationResult) {
                	window.confirmationResult = confirmationResult;
                	$(".registration-screen").addClass("hide");
		    					$(".otp-screen").removeClass("hide");
		    					startCounter();
		    					sendSignupMail();
		    					gtag('event', 'conversion', {'send_to': 'AW-*********/5AlFCMmVscsCEJeN3NgC'});
              	})
              	.catch(function(error) {
                	console.log("SMS FAILURE", error);
                	$("#company_register").removeClass("btn-theme-processing").val("Sign Up");
                	if(error.code == "auth/invalid-phone-number") {
                		var row = `<span class="custom-error-message invalid-phone-number">Invalid Mobile Number.</span>`;
                		$("#id_enterprise-mobile").addClass("error-border").next("label").after(row);
                	}
                	else if(error.code == "auth/too-many-requests") {
                  		swal("","You have tried too many attempts to register. <br />We have limited the registration from the same PC for a security purpose. Please try after some time.");
                	}
          		});
	        }
	        else {
	        	$("#company_register").removeClass("btn-theme-processing").val("Sign Up");
	        }
      	}

      	firebase.auth().onAuthStateChanged(function(user) {
	        if (user) {
	          //console.log("USER LOGGED IN");
	        } else {
	          // No user is signed in.
	          //console.log("USER NOT LOGGED IN");
	        }
      	});

      	function countdown(remaining) {
		    var m = Math.floor(remaining / 60);
		    var s = remaining % 60;
		    m = m < 10 ? '0' + m : m;
		  	s = s < 10 ? '0' + s : s;
			document.getElementById('counter').innerHTML = m + ':' + s;
			remaining -= 1;
			if(remaining >= 0) {
				setTimeout(function() {
			        countdown(remaining);
			    }, 1000);
			    return;
			}
			$("#counter-container").addClass("hide");
			$(".resend-otp").removeClass("disabled-otp")
			$(".resend-otp").attr("onclick", "grn()")
		}

		function startCounter(){
			$("#counter-container").removeClass("hide");
		    $(".resend-otp").addClass("disabled-otp")
		    $(".resend-otp").removeAttr("onclick");
		    countdown(120);
		}

		function testimonialSliderInit() {
		    pagenum = 1;
		    function AutoRotate() {
		        var myele = null;
		        var allElements = document.getElementsByClassName('slider-label');
		        for (var i = 0, n = allElements.length; i < n; i++) {
		            var myfor = allElements[i].getAttribute('for');
		            if ((myfor !== null) && (myfor == ('slide_2_' + pagenum))) {
		                allElements[i].click();
		                break;
		            }
		        }
		        if (pagenum == 5) {
		            pagenum = 1;
		        } else {
		            pagenum++;
		        }
		    }
		    $(".testimonial_slider_2").mouseenter(function() {
		        clearInterval(scroller)
		    })

		    $(".testimonial_slider_2").mouseleave(function() {
		        scroller = setInterval(AutoRotate, 5000);
		    })
		    var scroller = setInterval(AutoRotate, 5000);
		    setTimeout(function() {
		        AutoRotate()
		    }, 5500);
		    $('#contact-form-modal').on('hidden.bs.modal', function () {
			    $(".error-border").removeClass('error-border');
	    		$(".custom-error-message").remove();
			});
		}	

    function validateform() {
	    $(".error-border").removeClass('error-border');
	    $(".custom-error-message").remove();
	    var ControlCollections = [{
	            controltype: 'textbox',
	            controlid: 'id_user_email',
	            isrequired: true,
	            errormsg: 'Username is required.'
	        },
	        {
	            controltype: 'textbox',
	            controlid: 'id_password',
	            isrequired: true,
	            errormsg: 'Password is required.'
	        }
	    ];
	    var result = JSCustomValidator.JSvalidate(ControlCollections);
	    if(result) {
	    	$("#id_password").attr("type", "password");
	    }
	    return result;
	}

	$("#company_register").click(function() {
		$("#company_register").addClass("btn-theme-processing").val("Processing...");
	    $(".error-border").removeClass('error-border');
	    $(".custom-error-message").remove();
	    $(".recaptcha-error-message").addClass("hide");

	    var ControlCollections = [{
	            controltype: 'textbox',
	            controlid: 'id_enterprise-name',
	            isrequired: true,
	            errormsg: 'Required.'
	        },
	        {
	            controltype: 'textbox',
	            controlid: 'id_enterprise-firstname',
	            isrequired: true,
	            errormsg: 'Required.'
	        },
	        {
	            controltype: 'textbox',
	            controlid: 'id_enterprise-lastname',
	            isrequired: true,
	            errormsg: 'Required.'
	        },
	        {
	            controltype: 'textbox',
	            controlid: 'id_enterprise-email',
	            isrequired: true,
	            errormsg: 'Required.',
	            isemail: true,
	            emailerrormsg: 'Invalid Email Address.'
	        },
	        {
	            controltype: 'textbox',
	            controlid: 'id_enterprise-mobile',
	            isrequired: true,
	            errormsg: 'Required.',
	            minvalerrormsg: 'Contact Number should be atleast 10 Character.',
	            minvalue: 10
	        },
	        {
	            controltype: 'textbox',
	            controlid: 'id_enterprise-password',
	            isrequired: true,
	            errormsg: 'Required.',
	            minvalerrormsg: 'Minimum 6 Character.',
	            minvalue: 6
	        },
	        {
	            controltype: 'textbox',
	            controlid: 'id_enterprise-confirm-password',
	            isrequired: true,
	            errormsg: 'Required.',
	            minvalerrormsg: 'Minimum 6 Character.',
	            minvalue: 6
	        },
	        {
	            controltype: 'checkbox',
	            controlid: 'privacy_policy',
	            isrequired: true,
	            errormsg: 'Please read and accept the Terms & Conditions.'
	        }
	    ];
	    var result = JSCustomValidator.JSvalidate(ControlCollections);
	    if ($('#id_enterprise-password').val() != $('#id_enterprise-confirm-password').val()) {
				$('#id_enterprise-password, #id_enterprise-confirm-password').addClass('error-border');
				$("#id_enterprise-confirm-password").parent().find(".custom-error-message").addClass("hide");
				$("#id_enterprise-confirm-password").parent().append('<span class="custom-error-message">Not matched.</span>');
				result = false;
			}
	    if (result) {
    		var isDuplicateAccount = validateEmailDuplication($("#id_enterprise-email"));
    		if(isDuplicateAccount) {
    			swal("","The email address provided is already registered with us. Please try to login with this email address.","warning");
    			$("#company_register").removeClass("btn-theme-processing").val("Sign Up");
    		}
    		else {
		    	$("#id_enterprise-password, #id_enterprise-confirm-password").attr("type", "password");
		    	submitPhoneNumberAuth();
		    }
	    }
	    else {
	    	$("#company_register").removeClass("btn-theme-processing").val("Sign Up");
	    }
	});

	$("#btn-opt").click(function() {
		$("#btn-opt").addClass("btn-theme-processing").text("Processing...");
	    $(".error-border").removeClass('error-border');
	    $(".custom-error-message").remove();
	    var ControlCollections = [{
	            controltype: 'textbox',
	            controlid: 'id_otp',
	            isrequired: true,
	            errormsg: 'OTP is required.'
	        }
	    ];
	    var result = JSCustomValidator.JSvalidate(ControlCollections);
	    if(result) {
	    	submitPhoneNumberAuthCode();
	    	//Validate OTP and Submit registration form
	    	//$("#id_enterprise_registration").submit();
	    }
	    else {
	    	$("#btn-opt").removeClass("btn-theme-processing").text("Verify");
	    }
	});

	$("#password_reset").click(function() {
	    $(".error-border").removeClass('error-border');
	    $(".custom-error-message").remove();
	    var ControlCollections = [{
	        controltype: 'textbox',
	        controlid: 'id_forgot_email',
	        isrequired: true,
	        errormsg: 'Email Address is required.',
	        isemail: true,
	        emailerrormsg: 'Invalid Email Address.'
	    }];
	    var result = JSCustomValidator.JSvalidate(ControlCollections);
	    if (result) {
	        $("#loading_password_reset").removeClass('hide');
	        $("#password_reset").addClass('hide');
	        resetPassword($('#id_forgot_email').val());
	    }
	    return result;
	});

	function resetPassword(email) {
	    $("#reset_failure").addClass("hide");
	    $("#reset_success").addClass("hide");
	    $.ajax({
	        url: "/erp/auth/json/forget_password/",
	        type: "post",
	        data: {
	            user_email: email
	        },
	        success: function(response) {
	            if (response.response_message === "Success") {
	                $("#reset_success").html(response.custom_message);
	                $("#reset_success").removeClass("hide");
	                $("#loading_password_reset, .email_container, .reset_password_info").addClass('hide');
	                $("#id_forgot_email").closest("div").addClass("hide");
	            } else {
	                $("#reset_failure").html(response.custom_message);
	                $("#reset_failure").removeClass("hide");
	                $("#password_reset").removeClass('hide');
	                $("#loading_password_reset").addClass('hide');
	                $("#id_forgot_email").closest("div").removeClass("hide");
	            }
	        },
	        error: function(xhr, errmsg, err) {
	            console.log(xhr.status + ": " + xhr.responseText);
	        }
	    });
	}

	function gotoLogin() {
		$(".panel-forgot").removeClass("active");
		$(".panel-login").addClass("active");
	}

	function pageResolutoinSet() {
		setTimeout(function(){
			$(".testimonial-container").css({minHeight: $(".login-signup-container").height()+"px"})
		},150)
	}

	function registrationResponse(argument) {
	    $('#id_user_email').focus();
	    if ($("#id_submit_response").val() != "") {
	        var swalMessage = $("#id_submit_response").val();
	        var type = "success";
	        if (swalMessage.trim() != "") {
	            if (swalMessage == "Email address provided is already registered.") {
	                type = "warning";
	            } else if (swalMessage.indexOf("Failed") >= 0) {
	                type = "error";
	            }
	        }
	        swal({
	            title: "",
	            text: $("#id_submit_response").val(),
	            type: type
	        });
	    }
	    $("#id_submit_response").val("");
	}

	function validateEmailDuplication(current) {
		var enterprise_email = $(current).val();
		var isDuplicateAccount = false;
		var emailRegex = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
        var isEmailCorrect = emailRegex.test(enterprise_email);
        if (isEmailCorrect) {
			$.ajax({
		        url: "/erp/auth/validate_email/",
		        type: "post",
		        async: false,
		        data: {
		            enterprise_email: enterprise_email
		        },
		        success: function(response) {
		            if (response.response_code != 200) {
			            if(response.custom_message.length > 0) {
			            	var row = `<span class="custom-error-message duplicate-account">Already Registered.</span>`;
			            	$("#id_enterprise-email").next('label').after(row);
			            	$(current).addClass("error-border");
							console.log(response.custom_message);
							isDuplicateAccount = true;
			            }
				    }
				    else {
				    	$(current).removeClass("error-border");
				    	$(".duplicate-account").remove();
				    }	
		        }
		    });
		}
		else {
			$(current).removeClass("error-border");
			$(".duplicate-account").remove();
		}
		return isDuplicateAccount;
	}
</script>

</body>

</html>