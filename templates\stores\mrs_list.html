{% extends "stores/sidebar.html" %}
{% block indent %}

{% if logged_in_user|canView:'MATERIAL REQUEST' %}
<style>

	#cattable tr[data-toggle='open'] td:nth-child(8){
		opacity: 0.5;
		background: #ddd;
		pointer-events: none;
	}

	#cattable tr[data-toggle='open'] input,
	#cattable tr[data-toggle='open'] select	{
		opacity: 0.5;
		background: #ddd;
	}

	.side-content.div-disabled {
		padding: 0;
		background-color: transparent !important;
	}

	.side-content.div-disabled a,
	.side-content.div-disabled div,
	.side-content.div-disabled input {
		border: none;
		outline: none;
		box-shadow: 0 0;
		height: 30px;
		padding-left: 3px;
		background-color: transparent !important;
		font-size: 13px;
		color: #333;
	}

	.side-content.div-disabled input {
		padding-left: 6px;
	}

	.side-content.div-disabled .chosen-single div {
		display: none;
	}

	.side-content.div-disabled .chosen-single span {
		margin-top: 2px;
	}

	.table.text_box_in_table .chosen-single {
		height: 26px;
	    font-size: 13px;
	    line-height: 25px;
	    padding: 0 0 0 3px;
	}

	.table.text_box_in_table .chosen-container-single .chosen-single div b {
		margin-top: 2px;
	}

	.supplier_total_amt {
		font-size: 20px;
		float: right;
		margin-top: 15px;
	}

	.parent-supplier-container {
	    padding: 6px 12px;
	    border: solid 1px #ccc;
	    border-top: none;
	    margin-bottom: 15px;
	}

	.supplier_name_tab a{
		color:  #000;
		font-size: 16px;
	}

	.chosen-select[readonly='readonly']+div  {
		pointer-events: none;
	}

	.supplier_type_select {
		width:  100%;
		border-radius: 4px;
    	border-color: #ccc;
	}

	.disabled_material td {
		background: rgba(255,0,0,0.15);
		opacity: 0.5;
		pointer-events: none;
		color:  rgba(255,0,0,0.7) !important;
	}

	.disabled_material input {
		background: transparent !important;
		border-color: rgba(255,0,0,0.3);
    	color:  rgba(255,0,0,0.7) !important;
	}

	.disabled_material td:last-child {
		background: transparent;
		opacity: 1;
		pointer-events: inherit;
		color: #000 !important;
	}

	.td_for_icon .fa.fa-ban,
	.td_for_icon .fa.fa-plus {
		font-size: 18px;
	    margin-top: 3px;
	}

	.side-content .custom-error-message {
		margin-top: -24px;
    	right: 26px;
	}

	.error-duplicate-supplier {
		background: rgba(249, 255, 81,0.35);
	}

	.error-duplicate-supplier .chosen-container {
		box-shadow: 0 0;
	}

	.error-duplicate-supplier input,
	.error-duplicate-supplier select,
	.error-duplicate-supplier a span{
		background: rgba(221, 75, 57,0.00);
	}

	.add_split_container .chosen-disabled {
		opacity: 1 !important;
	    box-shadow: 0 0;
	}

	.add_split_container .chosen-disabled a {
		border:  none;
	}

	.add_split_container .chosen-disabled b {
		display: none !important;
	}

	.add_split_container select[disabled] {
		-webkit-appearance: none;
	    -moz-appearance: none;
	    text-indent: 1px;
	    text-overflow: '';
	    background: transparent;
	}

	.loading-main-container {
		display: block;
	}
	.table-inline-icon-container-list {
		position: absolute;
		margin-top:  -8px;
		float: left;
		display: block
	}

</style>

<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/utility.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/tags.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/mrs.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/load_material.js?v={{ current_version }}"></script>

<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">Material Requisition</span>
	</div>
	<div class="page-heading_new" style="padding: 0 30px;">
		<input type="hidden" id="id_material-material_id" />
		<input type="hidden" id="id_material-enterprise_id" />
			{% if logged_in_user|canEdit:'MATERIAL REQUEST' %}
				<a href="/erp/stores/material_requisition/" role="button" class="btn btn-new-item pull-right" data-tooltip="tooltip" title="Create New MRS" style="position: absolute; right: 85px; margin-top: 10px; z-index: 10;"><i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span></a>
			{% else %}
				<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in MRS Module. Please contact the administrator." style="position: absolute; right: 85px; margin-top: 5px; z-index: 10;">
					<a class="btn btn-new-item pull-right disabled">
						<i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span>
					</a>
				</div>
			{% endif %}
			<a role="button" href="/erp/stores/material_requsition_list/" class="btn btn-add-new pull-right hide" data-tooltip="tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
		<span class="prev_next_container"></span>
	</div>
	<div class="container-fluid" id="container-fluid">
		<div>
			<div class="col-lg-12">
				<div class="content_bg">
					<div class="tab-content">
						<div>
							<div class="row">
								<div class="filter-components">
									<div class="filter-components-container">
										<div class="dropdown">
											<button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
												<i class="fa fa-filter"></i>
											</button>
											<span class="dropdown-menu arrow_box arrow_box_filter">
												<form action="/erp/stores/material_requsition_list/" method="post">{% csrf_token %}
													<div class="col-sm-12 form-group" >
														<label>Date Range</label>
														<div id="reportrange" class="report-range form-control">
															<i class="glyphicon glyphicon-calendar"></i>&nbsp;
															<span></span> <b class="caret"></b>
															<input type="hidden" class="fromdate" id="fromdate" name="fromdate" value="{{filter.from_date|date:'Y-m-d'}}" />
															<input type="hidden" class="todate" id="todate" name="todate" value="{{filter.to_date|date:'Y-m-d'}}"/>
														</div>
													</div>
													<div class="col-sm-12 form-group">
														<label>Project</label>
														<select class="form-control chosen-select" name="projects" id="projectmain">
															<option value=0 data-id=0>ALL</option>
															{% for j in project_choices%}
															{% if j.0 == filter.project_id %}
																<option value="{{ j.0 }}" selected="selected">{{ j.1 }}</option>
																{% else %}
														    <option value="{{ j|lower }}" data-id={{ j.0 }}>{{ j.1 }}</option>
															{% endif  %}
															{% endfor %}
														</select>
													</div>
													<div class="filter-footer">
														<input type="submit" class="btn btn-save search_button" value="Apply" id="id-searchMRS"/>
													</div>
												</form>
											</span>
										</div>
										<span class='filtered-condition filtered-date'>Date: <b></b></span>
										<span class='filtered-condition filtered-project'>Project/Tag: <b></b></span>
									</div>
								</div>
								<div class="col-sm-12">

										<div class="csv_export_button">
											<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#MrsList'), 'Material_Requisition_List.csv']);" data-tooltip="tooltip" title="Download MRS List as CSV">
												<i class="fa fa-download" aria-hidden="true"></i>
											</a>
										</div>

									<table class="table table-bordered custom-table table-striped" id="MrsList" style="width: 100%;">
										<thead>
											<tr>
												<th style="min-width: 70px; max-width: 70px;"> S.No</th>
												<th width="15%"> MRS No</th>
												<th width="15"> Project</th>
												<th width="15%"> Requisition By </th>
												<th width="15%"> Date</th>
<!--												<th width="17%" hidden="hidden"> Type</th>-->
												<th width="16%"> Purpose</th>
												<th width="15%"> Status</th>
											</tr>
										</thead>
										<tbody id="issue_table_list_tbody">
											{% for mrs in mrs_list %}
												<tr bgcolor="#ececec" align="center">
													<td align="center">
														<span>{{ forloop.counter }}.</span>
													</td>
													<td class='text-center'>
														<a class="edit_link_code" data-MrsId="{{ mrs.mrs_id }}" onclick="editMRSRow('{{ mrs.mrs_id }}', '{{edit_link}}');">
															{{ mrs.code }}
														</a>
														<input type="hidden" value="{{ mrs_id }}"  id="mrs_id" name="mrs_no"/>
													</td>
													<td class='text-center'> {{ mrs.project_name }} </td>
													<td class='text-center'> {{ mrs.requisition_by }} </td>
													<td class='text-center'> {{ mrs.prepared_on|date:"M d, Y H:i:s" }} </td>
<!--													<td class='text-left' hidden="hidden"> {{ mrs.type }} </td>-->
													<td class='text-left' style='max-width: 100px; word-wrap: break-word;'> {{ mrs.purpose }} </td>
													<td align="center" class="td_status" style="padding: 0;">
														<a role="button" class="table-inline-icon-bg {% if mrs.status == 1 %}  approved {% elif mrs.status == -2 %} rejected {% else %} draft {% endif %}" >
															{% if mrs.status == 1 %} Approved {% elif mrs.status == -2 %} Rejected {% else %} Draft {% endif %}
														</a>
														<span class="table-inline-icon hide pdf_genereate" data-tooltip="tooltip" data-placement="left" title="" onclick="javascript:generate_pdf_ajax({{mrs.mrs_id}},{{mrs.status}});" data-original-title="Preview">
															<i class="fa fa-file-text"></i>
														</span>
														<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='right' title='Edit' role='button' onclick="editMRSRow('{{ mrs.mrs_id }}', '{{edit_link}}');">
															 <i class='fa fa-pencil'></i>
														</span>
														<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='right' title='Edit in New Tab' role='button' onclick="editMRSRow('{{ mrs.mrs_id }}', '{{edit_link}}', '_blank');">
															 <i class='fa fa-external-link'></i>
														</span>
													</td>
												</tr>
											{% endfor %}
										</tbody>
									</table>
								</div>
							</div>
							<div class="clearfix"></div>
						</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


{% include "attachment_popup.html" %}
<div id="mrs_document_modal" class="modal fade" role="dialog">
  	<div class="modal-dialog modal-lg">
		<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Preview Document</h4>
      		</div>
      		<div class="modal-body text-center">
				<input id="modal_mrs_id" name="mrs_id" value='' hidden="hidden"/>
		        <input id="modal_selected_mrs" name="mod_selected_supplier" value='' hidden="hidden"/>
		        <input id="modal_received_against" name="mod_received_against" value='' hidden="hidden"/>
      			<div class="row" style="text-align: left;">
					<div class="col-lg-12">
						<div class="content_bg">
							<div class="add_table">
								<form id="pdf_generation">{% csrf_token %}
									<input type="submit" hidden="hidden" value="Approve/Reject" id="view_receipt_document"/>
									<input type="hidden" name="receipt_no" id="id_receipt_no" value="{{receipt_no}}" />
									<div class="col-sm-6" style="padding-left: 0;">
										{%if access_level.edit or access_level.approve%}
											<input id="remarks" class="form-control" enterkey="do_nothing" maxlength="300" placeholder="Approval/Rejection Remarks" />
										{% endif %}
								       	<div class="remarks_count_link remarks_count disabled" onclick="remarksInSwal('mrs_document_remarks');">
											<span class="remarks_counter">No</span><span> remarks</span>
										</div>
								   	</div>
								   	<div id="mrs_doc_btn">
										{% if access_level.approve %}
										<span class="material_txt">
											<a role='button' id='approve_mrs' class="btn btn-save" onclick="approveMrs();">Approve</a>
										</span>
									    {% endif %}
									    {% if access_level.approve %}
											<a role='button' id='reject_mrs' class="btn btn-danger for_mrs_approve" onclick="rejectMrs();">Reject</a>
										{% else %}
										{%if access_level.edit %}
											<a role='button' id='reject_mrs' class="btn btn-danger for_mrs_edit" onclick="rejectMrs();">Discard</a>
										{% endif %}
										{% endif %}
									</div>
<!--									<div class="grn_icd_status" style="font-size: 16px; color: #28a745; float: right; margin-right: 30px;"></div>-->
								</form>
							</div>
						</div>
					</div>
				</div>
				<input type="hidden" id="mrs_document_remarks" />
				<div id="mrs_document_container"></div>
  			</div>
  			<div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
  		</div>
  	</div>
</div>

<div class="hide">
    <form id="edit_mrs_form" action="" method="post" >
       {% csrf_token %}
       <input type="hidden" id="edit_mrs_id" name="mrs_no" value="" />
    </form>
</div>
{% include "masters/add_material_modal.html" %}


<script type="text/javascript">
	$(window).load(function(){

    var oTable;
	var oSettings;
	var hash = window.location.hash;
	hash && $('ul.nav a[href="' + hash + '"]').tab('show');

	$('.nav-tabs a').click(function (e) {
		$(this).tab('show');
		var scrollmem = $('body').scrollTop() || $('html').scrollTop();
		window.location.hash = this.hash;
		$('html,body').scrollTop(scrollmem);
	});

	var url = window.location.href;
	TableHeaderFixed();
	NavTableRemove();

		updateFilterText();
		$("#loading").hide();
	});

	function updateFilterText() {
		$(".filtered-date b").text($("#reportrange").find("span").text());
		$(".filtered-project b").text($("#projectmain option:selected").text());
	}


</script>
{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
{% endblock %}
