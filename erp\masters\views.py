import datetime
from operator import or_
import simplejson
from django.http import HttpResponse
from django.template.response import TemplateResponse
from erp.admin.backend import UserService
from erp.auth import USER_IN_SESSION_KEY, ENTERPRISE_ID_SESSION_KEY, PRIMARY_ENTERPRISE_ID_SESSION_KEY, \
	ENTERPRISE_IN_SESSION_KEY
from erp.auth.request_handler import Request<PERSON>and<PERSON>
from erp.dao import executeQuery
from erp.helper import populateProjectChoices, populateUnitChoices, constructDifferentMakeName, getProjectByProjectId, \
	getProjectByProjectCode, getProjectByProjectNameAndParentId, validate_payload, CustomJSONEncoder, getStateList,\
	model_to_dict
from erp.masters import logger
from erp.masters.backend import MasterDAO, MasterService, LocationService, MrpService
from erp.masters.changelog import MaterialChangelog
from erp.models import Project, Enterprise, LocationMaster
from erp.properties import MANAGE_MASTERS_TEMPLATE, TEMPLATE_TITLE_KEY
from settings import SQLASession
from util.api_util import response_code
from erp.auth.backend import LoginService
import json

__author__ = 'saravanan'


def manageMasters(request):
	"""

	:param request:
	:return:
	"""
	return TemplateResponse(template=MANAGE_MASTERS_TEMPLATE, request=request, context={})


def populateProjectSelectChoices(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	logger.info("Populating Project Select box choices for Enterprise - %s" % enterprise_id)
	project_choices = populateProjectChoices(need_blank_first=True, enterprise_id=enterprise_id)
	return HttpResponse(content=simplejson.dumps(project_choices), mimetype="application/json")


def constructUnitChoices(request):
	"""
	
	:param request:
	:return:
	"""
	logger.info("Constructing Unit Choices...")
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	data = simplejson.loads(simplejson.dumps(request_handler.getPostData()))
	unit_choices = populateUnitChoices(
		enterprise_id=enterprise_id, chosen_unit_id=data["unit_id"] if "unit_id" in data else None)
	logger.debug("Units - %s" % unit_choices)
	return HttpResponse(content=simplejson.dumps(unit_choices), mimetype='application/json')


def saveProject(request):
	logger.info("Saving project  ...")
	db_session = SQLASession()
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		project_code = request.POST.get('project_code')
		project_name = request.POST.get('project_name')
		is_active = 1 if request.POST.get('is_active') == "true" else 0
		db_session.begin(subtransactions=True)
		existing_project = db_session.query(Project).filter(
			Project.name == project_name, Project.enterprise_id == enterprise_id).first()
		if existing_project is None:
			if project_code is None or project_code == "":
				project = Project(name=project_name, enterprise_id=enterprise_id, is_active=1)
				db_session.add(project)
				db_session.commit()
				db_session.refresh(project)
			else:
				db_session.query(Project).filter(Project.id == project_code).update(
					{Project.id: project_code, Project.name: project_name, Project.is_active: is_active}, synchronize_session='fetch')
				db_session.commit()
			response = response_code.success()
			response["custom_message"] = 'Project Added Successfully' if project_code is None or project_code == "" else 'Project Updated Successfully'
			response["code"] = project_code if project_code is not None and project_code != "" else project.id
			response["name"] = project_name
			response["is_active"] = is_active
		else:
			if project_code is None or project_code == "" or (int(project_code) != int(existing_project.id)):
				response = response_code.failure()
				response["custom_message"] = 'Project name already exist'
				response["name"] = project_name
			else:
				db_session.query(Project).filter(Project.id == project_code).update(
					{Project.id: project_code, Project.name: project_name, Project.is_active: is_active}, synchronize_session='fetch')
				db_session.commit()
				response = response_code.success()
				response["custom_message"] = 'Project Updated Successfully'
				response["code"] = project_code
				response["name"] = project_name
				response["is_active"] = is_active
	except Exception as e:
		db_session.rollback()
		logger.exception(e.message)
		response = response_code.internalError()
		response['custom_message'] = "%s" % e.message
	return HttpResponse(simplejson.dumps(response), 'content-type=text/json')


def createNewProject(request):
	logger.info("Create New Project...")
	db_session = SQLASession()
	try:
		request_handler = RequestHandler(request)
		primary_enterprise_id = request_handler.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
		primary_enterprise = request_handler.getSessionAttribute(ENTERPRISE_IN_SESSION_KEY)
		project_code = request_handler.getPostData('project_code')
		project_name = request_handler.getPostData('project_name')
		project_description = request_handler.getPostData('description')
		parent_project_id = request_handler.getPostData('parent_project_id')
		project_by_code = getProjectByProjectCode(project_code=str(project_code), enterprise_id=primary_enterprise_id)
		project_by_name = getProjectByProjectNameAndParentId(project_name=project_name, parent_id=parent_project_id)

		if project_by_code or project_by_name:
			response = response_code.failure()
			response['custom_message'] = "Project %s Already Exist" %("Code" if project_by_code else "Name")
		elif not parent_project_id or not getProjectByProjectId(project_id=parent_project_id):
			response = response_code.paramMissing()
			response['custom_message'] = "Invalid Parent Project Id"
		else:
			enterprise_email = project_code + "@gmail.com"
			login_service = LoginService()
			register_enterprise, register_response = login_service.registerEnterprise(
				enterprise_name=project_name, first_name=project_name, last_name="",
				enterprise_email=enterprise_email, enterprise_mobile="", password="")
			if register_response:
				enterprise = db_session.query(Enterprise).filter(Enterprise.email == enterprise_email).first()
				enterprise.parent_enterprise_id = primary_enterprise_id
				enterprise.parent_enterprise_code = primary_enterprise.code
				enterprise.type = 'Project'
				enterprise.setting_flags = 27
				#Update Parent Enterprise Details
				db_session.begin(subtransactions=True)
				db_session.add(enterprise)
				db_session.commit()
				logger.info("Project Creation for %s Enterprise" % enterprise.id)

				db_session.begin(subtransactions=True)
				project_to_be_saved = Project(
					name=project_name, code=project_code, project_description=str(project_description),
					project_currency="INR", enterprise_id=primary_enterprise_id, project_enterprise_id=enterprise.id,
					parent_id=parent_project_id, is_active=True)
				db_session.add(project_to_be_saved)
				db_session.commit()
				logger.info("Successfully created a new project - %s(%s)" % (project_name, project_code))
				response = response_code.success()
			else:
				response = response_code.failure()
				response['custom_message'] = "Project Enterprise Creation Failed"

	except Exception as e:
		db_session.rollback()
		logger.exception(str(e))
		response = response_code.internalError()
		response['custom_message'] = str(e)

	return HttpResponse(json.dumps(response), content_type='text/json')


def getMaterialLogList(request):
	"""

	:param request:
	:return:
	"""
	response = {}
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	material_id = request_handler.getPostData('material_id')
	offset = request_handler.getPostData('offset')
	limit = request_handler.getPostData('limit')
	material_type = True if (request_handler.getPostData('material_type') == "goods") else False
	try:
		response = MaterialChangelog(material_type).fetchLogList(
			material_id=material_id, enterprise_id=enterprise_id, offset=offset, limit=limit)
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getMaterialLogData(request):
	"""
	get changelog data from a specified voucher timeset
	:param request:
	:return:
	"""
	response = {}
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	material_id = request_handler.getPostData('material_id')
	modified_at = request_handler.getPostData('modified_at')
	material_type = True if (request_handler.getPostData('material_type') == "goods") else False
	try:
		response = MaterialChangelog(material_type).fetchLogData(
			material_id=material_id, enterprise_id=enterprise_id, modified_at=modified_at)
	except Exception as e:
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getCheapestSupplierBOM(request):
	"""
	Get the cheapest price for BOM of materials
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	material_id = request_handler.getPostData('material_id')
	item = MasterDAO().getMaterialByItemId(item_id=material_id, enterprise_id=enterprise_id)
	cat_code = item.material_id
	response = []
	#Make to be removed in future
	query = """SELECT * FROM (SELECT					    
					m.drawing_no,
					m.name,
					IFNULL(mk.make_name, '-NA-'),
					m.makes_json,
					um.unit_name,
					cm.qty,
					IFNULL(pm.party_name, '-NA-'),
					cur.currency_name currency_name,
					IFNULL(smp.price, IFNULL(m.price, 0)),
					m.is_stocked as is_stock,										    
					cur.code code,
					cm.item_id,
					IFNULL(smp.make_id, 0),
					IFNULL(m.is_service, 0) as is_service												    
				FROM
					cat_materials cm
						LEFT JOIN 
					catalogue_material_makes cmm ON cmm.item_id = cm.item_id
							AND cmm.parent_id = cm.parent_id 
							AND cmm.enterprise_id = cm.enterprise_id
						LEFT JOIN
					make mk ON cmm.make_id = mk.id
							AND cmm.enterprise_id = mk.enterprise_id
						LEFT JOIN (SELECT * FROM recent_valid_price_profiles  WHERE status = 1 
							AND effect_since <= CURDATE() AND (effect_till IS NULL OR effect_till >= CURDATE())
							GROUP BY item_id, make_id ORDER BY price asc) smp ON cm.item_id = smp.item_id 
							AND cmm.make_id = smp.make_id 
							AND cm.enterprise_id = smp.enterprise_id 
						LEFT JOIN 
					materials m ON cm.item_id = m.id 
							AND cm.enterprise_id = m.enterprise_id 
						LEFT JOIN 
					unit_master um ON um.unit_id = m.unit 
							AND um.enterprise_id = m.enterprise_id 
						LEFT JOIN 
					party_master pm ON pm.party_id = smp.supp_id 
							AND pm.enterprise_id = smp.enterprise_id 
						LEFT JOIN 
					enterprise e ON e.id = cm.enterprise_id 
						LEFT JOIN 
					currency cur ON cur.id = pm.currency 		
						WHERE 
						cm.parent_id = '%s' AND cm.enterprise_id = %s group by cm.item_id, smp.make_id
						ORDER BY cm.item_id ASC , smp.make_id DESC, smp.price ASC , smp.effect_since DESC) AS CHEAPCOSTTBL
		               GROUP BY item_id
						""" % (cat_code, enterprise_id)
	try:
		for material in executeQuery(query):
			item_id = material[11]
			material_pricing = MasterDAO().material_pricing(item_id=item_id, enterprise_id=enterprise_id)
			if material_pricing :
				purchase_price = float(material_pricing[0]) if material_pricing[0] else "-NA-"
				purchase_currency_id=material_pricing[1] if material_pricing[1] else "-NA-"
				approved_price = float(material_pricing[2]) if material_pricing[2] else "-NA-"
				approved_currency_id = material_pricing[3] if material_pricing[3] else "-NA-"
				supplier_id = material_pricing[4] if material_pricing[4] else "-NA-"
				stored_price = float(material_pricing[5]) if material_pricing[5] else "-NA-"
				supplier_name = "-NA-"
				if supplier_id != "-NA-":
					supplier_result = MasterDAO().get_supplier_name(supplier_id=supplier_id)
					supplier_name = supplier_result[0] if supplier_result else None
				purchase_currency_code = "-NA-"
				if purchase_currency_id != "-NA-":
					currency_result = MasterDAO().get_currency_name(currency_id=purchase_currency_id)
					purchase_currency_code = currency_result[0] if currency_result else  None
				approved_currency_code = "-NA-"
				if approved_currency_id != "-NA-":
					currency_result = MasterDAO().get_currency_name(currency_id=approved_currency_id)
					approved_currency_code = currency_result[0] if currency_result else  None

				catalogues_child = True if MasterDAO().getParentByItemId(item_id, enterprise_id) else False
				make_name = constructDifferentMakeName(material[3])
				material_name = material[1] + " [" + make_name + "]" if make_name else material[1]
				response.append({
					'drawing_no': material[0], 'name': material_name, 'make': material[2],
					'unit': material[4], 'qty': material[5], 'supplier': supplier_name,'stored_price': stored_price,
					'is_stock': material[9], "stored_currency_code": 'INR', "item_id": material[11],"cat_code": cat_code,
					"hasChildren": catalogues_child, 'is_service': material[13], 'approved_price': approved_price,
					'approved_currency_code':approved_currency_code, 'purchase_material_price':purchase_price,
					'purchase_currency_code':purchase_currency_code})
	except Exception as e:
		logger.exception('%s' % e)
		response = ""
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getBomParenId(request):
	"""
	Get the parent_id for BOM of materials
	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		item_id = request_handler.getPostData('material_id')
		service = MasterService()
		bom_materials_list = []
		response = service.recursive_bom_material_list(
			item_id=item_id, enterprise_id=enterprise_id, bom_materials_list=bom_materials_list)

	except Exception as e:
		logger.exception('%s' % e)
		response = ""
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def get_material_catalogue(request):
	response = {"Status_code": 200, "response_message": "catalogue material fetched successfully"}
	rh = RequestHandler(request=request)
	master_sevice = MasterService()
	primary_enterprise_id = int(rh.getData('enterprise_id'))
	cat_code = int(rh.getData('cat_code'))
	cat_item_qty = int(rh.getData('cat_item_qty'))
	depth = int(rh.getData('depth'))
	try:
		materials = master_sevice.get_catalogue_materials(enterprise_id=primary_enterprise_id, cat_code=cat_code,
												 			cat_item_qty=cat_item_qty, depth=depth)
		response["materials"] = materials
		return HttpResponse(json.dumps(response), status=200, mimetype='application/json')
	except Exception as e:
		logger.info("Failed to get the Catalogue Material: %s", e)
		response = {"Status_code": 500, "response_message": "Internal Server Error"}
		return HttpResponse(json.dumps(response), status=500, mimetype='application/json')


@validate_payload([
	'enterprise_id', 'name', 'phone_no', 'email',
	'city', 'state', 'country', 'pin_code', 'address1', 'code',
])
def create_location(request):
	"""
    The view is used to create the location
    """
	response = response_code.internalError()
	rh = RequestHandler(request=request)
	# Gather data from the request
	location_data = {
		'id': rh.getData('id'),
		'enterprise_id': int(rh.getData('enterprise_id')),
		'name': rh.getData('name'),
		'contact_person': rh.getData("contact_person"),
		'phone_no': rh.getData('phone_no'),
		'email': rh.getData('email'),
		'city': rh.getData('city'),
		'state': rh.getData('state'),
		'country': rh.getData('country'),
		'pin_code': rh.getData('pin_code'),
		'fax': rh.getData('fax'),
		'gst_label': rh.getData('gst_label'),
		'address1': rh.getData('address1'),
		'address2': rh.getData('address2'),
		'code': rh.getData('code'),
		'is_default': int(rh.getData('is_default') if rh.getData('is_default') else 0),
		'last_modified_by': rh.getData('last_modified_by'),
	}
	location_id = int(location_data['id']) if location_data['id'] and not location_data['id'] == "None" else None
	db_session = SQLASession()
	try:
		set_new_default_location = LocationService().get_default_location(enterprise_id=location_data['enterprise_id'])
		if set_new_default_location is None:
			location_data['is_default'] = 1
		if location_data["is_default"] == 1:
			LocationService().update_default_location(enterprise_id=location_data['enterprise_id'])
		if location_id:
			is_more_than_one = db_session.query(LocationMaster).filter(or_(LocationMaster.name == location_data['name'],
				LocationMaster.code == location_data['code']),
				LocationMaster.enterprise_id == location_data['enterprise_id'],
				LocationMaster.id != location_id).count()
			if is_more_than_one > 0:
				response = response_code.alreadyExists()
				return HttpResponse(json.dumps(response), status=response["response_code"], content_type='application/json')
			location_to_be_saved = db_session.query(LocationMaster).filter_by(
				id=location_id, enterprise_id=location_data['enterprise_id']).first()
			if location_to_be_saved:
				# Update existing location
				for key, value in location_data.items():
					if key != 'id':
						setattr(location_to_be_saved, key, value)
				location_to_be_saved.last_modified_on = datetime.datetime.now()
		else:
			# Check if location already exists
			location_is_exists = db_session.query(LocationMaster).filter(
				LocationMaster.enterprise_id == location_data['enterprise_id'],
				(LocationMaster.name == location_data['name']) | (LocationMaster.code == location_data['code'])
			).all()
			if not location_is_exists:
				location_to_be_saved = LocationMaster(
					enterprise_id=location_data['enterprise_id'],
					name=location_data['name'],
					contact_person=location_data['contact_person'],
					phone_no=location_data['phone_no'],
					email=location_data['email'],
					city=location_data['city'],
					state=location_data['state'],
					country=location_data['country'],
					pin_code=location_data['pin_code'],
					fax=location_data['fax'],
					gst_label=location_data['gst_label'],
					address1=location_data['address1'],
					address2=location_data['address2'],
					code=location_data['code'],
					is_default=location_data['is_default'],
					last_modified_by=location_data['last_modified_by'],
					created_on=datetime.datetime.now(),
				)
			else:
				response = response_code.alreadyExists()
				return HttpResponse(json.dumps(response), status=response["response_code"],
									content_type='application/json')
		if location_to_be_saved:
			db_session.begin(subtransactions=True)
			db_session.add(location_to_be_saved)
			db_session.commit()
			response = response_code.success()
	except Exception as e:
		db_session.rollback()
		logger.info("Location creation failed: %s", e.message)
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), status=response["response_code"], content_type='application/json')


@validate_payload(['enterprise_id', 'user_id'])
def fetch_location(request):
	"""
	The Function fetch all the locations for the given enterprise_id
	"""
	rh = RequestHandler(request=request)
	try:
		enterprise_id = int(rh.getData('enterprise_id'))
		user_id = int(rh.getData('user_id'))
		user_service = UserService()
		location_service = LocationService()
		if not user_service.is_super_user(user_id=user_id, enterprise_id=enterprise_id):
			from_location_list = location_service.get_user_locations(enterprise_id=enterprise_id, user_id=user_id)
			to_location_list = location_service.get_all_locations(enterprise_id=enterprise_id)
		else:
			from_location_list = location_service.get_all_locations(enterprise_id=enterprise_id)
			to_location_list = location_service.get_all_locations(enterprise_id=enterprise_id)
		response = response_code.success()
		response["from"] = []
		response["to"] = []
		for location in from_location_list:
			response["from"].append(model_to_dict(location))
		for location in to_location_list:
			response["to"].append(model_to_dict(location))
	except Exception as e:
		logger.info("Failed to fetch the location %s", e.message)
		response = response_code.internalError()
	return HttpResponse(json.dumps(response, cls=CustomJSONEncoder),
						status=response["response_code"], content_type='application/json')


def create_location_page(request):
	fields = [
		'location_id', 'enterprise_id', 'name', 'contact_person', 'phone_no',
		'email', 'city', 'state', 'country', 'pin_code', 'fax',
		'gst_label', 'address1', 'address2', 'code','is_default'
	]

	location_details = {field: request.POST.get(field) for field in fields}

	context = {
		TEMPLATE_TITLE_KEY: "Location",
		"location_details": location_details
	}

	return TemplateResponse(request, 'masters/location.html', context)


# @validate_payload(['enterprise_id', 'user_id'])
def location_listing_page(request):
	rh = RequestHandler(request=request)
	data = []
	try:
		enterprise_id = int(rh.getData('enterprise_id'))
		user_id = int(rh.getData('user_id'))
		user_service = UserService()
		location_service = LocationService()
		if not user_service.is_super_user(user_id=user_id, enterprise_id=enterprise_id):
			location_list = location_service.get_user_locations(enterprise_id=enterprise_id, user_id=user_id)
		else:
			location_list = location_service.get_all_locations(enterprise_id=enterprise_id)
		for location in location_list:
			data.append(model_to_dict(location))
	except Exception as e:
		logger.info("Failed to render the Location rendering page %s", e)
	return TemplateResponse(template='masters/location_list.html', request=request, context={
		"data": data,
		TEMPLATE_TITLE_KEY: "Locations"})


def get_countries_list(request):
	"""
	Get the List of countries
	"""
	gst_country_list = []
	try:
		master_service = MasterService()
		countries_list = master_service.getCountries()
		for country in countries_list:
			gst_country_list.append({"country_code": country.code, "country_name": country.name})
		response = response_code.success()
		response["data"] = gst_country_list
	except Exception as e:
		logger.info("Failed to fetch the countries list %s", e)
		response = response_code.internalError()
	return HttpResponse(json.dumps(response, cls=CustomJSONEncoder), status=response["response_code"],
						content_type='application/json')


def get_state_list(request):
	"""
	Get the List of states
	"""
	request_handler = RequestHandler(request)
	try:
		country_code = request_handler.getData("country_code")
		state_list = getStateList(country_code=country_code)
		response = response_code.success()
		response["data"] = sorted(state_list, key=lambda a: a['description'], reverse=False)
	except Exception as e:
		logger.info("Failed to fetch the countries list %s", e)
		response = response_code.internalError()
	return HttpResponse(json.dumps(response, cls=CustomJSONEncoder), status=response["response_code"],
						content_type='application/json')

def  bom_checking_from_mrp(request):
	rh = RequestHandler(request)
	enterprise_id = int(rh.getData("enterprise_id"))
	item_id=int(rh.getData("item_id"))
	pp_id=int(rh.getData("pp_id"))
	parent_id=int(rh.getData("parent_id"))
	return_value = MrpService.bom_checking_from_mrp_materials(enterprise_id=enterprise_id, item_id=item_id, pp_id=pp_id,
															  parent_id=parent_id)
	return return_value