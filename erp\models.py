import time
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from decimal import Decimal, ROUND_HALF_UP
from django.contrib.auth.hashers import make_password, check_password
from django.utils.encoding import smart_str
from num2words import num2words
from sqlalchemy import Column, Integer, String, ForeignKey, Date, BigInteger, DateTime, Boolean, SmallInteger, \
    LargeBinary, DECIMAL, PrimaryKeyConstraint
from sqlalchemy import ForeignKeyConstraint
from sqlalchemy import UniqueConstraint
from sqlalchemy.dialects.mysql import JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, backref

from erp import logger
from erp.abstracts import AutoSerialize, Taxable, TaxableItem, Chargeable, Remarkable
from erp.auth import VIEW_FLAG_POSITION, EDIT_FLAG_POSITION, DELETE_FLAG_POSITION, APPROVE_FLAG_POSITION, \
    NOTIFY_FLAG_POSITION
from util.helper import getFinancialYear, getFYStartDate

__author__ = 'Saravanan'

Base = declarative_base()


class AccountBook(Base, AutoSerialize):
	""" Models the Table 'account_books'"""
	__tablename__ = u'account_books'

	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	since = Column(DateTime, name='since', primary_key=True, autoincrement=False)
	till = Column(DateTime, name='till', primary_key=True, autoincrement=False)
	created_on = Column(DateTime, name='created_on', default=datetime.now())
	created_by = Column(Integer, ForeignKey('auth_user.id'), name='created_by')
	snapshot = Column(JSON, name='snapshot')
	financial_year = Column(String, name='financial_year')
	closure_voucher_id = Column(Integer, ForeignKey('voucher.id'), name='closure_voucher_id')

	closed_by = relationship("User", backref=backref("account_book_closed_by", order_by=[enterprise_id, created_by]))
	closure_voucher = relationship(
		"Voucher", backref="account_book_closure_voucher", single_parent=True,
		cascade="save-update, merge, delete, delete-orphan",
		primaryjoin="and_(Voucher.enterprise_id==AccountBook.enterprise_id, Voucher.id==AccountBook.closure_voucher_id)")

	def __init__(
			self, enterprise_id=None, financial_year='', since=datetime(1900, 1, 1), till=datetime.now(),
			created_on=datetime.now(), created_by=None, snapshot=None):
		self.enterprise_id = enterprise_id
		self.financial_year = financial_year
		self.since = since
		self.till = till
		self.created_on = created_on
		self.created_by = created_by
		self.snapshot = snapshot


class AccountGroup(Base, AutoSerialize):
	""" Data Model Object for the table 'account_groups' """
	__tablename__ = u'account_groups'
	__table_args__ = (
		ForeignKeyConstraint(('enterprise_id', 'parent_id'), ('account_groups.enterprise_id', 'account_groups.id')),)

	ALLOW_LEDGER = 1
	ALLOW_SUBGROUP = 2
	IS_SYSTEM_GROUP = 4

	BANK_GROUP_NAME = "Bank Account"
	CASH_GROUP_NAME = "Cash in Hand"
	LT_BORROWINGS_GROUP_NAME = "Long-term Borrowings"
	ST_BORROWINGS_GROUP_NAME = "Short-Term Borrowings"
	SUNDRY_DEBTOR_GROUP_NAME = "Sundry Debtors"
	SUNDRY_CREDITOR_GROUP_NAME = "Sundry Creditors"
	DUTIES_AND_TAXES_GROUP_NAME = "Duties and Taxes"
	PNL_GROUP_NAME = "Profit and Loss"
	SALES_GROUP_NAME = "Sales Account"
	PURCHASE_GROUP_NAME = "Purchase Account"
	OTHER_CURRENT_LIABILITIES_GROUP_NAME = "Other Current Liabilities"
	OTHER_CURRENT_ASSETS_GROUP_NAME = "Other Current Assets"
	INDIRECT_EXPENSES = "Indirect Expenses"

	id = Column(Integer, name='id', primary_key=True, autoincrement=True)
	parent_id = Column(Integer, name='parent_id')
	name = Column(String(100), name='name', nullable=False)
	description = Column(String(100), name='description')
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	created_on = Column(DateTime, name='created_on')
	created_by = Column(Integer, ForeignKey('auth_user.id'), name='created_by')
	config_flags = Column(Integer, name='config_flags')  # Bit flags to capture any A/c group level settings
	note_no = Column(Integer, ForeignKey('account_notes.id'), name='note_no')
	is_default = Column(Boolean, name='is_default', default=False)
	billable = Column(Boolean, name='billable', default=0)

	ledgers = relationship(
		"Ledger", backref='group_ledgers',
		primaryjoin="and_(AccountGroup.id==Ledger.group_id, AccountGroup.enterprise_id==Ledger.enterprise_id)")
	parent = relationship("AccountGroup", remote_side=[enterprise_id, id])
	children = relationship("AccountGroup", backref=backref("group_children", remote_side=[enterprise_id, id]))
	note = relationship("AccountNote", backref=backref("group_note", order_by=note_no))
	enterprise = relationship("Enterprise", backref=backref("group_enterprise", remote_side=[enterprise_id]))

	def __init__(
			self, parent_id=None, name='', description='', created_on=None, created_by=None, config_flags=0,
			enterprise_id=None, note_no=None, is_default=False, billable=0):
		self.parent_id = parent_id
		self.name = name
		self.description = description
		self.enterprise_id = enterprise_id
		self.created_on = created_on if created_on else time.strftime("%Y-%m-%d %H:%M:%S")
		self.created_by = created_by
		self.config_flags = config_flags
		self.note_no = note_no
		self.is_default = is_default
		self.billable = billable

	def __repr__(self):
		return "%s - %s" % (self.enterprise_id, self.name)

	def __eq__(self, other):
		return self.enterprise_id == other.enterprise_id and self.id == other.id

	def getLevelsAbove(self, current_level=0):
		"""
		Gives the number of levels above the current account group in the Account Group Tree

		:param current_level:
		:return:
		"""
		if (self.parent_id is None) or (self.parent_id == self.id):
			return current_level
		current_level += 1
		return self.parent.getLevelsAbove(current_level)

	def getRootGroup(self):
		"""

		:return:
		"""
		if not self.parent_id or self.parent_id == self.id:
			logger.debug("Parent Group of %s: %s" % (self, self.parent))
			return self
		return self.parent.getRootGroup()

	def isBillable(self):
		"""

		:return:
		"""
		if not self.parent_id or self.parent_id == self.id or self.name in (
				self.SUNDRY_DEBTOR_GROUP_NAME, self.SUNDRY_CREDITOR_GROUP_NAME):
			logger.debug("Is Group %s Billable? %s" % (self, self.parent))
			return self.name in (self.SUNDRY_DEBTOR_GROUP_NAME, self.SUNDRY_CREDITOR_GROUP_NAME)
		return self.parent.isBillable()

	def getAllLedgersInAndUnder(self, child_ledgers=None):
		"""

		:param child_ledgers:
		:return:
		"""
		logger.debug("%s has %s ledgers" % (self, len(self.ledgers)))
		if not child_ledgers:
			child_ledgers = set()
		if len(self.children) == 0:
			return self.ledgers
		child_ledgers = child_ledgers.union(self.ledgers)
		for _child in self.children:
			child_ledgers = child_ledgers.union(_child.getAllLedgersInAndUnder(child_ledgers))
		return child_ledgers

	def getRootParent(self, end_parent=()):
		if self.name in end_parent and len(end_parent) > 0:
			return self
		elif self.parent:
			return self.parent.getRootParent(end_parent=end_parent)
		return self

	def getSubAccountGroups(self, sub_groups=None):
		if not sub_groups:
			sub_groups = set()
		if len(self.children) == 0:
			return set()
		for child in self.children:
			sub_groups.add(child)
			sub_groups = sub_groups.union(child.getSubAccountGroups(sub_groups))
		return sub_groups

	def getBalance(self, since=None, till=None, approved_only=False, deep=False, debit_positive=False):
		"""

		:param since:
		:param till:
		:param approved_only:
		:param deep:
		:param debit_positive:
		:return:
		"""
		till = till if till else datetime.today()
		group_ledgers = self.getAllLedgersInAndUnder([]) if deep else self.ledgers
		return sum(ledger.getBalance(
			since=since, till=till, approved_only=approved_only,
			debit_positive=debit_positive) for ledger in group_ledgers)

	def isLedgerCreationAllowed(self):
		return (self.config_flags & self.ALLOW_LEDGER) == self.ALLOW_LEDGER

	def isSubGroupCreationAllowed(self):
		return (self.config_flags & self.ALLOW_SUBGROUP) == self.ALLOW_SUBGROUP

	def isSystemGroup(self):
		return (self.config_flags & self.IS_SYSTEM_GROUP) == self.IS_SYSTEM_GROUP


class AccountStatement(Base):
	"""

	"""
	__tablename__ = u'account_statements'

	ASSET_GROUP = "Assets"
	LIABILITY_GROUP = "Equities and Liabilities"

	item_no = Column(Integer, name="item_no", primary_key=True, autoincrement=False)
	type = Column(String(5), name="type", primary_key=True, autoincrement=False)
	group = Column(String(150), name="group")
	title = Column(String(150), name="title")
	inclusion = Column(JSON, name="inclusion")
	note_inclusion = Column(JSON, name="note_inclusion")
	display_debit_positive = Column(Boolean, name='display_debit_positive')

	notes = relationship(
		"AccountNote", backref="statement_notes",
		primaryjoin="and_(AccountStatement.type==AccountNote.type, AccountStatement.item_no==AccountNote.statement_no)")

	def __init__(
			self, item_no=None, type=None, group=None, title=None, inclusion=None, note_inclusion=None,
			display_debit_positive=False):
		self.item_no = item_no
		self.type = type
		self.group = group
		self.title = title
		self.inclusion = inclusion
		self.note_inclusion = note_inclusion
		self.display_debit_positive = display_debit_positive

	def __repr__(self):
		return "%s%s" % ("%s-" % self.group if self.group else "", self.title)


class AccountNote(Base):
	"""

	"""
	__tablename__ = u'account_notes'

	PNL_TYPE = 'PL'
	BS_TYPE = 'BS'

	id = Column(Integer, name='id', primary_key=True, autoincrement=True)
	description = Column(String(length=45), name='description', unique=True)
	type = Column(String, ForeignKey("account_statements.type"), name='type')
	statement_no = Column(Integer, ForeignKey("account_statements.item_no"), name="statement_no")
	add_ons = Column(JSON, name="add_ons")

	statement = relationship(
		"AccountStatement", backref="note_statement",
		primaryjoin="and_(AccountNote.type==AccountStatement.type, AccountNote.statement_no==AccountStatement.item_no)")

	def __init__(self, description="", type="", statement_no=None, add_ons=None):
		self.description = description
		self.type = type
		self.statement_no = statement_no
		self.add_ons = add_ons

	def __repr__(self):
		return "%s - %s" % (self.type, self.description)


class CatalogueMaterial(Base, AutoSerialize):
	"""
	Class to model the items to be listed under a Product Catalogue [Bill of Materials]
	Persisted in the table 'CAT_MATERIALS'
	"""

	__tablename__ = u'cat_materials'

	enterprise_id = Column(Integer, ForeignKey(
		'materials.enterprise_id'), name='enterprise_id', primary_key=True, autoincrement=False)
	item_id = Column(Integer, ForeignKey(
		'materials.id'), name='item_id', primary_key=True, autoincrement=False)
	parent_id = Column(Integer, ForeignKey(
		'materials.id'), name='parent_id', primary_key=True, autoincrement=False)
	quantity = Column(DECIMAL, name='qty')

	catalogue = relationship("Material", backref='catalogue_material_catalogue', primaryjoin=(
		"and_(CatalogueMaterial.parent_id == Material.material_id, CatalogueMaterial.enterprise_id == Material.enterprise_id)"))
	material = relationship("Material", backref='cat_material_material', primaryjoin=(
		'and_(CatalogueMaterial.item_id == Material.material_id, CatalogueMaterial.enterprise_id == Material.enterprise_id)'))

	makes = relationship(
		"CatalogueMaterialMake", backref="catalogue_material_catalogue_material_makes",
		primaryjoin="and_((CatalogueMaterial.parent_id==CatalogueMaterialMake.parent_id), (CatalogueMaterial.item_id==CatalogueMaterialMake.item_id), (CatalogueMaterial.enterprise_id==CatalogueMaterialMake.enterprise_id))",
		cascade="save-update, merge, delete, delete-orphan")


	def __init__(self, parent_id=None, item_id=None, quantity=0, enterprise_id=None):
		self.parent_id = parent_id
		self.item_id = item_id
		self.quantity = quantity
		self.enterprise_id = enterprise_id

	def __eq__(self, other):
		return (self.parent_id == other.parent_id) and (self.item_id == other.item_id) and (
			self.enterprise_id == other.enterprise_id)

	def __repr__(self):
		return '%s - %s - %s' % (self.enterprise_id, self.parent_id, self.item_id)

	def getPublicValue(self, exclude=('material_catalogue_materials',), extra=()):
		return super(CatalogueMaterial, self).getPublicValue(exclude=exclude, extra=extra)

	def getMakesAsString(self):
		"""

		:return:
		"""
		makes = ""
		for make in self.makes:
			makes = "%s%s, " % (makes, make.make)
		return makes[:-2]

	def getMakeIdsAsString(self):
		"""

		:return:
		"""
		makes = ""
		for make in self.makes:
			makes = "%s%s, " % (makes, make.make.id if make.make else "")
		return makes[:-2]


class CatalogueMaterialMake(Base, AutoSerialize):
	"""

	"""

	__tablename__ = u"catalogue_material_makes"

	enterprise_id = Column(Integer, ForeignKey(
		'cat_materials.enterprise_id'), name='enterprise_id', primary_key=True, autoincrement=False)
	item_id = Column(Integer, ForeignKey(
		'materials.id'), name='item_id', primary_key=True, autoincrement=False)
	parent_id = Column(Integer, ForeignKey(
		'materials.id'), name='parent_id', primary_key=True, autoincrement=False)
	make_id = Column(Integer, ForeignKey("make.id"), primary_key=True, name="make_id")

	catalogue_material = relationship(
		"CatalogueMaterial", backref="catalogue_material_make_catalogue_material",
		primaryjoin="and_(CatalogueMaterialMake.parent_id==(CatalogueMaterial.parent_id), CatalogueMaterialMake.item_id==(CatalogueMaterial.item_id), CatalogueMaterial.enterprise_id==(CatalogueMaterialMake.enterprise_id))")
	catalogue = relationship("Material", backref='catalogue_material_make_catalogue', primaryjoin=(
		"and_(CatalogueMaterialMake.parent_id == Material.material_id, CatalogueMaterialMake.enterprise_id == Material.enterprise_id)"))
	material = relationship("Material", backref='catalogue_material_make_material', primaryjoin=(
		'and_(CatalogueMaterialMake.item_id == Material.material_id, CatalogueMaterialMake.enterprise_id == Material.enterprise_id)'))
	make = relationship(
		"Make", backref="catalogue_material_make_make",
		primaryjoin="and_(CatalogueMaterialMake.enterprise_id==Make.enterprise_id, CatalogueMaterialMake.make_id==Make.id)")

	def __init__(self, enterprise_id=None, parent_id=None, item_id=None, make_id=None):
		self.parent_id = parent_id
		self.item_id = item_id
		self.enterprise_id = enterprise_id
		self.make_id = make_id

	def __repr__(self):
		return "%s-%s-%s-%s" % (self.parent_id, self.item_id, self.enterprise_id, self.make_id)

	def __eq__(self, other):
		return self.parent_id == other.parent_id and self.item_id == other.item_id and self.make_id == other.make_id



class MaterialSupplierProfile(Base, AutoSerialize):
	"""
	Class to model the items to be listed under a Product [Price of Materials with SupplierWise]
	Persisted in the table ''
	"""
	__tablename__ = u'supplier_materials_price'

	PENDING = 0
	APPROVED = 1
	REJECTED = -1

	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	item_id = Column(Integer, ForeignKey(
		'materials.id'), name='item_id', primary_key=True, autoincrement=False)
	make_id = Column(Integer, ForeignKey('make.id'), name='make_id', primary_key=True, autoincrement=False)
	supp_id = Column(String(20), ForeignKey(
		'party_master.party_id'), name='supp_id', primary_key=True, autoincrement=False)
	is_service = Column(Boolean, name='is_service', default=False, primary_key=True, autoincrement=False)
	effect_since = Column(DateTime, name='effect_since', primary_key=True, autoincrement=False)
	effect_till = Column(DateTime, name='effect_till')
	price = Column(DECIMAL(precision=5), name='price')
	is_primary = Column(Boolean, name='is_store_value', default=False)
	status = Column(Integer, name='status', default=0)
	remarks = Column(String(100), name='remarks')
	approved_on = Column(DateTime, name='approved_on')
	approved_by = Column(String(20), ForeignKey('auth_user.id'), name='approved_by', nullable=True)
	reject_remarks = Column(String(500), name="reject_remarks")
	currency_id = Column(Integer, ForeignKey('currency.id'), name='currency_id')
	moq = Column(DECIMAL, name='minimum_order_quantity')
	alternate_unit_id = Column(
		Integer, ForeignKey('materials_alternate_units.alternate_unit_id'), name='alternate_unit_id', default=None)
	lead_time = Column(Integer, name='lead_time', default=0)
	is_mask_price = Column(Boolean, name='is_mask_price', default=False)

	UniqueConstraint(enterprise_id, item_id, supp_id, effect_since, make_id)

	supplier_price_currency = relationship("Currency", backref=backref("supplier_price_currency", order_by=currency_id))

	enterprise = relationship(
		"Enterprise", backref="supplier_material_enterprise",
		primaryjoin="and_(MaterialSupplierProfile.enterprise_id==Enterprise.id)")
	supplier = relationship("Party", backref='supplier_material_party', primaryjoin=(
		'and_(MaterialSupplierProfile.supp_id == Party.id, MaterialSupplierProfile.enterprise_id == Party.enterprise_id)'))
	material = relationship("Material", backref='supplier_material_material', primaryjoin=(
		'and_(MaterialSupplierProfile.item_id == Material.material_id, MaterialSupplierProfile.enterprise_id == Material.enterprise_id)'))

	approved_user = relationship("User", backref='supplier_material_approve_user', primaryjoin=(
		'and_(MaterialSupplierProfile.approved_by == User.id)'))

	make = relationship(
		"Make", backref='supplier_material_price_make',
		primaryjoin="and_(MaterialSupplierProfile.make_id==Make.id, MaterialSupplierProfile.enterprise_id==Make.enterprise_id)")

	alternate_unit = relationship(
		"MaterialAlternateUnit", backref='supplier_materials_price_alternate_unit',
		primaryjoin="""and_(MaterialAlternateUnit.enterprise_id==MaterialSupplierProfile.enterprise_id, 
			MaterialAlternateUnit.alternate_unit_id==MaterialSupplierProfile.alternate_unit_id, 
			MaterialAlternateUnit.item_id==MaterialSupplierProfile.item_id)""")

	def __init__(
			self, item_id=None, make_id=None, supp_id=None, price=0, remarks='', enterprise_id=None, currency_id=356,
			effect_since=None, effect_till=None, is_primary=False, reject_remarks=None, is_service=False, moq=0,
			alternate_unit_id=None, lead_time=0, is_mask_price=0):
		self.supp_id = supp_id
		self.item_id = item_id
		self.price = price
		self.remarks = remarks
		self.enterprise_id = enterprise_id
		self.effect_since = effect_since
		self.effect_till = effect_till
		self.is_primary = is_primary
		self.reject_remarks = reject_remarks
		self.currency_id = currency_id
		self.make_id = make_id
		self.is_service = is_service
		self.moq = moq
		self.alternate_unit_id = alternate_unit_id
		self.lead_time = lead_time
		self.is_mask_price = is_mask_price

	def __eq__(self, other):
		return (self.supp_id == other.supp_id) and (self.item_id == other.item_id) and (
			self.enterprise_id == other.enterprise_id)

	def __repr__(self):
		return '%s - %s - %s' % (self.enterprise_id, self.supp_id, self.item_id)

class MaterialPricing(Base, AutoSerialize):
	"""
	Models the pricing information for materials, with prices stored for various stages of approval.F
	"""
	__tablename__ = 'material_pricing'

	enterprise_id = Column(Integer, primary_key=True, name='enterprise_id')
	item_id = Column(Integer, primary_key=True, name='item_id')
	stored_price = Column(DECIMAL(20, 2), nullable=True, name='stored_price')
	purchase_price = Column(DECIMAL(20, 2), nullable=True, name='purchase_price')
	purchase_currency_id = Column(Integer, nullable=True, name='purchase_currency_id')
	approved_price = Column(DECIMAL(20, 2), nullable=True, name='approved_price')
	approved_currency_id = Column(Integer, nullable=True, name='approved_currency_id')
	supplier_id = Column(Integer, nullable=True, name='supplier_id')

	def __init__(self, enterprise_id=None, item_id=None, stored_price=None, purchase_price=None, purchase_currency_id=None,
				 approved_price=None, approved_currency_id=None, supplier_id=None):
		self.enterprise_id = enterprise_id
		self.item_id = item_id
		self.stored_price = stored_price
		self.purchase_price = purchase_price
		self.purchase_currency_id = purchase_currency_id
		self.approved_price = approved_price
		self.approved_currency_id = approved_currency_id
		self.supplier_id = supplier_id


class Category(Base, AutoSerialize):
	"""
	Models a simple table that holds the various units used to measure the material quantities identified against the
	drawing_no provided
	"""
	__tablename__ = u'material_category'

	id = Column(Integer, primary_key=True, name="id", autoincrement=True)
	name = Column(String(50), name="name")
	is_service = Column(Boolean, name='is_service', default=False, autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey("enterprise.id"), name="enterprise_id")

	def __init__(self, name=None, is_service=False, enterprise_id=None):
		self.name = name
		self.is_service = is_service
		self.enterprise_id = enterprise_id

	def __repr__(self):
		return "%s" % self.name


class CreditDebitNote(Base, AutoSerialize, Taxable, Remarkable):
	""" Models the Note/Memo generated by the Audit, after verification """
	__tablename__ = u'crdrnote'

	id = Column(Integer, name='grn_no', primary_key=True, autoincrement=True)
	code = Column(Integer, name="note_no", default=0)
	sub_number = Column(String(1), name='sub_number')
	financial_year = Column(String(length=5), name='financial_year')
	value = Column(DECIMAL, name='amount')
	round_off = Column(DECIMAL, name='round_off')
	is_credit = Column(Boolean, name='is_credit', default=False)
	created_on = Column(DateTime, name='note_datetime')
	enterprise_id = Column(ForeignKey('enterprise.id'), name='enterprise_id')
	attachment = Column(JSON, name="attachment")
	project_code = Column(Integer, ForeignKey('projects.id'), name='project_code')
	party_id = Column(Integer, ForeignKey('party_master.party_id'), name='party_id')
	inv_no = Column(String(50), name="inv_no")
	inv_date = Column(DateTime, name='inv_date')
	inv_value = Column(DECIMAL, name='inv_value')
	currency_id = Column(Integer, ForeignKey("currency.id"), name="currency_id")
	currency_conversion_rate = Column(DECIMAL, name='currency_conversion_rate')
	ledger_bill_id = Column(Integer, ForeignKey('ledger_bills.id'), name='ledger_bill_id')
	voucher_id = Column(Integer, ForeignKey('voucher.id'), name='voucher_id')
	raised_on = Column(DateTime, name='raised_on')
	raised_by = Column(Integer, ForeignKey("auth_user.id"), name='raised_by')
	approved_on = Column(DateTime, name='approved_on')
	approved_by = Column(Integer, ForeignKey("auth_user.id"), name='approved_by')
	last_modified_on = Column(DateTime, name='last_modified_on')
	last_modified_by = Column(Integer, name="last_modified_by")
	super_modified_on = Column(DateTime, name='super_modified_on')
	super_modified_by = Column(Integer, name="super_modified_by")
	status = Column(Integer, name="status")
	remarks = Column(JSON, name="remarks")
	receipt_code = Column(String(45), name="receipt_code")
	party_revised_on = Column(DateTime, name='party_revised_on')
	irn_ack_json = Column(JSON, name='irn_ack_json')

	enterprise = relationship("Enterprise", backref=backref("note_enterprise", order_by=enterprise_id))
	party = relationship("Party", backref=backref("note_party", order_by=party_id))

	items = relationship("NoteItem", backref=backref(
		"note_items", order_by=id), cascade="save-update, merge, delete, delete-orphan")
	non_stock_items = relationship("NoteItemNonStock", backref=backref(
		"note_non_stock_items", order_by=id), cascade="save-update, merge, delete, delete-orphan")
	taxes = relationship("NoteTax", backref=backref(
		"note_taxes", order_by=id), cascade="save-update, merge, delete, delete-orphan")
	tags = relationship("NoteTag", backref=backref(
		"note_tags", order_by=id), cascade="save-update, merge, delete, delete-orphan")
	currency = relationship("Currency", backref=backref("note_currency", order_by=currency_id))
	note_creator = relationship("User", backref=backref("note_creator", order_by=raised_by), foreign_keys=[raised_by])
	note_approver = relationship("User", backref=backref("note_approver", order_by=approved_by), foreign_keys=[approved_by])
	note_receipt_map = relationship(
		"NoteReceiptMap", backref="note_receipt_map_receipt", lazy="subquery",
		primaryjoin="and_(CreditDebitNote.id==NoteReceiptMap.note_id, CreditDebitNote.enterprise_id==NoteReceiptMap.enterprise_id)", uselist=False)

	note_document = relationship(
		"CreditDebitNoteDocument", backref="note_document", cascade="save-update, merge, delete, delete-orphan",
		primaryjoin="and_(CreditDebitNoteDocument.note_id==CreditDebitNote.id)", uselist=False)

	note_attachment = relationship(
		"NoteAttachment", backref="note_attachment", cascade="save-update, merge, delete, delete-orphan",
		primaryjoin="and_(NoteAttachment.note_id==CreditDebitNote.id)", uselist=False)

	project = relationship(
		"Project", backref='note_project', lazy="subquery",
		primaryjoin="and_(CreditDebitNote.project_code==Project.id, CreditDebitNote.enterprise_id==Project.enterprise_id)")

	ledger_bill = relationship("LedgerBill", backref=backref(
		"note_ledger_bill", order_by=ledger_bill_id), cascade="save-update, merge, delete")
	voucher = relationship("Voucher", backref=backref(
		"note_voucher", order_by=voucher_id), cascade="save-update, merge, delete")

	def __init__(
			self, id=None, value=0.0, is_credit=False, code=0, created_on=None, remarks="", enterprise_id=None,
			financial_year=None, round_off=0.0, sub_number=None, party_id=None, inv_no=None, inv_date=None, inv_value=None,
			currency_conversion_rate=1, ledger_bill_id=None, voucher_id=None, raised_on=None, raised_by=None,
			approved_on=None, approved_by=None, last_modified_on=None, last_modified_by=None, super_modified_on=None,
			super_modified_by=None, status=None, currency_id=356, receipt_code=None, project_code=None, attachment=None,
			irn_ack_json=None, *args, **kwargs):
		super(CreditDebitNote, self).__init__(*args, **kwargs)
		self.code = code
		self.id = id
		self.sub_number = sub_number
		self.value = value
		self.is_credit = is_credit
		self.created_on = created_on if created_on else time.strftime('%Y-%m-%d %H:%M:%S')
		self.remarks = remarks
		self.enterprise_id = enterprise_id
		self.financial_year = financial_year if financial_year else time.strftime("%Y")
		self.party_id = party_id
		self.inv_no = inv_no
		self.inv_date = inv_date
		self.inv_value = inv_value
		self.currency_conversion_rate = currency_conversion_rate
		self.ledger_bill_id = ledger_bill_id
		self.voucher_id = voucher_id
		self.raised_on = raised_on
		self.raised_by = raised_by
		self.approved_on = approved_on
		self.approved_by = approved_by
		self.last_modified_on = last_modified_on
		self.last_modified_by = last_modified_by
		self.super_modified_on = super_modified_on
		self.super_modified_by = super_modified_by
		self.status = status
		self.round_off = round_off
		self.attachment = attachment
		self.currency_id = currency_id
		self.receipt_code = receipt_code
		self.project_code = project_code
		self.irn_ack_json = irn_ack_json

	def __repr__(self):
		return '%s %s%s' % (
			self.enterprise_id, self.id, "%s" % self.sub_number.strip() if self.sub_number else "")

	@staticmethod
	def generateNoteCode(financial_year=None, code=None, sub_number=None):
		return "%s/IAN/%06d%s" % (financial_year, code, "%s" % sub_number.strip() if sub_number else "")

	def getCode(self):
		return self.generateNoteCode(financial_year=self.financial_year, code=self.code, sub_number=self.sub_number)

	def getNetCreditValue(self):
		items_credit_value = 0
		items_debit_value = 0
		for item in self.items:
			if item.is_credit:
				items_credit_value += item.amount
			else:
				items_debit_value += item.amount
			logger.debug("Debit: %s| Credit: %s" % (items_debit_value, items_credit_value))
		for ns_item in self.non_stock_items:
			if ns_item.is_credit:
				items_credit_value += ns_item.amount
			else:
				items_debit_value += ns_item.amount
			logger.debug("Debit: %s| Credit: %s" % (items_debit_value, items_credit_value))
		return items_credit_value - items_debit_value

	def getTotalMaterialValueForDC(self, dc=None):
		material_value = 0
		material_credit_value =0
		material_debit_value = 0
		if dc:

			for material in self.items:
				if dc == material.dc_id:
					if material.is_credit:
						material_credit_value += material.quantity * material.rate
					else:
						material_debit_value += material.quantity * material.rate
			for material in self.non_stock_items:
				if dc == material.dc_id:
					if material.is_credit:
						material_credit_value += material.quantity * material.rate
					else:
						material_debit_value += material.quantity * material.rate
		return material_credit_value - material_debit_value

	def getConsolidatedTaxListForDC(self, dc=None):
		"""
		:return: a set that contains all taxes associated at both Document & Item level
		"""
		consolidated_tax_list = set()
		for tax in self.getTaxes():
			consolidated_tax_list.add(tax.tax)
		if dc:
			for item in self.items:
				if dc == item.dc_id:
					for tax in item.taxes:
						consolidated_tax_list.add(tax.tax)
			for item in self.non_stock_items:
				if dc == item.dc_id:
					for tax in item.taxes:
						consolidated_tax_list.add(tax.tax)
		return consolidated_tax_list

	def getTaxValuesForDC(self, dc=None):
		"""
		Creates & Returns a Map of tax_codes & net value against each tax_code

		:param dc:
		:return: a dict [tax_code: tax_value]
		"""
		sorted_taxes = self.getTaxes()
		tax_values = {}
		if dc:
			for item in self.items:
				if dc == item.dc_id:
					self._extractMaterialTaxValues(
						item=item, sorted_taxes=sorted_taxes, tax_values=tax_values, discount_applied=False)
			for ns_item in self.non_stock_items:
				if dc == ns_item.dc_id:
					self._extractMaterialTaxValues(
						item=ns_item, sorted_taxes=sorted_taxes, tax_values=tax_values,
						discount_applied=False)
		return tax_values


class NoteReceiptMap(Base, AutoSerialize):
	""" Data Model Object for the table NoteReceiptMap """
	__tablename__ = u'crdrnote_grn_map'

	note_id = Column(Integer, ForeignKey('crdrnote.grn_no'), name='crdrnote_id', primary_key=True, autoincrement=True)
	receipt_no = Column(ForeignKey('grn.grn_no'), name='crdrnote_grn_no', primary_key=True, autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name="enterprise_id", primary_key=True, autoincrement=False)

	receipt = relationship("Receipt", backref=backref("note_receipt_map_receipt", order_by=receipt_no))
	note = relationship("CreditDebitNote", backref=backref("note_receipt_map_note", order_by=note_id))
	enterprise = relationship("Enterprise", backref=backref("note_receipt_map_enterprise", order_by=enterprise_id))

	def __init__(self, note_id=None, receipt_no=None, enterprise_id=None):
		self.note_id = note_id
		self.receipt_no = receipt_no
		self.enterprise_id = enterprise_id

	def __eq__(self, other):
		return self.crdrnote_id == other.crdrnote_id and self.receipt_no == other.receipt_no

	def __repr__(self):
		return "%s - %s" % (self.note_id, self.receipt_no)


class NoteTag(Base):
	""" Tagging model for Audit Notes """
	__tablename__ = u'note_tags'

	note_id = Column(Integer, ForeignKey('crdrnote.grn_no'), primary_key=True, name='note_id', autoincrement=False)
	tag_id = Column(Integer, ForeignKey('tags.id'), primary_key=True, name='tag_id', autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), primary_key=True, name='enterprise_id', autoincrement=False)

	tag = relationship("Tag", backref=backref("note_tag_tag", order_by=(tag_id, enterprise_id)))
	note = relationship("CreditDebitNote", backref=backref("note_tags_note", order_by=note_id))

	def __init__(self, note_id=None, tag_id=None, enterprise_id=None):
		self.enterprise_id = enterprise_id
		self.note_id = note_id
		self.tag_id = tag_id

	def __repr__(self):
		return "[%s] %s" % (self.note, self.tag)


class NoteTax(Base):
	"""
	Models the Note-Tax_profile relationship, derived from the Receipt-Tax relationship.
	One-to-many association b/w the Note and Tax_profile.
	"""
	__tablename__ = u'crdr_tax'

	note_id = Column(Integer, ForeignKey('crdrnote.grn_no'), primary_key=True, name='crdr_id', autoincrement=False)
	tax_code = Column(String(20), ForeignKey('tax.code'), primary_key=True, name='tax_code', autoincrement=False)
	tax_order = Column(Integer, name='tax_order', default=1)
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name='enterprise_id')

	note = relationship("CreditDebitNote", backref=backref("note_tax_note", order_by=note_id))
	tax = relationship(
		"Tax", backref="note_tax_tax",
		primaryjoin="and_(NoteTax.tax_code==Tax.code, NoteTax.enterprise_id==Tax.enterprise_id)")

	def __init__(self, note_id=None, tax_code=None, tax_order=None, enterprise_id=None):
		self.note_id = note_id
		self.tax_code = tax_code
		self.tax_order = tax_order
		self.enterprise_id = enterprise_id

	def __repr__(self):
		return '%s %s %s' % (self.note_id, self.tax_code, self.tax_order)

	def getInputTaxLedgerId(self):
		return self.tax.getInputLedgerId()

	def getOutputTaxLedgerId(self):
		return self.tax.getOutputLedgerId()


class NoteItem(Base, TaxableItem):
	""" Model for Particulars listed in an Audit Note """
	__tablename__ = "crdr_details"

	note_id = Column(
		Integer, ForeignKey("crdrnote.grn_no"), name="crdr_id", primary_key=True, autoincrement=False)
	item_id = Column(Integer, ForeignKey(
		'materials.id'), name='item_id', primary_key=True, autoincrement=False)
	reason_id = Column(Integer, ForeignKey("note_reason.id"), name="reason_id", primary_key=True, autoincrement=False)
	po_no = Column(
		Integer, ForeignKey("purchase_order.id"), name="po_no", primary_key=True, autoincrement=False, nullable=True)
	quantity = Column(DECIMAL(precision=2), name="qty")
	hsn_code = Column(String(20), name='hsn_code')
	rate = Column(DECIMAL(precision=5), name="rate")
	amount = Column(DECIMAL(precision=2), name="amount")
	is_credit = Column(Boolean, name="is_credit")
	enterprise_id = Column(
		Integer, ForeignKey("materials.enterprise_id"), name="enterprise_id", primary_key=True, autoincrement=False)
	make_id = Column(Integer, ForeignKey('make.id'), name='make_id', primary_key=True, autoincrement=False)
	dc_id = Column(Integer, ForeignKey('invoice.id'), name='dc_id', primary_key=True, autoincrement=False)
	alternate_unit_id = Column(Integer, ForeignKey('materials_alternate_units.alternate_unit_id'), name='alternate_unit_id', default=None)
	is_faulty = Column(Integer, name='is_faulty', primary_key=True, autoincrement=False)

	UniqueConstraint(note_id, po_no, item_id, reason_id, enterprise_id, make_id, dc_id, is_faulty)
	note = relationship("CreditDebitNote", backref=backref("note_item_note", order_by=note_id))
	material = relationship(
		"Material", backref="note_item_material",
		primaryjoin="and_(Material.material_id==NoteItem.item_id, Material.enterprise_id==NoteItem.enterprise_id)")
	purchase_order = relationship("PurchaseOrder", backref=backref("note_item_po", order_by=po_no))
	reason = relationship("NoteReason", backref=backref("note_item_reason", order_by=reason_id))
	taxes = relationship(
		"NoteItemTax", backref="note_item_note_item_tax", lazy="subquery", cascade="save-update, merge, delete",
		primaryjoin="""and_(NoteItem.note_id==NoteItemTax.note_id, 
		NoteItem.item_id==NoteItemTax.item_id, 
		NoteItem.reason_id==NoteItemTax.reason_id, 		 
		NoteItem.make_id==NoteItemTax.make_id, 
		NoteItem.dc_id==NoteItemTax.dc_id,
		NoteItem.is_faulty==NoteItemTax.is_faulty,
		or_(NoteItem.po_no==NoteItemTax.po_no
		, and_(NoteItem.po_no.is_(None)
		, NoteItemTax.po_no.is_(None))))""")
	make = relationship(
		"Make", backref='note_material_make',
		primaryjoin="and_(NoteItem.make_id==Make.id, NoteItem.enterprise_id==Make.enterprise_id)")
	dc = relationship("Invoice", backref=backref("note_item_material_dc", order_by=dc_id))
	alternate_unit = relationship(
		"MaterialAlternateUnit", backref='crdr_material_alternate_unit',
		primaryjoin="""and_(MaterialAlternateUnit.enterprise_id==NoteItem.enterprise_id, 
				MaterialAlternateUnit.alternate_unit_id==NoteItem.alternate_unit_id, 
				MaterialAlternateUnit.item_id==NoteItem.item_id)""")

	def __init__(
			self, note_id=None, item_id=item_id, reason_id=1, quantity=0, rate=0, is_credit=False, amount=None,
			enterprise_id=None, po_no=None, dc_id=0, make_id=None, hsn_code=None, is_faulty=0):
		self.note_id = note_id
		self.item_id = item_id
		self.reason_id = reason_id
		self.quantity = quantity
		self.rate = rate
		self.is_credit = is_credit
		self.amount = amount
		self.enterprise_id = enterprise_id
		self.po_no = po_no
		self.make_id = make_id
		self.dc_id = dc_id
		self.hsn_code = hsn_code
		self.is_faulty = is_faulty

	def __repr__(self):
		return "%s - %s" % (self.material, self.make)


class NoteItemTax(Base):
	""" Model for Particulars listed in an Audit Note """
	__tablename__ = "crdr_details_tax"

	note_id = Column(
		Integer, ForeignKey("crdr_details.crdr_id"), name="crdr_id", primary_key=True, autoincrement=False)
	item_id = Column(Integer, ForeignKey(
		'crdr_details.item_id'), name='item_id', primary_key=True, autoincrement=False)
	reason_id = Column(
		Integer, ForeignKey("crdr_details.reason_id"), name="reason_id", primary_key=True, autoincrement=False)
	po_no = Column(
		Integer, ForeignKey("crdr_details.po_no"), name="po_no", primary_key=True, autoincrement=False, nullable=True)
	enterprise_id = Column(
		Integer, ForeignKey("enterprise.id"), name="enterprise_id", primary_key=True, autoincrement=False)
	tax_code = Column(String(20), ForeignKey('tax.code'), name='tax_code', primary_key=True, autoincrement=False)
	make_id = Column(Integer, ForeignKey('make.id'), name='make_id', primary_key=True, autoincrement=False)
	dc_id = Column(Integer, ForeignKey('invoice.id'), name='dc_id', primary_key=True, autoincrement=False)
	is_faulty = Column(Integer, name='is_faulty', primary_key=True, autoincrement=False)

	UniqueConstraint(note_id, po_no, item_id, reason_id, enterprise_id, make_id, dc_id, is_faulty)
	note_item = relationship(
		"NoteItem", backref="note_item_tax_note_item",
		primaryjoin="""and_(NoteItemTax.note_id==NoteItem.note_id,
		 NoteItemTax.item_id==NoteItem.item_id,
		 NoteItemTax.reason_id==NoteItem.reason_id, 
		 NoteItemTax.make_id==NoteItem.make_id,
		 NoteItemTax.is_faulty==NoteItem.is_faulty, 		 
		  or_(NoteItemTax.dc_id==NoteItem.dc_id
		, and_(NoteItemTax.dc_id.is_(None)
		, NoteItem.dc_id.is_(None))),
		 or_(NoteItemTax.po_no==NoteItem.po_no
		, and_(NoteItemTax.po_no.is_(None)
		, NoteItem.po_no.is_(None))))""")
	tax = relationship(
		"Tax", backref='note_item_tax_tax',
		primaryjoin="and_(NoteItemTax.tax_code == Tax.code, NoteItemTax.enterprise_id == Tax.enterprise_id)")
	make = relationship(
		"Make", backref='note_material_tax_make',
		primaryjoin="and_(NoteItemTax.make_id==Make.id, NoteItemTax.enterprise_id==Make.enterprise_id)")
	dc = relationship("Invoice", backref=backref("note_item_material_tax_dc", order_by=dc_id))

	def __init__(
			self, note_id=None, item_id=None, enterprise_id=None, po_no=None, reason_id=None, tax_code=None,
			make_id=None, dc_id=0, is_faulty=0):
		self.note_id = note_id
		self.item_id = item_id
		self.enterprise_id = enterprise_id
		self.po_no = po_no
		self.reason_id = reason_id
		self.id = id
		self.tax_code = tax_code
		self.make_id = make_id
		self.dc_id = dc_id
		self.is_faulty = is_faulty

	def __eq__(self, other):
		return self.note_id == other.note_id and self.po_no == other.po_no and self.item_id == other.item_id and self.reason_id == other.reason_id and self.tax_code == other.tax_code


class NoteItemNonStock(Base, TaxableItem, AutoSerialize):
	""" Model for Particulars, that are not profiled, listed in an Audit Note """
	__tablename__ = "crdr_details_nonstock_material"

	note_id = Column(Integer, ForeignKey('crdrnote.grn_no'), name='grn_no', primary_key=True, autoincrement=False)
	description = Column(String(100), name='description', primary_key=True, autoincrement=False)
	reason_id = Column(Integer, ForeignKey('note_reason.id'), name='reason_id', primary_key=True, autoincrement=False)
	po_no = Column(
		Integer, ForeignKey('purchase_order.id'), name='po_no', primary_key=True, autoincrement=False, nullable=True)
	quantity = Column(DECIMAL, name='qty')
	hsn_code = Column(String(20), name='hsn_code')
	rate = Column(DECIMAL, name='rate')
	amount = Column(DECIMAL, name='amount')
	is_credit = Column(String(10), name='is_credit')
	enterprise_id = Column(
		Integer, ForeignKey('enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	unit_id = Column(DECIMAL, ForeignKey('unit_master.unit_id'), name='unit_id')
	dc_id = Column(Integer, ForeignKey('invoice.id'), name='dc_id', primary_key=True, autoincrement=False)

	UniqueConstraint(note_id, description, enterprise_id, po_no, reason_id, dc_id)
	note = relationship("CreditDebitNote", backref=backref("note_ns_item_note", order_by=note_id))
	reason = relationship("NoteReason", backref=backref("note_ns_item_reason", order_by=reason_id))
	unit = relationship("UnitMaster", backref='note_nonstock_items',
	                    primaryjoin="and_(UnitMaster.enterprise_id==NoteItemNonStock.enterprise_id, UnitMaster.unit_id==NoteItemNonStock.unit_id)")
	purchase_order = relationship("PurchaseOrder", backref=backref("note_ns_item_po", order_by=po_no))
	taxes = relationship(
		"NoteItemNonStockTax", backref=backref("note_item_nonstock_note_item_tax"),
		cascade="save-update, merge, delete-orphan, delete", single_parent=True,
		primaryjoin="""and_(NoteItemNonStock.enterprise_id==NoteItemNonStockTax.enterprise_id
						, NoteItemNonStock.note_id==NoteItemNonStockTax.note_id
						, NoteItemNonStock.description==NoteItemNonStockTax.description
						, NoteItemNonStock.reason_id==NoteItemNonStockTax.reason_id
						, or_(NoteItemNonStock.po_no==NoteItemNonStockTax.po_no
							, and_(NoteItemNonStockTax.po_no.is_(None), NoteItemNonStock.po_no.is_(None)))
						, or_(NoteItemNonStock.dc_id==NoteItemNonStockTax.dc_id
							, and_(NoteItemNonStockTax.dc_id.is_(None), NoteItemNonStock.dc_id.is_(None))))""")
	dc = relationship("Invoice", backref=backref("note_item_non_stock_material_dc", order_by=dc_id))

	def __init__(
			self, note_id=None, description="Others", reason_id=None, quantity=0.00, rate=0.00, amount=0.00,
			is_credit=0, enterprise_id=None, po_no=None, unit_id=1, dc_id=0, hsn_code=0):
		self.note_id = note_id
		self.description = description
		self.reason_id = reason_id
		self.quantity = quantity
		self.rate = rate
		self.amount = amount
		self.is_credit = is_credit
		self.enterprise_id = enterprise_id
		self.po_no = po_no
		self.unit_id = unit_id
		self.dc_id = dc_id
		self.hsn_code = hsn_code

	def __eq__(self, other):
		return self.receipt_no == other.receipt_no and ((not self.po_no and not other.po_no) or (
			self.po_no == other.po_no)) and self.description == other.description and self.reason_id == other.reason_id and self.tax_code == other.tax_code

	def __repr__(self):
		return self.description


class NoteItemNonStockTax(Base):
	""" Model for Particulars, that are not profiled, listed in an Audit Note """
	__tablename__ = "crdr_details_nonstock_material_tax"

	note_id = Column(
		Integer, ForeignKey('crdr_details_nonstock_material.grn_no'), name='grn_no'
		, primary_key=True, autoincrement=False)
	description = Column(
		String(100), ForeignKey('crdr_details_nonstock_material.description'), name='description'
		, primary_key=True, autoincrement=False)
	reason_id = Column(
		Integer, ForeignKey('crdr_details_nonstock_material.reason_id'), name='reason_id'
		, primary_key=True, autoincrement=False)
	po_no = Column(
		Integer, ForeignKey('crdr_details_nonstock_material.po_no'), name='po_no'
		, primary_key=True, autoincrement=False, nullable=True)
	enterprise_id = Column(
		Integer, ForeignKey('enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	tax_code = Column(String(20), ForeignKey('tax.code'), name='tax_code', primary_key=True, autoincrement=False)
	dc_id = Column(
		Integer, ForeignKey('crdr_details_nonstock_material.dc_id'), name='dc_id'
		, primary_key=True, autoincrement=False)

	UniqueConstraint(note_id, description, reason_id, po_no, enterprise_id, tax_code, dc_id)

	note_nonstock_item = relationship(
		"NoteItemNonStock", backref="note_item_nonstock_tax_note_nonstock_item",
		primaryjoin="and_(NoteItemNonStockTax.note_id==NoteItemNonStock.note_id, NoteItemNonStockTax.description==NoteItemNonStock.description, NoteItemNonStockTax.po_no==NoteItemNonStock.po_no, NoteItemNonStockTax.reason_id==NoteItemNonStock.reason_id, NoteItemNonStock.dc_id==NoteItemNonStockTax.dc_id)")
	tax = relationship(
		"Tax", backref='note_ns_item_tax_tax',
		primaryjoin="and_(NoteItemNonStockTax.tax_code == Tax.code, NoteItemNonStockTax.enterprise_id == Tax.enterprise_id)")

	def __init__(
			self, note_id=None, description="Others", enterprise_id=None, po_no=None, reason_id=None, tax_code=None):
		self.note_id = note_id
		self.description = description
		self.enterprise_id = enterprise_id
		self.po_no = po_no
		self.reason_id = reason_id
		self.tax_code = tax_code


class NoteReason(Base, AutoSerialize):
	""" Models the master list of system-generated Reasons """
	__tablename__ = u'note_reason'

	id = Column(Integer, name="id", primary_key=True, autoincrement=True)
	reason = Column(String(100), name="reason", unique=True)

	def __init__(self, reason="Others"):
		self.reason = reason

	def __repr__(self):
		return "%s" % self.reason


class Currency(Base):
	"""
	Models a simple table that holds the various units used to measure the material quantities identified against the
	drawing_no provided
	"""
	__tablename__ = u'currency'

	id = Column(Integer, primary_key=True, name="id", autoincrement=False)
	code = Column(String(10), name="code")
	name = Column(String(50), name="currency_name")
	minor_unit = Column(String(20), name="minor_unit")
	decimal_places = Column(Integer, name="decimal_places")
	symbols = Column(String(20), name="symbols")

	def __init__(self, name=None):
		self.name = name

	def __repr__(self):
		return "%s" % self.code


class Employee(Base, AutoSerialize):
	"""
	Data Model Object for the table Employee

	Stores the master information that describes materials that goes through the procurement process
	"""
	__tablename__ = u'employee_master'

	emp_id = Column(BigInteger, primary_key=True, name='emp_id', autoincrement=True)
	emp_code = Column(String(10), name='emp_code')
	emp_name = Column(String(140), name='first_name')
	address1 = Column(String(140), name='address1')
	address2 = Column(String(140), name='address2')
	city = Column(String(40), name='city')
	state = Column(String(40), name='state')
	phone_no = Column(String(40), name='phone_no')
	email = Column(String(40), name='email')
	department_id = Column(Integer, ForeignKey('department.id'), name='department_id', primary_key=True)
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name='enterprise_id')
	last_name = Column(String(50), name='last_name')
	employment_type = Column(String, name='employment_type')
	status = Column(Integer, name='status')
	designation = Column(String(50), name='designation')
	date_of_joining = Column(DateTime, name='date_of_joining')
	date_of_birth = Column(DateTime, name='date_of_birth')
	gender = Column(String, name='gender')
	country = Column(String(45), name='country')
	postal_code = Column(String(10), name='postal_code')
	nationality = Column(String(45), name='nationality')
	martial_status = Column(String, name='martial_status')
	aadhar_number = Column(BigInteger, name='aadhar_number')
	pan_number = Column(String(45), name='pan_number')
	account_number = Column(String(45), name='account_number')
	account_name = Column(String(45), name='account_name')
	account_type = Column(String(45), name='account_type')
	ifsc_code = Column(String(45), name='ifsc_code')
	esi_no = Column(String(45), name='esi_no')
	pf_no = Column(String(45), name='pf_no')
	pay_structure_id = Column(Integer, name='pay_structure_id')
	no_of_el = Column(Integer, name='no_of_el')
	no_of_cl = Column(Integer, name='no_of_cl')
	place_of_work = Column(String(45), name='place_of_work')
	father_name = Column(String(45), name='father_name')
	mother_name = Column(String(45), name='mother_name')
	contact_number = Column(String(45), name='contact_number')
	profile_pic_filename = Column(String(length=25), name='profile_pic_filename')
	spouse_name = Column(String(45), name='spouse_name')
	blood_group = Column(String(45), name='blood_group')

	department = relationship("Department", backref=backref('employee_department', order_by=department_id))

	pay_structure_items = relationship(
		"EmployeePayStructureItem", backref="employee_pay_structure_item",
		primaryjoin="and_(Employee.emp_id==EmployeePayStructureItem.employee_id)",
		cascade="save-update, merge, delete-orphan, delete")

	def __init__(
			self, emp_id=0, emp_code=None, emp_name=None, address1=None, address2=None, city=None, state=None,
			phone_no=None, email=None, department_id=0, enterprise_id=0, last_name=None, employment_type=None,
			status=None, designation=None, date_of_joining=None, date_of_birth=None, gender=None, country=None,
			postal_code=None, nationality=None, martial_status=None, aadhar_number=None, pan_number=None,
			account_number=None, account_name=None, account_type=None, ifsc_code=None, esi_no=None, pf_no=None,
			pay_structure_id=0, no_of_el=0, no_of_cl=0, place_of_work=None, father_name=None, mother_name=None,
			contact_number=None, profile_pic=None, profile_pic_filename="", spouse_name=None, blood_group=None):
		self.emp_id = emp_id
		self.emp_code = emp_code
		self.emp_name = emp_name
		self.address1 = address1
		self.address2 = address2
		self.city = city
		self.state = state
		self.phone_no = phone_no
		self.email = email
		self.department_id = department_id
		self.enterprise_id = enterprise_id
		self.last_name = last_name
		self.employment_type = employment_type
		self.status = status
		self.designation = designation
		self.date_of_joining = date_of_joining
		self.date_of_birth = date_of_birth
		self.gender = gender
		self.country = country
		self.postal_code = postal_code
		self.nationality = nationality
		self.martial_status = martial_status
		self.aadhar_number = aadhar_number
		self.pan_number = pan_number
		self.account_number = account_number
		self.account_name = account_name
		self.account_type = account_type
		self.ifsc_code = ifsc_code
		self.esi_no = esi_no
		self.pf_no = pf_no
		self.pay_structure_id = pay_structure_id
		self.no_of_el = no_of_el
		self.no_of_cl = no_of_cl
		self.place_of_work = place_of_work
		self.father_name = father_name
		self.mother_name = mother_name
		self.contact_number = contact_number
		self.profile_pic = profile_pic
		self.profile_pic_filename = profile_pic_filename
		self.profile_pic_ext = ""
		self.spouse_name = spouse_name
		self.blood_group = blood_group

	def __eq__(self, other):
		return self.emp_id == Employee(other).emp_id

	def __repr__(self):
		return "%s (%s)" % (self.emp_name.__str__(), self.emp_code.__str__())

	def getPublicValue(self, exclude=('enterprise',), extra=()):
		return super(Employee, self).getPublicValue(exclude=exclude, extra=extra)


class Enterprise(Base, AutoSerialize):
	""" Models the base Entity around which this entire Product System functions """
	__tablename__ = u'enterprise'

	id = Column(BigInteger, primary_key=True, name='id', autoincrement=True)
	created_on = Column(DateTime, name='created_on')
	created_by = Column(Integer, name='created_by')
	last_modified_on = Column(DateTime, name='last_modified_on')
	last_modified_by = Column(Integer, name='last_modified_by')
	code = Column(String(6), unique=True, name='code')
	name = Column(String(50), name='name')
	address_1 = Column(String(140), name='address1')
	address_2 = Column(String(140), name='address2')
	phone = Column(String(40), name='phone_no')
	fax = Column(String(30), name='fax_no')
	email = Column(String(40), name='email')
	country_code = Column(String(4), name='country_code')
	city = Column(String(20), name='city')
	state = Column(String(20), name='state')
	pin_code = Column(String(10), name='pin_code')
	setting_flags = Column(Integer, name='setting_flags', default=0)
	fy_start_day = Column(String(5), name='fy_start_day', default='01/04')
	home_currency_id = Column(Integer, ForeignKey('currency.id'), name='currency_id')
	is_negative_stock_allowed = Column(Boolean, name='is_negative_stock_allowed', default=False)
	is_gate_inward_no_mandatory = Column(Boolean, name='is_gate_inward_no_mandatory', default=False)
	gate_inward_no_flags = Column(Integer, name='gate_inward_no_flags', default=0)
	is_purchase_order_mandatory = Column(Boolean, name='is_purchase_order_mandatory', default=False)
	contact_id = Column(Integer, name='contact_id')
	user_max_count = Column(Integer, name='user_count', default=10)
	erase_data_expiry_date = Column(DateTime, name='erase_data_expiry_date')
	is_delivery_schedule = Column(Boolean, name='is_delivery_schedule', default=False)
	is_multiple_units = Column(Boolean, name='is_multiple_units', default=False)
	is_icd_request_acknowledgement = Column(Boolean, name='is_icd_request_acknowledgement', default=False)
	is_blanket_po = Column(Boolean, name='is_blanket_po', default=False)
	is_price_modification_disabled = Column(Boolean, name='is_price_modification_disabled', default=False)
	is_price_modification_disabled_quick_po = Column(Boolean, name='is_price_modification_disabled_quick_po', default=False)
	parent_enterprise_id = Column(Integer, name='parent_enterprise_id')
	parent_enterprise_code = Column(String(6), name='parent_enterprise_code')
	type = Column(String(30), name='type', default='Organisation')
	is_project_wise_pl = Column(Boolean, name='is_project_wise_pl', default=False)

	home_currency = relationship("Currency", backref=backref("enterprise_home_currency", order_by=home_currency_id))
	registration_details = relationship(
		"EnterpriseRegistrationDetail", primaryjoin="(Enterprise.id==EnterpriseRegistrationDetail.enterprise_id)",
		backref="enterprise_enterprise_registration_detail",
		cascade="save-update, merge, delete, delete-orphan")
	images = relationship("EnterpriseImages", uselist=False, backref=backref(
		"enterprise_enterprise_images", order_by=id), cascade="save-update, merge, delete, delete-orphan")
	notes = relationship("EnterpriseNotes", uselist=False, backref=backref(
		"enterprise_enterprise_notes", order_by=id), cascade="save-update, merge, delete, delete-orphan")
	claim_heads = relationship("EnterpriseClaimHead", backref=backref(
		"enterprise_enterprise_claim_heads", order_by=id), cascade="save-update, merge, delete, delete-orphan")
	expense_heads = relationship("EnterpriseExpenseHead", backref=backref(
		"enterprise_enterprise_expense_heads", order_by=id), cascade="save-update, merge, delete, delete-orphan")

	created_user = relationship(
		"User", backref='enterprise_created_user', uselist=False,
		primaryjoin='foreign(User.id) == Enterprise.created_by')
	last_modified_user = relationship(
		"User", backref='enterprise_last_modified_user', uselist=False,
		primaryjoin='foreign(User.id) == Enterprise.last_modified_by')
	primary_contact_details = relationship(
		"EnterpriseContactMap", backref='primary_contact', uselist=False,
		primaryjoin='and_(Enterprise.contact_id == EnterpriseContactMap.contact_id, Enterprise.id == EnterpriseContactMap.enterprise_id)')
	se_doc_items = relationship("SalesEstimateTemplateConfig", uselist=False, backref=backref(
		"enterprise_se_template_config", order_by=id), cascade="save-update, merge, delete, delete-orphan")
	subscriptions = relationship(
		"EnterpriseSubscription", backref=backref('enterprise_subscription', order_by=id),
		cascade="save-update, merge, delete, delete-orphan")
	inv_template_config = relationship(
		"InvoiceTemplateConfig", uselist=False, backref='inv_template_config_invoice',
		primaryjoin='and_(Enterprise.id == InvoiceTemplateConfig.enterprise_id)')

	def __init__(
			self, id=None, created_by=None, created_on=None, last_modified_by=None, code=None, name=None, email=None
			, address_1=None, address_2=None, phone=None, fax=None, country_code=None, city=None, state=None, pin_code=None, home_currency_id=356
			, fy_start_day='01/04', is_negative_stock_allowed=False, is_gate_inward_no_mandatory=False
			, is_purchase_order_mandatory=False, user_count=10, erase_data_expiry_date=None, is_delivery_schedule=False
			, is_multiple_units=False, is_icd_request_acknowledgement=False, is_blanket_po=False
			, is_price_modification_disabled=False, is_price_modification_disabled_quick_po=False, parent_enterprise_id=None,
			parent_enterprise_code=None, type=None):
		self.id = id
		self.created_on = created_on if created_on else time.strftime('%Y-%m-%d %H:%M:%S')
		self.created_by = created_by
		self.last_modified_by = last_modified_by
		self.last_modified_on = time.strftime('%Y-%m-%d %H:%M:%S')
		self.code = code
		self.name = name
		self.email = email
		self.address_1 = address_1
		self.address_2 = address_2
		self.phone = phone
		self.fax = fax
		self.country_code = country_code
		self.city = city
		self.state = state
		self.pin_code = pin_code
		self.home_currency_id = home_currency_id
		self.fy_start_day = fy_start_day
		self.is_negative_stock_allowed = is_negative_stock_allowed
		self.is_gate_inward_no_mandatory = is_gate_inward_no_mandatory
		self.is_purchase_order_mandatory = is_purchase_order_mandatory
		self.user_count = user_count
		self.erase_data_expiry_date = erase_data_expiry_date if erase_data_expiry_date else datetime.now() + timedelta(days=30)
		self.is_delivery_schedule = is_delivery_schedule
		self.is_multiple_units = is_multiple_units
		self.is_icd_request_acknowledgement = is_icd_request_acknowledgement
		self.is_blanket_po = is_blanket_po
		self.is_price_modification_disabled = is_price_modification_disabled
		self.is_price_modification_disabled_quick_po = is_price_modification_disabled_quick_po
		self.parent_enterprise_id = parent_enterprise_id
		self.parent_enterprise_code = parent_enterprise_code
		self.type = type

	def __repr__(self):
		return '%s %s' % (self.code, self.name)

	def getPublicValue(
			self, exclude=(
					'supplier_material_enterprise', 'note_enterprise', 'employee_enterprise', 'indent_enterprise',
					'issue_master_enterprise', 'material_enterprise', 'po_enterprise', 'grn_enterprise',
					'tax_enterprise', 'voucher_enterprise', 'user_enterprise', 'invoice_enterprise',
					'invoice_nonstock_item_enterprise',), extra=()):
		return super(Enterprise, self).getPublicValue(exclude=exclude, extra=extra)

	def isRegistrationComplete(self):
		"""
		Checks either the enterprise is valid after registration
		:return:
		"""
		return self.phone and self.phone != '' and self.address_1 and self.address_1 != '' and self.state and self.state != '' and self.city and self.city != "" and self.pin_code and self.pin_code != ""


class EnterpriseSubscription(Base):
	"""
	Entity that captures Enterprise's subscription details
	"""
	__tablename__ = u'enterprise_subscriptions'

	PLAN_TRIAL = 'trial'
	PLAN_BASIC = 'XSBASIC'
	PLAN_STANDARD = 'XSSTANDARD'
	PLAN_ENTERPRISE = 'XSENTERPRISE'

	enterprise_id = Column(
		Integer, ForeignKey('enterprise.id'), primary_key=True, name='enterprise_id', autoincrement=False)
	plan = Column(String(length=150), name='plan')
	since = Column(DateTime, name='since', primary_key=True, autoincrement=False)
	till = Column(DateTime, name='till', primary_key=True, autoincrement=False)
	expired_on = Column(DateTime, name='expired_on')
	extension_requested_on = Column(DateTime, name='extension_requested_on')
	extension_requested_by = Column(Integer, ForeignKey('auth_user.id'), name='extension_requested_by')
	payment_info = Column(JSON, name='payment_info')
	transaction_id = Column(String(20), name='transaction_id', unique=True)
	is_active = Column(Boolean, name='is_active', default=True)
	created_on = Column(DateTime, name='created_on')
	invoice_id = Column(Integer, ForeignKey('invoice.id'), name='invoice_id')
	coupon = Column(String(45), name='coupon')
	discount = Column(DECIMAL(precision=3), name='discount', default=0.00)

	UniqueConstraint(enterprise_id, since, till, transaction_id)

	enterprise = relationship(
		"Enterprise", backref=backref("enterprise_subscription_enterprise", order_by=enterprise_id))

	invoice = relationship(
		"Invoice", backref=backref("enterprise_subscription_invoice", order_by=invoice_id))

	def __init__(
			self, enterprise_id=None, plan='trial', since=None, till=None, expired_on=None,
			extension_requested_on=None, extension_requested_by=None, created_on=None,
			payment_info=None, transaction_id=None, invoice_id=None, coupon=None, discount=0.00, is_active=True):
		self.enterprise_id = enterprise_id
		self.plan = plan
		self.since = since
		self.till = till
		self.expired_on = expired_on
		self.extension_requested_on = extension_requested_on
		self.extension_requested_by = extension_requested_by
		self.payment_info = payment_info
		self.transaction_id = transaction_id
		self.is_active = is_active
		self.created_on = created_on if created_on else time.strftime('%Y-%m-%d %H:%M:%S')
		self.invoice_id = invoice_id
		self.coupon = coupon
		self.discount = discount


class PaymentGatewayMerchantInfo(Base):
	"""
	Entity to hold Registration and Merchant information from Payment Gateway
	"""
	__tablename__ = u'pgi_merchant_info'
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), primary_key=True, name='enterprise_id', autoincrement=False)
	merchant_id = Column(Integer, name='merchant_id', primary_key=True, autoincrement=False)
	merchant_key = Column(String(length=150), name='merchant_key')
	merchant_salt = Column(String(length=150), name='merchant_salt')

	def __int__(self, enterprise_id=None, merchant_id=None, merchant_key=None, merchant_salt=None):
		self.enterprise_id = enterprise_id
		self.merchant_id = merchant_id
		self.merchant_key = merchant_key
		self.merchant_salt = merchant_salt

	def __repr__(self):
		return "%s: %s" % (self.enterprise_id, self.merchant_id)


class EnterpriseRegistrationDetail(Base):
	"""
	Entity to hold Enterprise's Registration Detail - One-to-One
	"""
	__tablename__ = u'enterprise_registration_detail'

	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), primary_key=True, name='enterprise_id', autoincrement=False)
	label_id = Column(Integer, name='label_id', primary_key=True, autoincrement=False)
	label = Column(String(length=30), name='label', primary_key=True, autoincrement=False)
	details = Column(String(length=150), name='details')
	created_on = Column(DateTime, name='created_on')
	created_by = Column(Integer, name='created_by')
	last_modified_on = Column(DateTime, name='last_modified_on')
	last_modified_by = Column(Integer, name='last_modified_by')

	def __init__(
			self, enterprise_id=None, label_id=None, label="", details="", created_by=None, created_on=None, last_modified_by=None):
		self.enterprise_id = enterprise_id
		self.label_id = label_id
		self.label = label
		self.details = details
		self.created_on = created_on if created_on else time.strftime('%Y-%m-%d %H:%M:%S')
		self.created_by = created_by
		self.last_modified_by = last_modified_by
		self.last_modified_on = time.strftime('%Y-%m-%d %H:%M:%S')


class EnterpriseImages(Base):
	"""
	Entity for persisting Enterprise Images, such as Logo, WaterMark Images, etc
	"""
	__tablename__ = u'enterprise_images'

	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	logo = Column(LargeBinary, name='logo')
	ext = Column(String(length=25), name='ext')

	def __init__(self, enterprise_id=None, logo="", ext=""):
		self.enterprise_id = enterprise_id
		self.logo = logo
		self.ext = ext

	def __repr__(self):
		return 'Size: %s Extension: %s' % (len(self.logo), self.ext)


class EnterpriseNotes(Base):
	"""
	Models the Enterprise's Document Configurations for Sales & Purchase module - One-to-One
	"""
	__tablename__ = u'enterprise_notes'

	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	purchase = Column(String, name='purchase')
	sales = Column(String, name='sales')
	po_doc_reg_items = Column(String, name='po_doc_registration_detail')
	include_hsnsac = Column(Boolean, name='include_hsnsac', default=False)
	po_doc_datetime_format = Column(String, name='po_doc_datetime_format')
	include_annexure = Column(Boolean, name='include_annexure', default=False)
	logo_size = Column(Integer, name='logo_size', autoincrement=False)
	include_hsnsac_column = Column(Boolean, name='include_hsnsac_column')

	def __init__(
			self, enterprise_id=None, purchase="", sales="", po_doc_reg_items="", include_hsnsac=None, po_doc_datetime_format="",
			include_annexure=None, logo_size=None, include_hsnsac_column=None):
		self.enterprise_id = enterprise_id
		self.purchase = purchase
		self.sales = sales
		self.po_doc_reg_items = po_doc_reg_items
		self.include_hsnsac = include_hsnsac
		self.po_doc_datetime_format = po_doc_datetime_format
		self.include_annexure = include_annexure
		self.logo_size = logo_size
		self.include_hsnsac_column = include_hsnsac_column

	def __repr__(self):
		return 'Purchase: %s\nSales: %s' % (self.purchase, self.sales)


class EnterpriseClaimHead(Base, AutoSerialize):
	"""
	Models the Enterprise's Expense Configurations - Claim Heads
	Enterprise-EnterpriseClaimHead :: One-to-Many
	"""
	__tablename__ = u'enterprise_claim_heads'

	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	ledger_id = Column(Integer, ForeignKey(
		'account_ledgers.id'), name='ledger_id', primary_key=True, autoincrement=False)

	enterprise = relationship(
		"Enterprise", backref="enterprise_claim_head_enterprise",
		primaryjoin="and_(Enterprise.id==EnterpriseClaimHead.enterprise_id)")
	ledger = relationship(
		"Ledger", backref="enterprise_ledger_enterprise", primaryjoin="and_(Ledger.id==EnterpriseClaimHead.ledger_id)")

	def __init__(self, enterprise_id=None, ledger_id=None):
		self.enterprise_id = enterprise_id
		self.ledger_id = ledger_id

	def __repr__(self):
		return "%s - %s" % (self.enterprise_id, self.ledger_id)

	def __eq__(self, other):
		return self.enterprise_id == other.enterprise_id and self.ledger_id == other.ledger_id


class EnterpriseExpenseHead(Base, AutoSerialize):
	"""
	Models the Enterprise's Expense Configurations - Expense Heads
	Enterprise-EnterpriseExpenseHead :: One-to-Many
	"""
	__tablename__ = u'enterprise_expense_heads'

	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	ledger_id = Column(Integer, ForeignKey(
		'account_ledgers.id'), name='ledger_id', primary_key=True, autoincrement=False)

	enterprise = relationship(
		"Enterprise", backref="enterprise_expense_head_enterprise",
		primaryjoin="and_(Enterprise.id==EnterpriseExpenseHead.enterprise_id)")

	ledger = relationship(
		"Ledger", backref="enterprise_expense_ledger_enterprise",
		primaryjoin="and_(Ledger.id==EnterpriseExpenseHead.ledger_id)")

	def __init__(self, enterprise_id=None, ledger_id=None):
		self.enterprise_id = enterprise_id
		self.ledger_id = ledger_id

	def __repr__(self):
		return "%s - %s" % (self.enterprise_id, self.ledger_id)

	def __eq__(self, other):
		return self.enterprise_id == other.enterprise_id and self.ledger_id == other.ledger_id


class ContactDetails(Base):
	"""
	Entity to hold Enterprise's Contact Detail
	"""
	__tablename__ = u'contact_details'

	id = Column(Integer, name='id',  primary_key=True, autoincrement=True)
	name = Column(String(length=50), name='name')
	phone_no = Column(String(length=20), name='phone_no')
	email = Column(String(length=50), name='email')
	fax_no = Column(String(length=20), name='fax_no')
	is_whatsapp = Column(Boolean, name='is_whatsapp')
	created_on = Column(DateTime, name='created_on')
	created_by = Column(Integer, name='created_by')
	last_modified_on = Column(DateTime, name='last_modified_on')
	last_modified_by = Column(Integer, name='last_modified_by')
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), primary_key=True, name='enterprise_id', autoincrement=False)

	def __init__(
			self, name=None, phone_no=None, email=None, fax_no=None, is_whatsapp=0,
			created_by=None, created_on=None, last_modified_by=None, enterprise_id=None):
		self.name = name
		self.phone_no = phone_no
		self.email = email
		self.fax_no = fax_no
		self.is_whatsapp = is_whatsapp
		self.created_on = created_on if created_on else time.strftime('%Y-%m-%d %H:%M:%S')
		self.created_by = created_by
		self.last_modified_by = last_modified_by
		self.last_modified_on = time.strftime('%Y-%m-%d %H:%M:%S')
		self.enterprise_id = enterprise_id


class Indent(Base, AutoSerialize, Remarkable):
	"""
	Class that models the table 'INDENTS'
	Persistent object

	Manufacturing Indent - a request for list materials required for manufacturing, initiated by the Production
	Department. It is composed of a list of materials requested for the purpose, modelled in a one-to-many relation.
	"""
	__tablename__ = u'indents'

	indent_no = Column(Integer, primary_key=True, name='indent_no', autoincrement=True)
	indent_id = Column(Integer, name='indent_id')
	sub_number = Column(String(1), name='sub_number')
	super_modified_on = Column(DateTime, name='super_modified_on')
	super_modified_by = Column(Integer, ForeignKey('auth_user.id'), name='super_modified_by')
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name='enterprise_id')
	project_code = Column(Integer, ForeignKey('projects.id'), name='project_code')
	purchase_account_id = Column(Integer, ForeignKey('account_ledgers.id'), name='purchase_account_id')
	purpose = Column(String(100), name='purpose')
	raised_by = Column(String(50), name='raised_by')  # Person who raises the indent is captured here
	raised_date = Column(DateTime, name='raised_date')
	modified_date = Column(DateTime, name='modified_date')
	modified_by = Column(Integer, ForeignKey('auth_user.id'),
	                     name='modified_by')  # Person who Modify the indent is captured here
	expected_date = Column(DateTime, name='expected_date')
	remarks = Column(JSON, name='sp_instructions')
	closed_date = Column(DateTime, name='closed_datetime')
	financial_year = Column(String(5), name='financial_year')
	is_stockable = Column(Integer, name='is_stockable')
	indent_module_id = Column(Integer, name='indent_module_id')

	type = relationship("Ledger", backref=backref(
		"indent_purchase_account_ledger", order_by=(purchase_account_id, enterprise_id)))
	project = relationship(
		"Project", backref='indent_indent_project',
		primaryjoin="and_(Indent.enterprise_id==Project.enterprise_id, Indent.project_code==Project.id)")
	enterprise = relationship("Enterprise", backref=backref('indent_enterprise', order_by=enterprise_id))
	modifier = relationship(
		"User", backref='indent_modified_by',
		primaryjoin='foreign(User.id) == Indent.modified_by')
	super_modified_user = relationship(
		"User", backref='indent_super_modified_user',
		primaryjoin='foreign(User.id) == Indent.super_modified_by')
	materials = relationship("IndentMaterial", backref=backref(
		"indent_indent_materials", order_by=indent_no), cascade="save-update, merge, delete, delete-orphan")
	purchase_orders = relationship("PurchaseOrder", backref=backref(
		"indent_indent_pos", order_by=indent_no), cascade="save-update, merge, delete, delete-orphan")
	tags = relationship("IndentTag", backref=backref(
		"indent_indent_tags", order_by=indent_no), cascade="save-update, merge, delete, delete-orphan")
	indent_oa = relationship(
		"OAIndentMap", backref='indent_oa_indent_map', primaryjoin=(
			"and_(Indent.indent_no==OAIndentMap.indent_no, Indent.enterprise_id==OAIndentMap.enterprise_id)"), uselist=False)

	def __init__(
			self, indent_no=None, indent_id=None, enterprise_id=None, project_code=None, indent_type=None, purpose='',
			raised_date=None, modified_date=None, expected_date=None, closed_date=None, raised_by=None,
			financial_year=None, is_stockable=0, modified_by=None, purchase_account_id=None, indent_module_id=0,
			sub_number=None, super_modified_on=None, super_modified_by=None, *args, **kwargs):
		super(Indent, self).__init__(*args, **kwargs)
		self.indent_no = indent_no
		self.indent_id = indent_id
		self.sub_number = sub_number
		self.super_modified_on = super_modified_on
		self.super_modified_by = super_modified_by
		self.enterprise_id = enterprise_id
		self.project_code = project_code
		self.indent_type = indent_type
		self.purpose = purpose
		self.raised_by = raised_by
		self.raised_date = raised_date if raised_date else time.strftime('%Y-%m-%d %H:%M:%S')
		self.modified_date = modified_date if modified_date else time.strftime('%Y-%m-%d %H:%M:%S')
		self.modified_by = modified_by
		self.expected_date = expected_date if expected_date else time.strftime('%Y-%m-%d')
		self.closed_date = closed_date
		self.financial_year = financial_year if financial_year else time.strftime('%Y')
		self.is_stockable = is_stockable
		self.purchase_account_id = purchase_account_id
		self.indent_module_id = indent_module_id

	def __eq__(self, other):
		return self.indent_no == Indent(other).indent_no

	def __repr__(self):
		return '%s' % self.__dict__

	@staticmethod
	def generateCode(indent_id=None, financial_year=None, sub_number=None, indent_module_id=None):
		if indent_id is None:
			return '%s/%s/None' % (financial_year, "MI" if indent_module_id == 1 else "IND")
		return '%s/%s/%s' % (financial_year, "MI" if indent_module_id == 1 else "IND", "%s%s" % (
			"%06d" % int(indent_id), "%s" % sub_number.strip() if sub_number else ""))

	def getCode(self):
		return self.generateCode(
			indent_id=self.indent_id, financial_year=self.financial_year, sub_number=self.sub_number,
			indent_module_id=self.indent_module_id)

	def getPublicValue(self, exclude=('project_indents', 'po_indent', 'indent_material_indent',), extra=()):
		return super(Indent, self).getPublicValue(exclude=exclude, extra=extra)

	def getMaterialsQuantityMap(self):
		"""

		:return:
		"""
		material_quantity_map = {}
		for material in self.materials:
			if material.alternate_unit_id:
				scale_factor = material.alternate_unit.scale_factor
				if scale_factor:
					material_quantity_map[(material.item_id, material.make_id)] = Decimal(material.quantity) / Decimal(scale_factor)
			else:
				material_quantity_map[(material.item_id, material.make_id)] = material.quantity
		return material_quantity_map

	def getMaterialQuantity(self, item_id=None):
		"""

		:param item_id:
		:return:
		"""
		for indent_material in self.materials:
			if indent_material.item_id == item_id:
				return indent_material.quantity
		return 0

	def getPurchaseQuantityMap(self):
		"""

		:return:
		"""
		ordered_materials_map = {}
		for purchase_order in self.purchase_orders:
			if purchase_order.status != 3:
				ordered_materials = purchase_order.getMaterialsQuantityMap()
				for item_key in ordered_materials:
					if item_key in ordered_materials_map:
						ordered_materials_map[item_key] += ordered_materials[item_key]
					else:
						ordered_materials_map[item_key] = ordered_materials[item_key]
		return ordered_materials_map

	def hadOrderedAllMaterials(self):
		"""

		:return:
		"""
		ordered_materials = self.getPurchaseQuantityMap()
		logger.debug("Number of Materials Ordered for %s - %s of %s" % (
			self.getCode(), len(ordered_materials), len(self.materials)))
		for material in self.materials:
			try:
				if ordered_materials[material.item_id] < material.quantity:
					return False
			except KeyError:
				return False
		return True

	def hadReceivedAllMaterials(self):
		"""

		:return:
		"""
		for purchase_order in self.purchase_orders:
			if not purchase_order.isAllMaterialsSupplied():
				return False
		return self.hadOrderedAllMaterials()

	def getStatus(self):
		"""

		:return:
		"""
		if not self.hadOrderedAllMaterials():
			return "Pending due to PO"
		elif not self.hadReceivedAllMaterials():
			return "Pending due to Material"
		else:
			return "Completed"


class IndentMaterial(Base, AutoSerialize):
	""" Models the Materials associated with an indent - to be listed along the Indent Model """
	__tablename__ = u'indent_material'
	#Make to be removed in future
	indent_no = Column(Integer, ForeignKey(
		'indents.indent_no'), primary_key=True, name='indent_no', autoincrement=False)
	item_id = Column(Integer, ForeignKey(
		'materials.id'), name='item_id', primary_key=True, autoincrement=False)
	quantity = Column(DECIMAL(precision=5), name='request_qty', default=0)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), primary_key=True, name='enterprise_id', autoincrement=False)
	make_id = Column(Integer, ForeignKey('make.id'), name='make_id', primary_key=True, autoincrement=False, default=1)
	alternate_unit_id = Column(Integer, ForeignKey('materials_alternate_units.alternate_unit_id'), name='alternate_unit_id', default=None)

	UniqueConstraint(indent_no, enterprise_id, item_id, make_id)

	material = relationship(
		"Material", backref="indent_material_material",
		primaryjoin="and_(Material.material_id==IndentMaterial.item_id, Material.enterprise_id==IndentMaterial.enterprise_id)")
	indent = relationship("Indent", backref=backref('indent_material_indent', order_by=indent_no))
	make = relationship(
		"Make", backref='indent_material_make',
		primaryjoin="and_(IndentMaterial.make_id==Make.id, IndentMaterial.enterprise_id==Make.enterprise_id)")
	alternate_unit = relationship(
		"MaterialAlternateUnit", backref='indent_material_alternate_unit',
		primaryjoin="""and_(MaterialAlternateUnit.enterprise_id==IndentMaterial.enterprise_id, 
				MaterialAlternateUnit.alternate_unit_id==IndentMaterial.alternate_unit_id, 
				MaterialAlternateUnit.item_id==IndentMaterial.item_id)""")

	def __init__(self, indent_no=None, item_id=None, enterprise_id=None, quantity=0, make_id=None):
		self.indent_no = indent_no
		self.item_id = item_id
		self.quantity = quantity
		self.enterprise_id = enterprise_id
		self.make_id = make_id

	def __repr__(self):
		return '("%s", "%s", "%s", "%s", "%s")' % (
			self.indent_no, self.item_id, self.make, self.enterprise_id, self.quantity)

	def getPublicValue(self, exclude=('indent_material_indent',), extra=()):
		return super(IndentMaterial, self).getPublicValue(exclude=exclude, extra=extra)


class OAIndentMap(Base, AutoSerialize):
	""" Data Model Object for the table OAIndentMap """
	__tablename__ = u'oa_indent_map'

	oa_id = Column(Integer, ForeignKey('order_acknowledgement.id'), name='oa_id', primary_key=True, autoincrement=False)
	indent_no = Column(ForeignKey('indents.indent_no'), name='indent_no', primary_key=True, autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name="enterprise_id", primary_key=True, autoincrement=False)

	indent = relationship("Indent", backref=backref("oa_indent_map_indent", order_by=indent_no))
	oa = relationship("OA", backref=backref("oa_indent_map_oa", order_by=oa_id))
	enterprise = relationship("Enterprise", backref=backref("oa_indent_map_enterprise", order_by=enterprise_id))

	def __init__(self, oa_id=None, indent_no=None, enterprise_id=None):
		self.oa_id = oa_id
		self.indent_no = indent_no
		self.enterprise_id = enterprise_id

	def __eq__(self, other):
		return self.oa_id == other.oa_id and self.indent_no == other.indent_no

	def __repr__(self):
		return "%s - %s" % (self.oa_id, self.indent_no)


class IndentTag(Base):
	""" Tagging model for Indents """
	__tablename__ = u'indent_tags'

	indent_id = Column(Integer, ForeignKey(
		'indents.indent_no'), primary_key=True, name='indent_id', autoincrement=False)
	tag_id = Column(Integer, ForeignKey('tags.id'), primary_key=True, name='tag_id', autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), primary_key=True, name='enterprise_id', autoincrement=False)

	tag = relationship("Tag", backref=backref("indent_tag_tag", order_by=(tag_id, enterprise_id)))
	indent = relationship("Indent", backref=backref("indent_tags_indent", order_by=indent_id))

	def __init__(self, indent_id=None, tag_id=None, enterprise_id=None):
		self.enterprise_id = enterprise_id
		self.indent_id = indent_id
		self.tag_id = tag_id

	def __repr__(self):
		return "[%s] %s" % (self.indent, self.tag)

	def __eq__(self, other):
		return (self.indent_id == IndentTag(other).indent_id) and (self.tag_id == IndentTag(other).tag_id)


class IndentType(Base, AutoSerialize):
	"""
	Models a simple table that holds the various units used to measure the material quantities identified against the
	drawing_no provided
	"""
	__tablename__ = u'indent_type'

	id = Column(Integer, primary_key=True, name="indent_type_id", autoincrement=True)
	name = Column(String(50), name="indent_type_name")
	description = Column(String(50), name="indent_type_description")
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name="enterprise_id", primary_key=True, autoincrement=False)

	def getPublicValue(self, exclude=('indents_indent_type',), extra=()):
		return super(IndentType, self).getPublicValue(exclude=exclude, extra=extra)


class Ledger(Base, AutoSerialize):
	""" Data Model Object for the table Ledger """
	__tablename__ = u'account_ledgers'

	id = Column(Integer, name='id', primary_key=True, autoincrement=True)
	group_id = Column(Integer, ForeignKey('account_groups.id'), name='group_id')
	name = Column(String(100), name='name')
	description = Column(String(100), name='description')
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name="enterprise_id", primary_key=True, autoincrement=False)
	created_on = Column(DateTime, name='created_on')
	created_by = Column(Integer, ForeignKey('auth_user.id'), name='created_by')
	is_default = Column(Boolean, name='is_default', default=False)
	billable = Column(Boolean, name='billable', default=0)
	status = Column(Integer, name='status', default=1)

	UniqueConstraint(name, group_id, enterprise_id)

	enterprise = relationship("Enterprise", backref=backref("ledger_enterprise", order_by=enterprise_id))
	group = relationship(
		"AccountGroup", backref='ledger_group',
		primaryjoin="and_(Ledger.group_id==AccountGroup.id, Ledger.enterprise_id==AccountGroup.enterprise_id)")
	voucher_entries = relationship(
		"VoucherParticulars", backref=backref("ledger_voucher_entries", order_by=id), viewonly=True,
		secondary="join(VoucherParticulars, Voucher, VoucherParticulars.voucher_id == Voucher.id)",
		secondaryjoin="VoucherParticulars.voucher_id==Voucher.id", order_by="Voucher.voucher_date, Voucher.voucher_no")
	bills = relationship("LedgerBill", backref=backref("ledger_bills", order_by=id))

	party_map = relationship(
		"PartyLedgerMap", backref="ledger_party_map", uselist=False,
		primaryjoin="and_(Ledger.id==PartyLedgerMap.ledger_id, Ledger.enterprise_id==PartyLedgerMap.enterprise_id)")

	def __init__(
			self, group_id=None, name='', description='', enterprise_id=None, created_by=None, created_on=None,
			is_default=False, billable=0, status=1):
		self.group_id = group_id
		self.name = name
		self.description = description
		self.enterprise_id = enterprise_id
		self.created_by = created_by
		self.created_on = created_on if created_on else time.strftime('%Y-%m-%d %H:%M:%S')
		self.is_default = is_default
		self.billable = billable
		self.status = status

	def __eq__(self, other):
		return self.group_id == other.group_id and self.name == other.name and self.enterprise_id == other.enterprise_id

	def __repr__(self):
		return "[%s] %s" % (self.group.name, self.name)

	def getVoucherEntries(self, since=None, till=None, approved_only=False):
		"""

		:param since:
		:param till:
		:param approved_only:
		:return:
		"""
		till = till if till else datetime.today()
		approved_entries = []
		for entry in self.voucher_entries:
			if (not since or (since <= entry.voucher.voucher_date)) and (
					not till or (entry.voucher.voucher_date <= till)) and (
					not approved_only or entry.voucher.approved_on):
				approved_entries.append(entry)
		return approved_entries

	def getDebitValueForPeriod(self, since=None, till=None, approved_only=False):
		"""

		:param since:
		:param till:
		:param approved_only:
		:return:
		"""
		till = till if till else datetime.now()
		total_debit = Decimal(0)
		for entry in self.getVoucherEntries(since, till, approved_only):
			if entry.is_debit:
				total_debit += entry.amount
		return total_debit

	def getCreditValueForPeriod(self, since=None, till=None, approved_only=False):
		"""

		:param since:
		:param till:
		:param approved_only:
		:return:
		"""
		till = till if till else datetime.now()
		total_credit = 0
		for entry in self.getVoucherEntries(since, till, approved_only):
			if not entry.is_debit:
				total_credit += entry.amount
		return total_credit

	def getOpeningBalance(self, as_on=None, approved_only=False, debit_positive=False):
		"""

		:param as_on:
		:param approved_only:
		:param debit_positive:
		:return:
		"""
		as_on = as_on if as_on else datetime.now()
		return self.getBalance(till=as_on, approved_only=approved_only, debit_positive=debit_positive)

	def getBalance(self, since=None, till=None, approved_only=False, debit_positive=False):
		"""

		:param since:
		:param till:
		:param approved_only:
		:param debit_positive:
		:return: [float] credit positive balance
		"""
		till = till if till else datetime.now()
		debit_balance = 0
		credit_balance = 0
		if not since:
			if AccountGroup.PNL_GROUP_NAME.lower() == self.group.getRootGroup().name.lower():
				since = getFYStartDate(fy_start_day=self.enterprise.fy_start_day, for_date=till)
			else:
				since = datetime(1, 1, 1)
		till = till - relativedelta(microseconds=1)
		for entry in self.getVoucherEntries(since=since, till=till, approved_only=approved_only):
			if entry.is_debit:
				debit_balance += entry.amount
			else:
				credit_balance += entry.amount
		return (debit_balance - credit_balance) if debit_positive else (credit_balance - debit_balance)

	def getImmediateBalance(self, as_on=None, voucher_no=None, approved_only=False, debit_positive=False):
		"""

		:param as_on:
		:param voucher_no:
		:param approved_only:
		:param debit_positive:
		:return:
		"""
		debit_balance = Decimal(0)
		credit_balance = Decimal(0)
		for entry in self.getVoucherEntries(till=as_on, approved_only=approved_only):
			if (not voucher_no) or entry.voucher.id < voucher_no:
				if entry.is_debit:
					debit_balance += Decimal(entry.amount)
				else:
					credit_balance += Decimal(entry.amount)
		return (debit_balance - credit_balance) if debit_positive else (credit_balance - debit_balance)


class PartyLedgerMap(Base, AutoSerialize):
	""" Data Model Object for the table PartyLedgerMap """
	__tablename__ = u'party_ledger_map'

	party_id = Column(Integer, ForeignKey(
		'party_master.party_id'), name='party_id', primary_key=True, autoincrement=False)
	ledger_id = Column(Integer, ForeignKey(
		'account_ledgers.id'), name='ledger_id', primary_key=True, autoincrement=False)
	is_supplier = Column(Boolean, name='is_supplier', default=True, primary_key=True, autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name="enterprise_id", primary_key=True, autoincrement=False)

	party = relationship("Party", backref=backref('party_ledger_map_party', order_by=party_id))
	ledger = relationship("Ledger", backref=backref('party_ledger_map_ledger', order_by=ledger_id))

	def __init__(self, party_id=None, ledger_id=None, is_supplier=1, enterprise_id=None):
		self.party_id = party_id
		self.ledger_id = ledger_id
		self.is_supplier = is_supplier
		self.enterprise_id = enterprise_id

	def __eq__(self, other):
		return self.party_id == other.party_id and self.ledger_id == other.ledger_id

	def __repr__(self):
		return "%s - %s" % (self.party_id, self.ledger_id)

	def getPublicValue(self, exclude=('supplier_ledger_map_ledger', 'customer_ledger_map_ledger',), extra=()):
		return super(PartyLedgerMap, self).getPublicValue(exclude=exclude, extra=extra)


class Material(Base, AutoSerialize):
	"""
	Data Model Object for the table MATERIALS

	Stores the master information that describes materials that goes through the procurement process
	"""
	__tablename__ = u'materials'

	CATEGORIES = {'RAW_COMPONENT': 1, 'SEMI_FINISHED': 2, 'FINISHED': 3}

	material_id = Column(Integer, name='id', primary_key=True, autoincrement=True)
	drawing_no = Column(String(20), name='drawing_no')
	enterprise_id = Column(
		Integer, ForeignKey('enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	name = Column(String(200), name='name', nullable=False)
	description = Column(String(200), name='description')
	price = Column(DECIMAL, name='price')
	category_id = Column(Integer, ForeignKey('material_category.id'), name='category', default=1)
	unit_id = Column(String(10), ForeignKey('unit_master.unit_id'), name='unit')
	is_stocked = Column(Boolean, name='is_stocked', default=True)
	in_use = Column(Boolean, name='in_use', default=True)
	is_service = Column(Boolean, name='is_service', default=False)
	created_on = Column(DateTime, name='created_on')
	tariff_no = Column(String(100), name='tariff_no')
	last_modified_on = Column(DateTime, name='last_modified_on')
	last_modified_by = Column(Integer, ForeignKey('auth_user.id'), name='last_modified_by')
	minimum_stock_level = Column(DECIMAL, name='minimum_stock_level')
	sample_size = Column(DECIMAL, name='sample_size')
	lot_size = Column(DECIMAL, name='lot_size')
	qc_method = Column(String(200), name='qc_method')
	reaction_plan = Column(String(450), name='reaction_plan')
	standard_packing_qty = Column(DECIMAL, name='standard_packing_quantity')
	location = Column(String(45), name='location')
	makes_json = Column(String(500), name='makes_json', default='[]')
	remarks = Column(String(450), name='remarks')

	category = relationship(
		"Category", backref=backref(
		                           'material_category', primaryjoin="""and_(Material.category_id==Category.id, Material.enterprise_id==Category.enterprise_id)"""))
	unit = relationship("UnitMaster", backref='material_unit',
	                                                 primaryjoin="""and_(UnitMaster.enterprise_id==Material.enterprise_id, UnitMaster.unit_id==Material.unit_id)""")
	enterprise = relationship("Enterprise", backref=backref('material_enterprise', order_by=enterprise_id))
	bill_of_materials = relationship(
		"CatalogueMaterial", backref='material_catalogue_materials', primaryjoin=(
			"and_(Material.material_id==CatalogueMaterial.parent_id, Material.enterprise_id==CatalogueMaterial.enterprise_id)"),
		cascade="save-update, merge, delete, delete-orphan")
	supplier_wise_materials_price = relationship(
		"MaterialSupplierProfile", backref='material_supplier_materials_price', primaryjoin=(
			"and_(Material.material_id==MaterialSupplierProfile.item_id, Material.enterprise_id==MaterialSupplierProfile.enterprise_id)"),
		cascade="save-update, merge, delete, delete-orphan",
		order_by="asc(MaterialSupplierProfile.supp_id), desc(MaterialSupplierProfile.is_primary), desc(MaterialSupplierProfile.effect_since)")

	#Make to be removed in future
	makes = relationship(
		"MaterialMakeMap", backref='make_material', primaryjoin=(
			"and_(Material.material_id==MaterialMakeMap.item_id, Material.enterprise_id==MaterialMakeMap.enterprise_id)"),
		cascade="save-update, merge, delete, delete-orphan")
	specifications = relationship(
		"Specification", backref='specification', primaryjoin=(
			"and_(Material.material_id==Specification.material_id,Material.enterprise_id==Specification.enterprise_id)"),
		cascade="save-update, merge, delete, delete-orphan")

	alternate_units = relationship(
		"MaterialAlternateUnit", backref='material_alternate_units', primaryjoin=(
			"and_(Material.material_id==MaterialAlternateUnit.item_id, "
			"Material.enterprise_id==MaterialAlternateUnit.enterprise_id)"),
		cascade="save-update, merge, delete, delete-orphan")

	def __init__(
			self, drawing_no=None, name=None, description='', price=0.0, in_use=True, is_service=False, unit_id=None,
			category_id=None, enterprise_id=None, created_on=None, tariff_no=None, material_id=None, is_stocked=True,
			minimum_stock_level=0.000, sample_size=0.000, lot_size=0.000, qc_method=None, reaction_plan=None,
			standard_packing_qty=None, last_modified_on=None, location=None):
		self.drawing_no = drawing_no
		self.name = name
		self.description = description
		self.price = price
		self.category_id = category_id
		self.is_stocked = is_stocked
		self.unit_id = unit_id
		self.in_use = in_use
		self.is_service = is_service
		self.enterprise_id = enterprise_id
		self.created_on = created_on if created_on else time.strftime('%Y-%m-%d %H:%M:%S')
		self.tariff_no = tariff_no
		self.material_id = material_id
		self.minimum_stock_level = minimum_stock_level
		self.sample_size = sample_size
		self.lot_size = lot_size
		self.qc_method = qc_method
		self.reaction_plan = reaction_plan
		self.standard_packing_qty = standard_packing_qty
		self.last_modified_on = last_modified_on
		self.location = location

	def __eq__(self, other):
		return self.material_id == Material(other).material_id and self.enterprise_id == Material(other).enterprise_id

	def __repr__(self):
		return "%s - %s" % (self.material_id, self.name)

	def getPublicValue(self, exclude=(
			'po_material_material', 'receipt_material_material', 'invoice_materials_material',
			'catalogue_material_catalogue', 'cat_material_material', 'supplier_material_material', 'note_item_material',
			'indent_material_material', 'enterprise',), extra=()):
		return super(Material, self).getPublicValue(exclude=exclude, extra=extra)


class MaterialSerialNo(Base, TaxableItem):
	""" Models material serial number table , its hold the materials serial number detail """
	__tablename__ = u'material_serial_number_details'

	enterprise_id = Column(
		Integer, ForeignKey('materials.enterprise_id'), name='enterprise_id', primary_key=True, autoincrement=False)
	item_id = Column(Integer, ForeignKey('materials.id'), primary_key=True, name='item_id', autoincrement=False)
	make_id = Column(Integer, ForeignKey('make.id'), name='make_id', primary_key=True, autoincrement=False)
	is_faulty = Column(Boolean, name='is_faulty', primary_key=True, autoincrement=False, default=False)
	serial_number = Column(String(32), name='serial_number', primary_key=True, autoincrement=False)
	created_on = Column(DateTime, name='created_on')
	created_by = Column(Integer, ForeignKey('auth_user.id'), name='created_by')

	UniqueConstraint(enterprise_id, serial_number)

	def __init__(
			self, enterprise_id=None, item_id=None, make_id=1, is_faulty=False, serial_number=None, created_on=None, created_by=None):
		self.enterprise_id = enterprise_id
		self.item_id = item_id
		self.make_id = make_id
		self.is_faulty = is_faulty
		self.serial_number = serial_number
		self.created_by = created_by
		self.created_on = created_on if created_on else time.strftime('%Y-%m-%d %H:%M:%S')

	def __repr__(self):
		return "%s" % self.__dict__


class Module(Base, AutoSerialize):
	""" Models 'action_module' table that stores various access points of the application and the actions associated """
	__tablename__ = u'action_module'

	code = Column(String(20), primary_key=True, name='code', autoincrement=False)
	action = Column(String(50), name='action')
	order = Column(SmallInteger, name='order')
	landing_url = Column(String(50), name='landing_url')

	def __init__(self, code, action, order, landing_url):
		self.code = code
		self.action = action
		self.order = order
		self.landing_url = landing_url

	def __repr__(self):
		return "%s %s %s %s" % (self.code, self.action, self.order, self.landing_url)


class Project(Base, AutoSerialize):
	""" Models the table 'PROJECTS' - Used for persisting basic information that identifies a Project uniquely """
	__tablename__ = u'projects'

	id = Column(Integer, primary_key=True, name='id', autoincrement=True)
	parent_id = Column(Integer, name='parent_id')
	name = Column(String(100), name='name')
	code = Column(String(20), name='code')
	is_active = Column(Boolean, name='is_active', default=True)
	enterprise_id = Column(Integer, ForeignKey(
		"enterprise.id"), name="enterprise_id", primary_key=True, autoincrement=False)
	start_date = Column(Date, name='start_date')
	end_date = Column(Date, name='end_date')
	project_owner = Column(String(100), name='project_owner')
	phone_no = Column(String(length=20), name='phone_no')
	email = Column(String(length=50), name='email')
	project_description = Column(String(100), name='project_description')
	working_capital = Column(DECIMAL(precision=2), name='working_capital', default=0.00)
	project_currency = Column(String(100), name='project_currency')
	project_enterprise_id = Column(Integer, name='project_enterprise_id')

	def __init__(self, id=None, parent_id=None, code=None, name=None, enterprise_id=None, is_active=True,
				 start_date=None, end_date=None, project_owner=None, phone_no=None, email=None, project_description=None,
				 working_capital=0, project_currency=None, project_enterprise_id=None):
		self.id = id
		self.parent_id = parent_id
		self.code = code
		self.name = name
		self.is_active = is_active
		self.enterprise_id = enterprise_id
		self.start_date = start_date
		self.end_date = end_date
		self.project_owner = project_owner
		self.phone_no = phone_no
		self.email = email
		self.project_description = project_description
		self.working_capital = working_capital
		self.project_currency = project_currency
		self.project_enterprise_id = project_enterprise_id

	def __eq__(self, other):
		return self.code == Project(other).code and self.enterprise_id == Project(other).enterprise_id

	def __repr__(self):
		return '%s - %s - %s - %s' % (self.enterprise_id, self.code, self.name, self.is_active)

	def getPublicValue(self, exclude=('project_user_project', 'po_project', 'indent_indent_project',), extra=()):
		return super(Project, self).getPublicValue(exclude=exclude, extra=extra)


class CashFlow(Base, AutoSerialize):
	__tablename__ = u'cashflow_forecast_details'

	id = Column(Integer, primary_key=True, name='id', autoincrement=True)
	enterprise_id = Column(Integer, ForeignKey("enterprise.id"), name="enterprise_id", primary_key=True, autoincrement=False)
	ledger_name = Column(String, name='ledger_name')
	project_id = Column(Integer, ForeignKey(
		"projects.id"), name="project_id")
	start_date = Column(Date, name='start_date')
	end_date = Column(Date, name='end_date')
	budget_amount = Column(DECIMAL, name='budget_amount')
	remarks = Column(JSON, name='remarks')
	type = Column(String(30), name='type')


	def __init__(self, id=None, enterprise_id=None, ledger_name=None, project_id=None, budget_amount=None, start_date=None, end_date=None, type=None):
		"""
        :param id:
        :param ledger_name:
        :param project_id:
        :param budget_amount:
        """
		self.id = id
		self.enterprise_id = enterprise_id
		self.ledger_name = ledger_name
		self.project_id = project_id
		self.budget_amount = budget_amount
		self.start_date = start_date
		self.end_date = end_date
		self.type = type

	def __repr__(self):
		return '%s - %s - %s' % (self.project_id, self.ledger_name, self.budget_amount)


class PurchaseOrder(Base, Taxable, AutoSerialize, Remarkable):
	"""
	Models the Purchase Order (PO) details.

	Purchase Order is the artifact that holds the information regarding a list of materials, for which form has been
	placed with a particular Supplier, against an Indent raised.
	PO persists various information that includes that of the project, indent, supplier, taxes, quotation apart from the
	list of materials placed for form. List of materials.
	"""
	__tablename__ = u'purchase_order'

	_STATUS_DRAFT = 0
	_STATUS_REVIEWED = 1
	_STATUS_APPROVED = 2
	_STATUS_REJECTED = 3
	_STATUS_COMPLETED = 4

	_STATUS_DICT = {
		_STATUS_DRAFT: 'Draft', _STATUS_REVIEWED: 'Reviewed', _STATUS_APPROVED: 'Approved',
		_STATUS_COMPLETED: 'Completed', _STATUS_REJECTED: 'Rejected'}

	po_id = Column(Integer, name='id', primary_key=True, autoincrement=True)
	po_no = Column(String(15), name='orderno')
	sub_number = Column(String(1), name='sub_number')
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'))
	project_code = Column(Integer, ForeignKey('projects.id'))
	indent_no = Column(Integer, ForeignKey('indents.indent_no'))
	indent_type = Column(Integer, ForeignKey('indent_type.indent_type_id'))
	purchase_account_id = Column(Integer, ForeignKey('account_ledgers.id'), name='purchase_account_id')
	supplier_id = Column(Integer, ForeignKey('party_master.party_id'))
	order_date = Column(Date, name='order_date')
	quotation_ref_no = Column(Integer, name='quotation_refno')
	quotation_date = Column(Date, name='quotation_date')
	status = Column(Integer, name='status')
	closing_remarks = Column(String(300), name='sp_instructions')
	payment = Column(String(5), name='payment')
	pay_in_days = Column(Integer, name='payment_days')
	pay_against = Column(Integer, name='payment_type')
	pay_through = Column(Integer, name='payment_mode')
	transport = Column(Boolean, name='transport')
	delivery = Column(Date, name='delivery')
	user = Column(String(100), name='user')
	drafted_on = Column(DateTime, name='drafted_on')
	purpose = Column(String(100), name='purpose')
	total = Column(DECIMAL, name='total')
	packing = Column(Boolean, name='packing_forwarding')
	tax1 = Column(DECIMAL, name='tax1amt')
	tax2 = Column(DECIMAL, name='tax2amt')
	remarks = Column(JSON, name='remarks')
	approved_on = Column(DateTime, name='approved_on')
	last_modified_on = Column(DateTime, name='last_modified_on')
	currency_code = Column(Integer, ForeignKey('currency.id'))
	financial_year = Column(String(5), name='financial_year')
	approved_by = Column(Integer, ForeignKey('auth_user.id'), name='approved_by')
	super_modified_on = Column(DateTime, name='super_modified_on')
	super_modified_by = Column(Integer, ForeignKey('auth_user.id'), name='super_modified_by')
	drafted_by = Column(Integer, ForeignKey('auth_user.id'), name='drafted_by')
	last_modified_by = Column(Integer, ForeignKey('auth_user.id'), name='last_modified_by')
	type = Column(Integer, name='type')
	is_blanket_po = Column(Boolean, name='is_blanket_po', default=False)
	valid_since = Column(Date, name='valid_since')
	valid_till = Column(Date, name='valid_till')
	round_off = Column(DECIMAL, name='round_off')
	issue_to = Column(String(45), name='issue_to')
	shipping_name = Column(String(50), name='shipping_name')
	shipping_address = Column(String(300), name='shipping_address')
	is_mask_po = Column(Boolean, name='is_mask_po', default=False)
	location_id = Column(Integer,name='location_id')

	enterprise = relationship("Enterprise", backref=backref('po_enterprise', order_by=enterprise_id))
	project = relationship(
		"Project", backref='po_project',
		primaryjoin="and_(PurchaseOrder.enterprise_id==Project.enterprise_id, PurchaseOrder.project_code==Project.id)")
	indent = relationship("Indent", backref=backref('po_indent', order_by=indent_no))
	supplier = relationship("Party", backref=backref('po_supplier', order_by=supplier_id))
	currency = relationship("Currency", backref=backref('po_currency', order_by=currency_code))
	approver = relationship("User", backref='po_approver', foreign_keys=[approved_by])
	drafter = relationship("User", backref='po_drafter', foreign_keys=[drafted_by])
	modified_by = relationship("User", backref='po_modified_by', foreign_keys=[last_modified_by])
	super_modified_user = relationship(
		"User", backref='po_super_modified_user',
		primaryjoin='foreign(User.id) == PurchaseOrder.super_modified_by')

	items = relationship("PurchaseOrderMaterial", backref=backref(
		'po_materials', order_by=po_id), cascade="save-update, merge, delete, delete-orphan")
	taxes = relationship("PurchaseOrderTax", order_by="PurchaseOrderTax.tax_order", backref=backref(
		'po_taxes', order_by=po_id), cascade="save-update, merge, delete, delete-orphan")
	materials_received = relationship(
		"ReceiptMaterial", backref='po_materials_received', order_by="ReceiptMaterial.item_id",
		primaryjoin="and_(ReceiptMaterial.receipt_no==Receipt.receipt_no, Receipt.status>-1, ReceiptMaterial.po_no==PurchaseOrder.po_id)")
	document = relationship(
		"PurchaseOrderDocument", backref="po_documents",
		primaryjoin="and_(PurchaseOrderDocument.po_id==PurchaseOrder.po_id, PurchaseOrderDocument.revised_on==PurchaseOrder.last_modified_on)")
	revisions = relationship(
		"PurchaseOrderDocument", backref="po_revisions", order_by="desc(PurchaseOrderDocument.revised_on)",
		primaryjoin="and_(PurchaseOrderDocument.po_id==PurchaseOrder.po_id, PurchaseOrderDocument.revised_on!=PurchaseOrder.last_modified_on)")
	tags = relationship("PurchaseOrderTag", backref=backref(
		"purchase_order_tags", order_by=po_id), cascade="save-update, merge, delete")
	in_process_dcs = relationship(
		"Invoice", backref="po_dcs_in_process", viewonly=True,
		primaryjoin="and_(PurchaseOrder.po_id==Invoice.job_po_id, PurchaseOrder.enterprise_id==Invoice.enterprise_id, Invoice.status>-1)")

	def __init__(
			self, po_id=None, po_no=None, enterprise_id=None, project_code=None, indent_no=None, supplier_id=None,
			remarks=None, quotation_ref_no=None, quotation_date=None, status=_STATUS_DRAFT, order_date=None,
			purpose='', tax1=0.00, tax2=0.00, total=0.00, closing_remarks='', transport=False, packing=False,
			payment=100, delivery=None, user=None, approved_by=None, pay_in_days=0, pay_against=1, pay_through=1,
			drafted_on=None, last_modified_by=None, approved_on=datetime.now(), sub_number=None, super_modified_on=None,
			super_modified_by=None, currency_code=356, is_blanket_po=False, valid_since=None, valid_till=None,
			issue_to=None, is_mask_po=False, location_id=None, *args, **kwargs):
		super(PurchaseOrder, self).__init__(*args, **kwargs)
		self.po_id = po_id
		self.po_no = po_no
		self.sub_number = sub_number
		self.super_modified_on = super_modified_on
		self.super_modified_by = super_modified_by
		self.enterprise_id = enterprise_id
		self.project_code = project_code
		self.indent_no = indent_no
		self.supplier_id = supplier_id
		self.quotation_date = quotation_date if quotation_date else time.strftime('%Y-%m-%d')
		self.quotation_ref_no = quotation_ref_no
		self.order_date = order_date if order_date else time.strftime('%Y-%m-%d')
		self.closing_remarks = closing_remarks
		self.payment = payment
		self.pay_in_days = pay_in_days
		self.pay_against = pay_against
		self.pay_through = pay_through
		self.purpose = purpose
		self.tax1 = tax1
		self.tax2 = tax2
		self.total = total
		self.transport = transport
		self.user = user
		self.drafted_on = drafted_on if drafted_on else time.strftime('%Y-%m-%d %H:%M:%S')
		self.delivery = delivery if delivery else time.strftime('%Y-%m-%d')
		self.packing = packing
		self.status = status
		self.remarks = remarks
		self.approved_by = approved_by
		self.last_modified_by = last_modified_by
		self.approved_on = approved_on if approved_on else time.strftime('%Y-%m-%d %H:%M:%S')
		self.currency_code = currency_code
		self.is_blanket_po = is_blanket_po
		self.valid_since = valid_since
		self.valid_till = valid_till
		self.issue_to = issue_to
		self.is_mask_po = is_mask_po
		self.location_id = location_id

	def __eq__(self, other):
		return self.po_id == other.po_id

	def __repr__(self):
		return '%s %s %s %s %s %s %s %s' % (
			self.po_id, self.po_no, self.project_code, self.indent_no, self.order_date, self.total, self.user,
			self.approved_by)

	def getStatus(self):
		"""

		:return:
		"""
		return self._STATUS_DICT[self.status]

	@staticmethod
	def generateInternalCode(financial_year=None, po_id=None, po_no=None, po_type=None, sub_number=None):
		"""
		Returns a Code used for internal purpose within the organisation/enterprise.
		It will not have the enterprise_id attached.

		:param financial_year:
		:param po_id:
		:param po_no:
		:param po_type:
		:param sub_number:
		:return:
		"""
		po_type_code = "PO" if po_type == 0 else ("JO" if po_type == 1 else "PP")
		if po_no == '0' or po_no is None:
			return "DRAFT#%s/%s" % (po_type_code, po_id)
		else:
			return '%s/%s/%s%s' % (
				financial_year, po_type_code, ("%06.0f" % int(po_no)) if ("%s" % po_no).isdigit() else po_no,
				"%s" % sub_number.strip() if sub_number else "")

	def getInternalCode(self):
		"""
		Returns a Code used for internal purpose within the organisation/enterprise.
		It will not have the enterprise_id attached.

		:return:
		"""
		return self.generateInternalCode(
			financial_year=self.financial_year, po_id=self.po_id, po_no=self.po_no, po_type=self.type,
			sub_number=self.sub_number)

	def getCode(self):
		"""

		:return:
		"""
		return self.getInternalCode()

	def getPublicValue(self, exclude=(
			'indent_indent_pos', 'po_material_po', 'po_non_stock_po', 'po_tax_po', 'receipt_po', 'receipt_non_stock_po',
			'note_item_po', 'note_ns_item_po', 'party_orders',), extra=()):
		return super(PurchaseOrder, self).getPublicValue(exclude=exclude, extra=extra)

	def isAllMaterialsSupplied(self):
		"""
		Generic status to find if the materials listed in the entire PO has been received and stocked in stores.
		This method compares the PO materials quantity with the sum of all materials received against this PO.

		:return: Boolean value telling if all materials have been received and stocked or not
		"""
		if len(self.materials_received) == 0:
			return False

		received_materials = {}
		for material in self.materials_received:
			try:
				received_materials[material.item_id] += material.accepted_qty
			except KeyError:
				received_materials[material.item_id] = material.accepted_qty

		for material in self.items:
			try:
				if received_materials[material.item_id] < material.quantity:
					return False
			except KeyError:
				return False
		return True

	def materialLastReceivedOn(self):
		"""

		:return:
		"""
		if len(self.materials_received) == 0:
			return datetime.now() + relativedelta(days=1)
		material_last_received_on = None
		for received_material in self.materials_received:
			if received_material.receipt.inward_date:
				if not material_last_received_on or received_material.receipt.inward_date > material_last_received_on:
					material_last_received_on = received_material.receipt.inward_date
		return material_last_received_on

	@property
	def STATUS_APPROVED(self):
		return self._STATUS_APPROVED

	def getMaterialsQuantityMap(self):
		"""

		:return:
		"""
		material_quantity_map = {}
		for material in self.items:
			if material.alternate_unit_id:
				scale_factor = material.alternate_unit.scale_factor
				if scale_factor:
					material_quantity_map[(material.item_id, material.make_id)] = Decimal(material.quantity) / Decimal(scale_factor)
			else:
				material_quantity_map[(material.item_id, material.make_id)] = material.quantity
		for material in self.non_stock_items:
			material_quantity_map[material.item_name] = material.quantity
		return material_quantity_map

	def getTaxSummary(self, po_materials=None):
		"""

		:param po_materials:
		:return:
		"""
		available_cgst = {}
		available_sgst = {}
		available_igst = {}

		for material in po_materials:
			item_quantity = material.quantity

			for material_tax in material.getTaxesOfType("CGST"):
				if material_tax and material_tax.tax.net_rate != 0:
					value = Decimal(item_quantity * material.rate * (100 - material.discount) * Decimal(
						material_tax.tax.net_rate) / (100 * 100)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
					if material_tax.tax.net_rate in available_cgst.keys():
						exsisting_value = available_cgst[material_tax.tax.net_rate]
						amount = exsisting_value + value
						available_cgst[material_tax.tax.net_rate] = amount
					else:
						available_cgst[material_tax.tax.net_rate] = value
			for material_tax in material.getTaxesOfType("SGST"):
				if material_tax and material_tax.tax.net_rate != 0:
					value = Decimal(item_quantity * material.rate * (100 - material.discount) * Decimal(
						material_tax.tax.net_rate) / (100 * 100)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
					if material_tax.tax.net_rate in available_sgst.keys():
						exsisting_value = available_sgst[material_tax.tax.net_rate]
						amount = exsisting_value + value
						available_sgst[material_tax.tax.net_rate] = amount
					else:
						available_sgst[material_tax.tax.net_rate] = value
			for material_tax in material.getTaxesOfType("IGST"):
				if material_tax and material_tax.tax.net_rate != 0:
					value = Decimal(item_quantity * material.rate * (100 - material.discount) * Decimal(
						material_tax.tax.net_rate) / (100 * 100)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
					if material_tax.tax.net_rate in available_igst.keys():
						exsisting_value = available_igst[material_tax.tax.net_rate]
						amount = exsisting_value + value
						available_igst[material_tax.tax.net_rate] = amount
					else:
						available_igst[material_tax.tax.net_rate] = value

		tax_summary = {"cgst_summary": available_cgst, "sgst_summary": available_sgst, "igst_summary": available_igst}
		return tax_summary

	def getConsolidatedTaxValue(self, rate=None, item_quantity=0, item_rate=0.00, item_discount=0):
		"""
		:param rate:
		:param item_quantity:
		:param item_rate:
		:param item_discount:
		:return:
		"""
		value = Decimal(Decimal(item_quantity) * Decimal(item_rate) * (100 - Decimal(item_discount)) * Decimal(rate) / (100 * 100)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
		return Decimal(value)


class PurchaseOrderDocument(Base, AutoSerialize):
	"""
	Finalized document once a PO is approved. This document will never be deleted.
	"""
	__tablename__ = u'purchase_order_document'

	po_id = Column(Integer, ForeignKey('purchase_order.id'), primary_key=True, name='po_id', autoincrement=False)
	is_rejected = Column(Boolean, name='is_rejected', default=False)
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name='enterprise_id')
	document = Column(LargeBinary, name='document')
	revised_on = Column(DateTime, primary_key=True, name='revised_on', autoincrement=False)
	revised_by = Column(Integer, ForeignKey('auth_user.id'), name='revised_by')

	reviser = relationship("User", backref=backref('po_reviser', order_by=revised_by))
	purchase_order = relationship("PurchaseOrder", backref=backref("po_doc_po", order_by=po_id))

	def __init__(
			self, enterprise_id=None, po_id=None, document=None, is_rejected=False, revised_on=None, revised_by=None):
		"""

		:param enterprise_id:
		:param po_id:
		:param document:
		:param is_rejected:
		:param revised_on:
		:param revised_by:
		"""
		self.enterprise_id = enterprise_id
		self.po_id = po_id
		self.document = document
		self.is_rejected = is_rejected
		self.revised_on = revised_on
		self.revised_by = revised_by

	def __repr__(self):
		return '%s %s' % (self.po_id, self.is_rejected)

	def getPublicValue(self, exclude=('po_revisions', 'po_documents',), extra=()):
		return super(PurchaseOrderDocument, self).getPublicValue(exclude=exclude, extra=extra)


class PurchaseOrderMaterial(Base, TaxableItem, AutoSerialize):
	""" Models the PO materials. Refers the Purchase Order and Material objects through Foreign Key relations. """
	__tablename__ = u'purchase_order_material'

	po_id = Column(Integer, ForeignKey('purchase_order.id'), primary_key=True, name='pid', autoincrement=False)
	item_id = Column(Integer, ForeignKey('materials.id'), name='item_id', primary_key=True, autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), primary_key=True, autoincrement=False)
	quantity = Column(DECIMAL(precision=2), name='pur_qty', default=0)
	rate = Column(DECIMAL(precision=5), name='po_price', default=0.00)
	discount = Column(DECIMAL(precision=2), name='discount', default=0.00)
	make_id = Column(Integer, ForeignKey('make.id'), name='make_id', primary_key=True, autoincrement=False)
	is_faulty = Column(Boolean, name='is_faulty', primary_key=True, autoincrement=False)
	alternate_unit_id = Column(Integer, ForeignKey('materials_alternate_units.alternate_unit_id'), name='alternate_unit_id', default=None)
	material = relationship(
		"Material", backref='po_material_material',
		primaryjoin="and_(PurchaseOrderMaterial.item_id == Material.material_id, PurchaseOrderMaterial.enterprise_id == Material.enterprise_id)")
	material_received_against_po = relationship(
		"ReceiptMaterial", backref="po_material_received", cascade="save-update, merge, delete, delete-orphan"
		, primaryjoin=("""and_(PurchaseOrderMaterial.po_id==foreign(ReceiptMaterial.po_no)
						, PurchaseOrderMaterial.item_id==foreign(ReceiptMaterial.item_id))"""))
	purchase_order = relationship("PurchaseOrder", backref=backref('po_material_po', order_by=po_id))
	taxes = relationship(
		"PurchaseOrderMaterialTax", backref="po_material_taxes", cascade="save-update, merge, delete, delete-orphan",
		primaryjoin="and_(PurchaseOrderMaterial.enterprise_id==PurchaseOrderMaterialTax.enterprise_id, PurchaseOrderMaterial.po_id==PurchaseOrderMaterialTax.po_id, PurchaseOrderMaterial.item_id==PurchaseOrderMaterialTax.item_id, PurchaseOrderMaterialTax.make_id==PurchaseOrderMaterial.make_id, PurchaseOrderMaterial.is_faulty==PurchaseOrderMaterialTax.is_faulty)")
	delivery_scedules = relationship(
		"PurchaseOrderMaterialDeliverySchedules", backref="po_material_delivery_schedules", cascade="save-update, merge, delete, delete-orphan",
		primaryjoin="and_(PurchaseOrderMaterial.po_id==PurchaseOrderMaterialDeliverySchedules.po_id, PurchaseOrderMaterial.enterprise_id==PurchaseOrderMaterialDeliverySchedules.enterprise_id, PurchaseOrderMaterial.item_id==PurchaseOrderMaterialDeliverySchedules.item_id, PurchaseOrderMaterial.make_id==PurchaseOrderMaterialDeliverySchedules.make_id)")
	make = relationship(
		"Make", backref='purchase_order_material_make',
		primaryjoin="and_(PurchaseOrderMaterial.make_id==Make.id, PurchaseOrderMaterial.enterprise_id==Make.enterprise_id)")
	alternate_unit = relationship(
		"MaterialAlternateUnit", backref='purchase_order_material_unit',
		primaryjoin="""and_(MaterialAlternateUnit.enterprise_id==PurchaseOrderMaterial.enterprise_id, 
				MaterialAlternateUnit.alternate_unit_id==PurchaseOrderMaterial.alternate_unit_id, 
				MaterialAlternateUnit.item_id==PurchaseOrderMaterial.item_id)""")

	def __init__(
			self, po_id=None, item_id=None, quantity=None, request_qty=None, raised_qty=0, balance_qty=0,
			approved_price=0, rate=0, total_price=0, make_id=None,
			enterprise_id=None, discount=0):
		self.po_id = po_id
		self.item_id = item_id
		self.enterprise_id = enterprise_id
		self.quantity = quantity
		self.request_qty = request_qty
		self.raised_qty = raised_qty
		self.balance_qty = balance_qty
		self.approved_price = approved_price
		self.rate = rate
		self.total_price = total_price
		self.discount = discount
		self.make_id = make_id

	def __repr__(self):
		return '%s %s %s %s %s' % (
			self.po_id, self.item_id, self.enterprise_id,
			self.quantity, self.rate)

	def getPublicValue(self, exclude=('po_materials',), extra=()):
		return super(PurchaseOrderMaterial, self).getPublicValue(exclude=exclude, extra=extra)

	def isMaterialSupplied(self):
		"""
		This method compares PO material quantity with the sum of received materials.
		:return: Boolean value telling if all materials have been received and stocked or not
		"""
		return self.getReceivedQuantity() >= self.quantity

	def getReceivedQuantity(self):
		"""

		:return:
		"""
		received_quantity = 0
		for received_material in self.material_received_against_po:
			received_quantity += received_material.accepted_qty
		return received_quantity

	def materialLastReceivedOn(self):
		"""
		:return:
		"""
		if len(self.purchase_order.materials_received) == 0:
			return datetime.now() + relativedelta(days=1)

		material_last_received_on = self.purchase_order.approved_on - relativedelta(days=100)
		for received_material in self.purchase_order.materials_received:
			if received_material.receipt.inward_date > material_last_received_on:
				material_last_received_on = received_material.receipt.inward_date
		return material_last_received_on

	def getIndentQuantity(self):
		"""

		:return:
		"""
		return self.purchase_order.indent.getMaterialQuantity(self.item_id)

	def getMaterialValueWithoutTax(self):
		"""

		:return:
		"""
		return round((self.quantity * self.rate * (100 - self.discount) / 100), 2)


class PurchaseOrderMaterialDeliverySchedules(Base, AutoSerialize):
	"""

	"""
	__tablename__ = u"purchase_order_material_delivery_schedules"
	po_id = Column(Integer, ForeignKey(
		"purchase_order_material.pid"), primary_key=True, name="po_id", autoincrement=False)
	item_id = Column(Integer, ForeignKey('purchase_order_material.item_id'), name='item_id', primary_key=True, autoincrement=False)
	make_id = Column(Integer, ForeignKey('make.id'), name='make_id', primary_key=True, autoincrement=False)
	quantity = Column(DECIMAL(precision=2), name='qty', primary_key=True, default=0)
	due_date = Column(DateTime, primary_key=True, name='due_date', autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), primary_key=True, autoincrement=False)

	UniqueConstraint(enterprise_id, due_date, make_id, quantity, item_id, po_id)

	po_material = relationship(
		"PurchaseOrderMaterial", backref="po_material_delivery_schedule_po_material",
		primaryjoin="and_(PurchaseOrderMaterialDeliverySchedules.po_id==PurchaseOrderMaterial.po_id, PurchaseOrderMaterialDeliverySchedules.enterprise_id==PurchaseOrderMaterial.enterprise_id, PurchaseOrderMaterialDeliverySchedules.item_id==PurchaseOrderMaterial.item_id, PurchaseOrderMaterialDeliverySchedules.make_id==PurchaseOrderMaterial.make_id)")

	def __init__(self, po_id=None, item_id=None, quantity=0, due_date=None, enterprise_id=None, make_id=None):
		self.po_id = po_id
		self.item_id = item_id
		self.quantity = quantity
		self.due_date = due_date
		self.enterprise_id = enterprise_id
		self.make_id = make_id

	def __repr__(self):
		return '%s %s %s %s %s %s'% (
			self.po_id, self.item_id, self.make_id, self.enterprise_id,
			self.quantity, self.due_date)


class PurchaseOrderMaterialTax(Base):
	"""

	"""
	__tablename__ = u"purchase_order_material_tax"

	po_id = Column(Integer, ForeignKey(
		"purchase_order_material.pid"), primary_key=True, name="po_id", autoincrement=False)
	item_id = Column(Integer, ForeignKey(
		'purchase_order_material.item_id'), name='item_id', primary_key=True, autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey(
		"enterprise.id"), primary_key=True, name="enterprise_id", autoincrement=False)
	make_id = Column(Integer, ForeignKey('make.id'), name='make_id', primary_key=True, autoincrement=False)
	is_faulty = Column(Boolean, name='is_faulty', primary_key=True, autoincrement=False)
	tax_code = Column(String(20), ForeignKey("tax.code"), primary_key=True, name="tax_code", autoincrement=False)

	po_material = relationship(
		"PurchaseOrderMaterial", backref="po_material_tax_po_material",
		primaryjoin="and_(PurchaseOrderMaterialTax.po_id==PurchaseOrderMaterial.po_id, PurchaseOrderMaterialTax.enterprise_id==PurchaseOrderMaterial.enterprise_id, PurchaseOrderMaterialTax.item_id==PurchaseOrderMaterial.item_id, PurchaseOrderMaterialTax.make_id==PurchaseOrderMaterial.make_id, PurchaseOrderMaterialTax.is_faulty==PurchaseOrderMaterial.is_faulty)")
	tax = relationship(
		"Tax", backref="po_material_tax_tax",
		primaryjoin="and_(PurchaseOrderMaterialTax.enterprise_id==Tax.enterprise_id, PurchaseOrderMaterialTax.tax_code==Tax.code)")

	def __init__(self, po_id=None, item_id=None, enterprise_id=None, tax_code=None):
		self.po_id = po_id
		self.item_id = item_id
		self.enterprise_id = enterprise_id
		self.tax_code = tax_code

	def __repr__(self):
		return "%s-%s-%s-%s" % (self.po_id, self.item_id, self.tax_code, self.enterprise_id)


class PurchaseOrderTag(Base):
	""" Tagging model for Purchase Order """
	__tablename__ = u'purchase_order_tags'

	po_id = Column(Integer, ForeignKey('purchase_order.id'), primary_key=True, name='po_id', autoincrement=False)
	tag_id = Column(Integer, ForeignKey('tags.id'), primary_key=True, name='tag_id', autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), primary_key=True, name='enterprise_id', autoincrement=False)

	tag = relationship("Tag", backref=backref("po_tag_tag", order_by=(tag_id, enterprise_id)))
	po = relationship("PurchaseOrder", backref=backref("po_tags_po", order_by=po_id))

	def __init__(self, po_id=None, tag_id=None, enterprise_id=None):
		self.enterprise_id = enterprise_id
		self.po_id = po_id
		self.tag_id = tag_id

	def __repr__(self):
		return "[%s] %s" % (self.po, self.tag)


class PurchaseOrderTax(Base):
	""" Models the PO-Tax_profile relationship. One-to-many association b/w the PO and Tax_profile. """
	__tablename__ = u'purchase_order_tax'

	po_id = Column(Integer, ForeignKey('purchase_order.id'), primary_key=True, name='po_id', autoincrement=False)
	tax_code = Column(String(20), ForeignKey('tax.code'), primary_key=True, name='tax_code', autoincrement=False)
	tax_order = Column(Integer, name='tax_order', default=1)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)

	purchase_order = relationship("PurchaseOrder", backref=backref('po_tax_po', order_by=po_id))
	tax = relationship(
		"Tax", backref='po_tax_tax',
		primaryjoin="and_(PurchaseOrderTax.tax_code==Tax.code, PurchaseOrderTax.enterprise_id==Tax.enterprise_id)")

	def __init__(self, po_id, tax_code, enterprise_id):
		self.po_id = po_id
		self.tax_code = tax_code
		self.enterprise_id = enterprise_id

	def __repr__(self):
		return '%s %s %s' % (self.po_id, self.tax_code, self.tax_order)

	def getPublicValue(self, exclude=('po_taxes',), extra=()):
		return super(PurchaseOrderTax, self).getPublicValue(exclude=exclude, extra=extra)


class Party(Base, AutoSerialize):
	"""
	Data Model Object for the table Party

	Stores the master information that describes Party towards whom orders will be placed and materials will be
	procured from.
	"""
	__tablename__ = u'party_master'

	_DUTY_PASS_REMINDER_FLAG = 1
	_IS_SUPPLIER = 2
	_IS_CUSTOMER = 4

	id = Column(Integer, primary_key=True, name='party_id', autoincrement=True)
	code = Column(String(30), name='party_code', nullable=True)
	name = Column(String(30), name='party_name')
	enterprise_id = Column(Integer, ForeignKey(
		"enterprise.id"), name="enterprise_id", primary_key=True, autoincrement=False)
	address_1 = Column(String(140), name='address1')
	address_2 = Column(String(140), name='address2')
	city = Column(String(20), name='city')
	state = Column(String(20), name='state')
	pin_code = Column(String(10), name='pin_code')
	phone = Column(String(40), name='phone_no')
	contact = Column(String(40), name='contact_person')
	email = Column(String(40), name='email')
	cst_no = Column(String(40), name='cstno')
	gst_no = Column(String(40), name='gstno')
	tin_no = Column(String(40), name='tinno')
	ecc_no = Column(String(40), name='eccno')
	cin_no = Column(String(40), name='cinno')
	tan_no = Column(String(40), name='tanno')
	pan_no = Column(String(40), name='panno')
	config_flags = Column(Integer, name='config_flags')
	payment_credit_days = Column(Integer, name='payment_credit_days')
	receipt_credit_days = Column(Integer, name='receipt_credit_days')
	currency = Column(Integer, ForeignKey("currency.id"), name='currency')
	category_id = Column(Integer, ForeignKey("gst_category.id"), name='category_id')
	port = Column(String(10), name='port')
	primary_contact_id = Column(Integer, name='primary_contact_id')
	country_code = Column(String(4), name='country_code')
	UniqueConstraint(name, code, enterprise_id)

	party_currency = relationship("Currency", backref=backref("currency_name", order_by=currency))
	supplier_ledger_map = relationship(
		"PartyLedgerMap", backref="supplier_ledger_map_ledger", cascade="save-update, merge, delete, delete-orphan",
		uselist=False, primaryjoin="and_(Party.id==PartyLedgerMap.party_id, PartyLedgerMap.is_supplier==1)")
	customer_ledger_map = relationship(
		"PartyLedgerMap", backref="customer_ledger_map_ledger", cascade="save-update, merge, delete, delete-orphan",
		uselist=False, primaryjoin="and_(Party.id==PartyLedgerMap.party_id, PartyLedgerMap.is_supplier==0)")

	purchase_orders = relationship("PurchaseOrder", backref=backref("party_orders", order_by=id))

	enterprise = relationship("Enterprise", backref=backref('party_enterprise', order_by=enterprise_id))
	registration_details = relationship(
		"PartyRegistrationDetail",
		primaryjoin="and_(Party.enterprise_id==PartyRegistrationDetail.enterprise_id, Party.id==PartyRegistrationDetail.party_id)",
		backref="party_party_registration_detail",
		cascade="save-update, merge, delete, delete-orphan")

	primary_contact_details = relationship(
		"PartyContactMap", backref='primary_contact', uselist=False,
		primaryjoin="and_("
					"Party.primary_contact_id == PartyContactMap.contact_id, Party.id == PartyContactMap.party_id, "
					"Party.enterprise_id == PartyContactMap.enterprise_id)")

	def __init__(
			self, code="", name="", address_1="", address_2="", city="", state="", phone_no="", config_flags=0, pin_code=None,
			email="", contact_person="", cst_no="", gst_no="", tin_no="", ecc_no="", cin_no="", tan_no="", primary_contact_id=None,
			pan_no="", enterprise_id=None, payment_credit_days=0, receipt_credit_days=0, currency=356, category_id=None, port=None,
			country_code=None):
		self.code = code
		self.name = name
		self.enterprise_id = enterprise_id
		self.address_1 = address_1
		self.address_2 = address_2
		self.city = city
		self.state = state
		self.pin_code = pin_code
		self.phone = phone_no
		self.email = email
		self.contact = contact_person
		self.cst_no = cst_no
		self.gst_no = gst_no
		self.tin_no = tin_no
		self.ecc_no = ecc_no
		self.cin_no = cin_no
		self.tan_no = tan_no
		self.pan_no = pan_no
		self.config_flags = config_flags if (config_flags or ("%s" % config_flags).isdigit()) else 0
		self.payment_credit_days = payment_credit_days
		self.receipt_credit_days = receipt_credit_days
		self.currency = currency
		self.category_id = category_id
		self.country_code = country_code
		self.port = port
		self.primary_contact_id = primary_contact_id

	def __eq__(self, other):
		return self.party_id == Party(other).party_id

	def __repr__(self):
		return '%s' % [self.id, self.code, self.name]

	def remindDutyPass(self):
		return (self.config_flags & self._DUTY_PASS_REMINDER_FLAG) == self._DUTY_PASS_REMINDER_FLAG

	@staticmethod
	def isSupplierFor(party):
		return (int(party.config_flags) & int(Party._IS_SUPPLIER)) == int(Party._IS_SUPPLIER)

	@staticmethod
	def isCustomerFor(party):
		return (int(party.config_flags) & int(Party._IS_CUSTOMER)) == int(Party._IS_CUSTOMER)

	def isSupplier(self):
		return (int(self.config_flags) & int(self._IS_SUPPLIER)) == int(self._IS_SUPPLIER)

	def isCustomer(self):
		return (int(self.config_flags) & int(self._IS_CUSTOMER)) == int(self._IS_CUSTOMER)

	def setSupplier(self):
		self.config_flags = int(self.config_flags) | int(self._IS_SUPPLIER)

	def setCustomer(self):
		self.config_flags = int(self.config_flags) | int(self._IS_CUSTOMER)

	def getCustomerLedger(self):
		return self.customer_ledger_map.ledger if self.customer_ledger_map else None

	def getSupplierLedger(self):
		return self.supplier_ledger_map.ledger if self.supplier_ledger_map else None

	def getPublicValue(self, extra=(), exclude=(
			'po_supplier', 'receipt_supplier', 'supplier_material_material', 'invoice_party_master', 'party_map')):
		return super(Party, self).getPublicValue(exclude=exclude, extra=extra)


class PartyRegistrationDetail(Base):
	"""
	Entity to hold Party's Registration Detail - One-to-One
	"""
	__tablename__ = u'party_registration_detail'

	enterprise_id = Column(
		Integer, ForeignKey('enterprise.id'), primary_key=True, name='enterprise_id', autoincrement=False)
	party_id = Column(Integer, ForeignKey('party_master.party_id'), primary_key=True, name='party_id', autoincrement=False)
	label_id = Column(Integer, name='label_id', primary_key=True, autoincrement=False)
	label = Column(String(length=30), name='label', primary_key=True, autoincrement=False)
	details = Column(String(length=150), name='details')
	created_on = Column(DateTime, name='created_on')
	created_by = Column(Integer, name='created_by')
	last_modified_on = Column(DateTime, name='last_modified_on')
	last_modified_by = Column(Integer, name='last_modified_by')

	def __init__(
			self, enterprise_id=None, party_id=None, label_id=None, label="", details="", created_by=None, created_on=None,
			last_modified_by=None):
		self.enterprise_id = enterprise_id
		self.party_id = party_id
		self.label_id = label_id
		self.label = label
		self.details = details
		self.created_on = created_on if created_on else time.strftime('%Y-%m-%d %H:%M:%S')
		self.created_by = created_by
		self.last_modified_by = last_modified_by
		self.last_modified_on = time.strftime('%Y-%m-%d %H:%M:%S')


class GSTCategory(Base):
	"""
	Entity to hold GST Category Detail - One-to-One
	"""
	__tablename__ = u'gst_category'

	id = Column(Integer, primary_key=True, name='id', autoincrement=True)
	name = Column(String(length=50), name='name')
	description = Column(String(length=250), name='description')

	def __init__(self, name=None, description=None):
		self.name = name
		self.description = description


class Country(Base):
	"""
	Entity to hold GST Category Detail - One-to-One
	"""
	__tablename__ = u'country'

	code = Column(String(length=4), primary_key=True, name='code')
	name = Column(String(length=50), primary_key=True, name='name')

	def __init__(self, code=None, name=None):
		self.code = code
		self.name = name


class Receipt(Base, Remarkable, Taxable, AutoSerialize):
	"""
	Models the Receipt (GRN and others) details.

	Receipt is an artifact that holds the information regarding a list of materials accepted at the Stores, against an
	Invoice/DC. It takes stock of the material listed in the invoice, quantity of the same received, accepted & rejected.
	It has an upward link with PO, through it to Indents.
	"""
	STATUS_DRAFT = 0
	STATUS_APPROVED = 1
	STATUS_ICD_CHECKED = 2
	STATUS_ICD_VERIFIED = 3
	STATUS_GRN_RETURNED = 4
	STATUS_ICD_RETURNED = 5
	STATUS_PARTY_ACK_PENDING = 6
	STATUS_PARTY_ACKNOWLEDGED = 7
	STATUS_REJECTED = -1
	STATUS_PARTY_REJECTED = -2

	TYPE_CODE = {
		'Others': 'GRN', 'Purchase Order': 'GRN', 'Job Work': 'GRN', 'Job In': 'GRN', 'Delivery Challan': 'GRN',
		'Issues': 'IRN', 'Sales Return': 'SR', 'Note': 'NR'}

	__tablename__ = u'grn'

	receipt_no = Column(Integer, name='grn_no', primary_key=True, autoincrement=True)
	receipt_code = Column(String, name='receipt_no', default=0)
	sub_number = Column(String(1), name='sub_number')
	financial_year = Column(String(length=5), name='financial_year')
	invoice_no = Column(Integer, name='invno')
	invoice_date = Column(DateTime, name='inv_date')
	invoice_value = Column(DECIMAL(precision=2), name="net_inv_value")
	receipt_date = Column(DateTime, name='grn_date')
	inward_no = Column(Integer, name='inward_no')
	inward_date = Column(Integer, name='inward_date')
	supplier_id = Column(ForeignKey('party_master.party_id'), name='party_id')
	received_by = Column(String(20), ForeignKey('auth_user.username'), name='raised_by')
	inspected_by = Column(String(20), name='inspector')
	dr_note_amt = Column(DECIMAL, name='dr_note_amt')
	checked_by = Column(Integer, ForeignKey("auth_user.id"), name='checked_by')
	checked_on = Column(DateTime, name='checked_on')
	verified_by = Column(Integer, ForeignKey("auth_user.id"), name='verified_by')
	verified_on = Column(DateTime, name='verified_on')
	status = Column(Integer, name='status')
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name="enterprise_id")
	approved_on = Column(DateTime, name='approved_on')
	approved_by = Column(Integer, ForeignKey("auth_user.id"), name='approved_by')
	super_modified_on = Column(DateTime, name='super_modified_on')
	super_modified_by = Column(Integer, ForeignKey('auth_user.id'), name='super_modified_by')
	last_modified_on = Column(DateTime, name='last_modified_on')
	last_modified_by = Column(Integer, ForeignKey('auth_user.id'), name='last_modified_by')
	remarks = Column(JSON, name="remarks")
	rejection_remarks = Column(String(300), name="rej_remarks")
	duty_passed = Column(DECIMAL(precision=2), name="duty_passed", default=0.00)
	packing_charges = Column(DECIMAL(precision=2), name="packing_charges", default=0.00)
	transport_charges = Column(DECIMAL(precision=2), name="transport_charges", default=0.00)
	currency_id = Column(Integer, ForeignKey("currency.id"), name="inv_currency_id")
	currency_conversion_rate = Column(DECIMAL(precision=3), name="cur_conversion_rate", default=1.00)
	project_code = Column(Integer, ForeignKey("projects.id"), name="project_code")
	round_off = Column(DECIMAL(precision=2), name="round_off", default=0.00)
	other_charges = Column(DECIMAL(precision=2), name="other_charges", default=0.00)
	ledger_bill_id = Column(Integer, ForeignKey('ledger_bills.id'), name='ledger_bill_id')
	voucher_id = Column(Integer, ForeignKey('voucher.id'), name='voucher_id')
	documents = Column(JSON, name="documents")
	received_against = Column(String(50), name="rec_against")
	audit_remarks = Column(JSON, name="audit_remarks")
	goods_already_received = Column(Boolean, name='goods_already_received', default=False)
	invoice_type = Column(Integer, name='inv_type')
	purchase_account_id = Column(Integer, ForeignKey('account_ledgers.id'), name='purchase_account_id')
	matrecthrough = Column(String(100), name="matrecthrough", default=1)
	ecommerce_gstin = Column(String(15), name="ecommerce_gstin")
	location_id = Column(Integer, ForeignKey("location_master.id"), name="location_id", autoincrement=False, nullable=True)

	enterprise = relationship("Enterprise", backref=backref('grn_enterprise', order_by=enterprise_id))
	project = relationship(
		"Project", backref='grn_project',
		primaryjoin="and_(Receipt.enterprise_id==Project.enterprise_id, Receipt.project_code==Project.id)")
	supplier = relationship("Party", backref=backref('receipt_supplier', order_by=supplier_id))
	approver = relationship("User", backref=backref(
		"receipt_approver", order_by=approved_by), foreign_keys=[approved_by])
	currency = relationship("Currency", backref=backref("receipt_currency", order_by=currency_id))
	receiver = relationship("User", backref="receipt_receiver",
	                        primaryjoin="foreign(User.username) == Receipt.received_by")
	note_creator = relationship("User", backref=backref(
		"receipt_note_creator", order_by=checked_by), foreign_keys=[checked_by])
	note_approver = relationship("User", backref=backref(
		"receipt_note_approver", order_by=verified_by), foreign_keys=[verified_by])
	super_modified_user = relationship(
		"User", backref='receipt_super_modified_user',
		primaryjoin='foreign(User.id) == Receipt.super_modified_by')

	taxes = relationship(
		"ReceiptTax", backref=backref('receipt_taxes', order_by=receipt_no), order_by="ReceiptTax.tax_order",
		cascade="save-update, merge, delete, delete-orphan")
	items = relationship(
		"ReceiptMaterial", backref='receipt_materials',
		primaryjoin="and_(ReceiptMaterial.receipt_no==Receipt.receipt_no, ReceiptMaterial.enterprise_id==Receipt.enterprise_id)",
		cascade="save-update, merge, delete, delete-orphan")
	items_received = relationship(
		"ReceiptMaterial", backref="receipt_materials_received", viewonly=True,
		primaryjoin="and_(ReceiptMaterial.receipt_no==Receipt.receipt_no, ReceiptMaterial.enterprise_id==Receipt.enterprise_id, ReceiptMaterial.dc_id.is_(None))")
	items_returned = relationship(
		"ReceiptMaterial", backref="receipt_materials_returned", viewonly=True,
		primaryjoin="and_(ReceiptMaterial.receipt_no==Receipt.receipt_no, ReceiptMaterial.enterprise_id==Receipt.enterprise_id, ReceiptMaterial.dc_id.isnot(None))")

	# audit_note = relationship("CreditDebitNote", backref=backref(
	# 	"receipt_note", order_by=receipt_no), cascade="save-update, merge, delete, delete-orphan", uselist=False)
	note_receipt_map = relationship(
		"NoteReceiptMap", backref="receipt_map_receipt_note", lazy="subquery",
		primaryjoin="and_(Receipt.receipt_no==NoteReceiptMap.receipt_no, Receipt.enterprise_id==NoteReceiptMap.enterprise_id)", uselist=False)

	attachment = relationship(
		"ReceiptAttachment", backref="receipt_attachment", order_by="desc(ReceiptAttachment.revised_on)",
		primaryjoin="and_(ReceiptAttachment.receipt_no==Receipt.receipt_no, ReceiptAttachment.enterprise_id==Receipt.enterprise_id)",
		cascade="save-update, merge, delete, delete-orphan", uselist=False)
	document = relationship(
		"ReceiptDocument", backref="receipt_document", cascade="save-update, merge, delete, delete-orphan",
		primaryjoin="and_(ReceiptDocument.receipt_no==Receipt.receipt_no, ReceiptDocument.revised_on==Receipt.last_modified_on)")
	document_revisions = relationship(
		"ReceiptDocument", backref="receipt_document_revisions", cascade="save-update, merge, delete, delete-orphan",
		primaryjoin="and_(ReceiptDocument.receipt_no==Receipt.receipt_no, ReceiptDocument.revised_on!=Receipt.last_modified_on)")

	ledger_bill = relationship("LedgerBill", backref=backref(
		"receipt_ledger_bill", order_by=ledger_bill_id), cascade="save-update, merge, delete")
	purchase_account_ledger = relationship("Ledger", backref=backref(
		"receipt_purchase_ledger", order_by=(purchase_account_id, enterprise_id)), viewonly=True)
	voucher = relationship("Voucher", backref=backref(
		"receipt_voucher", order_by=voucher_id), cascade="save-update, merge, delete")

	tags = relationship("ReceiptTag", backref=backref(
		"receipt_receipt_tags", order_by=receipt_no), cascade="save-update, merge, delete")

	invoice_materials = relationship(
		"InvoiceMaterial", backref=backref(
			"invoice_material_grn",
			primaryjoin="and_(InvoiceMaterial.receipt_no==Receipt.receipt_no, InvoiceMaterial.enterprise_id==Receipt.enterprise_id)"),
		cascade="save-update, merge, delete, delete-orphan")
	issue_map = relationship("ReceiptIssueMap", backref=backref(
		"receipt_receipt_issue_map", order_by=receipt_no), cascade="save-update, merge, delete, delete-orphan")
	location = relationship("LocationMaster", backref=backref("receipt_location_master", order_by=location_id))

	def __init__(
			self, receipt_no=None, receipt_code='0', financial_year=None, invoice_no=None, invoice_date=None,
			receipt_date=None, inward_no=0, inward_date=None, supplier_id=None, duty_passed=0.00,
			received_by=None, inspected_by=None, checked_by=None, status=0, enterprise_id=None, approved_on=None,
			approved_by=None, packing_charges=0.00, transport_charges=0.00, currency_id=356, currency_conversion_rate=1,
			round_off=0.000, other_charges=0.00, documents=None, received_against='Others', rejection_remarks=None, audit_remarks=None,
			sub_number=None, super_modified_on=None, super_modified_by=None, dr_note_amt=0.00, project_code=None,
			last_modified_on=datetime.now(), last_modified_by=None, goods_already_received=False, invoice_type=1,
			matrecthrough=None, ecommerce_gstin=None, location_id=None, *args, **kwargs):
		super(Receipt, self).__init__(*args, **kwargs)
		self.receipt_no = receipt_no
		self.receipt_code = receipt_code
		self.sub_number = sub_number
		self.super_modified_on = super_modified_on
		self.super_modified_by = super_modified_by
		self.last_modified_on = last_modified_on
		self.last_modified_by = last_modified_by
		self.financial_year = financial_year if financial_year else time.strftime('%Y')
		self.receipt_date = receipt_date if receipt_date else time.strftime('%Y-%m-%d %H:%M:%S')
		self.invoice_no = invoice_no
		self.invoice_date = invoice_date if invoice_date else time.strftime('%Y-%m-%d')
		self.inward_no = inward_no
		self.inward_date = inward_date if inward_date else time.strftime('%Y-%m-%d')
		self.enterprise_id = enterprise_id
		self.supplier_id = supplier_id
		self.received_by = received_by
		self.inspected_by = inspected_by
		self.checked_by = checked_by
		self.status = status
		self.approved_on = approved_on
		self.approved_by = approved_by
		self.duty_passed = duty_passed
		self.packing_charges = packing_charges
		self.transport_charges = transport_charges
		self.currency_id = currency_id
		self.currency_conversion_rate = currency_conversion_rate
		self.round_off = round_off
		self.other_charges = other_charges
		self.documents = documents
		self.received_against = received_against
		self.rejection_remarks = rejection_remarks
		self.audit_remarks = audit_remarks
		self.dr_note_amt = dr_note_amt
		self.project_code = project_code
		self.goods_already_received = goods_already_received
		self.invoice_type = invoice_type
		self.matrecthrough = matrecthrough
		self.ecommerce_gstin = ecommerce_gstin
		self.location_id = location_id

	def __repr__(self):
		return "%s" % self.getCode()

	@staticmethod
	def generateReceiptCode(
			receipt_no=None, receipt_code=None, received_against=None, financial_year=None, sub_number=None):
		if received_against == "Note":
			return receipt_code
		typecode = Receipt.TYPE_CODE[received_against]
		if receipt_code != "0":
			return "%s/%s/%s%s" % (
				financial_year, typecode, ("%s" % receipt_code).zfill(6),
				"%s" % sub_number.strip() if sub_number else "")
		else:
			return "TMP#%s-%s" % (typecode, receipt_no)

	def getCode(self):
		return self.generateReceiptCode(
			receipt_no=self.receipt_no, receipt_code=self.receipt_code, received_against=self.received_against,
			financial_year=self.financial_year, sub_number=self.sub_number)

	def getTotalMaterialValueForDC(self, dc=None):
		material_value = 0
		if dc:
			for material in self.items:
				if dc == material.dc_id:
					material_value += material.quantity * material.rate * (100 - material.discount) / 100
			for material in self.non_stock_items:
				if dc == material.dc_id:
					material_value += material.quantity * material.rate * (100 - material.discount) / 100
		return material_value

	def getTotalMaterialValue(self):
		"""

		:return:
		"""
		material_value = 0
		for material in self.items:
			material_value += material.quantity * material.rate * (100 - material.discount) / 100
		for material in self.non_stock_items:
			material_value += material.quantity * material.rate * (100 - material.discount) / 100
		return material_value

	def getTaxValuesForDC(self, discounts_applied=True, dc=None):
		"""
		Creates & Returns a Map of tax_codes & net value against each tax_code

		:param discounts_applied:
		:param dc:
		:return: a dict [tax_code: tax_value]
		"""
		sorted_taxes = self.getTaxes()
		tax_values = {}
		if dc:
			for item in self.items:
				if dc == item.dc_id:
					self._extractMaterialTaxValues(
						item=item, sorted_taxes=sorted_taxes, tax_values=tax_values, discount_applied=discounts_applied)
			for ns_item in self.non_stock_items:
				if dc == ns_item.dc_id:
					self._extractMaterialTaxValues(
						item=ns_item, sorted_taxes=sorted_taxes, tax_values=tax_values,
						discount_applied=discounts_applied)
		packing_tax = 0
		for inv_tax in sorted_taxes:
			if inv_tax.tax.is_compound:
				tax_value = Decimal((packing_tax + Decimal(self.packing_charges)) * Decimal(inv_tax.tax.net_rate / 100)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
			else:
				tax_value = Decimal(Decimal(self.packing_charges) * Decimal(inv_tax.tax.net_rate / 100)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
			if inv_tax.tax.code in tax_values:
				tax_values[inv_tax.tax.code] += Decimal(round(tax_value, 2))
			else:
				tax_values[inv_tax.tax.code] = Decimal(round(tax_value, 2))
			packing_tax += Decimal(tax_value)
		return tax_values

	def getTaxValues(self, discounts_applied=True, dc=None):
		"""

		:return:
		"""
		# Tax Calculation
		sorted_taxes = self.getTaxes()
		tax_values = super(Receipt, self).getTaxValues(discounts_applied=discounts_applied)
		packing_tax = 0
		for inv_tax in sorted_taxes:
			if inv_tax.tax.is_compound:
				tax_value = Decimal((packing_tax + Decimal(self.packing_charges)) * Decimal(inv_tax.tax.net_rate / 100)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
			else:
				tax_value = Decimal(Decimal(self.packing_charges) * Decimal(inv_tax.tax.net_rate / 100)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
			if inv_tax.tax.code in tax_values:
				tax_values[inv_tax.tax.code] += Decimal(round(tax_value, 2))
			else:
				tax_values[inv_tax.tax.code] = Decimal(round(tax_value, 2))
			packing_tax += Decimal(tax_value)
		return tax_values

	def getConsolidatedTaxListForDC(self, dc=None):
		"""
		:return: a set that contains all taxes associated at both Document & Item level
		"""
		consolidated_tax_list = set()
		for tax in self.getTaxes():
			consolidated_tax_list.add(tax.tax)
		if dc:
			for item in self.items:
				if dc == item.dc_id:
					for tax in item.taxes:
						consolidated_tax_list.add(tax.tax)
			for item in self.non_stock_items:
				if dc == item.dc_id:
					for tax in item.taxes:
						consolidated_tax_list.add(tax.tax)
		return consolidated_tax_list

	def hasQIR(self):
		"""
		Checks if the document has QIR log recorded & returns a Boolean value signifying the same
		:return:
		"""
		for item in self.items:
			if item.inspection_log and (len(item.inspection_log) > 0):
				return True
		return False


class ReceiptDocument(Base, AutoSerialize):
	""" Data Model that persists the Receipt document in both PDF & Text format. """
	__tablename__ = u'grn_document'

	receipt_no = Column(Integer, ForeignKey('grn.grn_no'), primary_key=True, name='grn_no', autoincrement=False)
	receipt_txt = Column(LargeBinary, name='receipt_txt')
	receipt_doc = Column(LargeBinary, name='receipt_pdf')
	enterprise_id = Column(Integer, name='enterprise_id', nullable=False)
	revised_on = Column(DateTime, primary_key=True, name='revised_on', autoincrement=False)
	revised_by = Column(Integer, ForeignKey('auth_user.id'), name='revised_by')

	receipt = relationship("Receipt", backref=backref("receipt_document_receipt", order_by=receipt_no))

	def __init__(
			self, receipt_no=None, receipt_txt='', receipt_doc=None, enterprise_id=None, revised_on=None,
			revised_by=None):
		self.receipt_no = receipt_no
		self.receipt_doc = receipt_doc
		self.receipt_txt = receipt_txt
		self.enterprise_id = enterprise_id
		self.revised_on = revised_on
		self.revised_by = revised_by

	def __repr__(self):
		return '%s\n%s' % (self.receipt_no, self.receipt_txt)


class ReceiptAttachment(Base, AutoSerialize):
	""" Data Model that persists the Receipt attachment with its extension. Persists invoice of the supplier"""
	__tablename__ = u'grn_attachment'

	receipt_no = Column(Integer, ForeignKey('grn.grn_no'), primary_key=True, name='grn_no', autoincrement=False)
	enterprise_id = Column(Integer, name='enterprise_id', nullable=False)
	invoice_doc_ext = Column(String(4), name='invoice_doc_ext', nullable=True)
	revised_on = Column(DateTime, primary_key=True, name='revised_on', autoincrement=False)
	revised_by = Column(Integer, ForeignKey('auth_user.id'), name='revised_by')
	attachment_id = Column(Integer, ForeignKey('attachment.id'), name='attachment_id')
	receipt = relationship("Receipt", backref=backref("receipt_document_invoice", order_by=receipt_no))
	attachment = relationship(
		"Attachment", backref="document_invoice", order_by="Attachment.attachment_id",
		primaryjoin="and_(ReceiptAttachment.attachment_id==Attachment.attachment_id, ReceiptAttachment.enterprise_id==Attachment.enterprise_id)",
		uselist=False)

	def __init__(
			self, receipt_no=None, enterprise_id=None, invoice_doc_ext='',
			revised_on=None, revised_by=None, attachment_id=None):
		self.receipt_no = receipt_no
		self.enterprise_id = enterprise_id
		self.invoice_doc_ext = invoice_doc_ext
		self.revised_on = revised_on
		self.revised_by = revised_by
		self.attachment_id = attachment_id


class NoteAttachment(Base, AutoSerialize):
	""" Data Model that persists the Note attachment with its extension. Persists invoice of the supplier"""
	__tablename__ = u'crdrnote_attachment'

	note_id = Column(Integer, ForeignKey('crdrnote.grn_no'), primary_key=True, name='note_id', autoincrement=False)
	enterprise_id = Column(Integer, name='enterprise_id', nullable=False)
	invoice_doc_ext = Column(String(4), name='invoice_doc_ext', nullable=True)
	revised_on = Column(DateTime, primary_key=True, name='revised_on', autoincrement=False)
	revised_by = Column(Integer, ForeignKey('auth_user.id'), name='revised_by')
	attachment_id = Column(Integer, ForeignKey('attachment.id'), name='attachment_id')
	receipt = relationship("CreditDebitNote", backref=backref("note_document_invoice", order_by=note_id))

	attachment = relationship(
		"Attachment", backref="note_document_note", order_by="Attachment.attachment_id",
		primaryjoin="and_(NoteAttachment.attachment_id==Attachment.attachment_id, NoteAttachment.enterprise_id==Attachment.enterprise_id)",
		uselist=False)

	def __init__(
			self, note_id=None, enterprise_id=None, invoice_doc_ext='',
			revised_on=None, revised_by=None, attachment_id=None):
		self.note_id = note_id
		self.enterprise_id = enterprise_id
		self.invoice_doc_ext = invoice_doc_ext
		self.revised_on = revised_on
		self.revised_by = revised_by
		self.attachment_id = attachment_id


class ReceiptIssueMap(Base, AutoSerialize):
	""" Data Model that persists the Receipt Issue to with its extension"""
	__tablename__ = u'grn_issue_map'

	receipt_no = Column(Integer, ForeignKey('grn.grn_no'), primary_key=True, name='grn_no', autoincrement=False)
	issue_no = Column(Integer, ForeignKey('invoice.id'), name='issue_id', primary_key=True, autoincrement=False)
	enterprise_id = Column(Integer, name='enterprise_id', nullable=False)

	receipt = relationship("Receipt", backref=backref("receipt_issue_to_map", order_by=receipt_no))

	def __init__(
			self, receipt_no=None, enterprise_id=None, issue_no=None):
		self.receipt_no = receipt_no
		self.enterprise_id = enterprise_id
		self.issue_no = issue_no

	def __repr__(self):
		return '%s-%s' % (self.receipt_no, self.issue_no)


class Attachment(Base, AutoSerialize):
	""" Data Model that persists the Receipt attachment with its extension. Persists invoice of the supplier"""
	__tablename__ = u'attachment'

	attachment_id = Column(Integer, name='id', nullable=False, primary_key=True, autoincrement=True)
	label = Column(String(40), name='description', nullable=False)
	file = Column(LargeBinary, name='file', nullable=False)
	file_ext = Column(String(4), name='ext', nullable=False)
	uploaded_on = Column(DateTime, primary_key=True, name='uploaded_on', autoincrement=False)
	# uploaded_by = Column(Integer, ForeignKey('auth_user.id'), name='uploaded_by')
	uploaded_by = Column(String(40), name='uploaded_by', nullable=False)
	enterprise_id = Column(Integer, name='enterprise_id', nullable=False)
	gcs_key = Column(String(150), name='gcs_key', nullable=True)

	def __init__(
			self, attachment_id=None, label=None, enterprise_id=None, file=None, file_ext='',
			uploaded_on=None, uploaded_by=None, gcs_key=None):
		self.attachment_id = attachment_id
		self.label = label
		self.file = file
		self.file_ext = file_ext
		self.uploaded_on = uploaded_on
		self.uploaded_by = uploaded_by
		self.enterprise_id = enterprise_id
		self.gcs_key = gcs_key


class CreditDebitNoteDocument(Base, AutoSerialize):
	""" Data Model that persists the Debit/Credit Note document that to be generated."""
	__tablename__ = u'crdr_note_document'

	note_id = Column(Integer, ForeignKey('crdrnote.grn_no'), primary_key=True, name='grn_no', autoincrement=False)
	enterprise_id = Column(Integer, name='enterprise_id', nullable=False)
	note_doc = Column(LargeBinary, name='note_doc', nullable=True)
	revised_on = Column(DateTime, primary_key=True, name='revised_on', autoincrement=False)
	revised_by = Column(Integer, ForeignKey('auth_user.id'), name='revised_by')

	note = relationship("CreditDebitNote", backref=backref("note_document_note", order_by=note_id))

	def __init__(
			self, note_id=None, enterprise_id=None, note_doc=None, revised_on=None, revised_by=None):
		self.note_id = note_id
		self.enterprise_id = enterprise_id
		self.note_doc = note_doc
		self.revised_on = revised_on
		self.revised_by = revised_by

	def __repr__(self):
		return '%s-note' % self.note_id


class ReceiptMaterial(Base, AutoSerialize, TaxableItem):
	""" Persists the Receipt's Detailed particulars. """

	__tablename__ = u'grn_material'

	receipt_no = Column(Integer, ForeignKey('grn.grn_no'), name='grnNumber', primary_key=True, autoincrement=False)
	item_id = Column(Integer, ForeignKey('materials.id'), name='item_id', primary_key=True, autoincrement=False)
	enterprise_id = Column(
		Integer, ForeignKey('enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	po_no = Column(Integer, ForeignKey('purchase_order.id'), autoincrement=False, name='po_no', nullable=True)
	quantity = Column(DECIMAL, name='dc_qty')
	accepted_qty = Column(DECIMAL, name='acc_qty')
	received_qty = Column(DECIMAL, name='rec_qty')
	rate = Column(DECIMAL(precision=5), name='inv_rate')
	discount = Column(DECIMAL, name='discount')
	make_id = Column(Integer, ForeignKey('make.id'), name='make_id', primary_key=True, autoincrement=False)
	is_faulty = Column(Integer, name='is_faulty', primary_key=True, autoincrement=False)
	dc_id = Column(Integer, ForeignKey('invoice.id'),  autoincrement=False, name='dc_id', nullable=True)
	received_grn_id = Column(Integer, ForeignKey('grn.grn_no'),  autoincrement=False, name="rec_grn_id", nullable=True)
	oa_id = Column(Integer, ForeignKey('order_acknowledgement.id'),  autoincrement=False, name="oa_id", nullable=True)
	alternate_unit_id = Column(Integer, ForeignKey(
		'materials_alternate_units.alternate_unit_id'), name='alternate_unit_id', default=None)
	inspection_log = Column(JSON, name='inspection_log')
	entry_order = Column(Integer, name='entry_order', primary_key=True, autoincrement=False)
	hsn_code = Column(String(20), name='hsn_code')
	location_id = Column(Integer, name='location_id', autoincrement=False, nullable=True)

	UniqueConstraint(enterprise_id, is_faulty, make_id, dc_id, receipt_no, item_id, po_no, received_grn_id, oa_id)

	receipt = relationship(
		"Receipt", backref=backref('receipt_material_receipt'),
		primaryjoin="""and_(Receipt.receipt_no == ReceiptMaterial.receipt_no, Receipt.enterprise_id == ReceiptMaterial.enterprise_id)"""
	)
	receipt_dc = relationship(
		"Receipt", backref=backref('receipt_material_receipt_dc'),
		primaryjoin="""and_(Receipt.receipt_no == ReceiptMaterial.received_grn_id, Receipt.enterprise_id == ReceiptMaterial.enterprise_id)"""
	)
	material = relationship(
		"Material", backref="receipt_material_material",
		primaryjoin="""and_(ReceiptMaterial.enterprise_id==Material.enterprise_id,
							ReceiptMaterial.item_id==Material.material_id)""")
	item = relationship(
		"Material", backref="receipt_material_item",
		primaryjoin="""and_(ReceiptMaterial.enterprise_id==Material.enterprise_id,
							ReceiptMaterial.item_id==Material.material_id)""")
	po = relationship("PurchaseOrder", backref=backref('receipt_po', order_by=po_no))
	taxes = relationship(
		"ReceiptMaterialTax", backref=backref("receipt_material_taxes"),
		primaryjoin="""and_(ReceiptMaterial.receipt_no==ReceiptMaterialTax.receipt_no,
							ReceiptMaterial.item_id==ReceiptMaterialTax.item_id,
							ReceiptMaterial.enterprise_id==ReceiptMaterialTax.enterprise_id,
							ReceiptMaterial.make_id==ReceiptMaterialTax.make_id,
							ReceiptMaterial.is_faulty==ReceiptMaterialTax.is_faulty,														
							or_(ReceiptMaterial.dc_id==ReceiptMaterialTax.dc_id,
								and_(ReceiptMaterial.dc_id.is_(None), ReceiptMaterialTax.dc_id.is_(None))),
							or_(ReceiptMaterial.received_grn_id==ReceiptMaterialTax.received_grn_id,
								and_(ReceiptMaterial.received_grn_id.is_(None), ReceiptMaterialTax.received_grn_id.is_(None))),
							or_(ReceiptMaterial.oa_id==ReceiptMaterialTax.oa_id,
								and_(ReceiptMaterial.oa_id.is_(None), ReceiptMaterialTax.oa_id.is_(None))),
							or_(ReceiptMaterial.po_no==ReceiptMaterialTax.po_id,
								and_(ReceiptMaterial.po_no.is_(None), ReceiptMaterialTax.po_id.is_(None))))""",
		cascade="save-update, merge, delete-orphan, delete", single_parent=True)
	make = relationship(
		"Make", backref='receipt_material_make',
		primaryjoin="and_(ReceiptMaterial.make_id==Make.id, ReceiptMaterial.enterprise_id==Make.enterprise_id)")
	dc = relationship("Invoice", backref=backref("receipt_material_dc", order_by=dc_id))
	oa = relationship("OA", backref=backref("receipt_material_oa", order_by=oa_id))
	alternate_unit = relationship(
		"MaterialAlternateUnit", backref='grn_material_alternate_unit',
		primaryjoin="""and_(MaterialAlternateUnit.enterprise_id==ReceiptMaterial.enterprise_id, 
			MaterialAlternateUnit.alternate_unit_id==ReceiptMaterial.alternate_unit_id, 
			MaterialAlternateUnit.item_id==ReceiptMaterial.item_id)""")
	rejections = relationship(
		"ReceiptMaterialRejection", backref=backref("receipt_material_rejections"),
		primaryjoin="""and_(
			ReceiptMaterial.receipt_no==ReceiptMaterialRejection.grn_id,
			ReceiptMaterial.item_id==ReceiptMaterialRejection.item_id,
			ReceiptMaterial.enterprise_id==ReceiptMaterialRejection.enterprise_id,
			or_(ReceiptMaterial.po_no==ReceiptMaterialRejection.po_no,
				and_(ReceiptMaterial.po_no.is_(None), ReceiptMaterialRejection.po_no.is_(None))),
			ReceiptMaterial.make_id==ReceiptMaterialRejection.make_id,
			ReceiptMaterial.is_faulty==ReceiptMaterialRejection.is_faulty,			
			or_(ReceiptMaterial.dc_id==ReceiptMaterialRejection.dc_id,
				and_(ReceiptMaterial.dc_id.is_(None), ReceiptMaterialRejection.dc_id.is_(None))),
			or_(ReceiptMaterial.received_grn_id==ReceiptMaterialRejection.rec_grn_id,
				and_(ReceiptMaterial.received_grn_id.is_(None), ReceiptMaterialRejection.rec_grn_id.is_(None))),
			or_(ReceiptMaterial.oa_id==ReceiptMaterialRejection.oa_id,
				and_(ReceiptMaterial.oa_id.is_(None), ReceiptMaterialRejection.oa_id.is_(None))))""",
		cascade="save-update, merge, delete-orphan, delete", single_parent=True)

	def __init__(
			self, receipt_no=None, item_id=None, enterprise_id=None, quantity=0, rate=0, received_qty=0, hsn_code=None,
			po_no=None, discount=0, is_faulty=0, make_id=None, accepted_qty=0, received_grn_id=None, oa_id=None, dc_id=None,
			location_id=None):
		self.receipt_no = receipt_no
		self.po_no = po_no
		self.item_id = item_id
		self.enterprise_id = enterprise_id
		self.quantity = quantity
		self.rate = rate
		self.received_qty = received_qty
		self.discount = discount
		self.is_faulty = is_faulty
		self.make_id = make_id
		self.accepted_qty = accepted_qty
		self.received_grn_id = received_grn_id
		self.oa_id = oa_id
		self.dc_id = dc_id
		self.hsn_code = hsn_code
		self.location_id = location_id

	def __repr__(self):
		return "%s" % [self.enterprise_id, self.receipt_no, self.po_no, self.item_id, self.received_grn_id]

	def getPublicValue(self, exclude=('receipt_materials', 'po_materials_received',), extra=()):
		return super(ReceiptMaterial, self).getPublicValue(exclude=exclude, extra=extra)


class ReceiptMaterialRejection(Base, AutoSerialize):
	""" Class to model the row-data of 'grn_materials_rejection' table"""
	__tablename__ = u'grn_material_rejection'

	grn_id = Column(ForeignKey('grn_material.grnNumber'), name='grnNumber', primary_key=True, autoincrement=False)
	item_id = Column(ForeignKey('grn_material.item_id'), name='item_id', primary_key=True, autoincrement=False)
	enterprise_id = Column(ForeignKey(
		'grn_material.enterprise_id'), name='enterprise_id', primary_key=True, autoincrement=False)
	po_no = Column(ForeignKey('grn_material.po_no'), autoincrement=False, name='po_no', nullable=True)
	make_id = Column(ForeignKey('grn_material.make_id'), name='make_id', primary_key=True, autoincrement=False)
	is_faulty = Column(ForeignKey('grn_material.is_faulty'), name='is_faulty', primary_key=True, autoincrement=False)
	dc_id = Column(ForeignKey('grn_material.dc_id'), autoincrement=False, name='dc_id', nullable=True)
	rec_grn_id = Column(ForeignKey(
		'grn_material.rec_grn_id'), name="rec_grn_id", nullable=True)
	oa_id = Column(ForeignKey('grn_material.oa_id'), autoincrement=False, name="oa_id", nullable=True)
	reason = Column(String(150), name='reason', primary_key=True, autoincrement=False)
	debit = Column(DECIMAL, name='debit')
	quantity = Column(DECIMAL, name='quantity')

	__table_args__ = (ForeignKeyConstraint((
		'grnNumber', 'item_id', 'enterprise_id', 'po_no', 'make_id', 'is_faulty', 'dc_id', 'rec_grn_id', 'oa_id'), [
		'grn_material.grnNumber', 'grn_material.item_id', 'grn_material.enterprise_id', 'grn_material.po_no',
		'grn_material.make_id', 'grn_material.is_faulty', 'grn_material.dc_id', 'grn_material.rec_grn_id',
		'grn_material.oa_id']),)

	UniqueConstraint(enterprise_id, is_faulty, make_id, dc_id, grn_id, item_id, po_no, rec_grn_id, oa_id, reason)

	receipt_material = relationship(
		"ReceiptMaterial", backref='rejection_refs_receipt_materials',
		primaryjoin="""and_(ReceiptMaterialRejection.grn_id==ReceiptMaterial.receipt_no,
			ReceiptMaterialRejection.item_id==ReceiptMaterial.item_id,
			ReceiptMaterialRejection.enterprise_id==ReceiptMaterial.enterprise_id,
			or_(ReceiptMaterial.po_no==ReceiptMaterialRejection.po_no,
				and_(ReceiptMaterial.po_no.is_(None), ReceiptMaterialRejection.po_no.is_(None))),
			ReceiptMaterialRejection.make_id==ReceiptMaterial.make_id,
			ReceiptMaterialRejection.is_faulty==ReceiptMaterial.is_faulty,			
			or_(ReceiptMaterial.dc_id==ReceiptMaterialRejection.dc_id,
				and_(ReceiptMaterial.dc_id.is_(None), ReceiptMaterialRejection.dc_id.is_(None))),			
			or_(ReceiptMaterial.received_grn_id==ReceiptMaterialRejection.rec_grn_id,
				and_(ReceiptMaterial.received_grn_id.is_(None), ReceiptMaterialRejection.rec_grn_id.is_(None))),
			or_(ReceiptMaterial.oa_id==ReceiptMaterialRejection.oa_id,
				and_(ReceiptMaterial.oa_id.is_(None), ReceiptMaterialRejection.oa_id.is_(None))))""", uselist=False)

	def __init__(
			self, grn_id=None, item_id=None, enterprise_id=None, quantity=0, reason=None, debit=0, po_no=None,
			is_faulty=0, make_id=None, rec_grn_id=None, oa_id=None, dc_id=0):
		self.grn_id = grn_id
		self.po_no = po_no
		self.item_id = item_id
		self.enterprise_id = enterprise_id
		self.is_faulty = is_faulty
		self.make_id = make_id
		self.rec_grn_id = rec_grn_id
		self.oa_id = oa_id
		self.dc_id = dc_id
		self.reason = reason
		self.quantity = quantity
		self.debit = debit


class ReceiptMaterialTax(Base):
	""" Persists the Receipt's Detailed particulars. """

	__tablename__ = u'grn_material_tax'

	receipt_no = Column(
		Integer, ForeignKey('grn_material.grnNumber'), name='grn_no', primary_key=True, autoincrement=False)
	item_id = Column(Integer, ForeignKey('grn_material.item_id'), name='item_id', primary_key=True, autoincrement=False)
	enterprise_id = Column(
		Integer, ForeignKey('enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	po_id = Column(
		Integer, ForeignKey('grn_material.po_no'), name='po_id', autoincrement=False, nullable=True)
	dc_id = Column(Integer, ForeignKey('grn_material.dc_id'), autoincrement=False, name='dc_id', nullable=True)
	make_id = Column(Integer, ForeignKey("make.id"), name="make_id", primary_key=True, autoincrement=False)
	is_faulty = Column(Integer, name='is_faulty', primary_key=True, autoincrement=False)
	tax_code = Column(String(20), ForeignKey('tax.code'), name='tax_code', primary_key=True, autoincrement=False)
	received_grn_id = Column(Integer, name="rec_grn_id", nullable=True)
	oa_id = Column(
		Integer, ForeignKey('order_acknowledgement.id'), autoincrement=False, name="oa_id", nullable=True)

	UniqueConstraint(receipt_no, item_id, po_id, enterprise_id, tax_code, make_id, is_faulty, dc_id, received_grn_id, oa_id)
	receipt_material = relationship(
		"ReceiptMaterial", backref="receipt_material_tax_receipt_material",
		primaryjoin="""and_(ReceiptMaterial.receipt_no==ReceiptMaterialTax.receipt_no,
							ReceiptMaterial.item_id==ReceiptMaterialTax.item_id,
							ReceiptMaterial.enterprise_id==ReceiptMaterialTax.enterprise_id,
							ReceiptMaterial.make_id==ReceiptMaterialTax.make_id,
							ReceiptMaterial.is_faulty==ReceiptMaterialTax.is_faulty,
							or_(ReceiptMaterial.po_no==ReceiptMaterialTax.po_id,
								and_(ReceiptMaterial.po_no.is_(None), ReceiptMaterialTax.po_id.is_(None))),							
							or_(ReceiptMaterial.dc_id==ReceiptMaterialTax.dc_id
							, and_(ReceiptMaterial.dc_id.is_(None), ReceiptMaterialTax.dc_id.is_(None))),
							or_(ReceiptMaterial.received_grn_id==ReceiptMaterialTax.received_grn_id
							, and_(ReceiptMaterial.received_grn_id.is_(None)
							, ReceiptMaterialTax.received_grn_id.is_(None))),
							or_(ReceiptMaterial.oa_id==ReceiptMaterialTax.oa_id
							, and_(ReceiptMaterial.oa_id.is_(None), ReceiptMaterialTax.oa_id.is_(None))))""")
	tax = relationship(
		"Tax", backref='receipt_material_tax_tax',
		primaryjoin="and_(ReceiptMaterialTax.tax_code==Tax.code, ReceiptMaterialTax.enterprise_id==Tax.enterprise_id)")

	def __init__(
			self, receipt_no=None, item_id=None, enterprise_id=None, po_id=None, dc_id=None, make_id=None
			, tax_code=None, is_faulty=0, received_grn_id=None, oa_id=None):
		self.receipt_no = receipt_no
		self.po_id = po_id
		self.item_id = item_id
		self.enterprise_id = enterprise_id
		self.tax_code = tax_code
		self.dc_id = dc_id
		self.received_grn_id = received_grn_id
		self.make_id = make_id
		self.is_faulty = is_faulty
		self.oa_id = oa_id

	def __repr__(self):
		return "%s - %s - %s -%s" % (self.enterprise_id, self.receipt_no, self.po_id, self.item_id)


class ReceiptTag(Base):
	""" Tagging model for Receipts """
	__tablename__ = u'receipt_tags'

	receipt_id = Column(Integer, ForeignKey('grn.grn_no'), primary_key=True, name='receipt_id', autoincrement=False)
	tag_id = Column(Integer, ForeignKey('tags.id'), primary_key=True, name='tag_id', autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), primary_key=True, name='enterprise_id', autoincrement=False)

	tag = relationship("Tag", backref=backref("receipt_tag_tag", order_by=(tag_id, enterprise_id)))
	receipt = relationship("Receipt", backref=backref("receipt_tags_receipt", order_by=receipt_id))

	def __init__(self, issue_id=None, tag_id=None, enterprise_id=None):
		self.enterprise_id = enterprise_id
		self.receipt_id = issue_id
		self.tag_id = tag_id

	def __repr__(self):
		return "[%s] %s" % (self.receipt, self.tag)


class ReceiptTax(Base):
	""" Models the GRN-Tax_profile relationship. One-to-many association b/w the PO and Tax_profile. """
	__tablename__ = u'grn_tax'

	receipt_no = Column(Integer, ForeignKey('grn.grn_no'), primary_key=True, name='grn_no', autoincrement=False)
	tax_code = Column(String(20), ForeignKey('tax.code'), primary_key=True, name='tax_code', autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey(
		"enterprise.id"), primary_key=True, name="enterprise_id", autoincrement=False)
	tax_order = Column(Integer, name='tax_order', default=1)

	grn = relationship("Receipt", backref=backref('grn_tax_grn', order_by=receipt_no))
	tax = relationship(
		"Tax", backref='grn_tax_tax',
		primaryjoin="and_(ReceiptTax.tax_code == Tax.code, ReceiptTax.enterprise_id == Tax.enterprise_id)")

	def __init__(self, receipt_no=None, tax_code=None, enterprise_id=None, tax_order=1):
		self.receipt_no = receipt_no
		self.tax_code = tax_code
		self.enterprise_id = enterprise_id
		self.tax_order = tax_order

	def __repr__(self):
		return '%s %s %s %s' % (self.receipt_no, self.tax_code, self.enterprise_id, self.tax_order)

	def getTaxLedger(self):
		return self.tax.getLedger()

	def getTaxLedgerId(self):
		return self.tax.getInputLedgerId()


class SubTax(Base, AutoSerialize):
	""" Data Model Object for the table SubTax """
	__tablename__ = u'sub_tax'

	parent_code = Column(String(20), ForeignKey('tax.code'), name='parent_code', autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey("enterprise.id"), name="enterprise_id", autoincrement=False)
	name = Column(String(100), name='name', autoincrement=False)
	rate = Column(DECIMAL, name='rate')

	PrimaryKeyConstraint(parent_code, enterprise_id, name)

	parent_tax = relationship(
		"Tax", backref="sub_tax_parent",
		primaryjoin="and_(SubTax.parent_code == Tax.code, SubTax.enterprise_id == Tax.enterprise_id)")

	def __init__(self, parent_code=None, name=None, rate=0.00, enterprise_id=None):
		self.enterprise_id = enterprise_id
		self.parent_code = parent_code
		self.name = name
		self.rate = rate

	def __eq__(self, other):
		return (self.name == other.name) and (self.parent_code == other.parent_code) and (
				self.enterprise_id == other.enterprise_id)

	def __repr__(self):
		return "%s %s %s" % (self.enterprise_id, self.parent_code, self.rate)

	def getPublicValue(self, exclude=('tax_sub_tax',), extra=()):
		return super(SubTax, self).getPublicValue(exclude=exclude, extra=extra)


class Tag(Base):
	""" Models the Tags - the base object for tagging """
	__tablename__ = u'tags'

	id = Column(Integer, primary_key=True, autoincrement=True, name='id')
	tag = Column(String(45), name='tag', nullable=False)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)

	purchase_order_tags = relationship("PurchaseOrderTag", backref=backref(
		"tag_purchase_order_tags", order_by=id), cascade="save-update, merge, delete")

	invoice_tags = relationship("InvoiceTag", backref=backref(
		"tag_invoice_tags", order_by=id), cascade="save-update, merge, delete")
	UniqueConstraint(tag, enterprise_id)

	def __init__(self, id=None, tag="", enterprise_id=None):
		self.id = id
		self.tag = tag
		self.enterprise_id = enterprise_id

	def __repr__(self):
		return self.tag


class Tax(Base):
	""" Data Model Object for the table Tax """
	__tablename__ = u'tax'

	code = Column(String(20), primary_key=True, name='code', autoincrement=False)
	name = Column(String(100), name='name')
	base_rate = Column(DECIMAL, name='base_rate')
	is_compound = Column(Boolean, name='is_compound', default=False)
	net_rate = Column(DECIMAL, name='net_rate')
	assess_rate = Column(DECIMAL, name='assess_rate', default=0.00)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	type = Column(String(20), name='type')
	is_default = Column(Boolean, name='is_default', default=False)

	enterprise = relationship("Enterprise", backref=backref('tax_enterprise', order_by=enterprise_id))
	sub_taxes = relationship(
		"SubTax", backref='tax_sub_tax', cascade="save-update, merge, delete",
		primaryjoin="and_(Tax.code == SubTax.parent_code, Tax.enterprise_id == SubTax.enterprise_id)")
	ledger_map = relationship(
		"TaxLedgerMap", backref="tax_ledger_map_ledger", lazy="subquery",
		primaryjoin="and_(Tax.code==TaxLedgerMap.tax_id, Tax.enterprise_id==TaxLedgerMap.enterprise_id)")
	input_ledger_map = relationship(
		"TaxLedgerMap", backref="tax_ledger_map_input_ledger", lazy="subquery",
		primaryjoin="and_(Tax.code==TaxLedgerMap.tax_id, Tax.enterprise_id==TaxLedgerMap.enterprise_id, TaxLedgerMap.is_input==1)")
	output_ledger_map = relationship(
		"TaxLedgerMap", backref="tax_ledger_map_output_ledger", lazy="subquery",
		primaryjoin="and_(Tax.code==TaxLedgerMap.tax_id, Tax.enterprise_id==TaxLedgerMap.enterprise_id, TaxLedgerMap.is_input==0)")

	def __init__(
			self, code="", name="", base_rate=0, is_compound=False, assess_rate=0, net_rate=0, enterprise_id=None,
			type=None, is_default=False):
		self.code = code
		self.name = name
		self.base_rate = base_rate
		self.is_compound = is_compound
		self.assess_rate = assess_rate
		self.net_rate = net_rate
		self.enterprise_id = enterprise_id
		self.type = type
		self.is_default = is_default

	def __eq__(self, other):
		return (self.code == Tax(other).code) and (self.enterprise_id == Tax(other).enterprise_id)

	def __repr__(self):
		return "%s-%s-%s-%s-%s" % (self.enterprise_id, self.code, self.name, self.base_rate, self.assess_rate)

	def getSubTax(self, sub_tax_name):
		for sub_tax in self.sub_taxes:
			if sub_tax.name == sub_tax_name:
				return sub_tax
		return SubTax(parent_code=self.code, name='', rate=0.00)

	def getPublicValue(self, extra=(), exclude=(
			'tax_map', 'invoice_tax_tax', 'note_tax_tax', 'po_tax_tax', 'grn_tax_tax', 'sub_tax_parent',
			'enterprise',)):
		return super(Tax, self).getPublicValue(exclude=exclude, extra=extra)

	def getInputLedgerId(self):
		return self.input_ledger_map[0].ledger.id if len(self.input_ledger_map) > 0 else None

	def getOutputLedgerId(self):
		return self.output_ledger_map[0].ledger.id if len(self.output_ledger_map) > 0 else None


class TaxLedgerMap(Base):
	""" Data Model Object for the table PartyLedgerMap  """
	__tablename__ = u'tax_ledger_map'

	tax_id = Column(String(20), ForeignKey('tax.code'), name='tax_id', primary_key=True, autoincrement=False)
	ledger_id = Column(String(20), ForeignKey(
		'account_ledgers.id'), name='ledger_id', primary_key=True, autoincrement=False)
	is_input = Column(Integer, name='is_input', default=1, primary_key=True, autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name="enterprise_id", primary_key=True, autoincrement=False)

	tax = relationship("Tax", backref=backref('tax_map', order_by=tax_id), viewonly=True)
	ledger = relationship("Ledger", backref=backref('tax_ledger_map', order_by=ledger_id), viewonly=True)

	def __init__(self, id=None, tax_id=None, ledger_id=None, is_input=1, enterprise_id=None):
		self.id = id
		self.tax_id = tax_id
		self.ledger_id = ledger_id
		self.is_input = is_input
		self.enterprise_id = enterprise_id

	def __eq__(self, other):
		return self.tax_id == other.tax_id and self.ledger_id == other.ledger_id

	def __repr__(self):
		return format(self.tax_id + ' - ' + self.ledger_id)


class VoucherType(Base, AutoSerialize):
	""" Data Model Object for the table VoucherType """
	__tablename__ = u'voucher_type'

	SALES_VOUCHER_TYPE = 'Sales'
	NOTES_VOUCHER_TYPE = 'Note'

	id = Column(Integer, name='id', primary_key=True, autoincrement=False)
	code = Column(String(3), name='code', primary_key=True, autoincrement=False)
	name = Column(String(20), name='name', unique=True)

	def __init__(self, code=None, name=''):
		self.code = code
		self.name = name

	def __eq__(self, other):
		return self.id == other.id

	def __repr__(self):
		return "%s - %s" % (self.code, self.name)


class VoucherParticulars(Base, AutoSerialize):
	""" Data Model Object for the table Ledger """
	__tablename__ = u'voucher_particulars'

	voucher_id = Column(Integer, ForeignKey('voucher.id'), name='voucher_id', primary_key=True, autoincrement=False)
	item_no = Column(Integer, name='item_no', primary_key=True, autoincrement=False)
	ledger_id = Column(Integer, ForeignKey(
		'account_ledgers.id'), name='ledger_id', primary_key=True, autoincrement=False)
	is_debit = Column(Integer, name='is_debit')
	amount = Column(DECIMAL(precision=2), name='amount')
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name="enterprise_id")
	budget_id = Column(Integer, name='budget_id')

	voucher = relationship("Voucher", backref=backref(
		'voucher_particular_voucher', order_by=voucher_id), viewonly=True, uselist=False)
	ledger = relationship("Ledger", backref=backref(
		'voucher_particular_ledger', order_by=ledger_id), viewonly=True, uselist=False)

	def __init__(self, voucher_id=None, item_no=None, ledger_id=None, is_debit=None, amount=0, enterprise_id=None, budget_id=None):
		self.voucher_id = voucher_id
		self.item_no = item_no
		self.ledger_id = ledger_id
		self.is_debit = is_debit
		self.amount = amount
		self.enterprise_id = enterprise_id
		self.budget_id = budget_id

	def __eq__(self, other):
		return self.voucher_id == other.voucher_id and self.ledger_id == other.ledger_id and self.item_no == other.item_no

	def __repr__(self):
		return '%s' % self.__dict__

	def getCode(self):
		return self.voucher.getCode()


class Voucher(Base, AutoSerialize):
	""" Data Model Object for the table Ledger """
	__tablename__ = u'voucher'

	DRAFT = 0
	APPROVED = 1

	CLOSURE_TYPE = 7

	id = Column(BigInteger, autoincrement=True, name='id', primary_key=True)
	type_id = Column(Integer, ForeignKey('voucher_type.id'), name='type')
	voucher_no = Column(Integer, name='voucher_no')
	sub_number = Column(String(1), name='sub_number')
	voucher_date = Column(Date, name='voucher_date')
	narration = Column(String(500), name='narration')
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name="enterprise_id")
	project_code = Column(Integer, ForeignKey('projects.id'), name='project_code')
	financial_year = Column(String(5), name='financial_year')
	created_on = Column(DateTime, name='created_on')
	created_by = Column(Integer, ForeignKey('auth_user.id'), name='created_by')
	approved_on = Column(DateTime, name='approved_on')
	approved_by = Column(Integer, ForeignKey('auth_user.id'), name='approved_by')
	super_modified_on = Column(DateTime, name='super_modified_on')
	super_modified_by = Column(Integer, ForeignKey('auth_user.id'), name='super_modified_by')
	status = Column(Integer, name='status')
	transaction_instrument_no = Column(String(30), name='transaction_instrument_no')
	transaction_description = Column(String(20), name='transaction_instrument_desc')
	parent_voucher_id = Column(Integer, name='parent_voucher_id')
	project_automation_status = Column(Integer, name='project_automation_status')

	enterprise = relationship("Enterprise", backref=backref('voucher_enterprise', order_by=enterprise_id))
	type = relationship("VoucherType", backref=backref('voucher_voucher_type', order_by=type_id))
	project = relationship(
		"Project", backref='voucher_project',
		primaryjoin="and_(Voucher.enterprise_id==Project.enterprise_id, Voucher.project_code==Project.id)")
	particulars = relationship("VoucherParticulars", backref=backref(
		'voucher_voucher_particulars', order_by=id), cascade="save-update, merge, delete")
	tags = relationship("VoucherTag", backref=backref(
		"voucher_voucher_tags", order_by=id), cascade="save-update, merge, delete")
	super_modified_user = relationship(
		"User", backref='voucher_super_modified_user',
		primaryjoin='foreign(User.id) == Voucher.super_modified_by')
	bill_settlements = relationship("LedgerBillSettlement", backref=backref("voucher_bill_settlements", order_by=id),
									cascade="save-update, merge, delete")

	def __init__(
			self, id=None, type_id=1, voucher_no=0, voucher_date=None, narration="", status=0, enterprise_id=None,
			project_code=None, created_on=datetime.now(), created_by=None, approved_by=None, approved_on=None,
			financial_year=getFinancialYear(), transaction_instrument_no='', transaction_description='',
			sub_number=None, super_modified_on=None, super_modified_by=None, parent_voucher_id=None, project_automation_status=0):
		self.id = id
		self.voucher_no = voucher_no
		self.sub_number = sub_number
		self.super_modified_on = super_modified_on
		self.super_modified_by = super_modified_by
		self.voucher_date = voucher_date if voucher_date else time.strftime('%Y-%m-%d')
		self.narration = narration
		self.enterprise_id = enterprise_id
		self.project_code = project_code
		self.type_id = type_id
		self.created_on = created_on if created_on else time.strftime('%Y-%m-%d %H:%M:%S')
		self.created_by = created_by
		self.approved_by = approved_by
		self.approved_on = approved_on
		self.financial_year = financial_year
		self.transaction_description = transaction_description
		self.transaction_instrument_no = transaction_instrument_no
		self.status = status
		self.parent_voucher_id = parent_voucher_id
		self.project_automation_status = project_automation_status

	def __eq__(self, other):
		return self.id == other.id or (
				self.voucher_no == other.voucher_no and self.type_id == other.type and self.financial_year == other.financial_year and self.enterprise_id == other.enterprise_id)

	def __repr__(self):
		return "%s - %s" % (self.voucher_no, self.narration)

	def getCode(self):
		return self.generateCode(
			voucher_id=self.id, voucher_no=self.voucher_no, financial_year=self.financial_year,
			voucher_type=self.type.code, sub_number=self.sub_number)

	def isValidParticulars(self):
		credit_amount = 0
		debit_amount = 0
		for item in self.particulars:
			if item.is_debit:
				debit_amount += item.amount
			else:
				credit_amount += item.amount
		if credit_amount == debit_amount:
			return True
		else:
			return False

	@staticmethod
	def generateCode(voucher_id=None, voucher_no=None, financial_year=None, voucher_type=None, sub_number=None):
		if voucher_no is None or voucher_no == 0:
			return "TMP#%s/%s" % (voucher_type, voucher_id)
		else:
			return "%s/%s/%06d%s" % (
				financial_year, voucher_type, voucher_no, "%s" % sub_number.strip() if sub_number else "")


class VoucherTag(Base):
	""" Tagging model for Indents """
	__tablename__ = u'voucher_tags'

	voucher_id = Column(Integer, ForeignKey('voucher.id'), primary_key=True, name='voucher_id', autoincrement=False)
	tag_id = Column(Integer, ForeignKey('tags.id'), primary_key=True, name='tag_id', autoincrement=False)
	enterprise_id = Column(
		Integer, ForeignKey('enterprise.id'), primary_key=True, name='enterprise_id', autoincrement=False)

	tag = relationship("Tag", backref=backref("voucher_tag_tag", order_by=(tag_id, enterprise_id)))
	voucher = relationship("Voucher", backref=backref("voucher_tags_voucher", order_by=voucher_id))

	def __init__(self, voucher_id=None, tag_id=None, enterprise_id=None):
		self.enterprise_id = enterprise_id
		self.voucher_id = voucher_id
		self.tag_id = tag_id

	def __repr__(self):
		return "[%s] %s" % (self.voucher, self.tag)

	def __eq__(self, other):
		return (self.voucher_id == VoucherTag(other).voucher_id) and (self.tag_id == VoucherTag(other).tag_id)


class LedgerBill(Base):
	"""
	Data Model Object for the table Ledger Bills. Captures any bill creation against a ledger transaction
	Uniquely identified by bill_no, bill_date within a ledger - hence composite-key - [ledger_id, bill_no, bill_date]
	"""

	__tablename__ = u'ledger_bills'

	id = Column(Integer, name='id', autoincrement=True, primary_key=True)
	bill_no = Column(String(30), name='bill_no')
	bill_date = Column(Date, name='bill_date')
	ledger_id = Column(Integer, ForeignKey('account_ledgers.id'), name='ledger_id')
	net_value = Column(DECIMAL(precision=2), name='net_value')
	is_debit = Column(Boolean, name='is_debit')
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name="enterprise_id")
	voucher_id = Column(Integer, ForeignKey('voucher.id'), name='voucher_id')
	parent_ledgerbill_id = Column(Integer, name='parent_ledgerbill_id')


	settlements = relationship("LedgerBillSettlement", backref=backref(
		"ledger_bill_ledger_bill_settlement", order_by=id), cascade="save-update, merge, delete, delete-orphan")
	ledger = relationship("Ledger", backref=backref("bill_ledger", order_by=ledger_id))
	voucher = relationship("Voucher", backref=backref("ledgerbill_voucher", order_by=voucher_id))
	original_settlement = relationship(
		"LedgerBillSettlement", backref="voucher_original_bill_settlement",
		primaryjoin="""and_(LedgerBill.id==LedgerBillSettlement.bill_id, LedgerBill.voucher_id==LedgerBillSettlement.voucher_id)""",
		uselist=False)

	UniqueConstraint(bill_no, bill_date, ledger_id)

	def __init__(
			self, bill_no=None, bill_date=None, ledger_id=None, net_value=0.00, is_debit=None, enterprise_id=None,
			voucher_id=None, parent_ledgerbill_id=None):
		self.bill_no = bill_no
		self.bill_date = bill_date if bill_date else time.strftime('%Y-%m-%d')
		self.ledger_id = ledger_id
		self.net_value = net_value
		self.is_debit = is_debit
		self.enterprise_id = enterprise_id
		self.voucher_id = voucher_id
		self.parent_ledgerbill_id = parent_ledgerbill_id

	def __eq__(self, other):
		return self.id == other.id

	def __repr__(self):
		return "%s-%s-%s" % (self.ledger_id, self.bill_no, self.bill_date)

	def getNetValue(self):
		return sum(float(_settlement.cr_value) - float(_settlement.dr_value) for _settlement in self.settlements)


class LedgerBillSettlement(Base, AutoSerialize):
	""" Data model for the table 'ledger_bill_settlements'. Captures bill settlements against any voucher entry. """
	__tablename__ = u'ledger_bill_settlements'

	bill_id = Column(Integer, ForeignKey('ledger_bills.id'), name='bill_id', primary_key=True, autoincrement=False)
	voucher_id = Column(Integer, ForeignKey('voucher.id'), name='voucher_id', primary_key=True, autoincrement=False)
	dr_value = Column(DECIMAL(precision=2), name='dr_value')
	cr_value = Column(DECIMAL(precision=2), name='cr_value')
	enterprise_id = Column(
		Integer, ForeignKey('enterprise.id'), name="enterprise_id", primary_key=True, autoincrement=False)

	voucher = relationship("Voucher", backref=backref("settlement_voucher", order_by=voucher_id))
	bill = relationship("LedgerBill", backref=backref("settlement_bill", order_by=bill_id))

	def __init__(self, bill_id=None, voucher_id=None, dr_value=0.00, cr_value=0.00, enterprise_id=None):
		self.bill_id = bill_id
		self.voucher_id = voucher_id
		self.dr_value = dr_value
		self.cr_value = cr_value
		self.enterprise_id = enterprise_id

	def __repr__(self):
		return "%s" % self.__dict__


class UnitMaster(Base, AutoSerialize):
	"""
	Models a simple table that holds the various units used to measure the material quantities identified against the
	drawing_no provided
	"""
	__tablename__ = u'unit_master'

	unit_id = Column(Integer, primary_key=True, name="unit_id", autoincrement=True)
	unit_name = Column(String(50), name="unit_name")
	unit_description = Column(String(50), name="unit_description")
	enterprise_id = Column(
		Integer, ForeignKey('enterprise.id'), name="enterprise_id", primary_key=True, autoincrement=False)

	def __repr__(self):
		return "%s" % self.unit_name

	def getPublicValue(self, exclude=(
			'invoice_nonstock_items', 'material_unit', 'po_non_stock_unit', 'receipt_non_stock_unit',), extra=()):
		return super(UnitMaster, self).getPublicValue(extra=extra, exclude=exclude)

	def getUnitName(self):
		return "%s" % self.unit_name

	def __init__(self, unit_id=0, unit_name='', unit_description='', enterprise_id=None):
		self.unit_name = unit_name
		self.unit_description = unit_description
		self.enterprise_id = enterprise_id
		self.unit_id = unit_id


class UserPermission(Base, AutoSerialize):
	"""
	Models 'user_permission' table - that maps every user with a set of permissions that controls his/her actions along
	the application's various modules and sub-modules. Basically 4 levels of accesses - Read, Edit, Approve & Delete are
	defined and are persisted in a single field as an integer holding bit-flags.
	"""
	__tablename__ = u'user_permissions'

	_ALL_ACCESS = 31
	_VIEW = 2 ** VIEW_FLAG_POSITION
	_EDIT = 2 ** EDIT_FLAG_POSITION
	_DELETE = 2 ** DELETE_FLAG_POSITION
	_APPROVE = 2 ** APPROVE_FLAG_POSITION
	_NOTIFY = 2 ** NOTIFY_FLAG_POSITION

	user_id = Column(Integer, ForeignKey('auth_user.id'), name='user_id', primary_key=True, autoincrement=False)
	module_code = Column(
		String(20), ForeignKey('action_module.code'), name='module_code', primary_key=True, autoincrement=False)
	bit_flags = Column(Integer, name='permission', default=0)
	enterprise_id = Column(
		Integer, ForeignKey('enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)

	user = relationship("User", backref=backref('user_permission_user', order_by=[user_id, enterprise_id]))
	module = relationship("Module", backref=backref('user_permission_module', order_by=module_code))

	def __init__(self, user_id=None, module_code=None, bit_flags=0, enterprise_id=None):
		self.user_id = user_id
		self.module_code = module_code
		self.bit_flags = bit_flags
		self.enterprise_id = enterprise_id

	def __repr__(self):
		return "%s - %s - %s" % (self.user_id, self.module_code, self.getAllAccessFlags())

	def __eq__(self, other):
		return (self.user_id == other.user_id) and (self.module_code.lower().__eq__(other.module_code.lower()))

	def hasAccess(self, access_level=_VIEW):
		try:
			return access_level if self.user.is_super else self.bit_flags & access_level
		except AttributeError:
			# Captures the condition when self.user is None, yet their permission levels are initialized
			return 0 if self.bit_flags is None else self.bit_flags & access_level

	def hasViewAccess(self):
		return self.hasAccess()

	def hasEditAccess(self):
		return self.hasAccess(self._EDIT)

	def hasDeleteAccess(self):
		return self.hasAccess(self._DELETE)

	def hasApproveAccess(self):
		return self.hasAccess(self._APPROVE)

	def hasNotifyAccess(self):
		return self.hasAccess(self._NOTIFY)

	def getAllAccessFlags(self):
		"""
		:return: Boolean Flag array - [VIEW_ACCESS, EDIT_ACCESS, APPROVE_ACCESS, DELETE_ACCESS, NOTIFY_ACCESS]
		"""
		return [
			self.hasViewAccess(), self.hasEditAccess(), self.hasApproveAccess(), self.hasDeleteAccess(),
			self.hasNotifyAccess()]


class User(Base, AutoSerialize):
	"""
	Class that models User table. User to hold the profile of a user who logs into the application, along with his/her
	access permissions and the projects associated.
	"""
	__tablename__ = u'auth_user'

	id = Column(Integer, primary_key=True, name='id', autoincrement=True)
	email = Column(String(75), name='email', nullable=False, unique=True)
	first_name = Column(String(30), name='first_name')
	last_name = Column(String(30), name='last_name')
	username = Column(String(30), name='username')
	password = Column(String(128), nullable=False, name='password')
	is_active = Column(Boolean, name='is_active')
	is_super = Column(Boolean, name='is_superuser')
	last_login = Column(DateTime, name='last_login')
	date_joined = Column(DateTime, name='date_joined')
	enterprise_id = Column(
		Integer, ForeignKey('enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	landing_url = Column(String(45), name='landing_url')

	images = relationship("UserImages", uselist=False, backref=backref(
		"user_user_images", order_by=id), cascade="save-update, merge, delete, delete-orphan")

	permissions = relationship("UserPermission", backref=backref(
		'user_user_permissions', order_by=[id, enterprise_id]), cascade="save-update, merge, delete, delete-orphan")

	enterprise = relationship("Enterprise", backref=backref('user_enterprise', order_by=enterprise_id))

	user_enterprise_map = relationship("UserEnterpriseMap", backref=backref(
		"user_auth_user_enterprise_map", order_by=[id, enterprise_id]), cascade="save-update, merge, delete, delete-orphan")

	claim_heads = relationship("UserClaimHead", backref=backref(
		"user_user_claim_heads", order_by=[id, enterprise_id]), cascade="save-update, merge, delete, delete-orphan")

	expense_heads = relationship("UserExpenseHead", backref=backref(
		"user_user_expense_heads", order_by=[id, enterprise_id]), cascade="save-update, merge, delete, delete-orphan")

	fcm_register = relationship("FcmUserRegister", backref=backref('user_fcm_register', order_by=id))

	def __init__(
			self, username='', email='', password=None, first_name='', last_name='', is_active=True,
			is_super=False, last_login=None, date_joined=None, enterprise_id=None, landing_url=None):
		self.username = username
		self.first_name = first_name
		self.last_name = last_name
		self.email = email
		self.setPassword(password)
		self.is_active = is_active
		self.is_super = is_super
		self.last_login = last_login
		self.date_joined = date_joined if date_joined else time.strftime('%Y-%m-%d %H:%M:%S')
		self.enterprise_id = enterprise_id
		self.landing_url = landing_url

	def __repr__(self):
		return "%s %s" % (self.first_name.capitalize(), self.last_name.capitalize())

	def checkPassword(self, raw_password):
		"""
		Returns a boolean of whether the raw_password was correct. Handles encryption formats behind the scenes.
		"""
		# Copied from django auth module
		# Backwards-compatibility check. Older passwords won't include the algorithm or salt.
		if '$' not in "%s" % self.password:
			is_correct = (self.password == make_password(raw_password, '', 'md5'))
			if is_correct:
				self.setPassword(raw_password)
			return is_correct
		return check_password(raw_password, self.password)

	def setPassword(self, raw_password):
		# Copied from django auth module
		if raw_password is not None:
			import random

			algor = 'sha1'
			salt = smart_str("%s%s%s" % (algor, str(random.random()), str(random.random())))[:5]
			self.password = make_password(raw_password, salt, algor)

	def getAllPermissions(self):
		"""
		Gets access permission for all modules for this user
		"""
		return self.permissions

	def hasPermission(self, permission):
		if self.is_active and self.is_super:
			return True
		try:
			return self.permissions[self.permissions.index(permission)].bit_flags & 1
		except IndexError:
			return False

	def hasModuleAccess(self, module_code, access_level):
		if self.is_active and self.is_super:
			return UserPermission._ALL_ACCESS
		for permission in self.permissions:
			if permission.module_code == module_code:
				return permission.bit_flags & access_level


class UserEnterpriseMap(Base, AutoSerialize):
	"""

	"""
	__tablename__ = u'auth_user_enterprise_map'

	user_id = Column(Integer, ForeignKey('auth_user.id'), name='user_id', primary_key=True, autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	status = Column(Integer, name='status')

	enterprise = relationship("Enterprise", backref="auth_user_enterprise", primaryjoin="and_(Enterprise.id==UserEnterpriseMap.enterprise_id)")
	user = relationship("User", backref="user_enterprise_map_user", primaryjoin="and_(User.id==UserEnterpriseMap.user_id)")

	def __init__(self, user_id=None, enterprise_id=None, status="1"):
		self.user_id = user_id
		self.enterprise_id = enterprise_id
		self.status = status

	def __repr__(self):
		return "%s - %s" % (self.user_id, self.enterprise_id)

	def __eq__(self, other):
		return self.user_id == other.user_id and self.enterprise_id == other.enterprise_id


class UserImages(Base):
	"""
	Entity for persisting User Images, such as profile Image, signature, etc
	"""
	__tablename__ = u'user_images'

	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	user_id = Column(Integer, ForeignKey('auth_user.id'), name='user_id', primary_key=True, autoincrement=False)
	profile_img = Column(LargeBinary, name='profile_img')
	profile_img_ext = Column(String(length=25), name='profile_img_ext')
	signature = Column(LargeBinary, name='signature')
	signature_ext = Column(String(length=25), name='signature_ext')

	def __init__(self, user_id=None, enterprise_id=None, profile_img="", profile_img_ext="", signature="", signature_ext=""):
		self.user_id = user_id
		self.enterprise_id = enterprise_id
		self.profile_img = profile_img
		self.profile_img_ext = profile_img_ext
		self.signature = signature
		self.signature_ext = signature_ext

	def __repr__(self):
		return 'Size: %s Extension: %s' % (len(self.profile_img), self.profile_img_ext)


class UserClaimHead(Base, AutoSerialize):
	"""
	Models the Enterprise's Expense Configurations - Claim Heads
	User-UserClaimHead :: One-to-Many
	"""
	__tablename__ = u'user_claim_heads'

	user_id = Column(Integer, ForeignKey('auth_user.id'), name='user_id', primary_key=True, autoincrement=False)
	enterprise_id = Column(
		Integer, ForeignKey('enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	ledger_id = Column(Integer, ForeignKey(
		'enterprise_claim_heads.ledger_id'), name='ledger_id', primary_key=True, autoincrement=False)

	enterprise = relationship(
		"Enterprise", backref="user_claim_heads_enterprise",
		primaryjoin="and_(Enterprise.id==UserClaimHead.enterprise_id)")
	user = relationship("User", backref="user_claim_head_user", primaryjoin="and_(User.id==UserClaimHead.user_id)")

	def __init__(self, user_id=None, enterprise_id=None, ledger_id=None):
		self.user_id = user_id
		self.enterprise_id = enterprise_id
		self.ledger_id = ledger_id

	def __repr__(self):
		return "%s - %s - %s" % (self.user_id, self.enterprise_id, self.ledger_id)

	def __eq__(self, other):
		return self.user_id == other.user_id and self.enterprise_id == other.enterprise_id and self.ledger_id == other.ledger_id


class UserExpenseHead(Base, AutoSerialize):
	"""
	Models the Enterprise's Expense Configurations - Expense Heads
	User-UserExpenseHead :: One-to-Many
	"""
	__tablename__ = u'user_expense_heads'

	user_id = Column(Integer, ForeignKey('auth_user.id'), name='user_id', primary_key=True, autoincrement=False)
	enterprise_id = Column(
		Integer, ForeignKey('enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	ledger_id = Column(Integer, ForeignKey(
		'enterprise_expense_heads.ledger_id'), name='ledger_id', primary_key=True, autoincrement=False)

	enterprise = relationship(
		"Enterprise", backref="expense_claim_heads_enterprise",
		primaryjoin="and_(Enterprise.id==UserExpenseHead.enterprise_id)")
	user = relationship("User", backref="expense_claim_head_user", primaryjoin="and_(User.id==UserExpenseHead.user_id)")

	def __init__(self, user_id=None, enterprise_id=None, ledger_id=None):
		self.user_id = user_id
		self.enterprise_id = enterprise_id
		self.ledger_id = ledger_id

	def __repr__(self):
		return "%s - %s - %s" % (self.user_id, self.enterprise_id, self.ledger_id)

	def __eq__(self, other):
		return self.user_id == other.user_id and self.enterprise_id == other.enterprise_id and self.ledger_id == other.ledger_id


class ChangeLog(Base, AutoSerialize):
	"""
	Models a simple table that holds the various units used to measure the material quantities identified against the
	drawing_no provided
	"""
	__tablename__ = u'change_log'

	timestamp = Column(DateTime, primary_key=True, name="timestamp", autoincrement=False)
	enterprise_id = Column(Integer, primary_key=True, name="enterprise_id", autoincrement=False)
	module_code = Column(String(30), primary_key=True, name="module_code", autoincrement=False)
	doc_id = Column(Integer, primary_key=True, name="doc_id", autoincrement=False)
	details = Column(LargeBinary, name="details")
	user_id = Column(Integer, name="user_id")

	def __init__(
			self, timestamp=None, enterprise_id=None, module_code=None, doc_id=None, details=None, user_id=None):
		self.timestamp = timestamp if timestamp else time.strftime('%Y-%m-%d %H:%M:%S')
		self.enterprise_id = enterprise_id
		self.module_code = module_code
		self.doc_id = doc_id
		self.details = details
		self.user_id = user_id

	def __repr__(self):
		return "%s" % self.__dict__


class Invoice(Base, AutoSerialize, Chargeable, Remarkable):
	""" Class that models invoice table. Its hold the order details of the . """
	__tablename__ = u'invoice'

	STATUS_DRAFT = 0
	STATUS_APPROVED = 1
	STATUS_CANCELLED = -1
	STATUS_REJECTED = -2
	TYPE_CHOICES = {
		"sales": (
			('GST', 'Tax Invoice (GST)'),
			('Trading', 'Trading'),
			('Service', 'Service/Labour'),
			('BoS', 'Bill of Supply'),
			('Excise', 'Excise')),
		"dc": (('DC', 'Delivery Challan'), ('JDC', 'Job Out'), ('JIN', 'Job In - (Return)')),
		"internal": (('Issue', 'Issue'),)
	}
	TYPES = {"internal": ("Issue",), "dc": ("DC", "JDC", "JIN"),
	         "sales": ("GST", "Trading", "Service", "BoS", "Excise")}
	TYPE_TITLES = {
		"Issue": "Issue",
		"GST": "Tax Invoice",
		"Trading": "Tax Invoice",
		"Service": "Tax Invoice",
		"Excise": "Tax Invoice",
		"BoS": "Bill of Supply",
		"DC": "Delivery Challan",
		"JDC": "Delivery Challan",
		"JIN": "Delivery Challan",
		"Others": "Issue"
	}
	TYPE_KEYS = {
		"Issue": "internal",
		"GST": "sales",
		"Trading": "sales",
		"Service": "sales",
		"Excise": "sales",
		"BoS": "sales",
		"DC": "dc",
		"JDC": "dc",
		"JIN": "dc",
		"Others": "internal"
	}

	PACKING_STATUS_DRAFT = 0
	PACKING_STATUS_PARTIAL = 1
	PACKING_STATUS_COMPLETED = 2

	id = Column(Integer, primary_key=True, name='id', autoincrement=True)
	type = Column(String(30), name='type')
	project_code = Column(Integer, ForeignKey('projects.id'), name='project_code')
	invoice_no = Column(Integer, name='invoice_no')
	sub_number = Column(String(1), name='sub_number')
	approved_on = Column(DateTime, name='approved_on')
	super_modified_on = Column(DateTime, name='super_modified_on')
	super_modified_by = Column(Integer, ForeignKey('auth_user.id'), name='super_modified_by')
	last_modified_on = Column(DateTime, name='last_modified_on')
	last_modified_by = Column(Integer, ForeignKey('auth_user.id'), name='last_modified_by')
	issued_on = Column(DateTime, name='issued_on')
	party_id = Column(Integer, ForeignKey('party_master.party_id'), name='party_id')
	prepared_on = Column(DateTime, name='prepared_on')
	po_no = Column(String, name='po_no')
	po_date = Column(Date, name='po_date')
	se_id = Column(Integer, ForeignKey('sales_estimate.id'), name='se_id')
	se_date = Column(DateTime, name='se_date')
	order_accept_no = Column(String, name='order_accept_no')
	order_accept_date = Column(DateTime, name='order_accept_date')
	transport_mode = Column(String(45), name='transport_mode')
	lr_no = Column(String(45), name='lr_no')
	road_permit_no = Column(String(45), name='road_permit_no')
	packing_slip_no = Column(String(45), name='packing_slip_no')
	packing_description = Column(String(100), name='packing_description')
	remarks = Column(JSON, name='remarks')
	payment_terms = Column(String(150), name='payment_terms')
	special_instruction = Column(String(400), name='special_instruction')
	prepared_by = Column(Integer, ForeignKey('auth_user.id'), name='prepared_by')
	approved_by = Column(Integer, ForeignKey('auth_user.id'), name='approved_by')
	financial_year = Column(String(5), name='financial_year')
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name='enterprise_id')
	status = Column(Integer, name='status', default=STATUS_DRAFT)
	currency_id = Column(Integer, ForeignKey('currency.id'), name='currency_id')
	currency_conversion_rate = Column(DECIMAL(precision=3), name="cur_conversion_rate", default=1.00)
	grand_total = Column(DECIMAL(precision=2), name='grand_total')
	ledger_bill_id = Column(Integer, ForeignKey('ledger_bills.id'), name='ledger_bill_id')
	deliver_to = Column(String, name='delivery_address')
	gstin = Column(String, name='ship_to_gstno')
	ship_to_name = Column(String, name='ship_to_name')
	round_off = Column(DECIMAL, name='round_off')
	job_po_id = Column(Integer, ForeignKey('purchase_order.id'), name='job_po_id', default=None)
	return_date = Column(Date, name='return_date')
	sale_account_id = Column(Integer, ForeignKey('account_ledgers.id'), name='sale_account_id')
	issued_to = Column(String(45), name='issued_to')
	issued_for = Column(String(45), name='issued_for')
	tax_payable_on_reverse_charge = Column(
		Boolean, name='tax_payable_on_reverse_charge', primary_key=True, autoincrement=False, default=False)
	notes = Column(String, name='notes')
	invoice_code = Column(String(25), name='invoice_code')
	goods_already_supplied = Column(Boolean, name='goods_already_supplied', autoincrement=False, default=False)
	ecommerce_gstin = Column(String(15), name="ecommerce_gstin")
	irn_ack_json = Column(JSON, name='irn_ack_json')
	is_courier = Column(Boolean, name='is_courier', autoincrement=False, default=False)
	no_of_consignment = Column(Integer, name='no_of_consignment')
	weight = Column(DECIMAL(precision=3), name="weight", default=0.00)
	parent_invoice_id = Column(Integer, name='parent_invoice_id')
	location_id = Column(Integer, ForeignKey("location_master.id"), name="location_id", autoincrement=False, nullable=True)

	items = relationship(
		"InvoiceMaterial", primaryjoin="and_(Invoice.id==InvoiceMaterial.invoice_id, Invoice.enterprise_id==InvoiceMaterial.enterprise_id)",
		backref="invoice_invoice_materials", cascade="save-update, merge, delete, delete-orphan")
	charges = relationship(
		"InvoiceCharge", primaryjoin="(Invoice.id==InvoiceCharge.invoice_id)",
		backref="invoice_invoice_charges", cascade="save-update, merge, delete, delete-orphan")
	taxes = relationship(
		"InvoiceTax", primaryjoin="(Invoice.id==InvoiceTax.invoice_id)", backref="invoice_invoice_tax",
		cascade="save-update, merge, delete, delete-orphan")
	documents = relationship(
		"InvoiceDocument", backref="invoice_invoice_document", order_by="InvoiceDocument.revised_on",
		primaryjoin="and_(Invoice.id==InvoiceDocument.invoice_id, InvoiceDocument.revised_on==Invoice.last_modified_on)")
	revisions = relationship(
		"InvoiceDocument", backref="invoice_invoice_document_revisions", order_by="InvoiceDocument.revised_on",
		primaryjoin="and_(Invoice.id==InvoiceDocument.invoice_id, InvoiceDocument.revised_on!=Invoice.last_modified_on)")
	job_po = relationship("PurchaseOrder", backref=backref("dc_job_po", order_by=job_po_id))
	enterprise = relationship("Enterprise", backref=backref("invoice_enterprise", order_by=enterprise_id))
	project = relationship(
		"Project", backref='invoice_project', lazy="subquery",
		primaryjoin="and_(Invoice.project_code==Project.id, Invoice.enterprise_id==Project.enterprise_id)")
	customer = relationship("Party", backref=backref("invoice_party_master", order_by=party_id))
	location = relationship("LocationMaster", backref=backref("invoice_location_master", order_by=location_id))

	tags = relationship(
		"InvoiceTag", backref=backref("invoice_invoice_tags", order_by=id), cascade="save-update, merge, delete")
	ledger_bill = relationship("LedgerBill", backref=backref(
		"invoice_ledger_bill", order_by=ledger_bill_id), cascade="save-update, merge, delete")
	sale_account_ledger = relationship("Ledger", backref=backref(
		"invoice_sale_ledger", order_by=(sale_account_id, enterprise_id)), viewonly=True)

	prepared_user = relationship(
		"User", backref='invoice_prepared_by',
		primaryjoin='foreign(User.id) == Invoice.prepared_by')
	approver = relationship(
		"User", backref='invoice_approver',
		primaryjoin='and_(Invoice.approved_by == User.id)')
	super_modified_user = relationship(
		"User", backref='invoice_super_modified_user',
		primaryjoin='foreign(User.id) == Invoice.super_modified_by')
	currency = relationship("Currency", backref=backref("invoice_currency", order_by=currency_id))
	se = relationship("SalesEstimate", backref="se_inv_se", primaryjoin="and_(Invoice.se_id == SalesEstimate.id)")

	def __init__(
			self, id=None, invoice_no=None, approved_on=None, issued_on=None, prepared_on=None,
			order_accept_no='', project_code=None, status=STATUS_DRAFT, order_accept_date=None,
			grand_total=0.00, round_off=0.00, transport_mode='', lr_no='', road_permit_no='', return_date=None,
			packing_slip_no=None, packing_description="", approved_by=None, financial_year=None,
			prepared_by=None, party_id=None, enterprise_id=None, type=None, issued_for="", issued_to="",
			currency_id=356, po_date=None, se_id=None, se_date=None, last_modified_on=None, last_modified_by=None, invoice_number=None,
			sub_number=None, super_modified_on=None, super_modified_by=None, tax_payable_on_reverse_charge=False,
			notes="", ecommerce_gstin=None, irn_ack_json=None, *args, **kwargs):
		super(Invoice, self).__init__(*args, **kwargs)
		self.id = id
		self.invoice_no = invoice_no
		self.sub_number = sub_number
		self.invoice_number = invoice_number
		self.super_modified_on = super_modified_on
		self.super_modified_by = super_modified_by
		self.approved_on = approved_on
		self.issued_on = issued_on
		self.party_id = party_id
		self.prepared_on = prepared_on if prepared_on else datetime.now()
		self.po_date = po_date
		self.se_id = se_id
		self.se_date = se_date
		self.order_accept_no = order_accept_no
		self.order_accept_date = order_accept_date
		self.transport_mode = transport_mode
		self.lr_no = lr_no
		self.road_permit_no = road_permit_no
		self.packing_slip_no = packing_slip_no
		self.packing_description = packing_description
		self.prepared_by = prepared_by
		self.approved_by = approved_by
		self.financial_year = financial_year if financial_year else getFinancialYear()
		self.enterprise_id = enterprise_id
		self.status = status
		self.type = type
		self.project_code = project_code
		self.currency_id = currency_id
		self.grand_total = grand_total
		self.round_off = round_off
		self.return_date = return_date
		self.issued_to = issued_to
		self.issued_for = issued_for
		self.last_modified_on = last_modified_on
		self.last_modified_by = last_modified_by
		self.tax_payable_on_reverse_charge = tax_payable_on_reverse_charge
		self.notes = notes
		self.ecommerce_gstin = ecommerce_gstin
		self.irn_ack_json = irn_ack_json
		self.remarks_as_text = None

	def __repr__(self):
		return '%s' % self.__dict__

	def getInternalCode(self):
		logger.debug("Invoice Draft ID: %s" % self.id)
		inv_number_format = None
		if self.enterprise is not None:
			inv_number_format = self.enterprise.inv_template_config.template_header_details.inv_number_format
		return self.generateInternalCode(
			invoice_id=self.id if self.id else 0, invoice_no=self.invoice_no, invoice_type=self.type,
			financial_year=self.financial_year, temp_title="PF", sub_number=self.sub_number,
			inv_number_format=inv_number_format, invoice_code=self.invoice_code)

	@staticmethod
	def generateInternalCode(
			invoice_id=None, invoice_no=None, invoice_type=None, financial_year=None, temp_title="PROFORMA",
			sub_number=None, inv_number_format=None, invoice_code=None):
		"""
		Returns a Code used for internal purpose within the organisation/ enterprise.
		It will not have the enterprise_id attached.

		:param invoice_id:
		:param invoice_no:
		:param invoice_type:
		:param financial_year:
		:param temp_title:
		:param sub_number
		:return:
		"""
		if invoice_type == 'Issue':
			return "%s%s" % (
				("%s/%s%06.0f" % (financial_year, invoice_type[0], float(invoice_no))).upper(),
				"%s" % sub_number.strip() if sub_number else "")
		elif invoice_no == "0" or invoice_no is None:
			return "%s#%s%06.0f" % (temp_title, invoice_type[0] if invoice_type else "", invoice_id)
		elif invoice_code is not None:
			return invoice_code
		else:
			return inv_number_format.format(
				type=invoice_type, fy=financial_year, number=int(invoice_no), sub_number=sub_number.strip() if sub_number is not None else "")

	def getSimpleCode(self):
		return self.getInternalCode()

	def getCode(self):
		inv_number_format = None
		if self.enterprise is not None:
			inv_number_format = self.enterprise.inv_template_config.template_header_details.inv_number_format
		return self.generateInternalCode(
			invoice_id=self.id if self.id else 0, invoice_no=self.invoice_no, invoice_type=self.type,
			financial_year=self.financial_year, sub_number=self.sub_number, inv_number_format=inv_number_format,
			invoice_code=self.invoice_code)

	def hasAssessRateTax(self):
		for inv_tax in self.getTaxes():
			if inv_tax.tax.assess_rate > 0:
				return True
		return False

	def getAssessableValue(self, tax_code=None):
		"""

		:return:
		"""
		assess_rate = 0
		assess_value = self.getTotalTaxableValue()
		for inv_tax in self.getTaxes():
			if (not tax_code or inv_tax.tax.code == tax_code) and (
					inv_tax.tax.assess_rate and inv_tax.tax.assess_rate > assess_rate):
				assess_rate = inv_tax.tax.assess_rate
				assess_value = 0
				for item in self.items:
					assess_value += item.rate * item.quantity * (
						(100 - item.discount) if (
							(100 - item.discount) > inv_tax.tax.assess_rate) else inv_tax.tax.assess_rate) / 100

				for charge in self.charges:
					assess_value += charge.rate * (
						(100 - charge.discount) if (
								(100 - charge.discount) > inv_tax.tax.assess_rate) else inv_tax.tax.assess_rate) / 100
			logger.debug("Assess Value at %s of Tax - %s is %s" % (
				inv_tax.tax.assess_rate, inv_tax.tax.code, assess_value))
		return assess_value

	def isTaxable(self):
		return self.type.upper() not in ("DC", "BOS", "JDC")

	def isRetunable(self):
		for item in self.items:
			if item.is_returnable:
				return True
		for ns_item in self.non_stock_items:
			if ns_item.is_returnable:
				return True
		return False

	def getDocumentTitle(self):
		return self.TYPE_TITLES[self.type] if self.type in self.TYPE_TITLES else self.TYPE_TITLES["Others"]

	def getInvoiceOACodes(self):
		oa_list = []
		for item in self.items:
			if item.oa_no:
				oa_code = item.inv_oa.getInternalCode()
				if oa_code not in oa_list:
					oa_list.append(oa_code)
		return ", ".join(oa_list)



	def getConsolidatedQuantity(self):
		"""
		Calculates the Gross Quantity of all the Items associated with the object

		:return: a Decimal value
		"""
		total_quantity = 0
		for material in self.items:
			if material.alternate_unit_id:
				quantity = 0
				scale_factor = material.alternate_unit.scale_factor
				if scale_factor:
					quantity = Decimal(material.quantity) / Decimal(scale_factor)
			else:
				quantity = material.quantity
			total_quantity += round(quantity, 3)
		return Decimal(total_quantity)

	def getConsolidatedTaxValue(self, rate=None, item_quantity=0, item_rate=0.00, item_discount=0):
		"""
		:param rate:
		:param item_quantity:
		:param item_rate:
		:param item_discount:
		:return:
		"""
		value = Decimal(Decimal(item_quantity) * Decimal(item_rate) * (100 - Decimal(item_discount)) * Decimal(rate) / (100 * 100)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
		return Decimal(value)

	def getTotalinWords(self):
		return num2words(round(self.grand_total, 2) + 0.001, lang='en_US').upper().replace('POINT ZERO ZERO', " ")

	def getTaxSummary(self, invoice_materials=None, source=None):
		"""

		:param invoice_materials:
		:param source:
		:return:
		"""
		available_cgst = {}
		available_sgst = {}
		available_igst = {}

		for item_key in invoice_materials:
			stock_item = invoice_materials[item_key]
			material = stock_item['material']
			item_quantity = stock_item["consolidated_quantity"]
			scale_factor = 1
			if material.alternate_unit_id:
				scale_factor = material.alternate_unit.scale_factor

			for material_tax in material.getTaxesOfType("CGST"):
				if material_tax and material_tax.tax.net_rate != 0:
					value = Decimal(item_quantity * material.rate * scale_factor * (100 - material.discount) * Decimal(
						material_tax.tax.net_rate) / (100 * 100)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
					if material_tax.tax.net_rate in available_cgst.keys():
						exsisting_value = available_cgst[material_tax.tax.net_rate]
						amount = exsisting_value + value
						available_cgst[material_tax.tax.net_rate] = amount
					else:
						available_cgst[material_tax.tax.net_rate] = value
			for material_tax in material.getTaxesOfType("SGST"):
				if material_tax and material_tax.tax.net_rate != 0:
					value = Decimal(item_quantity * material.rate * scale_factor * (100 - material.discount) * Decimal(
						material_tax.tax.net_rate) / (100 * 100)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
					if material_tax.tax.net_rate in available_sgst.keys():
						exsisting_value = available_sgst[material_tax.tax.net_rate]
						amount = exsisting_value + value
						available_sgst[material_tax.tax.net_rate] = amount
					else:
						available_sgst[material_tax.tax.net_rate] = value
			for material_tax in material.getTaxesOfType("IGST"):
				if material_tax and material_tax.tax.net_rate != 0:
					value = Decimal(item_quantity * material.rate * scale_factor * (100 - material.discount) * Decimal(
						material_tax.tax.net_rate) / (100 * 100)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
					if material_tax.tax.net_rate in available_igst.keys():
						exsisting_value = available_igst[material_tax.tax.net_rate]
						amount = exsisting_value + value
						available_igst[material_tax.tax.net_rate] = amount
					else:
						available_igst[material_tax.tax.net_rate] = value

		for charge in source.charges:
			for material_tax in charge.getTaxesOfType("CGST"):
				if material_tax and material_tax.tax.net_rate != 0:
					value = charge.rate * (100 - charge.discount) * Decimal(material_tax.tax.net_rate) / (100 * 100)
					if material_tax.tax.net_rate in available_cgst.keys():
						exsisting_value = available_cgst[material_tax.tax.net_rate]
						amount = exsisting_value + value
						available_cgst[material_tax.tax.net_rate] = amount
					else:
						available_cgst[material_tax.tax.net_rate] = value
			for material_tax in charge.getTaxesOfType("SGST"):
				if material_tax and material_tax.tax.net_rate != 0:
					value = charge.rate * (100 - charge.discount) * Decimal(material_tax.tax.net_rate) / (100 * 100)
					if material_tax.tax.net_rate in available_sgst.keys():
						exsisting_value = available_sgst[material_tax.tax.net_rate]
						amount = exsisting_value + value
						available_sgst[material_tax.tax.net_rate] = amount
					else:
						available_sgst[material_tax.tax.net_rate] = value
			for material_tax in charge.getTaxesOfType("IGST"):
				if material_tax and material_tax.tax.net_rate != 0:
					value = charge.rate * (100 - charge.discount) * Decimal(material_tax.tax.net_rate) / (
							100 * 100)
					if material_tax.tax.net_rate in available_igst.keys():
						exsisting_value = available_igst[material_tax.tax.net_rate]
						amount = exsisting_value + value
						available_igst[material_tax.tax.net_rate] = amount
					else:
						available_igst[material_tax.tax.net_rate] = value

		tax_summary = {"cgst_summary": available_cgst, "sgst_summary": available_sgst, "igst_summary": available_igst}
		return tax_summary

	def hasQIR(self):
		"""
		Checks if the Invoice has QIR profiled & returns a Boolean flag accordingly
		:return:
		"""
		for item in self.items:
			if item.inspection_log and (len(item.inspection_log) > 0):
				return True
		return False


class InvoiceCharge(Base, TaxableItem):
	__tablename__ = u'invoice_charges'

	invoice_id = Column(Integer, ForeignKey('invoice.id'), primary_key=True, name='invoice_id', autoincrement=False)
	item_name = Column(String(200), name='item_name', primary_key=True, autoincrement=False)
	hsn_code = Column(String(20), name='hsn_code')
	discount = Column(DECIMAL, name='discount')
	is_percent = Column(Boolean, name='is_percent')
	rate = Column(DECIMAL, name='rate')
	enterprise_id = Column(
		Integer, ForeignKey('enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)

	invoice = relationship("Invoice", backref=backref('invoice_charge_invoice', order_by=invoice_id))
	enterprise = relationship("Enterprise", backref=backref('invoice_charge_enterprise', order_by=enterprise_id))
	taxes = relationship(
		"InvoiceChargeTax", backref=backref("invoice_charge_invoice_charge_taxes"),
		primaryjoin="""and_(InvoiceCharge.invoice_id==InvoiceChargeTax.invoice_id,
							InvoiceCharge.enterprise_id==InvoiceChargeTax.enterprise_id,
							InvoiceCharge.item_name==InvoiceChargeTax.item_name)""",
		cascade="save-update, merge, delete-orphan, delete", single_parent=True)

	def __init__(
			self, invoice_id=None, item_name="Others", hsn_code="", enterprise_id=None, rate=0.00, discount=0.00,
			is_percent=False):
		self.invoice_id = invoice_id
		self.enterprise_id = enterprise_id
		self.item_name = item_name
		self.hsn_code = hsn_code
		self.rate = rate
		self.discount = discount
		self.is_percent = is_percent

	def __repr__(self):
		return "%s" % self.__dict__

	def getTotalCharge(self):
		return self.rate * (100 - (self.discount if self.discount else 0)) / 100

	def getInvoiceChargeTaxValue(self):
		"""

		:return:
		"""
		item_quantity = self.quantity if 'quantity' in self.__dict__ and self.quantity else 1
		cgst_rate, sgst_rate, igst_rate = [0, 0, 0]
		cgst_value, sgst_value, igst_value = [0, 0, 0]
		for charge_tax in self.getTaxesOfType("CGST"):
			cgst_rate = charge_tax.tax.net_rate if charge_tax else 0
			cgst_value = item_quantity * self.rate * (100 - self.discount) * Decimal(cgst_rate) / (100 * 100)

		for charge_tax in self.getTaxesOfType("SGST"):
			sgst_rate = charge_tax.tax.net_rate if charge_tax else 0
			sgst_value = item_quantity * self.rate * (100 - self.discount) * Decimal(sgst_rate) / (100 * 100)

		for charge_tax in self.getTaxesOfType("IGST"):
			igst_rate = charge_tax.tax.net_rate if charge_tax else 0
			igst_value = item_quantity * self.rate * (100 - self.discount) * Decimal(igst_rate) / (100 * 100)

		invoice_charge_tax_values = {
			"cgst_rate": cgst_rate, "cgst_value": cgst_value,
			"sgst_rate": sgst_rate, "sgst_value": sgst_value,
			"igst_rate": igst_rate, "igst_value": igst_value}
		return invoice_charge_tax_values


class InvoiceChargeTax(Base):
	__tablename__ = u'invoice_charge_tax'

	invoice_id = Column(
		Integer, ForeignKey("invoice_charges.invoice_id"), primary_key=True, name="invoice_id", autoincrement=False)
	item_name = Column(
		String(200), ForeignKey("invoice_charges.item_name"), primary_key=True, name="item_name", autoincrement=False)
	enterprise_id = Column(
		Integer, ForeignKey("enterprise.id"), primary_key=True, name="enterprise_id", autoincrement=False)
	tax_code = Column(String(20), ForeignKey("tax.code"), primary_key=True, name="tax_code", autoincrement=False)

	invoice_item = relationship(
		"InvoiceCharge", backref="invoice_charge_tax_invoice_charge",
		primaryjoin="""and_(InvoiceChargeTax.invoice_id==InvoiceCharge.invoice_id,
							InvoiceChargeTax.enterprise_id==InvoiceCharge.enterprise_id,
							InvoiceChargeTax.item_name==InvoiceCharge.item_name)""")
	tax = relationship(
		"Tax", backref="invoice_charge_tax_tax",
		primaryjoin="and_(InvoiceChargeTax.enterprise_id==Tax.enterprise_id, InvoiceChargeTax.tax_code==Tax.code)")

	def __init__(self, invoice_id=None, item_name=None, enterprise_id=None, tax_code=None):
		self.invoice_id = invoice_id
		self.item_name = item_name
		self.enterprise_id = enterprise_id
		self.tax_code = tax_code

	def __repr__(self):
		return "%s-%s-%s-%s" % (self.invoice_id, self.item_name, self.enterprise_id, self.tax_code)

	def __eq__(self, other):
		return self.invoice_id == other.invoice_id and self.item_name == other.item_name and self.tax_code == other.tax_code

	def getTaxLedgerId(self):
		return self.tax.getOutputLedgerId()


class InvoiceMaterial(Base, TaxableItem):
	""" Models invoice material table , its hold the  invoice materials detail """
	__tablename__ = u'invoice_materials'

	invoice_id = Column(Integer, ForeignKey('invoice.id'), primary_key=True, name='invoice_id', autoincrement=False)
	item_id = Column(Integer, ForeignKey('materials.id'), name='item_id', primary_key=True, autoincrement=False)
	hsn_code = Column(String(20), name='hsn_code')
	quantity = Column(DECIMAL, name='qty')
	rate = Column(DECIMAL, name='unit_rate')
	discount = Column(DECIMAL, name='discount', default=0.00)
	remarks = Column(String(100), name='remarks')
	enterprise_id = Column(Integer, ForeignKey(
		'materials.enterprise_id'), name='enterprise_id', primary_key=True, autoincrement=False)
	make_id = Column(Integer, ForeignKey('make.id'), name='make_id', primary_key=True, autoincrement=False)
	is_faulty = Column(Boolean, name='is_faulty', primary_key=True, autoincrement=False, default=False)
	oa_no = Column(Integer, ForeignKey('order_acknowledgement.id'), name='oa_no', autoincrement=False)
	is_returnable = Column(Boolean, name='is_returnable', default=False)
	entry_order = Column(Integer, name='entry_order', primary_key=True, autoincrement=False)
	delivered_dc_id = Column(Integer, ForeignKey("invoice.id"), name="delivered_dc_id", autoincrement=False, nullable=True)
	receipt_no = Column(Integer, ForeignKey("grn.grn_no"), name="grn_id", autoincrement=False, nullable=True)
	alternate_unit_id = Column(Integer, ForeignKey('materials_alternate_units.alternate_unit_id'), name='alternate_unit_id', default=None)
	inspection_log = Column(JSON, name='inspection_log')
	location_id = Column(Integer, name='location_id', autoincrement=False, nullable=True)

	UniqueConstraint(invoice_id, item_id, enterprise_id, make_id, is_faulty, oa_no, delivered_dc_id, receipt_no)

	invoice = relationship(
		"Invoice", backref=backref('invoice_material_invoice'),
		primaryjoin="""and_(Invoice.id == InvoiceMaterial.invoice_id, Invoice.enterprise_id == InvoiceMaterial.enterprise_id)"""
	)
	delivered_dc = relationship(
		"Invoice", backref=backref('invoice_material_invoice_dc'),
		primaryjoin="""and_(Invoice.id == InvoiceMaterial.delivered_dc_id, Invoice.enterprise_id == InvoiceMaterial.enterprise_id)"""
	)
	grn = relationship(
		"Receipt", backref='invoice_material_grn_received_against_oa',
		primaryjoin='and_(Receipt.receipt_no == InvoiceMaterial.receipt_no, Receipt.enterprise_id == InvoiceMaterial.enterprise_id)')

	item = relationship(
		"Material", backref=backref("invoice_materials_material"),
		primaryjoin="and_(InvoiceMaterial.item_id==Material.material_id)")
	items_received = relationship(
		"ReceiptMaterial", backref=backref("invoice_material_receipt_material"),
		primaryjoin="""and_(InvoiceMaterial.item_id==ReceiptMaterial.item_id,
		InvoiceMaterial.make_id==ReceiptMaterial.make_id,
		InvoiceMaterial.is_faulty==ReceiptMaterial.is_faulty,
		InvoiceMaterial.enterprise_id==ReceiptMaterial.enterprise_id,
		InvoiceMaterial.invoice_id==foreign(ReceiptMaterial.dc_id))""")
	taxes = relationship(
		"InvoiceMaterialTax", backref=backref("invoice_material_invoice_material_taxes"),
		primaryjoin="""and_(InvoiceMaterial.invoice_id==InvoiceMaterialTax.invoice_id,
							InvoiceMaterial.item_id==InvoiceMaterialTax.item_id,
							InvoiceMaterial.enterprise_id==InvoiceMaterialTax.enterprise_id,
							InvoiceMaterial.make_id==InvoiceMaterialTax.make_id,							
							InvoiceMaterial.is_faulty==InvoiceMaterialTax.is_faulty,
							or_(InvoiceMaterial.oa_no==InvoiceMaterialTax.oa_no,
							and_(InvoiceMaterial.oa_no.is_(None),
							InvoiceMaterialTax.oa_no.is_(None))),
							or_(InvoiceMaterial.delivered_dc_id==InvoiceMaterialTax.delivered_dc_id
							, and_(InvoiceMaterial.delivered_dc_id.is_(None)
							, InvoiceMaterialTax.delivered_dc_id.is_(None))),
							or_(InvoiceMaterial.receipt_no==InvoiceMaterialTax.receipt_no
							, and_(InvoiceMaterial.receipt_no.is_(None)
							, InvoiceMaterialTax.receipt_no.is_(None))))""",
		cascade="save-update, merge, delete-orphan, delete", single_parent=True)
	make = relationship(
		"Make", backref='invoice_material_make',
		primaryjoin="and_(InvoiceMaterial.make_id==Make.id, InvoiceMaterial.enterprise_id==Make.enterprise_id)")
	inv_oa = relationship("OA", backref=backref("oa_inv_mat", order_by=oa_no))

	alternate_unit = relationship(
		"MaterialAlternateUnit", backref='invoice_material_alternate_unit',
		primaryjoin="""and_(MaterialAlternateUnit.enterprise_id==InvoiceMaterial.enterprise_id, 
			MaterialAlternateUnit.alternate_unit_id==InvoiceMaterial.alternate_unit_id, 
			MaterialAlternateUnit.item_id==InvoiceMaterial.item_id)""")

	def __init__(
			self, invoice_id=None, item_id=None, enterprise_id=None, quantity=0, rate=0, discount=0, remarks="",
			make_id=None, is_faulty=0, is_returnable=0, oa_no=None, entry_order=None, delivered_dc_id=None,
			receipt_no=None):
		self.invoice_id = invoice_id
		self.enterprise_id = enterprise_id
		self.item_id = item_id
		self.quantity = quantity
		self.rate = rate
		self.discount = discount
		self.remarks = remarks
		self.make_id = make_id
		self.is_faulty = is_faulty
		self.is_returnable = is_returnable
		self.oa_no = oa_no
		self.entry_order = entry_order
		self.delivered_dc_id = delivered_dc_id
		self.receipt_no = receipt_no

	def __repr__(self):
		return "%s" % self.__dict__


class InvoiceMaterialTax(Base):
	"""

	"""
	__tablename__ = u"invoice_item_tax"

	invoice_id = Column(Integer, ForeignKey(
		"invoice_materials.invoice_id"), primary_key=True, name="invoice_id", autoincrement=False)
	item_id = Column(Integer, ForeignKey('invoice_materials.item_id'), name='item_id', primary_key=True,
	                 autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey(
		"enterprise.id"), primary_key=True, name="enterprise_id", autoincrement=False)
	tax_code = Column(String(20), ForeignKey("tax.code"), primary_key=True, name="tax_code", autoincrement=False)
	make_id = Column(Integer, ForeignKey("make.id"), primary_key=True, name="make_id", autoincrement=False)
	is_faulty = Column(Boolean, name='is_faulty', primary_key=True, autoincrement=False, default=False)
	oa_no = Column(Integer, ForeignKey('order_acknowledgement.id'), name='oa_no', autoincrement=False)
	delivered_dc_id = Column(Integer, name="delivered_dc_id", autoincrement=False, nullable=True)
	receipt_no = Column(Integer, ForeignKey("grn.grn_no"), name="grn_id", autoincrement=False, nullable=True)

	invoice_item = relationship(
		"InvoiceMaterial", backref="invoice_item_tax_invoice_material",
		primaryjoin="""and_(InvoiceMaterialTax.invoice_id==InvoiceMaterial.invoice_id,
			InvoiceMaterialTax.item_id==InvoiceMaterial.item_id,
			InvoiceMaterialTax.enterprise_id==InvoiceMaterial.enterprise_id,
			InvoiceMaterialTax.make_id==InvoiceMaterial.make_id,
			InvoiceMaterialTax.is_faulty==InvoiceMaterial.is_faulty,
			or_(InvoiceMaterial.oa_no==InvoiceMaterialTax.oa_no,
				and_(InvoiceMaterial.oa_no.is_(None), InvoiceMaterialTax.oa_no.is_(None))),
			or_(InvoiceMaterial.delivered_dc_id==InvoiceMaterialTax.delivered_dc_id,
				and_(InvoiceMaterial.delivered_dc_id.is_(None), InvoiceMaterialTax.delivered_dc_id.is_(None))),
			or_(InvoiceMaterial.receipt_no==InvoiceMaterialTax.receipt_no,
				and_(InvoiceMaterial.receipt_no.is_(None), InvoiceMaterialTax.receipt_no.is_(None))))""")
	tax = relationship(
		"Tax", backref="invoice_item_tax_tax",
		primaryjoin="and_(InvoiceMaterialTax.enterprise_id==Tax.enterprise_id, InvoiceMaterialTax.tax_code==Tax.code)")
	make = relationship(
		"Make", backref='invoice_material_tax_make',
		primaryjoin="and_(InvoiceMaterialTax.make_id==Make.id, InvoiceMaterialTax.enterprise_id==Make.enterprise_id)")

	def __init__(
			self, invoice_id=None, item_id=None, enterprise_id=None, tax_code=None, make_id=None, is_faulty=0,
			oa_no=None, delivered_dc_id=None, receipt_no=None):
		self.invoice_id = invoice_id
		self.item_id = item_id
		self.enterprise_id = enterprise_id
		self.tax_code = tax_code
		self.make_id = make_id
		self.is_faulty = is_faulty
		self.oa_no = oa_no
		self.delivered_dc_id = delivered_dc_id
		self.receipt_no = receipt_no

	def __repr__(self):
		return "%s-%s-%s-%s" % (self.invoice_id, self.item_id, self.enterprise_id, self.tax_code)

	def __eq__(self, other):
		return self.invoice_id == other.invoice_id and self.item_id == other.item_id and self.tax_code == other.tax_code

	def getTaxLedgerId(self):
		return self.tax.getOutputLedgerId()


class InvoiceTag(Base):
	""" Tagging model for Invoices """
	__tablename__ = u'invoice_tags'

	invoice_id = Column(Integer, ForeignKey('invoice.id'), primary_key=True, name='invoice_id', autoincrement=False)
	tag_id = Column(Integer, ForeignKey('tags.id'), primary_key=True, name='tag_id', autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), primary_key=True, name='enterprise_id', autoincrement=False)

	tag = relationship("Tag", backref=backref("invoice_tag_tag", order_by=(tag_id, enterprise_id)))
	invoice = relationship("Invoice", backref=backref("invoice_tags_invoice", order_by=invoice_id))

	def __init__(self, invoice_id=None, tag_id=None, enterprise_id=None):
		self.enterprise_id = enterprise_id
		self.invoice_id = invoice_id
		self.tag_id = tag_id

	def __repr__(self):
		return "[%s] %s" % (self.invoice, self.tag)

	def __eq__(self, other):
		return (self.invoice_id == InvoiceTag(other).invoice_id) and (self.tag_id == InvoiceTag(other).tag_id)


class InvoiceTax(Base):
	__tablename__ = u'invoice_tax'

	invoice_id = Column(Integer, ForeignKey('invoice.id'), name='invoice_id', primary_key=True, autoincrement=False)
	tax_code = Column(String, ForeignKey('tax.code'), name='tax_code', primary_key=True, autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)

	invoice = relationship("Invoice", backref=backref('invoice_tax_invoice', order_by=invoice_id))
	tax = relationship("Tax", backref='invoice_tax_tax', primaryjoin=(
		"and_(InvoiceTax.tax_code == Tax.code, InvoiceTax.enterprise_id == Tax.enterprise_id)"))

	def __init__(self, invoice_id=None, tax_code=None, enterprise_id=None):
		self.invoice_id = invoice_id
		self.tax_code = tax_code
		self.enterprise_id = enterprise_id

	def __repr__(self):
		return "%s" % self.__dict__

	def getTaxLedgerId(self):
		return self.tax.getOutputLedgerId()


class InvoiceDocument(Base, AutoSerialize):
	""" Model to collect invoice related documents """
	__tablename__ = u'invoice_document'

	invoice_id = Column(Integer, ForeignKey('invoice.id'), primary_key=True, name='invoice_id', autoincrement=False)
	is_rejected = Column(Boolean, name='is_rejected', default=False)
	document_pdf = Column(LargeBinary, name='document_pdf')
	document_txt = Column(LargeBinary, name='document_txt')
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	revised_on = Column(DateTime, primary_key=True, name='revised_on', autoincrement=False)
	revised_by = Column(Integer, ForeignKey('auth_user.id'), name='revised_by')

	reviser = relationship("User", backref=backref('invoice_reviser', order_by=revised_by))
	invoice = relationship("Invoice", backref=backref('invoice_document_invoice', order_by=invoice_id))

	def __init__(
			self, invoice_id=None, document_pdf=None, document_txt=None, is_rejected=False, enterprise_id=None,
			revised_on=None, revised_by=None):
		self.invoice_id = invoice_id
		self.document_pdf = document_pdf
		self.document_txt = document_txt
		self.is_rejected = is_rejected
		self.enterprise_id = enterprise_id
		self.revised_on = revised_on
		self.revised_by = revised_by

	def __repr__(self):
		return '%s %s' % (self.invoice_id, self.document_pdf)


class InvoiceMaterialSerialNo(Base, TaxableItem):
	""" Models invoice serial number table , its hold the  invoice materials serial number detail """
	__tablename__ = u'invoice_serial_number_details'

	invoice_id = Column(Integer, ForeignKey('invoice.id'), primary_key=True, name='invoice_id', autoincrement=False)
	serial_number = Column(String(32), ForeignKey(
		'material_serial_number_details.serial_number'), name='serial_number', primary_key=True, autoincrement=False)

	invoice = relationship("Invoice", backref=backref('invoice_serial_number_invoice', order_by=invoice_id))

	def __init__(
			self, invoice_id=None, serial_number=None):
		self.invoice_id = invoice_id
		self.serial_number = serial_number

	def __repr__(self):
		return "%s" % self.__dict__


class Notification(Base, AutoSerialize):
	"""
	Notification to the user for an action to be known instantly
	"""

	__tablename__ = u'notification'

	id = Column(Integer, name='id', primary_key=True, autoincrement=True)
	message = Column(String, name='message')
	collapse_key = Column(String, name='collapse_key')
	created_on = Column(DateTime, name='created_on')
	created_by = Column(Integer, ForeignKey('auth_user.id'), name='created_by')
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name='enterprise_id')

	user_maps = relationship(
		"NotificationUserMap", backref=backref('notification_user_maps', order_by=id),
		cascade="save-update, merge, delete, delete-orphan")

	def __init__(self, message=None, create_on=None, created_by=None, enterprise_id=None, collapse_key=None):
		self.message = message
		self.created_by = created_by
		self.created_on = create_on
		self.enterprise_id = enterprise_id
		self.collapse_key = collapse_key

	def __repr__(self):
		return '%s - %s' % (self.id, self.message)


class ImportMessage(Base, AutoSerialize):
	"""
	Notification to the user for an action to be known instantly
	"""

	__tablename__ = u'import_messages'

	id = Column(Integer, name='id', primary_key=True, autoincrement=True)
	message = Column(String, name='message')
	created_on = Column(DateTime, name='created_on')
	created_by = Column(Integer, ForeignKey('auth_user.id'), name='created_by')
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	is_process = Column(Boolean, name='is_process', default=False)

	def __init__(self, message=None, create_on=None, created_by=None, enterprise_id=None, is_process=False):
		self.message = message
		self.created_by = created_by
		self.created_on = create_on
		self.enterprise_id = enterprise_id
		self.is_process = is_process

	def __repr__(self):
		return '%s - %s' % (self.id, self.message)


class FcmUserRegister(Base, AutoSerialize):
	"""
	Notification register ids for devices will be kept here
	"""

	__tablename__ = u'fcm_user_register'

	fcm_id = Column(String, name='fcm_id', primary_key=True)
	user_id = Column(Integer, ForeignKey('auth_user.id'), name='user_id')
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name='enterprise_id')

	user = relationship("User", backref=backref('fcm_user_register_user', order_by=user_id))

	def __init__(self, user_id=None, fcm_id=None, enterprise_id=None):
		self.user_id = user_id
		self.fcm_id = fcm_id
		self.enterprise_id = enterprise_id

	def __repr__(self):
		return '%s - %s' % (self.user_id, self.fcm_id)


class NotificationUserMap(Base, AutoSerialize):
	"""
	Notification to the user for an action to be known instantly
	"""

	__tablename__ = u'notification_user_map'

	user_id = Column(Integer, ForeignKey('auth_user.id'), primary_key=True, autoincrement=False)
	notification_id = Column(Integer, ForeignKey('notification.id'), primary_key=True, autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name='enterprise_id')

	notification = relationship(
		"Notification", backref=backref('user_notification_notification', order_by=notification_id))
	user = relationship("User", backref=backref('user_notification_user', order_by=user_id))

	def __init__(self, user_id=None, notification_id=None, enterprise_id=None):
		self.user_id = user_id
		self.notification_id = notification_id
		self.enterprise_id = enterprise_id

	def __repr__(self):
		return '%s - %s' % (self.user_id, self.notification_id)


class Expense(Base, Remarkable, AutoSerialize):
	"""
	Employee Expenses header table
	"""

	__tablename__ = u'expenses'

	id = Column(Integer, name='id', primary_key=True, autoincrement=True)
	expense_no = Column(Integer, name='expense_no')
	sub_number = Column(String(1), name='sub_number')
	super_modified_on = Column(DateTime, name='super_modified_on')
	super_modified_by = Column(Integer, ForeignKey('auth_user.id'), name='super_modified_by')
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name='enterprise_id')
	claim_head_ledger_id = Column(Integer, ForeignKey('account_ledgers.id'), name='claim_head_ledger_id')
	group_description = Column(String(150), name='group_description')

	created_by = Column(Integer, ForeignKey('auth_user.id'), name='created_by')
	created_on = Column(DateTime, name='created_on')
	confirmed_on = Column(DateTime, name='confirmed_on')
	approved_by = Column(Integer, ForeignKey('auth_user.id'), name='approved_by')
	approved_on = Column(DateTime, name='approved_on')

	checked_by = Column(Integer, ForeignKey('auth_user.id'), name='checked_by')
	checked_on = Column(DateTime, name='checked_on')
	verified_by = Column(Integer, ForeignKey('auth_user.id'), name='verified_by')
	verified_on = Column(DateTime, name='verified_on')

	status = Column(Integer, name='status')
	financial_year = Column(String(10), name='financial_year')
	remarks = Column(JSON, name='remarks')

	enterprise = relationship("Enterprise", backref=backref('expense_enterprise', order_by=enterprise_id))
	created_user = relationship(
		"User", backref='expense_created_user',
		primaryjoin='and_(Expense.created_by == User.id)')
	approved_user = relationship(
		"User", backref='expense_approved_user',
		primaryjoin='and_(User.id == Expense.approved_by)')
	checked_user = relationship(
		"User", backref='expense_checked_user',
		primaryjoin='and_(User.id == Expense.checked_by)')
	verified_user = relationship(
		"User", backref='expense_verified_user',
		primaryjoin='and_(User.id == Expense.verified_by)')
	super_modified_user = relationship(
		"User", backref='expense_super_modified_user',
		primaryjoin='and_(User.id == Expense.super_modified_by)')
	claim_head_ledger = relationship(
		"Ledger", backref=backref('expense_claim_head_ledger', order_by=claim_head_ledger_id))

	expense_tags = relationship("ExpenseTag", backref=backref(
		"expense_tags_tags", order_by=id), cascade="save-update, merge, delete, delete-orphan")

	particulars = relationship(
		"ExpenseParticular", backref="expense_particulars_particulars",
		primaryjoin="(Expense.id==ExpenseParticular.expense_id)", cascade="save-update, merge, delete, delete-orphan")

	def __init__(
			self, expense_no=0, enterprise_id=None, claim_head_ledger_id=None, group_description=None, status=None,
			created_by=None, created_on=None, confirmed_on=None, approved_by=None, approved_on=None,
			checked_by=None, checked_on=None, verified_by=None, verified_on=None, financial_year=None,
			sub_number=None, super_modified_on=None, super_modified_by=None, *args, **kwargs):
		super(Expense, self).__init__(*args, **kwargs)
		self.expense_no = expense_no
		self.sub_number = sub_number
		self.super_modified_on = super_modified_on
		self.super_modified_by = super_modified_by
		self.enterprise_id = enterprise_id
		self.claim_head_ledger_id = claim_head_ledger_id
		self.group_description = group_description
		self.created_by = created_by
		self.created_on = created_on if created_on else time.strftime("%Y-%m-%d %H:%M:%S")
		self.confirmed_on = confirmed_on
		self.approved_by = approved_by
		self.approved_on = approved_on
		self.checked_by = checked_by
		self.checked_on = checked_on
		self.verified_by = verified_by
		self.verified_on = verified_on
		self.status = status
		self.financial_year = financial_year if financial_year else getFinancialYear()

	def getCode(self):
		return "DRAFT#%s" % self.id if self.expense_no == 0 else "%s/EXP/%06d%s" % (
			self.financial_year, self.expense_no, "%s" % self.sub_number.strip() if self.sub_number else "")

	def __repr__(self):
		return '%s' % self.__dict__

	def getTotalExpense(self):
		claimed_amount, approved_amount = float(0), float(0)
		for particular in self.particulars:
			claimed_amount += float(particular.amount)
			approved_amount += particular.getApprovedAmount()
		return claimed_amount, approved_amount


class ExpenseParticular(Base, Remarkable, AutoSerialize):
	"""
	Employee Expenses header table
	"""

	__tablename__ = u'expense_particulars'

	expense_id = Column(Integer, ForeignKey('expenses.id'), name='expense_id', primary_key=True, autoincrement=False)
	item_no = Column(Integer, name='item_no', primary_key=True, autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name='enterprise_id')
	expense_head_ledger_id = Column(Integer, ForeignKey('account_ledgers.id'), name='expense_head_ledger_id')
	spent_on = Column(Date, name='spent_on')
	description = Column(String, name='description')
	amount = Column(DECIMAL, name='amount')
	approver_debit = Column(DECIMAL, name='approver_debit')
	audit_debit = Column(DECIMAL, name='audit_debit')
	document = Column(String(60), name='document')
	bill_available = Column(Integer, name='bill_available')
	remarks = Column(JSON, name='remarks')

	expense = relationship("Expense", backref=backref('expense_particulars_expense', order_by=expense_id))
	enterprise = relationship("Enterprise", backref=backref('expense_particulars_enterprise', order_by=enterprise_id))

	expense_head_ledger = relationship(
		"Ledger", backref=backref('expense_particulars_expense_head_ledger', order_by=expense_head_ledger_id))

	def __init__(
			self, expense_id=None, item_no=None, enterprise_id=None, expense_head_ledger_id=None, spent_on=None,
			description=None, amount=None, approver_debit=None, audit_debit=None,
			document=None, bill_available=False, *args, **kwargs):
		super(ExpenseParticular, self).__init__(*args, **kwargs)
		self.expense_id = expense_id
		self.item_no = item_no
		self.enterprise_id = enterprise_id
		self.expense_head_ledger_id = expense_head_ledger_id
		self.spent_on = spent_on
		self.description = description
		self.amount = amount
		self.approver_debit = approver_debit
		self.audit_debit = audit_debit
		self.document = document
		self.bill_available = bill_available

	def __repr__(self):
		return "{enterprise_id:%s, expense_id:%s} %s, %s: amount:%s, approver_debit:%s, audit_debit:%s, bill:%s" % (
			self.enterprise_id, self.expense_id, self.item_no, self.spent_on, self.amount, self.approver_debit,
			self.audit_debit, self.document)

	def getApprovedAmount(self):
		return float(self.amount) - float(self.audit_debit)


class ExpenseTag(Base, AutoSerialize):
	"""
	Expenses tags
	"""

	__tablename__ = u'expense_tags'

	expense_id = Column(Integer, ForeignKey('expenses.id'), name='expense_id', primary_key=True, autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name='enterprise_id', primary_key=True,
	                       autoincrement=False)
	tag_id = Column(Integer, ForeignKey('tags.id'), name='tag_id', primary_key=True, autoincrement=False)

	expense = relationship("Expense", backref=backref('expense_tag_expense', order_by=expense_id))
	tag = relationship("Tag", backref=backref('expense_tag_tag', order_by=(tag_id, enterprise_id)))

	def __init__(self, expense_id=None, tag_id=None, enterprise_id=None):
		"""

		:param expense_id:
		:param tag_id:
		:param enterprise_id:
		"""

		self.expense_id = expense_id
		self.tag_id = tag_id
		self.enterprise_id = enterprise_id

	def __repr__(self):
		"""

		:return:
		"""
		return "%s - %s" % (self.tag, (self.expense_id, self.enterprise_id, self.tag_id))


class Make(Base):
	""" Models the Make """
	__tablename__ = u'make'

	id = Column(Integer, primary_key=True, autoincrement=False, name='id')
	label = Column(String(45), name='make_name')
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name='enterprise_id', primary_key=True,
	                       autoincrement=False)

	def __init__(self, id=None, label="", enterprise_id=None):
		"""

		:param id:
		:param label:
		:param enterprise_id:
		"""
		self.id = id
		self.label = label
		self.enterprise_id = enterprise_id

	def __repr__(self):
		return self.label


class MaterialMakeMap(Base):
	""" Material profile with Make  """
	__tablename__ = u'material_make_map'

	make_id = Column(Integer, ForeignKey('make.id'), primary_key=True, name='make_id', autoincrement=False)
	part_no = Column(String(20), name='part_no')
	item_id = Column(Integer, ForeignKey('materials.id'), name='item_id', primary_key=True, autoincrement=False)
	enterprise_id = Column(
		Integer, ForeignKey('enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	standard_packing_quantity = Column(DECIMAL, name='standard_packing_quantity')

	material = relationship(
		"Material", backref='material_material_make', primaryjoin="""and_(
		MaterialMakeMap.item_id == Material.material_id, MaterialMakeMap.enterprise_id == Material.enterprise_id)""")
	make = relationship(
		"Make", backref='make_make',
		primaryjoin="and_(MaterialMakeMap.enterprise_id == Make.enterprise_id, MaterialMakeMap.make_id == Make.id)")

	enterprise = relationship(
		"Enterprise", backref="material_make_enterprise",
		primaryjoin="and_(MaterialMakeMap.enterprise_id==Enterprise.id)")

	def __init__(self, make_id=1, part_no=None, item_id=None, enterprise_id=None, standard_packing_quantity=None):
		self.make_id = make_id
		self.part_no = part_no
		self.item_id = item_id
		self.enterprise_id = enterprise_id
		self.standard_packing_quantity = standard_packing_quantity

	def __repr__(self):
		return "%s-%s %s" % (self.enterprise_id, self.item_id, self.make_id)


class MaterialAlternateUnit(Base):
	""" Material profile with Alternate units  """
	__tablename__ = u'materials_alternate_units'

	item_id = Column(Integer, ForeignKey('materials.id'), name='item_id', primary_key=True, autoincrement=False)
	alternate_unit_id = Column(Integer, ForeignKey('unit_master.unit_id'), primary_key=True, name='alternate_unit_id', autoincrement=False)
	scale_factor = Column(DECIMAL, name='scale_factor', primary_key=True, autoincrement=False)
	enterprise_id = Column(
		Integer, ForeignKey('materials.enterprise_id'), name='enterprise_id', primary_key=True, autoincrement=False)
	UniqueConstraint(enterprise_id, item_id, alternate_unit_id, scale_factor)

	material = relationship(
		"Material", backref='materials_alternate_units', primaryjoin="""and_(
		MaterialAlternateUnit.item_id == Material.material_id, MaterialAlternateUnit.enterprise_id == Material.enterprise_id)""")
	unit = relationship(
		"UnitMaster", backref='material_alternate_unit_unit_id',
		primaryjoin="and_(UnitMaster.enterprise_id==MaterialAlternateUnit.enterprise_id, UnitMaster.unit_id==MaterialAlternateUnit.alternate_unit_id)")

	def __init__(self, item_id=None, alternate_unit_id=None, scale_factor=0, enterprise_id=None):
		self.item_id = item_id
		self.alternate_unit_id = alternate_unit_id
		self.scale_factor = scale_factor
		self.enterprise_id = enterprise_id

	def __repr__(self):
		return "%s" % [self.enterprise_id, self.item_id, self.alternate_unit_id, self.scale_factor]


class PayStructure(Base):
	"""
	Data Model Object for the table Ledger Bills. Captures any bill creation against a ledger transaction
	Uniquely identified by bill_no, bill_date within a ledger - hence composite-key - [ledger_id, bill_no, bill_date]
	"""

	__tablename__ = u'pay_structure'

	id = Column(Integer, name='id', autoincrement=True, primary_key=True)
	description = Column(String(50), name='description')
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name="enterprise_id")

	items = relationship(
		"PayStructureItem", backref="pay_structure_items_pay",
		primaryjoin="and_(PayStructureItem.pay_structure_id==PayStructure.id, PayStructureItem.enterprise_id==PayStructure.enterprise_id)",
		cascade="save-update, merge, delete, delete-orphan")

	def __init__(self, description="", enterprise_id=None):
		self.description = description
		self.enterprise_id = enterprise_id

	def __eq__(self, other):
		return self.id == other.id

	def __repr__(self):
		return "%s" % self.description


class EmployeePayStructureItem(Base, AutoSerialize):
	""" Data model for the table 'ledger_bill_settlements'. Captures bill settlements against any voucher entry. """
	__tablename__ = u'employee_pay_structure_items'

	employee_id = Column(Integer, ForeignKey('employee_master.emp_id'), name='employee_id', primary_key=True)
	type = Column(String(50), name='type', primary_key=True)
	description = Column(String(50), name='description', primary_key=True)
	enterprise_id = Column(Integer, ForeignKey('pay_structure.enterprise_id'), name="enterprise_id", primary_key=True)
	amount = Column(DECIMAL, name='amount')

	employee = relationship("Employee", backref=backref('employee_pay_structure', order_by=employee_id))

	def __init__(self, employee_id=None, type=None, description="", amount=0, enterprise_id=None):
		self.employee_id = employee_id
		self.type = type
		self.description = description
		self.amount = amount
		self.enterprise_id = enterprise_id

	def __repr__(self):
		return "%s" % self.__dict__

	def getEarned(self, weight):
		"""

		:param weight:
		:return: fixed * weight
		"""
		return float(self.amount) * weight


class PayStructureItem(Base, AutoSerialize):
	""" Data model for the table 'ledger_bill_settlements'. Captures bill settlements against any voucher entry. """
	__tablename__ = u'pay_structure_items'

	pay_structure_id = Column(Integer, ForeignKey('pay_structure.id'), name='pay_structure_id', primary_key=True)
	type = Column(Integer, name='type', primary_key=True)
	description = Column(String(50), name='description', primary_key=True)
	enterprise_id = Column(Integer, ForeignKey('pay_structure.enterprise_id'), name="enterprise_id", primary_key=True)

	pay_structure = relationship(
		"PayStructure", backref="pay_structure_pay_structure",
		primaryjoin="and_(PayStructureItem.pay_structure_id==PayStructure.id, PayStructureItem.enterprise_id==PayStructure.enterprise_id)")

	def __init__(self, pay_structure_id=None, type=None, description="", enterprise_id=None):
		self.pay_structure_id = pay_structure_id
		self.type = type
		self.description = description
		self.enterprise_id = enterprise_id

	def __repr__(self):
		return "%s" % self.__dict__


class Department(Base, AutoSerialize):
	""" Data Model Object for the table Ledger """
	__tablename__ = u'department'
	__table_args__ = (
		ForeignKeyConstraint(('enterprise_id', 'parent_id'), ['department.enterprise_id', 'department.id']),)

	id = Column(Integer, name='id', primary_key=True, autoincrement=True)
	parent_id = Column(Integer, name='parent_id', nullable=True)
	name = Column(String(100), name='name')
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name="enterprise_id", primary_key=True, autoincrement=False)
	created_on = Column(DateTime, name='created_on')
	created_by = Column(Integer, ForeignKey('auth_user.id'), name='created_by')

	UniqueConstraint(enterprise_id, name, parent_id)

	enterprise = relationship("Enterprise", backref=backref("department_enterprise", order_by=enterprise_id))
	parent = relationship(
		"Department", backref="parent_department", remote_side=[id, enterprise_id],
		cascade="save-update, merge, delete")

	def __init__(self, parent_id=None, name='', enterprise_id=None, created_by=None, created_on=None):
		"""

		:param parent_id:
		:param name:
		:param enterprise_id:
		:param created_by:
		:param created_on:
		"""
		self.parent_id = parent_id
		self.name = name
		self.enterprise_id = enterprise_id
		self.created_by = created_by
		self.created_on = created_on if created_on else time.strftime('%Y-%m-%d %H:%M:%S')

	def __eq__(self, other):
		return self.parent_id == other.parent_id and self.name == other.name and self.enterprise_id == other.enterprise_id

	def __repr__(self):
		return "%s" % self.name


class EmployeeAttendanceLog(Base):
	"""

	"""
	__tablename__ = u'employee_attendance_log'

	employee_code = Column(String, ForeignKey('employee_master.emp_code'), name='emp_code', primary_key=True)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	since = Column(DateTime, name='since', primary_key=True)
	till = Column(DateTime, name='till', primary_key=True)
	description = Column(JSON, name='description')

	employee = relationship("Employee", backref=backref(
		"attendance_report_employee", order_by=(enterprise_id, employee_code)))
	enterprise = relationship("Enterprise", backref=backref("attendance_report_enterprise", order_by=enterprise_id))

	def __init__(self, employee_code=None, enterprise_id=None, since=None, till=0, description=None):
		self.employee_code = employee_code
		self.enterprise_id = enterprise_id
		self.since = since
		self.till = till
		self.description = description

	def __repr__(self):
		return "%s-%s: %s - %s, %s" % (self.enterprise_id, self.employee_code, self.since, self.till, self.description)

	def getWeight(self):
		"""
		LOP: loss of pay, LP - late punch

		:return:
		"""
		total = float(self.description['TSD'])
		lop = float(self.description['LOP']) + float(self.description['LP']) / 8.0
		return (total - lop) / total


class EmployeeSalary(Base):
	"""

	"""
	__tablename__ = u'employee_salary'

	employee_code = Column(String, ForeignKey('employee_master.emp_code'), name='emp_code', primary_key=True)
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name='enterprise_id', primary_key=True,
	                       autoincrement=False)
	since = Column(Date, name='since', primary_key=True)
	till = Column(Date, name='till', primary_key=True)
	description = Column(JSON, name='description')
	created_on = Column(DateTime, name='created_on')
	updated_on = Column(DateTime, name='updated_on')
	created_by = Column(Integer, ForeignKey('auth_user.id'), name='created_by')
	updated_by = Column(Integer, ForeignKey('auth_user.id'), name='updated_by')

	employee = relationship("Employee", backref=backref(
		"salary_report_employee", order_by=(enterprise_id, employee_code)))
	enterprise = relationship("Enterprise", backref=backref("salary_report_enterprise", order_by=enterprise_id))
	created_user = relationship(
		"User", backref='salary_created_user',
		primaryjoin='foreign(User.id) == EmployeeSalary.created_by')
	last_updated_user = relationship(
		"User", backref='salary_last_updated_user',
		primaryjoin='foreign(User.id) == EmployeeSalary.updated_by')

	def __init__(
			self, employee_code=None, enterprise_id=None, since=None, till=0, description=None,
			created_on=datetime.now(), updated_on=datetime.now(), created_by=None, updated_by=None):
		self.employee_code = employee_code
		self.enterprise_id = enterprise_id
		self.since = since
		self.till = till
		self.description = description
		self.created_on = created_on
		self.updated_on = updated_on
		self.created_by = created_by
		self.updated_by = updated_by

	def __repr__(self):
		return "%s-%s: %s - %s, %s" % (self.enterprise_id, self.employee_code, self.since, self.till, self.description)

	def updateSalary(self, attendance):
		"""

		:param attendance:
		:return:
		"""
		try:
			earned_weight = attendance.getWeight()
			if self.description is None:
				self.description = {}
			self.description['EARNING'] = []
			for item in attendance.employee.pay_structure_items:
				if item.type == "Earning":
					self.description['EARNING'].append({
						"DESCRIPTION": item.description, "FIXED": float(item.amount),
						"EARNED": float(item.getEarned(earned_weight))})
		except:
			raise

	def getNetPay(self):
		"""

		:return:
		"""
		try:
			earnings = 0
			for item in self.description["EARNING"]:
				earnings += item["EARNED"]
			for item in self.description["DEDUCTION"]:
				earnings -= item["VALUE"]
			earnings += self.description["PREVIOUS BALANCE"]
			earnings -= self.description["UNPAID CHANGES"]
			return earnings
		except:
			raise


class OA(Base, AutoSerialize, Taxable, Remarkable):
	""" Class that models order_acknowledgement table. Its hold the order details of the . """
	__tablename__ = u'order_acknowledgement'

	TYPE_CHOICES = {"oa": (
		('Sale', 'Sale'),
		('Labour', 'Labour/Service'),
		('Job', 'Job Work'), ('IWO', 'IWO')),
		"mrs": ('MRS', 'MRS')
	}
	TYPES = {"oa": ("Sale", "Labour" ,"Job", "IWO"),
	         "mrs": ("mrs",)}

	TYPE_TITLES = {
		"Sale": "Sale",
		"Labour": "Labour/Service",
		"Job": "Job Work",
		"IWO": "IWO",
		"MRS": "MRS"
	}

	STATUS_NAMES = {0: "pending", 1: "approved", -1: "cancelled", -2: "rejected"}

	STATUS_DRAFT = 0
	STATUS_APPROVED = 1
	STATUS_CANCELLED = -1
	STATUS_REJECTED = -2

	id = Column(Integer, primary_key=True, name='id', autoincrement=True)
	type = Column(String(30), name='type')
	oa_no = Column(Integer, name='oa_no', default=0)
	sub_number = Column(String(1), name='sub_number')
	super_modified_on = Column(DateTime, name='super_modified_on')
	super_modified_by = Column(Integer, ForeignKey('auth_user.id'), name='super_modified_by')
	approved_on = Column(DateTime, name='approved_on')
	party_id = Column(Integer, ForeignKey('party_master.party_id'), name='party_id')
	prepared_on = Column(DateTime, name='prepared_on')
	po_no = Column(String, name='po_no')
	po_date = Column(Date, name='po_date')
	se_id = Column(Integer, ForeignKey('sales_estimate.id'), name='se_id')
	se_date = Column(DateTime, name='se_date')
	delivery_due_date = Column(Date, name='delivery_due_date')
	payment_terms = Column(String(150), name='payment_terms')
	special_instructions = Column(String(400), name='special_instructions')
	prepared_by = Column(Integer, name='prepared_by')
	approved_by = Column(Integer, ForeignKey('auth_user.id'), name='approved_by')
	last_modified_by = Column(Integer, ForeignKey('auth_user.id'), name='last_modified_by')
	last_modified_on = Column(DateTime, name='last_modified_on')
	financial_year = Column(String(5), name='financial_year')
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name='enterprise_id')
	status = Column(Integer, name='status', default=STATUS_DRAFT)
	currency_id = Column(Integer, ForeignKey('currency.id'), name='currency_id')
	currency_conversion_rate = Column(DECIMAL(precision=3), name="currency_conversion_rate", default=1.00)
	grand_total = Column(DECIMAL(precision=2), name='grand_total')
	round_off = Column(DECIMAL, name='round_off')
	document = Column(String(100), name='document')
	document_description = Column(String(100), name='document_description')
	remarks = Column(JSON, name='remarks')
	issue_id = Column(String(150), name='issue_id')
	project_code = Column(Integer, ForeignKey('projects.id'), name='project_code')

	items = relationship(
		"OAParticulars", primaryjoin="(OA.id==OAParticulars.oa_id)", backref="oa_oa_particulars",
		cascade="save-update, merge, delete, delete-orphan")
	enterprise = relationship("Enterprise", backref=backref("oa_enterprise", order_by=enterprise_id))
	customer = relationship("Party", backref=backref("oa_party_master", order_by=party_id))
	tags = relationship("OATag", backref=backref("oa_oa_tags", order_by=id), cascade="save-update, merge, delete")
	taxes = relationship(
		"OATax", primaryjoin="(OA.id == OATax.oa_id)", backref="oa_tax",
		cascade="save-update, merge, delete, delete-orphan")
	approver = relationship("User", backref='oa_approver', foreign_keys=[approved_by])
	# approver = relationship(
	# 	"User", backref='oa_approver',
	# 	primaryjoin='foreign(User.id) == OA.approved_by')
	currency = relationship("Currency", backref=backref("oa_currency", order_by=currency_id))
	documents = relationship(
		"OADocument", backref="oa_documents",
		primaryjoin="and_(OADocument.oa_id==OA.id, OADocument.revised_on==OA.last_modified_on)")
	revisions = relationship(
		"OADocument", backref="oa_revisions", order_by="OADocument.revised_on",
		primaryjoin="and_(OADocument.oa_id==OA.id, OADocument.revised_on!=OA.last_modified_on)")
	modifier = relationship(
		"User", backref='oa_modifier',
		primaryjoin='foreign(User.id) == OA.last_modified_by')
	super_modified_user = relationship(
		"User", backref='oa_super_modified_user',
		primaryjoin='foreign(User.id) == OA.super_modified_by')
	invoice_materials = relationship(
		"InvoiceMaterial", backref=backref(
			"invoice_material_oas",
			primaryjoin="and_(InvoiceMaterial.oa_no==OA.id, InvoiceMaterial.enterprise_id==OA.enterprise_id)"),
		cascade="save-update, merge, delete, delete-orphan")
	grn_items = relationship(
		"ReceiptMaterial", primaryjoin="(OA.id==ReceiptMaterial.oa_id)", backref="oa_grn_materials",
		cascade="save-update, merge, delete, delete-orphan")
	se = relationship("SalesEstimate", backref="se_oa_se", primaryjoin="and_(OA.se_id == SalesEstimate.id)")
	project = relationship(
		"Project", backref='oa_project', lazy="subquery",
		primaryjoin="and_(OA.project_code==Project.id, OA.enterprise_id==Project.enterprise_id)")


	def __init__(
			self, id=None, oa_no=0, approved_on=None, prepared_on=None, po_no=None, po_date=None, se_id=None, se_date=None,
			party_id=None, status=STATUS_DRAFT, currency_id=356, payment_terms=None, delivery_due_date=None, prepared_by=None,
			grand_total=0.00, round_off=0.00, special_instructions=None, approved_by=None, financial_year=None,
			enterprise_id=None, type=None, document=None, document_description=None, issue_id=None, project_code=None,
			sub_number=None, super_modified_on=None, super_modified_by=None, *args, **kwargs):
		super(OA, self).__init__(*args, **kwargs)
		self.id = id
		self.oa_no = oa_no
		self.po_no = po_no
		self.se_id = se_id
		self.sub_number = sub_number
		self.super_modified_on = super_modified_on
		self.super_modified_by = super_modified_by
		self.approved_on = approved_on
		self.party_id = party_id
		self.prepared_on = prepared_on if prepared_on else time.strftime('%Y-%m-%d %H:%M:%S')
		self.po_date = po_date
		self.se_date = se_date
		self.special_instructions = special_instructions
		self.payment_terms = payment_terms
		self.prepared_by = prepared_by
		self.approved_by = approved_by
		self.financial_year = financial_year if financial_year else getFinancialYear()
		self.enterprise_id = enterprise_id
		self.status = status
		self.currency_id = currency_id
		self.grand_total = grand_total
		self.round_off = round_off
		self.type = type
		self.delivery_due_date = delivery_due_date
		self.document = document
		self.document_description = document_description
		self.issue_id = issue_id
		self.project_code = project_code

	def __repr__(self):
		return '%s' % self.__dict__

	def getOAType(self):
		"""

		:return:
		"""
		self.type = OA.TYPE_CHOICES[self.type]
		return self.type

	def getSimpleCode(self):
		"""

		:return:
		"""
		return self.getInternalCode()

	def getStatusName(self):

		status_name = self.STATUS_NAMES[self.status]
		return status_name

	def getInternalCode(self):
		"""
		Returns a Code used for internal purpose within the organisation/enterprise.
		It will not have the enterprise_id attached.

		:return:
		"""
		return self.generateInternalCode(
			financial_year=self.financial_year, type=self.type, oa_no=self.oa_no, oa_id=self.id,
			sub_number=self.sub_number)

	@staticmethod
	def generateInternalCode(financial_year=None, type=None, oa_no=None, oa_id=None, sub_number=None):
		"""
		Returns a Code used for internal purpose within the organisation/enterprise.
		It will not have the enterprise_id attached.

		:return:
		"""
		if oa_no == 0 or oa_no is None:
			return "PF#%s%s" % (type[0] if type else "", oa_id)
		else:
			return "%s%s" % (
				("%s/%s%05.0f" % (financial_year, type[0], oa_no)).upper(),
				"%s" % sub_number.strip() if sub_number else "")

	@staticmethod
	def generateDraftInternalCode(type=None, oa_no=None, oa_id=None, sub_number=None):
		"""
		Returns a Code used for internal purpose within the organisation/enterprise.
		It will not have the enterprise_id attached.

		:return:
		"""
		return ("PF#%s%s" % (type[0] if type else "", oa_id)) if (
				oa_no == 0 or oa_no is None) else "%s%s" % (
			("%s/%s" % (type[0], oa_id)).upper(), "%s" % sub_number.strip() if sub_number else "")

	def getCode(self):
		"""

		:return:
		"""
		return self.getInternalCode()

	def getDocumentTitle(self):
		return self.TYPE_TITLES[self.type] if self.type in self.TYPE_TITLES else self.TYPE_TITLES["Sale"]

	def getInvoiceValue(self):
		"""
		:return:
		"""
		invoice_value = 0
		tax_value = 0
		for invoice_material in self.invoice_materials:
			if invoice_material.invoice.status > -1 and invoice_material.invoice.type == 'GST':
				invoice_value += invoice_material.quantity * invoice_material.rate
				item = invoice_material
				for item_tax in item.taxes:
					item_value = Decimal("%0.2f" % Decimal((float(item.rate) * float(item.quantity) * float(
						100 - (item.discount)) / 100)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP))
					tax_value += Decimal(
						"%0.2f" % Decimal((item_value * Decimal(item_tax.tax.net_rate / 100)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)))
		return invoice_value + tax_value

	def getMRSCode(self):
		if self.status is 0:
			return "TMP#MRS-" + str(self.id)
		return '%s/%s/%06d' % (self.financial_year, "MRS", int(self.oa_no))

	def getPendingValue(self):
		"""
		:return:
		"""
		return self.grand_total - self.getInvoiceValue()


class OAParticulars(Base, TaxableItem):
	""" Models OA Particulars table , its hold the  OA materials detail """
	__tablename__ = u'oa_particulars'

	oa_id = Column(Integer, ForeignKey('order_acknowledgement.id'), primary_key=True, name='oa_id', autoincrement=False)
	item_id = Column(Integer, ForeignKey('materials.id'), name='item_id', primary_key=True, autoincrement=False)
	quantity = Column(DECIMAL, name='quantity')
	price = Column(DECIMAL, name='price')
	remarks = Column(String(100), name='remarks')
	enterprise_id = Column(
		Integer, ForeignKey('materials.enterprise_id'), name='enterprise_id', primary_key=True, autoincrement=False)
	make_id = Column(Integer, ForeignKey('make.id'), name='make_id', primary_key=True, autoincrement=False)
	hsn_code = Column(String(20), name='hsn_code')

	discount = Column(DECIMAL, name='discount', default=0.00)
	is_faulty = Column(Boolean, name='is_faulty', primary_key=True, autoincrement=False, default=False)
	alternate_unit_id = Column(Integer, ForeignKey('materials_alternate_units.alternate_unit_id'), name='alternate_unit_id', default=None)
	mi_not_applicable = Column(Boolean, name='mi_not_applicable', autoincrement=False, default=False)
	project_id = Column(Integer, name='project_id')
	internal_price = Column(DECIMAL, name='internal_price', default=0.00)
	internal_oa_id = Column(Integer, name='internal_oa_id')

	oa = relationship("OA", backref=backref('oa_oa', order_by=oa_id))
	item = relationship(
		"Material", backref=backref("oa_particulars_material"),
		primaryjoin="and_(OAParticulars.item_id==Material.material_id)")
	make = relationship(
		"Make", backref='oa_material_make',
		primaryjoin="and_(OAParticulars.make_id==Make.id, OAParticulars.enterprise_id==Make.enterprise_id)")
	alternate_unit = relationship(
		"MaterialAlternateUnit", backref='oa_particulars_alternate_unit',
		primaryjoin="""and_(MaterialAlternateUnit.enterprise_id==OAParticulars.enterprise_id, 
				MaterialAlternateUnit.alternate_unit_id==OAParticulars.alternate_unit_id, 
				MaterialAlternateUnit.item_id==OAParticulars.item_id)""")

	def __init__(
			self, oa_id=None, item_id=None, enterprise_id=None, quantity=0, price=0, amount=0, remarks="",
			make_id=None, is_faulty=0, alternate_unit_id=None, hsn_code=None, discount=0):
		self.oa_id = oa_id
		self.enterprise_id = enterprise_id
		self.item_id = item_id
		self.quantity = quantity
		self.price = price
		self.amount = amount
		self.remarks = remarks
		self.make_id = make_id
		self.hsn_code = hsn_code
		self.discount = discount
		self.is_faulty = is_faulty
		self.alternate_unit_id = alternate_unit_id

	def __repr__(self):
		return "%s" % self.__dict__


class OATag(Base):
	""" Tagging model for OA """
	__tablename__ = u'oa_tags'

	oa_id = Column(Integer, ForeignKey(
		'order_acknowledgement.id'), primary_key=True, name='oa_id', autoincrement=False)
	tag_id = Column(Integer, ForeignKey('tags.id'), primary_key=True, name='tag_id', autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), primary_key=True, name='enterprise_id', autoincrement=False)

	tag = relationship("Tag", backref=backref("oa_tag_tag", order_by=(tag_id, enterprise_id)))
	oa = relationship("OA", backref=backref("oa_tags_oa", order_by=oa_id))

	def __init__(self, oa_id=None, tag_id=None, enterprise_id=None):
		self.enterprise_id = enterprise_id
		self.oa_id = oa_id
		self.tag_id = tag_id

	def __repr__(self):
		return "[%s] %s" % (self.oa, self.tag)

	def __eq__(self, other):
		return (self.oa_id == OATag(other).oa_id) and (self.tag_id == OATag(other).tag_id)


class OATax(Base):
	__tablename__ = u'oa_tax'

	oa_id = Column(Integer, ForeignKey(
		'order_acknowledgement.id'), primary_key=True, name='oa_id', autoincrement=False)
	tax_code = Column(String, ForeignKey('tax.code'), name='tax_code', primary_key=True, autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)

	oa = relationship("OA", backref=backref('oa_tax_oa', order_by=oa_id))
	tax = relationship("Tax", backref='oa_tax_tax', primaryjoin=(
		"and_(OATax.tax_code == Tax.code, OATax.enterprise_id == Tax.enterprise_id)"))

	def __init__(self, oa_id=None, tax_code=None, enterprise_id=None):
		self.oa_id = oa_id
		self.tax_code = tax_code
		self.enterprise_id = enterprise_id

	def __repr__(self):
		return "%s" % self.__dict__

	def getTaxLedgerId(self):
		return self.tax.getOutputLedgerId()


class OADocument(Base, AutoSerialize):
	""" Model to collect invoice related documents """
	__tablename__ = u'oa_document'

	oa_id = Column(Integer, ForeignKey(
		'order_acknowledgement.id'), primary_key=True, name='oa_id', autoincrement=False)
	is_rejected = Column(Boolean, name='is_rejected', default=False)
	document_pdf = Column(LargeBinary, name='document_pdf')
	document_txt = Column(LargeBinary, name='document_txt')
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	revised_on = Column(DateTime, primary_key=True, name='revised_on', autoincrement=False)
	revised_by = Column(Integer, ForeignKey('auth_user.id'), name='revised_by')

	reviser = relationship("User", backref=backref('oa_reviser', order_by=revised_by))
	oa = relationship("OA", backref=backref('oa_document_oa', order_by=oa_id))

	def __init__(
			self, oa_id=None, document_pdf=None, document_txt=None, is_rejected=False, enterprise_id=None,
			revised_on=None, revised_by=None):
		self.oa_id = oa_id
		self.document_pdf = document_pdf
		self.document_txt = document_txt
		self.is_rejected = is_rejected
		self.enterprise_id = enterprise_id
		self.revised_on = revised_on
		self.revised_by = revised_by

	def __repr__(self):
		return '%s %s' % (self.oa_id, self.document_pdf)


class BankReconciliation(Base):
	""" Tagging model for BRS """
	__tablename__ = u'bank_reconciliation'

	voucher_id = Column(Integer, ForeignKey('voucher.id'), primary_key=True, name='voucher_id', autoincrement=False)
	ledger_id = Column(Integer, ForeignKey('account_ledgers.id'), primary_key=True, name='ledger_id',
	                   autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), primary_key=True, name='enterprise_id',
	                       autoincrement=False)
	bank_reconciliation_date = Column(DateTime, name='bank_reconciliation_date')

	voucher = relationship("Voucher", backref=backref("br_voucher", order_by=(ledger_id, enterprise_id)))
	ledger = relationship("Ledger", backref=backref("br_ledger", order_by=ledger_id))
	enterprise = relationship("Enterprise", backref=backref('br_enterprise', order_by=enterprise_id))

	def __init__(self, voucher_id=None, ledger_id=None, enterprise_id=None, bank_reconciliation_date=None):
		self.voucher_id = voucher_id
		self.ledger_id = ledger_id
		self.enterprise_id = enterprise_id
		self.bank_reconciliation_date = bank_reconciliation_date

	def __repr__(self):
		return "[%s] %s" % (self.voucher, self.ledger)

	def __eq__(self, other):
		return (self.voucher_id == BankReconciliation(other).voucher_id) and (
				self.ledger_id == BankReconciliation(other).ledger_id)


class ImportDocuments(Base):
	""" Tagging model for Import Documents"""
	__tablename__ = u'import_documents'

	id = Column(Integer, primary_key=True, name='id', autoincrement=True)
	file_name = Column(LargeBinary, name='file_name')
	enterprise_id = Column(
		Integer, ForeignKey('enterprise.id'), primary_key=True, name='enterprise_id', autoincrement=False)
	uploaded_on = Column(DateTime, primary_key=True, name='uploaded_on', autoincrement=False)
	uploaded_by = Column(Integer, ForeignKey('auth_user.id'), name='uploaded_by')
	process = Column(Boolean, name='process', default=False)
	attachment_id = Column(Integer, ForeignKey('attachment.id'), name='attachment_id')
	enterprise = relationship("Enterprise", backref=backref('tally_enterprise', order_by=enterprise_id))

	attachment = relationship(
		"Attachment", backref="tally_document_attachment", order_by="Attachment.attachment_id", single_parent=True,
		primaryjoin="and_(ImportDocuments.attachment_id==Attachment.attachment_id, ImportDocuments.enterprise_id==Attachment.enterprise_id)",
		cascade="save-update, merge, delete, delete-orphan")

	def __repr__(self):
		return "[%s] %s" % (self.file_name, self.id)

	def __init__(self, file_name=None, enterprise_id=None, uploaded_on=None, uploaded_by=None, attachment_id=None):
		self.file_name = file_name
		self.enterprise_id = enterprise_id
		self.uploaded_on = uploaded_on
		self.uploaded_by = uploaded_by
		self.attachment_id = attachment_id


class InvoiceTemplateConfig(Base, AutoSerialize):
	""" Class that models template configuration table. It holds the template details for the enterprise. """
	__tablename__ = u'invoice_template_config'

	id = Column(Integer, name='id', primary_key=True, autoincrement=True)
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	module = Column(String, name='module')
	template_id = Column(Integer, ForeignKey('invoice_template.template_id'), name='template_id', autoincrement=False)
	last_modified_by = Column(Integer, ForeignKey('auth_user.id'), name='last_modified_by', autoincrement=False)
	last_modified_on = Column(DateTime, name='last_modified_on')

	template_details = relationship(
		"InvoiceTemplate", backref=backref("enterprise_configuration", order_by=template_id), uselist=False)
	template_general_details = relationship(
		"InvoiceTemplateGeneralConfig", backref=backref("general_details", order_by=id), uselist=False)
	template_header_details = relationship(
		"InvoiceTemplateHeaderConfig", backref=backref("header_details", order_by=id), uselist=False)
	template_item_details = relationship(
		"InvoiceTemplateItemsConfig", backref=backref("item_details", order_by=id), uselist=False)
	template_summary_details = relationship(
		"InvoiceTemplateSummaryConfig", backref=backref("summary_details", order_by=id), uselist=False)
	template_misc_details = relationship(
		"InvoiceTemplateMiscConfig", backref=backref("misc_details", order_by=id), uselist=False)
	template_banner_details = relationship(
		"InvoiceTemplateBannerImage", backref=backref("banner_details", order_by=id), lazy='dynamic',
		cascade="save-update, merge, delete, delete-orphan")

	modifier = relationship(
		"User", backref='invoice_template_config_modifier',
		primaryjoin='and_(InvoiceTemplateConfig.last_modified_by == User.id)')

	def __init__(
			self, id=None, enterprise_id=None, module=None, template_id=None, last_modified_by=None, last_modified_on=None):
		self.id = id
		self.enterprise_id = enterprise_id
		self.module = module
		self.template_id = template_id
		self.last_modified_by = last_modified_by
		self.last_modified_on = last_modified_on


class InvoiceTemplate(Base, AutoSerialize):
	""" Class that models template master table. It holds the frame details. """
	__tablename__ = u'invoice_template'

	template_id = Column(Integer, name='template_id', primary_key=True, autoincrement=True)
	template_name = Column(String, name='template_name', autoincrement=False)
	main_frame = Column(JSON, name='main_frame')
	form_no_frame = Column(JSON, name='form_no_frame')
	module = Column(String, name='module')

	def __init__(
			self, template_id=None, template_name=None, main_frame=None, form_no_frame=None, module=None):
		self.template_id = template_id
		self.template_name = template_name
		self.main_frame = main_frame
		self.form_no_frame = form_no_frame
		self.module = module


class InvoiceTemplateGeneralConfig(Base, AutoSerialize):
	""" Class that models template general table. It holds the general configuration details for the template page. """
	__tablename__ = u'invoice_template_generalconfig'

	config_id = Column(Integer, ForeignKey(
		'invoice_template_config.id'), primary_key=True, name='config_id', unique=True, autoincrement=False)
	page_size = Column(String, name='page_size')
	alignment = Column(String, name='alignment')
	base_font = Column(String, name='base_font')
	margin_top = Column(DECIMAL, name='margin_top')
	margin_bottom = Column(DECIMAL, name='margin_bottom')
	margin_right = Column(DECIMAL, name='margin_right')
	margin_left = Column(DECIMAL, name='margin_left')
	inv_doc_datetime_format = Column(String, name='inv_doc_datetime_format')

	def __init__(
			self, config_id=None, page_size=None, alignment=None, base_font=None,
			margin_top=None, margin_bottom=None, margin_right=None, margin_left=None, inv_doc_datetime_format=None):
		self.config_id = config_id
		self.page_size = page_size
		self.alignment = alignment
		self.base_font = base_font
		self.margin_top = margin_top
		self.margin_bottom = margin_bottom
		self.margin_right = margin_right
		self.margin_left = margin_left
		self.inv_doc_datetime_format = inv_doc_datetime_format


class InvoiceTemplateHeaderConfig(Base, AutoSerialize):
	""" Class that models template header table. It holds the configuration details for the template header page. """
	__tablename__ = u'invoice_template_headerconfig'

	config_id = Column(Integer, ForeignKey(
		'invoice_template_config.id'), primary_key=True, name='config_id', unique=True, autoincrement=False)
	include_logo = Column(Boolean, name='include_logo', default=True)
	logo_size = Column(Integer, name='logo_size', autoincrement=False)
	include_name = Column(Boolean, name='include_name')
	name_font = Column(String, name='name_font')
	name_font_size = Column(Integer, name='name_font_size', autoincrement=False)
	font_size = Column(Integer, name='font_size', autoincrement=False)
	form_name_font_size = Column(Integer, name='form_name_font_size', autoincrement=False)
	include_address = Column(Boolean, name='include_address')
	include_email = Column(Boolean, name='include_email')
	include_phone_no = Column(Boolean, name='include_phone_no')
	include_fax = Column(Boolean, name='include_fax')
	include_cin = Column(Boolean, name='include_cin')
	include_gstin = Column(Boolean, name='include_gstin')
	include_pan = Column(Boolean, name='include_pan')
	include_tin = Column(Boolean, name='include_tin')
	include_ecc_no = Column(Boolean, name='include_ecc_no')
	include_cst_no = Column(Boolean, name='include_cst_no')
	include_tan = Column(Boolean, name='include_tan')
	include_partycode = Column(Boolean, name='include_partycode')
	include_partyname = Column(Boolean, name='include_partyname')
	include_billingaddress = Column(Boolean, name='include_billingaddress')
	billingaddress_label = Column(String, name='billingaddress_label')
	include_shippingaddress = Column(Boolean, name='include_shippingaddress')
	shippingaddress_label = Column(String, name='shippingaddress_label')
	include_party_email = Column(Boolean, name='include_party_email')
	include_party_phoneno = Column(Boolean, name='include_party_phoneno')
	gst_label = Column(String, name='gst_label')
	service_label = Column(String, name='service_label')
	trading_label = Column(String, name='trading_label')
	billofsupply_label = Column(String, name='billofsupply_label')
	others_label = Column(String, name='others_label')
	invoiceno_label = Column(String, name='invoiceno_label')
	invoicedate_label = Column(String, name='invoicedate_label')
	include_paymentterms = Column(Boolean, name='include_paymentterms')
	paymentterms_label = Column(String, name='paymentterms_label')
	include_pono = Column(Boolean, name='include_pono')
	pono_label = Column(String, name='pono_label')
	include_podate = Column(Boolean, name='include_podate')
	podate_label = Column(String, name='podate_label')
	include_transportmode = Column(Boolean, name='include_transportmode')
	transportmode_label = Column(String, name='transportmode_label')
	include_splinstructions = Column(Boolean, name='include_splinstructions')
	splinstructions_label = Column(String, name='splinstructions_label')
	include_roadpermitno = Column(Boolean, name='include_roadpermitno')
	roadpermitno_label = Column(String, name='roadpermitno_label')
	include_lrnodate = Column(Boolean, name='include_lrnodate')
	lrnodate_label = Column(String, name='lrnodate_label')
	include_packingslipno = Column(Boolean, name='include_packingslipno')
	packingslipno_label = Column(String, name='packingslipno_label')
	include_packingdescription = Column(Boolean, name='include_packingdescription')
	packingdescription_label = Column(String, name='packingdescription_label')
	include_authorizedsign = Column(Boolean, name='include_authorizedsign')
	authorizedsign_label = Column(String, name='authorizedsign_label')
	include_approver_signature = Column(Boolean, name='include_approver_signature')
	dc_label = Column(String, name='dc_label')
	dcno_label = Column(String, name='dcno_label')
	included_reg_items = Column(String, name='included_reg_items')
	inv_number_format = Column(String, name='inv_number_format')
	include_estimate_nodate = Column(Boolean, name='include_estimate_nodate')
	estimate_nodate_label = Column(String, name='estimate_nodate_label')
	invoice_number_font_size = Column(Integer, name='invoice_no_font_size', autoincrement=False)
	scan_code_option = Column(Integer, name='scan_code_option')
	include_preparedsign = Column(Boolean, name='include_preparedsign')
	preparedsign_label = Column(String, name='preparedsign_label')

	def __init__(
			self, config_id=None, include_logo=True, logo_size=None, include_name=True, name_font=None,
			name_font_size=None,
			font_size=None, include_address=True, include_email=True, include_phone_no=True, include_fax=True,
			include_cin=True,
			include_gstin=True, include_pan=True, include_tin=True, include_ecc_no=True, include_cst_no=True,
			include_tan=True,
			include_partyname=True, include_billingaddress=True, billingaddress_label=True,
			include_shippingaddress=True, shippingaddress_label=None, include_party_email=True,
			include_party_phoneno=True,
			gst_label=None, service_label=None, trading_label=None, billofsupply_label=None, others_label=None,
			invoiceno_label=None, invoicedate_label=None, include_paymentterms=True, paymentterms_label=None,
			include_pono=True, pono_label=None, include_podate=True, podate_label=None, include_transportmode=None,
			transportmode_label=None, include_splinstructions=True,
			splinstructions_label=None, include_roadpermitno=None, roadpermitno_label=None, include_lrnodate=True,
			lrnodate_label=None,
			include_packingslipno=True, packingslipno_label=None, include_packingdescription=True,
			packingdescription_label=None,
			include_authorizedsign=True, authorizedsign_label=None, include_approver_signature=True, include_partycode=None, form_name_font_size=None,
			dc_label=None, dcno_label=None, included_reg_items=None, inv_number_format=None, include_estimate_nodate=None,
			estimate_nodate_label=None, invoice_no_font_size=None, scan_code_option=None, include_preparedsign=True,
			preparedsign_label=None):

		self.config_id = config_id
		self.include_logo = include_logo
		self.logo_size = logo_size
		self.include_name = include_name
		self.name_font = name_font
		self.name_font_size = name_font_size
		self.font_size = font_size
		self.form_name_font_size = form_name_font_size
		self.include_address = include_address
		self.include_email = include_email
		self.include_phone_no = include_phone_no
		self.include_fax = include_fax
		self.include_cin = include_cin
		self.include_gstin = include_gstin
		self.include_pan = include_pan
		self.include_tin = include_tin
		self.include_ecc_no = include_ecc_no
		self.include_cst_no = include_cst_no
		self.include_tan = include_tan
		self.include_partyname = include_partyname
		self.include_billingaddress = include_billingaddress
		self.billingaddress_label = billingaddress_label
		self.include_shippingaddress = include_shippingaddress
		self.shippingaddress_label = shippingaddress_label
		self.include_party_email = include_party_email
		self.include_party_phoneno = include_party_phoneno
		self.gst_label = gst_label
		self.service_label = service_label
		self.trading_label = trading_label
		self.billofsupply_label = billofsupply_label
		self.others_label = others_label
		self.invoiceno_label = invoiceno_label
		self.invoicedate_label = invoicedate_label
		self.include_paymentterms = include_paymentterms
		self.paymentterms_label = paymentterms_label
		self.include_pono = include_pono
		self.pono_label = pono_label
		self.include_podate = include_podate
		self.podate_label = podate_label
		self.include_transportmode = include_transportmode
		self.transportmode_label = transportmode_label
		self.include_splinstructions = include_splinstructions
		self.splinstructions_label = splinstructions_label
		self.include_roadpermitno = include_roadpermitno
		self.roadpermitno_label = roadpermitno_label
		self.include_lrnodate = include_lrnodate
		self.lrnodate_label = lrnodate_label
		self.include_packingslipno = include_packingslipno
		self.packingslipno_label = packingslipno_label
		self.include_packingdescription = include_packingdescription
		self.packingdescription_label = packingdescription_label
		self.include_authorizedsign = include_authorizedsign
		self.authorizedsign_label = authorizedsign_label
		self.include_approver_signature = include_approver_signature
		self.include_partycode = include_partycode
		self.dc_label = dc_label
		self.dcno_label = dcno_label
		self.included_reg_items = included_reg_items
		self.inv_number_format = inv_number_format
		self.include_estimate_nodate = include_estimate_nodate
		self.estimate_nodate_label = estimate_nodate_label
		self.invoice_no_font_size = invoice_no_font_size
		self.scan_code_option = scan_code_option
		self.include_preparedsign = include_preparedsign
		self.preparedsign_label = preparedsign_label


class InvoiceTemplateItemsConfig(Base, AutoSerialize):
	""" Class that models template items table. It holds the configuration details for the material table. """
	__tablename__ = u'invoice_template_itemsconfig'

	config_id = Column(Integer, ForeignKey('invoice_template_config.id'), name='config_id', primary_key=True,
	                   autoincrement=False)
	font_size = Column(Integer, name='font_size')
	include_sno = Column(Boolean, name='include_sno')
	sno_label = Column(String, name='sno_label')
	sno_width = Column(Integer, name='sno_width')
	itemdetails_label = Column(String, name='itemdetails_label')
	itemdetails_width = Column(Integer, name='itemdetails_width')
	include_itemcode = Column(Boolean, name='include_itemcode')
	itemcode_label = Column(String, name='itemcode_label')
	name_label = Column(String, name='name_label')
	include_make = Column(Boolean, name='include_make')
	make_label = Column(String, name='make_label')
	include_partno = Column(Boolean, name='include_partno')
	partno_label = Column(String, name='partno_label')
	include_oano = Column(Boolean, name='include_oano')
	oano_label = Column(String, name='oano_label')
	include_dc_no = Column(Boolean, name='include_dc_no')
	dc_no_label = Column(String, name='dc_no_label')
	include_dc_qty = Column(Boolean, name='include_dc_qty')
	include_dc_date = Column(Boolean, name='include_dc_date')
	include_description = Column(Boolean, name='include_description')
	description_label = Column(String, name='description_label')
	include_remarks = Column(Boolean, name='include_remarks')
	remarks_label = Column(String, name='remarks_label')
	include_hsnsac = Column(Boolean, name='include_hsnsac')
	hsnsac_label = Column(String, name='hsnsac_label')
	hsnsac_width = Column(Integer, name='hsnsac_width')
	hsnsac_part_of_itemdetails = Column(Boolean, name='hsnsac_part_of_itemdetails')
	include_quantity = Column(Boolean, name='include_quantity')
	quantity_label = Column(String, name='quantity_label')
	quantity_width = Column(Integer, name='quantity_width')
	include_units = Column(Boolean, name='include_units')
	units_label = Column(String, name='units_label')
	units_width = Column(Integer, name='units_width')
	units_in_quantity_column = Column(Boolean, name='units_in_quantity_column')
	include_primary_qty = Column(Boolean, name='include_primary_qty')
	include_unit_price = Column(Boolean, name='include_unit_price')
	unit_price_label = Column(String, name='unit_price_label')
	unit_price_width = Column(Integer, name='unit_price_width')
	include_discount = Column(Boolean, name='include_discount')
	discount_label = Column(String, name='discount_label')
	discount_width = Column(Integer, name='discount_width')
	include_taxable_amount = Column(Boolean, name='include_taxable_amount')
	taxable_amount_label = Column(String, name='taxable_amount_label')
	taxable_amount_width = Column(Integer, name='taxable_amount_width')
	include_tax = Column(Boolean, name='include_tax')
	tax_type = Column(Integer, name='tax_type')
	include_taxrate = Column(Boolean, name='include_taxrate')
	taxrate_width = Column(Integer, name='taxrate_width')
	include_taxamount = Column(Boolean, name='include_taxamount')
	taxamount_width = Column(Integer, name='taxamount_width')
	show_tax_for_dc = Column(Boolean, name='show_tax_for_dc')
	include_row_separator = Column(Boolean, name='include_row_separator')
	include_column_separator = Column(Boolean, name='include_column_separator')
	include_alternate_row_shading = Column(Boolean, name='include_alternate_row_shading')
	item_sort_order = Column(Integer, name='item_sort_order')
	tax_payable_on_reverse_charge = Column(Integer, name='tax_payable_on_reverse_charge')
	item_table_space = Column(Integer, name='item_table_space')
	data_separator = Column(String, name='data_separator')

	def __init__(
			self, config_id=None, font_size=None, include_sno=None, sno_label=None, sno_width=None,
			itemdetails_label=None,
			itemdetails_width=None, include_itemcode=None, itemcode_label=None, name_label=None,
			include_make=None, make_label=None, include_partno=None, partno_label=None, include_oano=None,
			oano_label=None,
			include_description=None, description_label=None, include_remarks=None,
			remarks_label=None, include_hsnsac=None, hsnsac_label=None, hsnsac_width=None,
			hsnsac_part_of_itemdetails=None, include_quantity=None, quantity_label=None, quantity_width=None,
			include_units=None, units_label=None, units_width=None, units_in_quantity_column=None,
			include_unit_price=None, include_primary_qty=None,
			unit_price_label=None, unit_price_width=None, include_discount=None, discount_label=None,
			discount_width=None,
			include_taxable_amount=None, taxable_amount_label=None, taxable_amount_width=None, include_tax=None,
			tax_type=None, include_taxrate=None, taxrate_width=None, include_taxamount=None, taxamount_width=None,
			include_row_separator=None, include_column_separator=None, include_alternate_row_shading=None,
			show_tax_for_dc=None, item_sort_order=None, include_dc_no=None, dc_no_label=None, include_dc_qty=None,
			include_dc_date=None, tax_payable_on_reverse_charge=None, item_table_space=None, data_separator=None):
		self.config_id = config_id
		self.font_size = font_size
		self.include_sno = include_sno
		self.sno_label = sno_label
		self.sno_width = sno_width
		self.itemdetails_label = itemdetails_label
		self.itemdetails_width = itemdetails_width
		self.include_itemcode = include_itemcode
		self.itemcode_label = itemcode_label
		self.name_label = name_label
		self.include_make = include_make
		self.make_label = make_label
		self.include_partno = include_partno
		self.partno_label = partno_label
		self.include_oano = include_oano
		self.oano_label = oano_label
		self.include_description = include_description
		self.description_label = description_label
		self.include_remarks = include_remarks
		self.remarks_label = remarks_label
		self.include_hsnsac = include_hsnsac
		self.hsnsac_label = hsnsac_label
		self.hsnsac_width = hsnsac_width
		self.hsnsac_part_of_itemdetails = hsnsac_part_of_itemdetails
		self.include_quantity = include_quantity
		self.quantity_label = quantity_label
		self.quantity_width = quantity_width
		self.include_units = include_units
		self.units_label = units_label
		self.units_width = units_width
		self.units_in_quantity_column = units_in_quantity_column
		self.include_primary_qty = include_primary_qty
		self.include_unit_price = include_unit_price
		self.unit_price_label = unit_price_label
		self.unit_price_width = unit_price_width
		self.include_discount = include_discount
		self.discount_label = discount_label
		self.discount_width = discount_width
		self.include_taxable_amount = include_taxable_amount
		self.taxable_amount_label = taxable_amount_label
		self.taxable_amount_width = taxable_amount_width
		self.include_tax = include_tax
		self.tax_type = tax_type
		self.include_taxrate = include_taxrate
		self.taxrate_width = taxrate_width
		self.include_taxamount = include_taxamount
		self.taxamount_width = taxamount_width
		self.show_tax_for_dc = show_tax_for_dc
		self.include_row_separator = include_row_separator
		self.include_column_separator = include_column_separator
		self.include_alternate_row_shading = include_alternate_row_shading
		self.item_sort_order = item_sort_order
		self.include_dc_no = include_dc_no
		self.dc_no_label = dc_no_label
		self.include_dc_qty = include_dc_qty
		self.include_dc_date = include_dc_date
		self.tax_payable_on_reverse_charge = tax_payable_on_reverse_charge
		self.item_table_space = item_table_space
		self.data_separator = data_separator


class InvoiceTemplateSummaryConfig(Base, AutoSerialize):
	""" Class that models template summary table. It holds the configuration details for the summary table. """
	__tablename__ = u'invoice_template_summaryconfig'

	config_id = Column(Integer, ForeignKey('invoice_template_config.id'), name='config_id', primary_key=True,
	                   autoincrement=False)
	font_size = Column(Integer, name='font_size', autoincrement=False)
	include_total = Column(Boolean, name='include_total')
	include_subtotal = Column(Boolean, name='include_subtotal')
	include_qty_total = Column(Boolean, name='include_qty_total')
	include_total_in_words = Column(Boolean, name='include_total_in_words')
	hsn_tax_font_size = Column(Integer, name='hsn_tax_font_size', autoincrement=False)
	include_hsn_summary = Column(Boolean, name='include_hsn_summary')

	def __init__(
			self, config_id=None, font_size=None, include_total=None, include_subtotal=None, include_qty_total=None,
			include_total_in_words=None, include_hsn_summary=None, hsn_tax_font_size=None):
		self.config_id = config_id
		self.font_size = font_size
		self.hsn_tax_font_size = hsn_tax_font_size
		self.include_total = include_total
		self.include_subtotal = include_subtotal
		self.include_qty_total = include_qty_total
		self.include_total_in_words = include_total_in_words
		self.include_hsn_summary = include_hsn_summary


class InvoiceTemplateMiscConfig(Base, AutoSerialize):
	""" Class that models template misc table. It holds the configuration details for the notes section """
	__tablename__ = u'invoice_template_miscconfig'

	config_id = Column(Integer, ForeignKey('invoice_template_config.id'), name='config_id', primary_key=True,
	                   autoincrement=False)
	font_size = Column(Integer, name='font_size', autoincrement=False)
	include_page_no_in_footer = Column(Boolean, name='include_page_no_in_footer')
	footer_page_no_alignment = Column(Integer, name='footer_page_no_alignment')
	notes = Column(String, name='notes')
	include_first_page_summary = Column(Boolean, name='include_first_page_summary')
	foot_note = Column(String, name='foot_note')

	def __init__(
			self, config_id=None, font_size=None, include_page_no_in_footer=True, footer_page_no_alignment=None,
			notes=None, include_first_page_summary=None, foot_note=None):
		self.config_id = config_id
		self.font_size = font_size
		self.include_page_no_in_footer = include_page_no_in_footer
		self.footer_page_no_alignment = footer_page_no_alignment
		self.notes = notes
		self.include_first_page_summary = include_first_page_summary
		self.foot_note = foot_note


class InvoiceTemplateBannerImage(Base):
	""" Class that models template banner image table. It holds the configuration details for the banner image """
	__tablename__ = u'invoice_template_banner_images'

	config_id = Column(Integer, ForeignKey('invoice_template_config.id'), name='config_id', primary_key=True,
	                   autoincrement=False)
	section = Column(String, name='section', primary_key=True)
	position = Column(String, name='position', primary_key=True)
	size = Column(Integer, name='size', autoincrement=False)
	attachment_id = Column(Integer, ForeignKey('attachment.id'), name='attachment_id')
	uploaded_on = Column(DateTime, name='uploaded_on')
	uploaded_by = Column(Integer, name='uploaded_by')
	last_modified_on = Column(DateTime, name='last_modified_on')
	last_modified_by = Column(Integer, name='last_modified_by')

	attachment = relationship(
		"Attachment", backref="invoice_template_banner", order_by="Attachment.attachment_id", single_parent=True,
		primaryjoin="and_(InvoiceTemplateBannerImage.attachment_id==Attachment.attachment_id)",
		cascade="save-update, merge, delete, delete-orphan")

	def __init__(
			self, config_id=None, section=None, position=None, size=None, attachment_id=None, uploaded_on=None,
			uploaded_by=None,
			last_modified_by=None):
		self.config_id = config_id
		self.section = section
		self.position = position
		self.size = size
		self.attachment_id = attachment_id
		self.uploaded_on = uploaded_on if uploaded_on else time.strftime('%Y-%m-%d %H:%M:%S')
		self.uploaded_by = uploaded_by
		self.last_modified_on = time.strftime('%Y-%m-%d %H:%M:%S')
		self.last_modified_by = last_modified_by

	def __repr__(self):
		return "%s" % self.__dict__


class EraseLog(Base):
	""" Class that models erase log table. It holds the details of user who erase transaction/reset enterprise data """
	__tablename__ = u'erase_log'

	id = Column(Integer, name='id', primary_key=True, autoincrement=True)
	enterprise_id = Column(Integer, name='enterprise_id', autoincrement=False)
	erase_type = Column(String, name='erase_type')
	erased_by = Column(Integer, ForeignKey('auth_user.id'), name='erased_by', autoincrement=False)
	erased_on = Column(DateTime, name='erased_on')

	modifier = relationship(
		"User", backref='erase_log_modifier',
		primaryjoin='foreign(User.id) == EraseLog.erased_by')

	def __init__(self, enterprise_id=None, erase_type=None, erased_by=None, erased_on=None):
		self.enterprise_id = enterprise_id
		self.erase_type = erase_type
		self.erased_by = erased_by
		self.erased_on = erased_on if erased_on else time.strftime('%Y-%m-%d %H:%M:%S')

	def __repr__(self):
		return "%s" % self.__dict__


class EnterpriseContactMap(Base):
	"""
	Entity to hold Enterprise's Contact Map
	"""
	__tablename__ = u'enterprise_contact_map'

	contact_id = Column(Integer, ForeignKey('contact_details.id'), name='contact_id', primary_key=True, autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), primary_key=True, name='enterprise_id', autoincrement=False)
	sequence_id = Column(Integer, name='sequence_id', autoincrement=False)

	contact = relationship(
		"ContactDetails", backref='enterprise_contact_details',
		primaryjoin="(ContactDetails.id==EnterpriseContactMap.contact_id)")

	def __init__(
			self, contact_id=None, enterprise_id=None, sequence_id=None):
		self.contact_id = contact_id
		self.enterprise_id = enterprise_id
		self.sequence_id = sequence_id


class SalesEstimate(Base, AutoSerialize, Taxable, Remarkable):
	""" Class that models sales_estimate table. Its hold the sales details of the . """
	__tablename__ = u'sales_estimate'

	TYPE_CHOICES = (
		('Sale', 'Sale'),
		('Labour', 'Labour/Service'),
		('Job', 'Job Work'))

	TYPE_TITLES = {
		"Sale": "Sale",
		"Labour": "Labour/Service",
		"Job": "Job Work"
	}

	STATUS_NAMES = {
		0: "draft",
		1: "reviewed",
		2: "approved",
		3: "client approved",
		4: "client rejected",
		-1: "cancelled",
		-2: "rejected"
	}

	STATUS_DRAFT = 0
	STATUS_REVIEWED = 1
	STATUS_APPROVED = 2
	STATUS_CLIENT_APPROVED = 3
	STATUS_CLIENT_Rejected = 4
	STATUS_CANCELLED = -1
	STATUS_REJECTED = -2

	id = Column(Integer, primary_key=True, name='id', autoincrement=True)
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name='enterprise_id')
	se_no = Column(Integer, name='se_no', default=0)
	sub_number = Column(String(1), name='sub_number')
	financial_year = Column(String(5), name='financial_year')
	party_id = Column(Integer, ForeignKey('party_master.party_id'), name='party_id')
	project_code = Column(Integer, ForeignKey('projects.id'))
	status = Column(Integer, name='status', default=STATUS_DRAFT)
	type = Column(String(30), name='type')
	currency_id = Column(Integer, ForeignKey('currency.id'), name='currency_id')
	currency_conversion_rate = Column(DECIMAL(precision=3), name="currency_conversion_rate", default=1.00)
	grand_total = Column(DECIMAL(precision=2), name='grand_total')
	payment_terms = Column(String(150), name='payment_terms')
	remarks = Column(JSON, name='remarks')
	special_instructions = Column(String(400), name='special_instructions')
	round_off = Column(DECIMAL, name='round_off')
	notes = Column(String, name='notes')
	ref_no = Column(String, name='ref_no')
	ref_date = Column(Date, name='ref_date')
	purpose = Column(String(40), name='purpose')
	expiry_date = Column(Date, name='expiry_date')
	prepared_by = Column(Integer, ForeignKey('auth_user.id'), name='prepared_by')
	prepared_on = Column(DateTime, name='prepared_on')
	last_modified_by = Column(Integer, ForeignKey('auth_user.id'), name='last_modified_by')
	last_modified_on = Column(DateTime, name='last_modified_on')
	approved_by = Column(Integer, ForeignKey('auth_user.id'), name='approved_by')
	approved_on = Column(DateTime, name='approved_on')
	super_modified_on = Column(DateTime, name='super_modified_on')
	super_modified_by = Column(Integer, ForeignKey('auth_user.id'), name='super_modified_by')
	client_revised_on = Column(DateTime, name='client_revised_on')

	items = relationship(
		"SEParticulars", primaryjoin="(SalesEstimate.id==SEParticulars.se_id)", backref="se_particulars",
		cascade="save-update, merge, delete, delete-orphan")
	enterprise = relationship("Enterprise", backref=backref("se_enterprise", order_by=enterprise_id))
	customer = relationship("Party", backref=backref("se_party_master", order_by=party_id))
	tags = relationship("SETag", backref=backref("se_tags", order_by=id), cascade="save-update, merge, delete")
	taxes = relationship(
		"SETax", primaryjoin="(SalesEstimate.id==SETax.se_id)", backref="se_tax",
		cascade="save-update, merge, delete, delete-orphan")
	approver = relationship("User", backref='se_approver', foreign_keys=[approved_by])
	currency = relationship("Currency", backref=backref("se_currency", order_by=currency_id))
	party = relationship(
		"Party", backref='se_party',
		primaryjoin='and_(SalesEstimate.party_id == Party.id, SalesEstimate.enterprise_id == Party.enterprise_id)')
	projects = relationship(
		"Project", backref='se_project',
		primaryjoin='and_(SalesEstimate.project_code == Project.id, SalesEstimate.enterprise_id == Project.enterprise_id)')
	documents = relationship(
		"SEDocument", backref="se_documents",
		primaryjoin="and_(SEDocument.se_id==SalesEstimate.id, SEDocument.revised_on==SalesEstimate.last_modified_on)")
	revisions = relationship(
		"SEDocument", backref="se_revisions", order_by="SEDocument.revised_on",
		primaryjoin="and_(SEDocument.se_id==SalesEstimate.id, SEDocument.revised_on!=SalesEstimate.last_modified_on)")
	modifier = relationship("User", backref='se_modifier', foreign_keys=[last_modified_by])
	# modifier = relationship(
	# 	"User", backref='se_modifier',
	# 	primaryjoin='foreign(User.id) == SalesEstimate.last_modified_by')
	super_modified_user = relationship(
		"User", backref='se_super_modified_user',
		primaryjoin='foreign(User.id) == SalesEstimate.super_modified_by')
	se_contacts = relationship(
		"SEContactMap", backref='se_contact_details', primaryjoin="(SalesEstimate.id==SEContactMap.se_id)",
		single_parent=True, cascade="save-update, merge, delete, delete-orphan")
	se_attachments = relationship(
		"SEAttachments", backref="se_attach", order_by="SEAttachments.attachment_id",
		primaryjoin="(SalesEstimate.id==SEAttachments.se_id)", uselist=False)

	def __init__(
			self, id=None, enterprise_id=None, se_no=0, sub_number=None, financial_year=None, party_id=None,
			project_code=None, status=STATUS_DRAFT, type=None, currency_id=356, currency_conversion_rate=1,
			payment_terms=None, remarks="", special_instructions=None, round_off=0.00, notes=None, ref_no=None, ref_date=None,
			purpose='', sales_person=None, expiry_date=None, prepared_by=None, prepared_on=None, last_modified_by=None,
			last_modified_on=None, approved_by=None, approved_on=None, super_modified_on=None, super_modified_by=None,
			client_revised_on=None, grand_total=0.00, *args, **kwargs):
		super(SalesEstimate, self).__init__(*args, **kwargs)
		self.id = id
		self.enterprise_id = enterprise_id
		self.se_no = se_no
		self.sub_number = sub_number
		self.financial_year = financial_year if financial_year else getFinancialYear()
		self.grand_total = grand_total
		self.party_id = party_id
		self.project_code = project_code
		self.status = status
		self.type = type
		self.currency_id = currency_id
		self.currency_conversion_rate = currency_conversion_rate
		self.payment_terms = payment_terms
		self.remarks = remarks
		self.special_instructions = special_instructions
		self.round_off = round_off
		self.notes = notes
		self.ref_no = ref_no
		self.ref_date = ref_date if ref_date else time.strftime('%Y-%m-%d %H:%M:%S')
		self.purpose = purpose
		self.sales_person = sales_person
		self.expiry_date = expiry_date if expiry_date else time.strftime('%Y-%m-%d %H:%M:%S')
		self.prepared_by = prepared_by
		self.prepared_on = prepared_on if prepared_on else time.strftime('%Y-%m-%d %H:%M:%S')
		self.last_modified_by = last_modified_by
		self.last_modified_on = last_modified_on
		self.approved_by = approved_by
		self.approved_on = approved_on
		self.super_modified_on = super_modified_on
		self.super_modified_by = super_modified_by
		self.client_revised_on = client_revised_on

	def __repr__(self):
		return '%s' % self.__dict__

	def getSEType(self):
		"""

		:return:
		"""
		self.type = SalesEstimate.TYPE_CHOICES[self.type]
		return self.type

	def getSimpleCode(self):
		"""

		:return:
		"""
		return self.getInternalCode()

	def getStatusName(self):
		"""
		:return:
		"""
		status_name = SalesEstimate.STATUS_NAMES[self.status]
		return status_name

	def getInternalCode(self):
		"""
		Returns a Code used for internal purpose within the organisation/enterprise.
		It will not have the enterprise_id attached.

		:return:
		"""
		return self.generateInternalCode(
			financial_year=self.financial_year, type=self.type, se_no=self.se_no, se_id=self.id,
			sub_number=self.sub_number)

	@staticmethod
	def generateInternalCode(financial_year=None, type=None, se_no=None, se_id=None, sub_number=None):
		"""
		Returns a Code used for internal purpose within the organisation/enterprise.
		It will not have the enterprise_id attached.

		:return:
		"""
		if se_no == 0 or se_no is None:
			return "PF#%s%s" % (type[0] if type else "", se_id)
		else:
			return "%s%s" % (
				("%s/%s/%s%05.0f" % (financial_year, "SE", type[0], se_no)).upper(),
				"%s" % sub_number.strip() if sub_number else "")

	@staticmethod
	def generateDraftInternalCode(type=None, se_no=None, se_id=None, sub_number=None):
		"""
		Returns a Code used for internal purpose within the organisation/enterprise.
		It will not have the enterprise_id attached.

		:return:
		"""
		return ("PF#%s%s" % (type[0] if type else "", se_id)) if (
			se_no == 0 or se_no is None) else "%s%s" % (
			("%s/%s" % (type[0], se_id)).upper(), "%s" % sub_number.strip() if sub_number else "")

	def getCode(self):
		"""

		:return:
		"""
		return self.getInternalCode()

	def getDocumentTitle(self):
		return self.TYPE_TITLES[self.type] if self.type in self.TYPE_TITLES else self.TYPE_TITLES["Sale"]


class SEParticulars(Base, TaxableItem):
	""" Models SE Particulars table , its hold the  SE materials detail """
	__tablename__ = u'se_particulars'

	se_id = Column(Integer, ForeignKey('sales_estimate.id'), primary_key=True, name='se_id', autoincrement=False)
	enterprise_id = Column(
		Integer, ForeignKey('sales_estimate.enterprise_id'), name='enterprise_id', primary_key=True, autoincrement=False)
	item_id = Column(Integer, ForeignKey('materials.id'), name='item_id', primary_key=True, autoincrement=False)
	make_id = Column(Integer, ForeignKey('make.id'), name='make_id', primary_key=True, autoincrement=False)
	is_faulty = Column(Boolean, name='is_faulty', primary_key=True, autoincrement=False, default=False)
	quantity = Column(DECIMAL, name='qty')
	hsn_code = Column(String(20), name='hsn_code')
	unit_rate = Column(DECIMAL, name='unit_rate')
	discount = Column(DECIMAL, name='discount')
	alternate_unit_id = Column(
		Integer, ForeignKey('materials_alternate_units.alternate_unit_id'), name='alternate_unit_id', default=None)

	item = relationship(
		"Material", backref=backref("se_particulars_material"),
		primaryjoin="and_(SEParticulars.item_id==Material.material_id, SEParticulars.enterprise_id==Material.enterprise_id)")
	make = relationship(
		"Make", backref='se_material_make',
		primaryjoin="and_(SEParticulars.make_id==Make.id, SEParticulars.enterprise_id==Make.enterprise_id)")

	alternate_unit = relationship(
		"MaterialAlternateUnit", backref='se_material_alternate_unit',
		primaryjoin="""and_(MaterialAlternateUnit.enterprise_id==SEParticulars.enterprise_id, 
				MaterialAlternateUnit.alternate_unit_id==SEParticulars.alternate_unit_id, 
				MaterialAlternateUnit.item_id==SEParticulars.item_id)""")

	def __init__(
			self, se_id=None, enterprise_id=None, item_id=None, make_id=None, is_faulty=0, qty=0, hsn_code='',
			unit_rate=0.000, discount=0.00):
		self.se_id = se_id
		self.enterprise_id = enterprise_id
		self.item_id = item_id
		self.make_id = make_id
		self.is_faulty = is_faulty
		self.qty = qty
		self.hsn_code = hsn_code
		self.unit_rate = unit_rate
		self.discount = discount

	def __repr__(self):
		return "%s" % self.__dict__


class SETag(Base):
	""" Tagging model for OA """
	__tablename__ = u'se_tags'

	se_id = Column(Integer, ForeignKey(
		'sales_estimate.id'), primary_key=True, name='se_id', autoincrement=False)
	tag_id = Column(Integer, ForeignKey('tags.id'), primary_key=True, name='tag_id', autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), primary_key=True, name='enterprise_id', autoincrement=False)

	se = relationship("SalesEstimate", backref=backref("se_tags_se", order_by=se_id))
	tag = relationship("Tag", backref=backref("see_tag_tag", order_by=(tag_id, enterprise_id)))

	def __init__(self, se_id=None, tag_id=None, enterprise_id=None):
		self.enterprise_id = enterprise_id
		self.se_id = se_id
		self.tag_id = tag_id


class SEAttachments(Base, AutoSerialize):
	""" Data Model that persists the SE attachment with its extension."""
	__tablename__ = u'se_attachment'

	se_id = Column(Integer, ForeignKey(
		'sales_estimate.id'), primary_key=True, name='se_id', autoincrement=False)
	enterprise_id = Column(
		Integer, ForeignKey('sales_estimate.enterprise_id'), name='enterprise_id', primary_key=True,
		autoincrement=False)
	doc_extension = Column(String(4), name='doc_extension', nullable=True)
	revised_on = Column(DateTime, primary_key=True, name='revised_on', autoincrement=False)
	revised_by = Column(Integer, ForeignKey('auth_user.id'), name='revised_by')
	attachment_id = Column(Integer, ForeignKey('attachment.id'), primary_key=True, name='attachment_id')

	attachment = relationship(
		"Attachment", backref="attachment_se", order_by="Attachment.attachment_id",
		primaryjoin="and_(SEAttachments.attachment_id==Attachment.attachment_id, SEAttachments.enterprise_id==Attachment.enterprise_id)",
		uselist=False)

	def __init__(
			self, se_id=None, enterprise_id=None, doc_extension='',
			revised_on=None, revised_by=None, attachment_id=None):
		self.se_id = se_id
		self.enterprise_id = enterprise_id
		self.doc_extension = doc_extension
		self.revised_on = revised_on
		self.revised_by = revised_by
		self.attachment_id = attachment_id

	def __repr__(self):
		return "%s" % self.__dict__


class SEDocument(Base, AutoSerialize):
	""" Data Model that persists the SE document with its extension."""
	__tablename__ = u'se_document'

	se_id = Column(Integer, ForeignKey('sales_estimate.id'), primary_key=True, name='se_id', autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	document_pdf = Column(LargeBinary, name='document_pdf')
	is_rejected = Column(Boolean, name='is_rejected', default=False)
	revised_by = Column(Integer, ForeignKey('auth_user.id'), name='revised_by')
	revised_on = Column(DateTime, primary_key=True, name='revised_on', autoincrement=False)

	reviser = relationship("User", backref=backref('se_reviser', order_by=revised_by))
	se = relationship("SalesEstimate", backref=backref('se_document_sales_estimate', order_by=se_id))

	def __init__(
			self, se_id=None, enterprise_id=None, document_pdf=None, is_rejected=False, revised_by=None,
			revised_on=None):
		self.se_id = se_id
		self.enterprise_id = enterprise_id
		self.document_pdf = document_pdf
		self.is_rejected = is_rejected
		self.revised_by = revised_by
		self.revised_on = revised_on

	def __repr__(self):
		return '%s %s' % (self.se_id, self.is_rejected)


class SEContactMap(Base, AutoSerialize):
	""" Data Model that persists the SE contact map with its extension."""
	__tablename__ = u'se_contact_map'

	se_id = Column(Integer, ForeignKey(
		'sales_estimate.id'), primary_key=True, name='se_id', autoincrement=False)
	contact_id = Column(
		Integer, ForeignKey('enterprise_contact_map.contact_id'), name='contact_id', primary_key=True,
		autoincrement=False)
	last_modified_on = Column(DateTime, name='last_modified_on', autoincrement=False)
	last_modified_by = Column(Integer, ForeignKey('auth_user.id'), name='last_modified_by')

	contact = relationship(
		"EnterpriseContactMap", backref='se_contact_details',
		primaryjoin="(EnterpriseContactMap.contact_id==SEContactMap.contact_id)")

	def __init__(
			self, se_id=None, contact_id=None, last_modified_on=None, last_modified_by=None):
		self.se_id = se_id
		self.contact_id = contact_id
		self.last_modified_on = last_modified_on
		self.last_modified_by = last_modified_by


class SETax(Base):
	__tablename__ = u'se_tax'

	se_id = Column(Integer, ForeignKey(
		'sales_estimate.id'), primary_key=True, name='se_id', autoincrement=False)
	tax_code = Column(String, ForeignKey('tax.code'), name='tax_code', primary_key=True, autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)

	se = relationship("SalesEstimate", backref=backref('se_tax_se', order_by=se_id))
	tax = relationship("Tax", backref='se_tax_tax', primaryjoin=(
		"and_(SETax.tax_code == Tax.code, SETax.enterprise_id == Tax.enterprise_id)"))

	def __init__(self, se_id=None, tax_code=None, enterprise_id=None):
		self.se_id = se_id
		self.tax_code = tax_code
		self.enterprise_id = enterprise_id

	def __repr__(self):
		return "%s" % self.__dict__

	def getTaxLedgerId(self):
		return self.tax.getOutputLedgerId()


class SalesEstimateTemplateConfig(Base):
	"""
	Models the Enterprise's Document Configurations for Sales Estimate module - One-to-One
	"""
	__tablename__ = u'se_template_config'

	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name='enterprise_id', primary_key=True, autoincrement=False)
	se_doc_reg_items = Column(String, name='se_doc_registration_detail')

	def __init__(self, enterprise_id=None, se_doc_reg_items=""):
		self.enterprise_id = enterprise_id
		self.se_doc_reg_items = se_doc_reg_items

	def __repr__(self):
		return 'SE_Doc_Reg_Details: %s' % self.se_doc_reg_items


class Specification(Base, AutoSerialize):
	"""Class that models Specification table.It holds specification on each parameter of material and range of it"""
	__tablename__ = u'specification'

	__table_args__ = (
	ForeignKeyConstraint(('enterprise_id', 'material_id'), ('materials.enterprise_id', 'materials.id')),)

	enterprise_id = Column(Integer, ForeignKey('materials.enterprise_id'), name='enterprise_id', primary_key=True,
	                       autoincrement=False)
	material_id = Column(Integer, ForeignKey('materials.id'), name='material_id', primary_key=True, autoincrement=False)
	parameter = Column(String(100), name='parameter', nullable=False, primary_key=True, autoincrement=False)
	comments = Column(String(100), name='comments')
	inspection_method = Column(String(100), name='inspection_method')
	min_value = Column(DECIMAL, name='min_value')
	max_value = Column(DECIMAL, name='max_value')
	unit = Column(String(10), name='unit')
	qc_critical = Column(Boolean, name='qc_critical', default=False)
	reaction_plan = Column(String(150), name='reaction_plan')

	UniqueConstraint(enterprise_id, material_id)

	material = relationship(
		"Material", backref='specification_refs_materials', single_parent=True,
		primaryjoin='and_(Specification.enterprise_id == Material.enterprise_id, Specification.material_id == Material.material_id)')

	def __init__(
			self, enterprise_id=None, material_id=None, parameter=None, comments=None,
			inspection_method=None, min_value=None, max_value=None, unit=None, qc_critical=False, reaction_plan=None):
		self.enterprise_id = enterprise_id
		self.material_id = material_id
		self.parameter = parameter
		self.comments = comments
		self.inspection_method = inspection_method
		self.min_value = min_value
		self.max_value = max_value
		self.unit = unit
		self.qc_critical = qc_critical
		self.reaction_plan = reaction_plan

	def __repr__(self):
		return "{parameter} : {min} - {max} {unit}".format(
			parameter=self.parameter, min=self.min_value, max=self.max_value, unit=self.unit)


class PartyContactMap(Base):
	"""
	Entity to hold Party contact Map.
	"""
	__tablename__ = u'party_contact_map'

	enterprise_id = Column(
		Integer, ForeignKey('enterprise.id'), primary_key=True, name='enterprise_id', autoincrement=False)
	party_id = Column(
		Integer, ForeignKey('party_master.party_id'), primary_key=True, name='party_id', autoincrement=False)
	contact_id = Column(
		Integer, ForeignKey('contact_details.id'), primary_key=True, name='contact_id', autoincrement=False)
	sequence_id = Column(Integer, name='sequence_id', autoincrement=False)

	contact = relationship(
		"ContactDetails", backref='party_contact_details',
		primaryjoin="(ContactDetails.id==PartyContactMap.contact_id)")

	def __init__(self, enterprise_id=None, party_id=None, contact_id=None, sequence_id=None):
		self.enterprise_id = enterprise_id
		self.party_id = party_id
		self.contact_id = contact_id
		self.sequence_id = sequence_id


class LocationMaster(Base, AutoSerialize):
	"""
	Models the location.
	"""
	__tablename__ = u'location_master'

	id = Column(BigInteger, primary_key=True, name='id', autoincrement=True)
	enterprise_id = Column(Integer, ForeignKey("enterprise.id"), name="enterprise_id")
	name = Column(String(100), name='name', nullable=False)
	contact_person = Column(String(100), name='contact_person', nullable=False)
	phone_no = Column(String(40), name='phone_no')
	email = Column(String(40), name='email')
	city = Column(String(40), name='city')
	state = Column(String(40), name='state')
	country = Column(String(45), name='country')
	pin_code = Column(String(10), name='pin_code')
	fax = Column(String(30), name='fax')
	gst_label = Column(String, name='gst_label')
	address1 = Column(String(140), name='address1')
	address2 = Column(String(140), name='address2')
	code = Column(String(20), name='code')
	last_modified_on = Column(DateTime, name='last_modified_on')
	last_modified_by = Column(Integer, name="last_modified_by")
	created_on = Column(DateTime, name='created_on')
	is_default = Column(Integer, name='is_default', default=0)

	def __init__(
			self, id=None, enterprise_id=None, name=None, contact_person=None, city=None, state=None, phone_no=None, email=None,
			pin_code=None, country=None, fax=None, gst_label=None, address1=None, address2=None, code=None,
			last_modified_on=None, last_modified_by=None, created_on=None, is_default=None):
		self.id = id
		self.enterprise_id = enterprise_id
		self.name = name
		self.contact_person = contact_person
		self.phone_no = phone_no
		self.email = email
		self.city = city
		self.state = state
		self.country = country
		self.pin_code = pin_code
		self.fax = fax
		self.gst_label = gst_label
		self.address1 = address1
		self.address2 = address2
		self.code = code
		self.last_modified_on = last_modified_on if last_modified_on else datetime.now()
		self.last_modified_by = last_modified_by
		self.created_on = created_on if created_on else datetime.now()
		self.is_default = is_default


class UserLocationMap(Base, AutoSerialize):
	"""
	Data Model Object for the table User Location Map
	"""
	__tablename__ = u'user_location_map'

	user_id = Column(Integer, ForeignKey('auth_user.id'), name='user_id', primary_key=True, autoincrement=False)
	location_id = Column(ForeignKey('location_master.id'), name='location_id', primary_key=True, autoincrement=False)
	enterprise_id = Column(Integer, ForeignKey(
		'enterprise.id'), name="enterprise_id", primary_key=True, autoincrement=False)
	status = Column(Boolean, name='status', default=False)

	user = relationship("User", backref=backref("user_location_map_user", order_by=user_id))
	location = relationship("LocationMaster", backref=backref("user_location_map_location", order_by=location_id))
	enterprise = relationship("Enterprise", backref=backref("user_location_map_user_enterprise", order_by=enterprise_id))

	def __init__(self, user_id=None, location_id=None, enterprise_id=None, status=None):
		self.user_id = user_id
		self.location_id = location_id
		self.enterprise_id = enterprise_id
		self.status = status


class StockTransfer(Base):
	"""
	Data Model Object for the table Stock Transfer
	"""
	__tablename__ = u'stock_transfer'

	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name="enterprise_id", nullable=False)
	transfer_id = Column(Integer, primary_key=True, autoincrement=True)
	transfer_no = Column(String(45), nullable=False)
	financial_year = Column(String(45), nullable=False)
	total_qty = Column(Integer, nullable=False)
	total_value = Column(DECIMAL(precision=2), nullable=False)
	from_location = Column(Integer, ForeignKey('location_master.id'), name="from_location", nullable=False)
	to_location = Column(Integer, ForeignKey('location_master.id'), name="to_location", nullable=False)
	status = Column(Integer, nullable=False)
	pp_id = Column(Integer,ForeignKey('purchase_order.id'), name='pp_id', nullable=True)
	created_on = Column(DateTime, nullable=False)
	created_by = Column(Integer, nullable=True)
	last_modified_on = Column(DateTime, nullable=True)
	last_modified_by = Column(Integer, nullable=True)
	approved_on = Column(DateTime, nullable=True)
	approved_by = Column(Integer, nullable=True)
	received_on = Column(DateTime, nullable=True)
	received_by = Column(Integer, nullable=True)
	remarks = Column(JSON, nullable=False)

	from_location_rel = relationship("LocationMaster", foreign_keys=[from_location], backref=backref("stock_transfer_from", order_by=transfer_id))
	to_location_rel = relationship("LocationMaster", foreign_keys=[to_location], backref=backref("stock_transfer_to", order_by=transfer_id))
	enterprise = relationship("Enterprise", backref=backref("stock_transfer_enterprise", order_by=enterprise_id))
	transfer_details = relationship("TransferDetails", backref=backref("stock_transfer_details", order_by=transfer_id))
	po = relationship("PurchaseOrder", backref=backref("transfer_pp", order_by=pp_id))

	def __init__(self, enterprise_id=None, transfer_no=None, financial_year=None, total_qty=None, total_value=None,
				 from_location=None, to_location=None, status=None, pp_id=None, created_on=None, created_by=None,
				 last_modified_on=None, last_modified_by=None, approved_on=None, approved_by=None,
				 received_on=None, received_by=None, remarks=None):
		self.enterprise_id = enterprise_id
		self.transfer_no = transfer_no
		self.financial_year = financial_year
		self.total_qty = total_qty
		self.total_value = total_value
		self.from_location = from_location
		self.to_location = to_location
		self.status = status
		self.pp_id = pp_id
		self.created_on = created_on
		self.created_by = created_by
		self.last_modified_on = last_modified_on
		self.last_modified_by = last_modified_by
		self.approved_on = approved_on
		self.approved_by = approved_by
		self.received_on = received_on
		self.received_by = received_by
		self.remarks = remarks

	def getCode(self):
		code_format = "{type}{number:05}{sub_number}"
		if self.status == 1:
			code = code_format.format(type="TMP#", number=int(self.transfer_no), sub_number="")
		else:
			code_format = "{fy}/" + code_format
			code = code_format.format(type="IDC", fy=self.financial_year, number=int(self.transfer_no), sub_number="")
		return code


class TransferDetails(Base):
	"""
	Data Model Object for the table Transfer Details
	"""
	__tablename__ = 'transfer_details'

	detail_id = Column(Integer, primary_key=True, autoincrement=True)
	enterprise_id = Column(Integer, ForeignKey('enterprise.id'), name="enterprise_id", nullable=False)
	transfer_id = Column(Integer, ForeignKey('stock_transfer.transfer_id'), name="transfer_id", nullable=False)
	item_id = Column(Integer, ForeignKey('materials.id'), name="item_id", nullable=False)
	is_faulty = Column(Integer, name='is_faulty', autoincrement=False, nullable=False)
	quantity = Column(Integer, nullable=False)
	rate = Column(DECIMAL(precision=2), nullable=False)

	enterprise = relationship("Enterprise", backref=backref("transfer_details_enterprise", order_by=enterprise_id))
	stock_transfer = relationship("StockTransfer", backref=backref("stock_transfer_header", order_by=detail_id))
	item = relationship("Material", backref=backref("transfer_item", order_by=detail_id))

	def __init__(self, enterprise_id=None, transfer_id=None, item_id=None, is_faulty=None, quantity=None, rate=None):
		self.enterprise_id = enterprise_id
		self.transfer_id = transfer_id
		self.item_id = item_id
		self.is_faulty = is_faulty
		self.quantity = quantity
		self.rate = rate

class MRP_Materials(Base):
	"""
	Models the materials for MRP (Material Requirements Planning), including allocation and issuance details.
	"""
	__tablename__ = 'mrp_materials'

	id = Column(Integer, primary_key=True, autoincrement=True, name='id')
	enterprise_id = Column(Integer, nullable=False, name='enterprise_id')
	item_id = Column(Integer, nullable=False, name='item_id')
	parent_id = Column(Integer, name='parent_id')
	pp_id = Column(Integer, nullable=False, name='pp_id')
	allocated_qty = Column(DECIMAL, default=0, name='allocated_qty')
	required_qty = Column(DECIMAL, default=0, name='required_qty')
	issued_qty = Column(DECIMAL, default=0, name='issued_qty')
	requested_qty = Column(Integer, default=0, name='requested_qty')
	updated_on = Column(Date, nullable=False, name='updated_on', default=datetime.now())
	updated_by = Column(Integer, nullable=False, name='updated_by')
	location_id = Column(Integer, nullable=False, name='location_id')
	is_expand = Column(Integer, nullable=False, name='is_expand')
	def __init__(self, enterprise_id=None, item_id=None, parent_id=None, pp_id=None, allocated_qty=0,
				 required_qty=0, issued_qty=0, requested_qty=0, updated_on=None, updated_by=None, location_id=None, is_expand=0):
		self.enterprise_id = enterprise_id
		self.item_id = item_id
		self.parent_id = parent_id
		self.pp_id = pp_id
		self.allocated_qty = allocated_qty
		self.required_qty = required_qty
		self.issued_qty = issued_qty
		self.requested_qty = requested_qty
		self.updated_on = updated_on
		self.updated_by = updated_by
		self.location_id = location_id
		self.is_expand = is_expand


class ClosingStock(Base):
	"""
	Models the closing stock, including item, location, quantity, last update date, and enterprise information.
	"""
	__tablename__ = 'closing_stock'

	item_id = Column(Integer, primary_key=True, nullable=False, name='item_id')
	location_id = Column(Integer, primary_key=True, nullable=False, name='location_id')
	qty = Column(DECIMAL, default=0, name='qty')
	last_updated_on = Column(DateTime, nullable=True, name='last_updated_on', default=datetime.now())
	enterprise_id = Column(Integer, nullable=False, name='enterprise_id')
	is_faulty = Column(Integer, name='is_faulty', autoincrement=False, nullable=False)

	def __init__(self, item_id=None, location_id=None, qty=0, last_updated_on=None, enterprise_id=None, is_faulty=None):
		self.item_id = item_id
		self.location_id = location_id
		self.qty = qty
		self.last_updated_on = last_updated_on or datetime.now()
		self.enterprise_id = enterprise_id
		self.is_faulty = is_faulty
