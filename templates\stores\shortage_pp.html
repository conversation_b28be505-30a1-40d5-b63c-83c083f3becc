{% extends "stores/sidebar.html" %}
{% block shortage_list_pp %}
{% if access_level.view %}
<style xmlns="http://www.w3.org/1999/html">
li.po_reports_side_menu a{
    outline: none;
    background-color: #e6983c !important;
}

.multiselect-filter {
	margin-bottom: 15px;
    position: fixed;
    width: 98%;
    background: #FFF;
    z-index: 10;
    margin-top: -15px;
    padding: 12px;
    margin-left: -15px;
}

#shortage-list-table .selected td{
	background: rgba(32, 155, 225,0.4);
	color: #fff;
}

<!--.table#report-table td {-->
<!--	min-width: 140px;-->
<!--	width: 140px !important;-->
<!--	max-width: 140px !important;-->
<!--}-->

label[for='include_item_order'],
label[for='include_item_order']::after {
	color: #004195;
}

label[for='include_item_order']::before {
	border: 1px solid #004195;
}

#report-table tbody tr td .tree-view {
	position: relative;
}

#report-table tbody tr td .tree-view:before {
    border-bottom: 1px solid #bbb;
    content: "";
    left: 6px;
    position: absolute;
    top: 6px;
    width: 1.5em;
}

#report-table tbody tr td .tree-view:after {
    border-left: 1px solid #bbb;
    content: "";
    left: 6px;
    position: absolute;
    top: -25px;
    height: 4em;
}

#report-table .fa-plus:before,
#report-table .fa-minus:before {
	border: solid 1px #333;
    padding: 2px 3px 2px 3px;
    border-radius: 3px;
    font-size: 10px;
}
#report-table {
    width: max-content;
    border-collapse: separate; /* Separate borders for more control */
    border-spacing: 0; /* Removes extra spacing around cells */
    background-color: #f2f2f2 !important;
}
#report-table thead tr:nth-child(1) th:nth-child(1) {
    position: sticky;
    top: 0;
    left: 0;
    z-index:10;
    background-color:#f2f2f2
}

#report-table thead tr:nth-child(1) th:nth-child(2) {
    position: sticky;
    left: 60px;
    top:0;
    z-index:10;
    background-color:#f2f2f2
}

#report-table thead tr:nth-child(1) th:nth-child(3) { position: sticky; left: 310px; top:0; z-index: 10;background-color:#f2f2f2 }
#report-table thead tr:nth-child(1) th:nth-child(4){ position: sticky; left: 460px; top:0;  z-index: 10;background-color:#f2f2f2 }
#report-table thead tr:nth-child(1) th:nth-child(5){ position: sticky; left: 520px; top:0;  z-index: 10;background-color:#f2f2f2 }
#report-table thead tr:nth-child(1) th:nth-child(6) { position: sticky; left: 620px; top:0;z-index: 10;background-color:#f2f2f2 }
#report-table thead tr:nth-child(1) th:nth-child(7) { position: sticky; left: 720px; top:0;  z-index: 10;background-color:#f2f2f2 }
#report-table thead tr:nth-child(1) th:nth-child(8) { position: sticky; left: 820px; top:0;  z-index: 10;background-color:#f2f2f2 }

#report-table tbody td:nth-child(1) { position: sticky; left: 0;top:137px;z-index: 100;   background-color:#f2f2f2 }
#report-table tbody td:nth-child(2) { position: sticky; left: 60px;top:137px;z-index: 100;  background-color:#f2f2f2 }
#report-table tbody td:nth-child(3) { position: sticky; left: 310px;top:137px;z-index: 100;  background-color:#f2f2f2 }
#report-table tbody td:nth-child(4){ position: sticky; left: 460px;top:137px;z-index: 100; background-color:#f2f2f2 }
#report-table tbody td:nth-child(5){ position: sticky; left: 520px;top:137px;z-index: 100; background-color:#f2f2f2 }
#report-table tbody td:nth-child(6) { position: sticky; left: 620px;top:137px;z-index: 100;background-color:#f2f2f2 }
#report-table tbody td:nth-child(7) { position: sticky; left: 720px;top:137px;z-index: 100; background-color:#f2f2f2 }
#report-table tbody td:nth-child(8) { position: sticky; left: 820px;top:137px;z-index: 100;background-color:#f2f2f2 }

.material_shortage_report {
    max-height:800px;
    overflow-y: auto;
}

.material_shortage_report thead{
    position: sticky;
    top: 0;
    z-index: 1;
}
#report-table tbody td.custom-background {
    background-color:    #a4d8f9 !important;
}
	.disabled-field,.disabled {
    pointer-events: none;
    opacity: 0.4
	}

</style>

<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/purchase_report.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/shortage_pp.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-multiselect.js?v={{ current_version }}"></script>
<input type="hidden" id="pp_permission" value="{% if logged_in_user|canApprove:'PRODUCTION' or logged_in_user|canEdit:'PRODUCTION' %}true{% else %}false{% endif %}">
<div class="right-content-container download-icon-onTop">
	<div class="page-title-container">
		<span class="page-title">Material Requirement Calculator</span>
	</div>
	<div class="container-fluid">
		<div class="view_table add_table row">
			<form action="/erp/stores/materialshortage_pp/" method="post">{% csrf_token %}
				<div>
					<div class="col-sm-12 text-right" style="padding-right: 30px; margin-top: 5px; margin-bottom: 10px;">
						<div class="btn btn-save hide" data-toggle="modal" data-target="#modal_shortage_list" >
							<i class="fa fa-plus" aria-hidden="true"></i>
						</div>
					</div>
					<div id="modal_shortage_list" class="modal fade" role="dialog">
						<div class="modal-dialog modal-lg">
							<div class="modal-content modal-multiselect">
								<div class="modal-header">
									<button type="button" class="close" data-dismiss="modal">&times;</button>
									<h4 class="modal-title">Select the Production Plan</h4>
								</div>
								<div class="modal-body">
									<div class="row multiselect-filter" style="margin-bottom: 15px">
										<div class="col-sm-6">
											<input type="text" class="form-control" onkeyup="filterShortageList()" id="filter-shortage-list" style="height: 28px;" placeholder="Filter..." />
										</div>
										<div class="col-sm-6 text-right selected_items_count" style="padding-top: 6px; font-size: 16px;">
											Selected items: <span >0</span>
										</div>
										<div class="col-sm-12 multiselect-header checkbox" style="margin: 15px 0 0;">
											<input type="checkbox" id="chk_all_multiselect">
											<label for="chk_all_multiselect">Select All</label>
										</div>
									</div>
									<div class="checkbox-block chk_multiselect row" style="margin-top: 90px;">
										<div class="text-center empty_shortage_list_row hide">
											No Matching Results Found!
										</div>
										<table class="table">
											{% for j in materials %}
											<tr>
												<td style="padding: 0;">
													<span class="chk_multiselect_textbox col-sm-12">
														<span class="checkbox sub-checkbox-block col-sm-10">
<!--															{% if not j.2 %} <span style="position: absolute; left: -3px; height: 13px;" class='non_stock-flag'></span> {% endif%}-->
															<input type="checkbox" id="material_{{j.0}}-{{j.6}}" value="{{ j.0 }}" >
															<label for="material_{{j.0}}-{{j.6}}"> {{ j.5 }} - {{ j.1 }}
																{% if j.3 == True %}<span class='service-item-flag'></span>{% endif %}
																{% if not j.2 and j.3 != True %} <span class='non_stock-flag'></span> {% endif%}
															</label>
														</span>
														<span class="checkbox_text col-sm-2">
															<input type="text" id="material_qty_{{j.0}}" class="form-control text-right" placeholder="Enter Quantity" maxlength="16" onfocus="setNumberRangeOnFocus(this,12,3)" disabled value="{{ j.4 }}"  />
														</span>
													</span>
												</td>
											</tr>
											{% endfor %}
										</table>

									</div>
								</div>
								<div class="modal-footer">
									<span class=" nonStock-text">  <span class=" nonStock-text non_stock-flag"></span> - NON-STOCKABLE</span>
									<span class=" nonStock-text">  <span class=" nonStock-text service-item-flag"></span> - SERVICE</span>
									<div style="text-align: left; float: left;">
										<div class="checkbox">
											<input type="checkbox" id="include_item_order" name="include_item_order" checked="checked">
											<label for="include_item_order">Include Items Ordered waiting to be Received</label>
										</div>
									</div>

									<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
									<button type="button" class="btn btn-save" id="save_material_list">View Report</button>
									<input type="hidden" name="cat_list" id="cat_list" class="form-control" placeholder="Enter Qty"  value="{{cat_list}}">
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="col-lg-12 view_list_table hide" style="align:center;">
					<div class="col-sm-3">
						<label>Material Name</label>
						<select class="form-control chosen-select" name="cat_code" id="cat_code" >
							<option value="0">Select Material Name</option>
							{% for j in materials %}
							<option value="{{ j.0 }}" {% if j.1 == cat_code %}selected{% endif %}>{{ j.1 }}</option>
							{% endfor %}
						</select>
					</div>
					<div class="col-sm-3">
						<label>Qty</label>
						<input type="text" name="qty" id="qty" class="form-control" placeholder="Enter Qty"  value={{qty}}>
					</div>

					<div class="col-sm-3">
						<div class="material_txt"><span class="po_title_txt">&nbsp;</span>
							<input type="submit" class="btn btn-save" value="View Report" id="refresh"/>
						</div>
					</div>
				</div>
			</form>
		</div>
		<div class="view_table add_table">
			<span class="pull-left nonStock-text" style="margin-top: -8px;">  <span class=" nonStock-text non_stock-flag"></span> - NON-STOCKABLE</span>
			<span  class="pull-left nonStock-text" style="margin-top: -8px;">  <span class=" nonStock-text service-item-flag"></span> - SERVICE</span>
			<div class="col-lg-12 " style="width: calc(100% + 30px); margin-left: -15px; background: #eee; padding: 7px 15px; border-bottom: 1px solid #bbb;">
				<div style="max-height: 200px; overflow: auto; margin: 0 15px; transition: height 0.7s linear;" >

					<table class="table custom-table table-striped table-bordered" id="shortage-list-table" style="width: 100%; margin-top: 10px;">
						<thead>
						<tr>
							<th style="width: 10%;">S.No</th>
							<th style="width: 30%;">Production Plan No</th>
							<th style="width: 30%;">Name</th>
							<th style="width: 25%;">Drawing No</th>

							<th class="hide" style="width: 10%;">Qty</th>
						</tr>
						</thead>
						<tbody >
						{% if material_list%}
							{% for material in material_list %}
							<tr>
								<td class="text-center">
									<a class='table-drag-icon' style='visibility: hidden; cursor: move; float: left;' >
										<i class='fa fa-bars' style='font-size: 1.7em; color: #555;width: 18px; height: 18px;'></i>
									</a>
									<span class="drag_serial_number">{{ forloop.counter}}</span>
								</td>
								<td name="pp_no">{{ material.pp_no }}
									<input type="hidden" name="location_id" id="location_id" value="{{ material.location_id }}">
									<input type="hidden" name="pp_id" id="pp_id" value="{{ material.pp_id }}">
								</td>
								<td {% if not material.is_stockable and material.is_service != True %} class='non_stock-flag'{% endif %}>
									{{ material.material_name }}
									{% if material.is_service == True %}
									<span class='service-item-flag'></span>
									{% endif %}
								</td>
								<td>{{ material.drawing_no }}</td>
								<td hidden="hidden">{{ material.make }}</td>
								<td class="text-right hide" name="material_qty" id="material_qty_{{ forloop.counter }}">{{ material.qty }}</td>
								<td name="material_cat_code" hidden="hidden">{{ material.cat_code}}</td>
							</tr>
							{% endfor %}
						{%else%}
							<tr style="text-align:center">
								<td colspan="4"><span>No BOM available for this material.</span></td>
							</tr>
						{% endif %}

						</tbody>
					</table>

				</div>
				<div class="col-sm-6 text-left" style="margin-top: 10px;">
					<div role="button" class="expand-shortage-list" onClick="expandShortageList()">
						<span>Show More</span>
						<i class="fa fa-chevron-down" aria-hidden="true"></i>
					</div>
				</div>
				<div class="col-sm-6 text-right" style="margin-top: 10px;">
					{% if material_list%}<button class="btn btn-save" value="Apply" id="apply-shortage-list" onclick="generateShortageReportPP()">Apply</button>{%endif%}
				</div>
			</div>
			<div class="csv_export_button" style="top: 18px !important;position: relative; float: right;margin-bottom:20px;">
					<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#report-table'), 'Shortage_List.csv']);" data-tooltip="tooltip" data-placement="top" title="Download&nbsp;Shortage&nbsp;List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
				</div>
			<div id='loadingmessage_changelog_listing_ie' style="text-align:center;position: absolute;z-index: 150;top: 60%;left: 50%;display:none">
						<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' width="100">
						<br /> Please wait...
					</div>
			<div class="col-lg-12 material_shortage_report hide" style="width:100%;overflow:auto;padding:0 !important">
				<br /><br />

				<table class="table custom-table table-striped table-bordered" id="report-table" style="width: 100%;">

					<thead></thead>
					<tbody></tbody>
				</table>
			</div>
		</div>
	</div>
</div>
<div id="available_stock_modal" class="modal fade" role="dialog" >
  	<div class="modal-dialog">
    	<div class="modal-content">
      		<div class="modal-header">
      			<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Available Stock</h4>
      		</div>
      		<div class="modal-body">
		        <table class="table table-striped table-bordered custom-table text_box_in_table" id="supp_material_table" style="margin-top: 15px;">
					<thead>
						<tr>
							<th width="50%">Location</th>
							<th>Stock Qty</th>
						</tr>
					</thead>
					<tbody></tbody>
					<tfoot>
						<tr>
							<td class="text-right grand-total-text">Total</td>
							<td class="text-right grand-total-amount" id="closing_stock_total" style="padding-right: 15px;">0</td>
						</tr>
					</tfoot>
				</table>
	        </div>
		    <div class="modal-footer">
			    <span class="btn btn-cancel" data-dismiss="modal">Close</span>
		    </div>
	    </div>
    </div>
</div>

<div id="stock_transfer_request_modal" class="modal fade" role="dialog" >
  	<div class="modal-dialog" style="width:80%">
    	<div class="modal-content">
      		<div class="modal-header">
      			<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Inter Stock Transfer Request</h4>
      		</div>
      		<div class="modal-body" style="height:80%">
		        <table class="table table-striped table-bordered custom-table text_box_in_table" id="" style="margin-top: 15px;">
					<thead>
						<tr>
							<th>S.no</th>
							<th>Material Name</th>
							<th>From Location</th>
							<th>To Location</th>
							<th>Qty</th>
						</tr>
					</thead>
					<tbody></tbody>
					<tfoot></tfoot>
				</table>
	        </div>
		    <div class="modal-footer">
			    <span class="btn btn-cancel" data-dismiss="modal">Close</span>
		    </div>
	    </div>
    </div>
</div>
<script>
$(document).ready(function(){
	ModalMultiSelect();
	$("#loading").hide();
	SelectedCatLoad();
	tableHoverIconsInit();
<!--	generateShortageReportPP();-->

	$("#shortage-list-table").sortable({
		helper: fixWidthHelper,
	    items: 'tbody tr',
	    cursor: 'pointer',
	    axis: 'y',
	    dropOnEmpty: false,
	    start: function (e, ui) {
	        ui.item.addClass("selected");
	    },
	    stop: function (e, ui) {
	        ui.item.removeClass("selected");
	        $(this).find("tr").each(function (index) {
	            if (index > 0) {
	                //$(this).find("td").eq(0).html(index);
	            }
	        });
	    }
	});
});

function expandShortageList() {
	if ($("#shortage-list-table").height() > $("#shortage-list-table").closest("div").height()) {
		$("#shortage-list-table").closest("div").css({maxHeight : "100%"});
		$(".expand-shortage-list").find("span").text("Show Less");
		$(".expand-shortage-list").find(".fa").removeClass("fa-chevron-down").addClass("fa-chevron-up");
	}
	else {
		$("#shortage-list-table").closest("div").css({maxHeight : "200px"});
		$(".expand-shortage-list").find("span").text("Show More");
		$(".expand-shortage-list").find(".fa").removeClass("fa-chevron-up").addClass("fa-chevron-down");
	}
}


function fixWidthHelper(e, ui) {
    ui.children().each(function() {
        $(this).width($(this).width());
    });
    return ui;
}

function tableHoverIconsInit(){
    $("#shortage-list-table tbody tr").hover(function(){
        $("#shortage-list-table tbody tr .table-drag-icon").css({visibility: "hidden"});
        $(this).find(".table-drag-icon").css({visibility: ""});
    },
    function(){
        $("#shortage-list-table tbody tr .table-drag-icon").css({visibility: "hidden"})
    });
}

function ModalMultiSelect() {
	$(".chk_multiselect input[type='checkbox']").change(function(){
		if($('.chk_multiselect').find('input[type="checkbox"]:visible').length == $('.chk_multiselect').find('input[type="checkbox"]:visible:checked').length) {
			$("#chk_all_multiselect").prop('checked', true);
		}
		else {
			$("#chk_all_multiselect").prop('checked', false);
		}

		$(".selected_items_count span").text($('.chk_multiselect').find('input:checked').length);
		if($(this).is(':checked')){
			$(this).closest('.chk_multiselect_textbox').find('.checkbox_text').find('input').removeAttr('disabled').val('1.000').focus();
		}
		else {
			$(this).closest('.chk_multiselect_textbox').find('.checkbox_text').find('input').attr('disabled','').val('');
		}
	});
	
	$(".checkbox_text input").blur(function(){
		if($(this).val() == "") $(this).val(0);
	});

	$("#chk_all_multiselect").change(function(){
		$(".chk_multiselect input[type='checkbox']:visible").prop('checked', $(this).is(':checked'));
		$(".selected_items_count span").text($('.chk_multiselect').find('input:checked').length);
		if($(this).is(':checked')){
			$('.checkbox_text:visible').find('input').removeAttr('disabled').val('1.000');
		}
		else {
			$('.checkbox_text:visible').find('input').attr('disabled','').val('');
		}
	});
	
	$("#save_material_list").click(function(){
		if($(".chk_multiselect input[type=checkbox]").is(":checked")) {
			var shortageQtyCount = 0;
			$(".chk_multiselect input[type='text']:enabled").each(function(){
				shortageQtyCount += Number($(this).val());
				if(shortageQtyCount > 0) {
					return false;
			    }
			});
			if(shortageQtyCount > 0) {
				$("#modal_shortage_list").modal('hide');
				var selected_boms=""
				$('#cat_list').val('')
				var multi_select_bom =  $('.chk_multiselect').find('input:checked');
			    multi_select_bom.each(function () {
			        selected_boms += $(this).val() + "[:]" + $(this).closest('.chk_multiselect_textbox').find('.checkbox_text').find('input').val() + "[:]" ;
			    });
				$('#cat_list').val(selected_boms);
				$('#refresh').click();
				$("#loading").show();
			}
			else {
				swal("","Please add Quantity for atleast one Material.","warning");
			}
		}
		else {
			swal("","Please select atleast one Material.","warning");
		}
	});

	$('#modal_shortage_list').on('shown.bs.modal', function (e) {
	  	if($('.chk_multiselect').find('input[type="checkbox"]:visible').length == $('.chk_multiselect').find('input[type="checkbox"]:visible:checked').length) {
			$("#chk_all_multiselect").prop('checked', true);
		}
		else {
			$("#chk_all_multiselect").prop('checked', false);
		}
	})
}

function SelectedCatLoad() {
    var selectedCat = $('#cat_list').val().split("[::]");
	$(".selected_items_count span").text(Number(selectedCat.length - 1));
    setTimeout(function(){
        $('.chk_multiselect').find('input').prop('checked', false);
        for (i = 0; i < (selectedCat.length-1); i++) {
            var selectedCatItems = selectedCat[i].split("[:]");
            $('.chk_multiselect').find('input#material_'+selectedCatItems[0]+'').prop('checked', true);
            $('.chk_multiselect').find('input#material_qty_'+selectedCatItems[0]+'').val(Number(selectedCatItems[1]).toFixed(3)).removeAttr('disabled');
        }
    },100);
}

setTimeout(function(){
	$("#search").next('.btn-group').find(".multiselect-container li:first-child").each(function(){
		$(this).addClass('li-select-all');
	});
	$("#search").next('.btn-group').find(".multiselect-container li:not(:first-child)").each(function(){
		$(this).addClass('li-all-others');
	});

	$('.li-all-others').on("change",function(){
		FieldRequiredInTable();
		$('.li-select-all').find('input').prop("checked", false);
		$('.li-select-all').removeClass('active');
	});

	$('.li-select-all').on("change",function(){
		if($(this).find('input').is(":checked")){
			$('.li-all-others').find('input').prop("checked", true);
			FieldRequiredInTable();
		}
		else {
			$('.li-all-others').find('input').prop("checked", false)
			FieldRequiredInTable();
		}
	});
	$('.nav-pills li').removeClass('active');
	$('#li_shortage').addClass('active');
},500);

function FieldRequiredInTable() {
		$("#report-table").find('tr').each(function(){
		   $(this).find('td').hide();
		   $(this).find('th').hide();
		   $(this).find('td:nth-child(1)').show();
		   $(this).find('th:nth-child(1)').show();
		});
		var selectedValue="1";
		$(".li-all-others").each(function(){
			if($(this).find('input').is(":checked")){
				selectedValue+=","+$(this).find('input').val();
			}
		});
		if(selectedValue != "") {
			var splitSelectedValue = selectedValue.split(',');
			$.each(splitSelectedValue,function(i){
			   $("#report-table").find('tr').each(function(){
				   $(this).find('td:nth-child('+splitSelectedValue[i]+')').show();
					$(this).find('th:nth-child('+splitSelectedValue[i]+')').show();
			   });
			});
		}
		$(".no_match_found, .empty_search_result").attr('colspan',$('#mod_tbl tr th:visible').length);
	}

 	var oTable;
	var oSettings;
	function TableHeaderFixed() {
	   oTable = $('#report-table').DataTable({
			fixedHeader: true,
			"pageLength": 50,
			"orderCellsTop": true,
			"search": {
				"smart": false
			}
		});
		oTable.on("draw",function() {
			var keyword = $('#report-table_filter > label:eq(0) > input').val();
			$('#report-table').unmark();
			$('#report-table').mark(keyword,{});
		});
		oTable.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
		oSettings = oTable.settings();
	}

	function filterShortageList() {
	  	var filter = $("#filter-shortage-list").val().toLowerCase();
	  	$(".chk_multiselect_textbox").each(function(){
	  		if($(this).find("label").text().trim().toLowerCase().indexOf(filter) > -1) {
	  			$(this).closest("tr").removeClass("hide");
	  		}
	  		else {
	  			$(this).closest("tr").addClass("hide")
	  		}
	  	});
	  	if(($(".chk_multiselect input[type='checkbox']:visible").length == $(".chk_multiselect input[type='checkbox']:visible:checked").length) && $(".chk_multiselect input[type='checkbox']:visible").length > 0) {
	  		$("#chk_all_multiselect").prop("checked", true)
	  	}
	  	else {
	  		$("#chk_all_multiselect").prop("checked", false)
	  	}
		if($(".chk_multiselect tr:visible").length == 0) {
		    $(".empty_shortage_list_row").removeClass('hide');
		}
		else {
			$(".empty_shortage_list_row").addClass('hide');
		}
	}
	$(window).scroll(function() {
	    var height = $(window).scrollTop();
	    if(height >= 400) {
	    	console.log("Entered")
	    	$( window ).resize();
	    }
	});
	$('#available_stock_modal').on('show.bs.modal', function (event) {
		$("#loading").show();
		var td = $(event.relatedTarget);
		console.log("td",td);
    	var itemId = [];
    	itemId.push(td.data('item_id'));
    	console.log("itemId",itemId);
    	var locationWiseStkQty = {
        	"is_faulty": 0,
        	"item_id": itemId,
        	"invoice_id": ""
    	};
        $.ajax({
            url: '/erp/stores/json/issue/location_wise_closingstock/',
            method: 'POST',
            dataType: 'json',
            data: {
                "material": JSON.stringify(locationWiseStkQty),
                enterprise_id: "102"
            },
            success: function(response) {
				var totalStock = 0;
				$('#supp_material_table tbody').empty();

				$.each(response.data, function(index, item) {
					$.each(item.closing_stocks, function(key, value) {
						totalStock += value.qty;
						$('#supp_material_table tbody').append(
							`<tr>
								<td class='text-center'>${value.location_name}</td>
								<td class='text-right'>${value.qty}</td>
							</tr>`
						);
					});
				});

				$('#closing_stock_total').text(totalStock);
				$("#loading").hide();
			},
            error: function(xhr, status, error) {
                console.error('Error fetching data:', error);
            }
        });
    });

</script>
{% else %}
<div class="text-center" style="margin-top: 100px;">
	<h3>You don't have adequate permission to access this module.</h3>
	<h4>Please Contact your Administrator.</h4>
</div>
{% endif %}
{% endblock %}