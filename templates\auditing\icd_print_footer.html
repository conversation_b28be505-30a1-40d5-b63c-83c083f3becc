<!DOCTYPE html>
<style type="text/css">
	body{
		color: #000;
		font-family: 'Times New Roman';
	}
	.auth_footer{
		font-size:11pt;
	}
	.auth_footer > hr{
		width:100%;
	}

	@font-face {
        font-family: 'Times New Roman';
        font-style: normal;
        font-weight: 400;
        src: url(https://www.xserp.in/site_media/fonts/times.ttf) format('truetype');
	}
</style>
<script>
	function subst() {
		var vars={};
		var x=document.location.search.substring(1).split('&');
		for (var i in x) {
			var z=x[i].split('=',2);
			vars[z[0]] = unescape(z[1]);
		}
		var x=['frompage','topage','page','webpage','section','subsection','subsubsection'];
		for (var i in x) {
			var y = document.getElementsByClassName(x[i]);
			for (var j=0; j<y.length; ++j)
				y[j].textContent = vars[x[i]];

			if(vars['page'] == vars['topage']){
			    document.getElementById("footer").style.display = 'block';
			}
			else {
				document.getElementById("footer").style.display = 'none';
			}
		}
	}
</script>
<body onload="subst()">
	<div id="footer" class=" auth_footer" style="margin-top:16px;width:100%;">
		<div style="width: 49.333%;float:left;  text-align:left;">
			<span>{{ note_drafter }}<br>
			Prepared By
			</span>
		</div>
		<div style="width: 49.333%; float:right; text-align:right;">
			<span>{{ note_approver }}<br>
			Authorised Signatory
			</span>
		</div>
	</div>
</body>