
Listen 8113

# Sets the security model of the Apache2 HTTPD server to allow the XSerp project directory /erp/
<Directory /erp/>
        Options Indexes FollowSymLinks
        AllowOverride None
        Require all granted
</Directory>

WSGISocketPrefix /var/run/wsgi

<VirtualHost *:8113>
        <PERSON><PERSON><PERSON><PERSON>@schnellenergy.com
        DocumentRoot /opt/deployment/XSerp

        <Directory "/opt/deployment/XSerp/site_media">
                Order deny,allow
                Allow from all
        </Directory>
        <Directory "/opt/deployment/XSerp">
                AllowOverride All
                Order deny,allow
                Allow from all
        </Directory>

        WSGIDaemonProcess XSerp python-path=/opt/deployment/XSerp:/opt/.envs/xserp/lib/python2.7/site-packages/
        WSGIProcessGroup XSerp
        WSGIScriptAlias / /opt/deployment/XSerp/xserp.wsgi process-group=XSerp

        # Apache Log for the application
        ErrorLog /var/log/httpd/xserp-error.log
        CustomLog /var/log/httpd/xserp-access.log combined

</VirtualHost>
