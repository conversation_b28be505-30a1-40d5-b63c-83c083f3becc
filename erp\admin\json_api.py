"""
"""
import json
import ast

from django.http import HttpResponse

from erp.admin import logger
from erp.admin.backend import UserService, UserDAO
from erp.auth import VIEW_FLAG_POSITION, NOTIFY_FLAG_POSITION, APPROVE_FLAG_POSITION, DELETE_FLAG_POSITION, \
	EDIT_FLAG_POSITION, ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import <PERSON>quest<PERSON>and<PERSON>
from erp.models import User, UserPermission, UserEnterpriseMap
from util.api_util import response_code
from settings import SQLASession
from erp.helper import getProjectByProjectId, validate_payload

__author__ = 'nandha'


def deleteUser(request):
	"""

	Deletes User requested from a set of users listed, identified by Username
	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		user_email = rh.getPostData('user_email')
		if user_email:
			# Process of exact request
			logger.info('Deleting User: %s' % user_email)
			UserService().user_dao.deleteUserByEmail(user_email)

			response = response_code.success()
			response['custom_message'] = 'User deleted from XSerp'
			response['username'] = user_email
		else:
			response = response_code.paramMissing()
	except Exception as e:
		logger.info(e)
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def userDetails(request):
	"""
	Get User full detail requested from a set of users listed, identified by Username

	:param request:
	:return:
	"""
	user_service = UserService()
	try:
		rh = RequestHandler(request)
		user_email = rh.getPostData('user_email')
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		if user_email:
			# Process of exact request
			logger.info('Gathering user details: %s' % user_email)
			user = user_service.user_dao.getUserByEmail(user_email)

			if user is not None:
				response = response_code.success()
				user.permissions = user_service.generatePermissionsStub(user, enterprise_id)
				response.update(user_service.constructUserMap(user, enterprise_id))
			else:
				response = response_code.failure()
				response['custom_message'] = 'Incorrect Username/Password'
		else:
			response = response_code.paramMissing()
	except Exception as e:
		logger.info(e)
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def updateUser(request):
	"""
	Get User full detail requested from a set of users listed, identified by Username

	:param request:
	:return:
	"""
	user_service = UserService()
	try:
		rh = RequestHandler(request)
		data = rh.getPostData('data')
		if data:
			# Process of exact request
			logger.info('Forming User data object user details: \n')

			json_data = json.loads(data)
			logger.info('------------------------')

			logger.info("email: %s" % json_data['user_email'])
			user = user_service.user_dao.getUserByEmail(json_data['user_email'])
			if user is None:
				user = User()
				user.enterprise_id = json_data['enterprise_id']
			user.username = json_data['username']
			user.email = json_data['user_email']
			user.first_name = json_data['first_name']
			user.last_name = json_data['last_name']
			user.is_super = json_data['is_super']
			user.is_active = json_data['is_active']
			logger.info('User data set from json')
			if user.is_super is False:
				user.permissions = []
				logger.info('Iterating through permissions...')
				for key, p in json_data['permissions'].iteritems():
					bit_flags = 0
					if p['view'] is True:
						bit_flags += 2 ** VIEW_FLAG_POSITION
					if p['edit'] is True:
						bit_flags += 2 ** EDIT_FLAG_POSITION
					if p['delete'] is True:
						bit_flags += 2 ** DELETE_FLAG_POSITION
					if p['approve'] is True:
						bit_flags += 2 ** APPROVE_FLAG_POSITION
					if p['alert'] is True:
						bit_flags += 2 ** NOTIFY_FLAG_POSITION

					up = UserPermission(user_id=user.id, module_code=p['module_code'], bit_flags=bit_flags)
					user.permissions.append(up)
			logger.info('User model is ready to process for email %s' % json_data['user_email'])
			user_service.user_dao.saveUser(user, user.permissions)
			response = response_code.success()
		else:
			response = response_code.paramMissing()
	except Exception as e:
		logger.info(e)
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def allUsers(request):
	"""
	Get All user basic detail to form a user list view

	:param request:
	:return:
	"""
	user_service = UserService()
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		if enterprise_id is not None:
			# Process of exact request
			logger.info('Fetching user list for enterprise: %s' % enterprise_id)
			users = user_service.user_dao.getAllUsers(enterprise_id=int(enterprise_id))
			logger.info('Fetched user list:%s' % len(users))
			if users is not None and len(users) > 0:
				response = response_code.success()
				response['users'] = []
				logger.info("Iterating through the users...")
				for user in users:
					response['users'].append(user_service.constructUserMap(user, int(enterprise_id)))
				logger.info('User list is packed for response.')
			else:
				response = response_code.failure()
				response['custom_message'] = 'Incorrect Username/Password'
		else:
			response = response_code.paramMissing()
	except Exception as e:
		logger.exception("Object-Map construction of User list failed.. %s" % e)
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


@validate_payload(["user_id", "project_list"])
def user_project_mapping(request):
	"""
    Update Project Permission to User
    """
	rh = RequestHandler(request)
	user_id = ast.literal_eval(rh.getPostData('user_id'))
	project_list = ast.literal_eval(rh.getPostData('project_list'))
	response = response_code.internalError()
	status_list = []
	try:
		for mapping_element in project_list:
			user_dao = UserDAO()
			project_enterprise_id = getProjectByProjectId(mapping_element.get("project_id")).project_enterprise_id
			_status = user_dao.update_user_project_map(user_id=user_id,
					enterprise_id=project_enterprise_id,
				    status=mapping_element.get("status"))
			status_list.append({"project_enterprise_id": project_enterprise_id, "status": _status})
		response = response_code.success()
		response["mapping_status"] = status_list
	except Exception as e:
		logger.exception("Failed to update project permissions: {}".format(e))
	return HttpResponse(json.dumps(response), 'content-type=text/json')


@validate_payload(["user_id", "enterprise_id", "mapping_list"])
def user_location_mapping(request):
	"""
	The view is used to update the useer accessible location
	"""
	request_handler = RequestHandler(request=request)
	user_id = request_handler.getData("user_id")
	enterprise_id = request_handler.getData("enterprise_id")
	mapping_list = json.loads(request_handler.getData("mapping_list"))
	response = response_code.internalError()
	status_list = []
	try:
		for mapping_element in mapping_list:
			user_dao = UserDAO()
			_status = user_dao.update_user_location_map(user_id=user_id, location_id=mapping_element.get("location_id"),
														enterprise_id=enterprise_id, status=mapping_element.get("status"))
			status_list.append({"location_id": mapping_element.get("location_id"), "status": _status})
		response = response_code.success()
		response["mapping_status"] = status_list
	except Exception as e:
		logger.info("Failed to create or update the user location map: %s", e)
	return HttpResponse(json.dumps(response), mimetype='application/json', status=response.get("response_code"))

