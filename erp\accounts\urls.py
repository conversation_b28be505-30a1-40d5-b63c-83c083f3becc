"""
"""
from django.conf.urls import patterns, url

from erp.accounts import json_api
from erp.accounts.accounts_views import runTrialBalance, renderAccountsDashboard, createAccountGroup, viewBook, \
	generateStatements, getTopCustomers, gstr1_purchase_report_page, gstr1_sales_report_page, closeBook, reOpenBooks, \
	gstr2_purchase_report_page, gstr3b_reconciliation_page, loadProjects, getSaveMatchedGstr3b, getClearMatchedGstr3b, \
	tcs_report_page, gvbv_report_page, sales_item_wise_report_page, outstanding_report_page
from erp.accounts.brs_views import manageBRS, editBRS, saveBRS, SaveVoucherBRS, CreateVoucherBRS, loadLedgerWiseBRS, \
	LedgerWiseOpening
from erp.accounts.ledger_views import manageLedger, saveLedger, editLedger, deleteLedger, populateLedgerChoices, \
	populateLedgerList, generatePaymentSummaryDocument, loadVoucherBillDetails, update_ledger_status
from erp.accounts.voucher_views import manageVoucher, saveVoucher, editVoucher, deleteVoucher, load_voucher_no, \
	loadLedger, loadLedgerBillDetails, loadBankLedger, loadCashLedger, gstr1PurchaseReportTbl, \
	getLedgerOpening, importVoucher, managePayment, salary_voucher, manageVoucherList, checkLedgerBillDetails, populateVoucherList, \
	getVoucherLogList, getVoucherLogData, gstr1B2BSeriesReportTbl, gstr1HSNReportTbl, \
	gstr1CDNRReportTbl, gstr1CDNURReportTbl, gstr1EXEMPReportTbl, gstr2HSNReportTbl, gstr2EXEMPReportTbl, \
	gstr2IMPGReportTbl, gstr2B2BReportTbl, gstr2B2BURCReportTbl, gstr1EXPReportTbl, gstr2CDNRReportTbl, \
	gstr2CDNURReportTbl, \
	gstr3bReconciliation, gstr2IMPSReportTbl, gstr3bSaveGSTUsername, tcsReportTbl, gvandbvReportTbl, \
	salesItemWiseReportTbl, outstandingReportTbl, salary_voucher

__author__ = 'kalaivanan'

urlpatterns = patterns(
	'',
	# HTML responses
	url('trial_balance/$', runTrialBalance),
	url('statements/', generateStatements),
	url('close_book/', closeBook),
	url('view_book/', viewBook),
	url('reopen_books/', reOpenBooks),
	url('home/$', renderAccountsDashboard),
	url('ledger/$', manageLedger),
	url('ledger/populate_ledger_lists/$', populateLedgerList),
	url('ledger/save/$', saveLedger),
	url('ledger/edit/$', editLedger),
	url('ledger/status_update/$', update_ledger_status),
	url('ledger/delete/$', deleteLedger),
	url('add_group/$', createAccountGroup),

	# GSTR1 purchase report page
	url('gstr1-purchase-report-statement/$', gstr1_purchase_report_page),
	url('gstr2-purchase-report-statement/$', gstr2_purchase_report_page),

	# GSTR1 sales report page
	url('gstr1-sales-report-statement/$', gstr1_sales_report_page),

	# GSTR-3B Reconciliation page
	url('gstr3b-reconciliation-report-statement/$', gstr3b_reconciliation_page),

	# Monthly Reports
	url('tcs_report/$', tcs_report_page),
	url('json/tcs_report_json/$', tcsReportTbl),

	url('gvbv_report/$', gvbv_report_page),
	url('json/gvbv_report_json/$', gvandbvReportTbl),

	url('sales_item_wise_report/$', sales_item_wise_report_page),
	url('json/sales_item_wise_report_json/$', salesItemWiseReportTbl),

	url('outstanding_report/$', outstanding_report_page),
	url('json/outstanding_report_json/$', outstandingReportTbl),

	url('voucher/$', manageVoucher),
	url('voucher/list/$', manageVoucherList),
	url('voucher/populate_voucher_lists/$', populateVoucherList),
	url('voucher/save/$', saveVoucher),
	url('voucher/edit/$', editVoucher),
	url('voucher/edit/#table_tab2$', editVoucher),
	url('voucher/delete/$', deleteVoucher),
	url('voucher/maxno/(\w+)/$', load_voucher_no),
	url('voucher/import/$', importVoucher),
	url('payments/$', managePayment),
	url('brs/$', manageBRS),
	url('brs/edit/$', editBRS),
	url('brs/save/$', saveBRS),

	# Changelog responses
	url('voucher/getloglist/$', getVoucherLogList),
	url('voucher/getlogdata/$', getVoucherLogData),

	# JSON responses
	url('json/voucher/loadcashledgers/$', loadCashLedger),
	url('json/voucher/loadbankledgers/$', loadBankLedger),
	url('json/voucher/loadledgerdetails/$', loadLedger),
	url('json/voucher/loadledgerbilldetails/$', loadLedgerBillDetails),
	url('json/voucher/get_ledger_opening/$', getLedgerOpening),
	url('json/voucher/check_bill_details/$', checkLedgerBillDetails),

	url('json/dashboard_api/$', json_api.getDashboardEntries),
	url('json/get_cash_balance/$', json_api.getCashBalance),
	url('json/get_cash_in_hand/$', json_api.getCashBalanceList),
	url('json/get_bank_balance/$', json_api.getCashBalanceList),
	url('json/get_sales_revenue/$', json_api.getSalesRevenue),
	url('json/get_sales_revenue_total/$', json_api.getSalesRevenueTotal),
	url('json/get_receivable_aging_details/$', json_api.getReceivableAgingDetails),
	url('json/get_payable_aging_details/$', json_api.getPayableAgingDetails),
	url('json/tax_liability/$', json_api.getTaxLiability),
	url('json/aging/$', json_api.getAging),
	url('json/generate_payment_summary/$', generatePaymentSummaryDocument),
	url('json/load_voucher_bill_details/$', loadVoucherBillDetails),
	url('json/ledger_names/', json_api.getAllLedgerNames),
	url('json/ledger_data/$', json_api.get_ledger_data),
	url('json/ledger_aging/$', json_api.getLedgerAging),
	url('json/aging_by_totaldue/$', json_api.getAgingByTotalDue),
	url('json/load_ledger_bills/$', json_api.getLedgerBills),
	url('json/make_payment_to_bills/$', json_api.makePaymentToBills),
	url('json/aging_ledgers/$', json_api.getAgingLedgers),
	url('json/aging_ledgers_by_detail/$', json_api.getAgingLedgersByDetail),
	url('json/gstr1purchasereport/$', gstr1PurchaseReportTbl),
	url('json/gstr2_hsn_report/$', gstr2HSNReportTbl),
	url('json/gstr2_exemp_report/$', gstr2EXEMPReportTbl),
	url('json/gstr2_impg_report/$', gstr2IMPGReportTbl),
	url('json/gstr2_imps_report/$', gstr2IMPSReportTbl),
	url('json/gstr2_b2b_report/$', gstr2B2BReportTbl),
	url('json/gstr2_b2burc_report/$', gstr2B2BURCReportTbl),
	url('json/gstr2_cdnr_report/$', gstr2CDNRReportTbl),
	url('json/gstr2_cdnur_report/$', gstr2CDNURReportTbl),
	url('json/gstr1_b2b_report/$', gstr1B2BSeriesReportTbl),
	url('json/gstr1_b2cl_report/$', gstr1B2BSeriesReportTbl),
	url('json/gstr1_b2cs_report/$', gstr1B2BSeriesReportTbl),
	url('json/gstr1_hsn_report/$', gstr1HSNReportTbl),
	url('json/gstr1_cdnr_report/$', gstr1CDNRReportTbl),
	url('json/gstr1_cdnur_report/$', gstr1CDNURReportTbl),
	url('json/gstr1_exp_report/$', gstr1EXPReportTbl),
	url('json/gstr1_exemp_report/$', gstr1EXEMPReportTbl),
	url('json/top_customers/$', getTopCustomers),
	url('json/gstr3_save_matched/$', getSaveMatchedGstr3b),
	url('json/gstr3_clear_matched/$', getClearMatchedGstr3b),
	url('json/income_and_expenses/$', json_api.getIncomeAndExpenses),
	url('json/ledger/populate_choices/$', populateLedgerChoices),
	url('json/gstr3b_reconciliation/', gstr3bReconciliation),
	url('json/gstr3b_save_gst_username/', gstr3bSaveGSTUsername),
	url('json/populate_account_group_tree/$', json_api.renderAccountGroupOptionTree),
	url('json/brs/ledgerdata/$', loadLedgerWiseBRS),
	url('json/brs/ledgeropening/$', LedgerWiseOpening),
	url('json/brs/brsvouchersave/$', SaveVoucherBRS),
	url('json/brs/brsvouchercreate/$', CreateVoucherBRS),
	url('json/super_edit_voucher_code/', json_api.superEditVoucherCode),
	url('json/check_is_billable/', json_api.checkIsBillable),
	url('json/load_projects/$', loadProjects),
	url('json/salary_voucher_bulk_upload/$', salary_voucher),
)
