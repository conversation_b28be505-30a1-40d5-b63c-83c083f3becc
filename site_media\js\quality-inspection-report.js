function openQuantityInspectionReport(current){
	var item_id = $(current).closest("tr").find('.item_id').val();
    if(!Number(item_id)) {
        swal("Sorry!", "You can't add <b>Quality Inspection Report</b> for a non-stock material.", "warning");
        return;
    }
    $(".inspection_reports-content").empty();
    var pageType = $("#id_dc_type").val();
    if (["dc", "internal", "sales"].indexOf(pageType) != -1) {
        $("#current-editable-id").val($(current).closest("tr").attr("id"));
        if($(current).closest("tr").find('.item_description').find("input").length > 0) {
            var title = $(current).closest("tr").find('.item_description').find("input").val();
        }
        else {
            var title = $(current).closest("tr").find('.item_description').text();
        }
        var unit_name = $(current).closest("tr").find('.td-unit-id').find("input").val();
    }
    else {
        $("#current-editable-id").val($(current).closest("tr").attr("data-row-id"));
        var title = $(current).closest("tr").find('.item_description').val();
        var unit_name = $(current).closest("tr").find('.hidden-unit-name').val()
    }


    
    var currentJSONHeader = $(current).closest("tr").find("input.quantity_inspection_headers").val();
    var currentJSONReview = $(current).closest("tr").find("input.quantity_inspection_review").val();

    if(currentJSONHeader != "") {
    	var headerResponse = JSON.parse(currentJSONHeader.length > 0 ? currentJSONHeader : '[]');
    	constructQIRTableHeader(headerResponse, current);
    }
    else {
    	$.ajax({
            url: "/erp/masters/json/materials/get_specifications/",
            type: "POST",
            datatype: "json",
            async: false,
            data : {'item_id': item_id},
            success: function (response){
                 if (response.response_message == "Session Timeout") {
                    location.reload();
                    return;
                }
		        constructQIRTableHeader(response, current);
		        $(current).closest("tr").find("input.quantity_inspection_headers").val(JSON.stringify(response));
            }
        });
    }
	var inspectionReview = JSON.parse(currentJSONReview.length > 0 ? currentJSONReview : '[]')
	constructQIRTable(inspectionReview, current);
    $("#quality_inspection_report").find(".modal-title").html('Quality Inspection Report - <small>' +title+'</small>');
    $("#quality_inspection_report").modal("show");
    $("#quality_inspection_report").find(".item_unit_id").html('Qty <small>(' + unit_name +')</small>');
}

function constructQIRTableHeader(response, current) {
    $(".inspection_reports-header, .quality-inspection-headers, .inspection_reports-parameter").empty();
    specifications = response.specifications;
    if(response.qc_method) {
        $(".qc_method_container").closest("div").removeClass("hide");
        $(".qc_method_container").text(response.qc_method);
    }
    else {
        $(".qc_method_container").closest("div").addClass("hide");
    }

    if(response.reaction_plan) {
        $(".reaction_plan_container").closest("div").removeClass("hide");
        $(".reaction_plan_container").text(response.reaction_plan);
    }
    else {
        $(".reaction_plan_container").closest("div").addClass("hide");
    }
    
	if(specifications.length > 0) {
    	var row = `<tr class='quality-inspection-headers'><th colspan='2'>Parameter</th>`;
    	var row1 = `<tr class='quality-inspection-headers'><td colspan='2' class='text-right'><b>Instruction</b></td>`;
    	var row2 = `<tr class='quality-inspection-headers tr-ref-value'><td colspan='2' class='text-right'><b>Ref Value</b></td>`;
    	var row3 = `<tr class='quality-inspection-headers'><td colspan='2' class='text-right'><b>Tool/ Method</b></td>`;
    	var row4 = `<tr class='quality-inspection-headers'><td colspan='2' class='text-right'><b>Reaction Plan</b></td>`;
    	$.each(specifications, function (i, item) {
            var quantityRange = "";
            if(item.min_value != "" && item.max_value != "") {
                quantityRange = item.min_value +" - "+ item.max_value;
            }
            else if(item.min_value == "" && item.max_value != "") {
                quantityRange = " <= "+ item.max_value;
            }
            else if(item.min_value != "" && item.max_value == "") {
                quantityRange = " >= "+ item.min_value;
            }
    		row += `<th class='parameter-name'>
                        ${item.parameter}
                        ${item.qc_critical ? "<span class='star-mark'></span>" : ''}
                    </th>`
    		row1 += `<td>${item.instruction}</td>`
    		row2 += `<td>${quantityRange} ${item.unit}</td>`
    		row3 += `<td>${item.tool}</td>`
    		row4 += `<td>${item.reaction_plan}</td>`;
    	});

    	row += `<th></th></tr>`;
    	row1 += `<td rowspan="4"></td></tr>`;
    	row2 += `</tr>`;
    	row3 += `</tr>`;
    	row4 += `</tr>`;

    	$("#quality_inspection_report_table .inspection_reports-header").html(row);
    	$("#quality_inspection_report_table .inspection_reports-parameter").html(`${row1}${row2}${row3}${row4}`);
    }
    var paramCount = $(".tr-ref-value td").length;
    if(paramCount > 0) {
		$(".qir-colspan").attr("colspan", paramCount-1)
		$(".qir-colspan").removeClass("hide");
        $(".label-qc-critical").removeClass("hide");
	}
	else {
		$(".qir-colspan").addClass("hide");
        $(".label-qc-critical").addClass("hide");
	}
}

function constructQIRTable(response, current) {
	if(response.length > 0) {
    	$.each(response, function (i, item) {
    		var row = `<tr>
    					<td>
    						<i class="fa fa-trash delete-inspection-icon" aria-hidden="true" onclick='deleteInspectionReportRow(this)'></i>
    						<input type="text" class="form-control text-center inspection-sample-id" maxlength="5" value="${item.sample_no}"
                            onfocus="setNumberRangeOnFocus(this,5,0,true)">
						</td>
    					<td>
                            <input type="text" class="form-control text-right inspection-qty" value="${item.quantity}" maxlength="16"
                            onfocus="setNumberRangeOnFocus(this,12,3)">
                        </td>`;
                        $(".parameter-name").each(function(){
                            var paramName = $(this).text().trim();
                            if (typeof(item.observations[paramName]) === "undefined"){
                                row += `<td><input type="text" class="form-control" value=""></td>`; 
                            }
                			else if (typeof(item.observations[paramName]) === "boolean"){
                                var isChecked = "";
                                if(item.observations[paramName]) {
                                    isChecked = "checked"
                                }
                				row += `<td class='text-center'><input type="checkbox" class="chkcase" ${isChecked}></td>`;
                			}
                			else {
                				row += `<td><input type="text" class="form-control" maxlength="15" value="${item.observations[paramName]}"></td>`;	
                			}
                        });
            row += `<td><input type="text" class="form-control inspection-remarks" maxlength="150" value="${item.remarks}"></td></tr>`;
    		$(".inspection_reports-content").append(row)
    	});
    }
    else {
    	var row = `<tr>
						<td>
							<i class="fa fa-trash delete-inspection-icon" aria-hidden="true" onclick='deleteInspectionReportRow(this)'></i>
							<input type="text" class="form-control text-center inspection-sample-id" maxlength="5" value="1"
                            onfocus="setNumberRangeOnFocus(this,5,0,true)" />
						</td>
						<td>
                            <input type="text" class="form-control text-right inspection-qty" maxlength="16"
                            onfocus="setNumberRangeOnFocus(this,12,3)">
                        </td>`;

						$(".tr-ref-value td").each(function(i){
							if(i == 0) return;
							if($(this).text().trim() == "" || $(this).text().trim() == "-") {
								row += `<td class="text-center">
									<input type="checkbox" class="chkcase" value="">
								</td>`;
							}
							else {
								row += `<td><input type="text" class="form-control" maxlength="15"></td>`;
							}
						});
					row += `<td><input type="text" class="form-control inspection-remarks" maxlength="150" value=""></td></tr>`;
		$(".inspection_reports-content").append(row);
    }
}

function addQualityInspection(){
	var serialNumber = [0];
	var largestValue = 0;
	$("#quality_inspection_report").find(".inspection_reports-content tr").each(function(){
		if(Number($(this).find(".inspection-sample-id").val()) > largestValue ) {
			largestValue = Number($(this).find(".inspection-sample-id").val());
		}
		serialNumber.push(Number($(this).find(".inspection-sample-id").val()));
	});
    var uniqueSerialNumber = [];
    $.each(serialNumber, function(i, el){
        if($.inArray(el, uniqueSerialNumber) === -1) uniqueSerialNumber.push(el);
    });
	var missingSerialNumber = missingSeries(uniqueSerialNumber)
	if(missingSerialNumber == undefined || missingSerialNumber == 'undefined') {
		missingSerialNumber = largestValue + 1
	}
	
	var row = `	<tr>
					<td>
						<i class="fa fa-trash delete-inspection-icon" aria-hidden="true" onclick='deleteInspectionReportRow(this)'></i>
						<input type="text" class="form-control text-center inspection-sample-id" maxlength="5" value="${missingSerialNumber}"
                        onfocus="setNumberRangeOnFocus(this,5,0,true)" />
					</td>
					<td>
                        <input type="text" class="form-control text-right inspection-qty" maxlength="16"
                        onfocus="setNumberRangeOnFocus(this,12,3)" />
                    </td>`;

					$(".tr-ref-value td").each(function(i){
						if(i == 0) return
						if($(this).text().trim() == "" || $(this).text().trim() == "-") {
							row += `<td class="text-center">
								<input type="checkbox" class="chkcase" value="">
							</td>`;
						}
						else {
							row += `<td><input type="text" class="form-control" maxlength="15"></td>`;
						}
					});
				row += `<td><input type="text" class="form-control inspection-remarks" maxlength="150" value=""></td></tr>`;
	$(".inspection_reports-content").append(row);
}

function updateQualityInspection(){
	var inspectionHeader = {};
	var inspections = [];
	
    var paramCount = $(".quality-inspection-headers").find("th").length;
    var serialNumber = [];
    $(".inspection_reports-content tr").each(function(index, tr) {
        if($(this).find(".inspection-sample-id").val().trim() != "") {
            serialNumber.push($(this).find(".inspection-sample-id").val());
        }
    });
    if((new Set(serialNumber)).size !== serialNumber.length) {
        swal("", "#ID seems to be non unique.<br />Please change those Sample ID before Closing.", "warning")
        return;
    }
    $(".inspection_reports-content tr").each(function(index, tr) {
    	var inspectionItems = {}; 
    	var observations = {};
    	var current = $(this);
    	if(current.find(".inspection-sample-id").val().trim() == "" || current.find(".inspection-qty").val().trim() <= 0) {
    		$(this).remove();
    		return;
    	}
    	inspectionItems["sample_no"] = current.find(".inspection-sample-id").val();
	    inspectionItems["quantity"] = current.find(".inspection-qty").val();
	    inspectionItems["remarks"] = current.find(".inspection-remarks").val();

    	for (var i = 3; i <= paramCount; i++) { 
            var paramName = $(".quality-inspection-headers").find(`th:nth-child(${i-1})`).text().trim()
    		if (current.find(`td:nth-child(${i})`).find("input").is('input:text')) {
    			observations[paramName] = current.find(`td:nth-child(${i})`).find("input").val();
    		}
    		else {
    			observations[paramName] = current.find(`td:nth-child(${i})`).find("input").is(":checked");
    		}
	    } 
	    inspectionItems["observations"] = observations; 
	    inspections.push(inspectionItems)
    });
    var currentRowId = $("#current-editable-id").val();
    var pageType = $("#id_dc_type").val();
    if (["dc", "internal", "sales"].indexOf(pageType) != -1) {
        var currentRow = $(".quality-inpection-table").find(`tr#${currentRowId}`);
        isFormChanged = true;
    }
    else {
        var currentRow = $(".quality-inpection-table").find(`tr[data-row-id="${currentRowId}"]`);
    }
	
    $(currentRow).find("input.quantity_inspection_review").val(JSON.stringify(inspections));
    $("#quality_inspection_report").modal("hide");
}

function deleteInspectionReportRow(current) {
	swal({
        title: "Are you sure!",
        text: "Do you want to delete this Row?",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#209be1",
        confirmButtonText: "Yes, do it!",
        closeOnConfirm: true,
        closeOnCancel: true
    },
    function(){
        $(current).closest("tr").remove();
    });
}

function missingSeries(nums){
    const numberArray = nums.sort((num1, num2)=>{
      	return num1 - num2;
   	});
   	for (let i=0; i < numberArray.length; i++){
      	if(i !== numberArray[i]){
        	return i;
      	}
   	}
}