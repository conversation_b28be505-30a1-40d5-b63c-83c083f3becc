"""
"""

from erp import logger
from erp.formsets import TagFormset
from erp.models import Tag
from settings import SQLASession
from util.helper import constructFormInitializer

__author__ = 'kalaivanan'

TAG_FORM_PREFIX = "tag"


def getPersistentTag(enterprise_id=None, tag_string=None, db_session=None):
	"""
	Returns a Persistent Tag attached to the db_session mentioned

	:param enterprise_id:
	:param tag_string: tag string Not null expected
	:param db_session:
	:return:
	"""
	try:
		if not tag_string or len(tag_string) == 0:
			return None
		tag = db_session.query(Tag).filter(Tag.enterprise_id == enterprise_id, Tag.tag == tag_string).first()
		if not tag:
			logger.info("Including new tag: %s for enterprise: %s" % (tag, enterprise_id))
			tag = Tag(tag=tag_string, enterprise_id=enterprise_id)
			db_session.add(tag)
			db_session.flush([tag])
			db_session.refresh(tag)
		return tag
	except:
		logger.error("Could not get a Persistent tag!")
		raise


def getEntityTagMaps(enterprise_id=None, tags=(), tag_map_class=None, db_session=None):
	"""

	:param enterprise_id:
	:param tags:
	:param tag_map_class:
	:param db_session:
	:return:
	"""
	try:
		db_session = db_session if db_session else SQLASession()
		mapped_tags = []
		for tag_string in tags:
			tag = getPersistentTag(enterprise_id=enterprise_id, tag_string=tag_string, db_session=db_session)
			mapped_tag = tag_map_class(enterprise_id=enterprise_id)
			mapped_tag.tag = tag
			mapped_tags.append(mapped_tag)
		return mapped_tags
	except:
		raise


def extractTagsFromFormset(tag_formset=TagFormset()):
	"""

	:param tag_formset:
	:return:
	"""
	try:
		tags = []
		for tag_form in tag_formset:
			if tag_form.is_valid():
				logger.debug("Tag Form: %s\nCleaned Data: %s" % (tag_form.as_p(), tag_form.cleaned_data))
				tag_string = tag_form.cleaned_data['tag'].strip()
				if not tag_form.cleaned_data['DELETE'] and tag_string not in tags:
					tags.append(tag_string)
		return tags
	except:
		logger.error("Could not fetch tags from tag formset")
		raise


def extractEntityTagMapsFromFormset(tag_formset=TagFormset(), enterprise_id=None, map_class=None, db_session=None):
	"""
	
	:param tag_formset: 
	:param enterprise_id: 
	:param map_class: 
	:param  db_session:
	:return: 
	"""
	try:
		entity_tags = getEntityTagMaps(
			enterprise_id=enterprise_id, tags=extractTagsFromFormset(tag_formset=tag_formset), tag_map_class=map_class,
			db_session=db_session)
		logger.info("Tags to be appended: %s" % len(entity_tags))
		return entity_tags
	except:
		logger.error("Failed to extract Entity Tag Maps From Formset!")
		raise


def generateTagFormset(request_data=None, tags=(), prefix=TAG_FORM_PREFIX):
	"""

	:param request_data:
	:param tags: 
	:param prefix: 
	:return: 
	"""
	if request_data:
		return TagFormset(data=request_data, prefix=prefix)
	else:
		# By adding log statement is working as expected DO NOT REMOVE LOG
		logger.debug("Tags :%s" % [tag.tag for tag in tags])
		return TagFormset(initial=[constructFormInitializer(tag) for tag in tags], prefix=prefix)


def populateTags(enterprise_id=None):
	if enterprise_id:
		return SQLASession().query(Tag.tag).filter(Tag.enterprise_id == enterprise_id).order_by(Tag.tag).all()
	return SQLASession().query(Tag.tag, Tag.id).group_by(Tag.tag).order_by(Tag.tag).all()
