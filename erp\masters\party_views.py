"""

"""
import ast
import re
from datetime import datetime
from decimal import Decimal

import simplejson
import xlrd
from django.http import HttpResponse
from django.template.response import TemplateResponse

from erp.accounts import PURCHASE_ACCOUNT_GROUP_ID, SALES_ACCOUNT_GROUP_ID
from erp.accounts.backend import AccountsDAO
from erp.auth import USER_IN_SESSION_KEY, SESSION_KEY, ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.dao import executeQuery
from erp.helper import validateFileHeaders, populatePartyChoices, getStateList
from erp.masters import logger, MANDATORY_GST_CATEGORY_LIST, GST_CATEGORY_LIST
from erp.masters.backend import createPartyLedgers, MasterService
from erp.models import Module, Party, Currency, Voucher, VoucherParticulars, Ledger, Enterprise
from erp.properties import TEMPLATE_TITLE_KEY
from settings import SQLASession
from util.helper import getFYStartDate, getFinancialYear

ACTION_MODULES = SQLASession().query(Module).order_by(Module.action)

DATA_BLOCK_SEPARATOR = "%2C%40%2C"  # ",@,"
DATA_SEPARATOR = "%5B%3A%3A%5D"  # "[::]"
FIELD_SEPARATOR = "%5B%3A%5D"  # "[:]"
COMMA_SEPARATOR = "%2C"  # ","


def manageParties(request):
	request_handler = RequestHandler(request)
	service = MasterService()
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	enterprise = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise
	gst_category = service.getGSTCategory()
	countries = service.getCountries()
	if request_handler.getPostData('is_inline_edit') and request_handler.getPostData('is_inline_edit') == "true":
		is_inline_edit = True
	else:
		is_inline_edit = False
	agent = 'Others'
	gst_category_list = []
	gst_country_list = []
	try:
		for category in gst_category:
			gst_category_list.append({"category_id": category.id, "category_name": category.name, "category_desc": category.description})

		for country in countries:
			gst_country_list.append({"country_code": country.code, "country_name": country.name})

		currency = service.master_dao.db_session.query(Currency).order_by(Currency.code).all()
		query_all_enterprise_parties = """
			SELECT party_code AS code, party_name AS name, address1 AS address_1, address2 AS address_2, 
			party_master.party_id AS party_id, city, state, pin_code, config_flags, payment_credit_days, 
			receipt_credit_days, currency, IFNULL(contact_details.name, '') AS primary_contact_name, 
			IFNULL(party_master.port, '') AS port, IFNULL(contact_details.phone_no, '') AS primary_phone_no, 
			IFNULL(contact_details.email, '') AS primary_email, IFNULL(contact_details.fax_no, '') AS primary_fax_no,
			IFNULL(party_master.country_code, '') AS country_code, party_master.category_id AS gst_category_id, 
			IFNULL(gst_detail.details, '') AS gstin, IFNULL(pan_detail.details, '') AS pan 
			FROM party_master
			LEFT JOIN party_contact_map ON party_contact_map.party_id = party_master.party_id 
				AND party_contact_map.enterprise_id = party_master.enterprise_id 
				AND party_contact_map.sequence_id = 1 
			LEFT JOIN contact_details ON party_contact_map.contact_id = contact_details.id 
			LEFT JOIN party_registration_detail as gst_detail ON gst_detail.enterprise_id =  '{enterprise_id}' 
				AND gst_detail.party_id = party_master.party_id AND gst_detail.label = 'GSTIN' 
			LEFT JOIN party_registration_detail as pan_detail ON pan_detail.enterprise_id =  '{enterprise_id}' 
				AND pan_detail.party_id = party_master.party_id AND pan_detail.label = 'PAN' 
			WHERE party_master.enterprise_id='{enterprise_id}'""".format(enterprise_id=enterprise_id)

		party_list = executeQuery(query_all_enterprise_parties, as_dict=True)
		if 'Windows' in request.META['HTTP_USER_AGENT']:
			agent = 'Windows'
	except Exception as e:
		currency = []
		logger.exception(e.message)
		party_list = ""
	if is_inline_edit is True:
		return HttpResponse(content=simplejson.dumps(party_list), mimetype='application/json')
	else:
		return TemplateResponse(template='masters/party_list.html', request=request, context={
			'fieldlist': party_list, 'user': str(request.session['USERNAME']).upper(), 'agent': agent,
			'currency': currency, 'home_currency': enterprise.home_currency_id, 'gst_category_list': gst_category_list,
			'gst_country_list': gst_country_list, 'state_list': getStateList(), 'gstin_value': "", 'pan_value': "", TEMPLATE_TITLE_KEY: "Party"})


def loadPartyDetails(request):
	request_handler = RequestHandler(request)
	service = MasterService()
	party_id = request_handler.getPostData("party_id")
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	gst_category = service.getGSTCategory()
	countries = service.getCountries()
	party_registration_detail = service.getPartyRegistrationDetails(enterprise_id=enterprise_id, party_id=party_id)
	gst_category_list = []
	gst_country_list = []
	gstin_value = ""
	pan_value = ""
	party_reg_detail = []
	try:
		currency = service.master_dao.db_session.query(Currency).order_by(Currency.code).all()
		party_details = service.master_dao.db_session.query(Party).filter(Party.enterprise_id == enterprise_id, Party.id == party_id).first()
		party_contact_details = service.getContactDetails(
				enterprise_id=enterprise_id, party_id=party_id)

		logger.info("Loaded details for %s Party" % party_id)
		for category in gst_category:
			gst_category_list.append({"category_id": category.id, "category_name": category.name, "category_desc": category.description})

		for country in countries:
			gst_country_list.append({"country_code": country.code, "country_name": country.name})

		for item in party_registration_detail:
			if item.label == "GSTIN":
				gstin_value = item.details
			elif item.label == "PAN":
				pan_value = item.details
			else:
				party_reg_detail_dict = {"label_id": item.label_id, "label": item.label, "details": item.details}
				party_reg_detail.append(party_reg_detail_dict)
	except Exception as e:
		logger.exception(e)
		party_details = []
		currency = []
		party_contact_details = []
	return TemplateResponse(template='masters/party.html', request=request, context={
		'party_details': party_details, 'party_contact_details': party_contact_details,
		'gst_category_list': gst_category_list, 'gst_country_list': gst_country_list, 'state_list': getStateList(), 'gstin_value': gstin_value,
		'pan_value': pan_value, 'party_reg_detail': party_reg_detail, TEMPLATE_TITLE_KEY: "Party", "currency": currency})


def loadPartyOptions(request):
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	return HttpResponse(content=simplejson.dumps(
		populatePartyChoices(enterprise_id=enterprise_id, populate_all=False)), mimetype='application/json')


def saveParty(request):
	request_handler = RequestHandler(request)
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	party_details = simplejson.loads(request_handler.getPostData('party_details'))
	party_contact_details = simplejson.loads(request_handler.getPostData('party_contact_details'))
	party_reg_detail = simplejson.loads(request_handler.getPostData('party_reg_details'))
	db_session = SQLASession()
	master_service = MasterService()
	db_session.begin(subtransactions=True)
	payment_credit_days = 0
	receipt_credit_days = 0
	try:
		if party_details["payment_credit_days"] and party_details["payment_credit_days"] != "":
			payment_credit_days = party_details["payment_credit_days"]
		if party_details["receipt_credit_days"] and party_details["receipt_credit_days"] != "":
			receipt_credit_days = party_details["receipt_credit_days"]
		party_to_be_created = Party(
			code=party_details["code"].upper(), name=party_details["name"], address_1=party_details["address_1"],
			address_2=party_details["address_2"], city=party_details["city"], state=party_details["state"],
			config_flags=party_details["config_flags"],	enterprise_id=enterprise_id, pin_code=party_details["pin_code"],
			payment_credit_days=payment_credit_days, country_code=party_details["country_code"],
			receipt_credit_days=receipt_credit_days, category_id=party_details["category_id"],
			currency=party_details["currency"], port=party_details["port"])
		db_session.add(party_to_be_created)
		createPartyLedgers(
			party=party_to_be_created, user_id=user_id, db_session=db_session, is_customer=party_to_be_created.isCustomer(),
			is_supplier=party_to_be_created.isSupplier())
		db_session.commit()
		db_session.begin(subtransactions=True)
		party = db_session.query(Party).filter(
			Party.code == party_to_be_created.code, Party.name == party_to_be_created.name,
			Party.enterprise_id == enterprise_id).first()
		primary_contact_id = master_service.savePartyContactDetails(
			enterprise_id=enterprise_id, party_contact_details=party_contact_details, user_id=user_id, party_id=party.id)
		party.primary_contact_id = primary_contact_id
		db_session.commit()
		master_service.savePartyRegistration(
			enterprise_id=enterprise_id, party_id=party.id, party_reg_detail=party_reg_detail, user_id=user_id)
		party_data_dump = {
			"message": 'Party Added Successfully',
			"id": party.id,
			"name": party.name,
			"config_flags": party.config_flags,}
		json = simplejson.dumps(party_data_dump)
	
	except Exception as e:
		db_session.rollback()
		logger.exception(e)
		party_exception_dump = {"message": str(e)}
		json = simplejson.dumps(party_exception_dump)
	return HttpResponse(content=json, mimetype='application/json')


def deleteParty(request):
	request_handler = RequestHandler(request)
	party_id = request_handler.getPostData('party_id')
	db_session = SQLASession()
	db_session.begin(subtransactions=True)
	try:
		db_session.query(Party).filter_by(id=party_id).delete()
		json = simplejson.dumps(0)
		db_session.commit()
	except Exception as e:
		db_session.rollback()
		logger.exception(e)
		json = simplejson.dumps(1)
	return HttpResponse(content=json, mimetype='application/json')


def updateParty(request):
	"""

	:param request:
	:return:
	"""
	db_session = SQLASession()
	master_service = MasterService()
	db_session.begin(subtransactions=True)
	try:
		request_handler = RequestHandler(request)
		party_details = simplejson.loads(request_handler.getPostData('party_details'))
		party_contact_details = simplejson.loads(request_handler.getPostData('party_contact_details'))
		inline_update = simplejson.loads(request_handler.getPostData('inline_update'))
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		if inline_update:
			logger.info("Party Details from POST: %s" % party_details)
			db_session.query(Party).filter(Party.id == party_details["party_id"]).update(
				{
					Party.config_flags: party_details["config_flags"],
					Party.payment_credit_days: party_details["payment_credit_days"],
					Party.receipt_credit_days: party_details["receipt_credit_days"]
				}, synchronize_session='fetch')
		else:
			db_session.query(Party).filter(Party.id == party_details["party_id"]).update(
				{
					Party.address_1: party_details["address_1"], Party.address_2: party_details["address_2"],
					Party.city: party_details["city"], Party.state: party_details["state"],
					Party.config_flags: party_details["config_flags"], Party.country_code: party_details["country_code"],
					Party.payment_credit_days: party_details["payment_credit_days"], Party.category_id: party_details["category_id"],
					Party.receipt_credit_days: party_details["receipt_credit_days"],
					Party.name: party_details["name"], Party.currency: party_details["currency"],
					Party.code: party_details["code"].upper(), Party.pin_code: party_details["pin_code"],
					Party.port: party_details["port"]
				}, synchronize_session='fetch')
		party_updated = db_session.query(Party).filter(Party.id == party_details["party_id"]).first()
		createPartyLedgers(
			party=party_updated, user_id=user_id, db_session=db_session, is_customer=party_updated.isCustomer(),
			is_supplier=party_updated.isSupplier())
		primary_contact_id = master_service.savePartyContactDetails(
			enterprise_id=party_updated.enterprise_id, party_contact_details=party_contact_details, user_id=user_id,
			party_id=party_details["party_id"])
		party_updated.primary_contact_id = primary_contact_id
		if not inline_update:
			party_reg_detail = simplejson.loads(request_handler.getPostData('party_reg_details'))
			master_service.savePartyRegistration(
				enterprise_id=party_updated.enterprise_id, party_id=party_details["party_id"], party_reg_detail=party_reg_detail,
				user_id=user_id)
		db_session.commit()
		party_data_dump = {
			"message": 'Party Updated Successfully',
			"id": party_updated.id,
			"name": party_updated.name,
			"config_flags": party_updated.config_flags,
			}
		json = simplejson.dumps(party_data_dump)
	except Exception as e:
		logger.exception(e)
		db_session.rollback()
		party_exception_dump = {"message": str(e)}
		json = simplejson.dumps(party_exception_dump)
	return HttpResponse(content=json, mimetype='application/json')


def importParties(request):
	"""
	Bulk import of Parties either for the first time or for Updating Party details in bulk
	
	:param request:
	:return:
	"""
	
	request_handler = RequestHandler(request)
	user = request_handler.getSessionAttribute(USER_IN_SESSION_KEY)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	party_list = request.POST.getlist('import_party_data[]')
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	if request.FILES:
		bulk_file = request.FILES['bulkfile']  # or self.files['image'] in your form
		bulk_file.seek(0)
		book = xlrd.open_workbook(file_contents=bulk_file.read())
		party_sheet = book.sheet_by_name('party')
		logger.debug('Sheet name: %s' % party_sheet.name)
		party_list = []
		for row_idx in range(1, party_sheet.nrows):  # Iterate through rows
			logger.debug('-' * 40)
			if row_idx == 0:
				party_list.append({})
			else:
				try:
					payment_credit_days = str(int(party_sheet.cell(row_idx, 12).value))
				except:
					payment_credit_days = str(party_sheet.cell(row_idx, 12).value)
				try:
					receipt_credit_days = str(int(party_sheet.cell(row_idx, 13).value))
				except:
					receipt_credit_days = str(party_sheet.cell(row_idx, 13).value)
				dict = {
					'code': str(party_sheet.cell(row_idx, 0).value),
					'name': str(party_sheet.cell(row_idx, 1).value),
					"address_1": str(party_sheet.cell(row_idx, 2).value),
					"address_2": "",
					'city': str(party_sheet.cell(row_idx, 3).value),
					'state': str(party_sheet.cell(row_idx, 4).value),
					'country_code': str(party_sheet.cell(row_idx, 5).value),
					'pin_code': str(party_sheet.cell(row_idx, 6).value),
					'contact': str(party_sheet.cell(row_idx, 7).value),
					'phone': int(round(party_sheet.cell(row_idx, 8).value)) if isinstance(
						party_sheet.cell(row_idx, 8).value, float) else party_sheet.cell(row_idx, 8).value.encode('ascii', 'ignore'),
					'email': str(party_sheet.cell(row_idx, 9).value),
					'fax_no': str(party_sheet.cell(row_idx, 10).value),
					'payment_credit_days': payment_credit_days if payment_credit_days != "" else "0",
					'payable_opening': str(party_sheet.cell(row_idx, 12).value),
					'receipt_credit_days': receipt_credit_days if receipt_credit_days != "" else "0",
					'receivable_opening': str(party_sheet.cell(row_idx, 14).value),
					'as_on': str(party_sheet.cell(row_idx, 15).value),
					'gst_category': str(party_sheet.cell(row_idx, 16).value),
					'port': str(party_sheet.cell(row_idx, 17).value),
					'gstin': str(party_sheet.cell(row_idx, 18).value),
					'pan': str(party_sheet.cell(row_idx, 19).value)}
				logger.debug(dict)
				is_empty = all(value == '' for value in dict.values())
				if not is_empty:
					party_list.append(dict)
		# party_list.append({})
	else:
		is_valid_file_headers = validateFileHeaders(module='party', title_row=ast.literal_eval(party_list[0]))
		if not is_valid_file_headers:
			return HttpResponse(
				content=simplejson.dumps({'message': 'Please upload the valid  file or check proper column names',
				                          'is_valid_file_headers': False}),
				mimetype='application/json')
	failed_items = []
	update_failed_items = []
	added_count = 0
	updated_count = 0
	failed_voucher_count = 0
	message = "No new party imported."
	db_session = SQLASession()
	enterprise = db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
	master_service = MasterService()
	opening_vouchers = {"payables": {}, "receivables": {}}
	try:
		logger.info("Importing Parties...")
		for party_item in party_list[1:]:
			if request.FILES:
				party_item_dict = party_item
			else:
				party_item_dict = ast.literal_eval(party_item)
			party_item_dict['code'] = re.sub('[^a-zA-Z0-9 ]', '_', party_item_dict['code'].strip()).upper()
			party_item_dict['payment_credit_days'] = int(party_item_dict['payment_credit_days']) if party_item_dict[
				'payment_credit_days'].isdigit() else 0
			party_item_dict['receipt_credit_days'] = int(party_item_dict['receipt_credit_days']) if party_item_dict[
				'receipt_credit_days'].isdigit() else 0
			db_session.begin(subtransactions=True)
			party = db_session.query(Party).filter_by(
				code=party_item_dict['code'], name=party_item_dict['name'], enterprise_id=enterprise_id).first()
			is_new = party is None
			party = party if party else Party()
			try:
				validation_errors = __validateImportPartyRow(
					party_item_dict=party_item_dict, user=user, master_service=master_service, enterprise_id=enterprise_id)
				if len(validation_errors) > 0:
					raise Exception(validation_errors)
				party.code = party_item_dict['code']
				party.enterprise_id = enterprise_id
				party.name = re.sub('\^|\}|\{|\/|\;|\:|\<|\>', '', party_item_dict['name'])
				party.address_1 = party_item_dict['address_1']
				party.address_2 = party_item_dict['address_2']
				party.city = party_item_dict['city']
				party.state = party_item_dict['state']
				party.country_code = party_item_dict['country_code']
				party.pin_code = party_item_dict['pin_code']
				party.payment_credit_days = party_item_dict['payment_credit_days']
				party.receipt_credit_days = party_item_dict['receipt_credit_days']
				party.config_flags = party.config_flags | (
					Party._IS_SUPPLIER if party_item_dict['payable_opening'] > 0 else 0)
				party.config_flags = party.config_flags | (
					Party._IS_CUSTOMER if party_item_dict['receivable_opening'] > 0 else 0)
				party.currency = enterprise.home_currency_id
				party.category_id = party_item_dict['gst_category']
				party.port = party_item_dict['port']

				logger.info("%s" % party)
				db_session.add(party)
				db_session.commit()
				db_session.refresh(party)
				db_session.begin(subtransactions=True)
				party_contact_details = [{
					'is_deleted': 0, 'name': party_item_dict['contact'], 'is_whatsapp': 0, 'fax_no': party_item_dict['fax_no'],
					'phone_no': party_item_dict['phone'], 'sequence_id': 1, 'email': party_item_dict['email']}]
				primary_contact_id = master_service.savePartyContactDetails(
					enterprise_id=enterprise_id, party_contact_details=party_contact_details, user_id=user_id,
					party_id=party.id)
				party.primary_contact_id = primary_contact_id
				db_session.commit()
				party_reg_detail = [
					{'is_deleted': 0, "label_id": 1, "label": "GSTIN", "details": party_item_dict['gstin']},
					{'is_deleted': 0, "label_id": 2, "label": "PAN", "details": party_item_dict['pan']}]

				master_service.savePartyRegistration(
					enterprise_id=enterprise_id, party_id=party.id, party_reg_detail=party_reg_detail, user_id=user_id)

				if is_new:
					added_count += 1
					if party_item_dict['payable_opening'] != 0 or party_item_dict['receivable_opening'] != 0:
						opening_vouchers.update(_importPartyOpening(
							party=party, enterprise_id=enterprise_id, user=user, db_session=db_session,
							payable_opening=party_item_dict['payable_opening'], opening_vouchers=opening_vouchers,
							receivable_opening=party_item_dict['receivable_opening'], as_on=party_item_dict['as_on']))
				else:
					updated_count += 1
			except OpeningVoucherConstructionFailure as e:
				logger.exception("Opening Voucher Creation Failed due to %s" % e.message)
				party_item_dict['error'] = "%s %s" % (
					party_item_dict['error'] if 'error' in party_item_dict else "",
					"Opening Voucher Creation Failed due to %s" % e.message)
				failed_voucher_count += 1
				failed_items.append(party_item_dict)
			except Exception as e:
				db_session.rollback()
				party_item_dict['error'] = "%s %s" % (
					party_item_dict['error'] if 'error' in party_item_dict else "", e.message)
				if is_new:
					failed_items.append(party_item_dict)
				else:
					update_failed_items.append(party_item_dict)
				logger.exception("Skipped inserting/updating party {enterprise:%s, party_code:%s} due to: %s" % (
					enterprise_id, party_item_dict['code'], e.message))
		if added_count > 0:
			message = "Successfully imported %s Parties!%s" % (added_count, "" if failed_voucher_count == 0 else (
				"Failed to create %s Opening Vouchers!" % failed_voucher_count))
		if len(failed_items) > 0:
			message = "%s\n%s" % (message, "Failed to import %s Parties:\n" % (len(failed_items)))
		if updated_count > 0:
			message = "%s\n%s" % (message, "Successfully updated %s Party(s)." % updated_count)
		if len(update_failed_items) > 0:
			message = "%s\n%s" % (message, "Failed to update %s Party(s):\n" % (len(update_failed_items)))
		json = str(message)
	except Exception as e:
		logger.exception(e.message)
		json = str("Something went wrong !!" + e.message)
	if request.FILES:
		return {'message': json, 'failed_items': failed_items + update_failed_items}
	else:
		return HttpResponse(
			content=simplejson.dumps(
				{'message': json, 'failed_items': failed_items + update_failed_items, 'is_valid_file_headers': True}),
			mimetype='application/json')


def __validateImportPartyRow(party_item_dict={}, user=None, master_service=None, enterprise_id=None):
	errors = ""
	party_item_dict['payable_opening'] = party_item_dict['payable_opening'] if party_item_dict['payable_opening'] else 0
	party_item_dict['receivable_opening'] = party_item_dict['receivable_opening'] if party_item_dict['receivable_opening'] else 0
	if len(party_item_dict['code']) > 10:
		errors = "%s %s" % (errors, "Code cannot be more than 10 characters long!")
	if len(party_item_dict['name']) == 0:
		errors = "%s %s" % (errors, "Name cannot be empty!")
	if len(party_item_dict['state']) == 0:
		errors = "%s %s" % (errors, "State cannot be empty!")	
	if not float(party_item_dict['gst_category']) > int(float(party_item_dict['gst_category'])):
		if int(float(party_item_dict['gst_category'])) in GST_CATEGORY_LIST:
			if int(float(party_item_dict['gst_category'])) in MANDATORY_GST_CATEGORY_LIST and party_item_dict['gstin'] == "":
				errors = "%s %s" % (errors, "GSTIN cannot be empty!")
		else:
			errors = "%s %s" % (errors, "Invalid GST Category!")
	else:
		errors = "%s %s" % (errors, "Invalid GST Category!")
	country = master_service.getCountryByCode(country_code=party_item_dict['country_code'])
	enterprise = master_service.getEnterprise(enterprise_id=enterprise_id)
	if country is not None:
		if (
				party_item_dict['country_code'] != 'IN' and party_item_dict['gst_category'] != '5') or (
				party_item_dict['country_code'] == 'IN' and party_item_dict['gst_category'] == '5'):
			errors = "%s %s" % (errors, "GST category is not consistent with the selected country")
	else:
		errors = "%s %s" % (errors, "Country code does not exist!")
	if party_item_dict['payable_opening'] != 0 or party_item_dict['receivable_opening'] != 0:
		if party_item_dict['as_on'] and len(party_item_dict['as_on']) > 0:
			try:
				datetime.strptime(party_item_dict['as_on'], "%Y-%m-%d")
			except ValueError as e:
				errors = "%s Opening Date - %s!" % (errors, e.message)
		else:
			party_item_dict['as_on'] = datetime.strftime(
				getFYStartDate(fy_start_day=enterprise.fy_start_day), "%Y-%m-%d")
	try:
		party_item_dict['payable_opening'] = float(party_item_dict['payable_opening'])
	except ValueError as e:
		errors = "%s %s" % (errors, "Payable Opening - %s!" % e.message)
	try:
		party_item_dict['receivable_opening'] = float(party_item_dict['receivable_opening'])
	except ValueError as e:
		errors = "%s %s" % (errors, "Receivable Opening - %s!" % e.message)
	return errors


def _importPartyOpening(
		party=None, enterprise_id=None, user=None, payable_opening=0.00, receivable_opening=0.00, as_on=None,
		opening_vouchers={}, db_session=None):
	"""
	
	:param party:
	:param enterprise_id:
	:param user:
	:param payable_opening:
	:param receivable_opening:
	:param as_on:
	:param opening_vouchers:
	:return:
	"""
	db_session.begin(subtransactions=True)
	try:
		createPartyLedgers(
			party=party, user_id=user.user_id, db_session=db_session, is_supplier=(payable_opening != 0),
			is_customer=(receivable_opening != 0))
		_purchase_ledger = db_session.query(Ledger.id).filter(
			Ledger.group_id == PURCHASE_ACCOUNT_GROUP_ID, Ledger.enterprise_id == enterprise_id).first()
		_sale_ledger = db_session.query(Ledger.id).filter(
			Ledger.group_id == SALES_ACCOUNT_GROUP_ID, Ledger.enterprise_id == enterprise_id).first()
		enterprise = db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
		if payable_opening != 0:
			if as_on not in opening_vouchers['payables']:
				db_session.begin(subtransactions=True)
				try:
					fy = getFinancialYear(
						for_date=datetime.strptime(as_on, '%Y-%m-%d'), fy_start_day=enterprise.fy_start_day)
					opening_vouchers['payables'][as_on] = Voucher(
						enterprise_id=enterprise_id, voucher_date=as_on, financial_year=fy, status=1,
						approved_on=datetime.now(), approved_by=user.user_id,
						voucher_no=AccountsDAO().getLatestVoucherNo(
							financial_year=fy, type_id=1, enterprise_id=enterprise_id),
						narration="Payable Openings for Parties imported on %s by %s %s" % (
							datetime.now(), user.first_name, user.last_name), created_by=user.user_id)
					db_session.add(opening_vouchers['payables'][as_on])
					db_session.commit()
					db_session.refresh(opening_vouchers['payables'][as_on])
				except Exception as e:
					db_session.rollback()
					logger.exception("Opening Voucher creation Failure due to - %s" % e.message)
					raise Exception(e.message)
				_purchase_voucher_particular = VoucherParticulars(
					enterprise_id=enterprise_id, item_no=0, voucher_id=opening_vouchers['payables'][as_on].id,
					ledger_id=_purchase_ledger.id)
			else:
				_purchase_voucher_particular = opening_vouchers['payables'][as_on].particulars[0]
			_net_purchase_value = (_purchase_voucher_particular.amount * (
				-1 if _purchase_voucher_particular.is_debit else 1) + Decimal(payable_opening))
			_purchase_voucher_particular.amount = round(abs(_net_purchase_value), 2)
			_purchase_voucher_particular.is_debit = _net_purchase_value > 0
			_payable_voucher_particular = VoucherParticulars(
				enterprise_id=enterprise_id, amount=abs(round(payable_opening, 2)),
				is_debit=payable_opening < 0, item_no=len(opening_vouchers['payables'][as_on].particulars),
				voucher_id= opening_vouchers['payables'][as_on].id,
				ledger_id = party.getSupplierLedger().id)
			db_session.add(_payable_voucher_particular)
			db_session.add(_purchase_voucher_particular)
		if receivable_opening != 0:
			if as_on not in opening_vouchers['receivables']:
				db_session.begin(subtransactions=True)
				try:
					fy = getFinancialYear(
						for_date=datetime.strptime(as_on, '%Y-%m-%d'), fy_start_day=enterprise.fy_start_day)
					opening_vouchers['receivables'][as_on] = Voucher(
						enterprise_id=enterprise_id, voucher_date=as_on, financial_year=fy, status=1,
						approved_on=datetime.now(), approved_by=user.user_id,
						voucher_no=AccountsDAO().getLatestVoucherNo(
							financial_year=fy, type_id=1, enterprise_id=enterprise_id),
						narration="Receivable Openings for Parties imported on %s by %s %s" % (
							datetime.now(), user.first_name, user.last_name), created_by=user.user_id)
					db_session.add(opening_vouchers['receivables'][as_on])
					db_session.commit()
					db_session.refresh(opening_vouchers['receivables'][as_on])
				except Exception as e:
					db_session.rollback()
					logger.exception("Opening Voucher creation Failure due to - %s" % e.message)
					raise Exception(e.message)
				_sale_voucher_particular = VoucherParticulars(
					enterprise_id=enterprise_id, item_no=0, voucher_id= opening_vouchers['receivables'][as_on].id,
					ledger_id=_sale_ledger.id)
			else:
				_sale_voucher_particular = opening_vouchers['receivables'][as_on].particulars[0]
			_net_sale_value = _sale_voucher_particular.amount * (
				1 if _sale_voucher_particular.is_debit else -1) + Decimal(receivable_opening)
			_sale_voucher_particular.amount = round(abs(_net_sale_value), 2)
			_sale_voucher_particular.is_debit = _net_sale_value < 0
			_receivable_voucher_particular = VoucherParticulars(
				enterprise_id=enterprise_id, amount=abs(round(receivable_opening, 2)),
				is_debit=receivable_opening > 0,
				item_no=len(opening_vouchers['receivables'][as_on].particulars),
				voucher_id=opening_vouchers['receivables'][as_on].id,
				ledger_id =party.getCustomerLedger().id)
			db_session.add(_receivable_voucher_particular)
			db_session.add(_sale_voucher_particular)
		db_session.commit()
	except Exception as e:
		db_session.rollback()
		logger.exception("Failed to persist Voucher entry for Party - %s\nDue to - %s" % (party, e.message))
		raise OpeningVoucherConstructionFailure(e.message)
	return opening_vouchers


def loadPartyContacts(request):
	"""
	Fetch the other contact details for the specified party.
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	party_id = request_handler.getPostData("party_id")
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		party_contact_details = MasterService().getContactDetails(
			enterprise_id=enterprise_id, party_id=party_id)
	except Exception as e:
		logger.exception(e)
		party_contact_details = []
	return HttpResponse(content=simplejson.dumps(party_contact_details), mimetype='application/json')


class OpeningVoucherConstructionFailure(Exception):
	pass
