{% extends "accounts/sidebar.html" %}
{% block statements %}
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/chosen.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/datatables.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/fixedHeader.bootstrap.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/dataTables.searchHighlight.css?v={{ current_version }}">	
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/tabs-vertical.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/bootstrap-datepicker.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-multiselect.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/chosen.jquery.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/jquery.easyResponsiveTabs.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/daterangepicker.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/datatables.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/dataTables.fixedHeader.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/dataTables.searchHighlight.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/jquery.scrolling-tabs.js?v={{ current_version }}"></script>
<style type="text/css">
    .dataTables_scrollBody {
        overflow: auto hidden !important;
    }

    .dataTables_scrollBody table{
        margin-bottom: 20px;
    }

    .gstr_csv_export_button {
	    position: absolute;
	    right: 33px;
	    margin-top: -37px;
    }

    .table tfoot .report-total-text {
    	font-size: 20px !important;
    }

    .report-total-text:after {
    	content: '* includes all field';
    	color:  #dd4b39;
    	font-size: 12px;
    	display: block;
	}

	.table tfoot.gstr-report-foot td {
		font-size: 14px !important;
		font-weight: bold;
		text-align: right;
	}
</style>
<div class="right-content-container download-icon-onTop">
    <div class="page-title-container">
        <span class="page-title">GSTR-1 Sales Report</span>
    </div>
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <div class="content_bg">
                    <ul class="resp-tabs-list hor_1"></ul>
                    <div class="resp-tabs-container hor_1">
                        <div class="row">
                            <div class="add_table">
                                <div class="col-lg-12 add_table_content">
                                    <div class="filter-components" style="width: 100%; margin-left: 0; margin-bottom: 0;">
                                    	<button class="btn btn-add-new pull-right gstr-xsl-download disabled" style="background: #FFF; color: #337ab7;" onclick="tablesToExcelLoad();" data-tooltip="tooltip" title="Please wait... Your Report is not generated completely for download." ><i class="fa fa-download" aria-hidden="true"></i> &nbsp; as xls</button>
                                        <div class="filter-components-container">
                                            <div class="dropdown">
                                                <button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
                                                    <i class="fa fa-filter"></i>
                                                </button>
                                                <span class="dropdown-menu arrow_box arrow_box_filter">
                                                    <div class="col-sm-12 form-group" >
                                                        <label>Date Range</label>
                                                        <div id="reportrange" class="report-range form-control">
                                                            <i class="glyphicon glyphicon-calendar"></i>&nbsp;
                                                            <span></span> <b class="caret"></b>
                                                            <input type="hidden" class="fromdate" id="fromdate"
                                                                   name="fromdate"/>
                                                            <input type="hidden" class="todate" id="todate" name="todate"/>
                                                        </div>
                                                    </div>
                                                    <div class="filter-footer">
                                                        <button type="submit" class="btn btn-save" id="gstr1salesreportview">Apply</button>
                                                    </div>
                                                </span>
                                            </div>
                                            <span class='filtered-condition filtered-date'>Date: <b></b></span>
	                                        <span style="float:right">*AT and ATADJ items are not captured in GSTR1 report for now. They will be available soon</span>
                                        </div>
                                    </div>
                                    <ul class="nav nav-tabs" id="ul-report-header" role="tablist">
								      	<li role="presentation" class="active">
								      		<a href="#tab1" role="tab" data-toggle="tab">
								      			<span class="xs-vertical-tabs__header">B2B <!-- - <small class="b2b-total-amt">0.00</small></span><br />
								           	 	<span class="xs-vertical-tabs__sub-header">
								           	 		CGST - <span class="b2b-cgst-total-amt">1000.00</span> / 
								           	 		SGST - <span class="b2b-sgst-total-amt">1000.00</span> / 
								           	 		IGST - <span class="b2b-igst-total-amt">500.00</span>
								           	 	</span> -->
												</span>
								      		</a>
								      	</li>
								      	<li role="presentation">
								      		<a href="#tab2" role="tab" data-toggle="tab">
								      			<span class="xs-vertical-tabs__header">B2CL <!-- - <small class="b2cl-total-amt">0.00</small></span><br />
								           	 	<span class="xs-vertical-tabs__sub-header">
								           	 		CGST - <span class="b2cl-cgst-total-amt">1000.00</span> / 
								           	 		SGST - <span class="b2cl-sgst-total-amt">1000.00</span> / 
								           	 		IGST - <span class="b2cl-igst-total-amt">500.00</span>
								           	 	</span> -->
												</span>
								      		</a>
								      	</li>
								      	<li role="presentation">
								      		<a href="#tab3" role="tab" data-toggle="tab">
								      			<span class="xs-vertical-tabs__header">B2CS <!-- - <small class="b2cs-total-amt">0.00</small></span><br />
								           	 	<span class="xs-vertical-tabs__sub-header">
								           	 		CGST - <span class="b2cs-cgst-total-amt">1000.00</span> / 
								           	 		SGST - <span class="b2cs-sgst-total-amt">1000.00</span> / 
								           	 		IGST - <span class="b2cs-igst-total-amt">500.00</span>
								           	 	</span> -->
												</span>
								      		</a>
								      	</li>
								      	<li role="presentation">
								      		<a href="#tab4" role="tab" data-toggle="tab">
								      			<span class="xs-vertical-tabs__header">HSN  <!-- - <small class="hsn-total-amt">0.00</small></span><br />
								           	 	<span class="xs-vertical-tabs__sub-header">
								           	 		CGST - <span class="hsn-cgst-total-amt">1000.00</span> / 
								           	 		SGST - <span class="hsn-sgst-total-amt">1000.00</span> / 
								           	 		IGST - <span class="hsn-igst-total-amt">500.00</span>
								           	 	</span> -->
												</span>
								      		</a>
								      	</li>
							      		<li role="presentation">
							      			<a href="#tab5" role="tab" data-toggle="tab">
							      				<span class="xs-vertical-tabs__header">CDNR <!-- - <small class="cdnr-total-amt">0.00</small></span><br />
								           	 	<span class="xs-vertical-tabs__sub-header">
								           	 		CGST - <span class="cdnr-cgst-total-amt">1000.00</span> /
								           	 		SGST - <span class="cdnr-sgst-total-amt">1000.00</span> /
								           	 		IGST - <span class="cdnr-igst-total-amt">500.00</span>
								           	 	</span> -->
												</span>
							      			</a>
							      		</li>
							      		<li role="presentation">
							      			<a href="#tab6" role="tab" data-toggle="tab">
							      				<span class="xs-vertical-tabs__header">CDNUR <!-- - <small class="cdnur-total-amt">500000.00</small></span><br />
								           	 	<span class="xs-vertical-tabs__sub-header">
								           	 		CGST - <span class="cdnur-cgst-total-amt">1000.00</span> /
								           	 		SGST - <span class="cdnur-sgst-total-amt">1000.00</span> /
								           	 		IGST - <span class="cdnur-igst-total-amt">500.00</span>
								           	 	</span> -->
												</span>
							      			</a>
							      		</li>
	                                    <li role="presentation">
							      			<a href="#tab7" role="tab" data-toggle="tab">
							      				<span class="xs-vertical-tabs__header">EXP <!-- - <small class="exp-total-amt">500000.00</small></span><br />
								           	 	<span class="xs-vertical-tabs__sub-header">
								           	 		CGST - <span class="exp-cgst-total-amt">1000.00</span> /
								           	 		SGST - <span class="exp-sgst-total-amt">1000.00</span> /
								           	 		IGST - <span class="exp-igst-total-amt">500.00</span>
								           	 	</span> -->
												</span>
							      			</a>
							      		</li>
								      	<li role="presentation">
								      		<a href="#tab8" role="tab" data-toggle="tab">
								      			<span class="xs-vertical-tabs__header">EXEMP  <!-- - <small class="exemp-total-amt">500000.00</small></span><br />
								           	 	<span class="xs-vertical-tabs__sub-header">
								           	 		CGST - <span class="exemp-cgst-total-amt">1000.00</span> / 
								           	 		SGST - <span class="exemp-sgst-total-amt">1000.00</span> / 
								           	 		IGST - <span class="exemp-igst-total-amt">500.00</span>
								           	 	</span> -->
												</span>
								      		</a>
								      	</li>
								    </ul>
								    <div class="tab-content report-tab-content">
								      	<div role="tabpanel" class="tab-pane active" id="tab1">
							      			<div class="xs-vertical-tabs__title">B2B</div>
							   				<div class="gstr_csv_export_button">
                                            	<a role="button" class="btn btn-add-new pull-right export_csv gstr-b2b-download" disabled onclick="GeneralExportTableToCSVInit(this, [$('#gstr-b2b-report'), 'GSTR B2B Report.csv']);" data-tooltip="tooltip" title="Please wait... Your Report is not generated completely for download."><i class="fa fa-download" aria-hidden="true"></i></a>
                                        	</div>
									      	<table class="table table-bordered custom-table table-striped search_result_table" id="gstr-b2b-report" style="width: 100%;margin-right: 80px; display: inline-table;">
												<thead class="th-vertical-center">
													<tr class='total-header'>
														<th data-style="tableHeader" style="min-width: 80px;">S. NO</th>
														<th data-style="tableHeader" style="min-width: 80px;">GSTIN/UIN</th>
														<th data-style="tableHeader" style="min-width: 80px;">Receiver Name </th>
														<th data-style="tableHeader" style="min-width: 80px;">Invoice Number</th>
														<th data-style="tableHeader" style="min-width: 100px;">Invoice Date </th>
														<th data-style="tableHeader" style="min-width: 60px;">Invoice Value </th>
														<th data-style="tableHeader" style="min-width: 60px;">Place of Supply</th>
														<th data-style="tableHeader" style="min-width: 60px;">Reverse Charge</th>
														<th data-style="tableHeader" style="min-width: 60px;">Applicable % of Tax Rate</th>
														<th data-style="tableHeader" style="min-width: 60px;">Invoice Type</th>
														<th data-style="tableHeader" style="min-width: 60px;">E-Commerce GSTIN </th>
														<th data-style="tableHeader" style="min-width: 60px;">Rate </th>
														<th data-style="tableHeader" style="min-width: 60px;">Taxable Value </th>
														<th data-style="tableHeader" style="min-width: 60px;">IGST </th>
														<th data-style="tableHeader" style="min-width: 60px;">CGST </th>
														<th data-style="tableHeader" style="min-width: 60px;">SGST </th>
<!-- 														<th data-style="tableHeader" style="min-width: 60px;">Duty </th>-->
														<th data-style="tableHeader" style="min-width: 60px;">Cess Amount </th>
													</tr>
												</thead>
												<tbody id="grn_b2b_tbl_tbody" class="gstr-report-body"></tbody>
												<tfoot id="grn_b2b_tbl_tfoot" class="gstr-report-foot"></tfoot>
											</table>
								      	</div>
								      	<div role="tabpanel" class="tab-pane" id="tab2">
								      		<div class="xs-vertical-tabs__title">B2CL</div>
								   			<div class="gstr_csv_export_button">
                                            	<a role="button" class="btn btn-add-new pull-right export_csv gstr-b2cl-download" disabled onclick="GeneralExportTableToCSVInit(this, [$('#gstr-b2cl-report'), 'GSTR B2CL Report.csv']);" data-tooltip="tooltip" title="Please wait... Your Report is not generated completely for download."><i class="fa fa-download" aria-hidden="true"></i></a>
                                        	</div>
								      		<table class="table table-bordered custom-table table-striped search_result_table" id="gstr-b2cl-report" style="width: 100%;margin-right: 80px; display: inline-table;">
												<thead class="th-vertical-center">
												<tr class='total-header'>
													<th data-style="tableHeader" style="min-width: 80px;">S. NO</th>
													<th data-style="tableHeader" style="min-width: 80px;">Invoice Number</th>
													<th data-style="tableHeader" style="min-width: 100px;">Invoice Date </th>
													<th data-style="tableHeader" style="min-width: 60px;">Invoice Value </th>
													<th data-style="tableHeader" style="min-width: 60px;">Place of Supply</th>
													<th data-style="tableHeader" style="min-width: 60px;">Applicable % of Tax Rate</th>
													<th data-style="tableHeader" style="min-width: 60px;">Rate </th>
													<th data-style="tableHeader" style="min-width: 60px;">Taxable Value </th>
													<th data-style="tableHeader" style="min-width: 60px;">Duty </th>
													<th data-style="tableHeader" style="min-width: 60px;">Cess Amount </th>
													<th data-style="tableHeader" style="min-width: 60px;">E-Commerce GSTIN </th>
												</tr>
												</thead>
												<tbody id="grn_b2cl_tbl_tbody" class="gstr-report-body"></tbody>
												<tfoot id="grn_b2cl_tbl_tfoot" class="gstr-report-foot"></tfoot>
											</table>
								      	</div>
								      	<div role="tabpanel" class="tab-pane" id="tab3">
								      		<div class="xs-vertical-tabs__title">B2CS</div>
									      	<div class="gstr_csv_export_button">
	                                            <a role="button" class="btn btn-add-new pull-right export_csv gstr-b2cs-download" disabled onclick="GeneralExportTableToCSVInit(this, [$('#gstr-b2cs-report'), 'GSTR B2CS Report.csv']);" data-tooltip="tooltip" title="Please wait... Your Report is not generated completely for download."><i class="fa fa-download" aria-hidden="true"></i></a>
	                                        </div>
											<table class="table table-bordered custom-table table-striped search_result_table" id="gstr-b2cs-report" style="width: 100%;margin-right: 80px; display: inline-table;">
												<thead class="th-vertical-center">
												<tr class='total-header'>
													<th data-style="tableHeader" style="min-width: 80px;">S. NO</th>
													<th data-style="tableHeader" style="min-width: 80px;">Invoice Number</th>
													<th data-style="tableHeader" style="min-width: 80px;">Invoice Date</th>
													<th data-style="tableHeader" style="min-width: 80px;">Invoice Value</th>
													<th data-style="tableHeader" style="min-width: 60px;">Type </th>
													<th data-style="tableHeader" style="min-width: 60px;">Place of Supply</th>
													<th data-style="tableHeader" style="min-width: 60px;">Applicable % of Tax Rate</th>
													<th data-style="tableHeader" style="min-width: 60px;">Rate </th>
													<th data-style="tableHeader" style="min-width: 60px;">Taxable Value </th>
													<th data-style="tableHeader" style="min-width: 60px;">IGST </th>
													<th data-style="tableHeader" style="min-width: 60px;">CGST </th>
													<th data-style="tableHeader" style="min-width: 60px;">SGST </th>
<!--													<th data-style="tableHeader" style="min-width: 60px;">Duty </th>-->
													<th data-style="tableHeader" style="min-width: 60px;">Cess Amount </th>
													<th data-style="tableHeader" style="min-width: 60px;">E-Commerce GSTIN </th>
												</tr>
												</thead>
												<tbody id="grn_b2cs_tbl_tbody" class="gstr-report-body"></tbody>
												<tfoot id="grn_b2cs_tbl_tfoot" class="gstr-report-foot"></tfoot>
											</table>
								      	</div>
								      	<div role="tabpanel" class="tab-pane" id="tab4">
								      		<div class="xs-vertical-tabs__title">HSN</div>
									      	<div class="gstr_csv_export_button">
	                                            <a role="button" class="btn btn-add-new pull-right export_csv gstr-hsn-download" disabled onclick="GeneralExportTableToCSVInit(this, [$('#gstr-hsn-report'), 'GSTR HSN Report.csv']);" data-tooltip="tooltip" title="Please wait... Your Report is not generated completely for download."><i class="fa fa-download" aria-hidden="true"></i></a>
	                                        </div>
											<table class="table table-bordered custom-table table-striped search_result_table" id="gstr-hsn-report" style="width: 100%;margin-right: 80px; display: inline-table;">
												<thead class="th-vertical-center">
												<tr class='total-header'>
													<th data-style="tableHeader" style="min-width: 80px;">S. NO</th>
													<th data-style="tableHeader" style="min-width: 60px;">HSN </th>
													<th data-style="tableHeader" style="min-width: 60px;">Description</th>
													<th data-style="tableHeader" style="min-width: 60px;">UQC</th>
													<th data-style="tableHeader" style="min-width: 60px;">Total Quantity</th>
													<th data-style="tableHeader" style="min-width: 60px;">Total Value</th>
													<th data-style="tableHeader" style="min-width: 60px;">Taxable Value</th>
													<th data-style="tableHeader" style="min-width: 60px;">Integrated Tax Amount</th>
													<th data-style="tableHeader" style="min-width: 60px;">Central Tax Amount</th>
													<th data-style="tableHeader" style="min-width: 60px;">State/UT Tax Amount</th>
													<th data-style="tableHeader" style="min-width: 60px;">Cess Amount</th>
												</tr>
												</thead>
												<tbody id="grn_hsn_tbl_tbody" class="gstr-report-body"></tbody>
												<tfoot id="grn_hsn_tbl_tfoot" class="gstr-report-foot"></tfoot>
											</table>
								      	</div>
								      	<div role="tabpanel" class="tab-pane" id="tab5">
								      		<div class="xs-vertical-tabs__title">CDNR</div>
									      	<div class="gstr_csv_export_button">
	                                            <a role="button" class="btn btn-add-new pull-right export_csv gstr-cdnr-download" disabled onclick="GeneralExportTableToCSVInit(this, [$('#gstr-cdnr-report'), 'GSTR CDNR Report.csv']);" data-tooltip="tooltip" title="Please wait... Your Report is not generated completely for download."><i class="fa fa-download" aria-hidden="true"></i></a>
	                                        </div>
											<table class="table table-bordered custom-table table-striped search_result_table" id="gstr-cdnr-report" style="width: 100%;margin-right: 80px; display: inline-table;">
												<thead class="th-vertical-center">
												<tr class='total-header'>
													<th data-style="tableHeader" style="min-width: 80px;">S. NO</th>
													<th data-style="tableHeader" style="min-width: 60px;">GSTIN/UIN</th>
													<th data-style="tableHeader" style="min-width: 60px;">Receiver Name</th>
													<th data-style="tableHeader" style="min-width: 60px;">Invoice/Advance Receipt No</th>
													<th data-style="tableHeader" style="min-width: 60px;">Invoice/Advance Receipt Date</th>
													<th data-style="tableHeader" style="min-width: 60px;">Note/Refund Voucher No</th>
													<th data-style="tableHeader" style="min-width: 60px;">Note/Refund Voucher Date</th>
													<th data-style="tableHeader" style="min-width: 60px;">Document Type</th>
													<th data-style="tableHeader" style="min-width: 60px;">Place of Supply</th>
													<th data-style="tableHeader" style="min-width: 60px;">Note/Refund Voucher Value</th>
													<th data-style="tableHeader" style="min-width: 60px;">Applicable % of Tax Rate</th>
													<th data-style="tableHeader" style="min-width: 60px;">Rate</th>
													<th data-style="tableHeader" style="min-width: 60px;">Taxable Value</th>
													<th data-style="tableHeader" style="min-width: 60px;">Duty </th>
													<th data-style="tableHeader" style="min-width: 60px;">Cess Amount</th>
													<th data-style="tableHeader" style="min-width: 60px;">Pre GST</th>
												</tr>
												</thead>
												<tbody id="grn_cdnr_tbl_tbody" class="gstr-report-body"></tbody>
												<tfoot id="grn_cdnr_tbl_tfoot" class="gstr-report-foot"></tfoot>
											</table>
								      	</div>
								      	<div role="tabpanel" class="tab-pane" id="tab6">
								      		<div class="xs-vertical-tabs__title">CDNUR</div>
									      	<div class="gstr_csv_export_button">
	                                            <a role="button" class="btn btn-add-new pull-right export_csv gstr-cdnur-download" disabled onclick="GeneralExportTableToCSVInit(this, [$('#gstr-cdnur-report'), 'GSTR CDNUR Report.csv']);" data-tooltip="tooltip" title="Please wait... Your Report is not generated completely for download."><i class="fa fa-download" aria-hidden="true"></i></a>
	                                        </div>
											<table class="table table-bordered custom-table table-striped search_result_table" id="gstr-cdnur-report" style="width: 100%;margin-right: 80px; display: inline-table;">
												<thead class="th-vertical-center">
												<tr class='total-header'>
													<th data-style="tableHeader" style="min-width: 80px;">S. NO</th>
													<th data-style="tableHeader" style="min-width: 60px;">UR Type</th>
													<th data-style="tableHeader" style="min-width: 60px;">Note/Refund Voucher No</th>
													<th data-style="tableHeader" style="min-width: 60px;">Note/Refund Voucher Date</th>
													<th data-style="tableHeader" style="min-width: 60px;">Document Type</th>
													<th data-style="tableHeader" style="min-width: 60px;">Invoice/Advance Receipt No</th>
													<th data-style="tableHeader" style="min-width: 60px;">Invoice/Advance Receipt Date</th>
													<th data-style="tableHeader" style="min-width: 60px;">Place of Supply</th>
													<th data-style="tableHeader" style="min-width: 60px;">Note/Refund Voucher Value</th>
													<th data-style="tableHeader" style="min-width: 60px;">Applicable % of Tax Rate</th>
													<th data-style="tableHeader" style="min-width: 60px;">Rate</th>
													<th data-style="tableHeader" style="min-width: 60px;">Taxable Value	</th>
													<th data-style="tableHeader" style="min-width: 60px;">Duty </th>
													<th data-style="tableHeader" style="min-width: 60px;">Cess Amount</th>
													<th data-style="tableHeader" style="min-width: 60px;">Pre GST</th>
												</tr>
												</thead>
												<tbody id="grn_cdnur_tbl_tbody" class="gstr-report-body"></tbody>
												<tfoot id="grn_cdnur_tbl_tfoot" class="gstr-report-foot"></tfoot>
											</table>
								      	</div>
									    <div role="tabpanel" class="tab-pane" id="tab7">
								      		<div class="xs-vertical-tabs__title">EXP</div>
									      	<div class="gstr_csv_export_button">
	                                            <a role="button" class="btn btn-add-new pull-right export_csv gstr-exp-download" disabled onclick="GeneralExportTableToCSVInit(this, [$('#gstr-exp-report'), 'GSTR EXP Report.csv']);" data-tooltip="tooltip" title="Please wait... Your Report is not generated completely for download."><i class="fa fa-download" aria-hidden="true"></i></a>
	                                        </div>
											<table class="table table-bordered custom-table table-striped search_result_table" id="gstr-exp-report" style="width: 100%;margin-right: 80px; display: inline-table;">
												<thead class="th-vertical-center">
												<tr class='total-header'>
													<th data-style="tableHeader" style="min-width: 80px;">S. NO</th>
													<th data-style="tableHeader" style="min-width: 40px;">Export Type</th>
													<th data-style="tableHeader" style="min-width: 60px;">Invoice No</th>
													<th data-style="tableHeader" style="min-width: 60px;">Invoice Date</th>
													<th data-style="tableHeader" style="min-width: 60px;">Invoice Value</th>
													<th data-style="tableHeader" style="min-width: 60px;">Port Code</th>
													<th data-style="tableHeader" style="min-width: 60px;">Shipping Bill No</th>
													<th data-style="tableHeader" style="min-width: 60px;">Shipping Bill Date</th>
													<th data-style="tableHeader" style="min-width: 60px;">Applicable % of Tax Rate</th>
													<th data-style="tableHeader" style="min-width: 60px;">Rate</th>
													<th data-style="tableHeader" style="min-width: 60px;">Taxable Value</th>
													<th data-style="tableHeader" style="min-width: 60px;">Cess Value</th>
												</tr>
												</thead>
												<tbody id="grn_exp_tbl_tbody" class="gstr-report-body"></tbody>
												<tbody id="grn_exp_tbl_tfoot" class="gstr-report-body"></tbody>
											</table>
								      	</div>
								      	<div role="tabpanel" class="tab-pane" id="tab8">
								      		<div class="xs-vertical-tabs__title">EXEMP</div>
									      	<div class="gstr_csv_export_button">
	                                            <a role="button" class="btn btn-add-new pull-right export_csv gstr-exemp-download" disabled onclick="GeneralExportTableToCSVInit(this, [$('#gstr-exemp-report'), 'GSTR EXEMP Report.csv']);" data-tooltip="tooltip" title="Please wait... Your Report is not generated completely for download."><i class="fa fa-download" aria-hidden="true"></i></a>
	                                        </div>
											<table class="table table-bordered custom-table table-striped search_result_table" id="gstr-exemp-report" style="width: 100%;margin-right: 80px; display: inline-table;	">
												<thead class="th-vertical-center">
												<tr class='total-header'>
													<th data-style="tableHeader" style="min-width: 80px;">S. NO</th>
													<th data-style="tableHeader" style="min-width: 160px;">Description</th>
													<th data-style="tableHeader" style="min-width: 60px;">Nil Rated Supplies</th>
													<th data-style="tableHeader" style="min-width: 60px;">Exempted(other than nil rated/non GST supply)</th>
													<th data-style="tableHeader" style="min-width: 60px;">Non-GST Supplies</th>
												</tr>
												</thead>
												<tbody id="grn_exemp_tbl_tbody" class="gstr-report-body"></tbody>
												<tfoot id="grn_exemp_tbl_tfoot" class="gstr-report-foot"></tfoot>
											</table>
								      	</div>
								    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        {% block stock_statement %}
                        {% endblock %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    $(document).ready(function () {
        setTimeout(function () {
			$("#gstr1salesreportview").trigger('click');
        }, 10);
        activateTabs();
    });

    $(window).load(function(){
        updateFilterText();
        closeFilterOption();
        $("#loading").addClass('hide');
    });

    function updateFilterText() {
        $(".filtered-date b").text($("#reportrange").find("span").text());
    }

    var oTable1, oTable2, oTable3, oTable4, oTable5, oTable6, oTable7, oTable8, tableReadyStatus = [];
    var oSettings1, oSettings2, oSettings3, oSettings4, oSettings5, oSettings6, oSettings7, oSettings8;
    function loopB2BTblData(data){
        var row = '';
        var b2b_invoice_value=0,b2b_taxable_value=0,b2b_duty_value=0,b2b_cess_amount=0 , b2b_rate_value=0, b2b_igst_value=0,b2b_cgst_value=0,b2b_sgst_value=0;
        if(data.length != 0){
            $.each(data, function (key, value) {
                row = `	<tr class='text-center'>
               			    <td>${key+1}</td>
                    		<td>${value['gstno']}</td>
		                    <td>${value['party_name']}</td>
		                    <td>${value['invoice_code']}</td>
		                    <td data-sort='${moment(value['invoice_date']).format("YYYYMMDDHHmmss")}'>${moment(value['invoice_date']).format("DD/MM/YYYY")}</td>
		                    <td class='text-right'>${(value['invoice_value'] ? Number(value['invoice_value']).toFixed(2) : "Nil")}</td>
		                    <td>${value['place_of_supply']}</td>
		                    <td>${value['reverse_charge']}</td>
		                    <td></td>
		                    <td>${value['type']}</td>
		                    <td>${value['ecommerce_gstin']}</td>
		                    <td class='text-right'>${value['rate']}</td>
		                    <td class='text-right'>${(value['taxable_value'] ? Number(value['taxable_value']).toFixed(2) : "Nil")}</td>
		                    <td class='text-right'>${(value['igst_value'] == undefined ? "0.00" : Number(value['igst_value']))}</td>
		                    <td class='text-right'>${(value['cgst_value'] == undefined ? "0.00" : Number(value['cgst_value']))}</td>
		                    <td class='text-right'>${(value['sgst_value'] == undefined ? "0.00" : Number(value['sgst_value']))}</td>
<!--		                    <td class='text-right'>${(value['taxable_value'] == undefined ? "0.00" : Number(value['taxable_value']) * value['rate']/100).toFixed(2)}</td>-->
		                    <td class='text-right'>${(value['cess_value'] == undefined ? "0.00" : value['cess_value'])}</td>
	                    </tr>`;
	            b2b_invoice_value = parseFloat((value['invoice_value'] ? value['invoice_value'] : "0.00")) + b2b_invoice_value;
	            b2b_taxable_value = parseFloat((value['taxable_value'] == undefined ? "0.00" : value['taxable_value'])) + b2b_taxable_value;
	            b2b_duty_value = parseFloat((value['taxable_value'] == undefined ? "0.00" : value['taxable_value'] * value['rate']/100)) + b2b_duty_value;
	            b2b_rate_value = parseFloat((value['rate'] ? value['rate'] : "0.00")) + b2b_rate_value;
	            b2b_cess_amount = parseFloat((value['cess_value'] == undefined ? "0.00" : value['cess_value'])) + b2b_cess_amount;
	            b2b_sgst_value = parseFloat((value['sgst_value'] ? value['sgst_value'] : "0.00")) + b2b_sgst_value;
	            b2b_cgst_value = parseFloat((value['cgst_value'] ? value['cgst_value'] : "0.00")) + b2b_cgst_value;
	            b2b_igst_value = parseFloat((value['igst_value'] ? value['igst_value'] : "0.00")) + b2b_igst_value;
                $('#grn_b2b_tbl_tbody').append(row);
            });
            var foot_row = `<tr class='exclude-export'>
            					<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td class="text-right report-total-text">Total</td>
								<td>${Number(b2b_invoice_value).toFixed(2)}</td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td>${Number(b2b_rate_value).toFixed(2)}</td>
								<td>${Number(b2b_taxable_value).toFixed(2)}</td>
								<td>${Number(b2b_igst_value).toFixed(2)}</td>
								<td>${Number(b2b_cgst_value).toFixed(2)}</td>
								<td>${Number(b2b_sgst_value).toFixed(2)}</td>
<!--								<td>${Number(b2b_duty_value).toFixed(2)}</td>-->
								<td>${Number(b2b_cess_amount).toFixed(2)}</td>
							</tr>`;
			var total_header_for_excel = `<tr class='hide total-header gstr-report-head'>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Invoice Value</td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Taxable Value</td>
<!--											<td data-style="reportHeader">Total Duty Value</td>-->
											<td data-style="reportHeader">Total Cess</td>
										  </tr>
										  <tr class='hide gstr-report-head'>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td>${Number(b2b_invoice_value).toFixed(2)}</td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td>${Number(b2b_taxable_value).toFixed(2)}</td>
<!--											<td>${Number(b2b_duty_value).toFixed(2)}</td>-->
											<td>${Number(b2b_cess_amount).toFixed(2)}</td>
										</tr>`;
			$("#gstr-b2b-report thead").prepend(total_header_for_excel);
			$('#grn_b2b_tbl_tfoot').append(foot_row);
            $('.b2b-total-amt').text(Number(b2b_invoice_value).toFixed(2));
        }
        oTable1 = $('#gstr-b2b-report').DataTable({
			fixedHeader: true,
			"pageLength": 50,
			"search": {
				"smart": false
			}
		});
		oTable1.on("draw",function() {
			var keyword = $('#gstr-b2b-report_filter > label:eq(0) > input').val();
			$('#gstr-b2b-report').unmark();
			$('#gstr-b2b-report').mark(keyword,{});
		});
		oTable1.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
        oSettings1 = oTable1.settings();
        $( window ).resize();
        tableReadyStatus.push("b2b");
        validateTableDownload();
    }

    function loopB2CLTblData(data){
        var row = '';
        var b2cl_invoice_value=0,b2cl_taxable_value=0,b2cl_duty_value=0,b2cl_cess_amount=0;
        if(data.length != 0){
            $.each(data, function (key, value) {
                row = 	`<tr class='text-center'>
                			<td>${key+1}</td>
		                    <td>${value['invoice_code']}</td>
		                    <td data-sort='${moment(value['invoice_date']).format("YYYYMMDDHHmmss")}'>${moment(value['invoice_date']).format("DD/MM/YYYY")}</td>
		                    <td class='text-right'>${(value['invoice_value'] ? Number(value['invoice_value']).toFixed(2) : "Nil")}</td>
		                    <td>${value['place_of_supply']}</td>
		                    <td></td>
		                    <td class='text-right'>${value['rate']}</td>
		                    <td class='text-right'>${(value['taxable_value'] == undefined ? "0.00" : Number(value['taxable_value']).toFixed(2))}</td>
		                    <td class='text-right'>${(value['taxable_value'] == undefined ? "0.00" : Number(value['taxable_value'] * value['rate']/100).toFixed(2))}</td>
		                    <td class='text-right'>${(value['cess_value'] == undefined ? "0.00" : value['cess_value'])}</td>
		                    <td>${value['ecommerce_gstin']}</td>
	                    </tr>`;
                $('#grn_b2cl_tbl_tbody').append(row);
                b2cl_invoice_value = parseFloat((value['invoice_value'] ? value['invoice_value'] : "0.00")) + b2cl_invoice_value;
	            b2cl_taxable_value = parseFloat((value['taxable_value'] == undefined ? "0.00" : value['taxable_value'])) + b2cl_taxable_value;
	            b2cl_duty_value = parseFloat((value['taxable_value'] == undefined ? "0.00" : value['taxable_value'] * value['rate']/100)) + b2cl_duty_value;
	            b2cl_cess_amount = parseFloat((value['cess_value'] == undefined ? "0.00" : value['cess_value'])) + b2cl_cess_amount;
            });
            var foot_row = `<tr class='exclude-export'>
            					<td></td>
								<td></td>
								<td class="text-right report-total-text">Total</td>
								<td class="text-right"><b style="font-size: 14px;">${Number(b2cl_invoice_value).toFixed(2)}</b></td>
								<td></td>
								<td></td>
								<td></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(b2cl_taxable_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(b2cl_duty_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(b2cl_cess_amount).toFixed(2)}</b></td>
								<td></td>
							</tr>`;
			var total_header_for_excel = `<tr class='hide total-header gstr-report-head'>
			            					<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total</td>
											<td data-style="reportHeader">Total Invoice Value</b></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Taxable Value</td>
											<td data-style="reportHeader">Total Duty Value</td>
											<td data-style="reportHeader">Total Cess</td>
											<td data-style="reportHeader"></td>
										</tr>
										<tr class='hide gstr-report-head'>
			            					<td></td>
											<td></td>
											<td></td>
											<td>${Number(b2cl_invoice_value).toFixed(2)}</td>
											<td></td>
											<td></td>
											<td></td>
											<td>${Number(b2cl_taxable_value).toFixed(2)}</td>
											<td>${Number(b2cl_duty_value).toFixed(2)}</td>
											<td>${Number(b2cl_cess_amount).toFixed(2)}</td>
											<td></td>
										</tr>`;
			$("#gstr-b2cl-report thead").prepend(total_header_for_excel)
			$('#grn_b2cl_tbl_tfoot').append(foot_row);
            $('.b2cl-total-amt').text(Number(b2cl_invoice_value).toFixed(2));
        }
        oTable2 = $('#gstr-b2cl-report').DataTable({
			fixedHeader: true,
			"pageLength": 50,
			"search": {
				"smart": false
			}
		});
		oTable2.on("draw",function() {
			var keyword = $('#gstr-b2cl-report_filter > label:eq(0) > input').val();
			$('#gstr-b2cl-report').unmark();
			$('#gstr-b2cl-report').mark(keyword,{});
		});
		oTable2.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
        oSettings2 = oTable2.settings();
        $( window ).resize();
        tableReadyStatus.push("b2cl");
        validateTableDownload();
    }

    function loopB2CSTblData(data){
        var row = '';
        var b2cs_invoice_value=0,b2cs_taxable_value=0,b2cs_duty_value=0,b2cs_cess_amount=0, b2cs_rate_value=0, b2cs_igst_value=0, b2cs_cgst_value=0, b2cs_sgst_value=0;
        var b2cs_invoice_value = 0;
        if(data.length != 0){
            $.each(data, function (key, value) {
                row = `<tr class='text-center'>
                			<td>${key+1}</td>
                			<td>${value['invoice_code']}</td>
                			<td data-sort='${moment(value['invoice_date']).format("YYYYMMDDHHmmss")}'>${moment(value['invoice_date']).format("DD/MM/YYYY")}</td>
                			<td class='text-right'>${(value['invoice_value'] ? Number(value['invoice_value']).toFixed(2) : "Nil")}</td>
		                    <td>${(value['ecommerce_gstin'] != '' ? 'E' : 'OE')}</td>
		                    <td>${value['place_of_supply']}</td>
		                    <td></td>
		                    <td class='text-right'>${value['rate']}</td>
		                    <td class='text-right'>${(value['taxable_value'] == undefined ? "0.00" : Number(value['taxable_value']).toFixed(2))}</td>
		                    <td class='text-right'>${(value['igst_value'] == undefined ? "0.00" : Number(value['igst_value']))}</td>
		                    <td class='text-right'>${(value['cgst_value'] == undefined ? "0.00" : Number(value['cgst_value']))}</td>
		                    <td class='text-right'>${(value['sgst_value'] == undefined ? "0.00" : Number(value['sgst_value']))}</td>
<!--		                    <td class='text-right'>${(value['taxable_value'] == undefined ? "0.00" : Number(value['taxable_value'] * value['rate']/100).toFixed(2))}</td>-->
		                    <td class='text-right'>${(value['cess_value'] == undefined ? "0.00" : value['cess_value'])}</td>
		                    <td>${value['ecommerce_gstin']}</td>
	                    </tr>`;
                $('#grn_b2cs_tbl_tbody').append(row);
                b2cs_invoice_value = parseFloat((value['invoice_value'] ? value['invoice_value'] : "0.00")) + b2cs_invoice_value;
	            b2cs_taxable_value = parseFloat((value['taxable_value'] == undefined ? "0.00" : value['taxable_value'])) + b2cs_taxable_value;
	            b2cs_duty_value = parseFloat((value['taxable_value'] == undefined ? "0.00" : value['taxable_value'] * value['rate']/100)) + b2cs_duty_value;
	            b2cs_cess_amount = parseFloat((value['cess_value'] == undefined ? "0.00" : value['cess_value'])) + b2cs_cess_amount;
	            b2cs_rate_value = parseFloat((value['rate'] ? value['rate'] : "0.00")) + b2cs_rate_value;
	            b2cs_sgst_value = parseFloat((value['sgst_value'] ? value['sgst_value'] : "0.00")) + b2cs_sgst_value;
	            b2cs_cgst_value = parseFloat((value['cgst_value'] ? value['cgst_value'] : "0.00")) + b2cs_cgst_value;
	            b2cs_igst_value = parseFloat((value['igst_value'] ? value['igst_value'] : "0.00")) + b2cs_igst_value;

            });
            var foot_row = `<tr class='exclude-export'>
            					<td></td>
								<td></td>
								<td class="text-right report-total-text">Total</td>
								<td class="text-right"><b style="font-size: 14px;">${Number(b2cs_invoice_value).toFixed(2)}</b></td>
								<td></td>
								<td></td>
								<td></td>
								<td class="text-right">${Number(b2cs_rate_value).toFixed(2)}</td>
								<td class="text-right">${Number(b2cs_taxable_value).toFixed(2)}</td>
								<td class="text-right"><b style="font-size: 14px;">${Number(b2cs_igst_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(b2cs_cgst_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(b2cs_sgst_value).toFixed(2)}</b></td>
<!--								<td class="text-right"><b style="font-size: 14px;">${Number(b2cs_duty_value).toFixed(2)}</b></td>-->
								<td class="text-right"><b style="font-size: 14px;">${Number(b2cs_cess_amount).toFixed(2)}</b></td>
								<td></td>
							</tr>`;
			var total_header_for_excel = `<tr class='hide total-header gstr-report-head'>
			            					<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Taxable Value</td>
<!--											<td data-style="reportHeader">Total Duty Value</td>-->
											<td data-style="reportHeader">Total Cess</td>
											<td data-style="reportHeader"></td>
										</tr>
										<tr class='hide gstr-report-head'>
			            					<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td>${Number(b2cs_taxable_value).toFixed(2)}</td>
<!--											<td>${Number(b2cs_duty_value).toFixed(2)}</td>-->
											<td>${Number(b2cs_cess_amount).toFixed(2)}</td>
											<td></td>
										</tr>`;
			$("#gstr-b2cs-report thead").prepend(total_header_for_excel);							
			$('#grn_b2cs_tbl_tfoot').append(foot_row);
            $('.b2cs-total-amt').text(Number(b2cs_invoice_value).toFixed(2));
        }
        oTable3 = $('#gstr-b2cs-report').DataTable({
			fixedHeader: true,
			"pageLength": 50,
			"search": {
				"smart": false
			}
		});
		oTable3.on("draw",function() {
			var keyword = $('#gstr-b2cs-report_filter > label:eq(0) > input').val();
			$('#gstr-b2cs-report').unmark();
			$('#gstr-b2cs-report').mark(keyword,{});
		});
		oTable3.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
        oSettings3 = oTable3.settings();
        $( window ).resize();
        tableReadyStatus.push("b2cs");
        validateTableDownload();
    }

    function loopHSNTblData(data){
        var row = '';
        var hsn_total_qty=0,hsn_total_value=0,hsn_tax_value=0,hsn_int_tax=0,hsn_central_tax=0,hsn_state_tax=0,hsn_cess_amt=0;
        var total_value = 0;
        if(data.length != 0){
            $.each(data, function (key, value) {
                row = `<tr class='text-center'>
                			<td>${key+1}</td>
		                    <td>${value['hsn_code']}</td>
		                    <td>${value['description']}</td>
		                    <td>${value['unit_name']}</td>
		                    <td class='text-right'>${value['qty']}</td>
		                    <td class='text-right'>${(value['total_value'] == undefined ? "0.00" : Number(value['total_value']).toFixed(2))}</td>
		                    <td class='text-right'>${(value['taxable_value'] == undefined ? "0.00" : Number(value['taxable_value']).toFixed(2))}</td>
		                    <td class='text-right'>${Number(value['igst_value']).toFixed(2)}</td>
		                    <td class='text-right'>${Number(value['cgst_value']).toFixed(2)}</td>
		                    <td class='text-right'>${Number(value['sgst_value']).toFixed(2)}</td>
		                    <td class='text-right'>${(value['cess_value'] == undefined ? "0.00" : value['cess_value'])}</td>
	                    </tr>`;
                $('#grn_hsn_tbl_tbody').append(row);
                hsn_total_qty = parseFloat(value['qty']) + hsn_total_qty;
                hsn_total_value = parseFloat((value['total_value'] ? value['total_value'] : "0.00")) + hsn_total_value;
                hsn_tax_value = parseFloat((value['taxable_value'] ? value['taxable_value'] : "0.00")) + hsn_tax_value;
                hsn_int_tax = parseFloat(value['igst_value']) + hsn_int_tax;
                hsn_central_tax = parseFloat(value['cgst_value']) + hsn_central_tax;
                hsn_state_tax = parseFloat(value['sgst_value']) + hsn_state_tax;
                hsn_cess_amt = parseFloat((value['cess_value'] ? value['cess_value'] : "0.00")) + hsn_cess_amt;
            });
            var foot_row = `<tr class='exclude-export'>
            					<td></td>
								<td></td>
								<td></td>
								<td class="text-right report-total-text">Total</td>
								<td class="text-right"><b style="font-size: 14px;">${Number(hsn_total_qty).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(hsn_total_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(hsn_tax_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(hsn_int_tax).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(hsn_central_tax).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(hsn_state_tax).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(hsn_cess_amt).toFixed(2)}</b></td>
							</tr>`;
			var total_header_for_excel = `<tr class='hide total-header gstr-report-head'>
			            					<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Quantity</td>
											<td data-style="reportHeader">Total Value</td>
											<td data-style="reportHeader">Total Taxable Value</td>
											<td data-style="reportHeader">Total Integrated Tax</td>
											<td data-style="reportHeader">Total Central Tax</td>
											<td data-style="reportHeader">Total State/UT Tax</td>
											<td data-style="reportHeader">Total Cess</td>
										</tr>
										<tr class='hide gstr-report-head'>
			            					<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td>${Number(hsn_total_qty).toFixed(2)}</td>
											<td>${Number(hsn_total_value).toFixed(2)}</td>
											<td>${Number(hsn_tax_value).toFixed(2)}</td>
											<td>${Number(hsn_int_tax).toFixed(2)}</td>
											<td>${Number(hsn_central_tax).toFixed(2)}</td>
											<td>${Number(hsn_state_tax).toFixed(2)}</td>
											<td>${Number(hsn_cess_amt).toFixed(2)}</td>
										</tr>`;
			$("#gstr-hsn-report thead").prepend(total_header_for_excel);
			$('#grn_hsn_tbl_tfoot').append(foot_row);
            $('.hsn-total-amt').text(Number(hsn_total_value).toFixed(2));
        }
        oTable4 = $('#gstr-hsn-report').DataTable({
			fixedHeader: true,
			"pageLength": 50,
			"search": {
				"smart": false
			}
		});
		oTable4.on("draw",function() {
			var keyword = $('#gstr-hsn-report_filter > label:eq(0) > input').val();
			$('#gstr-hsn-report').unmark();
			$('#gstr-hsn-report').mark(keyword,{});
		});
		oTable4.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
        oSettings4 = oTable4.settings();
        $( window ).resize();
        tableReadyStatus.push("hsn");
        validateTableDownload();
    }

    function loopCDNRTblData(data){
        var row = '';
        var cdnr_note_value = 0,cdnr_tax_value=0, cdnr_duty_value=0,cdnr_cess_amt=0
        if(data.length != 0){
            $.each(data, function (key, value) {
                row = `	<tr class='text-center'>
                			<td>${key+1}</td>
		                    <td>${value['gstin']}</td>
		                    <td>${value['party_name']}</td>
		                    <td>${value['inv_no']}</td>
		                    <td data-sort='${moment(value['inv_date']).format("YYYYMMDDHHmmss")}'>${moment(value['inv_date']).format("DD/MM/YYYY")}</td>
		                    <td>${value['sr_no']}</td>
		                    <td data-sort='${moment(value['grn_date']).format("YYYYMMDDHHmmss")}'>${moment(value['grn_date']).format("DD/MM/YYYY")}</td>
		                    <td>${value['doc_type']}</td>
		                    <td>${value['place_of_supply']}</td>
		                    <td class='text-right'>${Number(value['net_inv_value']).toFixed(2)}</td>
		                    <td></td>
		                    <td class='text-right'>${value['rate']}</td>
		                    <td class='text-right'>${(value['taxable_value'] == undefined ? "0.00" : Number(value['taxable_value']).toFixed(2))}</td>
		                    <td class='text-right'>${(value['taxable_value'] == undefined ? "0.00" : Number(value['taxable_value'] * value['rate']/100).toFixed(2))}</td>
		                    <td class='text-right'>${(value['cess_value'] == undefined ? "0.00" : value['cess_value'])}</td>
		                    <td>${value['pre_gst']}</td>
	                    </tr>`;
                $('#grn_cdnr_tbl_tbody').append(row);
                cdnr_note_value = parseFloat(value['net_inv_value']) + cdnr_note_value;
                cdnr_tax_value = parseFloat((value['taxable_value'] ? value['taxable_value'] : "0.00")) + cdnr_tax_value;
                cdnr_duty_value = parseFloat((value['taxable_value'] ? (value['taxable_value'] * value['rate'])/100 : "0.00")) + cdnr_duty_value;
                cdnr_cess_amt = parseFloat((value['cess_value'] ? value['cess_value'] : "0.00")) + cdnr_cess_amt;
            });
            var foot_row = `<tr class='exclude-export'>
            					<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td class="text-right report-total-text">Total</td>
								<td>${Number(cdnr_note_value).toFixed(2)}</td>
								<td></td>
								<td></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cdnr_tax_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cdnr_duty_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cdnr_cess_amt).toFixed(2)}</b></td>
								<td></td>
							</tr>`;
			var total_header_for_excel = `<tr class='hide total-header gstr-report-head'>
			            					<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Note/Refund Voucher Value</td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Taxable Value</td>
											<td data-style="reportHeader">Total Duty Value</td>
											<td data-style="reportHeader">Total Cess</td>
											<td data-style="reportHeader"></td>
										</tr>
										<tr class='hide gstr-report-head'>
			            					<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td>${Number(cdnr_note_value).toFixed(2)}</td>
											<td></td>
											<td></td>
											<td>${Number(cdnr_tax_value).toFixed(2)}</b></td>
											<td>${Number(cdnr_duty_value).toFixed(2)}</b></td>
											<td>${Number(cdnr_cess_amt).toFixed(2)}</b></td>
											<td></td>
										</tr>`;
			$("#gstr-cdnr-report thead").prepend(total_header_for_excel);
			$('#grn_cdnr_tbl_tfoot').append(foot_row);
        }
        oTable5 = $('#gstr-cdnr-report').DataTable({
			fixedHeader: true,
			"pageLength": 50,
			"search": {
				"smart": false
			}
		});
		oTable5.on("draw",function() {
			var keyword = $('#gstr-cdnr-report_filter > label:eq(0) > input').val();
			$('#gstr-cdnr-report').unmark();
			$('#gstr-cdnr-report').mark(keyword,{});
		});
		oTable5.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
        oSettings5 = oTable5.settings();
        $( window ).resize();
        tableReadyStatus.push("cdnr");
        validateTableDownload();
    }

    function loopCDNURTblData(data){
        var row = '';
        var cdnur_tax_value=0,cdnur_duty_value=0, cdnur_cess_amt=0, cdnur_note_value=0;
        if(data.length != 0){
            $.each(data, function (key, value) {
                row = `	<tr class='text-center'>
							<td>${key+1}</td>
		                    <td>${value['gstin']}</td>
		                    <td>${value['sr_no']}</td>
		                    <td data-sort='${moment(value['grn_date']).format("YYYYMMDDHHmmss")}'>${moment(value['grn_date']).format("DD/MM/YYYY")}</td>
		                    <td>${value['doc_type']}</td>
		                    <td>${value['inv_no']}</td>
		                    <td data-sort='${moment(value['inv_date']).format("YYYYMMDDHHmmss")}'>${moment(value['inv_date']).format("DD/MM/YYYY")}</td>
		                    <td>${value['place_of_supply']}</td>
		                    <td class='text-right'>${Number(value['net_inv_value']).toFixed(2)}</td>
		                    <td></td>
		                    <td class='text-right'>${value['rate']}</td>
		                    <td class='text-right'>${(value['taxable_value'] == undefined ? "0.00" : Number(value['taxable_value']).toFixed(2))}</td>
		                    <td class='text-right'>${(value['taxable_value'] == undefined ? "0.00" : Number(value['taxable_value'] * value['rate']/100).toFixed(2))}</td>
		                    <td class='text-right'>${(value['cess_value'] == undefined ? "0.00" : value['cess_value'])}</td>
		                    <td>${value['pre_gst']}</td>
	                    </tr>`;
	            $('#grn_cdnur_tbl_tbody').append(row);
	            cdnur_note_value = parseFloat((value['net_inv_value'] ? value['net_inv_value'] : "0.00")) + cdnur_note_value;
	            cdnur_tax_value = parseFloat((value['taxable_value'] ? value['taxable_value'] : "0.00")) + cdnur_tax_value;
	            cdnur_duty_value = parseFloat((value['taxable_value'] ? (value['taxable_value'] * value['rate'])/100 : "0.00")) + cdnur_duty_value;
	            cdnur_cess_amt = parseFloat((value['cess_value'] ? value['cess_value'] : "0.00")) + cdnur_cess_amt;     
            });
            var foot_row = `<tr class='exclude-export'>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td class="text-right report-total-text">Total</td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cdnur_note_value).toFixed(2)}</b></td>
								<td></td>
								<td></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cdnur_tax_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cdnur_duty_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(cdnur_cess_amt).toFixed(2)}</b></td>
								<td></td>
							</tr>`;
			var total_header_for_excel = `<tr class='hide total-header gstr-report-head'>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Note Value</td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Taxable Value</td>
											<td data-style="reportHeader">Total Duty Value</td>
											<td data-style="reportHeader">Total Cess</td>
											<td data-style="reportHeader"></td>
										</tr>
										<tr class='hide gstr-report-head'>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td>${Number(cdnur_note_value).toFixed(2)}</td>
											<td></td>
											<td></td>
											<td>${Number(cdnur_tax_value).toFixed(2)}</td>
											<td>${Number(cdnur_duty_value).toFixed(2)}</td>
											<td>${Number(cdnur_cess_amt).toFixed(2)}</td>
											<td></td>
										</tr>`;		
			$("#gstr-cdnur-report thead").prepend(total_header_for_excel);		
			$('#grn_cdnur_tbl_tfoot').append(foot_row);
        }
        oTable6 = $('#gstr-cdnur-report').DataTable({
			fixedHeader: true,
			"pageLength": 50,
			"search": {
				"smart": false
			}
		});
		oTable6.on("draw",function() {
			var keyword = $('#gstr-cdnur-report_filter > label:eq(0) > input').val();
			$('#gstr-cdnur-report').unmark();
			$('#gstr-cdnur-report').mark(keyword,{});
		});
		oTable6.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
        oSettings6 = oTable6.settings();
        $( window ).resize();
        tableReadyStatus.push("cdnur");
        validateTableDownload();
    }


    function loopEXPTblData(data){
        var row = '';
        var exp_invoice_value=0, exp_taxable_value=0, exp_cess_value=0;
        if(data.length != 0){
            $.each(data, function (key, value) {
                row = `	<tr class='text-center'>
                			<td>${key+1}</td>
		                    <td>WPAY</td>
		                    <td>${value['invoice_code']}</td>
		                    <td data-sort='${moment(value['invoice_date']).format("YYYYMMDDHHmmss")}'>${moment(value['invoice_date']).format("DD/MM/YYYY")}</td>
		                    <td class='text-right'>${(value['invoice_value'] ? Number(value['invoice_value']).toFixed(2) : "Nil")}</td>
		                    <td>${value['port_code']}</td>
		                    <td></td>
		                    <td data-sort='${moment(value['issued_on']).format("YYYYMMDDHHmmss")}'>${moment(value['issued_on']).format("DD/MM/YYYY")}</td>
		                    <td></td>
		                    <td class='text-right'>${value['rate']}</td>
		                    <td class='text-right'>${(value['taxable_value'] == undefined ? "0.00" : Number(value['taxable_value']).toFixed(2))}</td>
		                    <td class='text-right'>${(value['cess_value'] == undefined ? "0.00" : value['cess_value'])}</td>
	                    </tr>`;
	            $('#grn_exp_tbl_tbody').append(row);
	            exp_invoice_value = parseFloat((value['invoice_value'] ? value['invoice_value'] : "0.00")) + exp_invoice_value;
	            exp_taxable_value = parseFloat((value['taxable_value'] ? value['taxable_value'] : "0.00")) + exp_taxable_value;
	            exp_cess_value = parseFloat((value['cess_value'] ? value['cess_value'] : "0.00")) + exp_cess_value;
            });
            var foot_row = `<tr class='exclude-export'>
								<td></td>
								<td></td>
								<td></td>
								<td class="text-right report-total-text">Total</td>
								<td class="text-right"><b style="font-size: 14px;">${Number(exp_invoice_value).toFixed(2)}</b></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(exp_taxable_value).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(exp_cess_value).toFixed(2)}</b></td>
							</tr>`;
			var total_header_for_excel = `<tr class='hide total-header gstr-report-head'>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Invoice Value</td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Taxable Value</td>
											<td data-style="reportHeader">Total Cess</td>
										</tr>
										<tr class='hide gstr-report-head'>
											<td></td>
											<td></td>
											<td></td>
											<td>Total</td>
											<td>${Number(exp_invoice_value).toFixed(2)}</td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td></td>
											<td>${Number(exp_taxable_value).toFixed(2)}</td>
											<td>${Number(exp_cess_value).toFixed(2)}</td>
										</tr>`;
			$("#gstr-exp-report thead").prepend(total_header_for_excel);	
			$('#grn_exp_tbl_tfoot').append(foot_row);
        }
        oTable7 = $('#gstr-exp-report').DataTable({
			fixedHeader: true,
			"pageLength": 50,
			"search": {
				"smart": false
			}
		});
		oTable7.on("draw",function() {
			var keyword = $('#gstr-exp-report_filter > label:eq(0) > input').val();
			$('#gstr-exp-report').unmark();
			$('#gstr-exp-report').mark(keyword,{});
		});
		oTable7.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
        oSettings7 = oTable7.settings();
        $( window ).resize();
        tableReadyStatus.push("exp");
        validateTableDownload();
    }

 	function loopEXEMPTblData(data){
        var row = '';
        var exemp_nil_rated=0, exemp_exempted=0, exemp_non_gst=0;
        if(data.length != 0){
            $.each(data, function (key, value) {
                row = ` <tr class='text-center'>
                			<td>${Number(key)+1}</td>
		                    <td>${value[0]}</td>
		                    <td class='text-right'>${(value[1] == undefined ? "0.00" : Number(value[1]).toFixed(2))}</td>
		                    <td class='text-right'>${(value[2] == undefined ? "0.00" : Number(value[2]).toFixed(2))}</td>
		                    <td class='text-right'>${(value[3] == undefined ? "0.00" : Number(value[3]).toFixed(2))}</td>
	                    </tr>`;
                $('#grn_exemp_tbl_tbody').append(row);
                exemp_nil_rated = parseFloat((value[1] ? value[1] : "0.00")) + exemp_nil_rated;
                exemp_exempted = parseFloat((value[2] ? value[2] : "0.00")) + exemp_exempted;
                exemp_non_gst = parseFloat((value[3] ? value[3] : "0.00")) + exemp_non_gst;
            });
            var foot_row = `<tr class='exclude-export'>
            					<td></td>
								<td class="text-right report-total-text">Total</td>
								<td class="text-right"><b style="font-size: 14px;">${Number(exemp_nil_rated).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(exemp_exempted).toFixed(2)}</b></td>
								<td class="text-right"><b style="font-size: 14px;">${Number(exemp_non_gst).toFixed(2)}</b></td>
							</tr>`;
			var total_header_for_excel = `<tr class='hide total-header gstr-report-head'>
			            					<td data-style="reportHeader"></td>
											<td data-style="reportHeader"></td>
											<td data-style="reportHeader">Total Nil Rated Supplies</td>
											<td data-style="reportHeader">Total Exempted Supplies</td>
											<td data-style="reportHeader">Total Non-GST Supplies</td>
										</tr>
										<tr class='hide gstr-report-head'>
			            					<td></td>
											<td></td>
											<td>${Number(exemp_nil_rated).toFixed(2)}</td>
											<td>${Number(exemp_exempted).toFixed(2)}</td>
											<td>${Number(exemp_non_gst).toFixed(2)}</td>
										</tr>`;
			$("#gstr-exemp-report thead").prepend(total_header_for_excel);	
			$('#grn_exemp_tbl_tfoot').append(foot_row);
        }
        oTable8 = $('#gstr-exemp-report').DataTable({
			fixedHeader: true,
			"pageLength": 50,
			"search": {
				"smart": false
			}
		});
		oTable8.on("draw",function() {
			var keyword = $('#gstr-exemp-report_filter > label:eq(0) > input').val();
			$('#gstr-exemp-report').unmark();
			$('#gstr-exemp-report').mark(keyword,{});
		});
		oTable8.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
        oSettings8 = oTable8.settings();
        $( window ).resize();
        tableReadyStatus.push("exemp");
        validateTableDownload();
    }

    $("#gstr1salesreportview").click(function () {
    	if(oTable1 != undefined) {
         	oTable1.destroy();
        }
        if(oTable2 != undefined) {
         	oTable2.destroy();
        }
        if(oTable3 != undefined) {
         	oTable3.destroy();
        }
        if(oTable4 != undefined) {
         	oTable4.destroy();
        }
        if(oTable5 != undefined) {
         	oTable5.destroy();
        }
        if(oTable6 != undefined) {
         	oTable6.destroy();
        }
        if(oTable7 != undefined) {
         	oTable7.destroy();
        }
        if(oTable8 != undefined) {
         	oTable8.destroy();
        }
        tableReadyStatus = [];
        setDownloadButtonToDisabled();
        $("#loading").removeClass('hide');
        updateFilterText();
        from_date = $('.fromdate').val();
        to_date = $('.todate').val();
        $('.gstr-report-body, .gstr-report-foot').html("");
        $(".gstr-report-head").remove();
        // b2b Report
        $.ajax({
            url : "/erp/accounts/json/gstr1_b2b_report/",
            type : "POST",
            dataType: "json",
            data : {
                from_date: from_date,
                to_date: to_date,
                report_type:'b2b'
            },
            success : function(data) {
                loopB2BTblData(data);
            },error : function(xhr,errmsg,err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });

        // b2cl Report
        $.ajax({
            url : "/erp/accounts/json/gstr1_b2cl_report/",
            type : "POST",
            dataType: "json",
            data : {
                from_date: from_date,
                to_date: to_date,
                report_type:'b2cl'
            },
            success : function(data) {
                loopB2CLTblData(data);
            },error : function(xhr,errmsg,err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });

        // b2cs Report
        $.ajax({
            url : "/erp/accounts/json/gstr1_b2cs_report/",
            type : "POST",
            dataType: "json",
            data : {
                from_date: from_date,
                to_date: to_date,
                report_type:'b2cs'
            },
            success : function(data) {
                loopB2CSTblData(data);
            },error : function(xhr,errmsg,err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });

        // hsn Report
        $.ajax({
            url : "/erp/accounts/json/gstr1_hsn_report/",
            type : "POST",
            dataType: "json",
            data : {
                from_date: from_date,
                to_date: to_date,
                report_type:'hsn'
            },
            success : function(data) {
                loopHSNTblData(data);
            },error : function(xhr,errmsg,err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });

        // exp Report
        $.ajax({
            url : "/erp/accounts/json/gstr1_exp_report/",
            type : "POST",
            dataType: "json",
            data : {
                from_date: from_date,
                to_date: to_date,
                report_type:'exp'
            },
            success : function(data) {
                loopEXPTblData(data);
            },error : function(xhr,errmsg,err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });

        // cdnur Report
        $.ajax({
            url : "/erp/accounts/json/gstr1_cdnur_report/",
            type : "POST",
            dataType: "json",
            data : {
                from_date: from_date,
                to_date: to_date,
            },
            success : function(data) {
                loopCDNURTblData(data);
            },error : function(xhr,errmsg,err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });

        // cdnr Report
        $.ajax({
            url : "/erp/accounts/json/gstr1_cdnr_report/",
            type : "POST",
            dataType: "json",
            data : {
                from_date: from_date,
                to_date: to_date,
            },
            success : function(data) {
                loopCDNRTblData(data);
            },error : function(xhr,errmsg,err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });

        // exemp Report
        $.ajax({
            url : "/erp/accounts/json/gstr1_exemp_report/",
            type : "POST",
            dataType: "json",
            data : {
                from_date: from_date,
                to_date: to_date,
            },
            success : function(data) {
                loopEXEMPTblData(data);
            },error : function(xhr,errmsg,err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });
        closeFilterOption();
        $("#loading").addClass('hide');
        ga('send', 'event', 'Accounts', 'View GSTR1 Sales Report', $('#enterprise_label').val(), 1);
    });

	function tablesToExcelLoad() {
		if(!($(".gstr-xsl-download").hasClass("disabled"))) {
			$(".downloading-main-container").removeClass('hide').addClass('show');
			removeDataTable();
			setTimeout(function(){
				tablesToExcel(['gstr-b2b-report','gstr-b2cl-report','gstr-b2cs-report','gstr-hsn-report','gstr-cdnr-report','gstr-cdnur-report','gstr-exp-report','gstr-exemp-report'], ['B2B','B2CL','B2CS','HSN','CDNR','CDNUR','EXP','EXEMP'], 'GSTR-1 Sales Report.xls', 'Excel');
			},100);
		}
	}

  	var tablesToExcel = (function() {
    var uri = 'data:application/vnd.ms-excel;base64,'
    , tmplWorkbookXML = '<?xml version="1.0" encoding="windows-1252"?><?mso-application progid="Excel.Sheet"?>'
	  + '	<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel"  xmlns:html="http://www.w3.org/TR/REC-html40">'
    + '		<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">'
	  + '			<Author>Qompare</Author>'
	  + '			<Created>{created}</Created>'
	  + '		</DocumentProperties>'
    + '		<Styles>'
	  + '			<Style ss:ID="Default" ss:Name="Normal">'
	  + '				<NumberFormat ss:Format=""/>'
	  + '			</Style>'
	  + '			<Style ss:ID="reportHeader">'
	  + '				<Alignment ss:WrapText="1" />'
    + '				<Borders>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Right"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Left"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Top"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Bottom"/>'
    + '				</Borders>'
	  + '				<Font ss:Bold="1" ss:Color="#FFFFFF"/>'
	  + '				<Interior ss:Color="#209be1" ss:Pattern="Solid" />'
	  + '				<NumberFormat/>'
	  + '				<Protection/>'
	  + '			</Style>'
	  + '			<Style ss:ID="tableHeader">'
	  + '				<Alignment ss:WrapText="1" />'
    + '				<Borders>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Right"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Left"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Top"/>'
	  + '					<Border ss:Color="#000000" ss:Weight="1" ss:LineStyle="Continuous" ss:Position="Bottom"/>'
    + '				</Borders>'
	  + '				<Font ss:Bold="1" ss:Color="#000000"/>'
	  + '				<Interior ss:Color="#cccccc" ss:Pattern="Solid" />'
	  + '				<NumberFormat/>'
	  + '				<Protection/>'
	  + '			</Style>'
    + '			<Style ss:ID="Changed">'
	  + '				<Borders/>'
	  + '				<Font ss:Color="#ff0000"></Font>'
	  + '				<Interior ss:Color="#99CC00" ss:Pattern="Solid"></Interior>'
	  + '				<NumberFormat/>'
	  + '				<Protection/>'
	  + '			</Style>'
    + '			<Style ss:ID="Missed">'
	  + '				<Borders/>'
	  + '				<Font ss:Color="#ff0000"></Font>'
	  + '				<Interior ss:Color="#ff0000" ss:Pattern="Solid"></Interior>'
	  + '				<NumberFormat/>'
	  + '				<Protection/>'
	  + '			</Style>'
    + '			<Style ss:ID="Decimals">'
	  + '				<NumberFormat ss:Format="Fixed"/>'
	  + '			</Style>'    
    + '	</Styles>' 
    + '	{worksheets}'
	  + '</Workbook>'
    , tmplWorksheetXML = '<Worksheet ss:Name="{nameWS}">'
	  + '	<ss:Table ss:DefaultColumnWidth="60">'
	  + '		{rows}'
	  + '	</ss:Table>'
	  + '</Worksheet>'
    , tmplCellXML = '			<ss:Cell{attributeStyleID}{attributeFormula}>'
	  + '				<ss:Data ss:Type="{nameType}">{data}</ss:Data>'
	  + '			</ss:Cell>'
    , base64 = function(s) { return window.btoa(unescape(encodeURIComponent(s))) }
    , format = function(s, c) { return s.replace(/{(\w+)}/g, function(m, p) { return c[p]; }) }
	    return function(tables, wsnames, wbname, appname) {
      var ctx = "";
      var workbookXML = "";
      var worksheetsXML = "";
      var rowsXML = "";

      for (var i = 0; i < tables.length; i++) {
        if (!tables[i].nodeType) tables[i] = document.getElementById(tables[i]);
        for (var j = 0; j < tables[i].rows.length; j++) {
        	if(tables[i].rows[j].classList.value.indexOf("exclude-export") == -1) {
        		if(tables[i].rows[j].classList.value.indexOf("total-header") != -1) {
	          		rowsXML += '<ss:Row ss:Height="50">'
	          	}
	          	else {
	          		rowsXML += '<ss:Row>'
	          	}
	          	for (var k = 0; k < tables[i].rows[j].cells.length; k++) {
	            	var dataType = tables[i].rows[j].cells[k].getAttribute("data-type");
	            	var dataStyle = tables[i].rows[j].cells[k].getAttribute("data-style");
	            	var dataValue = tables[i].rows[j].cells[k].getAttribute("data-value");
	            	dataValue = (dataValue)?dataValue:tables[i].rows[j].cells[k].innerHTML;
	            
	            	var dataFormula = tables[i].rows[j].cells[k].getAttribute("data-formula");
	            	dataFormula = (dataFormula)?dataFormula:(appname=='Calc' && dataType=='DateTime')?dataValue:null;
	            	ctx = {  attributeStyleID: (dataStyle=='Changed' || dataStyle=='Missed'|| dataStyle=='Header'|| dataStyle=='reportHeader'|| dataStyle=='tableHeader')?' ss:StyleID="'+dataStyle+'"':''
	                   , nameType: (dataType=='Number' || dataType=='DateTime' || dataType=='Boolean' || dataType=='Error')?dataType:'String'
	                   , data: (dataFormula)?'':dataValue
	                   , attributeFormula: (dataFormula)?' ss:Formula="'+dataFormula+'"':''
	                  };
	            	rowsXML += format(tmplCellXML, ctx);
	          	}
	          	rowsXML += '</ss:Row>'
          	}
        }
        ctx = {rows: rowsXML, nameWS: wsnames[i] || 'Sheet' + i};
        worksheetsXML += format(tmplWorksheetXML, ctx);
        rowsXML = "";
      }

      ctx = {created: (new Date()).getTime(), worksheets: worksheetsXML};
      workbookXML = format(tmplWorkbookXML, ctx);
      $(".dataTables_length").find("select").trigger('change');
      var link = document.createElement("A");
      link.href = uri + base64(workbookXML);
      link.download = wbname || 'Workbook.xls';
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      $(".downloading-main-container").removeClass('show').addClass('hide');
    }
  })();

  	function activateTabs() {
	    $('.nav-tabs')
	      .scrollingTabs({
	        enableSwiping: true
      	})
	      .on('ready.xs', function() {
	        $('.tab-content').show();
      	});
	    $("#ul-report-header").click(function(){
	      	setTimeout(function(){
	      		$( window ).resize();
	      	},10)
	    })
  	}

  	function  GeneralExportTableToCSVInit(current, field) {
  		if(!$(current)[0].hasAttribute("disabled")) {
	  		removeDataTable();
	  		GeneralExportTableToCSV.apply(current, field);
	  	}
  	}

  	function  removeDataTable() {
  		if(oTable1 != undefined) {
         	oSettings1[0]._iDisplayLength = oSettings1[0].fnRecordsTotal();
	        oTable1.draw();
        }
        if(oTable2 != undefined) {
         	oSettings2[0]._iDisplayLength = oSettings2[0].fnRecordsTotal();
	        oTable2.draw();
        }
        if(oTable3 != undefined) {
         	oSettings3[0]._iDisplayLength = oSettings3[0].fnRecordsTotal();
	        oTable3.draw();
        }
        if(oTable4 != undefined) {
         	oSettings4[0]._iDisplayLength = oSettings4[0].fnRecordsTotal();
	        oTable4.draw();
        }
        if(oTable5 != undefined) {
         	oSettings5[0]._iDisplayLength = oSettings5[0].fnRecordsTotal();
	        oTable5.draw();
        }
        if(oTable6 != undefined) {
         	oSettings6[0]._iDisplayLength = oSettings6[0].fnRecordsTotal();
	        oTable6.draw();
        }
        if(oTable7 != undefined) {
         	oSettings7[0]._iDisplayLength = oSettings7[0].fnRecordsTotal();
	        oTable7.draw();
        }
        if(oTable8 != undefined) {
         	oSettings8[0]._iDisplayLength = oSettings8[0].fnRecordsTotal();
	        oTable8.draw();
        }
  	}

  	function setDownloadButtonToDisabled(){
  		$(".gstr-xsl-download").addClass("disabled").attr("data-original-title", "Please wait... Your Report is not generated completely for download.");
  		$(".export_csv").attr("disabled","disabled").attr("data-original-title", "Please wait... Your Report is not generated completely for download.");
  	}

  	function  validateTableDownload() {
  		if(tableReadyStatus.length == $("#ul-report-header li").length) {
  			$(".gstr-xsl-download").removeClass("disabled");
  			$(".export_csv").removeAttr("disabled")
  			$(".gstr-xsl-download").attr("data-original-title", "Download consoldated report (as XLS)");
  			$(".gstr-b2b-download").attr("data-original-title", "Download GSTR B2B Report as CSV");
  			$(".gstr-b2cl-download").attr("data-original-title", "Download GSTR B2CL Report as CSV");
  			$(".gstr-b2cs-download").attr("data-original-title", "Download GSTR B2CS Report as CSV");
  			$(".gstr-hsn-download").attr("data-original-title", "Download GSTR HSN Report as CSV");
  			$(".gstr-cdnr-download").attr("data-original-title", "Download GSTR CDNR Report as CSV");
  			$(".gstr-cdnur-download").attr("data-original-title", "Download GSTR CDNUR Report as CSV");
  			$(".gstr-exp-download").attr("data-original-title", "Download GSTR EXP Report as CSV");
  			$(".gstr-exemp-download").attr("data-original-title", "Download GSTR EXEMP Report as CSV");
  		}
  	}

  	$('.nav-pills li').removeClass('active');
	$("#li_gstr_sales_report").addClass('active');
</script>
{% endblock %}