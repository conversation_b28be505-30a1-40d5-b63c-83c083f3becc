<!DOCTYPE html>
<style type="text/css">
	body{
		color: #000;
	}
	.auth_footer{
		font-size:{{ summary_res.totals.font_size }}px;
	}
	.auth_footer > hr{
		width:100%;
	}

	.pull-left {
		float: left;
	}

	.pull-right {
		float: right;
	}

	.text-left {
		text-align: left;
	}

	.text-right {
		text-align: right;
	}

	.text-center {
		text-align: center;
	}

	@font-face {
        font-family: 'Times New Roman';
        font-style: normal;
        font-weight: 400;
        src: url(https://www.xserp.in/site_media/fonts/times.ttf) format('truetype');
	}
	@font-face {
	        font-family: 'pdf_Roboto';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/roboto.regular.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Helvetica';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/roboto.regular.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Ubuntu';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/Ubuntu-Regular.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Comic Sans';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/comicsansms3.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_DejaVu Sans';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/DejaVuSans.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Courier New';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/cour.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Verdana';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/verdana.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Open Sans';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/open-sans-Regular.ttf) format('truetype');
	}
</style>
<script>
	function subst() {
		var vars={};
		var x=document.location.search.substring(1).split('&');
		for (var i in x) {
			var z=x[i].split('=',2);
			vars[z[0]] = unescape(z[1]);
		}
		var x=['frompage','topage','page','webpage','section','subsection','subsubsection'];
		for (var i in x) {
			var y = document.getElementsByClassName(x[i]);
			for (var j=0; j<y.length; ++j)
				y[j].textContent = vars[x[i]];

			if(document.getElementById("annexure_footer")){
				if(vars['page'] == 1){
				    document.getElementById("annexure_footer").style.display = 'block';
				    document.getElementById("footer").style.display = 'none';
				}
				else {
					document.getElementById("annexure_footer").style.display = 'none';
				    document.getElementById("footer").style.display = 'block';
				}
			}
			else{
				if(vars['page'] == 1 && vars['page'] == vars['topage']){
				    document.getElementById("page_footer").style.display = 'block';
				    document.getElementById("footer").style.display = 'none';
				}
				else if (vars['page'] == 1 && vars['page'] != vars['topage']) {
					document.getElementById("page_footer").style.display = 'none';
				    document.getElementById("footer").style.display = 'none';
				}
				else{
					if (vars['page'] == vars['topage']){
						document.getElementById("page_footer").style.display = 'none';
					    document.getElementById("footer").style.display = 'block';
					}
					else{
						document.getElementById("page_footer").style.display = 'none';
					    document.getElementById("footer").style.display = 'none';
					}
				}
			}
			if(document.getElementById("footer_banner_images_first")){
				if(vars['page'] == 1){
				   document.getElementById("footer_banner_images_first").style.display = 'block';
				}
				else {
				   document.getElementById("footer_banner_images_first").style.display = 'none';
				}
			}
			else {
				if(vars['page'] == vars['topage']){
				   document.getElementById("footer_banner_images_last").style.display = 'block';
				}
				else {
				   document.getElementById("footer_banner_images_last").style.display = 'none';
				}
			}
		}
	}
</script>
<body onload="subst()">
	<div {% if misc_res.print_summary_first_page and appendix_pages > 0 %} id="annexure_footer" {% else %} id="page_footer" {% endif %} class="col-sm-12 auth_footer" style="margin-top:16px; font-family: 'pdf_{{general_res.font_family}}'">
		<div style="width: 32.33333333%;display: inline-block;"></div>
		<div style="width: 32.33333333%;display: inline-block;"></div>
		<div style="width: 32.333%; display: inline-block; text-align: right; margin-right: 0">
			<span class="sign_company_name" style="word-break: break-all;">
				<i>For {{ source.enterprise.name }}</i>
			</span>
			<br />
			{% if header_res.field_name.authorized_sign.print %}
				{% if header_res.field_name.authorized_sign.print_approversign %}
					<div>
						<span class="show_approver_signature"><img src="{{ approver_signature }}" style="max-width: 100px;" /><br></span>
					</div>
				{% else %}
					<br /><br /><br />
				{% endif %}
				<span>
					{% if se_approver != '-NA-'%}{{ se_approver }}{% endif %}<br>
					{{ header_res.field_name.authorized_sign.label }}
				</span>
			{% endif %}
		</div>
		<hr style="margin:0;border: 1pt solid #000;"/>
	</div>
	<div id="footer" class=" auth_footer" style="margin-top:16px;width:100%; font-family: 'pdf_{{general_res.font_family}}'">
		<div style="width: 32.33333333%;display: inline-block;"></div>
		<div style="width: 32.333%;display: inline-block; "></div>
		<div style="width: 32.333%; display: inline-block; text-align: right; margin-right: 0 ; padding-right: 0;">
			<span class="sign_company_name" style="word-break: break-all;">
				<i>For {{ source.enterprise.name }}</i>
			</span>
			<br />
			{% if header_res.field_name.authorized_sign.print %}
				{% if header_res.field_name.authorized_sign.print_approversign %}
					<div>
						<span class="show_approver_signature"><img src="{{ approver_signature }}" style="max-width: 100px;" /><br></span>
					</div>
				{% else %}
					<br /><br /><br />
				{% endif %}
				<span>
					{% if se_approver != '-NA-'%}{{ se_approver }}{% endif %}<br>
					{{ header_res.field_name.authorized_sign.label }}
				</span>
			{% endif %}
		</div>
		<hr style="margin:0;border: 1pt solid #000;"/>
	</div>
	{% if footer_image_size != 0 %}
			{% if misc_res.print_summary_first_page and appendix_pages > 0 %}
				<div class="footer-banner-container col-sm-12" id="footer_banner_images_first">
					<div class="text-left pull-left misc_footer-left_banner {% if footer_left_image_width <= 0%}hide{% endif %}" style="padding-left: 0; width: {{footer_left_image_width}}%;" >
						<img class="footer_uploader-left img-responsive" src="{{footer_left_image}}" style="max-height: {{footer_image_size}}px;">
					</div>
					<div class="text-center pull-left misc_footer-center_banner {% if footer_center_image_width <= 0%}hide{% endif %}" style="width: {{footer_center_image_width}}%;" >
						<img class="footer_uploader-center img-responsive" src="{{footer_center_image}}" style="margin:0 auto; max-height: {{footer_image_size}}px;" >
					</div>
					<div class="text-right pull-right misc_footer-right_banner {% if footer_right_image_width <= 0%}hide{% endif %}" style="padding-right: 0; width: {{footer_right_image_width}}%;" >
						<img class="footer_uploader-right img-responsive" src="{{footer_right_image}}" style="float: right; max-height: {{footer_image_size}}px;">
					</div>
				</div>
			{% else %}
				<div class="footer-banner-container col-sm-12" id="footer_banner_images_last" style="margin-top: 6px;">
					<div class="text-left pull-left misc_footer-left_banner {% if footer_left_image_width <= 0%}hide{% endif %}" style="padding-left: 0; width: {{footer_left_image_width}}%;" >
						<img class="footer_uploader-left img-responsive" src="{{footer_left_image}}" style="max-height: {{footer_image_size}}px;">
					</div>
					<div class="text-center pull-left misc_footer-center_banner {% if footer_center_image_width <= 0%}hide{% endif %}" style="width: {{footer_center_image_width}}%;" >
						<img class="footer_uploader-center img-responsive" src="{{footer_center_image}}" style="margin:0 auto; max-height: {{footer_image_size}}px;" >
					</div>
					<div class="text-right pull-right misc_footer-right_banner {% if footer_right_image_width <= 0%}hide{% endif %}" style="padding-right: 0; width: {{footer_right_image_width}}%;" >
						<img class="footer_uploader-right img-responsive" src="{{footer_right_image}}" style="float: right; max-height: {{footer_image_size}}px;">
					</div>
				</div>
			{% endif %}
	{% endif %}
    <div class="clear-fix" style="clear: both"></div>
	<div class="col-sm-12 footer_notes remove-padding" style="font-size: {{ misc_res.foot_note.font_size }}pt; font-family: 'pdf_{{general_res.font_family}}'">{{ misc_res.foot_note.notes }}</div>
</body>