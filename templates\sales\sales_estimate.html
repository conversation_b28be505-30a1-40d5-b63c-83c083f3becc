{% extends "sales/sidebar.html" %}
{% block sales_estimate %}
<style type="text/css">
	.file {
	  visibility: hidden;
	  position: absolute;
	}

	.form-inline .fixed-width-medium,
	.form-inline .fixed-width-medium {
		width: 180px;
		max-width: 180px;
		min-width: 180px;
	}

	.btn-basic:focus {
		background: #209be1;
		color: #FFF;
	}

	.form-inline .chosen-container {
		display: inline-block;
		width: 180px !important;
	}

	@-moz-document url-prefix() {
	  .moz-style-width {
	     width: 396px !important;
	  }
	}

	.net_total {
		font-size: 34px;
		font-weight: bold;
		font-family: monospace;
	}

	.basic_total {
		font-size: 20px !important;
		font-weight: bold;
	}

	#add_new_material:focus,
	#add_new_material_ns:focus {
		background: #209be1;
		color: #FFF;
	}

	.bootstrap-filestyle input {
		width: 241px !important;
	}

	#se_particulars_table tbody tr td,
	#se_particulars_table_ns tbody tr td {
		vertical-align: top !important;
	}

	#se_particulars_table .custom-error-message,
	#se_particulars_table_ns .custom-error-message {
		position: relative;
	}
	#id_se-document_description {
		width: 100%;
	}
	.cke_textarea_inline {
	    pointer-events: none;
	    border: dashed 1px #999;
	    border-radius: 3px;
	    padding: 6px;
	    word-break: break-all;
	}
	.document_attachment_part {
	    display: inline-block;
	    width: 96px;
	    border: solid 1px #ccc;
	    padding: 10px 6px 0;
	    margin: 8px;
	    border-radius: 4px;
	    text-align: center;
	}
	.document_remove {
	    position: absolute;
	    margin-left: 30px;
	    margin-top: -19px;
	    cursor: pointer;
	    font-size: 16px;
	}
	.document_text {
	    display: block;
	    width: 75px;
	    text-overflow: ellipsis;
	    max-width: 75px;
	    overflow: hidden;
	    white-space: nowrap;
	}
	.no-value {
		font-weight: bold;
		font-size: 14px !important;
		padding-top: 10px !important;
		padding-bottom: 10px !important;
		color: #8a6d3b !important;
		background-color: #fcf8e3 !important;
		border-color: #faebcc !important;
	}
	#id_se_particular-__prefix__-all_units {
		padding: 4px 6px;
		height: 26px;
		box-shadow: 0 0;
		font-size: 12px;
	}
	.item_text_box,
	 .item-with-service input{
		width: calc(100% - 30px);
	}

	.service-item-flag.floated-right-flag {
		float: right;
    	margin-top: -20px;
	}
</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
<link rel="stylesheet" type="text/css" href="/site_media/css/erp_default.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-multiselect.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/sales_estimate.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/tags.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/party_list.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/usage_report.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/plugin/ck_editor/js/ckeditor.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/plugin/ck_editor/js/ckeditor_other.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/mailer.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/sales_estimate_document.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/load_material.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/Component/projects.js?v={{ current_version }}"></script>

<div style="display:none">
	<form id="se_submit" method="post" action="/erp/sales/sales_estimate/editSalesEstimate/">
		{%csrf_token%}
		<input type="hidden" value="{% if se.id.value %}{{se.id.value}}{% endif %}" id="id_se-id" name="se_no"/>
		<input type="submit" value="Edit" id="se_resubmit" hidden="hidden"/>
	</form>
</div>
<div class="right-content-container">
	<input type="hidden" value="{{selected_project_code}}" id="selected_project_code" hidden="hidden">
	<div class="page-title-container">
		<span class="page-title">{% if se.id.value == '' or se.id.value == None %} New {% endif %}Sales Estimate</span>
	</div>
	<div class="container-fluid" style="margin:0 15px;">
		<div class="page-heading_new" style="padding: 0;">
			<span class="page_header">
				<span class="header_current_page"> {% if se.id.value != '' and se.id.value != None %} {{ se.code.value }} {% endif %}</span>
			</span>
			{% if logged_in_user.is_super %}
				<a class="btn super_user_icon hide" onclick="editSENumber(true);" data-tooltip="tooltip" data-placement="bottom" data-title="Super User" style="" >
					<i class="fa fa-pencil"></i>
				</a>
				<div class="xsid_number_edit hide">
					<form class="form-inline" style="display: inline-block;" action="">
					    <div class="form-group">
					      	<input type="text" class="form-control" id="se_financial_year" name="se_financial_year" maxlength="5">
					    </div>
						<div class="form-group">
					      	<input type="text" class="form-control" id="se_text" name="se_text" maxlength="2" disabled="disabled">
					    </div>
					    <div class="form-group">
						    <select class="form-control" name="se_type" id="se_type" style="width: 60px;">
							   {% for type in se_type %}
							        <option value="{{type.0}}" data-val="{{type.2}}">&nbsp;{{type.2}} &nbsp;&nbsp;&nbsp;({{type.1}})</option>
							   {% endfor %}
						    </select>
							<input type="hidden" value="{{se.status.value}}" id="se_status"/>
						</div>
					    <div class="form-group">
					      	<input type="text" id="se_number" name="se_number" class="form-control" maxlength="6" onfocus="setNumberRangeOnFocus(this,6,0,true)" style="width: 90px;" />
					    </div>
					    <div class="form-group">
					      	<input type="text" class="form-control text-lowercase" onkeypress="validateStringOnKeyPress(this,event,'character');" onblur="validateStringOnBlur(this,event,'character');" id="se_number_division" style="width: 35px;" name="se_number_division" maxlength="1" >
					    </div>
					    <div class="form-group super_edit_submit_icon">
					    	<span role='button' data-tooltip="tooltip" title="Save" class="super_edit_save" onclick="SaveSENumber();"><i class="fa fa-check"  aria-hidden="true" style="color: green;"></i></span>
					    	<span role='button' data-tooltip="tooltip" title="Cancel" class="super_edit_cancel" onclick="DiscardEditSENumber();"><i class="fa fa-times" aria-hidden="true" style="color: red;"></i></span>
					    </div>
			  		</form>
			  		<div class="save_xsid_error_format hide">Required. Please check help tool for valid format.</div>
			  	</div>
			  	<div class="super_user_tool_tip hide" style="float: left;margin-top: 15px;"><span class="question-mark"></span></div>
			{%else%}
				<a class="btn super_user_icon hide" onclick="" style="color:#777;" data-tooltip="tooltip" data-placement="right" data-title="Only Super User can edit this field">
					<i class="fa fa-pencil"></i>
				</a>
			{% endif %}
			<a href="/erp/sales/sales_estimate/view/" class="btn btn-add-new pull-right" data-tooltip="tooltip" data-placement="bottom" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
			<span class="prev_next_container"></span>
			{% if access_level.edit %}
				<a role="button" onclick="javascript:showUploadSalesEstimationModal();" style="margin-right: 6px; font-size: 20px; padding: 2px 10px;" id="se-attachment-icon" class="btn btn-add-new pull-right view_material " data-tooltip="tooltip" title="Attachments"><i class="fa fa-paperclip" aria-hidden="true"></i></a>
			{% else %}
				<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in Profile Module. Please contact the administrator.">
					<a role="button" style="margin-right: 6px; font-size: 20px; padding: 2px 10px;" id="se-attachment-icon" class="btn btn-add-new pull-right view_material "><i class="fa fa-paperclip" aria-hidden="true"></i></a>
				</div>
			{% endif %}
			{% if access_level.edit and se.status.value == 3 %}
				<div class="pull-right" style="margin-right:10px;">
					<a role="button" onclick="javascript:validateExpiryDate('promote_to_oa');" class="btn btn-save">Promote to OA</a><br>
					<a role="button" class= "oa_value" data-tooltip="tooltip" data-placement="bottom" title="Order Accepted" style="font-size:12px;text-align: right;float: right;">{{oa_total}}</a>
				</div>
				<div class=" pull-right" style="margin-right:24px;margin-right:10px;">
					<a role="button" onclick="javascript:validateExpiryDate('promote_to_invoice');" class="btn btn-save" >Promote to Invoice</a><br>
					<a role="button" class= "invoice_value" data-tooltip="tooltip" data-placement="bottom" title="Invoiced" style="font-size:12px;float:right;">{{invoice_total}}</a>
			</div>
			{% endif %}
		</div>
		<form id="se_add"  method="post" action="/erp/sales/sales_estimate/addSalesEstimate/" class="col-sm-12 remove-padding">
			{%csrf_token %}
			<div class="row">
				<div class="col-sm-8"><!-- Tags are hidden on the 2.16.3 release -->
					<div class="col-sm-12 form-group hide" style="padding: 0;" id="tags">
						<label>Tags</label>
						<label class="ui-autocomplete-loading hide">&nbsp;</label>
						<ul id="ul-tagit-display" class="tagit-display form-control">
						   {% for tag_form in tags_formset.initial_forms %}
						   <li class="li-tagit-display" id="{{ tag_form.prefix }}">
						      <div hidden="hidden">{{ tag_form.tag }}
						         {{ tag_form.DELETE }}{{ tag_form.ORDER }}</div>
						      <label class="label-tagit-display"  id="id_{{tag_form.prefix}}-tag_label">{{ tag_form.tag.value }}</label>&nbsp;
						      <a class="delete_tag" id="{{tag_form.prefix}}-del_button"></a>
						      &nbsp;
						   </li>
						{% endfor %}
						   <li id="tag-__dummy__" hidden="hidden">
						      <div hidden="hidden">{{ tags_formset.empty_form.tag }}
						      {{ tags_formset.empty_form.ORDER }}
						      {{ tags_formset.empty_form.DELETE }}</div>
						      <label class="label-tagit-display"  id="id_{{tags_formset.empty_form.prefix}}-tag_label"></label>&nbsp;
						      <a class="delete_tag" id="{{tags_formset.empty_form.prefix}}-del_button"></a>
						      &nbsp;
						   </li>
						<span>
						   {{ tags_formset.management_form }}
						   <span id="{{ tags_formset.empty_form.prefix }}" class="text_for_tag" >
						      {{ tags_formset.empty_form.tag }}
						      {{ tags_formset.empty_form.ORDER }}
						      <span hidden="hidden">{{ tags_formset.empty_form.DELETE }}</span>
						   </span>
						</span>
						</ul>
						<span style="position: absolute; margin-top: -8px; color: #aaa; font-size: 11px;">Type and press enter to add new tag.</span>
					</div>
					<div class="col-sm-2 hide" style="margin-top: 21px;">
						<input id="add_tag" class="btn btn-save btn-margin-1" value="+" type="button">
					</div>
				</div>
				{% if se.id.value != '' and se.id.value != None %}
				<div class="col-md-4">
					<table border="0" class="side-table" style="float: right; margin-bottom: 15px;">
						<tr>
							<td class="side-header" style="width: 150px;"> Estimate #</td>
							<td class="side-content">{{ se.code.value }}</td>
						</tr>
						<tr>
							<td class="side-header">Est.Date</td>
							<td class="side-content side-content-datepicker ">{{se.approved_on.value|date:'M d, Y H:i:s'}}
							</td>
						</tr>
					</table>
				</div>
				{% else %}
					&nbsp;
				{% endif %}
			</div>
			<div class="row">
				<div class="col-sm-3 form-group">
					<label>
						{% if logged_in_user.is_super %}
							<a class="super_edit_field super_edit_party hide" onclick="SuperEditSEDetails(this);" data-tooltip="tooltip" data-placement="left" title="Super Edit">
								<i class="fa fa-pencil super_edit_in_field"></i>
							</a>
						{%else%}
							<a class="super_edit_field super_edit_party hide"  onclick="" data-tooltip="tooltip" data-placement="left" title="Only Super User can edit this field">
								<i class="fa fa-pencil super_edit_in_field" style="color:#777;"></i>
							</a>
						{%endif%}
						Party Name<span class="mandatory_mark"> *</span>
					</label>
					<div class="{% if se.status.value > 1 %}div-disabled{% endif %}">
					{{se.party_id}}
					</div>
					{{se.enterprise_id}}
					{{se.id}}
					{{se.prepared_on}}
					{{se.se_no}}
				</div>
				<div class="col-sm-3 form-group">
					<label>Project<span class="mandatory_mark"> *</span></label>
					<div class="{% if se.status.value > 1 %}div-disabled{% endif %}">
						<div class="tour_project_tag">
							<div class="component_project" data-id="id_se-project_code" data-value="" data-name="se-project_code" data-value="selected_project_code"></div>
							<label id="expRev" style="display: block;margin-top: 5px;">
								Revenue: <span id="revenue" style="margin-right: 10px;">0</span>
								Expense : <span id="expense" style="margin-right: 10px;">0</span>
							</label>
							<span hidden="hidden" id="cashflow_amount" style="margin-right: 10px;">0</span>
							<span hidden="hidden" id="cash_allocated" style="margin-right: 10px;">0</span>
						</div>
					</div>
				</div>
				<div class="col-sm-3">
					<label>
						{% if logged_in_user.is_super %}
							<a class="super_edit_field super_edit_type hide" onclick="SuperEditSEDetails(this);" data-tooltip="tooltip" data-placement="left" title="Super Edit">
								<i class="fa fa-pencil super_edit_in_field"></i>
							</a>
						{%else%}
							<a class="super_edit_field super_edit_type hide" onclick="" data-tooltip="tooltip" data-placement="left" title="Only Super User can edit this field">
								<i class="fa fa-pencil super_edit_in_field" style="color:#777;"></i>
							</a>
						{%endif%}
							Type<span class="mandatory_mark"> *</span>
					</label>
					<div class="{% if se.status.value > 1 %}div-disabled{% endif %}">
						{{se.type}}
					</div>
				</div>
				<div class="col-sm-3 form-group">
					<label>Purpose</label>
					{% if se.purpose.value != '' and se.purpose.value != None %}
					<input type="text" class="form-control"  value="{{ se.purpose.value }}" id="id_se-purpose" name="se-purpose" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')"/>
					{% else %}
						{{ se.purpose }}
					{% endif %}
					<datalist id="frequents_purpose_list">
					{% for purpose in frequents_purpose %}
						<option value="{{purpose}}">
					{% endfor %}
					</datalist>
				</div>
			</div>
			<div class="row">
				<input type="hidden" value="{{se.status.value}}" id="id_se_status"/>
				<div class="col-md-3 form-group multiselect_option" id="spl_div">
					<label id="sp_display">Sales Person</label>
						{% if access_level.edit %}
							<span role="button" style="float:right;"  onclick="addNewSalesPerson()"><a>+ New Contact</a></span>
						{% endif %}
					<div id="add_sales_person_modal" class="modal fade" role="dialog">
					    <div class="modal-dialog">
					        <div class="modal-content">
					            <div class="modal-header">
					                <button type="button" class="close" data-dismiss="modal">&times;</button>
					                <h4 class="modal-title"> New Contact<div class="attachment-title" style="text-shadow: 0 0 #000;"></div></h4>
					            </div>
					            <div class="modal-body">
						            <div class="col-sm-6 form-group">
							            <label>Contact Person</label><span class="mandatory_mark"> *</span>
							            <input class="form-control" id="id_contact_name" placeholder="Enter Contact Person" maxlength="48" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');"  type="text" value="">
							        </div>
						            <div class="col-sm-6 form-group">
							            <label>Email Address</label>
									    <input class="form-control" id="id_contact_email" placeholder="Enter Email Address" maxlength="48" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" type="text" value="">
						            </div>
						            <div class="col-sm-6 form-group">
							            <label>Contact Number</label><span class="mandatory_mark"> *</span>
									    <input class="form-control"  id="id_contact_no" placeholder="Enter Contact Number" maxlength="18" onblur="return validateStringOnBlur(this, event, 'ph_number');" onkeypress="return validateStringOnKeyPress(this,event, 'ph_number');" type="text" value="">
						            </div>
						            <div class="col-sm-6 form-group">
							            <label>Fax</label>
									    <input class="form-control" id="id_contact_fax_no" placeholder="Fax" maxlength="18"  onblur="return validateStringOnBlur(this, event, 'ph_number');" onkeypress="return validateStringOnKeyPress(this,event, 'ph_number');" type="text" value="">
						            </div>
					            </div>
					            <div class="modal-footer">
						            <a href="javascript:void(0);" class="btn btn-save" onclick="saveNewContact()" id="add_contact">Save</a>
					                <a href="javascript:void(0);" class="btn btn-cancel" class="close" data-dismiss="modal">Close</a>
					            </div>
					        </div>
					    </div>
					</div>

					<select class="form-control" name="se_contact_id_list" id="sales_estimate_person" multiple="multiple">
						{% for enterprise_contact in enterprise_contacts_list %}
						<option value="{{enterprise_contact.contact_id}}">{{enterprise_contact.name}}</option>
						{% endfor %}
					</select>
					<input type="label" class="sales_person_details" id="sales_person_details" width="30%" hidden="hidden" value="{{se_contacts_list}}">
				</div>
				<div class="col-sm-3 form-group">
					<label>Expiry Date</label>
					{{se.expiry_date}}
					<input class="form-control custom_datepicker from-today"  type="text" placeholder="Select Date"  id="expiry_date" readonly="readonly">
					<i class="glyphicon glyphicon-calendar custom-calender-icon"></i>
				</div>
				<div class="col-sm-3 form-group">
					<label>Ref #</label>
					{{se.ref_no}}
				</div>
				<div class="col-sm-3 form-group">
					<label>Ref Date</label>
					{{se.ref_date}}
					<input type="text" class="form-control custom_datepicker" placeholder="Select Date" id="ref_date" readonly="readonly" >
					<i class="glyphicon glyphicon-calendar custom-calender-icon" ></i>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12">
					<div style="padding: 10px 5px;">
						<ul class="nav nav-tabs list-inline">
							<li class="active"><a data-toggle="tab" href="#tab1" id="tabs_stock">Item Particulars</a></li>
							<li style="margin-top: 11px"><span class='service-item-flag'></span> - Service</li>
						</ul>
					</div>
					<div class="tab-content">
						<div id="tab1" class="tab-pane fade in active">
							<table class="table custom-table table-bordered" id="se_particulars_table">
								<thead>
									<tr>
										<th width="50px">S.No.</th>
										<th>Item Name</th>
										<th style="width: 100px;" class="textbox_header_amt">HSN/SAC</th>
										<th class="textbox_header_amt">Quantity</th>
										<th class="textbox_header_amt">UoM</th>
										<th class="textbox_header_amt">Unit Price</th>
										<th style="width: 100px;" class="textbox_header_amt">Disc.(%)</th>
										<th class="textbox_header_amt">Total Price</th>
										<th width="50px">ACTION</th>
									</tr>
								</thead>
								<tbody class="item-for-goods hide">
								{{ se_particular_formset.management_form }}
									<!-- Item editing List -->
									{% for se_particulars_form in se_particular_formset.initial_forms %}
										{% if se_particulars_form.is_service.value == "False" %}
										<tr bgcolor="#ececec" id="{{se_particulars_form.prefix}}">
											<td hidden="hidden">
												{{ se_particulars_form.se_id }}
												{{ se_particulars_form.DELETE }}
												{{ se_particulars_form.enterprise_id }}
												{{ se_particulars_form.make_label }}
												{{ se_particulars_form.make_id }}
												{{ se_particulars_form.is_faulty }}
												{{ se_particulars_form.is_service }}
												{{ se_particulars_form.item_id }}
												{{ se_particulars_form.alternate_unit_id }}
												{{ se_particulars_form.scale_factor }}
												{{ se_particulars_form.alternate_unit_list }}
												<input name="item_tax"  type="text" hidden="hidden" id="id_{{se_particulars_form.prefix}}-item_tax">
											</td>
											<td class="text-center">{{forloop.counter}}</td>
											<td class="table_text_box">
												{{ se_particulars_form.item_code }}
												{{ se_particulars_form.item_name }}
											</td>
											<td hidden="hidden" class='text-left'><div id="id_{{ se_particulars_form.prefix }}-makeLabel"></div></td>
											<td class="table_text_box td-hsn-code hsn-wrapper">
												{{ se_particulars_form.hsn_code }}
											</td>
											<td class="table_text_box">
												{{ se_particulars_form.quantity }}
											</td>
											<td class="text-center table_text_box">
												{{ se_particulars_form.unit_id }}
											</td>
											<td class="table_text_box">
												{{ se_particulars_form.unit_price }}
											</td>
											<td class="table_text_box">
												{{ se_particulars_form.discount }}
											</td>
											<td class="text-right table_text_box">
												{{se_particulars_form.price }}
											</td>
											<td class="text-center">
												<a href="#" tabindex="-1"
												   id="id_{{ se_particulars_form.prefix }}-deleteSEParticulars"
												   onclick="javascript:deleteSEParticulars('{{ se_particulars_form.prefix }}')">
													<i class="fa fa-trash-o"></i>
												</a>
											</td>
										</tr>
										{% endif %}
									{% endfor %}

								</tbody>
								<tbody class="item-for-service hide">
									{% for se_particulars_form in se_particular_formset.initial_forms %}
										{% if  se_particulars_form.is_service.value == "True" %}
										<tr bgcolor="#ececec" id="{{se_particulars_form.prefix}}">
											<td hidden="hidden">
												{{ se_particulars_form.se_id }}
												{{ se_particulars_form.DELETE }}
												{{ se_particulars_form.enterprise_id }}
												{{ se_particulars_form.make_label }}
												{{ se_particulars_form.make_id }}
												{{ se_particulars_form.is_faulty }}
												{{ se_particulars_form.is_service }}
												{{ se_particulars_form.item_id }}
												{{ se_particulars_form.alternate_unit_id }}
												{{ se_particulars_form.scale_factor }}
												{{ se_particulars_form.alternate_unit_list }}
												<input name="item_tax"  type="text" hidden="hidden" id="id_{{se_particulars_form.prefix}}-item_tax">
											</td>
											<td class="text-center">{{forloop.counter}}</td>
											<td class="table_text_box item-with-service">
												{{ se_particulars_form.item_code }}
												{{ se_particulars_form.item_name }}
												<span class="service-item-flag floated-right-flag"></span>
											</td>
											<td hidden="hidden" class='text-left'><div id="id_{{ se_particulars_form.prefix }}-makeLabel"></div></td>
											<td class="table_text_box td-hsn-code hsn-wrapper">
												{{ se_particulars_form.hsn_code }}
											</td>
											<td class="table_text_box">
												{{ se_particulars_form.quantity }}
											</td>
											<td class="text-center table_text_box">
												{{ se_particulars_form.unit_id }}
											</td>
											<td class="table_text_box">
												{{ se_particulars_form.unit_price }}
											</td>
											<td class="table_text_box">
												{{ se_particulars_form.discount }}
											</td>
											<td class="text-right table_text_box">
												{{se_particulars_form.price }}
											</td>
											<td class="text-center">
												<a href="#" tabindex="-1"
												   id="id_{{ se_particulars_form.prefix }}-deleteSEParticulars"
												   onclick="javascript:deleteSEParticulars('{{ se_particulars_form.prefix }}')">
													<i class="fa fa-trash-o"></i>
												</a>
											</td>
										</tr>
										{% endif %}
									{% endfor %}
								</tbody>
								<tfoot>
									<tr hidden="hidden" bgcolor="#ececec" id="se_particular-__dummy__">
										<td hidden="hidden">
											{{ se_particular_formset.empty_form.se_id }}
											{{ se_particular_formset.empty_form.DELETE }}
											{{ se_particular_formset.empty_form.enterprise_id }}
											{{ se_particular_formset.empty_form.make_id }}
											{{ se_particular_formset.empty_form.is_faulty }}
											{{ se_particular_formset.empty_form.is_service }}
											{{ se_particular_formset.empty_form.item_id }}
											{{ se_particular_formset.empty_form.alternate_unit_id }}
											{{ se_particular_formset.empty_form.scale_factor }}
											{{ se_particular_formset.empty_form.alternate_unit_list }}
											<input name="item_tax" id="id_se_particular-__prefix__-item_tax" type="text" hidden="hidden">
										</td>
										<td class="text-center"><div id="id_se_particular-__prefix__-s_no"></div></td>
										<td class="table_text_box">
											{{ se_particular_formset.empty_form.item_code }}
											{{ se_particular_formset.empty_form.item_name}}
										</td>
										<td class="table_text_box td-hsn-code hsn-wrapper">
											<div>{{ se_particular_formset.empty_form.hsn_code }}</div>
										</td>
										<td class="table_text_box">
											{{ se_particular_formset.empty_form.quantity }}
										</td>
										<td class="table_text_box">
											{{ se_particular_formset.empty_form.unit_id }}
											<div class="alternate_unit_select_box hide">
												{{ se_particular_formset.empty_form.alternate_units }}
											</div>
										</td>
										<td class="table_text_box">
											{{ se_particular_formset.empty_form.unit_price}}
										</td>
										<td class="table_text_box">
											<div>{{ se_particular_formset.empty_form.discount }}</div>
										</td>
										<td class="text-right table_text_box">
											{{se_particular_formset.empty_form.price }}
										</td>
										<td class="text-center">
											<a href="#" tabindex="-1"
											   id="id_{{ se_particular_formset.empty_form.prefix }}-deleteSEParticulars"
											   onclick="javascript:deleteSEParticulars('{{ se_particular_formset.empty_form.prefix }}')">
												<i class="fa fa-trash-o"></i>
											</a>
										</td>
									</tr>
									<tr bgcolor="#ececec" id="{{se_particular_formset.empty_form.prefix}}">
										<td hidden="hidden">
											{{ se_particular_formset.empty_form.se_id }}
											{{ se_particular_formset.empty_form.DELETE }}
											{{ se_particular_formset.empty_form.enterprise_id }}
											{{ se_particular_formset.empty_form.make_id }}
											{{ se_particular_formset.empty_form.is_faulty }}
											{{ se_particular_formset.empty_form.is_service }}
											{{ se_particular_formset.empty_form.item_id }}
											<input type="text" value="" class="" id="material_id_hidden" placeholder="" hidden="hidden">
											{{ se_particular_formset.empty_form.alternate_unit_id }}
											{{ se_particular_formset.empty_form.scale_factor }}
											{{ se_particular_formset.empty_form.alternate_unit_list }}
										</td>
										<td class="text-center">{{forloop.counter}}</td>
										<td class="table_text_box">
											{{ se_particular_formset.empty_form.item_code }}
											{{ se_particular_formset.empty_form.item_name}}
											<span class="material-removal-icon removal-icon-table hide" style="padding: 4px 15px; margin-top: -26px;">
												<i class="fa fa-times"></i>
											</span>
										</td>
										<td class="table_text_box td-hsn-code hsn-wrapper">
											{{ se_particular_formset.empty_form.hsn_code }}
										</td>
										<td class="table_text_box">
											{{ se_particular_formset.empty_form.quantity }}
										</td>
										<td class="table_text_box">
											{{ se_particular_formset.empty_form.unit_id }}
											<div class="all_units_select_box hide">
												{{ se_particular_formset.empty_form.all_units }}
											</div>
											<div class="alternate_unit_select_box hide">
												{{ se_particular_formset.empty_form.alternate_units }}
											</div>
										</td>
										<td class="table_text_box">
											{{ se_particular_formset.empty_form.unit_price}}
											<input type="hidden" id="material_rate_hidden" class="form-control"/>
										</td>
										<td class="table_text_box">
											{{ se_particular_formset.empty_form.discount }}
										</td>
										<td class="text-right table_text_box">
											{{se_particular_formset.empty_form.price }}
										</td>
										<td></td>
									</tr>
									<tr class="se_new_content">
										<td style="border: none;"></td>
										<td colspan="2"  style="border: none;">
											<span id="description_display" placeholder="Display Description" class="textbox-as-label" style="background-color: #eee; cursor: text;"></span>
										</td>
										<td colspan="3" style="border: none;"></td>
										<td class="grand-total-text text-center" style="vertical-align: middle !important;"><b>TOTAL</b></td>
										<td class="text-right se_total_amt grand-total-amount" style="width: auto; vertical-align: middle !important;">0.00</td>
										<td class="text-center"><input type="button" class="btn" id="add_new_material" onclick="AddNewMateriaRow();" value="Add"></td>
									</tr>
								</tfoot>
							</table>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-8 remove-padding">
					<div class="col-md-12 remove-padding">
						<div class="col-md-3 remove-padding-right">
							<label>
								{% if logged_in_user.is_super %}
								    <a class="super_edit_field super_edit_for_load " onclick="SuperEditSEDetails(this);"  data-tooltip="tooltip" data-placement="left"  title="Super Edit" style="margin-right: -13px;margin-top: 19px">
										<i class="fa fa-pencil super_edit_in_field"></i>
									</a>
								{%else%}
									<a class="super_edit_field super_edit_for_load" onclick=""  data-tooltip="tooltip" data-placement="top"  title="Only Super User can edit this field" style="margin-right: -13px;margin-top: 19px">
										<i class="fa fa-pencil super_edit_in_field" style="color:#777;"></i>
									</a>
								{%endif%}
								Currency</label>
							<span class="div-disabled">
						    {{ se.currency_id}}
							</span>
                            <div id="div_con_rate" class="conversion_rate_container hide">
                                <span class="currency_convertor_section">
                                    <span>1</span>
                                    <span class="converted_currency_txt"></span>
                                    <span>=</span>
                                </span>
                                <span>{{ se.currency_conversion_rate }}</span>
                                <span class="base_currency_txt"></span>
                            </div>
						</div>
						<div class="col-md-4 form-group">
							<label>Payment Terms</label>
							{{se.payment_terms}}
						</div>
						<div hidden="hidden">
							{{ tax_list.empty_form.tax_code }}
							{{ tax_list.empty_form.enterprise_id }}
							{{ tax_list.empty_form.se_id }}
						</div>

						<div class="col-md-4">
							<div class="row form-group">
								<div class="col-md-10 taxable non-form-element" id="tax_choices" style="padding-left: 0;">
									<label>Taxes</label>
									{{ tax_list.management_form }}
									<select class="form-control chosen-select" name="select" id="se_tax_list">
										{% for tax in load_tax %}
										<option value="{{ tax.0 }}">{{ tax.1 }}</option>
										{% endfor %}
									</select>
								</div>
								<div class="col-md-2 taxable remove-padding-left">
									<span class="btn btn-add-tax" id="add_se_tax" data-tooltip="tooltip" title="Add tax">
										<i class="fa fa-angle-double-right" aria-hidden="true"></i>
									</span>
								</div>
							</div>
						</div>
					</div>

					<div class="form-group col-md-6">
						<label>Remarks</label>
					    <div class="remarks_count_link remarks_count disabled" onclick="remarks_history();">
							<span class="remarks_counter">No</span><span> remarks</span>
						</div>
						{{se.remarks}}
					</div>
					<div class="form-group col-md-6">
						<label id="lbl_special_instruction">Special Instruction</label>
						{{se.special_instructions}}
					</div>
					{% if type != 'internal' %}
						<div class="form-group col-md-12">
							<label>Notes</label>
						    <div style="float: right; margin-top: -1px; background: #FFF; margin-right: -8px; color: #004195; cursor: pointer;" onclick="openNotesOverlay();">
								<i class="fa fa-pencil super_edit_in_field" style="margin-top: -3px; margin-right: 15px;"></i>
							</div>
							{{se.notes}}
						</div>
					{% endif %}
				</div>
				<div class="col-md-4 remove-padding">
					<div id="se_taxes" style="padding-left: 0;">
						<div style="padding-right: 0;">
							<div class="col-md-12 po_taxes_table">
								<table id="se_tax_table" border="0" width="100%" class="taxable ">
									<tbody>
										<tr id="se_tax-__dummy__">
											<td hidden="hidden">
												{{ tax_list.empty_form.DELETE }}
												{{ tax_list.empty_form.enterprise_id }}
												{{ tax_list.empty_form.se_id }}
												{{ tax_list.empty_form.tax_code}}
												<a href="#"
												   id="id_{{ tax_list.empty_form.prefix }}-deleteSETax"
												   onclick="javascript:deleteSETax('{{ tax_list.empty_form.prefix }}')">
													<img src="/site_media/images/deleteButton.png"
													     title="Delete" alt="Delete"
													     align="center"/></a>
											</td>
										</tr>
										{% for tax_form in tax_list.initial_forms %}
										<tr id="{{ tax_form.prefix }}">
											<td hidden="hidden">
												{{ tax_form.DELETE }}
												{{ tax_form.enterprise_id }}
												{{ tax_form.se_id }}
												{{ tax_form.tax_code }}
												<a href="#"
												   id="id_{{ tax_form.prefix }}-deleteSETax"
												   onclick="javascript:deleteSETax('{{ tax_form.prefix }}')">
													<img src="/site_media/images/deleteButton.png"
													     title="Delete" alt="Delete"
													     align="center"/></a>
											</td>
										</tr>
										{% endfor %}
										<tr hidden="hidden">
											<td><input type=hidden id="id_edit_data"
											           value="{{se_edit_id}}"/></td>
										</tr>
									</tbody>
								</table>
								<table class="table" id="se_tot_table" border="0">
									<tbody>
                                        <tr>
                                            <td class="col-md-12" style="border: none; text-align: right; padding-right: 16px; float: right; margin-top: 6px; font-size: 13px !important;">
                                                <label>Round off</label>
                                            </td>
                                            <td style="border: none; padding-right:0;" align="right">
                                                {{ se.round_off }}
                                            </td>
                                        </tr>
										<tr>
											<td class="col-md-12" style="border: none; text-align: right; padding-right: 16px; float: right; margin-top: 6px; font-size: 13px !important;">
												<label style="font-size: 16px;">Grand Total</label>
											</td>
											<td class="col-md-6" style="border: none; padding-right:0;">
												{{ se.grand_total }}
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div class="clearfix"></div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="form-group col-sm-12 text-right">
					{% if access_level.edit %}
						<input type="button" class="btn btn-save" value="Save Draft" onclick="saveSalesEstimate('draft')" id="cmd_save_se"/>
						<input type="button" class="btn btn-save hide" value="Update" onclick="validateExpiryDate('update')" id="cmd_se_update" disabled="disabled" />
						<input type="button" class="btn btn-save hide" value="Submit for Approval" id="cmd_se_review" onclick="validateExpiryDate('submit_for_approval')"/>
					{% endif %}
					{% if access_level.approve %}
						<input id="se_generate_pdf" class="btn btn-save" type="button" value="Approve / Reject" hidden="hidden" onclick="approveAndRejectButtonClick()" />
						<input id="amend_se" class="btn btn-save hide" type="button" value="Amend" disabled="disabled" onclick="validateExpiryDate('amend')"/>
					{% endif %}
					<input type="hidden" name="cmd_submit_for_approval" id="cmd_submit_for_approval" />
				</div>
				{% if se.status.value == 2 %}
					<div class="form-group col-sm-12 text-right">
						{% if access_level.approve %}
							<input id="client_approve" class="btn btn-save" type="button" value="Client Approve" onclick="approveAndRejectButtonClick()"/>
							<input id="client_reject" class="btn btn-danger" type="button" value="Client Reject" onclick="approveAndRejectButtonClick()"/>
						{% endif %}
					</div>
				{% endif %}
			</div>
		</form>
	</div>
</div>
<div id="inv_model" class="modal fade" role="dialog" data-backdrop="static" data-keyboard="false">
	<div class="modal-dialog modal-lg">
	    <div class="modal-content">
             <div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Invoice Summary</h4>
      		</div>
			<div class="modal-body">
				<table class="table table-bordered custom-table" id="inv_value_model">
					<thead>
						<tr>
							<th>Invoice No</th>
							<th>Approved On</th>
							<th>Issued On</th>
							<th>Value</th>
						</tr>
					</thead>
					<tbody>
						{% for item in inv_details %}
							<tr>
								<td style="text-align: center">
									<form id="invoice_edit_{{ item.invoice_id }}" method="post" action="/erp/sales/invoice/#tab1" target="_blank">
										{%csrf_token%}
										<a role="button" class="edit_link_code" onclick="javascript:clickButton('editInvoice_{{item.invoice_id}}');">{{item.invoice_code}}</a>
										<input type="hidden" value="{{ item.invoice_id }}" id="id_invoice_no_{{ item.invoice_id }}"
										       name="invoice_no"  />
										<input type="hidden" value="{{item.type}}" id="id_edit_dc_type_{{ item.invoice_id }}"
										       name="edit_dc_type" class="invoice_type_in_class" />
										<input type="submit" value="Edit" id="editInvoice_{{ item.invoice_id }}"
										       hidden="hidden"/>
									</form>
								</td>
								<td>{{item.invoice_approved|date:'M d, Y H:i:s'}}</td>
								<td>{{item.invoice_issued|date:'M d, Y H:i:s'}}</td>
								<td style="text-align: right">{{item.invoice_total}}</td>
							</tr>
						{% endfor %}
					</tbody>
				</table>
			</div>
			<div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
		</div>
	</div>
</div>
<div id="oa_model" class="modal fade" role="dialog" data-backdrop="static" data-keyboard="false">
	<div class="modal-dialog modal-lg">
	    <div class="modal-content">
             <div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">OA Summary</h4>
      		</div>
			<div class="modal-body">
				<table class="table table-bordered custom-table" id="oa_value_model">
					<thead>
						<tr>
							<th >OA No</th>
							<th>Date</th>
							<th>Value</th>
						</tr>
					</thead>
					<tbody>
					    {% for item in oa_details %}
							<tr>
								<td style="text-align: center">
									<form id="oa_edit" method="post" action="/erp/sales/oa/editOA/#tab1" target="_blank">
										{%csrf_token%}
										<a role="button" class="edit_link_code" onclick="javascript:clickButton('editOA_{{item.oa_id}}');">
											{{item.oa_code}}
										</a>
										<input type="hidden" value="{{ item.oa_id }}" id="id_oa_no" class="oa_id_in_class" name="oa_no"/>
										<input type="submit" value="Edit" id="editOA_{{ item.oa_id }}" hidden="hidden"/>
									</form>
								</td>
								<td>{{item.oa_date|date:'M d, Y H:i:s'}}</td>
								<td style="text-align: right">{{item.oa_total}}</td>
							</tr>
						{% endfor %}
					</tbody>
				</table>
			</div>
			<div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
		</div>
	</div>
</div>

<div id="se_document_modal" class="modal fade" role="dialog">
  	<div class="modal-dialog modal-lg">
		<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Preview Document</h4>
      		</div>
      		<div class="modal-body text-center">
      			<input id="modal_se_id" name="se_id" value='' hidden="hidden"/>
      			<input id="expiry_date" name="expiry_date" value='' hidden="hidden"/>
				<div class="row" style="text-align: left;">
					<div class="col-lg-12">
						<div class="content_bg">
							<div class="add_table" id="se_doc_btn">
								<form id="se_approval_form" method="POST" action="/erp/sales/se/generateSEDoc/">
									{% csrf_token %}
									<div class="col-sm-6 add_table_content" style="padding-left: 0;">
										<input id="se_id" name="se_no" value='' hidden="hidden"/>
										<input type="submit" value="Edit" id="pdfGeneration_{{ se_id }}" hidden="hidden"/>
										<input id="remarks" class="form-control" enterkey="do_nothing" maxlength="248" placeholder="Approval/Rejection Remarks"/>
										<div class="remarks_count_link remarks_count disabled" onclick="remarksInSwal('se_document_remarks');">
											<span class="remarks_counter">No</span><span> remarks</span>
										</div>
									</div>
									{% if access_level.approve %}
										<a role='button' id='approve_se' class="btn btn-save" onclick="validateExpiryDate('approve')">Approve</a>
										<a role='button' id='client_approve_se' class="btn btn-save" onclick="validateExpiryDate('client_approve')">Client Approve</a>
										<a role='button' id='client_reject_se' class="btn btn-danger for_se_approve" onclick="validateExpiryDate('client_reject')">Client Reject</a>
										<a role='button' id='review_se' class="btn btn-warning" onclick="validateExpiryDate('review')">Review</a>
								    {% endif %}
								    {% if access_level.approve %}
										<a role='button' id='reject_se' class="btn btn-danger for_se_approve" onclick="validateExpiryDate('reject')">Reject</a>
									{% else %}
									{% if access_level.edit %}
										<a role='button' id='reject_se' class="btn btn-danger for_se_edit" onclick="validateExpiryDate('reject')">Discard</a>
									{% endif %}
									{% endif %}
									<div style="display: inline-block; margin-right: 30px; float: right;" id="send_mail_div"  class="hide">
										<a role="button" id='display_popup' onclick="openMailPopup()" class="btn transparent-btn" data-tooltip="tooltip" title="Email SE"><i class="fa fa-envelope" style="font-size: 14px;" aria-hidden="true"></i></a>
									</div>
									<div style="display: inline-block; float: right;">
										<a href="/erp/sales/sales_estimate/view/" class="btn transparent-btn" data-tooltip="tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
									</div>
								</form>
							</div>

						</div>
					</div>
				</div>
				<input type="hidden" id="se_document_remarks" />
				<div id="se_document_container"></div>
      		</div>
			<div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
      	</div>
  	</div>
</div>

<div id="notesEditableContainer" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg" style="width: 80%;">
	    <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Notes</h4>
            </div>
            <div class="modal-body">
                <div id="notes_editor">
                	<b>{{se.notes}}</b>
				</div>
            </div>
            <div class="modal-footer">
            	<button  id="add_se_notes" type="button" class="btn btn-save" onclick="addSENote();">Add</button>
            	<button  id="cancel_se_notes" data-dismiss="modal" type="button" class="btn btn-cancel">Cancel</button>
            </div>
        </div>
    </div>
</div>
{% include "masters/add_project_modal.html" %}
<div id="upload_se_modal" class="modal fade" role="dialog">
  	<div class="modal-dialog">
    	<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Attachments for <span class="attachment-title" style="text-shadow: 0 0 #000;"></span></h4>
      		</div>
      		<div class="modal-body">
		  		<form id="se_attachment_upload" onsubmit="return false;" method="post" enctype="multipart/form-data">{% csrf_token %}
		  			<div class="col-sm-12 form-group">
		  				<label style="width: 160px;">Label/ Description<span class="mandatory_mark"> *</span></label>
                        <input type="text" name="label" id="document_label" class="form-control" maxlength="120" autocomplete="off" placeholder="" />
		  			</div>
	    		</form>
		        <form action='https://storage.googleapis.com/{{ GCS_BUCKET_NAME }}/' id="se_attachment_uploading" name="se_attachment_uploading" method="POST" enctype='multipart/form-data'>
			         <div class="col-md-12 form-group" style="width: 500px;">
		  				<input type="file"  class="filestyle col-sm-12" data-buttonBefore="true" name="file" id="attachmentupload" />
		  			</div>
			        <div class="text-right" style="margin-right: 15px;">
				        <input type='submit' id="sehideUploadButton" class="btn btn-save" value="Add" disabled="true"/>
					</div>
				</form>
	    		<hr />
				<div class="col-sm-12 remove-padding" id="se_attachment_container" style="display: contents;">
				<div id='loadingmessage_se_attachment' style="text-align:center">
						<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' width="100">
						<br /> Please wait...
			    </div>
				</div>
            </div>
      		<div class="modal-footer">
      			<a class="btn btn-cancel" id="sehideUpload" onclick="closeSEUpload();">Close</a>
      		</div>
    	</div>
  	</div>
</div>
<div class="hide">
	<form id="id-promote_oa_form" method="POST" action="/erp/sales/oa/" target="_blank">{% csrf_token %}
		<input type="hidden" name="se_no" id="id-promote_oa_no" value="" />
	</form>
</div>
<div class="hide">
	<form id="id-promote_invoice_form" method="POST" action="/erp/sales/invoice/" target="_blank">{% csrf_token %}
		<input type="hidden" name="se_no" id="id-promote_invoice_no" value="" />
	</form>
</div>
<div id="change_log_modal" class="modal" tabindex="-1" role="dialog" aria-labelledby="" aria-hidden="true">
   <div class="modal-dialog">
	    <div class="modal-content">
		    <div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Activity Log</h4>
	      	</div>
      		<div class="modal-body remove-padding">
		        <div id='loadingmessage_changelog_listing_ie' style="text-align:center">
						<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' width="100">
						<br /> Please wait...
			    </div>
        		<ul class="history-log-container timeline">

        		</ul>
		         <ul class="show-more-log" style="list-style: none; text-align: right; margin-right: 15px; cursor: pointer;">
			        <li onclick="shoMoreLogs(20,20)">Show More</li>
		        </ul>
      		</div>
      		<div class="modal-footer">
      			<a href="#" class="btn btn-cancel" data-dismiss="modal">Close</a>
      		</div>
    	</div>
  	</div>
</div>

{% include "attachment_popup.html" %}
{% include "masters/add_party_modal.html" %}
{% include "masters/add_material_modal.html" %}
<style type="text/css">
	.history-log-part {
		width: 100%;
	    margin: 0;
	    padding: 8px 15px;
	    cursor: pointer;
	    padding-left: 60px;
	}
	.history-log-part:last-child {
		border-bottom: none;
	}
	.history-log-part:hover {
		background: #e8f0fe;
	}
	.history-log-part:hover .history-log-date {
		font-weight: bold;
	}
	.history-log-date {
	   font-style: italic;
	}
	.history-log-username {
		font-size: 15px;
		padding-top: 3px;
		font-weight: bold;
	}

	.history-log-content {
		margin-top: 8px;
	}

	.history-log-content ul {
		padding: 0px 10px 0px 35px;
	}
	.history-log-content li {
		margin-bottom: 10px;
	}

	.history-log-content li:last-child {
		margin-bottom: 0;
	}

	.history-log-part.active .history-log-date,
	.history-log-part.active .history-log-username {
		color: #004195;
	}

	.history-log-part.active .history-log-date {
	    text-align: right;
		display: block;
	}

	.history-log-part .fa-chevron-down:before {
		content: "\f078";
		color: #999;
	}

	.history-log-part.active .fa-chevron-down:before {
		content: "\f077";
		color: #999;
	}

	#change_log_modal .modal-body {
		overflow: auto;
	}


	ul.timeline {
	    list-style-type: none;
	    position: relative;
		padding: 0px;
	}
	ul.timeline:before {
	    content: ' ';
	    background: #d4d9df;
	    display: inline-block;
	    position: absolute;
	    left: 29px;
	    width: 2px;
	    height: 94%;
	    z-index: 400;
        margin-top: 12px;
	}
	ul.timeline > li {

	}
	ul.timeline > li:before {
	    content: ' ';
	    background: white;
	    display: inline-block;
	    position: absolute;
	    border-radius: 50%;
	    border: 3px solid #209be1;
	    left: 23px;
	    width: 14px;
	    height: 14px;
	    z-index: 400;
	    margin-top: 4px;
	}
	ul.timeline > li:last-child:before {
		background: #209be1;
	}
	body {
		overflow-y: scroll;
	}
	.hsn-suggestion-internal {
	    margin-top: 0 !important;
	}
</style>
<script type="text/javascript">
$(document).ready(function(){
	SESuperEditInit();
	displayRemarksHistory('remarks_list', $("#remarks_list_json").val(), "remarks_count");
	sePrevNextPaging();
	$('#sales_estimate_person').multiselect();
    salesPersonInit();
	var editorElement = CKEDITOR.document.getById( 'id_se-notes' );
	editorElement.setAttribute( 'contenteditable', 'false' );
	CKEDITOR.inline( 'id_se-notes' );
	CKEDITOR.replace( 'notes_editor' );
	CKEDITOR.config.height = 300;
	var editorElement1 = CKEDITOR.document.getById( 'notes_editor' );
	var htmlText = $("#id_se-notes").text();
	editorElement1.setHtml(
		htmlText
	);
	$(".new-tour").addClass('hide');

	if($("#se_particulars_table tbody.item-for-goods tr").length >= 1){
        $(".item-for-goods").removeClass('hide');
	}
	if($("#se_particulars_table tbody.item-for-service tr").length >= 1){
		$(".item-for-service").removeClass('hide');
	}

	if($('#id_edit_data').val() != ""){
		changeLogActivityInit();
	}
	if($(".header_current_page").text().trim() == "" || $(".header_current_page").text().indexOf("New") >=0 || $(".header_current_page").text().indexOf("PF") >=0){
		$("super_user_icon, .super_edit_party, .super_edit_type").remove();
	}
	else{
		$(".super_user_icon").removeClass('hide');
	}
	if($("#id_se-project_code").val()==='None') {
        $("#id_se-project_code").val($('#id_se-project_code optgroup:eq(0) option:eq(0)').val()).trigger("chosen:updated");
    }
});


$(window).load(function(){
	actualProjectsBudget($('#id_se-project_code').val(),$('#id_se-project_code').find(':selected').attr('project-type'));
    $('#id_se-project_code').change(function() {
            actualProjectsBudget($(this).val(),$('#id_se-project_code').find(':selected').attr('project-type'));
    });
	$('.component_project label').hide();
	$("#loading").hide();
});

function shoMoreLogs(offset, limit){
		if($("#change_log_modal").hasClass("change_log")) {
			var se_id = $("#id_se-id").val();
			$.ajax({
		        url: '/erp/sales/sales_estimate/getloglist/',
		        type: "POST",
		        dataType: "json",
		        data: {'se_id': se_id, offset: offset, limit: limit},
		        success: function (data) {
		        if (data['response_code'] != 400){
		            x = data['data'];
					var row = "";
					var i2 = offset;
					for(var i = 0; i < x.length; i++) {
					    var obj = x[i].log;
					    var row = `<li class="history-log-part" id="history-part-${i2}" onclick="loadHistoryContent('${data['data'][0].eid}', '${obj.modified_at}', ${i2}, '${obj.preference}')">
				                        <span class="history-log-username">${obj.username[0]+" "+obj.username[1]}</span>
				                        <div class="history-log-content" style="display: none;"></div>
				                        <span class="history-log-date">${moment(obj.modified_at).format("MMM DD, YYYY hh:mm:ss a")}</span>
				                    </li>`;

		                $(".history-log-container").append(row);
		                var displayedCount = $(".history-log-container").find("li").length;
		                i2++;
		                if(x.length < 20){
		                    $(".show-more-log").addClass("hide");
		                }
		                else {
		                    $(".show-more-log").find("li").attr("onclick", "shoMoreLogs("+displayedCount+", 20)");
	                    }
					}
				}
				else {
					$(".show-more-log").addClass("hide");
				}
		        },
		        error: function ($xhr,textStatus,errorThrown) {
			        console.log("ERROR : ", $xhr);
			        if ($xhr.responseText.indexOf("Your session has expired") != -1){
			            location.reload();
			        }
		        }
			});
		}
	}
function changeLogActivityInit(){
		$("#id-change-log").removeClass("hide");
		$("#btn_change_log").click(function(){
			$("#change_log_modal").addClass("change_log").removeClass("change_preference_log");
			$("#change_log_modal").modal("show");
			$("#loadingmessage_changelog_listing_ie").show();
			$(".history-log-container").html("");
			var se_id = $("#id_se-id").val();
			$.ajax({
		        url: '/erp/sales/sales_estimate/getloglist/',
		        type: "POST",
		        dataType: "json",
		        data: {'se_id': se_id, 'offset': 0, 'limit': 20},
		        success: function (data) {
		        if (data['response_code'] != 400){
		            x = data['data'];
					var row = "";
					for(var i = 0; i < x.length; i++) {
					    var obj = x[i].log;
					    var row = `<li class="history-log-part" id="history-part-${i}" onclick="loadHistoryContent('${data['data'][0].se_id}', '${obj.modified_at}', ${i})">
				                        <span class="history-log-username">${obj.username[0]+" "+obj.username[1]}</span>
				                        <div class="history-log-content" style="display: none;"></div>
				                        <span class="history-log-date">${moment(obj.modified_at).format("MMM DD, YYYY hh:mm:ss a")}</span>
				                    </li>`;
				        $("#loadingmessage_changelog_listing_ie").hide();
		                $(".history-log-container").append(row);
		                var displayedCount = $(".history-log-container").find("li").length;
					}
						if(x.length < 20){
		                    $(".show-more-log").addClass("hide");
		                }
		                else {
		                    $(".show-more-log").removeClass("hide");
		                    $(".show-more-log").find("li").attr("onclick", "shoMoreLogs("+displayedCount+", 20)");
	                    }
				}
				else {
				    var row = `<span style="width: 100%; padding: 15px; text-align:center; display: block;">No Activity Log has been captured.</span>`;
					$("#loadingmessage_changelog_listing_ie").hide();
		            $("#change_log_modal .modal-body").html(row);
				}
		        },
		        error: function ($xhr,textStatus,errorThrown) {
			        console.log("ERROR : ", $xhr);
			        if ($xhr.responseText.search("Session Expired!") != -1){
			            location.reload();
			        }
		        }
			});
		});

		$("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
}
function loadHistoryContent(se_id, modified_at, i) {
		var currentLogContent = $("#history-part-"+i).find('.history-log-content');
		if(currentLogContent.text() == "") {
			currentLogContent.html(`<img src="/site_media/images/loading_spinner.gif" style="width: 30px;"> Loading, Please wait...`).show();
			currentLogContent.closest(".history-log-part").addClass("active");
			setTimeout(function(){
				$.ajax({
			        url: '/erp/sales/sales_estimate/getlogdata/',
			        type: "POST",
			        dataType: "json",
			        data: {"se_id":se_id, "modified_at": modified_at},
			        success: function (data) {
			        if (data['response_code'] != 400){
			        var row = '';
				        $.each( data['data'], function( key, value ) {
						  row += `<li>` + value + `</li>`;
						});
			        currentLogContent.html(`<ul>` + row + `</ul>`);
					currentLogContent.hide();
					currentLogContent.slideDown();
					}else{
					currentLogContent.html(`log data not available`);
					currentLogContent.hide();
					currentLogContent.slideDown();

					}
			        },
			        error: function () {
			        console.log('Changelog listing failed to load');

			        }
				});

			},100);
		}
		else {
			if(currentLogContent.find("img").length > 0) {
				return;
			}
			if(currentLogContent.is(":visible")) {
				currentLogContent.slideUp('fast', function() {
				   currentLogContent.closest(".history-log-part").removeClass("active");
				});

			}
			else {
				currentLogContent.slideDown();
				currentLogContent.closest(".history-log-part").addClass("active");
			}
		}
		$("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
}
$(window).load(function() {
 	$('.nav-pills li').removeClass('active');
    $("#li_sales_estimate").addClass('active');
    $(".slide_container_part").removeClass('selected');
    $("#menu_sales").addClass('selected');
    setTimeout(function(){
		enableEditButtonInit();
		$('#cmd_se_update').attr('title', 'No changes detected.');
	},1000);
});

function enableEditButtonInit(){
	$('body').on('change keypress paste', ':input', function(e) {
		if($(this).attr("class") !="table_text_box"  && $(this).attr("id") !="add_se_tax" ) {
			enableEditButton();
	    }
	});

	$('body').on('keyup', ':input', function(e) {
		 if(e.which == 8 || e.which == 46) {
		    enableEditButton();
		 }
	});

	$("#add_se_notes").click(function(e){
		enableEditButton();
	})

	$("#ul-tagit-display").click(function(e){
		enableEditButton();
	})
	$("#se_tax_table").click(function(e){
	   enableEditButton();
	})
}

function enableEditButton() {
	$('#cmd_se_update').prop('disabled', false);
	$('#cmd_se_update').attr('title', '');
	$('#amend_se').prop('disabled', false);
	$('#amend_se').attr('title', '');
 }


$(".invoice_value").click(function(){
	$("#inv_model").modal('show');
	noInvoiceCreated();

});

function noInvoiceCreated(){
	if($("#inv_value_model tbody").find("tr").length <= 0) {
        var row = "<tr><td colspan='8' class='text-center no-value'>No Invoice Created!</td></tr>";
        $('#inv_value_model').append(row);
	}
}

$(".oa_value").click(function(){
	$("#oa_model").modal('show');
	noOaCreated();
});

function noOaCreated(){
	if($("#oa_value_model tbody").find("tr").length <= 0) {
        var row = "<tr><td colspan='8' class='text-center no-value'>No OA Created!</td></tr>";
        $('#oa_value_model').append(row);
	}
}
</script>
{% endblock %}
