{% extends "purchase/sidebar.html" %}
{% block material_wise_reports %}
<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/purchase_report.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-multiselect.js?v={{ current_version }}"></script>
<style type="text/css">
    .dataTables_scrollBody {
        overflow: auto hidden !important;
    }

    .dataTables_scrollBody table{
        margin-bottom: 20px;
    }
</style>
<div class="right-content-container download-icon-onTop">
	<div class="page-title-container">
		<span class="page-title">Material Wise Report</span>
	</div>
	<div class="col-lg-12">
		<div class="container-fluid">
			<div class="row">
				<div class="filter-components">
					<div class="filter-components-container">
						<div class="dropdown">
							<button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
								<i class="fa fa-filter"></i>
							</button>
							<span class="dropdown-menu arrow_box arrow_box_filter">
								<form action="/erp/purchase/po/materialwisereports/" method="post">{% csrf_token %}
									<div class="col-sm-12 form-group" >
										<label>Date Range</label>
										<div id="reportrange" class="report-range form-control">
											<i class="glyphicon glyphicon-calendar"></i>&nbsp;
											<span></span> <b class="caret"></b>
											<input type="hidden" class="fromdate" id="fromdate" name="from_date" value="{{from_date}}"/>
											<input type="hidden" class="todate" id="todate" name="to_date" value="{{to_date}}"/>
										</div>
									</div>
									<div class="filter-footer">
										<input type="submit" class="btn btn-save" value="Apply" id="refresh"/>
			      					</div>
			      				</form>
							</span>
						</div>
						<span class='filtered-condition filtered-date'>Date: <b></b></span>
					</div>
				</div>
			</div>
			<div class="col-sm-12 remove-padding search_result_table">
				<div class="csv_export_button" style="right: 0;">
	                <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#report-table'), 'Material_Wise_Report.csv']);" data-tooltip="tooltip" data-placement="bottom" title="Download&nbsp;Material&nbsp;Wise&nbsp;Report as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
					<input type="hidden"  id="id_module_access_indent" value="{{module_access.indent}}"/>
	            </div>
				<table border="1" class="table table-bordered custom-table table-striped vertical-center" id="report-table" style="width:100%">
					<thead>
						<tr align="center" valign="middle">
							<th style="min-width: 40px;">S.No.</th>
							<th style="min-width: 120px;">Material Name.</th>
							{% if module_access.indent %}
								<th style="min-width: 80px;">Indent No.</th>
								<th style="min-width: 80px;">Indent Date</th>
								<th style="min-width: 60px;">Indent Quantity</th>
							{% endif %}
							<th style="min-width: 60px;">PO Status</th>
							<th style="min-width: 60px;">PO Draft No</th>
							<th style="min-width: 60px;">PO Drafted on</th>
							<th style="min-width: 60px;">PO Due date</th>
							<th style="min-width: 80px;">PO No</th>
							<th style="min-width: 60px;">PO Date</th>
							<th style="min-width: 60px;">PO Qty</th>
							<th style="min-width: 160px;">Project/Tag</th>
							<th style="min-width: 160px;">Supplier</th>
							<th style="min-width: 60px;">PO Value (With Disc & Without Tax)</th>
							<th style="min-width: 60px;">Material Status</th>
							<th style="min-width: 60px;">Delivery Status</th>
							<th style="min-width: 60px;">Delivery Overdue (in Days)</th>
						</tr>
					</thead>
					{% for report_item in report_rows %}
					<tr align="center" valign="middle">
						<td>{{ forloop.counter }}.</td>
						<td class="text-left">{{ report_item.material_name }}</td>
						{% if module_access.indent %}
							<td>{{ report_item.indent_no }}</td>
							<td>{{ report_item.indent_date|date:"M d, Y" }}</td>
							<td>{{ report_item.indent_qty }}</td>
						{% endif %}
						<td>{{ report_item.po_status }}</td>
						<td>{{ report_item.draft_po_no }}</td>
						<td>{{ report_item.draft_date|date:"M d, Y H:i:s" }}</td>
						<td>{{ report_item.po_due_on|date:"M d, Y " }}</td>
						<td>{{ report_item.po_no }}</td>
						<td>{{ report_item.po_date|date:"M d, Y H:i:s" }}</td>
						<td>{{ report_item.po_qty }}</td>
						<td class="text-left">{{ report_item.project }}</td>
						<td class="text-left">{{ report_item.supplier }}</td>
						<td class="text-right">{{ report_item.po_value }}</td>
						<td>{{ report_item.material_status }}</td>
						<td>{{ report_item.delivery_status }}</td>
						<td>{{ report_item.delivery_overdue }}</td>
					</tr>
					{% endfor %}
				</table>
			</div>
			<div class="clearfix"></div>
		</div>
	</div>	
</div>
<script>
$(document).ready(function(){
	$('.nav-pills li').removeClass('active');
	$("#li_material_report").addClass('active');
	TableHeaderFixed();
	$("title").text("Material Wise Report")
});

$(window).load(function(){
	updateFilterText();
});

function updateFilterText() {
	$(".filtered-date b").text($("#reportrange").find("span").text());
}

var oTable;
var oSettings;
function TableHeaderFixed(){
if ($('#id_module_access_indent').val()=='True'){
	oTable = $('#report-table').DataTable({
		fixedHeader: false,
        "pageLength": 50,
        "scrollY": Number($(document).height() - 230),
        "scrollX": true,
		"pageLength": 50,
		"search": {
			"smart": false
		},
		"columns": [
			null,null,null,					
			{ "type": "date" },
			null,null,null,
			{ "type": "date" },
			{ "type": "date" },
			null,
			{ "type": "date" },
			null,null,null,null,null,null,null
			]
	});
}else {
	oTable = $('#report-table').DataTable({
		fixedHeader: false,
        "pageLength": 50,
        "scrollY": Number($(document).height() - 230),
        "scrollX": true,
		"search": {
			"smart": false
		},
		"columns": [
			null,null,
			null,null,
			{ "type": "date" },
			{ "type": "date" },
			null,
			{ "type": "date" },
			null,null,null,null,null,null,null
			]
	});
}
	oTable.on("draw",function() {
		var keyword = $('#report-table_filter > label:eq(0) > input').val();
		$('#report-table').unmark();
		$('#report-table').mark(keyword,{});
	});
	oTable.on('page.dt', function() {
	  $('html, body').animate({
		scrollTop: $(".dataTables_wrapper").offset().top - 15
	   }, 'slow');
	});
	oSettings = oTable.settings();
	$( window ).resize();
}
</script>

{% endblock %}