from django.test import TestCase

from erp.production.backend import ProductionService, ProductionDAO


class ProductionTests(TestCase):
	def setUp(self):
		pass

	def tearDown(self):
		pass

	def test_something(self):
		self.assertEqual(True, False)

	def test_load_oa(self):
		from_date = "2020-11-01 00:00:00"
		to_date = "2021-12-01 00:00:00"
		service = ProductionService()
		oa_details = service.getOADetails(enterprise_id=102, from_date=from_date, to_date=to_date)
		print("Loaded materials %s " % len(oa_details))
		self.assertNotEqual(len(oa_details), 0)

	def test_load_mi(self):
		from_date = "2020-11-01 00:00:00"
		to_date = "2021-12-01 00:00:00"
		service = ProductionService()
		oa_details = service.getManufactureIndentList(enterprise_id=102, from_date=from_date, to_date=to_date)
		print("Loaded mi %s " % len(oa_details))
		self.assertNotEqual(len(oa_details), 0)

	def test_load_pp(self):
		from_date = "2020-11-01 00:00:00"
		to_date = "2021-12-01 00:00:00"
		service = ProductionService()
		oa_details = service.getProductionPlanList(enterprise_id=102, from_date=from_date, to_date=to_date)
		print("Loaded mi %s " % len(oa_details))
		self.assertNotEqual(len(oa_details), 0)

