import collections

from pymongo.message import query

from erp.accounts import logger


class ClauseBuilder(object):
	where = '{where}'
	order = '{order}'
	limit = '{limit}'

	try:
		__str = basestring  # @UndefinedVariable
	except NameError:
		__str = bytes, str

	def _isScalar(self, v):
		return not isinstance(v, collections.Iterable) or isinstance(v, self.__str)

	@staticmethod
	def getReference(name, alias=None):
		if name.count('.') <= 1 and "AS" not in name:
			result = name.split('.')
			result_alias = name.split('.')
			result[-1] = '`{0}`'.format(result[-1])
			result_alias[-1] = '{0}'.format(result_alias[-1])
			alias_title = '_'.join(result_alias)
			result = '.'.join(result)
			if alias:
				result += ' AS {0}'.format(alias_title)
		else:
			result = name

		return result

	def getPlaceholder(self, name, nameOnly=False, postfix=''):

		result = name + postfix
		if not nameOnly:
			result = '%({0})s'.format(result)

		return result

	def getExpressions(self, fields, postfix='', op='='):
		expression = lambda f: ' '.join((self.getReference(f), op, self.getPlaceholder(f, postfix=postfix)))
		return tuple(map(expression, fields))

	def getDateExpressions(self, fields, postfix='', op='BETWEEN'):
		expression = lambda f: ' '.join((
			self.getReference(f), op, self.getPlaceholder("from", postfix=postfix), 'AND',
			self.getPlaceholder("to", postfix=postfix)))
		return tuple(map(expression, fields))

	def getSelectClause(self, fields):
		return 'SELECT ' + ', '.join(self.getReference(f, f) for f in fields)

	def getWithoutSelectClause(self, fields):
		return ', '.join(f for f in fields)

	def getFromClause(self, table):
		return 'FROM {0}'.format(self.getReference(table))

	def getWithoutFromClause(self, table):
		return '{0}'.format(self.getReference(table))

	def getJoinClause(self, join, condition=''):
		if not join:
			return ''

		join = join.copy()

		for k, val in join.items():
			condition = condition + ' {1} JOIN ({0}) ON {2} = {3}'.format(k, val[0], val[1], val[2])

		return condition

	def getWhereClause(self, where):

		if not where:
			return ''

		where = where.copy()
		special_case = ()
		special_case_2 = ()

		if 'grn.status' in where:
			special_case = ('IF(grn.`status` IS NOT NULL, grn.`status` IN (0 , 1, 2, 3, 4, 5),TRUE)',)
			del where['grn.status']
		if 'purchase_order.status' in where:
			special_case_2 = ('purchase_order.status != 3',)
			del where['purchase_order.status']

		scalar = set(k for k, v in where.items() if self._isScalar(v))
		none = set(k for k in scalar if where[k] is None)
		one = set(k for k in set(where.keys()) - scalar if len(where[k]) == 1)
		vector = set(
			k for k in set(where.keys()) - scalar - one if len(where[k]) > 1 and not isinstance(where[k], dict))
		tricular = set(k for k, v in where.items() if isinstance(v, dict))

		# converting one-item iterables into scalar value is important because connection.literal
		# converts them into tuple which results in "WHERE id IN('1',)" with erroneous trailing comma
		for k in one:
			where[k] = where[k][0]

		condition = self.getExpressions(scalar - none | one)
		condition += self.getExpressions(none, op='IS')
		condition += self.getExpressions(vector, op='IN')
		condition += self.getDateExpressions(tricular, op='BETWEEN')
		if len(special_case) != 0:
			condition += special_case if special_case else ''
		if len(special_case_2) != 0:
			condition += special_case_2 if special_case_2 else ''

		return 'WHERE {0}'.format(' AND '.join(condition)) if condition else ''

	def getWhereClauseExt(self, where_ext):

		return '{0}'.format(where_ext)

	def getHavingClause(self, having):
		return 'HAVING ({0})'.format(' AND '.join(having)) if having and len(having) > 0 else ''

	def getGroupByClause(self, groupby):
		if not groupby:
			return ''
		return 'GROUP BY ' + format(', ').join(groupby)

	def getOrderClause(self, order):

		if not order:
			return ''

		return 'ORDER BY ' + ', '.join('{0} {1}'.format(self.getReference(by[0]), by[1]) for by in order)

	def getLimitClause(self, limit):

		if limit is None:
			return ''

		if isinstance(limit, int):
			limit = 0, limit
		else:
			limit = tuple(map(int, limit))

		return 'LIMIT {0}, {1} '.format(*limit)

	def replaceClause(self, sql, name, values):

		placeholder = getattr(self, name)
		isPresent = sql.find(placeholder) != -1
		if values:
			clause = getattr(self, 'get{0}Clause'.format(name.capitalize()))
			part = clause(values)
			if isPresent:
				sql = sql.replace(placeholder, part)
			else:
				sql = sql + '\n' + part
		elif isPresent:
			sql = sql.replace(placeholder, '')

		return sql


class ReportsQueryBuilder(object):
	_clauseBuilder = None

	def __init__(self):
		self._clauseBuilder = ClauseBuilder()

	def buildQuery(self, fields, table, where=None, where_ext='', join=None, groupby=None, having=None, order=None, limit=None, alias_name=None):
		"""
		This method is helps to build query which is need to be executed.
		:param fields:
		:param table:
		:param where:
		:param where_ext:
		:param join:
		:param groupby:
		:param having:
		:param order:
		:param limit:
		:param field_post:
		:param alias_name:
		:return:
		"""
		query_response = ''
		# ================== FOR REFERENCE ========================
		# fields = 'film_id', 'title'
		# table = 'film'
		# where = {'rating': ('R', 'NC-17'), 'release_year': 2006}
		# order = [('release_year', 'asc'), ('length', 'desc')]
		# limit = 2
		# =========================================================
		try:
			query_response = """SELECT {SELECT} \n FROM {QTABLE} \n {JOIN} \n {WHERE} {WHEREEXT}\n {GROUPBY} \
								{HAVING}\n {ORDER} \n {LIMIT}""".format(
				SELECT=self._clauseBuilder.getWithoutSelectClause(fields),
				QTABLE=self._clauseBuilder.getWithoutFromClause(table),
				JOIN=self._clauseBuilder.getJoinClause(join),
				WHERE=self._clauseBuilder.getWhereClause(where),
				WHEREEXT=self._clauseBuilder.getWhereClauseExt(where_ext),
				HAVING=self._clauseBuilder.getHavingClause(having),
				GROUPBY=self._clauseBuilder.getGroupByClause(groupby),
				ORDER=self._clauseBuilder.getOrderClause(order),
				LIMIT=self._clauseBuilder.getLimitClause(limit)
			)
			if alias_name:
				query_response = "(%s) AS %s" % (query_response, alias_name)
		except Exception as e:
			logger.exception('%s' % e)
		return query_response


class GSTR1Report(object):
	"""
	This class is made to build queries for GSTR1 report module
	"""
	def __init__(self):
		logger.info('GSTR1 report class initiated.')

	def b2b(self, from_date=None, to_date=None, enterprise_id=None):
		"""

		:return:
		"""
		where = """IFNULL(i.issued_on, NOW()) BETWEEN '%s' AND '%s' AND i.enterprise_id = '%s' AND i.status > 0 
						AND i.type IN ('GST' , 'Trading', 'Service', 'BoS', 'Excise') AND gc.id in (1, 2, 6, 7, 8, 9)""" % (
					from_date, to_date, enterprise_id)
		group_by = """i.id , im.item_id, im.make_id, im.oa_no"""
		query = self.build_report_query(where=where, group_by=group_by)
		return query

	def b2b_invoice_total(self, from_date=None, to_date=None, enterprise_id=None):
		"""

		:return:
		"""
		where = """IFNULL(i.issued_on, NOW()) BETWEEN '%s' AND '%s' AND i.enterprise_id = '%s' AND i.status > 0 
						AND i.type IN ('GST' , 'Trading', 'Service', 'BoS', 'Excise') AND gc.id in (1, 2, 6, 7, 8, 9)""" % (
					from_date, to_date, enterprise_id)
		query = """
					SELECT i.id, i.grand_total as invoice_total
					FROM
						invoice i
					JOIN invoice_materials im ON i.id = im.invoice_id
						AND i.enterprise_id = im.enterprise_id					
					LEFT JOIN party_master p ON p.party_id = i.party_id
						AND p.enterprise_id = i.enterprise_id					
					LEFT JOIN gst_category gc ON gc.id = p.category_id					
					WHERE %s GROUP BY i.id""" % where
		return query

	def b2cl(self, from_date=None, to_date=None, enterprise_id=None):
		"""

		:return:
		"""
		where = """IFNULL(i.issued_on, NOW()) BETWEEN '%s' AND '%s' AND i.enterprise_id = '%s' AND i.status > 0 
					AND i.type IN ('GST' , 'Trading', 'Service', 'BoS', 'Excise') AND gc.id in (3, 4) 
					AND i.grand_total > 250000""" % (from_date, to_date, enterprise_id)
		group_by = """i.id , im.item_id, im.make_id"""
		query = self.build_report_query(where=where, group_by=group_by)
		return query

	def b2cs(self, from_date=None, to_date=None, enterprise_id=None):
		"""

		:return:
		"""
		where = """IFNULL(i.issued_on, NOW()) BETWEEN '%s' AND '%s' AND i.enterprise_id = '%s' AND i.status > 0 
					AND i.type IN ('GST' , 'Trading', 'Service', 'BoS', 'Excise') AND gc.id in (3, 4) AND i.grand_total < 250000""" % (
			from_date, to_date, enterprise_id)
		group_by = """i.id , im.item_id, im.make_id"""
		query = self.build_report_query(where=where, group_by=group_by)
		return query

	def hsn(self, from_date=None, to_date=None, enterprise_id=None):
		"""

		:return:
		"""
		where = """IFNULL(i.issued_on, NOW()) BETWEEN '%s' AND '%s' AND i.enterprise_id = '%s' AND i.status > 0 
					AND i.type IN ('GST' , 'Trading', 'Service', 'BoS', 'Excise') 
					AND im.hsn_code NOT IN ('NIL-RATED', 'EXEMPT', 'NON-GST')""" % (from_date, to_date, enterprise_id)
		query = self.hsn_inner_query(where=where)
		return query

	def cdnr(self, from_date=None, to_date=None, enterprise_id=None):
		"""

		:return:
		"""
		where = """IFNULL(g.inv_date, NOW()) BETWEEN '%s' AND '%s' AND g.rec_against = 'Sales Return' AND g.status > 0 
					AND g.inv_type != 1 AND g.enterprise_id = '%s' AND gc.id IN (1 , 2, 6, 7, 8, 9)""" % (from_date, to_date, enterprise_id)
		query = self.cdnr_inner_query(where=where)
		return query

	def cdnur(self, from_date=None, to_date=None, enterprise_id=None):
		"""

		:return:
		"""
		where = """IFNULL(g.inv_date, NOW()) BETWEEN '%s' AND '%s' AND g.rec_against = 'Sales Return'
					AND g.status > 0 AND g.inv_type != 1 AND g.enterprise_id = '%s' AND gc.id IN (3, 4, 5)""" % (from_date, to_date, enterprise_id)
		query = self.cdnr_inner_query(where=where)
		return query

	def exp(self, from_date=None, to_date=None, enterprise_id=None):
		"""

		:return:
		"""
		where = """IFNULL(i.issued_on, NOW()) BETWEEN '%s' AND '%s' AND i.enterprise_id = '%s' AND i.status > 0 
					AND i.type IN ('GST' , 'Trading', 'Service', 'BoS', 'Excise') AND gc.id in (5, 6, 7, 9)""" % (
			from_date, to_date, enterprise_id)
		group_by = """i.id, im.item_id, im.make_id"""
		query = self.build_report_query(where=where, group_by=group_by)
		return query

	def exemp(self, from_date=None, to_date=None, enterprise_id=None, conditions=None):
		"""

		:return:
		"""
		where = """IFNULL(i.issued_on, NOW()) BETWEEN '%s' AND '%s' AND i.enterprise_id = '%s' AND i.status > 0 
					AND i.type IN ('GST' , 'Trading', 'Service', 'BoS', 'Excise') %s""" % (
					from_date, to_date, enterprise_id, conditions)
		query = self.exemp_inner_query(where=where)
		return query

	def exemp_inner_query(self, where=None):
		"""

		:param where:
		:return:
		"""
		query = """SELECT 
						hsn_code,
						SUM(inv_total), 
						party_state,
						party_gstin,
						enterprise_state,
						enterprise_gstin
					FROM 
						((SELECT 
							im.hsn_code AS hsn_code,						
							SUM(ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100), 2)) * IF(cur_conversion_rate = 0, 1.00000, cur_conversion_rate) AS inv_total,
							p.state AS party_state,
							prd.details AS party_gstin,
							e.state AS enterprise_state,
							erd.details AS enterprise_gstin
					FROM
						invoice AS i
							LEFT JOIN
						invoice_materials AS im ON im.invoice_id = i.id
							AND i.enterprise_id = im.enterprise_id	
							LEFT JOIN
						party_master AS p ON p.party_id = i.party_id
							AND p.enterprise_id = i.enterprise_id
							LEFT JOIN
						party_registration_detail AS prd ON prd.party_id = p.party_id
							AND prd.enterprise_id = p.enterprise_id
							AND TRIM(prd.label) = 'GSTIN'    
							LEFT JOIN
						gst_category gc ON gc.id = p.category_id
							LEFT JOIN
						enterprise e ON e.id = i.enterprise_id
							LEFT JOIN
						enterprise_registration_detail AS erd ON erd.enterprise_id = e.id        
							AND TRIM(erd.label)  = 'GSTIN' WHERE %s 						
						GROUP BY hsn_code) UNION (SELECT
							ic.hsn_code AS hsn_code,                                                
							SUM(ROUND((ic.rate * (100 - ic.discount) / 100), 2)) * IF(cur_conversion_rate = 0, 1.00000, cur_conversion_rate) AS inv_total,
							p.state AS party_state,
							prd.details AS party_gstin,
							e.state AS enterprise_state,
							erd.details AS enterprise_gstin                    
						FROM
								invoice i
						LEFT JOIN invoice_charges ic ON i.id = ic.invoice_id
								AND i.enterprise_id = ic.enterprise_id
						LEFT JOIN invoice_charge_tax ict ON ic.item_name = ict.item_name			
								AND i.id = ict.invoice_id
								AND i.enterprise_id = ict.enterprise_id
						LEFT JOIN tax t ON ict.tax_code = t.code
								AND i.enterprise_id = t.enterprise_id
						LEFT JOIN party_master p ON p.party_id = i.party_id
								AND p.enterprise_id = i.enterprise_id
						LEFT JOIN party_registration_detail AS prd ON prd.party_id = p.party_id
							AND prd.enterprise_id = p.enterprise_id
							AND TRIM(prd.label) = 'GSTIN'
						LEFT JOIN gst_category gc ON gc.id = p.category_id
						LEFT JOIN enterprise e ON e.id = i.enterprise_id
						LEFT JOIN enterprise_registration_detail erd ON erd.enterprise_id = i.enterprise_id
							AND TRIM(erd.label) = 'GSTIN' WHERE %s 
						GROUP BY hsn_code)) AS exempt_tbl GROUP BY hsn_code""" % (where, where)
		return query

	def cdnr_inner_query(self, where=None):
		"""

		:param where:
		:return:
		"""
		query = """SELECT 
					grn_no,
					gstno,
					party_name,
					invno,
					inv_date,
					sr_code,
					grn_date,
					state,
					net_inv_value,
					ROUND(rate, 2) AS rate,
					SUM(taxable_value),
					cess_value AS cess_value,
					status,
					is_compound,
					SUM(taxable_value) + (sum_rate),
					(@cnt:=@cnt + 1) AS rowNumber,
					gst_category_id    
				FROM
				    ((SELECT 
							g.grn_no,
				            prd.details as gstno,
				            p.party_name,
				            g.invno,
				            g.inv_date,
				            CONCAT(g.financial_year, '/SR/', LPAD(g.receipt_no, 6, 0), IF(g.sub_number is not NULL, g.sub_number, '')) AS sr_code,
				            g.grn_date,
				            e.state,
				            g.net_inv_value * g.cur_conversion_rate AS net_inv_value,
				            ROUND(SUM(gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100), 2) * g.cur_conversion_rate AS taxable_value,
				            IF(t.type = 'CESS', SUM(t.net_rate) / COUNT(t.type), NULL) * g.cur_conversion_rate AS cess_value,
				            IF(t.type = 'CGST' OR t.type = 'SGST'
				                OR t.type = 'IGST', t.net_rate, 0) AS rate,				            
				            g.status,
				            g.enterprise_id,
				            t.is_compound,
				            NULL AS sum_rate,
				            p.category_id as gst_category_id
				    FROM
				        grn g
				    LEFT JOIN grn_material gm ON gm.grnNumber = g.grn_no
				        AND gm.enterprise_id = g.enterprise_id
				    LEFT JOIN grn_tax gt ON g.grn_no = gt.grn_no
				        AND g.enterprise_id = gt.enterprise_id
				    LEFT JOIN tax t ON gt.tax_code = t.code
				        AND gt.enterprise_id = t.enterprise_id
				    LEFT JOIN party_master p ON p.party_id = g.party_id
				        AND p.enterprise_id = g.enterprise_id
				    LEFT JOIN party_registration_detail AS prd ON prd.party_id = p.party_id
						AND prd.enterprise_id = p.enterprise_id
						AND TRIM(prd.label) = 'GSTIN'
				    LEFT JOIN enterprise e ON g.enterprise_id = e.id
				    LEFT JOIN gst_category gc ON gc.id = p.category_id
				    WHERE
				      %s
				    GROUP BY g.grn_no , t.net_rate
				    HAVING rate > 0 OR cess_value > 0) UNION (SELECT 
				        g.grn_no,
				            prd.details as gstno,
				            p.party_name,
				            g.invno,
				            g.inv_date,
				            CONCAT(g.financial_year, '/SR/', LPAD(g.receipt_no, 6, 0), IF(g.sub_number is not NULL, g.sub_number, '')) AS sr_code,
				            g.grn_date,
				            e.state,
				            g.net_inv_value * g.cur_conversion_rate AS net_inv_value,
				            ROUND((gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100), 2) * g.cur_conversion_rate AS taxable_value,
				            NULL AS cess_value,
				            SUM(t.net_rate) AS rate,
				            g.status,
				            g.enterprise_id,
				            NULL AS is_compound,
				            ROUND(((gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100) * SUM(t.net_rate) / 100), 2) * g.cur_conversion_rate AS sum_rate,
				            p.category_id as gst_category_id
				    FROM
				        grn g
				    LEFT JOIN grn_material gm ON gm.grnNumber = g.grn_no
				        AND gm.enterprise_id = g.enterprise_id
				    JOIN grn_material_tax gmt ON gmt.grn_no = g.grn_no 
				        AND gm.item_id = gmt.item_id
				        AND gm.make_id = gmt.make_id
				        AND gm.enterprise_id = gmt.enterprise_id
				    JOIN tax t ON gmt.tax_code = t.code
				        AND g.enterprise_id = t.enterprise_id
				    LEFT JOIN party_master p ON p.party_id = g.party_id
				        AND p.enterprise_id = g.enterprise_id
				    LEFT JOIN party_registration_detail AS prd ON prd.party_id = p.party_id
						AND prd.enterprise_id = p.enterprise_id
						AND TRIM(prd.label) = 'GSTIN'
				    LEFT JOIN enterprise e ON g.enterprise_id = e.id
				    LEFT JOIN gst_category gc ON gc.id = p.category_id
				    WHERE
				        %s
				    GROUP BY g.grn_no , gm.item_id , gm.make_id)) AS GSTR1
				     CROSS JOIN
					(SELECT @cnt:=0) AS dummy
				GROUP BY rate , grn_no
				ORDER BY invno, inv_date ASC
				""" % (where, where)

		return query

	def hsn_inner_query(self, where=None):
		"""

		:param where:
		:return:
		"""
		query = """SELECT 
					id,					
					item_id,
					hsn_code,
					name,
					description,
					unit_name,
					SUM(qty) AS qty,
					SUM(total_value) AS total_value,
					SUM(taxable_value) AS taxable_value,
					SUM(cgst_value) AS cgst_value,
					SUM(sgst_value) AS sgst_value,
					SUM(igst_value) AS igst_value,
					SUM(cess_value) AS cess_value,
					enterprise_id,
					issued_on,
					status,
					type, 
					(@cnt := @cnt + 1) AS rowNumber,
					SUM(is_cess_compound) AS  is_cess_compound,
					unit_id 
				FROM ((SELECT
					i.id,
					i.invoice_no,
					im.item_id,
					im.hsn_code,
					m.name,
					m.description,
					um.unit_description AS unit_name,
					ROUND(SUM(im.qty)/IF(count(t.type) != 0,count(t.type), 1), 2) AS qty,        
					ROUND(SUM(i.grand_total)/IF(count(t.type) != 0,count(t.type), 1), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate)  AS total_value,
					ROUND(SUM(im.qty * im.unit_rate * (100 - im.discount) / 100)/IF(count(t.type) != 0,count(t.type), 1), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS taxable_value,
					ROUND(SUM(IF((t.type = 'CGST'),
						((`t`.`net_rate` * (im.qty * im.unit_rate * (100 - im.discount) / 100)) / 100), 0)),
							2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS `cgst_value`,
					ROUND(SUM(IF((t.type = 'SGST'), (t.net_rate * (im.qty * im.unit_rate * (100 - im.discount) / 100)) / 100,
						0)), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS `sgst_value`,
					ROUND(SUM(IF((t.type = 'IGST'), (t.net_rate * (im.qty * im.unit_rate * (100 - im.discount) / 100)) / 100,
						0)), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS `igst_value`,
					0 AS cess_value,
					i.enterprise_id,
					i.issued_on,
					i.status,
					i.type,
					0 AS is_cess_compound,
					um.unit_id    
				FROM
					invoice i
						LEFT JOIN
					invoice_materials im ON i.id = im.invoice_id
						AND i.enterprise_id = im.enterprise_id
						LEFT JOIN
					materials m ON m.id = im.item_id
						AND m.enterprise_id = im.enterprise_id
						LEFT JOIN
					unit_master um ON  IF(im.alternate_unit_id is NULL,  um.unit_id = m.unit, um.unit_id = im.alternate_unit_id)
						AND um.enterprise_id = m.enterprise_id
						LEFT JOIN
					invoice_item_tax iit ON im.item_id = iit.item_id
						AND im.make_id = iit.make_id
						AND i.id = iit.invoice_id
						AND i.enterprise_id = iit.enterprise_id
						LEFT JOIN
					tax t ON iit.tax_code = t.code
						AND i.enterprise_id = t.enterprise_id
						LEFT JOIN
					party_master p ON p.party_id = i.party_id
						AND p.enterprise_id = i.enterprise_id
						LEFT JOIN
					gst_category gc ON gc.id = p.category_id
				WHERE
					%s
				group by hsn_code, i.id, IF(im.alternate_unit_id is NULL, m.unit, im.alternate_unit_id)) UNION (SELECT 
					i.id,
					i.invoice_no,
					im.item_id,
					im.hsn_code,
					m.name,
					m.description,
					um.unit_description AS unit_name,
					0 AS qty,
					0 AS total_value,
					0 AS taxable_value,
					ROUND(SUM(IF((t.type = 'CGST'), ((`t`.`net_rate` * (im.qty * im.unit_rate * (100 - im.discount) / 100)) / 100),
						0)), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS `cgst_value`,
					ROUND(SUM(IF((t.type = 'SGST'), (t.net_rate * (im.qty * im.unit_rate * (100 - im.discount) / 100)) / 100,
						0)), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS `sgst_value`,
					ROUND(SUM(IF((t.type = 'IGST'), (t.net_rate * (im.qty * im.unit_rate * (100 - im.discount) / 100)) / 100,
						0)), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS `igst_value`,
					SUM(IF(t.type = 'CESS', t.net_rate, 0)) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS cess_value,
					i.enterprise_id,
					i.issued_on,
					i.status,
					i.type,
					SUM(IF(t.type = 'CESS' AND t.is_compound, 1, 0)) AS is_cess_compound,
					um.unit_id    
				FROM
					invoice i
						LEFT JOIN
					invoice_materials im ON i.id = im.invoice_id
						AND i.enterprise_id = im.enterprise_id
						LEFT JOIN
					materials m ON m.id = im.item_id
						AND m.enterprise_id = im.enterprise_id
						LEFT JOIN
					unit_master um ON  IF(im.alternate_unit_id is NULL,  um.unit_id = m.unit, um.unit_id = im.alternate_unit_id)
						AND um.enterprise_id = m.enterprise_id
						LEFT JOIN 
					invoice_tax it ON i.id = it.invoice_id		
						AND i.enterprise_id = it.enterprise_id
						LEFT JOIN 
					tax t ON it.tax_code = t.code
						AND i.enterprise_id = t.enterprise_id
						LEFT JOIN
					party_master p ON p.party_id = i.party_id
						AND p.enterprise_id = i.enterprise_id
						LEFT JOIN
					gst_category gc ON gc.id = p.category_id
				WHERE
					%s
				group by hsn_code, IF(im.alternate_unit_id is NULL, m.unit, im.alternate_unit_id))) AS GSTR1
				CROSS JOIN (SELECT @cnt := 0) AS dummy
				group by hsn_code, unit_id
				order by rowNumber, id  ASC
				""" % (where, where)

		return query

	def build_inner_query(self, where=None, group_by=None):
		"""
		build inner query set
		:param where:
		:param group_by:
		:return:
		"""
		query = """(SELECT 
					i.id,
					im.item_id,
					prd.details AS gstno,
					p.party_name,
					IFNULL(i.invoice_code, CONCAT(i.financial_year, '/', SUBSTR(i.type, 1, 1), LPAD(i.invoice_no, 6, 0), IFNULL(i.sub_number, ''))) AS invoice_code,
					i.issued_on AS invoice_date,
					i.issued_on AS issued_on,
					i.grand_total * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS invoice_value,
					IF(p.state != '', p.state, p.gstno) AS place_of_supply,
					IF(i.tax_payable_on_reverse_charge = 1, 'Y', 'N') AS reverse_charge,
					gc.name AS invoice_type,
					i.ecommerce_gstin,
					ROUND(SUM(im.qty * im.unit_rate * (100 - im.discount) / 100)/count(t.type), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS taxable_value,
					ROUND(SUM(IF((t.type = 'CGST'), ((`t`.`net_rate` * (im.qty * im.unit_rate * (100 - im.discount) / 100)) / 100),
                        0)), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS `cgst_value`,
                    ROUND(SUM(IF((t.type = 'SGST'), (t.net_rate * (im.qty * im.unit_rate * (100 - im.discount) / 100)) / 100,
                        0)), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS `sgst_value`,
                    ROUND(SUM(IF((t.type = 'IGST'), (t.net_rate * (im.qty * im.unit_rate * (100 - im.discount) / 100)) / 100,
                        0)), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS `igst_value`,
					IF(t.type = 'CESS', SUM(t.net_rate)/count(t.type) , NULL) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS cess_value,
					SUM(IF(t.type = 'CGST' OR t.type = 'SGST' OR t.type = 'IGST', t.net_rate, 0)) AS rate,
					i.type,
					i.status,
					i.enterprise_id,
					t.is_compound,
					NULL AS sum_rate,
					egd.details AS enterprise_gst,
					t.net_rate AS common_tax_rate,
					ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS taxable_value_common_tax,
					0 AS item_tax_total,
					0 AS is_item,
					0 AS item_total,
					p.port AS port_code
				FROM
					invoice i
				JOIN invoice_materials im ON i.id = im.invoice_id
					AND i.enterprise_id = im.enterprise_id
				JOIN invoice_tax it ON i.id = it.invoice_id		
					AND i.enterprise_id = it.enterprise_id
				JOIN tax t ON it.tax_code = t.code
					AND i.enterprise_id = t.enterprise_id
				LEFT JOIN party_master p ON p.party_id = i.party_id
					AND p.enterprise_id = i.enterprise_id
				LEFT JOIN party_registration_detail AS prd ON prd.party_id = p.party_id
					AND prd.enterprise_id = p.enterprise_id
					AND TRIM(prd.label) = 'GSTIN'
				LEFT JOIN gst_category gc ON gc.id = p.category_id
				LEFT JOIN enterprise_registration_detail egd ON egd.enterprise_id = i.enterprise_id
					AND TRIM(egd.label) = 'GSTIN'
				WHERE
					%s									            
				GROUP BY i.id, %s, t.type != 'CESS', t.net_rate
				having rate > 0 OR cess_value > 0) UNION ALL
				(SELECT
					i.id,
					im.item_id,
					prd.details AS gstno,
					p.party_name,
					IFNULL(i.invoice_code, CONCAT(
						i.financial_year, '/', SUBSTR(i.type, 1, 1), LPAD(i.invoice_no, 6, 0), IFNULL(i.sub_number, '')
						)) AS invoice_code,
					i.issued_on AS invoice_date,
					i.issued_on AS issued_on,
					i.grand_total * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS invoice_value,
					IF(p.state != '', p.state, p.gstno) AS place_of_supply,
					IF(i.tax_payable_on_reverse_charge = 1, 'Y', 'N') AS reverse_charge,
					gc.name AS invoice_type,
					i.ecommerce_gstin,
					ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS taxable_value,
					ROUND(SUM(IF((t.type = 'CGST'), ((`t`.`net_rate` * (im.qty * im.unit_rate * (100 - im.discount) / 100)) / 100),
                        0)), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS `cgst_value`,
                    ROUND(SUM(IF((t.type = 'SGST'), (t.net_rate * (im.qty * im.unit_rate * (100 - im.discount) / 100)) / 100,
                        0)), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS `sgst_value`,
                    ROUND(SUM(IF((t.type = 'IGST'), (t.net_rate * (im.qty * im.unit_rate * (100 - im.discount) / 100)) / 100,
                        0)), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS `igst_value`,
					NULL AS cess_value,
					SUM(t.net_rate) AS rate,
					i.type,
					i.status,
					i.enterprise_id,
					t.is_compound,
					NULL AS sum_rate,
					egd.details AS enterprise_gst,
					0 AS common_tax_rate,
					0 AS taxable_value_common_tax,
					SUM(ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2)) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS item_tax_total,
					1 AS is_item,
					ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS item_total,
					p.port AS port_code
				FROM
					invoice i
				JOIN invoice_materials im ON i.id = im.invoice_id
					AND i.enterprise_id = im.enterprise_id
				JOIN invoice_item_tax iit ON im.item_id = iit.item_id
					AND im.make_id = iit.make_id
					AND i.id = iit.invoice_id
					AND i.enterprise_id = iit.enterprise_id
				JOIN tax t ON iit.tax_code = t.code
					AND i.enterprise_id = t.enterprise_id
				LEFT JOIN party_master p ON p.party_id = i.party_id
					AND p.enterprise_id = i.enterprise_id
				LEFT JOIN party_registration_detail AS prd ON prd.party_id = p.party_id
					AND prd.enterprise_id = p.enterprise_id
					AND TRIM(prd.label) = 'GSTIN'
				LEFT JOIN gst_category gc ON gc.id = p.category_id
				LEFT JOIN enterprise_registration_detail egd ON egd.enterprise_id = i.enterprise_id
					AND TRIM(egd.label) = 'GSTIN'
				WHERE
					%s
				GROUP BY %s having rate > 0) UNION ALL (
				SELECT
					i.id,
					ic.item_name as item_id,
					prd.details AS gstno,
					p.party_name,
					IFNULL(i.invoice_code, CONCAT(
							i.financial_year, '/', SUBSTR(i.type, 1, 1), LPAD(i.invoice_no, 6, 0), IFNULL(i.sub_number, '')
							)) AS invoice_code,
					i.issued_on AS invoice_date,
					i.issued_on AS issued_on,
					i.grand_total * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS invoice_value,
					IF(p.state != '', p.state, p.gstno) AS place_of_supply,
					IF(i.tax_payable_on_reverse_charge = 1, 'Y', 'N') AS reverse_charge,
					gc.name AS invoice_type,
					i.ecommerce_gstin,
					ROUND((ic.rate * (100 - ic.discount) / 100), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS taxable_value,
					ROUND(SUM(IF((t.type = 'CGST'), ((`t`.`net_rate` * (1 * ic.rate * (100 - ic.discount) / 100)) / 100),
                        0)), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS `cgst_value`,
                    ROUND(SUM(IF((t.type = 'SGST'), (t.net_rate * (1 * ic.rate * (100 - ic.discount) / 100)) / 100,
                        0)), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS `sgst_value`,
                    ROUND(SUM(IF((t.type = 'IGST'), (t.net_rate * (1 * ic.rate * (100 - ic.discount) / 100)) / 100,
                        0)), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS `igst_value`,
					NULL AS cess_value,
					SUM(t.net_rate) AS rate,
					i.type,
					i.status,
					i.enterprise_id,
					t.is_compound,
					NULL AS sum_rate,
					egd.details AS enterprise_gst,
					0 AS common_tax_rate,
					0 AS taxable_value_common_tax,
					SUM(ROUND((ic.rate * (100 - ic.discount) / 100) * (t.net_rate / 100), 2)) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS item_tax_total,
					1 AS is_item,
					ROUND((ic.rate * (100 - ic.discount) / 100), 2) * IF(i.cur_conversion_rate = 0, 1.00000, i.cur_conversion_rate) AS item_total,
					p.port AS port_code
				FROM
						invoice i
				JOIN invoice_charges ic ON i.id = ic.invoice_id
						AND i.enterprise_id = ic.enterprise_id
				JOIN invoice_charge_tax ict ON ic.item_name = ict.item_name			
						AND i.id = ict.invoice_id
						AND i.enterprise_id = ict.enterprise_id
				JOIN tax t ON ict.tax_code = t.code
						AND i.enterprise_id = t.enterprise_id
				LEFT JOIN party_master p ON p.party_id = i.party_id
						AND p.enterprise_id = i.enterprise_id
				LEFT JOIN party_registration_detail AS prd ON prd.party_id = p.party_id
					AND prd.enterprise_id = p.enterprise_id
					AND TRIM(prd.label) = 'GSTIN'
				LEFT JOIN gst_category gc ON gc.id = p.category_id
				LEFT JOIN enterprise_registration_detail egd ON egd.enterprise_id = i.enterprise_id
					AND TRIM(egd.label) = 'GSTIN'
				WHERE
						%s
				GROUP BY i.id , ic.item_name)""" % (where, group_by, where, group_by, where)

		return query

	def build_report_query(self, where=None, group_by=None):
		"""
		build GSTR1 report query
		:return:
		"""
		inner_query_set = self.build_inner_query(where=where, group_by=group_by)
		tbl_group_by = """rate, id""" if "im.item_id" in group_by else "id"
		report_query = """
			SELECT
				id,
				item_id,
				gstno,
				party_name,
				invoice_code,
				invoice_date,
				issued_on,
				invoice_value,
				SUM(cgst_value) AS cgst_value,
                SUM(sgst_value) AS sgst_value,
                SUM(igst_value) AS igst_value,
				place_of_supply,
				reverse_charge,
				IF(invoice_type = 'Registered Business' OR invoice_type= 'Registered Business - Composition', 'Regular', 
				IF(invoice_type = 'Special Economic Zone (SEZ)' OR invoice_type = 'SEZ Developer', 'SEZ Supplies with Payment', invoice_type)) AS invoice_type,
				ecommerce_gstin,
				SUM(taxable_value) AS taxable_value,
				cess_value AS cess_value,
				ROUND(rate, 2) AS rate,
				enterprise_id,
				type,
				status,
				is_compound,
				SUM(taxable_value) + SUM(sum_rate) AS total_value,
				(@cnt := @cnt + 1) AS rowNumber,
				enterprise_gst,
				common_tax_rate,
				SUM(taxable_value_common_tax),
				SUM(item_tax_total) AS item_tax_total,
				SUM(is_item) as is_item,
				IF(COUNT(rate) > 1, 1, 0) as grouping_count,
				SUM(item_total) as item_total,
				port_code
			FROM
				(%s) AS GSTR1
			CROSS JOIN (SELECT @cnt := 0) AS dummy
			GROUP BY %s
			ORDER BY id, invoice_date ASC
			""" % (inner_query_set, tbl_group_by)
		return report_query


class GSTR2Report(object):
	"""
	This class is made to build queries for GSTR1 report module
	"""
	def __init__(self):
		logger.info('GSTR2 report class initiated.')

	def b2b(self, from_date=None, to_date=None, enterprise_id=None):
		"""

		:return:
		"""
		where = """g.inv_date BETWEEN \'%s\' AND \'%s\' AND g.enterprise_id = \'%d\' 
						AND g.rec_against IN ('Purchase Order','Job Work') AND gc.id NOT IN (3 , 4, 5)
						AND g.inv_type != 1 AND g.status > 0
						""" % (from_date, to_date, enterprise_id)

		query = self.b2b_report_query(where)
		return query

	def b2b_subquery(self, from_date=None, to_date=None, enterprise_id=None):
		"""

		:param from_date:
		:param to_date:
		:param enterprise_id:
		:return:
		"""
		where = """g.inv_date BETWEEN \'%s\' AND \'%s\' AND g.enterprise_id = \'%d\' 
		AND g.rec_against IN ('Purchase Order','Job Work') AND gc.id NOT IN (3 , 4, 5)
		AND g.inv_type != 1 AND g.status > 0""" % (from_date, to_date, enterprise_id)

		query = self.b2b_series_sub_query(where)
		return query

	def b2burc_subquery(self, from_date=None, to_date=None, enterprise_id=None):
		"""

		:param from_date:
		:param to_date:
		:param enterprise_id:
		:return:
		"""
		where = """g.inv_date BETWEEN \'%s\' AND \'%s\' AND g.enterprise_id = \'%d\' AND g.inv_type != 1 AND g.status > 0 AND
						g.rec_against IN ('Purchase Order','Job Work') AND gc.id IN (3 , 4, 5)""" % (from_date, to_date, enterprise_id)

		query = self.b2b_series_sub_query(where)
		return query

	def b2burc(self, from_date=None, to_date=None, enterprise_id=None):
		"""

		:return:
		"""
		where = """g.inv_date BETWEEN \'%s\' AND \'%s\' AND g.enterprise_id = \'%d\' AND g.rec_against IN ('Purchase Order','Job Work') AND g.status > 0 
					AND g.inv_type != 1 AND gc.id IN (3 , 4, 5) """ % (from_date, to_date, enterprise_id)

		query = self.b2burc_report_query(where)
		return query

	def hsn(self, from_date=None, to_date=None, enterprise_id=None):
		"""

		:return:
		"""
		where = """IFNULL(g.inv_date, NOW()) BETWEEN '%s' AND '%s' AND g.enterprise_id = '%s' AND g.status > 0 AND g.inv_type != 1 AND gm.hsn_code NOT IN ('NIL-RATED', 'EXEMPT', 'NON-GST') AND TRIM(g.rec_against) != 'Others'""" % (
					from_date, to_date, enterprise_id)
		query = self.hsn_inner_query(where=where)
		return query

	def exemp(self, from_date=None, to_date=None, enterprise_id=None, conditions=None):
		"""

		:return:
		"""
		where = """IFNULL(g.inv_date, NOW()) BETWEEN '%s' AND '%s' AND g.enterprise_id = '%s' AND g.status > 0 %s""" % (
			from_date, to_date, enterprise_id, conditions)
		query = self.exemp_inner_query(where=where)
		return query

	def impg(self, from_date=None, to_date=None, enterprise_id=None):
		"""

		:return:
		"""
		where = """IFNULL(g.inv_date, NOW()) BETWEEN '%s' AND '%s' AND g.enterprise_id = '%s' AND g.status > 0 AND g.inv_type != 1 AND m.is_service = 0
					AND gc.id IN (5, 6, 7, 9)""" % (from_date, to_date, enterprise_id)
		query = self.impg_inner_query(where=where)
		return query

	def imps(self, from_date=None, to_date=None, enterprise_id=None):
		"""

		:return:
		"""
		where = """IFNULL(g.inv_date, NOW()) BETWEEN '%s' AND '%s' AND g.enterprise_id = '%s' AND g.status > 0 AND g.inv_type != 1 AND m.is_service = 1
					AND gc.id IN (5, 6, 7, 9)""" % (from_date, to_date, enterprise_id)
		query = self.imps_inner_query(where=where)
		return query

	def cdnr(self, from_date=None, to_date=None, enterprise_id=None):
		"""

		:return:
		"""
		where = """IFNULL(note.note_datetime, NOW()) BETWEEN '%s' AND '%s' AND IF(g.rec_against is not null, g.rec_against != 'Sales Return', TRUE) AND note.status >= 3
					AND note.enterprise_id = '%s' AND gc.id IN (1 , 2, 6, 7, 8, 9)""" % (from_date, to_date, enterprise_id)
		query = self.cdnr_inner_query(where=where)
		return query

	def cdnr_grn(self, from_date=None, to_date=None, enterprise_id=None):
		"""

		:return:
		"""
		where = """IFNULL(note.note_datetime, NOW()) BETWEEN '%s' AND '%s' AND note.status >= 3 AND g.rec_against = 'Sales Return' 
					AND note.enterprise_id = '%s' AND gc.id IN (1 , 2, 6, 7, 8, 9)""" % (from_date, to_date, enterprise_id)
		query = self.cdnr_inner_query(where=where)
		return query

	def cdnur(self, from_date=None, to_date=None, enterprise_id=None):
		"""

		:return:
		"""
		where = """IFNULL(note.note_datetime, NOW()) BETWEEN '%s' AND '%s' AND IF(g.rec_against is not null, g.rec_against != 'Sales Return', TRUE) AND note.status >= 3
					AND note.enterprise_id = '%s' AND gc.id IN (3, 4, 5)""" % (from_date, to_date, enterprise_id)
		query = self.cdnr_inner_query(where=where)
		return query

	def cdnur_grn(self, from_date=None, to_date=None, enterprise_id=None):
		"""

		:return:
		"""
		where = """IFNULL(note.note_datetime, NOW()) BETWEEN '%s' AND '%s' AND g.rec_against = 'Sales Return' AND note.status >= 3
					AND note.enterprise_id = '%s' AND gc.id IN (3, 4, 5)""" % (from_date, to_date, enterprise_id)
		query = self.cdnr_inner_query(where=where)
		return query

	def exemp_inner_query(self, where=None):
		"""

		:param where:
		:return:
		"""
		query = """SELECT hsn_code, 
					SUM(ROUND((gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100), 2)) * g.cur_conversion_rate AS inv_total, 
					p.state AS party_state, prd.details AS party_gstin,	e.state AS enterprise_state, erd.details AS enterprise_gstin, SUM(g.net_inv_value) * g.cur_conversion_rate AS grn_value
				FROM
					grn AS g
						LEFT JOIN
					grn_material gm ON g.grn_no = gm.grnNumber
						AND g.enterprise_id = gm.enterprise_id
						LEFT JOIN
					party_master p ON p.party_id = g.party_id
						AND p.enterprise_id = g.enterprise_id
						LEFT JOIN
					party_registration_detail AS prd ON prd.party_id = p.party_id
						AND prd.enterprise_id = p.enterprise_id
						AND TRIM(prd.label) = 'GSTIN'
						LEFT JOIN
					gst_category gc ON gc.id = p.category_id
						LEFT JOIN
					enterprise e ON e.id = g.enterprise_id
						LEFT JOIN
					enterprise_registration_detail AS erd ON erd.enterprise_id = g.enterprise_id
						AND TRIM(erd.label) = 'GSTIN'
				WHERE %s """ % where
		return query

	def hsn_inner_query(self, where=None):
		"""

		:param where:
		:return:
		"""
		query = """SELECT 
						grn_no,                                     
						item_id,
						hsn_code,		
						name,						
						description,
						unit_name,
						SUM(dc_qty) AS qty,
						SUM(total_value) AS total_value,
						SUM(taxable_value) AS taxable_value,
						SUM(cgst_value) AS cgst_value,
						SUM(sgst_value) AS sgst_value,
						SUM(igst_value) AS igst_value,
						SUM(cess_value) AS cess_value,
						enterprise_id,
						inv_date,
						status,
						inv_type, 
						(@cnt := @cnt + 1) AS rowNumber,
						SUM(is_cess_compound) AS  is_cess_compound 
					FROM ((SELECT 
						g.grn_no,		
						gm.item_id,
						gm.hsn_code,
						m.name,
						m.description,
						um.unit_description AS unit_name,						
						ROUND(SUM(gm.dc_qty)/IF(count(t.type) != 0,count(t.type), 1), 2) AS dc_qty,        
						ROUND(SUM(g.net_inv_value)/IF(count(t.type) != 0,count(t.type), 1), 2) * g.cur_conversion_rate AS total_value,
						ROUND(SUM(gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100)/IF(count(t.type) != 0,count(t.type), 1), 2) * g.cur_conversion_rate AS taxable_value,
						ROUND(SUM(IF((t.type = 'CGST'),
								((`t`.`net_rate` * (gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100)) / 100), 0)),
										2) * g.cur_conversion_rate AS `cgst_value`,
						ROUND(SUM(IF((t.type = 'SGST'), (t.net_rate * (gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100)) / 100,
								0)), 2) * g.cur_conversion_rate AS `sgst_value`,
						ROUND(SUM(IF((t.type = 'IGST'), (t.net_rate * (gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100)) / 100,
								0)), 2) * g.cur_conversion_rate AS `igst_value`,
						0 AS cess_value,
						g.enterprise_id,
						g.inv_date,
						g.status,
						g.inv_type,
						0 AS is_cess_compound    
					FROM
					grn g
					    LEFT JOIN
					grn_material gm ON g.grn_no = gm.grnNumber
					    AND g.enterprise_id = gm.enterprise_id
					    LEFT JOIN
					materials m ON m.id = gm.item_id
					    AND m.enterprise_id = gm.enterprise_id
					    LEFT JOIN
					unit_master um ON um.unit_id = m.unit
					    AND um.enterprise_id = m.enterprise_id
					    LEFT JOIN
					grn_material_tax gmt ON gm.item_id = gmt.item_id
					    AND gm.make_id = gmt.make_id
					    AND gm.grnNumber = gmt.grn_no
					    AND gm.enterprise_id = gmt.enterprise_id
					    LEFT JOIN
					tax t ON gmt.tax_code = t.code
					    AND g.enterprise_id = t.enterprise_id
					    LEFT JOIN
					party_master p ON p.party_id = g.party_id
					    AND p.enterprise_id = g.enterprise_id
					    LEFT JOIN
					gst_category gc ON gc.id = p.category_id
					WHERE
						%s					  
					GROUP BY hsn_code, g.grn_no, gm.item_id) UNION (SELECT 
					g.grn_no,
					gm.item_id,
					gm.hsn_code,
					m.name,
					m.description,
					um.unit_description AS unit_name,
					0 AS dc_qty,
					0 AS total_value,
					0 AS taxable_value,
					ROUND(SUM(IF((t.type = 'CGST'),
					            ((`t`.`net_rate` * (gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100)) / 100),
					            0)),
					        2) * g.cur_conversion_rate AS `cgst_value`,
					ROUND(SUM(IF((t.type = 'SGST'),
					            (t.net_rate * (gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100)) / 100,
					            0)),
					        2) * g.cur_conversion_rate AS `sgst_value`,
					ROUND(SUM(IF((t.type = 'IGST'),
					            (t.net_rate * (gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100)) / 100,
					            0)),
					        2) * g.cur_conversion_rate AS `igst_value`,
					SUM(IF(t.type = 'CESS', t.net_rate, 0)) * g.cur_conversion_rate AS cess_value,
					g.enterprise_id,
					g.inv_date,
					g.status,
					g.inv_type,
					SUM(IF(t.type = 'CESS' AND t.is_compound, 1, 0)) AS is_cess_compound
					FROM
					grn g
					    LEFT JOIN
					grn_material gm ON g.grn_no = gm.grnNumber
					    AND g.enterprise_id = gm.enterprise_id
					    LEFT JOIN
					materials m ON m.id = gm.item_id
					    AND m.enterprise_id = gm.enterprise_id
					    LEFT JOIN
					unit_master um ON um.unit_id = m.unit
					    AND um.enterprise_id = m.enterprise_id
					    LEFT JOIN
					grn_tax gt ON g.grn_no = gt.grn_no
					    AND g.enterprise_id = gt.enterprise_id
					    LEFT JOIN
					tax t ON gt.tax_code = t.code
					    AND g.enterprise_id = t.enterprise_id
					    LEFT JOIN
					party_master p ON p.party_id = g.party_id
					    AND p.enterprise_id = g.enterprise_id
					    LEFT JOIN
					gst_category gc ON gc.id = p.category_id
					WHERE
						%s					  
					GROUP BY hsn_code)) AS GSTR2
					CROSS JOIN (SELECT @cnt := 0) AS dummy
					group by hsn_code
					order by rowNumber, grn_no  ASC""" % (where, where)

		return query

	def impg_inner_query(self, where=None):
		"""

		:param where:
		:return:
		"""
		query = """SELECT 			
						grn_no,
						item_id,
						hsn_code,
						party_name,
						grn_invoice_date,
						grn_total,
						gst_type,
						IF(IF(COUNT(rate) > 1, 1, 0)>0, (SUM(taxable_value) * 2) + SUM(taxable_value_common_tax), SUM(taxable_value)) AS taxable_value,
						igst_value,
						cess_value AS cess_value,
						ROUND(rate, 2) AS rate,
						enterprise_id,
						status,
						is_compound,
						SUM(taxable_value) + SUM(sum_rate) AS total_value,
						(@cnt:=@cnt + 1) AS rowNumber,
						enterprise_gst,
						common_tax_rate,
						SUM(taxable_value_common_tax) AS taxable_value_common_tax,
						SUM(item_tax_total)  AS item_tax_total,
						SUM(is_item) AS is_item,
						IF(COUNT(rate) > 1, 1, 0) AS grouping_count,
						SUM(item_total) AS item_total,
						port_code,
						party_gstin,
						grn_inv_no
						FROM
						((SELECT 
						g.grn_no,
						    gm.item_id,
						    gm.hsn_code,
						    p.party_name,
						    g.inv_date AS grn_invoice_date,
						    g.invno AS grn_inv_no,
						    g.net_inv_value * g.cur_conversion_rate AS grn_total,
						    gc.name AS gst_type,
						    0 AS taxable_value,
						    ROUND(SUM(IF((t.type = 'IGST'), (t.net_rate * (gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100)) / 100, 0)), 2) * g.cur_conversion_rate AS `igst_value`,
						    IF(t.type = 'CESS', SUM(t.net_rate) / COUNT(t.type), NULL) * g.cur_conversion_rate AS cess_value,
						    IF(t.type = 'IGST', t.net_rate, 0) AS rate,
						    g.status,
						    g.enterprise_id,
						    t.is_compound,
						    NULL AS sum_rate,
						    egd.details AS enterprise_gst,
						    t.net_rate AS common_tax_rate,
						    ROUND((gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100), 2) * g.cur_conversion_rate AS taxable_value_common_tax,
						    0 AS item_tax_total,
						    0 AS is_item,
						    0 AS item_total,
						    p.gstno AS party_gstno,
						    p.port AS port_code,
						    prd.details AS party_gstin
						FROM
						grn g
						LEFT JOIN grn_material gm ON g.grn_no = gm.grnNumber
						AND g.enterprise_id = gm.enterprise_id
						LEFT JOIN materials m ON m.id = gm.item_id
						AND m.enterprise_id = gm.enterprise_id
						LEFT JOIN unit_master um ON um.unit_id = m.unit
						AND um.enterprise_id = m.enterprise_id
						LEFT JOIN grn_tax gt ON g.grn_no = gt.grn_no
						AND g.enterprise_id = gt.enterprise_id
						LEFT JOIN tax t ON gt.tax_code = t.code
						AND g.enterprise_id = t.enterprise_id
						LEFT JOIN party_master p ON p.party_id = g.party_id
						AND p.enterprise_id = g.enterprise_id
						LEFT JOIN party_registration_detail AS prd ON prd.party_id = p.party_id
						AND prd.enterprise_id = p.enterprise_id
						AND TRIM(prd.label) = 'GSTIN'
						LEFT JOIN gst_category gc ON gc.id = p.category_id
						LEFT JOIN enterprise_registration_detail egd ON egd.enterprise_id = g.enterprise_id
						AND egd.label = 'GSTIN'
						WHERE
						%s
						GROUP BY g.grn_no, t.net_rate
						HAVING rate > 0 OR cess_value > 0) UNION (SELECT 
						g.grn_no,
						    gm.item_id,
						    gm.hsn_code,
						    p.party_name,
						    g.inv_date AS grn_invoice_date,
						    g.invno AS grn_inv_no,
						    g.net_inv_value * g.cur_conversion_rate AS grn_total,
						    gc.name AS gst_type,
						    ROUND((gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100), 2) * g.cur_conversion_rate AS taxable_value,
						    ROUND(SUM(IF((t.type = 'IGST'), (t.net_rate * (gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100)) / 100, 0)), 2) * g.cur_conversion_rate AS `igst_value`,
						    NULL AS cess_value,
						    SUM(t.net_rate) AS rate,
						    g.status,
						    g.enterprise_id,
						    t.is_compound,
						    NULL AS sum_rate,
						    egd.details AS enterprise_gst,
						    0 AS common_tax_rate,
						    0 AS taxable_value_common_tax,
						    SUM(ROUND((gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100) * (t.net_rate / 100), 2)) * g.cur_conversion_rate AS item_tax_total,
						    1 AS is_item,
						    ROUND((gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100), 2) * g.cur_conversion_rate AS item_total,
						    p.gstno AS party_gstno,
						    p.port AS port_code,
						    prd.details AS party_gstin
						FROM
						grn g
						LEFT JOIN grn_material gm ON g.grn_no = gm.grnNumber
						AND g.enterprise_id = gm.enterprise_id
						LEFT JOIN materials m ON m.id = gm.item_id
						AND m.enterprise_id = gm.enterprise_id
						LEFT JOIN unit_master um ON um.unit_id = m.unit
						AND um.enterprise_id = m.enterprise_id
						LEFT JOIN grn_material_tax gmt ON gm.item_id = gmt.item_id
						AND gm.make_id = gmt.make_id
						AND g.grn_no = gmt.grn_no
						AND g.enterprise_id = gmt.enterprise_id
						LEFT JOIN tax t ON gmt.tax_code = t.code
						AND g.enterprise_id = t.enterprise_id
						LEFT JOIN party_master p ON p.party_id = g.party_id
						AND p.enterprise_id = g.enterprise_id
						LEFT JOIN party_registration_detail AS prd ON prd.party_id = p.party_id
						AND prd.enterprise_id = p.enterprise_id
						AND TRIM(prd.label) = 'GSTIN'
						LEFT JOIN gst_category gc ON gc.id = p.category_id
						LEFT JOIN enterprise_registration_detail egd ON egd.enterprise_id = g.enterprise_id
						AND egd.label = 'GSTIN'
						WHERE
						%s
						GROUP BY g.grn_no , gm.item_id , gm.make_id HAVING rate > 0 OR cess_value > 0)) AS GSTR2
						CROSS JOIN
						(SELECT @cnt:=0) AS dummy
						GROUP BY rate , grn_no
						ORDER BY grn_invoice_date ASC
						""" % (where, where)

		return query

	def imps_inner_query(self, where=None):
		"""

		:param where:
		:return:
		"""
		query = """SELECT 			
						grn_no,
						item_id,
						hsn_code,
						party_name,
						grn_invoice_date,
						grn_total,
						gst_type,
						IF(IF(COUNT(rate) > 1, 1, 0)>0, (SUM(taxable_value) * 2) + SUM(taxable_value_common_tax), SUM(taxable_value)) AS taxable_value,
						igst_value,
						cess_value AS cess_value,
						ROUND(rate, 2) AS rate,
						enterprise_id,
						status,
						is_compound,
						SUM(taxable_value) + SUM(sum_rate) AS total_value,
						(@cnt:=@cnt + 1) AS rowNumber,
						enterprise_gst,
						common_tax_rate,
						SUM(taxable_value_common_tax) AS taxable_value_common_tax,
						SUM(item_tax_total)  AS item_tax_total,
						SUM(is_item) AS is_item,
						IF(COUNT(rate) > 1, 1, 0) AS grouping_count,
						SUM(item_total) AS item_total,
						port_code,
						party_gstin,
						grn_inv_no,
						place_of_supply
						FROM
						((SELECT 
						g.grn_no,
						    gm.item_id,
						    gm.hsn_code,
						    p.party_name,
						    g.inv_date AS grn_invoice_date,
						    g.invno AS grn_inv_no,
						    g.net_inv_value * g.cur_conversion_rate AS grn_total,
						    gc.name AS gst_type,
						    0 AS taxable_value,
						    ROUND(SUM(IF((t.type = 'IGST'), (t.net_rate * (gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100)) / 100, 0)), 2) * g.cur_conversion_rate AS `igst_value`,
						    IF(t.type = 'CESS', SUM(t.net_rate) / COUNT(t.type), NULL) * g.cur_conversion_rate AS cess_value,
						    IF(t.type = 'IGST', t.net_rate, 0) AS rate,
						    g.status,
						    g.enterprise_id,
						    t.is_compound,
						    NULL AS sum_rate,
						    egd.details AS enterprise_gst,
						    t.net_rate AS common_tax_rate,
						    ROUND((gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100), 2) * g.cur_conversion_rate AS taxable_value_common_tax,
						    0 AS item_tax_total,
						    0 AS is_item,
						    0 AS item_total,
						    p.gstno AS party_gstno,
						    p.port AS port_code,
						    prd.details AS party_gstin,
						    IF(p.state != '', p.state, p.gstno) AS place_of_supply
						FROM
						grn g
						LEFT JOIN grn_material gm ON g.grn_no = gm.grnNumber
						AND g.enterprise_id = gm.enterprise_id
						LEFT JOIN materials m ON m.id = gm.item_id
						AND m.enterprise_id = gm.enterprise_id
						LEFT JOIN unit_master um ON um.unit_id = m.unit
						AND um.enterprise_id = m.enterprise_id
						LEFT JOIN grn_tax gt ON g.grn_no = gt.grn_no
						AND g.enterprise_id = gt.enterprise_id
						LEFT JOIN tax t ON gt.tax_code = t.code
						AND g.enterprise_id = t.enterprise_id
						LEFT JOIN party_master p ON p.party_id = g.party_id
						AND p.enterprise_id = g.enterprise_id
						LEFT JOIN party_registration_detail AS prd ON prd.party_id = p.party_id
						AND prd.enterprise_id = p.enterprise_id
						AND TRIM(prd.label) = 'GSTIN'
						LEFT JOIN gst_category gc ON gc.id = p.category_id
						LEFT JOIN enterprise_registration_detail egd ON egd.enterprise_id = g.enterprise_id
						AND egd.label = 'GSTIN'
						WHERE
						%s
						GROUP BY g.grn_no, t.net_rate
						HAVING rate > 0 OR cess_value > 0) UNION (SELECT 
						g.grn_no,
						    gm.item_id,
						    gm.hsn_code,
						    p.party_name,
						    g.inv_date AS grn_invoice_date,
						    g.invno AS grn_inv_no,
						    g.net_inv_value * g.cur_conversion_rate AS grn_total,
						    gc.name AS gst_type,
						    ROUND((gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100), 2) * g.cur_conversion_rate AS taxable_value,
						    ROUND(SUM(IF((t.type = 'IGST'), (t.net_rate * (gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100)) / 100, 0)), 2) * g.cur_conversion_rate AS `igst_value`,
						    NULL AS cess_value,
						    SUM(t.net_rate) AS rate,
						    g.status,
						    g.enterprise_id,
						    t.is_compound,
						    NULL AS sum_rate,
						    egd.details AS enterprise_gst,
						    0 AS common_tax_rate,
						    0 AS taxable_value_common_tax,
						    SUM(ROUND((gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100) * (t.net_rate / 100), 2)) * g.cur_conversion_rate AS item_tax_total,
						    1 AS is_item,
						    ROUND((gm.dc_qty * gm.inv_rate * (100 - gm.discount) / 100), 2) * g.cur_conversion_rate AS item_total,
						    p.gstno AS party_gstno,
						    p.port AS port_code,
						    prd.details AS party_gstin,
						    IF(p.state != '', p.state, p.gstno) AS place_of_supply
						FROM
						grn g
						LEFT JOIN grn_material gm ON g.grn_no = gm.grnNumber
						AND g.enterprise_id = gm.enterprise_id
						LEFT JOIN materials m ON m.id = gm.item_id
						AND m.enterprise_id = gm.enterprise_id
						LEFT JOIN unit_master um ON um.unit_id = m.unit
						AND um.enterprise_id = m.enterprise_id
						LEFT JOIN grn_material_tax gmt ON gm.item_id = gmt.item_id
						AND gm.make_id = gmt.make_id
						AND g.grn_no = gmt.grn_no
						AND g.enterprise_id = gmt.enterprise_id
						LEFT JOIN tax t ON gmt.tax_code = t.code
						AND g.enterprise_id = t.enterprise_id
						LEFT JOIN party_master p ON p.party_id = g.party_id
						AND p.enterprise_id = g.enterprise_id
						LEFT JOIN party_registration_detail AS prd ON prd.party_id = p.party_id
						AND prd.enterprise_id = p.enterprise_id
						AND TRIM(prd.label) = 'GSTIN'
						LEFT JOIN gst_category gc ON gc.id = p.category_id
						LEFT JOIN enterprise_registration_detail egd ON egd.enterprise_id = g.enterprise_id
						AND egd.label = 'GSTIN'
						WHERE
						%s
						GROUP BY g.grn_no , gm.item_id , gm.make_id HAVING rate > 0 OR cess_value > 0)) AS GSTR2
						CROSS JOIN
						(SELECT @cnt:=0) AS dummy
						GROUP BY rate , grn_no
						ORDER BY grn_invoice_date ASC
						""" % (where, where)

		return query

	def b2b_series_sub_query(self, where=None):
		query = """select distinct g.grn_no, sum(if(t.type = 'CESS' and t.is_compound = 1,t.net_rate,0) ) as compound,
					sum(if(t.type = 'CESS' and t.is_compound = 0,t.net_rate,0)) as not_compound,
					sum(if(t.type NOT IN('CESS')  ,t.net_rate,0)) as Tax
					from
					grn g
					left join grn_material gm on gm.grnNumber = g.grn_no
					and gm.enterprise_id = g.enterprise_id
					join grn_tax gt on gm.grnNumber = gt.grn_no
					and gm.enterprise_id = gt.enterprise_id
					join tax t on t.code = gt.tax_code
					and t.enterprise_id = gt.enterprise_id
					LEFT JOIN party_master p ON p.party_id = g.party_id
					AND p.enterprise_id = g.enterprise_id
					LEFT JOIN enterprise e ON g.enterprise_id = e.id
					LEFT JOIN gst_category gc ON gc.id = p.category_id
					WHERE %s
					group by gm.item_id,gm.make_id,g.grn_no
					having compound > 0 or not_compound >0""" % where
		return query

	def build_inner_query(self, where=None):
		"""
		build inner query set
		:param where:
		:return:
		"""
		query = """(SELECT 
						i.id,
						im.item_id,
						i.ship_to_gstno,
						p.party_name,
						IFNULL(i.invoice_code, CONCAT(i.financial_year, '/', SUBSTR(i.type, 1, 1), LPAD(i.invoice_no, 6, 0), IFNULL(i.sub_number, ''))) AS invoice_code,
						i.issued_on AS invoice_date,
						i.grand_total AS invoice_value,
						i.ship_to_gstno AS place_of_supply,
						IF(i.tax_payable_on_reverse_charge = 1, 'Y', 'N') AS reverse_charge,
						gc.name AS invoice_type,
						i.ecommerce_gstin,
						ROUND(SUM(im.qty * im.unit_rate * (100 - im.discount) / 100), 2) AS taxable_value,
						IF(t.type = 'CESS', SUM(t.net_rate)/count(t.type) , NULL) AS cess_value,
						IF(t.type = 'CGST' OR t.type = 'SGST' OR t.type = 'IGST', t.net_rate, 0) AS rate,
						i.type,
						i.status,
						i.enterprise_id,
						t.is_compound,
						NULL AS sum_rate,
						egd.details AS enterprise_gst,
						t.net_rate AS common_tax_rate,
						ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100), 2) AS taxable_value_common_tax,
						0 AS item_tax_total,
						0 AS is_item,
						0 AS item_total
					FROM
						invoice i
					JOIN invoice_materials im ON i.id = im.invoice_id
						AND i.enterprise_id = im.enterprise_id
					JOIN invoice_tax it ON i.id = it.invoice_id		
						AND i.enterprise_id = it.enterprise_id
					JOIN tax t ON it.tax_code = t.code
						AND i.enterprise_id = t.enterprise_id
					LEFT JOIN party_master p ON p.party_id = i.party_id
						AND p.enterprise_id = i.enterprise_id
					LEFT JOIN gst_category gc ON gc.id = p.category_id
					LEFT JOIN enterprise_registration_detail egd ON egd.enterprise_id = i.enterprise_id
						AND egd.label = 'GST'
					WHERE
						%s									            
					GROUP BY i.id, t.net_rate
					having rate > 0 OR cess_value > 0) UNION 
					(SELECT
						i.id,
						im.item_id,
						i.ship_to_gstno,
						p.party_name,
						IFNULL(i.invoice_code, CONCAT(
							i.financial_year, '/', SUBSTR(i.type, 1, 1), LPAD(i.invoice_no, 6, 0), IFNULL(i.sub_number, '')
							)) AS invoice_code,
						i.issued_on AS invoice_date,
						i.grand_total AS invoice_value,
						i.ship_to_gstno AS place_of_supply,
						IF(i.tax_payable_on_reverse_charge = 1, 'Y', 'N') AS reverse_charge,
						gc.name AS invoice_type,
						i.ecommerce_gstin,
						ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100), 2) AS taxable_value,
						NULL AS cess_value,
						SUM(t.net_rate) AS rate,
						i.type,
						i.status,
						i.enterprise_id,
						t.is_compound,
						NULL AS sum_rate,
						egd.details AS enterprise_gst,
						0 AS common_tax_rate,
						0 AS taxable_value_common_tax,
						SUM(ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100) * (t.net_rate / 100), 2)) AS item_tax_total,
						1 AS is_item,
						ROUND((im.qty * im.unit_rate * (100 - im.discount) / 100), 2) AS item_total
					FROM
						invoice i
					JOIN invoice_materials im ON i.id = im.invoice_id
						AND i.enterprise_id = im.enterprise_id
					JOIN invoice_item_tax iit ON im.item_id = iit.item_id
						AND im.make_id = iit.make_id
						AND i.id = iit.invoice_id
						AND i.enterprise_id = iit.enterprise_id
					JOIN tax t ON iit.tax_code = t.code
						AND i.enterprise_id = t.enterprise_id
					LEFT JOIN party_master p ON p.party_id = i.party_id
						AND p.enterprise_id = i.enterprise_id
					LEFT JOIN gst_category gc ON gc.id = p.category_id
					LEFT JOIN enterprise_registration_detail egd ON egd.enterprise_id = i.enterprise_id
						AND egd.label = 'GST'
					WHERE
						%s
					GROUP BY i.id , im.item_id, im.make_id)""" % (where, where)

		return query

	def b2b_report_query(self, where=None):
		"""

		:param where:
		:return:
		"""
		query = """select gstno,invno,inv_date,net_inv_value,place_of_supply,reverse_charge,name,total_rate,
	         sum(total_value) as taxable_value,
	         round(sum(igst),2) as igst_value,
	         round(sum(cgst),2) as cgst_value,
	         round(sum(sgst),2) as sgst_value,
	         sum(distinct c_cess) + sum( nc_cess) as cess,ITC,
	         grn_no, party_name,voucher_code
	         FROM
	         ((select g.grn_no,prd.details as gstno,g.invno,left(g.inv_date,10) as inv_date,
	         g.net_inv_value * g.cur_conversion_rate AS net_inv_value,IF(p.state != '', p.state, p.gstno) AS place_of_supply,'' as reverse_charge,gc.name,
	         t.net_rate as total_rate,
	         SUM(round((gm.dc_qty*gm.inv_rate)*((100-gm.discount)/100),2))/COUNT(DISTINCT t.code) * g.cur_conversion_rate as total_value,
	         ROUND(SUM(((IF(t.type = 'CGST', t.net_rate, 0))) * ((gm.dc_qty * gm.inv_rate) * ((100 - gm.discount) / 100) / 100)), 2) * g.cur_conversion_rate as cgst,
	         ROUND(SUM(((IF(t.type = 'IGST', t.net_rate, 0))) * ((gm.dc_qty * gm.inv_rate) * ((100 - gm.discount) / 100) / 100)), 2) * g.cur_conversion_rate as igst,
	         ROUND(SUM(((IF(t.type = 'SGST', t.net_rate, 0))) * ((gm.dc_qty * gm.inv_rate) * ((100 - gm.discount) / 100) / 100)), 2) * g.cur_conversion_rate as sgst,
	         0 as c_cess,
	         0 as nc_cess,
	         '' as ITC,p.party_name as party_name,
	         IF(v.status != 0,
			(IFNULL(CONCAT(v.financial_year,'/',vt.code, '/', LPAD(v.voucher_no, 6, '0')), "")),
			(IFNULL(CONCAT('TMP#-', v.id), ""))) AS voucher_code
	         FROM grn g
	         left join grn_material gm on g.grn_no = gm.grnNumber
	         and g.enterprise_id = gm.enterprise_id
	         left join grn_material_tax gmt on gm.grnNumber=gmt.grn_no	         
	         and gm.enterprise_id = gmt.enterprise_id
	         and gm.item_id = gmt.item_id
	         and gm.make_id=gmt.make_id
	         and (gm.po_no = gmt.po_id OR gm.po_no is NULL)
	         left join tax t on t.code = gmt.tax_code
	         and gmt.enterprise_id = t.enterprise_id
	         LEFT JOIN party_master p ON p.party_id = g.party_id
	         AND p.enterprise_id = g.enterprise_id
	         LEFT JOIN party_registration_detail prd ON p.party_id = prd.party_id
			 AND p.enterprise_id = prd.enterprise_id
			 AND prd.label = 'GSTIN'
	         LEFT JOIN enterprise e ON g.enterprise_id = e.id
	         LEFT JOIN gst_category gc ON gc.id = p.category_id
	         LEFT JOIN voucher v ON v.id= g.voucher_id
	         LEFT JOIN voucher_type vt ON v.type = vt.id
	         WHERE %s    
	         group by gm.item_id,gm.make_id,g.grn_no having total_rate > 0)    
	         union all (select g.grn_no,prd.details as gstno,g.invno,left(g.inv_date,10) as inv_date,
	         g.net_inv_value * g.cur_conversion_rate AS net_inv_value,IF(p.state != '', p.state, p.gstno) AS place_of_supply,'' as reverse_charge,gc.name,
	         if(t.type = 'CGST' or t.type = 'SGST' or t.type = 'IGST',round(t.net_rate,2),0) as total_rate,
	         SUM(round((gm.dc_qty*gm.inv_rate)*((100-gm.discount)/100),2))/COUNT(DISTINCT t.code) * g.cur_conversion_rate as total_value,
	         round((sum(if(t.type='CGST',t.net_rate,0)))*((gm.dc_qty*gm.inv_rate)*
	         ((100-gm.discount)/100)/100),2) * g.cur_conversion_rate as cgst,
	         round((sum(if(t.type='IGST',t.net_rate,0)))*((gm.dc_qty*gm.inv_rate)*
	         ((100-gm.discount)/100)/100),2) * g.cur_conversion_rate as igst,
	         round((sum(if(t.type='SGST',t.net_rate,0)))*((gm.dc_qty*gm.inv_rate)*
	         ((100-gm.discount)/100)/100),2) * g.cur_conversion_rate as sgst,
	         round(g.net_inv_value-(g.net_inv_value*100/(sum(if(t.type = 'CESS' and t.is_compound=1,
	         round(t.net_rate,2),0))+100)),2) * g.cur_conversion_rate as c_cess,
	         round(((gm.dc_qty*gm.inv_rate)*((100-gm.discount)/100))*(sum(if(t.type = 'CESS' and t.is_compound=0,
	         round(t.net_rate,2),0)))/100,2) * g.cur_conversion_rate as nc_cess,
	         '' as ITC,p.party_name as party_name,
	         IF(v.status != 0,
			(IFNULL(CONCAT(v.financial_year,'/',vt.code, '/', LPAD(v.voucher_no, 6, '0')), "")),
			(IFNULL(CONCAT('TMP#-', v.id), ""))) AS voucher_code
	         FROM grn g
	         JOIN grn_material gm ON g.grn_no = gm.grnNumber
	         and g.enterprise_id = gm.enterprise_id
	         left join grn_tax gt on gm.grnNumber=gt.grn_no
	         and gm.enterprise_id = gt.enterprise_id
	         JOIN tax t ON gt.tax_code = t.code
	         AND gt.enterprise_id = t.enterprise_id
	         LEFT JOIN party_master p ON p.party_id = g.party_id
	         AND p.enterprise_id = g.enterprise_id
	         LEFT JOIN party_registration_detail prd ON p.party_id = prd.party_id
			 AND p.enterprise_id = prd.enterprise_id
			 AND prd.label = 'GSTIN'
	         LEFT JOIN enterprise e ON g.enterprise_id = e.id
	         LEFT JOIN gst_category gc ON gc.id = p.category_id
	         LEFT JOIN voucher v ON v.id= g.voucher_id
	         LEFT JOIN voucher_type vt ON v.type = vt.id
	         WHERE %s 
	         group by gm.make_id,gm.item_id,g.grn_no, gm.po_no)) as gstr2_b2b group by grn_no,total_rate
	         order by invno,inv_date ASC""" % (where, where)
		return query

	def b2burc_report_query(self, where=None):
		"""

		:param where:
		:return:
		"""
		conct = """'%',LOWER(TRIM(REPLACE(e_state, ' ',''))),'%'"""
		query = """select party_name,invno,inv_date,net_inv_value,place_of_supply,
	         if(LOWER(REPLACE(p_state, ' ', '')) like concat(%s),'Intra State','Inter State') as supply_type,
	         reverse_charge,total_rate,
	         sum(total_value) as taxable_value,
	         round(sum(igst),2) as igst_value,
	         round(sum(cgst),2) as cgst_value,
	         round(sum(sgst),2) as sgst_value,
	         sum(distinct c_cess) + sum( nc_cess) as cess,ITC,
	         grn_no
	         FROM
	         ((select g.grn_no,p.party_name,g.invno,left(g.inv_date,10) as inv_date,
	         g.net_inv_value * g.cur_conversion_rate AS net_inv_value ,IF(p.state != '', p.state, p.gstno) AS place_of_supply,p.state as p_state,e.state as e_state,'' as reverse_charge,
	         sum(t.net_rate) as total_rate,
	         round((gm.dc_qty*gm.inv_rate)*((100-gm.discount)/100),2) * g.cur_conversion_rate as total_value,
	         round((sum(if(t.type='CGST',t.net_rate,0)))*((gm.dc_qty*gm.inv_rate)*
	         ((100-gm.discount)/100)/100),2) * g.cur_conversion_rate as cgst,
	         round((sum(if(t.type='IGST',t.net_rate,0)))*((gm.dc_qty*gm.inv_rate)*
	         ((100-gm.discount)/100)/100),2) * g.cur_conversion_rate as igst,
	         round((sum(if(t.type='SGST',t.net_rate,0)))*((gm.dc_qty*gm.inv_rate)*
	         ((100-gm.discount)/100)/100),2) * g.cur_conversion_rate as sgst,
	         0 as c_cess,
	         0 as nc_cess,
	         '' as ITC
	         FROM grn g
	         left join grn_material gm on g.grn_no = gm.grnNumber
	         and g.enterprise_id = gm.enterprise_id
	         left join grn_material_tax gmt on gm.grnNumber=gmt.grn_no
	         and gm.enterprise_id = gmt.enterprise_id
	         and gm.item_id = gmt.item_id
	         and gm.make_id=gmt.make_id
	         left join tax t on t.code = gmt.tax_code
	         and gmt.enterprise_id = t.enterprise_id
	         LEFT JOIN party_master p ON p.party_id = g.party_id
	         AND p.enterprise_id = g.enterprise_id
	         LEFT JOIN enterprise e ON g.enterprise_id = e.id
	         LEFT JOIN gst_category gc ON gc.id = p.category_id
	         WHERE %s
	         group by gm.item_id,gm.make_id,g.grn_no having total_rate > 0)    
	         union(select g.grn_no,p.party_name,g.invno,left(g.inv_date,10) as inv_date,
	         g.net_inv_value * g.cur_conversion_rate AS net_inv_value,IF(p.state != '', p.state, p.gstno) AS place_of_supply,p.state as p_state,e.state as e_state,
	         '' as reverse_charge,
	         sum(if(t.type = 'CGST' or t.type = 'SGST' or t.type = 'IGST',round(t.net_rate,2),0)) as total_rate,
	         round((gm.dc_qty*gm.inv_rate)*((100-gm.discount)/100),2) * g.cur_conversion_rate as total_value,
	         round((sum(if(t.type='CGST',t.net_rate,0)))*((gm.dc_qty*gm.inv_rate)*
	         ((100-gm.discount)/100)/100),2) * g.cur_conversion_rate as cgst,
	         round((sum(if(t.type='IGST',t.net_rate,0)))*((gm.dc_qty*gm.inv_rate)*
	         ((100-gm.discount)/100)/100),2) * g.cur_conversion_rate as igst,
	         round((sum(if(t.type='SGST',t.net_rate,0)))*((gm.dc_qty*gm.inv_rate)*
	         ((100-gm.discount)/100)/100),2) * g.cur_conversion_rate as sgst,
	         round(g.net_inv_value-(g.net_inv_value*100/(sum(if(t.type = 'CESS' and t.is_compound=1,
	         round(t.net_rate,2),0))+100)),2) * g.cur_conversion_rate as c_cess,
	         round(((gm.dc_qty*gm.inv_rate)*((100-gm.discount)/100))*(sum(if(t.type = 'CESS' and t.is_compound=0,
	         round(t.net_rate,2),0)))/100,2) * g.cur_conversion_rate as nc_cess,
	         '' as ITC
	         FROM grn g
	         JOIN grn_material gm ON g.grn_no = gm.grnNumber
	         and g.enterprise_id = gm.enterprise_id
	         left join grn_tax gt on gm.grnNumber=gt.grn_no
	         and gm.enterprise_id = gt.enterprise_id
	         JOIN tax t ON gt.tax_code = t.code
	         AND gt.enterprise_id = t.enterprise_id
	         LEFT JOIN party_master p ON p.party_id = g.party_id
	         AND p.enterprise_id = g.enterprise_id
	         LEFT JOIN enterprise e ON g.enterprise_id = e.id
	         LEFT JOIN gst_category gc ON gc.id = p.category_id
	         WHERE %s
	         group by gm.make_id,gm.item_id,g.grn_no)) as gstr2_b2b group by grn_no,total_rate
	         order by invno, inv_date asc""" %  (conct, where, where)
		return query

	def cdnr_inner_query(self, where=None):
		"""

		:param where:
		:return:
		"""
		query = """SELECT
						grn_no,
						gstno,
						party_name,
						inv_no,
						inv_value,
						note_datetime,
						note_code,		
						SUM(taxable_value) AS taxable_value,	
						ROUND(rate, 2) AS rate,
						cgst_rate,
						SUM(cgst_value),
						sgst_rate,
						SUM(sgst_value),
						igst_rate,
						SUM(igst_value),    
						cess_value,
						status,
						state,
						enterprise_id,    
						is_compound, 
						SUM(taxable_value) + SUM(rate),
						(@cnt:=@cnt + 1) AS rowNumber,
						amount as total_amount,
						inv_date,
						place_of_supply,
						is_credit,
						gst_category						
					FROM
					((SELECT 	
						note.grn_no,
						prd.details as gstno,
						p.party_name,
						note.inv_no,
						note.inv_value * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS inv_value,
						IF(g.inv_date is not null, g.inv_date, note.note_datetime) as note_datetime,
						CONCAT(note.financial_year, '/IAN/', LPAD(note.note_no, 6, 0), IF(note.sub_number is not NULL, note.sub_number, '')) AS note_code,
						SUM(ROUND((cd.qty * cd.rate), 2)) * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS taxable_value,	
						IF(t.type = 'CGST' OR t.type = 'SGST' OR t.type = 'IGST', t.net_rate, 0) AS rate,
						IF(t.type = 'CGST' OR t.type = 'SGST' OR t.type = 'IGST', t.net_rate, 0) AS net_rate_total,
						IF(t.type = 'CGST', SUM(t.net_rate) / COUNT(t.type), NULL) AS cgst_rate,
						ROUND(IF((t.type = 'CGST'),((SUM(t.net_rate) / COUNT(t.type) * (cd.qty * cd.rate)) / 100), 0), 2) * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS `cgst_value`,    
						IF(t.type = 'SGST', SUM(t.net_rate) / COUNT(t.type), NULL) AS sgst_rate,
						ROUND(IF((t.type = 'SGST'),((SUM(t.net_rate) / COUNT(t.type) * (cd.qty * cd.rate)) / 100), 0), 2) * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS `sgst_value`,
						IF(t.type = 'IGST', SUM(t.net_rate) / COUNT(t.type), NULL) AS igst_rate,
						ROUND(IF((t.type = 'IGST'),((SUM(t.net_rate) / COUNT(t.type) * (cd.qty * cd.rate)) / 100), 0), 2) * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS `igst_value`,
						IF(t.type = 'CESS', SUM(t.net_rate) / COUNT(t.type), NULL) * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS cess_value,
						note.status,
						e.state,
						note.enterprise_id,    
						t.is_compound,
						note.amount * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) amount,
						note.inv_date,
						IF(p.state != '', p.state, p.gstno) AS place_of_supply,
						note.is_credit,
						p.category_id as gst_category   
					FROM
						crdrnote note 
						LEFT JOIN crdrnote_grn_map cgm ON cgm.crdrnote_id = note.grn_no
							AND cgm.enterprise_id = note.enterprise_id    	
						LEFT JOIN crdr_details cd ON cd.crdr_id = cgm.crdrnote_id
							AND cd.enterprise_id = cd.enterprise_id
						LEFT JOIN crdr_tax ct ON cgm.crdrnote_id = ct.crdr_id
							AND cgm.enterprise_id = ct.enterprise_id
						LEFT JOIN tax t ON ct.tax_code = t.code
							AND ct.enterprise_id = t.enterprise_id
						LEFT JOIN grn g ON g.grn_no = cgm.crdrnote_grn_no
							AND g.enterprise_id = cgm.enterprise_id
						LEFT JOIN party_master p ON p.party_id = note.party_id
							AND p.enterprise_id = note.enterprise_id
						LEFT JOIN party_registration_detail prd ON p.party_id = prd.party_id
							AND p.enterprise_id = prd.enterprise_id
							AND prd.label = 'GSTIN'
						LEFT JOIN enterprise e ON note.enterprise_id = e.id
						LEFT JOIN gst_category gc ON gc.id = p.category_id
					WHERE %s
					GROUP BY cd.crdr_id, t.net_rate
					HAVING net_rate_total > 0 OR cess_value > 0) UNION (
					SELECT 
						note.grn_no,
						prd.details as gstno,
						p.party_name,
						note.inv_no,
						note.inv_value * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS inv_value,
						IF(g.inv_date is not null, g.inv_date, note.note_datetime) as note_datetime,
						CONCAT(note.financial_year, '/IAN/', LPAD(note.note_no, 6, 0), IF(note.sub_number is not NULL, note.sub_number, '')) AS note_code,
						ROUND((cd.qty * cd.rate * IF(note.is_credit = cd.is_credit, 1, -1)), 2) * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS taxable_value,
						SUM(IF(t.type = 'CGST' OR t.type = 'SGST' OR t.type = 'IGST', t.net_rate, 0)) AS rate,
						SUM(IF(t.type = 'CGST' OR t.type = 'SGST' OR t.type = 'IGST', t.net_rate, 0)) AS net_rate_total,    						
						SUM(IF(t.type = 'CGST', t.net_rate, 0)) AS cgst_rate,
						SUM(ROUND(IF((t.type = 'CGST'),((t.net_rate * (cd.qty * cd.rate)) / 100), 0), 2)  * IF(note.is_credit = cd.is_credit, 1, -1)) * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS `cgst_value`,  
						SUM(IF(t.type = 'SGST', t.net_rate, 0)) AS sgst_rate,
						SUM(ROUND(IF((t.type = 'SGST'),((t.net_rate * (cd.qty * cd.rate)) / 100), 0), 2) * IF(note.is_credit = cd.is_credit, 1, -1)) * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS `sgst_value`,  
						SUM(IF(t.type = 'IGST', t.net_rate, 0)) AS igst_rate,
						SUM(ROUND(IF((t.type = 'IGST'),((t.net_rate * (cd.qty * cd.rate)) / 100), 0), 2) * IF(note.is_credit = cd.is_credit, 1, -1)) * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS `igst_value`,
						IF(t.type = 'CESS', SUM(t.net_rate) / COUNT(t.type), NULL) * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS cess_value,
						note.status,
						e.state,
						note.enterprise_id,    
						t.is_compound,
						note.amount * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS amount,
						note.inv_date,
						IF(p.state != '', p.state, p.gstno) AS place_of_supply,
						note.is_credit,
						p.category_id as gst_category  	
					FROM
						crdrnote note 
						LEFT JOIN crdrnote_grn_map cgm ON cgm.crdrnote_id = note.grn_no
							AND cgm.enterprise_id = note.enterprise_id    	
						LEFT JOIN crdr_details cd ON cd.crdr_id = cgm.crdrnote_id
							AND cd.enterprise_id = cd.enterprise_id
						LEFT JOIN crdr_details_tax cdt ON cd.crdr_id  = cdt.crdr_id
							AND cd.reason_id = cdt.reason_id
							AND cd.item_id = cdt.item_id
							AND cd.make_id = cdt.make_id
							AND cd.enterprise_id = cdt.enterprise_id
						LEFT JOIN tax t ON cdt.tax_code = t.code
							AND cdt.enterprise_id = t.enterprise_id
						LEFT JOIN grn g ON g.grn_no = cgm.crdrnote_grn_no
							AND g.enterprise_id = cgm.enterprise_id	
						LEFT JOIN party_master p ON p.party_id = note.party_id
							AND p.enterprise_id = note.enterprise_id
						LEFT JOIN party_registration_detail prd ON p.party_id = prd.party_id
							AND p.enterprise_id = prd.enterprise_id
							AND prd.label = 'GSTIN'
						LEFT JOIN enterprise e ON note.enterprise_id = e.id
						LEFT JOIN gst_category gc ON gc.id = p.category_id
					WHERE %s
					GROUP BY note.grn_no, cd.crdr_id, cd.reason_id, cd.item_id, cd.make_id, cd.rate HAVING net_rate_total > 0) UNION (
					SELECT 
							note.grn_no,
							prd.details as gstno,
							p.party_name,
							note.inv_no,
							note.inv_value * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS inv_value,
							note.note_datetime as note_datetime,
							CONCAT(note.financial_year, '/IAN/', LPAD(note.note_no, 6, 0), IF(note.sub_number is not NULL, note.sub_number, '')) AS note_code,              
							ROUND((cnm.qty * cnm.rate), 2) * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS taxable_value,  
							SUM(IF(t.type = 'CGST' OR t.type = 'SGST' OR t.type = 'IGST', t.net_rate, 0)) AS rate,
							SUM(IF(t.type = 'CGST' OR t.type = 'SGST' OR t.type = 'IGST', t.net_rate, 0)) AS net_rate_total,    
							SUM(IF(t.type = 'CGST', t.net_rate, 0))/(select count(*) from crdr_details_nonstock_material WHERE crdr_details_nonstock_material.grn_no = note.grn_no) AS cgst_rate,
							SUM(ROUND(IF((t.type = 'CGST'),((t.net_rate * (cnm.qty * cnm.rate)) / 100), 0), 2))/(select count(*) from crdr_details_nonstock_material WHERE crdr_details_nonstock_material.grn_no = note.grn_no) * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS `cgst_value`,  
							SUM(IF(t.type = 'SGST', t.net_rate, 0))/(select count(*) from crdr_details_nonstock_material WHERE crdr_details_nonstock_material.grn_no = note.grn_no) AS sgst_rate,
							SUM(ROUND(IF((t.type = 'SGST'),((t.net_rate * (cnm.qty * cnm.rate)) / 100), 0), 2))/(select count(*) from crdr_details_nonstock_material WHERE crdr_details_nonstock_material.grn_no = note.grn_no) * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS `sgst_value`,  
							SUM(IF(t.type = 'IGST', t.net_rate, 0))/(select count(*) from crdr_details_nonstock_material WHERE crdr_details_nonstock_material.grn_no = note.grn_no) AS igst_rate,
							SUM(ROUND(IF((t.type = 'IGST'),((t.net_rate * (cnm.qty * cnm.rate)) / 100), 0), 2))/(select count(*) from crdr_details_nonstock_material WHERE crdr_details_nonstock_material.grn_no = note.grn_no) * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS `igst_value`,
							IF(t.type = 'CESS', SUM(t.net_rate) / COUNT(t.type), NULL) * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS cess_value,
							note.status,
							e.state,
							note.enterprise_id,    
							t.is_compound,
							note.amount * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS amount,
							note.inv_date,
							IF(p.state != '', p.state, p.gstno) AS place_of_supply,
							note.is_credit,
							p.category_id as gst_category          
					FROM
							crdrnote note 		
							LEFT JOIN crdr_details_nonstock_material cnm ON cnm.grn_no = note.grn_no
									AND cnm.enterprise_id = note.enterprise_id
							LEFT JOIN crdr_details_nonstock_material_tax cnmt ON cnmt.grn_no  = cnm.grn_no
									AND cnmt.reason_id = cnm.reason_id				
									AND cnmt.enterprise_id = cnm.enterprise_id
							LEFT JOIN crdrnote_grn_map cgm ON cgm.crdrnote_id = note.grn_no
									AND cgm.enterprise_id = note.enterprise_id
							LEFT JOIN grn g ON g.grn_no =  cgm.crdrnote_grn_no
									AND g.enterprise_id = cgm.enterprise_id
							LEFT JOIN tax t ON cnmt.tax_code = t.code
									AND cnmt.enterprise_id = t.enterprise_id		
							LEFT JOIN party_master p ON p.party_id = note.party_id
									AND p.enterprise_id = note.enterprise_id
							LEFT JOIN party_registration_detail prd ON p.party_id = prd.party_id
									AND p.enterprise_id = prd.enterprise_id
									AND prd.label = 'GSTIN'
							LEFT JOIN enterprise e ON note.enterprise_id = e.id
							LEFT JOIN gst_category gc ON gc.id = p.category_id
					WHERE %s
					GROUP BY note.grn_no, cnm.description, cnm.reason_id, cnm.rate HAVING net_rate_total > 0
					) UNION (
					SELECT
						note.grn_no,
						prd.details as gstno,
						p.party_name,
						note.inv_no,
						note.inv_value * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS inv_value,
						IF(g.inv_date is not null, g.inv_date, note.note_datetime) as note_datetime,
						CONCAT(note.financial_year, '/IAN/', LPAD(note.note_no, 6, 0), IF(note.sub_number is not NULL, note.sub_number, '')) AS note_code,
						SUM(ROUND((cnm.qty * cnm.rate), 2)) * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS taxable_value,	
						IF(t.type = 'CGST' OR t.type = 'SGST' OR t.type = 'IGST', t.net_rate, 0) AS rate,
						IF(t.type = 'CGST' OR t.type = 'SGST' OR t.type = 'IGST', t.net_rate, 0) AS net_rate_total,
						IF(t.type = 'CGST', SUM(t.net_rate) / COUNT(t.type), NULL) AS cgst_rate,
						ROUND(IF((t.type = 'CGST'),((SUM(t.net_rate) / COUNT(t.type) * (cnm.qty * cnm.rate)) / 100), 0), 2) * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS `cgst_value`,    
						IF(t.type = 'SGST', SUM(t.net_rate) / COUNT(t.type), NULL) AS sgst_rate,
						ROUND(IF((t.type = 'SGST'),((SUM(t.net_rate) / COUNT(t.type) * (cnm.qty * cnm.rate)) / 100), 0), 2) * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS `sgst_value`,
						IF(t.type = 'IGST', SUM(t.net_rate) / COUNT(t.type), NULL) AS igst_rate,
						ROUND(IF((t.type = 'IGST'),((SUM(t.net_rate) / COUNT(t.type) * (cnm.qty * cnm.rate)) / 100), 0), 2) * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS `igst_value`,
						IF(t.type = 'CESS', SUM(t.net_rate) / COUNT(t.type), NULL) * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) AS cess_value,
						note.status,
						e.state,
						note.enterprise_id,    
						t.is_compound,
						note.amount * IF(note.currency_conversion_rate is NULL, 1, note.currency_conversion_rate) amount,
						note.inv_date,
						IF(p.state != '', p.state, p.gstno) AS place_of_supply,
						note.is_credit,
						p.category_id as gst_category 
					 FROM                     
						crdrnote note
							LEFT JOIN
						crdr_details_nonstock_material cnm ON cnm.grn_no = note.grn_no        							
							AND cnm.enterprise_id = note.enterprise_id
							LEFT JOIN 
						crdr_tax ct ON note.grn_no = ct.crdr_id
							AND note.enterprise_id = ct.enterprise_id
							LEFT JOIN
						crdrnote_grn_map cgm ON cgm.crdrnote_id = note.grn_no
							AND cgm.enterprise_id = note.enterprise_id
							LEFT JOIN
						grn g ON g.grn_no = cgm.crdrnote_grn_no
							AND g.enterprise_id = cgm.enterprise_id
							LEFT JOIN
						tax t ON ct.tax_code = t.code
							AND ct.enterprise_id = t.enterprise_id
							LEFT JOIN
						party_master p ON p.party_id = note.party_id
							AND p.enterprise_id = note.enterprise_id
							LEFT JOIN
						party_registration_detail prd ON p.party_id = prd.party_id
							AND p.enterprise_id = prd.enterprise_id
							AND prd.label = 'GSTIN'
							LEFT JOIN
						enterprise e ON note.enterprise_id = e.id
							LEFT JOIN
						gst_category gc ON gc.id = p.category_id
					WHERE %s
					GROUP BY note.grn_no, cnm.description, cnm.reason_id, cnm.rate
					HAVING net_rate_total > 0 OR cess_value > 0)) AS GSTR2
						CROSS JOIN
					(SELECT @cnt:=0) AS dummy
					GROUP BY rate , grn_no ORDER BY inv_no, grn_no ASC""" % (where, where, where, where)

		return query