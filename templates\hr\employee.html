{% extends "hr/sidebar.html" %}
{% block employeesorm %}
<style>
	li.employee_side_menu a{
	outline: none;
	background-color: #e6983c !important;
	}
	
	.dateselector-default .dateselector-day {
	width: 20% !important;
	border-radius: 4px 0 0 4px;
}
.dateselector-default .dateselector-month {
	width: 40% !important;
	border-radius: 0;
}
.dateselector-default .dateselector-year {
	width: 30% !important;
	border-radius: 0 4px 4px 0;
}

.dateselector-default .dateselector-day,
.dateselector-default .dateselector-month,
.dateselector-default .dateselector-year {
	font-size: 13px !important;
	height: 28px !important;
    margin-top: 8px !important;
	padding: 0 5px;
}

#importtable th {
	min-width: 120px;
}

.create_employee {
	border-radius: 50px 0 0 50px !important; 
    border-right: 1px solid #ccc;
}

.create_add_container {
	margin-right: 15px;
}

.create_add_container a.btn-new-item {
	box-shadow: -4px 4px 5px 0 rgba(60,64,67,0.302), 0 1px 3px 1px rgba(60,64,67,0.149);
}

.export_csv.btn-new-item  {
	border-radius: 0 50px 50px 0;
}

.btn-new-item-label {
    color: #004195;
    padding: 6px !important;
}

.create_employee:hover,
.export_csv:hover {
	background: rgba(32, 155, 225,0.1);
	box-shadow: -4px 4px 5px 0 rgba(60,64,67,0.302), 0 1px 3px 1px rgba(60,64,67,0.149) !important;
}

.import_employee.arrow_box:before {
    margin-left: 95px;
}

.import_employee.arrow_box:after {
    margin-left: 96px;
}

</style>

<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/funkyRadio.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/dateselector.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/plugin/image_cropper/css/imgareaselect-default.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/plugin/image_cropper/css/jquery.awesome-cropper.css?v={{ current_version }}">

<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/employee.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/plugin/image_cropper/js/jquery.imgareaselect.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/plugin/image_cropper/js/jquery.awesome-cropper.js?v={{ current_version }}"></script>
<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">Employees</span>
	</div>
	<div class="page-heading_new">
		{% if access_level.edit %}
			<div class="create_add_container">
				<div class="dropdown pull-right" data-tooltip="tooltip" title="Bulk upload via excel">
					<button class="btn btn-new-item pull-right export_csv dropdown-toggle" id="btn-import" type="button" data-toggle="dropdown" ><i class="fa fa-upload" aria-hidden="true"></i><span class="btn-new-upload-label"></span></button>
					<ul class="dropdown-menu arrow_box import_employee" style="margin-top: 14px; width: 200px;">
						<li role="button" data-tooltip="tooltip" title="Import Employee List" onclick="javascript:showImportEmployee();"/><a>Employee</a></li>
						<li role="button" data-tooltip="tooltip" title="Import Pay Structure" onclick="javascript:showImportPayStructure();"/><a>Pay Structure</a></li>
					</ul>
				</div>
				<a class="btn btn-new-item pull-right export_csv hide" onclick="javascript:showImportEmployee();" data-tooltip="tooltip" title="Import Employee List">
					<i class="fa fa-upload" aria-hidden="true"></i>
					<span class="btn-new-upload-label"></span>
				</a>
				<a data-toggle="tab" href="#tab2" class="btn btn-new-item pull-right create_employee" id="custom-tab2" data-tooltip="tooltip" title="Add New Employee">
					<i class="fa fa-plus" aria-hidden="true"></i>
					<span class="btn-new-item-label"></span>
				</a>
			</div>
		{% else %}
			<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in HR Module. Please contact the administrator.">
				<div class="dropdown pull-right">
					<button class="btn btn-new-item pull-right export_csv dropdown-toggle disabled" id="btn-import" type="button" style="pointer-events: none;"><i class="fa fa-upload" aria-hidden="true"></i><span class="btn-new-upload-label"></span></button>
					<ul class="dropdown-menu arrow_box import_employee" style="margin-top: 14px; width: 200px;">
						<li role="button" data-tooltip="tooltip" title="Import Employee List" onclick="javascript:showImportEmployee();"/><a>Employee</a></li>
						<li role="button" data-tooltip="tooltip" title="Import Pay Structure" onclick="javascript:showImportPayStructure();"/><a>Pay Structure</a></li>
					</ul>
				</div>
				<a class="btn btn-new-item pull-right export_csv hide disabled">
					<i class="fa fa-upload" aria-hidden="true"></i>
					<span class="btn-new-upload-label"></span>
				</a>
				<a class="btn btn-new-item pull-right create_employee disabled" id="custom-tab2">
					<i class="fa fa-plus" aria-hidden="true"></i>
					<span class="btn-new-item-label"></span>
				</a>
			</div>
		{% endif %}
		<a href="/erp/hr/employee/" class="btn btn-add-new pull-right view_employee hide" data-tooltip="tooltip" title="Back" style="margin-right: 15px; margin-bottom: 7px;"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
	</div>
	<div>
		<div class="col-lg-12">
			<div class="content_bg">
				<div class="contant-container-nav hide">
					<ul class="nav nav-tabs list-inline">
						<li class="active"><a href="/erp/hr/employee"id="employee_view">VIEW</a></li>
						<li><a data-toggle="tab" href="#tab2" id="employee_add">ADD</a></li>
					</ul>
				</div>
				<div class="tab-content">
					<div id="tab2" class="tab-pane fade">
						<div class="col-sm-12 add_table_content static_width_content">
							<form action="/erp/hr/employee/save/" method="post" id="addCustomer">{% csrf_token %}
								<input type="label" value="" class="txtbox10" id="idsupp"
									   disabled="disabled" maxlength="12" width="30%" hidden="hidden">
								<div class="accordion" id="accordion" >
									<div class="accordion-group">
										<div class="accordion-heading">
											<a class="accordion-toggle" aria-expanded="true" data-toggle="collapse" data-target="#collapseOne">
												General Info
											</a>
										</div>
										<div id="collapseOne" class="collapse in">
											<div class="accordion-inner" style="margin: 0;">
												<div class="row">
													<div class="col-sm-3 form-group">
														<label>Employee ID<span class="mandatory_mark"> *</span></label>
														{{ employeeForm.emp_id }}
														<!-- Render the field in non-editable for Edit and editable for Add -->
														{% if employeeForm.emp_code.value != None %}
														<input type="text" class="form-control" disabled
															   value="{{ employeeForm.emp_code.value }}" id="id_emp_code1" />
														<!-- Disabled fields are not read in POST, hence the hidden field -->
														<input type="text" value="{{ employeeForm.emp_code.value }}"
															   id="id_hidden_emp_code" name="emp_code" hidden="hidden" />
														{% else %}
														{{ employeeForm.emp_code }}
														{% endif %}
														{{ employeeForm.enterprise_id}}
													</div>
												</div>	
												<div class="row">
													<div class="col-sm-3 form-group">
														<label>First Name<span class="mandatory_mark"> *</span></label>
														<!-- Render the field in non-editable for Edit and editable for Add -->
														{% if employeeForm.emp_name.value != None %}
															<input type="text" class="form-control" disabled
																   value="{{ employeeForm.emp_name.value }}" id="id_emp_name1"
																   name="name_display"/>
															<!-- Disabled fields are not read in POST, hence the hidden field -->
															<input type="text" value="{{ employeeForm.emp_name.value }}"
																   id="id_hidden_emp_name" name="emp_name" hidden="hidden"/>
														{% else %}
															{{ employeeForm.first_name }}
														{% endif %}
													</div>
													<div class="col-sm-3 form-group">
														<label>Last Name</label>
														{{ employeeForm.last_name }}
													</div>
													<div class="col-sm-3 form-group">
														<label>Email</label>
														{{ employeeForm.email }}
													</div>
													<div class="col-sm-3 form-group">
														<label>Mobile Number<span class="mandatory_mark"> *</span></label>
														{{ employeeForm.phone_no }}
													</div>
													<div class="col-sm-3">
														<label>Employee Photo</label>
														<div class="material_txt">
															<div class="btn btn-default profile_image" role="button">Upload Image</div>
															{{ employeeForm.profile_pic }}{{ employeeForm.profile_pic_filename }}
															<div class="remove_image hide" role="button" style="color: red;">Remove</div>
															<div class="profile_image upload_camera hide">
																<i class="fa fa-camera fa-5" aria-hidden="true"></i>
															</div>
														</div>
													</div>
												</div>	
											</div>	
										</div>
									</div>
									<div class="accordion-group">
										<div class="accordion-heading">
											<a class="accordion-toggle" aria-expanded="false" data-toggle="collapse" data-target="#collapseTwo">
												Work
											</a>
										</div>
										<div id="collapseTwo" class=" collapse">
											<div class="accordion-inner row" style="margin: 0;">
												<div class="col-sm-3 form-group">
													<label>Department<span class="mandatory_mark"> *</span></label>
													{{ employeeForm.department_id }}
												</div>
												<div class="col-sm-3 form-group">
													<label>Employment Type<span class="mandatory_mark"> *</span></label>
													{{ employeeForm.employment_type}}

												</div>
												<div class="col-sm-3 form-group">
													<label>Employment Status<span class="mandatory_mark"> *</span></label>
													{{ employeeForm.status}}
												</div>
												<div class="col-sm-3 form-group" >
													<label>Date Of Joining</label>
													{{ employeeForm.date_of_joining}}
													<input type="text" class="form-control custom_datepicker from-beginning set-empty-date erasable-date" placeholder="Select Date" id="date_of_joining" readonly="readonly" autocomplete="off">
													<i class="glyphicon glyphicon-calendar custom-calender-icon"></i>
												</div>
												<div class="col-sm-3 form-group">
													<label>Designation</label>
													{{ employeeForm.designation}}
												</div>
												<div class="col-sm-3 form-group">
													<div class="col-sm-6 remove-padding">
														<label>Pay Structure<span class="mandatory_mark"> *</span></label>
														{{ employeeForm.pay_structure_id}}
													</div>
													<div class="col-sm-3 remove-right-padding">
														<input id="add_pay" class="btn btn-save btn-margin-1" value="Edit" onclick="editPayStructure()" type="button">
													</div>
													<div class="col-sm-3 remove-right-padding">
														<input id="show_pay" class="btn btn-save btn-margin-1" value="Show" onclick="showPayStructure()" type="button">
													</div>
												</div>
												
												<div class="col-sm-3 form-group">
													<label>Place of Work</label>
													{{ employeeForm.place_of_work}}
												</div>
												<div class="clearfix"></div>
												<div class="col-sm-3 form-group">
													<h4 style="margin-bottom: 0;">Earned Leave Details</h4>
												</div>
												<div class="col-sm-3 form-group">
													<h4 style="margin-bottom: 0;">Casual Leave Details</h4>
												</div>	
												<div class="col-sm-6 form-group"><h4 style="margin-bottom: 0;">&nbsp;</h4></div>
												<div class="col-sm-3 form-group">			
													<div Class="row">
														<div class="col-sm-12 form-group">
															<label>Days Eligible (per year)</label>
															{{ employeeForm.no_of_el}}
														</div>
													</div>	
												</div>
												<div class="col-sm-3 form-group">
													<div Class="row">
														<div class="col-sm-12 form-group">
															<label>Days Eligible (per year)</label>
															{{ employeeForm.no_of_cl}}
														</div>
													</div>	
												</div>
											</div>
										</div>
									</div>
									<div class="accordion-group">
										<div class="accordion-heading">
											<a class="accordion-toggle" aria-expanded="false" data-toggle="collapse" data-target="#collapseThree">
												Personal Details
											</a>
										</div>
										<div id="collapseThree" class=" collapse">
											<div class="accordion-inner row" style="margin: 0;">
												<div class="col-sm-3">
													<label>Date Of Birth</label>
													{{ employeeForm.date_of_birth }}
													<input type="text" class="form-control custom_datepicker from-beginning till-today set-empty-date erasable-date" placeholder="Select Date" id="emp_dob" readonly="readonly" autocomplete="off">
													<i class="glyphicon glyphicon-calendar custom-calender-icon"></i>
												</div>	
												<div class="col-sm-3 from-group">
													<label>Gender<span class="mandatory_mark"> *</span></label>
													<div> {{ employeeForm.gender }}</div>
												</div>

												<div class="col-sm-3 from-group">
													<label>Blood Group</label>
													<div> {{ employeeForm.blood_group }}</div>
												</div>

												
												<div class="col-sm-3 form-group">
													<label>Address1</label>
													{{ employeeForm.address1 }}
												</div>
												
												<div class="col-sm-3 form-group">
													<label>Address2</label>
													{{ employeeForm.address2 }}
												</div>
												<div class="col-sm-3 form-group">
													<label>Country</label>
													<span class="hide">
														{{ employeeForm.country }}
													</span>
													<select class="form-control" id="id_country-__prefix__-make_choices" onchange="updateCountryValue('onchange')">
														{% for country in country_list %}
															<option {% if country.country_code == employeeForm.country.value %} selected {% endif %} value="{{ country.country_code }}">{{ country.country_name }}</option>
														{% endfor %}
													</select>
												</div>
												<div class="col-sm-3 form-group">
													<label>State</label>
													<span class="address-container-state-text">
														{{ employeeForm.state }}
													</span>
													<span class="address-container-state-list">
														<select class="form-control" id="id_state-__prefix__-make_choices" onchange="updateStateValue();">
															{% for state in state_list %}
																<option {% if state.description == employeeForm.state.value %} selected {% endif %} value="{{ state.code}}">{{ state.description }}</option>
															{% endfor %}
														</select>
													</span>
												</div>
												<div class="col-sm-3 form-group">
													<label>City</label>
													{{ employeeForm.city }}
												</div>
												<div class="col-sm-3 form-group">
													<label>Postal Code</label>
													{{ employeeForm.postal_code }}
												</div>

												<div class="col-sm-3 form-group">
													<label>Father's Name</label>
													{{ employeeForm.father_name }}
												</div>
												<div class="col-sm-3 form-group">
													<label>Mother's Name</label>
													{{ employeeForm.mother_name }}
												</div>
												<div class="col-sm-3 form-group">
													<label>Emergency Contact Number</label>
													{{ employeeForm.contact_number }}
												</div>

												<div class="col-sm-3 form-group">
													<label>Nationality</label>
													{{ employeeForm.nationality }}
												</div>
												<div class="col-sm-3 form-group">
													<label>Marital Status<span class="mandatory_mark"> *</span></label>
													{{ employeeForm.martial_status }}
												</div>
												<div class="col-sm-3 from-group">
													<label>Spouse Name</label>
													<div> {{ employeeForm.spouse_name }}</div>
												</div>
												<div class="col-sm-3 form-group">
													<label>Aadhaar Number<span class="mandatory_mark"> *</span></label>
													{{ employeeForm.aadhar_number }}
												</div>
												<div class="col-sm-3 form-group">
													<label>ESI Number</label>
													{{ employeeForm.esi_no }}
												</div>
												<div class="col-sm-3 form-group">
													<label>PF Number</label>
													{{ employeeForm.pf_no }}
												</div>
											</div>
										</div>
									</div>
									<div class="accordion-group">
										<div class="accordion-heading">
											<a class="accordion-toggle" aria-expanded="false" data-toggle="collapse" data-target="#collapseFour" >
												Bank Details
											</a>
										</div>
										<div id="collapseFour" class=" collapse">
											<div class="accordion-inner row" style="margin: 0;">
												<div class="col-sm-3 form-group">
													<label>Account Number</label>
													{{ employeeForm.account_number }}
												</div>
												<div class="col-sm-3 form-group">
													<label>Account Type</label>
													{{ employeeForm.account_type }}
												</div>
												
												<div class="col-sm-3 form-group">
													<label>IFSC Code</label>
													{{ employeeForm.ifsc_code }}
												</div>
												<div class="col-sm-3 form-group">
													<label>Account Name</label>
													{{ employeeForm.account_name }}
												</div>
												<div class="col-sm-3 form-group">
													<label>PAN Number</label>
													{{ employeeForm.pan_number }}
												</div>
												<div class="col-sm-3 form-group">
													
												</div>
												<div hidden="hidden" class="col-sm-3 form-group" id="ifcs_bank_details"
												     style="margin-top: -12px;">
													<label>Bank Name: </label>
													<span id="ifcs_bank_name"></span>
												</div>
											</div>
										</div>
									</div>	
								</div>			
								
								<div class="col-sm-12 form-group row">
									{% if access_level.edit %}
									<a href="#" id="saveEmployee" class="btn btn-save">Save</a>
									{% endif %}
									<a href="/erp/hr/employee/" id="backEmployee" class="btn btn-cancel">Cancel</a>
								</div>

								<div id="employee_pay_structure_show_div" class="modal fade" role="dialog">
									<div class="modal-dialog modal-lg">
										<div class="modal-content">
											<div class="modal-header">
												<button type="button" class="close" data-dismiss="modal">&times;</button>
												<h4 class="modal-title">Pay Structure Details</h4>
											</div>
											<div class="view_table_content modal-body">
												<div class="table-responsive">
													<table class="table table-striped table-bordered custom-table without-table-fixed tableWithText" id ="pay_list_show" >
														<thead>
															<tr id="bill_header_show">
																<th> Type </th>
																<th> Name </th>
																<th style="width: 250px;"> Value </th>
															</tr>
														</thead>
														<tbody>

														</tbody>
														<tr class="table-total-col">
															<td colspan="2"></td>
															<td>
																<input id="total_amount_dis" disabled="disabled" class="form-control" value="0.00" style="text-align:right;" />
															</td>
														</tr>
													</table>
												</div>
												<div class="material_txt">
												</div>
											</div>
										</div>
									</div>
								</div>

								<div id="employee_pay_structure_div" class="modal fade" role="dialog">
									<div class="modal-dialog modal-lg">
										<div class="modal-content">
											<div class="modal-header">
												<button type="button" class="close" data-dismiss="modal">&times;</button>
												<h4 class="modal-title">Pay Structure Details</h4>
											</div>
											<div class="view_table_content modal-body">
												<div class="table-responsive">

													<input type="text" id="id_hidden_pay_structure_id" name="pay-structure_id" hidden="hidden" value ="{{ pay_structure_id }}"/>
													<table class="table table-striped table-bordered custom-table without-table-fixed tableWithText" id ="pay_list" >
														<thead>
															<tr id="bill_header">
																<th> Type </th>
																<th> Name </th>
																<th style="width: 250px;"> Value </th>
															</tr>
														</thead>
														<tbody>
															<div id="employee_pay_structure_formset">
																{{ employee_pay_structure_formset.management_form }}
																{% for payStructureForm in employee_pay_structure_formset.forms %}
																<tr id="{{ payStructureForm.prefix }}" align="center">
																	<td hidden="hidden">
																		{{ payStructureForm.DELETE }}
																		{{ payStructureForm.enterprise_id }}
																		{{ payStructureForm.type }}
																		{{ payStructureForm.description }}
																	</td>
																	<td><div>{{ payStructureForm.type.value }}</div></td>
																	<td><div>{{ payStructureForm.description.value }}</div></td>
																	<td>
																		<input type="text" id="id_{{ payStructureForm.prefix }}-amount" 
																		name="{{ payStructureForm.prefix }}-amount" 
																		class='form-control' maxlength="13" 
																		onfocus="setNumberRangeOnFocus(this,10,2)" onchange="Calculate();" 
																		value="{{ payStructureForm.amount.value }}" />
																	</td>
																</tr>
																{% endfor %}
															</div>
															<div id="employee_pay_structure_new_forms">
															</div>
															<div id="employee_pay_structure_empty_form">
																<tr bgcolor="#ECECEC" id="employee_pay_structure-__prefix__" name="employee_pay_structure_prefix" align="center" hidden="hidden">
																	<td hidden="hidden">
																		{{ employee_pay_structure_formset.empty_form.DELETE }}
																		{{ employee_pay_structure_formset.empty_form.enterprise_id }}
																	</td>
																	<td><div id="id-__prefix__-type">{{ employee_pay_structure_formset.empty_form.type }}</div></td>
																	<td>{{ employee_pay_structure_formset.empty_form.description }}</td>
																	<td>{{ employee_pay_structure_formset.empty_form.amount }}</td>
																</tr>
															</div>
															<tr class="table-total-col">
																<td colspan="2"></td>
																<td>
																	<input id="total_amount" disabled="disabled" class="form-control" value="0.00" style="text-align:right;" />
																</td>
															</tr>
														</tbody>
													</table>
												</div>
												<div class="material_txt">
												</div>
											</div>
										</div>
									</div>
								</div>
							</form>
						</div>
					</div>
					<div id="tab1" class="tab-pane fade in active">
						<div class="remove-right-padding">
							<div class="col-sm-12 table-with-upload-button">
								<div class="csv_export_button">
									<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#employeeList'), 'Empolyee_List.csv']);" data-tooltip="tooltip" title="Download Employee List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
								</div>
								<table class="table table-bordered custom-table table-striped" id="employeeList" style="width: 100%;">
									<thead>
									<tr>
										<th> Code</th>
										<th style="min-width: 300px;"> Name</th>
										<th> Department</th>
										<th> Designation</th>
										<th> Employment Type</th>
										<th> Joined</th>
										<th> Phone No</th>
										<th> Email</th>
									</tr>
									</thead>
									<tbody>
										{% for employee in employees %}
											<tr align="center">
												<td>
													<a role="button" onclick="editEmployeeRow('{{ employee.emp_code }}');">
														{{ employee.emp_code }}
													</a>
												</td>
												<td class='text-left'>
													<span style='width: calc(100% - 75px); display: inline-block;'>
														{{ employee.emp_name }}
													</span>
													<span style='width: 75px; display: inline-block; float: right; text-align: right;'>
														<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='bottom' title='Edit' 
				                                            onclick="editEmployeeRow('{{ employee.emp_code }}');">
				                                                <i class='fa fa-pencil'></i>
			                                            </span>
			                                            <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='bottom' title='Edit in New Tab' onclick="editEmployeeRow('{{ employee.emp_code }}', '_blank');" >
			                                                <i class='fa fa-external-link'></i>
			                                            </span>
			                                        </span>
												</td>
												<td class='text-left'>{{ employee.department_name }}</td>
												<td class='text-left'>{{ employee.designation }}</td>
												<td>{{ employee.employment_type }}</td>
												<td>{{ employee.date_of_joining|date:'M d, Y' }}</td>
												<td>{{ employee.phone_no }}</td>
												<td class='text-left'>{{ employee.email }}</td>
											</tr>
										{% endfor %}
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>	
</div>	


<div id="error_messages" width="50%" style="display:none" class="error_panel hide">
	<table class="error_display" style="margin:100px 0px 0px 470px;color:#FF0000; background:#00FFFF;" cellpadding="2"
	       cellspacing="2" border="0">
		<tr>
			<td style="align:center">
				<h4>Please correct the mistakes listed below to Save!!</h4>
				<div>
					{% if employeeForm.errors %}
					<h4>Errors in Employee Creation:</h4>
					{% endif %}
					{% for field in employeeForm.hidden_fields %}
					{% if field.errors %}
					<i>{{field.label_tag}}</i>:
					{% endif %}
					<ul>{% for error in field.errors %}
						<li style="list-style-type: none;">{{error}}</li>
					    {% endfor %}
					</ul>
					{% endfor %}
					{% for field in employeeForm.visible_fields %}
					{% if field.errors %}
					<i>{{field.label_tag}}</i>:
					{% endif %}
					<ul>{% for error in field.errors %}
						<li style="list-style-type: none;">{{error}}</li>
					    {% endfor %}
					</ul>
					{% endfor %}
				</div>
			</td>
		</tr>
		<tr>
			<td>
				<input type="text" value='{{ employeeForm.errors }}' id="form_errors" hidden="hidden"/>
				<input type="text" value='{{ alertresponse }}' id="form_alert" hidden="hidden"/>
			</td>
		</tr>
		<tr align="center">
			<td><a href="#" id="error_close" class="update1">Close</a></td>
		</tr>
	</table>
</div>
<br clear="all"/>
<!-- TODO  Add department dialog code by include statement to reuse further-->
<div id="modal_department_creation" class="modal fade" role="dialog" tabindex='-1'>
	<div class="modal-dialog" >
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">ADD DEPARTMENT</h4>
			</div>
			<div class="modal-body">
				<div id="department_creation" class="view_table_content material_txt ">
					<form id="department_form" method="POST" action="/erp/hr/department/save/">
						{% csrf_token %}
						<div class="row">
							<div class="form-group intent_for col-sm-12">
						        <label>Department Name<span class="mandatory_mark"> *</span></label>
								{{ departmentForm.enterprise_id }}
								{{ departmentForm.id }}
								{{ departmentForm.name }}
					        </div>
							<div class="form-group col-sm-12">
								<label>Department</label>
								{{ departmentForm.parent_id }}
							</div>
						</div>

						<div class="modal-footer">
							{% if access_level.edit %}
							<input type="button" class="btn btn-save" id="save_department_button" value="Add Department"/>
							<input type="submit" hidden="hidden" id="saveDepartment" value="Save"/>
							{% endif %}
							<input type="submit" class="btn btn-cancel" data-dismiss="modal" value="Close"/>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>

<div id="modal_importPaystructure" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Import Pay Structure</h4>
			</div>
			<div class="modal-body">
				<div id="importPaystructure">
					<table id="importPaystructureTable" class="table table-bordered table-striped custom-table custom-table-large">
						<thead>
						<tr align="center">
							<th>S. No</th>
							<th>Employee Code</th>
							<th>Employee Name</th>
							<th>Basic DA</th>
							<th>HRA</th>
							<th>Mediclaim</th>
							<th>PF</th>
							<th>...</th>
							<th>TDS</th>
						</tr>
						</thead>
						<tr>
							<td class="text-center">1</td>
							<td>ABC001</td>
							<td>Kumaresan</td>
							<td>30000</td>
							<td>3500</td>
							<td>500</td>
							<td>760</td>
							<td class="text-center">...</td>
							<td>250</td>
						</tr>
						<tr>
							<td style="font-size:10px">(Number)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Number)</td>
							<td style="font-size:10px">(Number)</td>
							<td style="font-size:10px">(Number)</td>
							<td style="font-size:10px">(Number)</td>
							<td style="font-size:10px" class="text-center">...</td>
							<td style="font-size:10px;">(Number)</td>
						</tr>
						<tr>
							<td colspan="7" style="border: none;"><b>Please select the file to import Pay Structure</b></td>
							<td colspan="2" style="border: none;" class="text-right"><a href="/erp/hr/json/download_pay_structure/" >Download Sample</a> </td>
						</tr>
						<tr class="tr-file-upload">
							<td colspan="9">
								<div class="row col-sm-12 form-group">
									<div class="col-sm-8">
										<label>Upload Pay Structure</label>
										<div class="material_txt">
											<form action="#" method="post"  enctype="multipart/form-data">
												<input type="file" id="paystrucutre_details_uploader" name="paystrucutre_details_uploader" accept=".xls, .xlsx"  class="filestyle" data-buttonBefore="true" data-buttonText= "Attachment">
												<input type="button" name="upload" id="pay_upload" hidden="hidden" value="Upload" />
												<input type="hidden" id="paystrucutre_details_base64" class="base64_details" />
											</form>
										</div>
									</div>
								</div>
							</td>
						</tr>
						<tr>
							<td colspan="9">
								<div class="material_txt">
									<input type="button" id="upload_paystrucute_report" class="btn btn-save" value="Upload">
									<a href="#" class="btn btn-cancel" id="cmdhideUploadPay" data-dismiss="modal" value="Add">Cancel</a>
								</div>
							</td>
						</tr>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>

<div id="modal_importEmployee" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Import Employee Details</h4>
			</div>
			<div class="modal-body">
				<div id="importEmployee" style="overflow-x: scroll;">
					<table id="importtable" class="table table-bordered table-striped custom-table custom-table-large">
						<thead>
						<tr align="center">
							<th>S. No</th>
							<th>* Emp. Code</th>
							<th>* First Name</th>
							<th>Last Name</th>
							<th>Email</th>
							<th>* Mobile Number</th>
							<th>* Department</th>
							<th>* Employment Type</th>
							<th>* Employment Status</th>
							<th>Date of Joining (dd-mm-yyyy)</th>
							<th>Designation</th>
							<th>Place of work</th>
							<th>EL: Days Eligible</th>
							<th>CL: Days Eligible</th>
							<th>Date of Birth (dd-mm-yyyy)</th>
							<th>* Gender</th>
							<th>Address 1</th>
							<th>Address 2</th>
							<th>Country</th>
							<th>State</th>
							<th>City</th>
							<th>Pin Code</th>
							<th>Father's Name</th>
							<th>Mother's Name</th>
							<th>Emergency Contact Number</th>
							<th>Nationality</th>
							<th>* Marital Status</th>
							<th>* Aadhaar Number</th>
							<th>ESI Number</th>
							<th>PF Number</th>
							<th>Account Number</th>
							<th>Account Type</th>
							<th>IFSC Code</th>
							<th>Account Name</th>
							<th>PAN Number</th>
							<th>* PAY STRUCTURE</th>
							<th>BLOOD GROUP</th>
							<th>SPOUSE NAME</th>
							
						</tr>
						</thead>
						<tr>
							<td class="text-center">1</td>
							<td>ABC001</td>
							<td>Kumaresan</td>
							<td>S</td>
							<td><EMAIL></td>
							<td>**********</td>
							<td>R & D SW</td>
							<td>Full-Time</td>
							<td>Active</td>
							<td>20-02-2006</td>
							<td>Software Developer</td>
							<td>Coimbatore</td>
							<td>12</td>
							<td>12</td>
							<td>30-05-1987</td>
							<td>Male</td>
							<td>PN Palayam</td>
							<td>Coimbatore</td>
							<td>India</td>
							<td>Tamil Nadu</td>
							<td>Coimbatore</td>
							<td>641020</td>
							<td>Sugunthan</td>
							<td>Malar</td>
							<td>**********</td>
							<td>Indian</td>
							<td>Married</td>
							<td>************</td>
							<td>1123654</td>
							<td>*************</td>
							<td>*************</td>
							<td>Salary</td>
							<td>ICICI000054</td>
							<td>ABC</td>
							<td>AF353DF</td>
							<td>Salaried</td>
							<td>B +ve</td>
							<td>Koperundhevi</td>
						</tr>
						<tr>
							<td style="font-size:10px">(Number)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Number)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Date)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Float)</td>
							<td style="font-size:10px">(Float)</td>
							<td style="font-size:10px">(Date)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Number)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Number)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Number)</td>
							<td style="font-size:10px">(Number)</td>
							<td style="font-size:10px">(Number)</td>
							<td style="font-size:10px">(Number)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Text)</td>
							<td style="font-size:10px">(Text)</td>
						</tr>
						<tr>
							<td colspan="5" style="border: none;"><b>Please select the file to import Employee Details</b></td>
							<td colspan="2" style="border: none;" class="text-right"><a href="/site_media/docs/Employee_Details_Sample.xlsx" >Download Sample</a> </td>
						</tr>
						<tr class="tr-file-upload">
							<td colspan="7">
								<div class="row col-sm-12 form-group">
									<div class="col-sm-8">
										<label>Upload Employee Details</label>
										<div class="material_txt">
											<form action="#" method="post"  enctype="multipart/form-data">
												<input type="file" id="employee_details_uploader" name="employee_details_uploader" accept=".xls, .xlsx"  class="filestyle" data-buttonBefore="true" data-buttonText= "Attachment">
												<input type="button" name="upload" id="emp_upload" hidden="hidden" value="Upload" />
												<input type="hidden" id="employee_details_base64" class="base64_details" />
											</form>
										</div>
									</div>
								</div>
							</td>
						</tr>
						<tr>
							<td colspan="7">
								<div class="material_txt">
									<input type="button" id="upload_employee_report" class="btn btn-save" value="Upload">
									<a href="#" class="btn btn-cancel" id="cmdhideUpload" data-dismiss="modal" value="Add">Cancel</a></div>
							</td>
						</tr>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="hide">
	<form id="employee_edit_form" action="/erp/hr/employee/edit/" method="post">
		{% csrf_token %}
		<input type="hidden" id="edit_employee_id" name="emp_code" value="" />
	</form>
</div>


<script src="/site_media/js/jquery.dateselector.js?v={{ current_version }}"></script>
<script language="javascript">
$(document).ready(function(){
		$.extend($.expr[":"], {
                "contains-ci": function(elem, i, match, array) {
                        return (elem.textContent || elem.innerText || $(elem).text() || "").toLowerCase().indexOf((match[3] || "").toLowerCase()) >= 0;
                }
        });

        if($('#form_errors').val() != ''){
            document.getElementById('error_messages').style.display = "block";
        }

        $('#error_close').click(function(){
            document.getElementById('error_messages').style.display = "none";
        });
        if($('#form_alert').val() != ''){
            alert($('#form_alert').val());
            $('#form_alert').val("");
        }
		$('.date_selector').dateSelector();
		$(".date_selector").each(function(){
			if($(this).val() !="") {
				var emp_date = $(this).val().split('/');
				$(this).dateSelector().dateSelector('setDate', new Date(emp_date[2], emp_date[1]-1, emp_date[0]));
			}
			else {
				$(this).dateSelector().dateSelector('setDate', new Date(1989, 00, 01));
				$(this).val('01/01/1989');
			}
		});		
		
		if(window.location.href.indexOf('edit') > 0){
			$("#employee_add").click();
		}
		if($("#id_country").val() == ""){
			$("#id_country-__prefix__-make_choices").val("IN")
		}
		updateCountryValue();
	});
	function deleteEmployee(cusCode){
		swal({
			title: "Are you sure?",
			text: "Do you want to delete Employee: " + cusCode,
			type: "warning",
			showCancelButton: true,
			confirmButtonColor: "#209be1",
			confirmButtonText: "Yes, delete it!",
			closeOnConfirm: true
		},
		function(){
			clickButton('deleteEmployee_'+cusCode);
		});
    }

</script>

<script type="text/javascript">
var myCalendar;
function doOnLoad() {
	myCalendar = new dhtmlXCalendarObject(["issue_date","calendar1","calendar2"]);
	myCalendar.setDateFormat("%d/%m/%Y");
}
</script>
<script src="/site_media/js/jquery.easyResponsiveTabs.js?v={{ current_version }}"></script>
<script type="text/javascript">
	$('#li_employee').addClass('active');

	$(document).ready(function () {
        $('#id_profile_pic').awesomeCropper(
        { debug: false }
        );
		$(".profile_image").click(function(){
			$("#id_profile_pic").next('.awesome-cropper').find('input').click();
		});
		SetProfileImage();
		var url = window.location.href; 
	  	if(url.indexOf('edit') < 0) {
			TableHeaderFixed();
	  	}
	  	else {
		  	$(".create_employee, .export_csv").addClass('hide');
			$(".view_employee").removeClass('hide');
			var emp_code = $("#id_emp_code1").val();
			$(".page_header").html('<a href="/erp/hr/employee/" >Employee</a> <i class="fa fa-angle-right" style="padding: 0 8px;" aria-hidden="true"></i> <span class="header_current_page"> '+ emp_code +' </span>');	
			if($("#employeeList").hasClass('dataTable')) {
				oTable.destroy();
			}
	  	}
		NavTableRemove();
    });
	
	$(".remove_image").click(function(){
		$(".cropped_image, #id_profile_pic").removeAttr('src');
		$("#browsed_image_file_name").val('');
		$("#id_profile_pic").val('');
		$('.btn.profile_image').removeClass('hide');
		$(this).addClass('hide');
	});
	
	setTimeout(function(){
		$( ".progress" ).next('.profile_image').hover(function() {
			var setWidth = $(this).width();
			setWidth = Number((setWidth/2)-16)+"px";
			var setHeight = $(this).height();
			setHeight = Number((setHeight/2)-16)+"px";
			$( ".progress" ).next('.profile_image').addClass('hover-bg');
			$( ".upload_camera").css({marginLeft: setWidth, marginTop: setHeight});
			$(".upload_camera").removeClass('hide');
		}, function() {
			$( ".progress" ).next('.profile_image').removeClass('hover-bg');
			$(".upload_camera").addClass('hide');
		});
		  
		$(".upload_camera").hover(function() {
			$( ".progress" ).next('.profile_image').addClass('hover-bg');
			$(".upload_camera").removeClass('hide');
		}, function() {
			$( ".progress" ).next('.profile_image').removeClass('hover-bg');
			$(".upload_camera").addClass('hide');
		});
	},100);


	function SetProfileImage(){
		var imageDataSrc = $("#id_profile_pic").val();
		if(imageDataSrc !="") {
			$(".cropped_image").attr('src',imageDataSrc);
			$('.btn.profile_image').addClass('hide');
			$(".remove_image").removeClass('hide');
		}
	}
	
	function showImportEmployee(){
		$("#modal_importEmployee").modal('show');
	}

	function showImportPayStructure(){
		$("#modal_importPaystructure").modal('show');
	}
	
	$("#employee_details_uploader, #paystrucutre_details_uploader").change(function(){
		var fileExtension = ['xls', 'xlsx'];
		var thisElement = $(this); 
		if ($.inArray(thisElement.val().split('.').pop().toLowerCase(), fileExtension) == -1) {
			swal("Invalid File Format", "Please upload only "+fileExtension.join(', ')+" file format.", "warning");
			setTimeout(function(){
				thisElement.val('').clone(true);
				thisElement.closest('form').find('.bootstrap-filestyle').find('input.form-control ').val('');
				$("#id_exp_item-__prefix__-document_data").val('');
			},200);
		}
		else {
			var file = thisElement[0].files[0];
			var reader = new FileReader();
			reader.readAsDataURL(file);
			reader.onload = function () {
				console.log(reader.result)
				thisElement.closest('form').find(".base64_details").val(reader.result)
				//$("#employee_details_base64").val(reader.result);
			};
			reader.onerror = function (error) {
				console.log('Error: ', error);
			};
		}
	});

$("#upload_employee_report").click(function(){
    if ($("#employee_details_uploader")[0].files.length == 0) {
		swal('', 'Please choose a file to upload.', 'error');
		return;
	}
	var filename = $("#employee_details_uploader")[0].files[0].name;
	var base64data = $("#employee_details_base64").val();
	if (base64data == "") {
		swal('', 'Please choose another file to upload.', 'error');
		return;
	}

	$.ajax({
        url: '/erp/hr/json/employee_import/',
        type: "POST",
        dataType: "json",
        data: {
            base64data: base64data,
            filename: filename
        },
        success: function (data) {
            $("#modal_importEmployee").modal('hide');
            $("#employee_details_base64").val("");
            if (data.response_message === "Success") {
				swal({
					title: "",
					text: data.custom_message,
					type: "success"
				},
				function() {
					window.location.reload();
				});
                ga('send', 'event', 'Employee', 'Import', $('#enterprise_label').val(), 10);
            } else {
				swal({
					title: "",
					text: data.custom_message,
					type: "error"
				},
				function() {
					window.location.reload();
				});
            }
            setTimeout(function(){
				$("#employee_details_uploader").val('').clone(true);
				$("#employee_details_uploader").closest('form').find('.bootstrap-filestyle').find('input.form-control ').val('');
				$("#id_exp_item-__prefix__-document_data").val('');
			},200);
        },
        error: function (xhr, errmsg, err) {
            console.log(err);
            console.log(errmsg);
            console.log(xhr.status + ": " + xhr.responseText);
            alert("Failed: " + errmsg);
        }
    });
});

$("#upload_paystrucute_report").click(function(){
    if ($("#paystrucutre_details_uploader")[0].files.length == 0) {
		swal('', 'Please choose a file to upload.', 'error');
		return;
	}
	var filename = $("#paystrucutre_details_uploader")[0].files[0].name;
	var base64data = $("#paystrucutre_details_base64").val();
	if (base64data == "") {
		swal('', 'Please choose another file to upload.', 'error');
		return;
	}

	$.ajax({
        url: '/erp/hr/json/employee/pay_import/',
        type: "POST",
        dataType: "json",
        data: {
            base64data: base64data,
            filename: filename
        },
        success: function (data) {
            $("#modal_importEmployee").modal('hide');
            $("#employee_details_base64").val("");
            if (data.response_code == 200) {
                if (data.response_message === "Success") {
					swal({
						title: "",
						text: data.custom_message,
						type: "success"
					  },
					  function(){
						window.location.reload();
					});
                    ga('send', 'event', 'Employee', 'Import', $('#enterprise_label').val(), 10);
                } else {
					swal({title: "", text: data.custom_message, type: "warning"}, function() {
						window.location.reload();
					});
                }
            } else {
                swal(
                    {title: "Import Failed!", text: data.response_message + "<BR/>" + data.custom_message, type: "error"},
                    function() {
						window.location.reload();
					}
				);
            }
            setTimeout(function(){
				$("#employee_details_uploader").val('').clone(true);
				$("#employee_details_uploader").closest('form').find('.bootstrap-filestyle').find('input.form-control ').val('');
				$("#id_exp_item-__prefix__-document_data").val('');
			},200);
        },
        error: function (xhr, errmsg, err) {
            console.log(err);
            console.log(errmsg);
            console.log(xhr.status + ": " + xhr.responseText);
            alert("Failed: " + errmsg);
        }
    });
});

var oTable;
var oSettings;
function TableHeaderFixed() {
   oTable = $('#employeeList').DataTable({
		fixedHeader: false,
        "scrollY": Number($(document).height() - 230),
        "scrollX": true,
		"pageLength": 50,
		"search": {
			"smart": false
		},
		"columns": [
			null,null,null,null,null,
			{ "type": "date" },
			null,null
		]
	});
	oTable.on("draw",function() {
		var keyword = $('#employeeList_filter > label:eq(0) > input').val();
		$('#employeeList').unmark();
		$('#employeeList').mark(keyword,{});
		setHeightForTable();
		listTableHoverIconsInit('employeeList');
	});
	oTable.on('page.dt', function() {
	  $('html, body').animate({
		scrollTop: $(".dataTables_wrapper").offset().top - 15
	   }, 'slow');
	  listTableHoverIconsInit('employeeList');
	});
	oSettings = oTable.settings();
	listTableHoverIconsInit('employeeList');
    $( window ).resize();
}

function NavTableRemove(){
	$('ul.nav-tabs li a').click(function() {
		if($(this).attr('id') != "employee_view") {
			if($("#employeeList").hasClass('dataTable')) {
				oTable.destroy();
			}
		}
	});
}	

$(".create_employee").click(function(){
	$(".page-title").text("New Employee")
	$(this).addClass('hide');
	$(".export_csv").addClass('hide');
	$(".view_employee").removeClass('hide');
	$(".page_header").html('<a href="/erp/hr/employee/" >Employee</a> <i class="fa fa-angle-right" style="padding: 0 8px;" aria-hidden="true"></i> <span class="header_current_page"> New </span>');	
	if($("#employeeList").hasClass('dataTable')) {
		oTable.destroy();
	}
});

function editEmployeeRow(employeeId,  openTarget="") {
	$("#edit_employee_id").val(employeeId);
	$("#employee_edit_form").attr("target", openTarget).submit();
}

function updateStateValue() {
	var selectedState = $("#id_state-__prefix__-make_choices").find("option:selected").text();
	$("#id_state").val(selectedState);
}

function updateCountryValue(loadtype='') {
	var selectedCountry = $("#id_country-__prefix__-make_choices").val();
	if(loadtype == "onchange") {
		$("#id_country").val(selectedCountry);
	}
	if(selectedCountry == "IN") {
		$(".address-container-state-text").addClass("hide");
		$(".address-container-state-list").removeClass("hide");
		$("#id_state").val($("#id_state-__prefix__-make_choices option:selected").text());
	}
	else {
		$(".address-container-state-text").removeClass("hide");
		$(".address-container-state-list").addClass("hide");
		if(loadtype == "onchange") {
			$("#id_state").val("");
		}
	}
}
</script>


{% endblock %}