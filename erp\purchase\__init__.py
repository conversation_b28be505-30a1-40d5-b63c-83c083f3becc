"""
"""
import logging

__author__ = 'ka<PERSON>vanan'

logger = logging.getLogger(__name__)

PO_STATUS_DRAFT = 0
PO_STATUS_REVIEWED = 1
PO_STATUS_APPROVED = 2
PO_STATUS_REJECTED = 3
PO_STATUS_COMPLETED = 4

ORDER_TYPE_DICT = {'po': 0, 'jo': 1, 'wo': 2}

class POStatus(object):
	REJECTED = "Rejected"
	PENDING = "Pending"
	PARTIAL = "Part Supplied"
	SUPPLIED = "Supplied"
	ON_TIME = "On Time"
	DELAYED = "Delayed"
	ON_TRACK = "On Track"
