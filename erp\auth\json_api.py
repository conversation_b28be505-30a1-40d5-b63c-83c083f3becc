"""
"""
import json
from datetime import datetime

import jwt
from dateutil.relativedelta import relativedelta
from django.http import HttpResponse

from erp.auth import logger, ENTERPRISE_ID_SESSION_KEY
from erp.auth.backend import LoginService
from erp.auth.request_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from settings import JWT_SECRET
from util.api_util import response_code

__author__ = 'nandha'


def reset_password(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		user_email = rh.getPostData('user_email')
		url_base = request.build_absolute_uri().split("/erp/")[0]
		response = LoginService().verify_and_set_password(url_base=url_base, user_email=user_email, reset_password=True)

	except Exception as e:
		logger.exception("Failed forgot password request! %s" % e.message)
		response = response_code.internalError()
		response['custom_message'] = "Could not reset password"
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def change_password(request):
	"""

	:param request:
	:return:
	"""
	login_service = LoginService()
	try:
		rh = RequestHandler(request)
		token = rh.getPostData('cp_token')
		if token not in ("", None):
			user_data = jwt.decode(token, JWT_SECRET)
			user_email = user_data['user_email']
			old_password = user_data['password']
			reset_on = datetime.strptime(user_data['reset_on'], "%Y-%m-%d %H:%M:%S")
			if datetime.today() >= reset_on + relativedelta(minutes=30):
				return HttpResponse(json.dumps(response_code.sessionTimeout()), 'content-type=text/json')
		else:
			user_email = rh.getPostData('user_email')
			old_password = rh.getPostData('old_password')
		new_password = rh.getPostData('new_password')
		message = ""
		if user_email is None:
			response = response_code.paramMissing()
		else:
			url_base = request.build_absolute_uri().split("/erp/")[0]
			result = login_service.changePassword(
				url_base=url_base, user_email=user_email, old_password=old_password, new_password=new_password)
			if result is True:
				response = response_code.success()
				message = "Password changed successfully!"
			elif result is False:
				response = response_code.failure()
				if token:
					message = "Token expired!"
				else:
					message = "Old password was wrong!"
				response['custom_message'] = message
			else:
				response = response_code.failure()
				message = "User is not registered!"
		response['custom_message'] = message

	except Exception as e:
		logger.exception("Failed forgot password request! %s" % e.message)
		response = response_code.internalError()
		response['custom_message'] = "Could not change password!"
	return HttpResponse(json.dumps(response), 'content-type=text/json')
