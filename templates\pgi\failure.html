{% extends "template.html" %}
{% block home %}
<style>
	.message {
	        margin-top: 100px;
	}

	.success-tick-mark .fa-frown-o {
		background: #ffdd00;
	    color: #fff;
	    font-size: 55px;
	    padding: 14px 20px;
	    border-radius: 60px;
	}

	.subscription_id {
		font-size: 18px;
	    background: #eee;
	    padding: 10px 40px;
	    border-radius: 50px;
	    margin-bottom: 30px;
	}

	.subscription_text {
		font-size: 16px;
	}

	.supporting_text {
		color: #666;
		font-size: 12px;
	}

	.failure-ref-value tr:nth-child(odd) {
		background: rgba(32, 155, 225, 0.1)
	}

	.failure-ref-value td {
		padding:  10px;
	}
</style>
<div style="display:none">
   	<input type="hidden" name="csrfmiddlewaretoken" value="{% csrf_token %}">
</div>
<div class="message">
	<div class="text-center success-tick-mark">
		<i class="fa fa-frown-o" aria-hidden="true"></i>
	</div>
	<h1 class="text-center" style="color: #e92829">Payment Failed!</h1>
	<div class="text-center">
		<span class="subscription_text">
			{{title}}
			<br />
			<br />
			<table class="failure-ref-value" style="width: 600px;margin: 0 auto; border:solid 1px rgba(32, 155, 225, 0.3); margin-bottom: 0;">
				<tr>
					<td class="text-center"><label>Transaction ID</label></td>
					<td class="text-left">{{transaction_id}}</td>
				</tr>
				<tr>
					<td class="text-center"><label>Transaction Time</label></td>
					<td class="text-left">{{datetime}}</td>
				</tr>
			</table>
			<br />
			If any amount is debited from you account, it will be refunded within 7 working days.
		</span>
	</div>
	<div class="text-center" style="margin-top: 30px;">
		<span class="supporting_text">
			If you have any questions or concerns regarding this transaction, please contact us at <b><EMAIL></b>
		</span><BR/><BR/>
	</div>
</div>
{% endblock %}