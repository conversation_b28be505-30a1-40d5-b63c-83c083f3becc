{% extends "stores/sidebar.html" %}
{% block stockreport %}
{% if access_level.view %}
    <link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
    <link type="text/css" rel="stylesheet" href="/site_media/css/chosen.css?v={{ current_version }}">
    <link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
    <script type="text/javascript" src="/site_media/js/bootstrap-datepicker.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/load_material.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/report.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/bootstrap-multiselect.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/chosen.jquery.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/jquery.easyResponsiveTabs.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/daterangepicker.js?v={{ current_version }}"></script>

    <style>
        li.stock_report_menu a {
            outline: none;
            background-color: #e6983c !important;
        }
        .dataTables_scrollBody {
            overflow: auto hidden !important;
        }

        .dataTables_scrollBody table{
            margin-bottom: 20px;
        }
    </style>
    <div class="right-content-container download-icon-onTop">
        <div class="page-title-container">
            <span class="page-title">GRN Report</span>
        </div>
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12">
                    <div class="content_bg">
                        <ul class="resp-tabs-list hor_1"></ul>
                        <div class="resp-tabs-container hor_1">
                            <div class="row">
                                <div class="add_table">
                                    <div class="col-lg-12 add_table_content">
                                        <div class="filter-components">
                                            <div class="filter-components-container">
                                                <div class="dropdown">
                                                    <button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
                                                        <i class="fa fa-filter"></i>
                                                    </button>
                                                    <span class="dropdown-menu arrow_box arrow_box_filter">
                                                        <div class="col-sm-12 form-group" >
                                                            <label>Date Range</label>
                                                            <div id="reportrange" class="report-range form-control">
                                                                <i class="glyphicon glyphicon-calendar"></i>&nbsp;
                                                                <span></span> <b class="caret"></b>
                                                                <input type="hidden" class="fromdate" id="fromdate"
                                                                       name="from_date" value="{{from_date}}"/>
                                                                <input type="hidden" class="todate" id="todate" name="to_date" value="{{to_date}}"/>
                                                            </div>
                                                        </div>
                                                        <div class="col-sm-12 multiselect_option" style="margin-bottom:20px">
                                                            <label>Supplier</label>
                                                            <select name="select" id="cmbsupplier" multiple="multiple">
                                                                {% for j in suppliers %}
                                                                    <option value="{{ j.id }}">{{ j.name }} ({{ j.code }})</option>
                                                                {% endfor %}
                                                            </select>
                                                        </div>
                                                        <div class="col-sm-12 form-group">
                                                            <label>Material Name</label>
                                                            <select class="form-control chosen-select" name="select"
                                                                    id="cmbmaterial">
                                                                <option value="">All</option>
                                                                {% for j in materials %}
                                                                    <option value="{{ j.item_id }}">{{ j.name }} - {{ j.drawing_no }}</option>
                                                                {% endfor %}
                                                            </select>
                                                        </div>
                                                        <div class="col-sm-12 form-group">
                                                            <div class="checkbox" style="padding-left: 22px !important;">
                                                                <input name="approved_only" id="id_approved_only" type="checkbox"/>
                                                                <label for="id_approved_only">Exclude Drafts</label>
                                                            </div>
                                                        </div>
                                                       <div class="filter-footer">
                                                            <input type="button" class="btn btn-save" value="Apply" id="grnreportview"/>
                                                        </div>
                                                    </span>
                                                </div>
                                                <span class='filtered-condition filtered-date'>Date: <b></b></span>
                                                <span class='filtered-condition filtered-party'>Supplier Name: <b></b></span>
                                                <span class='filtered-condition filtered-material'>Material Name: <b></b></span>
                                                <span class='filtered-condition filtered-draft'>Draft Excluded: <b></b></span>
                                            </div>
                                        </div>
										<div class="col-lg-12 search_result_table">
                                            <div class="csv_export_button">
                                                <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#tablesorter'), 'GRN_Report.csv']);" data-tooltip="tooltip" data-placement="bottom" title="Download&nbsp;GRN&nbsp;Report as&nbsp;CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
                                            </div>
											<table class="table table-bordered custom-table table-striped grn_tbl"
												   id="tablesorter">
												<thead class="th-vertical-center">
													<tr class="exclude_export">
														<th rowspan="2" style="min-width: 40px;">S. No</th>
														<th rowspan="2" style="min-width: 80px;">Grn No</th>
														<th rowspan="2" style="min-width: 80px;">Grn Date</th>
														<th rowspan="2" style="min-width: 160px;">Party Name</th>
														<th rowspan="2" style="min-width: 80px;">Party Inv No</th>
														<th rowspan="2" style="min-width: 80px;">Party Inv/DC Date</th>
														<th rowspan="2" style="min-width: 80px;">Project</th>
														<th rowspan="2" style="min-width: 160px;">Material</th>
                                                        <th rowspan="2" style="min-width: 160px;">Drawing NO</th>
														<th rowspan="2" style="min-width: 60px;">Unit</th>
														<th colspan="5" style="min-width: 10px;">Quantity</th>
														<th name="price_col" rowspan="2" style="min-width: 60px;">Price/ Unit</th>
														<th name="price_col" rowspan="2" style="min-width: 60px;">Disc. (%)</th>
														<th rowspan="2" style="min-width: 60px;">Total Price</th>
														<th colspan="2" style="min-width: 60px;">CGST</th>
														<th colspan="2" style="min-width: 60px;">SGST</th>
														<th colspan="2" style="min-width: 60px;">IGST</th>
													</tr>
													<tr class="exclude_export">
														<th style="font-size:10px !important;">DC/Invoice</th>
														<th style="font-size:10px !important;">Received</th>
														<th style="font-size:10px !important;">Shortage</th>
														<th style="font-size:10px !important;">Accepted</th>
														<th style="font-size:10px !important;">Rejected</th>
														<th style="font-size:10px !important;">Rate</th>
														<th style="font-size:10px !important;">Amount</th>
														<th style="font-size:10px !important;">Rate</th>
														<th style="font-size:10px !important;">Amount</th>
														<th style="font-size:10px !important;">Rate</th>
														<th style="font-size:10px !important;">Amount</th>
													</tr>
                                                    <!-- This <tr> is used for csv download. Don't delete -->
                                                    <tr>
                                                        <th hidden="hidden">S. No</th>
                                                        <th hidden="hidden">Grn No</th>
                                                        <th hidden="hidden">Grn Date</th>
                                                        <th hidden="hidden">Party Name</th>
                                                        <th hidden="hidden">Party Inv No</th>
	                                                    <th hidden="hidden">Party Inv/DC Date</th>
	                                                    <th hidden="hidden">Project</th>
                                                        <th hidden="hidden">Material</th>
                                                        <th hidden="hidden">Drawing No</th>
                                                        <th hidden="hidden">Unit</th>
                                                        <th hidden="hidden">Quantity - DC/Invoice</th>
                                                        <th hidden="hidden">Quantity - Received</th>
                                                        <th hidden="hidden">Quantity - Shortage</th>
                                                        <th hidden="hidden">Quantity - Accepted</th>
                                                        <th hidden="hidden">Quantity - Rejected</th>
                                                        <th hidden="hidden">Price/ Unit</th>
                                                        <th hidden="hidden">Disc. (%)</th>
                                                        <th hidden="hidden">Total Price</th>
                                                        <th hidden="hidden">CGST-Rate</th>
                                                        <th hidden="hidden">CGST-Amount</th>
                                                        <th hidden="hidden">SGST-Rate</th>
                                                        <th hidden="hidden">SGST-Amount</th>
                                                        <th hidden="hidden">IGST-Rate</th>
                                                        <th hidden="hidden">IGST-Amount</th>
                                                    </tr>
												</thead>
												<tbody id="grn_tbl_tbody">
												</tbody>
											</table>
										</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            {% block stock_statement %}
                            {% endblock %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        $(document).ready(function () {
            setTimeout(function () {
                $("#grnreportview").trigger('click');
            }, 5);
            $('#cmbsupplier').multiselect({
            includeSelectAllOption: true,
            onDropdownShown: function(event) {
                $('.multiselect-container.dropdown-menu').addClass('show');
                $('.multiselect-container.dropdown-menu.show li:nth-child(1)').each(function(){
                    $(this).addClass('li-select-all').find("label").append('<span class="checkmark"></span>');
                });
            },
            onDropdownHidden: function(event) {
                $('.multiselect-container.dropdown-menu').removeClass('show');
            }
        });

        });

        $(window).load(function(){
            updateFilterText();
        });

        function updateFilterText() {
            $(".filtered-date b").text($("#reportrange").find("span").text());
            const selectedOptions = $("#cmbsupplier option:selected");
            const selectedTexts = [];
            selectedOptions.each(function() {
                selectedTexts.push($(this).text());
            });
            $(".filtered-party b").text(selectedTexts.join(', '));
            $(".filtered-material b").text($("#cmbmaterial option:selected").text());
            $(".filtered-draft b").text($("#id_approved_only").is(":checked")?"Yes":"No");
        }

        var oTable;
        var oSettings;
        function TableHeaderFixed() {
			oTable = $('#tablesorter').DataTable({
				fixedHeader: false,
                "pageLength": 50,
                "scrollY": Number($(document).height() - 230),
                "scrollX": true,
				"search": {
					"smart": false
				},
                "orderCellsTop": true,
				"columns": [
					null,null,
					{ "type": "date" },
					null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null
					]
			});
			oTable.on("draw",function() {
				var keyword = $('#tablesorter_filter > label:eq(0) > input').val();
				$('#tablesorter').unmark();
				$('#tablesorter').mark(keyword,{});
			});
			oTable.on('page.dt', function() {
			  $('html, body').animate({
				scrollTop: $(".dataTables_wrapper").offset().top - 15
			   }, 'slow');
			});
            oSettings = oTable.settings();
            $( window ).resize();
        }

        function loopTblData(data){
            var row = '';
			$('#tablesorter').DataTable().clear();
			$('#tablesorter').DataTable().destroy();
            if(data.length != 0){
                $.each(data, function (key, value) {
				if(value.grn_date !="") {
					var setDateTime = value.grn_date.split(' ');
					var setdateFormat = setDateTime[0].split('-');
					setdateFormat = setdateFormat[1]+"/"+setdateFormat[2]+"/"+setdateFormat[0];
				}
				else {
					var setdateFormat="";
				}
				if(value.party_inv_date !="") {
					var setDateTime1 = value.party_inv_date.split(' ');
					var setdateFormat1 = setDateTime1[0].split('-');
					setdateFormat1 = setdateFormat1[1]+"/"+setdateFormat1[2]+"/"+setdateFormat1[0];
				}
				else {
					var setdateFormat1="";
				}
                row += "<tr bgcolor='#ececec' border='0' align='center' style='font-size:16px; font-weight:normal;'>" +
                    "<td>" + ++key + "</td>" +
                    "<td>" + value.grn_no + "</td>" +
                    "<td>" + moment(setdateFormat).format('MMM D, YYYY') + " "+ setDateTime[1] +"</td>" +
                    "<td>" + value.party_name + "</td>" +
                    "<td>" + value.party_inv_no + "</td>" +
                    "<td>" + moment(setdateFormat1).format('MMM D, YYYY') +"</td>" +
                    "<td>" + value.project + "</td>" +
                    "<td>" + value.grn_material + "</td>" +
                    "<td>" + value.grn_drawing_no + "</td>" +
                    "<td>" + value.unit + "</td>" +
                    "<td>" + value.dc_qty + "</td>" +
                    "<td>" + value.rec_qty + "</td>" +
                    "<td>" + value.short_qty + "</td>" +
                    "<td>" + value.inw_qty + "</td>" +
                    "<td>" + value.rej_qty + "</td>" +
                    "<td>" + value.rate + "</td>" +
                    "<td>" + value.disc + "</td>" +
                    "<td>" + value.gtotal + "</td>" +
                    "<td>" + (value.CGST_rate == undefined ? "0" : value.CGST_rate) + "</td>" +
                    "<td>" + (value.CGST == undefined ? "0" : value.CGST) + "</td>" +
                    "<td>" + (value.SGST_rate == undefined ? "0" : value.SGST_rate) + "</td>" +
                    "<td>" + (value.SGST == undefined ? "0" : value.SGST) + "</td>" +
                    "<td>" + (value.IGST_rate == undefined ? "0" : value.IGST_rate) + "</td>" +
                    "<td>" + (value.IGST == undefined ? "0" : value.IGST) + "</td>" +
                    "</tr>";
                });
            }else{
                row += "";
            }
            $('#grn_tbl_tbody').html(row);
			TableHeaderFixed();
            updateFilterText();
            closeFilterOption();
			$('#loading').hide();
        }

        $("#grnreportview").click(function () {
			$('#loading').show();
            from_date = $('.fromdate').val();
            to_date = $('.todate').val();
            supplier = JSON.stringify($('#cmbsupplier').val());
            material = $('#cmbmaterial').val();
            approved_only = $('#id_approved_only:checkbox:checked').length>0;
            $.ajax({
                    url : "/erp/stores/json/grnreport/",
                    type : "POST",
                    dataType: "json",
                    data : {
                        from_date: from_date,
                        to_date: to_date,
                        supplier: supplier,
                        material : material,
                        approved_only : approved_only
                    },
                    success : function(data) {
                        loopTblData(data)
                    },error : function(xhr,errmsg,err) {
                        console.log(xhr.status + ": " + xhr.responseText);
                    }
            });
        });
		
		$('.nav-pills li').removeClass('active');
		$("#li_grn_report").addClass('active');
    </script>
{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
{% endblock %}