.slimScrollDiv {
	width: 350px !important;
	float: left;
}

.nav_purchase_template li {
        padding: 8px 4px;
        border: solid 1px #ccc;
        border-left: none;
        font-size: 12px;
        transition: all 0.4s;
        cursor: pointer;
        width: 70px;
        float: left;
        margin-top: 0 !important;
        text-align: center;
        margin-bottom: -1px;
        background: rgba(0, 0, 0, 0.05);
}

.nav_purchase_template li:hover {
	background: rgba(0, 0, 0, 0.15);
}

.nav_purchase_template li.active {
	border-bottom: transparent;
	background: #FFF;
}

.po_template_editor {
        width: 351px !important;
        padding: 20px 12px 5px;
        float: left;
        border: 1px solid #ccc;
        min-height: 257px;
        margin-left: -1px;
        margin-bottom: 10px;
        margin-right: 0px;
        border-top:  transparent;
}

.po_pdf_editor {
        width: 210mm;
        padding: 2mm;
        float: left;
        min-height: 257px;
        margin-left: 3px;
        margin-bottom: 10px;
        box-shadow: 0 0 3px #000;
        border-radius: 3px;
        line-height: 1.2;
}

.input-group-prepend,
.input-group-append,
.input_spinner {
	float: left;
}

.input-group-prepend button {
	border-radius: 4px 0 0 4px;
	padding: 2px 3px 4px;
	max-height: 27px;
  background: #ddd;
}

.input-group-append button {
	border-radius: 0 4px 4px 0;
	padding: 2px 3px 4px;
	max-height: 27px;
  background: #ddd;
}

.form-control.input_spinner {
	width: 35px;
	box-shadow: 0 0;
	margin-bottom: 6px;
  padding: 0 !important;
}

.spinner-label {
	float: left;
	width: 60px;
	padding-top: 10px;
}

.document-margin .input-group {
	display: inline-block;
}

.document-margin .doc-margin-prefix {
        margin-bottom: 15px;
        display: inline-block;
        vertical-align: bottom;
}

.purchase_template_default .pdf_others_details {
	width: 85px;
	display: inline-table;
	word-break: break-word;
}

.pdf_enterprise_details {
	width: 65px;
	display: inline-block;
}

.slidecontainer {
        width: 50%;
}

.slider {
        -webkit-appearance: none;
        width: 100%;
        height: 14px;
        background: #d3d3d3;
        outline: none;
        opacity: 0.7;
        -webkit-transition: .2s;
        transition: opacity .2s;
}

.slider:hover {
        opacity: 1;
}

.slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 18px;
        height: 18px;
        background: #4CAF50;
        cursor: pointer;
}

.slider::-moz-range-thumb {
        width: 18px;
        height: 18px;
        background: #4CAF50;
        cursor: pointer;
}

.form-group-small {
	margin-bottom: 6px;
}

.enterprise_details label {
	width: 124px;
	margin-bottom: 5px;
	float: left;
	font-size: 10px;
}

.enterprise_total_details label {
	width: 180px;
	margin-bottom: 5px;
	float: left;
	font-size: 10px;
}

.enterprise_header_details label {
	width: 70px;
	margin-bottom: 5px;
	float: left;
	font-size: 10px;
    word-break: break-word;
}

.enterprise_form_details label {
	width: 168px;
	margin-bottom: 5px;
	float: left;
	font-size: 10px;
    word-break: break-word;
}

.po_template_editor .form-control {
        height: 27px;
        padding: 4px 8px 6px;
}

.form-alternate-text {
        float: left;
        width: 98px;
        margin-top: -8px;
        margin-left: -20px;
        font-size: 12px;
}

.input_width_spinner .input-group{
        max-width: 110px;
        margin-top: -8px;
        margin-left: 20px;
        display: inline-block;
}

.banner-container.input_width_spinner .input-group{
        max-width: 110px;
        margin-top: -8px;
        margin-left: 0px;
        display: inline-block;
}

.form-horizontal .input_sub_item {
	padding-left: 60px !important;
}

.form-horizontal .input_sub_item label {
	width: 98px;
}

.form-horizontal .radio label {
	width: 190px;
}

.font-size-container .input-group {
        position: absolute;
        margin-top: 26px;
        margin-left: -60px;
}

.enterprise_details_packing_details label {
	width: 148px;
}

.form-medium-text input[type='text']{
	width: 170px;
}

.select_template .selected_template .selection_template {
	display: block !important;
	cursor: pointer;
}

.select_template .selected_template {
	opacity: 0.8;
}

.select_template div img {
	transition: all 0.1s;
}

.select_template div:hover img {
	border:  solid 3px #008000 !important;
	transform: scale(1.01);
}



.item_table th,
.item_table td,
.hsn_table th,
.hsn_table td {
    vertical-align: middle !important;
    word-break: break-word;
    padding: 2px !important;
    border-color: transparent !important;
}

.table.row-seperator th,
.table.row-seperator td,
.table.row-seperator th,
.table.row-seperator td {
    border-top: 1px solid #666666 !important;
    border-bottom: 1px solid #666666 !important;
}

.table.column-seperator th,
.table.column-seperator td,
.table.column-seperator th,
.table.column-seperator td {
    border-right: 1px solid #666666 !important;
    border-left: 1px solid #666666 !important;
}

/*.item_table.row-seperator .row_seperator td,
.item_table.row-seperator .row_seperator th,
.hsn_table.row-seperator .row_seperator td,
.hsn_table.row-seperator .row_seperator th {
	border-bottom-color: transparent;
}

.item_table.row-seperator .row_seperator td:not(.hide):last-child,
.item_table.row-seperator .row_seperator th:not(.hide):last-child,
.hsn_table.row-seperator .row_seperator td:not(.hide):last-child,
.hsn_table.row-seperator .row_seperator th:not(.hide):last-child {
    border-bottom-color: #ccc;
}

.item_table.column-seperator .column_seperator td,
.item_table.column-seperator .column_seperator th,
.hsn_table.column-seperator .column_seperator td,
.hsn_table.column-seperator .column_seperator th {
	border-right-color: transparent;
}

.item_table.column-seperator tr.column_seperator td:not(.hide):last-child,
.item_table.column-seperator tr.column_seperator th:not(.hide):last-child,
.hsn_table.column-seperator tr.column_seperator td:not(.hide):last-child,
.hsn_table.column-seperator tr.column_seperator th:not(.hide):last-child {
	border-right-color: #ccc;
}*/

 .row_shading.shaded {
	background: #efefef;
}

.header_shading.shaded {
	background: #dedede;
}

.transport_details div {
	border: solid 1px #666666;
    border-right: none;
    border-top: none;
	margin-bottom: -1px;
	padding: 3px 6px;
	width: 50%;
	float: left;
	min-height: 35px;
}

.template_type_container {
	font-size: 12px;
	color: #209be1;
}

.template_type_container:hover {
	text-decoration: underline;
}

.purchase_template_default .bill_to_text,
.purchase_template_default .ship_to_text {
        border-bottom: 1px solid #666;
        display: inline-block;
        padding: 3px 0 1px 0;
        width: 100%;
        margin-bottom: 3px;
        text-decoration: none;
}

.selection_template {
        position: inherit;
        color: #fff;
        display: none;
        position: absolute;
        bottom: 15px;
        right: 30px;
        background: #008000;
        padding: 15px;
        border-radius: 50px;
        opacity: 0.8;
        display: none;
}



@font-face {
        font-family: 'Ubuntu';
        font-style: normal;
        font-weight: 400;
        src: url('https://www.xserp.in/site_media/fonts/Ubuntu-Regular.ttf') format('truetype');
}

@font-face {
        font-family: 'Comic Sans';
        font-style: normal;
        font-weight: 400;
        src: url('https://www.xserp.in/site_media/fonts/comicsansms3.ttf') format('truetype');
}

@font-face {
        font-family: 'DejaVu Sans';
        font-style: normal;
        font-weight: 400;
        src: url('https://www.xserp.in/site_media/fonts/DejaVuSans.ttf') format('truetype');
}

@font-face {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 400;
        src: url('https://www.xserp.in/site_media/fonts/open-sans-Regular.ttf') format('truetype');
}

@font-face {
        font-family: 'Verdana';
        font-style: normal;
        font-weight: 400;
        src: url('https://www.xserp.in/site_media/fonts/verdana.ttf') format('truetype');
}

.cke_textarea_inline {
        pointer-events: none;
        border: dashed 1px #999;
        border-radius: 3px;
        padding: 6px;
}

.cke_combopanel {
        z-index: 10100 !important;
}

.last-modified-user-container {
        display: block;
        font-size: 11px;
        margin-top: 5px;
        margin-bottom: -8px;
}

.cke_textarea_inline {
    pointer-events: none;
    border: dashed 1px #999;
    border-radius: 3px;
    padding: 6px;
}

.cke_combopanel {
  z-index: 10100 !important;
}

.last-modified-user-container {
    display: block;
    font-size: 11px;
    margin-top: 5px;
    margin-bottom: -8px;
}

.table>tbody>tr>td,
.table>tbody>tr>th,
.table>tfoot>tr>td,
.table>tfoot>tr>th,
.table>thead>tr>td,
.table>thead>tr>th {
    padding: 6px 2px !important;
    line-height: 1.42857143;
}

.preview_purchase_number_inline {
    float: right;
    margin-right: 15px;
    margin-top: -16px;
    color: #666;
}