(function($, window) {

    var vendors = ['webkit', 'moz'];
    for (var i = 0; i < vendors.length && !window.requestAnimationFrame; ++i) {
        var vp = vendors[i];
        window.requestAnimationFrame = window[vp+'RequestAnimationFrame'];
        window.cancelAnimationFrame = (window[vp+'CancelAnimationFrame'] || window[vp+'CancelRequestAnimationFrame']);
    }

    if (/iP(ad|hone|od).*OS 6/.test(window.navigator.userAgent) // iOS6 is buggy
        || !window.requestAnimationFrame || !window.cancelAnimationFrame) {
        var lastTime = 0;
        window.requestAnimationFrame = function(callback) {
            var now = Date.now();
            var nextTime = Math.max(lastTime + 16, now);
            return setTimeout(function() { callback(lastTime = nextTime); },
                nextTime - now);
        };
        window.cancelAnimationFrame = clearTimeout;
    }


    var ForITTheme = window.ForITTheme = window.ForITTheme || {};

    ForITTheme.Global = function(){};

    ForITTheme.Global.initSettings = function(){

        ForITTheme.Global.uAgent = navigator.userAgent;
        ForITTheme.Global.macOS = ForITTheme.Global.uAgent.match(/(iPad|iPhone|iPod|Macintosh)/g) ? true : false;
        ForITTheme.Global.mobileIE = ForITTheme.Global.uAgent.indexOf('IEMobile') !== -1;
        ForITTheme.Global.touch = ('ontouchstart' in window || 'onmsgesturechange' in window) ? true : false;

        ForITTheme.Global.isNewerIe = ForITTheme.Global.uAgent.match(/msie (9|([1-9][0-9]))/i),
        ForITTheme.Global.isOlderIe = ForITTheme.Global.uAgent.match(/msie/i) && !isNewerIe,
        ForITTheme.Global.isAncientIe = ForITTheme.Global.uAgent.match(/msie 6/i);
        ForITTheme.Global.isIe = ForITTheme.Global.isAncientIe || ForITTheme.Global.isOlderIe || ForITTheme.Global.isNewerIe;

        ForITTheme.Global.latestKnownScrollY = $('html').scrollTop() || $('body').scrollTop();

        ForITTheme.Global.setSizes();
    }

    ForITTheme.Global.setSizes = function() {
        ForITTheme.Global.documentHeight = $(document).height();
        ForITTheme.Global.windowWidth = $(window).width();
        ForITTheme.Global.windowHeight = $(window).height();
    }

    ForITTheme.Global.getUniqueId = function() {
        return parseInt(Math.random() * (9000 - 1000) + 1000) + '-' + parseInt(Math.random() * (9999 - 1000) + 1000);
    }

    ForITTheme.Global.fillTemplate = function( template, vars){
        $.each(vars, function(name, value){
            value = value || '';
            template = template.replace(new RegExp('{{'+ name + '}}', 'g'), value);
        });

        return template;
    }

    ForITTheme.Global.initSettings();

    $(window).on("scroll", function () {
        ForITTheme.Global.latestKnownScrollY = $('html').scrollTop() || $('body').scrollTop();
    });

    $(document).ready(function(){
        ForITTheme.Global.setSizes();
    });

    $(window).on('load', function(){
        ForITTheme.Global.setSizes();
    });

    $(window).on("resize", function () {
        ForITTheme.Global.setSizes();
    });
})(jQuery, window);



(function($, window){
    function hasClass(element, cls) {
        return document.querySelector(element).className.indexOf(' ' + cls + ' ') > -1;
    }

    var ForITTheme = window.ForITTheme || {},
        _global = ForITTheme.Global
    ForITTheme.Menu = function($elem){
        this.$elem = $elem,
            this.$asideDropdown = $(".dropdown__content",this.$elem),
            this.debounce = null;

        this.$elem.find(' .dropdown').mouseenter(function(){
            if (_global.windowWidth > 1024){
                $(this).children('.dropdown__menu,  .dropdown__content').addClass('active');
            }
        });
        this.$elem.find('.dropdown').mouseleave(function(){
            if (_global.windowWidth > 1024){
                $(this).children(' .dropdown__menu,  .dropdown__content').removeClass('active');
            }
        });
        
        this.$elem.find('.dropdown').click(function(){

            if (_global.windowWidth <= 1024 || hasClass('body > header > nav', '-mobile__menu') || hasClass('body > header > nav', '-aside__menu') ){

                $(this).siblings().children('.dropdown__menu, .dropdown__content').removeClass('active');
                $(this).siblings().children('.dropdown__content').slideUp('active');
                $(this).children('.dropdown__menu, .dropdown__content').toggleClass('active');
                $(this).children('.dropdown__content').slideToggle();
                event.stopPropagation();
            }
        });



        this.initScroll();
        this.bindFunction.apply(this);
        return this;
    };

    ForITTheme.Menu.prototype.initScroll = function() {
        var _self = this;

        $(window).on("scroll", function () {
            _self.check();
        });
    };
    ForITTheme.Menu.prototype.initScrollTo = function(){
        var _self = this;

        _self.$elem.find(' li > a:not(a.dropdown__menu)').each(function(){
            var $this = $(this),
                href = $this.attr('href').match(/#[^\/]*/),
                $row = false;

            if(href) {
                $this.parent().removeClass('active');
                href = href[0];
            }

            try {
                $row = $(href);
            } catch(error) {
                $row = false;
            }

            if($row && $row.length) {

                $this.click(function (event) {
                    event.preventDefault();
                    event.stopPropagation();
                    var top = $row.offset().top;

                    TweenMax.to($(window), .8, {
                        scrollTo: {
                            y: top
                        },
                        ease: Power1.easeOut
                    });
                    if(hasClass('body > header > nav', '-aside__menu')){
                        $('.open__menu__btn').trigger('click');
                    }
                });
            }

        });
    };

    ForITTheme.Menu.prototype.check = function() {
        if (ForITTheme.Global.windowWidth > 1024) {
            if (ForITTheme.Global.latestKnownScrollY > 10) {
                    $(".main__menu").addClass("main__menu--scroll");
            } else {
                $(".main__menu").removeClass("main__menu--scroll");
            }
        }
    };

    ForITTheme.Menu.prototype.bindFunction = function(){
        $( window ).load((function(){
            this.ckeckAsideDropDown();
        }).bind(this));
        $(window).on('resize',(function () {
            clearTimeout(this.debounce);
            this.debounce = setTimeout(this.ckeckAsideDropDown.bind(this), 100)
        }).bind(this));
    }

    ForITTheme.Menu.prototype.ckeckAsideDropDown = function() {
        this.$asideDropdown.each(function(num, item){
            var $item = $(item);
            var $parent  = $item.parent(0);

            $item.addClass('push__left');
            $parent.addClass('theme__dropdown__arrow--right');
            var offsetParentPanel = _global.windowWidth - $parent.offset().left - $parent.width();
            //var offsetAsidePanel = _global.windowWidth - $item.offset().left - $item.width();

            if(offsetParentPanel <  20 +  $item.width()) { // lol 20 is tolerance's threshold
                $item.removeClass('push__left');
                $item.addClass('push__right');
                $parent.removeClass('theme__dropdown__arrow--right');
                $parent.addClass('theme__dropdown__arrow--left');
            }else{
                $item.removeClass('push__right');
                $item.addClass('push__left');
                $parent.removeClass('theme__dropdown__arrow--left');
                $parent.addClass('theme__dropdown__arrow--right');
            }
            if(_global.windowWidth <= 1024){
                $item.removeClass('push__left');
                $item.removeClass('push__right');
                $parent.removeClass('theme__dropdown__arrow--right');
                $parent.removeClass('theme__dropdown__arrow--left');

            }
        });


    }


    $(".main__menu__navbar").each(function(){
        var instance = new ForITTheme.Menu($(this));

        //ForITTheme.Loader.done(function(){
        instance.initScrollTo();
        //});
    });





})(jQuery, window);


(function($, window){
    var ForITTheme = window.ForITTheme || {};

    ForITTheme.DataActions = function(arg) {
        var _self = this;
        if ( arg && arg.length > 0 )
            arg.forEach(function (currentValue) {
               if(currentValue.selector.length > 0) _self[currentValue.method].call(_self,currentValue.selector);
            });
        return this
    }
    ForITTheme.DataActions.prototype.dataToggleClass = function ($elements) {
            //var $elements = $('[data-toggle="class"]');

            $elements.click(function () {
                var $this = $(this),
                    toggleClass = $this.data('class') || 'active',
                    $target = $($this.data('target')),
                    targetClass = $this.data('target-class') || $target.data('class') || 'active';

                $this.toggleClass(toggleClass);
                $target.toggleClass(targetClass);
            });

            return $elements;
    }

    ForITTheme.DataActions.prototype.dataMoveNext = function ($elements) {
            //var $elements = $('[data-move="next"]');

            $elements.click(function () {
                var $this = $(this),
                    $moveTo = $this.parent().next();

                TweenMax.to($(window), .6, {
                    scrollTo: {
                        y: $moveTo.offset().top
                    },
                    ease: Power1.easeOut
                });
            });

            return $elements;
    }

    ForITTheme.DataActions.prototype.dataShare = function ($elements) {
            //var $elements = $('[data-share]');

            var share = {
                twitter: function() {
                    window.open('http://twitter.com/intent/tweet?text=' + jQuery("h2.text-title").text() + ' ' + window.location,
                        "twitterWindow",
                        "width=650,height=350");
                    return false;
                },

                // Facebook

                facebook: function(){
                    window.open('https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(location.href),
                        'facebookWindow',
                        'width=650,height=350');
                    return false;
                },

                // Pinterest

                pinterest: function(){
                    window.open('http://pinterest.com/pin/create/bookmarklet/?description=' + jQuery("h2.text-title").text() + ' ' + encodeURIComponent(location.href),
                        'pinterestWindow',
                        'width=750,height=430, resizable=1');
                    return false;
                },

                // Google Plus

                google: function(){
                    window.open('https://plus.google.com/share?url=' + encodeURIComponent(location.href),
                        'googleWindow',
                        'width=500,height=500');
                    return false;
                },

                // Linkedin

                linkedin: function(){
                    window.open('http://www.linkedin.com/shareArticle?mini=true&url=' + encodeURIComponent(location.href) + '&title=' + jQuery("h1").text(),
                        'linkedinWindow',
                        'width=650,height=450, resizable=1');
                    return false;
                }
            }

            $elements.click(function (event) {
                event.preventDefault();
                var $this = $(this);

                share[$this.data('share')].call();

            });

            return $elements;
    }
    $(document).ready(function(){
        new ForITTheme.DataActions([
            {
                'selector': $('[data-share]'),
                'method': 'dataShare'
            },
            {
                'selector': $('[data-move="next"]'),
                'method': 'dataMoveNext'
            },
            {
                'selector': $('[data-toggle="class"]'),
                'method': 'dataToggleClass'
            }
        ]);
    });

})(jQuery, window);

!function(a){"use strict";a("html, body");var e=a(".pwdMask > .form-control"),t=a(".pwd-toggle");a(".lnk-toggler").on("click",function(t){pageResolutoinSet();t.preventDefault();var e=a(this).data("panel");a(".xserp-panel.active").removeClass("active"),a(e).addClass("active")}),a(t).on("click",function(t){t.preventDefault(),a(this).toggleClass("fa-eye-slash fa-eye"),a(this).hasClass("fa-eye")?a(e).attr("type","text"):a(e).attr("type","password")}),a("#forget-lnk").on("click",function(){a(".xserp-login .nav-tabs").find("li").removeClass("active")}),a(window).on("load",function(){a(".square-block").fadeOut(),a("#preload-block").fadeOut("slow",function(){a(this).remove()})})}(jQuery);
setTimeout(function(){
    $("#id_user_email").focus();
    $("#id_password").focus().blur();
},500);
$(".lnk-toggler").on("click", function(){
    var current = $(this).data("panel");
    if(current == ".panel-signup") {
        // $(".xserp-login").css({height: "640px"}); //Temporarily Hidden form current Slider
    }
    else if(current == ".panel-login") {
        // $(".xserp-login").css({height: "575px"}); //Temporarily Hidden form current Slider
    }
    else if(current == ".panel-forgot") {
        // $(".xserp-login").css({height: "575"}); //Temporarily Hidden form current Slider
    }
    $(".fa.pwd-toggle").removeClass("fa-eye").addClass("fa-eye-slash");
    $("#id_password, #id_enterprise-password, #id_enterprise-confirm-password").attr("type", "password");
});