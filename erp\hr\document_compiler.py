"""
"""
import base64

import pdfkit
from django.template import Context
from django.template.loader import get_template

from erp import logger
from util.document_compiler import PDFGenerator
from util.helper import getAbsolutePath

__author__ = 'nandha'


class PaySlipGenerator(PDFGenerator):
	"""

	"""
	def __init__(self, double_copy=False, target_file_path=None):
		"""

		:param double_copy:
		:param target_file_path:
		"""
		super(PaySlipGenerator, self).__init__(target_file_path)
		self.double_copy = double_copy

	def generatePDF(self, source=None, filename=None):
		"""

		:param source:
		:param filename:
		:return:
		"""
		try:
			attendance_log = source[0]
			salary = source[1]
			earnings = []
			deductions = []
			month = attendance_log.since.strftime('%b, %Y')
			if attendance_log.enterprise.images and attendance_log.enterprise.images.logo and attendance_log.enterprise.images.logo != "":
				logo = "data:image/%s;base64,%s" % (attendance_log.enterprise.images.ext, base64.encodestring(attendance_log.enterprise.images.logo))
			else:
				logo = ""

			if attendance_log.enterprise.address_2 and attendance_log.enterprise.address_2 != "":
				address = attendance_log.enterprise.address_1 + ", " + attendance_log.enterprise.address_2
			else:
				address = attendance_log.enterprise.address_1

			enterprise_address = "{address}, {city}, {pin_code} {state}".format(
				address=address, city=attendance_log.enterprise.city, pin_code=attendance_log.enterprise.pin_code,
				state=attendance_log.enterprise.state)

			total_fixed, total_earnings, total_deduction, previous_balance = 0, 0, 0, 0
			others_fixed, others_earned = 0, 0
			# Add basic records first and calculate total
			for item in salary.description["EARNING"]:
				total_fixed += item["FIXED"]
				total_earnings += item["EARNED"]
				description = item["DESCRIPTION"].lower()
				if 'basic' in description or 'hra' in description:
					earnings.append({
						"description": item["DESCRIPTION"], "fixed": "%.2f" % item["FIXED"], "earned": "%.2f" % item["EARNED"]})

			# Add non basic records at last
			for item in salary.description["EARNING"]:
				description = item["DESCRIPTION"].lower()
				if 'basic' not in description and 'hra' not in description:
					if len(earnings) < 4:
						earnings.append({
							"description": item["DESCRIPTION"], "fixed": "%.2f" % item["FIXED"], "earned": "%.2f" % item["EARNED"]})
					else:
						others_fixed += item["FIXED"]
						others_earned += item["EARNED"]

			if others_fixed > 0 or others_earned > 0:
				earnings.append({
					"description": "Other Allowances", "fixed": "%.2f" % others_fixed, "earned": "%.2f" % others_earned})

			earning_summary = {
				"total_fixed": "%.2f" % total_fixed, "total_earnings": "%.2f" % total_earnings,
				"net_pay": "%.2f" % salary.getNetPay()}

			total = 0
			others = 0
			for item in salary.description["DEDUCTION"]:
				value = item["VALUE"]
				total += value
				if item["DESCRIPTION"] not in ("PF", "ESI", "TDS"):
					others += value
					continue
				if value != 0:
					deductions.append({"description": item["DESCRIPTION"], "amount": "%.2f" % value})
			deductions.append({"description": "Others", "amount": "%.2f" % others})
			deductions_summary = {
				"total_deduction": "%.2f" % total, "previous_balance": "%.2f" % salary.description["PREVIOUS BALANCE"],
				"unpaid_changes": "%.2f" % salary.description["UNPAID CHANGES"]}
			context = Context({
				'form_name': 'Pay Slip', 'enterprise_logo': logo, 'month': month, 'enterprise': attendance_log.enterprise,
				'enterprise_address': enterprise_address, 'employee_details': attendance_log.employee,
				'attendance_log': attendance_log, 'earnings': earnings, 'earning_summary': earning_summary,
				'deductions': deductions, 'deductions_summary': deductions_summary})

			options = {
				'page-size': 'A4', 'orientation': 'Portrait', 'margin-top': '5', 'margin-right': '5', 'margin-bottom': '10',
				'margin-left': '5', 'title': filename}

			css = [
				getAbsolutePath('/site_media/css/bootstrap.css'), getAbsolutePath('/site_media/css/invoice_template_preview.css'),
				getAbsolutePath('/site_media/css/invoice_template.css')]

			template_src = getAbsolutePath('/templates/hr/payslip_print.html')

			template = get_template(template_src).render(context)
			data = pdfkit.from_string(template, False, options=options, css=css)
			return data
		except Exception as e:
			logger.exception('Something went wrong while creating PDF self.canvas - %s' % e)
