<html lang="en">
<head>
	<title>xserp</title>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap.css?v={{ current_version }}">
	<link type="text/css" rel="stylesheet" href="/site_media/css/erp_default.css?v={{ current_version }}">
	<link type="text/css" rel="stylesheet" href="/site_media/css/roboto-font.css?v={{ current_version }}">
  	<link rel="stylesheet" type="text/css" href="/site_media/css/login-menu.css?v={{ current_version }}" >
	<link type="image/png" rel="icon" href="/site_media/images/xs-logo-with-border.png">
	<style>
		.theme__section__404 {
		    min-height: 100vh;
		    display: flex;
		    justify-content: center;
		    align-items: center;
		    padding: 120px 0 60px;
		}
		.theme__section>*:not(.vc_video-bg) {
		    position: relative;
		    z-index: 3;
		}
		.theme__section__404 .pic__404 img {
		    width: 40%;
		}

		h2 {
		    font-size: 30px;
		    letter-spacing: 2px;
		    font-weight:  300;
		}

		.space__offset__md__25 {
		    height: 25px;
		    clear: both;
		}
		.btn-white {
		    color: #4a90e2;
		    background-color: #fff;
		    border-color: #fff;
		}
		.btn-white {
		    color: #4a90e2;
		    background-color: #fff;
		    border-color: #fff;
		}
		.btn-white:hover {
		    box-shadow: 0px 3px 12px rgba(0,0,0,0.5);
		    color: #4a90e2;
		    background-color: #fff;
		    border-color: #fff;
		}
		.btn {
		    display: inline-block;
		    padding: 0 30px;
		    font-size: 18px;
		    font-style: normal;
		    line-height: 48px;
		    text-transform: uppercase;
		    font-weight: 500;
		    border: 1px solid #fff;
		    border-radius: 70px !important;
		    outline: none !important;
		    background-color: #fff;
		    vertical-align: middle;
		    -webkit-transition: 0.5s;
		    -moz-transition: 0.5s;
		    transition: 0.5s;
		}
		.theme__section .theme__section__overlay.-overlay_theme {
		    background-color: #4a90e2;
		}
		.theme__section .theme__section__overlay.-overlay_theme {
		    background-color: #4a90e2;
		    z-index: 2;
		}
		.theme__section .theme__section__overlay {
		    position: absolute;
		    left: 0;
		    top: 0;
		    z-index: 0;
		    width: 100%;
		    height: 100%;
		    padding: 0;
		    overflow: hidden;
		}

		.text-white {
		    color: #fff !important;
		}
	</style>
</head>
<body style="background: #4a90e2;">
	<nav class="main__menu -normal__menu main__menu--boxed" style="background: #4a90e2;">
	    <div class="main__menu__inner__wrap">
	        <a class="main__menu__logo" href="/erp/login/">
	            <img alt="xserp" src="/site_media/images/logo-xserp-with-x.png">
	            <img alt="XS ERP" class="alt__logo ls-is-cached lazyloaded" src="/site_media/images/logo-xserp-with-x.png">
	        </a>
	    </div>
	</nav>
	<div class="main__theme__wrap">
	    <section class="text-center theme__section theme__section__404">
	        <div class="space__offset__sm__90 space__offset__md__180"></div>
	        <div class="container">
	            <div class="row">
	                <div class="text-white">
	                    <div class="pic__404">
	                        <img src="http://dev.permisso.in:8443/wp-content/themes/forit/images/forit-404.png" alt="404">
	                    </div>
	                    <div class="theme-section-heading text-white">
	                        <h2> There are no App, SaaS and Product. </h2>
	                    </div>
	                    <div class="space__offset__xs__15 space__offset__md__25"></div>
	                </div>
	            </div>
	            <div class="row">
	                <div class="col-sm-4"></div>
	                <div class="col-sm-4">
	                    <a href="" onclick="history.back();return false;" class="btn btn-white btn-block">Go Back</a>
	                </div>
	                <div class="col-sm-4"></div>
	            </div>
	        </div>
	        <div class="theme__section__overlay -overlay_theme - "></div>
	    </section>
	</div>
</body>
</html>