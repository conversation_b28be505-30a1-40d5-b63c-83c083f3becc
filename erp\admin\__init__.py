"""
"""
import logging

__author__ = 'kalaivanan'

logger = logging.getLogger(__name__)

enterprise_mandatory_fields = ['Company Name', 'Address', 'City', 'State', 'Pin code', 'Phone']
enterprise_module_settings = {
	'indent_flag': 1, 'icd_flag': 2, 'icd_ignore_credit_note': 4, 'icd_auto_gen_voucher': 8, 'scrutiny_flag': 16}
gate_inward_no_settings = {'mandatory': 1, 'unique_in_fiscal_yr': 2, 'automated': 4, 'editable': 8}
account_group_map = {
	"Direct Expenses": "Direct Expenses", "Indirect Expenses": "Indirect Expenses",
	"Direct Incomes": "Direct Income", "Indirect Incomes": "Indirect Incomes",
	"Purchase Accounts": "Purchase Account", "Sales Accounts": "Sales Account",
	"Capital Account": "Capital Account", "Current Assets": "Current Assets",
	"Current Liabilities": "Current Liabilities", "Reserves & Surplus": "Reserves and Surpluses",
	"Reserves   Surplus": "Reserves and Surpluses",
	"Cash-in-hand": "Cash in Hand", "Loans & Advances (Asset)": "Short-Term Loans and Advances",
	"Loans  Advances (Asset)": "Short-Term Loans and Advances",
	"Sundry Debtors": "Sundry Debtors", "Sundry Creditors": "Sundry Creditors",
	"Duties & Taxes": "Duties and Taxes", "Duties  Taxes": "Duties and Taxes", "Provisions": "Short-Term Provisions",
	"Loans (Liability)": "Short-Term Borrowings", "Fixed Assets": "Fixed Assets - Tangible",
	"Investments": "Current Investments", "Bank Accounts": "Bank Account",
	"Deposits (Asset)": "Current Assets", "Secured Loans": "Short-Term Borrowings",
	"Unsecured Loans": "Long-term Borrowings", "Stock-in-hand": "Current Assets",
	"Bank OD A/c": "Short-Term Borrowings", "Misc. Expenses (ASSET)": "Current Assets",
	"Branch / Divisions": "Current Assets", "Profit And Loss": "Profit And Loss",
	"Balance Sheet": "Balance Sheet", "Non-Current Liabilities": "Non-Current Liabilities",
	"Non-Current Assets": "Non-Current Assets", "Tax Expenses": "Tax Expenses",
	"Exceptional Items": "Exceptional Items", "Extra-ordinary Items": "Extra-ordinary Items",
	"Profits from Discontinuing Operations": "Profits from Discontinuing Operations",
	"Inventories": "Inventories",
	"Tax Expenses from Discontinuing Operations": "Tax Expenses from Discontinuing Operations",
	"Employee Benefits Expenses": "Employee Benefits Expenses", "Finance Costs": "Finance Costs",
	"Depreciation and Amortization Expenses": "Depreciation and Amortization Expenses",
	"Other Direct Expenses": "Other Direct Expenses", "Current Tax": "Current Tax",
	"Deferred Tax": "Deferred Tax", "Other Taxes and Provisions": "Other Taxes and Provisions",
	"Share Capital": "Share Capital", "Long - term Borrowings": "Long - term Borrowings",
	"Other Long - term Liabilities": "Other Long - term Liabilities",
	"Long - term Provisions": "Long - term Provisions",
	"Other non - current Liabilities": "Other non - current Liabilities",
	"Other Current Liabilities": "Other Current Liabilities",
	"Fixed Assets - Intangible": "Fixed Assets - Intangible",
	"Non - Current Investments": "Non - Current Investments",
	"Long - Term Loans and Advances": "Long - Term Loans and Advances",
	"Other Non - Current Assets": "Other Non - Current Assets",
	"Cash Equivalents": "Cash Equivalents",
	"Other Current Assets": "Other Current Assets",
	"Other Revenue Income": "Other Revenue Income"
}
account_ledger_map = {
	"Profit & Loss A/c": "Profit and Loss Account", "Profit  Loss A/c": "Profit and Loss Account",
	"Cash": "Cash In Hand"
}

default_voucher_type = {
	"Attendance": "Attendance", "Contra": "Contra",
	"Credit Note": "Credit Note", "Debit Note": "Debit Note",
	"Delivery Note": "Delivery Note", "Journal": "Journal",
	"Memorandum": "Memorandum", "Payment": "Payment",
	"Payroll": "Payroll", "Physical Stock": "Physical Stock",
	"Purchase": "Purchase", "Purchase Order": "Purchase Order",
	"Receipt": "Receipt", "Receipt Note": "Receipt Note",
	"Rejections In": "Rejections In", "Rejections Out": "Rejections Out",
	"Reversing Journal": "Reversing Journal", "Sales": "Sales",
	"Sales Order": "Sales Order", "Stock Journal": "Stock Journal",
	"Job Work In Order": "Job Work In Order", "Job Work Out Order": "Job Work Out Order",
	"Material In": "Material In", "Material Out": "Material Out",
}

accounts_voucher_type_dict = {
	"Sales", "Purchase", "Payment", "Receipt", "Contra", "Debit Note", "Credit Note", "Journal", "Reversing Journal"}

ledger_voucher_type_dict = {"Sales", "Purchase", "Payment", "Receipt"}

tax_code_dict = {
	"2.50": ["CGST2_5", "SGST2_5"], "5": ["IGST5"], "6": ["CGST6", "SGST6"], "9": ["CGST9", "SGST9"],
	"12": ["IGST12"], "14": ["CGST14", "SGST14"], "18": ["IGST18"], "28": ["IGST28"]}

default_tax_dict = {
	"CGST 2.5%": "CGST2_5", "SGST 2.5%": "SGST2_5", "IGST 5%": "IGST5", "CGST 6%": "CGST6",
	"SGST 6%": "SGST6", "CGST 9%": "CGST9", "SGST 9%": "SGST9", "IGST 12%": "IGST12",
	"CGST 14%": "CGST14", "SGST 14%": "SGST14", "IGST 18%": "IGST18", "IGST 28%": "IGST28"}

tax_percentage_dict = {'2.50': '2_5', '5': '5', '6': '6', '9': '9', '12': '12', '14': '14', '18': '18', '28': '28'}

indirect_expenses_dict = {
	"round-off", "round off", "discount"}
