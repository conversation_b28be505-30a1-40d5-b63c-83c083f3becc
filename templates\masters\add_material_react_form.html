<style>
	.ui-autocomplete {
		z-index: 10021 !important;
	}

	.material_price_status > select {
		padding: 2px;
		font-size: 12px;
	}

	.input_unit_display {
		border:  none;
		background: transparent;
		text-align: right;
		padding: 0 !important;
	    height: 12px !important;
	    width: 70px;
	}

	.document_attachment_part {
	    display: inline-block;
	    width: 90px;
	    border: solid 1px #ccc;
	    padding: 10px 6px 0;
	    margin: 8px;
	    border-radius: 4px;
	    text-align: center;
	}

	.document_text {
	    display: block;
	    width: 75px;
	    text-overflow: ellipsis;
	    max-width: 75px;
	    overflow: hidden;
	    white-space: nowrap;
	}

	.document_remove {
	    position: absolute;
	    margin-left: 38px;
	    margin-top: -19px;
	    cursor: pointer;
	    font-size: 16px;
	}
	.duplicate_material_name{
		font-size: 12px;
		color: #dd4b39;
		position: absolute;
		letter-spacing: 0.30px;
		line-height: 20px;
	}

	#material_attachment_upload .error-border {
		background-color: #fdeded !important;
	}

	#add_new_alternate_unit .unit_display {
	    max-width: 150px;
	    white-space: nowrap;
	    overflow: hidden;
	    text-overflow: ellipsis;
	}

	#alternate_unit_table td {
		border-top: none;
		padding: 3px 6px;
	}

	.set-moq-btn.moq-active {
		background: #004195;
		color: #fff;
	}

	.set-moq-btn.moq-active:hover {
		color: #FFF;
	}
	.item_text_box,
	 .item-with-service input{
		width: calc(100% - 30px);
	}

	.service-item-flag.floated-right-flag {
		float: right;
    	margin-top: -20px;
	}

	#materialtable_bom tbody tr td .tree-view {
	position: relative;
	}

	#materialtable_bom tbody tr td .tree-view:before {
	    border-bottom: 1px solid #bbb;
	    content: "";
	    left: 6px;
	    position: absolute;
	    top: 6px;
	    width: 1.5em;
	}

	#materialtable_bom tbody tr td .tree-view:after {
	    border-left: 1px solid #bbb;
	    content: "";
	    left: 6px;
	    position: absolute;
	    top: -25px;
	    height: 4em;
	}

	#materialtable_bom .fa-plus:before,
	#materialtable_bom .fa-minus:before {
		border: solid 1px #333;
	    padding: 2px 3px 2px 3px;
	    border-radius: 3px;
	    font-size: 10px;
	}
	.price_width{
		width: calc(100% - 42px);
	}
	.price_profile_mask{
	cursor:pointer;
	}
</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/add_material.js?v={{ current_version }}"></script>
<input type="hidden" id="is_access_level_edit" value="{% if access_level.edit %} 1 {%endif%}" />
<div class="col-lg-4 material-container">
	<div class="form-group">
		<div class="text-right">
			<span class="hide">{{ material_form.is_service }}</span>
			<div id="switch_material_service" class="switch-radio-button btn-group" style="max-width: 250px;">
				<a style="padding: 4px;" class="goods noActive" data-toggle="switch_material_type" data-title= "1">Goods</a>
				<a style="padding: 4px;" class="service noActive" data-toggle="switch_material_type" data-title="2">Service</a>
			</div>
		</div>
		{% if material_form.name.value == '' or material_form.name.value == None %}
			<div>
				<div id="id_material_name-react_component" allowEdit="true"></div>
			</div>
		{% else %}
			{% if access_level.approve %}
				<div class='material-name-in-input hide' id="material-input">
					<div id="id_material_name-react_component" allowEdit="true"></div>
				</div>
				<div class="material-name header-label" style="margin-top: -10px;" onclick="showContainer('material-input'); hideContainer(this); focusOn('id_material-name')">
					{{ material_form.name.value }}
					<i class="fa fa-pencil pull-right" style="color: #209be1; display: none;" aria-hidden="true"></i>
				</div>
			{% else %}
				<div class='material-name-in-input hide' id="material-input">
					<div id="id_material_name-react_component" allowEdit="true"></div>
				</div>
				<div class="material-name header-label" >
					{{ material_form.name.value }}
					<i class="fa fa-pencil pull-right" style="color: #777; display: none;" aria-hidden="true" data-tooltip="tooltip" data-placement="left" data-title="Only Super User can edit this field"></i>
				</div>
				<input type="hidden" value="{{ material_form.name.value }}" id="id_material-hidden_name" name="material-name"/>
			{% endif %}
		{% endif %}
		<div class="duplicate_material_name" style="margin-top: -20px;"></div>
	</div>
	
	<div class="form-group col-sm-6 remove-padding service_item_code">
		<label>Drawing Number</label>
		<input type="hidden" id="last_saved_item_name" value="{{last_saved_item_name}}"/>
		<div id="id_material_drawingno-react_component" allowEdit="true"></div>
		{{ material_form.enterprise_id}}
		{{ material_form.material_id }}
		<div class="duplicate_drawing" style="font-size: 12px; color: #dd4b39;  position: absolute; letter-spacing: 0.30px; line-height: 20px;"></div>
	</div>
	<div id="id_material_category-react_component" allowEdit="true"></div>
	<div id="id_material_description-react_component" allowEdit="true"></div>
	<div class="form-group for_goods" id="makes">
		<label style="width: 100%;">
			Makes
		</label>

		<label class="ui-autocomplete-loading hide">&nbsp;</label>
		<ul id="ul-tagit-display" class="tagit-display form-control">
			{% for make_form in material_makes_formset.initial_forms %}
				<!-- <li class="li-tagit-display" id="{{ make_form.prefix }}" {% if make_form.make_id.value == 1 %}style="display:none;"{% endif %}  {% if make_form.make.value in makes_in_use %} data-tooltip="tooltip" title="Makes in use cannot be removed." {% endif %} > -->
				<li class="li-tagit-display" id="{{ make_form.prefix }}" {% if make_form.make_id.value == 1 %}style="display:none;"{% endif %} >
					<div hidden="hidden">{{ make_form.make }} {{ make_form.part_no }} {{ make_form.make_id }} {{ make_form.standard_packing_quantity }} {{ make_form.DELETE }}</div>
					<input type="hidden" class="current_make_id" value="{{make_form.make_id.value}}" />
					<label class="label-tagit-display"  id="id_{{make_form.prefix}}-make_label" data-partno="{{ make_form.part_no.value }}" data-make="{{ make_form.make.value }}" data-spq="{{ make_form.standard_packing_quantity.value }}">
						{{ make_form.make.value }}{% if make_form.part_no.value != "" %} - {{ make_form.part_no.value }} {% endif %} {% if make_form.standard_packing_quantity.value and  make_form.standard_packing_quantity.value != "" %} - {{ make_form.standard_packing_quantity.value|floatformat:3 }} {% endif %}
					</label>&nbsp;
					<!-- {% if make_form.make.value not in makes_in_use %}<a class="delete_tag" id="{{make_form.prefix}}-del_button"></a>{% endif %}&nbsp; -->
					<a class="delete_tag" id="{{make_form.prefix}}-del_button"></a>
				</li>
			{% endfor %}
			<li id="material_make-__dummy__" hidden="hidden">
				<div hidden="hidden">{{ material_makes_formset.empty_form.make }} {{ material_makes_formset.empty_form.part_no }} {{ material_makes_formset.empty_form.make_id }} {{ material_makes_formset.empty_form.is_service }} {{ material_makes_formset.empty_form.standard_packing_quantity }}
				{{ material_makes_formset.empty_form.DELETE }}</div>
				<input type="hidden" class="current_make_id" value="{{ material_makes_formset.empty_form.make_id.value }}" />
				<label class="label-tagit-display"  id="id_{{material_makes_formset.empty_form.prefix}}-make_label"></label>&nbsp;
				<a class="delete_tag" id="{{material_makes_formset.empty_form.prefix}}-del_button"></a>
				&nbsp;
			</li>

			<span>
				{{ material_makes_formset.management_form }}
				<span id="{{ material_makes_formset.empty_form.prefix }}" class="text_for_tag" >
					{{ material_makes_formset.empty_form.make }}
					<span hidden="hidden">{{ material_makes_formset.empty_form.DELETE }}</span>
					<span hidden="hidden">{{ material_makes_formset.empty_form.make_id }}</span>
				</span>
			</span>
		</ul>
	</div>
	<div class="col-sm-2 hide" style="margin-top: 21px;">
		<input id="add_make" class="btn btn-save btn-margin-1" value="+" type="button">
	</div>
	<div class="clearfix"></div>
	<div id="id_material_tariffno-react_component" allowEdit="true"></div>
	<div id="id_material_price-react_component" allowEdit="true"></div>
	<div id="id_material_unit-react_component" allowEdit="true"></div>
	<div class="form-group col-sm-6 remove-padding-left for_goods">
		<label>Rack</label>
		{{ material_form.location }}
	</div>
	<div class="form-group col-sm-6 remove-padding-left for_goods">
		<label>STD Packing Qty(SPQ)</label>
		{{ material_form.standard_packing_qty }}
	</div>
	<input type="hidden" id="alternate_unit_count" value="{{alternate_unit_formset.initial_forms|length}}" />
	<input type="hidden" id="is_negative_stock_allowed" value="{{logged_in_user.enterprise.is_negative_stock_allowed}}" />
	<div id="id_material_msl-react_component" allowEdit="true" class="for_goods"></div>
	<div class="form-group col-sm-12 remove-padding hide" id="conversion_rate" >
		<label>Conversion Rate<span class="mandatory_mark"> *</span></label>
		{{ material_form.unit_conversion_rate }}
	</div>
	<div class="full_width_txt">
        <input type="text" value="{{ material_form.catalogue_code.value }}" id="id_material-catalogue_code" name="material-catalogue_code" hidden="hidden"/>
	</div>
	<div class="clearfix"></div>
	<div id="div_is_stocked">
		<div class="checkbox for_goods remove-chk-padding xs-styled-checkbox {% if stock_qty > 0 and not logged_in_user.is_super %}disabled{% endif %}" style="margin-top: 0; margin-right: 10px;">
			{{ material_form.is_stocked}}
			<label for="id_material-is_stocked">Stockable</label>
		</div>
		<div class="checkbox remove-chk-padding xs-styled-checkbox xs-styled-checkbox-inuse {% if stock_qty > 0 and material_form.in_use.value and not logged_in_user.is_super %}disabled{% endif %}">
			{{ material_form.in_use }}
			<label for="id_material-in_use">In Use</label>
		</div>
	</div>
	<div class="material_txt material-save-buttons" style="margin-top: 10px;">
		{% if access_level.edit %}
		<a role="button" tabindex="0" id="saveCatalogueButton" class="btn btn-save" >Save</a>
		{% endif %}
        <input type="button" hidden="hidden" id="saveCatalogue" value="Save">
		{% if material_type == 'service' %}
			<a href="/erp/masters/services/" id="cancellCatalogue" class="btn btn-cancel">Cancel</a>
		{% else %}
			<a href="/erp/masters/catalogues/" id="cancellCatalogue" class="btn btn-cancel">Cancel</a>
		{% endif %}
	</div>
</div>

<div class="col-lg-8 material-supporting-container remove-right-padding for_goods" style="margin-top: 15px;">
	{% if material_form.is_stocked.value %}
		 <div class="available_stock_container_parent" style="width:100%;">
			<div class="available_stock_container" data-toggle="modal" data-target="#available_stock_modal" style="width: 48.7%;padding-left: 4px;margin-top:44px;cursor:pointer">
				<h5 style="float: left; margin-bottom: 0;"><label>Available Stock:</label></h5>
				<span class="stock_total">{{ stock_qty }}</span>
			</div>
			<div class="available_stock_container_faulty" data-toggle="modal" data-target="#available_stock_modal" style="display: inline-block;padding: 0 15px;border: dashed 1px #CCC;position: absolute;top: -49px;left: 51%;width:49%;padding-left: 4px;margin-top:44px;cursor:pointer">
				<h5 style="float: left; margin-bottom: 0;"><label>Faulty:</label></h5>
				<span class="stock_total">{{faulty_qty}}</span>
			</div>
		</div>
	{% endif %}	
	<div class="clearfix"></div>
	<div class="accordion" id="accordion" style="margin-top:50px">
		<div class="accordion-group">
			<div class="accordion-heading">
				<span class="accordion-toggle" aria-expanded="false">
					Specifications
					<span class="btn btn-save pull-right" onclick="addNewSpecification()" style="padding: 2px 12px;">
						+
					</span>
				</span>
			</div>
			<div id="specification_container" class="collaps">
				<div class="accordion-inner row" style="margin: 0;">
					<div id="specification" style="margin: 0 -15px;" >
						<div hidden="hidden">
							{{ specifications_formset.empty_form.material_id }}
							{{ specifications_formset.empty_form.enterprise_id }}
						</div>
						<div id="edit_specification_modal" class="modal" role="dialog" data-backdrop="static" data-keyboard="false" style="margin-left: -730px;">
						  	<div class="modal-dialog" style="width: 400px; margin-top: 72px;">
						    	<div class="modal-content">
						      		<div class="modal-header">
						      			<button type="button" class="close" data-dismiss="modal">&times;</button>
						        		<h4 class="modal-title">Edit Specification</h4>
						      		</div>
						      		<div class="modal-body">
								        <div class="form-group">
								        	<label>Parameter<span class="mandatory_mark"> *</span></label>
											{{ specifications_formset.empty_form.parameter }}
										</div>
										<div class="form-group col-sm-4 remove-padding-left">
											<label>Min Value</label>
											{{ specifications_formset.empty_form.min_value }}
										</div>
										<div class="form-group col-sm-4 remove-padding">
											<label>Max Value</label>
											{{ specifications_formset.empty_form.max_value }}
										</div>
										<div class="form-group col-sm-4 remove-padding-right">
											<label>Unit</label>
											{{ specifications_formset.empty_form.unit }}
										</div>
										<div class="form-group">
											<label>Inspection Method/Tool</label>
											{{ specifications_formset.empty_form.inspection_method }}
										</div>
										<div class="form-group">
											<label>Reaction Plan</label>
											{{ specifications_formset.empty_form.reaction_plan }}
										</div>
										<div class="form-group" >
											<label>Instruction / comments</label>
											{{ specifications_formset.empty_form.comments }}
										</div>
										<div class="checkbox" style="padding-left: 20px !important;" >
											{{ specifications_formset.empty_form.qc_critical }}
											<label for="id_specification-__prefix__-qc_critical">QC Critical</label>
										</div>
							        </div>
								    <div class="modal-footer">
								    	<input type="button" id="add_new_specification" onclick="addSpecification(this, 'add');" class="btn btn-save hide" value="Add" />
										<input type="button" id="update_specification" onclick="addSpecification(this, 'update');" class="btn btn-save hide" value="Update"/>
									    <span class="btn btn-cancel" data-dismiss="modal">Cancel</span>
								    </div>
							    </div>
						    </div>
						</div>
						<div class="col-sm-12" style="overflow: auto; max-height: 265px;">
							{{ specifications_formset.management_form}}
							<table class="table custom-table text_box_in_table" id="specification_table" style="border-collapse: separate; border-spacing: 0 1em; border: none; margin-bottom: 0;">
								<tbody>
									<tr bgcolor="#ececec" id="specification-__dummy__" hidden="hidden">
										<td hidden="hidden" class="exclude_export">
											{{ specifications_formset.empty_form.material_id }}
											{{ specifications_formset.empty_form.DELETE }}
											{{ specifications_formset.empty_form.enterprise_id }}
										</td>
										<td class="material_specification_container hide">
											<span id="id_{{ specifications_formset.empty_form.prefix }}--parameterLabel">
												{{ specifications_formset.empty_form.parameter }}
											</span>
											<span id="id_{{ specifications_formset.empty_form.prefix }}-minvalueLabel">
												{{ specifications_formset.empty_form.min_value }}
											</span>
											<span id="id_{{ specifications_formset.empty_form.prefix }}-maxvalueLabel">
												{{ specifications_formset.empty_form.max_value }}
											</span>
											<span id="id_{{ specifications_formset.empty_form.prefix }}-unitLabel">
												{{ specifications_formset.empty_form.unit }}
											</span>
											<span id="id_{{ specifications_formset.empty_form.prefix }}-inspectionMethodLabel">
												{{ specifications_formset.empty_form.inspection_method }}
											</span>
											<span id="id_{{ specifications_formset.empty_form.prefix }}-reactionPlan">
												{{ specifications_formset.empty_form.reaction_plan }}
											</span>
											<span id="id_{{ specifications_formset.empty_form.prefix }}-commentsLabel">
												{{ specifications_formset.empty_form.comments }}
											</span>
											<span id="id_{{ specifications_formset.empty_form.prefix }}-qcCriticalLabel">
												{{ specifications_formset.empty_form.qc_critical }}
											</span>
										</td>
										<td class="material_specification_container_label" onclick="editSpecification(this)"></td>
										<td class="material_specification_container_delete"></td>
									</tr>

									{% for specification in specifications_formset.initial_forms %}
									<tr bgcolor="#ececec" id="{{ specification.prefix }}">
										<td hidden="hidden" class="exclude_export">
											{{ specification.material_id }}{{ specification.parameter }}{{ specification.comments }}
											{{ specification.inspection_method }}{{ specification.unit }}{{ specification.enterprise_id }}
											{{ specification.DELETE }}{{ specification.min_value}}{{ specification.max_value }}{{ specification.qc_critical }}{{ specification.reaction_plan }}
										</td>
										<td class="material_specification_container_label" onclick="editSpecification(this)">
											<span class="material_specification_parameter">{{ specification.parameter.value }}</span>
											<span class="material_specification_minMaxValue">
												{% if specification.min_value.value == specification.max_value.value and specification.min_value.value != None %}
													{{specification.min_value.value}}
												{% elif specification.min_value.value != None and  specification.max_value.value != None %}
													{{specification.min_value.value}} - {{specification.max_value.value}}
												{% elif specification.min_value.value == None and  specification.max_value.value != None %}
													<= {{specification.max_value.value}}
												{% elif specification.min_value.value != None and  specification.max_value.value == None %}
													>= {{specification.min_value.value}}
												{% endif %}
											</span>
						                    <span class="material_specification_unit">{{ specification.unit.value }}</span>
						                    <span class="material_specification_qc {% if specification.qc_critical.value%}checked{% endif%} pull-right"></span>
						                    <span class="material_specification_inspectionMethod">{{ specification.inspection_method.value }}</span>
						                    <span class="material_specification_reactionPlan">{{ specification.reaction_plan.value }}</span>
						                    <span class="material_specification_remarks">{{ specification.comments.value }}</span>
						                </td>
						                <td  class="material_specification_container_delete">
						                	<span class="material_specification_delete" role="button">
						                        <a onclick="javascript:deleteSpecification('{{specification.prefix}}')">
						                            <i class="fa fa-times"></i>
						                        </a>
					                    	</span>
					                    </td>
									</tr>
									{% endfor %}
									{% if specifications_formset.initial_forms|length == 0 %}
										<tr class="empty-specification">
											<td style="border: none; text-align: center"><b>No Specification profiled yet!</b></td>
										</tr>
									{% endif %}
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="clearfix"></div>
	</div>
	<div class="row" id="qc_related" style="padding: 0 15px;">
		<div class="form-group col-sm-6 remove-padding-left" >
			<label>QC Method </label>
			{{material_form.qc_method}}
		</div>
		<div class="form-group col-sm-3 remove-padding">
			<label>Sample Size</label>
			{{material_form.sample_size}}
		</div>
		<div class="form-group col-sm-3 remove-padding-right">
			<label>Lot Size</label>
			{{material_form.lot_size}}
		</div>
		<div class="form-group col-sm-12 remove-padding">
			<label>Reaction Plan</label>
			{{material_form.reaction_plan}}
		</div>
		<div class="form-group col-sm-12 remove-padding">
			<label>Remarks</label>
			{{material_form.remarks}}
		</div>
	</div>
</div>

<div id="material_bom_modal" class="modal fade" tabindex="-1" role="dialog" >
   <div class="modal-dialog" style="width: 1000px;">
	    <div class="modal-content">
		    <div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Bill of Materials</h4>
	      	</div>
      		<div class="modal-body remove-padding">
		        <div class="col-lg-12" id="bill_of_material" style="margin-bottom: 20px;margin-top: 10px;" >
					<div hidden="hidden">
						{{ bill_of_materials_formset.empty_form.catalogue_code }}
						<input type="text" value="0" hidden="hidden" id="newFormCount"/>
						{{ bill_of_materials_formset.empty_form.item_id }}
						{{ bill_of_materials_formset.empty_form.is_service }}
						{{ bill_of_materials_formset.empty_form.drawing_no }}
						{{ bill_of_materials_formset.empty_form.enterprise_id }}
						{{ bill_of_materials_formset.empty_form.material_select_field }}
						{{ bill_of_materials_formset.empty_form.make_select_field }}
						{{ bill_of_materials_formset.empty_form.make_id}}
					</div>
					<div class="col-sm-3 form-group" >
						<label>Item Name<span class="mandatory_mark"> *</span></label>
						<input type="text" value="" class="form-control" id="material_selected" placeholder="Select Item Name" maxlength="100"/>
						<input type="hidden" value="" class="text_box" id="drawing_no_selected"/>
					</div>
					<div class="col-sm-3" ><label>Quantity<span class="mandatory_mark"> *</span></label>
						<div>{{ bill_of_materials_formset.empty_form.quantity }}</div>
						<div >
							<label id="unit_display" class="unit_display pull-right">&nbsp;</label>
							<div style="display:none">{{ bill_of_materials_formset.empty_form.units }}</div>
						</div>
					</div>
					<div class="col-sm-3">
						<label>&nbsp;</label>
						<span class="material_txt">
							<input type="button" id="add_new_catalogue_material" class="btn btn-save btn-margin-1" value="+"/> &nbsp;&nbsp;
							<a role="button" class="btn btn-add-new pull-right btn-margin-1 export_bom_list" onclick="validateBOMDownload(this);"  data-tooltip="tooltip" title="" data-original-title="Download BoM List as CSV" data-placement="bottom"><i class="fa fa-download" aria-hidden="true"></i></a>
							<span class="tool-tip pull-right" data-toggle="tooltip" data-placement="top" title="Please enter drawing number">
							{% if access_level.edit %}
								<a role="button" id="importbutton" style="margin-right: 10px;"  title="" class="btn btn-add-new btn-margin-1" onclick="javascript:showImportBom();" data-tooltip="tooltip" title="" data-original-title="Upload Bulk BoM" data-placement="bottom"/><i class="fa fa-upload" aria-hidden="true" ></i></a>
							{% endif %}
							</span>
							<label>&nbsp;</label>
							<a role="button" class="btn btn-add-new pull-right btn-margin-1 service_costing" style="margin-right: 10px; padding: 4px 10px;" onclick="javascript:showCheapestSupplierBom(this);"  data-tooltip="tooltip" title="" data-placement="left" data-original-title="BOM Costing">
								<img src="/site_media/images/costing_sheet_icon.svg" style="width: 26px;" />
							</a>
						</span>
					</div>
					<div class="col-sm-12">
						{{bill_of_materials_formset.management_form}}
						<table class="table table-bordered table-striped custom-table text_box_in_table" id="materialtable_bom">
							<thead>
								<tr>
									<th width="20px">S.No</th>
									<th>Name</th>
									<th width="150px" class="service_item_code">Drawing No</th>
									<th width="84px">Quantity</th>
									<th width="55px">Unit</th>
									<th width="40px" class="exclude_export">Delete</th>
								</tr>
							</thead>

							<tbody>
							<div id='loadingmessage_changelog_listing_ie' style="text-align:center;position: absolute;z-index: 1;left: 40%;top: 30%;display: none;">
						<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' width="100">
						<br /> Please wait...
			    </div>
							<tr bgcolor="#ececec" id="bill_material-__dummy__" hidden="hidden">
								<td hidden="hidden" class="exclude_export">
									{{ bill_of_materials_formset.empty_form.catalogue_code }}
									{{ bill_of_materials_formset.empty_form.DELETE }}
									{{ bill_of_materials_formset.empty_form.id}}
									{{ bill_of_materials_formset.empty_form.ORDER }}
									{{ bill_of_materials_formset.empty_form.item_id }}
									{{ bill_of_materials_formset.empty_form.is_service }}
									{{ bill_of_materials_formset.empty_form.drawing_no }}
									{{ bill_of_materials_formset.empty_form.enterprise_id }}
								</td>
								<td></td>
								<td  colspan="2" style="text-align: left;">
									{{ bill_of_materials_formset.empty_form.material_select_field }}
								</td>
								<td class="text-right">
										<div id="id_{{ bill_of_materials_formset.empty_form.prefix }}-quantityLabel">{{ bill_of_materials_formset.empty_form.quantity }}</div>
									</td>
								<td width="100px">{{ bill_of_materials_formset.empty_form.units }}</td>
								<td width="20px" class="text-center"><a href="#" id="id_{{ bill_of_materials_formset.empty_form.prefix }}-deleteCatMaterial"
									   onclick="javascript:deleteCatMaterial('{{ bill_of_materials_formset.empty_form.prefix }}')">
									<i class="fa fa-trash-o"
										 id='deleteImage_{{ bill_of_materials_formset.empty_form.catalogue_code.value }}'></i></a>
								</td>
							</tr>

							{% for bill_material in bill_of_materials_formset.initial_forms %}
							<tr bgcolor="#ececec" data-toggle="close" data-padding="0" data-parent="{{ bill_material.item_id.value }}" id="{{ bill_material.prefix }}">
								<td hidden="hidden" class="exclude_export">
									{{ bill_material.catalogue_code }}{{ bill_material.drawing_no }}{{ bill_material.item_id }}
									{{ bill_material.quantity }}{{ bill_material.units }}{{ bill_material.enterprise_id }}
									{{ bill_material.DELETE }}{{ bill_material.id}}{{ bill_material.ORDER }}{{ bill_material.is_service }}
									{{ bill_material.is_stock.value }}
								</td>
								<td class="text-left bom-sno">{{ forloop.counter}}</td>
								<td>
									{% if bill_material.hasChildren.value %}
										<i class='fa fa-plus fa-for-arrow' role='button' onclick='javascript:appendMaterialTableBOM("{{ bill_material.item_id.value }}","{{ bill_material.prefix }}")'></i>
									{% endif %}
									<a role="button" class="{% if bill_material.hasChildren.value %}anchorTag-with-child{%else%}anchorTag-without-child{%endif%}" onclick='javascript:editMaterialFromBom("{{ bill_material.item_id.value }}")' >
										{{ bill_material.name.value }}
										{% if not bill_material.is_stock.value == "True" and  bill_material.is_service.value != 'True' %}<span class='non_stock-flag'></span>{% endif %}
										{% if bill_material.is_service.value == 'True' %}<span class='service-item-flag'></span>{% endif %}
									</a>
								</td>
								<td width="150px">{{ bill_material.drawing_no.value }}</td>
								<td class="text-right">
										<div id="id_{{ bill_material.prefix }}-quantityLabel"> {{ bill_material.quantity.value }}</div>
									</td>
								<td width="55px">{{ bill_material.units.value }}</td>
								<td width="20px" class="text-center"><a href="#" class="bom-delete" id="id_{{ bill_material.prefix }}-deleteCatMaterial"
									   onclick="javascript:deleteCatMaterial('{{ bill_material.prefix }}')">
									<i class="fa fa-trash-o" id='deleteImage_{{ bill_material.prefix }}'></i></a>
								</td>
							</tr>
							{% endfor %}
							{% if bill_of_materials_formset.initial_forms|length == 0 %}
							<tr><td colspan="9" align="center"><b class="service_package_profile">No BoM profiled yet!</b></td></tr>
							{% endif %}
							</tbody>
						</table>
						<hr/>
					</div>
					{% if participating_bom_of_materials|length > 0 %}
					<div class="col-sm-9 "><h4 class="service_material_container"> BoMs containing this Material </h4></div>
					<div class="col-sm-3"><span class="material_txt"><a role="button" class="btn btn-add-new pull-right export_csv"
					   onclick="GeneralExportTableToCSV.apply(this, [$('#participating_bom_materialtable'), '{{material_form.drawing_no.value}}-parts_thereof.csv']);"
					   data-tooltip="tooltip" title="" data-original-title="Download the list of BoMs, this Material is part of as CSV">
						<i class="fa fa-download" aria-hidden="true"></i>
					</a></span></div>
					<div class="col-sm-12">
						<table class="table table-bordered table-striped custom-table text_box_in_table" id="participating_bom_materialtable">
							<thead>
								<tr>
									<th width="20px">S.No</th>
									<th>Name</th>
									<th width="150px" class="service_item_code">Drawing No</th>
									<th width="84px">Quantity</th>
								</tr>
							</thead>
								{% for bom_material in participating_bom_of_materials %}
									<tr bgcolor="#ececec" id="{{ bom_material.prefix }}">
										<td class="text-right">{{ forloop.counter}} 	</td>
										<td>
											<a role='button' onclick='javascript:editMaterialFromBom("{{ bom_material.item_id }}")'>{{ bom_material.name }}</a>
											{% if bom_material.is_service == 1 %}
												<span class="service-item-flag"></span>
											{% endif %}
											{% if not bom_material.is_stock and bom_material.is_service != 1 %}
												<span class="non_stock-flag"></span>
											{% endif %}
										</td>
										<td width="150px">{{ bom_material.drawing_no }}</td>
										<td align="right" width="84px"><div id="id_{{ bom_material.prefix }}-quantityLabel"> {{ bom_material.qty }}</div></td>
									</tr>
								{% endfor %}
							</tbody>
						</table>
					</div>
					{% else %}
					<div class="col-sm-12 service_part_material" style="font-style:italic">
						This Material does not form part of any other Material's BoM!
					</div>
					{% endif %}
				</div>
      		</div>
      		<div class="modal-footer">
		       <span class="pull-left nonStock-text">  <span class=" nonStock-text non_stock-flag"></span> - NON-STOCKABLE</span>
				<span class="pull-left nonStock-text">  <span class=" nonStock-text service-item-flag"></span> - SERVICE</span>
      			<a href="#" class="btn btn-cancel" data-dismiss="modal">Close</a>
      		</div>
    	</div>
  	</div>
</div>
<div id="material_price_profile" class="modal fade" tabindex="-1" role="dialog" >
   <div class="modal-dialog" style="width: 1200px;">
	    <div class="modal-content">
		    <div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Price Profile</h4>
	      	</div>
      		<div class="modal-body remove-padding">
		        <div class="col-lg-12" id="material_sup_profile">
					<div class="general-warning" style="margin-top: 10px; padding: 3px 15px; margin-bottom: 10px;">
						<i>Ensure that Prices mentioned here are without Tax and with all Discounts applied.</i>
					</div>
					<div hidden="hidden">
						{{ materials_party_price_formset.empty_form.supp_id }}
						{{ materials_party_price_formset.empty_form.drawing_no }}
						{{ materials_party_price_formset.empty_form.item_id }}
						{{ materials_party_price_formset.empty_form.enterprise_id }}
						{{ materials_party_price_formset.empty_form.supplier_select_field }}
						{{ materials_party_price_formset.empty_form.effect_since }}
						{{ materials_party_price_formset.empty_form.effect_till }}
						{{ materials_party_price_formset.empty_form.is_primary }}
						{{ materials_party_price_formset.empty_form.is_service }}
						{{ materials_party_price_formset.empty_form.make_select_field }}
						{{ materials_party_price_formset.empty_form.currency_select_field }}
						{{ materials_party_price_formset.empty_form.alternate_unit_id }}
						{{ materials_party_price_formset.empty_form.is_mask_price }}
					</div>
					<div class="row">
						<div class="col-sm-4 form-group">
							<label>Supplier<span class="mandatory_mark"> *</span></label>
							<input type="text" value="" class="form-control" id="supplier_selected" placeholder="Select Supplier" maxlength="100"/>
							<input type="hidden" value="" class="text_box" id="supplier_no_selected"/>
							<span class="supplier-removal-icon hide">
								<i class="fa fa-times"></i>
							</span>
						</div>
						<div class="col-sm-2 form-group" hidden="hidden">
							
							<label>Make<span class="mandatory_mark"> *</span></label>
							<select class="form-control" id="id_price_material-__prefix__-make_choices" value="{{ make.0 }}">
								{% for make in make_list %}
								<option value="{{ make.0 }}" data-spq="{{ make.2 }}">{{ make.1 }}</option>
								{% endfor %}
							</select>
							

						</div>
						<div class="col-sm-2 form-group">
							<label>Price<span class="mandatory_mark"> *</span></label>
							{{ materials_party_price_formset.empty_form.price }}
							{{ materials_party_price_formset.empty_form.currency_id }}
							<label class="unit_display pull-right" id="currency_unit_display">&nbsp;</label>
						</div>
						<div class="col-sm-4">
							<div class="col-sm-6 form-group">
								<label>Min. Order Qty(MOQ)</label>
								{{ materials_party_price_formset.empty_form.moq }}
								<label class="unit_display pull-right" id="quantity_unit_display">&nbsp;</label>
								<select class="form-control unit_select_box hide" id="price_profile-alternate_units" name="price_profile-alternate_units" placeholder="Units">
									<option id="price-profile-default-unit" value='0' data-val='1'></option>
									{% for alternate_unit in alternate_unit_formset.initial_forms %}
										<option value="{{alternate_unit.alternate_unit_id.value}}" data-val="{{ alternate_unit.scale_factor.value }}">{{ alternate_unit.unit_name.value }}</option>
									{% endfor %}
								</select>
							</div>
							<div class="col-sm-6 form-group">
								<label>Lead Time</label>
								{{ materials_party_price_formset.empty_form.lead_time }}
							</div>
						</div>
					</div>
					<div class="row effect_date_parent">
						<div class="col-sm-3 form-group effect_since_parent">
							<label>Effect Since</label>
							<div class="hide">{{ materials_party_price_formset.empty_form.effect_since }}</div>
							<input type="text" value="" class="hide" id="id_material-effect_since" placeholder="Effect From" />
							<input type="text" class="form-control custom_datepicker full-datepicker effect_since_child" readonly="readonly" id="effect_from_date" placeholder="Select Date" style="background: #FFF;">
							<i class="glyphicon glyphicon-calendar custom-calender-icon"></i>
						</div>
						<div class="col-sm-3 form-group effect_till_parent">
							<label>Effect Till</label>
							<div class="hide">{{ materials_party_price_formset.empty_form.effect_till }}</div>
							<input type="text" value="" class="hide" id="id_material-effect_till" placeholder="Effect Till" />
							<input type="text" class="form-control custom_datepicker set-empty-date erasable-date effect_till_child" autocomplete="off" id="effect_till_date" placeholder="Select Date">
							<i class="glyphicon glyphicon-calendar custom-calender-icon"></i>
						</div>
						<div class="col-sm-5 form-group">
							<label>Remarks</label>
							{{ materials_party_price_formset.empty_form.remarks }}
						</div>
						<label>&nbsp;</label>
						<div class="material_txt">
							<input type="button" id="add_new_supplier_material" class="btn btn-save"
								   value="+"/>
						</div>
					</div>
				</div>
				<div class="col-sm-12">
					{{materials_party_price_formset.management_form}}
					<table class="table table-striped table-bordered custom-table text_box_in_table" id="supp_price_table" style="margin-top: 15px;">
						<thead>
							<tr>
								<th rowspan="2" width="20px">Store Value</th>
								<th rowspan="2" width="20px">Job</th>
								<th class='hide' rowspan="2">Supplier</th>
								<!-- <th rowspan="2" width="30%">Make</th> -->
								<th rowspan="2" width="100px">Price</th>
								<th rowspan="2" width="100px">MOQ</th>
								<th rowspan="2" width="100px">Lead Time</th>
								<th colspan="2" width="100px">Effective</th>
								<th class='hide'rowspan="2" width="70px">Remarks</th>
								<th rowspan="2" width="100px">Status</th>
							</tr>
							<tr>
								<th width="50px">Since</th>
								<th width="50px">Till</th>
							</tr>
						</thead>
						<tbody>
							<tr  id="price_material-__dummy__" hidden="hidden" class="effect_date_parent">
								<td hidden="hidden">
									{{ materials_party_price_formset.empty_form.supp_id }}
									{{ materials_party_price_formset.empty_form.DELETE }}
									{{ materials_party_price_formset.empty_form.id}}
									{{ materials_party_price_formset.empty_form.item_id }}
									{{ materials_party_price_formset.empty_form.is_service }}
									{{ materials_party_price_formset.empty_form.drawing_no }}
									{{ materials_party_price_formset.empty_form.enterprise_id }}
									{{ materials_party_price_formset.empty_form.currency_id }}
									{{ materials_party_price_formset.empty_form.alternate_unit_id }}
									{{ materials_party_price_formset.empty_form.is_mask_price }}
								</td>
								<td class="text-center div-disabled">
									{{ materials_party_price_formset.empty_form.is_primary }}
								</td>
								<td class="text-center">{{ materials_party_price_formset.empty_form.is_service }}</td>
								<td class='hide'>{{ materials_party_price_formset.empty_form.supplier_select_field }}</td>
								<td hidden="hidden">{{ materials_party_price_formset.empty_form.make_id }}</td>
								<td hidden="hidden">{{ materials_party_price_formset.empty_form.is_service }}</td>
								<td hidden="hidden">{{ materials_party_price_formset.empty_form.make_select_field }}</td>
								<td class="td_textbox_right text-right">
									{{ materials_party_price_formset.empty_form.price }}
									<span class='hide'>{{ materials_party_price_formset.empty_form.currency_select_field }}</span>
									<span class="price-profile-unit" style="display: block;"></span>
								</td>
								<td class="td_textbox_right text-right">
									{{ materials_party_price_formset.empty_form.moq }}
									<span class="price-profile-moq-unit" style="display: block;"> {{ materials_party_price_formset.empty_form.unit_name }}</span>
								</td>
								<td>
									{{ materials_party_price_formset.empty_form.lead_time }}
								</td>
								<td class="td_load_date effect_since_parent">
									{{ materials_party_price_formset.empty_form.effect_since }}
									<input type="text" class="form-control custom_datepicker full-datepicker effect_since_child" readonly="readonly" id="id_{{ materials_party_price_formset.empty_form.prefix }}-effect_since_date" placeholder="Select Date" style="background: #FFF; min-width: 84px; font-size: 12px; padding: 4px;">
								</td>
								<td class="td_load_date effect_till_parent">
									{{ materials_party_price_formset.empty_form.effect_till }}
									<input type="text" class="form-control custom_datepicker set-empty-date erasable-date effect_till_child" autocomplete="off" readonly="readonly" id="id_{{ materials_party_price_formset.empty_form.prefix }}-effect_till_date" placeholder="Select Date" style="background: #FFF; min-width: 84px; font-size: 12px; padding: 4px;">
								</td>
								<td style="width: 100px; max-width: 100px; word-wrap: break-word;">
									<span class='price_profile_remarks' title='Remarks' onclick='openPriceProfileRemarks(this);'>
	                                    <i class="fa fa-comment" aria-hidden="true"></i>
	                                </span>
									{% if access_level.approve %}
										{{ materials_party_price_formset.empty_form.status }}
									{% else %}
									<div class="hide">{{ materials_party_price_formset.empty_form.status }}</div>
										Pending
									{% endif %}
								</td>
							</tr>

							{% for price_material in materials_party_price_formset.initial_forms %}
							<tr style="background: rgba(32,155,255,0);" data-supplier-id='{{ price_material.supp_id.value }}'id="{{ price_material.prefix }}" class="effect_date_parent">
								<td hidden="hidden">
									{{ price_material.supp_id }}{{ price_material.drawing_no }}{{ price_material.item_id }}
									{{ price_material.remarks }}{{ price_material.enterprise_id }}
									{{ price_material.DELETE }}{{ price_material.id}}{{ price_material.currency_id}}
									{{ price_material.alternate_unit_id}}
								</td>
								<td class="text-center set-price-primary {% if price_material.status.value != 1 %}div-disabled{% endif %}">
									{{ price_material.is_primary}}
								</td>
								<td class="text-center {% if price_material.status.value == 1 %}div-disabled{% endif %}">
									{{ price_material.is_service}}
								</td>
								<td class='td-supplier-name hide'>{{ price_material.supplier_select_field.value }}</td>
								<td hidden="hidden">{{ price_material.make_id }}<div id="id_{{ price_material.prefix }}-makeLabel">{{ price_material.make_select_field.value }}</div></td>
								<td  class="text-right td_textbox_right td_textbox_righttable_text_box">
									{% if logged_in_user.is_super %}
										{% if price_material.is_mask_price.value == True %}
										<span class='price_profile_mask' data-tooltip='tooltip' title='Unmask Price'  onclick='maskPriceProfile(this, "unmask");'>
		                                    <i class="fa fa-eye-slash" aria-hidden="true"></i>
		                                </span>
										{% else %}
										<span class='price_profile_mask' data-tooltip='tooltip' title='Mask Price'  onclick='maskPriceProfile(this, "mask");'>
		                                    <i class="fa fa-eye" aria-hidden="true"></i>
		                                </span>

										{% endif %}
									{% endif %}
									{% if price_material.status.value != -1 and price_material.status.value != 0 %}
										<span class='hide'>{{ price_material.price }}</span>
										{% if logged_in_user.is_super %}
											<span class="price-display">{{ price_material.price.value }}</span>
										{% else %}
											{% if price_material.is_mask_price.value == True %}
												<span class="price-display">xxx.xx</span>
											{% else %}
												<span class="price-display">{{ price_material.price.value }}</span>
											{% endif %}
										{% endif %}
									{% else %}
										{{ price_material.price }}
									{% endif %}
									<span class="price-profile-unit price-currency-field" style="display: block;">
										{{ price_material.currency_select_field.value}}
									</span>
								</td>
								<td  class="text-right td_textbox_right td_textbox_righttable_text_box">
									{{ price_material.moq }}
									<span class="price-profile-unit" style="display: block;">
										<span class="price-profile-moq-unit" style="display: block;">{{ price_material.unit_name.value }}</span>
									</span>
								</td>
								<td  class="td_textbox_righttable_text_box">
									{{ price_material.lead_time }}
								</td>
								<td class="text-center td_load_date effect_since_parent">
									<div {% if price_material.status.value != -1 and price_material.status.value != 0 %}class="hide"{% endif %}>
										{{ price_material.effect_since }}
										<input type="text" class="form-control custom_datepicker full-datepicker effect_since_child" readonly="readonly" id="effect_from_date_{{ price_material.prefix }}" placeholder="Select Date" style="background: #FFF; min-width: 84px; font-size: 12px; padding: 4px;">
									</div>
									{% if price_material.status.value != -1 and price_material.status.value != 0 %}{{ price_material.effect_since.value|date:'M d, Y' }}{% endif %}
								</td>
								<td class="text-center td_load_date effect_till_parent">
									<div {% if price_material.status.value != -1 and price_material.status.value != 0 %}class="hide"{% endif %} id="{{ price_material.prefix}}_effect_till">
										{% if price_material.effect_till.value and price_material.status.value == 1 %}
											{{ price_material.effect_till.value|date:'M d, Y' }}
											{{ price_material.effect_till }}
										{% else %}
											{{ price_material.effect_till }}
											<input type="text" class="form-control custom_datepicker set-empty-date erasable-date effect_till_child" autocomplete="off" readonly="readonly" id="effect_from_since_{{ price_material.prefix }}" placeholder="Select Date" style="background: #FFF; min-width: 84px; font-size: 12px; padding: 4px;">
										{% endif %}
									</div>
									{% if price_material.effect_till.value and price_material.status.value == 1 %}
										{{ price_material.effect_till.value|date:'M d, Y' }}
									{% else %}
										{% if price_material.status.value != -1 and price_material.status.value != 0 %}
											<a role="button" class="pull-right" style="font-size: 16px;" onclick="javascript:updateSupplierPrice('{{ price_material.prefix}}', '{{access_level.approve}}')">
												<i class="fa fa-pencil" aria-hidden="true"></i>
											</a>
										{% endif %}
									{% endif %}
								</td>
								<td class="text-center">
									<span class='price_profile_remarks' data-tooltip='tooltip' title='Remarks' onclick='openPriceProfileRemarks(this);'>
										<i class="fa fa-comment" aria-hidden="true"></i>
                                    </span>
									{% if access_level.approve and price_material.status.value == 0 %}
										<div class="material_price_status">{{ price_material.status }}</div>
									{% else %}
										<div class="hide">{{price_material.reject_remarks}}</div>
										<div class="hide">{{ price_material.status }}</div>
										<span class="supplier_price_status">
										{% if price_material.status.value == 0 %}
											Pending
										{% else %}
											{% if price_material.status.value == 1 %}
												Approved
											{% else %}
										</span>
										<select id="id_{{price_material.prefix}}-status_select" {% if price_material.reject_remarks.value|length > 0 %} data-tooltip="tooltip" title="Rejected due to:  {{price_material.reject_remarks.value}}" {% endif %} class="form-control" style="border: solid 1px red; color: red;">
											<option value="-1">Rejected</option>
											<option value="0"style="color: #000;">Re-Submit</option>
											<option value="-2"style="color: #000;">Remove</option>
										</select>
										<script type="text/javascript">
											$("#id_{{price_material.prefix}}-status_select").change(function(){
												if($("#id_{{price_material.prefix}}-status_select option:selected").val()=="-2"){
													$("#id_{{price_material.prefix}}-DELETE").attr("checked", "checked");
												}else{
													$("#id_{{price_material.prefix}}-DELETE").removeAttr("checked");
													$("#id_{{price_material.prefix}}-status").val($("#id_{{price_material.prefix}}-status_select option:selected").val());
												}
											});
										</script>
									{% endif %}
									{% endif %}
									{% endif %}
								</td>
							</tr>
							<tr class="general-remarks hide">
								<td colspan="3" class="text-right"><label>Remarks:</label></td>
								<td colspan="6">
									<div {% if price_material.status.value != -1 and price_material.status.value != 0  %}class="hide"{% endif %}>{{ price_material.remarks }}</div>
									{% if price_material.remarks.value and price_material.status.value == 1 %}
										{{ price_material.remarks.value }}
									{%endif %}
								</td>
							</tr>
							{% if access_level.approve and price_material.status.value == 0 %}
							<tr class="hide reject_remarks">
								<td colspan="3" class="text-right"><label>Rejection Remarks:</label></td>
								<td colspan="4">{{price_material.reject_remarks}}</td>
							</tr>
							{% endif %}
							{% endfor %}
							{% if materials_party_price_formset.initial_forms|length == 0 %}
								<tr><td colspan="9" align="center"><b>No prices profiled yet!</b></td></tr>
							{% endif %}
						</tbody>
					</table>
				</div>
			</div>
      		<div class="modal-footer">
      			<a href="#" class="btn btn-cancel" data-dismiss="modal">Close</a>
      		</div>
    	</div>
  	</div>
</div>

<div id="available_stock_modal" class="modal fade" role="dialog" >
  	<div class="modal-dialog">
    	<div class="modal-content">
      		<div class="modal-header">
      			<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title" id="modalTitle">Available Stock</h4>
      		</div>
      		<div class="modal-body">
		        <table class="table table-striped table-bordered custom-table text_box_in_table" id="supp_material_table" style="margin-top: 15px;">
					<thead>
						<tr>
							<th width="50%">Location</th>
							<th width="50%" id="stockQtyHeader">Available Stock Qty</th>
						</tr>
					</thead>
					<tbody></tbody>
					<tfoot>
						<tr>

							<td class="text-right grand-total-text">Total</td>
							<td class="text-right grand-total-amount" id="closing_stock_total" style="padding-right: 15px;">0</td>
						</tr>
					</tfoot>
				</table>
	        </div>
		    <div class="modal-footer">
			    <span class="btn btn-cancel" data-dismiss="modal">Close</span>
		    </div>
	    </div>
    </div>
</div>

<div id="make_model" class="modal fade" role="dialog">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title"></h4>
			</div>
			<div class="modal-body">
				<input type="hidden" id="id_make_title" value="">
				<div class="col-sm-6">
					<label>Manufacturer Part Number</label>
			        <input type="text" class="form-control" id="id_manufacture_value" placeholder="Enter Manufacturer Part Number" value="" maxlength="25">
				</div>
				<div class="col-sm-6" hidden="hidden">
					<label>Standard Packing Qty</label>
					<input type="text" class="form-control" id="id_packing_value" placeholder="Enter Standard Packing Qty" onfocus="setNumberRangeOnFocus(this,12,3); setCurrentValueToData(this);" value="">
				</div>
		    </div>
		    <div class="modal-footer">
			    <input type="button" id="make_model_save" class="btn btn-save" value="OK">
			    <span class="btn btn-cancel" data-dismiss="modal">Close</span>
		    </div>
		</div>
	</div>
</div>

<div id="moq_validation_modal" class="modal fade" role="dialog" data-backdrop="static" data-keyboard="false">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">Select Appropriate MOQ</h4>
			</div>
			<div class="modal-body">
				<input type="hidden" id="moq_make_value" />
				<div class="text-center">
					<h5>At least one MOQ profiled earlier breaks the new SPQ factor.<br /><br />
    				Kindly select such MOQs to be consistent with the SPQ factors.</h5>
    			</div>
				<table class="table table-bordered table-striped custom-table" id="table_moq_validation">
					<thead>
						<tr>
							<th>MOQ</th>
							<th>Options</th>
						</tr>
					</thead>
					<tbody>

					</tbody>
				</table>
		    </div>
		    <div class="modal-footer">
			    <input type="button" id="update-moq-value" onclick="updateMoqValue()" class="btn btn-save" disabled="disabled" value="OK">
			    <span class="btn btn-cancel" onclick="cancelUpdateMoqValue()">Close</span>
		    </div>
		</div>
	</div>
</div>

<script  type="text/javascript">
async function fetchMaterialData() {
    const item_id = document.getElementById("id_material-material_id").value;
    const enterprise_id = document.getElementById("id_material-enterprise_id").value;
    const csrfToken = getCookie('csrftoken');
    const requestOptions = {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded', 'X-CSRFToken': csrfToken},
        body: "item_id="+ item_id +"&enterprise_id=" + enterprise_id
    };
    if(item_id != ''){
    	if(sessionStorage.getItem('mdata') != null){
            sessionStorage.removeItem('mdata');
        }
        let response = await fetch('/erp/masters/json/material/detail/', requestOptions)
	    let data = await response.json();
	    sessionStorage.setItem('mdata', JSON.stringify(data));
	    return data;
    }
    return '';

}
</script>
<script src="/site_media/js/react/react.development.js" crossorigin></script>
<script src="/site_media/js/react/react-dom.development.js" crossorigin></script>
<script src="/site_media/js/react/babel.min.js"></script>

<script type="text/babel" src="/site_media/js/add_material_react_component.js?v={{ current_version }}"></script>
<script type="text/javascript">
	$(document).ready(function() {
		if($('#id_material-material_id').val() !=""){
			if($("#is_super_user").val().toLowerCase() == 'false') {
				$("#id_material-drawing_no").attr("readonly", true);
			}
		}

		var urleditcheck = $(location).attr("href");
		if(urleditcheck.search("edit") != -1){
			$("#material-attachment-icon").show();
		}
		else {
			$("#material-attachment-icon").hide();
			$(".div_price_profile_container, .div_stock_container").addClass("hide");
		}
		PriceStatusChangeEvent();
		$("#id_material-effect_since").datepicker("setDate", new Date());

		setTimeout(function(){
			var selDate = $("#id_material-effect_since").val();
			$("#effect_till_date").datepicker("setStartDate", new Date(selDate));
			$("#supp_price_table").find(".effect_since_child").each(function(){
	            var selDate = $(this).prev("input").val();
	            $(this).closest(".effect_date_parent").find(".effect_till_child").datepicker("setStartDate", new Date(selDate));
	        })
		},3000);
		$(".supplier-removal-icon").click(function(){
			$(".supplier-removal-icon").addClass("hide");
			$("#supplier_no_selected").val("");
            $("#supplier_selected").val("");
            $("#id_price_material-__prefix__-supplier_select_field").val("");
            $("#id_price_material-__prefix__-supp_id").val("");
            $("#id_price_material-__prefix__-price").val("0.00");
            $("#currency_unit_display").html("&nbsp;");
		})
		ValidateEffectDate();
		validateNewAttachment();
		materialAttachmentSubmit();
		listAttachments();
		validateFileType();
		fetchMaterialData();
		isStockableChanged();
		isInuseChanged();
		materialTypeToggleInit();
	});

	function isStockableChanged() {
		$("#id_material-is_stocked").change(function(){
			if($("#id_material-material_id").val() != "") {
				swal({
		            title: "Please Confirm",
		            text: "Modifying the Stockable flag will affect the Stock values featuring in Financial Accounting Reports like P&L, Balance Sheet and the general Stock Dashboard. <br /><br />Do you wish to continue? ",
		            type: "warning",
		            showCancelButton: true,
		            confirmButtonColor: "#209be1",
		            confirmButtonText: "Yes!",
		            closeOnConfirm: true
		        },
		        function(isConfirm){
		            if(!isConfirm) {
		            	if($("#id_material-is_stocked").is(":checked")) {
		            		$("#id_material-is_stocked").prop("checked", false);
		            	}
		            	else {
		            		$("#id_material-is_stocked").prop("checked", true);
		            	}
		            }
		        });
			}
		})
	}

	function isInuseChanged() {
		$("#id_material-in_use").change(function(){
			if(!$(this).is(":checked")) {
				var itemId = document.getElementById("id_material-material_id").value;
				var materialName = document.getElementById("id_material-name").value;
	            $.ajax({
	                url: "/erp/masters/json/materials/checkstock/",
	                type: "POST",
	                dataType: "json",
	                data: {item_id:itemId},
	                success: function (json) {
	                    if (json['stock_qty'] > 0 || json['p_bom_count'] > 0) {
	                        if (json['stock_qty'] > 0){
	                            swal_text = "Cannot delete Item: <span style='color:#209be1'>" + materialName + "</span>, as it is still available in stock!"
	                        }else if (json['p_bom_count'] > 0){
	                            swal_text = "Cannot be marked as Not-In-Use: <span style='color:#209be1'>" + materialName + "</span> is part of BoM.!"
	                        }
	                        $("#id_material-in_use").prop("checked", true)
	                        swal({
	                            title: "",
	                            text: swal_text,
	                            type: "warning",
	                            showCancelButton: false,
	                            confirmButtonColor: "#209be1",
	                            confirmButtonText: "OK",
	                            closeOnConfirm: true
	                        });
	                        return;
	                    }
	                },
	                error: function (xhr, errmsg, err) {
	                    console.log(xhr.status + ": " + xhr.responseText);
	                }
	            });
	        }
		})
	}

	function cancelUnitAdd() {
		$("#add_new_unit_modal").modal('hide');
		$("#addNewUnit").removeClass('btn-processing').text("Add");
	}

	function showUploadMaterialModal() {
	    $("#upload_material_modal").modal('show');
	    $("#materialhideUploadButton").attr("disabled", true);
	    $("#upload_material_modal").find(".error-border").removeClass('error-border');
	    $("#upload_material_modal").find(".custom-error-message").remove();
	    var itemName = $('#id_material-name').val();
	    $(".attachment-title").text(itemName);
	    $('#upload_material_modal').on('hidden.bs.modal', function () {
	    	$("#attachmentupload").filestyle('clear');
	    	$("#document_label").val("");
	    });
	    getGCSKeyDataMaterial();
	}

	function getBomParentId(){
		var material_id = $("#id_material-material_id").val();
		var parent_id= []
        $.ajax({
            url: "/erp/masters/json/materials/get_bom_parentid/",
            type: "POST",
            dataType: "json",
            data: {material_id: material_id},
            success: function (response) {
                $.each(response, function(i, item) {
                    parent_id.push(item.item_id);
                });
                sessionStorage.setItem("parent_mat_list", JSON.stringify(parent_id));
            }
         });
	}

	function closeMaterialUpload() {
		$("#attachmentupload").filestyle('clear');
		$("#upload_material_modal").modal("hide");
		$("body").css({paddingRight: "17px"});
		setTimeout(function(){
			$( window ).resize();
		},500);
	}

	function PriceStatusChangeEvent() {
		$(".material_price_status").find("select").change(function(){
			var tr = $(this).closest('tr').next().next('tr.reject_remarks')
			if($(this).val() == "-1") {
				tr.removeClass('hide');
			}
			else {
				tr.addClass('hide');
			}
		});
	}

	function validateFileType(){
		$("#attachmentupload").change(function(){
			var fileExtension = ['csv', 'xls', 'xlsx', 'doc', 'docx', 'pdf', 'jpg', 'jpeg', 'png', 'svg', 'gif', 'txt', 'log', 'md', 'json'];
			if($("#attachmentupload").val()) {
		    	if(this.files[0].size/(1024*1024) > 16){
		    		$("#materialhideUploadButton").attr("disabled", true);
		    		swal("File Size Exceeds", "Please select a lower size file. Maximum file size limit is 16MB", "warning");
		    		setTimeout(function(){
		                $("#attachmentupload").filestyle('clear');
		    	   },200);
		    	}
		    	else {
		    		$("#materialhideUploadButton").removeAttr("disabled");
		    	}
		    }
		});
	}

	function deleteAttachmentFile(item_id, description, attachment_id) {
		var material_name = $("#searchResult").find("input[value='"+item_id+"']").closest("td").text();
	  	swal({
			title: "Are you sure?",
			text: "You want to remove the attachment <b>"+description+"</b>",
			type: "warning",
			showCancelButton: true,
			confirmButtonColor: "#209be1",
			confirmButtonText: "Yes, delete it!",
			closeOnConfirm: true
		},
		function(isConfirm){
			if(isConfirm){
		    	deleteMaterialAttachment(item_id, attachment_id);
		    }
		});
	}

	function deleteMaterialAttachment(item_id, attachment_id){
	 	$.ajax({
	        url : "/erp/masters/json/material/delete_material_attachment/",
	        type : "POST",
	        dataType: "json",
	        data : {
	            item_id: item_id,
	            attachment_id: attachment_id
	        },
	        success : function(data) {
	         	if(data.response_code == 400){
	                swal({title: "Not deleted.", text: "Attachment not saved successfully", type: "danger"});
				}
				$('#loadingmessage_material_attachment').hide();
				listAttachments();
	        },error : function(xhr,errmsg,err) {
	            console.log(xhr.status + ": " + xhr.responseText);
	            $("#loading").addClass('hide').removeClass('show');
	        }
	    });
	}

	function validateNewAttachment(){
		$("#materialhideUploadButton").click(function(){
			$(".error-border").removeClass('error-border');
		    $(".custom-error-message").remove();

		     var ControlCollections = [
		        {
		            controltype: 'textbox',
		            controlid: 'document_label',
		            isrequired: true,
		            errormsg: 'Description is required.'
		        },
		        {
		            controltype: 'file',
		            controlid: 'attachmentupload',
		            isrequired: true,
		            errormsg: 'Attachment is required.'
		        }
			];
			var result = JSCustomValidator.JSvalidate(ControlCollections);
			if(result){
				ajaxMaterialSubmit();
			}
			return result;
		});
	}

	function inlineAttachmentUpload(materialName, itemId){
		$("#materials_attachment_container").find(".document_attachment_part").remove();
		$('#id_material-material_id').val(itemId);
		$('#id_material-name').val(materialName);
		showUploadMaterialModal();
		listAttachments();
	}
	function getGCSKeyDataMaterial(){
	    var field_set_html = "";
	    return $.ajax({
	            url: '/erp/commons/json/get_gcs_security_key/',
	            type: "post",
	            dataType: "json",
	            data:{},
	            success: function(response) {
	                $.each(response['policy']['fields'], function(key, value){
	                    field_set_html += "  <input name='"+ key +"' value='"+ value +"' type='hidden'/>\n";
	                });
	                document.getElementById('material_attachment_uploading').action = response['policy']['url'];
	                $("#material_attachment_uploading").prepend(field_set_html);
	            },
	            error: function (xhr, errmsg, err) {
	                swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
	            }
	       });
	}

	function ajaxMaterialSubmit(element, callback=function(element, boolean1){}){
	    var form = document.querySelector('form#material_attachment_uploading');
	    var data = new FormData(form);
	    var url = $('form#material_attachment_uploading').attr('action');
	    $.ajax({
	       type: "POST",
	       url: url,
	       data: data,
	       //  below should be false for GCS file upload
	       contentType: false,
	       processData: false,
	       success: function(data)
	       {
	            console.log('Hurray file uploaded successfully');
	            callback(element, true);
	       },
	       error : function(xhr,errmsg,err) {
		        console.log(xhr.status + ": " + xhr.responseText);
		        callback(element, false);
		   }
	    });
	}

	function materialAttachmentSubmit(){
		$("#material_attachment_uploading").submit(function(e){
			e.preventDefault();
			ajaxMaterialSubmit(this, callback=materialAttachementSubmitSuccess);
		    $("#materialhideUploadButton").attr("disabled", true);
		});
	}

	function materialAttachementSubmitSuccess(element, isUploaded){
		if(isUploaded) {
			$('#loadingmessage_material_attachment').show();
		    var formData = new FormData(element);
		    formData.append('item_id', $('#id_material-material_id').val());
		    formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');
		    formData.append('gcs_key', $("#material_attachment_uploading input[name=key]").val());
		    formData.append('label', $("#document_label").val());
		    setTimeout(function(){
		        $.ajax({
			        url:"/erp/masters/json/material/material_attachment_upload/",
			        type: 'POST',
			        data: formData,
			        cache: false,
			        contentType: false,
			        processData: false,
			        success: function (data) {
			            $('#loadingmessage_material_attachment').hide();
			            listAttachments();
				        $("#attachmentupload").filestyle('clear');
				        $("#document_label").val('');
				        if (data.response_code == 200) {
				            swal({
								title: "Attachment Saved Successfully",
								text: "Your Attachment has been saved successfully",
								type: "success",
								confirmButtonColor: "#209be1",
								confirmButtonText: "Ok",
								closeOnConfirm: true
							});
						}
						setTimeout(function(){
	                         reset_upload_html = `<div class="col-md-12 form-group" style="width: 500px;">
					                <input type="file"  class="filestyle col-sm-12" data-buttonBefore="true" name="file" id="attachmentupload"/>
					            </div>
						        <div class="text-right" style="margin-right: 15px;">
							        <input type='submit' id="materialhideUploadButton" class="btn btn-save" value="Add" disabled="true"/>
								</div>`;
	                            $('#material_attachment_uploading').html(reset_upload_html);
	                            getGCSKeyDataMaterial();
	                            validateFileType();
	                            validateNewAttachment();
	                    },10);
	                    listAttachments();
			        },
			        error : function(xhr,errmsg,err) {
			            console.log(xhr.status + ": " + xhr.responseText);
			        }
			    });
			},500);
		}
		else {
			swal({
				title: "Upload Failed",
				text: "Your Attachment has failed to save.",
				type: "error"
			});
			setTimeout(function(){
                 reset_upload_html = `<div class="col-md-12 form-group" style="width: 500px;">
		                <input type="file"  class="filestyle col-sm-12" data-buttonBefore="true" name="file" id="attachmentupload"/>
		            </div>
			        <div class="text-right" style="margin-right: 15px;">
				        <input type='submit' id="materialhideUploadButton" class="btn btn-save" value="Add" disabled="true"/>
					</div>`;
                    $('#material_attachment_uploading').html(reset_upload_html);
                    getGCSKeyDataMaterial();
                    validateFileType();
                    validateNewAttachment();
            },10);
            listAttachments();

		}
	}

	function listAttachments(){
		var item_id = $('#id_material-material_id').val();
		$('#loadingmessage_material_attachment').show();
		if (item_id == null) {
			return;
		}
		$.ajax({
	        url : "/erp/masters/json/material/material_attachment_list/",
	        type : "POST",
	        dataType: "json",
	        data : {
	            item_id: item_id
	        },
	        success : function(data) {
		        $("#materials_attachment_container").find(".document_attachment_part").remove();
		        $('#loadingmessage_material_attachment').hide();
	            $.each( data, function( key, value ) {
				  	addAttachmentMaterial(value.item_id, value.description, value.attachment_id, value.file, value.enterprise_id, value.ext, value.file_base64);
				});
	        },
	        error : function(xhr,errmsg,err) {
	            console.log(xhr.status + ": " + xhr.responseText);
	        }
	    });
	}

	function addAttachmentMaterial(item_id, description, attachment_id, file, enterprise_id, extension, base64) {
		var cls_base64 = (base64 !=''? 'base64' : '');
		var isDocsvisible = "";
		if($("#is_access_level_edit").val() != 1) {
			isDocsvisible = "hide";
		}

		var row = `	<div class="document_attachment_part" data-tooltip="tooltip" title="${description}" >
						<span class="document_remove ${isDocsvisible}" onclick="deleteAttachmentFile('${item_id}', '${description}', '${attachment_id}');"><i class="fa fa-times" aria-hidden="true"></i></span>
						<div >
						<form action="/erp/commons/json/document/" method="post" target="_blank">
							<input type="hidden" class="base64-file-value" data-filename="${description}.${extension}" value="${base64}">
							<input type="hidden" name="document_name" value="${description}.${extension}"/>
							<input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}" />
							<input type="hidden" name="document_uri" value="${file}"/>
							<span class="document_attachment attached-file file_${extension.toLowerCase()} linked_text ${cls_base64}" data-extension="${extension.toLowerCase()}" data-url = "${file}" onclick="viewAttachedDocument(this)">${extension.toUpperCase()}</span>
							<span class="document_text">${description}</span>
						</form>
						</div>
					</div>`;

		$("#materials_attachment_container").append(row);
		TooltipInit();
		$("#attachmentupload").filestyle({buttonBefore: true});
	}

	function ValidateEffectDate(){
		$('.effect_since_child').on('hide.datepicker', function(ev, picker) {
			var selectedDate = $(this).prev("input").val();
			$(this).closest(".effect_date_parent").find(".effect_till_child").datepicker("setStartDate", new Date(selectedDate));
			var fit_end_time = $(this).closest(".effect_date_parent").find(".effect_till_child").prev("input").val();
			if(new Date(selectedDate) > new Date(fit_end_time)) {
				$(this).closest(".effect_date_parent").find(".effect_till_child").prev("input").val("");
				$(this).closest(".effect_date_parent").find(".effect_till_child").val("");
			}
		});
	}

	function validateBOMDownload(tableThis) {
		if($("#materialtable_bom tbody").find("tr.newly_added_item:visible").length <= 0) {
		    var fileName = $("#id_material-drawing_no").val();
		    if(fileName == "") {
		        fileName  = $("#id_material-name").val();
		    }
			GeneralExportTableToCSV.apply(tableThis, [$('#materialtable_bom'), `${fileName}-BoM.csv`]);
		}
		else {
			swal("Unable to download", "Newly added item(s) are yet to be saved. Kindly save this material to download the complete BoM.", "warning");
		}
	}

	function costingBOMDownload(tableThis) {
	    var fileName = $("#id_material-drawing_no").val();
        if(fileName == "") {
            fileName  = $("#id_material-name").val();
        }
		if($("#cheapest_supplier_bom tbody").find("tr.newly_added_item:visible").length <= 0) {
			GeneralExportTableToCSV.apply(tableThis, [$('#cheapest_supplier_bom'), `${fileName}-CostingBoM.csv`]);
		}
		else {
			swal("Unable to download", "Newly added item(s) are yet to be saved. Kindly save this material to download the complete BoM.", "warning");
		}
	}

	function editMaterialFromBom(drawingNumber){
		$("#edit_material_from_bom").find("#hdn_edit_material_from_bom").val(drawingNumber);
		$("#edit_material_from_bom").submit();
	}

	function appendMaterialTableBOM(cat_code,dataParent){
	    var currentCatChild = $("#"+dataParent).attr('id');
	    var constructedRow = '';
	    var current_bom_sno = $("#"+dataParent).find(".bom-sno").text().trim();
	    if($("#"+currentCatChild).attr('data-toggle') == 'open') {
	        var curPadding = $("#"+currentCatChild).attr('data-padding');
	        $("#"+currentCatChild).nextAll('tr').each(function(){
	            if(Number($(this).attr('data-padding')) > Number(curPadding)) {
	                $(this).remove();
	            }
	            else {
	                return false;
	            }
	        });
	        $("#materialtable_bom #"+dataParent).find('.fa.fa-for-arrow').removeClass('fa-minus').addClass('fa-plus');
	        $("#materialtable_bom #"+dataParent).attr('data-toggle','close');
            $("#"+dataParent).find(".bom-delete").removeClass('invisible').addClass('visible');
	    }
	    else {
	        $("#materialtable_bom #"+dataParent).find('.fa.fa-for-arrow').removeClass('fa-plus').addClass('fa-minus');
	        $("#materialtable_bom #"+dataParent).attr('data-toggle','open');
			$("#"+dataParent).find(".bom-delete").removeClass('visible').addClass('invisible');
			$("#loadingmessage_changelog_listing_ie").show();
		    $.ajax({
				url: "/erp/sales/json/bom_catalogue_materials/",
				type: "post",
				datatype: "json",
				data: {cat_code:cat_code},
				success: function (response) {
					try {
						if(response.length != 0) {
							$.each(response, function(i, item) {
								var drawingNo = "";
	                            var dataPadding = Number($("#"+dataParent).data('padding') + 30);
								if(item.drawing_no != "" && item.drawing_no != null) {
									drawingNo = item.drawing_no;
								}
								makes=[]
								if (item.makes.length > 0) {
											$.each(item.makes, function(i, make) {
											makes.push(make.make_name)
											});
								}
								if (item.material_type == true && item.is_service== false)
								{ classname='' }
								else if(item.is_service== true)
								{
									 classname='service-item-flag'
								}
								else{ classname='non_stock-flag' }
								if(item.hasChildren) {
								var childArrow = "<i class='fa fa-plus fa-for-arrow' role='button' onclick='return appendMaterialTableBOM(\""+ item.item_id +"\",\""+ dataParent +'-'+ i +"\")'></i><a style='padding-left: 26px; display: block; margin-top: -12px;' onclick='return editMaterialFromBom(\""+ item.item_id +"\")'>"+item.name+"<span class='"+classname+"'></span></a></i>"
								}
								else {childArrow = "<a style='padding-left: 8px;' onclick='return editMaterialFromBom(\""+ item.item_id +"\")'>"+item.name+"<span class='"+classname+"'></span></a>"}
								var row = `	<tr data-toggle='close' data-padding='${ dataPadding }' data-parent= "${ item.cat_code }" id= "${ dataParent }-${ i }" data-child="${ item.cat_code }">
					                        <td class='text-left bom-sno'>${current_bom_sno}.${(i + 1)}</td>
				                            <td style='padding-left:${Number(dataPadding-20)}px'><span style='padding-left:30px; display: block;' class='tree-view'>${childArrow}</span></td>
				                            <td align='left'>${drawingNo}</td>
					                        <td align='right' class='item-qty'>${(item.quantity).toFixed(3)}</td>
					                        <td align='left' width="55px">${item.unit_name}</td>
											<td width="20px" class="text-center"></td>
				                        </tr>`;
				                constructedRow = constructedRow+row
							});
							$('#materialtable_bom  #'+dataParent).after(constructedRow);
						}
						$("#loadingmessage_changelog_listing_ie").hide();
					}
					catch (Exception) {
					   console.log("Exception:" + Exception);
					}
				},
				error: function (xhr, errmsg, err) {
		            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
		        }
			});
		}
	}

	function updateSupplierPrice(priceProfileId, access_status){
		if($("#"+priceProfileId+"_effect_till").next("a").find("i").hasClass("fa-pencil")) {
			if (access_status != 'True'){
				$("#id_"+priceProfileId+"-status").val('0')
				$("#id_"+priceProfileId+"-status_select").val('0')
				$("#id_"+priceProfileId+"-status").closest("tr").find(".supplier_price_status").text("Pending")
			}
			$("#"+priceProfileId+"_effect_till").removeClass("hide").next("a").find("i").removeClass("fa-pencil").addClass("fa-times").css({color: "#dd4b39"});
		}
		else {
			$("#id_"+priceProfileId+"-status").val('1')
			$("#id_"+priceProfileId+"-status_select").val('1')
			$("#id_"+priceProfileId+"-effect_till").val('')
			$("#effect_from_since_"+priceProfileId).val("")
			$("#id_"+priceProfileId+"-status").closest("tr").find(".supplier_price_status").text("Approved")
			$("#"+priceProfileId+"_effect_till").addClass("hide").next("a").find("i").removeClass("fa-times").addClass("fa-pencil").css({color: "#337ab7"});
		}
	}



function openPriceProfileRemarks(current) {
	$(current).closest("tr").next("tr.general-remarks").toggleClass("hide");
}

function validateMoqFactor(current, makeId) {
	var currentSpq = $("#id_price_material-__prefix__-make_choices option[value='"+makeId+"']").attr("data-spq");
	var profiledSqpList = [];
	$("#supp_price_table").find(".effect_date_parent").each(function(){
		if($(this).attr("id") != "price_material-__dummy__") {
			var loopMakeId = $(this).find("input[name*='make_id']").val();
			if(loopMakeId == makeId) {
				var loopMoqValue = Number($(this).find("input[name*='moq']").val());
				if ($.inArray(loopMoqValue, profiledSqpList) == -1 && loopMoqValue > 0) {
					profiledSqpList.push(loopMoqValue)
				}
			} 
		}
	});
	$("#table_moq_validation tbody").html("");
	profiledSqpList.forEach(function(item, index){
		var ceilValue = moq_round_ceil($(current).val(), item) ;
        var floorValue = moq_round_floor($(current).val(), item);
        if(ceilValue != floorValue) {
			var buttons = ` <span class="btn transparent-btn set-moq-btn" onclick="updateIndvMoqValue(${floorValue}, ${item}, this);" style='width: 100px;'>${floorValue}</span>&nbsp;&nbsp;&nbsp;	
							<span class="btn transparent-btn set-moq-btn" onclick="updateIndvMoqValue(${ceilValue}, ${item}, this);" style='width: 100px;'>${ceilValue}</span>`;
			var row = ` <tr>
							<td class='text-center td-old-moq'>${item}</td>
							<td class='text-center'>${buttons}</td>
						</tr>`;
			$("#table_moq_validation tbody").append(row);			
		}
	});
	if($("#table_moq_validation tbody tr").length >= 1) {
		$("#moq_validation_modal").modal("show");
		$("#moq_make_value").val(makeId);
		$("#update-moq-value").attr("disabled", "disabled")
	}
	else {
		if(makeId == 1) {
			$("#id_price_material-__prefix__-make_choices").find(`option[value='${makeId}']`).attr("data-spq", $("#id_material-standard_packing_qty").val());
		}
		else {
			$("#id_price_material-__prefix__-make_choices").find(`option[value='${makeId}']`).attr("data-spq", $("#id_packing_value").val());
		}
	}
}

function updateIndvMoqValue(moqValue, value, current) {
	$(current).closest("tr").find(".set-moq-btn").removeClass("moq-active");
	$(current).addClass("moq-active");
	if($("#table_moq_validation tbody tr").length == $("#table_moq_validation tbody").find(".moq-active").length) {
		$("#update-moq-value").removeAttr("disabled");
	}
}

function updateMoqValue() {
	var makeId = $("#moq_make_value").val();
	$("#table_moq_validation tbody tr").each(function(){
		var oldMoqValue = Number($(this).find(".td-old-moq").text().trim());
		var newMoqValue = Number($(this).find("span.set-moq-btn.moq-active").text().trim());
		$("#supp_price_table").find(".effect_date_parent").each(function(){
			if($(this).attr("id") != "price_material-__dummy__") {
				var loopMakeId = $(this).find("input[name*='make_id']").val();
				if(loopMakeId == makeId) {
					if(Number($(this).find('input[name*="moq"]').val()) == oldMoqValue) {
						$(this).find('input[name*="moq"]').val(newMoqValue.toFixed(3));
					}
				}
			}
		});
	});
	$("#moq_validation_modal").modal("hide");
	$("#moq_make_value").val("");
	if(makeId == 1) {
		$("#id_price_material-__prefix__-make_choices").find(`option[value='${makeId}']`).attr("data-spq", $("#id_material-standard_packing_qty").val());
	}
	else {
		$("#id_price_material-__prefix__-make_choices").find(`option[value='${makeId}']`).attr("data-spq", $("#id_packing_value").val());
		$("#make_model_save").click();
	}
}

function cancelUpdateMoqValue() {
	var makeId = $("#moq_make_value").val();
	$("#moq_validation_modal").modal("hide");
	$("#moq_make_value").val("");
	if(makeId == 1) {
		$("#id_material-standard_packing_qty").val($("#id_material-standard_packing_qty").attr("data-default-value"));	
	}
	else {
		$("#id_packing_value").val($("#id_packing_value").attr("data-default-value"));
	}
}

function materialTypeToggleInit(){
	if($("#id_material-is_service").is(":checked")) {
	    $('#switch_material_service a[data-title="2"]').removeClass('noActive').addClass('active');
	}
	else {
		$('#switch_material_service a[data-title="1"]').removeClass('noActive').addClass('active');
	}
	$("#switch_material_service a").on('click', function(){
	    var selected = $(this).data('title');
	    $('#switch_material_service a').not('[data-title="'+selected+'"]').removeClass('active').addClass('noActive');
	    $('#switch_material_service a[data-title="'+selected+'"]').removeClass('noActive').addClass('active');
	    if(selected == 1) {
	        $("#id_material-is_service").prop("checked", false);
	        $("#id_material-is_stocked").prop("checked", true);
	        $(".for_goods").removeClass('hide');
	        $(".material-supporting-container").addClass('hide');
	        $(".service_item_code").find("label").text("Drawing Number");
	        $("#id_material-drawing_no").attr("placeholder", "Enter Drawing Number");
	        $("#tariff_no").find("label").text("HSN");
	        $("#makes").addClass('hide');
			$(".service_add_new_material").text("To add new Category/ Unit, go to Profile -> Goods Page.")
	    }
	    else if(selected == 2) {
	        $("#id_material-is_service").prop("checked", true);
	        $("#id_material-is_stocked").prop("checked", false);
	        $("#id_material-standard_packing_qty").val('0.00')
	        $("#id_material-location").val("")
	        $("#id_material-minimum_stock_level").val('0.00')
            $(".for_goods").addClass('hide');
            $(".service_item_code").find("label").text("Item Code");
            $("#tariff_no").find("label").text("SAC");
            $("#id_material-drawing_no").attr("placeholder", "Enter Item Code");
            $(".service_add_new_material").text("To add new Category/ Unit, go to Profile -> Service Page.")
	    }
	});
}
function maskPriceProfile(current, type){
		var price = $(current).closest("tr").find($('[id$=-price]')).val();
		var item_id = $(current).closest("tr").find($('[id$=item_id]')).val();
		var make_id = $(current).closest("tr").find($('[id$=make_id]')).val();
		var supp_id = $(current).closest("tr").find($('[id$=supp_id]')).val();
		var effect_since = $(current).closest("tr").find($('[id$=effect_since]')).val();
		var effect_till = $(current).closest("tr").find($('[id$=-effect_till]')).val();
		var status = $(current).closest("tr").find($('[id$=status]')).val();
		if (item_id == null) {
			return;
		}
		$.ajax({
	        url : "/erp/masters/json/materials/mask_supplier_price/",
	        type : "POST",
	        dataType: "json",
	        data : {item_id: item_id, make_id:make_id, supp_id:supp_id, effect_since:effect_since, effect_till:effect_till,
	            price:price, status:status, type:type
	        },
	        success : function(response) {
	            if (response.response_message =="Success") {
	                if(type === "mask"){
						$(current).closest("tr").find(".price_profile_mask").find("i").removeClass("fa-eye").addClass("fa-eye-slash");
						$(current).closest("tr").find(".price_profile_mask").attr('data-original-title', 'Unmask Price');
						$(current).closest("tr").find(".price_profile_mask").attr('onclick', 'maskPriceProfile(this, "unmask");');
						swal({title: "", text: "Price Profile Masked Successfully", type: "success"});
					}
					else {
						$(current).closest("tr").find(".price_profile_mask").find("i").addClass("fa-eye").removeClass("fa-eye-slash");
						$(current).closest("tr").find(".price_profile_mask").attr('data-original-title', 'Mask Price');
						$(current).closest("tr").find(".price_profile_mask").attr('onclick', 'maskPriceProfile(this, "mask");');
						swal({title: "", text: "Price Profile Unmasked Successfully", type: "success"});
					}
                }
                else {
                    swal({title: "", text: response.error, type: "error"});
               }
	        },
	        error : function(xhr,errmsg,err) {
	            console.log(xhr.status + ": " + xhr.responseText);
	        }
	    });
	}

		var locationWiseStkQty = {
        "is_faulty": 0,
        "item_id": [$('#id_material-material_id').val()],
        "invoice_id": ""
    };

    $('.available_stock_container, .available_stock_container_faulty').on('click', function() {
        if ($(this).hasClass('available_stock_container_faulty')) {
            locationWiseStkQty.is_faulty = 1;
            $('#modalTitle').text('Faulty Stock');
        	$('#stockQtyHeader').text('Faulty Stock Qty');
        } else {
            locationWiseStkQty.is_faulty = 0;
            $('#modalTitle').text('Available Stock');
        	$('#stockQtyHeader').text('Available Stock Qty');
        }
    });

    $('#available_stock_modal').on('show.bs.modal', function () {
		$("#loading").show();
        $.ajax({
            url: '/erp/stores/json/issue/location_wise_closingstock/',
            method: 'POST',
            dataType: 'json',
            data: {
                "material": JSON.stringify(locationWiseStkQty),
                enterprise_id: "102"
            },
            success: function(response) {
				var totalStock = 0;
				$('#supp_material_table tbody').empty();

				$.each(response.data, function(index, item) {
					$.each(item.closing_stocks, function(key, value) {
						totalStock += value.qty;
						$('#supp_material_table tbody').append(
							`<tr>
								<td class='text-center'>${value.location_name}</td>
								<td class='text-right'>${value.qty}</td>
							</tr>`
						);
					});
				});

				$('#closing_stock_total').text(totalStock);
				$("#loading").hide();
			},
            error: function(xhr, status, error) {
                console.error('Error fetching data:', error);
            }
        });
    });
</script>
