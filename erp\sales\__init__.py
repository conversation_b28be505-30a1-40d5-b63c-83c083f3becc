from __future__ import absolute_import
from erp.celery_config import app as celery_app

import logging

__all__ = [
    'celery_app'
]

logger = logging.getLogger(__name__)

# OA list: captured based on relationship variables in model
OA_EXCLUDE_FIELD_LIST = (
	'items', 'non_stock_items', 'enterprise', 'customer', 'tags', 'approver', 'currency', 'documents', 'revisions',
	'modifier', 'super_modified_user', 'invoice_materials', 'invoice_ns_materials', 'grn_items', 'grn_non_stock_items',
	'remarks')

# OAParticulars list: captured based on relationship variables in model
OA_PARTICULARS_EXCLUDE_FIELD_LIST = ('oa', 'item', 'make')


SE_EXCLUDE_FIELD_LIST = (
	'id', 'se_no', 'status', 'prepared_by', 'prepared_on', 'last_modified_by', 'last_modified_on', 'approved_by',
	'approved_on', 'super_modified_by', 'super_modified_on', 'items', 'enterprise', 'customer', 'tags', 'taxes', 'approver', 'currency', 'documents', 'revisions',
	'modifier', 'super_modified_user', 'se_contacts', 'se_attachments', 'remarks', '_sa_instance_state')

SE_PARTICULAR_EXCLUDE_FIELD_LIST = ('se_id', 'unit_rate', 'item', 'make', '_sa_instance_state')

SE_TAX_EXCLUDE_FIELD_LIST = ('se', 'se_id', 'tax', '_sa_instance_state')

INVOICE_EXCLUDE_FIELD_LIST = ('items', 'taxes', 'charges', 'tags')
