import json

from django.http import HttpResponseRedirect
from django.template.response import TemplateResponse

from erp.auth import USER_IN_SESSION_KEY, ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from erp.expenses import *
from erp.expenses.backend import ExpenseVO, ExpenseService
from erp.properties import MANAGE_EXPENSES_TEMPLATE, MANAGE_EXPENSES_URL, TEMPLATE_TITLE_KEY
from settings import GCS_PUBLIC_URL

__author__ = 'saravanan'


def manageExpense(request, expense_id=None, expense_vo=None):
	"""
	Main page to manage Expense - Add, Search, Edit and Delete a Bill of expense
	:param request:
	:param expense_id:
	:param expense_vo:
	:return:
	"""
	rh = RequestHandler(request)
	enterprise_id = rh.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	user_id = rh.getSessionAttribute(USER_IN_SESSION_KEY).user_id
	title = "Expense"
	badge_hit = False
	from_date = str(rh.getPostData('expenses_fromdate')) if rh.getPostData('expenses_fromdate') is not None else None
	status = str(rh.getPostData('expenses_status')) if rh.getPostData('expenses_status') is not None else None
	if rh.getPostData('expenses_fromdate') is not None:
		badge_hit = True

	if not expense_vo:
		expense_service = ExpenseService()
		expense_vo = expense_service.getExpenseVO(enterprise_id=enterprise_id, user_id=user_id, expense_id=expense_id)
		if expense_id:
			title = expense_vo.expense_code
		attachment = {}
		for index, item in enumerate(expense_vo.expense_particulars_formset):
			if item.initial['document'] != "" and item.initial['document'] != None and is_json(item.initial['document']):
				item_attachment = json.loads(str(item.initial['document']))
				item.initial['document'] = str(item.initial['document'])
				item.initial['document_base64'] = "{url}/{key}".format(url=GCS_PUBLIC_URL, key="{0}/{1}".format(enterprise_id, item_attachment['uid']))
				attachment[index] = {'data': "{url}/{key}".format(url=GCS_PUBLIC_URL, key="{0}/{1}".format(enterprise_id, item_attachment['uid'])), 'name': item_attachment['name'], 'ext': item_attachment['ext']}
	save_expense_response = rh.getSessionAttribute("save_expense_response")
	rh.setSessionAttribute("save_expense_response", "")

	return TemplateResponse(
		template=MANAGE_EXPENSES_TEMPLATE, request=request, context={
			'enterprise_id': enterprise_id, 'user_id': user_id, 'remarks_list': json.dumps(expense_vo.remarks_list),
			'claimed_amount': expense_vo.claimed_amount, 'approved_amount': expense_vo.approved_amount,
			'from_date': from_date, 'status': status, 'badge_hit': badge_hit,
			'save_expense_response': save_expense_response if save_expense_response else "",
			'attachment': json.dumps(attachment),
			EXPENSE_FORM_HEAD_KEY: expense_vo.expense_form,
			EXPENSE_FORMSET_ITEM_KEY: expense_vo.expense_particulars_formset,
			EXPENSE_FORMSET_TAG_KEY: expense_vo.expense_tag_formset, TEMPLATE_TITLE_KEY: title})


def is_json(myjson):
	"""

	:param myjson:
	:return:
	"""
	try:
		json_object = json.loads(myjson)
	except ValueError as e:
		return False
	return True


def viewExpenses(request):
	"""
	Main page to manage Expense - Add, Search, Edit and Delete a Bill of expense
	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	expense_id = rh.getPostData('expense_id')
	return manageExpense(request, expense_id=expense_id)


def saveExpense(request):
	"""
	Save Expenses with status
	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	try:
		user_id = rh.getSessionAttribute(USER_IN_SESSION_KEY).user_id
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		expense_service = ExpenseService()
		expense_vo = ExpenseVO(enterprise_id=enterprise_id, user_id=user_id, request_data=rh.getPostData())
		if not expense_vo.is_valid():
			return manageExpense(request=request, expense_vo=expense_vo)
		else:
			expense, is_success = expense_service.saveExpenseFromVO(user_id=user_id, expense_vo=expense_vo)
			if expense and is_success is True:
				response_message = "Last modified expense:<br /> %s" % expense.getCode()
			else:
				response_message = "Failed saving expense %s" % expense.getCode() if expense else ""
	except Exception as e:
		logger.exception('Failed saving expense...\n%s', e.message)
		response_message = "Exception: %s" % e.message
	rh.setSessionAttribute("save_expense_response", response_message)
	return HttpResponseRedirect(MANAGE_EXPENSES_URL)
