<div class="hsn_summary_contianer">
    <h6><b class="hsn_summary_title">HSN SUMMARY</b></h6>
    <table class="table table-bordered row-seperator column-seperator hsn_table hsn_summary" style="font-size:11px;width: 100%;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
        <thead>
            <tr style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;">
                <th rowspan="2" style="width: 6%;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">S.No</th>
                <th class="text-center" rowspan="2" style="width: 28%;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">HSN/SAC</th>
                <th class="text-center td_tax" rowspan="2" style="width: 18%;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">Taxable Value</th>
            </tr>
        </thead>
        <tbody style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
            {% for summary in hsn_summary %}
                {%  if forloop.counter|divisibleby:2 %}
				    <tr class="row_seperator column_seperator row_shading" style="border-spacing: 0;border-collapse: collapse ;border: 1px solid black ;">
				{% else %}
				    <tr class="row_seperator column_seperator row_shading" style="background: #ffffff;border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black ;">
				{% endif %}
                    <td class="text-center td_sno" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">{{forloop.counter}}.</td>
                    <td class="text-center" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: center">{{ summary.hsn_code }}</td>
                    <td class="text-right td_tax" style="border-spacing: 0 ;border-collapse: collapse ;border: 1px solid black;text-align: right">{{ summary.consolidated_taxable_value|floatformat:2 }}</td>
                    </tr>
            {% endfor %}

        </tbody>
    </table>
</div>