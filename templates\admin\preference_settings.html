	<div class="col-sm-12 financial-indv-container mc_1">
		<div class="col-sm-8">
			<div class="reg-chat-container sc_1" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border" style="font-size: 26px; text-shadow: 0 0;">
					Welcome aboard!
				</span>
			</div>
			<div class="reg-chat-container sc_1a no-arrow">
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Would you like to fine-tune your <b>PREFERENCES?</b>
				</span>
			</div>
			<div class="reg-chat-container chat-right sc_1b">
				<span class="chat-img"><span class='chat-img-char'>A</span></span>
				<span class="chat-border">
					<div class="switch_radio_btn btn-group" id="switch_btn_1">
						<a class="btn btn-primary noActive" data-title= "1" 
						data-remove='[{"path":"sc_1c"},{"path":"sc_1d"}]' 
						data-loading='mc_1_loading'
						data-locations='[{"path":"sc_1e","time":"500"}, {"path":"sc_1f","time":"2000"}, {"path":"mc_2","time":"4000"}, {"path":"sc_2a","time":"4000"}, {"path":"sc_2b","time":"6000"}, {"path":"sc_2c","time":"8000"}, {"path":"sc_2d","time":"10000"}, {"path":"mc_3","time":"12000"}, {"path":"sc_3a","time":"12000"}, {"path":"sc_3b","time":"14000"}, {"path":"sc_3ba","time":"18000"}, {"path":"sc_3baa","time":"18000"}]'>
							Yes, definitely
						</a>
						<a class="btn btn-primary noActive" data-title="0" 
						data-loading='mc_1_loading'
						data-remove='[{"path":"sc_1e"},{"path":"sc_1f"}]'
						data-locations='[{"path":"sc_1c","time":"1000"},{"path":"sc_1d","time":"3000"}]'>
							May be later
						</a>
					</div>
				</span>
			</div>
			<div class="reg-chat-container sc_1c" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Cool! we'll catchup later.
				</span>
			</div>
			<div class="reg-chat-container sc_1d no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Oh! By the way we do offer something by default. Check it out!
				</span>
			</div>
			<div class="reg-chat-container sc_1e" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Great! It won't be long.
				</span>
			</div>
			<div class="reg-chat-container sc_1f no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					We'll list out what we can offer and note down your preferences along the way.
				</span>
			</div>
			<div class="reg-chat-container mc_1_loading no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					<div class="wave">
					    <span class="dot"></span>
					    <span class="dot"></span>
					    <span class="dot"></span>
					</div>
				</span>
			</div>
		</div>	
		<div class="col-sm-4 text-center">
			<img class="img-responsive img-invoicing" src="/site_media/images/clip/welcome.png">
		</div>
	</div>

	<div class="col-sm-12 financial-indv-container mc_2">
		<div class="col-sm-3 text-center">
			<img class="img-responsive img-invoicing" src="/site_media/images/clip/sales.png">
		</div>
		<div class="col-sm-9">
			<div class="reg-chat-container sc_2a" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					We help you in <b>Sales & Invoicing!</b>
				</span>
			</div>
			<div class="reg-chat-container sc_2b no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					We offer <b>Sales Order</b> management by default.
				</span>
			</div>
			<div class="reg-chat-container sc_2c no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					You can track <b>Job Order Sales</b> too.
				</span>
			</div>
			<div class="reg-chat-container sc_2d no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					And... You can design your own <b>Print Templates</b> for Invoices.
				</span>
			</div>
			<div class="reg-chat-container mc_2_loading no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					<div class="wave">
					    <span class="dot"></span>
					    <span class="dot"></span>
					    <span class="dot"></span>
					</div>
				</span>
			</div>
		</div>
	</div>

	<div class="col-sm-12 financial-indv-container mc_3">
		<div class="col-sm-9">
			<div class="reg-chat-container sc_3a" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					We do assist you in <b>Managing Inventories</b>
				</span>
			</div>
			<div class="reg-chat-container sc_3b no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					You can manage your goods in & out via <b>Goods Receipt Note(GRN)</b> & <b>Delivery Challan(DC)</b> respectively.
				</span>
			</div>
			<div class="reg-chat-container sc_3ba no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					If you wish, we can finish a few configurations related to Gate Inward Numbers. Shall we?
				</span>
			</div>
			<div class="reg-chat-container chat-right sc_3baa">
				<span class="chat-img"><span class='chat-img-char'>A</span></span>
				<span class="chat-border">
					<div class="switch_radio_btn btn-group" id="switch_btn_2_dummy">
						<a class="btn btn-primary noActive" data-title= "1" data-value="0" 
						data-loading='mc_3_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_3d","time":"500"},{"path":"sc_3e","time":"500"}]'>
							Now is fine
						</a>
						<a class="btn btn-primary noActive" data-title="0" data-value="0" 
						data-loading='mc_3_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_3f","time":"500"},{"path":"sc_3c","time":"1500"},{"path":"sc_3g","time":"3000"},{"path":"sc_3h","time":"3000"}]'>
							Maybe Later
						</a>
					</div>
				</span>
			</div>
			
			<div class="reg-chat-container sc_3d no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					By the way, do you want to keep <b>Gate Inward Number mandatory</b> for all your GRNs?
				</span>
			</div>
			<div class="reg-chat-container chat-right sc_3e">
				<span class="chat-img"><span class='chat-img-char'>A</span></span>
				<span class="chat-border">
					<div class="switch_radio_btn btn-group gate-inward-flag" id="switch_btn_2">
						<a class="btn btn-primary noActive" data-title= "1" data-value="1" 
						data-loading='mc_3_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_3da","time":"500"},{"path":"sc_3ea","time":"500"}]'>
							Yes, Sure
						</a>
						<a class="btn btn-primary noActive" data-title="0" data-value="0" 
						data-loading='mc_3_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_3da","time":"500"},{"path":"sc_3ea","time":"500"}]'>
							No, I don't
						</a>
					</div>
				</span>
			</div>
			<div class="reg-chat-container sc_3da no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Do you want to keep <b>Gate Inward Number Unique for Financial Year</b>?
				</span>
			</div>
			<div class="reg-chat-container chat-right sc_3ea">
				<span class="chat-img"><span class='chat-img-char'>A</span></span>
				<span class="chat-border">
					<div class="switch_radio_btn btn-group gate-inward-flag" id="switch_btn_2a">
						<a class="btn btn-primary noActive" data-title= "1" data-value="2" 
						data-loading='mc_3_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_3db","time":"500"},{"path":"sc_3eb","time":"500"}]'>
							Yes
						</a>
						<a class="btn btn-primary noActive" data-title="0" data-value="0" 
						data-loading='mc_3_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_3db","time":"500"},{"path":"sc_3eb","time":"500"}]'>
							No
						</a>
					</div>
				</span>
			</div>
			<div class="reg-chat-container sc_3db no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Do you want to <b>Automate your Gate Inward Numbers</b>?
				</span>
			</div>
			<div class="reg-chat-container chat-right sc_3eb">
				<span class="chat-img"><span class='chat-img-char'>A</span></span>
				<span class="chat-border">
					<div class="switch_radio_btn btn-group gate-inward-flag" id="switch_btn_2b">
						<a class="btn btn-primary noActive" data-title= "1" data-value="4" 
						data-loading='mc_3_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_3dc","time":"500"},{"path":"sc_3ec","time":"500"}]'>
							Yes
						</a>
						<a class="btn btn-primary noActive" data-title="0" data-value="0" 
						data-loading='mc_3_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_3f","time":"500"},{"path":"sc_3c","time":"2000"}, {"path":"sc_3g","time":"4000"},{"path":"sc_3h","time":"4000"}]'>
							No
						</a>
					</div>
				</span>
			</div>
			<div class="reg-chat-container sc_3dc no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Will you <b>Edit your Gate Inward Numbers</b>?
				</span>
			</div>
			<div class="reg-chat-container chat-right sc_3ec">
				<span class="chat-img"><span class='chat-img-char'>A</span></span>
				<span class="chat-border">
					<div class="switch_radio_btn btn-group gate-inward-flag" id="switch_btn_2c">
						<a class="btn btn-primary noActive" data-title= "1" data-value="8" 
						data-loading='mc_3_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_3f","time":"500"},{"path":"sc_3c","time":"2000"}, {"path":"sc_3g","time":"4000"},{"path":"sc_3h","time":"4000"}]'>
							Yes
						</a>
						<a class="btn btn-primary noActive" data-title="0" data-value="0" 
						data-loading='mc_3_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_3f","time":"500"},{"path":"sc_3c","time":"2000"}, {"path":"sc_3g","time":"4000"},{"path":"sc_3h","time":"4000"}]'>
							No
						</a>
					</div>
				</span>
			</div>



			<div class="reg-chat-container sc_3f" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Ok!
				</span>
			</div>
			<div class="reg-chat-container sc_3c no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					You can manage your internal stock transfer through <b>Issues & Internal Receipts</b>
				</span>
			</div>
			<div class="reg-chat-container sc_3g no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Do you maintain Negative <b>Inventory</b>?
				</span>
			</div>
			<div class="reg-chat-container chat-right sc_3h">
				<span class="chat-img"><span class='chat-img-char'>A</span></span>
				<span class="chat-border" style="width: 385px; max-width: 385px;">
					<div class="switch_radio_btn btn-group" id="switch_btn_3">
						<a class="btn btn-primary noActive" data-title= "1" data-value="1" 
						data-loading='mc_3_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_3i","time":"500"},{"path":"sc_3j","time":"2000"},{"path":"sc_3k","time":"2000"}]'>
							Yes, Occasionally
						</a>
						<a class="btn btn-primary noActive" data-title="0" data-value="0" 
						data-loading='mc_3_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_3i","time":"500"},{"path":"sc_3j","time":"2000"},{"path":"sc_3k","time":"2000"}]'>
							No, not at all
						</a>
					</div>
				</span>
			</div>
			<div class="reg-chat-container sc_3i" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Ok! Noted.
				</span>
			</div>
			<div class="reg-chat-container sc_3j no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Do you deal with <b>Indents</b> to maintain Inventory?
				</span>
			</div>
			<div class="reg-chat-container chat-right sc_3k">
				<span class="chat-img"><span class='chat-img-char'>A</span></span>
				<span class="chat-border">
					<div class="switch_radio_btn btn-group flag-value" id="switch_btn_4">
						<a class="btn btn-primary noActive" data-title= "1" data-value="1" 
						data-loading='mc_3_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_3l","time":"500"}, {"path":"mc_4","time":"2000"}, {"path":"sc_4a","time":"2000"}, {"path":"sc_4b","time":"4000"}, {"path":"sc_4c","time":"4000"}]'>
							Yes
						</a>
						<a class="btn btn-primary noActive" data-title="0" data-value="0" 
						data-loading='mc_3_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_3l","time":"500"}, {"path":"mc_4","time":"2000"}, {"path":"sc_4a","time":"2000"}, {"path":"sc_4b","time":"4000"}, {"path":"sc_4c","time":"4000"}]'>
							No
						</a>
					</div>
				</span>
			</div>
			<div class="reg-chat-container sc_3l" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Cool!
				</span>
			</div>
			<div class="reg-chat-container mc_3_loading no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					<div class="wave">
					    <span class="dot"></span>
					    <span class="dot"></span>
					    <span class="dot"></span>
					</div>
				</span>
			</div>
		</div>
		<div class="col-sm-3 text-center">
			<img class="img-responsive img-invoicing" src="/site_media/images/clip/inventory.png">
		</div>
	</div>	
	<div class="col-sm-12 financial-indv-container mc_4">
		<div class="col-sm-3 text-center">
			<img class="img-responsive img-invoicing" src="/site_media/images/clip/purchase.png">
		</div>
		<div class="col-sm-9">
			<div class="reg-chat-container sc_4a" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					We support you with managing your <b>Purchases</b>.
				</span>
			</div>
			<div class="reg-chat-container sc_4b no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Would you like us to enforce <b>Purchase Order</b> for every Purchases?
				</span>
			</div>
			<div class="reg-chat-container chat-right sc_4c">
				<span class="chat-img"><span class='chat-img-char'>A</span></span>
				<span class="chat-border">
					<div class="switch_radio_btn btn-group" id="switch_btn_5">
						<a class="btn btn-primary noActive" data-title= "1" data-value="1" 
						data-loading='mc_4_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_4d","time":"500"}, {"path":"sc_4f","time":"2000"}, {"path":"mc_5","time":"4000"}, {"path":"sc_5a","time":"4000"}, {"path":"sc_5b","time":"6000"}, {"path":"sc_5c","time":"6000"}]'>
							Yes
						</a>
						<a class="btn btn-primary noActive" data-title="0" data-value="0" 
						data-loading='mc_4_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_4e","time":"500"}, {"path":"sc_4f","time":"2000"}, {"path":"mc_5","time":"4000"}, {"path":"sc_5a","time":"4000"}, {"path":"sc_5b","time":"6000"}, {"path":"sc_5c","time":"6000"}]'>
							Never
						</a>
					</div>
				</span>
			</div>
			<div class="reg-chat-container sc_4d" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					That's Great! Now, no purchase can be made without placing a Purchase Order(PO)
				</span>
			</div>
			<div class="reg-chat-container sc_4e" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Cool! You still can place a Purchase Order(PO) & track Receipts against them whenever you feel like it.
				</span>
			</div>
			<div class="reg-chat-container sc_4f no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					By the way, you can generate PO from Indent too.
				</span>
			</div>
			<div class="reg-chat-container mc_4_loading no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					<div class="wave">
					    <span class="dot"></span>
					    <span class="dot"></span>
					    <span class="dot"></span>
					</div>
				</span>
			</div>
		</div>
	</div>
	<div class="col-sm-12 financial-indv-container mc_5">
		<div class="col-sm-9">
			<div class="reg-chat-container sc_5a" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					You know, we can help you <b>Internally Audit</b> your Expenses.
				</span>
			</div>
			<div class="reg-chat-container sc_5b no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Would you like us to set up an <b>Internal Control Department(ICD)</b> for it?
				</span>
			</div>
			<div class="reg-chat-container chat-right sc_5c">
				<span class="chat-img"><span class='chat-img-char'>A</span></span>
				<span class="chat-border" style="width: 535px; max-width: 535px;">
					<div class="switch_radio_btn btn-group flag-value" id="switch_btn_6">
						<a class="btn btn-primary noActive" data-title= "1" data-value="2" 
						data-loading='mc_5_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_5d","time":"500"}, {"path":"sc_5e","time":"2000"}, {"path":"sc_5f","time":"4000"}, {"path":"sc_5g","time":"4000"}]'>
							Cool, Do it.
						</a>
						<a class="btn btn-primary noActive" data-title="0" data-value="0" 
						data-loading='mc_5_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_5d","time":"500"}, {"path":"mc_6","time":"2000"}, {"path":"sc_6a","time":"2000"}, {"path":"sc_6b","time":"4000"}, {"path":"sc_6c","time":"4000"}]'>
							That won't be necessary
						</a>
					</div>
				</span>
			</div>
			<div class="reg-chat-container sc_5d" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Ok!
				</span>
			</div>
			<div class="reg-chat-container sc_5e no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					We will suggest you any deviations in purchase agreements in terms of Quantity, Quality & Pricing that might result in Debit & Credit Notes.
				</span>
			</div>
			<div class="reg-chat-container sc_5f no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Would you want us to ignore any favorable deviations (those that might result in Credit Notes)?
				</span>
			</div>
			<div class="reg-chat-container chat-right sc_5g">
				<span class="chat-img"><span class='chat-img-char'>A</span></span>
				<span class="chat-border">
					<div class="switch_radio_btn btn-group flag-value" id="switch_btn_7">
						<a class="btn btn-primary noActive" data-title= "1" data-value="4" 
						data-loading='mc_5_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_5h","time":"500"}, {"path":"sc_5i","time":"2000"}, {"path":"sc_5j","time":"2000"}]'>
							Yes
						</a>
						<a class="btn btn-primary noActive" data-title="0" data-value="0" 
						data-loading='mc_5_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_5h","time":"500"}, {"path":"sc_5i","time":"2000"}, {"path":"sc_5j","time":"2000"}]'>
							No
						</a>
					</div>
				</span>
			</div>
			<div class="reg-chat-container sc_5h" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Ok!
				</span>
			</div>
			<div class="reg-chat-container sc_5i no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Would you want us to draft Debit & Credit Notes Automatically?
				</span>
			</div>
			<div class="reg-chat-container chat-right sc_5j">
				<span class="chat-img"><span class='chat-img-char'>A</span></span>
				<span class="chat-border">
					<div class="switch_radio_btn btn-group flag-value" id="switch_btn_8">
						<a class="btn btn-primary noActive" data-title= "1" data-value="8" 
						data-loading='mc_5_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_5k","time":"500"}, {"path":"mc_6","time":"2000"}, {"path":"sc_6a","time":"2000"}, {"path":"sc_6b","time":"4000"}, {"path":"sc_6c","time":"4000"}]'>
							Yes
						</a>
						<a class="btn btn-primary noActive" data-title="0" data-value="0" 
						data-loading='mc_5_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_5k","time":"500"}, {"path":"mc_6","time":"2000"}, {"path":"sc_6a","time":"2000"}, {"path":"sc_6b","time":"4000"}, {"path":"sc_6c","time":"4000"}]'>
							No
						</a>
					</div>
				</span>
			</div>
			<div class="reg-chat-container sc_5k" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Great!
				</span>
			</div>
			<div class="reg-chat-container mc_5_loading no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					<div class="wave">
					    <span class="dot"></span>
					    <span class="dot"></span>
					    <span class="dot"></span>
					</div>
				</span>
			</div>
		</div>
		<div class="col-sm-3 text-center">
			<img class="img-responsive img-invoicing" src="/site_media/images/clip/audit.png">
		</div>
	</div>

	<div class="col-sm-12 financial-indv-container mc_6">
		<div class="col-sm-3 text-center">
			<img class="img-responsive img-invoicing" src="/site_media/images/clip/accounts.png">
		</div>
		<div class="col-sm-9">
			<div class="reg-chat-container sc_6a" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					We certainly help you in <b>Accounts & Finances</b>. We create Vouchers/Journal Entries for your Sale, Purchase & other Expenses, Bank reconciliation, Bill Settlements.
				</span>
			</div>
			<div class="reg-chat-container sc_6b no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Ok! Do you want to validate them before taking them into Account?
				</span>
			</div>
			<div class="reg-chat-container chat-right sc_6c">
				<span class="chat-img"><span class='chat-img-char'>A</span></span>
				<span class="chat-border">
					<div class="switch_radio_btn btn-group flag-value" id="switch_btn_9">
						<a class="btn btn-primary noActive" data-title= "1" data-value="16" 
						data-loading='mc_6_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_6d","time":"500"}, {"path":"sc_6e","time":"2000"}]'>
							Yes
						</a>
						<a class="btn btn-primary noActive" data-title="0" data-value="0" 
						data-loading='mc_6_loading' 
						data-remove='' 
						data-locations='[{"path":"sc_6d","time":"500"}, {"path":"sc_6e","time":"2000"}]'>
							No
						</a>
					</div>
				</span>
			</div>
			<div class="reg-chat-container sc_6d" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border" style="font-size: 26px;">
					Hurray! You are all set!
				</span>
			</div>
			<div class="reg-chat-container sc_6e no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					Check-out how things have shaped up!
				</span>
			</div>
			<div class="reg-chat-container mc_6_loading no-arrow" >
				<span class="chat-img"><img src="/site_media/images/xs_logo_square.png" /></span>
				<span class="chat-border">
					<div class="wave">
					    <span class="dot"></span>
					    <span class="dot"></span>
					    <span class="dot"></span>
					</div>
				</span>
			</div>
		</div>
	</div>
	<div class="col-sm-12 text-right preference_save hide">
		<div class="col-sm-12 text-right next-slide-btn hide">
			<button type="button" style="padding: 14px 22px; font-size:24px; outline: none; margin-top: 15px;" class="btn btn-next save-preferenses-btn" data-next-container="workflow-container" onclick="savePreference(this);" >
				<i class="fa fa-arrow-right" aria-hidden="true"></i>
			</button>
		</div>
		<div class="col-sm-12 text-right save-current-btn hide">
			<button type="button" class="btn btn-save" onclick="savePreference(this);" >
				Save
			</button>
		</div>
	</div>