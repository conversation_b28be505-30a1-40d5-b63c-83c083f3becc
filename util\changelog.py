from erp.accounts import logger
from datetime import datetime
from util.api_util import response_code
from settings import MongoDbConnect


__auther__ = 'charlesmichel'


class ChangeLog(object):
	"""
	Changelog
	"""

	def __init__(self, collection=None, uid=None):
		self.collection = collection
		self.uid = uid
		try:
			self.db = MongoDbConnect[self.collection]
			logger.info("Mongo Db %s collection set" % self.collection)
		except Exception as e:
			logger.info("Mongo Db connection not established..")
			logger.exception(e)

	def insert(self, id=None, enterprise_id=None, data=None):
		"""
		Insert dataset on mongodb
		:param id:
		:param enterprise_id:
		:param data:
		:return:
		"""
		try:
			if self.db.count_documents({self.uid: id}) == 0:
				self.db.insert_one({
					self.uid: int(id), 'enterprise_id': int(enterprise_id), 'created_at': datetime.now(),
					'log': []})
			self.db.update_one({self.uid: id, 'enterprise_id': int(enterprise_id)}, {'$push': {'log': data}})
			return response_code.success()
		except Exception as e:
			logger.exception(e)
			return response_code.failure()

	def fetchLog(self, id=None, enterprise_id=None, offset=None, limit=None):
		"""
		Fetch log list
		:param id:
		:param enterprise_id:
		:return:
		"""
		try:
			if offset is not None:
				return self.db.aggregate([{'$unwind': '$log'}, {'$match': {
					self.uid: id, 'enterprise_id': enterprise_id}}, {'$project': {'_id': 0}}, {
				'$sort': {'log.modified_at': -1}}, {'$skip': int(offset)}, {'$limit': int(limit)}])
			else:
				return self.db.aggregate([{'$unwind': '$log'}, {'$match': {
					self.uid: id, 'enterprise_id': enterprise_id}}, {'$project': {'_id': 0}}, {
				'$sort': {'log.modified_at': -1}}])
		except Exception as e:
			logger.exception(e)

	def fetchLogDetails(self, id=None, enterprise_id=None, identifier=None, marker=None):
		"""
		Fetch individual record of list by datetime identifier
		:param id:
		:param enterprise_id:
		:param identifier:
		:return:
		"""
		try:
			if marker is not None:
				return self.db.aggregate([{'$unwind': '$log'}, {'$match': {
					self.uid: id, 'enterprise_id': enterprise_id, 'log.preference': {
						'$exists': 1 if int(marker['preference']) == 1 else 0}, 'log.modified_at': {
						'$lte': datetime.strptime(identifier, '%Y-%m-%d %H:%M:%S.%f')}}}, {
					'$project': {'log': 1, '_id': 0}}, {'$sort': {'log.modified_at': -1}}, {'$limit': 2}])
			else:
				return self.db.aggregate([{'$unwind': '$log'}, {'$match': {
					self.uid: id, 'enterprise_id': enterprise_id, 'log.modified_at': {
						'$lte': datetime.strptime(identifier, '%Y-%m-%d %H:%M:%S.%f')}}}, {
					'$project': {'log': 1, '_id': 0}}, {'$sort': {'log.modified_at': -1}}, {'$limit': 2}])
		except Exception as e:
			logger.exception(e)
