/***************************************New OA Materials are captured here*********************************************/
var OaPopup = function () {
    /**
     * Inserts popup modal into page if it is not already available
     */
    this.insertOaMaterialsPopup = function (title="Items to be Delivered") {
        var headers = ["HSN/SAC", "Unit", "Quantity", "Price"];
        var tHeaders = "";
        $.each(headers, function(i, item) {
            tHeaders = `${tHeaders}<th width: 110px;>${item}</th>`;
        });

        if ($("#oaMaterialsPopup").text() == "") {
            $("body").append(`<div id="oaMaterialsPopup" class="modal fade" role="dialog">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                            <h4 class="modal-title">OA not linked with some of the Items received. If Finished Items are to be different from those received, then such expected Finished Items can be profiled below.</h4>
                        </div>
                        <div class="modal-body">
                            <table id="id-popOaTable" class="table table-bordered custom-table table-striped tableWithText">
                                <thead><tr align="center">
                                    <th style="width: 50px;">S.No</th>
                                    <th>Item/Material</th>
                                    ${tHeaders}
                                    <th>DELETE</th>
                                </tr></thead>
                                <tbody id="id-popOaTableBody"></tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="7"r class="text-right">
                                            <input type="button" class="btn btn-save" onclick="populateOaUnitChoices(addNewOaMaterial)" value="Add" />
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                        <div class="modal-footer">
                            <span style="float: left; font-size: 14px; color: #8a6d3b; text-shadow: 0px 0px red;">* If Finished Items are same as received, leave the list blank & continue.</span>
                            <a class="btn btn-cancel" data-dismiss="modal">Go Back & Link OA's</a>
                            <input type="button" class="btn btn-save" onclick="return getOaMaterials(saveOa)" value="Continue" />
                        </div>
                    </div>
                </div>
            </div>`);
            populateOaUnitChoices(addNewOaMaterial);
        }
        return this;
    }

    this.show = function () {
        $("#oaMaterialsPopup").modal("show");
        return this;
    }

    this.close = function () {
        $("#oaMaterialsPopup").modal("hide");
        return this;
    }
}

var deleteOaMaterial = function(current){
    $(current).closest("tr").remove();
}

var oaMaterialValidation = function() {
    var result = true;
    var ControlCollections = [];
    $("#id-popOaTable tbody").find("tr").each(function() {
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();
        var control = {
            controltype: 'textbox',
            controlid: $(this).find(`input[name=oa_item_name]`).attr("id"),
            isrequired: true,
            errormsg: 'Material Name is required.'
        };
        ControlCollections[ControlCollections.length] = control;
        var control = {
            controltype: 'textbox',
            controlid: $(this).find(`input[name=qty]`).attr("id"),
            isrequired: true,
            errormsg: 'Quantity is required.',
            mindigit: 0.001,
            mindigiterrormsg: 'Quantity is required.'
        };
        ControlCollections[ControlCollections.length] = control;
        var control = {
            controltype: 'textbox',
            controlid: $(this).find(`input[name=price]`).attr("id"),
            isrequired: true,
            errormsg: 'Unit Rate is required.'
        };
        ControlCollections[ControlCollections.length] = control;
        if($(this).find(`input[name="hsn_code"]`).val().trim() == ""){
           $(this).find(`input[name="hsn_code"]`).addClass('error-border');
        }
    });
    result = result && JSCustomValidator.JSvalidate(ControlCollections);
    return result;
}

var oaMaterialValidateDuplicates = function(item){
    var result = true;
    $("#id-popOaTable tbody").find("tr").each(function(){
        if($(this).find(`input[name="drawing_no"]`).val() == item.value && $(this).find(`input[name="make_id"]`).val() == item.make || $(this).find(`input[name="item_name"]`).val() == item.label){
            alert('Material already added');
            result = false
        }
    });
    return result;
}

var addNewOaMaterial = function(optionHtml) {
    if(oaMaterialValidation()){
        var count = $("#id-popOaTable tbody").find("tr").length + 1;
        var row = `<tr data-toggle="close" data-padding="0" align="left" style="font-size:12px; font-weight:normal;" data-description="rowClass">
            <td class="text-center">${count}</td>
            <td style="width: 280px;"><input type="text" value="" class="form-control" id="oa_${count}_item_name" name="oa_item_name" placeholder ="Select Material"/></td>
            <td class="hsn-wrapper"><input type="text" id="oa_${count}_item_hsn_code" name="hsn_code" class="form-control"  onchange="validateHsnWithSuggestion(this);" onkeypress="validateStringOnKeyPress(this,event,'hsn_specialChar');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" maxlength='9' value="" />
            </td>
            <td class="text-center"><select class="form-control" name="unit_id" id="unit_id" >${optionHtml}</select></td>
            <td><input type="text" id="oa_${count}_item_qty" name="qty" class="form-control" maxlength='16' onfocus="setNumberRangeOnFocus(this,12,3)" value="0" /></td>
            <td><input type="text" id="oa_${count}_item_price" name="price" class="form-control" maxlength='16' onfocus="setNumberRangeOnFocus(this,10,5)" value="0.00"  /></td>
            <td class="text-center"><a href="#" id="delete" onclick="deleteOaMaterial(this)">
                <i style="padding-top: 7px;" class="fa fa-trash-o" title="Delete" alt="Delete"></i></a>
                <!-- Additional hidden elements here -->
                <div class="hide">
                    <input type="text" name="drawing_no" value=""/>
                    <input type="text" name="item_name" value=""/>
                    <input type="text" name="make_id" value=""/>
                    <input type="text" class="item_id" name="item_id" value=""/>
                    <input type="text" name="hsn_code" value=""/>
                    <input type="text" name="mat_type" value="0"/> <!-- 0: Stock material, 1: Non stock material -->
                </div>
            </td>
            </tr>`;
        $("#id-popOaTableBody").append(row);
        initAutoCompleteForOaMaterials();
    }
}


function initAutoCompleteForOaMaterials() {
    var existing_material =  JSON.parse(localStorage.getItem('goods_service_master_data'));
    material_choices = existing_material[0].material;
    $("#id-popOaTable").find(`input[name="oa_item_name"]`).each(function() {
        var element = this;
        var trElement = $(this).closest("tr");
        $(this).autocomplete({
            source: material_choices,
            minLength:0,
            select: function (event, ui) {
                event.preventDefault();
                if(ui.item!=undefined) {
                    if (oaMaterialValidateDuplicates(ui.item)) {
                        $(trElement).find(`input[name="drawing_no"]`).val(ui.item.id);
                        $(trElement).find(`input[name="oa_item_name"]`).val(ui.item.label);
                        $(trElement).find(`input[name="item_name"]`).val(ui.item.label);
                        $(trElement).find(`input[name="make_id"]`).val(ui.item.mid);
                        $(trElement).find(`input[name="item_id"]`).val(ui.item.id);
                        $(trElement).find(`input[name="mat_type"]`).val("0");
                        $(trElement).find(`input[name="hsn_code"]`).val(ui.item.hsn);
                        $(trElement).find('select[name="unit_id"] option').filter(function() {
                            return ($(this).text().includes(ui.item.unit)); //To select unit
                        }).prop('selected', true);
                        $(trElement).find('select[name="unit_id"]').attr("disabled", true);
                    }
                }
            },
            change: function (event, ui) {
                if (ui.item === null) {
                    $(trElement).find('select[name="unit_id"]').attr("disabled", false);
                    item = []
                    item.drawing_no = null;
                    item.label = $(trElement).find(`input[name="oa_item_name"]`).val();
                    if (oaMaterialValidateDuplicates(item)) {
                        $(trElement).find(`input[name="item_name"]`).val($(trElement).find(`input[name="oa_item_name"]`).val());
                        $(trElement).find(`input[name="mat_type"]`).val("1");
                    }
                }
            }
        }).on('focus', function() { $(this).keydown(); });
    });
}

var getOaMaterials = function (callback=function(oaMaterials) {}) {
    var oaMaterials = [];
    $("#id-popOaTableBody").find("tr").each(function() {
        if($(this).find(`input[name="oa_item_name"]`).val().trim() != "") {
           var hsn_code = $(this).find(`input[name="hsn_code"]`).val();
           if($(this).find(`input[name="hsn_code"]`).val() == null){
                hsn_code = '';
            }
            oaMaterials.push({
                item_code: $(this).find(`input[name="drawing_no"]`).val(),
                item_name: $(this).find(`input[name="item_name"]`).val(),
                make_id: $(this).find(`input[name="make_id"]`).val(),
                item_id: $(this).find(`input[name="item_id"]`).val(),
                quantity: $(this).find(`input[name="qty"]`).val(),
                hsn_code: hsn_code,
                is_faulty: false,
                is_service: $(this).find(`input[name="is_service"]`).val(),
                unit_id: $(this).find(`select[name="unit_id"]`).val(),
                price: Number($(this).find(`input[name="price"]`).val()),
                discount: 0,
                mat_type : Number($(this).find(`input[name="mat_type"]`).val())
            });
        }
    });
    var checkQtyPrice = false;
    if($("#id-popOaTableBody").find("tr").length >1) {
        checkQtyPrice = true;
    }
    else if($("#id-popOaTableBody").find("tr").length ==1 && $("#oa_1_item_name").val()!="") {
        checkQtyPrice = true
    }
    if(checkQtyPrice) {
        if(oaMaterialValidation()){
            $("#oaMaterialsPopup").modal("hide");
            callback(oaMaterials);
        }
    }
    else {
         $("#oaMaterialsPopup").modal("hide");
        callback(oaMaterials);
    }

}

var populateOaUnitChoices = function(callback=function(optionHtml){}) {
    if (oaMaterialValidation()) {
        populateUnitChoices(callback);
    }
}

var populateUnitChoices = function(callback=function(optionHtml){}) {
    if ($("#unitChoices").text().trim() == "") {
        $.ajax({
            url: "/erp/masters/json/materials/populate_unit_choices/",
            type: "POST",
            dataType: "json",
            async: true,
            data: "",
            success: function(response) {
                optionHtml = "";
                for(i=0; i < response.length; i++) {
                    optionHtml += "<option value='"+response[i][0]+"'>" + response[i][1] + "</option>";
                }
                $("#unitChoices").html(optionHtml);
                callback(optionHtml);
            }
        });
    } else {
         callback($("#unitChoices").html());
    }
}

/*************************************Selected OA Materials are captured here******************************************/
var MaterialSelector = function() {
    /**
     * Inserts popup modal into page if it is not already available
     */
    this.insertMaterialSelectorPopup = function (title="BoM Materials") {
        var headers = ["Quantity", "Unit", "HSN/ SAC", "Price"];
        var tHeaders = "";
        $.each(headers, function(i, item) {
            tHeaders = `${tHeaders}<th style='width: 110px;'>${item}</th>`;
        });
        if ($("#materialSelectorPopup").text() == "") {
            $("body").append(`<div id="materialSelectorPopup" class="modal fade" role="dialog">
                <div class="modal-dialog modal-lg" style="width: 90%; max-width: 1400px;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                            <h4 class="modal-title">${title}</h4>
                        </div>
                        <div class="modal-body">
                            <table id="id-popTable" class="table table-bordered custom-table table-striped tableWithText">
                                <thead><tr align="center">
                                    <th style="width: 50px;">S.No</th>
                                    <th style="width: 100px;"> OA No</th>
                                    <th>Item/Material</th>
                                    ${tHeaders}
                                    <th>DELETE</th>
                                </tr></thead>
                                <tbody id="id-popTableBody"></tbody>
                            </table>
                        </div>
                        <div class="modal-footer"><span class='pull-left nonStock-text'>  <span class='nonStock-text non_stock-flag'></span> - NON-STOCKABLE</span><span class='pull-left nonStock-text'>  <span class='nonStock-text service-item-flag'></span> - SERVICE</span> <input type="button" class="btn btn-save" id="id-popActionButton" value="Add" /><a class="btn btn-cancel" data-dismiss="modal">Close</a></div>
                    </div>
                </div>
            </div>`);
        }
    }

    this.constructMaterialRow = function (i, item) {
    console.log("item", item);
        var [drawingNumber, itemName] = [item.drawing_no, item.name];
        var description = itemName;
        var dataParent = item.item_id+ "_" + sessionStorage.clickcount
        var isStock = item.mat_type;
        if (item.drawing_no != null && trim(item.drawing_no) != "" && item.drawing_no != item.name) {
            description = `${description} - ${item.drawing_no}`;
            drawingNumber = item.drawing_no;
        }
        if (item.make_name != "" && item.make_name != null && item.make_name != '[]') {
            description = description + " [" + constructDifferentMakeName(item.make_name) + "]";
        }
        if(typeof(Storage) !== "undefined") {
            if (sessionStorage.clickcount) {
                sessionStorage.clickcount = Number(sessionStorage.clickcount) + 1;
            } else {
                sessionStorage.clickcount = 1;
            }
        }
        var isStockable = "";
        var isStockable = isStock == 0 ? isStockable = 'non_stock-flag': '';
        if(item.hasChildren) {
            description = `<i role="button" class="fa fa-plus fa-for-arrow" onclick="showCatalogMaterials(this, '${item.item_id}', '${item.oa_id}', '${item.oa_code}', '${dataParent}')">
            <input type="hidden" value="0" class="ChildLevel">
            <a  style="padding-left: 30px; display: flex; margin-top: -12px;">${description}</a></i>`
        }
        else{
            description = `${description}`
        }
        var is_service_value = item.is_service==true?1:0;
        var hsn_code = item.hsn_code == undefined ? "" : item.hsn_code;
        var priceElement = `<input type="text" name="price" class="form-control text-right" maxlength='16' onfocus="setNumberRangeOnFocus(this,10,5)" value="${Number(item.price)}">`;
        var qtyElement = `<input type="text" name="oa_qty" class="form-control text-right bom_po_qty bom_amt_calc mandatory_field setUnitValue" maxlength='16' onfocus="setNumberRangeOnFocus(this,12,3)" autocomplete="off" value="${Number(item.oa_pending_qty).toFixed(3)}">`
        var hsnElement = `<input type="text" id="edit_oa_hsn_${item.item_id}_${item.make_id}_${item.oa_id}" name="hsn_code" class="form-control text-center mandatory_field hsn_code" maxlength='9' onChange="validateHsnWithSuggestion(this);" onkeypress="validateStringOnKeyPress(this,event,'hsn_specialChar');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" value="${hsn_code}">`
        var itemTypeFlag = "";
        if(item.is_service == true){
            itemTypeFlag += `<span class="service-item-flag"></span>`;
        }
        if(item.mat_type == 0 && item.is_service != true){
           itemTypeFlag +=  `<span class="non_stock-flag"></span>`;
        }
        var rowClass = `${item.item_id}_${item.make_id}`;
        var row = `<tr data-toggle="close" data-padding="0" id='${dataParent}' align="left" style="font-size:12px; font-weight:normal;" data-description="rowClass">
            <td class="text-left SerialNumber">${i + 1}</td>
            <td>${item.oa_code}</td>
            <td>${description} ${itemTypeFlag}</td>
            <td>${qtyElement}</td>
            <td class="text-center" name="unitName">${item.unit}</td>
            <td class="hsn-wrapper">${hsnElement}</td>
            <td>${priceElement}</td>
            <td class="text-center td-delete-row"><a href="#" onclick="deleteTableRow(this)">
                <i style="padding-top: 7px;" class="fa fa-trash-o" title="Delete" alt="Delete"></i></a>
                <!-- Additional hidden elements here -->
                <div class="hide">
                    <input type="text" name="oa_id" value="${item.oa_id}"/>
                    <input type="text" name="doc_code" value="${item.oa_code}"/>
                    <input type="text" class="item_id" name="item_id" value="${item.item_id}"/>
                    <input type="text" name="drawing_no" value="${drawingNumber}"/>
                    <input type="text" name="item_name" value="${itemName}"/>
                    <select name="make_name"> <option value="${item.make_name}" selected=selected>${item.make_name}</option><select/>
                    <select name="make_id"> <option value="${item.make_id}" selected=selected>${item.make_id}</option><select/>
                    <input type="text" name="is_service" value="${item.is_service}"/>
                    <input type="text" name="unit_id" value="${item.unit_id}"/>
                    <input type="text" name="alternate_unit_id" value="${item.alternate_unit_id}"/>
                    <input type="text" name="scale_factor" value="${item.scale_factor}"/>
                    <input type="text" name="inspection_log" class="quantity_inspection_review" value='${item.inspection_log}'/>
                    <input type="text" name="mat_type" value="${item.mat_type}"/> <!-- 1: Stock material, 0: Non stock material -->
                </div>
            </td>
        </tr>`;
        $("#id-popTableBody").append(row);
    }

    /**
     * shows OA Materials
     */
    var oaMaterials = [];
    this.showOaMaterials = function (materials, callback=function(oaMaterials) {}) {
        $("#id-popTableBody").text("");
        $("#materialSelectorPopup").modal("show");
        $.each(materials, this.constructMaterialRow);
        $("#id-popActionButton").click(function() {
            var checkQty = false; 
            isValidHSN = true;
            $(".error-border").removeClass('error-border');
            $(".custom-error-message, .hsn-suggestion").remove();
            var ControlCollections = [];
            $("#id-popTableBody").find("tr").each(function(){
                var currentQty = $(this).find("input[name='oa_qty']").val();
                if(currentQty > 0) {
                    var currentHsn = $(this).find("input[name*='hsn_code']");
                    var currentElementId = currentHsn.attr("id");
                    var control = {
                        controltype: 'textbox',
                        controlid: currentElementId,
                        isrequired: true,
                        errormsg: 'HSN/SAC is required.',
                        ishsn: true,
                        hsnerrormsg: 'Invalid HSN Code',
                        suggestioncontainer: "hsn-wrapper"
                    }
                    ControlCollections[ControlCollections.length] = control;
                }
            });
            var result = JSCustomValidator.JSvalidate(ControlCollections);

            if(result){
                $("#material_is_service").val("");
                $("#id-popTableBody").find("tr").each(function() {
                    var oa_aty = $(this).find(`input[name="oa_qty"]`).val();
                    if (!$(this).hasClass('child-added')) {
                        var makeName = $(this).find(`select[name="make_id"] option:selected`).text();
                        makeName = makeName != "" && makeName != undefined && makeName != "-NA-" ? ` [${makeName}]` : "";
//                        var itemName = `${$(this).find('input[name="item_name"]').val()} ${makeName} `
                        var itemName = $(this).closest('tr').find('td:eq(2)').text();
                        var drawing_no = $(this).find(`input[name="drawing_no"]`).val()
                        drawing_no = drawing_no != null && drawing_no != "" ? `${drawing_no}` : "",
                        item_id =  $(this).find(`input[name="item_id"]`).val();
                        make_id =  $(this).find(`select[name="make_id"]`).val();
                        dc_qty = Number(oa_aty);
                        acc_qty = Number(oa_aty);
                        rec_qty = Number(oa_aty);
                        var construct = false
                        if (oaMaterials.length != 0 ){
                            $.each(oaMaterials, function (i,item) {
                                if ( item.item_id == item_id ){
                                    dc_qty += item.dc_qty;
                                    acc_qty += item.acc_qty;
                                    rec_qty += item.rec_qty;
                                    oaMaterials[i].dc_qty = dc_qty;
                                    oaMaterials[i].acc_qty = acc_qty;
                                    oaMaterials[i].rec_qty = rec_qty;
                                    construct = true
                                    return false;
                                }
                            });
                            if (construct == false){
                                oaMaterialConstruct(this, drawing_no, itemName, oa_aty);
                                construct = true
                            }
                        }
                        else{
                            oaMaterialConstruct(this, drawing_no, itemName, oa_aty);
                        }
                        checkQty = true;
                    }
                });
                if(!checkQty && $("#id-popTableBody").find("tr").length >= 1) {
                    swal({
                        title: "Are you sure?",
                        text: "None of the item quantity is filled, so these item will not be added to the table.<br />Do you want to continue?",
                        type: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#209be1",
                        confirmButtonText: "Yes",
                        closeOnConfirm: true
                    },
                    function(){
                        callback(oaMaterials);
                        Calculate();
                        enableEditButton();
                        $(".material-removal-icon").trigger("click");
                        $("#qty").val("0.00")
                        $("#materialSelectorPopup").modal("hide");
                    });
                }
                else {
                    callback(oaMaterials);
                    Calculate();
                    enableEditButton();
                    $(".material-removal-icon").trigger("click");
                    $("#qty").val("0.00")
                    $("#materialSelectorPopup").modal("hide");
                }
            }
        });
    }

    function oaMaterialConstruct(tr, drawing_no, itemName, oa_aty){
        oaMaterials.push({
            oa_code: $(tr).find(`input[name="doc_code"]`).val(),
            oa_id: $(tr).find(`input[name="oa_id"]`).val(),
            po_id: "", dc_id: "", rec_grn_id: "",
            mat_type: Number($(tr).find(`input[name="mat_type"]`).val()),

            item_id: $(tr).find(`input[name="item_id"]`).val(),
            drawing_no: drawing_no,
            item_name: itemName,
//            make_id: $(tr).find(`select[name="make_id"]`).val(),
            make_id: 1,
            is_faulty: false,

            hsn_code: $(tr).find(`input[name="hsn_code"]`).val(),
            unit_id: $(tr).find(`input[name="unit_id"]`).val(),
            unit_name: $(tr).find(`td[name="unitName"]`).text(),
            price: Number($(tr).find(`input[name="price"]`).val()),
            is_service: $(tr).find(`input[name="is_service"]`).val(),
            alternate_unit_id:$(tr).find(`input[name="alternate_unit_id"]`).val(),
            scale_factor:$(tr).find(`input[name="scale_factor"]`).val(),
            discount: 0,
            dc_qty: Number(oa_aty),  acc_qty: Number(oa_aty), rec_qty: Number(oa_aty),
        });
    }
}

var deleteTableRow = function (element) {
    $(element).closest("tr").remove();
}

/**
 * test function fetch
 */
function openRejectionProfilePopup(current, isModaltobeOpened){
    var current_item_id = $(current).closest("tr").find("input[name='item_id']").val();
    if(!Number(current_item_id) && isModaltobeOpened) {
        swal("Sorry!", "You can't add <b>Rejection Profile</b> for a non-stock material.", "warning");
        return;
    }
    $("#rejection-remarks-table").find("tbody").empty();
    var currentTR = $(current).closest("tr");
    var title = currentTR.find("input[name='item_name']").val();
    var quantity = $(current).text().trim();
    var unit = currentTR.find(".td-unit-text").text().trim();
    $("#current-editable-id").val(currentTR.attr("data-row-id"));
    $("#rejected-remarks-modal").find(".modal-title").html('REJECTION PROFILE - <small>'+title+' <span style="float: right; margin-top: 7px;"><b>QTY</b>: <span class="rejection-quantity">'+quantity+'</span> '+unit+'<span></small>')
    if(unit == "") {
        unit = currentTR.find(".td-unit-name").text().trim();
    }
    if($(current).closest("tr").find("input[name='rejection_profiles']").val() != "") {
        var response = JSON.parse($(current).closest("tr").find("input[name='rejection_profiles']").val());
        constructRejectionProfileTable(response, current, isModaltobeOpened);
    }
    else {
        var data = JSON.stringify({
                                "enterprise_id": $("#enterprise_id").val(),
                                "grn_id": $("#id_receipt_no").val(),
                                "item_id": current_item_id,
                                "make_id": $(current).closest("tr").find("input[name='make_id']").val(),
                                "is_faulty": $(current).closest("tr").find("input[name='is_faulty']").val(),
                                "po_id": $(current).closest("tr").find("input[name='po_id']").val() == undefined || $(current).closest("tr").find("input[name='po_id']").val() == ''? "NULL" : $(current).closest("tr").find("input[name='po_id']").val(),
                                "rec_grn_id": $(current).closest("tr").find("input[name='rec_grn_id']").val() == undefined || $(current).closest("tr").find("input[name='rec_grn_id']").val() == ''? "NULL" : $(current).closest("tr").find("input[name='rec_grn_id']").val(),
                                "oa_id": $(current).closest("tr").find("input[name='oa_id']").val() == undefined || $(current).closest("tr").find("input[name='oa_id']").val() == ''? "NULL" : $(current).closest("tr").find("input[name='oa_id']").val(),
                                "dc_id": $(current).closest("tr").find("input[name='dc_id']").val() == undefined || $(current).closest("tr").find("input[name='dc_id']").val() == ''? "NULL" : $(current).closest("tr").find("input[name='dc_id']").val(),
                            });
        $.ajax({
            url: "/erp/stores/json/grn/fetch_rejection_profiles/",
            type: "POST",
            datatype: "json",
            data : {grn_item_details: data},
            success: function (response){
                $(current).closest("tr").find("input[name='rejection_profiles']").val(JSON.stringify(response))
                constructRejectionProfileTable(response, current, isModaltobeOpened);
            }
        })
    }
    if(isModaltobeOpened && quantity > 0) {
        $("#rejected-remarks-modal").modal("show");
    }
}

function closeRejectedRemarksModal() {
    var quantity = 0;
    var isValid = true;
    $("#rejection-remarks-table").find(".error-border").removeClass("error-border");
    if(Number($("#rejected-remarks-modal").find(".rejection-quantity").text().trim()) < 0) {
        swal({
            "title": "",
            "text": "The Total Rejected quantity is less than Zero.<br /> This rejection profile will not be stored",
            "type": "warning"
        },
        function(){
            $("#rejected-remarks-modal").modal("hide");
        });
        var currentRowId = $("#current-editable-id").val();
        var currentRow = $("#materialtable, #dc_materialtable").find("tr[data-row-id='"+currentRowId+"']")
        $(currentRow).find("input[name='rejection_profiles']").val("");
    }
    else {
        $("#rejection-remarks-table").find("tbody tr").each(function(index, tr) {
            if(Number($(this).find('td.td_rejection_qty input').val().trim()) <= 0 ) {
                $(this).remove();
                return;
            }
            if($(this).find('td.td_rejection_reason input').val().trim() == "") {
                $(this).find('td.td_rejection_reason input').addClass("error-border");
                isValid = false;
            }
            quantity += Number($(this).find(".td_rejection_qty input").val().trim())
        });

        if(Number(quantity.toFixed(3)) != Number($("#rejected-remarks-modal").find(".rejection-quantity").text())) {
            $("#rejected-remarks-modal").modal("show");
            swal("","The Total Rejected quantity is not equal to the entered quantity.<br /> Please update the quantity.", "warning");
        }
        else {
            if(isValid) {
                updateNewRejectionRemarks();
                $("#rejected-remarks-modal").modal("hide");
            }
        }
    }
}

function constructRejectionProfileTable(response, current, isModaltobeOpened) {
    var price = $(current).closest("tr").find("input[name='price']").val();
    if (!Number(price)){
        price = 0;
    }
    if(response.length > 0) {
        $.each(response, function (i, item) {
            var isMatch = false;
            $("#rejection-remarks-table").find("tbody tr").each(function(){
                if($(this).find(".td_rejection_reason input").val().trim().toLowerCase() == item.reason.toLowerCase()) {
                    var consolidateQty = Number(item.quantity) + Number($(this).find(".td_rejection_qty input").val().trim());
                    $(this).find(".td_rejection_qty input").val(consolidateQty.toFixed(3));
                    isMatch = true;
                }
            });
            if(!isMatch) {
                var row = `<tr>
                                <td class='text-left td_rejection_reason'>
                                    <input type='text' class='form-control' value='${item.reason}' maxlength='150' onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar');" />
                                </td>
                                <td class='text-right td_rejection_qty'>
                                    <input type='text' class='form-control' value='${item.quantity.toFixed(3)}' maxlength='16' onfocus="setNumberRangeOnFocus(this,10,5)" />
                                </td>
                                <td class='text-right td_rejection_debit'>
                                    <input type='text' class='form-control' value='${item.debit.toFixed(5)}' maxlength='16' onfocus="setNumberRangeOnFocus(this,10,5)" />
                                </td>
                            </tr>`;
                $("#rejection-remarks-table").find("tbody").append(row);
            }
        });
        var quantity = 0;
        $("#rejection-remarks-table").find("tbody tr").each(function(index, tr) {
            quantity += Number($(this).find(".td_rejection_qty input").val().trim())
        });
        if(quantity < Number($(current).text().trim())) {
            var diffQuantity = Number($("#rejected-remarks-modal").find(".rejection-quantity").text().trim()) - quantity;
            var isRejectedText = false;
            $("#rejection-remarks-table").find("tbody tr").each(function(index, tr) {
                if($(this).find(".td_rejection_reason input").val().trim().toLowerCase() == "rejected") {
                    $(this).find(".td_rejection_qty input").val((Number($(this).find(".td_rejection_qty input").val()) + diffQuantity).toFixed(2));
                    isRejectedText = true;
                    return;
                }
            });
            if(!isRejectedText) {
                var row = `<tr>
                                <td class='text-left td_rejection_reason'>
                                    <input type='text' class='form-control' value='REJECTED' maxlength='150' onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar');" />
                                </td>
                                <td class='text-right td_rejection_qty'>
                                    <input type='text' class='form-control' value='${diffQuantity}' maxlength='16' onfocus="setNumberRangeOnFocus(this,10,5)" />
                                </td>
                                <td class='text-right td_rejection_debit'>
                                    <input type='text' class='form-control' value='${price}' maxlength='16' onfocus="setNumberRangeOnFocus(this,10,5)" />
                                </td>
                            </tr>`;
                $("#rejection-remarks-table").find("tbody").append(row);
            }
        }
    }
    else {
        var row = `<tr>
                        <td class='text-left td_rejection_reason'>
                            <input type='text' class='form-control' value='REJECTED' maxlength='150' onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar');"  />
                        </td>
                        <td class='text-right td_rejection_qty'>
                            <input type='text' class='form-control' value='${$(current).text().trim()}' maxlength='16' onfocus="setNumberRangeOnFocus(this,10,5)" />
                        </td>
                        <td class='text-right td_rejection_debit'>
                            <input type='text' class='form-control' value='${price}' maxlength='9' maxlength='16' onfocus="setNumberRangeOnFocus(this,10,5)" />
                        </td>
                    </tr>`;
        var jsonObj = [];
        var item = {}
        item["reason"] = "REJECTED";
        item ["quantity"] = Number($(current).text().trim());
        item ["debit"] = Number(price);
        jsonObj.push(item);
        $(current).closest("tr").find("input[name='rejection_profiles']").val(JSON.stringify(jsonObj))
        $("#rejection-remarks-table").find("tbody").append(row);
    }
    if(!isModaltobeOpened) {
        closeRejectedRemarksModal();
    }
}
/**
 * Displays the catalog materials under the selected materials
 */
var showCatalogMaterials = function (element, catalogId, oaId, oaCode, dataParent) {
    var linkElement = $(element).parent();
    var dataP = dataParent;
    var current_bom_sno = $("#"+dataParent).find(".SerialNumber").text().trim();
    var constructedRow = '';
    var linkElementTr = linkElement.closest("tr");
    var childLevel = Number($(linkElement).find(".ChildLevel").val()) + 1;
    var currentCatChild = $("#"+dataP).attr('id');
    if($("#"+currentCatChild).attr('data-toggle') == 'open') {
        var curPadding = $("#"+currentCatChild).attr('data-padding');
        $("#"+currentCatChild).nextAll('tr').each(function(){
            if(Number($(this).attr('data-padding')) > Number(curPadding)) {
                $(this).remove();
            }
            else {
                return false;
            }
        });
        $("#id-popTable #"+dataP).find('.fa.fa-for-arrow').removeClass('fa-minus').addClass('fa-plus');
        $("#id-popTable #"+dataP).attr('data-toggle','close');
        linkElement.closest("tr").find(`input[name="oa_qty"]`).attr("disabled", false);
        linkElement.closest("tr").find(`input[name="price"]`).attr("disabled", false);
        linkElement.closest("tr").removeClass("child-added");
        $("#id-popTableBody").find(`.ChildLevel-${childLevel}-${catalogId}`).each(function () {
            $(this).remove();
        });
    }
    else {
        $("#id-popTable #"+dataP).find('.fa.fa-for-arrow').removeClass('fa-plus').addClass('fa-minus');
        $("#id-popTable #"+dataP).attr('data-toggle','open');
        var quantity = linkElement.closest("tr").find(`input[name="oa_qty"]`).val();
        linkElement.closest("tr").find(`input[name="oa_qty"]`).attr("disabled", true);
        linkElement.closest("tr").find(`input[name="price"]`).attr("disabled", true);
        linkElement.closest("tr").addClass("child-added");
        $.ajax({
            url: "/erp/stores/json/catalogue_materials/",
            type: "post",
            datatype: "json",
            data: {"cat_code": catalogId, "alternate_unit_id":linkElement.closest("tr").find(`input[name="alternate_unit_id"]`).val()},
            success: function (response) {
                $.each(response, function (i, item) {
                    var [drawingNumber, itemName] = [item.drawing_no, item.name];
                    var description = itemName;
                    var isStockable = "";
                    var isStock = item.material_type;
                    var dataPadding = Number($(linkElementTr).data('padding') + 30);
                    if(!item.material_type) {
                        isStockable = 'non_stock-flag';
                    }
                    if (item.drawing_no != null && trim(item.drawing_no) != "" && item.drawing_no != item.name) {
                        description = `${description} - ${drawingNumber}`;
                    }
                    if (item.make_name !="" && item.make_name !=null && item.make_name) {
                        description += " [" + item.make_name +"]";
                    }
                    if(typeof(Storage) !== "undefined") {
                        if (sessionStorage.clickcount) {
                            sessionStorage.clickcount = Number(sessionStorage.clickcount) + 1;
                        } else {
                            sessionStorage.clickcount = 1;
                        }
                    }

                    var dataParent = item.item_id+ "_" + sessionStorage.clickcount
                    if(item.hasChildren) {
                        description = `<i role="button" class="fa fa-plus fa-for-arrow" onclick="showCatalogMaterials(this, '${item.item_id}', '${oaId}', '${oaCode}', '${dataParent}')">
                        <input type="hidden" value="0" class="ChildLevel">
                        <a  style="padding-left: 30px; display: flex; margin-top: -12px;">${description}</a></i>`
                    }
                    else{
                        description = `${description}`
                    }
                    var is_service_value = item.is_service==true?1:0;
                    var itemTypeFlag = "";
                    if(item.is_service == true){
                        itemTypeFlag = `<span class="service-item-flag"></span>`;
                    }
                    if(isStock == 0 && item.is_service != true) {
                        itemTypeFlag = `<span class="non_stock-flag"></span>`;
                    }
                    var makes = "";
                    if (item.makes.length > 0) {
                        makes = `<select style="min-width: 100px;float: right; max-width: 100px; margin-left: 10px;" class="form-control set-make-qty parent_bom_delete"  name="make_id" id="txtmake${i}">`;
                        for (j = 0; j <= item.makes.length - 1 ; j++) {
                            makes = `${makes}<option value="${item.makes[j].make_id}" data-quantity="${item.makes[j].stock}">${item.makes[j].make_name}</option>`;
                        }
                        makes = `${makes}</select>`;
                    }
                    var qtyElement = `<input type="text" data-unitvalue="${item.quantity}" value="${Number(item.quantity) * Number(quantity)}"
                        onkeypress="return validateFloatKeyPress(this, event);" class="form-control text-right bom_po_qty bom_amt_calc mandatory_field setUnitValue" name="oa_qty" autocomplete="off">`;
                    var priceElement = `<input type="text" onkeypress ="return validateFloatKeyPress(this, event);"
                        class="form-control text-right" name="price" value="${item.price}">`;
                    var hsnElement = `<input type="text" id="grn_oa_child-hsn_${sessionStorage.clickcount}_${item.item_id}_${item.make_id}" name="hsn_code" class="form-control text-center mandatory_field hsn_code" maxlength='9' onchange="validateHsnWithSuggestion(this);" onkeypress="validateStringOnKeyPress(this,event,'hsn_specialChar');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" value="${item.tariff_no}">`
                    // var row = `<tr class="ChildLevel-${childLevel}-${catalogId}" data-padding="${dataPadding}" id="${dataParent}" data-child="${dataP}" data-toggle="close" bgcolor="#ececec" border="1" align="left" style="font-size:12px; font-weight:normal;">
                    //     <td class="text-left SerialNumber">${current_bom_sno}.${i+1}</td>
                    //     <td class="text-center">${oaCode}</td>
                    //     <td class='text-left' ${dataPadding} style='padding-left:${dataPadding-20}px'>
                    //         <span style='padding-left:30px; display: inline-block' class='tree-view'>
                    //             ${description} ${makes} ${itemTypeFlag}
                    //         </span>
                    //     </td>
                    //     <td>${qtyElement}</td>
                    //     <td class="text-center" name="unitName">${item.unit_name}</td>
                    //     <td class="hsn-wrapper">${hsnElement}</td>
                    //     <td>${priceElement}</td>
                    //     <td class="text-center td-delete-row"><a href="#" onclick="deleteTableRow(this)">
                    //         <i style="padding-top: 7px;" class="fa fa-trash-o" title="Delete" alt="Delete"></i></a>
                    //         <!-- Additional hidden elements here -->
                    //         <div class="hide">
                    //             <input type="text" class="item_id" name="item_id" value="${item.item_id}"/>
                    //             <input type="text" name="oa_id" value="${oaId}"/>
                    //             <input type="text" name="item_name" value="${itemName}"/>
                    //             <input type="text" name="drawing_no" value="${drawingNumber}"/>
                    //             <input type="text" name="unit_id" value="${item.unit_id}"/>
                    //             <input type="text" name="alternate_unit_id" value="${item.alternate_unit_id}"/>
                    //             <input type="text" name="scale_factor" value="${item.scale_factor}"/>
                    //             <input type="text" name="mat_type" value="0"/> <!-- 0: Stock material, 1: Non stock material -->
                    //             <input type="text" name="is_service" value="${is_service_value}"/>
                    //         </div>
                    //     </td></tr>`;
                    var row = `<tr class="ChildLevel-${childLevel}-${catalogId}" data-padding="${dataPadding}" id="${dataParent}" data-child="${dataP}" data-toggle="close" bgcolor="#ececec" border="1" align="left" style="font-size:12px; font-weight:normal;">
                        <td class="text-left SerialNumber">${current_bom_sno}.${i+1}</td>
                        <td class="text-center">${oaCode}</td>
                        <td class='text-left' ${dataPadding} style='padding-left:${dataPadding-20}px'>
                            <span style='padding-left:30px; display: inline-block' class='tree-view'>
                                ${description} ${itemTypeFlag}
                            </span>
                        </td>
                        <td>${qtyElement}</td>
                        <td class="text-center" name="unitName">${item.unit_name}</td>
                        <td class="hsn-wrapper">${hsnElement}</td>
                        <td>${priceElement}</td>
                        <td class="text-center td-delete-row"><a href="#" onclick="deleteTableRow(this)">
                            <i style="padding-top: 7px;" class="fa fa-trash-o" title="Delete" alt="Delete"></i></a>
                            <!-- Additional hidden elements here -->
                            <div class="hide">
                                <input type="text" class="item_id" name="item_id" value="${item.item_id}"/>
                                <input type="text" name="oa_id" value="${oaId}"/>
                                <input type="text" name="item_name" value="${itemName}"/>
                                <input type="text" name="drawing_no" value="${drawingNumber}"/>
                                <input type="text" name="unit_id" value="${item.unit_id}"/>
                                <input type="text" name="alternate_unit_id" value="${item.alternate_unit_id}"/>
                                <input type="text" name="scale_factor" value="${item.scale_factor}"/>
                                <input type="text" name="mat_type" value="0"/> <!-- 0: Stock material, 1: Non stock material -->
                                <input type="text" name="is_service" value="${is_service_value}"/>
                            </div>
                        </td></tr>`;
                    constructedRow = constructedRow+row
                });
                $(linkElement.closest("tr")).after(constructedRow);
            }
        });
    }
}

var inputAttributes = `type="text" class="form-control text-right"`;
var quantityAttributes = `${inputAttributes} maxlength='16' onfocus="setNumberRangeOnFocus(this,12,3)" onblur="Calculate();"`;
var priceAttributes = `${inputAttributes} maxlength='16' onfocus="setNumberRangeOnFocus(this,10,5)" onblur="Calculate();"`;
var percentAttributes = `${inputAttributes} maxlength='6' onfocus="setNumberRangeOnFocus(this,3,2)" onblur="validatePercentage(this, event); Calculate();"`;
var quantityAttributesForConsolidateRow = `${inputAttributes} maxlength='16' onfocus="setNumberRangeOnFocus(this,12,3)" onblur="updateConsolidatedQty(this); Calculate();"`;
var priceAttributesForConsolidateRow = `${inputAttributes} maxlength='16' onfocus="setNumberRangeOnFocus(this,10,5)" onblur="updateConsolidatedPrice(this); Calculate();"`;
var percentAttributesForConsolidateRow = `${inputAttributes} maxlength='6' onfocus="setNumberRangeOnFocus(this,3,2)" onblur="updateConsolidatedPrice(this); validatePercentage(this, event); Calculate();"`;

/**
 * Class for constructing the material row
 * param: receivedAgainst is either one of [Purchase Order, Job Work, Delivery Challan, Sales Return, Issues, Others]
 * param: tableId for displaying materials; This configurable while using materials returned tab
 * param: isGoodsAlreadyReceived is Boolean
 */
var MaterialTable = function(receivedAgainst="Purchase Order", tableId="materialtable", isGoodsAlreadyReceived=false) {
    this.receivedAgainst = receivedAgainst;
    this.tableId = tableId;
    this.isGoodsAlreadyReceived = isGoodsAlreadyReceived;

    this.createTaxElements = function(additionalClasses="", onchangeEvents="") {
        var row =
            `<td class="${additionalClasses}">
                <select class='form-control' name='cgst' onchange='${onchangeEvents} Calculate()'>${CGST_rates}</select>
                <span class='td-sub-content bracket-enclosed' name='cgstamt'>0.00</span>
            </td>
            <td class="${additionalClasses}">
                <select class='form-control' name='sgst' onchange='${onchangeEvents} Calculate()'>${SGST_rates}</select>
                <span class='td-sub-content bracket-enclosed' name='sgstamt' >0.00</span>
            </td>
            <td class="${additionalClasses}">
                <select class='form-control' name='igst' onchange='${onchangeEvents} Calculate()'>${IGST_rates}</select>
                <span class='td-sub-content bracket-enclosed' name='igstamt' >0.00</span>
            </td>`;
        return row;
    }
}

/**
 * Material Table: Row construction
 */
MaterialTable.prototype.createRow = function(item={}) {
    item.is_faulty = item.is_faulty == 1 || item.is_faulty == "true" ? 1 : 0; // data manipulation
    item.pending_qty = item.pending_qty == undefined ? 0 : Number(item.pending_qty);
    item.dc_qty = item.dc_qty == undefined ? 0 : Number(item.dc_qty);
    item.rec_qty = item.rec_qty == undefined ? 0 : Number(item.rec_qty);
    item.acc_qty = item.acc_qty == undefined ? 0 : Number(item.acc_qty);
    item.rej_qty = item.rej_qty == undefined ? 0 : Number(item.rej_qty);
    item.hsn_code = item.hsn_code == undefined ? "" : item.hsn_code;
    item.price = item.price == undefined ? 0 : parseFloat(item.price).toFixed(5);
    item.discount = item.discount == undefined ? 0 : Number(item.discount);
    item.saved_acc_qty = item.saved_acc_qty === undefined ? 0 : item.saved_acc_qty;
    item.issue_qty = item.issue_qty == undefined ? 0 : Number(item.issue_qty);
    item.ret_qty = item.ret_qty == undefined ? 0 : Number(item.ret_qty);
    //doc id links
    item.oa_id = item.oa_id == undefined || item.oa_id == null ? "": item.oa_id
    item.po_id = item.po_id == undefined? "": item.po_id
    item.dc_id = item.oa_id == undefined? "": item.dc_id
    item.rec_grn_id = item.rec_grn_id == undefined? "": item.rec_grn_id
    item.inspection_log = item.inspection_log == undefined? "[]": item.inspection_log;
    item.rejection_profiles = item.rejection_profiles == undefined? "": item.rejection_profiles;
    item.material_index = `data-material-id="${item.item_id}:${item.make_id}:${item.is_faulty}"`;
    if (this.isGoodsAlreadyReceived) {
        this.createRowForGoodsAlreadyReceived(item=item)
        return
    }
    var taxElements = "";
    var rowPlaceHolder = "";
    var isSerialNumberRequired = false;
    var isPriceRequired = false;
    var rowGroup = "";
    var docCodeElement = "";
    switch(this.receivedAgainst) {
        case "Purchase Order": { // GRN
            rowPlaceHolder = `data-row-id="${item.item_id}:${item.make_id}:${item.is_faulty}:${item.po_id}"`;
            taxElements = this.createTaxElements();
            item.doc_code =  item.po_code == null ? "" : item.po_code;
            isPriceRequired = true;
            rowGroup = `class="${item.po_id}"`
            if(item.doc_code != ""){
                item.pending_qty = item.pending_qty == 0 ? item.pending_qty + item.acc_qty : item.pending_qty;
            }else{
                item.pending_qty = 0.00
            }
            break;
        }
        case "Job Work": { // GRN
            rowPlaceHolder = `data-row-id="${item.item_id}:${item.make_id}:${item.is_faulty}:${item.po_id}"`;
            if (this.tableId == "materialtable") {
                taxElements = this.createTaxElements();
                item.doc_code =  item.po_code == null ? "" : item.po_code;
                isPriceRequired = true;
                rowGroup = `class="${item.po_id}"`
                if(item.doc_code != ""){
                    item.pending_qty = item.pending_qty == 0 ? item.pending_qty + item.acc_qty : item.pending_qty;
                }else{
                    item.pending_qty = 0.00
                }
            } else {
                rowPlaceHolder = `data-row-id="${item.item_id}:${item.make_id}:${item.is_faulty}:${item.po_id}:${item.dc_id}"`;
                item.doc_code =  item.dc_code == null ? "" : item.dc_code;
                var isSerialNumberRequired = true;
                rowGroup = `class="${item.dc_id}"`
                item.pending_qty = item.pending_qty == 0 ? item.issue_qty + item.acc_qty - item.ret_qty : item.pending_qty;
            }
            break;
        }
        case "Job In": { // GRN
            rowPlaceHolder = `data-row-id="${item.item_id}:${item.make_id}:${item.is_faulty}:${item.oa_id}"`;
            item.doc_code =  item.oa_code == null ? "" : item.oa_code;
            isPriceRequired = true;
            rowGroup = `class="${item.oa_id}"`
            docCodeElement = `<select class="form-control td-oa-chosen" name="select_oa_id"> ${$("#id-OANumbers").html()}</select>`
            break;
        }
        case "Delivery Challan": { // GRN
            rowPlaceHolder = `data-row-id="${item.item_id}:${item.make_id}:${item.is_faulty}:${item.dc_id}"`;
            item.doc_code =  item.dc_code == null ? "" : item.dc_code;
            isPriceRequired = false;
            isSerialNumberRequired = true;
            rowGroup = `class="${item.dc_id}"`
            break;
        }
        case "Issues": { // IRN
            rowPlaceHolder = `data-row-id="${item.item_id}:${item.make_id}:${item.is_faulty}:${item.dc_id}"`;
            if (this.tableId == "dc_materialtable") {
                item.doc_code =  item.dc_code == null ? "" : item.dc_code;
                var isSerialNumberRequired = true;
                rowGroup = `class="${item.dc_id}"`
            }
            break;
        }
        case "Sales Return": { // SR
            rowPlaceHolder = `data-row-id="${item.item_id}:${item.make_id}:${item.is_faulty}:${item.dc_id}"`;
            taxElements = this.createTaxElements();
            item.doc_code =  item.dc_code == null ? "" : item.dc_code;
            isPriceRequired = true;
            rowGroup = `class="${item.dc_id}"`
            item.pending_qty = item.pending_qty == 0 ? item.issue_qty + item.acc_qty - item.ret_qty : item.pending_qty;
            break;
        }
        default: { // GRN Others
            rowPlaceHolder = `data-row-id="${item.item_id}:${item.make_id}:${item.is_faulty}"`;
        }
    }

    docCodeElement = docCodeElement == "" ? item.doc_code : docCodeElement;
    var rowElement = $(`#${this.tableId} tbody tr[${rowPlaceHolder}]`);
    if (rowElement.length == 0) {
        var index = $(`#${this.tableId} tbody tr`).length + 1;
        var item_name = item.item_name;
//        if(item.shall_construct_item_name == true && item.mat_type == 0) {
//            if(this.receivedAgainst != "Others" && this.receivedAgainst != "Issues" ) {
//                if (item.drawing_no != 'null' && item.drawing_no != null && item.drawing_no != ''){
//                    item_name = `${item.item_name} - ${item.drawing_no}`;
//                }else{
//                    item_name = `${item.item_name}`;
//                }
//            }
//        }
        if($("#material_is_service").val() == 1 || item.is_service == 1){
            item_name += `<span class="service-item-flag"></span>`;
        }
         var row = `
            <td ${item.material_index}>${item_name}</td>
            <td class="hide">
                <input type="hidden" class="item_id" name="item_id" value="${item.item_id}"/>
                <input type="hidden" name="drawing_no" value="${item.drawing_no}"/>
                <input type="hidden" class="item_description" name="item_name" value="${item_name}" />
                <input type="hidden" name="make_id" value="${item.make_id}"/>
                <input type="hidden" name="is_faulty" value="${item.is_faulty}"/>
                <input type="hidden" name="is_service" value="${item.is_service}"/>
                <input type="hidden" name="alternate_unit_id" value="${item.alternate_unit_id}"/>
                <input type="hidden" name="scale_factor" value="${item.scale_factor}"/>
                <input type="hidden" class="hidden-unit-name" value="${item.unit_name}"/>
                <input type="hidden" class="quantity_inspection_headers" value=''>
                <input type="hidden" name="inspection_log" class="quantity_inspection_review" value='${item.inspection_log}'>
            </td>`;
        if (["Issues", "Others"].indexOf(this.receivedAgainst) != -1 && this.tableId == "materialtable") {
            row = `${row}
                <td>
                    <input ${quantityAttributes} style="text-align:left !important;" name="acc_qty" value ="${item.acc_qty.toFixed(2)}"/>
                    <label class="unit_value pull-right" style=""> ${item.unit_name}</label>
                </td>
                <td class="text-center hide">
                    ${item.unit_name}
                     <a class='edit_quality_inspection table-inline-icon hide' style="right: auto;float: left;left: 0;margin-left: 35px;" data-tooltip='tooltip' data-placement="right" title='Quality Inspection Report' onclick= "openQuantityInspectionReport(this)">
                        <img src='/site_media/images/quality-inspection.png' />
                     </a>
                </td>
                <td class="hsn-wrapper"><input type="text" id="edit_other_hsn_${item.item_id}" name="hsn_code" class="form-control hsn_code" maxlength="9" onchange="validateHsnWithSuggestion(this);" onkeypress="validateStringOnKeyPress(this,event,'hsn_specialChar');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" value="${item.hsn_code}"/>
                </td>
                <td class="text-center"><a href="#" onclick="deleteRow(this)"><i class="fa fa-trash-o"></i> </a></td>
                <td class="hide"> <!-- Additional hidden elements here -->
                <input type="hidden" name="mat_type" value="${item.mat_type}"/>
                <input type="hidden" name="saved_acc_qty" value="${item.acc_qty.toFixed(2)}"/>
                <input type="hidden" name="po_id" value="${item.po_id}"/>
                <input type="hidden" name="dc_id" value="${item.dc_id}"/>
                <input type="hidden" name="oa_id" value="${item.oa_id}"/>
                </td>`;
        } else {
            var serialNumberColumn = "";
            if(isSerialNumberRequired) {
                serialNumberColumn = `<td class="text-center sno_order">${index}</td>`;
            }
            var pendingQuantityElement = "";
            var priceElements = ""
            if (isPriceRequired) {
                priceElements = `
                    <td name="price_col"><input ${priceAttributes} name="price" value ="${item.price}"/></td>
                    <td><input ${percentAttributes} name="discount" value ="${item.discount.toFixed(2)}"></td>
                    <td name="tot_price_col"><input ${inputAttributes} name="totalprice" value ="0.00" disabled="true" onChange="Calculate();"/></td>
                `;
            }
            row = `${serialNumberColumn}
                 <td class="text-center doc_code">${docCodeElement} <a class='edit_quality_inspection table-inline-icon hide' style="right: auto;float: left;left: 0;margin-left: 35px;" data-tooltip='tooltip' data-placement="right" title='Quality Inspection Report' onclick= "openQuantityInspectionReport(this)">
                    <img src='/site_media/images/quality-inspection.png' />
                     </a></td>
                 ${row}
                 ${pendingQuantityElement}
                 <td>
                    <input ${quantityAttributes} name="dc_qty" value="${item.dc_qty.toFixed(3)}" style="text-align:left;"/>
                    <label class="unit_value pull-right"> ${item.unit_name}</label>
                    <span class="text-right td-sub-content bracket-enclosed " name="pending_qty">${($.isNumeric(item.pending_qty))?item.pending_qty.toFixed(3):item.pending_qty}</span>
                 </td>
                 <td>
                    <input ${quantityAttributes} name="rec_qty" value ="${item.rec_qty.toFixed(3)}" style="text-align:left;"/>
                    <label class="unit_value pull-right"> ${item.unit_name}</label>
                    <span class="td-sub-content bracket-enclosed"  name="short_qty" >${(item.dc_qty - item.rec_qty).toFixed(3)}</span>
                 </td>
                 <td>
                    <input ${quantityAttributes} name="acc_qty" value ="${item.acc_qty.toFixed(3)}" style="text-align:left;"/>
                    <label class="unit_value pull-right"> ${item.unit_name}</label>
                    <span class='text-right td_rejection_profile'>
                        <span class="${(item.rec_qty - item.acc_qty)>0? 'link-addon':''}" onclick="javascript:openRejectionProfilePopup(this, true);">
                            <span class="td-sub-content bracket-enclosed" name="rej_qty">${(item.rec_qty - item.acc_qty).toFixed(3)}</span>
                        </span>
                    </span>
                </td>
                <td class="hsn-wrapper"><input  type="text" id="edit_hsn_field_${item.item_id}_${item.dc_id}_${item.make_id}_${index}" name="hsn_code" class="form-control hsn_code" maxlength="9" onchange="validateHsnWithSuggestion(this);" onkeypress="validateStringOnKeyPress(this,event,'hsn_specialChar');" onblur="return validateStringOnBlur(this, event, 'hsn_specialChar');" value="${item.hsn_code}"/>
                </td>
                ${priceElements}
                ${taxElements} <td class="hide"><!-- Additional hidden elements here -->
                    <input type="hidden" name="mat_type" value="${item.mat_type}"/>
                    <input type="hidden" name="item_tax" value ="0"/>
                    <input type="hidden" name="po_id" value="${item.po_id}"/>
                    <input type="hidden" name="unit_id" value="${item.unit_id}"/>
                    <input type="hidden" name="dc_id" value="${item.dc_id}"/>
                    <input type="hidden" name="oa_id" value="${item.oa_id}"/>
                    <input type="hidden" name="rec_grn_id" value="${item.rec_grn_id}"/>
                    <input type="hidden" name="pending_qty" value="${item.pending_qty}" disabled="true"/>
                    <input type="hidden" name="saved_acc_qty" value="${item.saved_acc_qty}" disabled="true"/>
                    <input type="hidden" name="inspection_log" class="quantity_inspection_review" value='${item.inspection_log}' />
                    <input type="hidden" name="rejection_profiles" value="${item.rejection_profiles}" />
                    <input type="hidden" name="is_blanket_po" value="${item.is_blanket_po}"/>
                </td>`;
        }
        row = `<tr ${rowPlaceHolder} ${rowGroup}> ${row} </tr>`;
//      $(`#${this.tableId}`).append(row);
        if($("#material_is_service").val() == 1 || item.is_service == 1){
            $(`#${this.tableId} tbody.item-for-service`).append(row);
            $(".item-for-service").removeClass('hide');
        }
        else {
            $(`#${this.tableId} tbody.item-for-goods`).append(row);
            $(".item-for-goods").removeClass('hide');
        }
        listTableHoverIconsInit(this.tableId);
        var rowElement = $(`#${this.tableId} tbody tr[${rowPlaceHolder}]`);
        if (taxElements != "") {
            $(rowElement).find(`select[name="cgst"]`).val(item.cgst_code);
            $(rowElement).find(`select[name="sgst"]`).val(item.sgst_code);
            $(rowElement).find(`select[name="igst"]`).val(item.igst_code);
        }
        if ($(rowElement).find(`select[name="select_oa_id"]`).length > 0) {
            $(rowElement).find(`select[name="select_oa_id"]`).val(item.oa_id);
        }
        $(".td-oa-chosen").chosen();
        grnMaterialTableSerialNumberInit();
        return {object: rowElement, message: "Success"};
    } else {
        return {object: null, message: "Selected material is already added in the table"};
    }
}

/**
 * Material Table: Row construction for Goods already received case
 */
MaterialTable.prototype.createRowForGoodsAlreadyReceived = function (item={}) {
    var taxElements = "";
    var rowPlaceHolder = "";
    var isPriceRequired = false;
    var consolidatedBy = formatMaterialConsolidatedBy(item.item_id, item.make_id, item.is_faulty);

    switch(this.receivedAgainst) {
        case "Purchase Order": { // GRN
            rowPlaceHolder = `data-row-id="${item.item_id}:${item.make_id}:${item.is_faulty}:${item.po_id}:${item.rec_grn_id}"`;
            taxElements = this.createTaxElements(additionalClasses="consolidated_price_column", onchangeEvents="updateConsolidatedPrice(this);");
            item.doc_code =  item.po_code == null ? "" : `PO No: ${item.po_code}`;
            isPriceRequired = true;
            break;
        }
        case "Job Work": { // GRN
            rowPlaceHolder = `data-row-id="${item.item_id}:${item.make_id}:${item.is_faulty}:${item.po_id}:${item.rec_grn_id}"`;
            if (this.tableId == "materialtable") {
                taxElements = this.createTaxElements(additionalClasses="consolidated_price_column", onchangeEvents="updateConsolidatedPrice(this);");
                item.doc_code =  item.po_code == null ? "" : `JO No: ${item.po_code}`;
                isPriceRequired = true;
            } else {
                rowPlaceHolder = `data-row-id="${item.item_id}:${item.make_id}:${item.is_faulty}:${item.po_id}:${item.dc_id}:${item.rec_grn_id}"`;
                item.doc_code =  item.dc_code == null ? "" : `JDC No: ${item.dc_code}`;
                consolidatedBy = `return_${consolidatedBy}`;
            }
            break;
        }
        case "Sales Return": { // SR
            rowPlaceHolder = `data-row-id="${item.item_id}:${item.make_id}:${item.is_faulty}:${item.dc_id}:${item.rec_grn_id}"`;
            taxElements = this.createTaxElements(additionalClasses="consolidated_price_column", onchangeEvents="updateConsolidatedPrice(this);");
            item.doc_code =  item.dc_code == null ? "" : `INV No: ${item.dc_code}`;
            isPriceRequired = true;
            break;
        }
        default: { // GRN Others
            return
        }
    }
    var consolidatedPlaceHolder = `consolidated-for="${consolidatedBy}"`;
    var consolidatedRowFindKey = `tr[${consolidatedPlaceHolder}]`;
    if(item.is_service == 1){
        var consolidatedRowId = `#${this.tableId} tbody.item-for-service ${consolidatedRowFindKey}`;
        $(".item-for-service").removeClass('hide');
    }
    else{
        var consolidatedRowId = `#${this.tableId} tbody.item-for-goods ${consolidatedRowFindKey}`;
        $(".item-for-goods").removeClass('hide');
    }

    isRowHide = "hide";
    if ($(consolidatedRowId).find(".consolidated_fa").hasClass("fa-chevron-down")) {
        isRowHide = "";
    }
    var priceElements = "";
    if (isPriceRequired) {
        priceElements = `
            <td class="consolidated_price_column"><input ${priceAttributesForConsolidateRow} name="price" value ="${item.price}"/></td>
            <td class="consolidated_price_column"><input ${percentAttributesForConsolidateRow} name="discount" value ="${item.discount.toFixed(2)}"></td>
            <td class="consolidated_price_column" name="tot_price_col"><input ${inputAttributes} name="totalprice" value ="0.00" disabled="true" onChange="Calculate();"/></td>
        `;
    }
    // Consolidated rows
    if($(consolidatedRowId).length == 0) {
        var options="";
         $.each(item.alternate_unit_list, function(i, unit) {
            options = `${options}<option value="${unit.alternate_unit_id}" data-val="${unit.scale_factor}">${unit.unit_name}</option>`;
        });
        if (item.alternate_unit_list.length > 1) {
            options = `<select class='form-control' name='alternate_units' onChange='updateConsolidatedUnit(this); Calculate();'>${options}</select>`;
        } else {
            options = item.unit_name;
        }
        if(item.is_service == 1){
            item.item_name += `<span class="service-item-flag"></span>`;
        }
        var itemNameElement = `<td onclick="consolidatedRowToggle(this)" style="min-width: 200px;">
            <i class="fa fa-chevron-right consolidated_fa" style="float: left; width: 12px;" aria-hidden="true"></i>
            <span class="consolidated_material_name">${item.item_name}</span></td>`;
        row = `<tr ${consolidatedPlaceHolder} class="consolidated_row">
        ${itemNameElement}
        <td class="text-right">
            <span class="total_dc_qty">${item.dc_qty.toFixed(2)}</span>
            <span style="font-size:75%;" class="td-unit-selected-text">${item.unit_name}</span>
        </td>
        <td class="text-right">
            <span class="text-right total_rec_qty">${item.total_rec_qty.toFixed(2)}</span>
            <span style="font-size:75%;" class="td-unit-selected-text">${item.unit_name}</span>
            <span class="text-right total_short_qty td-sub-content bracket-enclosed">${item.total_dc_shortage_qty.toFixed(2)}</span>
        </td>
        <td class="text-right">
           <span class="text-right total_accept_qty">${item.total_acc_qty.toFixed(2)} </span>
           <span style="font-size:75%;" class="td-unit-selected-text">${options}</span>
           <span class="text-right total_reject_qty td-sub-content bracket-enclosed">${item.total_dc_rejected_qty.toFixed(2)}</span>
        </td>
        <td class="consolidated_hsn_code hsn-wrapper"><input ${inputAttributes} maxlength='9' id="edit_already-received_hsn_${item.item_id}_${item.make_id}"  onchange="validateHsnWithSuggestion(this);" onkeypress="validateStringOnKeyPress(this,event,'hsn_specialChar');" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar'); updateConsolidatedPrice(this);" name="hsn_code" value ="${item.hsn_code}"/>
        </td>
        ${priceElements}
        ${taxElements}
        <td class="hide"><!-- Additional hidden elements here -->
            <input type="hidden" name="pending_qty" value="${item.pending_dc_qty}"/>
        </td></tr>`;

//      $(`#${this.tableId} tbody`).append(row);
        if(item.is_service == 1){
            $(`#${this.tableId} tbody.item-for-service`).append(row);
            $(".item-for-service").removeClass('hide');
        }
        else{
            $(`#${this.tableId} tbody.item-for-goods`).append(row);
            $(".item-for-goods").removeClass('hide');
        }
        $(consolidatedRowId).find(`select[name="cgst"]`).val(item.cgst_code);
        $(consolidatedRowId).find(`select[name="sgst"]`).val(item.sgst_code);
        $(consolidatedRowId).find(`select[name="igst"]`).val(item.igst_code);
    } else {
        var existingPoQty = $(consolidatedRowId).find(".total_pending_qty").val();
        $(consolidatedRowId).find(".total_pending_qty").text(item.pending_qty - Number(existingPoQty));
        $(consolidatedRowId).find(`input[name="total_pending_qty"]`).val(item.pending_qty - Number(existingPoQty));
    }
    // General rows
    var rowElement = $(`#${this.tableId} tbody tr[${rowPlaceHolder}]`);
    if (rowElement.length == 0) {
        var hiddenTaxElements = "";
        if (taxElements != "") {
            hiddenTaxElements = `
                <select name="cgst" onchange="Calculate()">${CGST_rates}</select>
                <span type="hidden" name="cgstamt" disabled="true">0.00</span>
                <select name="sgst" onchange="Calculate()">${SGST_rates}</select>
                <span type="hidden" name="sgstamt" disabled="true">0.00</span>
                <select name="igst" onchange="Calculate()">${IGST_rates}</select>
                <span type="hidden" name="igstamt" disabled="true">0.00</span>
            `
        }
        var index = $(`#${this.tableId} tbody tr`).length + 1;
        var row = `
            <td style="padding-left:45px !important;" class="doc_code">${item.doc_code}<br />GRN: ${item.dc_grn_code}</td>
            <td class="hide">
                <input type="hidden" class="item_id" name="item_id" value="${item.item_id}"/>
                <input type="hidden" name="drawing_no" value="${item.drawing_no}"/>
                <input type="hidden" name="dc_id" value="${item.mat_dc_id}"/>
                <input type="hidden" class="item_description" name="item_name" value="${item.item_name}" />
                <input type="hidden" name="make_id" value="${item.make_id}"/>
                <input type="hidden" name="is_faulty" value="${item.is_faulty}"/>
                <input type="hidden" name="is_service" value="${item.is_service}"/>
                <input type="hidden" name="alternate_unit_id" data-default-value='${item.alternate_unit_id}' value="${item.alternate_unit_id}"/>
                <input type="hidden" name="scale_factor" value="${item.scale_factor}"/>
                <input type="hidden" class="quantity_inspection_headers" value=''>
                <input type="hidden" name="inspection_log" class="quantity_inspection_review" value='${item.inspection_log}'>
            </td>`;
        row = `${row}
            <td><input ${quantityAttributesForConsolidateRow} style="text-align:left;" name="dc_qty" data-default-value='${(item.dc_qty * item.scale_factor).toFixed(2)}' value="${(item.dc_qty * item.scale_factor).toFixed(2)}"/>
                 <label class="text-center td-unit-name "><span class="unit_value" style="float: right !important;margin-left: 115px;margin-bottom: 11px;width: 40px;">${item.unit_name}</span> <a class='edit_quality_inspection table-inline-icon hide'style="float: left;left: 0px;right: auto;padding-left: 18px;" data-tooltip='tooltip' data-placement="right" title='Quality Inspection Report' onclick= "openQuantityInspectionReport(this)">
				    <img src='/site_media/images/quality-inspection.png' /></a>
                </label>
            </td>
            <td><input ${quantityAttributesForConsolidateRow} style="text-align:left;" name="rec_qty" data-default-value='${(item.rec_qty * item.scale_factor).toFixed(2)}' value ="${(item.rec_qty * item.scale_factor).toFixed(2)}"/>
                 <label class="text-center td-unit-name "><span class="unit_value" style="float: right !important;margin-left: 115px;margin-bottom: 11px;width: 40px;">${item.unit_name}</span> </label>
                 <span class="td-sub-content bracket-enclosed" name="short_qty" data-default-value='${((item.dc_qty - item.rec_qty)*item.scale_factor).toFixed(2)}'>${((item.dc_qty - item.rec_qty)*item.scale_factor).toFixed(2)}</span>
            </td>
            <td><input ${quantityAttributesForConsolidateRow} style="text-align:left;" name="acc_qty" data-default-value='${(item.acc_qty*item.scale_factor).toFixed(2)}' value ="${(item.acc_qty*item.scale_factor).toFixed(2)}"/>
                <span class='text-right td_rejection_profile'>
                    <span class="${((item.rec_qty - item.acc_qty) * item.scale_factor)>0? 'link-addon':''}" onclick="javascript:openRejectionProfilePopup(this, true);">
                    <label class="text-center td-unit-name "><span class="unit_value" style="float: right !important;margin-left: 115px;margin-bottom: 11px;width: 40px;">${item.unit_name}</span></label>
                        <span class="td-sub-content bracket-enclosed" data-default-value='${((item.rec_qty - item.acc_qty) * item.scale_factor).toFixed(2)}' name="rej_qty">${((item.rec_qty - item.acc_qty) * item.scale_factor).toFixed(2)}</span>
                    </a>
                </span>
            </td>
			<td><input type="text" class="form-control text_box_label text-right hsn_code" tabindex="-1" name="hsn_code" data-default-value='${item.hsn_code}' value="${item.hsn_code}" readonly /></td>
            <td class="hide"><!-- Additional hidden elements here -->
                <input type="hidden" name="price" data-default-value='${item.price}' value ="${item.price}"/>
                <input type="hidden" name="discount" data-default-value='${item.discount.toFixed(2)}' value ="${item.discount.toFixed(2)}"/>
                <input type="hidden" name="totalprice" value ="0.00" disabled="true" onChange="Calculate();"/>
                ${hiddenTaxElements}
                <input type="hidden" name="mat_type" value="${item.mat_type}"/>
                <input type="hidden" name="item_tax" value ="0"/>
                <input type="hidden" name="po_id" value="${item.po_id}"/>
                <input type="hidden" name="unit_id" value="${item.unit_id}"/>
                <input type="hidden" name="dc_id" value="${item.dc_id}"/>
                <input type="hidden" name="oa_id" value="${item.oa_id}"/>
                <input type="hidden" name="rec_grn_id" value="${item.rec_grn_id}"/>
                <input type="hidden" name="pending_qty" data-default-value='${item.pending_dc_qty*item.scale_factor}' value="${item.pending_dc_qty*item.scale_factor}" disabled="true">
                <input type="hidden" name="saved_acc_qty" data-default-value='${item.saved_acc_qty*item.scale_factor}' value="${item.saved_acc_qty*item.scale_factor}" disabled="true">
                <input type="hidden" name="rejection_profiles" value="${item.rejection_profiles}" />
                <td class="text-right total_dc_qty hide" data-default-value='${(item.pending_dc_qty*item.scale_factor).toFixed(2)}'>${(item.pending_dc_qty*item.scale_factor).toFixed(2)}</td>
                <td class="text-right total_rec_qty hide" data-default-value='${(item.total_rec_qty*item.scale_factor).toFixed(2)}'>${(item.total_rec_qty*item.scale_factor).toFixed(2)}</td>
                <td class="text-right total_short_qty hide" data-default-value='${(item.total_dc_shortage_qty*item.scale_factor).toFixed(2)}'>${(item.total_dc_shortage_qty*item.scale_factor).toFixed(2)}</td>
                <td class="text-right total_accept_qty hide" data-default-value='${(item.total_acc_qty*item.scale_factor).toFixed(2)}'>${(item.total_acc_qty*item.scale_factor).toFixed(2)}</td>
                <td class="text-right total_reject_qty hide" data-default-value='${(item.total_dc_rejected_qty*item.scale_factor).toFixed(2)}'>${(item.total_dc_rejected_qty*item.scale_factor).toFixed(2)}</td>
            </td>`;
        row = `<tr ${rowPlaceHolder} class="${consolidatedBy} ${isRowHide} ${item.rec_grn_id}" data-row="${consolidatedBy}"> ${row} </tr>`;
        if($(`#${this.tableId} tbody tr.${consolidatedBy}`).length > 0) {
            $(row).insertAfter(consolidatedRowId);
        } else {
            if(item.is_service == 1){
                $(`#${this.tableId} tbody.item-for-service`).append(row);
                $(".item-for-service").removeClass('hide');
            }
            else{
                $(`#${this.tableId} tbody.item-for-goods`).append(row);
                $(".item-for-goods").removeClass('hide');
            }

        }

        listTableHoverIconsInit(this.tableId);
        var rowElement = $(`#${this.tableId} tbody tr[${rowPlaceHolder}]`);
        rowElement.find(`select[name="cgst"]`).val(item.cgst_code);
        rowElement.find(`select[name="sgst"]`).val(item.sgst_code);
        rowElement.find(`select[name="igst"]`).val(item.igst_code);

        var rowSpanCount = $(`#${this.tableId} .${consolidatedBy}`).length + 1;
        var updateRow = $(consolidatedRowId).find(".consolidated_price_column").attr("data-rowspan", rowSpanCount);
        if($(consolidatedRowId).find(".consolidated_fa").hasClass("fa-chevron-down")) {
            updateRow.attr("rowspan", rowSpanCount);
        }
        $(".price_column").addClass("hide");
        $(".td-material").addClass("hide");
        updateConsolidatedQty($(`.${consolidatedBy}`));
        updateConsolidatedHeaderUnit($(`.${consolidatedBy}`));
        $("#materialtable").find("tr[consolidated-for='"+consolidatedBy+"']").find(`input[name="hsn_code"]`).trigger("blur");
        $(`.${consolidatedBy} input[name="dc_qty"]`).trigger("blur");
        return {object: rowElement, message: "Success"};
    } else {
        return {object: null, message: "Selected material is already added in the table"};
    }
}
function grnMaterialTableSerialNumberInit() {
    $("#dc_materialtable tbody.item-for-goods tr:visible, #dc_materialtable tbody.item-for-service tr:visible").each(function(i) {
       $(this).find(".sno_order").text(i+1+".")
    })
}