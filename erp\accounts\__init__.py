"""
"""
import logging

__author__ = 'saravanan'

logger = logging.getLogger(__name__)

TRIAL_BALANCE = "TRIAL_BALANCE"
ACCOUNT_NOTE_VALUES = "ACCOUNT_NOTE_VALUES"
ACCOUNT_NOTES = "ACCOUNT_NOTES"
ACCOUNT_BOOK = "ACCOUNT_BOOK"

STOCK_TYPE_WIP = "WIP"
STOCK_TYPE_FG = "FG"

STATEMENTS_URL = "/erp/accounts/trial_balance/"

# Ledger Book Names
PACKING_ACCOUNT_NAME = "Packing & Forwarding"
TRANSPORT_ACCOUNT_NAME = "Transport & Freight"
ROUND_OFF_ACCOUNT_NAME = "Round-off"
OTHER_CHARGES_ACCOUNT_NAME = "Other Charges"

# Account Group Name Constants
SALES_ACCOUNT_GROUP_NAME = "Sales Account"
PURCHASE_ACCOUNT_GROUP_NAME = "Purchase Account"

# Account Group IDs
OTHER_INDIRECT_EXPENSES_GROUP_ID = 4
PURCHASE_ACCOUNT_GROUP_ID = 7
SALES_ACCOUNT_GROUP_ID = 8
OTHER_DIRECT_EXPENSES_GROUP_ID = 31
OTHER_DIRECT_INCOME_GROUP_ID = 50
SUNDRY_DEBTORS_GROUP_ID = 23
SUNDRY_CREDITORS_GROUP_ID = 24
OTHER_CURRENT_LIABILITIES = 41
OTHER_CURRENT_ASSETS = 49

# Voucher type Constants
GENERAL_VOUCHER = 1
CASH_VOUCHER = 2
BANK_VOUCHER = 3
PURCHASE_VOUCHER = 4
SALES_VOUCHER = 5
NOTE_VOUCHER = 6
BOOK_CLOSURE_VOUCHER = 7