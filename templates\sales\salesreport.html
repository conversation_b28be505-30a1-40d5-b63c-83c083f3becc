{% extends "sales/sidebar.html" %}
{% block sales_reports %}
    <link type="text/css" rel="stylesheet" href="/site_media/css/chosen.css?v={{ current_version }}">
    <script type="text/javascript" src="/site_media/js/bootstrap-datepicker.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/bootstrap-multiselect.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/chosen.jquery.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/jquery.easyResponsiveTabs.js?v={{ current_version }}"></script>
    <script type="text/javascript" src="/site_media/js/daterangepicker.js?v={{ current_version }}"></script>

    <style xmlns="http://www.w3.org/1999/html">
        li.po_reports_side_menu a{
            outline: none;
            background-color: #e6983c !important;
        }

        .loading-main-container {
            display: block;
        }

        .dataTables_scrollBody {
            overflow: auto hidden !important;
        }

        .dataTables_scrollBody table{
            margin-bottom: 20px;
        }
    </style>

<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>

    <div class="right-content-container download-icon-onTop">
        <div class="page-title-container">
            <span class="page-title">Sales Report</span>
        </div>
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12">
                    <div class="content_bg">
                        <ul class="resp-tabs-list hor_1"></ul>
                        <div class="resp-tabs-container hor_1">
                            <div class="row">
                                <div class="add_table">
                                    <div class="col-lg-12 add_table_content">
                                        <div class="filter-components">
                                            <div class="filter-components-container">
                                                <div class="dropdown">
                                                    <button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
                                                        <i class="fa fa-filter"></i>
                                                    </button>
                                                    <span class="dropdown-menu arrow_box arrow_box_filter">
                                                        <div class="col-sm-12 form-group" >
                                                            <label>Date Range</label>
                                                            <div id="reportrange" class="report-range form-control">
                                                                <i class="glyphicon glyphicon-calendar"></i>&nbsp;
                                                                <span></span> <b class="caret"></b>
                                                                <input type="hidden" class="fromdate" id="fromdate"
                                                                       name="from_date" value="{{from_date}}"/>
                                                                <input type="hidden" class="todate" id="todate" name="to_date" value="{{to_date}}"/>
                                                            </div>
                                                        </div>
                                                        <div class="col-sm-12 form-group">
                                                            <label>Supplier</label>
                                                            <select class="form-control chosen-select" name="select"
                                                                    id="cmbsupplier">
                                                                {% for j in suppliers %}
                                                                    <option value="{{ j.0 }}">{{ j.1 }}</option>
                                                                {% endfor %}
                                                            </select>
                                                        </div>
                                                        <div class="col-sm-12 form-group">
                                                            <label>Material Name</label>
                                                            <select class="form-control chosen-select" name="select"
                                                                    id="cmbmaterial">
                                                                <option value="">All</option>
                                                                {% for j in materials %}
                                                                    <option value="{{ j.item_id }}">{{ j.name }}</option>
                                                                {% endfor %}
                                                            </select>
                                                        </div>
                                                        <div class="col-sm-12 form-group">
                                                            <div class="checkbox" style="padding-left: 22px !important;">
                                                                <input name="approved_only" id="id_approved_only" type="checkbox"/>
                                                                <label for="id_approved_only">Exclude Drafts</label>
                                                            </div>
                                                        </div>
                                                        <div class="filter-footer">
                                                            <input type="button" class="btn btn-save" value="Apply" id="salesreportview"/>
                                                        </div>
                                                    </span>
                                                </div>
                                                <span class='filtered-condition filtered-date'>Date: <b></b></span>
                                                <span class='filtered-condition filtered-party'>Supplier Name: <b></b></span>
                                                <span class='filtered-condition filtered-material'>Material Name: <b></b></span>
                                                <span class='filtered-condition filtered-draft'>Draft Excluded: <b></b></span>
                                            </div>
                                        </div>
										<div id="grn_report">
                                            <div class="col-lg-12">
                                                <div class="search_result_table">
                                                    <div class="csv_export_button">
                                                        <a role="button" class="btn btn-add-new pull-right export_csv for-primary-ent" onclick="GeneralExportTableToCSV.apply(this, [$('#tablesorter'), 'Sales_Report.csv']);" data-tooltip="tooltip" data-placement="bottom" title="Download&nbsp;Sales&nbsp;Report as&nbsp;CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
                                                        <a role="button" class="btn btn-add-new pull-right export_csv hide" id="for-sec-ent" onclick="GeneralExportTableToCSV.apply(this, [$('#tablesorter'), 'InternalInvoice_Report.csv']);" data-tooltip="tooltip" data-placement="bottom" title="Download&nbsp;Internal Invoice&nbsp;Report as&nbsp;CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
                                                    </div>
                                                    <table class="table table-bordered custom-table table-striped grn_tbl" id="tablesorter" style="width: 100%;">
                                                        <thead class="th-vertical-center">
                                                        <tr class="exclude_export">
                                                            <th rowspan="2" style="min-width: 40px;">S. No</th>
                                                            <th rowspan="2" style="min-width: 100px;">Invoice No</th>
                                                            <th rowspan="2" style="min-width: 80px;">Invoice Date</th>
                                                            <th rowspan="2" style="min-width: 160px;">Party Name</th>
                                                            <th rowspan="2" style="min-width: 160px;">Material</th>
                                                            <th rowspan="2" style="min-width: 160px;">Drawing No</th>
                                                            <th rowspan="2" style="min-width: 60px;">Unit</th>
                                                            <th rowspan="2" style="min-width: 60px;">Quantity</th>
                                                            <th name="price_col" rowspan="2" style="min-width: 60px;">Price/ Unit</th>
                                                            <th name="price_col" rowspan="2" style="min-width: 60px;">Disc. (%)</th>
                                                            <th rowspan="2" style="min-width: 60px;">Total Price</th>
                                                            <th class="for-primary-ent" colspan="2" style="min-width: 60px;">CGST</th>
                                                            <th class="for-primary-ent" colspan="2" style="min-width: 60px;">SGST</th>
                                                            <th class="for-primary-ent" colspan="2" style="min-width: 60px;">IGST</th>
                                                        </tr>
                                                        <tr class="exclude_export">
                                                            <th class="for-primary-ent" style="font-size:10px !important;">Rate</th>
                                                            <th class="for-primary-ent" style="font-size:10px !important;">Amount</th>
                                                            <th class="for-primary-ent" style="font-size:10px !important;">Rate</th>
                                                            <th class="for-primary-ent" style="font-size:10px !important;">Amount</th>
                                                            <th class="for-primary-ent" style="font-size:10px !important;">Rate</th>
                                                            <th class="for-primary-ent" style="font-size:10px !important;">Amount</th>
                                                        </tr>
                                                        <!-- This <tr> is used for csv download. Don't delete -->
                                                        <tr>
                                                            <th hidden="hidden">S. No</th>
                                                            <th hidden="hidden">Invoice No</th>
                                                            <th hidden="hidden">Invoice Date</th>
                                                            <th hidden="hidden">Party Name</th>
                                                            <th hidden="hidden">Material</th>
                                                             <th hidden="hidden">Drawing No</th>
                                                            <th hidden="hidden">Unit</th>
                                                            <th hidden="hidden">Quantity</th>
                                                            <th hidden="hidden">Price/ Unit</th>
                                                            <th hidden="hidden">Disc. (%)</th>
                                                            <th hidden="hidden">Total Price</th>
                                                            <th hidden="hidden">CGST-Rate</th>
                                                            <th hidden="hidden">CGST-Amount</th>
                                                            <th hidden="hidden">SGST-Rate</th>
                                                            <th hidden="hidden">SGST-Amount</th>
                                                            <th hidden="hidden">IGST-Rate</th>
                                                            <th hidden="hidden">IGST-Amount</th>
                                                        </tr>
                                                        </thead>
                                                        <tbody id="grn_tbl_tbody">
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            {% block stock_statement %}
                            {% endblock %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
    <script>
        $(document).ready(function () {
            setTimeout(function () {
                $("#salesreportview").trigger('click');
            }, 5);
        });

        $(window).load(function(){
            updateFilterText();
            var project = JSON.parse(localStorage.getItem('project'));
            if(project && project.type == 'Secondary'){
                $('.page-title').text('Internal Invoice Report');
                $('#for-sec-ent').removeClass('hide');
            }
        });

        function updateFilterText() {
            $(".filtered-date b").text($("#reportrange").find("span").text());
            $(".filtered-party b").text($("#cmbsupplier option:selected").text());
            $(".filtered-material b").text($("#cmbmaterial option:selected").text());
            $(".filtered-draft b").text($("#id_approved_only").is(":checked")?"Yes":"No");
        }

        var oTable;
        var oSettings;
        function TableHeaderFixed() {
            oTable = $('#tablesorter').DataTable({
            fixedHeader: false,
            "pageLength": 50,
            "scrollY": Number($(document).height() - 230),
            "scrollX": true,
            "orderCellsTop": true,
            "search": {
                "smart": false
            },
            "columns": [
                null,null,          
                { "type": "date" },
                null,null,null,null,
                null,null,null,null,
                null,null,null,null,
                null,null
                ]
            });
            oTable.on("draw",function() {
                var keyword = $('#tablesorter_filter > label:eq(0) > input').val();
                $('#tablesorter').unmark();
                $('#tablesorter').mark(keyword,{});
                setHeightForTable();
            });
            oTable.on('page.dt', function() {
              $('html, body').animate({
                scrollTop: $(".dataTables_wrapper").offset().top - 15
               }, 'slow');
            });
            oSettings = oTable.settings();
            $( window ).resize();
        }

        function loopTblData(data){
            var row = '';
            if(data.length != 0){
                $.each(data, function (key, value) {
				if(value.invoice_date !="" && value.invoice_date !="None") {
					var setDateTime = value.invoice_date.split(' ');
					var setdateFormat = setDateTime[0].split('-');
					setdateFormat = setdateFormat[1]+"/"+setdateFormat[2]+"/"+setdateFormat[0];
                    setdateFormat = moment(setdateFormat).format('MMM D, YYYY')
				}
				else {
					var setdateFormat="-";
				}
                row += "<tr bgcolor='#ececec' border='0' align='center' style='font-size:16px; font-weight:normal;'>" +
                    "<td>" + ++key + "</td>" +
                    "<td>" + value.invoice_no + "</td>" +
                    "<td>" + setdateFormat + "</td>" +
                    "<td class='text-left'>" + value.party_name + "</td>" +
                    "<td class='text-left'>" + value.invoice_material + "</td>" +
                    "<td class='text-left'>" + value.drawing_no + "</td>" +
                    "<td>" + value.unit + "</td>" +
                    "<td class='text-right'>" + value.qty + "</td>" +
                    "<td class='text-right'>" + value.rate + "</td>" +
                    "<td class='text-right'>" + value.disc + "</td>" +
                    "<td class='text-right'>" + value.gtotal + "</td>" +
                    "<td class='text-right for-primary-ent'>" + (value.CGST_rate == undefined ? "0" : value.CGST_rate) + "</td>" +
                    "<td class='text-right for-primary-ent'>" + (value.CGST == undefined ? "0" : value.CGST) + "</td>" +
                    "<td class='text-right for-primary-ent'>" + (value.SGST_rate == undefined ? "0" : value.SGST_rate) + "</td>" +
                    "<td class='text-right for-primary-ent'>" + (value.SGST == undefined ? "0" : value.SGST) + "</td>" +
                    "<td class='text-right for-primary-ent'>" + (value.IGST_rate == undefined ? "0" : value.IGST_rate) + "</td>" +
                    "<td class='text-right for-primary-ent'>" + (value.IGST == undefined ? "0" : value.IGST) + "</td>" +
                    "</tr>";
                });
            }
            $('#grn_tbl_tbody').html(row);
            TableHeaderFixed();
            updateFilterText();
            closeFilterOption();
            $("#loading").addClass('hide');
        }

        $("#salesreportview").click(function () {
            $("#loading").removeClass('hide');
            from_date = $('.fromdate').val();
            to_date = $('.todate').val();
            supplier = $('#cmbsupplier').val();
            material = $('#cmbmaterial').val();
            approved_only = $('#id_approved_only:checkbox:checked').length>0;
            $.ajax({
                    url : "/erp/stores/json/salesreport/",
                    type : "POST",
                    dataType: "json",
                    data : {
                        from_date: from_date,
                        to_date: to_date,
                        supplier: supplier,
                        material : material,
                        approved_only: approved_only
                    },
                    success : function(data) {
                        if(oTable != undefined) {
                            oTable.destroy();
                        }
                        loopTblData(data);
                    },error : function(xhr,errmsg,err) {
                        console.log(xhr.status + ": " + xhr.responseText);
                        $("#loading").addClass('hide');
                    }
            });
        });

        $(document).ready(function(){
	var url=window.location.href;
	url = url.substring(7)
	window.urlpath = url.substring(0,url.indexOf('/'));
	//used to format the date displayed
	/*$( "#fromdate" ).datepicker({
		dateFormat: "yy-mm-dd",
		autoclose: true,
		orientation: 'bottom auto'
	});
	$('#fromdate').datepicker('setDate', '-30d');
	$( "#todate" ).datepicker({
		dateFormat: "yy-mm-dd",
		autoclose: true,
		orientation: 'bottom auto'
	});
	$('#todate').datepicker('setDate', '+0');
	$('#todate').datepicker('setStartDate', '-30d');*/
	$("#reporttype").removeAttr("disabled");
//  loadsuppliers()
//  loadmaterials()
	FromToDateValidation();
	$(".chosen-select").chosen();
});

$('.nav-pills li').removeClass('active');
$("#li_sales_report").addClass('active');
$(".slide_container_part").removeClass('selected');
$("#menu_sales").addClass('selected');

function FromToDateValidation(){
	$('#fromdate').on('changeDate', function(){
		$('#todate').datepicker('setStartDate', new Date($(this).val()));
		var start = new Date($('#fromdate').val()),
		end   = new Date($('#todate').val()),
		diff  = new Date(end - start),
		days  = diff/1000/60/60/24;
		if(days < 0) {
			$('#todate').datepicker('update', $('#fromdate').val());
		}
	});
}
    </script>
{% endblock %}