function setCookie(fromdate, todate, party, project, status) {
    var expires = "";
    var from_date = fromdate;
    var to_date = todate;
    var party_id = party;
    var project_id = project;
    var status = status;
    document.cookie = "from_date="+ fromdate + "; path=/;";
    document.cookie = "to_date="+ todate + "; path=/;";
    document.cookie = "party_id="+ party + "; path=/;";
    document.cookie = "party_name="+ $( "#party option:selected" ).text() + "; path=/;"
    document.cookie = "project_id="+ project + "; path=/;"
    document.cookie = "status="+ status + "; path=/;"
}

function getCookie(name) {
    var nameEQ = name + "=";
    var ca = document.cookie.split(';');
    for(var i=0;i < ca.length;i++) {
        var c = ca[i];
        while (c.charAt(0)==' ') c = c.substring(1,c.length);
        if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length,c.length);
    }
    return null;
}

$(function() {
	$('#cmdadd').click(function () {
		addupdate(1);
	});

	$("#loadgrns").click(function() {
        setCookie($("#fromdate").val(), $("#todate").val(), $("#party option:selected").val(), $("#project option:selected").val(), $("#search_status").val());
		loadgrns();
	});
    $('#cmdSuperUpdate').click(function () {
        $("#loading").show();
        $.ajax({
            url: "/erp/stores/json/get_receipt_linked_message/",
            method: "POST",
            data:{receipt_no: $("#receipt_no").val()},
            success: function(response) {
                $("#loading").hide();
                if (response.response_message =="Success") {
                    var invalid_quantity = false
                    $("#note_item_table").find(".validate-icd-qty:visible").each(function(){
                        if($(this).val() == 0) {
                            $(this).addClass("error-border");
                            invalid_quantity = true;
                        }
                        else {
                            $(this).removeClass("error-border");
                        }
                    });

                    $("#note_item_table").find(".validate-icd-rate:visible").each(function(){
                        if($(this).val() == 0) {
                            $(this).addClass("error-border");
                            invalid_quantity = true;
                        }
                        else {
                            $(this).removeClass("error-border");
                        }
                    });
                     $("#drcrtable").find(".validate-icd-rate:visible").each(function(){
                        if($(this).val() == 0) {
                            $(this).addClass("error-border");
                            invalid_quantity = true;
                        }
                        else {
                            $(this).removeClass("error-border");
                        }
                    });

                    if(invalid_quantity && $("#id-received_against").val() == "Note") {
                        swal({title: "", text:"Any one or more Quantity/ Rate is seems to be Zero. Please edit the Quantity/ Rate field to continue.", type: "warning"});
                        return false;
                    }else if(response.custom_message == "") {
                         insert_details($("#note_status").val(), true);
                    } else {
                        swal({
                            title: "",
                            text: response.custom_message,
                            type: "warning",
                            showCancelButton: true,
                            confirmButtonColor: "#209be1",
                            confirmButtonText: "Yes, do it!",
                            closeOnConfirm: true,
                            closeOnCancel: true
                        },
                        function(isConfirm) {
                            if (isConfirm) {
                                insert_details($("#note_status").val(), true);
                            }
                        });
                    }
                } else {
                    swal({title: "", text: response.custom_message, type: "error"});
                }
            },
            error: function (xhr, errmsg, err) {
                $("#loading").hide();
                swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
            }
        });
	});
	$('#cmdupdate, #cmdupdate_up').click(function () {
		insert_details(2, false);
	});

	$('#cmdverify').click(function() {
        $("#loading").show();
        var icd_id = $('#id-receipt_no').val();
        var note_id = $('#id-note_id').val();
        var icd_remarks = $('#id-remarks').val();
        $.ajax({
            url: "/erp/auditing/json/verifyNote/",
            type: "post",
            datatype: "json",
            data: {grn_number:icd_id, note_id:note_id, icd_remarks:icd_remarks},
            success: function (response) {
                $("#loading").hide();
                var message_type = 'warning';
                if (response.response_message =="Success") {
                    message_type = 'success';
                }
                swal({title: "", text: response.custom_message, type: message_type,
                        showCancelButton: false,
                        confirmButtonColor: "#209be1",
                        confirmButtonText: "Ok",
                        closeOnConfirm: true,
                        closeOnCancel: true
                },
                function() {
                    if(message_type == 'success'){
                        ga('send', 'event', 'Cr/Dr Note', 'Verify', $('#enterprise_label').val(), 1);
                        updateTableStatus(3, icd_id, note_id);
                        generate_pdf_ajax(icd_id, 3, note_id);
                    }
                    //$("#resubmit_icd_note").click();
                });
            },
            error: function(){
                $("#loading").hide();
                swal("", response.custom_message, "warning")
            }
        });
    });

    $('#receipt_tap').click(function() {
        //GRN Generated Function
        $.ajax({
            url: "/erp/stores/json/grn/loadgrnPDF/",
            type: "post",
            datatype: "json",
            data: {grn_id: $('#receipt_no').val()},
            success: function (json) {
                grn_doc = document.getElementById("grn");
                grn_doc.setAttribute('data',json);
                $("#grn").html(getPdfErrorMessage());
            },
        });
    });

    $('#po_tap').click(function() {
        //PO Generated Function
        $.ajax({
            url: "/erp/auditing/json/icd/load_grn_pos_doc/",
            type: "post",
            datatype: "json",
            data: {receipt_no: $('#receipt_no').val()},
            success: function (json) {
                po_doc = document.getElementById("po");
                po_doc.setAttribute('data',json);
                $("#po").html(getPdfErrorMessage());
            },
        });
    });

    $('#invoice_tap').click(function() {
        //Invoice Generated Function
        $.ajax({
            url: "/erp/auditing/json/icd/load_invoice_doc/",
            type: "post",
            datatype: "json",
            data: {receipt_no: $("#receipt_no").val()},
            success: function (json) {
                invoice_doc = document.getElementById("invoice_data");
                invoice_doc.setAttribute('data',json);
                $("#invoice_data").html(getPdfErrorMessage());
            },
        });
    });

    CGST_rates="";
    SGST_rates="";
    IGST_rates="";

    $.ajax({
        url: "/erp/purchase/json/po/load_gst_tax/",
        type: "post",
        datatype:"json",
        data: {gst_type: "CGST"},
        success: function(response){
            CGST_rates += "<option value=0></option>";
            for (i = 0; i <= response.length - 1 ; i++) {
                CGST_rates += "<option value='" + response[i][0] + "'> " + response[i][2] + " </option>";
            }
        }
    });
    $.ajax({
        url: "/erp/purchase/json/po/load_gst_tax/",
        type: "post",
        datatype:"json",
        data: {gst_type: "SGST"},
        success: function(response){
            SGST_rates += "<option value=0></option>";
            for (i = 0; i <= response.length - 1 ; i++) {
                SGST_rates += "<option value='" + response[i][0] + "'> " + response[i][2] + " </option>";
            }
        }
    });

    $.ajax({
        url: "/erp/purchase/json/po/load_gst_tax/",
        type: "post",
        datatype:"json",
        data: {gst_type: "IGST"},
        success: function(response){
            IGST_rates += "<option value=0></option>";
            for (i = 0; i <= response.length - 1 ; i++) {
                IGST_rates += "<option value='" + response[i][0] + "'> " + response[i][2] + " </option>";
            }
        }
    });

	$('#upload_invoice_btn').click(function(){
		var regex = /^([a-zA-Z0-9\s_\\.\-:])+(.csv|.txt|.pdf|.jpg|.png)$/;
		var file_extension = ($("#invoice_uploader").val().toLowerCase()).match(/(.docx|.doc|.pdf|.jpg|.png)$/);
		var receipt_no = $('#receipt_no').val();
        if (regex.test($("#invoice_uploader").val().toLowerCase())) {
	        if (typeof (FileReader) != "undefined") {
                var reader = new FileReader();
                reader.readAsBinaryString($("#invoice_uploader")[0].files[0]);
                reader.onload = function (e) {
                    var file_data = "" + e.target.result;
                    //alert(file_data);
					$.ajax({
						url: "/erp/auditing/json/icd/upload_invoice/",
						type: "POST",
						dataType: "json",
						data: {receipt_no: receipt_no, document_ext: file_extension, document_data: file_data},
						success: function(json) {
							//alert(json);
							editrow(receipt_no, 1);
						},
						error : function(xhr,errmsg,err) {
							//console.log(xhr.status + ": " + xhr.responseText);
						}
					});
                }
            } else {
                alert("This browser does not support HTML5.");
            }
        } else {
            alert("Please upload a PDF/DOC/DOCX/JPG/PNG file.");
        }
    });

    $("#add_grn_tag").click(function(){
		var tag_code = $("#grn_tag_value").val();
		var tag_text = $("#id_grn_tag").val();

		if (tag_code == "") {
			if (tag_text == "") {
				return;
            }
            $(".li-tagit-display").removeClass('flash');
            var currentFieldName = $("#id_grn_tag").val();
            $("#grn_tags_table").find('.li-tagit-display:visible').each(function(){
                if(currentFieldName.toLowerCase().trim() == $(this).find('.label-tagit-display').text().toLowerCase().trim()){
                    $('#id_grn_tag').val('');
                    $(this).addClass('flash');
                    return;
                }
            });
            if($("#id_grn_tag").val().trim() !="") {
                var row = "<li class='li-tagit-display'>" +
                            "<label class='label-tagit-display'>"+$("#id_grn_tag").val()+"</label>"+
                            "<div class='hidden-div-tagit-id' hidden='hidden'>0</div>"+
                            "<a class='delete_tag'></a>"  + "</li>";
                $('#grn_tags_table').append(row).addClass('tbl');
            }
        }
        else {
            $(".li-tagit-display").removeClass('flash');
            var currentFieldName = $("#id_grn_tag").val();
            $("#grn_tags_table").find('.li-tagit-display:visible').each(function(){
                if(currentFieldName.toLowerCase().trim() == $(this).find('.label-tagit-display').text().toLowerCase().trim()){
                    $('#grn_tag_value').val('');
                    $('#id_grn_tag').val('');
                    $(this).addClass('flash');
                    return;
                }
            });
            if($("#id_grn_tag").val().trim() !="") {
                var row = "<li class='li-tagit-display'>" +
                            "<label class='label-tagit-display'>"+$("#id_grn_tag").val()+"</label>"+
                            "<div class='hidden-div-tagit-id' hidden='hidden'>"+ $("#grn_tag_value").val() +"</div>"+
                            "<a class='delete_tag' ></a>"  + "</li>";
                $('#grn_tags_table').append(row).addClass('tbl');
            }
		}
		$('#grn_tag_value').val('');
		$('#id_grn_tag').val('');
        create_delete_tag_button();
	});
});

function CancelButtonClick() {
    $('#template_title').text(getPageTitle());
    //$('#edit_icd').hide();
    //$('#view_icd').show();
    AccordionGroup();
    TableHeaderFixed();
    $(".page-title").html(getPageTitle());
    $("#cmdcancel_up").addClass('hide');
    $(".single-eInvoice").addClass("hide");
    $(".multiple-eInvoice").removeClass("hide");
    $(".export_csv, #note_creation").removeClass('hide');
    ICDSuperEditInit();
    location.reload();
}

function AccordionGroup() {
	$("#collapseTwo").addClass('in').removeAttr('style');
	$("#collapseThree").removeClass('in');
	$("#collapseOne").removeClass('in');
	$("#collapseFour").removeClass('in');
	$("#collapseFive").removeClass('in');
	$(".accordion-toggle").attr('aria-expanded','false');
	$("#auditNoteID").attr('aria-expanded','true');
}

$(document).ready(function(){
	var url=window.location.href;
	url = url.substring(7)
	window.urlpath = url.substring(0,url.indexOf('/'));
	$("#quotdate").datepicker({dateFormat: "yy-mm-dd"});
	$("#quotdate").datepicker('setDate', '+0');

	$('#table_tab h2:first').click();

	$("#add_note_tax").click(function(){
        tax_code = $("#id_note_tax option:selected").val();
        if (tax_code != "") {
            addPoTax(tax_code);
        }
		$('.chosen-select').trigger('chosen:updated');
	});

	if ($('#grand_total').val() != "0.00") {
	    $('#audit_note_total_div').show()
	}
	else {
	    $('#audit_note_total_div').hide()
	}
    $('#id-collapse_document').text("Party Invoice");
    $('#id-invoice_data').removeClass('hide');
    checkICDNote();
});

function ICDVerifiedCheck() {
	$(".multi_check").on("click",function() {
		if($(".multi_check").length == $(".multi_check:checked").length) {
			$('#cmdupdate').prop('disabled',false);
			$('#cmdupdate_up').prop('disabled',false);
		} else {
			$('#cmdupdate').prop('disabled',true);
			$('#cmdupdate_up').prop('disabled',true);
		}
	});
}

function addRow() {
    var rowcount = document.getElementById('drcrtable').rows.length-1;
    var row = ` <tr>
                    <td class='text-center'>${rowcount}</td>
                    <td>
                        <input type='text' id='txtDescription' name='otherDescription' class='form-control' value='' placeholder='Enter Description' maxlength='150' onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" />
                    </td>
                    <td>
                        <input type='text' hidden='hidden' id='txtReason' name='otherReason' value='Others'/>
                        Others
                    </td>
                    <td>
                        <input type='text' id='txtQty' name='otherQty' class='form-control text-right' value='0.00' maxlength='16' onfocus='setNumberRangeOnFocus(this,12,3)' onblur='Calculate()' />
                    </td>
                    <td class="hsn-wrapper">
                        <input type="text" class="form-control text-right td-hsn-code" id="other_inline_hsn_code${i}_${rowcount}" name="othersHsn_code" value="" maxlength="9" onkeypress='validateStringOnKeyPress(this,event, "hsn_specialChar")' onblur='return validateStringOnBlur(this, event, "alphaSpecialChar");' onchange="validateHsnWithSuggestion(this);">
                    </td>
                    <td>
                        <input type='text' id='txtRate' name='otherRate' class='form-control text-right' value='0.00' maxlength='19' onfocus='setNumberRangeOnFocus(this,13,5)' onBlur='Calculate()' />
                        <span class='checkbox checkbox-border' style='padding-top: 0 !important; padding-bottom: 0 !important;'>
                            <input name='is_credit' id='id_is_credit_${rowcount}' type='checkbox' onChange='Calculate()'>
                            <label for='id_is_credit_${rowcount}'>Is Credit</label>
                        </span>
                    </td>
                    <td>
                        <input type='text' id='txtAmount' name='otherAmount' class='form-control text-right' disabled='disabled' value='0.00'/>
                    </td>
                    <td hidden='hidden'>Dr</td>
                    <td hidden='hidden'>0</td>
                    <td hidden='hidden'>0</td>
                    <td>
                        <select class='form-control' name='other_cgst' onchange='Calculate()' id='txtothercgst${i}_${rowcount}'>${CGST_rates}</select>
                        <span class="td-sub-content bracket-enclosed" name='other_cgstamt' id="txtothercgstamt${i}">0.00</span>
                    </td>
                    <td>
                        <select class='form-control' name='other_sgst' onchange='Calculate()' id='txtothersgst${i}_${rowcount}'>${SGST_rates}</select>
                        <span class="td-sub-content bracket-enclosed" name='other_sgstamt' id=txtothersgstamt${i}">0.00</span>
                    </td>
                    <td>
                        <select class='form-control' name='other_igst' onchange='Calculate()' id='txtotherigst${i}_${rowcount}'>${IGST_rates}</select>
                        <span class="td-sub-content bracket-enclosed" name='other_igstamt' id=txtotherigstamt${i}">0.00</span>
                    </td>
                </tr>`;
    $(row).insertBefore('#drcrtable tbody tr:last-child').addClass('tbl');
}

function insert_details(status, is_super_edit){
    $("#loading").show();
    var input_string = "";
    var tax1type = 0;
    if ($("#txtnetvalue").val() == "") {
        $("#txtnetvalue").val(0);
    }
    var ControlCollections = [];
    $(".error-border").removeClass('error-border');
    $(".custom-error-message, .suggestion-container").remove();
    $("#drcrtable tbody tr .td-hsn-code").each(function(){
        var is_valid_row = true;
        if($(this).closest("tr").find("input[name='item_unit']").length <= 0) {
            if($(this).closest("tr").find("input[name='otherQty']").val() == "" || $(this).closest("tr").find("input[name='otherQty']").val() <= 0) {
                is_valid_row = false
            }
        }
        if(is_valid_row) {
            var currentHsn = $(this);
            var currentElementId = currentHsn.attr("id");
            var control = {
                controltype: 'textbox',
                controlid: currentElementId,
                isrequired: true,
                errormsg: 'HSN/SAC is required',
                ishsn: true,
                hsnerrormsg: 'Invalid HSN Code',
                suggestioncontainer: "hsn-wrapper"
            }
            ControlCollections[ControlCollections.length] = control;            
        }
    });
    var result = JSCustomValidator.JSvalidate(ControlCollections);
    if(!result) {
        $("#loading").hide();
        return false;
    }

    note_header = { net_value: $("#txtnetvalue").val(), receipt_no: $("#txtgrn_id").val(), status: status,
                    change_dr_type: $("#change_dr_type").val(), dr_type: $("#dr_type").val(),
                    total_auto_amount: $("#tot_auto_amount").val(), remarks: $("#txtremarks").val(),
                    rounded_off: $("#txtround_off").val(), note_id: $("#id-note_id").val() }
    var i;
    var table = document.getElementById('drcrtable');
    var rowcount = document.getElementById('drcrtable').rows.length;
    input_obj = document.getElementsByName('changedrvalue');
    auto_dr_value = document.getElementsByName('autodrvalue');
    var rates_diff_change = document.getElementsByName('rate');
    var hsn_code = document.getElementsByName('hsn_code');

    var other_Description  = document.getElementsByName('otherDescription');
    var other_Qty  = document.getElementsByName('otherQty');
    var other_Hsn_code  = document.getElementsByName('othersHsn_code');
    var other_Rate  = document.getElementsByName('otherRate');
    var other_Amount  = document.getElementsByName('otherAmount');
    var other_is_credit = document.getElementsByName('is_credit');

    var cgst = document.getElementsByName('cgst');
    var sgst = document.getElementsByName('sgst');
    var igst = document.getElementsByName('igst');

    var other_cgst = document.getElementsByName('other_cgst');
    var other_sgst = document.getElementsByName('other_sgst');
    var other_igst = document.getElementsByName('other_igst');
    var item_make = document.getElementsByName('item_make');
    var item_id = document.getElementsByName('item_id');

    var item_unit = document.getElementsByName('item_unit');
    var item_dc_id = document.getElementsByName('item_dc_id');
    var alternate_unit_id = document.getElementsByName('alternate_unit_id');
    var is_faulty = document.getElementsByName('is_faulty');

    var note_details = "";
    var GenData = "";
    var cr_type = 0;
    var gen_det_count = 0;
    var check_tax = false
    var GenCount = document.getElementsByName('is_credit');
    if (GenCount.length != 0){
        for(i = 1; i <= GenCount.length; i++)	{   //No of Others Entry
            if(other_Qty[i-1].value != 0){   //Check Qty
                if(other_Description[i-1].value == ""){     //Check Description
                    $("#loading").hide();
                    swal("","Description is required.","warning");
                    return;
                }
                if(other_is_credit[i-1].checked==true) { cr_type= 1 } else { cr_type= 0 }   // Check dr or cr
                GenData = GenData +  other_Description[i-1].value + "[:]Others[:]" + other_Qty[i-1].value + "[:]" +
                    other_Rate[i-1].value + "[:]" +  other_Amount[i-1].value + "[:]"+ cr_type +"[:]" +
                    other_cgst[i-1].options[other_cgst[i-1].selectedIndex].value +"[:]"+
                    other_sgst[i-1].options[other_sgst[i-1].selectedIndex].value +"[:]"+
                    other_igst[i-1].options[other_igst[i-1].selectedIndex].value +"[:]"+
                    other_Hsn_code[i-1].value +"[:]";
                gen_det_count =gen_det_count + 1
                if (other_cgst[i-1].options[other_cgst[i-1].selectedIndex].value=="0" && other_sgst[i-1].options[other_sgst[i-1].selectedIndex].value=="0" && other_igst[i-1].options[other_igst[i-1].selectedIndex].value=="0"){
                    check_tax = true
                }
            }
        }
    }
    GenData = gen_det_count +"[:]"+ GenData
    dataCount = auto_dr_value.length+1

    for(i = 1 ; i < dataCount; i++)	{
        cgst_rate = 0;
        sgst_rate = 0;
        igst_rate = 0;
        if (typeof cgst[i-1].options[cgst[i-1].selectedIndex] != 'undefined'){ cgst_rate = cgst[i-1].options[cgst[i-1].selectedIndex].value; }
        if (typeof sgst[i-1].options[sgst[i-1].selectedIndex] != 'undefined'){ sgst_rate = sgst[i-1].options[sgst[i-1].selectedIndex].value; }
        if (typeof igst[i-1].options[igst[i-1].selectedIndex] != 'undefined'){ igst_rate = igst[i-1].options[igst[i-1].selectedIndex].value; }
        note_details  = note_details + $(table.rows[i].cells[8]).text() + "[:]" + $(table.rows[i].cells[2]).text() + "[:]"+
                $(table.rows[i].cells[3]).text() +"[:]"+rates_diff_change[i-1].value +"[:]"+
                auto_dr_value[i-1].value +"[:]"+ input_obj[i-1].value+"[:]"+ $(table.rows[i].cells[7]).text() +"[:]"+
                $(table.rows[i].cells[1]).text() +"[:]"+ $(table.rows[i].cells[9]).text() +"[:]"+ cgst_rate +"[:]"+
                sgst_rate +"[:]"+ igst_rate +"[:]"+ item_make[i-1].value +"[:]"+ item_unit[i-1].value +"[:]"+
                item_dc_id[i-1].value +"[:]"+ item_id[i-1].value +"[:]"+ alternate_unit_id[i-1].value +"[:]"+
                hsn_code[i-1].value +"[:]"+ is_faulty[i-1].value +"[:]";
        if(rates_diff_change[i-1].value != 0 && rates_diff_change[i-1].value != ""){     //Check Rate
            if (cgst_rate=="0" && sgst_rate=="0" && igst_rate=="0"){
                check_tax = true
            }
        }
    }
    note_details = dataCount + "[:]" + note_details
    tax_codes = document.getElementsByName('po_tax_code');
    tax_data = "";
    tax_order = 1;
    if(tax_codes.length == 0 && check_tax) {
        setTimeout(function(){
            $("#loading").hide();
            swal({
                title: "Please Confirm",
                text: "One or more item has no tax attached!",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#209be1",
                confirmButtonText: "Yes, do it!",
                closeOnConfirm: true,
                closeOnCancel: true
            },
            function() {
                saveAuditNote();
            });
        },500);
    }
    else {
        saveAuditNote();
    }

    function saveAuditNote(){
        $("#loading").show();
        for(i=0; i<tax_codes.length; i++){
            tax_data += tax_codes[i].value + "[:]" + tax_order + "[::]";
            tax_order += 1;
        }

        var tag_data = "";
        $("#grn_tags_table").find('.li-tagit-display:visible').each(function(){
            tag_data += $(this).find('.hidden-div-tagit-id').text()+ "[::]" + $(this).find('.label-tagit-display').text()+ "[::]" ;
        });

        note_data = {note_header: JSON.stringify(note_header), note_details :note_details, dataCount: dataCount, tag_data: tag_data,
                    general_details:GenData,tax_data: tax_data,is_super_edit: is_super_edit }
        $.ajax({
            url: "/erp/auditing/json/icdedit/",
            type: "post",
            datatype: "json",
            data: note_data,
            success : function(response) {
                $("#loading").hide();
                $('#template_title').text(getPageTitle());
                $("#txtgrn_id").val("");
                $("#id-note_id").val("");
                $("#txtchangetotamount").val("");
                $("#txtremarks").val("");
                $("#txround_off").val("");
//                $("#search_status").val('0')
                clearTable();
                clearSuperEdit();
                loadgrns();
                $('#edit_icd').hide();
                $('#view_icd').show();
                $(".header_current_page").remove();
                $(".page-title").html(getPageTitle());
                $("#cmdcancel_up").addClass('hide');
                $(".single-eInvoice").addClass("hide");
                $(".multiple-eInvoice").removeClass("hide");
                $(".export_csv, #note_creation").removeClass('hide');
                if(status > 1 && status != 5){
                    $('#cmdverify').show();
                }else{
                    $('#cmdverify').hide();
                }
                TableHeaderFixed();
                ga('send', 'event', 'Cr/Dr Note', 'Create via ICD Check', $('#enterprise_label').val(), 1);
                //location.reload();
            },
            error: function(xhr, errmsg, err) {
                setTimeout(function(){
                    $("#loading").hide();
                    swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
                },500);
            }
        });
        $('#invoice_doc_pdf').attr('src', '');
        $('#invoice_doc').attr('src', '');
        return false;
    }
}

function clearSuperEdit(){
    DiscardEditICDNumber();
    $(".super_user_icon, .xsid_number_edit, .super_user_tool_tip").addClass("hide");
}

function showdoc(id){
    var doc_element;
    var expand_img;
    if (id=="1"){
        doc_element = document.getElementById('invoice');
        expand_img = document.getElementById('invoice_expand');
    }else if (id=="2"){
        doc_element = document.getElementById('drcr');
        expand_img = document.getElementById('drcr_expand');
    }
    else if (id=="3"){
        doc_element = document.getElementById('grn');
        expand_img = document.getElementById('grn_expand');
    }
    else if (id=="4"){
        doc_element = document.getElementById('po');
        expand_img = document.getElementById('po_expand');
    }
    if(doc_element.style.display=="none"){
        doc_element.style.display='block';
        expand_img.src = "/site_media/images/minus.jpg"
    }
    else{
        doc_element.style.display='none';
        expand_img.src = "/site_media/images/plus.jpg"
    }
}

function clearTable()	{
	var table = document.getElementById("drcrtable");
	var rowCount = table.rows.length;
	if (rowCount >= 1) {
		for (var i = 1; i < rowCount; i++) {
		table.deleteRow(i);
			rowCount--;
			i--;
		}
	}
}

function Calculate() {
    var i;
    var table = document.getElementById('drcrtable')
    var rowcount = document.getElementById('drcrtable').rows.length;

    var simple_rates = document.getElementsByName('net_rate');
    var compound_rates = document.getElementsByName('net_rate_compound');
    var tax_wise_subtotal = document.getElementsByName('tax');
    var compound_tax_wise_subtotal = document.getElementsByName('tax_compound');

    var rates_diff_change = document.getElementsByName('rate');
    var rounded_off= parseFloat(document.getElementById("txtround_off").value);

    var txt_cgst = document.getElementsByName('cgst');
	var txt_sgst = document.getElementsByName('sgst');
	var txt_igst = document.getElementsByName('igst');

	var txt_cgst_amt = document.getElementsByName('cgstamt');
	var txt_sgst_amt = document.getElementsByName('sgstamt');
	var txt_igst_amt = document.getElementsByName('igstamt');

	var txt_other_cgst = document.getElementsByName('other_cgst');
	var txt_other_sgst = document.getElementsByName('other_sgst');
	var txt_other_igst = document.getElementsByName('other_igst');

	var txt_other_cgst_amt = document.getElementsByName('other_cgstamt');
	var txt_other_sgst_amt = document.getElementsByName('other_sgstamt');
	var txt_other_igst_amt = document.getElementsByName('other_igstamt');


    var total=0;
    var cr_total =0;
    var dr_total =0;
    var cr_auto_total=0;
    var dr_auto_total =0;
    var GenAmount=0;
    var auto_total=0;
    var dataCount =0;
    var dr_other_total = 0;
    var cr_other_total =0;

    var cgst_total=0;
    var dr_cgst_total_amt = 0;
    var cr_cgst_total_amt=0 ;

    var sgst_total=0;
    var dr_sgst_total_amt = 0;
    var cr_sgst_total_amt=0 ;

    var igst_total=0;
    var dr_igst_total_amt = 0;
    var cr_igst_total_amt=0 ;

    var dr_other_cgst_total_amt = 0;
    var cr_other_cgst_total_amt=0 ;

    var dr_other_sgst_total_amt = 0;
    var cr_other_sgst_total_amt=0 ;

    var dr_other_igst_total_amt = 0;
    var cr_other_igst_total_amt=0 ;
    var gst_total =0;

    dr_value = document.getElementsByName('changedrvalue');
    auto_dr_value = document.getElementsByName('autodrvalue');

    var other_Description  = document.getElementsByName('otherDescription');
    var other_Qty  = document.getElementsByName('otherQty');
    var other_Rate  = document.getElementsByName('otherRate');
    var other_Amount  = document.getElementsByName('otherAmount');
    var other_is_credit = document.getElementsByName('is_credit');

    var GenData = "";
    var cr_type = "";
    var GenCount = document.getElementsByName('is_credit');
    if (GenCount.length != 0){
        for(i=0;i<=GenCount.length-1;i++) {   //No of Others Entry
            other_Amount[i].value = (other_Qty[i].value * other_Rate[i].value).toFixed(2);
            cgst_rate = parseFloat(txt_other_cgst[i].options[txt_other_cgst[i].selectedIndex].text);
	        if(parseFloat(other_Amount[i].value) > 0 && cgst_rate > 0) {
				txt_other_cgst_amt[i].innerHTML = (parseFloat(other_Amount[i].value) * cgst_rate )/100;
			} else {
				txt_other_cgst_amt[i].innerHTML = 0.00;
			}

			sgst_rate = parseFloat(txt_other_sgst[i].options[txt_other_sgst[i].selectedIndex].text);
	        if(parseFloat(other_Amount[i].value) > 0 && sgst_rate > 0){
				txt_other_sgst_amt[i].innerHTML = (parseFloat(other_Amount[i].value) * sgst_rate )/100;
			} else {
				txt_other_sgst_amt[i].innerHTML = 0.00;
			}

			igst_rate = parseFloat(txt_other_igst[i].options[txt_other_igst[i].selectedIndex].text);
	        if(parseFloat(other_Amount[i].value) > 0 && igst_rate > 0){
				txt_other_igst_amt[i].innerHTML = (parseFloat(other_Amount[i].value) * igst_rate )/100;
			} else {
				txt_other_igst_amt[i].innerHTML = 0.00;
			}

            if (other_is_credit[i].checked==false){  //Check Cr or Dr
                dr_other_total = dr_other_total+parseFloat(other_Amount[i].value);
                dr_other_cgst_total_amt = dr_other_cgst_total_amt + (parseFloat(txt_other_cgst_amt[i].innerHTML));
                dr_other_sgst_total_amt = dr_other_sgst_total_amt + (parseFloat(txt_other_sgst_amt[i].innerHTML));
                dr_other_igst_total_amt = dr_other_igst_total_amt + (parseFloat(txt_other_igst_amt[i].innerHTML));
            }else if (other_is_credit[i].checked==true){
                cr_other_total = cr_other_total+parseFloat(other_Amount[i].value);
                cr_other_cgst_total_amt = cr_other_cgst_total_amt + (parseFloat(txt_other_cgst_amt[i].innerHTML));
                cr_other_sgst_total_amt = cr_other_sgst_total_amt + (parseFloat(txt_other_sgst_amt[i].innerHTML));
                cr_other_igst_total_amt = cr_other_igst_total_amt + (parseFloat(txt_other_igst_amt[i].innerHTML));
            }
        }
    }

    for(i = 0; i < auto_dr_value.length; i++)	{
        var cgst_rate = 0;
        var sgst_rate = 0;
        var igst_rate = 0;
        dr_value[i].value = ($(table.rows[i+1].cells[3]).text() * rates_diff_change[i].value).toFixed(2);
        if (typeof txt_cgst[i].options[txt_cgst[i].selectedIndex] != 'undefined'){
            cgst_rate = parseFloat(txt_cgst[i].options[txt_cgst[i].selectedIndex].text);
        }
      	if(parseFloat(dr_value[i].value) > 0 && cgst_rate > 0){
			txt_cgst_amt[i].innerHTML = ((parseFloat(dr_value[i].value) * cgst_rate )/100).toFixed(2);
		}else{
			txt_cgst_amt[i].innerHTML = 0.00;
		}

        if (typeof txt_sgst[i].options[txt_sgst[i].selectedIndex] != 'undefined'){
		    sgst_rate = parseFloat(txt_sgst[i].options[txt_sgst[i].selectedIndex].text);
		}
		if(parseFloat(dr_value[i].value) > 0 && sgst_rate > 0){
			txt_sgst_amt[i].innerHTML = ((parseFloat(dr_value[i].value) * sgst_rate )/100).toFixed(2);
		}else{
			txt_sgst_amt[i].innerHTML = 0.00;
		}

        if (typeof txt_igst[i].options[txt_igst[i].selectedIndex] != 'undefined'){
		    igst_rate = parseFloat(txt_igst[i].options[txt_igst[i].selectedIndex].text);
		}
		if(parseFloat(dr_value[i].value) > 0 && igst_rate > 0){
			txt_igst_amt[i].innerHTML = ((parseFloat(dr_value[i].value) * igst_rate )/100).toFixed(2);
		}else{
			txt_igst_amt[i].innerHTML = 0.00;
		}

        if ($(table.rows[i+1].cells[7]).text() == 'Dr'){
            dr_total = dr_total+parseFloat(dr_value[i].value) ;
            dr_auto_total = dr_auto_total + parseFloat(auto_dr_value[i].value);
            dr_cgst_total_amt = dr_cgst_total_amt + (parseFloat(txt_cgst_amt[i].innerHTML));
            dr_sgst_total_amt = dr_sgst_total_amt + (parseFloat(txt_sgst_amt[i].innerHTML));
            dr_igst_total_amt = dr_igst_total_amt + (parseFloat(txt_igst_amt[i].innerHTML));
        }else if ($(table.rows[i+1].cells[7]).text() == 'Cr'){
            cr_total = cr_total+parseFloat(dr_value[i].value);
            cr_auto_total = cr_auto_total + parseFloat(auto_dr_value[i].value);
            cr_cgst_total_amt = cr_cgst_total_amt + (parseFloat(txt_cgst_amt[i].innerHTML));
            cr_sgst_total_amt = cr_sgst_total_amt + (parseFloat(txt_sgst_amt[i].innerHTML));
            cr_igst_total_amt = cr_igst_total_amt + (parseFloat(txt_igst_amt[i].innerHTML));
        }
    }

    total = (dr_total + dr_other_total) - (cr_total + cr_other_total);
    auto_total = dr_auto_total - cr_auto_total;
    cgst_total = (dr_cgst_total_amt+dr_other_cgst_total_amt) - (cr_cgst_total_amt+cr_other_cgst_total_amt);
    sgst_total = (dr_sgst_total_amt+dr_other_sgst_total_amt) - (cr_sgst_total_amt+cr_other_sgst_total_amt);
    igst_total = (dr_igst_total_amt+dr_other_igst_total_amt) - (cr_igst_total_amt+cr_other_igst_total_amt);

    if (total >= 0) {
	    document.getElementById("txttotamount").value=total.toFixed(2);
	    document.getElementById("change_dr_type").value="Dr";
	    $("#txttotamount-txt").text(total.toFixed(2)+' Dr');
	    $(table.rows[rowcount-1].cells[7]).text(total.toFixed(2)+' Dr');
    } else {
	    total = total * -1;
	    document.getElementById("txttotamount").value=total.toFixed(2);
	    document.getElementById("change_dr_type").value="Cr";
	    $("#txttotamount-txt").text(total.toFixed(2)+' Cr');
	    $(table.rows[rowcount-1].cells[7]).text(total.toFixed(2)+' Cr');
    }
    if (auto_total >= 0){
        $("#tot_auto_amount").val(auto_total.toFixed(2));
        $("#dr_type").val("Dr");
        $("#auto_total_amt").text(auto_total.toFixed(2) + " Dr");
    } else {
        auto_total = (auto_total * -1);
        $("#tot_auto_amount").val(auto_total.toFixed(2));
        $("#dr_type").val("Cr");
        $("#auto_total_amt").text(auto_total.toFixed(2) + " Cr");
    }
    if (cgst_total>=0){
        document.getElementById("cgst_total_amt").innerHTML = cgst_total.toFixed(2) + " Dr";
    }else{
        document.getElementById("cgst_total_amt").innerHTML = (cgst_total.toFixed(2)*-1) + " Cr";
    }
    if (sgst_total>=0){
        document.getElementById("sgst_total_amt").innerHTML = sgst_total.toFixed(2) + " Dr";
    }else{
        document.getElementById("sgst_total_amt").innerHTML = (sgst_total.toFixed(2)*-1) + " Cr";
    }
    if (igst_total>=0){
        document.getElementById("igst_total_amt").innerHTML = igst_total.toFixed(2) + " Dr";
    }else{
        document.getElementById("igst_total_amt").innerHTML = (igst_total.toFixed(2)*-1) + " Cr";
    }

	total = (dr_total+dr_other_total) - (cr_total+cr_other_total);
	gst_total = (((dr_cgst_total_amt + dr_other_cgst_total_amt) - (cr_cgst_total_amt + cr_other_cgst_total_amt)) + ((dr_sgst_total_amt + dr_other_sgst_total_amt) - (cr_sgst_total_amt + cr_other_sgst_total_amt)) + ((dr_igst_total_amt + dr_other_igst_total_amt) - (cr_igst_total_amt + cr_other_igst_total_amt)));

	if ((total+gst_total) < 0){
		total = total*-1;
		gst_total = gst_total *-1;
	}

    net_tax = 0;
    simple_tax_count = simple_rates.length;
    // Calculating the net Taxes
    for (i=0; i<simple_tax_count; i++){
        net_tax += parseFloat(simple_rates[i].value);
        tax_wise_subtotal[i].value = Math.round(total * parseFloat(simple_rates[i].value))/100;
    }
    cascading_tax = Math.round(total * net_tax)/100;
    cascading_tax += gst_total;
    net_tax += 100;
    compound_tax_count = compound_rates.length;
    // Calculating those taxes that are compound
    for (i=0; i<compound_tax_count; i++){
        net_tax += net_tax * parseFloat(compound_rates[i].value)/100;
        compound_tax_wise_subtotal[i].value = Math.round((total + cascading_tax) * parseFloat(compound_rates[i].value))/100;
        cascading_tax += parseFloat(compound_tax_wise_subtotal[i].value);
    }

    // Grand Total (tax-rate applied)| actual value = (total * net_tax/100)| work-around made to arrive @ 2 decimal pts
    document.getElementById("txtnetvalue").value = (total + cascading_tax + rounded_off).toFixed(2);
    document.getElementById("grand_total").innerHTML = (total + cascading_tax + rounded_off).toFixed(2) + " " + $("#id-grn_currency_code").val();
    if (document.getElementById('grand_total').innerHTML != "0.00"){
        $('#audit_note_total_div').show();
    } else {
        $('#audit_note_total_div').hide();
    }
}

function editrow(grn_id, note_code, type, note_id, status) {
    var po_id ="";
    var title_grn = "ICD";
    $("#data-current-id").val(grn_id);
    $("#data-note-id").val(note_code);
    document.getElementById("txtnetvalue").value = "";
    document.getElementById("grand_total").innerHTML = "";
    $('#cmdgrnreturn, #cmdgrnreturn_up').addClass("hide");
    var addNoteSuperEdit = '<form method="post" action="/erp/auditing/note/edit/">\
        <input type="hidden" name="csrfmiddlewaretoken" value="'+getCookie('csrftoken')+'">\
        <input type="hidden" value="'+grn_id+'" name="receipt_no">\
        <input type="hidden" value="'+note_id+'" name="note_id">\
        <input type="hidden" value="1" name="is_super_edit">\
        <input type="submit" id="editNote_'+note_id+'" hidden="hidden">\
    </form>';
    $( addNoteSuperEdit ).insertBefore( "#super_note_edit" );
    $("#super_note_edit").attr("onclick", "javascript:clickButton('editNote_"+note_id+"')");
    $("#super_note_edit").hide()
    var icd_request_acknowledgement = $("#icd_request_acknowledgement").val();
	if (grn_id!='null' && grn_id !="" && grn_id!=null){
        $.ajax({
            url: "/erp/stores/json/grn/loadgrnheader/",
            type: "post",
            datatype:"json",
            data: {receipt_no: grn_id},
            success: function(response) {
                if(response['response_message'] == "Session Timeout") {
                    location.reload();
                    return;
                }
                var is_note = false;
                var grn = response[0];
                displayRemarksHistory("GRNremarksList", grn.remarks, "count_not_required");
                if(grn.icd_remarks != "null"){
                    displayRemarksHistory("remarks_list", grn.icd_remarks, "remarks_count");
                }
                var iDate = grn.invoice_date;
                iDate = moment(iDate, "YYYY-MM-DD").format("MMM D, YYYY");
                if(iDate.toLowerCase().trim() == "invalid date") { iDate = '-'; }
                document.getElementById("txtgrn_id").value = grn.grn_no;
                $("#id-note_id").val(grn.note_id);
                document.getElementById("inv_no").innerHTML = grn.invoice_no;
                document.getElementById("inv_date").innerHTML = iDate;
                document.getElementById("inv_value").innerHTML = grn.net_invoice_value.toFixed(2) + " " + grn.currency_code;
                $("#id-grn_currency_code").val(grn.currency_code);
                document.getElementById("party_name").innerHTML = grn.party_name;
                $("#note_status").val(grn.status)
                var grn_code = ""
                if (grn.received_against=='Note') {
                    document.getElementById("grn_no").innerHTML = grn.receipt_no;
                    title_grn = note_code
                    $("#super_note_edit").show()
                } else if(grn.received_against=='Purchase Order' || grn.received_against=='Job Work' || grn.received_against=='Delivery Challan'){
                    grn_code = grn.financial_year+"/GRN/"+grn.receipt_no + grn.sub_number;
                    title_grn = grn_code;
                    document.getElementById("grn_no").innerHTML = title_grn;
                } else if(grn.received_against=='Sales Return') {
                    grn_code = grn.financial_year+"/SR/"+grn.receipt_no + grn.sub_number;
                    title_grn = grn_code;
                    document.getElementById("grn_no").innerHTML = title_grn;
                } else {
                    document.getElementById("grn_no").innerHTML = "";
                    title_grn = "ICD";
                }
                document.getElementById("txtremarks").value = ""; // grn.icd_remarks
                document.getElementById("receipt_no").value = grn.grn_no;
                document.getElementById("txtround_off").value = grn.icd_round_off;
                document.getElementById("inv_no_inv").innerHTML = grn.invoice_no;
                document.getElementById("inv_date_inv").innerHTML = iDate;
                document.getElementById("inv_value_inv").innerHTML = grn.net_invoice_value.toFixed(2) + " " + grn.currency_code;
                document.getElementById("party_name_inv").innerHTML = grn.party_name;
                document.getElementById("grn_no_inv").innerHTML = grn_code; // TODO why here too
                if(grn.isval_einvoice != "1"){
                    $('.e-invoice_mod').hide();
                    $('#icd_cnl_irn').hide();
                }else{
                    if(grn.is_cnl_einvoice == "1"){
                        $('.e-invoice_mod').show();
                        $('#icd_cnl_irn').hide();
                    }else{
                        $('#icd_cnl_irn').show();
                        $('.e-invoice_mod').hide();
                    }
                }
                if (icd_request_acknowledgement != "True"){
                    $("#note_status").val(grn.status)
                }
                rec_against=grn.received_against;
                $("#id-received_against").val(grn.received_against);
                $('#id-collapse_document').text("Party Invoice");
                $('#id-invoice_data').removeClass('hide')
                if (grn.received_against=='Note') {
                    is_note = true;
                    $('.accordion-heading-invoice').addClass('hide');
                    $('.accordion-heading-receipt').addClass('hide');
                    $('.accordion-heading-po').addClass('hide');
                } else if (grn.received_against=='Sales Return' || grn.received_against=='Delivery Challan') {
                    $('.accordion-heading-invoice').removeClass('hide');
                    $('.accordion-heading-receipt').removeClass('hide');
                    $('.accordion-heading-po').addClass('hide');
                } else {
                    $('.accordion-heading-invoice').removeClass('hide');
                    $('.accordion-heading-receipt').removeClass('hide');
                    $('.accordion-heading-po').removeClass('hide');
                }
                load_details(rec_against, grn.status, grn_id);
                try {
                    if (grn.attachment != "" && grn.attachment != null && grn.attachment != "null") {
                        var attachment = JSON.parse(grn.attachment);
                        if (attachment.ext.toLowerCase() == "pdf") {
                            $("#invoice_doc_pdf").attr('src', grn.attachment_data.data).removeClass("hide");
                        } else {
                            $("#invoice_doc_pdf").addClass("hide");
                            $("#invoice_doc").attr('src', grn.attachment_data.data);
                        }
                        if (is_note == true) {
                            $('.accordion-heading-invoice').removeClass('hide');
                            $('#id-collapse_document').text("Note Document");
                            $('#id-invoice_data').addClass('hide')
                        }
                    }
                } catch(e) {
                    console.log(e);
                }
                if (grn.status > 1) {
                    if (icd_request_acknowledgement != "True"){
                        grn_status = grn.status;
                    }
                    else{
                        grn_status = status;
                    }
                    $('#view_note').show().attr("onclick", "generate_pdf_ajax("+grn_id+","+grn_status+","+grn.note_id+")");
                    $('#cmdSuperUpdate').show();
                }
                else {
                    $('#view_note').hide().removeAttr("onclick");
                    $('#cmdSuperUpdate').hide();
                }
                if (grn.status == 1){
                    $('#cmdgrnreturn, #cmdgrnreturn_up').removeClass("hide");
                }
                $('#cmdupdate').prop('disabled',true);
                $('#cmdupdate_up').prop('disabled',true);
                $('.multi_check').prop('checked', false);
                $('#template_title').text(title_grn);
                $(".page_header").html('<span class="header_current_page">'+ title_grn +'</span>');
                ICDSuperEditInit();
            }
        });
    }
    else{
        rec_against = 'Note';
        $.ajax({
            url: "/erp/stores/json/grn/loadnoteheader/",
            type: "post",
            datatype:"json",
            data: {note_id: note_id},
            success: function(response) {
                var is_note = false;
                var note = response[0];
                document.getElementById("inv_no").innerHTML = note.invoice_no;
                document.getElementById("inv_date").innerHTML = note.invoice_date;
                document.getElementById("inv_value").innerHTML = note.net_invoice_value.toFixed(2);
                document.getElementById("party_name").innerHTML = note.party_name;
                document.getElementById("txtround_off").value = note.round_off;
                document.getElementById("receipt_no").value = note.receipt_no;
                note_code = note.financial_year+"/IAN/"+note.note_no + note.sub_number;
                $("#id-note_id").val(note_id);
                $("#search_status").val(note.status)
                $("#note_status").val(note.status)
                $("#super_note_edit").show()
                title_grn = note_code
                $(".page_header").html('<span class="header_current_page">'+ title_grn +'</span>');
                document.getElementById("grn_no").innerHTML = note.receipt_no;
                load_details(rec_against, status, note_id);
                is_note = true;
                $('.accordion-heading-invoice').addClass('hide');
                $('.accordion-heading-receipt').addClass('hide');
                $('.accordion-heading-po').addClass('hide');
                if (status > 1 || status == -2) {
                    $('#view_note').show().attr("onclick", "generate_pdf_ajax("+grn_id+","+status+","+note_id+",)");
                    $('#cmdSuperUpdate').show();
                }
                else {
                    $('#view_note').hide().removeAttr("onclick");
                    $('#cmdSuperUpdate').hide();
                }
                if(note.isval_einvoice != "1"){
                    $('.e-invoice_mod').hide();
                    $('#icd_cnl_irn').hide();
                }else{
                    if(note.is_cnl_einvoice == "1"){
                        $('.e-invoice_mod').show();
                        $('#icd_cnl_irn').hide();
                    }else{
                        $('#icd_cnl_irn').show();
                        $('.e-invoice_mod').hide();
                    }
                }
                $('#cmdupdate').prop('disabled',true);
                $('#cmdupdate_up').prop('disabled',true);
                $('.multi_check').prop('checked', false);
                try {
                    if (note.attachment != "" && note.attachment != null && note.attachment != "null") {
                        var attachment = JSON.parse(note.attachment);
                        if (attachment.ext.toLowerCase() == "pdf") {
                            $("#invoice_doc_pdf").attr('src', note.attachment_data.data).removeClass("hide");
                        } else {
                            $("#invoice_doc_pdf").addClass("hide");
                            $("#invoice_doc").attr('src', note.attachment_data.data);
                        }
                        if (is_note == true) {
                            $('.accordion-heading-invoice').removeClass('hide');
                            $('#id-collapse_document').text("Note Document");
                            $('#id-invoice_data').addClass('hide')
                        }
                    }
                } catch(e) {
                    console.log(e);
                }
            }
        });
    }
}

function getPageTitle() {
    var url = window.location.href;
    if(url.indexOf('is_credit_debit') > 0) {
        return 'Credit/Debit Note'
    }
    else {
        return 'Internal Control';
    }
}

function load_details(rec_against, status, grn_id){
    $("#drcrtable").find("tr:gt(0)").remove();
    $("#grn_table").find("tr:gt(1)").remove();
    $(".instant_invoice_loading_message, .instant_audit_loading_message").removeClass('hide').find('small').text('');
    setTimeout(function(){
        $(".instant_invoice_loading_message, .instant_audit_loading_message").find('small').text('Large data will take a bit time to load. Thanks for your patience.')
    },5000);
	var sno =0;
	var seltext = grn_id +","+ rec_against;
	item_details=""
	var is_return_material = false;
	var price =0;
	var add_new_others = ""
	$.ajax({
		url: "/erp/auditing/json/icd/loadgrnmaterial/",
		type: "post",
		datatype:"json",
		data: {'grn_id': grn_id,'rec_against':rec_against},
		success: function(response){
			$("#drcrtable").find("tr:gt(0)").remove();
			if(rec_against == 'Note'){
			    displayRemarksHistory("remarks_list", response['icd_remarks'], "remarks_count");
			}
            $.each(response['grn_materials'], function(i, item) {
			    if ((status == 1 || status == 5) && rec_against != 'Note' ){
			        is_return_material = false
                    balqty = (item['dc_qty']-item['rec_qty']).toFixed(3);
                    if (item['item_id']!="" && item['item_id']!= null) { classname='no-bg' } else { classname='tr-alert-danger' }
                    if (item['drawing_no'] != "" && item['drawing_no'] != null) {item_details = item['item_name'] + " - " + item['drawing_no']}
                    else{
                     item_details = item['item_name']
                    };
                    if (item['dc_id']!=null && rec_against !='Sales Return'){
                        is_return_material = true
                    }
                    if(!is_return_material){
                        if (rec_against !='Sales Return'){
                            price = item['inv_rate'].toFixed(5)
                        }else{
                            price = item['sales_return_rate'].toFixed(5)
                        }
                    }else{
                        price = item['store_price'].toFixed(5)
                    }
                    var make_name = constructDifferentMakeName(item['make_name'])
                    if (make_name != ""){ item_details = item_details + " <i>[" + make_name + "]</i>"; }
                    if (item['is_faulty'] == 1){ item_details = item_details + " <i>[Faulty]</i>"; }
                    if(item.is_service == 1){
                        item_details += `<span class="service-item-flag"></span>`;
                    }
                    var poPrice = 0
                    if (item['po_no'] != null){ poPrice = item['po_price'] }else{ poPrice = item['store_price'] }

                    if(rec_against =='Sales Return'){
                        if (item['dc_qty'] > 0){
                            sno = sno+1;
                            qty = item['dc_qty']
                            var sales_return_row = `<tr class='${classname}'>
                                            <td class='text-center'>${sno}</td>
                                            <td>${item_details}</td>
                                            <td>Return</td>
                                            <td class='text-right validate-icd-qty'>${qty}</td>
                                            <td>
                                                <input type='text' class='form-control text-right td-hsn-code' name='hsn_code' id='txt_hsn_code${sno}' onchange="validateInlineHsn(this);" onkeypress="validateStringOnKeyPress(this, event, \'hsn_specialChar\');" onblur="return validateStringOnBlur(this, event, \'alphaSpecialChar\');" value="${item['hsn_code']}" maxlength='16' >
                                                <div class="hsn_empty hide"style="margin-top:4px;">
                                                    <span style="font-size:11px;color:#dd4b39;">Suggestion :</span>
                                                    <span class="btn hsn-btn" onclick="updateHSNValue('NIL-RATED', 'txt_hsn_code${sno}');" value="NIL-RATED" >NIL-RATED</span>
                                                    <span class="btn hsn-btn" onclick="updateHSNValue('EXEMPT', 'txt_hsn_code${sno}');" value="EXEMPT" >EXEMPT</span>
                                                    <span class="btn hsn-btn" onclick="updateHSNValue('NON-GST', 'txt_hsn_code${sno}');" value="NON-GST" >NON-GST</span>
                                                </div>
                                            </td>
                                            <td>
                                                <input type='text' class='form-control text-right validate-icd-rate' name='rate' id='txtrate${i}' value='${price}' maxlength='19' onfocus='setNumberRangeOnFocus(this,13,5)' onBlur='Calculate()' style='width: 85%; float:left;'><span class='account-type'>Cr</span>
                                                <input type='text' class='form-control text-right hide' disabled='disabled' name='Acc_rate' id='txtAccrate${i}' value='${price}' maxlength='16' onfocus='setNumberRangeOnFocus(this,10,5)' onblur='Calculate()' >
                                                <span class="td-sub-content bracket-enclosed pull-right" >${price} Cr</span>
                                            </td>
                                            <td>
                                                <input type='text' class='form-control text-right text_box_label' disabled='disabled' name='changedrvalue' id='txtchangedrvalue${i}' value='${(qty * price).toFixed(2)}' style='width: 85%; float:left;'><span class='account-type'>Cr</span>
                                                <input type='text' class='form-control text-right hide' disabled='disabled' name='autodrvalue' id='txtdrvalue${i}' value='${(qty * price).toFixed(2)}' >
                                                <span class="td-sub-content bracket-enclosed pull-right" >${(qty * price).toFixed(2)} Cr</span>
                                            </td>
                                            <td hidden='hidden'>Cr</td>
                                            <td hidden='hidden'>${item['item_id']}</td>
                                            <td hidden='hidden'>${item['po_no']}</td>
                                            <td>
                                                <select class='form-control' name='cgst' onchange='Calculate()' id='txtcgst${i}_${sno}'>${CGST_rates}</select>
                                                <span class="td-sub-content bracket-enclosed" name="cgstamt" id="txtcgstamt${i}">0.00</span>
                                            </td>
                                            <td>
                                                <select class='form-control' name='sgst' onchange='Calculate()' id='txtsgst${i}_${sno}'>${SGST_rates}</select>
                                                <span class="td-sub-content bracket-enclosed" name="sgstamt" id="txtsgstamt${i}">0.00</span>
                                            </td>
                                            <td>
                                                <select class='form-control' name='igst' onchange='Calculate()' id='txtigst${i}_${sno}'>${IGST_rates}</select>
                                                <span class="td-sub-content bracket-enclosed" name="igstamt" id="txtigstamt${i}">0.00</span>
                                            </td>
                                            <td hidden='hidden'>
                                                <input type='text' name='item_id' id='item_id_${i}' value="${item['item_id']}">
                                                <input type='text' name='item_make' id='item_make_${i}' value="${item['make_id']}" >
                                            </td>
                                            <td hidden='hidden'>
                                                <input type='text' name='item_unit' id='item_unit_${i}' value="${item['unit_id']}">
                                                <input type='text' name='item_dc_id' id='item_dc_id_${i}' value="${item['dc_id']}">
                                                <input type='text' name='is_faulty' id='is_faulty_${i}' value="${item['is_faulty']}">
                                                <input type='text' name='alternate_unit_id' id='alternate_unit_id_${i}' value="${item['alternate_unit_id']}">
                                                <input type='text' name='scale_factor' id='scale_factor_${i}' value="${item['scale_factor']}">
                                            </td>
                                        </tr>`;
                        $('#drcrtable').append(sales_return_row).addClass('tbl');
                        $("#txtcgst"+i+"_"+sno).val(item['cgst_code']);
                        $("#txtsgst"+i+"_"+sno).val(item['sgst_code']);
                        $("#txtigst"+i+"_"+sno).val(item['igst_code']);
                        }
                    }

                    //Shortage
                    if (balqty > 0){
                        sno = sno+1;
                        var row1 = `<tr class='${classname}'>
                                        <td class='text-center'>${sno}</td>
                                        <td>${item_details}</td>
                                        <td>Shortage</td>
                                        <td class='text-right validate-icd-qty'>${balqty}</td>
                                        <td class='hsn-wrapper tour_hsn'>
                                            <input type='text' class='form-control text-right td-hsn-code' name='hsn_code' id='txt_hsn_code_${sno}' onchange="validateHsnWithSuggestion(this);" onkeypress="validateStringOnKeyPress(this, event, 'hsn_specialChar');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" value="${item['hsn_code']}" maxlength='9' >
                                        </td>
                                        <td>
                                            <input type='text' class='form-control validate-icd-rate text-right' name='rate' id='txtrate${i}' value='${price}' maxlength='19' onfocus='setNumberRangeOnFocus(this,13,5)' onblur='Calculate()' style='width: 85%; float:left;'><span class='account-type'>Dr</span>
                                            <input type='text' class='form-control text-right hide' disabled='disabled' name='Acc_rate' id='txtAccrate${i}' value='${price}' maxlength='16' onfocus='setNumberRangeOnFocus(this,10,5)' onblur='Calculate()' >
                                            <span class="td-sub-content bracket-enclosed pull-right" >${price} Dr</span>
                                        </td>
                                        <td>
                                            <input type='text' class='form-control text-right text_box_label' disabled='disabled' name='changedrvalue' id='txtchangedrvalue${i}' value='${(balqty * price).toFixed(2)}' style='width: 85%; float:left;'><span class='account-type'>Dr</span>
                                            <input type='text' class='form-control text-right hide' disabled='disabled' name='autodrvalue' id='txtdrvalue${i}' value='${(balqty * price).toFixed(2)}' >
                                            <span class="td-sub-content bracket-enclosed pull-right" >${(balqty * price).toFixed(2)} Dr</span>
                                        </td>
                                        <td hidden='hidden'>Dr</td>
                                        <td hidden='hidden'>${item['item_id']}</td>
                                        <td hidden='hidden'>${item['po_no']}</td>
                                        <td>
                                            <select class='form-control' name='cgst' onchange='Calculate()' id='txtcgst${i}_${sno}'>${CGST_rates}</select>
                                            <span class="td-sub-content bracket-enclosed" name="cgstamt" id="txtcgstamt${i}">0.00</span>
                                        </td>
                                        <td>
                                            <select class='form-control' name='sgst' onchange='Calculate()' id='txtsgst${i}_${sno}'>${SGST_rates}</select>
                                            <span class="td-sub-content bracket-enclosed" name="sgstamt" id="txtsgstamt${i}">0.00</span>
                                        </td>
                                        <td>
                                            <select class='form-control' name='igst' onchange='Calculate()' id='txtigst${i}_${sno}'>${IGST_rates}</select>
                                            <span class="td-sub-content bracket-enclosed" name="igstamt" id="txtigstamt${i}">0.00</span>
                                        </td>
                                        <td hidden='hidden'>
                                            <input type='text' name='item_id' id='item_id_${i}' value="${item['item_id']}" >
                                            <input type='text' name='item_make' id='item_make_${i}' value="${item['make_id']}" >
                                        </td>
                                        <td hidden='hidden'>
                                            <input type='text' name='item_unit' id='item_unit_${i}' value="${item['unit_id']}">
                                            <input type='text' name='item_dc_id' id='item_dc_id_${i}' value="${item['dc_id']}">
                                            <input type='text' name='is_faulty' id='is_faulty_${i}' value="${item['is_faulty']}">
                                            <input type='text' name='alternate_unit_id' id='alternate_unit_id_${i}' value="${item['alternate_unit_id']}">
                                            <input type='text' name='scale_factor' id='scale_factor_${i}' value="${item['scale_factor']}">
                                        </td>
                                    </tr>`;
                    $('#drcrtable').append(row1).addClass('tbl');
                    $("#txtcgst"+i+"_"+sno).val(item['cgst_code']);
                    $("#txtsgst"+i+"_"+sno).val(item['sgst_code']);
                    $("#txtigst"+i+"_"+sno).val(item['igst_code']);
                    }
                    //Credit Note Excess Qty Received
                    if($('#id_icd_ignore_credit_note').val() != "True"){
                        if (balqty < 0){
                            sno = sno+1;
                            balqty = balqty *-1
                            var row2 = `<tr class='${classname}'>
                                            <td class='text-center'>${sno}</td>
                                            <td>${item_details}</td>
                                            <td>Shortage</td>
                                            <td class='text-right validate-icd-qty'>${balqty}</td>
                                            <td class="hsn-wrapper">
                                                <input type='text' class='form-control text-right td-hsn-code' name='hsn_code' id='txt_hsn_code_${sno}' onchange="validateHsnWithSuggestion(this);" onkeypress="validateStringOnKeyPress(this, event, 'hsn_specialChar');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" value="${item['hsn_code']}" maxlength='9' >
                                            </td>
                                            <td>
                                                <input type='text' class='form-control text-right validate-icd-rate' name='rate' id='txtrate${i}' value='${price}' maxlength='19' onfocus='setNumberRangeOnFocus(this,13,5)' onBlur='Calculate()' style='width: 85%; float:left;'><span class='account-type'>Cr</span>
                                                <input type='text' class='form-control text-right hide' disabled='disabled' name='Acc_rate' id='txtAccrate${i}' value='${price}' maxlength='16' onfocus='setNumberRangeOnFocus(this,10,5)' onblur='Calculate()' >
                                                <span class="td-sub-content bracket-enclosed pull-right" >${price} Cr</span>
                                            </td>
                                                <input type='text' class='form-control text-right text_box_label' disabled='disabled' name='changedrvalue' id='txtchangedrvalue${i}' value='${(balqty * price).toFixed(2)}' style='width: 85%; float:left;'><span class='account-type'>Cr</span>
                                                <input type='text' class='form-control text-right hide' disabled='disabled' name='autodrvalue' id='txtdrvalue${i}' value='${(balqty * price).toFixed(2)}' >
                                                <span class="td-sub-content bracket-enclosed pull-right" >${(balqty * price).toFixed(2)} Cr</span>
                                            </td>
                                            <td hidden='hidden'>Cr</td>
                                            <td hidden='hidden'>${item['item_id']}</td>
                                            <td hidden='hidden'>${item['po_no']}</td>
                                            <td>
                                                <select class='form-control' name='cgst' onchange='Calculate()' id='txtcgst${i}_${sno}'>${CGST_rates}</select>
                                                <span class="td-sub-content bracket-enclosed" name="cgstamt" id="txtcgstamt${i}">0.00</span>
                                            </td>
                                            <td>
                                                <select class='form-control' name='sgst' onchange='Calculate()' id='txtsgst${i}_${sno}'>${SGST_rates}</select>
                                                <span class="td-sub-content bracket-enclosed" name="sgstamt" id="txtsgstamt${i}">0.00</span>
                                            </td>
                                            <td>
                                                <select class='form-control' name='igst' onchange='Calculate()' id='txtigst${i}_${sno}'>${IGST_rates}</select>
                                                <span class="td-sub-content bracket-enclosed" name="igstamt" id="txtigstamt${i}">0.00</span>
                                            </td>
                                            <td hidden='hidden'>
                                                <input type='text' name='item_id' id='item_id_${i}' value="${item['item_id']}">
                                                <input type='text' name='item_make' id='item_make_${i}' value="${item['make_id']}" >
                                            </td>
                                            <td hidden='hidden'>
                                                <input type='text' name='item_unit' id='item_unit_${i}' value="${item['unit_id']}">
                                                <input type='text' name='item_dc_id' id='item_dc_id_${i}' value="${item['dc_id']}">
                                                <input type='text' name='is_faulty' id='is_faulty_${i}' value="${item['is_faulty']}">
                                                <input type='text' name='alternate_unit_id' id='alternate_unit_id_${i}' value="${item['alternate_unit_id']}">
                                                <input type='text' name='scale_factor' id='scale_factor_${i}' value="${item['scale_factor']}">
                                            </td>
                                        </tr>`;
                        $('#drcrtable').append(row2).addClass('tbl');
                        $("#txtcgst"+i+"_"+sno).val(item['cgst_code']);
                        $("#txtsgst"+i+"_"+sno).val(item['sgst_code']);
                        $("#txtigst"+i+"_"+sno).val(item['igst_code']);
                        }
                    }
                    //Rejected
                    if (item['rec_qty']-item['acc_qty']>0){
                        sno = sno+1;
                        var rejected_qty = item['rec_qty']-item['acc_qty']
                            var row3 = `<tr class='${classname}'>
                                            <td class='text-center'>${sno}</td>
                                            <td>${item_details}</td>
                                            <td>Rejected</td>
                                            <td class='text-right'>${rejected_qty.toFixed(3)}</td>
                                            <td class='text-right hsn-wrapper'>
                                                <input type='text' class='form-control text-right td-hsn-code' name='hsn_code' id='txt_hsn_code_${sno}' onchange="validateHsnWithSuggestion(this);" onkeypress="validateStringOnKeyPress(this, event, 'hsn_specialChar');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" value="${item['hsn_code']}" maxlength='9'>
                                            </td>
                                            <td>
                                                <input type='text' class='form-control text-right validate-icd-rate' name='rate' id='txtrate${i}' value='${price}' maxlength='19' onfocus='setNumberRangeOnFocus(this,13,5)' onblur='Calculate()' style='width: 85%; float:left;'><span class='account-type'>Dr</span>
                                                <input type='text' class='form-control text-right hide' disabled='disabled' name='Acc_rate' id='txtAccrate${i}' value='${price}' maxlength='16' onfocus='setNumberRangeOnFocus(this,10,5)' onblur='Calculate()' >
                                                <span class="td-sub-content bracket-enclosed pull-right" >${price} Dr</span>
                                            </td>
                                            <td>
                                                <input type='text' class='form-control text-right text_box_label' disabled='disabled' name='changedrvalue' id='txtchangedrvalue${i}' value='${(rejected_qty * price).toFixed(2)}' style='width: 85%; float:left;'><span class='account-type'>Dr</span>
                                                <input type='text' class='form-control text-right hide' disabled='disabled' name='autodrvalue' id='txtdrvalue${i}' value='${(rejected_qty * price).toFixed(2)}' >
                                                <span class="td-sub-content bracket-enclosed pull-right" >${(rejected_qty * price).toFixed(2)} Dr</span>
                                            </td>
                                            <td hidden='hidden'>Dr</td>
                                            <td hidden='hidden'>${item['item_id']}</td>
                                            <td hidden='hidden'>${item['po_no']}</td>
                                            <td>
                                                <select class='form-control' name='cgst' onchange='Calculate()' id='txtcgst${i}_${sno}'>${CGST_rates}</select>
                                                <span class="td-sub-content bracket-enclosed" name="cgstamt" id="txtcgstamt${i}">0.00</span>
                                            </td>
                                            <td>
                                                <select class='form-control' name='sgst' onchange='Calculate()' id='txtsgst${i}_${sno}'>${SGST_rates}</select>
                                                <span class="td-sub-content bracket-enclosed" name="sgstamt" id="txtsgstamt${i}">0.00</span>
                                            </td>
                                            <td>
                                                <select class='form-control' name='igst' onchange='Calculate()' id='txtigst${i}_${sno}'>${IGST_rates}</select>
                                                <span class="td-sub-content bracket-enclosed" name="igstamt" id="txtigstamt${i}">0.00</span>
                                            </td>
                                            <td hidden='hidden'>
                                                <input type='text' name='item_id' id='item_id_${i}' value="${item['item_id']}">
                                                <input type='text' name='item_make' id='item_make_${i}' value="${item['make_id']}" >
                                            </td>
                                            <td hidden='hidden'>
                                                <input type='text' name='item_unit' id='item_unit_${i}' value="${item['unit_id']}">
                                                <input type='text' name='item_dc_id' id='item_dc_id_${i}' value="${item['dc_id']}">
                                                <input type='text' name='is_faulty' id='is_faulty_${i}' value="${item['is_faulty']}">
                                                <input type='text' name='alternate_unit_id' id='alternate_unit_id_${i}' value="${item['alternate_unit_id']}">
                                                <input type='text' name='scale_factor' id='scale_factor_${i}' value="${item['scale_factor']}">
                                            </td>
                                        </tr>`;
                        $('#drcrtable').append(row3).addClass('tbl');
                        $("#txtcgst"+i+"_"+sno).val(item['cgst_code']);
                        $("#txtsgst"+i+"_"+sno).val(item['sgst_code']);
                        $("#txtigst"+i+"_"+sno).val(item['igst_code']);
                    }
                    if (!is_return_material && rec_against !='Sales Return'){
                        //Rate Diffrence
                        //Debit Note
                        debitnote_rate_diff = 0
                        if (item['inv_rate']> poPrice ){
                            debitnote_rate_diff = (item['po_no'] != null? (item['inv_rate']- item['po_price']).toFixed(5):(item['inv_rate']- item['store_price']).toFixed(5));
                            if (item['acc_qty']>0){
                                sno = sno+1;
                                var row4 = `<tr class='${classname}'>
                                                <td class='text-center'>${sno}</td>
                                                <td>${item_details}</td>
                                                <td>Rate Difference</td>
                                                <td class='text-right validate-icd-qty'>${item['acc_qty'].toFixed(3)}</td>
                                                <td class='text-right hsn-wrapper'>
                                                    <input type='text' class='form-control text-right td-hsn-code' name='hsn_code' id='txt_hsn_code_${sno}' value="${item['hsn_code']}" maxlength='9' onchange="validateHsnWithSuggestion(this);" onkeypress="validateStringOnKeyPress(this, event, 'hsn_specialChar');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');">
                                                </td>
                                                <td>
                                                    <input type='text' class='form-control text-right validate-icd-rate' name='rate' id='txtrate${i}' value='${debitnote_rate_diff}' maxlength='19' onfocus='setNumberRangeOnFocus(this,13,5)' onblur='Calculate()' style='width: 85%; float:left;'><span class='account-type'>Dr</span>
                                                    <input type='text' class='form-control text-right hide' disabled='disabled' name='Acc_rate' id='txtAccrate${i}' value='${debitnote_rate_diff}' maxlength='16' onfocus='setNumberRangeOnFocus(this,10,5)' onblur='Calculate()' >
                                                    <span class="td-sub-content bracket-enclosed pull-right" >${debitnote_rate_diff} Dr</span>
                                                </td>
                                                <td>
                                                    <input type='text' class='form-control text-right text_box_label' disabled='disabled' name='changedrvalue' id='txtchangedrvalue${i}' value="${(item['acc_qty'] *(debitnote_rate_diff)).toFixed(2)}" style='width: 85%; float:left;'><span class='account-type'>Dr</span>
                                                    <input type='text' class='form-control text-right hide' disabled='disabled' name='autodrvalue' id='txtdrvalue${i}' value="${(item['acc_qty'] *(debitnote_rate_diff)).toFixed(2)}" >
                                                    <span class="td-sub-content bracket-enclosed pull-right" >${(item['acc_qty'] *(debitnote_rate_diff)).toFixed(2)} Dr</span>
                                                </td>
                                                <td hidden='hidden'>Dr</td>
                                                <td hidden='hidden'>${item['item_id']}</td>
                                                <td hidden='hidden'>${item['po_no']}</td>
                                                <td>
                                                    <select class='form-control' name='cgst' onchange='Calculate()' id='txtcgst${i}_${sno}'>${CGST_rates}</select>
                                                    <span class="td-sub-content bracket-enclosed" name="cgstamt" id="txtcgstamt${i}">0.00</span>
                                                </td>
                                                <td>
                                                    <select class='form-control' name='sgst' onchange='Calculate()' id='txtsgst${i}_${sno}'>${SGST_rates}</select>
                                                    <span class="td-sub-content bracket-enclosed" name="sgstamt" id="txtsgstamt${i}">0.00</span>
                                                </td>
                                                <td>
                                                    <select class='form-control' name='igst' onchange='Calculate()' id='txtigst${i}_${sno}'>${IGST_rates}</select>
                                                    <span class="td-sub-content bracket-enclosed" name="igstamt" id="txtigstamt${i}">0.00</span>
                                                </td>
                                                <td hidden='hidden'>
                                                    <input type='text' name='item_id' id='item_id_${i}' value="${item['item_id']}">
                                                    <input type='text' name='item_make' id='item_make_${i}' value="${item['make_id']}" >
                                                </td>
                                                <td hidden='hidden'>
                                                    <input type='text' name='item_unit' id='item_unit_${i}' value="${item['unit_id']}" >
                                                    <input type='text' name='item_dc_id' id='item_dc_id_${i}' value="${item['dc_id']}">
                                                    <input type='text' name='is_faulty' id='is_faulty_${i}' value="${item['is_faulty']}">
                                                    <input type='text' name='alternate_unit_id' id='alternate_unit_id_${i}' value="${item['alternate_unit_id']}">
                                                    <input type='text' name='scale_factor' id='scale_factor_${i}' value="${item['scale_factor']}">
                                                </td>
                                            </tr>`;
                                $('#drcrtable').append(row4).addClass('tbl');
                                $("#txtcgst"+i+"_"+sno).val(item['cgst_code']);
                                $("#txtsgst"+i+"_"+sno).val(item['sgst_code']);
                                $("#txtigst"+i+"_"+sno).val(item['igst_code']);
                            }
                        }
                        //Credit Note
                        if($('#id_icd_ignore_credit_note').val() != "True"){
                            creditnote_rate_diff = 0
                            if (item['inv_rate']<poPrice){
                                creditnote_rate_diff = (item['po_no'] != null? (item['po_price'] -item['inv_rate']).toFixed(5):(item['store_price']-item['inv_rate']).toFixed(5));
                                if (item['acc_qty']>0){
                                    sno = sno+1;
                                    var row5 = `<tr class='${classname}'>
                                                    <td class='text-center'>${sno}</td>
                                                    <td>${item_details}</td>
                                                    <td>Rate Difference</td>
                                                    <td class='text-right validate-icd-qty'>${item['acc_qty']}</td>
                                                    <td class='text-right hsn-wrapper'>
                                                        <input type='text' class='form-control text-right td-hsn-code' name='hsn_code' id='txt_hsn_code_${sno}' value="${item['hsn_code']}" maxlength='9' onchange="validateHsnWithSuggestion(this);" onkeypress="validateStringOnKeyPress(this, event, 'hsn_specialChar');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" >
                                                    </td>
                                                    <td>
                                                        <input type='text' class='form-control text-right validate-icd-rate' name='rate' id='txtrate${i}' value='${creditnote_rate_diff}' maxlength='19' onfocus='setNumberRangeOnFocus(this,13,5)' onblur='Calculate()' style='width: 85%; float:left;'><span class='account-type'>Cr</span>
                                                        <input type='text' class='form-control text-right hide' disabled='disabled' name='Acc_rate' id='txtAccrate${i}' value='${creditnote_rate_diff}' maxlength='16' onfocus='setNumberRangeOnFocus(this,10,5)' onblur='Calculate()' >
                                                        <span class="td-sub-content bracket-enclosed pull-right" >${creditnote_rate_diff} Cr</span>
                                                    </td>
                                                    <td>
                                                        <input type='text' class='form-control text-right text_box_label' disabled='disabled' name='changedrvalue' id='txtchangedrvalue${i}' value="${(item['acc_qty'] *(creditnote_rate_diff)).toFixed(2)}" style='width: 85%; float:left;'><span class='account-type'>Cr</span>
                                                        <input type='text' class='form-control text-right hide' disabled='disabled' name='autodrvalue' id='txtdrvalue${i}' value="${(item['acc_qty'] *(creditnote_rate_diff)).toFixed(2)}" >
                                                        <span class="td-sub-content bracket-enclosed pull-right" >${(item['acc_qty'] *(creditnote_rate_diff)).toFixed(2)} Cr</span>
                                                    </td>
                                                    <td hidden='hidden'>Cr</td>
                                                    <td hidden='hidden'>${item['item_id']}</td>
                                                    <td hidden='hidden'>${item['po_no']}</td>
                                                    <td>
                                                        <select class='form-control' name='cgst' onchange='Calculate()' id='txtcgst${i}_${sno}'>${CGST_rates}</select>
                                                        <span class="td-sub-content bracket-enclosed" name="cgstamt" id="txtcgstamt${i}">0.00</span>
                                                    </td>
                                                    <td>
                                                        <select class='form-control' name='sgst' onchange='Calculate()' id='txtsgst${i}_${sno}'>${SGST_rates}</select>
                                                        <span class="td-sub-content bracket-enclosed" name="sgstamt" id="txtsgstamt${i}">0.00</span>
                                                    </td>
                                                    <td>
                                                        <select class='form-control' name='igst' onchange='Calculate()' id='txtigst${i}_${sno}'>${IGST_rates}</select>
                                                        <span class="td-sub-content bracket-enclosed" name="igstamt" id="txtigstamt${i}">0.00</span>
                                                    </td>
                                                    <td hidden='hidden'>
                                                        <input type='text' name='item_id' id='item_id_${i}' value="${item['item_id']}">
                                                        <input type='text' name='item_make' id='item_make_${i}' value="${item['make_id']}" >
                                                    </td>
                                                    <td hidden='hidden'>
                                                        <input type='text' name='item_unit' id='item_unit_${i}' value="${item['unit_id']}" >
                                                        <input type='text' name='item_dc_id' id='item_dc_id_${i}' value="${item['dc_id']}">
                                                        <input type='text' name='is_faulty' id='is_faulty_${i}' value="${item['is_faulty']}">
                                                        <input type='text' name='alternate_unit_id' id='alternate_unit_id_${i}' value="${item['alternate_unit_id']}">
                                                        <input type='text' name='scale_factor' id='scale_factor_${i}' value="${item['scale_factor']}">
                                                    </td>
                                                </tr>`;
                                    $('#drcrtable').append(row5).addClass('tbl');
                                    $("#txtcgst"+i+"_"+sno).val(item['cgst_code']);
                                    $("#txtsgst"+i+"_"+sno).val(item['sgst_code']);
                                    $("#txtigst"+i+"_"+sno).val(item['igst_code']);
                                }
                            }
                        }
                        //PO Rate Diffrence
                        if (item['po_price']>item['approved_rate']){
                            if (item['acc_qty']>0){
                                sno = sno+1;
                                var row6 = `<tr class='${classname}'>
                                                <td class='text-center'>${sno}</td>
                                                <td>${item_details}</td>
                                                <td>PO Rate Difference</td>
                                                <td class='text-right validate-icd-qty'>${item['acc_qty']}</td>
                                                <td class='text-right hsn-wrapper'>
                                                    <input type='text' class='form-control text-right td-hsn-code' name='hsn_code' id='txt_hsn_code_${sno}' value="${item['hsn_code']}" maxlength='9' onchange="validateHsnWithSuggestion(this);" onkeypress="validateStringOnKeyPress(this, event, 'hsn_specialChar');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');">
                                                </td>
                                                <td>
                                                    <input type='text' class='form-control text-right validate-icd-rate' name='rate' id='txtrate${i}' value ="${(item['po_price']- item['approved_rate']).toFixed(5)}" maxlength='19' onfocus='setNumberRangeOnFocus(this,13,5)' onblur='Calculate()' style='width: 85%; float:left;'><span class='account-type'>Dr</span>
                                                    <input type='text' class='form-control text-right hide' disabled='disabled' name='Acc_rate' id='txtAccrate${i}' value ="${(item['po_price']- item['approved_rate']).toFixed(5)}" maxlength='16' onfocus='setNumberRangeOnFocus(this,10,5)' onblur='Calculate()' >
                                                    <span class="td-sub-content bracket-enclosed pull-right" >${(item['po_price']- item['approved_rate']).toFixed(5)} Dr</span>
                                                </td>
                                                <td>
                                                    <input type='text' class='form-control text-right text_box_label' disabled='disabled' name='changedrvalue' id='txtchangedrvalue${i}' value="${(item['acc_qty'] *(item['po_price']- item['approved_rate'])).toFixed(2)}" style='width: 85%; float:left;'><span class='account-type'>Dr</span>
                                                    <input type='text' class='form-control text-right hide' disabled='disabled' name='autodrvalue' id='txtdrvalue${i}' value="${(item['acc_qty'] *(item['po_price']- item['approved_rate'])).toFixed(2)}" >
                                                    <span class="td-sub-content bracket-enclosed pull-right" >${(item['acc_qty'] *(item['po_price']- item['approved_rate'])).toFixed(2)} Dr</span>
                                                </td>
                                                <td hidden='hidden'>Dr</td>
                                                <td hidden='hidden'>${item['item_id']}</td>
                                                <td hidden='hidden'>${item['po_no']}</td>
                                                <td>
                                                    <select class='form-control' name='cgst' onchange='Calculate()' id='txtcgst${i}_${sno}'>${CGST_rates}</select>
                                                    <span class="td-sub-content bracket-enclosed" name="cgstamt" id="txtcgstamt${i}">0.00</span>
                                                </td>
                                                <td>
                                                    <select class='form-control' name='sgst' onchange='Calculate()' id='txtsgst${i}_${sno}'>${SGST_rates}</select>
                                                    <span class="td-sub-content bracket-enclosed" name="sgstamt" id="txtsgstamt${i}">0.00</span>
                                                </td>
                                                <td>
                                                    <select class='form-control' name='igst' onchange='Calculate()' id='txtigst${i}_${sno}'>${IGST_rates}</select>
                                                    <span class="td-sub-content bracket-enclosed" name="igstamt" id="txtigstamt${i}">0.00</span>
                                                </td>
                                                <td hidden='hidden'>
                                                    <input type='text' name='item_id' id='item_id_${i}' value="${item['item_id']}">
                                                    <input type='text' name='item_make' id='item_make_${i}' value="${item['make_id']}" >
                                                </td>
                                                <td hidden='hidden'>
                                                    <input type='text' name='item_unit' id='item_unit_${i}' value="${item['unit_id']}" >
                                                    <input type='text' name='item_dc_id' id='item_dc_id_${i}' value="${item['dc_id']}">
                                                    <input type='text' name='is_faulty' id='is_faulty_${i}' value="${item['is_faulty']}">
                                                    <input type='text' name='alternate_unit_id' id='alternate_unit_id_${i}' value="${item['alternate_unit_id']}">
                                                    <input type='text' name='scale_factor' id='scale_factor_${i}' value="${item['scale_factor']}">
                                                </td>
                                            </tr>`;
                                    $('#drcrtable').append(row6).addClass('tbl');
                                    $("#txtcgst"+i+"_"+sno).val(item['cgst_code']);
                                    $("#txtsgst"+i+"_"+sno).val(item['sgst_code']);
                                    $("#txtigst"+i+"_"+sno).val(item['igst_code']);
                            }
                        }
                    }
			    }else if (status>1 || status == -2){
                    var qty = 0
                    var acc_rate = 0
                    var is_credit=0
                    var auto_dr_value=0
                    var change_dr_value =0
                    var change_rate =0
                    var drcr = ""
                    change_dr_value = item.amount.toFixed(2)
                    change_rate = item.rate.toFixed(5)
                    is_credit = item.is_credit
                    if(is_credit==0) {drcr="Dr"} else {drcr="Cr"}
                    var is_ignore = false;
                    if (drcr == 'Cr' && $('#id_icd_ignore_credit_note').val()=='True' && rec_against !='Sales Return'){
                        is_ignore = true;
                    }
                    if(Boolean(!is_ignore)){
                        if (item.dc_id!=0){
                            is_return_material = true
                        }
                        if(!is_return_material){
                            price = item.invrate.toFixed(5)
                        }else{
                            price = item.store_price.toFixed(5)
                        }
                        qty = item.qty
                        if (item.reason.toUpperCase()=="SHORTAGE"){
                            acc_rate = price
                            auto_dr_value = (qty * acc_rate).toFixed(2)
                        }else if(item.reason.toUpperCase()=="REJECTED"){
                            acc_rate = price
                            auto_dr_value = (qty * acc_rate).toFixed(2)
                        }else if(item.reason.toUpperCase()=="RATE DIFFERENCE"){
                            if (is_credit==0){ //Dr
                                acc_rate = (item.invrate- item.porate).toFixed(5)
                            }else{            //Cr
                                acc_rate = (item.porate- item.invrate).toFixed(5)
                            }
                            auto_dr_value = (qty * acc_rate).toFixed(2)
                        }else if(item.reason.toUpperCase()=="PO RATE DIFFERENCE"){
                            acc_rate = (item.porate- item.price).toFixed(5)
                            auto_dr_value = (qty * acc_rate).toFixed(2)
                        }else if(item.reason.toUpperCase()=="RETURN"){
                            acc_rate = price
                            auto_dr_value = (qty * acc_rate).toFixed(2)
                        }

                        sno = sno+1;
                        if (item.item_id!="" && item.item_id !=null){ classname='no-bg' } else{ classname='tr-alert-danger' }

                        if (item.drawing_no != "" && item.drawing_no !=null) {item_details = item.drawing_no + " - " + item.item_name}
                        else{
                        item_details = item.item_name
                        };


                        if (item.make_id != 1){ item_details = item_details + " <i>[" + item.make_name + "]</i>"; }
                        if (item.is_faulty == 1){ item_details = item_details + " <i>[Faulty]</i>"; }
                        if(item.is_service == 1){
                            item_details += `<span class="service-item-flag"></span>`;
                        }
                        var row7 = `<tr class="${classname}">
                                        <td class='text-center'>${sno}</td>
                                        <td>${item_details}</td>
                                        <td>${item.reason}</td>
                                        <td class='text-right validate-icd-qty'>${qty.toFixed(3)}</td>
                                        <td class="hsn-wrapper">
                                            <input type='text' class='form-control text-right td-hsn-code' name='hsn_code' id='txt_hsn_code_${sno}' value ='${item.hsn_code}' maxlength='9' onchange="validateHsnWithSuggestion(this);" onkeypress="validateStringOnKeyPress(this, event, 'hsn_specialChar');" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');">
                                        </td>
                                        <td>
                                            <input type='text' class='form-control text-right validate-icd-rate' name='rate' id='txtrate${i}' value ='${change_rate}' maxlength='19' onfocus='setNumberRangeOnFocus(this,13,5)' onblur='Calculate()' style='width: 85%; float:left;'><span class='account-type'>${drcr}</span>
                                            <input type='text' class='form-control text-right hide' disabled='disabled' name='Acc_rate' id='txtAccrate${i}' value ='${acc_rate}' maxlength='16' onfocus='setNumberRangeOnFocus(this,10,5)' onblur='Calculate()' >
                                            <span class="td-sub-content bracket-enclosed pull-right" >${acc_rate} ${drcr}</span>
                                        </td>
                                        <td>
                                            <input type='text' class='form-control text-right text_box_label' disabled='disabled' name='changedrvalue' id='txtchangedrvalue${i}' value ='${change_dr_value}' style='width: 85%; float:left;'><span class='account-type'>${drcr}</span>
                                            <input type='text' class='form-control text-right hide' disabled='disabled' name='autodrvalue' id='txtdrvalue${i}' value ='${auto_dr_value}' >
                                            <span class="td-sub-content bracket-enclosed pull-right" >${auto_dr_value} ${drcr}</span>
                                        </td>
                                        <td hidden='hidden'>${drcr}</td>
                                        <td hidden='hidden'>${item.drawing_no}</td>
                                        <td hidden='hidden'>${item.po_no}</td>
                                        <td>
                                            <select class='form-control' name='cgst' onchange='Calculate()' id='txtcgst${i}_${sno}'>${CGST_rates}</select>
                                            <span class="td-sub-content bracket-enclosed" name="cgstamt" id="txtcgstamt${i}">0.00</span>
                                        </td>
                                        <td>
                                            <select class='form-control' name='sgst' onchange='Calculate()' id='txtsgst${i}_${sno}'>${SGST_rates}</select>
                                            <span class="td-sub-content bracket-enclosed" name="sgstamt" id="txtsgstamt${i}">0.00</span>
                                        </td>
                                        <td>
                                            <select class='form-control' name='igst' onchange='Calculate()' id='txtigst${i}_${sno}'>${IGST_rates}</select>
                                            <span class="td-sub-content bracket-enclosed" name="igstamt" id="txtigstamt${i}">0.00</span>
                                        </td>
                                        <td hidden='hidden'>
                                            <input type='text' name='item_id' id='item_id_${i}' value='${item.item_id}'>
                                            <input type='text' name='item_make' id='item_make_${i}' value='${item.make_id}' disabled='true' >
                                        </td>
                                        <td hidden='hidden'>
                                            <input type='text' name='item_unit' id='item_unit_${i}' value='${item.unit_id}' >
                                            <input type='text' name='item_dc_id' id='item_dc_id_${i}' value='${item.dc_id}' >
                                            <input type='text' name='is_faulty' id='is_faulty_${i}' value='${item.is_faulty}'>
                                            <input type='text' name='alternate_unit_id' id='alternate_unit_id_${i}' value="${item['alternate_unit_id']}">
                                            <input type='text' name='scale_factor' id='scale_factor_${i}' value="${item['scale_factor']}">
                                        </td>
                                    </tr>`;
                        $('#drcrtable').append(row7).addClass('tbl');
                        $("#txtcgst"+i+"_"+sno).val(item.CGST);
                        $("#txtsgst"+i+"_"+sno).val(item.SGST);
                        $("#txtigst"+i+"_"+sno).val(item.IGST);
                        }
                    }

			});// css for update the table row color and border line

			if ((status == 1 || status==5) && rec_against!="Note" ){
				if (rec_against!="Sales Return" ){
                    sno = sno+1;
                    var row8 = `<tr>
                                    <td class='text-center'>${sno}</td>
                                    <td>
                                        <input type='text' id='txtDescription' name='otherDescription' class='form-control' value='' placeholder='Enter Description' maxlength='150' onkeypress='validateStringOnKeyPress(this,event, "alphaSpecialChar")' onblur='validateStringOnBlur(this, event, "alphaSpecialChar")' />
                                    </td>
                                    <td>
                                        <input type='text' hidden='hidden' id='txtReason' name ='otherReason' value='Others'/>
                                        Others
                                    </td>
                                    <td>
                                        <input type='text' id='txtQty' name='otherQty' class='form-control text-right' maxlength='16' onfocus='setNumberRangeOnFocus(this,12,3)' onblur='Calculate()' value='0.00' />
                                    </td>
                                    <td class="hsn-wrapper">
                                        <input type='text' id='txt_othersHsn_code_${sno}' name='othersHsn_code' class='form-control text-right td-hsn-code' maxlength='9' onchange="validateHsnWithSuggestion(this);" onkeypress='validateStringOnKeyPress(this,event, "hsn_specialChar")' onblur='return validateStringOnBlur(this, event, "alphaSpecialChar");'  value='' />
                                    </td>

                                    <td>
                                        <input type='text' id='txtRate' name='otherRate' class='form-control text-right validate-icd-rate' value='0.00' maxlength='19' onfocus="setNumberRangeOnFocus(this,13,5)" onblur='Calculate()' />
                                         <span class='checkbox checkbox-border' style='padding-top: 0 !important; padding-bottom: 0 !important;'>
                                            <input name='is_credit' id='id_is_credit_${i}' type='checkbox' onChange='Calculate()'>
                                            <label for='id_is_credit_${i}'>Is Credit</label>
                                        </span>
                                    </td>
                                    <td>
                                        <input type='text' id='txtAmount' name='otherAmount' class='form-control text-right' disabled='disabled' value='0.00' />
                                    </td>
                                    <td hidden='hidden'>Dr</td>
                                    <td hidden='hidden'>0</td>
                                    <td hidden='hidden'>0</td>
                                    <td>
                                        <select class='form-control' name='other_cgst' onchange='Calculate()' id='txtothercgst${i}_${sno}'>${CGST_rates}</select>
                                        <span class="td-sub-content bracket-enclosed" name='other_cgstamt' id="txtothercgstamt${i}">0.00</span>
                                    </td>
                                    <td>
                                        <select class='form-control' name='other_sgst' onchange='Calculate()' id='txtothersgst${i}_${sno}'>${SGST_rates}</select>
                                        <span class="td-sub-content bracket-enclosed" name='other_sgstamt' id="txtothersgstamt${i}">0.00</span>
                                    </td>
                                    <td>
                                        <select class='form-control' name='other_igst' onchange='Calculate()' id='txtotherigst${i}_${sno}'>${IGST_rates}</select>
                                        <span class="td-sub-content bracket-enclosed" name='other_igstamt' id="txtotherigstamt${i}">0.00</span>
                                    </td>
                                </tr>`;
                    $('#drcrtable').append(row8).addClass('tbl');
                }
                add_new_others = ""
                if (rec_against!="Sales Return" ){
                    add_new_others = `<input type='button' class='add_tax_btn btn btn-save' id='add_new_form' onclick='javascript:addRow();' value='+'>`
                }
				var row9 = `<tr>
                                <td>${add_new_others}</td>
                                <td></td>
                                <td></td>
                                <td class='hide'></td>
                                <td class='hide'></td>
                                <td></td>
                                <td class='text-right'>Total</td>
                                <td hidden='hidden'></td>
                                <td></td>
                                <td class='text-right hide'>
                                    <label id='auto_total_amt'></label>
                                </td>
                                <td class='text-right'>
                                    <input type='text'  id='txttotamount' class='form-control hide' disabled='disabled' value='0'/>
                                    <span id="txttotamount-txt"></span>
                                </td>
                                <td class='text-right'>
                                    <label id='cgst_total_amt'></label>
                                </td>
                                <td class='text-right'>
                                    <label id='sgst_total_amt'></label>
                                </td>
                                <td class='text-right'>
                                    <label id='igst_total_amt'></label>
                                </td>
                            </tr>`;
				$('#drcrtable').append(row9).addClass('tbl');
				$('#cmdupdate').show();
				$('#cmdupdate_up').show();
				$('#cmdverify').hide();
				loadTaxesOfNote(status, grn_id);
			} else if (status > 1 || status == -2) {
				$.ajax({
				    url: "/erp/auditing/json/icd/loadgeneraldetails/",
				    type: "post",
				    datatype: "json",
				    data: {receipt_no: grn_id},
				    success: function (response) {
				        if (response != ""){
				            $.each(response, function(i, item) {
					            if (item[1] != ""){
				                    sno = sno+1;
				                    var is_credit = ""
				                    if (item[6] == 1){ is_credit = "checked" }
				                    classname = 'tr-alert-danger'
				                    var row10 = `<tr class=${classname}>
                                                    <td class='text-center'>${sno}</td>
                                                    <td>
                                                        <input type='text' name='otherDescription' id='txtDescription' class='form-control' value='${item[1]}' placeholder='Enter Description' maxlength='150' onkeypress='validateStringOnKeyPress(this,event, "alphaSpecialChar")' onblur='validateStringOnBlur(this, event, "alphaSpecialChar")' />
			                                        </td>
                                                    <td>
                                                        <input type='text' id='txtReason' name ='otherReason' hidden value='${item[2]}' />
                                                        ${item[2].camelize()}
                                                    </td>
                                                    <td>
                                                        <input type='text' name='otherQty' id='txtQty' class='form-control text-right' maxlength='16' onfocus='setNumberRangeOnFocus(this,12,3)' onblur='Calculate()' value='${item[3]}' />
    				                                </td>
    				                                <td class="hsn-wrapper">
                                                        <input type='text' name='othersHsn_code' id='txt_othersHsn_code_${sno}' class='form-control text-right td-hsn-code' maxlength='9' onkeypress='validateStringOnKeyPress(this,event, "hsn_specialChar")' onblur='return validateStringOnBlur(this, event, "alphaSpecialChar");' onchange="validateHsnWithSuggestion(this);" value='${item[3]}' />
      				                                </td>
                                                    <td>
                                                        <input type='text' name='otherRate' id='txtRate' class='form-control text-right validate-icd-rate' value='${item[4]}' maxlength='19' onfocus='setNumberRangeOnFocus(this,13,5)' onblur='Calculate()' />
                                                        <span class='checkbox checkbox-border' style='padding-top: 0 !important; padding-bottom: 0 !important;''>
                                                            <input name='is_credit' id='id_is_credit_${i}' type='checkbox' ${is_credit} onChange='Calculate()' />
                                                            <label for='id_is_credit_${i}'>Is Credit</label>
                                                        </span>
                                                    </td>
				                                    <td>
                                                        <input type='text' name='otherAmount' id='txtAmount' class='form-control text-right' disabled='disabled' value='0.00'/>
                                                    </td>
                                                    <td hidden='hidden'>Dr</td>
                                                    <td hidden='hidden'>0</td>
                                                    <td hidden='hidden'>0</td>
                                                    <td>
                                                        <select class='form-control' name='other_cgst' onchange='Calculate()' id='txtothercgst${i}_${sno}'>${CGST_rates}</select>
                                                        <span class="td-sub-content bracket-enclosed" name='other_cgstamt' id="txtothercgstamt${i}">0.00</span>
                                                    </td>
                                                    <td>
                                                        <select class='form-control' name='other_sgst' onchange='Calculate()' id='txtothersgst${i}_${sno}'>${SGST_rates}</select>
                                                        <span class="td-sub-content bracket-enclosed" name='other_sgstamt' id="txtothersgstamt${i}">0.00</span>
                                                    </td>
                                                    <td>
                                                        <select class='form-control' name='other_igst' onchange='Calculate()' id='txtotherigst${i}_${sno}'>${IGST_rates}</select>
                                                        <span class="td-sub-content bracket-enclosed" name='other_igstamt' id="txtotherigstamt${i}">0.00</span>
                                                    </td>
                                                </tr>`;
				                    $('#drcrtable').append(row10).addClass('tbl');
				                    $("#txtothercgst"+i+"_"+sno).val(item[7]);
                                    $("#txtothersgst"+i+"_"+sno).val(item[8]);
                                    $("#txtotherigst"+i+"_"+sno).val(item[9]);
				                }
				            });
				        }
				        add_new_others = ""
                        if (rec_against!="Sales Return" ){
                            add_new_others = `<input type='button' class='add_tax_btn btn btn-save' id='add_new_form' onclick='javascript:addRow();' value='+'>`
                        }
				        var row11 = `<tr>
                                        <td>${add_new_others}</td>
                                        <td></td>
                                        <td></td>
                                        <td class='hide'></td>
                                        <td class='hide'></td>
                                        <td></td>
                                        <td class='text-right'>Total</td>
                                        <td hidden='hidden'></td>
                                        <td></td>
                                        <td class='text-right hide'>
                                            <label id='auto_total_amt'></label>
                                        </td>
                                        <td class='text-right'>
                                            <input type='text' id='txttotamount' class='form-control hide' disabled='disabled' value='0'/>
                                            <span id="txttotamount-txt"></span>
                                        </td>
                                        <td class='text-right'>
                                            <label id='cgst_total_amt'></label>
                                        </td>
                                        <td class='text-right'>
                                            <label id='sgst_total_amt'></label>
                                        </td>
                                        <td class='text-right'>
                                            <label id='igst_total_amt'></label>
                                        </td>
                                    </tr>`;
                        $('#drcrtable').append(row11).addClass('tbl');
                        loadTaxesOfNote(status, $("#id-note_id").val());
				    }
				});
				if(status != 5 && status != -2){
                    $('#cmdupdate').hide();
                    $('#cmdupdate_up').hide();
                    $('#cmdverify').show();
				}else{
                    $('#cmdupdate').show();
                    $('#cmdupdate_up').show();
                    $('#cmdverify').hide();
				}
			}
			clear_po_tax_table();

			$.ajax({
                url: "/erp/auditing/json/loadicdtags/",
                type: "post",
                datatype: "json",
                data: {receipt_no: grn_id},
                success: function (response) {
                    $("#grn_tags_table").html('');
	                $.each(response, function (i, item) {
                        var row = "<li class='li-tagit-display'>" +
                                    "<label class='label-tagit-display'>"+item[0]+"</label>"+
                                    "<div class='hidden-div-tagit-id' hidden='hidden'>"+ item[1] +"</div>"+
                                    "<a class='delete_tag' ></a>"  + "</li>";
			            $('#grn_tags_table').append(row).addClass('tbl');

                    });
                    create_delete_tag_button();
                }
            });

            if ((status == 1 || status==5) && rec_against!="Note" ){
                $.ajax({
                    url: "/erp/auditing/json/loadgrntax/",
                    type: "post",
                    datatype: "json",
                    data: {receipt_no: grn_id},
                    success: function (response) {
                        $("#grn_taxes_table").html('');
                        $.each(response, function (i, item) {

                            var row = "<li class='li-tagit-display'>" + item['tax_code'] +" @ " + item['rate'] + " %: " +
                                        "<label class='label-tagit-display'>" + item['value'].toFixed(2) + "</label></li>";
                            $('#grn_taxes_table').append(row).addClass('tbl');

                        });
                        create_delete_tag_button();
                    }
                });
            }

            $.ajax({
                url: "/erp/stores/json/grn/loadgrnmaterial/",
                type: "post",
                datatype: "json",
                data:{grn_id: grn_id,
                  received_against:rec_against,
                  selected_supplier: null,
                  goods_already_received:false},
                success: function (response) {
                    $("#grn_table").find("tr:gt(1)").remove();
                    var isGrnWithPo = false;
                    $.each(response, function (i, item) {
                        cgst_tax_amt=0
                        sgst_tax_amt=0
                        igst_tax_amt=0
						txt_total_amount=0;
                        if (!isNaN(parseFloat(item['discount']))){
				            txt_total_amount = (parseFloat(item['dc_qty']) * parseFloat(item['price']) * (100 - parseFloat(item['discount'])) / 100).toFixed(2);
				        }else{
				            txt_total_amount = (parseFloat(item['dc_qty']) * parseFloat(txt_price[i].value)).toFixed(2);
				        }
						cgst_tax_amt = ((txt_total_amount * parseFloat(item['cgst_rate']))/100).toFixed(2)
						sgst_tax_amt = ((txt_total_amount * parseFloat(item['sgst_rate']))/100).toFixed(2)
						igst_tax_amt = ((txt_total_amount * parseFloat(item['igst_rate']))/100).toFixed(2)

						if (item['drawing_no']!="" && item['drawing_no']!=null){
						    item_details = item['item'] + " - " + item['drawing_no']
						}else{
                            item_details = item['item'];
                        }
//                        if (item['make_id'] != 1){ item_details = item_details + " <i>[" + item['make_name'] + "]</i>"; }
                        var make_name = constructDifferentMakeName(item['make_name']);
                        if (make_name != ""){ item_details = item_details + " <i>[" + make_name + "]</i>"; }
                        if (item['is_faulty'] == 1){ item_details = item_details + " <i>[Faulty]</i>"; }
                        if(item.is_service == 1){
                            item_details += `<span class="service-item-flag"></span>`;
                        }

                        var row_grn_table = "<tr bgcolor='#ececec' border='0' style='font-size:9px; font-weight:normal;'><td align='left'> "+(i+1)+"</td><td align='left'>" +
			                    item_details + "</td><td>" +
			                    item['dc_qty'] + "</td><td>" +
			                    item['rec_qty'] + "</td><td>" +
			                    item['acc_qty'] + "</td><td>" +
			                    item['price'] + "</td><td>" +
			                    item['discount'] + "</td><td>" +
			                    cgst_tax_amt + " @"+ parseFloat(item['cgst_rate']).toFixed(1) +"%</td><td>" +
			                    sgst_tax_amt + " @"+ parseFloat(item['sgst_rate']).toFixed(1) + "%</td><td>" +
			                    igst_tax_amt + " @"+ parseFloat(item['igst_rate']).toFixed(1) + "%</td><td align='center'><span class='checkbox checkbox-border' style='padding: 0 0px 0 22px !important;'><input  name='is_checked' id='auditNoteCheck_"+i+"' type='checkbox' class='multi_check'> <label for='auditNoteCheck_"+i+"'></label></span></td></tr>"
			            $('#grn_table').append(row_grn_table);
			            if (item.po_no != null) {
			                isGrnWithPo = true;
			            }
                    });
                    if(rec_against == "Sales Return") {
                        $(".po_tap").addClass("hide");
                        $(".invoice_tap").removeClass("hide");
                    }
                    else if (isGrnWithPo) {
                        $(".po_tap").removeClass("hide");
                        $(".invoice_tap").addClass("hide");
                    }
                    else {
                        $(".po_tap").addClass("hide");
                        $(".invoice_tap").addClass("hide");
                    }
                    $(".instant_invoice_loading_message").addClass('hide');
                    ICDVerifiedCheck();
                    setTimeout(function(){
                        if (status>1 || status == -2){
                            $('.multi_check').prop('checked', true);
                            if(status ==5 || status == -2){
                                $('#auditNoteCheck').prop('checked', false);
                            }
                        }
					}, 300);
                }
            });
            $(".instant_audit_loading_message").addClass('hide');
		}
	});

	document.getElementById('drcr').style.display='block';
	$('#edit_icd').show();
	$('#view_icd').hide();
    $("#cmdcancel_up").removeClass('hide');
    $(".export_csv, #note_creation").addClass('hide');
    if($("#note_status").val() == 3) {
        $(".single-eInvoice").removeClass("hide");
    }
    $(".multiple-eInvoice").addClass("hide");
}

function loadTaxesOfNote(status, grn_id) {
    if (status == 1){
        url = "/erp/stores/json/grn/loadgrntaxes/"
    }else if (status>1 || status == -2){
        url = "/erp/auditing/json/loadcrdrtaxes/"
    }

    $.ajax({
        url: url,
        type: "post",
        datatype: "json",
        data: {receipt_no: grn_id},
        success: function (response) {
            $.each(response, function (i, po_tax_detail_as_row) {
                $('#note_taxes_table').append(po_tax_detail_as_row).addClass('tbl');
            });
            $("#note_taxes_table").find('.hnd_text_id').each(function(){
                var selectedTax = $(this).val();
                $("#id_note_tax option[value='"+selectedTax+"']").hide();
                $('.chosen-select').trigger('chosen:updated');
            });
            Calculate();
        }
    });
}

function deleteRow(currentRow) {
	try {
		if (window.confirm('Do you want delete this row?')) {
			var table = document.getElementById("drcrtable");
			var rowCount = table.rows.length;
			table.deleteRow(currentRow);
			Calculate();
		}else{
			e.preventDefault();
		}
	} catch (e) {
		//alert(e);
	}
}

function loadgrns() {

     frmdate = $("#fromdate").val();
     todate =  $("#todate").val();
     if($('#cstatus').val() != 'None'){
         $("#search_status").val($('#cstatus').val())
         $("#search_status").trigger("chosen:updated");
     }
     search_status = $("#search_status").val();
     project = (getCookie('project_id') != null? getCookie('project_id') : "0");
     party_id = (getCookie('party_id') != null? getCookie('party_id') : "-1");
     $("#project").val(project);
     $("#project").trigger("chosen:updated");
     $("#party").val(party_id);
     $("#party").trigger("chosen:updated");
    var icd_request_acknowledgement = $("#icd_request_acknowledgement").val();
    var csrf_token = document.getElementsByName('csrfmiddlewaretoken')[0].value;
    var url = window.location.href.replace(/\/$/, '');
    var type = "icd";
    var url = window.location.href;
    if(url.indexOf("is_credit_debit") > 0) {
        type= 'note';
    }

    $('#loading').show();
    var grn_code =""; var grn_status = ""; var pdf_status = "";
    var data_icd_id="";
    $.ajax({
		url: "/erp/auditing/json/icdload/",
		type: "post",
		datatype:"json",
		data: { from_date: frmdate, to_date: todate, party_id: party_id, status: search_status, project: project, type: type},
		success: function(response){
	        $('#cstatus').val("None"); // clearing badge count hit
	        $("#icdList").find("tr:gt(0)").remove();
			$('#icdList').DataTable().clear();
			$('#icdList').DataTable().destroy();
	        $.each(response, function(i, item) {
                pdf_status = item['status'];
                if(item['status'] == 0){
                    grn_status = "Draft";
                }else if(item['status'] == 1){
                    grn_status = "Pending";
                }else if(item['status'] == 2 || item['status'] == 6){
                    if (icd_request_acknowledgement == "True" && item['note_amt'] != 0){
                        grn_status = "Party Acknowledgement Pending";
                        pdf_status = 6
                    }else {
                        grn_status = "Checked";
                        pdf_status = 2
                    }
                }else if(item['status'] == 3){
                    grn_status = "Verified";
                }else if(item['status'] == 5){
                    grn_status = "Returned";
                }else if(item['status'] == 7){
                    if (icd_request_acknowledgement == "True"){
                        grn_status = "Party Acknowledged";
                        pdf_status = 7
                    }else {
                        grn_status = "Checked";
                        pdf_status = 2
                    }
                }else if(item['status'] == -2){
                    if (icd_request_acknowledgement == "True"){
                        grn_status = "Party Rejected";
                        pdf_status = -2
                    }
                    else{
                        grn_status = "Returned";
                        pdf_status = 5
                    }
                }

                if((item['rec_against'] == "Purchase Order") || item['rec_against'] == "Job Work" || item['rec_against'] == "Delivery Challan" || item['rec_against'] == "Sales Return"){
				    grn_code = item['code'];
				    var edit =` <td class='text-center'>
				                    <a role='button' class='edit_link_code' data-type='${type}' data-current-id='${item['grn_no']}' data-note-id='${item['note_id']}' onclick='javascript:editrow("${item['grn_no']}", "${item['note_code']}", "${type}", "${item['note_id']}", "${pdf_status}")'>${grn_code}
                                    </a>
                                </td>`;
                    var status = `<a class='${grn_status.toLowerCase()} table-inline-icon-bg'>${grn_status}</a>`;
                    if (grn_status != "Pending"){
                        status += `<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Preview'  onclick='javascript:generate_pdf_ajax(${item['grn_no']},"${pdf_status}",${item['note_id']});'>
                                        <i class='fa fa-file-text'></i>
                                    </span>`;
                    }
                    status += `<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit' role='button' data-status='${item[8]}' onclick='javascript:editrow("${item['grn_no']}", "${item['note_code']}", "${type}", "${item['note_id']}", "${pdf_status}")'>
                                    <i class='fa fa-pencil'></i>
                                </span>`;

				}
				else if(item['rec_against'] == "Note"){
				    grn_code = item['note_code'];
				    if (grn_status == "Pending"){
                        var edit ="<td class='text-center'><form id=\'note_edit\' data-type='"+type+"' data-current-id='"+item['grn_no']+"' data-note-id='"+item['note_id']+"'  method=\'post\' action=\'/erp/auditing/note/edit/\'>" +
                        "<input type=\'hidden\' name=\'csrfmiddlewaretoken\' value=\'" +csrf_token+ "\' />" +
                        "<input type=\'hidden\' value=\'"+item['grn_no']+"\' id=\'id_receipt_no\' name=\'receipt_no\'/>" +
                        "<input type=\'hidden\' value=\'"+item['note_id']+"\' id=\'id-note_id\' name=\'note_id\'/>" +
                        "<input type=\'submit\' id=\'editNote_"+item['note_id']+"\' hidden=\'hidden\'/></form>" +
                        "<a role='button' class='edit_link_code' onclick=\"javascript:clickButton(\'editNote_"+item['note_id']+"\');\">"+ grn_code +"</a></td>";
                        var status = `  <a class='${grn_status.toLowerCase()} table-inline-icon-bg'>${grn_status}</a>
                                        <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit' role='button' onclick="clickButton('editNote_${item["note_id"]}')">
                                            <i class='fa fa-pencil'></i>
                                        </span>`;
				    }
				    else {
				        var edit =` <td class='text-center'>
                                        <a role='button' class='edit_link_code' data-type='${type}' data-current-id='${item['grn_no']}' data-note-id='${item['note_id']}' onclick='javascript:editrow("${item['grn_no']}", "${item['note_code']}", "${type}", "${item['note_id']}", "${pdf_status}")'>${grn_code}
                                        </a>
                                    </td>`;
                        var status ="<a role='button' class='"+grn_status.toLowerCase()+" pdf_genereate table-inline-icon-bg' onclick='javascript:generate_pdf_ajax("+item['grn_no']+",\""+pdf_status+"\","+ item['note_id'] + ");'>"+ grn_status +"</a>"+
                                "<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Preview'  onclick='javascript:generate_pdf_ajax("+item['grn_no']+",\""+pdf_status+"\","+ item['note_id'] + ");'>"+"<i class='fa fa-file-text'></i>"+"</span>"+
                                `<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit' role='button' data-status='${item[8]}' onclick='javascript:editrow(\"${item['grn_no']}\", \"${item['note_code']}\", \"${type}\", \"${item['note_id']}\", \"${pdf_status}\")'>`+
                                "<i class='fa fa-pencil'></i>"+
                                "</span>"
				    }
				}

                if(item['note_amt'] == 0){
                    note_type = "&nbsp;&nbsp;";
                }else{
                    note_type = item['note_type'] == "1" ? "Cr" : "Dr";
                }

				var gDate = item['grn_date'];
				gDate = moment(gDate, "DD.MM.YYYY").format("MMM D, YYYY");
				if(gDate.toLowerCase().trim() == "invalid date") { gDate = '-'; }
				var dcDate = item['inv_date'];
				dcDate = moment(dcDate, "DD.MM.YYYY").format("MMM D, YYYY");
				if(dcDate.toLowerCase().trim() == "invalid date") { dcDate = '-'; }
                if(item['rec_against'] == "Note"){
				    data_icd_id = item['note_id']
				}else{
				    data_icd_id = item['grn_no']
				}
                var row = "<tr bgcolor='#ececec' border='0' align='center' style='font-size:12px; font-weight:normal;' data-icdid='"+data_icd_id+"' >"+
	                "<td class='text-center'>"+Number(i+1)+".</td><td class='text-center'>" + gDate  + "</td>" +
	                edit  + "<td class='text-left td_ref_no hide'>" +
	                item['grn_code']  + "</td><td class='text-left'>" +
	                item['project']  + "</td><td class='text-left'>" +
	                item['supplier']  + "</td><td class='text-left'>" +
	                item['inv_no']  + "</td><td class='text-center'>" +
	                dcDate  + "</td><td class='text-right'>" +
	                item['inv_value'].toFixed(2)  + "</td><td class='text-right' data-sort="+item['note_amt']+">" +
	                item['note_amt'].toFixed(2)  + " " + note_type + "</td><td class='td_status'>" +
	                status + "</td></tr>";
                $('#icdList').append(row).addClass('tbl');
	        });
			TableHeaderFixed();
	        if ($("#icdList tr").length == 1){
                var row = "<tr bgcolor='#ececec' border='0' align='center' style='font-size:11px; font-weight:bold;'><td colspan=12 class='td-empty-result'>" +
                    "No matching Results Found" + "</td></tr>";
				$('#icdList').DataTable().clear();
				$('#icdList').DataTable().destroy();
                $('#icdList').append(row).addClass('tbl');
	        }
            updateFilterText();
	        $('#loading').hide();
            closeFilterOption();
        },
	    error: function (xhr, errmsg, err) {
	        //console.log(xhr.status + ": " + xhr.responseText);
	    }
    });
}

function updateTableStatus(status, icd_id, note_id){
    var url = window.location.href;
    if(url.indexOf("is_credit_debit") > 0) {
        var editedRow = $("#icdList").find("tr[data-icdid='"+note_id+"']");
    }else{
        var editedRow = $("#icdList").find("tr[data-icdid='"+icd_id+"']");
    }
    if(status == 3) {
        editedRow.find("td.td_status").find("a").removeAttr('class').addClass('pdf_genereate verified').text("Verified");
    }
    if(status == 5) {
        editedRow.find("td.td_status").find("a").removeAttr('class').addClass('pdf_genereate returned').text("Returned");
    }
    if(status == 6) {
        editedRow.find("td.td_status").find("a").removeAttr('class').addClass('pdf_genereate pending').text("Party Acknowledgement Pending");
    }
    if(status == 7) {
        editedRow.find("td.td_status").find("a").removeAttr('class').addClass('pdf_genereate acknowledged').text("Party Acknowledged");
    }
    if(status == -2) {
        editedRow.find("td.td_status").find("a").removeAttr('class').addClass('pdf_genereate rejected').text("Party Rejected");
    }
    editedRow.find(".pdf_genereate").attr("onclick", "generate_pdf_ajax('"+icd_id+"', "+status+", "+note_id+")");
}

var oTable;
var oSettings;
function TableHeaderFixed(){
    var url = window.location.href;
    if(url.indexOf("is_credit_debit") > 0) {
        $('.td_ref_no').removeClass('hide exclude_export');
    }
    else {
        $('.td_ref_no').addClass('hide exclude_export');
    }
	oTable = $('#icdList').DataTable({
		fixedHeader: false,
        "scrollY": Number($(document).height() - 230),
        "scrollX": true,
		"pageLength": 50,
		"search": {
			"smart": false
		},
		"columns": [
			null,
			{ "type": "date" },
			null,null,null,null,null,
			{ "type": "date" },
			null,null,null
			]
	});
	oTable.on("draw",function() {
		var keyword = $('#icdList_filter > label:eq(0) > input').val();
		$('#icdList').unmark();
		$('#icdList').mark(keyword,{});
        setHeightForTable();
        listTableHoverIconsInit('icdList');
        $( window ).resize();
	});
	oTable.on('page.dt', function() {
	  $('html, body').animate({
		scrollTop: $(".dataTables_wrapper").offset().top - 15
	   }, 'slow');
      listTableHoverIconsInit('icdList');
	});
    oSettings = oTable.settings();
    listTableHoverIconsInit('icdList');
    $( window ).resize();
}

function addPoTax(tax_code){
// TODO Tax Row generation: Reorganise JS
    $.ajax({
	    url: "/erp/purchase/json/po/add_tax/",
	    type: "POST",
	    dataType: "json",
	    data: {code: tax_code},
	    success:function (response) {
	        $.each(response, function(i, tax_detail_as_row){
                $('#note_taxes_table').append(tax_detail_as_row);
	        });
            Calculate();    // Calculate the Total Price after applying new Tax profile
	    },
	    error: function (xhr, errmsg, err) {
	        //console.log(xhr.status + ": " + xhr.responseText);
	    }
	});
    $("#id_note_tax option:selected").hide(); // Remove the added Tax from the Tax-list to avoid multiple addition
    $("#id_note_tax").val('');    // Reset the Tax Select input
}

function removePoTax(tax_code){
    tax_rows = document.getElementsByName('tr_' + tax_code);
    for(i=tax_rows.length - 1; i>=0; i--){
        // Removed in reverse order as the removal renumbers the RowIndices immediately
        document.getElementById("note_taxes_table").deleteRow(tax_rows[i].rowIndex);
    }
    $("#id_note_tax option[value='"+tax_code+"']").show();    // Re-show the tax in the Tax list
	$('.chosen-select').trigger('chosen:updated');
    Calculate();    // Calculate the PO price after Removing the Tax
}

function clear_po_tax_table() {
    var table = document.getElementById("note_taxes_table");
    var rowCount = table.rows.length;
    if (rowCount >= 0) {
        for (var i = 0; i < rowCount; i++) {
            table.deleteRow(i);
            rowCount--;
            i--;
        }
    }
}

function checkICDNote(){
    $('.nav-pills li').removeClass('active');
    var url = window.location.href;
    if(url.indexOf("is_credit_debit") > 0) {
        $('#credit_debit').addClass('active');
        $('#li_internal_control').removeClass('active');
    }
    else {
        $('#li_internal_control').addClass('active');
    }
}

$(function () {
		var tags = []
		var zid =  ""
		$.ajax({
			url: "/erp/purchase/json/po/loadtags/",
			type: "post",
			datatype:"json",
			data: zid,
			success: function(response){
			        for (i = 0; i <= response.length - 1 ; i++) {
					tags.push({value:response[i][0],label:response[i][1]});
				}
				}
		});
            $("#id_grn_tag").autocomplete({
                source: function(request, response) {
                    var results = $.ui.autocomplete.filter(tags, request.term);
                    response(results.slice(0, 30));
                },
                select: function (event, ui) {
                    event.preventDefault();
                    $("#id_grn_tag").val(ui.item.label);
                    $("#grn_tag_value").val(ui.item.value);
                    $("#add_grn_tag").click();
                }
            });
	});

    function create_delete_tag_button() {
        $(".delete_tag").click(function(){
            $(this).closest('li').remove();
        });
    }

function returnRemarks(){
	swal({
	  title: "Enter Remarks",
	  text: "",
	  type: "input",
	  showCancelButton: true,
	  closeOnConfirm: false,
	  inputPlaceholder: "Enter Remarks"
	}, function (inputValue) {
	  if (inputValue === false) return false;
	  if (inputValue === "") {
	    swal.showInputError("You need to write something!");
	    return false
	  }
	  $.ajax({
	        url: "/erp/auditing/json/returnGrn/",
	        type: "post",
	        datatype: "json",
	        data: {grn_number: $("#txtgrn_id").val(),
	            audit_remarks: inputValue,
	            return_status: 4},
	        success: function (response) {
	        	$("#loading").hide();
	            var message_type = 'warning';
	            if (response.response_message == "Success") {
	            	message_type = 'success';
		            	swal({title: "", text: "Returned Successfully", type: message_type,
	                        showCancelButton: false,
	                        confirmButtonColor: "#209be1",
	                        confirmButtonText: "Ok",
	                        closeOnConfirm: true,
	                        closeOnCancel: true,
	                        allowEscapeKey : false,
       						allowOutsideClick: false
	                },
	                function() {
	                        window.location.href = "/erp/auditing/icd/";
	                });
	            }
	            else {
	            	swal("", response.custom_message, "warning")
	            }
	        }
	  });
	  swal.close();
	});
	$("input[placeholder='Enter Remarks']").attr("maxlength", 248);
}