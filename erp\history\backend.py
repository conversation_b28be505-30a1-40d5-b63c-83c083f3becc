"""
Backend for logging Change History of all objects
"""
from erp.history import logger
from erp.models import ChangeLog
from settings import SQLASession

__author__ = 'sara<PERSON>n'


def saveChangeLog(timestamp, enterprise_id, module_code, doc_id, data, user_id):
	"""
	Save Change Logs
	
	:param timestamp: 
	:param enterprise_id: 
	:param module_code: 
	:param doc_id: 
	:param data: 
	:param user_id: 
	:return: 
	"""
	logger.info('Inside Change Log...')
	db_session = SQLASession()
	db_session.begin(subtransactions=True)
	try:
		log_to_be_saved = ChangeLog(timestamp=timestamp, enterprise_id=enterprise_id, module_code=module_code,
									doc_id=doc_id, details=data, user_id=user_id)
		logger.info(log_to_be_saved)
		db_session.add(log_to_be_saved)
		db_session.commit()
	except Exception as e:
		db_session.rollback()
		logger.exception(e)
