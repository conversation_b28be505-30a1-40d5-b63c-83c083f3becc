{% extends "sales/sidebar.html" %}
{% block salesHome %}
<link type="text/css" rel="stylesheet" href="/site_media/css/account.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/dashboard.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/Fonts.css?v={{ current_version }}">

<script type="text/javascript" src="/site_media/js/jquery.slimscroll.js?v={{ current_version }}"></script>
<style type="text/css">
    #loading {
        display: block;
    }

</style>
<div class="right-content-container col-sm-12">
	<div class="page-title-container">
		<span class="page-title">Dashboard</span>
	</div>
	<div class="container" style="margin-top: 20px;">
		<div class="col-md-5">
			<div class="header-box header-box-2">
				<div class="header-box-value">
					<i class="fa fa-inr fa-black fa-5" aria-hidden="true"></i>
					{% if response %}
					{{ response.sales_revenue }}
					{% endif %}
				</div>
				<span style="font-size: 10px; position: absolute; top: 67px; left: 190px;">(Since {{ fiscal_start_date|date:'M d, Y'}})</span>
				<div class="header-box-text">
					Sales Revenue
				</div>
			</div>
			<h3>Sales Revenue</h3>
			<div id="chart_div_1"></div>
		</div>
		<div class="col-md-7">
			<h3 style="margin-top:11px;">Receivable Bills Ageing</h3>
			<div class="invoice-box bills">
				<div class="invoice-box-content" id="receivable_advance" role="button">
                    <span class="invoice-box-text">
                        {{ 'Advances' }}
                    </span>
					<span class="invoice-box-value">
                        <i class="fa fa-inr fa-black fa-4"
                           aria-hidden="true"></i> {{ response_bills.receivable_aging.advance|floatformat:2 }}
                    </span>
					<input type="hidden" id="receivable_advance_particulars"
					       value="{{ response_bills.receivable_aging.advance_particulars }}">
				</div>
				<div class="invoice-box-content" id="receivable_billable" role="button">
                    <span class="invoice-box-text">
                        {{ 'Un-Billed' }}
                    </span>
					<span class="invoice-box-value">
                        <i class="fa fa-inr fa-black fa-4"
                           aria-hidden="true"></i> {{ response_bills.receivable_aging.billable|floatformat:2 }}
                    </span>
					<input type="hidden" id="receivable_billable_particulars"
					       value="{{ response_bills.receivable_aging.billable_particulars }}">
				</div>
				<div class="invoice-box-content box-clickable"
				     onclick="getAgeingDetails(null, true, 'ledger_bills', 0, 0, true);">
                            <span class="invoice-box-text">
                                {{ 'Excess Received' }}
                            </span>
					<span class="invoice-box-value">
                                <i class="fa fa-inr fa-black fa-4"
                                   aria-hidden="true"></i> {{ response_bills.receivable_aging.excess|floatformat:2 }}
                            </span>
				</div>
				<div class="invoice-box-content box-clickable"
				     onclick="getAgeingDetails(null, true, 'ledger_bills', 0, 30, false);">
                            <span class="invoice-box-text">
                                {{ '< 30 Days' }}
                            </span>
					<span class="invoice-box-value">
                                <i class="fa fa-inr fa-black fa-4"
                                   aria-hidden="true"></i> {{ response_bills.receivable_aging.age1|floatformat:2 }}
                            </span>
				</div>
				<div class="invoice-box-content box-clickable"
				     onclick="getAgeingDetails(null, true, 'ledger_bills', 30, 60, false);">
                            <span class="invoice-box-text">
                                {{ '30 - 60 Days' }}
                            </span>
					<span class="invoice-box-value">
                                <i class="fa fa-inr fa-black fa-4"
                                   aria-hidden="true"></i> {{ response_bills.receivable_aging.age2|floatformat:2 }}
                            </span>
				</div>
				<div class="invoice-box-content box-clickable"
				     onclick="getAgeingDetails(null, true, 'ledger_bills', 60, 90, false);">
                            <span class="invoice-box-text">
                                {{ '60 - 90 Days' }}
                            </span>
					<span class="invoice-box-value">
                                <i class="fa fa-inr fa-black fa-4"
                                   aria-hidden="true"></i> {{ response_bills.receivable_aging.age3|floatformat:2 }}
                            </span>
				</div>
				<div class="invoice-box-content box-clickable"
				     onclick="getAgeingDetails(null, true, 'ledger_bills', 90, 0, false);">
                            <span class="invoice-box-text">
                                {{ '> 90 Days' }}
                            </span>
					<span class="invoice-box-value">
                                <i class="fa fa-inr fa-black fa-4"
                                   aria-hidden="true"></i> {{ response_bills.receivable_aging.age4|floatformat:2 }}
                            </span>
				</div>
				<div class="invoice-box-content box-clickable_2" onclick="getAgingByTotalDue(true)">
                    <span class="invoice-box-text total">
                        Total Due
                    </span>
					<span class="invoice-box-value total">
                        <i class="fa fa-inr fa-black fa-4"
                           aria-hidden="true"></i> {{ response_bills.receivable_aging.total|floatformat:2 }}
                    </span>
				</div>
			</div>
			<br>
			<div class="invoice-box bills">
				<div class="invoice-box-content box-clickable_2"
				     onclick="getAgingByDetailedView(true,'overdue',0,0,true)">
                    <span class="invoice-box-text overdue">
                        Overdue
                    </span>
					<span class="invoice-box-value overdue">
                        <i class="fa fa-inr fa-black fa-4"
                           aria-hidden="true"></i> {{ response_bills.receivable_aging.overdue|floatformat:2 }}
                    </span>
				</div>
				<div class="invoice-box-content box-clickable_2"
				     onclick="getAgingByDetailedView(true,'due_in_days',0,0,true)">
                    <span class="invoice-box-text">
                        Due in Next 10 Days
                    </span>
					<span class="invoice-box-value">
                        <i class="fa fa-inr fa-black fa-4"
                           aria-hidden="true"></i> {{ response_bills.receivable_aging.age5|floatformat:2 }}
                    </span>
				</div>
			</div>
		</div>
		<div class="clearfix"></div>
		<div class="col-md-5 hide">
			<h3>On-time Supplies</h3>
			<div id="supplier_chart"></div>
		</div>
		<div class="col-md-12">
			<h3 style="margin-top: 0;">Orders due to be Supplied (Today)</h3>
			<table class="table table-bordered custom-table dashboard_table_green">
				<thead>
					<tr>
						<th>OA Code</th>
						<th>Customer</th>
						<th>Value</th>
					</tr>
				</thead>
				<tbody id="orders_supplied_today">
				</tbody>
				<tfoot>
					<tr class="ontime_supplier_show_all">
						<td colspan="3" class="text-right">
							<a data-toggle="modal" data-target="#order_due_modal" role="button">Show All</a>
						</td>
					</tr>
				</tfoot>
			</table>
		</div>
		<div class="clearfix"></div>
		<div class="col-md-12">
			<h3>OA Overdue <span style="font-size: 12px;position: absolute;right: 15px;margin-top: 21px;">Total Record: <span id="oa_delivery_overdue_count"></span></span></h3>
			<table class="table table-bordered custom-table dashboard_table_blue">
				<thead>
				<tr>
					<th>OA Code</th>
					<th>Customer</th>
					<th>OA Due Date</th>
					<th>Value</th>
				</tr>
				</thead>
				<tbody id="oa_delivery_overdue">

				</tbody>
				<tfoot>
				<tr class="oa_delivery_show_all">
					<td colspan="4" class="text-right">
						<a data-toggle="modal" data-target="#oa_order_due_modal" role="button">Show All</a>
					</td>
				</tr>
				</tfoot>
			</table>
		</div>
		<div class="clearfix"></div>
		<div class="col-md-5">
			<div class="revenue-container" style="margin: 0;">
				<div>
					<h3>Top Geographies</h3>
					<div class="col-md-12" style="margin-bottom: 10px; padding: 0">
						<label>Date Range</label>
						<div id="reportrange_1" class="sales_geographies_report report-range form-control">
							<i class="glyphicon glyphicon-calendar"></i>&nbsp;
							<span></span> <b class="caret"></b>
							<input type="hidden" class="fromdate" id="fromdate_1" name="fromdate"/>
							<input type="hidden" class="todate" id="todate_1" name="todate"/>
						</div>
					</div>
					<div class="col-md-12" style="padding: 0">
						<div class="revenue-box scroll-container">
							<table class="table table-bordered">
								<thead>
								<tr class="revenue-table-header">
									<th colspan="3">
										<span class="revenue-header-text">Top 10 Geographies</span>
									</th>
								</tr>
								<tr class="revenue-table-header">
									<th colspan="3">
										<input type="radio" name="sales_region" value="city" checked>&nbsp;&nbsp;<span style="display: inline-block; margin-right: 24px;">City</span>
										<input type="radio" name="sales_region" value="state" >&nbsp;&nbsp;State
										<!--<input type="radio" name="sales_region" value="Country">&nbsp;&nbsp;Country-->
									</th>
								</tr>
								</thead>
								<tbody id="top_geographies_list">

								</tbody>
								<tfoot>
									<tr class="top_geographies_show_all">
										<td colspan="4" class="text-right">
											<a data-toggle="modal" data-target="#top_geographies_model" role="button">Show All</a>
										</td>
									</tr>
								</tfoot>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="col-md-7">
			<div class="revenue-container" style="margin: 0;">
				<div>
					<h3>Top Customers</h3>
					<div class="col-md-12" style="margin-bottom: 10px;">
						<label>Date Range</label>
						<div id="reportrange" class="sales_revenue_report report-range_2 form-control">
							<i class="glyphicon glyphicon-calendar"></i>&nbsp;
							<span></span> <b class="caret"></b>
							<input type="hidden" class="fromdate_2" id="fromdate" name="fromdate"/>
							<input type="hidden" class="todate_2" id="todate" name="todate"/>
						</div>
					</div>
					<div class="col-md-12">
						<div class="revenue-box scroll-container">
						<table class="table table-bordered">
							<thead style="background-color:#fff">
							<tr id="revenue-table-header">
								<th colspan="3">
								<span class="revenue-header-text">Top 8 Customers</span>
								<span class="revenue-header-value" style="padding-right: 12px;">
									<i class="fa fa-inr fa-black fa-4" style="font-size: 18px;"></i> 
									<span id="sales_top_customers_total"></span>
								</span>
							</th>

							</tr>
							</thead>
							<tbody id="top_customers_list">


							</tbody>
							<tfoot>
								<tr class="top_customer_show_all">
									<td colspan="4" class="text-right">
										<a data-toggle="modal" data-target="#top_customers_model" role="button">Show All</a>
									</td>
								</tr>
							</tfoot>
						</table>
					</div>
					</div>
				</div>


			</div>
		</div>
		<div class="clearfix"></div>
		<div class="col-md-5">
			<h3>Payment Collection</h3>
			<div id="chart_div_2"></div>
		</div>
		<div class="col-md-7">
			<h3>Most Recent Customers</h3>
			<table class="table table-bordered custom-table dashboard_table_red">
				<thead>
				<tr>
					<th>Customer</th>
					<th>Since</th>
					<th>Value</th>
				</tr>
				</thead>
				<tbody id="most_recent_customers">

				</tbody>
			</table>
		</div>
		<div class="clearfix"></div>
		<div class="col-md-12">
			<h3>Items Sold Recently</h3>
			<table class="table table-bordered custom-table dashboard_table_green">
				<thead>
				<tr>
					<th>Material</th>
					<th>Unit</th>
					<th>Quantity</th>
					<th style="width: 120px;">Invoice No.</th>
					<th style="width: 100px;">Issued On Date</th>
					<th style="width: 115px;">OA Code</th>
					<th style="width: 300px;">Customer</th>
				</tr>
				</thead>
				<tbody id="items_sold_recently">

				</tbody>
				<tfoot>
				<tr>
					<td colspan="7" class="text-right"><a href="/erp/sales/material_tax_report/" target="_blank">Goto Report</a></td>
				</tr>
				</tfoot>
			</table>
		</div>
		<input type="hidden" id="salesrevenue" value="{{ response_sales_revenue }}">
	</div>
</div>
<div id="order_due_modal" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Orders due to be supplied</h4>
				<span class="modal_download_link">
					<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#orders_supplied'), 'Orders_Supplied_List.csv']);" data-tooltip="tooltip" title="" data-placement="bottom" data-original-title="Download as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
				</span>
			</div>
			<div class="modal-body">
				<table class="table table-bordered custom-table dashboard_table_green" id="orders_supplied">
					<thead>
					<tr>
						<th>OA Code</th>
						<th>Customer</th>
						<th>Due date</th>
						<th>Value</th>
					</tr>
					</thead>
					<tbody id="orders_supplied_today_modal">

					</tbody>
				</table>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>
<!-- Top geographies model window -->
<div id="top_geographies_model" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Top Geographies</h4>
				<span class="modal_download_link">
					<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#geographies_list'), 'Geographies List.csv']);" data-tooltip="tooltip" title="" data-placement="bottom" data-original-title="Download as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
				</span>
			</div>
			<div class="modal-body">
				<div class="row">
					<div class="col-md-12">
						<div class="revenue-box">
							<table class="table table-bordered" id="geographies_list" style="background: rgba(239, 173, 77,0.15);">
								<thead>
									<tr class="revenue-table-header exclude_export" style="background: #FFF;">
										<th colspan="3">
											<input type="radio" name="sales_region2"  value="city" checked>&nbsp;&nbsp;<span style="display: inline-block; margin-right: 24px;">City</span>
											<input type="radio" name="sales_region2" value="state">&nbsp;&nbsp;State
											<!--<input type="radio" name="sales_region" value="Country">&nbsp;&nbsp;Country-->
										</th>
									</tr>
								</thead>
								<tbody id="full_geographies_list">

								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>
<div id="top_customers_model" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Top Customers</h4>
				<span class="modal_download_link">
					<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#customers_list'), 'Customer List.csv']);" data-tooltip="tooltip" title="" data-placement="bottom" data-original-title="Download as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
				</span>
			</div>
			<div class="modal-body">
				<div class="row">
					<div class="col-md-12">
						<div class="revenue-box">
							<table class="table table-bordered" id="customers_list" style="background: rgba(239, 173, 77,0.15);">
								<thead>
									<tr id="revenue-table-header" style="background: #fff;">
										<th>
											<span class="revenue-header-text">Top Order Customers List</span>
										</th>
										<th colspan="2">
											<span class="revenue-header-value" style="padding-right: 12px;">
												<i class="fa fa-inr fa-black fa-4" style="font-size: 18px;"></i>
												<span id="sales_full_customers_total"></span>
											</span>
										</th>
									</tr>
								</thead>
								<tbody id="full_customers_list">
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>

<div id="oa_order_due_modal" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">OA Overdue</h4>
				<span class="modal_download_link">
					<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#tbl_oa_delivery_overdue'), 'OA_Delivery_Overdue.csv']);" data-tooltip="tooltip" title="" data-placement="bottom" data-original-title="Download as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
				</span>
			</div>
			<div class="modal-body">
				<table class="table table-bordered custom-table dashboard_table_blue" id="tbl_oa_delivery_overdue">
					<thead>
					<tr>
						<th>OA Code</th>
						<th>Customer</th>
						<th>OA Due Date</th>
						<th>Value</th>
					</tr>
					</thead>
					<tbody id="oa_delivery_overdue_modal">
					</tbody>
				</table>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>
<!-- Bill modals and JS are segregated -->
{% include "accounts/bills.html" %}
<script type="text/javascript" src="/site_media/js/loader.js?v={{ current_version }}"></script>
<script>
$(document).ready(function(){
	DateRangeInit_2();
	localStorage.removeItem('receivable_totaldue');
	getTotalDueConstruct(true);
	$('.sales_revenue_report').on('hide.daterangepicker', function(ev, picker) {
		setTimeout(function(){
			getTopCustomers($(".fromdate_2").val(),$(".todate_2").val());
		},100);
	});
	$('.sales_geographies_report').on('hide.daterangepicker', function(ev, picker) {
		setTimeout(function(){
			console.log($(".fromdate").val(),$(".todate").val())
			getTopGeographies($(".fromdate").val(),$(".todate").val(), $("input[name='sales_region']:checked").val());
			getTopGeographies2($(".fromdate").val(),$(".todate").val(), $("input[name='sales_region2']:checked").val());
		},100);
	});

	$("input[name='sales_region']").change(function(){
	    getTopGeographies($(".fromdate").val(),$(".todate").val(), $("input[name='sales_region']:checked").val());
	});
	$("input[name='sales_region2']").change(function(){
	    getTopGeographies2($(".fromdate").val(),$(".todate").val(), $("input[name='sales_region2']:checked").val());
	});
    $('.nav-pills li').removeClass('active');
    $('#li_invoice_dashboard').addClass('active');
    $(".slide_container_part").removeClass('selected');
    $("#menu_sales").addClass('selected');
});

$(window).load(function(){
    $.ajaxSetup({
        headers: {"X-CSRFToken": getCookie('csrftoken')}
    });
    ordersDueToday();
    getOAOverdue();
    recentCustomers();
    recentSoldItems();
	setTimeout(function(){
		getTopCustomers($(".fromdate_2").val(),$(".todate_2").val());
	}, 100);
	setTimeout(function(){
		getTopGeographies($(".fromdate").val(),$(".todate").val(), $("input[name='sales_region']:checked").val());
		getTopGeographies2($(".fromdate").val(),$(".todate").val(), $("input[name='sales_region2']:checked").val());
	}, 100);
});

function getTopCustomers(since, till) {
    $.ajaxSetup({
        headers: {"X-CSRFToken": getCookie('csrftoken')}
    });
    $.ajax({
        url: '/erp/sales/json/invoice/top_customers/',
        type: "POST",
        data: {since: since, till: till},
        success: function (json) {
        console.log(json);
            var top_customers_list_html = full_customers_list_html = '';
            var top_customers_total = full_customers_total = 0;
            document.getElementById("top_customers_list").innerHTML = "";
            if (isEmpty(json['sales_top_customers'])) {
                top_customers_list_html = "<tr><td colspan='3' class='text-center'>No data found</td></tr>";
                $(".top_customer_show_all").addClass("hide");
            } 
            else {
            	if(json['sales_top_customers'].length <= 7) {
            		$(".top_customer_show_all").addClass("hide");
            	}
            	else {
            		$(".top_customer_show_all").removeClass("hide");
            	}
                for (i = 0, len = json['sales_top_customers'].length; i < len; i++) {
	                if(i <= 7){
	                 	top_customers_list_html += '<tr>\
													<td class="text-left">' + json['sales_top_customers'][i].name + '</td>\
													<td class="text-right"><i class="fa fa-inr fa-black fa-4" aria-hidden="true"></i> ' + json['sales_top_customers'][i].closing_balance.toFixed(2) + '</td>\
													<td class="text-center"  >' + ((json['sales_top_customers'][i].closing_balance /json['sales_top_customers_total']) *100).toFixed(2) + '%</td>\
												</tr>';
						top_customers_total += json['sales_top_customers'][i].closing_balance;
					}
					full_customers_list_html += '<tr>\
											<td class="text-left">' + json['sales_top_customers'][i].name + '</td>\
											<td class="text-right"><i class="fa fa-inr fa-black fa-4" aria-hidden="true"></i> ' + json['sales_top_customers'][i].closing_balance.toFixed(2) + '</td>\
											<td class="text-center"  >' + ((json['sales_top_customers'][i].closing_balance /json['sales_top_customers_total']) *100).toFixed(2) + '%</td>\
										</tr>';
					full_customers_total += json['sales_top_customers'][i].closing_balance;
				}
            }
            document.getElementById("top_customers_list").innerHTML = top_customers_list_html;
            document.getElementById("full_customers_list").innerHTML = full_customers_list_html;
            document.getElementById("sales_top_customers_total").innerHTML = top_customers_total.toFixed(2);
            document.getElementById("sales_full_customers_total").innerHTML = full_customers_total;
            if($("#top_customers_list").closest("table").height() >= 400){
                 $('.scroll-container').slimScroll({
                    height: "400px",
                    alwaysVisible: true
                });
            }
        },
        error: function (xhr, errmsg, err) {
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });
}


function getTopGeographies(since, till, region) {
    $.ajaxSetup({
        headers: {"X-CSRFToken": getCookie('csrftoken')}
    });
    $.ajax({
        url: '/erp/sales/json/invoice/top_geographies/',
        type: "POST",
        data: {since: since, till: till, type:region},
        success: function (json) {
            var top_geographies_list_html  = '<tr> \
                                            <th class="text-center" style="width: 300px;">REGION</th> \
                                            <th class="text-center" style="width: 150px;">TOTAL SALE</th> \
                                        </tr>';
            document.getElementById("top_geographies_list").innerHTML = "";
            if (json.length <= 0) {
                top_geographies_list_html = "<tr><td colspan='3' class='text-center'>No data Found</td></tr>";
                $(".top_geographies_show_all").addClass("hide");
            } 
            else {
            	if(json.length <= 9) {
            		$(".top_geographies_show_all").addClass("hide");
            	}
            	else {
            		$(".top_geographies_show_all").removeClass("hide");
            	}
                for (i = 0, len = json.length; i < len; i++) {
                	if(i <= 9){
                 		top_geographies_list_html += '<tr>\
												<td class="text-left">' + json[i].region + '</td>\
												<td class="text-right" style="padding-right: 14px;"><i class="fa fa-inr fa-black fa-4" aria-hidden="true"></i> ' + json[i].total.toFixed(2) + '</td>\
											</tr>';
					}
				}

            }
            document.getElementById("top_geographies_list").innerHTML = top_geographies_list_html;
            if($("#top_geographies_list").closest("table").height() >= 400){
                 $('.scroll-container').slimScroll({
                    height: "400px",
                    alwaysVisible: true
                });
            }
        },
        error: function (xhr, errmsg, err) {
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });
}

function getTopGeographies2(since, till, region) {
    $.ajaxSetup({
        headers: {"X-CSRFToken": getCookie('csrftoken')}
    });
    $.ajax({
        url: '/erp/sales/json/invoice/top_geographies/',
        type: "POST",
        data: {since: since, till: till, type:region},
        success: function (json) {

            var full_geographies_list_html = '<tr> \
                                            <th class="text-center" style="width: 300px;">REGION</th> \
                                            <th class="text-center" style="width: 150px;">TOTAL SALE</th> \
                                        </tr>';
            if (json.length <= 0) {
                full_geographies_list_html = "<tr><td colspan='3' class='text-center'>No data Found</td></tr>";
            }
            else {
            	if(json.length <= 9) {
            		$(".top_geographies_show_all").addClass("hide");
            	}
            	else {
            		$(".top_geographies_show_all").removeClass("hide");
            	}

				for (i = 0, len = json.length; i < len; i++) {
                 	full_geographies_list_html += '<tr>\
												<td class="text-left">' + json[i].region + '</td>\
												<td class="text-right"><i class="fa fa-inr fa-black fa-4" aria-hidden="true"></i> ' + json[i].total.toFixed(2) + '</td>\
											</tr>';
				}
            }

            document.getElementById("full_geographies_list").innerHTML = full_geographies_list_html;
        },
        error: function (xhr, errmsg, err) {
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });
}

function ordersDueToday(){
    $.ajaxSetup({
        headers: {"X-CSRFToken": getCookie('csrftoken')}
    });
	$.ajax({
        url: '/erp/sales/json/invoice/get_ordersdue_today/',
        type: "POST",
        data: {},
        beforeSend: function () {
            $("#loadingmessage_ie").show();
        },
        success: function (response) {
            oa_supplied_html = '';
            oa_supplied_full_html = '';
            var todaysDate = new Date();
            var today = moment(todaysDate).format('YYYY-MM-DD');
            if(response.length <= 7) {
            	$(".ontime_supplier_show_all").addClass("hide");
            }
            else {
            	$(".ontime_supplier_show_all").removeClass("hide");
            }
            if(response.length > 0){
                $.each( response, function( key, value ) {
	                if(today == moment(Date.parse(value['due_date'])).format('YYYY-MM-DD')){
		                oa_supplied_html += '<tr> \
	                                 	<td class="text-center"> \
		                                   <form id="oa_edit" method="post" action="/erp/sales/oa/editOA/"> \
		                                   <div style="display:none"><input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}" /></div> \
			                               <a role="button" onclick="javascript:clickButton(\'editOA_' + value['oa_id'] +'\');">'+ value['oa_code'] +'</a> \
			                               <input type="hidden" value="'+ value['oa_id'] +'" id="id_oa_no" name="oa_no"> \
			                               	<input type="submit" value="Edit" id="editOA_'+ value['oa_id'] +'" hidden="hidden"> \
											</form></td> \
			                                <td>'+ value['party'] +'</td> \
			                                <td class="text-right">'+ value['oa_value'].toFixed(2) +'</td> \
			                            </tr>';
	             	}
	             	oa_supplied_full_html += '<tr> \
	                             	<td class="text-center"> \
	                                   <form id="oa_edit" method="post" action="/erp/sales/oa/editOA/"> \
	                                   <div style="display:none"><input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}" /></div> \
		                               <a role="button" onclick="javascript:clickButton(\'editOA_' + value['oa_id'] +'\');">'+ value['oa_code'] +'</a> \
		                               <input type="hidden" value="'+ value['oa_id'] +'" id="id_oa_no" name="oa_no"> \
		                               	<input type="submit" value="Edit" id="editOA_'+ value['oa_id'] +'" hidden="hidden"> \
										</form></td> \
		                                <td>'+ value['party'] +'</td> \
		                                <td>'+ value['due_date'] +'</td> \
		                                <td class="text-right">'+ value['oa_value'].toFixed(2) +'</td> \
		                            </tr>';
				});
			}
			else {
				oa_supplied_html = '<tr> \
	                               <td class="text-center"></a></td> \
	                                <td>No due available for Today.</td> \
	                                <td class="text-right"></td> \
	                            </tr>';
			}
            $("#orders_supplied_today").html(oa_supplied_html);
            $("#orders_supplied_today_modal").html(oa_supplied_full_html);
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });

}

function recentCustomers(){
    $.ajaxSetup({
        headers: {"X-CSRFToken": getCookie('csrftoken')}
    });
	$.ajax({
        url: '/erp/sales/json/invoice/get_recent_customers/',
        type: "POST",
        data: {},
        beforeSend: function () {
            $("#loadingmessage_ie").show();
        },
        success: function (response) {
            recent_customers_html = '';
            if(response.length > 0){
	            $.each( response, function( key, value ) {
	            	recent_customers_html += '<tr> \
	                                <td>'+ value['party'] +'</td> \
	                               <td class="text-center">'+ value['since']  +'</td> \
	                                <td class="text-right">'+ value['value'].toFixed(2) +'</td> \
	                            </tr>';
				});
			}
			else {
				recent_customers_html = '<tr> \
                               <td class="text-center"></a></td> \
                                <td>No Recent customers available.</td> \
                                <td class="text-right"></td> \
                            </tr>';
			}
            $("#most_recent_customers").html(recent_customers_html);
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });

}

function recentSoldItems(){
    $.ajaxSetup({
        headers: {"X-CSRFToken": getCookie('csrftoken')}
    });
	$.ajax({
        url: '/erp/sales/json/invoice/get_recent_sold_items/',
        type: "POST",
        data: {},
        beforeSend: function () {
            $("#loadingmessage_ie").show();
        },
        success: function (response) {
            recent_items_html = '';
            if(response.length > 0){
            $.each( response, function( key, value ) {
            recent_items_html += '<tr> \
                    <td>' + value['material']  + '</td> \
                    <td class="text-right">'+ value['unit'] +'</td> \
                    <td class="text-right">'+ value['qty'] +'</td> \
                    <td class="text-center">'+ value['invoice_no'] +'</td> \
                    <td class="text-center">' + value['issued_on'] + '</td> \
                    <td class="text-center">' + value['oa_code'] + '</td> \
                    <td>' + value['party'] + '</td> \
                </tr>';
			});
			}else{
			recent_items_html = '<tr> \
                               <td class="text-center"></a></td> \
                                <td>No Recent sold items available.</td> \
                                <td class="text-right"></td> \
                            </tr>';
			}
            $("#items_sold_recently").html(recent_items_html);
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });

}

function paymentCollection(){
	var result = {};
	$.ajaxSetup({
        headers: {"X-CSRFToken": getCookie('csrftoken')}
    });
	$.ajax({
        url: '/erp/sales/json/invoice/get_payment_collection/',
        type: "POST",
        data: {},
        beforeSend: function () {
            $("#loadingmessage_ie").show();
        },
        success: function (response) {
            drawVisualization(response);
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });
}

function getOAOverdue(){
	$.ajaxSetup({
        headers: {"X-CSRFToken": getCookie('csrftoken')}
    });
    $.ajax({
        url: '/erp/sales/json/invoice/get_oa_overdue/',
        type: "POST",
        data: {},
        beforeSend: function () {
            $("#loadingmessage_ie").show();
        },
        success: function (response) {
            oa_overdue_html = '';
            oa_overdue_full_html = '';
            if(response.length <= 10) {
            	$(".oa_delivery_show_all").addClass("hide");
            }
            else {
            	$(".oa_delivery_show_all").removeClass("hide");
            }
            if(response.length > 0){
                $.each( response, function( key, value ) {
	                if(key < 10){
	                	oa_overdue_html += '<tr> \
	                                 <td class="text-center"> \
	                                   <form id="oa_edit" method="post" action="/erp/sales/oa/editOA/"> \
	                                   <div style="display:none"><input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}" /></div> \
		                               <a role="button" onclick="javascript:clickButton(\'editOA_' + value['oa_id'] +'\');">'+ value['oa_code'] +'</a> \
		                               <input type="hidden" value="'+ value['oa_id'] +'" id="id_oa_no" name="oa_no"> \
		                               	<input type="submit" value="Edit" id="editOA_'+ value['oa_id'] +'" hidden="hidden"> \
										</form></td> \
		                                <td>'+ value['party'] +'</td> \
		                                <td class="text-center">'+ value['due_date'] +'</td> \
		                                <td class="text-right">'+ value['oa_value'].toFixed(2) +'</td> \
		                            </tr>';
	              	}
	              	oa_overdue_full_html += '<tr> \
                                 	<td class="text-center"> \
	                                   <form id="oa_edit" method="post" action="/erp/sales/oa/editOA/"> \
	                                   <div style="display:none"><input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}" /></div> \
		                               <a role="button" onclick="javascript:clickButton(\'editOA_' + value['oa_id'] +'\');">'+ value['oa_code'] +'</a> \
		                               <input type="hidden" value="'+ value['oa_id'] +'" id="id_oa_no" name="oa_no"> \
		                               	<input type="submit" value="Edit" id="editOA_'+ value['oa_id'] +'" hidden="hidden"> \
										</form></td> \
		                                <td>'+ value['party'] +'</td> \
		                                <td class="text-center">'+ value['due_date'] +'</td> \
		                                <td class="text-right">'+ value['oa_value'].toFixed(2) +'</td> \
		                            </tr>';

				});
			}
			else {
				oa_overdue_html = '<tr> \
                               <td class="text-center"></a></td> \
                                <td>No overdue available.</td> \
                                <td class="text-right"></td> \
                            </tr>';
			}
            $("#oa_delivery_overdue").html(oa_overdue_html);
            $("#oa_delivery_overdue_count").html(response.length);
            $("#oa_delivery_overdue_modal").html(oa_overdue_full_html);
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });
}

google.charts.load('current', {'packages': ['corechart']});
google.charts.setOnLoadCallback(DrawSalesRevenue);
function DrawSalesRevenue() {
    var json = JSON.parse(document.getElementById('salesrevenue').value);
    var data = google.visualization.arrayToDataTable(json);

    var options = {
        seriesType: 'bars',
        legend: {position: 'top'},
        series: {1: {type: 'line'}},
        width: '100%',
        height: 280,
        'chartArea': {'width': '80%', 'height': '80%'},
        colors: ['#efad4d', 'green'],
        vAxis: {title: "Rs in lakhs"}, isStacked: true
    };

    var chart = new google.visualization.ComboChart(document.getElementById('chart_div_1'));
    chart.draw(data, options);


}

google.charts.load("visualization", "1", {packages:["corechart"]});
google.charts.setOnLoadCallback(paymentCollection);
function drawVisualization(salesData) {
      // Create and populate the data table.
      var data = google.visualization.arrayToDataTable(salesData['sales_performance']);
      // Create and draw the visualization.
      new google.visualization.ColumnChart(document.getElementById('supplier_chart')).
          draw(data,
               {title:"",
                width: '100%',
                height: 400,
                'chartArea': {'width': '80%', 'height': '75%'},
                legend: {position: 'top'},
                vAxis: {title: "Rs in lakhs"}, isStacked: true,
                hAxis: {title: "months"}}
          );
      var data_pc = google.visualization.arrayToDataTable(salesData['payment_collection']);
      new google.visualization.ColumnChart(document.getElementById('chart_div_2')).
      draw(data_pc,
           {title:"",
            width: '100%',
            height: 400,
            'chartArea': {'width': '80%', 'height': '75%'},
            legend: {position: 'top'},
            vAxis: {title: "Rs in lakhs"}, isStacked: true,
            hAxis: {title: "months"}}
      );
}

$("#receivable_advance").click(function(){
    if ($("#receivable_advance_particulars").val().length > 0){
        populateAllUnsettledParticulars(JSON.parse($("#receivable_advance_particulars").val()), "Advance Received");
    }else{
        swal("", "No Records Found!", "warning");
    }
});
$("#receivable_billable").click(function(){
    if ($("#receivable_advance_particulars").val().length > 0){
		populateAllUnsettledParticulars(JSON.parse($("#receivable_billable_particulars").val()), "Unbilled Outstanding");
    }else{
        swal("", "No Records Found!", "warning");
    }
});

$(window).load(function(){
    $("#loading").hide();
})        

</script>
{% endblock %}