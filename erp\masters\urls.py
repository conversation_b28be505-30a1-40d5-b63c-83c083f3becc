"""
"""
from django.conf.urls import patterns, url

from erp.masters import json_api
from erp.masters.material_views import manageCatalogues, editCatalogue, saveCatalogue, deleteCatalogue, \
	saveMaterialCategory, populateMaterialChoices, populateCategoryChoices, importMaterials, importBomMaterials, \
	deleteMaterial, checkDrawingNumber, checkStockQty, populateMaterials, saveMaterialUnit,\
	checkMaterialUnit, populateUnitChoices, checkAlternateUnit, getSupplierPrice, manageService, maskSupplierPrice
from erp.masters.party_views import manageParties, updateParty, deleteParty, importParties, loadPartyDetails, \
	saveParty, loadPartyOptions, loadPartyContacts
from erp.masters.tax_views import manageTax, saveTax, editTax, deleteTax, checkTaxCode
from erp.masters.views import manageMasters, populateProjectSelectChoices, saveProject, constructUnitChoices, \
	getCheapestSupplierBOM, getBomParenId, getMaterialLogList, getMaterialLogData, createNewProject,\
	get_material_catalogue, create_location, fetch_location, create_location_page, location_listing_page, \
	get_countries_list, get_state_list, bom_checking_from_mrp

__author__ = 'kalaivanan'

urlpatterns = patterns(
	'',
	url('home/$', manageMasters),
	url('materials/delete/$', deleteMaterial),
	url("json/populate_projects/$", populateProjectSelectChoices),
	url("json/populate_units/$", constructUnitChoices),
	url("json/materials/populate_choices/$", populateMaterialChoices),
	url('json/materials/import/$', importMaterials),
	url('json/materials/import_bill_of_materials/$', importBomMaterials),
	url('json/materials/categorysave/$', saveMaterialCategory),
	url('json/materials/checkdrawingno/$', checkDrawingNumber),
	url('json/materials/check_alternate_unit/$', checkAlternateUnit),
	url('json/materials/populate_categories/$', populateCategoryChoices),
	url('json/material/list/$', json_api.listMaterial),
	url('json/materialNames/$', json_api.listMaterialNames),
	url('json/material/detail/$', json_api.getMaterialDetail),
	url('json/material/delete/$', json_api.deleteMaterial),
	url('json/material/save/$', json_api.saveMaterial),
	url('json/material/supplierPrices/$', json_api.getSupplierPrices),
	url('json/material/approveRate/$', json_api.approveRate),
	url('json/material/rejectRate/$', json_api.rejectRate),
	url('json/material/getCurrentDate/$', json_api.getCurrentDate),
	url('json/materials/checkstock/$', checkStockQty),
	url('json/materials/populate_materials/$', populateMaterials),
	url('json/material/material_attachment_upload/$', json_api.uploadAttachmentMaterial),
	url('json/material/material_attachment_list/$', json_api.getAllAttachmentMaterial),
	url('json/material/delete_material_attachment/$', json_api.deleteAttachmentMaterial),
	url('json/materials/checkunit/$', checkMaterialUnit),
	url('json/materials/saveunit/$', saveMaterialUnit),
	url('json/materials/populate_unit_choices/$', populateUnitChoices),
	url('json/materials/get_cheapest_supplier_bom/$', getCheapestSupplierBOM),
	url('json/materials/get_bom_parentid/$', getBomParenId),
	url('json/materials/get_supplier_price/$', getSupplierPrice),
	url('json/materials/mask_supplier_price/$', maskSupplierPrice),

	url('json/materials/get_specifications/$', json_api.getMaterialSpecifications),

	url('material/getloglist/$', getMaterialLogList),
	url('material/getlogdata/$', getMaterialLogData),

	url('catalogues/$', manageCatalogues),
	url('catalogues/#table_tab1$', manageCatalogues),
	url('catalogues/save/$', saveCatalogue),
	url('catalogues/edit/$', editCatalogue),
	url('catalogues/delete/$', deleteCatalogue),
	url('json/projects/save/$', saveProject),
	url('json/projects/add/$', createNewProject),
	url('services/$', manageService),
	url('services/#table_tab1$', manageService),

	url('party_list/$', manageParties),
	url('party/$', loadPartyDetails),
	url('json/web/party/delete/$', deleteParty),
	url('json/party/update/$', updateParty),
	url('json/web/party/save/$', saveParty),
	url('json/party/import/$', importParties),
	url('json/party/list/$', json_api.listParty),
	url('json/partyNames/$', json_api.listPartyNames),
	url('json/party/detail/$', json_api.getPartyDetail),
	url('fetch_party_contacts/$', loadPartyContacts),
	url('json/party/last_added/$', json_api.getLastAddedParty),
	url('json/party/delete/$', json_api.deleteParty),
	url('json/party/save/$', json_api.saveParty),
	url('json/party/populate_party_lists/$', loadPartyOptions),

	url('tax/$', manageTax),
	url('tax/save/$', saveTax),
	url('tax/edit/$', editTax),
	url('tax/delete/$', deleteTax),
	url('json/tax_code_exists/$', checkTaxCode),
	url('json/tax/list/$', json_api.listTaxes),
	url('json/tax/delete/$', json_api.deleteTax),
	url('json/tax/save/$', json_api.saveTax),

	url('json/employee/list/$', json_api.listEmployee),
	url('json/employee/delete/$', json_api.deleteEmployee),
	url('json/employee/save/$', json_api.saveEmployee),

	url('json/projects/$', json_api.listProjects),
	url('json/material_list/$', json_api.loadMaterials),
	url('json/material_frequent_list/$', json_api.loadFrequentMaterials),
	url('json/loadTaxList/$', json_api.loadTax),
	url('json/fetch_frequently_used_partyAndProjects/$', json_api.fetchFrequentlyUsedPartyAndProjects),
	url('json/extract_catalogue/$', get_material_catalogue),

	url('json/location_json/$', create_location),
	url('location/$', create_location_page),
	url('json/location_fetch/$', fetch_location),
	url('location_list/$', location_listing_page),
	url('json/countries/$', get_countries_list),
	url('json/states/$', get_state_list),
	url("bom_checking_from_mrp/$", bom_checking_from_mrp),
)
