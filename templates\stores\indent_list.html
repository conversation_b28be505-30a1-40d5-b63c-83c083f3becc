{% extends "stores/sidebar.html" %}
{% block indent %}

{% if logged_in_user|canView:'PURCHASE INDENT' %}
<style>

	li.indent_side_menu a{
	outline: none;
	background-color: #e6983c !important;
	}

	#id_indent-instructions {
		height: 100px;
	}

	#cattable tr[data-toggle='open'] td:nth-child(8){
		opacity: 0.5;
		background: #ddd;
		pointer-events: none;
	}

	#cattable tr[data-toggle='open'] input,
	#cattable tr[data-toggle='open'] select	{
		opacity: 0.5;
		background: #ddd;
	}

	.side-content.div-disabled {
		padding: 0;
		background-color: transparent !important;
	}

	.side-content.div-disabled a,
	.side-content.div-disabled div,
	.side-content.div-disabled input {
		border: none;
		outline: none;
		box-shadow: 0 0;
		height: 30px;
		padding-left: 3px;
		background-color: transparent !important;
		font-size: 13px;
		color: #333;
	}

	.side-content.div-disabled input {
		padding-left: 6px;
	}

	.side-content.div-disabled .chosen-single div {
		display: none;
	}

	.side-content.div-disabled .chosen-single span {
		margin-top: 2px;
	}

	.table.text_box_in_table .chosen-single {
		height: 26px;
	    font-size: 13px;
	    line-height: 25px;
	    padding: 0 0 0 3px;
	}

	.table.text_box_in_table .chosen-container-single .chosen-single div b {
		margin-top: 2px;
	}

	.supplier_total_amt {
		font-size: 20px;
		float: right;
		margin-top: 15px;
	}

	.parent-supplier-container {
	    padding: 6px 12px;
	    border: solid 1px #ccc;
	    border-top: none;
	    margin-bottom: 15px;
	}

	.supplier_name_tab a{
		color:  #000;
		font-size: 16px;
	}

	.chosen-select[readonly='readonly']+div  {
		pointer-events: none;
	}

	.supplier_type_select {
		width:  100%;
		border-radius: 4px;
    	border-color: #ccc;
	}

	.disabled_material td {
		background: rgba(255,0,0,0.15);
		opacity: 0.5;
		pointer-events: none;
		color:  rgba(255,0,0,0.7) !important;
	}

	.disabled_material input {
		background: transparent !important;
		border-color: rgba(255,0,0,0.3);
    	color:  rgba(255,0,0,0.7) !important;
	}

	.disabled_material td:last-child {
		background: transparent;
		opacity: 1;
		pointer-events: inherit;
		color: #000 !important;
	}

	.td_for_icon .fa.fa-ban,
	.td_for_icon .fa.fa-plus {
		font-size: 18px;
	    margin-top: 3px;
	}

	.side-content .custom-error-message {
		margin-top: -24px;
    	right: 26px;
	}

	.indent_add .fixedHeader-floating {
		display: none;
	}

	.error-duplicate-supplier {
		background: rgba(249, 255, 81,0.35);
	}

	.error-duplicate-supplier .chosen-container {
		box-shadow: 0 0;
	}

	.error-duplicate-supplier input,
	.error-duplicate-supplier select,
	.error-duplicate-supplier a span{
		background: rgba(221, 75, 57,0.00);
	}

	.add_split_container .chosen-disabled {
		opacity: 1 !important;
	    box-shadow: 0 0;
	}

	.add_split_container .chosen-disabled a {
		border:  none;
	}

	.add_split_container .chosen-disabled b {
		display: none !important;
	}

	.add_split_container select[disabled] {
		-webkit-appearance: none;
	    -moz-appearance: none;
	    text-indent: 1px;
	    text-overflow: '';
	    background: transparent;
	}

	.loading-main-container {
		display: block;
	}
	.table-inline-icon-container-list {
		position: absolute;
		margin-top:  -8px;
		float: left;
		display: block
	}

</style>

<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/utility.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/indent_list.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/tags.js?v={{ current_version }}"></script>

{% if module_access.indent %}
<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">Purchase Indent</span>
	</div>
	<div class="page-heading_new" style="padding: 0 30px;">
		<input type="hidden" class="indent_module_access" id="indent_module_access" name="indent_module_access" value="{{ module_access.indent }}" />
		<span class="page_header_indent"></span>
		{% if module_access.indent %}
			{% if logged_in_user|canEdit:'PURCHASE INDENT' %}
				<a href="{{edit_link}}" role="button" class="btn btn-new-item pull-right create_indent" data-tooltip="tooltip" title="Create New Indent" style="position: absolute; right: 85px; margin-top: 10px; z-index: 10;"><i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span></a>
			{% else %}
				<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in Indent Module. Please contact the administrator." style="position: absolute; right: 85px; margin-top: 5px; z-index: 10;">
					<a class="btn btn-new-item pull-right create_indent disabled">
						<i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span>
					</a>
				</div>
			{% endif %}
		<a role="button" href="/erp/stores/indent_list/" class="btn btn-add-new pull-right view_indent hide" data-tooltip="tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
		{% else %}
		Indent module is not available for your Enterprise. To avail this functionality, enable this section in Configurations
		{% endif %}
		<span class="prev_next_container"></span>
	</div>
	<div class="container-fluid" id="container-fluid">
		<div>
			<div class="col-lg-12">
				<div class="content_bg">
					<div class="tab-content">
						<div>
							<div class="row">
								<div class="filter-components">
									<div class="filter-components-container">
										<div class="dropdown">
											<button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
												<i class="fa fa-filter"></i>
											</button>
											<span class="dropdown-menu arrow_box arrow_box_filter">
												<form action="/erp/stores/indent_list/loadindents/" method="post">{% csrf_token %}
													<div class="col-sm-12 form-group" >
														<label>Date Range</label>
														<div id="reportrange" class="report-range form-control">
															<i class="glyphicon glyphicon-calendar"></i>&nbsp;
															<span></span> <b class="caret"></b>
															<input type="hidden" class="fromdate" id="fromdate" name="fromdate" value="{{filter_from|date:'Y-m-d'}}" />
															<input type="hidden" class="todate" id="todate" name="todate" value="{{filter_to|date:'Y-m-d'}}"/>
														</div>
													</div>
													<div class="col-sm-12 form-group">
														<label>Project/Tag</label>
														<select class="form-control chosen-select" name="projects" id="projectmain">
															{% for project in projects %}
																{% if project.0 == filter_projects %}
																<option value="{{ project.0 }}" selected="selected">{{ project.1 }}</option>
																{% else %}
																<option value="{{ project.0 }}" {% if search_project == project.0 %} selected {% endif %}>{{ project.1 }}</option>
																{% endif  %}
															{% endfor %}
														</select>
													</div>
													<div class="filter-footer">
														<input type="submit" class="btn btn-save search_button" value="Apply" id="id-searchIndent"/>
													</div>
												</form>
											</span>
										</div>
										<span class='filtered-condition filtered-date'>Date: <b></b></span>
										<span class='filtered-condition filtered-project'>Project/Tag: <b></b></span>
									</div>
								</div>
								<div class="col-sm-12">
									{% if module_access.indent %}
										<div class="csv_export_button">
											<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#indentList'), 'Indent_List.csv']);" data-tooltip="tooltip" title="Download Indent List as CSV">
												<i class="fa fa-download" aria-hidden="true"></i>
											</a>
										</div>
									{% endif %}
									<table class="table table-bordered custom-table table-striped" id="indentList" style="width: 100%;">
										<thead>
											<tr>
												<th style="min-width: 70px; max-width: 70px;"> S.No</th>
												<th width="10%"> Indent No</th>
												<th width="8%"> Project</th>
												<th width="10%"> Date</th>
												<th width="15%"> Type</th>
												<th width="15%"> Purpose</th>
												<th width="10%"> Indent Value</th>
												<th width="8%"> Status</th>
												<th width="8%"> Basic PO Value</th>
												<th width="8%"> PO Value</th>
											</tr>
										</thead>
										<tbody id="issue_table_list_tbody">
											{% for indent in indents %}
											<tr bgcolor="#ececec" align="center">
												<td align="center">
													<span class="table-inline-icon-bg">{{ forloop.counter }}.</span>
													<span class='table-inline-icon-container-left'>
														<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='right' title='Edit' role='button' onclick="editIndentRow('{{ indent.indent_no }}', '{{edit_link}}');">
															 <i class='fa fa-pencil'></i>
														</span>
														<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='right' title='Edit in New Tab' role='button' onclick="editIndentRow('{{ indent.indent_no }}', '{{edit_link}}', '_blank');">
															 <i class='fa fa-external-link'></i>
														</span>
														{% if access_level.delete %}
															<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='right' title='Delete'   onclick="javascript:deleteIndent('{{ indent.indent_no }}');">
																<i class="fa fa-trash-o"></i>
															</span>
														{% endif  %}
													</span>
												</td>
												<td class='text-center'>
													<a class="edit_link_code" data-indentId="{{ indent.indent_no }}" onclick="editIndentRow('{{ indent.indent_no }}', '{{edit_link}}');">
														{{ indent.code }}
													</a>
												</td>
												<td class='text-left'> {{ indent.project }} </td>
												<td class='text-center'> {{ indent.raised_date|date:"M d, Y H:i:s" }} </td>
												<td class='text-left'> {{ indent.type }} </td>
												<td class='text-left' style='max-width: 100px; word-wrap: break-word;'> {{ indent.purpose }} </td>
												<td class='text-right'>{{ indent.indent_value }} </td>
												<td class='text-left'> {{ indent.status }} </td>
												<td class='text-right'> {% if indent.basic_po_value %} {{ indent.basic_po_value }} {% else %} 0 {% endif %} </td>
												<td class='text-right'> {% if indent.po_value %} {{ indent.po_value }} {% else %} 0 {% endif %} </td>
											</tr>
											{% endfor %}
										</tbody>
									</table>
								</div>
							</div>
							<div class="clearfix"></div>
						</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="hide">
	<form id="edit_indent_form" action="" method="post" >
		{% csrf_token %}
		<input type="hidden" id="edit_indent_id" name="indent_no" value="" />
	</form>
	<form id="delete_indent_form" action="/erp/stores/indent_list/delete/" method="post" >
		{% csrf_token %}
		<input type="hidden" id="delete_indent_id" name="indent_no" value="" />
	</form>
</div>
{% include "masters/add_material_modal.html" %}
{% else %}
	<div class="right-content-container">
		<h3 class="text-center">Indent module is not available for your Enterprise. To avail this functionality, enable this section in Configurations.</h3>
	</div>
{% endif %}

<script type="text/javascript">
$(window).load(function(){
	updateFilterText();
	$("#loading").hide();
});

function updateFilterText() {
	$(".filtered-date b").text($("#reportrange").find("span").text());
	$(".filtered-project b").text($("#projectmain option:selected").text());
}

function editIndentRow(indentId, indentUrl, openTarget="") {
	$("#edit_indent_id").val(indentId);
	$("#edit_indent_form").attr("action", indentUrl);
	$("#edit_indent_form").attr("target", openTarget).submit();
}

</script>
{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
{% endblock %}

