import os
import pymysql
from logging import handlers
import logging

__author__ = 'saravanan'

# Logs
PATH = os.path.dirname(os.path.realpath(__file__))
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log_path = "/var/log/django/insert_default_location.log"
logHandler = handlers.TimedRotatingFileHandler(log_path, when='midnight', interval=1, backupCount=100)
logHandler.setFormatter(formatter)
logger = logging.getLogger(os.path.splitext(os.path.basename(__file__))[0])
# Setting the threshold of logger to DEBUG
logger.setLevel(logging.DEBUG)
logger.addHandler(logHandler)

DB_PORT = 25394
DB_HOST = "localhost"
DB_USER = "xdevops"
DB_PASSWORD = "D3v$EnIq!^3"
DB_NAME = "migutharavu"


def executeQuery(query, as_dict=False, query_data=None, conversions=None):
    """
    Executes a query & returns a result-set, as a list of either indexed tuples or dicts

    :param query:
    :param as_dict: Flag to specify the type of result-set, if True return a list of dicts, else a list of tuples
    :param query_data: Query parameters
    :param conversions
    :return:
    """
    if conversions is None:
        conversions = pymysql.converters.conversions.copy()
    db_connection = pymysql.connect(host=DB_HOST, user=DB_USER, passwd=DB_PASSWORD, db=DB_NAME, port=DB_PORT,
                                    conv=conversions, charset="utf8mb4", binary_prefix=True)
    if as_dict:
        db_connection.cursorclass = pymysql.cursors.DictCursor
    cursor = db_connection.cursor()
    try:
        cursor.execute(query, query_data)
        db_connection.commit()
        return cursor

    except Exception as e:
        logger.info("Query cannot be executed %s" % e)
        db_connection.rollback()
    finally:
        db_connection.close()


def insert_location(location_data=None):
    """"
    The function is used to create the default location in the location master table
    """
    try:
        logger.info(location_data)
        location_insert_query = """INSERT INTO location_master(
                    enterprise_id,
                    name,
                    contact_person,
                    phone_no,
                    email,
                    city,
                    state,
                    country,
                    pin_code,
                    fax,
                    gst_label,
                    address1,
                    address2,
                    code,
                    last_modified_on,
                    created_on,
                    last_modified_by,
                    is_default
                )VALUES ('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s',
                '%s', '%s' );""" % location_data
        cursor = executeQuery(query=location_insert_query)

        location_id = cursor.lastrowid
        print(location_id, location_data[0])

        grn_update_query = """UPDATE `grn_material` SET `location_id`=%s WHERE enterprise_id=%s;""" % (
            location_id, location_data[0])
        executeQuery(query=grn_update_query)
        grn_material_update_query = """UPDATE `grn` SET `location_id`=%s WHERE enterprise_id=%s;""" % (
            location_id, location_data[0])
        executeQuery(query=grn_material_update_query)
        invoice_update_query = """UPDATE `invoice` SET `location_id`=%s WHERE enterprise_id=%s;""" % (
            location_id, location_data[0])
        executeQuery(query=invoice_update_query)
        invoice_material_update_query = """UPDATE `invoice_materials` SET `location_id`=%s WHERE enterprise_id=%s;""" % (
            location_id, location_data[0])
        executeQuery(query=invoice_material_update_query)

    except Exception as e:
        print(e)
        logger.info("failed to insert location in location master table %s:%s" % (e, location_data))


def main():
    location_list = [
        ('102', 'Schenll Energy Equipments Pvt Ltd-Unit-I', '', '0422 269 2221', '<EMAIL>',
         'Coimbatore', '33,TAMIL NADU', 'IN, India', '641020', '', '',
         'Building No. 6, 4, Lions Club Rd, Ranganayaki Nagar, Dhamu Nagar, Periyanaickenpalayam, Tamil Nadu 641020',
         '', 'SCHUnit-I', '2024-09-19 10:00:57', '2024-09-19 09:51:11', '1', '1'),
        ('107', 'Schenll Energy Equipments Pvt Ltd-Unit-II', '', '1254454654', '<EMAIL>',
         'Coimbatore', '33,TAMIL NADU', 'IN, India', '641020', '', '',
         '169 RAILWAY STATION ROAD, PERIYANAICKEN PALAYAM',
         '', 'SCHUnit-II', '2024-09-20 10:00:57', '2024-09-20 09:51:11', '1', '1'),
        ('791', 'Schenll Energy Equipments Pvt Ltd-Service', '', '9042090658', '<EMAIL>',
         'Coimbatore', '33,TAMIL NADU', 'IN, India', '641020', '', '',
         '169 RAILWAY STATION ROAD, PERIYANAICKEN PALAYAM',
         '', 'SCHUnit-Service', '2024-09-20 10:00:57', '2024-09-20 09:51:11', '1', '1'),
        ('973', 'SCHNELL ENERGY EQUIPMENTS PVT LTD-CBE', '', '8838330031', '<EMAIL>',
         'Coimbatore', '33,TAMIL NADU', 'IN, India', '641006', '', '',
         'KK Nagar,Police Quarters Main Road, Ganapathy,Coimbatore',
         '', 'SCHUnit-CBE', '2024-09-20 10:00:57', '2024-09-20 09:51:11', '1', '1'),
        ('976', 'SCHNELL ENERGY EQUIPMENTS PVT LTD-ERODE', '', '8838330031', '<EMAIL>',
         'ERODE', '33,TAMIL NADU', 'IN, India', '638011', '', '',
         'DOOR NO.H 13, NASIYAYANUR MANIKAMPALAYAM, ERODE, Tamil Nadu, 638011',
         '', 'SCHUnit-ERODE', '2024-09-20 10:00:57', '2024-09-20 09:51:11', '1', '1'),
        ('977', 'SCHNELL ENERGY EQUIPMENTS PVT LTD-TIRUPPUR', '', '8838330031', '<EMAIL>',
         'Tiruppur', '33,TAMIL NADU', 'IN, India', '641607', '', '',
         'D No 48, TPA Colony, Renganadhapuram, Kongu Nagar, Tiruppur-641607',
         '', 'SCHUnit-TIRUPUR', '2024-09-20 10:00:57', '2024-09-20 09:51:11', '1', '1'),
        ('992', 'Schnell Energy Equipments (P) Ltd-CHENNAI', '', '8838330031',
         '<EMAIL>',
         'Chennai', '33,TAMIL NADU', 'IN, India', '600075', '', '',
         'No 149/2,Vishveshapuram,Near Bypass,Pammal Kamarajapuram Road,Chengalpattu',
         '', 'SCHUnit-CHENNAI', '2024-09-20 10:00:57', '2024-09-20 09:51:11', '1', '1'),
        ('993', 'SCHNELL ENERGY EQUIPMENTS PVT LTD - AP', '', '9344658063',
         '<EMAIL>',
         'KADAPA', '37,ANDHRA PRADESH', 'IN, India', '516004', '', '',
         'Building No./Flat No.: DOOR NO.100/17-5-3,Professors Colony Road,REVENUE WARD NO.100,Kadapa,YSR',
         '', 'SCHUnit-AP', '2024-09-20 10:00:57', '2024-09-20 09:51:11', '1', '1'),
        ('788', 'Tailwind Entertainment Pvt LTD', '', '6374097742',
         '<EMAIL>',
         'COIMBATORE', '33,TAMIL NADU', 'IN, India', '641035', '', '',
         'COIMBATORE',
         '', 'Kos', '2024-09-20 10:00:57', '2024-09-20 09:51:11', '1', '1')
    ]
    for location in location_list:
        insert_location(location_data=location)


if __name__ == "__main__":
    main()
