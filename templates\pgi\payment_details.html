<script type="text/javascript" src="/site_media/js/jquery-3.1.1.min.js?v={{ current_version }}"></script>
<html hidden="hidden">
  <head onload="submitPayuForm()"/>
  <body>
    <form id="payment_details_form" action={{ action }} method="post" name="payuForm" >{% csrf_token %}
      <input type="hidden" name="key" value="{{ MERCHANT_KEY }}" />
      <input type="hidden" name="hash_string" value="{{ hash_string }}" />
      <input type="hidden" id="id-hashh" name="hash" value="{{ hashh }}"/>
      <input type="hidden" name="posted" value="{{ posted }}"/>
      <input type="hidden" name="txnid" value="{{ txnid }}" />
      <table>
        <tr>
          <td><b>Item Parameters</b></td>
        </tr>
        <tr>
          <td>Amount: </td>
          <td><input name="amount" value="{{ posted.amount|default:'' }}" /></td>
          <td>First Name: </td>
          <td><input name="firstname" id="firstname" value="{{ posted.firstname|default:'' }}" /></td>
        </tr>
        <tr>
          <td>Email: </td>
          <td><input name="email" id="email"  value="{{ posted.email|default:'' }}" /></td>
          <td>Phone: </td>
          <td><input name="phone" value="{{ posted.phone|default:'' }}" /></td>
        </tr>
        <tr>
          <td>Product Info: </td>
          <td colspan="3"><textarea name="productinfo">{{ posted.product_info|default:'' }}</textarea></td>
        </tr>
        <tr>
          <td>Success URI: </td>
          <td colspan="3"><input name="surl" value="{{ posted.success_url }}" size="64" /></td>
        </tr>
        <tr>
          <td>Failure URI: </td>
          <td colspan="3"><input name="furl" value="{{ posted.failure_url }}" size="64" /></td>
        </tr>
        <tr>
          <td colspan="3"><input type="hidden" name="service_provider" value="payu_paisa" size="64" /></td>
        </tr>
        <tr>
          <td><b>Optional Parameters</b></td>
        </tr>
        <tr>
          <td>Last Name: </td>
          <td><input name="lastname" id="lastname" value="{{ posted.lastname }}" /></td>
          <td>Cancel URI: </td>
          <td><input name="curl" value="" /></td>
        </tr>
        <tr>
          <td>Address1: </td>
          <td><input name="address1" value="" /></td>
          <td>Address2: </td>
          <td><input name="address2" value="" /></td>
        </tr>
        <tr>
          <td>City: </td>
          <td><input name="city" value="" /></td>
          <td>State: </td>
          <td><input name="state" value="" /></td>
        </tr>
        <tr>
          <td>Country: </td>
          <td><input name="country" value="" /></td>
          <td>Zipcode: </td>
          <td><input name="zipcode" value="" /></td>
        </tr>
        <tr>
          <td>UDF1: </td>
          <td><input name="udf1" value="{{ posted.pay_type|default:'' }}" /></td>
          <td>UDF2: </td>
          <td><input name="udf2" value="" /></td>
        </tr>
        <tr>
          <td>UDF3: </td>
          <td><input name="udf3" value="" /></td>
          <td>UDF4: </td>
          <td><input name="udf4" value="" /></td>
        </tr>
        <tr>
          <td>UDF5: </td>
          <td><input name="udf5" value="" /></td>
          <td>PG: </td>
          <td><input name="pg" value="" /></td>
        </tr>

        <tr>
          <td colspan="4"><input type="submit" value="Submit" /></td>
        </tr>
      </table>
    </form>
  </body>
</html>
<script type="text/javascript">
  var hash = $("#id-hashh").val();
  $(document).ready(function() {
    if(hash =='') {
      return;
    }
    $("#payment_details_form").submit();
  });
</script>