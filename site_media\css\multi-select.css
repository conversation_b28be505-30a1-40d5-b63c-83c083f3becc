.multi-wrapper {
    border: 1px solid #ccc;
    border-radius: 3px;
    overflow: hidden;
    width: 100%;
    background:  #ddd;
}

.multi-wrapper .non-selected-wrapper,
.multi-wrapper .selected-wrapper {
    box-sizing: border-box;
    display: inline-block;
    height: 200px;
    overflow-y: scroll;;
    padding: 10px;
    vertical-align: top;
    width: 50%;
    overflow-x: hidden;
}

.multi-wrapper .non-selected-wrapper a,
.multi-wrapper .selected-wrapper a {
    text-decoration: none;
}

.multi-wrapper .non-selected-wrapper {
    background: #fafafa;
    border-right: 1px solid #ccc;
    border-top: 1px solid #ccc;
}

.multi-wrapper .selected-wrapper {
    background: #fff;
    border-top: 1px solid #ccc;
}

.multi-wrapper .item {
    cursor: pointer;
    display: block;
    padding: 5px 10px;
    background: #efefef;
    margin: 2px 0;
}

.multi-wrapper .item:hover {
    border-radius: 2px;
    background: rgba(32, 155, 225,0.8) !important;
    font-weight: normal;
    color: #ffffff;
}

.multi-wrapper .search-input {
    border: 0;
    border-bottom: 1px solid #ccc;
    border-radius: 0;
    display: block;
    font-size: 1em;
    margin: 0;
    outline: 0;
    padding: 10px 20px;
    width: 100%;
}

.multi-wrapper .non-selected-wrapper .item.selected {
    opacity: 0.5;
    display: none;
}

.multi-wrapper .non-selected-wrapper .row.selected:hover {
    background: inherit;
    cursor: inherit;
}

.non-selected-wrapper .fa-chevron-right {
    float: right;
    margin-top: -20px;
    margin-right: 6px;
    font-size: 12px;
    cursor: pointer;
}

.non-selected-wrapper .fa-chevron-right:before {
    content: "\f054";
}

.selected-wrapper .fa-chevron-right {
    float: left;
    margin-top: -20px;
    margin-left: 6px;
    font-size: 11px;
    cursor: pointer;
}

.selected-wrapper .fa-chevron-right:before {
    content: "\f053";
}

.selected-wrapper a {
    padding-left: 30px !important;
    background: rgba(32,155,225,0.3) !important;
    color: #000;
}

.multi-js-search {
    width: 180px;
    padding: 6px;
    margin: 4px 10px;
    height: 22px;
    font-size: 12px;
    box-shadow: 0 0;
}

.multi-wrapper-header {
    margin-bottom: 30px;
}

.multi-wrapper-header div {
    width:  25%;
    float: left;
}

.multi-wrapper-header div.multi-wrapper-header-search,
.multi-wrapper-header div.multi-wrapper-header-count {
    width:  32%;
    float: left;
}

.multi-wrapper-header div.multi-wrapper-header-add,
.multi-wrapper-header div.multi-wrapper-header-remove {
    width:  18%;
    float: left;
}

.multi-wrapper-header-add,
.multi-wrapper-header-remove {
    text-align: right;
    padding: 6px 12px;
    font-weight: bold;
    cursor: pointer;
}

.multi-wrapper-header-add:hover,
.multi-wrapper-header-remove:hover {
    text-decoration: underline;
}

.multi-wrapper-header-add {
    border-right:  1px solid #999;
}

.multi-wrapper-header-count {
    padding: 6px 12px;
}