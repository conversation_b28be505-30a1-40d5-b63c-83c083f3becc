var ledgerdetails = [];

$(function() {
    $('#add_new_form').click(function() {
        var form_idx = parseInt($('#id_v_particular-TOTAL_FORMS').val());
        new_form = $('#v_particular-__prefix__').html().replace(/__prefix__/g, form_idx);
        new_form_html = "<tr bgcolor=\"#ECECEC\" id=\"v_particular-" + form_idx +
                        "\" align=\"center\">" + new_form + "</tr>";
        $(new_form_html).insertBefore('#v_particular-__prefix__');
        $('#id_v_particular-TOTAL_FORMS').val(form_idx + 1);
        copyFromEmptyForm(form_idx);
    });

    $('#buttonautofill').click(function(){
        var table = document.getElementById("bill_list");
        var rowCount = table.rows.length;
        var voucherBalanceAmt = 0;
        var ledgerAmount = $('#ledger_amount').val();
        var balanceAmount = $('#balance_amount').val();
        var auto_net_value = 0;
        var ledger_id = document.getElementById('bill_ledger_id').value;
        if (balanceAmount > 0) {
            is_debit = $("#ledger_is_debit").val();
            if (rowCount > 1) {
                for (var i = 0; i < rowCount-3; i++) {
                    if(($('#id_ledger_bill-'+i+'-ledger_id').val() == ledger_id) && !$('#id_ledger_bill-' + i + '-DELETE').is(':checked') && ($('#id_ledger_bill-'+i+'-is_debit').val() != is_debit)){
                        if (parseFloat((ledgerAmount-voucherBalanceAmt)) > parseFloat($('#id_ledger_bill-'+i+'-net_value').val())){
                            voucherBalanceAmt += parseFloat($('#id_ledger_bill-'+i+'-net_value').val());
                            auto_net_value = parseFloat($('#id_ledger_bill-'+i+'-net_value').val()).toFixed(2);
                        }else{
                            auto_net_value = parseFloat((ledgerAmount-voucherBalanceAmt)).toFixed(2);
                            voucherBalanceAmt +=parseFloat(auto_net_value);
                        }
                        if (is_debit == 0){
                            $('#id_ledger_bill-'+i+'-credit_settlement').val(auto_net_value);
                        }else if (is_debit == 1){
                            $('#id_ledger_bill-'+i+'-debit_settlement').val(auto_net_value);
                        }
                    }
                }
            }
            $('#balance_amount').val(parseFloat((ledgerAmount-voucherBalanceAmt)));
            calcLedgerTotal();
        }
    });

    $('#approve_voucher_button').click(function(){
        if (validateVoucherForm()){
            $('#loading').show();
            $("#approve_voucher_button").val('Processing...').addClass('btn-processing');
            var form_status = document.getElementById('id_voucher-status');
            form_status.value = 1;
            clickButton('save_voucher_button');
        }
    });

    $('#add_new_ledger').click(function () {
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();

        var ControlCollections = [
            {
                controltype: 'textbox',
                controlid: 'ledgername',
                isrequired: true,
                errormsg: 'Ledger is required.'
            },
            {
                controltype: 'textbox',
                controlid: 'amount',
                isrequired: true,
                errormsg: 'Amount is required.'
            }
        ];

        if($("#ledgername").val() != "") {
            var control = {
                controltype: 'textbox',
                controlid: 'ledgercode',
                isrequired: true,
                errormsg: 'select ledger from the type ahead list.'
            };
            ControlCollections[ControlCollections.length] = control;
        }

        var result = JSCustomValidator.JSvalidate(ControlCollections);

        refreshSessionPerNActions(10);
        if(result) {
            if ($('#ledgername').val()!="" && $('#amount').val()!="") {

                var form_idx = parseInt($('#id_v_particular-TOTAL_FORMS').val());
                new_form = $('#v_particular-__prefix__').html().replace(/__prefix__/g, form_idx);
                new_form_html = "<tr bgcolor=\"#ECECEC\" id=\"v_particular-" + form_idx +
                                "\" align=\"center\">" + new_form + "</tr>";
                $(new_form_html).insertBefore('#v_particular-__prefix__');
                $('#id_v_particular-TOTAL_FORMS').val(form_idx + 1);

                var added_form_ledger_id = document.getElementById('id_v_particular-'+form_idx+'-ledger_id');
                var added_form_ledger_label = document.getElementById('id_v_particular-'+form_idx+'-label');
                var added_form_ledger_label_element = document.getElementById('id_v_particular-'+form_idx+'-ledger_label');
                var added_form_ledger_opening_element = document.getElementById('id_v_particular-'+form_idx+'-opening');
                var added_form_item_no = document.getElementById('id_v_particular-'+form_idx+'-item_no');
                var added_form_is_debit = document.getElementById('id_v_particular-'+form_idx+'-is_debit');
                var added_form_amount = document.getElementById('id_v_particular-'+form_idx+'-amount');
                var added_form_debit_amount = document.getElementById('id_v_particular-'+form_idx+'-debit_amount');
                var added_form_credit_amount = document.getElementById('id_v_particular-'+form_idx+'-credit_amount');
                var added_form_delete_flag = document.getElementById('id_v_particular-'+form_idx+'-DELETE');
                var added_form_enterprise_id = document.getElementById('id_v_particular-'+form_idx+'-enterprise_id');
                var added_form_item_no_label = document.getElementById('id_v_particular-'+form_idx+'-item_no_label');
                var added_form_bill_details = document.getElementById('id_v_particular-'+form_idx+'-bill_details');
                //var added_billable = document.getElementById('id_v_particular-'+form_idx+'-billable');

                // Initializing the form's ref-key data (composite key - voucher_no + item_no)
                if(document.getElementById('id_v_particular-'+parseInt(form_idx-1)+'-item_no') != null){
                    added_form_item_no.value = parseInt(parseInt(document.getElementById('id_v_particular-'+parseInt(form_idx-1)+'-item_no').value) + 1);
                }else{
                    added_form_item_no.value = 1;
                }
                if (($("#id_voucher-type_id option:selected").val() == 3 || $("#id_voucher-type_id option:selected").val() == 2) && $("#id_hidden_voucher_id").val()!="" && $("#id_hidden_voucher_id").val()!="None") {
                    added_form_item_no_label.innerHTML = form_idx + '.';
                }else{
                    added_form_item_no_label.innerHTML = (form_idx + 1) + '.';
                }
                // Copying input entry data to the new form

                added_form_amount.value = document.getElementById('amount').value;
                if ($("#is_debit option:selected").text()=="Cr"){
                    added_form_debit_amount.value = 0;
                    added_form_credit_amount.value = document.getElementById('amount').value;
                    added_form_credit_amount.removeAttribute("disabled");
                    added_form_credit_amount.setAttribute("class", "form-control text-right");
                }else{
                    added_form_credit_amount.value = 0;
                    added_form_debit_amount.value = document.getElementById('amount').value;
                    added_form_debit_amount.removeAttribute("disabled");
                    added_form_debit_amount.setAttribute("class", "form-control text-right");
                }

                added_form_amount.value = document.getElementById('amount').value;
                added_form_ledger_id.value = document.getElementById('ledgercode').value;
                added_form_ledger_label.innerHTML = document.getElementById('ledgername').value;
                added_form_ledger_label_element.value = document.getElementById('ledgername').value;

                if($("#billable").val() != 1){
                    added_form_bill_details.setAttribute("hidden","hidden");
                }
                added_form_is_debit.value = $("#is_debit option:selected").val();
                added_form_delete_flag.checked = false;
                added_form_enterprise_id.value = document.getElementById('id_voucher-enterprise_id').value;
                ledger_opening_criteria = {ledger_ids: $('#ledgercode').val(), opening_on: $('#id_voucher-voucher_date').val()};
                added_form_ledger_opening_element.innerHTML = "<div id='id_opening-" + $('#ledgercode').val() + "'></div>";
                loadLedgerOpening(ledger_opening_criteria);

                $('#ledgername').val('');
                $('#ledgercode').val('');
                $('#amount').val('');
                $('#is_debit').focus() ;
                calcTotal();
                enterKeySubmitEvent();
            }
            setTimeout(function(){
                $(".error-border").removeClass('error-border');
                $(".custom-error-message").remove();
            },0);
        }
        CustomAutocomplete();
        validateTextInputs();
    });

    $('#add_new_ledger_bill').click(function () {
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();

        var ControlCollections = [
            {
                controltype: 'textbox',
                controlid: 'bill_no',
                isrequired: true,
                errormsg: 'Bill No. is required.'
            },
            {
                controltype: 'textbox',
                controlid: 'bill_date',
                isrequired: true,
                errormsg: 'Date is required.'
            },
            {
                controltype: 'textbox',
                controlid: 'ledger_bill_amount',
                isrequired: true,
                errormsg: 'Amount is required.'
            }
        ];
        var result = JSCustomValidator.JSvalidate(ControlCollections);
        if(result) {
            $.ajax({
                url: "/erp/accounts/json/voucher/check_bill_details/",
                type: "post",
                datatype:"json",
                data: {bill_no:$('#bill_no').val().trim(), bill_date:$('#bill_date').val(),
                        ledger_id: document.getElementById('bill_ledger_id').value},
                success: function(response){
                    if (response>0){
                        swal("Bill Details Already Exists!")
                        return;
                    }else{
                        refreshSessionPerNActions(10);
                        if ($('#bill_no').val()!=""&&$('#bill_date').val()!=""&&$('#ledger_bill_amount').val()!="") {

                            var ledger_id = document.getElementById('bill_ledger_id').value;
                            var form_idx = parseInt($('#id_ledger_bill-TOTAL_FORMS').val());
                            new_form = $('#ledger_bill-__prefix__').html().replace(/__prefix__/g, form_idx);
                            new_form_html = "<tr bgcolor=\"#ECECEC\" class=\"tr-ledger-bill\" id=\"ledger_bill-" + ledger_id + "-" + form_idx +
                                            "\" name=\"ledger_bill-"+ ledger_id +"\" ledger_no= \""+ ledger_id +"\" align=\"center\">" + new_form + "</tr>";
                            $(new_form_html).insertBefore('#ledger_bill-__prefix__');
                            $('#id_ledger_bill-TOTAL_FORMS').val(form_idx + 1);

                            var add_form_ledger_id = document.getElementById('id_ledger_bill-'+form_idx+'-ledger_id');
                            var add_form_bill_no = document.getElementById('id_ledger_bill-'+form_idx+'-bill_no');
                            var add_form_bill_date = document.getElementById('id_ledger_bill-'+form_idx+'-bill_date');
                            var add_form_is_debit = document.getElementById('id_ledger_bill-'+form_idx+'-is_debit');
                            var add_form_net_value = document.getElementById('id_ledger_bill-'+form_idx+'-net_value');
                            var add_form_delete_flag = document.getElementById('id_ledger_bill-'+form_idx+'-DELETE');
                            var add_form_enterprise_id = document.getElementById('id_ledger_bill-'+form_idx+'-enterprise_id');
                            var add_form_dr_cr_label = document.getElementById('id_ledger_bill-'+form_idx+'-dr_cr_label');
                            var add_form_debit_settlement = document.getElementById('id_ledger_bill-'+form_idx+'-debit_settlement');
                            var add_form_credit_settlement = document.getElementById('id_ledger_bill-'+form_idx+'-credit_settlement');

                            add_form_bill_no.value = document.getElementById('bill_no').value;
                            add_form_bill_date.value = document.getElementById('bill_date').value;
                            add_form_ledger_id.value = document.getElementById('bill_ledger_id').value;
                            add_form_net_value.value = document.getElementById('ledger_bill_amount').value;
                            if ($("#ledger_is_debit").val() == 0) {
                                add_form_dr_cr_label.innerHTML = "Cr"
                                add_form_credit_settlement.value = document.getElementById('ledger_bill_amount').value;
                                add_form_debit_settlement.value = 0;
                            }else{
                                add_form_dr_cr_label.innerHTML = "Dr"
                                add_form_debit_settlement.value = document.getElementById('ledger_bill_amount').value;
                                add_form_credit_settlement.value = 0;
                            }
                            add_form_is_debit.value = $("#ledger_is_debit").val()
                            add_form_delete_flag.checked = false;
                            add_form_enterprise_id.value = document.getElementById('id_voucher-enterprise_id').value;
                            calcLedgerTotal();
                            $('#bill_no').val('');
                            $('#ledger_bill_amount').val('');
                            $('#bill_no').focus() ;
                            validateTextInputs();
                            setPartialDatePicker();
                            removeValidationError();
                            enterKeySubmitEvent();
                        }

                        $(".delete_ledger_bill").click(function(){
                        var currentId = $(this);
                           swal({
                              title: "Are you sure?",
                              text: "Do you want to delete?",
                              type: "warning",
                              showCancelButton: true,
                              confirmButtonColor: "#209be1",
                              confirmButtonText: "Yes, delete it!",
                              closeOnConfirm: true
                            },
                            function(){
                                currentId.closest('tr').find("input[type='checkbox']").prop('checked', true);
                                currentId.closest('tr').addClass('hide');
                                calcTotal();
                                calcLedgerTotal();
                            });
                        });
                    }
                }
            });
        }
    });
    updateProjectChosen($("#selected_project_code").val(), "id_voucher-project_code");
});


function loadLedgerOpening(ledger_opening_criteria){
    $.ajax({
        url: "/erp/accounts/json/voucher/get_ledger_opening/",
        type: "post",
        datatype:"json",
        data: ledger_opening_criteria,
        success: function(response){
            $.each(response, function (i, item) {
                ledger_opening_json = JSON.parse(JSON.stringify(item));
                var opening_label = ledger_opening_json["opening"];
                if(parseFloat(ledger_opening_json["opening"]) > 0){
                    opening_label = opening_label + " Cr";
                }
                if(parseFloat(ledger_opening_json["opening"]) < 0){
                    opening_label = (parseFloat(opening_label) * -1) + " Dr";
                }
                $("#id_opening-" + ledger_opening_json["ledger_id"]).html(opening_label);
            });
        }
    });
}

function SetDatePickerValue(){
	if($("#id_voucher-voucher_date").val().trim() == "") {
	    $('#id_voucher-voucher_date').datepicker('setDate', '+0');
	}
    $('#id_voucher-bill_date').datepicker('setDate', '+0');
}

$(document).ready(function(){
    if($('#form_errors').val() != '' || ($('#formset_errors').val().search(/[a-z]/ig) >= 0)){
        document.getElementById('error_messages').style.display = "block";
    }
    $('#error_close').click(function(){
        document.getElementById('error_messages').style.display = "none";
    });

    $("#id_voucher-type_id > option").each(function() {
        if(this.value == '7'){
            this.setAttribute("disabled", "disabled");
        }
    });

    $('.modal').on('hidden.bs.modal', function(e) {
        $(this).find('.error-border').removeClass("error-border");
        $('#bill_no, #ledger_bill_amount').val('');
        $(this).find('.custom-error-message').remove();
    }) ;

    $('#close_button, #save_button').click(function () {
        refreshSessionPerNActions(10);
        if (parseFloat(document.getElementById('total_debit_amount').value) > 0 || parseFloat(document.getElementById('total_credit_amount').value) > 0)
        {
            if (parseFloat(document.getElementById('total_debit_amount').value) == parseFloat(document.getElementById('ledger_amount').value) || parseFloat(document.getElementById('total_credit_amount').value) == parseFloat(document.getElementById('ledger_amount').value) ) {
                $("#ledger_bill_div").modal('hide');
            } else if (parseFloat(document.getElementById('total_debit_amount').value) < parseFloat(document.getElementById('ledger_amount').value) && parseFloat(document.getElementById('total_credit_amount').value) < parseFloat(document.getElementById('ledger_amount').value) ) {
                swal({
                    title: "Are you sure?",
                    text: "Total amount is not completely utilized for Bill Settlement. Do you still want to continue?",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#209be1",
                    confirmButtonText: "Yes, do it!",
                    closeOnConfirm: true
                },
                function(){
                    $("#ledger_bill_div").modal('hide');
                });
            }
            else{
                swal("", "Settlement amount exceeds the Voucher Value.", "error");
                $("#ledger_bill_div").modal('show');
                return;
            }
        }else{
            $("#ledger_bill_div").modal('hide');
        }
		$(".error-border").removeClass('error-border');
		$(".custom-error-message").remove();
    });

    $('#cancel_button').click(function () {
	    var LedgerBillCount = parseInt($('#id_ledger_bill-TOTAL_FORMS').val())
	    var cr_amount =0;
	    var dr_amount =0;
	    for (i=0; i<LedgerBillCount; i++){
            document.getElementById('id_ledger_bill-'+i+'-debit_settlement').value= "0.00";
            document.getElementById('id_ledger_bill-'+i+'-credit_settlement').value= "0.00";
	    }
	    calcLedgerTotal();

    });

    $("select#id_voucher-type_id").change(function () {
        var start = new Date($("#voucher_date").val()),
            end   = new Date(),
            diff  = new Date(end - start),
            days  = diff/1000/60/60/24;
            
        if ($("#id_voucher-type_id option:selected").val() == 3){
            document.getElementById("ledger_bank_div").style.display = "block";
            document.getElementById("ledger_cash_div").style.display = "none";
            $("#voucher_date").addClass('financial-date-end').removeClass("till-today");
            CustomDatePickerSettings();
        }else if ($("#id_voucher-type_id option:selected").val() == 2){
            document.getElementById("ledger_bank_div").style.display = "none";
            document.getElementById("ledger_cash_div").style.display = "block";
            $("#voucher_date").removeClass('financial-date-end').addClass("till-today");
            if(days < 0) {
                swal({
                    title: "",
                    text: "<b>Note: </b>"+$("#id_voucher-type_id option:selected").text()+" voucher cannot be set to future date. So voucher date is set to Today.",
                    type: "info"
                });
                $("#id_voucher-voucher_date").val(new Date());
            }
            CustomDatePickerSettings();
        }else{
            document.getElementById("ledger_bank_div").style.display = "none";
            document.getElementById("ledger_cash_div").style.display = "none";
            is_debit = document.getElementById("is_debit")
            is_debit.removeAttribute("disabled");
            $("#voucher_date").removeClass('financial-date-end').addClass("till-today");
            if(days < 0) {
                swal({
                    title: "",
                    text: "<b>Note: </b>"+$("#id_voucher-type_id option:selected").text()+" voucher cannot be set to future date. <br />So voucher date is set to <b><i>Today</i></b>.",
                    type: "info"
                });
                $("#id_voucher-voucher_date").val(new Date());
            }
            CustomDatePickerSettings();
        }
    });

    $("select#payment").change(function () {
        if ($("#payment option:selected").val() ==0){
			$("#is_debit").val(0);
        }else{
            $("#is_debit").val(1);
        }
	});

    calcTotal();
    if(document.getElementById('id_voucher-voucher_no').value=="0"){
		$("#approve_voucher_button").show();
    }else{
        $("#approve_voucher_button").hide();
    }

	if($("#last_created_voucher_code").val()!="" && $("#last_created_voucher_code").val()!="None"){
		$('#loading').hide();
		swal({
		  text: "",
		  title: "Last Modified Voucher Code:<br> " + $("#last_created_voucher_code").val()
		});
	}
	if($("#last_saved_voucher_error").val()!="" && $("#last_saved_voucher_error").val()!="None"){
		$('#loading').hide();
		swal({
		  title: "Voucher cannot be saved",
		  text: $("#last_saved_voucher_error").val(),
		  type: "warning"
		});
	}

    $('#voucher_date').on('hide.daterangepicker', function(ev, picker) {
        setTimeout(function(){
            var particulars_count = $('#id_v_particular-TOTAL_FORMS').val();
            $('#id_opening_date_label').html(moment($('#id_voucher-voucher_date').val()).format('MMM D, YYYY'));
            if(particulars_count > 0){
                var ledger_ids =[];
                for(i=0; i<particulars_count; i++){
                    ledger_ids.push($('#id_v_particular-' + i + '-ledger_id').val());
                }
                var ledger_opening_criteria = { ledger_ids: ledger_ids.join(',') , opening_on: $('#id_voucher-voucher_date').val() };
                console.log(ledger_opening_criteria)
                loadLedgerOpening(ledger_opening_criteria);
            }
        },100);
    });

    transformToDummyForm('tag');
    create_delete_tag_button();
    if($(".header_current_page").text() == "" || $(".header_current_page").text() == "New") {
        $("#id_voucher-project_code").val($('#id_voucher-project_code optgroup:eq(0) option:eq(0)').val()).trigger("chosen:updated");
    }
});


function autoGenerateLedgerForm(){
    var LedgerFormCount = parseInt($('#id_v_particular-TOTAL_FORMS').val());
    var cr_amount =0;
    var dr_amount =0;
     for (i=0; i<LedgerFormCount; i++){
        if (document.getElementById('id_v_particular-'+i+'-DELETE').checked==false){
            cr_amount = (parseFloat(cr_amount) + parseFloat(document.getElementById('id_v_particular-'+i+'-credit_amount').value)).toFixed(2);
            dr_amount  = (parseFloat(dr_amount) + parseFloat(document.getElementById('id_v_particular-'+i+'-debit_amount').value)).toFixed(2);
        }
     }

    var form_idx = parseInt($('#id_v_particular-TOTAL_FORMS').val());
    new_form = $('#v_particular-__prefix__').html().replace(/__prefix__/g, form_idx);
    new_form_html = "<tr hidden='hidden' bgcolor=\"#ECECEC\" id=\"v_particular-" + form_idx +
                    "\" align=\"center\">" + new_form + "</tr>";
    $(new_form_html).insertBefore('#v_particular-__prefix__');
    $('#id_v_particular-TOTAL_FORMS').val(form_idx + 1);

    var added_form_ledger_id = document.getElementById('id_v_particular-'+form_idx+'-ledger_id');
    var added_form_ledger_label = document.getElementById('id_v_particular-'+form_idx+'-label');
    var added_form_ledger_label_element = document.getElementById('id_v_particular-'+form_idx+'-ledger_label');
    var added_form_item_no = document.getElementById('id_v_particular-'+form_idx+'-item_no');
    var added_form_is_debit = document.getElementById('id_v_particular-'+form_idx+'-is_debit');
    var added_form_amount = document.getElementById('id_v_particular-'+form_idx+'-amount');
    var added_form_debit_amount = document.getElementById('id_v_particular-'+form_idx+'-debit_amount');
    var added_form_credit_amount = document.getElementById('id_v_particular-'+form_idx+'-credit_amount');
    var added_form_delete_flag = document.getElementById('id_v_particular-'+form_idx+'-DELETE');
    var added_form_enterprise_id = document.getElementById('id_v_particular-'+form_idx+'-enterprise_id');
    var added_form_item_no_label = document.getElementById('id_v_particular-'+form_idx+'-item_no_label');

    // Initializing the form's ref-key data (composite key - voucher_no + item_no)
    if(document.getElementById('id_v_particular-'+parseInt(form_idx-1)+'-item_no') != null){
        added_form_item_no.value = parseInt(parseInt(document.getElementById('id_v_particular-'+parseInt(form_idx-1)+'-item_no').value) + 1);
    }else{
        added_form_item_no.value = 1;
    }

    added_form_item_no_label.innerHTML = (form_idx + 1) + '.';

    // Copying input entry data to the new form
    added_form_amount.value = Math.abs(parseFloat(cr_amount)-parseFloat(dr_amount));
    if ($("#id_voucher-type_id option:selected").val() ==3){
        added_form_ledger_id.value = $("#id_bank_ledger option:selected").val();
    }else if ($("#id_voucher-type_id option:selected").val() ==2){
        added_form_ledger_id.value = $("#id_cash_ledger option:selected").val();
    }
    added_form_credit_amount.removeAttribute("disabled");
    added_form_debit_amount.removeAttribute("disabled");

    if(cr_amount > dr_amount)
    {
        added_form_debit_amount.value = cr_amount;
        added_form_is_debit.value = 1;
        added_form_credit_amount.value = 0
    } else {
        added_form_credit_amount.value = dr_amount;
        added_form_is_debit.value = 0;
        added_form_debit_amount.value =0;
    }
    added_form_delete_flag.checked = false;
    added_form_enterprise_id.value = document.getElementById('id_voucher-enterprise_id').value;

    CustomAutocomplete();
    calcTotal();
    return true;
}

function copyFromEmptyForm(form_idx){
    var added_form_ledger_id = document.getElementById('id_v_particular-'+form_idx+'-ledger_id');
    var added_form_item_no = document.getElementById('id_v_particular-'+form_idx+'-item_no');
    var added_form_is_debit = document.getElementById('id_v_particular-'+form_idx+'-is_debit');
    var added_form_amount = document.getElementById('id_v_particular-'+form_idx+'-amount');
    var added_form_debit_amount = document.getElementById('id_v_particular-'+form_idx+'-debit_amount');
    var added_form_credit_amount = document.getElementById('id_v_particular-'+form_idx+'-credit_amount');
    var added_form_delete_flag = document.getElementById('id_v_particular-'+form_idx+'-DELETE');
    var added_form_enterprise_id = document.getElementById('id_v_particular-'+form_idx+'-enterprise_id');

    // Initializing the form's ref-key data (composite key - voucher_no + item_no)
    if(document.getElementById('id_v_particular-'+parseInt(form_idx-1)+'-item_no') != null){
    	added_form_item_no.value = parseInt(parseInt(document.getElementById('id_v_particular-'+parseInt(form_idx-1)+'-item_no').value) + 1);
    }else{
    	added_form_item_no.value = 1;
    }

    // Copying input entry data to the new form

    added_form_amount.value = document.getElementById('id_v_particular-__prefix__-amount').value;
    added_form_debit_amount.value = document.getElementById('id_v_particular-__prefix__-debit_amount').value;
    added_form_credit_amount.value = document.getElementById('id_v_particular-__prefix__-credit_amount').value;
    added_form_ledger_id.value = document.getElementById('id_v_particular-__prefix__-ledger_id').value;
    added_form_is_debit.value = document.getElementById('id_v_particular-__prefix__-is_debit').value;
    added_form_delete_flag.checked = document.getElementById('id_v_particular-__prefix__-DELETE').checked;
    added_form_enterprise_id.value = document.getElementById('id_voucher-enterprise_id').value;

	// Resetting the values of new form
    document.getElementById('id_v_particular-__prefix__-ledger_id').value = 'None';
    document.getElementById('id_v_particular-__prefix__-is_debit').value = 'None';
    document.getElementById('id_v_particular-__prefix__-amount').value = '';
    document.getElementById('id_v_particular-__prefix__-debit_amount').value = '';
    document.getElementById('id_v_particular-__prefix__-credit_amount').value = '';
    document.getElementById('id_v_particular-__prefix__-DELETE').checked = false;
}

function calcTotal()
{
	var form_count = parseInt(document.getElementById('id_v_particular-TOTAL_FORMS').value);
	var crTotal=0;
	var drTotal=0;
    for (i=0; i<form_count; i++){
        var amount = $('#id_v_particular-'+i+'-amount').val();
        if (document.getElementById('id_v_particular-'+i+'-DELETE').checked==false){
            if(document.getElementById('id_v_particular-'+i+'-is_debit').value == '0'){
                var credit_amount = document.getElementById('id_v_particular-'+i+'-credit_amount').value;
                if(/[0-9.]/g.test(credit_amount)){
                    crTotal = parseFloat(crTotal) + parseFloat(amount);
                }
            }
            if(document.getElementById('id_v_particular-'+i+'-is_debit').value == '1'){
                var debit_amount = document.getElementById('id_v_particular-'+i+'-debit_amount').value
                if(/[0-9.]/g.test(debit_amount)){
                    drTotal = parseFloat(drTotal) + parseFloat(amount);
                }
            }
        }
    }
    if(document.getElementById('id_v_particular-__prefix__-is_debit').value == '1'){
        drTotal = parseFloat(drTotal) + parseFloat(document.getElementById('id_v_particular-__prefix__-debit_amount').value);
    }
    if(document.getElementById('id_v_particular-__prefix__-is_debit').value == '0'){
        crTotal = parseFloat(crTotal) + parseFloat(document.getElementById('id_v_particular-__prefix__-credit_amount').value);
    }
    document.getElementById('id_cr_total_amount').value = crTotal.toFixed(2);
    document.getElementById('id_dr_total_amount').value = drTotal.toFixed(2);
}

function calcNetLedgerAmount(ledger_id)
{
	var form_count = parseInt(document.getElementById('id_v_particular-TOTAL_FORMS').value);
	var crTotal=0;
	var drTotal=0;
	var ledgerNetAmount = 0;
    for (i=0; i<form_count; i++){
        if(document.getElementById('id_v_particular-'+i+'-ledger_id').value == ledger_id){
            var amount = $('#id_v_particular-'+i+'-amount').val();
            if (document.getElementById('id_v_particular-'+i+'-DELETE').checked==false){
                if(document.getElementById('id_v_particular-'+i+'-is_debit').value == '0'){
                    var credit_amount = document.getElementById('id_v_particular-'+i+'-credit_amount').value;
                    if(/[0-9.]/g.test(credit_amount)){
                        crTotal = parseFloat(crTotal) + parseFloat(amount);
                    }
                }
                if(document.getElementById('id_v_particular-'+i+'-is_debit').value == '1'){
                    var debit_amount = document.getElementById('id_v_particular-'+i+'-debit_amount').value
                    if(/[0-9.]/g.test(debit_amount)){
                        drTotal = parseFloat(drTotal) + parseFloat(amount);
                    }
                }
            }
        }
    }
    if(document.getElementById('id_v_particular-__prefix__-is_debit').value == '1'){
        drTotal = parseFloat(drTotal) + parseFloat(document.getElementById('id_v_particular-__prefix__-debit_amount').value);
    }
    if(document.getElementById('id_v_particular-__prefix__-is_debit').value == '0'){
        crTotal = parseFloat(crTotal) + parseFloat(document.getElementById('id_v_particular-__prefix__-credit_amount').value);
    }
    ledgerNetAmount = Math.abs(drTotal - crTotal)
    return ledgerNetAmount
}


function calcLedgerTotal(){
	var form_count = parseInt(document.getElementById('id_ledger_bill-TOTAL_FORMS').value);
	var crTotal=0;
	var drTotal=0;
    var bills_to_be_attended = [];

    for (i=0; i<form_count; i++){

        if ($('#id_ledger_bill-'+i+'-ledger_id').val() == document.getElementById('bill_ledger_id').value){
	        var cr_amount = $('#id_ledger_bill-'+i+'-credit_settlement').val();
	        var dr_amount = $('#id_ledger_bill-'+i+'-debit_settlement').val();
	        if (document.getElementById('id_ledger_bill-'+i+'-DELETE').checked==false){
                var credit_amount = document.getElementById('id_ledger_bill-'+i+'-credit_settlement').value;
                if(/[0-9.]/g.test(credit_amount)){
                    crTotal = parseFloat(crTotal) + parseFloat(cr_amount);
                }
                var debit_amount = document.getElementById('id_ledger_bill-'+i+'-debit_settlement').value
                if(/[0-9.]/g.test(debit_amount)){
                    drTotal = parseFloat(drTotal) + parseFloat(dr_amount);
                }
	        }
	    }
    }
    document.getElementById('total_debit_amount').value = (drTotal).toFixed(2);
    document.getElementById('total_credit_amount').value = (crTotal).toFixed(2);
	highLightRow();
	ledgerAmount = $('#ledger_amount').val();
	document.getElementById('balance_amount').value = (ledgerAmount - (parseFloat(drTotal) + parseFloat(crTotal))).toFixed(2);

	for (i=0; i<form_count; i++){
	    if ($('#id_ledger_bill-'+i+'-ledger_id').val() == document.getElementById('bill_ledger_id').value){
            if (document.getElementById('id_ledger_bill-'+i+'-is_debit').value==1){
                if (parseFloat(document.getElementById('id_ledger_bill-'+i+'-net_value').value) < parseFloat(document.getElementById('id_ledger_bill-'+i+'-credit_settlement').value)){
                    swal("", "Settlement amount exceeds the Unsettled Bill Value.", "error");
                    return;
                }
                if (parseFloat(document.getElementById('id_ledger_bill-'+i+'-debit_settlement').value) > 0 && ($("#id_hidden_voucher_id").val()!="" && $("#id_ledger_bill-" + i + "-origin_voucher_id").val() !=undefined && $("#id_ledger_bill-" + i + "-origin_voucher_id").val() != $("#id_hidden_voucher_id").val())){
                    bills_to_be_attended.push(i.toString());
                }
            }else{
                if (parseFloat(document.getElementById('id_ledger_bill-'+i+'-net_value').value) < parseFloat(document.getElementById('id_ledger_bill-'+i+'-debit_settlement').value)){
                    swal("", "Settlement amount exceeds the Unsettled Bill Value.", "error");
                    return;
                }
                if (parseFloat(document.getElementById('id_ledger_bill-'+i+'-credit_settlement').value) > 0 && ($("#id_hidden_voucher_id").val()!="" && $("#id_ledger_bill-" + i + "-origin_voucher_id").val() !=undefined && $("#id_ledger_bill-" + i + "-origin_voucher_id").val() != $("#id_hidden_voucher_id").val())){
                    bills_to_be_attended.push(i.toString());
                }
            }
        }
	}
	if (bills_to_be_attended.length > 0){
	    var bill_nos = "";
	    for(i=0; i<bills_to_be_attended.length; i++){
	        bill_nos += "[" + $('#id_ledger_bill-' + bills_to_be_attended[i] + '-bill_no').val() + "] ";
	    }
	    swal({
            title: "",
            text: "Settlement stretches value for the bills - <b>" + bill_nos + "</b>, instead of clearing them!<br/><br/>Probably you may need to create a New Bill!",
            type: "warning",
            confirmButtonText: "OOPS! Clear them",
            showCancelButton: true,
            cancelButtonText: "That's OK! Proceed"
        },
        function(){
            for(i=0; i<bills_to_be_attended.length; i++){
                if(document.getElementById("id_ledger_bill-" + bills_to_be_attended[i] + "-origin_voucher_id") == null){
                    $('#id_ledger_bill-' + bills_to_be_attended[i] + '-delete_ledger_bill').click();
                } else {
                    $('#id_ledger_bill-' + bills_to_be_attended[i] + '-debit_settlement').val('0.00');
                    $('#id_ledger_bill-' + bills_to_be_attended[i] + '-credit_settlement').val('0.00');
                }
            }
            $("#bill_no").focus();
            calcLedgerTotal();
        });
	}

}


function loadAptAmount(prefix){
	var is_debit = document.getElementById('id_'+prefix+'-is_debit').value;
	var credit_amount = document.getElementById('id_'+prefix+'-credit_amount');
	var debit_amount = document.getElementById('id_'+prefix+'-debit_amount');

	credit_amount.value = "";
	debit_amount.value = "";

	if(is_debit ==0){
		credit_amount.removeAttribute("disabled");
		credit_amount.setAttribute("class", "form-control");
		debit_amount.setAttribute("disabled", true);
		debit_amount.setAttribute("class", "form-control");
	}else if(is_debit ==1){
		debit_amount.removeAttribute("disabled");
		debit_amount.setAttribute("class", "form-control");
		credit_amount.setAttribute("disabled", true);
		credit_amount.setAttribute("class", "form-control");
	}else{
		debit_amount.setAttribute("disabled", true);
		credit_amount.setAttribute("disabled", true);
		debit_amount.setAttribute("class", "form-control");
		credit_amount.setAttribute("class", "form-control");
	}
}

function deleteVoucherForm(FormId) {
	swal({
	  title: "Are you sure?",
	  text: "Do you want to delete?",
	  type: "warning",
	  showCancelButton: true,
	  confirmButtonColor: "#209be1",
	  confirmButtonText: "Yes, delete it!",
	  closeOnConfirm: true
	},
	function(){
		var deleteFlag = document.getElementById('id_' + FormId + '-DELETE');
		var deleteRow = document.getElementById(FormId);
        var ledger = document.getElementById('id_' + FormId + '-ledger_id');
        var ledger_id =  ledger.value;

		deleteFlag.checked = true;
		deleteRow.style.display = 'none';
		calcTotal();
		calcLedgerTotal();
        ledger_bills = document.getElementsByName('ledger_bill-'+ledger_id);
        var billCount = ledger_bills.length;
        $(".tr-ledger-bill").hide();
        $("#bill_list tr[ledger_no="+ledger_id+"]").each(function(){
            $(this).find("input[type='checkbox']").prop('checked', 'checked');
            $(this).hide();
        });

	});
}

function showBill(FormId) {
    var ledger = document.getElementById('id_' + FormId + '-ledger_id');
    var ledger_id =  ledger.value
    var particular_is_debit = document.getElementById('id_' + FormId + '-is_debit');
    var ledger_amount = calcNetLedgerAmount(ledger_id)

    var net_amount =0
    var type = ""
    var bill_loaded=false
    var is_debit =0;
    var debit_settlement="";
    var credit_settlement="";
    var origin_amount =0;

    document.getElementById('bill_ledger_id').value = ledger_id;
    $('#ledger_amount').val(parseFloat(ledger_amount).toFixed(2));
    $('#ledger_amounts').text(parseFloat(ledger_amount).toFixed(2));
    ledger_bills = document.getElementsByName('ledger_bill-'+ledger_id);
    var billCount = ledger_bills.length;
    $(".tr-ledger-bill").hide();
    for (j=0; j<billCount; j++){
		if ((ledger_bills[j].getAttribute('ledger_no')==ledger_id) && !$('#id_ledger_bill-' + j + '-DELETE').is(':checked')){
            ledger_bills[j].style.display = "table-row";
            bill_loaded = true;
        }
    }

    if (particular_is_debit.value == 0) {
        $("#ledger_is_debit").val(0);
        $("#dr_cr_label").text("Cr");
    }else{
        $("#ledger_is_debit").val(1);
        $("#dr_cr_label").text("Dr");
    }

	var voucher_id = 0;
	if ($('#id_hidden_voucher_id').val()=="None" || $('#id_hidden_voucher_id').val()=="") {
		voucher_id = 0;
	}else{
		voucher_id = $('#id_hidden_voucher_id').val()
	}

	if (!bill_loaded) {
	    $("#loading").show();
        $.ajax({
            url: "/erp/accounts/json/voucher/loadledgerbilldetails/",
            type: "post",
            datatype:"json",
            data: { ledger_id:ledger_id,  voucher_id: voucher_id },
            success: function(response){
                $.each(response, function (i, item) {
                    var amount = (parseFloat(item[2]) - parseFloat(item[3]));
                    var has_valid_item = false;
                    if (amount > 0){
                        type = "Dr";
                        is_debit = 1;
                        net_amount = amount.toFixed(2);
                        has_valid_item = true;
                    }else if(amount < 0){
                        type = "Cr";
                        is_debit = 0;
                        net_amount = -1 * amount.toFixed(2);
                        has_valid_item = true;
                    }else{
                        type = "";
                        net_amount = 0;
                    }

                    if (net_amount > 0 || (parseFloat(item[6])> 0 || parseFloat(item[7])>0 )){
                        var form_idx = parseInt($('#id_ledger_bill-TOTAL_FORMS').val());
                        $('#id_ledger_bill-TOTAL_FORMS').val(form_idx + 1);
                        $('#id_ledger_bill-INITIAL_FORMS').val(form_idx + 1);
                        var origin_voucher_id = "<input type='hidden' id='id_ledger_bill-" + form_idx +
                            "-origin_voucher_id' value='" + item[5] + "'/>";

                        if (particular_is_debit.value == 0) {
                            debit_settlement = "<input type='text' id='id_ledger_bill-" + form_idx +
                                "-debit_settlement' maxlength='18' onfocus='setNumberRangeOnFocus(this,15,2)' onblur='calcLedgerTotal();' hidden='hidden' name='ledger_bill-" +
                                form_idx + "-debit_settlement' value='" + item[6].toFixed(2) +
                                "'/><input type='text' class='form-control' disabled='disabled' value='" +
                                item[6].toFixed(2) + "'/>";
                            credit_settlement = "<input type='text' class='form-control' id='id_ledger_bill-" +
                                form_idx + "-credit_settlement' maxlength='18' onfocus='setNumberRangeOnFocus(this,15,2)' onblur='calcLedgerTotal();'  name='ledger_bill-" +
                                form_idx + "-credit_settlement' data-enterkey-submit='save_button' value='" + item[7].toFixed(2) + "'/>";
                        }else{
                            credit_settlement = "<input type='text' id='id_ledger_bill-" + form_idx +
                                "-credit_settlement' maxlength='18' onfocus='setNumberRangeOnFocus(this,15,2)' onblur='calcLedgerTotal();' hidden='hidden' name='ledger_bill-" +
                                form_idx + "-credit_settlement' value='" + item[7].toFixed(2) +
                                "'/><input type='text' class='form-control' disabled='disabled' value='" +
                                item[7].toFixed(2) + "'/>";
                            debit_settlement = "<input type='text' class='form-control' id='id_ledger_bill-" +
                                form_idx + "-debit_settlement' maxlength='18' onfocus='setNumberRangeOnFocus(this,15,2)' onblur='calcLedgerTotal();' name='ledger_bill-" +
                                form_idx + "-debit_settlement' data-enterkey-submit='save_button' value='" + item[6].toFixed(2) + "'/>";
                        }
                        if ($('#id_hidden_voucher_id').val()!="" || $('#id_hidden_voucher_id').val()!="None"){
                            if ($('#id_hidden_voucher_id').val()==item[5]){

                                origin_amount = (parseFloat(item[6])- parseFloat(item[7]));
                                if (origin_amount > 0){
                                    type = "Dr";
                                    is_debit = 1;
                                    net_amount = origin_amount.toFixed(2);
                                }else if(origin_amount < 0){
                                    type = "Cr";
                                    is_debit = 0;
                                    net_amount = -1 * origin_amount.toFixed(2);
                                }else{
                                    type = "";
                                    net_amount = 0;
                                }

                                if(item[1] !="") {
                                    var date_format = item[1].split('-');
                                    date_format = date_format[1]+"/"+date_format[2]+"/"+date_format[0];
                                }
                                else {
                                    var date_format = "";
                                }
                                var row = "<tr bgcolor='#ececec' class='tr-ledger-bill exclude_export' border='0' id='ledger_bill-"+ledger_id+"-"+i+"' ledger_no='"+ledger_id+"' name='ledger_bill-"+ledger_id+"' style='font-size:11px; font-weight:normal;' align='center'><td>" +
                                    "<input type='text' class='form-control' id='id_ledger_bill-" + form_idx + "-bill_no' name='ledger_bill-" + form_idx + "-bill_no'  value='" + item[0].trim() +"' />" + origin_voucher_id + "</td><td>" +
                                    "<input type='text' class='form-control single-datepicker-view hide' id='id_ledger_bill-" + form_idx + "-bill_date' name='ledger_bill-" + form_idx + "-bill_date'  value='" + item[1] + "'/>" +
                                    "<input type='text' class='form-control single-datepicker' value='" + moment(date_format).format('MMM D, YYYY') + "' /><i class='manual-gly glyphicon glyphicon-calendar'></i></td><td>" +
                                    "<input type='text' id='id_ledger_bill-" + form_idx + "-bill_id' name='ledger_bill-" + form_idx + "-bill_id' hidden='hidden' value='" + item[4] +"' />" +
                                    "<input type='text' id='id_ledger_bill-" + form_idx + "-net_value' name='ledger_bill-" + form_idx + "-net_value' hidden='hidden' value='" + net_amount +"' />"+
                                    "<input type='text' id='id_ledger_bill-" + form_idx + "-is_debit' name='ledger_bill-" + form_idx + "-is_debit' hidden='hidden' value='" + is_debit +"' />"+
                                    "<input type='checkbox' id='id_ledger_bill-" + form_idx + "-DELETE' name='ledger_bill-" + form_idx + "-DELETE' hidden='hidden'/>"+
                                    "<input type='text' id='id_ledger_bill-" + form_idx + "-ledger_id' name='ledger_bill-" + form_idx + "-ledger_id' hidden='hidden' value='" + ledger_id +"' />" +item[2] +"</td><td  class='text-right'>" +
                                    item[3] + "</td><td  class='text-right'>" +
                                    net_amount + " (" + type + ")" +"</td><td  class='text-right'>"+debit_settlement+""
                                    +"</td><td  class='text-right'>"+credit_settlement+""
                                    + "</td><td></td></tr>";
                                $(row).insertBefore('#ledger_bill-__prefix__');
                            }else{
                                if(item[1] !="") {
                                    var date_format = item[1].split('-');
                                    date_format = date_format[1]+"/"+date_format[2]+"/"+date_format[0];
                                }
                                else {
                                    var date_format="";
                                }
                                var row = "<tr bgcolor='#ececec' class='tr-ledger-bill' border='0' id='ledger_bill-"+ledger_id+"-"+i+"'ledger_no='"+ledger_id+"' name='ledger_bill-"+ledger_id+"' style='font-size:11px; font-weight:normal;' align='center'><td>" +
                                    item[0] + "<input type='text' id='id_ledger_bill-" + form_idx + "-bill_id' name='ledger_bill-" + form_idx + "-bill_id' hidden='hidden' value='" + item[4] +"' />" + origin_voucher_id +
                                    "<input type='text' id='id_ledger_bill-" + form_idx + "-bill_no' name='ledger_bill-" + form_idx + "-bill_no' hidden='hidden' value='" + item[0].trim() +"' />" +
                                    "<input type='text' id='id_ledger_bill-" + form_idx + "-bill_date' name='ledger_bill-" + form_idx + "-bill_date' hidden='hidden' value='" + item[1] +"' />"+
                                    "<input type='text' id='id_ledger_bill-" + form_idx + "-net_value' name='ledger_bill-" + form_idx + "-net_value' hidden='hidden' value='" + net_amount +"' />"+
                                    "<input type='text' id='id_ledger_bill-" + form_idx + "-is_debit' name='ledger_bill-" + form_idx + "-is_debit' hidden='hidden' value='" + is_debit +"' />"+
                                    "<input type='checkbox' id='id_ledger_bill-" + form_idx + "-DELETE' name='ledger_bill-" + form_idx + "-DELETE' hidden='hidden' />"+
                                    "<input type='text' id='id_ledger_bill-" + form_idx + "-ledger_id' name='ledger_bill-" + form_idx + "-ledger_id' hidden='hidden' value='" + ledger_id +"' /></td><td>" +
                                    moment(date_format).format('MMM D, YYYY') + "</td><td  class='text-right'>" +
                                    item[8] + "</td><td  class='text-right'>" +
                                    item[2] + "</td><td  class='text-right'>" +
                                    item[3] + "</td><td  class='text-right'>" +
                                    net_amount + " (" + type + ")" +"</td><td  class='text-right'>"+debit_settlement+""
                                    +"</td><td  class='text-right'>"+credit_settlement+""
                                    + "</td><td></td></tr>";
                                $('#bill_list').append(row).addClass('tbl');
                            }
                        }
                    }
                });
                if(parseInt($('#id_ledger_bill-TOTAL_FORMS').val()) === 0){
                    $('#bill_list').append("<tr bgcolor='#ececec' class='tr-ledger-bill' border='0' style='font-size:11px; font-weight:normal;' align='center'><td colspan='8'>No Pending Bills found. Kindly add a Bill for this Voucher!</td>").addClass('tbl');
                }
                PartialDatePicker();
                calcLedgerTotal();
                enterKeySubmitEvent();
                $("#ledger_bill_div").modal('show');
                $("#loading").hide();
            }
        });
    }
    calcLedgerTotal();
    $("#"+FormId).find('.td_bill_list').addClass("edited_bill_list");
    $("#ledger_bill_div").modal('show');
    $("#ledger_bill_div").on('hidden.bs.modal', function(e) {
        $(".info_message").remove();
    });
}

function setPartialDatePicker(){
	$('.single-datepicker-view-manual').each(function(){
		var thisDate = $(this).val().split('-');
		thisDate = thisDate[1]+"/"+thisDate[2]+"/"+thisDate[0];
		var formattedDate = (moment(thisDate)).format('MMM D, YYYY');
		$(this).next('.single-datepicker-manual').val(formattedDate);
	});
	$('.single-datepicker-manual').datepicker({
		format: 'M d, yyyy'
	}).change(dateChangedManual);

	$(".single-datepicker-manual").keydown(function(e){
		e.preventDefault();
	});
}

function dateChangedManual(ev) {
  $('.single-datepicker-manual').each(function(){
	  var thisVal = $(this).val();
	  var formattedDate = (moment(thisVal)).format('YYYY-M-D');
	  $(this).prev('.single-datepicker-view-manual').val(formattedDate);
  });
}

function PartialDatePicker() {
	$('.single-datepicker-view').each(function(){
		var thisVal = $(this).val().split('-');
		thisVal = thisVal[1]+"/"+thisVal[2]+"/"+thisVal[0];
		var formattedDate = (moment(thisVal)).format('MMM D, YYYY');
		$(this).next('.single-datepicker').val(formattedDate);
	});
	$('.single-datepicker').datepicker({
		format: 'M d, yyyy'
	}).change(dateChanged);

	$(".single-datepicker").keydown(function(e){
		e.preventDefault();
	});
}

function dateChanged(ev) {
  $('.single-datepicker').each(function(){
	  var thisVal = $(this).val();
	  var formattedDate = (moment(thisVal)).format('YYYY-MM-DD');
	  $(this).prev('.single-datepicker-view').val(formattedDate);
  });
}

$(function () {
    var zid =  ""
    $.ajax({
        url: "/erp/accounts/json/voucher/loadledgerdetails/",
        type: "post",
        datatype:"json",
        data: zid,
        success: function(response){
            for (i = 0; i <= response.length - 1 ; i++) {
               ledgerdetails.push({value:response[i][0],label:"["+response[i][2]+"] "+response[i][1]+" ",group:""+response[i][3]+"",billable:response[i][4]});
            }
        }
    });

	$.ajax({
        url: "/erp/accounts/json/voucher/loadbankledgers/",
        type: "post",
        datatype:"json",
        data: "",
        success: function(response){
            $('#id_bank_ledger').val("");
            var items =  "";
            var bank_ledger_id = 0;
            var bank_ledger_form_idx = 0;
            if(document.getElementById("id_voucher-id").value!="" && $("#id_voucher-type_id option:selected").val() ==3){
		        var LedgerFormCount = parseInt($('#id_v_particular-TOTAL_FORMS').val())
		        if (LedgerFormCount !=0){
		            bank_ledger_form_idx = LedgerFormCount-1;
					selected_bank_ledger = document.getElementById("id_v_particular-"+bank_ledger_form_idx+"-ledger_id");
					if (selected_bank_ledger != null) bank_ledger_id = selected_bank_ledger.value;
					document.getElementById("ledger_bank_div").style.display = "block";
					var is_debit = document.getElementById('id_v_particular-0-is_debit').value;
					var is_debit = document.getElementById('id_v_particular-0-is_debit').value;
					if(is_debit == 0){
						$("#payment").val(0);
						$("#is_debit").val(0);
					}else{
						$("#payment").val(1);
						$("#is_debit").val(1);
					}
		        }
		    }

            items = '';
            $("#id_bank_ledger").html(items);
            var is_bank_ledger_selected = false;
            for (i = 0; i <= response.length - 1 ; i++) {
                if (response[i][0] == bank_ledger_id) {
                    is_bank_ledger_selected = true;
                    items += "<option value="+response[i][0]+" selected='selected'> "+response[i][1]+" </option>";
                    var bank_ledger_form_delete_flag = document.getElementById('id_v_particular-'+bank_ledger_form_idx+'-DELETE');
                    var bank_ledger_form_row = document.getElementById('v_particular-' + bank_ledger_form_idx);
                    bank_ledger_form_delete_flag.checked = true;
                    bank_ledger_form_row.style.display = 'none';
                } else {
                    items += "<option value="+response[i][0]+"> "+response[i][1]+" </option>";
                }
            }
            $("#id_bank_ledger").html(items);
            $("#id_bank_ledger").trigger("chosen:updated");
            if (!is_bank_ledger_selected) {
                BankVoucherCheck();
            }
            calcTotal();
        },
        error : function(xhr,errmsg,err) {
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });

    $.ajax({
        url: "/erp/accounts/json/voucher/loadcashledgers/",
        type: "post",
        datatype:"json",
        data: "",
        success: function(response){
            $('#id_cash_ledger').val("");
            var items =  "";
            var cash_ledger_id = 0;
            var cash_ledger_form_idx = 0;
            if(document.getElementById("id_voucher-id").value!="" && $("#id_voucher-type_id option:selected").val() ==2){
		        var LedgerFormCount = parseInt($('#id_v_particular-TOTAL_FORMS').val())
		        if (LedgerFormCount !=0){
		            cash_ledger_form_idx = LedgerFormCount-1;
					selected_cash_ledger = document.getElementById("id_v_particular-"+cash_ledger_form_idx+"-ledger_id");
					if (selected_cash_ledger != null) cash_ledger_id = selected_cash_ledger.value;
					document.getElementById("ledger_cash_div").style.display = "block";
					var is_debit = document.getElementById('id_v_particular-0-is_debit').value;
					if(is_debit == 0){
						$("#payment").val(0);
						$("#is_debit").val(0);
					}else{
						$("#payment").val(1);
						$("#is_debit").val(1);
					}
		        }
		    }

            items = '';
            var is_cash_ledger_selected = false;
            for (i = 0; i <= response.length - 1 ; i++) {
                if (response[i][0] == cash_ledger_id) {
                    is_cash_ledger_selected = true;
                    items += "<option value="+response[i][0]+" selected='selected'> "+response[i][1]+" </option>";
                    var cash_ledger_form_delete_flag = document.getElementById('id_v_particular-'+cash_ledger_form_idx+'-DELETE');
                    var cash_ledger_form_row = document.getElementById('v_particular-' + cash_ledger_form_idx);
                    cash_ledger_form_delete_flag.checked = true;
                    cash_ledger_form_row.style.display = 'none';
                }
                else
                    items += "<option value="+response[i][0]+"> "+response[i][1]+" </option>";
            }
            $("#id_cash_ledger").html(items);
            $("#id_cash_ledger").trigger("chosen:updated");
            if(!is_cash_ledger_selected) {
                CashVoucherCheck();
            }
            calcTotal();
        }
    });

    $("#ledgername").autocomplete({
        source: ledgerdetails,
        minLength: 0,
        focus: function (event, ui) {
            event.preventDefault();
        },
        select: function (event, ui) {
            event.preventDefault();
            $("#ledgercode").val(ui.item.value);
            $("#ledgername").val(ui.item.label);
            $("#group").val(ui.item.group);
            $("#billable").val(ui.item.billable);
        }
    }).focus(function () {
        $(this).autocomplete("search");
    });

    CustomAutocomplete();
});

function CustomAutocomplete(){
    $(".ledger_select_label").autocomplete({
        source: ledgerdetails,
        minLength: 0,
        focus: function (event, ui) {
            event.preventDefault();
        },
        select: function (event, ui) {
            event.preventDefault();
            $(this).closest('tr').find('.ledger_select').val(ui.item.value);
            $(this).closest('tr').find('.ledger_select_label').val(ui.item.label);
            $(this).closest('tr').find('.ledger_select_group').val(ui.item.group);
            $(this).closest('tr').find('.ledger_select_billable').val(ui.item.billable);
        }
    }).focus(function () {
        $(this).autocomplete("search");
    });
}

function saveVoucherFunctionCall() {
    if ($("#id_voucher-status").val() == "0" || $("#id_voucher-id").val() == "" || Number($("#id_voucher-type_id").val() == $("#current_type_id").val())) {
        clickButton("saveVoucher");
    } else {
        EditVoucherNumber(false);
        $.ajax({
            url: "erp/accounts/json/super_edit_voucher_code/",
            method: "POST",
            data:{
                voucher_id: $("#id_voucher-id").val(),
                new_financial_year: $("#voc_financial_year").val(),
                new_voucher_type_id: $("#id_voucher-type_id").val(),
                new_voucher_no: $("#voc_number").val(),
                new_sub_number: $("#voc_number_division").val().trim()
            },
            success: function(response) {
                if (response.response_message == "Success") {
                    ga('send', 'event', 'Voucher', 'Super-Edit Voucher Type & Save', $('#enterprise_label').val(), 1);
//                    clickButton("saveVoucher");
                     $('#loading').hide();
                     location.reload();
                } else {
                    $('#loading').hide();
                    swal({title: "", text: response.custom_message, type: "warning"});
                }
            },
            error: function (xhr, errmsg, err) {
                $('#loading').hide();
                swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
            }
        });
    }
}
function saveVoucher() {
    var new_particular_ledger_id = $('#id_v_particular-__prefix__-ledger_id').val();
    var new_particular_type = $('#id_v_particular-__prefix__-is_debit').val();

    if (new_particular_ledger_id != '' || new_particular_type != 'None' ){
        $('#add_new_form').click();
    }
    if (validateVoucherForm()){
        $('#loading').show();
        $("#save_voucher_button").text('Processing...').addClass('btn-processing');
        saveVoucherFunctionCall();
    }
}

function validateVoucherForm(){
    var new_particular_ledger_id = $('#id_v_particular-__prefix__-ledger_id').val();
    var new_particular_type = $('#id_v_particular-__prefix__-is_debit').val();
    validation_message = "";
    is_valid = true;

    if (new_particular_ledger_id != '' || new_particular_type != 'None' ){
        $('#add_new_form').click();
    }

    if ($("#id_voucher-type_id option:selected").text()== "" || $("#id_voucher-type_id option:selected").text()== "-- Voucher Type --"){
        validation_message = validation_message + "* Please Select a Voucher Type. \n";
        is_valid = false;
    }

    if ($('#id_voucher-voucher_date').val()== ""){
        validation_message = validation_message + "* Please Select a Valid Voucher Date. \n";
        is_valid = false;
    }

    if ($("#id_voucher-type_id option:selected").text()== "Bank" && parseFloat($("#id_dr_total_amount").val()) != parseFloat($("#id_cr_total_amount").val()) ){
        if ($("#id_bank_ledger option:selected").val()== "null" || $("#id_bank_ledger option:selected").val()== ""){
            validation_message = validation_message + "* Please select a valid Bank Ledger. \n";
            is_valid = false;
        }
    }

    if ($("#id_voucher-type_id option:selected").text()== "Cash" && parseFloat($("#id_dr_total_amount").val()) != parseFloat($("#id_cr_total_amount").val())){
        if ($("#id_cash_ledger option:selected").val()== "null" || $("#id_cash_ledger option:selected").val()== ""){
            validation_message = validation_message + "* Please select a valid Cash Ledger. \n";
            is_valid = false;
        }
    }

    if ($("#id_voucher-project_code option:selected").text()== "" || $("#id_voucher-project_code option:selected").text()== "--Projects--"){
        validation_message = validation_message + "* Please Select  Project. \n";
        is_valid = false;
    }

    if ($('#id_dr_total_amount').val() == 0 && $('#id_cr_total_amount').val() == 0){
        validation_message = validation_message + "* Please add atleast one ledger value to save/ approve the voucher. \n";
        is_valid = false;
    }
    var project_type = $('#id_voucher-project_code').find(':selected').attr('project-type');
    if (project_type == "Secondary" && $('#is_project_wise_pl').val()=='true' && $("#id_voucher-id").val() == ''){
        if ($("#id_voucher-type_id option:selected").text() == "Bank" || $("#id_voucher-type_id option:selected").text() == "Cash"){
//        Temporary Fix
//            if ($('#id_dr_total_amount').val()>0 && parseFloat($('#expense').text()) >= parseFloat($('#budgetAmount').text())) {
//                 validation_message = validation_message + "* Budget Amount Exceeds. \n";
//                 is_valid = false;
//            }

            if (parseFloat($('#cash_allocated').text()) == 0 || (parseFloat($('#cashflow_amount').text()) <  parseFloat($('#cash_allocated').text()))){
                if ($('#id_dr_total_amount').val()>0) {
                     validation_message = validation_message + "* Actual Cash Flow Amount Exceeds. \n";
                     is_valid = false;
                     }
            }
        }
    }
	// Check The Voucher Type
    if ($('#id_dr_total_amount').val() != $('#id_cr_total_amount').val()){
        if ($("#id_voucher-type_id option:selected").text() == "Bank" || $("#id_voucher-type_id option:selected").text() == "Cash"){
            autoGenerateLedgerForm();
        }
        else{
            validation_message = validation_message + "* Debit and Credit Values are not Equal. \n";
            is_valid = false;
        }
    }


    if(!is_valid){
        swal("Voucher Form errors", validation_message, "error");
        $('#id_voucher-status').val($('#id_voucher-original_status').val());
    }
    return is_valid;
}

function highLightRow(){
    var table = document.getElementById("bill_list");
    var rowCount = table.rows.length;
    if (rowCount > 1) {
        for (var i = 0; i < rowCount-2; i++) {
            if ($('#id_ledger_bill-'+i+'-credit_settlement').val() > 0 || $('#id_ledger_bill-'+i+'-debit_settlement').val() > 0) {
                $('#id_ledger_bill-'+i+'-credit_settlement').closest('tr').css('background', "rgba( 75, 181, 67,0.2)");
            }else{
                $('#id_ledger_bill-'+i+'-credit_settlement').closest('tr').css('background', "");
            }
        }
    }
}

function BankVoucherCheck() {
	if($("#id_dr_total_amount").val() != $("#id_cr_total_amount").val() && $("#id_voucher-type_id").val() == 3 && ($("#id_bank_ledger").val() == "" || $("#id_bank_ledger").val() == null || $("#id_bank_ledger").val() == "null")) {
		swal({
			title: "Bank Ledger is empty.",
			text: "Please select a bank ledger and save to complete this voucher.",
			type: "warning"
		}, function(){
			$("#enable_edit_voucher").trigger("click");
			setTimeout(function(){
				$("#id_bank_ledger").trigger("chosen:open");
			},600);
		});
	}
}

function CashVoucherCheck() {
	if($("#id_dr_total_amount").val() != $("#id_cr_total_amount").val() && $("#id_voucher-type_id").val() == 2 && ($("#id_cash_ledger").val() == "" || $("#id_cash_ledger").val() == null || $("#id_cash_ledger").val() == "null")) {
		swal({
			title: "Cash Ledger is empty.",
			text: "Please select a cash ledger and save to complete this voucher.",
			type: "warning"
		}, function(){
			$("#enable_edit_voucher").trigger("click");
			setTimeout(function(){
				$("#id_cash_ledger").trigger("chosen:open");
			},600);
		});
	}
}