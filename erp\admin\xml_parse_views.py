import json
import random
import string
from datetime import datetime
from decimal import Decimal
from xml.etree.ElementTree import tostring

import pymysql
import simplejson
import xmltodict
from sqlalchemy import func, Integer, and_

from erp import APPROVED_VOUCHER, APPROVED_INVOICE
from erp.accounts import SALES_ACCOUNT_GROUP_ID, PURCHASE_ACCOUNT_GROUP_ID, SUNDRY_DEBTORS_GROUP_ID, \
	SUNDRY_CREDITORS_GROUP_ID, GENERAL_VOUCHER, BANK_VOUCHER, CASH_VOUCHER, PURCHASE_VOUCHER, SALES_VOUCHER
from erp.admin import logger, account_group_map, account_ledger_map, accounts_voucher_type_dict, default_voucher_type, \
	tax_code_dict, ledger_voucher_type_dict, tax_percentage_dict, indirect_expenses_dict
from erp.admin.import_backend import TallyService
from erp.dao import DataAccessObject
from erp.helper import getAccountGroupIDs, getUser
from erp.masters import PARTY_IS_SUPPLIER, PARTY_IS_CUSTOMER, PARTY_IS_BOTH
from erp.masters.backend import createPartyLedgers, MasterService
from erp.models import UnitMaster, Currency, Material, Ledger, AccountGroup, Voucher, VoucherParticulars, LedgerBill, \
	LedgerBillSettlement, ImportDocuments, Category, ImportMessage, Party, Invoice, InvoiceMaterial, \
	Receipt, ReceiptMaterial, InvoiceTax, ReceiptTax, PartyLedgerMap, \
	TaxLedgerMap, PurchaseOrder, PurchaseOrderMaterial, PurchaseOrderTax, OA, \
	OAParticulars, Attachment, Project
from settings import HOST, USER, DBNAME, PORT, PASSWORD
from util.helper import getFinancialYear

__author__ = 'saravanan'


class ImportDAO(DataAccessObject):

	def __init__(self):
		super(ImportDAO, self).__init__()

	def getLatestVoucherNo(self, enterprise=None, voucher_type=1):
		"""

		:param enterprise:
		:param voucher_type:
		:return:
		"""
		current_fy = getFinancialYear(for_date=datetime.now(), fy_start_day=enterprise.fy_start_day)
		latest_voucher = self.db_session.query(Voucher.voucher_no).filter(
			Voucher.financial_year == current_fy, Voucher.type_id == voucher_type,
			Voucher.enterprise_id == enterprise.id, Voucher.voucher_no != '0').order_by(
			Voucher.voucher_no.desc()).first()
		if latest_voucher:
			latest_voucher_no = int(latest_voucher[0])
		else:
			latest_voucher_no = 0
		return latest_voucher_no

	def getSalesLedger(self, enterprise_id=None):
		sales_ledger = None
		try:
			sales_ledger = self.db_session.query(Ledger).filter(
				Ledger.group_id == SALES_ACCOUNT_GROUP_ID, Ledger.enterprise_id == enterprise_id).order_by(
				Ledger.id).first()
		except Exception as e:
			logger.exception(e)
		return sales_ledger

	def getPurchaseLedger(self, enterprise_id=None):
		purchase_ledger = None
		try:
			purchase_ledger = self.db_session.query(Ledger).filter(
				Ledger.group_id == PURCHASE_ACCOUNT_GROUP_ID, Ledger.enterprise_id == enterprise_id).order_by(
				Ledger.id).first()
		except Exception as e:
			logger.exception(e)
		return purchase_ledger

	@staticmethod
	def getBankGroupIds(enterprise_id=None):
		bank_group_ids = None
		try:
			bank_group_ids = getAccountGroupIDs(enterprise_id=enterprise_id, names=[
				AccountGroup.BANK_GROUP_NAME, AccountGroup.LT_BORROWINGS_GROUP_NAME,
				AccountGroup.ST_BORROWINGS_GROUP_NAME])
		except Exception as e:
			logger.exception(e)
		return bank_group_ids

	def getMaterialDict(self, enterprise_id=None):
		material_dict = {}
		try:
			materials = self.db_session.query(Material).filter_by(enterprise_id=enterprise_id).all()
			if materials:
				for material in materials:
					material_dict.update({material.name: material.material_id})
		except Exception as e:
			logger.exception(e)
		return material_dict

	def getVoucherDict(self, enterprise_id=None):
		voucher_dict = {}
		try:
			vouchers = self.db_session.query(
				func.substring_index(func.substring_index(Voucher.narration, "TALLY_ID:", -1), "}", 1).label(
					'voucher_details'), Voucher.id.label('voucher_id')).outerjoin(Voucher.type).filter(
				Voucher.enterprise_id == enterprise_id).all()
			if vouchers:
				for voucher in vouchers:
					voucher_dict.update({voucher.voucher_details: voucher.voucher_id})
		except Exception as e:
			logger.exception(e)
		return voucher_dict

	def getInvoiceDict(self, enterprise_id=None):
		invoice_dict = {}
		try:
			invoices = self.db_session.query(
				func.substring_index(func.substring_index(Invoice.remarks, "TALLY_ID:", -1), "}", 1).label(
					'invoice_details'), Invoice.id.label('invoice_id')).filter(
				Invoice.enterprise_id == enterprise_id).all()
			if invoices:
				for invoice in invoices:
					invoice_dict.update({invoice.invoice_details: invoice.invoice_id})
		except Exception as e:
			logger.exception(e)
		return invoice_dict

	def getReceiptDict(self, enterprise_id=None):
		receipt_dict = {}
		try:
			receipts = self.db_session.query(
				func.substring_index(func.substring_index(Receipt.remarks, "TALLY_ID:", -1), "}", 1).label(
					'receipt_details'), Receipt.receipt_no.label('receipt_id')).filter(
				Receipt.enterprise_id == enterprise_id).all()
			if receipts:
				for receipt in receipts:
					receipt_dict.update({receipt.receipt_details: receipt.receipt_id})
		except Exception as e:
			logger.exception(e)
		return receipt_dict

	def getPurchaseOrderDict(self, enterprise_id=None):
		po_dict = {}
		try:
			purchase_orders = self.db_session.query(
				func.substring_index(func.substring_index(PurchaseOrder.remarks, "TALLY_ID:", -1), "}", 1).label(
					'po_details'), PurchaseOrder.po_id.label('po_id')).filter(
				PurchaseOrder.enterprise_id == enterprise_id).all()
			if purchase_orders:
				for po in purchase_orders:
					po_dict.update({po.po_details: po.po_id})
		except Exception as e:
			logger.exception(e)
		return po_dict

	def getPurchaseOrderNoDict(self, enterprise_id=None):
		po_no_dict = {}
		try:
			purchase_orders = self.db_session.query(
				PurchaseOrder.purpose.label('po_no'), PurchaseOrder.po_id.label('po_id'), PurchaseOrder.type.label('type')).filter(
				PurchaseOrder.enterprise_id == enterprise_id).all()
			if purchase_orders:
				for po in purchase_orders:
					po_no_dict.update({po.po_no: {"po_id": po.po_id, "po_type": po.type}})
		except Exception as e:
			logger.exception(e)
		return po_no_dict

	def getOrderAcknowledgeDict(self, enterprise_id=None):
		oa_dict = {}
		try:
			order_acknowledges = self.db_session.query(
				func.substring_index(func.substring_index(OA.remarks, "TALLY_ID:", -1), "}", 1).label(
					'oa_details'), OA.id.label('oa_id')).filter(
				OA.enterprise_id == enterprise_id).all()
			if order_acknowledges:
				for oa in order_acknowledges:
					oa_dict.update({oa.oa_details: oa.oa_id})
		except Exception as e:
			logger.exception(e)
		return oa_dict

	def getOrderAcknowledgeNoDict(self, enterprise_id=None):
		oa_no_dict = {}
		try:
			order_acknowledges = self.db_session.query(OA.special_instructions.label(
					'oa_no'), OA.id.label('oa_id')).filter(
				OA.enterprise_id == enterprise_id).all()
			if order_acknowledges:
				for oa in order_acknowledges:
					oa_no_dict.update({oa.oa_no: oa.oa_id})
		except Exception as e:
			logger.exception(e)
		return oa_no_dict

	def getDCNoDict(self, enterprise_id=None):
		dc_no_dict = {}
		try:
			dcs = self.db_session.query(Invoice.special_instruction.label(
				'dc_no'), Invoice.id.label('dc_id')).filter(
				Invoice.enterprise_id == enterprise_id, Invoice.type == 'DC').all()
			if dcs:
				for dc in dcs:
					dc_no_dict.update({dc.dc_no: dc.dc_id})
		except Exception as e:
			logger.exception(e)
		return dc_no_dict

	def getReceiptNoteNoDict(self, enterprise_id=None):
		receipt_note_no_dict = {}
		try:
			dcs = self.db_session.query(Receipt.rejection_remarks.label(
				'dc_no'), Receipt.receipt_no.label('dc_id')).filter(
				Receipt.enterprise_id == enterprise_id).all()
			if dcs:
				for dc in dcs:
					receipt_note_no_dict.update({dc.dc_no: dc.dc_id})
		except Exception as e:
			logger.exception(e)
		return receipt_note_no_dict

	def getLedgerDict(self, enterprise_id=None):
		all_ledger_dict = {}
		try:
			ledgers = self.db_session.query(
						Ledger.id.label('id'), Ledger.name.label('name'),
						AccountGroup.name.label('group_name')).join(
				Ledger.group).filter(Ledger.enterprise_id == enterprise_id).all()
			if ledgers:
				for ledger in ledgers:
					if ledger.name in all_ledger_dict:
						all_ledger_dict[ledger.name].update({ledger.group_name: ledger.id})
					else:
						all_ledger_dict.update({ledger.name: {ledger.group_name: ledger.id}})

		except Exception as e:
			logger.exception(e)
		return all_ledger_dict

	def getPartyLedgerDict(self, enterprise_id=None):
		all_party_ledger_dict = {}
		try:
			ledger_group_id = (SUNDRY_DEBTORS_GROUP_ID, SUNDRY_CREDITORS_GROUP_ID)
			ledgers = self.db_session.query(
				Ledger.id.label('id'), Ledger.name.label('name'), AccountGroup.name.label('group_name')).join(
				Ledger.group).filter(Ledger.enterprise_id == enterprise_id, Ledger.group_id.in_(ledger_group_id)).all()

			if ledgers:
				for ledger in ledgers:
					if ledger.name in all_party_ledger_dict:
						all_party_ledger_dict[ledger.name].update({ledger.group_name: ledger.id})
					else:
						all_party_ledger_dict.update({ledger.name: {ledger.group_name: ledger.id}})
		except Exception as e:
			logger.exception(e)
		return all_party_ledger_dict

	def getPartyDict(self, enterprise_id=None):
		all_party_dict = {}
		try:
			parties = self.db_session.query(Party).filter(Party.enterprise_id == enterprise_id).all()
			if parties:
				for party in parties:
					all_party_dict.update({party.name: {'id': party.id, 'con_flags': int(party.config_flags)}})
		except Exception as e:
			logger.exception(e)
		return all_party_dict

	def getVoucherNumberDict(self, enterprise=None):
		fy_latest_voucher_nos = {}
		try:
			financial_years_list = self.db_session.query(Voucher.financial_year).filter(
				Voucher.enterprise_id == enterprise.id).group_by(Voucher.financial_year).all()
			if financial_years_list:
				for current_fy in financial_years_list:
					for voucher_type in range(5):
						voucher_type = voucher_type + 1
						latest_voucher = self.db_session.query(Voucher.voucher_no).filter(
							Voucher.financial_year == current_fy.financial_year, Voucher.type_id == voucher_type,
							Voucher.enterprise_id == enterprise.id, Voucher.voucher_no != '0').order_by(
							Voucher.voucher_no.desc()).first()
						if latest_voucher:
							latest_voucher_no = int(latest_voucher[0])
						else:
							latest_voucher_no = 0

						if current_fy.financial_year in fy_latest_voucher_nos:
							fy_latest_voucher_nos[current_fy.financial_year].update({voucher_type: latest_voucher_no})
						else:
							fy_latest_voucher_nos.update({current_fy.financial_year: {voucher_type: latest_voucher_no}})

		except Exception as e:
			logger.exception(e)
		return fy_latest_voucher_nos

	def getInvoiceNumberDict(self, enterprise=None, invoice_type=None):
		invoice_no_dict = {}
		try:
			financial_years_list = self.db_session.query(Invoice.financial_year).filter(
				Invoice.enterprise_id == enterprise.id).group_by(Invoice.financial_year).all()
			if financial_years_list:
				for current_fy in financial_years_list:
					latest_invoice_no = self.db_session.query(func.max(Invoice.invoice_no.cast(Integer))).filter(
						Invoice.financial_year == current_fy.financial_year, Invoice.enterprise_id == enterprise.id,
						Invoice.id != '0', Invoice.type == invoice_type).first()
					new_invoice_no = 0
					if latest_invoice_no and latest_invoice_no[0] is not None:
						new_invoice_no = int(latest_invoice_no[0])
					invoice_no_dict.update({current_fy.financial_year: new_invoice_no})

		except Exception as e:
			logger.exception(e)
		return invoice_no_dict

	def getReceiptNumberDict(self, enterprise=None):
		receipt_no_dict = {}
		try:
			financial_years_list = self.db_session.query(Receipt.financial_year).filter(
				Receipt.enterprise_id == enterprise.id).group_by(Receipt.financial_year).all()
			if financial_years_list:
				for current_fy in financial_years_list:
					latest_receipt_no = self.db_session.query(func.max(Receipt.receipt_code.cast(Integer))).filter(
						Receipt.financial_year == current_fy.financial_year, Receipt.enterprise_id == enterprise.id,
						Receipt.receipt_no != '0').first()
					new_receipt_no = 0
					if latest_receipt_no and latest_receipt_no[0] is not None:
						new_receipt_no = int(latest_receipt_no[0])
					receipt_no_dict.update({current_fy.financial_year: new_receipt_no})

		except Exception as e:
			logger.exception(e)
		return receipt_no_dict

	def getPurchaseOrderNumberDict(self, enterprise=None, po_type=None):
		purchase_order_no_dict = {}
		try:
			financial_years_list = self.db_session.query(PurchaseOrder.financial_year).filter(
				PurchaseOrder.enterprise_id == enterprise.id).group_by(PurchaseOrder.financial_year).all()
			if financial_years_list:
				for current_fy in financial_years_list:
					latest_purchase_order_no = self.db_session.query(func.max(PurchaseOrder.po_no.cast(Integer))).filter(
						PurchaseOrder.financial_year == current_fy.financial_year, PurchaseOrder.enterprise_id == enterprise.id,
						PurchaseOrder.po_no != '0', PurchaseOrder.type == po_type).first()
					new_purchase_order_no = 0
					if latest_purchase_order_no and latest_purchase_order_no[0] is not None:
						new_purchase_order_no = int(latest_purchase_order_no[0])
					purchase_order_no_dict.update({current_fy.financial_year: new_purchase_order_no})

		except Exception as e:
			logger.exception(e)
		return purchase_order_no_dict

	def getOrderAcknowledgementNumberDict(self, enterprise=None, oa_type=None):
		order_acknowledgement_number_dict = {}
		try:
			financial_years_list = self.db_session.query(OA.financial_year).filter(
				OA.enterprise_id == enterprise.id).group_by(OA.financial_year).all()
			if financial_years_list:
				for current_fy in financial_years_list:
					latest_order_acknowledgement_no = self.db_session.query(func.max(OA.oa_no.cast(Integer))).filter(
						OA.financial_year == current_fy.financial_year, OA.enterprise_id == enterprise.id,
						OA.oa_no != '0', OA.type == oa_type).first()
					new_order_acknowledgement_no = 0
					if latest_order_acknowledgement_no and latest_order_acknowledgement_no[0] is not None:
						new_order_acknowledgement_no = int(latest_order_acknowledgement_no[0])
					order_acknowledgement_number_dict.update({current_fy.financial_year: new_order_acknowledgement_no})

		except Exception as e:
			logger.exception(e)
		return order_acknowledgement_number_dict


class ImportService:

	def __init__(self, enterprise=None):
		self.dao = ImportDAO()
		self.voucher_no_dict = self.dao.getVoucherNumberDict(enterprise=enterprise)
		self.receipt_no_dict = self.dao.getReceiptNumberDict(enterprise=enterprise)
		self.invoice_no_dict = self.dao.getInvoiceNumberDict(enterprise=enterprise, invoice_type='GST')
		self.all_ledger_dict = self.dao.getLedgerDict(enterprise_id=enterprise.id)
		self.all_party_ledger_dict = self.dao.getPartyLedgerDict(enterprise_id=enterprise.id)
		self.all_party_dict = self.dao.getPartyDict(enterprise_id=enterprise.id)
		self.po_no_dict = self.dao.getPurchaseOrderNoDict(enterprise_id=enterprise.id)
		self.oa_no_dict = self.dao.getOrderAcknowledgeNoDict(enterprise_id=enterprise.id)
		self.dc_no_dict = self.dao.getDCNoDict(enterprise_id=enterprise.id)
		self.receipt_note_no_dict = self.dao.getReceiptNoteNoDict(enterprise_id=enterprise.id)
		self.po_dict = self.dao.getPurchaseOrderDict(enterprise_id=enterprise.id)
		self.oa_dict = self.dao.getOrderAcknowledgeDict(enterprise_id=enterprise.id)
		self.voucher_dict = self.dao.getVoucherDict(enterprise_id=enterprise.id)
		self.receipt_dict = self.dao.getReceiptDict(enterprise_id=enterprise.id)
		self.invoice_dict = self.dao.getInvoiceDict(enterprise_id=enterprise.id)
		self.material_dict = self.dao.getMaterialDict(enterprise_id=enterprise.id)
		self.sales_ledger = self.dao.getSalesLedger(enterprise_id=enterprise.id)
		self.purchase_ledger = self.dao.getPurchaseLedger(enterprise_id=enterprise.id)
		self.bank_group_ids = self.dao.getBankGroupIds(enterprise_id=enterprise.id)
		self.delivery_no_dict = self.dao.getInvoiceNumberDict(enterprise=enterprise, invoice_type='DC')
		self.issue_no_dict = self.dao.getInvoiceNumberDict(enterprise=enterprise, invoice_type='Issue')
		self.purchase_order_no_dict = self.dao.getPurchaseOrderNumberDict(enterprise=enterprise, po_type=0)
		self.job_order_no_dict = self.dao.getPurchaseOrderNumberDict(enterprise=enterprise, po_type=1)
		self.order_acknowledgement_number_dict = self.dao.getOrderAcknowledgementNumberDict(enterprise=enterprise, oa_type='Sale')
		self.job_order_number_dict = self.dao.getOrderAcknowledgementNumberDict(enterprise=enterprise, oa_type='Job')
		self.invoice_count = 0
		self.dc_count = 0
		self.issue_count = 0
		self.receipt_count = 0
		self.update_invoice_count = 0
		self.update_dc_count = 0
		self.update_issue_count = 0
		self.update_receipt_count = 0
		self.po_count = 0
		self.update_po_count = 0
		self.oa_count = 0
		self.update_oa_count = 0
		self.voucher_count = 0
		self.update_voucher_count = 0

	def refreshMasters(self, enterprise=None):
		"""

		:return:
		"""
		self.voucher_no_dict = self.dao.getVoucherNumberDict(enterprise=enterprise)
		self.receipt_no_dict = self.dao.getReceiptNumberDict(enterprise=enterprise)
		self.invoice_no_dict = self.dao.getInvoiceNumberDict(enterprise=enterprise, invoice_type='GST')
		self.all_ledger_dict = self.dao.getLedgerDict(enterprise_id=enterprise.id)
		self.all_party_ledger_dict = self.dao.getPartyLedgerDict(enterprise_id=enterprise.id)
		self.all_party_dict = self.dao.getPartyDict(enterprise_id=enterprise.id)
		self.po_no_dict = self.dao.getPurchaseOrderNoDict(enterprise_id=enterprise.id)
		self.oa_no_dict = self.dao.getOrderAcknowledgeNoDict(enterprise_id=enterprise.id)
		self.dc_no_dict = self.dao.getDCNoDict(enterprise_id=enterprise.id)
		self.receipt_note_no_dict = self.dao.getReceiptNoteNoDict(enterprise_id=enterprise.id)
		self.po_dict = self.dao.getPurchaseOrderDict(enterprise_id=enterprise.id)
		self.oa_dict = self.dao.getOrderAcknowledgeDict(enterprise_id=enterprise.id)
		self.voucher_dict = self.dao.getVoucherDict(enterprise_id=enterprise.id)
		self.receipt_dict = self.dao.getReceiptDict(enterprise_id=enterprise.id)
		self.invoice_dict = self.dao.getInvoiceDict(enterprise_id=enterprise.id)
		self.material_dict = self.dao.getMaterialDict(enterprise_id=enterprise.id)
		self.sales_ledger = self.dao.getSalesLedger(enterprise_id=enterprise.id)
		self.purchase_ledger = self.dao.getPurchaseLedger(enterprise_id=enterprise.id)
		self.bank_group_ids = self.dao.getBankGroupIds(enterprise_id=enterprise.id)
		self.delivery_no_dict = self.dao.getInvoiceNumberDict(enterprise=enterprise, invoice_type='DC')
		self.issue_no_dict = self.dao.getInvoiceNumberDict(enterprise=enterprise, invoice_type='Issue')
		self.purchase_order_no_dict = self.dao.getPurchaseOrderNumberDict(enterprise=enterprise, po_type=0)
		self.job_order_no_dict = self.dao.getPurchaseOrderNumberDict(enterprise=enterprise, po_type=1)
		self.order_acknowledgement_number_dict = self.dao.getOrderAcknowledgementNumberDict(enterprise=enterprise, oa_type='Sale')
		self.job_order_number_dict = self.dao.getOrderAcknowledgementNumberDict(enterprise=enterprise, oa_type='Job')

	def getLedgerId(self, ledger_name=None, is_party_ledger=None, voucher_type=None, enterprise_id=None, user_id=None):
		try:
			if ledger_name in account_ledger_map:
				ledger_name = account_ledger_map.get(ledger_name, "None")
			else:
				ledger_name = ledger_name
			if is_party_ledger and voucher_type in ledger_voucher_type_dict:
				group_name = ""
				group_ids = ""
				if voucher_type == "Payment" or voucher_type == "Purchase":
					group_name = "Sundry Creditors"
					group_ids = getAccountGroupIDs(
						enterprise_id=enterprise_id, names=[AccountGroup.SUNDRY_CREDITOR_GROUP_NAME], deep=True)
				elif voucher_type == "Receipt" or voucher_type == "Sales":
					group_name = "Sundry Debtors"
					group_ids = getAccountGroupIDs(
						enterprise_id=enterprise_id, names=[AccountGroup.SUNDRY_DEBTOR_GROUP_NAME], deep=True)
				ledger_id = ""
				if ledger_name in self.all_ledger_dict:
					if group_name in self.all_ledger_dict[ledger_name]:
						ledger_id = self.all_ledger_dict[ledger_name][group_name]
					else:
						ledger = self.dao.db_session.query(
							Ledger.id.label('id'), Ledger.name.label('name'),
							AccountGroup.name.label('group_name')).join(Ledger.group).filter(
							Ledger.enterprise_id == enterprise_id, Ledger.name == ledger_name,
							Ledger.group_id.in_(group_ids)).first()
						if ledger:
							ledger_id = ledger.id
						if not ledger_id:
							try:
								self.dao.db_session.begin(subtransactions=True)
								ledger = Ledger(
									group_id=SUNDRY_CREDITORS_GROUP_ID if group_name == "Sundry Creditors" else SUNDRY_DEBTORS_GROUP_ID,
									name=ledger_name, enterprise_id=enterprise_id, description='%s (%s)' % (
										ledger_name, "Customer" if group_name == 'Sundry Debtors' else "Supplier"), created_by=user_id)
								self.dao.db_session.add(ledger)
								self.dao.db_session.commit()
							except Exception as e:
								self.dao.db_session.rollback()
								raise e
							ledger_id = ledger.id
							if ledger_name in self.all_ledger_dict:
								self.all_ledger_dict[ledger_name].update({group_name: ledger.id})
							else:
								self.all_ledger_dict.update({ledger_name: {group_name: ledger.id}})

							party = self.dao.db_session.query(Party).filter(
									Party.name == ledger_name, Party.enterprise_id == enterprise_id).first()
							if party:
								try:
									self.dao.db_session.begin(subtransactions=True)
									flags = 2 if group_name == "Sundry Creditors" else 4
									party.config_flags = int(party.config_flags) + int(flags)
									self.dao.db_session.commit()
								except Exception as e:
									self.dao.db_session.rollback()
									raise e
								self.all_party_dict.update({party.name: {'id': party.id, 'con_flags': party.config_flags}})
								PartyLedgerMap(
									party_id=party.id, is_supplier=True if group_name == "Sundry Creditors" else False,
									enterprise_id=party.enterprise_id, ledger_id=ledger_id)

			else:
				ledger_id = self.all_ledger_dict[ledger_name].values()[0]
			return ledger_id
		except Exception as e:
			logger.exception(e)
		return None

	def insertVoucherParticulars(
			self, enterprise_id=None, voucher=None, particular=None, item_no=None, ledger_dict=None, voucher_type=None,
			is_party_ledger=None, user_id=None):

		ledger_id = self.getLedgerId(
			ledger_name=particular['LEDGERNAME'], is_party_ledger=is_party_ledger, voucher_type=voucher_type,
			enterprise_id=enterprise_id, user_id=user_id)

		if ledger_id:
			voucher_particular = VoucherParticulars(
				voucher_id=voucher.id, item_no=item_no, ledger_id=ledger_id, enterprise_id=enterprise_id,
				amount=abs(Decimal(particular['AMOUNT']).quantize(Decimal('1.00'))) if 'AMOUNT' in particular else 0,
				is_debit=1 if particular['ISDEEMEDPOSITIVE'] == "Yes" else 0)

		else:
			logger.debug("Voucher Particulars Ledger id Not Available:%s" % ledger_dict)
			return None
		return voucher_particular

	def readImportedFiles(self, enterprise_id=None):
		try:
			files = self.dao.db_session.query(Attachment.file).outerjoin(
				ImportDocuments, and_(
					ImportDocuments.attachment_id == Attachment.attachment_id,
					ImportDocuments.enterprise_id == Attachment.enterprise_id)
			).filter(
				ImportDocuments.enterprise_id == enterprise_id,
				ImportDocuments.process == 0).all()
			return files
		except Exception as e:
			logger.exception("Insert Ledger Bill data %s" % e.message)
		return None

	@staticmethod
	def getVoucherTypeDict(my_dict=None):
		try:
			for _dict in my_dict:
				output_dict = json.loads(json.dumps(xmltodict.parse(tostring(_dict))))
				tally_voucher_type = output_dict['VOUCHERTYPE']
				if tally_voucher_type['ISOPTIONAL'] == 'No':
					if tally_voucher_type['@RESERVEDNAME']:
						default_voucher_type.update({tally_voucher_type['@NAME']: tally_voucher_type['@RESERVEDNAME']})
					else:
						parent_name = default_voucher_type.get(tally_voucher_type['PARENT'], "None")
						default_voucher_type.update({tally_voucher_type['@NAME']: parent_name})

		except Exception as e:
			logger.exception(e)
		return default_voucher_type

	@staticmethod
	def insertLedgerBill(
			enterprise_id=None, bill_no=None, amount=None, ledger_id=None, voucher_id=None, db_session=None,
			bill_type=None, bill_date=None):
		try:
			ledger_bill = db_session.query(LedgerBill).filter(
				LedgerBill.ledger_id == ledger_id, LedgerBill.bill_no == bill_no, LedgerBill.enterprise_id == enterprise_id).first()
			if ledger_bill is None:
				ledger_bill = LedgerBill(
					bill_no=bill_no, ledger_id=ledger_id, voucher_id=voucher_id,
					enterprise_id=enterprise_id, bill_date=bill_date,
					net_value=abs(Decimal(amount)), is_debit=(Decimal(amount) < 0))
			dr_value = 0
			cr_value = 0
			is_debit = (Decimal(amount) < 0)
			if is_debit == 1:
				dr_value = abs(Decimal(amount))
			else:
				cr_value = abs(Decimal(amount))
			if bill_type == 'Agst Ref':
				try:
					db_session.begin(subtransactions=True)
					ledger_bill_settlement = TallyService().constructLedgerBillSettlement(
						enterprise_id=enterprise_id,
						ledger_bill=ledger_bill,
						voucher_id=voucher_id,
						dr_value=Decimal(dr_value).quantize(Decimal('1.00')),
						cr_value=Decimal(cr_value).quantize(Decimal('1.00')),
						db_session=db_session)
					db_session.add(ledger_bill_settlement)
					db_session.commit()
				except Exception as e:
					db_session.rollback()
					raise e
			else:
				ledger_bill_settlement = TallyService().constructLedgerBillSettlement(
					enterprise_id=enterprise_id,
					ledger_bill=ledger_bill,
					voucher_id=voucher_id,
					dr_value=Decimal(dr_value).quantize(Decimal('1.00')),
					cr_value=Decimal(cr_value).quantize(Decimal('1.00')),
					db_session=db_session)
				db_session.add(ledger_bill_settlement)

		except Exception as e:
			logger.exception("Insert Ledger Bill data %s" % e.message)
			raise e

	def insertOpeningLedgerBill(
			self, enterprise_id=None, bill_no=None, bill_date=None, amount=None, ledger_id=None, voucher_id=None):
		try:
			self.dao.db_session.begin(subtransactions=True)
			ledger_bill = self.dao.db_session.query(LedgerBill).filter(
				LedgerBill.bill_no == bill_no, LedgerBill.ledger_id == ledger_id,
				LedgerBill.bill_date == datetime.strptime(bill_date, "%Y%m%d").strftime("%Y-%m-%d")).first()
			if not ledger_bill:
				ledger_bill = LedgerBill(
					bill_no=bill_no, ledger_id=ledger_id, voucher_id=voucher_id, enterprise_id=enterprise_id,
					net_value=abs(float(amount)), is_debit=(float(amount) < 0))
				ledger_bill.bill_date = datetime.strptime(bill_date, "%Y%m%d").strftime("%Y-%m-%d")
				dr_value = 0
				cr_value = 0
				if ledger_bill.is_debit == 1:
					dr_value = ledger_bill.net_value
				else:
					cr_value = ledger_bill.net_value
				ledger_bill_settlement = LedgerBillSettlement(
					voucher_id=voucher_id, dr_value=dr_value, cr_value=cr_value, enterprise_id=enterprise_id)
				ledger_bill_settlement.bill = ledger_bill
				self.dao.db_session.add(ledger_bill_settlement)
				self.dao.db_session.commit()
		except Exception as e:
			self.dao.db_session.rollback()
			logger.exception("Exception occurred while inserting Ledger opening  Bill%s" % e.message)
			raise e

	@staticmethod
	def insertParty(
			enterprise=None, ledger_name=None, parent_name=None, db_session=None, party_det=None, user_id=None):
		try:
			party = db_session.query(Party).filter(
				Party.name == ledger_name, Party.enterprise_id == enterprise.id).first()
			if party:
				if party.config_flags == PARTY_IS_SUPPLIER and parent_name == 'Sundry Creditors':  # Supplier
					party.config_flags = PARTY_IS_BOTH
				if party.config_flags == PARTY_IS_CUSTOMER and parent_name == 'Sundry Debtors':  # Customer
					party.config_flags = PARTY_IS_BOTH
			else:
				if parent_name == 'Sundry Debtors':
					config_flags = 4
				else:
					config_flags = 2
				try:
					db_session.begin(subtransactions=True)
					master_service = MasterService()
					code = ''.join(random.choice(string.ascii_uppercase + string.digits) for _ in range(6))
					gst_no = party_det['gst'] if party_det['gst'] else ""
					category_id = 3
					if gst_no != "":
						category_id = 1
					party = Party(
						code=code, name=ledger_name, enterprise_id=enterprise.id, currency=enterprise.home_currency_id,
						config_flags=config_flags, address_1=party_det['address'] if party_det['address'] else "",
						state=party_det['state_name'] if party_det['state_name'] else "",
						payment_credit_days=party_det['credit_period'] if parent_name == 'Sundry Creditors' else 0,
						receipt_credit_days=party_det['credit_period'] if parent_name == 'Sundry Debtors' else 0, category_id=category_id)
					db_session.add(party)
					db_session.commit()
					db_session.refresh(party)
					party_reg_detail = simplejson.loads(simplejson.dumps([
						{"label_id": 1, "details": gst_no, "label": "GSTIN", "is_deleted": 0},
						{"label_id": 2, "details": "", "label": "PAN", "is_deleted": 0}]))
					if party_det['cst'] and party_det['cst'] != "":
						party_reg_detail.append(simplejson.loads(simplejson.dumps(
							{"label_id": 3, "details": party_det['cst'], "label": "CSTNO", "is_deleted": 0})))
					master_service.savePartyRegistration(
						enterprise_id=enterprise.id, party_id=party.id, party_reg_detail=party_reg_detail, user_id=user_id)
				except Exception as e:
					db_session.rollback()
					raise e
			return party.id
		except Exception as e:
			logger.exception(e.message)
			raise e

	def importUnits(self, enterprise_id=None, units_dict=None, user=None):
		try:
			unit_list = set()
			self.dao.db_session.begin(subtransactions=True)
			new_unit_id = 0
			max_unit_id = self.dao.db_session.query(func.max(UnitMaster.unit_id)).filter_by(
				enterprise_id=enterprise_id).first()
			if max_unit_id and max_unit_id[0] is not None:
				new_unit_id = int(max_unit_id[0])
			unit_count = 0
			for _dict in units_dict:
				output_dict = json.loads(json.dumps(xmltodict.parse(tostring(_dict))))
				tally_unit = output_dict['UNIT']
				unit = self.dao.db_session.query(UnitMaster).filter_by(
					unit_name=tally_unit['NAME'], enterprise_id=enterprise_id).first()
				if unit:
					unit.unit_id = unit.unit_id
				else:
					unit_list.add((tally_unit['NAME'], tally_unit['NAME']))
			for item in list(unit_list):
				unit = UnitMaster()
				new_unit_id += 1
				unit.unit_id = new_unit_id
				unit_count = int(unit_count) + 1
				unit.enterprise_id = enterprise_id
				unit.unit_name = item[0]
				unit.unit_description = item[1]
				self.dao.db_session.add(unit)
			if len(unit_list) > 0:
				tallyxmlmessage = ImportMessage(
					message=str(len(unit_list)) + " Units Imported \n", create_on=datetime.now(), created_by=user,
					enterprise_id=enterprise_id, is_process=False)
				self.dao.db_session.add(tallyxmlmessage)
			logger.debug("Unit Dirty: %s" % self.dao.db_session.dirty)
			self.dao.db_session.commit()
			return True

		except Exception as e:
			self.dao.db_session.rollback()
			logger.exception("Import Unit data %s" % e.message)
			raise e

	def importCategory(self, enterprise_id=None, material_dict=None, user=None):
		try:
			category_list = set()
			self.dao.db_session.begin(subtransactions=True)
			category_dict = {}
			categories = self.dao.db_session.query(Category).filter_by(enterprise_id=enterprise_id).all()
			if categories:
				for category in categories:
					category_dict.update({category.name: {'category_id': category.id, 'name': category.name}})
			category_count = 0
			for _dict in material_dict:
				output_dict = json.loads(json.dumps(xmltodict.parse(tostring(_dict))))
				tally_material = output_dict['STOCKITEM']
				if 'CATEGORY' in tally_material:
					if tally_material['CATEGORY'] is not None:
						if str(tally_material['CATEGORY']).upper() not in str(category_dict).upper():
							category_list.add((tally_material['CATEGORY']))
			for item in category_list:
				category = Category()
				category_count = int(category_count) + 1
				category.enterprise_id = enterprise_id
				category.name = item
				self.dao.db_session.add(category)
			if len(category_list) > 0:
				tally_xml_message = ImportMessage(
					message=str(len(category_list)) + " Category Imported \n", create_on=datetime.now(), created_by=user,
					enterprise_id=enterprise_id, is_process=False)
				self.dao.db_session.add(tally_xml_message)
			logger.debug("Category Dirty: %s" % self.dao.db_session.dirty)
			self.dao.db_session.commit()
			return True

		except Exception as e:
			self.dao.db_session.rollback()
			logger.exception("Import Category data %s" % e.message)
			raise e

	def importCurrency(self, my_dict=None):
		try:
			self.dao.db_session.begin(subtransactions=True)
			new_currency_code = 0
			max_currency_code = self.dao.db_session.query(func.max(Currency.code)).first()
			if max_currency_code and max_currency_code[0] is not None:
				new_currency_code = int(max_currency_code[0])
			new_currency_code += 1
			count = 0
			for _dict in my_dict:
				output_dict = json.loads(json.dumps(xmltodict.parse(tostring(_dict))))
				tally_currency = output_dict['CURRENCY']
				currency = self.dao.db_session.query(Currency).filter_by(
					code=tally_currency['@NAME']).first()
				if currency:
					currency.id = currency.id
				else:
					currency = Currency()
				count = int(count) + 1
				currency.id = new_currency_code
				currency.code = tally_currency['@NAME']
				self.dao.db_session.add(currency)
			logger.debug("Currency Dirty: %s" % self.dao.db_session.dirty)
			self.dao.db_session.commit()
		except Exception as e:
			self.dao.db_session.rollback()
			logger.exception("Import Currency data %s" % e.message)
			raise e

	def importMaterial(self, enterprise_id=None, my_dict=None, user=None):
		try:
			self.dao.db_session.begin(subtransactions=True)
			grn_materials = []
			material_list = []
			material_without_price = []
			material_with_price = []
			category_dict = {}
			unit_dict = {}
			material_dict = {}
			drawing_no = self.dao.db_session.query(func.count(Material.drawing_no)).filter(
				Material.enterprise_id == enterprise_id).first()
			if drawing_no[0] is not None:
				drawing_no = int(drawing_no[0])
			else:
				drawing_no = 0

			materials = self.dao.db_session.query(Material).filter_by(enterprise_id=enterprise_id).all()
			if materials:
				for material in materials:
					material_dict.update({material.name: {'drawing_no': material.drawing_no, 'name': material.name}})
			categories = self.dao.db_session.query(Category).filter_by(enterprise_id=enterprise_id).all()
			if categories:
				for category in categories:
					category_dict.update({category.name: {'category_id': category.id, 'name': category.name}})
			units = self.dao.db_session.query(UnitMaster).filter_by(enterprise_id=enterprise_id).all()
			if units:
				for unit in units:
					unit_dict.update({str(unit.unit_name).upper(): {'unit_id': unit.unit_id, 'name': unit.unit_name}})
			for _dict in my_dict:
				output_dict = json.loads(json.dumps(xmltodict.parse(tostring(_dict))))
				tally_material = output_dict['STOCKITEM']
				material = ""
				category = ""
				unit = ""
				if '@NAME' in tally_material:
					material = tally_material['@NAME'] in material_dict
				if not material:
					if 'OPENINGRATE' in tally_material:
						price = float(tally_material['OPENINGRATE'].split('/')[0].strip())
					else:
						price = ""
					if 'OPENINGBALANCE' in tally_material:
						opening_qty = float(tally_material['OPENINGBALANCE'].strip().split(" ")[0])
					else:
						opening_qty = 0
					if 'CATEGORY' in tally_material:
						category = category_dict.get(tally_material['CATEGORY'], "")
					category_id = category['category_id'] if category else category_dict['Components']['category_id']
					description = "%s%s" % (
						tally_material['@NAME'],
						(" (%s)" % tally_material['PARENT']) if tally_material['PARENT'] else "")
					if 'BASEUNITS' in tally_material:
						unit = unit_dict.get(str(tally_material['BASEUNITS']).upper(), "")
					unit_id = unit['unit_id'] if unit else 1

					new_dict = {
						'name': tally_material['@NAME'], 'category_id': category_id, 'description': description,
						'unit_id': unit_id, 'opening_qty': opening_qty, 'price': price}
					if tally_material.get('OPENINGRATE') is not None:
						material_with_price.append(new_dict)
					else:
						new_dict.pop('price')
						material_without_price.append(new_dict)

			material_list.extend(material_without_price)
			material_list.extend(material_with_price)
			final_material_list = list({v['name']: v for v in material_list}.values())
			for item in final_material_list:
				drawing_no = int(drawing_no) + 1
				material = Material()
				material.drawing_no = drawing_no
				if item['category_id'] != "":
					material.category_id = item['category_id']
				material.name = item['name']
				material.catalogue_code = drawing_no
				material.description = item['description']
				material.unit_id = item['unit_id']
				if item.get('price') is not None:
					material.price = item['price']
				else:
					material.price = 0
				material.is_stocked = 1
				material.enterprise_id = enterprise_id
				self.dao.db_session.add(material)
				if float(item['opening_qty']) > 0:
					grn_materials.append([material.name, material.price, float(item['opening_qty']), 0])

			if len(final_material_list) > 0:
				tally_xml_message = ImportMessage(
					message=str(len(final_material_list)) + " Material Imported \n", create_on=datetime.now(),
					created_by=user, enterprise_id=enterprise_id, is_process=False)
				self.dao.db_session.add(tally_xml_message)
			logger.debug("Stock Item Dirty: %s" % self.dao.db_session.dirty)
			self.dao.db_session.commit()
			return grn_materials
		except Exception as e:
			self.dao.db_session.rollback()
			logger.exception("Import Material data %s" % e.message)
			raise e

	def importGroup(self, enterprise_id=None, my_dict=None, user=None):
		try:
			self.dao.db_session.begin(subtransactions=True)
			account_groups_dict = {}
			account_groups = self.dao.db_session.query(AccountGroup).filter_by(enterprise_id=enterprise_id).all()
			if account_groups:
				for account_group in account_groups:
					account_groups_dict.update({str(account_group.name).strip().lower(): account_group.id})
			latest_group_id = self.dao.db_session.query(AccountGroup.id).filter(
				AccountGroup.enterprise_id == enterprise_id).order_by(AccountGroup.id.desc()).first()
			group_id = int(latest_group_id.id)
			for _dict in my_dict:
				parent_id = 0
				output_dict = json.loads(json.dumps(xmltodict.parse(tostring(_dict))))
				tally_acc_group = output_dict['GROUP']
				import_group_name = str(tally_acc_group['@NAME']).rstrip()
				group = import_group_name.lower() in account_groups_dict
				if not group:
					logger.debug("Group Name:%s" % import_group_name)
					group_name = account_group_map.get(import_group_name.title(), "None")
					if group_name == "None":
						if tally_acc_group['PARENT']:
							logger.debug("Parent Name:%s" % (tally_acc_group['PARENT'].lower()))
							parent_name = account_group_map.get(tally_acc_group['PARENT'].title(), "None")
							if parent_name != "None":
								parent_id = account_groups_dict.get(parent_name.lower(), "None")
							else:
								parent_id = account_groups_dict.get(tally_acc_group['PARENT'].lower(), "None")

					else:
						parent_id = account_groups_dict.get(group_name.lower(), "None")
					if import_group_name.lower() not in account_groups_dict:
						group = AccountGroup(
							parent_id=parent_id,name=import_group_name, description=import_group_name,
							config_flags=3, enterprise_id=enterprise_id)
						group_id = group_id + 1
						group.id = group_id
						self.dao.db_session.add(group)
						account_groups_dict.update({str(import_group_name).strip().lower(): group_id})
			self.dao.db_session.commit()
			return True
		except Exception as e:
			self.dao.db_session.rollback()
			logger.exception("Import Group data %s" % e.message)
			raise e

	def importLedger(self, enterprise_id=None, my_dict=None, user=None, enterprise=None):
		try:
			self.dao.db_session.begin(subtransactions=True)
			total_opening_balance_credit = []
			total_opening_balance_debit = []
			ledger_opening_balance_list = []
			ledger_list = set()
			ledger_dict = {}
			account_groups_dict = {}
			tax_ledger_maps_dict = {}
			category_dict = {}
			ledgers = self.dao.db_session.query(Ledger).filter_by(enterprise_id=enterprise_id).all()
			if ledgers:
				for ledger in ledgers:
					ledger_dict.update({ledger.name: ledger.group_id})
			account_groups = self.dao.db_session.query(AccountGroup).filter_by(enterprise_id=enterprise_id).all()
			if account_groups:
				for account_group in account_groups:
					account_groups_dict.update({str(account_group.name).strip().lower(): account_group.id})
			tax_ledger_maps = self.dao.db_session.query(TaxLedgerMap).filter_by(enterprise_id=enterprise_id).all()
			if tax_ledger_maps:
				for tax_ledger_map in tax_ledger_maps:
					tax_ledger_maps_dict.update(
						{"%s%s" % (tax_ledger_map.enterprise_id, tax_ledger_map.tax_id): tax_ledger_map.ledger_id})

			categories = self.dao.db_session.query(Category).filter_by(enterprise_id=enterprise_id).all()
			if categories:
				for category in categories:
					category_dict.update({category.name: {'category_id': category.id, 'name': category.name}})

			duties_and_taxes_group_ids = getAccountGroupIDs(
				enterprise_id=enterprise_id, names=[AccountGroup.DUTIES_AND_TAXES_GROUP_NAME], deep=True)
			party_det = {}
			tax_det = {}
			for _dict in my_dict:
				output_dict = json.loads(json.dumps(xmltodict.parse(tostring(_dict))))
				tally_ledger = output_dict['LEDGER']
				tally_ledger_name = str(tally_ledger['@NAME']).strip()
				ledger_name, ledger_group_id = "", ""
				ledger = tally_ledger_name in ledger_dict
				if not ledger:
					ledger_name = account_ledger_map.get(tally_ledger_name, "None")
					if ledger_name != "None":
						ledger_group_id = ledger_dict.get(ledger_name, "None")
					else:
						address = ""
						cst = ""
						gst = ""
						credit_period = 0
						state_name = ""
						if 'ADDRESS.LIST' in tally_ledger:
							address_items = tally_ledger.get('ADDRESS.LIST')
							if address_items and "ADDRESS" in address_items:
								address = address_items["ADDRESS"]
								address = ", ".join(address) if type(address) is list else address
						if 'PINCODE' in tally_ledger:
							address += tally_ledger['PINCODE']
						if 'INCOMETAXNUMBER' in tally_ledger:
							cst = tally_ledger['INCOMETAXNUMBER']
						if 'PARTYGSTIN' in tally_ledger:
							gst = tally_ledger['PARTYGSTIN']
						elif 'SALESTAXNUMBER' in tally_ledger:
							gst = tally_ledger['SALESTAXNUMBER']
						if 'BILLCREDITPERIOD' in tally_ledger:
							credit_period = tally_ledger['BILLCREDITPERIOD'].strip().split(" ")[0]
							if credit_period.isdigit():
								credit_period = credit_period
							else:
								credit_period = 0
						if 'STATENAME' in tally_ledger:
							state_name = tally_ledger['STATENAME']
						elif 'LEDSTATENAME' in tally_ledger:
							state_name = tally_ledger['LEDSTATENAME']
						party_det.update({tally_ledger_name: {
							'address': address, 'gst': gst, 'cst': cst, 'credit_period': credit_period,
							'state_name': state_name}})

						if tally_ledger['PARENT']:
							if tally_ledger['@RESERVEDNAME']:
								parent_name = tally_ledger['@RESERVEDNAME']
							else:
								parent_name = account_group_map.get(tally_ledger['PARENT'].title(), "None")
							# parent_name = account_group_map.get(tally_ledger['PARENT'].lower(), "None")
							if parent_name != "None":
								ledger_group_id = account_groups_dict.get(parent_name.lower(), "None")
							else:
								ledger_group_id = account_groups_dict.get(tally_ledger['PARENT'].lower(), "None")
							ledger_list.add((tally_ledger_name, ledger_group_id))
						else:
							ledger_list.add((tally_ledger_name, 1))
				else:
					ledger_group_id = ledger_dict.get(tally_ledger_name, "None")
				if ledger_group_id in duties_and_taxes_group_ids:
					tax_type = ""
					rate = ""
					if 'TAXTYPE' in tally_ledger:
						tax_type = tally_ledger['TAXTYPE']
					if 'RATEOFTAXCALCULATION' in tally_ledger:
						rate = tally_ledger['RATEOFTAXCALCULATION']
					tax_det.update({tally_ledger_name: {'name': tally_ledger_name, 'rate': rate, 'type': tax_type}})
				try:
					if 'OPENINGBALANCE' in tally_ledger:
						if float(tally_ledger['OPENINGBALANCE']) > 0:
							total_opening_balance_debit.append(float(tally_ledger['OPENINGBALANCE']))
							opening_balance_debit = {
								'ledger_name': tally_ledger_name, 'account_group_id': ledger_group_id, 'opening_debit': '',
								'opening_credit': abs(float(tally_ledger['OPENINGBALANCE']))}
							if opening_balance_debit not in ledger_opening_balance_list:
								ledger_opening_balance_list.append(opening_balance_debit)
						else:
							total_opening_balance_credit.append(float(tally_ledger['OPENINGBALANCE']))
							opening_balance_credit = {
								'ledger_name': tally_ledger_name, 'account_group_id': ledger_group_id,
								'opening_debit': abs(float(tally_ledger['OPENINGBALANCE'])), 'opening_credit': ''}
							if opening_balance_credit not in ledger_opening_balance_list:
								ledger_opening_balance_list.append(opening_balance_credit)
				except Exception as e:
					logger.debug('Opening balance is not found for %s due to %s ' % (tally_ledger_name, e))
			debtors_group_ids = getAccountGroupIDs(
				enterprise_id=enterprise_id, names=[AccountGroup.SUNDRY_DEBTOR_GROUP_NAME], deep=True)
			creditors_group_ids = getAccountGroupIDs(
				enterprise_id=enterprise_id, names=[AccountGroup.SUNDRY_CREDITOR_GROUP_NAME], deep=True)
			duties_and_taxes_group_ids = getAccountGroupIDs(
				enterprise_id=enterprise_id, names=[AccountGroup.DUTIES_AND_TAXES_GROUP_NAME], deep=True)
			in_direct_expenses_ids = getAccountGroupIDs(
				enterprise_id=enterprise_id, names=[AccountGroup.INDIRECT_EXPENSES], deep=True)
			drawing_no = self.dao.db_session.query(func.count(Material.drawing_no)).filter(
				Material.enterprise_id == enterprise_id).first()
			if drawing_no[0] is not None:
				drawing_no = int(drawing_no[0])
			else:
				drawing_no = 0
			for item in list(ledger_list):
				ledger = Ledger()
				ledger.name = item[0]
				party_details = party_det[ledger.name]
				ledger.description = item[0]
				ledger.enterprise_id = enterprise_id
				ledger.group_id = item[1]
				if int(item[1]) in debtors_group_ids or int(item[1]) in creditors_group_ids:
					party_id = self.insertParty(
						enterprise=enterprise, ledger_name=item[0],
						parent_name='Sundry Debtors' if int(item[1]) in debtors_group_ids else 'Sundry Creditors',
						db_session=self.dao.db_session, party_det=party_details, user_id=user)
					party_ledger_map = PartyLedgerMap(
						party_id=party_id, enterprise_id=enterprise_id, is_supplier=False if int(item[1]) in debtors_group_ids else True)
					party_ledger_map.ledger = ledger
					self.dao.db_session.add(party_ledger_map)
				elif int(item[1]) in duties_and_taxes_group_ids:
					tax_details = tax_det[ledger.name]
					if tax_details['rate'] in tax_code_dict:
						tax_ledger_code = ""
						if str(tax_details['name']).lower().find('cgst') != -1:
							tax_ledger_code = "CGST" + tax_percentage_dict[tax_details['rate']]
						elif str(tax_details['name']).lower().find('sgst') != -1:
							tax_ledger_code = "SGST" + tax_percentage_dict[tax_details['rate']]
						elif str(tax_details['name']).lower().find('igst') != -1:
							tax_ledger_code = "IGST" + tax_percentage_dict[tax_details['rate']]
						compare_code_combination = ""
						if tax_ledger_code != "":
							self.dao.db_session.add(ledger)
							self.dao.db_session.flush([ledger])
							self.dao.db_session.refresh(ledger)
							compare_code_combination = "%s%s" % (enterprise_id, tax_ledger_code)
							if compare_code_combination not in tax_ledger_maps_dict:
								tax_ledger_map = TaxLedgerMap(
									tax_id=tax_ledger_code, enterprise_id=enterprise_id, ledger_id=ledger.id)
								tax_ledger_map.ledger = ledger
								self.dao.db_session.add(tax_ledger_map)
								tax_ledger_maps_dict.update(
									{"%s%s" % (enterprise_id, tax_ledger_code): ledger.id})
						else:
							self.dao.db_session.add(ledger)
					else:
						self.dao.db_session.add(ledger)
					drawing_no = int(drawing_no) + 1
					material = Material()
					material.drawing_no = drawing_no
					material.category_id = category_dict['Components']['category_id']
					material.name = item[0]
					material.catalogue_code = drawing_no
					material.description = item[0]
					material.unit_id = 1
					material.price = 0
					material.is_stocked = 0
					material.enterprise_id = enterprise_id
					self.dao.db_session.add(material)
				elif int(item[1]) in in_direct_expenses_ids:
					if item[0].lower() in indirect_expenses_dict:
						drawing_no = int(drawing_no) + 1
						material = Material()
						material.drawing_no = drawing_no
						material.category_id = category_dict['Components']['category_id']
						material.name = item[0]
						material.catalogue_code = drawing_no
						material.description = item[0]
						material.unit_id = 1
						material.price = 0
						material.is_stocked = 0
						material.enterprise_id = enterprise_id
						self.dao.db_session.add(ledger)
						self.dao.db_session.add(material)
					else:
						self.dao.db_session.add(ledger)
				else:
					self.dao.db_session.add(ledger)
			if len(ledger_list) > 0:
				tallyxmlmessage = ImportMessage(
					message=str(len(ledger_list)) + " Ledgers Imported  \n", create_on=datetime.now(), created_by=user,
					enterprise_id=enterprise_id, is_process=False)
				self.dao.db_session.add(tallyxmlmessage)
			logger.debug("Ledger Dirty: %s" % self.dao.db_session.dirty)
			self.dao.db_session.commit()
			bill_allocations_list = self.getBillallocationList(my_dict=my_dict, enterprise_id=enterprise_id)
			result_dict = ""
			if len(ledger_list) > 0:
				result_dict = {
					'ledger_opening_balance_list': ledger_opening_balance_list, 'bill_allocations_list': bill_allocations_list}
			return result_dict
		except Exception as e:
			self.dao.db_session.rollback()
			logger.exception("Import Ledger data %s" % e.message)
			raise e

	def importNonAccountingVoucher(self, enterprise=None, my_dict=None, user=None, voucher_type_dict=None):
		try:
			self.dao.db_session.begin(subtransactions=True)
			for _dict in my_dict:
				output_dict = json.loads(json.dumps(xmltodict.parse(tostring(_dict))))
				tally_voucher = output_dict['VOUCHER']
				if 'VOUCHERTYPENAME' in tally_voucher:
					voucher_type = voucher_type_dict.get(tally_voucher['VOUCHERTYPENAME'], "").title()
					if voucher_type in (
							"Delivery Note", "Receipt Note", "Rejections In", "Rejections Out") and tally_voucher['@ACTION'] != "Cancel":
						self.createNonAccountingVouchers(
							voucher_type=voucher_type, tally_voucher=tally_voucher, enterprise=enterprise, user=user)
					elif voucher_type in ("Stock Journal", "Material In", "Material Out") and tally_voucher['@ACTION'] != "Cancel":
						self.createStockJournal(tally_voucher=tally_voucher, enterprise=enterprise, user=user)
			self.createImportLogMessages(enterprise=enterprise, user=user)
			self.dao.db_session.commit()
		except Exception as e:
			self.dao.db_session.rollback()
			logger.exception("Import Voucher data %s" % e.message)
			raise e

	def importVoucher(self, enterprise=None, my_dict=None, user=None, voucher_type_dict=None):
		try:
			self.importNonAccountingVoucher(
				enterprise=enterprise, my_dict=my_dict, user=user, voucher_type_dict=voucher_type_dict)
			self.dc_no_dict = self.dao.getDCNoDict(enterprise_id=enterprise.id)
			self.receipt_note_no_dict = self.dao.getReceiptNoteNoDict(enterprise_id=enterprise.id)
			voucher_status = False
			self.dao.db_session.begin(subtransactions=True)
			for _dict in my_dict:
				output_dict = json.loads(json.dumps(xmltodict.parse(tostring(_dict))))
				tally_voucher = output_dict['VOUCHER']
				if 'VOUCHERTYPENAME' in tally_voucher:
					voucher_details = tally_voucher['EFFECTIVEDATE'] + "-" + tally_voucher['VOUCHERTYPENAME'] + "-" + \
						(tally_voucher['VOUCHERNUMBER'] if 'VOUCHERNUMBER' in tally_voucher else "")
					voucher_type = voucher_type_dict.get(tally_voucher['VOUCHERTYPENAME'], "").title()
					if voucher_type in accounts_voucher_type_dict:
						if voucher_details in self.voucher_dict:
							voucher = self.dao.db_session.query(Voucher).filter(
								Voucher.id == self.voucher_dict[voucher_details]).first()
							for particular in voucher.particulars:
								self.dao.db_session.delete(particular)
								for bill in particular.ledger.bills:
									if bill.voucher_id == particular.voucher_id:
										self.dao.db_session.delete(bill)
									else:
										for settlement in bill.settlements:
											if settlement.voucher_id == particular.voucher_id:
												self.dao.db_session.delete(settlement)
							self.update_voucher_count = self.update_voucher_count + 1
						elif voucher_details not in self.voucher_dict:
							project_code = self.dao.db_session.query(Project.id).filter(
								Project.enterprise_id == enterprise.id).first()
							voucher = Voucher()
							self.voucher_count = self.voucher_count + 1
							narration = ""
							if tally_voucher.get('NARRATION', "None") != "None":
								narration = tally_voucher['NARRATION']
							narration = str(narration) + " Date:" + tally_voucher[
								'EFFECTIVEDATE'] + " {TALLY_ID:" + voucher_details + "}"
							voucher.narration = narration
							voucher.project_code = project_code[0] if project_code else ''
							voucher.enterprise_id = enterprise.id
						voucher.voucher_date = tally_voucher['EFFECTIVEDATE']
						voucher.financial_year = getFinancialYear(
							for_date=datetime.strptime(tally_voucher['EFFECTIVEDATE'], "%Y%m%d"),
							fy_start_day=enterprise.fy_start_day)
						inventory_particulars = tally_voucher.get(
							'ALLINVENTORYENTRIES.LIST', []) if 'ALLINVENTORYENTRIES.LIST' in tally_voucher else tally_voucher.get(
							'INVENTORYENTRIES.LIST', [])
						inventory_particulars_list = inventory_particulars if isinstance(
							inventory_particulars, (list,)) else [
							inventory_particulars] if inventory_particulars else []
						voucher_particulars = self.getVoucherParticulars(tally_voucher=tally_voucher)
						voucher_abstract = self.constructVoucherParticulars(
							tally_voucher=tally_voucher, voucher_type=voucher_type, voucher=voucher,
							voucher_status=voucher_status, enterprise=enterprise, inventory_particulars_list=inventory_particulars_list)
						voucher.particulars = voucher_abstract["particulars_to_be_saved"]
						bank_ledger_ids = voucher_abstract["bank_ledger_ids"]
						voucher.status = APPROVED_VOUCHER
						inventory_list = voucher_abstract["inventory_list"]
						grand_total = voucher_abstract["grand_total"]
						if inventory_list:
							inventory_particulars_list.extend(inventory_list)
						self.createVouchers(
							voucher_type=voucher_type, tally_voucher=tally_voucher, voucher=voucher,
							enterprise=enterprise, inventory_particulars_list=inventory_particulars_list,
							grand_total=grand_total, voucher_particulars=voucher_particulars,
							user=user, bank_ledger_ids=bank_ledger_ids)

						self.dao.db_session.add(voucher)
			self.createImportLogMessages(enterprise=enterprise, user=user)
			self.dao.db_session.commit()
			voucher_status = True
			return voucher_status
		except Exception as e:
			self.dao.db_session.rollback()
			logger.exception("Import Voucher data %s" % e.message)
			raise e

	def createImportLogMessages(self, enterprise=None, user=None):

		if self.voucher_count > 0:
			tally_xml_message = ImportMessage(
				message=str(self.voucher_count) + " Vouchers Imported \n", create_on=datetime.now(), created_by=user,
				enterprise_id=enterprise.id, is_process=False)
			self.dao.db_session.add(tally_xml_message)
		if self.update_voucher_count > 0:
			tally_xml_message = ImportMessage(
				message=str(self.update_voucher_count) + " Vouchers Updated \n", create_on=datetime.now(),
				created_by=user,
				enterprise_id=enterprise.id, is_process=False)
			self.dao.db_session.add(tally_xml_message)
		if self.invoice_count > 0:
			tally_xml_message = ImportMessage(
				message=str(self.invoice_count) + " Invoices Imported \n", create_on=datetime.now(), created_by=user,
				enterprise_id=enterprise.id, is_process=False)
			self.dao.db_session.add(tally_xml_message)
		if self.update_invoice_count > 0:
			tally_xml_message = ImportMessage(
				message=str(self.update_invoice_count) + " Invoices Updated \n", create_on=datetime.now(),
				created_by=user,
				enterprise_id=enterprise.id, is_process=False)
			self.dao.db_session.add(tally_xml_message)

		if self.dc_count > 0:
			tally_xml_message = ImportMessage(
				message=str(self.dc_count) + " Delivery Challans Imported \n", create_on=datetime.now(), created_by=user,
				enterprise_id=enterprise.id, is_process=False)
			self.dao.db_session.add(tally_xml_message)
		if self.update_dc_count > 0:
			tally_xml_message = ImportMessage(
				message=str(self.update_dc_count) + " Delivery Challans Updated \n", create_on=datetime.now(),
				created_by=user,
				enterprise_id=enterprise.id, is_process=False)
			self.dao.db_session.add(tally_xml_message)

		if self.issue_count > 0:
			tally_xml_message = ImportMessage(
				message=str(self.issue_count) + " Issues Imported \n", create_on=datetime.now(), created_by=user,
				enterprise_id=enterprise.id, is_process=False)
			self.dao.db_session.add(tally_xml_message)
		if self.update_issue_count > 0:
			tally_xml_message = ImportMessage(
				message=str(self.update_issue_count) + " Issues Updated \n", create_on=datetime.now(),
				created_by=user,
				enterprise_id=enterprise.id, is_process=False)
			self.dao.db_session.add(tally_xml_message)

		if self.receipt_count > 0:
			tally_xml_message = ImportMessage(
				message=str(self.receipt_count) + " Receipts Imported \n", create_on=datetime.now(), created_by=user,
				enterprise_id=enterprise.id, is_process=False)
			self.dao.db_session.add(tally_xml_message)
		if self.update_receipt_count > 0:
			tally_xml_message = ImportMessage(
				message=str(self.update_receipt_count) + " Receipts Updated \n", create_on=datetime.now(),
				created_by=user,
				enterprise_id=enterprise.id, is_process=False)
			self.dao.db_session.add(tally_xml_message)

	def createNonAccountingVouchers(self, voucher_type=None, tally_voucher=None, enterprise=None, user=None):
		party_id = self.getParty_id(
			voucher=tally_voucher, is_supplier=False, is_customer=True,
			enterprise=enterprise, user_id=user)
		voucher_particulars = self.getVoucherParticulars(tally_voucher=tally_voucher)
		inventory_list = []
		grand_total = 0
		for particular in voucher_particulars:
			if particular is not None:
				if particular.get('INVENTORYALLOCATIONS.LIST') is not None:
					led_inv_item = particular.get('INVENTORYALLOCATIONS.LIST', [])
					inventory_list = led_inv_item if isinstance(led_inv_item, (list,)) else [led_inv_item]

				if particular['ISPARTYLEDGER'] == "Yes":
					grand_total = abs(float(particular['AMOUNT'])) if 'AMOUNT' in particular else 0

		inventory_particulars = tally_voucher.get(
			'ALLINVENTORYENTRIES.LIST', []) if 'ALLINVENTORYENTRIES.LIST' in tally_voucher else tally_voucher.get(
			'INVENTORYENTRIES.LIST', [])
		inventory_particulars_list = inventory_particulars if isinstance(
			inventory_particulars, (list,)) else [inventory_particulars] if inventory_particulars else []
		current_fy = getFinancialYear(
						for_date=datetime.strptime(tally_voucher['EFFECTIVEDATE'], "%Y%m%d"),
						fy_start_day=enterprise.fy_start_day)
		if inventory_list:
			inventory_particulars_list.extend(inventory_list)

		if len(inventory_particulars_list) > 0:
			if voucher_type in ("Delivery Note", "Rejections Out"):
				if current_fy in self.delivery_no_dict:
					new_delivery_no = self.delivery_no_dict[current_fy] + 1
					self.delivery_no_dict.update({current_fy: new_delivery_no})
				else:
					new_delivery_no = 1
					self.delivery_no_dict.update({current_fy: 1})
				if party_id:
					invoice = self.constructInvoice(
						voucher=tally_voucher, enterprise=enterprise, new_invoice_no=new_delivery_no,
						user_id=user, grand_total=grand_total, invoice_particulars_list=inventory_particulars_list,
						voucher_particulars=voucher_particulars, party_id=party_id, invoice_type='DC')
					invoice.financial_year = current_fy
					self.dao.db_session.add(invoice)
			if voucher_type in ("Receipt Note", "Rejections In"):
				if current_fy in self.receipt_no_dict:
					new_receipt_no = self.receipt_no_dict[current_fy] + 1
					self.receipt_no_dict.update({current_fy: new_receipt_no})
				else:
					new_receipt_no = 1
					self.receipt_no_dict.update({current_fy: 1})
				if party_id:
					# self.receipt_count = self.receipt_count + 1
					receipt = self.constructReceipt(
						voucher=tally_voucher, enterprise=enterprise, new_receipt_no=new_receipt_no, user_id=user,
						receipt_particulars_list=inventory_particulars_list, grand_total=grand_total,
						voucher_particulars=voucher_particulars, party_id=party_id)
					receipt.financial_year = current_fy
					self.dao.db_session.add(receipt)

	def createStockJournal(self, tally_voucher=None, enterprise=None, user=None):
		party_id = self.getParty_id(
			voucher=tally_voucher, is_supplier=False, is_customer=True,
			enterprise=enterprise, user_id=user)
		current_fy = getFinancialYear(
			for_date=datetime.strptime(tally_voucher['EFFECTIVEDATE'], "%Y%m%d"),
			fy_start_day=enterprise.fy_start_day)
		voucher_particulars = self.getVoucherParticulars(tally_voucher=tally_voucher)
		grand_total = 0
		for particular in voucher_particulars:
			if particular is not None:
				if particular['ISPARTYLEDGER'] == "Yes":
					grand_total = abs(float(particular['AMOUNT'])) if 'AMOUNT' in particular else 0

		stock_in_particulars = tally_voucher.get('INVENTORYENTRIESIN.LIST', [])
		stock_in_particulars_list = stock_in_particulars if isinstance(
			stock_in_particulars, (list,)) else [stock_in_particulars] if stock_in_particulars else []

		stock_out_particulars = tally_voucher.get('INVENTORYENTRIESOUT.LIST', [])
		stock_out_particulars_list = stock_out_particulars if isinstance(
			stock_out_particulars, (list,)) else [stock_out_particulars] if stock_out_particulars else []

		if len(stock_out_particulars_list) > 0:
			if party_id:
				if current_fy in self.delivery_no_dict:
					new_issue_no = self.delivery_no_dict[current_fy] + 1
					self.delivery_no_dict.update({current_fy: new_issue_no})
				else:
					new_issue_no = 1
					self.delivery_no_dict.update({current_fy: 1})
			else:
				if current_fy in self.issue_no_dict:
					new_issue_no = self.issue_no_dict[current_fy] + 1
					self.issue_no_dict.update({current_fy: new_issue_no})
				else:
					new_issue_no = 1
					self.issue_no_dict.update({current_fy: 1})

			invoice = self.constructInvoice(
				voucher=tally_voucher, enterprise=enterprise, new_invoice_no=new_issue_no, user_id=user, grand_total=grand_total,
				invoice_particulars_list=stock_out_particulars_list, invoice_type='DC' if party_id else "Issue",
				party_id=party_id, voucher_particulars=[])
			invoice.financial_year = current_fy
			self.dao.db_session.add(invoice)

		if len(stock_in_particulars_list) > 0:
			if current_fy in self.receipt_no_dict:
				new_receipt_no = self.receipt_no_dict[current_fy] + 1
				self.receipt_no_dict.update({current_fy: new_receipt_no})
			else:
				new_receipt_no = 1
				self.receipt_no_dict.update({current_fy: 1})

			# self.receipt_count = self.receipt_count + 1
			receipt = self.constructReceipt(
				voucher=tally_voucher, enterprise=enterprise, new_receipt_no=new_receipt_no, user_id=user, grand_total=grand_total,
				receipt_particulars_list=stock_in_particulars_list, voucher_particulars=[], party_id=party_id if party_id else 0)
			receipt.financial_year = current_fy
			self.dao.db_session.add(receipt)

	def createVouchers(
			self, voucher_type=None, tally_voucher=None, voucher=None, enterprise=None, inventory_particulars_list=None,
			grand_total=None, voucher_particulars=None, user=None, bank_ledger_ids=None):
		current_fy = voucher.financial_year
		party_ledger = tally_voucher['PARTYLEDGERNAME'] if 'PARTYLEDGERNAME' in tally_voucher else ""
		voucher_details = tally_voucher['EFFECTIVEDATE'] + "-" + tally_voucher['VOUCHERTYPENAME'] + "-" + \
			(tally_voucher['VOUCHERNUMBER'] if 'VOUCHERNUMBER' in tally_voucher else "")

		if voucher_type == "Sales" and tally_voucher['@ACTION'] != "Cancel":
			voucher.type_id = SALES_VOUCHER
			party_id = self.getParty_id(
				voucher=tally_voucher, is_supplier=False, is_customer=True,
				enterprise=enterprise, user_id=user)
			if len(inventory_particulars_list) > 0:
				if current_fy in self.invoice_no_dict:
					new_invoice_no = self.invoice_no_dict[current_fy] + 1
					self.invoice_no_dict.update({current_fy: new_invoice_no})
				else:
					new_invoice_no = 1
					self.invoice_no_dict.update({current_fy: 1})
				if party_id:
					invoice = self.constructInvoice(
						voucher=tally_voucher, enterprise=enterprise, new_invoice_no=new_invoice_no,
						user_id=user, grand_total=grand_total, invoice_particulars_list=inventory_particulars_list,
						voucher_particulars=voucher_particulars, party_id=party_id, invoice_type='GST')
					invoice.financial_year = current_fy
					self.dao.db_session.add(invoice)
		elif voucher_type == "Purchase" and tally_voucher['@ACTION'] != "Cancel":
			voucher.type_id = PURCHASE_VOUCHER
			party_id = self.getParty_id(
				voucher=tally_voucher, is_supplier=True, is_customer=False,
				enterprise=enterprise, user_id=user)
			if len(inventory_particulars_list) > 0:
				if current_fy in self.receipt_no_dict:
					new_receipt_no = self.receipt_no_dict[current_fy] + 1
					self.receipt_no_dict.update({current_fy: new_receipt_no})
				else:
					new_receipt_no = 1
					self.receipt_no_dict.update({current_fy: 1})
				if party_id:
					# self.receipt_count = self.receipt_count + 1
					receipt = self.constructReceipt(
						voucher=tally_voucher, enterprise=enterprise, new_receipt_no=new_receipt_no, user_id=user,
						receipt_particulars_list=inventory_particulars_list, grand_total=grand_total,
						voucher_particulars=voucher_particulars, party_id=party_id)
					receipt.financial_year = current_fy
					self.dao.db_session.add(receipt)
		elif voucher_type == "Payment" or voucher_type == "Receipt":
			ledger = ""
			if party_ledger:
				ledger = self.dao.db_session.query(Ledger.group_id).filter(
					Ledger.name.in_(bank_ledger_ids),
					Ledger.enterprise_id == enterprise.id,
					Ledger.group_id.in_(self.bank_group_ids)).first()
			if ledger:
				voucher.type_id = BANK_VOUCHER
			else:
				voucher.type_id = CASH_VOUCHER
		else:
			voucher.type_id = GENERAL_VOUCHER
		type_id = voucher.type_id
		if voucher_details not in self.voucher_dict:
			if current_fy in self.voucher_no_dict:
				if type_id in self.voucher_no_dict[current_fy]:
					self.voucher_no_dict[current_fy][type_id] = self.voucher_no_dict[current_fy][type_id] + 1
				else:
					self.voucher_no_dict[current_fy].update({type_id: 1})
			else:
				self.voucher_no_dict.update({current_fy: {type_id: 1}})
			voucher.voucher_no = self.voucher_no_dict[current_fy][type_id]

	def constructVoucherParticulars(
			self, tally_voucher=None, voucher_type=None, voucher=None,
			user=None, voucher_status=None, enterprise=None, inventory_particulars_list=None):
		j = 1
		dr_value = 0
		cr_value = 0
		is_debit = 0
		grand_total = 0
		particulars_to_be_saved = []
		bank_ledger_ids = []
		inventory_list = []
		voucher_particulars = self.getVoucherParticulars(tally_voucher=tally_voucher)

		for particular in voucher_particulars:
			if particular is not None:
				if 'PARTYLEDGERNAME' in tally_voucher:
					is_party_ledger = True if particular['LEDGERNAME'] in self.all_party_ledger_dict else False
				else:
					is_party_ledger = False
				voucher_particular = self.insertVoucherParticulars(
					enterprise_id=enterprise.id, particular=particular, voucher=voucher,
					item_no=j, ledger_dict=self.all_ledger_dict, voucher_type=voucher_type,
					is_party_ledger=is_party_ledger, user_id=user)
				if voucher_particular is None:
					self.dao.db_session.rollback()
					return voucher_status
				particulars_to_be_saved.append(voucher_particular)
				bank_ledger_ids.append(particular['LEDGERNAME'])
				# Sales default Ledger Entry Related changes
				if particular['ISDEEMEDPOSITIVE'] == "Yes":
					dr_value = dr_value + abs(
						Decimal(particular['AMOUNT']).quantize(Decimal('1.00'))) if 'AMOUNT' in particular else 0
				else:
					cr_value = cr_value + abs(
						Decimal(particular['AMOUNT']).quantize(Decimal('1.00'))) if 'AMOUNT' in particular else 0
				if particular['ISPARTYLEDGER'] == "Yes":
					grand_total = abs(float(particular['AMOUNT'])) if 'AMOUNT' in particular else 0
					if 'AMOUNT' in particular:
						if float(particular['AMOUNT']) > 0:
							is_debit = 1
						else:
							is_debit = 0
				if particular.get('INVENTORYALLOCATIONS.LIST') is not None:
					led_inv_item = particular.get('INVENTORYALLOCATIONS.LIST', [])
					inventory_list = led_inv_item if isinstance(led_inv_item, (list,)) else [led_inv_item]

				j = j + 1
		if voucher_type == "Sales" or voucher_type == "Purchase":
			ledger_id = ""
			if voucher_type == "Sales":
				sales_ledger_id = None
				if len(inventory_particulars_list) > 0:
					sales_ledger_id = self.getVoucherAccountingLedger(
						inventory_particulars_list=inventory_particulars_list)
				if sales_ledger_id:
					ledger_id = sales_ledger_id
				else:
					ledger_id = self.sales_ledger.id
			elif voucher_type == "Purchase":
				purchase_ledger_id = None
				if len(inventory_particulars_list) > 0:
					purchase_ledger_id = self.getVoucherAccountingLedger(
						inventory_particulars_list=inventory_particulars_list)
				if purchase_ledger_id:
					ledger_id = purchase_ledger_id
				else:
					ledger_id = self.purchase_ledger.id
			if float(abs(dr_value - cr_value)) != 0.00:
				voucher_particular = VoucherParticulars(
					voucher_id=voucher.id, item_no=j, ledger_id=ledger_id, enterprise_id=enterprise.id,
					amount=abs(dr_value - cr_value), is_debit=is_debit)
				particulars_to_be_saved.append(voucher_particular)
		return {
			"particulars_to_be_saved": particulars_to_be_saved, "inventory_list": inventory_list,
			"grand_total": grand_total, "bank_ledger_ids": bank_ledger_ids}

	def importOrders(self, enterprise=None, my_dict=None, user=None, voucher_type_dict=None):
		try:
			self.dao.db_session.begin(subtransactions=True)
			for _dict in my_dict:
				output_dict = json.loads(json.dumps(xmltodict.parse(tostring(_dict))))
				tally_voucher = output_dict['VOUCHER']
				if 'VOUCHERTYPENAME' in tally_voucher:
					voucher_type = voucher_type_dict.get(tally_voucher['VOUCHERTYPENAME'], "").title()
					if voucher_type in("Purchase Order", "Sales Order", "Job Work In Order", "Job Work Out Order"):
						current_fy = getFinancialYear(
							for_date=datetime.strptime(tally_voucher['EFFECTIVEDATE'], "%Y%m%d"),
							fy_start_day=enterprise.fy_start_day)
						item_abstract = self.getItemParticulars(
							tally_voucher=tally_voucher)
						if voucher_type in ("Purchase Order", "Job Work Out Order") and tally_voucher['@ACTION'] != "Cancel":
							party_id = self.getParty_id(
								voucher=tally_voucher, is_supplier=True, is_customer=False,
								enterprise=enterprise, user_id=user)
							if len(item_abstract['particulars']) > 0:
								if party_id:
									purchase_order = self.constructPurchaseOrder(
										voucher=tally_voucher, enterprise=enterprise, voucher_type=voucher_type,
										user_id=user, grand_total=item_abstract['grand_total'],
										po_particulars_list=item_abstract['particulars'], party_id=party_id)
									purchase_order.financial_year = current_fy
									self.dao.db_session.add(purchase_order)
						if voucher_type in ("Sales Order", "Job Work In Order") and tally_voucher['@ACTION'] != "Cancel":
							party_id = self.getParty_id(
								voucher=tally_voucher, is_supplier=False, is_customer=True,
								enterprise=enterprise, user_id=user)
							if len(item_abstract['particulars']) > 0:
								if party_id:
									sales_order = self.constructSalesOrder(
										voucher=tally_voucher, enterprise=enterprise, voucher_type=voucher_type, user_id=user,
										grand_total=item_abstract['grand_total'], oa_particulars_list=item_abstract['particulars'],
										oa_dict=self.oa_dict, party_id=party_id)
									sales_order.financial_year = current_fy
									self.dao.db_session.add(sales_order)
			if self.po_count > 0:
				tally_xml_message = ImportMessage(
					message=str(self.po_count) + " Purchase Orders Imported \n", create_on=datetime.now(), created_by=user,
					enterprise_id=enterprise.id, is_process=False)
				self.dao.db_session.add(tally_xml_message)
			if self.oa_count > 0:
				tally_xml_message = ImportMessage(
					message=str(self.oa_count) + " Sales Orders Imported \n", create_on=datetime.now(), created_by=user,
					enterprise_id=enterprise.id, is_process=False)
				self.dao.db_session.add(tally_xml_message)
			if self.update_po_count > 0:
				tally_xml_message = ImportMessage(
					message=str(self.update_po_count) + " Purchase Orders Updated \n", create_on=datetime.now(), created_by=user,
					enterprise_id=enterprise.id, is_process=False)
				self.dao.db_session.add(tally_xml_message)
			if self.update_oa_count > 0:
				tally_xml_message = ImportMessage(
					message=str(self.update_oa_count) + " Sales Orders Updated \n", create_on=datetime.now(), created_by=user,
					enterprise_id=enterprise.id, is_process=False)
				self.dao.db_session.add(tally_xml_message)

			logger.debug("Order Dirty: %s" % self.dao.db_session.dirty)
			self.dao.db_session.commit()
		except Exception as e:
			self.dao.db_session.rollback()
			logger.exception("Import Order data %s" % e.message)
			raise e

	@staticmethod
	def getVoucherParticulars(tally_voucher=None):
		voucher_particulars = []
		if tally_voucher.get('ALLLEDGERENTRIES.LIST', []) is not None:
			voucher_particulars = tally_voucher.get('ALLLEDGERENTRIES.LIST', [])
		more_particulars = tally_voucher.get('LEDGERENTRIES.LIST', [])
		voucher_particulars.extend(
			more_particulars if isinstance(more_particulars, (list,)) else [more_particulars])
		return voucher_particulars

	def getVoucherAccountingLedger(self, inventory_particulars_list=None):
		sales_ledger_id = None
		for particular in inventory_particulars_list:
			sales_ledgers = particular.get('ACCOUNTINGALLOCATIONS.LIST', [])
			sales_ledgers_list = sales_ledgers if isinstance(
				sales_ledgers, (list,)) else [sales_ledgers] if sales_ledgers else []
			if len(sales_ledgers_list) > 0:
				for ledger in sales_ledgers_list:
					if 'LEDGERNAME' in ledger:
						ledger_name = ledger['LEDGERNAME']
						if ledger_name in self.all_ledger_dict:
							sales_ledger_id = self.all_ledger_dict[ledger_name].values()[0]
		return sales_ledger_id

	@staticmethod
	def getItemParticulars(tally_voucher=None):
		j = 1
		grand_total = 0
		item_list = []
		voucher_particulars = ImportService.getVoucherParticulars(tally_voucher=tally_voucher)
		if len(voucher_particulars) > 0:
			for particular in voucher_particulars:
				if particular is not None:
					if particular.get('INVENTORYALLOCATIONS.LIST') is not None:
						led_inv_item = particular.get('INVENTORYALLOCATIONS.LIST', [])
						item_list = led_inv_item if isinstance(led_inv_item, (list,)) else [led_inv_item]

					j = j + 1
				if particular is not None and particular['ISPARTYLEDGER'] == "Yes":
					grand_total = abs(float(particular['AMOUNT'])) if 'AMOUNT' in particular else 0
		item_particulars_tally = tally_voucher.get(
			'ALLINVENTORYENTRIES.LIST', []) if 'ALLINVENTORYENTRIES.LIST' in tally_voucher else tally_voucher.get(
			'INVENTORYENTRIES.LIST', [])
		item_particulars = item_particulars_tally if isinstance(
			item_particulars_tally, (list,)) else [item_particulars_tally] if item_particulars_tally else []
		if item_list:
			item_particulars.extend(item_list)
		return {"particulars": item_particulars, "grand_total": grand_total}

	def getParty_id(
			self, voucher=None, enterprise=None, is_customer=None, is_supplier=None, party_type=None, user_id=None):
		party_id, party = None, None
		try:
			if 'PARTYLEDGERNAME' in voucher:
				party = voucher['PARTYLEDGERNAME'] in self.all_party_dict
			if party:
				party_det = self.all_party_dict.get(voucher['PARTYLEDGERNAME'], "None")
				if party_det:
					party_id = party_det['id']
					flags = party_det['con_flags']
					if is_customer:
						party_type = 4
					if is_supplier:
						party_type = 2
					if flags not in (party_type, 6):
						try:
							self.dao.db_session.begin(subtransactions=True)
							party_to_be_saved = self.dao.db_session.query(Party).filter(
								Party.name == voucher['PARTYLEDGERNAME'], Party.enterprise_id == enterprise.id).first()
							party_to_be_saved.config_flags = int(party_to_be_saved.config_flags) + int(party_type)
							self.dao.db_session.commit()
						except Exception as e:
							self.dao.db_session.rollback()
							raise e
						party_det['con_flags'] = party_to_be_saved.config_flags
						createPartyLedgers(
							party=party_to_be_saved, user_id=user_id, db_session=self.dao.db_session, is_customer=is_customer,
							is_supplier=is_supplier)
			else:
				if 'PARTYLEDGERNAME' in voucher and voucher['PARTYLEDGERNAME'] == 'Cash':
					try:
						self.dao.db_session.begin(subtransactions=True)
						code = ''.join(random.choice(string.ascii_uppercase + string.digits) for _ in range(6))
						party_to_be_saved = Party(
							code=code, name=voucher['PARTYLEDGERNAME'], enterprise_id=enterprise.id,
							currency=enterprise.home_currency_id, config_flags=6)
						self.dao.db_session.add(party_to_be_saved)
						self.dao.db_session.commit()
						self.dao.db_session.refresh(party_to_be_saved)
					except Exception as e:
						self.dao.db_session.rollback()
						raise e
					party_id = party_to_be_saved.id
		except Exception as e:
			logger.exception("Create Party %s" % e.message)
		return party_id

	def constructInvoice(
			self, voucher=None, enterprise=None, new_invoice_no=None, user_id=None, grand_total=None,
			invoice_particulars_list=None, voucher_particulars=None, party_id=None, invoice_type=None):
		try:
			voucher_details = voucher['EFFECTIVEDATE'] + "-" + voucher['VOUCHERTYPENAME'] + "-" + \
				(voucher['VOUCHERNUMBER'] if 'VOUCHERNUMBER' in voucher else "")
			project_code = self.dao.db_session.query(Project.id).filter(
				Project.enterprise_id == enterprise.id).first()
			if voucher_details in self.invoice_dict:
				invoice = self.dao.db_session.query(Invoice).filter(
					Invoice.id == self.invoice_dict[voucher_details]).first()
				try:
					self.dao.db_session.begin(subtransactions=True)
					invoice.items = []
					invoice.non_stock_items = []
					self.dao.db_session.commit()
					self.dao.db_session.refresh(invoice)
				except Exception as e:
					self.dao.db_session.rollback()
					raise e
				if invoice_type == 'GST':
					self.update_invoice_count = self.update_invoice_count + 1
				elif invoice_type == 'DC':
					self.update_dc_count = self.update_dc_count + 1
				elif invoice_type == 'Issue':
					self.update_issue_count = self.update_issue_count + 1
			else:
				invoice = Invoice()
				invoice.invoice_no = new_invoice_no
				if invoice_type == 'GST':
					self.invoice_count = self.invoice_count + 1
				elif invoice_type == 'DC':
					self.dc_count = self.dc_count + 1
				elif invoice_type == 'Issue':
					self.issue_count = self.issue_count + 1
				narration = ""
				voucher_details = voucher['EFFECTIVEDATE'] + "-" + voucher['VOUCHERTYPENAME'] + "-" + \
				                  (voucher['VOUCHERNUMBER'] if 'VOUCHERNUMBER' in voucher else "")
				if voucher.get('NARRATION', "None") != "None":
					narration = voucher['NARRATION']
				narration = str(narration) + " Date:" + voucher['EFFECTIVEDATE'] + " {TALLY_ID:" + voucher_details + "}"
				modifying_user = getUser(enterprise_id=enterprise.id, user_id=user_id)
				invoice.updateRemarks(remarks=narration, user=modifying_user)
				invoice.special_instruction = narration
			invoice.status = APPROVED_INVOICE
			invoice.approved_on = voucher['EFFECTIVEDATE']
			invoice.grand_total = grand_total
			invoice.project_code = project_code[0] if project_code else ''
			invoice.currency_id = enterprise.home_currency_id
			invoice.prepared_by = user_id
			invoice.approved_by = user_id
			invoice.prepared_on = voucher['EFFECTIVEDATE']
			invoice.party_id = party_id
			invoice.po_date = datetime.strptime(voucher['EFFECTIVEDATE'], "%Y%m%d").strftime("%Y-%m-%d")
			invoice.order_accept_date = datetime.strptime(voucher['EFFECTIVEDATE'], "%Y%m%d").strftime("%Y-%m-%d")
			invoice.enterprise_id = enterprise.id
			invoice.type = invoice_type
			invoice.issued_on = voucher['EFFECTIVEDATE']
			goods_already_received = False
			invoice.goods_already_supplied = False
			if 'REFERENCE' in voucher:
				instructions = voucher['REFERENCE']
				invoice.special_instruction = instructions
			if 'INVOICEDELNOTES.LIST' in voucher:
				if voucher.get('INVOICEDELNOTES.LIST', []) is not None:
					goods_already_received = True
					invoice.goods_already_supplied = True

			if 'INVOICEORDERLIST.LIST' in voucher:
				order_date = voucher.get('INVOICEORDERLIST.LIST', [])
				order_date_list = order_date if isinstance(
					order_date, (list,)) else [order_date] if order_date else []
				if len(order_date_list) > 0:
					for order_date in order_date_list:
						if 'BASICORDERDATE' in order_date:
							invoice.order_accept_date = datetime.strptime(order_date['BASICORDERDATE'], "%Y%m%d").strftime("%Y-%m-%d")

			item_abstract = self.constructInvoiceParticulars(
					invoice_particulars_list=invoice_particulars_list, voucher_particulars=voucher_particulars,
					enterprise=enterprise, goods_already_received=goods_already_received)
			if 'VOUCHERTYPENAME' in voucher:
				if voucher['VOUCHERTYPENAME'] == "Rejections Out":
					invoice.grand_total = item_abstract["invoice_particular_grand_total"]
			if item_abstract["sales_ledger_id"]:
				invoice.sale_account_id = item_abstract["sales_ledger_id"]
			else:
				invoice.sale_account_id = self.sales_ledger.id

			invoice.items = item_abstract["invoice_particulars_to_be_saved"]
			invoice.non_stock_items = item_abstract["invoice_ns_particulars_to_be_saved"]
			invoice.taxes = item_abstract["invoice_tax_to_be_saved"]

			return invoice
		except Exception as e:
			logger.exception("Invoice Creation %s" % e.message)
			raise e

	def constructInvoiceParticulars(
			self, invoice_particulars_list=None, voucher_particulars=None, enterprise=None, goods_already_received=None):
		invoice_particulars_to_be_saved = []
		invoice_ns_particulars_to_be_saved = []
		invoice_tax_to_be_saved = []
		invoice_particular_grand_total = 0
		entry_order = 0
		if len(invoice_particulars_list) > 0:
			item_dict = {}
			order_no = None
			dc_id = None
			sales_ledger_id = self.getVoucherAccountingLedger(inventory_particulars_list=invoice_particulars_list)
			for particular in invoice_particulars_list:
				if particular is not None:
					if 'RATE' in particular:
						rate = float(particular['RATE'].split('/')[0].strip())
						quantity = float(particular['BILLEDQTY'].strip().split(" ")[0])
					else:
						rate = float(particular['AMOUNT']) if 'AMOUNT' in particular else 0
						quantity = float(particular['BILLEDQTY'].strip().split(" ")[0]) if 'BILLEDQTY' in particular else 1
					if 'DISCOUNT' in particular:
						discount = float(particular['DISCOUNT'])
					else:
						discount = 0
					invoice_particular_grand_total = invoice_particular_grand_total + float(particular['AMOUNT']) if 'AMOUNT' in particular else 0
					order_id = 0
					batch_particulars = particular.get('BATCHALLOCATIONS.LIST', [])
					batch_particulars_list = batch_particulars if isinstance(
						batch_particulars, (list,)) else [batch_particulars] if batch_particulars else []
					if len(batch_particulars_list) > 0:
						for batch in batch_particulars_list:
							if 'ORDERNO' in batch:
								order_no = batch['ORDERNO']
								if order_no in self.oa_no_dict:
									order_id = self.oa_no_dict[order_no]
							if 'TRACKINGNUMBER' in batch:
								dc_no = batch['TRACKINGNUMBER']
								if dc_no in self.dc_no_dict:
									dc_id = self.dc_no_dict[dc_no]
					if 'STOCKITEMNAME' in particular:
						item_name = particular['STOCKITEMNAME']
						if order_id and order_no:
							stock_item_name = particular['STOCKITEMNAME'] + "-" + order_no
						else:
							stock_item_name = particular['STOCKITEMNAME']
						if stock_item_name in item_dict:
							item_dict[stock_item_name]['qty'] = item_dict[stock_item_name]['qty'] + quantity
							amount = round(quantity * rate, 2)
							item_dict[stock_item_name]['amount'] = item_dict[stock_item_name]['amount'] + amount
							item_dict[stock_item_name]['rate'] = item_dict[stock_item_name]['amount'] / item_dict[stock_item_name]['qty']
						else:
							item_dict.update({stock_item_name: {
								'item_name': item_name, 'qty': quantity, 'rate': rate, 'oa_no': order_id,
								'discount': discount, 'amount': round(quantity * rate, 2)}})

			for item, item_details in item_dict.iteritems():
				entry_order = entry_order + 1
				quantity = item_details['qty']
				oa_no = item_details['oa_no']
				if item_details['item_name'] in self.material_dict:
					item_id = self.material_dict.get(item_details['item_name'], "None")
					if item_id != "None":
						invoice_particular = InvoiceMaterial(
							item_id=item_id, enterprise_id=enterprise.id,
							quantity=quantity, rate=item_details['rate'], make_id=1, oa_no=oa_no, discount=item_details['discount'],
							delivered_dc_id=dc_id, entry_order=entry_order)
						invoice_particulars_to_be_saved.append(invoice_particular)
		tax_item_dict = {}
		if len(voucher_particulars) > 0:
			tax_item_dict = self.constructTaxItemDict(voucher_particulars=voucher_particulars)

		for tax, tax_item_details in tax_item_dict.iteritems():
			item_id = self.material_dict.get(tax_item_details['tax_item_name'], "None")
			if item_id != "None":
				entry_order = entry_order + 1
				invoice_particular = InvoiceMaterial(
					item_id=self.material_dict.get(tax_item_details['tax_item_name'], "None"), enterprise_id=enterprise.id,
					quantity=1, make_id=1, rate=tax_item_details['tax_item_amount'],  entry_order=entry_order)
				invoice_particulars_to_be_saved.append(invoice_particular)

		return {
			"invoice_particulars_to_be_saved": invoice_particulars_to_be_saved,
			"invoice_ns_particulars_to_be_saved": invoice_ns_particulars_to_be_saved,
			"invoice_tax_to_be_saved": invoice_tax_to_be_saved,
			"invoice_particular_grand_total": invoice_particular_grand_total,
			"sales_ledger_id": sales_ledger_id}

	def constructReceipt(
			self, voucher=None, enterprise=None, new_receipt_no=None, user_id=None, receipt_particulars_list=None,
			grand_total=0, voucher_particulars=None, party_id=None):
		try:
			voucher_details = voucher['EFFECTIVEDATE'] + "-" + voucher['VOUCHERTYPENAME'] + "-" + \
				(voucher['VOUCHERNUMBER'] if 'VOUCHERNUMBER' in voucher else "")
			user = getUser(enterprise_id=enterprise.id, user_id=user_id)
			project_code = self.dao.db_session.query(Project.id).filter(
				Project.enterprise_id == enterprise.id).first()
			if voucher_details in self.receipt_dict:
				conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
				cur = conn.cursor()
				grn_no = self.receipt_dict[voucher_details]
				cur.execute("DELETE FROM grn_material_tax WHERE grn_no = %s" % grn_no)
				cur.execute("DELETE FROM grn_material WHERE grnNumber = %s" % grn_no)
				cur.execute("DELETE FROM grn_tax WHERE grn_no = %s" % grn_no)
				cur.execute("DELETE FROM receipt_tags WHERE receipt_id = %s" % grn_no)
				conn.commit()
				conn.close()
				receipt = self.dao.db_session.query(Receipt).filter(Receipt.receipt_no == grn_no).first()
				self.update_receipt_count = self.update_receipt_count + 1
			else:
				receipt = Receipt(enterprise_id=enterprise.id)
				receipt.receipt_code = new_receipt_no
				self.receipt_count = self.receipt_count + 1
				narration = ""
				voucher_details = voucher['EFFECTIVEDATE'] + "-" + voucher['VOUCHERTYPENAME'] + "-" + \
				                  (voucher['VOUCHERNUMBER'] if 'VOUCHERNUMBER' in voucher else "")
				if voucher.get('NARRATION', "None") != "None":
					narration = voucher['NARRATION']
				narration = str(narration) + " Date:" + voucher['EFFECTIVEDATE'] + " {TALLY_ID:" + voucher_details + "}"
				modifying_user = user.first_name
				receipt.updateRemarks(remarks=narration, user=modifying_user)

			receipt.invoice_no = voucher['REFERENCE'] if 'REFERENCE' in voucher else ""
			receipt.receipt_date = voucher['EFFECTIVEDATE']
			receipt.inward_date = voucher['EFFECTIVEDATE']
			receipt.invoice_date = voucher['EFFECTIVEDATE']
			receipt.invoice_value = grand_total
			receipt.supplier_id = party_id
			receipt.received_by = user.username
			receipt.approved_on = voucher['EFFECTIVEDATE']
			receipt.approved_by = user_id
			receipt.inspected_by = user.first_name
			receipt.checked_by = user_id
			receipt.last_modified_by = user_id
			receipt.dr_note_amt = 0
			receipt.project_code = project_code[0] if project_code else ''
			goods_already_received = False
			receipt.goods_already_received = 0
			receipt.invoice_type = 2
			if 'VOUCHERTYPENAME' in voucher:
				if voucher['VOUCHERTYPENAME'] == 'Receipt Note':
					receipt.invoice_type = 1
			if 'INVOICEDELNOTES.LIST' in voucher:
				if voucher.get('INVOICEDELNOTES.LIST', []) is not None:
					receipt.goods_already_received = 1
					goods_already_received = True
			if 'REFERENCE' in voucher:
				instructions = voucher['REFERENCE']
				receipt.rejection_remarks = instructions

			item_abstract = self.constructReceiptParticulars(
					receipt_particulars_list=receipt_particulars_list, voucher_particulars=voucher_particulars,
					enterprise=enterprise, goods_already_received=goods_already_received)
			receipt.status = 1
			if item_abstract["order_id"] is not None:
				if item_abstract["order_type"]:
					receipt.received_against = 'Job Work'
				else:
					if 'VOUCHERTYPENAME' in voucher:
						if voucher['VOUCHERTYPENAME'] == "Material In":
							receipt.received_against = 'Job In'
						elif voucher['VOUCHERTYPENAME'] != "Purchase":
							receipt.received_against = 'Purchase Order'
						else:
							receipt.received_against = 'Others'
				receipt.status = 3
			elif party_id == 0:
				receipt.received_against = 'Issues'
			else:
				receipt.received_against = 'Others'
			if 'VOUCHERTYPENAME' in voucher:
				if voucher['VOUCHERTYPENAME'] == "Rejections In":
					receipt.invoice_value = item_abstract["receipt_particular_grand_total"]

			receipt.items = item_abstract["receipt_particulars_to_be_saved"]
			receipt.non_stock_items = item_abstract["receipt_ns_particulars_to_be_saved"]
			receipt.taxes = item_abstract["receipt_tax_to_be_saved"]

			return receipt
		except Exception as e:
			logger.exception("Receipt Creation %s" % e.message)
			raise e

	def constructReceiptParticulars(
			self, receipt_particulars_list=None, voucher_particulars=None, enterprise=None, goods_already_received=None):
		receipt_particulars_to_be_saved = []
		receipt_ns_particulars_to_be_saved = []
		receipt_tax_to_be_saved = []
		order_id, order_type, receipt_note_id = None, None, None
		receipt_particular_grand_total = 0
		if len(receipt_particulars_list) > 0:
			item_dict = {}
			for particular in receipt_particulars_list:
				if particular is not None:
					if 'RATE' in particular:
						rate = float(particular['RATE'].split('/')[0].strip())
						quantity = float(particular['BILLEDQTY'].strip().split(" ")[0])
					else:
						rate = float(particular['AMOUNT']) if 'AMOUNT' in particular else 0
						quantity = float(particular['BILLEDQTY'].strip().split(" ")[0]) if 'BILLEDQTY' in particular else 1
					if 'DISCOUNT' in particular:
						discount = float(particular['DISCOUNT'])
					else:
						discount = 0
					receipt_particular_grand_total = receipt_particular_grand_total + float(
						particular['AMOUNT']) if 'AMOUNT' in particular else 0
					batch_particulars = particular.get('BATCHALLOCATIONS.LIST', [])
					batch_particulars_list = batch_particulars if isinstance(
						batch_particulars, (list,)) else [batch_particulars] if batch_particulars else []
					if len(batch_particulars_list) > 0:
						for batch in batch_particulars_list:
							if 'ORDERNO' in batch:
								order_no = batch['ORDERNO']
								if order_no in self.po_no_dict:
									order_id = self.po_no_dict[order_no]["po_id"]
									order_type = self.po_no_dict[order_no]["po_type"]
							if 'TRACKINGNUMBER' in batch:
								receipt_note_no = batch['TRACKINGNUMBER']
								if receipt_note_no in self.receipt_note_no_dict:
									receipt_note_id = self.receipt_note_no_dict[receipt_note_no]
					if 'STOCKITEMNAME' in particular:
						stock_item_name = particular['STOCKITEMNAME']
						if stock_item_name in item_dict:
							item_dict[stock_item_name]['qty'] = item_dict[stock_item_name]['qty'] + quantity
							amount = round(quantity * rate, 2)
							item_dict[stock_item_name]['amount'] = item_dict[stock_item_name]['amount'] + amount
							item_dict[stock_item_name]['rate'] = item_dict[stock_item_name]['amount'] / item_dict[stock_item_name]['qty']
						else:
							item_dict.update({stock_item_name: {
								'qty': quantity, 'rate': rate, 'po_no': order_id, 'discount': discount, 'amount': round(quantity * rate, 2)}})
			for item, item_details in item_dict.iteritems():
				quantity = item_details['qty']
				po_no = item_details['po_no']
				if item in self.material_dict:
					receipt_particular = ReceiptMaterial(
						item_id=self.material_dict.get(item, "None"), enterprise_id=enterprise.id, quantity=quantity,
						received_qty=quantity, accepted_qty=quantity, rate=item_details['rate'], discount=item_details['discount'],
						make_id=1, po_no=po_no, received_grn_id=receipt_note_id)
					receipt_particulars_to_be_saved.append(receipt_particular)

		tax_item_dict = {}
		if len(voucher_particulars) > 0:
			tax_item_dict = self.constructTaxItemDict(voucher_particulars=voucher_particulars)

		for tax, tax_item_details in tax_item_dict.iteritems():
			if self.material_dict.get(tax_item_details['tax_item_name'], "None") != "None":
				receipt_particular = ReceiptMaterial(
					item_id=self.material_dict.get(tax_item_details['tax_item_name'], "None"), enterprise_id=enterprise.id,
					quantity=1, received_qty=1, accepted_qty=1, make_id=1, rate=tax_item_details['tax_item_amount'])
				receipt_particulars_to_be_saved.append(receipt_particular)

		return {
			"receipt_tax_to_be_saved": receipt_tax_to_be_saved,
			"receipt_particulars_to_be_saved": receipt_particulars_to_be_saved,
			"receipt_ns_particulars_to_be_saved": receipt_ns_particulars_to_be_saved,
			"receipt_particular_grand_total": receipt_particular_grand_total,
			"order_id": order_id, "order_type": order_type}

	@staticmethod
	def constructTaxDict(voucher_particulars=None):
		tax_dict = {}
		tax_list = ""
		for particular in voucher_particulars:
			if particular is not None:
				tax_particulars = particular.get('BASICRATEOFINVOICETAX.LIST', [])
				tax_particulars_list = tax_particulars if isinstance(
					tax_particulars, (list,)) else [tax_particulars] if tax_particulars else []
				if len(tax_particulars_list) > 0:
					for tax in tax_particulars_list:
						tax_percentage = tax['BASICRATEOFINVOICETAX']
						if tax_percentage in tax_code_dict:
							tax_list = tax_code_dict.get(tax_percentage, "None")
						if tax_percentage not in tax_dict and tax_percentage in tax_code_dict:
							tax_dict.update({tax_percentage: tax_list})
		return tax_dict

	@staticmethod
	def constructTaxItemDict(voucher_particulars=None):
		tax_item_dict = {}
		for particular in voucher_particulars:
			if particular is not None:
				if 'VATEXPAMOUNT' in particular:
					tax_item_name = particular['LEDGERNAME']
					tax_item_amount = particular['VATEXPAMOUNT']
					if tax_item_name:
						tax_item_dict.update({tax_item_name: {'tax_item_name': tax_item_name, 'tax_item_amount': tax_item_amount}})
		return tax_item_dict

	def constructPurchaseOrder(
			self, voucher=None, enterprise=None, voucher_type=None, user_id=None, grand_total=None,
			po_particulars_list=None, party_id=None):
		try:
			new_po_order_no = 0
			voucher_details = voucher['EFFECTIVEDATE'] + "-" + voucher['VOUCHERTYPENAME'] + "-" + \
				(voucher['VOUCHERNUMBER'] if 'VOUCHERNUMBER' in voucher else "")
			current_fy = getFinancialYear(
				for_date=datetime.strptime(voucher['EFFECTIVEDATE'], "%Y%m%d"),
				fy_start_day=enterprise.fy_start_day)
			project_code = self.dao.db_session.query(Project.id).filter(
				Project.enterprise_id == enterprise.id).first()
			if voucher_details in self.po_dict:
				po_id = self.po_dict[voucher_details]
				purchase_order = self.dao.db_session.query(PurchaseOrder).filter(
					PurchaseOrder.po_id == po_id).first()
				try:
					self.dao.db_session.begin(subtransactions=True)
					if len(purchase_order.items) > 0:
						purchase_order.items = []
					if len(purchase_order.non_stock_items) > 0:
						purchase_order.non_stock_items = []
					self.dao.db_session.commit()
					self.dao.db_session.refresh(purchase_order)
				except Exception as e:
					self.dao.db_session.rollback()
					raise e
				self.update_po_count = self.update_po_count + 1
			else:
				purchase_order = PurchaseOrder()
				if voucher_type == "Purchase Order":
					if current_fy in self.purchase_order_no_dict:
						new_po_order_no = self.purchase_order_no_dict[current_fy] + 1
						self.purchase_order_no_dict.update({current_fy: new_po_order_no})
					else:
						new_po_order_no = 1
						self.purchase_order_no_dict.update({current_fy: 1})
				elif voucher_type == "Job Work Out Order":
					if current_fy in self.job_order_no_dict:
						new_po_order_no = self.job_order_no_dict[current_fy] + 1
						self.job_order_no_dict.update({current_fy: new_po_order_no})
					else:
						new_po_order_no = 1
						self.job_order_no_dict.update({current_fy: 1})

				purchase_order.po_no = new_po_order_no
				self.po_count = self.po_count + 1
				narration = ""
				if voucher.get('NARRATION', "None") != "None":
					narration = voucher['NARRATION']
				narration = str(narration) + " Date:" + voucher['EFFECTIVEDATE'] + " {TALLY_ID:" + voucher_details + "}"
				modifying_user = getUser(enterprise_id=enterprise.id, user_id=user_id)
				purchase_order.updateRemarks(remarks=narration, user=modifying_user)

			purchase_order.status = 2
			purchase_order.approved_on = voucher['EFFECTIVEDATE']
			purchase_order.total = grand_total
			purchase_order.project_code = project_code[0] if project_code else ''
			purchase_order.currency_id = enterprise.home_currency_id
			purchase_order.prepared_by = user_id
			purchase_order.approved_by = user_id
			purchase_order.drafted_by = user_id
			purchase_order.prepared_on = voucher['EFFECTIVEDATE']
			purchase_order.drafted_on = voucher['EFFECTIVEDATE']
			purchase_order.order_date = voucher['EFFECTIVEDATE']
			purchase_order.last_modified_on = voucher['EFFECTIVEDATE']
			purchase_order.last_modified_by = user_id
			purchase_order.supplier_id = party_id
			purchase_order.po_date = datetime.strptime(voucher['EFFECTIVEDATE'], "%Y%m%d").strftime("%Y-%m-%d")
			purchase_order.enterprise_id = enterprise.id
			purchase_order.type = 0
			if 'VOUCHERTYPENAME' in voucher:
				if voucher['VOUCHERTYPENAME'] == "Job Work Out Order":
					purchase_order.type = 1

			purchase_order.indent_no = 0
			purchase_order.quotation_ref_no = ""
			purchase_order.quotation_date = datetime.strptime(voucher['EFFECTIVEDATE'], "%Y%m%d").strftime("%Y-%m-%d")
			purchase_order.payment = " "
			purchase_order.pay_in_days = 0
			purchase_order.pay_against = 0
			purchase_order.pay_through = 0
			purchase_order.transport = 0
			purchase_order.packing = 0
			purchase_order.purpose = " "
			instructions = None
			if 'REFERENCE' in voucher:
				instructions = voucher['REFERENCE']
				purchase_order.purpose = instructions
			if 'ADDITIONALNARRATION' in voucher:
				instructions = str(instructions) + "," + voucher['ADDITIONALNARRATION']
			if instructions:
				purchase_order.closing_remarks = instructions
			else:
				purchase_order.closing_remarks = " "
			purchase_order.tax1type = 0
			purchase_order.tax1 = 0
			purchase_order.tax2 = 0
			purchase_order.stock_type = 0
			purchase_order.user = user_id
			voucher_particulars = self.getVoucherParticulars(tally_voucher=voucher)
			item_abstract = self.constructPurchaseOrderParticulars(
				po_particulars_list=po_particulars_list, purchase_order=purchase_order, enterprise=enterprise,
				voucher_particulars=voucher_particulars)

			purchase_order.items = item_abstract["po_particulars_to_be_saved"]
			purchase_order.non_stock_items = item_abstract["po_ns_particulars_to_be_saved"]
			purchase_order.taxes = item_abstract["po_tax_to_be_saved"]
			if item_abstract["order_due_date"]:
				purchase_order.delivery = item_abstract["order_due_date"]

			return purchase_order
		except Exception as e:
			logger.exception("Purchase Order Creation %s" % e.message)
			raise e

	def constructPurchaseOrderParticulars(
			self, po_particulars_list=None, purchase_order=None, enterprise=None, voucher_particulars=None):
		po_particulars_to_be_saved = []
		po_ns_particulars_to_be_saved = []
		po_tax_to_be_saved = []
		order_due_date = None
		if len(po_particulars_list) > 0:
			item_dict = {}
			for particular in po_particulars_list:
				if particular is not None:
					if 'RATE' in particular:
						rate = float(particular['RATE'].split('/')[0].strip())
						quantity = float(particular['BILLEDQTY'].strip().split(" ")[0])
					else:
						rate = float(particular['AMOUNT']) if 'AMOUNT' in particular else 0
						quantity = float(particular['BILLEDQTY'].strip().split(" ")[0]) if 'BILLEDQTY' in particular else 1
					if 'DISCOUNT' in particular:
						discount = float(particular['DISCOUNT'])
					else:
						discount = 0
					if 'ORDERDUEDATE' in particular:
						order_due_date = datetime.strptime(particular['ORDERDUEDATE'], "%Y%m%d").strftime("%Y-%m-%d")
					if 'STOCKITEMNAME' in particular:
						stock_item_name = particular['STOCKITEMNAME']
						if stock_item_name in item_dict:
							item_dict[stock_item_name]['qty'] = item_dict[stock_item_name]['qty'] + quantity
						else:
							item_dict.update({stock_item_name: {'qty': quantity, 'rate': rate, 'discount': discount}})

			for item, item_details in item_dict.iteritems():
				quantity = item_details['qty']
				if item in self.material_dict:
					po_particular = PurchaseOrderMaterial(
						po_id=purchase_order.po_id, item_id=self.material_dict.get(item, "None"),
						enterprise_id=enterprise.id, quantity=quantity, rate=item_details['rate'], discount=item_details['discount'])
					po_particulars_to_be_saved.append(po_particular)
		tax_dict = {}
		if len(voucher_particulars) > 0:
			tax_dict = self.constructTaxDict(voucher_particulars=voucher_particulars)

		for tax, tax_details in tax_dict.iteritems():
			for item in tax_details:
				po_tax = PurchaseOrderTax(po_id=purchase_order.po_id, tax_code=item, enterprise_id=enterprise.id)
				po_tax_to_be_saved.append(po_tax)

		return {
			"po_particulars_to_be_saved": po_particulars_to_be_saved,
			"po_ns_particulars_to_be_saved": po_ns_particulars_to_be_saved,
			"po_tax_to_be_saved": po_tax_to_be_saved, 'order_due_date': order_due_date}

	def constructSalesOrder(
			self, voucher=None, enterprise=None, voucher_type=None, user_id=None, grand_total=None, oa_particulars_list=None,
			party_id=None, oa_dict=None):
		try:
			new_oa_no = 0
			current_fy = getFinancialYear(
				for_date=datetime.strptime(voucher['EFFECTIVEDATE'], "%Y%m%d"),
				fy_start_day=enterprise.fy_start_day)
			voucher_details = voucher['EFFECTIVEDATE'] + "-" + voucher['VOUCHERTYPENAME'] + "-" + \
				(voucher['VOUCHERNUMBER'] if 'VOUCHERNUMBER' in voucher else "")
			if voucher_details in oa_dict:
				order_acknowledge = self.dao.db_session.query(OA).filter(
					OA.id == oa_dict[voucher_details]).first()
				self.dao.db_session.begin(subtransactions=True)
				order_acknowledge.items = []
				order_acknowledge.non_stock_items = []
				self.dao.db_session.commit()
				self.dao.db_session.refresh(order_acknowledge)
				self.update_oa_count = self.update_oa_count + 1
			else:
				order_acknowledge = OA()
				if voucher_type == "Sales Order":
					if current_fy in self.order_acknowledgement_number_dict:
						new_oa_no = self.order_acknowledgement_number_dict[current_fy] + 1
						self.order_acknowledgement_number_dict.update({current_fy: new_oa_no})
					else:
						new_oa_no = 1
						self.order_acknowledgement_number_dict.update({current_fy: 1})
				elif voucher_type == "Job Work In Order":
					if current_fy in self.job_order_number_dict:
						new_oa_no = self.job_order_number_dict[current_fy] + 1
						self.job_order_number_dict.update({current_fy: new_oa_no})
					else:
						new_oa_no = 1
						self.job_order_number_dict.update({current_fy: 1})

				order_acknowledge.oa_no = new_oa_no
				self.oa_count = self.oa_count + 1
				narration = ""
				if voucher.get('NARRATION', "None") != "None":
					narration = voucher['NARRATION']
				narration = str(narration) + " Date:" + voucher['EFFECTIVEDATE'] + " {TALLY_ID:" + voucher_details + "}"
				modifying_user = getUser(enterprise_id=enterprise.id, user_id=user_id)
				order_acknowledge.updateRemarks(remarks=narration, user=modifying_user)
			order_acknowledge.status = 1
			order_acknowledge.approved_on = voucher['EFFECTIVEDATE']
			order_acknowledge.last_modified_by = user_id
			order_acknowledge.last_modified_on = voucher['EFFECTIVEDATE']
			order_acknowledge.grand_total = grand_total
			order_acknowledge.currency_id = enterprise.home_currency_id
			order_acknowledge.prepared_by = user_id
			order_acknowledge.approved_by = user_id
			order_acknowledge.prepared_on = voucher['EFFECTIVEDATE']
			order_acknowledge.delivery_due_date = voucher['EFFECTIVEDATE']
			order_acknowledge.party_id = party_id
			order_acknowledge.po_date = datetime.strptime(voucher['EFFECTIVEDATE'], "%Y%m%d").strftime("%Y-%m-%d")
			order_acknowledge.enterprise_id = enterprise.id
			order_acknowledge.type = "Sale"
			instructions = None
			if 'VOUCHERTYPENAME' in voucher:
				if voucher['VOUCHERTYPENAME'] == "Job Work In Order":
					order_acknowledge.type = "Job"
			if 'REFERENCE' in voucher:
				instructions = voucher['REFERENCE']
			if 'ADDITIONALNARRATION' in voucher:
				instructions = str(instructions) + "," + voucher['ADDITIONALNARRATION'] if voucher['ADDITIONALNARRATION'] else ""
			if instructions:
				order_acknowledge.special_instructions = instructions
			else:
				order_acknowledge.special_instructions = " "
			order_acknowledge.payment_terms = " "
			order_acknowledge.user = getUser(enterprise_id=enterprise.id, user_id=user_id)
			item_abstract = self.constructSalesOrderParticulars(
				oa_particulars_list=oa_particulars_list, order_acknowledge=order_acknowledge, enterprise=enterprise)
			order_acknowledge.items = item_abstract["oa_particulars_to_be_saved"]

			return order_acknowledge
		except Exception as e:
			logger.exception("Sales Order Creation %s" % e.message)
			raise e

	def constructSalesOrderParticulars(self, oa_particulars_list=None, order_acknowledge=None, enterprise=None):
		oa_particulars_to_be_saved = []
		if len(oa_particulars_list) > 0:
			item_dict = {}
			for particular in oa_particulars_list:
				if particular is not None:
					if 'RATE' in particular:
						rate = float(particular['RATE'].split('/')[0].strip())
						quantity = float(particular['BILLEDQTY'].strip().split(" ")[0])
					else:
						rate = float(particular['AMOUNT']) if 'AMOUNT' in particular else 0
						quantity = float(particular['BILLEDQTY'].strip().split(" ")[0]) if 'BILLEDQTY' in particular else 1
					stock_item_remarks = None
					item_remarks = particular.get('BASICUSERDESCRIPTION.LIST', [])
					item_remarks_list = item_remarks if isinstance(
						item_remarks, (list,)) else [item_remarks]
					for remarks in item_remarks_list:
						if 'BASICUSERDESCRIPTION' in remarks:
							remark = remarks['BASICUSERDESCRIPTION']
							stock_item_remarks = ", ".join(remark) if type(remark) is list else remark

					if 'STOCKITEMNAME' in particular:
						stock_item_name = particular['STOCKITEMNAME']
						if stock_item_name in item_dict:
							item_dict[stock_item_name]['qty'] = item_dict[stock_item_name]['qty'] + quantity
						else:
							item_dict.update({stock_item_name: {'qty': quantity, 'rate': rate, 'stock_item_remarks': stock_item_remarks}})

			for item, item_details in item_dict.iteritems():
				quantity = item_details['qty']
				if item in self.material_dict:
					oa_particular = OAParticulars(
						oa_id=order_acknowledge.id, item_id=self.material_dict.get(item, "None"),
						enterprise_id=enterprise.id,
						quantity=quantity, price=item_details['rate'], amount=quantity * item_details['rate'],
						remarks="")
					oa_particulars_to_be_saved.append(oa_particular)
		return {
			"oa_particulars_to_be_saved": oa_particulars_to_be_saved}

	def importLedgerBills(self, enterprise=None, my_dict=None, user=None, bill_type=None, voucher_type_dict=None):
		db_session = self.dao.db_session
		try:
			db_session.begin(subtransactions=True)
			ledger_bill_count = 0
			for _dict in my_dict:
				output_dict = json.loads(json.dumps(xmltodict.parse(tostring(_dict))))
				tally_voucher = output_dict['VOUCHER']
				voucher = None
				if tally_voucher['VOUCHERTYPENAME']:
					voucher_details = tally_voucher['EFFECTIVEDATE'] + "-" + tally_voucher['VOUCHERTYPENAME'] + "-" + \
						(tally_voucher['VOUCHERNUMBER'] if 'VOUCHERNUMBER' in tally_voucher else "")
					voucher_no = "{TALLY_ID:" + voucher_details + "}"
					voucher_type = voucher_type_dict.get(tally_voucher['VOUCHERTYPENAME'], "").title()
					if voucher_type in accounts_voucher_type_dict:
						if voucher_no:
							voucher = db_session.query(Voucher.id).filter(
								Voucher.narration.endswith(voucher_no),
								Voucher.enterprise_id == enterprise.id).first()
						voucher_particulars = self.getVoucherParticulars(tally_voucher=tally_voucher)
						if len(voucher_particulars) > 0:
							bill_dict = {}
							for particular in voucher_particulars:
								if particular is not None:
									if 'PARTYLEDGERNAME' in tally_voucher:
										is_party_ledger = True if tally_voucher['PARTYLEDGERNAME'] == particular[
											'LEDGERNAME'] else False
									else:
										is_party_ledger = False
									if particular.get('BILLALLOCATIONS.LIST') is not None:
										bill_particulars = particular.get('BILLALLOCATIONS.LIST', [])
										bills = bill_particulars if isinstance(bill_particulars, (list,)) else [
											bill_particulars]
										ledger_id = self.getLedgerId(
											ledger_name=particular['LEDGERNAME'], is_party_ledger=is_party_ledger,
											voucher_type=voucher_type, enterprise_id=enterprise.id, user_id=user)
										for bill in bills:
											if bill['BILLTYPE'] == bill_type:
												bill_det = str(bill['NAME']) + str(ledger_id)
												if bill_det in bill_dict:
													bill_dict[bill_det]['amount'] = bill_dict[bill_det][
														'amount'] + float(bill['AMOUNT'])
												else:
													bill_dict.update({
														bill_det: {
															'bill_no': str(bill['NAME']), 'ledger_id': ledger_id,
															'amount': float(bill['AMOUNT'])}})

							for bill, bill_details in bill_dict.iteritems():
								self.insertLedgerBill(
									enterprise_id=enterprise.id, bill_no=bill_details['bill_no'], amount=bill_details['amount'],
									ledger_id=bill_details['ledger_id'], voucher_id=voucher.id, db_session=db_session,
									bill_type=bill_type, bill_date=tally_voucher['EFFECTIVEDATE'])

								ledger_bill_count = ledger_bill_count + 1

			message = ""
			if ledger_bill_count > 0:

				if bill_type == "New Ref":
					message = str(ledger_bill_count) + " Bill Details Imported  \n"
				elif bill_type == "Agst Ref":
					message = str(ledger_bill_count) + " Bill Settlements Imported  \n"
				elif bill_type == "Advance":
					message = str(ledger_bill_count) + " Advance Details Imported  \n"
				tally_xml_message = ImportMessage(
					message=message, create_on=datetime.now(), created_by=user, enterprise_id=enterprise.id, is_process=False)
				db_session.add(tally_xml_message)
			logger.debug("Ledger Bills Dirty: %s" % db_session.dirty)
			db_session.commit()
		except Exception as e:
			db_session.rollback()
			logger.exception("Import Ledger Bill data %s" % e.message)
			raise e

	def getBillallocationList(self, my_dict=None, enterprise_id=None):
		bill_dict = {}
		for _dict in my_dict:
			output_dict = json.loads(json.dumps(xmltodict.parse(tostring(_dict))))
			tally_ledger = output_dict['LEDGER']
			tally_ledger_name = str(tally_ledger['@NAME']).rstrip()
			ledger = self.dao.db_session.query(Ledger).filter_by(
				name=tally_ledger_name, enterprise_id=enterprise_id).first()
			if not ledger:
				ledger = Ledger()
				if tally_ledger['PARENT']:
					ledger_name = account_ledger_map.get(tally_ledger_name, "None")
					if ledger_name != "None":
						ledger.group_id = self.dao.db_session.query(Ledger.group_id).filter_by(
							name=ledger_name, enterprise_id=enterprise_id).first().group_id
					else:
						if tally_ledger['PARENT']:
							parent_name = account_group_map.get(tally_ledger['PARENT'].title(), "None")
							logger.debug("Parent Name:%s" % parent_name)
							if parent_name != "None":
								ledger.group_id = self.dao.db_session.query(AccountGroup.id).filter_by(
									name=parent_name, enterprise_id=enterprise_id).first().id
							else:
								ledger.group_id = self.dao.db_session.query(AccountGroup.id).filter_by(
									name=tally_ledger['PARENT'], enterprise_id=enterprise_id).first().id

			if tally_ledger.get('BILLALLOCATIONS.LIST') is not None:
				bills = tally_ledger.get('BILLALLOCATIONS.LIST') if isinstance(
					tally_ledger.get('BILLALLOCATIONS.LIST'), (list,)) else [tally_ledger.get('BILLALLOCATIONS.LIST')]
				for bill in bills:
					bill_det = str(bill['NAME']) + str(ledger.id)
					if bill_det not in bill_dict:
						bill_dict.update({
							bill_det: {
								'bill_no': str(bill['NAME']), 'ledger_id': ledger.id,'bill_date': bill['BILLDATE'],
								'amount': float(bill['OPENINGBALANCE'])}})

		return bill_dict
