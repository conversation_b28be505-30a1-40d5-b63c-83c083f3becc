{% extends "stores/sidebar.html" %}
{% block material_history %}
{% if access_level.view %}
<style xmlns="http://www.w3.org/1999/html">
li.po_reports_side_menu a{
    outline: none;
    background-color: #e6983c !important;
}

.tr_thead {
	border: 1px solid #ddd !important;
	background:#edf2f8;
}

.tr_thead td {
	padding: 8px 10px !important;
    text-transform: capitalize;
}
</style>

<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/purchase_report.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-multiselect.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/load_material.js?v={{ current_version }}"></script>

<div class="right-content-container">
	<div class="page-title-container">
        <span class="page-title">Material Receipt</span>
    </div>
	<div class="container-fluid">
		<div class="view_table add_table row">
			<form action="/erp/stores/materialhistory/" id="id_material_history" method="post">{% csrf_token %}
				<div class="col-lg-12 view_list_table" style="align:center;">
					<div class="col-sm-3 form-group">
						<label>Date Range</label>
						<div id="reportrange" class="report-range form-control">
							<i class="glyphicon glyphicon-calendar"></i>&nbsp;
							<span></span> <b class="caret"></b>
							<input type="hidden" class="fromdate" id="fromdate" name="fromdate" value={{from_date}} />
							<input type="hidden" class="todate" id="todate" name="todate" value={{to_date}}/>
						</div>
					</div>

					<div class="col-sm-2">
						<label>Material Name</label>
						<select class="form-control chosen-select" name="item_details" id="item_details" >
							<option value="0">Select Material Name</option>
							{% for j in materials %}
							<option value="{{ j.0 }}" {% if j.0 == item_details %}selected{% endif %}>{{ j.1 }}</option>
							{% endfor %}
						</select>
					</div>

					<div class="col-sm-2">
						<label>Project</label>
						<select  name="project_id" id="project_id" multiple="multiple">
						</select>
					</div>

					<div id="stock_location" class="col-sm-2 multiselect_option">
						<label>Location</label>
						<select  name="location_id" id="location_id" multiple="multiple">
						</select>
					</div>

					<div class="col-sm-1">
		                <span class="po_title_txt">&nbsp;</span>
		                <div class="checkbox">
		                    <input {% if is_faulty %} checked="checked" {% endif %} name="is_faulty" value="{{is_faulty}}" id="id_is_faulty" type="checkbox"/>
		                    <label for="id_is_faulty">Faulty</label>
		                </div>
		            </div>
					<div class="col-sm-1" style="right: 43px;">
		                <span class="po_title_txt">&nbsp;</span>
		                <div class="checkbox">
		                    <input {% if approved_only %} checked="checked" {% endif %} name="approved_only" value="{{approved_only}}" id="id_approved_only" type="checkbox"/>
		                    <label for="id_approved_only">Exclude Drafts</label>
		                </div>
		            </div>

					<div class="col-sm-1" style="right: 54px;">
						<div class="material_txt"><span class="po_title_txt">&nbsp;</span>
							<input type="button" class="btn btn-save" value="View Report" id="refresh"/>
						</div>
					</div>
				</div>
			</form>
		</div>

		<div class="view_table add_table">
			<div class="col-lg-12 ">
				<div class="csv_export_button" style="top: -46px;">
                    <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#report-table'), 'Material_Receipt.csv']);" data-tooltip="tooltip" data-placement="bottom" title="Download&nbsp;Material&nbsp;Receipt&nbsp;as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
                </div>
				<table class="table custom-table table-striped table-bordered" id="report-table">
					<thead>
						<tr align="center" valign="middle">
							<th>S.No.</th>
							<th>Receipt/Issue No.</th>
							<th>Receipt/Issue Date</th>
							<th>Receipt Qty</th>
							<th>Issue Qty</th>
							<th>Rate/Unit</th>
							<th>Receipt Value</th>
							<th>Issue Value</th>
							<th>Supplier Name/Issue To</th>
							<th>Project/Tag</th>
							<th>Location</th>
						</tr>
					</thead>
					{% for report_item in report_rows %}
					<tr align="center" valign="middle">
						{% if report_item.receipt_date != None%}
						<td>{{ forloop.counter0}}.</td>
						{% else %}
						<td></td>
						{% endif %}
						{% if report_item.receipt_id != "" %}
							{% if report_item.receipt_type == 1 %}
								<td class="receipt_no">
									<form id="invoice_edit" method="post" action="/erp/sales/invoice/" target="_blank">
										<div style="display:none"><input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}"></div>
										<a class="edit_link_code" onclick="javascript:clickButton('editInvoice_{{report_item.receipt_id}}');">{{ report_item.receipt_no }}</a>
										<input type="hidden" value="{{report_item.receipt_id}}" id="id_invoice_no" name="invoice_no" >
										<input type="hidden" value="{{report_item.invoice_type}}" id="id_edit_dc_type" name="edit_dc_type" class="invoice_type_in_class">
										<input type="submit" value="Edit" id="editInvoice_{{report_item.receipt_id}}" hidden="hidden">
									</form>
								</td>
							{% else %}
								<td class='receipt_no'>
									<a role="button" class="edit_link_code" onclick="javascript:editReceipt('{{ report_item.receipt_id }}', '{{report_item.edit_link}}');">{{report_item.receipt_no}}</a>
								</td>
							{% endif %}
						{% else %}
							<td class='receipt_no'>{{ report_item.receipt_no }}</td>
						{% endif %}	
						<td>{{ report_item.receipt_date|date:"M d, Y" }}</td>
						<td class="text-right">{{ report_item.receipt_qty }}</td>
						<td class="text-right">{{ report_item.issue_qty }}</td>
						<td class="text-right">{{ report_item.rate }}</td>
						<td class="text-right">{{ report_item.receipt_value }}</td>
						<td class="text-right">{{ report_item.issue_value }}</td>
						<td class="text-left">{{ report_item.supplier_name}}</td>
						<td class="text-left">{{ report_item.project }}</td>
						<td class="text-left">{{ report_item.location }}</td>
					</tr>
					{% endfor %}
					<tfoot></tfoot>
				</table>
			</div>
		</div>
	</div>
</div>
<div class="hide">
	<form id="id-edit_receipt_form" method="POST" target="_blank" action="">{% csrf_token %}
		<input type="hidden" name="receipt_no" id="id-edit_receipt_no" value="" />
	</form>
</div>
<script>
	$(document).ready(function(){
		constructProject();
		fetchAndPopulateLocations();
		$("#project_id").next('.btn-group').find(".multiselect-container li:nth-child(1)").each(function(){
			$(this).addClass('li-select-all').find("label").append('<span class="checkmark"></span>');
		});
		$('#project_id').multiselect({
			includeSelectAllOption: true
		});
		$("#location_id").next('.btn-group').find(".multiselect-container li:nth-child(1)").each(function(){
			$(this).addClass('li-select-all').find("label").append('<span class="checkmark"></span>');
		});
		$('#location_id').multiselect({
			includeSelectAllOption: true
		});
		$('.nav-pills li').removeClass('active');
		$('#li_issue_report').addClass('active');
	})
var oTable;
var oSettings;
function TableHeaderFixed(){
	$("#report-table .receipt_no").each(function(){
		if($(this).text().trim() == 'Opening Balance') {
			var setThead = $(this).closest('tr').html();
			$(this).closest('tr').remove();
			$("#report-table thead").append("<tr class='tr_thead'>"+setThead+"</tr>");
		}
		if($(this).text().trim() == 'Closing Balance' || $(this).text() == 'Total') {
			var setTfoot = $(this).closest('tr').html();
			$(this).closest('tr').remove();
			$("#report-table tfoot").append("<tr class='tr_thead'>"+setTfoot+"</tr>");
		}
	});
	oTable = $('#report-table').DataTable({
		fixedHeader: true,
		"pageLength": 50,
		"search": {
			"smart": false
		},
		"bSortCellsTop": true,
		"columns": [
			null,null,
			{ "type": "date" },
			null,null,null,null,
			null,null,null
		]
	});
	oTable.on("draw",function() {
		var keyword = $('#report-table_filter > label:eq(0) > input').val();
		$('#report-table').unmark();
		$('#report-table').mark(keyword,{});
	});
	oTable.on('page.dt', function() {
	  $('html, body').animate({
		scrollTop: $(".dataTables_wrapper").offset().top - 15
	   }, 'slow');
	});
	oSettings = oTable.settings();
}

function editReceipt(receipt_no, formAction) {
    $("#id-edit_receipt_no").val(receipt_no);
    $("#id-edit_receipt_form").attr("action", formAction);
    $("#id-edit_receipt_form").submit();
}
const constructProject = async(current) => {
		var project_ids = {{project_id|safe}}
		console.log("project_ids", project_ids)
        const projectList = await getProjects();

       $('#project_id').empty();
       projectList.projects.forEach(project => {
           $('#project_id').append(new Option(`${project.name} (${project.code})`, project.id));
       });
       $('#project_id').multiselect('rebuild');
        project_ids.forEach(item => {
            $('#project_id').multiselect('select', item);
        });

};
	const fetchAndPopulateLocations = async () => {
    const locationListAll = await getLocations();
    const fromLocation = "from";
    locationList = locationListAll[fromLocation]
	var location_ids = {{location_id|safe}}
     $('#location_id').empty();
       locationList.forEach(location => {
           $('#location_id').append(new Option(`${location.name}`, location.id));
       });
       $('#location_id').multiselect('rebuild');
        location_ids.forEach(item => {
            $('#location_id').multiselect('select', item);
        });

        $('#location_id').multiselect('rebuild');
	};
</script>
{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
{% endblock %}