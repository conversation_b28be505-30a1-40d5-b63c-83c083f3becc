var material_choices = [];
var isDateChanged = false;
var isShortClosed = false;
$(document).ready(function() {
    $(".chosen-select").chosen();
    $('#loadingmessage_se_attachment').hide();
    CalculateSubTotal();
    qtyOnkeyPress();
    transformToDummyForm('se_particular');
    transformToDummyForm('se_tax');
    transformToDummyForm('tag');
    create_delete_tag_button();
    $("#se_add").trackChanges();
    $("#id_se_no_display_div").modal('hide');
    OnLoadTotalCalculation();
    validateNewAttachmentSe();
    seAttachmentSubmit();
    listSEAttachments();
    validateSEFileType();
    TagitFocus();
    populateUnitOptions("",'id_se_particular-__prefix__-all_units');
    materialListBlurEvent('id_se_particular-__prefix__-item_name');
    setTimeout(function(){
       $("#id_tag-__prefix__-tag").val("");
    },100);
    $('#cmdreview').addClass("hide");

    showDocumentListener();
    setTimeout(function(){
        $('.report-date').on('hide.daterangepicker', function(ev, picker) {
            isDateChanged = true;
        });
    },100);

    if (isNaN($("#id_se-party_id").val())) {
        $("#id_se-party_id").val($('#id_se-party_id optgroup:eq(0) option:eq(0)').val()).trigger("chosen:updated");
    } else {
        $("#id_se-party_id").trigger("chosen:updated");
    }
    $("#doc_uploader").click(function(){
    	$("#bill_copy_uploader").click();
    });

    $("#id_se-party_id").change(function () {
        if ($("#id_se-party_id").val()=='add_new_party') {
            $(".error-border").removeClass('error-border');
            $(".custom-error-message").remove();
            $('#modalPartyDetails').modal('show');
        }
    });

    if ($("#id_edit_data").val() != "") {
        var se_status = $('#id_se_status').val();

        if (se_status == 0) {
            $('#cmd_save_se').addClass("hide");
            $('#cmd_se_review').addClass("hide");
            $('#cmd_se_update').removeClass("hide");
            $('#se_generate_pdf').removeClass("hide");
        }
        else if (se_status == 1) {
            $('#cmd_save_se').addClass("hide");
            $('#se_generate_pdf').addClass("hide");
            $('#cmd_se_review').removeClass("hide");
        }
        else if (se_status == 2 || se_status == 3) {
            $('#cmd_save_se').addClass("hide");
            $('#cmd_se_review').addClass("hide");
            $('#se_generate_pdf').removeClass("hide");
            $('#amend_se').removeClass("hide");
            $('#cmd_se_update').addClass("hide");
        }
        else{
            $('#cmd_save_se').addClass("hide");
            $('#cmd_se_review').addClass("hide");
            $('#cmd_se_update').removeClass("hide");
        }
    }
    else{
        $('#se_generate_pdf').hide();
    }
    generateTaxList($("#id_edit_data").val());
    addSeTaxClickEvent();
    loadMaterial("onload");
    currencyChangeEvent("onload");
    updateProjectChosen($("#selected_project_code").val(), "id_se-project_code");
});

function sePrevNextPaging() {
    if($(".header_current_page").text().indexOf("New") >=0 || $(".header_current_page").text().trim() == "") {
        $(".prev_next_container").remove();
    } else {
        var seListNav = JSON.parse(localStorage.getItem('seListNav'));
        if(seListNav != null) {
            var curSeId = $("#id_se-id").val();
            for (var i = 0; i < seListNav.length; i++){
              if (seListNav[i].seId == curSeId){
                if(i != 0) {
                    var prevSeId = seListNav[i-1].seId;
                    var prevSeNo = seListNav[i-1].seNumber;
                }
                if(i != Number(seListNav.length - 1)) {
                    var nextSeId = seListNav[i+1].seId;
                    var nextSeNo = seListNav[i+1].seNumber;
                 }
              }
            }
            var PrevNextSE = "";
            if(prevSeId) {
                PrevNextSE += '<form id="se_edit_'+prevSeId+'" method="post" action="/erp/sales/sales_estimate/editSalesEstimate/">\
                                    <div style="display:none"><input type="hidden" name="csrfmiddlewaretoken" value="'+getCookie('csrftoken')+'"></div>\
                                    <a role="button" onclick="javascript:clickButton(\'editSE_'+prevSeId+'\');" class="btn btn-add-new pull-right btn-data-tooltip" data-tooltip="tooltip" title="Prev. SE: '+prevSeNo+'" style="margin-right: 7px;"><i class="fa fa-backward" aria-hidden="true"></i></a>\
                                    <input type="hidden" value="'+prevSeId+'" name="se_no">\
                                    <input type="submit" value="Edit" id="editSE_'+prevSeId+'" hidden="hidden">\
                                </form>';
            }
            else {
                PrevNextSE += '<form><a class="btn btn-add-new pull-right disabled btn-data-tooltip" data-tooltip="tooltip" title="Next/ Prev will appear only for the current page" style="padding: 5px 12px; margin-right: 7px;"><i class="fa fa-backward" aria-hidden="true"></i></a></form>';
            }
            if(nextSeId) {
                PrevNextSE += '<form id="se_edit_'+nextSeId+'" method="post" action="/erp/sales/sales_estimate/editSalesEstimate/">\
                                    <div style="display:none"><input type="hidden" name="csrfmiddlewaretoken" value="'+getCookie('csrftoken')+'"></div>\
                                    <a role="button" onclick="javascript:clickButton(\'editSE_'+nextSeId+'\');" class="btn btn-add-new pull-right btn-data-tooltip" data-tooltip="tooltip" title="Next SE: '+nextSeNo+'" style="margin-right: 7px;"><i class="fa fa-forward" aria-hidden="true"></i></a>\
                                    <input type="hidden" value="'+nextSeId+'" name="se_no">\
                                    <input type="submit" value="Edit" id="editSE_'+nextSeId+'" hidden="hidden">\
                                </form>';
            }
            else {
                PrevNextSE += '<form><a class="btn btn-add-new pull-right disabled btn-data-tooltip" data-tooltip="tooltip" title="Next/ Prev will appear only for the current page" style="padding: 5px 12px; margin-right: 7px;"><i class="fa fa-forward" aria-hidden="true"></i></a></form>';
            }
            $(".prev_next_container").html(PrevNextSE);
            TooltipInit();
        }
    }
}

function saveNewContact(){
    var ControlCollections = [
    {
        controltype: 'textbox',
        controlid: 'id_contact_name',
        isrequired: true,
        errormsg: 'Contact name is required.'
    },
    {
        controltype: 'textbox',
        controlid: 'id_contact_email',
        isemail: true,
        emailerrormsg: 'Invalid Email Address.'
    },
    {
        controltype: 'textbox',
        controlid: 'id_contact_no',
        isrequired: true,
        errormsg: 'Contact number is required.'
    }];

    var result = JSCustomValidator.JSvalidate(ControlCollections);
    if(result){
        $("#loading").show();
        $.ajax({
            url: "/erp/sales/sales_estimate/saveSalesContactPerson/",
            method: "POST",
            data:{
                name: $("#id_contact_name").val(),
                email: $("#id_contact_email").val().trim(),
                phone_no: $("#id_contact_no").val().trim(),
                fax_no: $("#id_contact_fax_no").val().trim()
            },
            success: function(response) {
                if (response.response_message == "Success") {
                    $("#sales_estimate_person").multiselect('destroy');
                    $('select#sales_estimate_person').append(
                        $(`<option value="${response.contact_id}">${response.contact_name}</option>`)
                    );
                    $("#sales_estimate_person").multiselect();
                    $("#add_sales_person_modal").modal('hide');
                    $("#add_sales_person_modal").find("input").val("");
                    swal({title: "", text: "Sales person added successfully!", type: "success"});
                }else if(response.response_message == "Failure"){
                    swal({title: "", text: response.custom_message, type: "warning"});
                }else {
                    swal({title: "", text: response.custom_message, type: "warning"});
                }
                $("#loading").hide();
            },
            error: function (xhr, errmsg, err) {
                $("#loading").hide();
                swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
            }
        });
    }
}

function approveAndRejectButtonClick(){
    var se_id = $("#id_se-id").val() ;
    var se_status = $("#id_se_status").val() ;
    var expiry_date = $('#expiry_date').val()
    generate_pdf_ajax(se_id, se_status, expiry_date);
    return false;
}

function submitForApprovalClick(){
    $("#cmd_submit_for_approval").val('draft');
    saveSalesEstimate('draft');
}

function saveSalesEstimate(type) {
    $(".error-border").removeClass('error-border');
    $(".custom-error-message, .hsn-suggestion").remove();

    var ControlCollections = [
        {
            controltype: 'dropdown',
            controlid: 'id_se-party_id',
            isrequired: true,
            errormsg: 'Party is required.'
        }
    ];

    if ($("#div_con_rate").is(":visible")){
        var control = {
            controltype: 'textbox',
            controlid: 'id_se-currency_conversion_rate',
            isrequired: true,
            errormsg: 'Required.',
            mindigit: 0.00001,
            mindigiterrormsg: 'Rate cannot be 0.'
        };
        ControlCollections[ControlCollections.length] = control;
    }
    $("#se_particulars_table").find("tr:visible").each(function(){
        var currentHsn = $(this).find(".se_hsn_code");
        var currentElementId = currentHsn.attr("id");
        if(currentElementId != "id_se_particular-__prefix__-hsn_code") {
            var control = {
                controltype: 'textbox',
                controlid: currentElementId,
                isrequired: true,
                errormsg: 'HSN/SAC is required',
                ishsn: true,
                hsnerrormsg: 'Invalid HSN Code',
                suggestioncontainer: "hsn-wrapper"
            }
            ControlCollections[ControlCollections.length] = control;            
        }
    });
    var result = JSCustomValidator.JSvalidate(ControlCollections);
    if(result) {
        var isValidated = false;
        if($("#id_se_particular-__prefix__-item_name").val() != "") {
            if($("#id_se_particular-__prefix__-unit_price").val() == 0 || $("#id_se_particular-__prefix__-unit_price").val() == "" || $("#id_se_particular-__prefix__-quantity").val() == "" || $("#id_se_particular-__prefix__-quantity").val() == 0 ||  $("#id_se_particular-__prefix__-item_code").val() == "" || $("#id_se_particular-__prefix__-item_name").val().indexOf($("#id_se_particular-__prefix__-item_code").val()) < 0) {
                AddNewMateriaRow();
                $("#tabs_stock").trigger('click');
            }
            else {
                AddNewMateriaRow();
                isValidated = true;
            }
        }
        else {
            isValidated = true;
        }
        if(isValidated) {
            setTimeout(function(){
                checkAndSaveSE(type);
            },100);
        }
    }
}

function SESuperEditInit(){
    if($("#is_super_user").val().toLowerCase() == 'true') {
        if($(".header_current_page").text().trim() == "" || $(".header_current_page").text().indexOf("New") >=0 || $(".header_current_page").text().indexOf("PF") >=0) {
            $(".super_user_icon, .xsid_number_edit, .super_user_tool_tip").remove();
            $(".super_edit_for_load").removeClass("hide");
        }
        else {
            $(".super_user_icon, .super_user_tool_tip").removeClass("hide");
            $(".super_edit_type, .super_edit_party").removeClass("hide");
            $(".super_edit_for_load").removeClass("hide");
            $('.super_user_tool_tip span').qtip({
               content: {
                    text: "<ul class='ul_for_tool_tip'><li>Super-edit enables modifying the SE Code subject to Duplication Check.</li>\
                               <li>Code format will be 'FY-FY/SE/OTNNNNNNx', <br />eg. '18-19/SE/L000731b'.<br />\
                               FY details - 5 characters (max), <br />SE Type - 1 characters, <br />Number - 6 digits (max), <br />Sub-number - 1 alphabet (max).</li>\
                               <li>Subsequent numbering of SE will pick from the highest of the SE Code, <br/> i.e., gaps will not be filled automatically.</li></ul>",

                    title: 'Super-Edit'
                }
            });
        }
    }
    else {
        $(".super_edit_type, .super_edit_party").removeClass("hide");
    }
}

function editSENumber(editable) {
    var seNumber = $(".header_current_page").text().trim();
    var seNumberSplit = seNumber.split("/");
    $("#se_financial_year").val(seNumberSplit[0]);
    $("#se_text").val(seNumberSplit[1]);
    $("#se_type").val($("#se_type option[data-val="+seNumberSplit[2].charAt(0)+"]").val())
    if($.isNumeric( seNumberSplit[2].substr(-1) )){
        $("#se_number").val(seNumberSplit[2].slice(1));
    }
    else {
        $("#se_number").val(seNumberSplit[2].slice(1, -1));
        $("#se_number_division").val(seNumberSplit[2].substr(-1));
    }
    if (editable) {
        $(".xsid_number_edit").removeClass("hide");
        $(".super_user_icon, .header_current_page").addClass("hide");
    }
}

function DiscardEditSENumber(){
    $(".xsid_number_edit").addClass("hide");
    $(".super_user_icon, .header_current_page").removeClass("hide");
    $("#se_financial_year, #se_text, #se_number, #se_number_division").val("");
    //$("#se_type").val(0);
}

function SaveSENumber(){
    if($("#se_financial_year").val() =="" || $("#se_number").val() == "" || $("#se_type").val() == "") {
        $(".save_xsid_error_format").removeClass("hide");
        if($("#se_financial_year").val() == "") $("#se_financial_year").addClass("super_edit_error_border");
        if($("#se_number").val() == "") $("#se_number").addClass("super_edit_error_border");
        if($("#se_type").val() == "") $("#se_type").addClass("super_edit_error_border");
    }
    else {
        $(".save_xsid_error_format").addClass("hide");
        $("#se_number_division").val($("#se_number_division").val().toLowerCase());
        $.ajax({
            url: "erp/sales/json/super_edit_se_code/",
            method: "POST",
            data:{
                se_id: $("#id_se-id").val(),
                new_financial_year: $("#se_financial_year").val().trim(),
                new_se_type: $("#se_type").val(),
                new_se_no: $("#se_number").val().trim(),
                new_sub_number: $("#se_number_division").val().trim()
            },
            success: function(response) {
                ga('send', 'event', 'Sales Estimate', 'Super-Edit Code', $('#enterprise_label').val(), 1);
                if (response.response_message == "Success") {
                    swal({title: "", text: response.custom_message, type: "success"});
                    DiscardEditSENumber();
                    $(".header_current_page, title").text(response.code);
                } else {
                    swal({title: "", text: response.custom_message, type: "warning"});
                }
            },
            error: function (xhr, errmsg, err) {
               swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
            }
        });
    }
}

function SuperEditSEDetails(field){
    $(field).closest("label").next(".div-disabled").removeClass("div-disabled");
    $(field).addClass("hide");
}

seRowcount = $("#se_particulars_table tbody").length - 1;

function SaveSEClick(){
    $(".error-border").removeClass('error-border');
    $(".custom-error-message, .hsn-suggestion").remove();
    var ControlCollections = [
        {
            controltype: 'dropdown',
            controlid: 'id_se-type',
            isrequired: true,
            errormsg: 'Type is required.'
        },
        {
            controltype: 'dropdown',
            controlid: 'id_se-project_code',
            isrequired: true,
            errormsg: 'Project is required.'
        },
        {
            controltype: 'dropdown',
            controlid: 'id_se-party_id',
            isrequired: true,
            errormsg: 'Customer is required.'
        }
    ];

    if ($("#div_con_rate").is(":visible")){
        var control = {
            controltype: 'textbox',
            controlid: 'id_se-currency_conversion_rate',
            isrequired: true,
            errormsg: 'Required.',
            mindigit: 0.00001,
            mindigiterrormsg: 'Rate cannot be 0.'
        };
        ControlCollections[ControlCollections.length] = control;
    }

    $("#se_particulars_table").find("tr:visible").each(function(){
        var currentHsn = $(this).find(".se_hsn_code");
        var currentElementId = currentHsn.attr("id");
        if(currentElementId != "id_se_particular-__prefix__-hsn_code") {
            var control = {
                controltype: 'textbox',
                controlid: currentElementId,
                isrequired: true,
                errormsg: 'HSN/SAC is required',
                ishsn: true,
                hsnerrormsg: 'Invalid HSN Code',
                suggestioncontainer: "hsn-wrapper"
            }
            ControlCollections[ControlCollections.length] = control;            
        }
    });

    var result = JSCustomValidator.JSvalidate(ControlCollections);
    var current_currency = $("#id_se-currency_id option:selected").text();
    var home_currency = $("#home_currency_id").val();
    var currency_conversion_reminder_message = `Rate of Currency Conversion from ${current_currency} to ${home_currency} is mentioned as 1.00`;
    var confirm_message =  "Do you confirm?";
    var allow_save_flag = true;
    var isValid = result;
    var index = 0;
    $("#se_particulars_table").find("tr").find('.se_hsn_code').each(function(){
        var hsn_name = $(this).attr('name')
        if(hsn_name != 'se_particular-__dummy__-hsn_code' && hsn_name != 'se_particular-__prefix__-hsn_code' && $(this).val() == "") {
            $(this).addClass("error-border");
            isValid = false;
        }
        index+=1;
    });
    if(isValid){
        if($("#id_se-currency_id option:selected").text()!=$("#home_currency_id").val()){
            if(parseFloat($("#id_se-currency_conversion_rate").val())==parseFloat(1)){
                confirm_message = currency_conversion_reminder_message + '\n' + confirm_message;
                allow_save_flag = false;
            }
        }
        if(!allow_save_flag){
            allow_save_flag = window.confirm(confirm_message);
            result = allow_save_flag
        }
        if(result) {
            SESaveFunction();
        }
        else {
            $("html, body").animate({ scrollTop: 0 }, "fast");
        }
    }
    return result;
}

function AmendSEClick() {
    if($("#se_add").isChanged() || isDateChanged || isTagChanged) {
        var se_status = $('#id_se_status').val();
        var amend_se = "True";
        if (se_status == 3){
            setTimeout(function(){
                swal({title: "", text: "You are about to Amend a Sales Estimate that was Approved by the Customer. <br /><br />Are you sure, you want to proceed with this Amendment?",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#209be1",
                confirmButtonText: "Yes, Sure!",
                closeOnConfirm: true,
                closeOnCancel: true
                },
                function(isConfirm){
                    if (isConfirm) {
                        AmendSE();
                    }
                });
            },150);
        }
        else{
            AmendSE();
        }
    }
    else{
        swal("", "Amending this SE is not necessary, as no change has been made for the SE!", "warning");
    }
}

function AmendSE() {
    $("#loading").show();
    $("#amend_se").val('Processing...').addClass('btn-processing');
//    if($('#id_se_status').val() != 0 && $('#id_se_status').val() != 1){
        editSENumber(false);
//    }
    if ($("#id_se-id").val() == "" || $("#is_super_user").val() == "False"|| $("#id_se-type").val() == $("#se_type").val())  {
        $("#se_add").submit();
        ga('send', 'event', 'Sales Estimate', 'Amend', $('#enterprise_label').val(), 1);
    }
    else {
        $.ajax({
            url: "erp/sales/json/super_edit_se_code/",
            method: "POST",
            data:{
                se_id: $("#id_se-id").val(),
                new_financial_year: $("#se_financial_year").val().trim(),
                new_se_type: $("#id_se-type").val(),
                new_se_no: $("#se_number").val().trim(),
                new_sub_number: $("#se_number_division").val().trim()
            },
            success: function(response) {
                if (response.response_message == "Success") {
                    $("#se_add").submit();
                    ga('send', 'event', 'Sales Estimate', 'Amend', $('#enterprise_label').val(), 1);
                } else {
                    $("#loading").hide();
                    $("#amend_se").val('Amend').removeClass('btn-processing');
                    swal({title: "", text: response.custom_message, type: "warning"});
                }
            },
            error: function (xhr, errmsg, err) {
                $("#loading").hide();
                $("#amend_se").val('Amend').removeClass('btn-processing');
               swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
            }
        });
    }
}
function TagitFocus() {
    $(".tagit-display").click(function(){
        $(this).find('input[type="text"]').focus();
    });
}

$(function() {
    try {
        $('select#id_se-currency_id').change(function(){
            currencyChangeEvent("onchange");
        });

        $("select#id_se-party_id").change(function(){
            var party_id = $("#id_se-party_id").val();
            ChangePartyCurrency(party_id,"id_se-currency_id","id_se-currency_conversion_rate");
            currencyChangeEvent("onchange");
        });

    } catch(e) {
        console.log(e);
    }
});

function checkAndSaveSE(type) {
    if(type == 'draft') {
        SaveSEClick();
    }
    else if(type == 'amend') {
       AmendSEClick();
    }
    return;
}

function SESaveFunction(){
    var item_count = parseFloat($("#id_se_particular-TOTAL_FORMS").val());
    var item_list = 0;

    if($('#id_se-party_id option:selected').val() == "None"){
        swal('Please Select a Party');
        return;
    }
    for(i=0;i<item_count;i++){
        if(!document.getElementById("id_se_particular-"+i+"-DELETE").checked){
            item_list = item_list+1
        }
    }
        $('#se_generate_pdf').hide();
        $('#cmdSave').show();
        $('#cmd_se_review').addClass("hide");
    if(item_list == 0) {
        swal('','Please add atleast one material to the SE!','error');
    }
    else {
            $("#loading").show();
            $("#cmd_save_se").val('Processing...').addClass('btn-processing');
            $("#se_add").submit();
            var event_action = $("#id_se-id").val() == '' ? 'Create' : 'Update';
            ga('send', 'event', 'Sales Estimate', event_action, $('#enterprise_label').val(), 1);
    }
}


function BindMaterialList(material_choices, textid) {
    $("#"+textid).autocomplete({
        source: material_choices,
        minLength: 1,
        focus: function (event, ui) {
            event.preventDefault();
        },
        select: function (event, ui) {
            $(this).closest('tr').find('.se_material_text').val(ui.item.value);
            $(this).closest('tr').find('.se_material_code').val(ui.item.code);
            $(this).closest('tr').find('.se_unitPrice').val(ui.item.rate);
            $(this).closest('tr').find('.se_unit').text(ui.item.unit);
            $(this).closest('tr').find('.se_quantity').find('input').focus();
        }
   });
}

function create_delete_tag_button(){
    $(".delete_tag").click(function(){
        $(this).closest('li').remove();
    });
}

$(document).on('click', '.browse', function(){
    var file = $(this).parent().parent().parent().find('.file');
    file.trigger('click');
});

$(document).on('change', '.file', function(){
    $(this).parent().find('.form-control').val($(this).val().replace(/C:\\fakepath\\/i, ''));
});

// Delete The SE Material list Items
function deleteSEParticulars(selectedItem) {
    swal({
        title: "Are you sure?",
        text: "Do you want to delete this Item!",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#209be1",
        confirmButtonText: "Yes, delete it!",
        closeOnConfirm: true
    },
    function(){
        var deleteFlag = document.getElementById('id_' + selectedItem + '-DELETE');
        var deleteRow = document.getElementById(selectedItem);
        deleteFlag.checked = true;
        deleteRow.style.display = 'none';
        deleteRow.classList.add("deleted-row");
        CalculateGrandTotal();
        enableEditButton();
//            NetTotalCalculation();
    });
}

function qtyOnkeyPress() {
    $(".se_quantity").keypress(function(e){
        if (e.which == 13) {
           AddNewMateriaRow();
        }
    });
}

function OnLoadTotalCalculation() {
    $(".se_price").each(function(){
        var cUnitPrice = $(this).closest('tr').find('.se_unitPrice').val();
        var cQty = $(this).closest('tr').find('.se_quantity').val();
        var cDiscount = $(this).closest('tr').find('.se_discount').val();
        var discount_val = ((cUnitPrice * cQty) * (cDiscount/100))
        $(this).val(Number((cUnitPrice*cQty) - discount_val).toFixed(2));
    });
    CalculateGrandTotal();
}

function CalculateSubTotal() {
    $('.se_quantity, .se_unitPrice, .se_discount').blur(function(){
        var cUnitPrice = $(this).closest('tr').find('.se_unitPrice').val();
        var cQty = $(this).closest('tr').find('.se_quantity').val();
        var cDiscount = $(this).closest('tr').find('.se_discount').val();
        var discount_val = ((cUnitPrice * cQty) * (cDiscount/100))
        $(this).closest('tr').find('.se_price').val(Number((cUnitPrice*cQty) - discount_val).toFixed(2));
        CalculateGrandTotal();
    });
}

function salesPersonInit(){
    if($("#sales_person_details").val() && $("#sales_person_details").val() != "") {
        var salesListPerson = JSON.parse($("#sales_person_details").val());
        for (i = 0; i < salesListPerson.length; i++) {
            $("#sales_estimate_person").multiselect('select', salesListPerson[i]);
        }   
    }
}

function CalculateGrandTotal() {
    var i;
    var round_off =0;
    var simple_rates = document.getElementsByName('net_rate');
    var compound_rates = document.getElementsByName('net_rate_compound');
    var tax_wise_subtotal = document.getElementsByName('tax');
    var compound_tax_wise_subtotal = document.getElementsByName('tax_compound');
    var simple_assess_rates = document.getElementsByName('asses_rate');
    var compound_assess_rates = document.getElementsByName('asses_rate_compound');
    var item_taxes = document.getElementsByName('item_tax');

    if(!isNaN(parseFloat($("#id_se-round_off").val()))){
		round_off = parseFloat(document.getElementById("id_se-round_off").value);
	}else{
		round_off =0;
	}

    //Calculate The Total with out Tax
	var item_count = parseFloat($("#id_se_particular-TOTAL_FORMS").val());
	var i,j, total= 0,discount_val =0,assess_total=0,net_value_for_tax=0;
    //	Calculate The Net Value
        for (i=0; i<simple_rates.length; i++){tax_wise_subtotal[i].value=0;}
        for (i=0; i<compound_rates.length; i++){compound_tax_wise_subtotal[i].value=0;}
        var compound_tax_total = 0;
        var compound_tax = 0;
        var net_tax =0 ;
    for(j=0;j<item_count;j++){
        // This if condition is used to avoid deleted item in calculation
            if(!document.getElementById("id_se_particular-"+j+"-DELETE").checked){
                $("#id_se_particular-"+j+"-price").val(calculateAssessValue($("#id_se_particular-"+j+"-quantity").val(), $("#id_se_particular-"+j+"-unit_price").val(), $("#id_se_particular-"+j+"-discount").val(), 0));
                total += parseFloat($("#id_se_particular-"+j+"-price").val());
                item_taxes[j].value = 0;
                if(!isNaN(parseFloat($("#id_se_particular-"+j+"-quantity").val())) && parseFloat($("#id_se_particular-"+j+"-quantity").val()) > 0){
                        var item_simple_tax = calculateItemTax($("#id_se_particular-"+j+"-quantity").val(), $("#id_se_particular-"+j+"-unit_price").val(), $("#id_se_particular-"+j+"-discount").val(), simple_rates, simple_assess_rates, tax_wise_subtotal, false, 0);
                        item_taxes[j].value = parseFloat(item_taxes[j].value) + parseFloat(item_simple_tax) +
                                parseFloat(calculateItemTax($("#id_se_particular-"+j+"-quantity").val(), $("#id_se_particular-"+j+"-unit_price").val(), $("#id_se_particular-"+j+"-discount").val(), compound_rates, compound_assess_rates, compound_tax_wise_subtotal, true, parseFloat(item_simple_tax)));
                        net_tax = parseFloat(net_tax) + parseFloat(item_taxes[j].value);
                }
            }
    }

    var gTotal = 0;
    var net_total = 0;

    $('table').find('.se_price').each(function(){
        if(!$(this).closest("tr").hasClass("deleted-row")) {
            gTotal += Number($(this).val());
        }
    });
    $('.se_total_amt').text(gTotal.toFixed(2));
    net_total = parseFloat(gTotal) + parseFloat(round_off) + parseFloat(net_tax);
 	$("#id_se-grand_total").val(net_total.toFixed(2));
 	$("#grand_total_value").val(net_total.toFixed(2)).attr("value", net_total.toFixed(2));
}

function calculateAssessValue(quantity, price, discount, assess_rate){
    var value = (parseFloat(quantity) * parseFloat(price)).toFixed(2);
    if (( 100-parseFloat(discount)) > parseFloat(assess_rate)){
        return (value * (100- parseFloat(discount))/100).toFixed(2);
    }else{
        return (value * parseFloat(assess_rate)/100).toFixed(2);
    }
}

function calculateItemTax(quantity, unit_rate, discount, tax_rates, assess_rates, tax_subtotal, is_compound, cascading_item_tax){
    var item_tax = 0;
    var tax_count = tax_rates.length;
    // Calculating the net Taxes
    for (i=0; i<tax_count; i++){
        var item_assess_value = calculateAssessValue(quantity, unit_rate, discount, assess_rates[i].value);
        if (is_compound){
            var sub_total = Math.round((parseFloat(item_assess_value) + parseFloat(cascading_item_tax)) * parseFloat(tax_rates[i].value))/100;
            cascading_item_tax = parseFloat(cascading_item_tax) + parseFloat(sub_total);
        } else {
            var sub_total = Math.round(parseFloat(item_assess_value) * parseFloat(tax_rates[i].value))/100;
        }
        tax_subtotal[i].value = (parseFloat(tax_subtotal[i].value) + parseFloat(sub_total)).toFixed(2);
        item_tax = parseFloat(item_tax) + parseFloat(sub_total);
    }
    return item_tax;
}

function AddNewMateriaRow() {
    $(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();
    $(".suggestion-container").remove();
    var ControlCollections = [
        {
            controltype: 'textbox',
            controlid: 'id_se_particular-__prefix__-item_name',
            isrequired: true,
            errormsg: 'Material Name is required.'
        },
        {
            controltype: 'textbox',
            controlid: 'id_se_particular-__prefix__-quantity',
            isrequired: true,
            errormsg: 'Quantity is required.',
            mindigit: 0.001,
            mindigiterrormsg: 'Quantity is required.'
        },
        {
            controltype: 'textbox',
            controlid: 'id_se_particular-__prefix__-unit_price',
            isrequired: true,
            errormsg: 'Unit Rate is required.'
        },
        {
            controltype: 'textbox',
            controlid: 'id_se_particular-__prefix__-hsn_code',
            isrequired: true,
            errormsg: 'HSN/SAC is required.',
            ishsn: true,
            hsnerrormsg: 'Invalid HSN Code.'
        }
    ];
    if($('#id_se_particular-__prefix__-discount').val()==''){
        $('#id_se_particular-__prefix__-discount').val('0.00');
    }
    var result = JSCustomValidator.JSvalidate(ControlCollections);
    if(result) {
        if ($('#material_id_hidden').val() != "") {
            if ($("#id_se_particular-__prefix__-item_code").val()== ""){
    	        swal("","Invalid Material. Please select the Material again.","error");
                $("#id_se_particular-__prefix__-item_name").addClass('error-border');
    	        return;
    	    }

            enterprise_id = $("#id_se-enterprise_id").val();
            $( "#id_se_particular-__prefix__-enterprise_id").val(enterprise_id);

            var materialtable = document.getElementById("se_particulars_table");
            var materialrowCount = materialtable.rows.length-1;
            var match = false;
            var row_id = 0;

            // Update the repeated values in the sales estimate material table...
            for(j=0;j<materialrowCount;j++){
                if($('#id_se_particular-__prefix__-item_code').val() == $('#id_se_particular-'+parseInt(j) +'-item_code').val() && $('#id_se_particular-__prefix__-make_id').val() == $('#id_se_particular-'+parseInt(j) +'-make_id').val() && !document.getElementById("id_se_particular-"+j+"-DELETE").checked ){
                   match = true;
                   row_id = j;
                }
            }
            if(match){
                if (!document.getElementById("id_se-")){
                    if (window.confirm('Item has already been added to the Order. Do you still want to add to its quantity?')) {
                        if ($("#id_se_particular-__prefix__-alternate_units:visible").length >= 1){
                            if ($("#id_se_particular-__prefix__-alternate_units option:selected").attr('data-val') !=0) {
                                var alternate_unit_id = 0
                                var scale_factor =1
                                if ($("#id_se_particular-"+row_id+"-alternate_unit_id").val() !="" && $("#id_se_particular-"+row_id+"-alternate_unit_id").val()!='undefined'){
                                    var alternate_unit_id = $("#id_se_particular-"+row_id+"-alternate_unit_id").val()
                                }

                                scale_factor = $('#id_se_particular-__prefix__-alternate_units option[value="'+alternate_unit_id+'"]').attr("data-val")
                                var existing_qty = parseFloat($("#id_se_particular-"+row_id+"-quantity").val()) * scale_factor;
                                var current_qty = parseInt($("#id_se_particular-__prefix__-quantity").val()) * $("#id_se_particular-__prefix__-alternate_units option:selected").attr('data-val')
                                var rate = parseFloat($("#id_se_particular-"+row_id+"-unit_price").val()) / scale_factor;
                                $("#id_se_particular-"+row_id+"-quantity").val(existing_qty + current_qty)
                                $("#id_se_particular-"+row_id+"-unit_price").val(rate)
                                $("#id_se_particular-"+row_id+"-alternate_unit_id").val("");
                                $("#id_se_particular-"+row_id+"-scale_factor").val("1.000");
                                $("#id_se_particular-"+row_id+"-unit_id").val($('#id_se_particular-__prefix__-alternate_units option[value="0"]').text())
                            }
                        }else{
                            $("#id_se_particular-"+row_id+"-quantity").val(parseInt($("#id_se_particular-__prefix__-quantity").val()) +parseInt($("#id_se_particular-"+row_id+"-quantity").val()));
                            $("#id_se_particular-"+row_id+"-price").val((parseFloat($("#id_se_particular-"+row_id+"-quantity").val()) *parseFloat($("#id_se_particular-"+row_id+"-unit_price").val())).toFixed(2));
                            $("#id_se_particular-__prefix__-price").val('');
                        }
                    }
                }
                else {
                    $('#id_se_particular-__prefix__-price').val('');
                    CalculateGrandTotal();
                }
                match=false;
            }
            else{
                //generateFormsetFormRowBefore('se_particular');
                if($("#id_se_particular-__prefix__-is_service").val() == 1) {
                    generateFormsetFormRowAppend('se_particular', ".item-for-service");
                    $(".item-for-service").removeClass('hide');
                }
                else {
                    generateFormsetFormRowAppend('se_particular', ".item-for-goods");
                    $(".item-for-goods").removeClass('hide');
                }
                copySEEmptyForm('se_particular', parseInt($('#id_se_particular-TOTAL_FORMS').val()) - 1, 'se_particular-se_id', 'se_id');
                var index = parseInt(parseInt($('#id_se_particular-TOTAL_FORMS').val()) - 1) ;
                var s_no = document.getElementById("id_se_particular-" + index + "-s_no");
                s_no.innerHTML = index+1;

            }
            clearItemContainer();
            CalculateGrandTotal();
            CalculateSubTotal();
        }
        else {
            if ($('#material_id_hidden').val() == "" && $('#id_se_particular-__prefix__-item_name').val() != ""){
                var materialName = $("#id_se_particular-__prefix__-item_name").val();
                var materialUnit = $('#id_se_particular-__prefix__-all_units').val();
                $('#id_se_particular-__prefix__-alternate_units').html("");
                var materialHsn = $('#id_se_particular-__prefix__-hsn_code').val();
                var materialPrice = $("#id_se_particular-__prefix__-unit_price").val();
                addNewMaterial(materialName, materialUnit, materialHsn, materialPrice);
            }
        }
    }
}

function clearItemContainer() {
    $('#id_se_particular-__prefix__-quantity').val(0.00)
    $("#id_se_particular-__prefix__-alternate_units").closest(".alternate_unit_select_box").addClass("hide");
    $("#id_se_particular-__prefix__-unit_id").removeClass('hide')
    $('#id_se_particular-__prefix__-unit_price').val(0.00)
    $("#id_se_particular-__prefix__-unit_id").val('') ;
    $('#id_se_particular-__prefix__-item_name').val("");
    $('#id_se_particular-__prefix__-item_code').val("");
    $('#id_se_particular-__prefix__-item_id').val("");
    $('#id_se_particular-__prefix__-hsn_code').val("");
    $('#id_se_particular-__prefix__-item_name').focus();
    $("#description_display").text('');
        $("#material_id_hidden").val("");
        $("#id_se_particular-__prefix__-item_name").removeAttr("readonly");
        $(".material-removal-icon").addClass("hide");
}

function copySEEmptyForm(form_prefix, form_idx, fk_field_name, new_form_fk_field_name) {

    var new_form_item_code = document.getElementById('id_' + form_prefix + '-' + form_idx + '-item_code');
    var new_form_item_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-item_id');
    var new_form_item_name = document.getElementById('id_' + form_prefix + '-' + form_idx + '-item_name');
    var new_form_qty = document.getElementById('id_' + form_prefix + '-' + form_idx + '-quantity');
    var new_form_units = document.getElementById('id_' + form_prefix + '-' + form_idx + '-unit_id');
    var new_form_price = document.getElementById('id_' + form_prefix + '-' + form_idx + '-unit_price');
    var new_form_hsn_code = document.getElementById('id_' + form_prefix + '-' + form_idx + '-hsn_code');
    var new_form_discount = document.getElementById('id_' + form_prefix + '-' + form_idx + '-discount');
    var new_form_amount = document.getElementById('id_' + form_prefix + '-' + form_idx + '-price');
    var new_form_enterprise_id= document.getElementById('id_'+ form_prefix +'-'+form_idx+'-enterprise_id');
    var new_form_is_faulty = document.getElementById('id_' + form_prefix + '-' + form_idx + '-is_faulty');
    var new_form_is_service = document.getElementById('id_' + form_prefix + '-' + form_idx + '-is_service');
    var new_form_alternate_unit_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-alternate_unit_id');
    var new_form_scale_factor = document.getElementById('id_' + form_prefix + '-' + form_idx + '-scale_factor');

    new_form_item_code.value = document.getElementById('id_' + form_prefix + '-__prefix__-item_code').value;
    new_form_item_id.value = document.getElementById('id_' + form_prefix + '-__prefix__-item_id').value;
    new_form_qty.value = document.getElementById('id_' + form_prefix + '-__prefix__-quantity').value;
    new_form_units.value = document.getElementById('id_'+form_prefix +'-__prefix__-unit_id').value;
    new_form_price.value = document.getElementById('id_' + form_prefix + '-__prefix__-unit_price').value;
    new_form_hsn_code.value = document.getElementById('id_' + form_prefix + '-__prefix__-hsn_code').value;
    new_form_discount.value = document.getElementById('id_' + form_prefix + '-__prefix__-discount').value;
    new_form_amount.value = document.getElementById('id_'+form_prefix + '-__prefix__-price').value;
    new_form_enterprise_id.value = document.getElementById('id_'+form_prefix +'-__prefix__-enterprise_id').value;
    new_form_is_service.value = document.getElementById('id_'+form_prefix +'-__prefix__-is_service').value;

    var item_name = document.getElementById('id_se_particular-__prefix__-item_name').value
    if ($('#id_se_particular-__prefix__-make_id').val() != 1){
        var new_form_make_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-make_id');
        new_form_make_id.value = $('#id_se_particular-__prefix__-make_id').val();
    }else{
        var new_form_make_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-make_id');
        new_form_make_id.value = 1;
    }

    if (document.getElementById('id_' + form_prefix + '-__prefix__-alternate_units').value != 0 ) {
        new_form_units.value = $('#id_se_particular-__prefix__-alternate_units option:selected').text();
        new_form_alternate_unit_id.value = $('#id_se_particular-__prefix__-alternate_units option:selected').val();
        new_form_scale_factor.value = $('#id_se_particular-__prefix__-alternate_units option:selected').attr("data-val");
    } else{
        new_form_units.value = document.getElementById('id_'+form_prefix +'-__prefix__-unit_id').value;
    }

    if($("#chkfaulty").is(':checked')){
       new_form_is_faulty.value = 1;
       item_name = item_name + "[Faulty]"
    }else{ new_form_is_faulty.value = 0; }

    new_form_item_name.value = item_name
    if(new_form_is_service.value == 1) {
        $('<span class="service-item-flag"></span>').insertAfter( new_form_item_name );
        $(new_form_item_name).addClass("item_text_box");
        $("#se_particulars_table .service-item-flag").addClass("floated-right-flag");
    }
    document.getElementById('id_se_particular-__prefix__-item_name').value = '';
    document.getElementById('id_se_particular-__prefix__-item_code').value = '';
    document.getElementById('id_se_particular-__prefix__-quantity').value = '';
    document.getElementById('id_se_particular-__prefix__-unit_price').value = '';
    document.getElementById('id_se_particular-__prefix__-hsn_code').value = '';
    document.getElementById('id_se_particular-__prefix__-discount').value = '';
    document.getElementById('id_se_particular-__prefix__-price').value = '';
    document.getElementById('id_se_particular-__prefix__-unit_id').value ='';
} /* Copy Form End Here */


function convertToBase64() {
	var fileExtension = ['jpeg', 'jpg', 'png', 'pdf'];
    if($("#bill_copy_uploader").val()) {
    	if ($.inArray($("#bill_copy_uploader").val().split('.').pop().toLowerCase(), fileExtension) == -1) {
    		swal("Invalid File Format", "Please upload only "+fileExtension.join(', ')+" file format.", "warning");
    		setTimeout(function(){
    			$("#bill_copy_uploader").val('').clone(true);
    			$("#bill_copy_uploader").closest('form').find('.bootstrap-filestyle').find('input.form-control ').val('');
    			$("#id_se-document_data").val('');
                var billcopy="";
                $(".uploaded_doc_preview").html(billcopy);
    	   },200);
    	}
    	else {
    		var selectedFile = document.getElementById("bill_copy_uploader").files;
    		if (selectedFile.length > 0) {
    			var fileToLoad = selectedFile[0];
    			var fileReader = new FileReader();
    			var base64;
    			fileReader.onload = function(fileLoadedEvent) {
    				base64 = fileLoadedEvent.target.result;
    				var filename = $("#bill_copy_uploader").val().replace(/C:\\fakepath\\/i, '');
                    $("#id_se-document_data").val(base64);
                    $("#id_se-document").val(filename);
                    if(filename != "") {
                        var extension = filename.replace(/^.*\./, '');
                        var billcopy = "<span class='document_attachment linked_text base64 "+extension.toLowerCase()+"' onclick='viewAttachedDocument(this)' data-extension='"+extension.toLowerCase()+"' data-url='"+base64+"'></span>";
                    }
                    else {
                        var billcopy="";
                    }
                    $(".uploaded_doc_preview").html(billcopy);
    			};

    			fileReader.readAsDataURL(fileToLoad);
    		}
    	}
    }
}

function showDocumentListener(){
    $(".document_attachment").each(function() {
        var self = $(this);
        filename = self.attr('data-url');
        if (filename != null) {
            var extension = filename.replace(/^.*\./, '').toLowerCase();
            self.attr('data-extension', extension).addClass(extension);
        }
    });
}

$(function () {
    $('.modal').on('hidden.bs.modal', function(e) {
        $(this).find('.error-border').removeClass("error-border");
        $(this).find('.custom-error-message').remove();
    });
});

/* This Function is used to Load SE Items List to the Drop Down Box*/
function loadMaterial(loadType = "") {
    var dataToSend = {
        'type': $("#id_se-type option:selected").val(),
        'party_id': $("#id_se-party_id option:selected").val(),
        'module': 'order_acknowledgement',
        'particulars':'se_particulars'
    }
    var local_material_list  = JSON.parse(localStorage.getItem('goods_service_master_data'));
    if(loadType == "onchange" && local_material_list) {
        var material_choices = local_material_list[0].material;
    }
    else {
        var material_choices = generateMaterialAsAutoComplete(dataToSend);
    }
    var frequently_material_choices = generateFrequentlyMaterialAsAutoComplete(dataToSend);
    $("#id_se_particular-__prefix__-item_name").materialAutocomplete({
        source: frequently_material_choices.concat(material_choices),
        minLength:1,
        response: function( event, ui ) {
            ui.content.unshift({value:"add_new_item", label:"+ ADD NEW MATERIAL ", category: ""});
        },
        open: function(event, ui) {
            $("ul.ui-autocomplete.ui-menu a:contains('+ ADD NEW MATERIAL')").addClass("add_new_material_link");
        },
        select: function (event, ui) {
            event.preventDefault();
            if(ui.item.value != "add_new_item") {
                var itemName = ui.item.label.replace("<span class='service-item-flag'></span>", '');
                $("#description_display").text('');
                $("#id_se_particular-__prefix__-item_name").val(itemName).attr("readonly", true);
                $("#id_se_particular-__prefix__-item_code").val(ui.item.id);
                $("#id_se_particular-__prefix__-item_id").val(ui.item.id);
                $("#id_se_particular-__prefix__-make_id").val(ui.item.mid);
                 $("#id_se_particular-__prefix__-is_service").val(ui.item.is_service);
                $("#material_id_hidden").val(ui.item.id);
                loadPartyRate()
                $("#id_se_particular-__prefix__-discount").val(ui.item.discount);
                $('#id_se_particular-__prefix__-alternate_units').html("");
                $("#id_se_particular-__prefix__-unit_id").val(ui.item.unit) ;
                if($('#is_multiple_units').val()=="True" ){
                    if (ui.item.alt_uom > 0){
                        loadAlternateUnits(ui.item.id, ui.item.unit, '#id_se_particular-__prefix__-alternate_units');
                        $("#id_se_particular-__prefix__-unit_id").addClass('hide')
                        $("#id_se_particular-__prefix__-alternate_units").closest(".alternate_unit_select_box").removeClass("hide");
                        $("#id_se_particular-__prefix__-all_units").closest(".all_units_select_box").addClass("hide");
                    }
                    else {
                        $("#id_se_particular-__prefix__-alternate_units").closest(".alternate_unit_select_box").addClass("hide");
                        $("#id_se_particular-__prefix__-unit_id").removeClass('hide')
                    }
                }
                //$("#description_display").text(ui.item.description);
                $(".material-removal-icon").removeClass("hide");
            }
            else {
                openMaterialAddModal();
            }
        }
    }).on('focus', function() { $(this).keydown(); });
}

$(function () {
    $('#edit_catalogue_form').submit(function() {
        $.ajax({
            data: $(this).serialize(),
            type: $(this).attr('method'),
            url: $(this).attr('action'),
            success: function(response) {
                $('#loading').hide();
                $("#description_display").html('');
                $("#material_id_hidden").val(response['item_id']);
                $("#id_se_particular-__prefix__-item_id").val(response['item_id']);
                $("#id_se_particular-__prefix__-item_code").val(response['item_id']);
                $("#id_se_particular-__prefix__-item_name").val(response['name']).attr("readonly","readonly");
                $("#id_se_particular-__prefix__-item_name").closest("tr").find(".material-removal-icon").removeClass("hide");
                $("#id_se_particular-__prefix__-make_id").val(response['make_id']);
                $("#id_se_particular-__prefix__-unit_id").val(response['unit_name']);
                $("#id_se_particular-__prefix__-is_service").val(response['is_service']);
                $("#id_se_particular-__prefix__-hsn_code").val(response['hsn_code'])
                $("#id_se_particular-__prefix__-unit_price").val(response['price'].toFixed(5))
                if(!$('#add_material_modal').hasClass('in')) {
                    $("#add_new_material").trigger("click");
                }
                $('#add_material_modal').modal('hide');
                setTimeout(function(){
                    $("#id_se_particular-__prefix__-hsn_code").focus();
                },250);
            }
        });
        return false;
    });

    $("select#id_se-party_id,#id_se-type").change(function(){
        loadMaterial("onchange");
        var party_id = $("#id_se-party_id").val();
        ChangePartyCurrency(party_id,"id_se-currency_id","id_se-currency_conversion_rate");
    });
});

function loadPartyRate(){
    var item_id = $('#id_se_particular-__prefix__-item_id').val();
    var party_id = $("#id_se-party_id option:selected").val()
    var make_id = $('#id_se_particular-__prefix__-make_id').val()
    $("#material_rate_hidden").val("0.00000");
    var se_type = $("#id_se-type option:selected").val()
    if ($('#id_se_particular-__prefix__-item_code').val()!="") {
        $.ajax({
            url: "/erp/sales/json/invoice/loadPartyRate/",
            type: "post",
            datatype: "json",
            data: {party_id:party_id,item_id:item_id,make_id:make_id,oa_type:se_type},
            success: function(response) {
                $("#id_se_particular-__prefix__-unit_price").val(response.item_rate.price);
                $("#material_rate_hidden").val(response.item_rate.price);
                $("#id_se_particular-__prefix__-hsn_code").val(response.hsn_code);
	        }
        });
    }
}


function prepareReportModal() {
    if ($("#seReport").text() == "") {
        $("body").append(`<div id="seReport" class="modal fade" role="dialog">
            <div class="modal-dialog modal-lg"><div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Materials Received</h4>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="id_job_se" name="job_se_id" value="">
                    <table class="table table-bordered custom-table table-striped" id="grn_material_list">
                    <thead><tr>
                    <th width="5%"> S.No. </th>
                    <th width="12%"> GRN No</th>
                    <th width="40%"> Item Details</th>
                    <th width="20%"> Quantity</th>
                    <th width="9%"> Unit rate</th>
                    <th width="5%"> Discount</th>
                    </tr></thead>
                    <tbody></tbody>
                    </table>
                </div>
                <div class="modal-footer">
                    <a class="btn btn-cancel" data-dismiss="modal">Close</a>
                </div>
            </div></div></div>`);
    }
}

function showReceivedItems() {
    prepareReportModal();
    $.ajax({
        url: "/erp/sales/json/se/job_se_received/",
        type: "post",
        datatype: "json",
        data: {se_id: $("#id_se-id").val()},
        success: function(response){
            $("#grn_material_list").find("tbody").html('');
            $.each(response, function (i, item) {
                if (item.is_stock == 0){ classname='no-bg' } else { classname='tr-alert-danger' }
                var pdf_generation_form = `<a role="button" onclick="openReceipt(${item.receipt_no});">${item.code}</a>`;
                row="<tr align='center' class="+classname+" >"+
                    "<td width='5%'>"+(i+1)+"</td>"+
                    "<td width='12%'>"+ pdf_generation_form +"</td>"+
                    "<td width='15%'>"+ item.item_name +"</td>"+
                    "<td width='20%' align='left'>" +item.accepted_qty+"</td>"+
                    "<td width='9%' align='right'>"+item.rate+"</td>"+
                    "<td width='9%' align='right'>"+item.discount+"</td>"+
                    "</tr>";
                $('#grn_material_list').append(row);
            });
            if($("#grn_material_list tbody").find("tr").length <= 0) {
                var row = "<tr><td colspan='8' class='text-center' style='font-weight: bold;'>No Records Found!</td></tr>";
                $('#grn_material_list').append(row);
            }
        }
    });
    $("#seReport").modal('show');
}

var showJobSeUsageReport = function () {
    showUsageReport(url="/erp/sales/json/se/job_se_usage/", params={se_id: $("#id_se-id").val()});
}

function openNotesOverlay() {
        $("#notesEditableContainer").modal("show");
}

function addSENote() {
        var notes = CKEDITOR.instances.notes_editor.getData();
        $("#id_se-notes").html(notes);
        $("#id_se-notes").next("div").html(notes);
        $("#notesEditableContainer").modal("hide");
        isFormChanged = true;
}

function deleteSETax(catTaxFormId) {
	var deleteFlag = document.getElementById('id_' + catTaxFormId + '-DELETE');
	var deleteRow = document.getElementById(catTaxFormId);
	deleteFlag.checked = true;
	deleteRow.style.display = 'none';
	isFormChanged = true;
}

function addSeTaxClickEvent() {
        $("#add_se_tax").click(function(){
                $(".error-border").removeClass('error-border');
                $(".custom-error-message").remove();
                var ControlCollections = [
                        {
                                controltype: 'dropdown',
                                controlid: 'se_tax_list',
                                isrequired: true,
                                errormsg: 'Tax type is required.'
                        }
                ];
                var result = JSCustomValidator.JSvalidate(ControlCollections);
                if(result) {
                        AddSETaxClick();
                }
                return result;
        });
}

function AddSETaxClick() {
        var selected_item =$("#se_tax_list option:selected").val();
        enterprise_id = $("#id_se-enterprise_id").val();
        if($("#se_tax_table").find("tr[name*='"+selected_item+"']").length){
                swal("Tax Already Added...");
        }
        else{
                /* Fetching Assessment Value */
                generateFormsetFormRowAtIndex('se_tax', '#description_display');
                copyTaxEmptyForm('se_tax',parseInt($('#id_se_tax-TOTAL_FORMS').val()) - 1);
                seTaxListAdding();
                isFormChanged = true;
        }
        CalculateGrandTotal();
}

function seTaxListAdding(){
	$.ajax({
        url: "/erp/sales/json/invoice/getTax/",
        type: "post",
        datatype:"json",
        data: {tax_code: $("#se_tax_list option:selected").val() },
        success: function(response){
			$.each(response, function(i, tax_detail_as_row){
				$('#se_tax_table').append(tax_detail_as_row);
            });
        // Calculate the Total Price after applying new Tax profile
            CalculateGrandTotal();
        },
        error: function (xhr, errmsg, err) {
           console.log(xhr.status + ": " + xhr.responseText);
        }
    });
	$("#se_tax_list option:selected").hide(); // Remove the added Tax from the Tax-list to avoid multiple addition
	$("#se_tax_list").val('None');
	$('.chosen-select').trigger('chosen:updated');
}

function generateTaxList(se_id){
	if (se_id != ""){
        $.ajax({
            url: "/erp/sales/json/invoice/editTax/",
            type: "post",
            datatype: "json",
            data: { se_id: se_id},
            success: function (response) {
                $.each(response, function (i, se_tax_detail_as_row) {
                    $('#se_tax_table').append(se_tax_detail_as_row).addClass('tbl');
                    CalculateGrandTotal();
                });
				$("#se_tax_table").find('.hnd_text_id').each(function(){
					var selectedTax = $(this).val();
					$("#se_tax_list option[value='"+selectedTax+"']").hide();
				});
				$('.chosen-select').trigger('chosen:updated');
			}
		});
	}
}

function removeInvoiceTax(tax_code){
	var data_count = document.getElementById('se_tax_table').rows.length - 1;
	var row_id=0;
	var param_tax;
	for(i=0;i<=data_count;i++){
		if($("#id_se_tax-"+i+"-tax_code").val()== tax_code)
			row_id = i;
    }
	tax_rows = document.getElementsByName('tr_' + tax_code);
	for(i=tax_rows.length - 1; i>=0; i--){
		// Removed in reverse order as the removal renumbers the RowIndices immediately
		document.getElementById("se_tax_table").deleteRow(tax_rows[i].rowIndex);
	}
	$("#se_tax_list option[value='"+ tax_code +"']").show();
	$('.chosen-select').trigger('chosen:updated');
	param_tax ="se_tax-"+row_id;

	deleteSETax(param_tax);
	CalculateGrandTotal();
	isFormChanged = true;
    // Re-show the tax in the Tax list
}

/*Tax Copy Processing Start here...*/
function copyTaxEmptyForm(form_prefix, form_idx) {
	var new_form_tax_code = document.getElementById('id_' + form_prefix + '-' + form_idx + '-tax_code');
	var new_form_enterprise_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-enterprise_id');
	var new_form_se_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-se_id');

	new_form_tax_code.value = document.getElementById('id_' + form_prefix + '-__prefix__-tax_code').value;
	new_form_enterprise_id.value = document.getElementById('id_' + form_prefix + '-__prefix__-enterprise_id').value;
	new_form_se_id.value = document.getElementById('id_' + form_prefix + '-__prefix__-se_id').value;

	document.getElementById('id_' + form_prefix + '-__prefix__-tax_code').value = '';
	document.getElementById('id_' + form_prefix + '-__prefix__-enterprise_id').value = '';
	document.getElementById('id_' + form_prefix + '-__prefix__-se_id').value = '';
}/*Tax Copy Processing End here...*/

$(function() {
    try {
        $("select#se_tax_list").change(function(){
               $("#id_se_tax-__prefix__-tax_code").val($(this).find("option:selected").attr("value"));
                $("#id_se_tax-__prefix__-se_id").val($("#id_se-id").val());
                $("#id_se_tax-__prefix__-enterprise_id").val($("#id_se-enterprise_id").val());

        });

    } catch(e) {
        console.log(e);
    }
});

// Attachment file

function showUploadSalesEstimationModal() {
    $("#upload_se_modal").modal('show');
    $("#sehideUploadButton").attr("disabled", true);
    $("#upload_se_modal").find(".error-border").removeClass('error-border');
    $("#upload_se_modal").find(".custom-error-message").remove();
    var se_id = $('#id_se-id').val();
    var url = window.location.href;

    $(".attachment-title").text(se_id);
    $('#upload_se_modal').on('hidden.bs.modal', function () {
        $("#attachmentupload").filestyle('clear');
        $("#document_label").val("");
    });
    getGCSKeySEDataMaterial();
}

function closeSEUpload() {
        $("#attachmentupload").filestyle('clear');
        $("#upload_se_modal").modal("hide");
}

function validateSEFileType(){
    $("#attachmentupload").change(function(){
            var fileExtension = ['csv', 'xls', 'xlsx', 'doc', 'docx', 'pdf', 'jpg', 'jpeg', 'png', 'svg', 'gif', 'txt', 'log', 'md', 'json'];
            if($("#attachmentupload").val()) {
            if ($.inArray($("#attachmentupload").val().split('.').pop().toLowerCase(), fileExtension) == -1) {
                    $("#sehideUploadButton").attr("disabled", true);
                    swal("Invalid File Format", "Please upload only "+fileExtension.join(', ')+" file format.", "warning");
                    setTimeout(function(){
                    $("#attachmentupload").filestyle('clear');
               },200);
            }
            else if(this.files[0].size/(1024*1024) > 16){
                    $("#sehideUploadButton").attr("disabled", true);
                    swal("File Size Exceeds", "Please select a lower size file. Maximum file size limit is 16MB", "warning");
                    setTimeout(function(){
                    $("#attachmentupload").filestyle('clear');
               },200);
            }
            else {
                    $("#sehideUploadButton").removeAttr("disabled");
            }
        }
    });
}

function deleteSEAttachmentFile(se_id, description, attachment_id) {
        $("#attachmentPreview").modal("hide");
        var material_name = $("#searchResult").find("input[value='"+se_id+"']").closest("td").text();
        swal({
                title: "Are you sure?",
                text: "You want to remove the attachment <b>"+description+"</b> from the <b>"+material_name+"</b> - <b>["+se_id+"]</b> profile.",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#209be1",
                confirmButtonText: "Yes, delete it!",
                closeOnConfirm: true
        },
        function(isConfirm){
                if(isConfirm){
                deleteSalesEstimationAttachment(se_id, attachment_id);
            }
        });
}

function deleteSalesEstimationAttachment(se_id, attachment_id){
        $.ajax({
        url : "/erp/sales/json/se/delete_se_attachment/",
        type : "POST",
        dataType: "json",
        data : {
            se_id: se_id,
            attachment_id: attachment_id
        },
        success : function(data) {
                if (data.response_code == 200) {
            swal({title: "File Deleted Successfully", text: "Your Attachment has been successfully deleted", type: "success"});
                        }
                        else if(data.response_code == 400){
                swal({title: "Not deleted.", text: "Attachment not saved successfully", type: "danger"});
                        }
                        listSEAttachments();
        },error : function(xhr,errmsg,err) {
            console.log(xhr.status + ": " + xhr.responseText);
            $("#loading").addClass('hide').removeClass('show');
        }
    });
}

function validateNewAttachmentSe(){
        $("#sehideUploadButton").click(function(){
                $(".error-border").removeClass('error-border');
            $(".custom-error-message").remove();

             var ControlCollections = [
                {
                    controltype: 'textbox',
                    controlid: 'document_label',
                    isrequired: true,
                    errormsg: 'Description is required.'
                },
                {
                    controltype: 'file',
                    controlid: 'attachmentupload',
                    isrequired: true,
                    errormsg: 'Attachment is required.'
                }
                ];
                var result = JSCustomValidator.JSvalidate(ControlCollections);
                if(result){
                    ajaxSEMaterialSubmit();
                }
                return result;
        });
}

function getGCSKeySEDataMaterial(){
	    var field_set_html = "";
	    return $.ajax({
	            url: '/erp/commons/json/get_gcs_security_key/',
	            type: "post",
	            dataType: "json",
	            data:{},
	            success: function(response) {
	                $.each(response['policy']['fields'], function(key, value){
	                    field_set_html += "  <input name='"+ key +"' value='"+ value +"' type='hidden'/>\n";
	                });
	                document.getElementById('se_attachment_uploading').action = response['policy']['url'];
	                $("#se_attachment_uploading").prepend(field_set_html);
	            },
	            error: function (xhr, errmsg, err) {
	                swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
	            }
	       });
	}

function ajaxSEMaterialSubmit(element, callback=function(element, boolean1){}){
    var form = document.querySelector('form#se_attachment_uploading');
    var data = new FormData(form);
    var url = $('form#se_attachment_uploading').attr('action');
    $.ajax({
       type: "POST",
       url: url,
       data: data,
       //  below should be false for GCS file upload
       contentType: false,
       processData: false,
       success: function(data)
       {
            console.log('Hurray file uploaded successfully');
            callback(element, true);
       },
       error : function(xhr,errmsg,err) {
            console.log(xhr.status + ": " + xhr.responseText);
            callback(element, false);
       }
    });
}

function seAttachmentSubmit(){
        $("#se_attachment_uploading").submit(function(e){
            e.preventDefault();
            ajaxSEMaterialSubmit(this, callback=seAttachmentSubmitSuccess);
            $("#sehideUploadButton").attr("disabled", true);
        });
}

function seAttachmentSubmitSuccess(element, isUploaded){
    if(isUploaded){
        var formData = new FormData(element);
    formData.append('csrfmiddlewaretoken', $("#se_attachment_upload input[name=csrfmiddlewaretoken]").val());
    formData.append('gcs_key', $("#se_attachment_uploading input[name=key]").val());
    formData.append('se_id', $('#id_se-id').val());
    formData.append('label', $("#document_label").val());
    setTimeout(function(){
        $.ajax({
            url:"/erp/sales/json/se/se_attachment_upload/",
            type: 'POST',
            data: formData,
            async: false,
            cache: false,
            contentType: false,
            processData: false,
            success: function (data) {
                    $("#attachmentupload").filestyle('clear');
                    $("#document_label").val('');
                    listSEAttachments();
                    if (data.response_code == 200) {
                        swal({
                            title: "Attachment Saved Successfully",
                            text: "Your Attachment has been saved successfully",
                            type: "success",
                            confirmButtonColor: "#209be1",
                            confirmButtonText: "Ok",
                            closeOnConfirm: true
                        });
                    }
                    setTimeout(function(){
                         reset_upload_html = `<div class="col-md-12 form-group" style="width: 500px;">
                                <input type="file"  class="filestyle col-sm-12" data-buttonBefore="true" name="file" id="attachmentupload" />
                            </div>
                            <div class="text-right" style="margin-right: 15px;">
                                <input type='submit' id="sehideUploadButton" class="btn btn-save" value="Add" disabled="true"/>
                            </div>`;
                            $('#se_attachment_uploading').html(reset_upload_html);
                            getGCSKeySEDataMaterial();
                            validateSEFileType();
                            validateNewAttachmentSe();
                    },10);
                    listSEAttachments();
            },
            error : function(xhr,errmsg,err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });
    }, 500);

    }else{
         swal({
				title: "Upload Failed",
				text: "Your Attachment has failed to save.",
				type: "error"
			});
			  setTimeout(function(){
                         reset_upload_html = `<div class="col-md-12 form-group" style="width: 500px;">
                                <input type="file"  class="filestyle col-sm-12" data-buttonBefore="true" name="file" id="attachmentupload" />
                            </div>
                            <div class="text-right" style="margin-right: 15px;">
                                <input type='submit' id="sehideUploadButton" class="btn btn-save" value="Add" disabled="true"/>
                            </div>`;
                            $('#se_attachment_uploading').html(reset_upload_html);
                            getGCSKeySEDataMaterial();
                            validateSEFileType();
                            validateNewAttachmentSe();
                    },10);
                    listSEAttachments();

    }

}

function listSEAttachments(){
        var se_id = $('#id_se-id').val();
        $('#loadingmessage_se_attachment').show();
        if (se_id == null) {
                return;
        }
        $.ajax({
        url : "/erp/sales/json/se/se_attachment_list/",
        type : "POST",
        dataType: "json",
        data : {
            se_id: se_id
        },
        success : function(data) {
            $("#se_attachment_container").find(".document_attachment_part").remove();
            $('#loadingmessage_se_attachment').hide();
            $.each( data, function( key, value ) {

                addAttachmentSE(value.se_id, value.description, value.attachment_id, value.file, value.enterprise_id, value.ext, value.file_base64);
            });
        },
        error : function(xhr,errmsg,err) {
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });
}

function addAttachmentSE(se_id, description, attachment_id, file, enterprise_id, extension, base64) {
        var ext = description + "." + extension;
        var cls_base64 = (base64 !=''? 'base64' : '');
        var csrf_token = document.getElementsByName('csrfmiddlewaretoken')[0].value;
        var row = '<div class="document_attachment_part" data-tooltip="tooltip" title="'+ description +'" > \
                       <span class="document_remove" onclick="deleteSEAttachmentFile(\''+ se_id +'\', \''+ description +'\', \''+ attachment_id +'\');"><i class="fa fa-times" aria-hidden="true"></i></span>\
                       <div >\
                       <form action="/erp/commons/json/document/" method="post" target="_blank">\
                                <input type="hidden" class="base64-file-value" data-filename="'+ ext +'" value="'+base64+'">\
                                <input type="hidden" name="csrfmiddlewaretoken" value="'+ csrf_token +'" />\
                                <input type="hidden" name="document_name" value="'+ ext +'"/>\
                                <input type="hidden" name="document_uri" value="'+ file +'"/>\
                                <span class="document_attachment attached-file file_'+extension.toLowerCase()+' linked_text '+cls_base64+'" data-extension="'+extension.toLowerCase()+'" data-url = "'+file+'"  onclick="viewAttachedDocument(this)">'+extension.toUpperCase()+'</span>\
                                <span class="document_text">'+description+'</span>\
                        </form></div></div>';
        $("#se_attachment_container").append(row);
        TooltipInit();
        $("#attachmentupload").filestyle({buttonBefore: true});
}

function addNewSalesPerson(){
        $("#add_sales_person_modal").modal('show');
}

function promoteToOA() {
    se_id = $("#id_se-id").val() ;
    $("#id-promote_oa_no").val(se_id);
    $("#id-promote_oa_form").submit();
}

function promoteToInvoice() {
    se_id = $("#id_se-id").val() ;
    $("#id-promote_invoice_no").val(se_id);
    $("#id-promote_invoice_form").submit();
}
function openMailPopup() {
    new Mailer().prepareEmailPopup().getSupplierMailID(id=$("#se_id").val(), type= "se").show();
    return false;
}

function loadAlternateUnits(itemId, defaultUnit, elementId, callback=function(){}){
    $.ajax({
        url: "/erp/stores/json/indent/loadalternateunits/",
        type: "post",
        datatype: "json",
        data: {item_id: itemId},
        success: function(response) {
            $(elementId).html("");
            items = `<option value='0' data-val='1'>${defaultUnit}</option>`;
            $.each(response, function(i, unit) {
                items = `${items}<option value="${unit.alternate_unit_id}" data-val="${unit.scale_factor}">${unit.unit_name}</option>`;
            });
            $(elementId).html(items);
            callback();
        }
    });
}

function loadAlternateUnitPrice(){
    if($("#id_se_particular-__prefix__-alternate_units option:selected").attr('data-val') !=0 && $("#material_rate_hidden").val() != 0 && $("#material_rate_hidden").val() != ""){
        $("#id_se_particular-__prefix__-unit_price").val($("#id_se_particular-__prefix__-alternate_units option:selected").attr('data-val') * $("#material_rate_hidden").val());
    }else{
        $("#id_se_particular-__prefix__-unit_price").val("0.00000")
    }
    $("#id_se_particular-__prefix__-unit_price").trigger("blur");
}

$(window).load(function(){
    if($("#id_se-id").val() == "") {
        $("#id_se-party_id").trigger("change");
    }
});