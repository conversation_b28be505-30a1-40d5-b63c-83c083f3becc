"""
"""
__author__ = 'karuppasamy'

PUSH_NOTIFICATION = {
	"expenses": "Expense %s by %s for amount %.2f with Exp No: %s",
	"expense": "Expenses claimed for amount %s is rejected by %s Exp No: %s",
	"icd": "Receipt note %s by %s with code %s",
	"material_price_approved": "%s has been approved for %s%s for %s wef:%s",
	"material_price_rejected": "Supplier price %s is rejected for material %s",
	"purchase_oder_updated": "%s# %s has been modified by %s on %s",
	"purchase_oder_approved": "%s No. %s to %s for %s has been approved",
	"purchase_oder_rejected": "%s No. %s to %s for %s has been rejected",
	"purchase_oder_discarded": "%s No. %s to %s for %s has been removed",
	"purchase_oder_review": "%s No. %s to %s has been reviewed",
	"invoice_approved": "Inv No. %s to %s for %s has been approved",
	"invoice_rejected": "Inv No. %s to %s for %s has been rejected",
	"invoice_voucher": "Inv No. %s to %s for %s voucher has been created",
	"delivery_jobwork_approved": "Dc No. %s to %s for %s has been approved",
	"delivery_jobwork_rejected": "Dc No. %s to %s for %s has been rejected",
	"issues": "Issue %s has been raised for %s and Issued to %s ",
	"indent": "Raised indent %s for %s materials",
	"receipt_approved": "Receipt No. %s to %s for %s has been approved",
	"receipt_rejected": "Receipt No. %s%s has been rejected",
	"OA_approved": "OA No. %s to %s for %s has been approved",
	"OA_rejected": "OA No. %s to %s for %s has been rejected",
	"sales": "",
	"stores": "",
	"voucher_code": "Voucher code has been changed from '%s' to '%s'!",
	"expense_code": "Expense code has been changed from '%s' to '%s'!",
	"icd_code": "Note code has been changed from '%s' to '%s'!",
	"invoice_code": "{inv_type} code has been changed from {from_code_value} to {to_code_value}!",
	"invoice_serial_no": "{inv_type} Serial number has been changed from {from_inv_no} to {to_inv_no}!",
	"oa_code": "OA code has been changed from '%s' to '%s'!",
	"indent_code": "Indent code has been changed from '%s' to '%s'!",
	"receipt_code": "Receipt code has been changed from '%s' to '%s'!",
	"purchase_code": "PO code has been changed from '%s' to '%s'!",
	"grn_update": "GRN No: %s has been updated successfully",
	"icd_saved": "Note code %s has been saved successfully",
	"ledger_saved": "Ledger name has been changed from %s to %s",
	"sales_estimate_review": "%s No. %s to %s has been reviewed",
	"Sales_Estimate_approved": "Sales_Estimate No. %s to %s for %s has been approved",
	"Sales_Estimate_rejected": "Sales_Estimate No. %s to %s for %s has been rejected",
	"se_code": "Sales Estimate code has been changed from '%s' to '%s'!",
}

SALES_NOTIFICATION = [" Saved with party - ID", "Invoice draft with Approval - ID:", "Rejected with Invoice - no:"]
STORE_NOTIFICATION = [
	"Saved intent with code:",
	"Updated an existing Receipt",
	"Created a new Receipt",
	"Approved with receipt no is",
	"Rejected with receipt code is :",
	"Issue Updated with code is :",
	"Issue saved with no is :"]
