.CSSTableGenerator {
	margin:20px auto auto auto;	
	width:95%;border:1px solid #C9C9C9;	
}.CSSTableGenerator table{		
	border:1px solid #C9C9C9;border-collapse: collapse;	
	width:100%;	height:100%;margin:0px;padding:0px;
}.CSSTableGenerator tr:nth-child(odd){ background-color:#F9F9F9; 
}.CSSTableGenerator tr:nth-child(even){ background-color:#ffffff; 
}.CSSTableGenerator td{
	vertical-align:middle;border:1px solid #C9C9C9;
	border-width:0px 1px 1px 0px;text-align:center;
	padding:6px;font-size:13px;	
	font-family:Arial, Helvetica, sans-serif;
	font-weight:normal;color:#5A5B5A;
}.CSSTableGenerator tr th{
	background:-o-linear-gradient(bottom, #FFFFFF 5%, #B9BAB9 100%);	
	background:-webkit-gradient( linear, left top, left bottom, color-stop(0.05, #FFFFFF), color-stop(1, #B9BAB9) );
	background:-moz-linear-gradient( center top, #FFFFFF 5%, #B9BAB9 100% );
	background: -o-linear-gradient(top,#FFFFFF,B9BAB9);
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr="#FFFFFF", endColorstr="#B9BAB9");	
	height:30px;background-color:#FFFFFF;border:0px solid #C9C9C9;text-align:center;
	border-width:0px 0px 1px 1px;font-size:14px;font-family:Arial, Helvetica, sans-serif;
	font-weight:bold;color:#5A5B5A;
}.CSSTableGenerator tr:hover td{	
	color:#170D00;background-color:#DBDBDB;
}.CSSTableGenerator tr:first-child td:last-child{
	border-width:0px 0px 1px 1px;
}

.CSSTableGenerator1 {
	margin:20px auto auto auto;	
	width:60%;border:1px solid #C9C9C9;	
}.CSSTableGenerator1 table{		
	border:1px solid #C9C9C9;border-collapse: collapse;	
	width:100%;	height:100%;margin:0px;padding:0px;
}.CSSTableGenerator1 tr:nth-child(odd){ background-color:#F9F9F9; 
}.CSSTableGenerator1 tr:nth-child(even){ background-color:#ffffff; 
}.CSSTableGenerator1 td{
	vertical-align:middle;border:1px solid #C9C9C9;
	border-width:0px 1px 1px 0px;text-align:center;
	padding:6px;font-size:13px;	
	font-family:Arial, Helvetica, sans-serif;
	font-weight:normal;color:#5A5B5A;
}.CSSTableGenerator1 tr th{
	background:-o-linear-gradient(bottom, #FFFFFF 5%, #B9BAB9 100%);	
	background:-webkit-gradient( linear, left top, left bottom, color-stop(0.05, #FFFFFF), color-stop(1, #B9BAB9) );
	background:-moz-linear-gradient( center top, #FFFFFF 5%, #B9BAB9 100% );
	background: -o-linear-gradient(top,#FFFFFF,B9BAB9);
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr="#FFFFFF", endColorstr="#B9BAB9");	
	height:30px;background-color:#FFFFFF;border:0px solid #C9C9C9;text-align:center;
	border-width:0px 0px 1px 1px;font-size:14px;font-family:Arial, Helvetica, sans-serif;
	font-weight:bold;color:#5A5B5A;
}.CSSTableGenerator1 tr:hover td{	
	color:#170D00;background-color:#DBDBDB;
}.CSSTableGenerator1 tr:first-child td:last-child{
	border-width:0px 0px 1px 1px;
}

.CSSTableGenerator2 {
	margin:5px auto auto -155px;	
	width:95%;border:1px solid #C9C9C9;	
}.CSSTableGenerator2 table{		
	border:1px solid #C9C9C9;border-collapse: collapse;	
	width:100%;	height:100%;margin:0px;padding:0px;
}.CSSTableGenerator2 tr:nth-child(odd){ background-color:#F9F9F9; 
}.CSSTableGenerator2 tr:nth-child(even){ background-color:#ffffff; 
}.CSSTableGenerator2 td{
	vertical-align:middle;border:1px solid #C9C9C9;
	border-width:0px 1px 1px 0px;text-align:center;
	padding:6px;font-size:12px;	
	font-family:Arial, Helvetica, sans-serif;
	font-weight:normal;color:#5A5B5A;
}.CSSTableGenerator2 tr th{
	background:-o-linear-gradient(bottom, #FFFFFF 5%, #B9BAB9 100%);	
	background:-webkit-gradient( linear, left top, left bottom, color-stop(0.05, #FFFFFF), color-stop(1, #B9BAB9) );
	background:-moz-linear-gradient( center top, #FFFFFF 5%, #B9BAB9 100% );
	background: -o-linear-gradient(top,#FFFFFF,B9BAB9);
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr="#FFFFFF", endColorstr="#B9BAB9");	
	height:30px;background-color:#FFFFFF;border:0px solid #C9C9C9;text-align:center;
	border-width:0px 0px 1px 1px;font-size:13px;font-family:Arial, Helvetica, sans-serif;
	font-weight:bold;color:#5A5B5A;
}.CSSTableGenerator2 tr:hover td{	
	color:#170D00;background-color:#DBDBDB;
}.CSSTableGenerator2 tr:first-child td:last-child{
	border-width:0px 0px 1px 1px;
}

CSSTableGenerator3 {
	margin:20px auto auto auto;	
	width:95%;border:1px solid #C9C9C9;	
}.CSSTableGenerator3 table{		
	border:1px solid #C9C9C9;border-collapse: collapse;	
	width:100%;	height:100%;margin:0px;padding:0px;
}.CSSTableGenerator3 tr:nth-child(odd){ background-color:#F9F9F9; 
}.CSSTableGenerator3 tr:nth-child(even){ background-color:#ffffff; 
}.CSSTableGenerator3 td{
	vertical-align:middle;border:1px solid #C9C9C9;
	border-width:0px 1px 1px 0px;text-align:center;
	padding:6px;font-size:13px;	
	font-family:Arial, Helvetica, sans-serif;
	font-weight:normal;color:#5A5B5A;
}.CSSTableGenerator3 tr th{
	background:-o-linear-gradient(bottom, #FFFFFF 5%, #B9BAB9 100%);	
	background:-webkit-gradient( linear, left top, left bottom, color-stop(0.05, #FFFFFF), color-stop(1, #B9BAB9) );
	background:-moz-linear-gradient( center top, #FFFFFF 5%, #B9BAB9 100% );
	background: -o-linear-gradient(top,#FFFFFF,B9BAB9);
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr="#FFFFFF", endColorstr="#B9BAB9");	
	height:30px;background-color:#FFFFFF;border:0px solid #C9C9C9;text-align:center;
	border-width:0px 0px 1px 1px;font-size:14px;font-family:Arial, Helvetica, sans-serif;
	font-weight:bold;color:#5A5B5A;
}.CSSTableGenerator3 tr:hover td{	
	color:#170D00;background-color:#B7B7B7;
}.CSSTableGenerator3 tr:first-child td:last-child{
	border-width:0px 0px 1px 1px;
}

.CSSTableGenerator4 {
	margin:10px auto auto 80px;	
	border:1px solid #C9C9C9;	
}.CSSTableGenerator4 table{		
	border:1px solid #C9C9C9;border-collapse: collapse;	
	width:100%;	height:100%;margin:0px;padding:0px;
}