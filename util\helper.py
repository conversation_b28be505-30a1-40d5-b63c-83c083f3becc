"""
Compilation of many utility methods to be used repeatedly across the application irrespective of functional requisites.
"""
import os
import string
from io import StringIO
from datetime import datetime, date, timedelta

import xlwt
from dateutil.relativedelta import relativedelta
from unidecode import unidecode

from util import logger
from util.properties import TEMP_DOC_PATH, TEMP_REV_DOC_PATH

__author__ = 'ka<PERSON><PERSON>n'


def constructFormInitializer(entity=None, exclude_field_keys=()):
	"""
	Constructs a dict to be used to initialize a Django form, i.e., to pre-populate a form with the values of the
	object given as argument.

	This form construction method is just a basic copy of all fields of the entity into form - to be extended for any
	specific manipulation of the field values.

	Care to be taken to ensure both the form and the object have fields of same names.

	:param entity: Any persistent object that need to be loaded to a form.
	:param exclude_field_keys:
	:return: a tuple that will initialize an appropriate form.
	"""
	# logger.debug("Entity:%s" % entity)
	try:
		initializer = dict()
		if hasattr(entity, '__iter__'):
			for item in entity:
				initializer.update(item.__dict__)
		else:
			initializer = entity.__dict__
		for key in exclude_field_keys:
			if key in initializer:
				initializer.__delitem__(key)
		return initializer
	except Exception as e:
		logger.exception('Form Initializer construction failed... %s' % e.message)
		return {}


def copyFormToEntity(form, entity, exclude_field_list=()):
	"""
	Copies information held in a given form to the corresponding field to an object's field of same name.

	A pre-requisite is that the name of form fields and appropriate fields in the objects to which the data collected
	in the form are to be copied must be spelt exactly the same.

	:param form:
	:param entity:
	:param exclude_field_list:
	:return: the resultant Object instance - entity, that has field data copied from the form.
	"""
	for field_key in form.cleaned_data:
		try:
			if field_key not in exclude_field_list:
				logger.debug('%s - %s' % (field_key, form.cleaned_data[field_key]))
				entity.__dict__[field_key] = form.cleaned_data[field_key]
		except KeyError:
			logger.debug('Field - %s not found in entity' % field_key)
			pass
	return entity


def copyDictToDict(source=None, destination=None, exclude_keys=()):
	"""
	Copies information held in a given form to the dictionary field of same name.

	:param source:
	:param destination:
	:param exclude_keys:
	:return: the resultant
	"""
	if not destination:
		destination = dict()
	for field_key in source:
		try:
			if field_key not in exclude_keys:
				logger.debug('%s - %s' % (field_key, source[field_key]))
				destination[field_key] = source[field_key]
		except KeyError:
			logger.debug('Field - %s not found in entity' % field_key)
			pass
	return destination


def getFinancialYear(for_date=None, fy_start_day='01/04'):
	"""
	Gives the Financial Year(FY) in which the given date falls, depending on a start day provided for the Financial year.

	:param for_date: Datetime object date for which the FY is to be determined.
	:param fy_start_day: Start day string in the form 'dd/mm'.
	:return: Start and end years of the FY in the format 'y1-y2', if the FY start date is other than '01/01', else the
			respective year in 'yyyy' format
	"""
	if not for_date:
		for_date = datetime.now()
	if fy_start_day == '01/01':
		return '%s' % for_date.year
	fy_start = fy_start_day.split('/')
	fy_start_date = datetime(day=int(fy_start[0]), month=int(fy_start[1]), year=for_date.year)
	fy_start_date += relativedelta(years=0 if for_date >= fy_start_date else -1)
	fy_end_date = fy_start_date + relativedelta(years=+1) + relativedelta(microseconds=-1)
	return '%s-%s' % (('%s' % fy_start_date.year)[2:4], ('%s' % fy_end_date.year)[2:4])


def getFYDateRange(for_date=None, fy_start_day='01/04'):
	"""
	Gives the start and end date of a Financial Year that starts with a given day, within which a given date falls.
	:param for_date: Datetime object date for which the FY is to be determined.
	:param fy_start_day: Start day string in the form 'dd/mm'.
	:return: Start and end dates of the Financial Year in an array of two datetime objects - [start_date, end_date].
	"""
	if not for_date:
		for_date = datetime.now()
	fy_start = fy_start_day.split('/')
	fy_start_date = datetime(day=int(fy_start[0]), month=int(fy_start[1]), year=for_date.year)
	fy_start_date += relativedelta(years=(0 if for_date >= fy_start_date else -1))
	fy_end_date = fy_start_date + relativedelta(years=1) - relativedelta(microseconds=1)
	return [fy_start_date, fy_end_date]


def getFYStartDate(for_date=None, fy_start_day='01/04'):
	"""
	
	:param for_date: 
	:param fy_start_day: 
	:return: 
	"""
	if not for_date:
		for_date = datetime.now()
	return getFYDateRange(for_date=for_date, fy_start_day=fy_start_day)[0]


def getFYEndDate(for_date=None, fy_start_day='01/04'):
	"""
	
	:param for_date: 
	:param fy_start_day: 
	:return: 
	"""
	if not for_date:
		for_date = datetime.now()
	return getFYDateRange(for_date=for_date, fy_start_day=fy_start_day)[1]


def convertStringToDate(date_string):
	"""
	Returns a datetime object constructed from a date text in the format YYYY-MM-dd.
	:param date_string:
	:return:
	"""
	date_values = date_string.split('-')
	return datetime(year=int(date_values[0]), month=int(date_values[1]), day=int(date_values[2]))


def readFile(file_path_name):
	"""
	Reads a file identified by a given path in binary format and returns the same for futher process

	:param file_path_name:
	:return:
	"""
	with open(getAbsolutePath(file_path_name), 'rb') as source_file:
		document = source_file.read()
	return document


def writeFile(data, file_path_name):
	"""
	Writes the given data into a file, specified.

	:param data:
	:param file_path_name:
	:return:
	"""
	logger.debug('Writing a file to: %s' % getAbsolutePath(file_path_name))
	with open(getAbsolutePath(file_path_name), 'wb') as target_file:
		target_file.write(data)
	return


def getAbsolutePath(relative_path):
	"""

	:param relative_path:
	:return:
	"""
	return os.path.join(os.path.dirname(os.path.abspath(__file__)), "..%s" % relative_path)


def getFormattedDocPath(code=None, id=None, separator="_"):
	"""

	:param code:
	:param id:
	:param separator:
	:return:
	"""
	return TEMP_DOC_PATH % (code.replace("/", separator), id)


def getFormattedRevisedDocPath(code=None, id=None, suffix="", separator="_"):
	"""

	:param code:
	:param id:
	:param suffix:
	:param separator:
	:return:
	"""
	return TEMP_REV_DOC_PATH % (code.replace("/", separator), id, suffix)


def qualifiedDate(date_instance=None):
	"""

	:param date_instance: datetime, date, date formatted string
	:return:
	"""

	logger.info("Converting to date object %s" % date_instance)
	date_object = None
	try:
		if date_instance and date_instance != "":
			if type(date_instance) in (datetime, date):
				date_object = date_instance
			else:
				date_object = datetime.strptime(date_instance, "%Y-%m-%d %H:%M:%S")
	except Exception as e:
		logger.error("Could not parse date %s. This may cause issues in functionality." % date_instance)
		raise e
	return date_object


def popKeys(dict_object=None, pop_list=None):
	"""

	:param dict_object:
	:param pop_list:
	:return:
	"""
	try:
		for key in pop_list:
			if key in dict_object:
				dict_object.pop(key)
	except Exception as e:
		logger.error("Pop key from list failed %s " % e.message)


def remove_non_ascii(text):
	return unidecode(unicode(text, encoding="utf-8"))


def createXlsFile(column_names, sheet_name='Sheet'):
	"""

	:param column_names:
	:param sheet_name:
	:return:
	"""
	workbook = xlwt.Workbook()
	worksheet = workbook.add_sheet(sheet_name)
	for i, field_name in enumerate(column_names):
		worksheet.write(0, i, field_name)
		worksheet.col(i).width = 5000  # around 150 pixels
	xlwt.easyxf('align: wrap yes')
	fp = StringIO()
	workbook.save(fp)
	fp.seek(0)
	data = fp.read()
	fp.close()
	return data


def set_datetime(old_date=None, format_=None):
	"""

	:param old_date:
	:param format_:
	:return:
	"""
	response = ""
	try:
		response = old_date + ' 00:00:00' if format_ == "from_date" else old_date + ' 23:59:00'
	except Exception as e:
		logger.exception("date format is wrong: %s" % e)
	return response


def compare_string(s1=None, s2=None):
	"""

	:param s1:
	:param s2:
	:return:
	"""
	try:
		remove = string.punctuation + string.whitespace
	except Exception as e:
		logger.exception("date format is wrong: %s" % e)
	return s1.translate(None, remove) == s2.translate(None, remove)


def icdEinvoiceQuery(invoice_list=None):
	"""

	:param invoice_list:
	:return:
	"""
	query = """SELECT
					note.grn_no AS invoice_id,
					"1.1" AS Version,
					"GST" AS TaxSch,
					"B2B" AS SupTyp,
					"N" AS IgstOnIntra,
					NULL AS RegRev,
					NULL AS EcmGstin,
					"INV" AS Typ,
					CONCAT(note.financial_year, '/IAN/', LPAD(note.note_no, 6, 0), IF(note.sub_number is not NULL, note.sub_number, '')) AS No,
					note.note_datetime AS Dt,
					erd.details AS Gstin,
					e.name AS LglNm,
					e.name AS TrdNm,
					e.address1 AS Addr1,
					e.address2 AS Addr2,
					e.city AS Loc,
					e.pin_code AS Pin,
					left(erd.details, 2) as Stcd,
					left(erd.details, 2) as Pos,
					e.phone_no AS Ph,
					e.email AS Em,
					prd.details AS p_Gstin,
					p.party_name AS p_LglNm,
					p.party_name AS p_TrdNm,
					p.address1 AS p_Addr1,
					p.address2 AS p_Addr2,
					p.city AS p_Loc,
					p.pin_code AS p_Pin,
					left(prd.details, 2) as p_Stcd,
					left(prd.details, 2) as p_Pos,
					p.phone_no AS p_Ph,
					p.email AS p_Em,
					note.amount AS AssVal,
					IF(t.type = 'IGST', SUM(t.net_rate)/count(t.type) , NULL) * IF(note.currency_conversion_rate = 0, 1.00000, note.currency_conversion_rate) AS IgstVal,
					IF(t.type = 'CGST', SUM(t.net_rate)/count(t.type) , NULL) * IF(note.currency_conversion_rate = 0, 1.00000, note.currency_conversion_rate) AS CgstVal,
					IF(t.type = 'SGST', SUM(t.net_rate)/count(t.type) , NULL) * IF(note.currency_conversion_rate = 0, 1.00000, note.currency_conversion_rate) AS SgstVal,
					IF(t.type = 'CESS', SUM(t.net_rate)/count(t.type) , NULL) * IF(note.currency_conversion_rate = 0, 1.00000, note.currency_conversion_rate) AS CesVal,
					IF(t.type = 'CESS', SUM(t.net_rate)/count(t.type) , NULL) * IF(note.currency_conversion_rate = 0, 1.00000, note.currency_conversion_rate) AS StCesVal,
					0 AS Discount,
					0 AS OthChrg,
					0 AS RndOffAmt,
					note.amount AS TotInvVal,
					0 AS TotInvValFc,
					NULL AS EwbDtls,
					NULL AS PayDtls,
					NULL AS RefDtls,
					NULL AS Url,
					NULL AS Docs,
					NULL AS Info
			
			FROM
					crdrnote note 
					LEFT JOIN crdrnote_grn_map cgm ON cgm.crdrnote_id = note.grn_no
						AND cgm.enterprise_id = note.enterprise_id    	
					LEFT JOIN crdr_details cd ON cd.crdr_id = cgm.crdrnote_id
						AND cd.enterprise_id = cd.enterprise_id
					LEFT JOIN crdr_details_tax cdt ON cd.crdr_id  = cdt.crdr_id
						AND cd.reason_id = cdt.reason_id
						AND cd.item_id = cdt.item_id
						AND cd.make_id = cdt.make_id
						AND cd.enterprise_id = cdt.enterprise_id
					LEFT JOIN tax t ON cdt.tax_code = t.code
						AND cdt.enterprise_id = t.enterprise_id
					LEFT JOIN grn g ON g.grn_no = cgm.crdrnote_grn_no
						AND g.enterprise_id = cgm.enterprise_id	
					LEFT JOIN party_master p ON p.party_id = note.party_id
						AND p.enterprise_id = note.enterprise_id
					LEFT JOIN party_registration_detail prd ON p.party_id = prd.party_id
						AND p.enterprise_id = prd.enterprise_id
						AND prd.label = 'GSTIN'
					LEFT JOIN enterprise_registration_detail erd ON erd.enterprise_id = note.enterprise_id
						AND erd.label = 'GSTIN'
					LEFT JOIN enterprise e ON note.enterprise_id = e.id
				WHERE note.grn_no in (%s) 
			GROUP BY note.grn_no""" % invoice_list
	return query


def icdEinvoiceItemQuery(invoice_id=None):
	"""

	:param invoice:
	:return:
	"""
	query = """SELECT 
						 m.description AS PrdDesc, 
						 IF(m.is_service = 1, 'Y', 'N') AS IsServc,
						 cd.hsn_code AS HsnCd,
						 ROUND(SUM(ROUND((cd.qty * IF(note.is_credit = cd.is_credit, 1, -1)), 2))/IF(count(distinct cdt.tax_code) != 0, count(distinct cdt.tax_code), 1), 2) AS Qty,
						 um.unit_name AS Unit,
						 ABS(ROUND(SUM(ROUND(((cd.rate) * IF(note.is_credit = cd.is_credit, 1, -1)), 2))/IF(count(distinct cdt.tax_code) != 0, count(distinct cdt.tax_code), 1), 2)) AS UnitPrice,
						 ROUND(SUM(ROUND(((cd.qty * cd.rate) * IF(note.is_credit = cd.is_credit, 1, -1)), 2))/IF(count(distinct cdt.tax_code) != 0, count(distinct cdt.tax_code), 1), 2) AS TotAmt,
						 0 AS Discount,
						 0 AS PreTaxVal,
						 ROUND(SUM(ROUND(((cd.qty * cd.rate) * IF(note.is_credit = cd.is_credit, 1, -1)), 2))/IF(count(distinct cdt.tax_code) != 0, count(distinct cdt.tax_code), 1), 2) AS AssAmt,
						 ROUND(((IF(t.type = 'CGST', SUM(t.net_rate), 0)))/(count(cd.item_id)/IF(count(distinct cdt.tax_code) != 0, count(distinct cdt.tax_code), 1)) 
							+ ((IF(t.type = 'SGST', SUM(t.net_rate) , 0)))/(count(cd.item_id)/IF(count(distinct cdt.tax_code) != 0, count(distinct cdt.tax_code), 1))
							+ ((IF(t.type = 'IGST', SUM(t.net_rate), 0)))/(count(cd.item_id)/IF(count(distinct cdt.tax_code) != 0, count(distinct cdt.tax_code), 1)), 2) AS GstRt,
						 ROUND(SUM(ROUND(((cd.qty * cd.rate) * IF(note.is_credit = cd.is_credit, 1, -1)), 2))/IF(count(distinct cdt.tax_code) != 0, count(distinct cdt.tax_code), 1) 
							* (IF(t.type = 'IGST', SUM(t.net_rate), 0)/(count(cd.item_id)/IF(count(distinct cdt.tax_code) != 0, count(distinct cdt.tax_code), 1))/100), 2) AS IgstAmt,
						 SUM(ROUND((cd.qty * cd.rate) * (IF(t.type = 'CGST', t.net_rate * IF(note.is_credit = cd.is_credit, 1, -1), NULL)/100) * IF(note.currency_conversion_rate = 0, 1.00000, note.currency_conversion_rate), 2)) AS CgstAmt, 
						 SUM(ROUND((cd.qty * cd.rate) * (IF(t.type = 'SGST', t.net_rate * IF(note.is_credit = cd.is_credit, 1, -1), NULL)/100) * IF(note.currency_conversion_rate = 0, 1.00000, note.currency_conversion_rate), 2)) AS SgstAmt,
						 IF(t.type = 'CESS', SUM(t.net_rate) , NULL) AS CesRt,
						 IF(t.type = 'CESS', SUM(t.net_rate)/count(t.type) , NULL) * IF(note.currency_conversion_rate = 0, 1.00000, note.currency_conversion_rate) AS CesAmt,
						 0 AS CesNonAdvlAmt,
						 0 AS StateCesRt,
						 0 AS StateCesAmt,
						 0 AS StateCesNonAdvlAmt,
						 0 AS OthChrg,
						ROUND((SUM(ROUND(((cd.qty * cd.rate) * IF(note.is_credit = cd.is_credit, 1, -1)), 2))/IF(count(distinct cdt.tax_code) != 0, count(distinct cdt.tax_code), 1)
						 + SUM(ROUND((cd.qty * cd.rate) * (IF(t.type = 'CGST', t.net_rate * IF(note.is_credit = cd.is_credit, 1, -1), 0)/100), 2))
                         + SUM(ROUND((cd.qty * cd.rate) * (IF(t.type = 'SGST', t.net_rate * IF(note.is_credit = cd.is_credit, 1, -1), 0)/100), 2)) 
                         + SUM(ROUND((cd.qty * cd.rate) * (IF(t.type = 'IGST', t.net_rate * IF(note.is_credit = cd.is_credit, 1, -1), 0)/100), 2)))
                          * IF(note.currency_conversion_rate = 0, 1.00000, note.currency_conversion_rate), 2) AS TotItemVal
						FROM
							crdrnote note 
						LEFT JOIN crdrnote_grn_map cgm ON cgm.crdrnote_id = note.grn_no
							AND cgm.enterprise_id = note.enterprise_id    	
						LEFT JOIN crdr_details cd ON cd.crdr_id = cgm.crdrnote_id
							AND cd.enterprise_id = cd.enterprise_id
						LEFT JOIN materials m ON m.id = cd.item_id
							AND m.enterprise_id = cd.enterprise_id
						LEFT JOIN unit_master um ON um.unit_id = m.unit
							AND um.enterprise_id = m.enterprise_id
						LEFT JOIN crdr_details_tax cdt ON cd.crdr_id  = cdt.crdr_id
							AND cd.reason_id = cdt.reason_id
							AND cd.item_id = cdt.item_id
							AND cd.make_id = cdt.make_id
							AND cd.enterprise_id = cdt.enterprise_id
						LEFT JOIN tax t ON cdt.tax_code = t.code
							AND cdt.enterprise_id = t.enterprise_id
						LEFT JOIN grn g ON g.grn_no = cgm.crdrnote_grn_no
							AND g.enterprise_id = cgm.enterprise_id	
						LEFT JOIN party_master p ON p.party_id = note.party_id
							AND p.enterprise_id = note.enterprise_id
						LEFT JOIN party_registration_detail prd ON p.party_id = prd.party_id
							AND p.enterprise_id = prd.enterprise_id
							AND prd.label = 'GSTIN'
						LEFT JOIN enterprise_registration_detail erd ON erd.enterprise_id = note.enterprise_id
							AND erd.label = 'GSTIN'
						LEFT JOIN enterprise e ON note.enterprise_id = e.id
					WHERE note.grn_no = '%s'
				GROUP BY note.grn_no, cd.item_id, cd.make_id""" % invoice_id
	return query


def icdEinvoiceItemCes(invoice_id=None, enterprise_id=None):
	"""

	:param invoice_id:
	:param enterprise_id:
	:return:
	"""
	query = """SELECT 
					note.grn_no,
					SUM(ROUND((cd.qty * cd.rate) * (IF(t.type = 'CESS', t.net_rate * IF(note.is_credit = cd.is_credit, 1, -1), 0)/100), 2)) AS CesAmt,
					ROUND(SUM(IF(t.type = 'CESS', (t.net_rate), 0)), 2) AS CesRt											
					FROM
						crdrnote note 
					LEFT JOIN crdrnote_grn_map cgm ON cgm.crdrnote_id = note.grn_no
						AND cgm.enterprise_id = note.enterprise_id    	
					LEFT JOIN crdr_details cd ON cd.crdr_id = cgm.crdrnote_id
						AND cd.enterprise_id = cd.enterprise_id
					LEFT JOIN materials m ON m.id = cd.item_id
						AND m.enterprise_id = cd.enterprise_id
					LEFT JOIN unit_master um ON um.unit_id = m.unit
						AND um.enterprise_id = m.enterprise_id
					LEFT JOIN crdr_tax ct ON cd.crdr_id  = ct.crdr_id
						AND cd.enterprise_id = ct.enterprise_id
					LEFT JOIN tax t ON ct.tax_code = t.code
						AND ct.enterprise_id = t.enterprise_id							
				WHERE note.grn_no = '%s' AND note.enterprise_id = '%s'
				GROUP BY note.grn_no""" % (invoice_id, enterprise_id)
	return query


def icdEinvoiceNonItemQuery(invoice_id=None):
	"""

	:param invoice_id:
	:return:
	"""
	query = """SELECT 
						 cnm.description AS PrdDesc, 
						 'N' AS IsServc,
						 cnm.hsn_code AS HsnCd,
						 ROUND(SUM(ROUND(((cnm.qty) * IF(note.is_credit = cnm.is_credit, 1, -1)), 2))/count(distinct cnmt.tax_code), 2) AS Qty,
						 um.unit_name AS Unit,
						 ROUND(SUM(ROUND(((cnm.rate) * IF(note.is_credit = cnm.is_credit, 1, -1)), 2))/count(distinct cnmt.tax_code), 2) AS UnitPrice,
						 ROUND(SUM(ROUND(((cnm.qty * cnm.rate) * IF(note.is_credit = cnm.is_credit, 1, -1)), 2))/count(distinct cnmt.tax_code), 2) AS TotAmt,
						 0 AS Discount,
						 0 AS PreTaxVal,
						 ROUND(SUM(ROUND(((cnm.qty * cnm.rate) * IF(note.is_credit = cnm.is_credit, 1, -1)), 2))/count(distinct cnmt.tax_code), 2) AS AssAmt,
						 ROUND(SUM((IF(t.type = 'CGST', t.net_rate * IF(note.is_credit = cnm.is_credit, 1, -1), 0)))/(count(cnm.description)/count(distinct cnmt.tax_code)) 
							+ SUM((IF(t.type = 'SGST', t.net_rate * IF(note.is_credit = cnm.is_credit, 1, -1), 0)))/(count(cnm.description)/count(distinct cnmt.tax_code))
							+ ((IF(t.type = 'IGST', t.net_rate * IF(note.is_credit = cnm.is_credit, 1, -1), 0)))/(count(cnm.description)/count(distinct cnmt.tax_code)), 2) AS GstRt,
						 (ROUND((cnm.qty * cnm.rate) * (IF(t.type = 'IGST', t.net_rate * IF(note.is_credit = cnm.is_credit, 1, -1), NULL)/100) * IF(note.currency_conversion_rate = 0, 1.00000, note.currency_conversion_rate), 2)) AS IgstAmt,
						 SUM(ROUND((cnm.qty * cnm.rate) * (IF(t.type = 'CGST', t.net_rate * IF(note.is_credit = cnm.is_credit, 1, -1), NULL)/100) * IF(note.currency_conversion_rate = 0, 1.00000, note.currency_conversion_rate), 2)) AS CgstAmt, 
						 SUM(ROUND((cnm.qty * cnm.rate) * (IF(t.type = 'SGST', t.net_rate * IF(note.is_credit = cnm.is_credit, 1, -1), NULL)/100) * IF(note.currency_conversion_rate = 0, 1.00000, note.currency_conversion_rate), 2)) AS SgstAmt,
						 IF(t.type = 'CESS', SUM(t.net_rate) , NULL) AS CesRt,
						 IF(t.type = 'CESS', SUM(t.net_rate)/count(t.type) , NULL) * IF(note.currency_conversion_rate = 0, 1.00000, note.currency_conversion_rate) AS CesAmt,
						 0 AS CesNonAdvlAmt,
						 0 AS StateCesRt,
						 0 AS StateCesAmt,
						 0 AS StateCesNonAdvlAmt,
						 0 AS OthChrg,
						ROUND((SUM(ROUND(((cnm.qty * cnm.rate) * IF(note.is_credit = cnm.is_credit, 1, -1)), 2))/count(distinct cnmt.tax_code)
						 + SUM(ROUND((cnm.qty * cnm.rate) * (IF(t.type = 'CGST', t.net_rate * IF(note.is_credit = cnm.is_credit, 1, -1), 0)/100), 2))
						 + SUM(ROUND((cnm.qty * cnm.rate) * (IF(t.type = 'SGST', t.net_rate * IF(note.is_credit = cnm.is_credit, 1, -1), 0)/100), 2)) 
						 + SUM(ROUND((cnm.qty * cnm.rate) * (IF(t.type = 'IGST', t.net_rate * IF(note.is_credit = cnm.is_credit, 1, -1), 0)/100), 2)))
						  * IF(note.currency_conversion_rate = 0, 1.00000, note.currency_conversion_rate), 2) AS TotItemVal
						FROM
							crdrnote note 
						LEFT JOIN crdrnote_grn_map cgm ON cgm.crdrnote_id = note.grn_no
							AND cgm.enterprise_id = note.enterprise_id
						LEFT JOIN crdr_details_nonstock_material cnm ON cnm.grn_no = note.grn_no        							
							AND cnm.enterprise_id = note.enterprise_id
						LEFT JOIN unit_master um ON um.unit_id = cnm.unit_id
							AND um.enterprise_id = cnm.enterprise_id
						LEFT JOIN crdr_details_nonstock_material_tax cnmt ON cnm.grn_no  = cnmt.grn_no
							AND cnm.reason_id = cnmt.reason_id
							AND cnm.enterprise_id = cnmt.enterprise_id
						LEFT JOIN tax t ON cnmt.tax_code = t.code
							AND cnmt.enterprise_id = t.enterprise_id
						LEFT JOIN grn g ON g.grn_no = cgm.crdrnote_grn_no
							AND g.enterprise_id = cgm.enterprise_id	
						LEFT JOIN party_master p ON p.party_id = note.party_id
							AND p.enterprise_id = note.enterprise_id
						LEFT JOIN party_registration_detail prd ON p.party_id = prd.party_id
							AND p.enterprise_id = prd.enterprise_id
							AND prd.label = 'GSTIN'
						LEFT JOIN enterprise_registration_detail erd ON erd.enterprise_id = note.enterprise_id
							AND erd.label = 'GSTIN'
						LEFT JOIN enterprise e ON note.enterprise_id = e.id
					WHERE note.grn_no = %s
				GROUP BY note.grn_no, cnm.description""" % invoice_id
	return query


def icdEinvoiceNonItemCes(invoice_id=None, enterprise_id=None):
	"""

	:param invoice_id:
	:param enterprise_id:
	:return:
	"""
	query = """SELECT 
					note.grn_no,
					SUM(ROUND((cnm.qty * cnm.rate) * (IF(t.type = 'CESS', t.net_rate * IF(note.is_credit = cnm.is_credit, 1, -1), 0)/100), 2)) AS CesAmt,
					ROUND(SUM(IF(t.type = 'CESS', (t.net_rate), 0)), 2) AS CesRt											
				FROM
					crdrnote note 
				LEFT JOIN crdrnote_grn_map cgm ON cgm.crdrnote_id = note.grn_no
					AND cgm.enterprise_id = note.enterprise_id    	
				LEFT JOIN crdr_details_nonstock_material cnm ON cnm.grn_no = note.grn_no        							
					AND cnm.enterprise_id = note.enterprise_id
				LEFT JOIN unit_master um ON um.unit_id = cnm.unit_id
					AND um.enterprise_id = cnm.enterprise_id
				LEFT JOIN crdr_tax ct ON cnm.grn_no  = ct.crdr_id
					AND cnm.enterprise_id = ct.enterprise_id
				LEFT JOIN tax t ON ct.tax_code = t.code
					AND ct.enterprise_id = t.enterprise_id							
				WHERE note.grn_no = '%s' AND note.enterprise_id = '%s'
				GROUP BY note.grn_no""" % (invoice_id, enterprise_id)
	return query


def getFinancialYearMonths():
	today = date.today()
	if today.month < 4:  # Financial year starts from April
		start_year = today.year - 1
	else:
		start_year = today.year
	financial_year_months = [(date(start_year, month, 1).strftime('%b-%y')) for month in range(4, 13)] + \
							[(date(start_year + 1, month, 1).strftime('%b-%y')) for month in range(1, 4)]
	return financial_year_months


def getFinancialStartEndDate():
	today = date.today()
	if today.month < 4:
		start_date = date(today.year - 1, 4, 1)
	else:
		start_date = date(today.year, 4, 1)
	end_date = start_date.replace(year=start_date.year + 1) - timedelta(days=1)
	return start_date, end_date


def getMonthStartEndDate(month):
	try:
		input_string = month
		date_obj = datetime.strptime(input_string, "%b-%y")
		start_date = date_obj.replace(day=1)
		next_month = date_obj.replace(day=28) + timedelta(days=4)
		end_date = next_month - timedelta(days=next_month.day)
		return start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d")
	except Exception as e:
		print("Failed to Process Date - %s" % str(e))
