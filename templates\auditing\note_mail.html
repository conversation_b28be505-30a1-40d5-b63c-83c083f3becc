<!DOCTYPE html>
<html lang="en">
<head>
	<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400&display=swap" rel="stylesheet">
</head>
<body style="font-size:12px; font-family: <PERSON><PERSON>; background: #eee; padding: 30px 0;">
	<div border="0" cellpadding="0" cellspacing="0" style="max-width:800px;min-width:320px;width:100%; margin: 0 auto; font-size: 12px !important;">
		{% if include_btns == True %}
			<div style="text-align: right; font-size:14px;line-height:24px;margin-bottom: 15px;">
				<a href="<<approve_url>>" target="_blank" style="text-decoration: none;">
					<span style="display: inline-block;padding: 6px 12px;margin-bottom: 0;font-size: 14px;font-weight: normal;line-height: 1.42857143;text-align: center;cursor: pointer; border: 1px solid transparent;border-radius: 4px;background: #209be1;color: #FFF;">Accept Note</span>
				</a>
				<a href="<<reject_url>>" target="_blank" style="text-decoration: none;">
					<span style="display: inline-block;padding: 6px 12px;margin-bottom: 0;font-size: 14px;font-weight: normal;line-height: 1.42857143;text-align: center;cursor: pointer;border: 1px solid transparent;border-radius: 4px;color: #fff;background-color: #d9534f;">Reject Note</span>
				</a>
			</div>
		{% endif %}

		<div style="border:1px solid #ccc;padding: 28px; background: #FFF;">
			<div style="width: 100%;">
				<div style="width:70%;float:left;font-size:11px;">
					<img  style="max-height: 10mm">
					<div>{{ source.enterprise.name }}</div>
					<div>{{ enterprise_address }}</div>
					<div><b>Ph:</b>{% if source.enterprise.primary_contact_details.contact.phone_no %} {{ source.enterprise.primary_contact_details.contact.phone_no }} {% endif %}<b>E-mail:</b>{% if source.enterprise.primary_contact_details.contact.email %} {{ source.enterprise.primary_contact_details.contact.email }} {% endif %}</div>
				</div>

				<div style="width:30%;float:left;">
					<div style="width:100%;">
						<div style="width: 100%;float: left;">
							<span style="font-size: 16px;float:right;width:205px;" >{{ form_name }}</span>
						</div>
						<div style="width:65px;font-size: 10px;float: left;"><b>{{ gst_label }}</b></div>
						<div style="width:calc(100% - 1em);margin-left: -4px;font-size:10px;">{{ gst_detail }}</div>
					</div>
				</div>
			</div>

			<div style="width:100%;margin-top:80px;"></div>

			<div style="width:100%">
				<div style="width:50%;float:left;">
					<div>
						<div style="width:30%;float: left;font-weight:bold;font-size:13px;" >Vendor Name </div>
						<div style="width:calc(100% - 1em);font-size:11px;">: <b>{{ source.party.name }}</b></div>
						<div style="font-size:12px;">{{ source.party.address_1 }}, {{ source.party.address_2 }} </div>
						<div style="margin-left:111px;font-size:12px;">{{ source.party.city }}, {{ source.party.state }} </div>
					</div>
					<div>
						<div style="width:30%;float: left;font-weight:bold;font-size:13px;">Vendor Code</div>
						<div style="width:calc(100% - 1em);font-size:11px;">: {{ source.party.code }} <b style="font-size:13px;">GSTIN</b> : {% for reg_detail in source.party.registration_details %}
																									{% if reg_detail.label == "GSTIN" %}
																										{{ reg_detail.details }}
																									{% endif %}
																								{% endfor %}</div>
					</div>
					<div>
					   <div style="width:30%;float: left;font-weight:bold;font-size:13px;" >Phone No </div>
					   <div style="width:calc(100% - 1em);font-size:12px;">: {{ source.party.phone }}</div>
					</div>
				</div>

				<div style="width:50%;float:left;margin-left:-40px;">
					<div>
						<div style="width:28%;float: left;font-weight: bold;font-size:13px;margin-left:100px;">Note No.</div>
						<div style="width: calc(100% - 1em);font-size:12px;"><b>:</b> {{ source.getCode }} <i>{% if source.revisions.length > 0 %} (#{{ source.revisions.length }}) {% endif %}</i> </div>
					</div>

					<div>
						<div style="width:28%;float: left;font-weight: bold;font-size:13px;margin-left:100px;">Date</div>
						<div style="width: calc(100% - 1em);font-size:12px;"><b>:</b> {{ note_date }} </div>
					</div>

					<div>
						<div style="width:28%;float: left;font-weight: bold;font-size:13px;margin-left:100px;">GRN No</div>
						<div style="width: calc(100% - 1em);font-size:12px;"><b>:</b> {% if source.note_receipt_map %} {{ source.note_receipt_map.receipt.getCode }} {% else %} -NA- {% endif %} </div>
					</div>

					<div>
						<div style="width:28%;float: left;font-weight: bold;font-size:13px;margin-left:100px;">GRN Date</div>
						<div style="width: calc(100% - 1em);font-size:12px;"><b>:</b> {{ receipt_date }}</div>
					</div>
				</div>
			</div>

			<div style="width:100%;margin-top:185px;"></div>

			<div style="width:100%;">
				<div>Dear Sir/Madam,</div>
				<div style="text-align: center;">In Reference to {%if source.is_credit %} our {% else %} your {% endif %} <b>Invoice No.</b>: {{ source.inv_no }} (<i>Date: {{ source.inv_date |date:"M d, Y" }} Value: {{ source.inv_value }})</i></div>
				<div style="text-align: justified;">We wish to advice having {%if source.is_credit %} credited {% else %} debited {% endif %} your Account with us as follows :</div>
			</div>
			<div style="width:100%;">
				<table  border="0" cellpadding="0" cellspacing="0" style="border-collapse: collapse;width:100%;">
					<tr>
						<th rowspan="2" style="border:1px solid #000;line-height:18px;">S.No</th>
						<th rowspan="2" style="border:1px solid #000;line-height:18px;">Drawing No<br>Description</th>
						<th rowspan="2" style="border:1px solid #000;line-height:18px;">Reason</th>
						<th rowspan="2" style="border:1px solid #000;line-height:18px;">Quantity</th>
						<th rowspan="2" style="border:1px solid #000;line-height:18px;">Unit</th>
						<th rowspan="2" style="border:1px solid #000;line-height:18px;">Rate</th>
						<th rowspan="2" style="border:1px solid #000;line-height:18px;">Amount</th>
						<th colspan="2" style="border:1px solid #000;line-height:18px;">CGST</th>
						<th colspan="2" style="border:1px solid #000;line-height:18px;">SGST</th>
						<th colspan="2" style="border:1px solid #000;line-height:18px;">IGST</th>
					</tr>

					<tr>
						<th style="border:1px solid #000;line-height:18px;">%</th>
						<th style="border:1px solid #000;line-height:18px;;">({{ source.currency }})</th>
						<th style="border:1px solid #000;line-height:18px;">%</th>
						<th style="border:1px solid #000;line-height:18px;">({{ source.currency }})</th>
						<th style="border:1px solid #000;line-height:18px;">%</th>
						<th style="border:1px solid #000;line-height:18px;">({{ source.currency }})</th>
					</tr>
					{% for material in note_item_details %}
						<tr style="text-align:right;font-weight: normal;padding-right:3px;font-size:11px;" >
							<td style="border:1px solid #000;line-height:18px;text-align: center;font-size:11px;">{{forloop.counter}}</td>
							<td style="border:1px solid #000;width:500px;text-align:left;padding:5px;line-height:18px;font-size:11px;"><i>{% if material.item_code and material.item_code != "" %} {{ material.item_code }} - {% endif %} </i>{% if material.item_description and material.item_description != "" %} {{ material.item_description }} {% endif %} <i> {{ material.make }}</i> {% if material.hsn_code and material.hsn_code != "" %} <br> HSN/ SAC: {{ material.hsn_code }} {% endif %}</td>
							<td style="border:1px solid #000;width:150px;text-align:left;padding:5px;line-height:18px;font-size:11px;">{{ material.reason }}</td>
							<td style="border:1px solid #000;width:150px;font-size:11px;">{{ material.quantity }}</td>
							<td style="text-align:left;padding-left:3px;border:1px solid #000;text-align:left;padding:5px;line-height:18px;width:80px;font-size:11px;">{{ material.unit_name }}</td>
							<td style="text-align:right;padding:5px;border:1px solid #000;line-height:18px;width:150px;font-size:11px;">{{ material.rate }}</td>
							<td style="text-align:right;padding:5px;border:1px solid #000;line-height:18px;width:230px;font-size:11px;">{{ material.amount }}</td>
							<td style="text-align:right;padding:5px;border:1px solid #000;line-height:18px;font-size:11px;">{{ material.cgst_rate }}</td>
							<td style="text-align:right;padding:5px;border:1px solid #000;line-height:18px;font-size:11px;">{{ material.cgst_value }}</td>
							<td style="text-align:right;padding:5px;border:1px solid #000;line-height:18px;font-size:11px;">{{ material.sgst_rate }}</td>
							<td style="text-align:right;padding:5px;border:1px solid #000;line-height:18px;font-size:11px;">{{ material.sgst_value }}</td>
							<td style="text-align:right;padding:5px;border:1px solid #000;line-height:18px;font-size:11px;">{{ material.igst_rate }}</td>
							<td style="text-align:right;padding:5px;border:1px solid #000;line-height:18px;font-size:11px;">{{ material.igst_value }}</td>
						</tr>
					{% endfor %}
					<tr style="text-align:right;font-weight: normal;padding-right:3px;font-size:11px;line-height:23px;">
						<td colspan="2" style="border-left:hidden;border-bottom:hidden;border-right:hidden;"></td>
						<td colspan="2" style="border:1px solid #000;border-right:hidden;"><b>Sub Total</b></td>
						<td colspan="3" style="border:1px solid #000;border-right:hidden;"><b>{{ summary_details.total }}</b></td>
						<td colspan="2" style="border:1px solid #000;border-right:hidden;">{{ summary_details.net_cgst }}</td>
						<td colspan="2" style="border:1px solid #000;border-right:hidden;">{{ summary_details.net_sgst }}</td>
						<td colspan="2" style="border:1px solid #000;border-right:hidden;">{{ summary_details.net_igst }}</td>
					</tr>
					{% for tax in note_taxes %}
						<tr style="line-height:23px;font-size:11px;">
							<td style="text-align: right;border-bottom:hidden;border-left:hidden;border-right:hidden;line-height:25px;"></td>
							<td colspan="3" style="text-align: right;border-bottom:hidden;border-left:hidden;border-right:hidden;line-height:25px;">{{ tax.tax_name }}</td>
							<td colspan="1" style="text-align: right;border-bottom:hidden;border-left:hidden;border-right:hidden;line-height:25px;">{{ tax.tax_rate }}</td>
							<td colspan="1" style="text-align: right;border-bottom:hidden;border-left:hidden;border-right:hidden;line-height:25px;text-align:left" ></td>
							<td style="text-align: right;border-bottom:hidden;border-left:hidden;border-right:hidden;line-height:25px;">{{ tax.tax_value }}</td>
							<td style="text-align: right;border-bottom:hidden;border-left:hidden;border-right:hidden;line-height:25px;"></td>
						</tr>
					{% endfor %}
					{% if note_taxes %}
						<tr>
							<td style=":border-top:1px solid #000;"></td>
						</tr>
					{% endif %}

					{% if source.round_off and source.round_off != 0 %}
						<tr style="text-align:right;font-weight: normal;padding-right:3px;font-size:11px;line-height:23px;">
							<td colspan="2" style="border-left:hidden;border-right:hidden;border-bottom:hidden;"></td>
							<td colspan="2"  style="border-bottom:1px solid #000;border-left:hidden;border-right:hidden;font-size:11px;text-align:right;"><b>Round-off</b></td>
							<td style="border-bottom:1px solid #000;;border-left:hidden;border-right:hidden;font-size:11px;text-align:center;">{{ source.currency }}</td>
							<td colspan="2" style="border-bottom:1px solid #000;border-left:hidden;border-right:hidden;font-size:11px;"><b>{{ summary_details.round_off }}</b></td>
							<td colspan="2" style="border-bottom:1px solid #000;border-left:hidden;border-right:hidden;font-size:11px;"></td>
						</tr>
					{% endif %}

					<tr style="line-height:23px;font-size:11px;">
						<td colspan="4" style="border:1px solid #000;border-left:hidden;border-right:hidden;text-align:right;"><b>Total Value</b></td>
						<td style="border-bottom:1px solid #000;border-left:hidden;text-align:right;">{% if source.currency != None %} {{ source.currency }} {% endif %}</td>
						<td colspan="2" style="border:1px solid #000;border-left:hidden;border-right:hidden;text-align:right;"><b>{{ summary_details.grand_total }}</b></td>
						<td colspan="6" style="border:1px solid #000;border-left:hidden;border-right:hidden;text-align:right;"></td>
					</tr>
				</table>
				<div>
					<span style="border-top:1px solid #000;border-left:hidden;border-right:hidden;border-bottom:hidden;font-size:11px;"></span>
				</div>
				<div>
					{% if summary_details.grand_total_in_words != "" %}
						<span style="text-align:left;border-left:hidden;border-right:hidden;border-bottom:hidden;font-size:11px;">({{ source.currency }} {{ summary_details.grand_total_in_words }})</span>
					{% endif %}
				</div>
				<div id="footer" style="margin-top:50px;width:100%; font-size:11px;margin-bottom:80px;">
					<div style="width: 49.333%;float:left;  text-align:left;">
						<span>{{ note_drafter }}<br>
						Prepared By
						</span>
					</div>
					<div style="width: 49.333%; float:right; text-align:right;">
						<span>{{ note_approver }}<br>
						Authorised Signatory
						</span>
					</div>
				</div>
			</div>

			</div>

		</div>
	</div>
</body>
</html>
