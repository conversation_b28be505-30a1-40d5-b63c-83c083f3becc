"""
"""
import jwt
from django.test import TestCase
from erp.sales import logger
from erp.auth.request_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from settings import JWT_SECRET

__author__ = 'pavithra'


class AuthTestSubscription(TestCase):

	def setUp(self):
		pass

	def tearDown(self):
		pass

	def test_reseted_session_data(self, request=None):
		request_handler = RequestHandler(request)
		data_reseted = request_handler.validateAndResetExpiredOnValue(enterprise_id=102)
		logger.info("Session data has been reseted:%s" % data_reseted)
		self.assertEquals(data_reseted, True)

	def test_tokenGenerator(self):
	   token = RequestHandler.generateJwtToken(user_id=56, enterprise_id=102)
	   decoded_token = jwt.decode(token, JWT_SECRET)
	   self.assertEqual([56, 102], [decoded_token["user_id"], decoded_token["enterprise_id"]])