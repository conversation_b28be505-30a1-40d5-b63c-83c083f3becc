<style>
	.title_content{
		text-align: center;
	    background: #4a90e2;
	    color: #fff;
	    box-shadow: 0px 2px 23px 1px #ccc;
	    border-radius: 9px 9px 0px 0px;
	    font-size: 20px;
	    padding: 20px 0 26px;
	}
	.body_content{
		background: #fff;
		height: 535px;
		box-shadow: 3px -3px 30px -2px #ccc;
		width: 100%;
		margin-top: -20px;
		border-radius: 0px 0px 10px 10px;
		border:  1px solid #ddd;
		border-top:  none !important;
	}

	.price, .original-price{
		font-size: 50px;
	}

	.price {
		text-decoration: line-through;
	}

	.original-price {
		display: block;
	    text-align: center;
	    margin-left: -74px;
	    margin-bottom: -23px;
	    font-family: 'Permanent Marker', cursive;
	    font-weight: normal;
	}

	.price_symbol{
	    font-size: 28px;
	    font-weight:300;
	}

	.pay_button{
		color: #FFF;
	    border: #4a90e2;
	    background-color: #4a90e2;
	    border-radius: 5px;
	    font-size: 16px;
	    text-align: center;
	    padding: 10px 35px 11px 35px;
	    line-height: 50px;
	    text-decoration:none !important;
	}

	.pay_button:hover{
		box-shadow: 0px 3px 12px #c8c7c8;
    	color: #FFF;
	}

	.plus_tax{
		font-size: 18px;
		vertical-align: text-bottom;
	}

	.body_content img{
		height: 125px;
		background-position: center;
	}

	.price_feature{
		list-style: none;
		text-align:center;
		line-height: 30px;
		padding-left: 0;
		color: #777;
		padding: 0 60px;
	}

	.price_feature li {
		border-bottom: 1px solid #ddd;
		padding: 5px 0;
	}

	.price_feature li:last-child {
		border-bottom: none;
	}

	.active .body_content {
		border:  solid 2px #209be1;
	}

	#subscription-modal .modal-content {
		overflow-y:  auto;
	}
</style>
<script async src="https://www.googletagmanager.com/gtag/js?id=AW-*********"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'AW-*********');
</script>
<link href="https://fonts.googleapis.com/css2?family=Permanent+Marker&display=swap" rel="stylesheet">
<div id="subscription-modal" class="modal fade modal-fullscreen" role="dialog">
  	<div class="modal-dialog" style="width: 100%; margin: 0px;">
    	<div class="modal-content">
      		<div class="modal-header form-register" style="max-width: 1280px; width: 85%; margin: 0 auto; border: none;">
      			<button type="button" class="enterprice-modal-close close" data-dismiss="modal" style="position: absolute; right: 15px; font-size: 36px; color: #999; opacity: 0.8;">&times;</button>
      		</div>
      		<div class="modal-body" style="max-width: 1280px; width: 85%; margin: 0 auto; max-height: initial; position: fixed;top: 48%; left: 50%; transform: translate(-50%, -50%);;">
        		<div>
			        <form hidden="hidden" id="id-payment_details_form" method="POST" action="/erp/admin/pay/initiateSubscriptionPayment/">{% csrf_token %}
						<input id="id-pay_type" type="hidden" name="pay_type" value=""/>
					</form>
					<div class="col-sm-4 {% if subscription.plan == 'XSBASIC' %}active{% endif %}">
						<h4 class="title_content">BASIC</h4>
						<div class="body_content">
							<center><img src="/site_media/images/basic.png"></center><br>
							<div class="text-center" style="padding-bottom: 20px;">
								<span class="original-price"> {{basic_material_price}}</span>
								<span class="price_symbol">₹</span>
								<span class="price"> 10000</span>
								<span class="plus_tax"> + Taxes <small style="font-size: 12px;">/year</small></span>
							</div>
							<ul class="price_feature">
								<li>5 Users</li>
								<li>Access to all Modules</li>
								<li>Mobile App</li>
								<li>Limited Support</li>
							</ul>

							<div class='text-center'>
								<a role="button" class="pay_button subscription_button" data-plan="XSBASIC" >PAY NOW</a>
							</div>
						</div>
					</div>

					<div class="col-sm-4 {% if subscription.plan == 'XSSTANDARD' %}active{% endif %}" >
						<h4 class="title_content">STANDARD</h4>
						<div class="body_content">
							<center><img src="/site_media/images/standard.png"></center><br>
							<div class="text-center" style="padding-bottom: 20px;">
								<span class="original-price"> {{standard_material_price}}</span>
								<span class="price_symbol">₹</span>
								<span class="price"> 18000</span>
								<span class="plus_tax"> + Taxes <small style="font-size: 12px;">/year</small></span>
							</div>
							<ul class="price_feature">
								<li>10 Users</li>
								<li>Access to all Modules</li>
								<li>Mobile App</li>
								<li>Extended Support</li>
							</ul>
							<div class='text-center'>
								<a role="button" class="pay_button subscription_button" data-plan="XSSTANDARD">PAY NOW</a>
							</div>
						</div>
					</div>

					<div class="col-sm-4 {% if subscription.plan == 'XSENTERPRISE' %}active{% endif %}">
						<h4 class="title_content">ENTERPRISE</h4>
						<div class="body_content">
							<center><img src="/site_media/images/enterprise.png"></center><br>
							<div class="text-center" style="padding-bottom: 29px;margin-top: 70px;">
								<span class="price_symbol">₹</span>
								<span class="price" style="font-size: 24px; text-decoration: none;"> _ _ _ _ _</span>
							</div>
							<ul class="price_feature">
								<li>Unlimited Users</li>
								<li>Access to all Modules</li>
								<li>Mobile App</li>
								<li>Dedicated Support</li>
							</ul>
							<div class='text-center'>
								<a role="button" class="pay_button" onclick="requestEnterpriseSubscription()">REACH SALES</a>
							</div>
						</div>
					</div>
				</div>
      		</div>
		</div>
  	</div>
</div>

<script type="text/javascript">
	$(document).ready(function(){
		$(".modal-fullscreen .modal-dialog, .modal-fullscreen .modal-content").css("height", $(window).height());
		$(window).resize(function(){
			$(".modal-fullscreen .modal-dialog, .modal-fullscreen .modal-content").css("height", $(window).height());
		});

		$('.subscription_button').click(function(){
			gtag('event', 'conversion', {'send_to': 'AW-*********/CSFUCI6FscsCEJeN3NgC'});
			$('#id-pay_type').val($(this).attr("data-plan"));
			$("#id-payment_details_form").submit();
		});
	});
</script>