{% extends "purchase/sidebar.html" %}
{% block purchase_orders %}
<style xmlns="http://www.w3.org/1999/html">
#custom-tab3 {
	border-radius: 0;
    border-right: 1px solid #ccc;
}

#custom-tab2 {
	border-radius: 50px 0 0 50px;
    border-right: 1px solid #ccc;
    padding-left: 30px;
    padding-right: 30px;
}

.create_po_container a.btn-new-item {
	box-shadow: -4px 4px 5px 0 rgba(60,64,67,0.302), 0 1px 3px 1px rgba(60,64,67,0.149);
}

.create_indent_po.btn-new-item  {
	border-radius: 0 50px 50px 0;
}

.btn-new-item-label {
    color: #004195;
    padding: 6px !important;
}

.btn-new-item-label:after {
	content: '';
}

.btn-new-item-label.create_jo_page:after {
    content: 'Create';
}

.btn-new-item-label.normal_po:after {
	content: '+';
	font-size: 24px;
}

.btn-new-item-label.quick_po:after {
	content: 'Quick';
}

.btn-new-item-label.po_via_indent:after {
	content: 'via Indent';
}

#custom-tab3:hover,
#custom-tab2:hover,
.create_indent_po:hover {
	background: rgba(32, 155, 225,0.1);
	box-shadow: -4px 4px 5px 0 rgba(60,64,67,0.302), 0 1px 3px 1px rgba(60,64,67,0.149) !important;
}

#chooseIndent .dataTables_paginate {
	margin-right: 0 !important;
}

</style>

<script type="text/javascript" src="/site_media/js/po_list.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/mailer.js?v={{ current_version }}"></script>

<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">{{ template_title }}</span>
	</div>
	<div style="display:none">
		<form id="po_submit" method="post" action="/erp/purchase/po_list/">
			{%csrf_token%}
			<input type="hidden" value="{{ po.po_id.value }}" id="id_po-id" name="po_no"/>
			<input type="hidden" value="{{ type }}" id="id_edit_dc_type" name="edit_dc_type"/>
			<input type="submit" value="Edit" id="po_resubmit" hidden="hidden"/>
			<input type="hidden" value="{{ order_type }}" id="id_order_type" name="order_type"/>
		</form>
	</div>
	<div class="col-lg-12 remove-padding">
		<div class="page-heading_new">
			<input type="hidden" class="indent_access" id="indent_access" name="indent_access" value="{{ module_access.indent }}" />
			<span class="page_header" style="margin-left: 15px;"></span>
            {% if order_type == 'po' %}
                {% if access_level.edit %}
                    <div class="create_po_container" style="margin-right: 15px;">
                        <a class="btn btn-new-item pull-right create_indent_po" data-tooltip="tooltip" {% if not indent_access %} title="Enable Indent module in Enterprise Configurations to avail this feature" {% else %} title="Create P.O. through Indent" data-toggle="modal" data-target="#chooseIndent" {% endif %}>
                            <span class="btn-new-item-label po_via_indent"></span>
                        </a>
                        <a href="/erp/purchase/{{order_type}}/?id=custom-tab3" class="btn btn-new-item pull-right create_quick_po" id="custom-tab3" data-tooltip="tooltip" title="Create Quick P.O.">
                            <span class="btn-new-item-label quick_po"></span>
                        </a>
                        <a href="/erp/purchase/{{order_type}}/?id=custom-tab2" class="btn btn-new-item pull-right create_quick_po" id="custom-tab2" data-tooltip="tooltip" title="Create New P.O.">
                            <i class="fa fa-plus" aria-hidden="true"></i>
                        </a>
                    </div>
                {% else %}
                    <div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in Purchase Module. Please contact the administrator." style="margin-right: 15px;">
                        <a class="btn btn-new-item pull-right create_indent_po disabled">
                            <span class="btn-new-item-label po_via_indent"></span>
                        </a>
                        <a class="btn btn-new-item pull-right create_quick_po disabled" id="custom-tab3">
                            <span class="btn-new-item-label quick_po"></span>
                        </a>
                        <a class="btn btn-new-item pull-right create_quick_po disabled" id="custom-tab2">
                            <i class="fa fa-plus" aria-hidden="true"></i>
                        </a>
                    </div>
                {% endif %}
            {% else %}
                {% if access_level.edit %}
                    <a href="/erp/purchase/{{order_type}}/?id=custom-tab2" class="btn btn-new-item pull-right create_quick_po"
                       id="custom-tab2" data-tooltip="tooltip" title="Create New P.O." style="border-radius: 50px; padding: 6px 20px 6px; margin-right: 15px;">
                        <i class="fa fa-plus" aria-hidden="true"></i>
                        <span class="btn-new-item-label create_jo_page"></span>
                    </a>
                {% else %}
                    <div class="disabled-upload-container">
                        <a class="btn btn-new-item pull-right create_quick_po" id="custom-tab2" style="border-radius: 50px; padding: 6px 20px 6px; margin-right: 15px;">
                            <i class="fa fa-plus" aria-hidden="true"></i>
                            <span class="btn-new-item-label create_jo_page"></span>
                        </a>
                    </div>
                {% endif %}
            {% endif %}
			<a href="/erp/purchase/po_list/" class="btn btn-add-new pull-right view_po hide" style="margin-right: 15px; margin-bottom: 15px;" data-tooltip="tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
			<span class="prev_next_container"></span>
		</div>
		<div class="tab-content">
			<div>
				<div class="view_table add_table">
					<div class="filter-components" style="width: calc(100% - 674px); margin-left: 340px; margin-bottom: -12px; margin-top: 25px;">
						<div class="filter-components-container">
							<div class="dropdown">
							<button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
									<i class="fa fa-filter"></i>
								</button>
								<span class="dropdown-menu arrow_box arrow_box_filter">
									<div class="col-sm-12 form-group" >
										<label>Date Range</label>
										<div id="reportrange" class="report-range form-control">
											<i class="glyphicon glyphicon-calendar"></i>&nbsp;
											<span></span> <b class="caret"></b>
											<input type="hidden" class="fromdate" id="fromdate" name="fromdate" value="{{ since }}" />
											<input type="hidden" class="todate" id="todate" name="todate" value="{{ till }}"/>
										</div>
									</div>
									<div class="col-sm-12 form-group">
										<label>Project/Tag</label>
										<select class="form-control chosen-select" name="select" id="project">
											<option value="0" >ALL</option>
											{% for j in projects %}
											<option value="{{ j.0 }}" {% if project_id == j.0 %}selected{% endif %}>{{ j.1 }}</option>
											{% endfor %}
										</select>
									</div>
									<div class="col-sm-12 form-group">
										<label>Status</label>
										<select class="form-control" name="select" id="status">
											<option value="5" >ALL</option>
											<option value="0" {% if status == '0' %}selected{% endif %}>Draft</option>
											<option value="1" {% if status == '1' %}selected{% endif %}>Reviewed</option>
											<option value="2" {% if status == '2' %}selected{% endif %}>Approved</option>
											<option value="3" {% if status == '3' %}selected{% endif %}>Rejected</option>
										</select>
									</div>
									<div class="filter-footer">
										<input type="button" class="btn btn-save" value="Apply" id="filterPO"/>
			      					</div>
								</span>
							</div>
							<span class='filtered-condition filtered-date'>Date: <b></b></span>
							<span class='filtered-condition filtered-project'>Project/Tag: <b></b></span>
							<span class='filtered-condition filtered-status'>Status: <b></b></span>
						</div>
					</div>
				</div>
				<div class="col-lg-12">
					<div class="col-lg-12" id="search_result">
						<div class="csv_export_button">
							<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#po_list'), 'PO_List.csv']);" data-tooltip="tooltip" title="Download P.O. List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
						</div>
						<table class="table table-bordered custom-table table-striped" id ="po_list" style="width: 100%;">
							<thead>
								<tr>
									<th style="min-width: 40px; max-width: 40px;"> S. No </th>
									<th class="exclude_export" hidden="hidden"></th>
									<th style="min-width: 100px; max-width: 100px;">Draft Date</th>
									<th style="min-width: 120px; max-width: 120px;"> {{order_type}} No </th>
									<th style="min-width: 70px; max-width: 70px;"> {{order_type}} Date </th>
									<th> Supplier </th>
									<th style="min-width: 70px; max-width: 70px;"> {{order_type}} Value </th>
									<th> Project</th>
									{% if  module_access.indent %}
										<th style="min-width: 120px; max-width: 120px;"> Indent No </th>
									{% endif %}
									<th hidden="hidden" class="exclude_export"></th>
									<th style="min-width: 100px; max-width: 100px;"> Status </th>
								</tr>
							</thead>
							<tbody>
							</tbody>
						</table>
					</div>
				</div>
				<div class="clearfix"></div>
			</div>
        </div>
    </div>
	<div class="hide">
		<form id="id-edit_po_form" method="POST" action="/erp/purchase/{{order_type}}/">{% csrf_token %}
			<input type="hidden" name="po_no" id="id-edit_po_id" value="" />
			<input type="hidden" name="po_status" id="id-edit_po_status" value="" />
		</form>
	</div>
</div>
{% include "purchase/po_document.html" %}
<div id="chooseIndent" class="modal fade" role="dialog" >
  	<div class="modal-dialog modal-lg">
    	<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Choose Indent</h4>
      		</div>
      		<div class="modal-body" style="padding-top: 50px;">
            	<table class="table table-bordered custom-table table-striped" id="indent_po">
                    <thead>
	                    <tr>
	                        <th style="width: 35px;">S.No.</th>
	                        <th style="width: 110px;">Indent No</th>
	                        <th style="width: 70px;">Date</th>
	                        <th>Indent For</th>
	                    </tr>
                    </thead>
                    <tbody>
                        {% for indent in pending_indents %}
	                        <tr>
	                            <td class="text-center">{{ forloop.counter }}</td>
		                        <td class="text-center">
		                        	<form action="/erp/stores/indent/" method="post"
										id="edit_{{ indent.0 }}" target="_blank">{% csrf_token %}
										<a role="button" onclick="javascript:clickButton('editIndent_{{ indent.0 }}');">
											{{ indent.6 }}</a>
										<input type="hidden" value="{{ indent.0 }}" id="id_indent_no_{{ indent.0 }}" name="indent_no"/>
										<input type="submit" value="Edit" id="editIndent_{{ indent.0 }}" hidden="hidden"/>
									</form>
								</td>
	                            <td class="text-center">{{ indent.4|date:'M d, Y' }}</td>
	                            <td>{{ indent.3 }}</td>
	                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
      		</div>
    	</div>
  	</div>
</div>
<script type="text/javascript">
$(document).ready(function() {
	initializePage();
	$("#template_configuration").removeClass('hide');
	$("#template_configuration").click(function(){
		window.open('/erp/admin/po_template/','_blank');
	});
	if($("#id_order_type").val() == "jo"){
		$("#custom-tab2").attr('data-tooltip', 'tooltip').attr('data-original-title', 'Create New J.O.');
		$("#custom-tab3").attr('data-tooltip', 'tooltip').attr('data-original-title', 'Create Quick J.O.');
		$(".create_indent_po").attr('data-tooltip', 'tooltip').attr('data-original-title', 'Create J.O. through Indent');
	}
});
</script>
{% endblock %}
