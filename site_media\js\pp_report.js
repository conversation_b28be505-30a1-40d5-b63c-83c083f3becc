var onPageLoad = function() {
    $("#id-search_isr").click(function () {
        productionPlanReport();
    });
}

function TableHeaderFixed() {
    oTable = $('#pp_report').DataTable({
        fixedHeader: false,
        "pageLength": 50,
        "scrollY": Number($(document).height() - 230),
        "scrollX": true,
        "orderCellsTop": true,
        "search": {
            "smart": false
        }

    });
    oTable.on("draw",function() {
        var keyword = $('#pp_report_filter > label:eq(0) > input').val();
        $('#pp_report').unmark();
        $('#pp_report').mark(keyword,{});
    });
    oTable.on('page.dt', function() {
      $('html, body').animate({
        scrollTop: $(".dataTables_wrapper").offset().top - 15
       }, 'slow');
    });
    oSettings = oTable.settings();
    $( window ).resize();
}


function loadAllMaterials(itemId, makeId, isFaulty) {
    var dataToSend = {
        'type': $("#id_invoice-type option:selected").val(),
        'party_id': $("#id_invoice-party_id option:selected").val(),
        'module': 'invoice',
        'particulars':'invoice_materials'
    }
    generateMaterialAsComboBox(itemId, makeId, isFaulty, dataToSend)
    productionPlanReport();
}

function productionPlanReport() {
    $('#loading').show();

    var material = $('#id-material').val().split("[::]");
    var [itemId, makeId, isFaulty] = $('#id-material').val().split("[::]");
    var searchCriteria = {
        since: $('.fromdate').val(), till: $('.todate').val(),
        is_detailed: $('#id-is_detailed').is(":checked"),
        item_id: itemId, make_id: makeId, is_faulty: isFaulty
    };
    $.ajax({
        url: "/erp/stores/json/pp_register/",
        type: "post",
        dataType: "json",
        data: searchCriteria,
        success: function(response) {
            $('#pp_report').DataTable().clear();
            $('#pp_report').DataTable().destroy();
            $("#pp_report tbody").find("tr").remove();
            s_no = 0
            $.each(response.report, function (key, value) {
                s_no = s_no + 1
                constructReportRow(key, s_no, value);
            });
            TableHeaderFixed();
            updateFilterText();
            closeFilterOption();
            $('#loading').hide();
        }, error: function(xhr,errmsg,err) {
            $('#loading').hide();
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });
}

/**
 *
 */
function constructReportRow(key, s_no, value) {

    if(value.grn_date !="") {
        setGRNDateFormat = formatDate(value.grn_date)
    }
    else {
        var setGRNDateFormat="";
    }

    var row = `<tr bgcolor="#ececec" border="0" align="center" style="font-size:16px; font-weight:normal;">
        <td class="text-left"> ${s_no} </td>
        <td class="text-center"> ${setGRNDateFormat}</td>
        <td class="text-left"> ${value.grn_no} </td>
        <td class="text-left"> ${value.project} </td>
        <td class="text-left"> ${value.received_from} </td>
        <td class="text-left"> ${value.material_name} </td>
        <td> ${value.qty} </td>
        <td> ${value.unit} </td>
        </tr>`;
    $('#pp_report').append(row);
}

function formatDate(change_date){
    var setDateTime = change_date.split(' ');
    var setDateFormat = setDateTime[0].split('-');
    setDateFormat = setDateFormat[1]+"/"+setDateFormat[2]+"/"+setDateFormat[0];
    setDateFormat = moment(setDateFormat).format('MMM D, YYYY')
    return setDateFormat
}