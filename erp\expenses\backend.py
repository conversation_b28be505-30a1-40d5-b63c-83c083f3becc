"""
"""
import json
from datetime import datetime

from dateutil.relativedelta import relativedelta
from sqlalchemy import or_, and_

from erp import helper, dao
from erp.accounts.backend import AccountService
from erp.accounts.changelog import VoucherChangelog
from erp.admin import enterprise_module_settings
from erp.commons.backend import push_notification
from erp.dao import DataAccessObject
from erp.expenses import *
from erp.forms import ExpenseForm
from erp.formsets import ExpenseParticularsFormset
from erp.models import Expense, ExpenseParticular, ExpenseTag, User, UserPermission, VoucherParticulars, Voucher, \
	UserClaimHead, Enterprise
from erp.notifications import PUSH_NOTIFICATION
from erp.tags import getEntityTagMaps, generateTagFormset, extractTagsFromFormset
from util.api_util import response_code
from util.ftp_helper import FTPUtil
from util.helper import writeFile, copyDictToDict, getFinancialYear

__author__ = 'nandha'


class ExpenseVO:
	"""
	To process the expenses expense_particulars and expense_tag formset
	"""

	def __init__(self, enterprise_id=None, user_id=None, expense=None, request_data=None):
		"""

		:param enterprise_id:
		:param user_id:
		:param expense:
		:param request_data:
		"""
		self.enterprise_id = enterprise_id
		self.remarks_list = []
		self.approved_amount = 0
		self.claimed_amount = 0
		self.expense_code = ""
		self.expense_form = self.__generateExpenseForm(user_id=user_id, expense=expense, request_data=request_data)
		self.expense_particulars_formset = self.__generateParticularsFormset(expense=expense, request_data=request_data)
		if expense:
			self.expense_tag_formset = generateTagFormset(request_data=request_data, tags=expense.expense_tags)
		else:
			self.expense_tag_formset = generateTagFormset(request_data=request_data)

	def __generateExpenseForm(self, user_id=None, expense=None, request_data=None, prefix=EXPENSE_HEAD_PREFIX):
		"""

		:param expense:
		:param user_id:
		:param request_data:
		:param prefix:
		:return: formset type [ExpenseForm]
		"""

		if request_data:
			return ExpenseForm(data=request_data, enterprise_id=self.enterprise_id, user_id=user_id, prefix=prefix)
		elif expense:
			initializer = copyDictToDict(source=expense.__dict__, exclude_keys=EXPENSE_EXCLUDE_FIELD_LIST)

			if expense.claim_head_ledger:
				initializer['claim_head_ledger_name'] = expense.claim_head_ledger.name
			initializer['code'] = self.expense_code = expense.getCode()
			initializer['remarks'] = ""

			if expense.remarks:
				self.remarks_list = expense.remarks
				for remark in self.remarks_list:
					remark['date'] = datetime.strptime(remark['date'], '%Y-%m-%d %H:%M:%S').strftime("%b %d, %Y")
			else:
				self.remarks_list = []
			self.claimed_amount, self.approved_amount = expense.getTotalExpense()
			initializer['status_code'] = STATUS_DISPLAY[expense.status]
			if expense.created_user:
				initializer['claimed_user'] = expense.created_user

			return ExpenseForm(initial=initializer, prefix=prefix, enterprise_id=expense.enterprise_id, user_id=user_id)
		else:
			return ExpenseForm(enterprise_id=self.enterprise_id, user_id=user_id, prefix=prefix)

	def __generateParticularsFormset(self, expense=None, request_data=None, prefix=EXPENSE_ITEM_PREFIX):
		"""

		:param expense:
		:param request_data:
		:param prefix:
		:return: formset type [ExpenseParticularsFormset]
		"""

		if request_data:
			return ExpenseParticularsFormset(data=request_data, prefix=prefix)
		else:
			formset_initializer = []
			for particular in expense.particulars:
				initializer = copyDictToDict(
					source=particular.__dict__, exclude_keys=EXPENSE_PARTICULAR_EXCLUDE_FIELD_LIST)
				initializer['expense_head_ledger_name'] = particular.expense_head_ledger.name
				initializer['net_amount'] = particular.getApprovedAmount()
				initializer['spent_on'] = particular.spent_on
				initializer['enterprise_id'] = self.enterprise_id
				initializer['remarks'] = ""

				remarks = ""
				if particular.remarks:
					logger.info("Remarks count %s" % len(particular.remarks))
					for item in particular.remarks:
						if remarks == "":
							remarks = "%s: %s" % (item['by'], item['remarks'])
						else:
							remarks = "%s, %s: %s" % (remarks, item['by'], item['remarks'])
				initializer['remarks_list'] = remarks
				formset_initializer.append(initializer)
			return ExpenseParticularsFormset(initial=formset_initializer, prefix=prefix)

	def __extractExpenseParticularsAsJson(self):
		"""

		:return:
		"""
		try:
			particulars = []
			for form in self.expense_particulars_formset:
				if not form.is_valid():
					raise Exception("Invalid Expense Particular form data")

				if not form.cleaned_data['DELETE']:
					json_particular = copyDictToDict(
						source=form.cleaned_data, exclude_keys=EXPENSE_PARTICULAR_EXCLUDE_FIELD_LIST)
					json_particular['remarks'] = form.cleaned_data['remarks']
					try:
						json_particular['spent_on'] = datetime.strptime(
							json_particular['spent_on'], "%b %d, %Y").strftime("%Y-%m-%d")
					except Exception as e:
						json_particular['spent_on'] = json_particular['spent_on']
						logger.error("Expense form submit %s; Taken date without parsing" % e.message)

					particulars.append(json_particular)
			return particulars
		except:
			raise

	def getExpenseJson(self):
		"""

		:return:
		"""
		try:

			if not self.expense_form.is_valid():
				logger.error("Expense form:  %s" % self.expense_form.errors)
				raise Exception("Invalid form data")

			expense_dict = copyDictToDict(
				source=self.expense_form.cleaned_data, exclude_keys=EXPENSE_EXCLUDE_FIELD_LIST)
			expense_dict['enterprise_id'] = self.enterprise_id
			expense_dict['status'] = int(expense_dict['status_to_be_changed'])
			expense_dict['particulars'] = self.__extractExpenseParticularsAsJson()
			expense_dict['tags'] = extractTagsFromFormset(tag_formset=self.expense_tag_formset)
			expense_dict['remarks'] = self.expense_form.cleaned_data['remarks']

			return expense_dict
		except:
			raise

	def is_valid(self):
		"""

		:return:
		"""
		is_valid = self.expense_form.is_valid() and self.expense_particulars_formset.is_valid() and self.expense_tag_formset.is_valid()
		logger.error(
			"Expense Form Errors: %s\n Expense Particular formset Errors: %s\n Expense Tag formset Errors: %s" % (
				self.expense_form.errors, self.expense_particulars_formset.errors, self.expense_tag_formset.errors))
		return is_valid


class ExpenseDAO(DataAccessObject):
	"""
	To process the expenses expense_particulars and expense_tag data with db and db_session
	"""

	def getExpense(self, enterprise_id=None, expense_id=None, financial_year=None, expense_no=None, sub_number=None):
		try:
			if expense_id:
				return self.db_session.query(Expense).filter(
					Expense.id == expense_id, Expense.enterprise_id == enterprise_id).first()
			else:
				return self.db_session.query(Expense).filter(
					Expense.enterprise_id == enterprise_id, Expense.financial_year == financial_year,
					Expense.expense_no == expense_no, Expense.sub_number == sub_number).first()
		except:
			raise

	def listExpenseObjects(
			self, enterprise_id=None, user_id=None, since=datetime.now() + relativedelta(days=-29),
			till=datetime.now(), my_expense_flag=None, status=None):
		"""

		:param enterprise_id:
		:param user_id:
		:param since:
		:param till:
		:param my_expense_flag: 'true' to get only current user's expense and 'false' for others
		:param status:
		:return: List of Expenses [obj1, obj2....]
		"""

		try:
			logger.info('Fetching expense list between %s - %s' % (since, till))
			user = self.db_session.query(User).filter(User.id == user_id).first()
			# Preparing query
			query = self.db_session.query(Expense).outerjoin(UserClaimHead, and_(
				UserClaimHead.ledger_id == Expense.claim_head_ledger_id, UserClaimHead.user_id == user_id))
			# Getting permission
			is_approver = user.hasModuleAccess(module_code="EXPENSES", access_level=UserPermission._APPROVE) > 0
			is_icd_edit = user.hasModuleAccess(module_code="ICD", access_level=UserPermission._EDIT) > 0

			if status is not None:
				query = query.filter(Expense.status == status)
			query = query.filter(Expense.enterprise_id == enterprise_id)

			# Filters
			my_expenses = and_(Expense.created_by == user_id, Expense.created_on >= since, Expense.created_on < till)
			other_expenses = and_(
				Expense.created_by != user_id, Expense.confirmed_on >= since, Expense.confirmed_on < till)
			all_drafts = and_(Expense.status == 0, Expense.created_on >= since, Expense.created_on < till)
			non_drafts = and_(Expense.status != 0, Expense.confirmed_on >= since, Expense.confirmed_on < till)
			allowed_statuses_to_icd = [STATUS_APPROVED, STATUS_ICD_CHECKED, STATUS_ICD_VERIFIED]

			# Applying additional filters on the query
			if my_expense_flag is True or (status and int(status) == STATUS_DRAFT):
				query = query.filter(my_expenses).order_by(Expense.created_on.asc())
			elif my_expense_flag is False:
				query = query.filter(other_expenses)
				if is_icd_edit and not is_approver:
					query = query.filter(Expense.status.in_(allowed_statuses_to_icd))
				query = query.order_by(Expense.financial_year.asc(), Expense.expense_no.asc())
			else:
				all_expenses = or_(my_expenses, non_drafts)
				if is_approver:
					query = query.filter(all_expenses)
				elif is_icd_edit:
					query = query.filter(all_expenses, or_(
						and_(Expense.status.in_((STATUS_CONFIRMED, STATUS_DRAFT)), Expense.created_by == user_id),
						Expense.status.in_(allowed_statuses_to_icd)))
				else:
					query = query.filter(Expense.created_by == user_id, or_(all_drafts, non_drafts))
				query = query.order_by(Expense.financial_year.asc(), Expense.expense_no.asc())
			expenses = query.all()
			logger.info('Fetching %s expenses' % len(expenses))
			return expenses
		except:
			raise

	def getExpenseParticular(self, enterprise_id=None, expense_id=None, item_no=None):
		"""

		:param enterprise_id:
		:param expense_id:
		:param item_no:
		:return:
		"""
		try:
			return self.db_session.query(ExpenseParticular).filter(
				ExpenseParticular.item_no == item_no, ExpenseParticular.expense_id == expense_id,
				ExpenseParticular.enterprise_id == enterprise_id).first()
		except:
			raise

	@staticmethod
	def getMaxItemNo(enterprise_id=None, expense_id=None):
		"""

		:param enterprise_id:
		:param expense_id:
		:return:
		"""
		try:
			if not expense_id or not enterprise_id:
				return 0
			max_item_no_result = dao.executeQuery(
				"SELECT Max(item_no) FROM expense_particulars where enterprise_id='%s' AND expense_id='%s'" % (
					enterprise_id, expense_id))
			return max_item_no_result[0][0] if max_item_no_result[0][0] and len(max_item_no_result) > 0 else 0
		except:
			raise

	def getAssociatedVoucher(self, enterprise_id=None, expense_code=None):
		"""

		:return: Voucher object that is linked to this expense
		"""
		return self.db_session.query(Voucher).filter(
			Voucher.enterprise_id == enterprise_id, Voucher.narration.like(expense_code + ":%")).first()


class ExpenseService:
	"""

	"""

	def __init__(self):
		"""

		Initiate service to process all expense oriented events initiate this class with proper enterprise_id and user_id
		"""
		self.expense_dao = ExpenseDAO()

	def getExpenseVO(self, enterprise_id=None, user_id=None, expense_id=None):
		"""

		:param enterprise_id:
		:param user_id:
		:param expense_id:
		:return:
		"""
		try:
			expense = self.expense_dao.getExpense(enterprise_id=enterprise_id, expense_id=expense_id)
			if not expense:
				expense = Expense(enterprise_id=enterprise_id, created_by=user_id, status=STATUS_DRAFT)
			return ExpenseVO(enterprise_id=enterprise_id, user_id=user_id, expense=expense)
		except:
			raise

	def getExpenseDocument(self, enterprise_id=None, expense_id=None, item_no=None):
		"""

		:param enterprise_id:
		:param expense_id:
		:param item_no:
		:return: filename, data
		"""
		try:
			particular = self.expense_dao.getExpenseParticular(
				enterprise_id=enterprise_id, expense_id=expense_id, item_no=item_no)
			return particular.document, FTPUtil().download(particular.document)
		except:
			raise

	@staticmethod
	def extractExpenseParticularsData(particulars=()):
		"""

		:param particulars:
		:return: ExpenseParticular as Map {'key':value, ...}
		"""
		try:
			particulars_list = []
			for particular in particulars:
				particular_dict = copyDictToDict(
					source=particular.__dict__, exclude_keys=EXPENSE_PARTICULAR_EXCLUDE_FIELD_LIST)
				particular_dict['expense_head_ledger'] = {
					'id': particular.expense_head_ledger.id, 'name': particular.expense_head_ledger.name}
				particular_dict.update({
					'spent_on': particular.spent_on.strftime("%Y-%m-%d"), 'amount': float(particular.amount),
					'approver_debit': float(particular.approver_debit), 'audit_debit': float(particular.audit_debit)})

				if particular.remarks:
					particular_dict['remarks_list'] = particular.remarks
				else:
					particular_dict['remarks_list'] = []
				particular_dict['remarks'] = ""
				if particular_dict['document']:
					particular_dict['document'] = None if particular_dict['document'] == "" else json.loads(particular_dict['document'])
				particulars_list.append(particular_dict)

			return particulars_list
		except:
			raise

	def extractExpenseData(self, expense=None, exclude_particulars=True):
		"""

		:param expense:
		:param exclude_particulars:
		:return: Expense as Map {'key':value, ...}
		"""
		try:
			expense_dict = copyDictToDict(source=expense.__dict__, exclude_keys=EXPENSE_EXCLUDE_FIELD_LIST)
			expense_dict.update({
				'expense_id': expense.id, 'code': expense.getCode(), 'status_code': STATUS_DISPLAY[expense.status],
				'created_on': expense.created_on.strftime("%Y-%m-%d %H:%M:%S")})
			if expense.super_modified_on:
				expense_dict['super_modified_on'] = expense.super_modified_on.strftime("%Y-%m-%d %H:%M:%S")
			if expense.confirmed_on:
				expense_dict['confirmed_on'] = expense.confirmed_on.strftime("%Y-%m-%d %H:%M:%S")

			if expense.remarks:
				expense_dict['remarks_list'] = expense.remarks
			else:
				expense_dict['remarks_list'] = []
			expense_dict['remarks'] = ""

			claimed_amount, approved_amount = expense.getTotalExpense()
			expense_dict['claimed_amount'] = float(claimed_amount)
			expense_dict['approved_amount'] = float(approved_amount)
			expense_dict['claim_head_ledger'] = {
				'id': expense.claim_head_ledger.id, 'name': expense.claim_head_ledger.name}
			expense_dict['created_user'] = helper.extractUserSimpleData(expense.created_user)

			if expense.status >= STATUS_APPROVED:
				expense_dict['approved_user'] = helper.extractUserSimpleData(expense.approved_user)
				expense_dict['approved_by'] = expense.approved_by
				expense_dict['approved_on'] = expense.approved_on.strftime("%Y-%m-%d %H:%M:%S")

			if expense.status >= STATUS_ICD_CHECKED:
				expense_dict['checked_user'] = helper.extractUserSimpleData(expense.checked_user)
				expense_dict['checked_by'] = expense.checked_by
				expense_dict['checked_on'] = expense.checked_on.strftime("%Y-%m-%d %H:%M:%S")

			if expense.status >= STATUS_ICD_VERIFIED:
				expense_dict['verified_user'] = helper.extractUserSimpleData(expense.verified_user)
				expense_dict['verified_by'] = expense.verified_by
				expense_dict['verified_on'] = expense.verified_on.strftime("%Y-%m-%d %H:%M:%S")

			if not exclude_particulars:
				expense_dict['particulars'] = self.extractExpenseParticularsData(particulars=expense.particulars)
				expense_dict['tags'] = []
				for tag in expense.expense_tags:
					expense_dict['tags'].append(tag.tag.tag)

			return expense_dict
		except:
			raise

	def listExpenseData(
			self, enterprise_id=None, user_id=None, status=None, since=datetime.now() + relativedelta(days=-29),
			till=datetime.now(), my_expense_flag=None):
		"""

		:param enterprise_id:
		:param user_id:
		:param status:
		:param since:
		:param till:
		:param my_expense_flag: Either one of (None, True, False)
		:return: Expenses as List of Map [{'key':value, ...},...]
		"""
		try:

			expenses = self.expense_dao.listExpenseObjects(
				enterprise_id=enterprise_id, user_id=user_id, since=since, till=till,
				my_expense_flag=my_expense_flag, status=status)
			expenses_data = []
			for expense in expenses:
				expenses_data.append(self.extractExpenseData(expense=expense))
			logger.info('Getting expenses list of size %s' % len(expenses_data))
			return expenses_data
		except:
			raise

	def listExpenseDataGroup(
			self, enterprise_id=None, user_id=None, since=datetime.now() + relativedelta(days=-29),
			till=datetime.now()):
		"""

		:param enterprise_id:
		:param user_id:
		:param since:
		:param till:
		:return: Expenses as group of List of Map {'draft': [{'key':value, ...},...], }
		"""

		try:
			expenses = self.expense_dao.listExpenseObjects(
				enterprise_id=enterprise_id, user_id=user_id, since=since, till=till, status=None)

			expenses_data = {
				STATUS_DISPLAY[STATUS_DRAFT]: [], STATUS_DISPLAY[STATUS_CONFIRMED]: [],
				STATUS_DISPLAY[STATUS_APPROVED]: [], STATUS_DISPLAY[STATUS_ICD_CHECKED]: [],
				STATUS_DISPLAY[STATUS_ICD_VERIFIED]: []}
			for expense in expenses:
				status = STATUS_DISPLAY[expense.status]
				expenses_data[status].append(self.extractExpenseData(expense=expense))
			return expenses_data
		except:
			raise

	def __getExpenseParticularsFromJson(
			self, enterprise_id=None, expense=None, user=None, particulars_dict=None, is_super_edit=False):
		"""
		:return:
		"""
		try:
			particulars = []
			logger.info("Converting json to model with %s particulars" % len(particulars_dict))
			last_item_no = self.expense_dao.getMaxItemNo(enterprise_id=enterprise_id, expense_id=expense.id)
			for particular_dict in particulars_dict:
				particular = None
				if 'item_no' in particular_dict:
					particular = self.expense_dao.getExpenseParticular(
						enterprise_id=enterprise_id, expense_id=expense.id, item_no=particular_dict['item_no'])

				if 'spent_on' not in particular_dict:
					raise Exception('spent_on should be date string with format yyyy-mm-dd')

				if is_super_edit is True or expense.status in (STATUS_DRAFT, STATUS_CONFIRMED):
					if not particular:
						particular = ExpenseParticular()
						last_item_no = last_item_no + 1
						particular.item_no = last_item_no
						particular_dict['item_no'] = particular.item_no
						particular.audit_debit = particular.approver_debit = 0
						particular.expense = expense
						particular.enterprise_id = enterprise_id
					particular.document = str(particular_dict['document']) if 'document' in particular_dict else None
					particular.bill_available = particular_dict[
						'bill_available'] if 'bill_available' in particular_dict else False
					if particular.bill_available is False:
						particular.bill_available = 'document' in particular_dict and len(
							particular_dict['document']) > 0

				if is_super_edit is True or expense.status in (STATUS_DRAFT, STATUS_CONFIRMED):
					spent_on = particular_dict['spent_on']
					particular.spent_on = datetime.strptime(spent_on, "%Y-%m-%d")
					particular.expense_head_ledger_id = particular_dict['expense_head_ledger_id']
					particular.description = particular_dict['description']
					particular.amount = particular_dict['amount'] if 'amount' in particular_dict else 0

				if is_super_edit is True or expense.status == STATUS_APPROVED:
					particular.approver_debit = particular_dict[
						'approver_debit'] if 'approver_debit' in particular_dict else 0
					particular.audit_debit = particular.approver_debit

				if is_super_edit is True or expense.status in (STATUS_ICD_CHECKED, STATUS_ICD_VERIFIED):
					particular.audit_debit = particular_dict[
						'audit_debit'] if 'audit_debit' in particular_dict else 0
				if 'remarks' in particular_dict:
					particular.updateRemarks(remarks=particular_dict['remarks'], user=user)

				if not particular.expense_head_ledger_id >= 0:
					raise Exception('expense_head_ledger_id should be valid')

				particulars.append(particular)
				expense.particulars = particulars
			return particulars
		except:
			raise

	def __updateExpenseFromJSON(
			self, expense=None, enterprise_id=None, user=None, expense_dict=None, is_super_edit=False ):
		"""
		:return:
		"""

		try:
			if 'remarks' in expense_dict:
				expense.updateRemarks(remarks=expense_dict['remarks'], user=user)
			if is_super_edit is False and (not expense.status or int(expense_dict['status']) > expense.status):
					expense.status = int(expense_dict['status'])
			if is_super_edit is True:
				expense.group_description = expense_dict['group_description']
				expense.claim_head_ledger_id = expense_dict['claim_head_ledger_id']
				expense.super_modified_by = user.id
				expense.super_modified_on = datetime.now()
			elif expense.status in (STATUS_DRAFT, STATUS_CONFIRMED):
				if expense.created_by is None or expense.created_on is None:
					expense.created_by = user.id
					expense.created_on = datetime.today()
				if expense.status == STATUS_CONFIRMED:
					expense.confirmed_on = datetime.today()
					enterprise = expense.enterprise
					if not enterprise:
						enterprise = self.expense_dao.db_session.query(Enterprise).filter(
							Enterprise.id == enterprise_id).first()
					expense.financial_year = getFinancialYear(
						for_date=expense.confirmed_on, fy_start_day=enterprise.fy_start_day)
					if expense.expense_no == 0:
						result = dao.executeQuery(
							"SELECT IFNULL(Max(expense_no), 0) FROM expenses where enterprise_id='%s' "
							"and financial_year='%s'" % (enterprise_id, expense.financial_year))
						expense.expense_no = result[0][0] + 1
				expense.group_description = expense_dict['group_description']
				expense.claim_head_ledger_id = expense_dict['claim_head_ledger_id']
			elif expense.status == STATUS_APPROVED:
				expense.approved_by = user.id
				expense.approved_on = datetime.today()
			elif expense.status == STATUS_ICD_CHECKED:
				expense.checked_by = user.id
				expense.checked_on = datetime.today()
			elif expense.status == STATUS_ICD_VERIFIED:
				expense.verified_by = user.id
				expense.verified_on = datetime.today()

			self.__getExpenseParticularsFromJson(
				enterprise_id=enterprise_id, expense=expense, user=user, particulars_dict=expense_dict['particulars'],
				is_super_edit=is_super_edit)

			expense.expense_tags = getEntityTagMaps(
				enterprise_id=enterprise_id, tags=expense_dict['tags'], tag_map_class=ExpenseTag,
				db_session=self.expense_dao.db_session)
			return expense
		except:
			raise

	def __uploadDocument(self, expense=None, particulars_dict=None):
		"""

		:param expense:
		:param particulars_dict:
		:return:
		"""
		try:
			self.expense_dao.db_session.begin(subtransactions=True)
			for particular_dict in particulars_dict:
				particular = self.expense_dao.getExpenseParticular(
					enterprise_id=expense.enterprise_id, expense_id=expense.id, item_no=particular_dict['item_no'])
				if 'document' in particular_dict.keys() and particular_dict['document'] and particular_dict['document'] != '' and 'uid' in particular_dict['document']:
					particular_dict['document'] = json.loads(particular_dict['document']) if not isinstance(particular_dict['document'], dict) else particular_dict['document']
					attachment_data = {'uid': particular_dict['document']['uid'], 'name': particular_dict['document']['name'], 'ext': particular_dict['document']['ext']}
					particular.document = json.dumps(attachment_data)
					self.expense_dao.db_session.add(particular)
			self.expense_dao.db_session.commit()
		except:
			# Session rollback not necessary the calling function is doing it
			raise

	def saveExpenseFromVO(self, user_id=None, expense_vo=None):
		"""

		:param user_id:
		:param expense_vo:
		:return:
		"""
		try:
			if expense_vo.is_valid():
				expense_dict = expense_vo.getExpenseJson()
				logger.info("Submitted expense form")
				logger.debug("Submitted expense form. %s" % expense_dict)
				return self.saveExpenseJSON(enterprise_id=expense_vo.enterprise_id, user_id=user_id,
				                            expense_dict=expense_dict)
			return None, False
		except:
			raise

	def saveExpenseJSON(self, enterprise_id=None, user_id=None, expense_dict=None):
		"""

		:param enterprise_id:
		:param user_id:
		:param expense_dict:
		:return:
		"""
		expense = None
		db_session = self.expense_dao.db_session
		enterprise = db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
		try:
			is_scrutiny = enterprise.setting_flags & enterprise_module_settings['scrutiny_flag'] > 0
			icd_auto_gen = enterprise.setting_flags & enterprise_module_settings['icd_auto_gen_voucher'] > 0
			icd_flag = enterprise.setting_flags & enterprise_module_settings['icd_flag'] > 0
			self.expense_dao.db_session.begin(subtransactions=True)
			if not expense_dict:
				raise Exception("Provide valid json with expense data")

			if 'id' in expense_dict:
				expense = self.expense_dao.getExpense(enterprise_id=enterprise_id, expense_id=expense_dict['id'])

			if not expense:
				expense = Expense(enterprise_id=enterprise_id)

			particulars_dict = expense_dict['particulars']
			is_super_edit = False
			if 'super_edit' in expense_dict:
				is_super_edit = bool(int(expense_dict['super_edit']))

			modifying_user = self.expense_dao.db_session.query(User).filter(
				User.id == user_id).first()
			expense = self.__updateExpenseFromJSON(
				expense=expense, enterprise_id=enterprise_id, user=modifying_user, expense_dict=expense_dict,
				is_super_edit=is_super_edit)
			if not icd_flag or icd_auto_gen:
				if expense.status == STATUS_APPROVED:
					expense.checked_by = modifying_user.id
					expense.checked_on = datetime.today()
					expense.verified_by = modifying_user.id
					expense.verified_on = datetime.today()
					expense.status = STATUS_ICD_VERIFIED

			if not expense.claim_head_ledger_id >= 0:
				raise Exception('claim_head_ledger_id should be valid')

			if expense.status >= STATUS_DRAFT and not expense.created_by >= 0:
				raise Exception('created_by missing')

			if expense.status > STATUS_CONFIRMED and not expense.approved_by >= 0:
				raise Exception('approved_by missing')

			if expense.status > STATUS_APPROVED and not expense.checked_by >= 0:
				raise Exception('checked_by missing')

			if expense.status > STATUS_ICD_CHECKED and not expense.verified_by >= 0:
				raise Exception('verified_by missing')
			claimed_amount, approved_amount = expense.getTotalExpense()

			if approved_amount > 0.0:
				message = PUSH_NOTIFICATION['expenses'] % (
					STATUS_DISPLAY[expense.status], modifying_user, approved_amount, expense.getCode())
			else:
				message = PUSH_NOTIFICATION['expense'] % (
					claimed_amount, modifying_user, expense.getCode())

			if expense.status == STATUS_ICD_VERIFIED and is_super_edit is False:
				self.createBillsAndVouchers(expense_id=expense.id, is_scrutiny=is_scrutiny, user_id=user_id, enterprise=enterprise)
				AccountService().notifyPendingVoucherCount(enterprise_id=enterprise_id, sender_id=user_id,code=expense.getCode())

			self.expense_dao.db_session.add(expense)
			self.expense_dao.db_session.commit()
			if expense.status in (STATUS_CONFIRMED, STATUS_APPROVED, STATUS_ICD_CHECKED):
				push_notification(
					enterprise_id=enterprise_id, sender_id=user_id, module_code="EXPENSES",
					message=message,collapse_key="expense",code=expense.getCode(),expense_created_by=expense.created_by)

			# GCS upload so no need this code
			if expense.status in (0, 1):
				self.__uploadDocument(expense=expense, particulars_dict=particulars_dict)
			return expense, True
		except Exception as e:
			self.expense_dao.db_session.rollback()
			logger.exception("Could not save expense %s" % e.message)
			return expense, False

	def createBillsAndVouchers(self, expense_id=None, is_scrutiny=None , user_id = None, enterprise=None):
		"""

		:param enterprise_id:
		:param expense_id:
		:return:
		"""
		try:
			self.expense_dao.db_session.begin(subtransactions=True)
			expense = self.expense_dao.getExpense(enterprise_id=enterprise.id, expense_id=expense_id)
			logger.info("Creating voucher for expense {enterprise_id: %s, expense_id:%s}" % (enterprise.id, expense.id))
			voucher_particulars = []
			item_no = 1
			# Creating voucher particulars for expense_head not for all expense particulars.
			expense_head_voucher_particular_map = {}
			for particular in expense.particulars:
				if particular.getApprovedAmount() > 0:
					if particular.expense_head_ledger_id in expense_head_voucher_particular_map:
						voucher_particular = expense_head_voucher_particular_map[particular.expense_head_ledger_id]
						voucher_particular.amount = voucher_particular.amount + particular.getApprovedAmount()
					else:
						logger.info("Creating voucher particular for {expense_id:%s, item_no: %s}" % (
							expense.id, particular.item_no))
						voucher_particular = VoucherParticulars(
							enterprise_id=enterprise.id, item_no=item_no, is_debit=True,
							ledger_id=particular.expense_head_ledger_id, amount=particular.getApprovedAmount())

						voucher_particulars.append(voucher_particular)
						item_no += 1
						expense_head_voucher_particular_map[particular.expense_head_ledger_id] = voucher_particular
				else:
					logger.info("Skipped voucher particular for {expense_id:%s, item_no: %s}" % (
						expense.id, particular.item_no))

			claimed_amount, approved_amount = expense.getTotalExpense()
			if approved_amount > 0:
				voucher_particular = VoucherParticulars(
					enterprise_id=enterprise.id, item_no=item_no, is_debit=False,
					ledger_id=expense.claim_head_ledger_id, amount=approved_amount)
				voucher_particulars.append(voucher_particular)
			if len(voucher_particulars) > 0:
				voucher = Voucher(
					type_id="1", voucher_date=expense.verified_on,
					narration="%s:%s" % (expense.getCode(), expense.group_description),
					enterprise_id=enterprise.id, created_by=expense.verified_by, financial_year=expense.financial_year)
				if not is_scrutiny:
					AccountService().prepareVoucherApproval(
						voucher_to_be_saved=voucher, enterprise=enterprise, approved_by=user_id)
				voucher.particulars = voucher_particulars
				self.expense_dao.db_session.add(voucher)
				logger.info(
					"Created voucher for expense {enterprise_id: %s, expense_id:%s}" % (enterprise.id, expense.id))
				logger.info(
					"Created voucher for expense_id %s with %s particulars" % (expense.id, len(voucher_particulars)))
				self.expense_dao.db_session.commit()
				VoucherChangelog().queryVoucherInsert(user_id=user_id, enterprise_id=enterprise.id, data=voucher)
			else:
				logger.info("Skipping voucher creation for expense_id %s, due to approved amount is 0" % expense.id)
			return True
		except:
			self.expense_dao.db_session.rollback()
			raise

	def superEditExpenseCode(
			self, enterprise_id=None, user_id=None, expense_id=None, new_financial_year=None,
			new_expense_no=None, new_sub_number=None):
		"""

		:return: [is_success, json_response]
		"""
		self.expense_dao.db_session.begin(subtransactions=True)
		try:
			expense_to_be_modified = self.expense_dao.getExpense(enterprise_id=enterprise_id, expense_id=expense_id)
			old_code = expense_to_be_modified.getCode()
			existing_expense = self.expense_dao.getExpense(
				enterprise_id=enterprise_id, financial_year=new_financial_year,
				expense_no=new_expense_no, sub_number=new_sub_number)
			response = response_code.failure()
			response['code'] = old_code
			if not existing_expense:
				expense_to_be_modified.financial_year = new_financial_year
				expense_to_be_modified.expense_no = new_expense_no
				expense_to_be_modified.sub_number = new_sub_number
				expense_to_be_modified.super_modified_on = datetime.now()
				expense_to_be_modified.super_modified_by = user_id
				self.expense_dao.db_session.add(expense_to_be_modified)
				response = response_code.success()
				response['custom_message'] = "Successfully changed the Expense Code from '%s' to '%s'!" % (
					old_code, expense_to_be_modified.getCode())
				response['code'] = expense_to_be_modified.getCode()
				message = PUSH_NOTIFICATION['expense_code'] % (old_code, expense_to_be_modified.getCode())
				push_notification(
					enterprise_id=enterprise_id, sender_id=user_id, module_code="EXPENSES", message=message,code=expense_to_be_modified.getCode())
			elif existing_expense.id == expense_to_be_modified.id:
				response['custom_message'] = "No changes detected in Expense code to save!"
			else:
				response['custom_message'] = "An Expense with Code '%s' already exists." \
				                             " Please assign a different Code!" % existing_expense.getCode()
			self.expense_dao.db_session.commit()
			return response
		except:
			self.expense_dao.db_session.rollback()
			raise
