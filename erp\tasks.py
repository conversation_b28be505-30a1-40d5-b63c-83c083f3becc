from celery import shared_task
from erp.helper import create_or_update_closing_stock
from erp import logger


@shared_task()
def update_closing_stock(enterprise_id=None, item_id=None, is_sales=None, quantity=None, is_faulty=None,
                         location_id=None):
    logger.info("Update closing stock queue triggered")
    create_or_update_closing_stock(enterprise_id=enterprise_id, item_id=item_id, is_sales=is_sales,
                            quantity=quantity, is_faulty=is_faulty, location_id=location_id)


@shared_task()
def closing_stock_material_wise_queue_creator(item_list=None, is_sales=None, location_id=None):
    for item in item_list:
        update_closing_stock.delay(enterprise_id=item["enterprise_id"], item_id=item["item_id"],
                                   quantity=item["quantity"], is_faulty=item["is_faulty"], is_sales=is_sales,
                                   location_id=location_id)


@shared_task()
def process_inventory_update(new, old, is_sales=None, location_id=None):
    """
    Celery task to process inventory updates by calculating
    quantity differences between `new` and `old` lists.

    Args:
        new (list): List of new inventory data.
        old (list): List of old inventory data.
        is_sales (list): Inventory transaction type.
        location_id (list): Inventory location.

    Returns:
        list: Processed inventory updates.
    """
    # Convert new and old lists to dictionaries for fast lookup
    old_dict = {item["item_id"]: item for item in old}
    new_dict = {item["item_id"]: item for item in new}

    # Extract item IDs from new and old lists
    new_ids = set(new_dict.keys())
    old_ids = set(old_dict.keys())

    # Identify the relationships using set operations
    in_both = new_ids & old_ids  # Items present in both new and old
    only_in_new = new_ids - old_ids  # Items only in new
    only_in_old = old_ids - new_ids  # Items only in old

    op = []

    # Process items present in both new and old
    for item_id in in_both:
        new_item = new_dict[item_id]
        old_item = old_dict[item_id]

        if new_item["quantity"] == old_item["quantity"]:
            # Quantities are the same, use the new quantity
            op.append({
                "item_id": item_id,
                "enterprise_id": new_item["enterprise_id"],
                "is_faulty": new_item["is_faulty"],
                "quantity": new_item["quantity"]
            })
        else:
            # Calculate the quantity difference
            quantity_diff = new_item["quantity"] - old_item["quantity"]
            op.append({
                "item_id": item_id,
                "enterprise_id": new_item["enterprise_id"],
                "is_faulty": new_item["is_faulty"],
                "quantity": quantity_diff
            })

    # Process items only in new
    for item_id in only_in_new:
        op.append(new_dict[item_id])

    # Process items only in old
    for item_id in only_in_old:
        old_item = old_dict[item_id]
        op.append({
            "item_id": item_id,
            "enterprise_id": old_item["enterprise_id"],
            "is_faulty": old_item["is_faulty"],
            "quantity": -old_item["quantity"]  # Negative of old quantity
        })
    closing_stock_material_wise_queue_creator.delay(item_list=op, is_sales=is_sales, location_id=location_id)
