import unittest
import simplejson
import pprint

from erp.accounts.reconciliation_engine import ReconciliationEngine


class ReconciliationTest(unittest.TestCase):
    """


    """

    def test_exact_match(self):

        dict_pr = """
				{
		   "val":6736.************,
		   "items":[
		      {
		         "cess":0,
		         "sgst":513.81,
		         "txval":5709,
		         "cgst":513.81
		      }
		   ],
		   "ctin":"33ANCPM1460F1ZH",
		   "inum":"2807",
		   "dt":"17-12-2020",
		   "typ":"R"
		}
		"""
        dict_2b = """
						{
				   "rsn":"",
				   "val":27358,
				   "items":[
				      {
				         "rt":18,
				         "txval":23184,
				         "igst":0,
				         "sgst":2086.56,
				         "cess":0,
				         "num":1,
				         "cgst":2086.56
				      }
				   ],
				   "rev":"N",
				   "pos":"33",
				   "itcavl":"Y",
				   "ctin":"33ANCPM1460F1ZH",
				   "inum":"2640",
				   "diffprcnt":1,
				   "dt":"05-10-2020",
				   "typ":"R"
				}"""
        response = []
        re = ReconciliationEngine().exact_match(dict_pr=dict_pr, dict_2b=dict_2b)

    def test_reconciliation_engine(self):
        response = []
        pp = pprint.PrettyPrinter(indent=4)
        try:
            with open("2b_returns_json_testcase.json", "r") as file:
                json_2b_response = file.read()
            self.response_data_2b = simplejson.loads(json_2b_response)
            with open("pr_json_testcase.json", "r") as file:
                json_pr_response = file.read()
            self.response_data_pr = simplejson.loads(json_pr_response)
            re = ReconciliationEngine(gst_2b_data=self.response_data_2b, gst_pr_data=self.response_data_pr)
            response = re.engine(tolerance=0, approximation=False)
            print(response)
            pp.pprint(response)
        except Exception as e:
            print("-------------- ERROR ------------------")
            print(e)
