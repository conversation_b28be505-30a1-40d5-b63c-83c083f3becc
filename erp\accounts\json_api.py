"""
"""
import json

from django.http import HttpResponse

from erp.accounts import logger
from erp.accounts.backend import AccountService
from erp.auth import SESSION_KEY, USER_IN_SESSION_KEY, ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import Request<PERSON><PERSON><PERSON>
from erp.helper import constructAccountGroupOptionTree, getAccountGroupIDs, DEFAULT_BILLABLE_ACCOUNT_GROUP
from erp.models import AccountGroup
from util.api_util import response_code, JsonUtil
from erp.dao import executeQuery

__author__ = 'nandha'


def getDashboardEntries(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')

		logger.info('Fetching dashboard data for enterprise %s' % enterprise_id)
		response = response_code.success()
		since, till = JsonUtil.getDateRange(rh=rh)
		response.update(AccountService().getDashboardEntries(enterprise_id=enterprise_id, since=since, till=till))
		logger.debug("Dashboard entries %s " % response)
	except Exception as e:
		logger.exception("Failed fetching dashboard entries. %s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getCashBalance(request):
	"""

	:param request:
	:return:
	"""
	try:
		accounts_service = AccountService()
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		logger.info('Fetching cash balance data for enterprise %s' % enterprise_id)
		response = accounts_service.cashBalanceChartRender(enterprise_id=enterprise_id)
	except Exception as e:
		logger.exception("Failed fetching cash balance for account dashboard. %s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message
	return HttpResponse(json.dumps(json.loads(json.dumps(response))), 'content-type=text/json')


def getCashBalanceList(request):
	"""
	get Cash in hand only ledgers and balance
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	logger.info('Fetching cash balance data for enterprise %s' % enterprise_id)
	list_type = request_handler.getPostData('type')
	try:
		response = response_code.success()
		response['data'] = AccountService().cashBalance(enterprise_id=enterprise_id, type=list_type)
	except Exception as e:
		logger.exception("Failed fetching cash in hand balance for account dashboard. %s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message
	return HttpResponse(json.dumps(json.loads(json.dumps(response))), 'content-type=text/json')


def getSalesRevenueTotal(request):
	"""
	get sales revenue total for displaying on dashboard card
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	logger.info('Fetching cashbalance data for enterprise %s' % enterprise_id)
	since, till = AccountService().getSelectFyDateRange(enterprise_id=enterprise_id)
	logger.info("since date: %s & till date: %s for sales revenue" % (since, till))
	try:
		response = response_code.success()
		response['data'] = float(AccountService().getSalesRevenue(
			enterprise_id=enterprise_id, since=since, till=till))
	except Exception as e:
		logger.exception("Failed fetching cash in hand balance for account dashboard. %s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message
	return HttpResponse(json.dumps(json.loads(json.dumps(response))), 'content-type=text/json')


def getSalesRevenue(request):
	"""

	:param request:
	:return:
	"""
	try:
		accounts_service = AccountService()
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		logger.info('Fetching sales revenue data for enterprise %s' % enterprise_id)
		response = accounts_service.salesRevenueChartRender(enterprise_id=enterprise_id)
	except Exception as e:
		logger.exception("Failed fetching sales revenue for account dashboard. %s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message
	return HttpResponse(json.dumps(json.loads(json.dumps(response))), 'content-type=text/json')


def getReceivableAgingDetails(request):
	"""

	:param request:
	:return:
	"""
	try:
		accounts_service = AccountService()
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		logger.info('Fetching receivables ageing for enterprise %s' % enterprise_id)
		response = accounts_service.getReceivableAging(enterprise_id=enterprise_id, list_unsettled_particulars=True)
	except Exception as e:
		logger.exception("Failed fetching sales revenue for account dashboard. %s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message
	return HttpResponse(json.dumps(json.loads(json.dumps(response))), 'content-type=text/json')


def getPayableAgingDetails(request):
	"""

	:param request:
	:return:
	"""
	try:
		accounts_service = AccountService()
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		logger.info('Fetching payables ageing for enterprise %s' % enterprise_id)
		response = accounts_service.getPayableAging(enterprise_id=enterprise_id, list_unsettled_particulars=True)
	except Exception as e:
		logger.exception("Failed fetching sales revenue for account dashboard. %s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message
	return HttpResponse(json.dumps(json.loads(json.dumps(response))), 'content-type=text/json')


def getTaxLiability(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')

		since, till = JsonUtil.getDateRange(rh=rh)
		logger.info('Tax Liability between %s - %s' % (since, till))
		response = response_code.success()
		response['tax_liability'], response['tax_liable_items'] = AccountService().getTaxLiability(
			enterprise_id=enterprise_id, since=since, till=till)
		logger.debug("Fetching ledger data : %s" % response)
	except Exception as e:
		logger.exception("Failed tax liability search. %s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getAging(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		logger.info('Fetching dashboard data for enterprise %s' % enterprise_id)
		response = response_code.success()
		response.update(AccountService().getAging(enterprise_id=enterprise_id, list_unsettled_particulars=True))
		logger.debug("Aging %s " % response)
	except Exception as e:
		logger.exception("Failed fetching aging. %s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getAllLedgerNames(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')

		logger.info('Fetching dashboard data for enterprise %s' % enterprise_id)
		response = response_code.success()
		response['ledgers'] = AccountService().getLedgerNames(enterprise_id=enterprise_id)
		logger.debug("Ledger Names %s " % response)
	except Exception as e:
		logger.exception("Failed fetching ledger names. %s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def get_ledger_data(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		ledger_id = rh.getPostData('ledger_id')
		if not ledger_id:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')

		enterprise_id = rh.getPostData('enterprise_id')
		since, till = JsonUtil.getDateRange(rh=rh)
		logger.info('Fetching {ledger: %s} and vouchers between %s - %s' % (ledger_id, since, till))
		response = response_code.success()
		response.update(
			AccountService().getLedger(enterprise_id=enterprise_id, since=since, till=till, ledger_id=ledger_id))
		logger.debug("Fetching ledger data : %s" % response)
	except Exception as e:
		logger.exception("Failed fetching ledger data. %s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getLedgerAging(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		ledger_id = rh.getPostData('ledger_id')
		if not ledger_id:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')

		logger.info('Fetching ledger aging for {enterprise:%s, ledger:%s}' % (enterprise_id, ledger_id))
		response = response_code.success()
		response.update(AccountService().getLedgerAging(enterprise_id=enterprise_id, ledger_id=ledger_id))
		# TODO remove this pop lines by correcting exact issue
		response['aging'].pop("advance_particulars")
		response['aging'].pop("billable_particulars")
		logger.debug("Aging %s " % response)
	except Exception as e:
		logger.exception("Failed fetching ledger aging. %s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getAgingByTotalDue(request):
	"""
	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		result = []
		enterprise_id = rh.getData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		is_receivable = json.dumps(rh.getData('is_receivable')) == '"true"'
		if is_receivable:
			gettotaldue_query = """SELECT 
										ledger_name AS Ledger,
										credit_period AS 'Cr. Period',
										SUM(excess + lessthan30 + thirtytosixty + sixtytoninety + greaterninety + total_unsettled_dr + total_unsettled_cr) AS Total_Due,
										lessthan30 AS '< 30 Days',
										thirtytosixty AS '30 - 60 Days',
										sixtytoninety AS '60 - 90 Days',
										greaterninety AS '> 90 Days',
										total_unsettled_cr AS 'Advances',
										total_unsettled_dr AS 'Un-Billed',
										excess AS Excess
									FROM
										(SELECT 
											account_ledgers.id AS ledger_id,
												account_ledgers.name ledger_name,
												IFNULL(receivable_excess.excess, 0) AS excess,
												IFNULL(SUM(CASE
													WHEN
														DATEDIFF(CURDATE(), ledger_bills.bill_date) <= 30
															AND voucher.status = 1
													THEN
														ledger_bills.net_value
													ELSE 0
												END), 0) AS lessthan30,
												IFNULL(SUM(CASE
													WHEN
														DATEDIFF(CURDATE(), ledger_bills.bill_date) >= 31 
														AND DATEDIFF(CURDATE(), ledger_bills.bill_date) <= 60
															AND voucher.status = 1
													THEN
														ledger_bills.net_value
													ELSE 0
												END), 0) AS thirtytosixty,
												IFNULL(SUM(CASE
													WHEN
														DATEDIFF(CURDATE(), ledger_bills.bill_date) >= 61 
														AND DATEDIFF(CURDATE(), ledger_bills.bill_date) <= 90
															AND voucher.status = 1
													THEN
														ledger_bills.net_value
													ELSE 0
												END), 0) AS sixtytoninety,
												IFNULL(SUM(CASE
													WHEN
														DATEDIFF(CURDATE(), ledger_bills.bill_date) >= 91
															AND voucher.status = 1
													THEN
														ledger_bills.net_value
													ELSE 0
												END), 0) AS greaterninety,
												IFNULL(receivable_advance_billable.total_unsettled_dr, 0) AS total_unsettled_dr,
												IFNULL(receivable_advance_billable.total_unsettled_cr * - 1, 0) AS total_unsettled_cr,
												voucher.status,
												IFNULL(party_credit_days.credit_period, 0) AS credit_period
										FROM
											account_groups
										JOIN account_ledgers ON account_groups.enterprise_id = account_ledgers.enterprise_id
											AND account_groups.id = account_ledgers.group_id
											AND account_ledgers.billable = 1
										LEFT JOIN party_credit_days ON party_credit_days.ledger_id = account_ledgers.id
											AND party_credit_days.enterprise_id = account_ledgers.enterprise_id
										LEFT JOIN receivable_advance_billable ON receivable_advance_billable.ledger_id = account_ledgers.id
										LEFT JOIN receivable_excess ON receivable_excess.ledger_id = account_ledgers.id
										LEFT JOIN ledger_bills ON ledger_bills.ledger_id = account_ledgers.id
											AND ledger_bills.is_debit = 1
										LEFT JOIN voucher ON voucher.id = ledger_bills.voucher_id
											AND voucher.status = 1
										WHERE
											account_groups.enterprise_id = %s
												AND account_groups.id IN (23 , 49)
										GROUP BY account_ledgers.id) AS a
									GROUP BY ledger_id
									HAVING Total_Due != '0.00'
									ORDER BY ledger_name""" % enterprise_id
		else:
			gettotaldue_query = """SELECT 
										ledger_name AS Ledger,
										credit_period AS 'Cr. Period',
										SUM(excess + lessthan30 + thirtytosixty + sixtytoninety + greaterninety + total_unsettled_dr + total_unsettled_cr) AS Total_Due,
										lessthan30 AS '< 30 Days',
										thirtytosixty AS '30 - 60 Days',
										sixtytoninety AS '60 - 90 Days',
										greaterninety AS '> 90 Days',
										total_unsettled_dr AS 'Advances',
										total_unsettled_cr AS 'Un-Billed',
										excess AS Excess
									FROM
										(SELECT 
											account_ledgers.id AS ledger_id,
												account_ledgers.name ledger_name,
												IFNULL(payable_excess.excess, 0) AS excess,
												IFNULL(SUM(CASE
													WHEN
														DATEDIFF(CURDATE(), ledger_bills.bill_date) <= 30
															AND voucher.status = 1
													THEN
														ledger_bills.net_value
													ELSE 0
												END), 0) AS lessthan30,
												IFNULL(SUM(CASE
													WHEN
														DATEDIFF(CURDATE(), ledger_bills.bill_date) >= 31 
														AND DATEDIFF(CURDATE(), ledger_bills.bill_date) <= 60
															AND voucher.status = 1
													THEN
														ledger_bills.net_value
													ELSE 0
												END), 0) AS thirtytosixty,
												IFNULL(SUM(CASE
													WHEN
														DATEDIFF(CURDATE(), ledger_bills.bill_date) >= 61 
														AND DATEDIFF(CURDATE(), ledger_bills.bill_date) <= 90
															AND voucher.status = 1
													THEN
														ledger_bills.net_value
													ELSE 0
												END), 0) AS sixtytoninety,
												IFNULL(SUM(CASE
													WHEN
														DATEDIFF(CURDATE(), ledger_bills.bill_date) >= 91
															AND voucher.status = 1
													THEN
														ledger_bills.net_value
													ELSE 0
												END), 0) AS greaterninety,
												IFNULL(payable_advance_billable.total_unsettled_dr * - 1, 0) AS total_unsettled_dr,
												IFNULL(payable_advance_billable.total_unsettled_cr, 0) AS total_unsettled_cr,
												voucher.status,
												IFNULL(party_credit_days.credit_period, 0) AS credit_period
										FROM
											account_groups
										JOIN account_ledgers ON account_groups.enterprise_id = account_ledgers.enterprise_id
											AND account_groups.id = account_ledgers.group_id
											AND account_ledgers.billable = 1
										LEFT JOIN party_credit_days ON party_credit_days.ledger_id = account_ledgers.id
											AND party_credit_days.enterprise_id = account_ledgers.enterprise_id
										LEFT JOIN payable_advance_billable ON payable_advance_billable.ledger_id = account_ledgers.id
										LEFT JOIN payable_excess ON payable_excess.ledger_id = account_ledgers.id
										LEFT JOIN ledger_bills ON ledger_bills.ledger_id = account_ledgers.id
											AND ledger_bills.is_debit = 0
										LEFT JOIN voucher ON voucher.id = ledger_bills.voucher_id
											AND voucher.status = 1
										WHERE
											account_groups.enterprise_id = %s
												AND account_groups.id IN (24 , 41)
										GROUP BY account_ledgers.id) AS a
									GROUP BY ledger_id
									HAVING Total_Due != '0.00'
									ORDER BY ledger_name""" % enterprise_id

		query_data = executeQuery(gettotaldue_query, as_dict=True)
		for rec in query_data:
			row = {
					'Ledger': str(rec['Ledger']),
					'30 - 60 Days': str(rec['30 - 60 Days']),
					'60 - 90 Days': str(rec['60 - 90 Days']),
					'Cr. Period': str(rec['Cr. Period']),
					'Advances': str(rec['Advances']),
					'< 30 Days': str(rec['< 30 Days']),
					'> 90 Days': str(rec['> 90 Days']),
					'Total_Due': str(rec['Total_Due']),
					'Excess': str(rec['Excess']),
					'Un-Billed': str(rec['Un-Billed'])
				}
			result.append(row)
		response = response_code.success()
		response['totaldue'] = result
	except Exception as e:
		logger.exception("Failed fetching ledger aging. %s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getLedgerBills(request):
	"""
	Get ledger, unsettled bills
	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		ledger_id = rh.getPostData('ledger_id')
		receivable_flag = rh.getPostData('is_receivable')
		is_receivable = receivable_flag == 'true' or receivable_flag == "0"
		logger.info("Loading ledger bills for ledger_id %s " % ledger_id)
		if not ledger_id:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')
		account_service = AccountService()
		response = response_code.success()
		response['ledger'] = account_service.getLedgerAging(enterprise_id=enterprise_id, ledger_id=ledger_id)
		response['unsettled'] = account_service.getUnsettledParticulars(
			enterprise_id=enterprise_id, ledger_id=ledger_id, is_receivable=is_receivable)
		response['bills'] = account_service.getLedgerBills(enterprise_id=enterprise_id, ledger_id=ledger_id)
		logger.debug("Aging %s " % response)
		response['custom_message'] = ''
	except Exception as e:
		logger.exception("Failed fetching ledger aging. %s" % e.message)
		response = response_code.internalError()
		response['exception'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def makePaymentToBills(request):
	"""
	Create voucher for payment done for bills submitted
	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		user_id = rh.getPostData('user_id')
		if not user_id:
			user_id = rh.getSessionAttribute(SESSION_KEY)

		payment_type = rh.getPostData('payment_type')
		is_receivable = payment_type == 'RECEIVED'
		payment_info = json.loads(rh.getPostData('payment_info'))
		bills_to_settle = json.loads(rh.getPostData('bills_to_settle'))

		response = response_code.success()
		account_service = AccountService()
		account_service.createPaymentSettlement(
			enterprise_id=enterprise_id, is_receivable=is_receivable, advance_bills=bills_to_settle['advance'])
		voucher = account_service.createPaymentVoucher(
			enterprise_id=enterprise_id, user_id=user_id, payment_dict=payment_info,
			paid_bills=bills_to_settle['paid'], is_receivable=is_receivable)
		response['custom_message'] = ""
		if len(bills_to_settle['advance']) > 0:
			bill_ids = set()
			for bill in bills_to_settle['advance']:
				bill_ids.add(bill['bill_id'])
			response['custom_message'] = "Settled %s bill(s) against advance." % len(bill_ids)
		if voucher:
			response[
				'custom_message'] = "%s Voucher %s has been created and %s bill(s) are settled against payment." % (
				response['custom_message'], voucher.getCode(), len(bills_to_settle['paid']))
	except Exception as e:
		logger.exception("Failed fetching ledger aging. %s" % e.message)
		response = response_code.internalError()
		response['exception'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getAgingLedgers(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		start_days = rh.getPostData('start_days')
		end_days = rh.getPostData('end_days')
		option = rh.getPostData('option')  # ledgers, ledger_bills
		ledger_id = rh.getPostData('ledger_id')
		is_ledger = json.dumps(rh.getPostData('ledger_only')) == '"true"' if rh.getPostData('ledger_only') is not None \
			else False
		if option in ['ledger_bills', 'overdue']:
			is_receivable = json.dumps(rh.getPostData('is_receivable')) == '"true"'
			is_advance = json.dumps(rh.getPostData('is_advance')) == '"true"'
		else:
			is_receivable = bool(int(rh.getPostData('is_receivable')))  # 0, 1
			is_advance = bool(int(rh.getPostData('is_advance')))  # 0, 1
		if not option:
			option = 'ledgers'
		ledger_id = int(ledger_id) if ledger_id and ledger_id.isdigit() else None
		if start_days:
			start_days = int(start_days)
		if end_days:
			end_days = int(end_days)
		if is_receivable is None or is_advance is None:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')
		logger.info('Ledgers of aging {enterprise:%s, aging:%s, is_receivable:%s, option:%s, is_advance:%s}' % (
			enterprise_id, ("%s - %s" % (start_days, end_days)), is_receivable, option, is_advance))
		response = response_code.success()
		if is_ledger:
			response["params"] = {
				'enterprise_id': enterprise_id, 'start_days': start_days, 'end_days': end_days,
				'is_receivable': is_receivable, 'option': option, 'is_advance': is_advance, 'ledger_id': ledger_id}
			response["ledgers"] = AccountService().getAgingLedgersBase(
				enterprise_id=enterprise_id, start_days=start_days, end_days=end_days, is_receivable=is_receivable,
				is_advance=is_advance, with_bills=(option == "ledger_bills"), ledger_id=ledger_id)
		else:
			response["ledgers"] = AccountService().getAgingLedgers(
				enterprise_id=enterprise_id, start_days=start_days, end_days=end_days, is_receivable=is_receivable,
				is_advance=is_advance, with_bills=(option == "ledger_bills"), ledger_id=ledger_id)

		logger.debug("Aging %s " % response)
	except Exception as e:
		logger.exception("Failed fetching ledger of aging. %s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getIncomeAndExpenses(request):
	try:
		logger.info("Getting Income VS Expense data")
		response = response_code.success()
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		response['income_and_expenses'] = AccountService().constructIncomeVsExpensesData(enterprise_id=enterprise_id)

	except Exception as e:
		logger.exception("Failed loading income and expense. %s" % e.message)
		response = response_code.internalError()
		response['custom_message'] = "Failed loading income and expense"
	logger.debug("Result I VS E %s" % response)
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getAgingLedgersByDetail(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		start_days = rh.getPostData('start_days')
		end_days = rh.getPostData('end_days')
		option = rh.getPostData('option')  # ledgers, ledger_bills
		if option in ['total_due', 'overdue', 'due_in_days']:
			is_receivable = json.dumps(rh.getPostData('is_receivable')) == '"true"'
			is_advance = json.dumps(rh.getPostData('is_advance')) == '"true"'
		else:
			is_receivable = bool(int(rh.getPostData('is_receivable')))  # 0, 1
			is_advance = bool(int(rh.getPostData('is_advance')))  # 0, 1
		if not option:
			option = 'ledgers'
		if start_days:
			start_days = int(start_days)
		if end_days:
			end_days = int(end_days)
		if is_receivable is None or is_advance is None:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')
		logger.info('Ledgers of aging {enterprise:%s, aging:%s, is_receivable:%s, option:%s}' % (
			enterprise_id, ("%s - %s" % (start_days, end_days)), is_receivable, option))
		response = response_code.success()
		result = {}
		account_service = AccountService()
		if option == 'total_due':
			_parent_group = [AccountGroup.SUNDRY_DEBTOR_GROUP_NAME, AccountGroup.OTHER_CURRENT_ASSETS_GROUP_NAME] \
				if is_receivable else [AccountGroup.SUNDRY_CREDITOR_GROUP_NAME, AccountGroup.OTHER_CURRENT_LIABILITIES_GROUP_NAME]
			_group_ids = getAccountGroupIDs(enterprise_id=enterprise_id, names=_parent_group)
			_all_ledgers = account_service.accounts_dao.getLedgers(enterprise_id=enterprise_id, group_ids=_group_ids)
			for ledger in _all_ledgers:
				ledger_record = {'ledger': ledger.name, 'ledger_id': ledger.id}
				result[ledger.id] = ledger_record
			response[option] = result
		elif option == 'overdue':
			res = account_service.getAgingLedgers(
				enterprise_id=enterprise_id, is_receivable=is_receivable, with_bills=True)
			response[option] = res
			# for ledger in res:
			# 	if ledger['id'] not in result:
			# 		ledger_record = account_service.getAgeWiseDue(
			# 			enterprise_id=enterprise_id, account_group_names=[ledger['group_name']], ledger_id=ledger['id'],
			# 			is_receivable=is_receivable)
			# 		if ledger_record['overdue'] != 0:
			# 			result[ledger['id']] = ledger
			# response[option] = result
		elif option == 'due_in_days':
			res = account_service.getAgingLedgers(
				enterprise_id=enterprise_id, start_days=start_days, end_days=end_days, is_receivable=is_receivable,
				with_bills=True)
			response[option] = res
			# for ledger in res:
			# 	if ledger['id'] not in result:
			# 		ledger_record = account_service.getAgeWiseDue(
			# 			enterprise_id=enterprise_id, account_group_names=[ledger['group_name']], ledger_id=ledger['id'],
			# 			is_receivable=is_receivable)
			# 		if ledger_record['age5'] > 0:
			# 			result[ledger['id']] = ledger
			# response[option] = result
	except Exception as e:
		logger.exception("Failed fetching ledger of aging. %s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def renderAccountGroupOptionTree(request):
	"""
	Render Account Group Options as JSON indented in Tree form
	
	:param request:
	:return:
	"""
	logger.info("Populating Account Group Option Tree...")
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	data = json.loads(json.dumps(request_handler.getPostData()))
	group_option_tree = constructAccountGroupOptionTree(
		selected_group_id=data["selected_group_id"] if "selected_group_id" in data else None,
		need_add_option=data["need_add_option"] == "true", for_ledger_creation=data["for_ledger_creation"] == "true",
		enterprise_id=enterprise_id, for_subgroup_creation=data["for_subgroup_creation"] == "true")
	logger.debug("\n".join(["%s - %s - %s" % (
		option["value"], ("%s" % option["label"]).replace("&nbsp;", " "),
		option["attrs"]) for option in group_option_tree]))
	return HttpResponse(content=json.dumps(group_option_tree), mimetype='application/json')


def superEditVoucherCode(request):
	"""
	Super user can edit indent code through this api
	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		voucher_id = int(request_handler.getPostData("voucher_id"))

		new_financial_year = request_handler.getPostData("new_financial_year")
		new_voucher_type_id = int(request_handler.getPostData("new_voucher_type_id"))
		new_voucher_no = int(request_handler.getPostData("new_voucher_no"))
		new_sub_number = request_handler.getPostData("new_sub_number")
		if new_sub_number is not None and new_sub_number.strip() == "":
			new_sub_number = None
		response = AccountService().superEditVoucherCode(
			enterprise_id=enterprise_id, user_id=user_id, voucher_id=voucher_id, new_financial_year=new_financial_year,
			new_voucher_type_id=new_voucher_type_id, new_voucher_no=new_voucher_no, new_sub_number=new_sub_number)

	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def checkIsBillable(request):
	"""
	Get the billable flag
	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		account_service = AccountService()
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		group_id = int(request_handler.getPostData("group_id"))
		is_billable = account_service.accounts_dao.getIsBillableAccountGroup(enterprise_id=enterprise_id, group_id=group_id)
		response = response_code.success()
		response["is_billable"] = is_billable
		is_default_billable = False
		if group_id in getAccountGroupIDs(enterprise_id=enterprise_id, names=DEFAULT_BILLABLE_ACCOUNT_GROUP):
			is_default_billable = True
		response["is_default_billable"] = is_default_billable
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')
