"""
"""
import re

from django.template.defaultfilters import register

__author__ = 'ka<PERSON><PERSON><PERSON>'


@register.filter
def getStatus(obj):
	"""

	:param obj:
	:return:
	"""
	return obj.getStatus()


@register.filter
def getCode(obj):
	"""

	:param obj:
	:return:
	"""
	return obj.getCode()


@register.filter
def getOpeningBalance(obj):
	"""

	:param obj:
	:return:
	"""
	return obj.getOpeningBalance()


@register.filter
def getBalance(obj, deep):
	"""

	:param obj:
	:return:
	"""
	return obj.getBalance(deep=deep)


@register.filter
def getInternalCode(obj):
	return obj.getInternalCode()


@register.filter
def to_char(value):
	return chr(96 + value)


@register.filter
def subtract(value, arg):
	return value - arg


@register.filter
def multiply(value, arg):
	return value * arg


@register.filter
def toDOMId(value):
	return re.sub(r'(&nbsp;)*(\W+)', '_', '%s' % value)


@register.filter
def canView(user_vo, module_code):
	try:
		return user_vo.getPermissions(module_code=module_code)[0]
	except Exception as e:
		return False


@register.filter
def canEdit(user_vo, module_code):
	try:
		return user_vo.getPermissions(module_code=module_code)[1] and user_vo.is_enterprise_active
	except Exception as e:
		return False


@register.filter
def canApprove(user_vo, module_code):
	try:
		return user_vo.getPermissions(module_code=module_code)[2] and user_vo.is_enterprise_active
	except Exception as e:
		return False


@register.filter
def getStatusName(obj):
	try:
		return obj.getStatusName()
	except Exception as e:
		return ""

@register.filter
def getItem(iter, key):
	try:
		return iter[key]
	except Exception as e:
		return None

@register.simple_tag
def custom_subtract(value1, value2):
	return float(value1) - float(value2)

@register.simple_tag
def getGrandTotal(value1, value2, value3):
	return float(value1) + (float(value2)-float(value3))

