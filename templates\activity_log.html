<style type="text/css">
	.history-log-part {
		width: 100%;
	    margin: 0;
	    padding: 8px 15px;
	    cursor: pointer;
	    padding-left: 60px;
	}
	.history-log-part:last-child {
		border-bottom: none;
	}
	.history-log-part:hover {
		background: #e8f0fe;
	}
	.history-log-part:hover .history-log-date {
		font-weight: bold;
	}
	.history-log-date {
	   font-style: italic;
	}
	.history-log-username {
		font-size: 15px;
		padding-top: 3px;
		font-weight: bold;
	}

	.history-log-content {
		margin-top: 8px;
	}

	.history-log-content ul {
		padding: 0px 10px 0px 35px;
	}
	.history-log-content li {
		margin-bottom: 10px;
	}

	.history-log-content li:last-child {
		margin-bottom: 0;
	}

	.history-log-part.active .history-log-date,
	.history-log-part.active .history-log-username {
		color: #004195;
	}

	.history-log-part.active .history-log-date {
	    text-align: right;
		display: block;
	}

	.history-log-part .fa-chevron-down:before {
		content: "\f078";
		color: #999;
	}

	.history-log-part.active .fa-chevron-down:before {
		content: "\f077";
		color: #999;
	}

	#change_log_modal .modal-body {
		overflow: auto;
	}


	ul.timeline {
	    list-style-type: none;
	    position: relative;
		padding: 0px;
	}
	ul.timeline:before {
	    content: ' ';
	    background: #d4d9df;
	    display: inline-block;
	    position: absolute;
	    left: 29px;
	    width: 2px;
	    height: 94%;
	    z-index: 400;
        margin-top: 12px;
	}
	ul.timeline > li {
	    
	}
	ul.timeline > li:before {
	    content: ' ';
	    background: white;
	    display: inline-block;
	    position: absolute;
	    border-radius: 50%;
	    border: 3px solid #209be1;
	    left: 23px;
	    width: 14px;
	    height: 14px;
	    z-index: 400;
	    margin-top: 4px;
	}
	ul.timeline > li:last-child:before {
		background: #209be1;
	}
	body {
		overflow-y: scroll;
	}
</style>
<div id="change_log_modal" class="modal" tabindex="-1" role="dialog" aria-labelledby="" aria-hidden="true">
   <div class="modal-dialog">
	    <div class="modal-content">
		    <div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Activity Log</h4>
	      	</div>
      		<div class="modal-body remove-padding">
		        <div id='loadingmessage_changelog_listing_ie' style="text-align:center">
						<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' width="100">
						<br /> Please wait...
			    </div>
        		<ul class="history-log-container timeline">
        			
        		</ul>
      		</div>
      		<div class="modal-footer">
      			<a href="#" class="btn btn-cancel" data-dismiss="modal">Close</a>
      		</div>
    	</div>
  	</div>
</div>

<script type="text/javascript">
	function changeLogActivityInit(){ 
		$("#id-change-log").removeClass("hide");
		$("#btn_change_log").click(function(){
			$("#change_log_modal").modal("show");
			$("#loadingmessage_changelog_listing_ie").show();
			$(".history-log-container").html("");
			var voucher_id = $("#id_voucher-id").val();
			$.ajax({
		        url: '/erp/accounts/voucher/getloglist/',
		        type: "POST",
		        dataType: "json",
		        data: {"voucher_id":voucher_id},
		        success: function (data) {
		        if (data['response_code'] != 400){
		            x = data['data'];
					var row = "";
					for(var i = 0; i < x.length; i++) {
					    var obj = x[i].log;
					    var row = `<li class="history-log-part" id="history-part-${i}" onclick="loadHistoryContent('${data['data'][0].voucher_id}', '${obj.modified_at}', ${i})">
				                        <span class="history-log-username">${obj.username[0]+" "+obj.username[1]}</span>
				                        <div class="history-log-content" style="display: none;"></div>
				                        <span class="history-log-date">${moment(obj.modified_at).format("MMM DD, YYYY hh:mm:ss a")}</span>
				                    </li>`;
				        $("#loadingmessage_changelog_listing_ie").hide();
		                $(".history-log-container").append(row);
					}
				}
				else {
				    var row = `<span style="width: 100%; padding: 15px; text-align:center; display: block;">No Activity Log has been captured.</span>`;
					$("#loadingmessage_changelog_listing_ie").hide();
		            $("#change_log_modal .modal-body").html(row);
				}
		        },
		        error: function ($xhr,textStatus,errorThrown) {
			        console.log("ERROR : ", $xhr);
			        if ($xhr.responseText.indexOf("Your session has expired") != -1){
			            location.reload();
			        }
		        }
			});
		});
		$("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
	}

	function loadHistoryContent(voucher_id, modified_at, i) {
		var currentLogContent = $("#history-part-"+i).find('.history-log-content');
		if(currentLogContent.text() == "") {
			currentLogContent.html(`<img src="/site_media/images/loading_spinner.gif" style="width: 30px;"> Loading, Please wait...`).show();
			currentLogContent.closest(".history-log-part").addClass("active");
			setTimeout(function(){
				$.ajax({
			        url: '/erp/accounts/voucher/getlogdata/',
			        type: "POST",
			        dataType: "json",
			        data: {"voucher_id":voucher_id, "modified_at": modified_at},
			        success: function (data) {
			        if (data['response_code'] != 400){
			        var row = '';
				        $.each( data['data'], function( key, value ) {
						  row += `<li>` + value + `</li>`;
						});
			        currentLogContent.html(`<ul>` + row + `</ul>`);
					currentLogContent.hide();
					currentLogContent.slideDown();
					}else{
					currentLogContent.html(`log data not available`);
					currentLogContent.hide();
					currentLogContent.slideDown();

					}
			        },
			        error: function () {
			        console.log('Changelog listing failed to load');

			        }
				});

			},100);
		}
		else {
			if(currentLogContent.find("img").length > 0) {
				return;
			}
			if(currentLogContent.is(":visible")) {
				currentLogContent.slideUp('fast', function() {
				   currentLogContent.closest(".history-log-part").removeClass("active");
				});
				
			}
			else {
				currentLogContent.slideDown();
				currentLogContent.closest(".history-log-part").addClass("active");
			}
		}
		$("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
	}
</script>