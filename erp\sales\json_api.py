"""
"""
import datetime
import json
import os
from decimal import Decimal

from django.http import HttpResponse
from django.utils import simplejson
from django.utils.encoding import smart_str
from sqlalchemy import func, and_
from sqlalchemy.orm import make_transient

from erp import helper
from erp.auth import USER_IN_SESSION_KEY, SESSION_KEY, ENTERPRISE_IN_SESSION_KEY, ENTERPRISE_ID_SESSION_KEY, PRIMARY_ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.helper import saveAttachment
from erp.masters.backend import MasterDAO
from erp.models import User, Attachment, SEAttachments, OATax, Invoice, Enterprise, MaterialSerialNo, InvoiceMaterial, \
	InvoiceMaterialSerialNo
from erp.sales import logger
from erp.sales.backend import InvoiceService
from erp.sales.document_compiler import InvoicePackingListPDFGenerator
from erp.sales.oa_backend import OrderAcknowledgementService
from erp.sales.se_backend import SalesEstimateService
from erp.sales.views import getInvoice
from erp.stores.backend import StoresService
from settings import GCS_PUBLIC_URL
from util.api_util import response_code, JsonUtil
from util.helper import getFinancialYear, getFormattedDocPath

__author__ = 'nandha'


def getDashboardData(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')

		logger.info('Fetching Sales dashboard data...')
		response = response_code.success()
		response.update(InvoiceService().formDashboardData(enterprise_id=enterprise_id))
	except Exception as e:
		logger.exception("Failed accessing dashboard data. %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getPaymentOADetail(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')

		logger.info('Fetching Sales details OA & Payment completed information...')
		since, till = JsonUtil.getDateRange(rh=rh)
		response = response_code.success()
		response.update(InvoiceService().paymentOAStatus(enterprise_id=enterprise_id, since=since, till=till))
	except Exception as e:
		logger.exception("Failed accessing payment detail. %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def searchInvoice(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	invoice_no = rh.getPostData('invoiceNo')
	customer_id = rh.getPostData('customerId')
	project_code = rh.getPostData('project_code')
	item_id = rh.getPostData('item_id')
	status = rh.getPostData('status')
	finance_year = rh.getPostData('finance_year')
	is_dispatch = rh.getPostData('is_dispatch')
	since, till = JsonUtil.getDateRange(rh)

	try:
		enterprise_id = rh.getPostData('enterprise_id')
		invoice_service = InvoiceService()
		enterprise = invoice_service.invoice_dao.db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
		current_fy = getFinancialYear(for_date=datetime.datetime.now(), fy_start_day=enterprise.fy_start_day)
		if finance_year == "-1":
			finance_year = current_fy
		if item_id != "-1":
			item = MasterDAO().getMaterialByItemId(item_id=item_id, enterprise_id=enterprise_id)
			item_id = item.material_id
		response = response_code.success()
		response['invoice_list'] = invoice_service.searchInvoices(
			enterprise_id=enterprise_id, since=since, till=till, invoice_no=invoice_no, customer_id=customer_id,
			item_id=item_id, project_code=project_code, status=status, finance_year=finance_year,
			is_dispatch=True if is_dispatch == "true" else False)
		logger.info("Finished search with %s results." % len(response['invoice_list']))
	except Exception as e:
		logger.exception("Search failed for key %s in field %s. %s" % (invoice_no, customer_id, e.message))
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getInvoiceFinanceYear(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		response = response_code.success()
		response["financial_years"] = InvoiceService().invoiceFinanceYear(enterprise_id=enterprise_id)
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getInvoiceMaterial(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	invoice_id = rh.getPostData('invoice_id')
	enterprise_id = rh.getPostData('enterprise_id')
	try:
		response = response_code.success()
		response.update(InvoiceService().invoiceMaterial(enterprise_id=enterprise_id, invoice_id=invoice_id))
		logger.info("invoice id %s " % invoice_id)
	except Exception as e:
		logger.exception("Search failed for key %s in field %s. %s" % (invoice_id, enterprise_id, e.message))
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(simplejson.dumps(response), 'content-type=text/json')


def getOaMaterial(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	oa_id = rh.getPostData("oa_id")
	try:
		enterprise_id = rh.getPostData("enterprise_id")
		response = response_code.success()
		response.update(InvoiceService().getOAMaterial(enterprise_id=enterprise_id, oa_id=oa_id))
	except Exception as e:
		logger.exception("search failed for key %s" % oa_id)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(simplejson.dumps(response), 'content-type=text/json')


def getInvoiceMaterialOverDue(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	party_id = rh.getPostData('party_id')
	enterprise_id = rh.getPostData('enterprise_id')
	try:
		response = response_code.success()
		response["outstanding"] = InvoiceService().invoiceMaterialOverDue(
			enterprise_id=enterprise_id, party_id=party_id)
		logger.info("party_id %s " % party_id)
	except Exception as e:
		logger.exception("Search failed for key %s in field %s. %s" % (party_id, enterprise_id, e.message))
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def fetchDraftInvoice(request):
	"""
	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	try:
		enterprise_id = rh.getPostData('enterprise_id')
		response = response_code.success()
		response['invoice_list'] = InvoiceService().draftInvoices(enterprise_id=enterprise_id)
		logger.info("Finished search with %s results." % len(response['invoice_list']))
	except Exception as e:
		logger.exception("Search failed for key %s in field %s." % (enterprise_id, e.message))
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def searchOA(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	oa_no = rh.getPostData('oa_no')
	supplier_id = rh.getPostData('supplier_id')
	project_code = rh.getPostData('project_code')
	item_id = rh.getPostData('item_id')
	status = rh.getPostData('status')
	since, till = JsonUtil.getDateRange(rh)
	try:
		enterprise_id = rh.getPostData('enterprise_id')
		response = response_code.success()
		response.update(InvoiceService().searchOA(
			enterprise_id=enterprise_id, since=since, till=till, oa_no=oa_no, supplier_id=supplier_id,
			item_id=item_id, project_code=project_code, status=status))
	except Exception as e:
		logger.exception("Search failed for key %s in field %s. %s" % (oa_no, supplier_id, e.message))
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getOAFinanceYear(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		response = response_code.success()
		response["financial_years"] = InvoiceService().oaFinanceYear(enterprise_id=enterprise_id)
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def fetchDraftOA(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	try:
		enterprise_id = rh.getPostData('enterprise_id')
		response = response_code.success()
		response.update(InvoiceService().draftOA(enterprise_id=enterprise_id))
	except Exception as e:
		logger.exception("Search failed for key %s in field %s." % (enterprise_id, e.message))
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def downloadInvoiceDocument(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	invoice_id = rh.getPostData('invoice_id')
	document_header = rh.getPostData('document_header')
	invoice_type = rh.getPostData('inv_type')
	document_regenerate = rh.getPostData('document_regenerate')
	try:
		if not document_header or invoice_type == "Issue" or invoice_type == "internal":
			labels = None
		else:
			labels = json.loads(document_header)
		if not invoice_id:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')

		document_type = rh.getPostData('response_data_type')  # data, file
		if not document_type:
			document_type = 'file'
		enterprise_id = rh.getPostData('enterprise_id')
		primary_enterprise_id = rh.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		logger.info("Downloading Invoice Document for {enterprise: %s, id: %s, labels: %s}" % (
			enterprise_id, invoice_id, labels))
		user = rh.getSessionAttribute(USER_IN_SESSION_KEY)
		invoice_service = InvoiceService()
		if not user:
			user = invoice_service.invoice_dao.db_session.query(User).filter(User.id == rh.getPostData("user_id")).first()
		invoice_document, invoice_code = invoice_service.generateInvoiceDocument(
			enterprise_id=enterprise_id, invoice_id=invoice_id, labels=labels, invoice_type=invoice_type,
			document_regenerate=document_regenerate, user=user, primary_enterprise_id=primary_enterprise_id)

		data = invoice_document.document_pdf

		invoice_need_to_load = InvoiceService().invoice_dao.getInvoice(enterprise_id=enterprise_id, invoice_id=invoice_id)
		if invoice_document and '_sa_instance_state' in invoice_document.__dict__:
			make_transient(invoice_document)
		invoice_code = invoice_code.replace("/", "_")
		filename = smart_str("%s(%s).pdf" % (invoice_code, invoice_id))
		if document_type == "file":
			# mimetype is replaced by content_type for django 1.7
			response = HttpResponse(data, mimetype='application/force-download')
			response['Content-Disposition'] = 'attachment; filename=%s' % filename
			return response
		else:
			response = response_code.success()
			response['ext'] = "pdf"
			response['data'] = "data:application/pdf;base64,%s" % data.encode('base64')
			response['url'] = os.path.join(os.path.dirname(__file__), (
				getFormattedDocPath(code=invoice_code, id=invoice_id)).replace("#", "%23"))
			response['remarks'] = invoice_need_to_load.remarks
	except Exception as e:
		logger.exception("Download invoice document failed for id %s. %s" % (invoice_id, e.message))
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getFormName(issue_type="", inv_header_details=None):
	if issue_type == "GST":
		form_name = inv_header_details.gst_label
	elif issue_type == "Service":
		form_name = inv_header_details.service_label
	elif issue_type == "Trading":
		form_name = inv_header_details.trading_label
	elif issue_type == "BoS":
		form_name = inv_header_details.billofsupply_label
	elif issue_type == "DC" or issue_type == "JDC":
		form_name = inv_header_details.dc_label
	else:
		form_name = inv_header_details.others_label
	return form_name


def downloadOADocument(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	oa_id = rh.getPostData('oa_id')
	document_regenerate = rh.getPostData('document_regenerate')
	try:
		if not oa_id:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')
		document_type = rh.getPostData('response_data_type')  # data, file
		if not document_type:
			document_type = 'file'
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		logger.info("Downloading OA Document for {enterprise: %s, id: %s}" % (enterprise_id, oa_id))
		invoice_service = InvoiceService()
		oa_document = invoice_service.generateOADocument(enterprise_id=enterprise_id, oa_id=oa_id,
														 document_regenerate=document_regenerate)
		oa = invoice_service.invoice_dao.getOA(enterprise_id=enterprise_id, oa_id=oa_id)
		oa_code = oa.getCode().replace("/", "_")
		filename = smart_str("%s(%s).pdf" % (oa_code, oa_id))
		if document_type == "file":
			# mimetype is replaced by content_type for django 1.7
			response = HttpResponse(oa_document.document_pdf, mimetype='application/force-download')
			response['Content-Disposition'] = 'attachment; filename=%s' % filename
			return response
		if not oa_document:
			response = response_code.failure()
			return HttpResponse(json.dumps(response), 'content-type=text/json')
		response = response_code.success()
		response['ext'] = "pdf"
		response['data'] = "data:application/pdf;base64,%s" % oa_document.document_pdf.encode('base64')
		response['url'] = os.path.join(os.path.dirname(__file__), (
			getFormattedDocPath(code=oa.getCode(), id=oa_id)).replace("#", "%23"))
		response['filename'] = filename
		response['remarks'] = oa.remarks if oa.remarks else []
	except Exception as e:
		logger.exception("Download OA document failed for id %s. %s" % (oa_id, e.message))
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getOAStatus(request):
	"""

	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		since, till = JsonUtil.getDateRange(rh)
		if rh.getPostData("since") in ("", None):
			since = None
		if rh.getPostData("till") in ("", None):
			till = None
		logger.info('Fetching Sales details OA & Payment completed information...')
		response = response_code.success()
		from erp.sales.oa_backend import OrderAcknowledgementService
		response.update(
			OrderAcknowledgementService().getOADeliveryStatus(since=since, till=till, enterprise_id=enterprise_id))
		logger.info('Success %s ' % response)
	except Exception as e:
		logger.exception("Failed accessing payment detail. %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getLatestInvoiceSerialNo(request):
	"""

	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		invoice_service = InvoiceService()
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		invoice_id = request_handler.getPostData("invoice_id")

		invoice = getInvoice(invoice_id, enterprise_id)
		invoice_fy = getFinancialYear(
			for_date=invoice.issued_on if invoice.issued_on else datetime.datetime.now(),
			fy_start_day=invoice.enterprise.fy_start_day)
		inv_number_format = invoice.enterprise.inv_template_config.template_header_details.inv_number_format

		response = response_code.success()
		if inv_number_format.__contains__("type") and not inv_number_format.__contains__("type:.0"):
			types = (invoice.type,)
		else:
			if invoice.type in ('DC', 'JDC', 'JIN'):
				types = ('DC', 'JDC', 'JIN')
			elif invoice.type == 'Issue':
				types = ('Issue',)
			else:
				types = ('Trading', 'GST', 'Excise', 'BoS', 'Service')

		latest_invoice_no = invoice_service.invoice_dao.db_session.query(func.max(Invoice.invoice_no)).filter(
			Invoice.financial_year == invoice_fy, Invoice.enterprise_id == invoice.enterprise_id,
			Invoice.id != '0', Invoice.type.in_(types)).first()
		response['next_invoice_no'] = (latest_invoice_no[0] + 1) if latest_invoice_no[0] else 1
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def superEditInvoiceCode(request):
	"""
	Super user can edit indent code through this api
	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		invoice_id = request_handler.getPostData("invoice_id")
		new_invoice_code = request_handler.getPostData("new_invoice_code")
		new_invoice_no = request_handler.getPostData("new_invoice_no")
		logger.info("Super Edit: Invoice (%s)" % invoice_id)
		response = InvoiceService().superEditInvoiceCode(
			enterprise_id=enterprise_id, user_id=user_id, invoice_id=invoice_id, new_invoice_code=new_invoice_code,
			new_invoice_no=new_invoice_no)

	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def superEditIssueCode(request):
	"""
	Super user can edit issue code through this api
	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		invoice_id = request_handler.getPostData("invoice_id")

		new_financial_year = request_handler.getPostData("new_financial_year")
		new_invoice_type = request_handler.getPostData("new_invoice_type")
		new_invoice_no = int(request_handler.getPostData("new_invoice_no"))
		new_sub_number = request_handler.getPostData("new_sub_number")
		logger.info("Super Edit: Invoice (%s)" % new_invoice_type)
		response = InvoiceService().superEditIssueCode(
			enterprise_id=enterprise_id, user_id=user_id, invoice_id=invoice_id, new_financial_year=new_financial_year,
			invoice_type=new_invoice_type, new_invoice_no=new_invoice_no, new_sub_number=new_sub_number)

	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def superEditOACode(request):
	"""
	Super user can edit indent code through this api
	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		primary_enterprise_id = request_handler.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		oa_id = request_handler.getPostData("oa_id")

		new_financial_year = request_handler.getPostData("new_financial_year")
		new_oa_type = request_handler.getPostData("new_oa_type")
		new_oa_no = int(request_handler.getPostData("new_oa_no"))
		new_sub_number = request_handler.getPostData("new_sub_number")
		response = InvoiceService().superEditOACode(
			enterprise_id=enterprise_id, user_id=user_id, oa_id=oa_id, new_financial_year=new_financial_year,
			new_oa_type=new_oa_type, new_oa_no=new_oa_no, new_sub_number=new_sub_number, is_primary=primary_enterprise_id == enterprise_id)

	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def superEditSECode(request):
	"""
	Super user can edit sales estimate code through this api
	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		se_id = request_handler.getPostData("se_id")

		new_financial_year = request_handler.getPostData("new_financial_year")
		new_se_type = request_handler.getPostData("new_se_type")
		new_se_no = int(request_handler.getPostData("new_se_no"))
		new_sub_number = request_handler.getPostData("new_sub_number")
		response = InvoiceService().superEditSECode(
			enterprise_id=enterprise_id, user_id=user_id, se_id=se_id, new_financial_year=new_financial_year,
			new_se_type=new_se_type, new_se_no=new_se_no, new_sub_number=new_sub_number)

	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getInvoiceLinkedMessage(request):
	"""

	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		invoice_id = request_handler.getPostData("invoice_id")
		invoice = InvoiceService().invoice_dao.getInvoice(enterprise_id=enterprise_id, invoice_id=invoice_id)
		receipt_code = StoresService().getReceiptCodes(enterprise_id=enterprise_id, invoice_id=invoice_id)
		response = response_code.success()
		if invoice.ledger_bill:
			voucher_codes = [settlement.voucher.getCode() for settlement in invoice.ledger_bill.settlements]
			response['custom_message'] = """Voucher - <b><i>%s</i></b>, Bill <b><i>%s</i></b>  and GRN - <b><i>%s</i></b> are linked with this Invoice. 
					Unfortunately system will not handle the perpetuation of these changes to the linked documents, 
					hence need to be addressed manually.<br/> Do you want to continue?""" % (
				", ".join(voucher_codes), invoice.ledger_bill.bill_no, ", ".join(receipt_code))
			logger.warn("Trying to edit invoice %s that is linked to %s" % (invoice.ledger_bill.bill_no, voucher_codes))
		else:
			response['custom_message'] = ""
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getOALinkedMessage(request):
	"""

	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		oa_id = request_handler.getPostData("oa_id")
		invoice_code = OrderAcknowledgementService().oa_dao.getInvoiceId(enterprise_id=enterprise_id, oa_id=oa_id)
		response = response_code.success()
		if invoice_code:
			if invoice_code.__len__() > 1:
				response['custom_message'] = """Invoices - <b><i>%s</i></b> are linked with this OA. 
					Unfortunately system will not handle the perpetuation of these changes to the linked documents, 
					hence need to be addressed manually.<br/> Do you want to continue?""" % (
				",".join(invoice_code))
			else:
				response['custom_message'] = """Invoices - <b><i>%s</i></b> is linked with this OA. 
					Unfortunately system will not handle the perpetuation of these changes to the linked documents, 
					hence need to be addressed manually.<br/> Do you want to continue?""" % (
				",".join(invoice_code))
			logger.warn("Trying to edit expense %s that is linked to %s" % (oa_id, invoice_code))
		else:
			response['custom_message'] = ""
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
		response['custom_message'] = ""
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getJOLinkedMessage(request):
	"""

	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		po_id = request_handler.getPostData("po_id")
		invoice_codes, grn_codes = InvoiceService().getDocumentLinkedCodes(
			enterprise_id=enterprise_id, po_id=po_id)
		response = response_code.success()
		if invoice_codes:
			if len(grn_codes) > 0:
				grn_string = " and GRN - <b><i>%s</i></b> are" % (", ".join(grn_codes))
			else:
				grn_string = "are" if invoice_codes.__len__() > 1 else "is"
			response['custom_message'] = """ JDC - <b><i>%s</i></b> %s linked with this JO. 
				Unfortunately system will not handle the perpetuation of these changes to the linked documents, 
				hence need to be addressed manually.<br/> Do you want to continue?""" % (
				", ".join(invoice_codes), grn_string)
			logger.warn("Trying to edit expense %s that is linked to %s" % (po_id, invoice_codes))
		else:
			response['custom_message'] = ""
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getDCLinkedMessage(request):
	"""

	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		invoice_id = request_handler.getPostData("invoice_id")
		invoice_codes = InvoiceService().getDCLinkedCodes(enterprise_id=enterprise_id, invoice_id=invoice_id)
		response = response_code.success()
		if invoice_codes:
			invoice_string = "are" if invoice_codes.__len__() > 1 else "is"
			response['custom_message'] = """ Invoice - <b><i>%s</i></b> %s linked with this DC. 
				Unfortunately system will not handle the perpetuation of these changes to the linked documents, 
				hence need to be addressed manually.<br/> Do you want to continue?""" % (
				", ".join(invoice_codes), invoice_string)
		else:
			response['custom_message'] = ""
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getPartyDC(request):
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		party_id = rh.getPostData('party_id')
		if party_id == "" or party_id == "None":
			party_id = None
		invoice_id = rh.getPostData('invoice_id')
		if invoice_id == "" or invoice_id == 'None':
			invoice_id = None
		financial_years = rh.getPostData('financial_years')
		if financial_years:
			financial_years = "'%s'" % financial_years.replace(",", "','")
		response = response_code.success()
		response["dc_numbers"], response["financial_years"] = InvoiceService().getPendingDCByParty(
			enterprise_id=enterprise_id, party_id=party_id, invoice_id=invoice_id, financial_years=financial_years)
	except Exception as e:
		logger.exception('Failed getting dashboard data. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message

	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getPartyFinancialYears(request):
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		party_id = rh.getPostData('party_id')
		if party_id == "" or party_id == "None":
			party_id = None
		response = response_code.success()
		response["financial_years"] = InvoiceService().getPartyFinancialYears(
			enterprise_id=enterprise_id, party_id=party_id)
	except Exception as e:
		logger.exception('Failed getting financial year. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getInvoiceDC(request):
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		invoice_id = rh.getPostData('invoice_id')
		if invoice_id == "" or invoice_id == 'None':
			invoice_id = None
		response = response_code.success()
		response["dc_codes"], response["financial_years"] = InvoiceService().getDCCodeByInvoice(
			enterprise_id=enterprise_id, invoice_id=invoice_id)
	except Exception as e:
		logger.exception('Failed getting dashboard data. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def orderAcknowledgement(request):
	"""
	TODO POST saveOA, PUT updateOA, DELETE deleteOa, GET getOA
	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request=request)
		enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		primary_enterprise_id = rh.getSessionAttribute(PRIMARY_ENTERPRISE_ID_SESSION_KEY)
		user_id = rh.getSessionAttribute(SESSION_KEY)
		oa_service = OrderAcknowledgementService()
		response = response_code.success()
		if rh.isGetRequest():
			oa_id=rh.getPostData("oa_id")
			# TODO get OA as JSON and add to response
		elif rh.isPostRequest():
			oa_dict = json.loads(request.body)
			oa_id = oa_service.saveOaFromJson(enterprise_id=enterprise_id, user_id=user_id, oa_dict=oa_dict)
			oa = oa_service.approveOa(enterprise_id=enterprise_id, oa_id=oa_id, user_id=user_id, primary_enterprise_id=primary_enterprise_id)
			response.update(oa)
	except Exception as e:
		logger.exception('Failed getting dashboard data. %s' % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def downloadSalesEstimateDocument(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	se_id = rh.getPostData('se_id')
	try:
		if not se_id:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')
		document_type = rh.getPostData('response_data_type')  # data, file
		document_regenerate = rh.getPostData('document_regenerate')
		if not document_type:
			document_type = 'file'
		enterprise_id = rh.getPostData('enterprise_id')
		if not enterprise_id:
			enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		logger.info("Downloading Sales Estimate Document for {enterprise: %s, id: %s}" % (enterprise_id, se_id))
		se_service = SalesEstimateService()
		se_document = se_service.generateSalesEstimateDocument(
				enterprise_id=enterprise_id, se_id=se_id, document_regenerate=document_regenerate)
		sales_estimate = se_service.sales_estimate_dao.getSalesEstimate(se_id=se_id)
		# print '*****', se_document, '****'
		se_code = sales_estimate.getCode().replace("/", "_")
		filename = smart_str("%s(%s).pdf" % (se_code, se_id))
		if document_type == "file":
			# mimetype is replaced by content_type for django 1.7
			response = HttpResponse(se_document.document, mimetype='application/force-download')
			response['Content-Disposition'] = 'attachment; filename=%s' % filename
			return response
		if not se_document:
			response = response_code.failure()
			return HttpResponse(json.dumps(response), 'content-type=text/json')
		response = response_code.success()
		response['ext'] = "pdf"
		response['data'] = "data:application/pdf;base64,%s" % se_document.document_pdf.encode('base64')
		response['url'] = os.path.join(os.path.dirname(__file__), (
			getFormattedDocPath(code=sales_estimate.getCode(), id=se_id)).replace("#", "%23"))
		response['filename'] = filename
		response['remarks'] = sales_estimate.remarks if sales_estimate.remarks else []
	except Exception as e:
		logger.exception("Download Sales Estimate document failed for id %s. %s" % (se_id, e.message))
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def uploadAttachmentSE(request):
	"""
	Upload attachment for material
	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		attached_docs = request_handler.getSessionAttribute('se_attachments')
		uploaded_by = request_handler.getSessionAttribute(SESSION_KEY)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		uploaded_on = datetime.datetime.now()
		label = request_handler.getPostData('label')
		se_id = request_handler.getPostData('se_id')
		gcs_key = request_handler.getPostData('gcs_key')
		se_attachments = []
		if attached_docs and attached_docs != []:
			for i in range(0, len(attached_docs)):
				se_attachments.append(attached_docs[i])
		logger.info("SE ID :%s & label: %s" % (se_id, label))
		file_to_be_saved = request.FILES.getlist('file')
		file_ext = file_to_be_saved[0].name.split(".")[-1].lower()
		filename = "%s.%s" % (gcs_key, file_ext)
		attachment_id = saveAttachment(
			enterprise_id=enterprise_id, label=label, file_to_be_saved=filename, ext=file_ext,
			uploaded_by=uploaded_by, uploaded_on=uploaded_on, gcs_key=gcs_key)
		attachments = {'file_ext': file_ext, 'attachment_id': attachment_id, 'gcs_key': gcs_key}
		se_attachments.append(attachments)
		logger.info("Attachment inserted succesfully")
		request_handler.setSessionAttribute(key='se_attachments', value=se_attachments)
		response = response_code.success()
	except Exception as e:
		logger.exception("Import data %s" % e)
		response = response_code.internalError()
	return HttpResponse(
		content=json.dumps(response), mimetype='application/json')


def getAllAttachmentSE(request):
	"""
	list all the attachments for each material
	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		se_service = SalesEstimateService()
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		attached_docs = request_handler.getSessionAttribute('se_attachments')
		se_id = request_handler.getPostData('se_id')
		response = []
		se_attachments = se_service.sales_estimate_dao.db_session.query(SEAttachments).filter(
				SEAttachments.enterprise_id == enterprise_id, SEAttachments.se_id == se_id).all()
		if se_attachments and se_attachments != []:
			for item in se_attachments:
				if item.attachment:
					gcs_url = "{url}/{key}".format(url=GCS_PUBLIC_URL, key=item.attachment.gcs_key)
					result = {
						'se_id': se_id, 'attachment_id': item.attachment.attachment_id, 'enterprise_id': enterprise_id,
						'description': item.attachment.label, 'file': item.attachment.file,
						'ext': item.attachment.file_ext, 'gcs_id': item.attachment.gcs_key, 'file_base64': gcs_url}
					response.append(result)
		if attached_docs and attached_docs != []:
			for attachment in attached_docs:
				attachments = se_service.sales_estimate_dao.db_session.query(Attachment).filter(
					Attachment.attachment_id == attachment['attachment_id'], Attachment.enterprise_id == enterprise_id).first()
				if attachments:
					gcs_url = "{url}/{key}".format(url=GCS_PUBLIC_URL, key=attachments.gcs_key)
					result = {
						'se_id': se_id, 'attachment_id': attachments.attachment_id, 'enterprise_id': enterprise_id,
						'description': attachments.label, 'file': attachments.file, 'ext': attachments.file_ext, 'gcs_id': attachments.gcs_key, 'file_base64': gcs_url}
					response.append(result)
	except Exception as e:
		logger.exception("Import data %s" % e)
		response = response_code.internalError()
	return HttpResponse(
		content=json.dumps(response), mimetype='application/json')


def deleteAttachmentSE(request):
	"""
	Delete attachment material
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	se_service = SalesEstimateService()
	try:
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		se_id = request_handler.getPostData('se_id').encode("utf-8")
		se_id = 0 if se_id == "" else se_id
		attachment_id = request_handler.getPostData('attachment_id').encode("utf-8")
		response = response_code.success()
		se_service.deleteSEAttachmentFromFTP(enterprise_id=enterprise_id, se_id=se_id, attachment_id=attachment_id)
	except Exception as e:
		logger.exception("Delete attachment data %s" % e)
		response = response_code.internalError()
	logger.info("response code: %s" % json.dumps(response))
	return HttpResponse(
		content=json.dumps(response), mimetype='application/json')


def getOATax(request):
	logger.info("The Edit Tax Loaded...")
	request_handler = RequestHandler(request)
	oa_tax_list = []
	oa_service = OrderAcknowledgementService()
	try:
		if request_handler.isPostRequest():
			oa_ids = request_handler.getPostData('oa_ids')
			oa_ids = oa_ids.split(",")
			enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
			for oa_id in oa_ids:
				if oa_id != "":
					tax_li = oa_service.oa_dao.db_session.query(OATax.tax_code).filter(
						OATax.oa_id == oa_id, OATax.enterprise_id == enterprise_id).all()

					for res_tax in tax_li:
						oa_tax_list.append(dict(oa_id=oa_id, oa_tax_code=res_tax.tax_code))
		response = response_code.success()
		response["oa_taxes"] = oa_tax_list
	except Exception as e:
		logger.exception("Tax retrieval fails...")
		response = response_code.internalError()
		response['error'] = '%s' % e.message

	return HttpResponse(json.dumps(response["oa_taxes"]), 'content-type=text/json')


def saveInvoice(request):
	"""
	Validate invoice data and then save in database then return the saved invoice model
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getPostData('enterprise_id')
	user_id = request_handler.getPostData('user_id')
	invoice_data = json.loads(request_handler.getPostData('invoice_data'))
	if not enterprise_id:
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_IN_SESSION_KEY).id
	if not user_id:
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
	try:
		invoice = InvoiceService().saveInvoice(
				enterprise_id=enterprise_id, user_id=user_id, invoice_data=invoice_data, is_device=True)
		response = response_code.success()
		if invoice_data['status'] == 1:
			response['custom_message'] = "Invoice Created Successfully..."
		else:
			response['custom_message'] = "Invoice Draft Created Successfully..."
	except Exception as e:
		response = response_code.internalError()
		response['error'] = '%s' % e.message
		logger.exception('Failed to save invoice...')
		raise e
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getInvoiceforDispatch(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	enterprise_id = rh.getPostData('enterprise_id')
	invoice_code = rh.getPostData('invoice_code')
	invoice_service = InvoiceService()
	invoice_detail = {}
	invoice_items = []
	packing_status = 0
	i = 0
	try:
		invoice_need_to_load = InvoiceService().invoice_dao.getInvoice(enterprise_id=enterprise_id, invoice_code=invoice_code)

		if invoice_need_to_load and invoice_need_to_load.goods_already_supplied != 1 and invoice_need_to_load.status == 1:
			total_invoiced_qty = invoice_service.invoice_dao.db_session.query(
				func.sum(InvoiceMaterial.quantity).label('invoiced_qty')).join(
				Invoice, and_(
					InvoiceMaterial.enterprise_id == Invoice.enterprise_id, InvoiceMaterial.invoice_id == Invoice.id)).filter(
				InvoiceMaterial.invoice_id == invoice_need_to_load.id).all()
			invoice_serial_no_count = invoice_service.invoice_dao.db_session.query(InvoiceMaterialSerialNo).filter(
				InvoiceMaterialSerialNo.invoice_id == invoice_need_to_load.id).count()

			if invoice_serial_no_count:
				if Decimal(invoice_serial_no_count) != 0:
					if Decimal(invoice_serial_no_count) == total_invoiced_qty[0][0]:
						packing_status = invoice_need_to_load.PACKING_STATUS_COMPLETED
					else:
						packing_status = invoice_need_to_load.PACKING_STATUS_PARTIAL
				else:
					packing_status = invoice_need_to_load.PACKING_STATUS_DRAFT

			invoice_detail.update({
				"invoice_id": invoice_need_to_load.id, "invoice_code": invoice_need_to_load.invoice_code,
				"party": {"id": invoice_need_to_load.party_id, "name": invoice_need_to_load.customer.name},
				"project_name": invoice_need_to_load.project.name, "issued_on": str(invoice_need_to_load.issued_on),
				'packing_status': packing_status, "items": "",
				"packing_info": {"packing_slip_no": invoice_need_to_load.packing_slip_no, "description": invoice_need_to_load.packing_description}})
			for item in invoice_need_to_load.items:
				item_quantity = item.quantity
				unit_name = item.item.unit.unit_name
				if item.alternate_unit_id:
					scale_factor = helper.getScaleFactor(
						enterprise_id=item.enterprise_id, item_id=item.item_id,
						alternate_unit_id=item.alternate_unit_id)
					unit_name = helper.getUnitName(enterprise_id=item.enterprise_id, unit_id=item.alternate_unit_id)
					if scale_factor:
						item_quantity = item.quantity / scale_factor

				invoice_items.append({
					"item_id": item.item_id, "item_name": item.item.name, "make_id": item.make_id, "is_faulty": item.is_faulty,
					"qty": str(item_quantity), "unit": unit_name, "serial_numbers": []})

				item_serial_nos = invoice_service.invoice_dao.db_session.query(MaterialSerialNo).join(
					InvoiceMaterialSerialNo, (MaterialSerialNo.serial_number == InvoiceMaterialSerialNo.serial_number)).join(
					InvoiceMaterial, and_(
						InvoiceMaterial.invoice_id == InvoiceMaterialSerialNo.invoice_id,
						MaterialSerialNo.item_id == InvoiceMaterial.item_id, MaterialSerialNo.make_id == InvoiceMaterial.make_id,
						MaterialSerialNo.is_faulty == InvoiceMaterial.is_faulty, )).filter(
					InvoiceMaterial.invoice_id == item.invoice_id, MaterialSerialNo.enterprise_id == enterprise_id,
					MaterialSerialNo.item_id == item.item_id, MaterialSerialNo.make_id == item.make_id,
					MaterialSerialNo.is_faulty == item.is_faulty)

				for s_no in item_serial_nos:
					invoice_items[i]["serial_numbers"].append({'s_no': s_no.serial_number})

				i = i + 1

			invoice_detail["items"] = invoice_items

			response = response_code.success()
			response['invoice_detail'] = invoice_detail
			logger.info("Fetched Invoice details: %s" % response['invoice_detail'])
		else:
			response = response_code.failure()
			response['custom_message'] = "Please enter a valid invoice number"
	except Exception as e:
		logger.exception("Fetch Invoice failed for %s. %s" % (invoice_code, e.message))
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def saveInvoiceSerialNumber(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	enterprise_id = rh.getPostData('enterprise_id')
	invoice_id = rh.getPostData('invoice_id')
	item_id = rh.getPostData('item_id')
	make_id = rh.getPostData('make_id')
	is_faulty = rh.getPostData('is_faulty')
	serial_no = rh.getPostData('serial_no')
	user_id = rh.getPostData('user_id')
	invoice_service = InvoiceService()
	try:
		enterprise_serial_no = invoice_service.invoice_dao.getEnterpriseSerialNo(
			enterprise_id=enterprise_id, serial_no=serial_no)
		if enterprise_serial_no:
			response = response_code.failure()
			response['custom_message'] = "The scanned serial number %s is already profiled for this enterprise." % serial_no
		else:
			serial_numbers = invoice_service.saveEnterpriseSerialNumber(
				enterprise_id=enterprise_id, user_id=user_id, invoice_id=invoice_id, item_id=item_id, make_id=make_id,
				is_faulty=True if is_faulty == "true" else False, serial_no=serial_no)

			invoice_item_serial_count = invoice_service.invoice_dao.getInvoiceItemSerialNoCount(
				enterprise_id=enterprise_id, invoice_id=invoice_id, item_id=item_id, make_id=make_id,
				is_faulty=True if is_faulty == "true" else False)
			response = response_code.success()
			response['custom_message'] = "The scanned serial number %s saved successfully." % serial_no
			response['total_serial_nos'] = invoice_item_serial_count
			logger.info("Saved serial number: %s." % serial_numbers.serial_number)
	except Exception as e:
		logger.exception("Save serial number failed for %s against the invoice %s. %s" % (serial_no, invoice_id, e.message))
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def deleteInvoiceSerialNumber(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	enterprise_id = rh.getPostData('enterprise_id')
	invoice_id = rh.getPostData('invoice_id')
	item_id = rh.getPostData('item_id')
	make_id = rh.getPostData('make_id')
	is_faulty = rh.getPostData('is_faulty')
	serial_no = rh.getPostData('serial_no')
	invoice_service = InvoiceService()
	try:
		invoice_service.deleteEnterpriseSerialNumber(
			enterprise_id=enterprise_id, invoice_id=invoice_id, serial_no=serial_no)
		invoice_item_serial_count = invoice_service.invoice_dao.getInvoiceItemSerialNoCount(
			enterprise_id=enterprise_id, invoice_id=invoice_id, item_id=item_id, make_id=make_id,
			is_faulty=True if is_faulty == "true" else False)
		response = response_code.success()
		response['total_serial_nos'] = invoice_item_serial_count
		response['custom_message'] = "The scanned serial number %s removed successfully." % serial_no
	except Exception as e:
		logger.exception("Delete serial number failed for %s against the invoice %s. %s" % (serial_no, invoice_id, e.message))
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def saveInvoicePackingInfo(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	enterprise_id = rh.getPostData('enterprise_id')
	invoice_id = rh.getPostData('invoice_id')
	packing_slip_no = rh.getPostData('packing_slip_no')
	packing_description = rh.getPostData('packing_description')
	user_id = rh.getPostData('user_id')
	invoice_service = InvoiceService()
	updated_invoice = None
	try:
		updated_invoice = invoice_service.saveInvoicePackingDetails(
			enterprise_id=enterprise_id, user_id=user_id, invoice_id=invoice_id, packing_slip_no=packing_slip_no,
			packing_description=packing_description)

		response = response_code.success()
		response['custom_message'] = "Packing info saved successfully."
	except Exception as e:
		logger.exception("Save packing info failed for the invoice %s . %s" % (updated_invoice.getcode(), e.message))
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def generatePackingSlip(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request)
	enterprise_id = rh.getPostData('enterprise_id')
	invoice_id = rh.getPostData('invoice_id')
	invoice_code = ""
	try:
		source = InvoiceService().invoice_dao.getInvoice(enterprise_id=enterprise_id, invoice_id=invoice_id)
		invoice_code = source.getCode()
		temp_doc_path = getFormattedDocPath(code=source.getCode(), id=invoice_id)
		pdf_generator = InvoicePackingListPDFGenerator(target_file_path=temp_doc_path)
		data = pdf_generator.generatePDF(source=source)
		filename = smart_str("%s(%s).pdf" % (invoice_code, invoice_id))

		response = HttpResponse(data, mimetype='application/force-download')
		response['Content-Disposition'] = 'attachment; filename=%s' % filename
		return response
	except Exception as e:
		logger.exception("Generation of Packing List document failed for invoice %s. %s" % (invoice_code, e.message))
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getPartyList(request):
	request_handler = RequestHandler(request)
	try:
		enterprise_id = request_handler.getData('enterprise_id')
		party_list = helper.populatePartyChoices(enterprise_id=enterprise_id)
		response = response_code.success()
		response["party_list"] = party_list
	except Exception as e:
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')