"""
"""
from __future__ import absolute_import
from erp.celery_config import app as celery_app
import logging
__author__ = 'saravanan'

__all__ = ['celery_app']

logger = logging.getLogger(__name__)

INDENT_COMPLETED = "Completed"
INDENT_PENDING_DUE_TO_PO = "Pending Due to PO"
INDENT_PENDING_DUE_TO_MATERIAL = "Pending Due to Material"
PENDING_RECEIPT_NOTE = ['Purchase Order', 'Job Work', 'Delivery Challan', 'Sales Return']

RECEIPT_EXCLUDE_FIELD_LIST = (
	'receipt_no', 'tax_details', 'tag_details', 'selectedIssueIds', 'grn_materials', 'rec_through',  'remarks')
