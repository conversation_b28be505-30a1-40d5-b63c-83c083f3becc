$(window).load(function(){
    $("#loading").hide();
});
$(document).ready(function() {
  var project = JSON.parse(localStorage.getItem('project'));
  if(project && project.type == 'Secondary'){
      $('.page-title').text('Internal Invoice');
      $('#id_project').val(project.id).trigger('chosen:updated');
      $('#id_project').prop('disabled', true);
      $('.secondaryColumn').hide();
  }
});

function openMailPopup() {
    var type = $("#id_dc_type").val().toLowerCase();
    new Mailer().prepareEmailPopup().getSupplierMailID(id=sessionStorage.invoiceid, type=type).show();
    return false;
}

$(document).ready(function () {
    $(".chosen-select").chosen();
    downloadInvoiceCopy();
    $("#id_search_submit").click(function(){
        $("#loading").show();
    });
    TableHeaderFixedInvoice();
    $("#inv_document_container").html(getPdfLoadingImage());
    InvoiceFieldSpilt();
    pdfDocumentBtnEvent();
});

$(window).load(function(){
	updateFilterText();
});

function updateFilterText() {
	$(".filtered-date b").text($("#reportrange").find("span").text());
	$(".filtered-party b").text($("#id_party_name option:selected").text());
	$(".filtered-project b").text($("#id_project option:selected").text());
	$(".filtered-status b").text($("#id_status option:selected").text());
}

function generate_pdf_ajax(inv_id, inv_type, inv_status, document_header=[], print_without_header=false, document_regenerate=false){
    $("#inv_doc_btn a.btn, #remarks, #download_copy").addClass("hide");
    $("#inv_document_modal").modal("show");
    if(inv_type == "internal"){
        inv_type="Issue"
    }
    sessionStorage.invoiceid = inv_id
    sessionStorage.invoicetype = inv_type
    sessionStorage.invoicestatus = inv_status
    sessionStorage.invoicedocheader = document_header
    sessionStorage.print_without_header = print_without_header
    $.ajax({
        url: "/erp/sales/json/inv_doc/",
        type: "post",
        datatype: "json",
        data: {invoice_id:inv_id, response_data_type: 'data', document_header: JSON.stringify(document_header), inv_type:inv_type, document_regenerate:document_regenerate},
        success: function (response) {
            if(response.remarks != undefined) {
                if(response.remarks.length > 0){
                    $(".remarks_count_link").text(response.remarks.length+" remarks").removeClass('disabled')
                }
                else {
                    $(".remarks_count_link").text("No remarks").addClass('disabled')
                }
                $("#inv_document_remarks").val(JSON.stringify(response.remarks));
            }
            $("#modal_inv_id").val(inv_id);
            $("#modal_inv_type").val(inv_type);
            var row = '<object id="inv_document" class="document_viewer" data="'+response.url+'">'+getPdfErrorMessage()+'</object>';
            if(document_regenerate == true) {
                $("#inv_document_container").html('');
            }
            $("#inv_document_container").html(row);
            $("#display_popup").removeClass('hide');

            if(inv_status == -1) {
                if($("#reject_invoice").hasClass("for_inv_approve")) {
                    $("#approve_invoice, #remarks").removeClass("hide");
                }
                $("#download_copy").removeClass("hide");
            }
            else if(inv_status == 0) {
                if($("#reject_invoice").hasClass("for_inv_approve")) {
                    $("#approve_invoice, #remarks, #reject_invoice").removeClass("hide");
                }
                else if($("#reject_invoice").hasClass("for_inv_edit")){
                    $("#remarks, #reject_invoice").removeClass("hide");
                }
                $("#reject_invoice").text("Discard");
                $("#download_copy").addClass("hide");
            }
            else if(inv_status == 1 ) {
                if($("#reject_invoice").hasClass("for_inv_approve")) {
                    $("#remarks, #reject_invoice, #regenerate_invoice").removeClass("hide");
                    $("#reject_invoice").text("Reject");
                }
                $("#download_copy").removeClass("hide");
            }
            if($("#remarks").hasClass("hide") || $("#remarks").length <= 0) {
                $(".remarks_count_link").css({float: "left"});
            }
            else {
                $(".remarks_count_link").css({float: "right"});
            }
            $("#remarks").val("");
            closeCustomDropdown();
            if(document_header != "" || print_without_header == true) {
                printJS(response.url);
            }
        },
        error: function(){
            $("#inv_document_modal").modal("hide");
        }
    });
    $("#inv_document_modal footer").remove();
    $('#inv_document_modal').on('hidden.bs.modal', function () {
        $(".remarks_count_link").css({float: "right"});
        $("#inv_document").remove();
        $("#inv_document_container").html(getPdfLoadingImage());
    });
}

function updateTableStatus(status, inv_id, inv_type, inv_no){
    var editedRow = $("#invoice_list").find("tr[data-invid='"+inv_id+"']");
    if(status == 1) {
    	editedRow.find("td.td_status").find("a").removeClass('draft cancelled').addClass('approved').text("Approved");
    	editedRow.find(".edit_link_code").text(inv_no);
    }
    if(status == -1) {
    	editedRow.find("td.td_status").find("a").removeClass('approved').addClass('cancelled').text("Cancelled");
    }
    editedRow.find(".inline-icon-document").attr("onclick", `generate_pdf_ajax(${inv_id}, '${inv_type}', ${status})`);
}

function InvoiceFieldSpilt() {
    $(".for_invoice, .for_dc, .for_issue").addClass('hide');
    if($("#id_dc_type").val().toLowerCase() == "sales") {
        $(".for_invoice").removeClass('hide');
    }
    else if($("#id_dc_type").val().toLowerCase() == "dc") {
        $(".for_dc").removeClass('hide');
        $(".item_particulars_chk").css({marginTop: "11px"});
    }
    else if($("#id_dc_type").val().toLowerCase() == "internal") {
        $(".for_issue").removeClass('hide');
    }
    $("#individual_item_description").addClass("hide");
}

function downloadInvoiceCopy(){
    $("#download_copy").click(function(){
        $("#download_copy_list").show();
        checkReceiverSignatureStorage();
    	receiverSignatureCheckInit();
    });
}

function closeCustomDropdown() {
    $("#download_copy_list input[type='checkbox']").prop("checked", false);
    $("#download_copy_list").hide();
}

function generateCustomDropdown(){
    document_header= []
    $(".custom-dropdown-menu .print_option_container").each(function(){
        var docsOption = {}
        $(this).find("input").each(function(){
            if($(this).hasClass("parent-chk")) {
                if($(this).is(":checked")) {
                    docsOption.label = $(this).val();
                }
                else {
                    return false;
                }
            }
            else {
                docsOption.signature = $(this).is(":checked");
            }
        });
        if(Object.keys(docsOption).length != 0){
            document_header.push(docsOption)
        }
    });
    receiverSignatureStorageInit();
    generate_pdf_ajax($("#modal_inv_id").val(), $("#modal_inv_type").val(), $("#modal_invoice_status").val(), document_header, true)
}

var oTable;
var oSettings;
function TableHeaderFixedInvoice(){
	if($("#id_dc_type").val() == "dc" || $("#id_dc_type").val() == "sales") {
		oTable= $('#invoice_list').DataTable({
			fixedHeader: false,
		    "scrollY": Number($(document).height() - 230),
		    "scrollX": true,
		    "pageLength": 50,
			"search": {
				"smart": false
			},
			"columnDefs": [
			    { "searchable": false, "targets": [5,6,8,9,10] }
			  ],
			"columns": [
				null,null,null,
				{ "type": "date" },
				{ "type": "date" },
				null,null,null,null,null,null,null,null,null
				]
		});
		oTable.columns( [5,6,8,9,10] ).visible( false );
		UpdateInvoiceJson();
	}
	else {
		oTable= $('#invoice_list').DataTable({
			fixedHeader: false,
		    "scrollY": Number($(document).height() - 230),
		    "scrollX": true,
		    "pageLength": 50,
			"search": {
				"smart": false
			},
			"columnDefs": [
			    { "searchable": false, "targets": [2,3,11,12,13] }
			  ],
			"columns": [
				null,null,null,
				{ "type": "date" },
				{ "type": "date" },
				null,null,null,null,null,null,null,null,null
				]
		});
		oTable.columns( [2,3,11,12,13] ).visible( false );
		UpdateInvoiceJson();
	}
	oTable.on("draw",function() {
		var keyword = $('#invoice_list_filter > label:eq(0) > input').val();
		$('#invoice_list').unmark();
		$('#invoice_list').mark(keyword,{});
		UpdateInvoiceJson();
		listTableHoverIconsInit('invoice_list');
		appendRemarksList();
	});
	oTable.on('page.dt', function() {
        $('html, body').animate({
		    scrollTop: $(".dataTables_wrapper").offset().top - 15
	    }, 'slow');
	    listTableHoverIconsInit('invoice_list');
	    //appendRemarksList();
	});
	oSettings = oTable.settings();
	listTableHoverIconsInit('invoice_list');
	appendRemarksList();
	$( window ).resize();
}

function UpdateInvoiceJson(){
	setTimeout(function(){
    	InvoiceListjsonObj = [];
    	var rows = oTable.rows( { page : 'current'} ).data();
        for(var i=0;i<rows.length;i++) {
        	var selectedRow = $(rows[i][1]);
        	invoice = {}
	        invoice ["invoiceId"] = selectedRow.attr("data-invoiceId");
	        invoice ["invoiceNumber"] = selectedRow.text().trim();
	        invoice ["invoiceType"] = selectedRow.attr("data-invoiceType");
            InvoiceListjsonObj.push(invoice);
        }
        if($("#id_dc_type").val() == "sales") {
			localStorage.setItem('invoiceListNav', JSON.stringify(InvoiceListjsonObj));
		}
		else if($("#id_dc_type").val() == "dc") {
			localStorage.setItem('dcListNav', JSON.stringify(InvoiceListjsonObj));
		}
		else if($("#id_dc_type").val() == "internal") {
			localStorage.setItem('issueListNav', JSON.stringify(InvoiceListjsonObj));
		}
	},10);
}
function checkIsConsignment(){
    $("#loading").hide();
    $.ajax({
        url: "/erp/sales/invoice/checkconsignment/",
        type: "POST",
        dataType: "json",
        data: {invoice_id:invoice_id},
        success: function (response) {
            if (response.response_code!=400){
                $.each(response, function (i, item) {
                    if(item.is_consignment && (item.cons_qty == "" || item.cons_qty == 0)) {
                        swal({
                            title: "Ooops!!!",
                            text: "No. of Consignments and Total Weight is mandatory & a Compliance for Courier Delivery. Do you want to Deviate?",
                            type: "error",
                            showCancelButton: true,
                            confirmButtonColor: "#ff0000",
                            confirmButtonText: "Deviate",
                            cancelButtonText: "No",
                            closeOnConfirm: true,
                            customClass: "sw-deviate"
                        },
                        function(){
                            $("#loading").show();
                            approveInvoice();
                        });
                    }
                    else {
                        $("#loading").show();
                        approveInvoice();
                    }
                });
            }
        }
    });
}

function pdfDocumentBtnEvent(){
	$("#approve_invoice").click(function () {
		$("#loading").show();
	    invoice_id =$("#modal_inv_id").val();
	    remarks = $("#remarks").val();
	    dc_type = $("#modal_inv_type").val();
	    if (dc_type = 'dc') {
	        checkIsConsignment();
	    }
	    else {
	        approveInvoice();
	    }
	});

	$("#reject_invoice").click(function () {
	    var event_labels = {'internal': 'Issue', 'dc': 'Delivery Challan', 'sales': 'Invoice'};
	    var project = JSON.parse(localStorage.getItem('project'));
        if(project && project.type == 'Secondary'){
            event_labels['sales'] = "Internal Invoice"
        }
	    dc_type = $("#modal_inv_type").val();
	    if($('#remarks').val() != ""){
	    	$("#loading").show();
	        invoice_id = $("#modal_inv_id").val() ;
	        rejection_remarks = $("#remarks").val();
	        date = new Date();
	        $.ajax({
	            url: "/erp/sales/invoice/reject/",
	            type: "POST",
	            dataType: "json",
	            data: {inv_id: invoice_id , remarks: rejection_remarks},
	            success: function (json) {
	            	$("#loading").hide();
	                if (json == '0'){
	                	swal({
	                		title: "Successfully Removed",
	                		text: "Removed the "+ event_labels[dc_type] +" (Draft no - "+ invoice_id +") permanently!",
	                		type: "warning",
		            		allowEscapeKey: false
	                	},
	                	function(){
	                		url_link = '/erp/sales/invoice_list/'
	                		if (dc_type == "internal"){
	                		    url_link = '/erp/stores/issue_list/'
	                		}
	                		else if(dc_type == "dc"){
	                		    url_link = '/erp/stores/dc_list/'
	                		}
	                		window.location.assign(url_link);
	                        ga('send', 'event', event_labels[dc_type], 'Discard', $('#enterprise_label').val(), 1);
	                	});
	                }
	                else if (json == '-1') {
	                    swal("Unable to Reject",event_labels[dc_type] + " cannot be Rejected, as items have been received/invoiced against this!","warning");
	                }
	                else {
	                	swal({
	                		title: "Successfully Rejected",
	                		text: event_labels[dc_type] + " No: " + json + " successfully rejected",
	                		type: "success",
		            		allowEscapeKey: false
	                	},
	                	function(){
	                		$('#invoice_id').val(invoice_id);
	                		generate_pdf_ajax(invoice_id, dc_type, "-1");
	                		updateTableStatus(-1, invoice_id, dc_type, 0);
	                    	//$('#pdfGeneration_' + invoice_id).click();
	                        ga('send', 'event', event_labels[dc_type], 'Reject', $('#enterprise_label').val(), 1);
	                	});
	                }
	            },
	            error: function (xhr, errmsg, err) {
	            	$("#loading").hide();
	                console.log(xhr.status + ": " + xhr.responseText);
	            }
	        });
	        return false;
	    }else{
	        swal({
	            title: "<span style='color: #dd4b39;'>Remarks Required</span>",
	            text: "Please add a comment to reject this "+ event_labels[dc_type] +".",
	            type: "warning"
	        });
	        $('#remarks').focus();
	        return;
	    }
	});

	$("#regenerate_invoice").click(function () {
        if(sessionStorage.invoicetype == "Issue" || sessionStorage.invoicetype == "internal"){
            generate_issue_pdf(sessionStorage.invoiceid,sessionStorage.invoicetype,sessionStorage.invoicestatus,sessionStorage.invoicedocheader,sessionStorage.print_without_header,true);
        }else{
            generate_pdf_ajax(sessionStorage.invoiceid,sessionStorage.invoicetype,sessionStorage.invoicestatus,sessionStorage.invoicedocheader,sessionStorage.print_without_header,true);
        }
    });
}

function approveInvoice() {
        var event_labels = {'internal': 'Issue', 'dc': 'Delivery Challan', 'sales': 'Invoice'};
        date = new Date();
        $.ajax({
            url: "/erp/sales/invoice/approve/",
            type: "POST",
            dataType: "json",
            data: {invoice_id:invoice_id, remarks:remarks},
            success: function (json) {
                $("#loading").hide();
                if(json.response_message == "Success") {
                swal({
                        title: "<span style='color: #44ad6b;'>Approved Successfully</span>",
                        text: json.custom_message,
                        type: "success",
                        allowEscapeKey: false
                    },
                    function(){
                        $('#invoice_id').val(invoice_id);
                        generate_pdf_ajax(invoice_id, dc_type, 1);
                        updateTableStatus(1, invoice_id, dc_type, json.code);
                        //$('#pdfGeneration_' + invoice_id).click();
                    });
                    ga('send', 'event', event_labels[dc_type], 'Approve', $('#enterprise_label').val(), 1);
                }
                else {
                    swal({
                        title: json,
                        text:  json.custom_message,
                        type: "info",
                        allowEscapeKey: false
                    },
                    function(){
                        $('#invoice_id').val(invoice_id);
                        generate_pdf_ajax(invoice_id, dc_type, 0);
                        //$('#pdfGeneration_' + invoice_id).click();
                    });
                }

            },
            error: function (xhr, errmsg, err) {
                $("#loading").hide();
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });
        return false;
}

function appendRemarksList() {
    $('.matchCountdown').not(".json_parsed").each(function() {
        var remarksString = $(this).text();
        $(this).text("-NA-").addClass("json_parsed");
        if (remarksString != "" && remarksString != "None") {
            remarks = [];
            try{
            json = JSON.parse(remarksString);
            if (typeof json !== 'undefined'){
                $.each(json, function( index, value ) {
                    if(value['remarks'] != ""){
                    	remarks.push(value['remarks']);
                    }
                });
                if (typeof remarks !== 'undefined' && remarks.length > 0) {
                    if(remarks[remarks.length-1]) {
	                    $(this).text(remarks[remarks.length-1].replace(/(<|&lt;)BR\s*\/*(>|&gt;)/g,'. '));
	                }
	            }
	     	}
	     	} catch(e) {
	     	    console.log(e);
	     	}
        }
    });
}

function receiverSignatureCheckInit() {
	$(".print_option_container .parent-chk").each(function(){
		if($(this).is(":checked")) {
			$(this).closest(".print_option_container").find(".child-chk").removeAttr("disabled");
		}
		else {
			$(this).closest(".print_option_container").find(".child-chk").prop("checked", false).attr("disabled", "disabled");
		}
	});

	$(".print_option_container .parent-chk").change(function(){
		if($(this).is(":checked")) {
			$(this).closest(".print_option_container").find(".child-chk").removeAttr("disabled");
		}
		else {
			$(this).closest(".print_option_container").find(".child-chk").prop("checked", false).attr("disabled", "disabled");
		}
	});
}

function receiverSignatureStorageInit(){
	document_header= []
    $(".custom-dropdown-menu .print_option_container").each(function(){
        var docsOption = {}
        $(this).find("input").each(function(){
            if($(this).hasClass("parent-chk")) {
                if($(this).is(":checked")) {
                    docsOption.label = $(this).val();
                }
                else {
                    return false;
                }
            }
            else {
                docsOption.signature = $(this).is(":checked");
            }
        });
        if(Object.keys(docsOption).length != 0){
            document_header.push(docsOption)
        }
    });
	var currentUserId = $(".user_profile_login").text().trim();
    var current_enterprise_id = $("#enterprise_id").val();
    var local_print_settings = JSON.parse(localStorage.getItem('invoice_print_settings'));
    var is_new_user = true;
    if(local_print_settings) {
        for (var i = 0; i < local_print_settings.length; i++){
            if(local_print_settings[i].user_id == currentUserId+"-"+current_enterprise_id) {
                local_print_settings[i].invoice_print = document_header;
                is_new_user = false
                break;
            }
        }
    }
    if(is_new_user || !local_print_settings) {
        if(!local_print_settings) {
            local_print_settings = [];
        }
        var printSettings = {}
        printSettings ["user_id"] = currentUserId+"-"+current_enterprise_id;
        printSettings['invoice_print'] = document_header;
        local_print_settings.push(printSettings);
    }
    localStorage.setItem("invoice_print_settings", JSON.stringify(local_print_settings));
}

function checkReceiverSignatureStorage() {
    var currentUserId = $(".user_profile_login").text().trim();
    var current_enterprise_id = $("#enterprise_id").val();
    var local_user_settings = JSON.parse(localStorage.getItem('invoice_print_settings'));
    var parentChk = false;
    var childChk = false;
    if(local_user_settings) {
        for (var i = 0; i < local_user_settings.length; i++){
            if(local_user_settings[i].user_id == currentUserId+"-"+current_enterprise_id) {
        	 	for (var j = 0; j < local_user_settings[i].invoice_print.length; j++){
                    $(".custom-dropdown-menu .print_option_container").find(`input[value='${local_user_settings[i].invoice_print[j].label}']`).prop("checked", true);
                    if(local_user_settings[i].invoice_print[j].signature){
                    	$(".custom-dropdown-menu .print_option_container").find(`input[value='${local_user_settings[i].invoice_print[j].label}']`).closest(".print_option_container").find(".child-chk").prop("checked", true);
                    }
                }
            }
        }
    }
}