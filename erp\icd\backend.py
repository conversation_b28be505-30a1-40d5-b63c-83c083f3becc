import datetime
import json
from decimal import Decimal

import pymysql
from sqlalchemy import func, and_, or_
from sqlalchemy.orm import make_transient

from erp import helper, dao, DEFAULT_MAKE_ID
from erp.accounts import OTHER_DIRECT_EXPENSES_GROUP_ID, PACKING_ACCOUNT_NAME, \
	TRANSPORT_ACCOUNT_NAME, OTHER_INDIRECT_EXPENSES_GROUP_ID, OTHER_CHARGES_ACCOUNT_NAME, ROUND_OFF_ACCOUNT_NAME, \
	SALES_ACCOUNT_GROUP_ID, PURCHASE_ACCOUNT_GROUP_ID
from erp.accounts.backend import AccountService
from erp.accounts.changelog import VoucherChangelog
from erp.admin import enterprise_module_settings
from erp.commons.backend import push_notification
from erp.dao import DataAccessObject
from erp.dao import executeQuery
from erp.forms import NoteForm, ItemTaxFormset
from erp.formsets import NoteItemFormset, NoteTaxFormset, TagFormset
from erp.helper import updateRemarks
from erp.icd import logger, GRN_CAPABLE_TO_ICD
from erp.icd.document_compiler import DebitNoteDocumentGenerator
from erp.masters.backend import createPartyLedgers, createTaxLedgers
from erp.models import CreditDebitNote, NoteItemNonStock, NoteTax, NoteTag, NoteItemNonStockTax, Receipt, \
	User, Ledger, ReceiptMaterial, VoucherParticulars, Voucher, LedgerBill, \
	ReceiptTag, VoucherTag, LedgerBillSettlement, CreditDebitNoteDocument, Enterprise, Project, Party, NoteReceiptMap
from erp.notifications import PUSH_NOTIFICATION
from erp.stores.backend import StoresService
from erp.tags import extractEntityTagMapsFromFormset, generateTagFormset
from settings import HOST, USER, PASSWORD, DBNAME, PORT
from util.api_util import response_code
from util.helper import constructFormInitializer, copyDictToDict, getFinancialYear, writeFile, readFile, \
	getFormattedDocPath

RECEIPT_FORM_PREFIX = 'receipt'
NOTE_FORM_PREFIX = 'note'
NOTE_ITEM_PREFIX = 'note_item'
NOTE_ITEM_TAX_PREFIX = 'note_item_tax_%s'
NOTE_TAX_PREFIX = 'note_tax'
NOTE_TAG_PREFIX = 'tag'

STATUS_APPROVED = 1
STATUS_CHECKED = 2
STATUS_PARTY_ACK_PENDING = 6
RECEIPT_TYPE_NOTE = 'Note'


class NoteVO(object):
	"""
	This Class holds both Note Form and Note Form Set
	"""

	def __init__(
			self,
			note_form=NoteForm(prefix=NOTE_FORM_PREFIX),
			note_item_formset=NoteItemFormset(prefix=NOTE_ITEM_PREFIX),
			note_tax_formset=NoteTaxFormset(prefix=NOTE_TAX_PREFIX),
			note_tag_formset=TagFormset(prefix=NOTE_TAG_PREFIX)):
		self.note_form = note_form
		self.note_item_formset = note_item_formset
		self.note_tax_formset = note_tax_formset
		self.note_tag_formset = note_tag_formset
		self.remarks_list = []

	def is_valid(self):
		logger.info(
			"Valid Header: %s, Valid Details: %s, Valid Taxes: %s" % (
				self.note_form.is_valid(), self.note_item_formset.is_valid(),
				self.note_tax_formset.is_valid()))
		logger.info("Note Form Errors: \n%s\n%s\n%s\n%s" % (
			self.note_form.errors, self.note_item_formset.errors,
			self.note_tax_formset.errors, self.note_tag_formset.errors))
		return self.note_form.is_valid() and self.note_item_formset.is_valid() and self.note_tax_formset.is_valid() and self.note_tag_formset.is_valid()

	def __repr__(self):
		return 'Note VO - Note: %s\nNote Items: %s\nNote Taxes: %s\nNote Tags: %s' % (
			self.note_form, self.note_item_formset, self.note_tax_formset, self.note_tag_formset)


class NoteDAO(DataAccessObject):
	"""
	Class that handles all the Purchase module related DB activities - like fetching, save and deleting persistent objects
	"""

	def getReceipt(self, receipt_no=None):
		note_query = self.db_session.query(Receipt).filter(Receipt.receipt_no == receipt_no)
		return note_query.first()

	def getNote(self, note_id, enterprise_id):
		return self.db_session.query(CreditDebitNote).filter(
			CreditDebitNote.id == note_id, CreditDebitNote.enterprise_id == enterprise_id).first()

	def getNoteItem(self, note_id, description, enterprise_id, po_no, reason_id):
		return self.db_session.query(NoteItemNonStock).filter(
			NoteItemNonStock.note_id == note_id,
			NoteItemNonStock.description == description,
			NoteItemNonStock.reason_id == reason_id,
			NoteItemNonStock.enterprise_id == enterprise_id,
			NoteItemNonStock.po_no == po_no).first()

	def getNoteTax(self, note_id, tax_code, enterprise_id):
		return self.db_session.query(NoteTax).filter(
			NoteTax.note_id == note_id,
			NoteTax.tax_code == tax_code,
			NoteTax.enterprise_id == enterprise_id).first()

	def getNoteNonStockItemTax(self, note_id, description, reason_id, tax_code, enterprise_id):
		return self.db_session.query(NoteItemNonStockTax).filter(
			NoteItemNonStockTax.note_id == note_id,
			NoteItemNonStockTax.description == description,
			NoteItemNonStockTax.reason_id == reason_id,
			NoteItemNonStockTax.tax_code == tax_code,
			NoteItemNonStockTax.enterprise_id == enterprise_id).first()

	def getNoteNo(self, enterprise_id):
		financial_year = getFinancialYear(for_date=datetime.datetime.now())
		note = self.db_session.query(CreditDebitNote.code).filter(
			CreditDebitNote.enterprise_id == enterprise_id, CreditDebitNote.financial_year == financial_year,
			CreditDebitNote.code != 0).order_by(CreditDebitNote.code.desc()).first()
		note_no = 1 if (note is None) else note.code + 1
		return note_no


class NoteService:
	"""

	"""

	def __init__(self):
		self.note_dao = NoteDAO()
		self.audit_service = ICDService()

	def constructNoteVO(self, note=None, enterprise_id=None):
		"""
		Converts the DB persistent entity into a Value Object presentable to the UI layer.
		:param note:
		:param enterprise_id:
		:return:
		"""
		if not note:
			note = CreditDebitNote(enterprise_id=enterprise_id)
		return NoteVO(
			note_form=self._generateNoteForm(note=note, prefix=NOTE_FORM_PREFIX, enterprise_id=enterprise_id),
			note_item_formset=self._genereateNoteItemFormset(
				enterprise_id=enterprise_id, note_items=note.non_stock_items),
			note_tax_formset=self._generateNoteTaxFormset(note.taxes),
			note_tag_formset=generateTagFormset(tags=note.tags, prefix='tag'))

	def getMaxNoteNumber(self, enterprise_id=None, financial_year=None):
		"""

		:param enterprise_id:
		:param financial_year:
		:return:
		"""
		try:
			max_code = self.note_dao.db_session.query(func.max(CreditDebitNote.code).label("max_code")).filter(
				CreditDebitNote.enterprise_id == enterprise_id,
				CreditDebitNote.financial_year == financial_year).first().max_code
			if max_code is None:
				max_code = 0
			return max_code
		except Exception as e:
			logger.exception("Max Note Number fetch failed: %s" % e.message)
			raise

	def saveNote(
			self, note_vo, status=0, financial_year=None, user=None, attachment_json=None, icd_request_acknowledgement=False, enterprise_id=None):
		"""

		:param note_vo:
		:param status:
		:param financial_year:
		:param user:
		:param attachment_json:
		:param icd_request_acknowledgement:
		:param enterprise_id:
		:return:
		"""
		if note_vo.is_valid():
			note_id = note_vo.note_form.cleaned_data['id']
			self.note_dao.db_session.begin(subtransactions=True)
			try:
				entity = self.note_dao.getNote(note_id=note_id, enterprise_id=enterprise_id)
				if entity:
					note_number = entity.code
				else:
					entity = CreditDebitNote(enterprise_id=enterprise_id)
					note_number = self.getMaxNoteNumber(
						enterprise_id=enterprise_id, financial_year=financial_year) + 1
				logger.info("Note Entity for id: %s - %s" % (note_id, entity))
				note_receipt_to_be_saved = self._copyNoteVOtoEntity(note_vo, entity, financial_year, user, enterprise_id)
				note_receipt_to_be_saved.last_modified_on = datetime.datetime.now()
				note_receipt_to_be_saved.last_modified_by = user.id
				note_receipt_to_be_saved.financial_year = financial_year
				note_receipt_to_be_saved.code = note_number
				note_receipt_to_be_saved.raised_by = '%s' % user.id
				note_receipt_to_be_saved.approved_by = '%s' % user.id
				if attachment_json:
					note_receipt_to_be_saved.attachment = json.loads(attachment_json)
				note_receipt_to_be_saved.invoice_type = 2
				if status == 0:
					note_receipt_to_be_saved.status = STATUS_APPROVED
					note_receipt_to_be_saved.approved_on = datetime.datetime.now()
					note_receipt_to_be_saved.approved_by = user.id
				elif status == 1:
					if not note_receipt_to_be_saved.approved_on:
						note_receipt_to_be_saved.approved_on = datetime.datetime.now()
					note_receipt_to_be_saved.status = STATUS_PARTY_ACK_PENDING if icd_request_acknowledgement else STATUS_CHECKED
					note_receipt_to_be_saved.last_modified_by = user.id
					note_receipt_to_be_saved.approved_by = user.id

				logger.info('Note to be saved: %s' % note_receipt_to_be_saved)
				self.note_dao.db_session.add(note_receipt_to_be_saved)
				self.note_dao.db_session.commit()
				self.note_dao.db_session.refresh(note_receipt_to_be_saved)

				if int(note_receipt_to_be_saved.status) == 2:
					self.audit_service.notifyCheckedReceiptNoteCount(
						enterprise_id=enterprise_id, sender_id=user.id)
				elif int(note_receipt_to_be_saved.status) == 6:
					self.audit_service.notifyPartyAckPendingReceiptNoteCount(
						enterprise_id=enterprise_id, sender_id=user.id)
				else:
					self.audit_service.notifyPendingReceiptNoteCount(
						enterprise_id=enterprise_id, sender_id=user.id)

			except Exception as e:
				self.note_dao.db_session.rollback()
				logger.exception("Saving Note Failed: %s" % e.message)
				raise
		return note_vo

	def _copyNoteVOtoEntity(self, note_vo=None, entity=None, financial_year="", user=None, enterprise_id=None):
		"""
		Method converts VO into persist-able data entity.
		Caution: Don't use this method unless the data is to be persisted immediately, as it might lead to inconsistent
			Note numbers

		:param note_vo:
		:param entity:
		:param financial_year:
		:param user:
		:param enterprise_id:
		:return:
		"""
		try:
			logger.debug("Note Id fetched from Note_VO: %s" % entity.id)
			entity = self.__copyNoteFormToEntity(note_vo.note_form, financial_year, enterprise_id)
			entity.non_stock_items = self.__extractNoteItemsFromFormset(note_vo.note_item_formset, entity)
			logger.debug("The Tax List Constructed: %s" % note_vo.note_tax_formset)
			entity.taxes = self.__extractNoteTaxFormset(note_vo.note_tax_formset, entity)
			entity.tags = extractEntityTagMapsFromFormset(
				tag_formset=note_vo.note_tag_formset, enterprise_id=entity.enterprise_id,
				map_class=NoteTag, db_session=self.note_dao.db_session)
			remarks = str(note_vo.note_form.cleaned_data['remarks'])
			entity.updateRemarks(remarks=remarks, user=user)
			logger.info("Remarks:%s" % entity.remarks)
			return entity
		except Exception as e:
			logger.exception("Error in Copy Note Vo to Entity: %s" % e.message)
		return entity

	@staticmethod
	def _generateNoteForm(note=None, prefix=NOTE_FORM_PREFIX, enterprise_id=None):
		if not note:
			note = CreditDebitNote(enterprise_id=enterprise_id)
		initializer = copyDictToDict(note.__dict__, destination={}, exclude_keys=("remarks",))
		initializer['remarks'] = ''
		initializer['invoice_no'] = note.inv_no
		initializer['invoice_value'] = note.inv_value
		initializer['invoice_date'] = note.inv_date
		initializer['supplier_id'] = note.party_id
		initializer['receipt_code'] = note.receipt_code
		initializer[u'code'] = ''
		if note and note.note_attachment:
			initializer['document_data'] = note.note_attachment.attachment.file
			initializer['document'] = note.note_attachment.attachment.file_ext
		return NoteForm(initial=initializer, prefix=prefix, enterprise_id=enterprise_id)

	def _genereateNoteItemFormset(self, enterprise_id=None, note_items=()):
		"""

		:param enterprise_id:
		:param note_items:
		:return:
		"""
		initializer = []
		note_item_tax_formsets = []
		i = 0
		_unit_choices = helper.populateUnit(enterprise_id=enterprise_id)
		for note_item in note_items:
			form_initializer = constructFormInitializer(note_item)
			form_initializer['_unit_choices'] = _unit_choices
			form_initializer['item_name'] = "%s" % note_item.description
			logger.debug("after resetting the item name: %s" % form_initializer)
			tax_initializer = self.__generateNoteItemTaxesInitial(note_item.taxes)
			note_item_tax_formsets.append(
				ItemTaxFormset(prefix=NOTE_ITEM_TAX_PREFIX % i, initial=tax_initializer))
			initializer.append(form_initializer)
			i += 1
		note_item_formset = NoteItemFormset(initial=initializer, prefix=NOTE_ITEM_PREFIX)
		i = 0
		for form in note_item_formset:
			form.taxes = note_item_tax_formsets[i]
			i += 1
		return note_item_formset

	@staticmethod
	def __generateNoteItemTaxesInitial(note_item_taxes=()):
		tax_formset_initializer = [
			{"tax_code": "", "tax_type": "CGST", "rate": "", "amount": ""},
			{"tax_code": "", "tax_type": "SGST", "rate": "", "amount": ""},
			{"tax_code": "", "tax_type": "IGST", "rate": "", "amount": ""}]
		for material_tax in note_item_taxes:
			if material_tax.tax.type == "CGST":
				tax_formset_initializer[0]["tax_code"] = material_tax.tax_code
				tax_formset_initializer[0]["rate"] = material_tax.tax.net_rate
			elif material_tax.tax.type == "SGST":
				tax_formset_initializer[1]["tax_code"] = material_tax.tax_code
				tax_formset_initializer[1]["rate"] = material_tax.tax.net_rate
			elif material_tax.tax.type == "IGST":
				tax_formset_initializer[2]["tax_code"] = material_tax.tax_code
				tax_formset_initializer[2]["rate"] = material_tax.tax.net_rate
			else:
				tax_formset_initializer.append(constructFormInitializer(material_tax))
		return tax_formset_initializer

	@staticmethod
	def _generateNoteTaxFormset(note_taxes=()):
		initializer = []
		logger.info("Note has %s note_taxes associated" % len(note_taxes))
		for tax in note_taxes:
			from_initializer = constructFormInitializer(tax)
			initializer.append(from_initializer)
		return NoteTaxFormset(initial=initializer, prefix=NOTE_TAX_PREFIX)

	def __copyNoteFormToEntity(self, note_form, financial_year, enterprise_id):
		note = self.note_dao.getNote(note_form.cleaned_data['id'], enterprise_id)
		if note is None:
			note_no = self.note_dao.getNoteNo(enterprise_id)
			note = CreditDebitNote(
				value=note_form.cleaned_data['value'], is_credit=note_form.cleaned_data['is_credit'],
				code=note_no, enterprise_id=enterprise_id,
				financial_year=financial_year, round_off=note_form.cleaned_data['round_off'])
		else:
			note.value = note_form.cleaned_data['value']
			note.is_credit = note_form.cleaned_data['is_credit']
			note.round_off = note_form.cleaned_data['round_off']
		note.inv_no = note_form.cleaned_data['invoice_no']
		note.inv_value = note_form.cleaned_data['invoice_value']
		note.inv_date = note_form.cleaned_data['invoice_date']
		note.party_id = note_form.cleaned_data['supplier_id']
		note.currency_id = note_form.cleaned_data['currency_id']
		note.currency_conversion_rate = note_form.cleaned_data['currency_conversion_rate']
		note.approved_by = note_form.cleaned_data['approved_by']
		note.ledger_bill_id = note_form.cleaned_data['ledger_bill_id']
		note.receipt_code = note_form.cleaned_data['receipt_code']
		note.project_code = note_form.cleaned_data['project_code']
		return note

	def __extractNoteItemsFromFormset(self, note_item_formset, note):
		note_items = []
		for note_item_form in note_item_formset:
			if not note_item_form.cleaned_data['DELETE']:
				note_item = self.note_dao.getNoteItem(
					note_id=note.id, po_no=0, description=note_item_form.cleaned_data['item_name'],
					reason_id=note_item_form.cleaned_data['reason_id'], enterprise_id=note.enterprise_id)
				if not note_item:
					note_item = NoteItemNonStock(
						po_no=0, description=note_item_form.cleaned_data['item_name'],
						reason_id=note_item_form.cleaned_data['reason_id'], enterprise_id=note.enterprise_id)
					note_item.note = note
				note_item.quantity = note_item_form.cleaned_data['quantity']
				note_item.hsn_code = note_item_form.cleaned_data['hsn_code']
				note_item.unit_id = note_item_form.cleaned_data['unit_id']
				note_item.reason_id = note_item_form.cleaned_data['reason_id']
				note_item.rate = note_item_form.cleaned_data['rate']
				note_item.amount = note_item_form.cleaned_data['amount']
				note_item.amount_display = note_item_form.cleaned_data['amount_display']
				note_item.is_credit = note.is_credit
				note_item.enterprise_id = note.enterprise_id
				note_item.taxes = self.__extractNoteItemTaxesFormset(
					note_item_taxes_formset=note_item_form.taxes, note_item=note_item,
					description=note_item_form.cleaned_data['item_name'], reason_id=note_item.reason_id,
					enterprise_id=note.enterprise_id)
				note_items.append(note_item)
		return note_items

	def __extractNoteItemTaxesFormset(self, note_item_taxes_formset, note_item, description, reason_id, enterprise_id):
		note_item_taxes = []
		logger.debug("Receipt Id: %s" % note_item.note_id)
		for note_item_tax_form in note_item_taxes_formset:
			if note_item_tax_form.is_valid() and note_item_tax_form.cleaned_data['tax_code'] != "":
				note_item_tax = self.note_dao.getNoteNonStockItemTax(
					note_id=note_item.note_id, description=description, reason_id=reason_id, enterprise_id=enterprise_id,
					tax_code=note_item_tax_form.cleaned_data['tax_code'])
				note_item_tax = note_item_tax if note_item_tax else NoteItemNonStockTax(
					description=description, enterprise_id=enterprise_id,
					tax_code=note_item_tax_form.cleaned_data['tax_code'])
				note_item_tax.note_item = note_item
				note_item_taxes.append(note_item_tax)
		return note_item_taxes

	def __extractNoteTaxFormset(self, note_tax_formset, note):
		note_tax_list = []
		try:
			for note_tax_form in note_tax_formset:
				if not note_tax_form.cleaned_data['DELETE']:
					note_tax = self.note_dao.getNoteTax(
						note_id=note.id, tax_code=note_tax_form.cleaned_data['tax_code'],
						enterprise_id=note.enterprise_id)
					if not note_tax:
						note_tax = NoteTax(
							tax_code=note_tax_form.cleaned_data['tax_code'], enterprise_id=note.enterprise_id)
						note_tax.note = note
					note_tax_list.append(note_tax)
		except Exception as e:
			logger.exception("Note Tax Extraction from Formset failed: %s" % e.message)
			pass
		return note_tax_list


class AuditDAO(DataAccessObject):
	"""

	"""

	def getReceipt(self, enterprise_id=None, receipt_no=None, financial_year=None, code=None, sub_number=None):
		"""
		enterprise_id and either receipt_no or (financial_year, code, sub_number) are mandatory
		:return:
		"""
		try:
			if receipt_no:
				return self.db_session.query(Receipt).filter(
					Receipt.enterprise_id == enterprise_id, Receipt.receipt_no == receipt_no).first()
			else:
				return self.db_session.query(Receipt).join(Receipt.note_receipt_map).join(NoteReceiptMap.note).filter(
					CreditDebitNote.enterprise_id == enterprise_id,
					CreditDebitNote.financial_year == financial_year,
					CreditDebitNote.code == code, CreditDebitNote.sub_number == sub_number).first()
		except Exception as e:
			logger.exception("Get Receipt failed: %s" % e.message)
			raise

	def getNote(self, enterprise_id=None, note_id=None):
		"""
		enterprise_id and either note_id or (financial_year, code, sub_number) are mandatory
		:return:
		"""
		try:

			return self.db_session.query(CreditDebitNote).filter(
				CreditDebitNote.enterprise_id == enterprise_id,
				CreditDebitNote.id == note_id).first()
		except Exception as e:
			raise

	def getReceiptNoteCount(self, enterprise_id=None, status=None, for_note=False):
		"""

		:param enterprise_id:
		:param status:
		:param for_note:
		:return:
		"""
		try:
			query = self.db_session.query(Receipt).filter(Receipt.enterprise_id == enterprise_id)
			if status is not None:
				query = query.filter(Receipt.status == status)
			if for_note:
				query = query.filter(Receipt.received_against.in_((
					'Purchase Order', 'Job Work', 'Note', 'Delivery Challan', 'Sales Return')))
			return query.count()
		except Exception as e:
			logger.exception("Fetching Receipt Note count Failed: %s" % e.message)
			raise

	def getCheckedReceipts(self, enterprise_id=None, icd_request_acknowledgement=None):
		if icd_request_acknowledgement:
			query = self.db_session.query(Receipt).outerjoin(Receipt.note_receipt_map).outerjoin(NoteReceiptMap.note).filter(
				Receipt.enterprise_id == enterprise_id, or_(Receipt.invoice_type == 2, Receipt.received_against == "Note"),
				or_(
					and_(CreditDebitNote.id.is_(None), Receipt.status == Receipt.STATUS_ICD_CHECKED),
					and_(CreditDebitNote.value > 0, CreditDebitNote.status == Receipt.STATUS_PARTY_ACKNOWLEDGED),
					and_(CreditDebitNote.value == 0, CreditDebitNote.status == Receipt.STATUS_ICD_CHECKED)))
		else:
			query = self.db_session.query(Receipt).filter(
				Receipt.status == Receipt.STATUS_ICD_CHECKED, Receipt.enterprise_id == enterprise_id, or_(
					Receipt.invoice_type == 2, Receipt.received_against == "Note"))
		return query.distinct().all()

	def getPendingAckNote(self, enterprise_id=None):
		"""
		enterprise_id is mandatory
		:return:
		"""
		try:
			return self.db_session.query(func.count(CreditDebitNote.id)).filter(
				CreditDebitNote.enterprise_id == enterprise_id,
				CreditDebitNote.status.in_([Receipt.STATUS_PARTY_ACK_PENDING, Receipt.STATUS_PARTY_REJECTED])).first()
		except Exception as e:
			logger.exception("Fetching Party Pending/Acknowledged Note count Failed: %s" % e.message)
			raise


class ICDService:
	"""

	"""

	def __init__(self):
		self.audit_dao = AuditDAO()

	def deleteCrDrNoteDetails(self, enterprise_id, note_id, include_note=False):
		"""
		:return:
		"""
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
		try:
			cur = conn.cursor()
			cur.execute("DELETE FROM crdr_details_tax WHERE enterprise_id=%s AND crdr_id=%s" % (enterprise_id, note_id))
			cur.execute("DELETE FROM crdr_details WHERE enterprise_id=%s AND crdr_id=%s" % (enterprise_id, note_id))
			cur.execute("DELETE FROM crdr_details_nonstock_material_tax WHERE enterprise_id=%s AND grn_no=%s" % (
				enterprise_id, note_id))
			cur.execute("DELETE FROM crdr_details_nonstock_material WHERE enterprise_id=%s AND grn_no=%s" % (
				enterprise_id, note_id))
			if include_note is True:
				cur.execute("DELETE FROM crdr_tax WHERE enterprise_id=%s AND crdr_id=%s" % (enterprise_id, note_id))
				cur.execute("DELETE FROM note_tags WHERE enterprise_id=%s AND note_id=%s" % (enterprise_id, note_id))
				cur.execute("DELETE FROM crdrnote WHERE enterprise_id=%s AND grn_no=%s" % (enterprise_id, note_id))
			conn.commit()
		except Exception as e:
			conn.rollback()
			raise e
		finally:
			conn.close()

	def notifyPendingReceiptNoteCount(self, enterprise_id=None, sender_id=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:return:
		"""
		try:
			count = self.audit_dao.getReceiptNoteCount(
				enterprise_id=enterprise_id, status=Receipt.STATUS_APPROVED, for_note=True)
			message = None
			if count == 1:
				message = "1 Note is pending for approval"
			elif count > 1:
				message = "%s Notes are pending for approval" % count

			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="ICD", message=message,
				collapse_key="icd_approved_count", include_sender=True)
		except Exception as e:
			logger.exception("ICD Pending notification failed %s" % e.message)

	def notifyPartyAckPendingReceiptNoteCount(self, enterprise_id=None, sender_id=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:return:
		"""
		try:
			count = self.audit_dao.getReceiptNoteCount(
				enterprise_id=enterprise_id, status=Receipt.STATUS_PARTY_ACK_PENDING, for_note=True)
			message = None
			if count == 1:
				message = "1 Note is pending for party acknowledgement"
			elif count > 1:
				message = "%s Notes are pending for party acknowledgement" % count

			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="ICD", message=message,
				collapse_key="icd_party_ack_pending_count", include_sender=True)
		except Exception as e:
			logger.exception("ICD Party Acknowledgement Pending notification failed %s" % e.message)

	def notifyCheckedReceiptNoteCount(self, enterprise_id=None, sender_id=None, include_sender=True):
		"""

		:param enterprise_id:
		:param sender_id:
		:param include_sender:
		:return:
		"""
		try:
			count = self.audit_dao.getReceiptNoteCount(enterprise_id=enterprise_id, status=Receipt.STATUS_ICD_CHECKED)
			message = None
			if count == 1:
				message = "1 Note is checked for verification"
			elif count > 1:
				message = "%s Notes are checked for verification" % count
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="ICD", message=message,
				collapse_key="icd_checked_count", include_sender=include_sender)
			self.notifyPendingReceiptNoteCount(enterprise_id=enterprise_id, sender_id=sender_id)
		except Exception as e:
			logger.exception("ICD Pending notification failed %s" % e.message)

	def notifyVerifyReceiptNoteCount(self, enterprise_id=None, sender_id=None, include_sender=True):
		"""

		:param enterprise_id:
		:param sender_id:
		:param include_sender:
		:return:
		"""
		try:
			count = self.audit_dao.getReceiptNoteCount(enterprise_id=enterprise_id, status=Receipt.STATUS_ICD_CHECKED)
			message = None
			if count == 1:
				message = "1 Note is checked for verification"
			elif count > 1:
				message = "%s Notes are checked for verification" % count
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="ICD", message=message,
				collapse_key="icd_checked_count", include_sender=include_sender)
		except Exception as e:
			logger.exception("ICD Pending notification failed %s" % e.message)

	def notifyVerifiedReceiptNoteCount(self, enterprise_id=None, sender_id=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:return:
		"""
		try:
			count = self.audit_dao.getReceiptNoteCount(enterprise_id=enterprise_id, status=Receipt.STATUS_ICD_VERIFIED)
			message = None
			if count == 1:
				message = "1 Note is checked for verification"
			elif count > 1:
				message = "%s Notes are checked for verification" % count
			if message:
				push_notification(
					enterprise_id=enterprise_id, sender_id=sender_id, module_code="ACCOUNTS", message=message,
					collapse_key="icd_verified_count")
			self.notifyCheckedReceiptNoteCount(enterprise_id=enterprise_id, sender_id=sender_id)
		except Exception as e:
			logger.exception("ICD Pending notification failed %s" % e.message)

	def notifyReceiptNoteCount(self, enterprise_id=None, sender_id=None, status=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param status:
		:return:
		"""
		try:
			if status == Receipt.STATUS_ICD_CHECKED:
				self.notifyCheckedReceiptNoteCount(enterprise_id=enterprise_id, sender_id=sender_id)
			elif status == Receipt.STATUS_ICD_VERIFIED:
				self.notifyVerifiedReceiptNoteCount(enterprise_id=enterprise_id, sender_id=sender_id)
			elif status == Receipt.STATUS_ICD_RETURNED:
				self.notifyCheckedReceiptNoteCount(enterprise_id=enterprise_id, sender_id=sender_id)
		except Exception as e:
			logger.exception("ICD Pending notification failed %s" % e.message)

	def getCheckedGRNCount(self, enterprise_id=None):
		"""
		if enterprise id should not be None for success result
		here PENDING meant CHECKED
		
		:param enterprise_id:
		:return:
		"""
		try:
			logger.info('Fetching count of checked GRN for enterprise %s' % enterprise_id)
			return self.audit_dao.getReceiptNoteCount(enterprise_id=enterprise_id, status=Receipt.STATUS_ICD_CHECKED)
		except Exception as e:
			logger.exception(e)
		return 0

	def getCheckedGRN(self, enterprise_id=None, icd_request_acknowledgement=None):
		"""
		if enterprise id should not be None for success result
		here PENDING meant CHECKED
		
		:param enterprise_id:
		:param icd_request_acknowledgement:
		:return:
		"""
		grn_list = []
		try:
			logger.info('Fetching checked GRN of enterprise %s' % enterprise_id)
			grns = self.audit_dao.getCheckedReceipts(
				enterprise_id=enterprise_id, icd_request_acknowledgement=icd_request_acknowledgement)

			for grn in grns:

				document_uri = None if grn.attachment is None else grn.attachment.attachment.file
				# TODO enhance performance by raw query
				grn_item = {
					'receipt_no': grn.receipt_no, 'code': grn.getCode(), 'supplier_name': grn.supplier.name,
					'currency_name': grn.currency.code, 'receipt_date': grn.receipt_date.strftime("%d-%m-%Y"),
					'invoice_date': grn.invoice_date.strftime("%d-%m-%Y"), 'invoice_value': float(grn.invoice_value),
					'is_grn': grn.received_against in GRN_CAPABLE_TO_ICD, 'project_name': grn.project.name if grn.project else "",
					'project_code': grn.project.code if grn.project else "",
					'note_value': 0, 'note_code': '-', 'remarks': grn.remarks if grn.remarks else [],
					'audit_remarks': grn.audit_remarks if grn.audit_remarks else [], 'materials': [], 'tags': [],
					'document_uri': document_uri, 'documents': None if grn.documents == "" else grn.documents}

				if grn.note_receipt_map:
					if grn.note_receipt_map.note:
						grn_item['note_value'] = float(grn.note_receipt_map.note.value)
						grn_item['note_code'] = grn.note_receipt_map.note.getCode()
						grn_item['note_is_credit'] = grn.note_receipt_map.note.is_credit
						for note_tag in grn.note_receipt_map.note.tags:
							grn_item['tags'].append(note_tag.tag.tag)
				grn_list.append(grn_item)
		except Exception as e:
			logger.info("Could not get the grn list for enterprise %s" % enterprise_id)
			logger.exception(e)
		return grn_list

	def getDocument(self, enterprise_id=None, receipt_no=None, doc_type=None):
		"""

		:param enterprise_id:
		:param receipt_no:
		:param doc_type:
		:return:
		"""
		try:
			logger.info("Querying '%s' document for receipt number '%s' %s" % (doc_type, receipt_no, enterprise_id))
			data = []
			receipt_code = ""
			if doc_type == "receipt":
				doc, receipt_code = StoresService().generateGRNDocument(receipt_no)
				if doc:
					data.append(readFile(getFormattedDocPath(code=receipt_code, id=receipt_no)))
					data.append('pdf')
			elif doc_type == "note":
				doc, receipt_code = self.generateNoteDocument(note_id=receipt_no, enterprise_id=enterprise_id)
				if doc:
					logger.info("Note document - None: %s " % (doc.note_doc is None))
					data.append(doc.note_doc)
					data.append('pdf')
			return data, receipt_code
		except Exception as e:
			logger.exception("Could not get GRN doc for %s: %s" % (doc_type, e))
		return None

	def getGrnMaterials(self, enterprise_id=None, grn_number=None):
		"""

		:param enterprise_id:
		:param grn_number:
		:return:
		"""
		material_list = []
		# TODO in single query
		try:
			grn = self.audit_dao.db_session.query(Receipt).filter(
				Receipt.receipt_no == grn_number, Receipt.enterprise_id == enterprise_id).first()

			# Getting materials
			for m in grn.items:
				mat_item = {
					'quantity': float(m.quantity), 'drawing_no': m.material.drawing_no,'item_id':m.material.material_id,
					'price': float(m.rate * (100 - m.discount) / 100),'accepted_qty': float(m.accepted_qty),
					'received_qty': float(m.received_qty),'is_stocked':m.material.is_stocked
				}
				if m.make:
					mat_item.update({'make_name': m.make.label, 'make_id': m.make.id})
					if m.make_id != DEFAULT_MAKE_ID:
						part_no = helper.getMakePartNumber(
							enterprise_id=enterprise_id, item_id=m.item_id, make_id=m.make_id)
						mat_item['make_name'] = "%s%s" % (mat_item['make_name'], (" - %s" % part_no) if part_no else "")
				if m.material:
					mat_item['name'] = m.material.name
					mat_item['unit'] = m.material.unit.unit_name
				if m.alternate_unit_id :
					scale_factor = helper.getScaleFactor(
						enterprise_id=enterprise_id,item_id=m.material.material_id,alternate_unit_id=m.alternate_unit_id)
					if scale_factor :
						mat_item['quantity'] = float(mat_item['quantity']) / float(scale_factor) if mat_item['quantity'] else 0
						mat_item['price'] = float(mat_item['price']) * float(scale_factor) if mat_item['price'] else 0
						mat_item['unit'] = helper.getUnitName(enterprise_id=enterprise_id,unit_id=m.alternate_unit_id)
				material_list.append(mat_item)

			# Getting non stock materials
			for m in grn.non_stock_items:
				mat_item = {
					'quantity': float(m.received_qty), 'drawing_no': '--', 'unit': m.unit.unit_name,
					'price': float(m.rate * (100 - m.discount) / 100), 'name': m.item_name, 'non_stock': True,
					'make_name': '', 'make_id': 1}
				material_list.append(mat_item)
		except Exception as e:
			logger.exception("Failed loading GRN materials %s" % e.message)

		return material_list

	def returnNote(
			self, enterprise_id=None, receipt_no=None, user_id=None, remarks=None, return_status=None, note_id=None, db_session=None):
		"""

		:param enterprise_id:
		:param receipt_no:
		:param user_id:
		:param remarks:
		:param return_status:
		:param note_id:
		:param db_session:
		:return:
		"""
		logger.info("Making GRN as RETURNED for Receipt - %s" % receipt_no)
		if not db_session:
			db_session = self.audit_dao.db_session
		db_session.begin(subtransactions=True)
		try:
			if int(return_status) == Receipt.STATUS_GRN_RETURNED:
				status = Receipt.STATUS_APPROVED
			else:
				status = Receipt.STATUS_ICD_CHECKED
			current_datetime = datetime.datetime.now()
			grn = db_session.query(Receipt).filter(
				Receipt.receipt_no == receipt_no, Receipt.status == status, Receipt.enterprise_id == enterprise_id).first()
			note = None
			if note_id:
				note = db_session.query(CreditDebitNote).filter(
					CreditDebitNote.id == note_id, CreditDebitNote.status.in_(
						[status, Receipt.STATUS_PARTY_ACK_PENDING, Receipt.STATUS_PARTY_ACKNOWLEDGED]),
					CreditDebitNote.enterprise_id == enterprise_id).first()
			user = db_session.query(User).filter(User.id == user_id).first()
			if grn:
				grn.audit_remarks = updateRemarks(json_remarks_list=grn.audit_remarks, remarks_text=remarks, user=user)
				if int(return_status) == Receipt.STATUS_GRN_RETURNED:
					grn.status = Receipt.STATUS_GRN_RETURNED
					grn.remarks = updateRemarks(json_remarks_list=grn.remarks, remarks_text=remarks, user=user)
				else:
					grn.status = Receipt.STATUS_ICD_RETURNED
				if note:
					note.remarks = updateRemarks(json_remarks_list=note.remarks, remarks_text=remarks, user=user)
					if int(return_status) == Receipt.STATUS_GRN_RETURNED:
						note.status = Receipt.STATUS_GRN_RETURNED
					else:
						note.status = Receipt.STATUS_ICD_RETURNED
				grn.verified_on = current_datetime
				grn.verified_by = user_id
				grn.last_modified_on = current_datetime
				grn.last_modified_by = user_id
				db_session.commit()
				if int(return_status) != Receipt.STATUS_GRN_RETURNED:
					self.notifyCheckedReceiptNoteCount(enterprise_id=enterprise_id, sender_id=user_id)
			elif note:
				if int(return_status) == Receipt.STATUS_GRN_RETURNED:
					note.status = Receipt.STATUS_GRN_RETURNED
				elif int(return_status) == Receipt.STATUS_PARTY_REJECTED:
					note.status = Receipt.STATUS_PARTY_REJECTED
				else:
					note.status = Receipt.STATUS_ICD_RETURNED
				note.remarks = updateRemarks(json_remarks_list=note.remarks, remarks_text=remarks, user=user)
				note.last_modified_on = current_datetime
				note.last_modified_by = user_id
				db_session.commit()
			else:
				db_session.rollback()
				return "ALREADY_UPDATED"
			return receipt_no if grn else note_id
		except Exception as e:
			db_session.rollback()
			logger.exception("Could not change the GRN status %s" % e)
		return None

	def verifyNote(self, enterprise_id=None, receipt_no=None, user_id=None, user_name=None, icd_remarks=None, icd=None, note_id=None, project_code=None):
		"""

		:param enterprise_id:
		:param receipt_no:
		:param user_id:
		:param user_name:
		:param icd_remarks:
		:param icd:
		:return:
		"""
		db_session = self.audit_dao.db_session
		enterprise = db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
		db_session.begin(subtransactions=True)
		try:
			scrutiny_flag = enterprise.setting_flags & enterprise_module_settings['scrutiny_flag'] > 0
			is_bill_exist = False
			note_to_be_approved = None
			receipt_to_be_approved = None
			if note_id:
				note_to_be_approved = db_session.query(CreditDebitNote).filter(
					CreditDebitNote.id == note_id, CreditDebitNote.enterprise_id == enterprise_id, CreditDebitNote.status.in_(
						[Receipt.STATUS_APPROVED, Receipt.STATUS_ICD_CHECKED, Receipt.STATUS_PARTY_ACKNOWLEDGED, Receipt.STATUS_ICD_RETURNED])).first()
			if receipt_no:
				logger.info("Verifying the Note for Receipt - %s" % receipt_no)
				receipt_to_be_approved = db_session.query(Receipt).filter(
					Receipt.receipt_no == receipt_no, Receipt.enterprise_id == enterprise_id, Receipt.status.in_([
						Receipt.STATUS_APPROVED, Receipt.STATUS_ICD_CHECKED, Receipt.STATUS_PARTY_ACKNOWLEDGED,
						Receipt.STATUS_ICD_RETURNED])).first()
			if receipt_to_be_approved:
				current_datetime = datetime.datetime.now()
				if receipt_to_be_approved.status is None:
					db_session.rollback()
					return 'ALREADY_UPDATED', False

				if receipt_to_be_approved.received_against in ('Purchase Order', 'Job Work', 'Note'):
					if not receipt_to_be_approved.supplier.getSupplierLedger():
						receipt_to_be_approved.supplier.setSupplier()
						createPartyLedgers(
							party=receipt_to_be_approved.supplier, user_id=user_id, db_session=db_session, is_supplier=True)
				elif receipt_to_be_approved.received_against in ('Sales Return',):
					if not receipt_to_be_approved.supplier.getCustomerLedger():
						receipt_to_be_approved.supplier.setCustomer()
						createPartyLedgers(
							party=receipt_to_be_approved.supplier, user_id=user_id, db_session=db_session, is_customer=True)

				taxes = receipt_to_be_approved.getConsolidatedTaxList()
				if receipt_to_be_approved.note_receipt_map and receipt_to_be_approved.note_receipt_map.note:
					taxes.union(receipt_to_be_approved.note_receipt_map.note.getConsolidatedTaxList())
				for tax in taxes:
					createTaxLedgers(tax=tax, created_by=user_id, db_session=db_session, is_input=True)

				receipt_to_be_approved.status = Receipt.STATUS_ICD_VERIFIED
				receipt_to_be_approved.verified_on = current_datetime
				receipt_to_be_approved.verified_by = user_id
				receipt_to_be_approved.last_modified_on = current_datetime
				receipt_to_be_approved.last_modified_by = user_id
				if project_code:
					receipt_to_be_approved.project_code = project_code
				receipt_to_be_approved.audit_remarks = updateRemarks(
					json_remarks_list=receipt_to_be_approved.audit_remarks, remarks_text=icd_remarks, user=user_name)
				if note_to_be_approved:
					note_to_be_approved.remarks = updateRemarks(
						json_remarks_list=note_to_be_approved.remarks, remarks_text=icd_remarks, user=user_name)
					if project_code:
						note_to_be_approved.project_code = project_code
				logger.info("Has Supplier Ledger? - %s" % receipt_to_be_approved.supplier.getSupplierLedger())

				if receipt_to_be_approved.received_against in ('Purchase Order', 'Job Work') and float(
						receipt_to_be_approved.invoice_value) > 0:
					if self.audit_dao.db_session.query(LedgerBill).join(Receipt, and_(
						Receipt.ledger_bill_id == LedgerBill.id, Receipt.enterprise_id == LedgerBill.enterprise_id
						)).filter(Receipt.enterprise_id == enterprise_id, Receipt.receipt_no == receipt_no).first() is None:
						self._generateReceiptVoucherAndBills(
								user_id, enterprise, receipt_to_be_approved, db_session,
								receipt_to_be_approved.received_against, scrutiny_flag)
					else:
						is_bill_exist = True
				if not is_bill_exist and receipt_to_be_approved.note_receipt_map and receipt_to_be_approved.note_receipt_map.note:
					self._generateNoteVoucherAndBills(
							user_id, enterprise, receipt_to_be_approved.note_receipt_map.note, db_session,
							receipt_to_be_approved.received_against, scrutiny_flag)
			elif note_to_be_approved:
				if not note_to_be_approved.party.getSupplierLedger():
					note_to_be_approved.party.setSupplier()
					createPartyLedgers(
						party=note_to_be_approved.party, user_id=user_id, db_session=db_session, is_supplier=True)
				note_to_be_approved.remarks = updateRemarks(
					json_remarks_list=note_to_be_approved.remarks, remarks_text=icd_remarks, user=user_name)
				self._generateNoteVoucherAndBills(
							user_id=user_id, enterprise=enterprise, audit_note=note_to_be_approved, db_session=db_session,
							voucher_type="Note", scrutiny_flag=scrutiny_flag)
			logger.info("Note Verified")
			db_session.commit()
			if icd:
				self.notifyVerifyReceiptNoteCount(enterprise_id=enterprise_id, sender_id=user_id)
			if scrutiny_flag:
				AccountService().notifyPendingVoucherCount(enterprise_id=enterprise_id, sender_id=user_id)
			if receipt_no:
				return receipt_no, is_bill_exist
			else:
				return note_id, is_bill_exist
		except Exception as e:
			db_session.rollback()
			logger.exception("Verifying Note failed!!... %s" % e.message)
			raise

	# Automatic Purchase Voucher Entry
	@staticmethod
	def _generateReceiptVoucherAndBills(
				user_id=None, enterprise=None, receipt=None, db_session=None, voucher_type=None,
				scrutiny_flag=None):
		"""
		Persists Purchase Voucher with particulars split under appropriate Ledger heads.
		Also creates a Bill log against the Party Ledger to track any further Bill-wise settlements.

		:param receipt: Receipt model object that will be verified and sent for Accounts
		:param db_session:
		:return:
		"""
		voucher_particulars = []
		voucher_tags = []
		item_no = 0
		db_session.begin(subtransactions=True)
		account_service = AccountService()
		try:
			receipt_items = db_session.query(ReceiptMaterial).filter(
				ReceiptMaterial.receipt_no == receipt.receipt_no).order_by(
				ReceiptMaterial.accepted_qty.desc(), ReceiptMaterial.rate.desc()).limit(2).all()
			target_ledger_id = None
			if voucher_type in ('Purchase Order', 'Job Work'):
				target_ledger_id = receipt.purchase_account_id
				party_ledger_id = receipt.supplier.getSupplierLedger().id
				type_id = 4
			elif voucher_type in ('Sales Return',):
				if receipt_items and len(receipt_items) > 0:
					target_ledger_id = receipt_items[0].dc.sale_account_id
				if target_ledger_id is None:
					sales_ledger = db_session.query(Ledger.id).filter(
						Ledger.group_id == SALES_ACCOUNT_GROUP_ID, Ledger.enterprise_id == receipt.enterprise_id).order_by(
						Ledger.id).first()
					target_ledger_id = sales_ledger.id
				party_ledger_id = receipt.supplier.getCustomerLedger().id
				type_id = 6
			else:
				db_session.rollback()
				return
			enterprise_id = receipt.enterprise_id
			narration = "[%s (%s)]" % (receipt.getCode(), receipt.approved_on.strftime("%d-%b-%Y %H:%M:%S"))

			for item in receipt_items:
				narration = "%s %s %s %s @ %s," % (
					narration, item.material.name, item.accepted_qty, item.material.unit, round(item.rate, 2))
			narration = "%s FROM: %s [%s (%s)]" % (
				narration, receipt.supplier.name, receipt.invoice_no, receipt.invoice_date.strftime("%d-%b-%Y"))
			item_no = item_no + 1
			voucher_value = receipt.getTotalMaterialValue() * receipt.currency_conversion_rate
			voucher_particulars.append(VoucherParticulars(
				item_no=item_no, ledger_id=target_ledger_id, is_debit=1, amount=round(float(voucher_value), 2),
				enterprise_id=enterprise_id))
			tax_values = receipt.getTaxValues()
			for tax in receipt.getConsolidatedTaxList():
				logger.info("Tax Ledger: %s Amount: %s" % (tax.getInputLedgerId(), tax_values[tax.code]))
				tax_ledger_id = tax.getInputLedgerId()
				item_no = item_no + 1
				voucher_value = Decimal(tax_values[tax.code]) * Decimal(receipt.currency_conversion_rate)
				voucher_particulars.append(VoucherParticulars(
					item_no=item_no, ledger_id=tax_ledger_id, is_debit=1, amount=round(float(voucher_value), 2),
					enterprise_id=enterprise_id))

			if receipt.packing_charges > 0:
				package_ledger = db_session.query(Ledger).filter(
					Ledger.group_id == OTHER_DIRECT_EXPENSES_GROUP_ID, Ledger.enterprise_id == enterprise_id,
					Ledger.name == PACKING_ACCOUNT_NAME).first()
				item_no = item_no + 1
				voucher_particulars.append(VoucherParticulars(
					item_no=item_no, ledger_id=package_ledger.id, is_debit=1, enterprise_id=enterprise_id,
					amount=round(receipt.packing_charges * receipt.currency_conversion_rate, 2)))
			if receipt.transport_charges > 0:
				transport_ledger = db_session.query(Ledger).filter(
					Ledger.group_id == OTHER_DIRECT_EXPENSES_GROUP_ID, Ledger.enterprise_id == enterprise_id,
					Ledger.name == TRANSPORT_ACCOUNT_NAME).first()
				item_no = item_no + 1
				voucher_particulars.append(
					VoucherParticulars(
						item_no=item_no, ledger_id=transport_ledger.id, is_debit=1, enterprise_id=enterprise_id,
						amount=round(receipt.transport_charges * receipt.currency_conversion_rate, 2)))
			if receipt.other_charges > 0:
				others_ledger = db_session.query(Ledger).filter(
					Ledger.group_id == OTHER_INDIRECT_EXPENSES_GROUP_ID, Ledger.enterprise_id == enterprise_id,
					Ledger.name == OTHER_CHARGES_ACCOUNT_NAME).first()
				item_no = item_no + 1
				voucher_particulars.append(VoucherParticulars(
					item_no=item_no, ledger_id=others_ledger.id, is_debit=1, enterprise_id=enterprise_id,
					amount=round(receipt.other_charges * receipt.currency_conversion_rate, 2)))
			if receipt.round_off != 0:
				round_off_ledger = db_session.query(Ledger).filter(
					Ledger.group_id == OTHER_INDIRECT_EXPENSES_GROUP_ID, Ledger.enterprise_id == enterprise_id,
					Ledger.name == ROUND_OFF_ACCOUNT_NAME).first()
				is_debit = 1 if receipt.round_off > 0 else 0
				item_no = item_no + 1
				voucher_particulars.append(
					VoucherParticulars(
						item_no=item_no, ledger_id=round_off_ledger.id, is_debit=is_debit, enterprise_id=enterprise_id,
						amount=round((receipt.round_off * receipt.currency_conversion_rate), 2) * (
							1 if is_debit else -1)))

			party_net_debit = receipt.invoice_value * receipt.currency_conversion_rate
			item_no = item_no + 1
			voucher_particulars.append(VoucherParticulars(
				item_no=item_no, ledger_id=party_ledger_id, is_debit=0, amount=round(party_net_debit, 2),
				enterprise_id=enterprise_id))
			voucher = Voucher(
				type_id=type_id, voucher_no=0, voucher_date=receipt.inward_date, narration=narration,
				enterprise_id=enterprise_id, project_code=receipt.project_code, created_by=receipt.verified_by)
			if not scrutiny_flag:
				account_service.prepareVoucherApproval(
						voucher_to_be_saved=voucher, enterprise=enterprise, approved_by=user_id)
			receipt_tags = db_session.query(ReceiptTag).filter(ReceiptTag.receipt_id == receipt.receipt_no).all()
			for tag in receipt_tags:
				voucher_tags.append(VoucherTag(tag_id=tag.tag_id))
			if float(receipt.invoice_value) > 0:
				if voucher_type in ('Purchase Order', 'Job Work'):
					ledger_bill = LedgerBill(
						bill_no=receipt.invoice_no, bill_date=receipt.invoice_date, ledger_id=party_ledger_id,
						net_value=round(float(receipt.invoice_value * receipt.currency_conversion_rate), 2),
						is_debit=False, enterprise_id=enterprise_id)
					ledger_bill.voucher = voucher
					ledger_bill_settlement = LedgerBillSettlement(
						cr_value=round(float(receipt.invoice_value * receipt.currency_conversion_rate), 2))
					ledger_bill_settlement.voucher = voucher
					ledger_bill_settlement.bill = ledger_bill
					ledger_bill_settlement.enterprise_id = enterprise_id
					voucher.particulars = voucher_particulars
					receipt.ledger_bill = ledger_bill
					receipt.voucher = voucher
					voucher.tags = voucher_tags
					db_session.add(ledger_bill_settlement)
				elif voucher_type in ('Sales Return',):
					query = """SELECT DISTINCT 
								(inv.ledger_bill_id), gm.dc_id
							FROM
								grn_material AS gm, 
								invoice AS inv
							WHERE 
								gm.dc_id = inv.id
								AND gm.enterprise_id = inv.enterprise_id
								AND gm.grnNumber = %s """ % receipt.receipt_no
					ledger_bill_list = dao.executeQuery(query)
					for item in ledger_bill_list:
						ledger_bill = db_session.query(LedgerBill).filter(LedgerBill.id == item[0]).first()
						tax_values = receipt.getTaxValuesForDC(dc=item[1])
						tax_value = 0
						for tax in receipt.getConsolidatedTaxListForDC(dc=item[1]):
							tax_value += Decimal(tax_values[tax.code])
						credit_amount = round(float((receipt.getTotalMaterialValueForDC(
							dc=item[1]) + tax_value) * receipt.currency_conversion_rate), 2)
						ledger_bill_settlement = LedgerBillSettlement(
							cr_value=credit_amount)
						ledger_bill_settlement.voucher = voucher
						ledger_bill_settlement.bill = ledger_bill
						ledger_bill_settlement.enterprise_id = enterprise_id
						voucher.particulars = voucher_particulars
						receipt.ledger_bill = ledger_bill
						receipt.voucher = voucher
						voucher.tags = voucher_tags
						db_session.add(ledger_bill_settlement)
			else:
				db_session.rollback()
				return
			db_session.commit()
			VoucherChangelog().queryVoucherInsert(user_id=user_id, enterprise_id=enterprise.id, data=voucher)
		except Exception as e:
			db_session.rollback()
			logger.error("Purchase voucher and bill creation failed %s " % e.message)
			raise

	@staticmethod
	def _generateNoteVoucherAndBills(
				user_id=None, enterprise=None, audit_note=None, db_session=None, voucher_type=None,
				scrutiny_flag=None):
		"""

		:param user_id:
		:param enterprise:
		:param audit_note:
		:param db_session:
		:param voucher_type:
		:param scrutiny_flag:
		:return:
		"""
		voucher_particulars = []
		voucher_tags = []
		index = 0
		db_session.begin(subtransactions=True)
		account_service = AccountService()
		try:
			enterprise_id = audit_note.enterprise_id
			if audit_note:
				target_ledger_id = None
				if voucher_type in ('Sales Return',):
					if audit_note.items and len(audit_note.items) > 0:
						target_ledger_id = audit_note.note_receipt_map.receipt.items[0].dc.sale_account_id
					elif audit_note.non_stock_items and len(audit_note.non_stock_items) > 0:
						target_ledger_id = audit_note.note_receipt_map.receipt.non_stock_items[0].dc.sale_account_id
					if target_ledger_id is None:
						sales_ledger = db_session.query(Ledger.id).filter(
							Ledger.group_id == SALES_ACCOUNT_GROUP_ID,
							Ledger.enterprise_id == audit_note.enterprise_id).order_by(
							Ledger.id).first()
						target_ledger_id = sales_ledger.id
					ledger_id = audit_note.party.getCustomerLedger().id
				else:  # ('Purchase Order', 'Job Work', 'Note')
					ledger_id = audit_note.party.getSupplierLedger().id
					if audit_note.note_receipt_map:
						target_ledger_id = audit_note.note_receipt_map.receipt.purchase_account_id
					if target_ledger_id is None:
						purchase_ledger = db_session.query(Ledger.id).filter(
							Ledger.group_id == PURCHASE_ACCOUNT_GROUP_ID,
							Ledger.enterprise_id == audit_note.enterprise_id).order_by(
							Ledger.id).first()
						target_ledger_id = purchase_ledger.id
				logger.info("Voucher to be created Note: %s" % audit_note)
				is_note_credit = (audit_note.getNetCreditValue() > 0)
				net_note_value = round(
					Decimal(audit_note.value) * Decimal(audit_note.currency_conversion_rate), 2)
				logger.info("Net Note Value: %s %s" % (
					abs(audit_note.getNetCreditValue()), "Cr" if is_note_credit else "Dr"))
				index += 1
				voucher_particulars.append(VoucherParticulars(
					item_no=index, ledger_id=target_ledger_id, is_debit=is_note_credit, enterprise_id=enterprise_id,
					amount=round(abs(audit_note.getNetCreditValue()) * audit_note.currency_conversion_rate, 2)))
				tax_values = audit_note.getTaxValues(discounts_applied=False)
				for tax in audit_note.getConsolidatedTaxList():
					tax_ledger_id = tax.getInputLedgerId()
					logger.info("Tax Ledger: %s Amount: %s" % (tax_ledger_id, tax_values[tax.code]))
					index += 1
					is_tax_item_debit = tax_values[tax.code] >= 0
					voucher_particulars.append(VoucherParticulars(
						item_no=index, ledger_id=tax_ledger_id, is_debit=is_tax_item_debit,
						amount=round(Decimal(abs(tax_values[tax.code])) * Decimal(
							audit_note.currency_conversion_rate), 2), enterprise_id=enterprise_id))
				index += 1
				voucher_particulars.append(VoucherParticulars(
					item_no=index, ledger_id=ledger_id,
					is_debit=not is_note_credit, amount=net_note_value, enterprise_id=enterprise_id))
				if audit_note.round_off != 0:
					round_off_ledger = db_session.query(Ledger).filter(
						Ledger.group_id == OTHER_INDIRECT_EXPENSES_GROUP_ID, Ledger.enterprise_id == enterprise_id,
						Ledger.name == "Round-off").first()
					is_debit = ((audit_note.round_off > 0) == is_note_credit)  # XNOR(round_off>0, is_note_credit)
					index += 1
					voucher_particulars.append(VoucherParticulars(
						item_no=index, ledger_id=round_off_ledger.id, is_debit=is_debit,
						amount=abs(round(Decimal(audit_note.round_off) * Decimal(
							audit_note.currency_conversion_rate), 2)), enterprise_id=enterprise_id))

				voucher = Voucher(
					type_id=6, voucher_no=0, voucher_date=audit_note.approved_on, enterprise_id=enterprise_id,
					project_code=audit_note.project_code, created_by=audit_note.approved_by,
					narration="Note Created. Grn No: %s ,Inv No: %s ,Inv Value: %s" % (
						audit_note.note_receipt_map.receipt.getCode() if audit_note.note_receipt_map else audit_note.getCode(),
						audit_note.inv_no, round(Decimal(audit_note.inv_value) if audit_note.inv_value else 0 * Decimal(
							audit_note.currency_conversion_rate) if audit_note.currency_conversion_rate else 1, 2)))
				if not scrutiny_flag:
					account_service.prepareVoucherApproval(
						voucher_to_be_saved=voucher, enterprise=enterprise, approved_by=user_id)

				note_tags = db_session.query(NoteTag).filter(NoteTag.note_id == audit_note.id).all()
				for tag in note_tags:
					voucher_tags.append(VoucherTag(tag_id=tag.tag_id))
				ledger_bill_settlement = LedgerBillSettlement(
					cr_value=net_note_value if is_note_credit else 0, dr_value=0 if is_note_credit else net_note_value)
				if voucher_type not in ('Sales Return',):
					ledger_bill_settlement.voucher = voucher
					ledger_bill = None
					if audit_note.note_receipt_map:
						ledger_bill = audit_note.note_receipt_map.receipt.ledger_bill
					if not ledger_bill:
						invoice_type = None
						if audit_note.note_receipt_map:
							invoice_type = audit_note.note_receipt_map.receipt.invoice_type
						bill_receipt = StoresService().getReceiptForPurchaseInvoice(
							invoice_no=audit_note.inv_no, invoice_date=audit_note.inv_date,
							enterprise_id=enterprise_id, fy_start_day=audit_note.enterprise.fy_start_day,
							supplier_id=audit_note.party_id, invoice_type=invoice_type if audit_note.note_receipt_map else 2)
						ledger_bill = bill_receipt.ledger_bill if bill_receipt else None
						logger.info("Bill Already exist: %s" % bill_receipt)
						if not ledger_bill:
							ledger_bill = LedgerBill(
								bill_no=audit_note.inv_no, bill_date=audit_note.inv_date,
								ledger_id=ledger_id, is_debit=not is_note_credit, enterprise_id=enterprise_id,
								net_value=round(Decimal(
									audit_note.inv_value if audit_note.inv_value else 0 * audit_note.currency_conversion_rate), 2))
							ledger_bill.voucher = voucher
							if audit_note.note_receipt_map:
								audit_note.note_receipt_map.receipt.ledger_bill = ledger_bill
						else:
							if audit_note.note_receipt_map:
								audit_note.note_receipt_map.receipt.ledger_bill_id = ledger_bill.id
								db_session.add(audit_note.note_receipt_map.receipt)
							else:
								audit_note.ledger_bill_id = ledger_bill.id
					ledger_bill_settlement.bill = ledger_bill
					ledger_bill_settlement.enterprise_id = enterprise_id
					_net_value = Decimal(ledger_bill.getNetValue()) + Decimal(
						ledger_bill_settlement.cr_value) - Decimal(ledger_bill_settlement.dr_value)
					ledger_bill.net_value = round(abs(_net_value), 2)
					ledger_bill.is_debit = _net_value < 0
					voucher.particulars = voucher_particulars
					voucher.tags = voucher_tags
					audit_note.status = Receipt.STATUS_ICD_VERIFIED
					audit_note.approved_by = user_id
					audit_note.ledger_bill = ledger_bill
					audit_note.voucher = voucher
					db_session.add(ledger_bill_settlement)
				else:
					query = """SELECT DISTINCT 
								(inv.ledger_bill_id), gm.dc_id
							FROM
								grn_material AS gm, 
								invoice AS inv
							WHERE 
								gm.dc_id = inv.id
								AND gm.enterprise_id = inv.enterprise_id
								AND gm.grnNumber = %s """ % audit_note.note_receipt_map.receipt.receipt_no
					ledger_bill_list = dao.executeQuery(query)
					for item in ledger_bill_list:
						ledger_bill = db_session.query(LedgerBill).filter(LedgerBill.id == item[0]).first()
						tax_values = audit_note.getTaxValuesForDC(dc=item[1])
						tax_value = 0
						for tax in audit_note.getConsolidatedTaxListForDC(dc=item[1]):
							tax_value += Decimal(tax_values[tax.code])
						credit_amount = round(float((audit_note.getTotalMaterialValueForDC(
							dc=item[1]) + tax_value) * audit_note.note_receipt_map.receipt.currency_conversion_rate), 2)
						ledger_bill_settlement = LedgerBillSettlement(
							cr_value=credit_amount)
						ledger_bill_settlement.voucher = voucher
						ledger_bill_settlement.bill = ledger_bill
						ledger_bill_settlement.enterprise_id = enterprise_id
						bill_net_value = float(ledger_bill.net_value) * (1 if ledger_bill.is_debit else -1) - round(
										Decimal(audit_note.inv_value) * Decimal(audit_note.currency_conversion_rate), 2)
						ledger_bill.is_debit = bill_net_value > 0
						ledger_bill.net_value = abs(bill_net_value)
						voucher.particulars = voucher_particulars
						audit_note.note_receipt_map.receipt.ledger_bill = ledger_bill
						audit_note.note_receipt_map.receipt.voucher = voucher
						voucher.tags = voucher_tags
						audit_note.status = Receipt.STATUS_ICD_VERIFIED
						audit_note.approved_by = user_id
						audit_note.ledger_bill = ledger_bill
						audit_note.voucher = voucher
						db_session.add(ledger_bill_settlement)
				db_session.commit()
				VoucherChangelog().queryVoucherInsert(user_id=user_id, enterprise_id=enterprise.id, data=voucher)
		except Exception as e:
			db_session.rollback()
			logger.error("Note Voucher Creation Failed!!... %s" % e.message)
			raise

	def generateNoteDocument(self, note_id=None, enterprise_id=None):
		"""

		:param note_id:
		:param enterprise_id:
		:return:
		"""
		db_session = self.audit_dao.db_session
		try:
			db_session.begin(subtransactions=True)
			note = self.audit_dao.getNote(enterprise_id=enterprise_id, note_id=note_id)
			note_code = ""
			if not note or (len(note.non_stock_items) == 0 and len(note.items) == 0):
				# If no note is generated, no need to attempt Document generation for the note.
				logger.info("No Note is associated for the Receipt")
				db_session.rollback()
				return None, note_code

			note_document = db_session.query(CreditDebitNoteDocument).join(CreditDebitNote, and_(
				CreditDebitNoteDocument.note_id == CreditDebitNote.id,
				CreditDebitNoteDocument.revised_on == CreditDebitNote.last_modified_on)).filter(
				CreditDebitNoteDocument.enterprise_id == enterprise_id, CreditDebitNoteDocument.note_id == note_id).first()
			if not note_document:
				note_document = CreditDebitNoteDocument(note_id=note_id, enterprise_id=enterprise_id)
				note_document.note = note
			note_code = note.getCode()
			temp_doc_path = getFormattedDocPath(code=note_code, id=note_id)
			if note_document.note_doc:
				writeFile(note_document.note_doc, temp_doc_path)
				db_session.rollback()
			else:
				note_doc_generator = DebitNoteDocumentGenerator(temp_doc_path)
				document_pdf = note_doc_generator.generatePDF(source=note)
				writeFile(document_pdf, temp_doc_path)
				note_document.note_doc = readFile(temp_doc_path)
				note_document.revised_on = note.last_modified_on
				note_document.revised_by = note.last_modified_by
				note_document.note = note
				if note.status == Receipt.STATUS_ICD_VERIFIED:
					db_session.commit()
				else:
					# Do not remove this log; it causes exception NoneType for receipt.audit_note.
					logger.info("Generated note doc for %s" % note_document.note.getCode())
					make_transient(note_document)
					make_transient(note)
					db_session.rollback()
			return note_document, note_code
		except Exception as e:
			db_session.rollback()
			logger.exception("Note Document creation failed: %s" % e)
			raise

	def superEditNoteCode(
			self, enterprise_id=None, user_id=None, receipt_no=None, new_financial_year=None,
			new_code=None, new_sub_number=None):
		"""

		:return: [is_success, json_response]
		"""
		self.audit_dao.db_session.begin(subtransactions=True)
		try:
			receipt_to_be_modified = self.audit_dao.getReceipt(enterprise_id=enterprise_id, receipt_no=receipt_no)
			old_code = receipt_to_be_modified.audit_note.getCode()
			if new_sub_number is not None and new_sub_number.strip() == "":
				new_sub_number = None
			existing_receipt = self.audit_dao.getReceipt(
				enterprise_id=enterprise_id, financial_year=new_financial_year,
				code=new_code, sub_number=new_sub_number)
			response = response_code.failure()
			response['code'] = old_code
			if not existing_receipt:
				receipt_to_be_modified.audit_note.financial_year = new_financial_year
				receipt_to_be_modified.audit_note.code = new_code
				receipt_to_be_modified.audit_note.sub_number = new_sub_number
				receipt_to_be_modified.super_modified_on = datetime.datetime.now()
				receipt_to_be_modified.last_modified_on = datetime.datetime.now()
				receipt_to_be_modified.last_modified_by = user_id
				receipt_to_be_modified.super_modified_by = user_id
				self.audit_dao.db_session.add(receipt_to_be_modified)
				response = response_code.success()
				response['custom_message'] = "Successfully changed the Note code from '%s' to '%s'!" % (
					old_code, receipt_to_be_modified.audit_note.getCode())
				response['code'] = receipt_to_be_modified.audit_note.getCode()
				message = PUSH_NOTIFICATION['icd_code'] % (old_code, receipt_to_be_modified.audit_note.getCode())
				push_notification(
					enterprise_id=enterprise_id, sender_id=user_id, module_code="ICD", message=message,code=receipt_to_be_modified.audit_note.getCode(),status=0)
			elif receipt_to_be_modified.receipt_no == existing_receipt.receipt_no:
				response['custom_message'] = "No changes detected in Note code to save!"
			else:
				response['custom_message'] = "A Note with code '%s' already exists. Please assign a different Code!" % (
					existing_receipt.audit_note.getCode())
			self.audit_dao.db_session.commit()
			return response
		except:
			self.audit_dao.db_session.rollback()
			raise

	@staticmethod
	def getIcdMaterials(grn_no=None, received_against=None, enterprise_id=None):
		try:
			if received_against == "Job Work":
				condition = " and smp.is_service=1"
			else:
				condition = " and smp.is_service=0"
			if received_against != 'Note':
				query_check_grn = "select status from grn where grn_no=" + grn_no + ""
			else:
				query_check_grn = "select status from crdrnote where grn_no=" + grn_no + ""
			grn_check = executeQuery(query_check_grn)
			if grn_check[0][0] in (2, 3) and received_against != 'Note':
				query_grn_materials_by_grn_no = """SELECT IFNULL(a.po_no,0) as po_no, IFNULL(b.name,"") as item_name,
				IFNULL(b.drawing_no,"") as drawing_no, IFNULL(a.dc_qty,0) as dc_qty, IFNULL(a.rec_qty,0) as rec_qty, 
				IFNULL(a.acc_qty,0) as acc_qty, (IFNULL(a.rec_qty,0) - IFNULL(a.acc_qty,0)) AS reject_qty, 
				IFNULL((SELECT price FROM recent_valid_price_profiles AS smp WHERE a.item_id = item_id AND supp_id = g.party_id 
				AND g.inward_date BETWEEN effect_since AND IFNULL(effect_till, NOW()) {condition} AND smp.status = 1 
				ORDER BY effect_since DESC LIMIT 1), IFNULL(b.price,0)) AS price, IF(pom.po_price IS NOT NULL, 
				ROUND((pom.po_price - ((pom.po_price * pom.discount) / 100)), 2), 
				IFNULL((SELECT price FROM recent_valid_price_profiles AS smp WHERE a.item_id= item_id AND supp_id = g.party_id 
				AND g.inward_date BETWEEN effect_since AND IFNULL(effect_till, NOW()) {condition} AND smp.status = 1 
				ORDER BY effect_since DESC LIMIT 1), IFNULL(b.price,0))) AS porate, 
				ROUND((a.inv_rate - ((a.inv_rate * a.discount) / 100)), 2) AS invrate, IFNULL(h.rate,0) as rate, 
				IFNULL(h.amount,0) as amount, IFNULL(nr.reason,"") as reason, IFNULL(h.is_credit,0) as is_credit,				
				IFNULL((SELECT tax_code FROM crdr_details_tax AS mt, tax AS t, invoice AS inv WHERE mt.crdr_id = cgm.crdrnote_id 
				AND mt.item_id = a.item_id AND mt.make_id = a.make_id AND mt.enterprise_id = t.enterprise_id AND (mt.po_no = a.po_no OR mt.dc_id = 
				inv.id OR mt.dc_id = a.dc_id OR mt.po_no is null) AND mt.tax_code = t.code AND mt.reason_id = nr.id AND 
				t.type = 'CGST' LIMIT 1), '0') AS CGST, IFNULL((SELECT tax_code FROM crdr_details_tax AS mt, 
				tax AS t, invoice AS inv WHERE mt.crdr_id = cgm.crdrnote_id AND mt.item_id = a.item_id AND mt.make_id = a.make_id
				AND mt.enterprise_id = t.enterprise_id AND (mt.po_no = a.po_no OR mt.dc_id = inv.id OR mt.dc_id = a.dc_id 
				OR mt.po_no is null) AND mt.tax_code = t.code AND mt.reason_id = nr.id  AND 
				t.type = 'SGST' LIMIT 1), '0') AS SGST, IFNULL((SELECT tax_code FROM crdr_details_tax AS mt, tax AS t, 
				invoice AS inv WHERE mt.crdr_id = cgm.crdrnote_id AND mt.item_id = a.item_id AND mt.make_id = a.make_id AND mt.enterprise_id = t.enterprise_id 
				AND (mt.po_no = a.po_no OR mt.dc_id = inv.id OR mt.dc_id = a.dc_id OR mt.po_no is null) AND mt.tax_code = t.code AND 
				mt.reason_id = nr.id AND t.type = 'IGST' LIMIT 1),'0') AS IGST,								
				1 as make_id, b.makes_json AS make_name,
				IFNULL(d.unit_id, 0) AS unit_id, IFNULL(a.dc_id, 0) AS dc_id, IFNULL(b.price,0) AS store_price,
				IFNULL(a.is_faulty, 0) as is_faulty,IFNULL(h.qty,0) as qty,IFNULL(h.rate,0) as rate ,b.id as item_id, 
				IFNULL(a.alternate_unit_id, 0) as alternate_unit_id, IFNULL(a.hsn_code, "") as hsn_code, b.is_service as is_service FROM grn_material AS a  
				JOIN materials AS b ON a.item_id = b.id AND a.enterprise_id = b.enterprise_id 
				JOIN grn AS g ON a.grnNumber = g.grn_no LEFT JOIN purchase_order AS o ON o.id = a.po_no JOIN 
				unit_master AS d ON d.unit_id = b.unit AND b.enterprise_id = d.enterprise_id LEFT JOIN 
				invoice AS inv ON inv.id = a.dc_id LEFT JOIN purchase_order_material AS pom ON pom.item_id = a.item_id 
				AND pom.pid = a.po_no LEFT JOIN crdrnote_grn_map AS cgm ON cgm.crdrnote_grn_no = g.grn_no  
				JOIN crdr_details AS h ON h.crdr_id = cgm.crdrnote_id AND a.item_id = h.item_id AND a.make_id = h.make_id AND a.is_faulty = h.is_faulty
                AND ((a.po_no = h.po_no OR (a.po_no IS NULL AND h.po_no IS NULL) OR a.dc_id = h.dc_id OR a.dc_id = inv.id 
                OR (a.dc_id IS NULL AND h.dc_id IS NULL))) JOIN note_reason AS nr ON nr.id = h.reason_id WHERE 
				grnNumber = {grn_no} GROUP BY a.dc_id , a.item_id, a.make_id , h.reason_id ,a.po_no, a.is_faulty
				UNION
				SELECT IFNULL(h.po_no, 0) AS po_no,IFNULL(h.description, '') AS item_name,'' AS drawing_no,
				0 AS dc_qty,0 AS rec_qty,0 AS acc_qty,0 AS reject_qty,0 AS price,0 AS porate,0 AS invrate,
				IFNULL(h.rate, 0) AS rate,IFNULL(h.amount, 0) AS amount,IFNULL(nr.reason, '') AS reason,
				IFNULL(h.is_credit, '') AS is_credit,				
				IFNULL((SELECT tax_code FROM crdr_details_nonstock_material_tax AS mt,
				tax AS t,invoice AS inv WHERE mt.grn_no = cgm.crdrnote_id AND mt.description = h.description 
				AND mt.enterprise_id = t.enterprise_id AND (mt.po_no = h.po_no OR mt.dc_id = inv.id OR mt.po_no IS NULL) 
				AND mt.tax_code = t.code AND mt.reason_id = nr.id AND t.type = 'CGST' LIMIT 1),'0') AS CGST,				
				IFNULL((SELECT tax_code FROM crdr_details_nonstock_material_tax AS mt,
				tax AS t, invoice AS inv WHERE mt.grn_no = cgm.crdrnote_id AND mt.description = h.description AND mt.enterprise_id = t.enterprise_id
				AND (mt.po_no = h.po_no OR mt.dc_id = inv.id OR mt.po_no IS NULL) AND mt.tax_code = t.code AND mt.reason_id = nr.id 
				AND t.type = 'SGST'LIMIT 1),'0') AS SGST,				
				IFNULL((SELECT tax_code FROM crdr_details_nonstock_material_tax AS mt, tax AS t, invoice AS inv WHERE mt.grn_no = cgm.crdrnote_id
				AND mt.description = h.description AND mt.enterprise_id = t.enterprise_id AND (mt.po_no = h.po_no OR mt.dc_id = inv.id OR mt.po_no IS NULL)
				AND mt.tax_code = t.code AND mt.reason_id = nr.id AND t.type = 'IGST'LIMIT 1),'0') AS IGST,
				1 AS make_id,'' AS make_name,IFNULL(h.unit_id, 0) AS unit_id,IFNULL(h.dc_id, 0) AS dc_id,0 AS store_price,0 AS is_faulty,IFNULL(h.qty, 0) AS qty,
				IFNULL(h.rate, 0) AS rate,'' AS item_id,0 AS alternate_unit_id,IFNULL(h.hsn_code, "") as hsn_code, 0 as is_service FROM crdr_details_nonstock_material AS h 
				LEFT JOIN crdrnote_grn_map AS cgm ON cgm.crdrnote_id = h.grn_no JOIN grn AS g ON cgm.crdrnote_grn_no = g.grn_no 
				AND h.enterprise_id = g.enterprise_id LEFT JOIN purchase_order AS o ON o.id = h.po_no LEFT JOIN unit_master AS d ON d.unit_id = h.unit_id 
				AND d.enterprise_id = h.enterprise_id LEFT JOIN invoice AS dc ON dc.id = h.dc_id JOIN note_reason AS nr ON nr.id = h.reason_id WHERE 
				g.grn_no = {grn_no} GROUP BY h.dc_id , h.description , h.reason_id """.format(
					grn_no=grn_no, condition=condition)
			elif grn_check[0][0] in (2, 3, 5, 6, 7, -2) and received_against == 'Note':
				query_grn_materials_by_grn_no = """SELECT IFNULL(h.po_no, 0) AS po_no,IFNULL(h.description, '') AS item_name,'' AS drawing_no,
				0 AS dc_qty,0 AS rec_qty,0 AS acc_qty,0 AS reject_qty,0 AS price,0 AS porate,0 AS invrate,
				IFNULL(h.rate, 0) AS rate,IFNULL(h.amount, 0) AS amount,IFNULL(nr.reason, '') AS reason,
				IFNULL(h.is_credit, '') AS is_credit,				
				IFNULL((SELECT tax_code FROM crdr_details_nonstock_material_tax AS mt,
				tax AS t,invoice AS inv WHERE mt.grn_no = g.grn_no AND mt.description = h.description 
				AND mt.enterprise_id = t.enterprise_id AND (mt.po_no = h.po_no OR mt.dc_id = inv.id OR mt.po_no IS NULL) 
				AND mt.tax_code = t.code AND mt.reason_id = nr.id AND t.type = 'CGST' LIMIT 1),'0') AS CGST,				
				IFNULL((SELECT tax_code FROM crdr_details_nonstock_material_tax AS mt,
				tax AS t, invoice AS inv WHERE mt.grn_no = g.grn_no AND mt.description = h.description AND mt.enterprise_id = t.enterprise_id
				AND (mt.po_no = h.po_no OR mt.dc_id = inv.id OR mt.po_no IS NULL) AND mt.tax_code = t.code AND mt.reason_id = nr.id 
				AND t.type = 'SGST'LIMIT 1),'0') AS SGST,				
				IFNULL((SELECT tax_code FROM crdr_details_nonstock_material_tax AS mt, tax AS t, invoice AS inv WHERE mt.grn_no = g.grn_no
				AND mt.description = h.description AND mt.enterprise_id = t.enterprise_id AND (mt.po_no = h.po_no OR mt.dc_id = inv.id OR mt.po_no IS NULL)
				AND mt.tax_code = t.code AND mt.reason_id = nr.id AND t.type = 'IGST'LIMIT 1),'0') AS IGST,
				1 AS make_id,'' AS make_name,IFNULL(h.unit_id, 0) AS unit_id,IFNULL(h.dc_id, 0) AS dc_id,0 AS store_price,0 AS is_faulty,IFNULL(h.qty, 0) AS qty,
				IFNULL(h.rate, 0) AS rate,'' AS item_id,0 AS alternate_unit_id,IFNULL(h.hsn_code, "") as hsn_code FROM crdr_details_nonstock_material AS h 
				JOIN crdrnote AS g ON h.grn_no = g.grn_no AND h.enterprise_id = g.enterprise_id LEFT JOIN purchase_order AS o ON o.id = h.po_no 
				LEFT JOIN unit_master AS d ON d.unit_id = h.unit_id AND d.enterprise_id = h.enterprise_id 
				LEFT JOIN invoice AS dc ON dc.id = h.dc_id JOIN note_reason AS nr ON nr.id = h.reason_id 
				WHERE g.grn_no = {grn_no} GROUP BY h.dc_id , h.description , h.reason_id , h.po_no""".format(grn_no=grn_no)
			else:
				query_grn_materials_by_grn_no = """
					SELECT gm.po_no,gm.dc_id,pom.po_code,im.issue_code,gm.drawing_no,gm.item_name as item_name, 
					IF(smp.price IS NOT NULL, smp.price ,IFNULL(m.store_price,0)) as store_price, 
					IF(smp.price IS NOT NULL, smp.price ,IFNULL(m.store_price,0)) as approved_rate, gm.make_id AS make_id,
					m.make_name as make_name, gm.is_faulty,gm.unit_id AS unit_id,m.unit_name, 
					gm.dc_qty as dc_qty,gm.rec_qty as rec_qty,gm.acc_qty as acc_qty,gm.discount,
					ROUND((gm.inv_rate - ((gm.inv_rate*gm.discount)/100)),2) as inv_rate,
					pom.order_qty, pom.pending_qty,IF(pom.po_price IS NOT NULL,
					ROUND((pom.po_price - ((pom.po_price*pom.discount)/100)),2), IFNULL(smp.price, 0)) as po_price,
					im.unit_rate,im.issue_qty as issue_qty,im.ret_qty as ret_qty, cgst_code,cgst_rate,
					sgst_code,sgst_rate,igst_code,igst_rate,IF(gm.drawing_no IS NULL, 1, 0) stock_type, 
					IFNULL(ROUND((im.unit_rate - ((im.unit_rate*im.discount)/100)),2),0) as sales_return_rate, gm.item_id as item_id, 
					IFNULL(gm.alternate_unit_id, 0) as alternate_unit_id,IFNULL(gm.hsn_code, "") as hsn_code, m.is_service as is_service FROM grn_material_tax_abstract AS gm 
					LEFT JOIN grn as g on gm.grn_no = g.grn_no AND 
					gm.enterprise_id=g.enterprise_id LEFT JOIN material_abstract as m on m.enterprise_id = gm.enterprise_id 
					AND m.item_id = gm.item_id LEFT JOIN recent_valid_price_profiles AS smp ON 
					m.item_id = smp.item_id AND smp.status = 1 AND g.party_id = smp.supp_id 
					AND g.inward_date BETWEEN smp.effect_since AND IFNULL(smp.effect_till, NOW()) {condition} 
					LEFT JOIN po_material_received_abstract AS pom ON gm.po_no = pom.po_id 
					AND (gm.item_id = pom.item_id OR gm.item_name = pom.item_name) AND gm.make_id = pom.make_id
					AND gm.enterprise_id = pom.enterprise_id AND gm.is_faulty = pom.is_faulty LEFT JOIN 
					issue_material_received_abstract AS im ON gm.enterprise_id = im.enterprise_id AND gm.dc_id = im.issue_id
					AND (gm.item_id = im.item_id OR gm.item_name = im.item_name) AND gm.make_id = im.make_id AND 
					gm.is_faulty = im.is_faulty WHERE gm.grn_no = {grn_no} GROUP BY gm.grn_no , gm.drawing_no , 
					gm.is_faulty , gm.po_no , gm.dc_id, gm.item_name;""".format(grn_no=grn_no, condition=condition)
			grn_materials = executeQuery(query=query_grn_materials_by_grn_no, as_dict=True)
			if grn_materials:
				if grn_check[0][0] in (2, 3) or received_against == 'Note':
					for material in grn_materials:
						material['scale_factor'] = 1
						if material['alternate_unit_id'] != '0' and int(material['alternate_unit_id']) != 0:
							scale_factor = helper.getScaleFactor(
								enterprise_id=enterprise_id, item_id=material['item_id'],
								alternate_unit_id=material['alternate_unit_id'])
							if scale_factor:
								material['dc_qty'] = float(material['dc_qty']) / float(scale_factor) if material[
									'dc_qty'] else 0
								material['rec_qty'] = float(material['rec_qty']) / float(scale_factor) if material[
									'rec_qty'] else 0
								material['acc_qty'] = float(material['acc_qty']) / float(scale_factor) if material[
									'acc_qty'] else 0
								material['reject_qty'] = float(material['reject_qty']) / float(scale_factor) if material[
									'reject_qty'] else 0
								material['price'] = float(material['price']) * float(scale_factor) if material['price'] else 0
								material['invrate'] = float(material['invrate']) * float(scale_factor) if material['invrate'] else 0
								material['porate'] = float(material['porate']) * float(scale_factor) if material['porate'] else 0
								material['amount'] = float(material['amount']) * float(scale_factor) if material['amount'] else 0
								material['unit_name'] = helper.getUnitName(enterprise_id=enterprise_id, unit_id=material['alternate_unit_id'])
								material['scale_factor'] = scale_factor if scale_factor else 1
				else:
					for material in grn_materials:
						material['scale_factor'] = 1
						if material['alternate_unit_id'] != '0' and int(material['alternate_unit_id']) != 0:
							scale_factor = helper.getScaleFactor(
								enterprise_id=enterprise_id, item_id=material['item_id'],
								alternate_unit_id=material['alternate_unit_id'])
							if scale_factor:
								material['dc_qty'] = float(material['dc_qty']) / float(scale_factor) if material['dc_qty'] else 0
								material['rec_qty'] = float(material['rec_qty']) / float(scale_factor) if material['rec_qty'] else 0
								material['acc_qty'] = float(material['acc_qty']) / float(scale_factor) if material['acc_qty'] else 0
								material['order_qty'] = float(material['order_qty']) / float(scale_factor) if material['order_qty'] else 0
								material['pending_qty'] = float(material['pending_qty']) / float(scale_factor) if material['pending_qty'] else 0
								material['inv_rate'] = float(material['inv_rate']) * float(scale_factor) if material['inv_rate'] else 0
								material['po_price'] = float(material['po_price']) * float(scale_factor) if material['po_price'] else 0
								material['approved_rate'] = float(material['approved_rate']) * float(scale_factor) if material['approved_rate'] else 0
								material['store_price'] = float(material['store_price']) * float(scale_factor) if material['store_price'] else 0
								material['unit_name'] = helper.getUnitName(enterprise_id=enterprise_id, unit_id=material['alternate_unit_id'])
								material['scale_factor'] = scale_factor if scale_factor else 1
		except Exception as e:
			grn_materials = ""
			logger.exception("Fetching grn materials failed %s" % e.message)
		return grn_materials

	def getManualNoteList(self, enterprise_id=None, frmdate=None, todate=None, party_id=None, project_code=None, status=None):
		json_receipts = []
		try:
			query = self.audit_dao.db_session.query(
				CreditDebitNote.created_on.label('receipt_date'),
				CreditDebitNote.id.label('note_id'),
				Receipt.receipt_no.label('receipt_no'),
				CreditDebitNote.sub_number.label('sub_number'),
				CreditDebitNote.inv_no.label('invoice_no'),
				CreditDebitNote.inv_date.label('invoice_date'),
				CreditDebitNote.inv_value.label('invoice_value'),
				CreditDebitNote.status.label('status'),
				CreditDebitNote.receipt_code.label('receipt_code'),
				CreditDebitNote.financial_year.label('financial_year'),
				Project.name.label('project_name'), Party.name.label('supplier_name'),
				CreditDebitNote.value.label('note_value'), CreditDebitNote.is_credit.label('note_type'),
				CreditDebitNote.code.label('note_code'), CreditDebitNote.financial_year.label('note_financial_year'),
				CreditDebitNote.sub_number.label("note_sub_number"),
				Receipt.financial_year.label('receipt_financial_year'),
				Receipt.receipt_code.label('receipt_receipt_code'),
				Receipt.sub_number.label('receipt_sub_number'),
				Receipt.received_against.label('receipt_received_against'),
				Receipt.receipt_no.label('receipt_receipt_no')
			).outerjoin(
				NoteReceiptMap, and_(NoteReceiptMap.note_id == CreditDebitNote.id)).outerjoin(
				Receipt, and_(NoteReceiptMap.receipt_no == Receipt.receipt_no)).outerjoin(
				Project, and_(Project.id == CreditDebitNote.project_code, Project.enterprise_id == CreditDebitNote.enterprise_id)).outerjoin(
				Party, and_(Party.id == CreditDebitNote.party_id))
			if project_code != '0':
				query = query.filter(CreditDebitNote.project_code == project_code)
			if party_id and int(party_id) != -1:
				query = query.filter(CreditDebitNote.party_id == party_id)
			if status != 0:
				if status == 2:
					query = query.filter(CreditDebitNote.status.in_([status, 6]))
				elif status == 5:
					query = query.filter(CreditDebitNote.status.in_([status, -2]))
				else:
					query = query.filter(CreditDebitNote.status == status)

			query = query.filter(
				CreditDebitNote.created_on >= frmdate, CreditDebitNote.created_on <= todate,
				CreditDebitNote.enterprise_id == enterprise_id).group_by(CreditDebitNote.id)
			receipts = query.all()
			for receipt in receipts:
				receipt_json = dict()
				receipt_json['grn_date'] = receipt.receipt_date.strftime("%d-%m-%Y") if receipt.receipt_date else ""
				receipt_json['grn_no'] = receipt.receipt_no
				receipt_json['note_id'] = receipt.note_id
				receipt_json['status'] = receipt.status
				receipt_json['inv_no'] = receipt.invoice_no
				receipt_json['inv_date'] = receipt.invoice_date.strftime("%d-%m-%Y") if receipt.invoice_date else ""
				receipt_json['inv_value'] = receipt.invoice_value if receipt.invoice_value else 0
				receipt_json['status'] = receipt.status
				receipt_json['rec_against'] = 'Note'
				receipt_json['receipt_code'] = receipt.receipt_code
				receipt_json['code'] = receipt.receipt_code
				receipt_json['project'] = receipt.project_name if receipt.project_name else "-NA-"
				receipt_json['supplier'] = receipt.supplier_name
				receipt_json['note_code'] = CreditDebitNote.generateNoteCode(
					financial_year=receipt.note_financial_year, code=receipt.note_code, sub_number=receipt.sub_number)
				receipt_json['note_amt'] = receipt.note_value if receipt.note_value else 0
				receipt_json['note_type'] = receipt.note_type
				receipt_json['grn_code'] = Receipt.generateReceiptCode(
					financial_year=receipt.receipt_financial_year,
					received_against=receipt.receipt_received_against,
					receipt_no=receipt.receipt_receipt_no, receipt_code=receipt.receipt_receipt_code,
					sub_number=receipt.receipt_sub_number) if receipt.receipt_receipt_no else ""
				json_receipts.append(receipt_json)

		except Exception as e:
			json_receipts = []
			logger.exception("Fetching grn materials failed %s" % e.message)
		return json_receipts

	def getICDList(
			self, enterprise_id=None, module_access=None, credit_debit=None, frmdate=None, todate=None, party_id=None,
			project_code=None, status=None, icd_request_acknowledgement=None):
		json_receipts = []
		try:
			query = self.audit_dao.db_session.query(
				Receipt.receipt_date.label('receipt_date'),
				Receipt.receipt_no.label('receipt_no'),
				CreditDebitNote.id.label('note_id'),
				Receipt.sub_number.label('sub_number'),
				Receipt.invoice_no.label('invoice_no'),
				Receipt.invoice_date.label('invoice_date'),
				Receipt.invoice_value.label('invoice_value'),
				Receipt.status.label('status'),
				Receipt.received_against.label('received_against'),
				Receipt.receipt_code.label('receipt_code'),
				Receipt.financial_year.label('financial_year'),
				Receipt.invoice_type.label('invoice_type'),
				Project.name.label('project_name'),
				Project.code.label('project_code'),
				Party.name.label('supplier_name'),
				CreditDebitNote.value.label('note_value'), CreditDebitNote.is_credit.label('note_type'),
				CreditDebitNote.code.label('note_code'), CreditDebitNote.financial_year.label('note_financial_year'),
				CreditDebitNote.sub_number.label("note_sub_number"), CreditDebitNote.status.label('note_status'),
				func.sum(ReceiptMaterial.quantity).label("dc_qty"),
				func.sum(ReceiptMaterial.received_qty).label("rec_qty"),
				func.sum(ReceiptMaterial.accepted_qty).label("acc_qty"),
			).outerjoin(
				ReceiptMaterial, and_(ReceiptMaterial.receipt_no == Receipt.receipt_no)).outerjoin(
				NoteReceiptMap, and_(NoteReceiptMap.receipt_no == Receipt.receipt_no)).outerjoin(
				Project, and_(Project.id == Receipt.project_code, Project.enterprise_id == Receipt.enterprise_id)).outerjoin(
				Party, and_(Party.id == Receipt.supplier_id)).outerjoin(
				CreditDebitNote, and_(CreditDebitNote.id == NoteReceiptMap.note_id))

			if project_code != '0':
				query = query.filter(Receipt.project_code == project_code)
			if party_id and int(party_id) != -1:
				query = query.filter(Receipt.supplier_id == party_id)
			if status != 0:
				if status == 2 and icd_request_acknowledgement:
					query = query.filter(CreditDebitNote.status.in_([status, 6]), or_(Receipt.status == status))
				elif status == 5 and icd_request_acknowledgement:
					query = query.filter(CreditDebitNote.status.in_([status, -2]), or_(Receipt.status == status))
				elif status == 7:
					query = query.filter(CreditDebitNote.status == status)
				else:
					query = query.filter(Receipt.status == status)

			status = 2 if credit_debit else 1
			rec_agnst_grn = ['Purchase Order', 'Job Work', 'Sales Return']
			rec_aganst_ian = ['Note', 'Delivery Challan'] if credit_debit else ['Delivery Challan']
			if not module_access and credit_debit:
				rec_agnst_grn = []
				rec_aganst_ian = ['Note']
			query = query.filter(or_(and_(
				Receipt.received_against.in_(rec_agnst_grn)
				, Receipt.invoice_type == 2, Receipt.status != 4), Receipt.received_against.in_(rec_aganst_ian)),
				or_(Receipt.status >= status, and_(Receipt.received_against == 'Note', Receipt.status >= 1))
				, Receipt.receipt_date >= frmdate, Receipt.receipt_date <= todate,
				Receipt.enterprise_id == enterprise_id).group_by(Receipt.receipt_no)
			receipts = query.all()
			for receipt in receipts:
				reject_material = 0
				if receipt.received_against == "Delivery Challan":
					if receipt.dc_qty is not None and receipt.rec_qty is not None:
						reject_material += receipt.dc_qty - receipt.rec_qty
						reject_material += receipt.rec_qty - receipt.acc_qty
				receipt_json = dict()
				receipt_json['grn_date'] = receipt.receipt_date.strftime("%d-%m-%Y")
				receipt_json['grn_no'] = receipt.receipt_no
				receipt_json['note_id'] = receipt.note_id
				receipt_json['inv_no'] = receipt.invoice_no
				receipt_json['inv_date'] = receipt.invoice_date.strftime("%d-%m-%Y")
				receipt_json['inv_value'] = float(receipt.invoice_value)
				receipt_json['status'] = receipt.status
				receipt_json['rec_against'] = receipt.received_against
				receipt_json['receipt_code'] = receipt.receipt_code
				receipt_json['code'] = Receipt.generateReceiptCode(
					financial_year=receipt.financial_year, received_against=receipt.received_against,
					receipt_no=receipt.receipt_no, receipt_code=receipt.receipt_code, sub_number=receipt.sub_number)
				receipt_json['grn_code'] = ''
				# Relative table fields
				if receipt.project_code and receipt.project_name:
					receipt_json['project'] = receipt.project_name + " (" + receipt.project_code + ")"
				elif receipt.project_name:
					receipt_json['project'] = receipt.project_name
				else:
					receipt_json['project'] = "-NA-"
				receipt_json['supplier'] = receipt.supplier_name
				if receipt.note_code is None:
					receipt_json['note_code'] = 0
					receipt_json['note_amt'] = 0
					receipt_json['note_type'] = ""
					receipt_json['status'] = receipt.status
				else:
					receipt_json['note_code'] = CreditDebitNote.generateNoteCode(
						financial_year=receipt.note_financial_year, code=receipt.note_code, sub_number=receipt.note_sub_number)
					receipt_json['note_amt'] = float(receipt.note_value)
					receipt_json['note_type'] = receipt.note_type
					receipt_json['status'] = receipt.note_status
				if credit_debit:
					if receipt.note_value > 0:
						if receipt.received_against == "Delivery Challan":
							if reject_material > 0:
								json_receipts.append(receipt_json)
						else:
							json_receipts.append(receipt_json)
				else:
					if receipt.received_against == "Delivery Challan":
						if reject_material > 0:
							json_receipts.append(receipt_json)
					else:
						json_receipts.append(receipt_json)
		except Exception as e:
			json_receipts = []
			logger.exception("Fetching grn materials failed %s" % e.message)
		return json_receipts

	def partyStatusNote(
			self, grn_number=None, note_id=None, party_status=None, user_id=None, enterprise_id=None, remarks=None):
		"""

		:param grn_number:
		:param note_id:
		:param party_status:
		:param user_id:
		:param enterprise_id:
		:param remarks:
		:return:
		"""
		logger.info("Note Party Status Update Triggered...")
		db_session = self.audit_dao.db_session
		db_session.begin(subtransactions=True)
		try:
			icd_service = ICDService()
			enterprise = db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
			icd_auto_gen = enterprise.setting_flags & enterprise_module_settings['icd_auto_gen_voucher'] > 0
			note_to_approve = db_session.query(CreditDebitNote).filter(CreditDebitNote.id == note_id).first()
			party_revised_on = datetime.datetime.now()
			user = db_session.query(User).filter(User.id == user_id).first()
			if note_to_approve:
				logger.info("The Status of Note :%s" % party_status)
				if party_status == -2:
					icd_service.returnNote(
						enterprise_id=enterprise_id, receipt_no=grn_number, user_id=user_id, remarks=remarks, return_status=party_status,
						note_id=note_id, db_session=db_session)
				else:
					if icd_auto_gen and grn_number != "":
						icd_service.verifyNote(enterprise_id=enterprise_id, receipt_no=grn_number, user_id=user_id, icd_remarks=remarks)
						party_status = Receipt.STATUS_ICD_VERIFIED
					else:
						note_to_approve.remarks = updateRemarks(
							json_remarks_list=note_to_approve.remarks, remarks_text=remarks, user=user)
				note_to_approve.status = party_status
				note_to_approve.party_revised_on = party_revised_on.strftime('%Y-%m-%d %H:%M:%S')

				db_session.add(note_to_approve)
			logger.debug('Note in orm session: %s' % db_session.dirty)
			db_session.commit()
			response = response_code.success()
			response['id'] = note_id
			response['status'] = party_status
		except Exception as e:
			db_session.rollback()
			raise e
		return response
