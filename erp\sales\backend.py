import copy
import json
import os
import calendar

from datetime import datetime
from itertools import zip_longest
from dateutil.relativedelta import relativedelta
from decimal import Decimal
from sqlalchemy import and_, or_
from sqlalchemy import func
from sqlalchemy.orm import make_transient

from erp import helper, dao, DEFAULT_MAKE_ID, IS_FAULTY_TRUE
from erp.accounts import PACKING_ACCOUNT_NAME, TRANSPORT_ACCOUNT_NAME, OTHER_DIRECT_INCOME_GROUP_ID, \
    OTHER_INDIRECT_EXPENSES_GROUP_ID
from erp.accounts.backend import AccountService
from erp.accounts.changelog import VoucherChangelog
from erp.admin import enterprise_module_settings
from erp.admin.backend import UserDAO, InvoiceTemplateDAO, PurchaseTemplateConfigService
from erp.commons.backend import push_notification
from erp.dao import DataAccessObject
from erp.forms import InvoiceForm, ItemTaxFormset
from erp.formsets import InvoiceMaterialFormset, InvoiceTaxFormset, TagFormset, \
    InvoiceChargesFormset
from erp.helper import getAccountGroupIDs, getProjectByProjectId, CustomJSONEncoder
from erp.masters.backend import createPartyLedgers, createTaxLedgers, MasterDAO
from erp.models import Invoice, InvoiceMaterial, InvoiceTax, InvoiceTag, InvoiceMaterialTax, \
    InvoiceCharge, InvoiceChargeTax, Ledger, VoucherParticulars, Voucher, LedgerBill, \
    VoucherTag, LedgerBillSettlement, ReceiptMaterial, Receipt, InvoiceDocument, OA, \
    OADocument, Party, OATag, Currency, Tag, Material, Make, PartyLedgerMap, UnitMaster, OAParticulars, \
    AccountGroup, MaterialMakeMap, Enterprise, SalesEstimate, MaterialSerialNo, InvoiceMaterialSerialNo, CashFlow, Project
from erp.notifications import PUSH_NOTIFICATION
from erp.sales import logger, SE_EXCLUDE_FIELD_LIST, SE_PARTICULAR_EXCLUDE_FIELD_LIST, SE_TAX_EXCLUDE_FIELD_LIST, \
    INVOICE_EXCLUDE_FIELD_LIST
from erp.sales.changelog import InvoiceChangeLog
from erp.sales.document_compiler import InvoicePDFGenerator, OrderAcknowlegdementPDFGenerator
from erp.sales.oa_document_compiler import OAPDFGenerator
from erp.stores.backend import StoresService
from erp.tags import extractTagsFromFormset, generateTagFormset, getEntityTagMaps
from util.api_util import response_code
from util.document_properties import CANCEL_WATERMARK_DOC_PATH, ELEGANT_BACKGROUND_DOC_PATH
from util.helper import constructFormInitializer, getFinancialYear, readFile, writeFile, getAbsolutePath, \
    copyDictToDict, getFormattedDocPath, getFormattedRevisedDocPath, getFinancialYearMonths, getFinancialStartEndDate, \
	getMonthStartEndDate
from erp.stores.backend import StoresDAO
from erp.dao import executeQuery
from erp.auth import PRIMARY_ENTERPRISE_ID_SESSION_KEY
from settings import SQLASession, MongoDbConnect
from erp.tasks import closing_stock_material_wise_queue_creator, process_inventory_update, update_closing_stock

INVOICE_FORM_PREFIX = 'invoice'
INVOICE_MAT_PREFIX = 'invoice_mat'
INVOICE_NON_STOCK_MAT_PREFIX = 'invoice_non_stock_mat'
INVOICE_CHARGE_PREFIX = 'invoice_charge'
INVOICE_MAT_TAX_PREFIX = 'inv_mat_tax_%s'
INVOICE_NS_ITEM_TAX_PREFIX = 'inv_ns_item_tax_%s'
INVOICE_CHARGE_TAX_PREFIX = 'invoice_charge_tax_%s'
INVOICE_TAX_PREFIX = 'invoice_tax'
INVOICE_TAG_PREFIX = 'tag'

NON_EXCISE_INV_FORM_PREFIX = 'non_excise_invoice'
NON_EXCISE_INV_MAT_PREFIX = 'non_excise_invoice_mat'
NON_EXCISE_INV_TAX_PREFIX = 'non_excise_invoice_tax'
CHARGES_INITIAL = [
	{'item_name': PACKING_ACCOUNT_NAME, 'rate': 0.00}, {'item_name': TRANSPORT_ACCOUNT_NAME, 'rate': 0.00}]

INVOICE_KEY = 'invoice'


class InvoiceVO(object):
	"""
	This Class holds both Invoice Form and Invoice Form Set
	"""

	def __init__(
			self, invoice_form=InvoiceForm(prefix=INVOICE_FORM_PREFIX, type="internal"),
			invoice_material_formset=InvoiceMaterialFormset(prefix=INVOICE_MAT_PREFIX),
			invoice_tax_formset=InvoiceTaxFormset(prefix=INVOICE_TAX_PREFIX),
			invoice_tag_formset=TagFormset(prefix=INVOICE_TAG_PREFIX),
			invoice_charges_formset=InvoiceChargesFormset(prefix=INVOICE_CHARGE_PREFIX, initial=CHARGES_INITIAL)):
		self.invoice_form = invoice_form
		self.invoice_material_formset = invoice_material_formset
		self.invoice_charges_formset = invoice_charges_formset
		self.invoice_tax_formset = invoice_tax_formset
		self.invoice_tag_formset = invoice_tag_formset

	def is_valid(self):
		try:
			is_valid = self.invoice_form.is_valid() and self.invoice_material_formset.is_valid() and self.invoice_tax_formset.is_valid() and self.invoice_charges_formset.is_valid()
			logger.info(
				"Invoice - Valid Header: %s, Valid Materials: %s,Valid Charges: %s, Valid Taxes: %s" % (
					self.invoice_form.is_valid(), self.invoice_material_formset.is_valid(),
					self.invoice_charges_formset.is_valid(),
					self.invoice_tax_formset.is_valid()))
			if not is_valid:
				logger.info("Invoice Form Errors: \n%s\n%s\n%s\n%s\n%s" % (
					self.invoice_form.errors, self.invoice_material_formset.errors,
					self.invoice_charges_formset.errors,
					self.invoice_tax_formset.errors, self.invoice_tag_formset.errors))
		except Exception as e:
			is_valid = False
			logger.exception("The Form Set Validation Error... %s" % str(e))
		return is_valid

	def __repr__(self):
		return "Invoice Form : %s\nInvoice Material Formset : %s\nInvoice Charges Formset: %s\nInvoice Tax Formset :%s\nInvoice Tag Formset :%s" % (
			self.invoice_form, self.invoice_material_formset, self.invoice_charges_formset, self.invoice_tax_formset, self.invoice_tag_formset)


class InvoiceDAO(DataAccessObject):
	"""
	Class that handles all the Purchase module related DB activities - like fetching, save and deleting persistent objects
	"""

	def getInvoice(
			self, enterprise_id=None, invoice_id=None, financial_year=None, invoice_code=None, invoice_type=None,
			invoice_no=None, sub_number=None):
		query = self.db_session.query(Invoice)
		if invoice_id:
			query = query.filter(Invoice.enterprise_id == enterprise_id, Invoice.id == invoice_id)
		elif invoice_type == 'Issue':
			query = query.filter(
				Invoice.enterprise_id == enterprise_id, Invoice.financial_year == financial_year,
				Invoice.type == invoice_type, Invoice.invoice_no == invoice_no, Invoice.sub_number == sub_number)
		elif invoice_code:
			query = query.filter(Invoice.enterprise_id == enterprise_id, Invoice.invoice_code == invoice_code)
		else:
			query = query.filter(
				Invoice.enterprise_id == enterprise_id, Invoice.financial_year == financial_year)
		return query.first()

	def getOA(self, enterprise_id=None, oa_id=None, financial_year=None, oa_type=None, oa_no=None, sub_number=None):
		"""
		enterprise_id and either oa_id or (financial_year, oa_no, sub_number) are mandatory
		:return:
		"""
		if oa_id:
			return self.db_session.query(OA).filter(OA.enterprise_id == enterprise_id, OA.id == oa_id).first()
		else:
			return self.db_session.query(OA).filter(
				OA.enterprise_id == enterprise_id, OA.financial_year == financial_year, OA.type == oa_type,
				OA.oa_no == oa_no, OA.sub_number == sub_number).first()

	def getSalesEstimate(
			self, se_id=None, enterprise_id=None, financial_year=None, se_type=None, se_no=None, sub_number=None):
		query = self.db_session.query(SalesEstimate)
		if se_id:
			query = query.filter(SalesEstimate.enterprise_id == enterprise_id, SalesEstimate.id == se_id)
		else:
			query = query.filter(
				SalesEstimate.enterprise_id == enterprise_id, SalesEstimate.financial_year == financial_year,
				SalesEstimate.type == se_type, SalesEstimate.se_no == se_no, SalesEstimate.sub_number == sub_number)
		return query.first()

	def getOACode(self, grn_no=None, item_id=None, enterprise_id=None, make_id=None, is_faulty=None):
		"""

		:param grn_no:
		:param item_id:
		:param enterprise_id:
		:param make_id:
		:param is_faulty:
		:return:
		"""
		query = self.db_session.query(ReceiptMaterial.oa_id)
		filters = [ReceiptMaterial.enterprise_id == enterprise_id, ReceiptMaterial.item_id == item_id,
					ReceiptMaterial.receipt_no == grn_no]
		if is_faulty:
			filters.append(ReceiptMaterial.is_faulty == is_faulty)
		if make_id:
			filters.append(ReceiptMaterial.make_id == make_id)
		result = query.filter(*filters).first()
		if not result or not result[0]:
			return None
		return int(result[0])

	def getLatestInvoiceCode(self, financial_year=None, enterprise_id=None):
		"""
		Generate the new Invoice code, combined with the financial year.
		As per requirement the numeric part of code is to be recycled every year.
		:param financial_year:
		:param enterprise_id:
		:return:
		"""
		financial_year = financial_year if financial_year else datetime.now()
		latest_invoice_id = len(self.db_session.query(Invoice.id).filter(
			Invoice.financial_year == financial_year, Invoice.enterprise_id == enterprise_id).all())
		logger.info('Latest Invoice Code: %s' % (
			1 if not ('%s' % latest_invoice_id).isdigit() else (int('%s' % latest_invoice_id) + 1)))
		return 1 if not ('%s' % latest_invoice_id).isdigit() else (int('%s' % latest_invoice_id) + 1)

	def getInvoiceMaterial(self, invoice_id, item_id, enterprise_id, make_id, is_faulty, oa_no, dc_no, grn_no):
		return self.db_session.query(InvoiceMaterial).filter(
			InvoiceMaterial.invoice_id == invoice_id, InvoiceMaterial.item_id == item_id,
			InvoiceMaterial.make_id == make_id, InvoiceMaterial.is_faulty == is_faulty,
			InvoiceMaterial.oa_no == oa_no, InvoiceMaterial.delivered_dc_id == dc_no,
			InvoiceMaterial.receipt_no == grn_no, InvoiceMaterial.enterprise_id == enterprise_id).first()

	def getInvoiceMaterialTax(self, invoice_id, item_id, enterprise_id, tax_code, make_id, is_faulty, oa_no, dc_no, grn_no):
		return self.db_session.query(InvoiceMaterialTax).filter(
			InvoiceMaterialTax.invoice_id == invoice_id, InvoiceMaterialTax.item_id == item_id,
			InvoiceMaterialTax.tax_code == tax_code, InvoiceMaterialTax.make_id == make_id,
			InvoiceMaterialTax.is_faulty == is_faulty, InvoiceMaterialTax.oa_no == oa_no,
			InvoiceMaterialTax.delivered_dc_id == dc_no, InvoiceMaterialTax.receipt_no == grn_no,
			InvoiceMaterialTax.enterprise_id == enterprise_id).first()

	def getInvoiceCharge(self, invoice_id, item_name, enterprise_id):
		return self.db_session.query(InvoiceCharge).filter(
			InvoiceCharge.invoice_id == invoice_id, InvoiceCharge.item_name == item_name,
			InvoiceCharge.enterprise_id == enterprise_id).first()

	def getInvoiceChargeTax(self, invoice_id, item_name, tax_code, enterprise_id):
		return self.db_session.query(InvoiceChargeTax).filter(
			InvoiceChargeTax.invoice_id == invoice_id, InvoiceChargeTax.item_name == item_name,
			InvoiceChargeTax.tax_code == tax_code, InvoiceChargeTax.enterprise_id == enterprise_id).first()

	def getInvoiceTax(self, invoice_id, tax_code, enterprise_id):
		return self.db_session.query(InvoiceTax).filter(
			InvoiceTax.invoice_id == invoice_id, InvoiceTax.tax_code == tax_code,
			InvoiceTax.enterprise_id == enterprise_id).first()

	def checkInvoice(self, enterprise_id=None, invoice_id=None):
		count = self.db_session.query(ReceiptMaterial).join(ReceiptMaterial.receipt).filter(
			ReceiptMaterial.dc_id == invoice_id, ReceiptMaterial.enterprise_id == enterprise_id,
			Receipt.status > -1).count()
		if count == 0:
			count = self.db_session.query(InvoiceMaterial).join(InvoiceMaterial.delivered_dc).filter(
				InvoiceMaterial.delivered_dc_id == invoice_id, InvoiceMaterial.enterprise_id == enterprise_id,
				Invoice.status > -1).count()
		return count

	def getPendingInvoiceCount(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""

		try:
			invoice = self.db_session.query(Invoice).filter(
				Invoice.type.in_(Invoice.TYPES["sales"]), Invoice.enterprise_id == enterprise_id, Invoice.status == 0).all()
			if not invoice:
				return 0
			return len(invoice)
		except Exception as e:
			raise

	def getPendingDeliveryChallanCount(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		try:
			count = self.db_session.query(Invoice).filter(
				Invoice.type.in_(Invoice.TYPES["dc"]), Invoice.enterprise_id == enterprise_id, Invoice.status == 0).all()
			if not count:
				return 0
			return len(count)
		except Exception as e:
			raise

	def getPendingOACount(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		try:
			count = self.db_session.query(OA).filter(OA.enterprise_id == enterprise_id, OA.status == 0).all()
			if not count:
				return 0
			return len(count)
		except Exception as e:
			raise

	def getPendingVoucherCount(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		try:
			count = self.db_session.query(
				Voucher).filter(Voucher.enterprise_id == enterprise_id, Voucher.status == Voucher.DRAFT).all()
			if not count:
				return 0
			return len(count)
		except Exception as e:
			raise

	def getCustomerDetails(self, enterprise_id=None, party_id=None):
		"""

		:param enterprise_id:
		:param party_id:
		:return:
		"""
		try:
			return self.db_session.query(Party).filter(
				Party.enterprise_id == enterprise_id, Party.id == party_id).first()
		except Exception as e:
			raise

	def getPendingSalesEstimateCount(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		try:
			count = self.db_session.query(SalesEstimate).filter(
				SalesEstimate.enterprise_id == enterprise_id, SalesEstimate.status == 0).all()
			if not count:
				return 0
			return len(count)
		except Exception as e:
			raise

	def getLatestInvoiceNoforType(self, inv_number_format=None, issue=None, current_fy=None, db_session=None):
		"""

		:param inv_number_format:
		:param issue:
		:param current_fy:
		:param db_session:
		:return:
		"""
		try:
			if inv_number_format.__contains__("type") and not inv_number_format.__contains__("type:.0"):
				based_on_type = True
			else:
				based_on_type = False
			if issue.type in ('DC', 'JDC', 'JIN'):
				types = ('DC', 'JDC', 'JIN')
			elif issue.type == 'Issue':
				types = 'Issue'
			else:
				types = ('Trading', 'GST', 'Excise', 'BoS', 'Service')
			if issue.type != 'Issue':
				if based_on_type is True:
					if issue.type not in ('JDC', 'JIN'):
						latest_invoice_no = db_session.query(func.max(Invoice.invoice_no)).filter(
							Invoice.financial_year == current_fy, Invoice.enterprise_id == issue.enterprise_id,
							Invoice.id != '0', Invoice.type == issue.type).first()
					else:
						latest_invoice_no = db_session.query(func.max(Invoice.invoice_no)).filter(
							Invoice.financial_year == current_fy, Invoice.enterprise_id == issue.enterprise_id,
							Invoice.id != '0', Invoice.type.in_(('JDC', 'JIN'))).first()
				else:
					latest_invoice_no = db_session.query(func.max(Invoice.invoice_no)).filter(
						Invoice.financial_year == current_fy, Invoice.enterprise_id == issue.enterprise_id,
						Invoice.id != '0', Invoice.type.in_(types)).first()
			else:
				latest_invoice_no = db_session.query(func.max(Invoice.invoice_no)).filter(
					Invoice.financial_year == current_fy, Invoice.enterprise_id == issue.enterprise_id,
					Invoice.id != '0', Invoice.type == 'Issue').first()
			return latest_invoice_no
		except Exception as e:
			logger.exception("Error in getting Latest Invoice No - %s" % str(e))
			raise

	def getEnterpriseSerialNo(self, enterprise_id=None, serial_no=None):
		"""

		:param enterprise_id:
		:param serial_no:
		:return:
		"""
		try:
			return self.db_session.query(MaterialSerialNo).filter(
					MaterialSerialNo.enterprise_id == enterprise_id, MaterialSerialNo.serial_number == serial_no).first()
		except Exception as e:
			logger.exception("Error in getting Enterprise Serial No - %s" % str(e))
			raise

	def getInvoiceSerialNo(self, invoice_id=None, serial_no=None):
		"""

		:param invoice_id:
		:param serial_no:
		:return:
		"""
		try:
			return self.db_session.query(InvoiceMaterialSerialNo).filter(
					InvoiceMaterialSerialNo.invoice_id == invoice_id, InvoiceMaterialSerialNo.serial_number == serial_no).first()
		except Exception as e:
			logger.exception("Error in getting Enterprise Serial No - %s" % str(e))
			raise

	def getInvoiceItemSerialNoCount(self, enterprise_id=None, invoice_id=None, item_id=None, make_id=1, is_faulty=False):
		"""

		:param enterprise_id:
		:param invoice_id:
		:param item_id:
		:param make_id:
		:param is_faulty:
		:return:
		"""
		try:
			invoice_item_serial_no_count = self.db_session.query(InvoiceMaterial).join(
				InvoiceMaterialSerialNo, (InvoiceMaterial.invoice_id == InvoiceMaterialSerialNo.invoice_id)).join(
				MaterialSerialNo, and_(
					MaterialSerialNo.serial_number == InvoiceMaterialSerialNo.serial_number,
					MaterialSerialNo.item_id == InvoiceMaterial.item_id, MaterialSerialNo.make_id == InvoiceMaterial.make_id,
					MaterialSerialNo.is_faulty == InvoiceMaterial.is_faulty, )).filter(
				InvoiceMaterial.invoice_id == invoice_id, InvoiceMaterial.enterprise_id == enterprise_id,
				InvoiceMaterial.item_id == item_id, InvoiceMaterial.make_id == make_id,
				InvoiceMaterial.is_faulty == is_faulty).count()
			return invoice_item_serial_no_count
		except Exception as e:
			logger.exception("Error in getting Enterprise Serial No - %s" % str(e))
			raise


class InvoiceService:
	def __init__(self):
		"""

		"""
		self.invoice_dao = InvoiceDAO()
		self.invoice_template_dao = InvoiceTemplateDAO()

	def constructInvoiceVo(self, invoice=Invoice(), type="", default_notes="", is_dc_edit=False, current_user=None):
		"""
		Converts the DB persistent entity into a Value Object presentable to the UI layer.

		:param invoice:
		:param type:
		:param default_notes:
		:return:
		"""
		invoice_charges = invoice.charges if invoice and invoice.charges else [
			InvoiceCharge(item_name=PACKING_ACCOUNT_NAME, enterprise_id=invoice.enterprise_id),
			InvoiceCharge(item_name=TRANSPORT_ACCOUNT_NAME, enterprise_id=invoice.enterprise_id)]
		return InvoiceVO(
			self._generateInvoiceForm(
				invoice=invoice, prefix=INVOICE_FORM_PREFIX, type=type, default_notes=default_notes, is_dc_edit=is_dc_edit,
				current_user=current_user),
			self._genereateInvoiceMaterialFormset(invoice.items),
			self._generateInvoiceTaxFormset(invoice.taxes),
			generateTagFormset(tags=invoice.tags, prefix='tag'),
			self._genereateInvoiceChargesFormset(invoice_charges=invoice_charges))

	def saveInvoiceFromVO(self, is_super_edit=False, invoice_vo=None, enterprise_id=None, user_id=None, fy_start_day='01/04',
						  mrs_type=None):
		"""

		:return:
		"""
		try:
			if invoice_vo.is_valid():
				invoice_data = self.populateInvoiceDataFromForm(invoice_vo=invoice_vo)
				self.saveInvoice(
					enterprise_id=enterprise_id, user_id=user_id, invoice_data=invoice_data,
					is_super_edit=is_super_edit, fy_start_day=fy_start_day, mrs_type=mrs_type)
		except Exception as e:
			logger.exception("Error in Save Invoice - %s" % str(e))
			pass
		return invoice_vo

	def saveInvoice(
			self, enterprise_id=None, user_id=None, invoice_data=None, is_super_edit=False, fy_start_day='01/04',
			is_device=False, mrs_type=None):
		"""

		Validate invoice data and then save in database then return the saved invoice model
		:param enterprise_id:
		:param user_id:
		:param invoice_data: dictionary data of invoice model
		:param is_super_edit:
		:param fy_start_day:
		:param is_device: Boolean value used to check whether save function has been called from mobile or web.
						True value used to save and approve the invoice.
		:return: invoice model
		"""
		db_session = self.invoice_dao.db_session
		items_before_update = []
		try:
			db_session.begin(subtransactions=True)
			user = UserDAO().getUserById(enterprise_id=enterprise_id, user_id=user_id)
			invoice_id = invoice_data['id'] if 'id' in invoice_data else None
			invoice_data['po_date'] = invoice_data['po_date'] if 'po_date' in invoice_data and invoice_data['po_date'] != "" else None
			invoice_data['order_accept_date'] = invoice_data['order_accept_date'] if 'order_accept_date' in invoice_data and invoice_data['order_accept_date'] != "" else None

			entity = self.invoice_dao.getInvoice(enterprise_id=enterprise_id, invoice_id=invoice_id if invoice_id != "" else None)
			if entity is not None:
				for item in entity.items:
					items_before_update.append(copy.deepcopy(item))
			is_entity = False
			if entity is None:
				entity = Invoice(enterprise_id=enterprise_id)
			[db_session.delete(tag) for tag in entity.tags]

			invoice_to_be_saved = self._copyInvoiceDataToEntity(invoice_data=invoice_data, entity=entity)
			if len(invoice_to_be_saved.items) == 0:
				raise Exception("Materials not added to the generated invoice object")
			if isinstance(entity.remarks, list):
				entity.updateRemarks(remarks=invoice_data['remarks'], user=user)
			elif invoice_data['remarks'] != '':
				entity.remarks = ([dict(
					date=datetime.today().strftime("%Y-%m-%d %H:%M:%S"),
					remarks=str(invoice_data['remarks']).replace(
						'\n', '<BR/>'), by="%s" % user)])
			if entity.type in ("Issue", "Internal") and (entity.invoice_no == 0 or not entity.invoice_no):
				inv_config_details = self.invoice_template_dao.getInvoiceTemplateConfig(
					enterprise_id=enterprise_id, module="Sales")
				inv_number_format = inv_config_details.template_header_details.inv_number_format
				self._prepareIssueForApproval(
					issue=entity, db_session=db_session, approved_by=user_id,
					fy_start_day=fy_start_day, inv_number_format=inv_number_format)
			if entity.type in ("Issue", "Internal"):
				invoice_to_be_saved.approved_on = datetime.now()
				invoice_to_be_saved.approved_by = user_id

			if entity.id is None or entity.id == '':
				is_entity = True
				invoice_to_be_saved.prepared_on = datetime.now()
				invoice_to_be_saved.prepared_by = user_id
			logger.info('Invoice to be saved: %s' % entity.type)
			if is_super_edit is True:
				invoice_to_be_saved.super_modified_by = user_id
				invoice_to_be_saved.super_modified_on = datetime.now()

			purchase_order_type = None
			if invoice_to_be_saved.job_po:
				purchase_order_type = invoice_to_be_saved.job_po.type

			if (purchase_order_type != 2 and (invoice_to_be_saved.type == "JDC" or int(invoice_to_be_saved.job_po_id) == 0) or
			(not invoice_to_be_saved.job_po_id > 0 and not invoice_to_be_saved.type == "Issue") or int(mrs_type) == 1) and \
					not invoice_to_be_saved.goods_already_supplied:

				if invoice_id:
					if invoice_to_be_saved.status in (
					Invoice.STATUS_DRAFT, Invoice.STATUS_APPROVED) and not invoice_to_be_saved.goods_already_supplied:

						old = [{"item_id": i.item_id, "quantity": float(i.quantity), "is_faulty": i.is_faulty,
								"enterprise_id": i.enterprise_id} for i in items_before_update]
						new = [{"item_id": i.item_id, "quantity": float(i.quantity), "is_faulty": i.is_faulty,
								"enterprise_id": i.enterprise_id, "is_delete": i.is_delete} for i in
							   invoice_to_be_saved.items]

						for old_item, new_item in zip_longest(old, new, fillvalue=0):
							if old_item != 0 and not new_item["is_delete"] and old_item["quantity"] != new_item["quantity"]:

								if old_item["quantity"] < new_item["quantity"]:
									qty = abs(old_item["quantity"] - new_item["quantity"])
									update_closing_stock.delay(enterprise_id=enterprise_id, item_id=new_item["item_id"],
															   is_sales=True, quantity=float(qty),
															   location_id=invoice_to_be_saved.location_id, is_faulty=int(new_item["is_faulty"]))
								else:
									qty = abs(old_item["quantity"] - new_item["quantity"])
									update_closing_stock.delay(enterprise_id=enterprise_id, item_id=new_item["item_id"],
															   is_sales=False, quantity=float(qty),
															   location_id=invoice_to_be_saved.location_id, is_faulty=int(new_item["is_faulty"]))
							elif old_item == 0 and not new_item["is_delete"]:
								update_closing_stock.delay(enterprise_id=enterprise_id, item_id=new_item["item_id"],
														   is_sales=True, quantity=float(new_item["quantity"]),
														   location_id=invoice_to_be_saved.location_id, is_faulty=int(new_item["is_faulty"]))
							elif new_item["is_delete"]:
								update_closing_stock.delay(enterprise_id=enterprise_id, item_id=new_item["item_id"],
														   is_sales=False, quantity=float(new_item["quantity"]),
														   location_id=invoice_to_be_saved.location_id, is_faulty=int(new_item["is_faulty"]))
				else:
					for item in invoice_to_be_saved.items:
						if not item.is_delete :
							update_closing_stock.delay(enterprise_id=enterprise_id, item_id=item.item_id, is_sales=True,
													   quantity=float(item.quantity),
													   location_id=invoice_to_be_saved.location_id, is_faulty=int(item.is_faulty))

			invoice_to_be_saved.items = [item for item in invoice_to_be_saved.items if not getattr(item, 'is_delete', False)]
			invoice_to_be_saved.last_modified_by = user_id
			invoice_to_be_saved.last_modified_on = datetime.now()
			db_session.add(invoice_to_be_saved)
			db_session.commit()
			db_session.refresh(invoice_to_be_saved)
			InvoiceChangeLog().queryInsert(userid=user_id, enterprise_id=enterprise_id, data=invoice_to_be_saved)
			if is_device is True and invoice_data['status'] == 1:
				self.approveInvoice(invoice_id=invoice_to_be_saved.id, approved_by=user_id, enterprise_id=enterprise_id)
		except Exception as e:
			logger.exception(str(e))
			db_session.rollback()
			raise e
		if (is_device is False and entity.type == "GST") or (
				is_device is True and entity.type == "GST" and invoice_data['status'] == 0):
			if invoice_id is None:
				self.notifyPendingInVoiceApprovalCount(
					enterprise_id=enterprise_id, sender_id=user_id,
					code=invoice_to_be_saved.getCode(), type=invoice_to_be_saved.type)
		if entity.type in Invoice.TYPES["dc"]:
			if invoice_id is None:
				self.notifyPendingDeliverChallanCount(
					enterprise_id=enterprise_id, sender_id=user_id,
					code=invoice_to_be_saved.getCode(), type=entity.type)
		if entity.type in ("Issue", "Internal") and is_entity:
			self.notifySaveIssueCount(enterprise_id=enterprise_id, sender_id=user_id, issue=invoice_to_be_saved)

		return invoice_to_be_saved


	def _prepareIssueForApproval(
			self, issue=None, db_session=None, approved_on=None, approved_by=None, fy_start_day='01/04', inv_number_format=None):
		"""
		Prepare Issue for approval - Set values for:
		- Issue Code, Approver, Approved On

		:param issue:
		:param db_session:
		:param approved_on:
		:param approved_by:
		:param fy_start_day:
		:return:
		"""
		if not approved_on:
			approved_on = datetime.now()
		current_fy = getFinancialYear(for_date=approved_on, fy_start_day=fy_start_day)

		if issue.type in ('DC', 'JDC', 'JIN'):
			types = ('DC', 'JDC', 'JIN')
		elif issue.type == 'Issue':
			types = 'Issue'
		else:
			types = ('Trading', 'GST', 'Excise', 'BoS', 'Service')

		latest_invoice_no = self.invoice_dao.getLatestInvoiceNoforType(
			inv_number_format=inv_number_format, issue=issue, current_fy=current_fy, db_session=db_session)
		new_invoice_no = 0
		if latest_invoice_no and latest_invoice_no[0] is not None:
			new_invoice_no = int(latest_invoice_no[0])
		new_invoice_no += 1
		logger.info('Recent Issue No & Type: %s' % latest_invoice_no)
		issue.invoice_no = '%s' % new_invoice_no
		issue.status = Invoice.STATUS_APPROVED
		issue.approved_on = approved_on.strftime('%Y-%m-%d %H:%M:%S')
		issue.financial_year = current_fy
		issue.approved_by = approved_by
		issue.invoice_code = issue.generateInternalCode(
			invoice_id=issue.id, invoice_no=issue.invoice_no, invoice_type=issue.type, financial_year=issue.financial_year,
			temp_title="PROFORMA", sub_number=issue.sub_number, inv_number_format=inv_number_format, invoice_code=issue.invoice_code)
		if issue.type != 'Issue':
			self.validateGeneratedInvoiceCode(issue=issue, types=types, inv_number_format=inv_number_format)

	def validateGeneratedInvoiceCode(self, issue=None, types=None, inv_number_format=None):
		try:
			logger.info("Validating generated invoice code for duplicate code")
			existing_invoice = self.invoice_dao.db_session.query(Invoice).filter(
				Invoice.enterprise_id == issue.enterprise_id, Invoice.financial_year == issue.financial_year,
				Invoice.type.in_(types), Invoice.invoice_code == issue.invoice_code).first()
			new_invoice_no = int(issue.invoice_no)
			while existing_invoice:
				new_invoice_no += 1
				new_invoice_code = issue.generateInternalCode(
					invoice_id=issue.id, invoice_no=new_invoice_no, invoice_type=issue.type,
					financial_year=issue.financial_year,
					temp_title="PROFORMA", sub_number=issue.sub_number, inv_number_format=inv_number_format,
					invoice_code=None)
				issue.invoice_no = new_invoice_no
				issue.invoice_code = new_invoice_code
				existing_invoice = self.invoice_dao.db_session.query(Invoice).filter(
					Invoice.enterprise_id == issue.enterprise_id, Invoice.financial_year == issue.financial_year,
					Invoice.type.in_(types), Invoice.invoice_code == new_invoice_code).first()
		except Exception as e:
			logger.exception(str(e))

	def approveInvoice(
			self, invoice_id=None, remarks=None, enterprise_id=None, approved_by=None, isprimary_project=True):
		"""

		:return:
		"""
		logger.info('Invoice Approval - ID: %s' % Invoice.type)
		db_session = self.invoice_dao.db_session
		db_session.begin(subtransactions=True)
		is_approve_first_time = False
		try:
			invoice_to_approve = db_session.query(Invoice).filter(Invoice.id == invoice_id).first()
			logger.debug("invoice to approve : %s" % invoice_to_approve)
			if (invoice_to_approve.status == -1 and (invoice_to_approve.invoice_no and invoice_to_approve.invoice_no != '0')
					and not invoice_to_approve.goods_already_supplied):
				closing_stock_material_wise_queue_creator.delay(
					item_list=[{"item_id": i.item_id, "quantity": float(i.quantity), "is_faulty": i.is_faulty,
								"enterprise_id": i.enterprise_id} for i in invoice_to_approve.items], is_sales=True,
					location_id=invoice_to_approve.location_id)
			if invoice_to_approve.status != -1 and (
						invoice_to_approve.invoice_no and invoice_to_approve.invoice_no != '0'):
				db_session.rollback()
				return invoice_to_approve, False
			if invoice_to_approve.invoice_no == 0 or not invoice_to_approve.invoice_no:
				inv_number_format = invoice_to_approve.enterprise.inv_template_config.template_header_details.inv_number_format
				self._prepareIssueForApproval(
					issue=invoice_to_approve, db_session=db_session, approved_by=approved_by,
					fy_start_day=invoice_to_approve.enterprise.fy_start_day, inv_number_format=inv_number_format)
				is_approve_first_time = True
				# ##Create Internal Invoice
				# internal_inv_service  = InternalInvoiceService()
				# internal_inv_service.construct_internal_invoice(enterprise_id=enterprise_id, invoice_id=invoice_id, user_id = approved_by)
			else:
				# This case will happen on Super edit
				invoice_to_approve.status = Invoice.STATUS_APPROVED
				invoice_to_approve.super_modified_by = approved_by
				invoice_to_approve.super_modified_on = datetime.now()
				invoice_to_approve.approved_on = datetime.now()
				invoice_to_approve.approved_by = approved_by
			if not invoice_to_approve.issued_on:
				invoice_to_approve.issued_on = invoice_to_approve.approved_on
			modifying_user = UserDAO().getUserById(enterprise_id=enterprise_id, user_id=approved_by)
			invoice_to_approve.updateRemarks(remarks=remarks, user=modifying_user)
			invoice_to_approve.enterprise_id = enterprise_id
			invoice_to_approve.last_modified_by = approved_by
			invoice_to_approve.last_modified_on = datetime.now()
			db_session.add(invoice_to_approve)
			db_session.commit()
			enterprise = db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
			logger.info('invoice_approved: %s' % invoice_to_approve.type)
			scrutiny_flag = enterprise.setting_flags & enterprise_module_settings['scrutiny_flag'] > 0
			if invoice_to_approve.type in Invoice.TYPES["sales"]:
				self.__generateInvoiceApprovalVouchersAndBills(
					invoice_to_approve, db_session, approved_by=approved_by, is_scrutiny=scrutiny_flag,
					enterprise=enterprise, isprimary_project=isprimary_project)
				self.notifyInvoiceApproveCount(
					enterprise_id=enterprise_id, sender_id=approved_by, invoice=invoice_to_approve)
			elif invoice_to_approve.type in Invoice.TYPES["dc"]:
				self.notifyDeliveryChallanApproveCount(
					enterprise_id=enterprise_id, sender_id=approved_by, invoice=invoice_to_approve)
			if is_approve_first_time:
				##Create Internal Invoice
				internal_inv_service  = InternalInvoiceService()
				internal_inv_service.construct_internal_invoice(enterprise_id=enterprise_id, invoice_id=invoice_id, user_id = approved_by)
			# if not invoice_to_approve.goods_already_supplied:
			# 	closing_stock_material_wise_queue_creator.delay(
			# 		item_list=[{"item_id": i.item_id, "quantity": float(i.quantity), "is_faulty": i.is_faulty,
			# 					"enterprise_id": i.enterprise_id} for i in invoice_to_approve.items], is_sales=True,
			# 		location_id=invoice_to_approve.location_id)
			return invoice_to_approve, True
		except Exception as e:
			db_session.rollback()
			logger.exception('Error in Invoice Approval: %s' % e)
			return None

	def rejectInvoice(self, invoice_id=None, rejection_remarks="", rejected_by=None, enterprise_id=None):
		"""

		:param invoice_id:
		:param rejection_remarks:
		:param rejected_by:
		:param enterprise_id:
		:return:
		"""
		logger.info("Invoice ID%s" % invoice_id)
		db_session = self.invoice_dao.db_session
		db_session.begin(subtransactions=True)
		try:
			invoice_to_be_rejected = db_session.query(Invoice).filter(Invoice.id == invoice_id).first()
			if invoice_to_be_rejected.status == Invoice.STATUS_DRAFT:
				invoice_to_be_rejected.status = Invoice.STATUS_REJECTED
				invoice_to_be_rejected.updateRemarks(remarks=rejection_remarks, user=rejected_by)
				logger.info("Removing the Invoice %s permanently" % invoice_to_be_rejected.approved_by)
				db_session.commit()
				if not invoice_to_be_rejected.goods_already_supplied:
						closing_stock_material_wise_queue_creator.delay(
							item_list=[{"item_id": i.item_id, "quantity": float(i.quantity), "is_faulty": i.is_faulty,
										"enterprise_id": i.enterprise_id} for i in invoice_to_be_rejected.items],
							is_sales=False, location_id=invoice_to_be_rejected.location_id)
				if invoice_to_be_rejected.type == "DC" or invoice_to_be_rejected.type == "JDC":
					self.notifyDeliveryChallanRejectCount(
						enterprise_id=enterprise_id, sender_id=rejected_by, invoice=invoice_to_be_rejected)
				else:
					self.notificationInvoiceRejectCount(
						enterprise_id=enterprise_id, sender_id=rejected_by,
						invoice=invoice_to_be_rejected)
				return 0
			invoice_to_be_rejected.status = Invoice.STATUS_CANCELLED
			modifying_user = UserDAO().getUserById(enterprise_id=enterprise_id, user_id=rejected_by)
			invoice_to_be_rejected.updateRemarks(remarks=rejection_remarks, user=modifying_user)
			logger.debug("Invoice Reject Status %s" % invoice_to_be_rejected)
			invoice_to_be_rejected.last_modified_by = rejected_by
			invoice_to_be_rejected.last_modified_on = datetime.now()
			db_session.add(invoice_to_be_rejected)
			db_session.commit()
			enterprise = db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
			scrutiny_flag = enterprise.setting_flags & enterprise_module_settings['scrutiny_flag'] > 0
			if invoice_to_be_rejected.type in Invoice.TYPES["sales"]:
				self.__generateInvoiceRejectionVouchersAndBills(
					invoice_to_be_rejected, db_session=db_session, is_scrutiny=scrutiny_flag, user_id=rejected_by,
					enterprise=enterprise)

			if invoice_to_be_rejected.type == "DC" or invoice_to_be_rejected.type == "JDC":
				self.notifyDeliveryChallanRejectCount(
					enterprise_id=enterprise_id, sender_id=rejected_by, invoice=invoice_to_be_rejected)
			else:
				self.notificationInvoiceRejectCount(
					enterprise_id=enterprise_id, sender_id=rejected_by, invoice=invoice_to_be_rejected)
			if not invoice_to_be_rejected.goods_already_supplied:
				closing_stock_material_wise_queue_creator.delay(
					item_list=[{"item_id": i.item_id, "quantity": float(i.quantity), "is_faulty": i.is_faulty,
								"enterprise_id": i.enterprise_id} for i in invoice_to_be_rejected.items], is_sales=False,
					location_id=invoice_to_be_rejected.location_id)
			return invoice_to_be_rejected.getSimpleCode()
		except Exception as e:
			logger.exception(str(e))
			db_session.rollback()
			return "Rejection Failed!!"

	def __constructSalesVoucher(self, invoice=Invoice(), narration="", db_session=None, for_rejection=False, is_scrutiny=None,
	                            user_id=None, enterprise=None):
		"""

		:param invoice:
		:param narration:
		:param db_session:
		:param for_rejection:
		:return:
		"""
		account_service = AccountService()
		voucher_particulars = []
		voucher_tags = []
		item_no = 1
		self.generateRequiredLedgers(invoice=invoice, db_session=db_session)
		voucher_value = Decimal(invoice.getTotalMaterialValue()) * Decimal(invoice.currency_conversion_rate)
		voucher_particulars.append(VoucherParticulars(
			item_no=item_no, ledger_id=invoice.sale_account_id, is_debit=for_rejection, amount=round(voucher_value, 2),
			enterprise_id=invoice.enterprise_id))

		for _charge in invoice.charges:
			item_no += 1
			_charge_ledger = db_session.query(Ledger).filter(
				Ledger.group_id == OTHER_DIRECT_INCOME_GROUP_ID, Ledger.name == _charge.item_name,
				Ledger.enterprise_id == invoice.enterprise_id).first()
			_charge_voucher_value = Decimal(round(_charge.rate * (100 - _charge.discount) / 100, 2)) * Decimal(
				invoice.currency_conversion_rate)
			if _charge_voucher_value > 0:
				voucher_particulars.append(VoucherParticulars(
					item_no=item_no, ledger_id=_charge_ledger.id, is_debit=for_rejection,
					amount=round(_charge_voucher_value, 2), enterprise_id=invoice.enterprise_id))

		tax_values = invoice.getTaxValues()
		for tax in invoice.getConsolidatedTaxList():
			tax_ledger_id = tax.getOutputLedgerId()
			logger.info("Tax Ledger: %s Amount: %s" % (tax_ledger_id, tax_values[tax.code]))
			item_no += 1
			tax_value = Decimal(tax_values[tax.code]) * Decimal(invoice.currency_conversion_rate)
			voucher_particulars.append(VoucherParticulars(
				item_no=item_no, ledger_id=tax_ledger_id, is_debit=for_rejection, amount=round(tax_value, 2),
				enterprise_id=invoice.enterprise_id))

		logger.info("Party Ledger: %s" % invoice.customer.getCustomerLedger().id)
		item_no += 1
		voucher_particulars.append(VoucherParticulars(
			item_no=item_no, ledger_id=invoice.customer.getCustomerLedger().id, is_debit=(not for_rejection),
			amount=round(Decimal(invoice.grand_total) * Decimal(invoice.currency_conversion_rate), 2),
			enterprise_id=invoice.enterprise_id))
		if invoice.round_off != 0:
			round_off_ledger = db_session.query(Ledger).filter(
				Ledger.group_id == OTHER_INDIRECT_EXPENSES_GROUP_ID, Ledger.enterprise_id == invoice.enterprise_id,
				Ledger.name == "Round-off").first()
			item_no += 1
			voucher_particulars.append(VoucherParticulars(
				item_no=item_no, ledger_id=round_off_ledger.id, is_debit=((invoice.round_off > 0) == for_rejection),
				amount=abs(round(Decimal(invoice.round_off) * Decimal(invoice.currency_conversion_rate), 2)),
				enterprise_id=invoice.enterprise_id))
		voucher = Voucher(
			type_id=5, voucher_no=0, voucher_date=invoice.issued_on if invoice.issued_on else datetime.now(), narration=narration,
			enterprise_id=invoice.enterprise_id, project_code=invoice.project_code, created_by=invoice.approved_by)
		invoice_tags = db_session.query(InvoiceTag).filter(InvoiceTag.invoice_id == invoice.id).all()
		if not is_scrutiny:
			account_service.prepareVoucherApproval(
				voucher_to_be_saved=voucher, enterprise=enterprise, approved_by=user_id)
		for tag in invoice_tags:
			voucher_tags.append(VoucherTag(tag_id=tag.tag_id))
		voucher.particulars = voucher_particulars
		voucher.tags = voucher_tags
		return voucher

	def __generateInvoiceApprovalVouchersAndBills(
			self, invoice=Invoice(), db_session=None, approved_by=None, is_scrutiny=None, enterprise=None, isprimary_project=True):
		"""

		:param invoice:
		:param db_session:
		:param approved_by:
		:return:
		"""
		if invoice.issued_on:
			issued_on = invoice.issued_on
		else:
			issued_on = datetime.now()
		narration = "[%s (%s)]" % (invoice.getCode(), issued_on.strftime("%d-%b-%Y %H:%M:%S"))
		invoice_items = db_session.query(InvoiceMaterial).filter(
			InvoiceMaterial.invoice_id == invoice.id).order_by(
			InvoiceMaterial.quantity.desc(), InvoiceMaterial.rate.desc()).limit(2)
		for item in invoice_items:
			narration = "%s %s %s %s %s @ %s," % (
				narration, item.item.name, "%s" % item.remarks if item.remarks else "", item.quantity, item.item.unit,
				round(item.rate, 2))
		narration = "%s TO: %s" % (narration, invoice.customer.name)
		db_session.begin(subtransactions=True)
		try:
			voucher = self.__constructSalesVoucher(
				invoice=invoice, narration=narration, db_session=db_session, is_scrutiny=is_scrutiny,
				user_id=approved_by, enterprise=enterprise)
			ledger_bill = LedgerBill(
				bill_no=invoice.getCode(), bill_date=issued_on.date(), ledger_id=invoice.customer.getCustomerLedger().id,
				is_debit=True, enterprise_id=invoice.enterprise_id,
				net_value=round(Decimal(invoice.grand_total) * Decimal(invoice.currency_conversion_rate), 2))
			ledger_bill.voucher = voucher
			ledger_bill_settlement = LedgerBillSettlement(
				dr_value=round(Decimal(invoice.grand_total) * Decimal(invoice.currency_conversion_rate), 2))
			ledger_bill_settlement.voucher = voucher
			ledger_bill_settlement.bill = ledger_bill
			ledger_bill_settlement.enterprise_id = invoice.enterprise_id
			invoice.ledger_bill = ledger_bill
			db_session.add(ledger_bill_settlement)
			db_session.commit()
			if not isprimary_project:
				createPurchaseInternalVoucher(invoice=invoice, db_session=db_session, narration=narration, enterprise_id=enterprise.id,
												 user_id=approved_by, issued_on=issued_on, internal_invoice_ledger_bill=ledger_bill)
			VoucherChangelog().queryVoucherInsert(user_id=approved_by, enterprise_id=enterprise.id, data=voucher)
		except Exception as e:
			logger.exception("Sales voucher creation failed... %s" % str(e))
			db_session.rollback()
		return True

	def __generateInvoiceRejectionVouchersAndBills(
			self, invoice=Invoice(), db_session=None, is_scrutiny=None, user_id=None, enterprise=None):
		"""

		:param invoice:
		:param db_session:
		:return:
		"""
		narration = "Rejected Sales Invoice [%s] on %s" % (
			invoice.getCode(), datetime.now().strftime("%b %d, %Y %H:%M:%S"))
		db_session.begin(subtransactions=True)
		try:
			if invoice.issued_on:
				issued_on = invoice.issued_on
			else:
				issued_on = datetime.now()
			voucher = self.__constructSalesVoucher(
				user_id=user_id, enterprise=enterprise, invoice=invoice, narration=narration, db_session=db_session,
				for_rejection=True, is_scrutiny=is_scrutiny)
			if invoice.ledger_bill_id:
				ledger_bill = db_session.query(LedgerBill).filter(
					LedgerBill.id == invoice.ledger_bill_id, LedgerBill.enterprise_id == invoice.enterprise_id).first()
			else:
				ledger_bill = db_session.query(LedgerBill).filter(
					LedgerBill.bill_no == invoice.getCode(), LedgerBill.bill_date == issued_on.date(),
					LedgerBill.ledger_id == invoice.customer.getCustomerLedger().id,
					LedgerBill.enterprise_id == invoice.enterprise_id).first()
			ledger_bill_settlement = LedgerBillSettlement(
				cr_value=round(Decimal(invoice.grand_total) * Decimal(invoice.currency_conversion_rate), 2))
			ledger_bill_settlement.voucher = voucher
			ledger_bill_settlement.bill = ledger_bill
			ledger_bill_settlement.enterprise_id = invoice.enterprise_id
			bill_net_value = float(ledger_bill.net_value) * (1 if ledger_bill.is_debit else -1) - round(
				Decimal(invoice.grand_total) * Decimal(invoice.currency_conversion_rate), 2)
			ledger_bill.is_debit = bill_net_value > 0
			ledger_bill.net_value = abs(bill_net_value)
			invoice.ledger_bill = ledger_bill
			db_session.add(ledger_bill_settlement)
			db_session.commit()
		except Exception as e:
			logger.exception("Ledger Bill creation Failed - %s" % str(e))
			db_session.rollback()
			return False
		return True

	def generateRequiredLedgers(self, invoice=None, db_session=None):
		"""
		Automatic Party & Taxes Voucher Creation
		:param invoice:
		:param db_session:
		:return:
		"""
		db_session.begin(subtransactions=True)
		try:
			if not invoice:
				invoice = Invoice()
			if not invoice.customer.getCustomerLedger():
				createPartyLedgers(
					party=invoice.customer, user_id=invoice.approved_by, db_session=db_session, is_customer=True)
			if invoice.type == 'JDC':
				if not invoice.customer.getSupplierLedger():
					createPartyLedgers(
						party=invoice.customer, user_id=invoice.approved_by, db_session=db_session, is_supplier=True)
			for tax in invoice.getConsolidatedTaxList():
				if not tax.getOutputLedgerId():
					createTaxLedgers(tax=tax, created_by=invoice.approved_by, db_session=db_session, is_output=True)
			db_session.commit()
			db_session.refresh(invoice)
		except Exception as e:
			logger.exception("Failed to create Ledgers - %s" % str(e))
			db_session.rollback()

	def generateRequiredLedgersForSupplier(self, receipt=None, db_session=None):
		"""
		Automatic Party & Taxes Voucher Creation
		:param receipt:
		:param db_session:
		:return:
		"""
		db_session.begin(subtransactions=True)
		try:
			if not receipt:
				receipt = Receipt()
			if not receipt.supplier.getSupplierLedger():
				createPartyLedgers(
					party=receipt.supplier, user_id=receipt.approved_by, db_session=db_session, is_supplier=True)
			for tax in receipt.getConsolidatedTaxList():
				if not tax.getInputLedgerId():
					createTaxLedgers(tax=tax, created_by=receipt.approved_by, db_session=db_session, is_input=True)
			db_session.commit()
			db_session.refresh(receipt)
		except Exception as e:
			logger.exception("Failed to create Supplier Ledgers - %s" % str(e))
			db_session.rollback()

	def extractInvoiceVO(self, post_data={}, enterprise_id=None, issue_type="internal", current_user=None):
		"""
		Extracts the request.POST data & construct Invoice VO, comprising of various Invoice related forms

		:param post_data:
		:param enterprise_id:
		:param issue_type:
		:return:
		"""
		invoice_vo = InvoiceVO(invoice_form=InvoiceForm(
			data=post_data, prefix=INVOICE_FORM_PREFIX, enterprise_id=enterprise_id, type=issue_type, current_user=current_user),
			invoice_material_formset=InvoiceMaterialFormset(post_data, prefix=INVOICE_MAT_PREFIX),
			invoice_tax_formset=InvoiceTaxFormset(post_data, prefix=INVOICE_TAX_PREFIX),
			invoice_tag_formset=generateTagFormset(post_data),
			invoice_charges_formset=InvoiceChargesFormset(post_data, prefix=INVOICE_CHARGE_PREFIX))
		i = 0
		for invoice_material_form in invoice_vo.invoice_material_formset:
			invoice_material_form.taxes = ItemTaxFormset(
				post_data, prefix=INVOICE_MAT_TAX_PREFIX % i)
			i += 1
		i = 0
		for invoice_charge_form in invoice_vo.invoice_charges_formset:
			invoice_charge_form.taxes = ItemTaxFormset(
				post_data, prefix=INVOICE_CHARGE_TAX_PREFIX % i)
			i += 1
		return invoice_vo

	def populateInvoiceDataFromForm(self, invoice_vo=None):
		invoice_data = copyDictToDict(source=invoice_vo.invoice_form.cleaned_data)
		# convert item form into item data
		invoice_data["project_code"] = invoice_data["project_id"]
		invoice_data['items'] = []
		invoice_data['taxes'] = []
		invoice_data['charges'] = []
		for item_form in invoice_vo.invoice_material_formset:
			if (item_form.cleaned_data['invoice_id'] or (item_form.cleaned_data['invoice_id'] is None
					and not item_form.cleaned_data['DELETE'])):
				item_data = copyDictToDict(source=item_form.cleaned_data, exclude_keys='taxes')
				item_data['taxes'] = []
				for tax in item_form.taxes:
					if tax.is_valid() and tax.cleaned_data['tax_code'] != "" and tax.cleaned_data['DELETE'] is False:
						item_data['taxes'].append(copyDictToDict(source=tax.cleaned_data))
				invoice_data['items'].append(item_data)
		invoice_data['tags'] = extractTagsFromFormset(tag_formset=invoice_vo.invoice_tag_formset)
		for invoice_tax in invoice_vo.invoice_tax_formset:
			if not invoice_tax.cleaned_data['DELETE']:
				invoice_data['taxes'].append(copyDictToDict(source=invoice_tax.cleaned_data))
		for invoice_charge in invoice_vo.invoice_charges_formset:
			if invoice_charge.cleaned_data['rate'] > 0:
				invoice_charges = copyDictToDict(source=invoice_charge.cleaned_data, exclude_keys='taxes')
				invoice_charges['taxes'] = []
				for tax in invoice_charge.taxes:
					if tax.is_valid() and tax.cleaned_data['tax_code'] != "" and tax.cleaned_data['DELETE'] is False:
						invoice_charges['taxes'].append(copyDictToDict(source=tax.cleaned_data))
				invoice_data['charges'].append(invoice_charges)
		return invoice_data

	def _copyInvoiceDataToEntity(self, invoice_data=None, entity=None):
		"""
		Method converts json data into persist-able data entity.
		Caution: Don't use this method unless the data is to be persisted immediately, as it might lead to inconsistent
			Invoice numbers
		:param invoice_data:
		:param entity:
		:return:
		"""
		try:
			logger.info("Invoice Id fetched from Invoice_VO: %s" % invoice_data['id'])
			entity = self.__copyInvoiceHeaderDataToEntity(invoice_data=invoice_data, entity=entity)
			entity.items = self.__extractInvoiceMaterialsFromData(
				invoice_material_data=invoice_data['items'], invoice=entity, invoice_type=invoice_data['type'])
			entity.charges = self.__extractInvoiceChargesFromData(
				invoice_charges_data=invoice_data['charges'], invoice_id=invoice_data['id'])
			logger.debug("Invoice Charges: %s" % entity.charges)
			entity.taxes = self.__extractInvoiceTaxFromJData(
				invoice_taxes_data=invoice_data['taxes'], invoice_id=invoice_data['id'])
			entity.tags = getEntityTagMaps(
				enterprise_id=invoice_data['enterprise_id'], tags=invoice_data['tags'], tag_map_class=InvoiceTag,
				db_session=self.invoice_dao.db_session)
			return entity
		except Exception as e:
			logger.exception("Error in Copy Invoice Vo to Entity: %s" % e)
		return entity

	def _generateInvoiceForm(self, invoice=Invoice(), prefix=INVOICE_FORM_PREFIX, type="internal", default_notes="",
							 is_dc_edit=False, current_user=None):
		initializer = constructFormInitializer(invoice, exclude_field_keys=('remarks',))
		initializer[u'code'] = invoice.getInternalCode()
		initializer[u'notes'] = default_notes
		if invoice.issued_on:
			initializer[u'issued_on'] = invoice.issued_on.strftime("%Y-%m-%d %H:%M:%S")
		return InvoiceForm(
			enterprise_id=invoice.enterprise_id, po_party_id=invoice.party_id, initial=initializer, prefix=prefix,
			type=type, invoice_id=invoice.id, is_dc_edit=is_dc_edit, current_user=current_user)

	def _genereateInvoiceMaterialFormset(self, invoice_items=()):
		initializer = []
		invoice_item_tax_formsets = []
		i = 0
		for invoice_material in invoice_items:
			form_initializer = constructFormInitializer(invoice_material)
			description = invoice_material.item.name
			make_name = helper.constructDifferentMakeName(invoice_material.item.makes_json)
			make = " [" + make_name + "]" if make_name else ""
			if invoice_material.item.drawing_no:
				description += " - %s" % invoice_material.item.drawing_no
			form_initializer['item_name'] = "%s  %s %s" % (
				description, make, "[Faulty]" if invoice_material.is_faulty == IS_FAULTY_TRUE else "")
			form_initializer['item_code'] = "%s" % invoice_material.item.drawing_no
			form_initializer['unit_id'] = "%s" % invoice_material.item.unit.unit_name
			form_initializer['make'] = "%s" % invoice_material.make.label if invoice_material.make else "-NA-"
			form_initializer['entry_order'] = "%s" % invoice_material.entry_order
			form_initializer['inspection_log'] = json.dumps(
				invoice_material.inspection_log if invoice_material.inspection_log else [])
			form_initializer['material_type'] = "%s" % int(invoice_material.item.is_stocked)
			form_initializer['is_service'] = "%s" % int(invoice_material.item.is_service)
			if invoice_material.inv_oa and invoice_material.invoice.type != 'JIN':
				form_initializer['oa_code'] = invoice_material.inv_oa.getInternalCode()
			if invoice_material.grn:
				form_initializer['grn_code'] = invoice_material.grn.getCode()
				form_initializer['grn_no'] = invoice_material.receipt_no
			if invoice_material.delivered_dc_id:
				dc = self.invoice_dao.getInvoice(
					enterprise_id=invoice_material.enterprise_id, invoice_id=invoice_material.delivered_dc_id)
				form_initializer['dc_code'] = dc.getCode()
				form_initializer['dc_no'] = invoice_material.delivered_dc_id
			enterprise = invoice_material.invoice.enterprise

			if enterprise.is_multiple_units > 0:
				is_alternate_unit = helper.getAlternateUnitCount(
					enterprise_id=invoice_material.enterprise_id, item_id=invoice_material.item_id)
				form_initializer['alternate_unit_list'] = []
				if is_alternate_unit > 0:
					alternate_unit_list = [{
						"alternate_unit_id": 0, "unit_name": form_initializer['unit_id'], "scale_factor": 1}]
					alternate_unit_list.extend(helper.populateAlternateUnits(
						enterprise_id=invoice_material.enterprise_id, item_id=invoice_material.item_id))
					form_initializer['alternate_unit_list'] = json.dumps(alternate_unit_list, cls=CustomJSONEncoder)
			if invoice_material.alternate_unit_id:
				scale_factor = helper.getScaleFactor(
						enterprise_id=invoice_material.enterprise_id, item_id=invoice_material.item_id, alternate_unit_id=invoice_material.alternate_unit_id)
				if scale_factor:
					form_initializer['quantity'] = Decimal(invoice_material.quantity) / Decimal(scale_factor)
					form_initializer['rate'] = Decimal(invoice_material.rate) * Decimal(scale_factor)
					form_initializer['unit_id'] = invoice_material.alternate_unit.unit.unit_name
					form_initializer['scale_factor'] = scale_factor if scale_factor else 1
			else:
				form_initializer['alternate_unit_id'] = 0
			logger.debug("after resetting the item name: %s" % form_initializer)
			tax_initializer = self.__generateInvoiceMaterialTaxesInitial(invoice_material.taxes)
			invoice_item_tax_formsets.append(
				ItemTaxFormset(prefix=INVOICE_MAT_TAX_PREFIX % i, initial=tax_initializer))
			qty = 0
			for received_item in invoice_material.items_received:
				qty += received_item.quantity
			form_initializer['received_qty'] = qty
			initializer.append(form_initializer)
			i += 1
		invoice_material_formset = InvoiceMaterialFormset(initial=initializer, prefix=INVOICE_MAT_PREFIX)
		i = 0
		for form in invoice_material_formset:
			if invoice_item_tax_formsets:
				form.taxes = invoice_item_tax_formsets[i]
				i += 1
		return invoice_material_formset

	def __generateInvoiceMaterialTaxesInitial(self, invoice_item_taxes=()):
		tax_formset_initializer = [
			{"tax_code": "", "tax_type": "CGST", "rate": "", "amount": ""},
			{"tax_code": "", "tax_type": "SGST", "rate": "", "amount": ""},
			{"tax_code": "", "tax_type": "IGST", "rate": "", "amount": ""}]
		for material_tax in invoice_item_taxes:
			if material_tax.tax.type == "CGST":
				tax_formset_initializer[0]["tax_code"] = material_tax.tax_code
				tax_formset_initializer[0]["rate"] = material_tax.tax.net_rate
			elif material_tax.tax.type == "SGST":
				tax_formset_initializer[1]["tax_code"] = material_tax.tax_code
				tax_formset_initializer[1]["rate"] = material_tax.tax.net_rate
			elif material_tax.tax.type == "IGST":
				tax_formset_initializer[2]["tax_code"] = material_tax.tax_code
				tax_formset_initializer[2]["rate"] = material_tax.tax.net_rate
			else:
				tax_formset_initializer.append(constructFormInitializer(material_tax))
		return tax_formset_initializer

	def _genereateInvoiceChargesFormset(self, invoice_charges=(), prefix=INVOICE_CHARGE_PREFIX):
		initializer = []
		invoice_charge_tax_formsets = []
		i = 0
		for invoice_charge in invoice_charges:
			form_initializer = constructFormInitializer(invoice_charge)
			form_initializer['item_name'] = "%s" % invoice_charge.item_name
			form_initializer['quantity'] = 1
			tax_initializer = self.__generateInvoiceMaterialTaxesInitial(invoice_charge.taxes)
			invoice_charge_tax_formsets.append(
				ItemTaxFormset(prefix=INVOICE_CHARGE_TAX_PREFIX % i, initial=tax_initializer))
			logger.debug("[Charge Taxes] Final Initializer: %s" % form_initializer)
			initializer.append(form_initializer)
			i += 1
		invoice_charges_formset = InvoiceChargesFormset(initial=initializer, prefix=prefix)
		i = 0
		for form in invoice_charges_formset:
			form.taxes = invoice_charge_tax_formsets[i]
			logger.debug("Invoice Charge Tax form: %s" % invoice_charge_tax_formsets[i].as_p())
			i += 1
		return invoice_charges_formset

	def _generateInvoiceTaxFormset(self, invoice_taxes=()):
		initializer = []
		logger.info("Invoice has %s invoice_taxes associated" % len(invoice_taxes))
		for tax in invoice_taxes:
			from_initializer = constructFormInitializer(tax)
			initializer.append(from_initializer)
		return InvoiceTaxFormset(initial=initializer, prefix=INVOICE_TAX_PREFIX)

	def __copyInvoiceHeaderDataToEntity(self, invoice_data=None, entity=None):
		try:
			if entity.id is None or entity.id == '':
				invoice_data['id'] = invoice_data['id'] if invoice_data['id'] != "" else None
				copyDictToDict(source=invoice_data, destination=entity.__dict__, exclude_keys=INVOICE_EXCLUDE_FIELD_LIST)
			else:
				entity.enterprise_id = invoice_data['enterprise_id']
				entity.po_date = invoice_data['po_date']
				entity.po_no = invoice_data['po_no']
				entity.party_id = invoice_data['party_id']
				entity.order_accept_no = ("%s" % invoice_data['order_accept_no'])[0:30]
				entity.order_accept_date = invoice_data['order_accept_date']
				entity.transport_mode = invoice_data['transport_mode']
				entity.lr_no = invoice_data['lr_no']
				entity.road_permit_no = invoice_data['road_permit_no']
				entity.packing_slip_no = invoice_data['packing_slip_no']
				entity.packing_description = invoice_data['packing_description']
				entity.grand_total = invoice_data['grand_total']
				entity.type = invoice_data['type']
				entity.issued_to = invoice_data['issued_to']
				entity.issued_for = invoice_data['issued_for']
				entity.project_code = invoice_data['project_code']
				entity.deliver_to = invoice_data['deliver_to']
				entity.gstin = invoice_data['gstin']
				entity.ship_to_name = invoice_data['ship_to_name']
				entity.round_off = invoice_data['round_off']
				entity.currency_id = invoice_data['currency_id']
				entity.payment_terms = invoice_data['payment_terms']
				entity.special_instruction = invoice_data['special_instruction']
				entity.notes = invoice_data['notes']
				entity.currency_conversion_rate = invoice_data['currency_conversion_rate']
				entity.return_date = invoice_data['return_date']
				entity.issued_on = invoice_data['issued_on']
				entity.tax_payable_on_reverse_charge = invoice_data['tax_payable_on_reverse_charge']
				entity.goods_already_supplied = invoice_data['goods_already_supplied']
				entity.ecommerce_gstin = invoice_data['ecommerce_gstin']
				entity.sale_account_id = invoice_data[
					'sale_account_id'] if 'sale_account_id' in invoice_data else None
				entity.job_po_id = invoice_data[
					'job_po_id'] if 'job_po_id' in invoice_data else None
				entity.no_of_consignment = invoice_data['no_of_consignment'] if invoice_data['no_of_consignment'] else 0
				entity.weight = invoice_data['weight'] if invoice_data['weight'] else 0
				entity.is_courier = invoice_data['is_courier'] if invoice_data['is_courier'] else 0
				entity.location_id = invoice_data["location_id"]
			if not entity.job_po_id or entity.job_po_id == '':
				entity.job_po_id = 0
			if not ('%s' % entity.party_id).isdigit():
				entity.party_id = None
		except Exception as e:
			logger.exception("Error in Copy Form to Entity Part - %s" % str(e))
			raise
		return entity

	def __extractInvoiceMaterialsFromData(self, invoice_material_data=None, invoice=None, invoice_type=None):
		invoice_items = []
		try:
			for item_data in invoice_material_data:
				item_id = item_data['item_id']
				enterprise_id = item_data['enterprise_id']
				make_id = item_data['make_id']
				is_faulty = item_data['is_faulty']
				oa_no = item_data['oa_no'] if 'oa_no' in item_data else None
				dc_no = item_data['dc_no'] if 'dc_no' in item_data else None
				alternate_unit_id = item_data['alternate_unit_id']
				grn_no = None
				if invoice_type == 'JIN' or (
						'grn_no' in item_data and item_data['grn_no'] != 0 and item_data['grn_no']):
					grn_no = item_data['grn_no']
				invoice_mat = self.invoice_dao.getInvoiceMaterial(
					invoice_id=invoice.id if invoice.id != '' else None, item_id=item_id, enterprise_id=enterprise_id, make_id=make_id,
					is_faulty=is_faulty, oa_no=oa_no, dc_no=dc_no, grn_no=grn_no)
				if not invoice_mat:
					invoice_mat = InvoiceMaterial(
						item_id=item_id, enterprise_id=enterprise_id, make_id=make_id,
						is_faulty=is_faulty, oa_no=oa_no, delivered_dc_id=dc_no, receipt_no=grn_no)
					invoice_mat.invoice = invoice
				invoice_mat.quantity = item_data['quantity']
				invoice_mat.rate = item_data['rate']
				invoice_mat.location_id = invoice.location_id
				if alternate_unit_id:
					scale_factor = helper.getScaleFactor(
						enterprise_id=enterprise_id, item_id=item_id, alternate_unit_id=alternate_unit_id)
					if scale_factor:
						invoice_mat.quantity = Decimal(item_data['quantity']) * Decimal(scale_factor)
						invoice_mat.rate = Decimal(item_data['rate']) / Decimal(scale_factor) if item_data[
							'rate'] else 0
						invoice_mat.alternate_unit_id = alternate_unit_id
				else:
					invoice_mat.alternate_unit_id = None
				invoice_mat.is_delete = item_data['DELETE']
				invoice_mat.hsn_code = item_data['hsn_code']
				invoice_mat.discount = item_data['discount']
				invoice_mat.remarks = item_data['remarks'] if 'remarks' in item_data else ''
				invoice_mat.entry_order = item_data['entry_order'] if item_data['entry_order'] != "" else 0
				if dc_no:
					invoice_mat.delivered_dc_id = item_data['dc_no']
				if oa_no and oa_no != "" and oa_no != 0 and oa_no != '0':
					invoice_mat.oa_no = oa_no
				else:
					invoice_mat.oa_no = None
				if invoice_type == 'JIN' or (
						'grn_no' in item_data and item_data['grn_no'] != 0 and item_data['grn_no']):
					invoice_mat.receipt_no = grn_no
					if item_data['oa_no'] != 0 and grn_no:
						oa_no = self.invoice_dao.getOACode(
							grn_no=grn_no, item_id=item_id, enterprise_id=enterprise_id, make_id=make_id,
							is_faulty=is_faulty)
						if oa_no and oa_no != 0:
							invoice_mat.oa_no = oa_no
				invoice_mat.is_returnable = item_data['is_returnable']
				if invoice_type != "BoS":
					invoice_mat.taxes = self.__extractInvoiceMaterialTaxesFromData(
						invoice_item_taxes=item_data['taxes'], invoice_id=invoice_mat.invoice_id if invoice_mat.invoice_id != "" else None,
						item_id=invoice_mat.item_id, enterprise_id=enterprise_id, make_id=invoice_mat.make_id,
						is_faulty=is_faulty, oa_no=oa_no, dc_no=dc_no, grn_no=grn_no)
				if 'inspection_log' in item_data:
					invoice_mat.inspection_log = json.loads(
						item_data['inspection_log'] if item_data['inspection_log'] and len(
							item_data['inspection_log']) > 0 else '[]')
				invoice_items.append(invoice_mat)
			logger.debug('Invoice Material constructed: %s' % invoice_items)
			return invoice_items
		except Exception as e:
			raise e

	def __extractInvoiceMaterialTaxesFromData(
			self, invoice_item_taxes=None, invoice_id=None, item_id=None, enterprise_id=None, make_id=None, is_faulty=None, oa_no=None, dc_no=None, grn_no=None):
		invoice_material_taxes = []
		for invoice_item_tax in invoice_item_taxes:
			tax_code = invoice_item_tax['tax_code']
			if invoice_item_tax['tax_code'].strip() == "":
				continue
			if oa_no and oa_no != "" and oa_no != 0 and oa_no != '0':
				oa_no = oa_no
			else:
				oa_no = None
			invoice_material_tax = self.invoice_dao.getInvoiceMaterialTax(
				invoice_id=invoice_id if invoice_id != "" else None, item_id=item_id, enterprise_id=enterprise_id,
				tax_code=tax_code, make_id=make_id, is_faulty=is_faulty, oa_no=oa_no, dc_no=dc_no, grn_no=grn_no)
			if not invoice_material_tax:
				invoice_material_tax = InvoiceMaterialTax(
					invoice_id=invoice_id if invoice_id != "" else None, item_id=item_id, enterprise_id=enterprise_id,
					tax_code=tax_code, make_id=make_id, is_faulty=is_faulty, oa_no=oa_no, delivered_dc_id=dc_no, receipt_no=grn_no)
			invoice_material_taxes.append(invoice_material_tax)
		return invoice_material_taxes

	def __extractInvoiceChargesFromData(self, invoice_charges_data=None, invoice_id=None):
		invoice_charges = []
		for invoice_charge_data in invoice_charges_data:
			enterprise_id = invoice_charge_data['enterprise_id']
			item_name = invoice_charge_data['item_name']
			invoice_charge = self.invoice_dao.getInvoiceCharge(
				invoice_id=invoice_id if invoice_id != "" else None, item_name=item_name, enterprise_id=enterprise_id)
			if not invoice_charge:
				invoice_charge = InvoiceCharge(
					invoice_id=invoice_id if invoice_id != "" else None, enterprise_id=enterprise_id, item_name=item_name)
			invoice_charge.hsn_code = invoice_charge_data['hsn_code']
			invoice_charge.is_percent = invoice_charge_data['is_percent']
			invoice_charge.rate = invoice_charge_data['rate']
			invoice_charge.discount = invoice_charge_data['discount']
			invoice_charge.taxes = self.__extractInvoiceChargeTaxesFromData(
				invoice_charge_taxes_data=invoice_charge_data['taxes'], invoice_id=invoice_id if invoice_id != "" else None,
				item_name=item_name, enterprise_id=enterprise_id)
			invoice_charges.append(invoice_charge)
		logger.debug("Invoice Charge Items constructed from Form: %s" % invoice_charges)
		return invoice_charges

	def __extractInvoiceChargeTaxesFromData(
			self, invoice_charge_taxes_data=None, invoice_id=None, item_name=None, enterprise_id=None):
		invoice_charge_taxes = []
		for invoice_charge_tax_data in invoice_charge_taxes_data:
			tax_code = invoice_charge_tax_data['tax_code']
			invoice_charge_tax = self.invoice_dao.getInvoiceChargeTax(
				invoice_id=invoice_id if invoice_id != "" else None, item_name=item_name, enterprise_id=enterprise_id, tax_code=tax_code)
			if not invoice_charge_tax:
				invoice_charge_tax = InvoiceChargeTax(
					invoice_id=invoice_id if invoice_id != "" else None, item_name=item_name, enterprise_id=enterprise_id, tax_code=tax_code)
			invoice_charge_taxes.append(invoice_charge_tax)
		logger.debug("Invoice Charge Taxes: %s" % invoice_charge_taxes)
		return invoice_charge_taxes

	def __extractInvoiceTaxFromJData(self, invoice_taxes_data=None, invoice_id=None):
		invoice_tax_list = []
		try:
			for invoice_tax_data in invoice_taxes_data:
				tax_code = invoice_tax_data['tax_code']
				enterprise_id = invoice_tax_data['enterprise_id']
				invoice_tax = self.invoice_dao.getInvoiceTax(
					invoice_id=invoice_id if invoice_id != "" else None, tax_code=tax_code, enterprise_id=enterprise_id)
				if not invoice_tax:
					invoice_tax = InvoiceTax(invoice_id=invoice_id if invoice_id != "" else None, tax_code=tax_code, enterprise_id=enterprise_id)
				invoice_tax_list.append(invoice_tax)
			logger.debug('Invoice Tax constructed: %s' % invoice_tax_list)
		except Exception as e:
			logger.exception("Invoice Tax Extraction from Formset failed...\n %s" % str(e))
			pass
		return invoice_tax_list

	def __paymentStatus(self, invoices=None):
		"""

		:param invoices:
		:return: grand_total, pending_delayed, pending_on_track, collection_on_time, collection_delayed
		"""
		try:
			today = datetime.today()
			collection_on_time, collection_delayed = 0, 0
			for invoice in invoices:
				due_on = invoice.issued_on if invoice.issued_on else datetime.now() + relativedelta(days=invoice.customer.receipt_credit_days)
				if not invoice.ledger_bill or len(invoice.ledger_bill.settlements) == 0:
					logger.warn(
						"Skipping bill for getting payment status due to no settlements %s " % invoice.ledger_bill)
					continue
				# Collected
				amount_collected = float(invoice.ledger_bill.net_value) * (
					-1 if invoice.ledger_bill.is_debit else 1) - float(
					invoice.ledger_bill.original_settlement.cr_value - invoice.ledger_bill.original_settlement.dr_value)
				if amount_collected > 0:
					if due_on.date() >= invoice.ledger_bill.settlements[-1].voucher.voucher_date.date():
						collection_on_time += amount_collected
					else:
						collection_delayed += amount_collected
				logger.debug("On Track - %s | Collection profile - %s-%s" % (
					due_on.date() >= today.date(), collection_on_time, collection_delayed))
			return collection_on_time, collection_delayed
		except Exception as e:
			raise

	def getMonthlySalesPerformance(self, enterprise_id=None, since=None, till=None):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:return:
		"""
		_group_ids = "'-1'"
		for _id in getAccountGroupIDs(enterprise_id=enterprise_id, names=[AccountGroup.SUNDRY_DEBTOR_GROUP_NAME]):
			_group_ids = "%s, '%s'" % (_group_ids, _id)
		query = """
			SELECT SUBSTRING(v.voucher_date, 1, 7) AS v_month, SUM(vp.amount * IF(vp.is_debit = 1, 1, - 1)) AS total_sales
			FROM voucher AS v
			JOIN voucher_particulars AS vp ON v.id = vp.voucher_id AND v.enterprise_id = vp.enterprise_id
			JOIN account_ledgers AS l ON vp.ledger_id = l.id AND vp.enterprise_id = l.enterprise_id
			WHERE v.enterprise_id = %s AND l.group_id IN (%s) AND v.type = 5 AND v.status = 1
			AND DATE(v.voucher_date) >= DATE('%s') AND DATE(v.voucher_date) <= DATE('%s')
			GROUP BY v_month;""" % (enterprise_id, _group_ids, since, till)
		return [{
			"key_to_sort": row[0], "month": datetime.strptime(row[0], "%Y-%m").strftime("%b'%y"),
			"value": float(row[1])} for row in dao.executeQuery(query)]

	def getMonthlyPaymentCollection(self, enterprise_id=None, since=None, till=None):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:return:
		"""
		_group_ids = "'-1'"
		for _id in getAccountGroupIDs(enterprise_id=enterprise_id, names=[AccountGroup.SUNDRY_DEBTOR_GROUP_NAME]):
			_group_ids = "%s, '%s'" % (_group_ids, _id)
		query = """
			SELECT
			collection.v_month, SUM(collection.v_amount) AS payment_received,
			SUM(collection.v_amount - (collection.collectted_on_time + collection.collectted_delayed)) AS advance,
			SUM(collection.collectted_on_time), SUM(collection.collectted_delayed)
			FROM (SELECT
				v.id, vp.amount * IF(vp.is_debit = 1, - 1, 1) AS v_amount,
				SUBSTRING(v.voucher_date, 1, 7) AS v_month,
				SUM(IF(DATEDIFF(DATE(v.voucher_date), DATE(lb.bill_date)) <= p.receipt_credit_days, IFNULL(lbs.cr_value, 0), 0)) AS collectted_on_time,
				SUM(IF(DATEDIFF(DATE(v.voucher_date), DATE(lb.bill_date)) > p.receipt_credit_days, IFNULL(lbs.cr_value, 0), 0)) AS collectted_delayed
				FROM voucher AS v
				JOIN voucher_particulars AS vp ON v.id = vp.voucher_id AND v.enterprise_id = vp.enterprise_id
				JOIN account_ledgers AS l ON vp.ledger_id = l.id AND vp.enterprise_id = l.enterprise_id
				JOIN party_ledger_map plm ON plm.ledger_id = l.id
				LEFT JOIN party_master AS p ON p.party_id = plm.party_id
				LEFT JOIN ledger_bill_settlements AS lbs ON lbs.voucher_id = v.id
				LEFT JOIN ledger_bills AS lb ON lbs.bill_id = lb.id
				WHERE v.enterprise_id = %s AND l.group_id IN (%s) AND v.status = 1
					AND DATE(v.voucher_date) >= DATE('%s') AND DATE(v.voucher_date) <= DATE('%s')
					AND vp.is_debit = 0 GROUP BY v.id) AS collection
			GROUP BY v_month;""" % (enterprise_id, _group_ids, since, till)
		return [{
			"key_to_sort": row[0], "month": datetime.strptime(row[0], "%Y-%m").strftime("%b'%y"),
			"value": float(row[1]), "advance": float(row[2]), "on_time": float(row[3]), "delayed": float(row[4])}
			for row in dao.executeQuery(query)]

	def formDashboardData(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		try:
			since = datetime.today() + relativedelta(months=-5, hour=0, minute=0, second=0, microsecond=0)
			since = since + relativedelta(days=-since.day + 1)
			sales_performance = self.getMonthlySalesPerformance(
				enterprise_id=enterprise_id, since=since, till=datetime.today().strftime("%y-%m-%d"))
			payment_collection = self.getMonthlyPaymentCollection(
				enterprise_id=enterprise_id, since=since, till=datetime.today().strftime("%y-%m-%d"))

			months_in_sales_performance = [s['month'] for s in sales_performance]
			months_in_payment_collection = [c['month'] for c in payment_collection]

			for i in range(0, 6):
				next_date = since + relativedelta(months=1)
				month = "%s" % since.strftime("%b'%y")
				logger.info("Getting invoices between %s - %s" % (since, next_date))
				key_to_sort = since.strftime("%Y-%m")
				if month not in months_in_sales_performance:
					sales_performance.append(
						{'month': ("%s" % since.strftime("%b'%y")), 'value': float(0), "key_to_sort": key_to_sort})
				if month not in months_in_payment_collection:
					payment_collection.append({
						'month': ("%s" % since.strftime("%b'%y")), 'value': float(0), "key_to_sort": key_to_sort,
						"advance": float(0), "on_time": float(0), "delayed": float(0)
					})
				since = next_date  # this is like for loop incrementer
			return {
				'sales_performance': sorted(sales_performance, key=lambda a: a['key_to_sort'])
				, 'payment_collection': sorted(payment_collection, key=lambda a: a['key_to_sort'])}
		except Exception as e:
			raise

	def paymentOAStatus(self, enterprise_id=None, since=None, till=None):
		"""

		:param enterprise_id:
		:param since:
		:param till:
		:return:
		"""

		try:
			invoices = self.invoice_dao.db_session.query(Invoice).filter(
				Invoice.enterprise_id == enterprise_id, Invoice.issued_on if Invoice.issued_on else datetime.now() >= since,
				Invoice.issued_on if Invoice.issued_on else datetime.now() < till, Invoice.type.in_(Invoice.TYPES["sales"]),
				Invoice.status == Invoice.STATUS_APPROVED).all()
			coll_on_time, coll_delayed = self.__paymentStatus(invoices=invoices)
			return {'collection_on_time': float(coll_on_time), 'collection_delayed': float(coll_delayed)}
		except Exception as e:
			raise

	def searchInvoices(
			self, enterprise_id=None, since=None, till=None, invoice_no=None, customer_id=None,
			item_id=None, project_code=None, status=None, finance_year=None, is_dispatch=False):
		result = []
		try:
			if is_dispatch:
				query = self.invoice_dao.db_session.query(Invoice).filter(
					Invoice.enterprise_id == enterprise_id,
					or_(Invoice.type.in_(Invoice.TYPES["sales"]), Invoice.type.in_(Invoice.TYPES["dc"])),
					Invoice.goods_already_supplied != 1)
			else:
				query = self.invoice_dao.db_session.query(Invoice).filter(Invoice.enterprise_id == enterprise_id, Invoice.type.in_(Invoice.TYPES["sales"]), )
			if invoice_no != "-1":
				if finance_year == 'All':
					query = query.filter(Invoice.invoice_no == invoice_no)
				else:
					query = query.filter(Invoice.invoice_no == invoice_no, Invoice.financial_year == finance_year)
			else:
				if status == "1":
					query = query.filter(Invoice.approved_on >= since, Invoice.approved_on <= till)
				else:
					query = query.filter(Invoice.prepared_on >= since, Invoice.prepared_on <= till)
				if customer_id != "-1":
					query = query.filter(Invoice.party_id == customer_id)
				if item_id != "-1":
					query = query.join(Invoice.items).filter(InvoiceMaterial.item_id == item_id)
				if project_code != "-1":
					query = query.filter(Invoice.project_code == project_code)
				if status != '100':
					query = query.filter(Invoice.status == status)
				else:
					query = query.filter(Invoice.status >= -1)
			invoices = query.all()

			for invoice in invoices:
				invoice_data = self.__getInvoiceData(invoice=invoice)
				result.append(invoice_data)

		except Exception as e:
			raise
		return result

	def invoiceFinanceYear(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		years = self.invoice_dao.db_session.query(
			Invoice.financial_year).filter(
			Invoice.enterprise_id == enterprise_id, Invoice.financial_year.isnot(None), Invoice.financial_year != ''
		).group_by(Invoice.financial_year).order_by(Invoice.financial_year.desc()).all()
		years = [year.financial_year for year in years]
		years.insert(0, "ALL")
		return years

	def invoiceMaterial(self, enterprise_id=None, invoice_id=None):
		"""

		:param enterprise_id:
		:param invoice_id:
		:return:
		"""
		material = []
		invoice_material = self.invoice_dao.db_session.query(
			InvoiceMaterial.quantity, InvoiceMaterial.discount, InvoiceMaterial.rate.label("price"),
			InvoiceMaterial.make_id, MaterialMakeMap.part_no, Material.drawing_no,
			Material.material_id.label("item_id"),
			Material.is_stocked, Material.name, Material.price.label("store_price"), Make.label.label("make_name"),
			UnitMaster.unit_name.label("unit"), InvoiceMaterial.alternate_unit_id.label('alternate_unit_id'),
			InvoiceMaterial.enterprise_id.label('enterprise_id')
		).outerjoin(
			InvoiceMaterial.item
		).outerjoin(
			InvoiceMaterial.make
		).outerjoin(
			MaterialMakeMap, and_(
				MaterialMakeMap.item_id == InvoiceMaterial.item_id
				, MaterialMakeMap.make_id == InvoiceMaterial.make_id
				, MaterialMakeMap.enterprise_id == InvoiceMaterial.enterprise_id)
		).outerjoin(
			UnitMaster, and_(Material.unit_id == UnitMaster.unit_id, Material.enterprise_id == UnitMaster.enterprise_id)
		).filter(InvoiceMaterial.enterprise_id == enterprise_id, InvoiceMaterial.invoice_id == invoice_id).all()

		for inv in invoice_material:
			inv_dict = inv._asdict()
			if inv.make_id != DEFAULT_MAKE_ID:
				part_no = inv.part_no
				inv_dict['make_name'] = "%s%s" % (inv.make_name, (" - %s" % part_no) if part_no else "")
			inv_dict['quantity'] = float(inv.quantity)
			inv_dict['discount'] = float(inv.discount)
			inv_dict['price'] = float(inv.price)
			inv_dict['store_price'] = float(inv.store_price)
			inv_dict['unit'] = inv.unit
			if inv.alternate_unit_id:
				scale_factor = helper.getScaleFactor(
					enterprise_id=inv.enterprise_id, item_id=inv.item_id, alternate_unit_id=inv.alternate_unit_id)
				if scale_factor:
					inv_dict['quantity'] = Decimal(inv.quantity) / Decimal(scale_factor)
					inv_dict['price'] = Decimal(inv.price) * Decimal(scale_factor)
					inv_dict['unit'] = helper.getUnitName(enterprise_id=inv.enterprise_id,
					                                      unit_id=inv.alternate_unit_id)
			material.append(inv_dict)
		return dict(materials=material)

	def invoiceMaterialOverDue(self, enterprise_id=None, party_id=None):
		"""

		:param enterprise_id:
		:param party_id:
		:return:
		"""
		outstanding = []
		ledger_data = self.invoice_dao.db_session.query(PartyLedgerMap).filter(
			PartyLedgerMap.enterprise_id == enterprise_id,
			PartyLedgerMap.party_id == party_id, PartyLedgerMap.is_supplier.is_(False)).first()
		if ledger_data != None:
			ledger = AccountService().getLedgerAging(enterprise_id=enterprise_id, ledger_id=ledger_data.ledger_id)
			ledger['party_id'] = ledger_data.party_id
			# TODO remove this pop lines by correcting exact issue
			ledger['aging'].pop("advance_particulars")
			ledger['aging'].pop("billable_particulars")
			outstanding.append(ledger)
		return outstanding

	def draftInvoices(self, enterprise_id=None):
		"""
		:param enterprise_id:
		:return:
		"""
		result = []
		try:
			invoices = self.invoice_dao.db_session.query(Invoice).filter(
				Invoice.enterprise_id == enterprise_id, Invoice.type.in_(Invoice.TYPES["sales"]),
				Invoice.status == Invoice.STATUS_DRAFT).order_by(Invoice.id).all()
			for invoice in invoices:
				invoice_data = self.__getInvoiceData(invoice=invoice)
				result.append(invoice_data)
		except Exception as e:
			raise
		return result

	def searchOA(
			self, enterprise_id=None, since=None, till=None, oa_no=None, supplier_id=None,
			item_id=None, project_code=None, status=None):
		"""

		:param enterprise_id:
		:param since: non None param
		:param till:
		:param oa_no:
		:param supplier_id:
		:param item_id:
		:param project_code:
		:param status:
		:return:
		"""
		oa_list = []
		try:
			query = self.invoice_dao.db_session.query(
				OA.po_date, OA.party_id, OA.id, OA.enterprise_id, OA.financial_year.label("year"), OA.oa_no, OA.type,
				OA.status, OA.approved_on, OA.prepared_on, OA.grand_total, OA.delivery_due_date, OA.document,
				OA.payment_terms, Currency.code.label('currency_name'), Party.name, Tag.tag.label('tag_name'),
				Project.name.label('project_name'), Project.code.label('project_code')
			).outerjoin(
				OA.currency).outerjoin(OA.customer).outerjoin(OA.project).outerjoin(OA.tags).outerjoin(
				OATag.tag
			).filter(OA.enterprise_id == enterprise_id, OA.type !='MRS')
			if oa_no != "-1":
				query = query.filter(OA.oa_no == oa_no)
			else:
				query = query.filter(OA.prepared_on >= since, OA.prepared_on <= till)
				if supplier_id != "-1":
					query = query.filter(OA.party_id == supplier_id)
				if item_id != "-1":
					query = query.join(OA.items).filter(OAParticulars.item_id == item_id)
				if status != '100':
					query = query.filter(OA.status == status)
				else:
					query = query.filter(OA.status >= -1)

			order_acknowledgements = query.order_by(OA.id).all()
			oa_id = None
			oa_dict = None

			for oa in order_acknowledgements:
				if oa_id != oa.id:
					oa_id = oa.id
					oa_dict = oa._asdict()
					if oa.approved_on is not None:
						oa_dict['approved_on'] = oa.approved_on.strftime('%d-%m-%Y')
					if oa.po_date is not None:
						oa_dict['po_date'] = oa.po_date.strftime('%d-%m-%Y')
					if oa.prepared_on is not None:
						oa_dict['prepared_on'] = oa.prepared_on.strftime('%d-%m-%Y')
					if oa.delivery_due_date is not None:
						oa_dict['delivery_due_date'] = oa.delivery_due_date.strftime('%d-%m-%Y')
					oa_dict['grand_total'] = float(oa.grand_total)
					oa_dict['supplier'] = dict(id=oa.party_id, name=oa.name)
					if oa.document:
						oa_dict['is_attached'] = True
					if oa.status == OA.STATUS_DRAFT:
						oa_dict['oa_code'] = OA.generateDraftInternalCode(type=oa.type, oa_no=oa.oa_no, oa_id=oa.id)
					else:
						oa_dict['oa_code'] = OA.generateInternalCode(financial_year=oa.year, type=oa.type,
						                                             oa_no=oa.oa_no)
					if project_code != "-1":
						oa_dict['project_name'] = oa.project.name if oa.project else ""
						oa_dict['project_code'] = oa.project.code if oa.project else ""
					oa_list.append(oa_dict)
					# if project_code != "-1":
					# 	tags = self.invoice_dao.db_session.query(OATag).filter(OATag.oa_id == oa.id).all()
					# 	oa_dict['tags'] = helper.getTagList(tags)
					# elif oa.tag_name:
					# 	oa_dict['tags'] = [oa.tag_name]
					# 	oa_list.append(oa_dict)
					# else:
					# 	oa_dict['tags'] = []
					# 	oa_list.append(oa_dict)
				# else:
				# 	oa_dict['tags'].append(oa.tag_name)
		except Exception as e:
			raise
		return dict(oa_list=oa_list)

	def oaFinanceYear(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		years = self.invoice_dao.db_session.query(
			OA.financial_year).filter(
			OA.enterprise_id == enterprise_id, OA.financial_year.isnot(None), OA.financial_year != '', OA.type !='MRS'
		).group_by(OA.financial_year).order_by(OA.financial_year.desc()).all()
		years = [year.financial_year for year in years]
		return years

	def draftOA(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		oa_list = []
		order_acknowledgements = self.invoice_dao.db_session.query(
			OA.status, OA.prepared_on, OA.party_id, OA.po_date, OA.oa_no, OA.delivery_due_date, OA.document,
			OA.approved_on, OA.type, OA.grand_total, OA.id, OA.financial_year, OA.payment_terms,
			Party.name, Currency.id.label('currency_id'), Currency.code.label('currency_name'), Tag.tag.label("tag_name"),
			Project.name.label('project_name'), Project.code.label('project_code')
		).outerjoin(
			OA.customer).outerjoin(OA.project).outerjoin(OA.tags).outerjoin(OA.currency).outerjoin(OATag.tag).filter(
			OA.enterprise_id == enterprise_id, OA.status == OA.STATUS_DRAFT, OA.type !='MRS').order_by(OA.id).all()
		oa_id = None
		oa_dict = None
		for oa in order_acknowledgements:
			if oa_id != oa.id:
				oa_id = oa.id
				oa_dict = oa._asdict()
				if oa.approved_on is not None:
					oa_dict['approved_on'] = oa.approved_on.strftime('%d-%m-%Y')
				if oa.po_date is not None:
					oa_dict['po_date'] = oa.po_date.strftime('%d-%m-%Y')
				if oa.prepared_on is not None:
					oa_dict['prepared_on'] = oa.prepared_on.strftime('%d-%m-%Y')
				if oa.delivery_due_date is not None:
					oa_dict['delivery_due_date'] = oa.delivery_due_date.strftime('%d-%m-%Y')
				if oa.document:
					oa_dict['is_attached'] = True
				if oa.grand_total is None:
					oa_dict['grand_total'] = 0
				else:
					oa_dict['grand_total'] = float(oa.grand_total)
				oa_dict['supplier'] = dict(id=oa.party_id, name=oa.name)
				oa_dict['currency'] = dict(code=oa.currency_id, name=oa.currency_name)
				oa_dict['oa_code'] = OA.generateInternalCode(
					financial_year=oa.financial_year, type=oa.type, oa_no=oa.oa_no, oa_id=oa.id)
				oa_dict['project_name'] = oa.project_name if oa.project_name else ""
				oa_dict['project_code'] = oa.project_code if oa.project_code else ""
				oa_list.append(oa_dict)
			# else:
			# 	oa_dict['tags'].append(oa.tag_name)
		return dict(oa_list=oa_list)

	def getOAMaterial(self, enterprise_id=None, oa_id=None):
		"""

		:param enterprise_id:
		:param oa_id:
		:return:
		"""
		oa_material_query = """SELECT m.drawing_no AS drawing_no,m.price AS store_price, m.is_stocked,
		oap.price AS price, oap.make_id AS make_id
			, oap.quantity AS quantity, m.name AS name, u.unit_name AS unit_name, u.unit_id AS unit_id
			, m.makes_json AS make_name, "false" AS non_stock,m.id AS item_id,
				oap.enterprise_id,oap.alternate_unit_id as alternate_unit_id
			FROM oa_particulars AS oap			
			LEFT JOIN materials AS m ON oap.item_id = m.id AND oap.enterprise_id = m.enterprise_id
			
			LEFT JOIN unit_master AS u ON u.unit_id = m.unit AND u.enterprise_id = m.enterprise_id
			WHERE oap.enterprise_id = {enterprise_id} AND oap.oa_id = {oa_id}""" \
			.format(enterprise_id=enterprise_id, oa_id=oa_id)
		materials = dao.executeQuery(oa_material_query, True)
		material_list = []
		for material in materials:
			if material['is_stocked'] == 1:
				material['is_stocked'] = 'true'
			else:
				material['is_stocked'] = 'false'
			if material['alternate_unit_id'] and material['alternate_unit_id'] != 0:
				scale_factor = helper.getScaleFactor(
					enterprise_id=material['enterprise_id'], item_id=material['item_id'],
					alternate_unit_id=material['alternate_unit_id'])
				if scale_factor:
					material['quantity'] = Decimal(material['quantity']) / Decimal(scale_factor)
					material['price'] = Decimal(material['price']) * Decimal(scale_factor)
					material['unit_name'] = helper.getUnitName(
						enterprise_id=material['enterprise_id'], unit_id=material['alternate_unit_id'])
			material_list.append(material)
		return {"materials": material_list}

	def __getInvoiceData(self, invoice=None):
		"""

		:param invoice:
		:return:
		"""
		invoice_data = {}
		try:
			today = datetime.today()
			invoice_data['id'] = invoice.id
			invoice_data['invoice_no'] = invoice.invoice_no
			invoice_data['type'] = invoice.type
			invoice_data['invoice_code'] = invoice.getSimpleCode()
			invoice_data['prepared_on'] = invoice.prepared_on.strftime("%d-%m-%Y")
			if invoice.po_date:
				invoice_data['po_date'] = invoice.po_date.strftime(
					"%d-%m-%Y") if invoice.po_date is not None and invoice.po_date != '0000-00-00' else ""
			invoice_data['party'] = dict(id=invoice.party_id, name=invoice.customer.name)
			invoice_data['enterprise_id'] = invoice.enterprise_id
			invoice_data['tags'] = helper.getTagList(tags=invoice.tags)
			if invoice.project:
				invoice_data['project_name'] = invoice.project.name
				invoice_data['project_code'] = invoice.project.code
			invoice_data['value'] = float(invoice.grand_total)
			invoice_data['status'] = invoice.status
			if invoice.approved_on:
				invoice_data['approved_on'] = invoice.approved_on.strftime("%d-%m-%Y")
			if invoice.currency:
				invoice_data['currency_name'] = invoice.currency.code
			invoice_data['issued_on'] = None
			invoice_data['due_on'] = None
			invoice_data['due_days'] = 0
			invoice_data['payment_status'] = "-"
			invoice_data['packing_status'] = invoice.PACKING_STATUS_DRAFT
			if invoice.issued_on:
				invoice_data['issued_on'] = invoice.issued_on.strftime("%Y-%m-%d %H:%M:%S")
			else:
				invoice_data['issued_on'] = invoice.prepared_on.strftime("%Y-%m-%d %H:%M:%S")
			if invoice.approved_on and invoice.status != -1:
				due_on = invoice.approved_on + relativedelta(days=+invoice.customer.receipt_credit_days)
				invoice_data['due_on'] = due_on.strftime("%d-%m-%Y")
				invoice_data['due_days'] = 0
				if invoice.ledger_bill and invoice.ledger_bill.net_value == 0:
					invoice_data['payment_status'] = 'Received'
				else:
					if due_on >= today:
						invoice_data['payment_status'] = 'On Track'
					else:
						invoice_data['payment_status'] = 'Delayed'
						td = today - due_on
						invoice_data['due_days'] = int(td.days)
			else:
				invoice_data['due_on'] = ""
				invoice_data['due_days'] = 0
				invoice_data['payment_status'] = ""
				invoice_data['due_days'] = 0
			total_invoiced_qty = self.invoice_dao.db_session.query(
				func.sum(InvoiceMaterial.quantity).label('invoiced_qty')).join(
				Invoice, and_(
					InvoiceMaterial.enterprise_id == Invoice.enterprise_id, InvoiceMaterial.invoice_id == Invoice.id)).filter(
				InvoiceMaterial.invoice_id == invoice.id).all()
			invoice_serial_no_count = self.invoice_dao.db_session.query(InvoiceMaterialSerialNo).filter(
				InvoiceMaterialSerialNo.invoice_id == invoice.id).count()

			if invoice_serial_no_count:
				if Decimal(invoice_serial_no_count) != 0:
					if Decimal(invoice_serial_no_count) == total_invoiced_qty[0][0]:
						invoice_data['packing_status'] = invoice.PACKING_STATUS_COMPLETED
					else:
						invoice_data['packing_status'] = invoice.PACKING_STATUS_PARTIAL
				else:
					invoice_data['packing_status'] = invoice.PACKING_STATUS_DRAFT
		except Exception as e:
			logger.exception("Exception while forming invoice_data.  %s" % e)
		return invoice_data

	def generateInvoiceDocument(
			self, invoice_id=None, enterprise_id=None, labels=None, invoice_type=None, document_regenerate=False,
			user=None, primary_enterprise_id=None):
		"""
		Fetches the document persisted for a given receipt_no. If such a document is not available in DB, fetches the
		Receipt details and generates the respective document.

		:param invoice_id:
		:param enterprise_id:
		:param labels:
		:param invoice_type:
		:param document_regenerate:
		:param user:
		:return:
		"""
		logger.info('Generating documents for Invoice id: %s' % invoice_id)
		primary_enterprise_id = primary_enterprise_id if primary_enterprise_id else enterprise_id
		self.invoice_dao.db_session.begin(subtransactions=True)
		invoice_to_print = self.invoice_dao.getInvoice(enterprise_id=enterprise_id, invoice_id=invoice_id)
		inv_config_details = self.invoice_template_dao.getInvoiceTemplateConfig(
			enterprise_id=enterprise_id, module=invoice_type)
		invoice_documents = invoice_to_print.documents
		invoice_document = invoice_documents[0] if len(invoice_documents) > 0 else None
		try:
			# FIXME: Generating, Printing and persisting the documents must be segregated
			logger.info("Invoice Document object %s" % (
				"exists" if invoice_document and invoice_document.document_pdf else "does not exist"))
			doc_status = ""
			temp_doc_path = getFormattedDocPath(code=invoice_to_print.getCode(), id=invoice_id)
			document_generator = InvoicePDFGenerator(inv_template_config=inv_config_details, target_file_path=temp_doc_path)
			enterprise = self.invoice_dao.db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
			if document_regenerate == "true" or invoice_document is None or invoice_document.document_pdf is None or len(
					invoice_document.document_pdf) == 0:
				document_pdf = document_generator.generatePDF(
					source=invoice_to_print, config_info=inv_config_details, enterprise=enterprise,
					logged_in_user=user,
					invoice_id=invoice_id, doc_status=doc_status)
				writeFile(document_pdf, temp_doc_path)
				logger.info('Invoice to be printed: %s' % invoice_to_print.getCode())
				if invoice_document is None:
					invoice_document = InvoiceDocument(
						invoice_id=invoice_id, enterprise_id=invoice_to_print.enterprise_id,
						revised_on=invoice_to_print.last_modified_on, revised_by=invoice_to_print.last_modified_by)
				invoice_document.invoice = invoice_to_print
			else:
				if os.path.exists(getAbsolutePath(temp_doc_path)):
					os.remove(getAbsolutePath(temp_doc_path))
				logger.info("Writing persisted Doc in temp path...")
				writeFile(invoice_document.document_pdf, temp_doc_path)
			if document_regenerate == "true":
				if inv_config_details.template_id == 3:
					InvoicePDFGenerator.addBackgroundImage(ELEGANT_BACKGROUND_DOC_PATH, temp_doc_path)
			elif invoice_document and invoice_document.document_pdf:
				logger.info("Document approved...")
			else:
				if inv_config_details.template_id == 3:
					InvoicePDFGenerator.addBackgroundImage(ELEGANT_BACKGROUND_DOC_PATH, temp_doc_path)
			if invoice_document.invoice.status == Invoice.STATUS_CANCELLED and (
								invoice_document is None or invoice_document.document_pdf is None or len(invoice_document.document_pdf) == 0):
				# Adding Water-mark for the rejected PO for the first time
				InvoicePDFGenerator.addCancelWaterMark(temp_doc_path)
			invoice_document.document_pdf = readFile(temp_doc_path)

			if invoice_to_print.status != Invoice.STATUS_DRAFT:
				self.invoice_dao.db_session.add(invoice_document)
				self.invoice_dao.db_session.commit()
				self.invoice_dao.db_session.refresh(invoice_document)
			else:
				make_transient(invoice_document)
				make_transient(invoice_to_print)
				self.invoice_dao.db_session.rollback()
			if labels and labels != []:
				InvoicePDFGenerator.addLabel(
					temp_doc_path, labels, document_generator, source=invoice_to_print, config_info=inv_config_details,
					enterprise=enterprise, logged_in_user=user, invoice_id=invoice_id, doc_status=doc_status)
		except Exception as e:
			self.invoice_dao.db_session.rollback()
			logger.exception('Invoice Document creation failed - %s' % str(e))
		return invoice_document, invoice_to_print.getCode()

	def generatePreviewInvoiceDocument(
			self, invoice_id=None, user=None, inv_config_details=None, updated_banner_image=None, db_session=None, enterprise_id=None):
		"""
		Fetches the document persisted for a given receipt_no. If such a document is not available in DB, fetches the
		Receipt details and generates the respective document.

		:param invoice_id:
		:param user:
		:param inv_config_details:
		:param updated_banner_image:
		:param db_session:
		:param enterprise_id:
		:return: ReceiptDocument instance holding the document in PDF format queried (or generated)
		"""
		logger.info('Generating documents for preview pdf of Invoice id: %s' % invoice_id)
		preview_documents = []
		if not db_session:
			db_session = self.invoice_dao.db_session
		try:
			invoice_to_print = db_session.query(Invoice).filter(Invoice.id == invoice_id).first()
			enterprise = db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
			# Preview for single Page
			total_items = len(invoice_to_print.items)
			no_of_items_needed = 6
			logger.info(no_of_items_needed)
			if total_items > no_of_items_needed:
				no_of_items_need_to_remove = total_items - no_of_items_needed
				if len(invoice_to_print.items) > no_of_items_need_to_remove:
					invoice_to_print.items = invoice_to_print.items[:len(invoice_to_print.items) - no_of_items_need_to_remove]

			temp_rev_doc_path = getFormattedRevisedDocPath(
				code=invoice_to_print.getCode(), id=invoice_id, suffix="_single_page_preview")
			document_generator = InvoicePDFGenerator(inv_template_config=inv_config_details, target_file_path=temp_rev_doc_path)
			document_pdf = document_generator.generatePDF(
					source=invoice_to_print, config_info=inv_config_details, enterprise=enterprise, logged_in_user=user,
					invoice_id=invoice_id, doc_status="PREVIEW", updated_banner_image=updated_banner_image)
			logger.info('Invoice to be printed: %s' % invoice_to_print.getCode())
			writeFile(document_pdf, temp_rev_doc_path)
			if inv_config_details.template_id == 3:
				InvoicePDFGenerator.addBackgroundImage(ELEGANT_BACKGROUND_DOC_PATH, temp_rev_doc_path)
			preview_documents.append(readFile(temp_rev_doc_path))

			# Preview for multiple Page
			if not inv_config_details.template_misc_details.include_first_page_summary:
				no_of_items_needed = 12
			total_items_multi_page = (no_of_items_needed * 2) - (len(invoice_to_print.items))
			db_session.refresh(invoice_to_print)
			if len(invoice_to_print.items) > 0:
				for i in range(total_items_multi_page):
					material = copy.deepcopy(invoice_to_print.items[0])
					item = InvoiceMaterial(
						invoice_id=invoice_id, item_id="%s%s" % (material.item_id, i), enterprise_id=enterprise.id,
						quantity=material.quantity, rate=material.rate + i, discount=material.discount,
						remarks=material.remarks, make_id=material.make_id)
					item.item = Material(
						drawing_no="%s%s" % (material.item_id, i), name="Material_%s" % i, description=None, price=material.rate + i,
						in_use=True, unit_id=None, category_id=None, enterprise_id=None, created_on=None,
						tariff_no=None, material_id=None, is_stocked=True, minimum_stock_level=0.000)
					invoice_to_print.items.append(item)

			temp_rev_doc_path = getFormattedRevisedDocPath(
				code=invoice_to_print.getCode(), id=invoice_id, suffix="_multiple_page_preview")
			document_generator = InvoicePDFGenerator(inv_template_config=inv_config_details, target_file_path=temp_rev_doc_path)
			document_pdf = document_generator.generatePDF(
					source=invoice_to_print, config_info=inv_config_details, enterprise=enterprise, logged_in_user=user,
					invoice_id=invoice_id, doc_status="PREVIEW", updated_banner_image=updated_banner_image)
			logger.info('Invoice to be printed: %s' % invoice_to_print.getCode())
			writeFile(document_pdf, temp_rev_doc_path)
			if inv_config_details.template_id == 3:
				InvoicePDFGenerator.addBackgroundImage(ELEGANT_BACKGROUND_DOC_PATH, temp_rev_doc_path)
			preview_documents.append(readFile(temp_rev_doc_path))
		except Exception as e:
			logger.exception('Invoice Document creation failed - %s' % str(e))
		return preview_documents

	def generateOADocument(self, enterprise_id=None, oa_id=None, document_regenerate=False):
		"""

		:param enterprise_id:
		:param oa_id:
		:param document_regenerate:
		:return:
		"""
		template_config = PurchaseTemplateConfigService().purchase_template_dao.getTemplateConfig(
			enterprise_id=enterprise_id, collection='order_acknowledgement_template_config')
		if template_config["print_template"] is None or template_config["print_template"].strip() == "":
			return None
		self.invoice_dao.db_session.begin(subtransactions=True)
		try:
			oa_to_print = self.invoice_dao.getOA(enterprise_id=enterprise_id, oa_id=oa_id)
			logger.debug('Order to be printed - %s' % oa_to_print)
			oa_revisions = oa_to_print.revisions
			oa_documents = oa_to_print.documents
			oa_document = oa_documents[0] if len(oa_documents) > 0 else None
			temp_doc_path = getFormattedDocPath(code=oa_to_print.getCode(), id=oa_id)
			temp_rev_doc_path = getFormattedRevisedDocPath(code=oa_to_print.getCode(), id=oa_id, suffix="-rev")
			pdf_generator = OrderAcknowlegdementPDFGenerator(order_acknowledgement=oa_to_print, target_file_path=temp_doc_path)
			if document_regenerate == "true" or oa_document is None or oa_to_print.documents is None or len(
					oa_to_print.documents) < 1:
				logger.info('Document not persisted for Order Acknowledgement: %s' % oa_id)
				document_pdf = pdf_generator.generatePDF(source=oa_to_print, template_config=template_config)
				writeFile(document_pdf, temp_doc_path)
				if oa_document is None:
					oa_document = OADocument(
						oa_id=oa_to_print.id, document_pdf=readFile(temp_doc_path),
						revised_on=oa_to_print.last_modified_on,
						revised_by=oa_to_print.approved_by, enterprise_id=enterprise_id)
			else:
				# logger.info('Printed the Order Acknowledgement - %s' % oa_to_print)
				writeFile(oa_to_print.documents[0].document_pdf, temp_doc_path)
			for revised_doc in oa_revisions:
				writeFile(revised_doc.document_pdf, temp_rev_doc_path)
				pdf_generator.addWaterMark(file(getAbsolutePath(CANCEL_WATERMARK_DOC_PATH), "rb"),
										   getAbsolutePath(temp_rev_doc_path))
				pdf_generator._appendToPDF(getAbsolutePath(temp_rev_doc_path), getAbsolutePath(temp_doc_path))
				logger.info("File to download: %s" % getAbsolutePath(temp_doc_path))
			if ((oa_to_print.status == OA.STATUS_CANCELLED and not oa_document.is_rejected) or (
					oa_to_print.status == OA.STATUS_REJECTED and not oa_document.is_rejected)):
				# Adding Water-mark for the rejected OA for the first time
				logger.info(
					"Order Acknowledgment Status: %s Order Acknowledgement Doc Rejected Status: %s" % (
						oa_to_print.status, oa_document.is_rejected))
				OAPDFGenerator.addCancelWaterMark(target_file_path=temp_doc_path)
				oa_document.document_pdf = readFile(temp_doc_path)
				oa_document.is_rejected = True
			if len(oa_revisions) > 0 or not oa_document:
				oa_document = OADocument(
					oa_id=oa_to_print.id, document_pdf=readFile(temp_doc_path))
			oa_document.document_pdf = readFile(temp_doc_path)
			oa_document.enterprise_id = enterprise_id
			if oa_to_print.oa_no and oa_to_print.oa_no != '0':
				logger.info('Persisting Document for Order Acknowledgement: %s' % oa_to_print.oa_no)
				self.invoice_dao.db_session.add(oa_document)
				self.invoice_dao.db_session.commit()
			else:
				make_transient(oa_document)
				make_transient(oa_to_print)
				self.invoice_dao.db_session.rollback()
			logger.info('Generating Rendering Order Acknowledgmeent PDF... %s' % oa_to_print.id)
			return oa_document
		except Exception as e:
			self.invoice_dao.db_session.rollback()
			logger.exception('Order Acknowledgement PDF generation failed %s' % str(e))

	def notifyPendingInVoiceApprovalCount(
			self, enterprise_id=None, sender_id=None, include_sender=True, code=None, type=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param include_sender:
		:param code:
		:param type:
		:return:
		"""
		try:
			count = self.invoice_dao.getPendingInvoiceCount(enterprise_id=enterprise_id)
			message = None
			if count == 1:
				message = "1 Invoice is pending for approval"
			elif count > 1:
				message = "%s Invoices are pending for approval" % count
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="SALES",
				message=message, collapse_key="sales_verified_count", include_sender=include_sender, code=code, type=type)
		except Exception as e:
			logger.error("Failed notification.. %s " % str(e))

	def notifyInvoiceApproveCount(self, enterprise_id=None, sender_id=None, include_sender=False, invoice=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param include_sender:
		:param invoice:
		:return:
		"""
		try:
			grand_total = "%s %s " % (invoice.currency.code, float(invoice.grand_total))
			customer = self.invoice_dao.getCustomerDetails(enterprise_id=enterprise_id, party_id=invoice.party_id)
			message = PUSH_NOTIFICATION["invoice_approved"] % (invoice.getCode(), customer.name, grand_total)
			logger.info('invoice_approved: %s' % invoice.type)
			self.notifyPendingVoucherCount(
				enterprise_id=enterprise_id, sender_id=sender_id, code=invoice.getCode(), type=invoice.type)
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="SALES", message=message,
				include_sender=include_sender, code=invoice.getCode(), type=invoice.type)
			self.notifyPendingInVoiceApprovalCount(
				enterprise_id=enterprise_id, sender_id=sender_id, code=invoice.getCode(), type=invoice.type)
		except Exception as e:
			logger.error("Failed notification.. %s " % str(e))

	def notifySaveIssueCount(self, enterprise_id=None, sender_id=None, issue=None, include_sender=True):
		"""
		:param enterprise_id:
		:param sender_id:
		:param issue:
		:param include_sender:
		:return:
		"""

		try:
			message = PUSH_NOTIFICATION["issues"] % (issue.getCode(), issue.issued_for, issue.issued_to)
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="STORES", message=message,
				include_sender=include_sender, code=issue.getCode(), type=issue.type)

		except Exception as e:
			logger.error("Failed notification.. %s " % str(e))

	def notificationInvoiceRejectCount(self, enterprise_id=None, sender_id=None, include_sender=False, invoice=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param include_sender:
		:param invoice:
		:return:
		"""
		try:
			grand_total = "%s %s " % (invoice.currency.code, invoice.grand_total)
			customer = self.invoice_dao.getCustomerDetails(enterprise_id=enterprise_id, party_id=invoice.party_id)
			message = PUSH_NOTIFICATION["invoice_rejected"] % (invoice.getCode(), customer.name, grand_total)
			self.notifyPendingInVoiceApprovalCount(enterprise_id=enterprise_id, sender_id=sender_id,code=invoice.getCode(),type=invoice.type)
			self.notifyPendingVoucherCount(enterprise_id=enterprise_id, sender_id=sender_id,code=invoice.getCode(),type=invoice.type)
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="SALES", message=message,
				include_sender=include_sender, code=invoice.getCode(), type=invoice.type)
		except Exception as e:
			logger.error("Failed notification.. %s " % str(e))

	def notifyPendingOACount(self, enterprise_id=None, sender_id=None, include_sender=True, code=None, type=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param include_sender:
		:param code:
		:param type:
		:return:
		"""
		try:
			count = self.invoice_dao.getPendingOACount(enterprise_id=enterprise_id)
			message = None
			if count == 1:
				message = "1 Order acknowledgement is pending for approval"
			elif count > 1:
				message = "%s Order acknowledgement are pending for approval" % count
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="SALES",
				message=message, collapse_key="order_ack_count", include_sender=include_sender, code=code, type=type)
		except Exception as e:
			logger.error("Failed notification.. %s " % str(e))

	def notifyPendingVoucherCount(self, enterprise_id=None, sender_id=None, include_sender=True, code=None, type=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param include_sender:
		:param code:
		:param type:
		:return:
		"""
		try:
			count = self.invoice_dao.getPendingVoucherCount(enterprise_id=enterprise_id)
			message = None
			if count == 1:
				message = "1 Voucher is pending for approval"
			elif count > 1:
				message = "%s Voucher are pending for approval" % count
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="ACCOUNTS",
				message=message, collapse_key="voucher_count", include_sender=include_sender, code=code, type=type)
		except Exception as e:
			logger.error("Failed notification.. %s " % str(e))

	def notifyOAapproveCount(
			self, enterprise_id=None, sender_id=None, include_sender=False, oa=None, code=None, type=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param include_sender:
		:param oa:
		:param code:
		:param type:
		:return:
		"""
		try:
			grand_total = "%s %s " % (oa.currency.code, oa.grand_total)
			customer = self.invoice_dao.getCustomerDetails(enterprise_id=enterprise_id, party_id=oa.party_id)
			message = PUSH_NOTIFICATION["OA_approved"] % (oa.getCode(), customer.name, grand_total)
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="SALES", message=message,
				include_sender=include_sender, code=code, type=type)
			self.notifyPendingOACount(enterprise_id=enterprise_id, sender_id=sender_id, code=code, type=type)
		except Exception as e:
			logger.error("Failed notification.. %s " % str(e))

	def notifyOArejectCount(self, enterprise_id=None, sender_id=None, include_sender=False, oa=None, code=None, type=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param include_sender:
		:param oa:
		:param code:
		:param type:
		:return:
		"""
		try:
			grand_total = "%s %s " % (oa.currency.code, oa.grand_total)
			customer = self.invoice_dao.getCustomerDetails(enterprise_id=enterprise_id, party_id=oa.party_id)
			message = PUSH_NOTIFICATION["OA_rejected"] % (oa.getCode(), customer.name, grand_total)
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="SALES", message=message,
				include_sender=include_sender, code=code, type=type)
			self.notifyPendingOACount(enterprise_id=enterprise_id, sender_id=sender_id, code=code, type=type)
		except Exception as e:
			logger.error("Failed notification.. %s " % str(e))

	def notifyPendingDeliverChallanCount(self, enterprise_id=None, sender_id=None, include_sender=True, code=None, type=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param include_sender:
		:param code:
		:param type:
		:return:
		"""
		try:
			count = self.invoice_dao.getPendingDeliveryChallanCount(enterprise_id=enterprise_id)
			message = None
			if count == 1:
				message = "1 Delivery challan is pending for approval"
			elif count > 1:
				message = "%s Delivery challan are pending for approval" % count
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="STORES",
				message=message, collapse_key="delivery_challan_count", include_sender=include_sender, code=code, type=type)
		except Exception as e:
			logger.error("Failed notification.. %s " % str(e))

	def notifyDeliveryChallanApproveCount(self, enterprise_id=None, sender_id=None, include_sender=False, invoice=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param include_sender:
		:param invoice:
		:return:
		"""
		try:
			grand_total = "%s %s " % (invoice.currency.code, invoice.grand_total)
			customer = self.invoice_dao.getCustomerDetails(enterprise_id=enterprise_id, party_id=invoice.party_id)
			message = PUSH_NOTIFICATION["delivery_jobwork_approved"] % (invoice.getCode(), customer.name, grand_total)
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="STORES", message=message,
				include_sender=include_sender, code=invoice.getCode(), type=invoice.type)
			self.notifyPendingDeliverChallanCount(
				enterprise_id=enterprise_id, sender_id=sender_id, code=invoice.getCode(), type=invoice.type)
		except Exception as e:
			logger.error("Failed notification.. %s " % str(e))

	def notifyDeliveryChallanRejectCount(self, enterprise_id=None, sender_id=None, include_sender=False, invoice=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param include_sender:
		:param invoice:
		:return:
		"""
		try:
			grand_total = "%s %s " % (invoice.currency.code, invoice.grand_total)
			customer = self.invoice_dao.getCustomerDetails(enterprise_id=enterprise_id, party_id=invoice.party_id)
			message = PUSH_NOTIFICATION["delivery_jobwork_rejected"] % (
				invoice.getCode(), customer.name, grand_total)
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="STORES", message=message,
				include_sender=include_sender, code=invoice.getCode(), type=invoice.type)
			self.notifyPendingDeliverChallanCount(
				enterprise_id=enterprise_id, sender_id=sender_id, code=invoice.getCode(), type=invoice.type)
		except Exception as e:
			logger.error("Failed notification.. %s " % str(e))

	def superEditInvoiceCode(
			self, enterprise_id=None, user_id=None, invoice_id=None, new_invoice_code=None, new_invoice_no=None):
		"""

		:return: [is_success, json_response]
		"""
		self.invoice_dao.db_session.begin(subtransactions=True)
		try:
			invoice_to_be_modified = self.invoice_dao.getInvoice(
				enterprise_id=enterprise_id, invoice_id=invoice_id)
			old_code = invoice_to_be_modified.invoice_code
			old_serial_number = invoice_to_be_modified.invoice_no
			invoice_type_name = "Invoice"
			if invoice_to_be_modified.type in ("DC", "JDC", "JIN"):
				invoice_type_name = invoice_to_be_modified.type
			if invoice_to_be_modified.type in ('DC', 'JDC', 'JIN'):
				types = ('DC', 'JDC', 'JIN')
			elif invoice_to_be_modified.type == 'Issue':
				types = 'Issue'
			else:
				types = ('Trading', 'GST', 'Excise', 'BoS', 'Service')
			existing_invoice = None
			if new_invoice_code != invoice_to_be_modified.invoice_code:
				existing_invoice = self.invoice_dao.db_session.query(Invoice).filter(
					Invoice.enterprise_id == enterprise_id, Invoice.financial_year == invoice_to_be_modified.financial_year,
					Invoice.type.in_(types), Invoice.invoice_code == new_invoice_code).first()
			response = response_code.failure()
			response['code'] = old_code
			if existing_invoice:
				response['custom_message'] = "A %s with Code '%s' already exists. Please assign a different Code!" % (
					invoice_type_name, existing_invoice.invoice_code)

			elif invoice_to_be_modified.invoice_code == new_invoice_code and invoice_to_be_modified.invoice_no == int(new_invoice_no):
				response['custom_message'] = "No changes detected in %s code to save!" % invoice_type_name
			else:
				invoice_to_be_modified.last_modified_on = datetime.now()
				invoice_to_be_modified.last_modified_by = user_id
				invoice_to_be_modified.super_modified_on = datetime.now()
				invoice_to_be_modified.super_modified_by = user_id
				invoice_to_be_modified.invoice_code = new_invoice_code
				invoice_to_be_modified.invoice_no = new_invoice_no
				self.invoice_dao.db_session.add(invoice_to_be_modified)
				response = response_code.success()
				custom_message = ""
				message = ""
				inv_code_update_message = "Successfully changed the {inv_type} Code from {from_code_value} to {to_code_value}!"
				inv_sno_update_message = "Successfully changed the {inv_type} Serial number from {from_inv_no} to {to_inv_no}!"

				if old_code != new_invoice_code and old_serial_number != int(new_invoice_no):
					custom_message = inv_code_update_message + " & " + inv_sno_update_message
					message = PUSH_NOTIFICATION['invoice_code'] + " & " + PUSH_NOTIFICATION['invoice_serial_no']
				elif old_code != new_invoice_code:
					custom_message = inv_code_update_message
					message = PUSH_NOTIFICATION['invoice_code']
				elif old_serial_number != int(new_invoice_no):
					custom_message = inv_sno_update_message
					message = PUSH_NOTIFICATION['invoice_serial_no']

				message = message.format(
					inv_type=invoice_type_name, from_code_value=old_code, to_code_value=invoice_to_be_modified.invoice_code,
					from_inv_no=old_serial_number, to_inv_no=invoice_to_be_modified.invoice_no)
				push_notification(
					enterprise_id=enterprise_id, sender_id=user_id, module_code="Invoice", message=message,
					code=invoice_to_be_modified.getCode(), type=invoice_to_be_modified.type)
				response['code'] = invoice_to_be_modified.invoice_code
				response['custom_message'] = custom_message.format(
					inv_type=invoice_type_name, from_code_value=old_code, to_code_value=invoice_to_be_modified.invoice_code,
					from_inv_no=old_serial_number, to_inv_no=invoice_to_be_modified.invoice_no)
			self.invoice_dao.db_session.commit()
			return response
		except Exception as e:
			self.invoice_dao.db_session.rollback()
			raise

	def superEditIssueCode(
			self, enterprise_id=None, user_id=None, invoice_id=None, new_financial_year=None,
			invoice_type=None, new_invoice_no=None, new_sub_number=None):
		"""

		:return: [is_success, json_response]
		"""
		self.invoice_dao.db_session.begin(subtransactions=True)
		try:
			invoice_to_be_modified = self.invoice_dao.getInvoice(enterprise_id=enterprise_id, invoice_id=invoice_id)
			old_code = invoice_to_be_modified.getCode()
			invoice_type_name = invoice_to_be_modified.type
			if new_sub_number is not None and new_sub_number.strip() == "":
				new_sub_number = None
			existing_invoice = self.invoice_dao.getInvoice(
				enterprise_id=enterprise_id, financial_year=new_financial_year, invoice_type=invoice_type,
				invoice_no=new_invoice_no, sub_number=new_sub_number)
			response = response_code.failure()
			response['code'] = old_code
			if not existing_invoice:
				invoice_to_be_modified.financial_year = new_financial_year
				invoice_to_be_modified.type = invoice_type
				invoice_to_be_modified.invoice_no = new_invoice_no
				invoice_to_be_modified.sub_number = new_sub_number
				invoice_to_be_modified.last_modified_on = datetime.now()
				invoice_to_be_modified.last_modified_by = user_id
				invoice_to_be_modified.super_modified_on = datetime.now()
				invoice_to_be_modified.super_modified_by = user_id
				self.invoice_dao.db_session.add(invoice_to_be_modified)
				response = response_code.success()
				response['custom_message'] = "Successfully changed the %s Code from '%s' to '%s'!" % (
					invoice_type_name, old_code, invoice_to_be_modified.getCode())
				message = PUSH_NOTIFICATION['invoice_code'].format(
					inv_type=invoice_type_name, from_code_value=old_code, to_code_value=invoice_to_be_modified.getCode())
				push_notification(
					enterprise_id=enterprise_id, sender_id=user_id, module_code="Invoice", message=message,
					code=invoice_to_be_modified.getCode(), type=invoice_to_be_modified.type)
				response['code'] = invoice_to_be_modified.getCode()
				invoice_to_be_modified.invoice_code = invoice_to_be_modified.getCode()
			elif invoice_to_be_modified.id == existing_invoice.id:
				response['custom_message'] = "No changes detected in %s code to save!" % invoice_type_name
			else:
				response['custom_message'] = "A %s with Code '%s' already exists. Please assign a different Code!" % (
					invoice_type_name, existing_invoice.getCode())
			self.invoice_dao.db_session.commit()
			return response
		except Exception as e:
			self.invoice_dao.db_session.rollback()
			raise

	def superEditOACode(
			self, enterprise_id=None, user_id=None, oa_id=None, new_financial_year=None,
			new_oa_type=None, new_oa_no=None, new_sub_number=None, is_primary = True):
		"""

		:return: [is_success, json_response]
		"""
		self.invoice_dao.db_session.begin(subtransactions=True)
		try:
			oa_to_be_modified = self.invoice_dao.getOA(enterprise_id=enterprise_id, oa_id=oa_id)
			old_code = oa_to_be_modified.getCode()
			if new_sub_number is not None and new_sub_number.strip() == "":
				new_sub_number = None
			existing_oa = self.invoice_dao.getOA(
				enterprise_id=enterprise_id, financial_year=new_financial_year, oa_type=new_oa_type,
				oa_no=new_oa_no, sub_number=new_sub_number)
			response = response_code.failure()
			response['code'] = old_code
			word = "OA" if is_primary else "IWO"

			if not existing_oa:
				oa_to_be_modified.financial_year = new_financial_year
				oa_to_be_modified.oa_no = new_oa_no
				oa_to_be_modified.type = new_oa_type
				oa_to_be_modified.sub_number = new_sub_number
				oa_to_be_modified.super_modified_on = datetime.now()
				oa_to_be_modified.super_modified_by = user_id
				oa_to_be_modified.last_modified_on = datetime.now()
				oa_to_be_modified.last_modified_by = user_id
				self.invoice_dao.db_session.add(oa_to_be_modified)
				response = response_code.success()
				response['custom_message'] = "Successfully changed the %s Code from '%s' to '%s'!" % (word,
					old_code, oa_to_be_modified.getCode())
				response['code'] = oa_to_be_modified.getCode()
				message = PUSH_NOTIFICATION['oa_code'] % (old_code, oa_to_be_modified.getCode())
				push_notification(
					enterprise_id=enterprise_id, sender_id=user_id, module_code="OA", message=message,
					code=oa_to_be_modified.getCode(), type=oa_to_be_modified.type)
			elif oa_to_be_modified.id == existing_oa.id:
				response['custom_message'] = "No changes detected in %s code to save!" % word
			else:
				response['custom_message'] = "An %s with Code '%s' already exists. Please assign a different Code!" % (
					word, existing_oa.getCode())
			self.invoice_dao.db_session.commit()
			return response
		except Exception as e:
			self.invoice_dao.db_session.rollback()
			raise

	def superEditSECode(
			self, enterprise_id=None, user_id=None, se_id=None, new_financial_year=None,
			new_se_type=None, new_se_no=None, new_sub_number=None):
		"""

		:return: [is_success, json_response]
		"""
		self.invoice_dao.db_session.begin(subtransactions=True)
		try:
			se_to_be_modified = self.invoice_dao.getSalesEstimate(enterprise_id=enterprise_id, se_id=se_id)
			old_code = se_to_be_modified.getCode()
			if new_sub_number is not None and new_sub_number.strip() == "":
				new_sub_number = None
			existing_se = self.invoice_dao.getSalesEstimate(
				enterprise_id=enterprise_id, financial_year=new_financial_year, se_type=new_se_type,
				se_no=new_se_no, sub_number=new_sub_number)
			response = response_code.failure()
			response['code'] = old_code
			if not existing_se:
				se_to_be_modified.financial_year = new_financial_year
				se_to_be_modified.se_no = new_se_no
				se_to_be_modified.type = new_se_type
				se_to_be_modified.sub_number = new_sub_number
				se_to_be_modified.super_modified_on = datetime.now()
				se_to_be_modified.super_modified_by = user_id
				se_to_be_modified.last_modified_on = datetime.now()
				se_to_be_modified.last_modified_by = user_id
				self.invoice_dao.db_session.add(se_to_be_modified)
				response = response_code.success()
				response['custom_message'] = "Successfully changed the Sales Estimate Code from '%s' to '%s'!" % (
					old_code, se_to_be_modified.getCode())
				response['code'] = se_to_be_modified.getCode()
				message = PUSH_NOTIFICATION['se_code'] % (old_code, se_to_be_modified.getCode())
				push_notification(
					enterprise_id=enterprise_id, sender_id=user_id, module_code="SE", message=message,
					code=se_to_be_modified.getCode(), type=se_to_be_modified.type)
			elif se_to_be_modified.id == existing_se.id:
				response['custom_message'] = "No changes detected in Sales Estimate code to save!"
			else:
				response['custom_message'] = "A Sales Estimate with Code '%s' already exists. Please assign a different Code!" % (
					existing_se.getCode())
			self.invoice_dao.db_session.commit()
			return response
		except Exception as e:
			self.invoice_dao.db_session.rollback()
			raise

	def getDocumentLinkedCodes(self, enterprise_id=None, po_id=None):
		"""

		:return:
		"""
		try:
			invoices = self.invoice_dao.db_session.query(Invoice).join(Invoice.items).filter(
				Invoice.job_po_id == po_id, Invoice.enterprise_id == enterprise_id,
				Invoice.status >= Receipt.STATUS_DRAFT).group_by(Invoice.id).all()
			invoice_codes = [invoice.getCode() for invoice in invoices]
			store_service = StoresService()
			grn_codes = []
			for invoice in invoices:
				grn_codes = grn_codes + store_service.getReceiptCodes(enterprise_id=enterprise_id, invoice_id=invoice.id)
			return invoice_codes, grn_codes
		except Exception as e:
			raise

	def getDCLinkedCodes(self, enterprise_id=None, invoice_id=None):
		"""

		:return:
		"""
		try:
			invoices = self.invoice_dao.db_session.query(Invoice).join(InvoiceMaterial, and_(
				Invoice.enterprise_id == enterprise_id, Invoice.id == InvoiceMaterial.invoice_id)).filter(
				InvoiceMaterial.delivered_dc_id == invoice_id, Invoice.status >= Invoice.STATUS_DRAFT).group_by(
				Invoice.id).all()
			invoice_codes = [invoice.getCode() for invoice in invoices]
			if invoice_codes:
				return invoice_codes
		except Exception as e:
			raise

	def getOAMaterials(self, enterprise_id=None, oa_id=None):
		"""

		:return:
		"""
		try:
			query = """SELECT m.drawing_no as drawing_no, m.id as cat_code, m.name as name, 
			m.description as description, u.unit_name as unit, m.price as price, m.unit as unit_id, 
			m.minimum_stock_level as minimum_stock_level, oap.enterprise_id as enterprise_id,oap.quantity AS qty, 
			m.is_stocked as material_type, IFNULL(m.tariff_no, "") as hsn_code, 
			m.makes_json AS make_name, oap.make_id AS make_id, 0 AS is_faulty, '' AS invoice_id, m.id as item_id,
			IFNULL(oap.alternate_unit_id, 0) as alternate_unit_id, m.is_service as is_service     
			FROM oa_particulars as oap
			LEFT JOIN materials as m ON oap.item_id = m.id AND oap.enterprise_id = m.enterprise_id
			LEFT JOIN unit_master as u ON u.enterprise_id = m.enterprise_id AND u.unit_id = m.unit						
			WHERE oap.oa_id={oa_id} AND oap.enterprise_id = {enterprise_id} GROUP BY drawing_no , name , make_id , 
			is_faulty""".format(oa_id=oa_id, enterprise_id=enterprise_id)
			response = list(dao.executeQuery(query, as_dict=True))
			for material in response:
				item_name = material['name']
				if material['make_name']:
					make_name = helper.constructDifferentMakeName(material['make_name'])
					if make_name:
						item_name = item_name + " [" + make_name + "]"
						material['name'] = item_name
				if material['alternate_unit_id'] and material['alternate_unit_id'] != 0:
					scale_factor = helper.getScaleFactor(
						enterprise_id=enterprise_id, item_id=material['item_id'],
						alternate_unit_id=material['alternate_unit_id'])
					if scale_factor:
						material['qty'] = Decimal(material['qty']) / Decimal(scale_factor) if material['qty'] else 0
						material['unit'] = helper.getUnitName(enterprise_id=enterprise_id,
						                                      unit_id=material['alternate_unit_id'])
			return response
		except Exception as e:
			raise

	def getPOMaterials(self, enterprise_id=None, po_id=None):
		"""

		:return:
		"""
		try:
			query = """SELECT m.drawing_no as drawing_no, m.id as cat_code, m.name as name, 
			m.description as description, u.unit_name as unit, m.price as price, m.unit as unit_id, 
			m.minimum_stock_level as minimum_stock_level, pom.enterprise_id as enterprise_id,pom. pur_qty AS qty, 
			m.is_stocked as material_type, IFNULL(m.tariff_no, "") as hsn_code, 
			m.makes_json AS make_name, pom.make_id AS make_id, 0 AS is_faulty, '' AS invoice_id, m.id as item_id,
			IFNULL(pom.alternate_unit_id, 0) as alternate_unit_id, m.is_service as is_service,			
			CONCAT(m.name, ' - ', m.drawing_no)  AS item_name		
			FROM purchase_order_material as pom
			LEFT JOIN materials as m ON pom.item_id = m.id AND pom.enterprise_id = m.enterprise_id
			LEFT JOIN unit_master as u ON u.enterprise_id = m.enterprise_id AND u.unit_id = m.unit			
			WHERE pom.pid={po_id} AND pom.enterprise_id = {enterprise_id}            
			GROUP BY drawing_no , name , make_id , 
			is_faulty""".format(po_id=po_id, enterprise_id=enterprise_id)
			response = list(dao.executeQuery(query, as_dict=True))
			for material in response:
				item_name = material['name']
				if material['make_name']:
					make_name = helper.constructDifferentMakeName(material['make_name'])
					if make_name:
						item_name = item_name + " [" + make_name + "]"
						material['name'] = item_name
				if material['alternate_unit_id'] and material['alternate_unit_id'] != 0:
					scale_factor = helper.getScaleFactor(
						enterprise_id=enterprise_id, item_id=material['item_id'],
						alternate_unit_id=material['alternate_unit_id'])
					if scale_factor:
						material['qty'] = Decimal(material['qty']) / Decimal(scale_factor) if material['qty'] else 0
						material['unit'] = helper.getUnitName(enterprise_id=enterprise_id,
						                                      unit_id=material['alternate_unit_id'])
			return response
		except Exception as e:
			raise

	def getGRNMaterials(self, enterprise_id=None, oa_id=None):
		"""

		:return:
		"""
		try:
			query = """SELECT m.drawing_no AS drawing_no, m.id AS cat_code, m.name AS name, m.description AS description, 
			u.unit_name AS unit, m.price AS price, m.unit AS unit_id, m.minimum_stock_level AS minimum_stock_level,
			gm.enterprise_id AS enterprise_id, SUM(gm.acc_qty) AS qty, m.is_stocked AS material_type, IFNULL(m.tariff_no, '') AS hsn_code,
			m.makes_json AS make_name, gm.make_id AS make_id, gm.is_faulty AS is_faulty, grn.grn_no AS invoice_id,m.id as item_id,
			IFNULL(gm.alternate_unit_id, 0) as alternate_unit_id, m.is_service as is_service	
			FROM grn 
			LEFT JOIN grn_material gm ON grn.grn_no = gm.grnNumber  
			AND grn.enterprise_id = gm.enterprise_id
			LEFT JOIN materials AS m ON gm.item_id = m.id AND gm.enterprise_id = m.enterprise_id
			LEFT JOIN unit_master AS u ON u.enterprise_id = m.enterprise_id AND u.unit_id = m.unit			
			WHERE gm.oa_id = {oa_id} AND grn.enterprise_id = {enterprise_id}
			AND grn.status >= 0
			GROUP BY gm.item_id, gm.make_id, gm.is_faulty""".format(oa_id=oa_id, enterprise_id=enterprise_id)
			response = list(dao.executeQuery(query, as_dict=True))
			for material in response:
				item_name = material['name']
				if material['make_name']:
					make_name = helper.constructDifferentMakeName(material['make_name'])
					if make_name:
						item_name = item_name + " [" + make_name + "]"
						material['name'] = item_name
				if material['alternate_unit_id'] and material['alternate_unit_id'] != 0:
					scale_factor = helper.getScaleFactor(
						enterprise_id=enterprise_id, item_id=material['item_id'],
						alternate_unit_id=material['alternate_unit_id'])
					if scale_factor:
						material['qty'] = Decimal(material['qty']) / Decimal(scale_factor) if material['qty'] else 0
			return response
		except Exception as e:
			raise

	def getGRNMaterialsNonOa(self, enterprise_id=None, grn_no=None):
		"""

		:return:
		"""
		try:
			query = """SELECT m.drawing_no AS drawing_no, m.id AS cat_code, m.name AS name, m.description AS description, 
			u.unit_name AS unit, m.price AS price, m.unit AS unit_id, m.minimum_stock_level AS minimum_stock_level,
			gm.enterprise_id AS enterprise_id, SUM(gm.acc_qty) AS qty, m.is_stocked AS material_type, IFNULL(m.tariff_no, '') AS hsn_code,
			m.makes_json AS make_name, gm.make_id AS make_id, gm.is_faulty AS is_faulty, grn.grn_no AS invoice_id,
			m.id as item_id, IFNULL(gm.alternate_unit_id, 0) as alternate_unit_id, m.is_service as is_service
			FROM grn 
			LEFT JOIN grn_material gm ON grn.grn_no = gm.grnNumber  
			AND grn.enterprise_id = gm.enterprise_id
			LEFT JOIN materials AS m ON gm.item_id = m.id AND gm.enterprise_id = m.enterprise_id
			LEFT JOIN unit_master AS u ON u.enterprise_id = m.enterprise_id AND u.unit_id = m.unit			
			AND m.enterprise_id = gm.enterprise_id
			WHERE grn.grn_no = {grn_no} AND gm.oa_id is null AND grn.enterprise_id = {enterprise_id}
			AND grn.status >= 0
			GROUP BY gm.item_id, gm.make_id, gm.is_faulty""".format(grn_no=grn_no, enterprise_id=enterprise_id)
			response = list(dao.executeQuery(query, as_dict=True))
			for material in response:
				item_name = material['name']
				if material['make_name']:
					make_name = helper.constructDifferentMakeName(material['make_name'])
					if make_name:
						item_name = item_name + " [" + make_name + "]"
						material['name'] = item_name
				if material['alternate_unit_id'] and material['alternate_unit_id'] != 0:
					scale_factor = helper.getScaleFactor(
						enterprise_id=enterprise_id, item_id=material['item_id'],
						alternate_unit_id=material['alternate_unit_id'])
					if scale_factor:
						material['qty'] = Decimal(material['qty']) / Decimal(scale_factor) if material['qty'] else 0
			return response
		except Exception as e:
			raise

	def getInvoiceMaterials(self, enterprise_id=None, po_id=None):
		"""

		:return:
		"""
		try:
			query = """SELECT m.drawing_no AS drawing_no, m.id AS cat_code, m.name AS name, m.description AS description, 
			u.unit_name AS unit, m.price AS price, m.unit AS unit_id, m.minimum_stock_level AS minimum_stock_level,
			im.enterprise_id AS enterprise_id, SUM(im.qty) AS qty, m.is_stocked AS material_type, IFNULL(m.tariff_no, '') AS hsn_code,
			m.makes_json AS make_name, im.make_id AS make_id, im.is_faulty AS is_faulty, invoice.id AS invoice_id,
			m.id as item_id, IFNULL(im.alternate_unit_id, 0) as alternate_unit_id, m.is_service as is_service
			FROM invoice 
			LEFT JOIN invoice_materials im ON invoice.id = im.invoice_id  
			AND invoice.enterprise_id = im.enterprise_id
			LEFT JOIN materials AS m ON im.item_id = m.id AND im.enterprise_id = m.enterprise_id
			LEFT JOIN unit_master AS u ON u.enterprise_id = m.enterprise_id AND u.unit_id = m.unit			
			AND m.enterprise_id = im.enterprise_id
			WHERE invoice.job_po_id = {po_id} AND invoice.enterprise_id = {enterprise_id}
			AND invoice.status >= 0
			GROUP BY im.item_id, im.make_id, im.is_faulty""".format(po_id=po_id, enterprise_id=enterprise_id)
			response = list(dao.executeQuery(query, as_dict=True))
			for material in response:
				item_name = material['name']
				if material['make_name']:
					make_name = helper.constructDifferentMakeName(material['make_name'])
					if make_name:
						item_name = item_name + " [" + make_name + "]"
						material['name'] = item_name
				if material['alternate_unit_id'] and material['alternate_unit_id'] != 0:
					scale_factor = helper.getScaleFactor(
						enterprise_id=enterprise_id, item_id=material['item_id'],
						alternate_unit_id=material['alternate_unit_id'])
					if scale_factor:
						material['qty'] = Decimal(material['qty']) / Decimal(scale_factor) if material['qty'] else 0
			return response
		except Exception as e:
			raise

	def getClubbedCatalogue(self, materials=None, enterprise_id=None):
		"""
		Get catalogue materials which grouped by material drawing_no not on make specified
		:param materials:
		:param enterprise_id:
		:return:
		"""
		multi_bom_mat = {}
		response = []
		make_list = ""
		make_arr = []
		query = """SELECT cat_materials.qty AS cat_materials_qty, materials.drawing_no 
			AS drawing_no,cat_materials.parent_id AS cat_code, materials.name AS materials_name, 
			materials.description AS materials_description, materials.price AS materials_price, 
			unit_master.unit_id AS unit_master_unit_id, unit_master.unit_name AS unit_master_unit_name, 
			("1") AS make_id, materials.makes_json AS make_make_name,
			materials.tariff_no AS materials_tariff_no, materials.id as item_id, materials.is_stocked as material_type,
			materials.is_service as is_service 
			FROM cat_materials INNER JOIN materials ON materials.id = cat_materials.item_id INNER JOIN 
			unit_master ON materials.unit = unit_master.unit_id AND materials.enterprise_id = unit_master.enterprise_id 
			LEFT OUTER JOIN catalogue_material_makes ON cat_materials.item_id = catalogue_material_makes.item_id 
			AND cat_materials.parent_id = catalogue_material_makes.parent_id  
			WHERE cat_materials.parent_id = '{cat_code}' AND materials.enterprise_id = {enterprise_id}
			GROUP BY cat_materials.item_id """
		try:
			for idx, material in enumerate(materials):
				child_list = []
				if material['cat_code']:
					queryset = query.format(cat_code=material['cat_code'], enterprise_id=enterprise_id)
					catalogues = dao.executeQuery(queryset, as_dict=True)
					if len(catalogues) > 0:
						for cat_child in catalogues:
							make_name = helper.constructDifferentMakeName(cat_child['make_make_name'])
							child = {'drawing_no': cat_child['drawing_no'], 'item_id': cat_child['item_id'],
							            'cat_code': cat_child['cat_code'],
									 	'name': cat_child['materials_name'] + " [" + make_name + "]" if make_name else cat_child['materials_name'],
							            'description': cat_child['materials_description'],
							            'unit': cat_child['unit_master_unit_name'],
							            'price': cat_child['materials_price'], 'unit_id': cat_child['unit_master_unit_id'],
							            'qty': cat_child['cat_materials_qty'] * material['qty'],
							            'material_type': cat_child['material_type'],
							            'is_service': cat_child['is_service'],
							            'make_id': cat_child['make_id'], 'make_name': cat_child['make_make_name'], 'makes': "",
							            'is_faulty': 0, 'invoice_id': '', 'actual_qty': 0, 'supplied': 0, 'bom_total_qty': cat_child['cat_materials_qty'] * material['qty'], 'enterprise_id': enterprise_id}
							#PHASE1 Get the sum of qty where same materials presents on different bom of materials list
							for idx_, material_ in enumerate(materials):
								if idx != idx_ and (material_['cat_code'] != cat_child['cat_code'] or (material_['cat_code'] == cat_child['cat_code'] and material_['make_id'] != cat_child['make_id'])) and material_['drawing_no'] != "-NA-":
									queryset = query.format(cat_code=material_['cat_code'], enterprise_id=enterprise_id)
									catalogues_ = dao.executeQuery(queryset, as_dict=True)
									for cat_child_ in catalogues_:
										if cat_child['item_id'] == cat_child_['item_id']:
											if len(set(cat_child['make_id'].split(",")).intersection((cat_child_['make_id'])))>0:
												child['bom_total_qty'] += (cat_child_['cat_materials_qty'] * material_['qty'])
												l = child['make_id'].split(",")
												[make_arr.append(i) for i in set(make_arr).intersection(l)] \
													if len(make_arr) > 0 else [make_arr.append(i) for i in l]
												multi_bom_mat.update({child['item_id']: (make_arr, child['bom_total_qty'])})
							#PHASE1 end
							child_list.append(child)
					make_list = [(material['make_id'], material['make_name'])]
				material['child'] = child_list
				material['makes'] = make_list
				material['make_name'] = material['make_name']
				material['supplied'] = 0
				response.append(material)
			return response, multi_bom_mat
		except Exception as e:
			raise

	def getCalcActualQty(self, po_id, po_material_lst, job_type):
		try:
			for material in po_material_lst:
				if isinstance(material['child'], list) and len(material['child']) > 0:
					for material_ in material['child']:
						if job_type == 'PO':
							material_ = self.getAQty(po_id=po_id, material_=material_)
						elif job_type == 'GRN':
							material = self.getSuppliedGRNQty(grn_no=po_id, material_=material)
						else:
							material = self.getSuppliedQty(oa_id=po_id, material_=material)
				if job_type == 'PO':
					material = self.getAQty(po_id=po_id, material_=material)
				elif job_type == 'GRN':
					material = self.getSuppliedGRNQty(grn_no=po_id, material_=material)
				else:
					material = self.getSuppliedQty(oa_id=po_id, material_=material)
			return po_material_lst
		except Exception as e:
			raise

	@staticmethod
	def getAQty(po_id, material_):
		try:
			query = """SELECT SUM(IFNULL(gm.acc_qty, 0)) AS accepted_qty,IFNULL(gm.alternate_unit_id, 0) as alternate_unit_id
					FROM grn_material gm
					LEFT JOIN grn ON grn.grn_no = gm.grnNumber
					AND grn.enterprise_id = gm.enterprise_id
					WHERE
					gm.po_no = {po_id} AND gm.item_id = '{item_id}' AND gm.make_id IN ({make_id}) AND gm.is_faulty = {is_faulty} 
					AND gm.enterprise_id = {enterprise_id} AND gm.dc_id != 0 AND grn.status > -1 AND grn.goods_already_received != 1""".format(
				po_id=po_id, item_id=material_['item_id'], make_id=material_['make_id'],
				is_faulty=material_['is_faulty'], enterprise_id=material_['enterprise_id'])
			result_set = dao.executeQuery(query, as_dict=True)
			for material in result_set:
				if material['alternate_unit_id'] and material['alternate_unit_id'] != 0:
					scale_factor = helper.getScaleFactor(
						enterprise_id=material_['enterprise_id'], item_id=material_['item_id'],
						alternate_unit_id=material['alternate_unit_id'])
					if scale_factor:
						material['accepted_qty'] = Decimal(material['accepted_qty']) / Decimal(scale_factor) if material['accepted_qty'] else 0
			if 'actual_qty' not in material_:
				material_.update({'actual_qty': 0})
			acc_qty = result_set[0]['accepted_qty'] if result_set[0]['accepted_qty'] != None else 0
			material_['actual_qty'] = float(material_['actual_qty'] - acc_qty)
			material_['returned'] = acc_qty
			return material_
		except Exception as e:
			raise

	@staticmethod
	def getSuppliedQty(oa_id, material_):
		try:
			# TODO Calculate the returned qty based on party DC No against OA NO...
			get_return_qty_query = """SELECT SUM(IFNULL(im.qty, 0)) AS returned_qty,
					IFNULL(im.alternate_unit_id, 0) as alternate_unit_id 
					FROM invoice_materials im
					LEFT JOIN invoice ON invoice.id = im.invoice_id
					AND invoice.enterprise_id = im.enterprise_id
					WHERE invoice.type = "JIN" AND
					im.oa_no = {oa_id} AND im.item_id = '{item_id}' AND im.make_id IN ({make_id}) 
					AND im.is_faulty = {is_faulty} AND im.enterprise_id = {enterprise_id} AND 
					invoice.status > -1 AND invoice.goods_already_supplied != 1""".format(
				oa_id=oa_id, item_id=material_['item_id'], make_id=material_['make_id'],
				is_faulty=material_['is_faulty'], enterprise_id=material_['enterprise_id'])
			result_set = dao.executeQuery(get_return_qty_query, as_dict=True)
			for material in result_set:
				if material['alternate_unit_id'] and material['alternate_unit_id'] != 0:
					scale_factor = helper.getScaleFactor(
						enterprise_id=material_['enterprise_id'], item_id=material_['item_id'],
						alternate_unit_id=material['alternate_unit_id'])
					if scale_factor:
						material['returned_qty'] = Decimal(material['returned_qty']) / Decimal(scale_factor) if material['returned_qty'] else 0

			material_['actual_qty'] = 0
			if 'actual_qty' not in material_:
				material_.update({'actual_qty': 0})
			ret_qty = result_set[0]['returned_qty'] if result_set[0]['returned_qty'] else 0
			material_['actual_qty'] = float(material_['actual_qty']) - float(ret_qty)
			material_['returned'] = float(ret_qty)
			return material_
		except Exception as e:
			raise

	@staticmethod
	def getSuppliedGRNQty(grn_no, material_):
		try:
			# TODO Calculate the returned qty based on party DC No against OA NO...
			get_return_qty_query = """SELECT SUM(IFNULL(im.qty, 0)) AS returned_qty,
					IFNULL(im.alternate_unit_id, 0) as alternate_unit_id
					FROM invoice_materials im
					LEFT JOIN invoice ON invoice.id = im.invoice_id
					AND invoice.enterprise_id = im.enterprise_id
					WHERE invoice.type = "JIN" AND
					im.grn_id = {grn_no} AND im.item_id = '{item_id}' AND im.make_id IN ({make_id}) 
					AND im.is_faulty = {is_faulty} AND im.enterprise_id = {enterprise_id} AND 
					invoice.status > -1 AND invoice.goods_already_supplied != 1""".format(
				grn_no=grn_no, item_id=material_['item_id'], make_id=material_['make_id'],
				is_faulty=material_['is_faulty'], enterprise_id=material_['enterprise_id'])
			result_set = dao.executeQuery(get_return_qty_query, as_dict=True)
			for material in result_set:
				if material['alternate_unit_id'] and material['alternate_unit_id'] != 0:
					scale_factor = helper.getScaleFactor(
						enterprise_id=material_['enterprise_id'], item_id=material_['item_id'],
						alternate_unit_id=material['alternate_unit_id'])
					if scale_factor:
						material['returned_qty'] = Decimal(material['returned_qty']) / Decimal(scale_factor) if material['returned_qty'] else 0
			material_['actual_qty'] = 0
			if 'actual_qty' not in material_:
				material_.update({'actual_qty': 0})
			ret_qty = result_set[0]['returned_qty'] if result_set[0]['returned_qty'] else 0
			material_['actual_qty'] = float(material_['actual_qty']) - float(ret_qty)
			material_['returned'] = float(ret_qty)
			return material_
		except Exception as e:
			raise

	def getInvoicedItemQuantity(
			self, item_id=None, make_id=None, is_faulty=None, enterprise_id=None, dc_id=None, invoice_id=None, hsn_code=None):
		"""

		:param item_id:
		:param make_id:
		:param is_faulty:
		:param enterprise_id:
		:param dc_id:
		:param invoice_id:
		:param hsn_code:
		:return:
		"""
		dc_pending_qty = 0
		try:
			invoiced_qty_query = self.invoice_dao.db_session.query(
				func.sum(InvoiceMaterial.quantity).label('invoiced_qty')).outerjoin(
				InvoiceMaterial.invoice)
			filters = [Invoice.status > -1]
			if item_id:
				filters.append(InvoiceMaterial.item_id == item_id)
			if make_id:
				filters.append(InvoiceMaterial.make_id == make_id)
			filters.append(InvoiceMaterial.is_faulty == is_faulty)
			if enterprise_id:
				filters.append(InvoiceMaterial.enterprise_id == enterprise_id)
			if dc_id:
				filters.append(InvoiceMaterial.delivered_dc_id == dc_id)
			if invoice_id:
				filters.append(InvoiceMaterial.invoice_id != invoice_id)
			if hsn_code:
				filters.append(InvoiceMaterial.hsn_code == hsn_code)
			result = invoiced_qty_query.filter(*filters).first()
			dc_pending_qty = result[0] if result[0] else 0
		except Exception as e:
			logger.error("Failed fetching DC material quantity... %s " % str(e))
		return dc_pending_qty

	def getDCCodeByInvoice(self, enterprise_id=None, invoice_id=None):
		"""

		:param enterprise_id:
		:param invoice_id:
		:return:
		"""
		try:
			dc_list = []
			financial_years_from_db = []
			if invoice_id:
				dcs = self.invoice_dao.db_session.query(Invoice).join(InvoiceMaterial, and_(
					InvoiceMaterial.delivered_dc_id == Invoice.id)).filter(
					Invoice.enterprise_id == enterprise_id, InvoiceMaterial.invoice_id == invoice_id,
					InvoiceMaterial.enterprise_id == Invoice.enterprise_id).group_by(Invoice.id).all()
				for dc in dcs:
					dc_list.append(dict(dc_id=dc.id, dc_code=dc.getInternalCode(), financial_year=dc.financial_year))
					if dc.financial_year not in financial_years_from_db:
						financial_years_from_db.append(dc.financial_year)
			return dc_list, financial_years_from_db
		except Exception as e:
			logger.error("Failed DC List... %s " % str(e))

	def getInvoicedItems(self, enterprise_id=None, dc_id=None):
		"""

		:param enterprise_id:
		:param dc_id:
		:return:
		"""
		item_list = []
		dc_list = self.invoice_dao.db_session.query(InvoiceMaterial).outerjoin(InvoiceMaterial.invoice).filter(
			InvoiceMaterial.delivered_dc_id == dc_id, InvoiceMaterial.enterprise_id == enterprise_id,
			Invoice.status > -1).all()
		if dc_list:
			for dc_mat in dc_list:
				if dc_mat.quantity > 0:
					make = helper.constructDifferentMakeName(dc_mat.item.makes_json)
					item_name = dc_mat.item.name
					if dc_mat.item.drawing_no:
						item_name += " - " + dc_mat.item.drawing_no
					if make:
						item_name += " [" + make + "]"
					if dc_mat.is_faulty == IS_FAULTY_TRUE:
						item_name += " [Faulty]"
					invoice_type = Invoice.TYPE_KEYS[dc_mat.invoice.type]
					item_list.append({
						"doc_no": dc_mat.invoice.getCode(),
						"doc_date": dc_mat.invoice.prepared_on.strftime(
							'%Y-%m-%d') if dc_mat.invoice.status == 0 else dc_mat.invoice.approved_on.strftime(
							'%Y-%m-%d'),
						"item_details": item_name, "invoice_no": dc_mat.invoice_id, "invoice_type": invoice_type,
						"quantity": dc_mat.quantity, "unit": dc_mat.item.unit.unit_name, "is_stock": dc_mat.item.is_stocked,
						"is_service": dc_mat.item.is_service})
			item_list = sorted(item_list, key=lambda a: a['doc_no'])
		return item_list

	def getReturnedItems(self, enterprise_id=None, dc_id=None):
		"""

		:param enterprise_id:
		:param dc_id:
		:return:
		"""
		dc_materials_list = []
		dc_list = self.invoice_dao.db_session.query(ReceiptMaterial).outerjoin(ReceiptMaterial.receipt).filter(
			ReceiptMaterial.dc_id == dc_id, ReceiptMaterial.enterprise_id == enterprise_id, Receipt.status > -1).all()
		if dc_list:
			for dc_mat in dc_list:
				if dc_mat.accepted_qty > 0:
					make = helper.constructDifferentMakeName(dc_mat.material.makes_json)
					item_name = dc_mat.material.name
					if dc_mat.material.drawing_no:
						item_name += " - " + dc_mat.material.drawing_no
					if make:
						item_name += " [" + make + "]"
					if dc_mat.is_faulty == IS_FAULTY_TRUE:
						item_name += " [Faulty]"
					quantity = float(dc_mat.accepted_qty)
					unit_name = dc_mat.material.unit.unit_name
					if dc_mat.alternate_unit_id and dc_mat.alternate_unit_id != '0' and int(
							dc_mat.alternate_unit_id) != 0:
						scale_factor = helper.getScaleFactor(
							enterprise_id=dc_mat.enterprise_id, item_id=dc_mat.item_id,
							alternate_unit_id=dc_mat.alternate_unit_id)
						unit_name = helper.getUnitName(enterprise_id=dc_mat.enterprise_id, unit_id=dc_mat.alternate_unit_id)
						if scale_factor:
							quantity = float(dc_mat.accepted_qty) / float(scale_factor) if float(dc_mat.accepted_qty) else 0

					dc_materials_list.append({
						"doc_no": dc_mat.receipt.getCode(), "doc_date": dc_mat.receipt.inward_date.strftime('%Y-%m-%d'),
						"item_details": item_name,  "receipt_no": dc_mat.receipt.receipt_no, "receipt_type": dc_mat.receipt.received_against,
						"quantity": quantity, "unit": unit_name, "is_stock": dc_mat.material.is_stocked,
						"is_service": dc_mat.material.is_service})
			dc_materials_list = sorted(dc_materials_list, key=lambda a: a['doc_no'])
		return dc_materials_list

	@staticmethod
	def getPendingDCByParty(enterprise_id=None, party_id=None, invoice_id=None, financial_years=None):
		"""

		:param enterprise_id:
		:param party_id:
		:param invoice_id:
		:param financial_years:
		:return:
		"""
		try:
			conditions = ""
			fin_year_conditions = ""
			financial_years_from_db = []
			if invoice_id:
				conditions = " AND inv_mat.invoice_id != " + invoice_id + " "
			if financial_years:
				fin_year_conditions = " AND a.financial_year in (%s) " % financial_years
			query = """SELECT 
					a.id as id, a.financial_year as financial_year,   
					IFNULL(a.invoice_code,CONCAT(IFNULL(a.financial_year, 'PROFORMA'), '/', LEFT(a.type, 1), 
					IFNULL(CONVERT( LPAD(a.invoice_no, 6, '0') USING LATIN1), a.id), 
					IFNULL(a.sub_number, ''))) AS code
				FROM
					invoice AS a, invoice_materials AS c
				WHERE
					a.status = 1
					AND a.type = 'DC' AND c.invoice_id = a.id
					AND a.party_id = {party_id}  AND a.enterprise_id = {enterprise_id} {fin_year_conditions}
					AND IF(IFNULL((SELECT  SUM(qty) FROM invoice_materials AS dc_mat,
					invoice AS dc WHERE dc.enterprise_id = {enterprise_id}
					AND dc.status > - 1 AND dc.type = 'DC' AND dc_mat.invoice_id = dc.id
					AND dc_mat.invoice_id = a.id AND dc_mat.is_returnable = 0 ), 0) > IFNULL((SELECT  SUM(qty) FROM
					invoice_materials AS inv_mat, invoice AS inv WHERE inv.enterprise_id = {enterprise_id}
					AND inv.status > - 1 AND inv_mat.invoice_id = inv.id {conditions} 
					AND inv_mat.delivered_dc_id = a.id),0),1,0) 
				group by a.id""".format(
				party_id=party_id, enterprise_id=enterprise_id, conditions=conditions,
				fin_year_conditions=fin_year_conditions)
			response = dao.executeQuery(query, as_dict=True)
			for fy in response:
				if fy['financial_year'] not in financial_years_from_db:
					financial_years_from_db.append(fy['financial_year'])
			return response, financial_years_from_db
		except Exception as e:
			logger.exception("Failed to calculate pending DC Qty \n%s" % str(e))

	@staticmethod
	def getPartyFinancialYears(enterprise_id=None, party_id=None):
		"""
		:param enterprise_id:
		:param party_id:
		:return:
		"""
		try:
			financial_years_from_db = []
			query = """SELECT financial_year    
					FROM invoice AS a WHERE status = 1 AND type = 'DC'  AND party_id = {party_id}  
					AND enterprise_id = {enterprise_id}  group by financial_year""".format(
				party_id=party_id, enterprise_id=enterprise_id)
			financial_years = dao.executeQuery(query, as_dict=True)
			for fy in financial_years:
				if fy['financial_year'] not in financial_years_from_db:
					financial_years_from_db.append(fy['financial_year'])
			return financial_years_from_db
		except Exception as e:
			logger.exception("Failed to get financial years \n%s" % str(e))

	def notifyPendingSalesEstimateCount(
			self, enterprise_id=None, sender_id=None, include_sender=True, code=None, type=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param include_sender:
		:param code:
		:param type:
		:return:
		"""
		try:
			count = self.invoice_dao.getPendingSalesEstimateCount(enterprise_id=enterprise_id)
			message = None
			if count == 1:
				message = "1 Sales Estimate is pending for approval"
			elif count > 1:
				message = "%s Sales Estimate are pending for approval" % count
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="SALES",
				message=message, collapse_key="sales_est_count", include_sender=include_sender, code=code, type=type)
		except Exception as e:
			logger.error("Failed notification.. %s " % str(e))

	def notifySalesEstimateApproveCount(
			self, enterprise_id=None, sender_id=None, include_sender=False, sales_estimate=None, code=None, type=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param include_sender:
		:param sales_estimate:
		:param code:
		:param type:
		:return:
		"""
		try:
			grand_total = "%s %s " % (sales_estimate.currency.code, sales_estimate.grand_total)
			customer = self.invoice_dao.getCustomerDetails(enterprise_id=enterprise_id, party_id=sales_estimate.party_id)
			message = PUSH_NOTIFICATION["Sales_Estimate_approved"] % (sales_estimate.getCode(), customer.name, grand_total)
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="SALES", message=message,
				include_sender=include_sender, code=code, type=type)
			self.notifyPendingSalesEstimateCount(enterprise_id=enterprise_id, sender_id=sender_id, code=code, type=type)
		except Exception as e:
			logger.error("Failed notification.. %s " % str(e))

	def notifySalesEstimateRejectCount(
			self, enterprise_id=None, sender_id=None, include_sender=False, sales_estimate=None, code=None, type=None):
		"""

		:param enterprise_id:
		:param sender_id:
		:param include_sender:
		:param sales_estimate:
		:param code:
		:param type:
		:return:
		"""
		try:
			grand_total = "%s %s " % (sales_estimate.currency.code, sales_estimate.grand_total)
			customer = self.invoice_dao.getCustomerDetails(enterprise_id=enterprise_id, party_id=sales_estimate.party_id)
			message = PUSH_NOTIFICATION["Sales_Estimate_rejected"] % (sales_estimate.getCode(), customer.name, grand_total)
			push_notification(
				enterprise_id=enterprise_id, sender_id=sender_id, module_code="SALES", message=message,
				include_sender=include_sender, code=code, type=type)
			self.notifyPendingSalesEstimateCount(enterprise_id=enterprise_id, sender_id=sender_id, code=code, type=type)
		except Exception as e:
			logger.error("Failed notification.. %s " % str(e))

	def generateInvoiceFromSE(self, invoice_obj=None, se_no=None, enterprise_id=None):
		"""

		:param invoice_obj:
		:param se_no:
		:param enterprise_id:
		:return:
		"""
		try:

			se_obj = self.invoice_dao.getSalesEstimate(se_id=se_no, enterprise_id=enterprise_id)
			copyDictToDict(source=se_obj.__dict__, destination=invoice_obj.__dict__, exclude_keys=SE_EXCLUDE_FIELD_LIST)
			invoice_obj.se_id = se_obj.id
			invoice_obj.type = ""
			invoice_obj.se_date = se_obj.client_revised_on
			invoice_obj.enterprise = se_obj.enterprise
			invoice_obj.special_instruction = se_obj.special_instructions

			invoice_items = []
			i = 0
			for item in se_obj.items:
				inv_item = InvoiceMaterial(enterprise_id=enterprise_id)
				copyDictToDict(
							source=item.__dict__, destination=inv_item.__dict__, exclude_keys=SE_PARTICULAR_EXCLUDE_FIELD_LIST)
				inv_item.entry_order = i + 1
				inv_item.rate = item.unit_rate
				inv_item.item = item.item
				inv_item.make = item.make
				inv_item.alternate_unit = item.alternate_unit
				inv_item.invoice = invoice_obj
				invoice_items.append(inv_item)
				i = i + 1
			logger.info(invoice_items)
			invoice_obj.items = invoice_items

			invoice_taxes = []
			for tax in se_obj.taxes:
				inv_tax = InvoiceTax(enterprise_id=enterprise_id)
				copyDictToDict(
							source=tax.__dict__, destination=inv_tax.__dict__, exclude_keys=SE_TAX_EXCLUDE_FIELD_LIST)
				invoice_taxes.append(inv_tax)
			invoice_obj.taxes = invoice_taxes

			return invoice_obj, se_obj
		except Exception as e:
			logger.exception("Invoice generation from SE Failed...\n%s" % str(e))
			raise

	def saveEnterpriseSerialNumber(
			self, enterprise_id=None, user_id=None, item_id=None, invoice_id=None, make_id=1, is_faulty=False, serial_no=None):
		"""

		:param enterprise_id:
		:param user_id:
		:param item_id:
		:param invoice_id:
		:param make_id:
		:param is_faulty:
		:param serial_no:
		:return:
		"""
		db_session = self.invoice_dao.db_session
		try:
			db_session.begin(subtransactions=True)
			enterprise_serial_no = MaterialSerialNo(
				enterprise_id=enterprise_id, item_id=item_id, make_id=make_id, is_faulty=is_faulty, serial_number=serial_no,
				created_on=datetime.now(), created_by=user_id)
			invoice_serial_no = InvoiceMaterialSerialNo(invoice_id=invoice_id, serial_number=serial_no)

			db_session.add(enterprise_serial_no)
			db_session.add(invoice_serial_no)
			db_session.commit()
		except Exception as e:
			logger.exception(str(e))
			db_session.rollback()
			raise e
		return enterprise_serial_no

	def deleteEnterpriseSerialNumber(self, enterprise_id=None, invoice_id=None, serial_no=None):
		"""

		:param enterprise_id:
		:param invoice_id:
		:param serial_no:
		:return:
		"""
		db_session = self.invoice_dao.db_session
		try:
			db_session.begin(subtransactions=True)
			enterprise_serial_no = self.invoice_dao.getEnterpriseSerialNo(enterprise_id=enterprise_id, serial_no=serial_no)
			invoice_serial_no = self.invoice_dao.getInvoiceSerialNo(invoice_id=invoice_id, serial_no=serial_no)

			if enterprise_serial_no:
				db_session.delete(enterprise_serial_no)

			if invoice_serial_no:
				db_session.delete(invoice_serial_no)

			db_session.commit()
		except Exception as e:
			logger.exception(str(e))
			db_session.rollback()
			raise e

	def saveInvoicePackingDetails(
			self, enterprise_id=None, user_id=None, invoice_id=None, packing_slip_no=None,
			packing_description=None):
		"""

		:param enterprise_id:
		:param user_id:
		:param invoice_id:
		:param packing_slip_no:
		:param packing_description:
		:return:
		"""
		db_session = self.invoice_dao.db_session
		try:
			db_session.begin(subtransactions=True)
			invoice = self.invoice_dao.getInvoice(enterprise_id=enterprise_id, invoice_id=invoice_id)
			if invoice:
				invoice.packing_slip_no = packing_slip_no
				invoice.packing_description = packing_description
				invoice.last_modified_by = user_id
				invoice.last_modified_on = datetime.now()

			db_session.add(invoice)
			db_session.commit()
		except Exception as e:
			logger.exception(str(e))
			db_session.rollback()
			raise e
		return invoice


def getJDCMaterials(po_id, enterprise_id, issued_on, location_id):
	po_details = []
	try:
		store_dao = StoresDAO()
		store_service = StoresService()
		po_materials_query = """SELECT m.drawing_no as drawing_no, m.id as cat_code, m.name as name, 
						m.description as description, u.unit_name as unit, m.price as price, m.unit as unit_id, 
						m.minimum_stock_level as minimum_stock_level, pom.enterprise_id as enterprise_id, 
						m.is_stocked as material_type, IFNULL(m.tariff_no, "") as hsn_code, 
						m.makes_json AS make_name, pom.make_id AS make_id, m.id as item_id,
						IFNULL(pom.alternate_unit_id, 0) as alternate_unit_id, m.is_service as is_service, pom.pur_qty as qty
						FROM purchase_order_material as pom
						LEFT JOIN materials as m ON pom.item_id = m.id AND pom.enterprise_id = m.enterprise_id
						LEFT JOIN unit_master as u ON u.enterprise_id = m.enterprise_id AND u.unit_id = m.unit			
						WHERE pom.pid={po_id} AND pom.enterprise_id = {enterprise_id}""".format(
			po_id=po_id, enterprise_id=enterprise_id)
		materials = dao.executeQuery(po_materials_query, as_dict=True)
		for material in materials:
			# FIXME performance issue while accessing item.material in loop; Consider 250 materials in a PO;
			stock = 0
			catalogues = store_dao.getCatalogues(material['cat_code'], enterprise_id)
			catalogues_child = 1 if len(catalogues) > 0 else 0
			if material['material_type'] != 0:
				# stock_on_date = store_dao.getClosingStock(
				# 	enterprise_id=enterprise_id, item_id=material['item_id'],
				# 	till=datetime.now(), is_faulty=0, issued_on=issued_on if issued_on else datetime.now())
				# stock_till_today = store_dao.getClosingStock(
				# 	enterprise_id=enterprise_id, item_id=material['item_id'],
				# 	till=datetime.now(), is_faulty=0)
				# stock = min(stock_on_date, stock_till_today)
				stock_details = store_service.getClosingStock(
					mat_list=[int(material['item_id'])], enterprise_id=enterprise_id, is_faulty=0, location_id=location_id)
				for item in stock_details:
					stock = item['closing_qty']

				material['scale_factor'] = 1
			if material['alternate_unit_id'] and int(material['alternate_unit_id']) != 0:
				scale_factor = helper.getScaleFactor(
					enterprise_id=enterprise_id, item_id=material['item_id'],
					alternate_unit_id=material['alternate_unit_id'])
				material['unit'] = helper.getUnitName(enterprise_id=enterprise_id, unit_id=material['alternate_unit_id'])
				if scale_factor:
					stock = Decimal(stock) / Decimal(scale_factor) if stock != 0 else 0
					material['minimum_stock_level'] = Decimal(material['minimum_stock_level']) / Decimal(scale_factor)
					material['price'] = Decimal(material['price']) * Decimal(scale_factor)
					material['scale_factor'] = scale_factor if scale_factor else 1
			stock_materials = [(material['make_id'], material['make_name'], stock, material['hsn_code'])]

			material['child'] = catalogues_child
			material['makes'] = stock_materials
			po_details.append(material)
	except Exception as e:
		logger.exception("Failed to load materials %s" % str(e))
	return po_details

def getPPMaterials(po_id, enterprise_id):
	po_details = []
	try:
		po_materials_query = """SELECT m.drawing_no as drawing_no, m.id as cat_code, m.name as name,
						m.description as description,m.minimum_stock_level as minimum_stock_level, u.unit_name as unit, m.price as price, m.unit as unit_id, 
						IFNULL(mrpm.allocated_qty, 0) as allocated_qty,IFNULL(mrpm.required_qty,0) as required_qty,
						IFNULL(mrpm.issued_qty,0) as issued_qty, mrpm.location_id as location_id,mrpm.parent_id as parent_id, 
						m.is_stocked as material_type, IFNULL(m.tariff_no, "") as hsn_code, 
						1 AS make_id, m.id as item_id, m.is_service as is_service, "" as make_name, m.enterprise_id as enterprise_id 
						FROM mrp_materials as mrpm 
						LEFT JOIN materials as m ON mrpm.item_id = m.id AND mrpm.enterprise_id = m.enterprise_id 
						LEFT JOIN unit_master as u ON u.enterprise_id = m.enterprise_id AND u.unit_id = m.unit 
						WHERE mrpm.pp_id={po_id} AND mrpm.enterprise_id = {enterprise_id}""".format(
			po_id=po_id, enterprise_id=enterprise_id)
		materials = dao.executeQuery(po_materials_query, as_dict=True)
		for material in materials:
			material['material_type'] = 1
			material['child'] = 0
			if material['allocated_qty'] != 0:
				stock_materials = [
					(material['make_id'], material['make_name'], material['allocated_qty'], material['hsn_code'])]
				material['makes'] = stock_materials
				po_details.append(material)
	except Exception as e:
		logger.exception("Failed to load materials %s" % str(e))
	return po_details

def getCatalogueMaterials(enterprise_id, cat_code, cat_item_qty, materials, depth=0):
	try:
		if depth >= 6:
			return materials  # Termination condition
		store_dao = StoresDAO()
		catalogues = store_dao.getCatalogues(cat_code, enterprise_id)
		sub_child = []
		for catalogue in catalogues:
			catalogues_child = store_dao.getCatalogues(catalogue.item_id, enterprise_id)
			pp_qty = int(catalogue.quantity * cat_item_qty)
			if len(catalogues_child) > 0:
				sub_child.append([catalogue.item_id, pp_qty])
			if catalogue.item_id not in materials:
				materials[catalogue.item_id] = pp_qty
		for item in sub_child:
			materials = getCatalogueMaterials(enterprise_id,item[0], item[1], materials, depth + 1)
	except Exception as e:
		logger.exception("Failed to Get BOM Materials -  %s" % str(e))
	return materials


def getTotalCreditDebitValue(enterprise_id, from_date, to_date):
	query_data = []
	try:
		totals_query = """SELECT
							IFNULL(SUM(CASE WHEN is_debit = 1 THEN amount ELSE 0 END),0) AS total_debit,
							IFNULL(SUM(CASE WHEN is_debit = 0 THEN amount ELSE 0 END),0) AS total_credit,
							(Select IFNULL(working_capital * -1 ,0) from projects where project_enterprise_id='{enterprise_id}') as cash_allocated
						FROM voucher AS v
							  JOIN voucher_particulars AS vp ON vp.voucher_id = v.id AND vp.enterprise_id = v.enterprise_id 
							  JOIN account_ledgers as al ON al.id = vp.ledger_id  and al.enterprise_id = vp.enterprise_id 
							  JOIN account_groups AS ag ON ag.id = al.group_id  AND ag.enterprise_id = al.enterprise_id 
						WHERE voucher_date BETWEEN '{from_date}' AND '{to_date}' AND v.type in(2,3) AND v.enterprise_id='{enterprise_id}' 
							AND vp.ledger_id  in (SELECT id FROM account_ledgers WHERE group_id not IN (18,20,36,40) AND enterprise_id = '{enterprise_id}') 
							""".format(enterprise_id=enterprise_id, from_date=str(from_date), to_date=str(to_date))
		query_data = executeQuery(totals_query, as_dict=True)
	except Exception as e:
		logger.exception("Failed to Get Credit Debit value -  %s" % str(e))
	return query_data


def getTotalExpensesBudgetAmount(from_date, to_date, project_id):
	try:

		budget_query = """SELECT IFNULL(SUM(c_flow.budget_amount),0) as budget_amount
				FROM cashflow_forecast_details As c_flow				
				WHERE c_flow.project_id='{project_id}' and c_flow.start_date >= '{from_date}' and c_flow.end_date <='{to_date}'
				and c_flow.type = 'expenses'
				ORDER BY c_flow.type, c_flow.start_date""".format(project_id=project_id, from_date=from_date, to_date=to_date)
		query_data = executeQuery(budget_query)
		return query_data[0][0]
	except Exception as e:
		logger.exception("Failed to Get total Expenses Budget amount -  %s" % str(e))


def getTotalRevenueBudgetAmount(from_date, to_date, project_id):
	try:

		budget_query = """SELECT IFNULL(SUM(c_flow.budget_amount),0) as budget_amount
				FROM cashflow_forecast_details As c_flow				
				WHERE c_flow.project_id='{project_id}' and c_flow.start_date >= '{from_date}' and c_flow.end_date <='{to_date}'
				and c_flow.type = 'revenue'
				ORDER BY c_flow.type, c_flow.start_date""".format(project_id=project_id, from_date=from_date, to_date=to_date)
		query_data = executeQuery(budget_query)
		return query_data[0][0]
	except Exception as e:
		logger.exception("Failed to Get total Revenue Budget amount -  %s" % str(e))


def createPurchaseInternalVoucher(
		invoice, enterprise_id, issued_on, narration="", db_session=None, for_rejection=False, user_id=None,
		internal_invoice_ledger_bill=None):
	"""
	Generate Purchase Internal Voucher for Child Project
	"""
	db_session.begin(subtransactions=True)
	account_service = AccountService()
	try:
		voucher_particulars = []
		voucher_tags = []
		item_no = 1
		projects = helper.getProjectByProjectEnterpriseId(enterprise_id)
		project_name = projects.name
		project_code = projects.code
		target_enterprise_id = getProjectEnterpriseIdByProjectCode(code=invoice.customer.code, name=invoice.customer.name)
		if target_enterprise_id:
			master_service = MasterDAO()
			party = master_service.getPartyByCode(enterprise_id=target_enterprise_id, party_code=project_code)
			if not party:
				createPartyToProjectEnterprise(target_enterprise_id, project_name, project_code, user_id, db_session)
				party = master_service.getPartyByCode(enterprise_id=target_enterprise_id, party_code=project_code)
			purchase_account_leder_id = account_service.getPurchaseAcountLedgerId(enterprise_id=target_enterprise_id)[0]
			voucher_value = Decimal(invoice.getTotalMaterialValue()) * Decimal(invoice.currency_conversion_rate)
			voucher_particulars.append(VoucherParticulars(item_no=item_no, ledger_id=purchase_account_leder_id, is_debit=(not for_rejection),
														  amount=round(voucher_value, 2),enterprise_id=target_enterprise_id))
			if not party.getSupplierLedger():
				createPartyLedgers(
					party=party, user_id=invoice.approved_by, db_session=db_session, is_supplier=True)
			logger.info("Supplier Ledger: %s" % party.getSupplierLedger().id)
			item_no += 1
			voucher_particulars.append(VoucherParticulars(
				item_no=item_no, ledger_id=party.getSupplierLedger().id, is_debit=for_rejection,
				amount=round(Decimal(invoice.grand_total) * Decimal(invoice.currency_conversion_rate), 2),
				enterprise_id=target_enterprise_id))
			voucher = Voucher(type_id=4, voucher_no=0, voucher_date=invoice.issued_on if invoice.issued_on else datetime.now(),
				narration=narration, enterprise_id=target_enterprise_id, project_code=invoice.project_code, created_by=invoice.approved_by)
			voucher.particulars = voucher_particulars
			voucher.tags = voucher_tags
			ledger_bill = LedgerBill(
				bill_no=invoice.getCode(), bill_date=issued_on.date(), ledger_id=party.getSupplierLedger().id,
				is_debit=False, enterprise_id=target_enterprise_id,
				net_value=round(Decimal(invoice.grand_total) * Decimal(invoice.currency_conversion_rate), 2),
				parent_ledgerbill_id=internal_invoice_ledger_bill.id)
			ledger_bill.voucher = voucher
			ledger_bill_settlement = LedgerBillSettlement(
				cr_value=round(Decimal(invoice.grand_total) * Decimal(invoice.currency_conversion_rate), 2))
			ledger_bill_settlement.voucher = voucher
			ledger_bill_settlement.bill = ledger_bill
			ledger_bill_settlement.enterprise_id = target_enterprise_id
			db_session.add(ledger_bill_settlement)
			db_session.commit()
			logger.info("Purchase Internal Voucher Create to the Enterprise ID - %s" % str(target_enterprise_id))
		else:
			logger.exception("Project Enterprise not exist  - %s(%s) " %(invoice.customer.name, invoice.customer.code))

	except Exception as e:
		logger.exception("Failed to Create Purchase Internal workorder for Child Project - %s" %str(e))


def getProjectForecast(enterprise_id, project_id, primary_enterprise_id, is_root_level=False):
	budget_flow = {
		"revenue": {},
		"expenses": {},
		"working_capital" : 0.00,
		"project_owner": "",
		"email": "",
		"phone_no" : ""
	}
	child_project_ids = [int(project_id)]
	project_enterprise_ids = [int(enterprise_id)]
	if is_root_level:
		projects_list = helper.populateAllProjects(primary_enterprise_id)
		child_project_ids, project_enterprise_ids = getChildProjecstId(projects_list, project_id, child_project_ids, project_enterprise_ids)

	try:
		project_service = ProjectForecastService()
		financial_year_months = getFinancialYearMonths()
		start_date,end_date = getFinancialStartEndDate()
		revenue_accounts_group = project_service.getPreferAccountsGroupForCashflow(primary_enterprise_id, is_revenue=True)
		revenue_accounts_group_ids = revenue_accounts_group.keys()
		expenses_accounts_group = project_service.getPreferAccountsGroupForCashflow(primary_enterprise_id, is_revenue=False)
		expenses_accounts_group_ids = expenses_accounts_group.keys()

		default_value = {month: {"budget": 0, "actual": 0} for month in financial_year_months}
		##Construct Budget Flow
		budget_query = """SELECT c_flow.project_id, c_flow.ledger_name, DATE_FORMAT(c_flow.start_date,'%b-%y') AS month, c_flow.budget_amount, c_flow.type
				FROM cashflow_forecast_details As c_flow
				WHERE c_flow.project_id in {project_id} and c_flow.start_date >= '{start_date}' and c_flow.end_date <='{end_date}'
				ORDER BY c_flow.type, c_flow.start_date""".format(project_id=str(child_project_ids).replace('[','(').replace(']',')'),
																  start_date=start_date, end_date=end_date)
		budget_query_data = executeQuery(budget_query, as_dict=True)
		for rec in budget_query_data:
			ledger_name = str(rec['ledger_name'])
			transact_type = str(rec['type'])
			budget_flow[transact_type].setdefault(ledger_name,default_value)
			temp_value = json.dumps(budget_flow[transact_type][ledger_name])
			temp_value = json.loads(temp_value)
			temp_value[str(rec['month'])]['budget'] += int(rec['budget_amount'])
			budget_flow[transact_type][ledger_name] = temp_value

		##Construct Actual Data for forecast
		actual_query_data = get_actual_cashflow_record(enterprise_id=project_enterprise_ids, start_date=start_date, end_date=end_date)

		for rec in actual_query_data:
			ledger_name = str(rec["account_ledger_name"])
			transaction_month = str(rec["transaction_month"])
			total_credit = float(rec["total_credit"])
			total_debit = float(rec["total_debit"])

			if total_credit > 0:
				budget_flow['revenue'].setdefault(ledger_name, default_value)
				temp_value = json.loads(json.dumps(budget_flow['revenue'][ledger_name]))
				temp_value[transaction_month]['actual'] += total_credit
				budget_flow['revenue'][ledger_name] = temp_value
			if total_debit > 0:
				budget_flow['expenses'].setdefault(ledger_name, default_value)
				temp_value = json.loads(json.dumps(budget_flow['expenses'][ledger_name]))
				temp_value[transaction_month]['actual'] += total_debit
				budget_flow['expenses'][ledger_name] = temp_value

		budget_flow['months'] = financial_year_months
		budget_flow['revenue_groups'] = []
		budget_flow['expenses_groups'] = []
		revenue_account_options = getAccountsGroupsOptionTree(enterprise_id=primary_enterprise_id,
													  accounts_group_ids=revenue_accounts_group_ids)
		expenses_account_options = getAccountsGroupsOptionTree(enterprise_id=primary_enterprise_id,
															  accounts_group_ids=expenses_accounts_group_ids)
		budget_flow['revenue_accounts_options'] = revenue_account_options
		budget_flow['expeses_accounts_options'] = expenses_account_options
		project = getProjectByProjectId(project_id)
		if project:
			budget_flow['working_capital'] = project.working_capital
			budget_flow['project_owner'] = project.project_owner
			budget_flow['email'] = project.email
			budget_flow['phone_no'] = project.phone_no
	except Exception as e:
		logger.exception("Failed to Get Project Forecast Value... - %s" % str(e))
	return budget_flow


def get_actual_cashflow_record(enterprise_id, start_date, end_date):
	if not isinstance(enterprise_id, list):
		enterprise_id = [enterprise_id]
	query = """SELECT DATE_FORMAT(voucher_date,'%b-%y') AS transaction_month, vp.budget_id, al.name as account_ledger_name,
				ag.name AS account_group_name, ag.id AS group_id,
				v.id, v.financial_year, v.voucher_no, SUM(CASE WHEN is_debit = 1 THEN amount ELSE 0 END) AS total_debit,
				SUM(CASE WHEN is_debit = 0 THEN amount ELSE 0 END) AS total_credit 
				FROM voucher AS v
					JOIN voucher_particulars AS vp ON vp.voucher_id = v.id AND vp.enterprise_id = v.enterprise_id
					JOIN account_ledgers as al ON al.id = vp.budget_id and al.enterprise_id = vp.enterprise_id
					JOIN account_groups AS ag ON ag.id = al.group_id AND ag.enterprise_id = al.enterprise_id
				WHERE voucher_date BETWEEN '{start_date}' AND '{end_date}' AND v.type in(2,3) and v.enterprise_id in {enterprise_id} 
					and vp.budget_id in (SELECT id FROM account_ledgers WHERE group_id not IN (18,20,36,40) AND enterprise_id in {enterprise_id}) 
				GROUP BY transaction_month, vp.budget_id
				ORDER BY transaction_month""".format(enterprise_id=str(enterprise_id).replace('[','(').replace(']',')'),
													 start_date=start_date, end_date=end_date)
	return executeQuery(query=query, as_dict=True)


def getChildProjecstId(projects_list, project_id, projects_ids, enterprise_ids):
	try:
		for rec in projects_list:
			if int(project_id) == rec['parent_id']:
				projects_ids.append(rec['id'])
				enterprise_ids.append(rec['project_enterprise_id'])
				projects_ids,enterprise_ids = getChildProjecstId(projects_list, rec['id'], projects_ids, enterprise_ids)
	except Exception as e:
		logger.exception("Failed to Construct Projects ID -  %s" % str(e))
	return projects_ids, enterprise_ids


def get_start_end_date_from_month(start_month, end_month):
	try:
		# Convert month-year format to a datetime object
		start_date = datetime.strptime(start_month, "%b-%Y")
		end_date = datetime.strptime(end_month, "%b-%Y")

		# Calculate the last day of the end month
		end_day = calendar.monthrange(end_date.year, end_date.month)[1]
		end_date = datetime(end_date.year, end_date.month, end_day)

		return start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d")
	except Exception as e:
		logger.info("Failed to get the start_date and end_date: %s", e)


def getLedgerNameByAccountsGroupIds(project_enterprise_id):
	ledger_name_list = []
	query = """SELECT al.name FROM account_ledgers AS al JOIN account_groups AS ag ON ag.id = al.group_id 
	WHERE ag.enterprise_id = {enterprise_id}""".format(enterprise_id=project_enterprise_id)
	query_data = executeQuery(query)
	for rec in query_data:
		ledger_name_list.append(rec[0])
	return ledger_name_list


def getAccountsGroupsOptionTree(enterprise_id, accounts_group_ids):
	option_list = []
	parent_ids = []
	query = "SELECT id, parent_id, name FROM account_groups WHERE enterprise_id=" + str(enterprise_id)
	query_data = executeQuery(query)
	for rec in query_data:
		if str(rec[0]) in accounts_group_ids:
			parent_ids.append(rec[0])
			data = {}
			data["id"] = rec[0]
			data['parent_id'] = rec[1]
			data['name'] = rec[2]
			option_list.append(data)
	parent_ids = list(set(parent_ids))
	return construct_account_list(option_list, parent_ids)


def construct_account_list(option_list, parent_id):
	account_group_list = []
	for rec in option_list:
		if rec['parent_id'] in parent_id:
			account_group_list.append(rec)
		else:
			rec['parent_id'] = 0
			account_group_list.append(rec)
	data_set = build_tree(account_group_list)
	return build_option_list(data_set=data_set, level=0, account_options_list=[])


def build_option_list(data_set, level, account_options_list):
	for rec in data_set:
		name = "----" * level + rec['name']
		account_options_list.append([rec['id'], name])
		if "children" in rec:
			account_options_list = build_option_list(rec['children'], level+1, account_options_list)
	return account_options_list


def build_tree(data, parent_id=0):
	tree = {}
	for item in data:
		if item['parent_id'] == parent_id:
			children = build_tree(data, item['id'])
			if children:
				item['children'] = children
			tree[item['id']] = item
	return list(tree.values())


def getProjectEnterpriseIdByProjectCode(code, name):
	query = "SELECT project_enterprise_id FROM projects WHERE code='%s' AND name='%s' AND is_active=1" % (code, name)
	query_data = executeQuery(query)
	return query_data[0][0] if query_data else None


def getProjectEnterpriseIdByProjectId(project_id):
	return SQLASession().query(Project.project_enterprise_id).filter(Project.id == project_id).scalar()


class ProjectForecastService():

	def __init__(self):
		self.stores_service = StoresDAO()

	def construct_cash_flow(self, project_id, forecast_value, primary_enterprise_id, user_id):
		try:
			for cash_type,group_value in forecast_value.items():
				for ledger_name, rec in group_value.items():
					start_date, end_date = getMonthStartEndDate(list(rec.keys())[0])
					#Validate all budget values are zero or budget value already exist
					if (self.isBudgettoInsert(amounts = list(rec.values())) or
							self.getCashFlowForMonth(primary_enterprise_id,project_id,ledger_name,cash_type,start_date, end_date)):
						for month, cash in rec.items():
							start_date, end_date = getMonthStartEndDate(month)
							cashflow = self.getCashFlowForMonth(primary_enterprise_id,project_id,ledger_name,cash_type,start_date,end_date)
							if not cashflow:
								cashflow = CashFlow(enterprise_id=primary_enterprise_id,ledger_name=ledger_name,project_id=project_id,
													type=cash_type,start_date=start_date,end_date=end_date)
							cashflow.budget_amount = cash['budget']
							self.saveCashFlow(entity=cashflow)
					else:
						forecast_value[cash_type].pop(ledger_name, None)
			self.save_cashflow_version(forecast_value, primary_enterprise_id, project_id, user_id)
		except Exception as e:
			logger.exception("Failed to Construct Cashflow... - %s" % str(e))

	def save_cashflow_version(self, forecast_value, primary_enterprise_id, project_id, user_id):
		try:
			financial_year_months = getFinancialYearMonths()
			collection = 'project_forecast'
			db = MongoDbConnect[collection]
			if forecast_value['expenses'] or forecast_value['revenue']:
				forecast_query = {
					"enterprise_id": int(primary_enterprise_id),
					"project_id" : int(project_id),
					"created_by": user_id,
					"created_on": datetime.now(),
					"forecast": forecast_value,
					"months" : financial_year_months
				}
				db.insert(forecast_query)
				logger.info("Forecast version successfully Saved - %s" %str(project_id))
		except Exception as e:
			logger.exception("Failed to Save cash flow versions... - %s" % str(e))

	def isBudgettoInsert(self, amounts):
		return any(amount['budget'] != '0' for amount in amounts)

	def saveCashFlow(self, entity):
		self.stores_service.db_session.begin(subtransactions=True)
		try:
			self.stores_service.db_session.add(entity)
			self.stores_service.db_session.commit()
		except Exception as e:
			logger.exception("Failed to Save Cashflow... - %s" % str(e))
			self.stores_service.db_session.rollback()
		return

	def getCashFlowForMonth(self, enterprise_id,project_id,ledger_name,type, start_date,end_date):
		return self.stores_service.db_session.query(CashFlow).filter(CashFlow.enterprise_id == enterprise_id,
									CashFlow.project_id == project_id,CashFlow.ledger_name == ledger_name,CashFlow.type == type,
									CashFlow.start_date == start_date,CashFlow.end_date == end_date).first()

	def getPreferAccountsGroupForCashflow(self, enterprise_id, is_revenue=None):
		account_groups = {}
		revenue_ids = expenses_ids = []
		revenue_names = ["Direct Income", "Indirect Incomes"]
		expenses_names = ["Direct Expenses", "Indirect Expenses"]
		if is_revenue is None:
			revenue_ids = getAccountGroupIDs(enterprise_id=enterprise_id, names=revenue_names, deep=True)
			expenses_ids = getAccountGroupIDs(enterprise_id=enterprise_id, names=expenses_names, deep=True)
		elif is_revenue:
			revenue_ids = getAccountGroupIDs(enterprise_id=enterprise_id, names=revenue_names, deep=True)
		else:
			expenses_ids = getAccountGroupIDs(enterprise_id=enterprise_id, names=expenses_names, deep=True)
		group_ids = revenue_ids + expenses_ids

		try:
			if group_ids:
				accounts_group_query = """SELECT ag.name as name, ag.id as group_id FROM account_groups AS ag 
				WHERE ag.enterprise_id = '{enterprise_id}' and ag.id in {group_ids}""".format(enterprise_id=enterprise_id,
										group_ids= str(group_ids).replace('[','(').replace(']',')'))
				query_data = executeQuery(accounts_group_query, as_dict=True)
				for rec in query_data:
					if str(rec['name']) not in revenue_names + expenses_names:
						account_groups[str(rec['group_id'])] = str(rec['name'])
		except Exception as e:
			logger.exception("Failed to Get Accounts Group Id.... - %s" % str(e))
		return account_groups

	def updateProjectsInfo(self, project_id, capital, project_owner, email, phone_no):
		try:
			project_to_be_update = self.stores_service.db_session.query(Project).filter(Project.id == project_id).first()
			if project_to_be_update:
				project_to_be_update.working_capital = float(capital) if capital else 0
				project_to_be_update.project_owner = str(project_owner)
				project_to_be_update.email = email
				project_to_be_update.phone_no = phone_no
				self.saveCashFlow(project_to_be_update)
				logger.info("Successfully updated working capital value (%s) and Project Owner (%s)to the Project %s" %(str(capital),
																								str(project_owner), str(project_id)))
		except Exception as e:
			logger.exception("Failed to Update Working Capital.... - %s" % str(e))


def createPartyToProjectEnterprise(project_enterprise_id, party_name, party_code, user_id, db_session):
	party_id = None
	db_session.begin(subtransactions=True)
	try:
		party_to_be_created = Party(name=party_name, enterprise_id=project_enterprise_id, code=party_code)
		db_session.add(party_to_be_created)
		db_session.commit()
		db_session.refresh(party_to_be_created)
		party_id = party_to_be_created.id
		createPartyLedgers(
			party=party_to_be_created, user_id=user_id, db_session=db_session,
			is_customer=True, is_supplier=True)
	except Exception as e:
		logger.exception("Party Create Failed for Project.... - %s" % str(e))
		db_session.rollback()
	return party_id

class InternalInvoiceService():

	def __init__(self):
		"""
		"""
		self.invoice_dao = InvoiceDAO()

	def approveInternalVoucher(self, voucher_id, user_id):
		try:
			internal_invoice_ids = self.getInternalInvoiceIdByParentVoucherId(voucher_id=voucher_id)
			account_service = AccountService()
			for internal_invoice_id in internal_invoice_ids:
				internal_invoice = self.invoice_dao.db_session.query(Invoice).filter(Invoice.id == internal_invoice_id).first()
				if internal_invoice:
					internal_voucher_id = self.getVoucherIdByInvoiveId(internal_invoice.id)
					if internal_voucher_id:
						##Approve Sales Voucher
						internal_voucher = self.invoice_dao.db_session.query(Voucher).filter(Voucher.id == internal_voucher_id).first()
						internal_voucher_to_be_saved = account_service.prepareVoucherApproval(voucher_to_be_saved=internal_voucher,
																		  enterprise=internal_invoice.enterprise, approved_by=user_id)
						self.saveInternalInvoice(entity=internal_voucher_to_be_saved)
						logger.info("Successfully Approved Internal Sales Voucher Id - %s, Internal Invoice Id - %s" %(
							str(internal_voucher_id),str(internal_invoice_id)))

						##Approve Purchase Voucher
						ledger_bill_id = internal_invoice.ledger_bill_id
						purchase_voucher_id  = self.getPurchaseVoucherIdIdbySalesLedgerId(ledger_bill_id)
						if purchase_voucher_id:
							internal_purchase_voucher = self.invoice_dao.db_session.query(Voucher).filter(
								Voucher.id == purchase_voucher_id).first()
							internal_puchase_voucher_to_be_saved = account_service.prepareVoucherApproval(
								voucher_to_be_saved=internal_purchase_voucher, enterprise=internal_invoice.enterprise, approved_by=user_id)
							self.saveInternalInvoice(entity=internal_puchase_voucher_to_be_saved)
							logger.info("Successfully Approved Internal Purchase Voucher Id - %s, Internal Invoice Id - %s" % (
								str(purchase_voucher_id), str(internal_invoice_id)))
		except Exception as e:
			logger.exception("Failed to Approve Internal Voucher.... - %s" % str(e))

	def getInternalInvoiceIdByParentVoucherId(self, voucher_id):
		internal_invoice_ids = []
		try:
			query = """SELECT ins.id FROM invoice AS ins WHERE ins.parent_invoice_id in (SELECT inv.id FROM invoice AS inv
			INNER JOIN ledger_bills AS lb ON inv.ledger_bill_id = lb.id INNER JOIN voucher AS v ON lb.voucher_id = v.id
			WHERE v.id = {voucher_id})""".format(voucher_id=voucher_id)
			query_data = executeQuery(query)
			for id in query_data:
				internal_invoice_ids.append(id[0])
		except Exception as e:
			logger.exception("Failed to Get Invoice .... - %s" % str(e))
		return internal_invoice_ids

	def getPurchaseVoucherIdIdbySalesLedgerId(self, sales_ledger_id):
		purchase_voucher_id = None
		query = """select v.id from ledger_bills as lb join voucher as v on lb.voucher_id=v.id 
		where lb.parent_ledgerbill_id={sales_ledger_id}""".format(sales_ledger_id=sales_ledger_id)
		query_data = executeQuery(query)
		if query_data:
			purchase_voucher_id = query_data[0][0]
		return purchase_voucher_id

	def getVoucherIdByInvoiveId(self, invoice_id):
		voucher_id = None
		try:
			query = """SELECT v.id FROM voucher AS v JOIN ledger_bills AS lb ON v.id = lb.voucher_id
					JOIN invoice AS inv ON inv.ledger_bill_id = lb.id WHERE inv.id = {invoice_id}""".format(invoice_id=invoice_id)
			query_data = executeQuery(query)
			if query_data:
				voucher_id = query_data[0][0]
		except Exception as e:
			logger.exception("Failed to Get Voucher Id .... - %s" % str(e))
		return voucher_id

	def construct_internal_invoice(self, enterprise_id, invoice_id, user_id):
		try:
			invoice = self.invoice_dao.db_session.query(Invoice).filter(Invoice.id == invoice_id).first()
			if invoice:
				internal_invoice_map = self.construct_internal_invoice_map(invoice)
				for target_enterprise, internal_list in internal_invoice_map.items():
					internal_inv_items, grand_total = self.construct_internal_inv_items(invoice, target_enterprise[0], internal_list)
					entity = self.create_internal_invoice_entity(enterprise_id, invoice, target_enterprise[0], internal_inv_items,
																 grand_total, user_id, internal_list)
					internal_inv_to_approve = self.saveInternalInvoice(entity)
					inv_service = InvoiceService()
					inv_service.approveInvoice(invoice_id=internal_inv_to_approve.id, enterprise_id=internal_inv_to_approve.enterprise_id,
											   approved_by=user_id, isprimary_project=False)
					logger.info("Internal invoice Successfully Approved - %s" % str(internal_inv_to_approve.id))
		except Exception as e:
			logger.exception("Construct Internal Invoice Failed... - %s" % str(e))

	def construct_internal_invoice_map(self, invoice):
		internal_invoice_map = {}
		for invoice_item in invoice.items:
			if invoice_item.oa_no:
				oa_id = invoice_item.oa_no
				requested_project_code = self.getProjectCodeByOAid(oa_id)
				item_id = invoice_item.item_id
				item_entity = self.getOAParticularByOAId(oa_id=oa_id, item_id=item_id)
				if item_entity and item_entity.internal_oa_id:
					internal_oa_id = item_entity.internal_oa_id
					target_enterprise_id = self.getEnterpriseIdByOaId(oa_id=internal_oa_id)
					if target_enterprise_id:
						internal_invoice_map.setdefault((target_enterprise_id,requested_project_code), [])
						internal_invoice_map[(target_enterprise_id,requested_project_code)].append(dict(oa_id=oa_id,internal_oa_id=internal_oa_id,item_id=item_id))
		return internal_invoice_map

	def getProjectCodeByOAid(self, oa_id):
		return self.invoice_dao.db_session.query(OA.project_code).filter(OA.id == oa_id).scalar()

	def construct_internal_inv_items(self, invoice, target_enterprise_id, internal_list):
		internal_inv_items = []
		grand_total = 0
		for item_count, rec in enumerate(internal_list, start=1):
			internal_oa_id = rec['internal_oa_id']
			item_id = rec['item_id']
			oa_id = rec['oa_id']
			for invoice_item in invoice.items:
				if invoice_item.item_id == int(item_id) and invoice_item.oa_no == int(oa_id):
					invoice_mat = InvoiceMaterial(item_id=item_id, enterprise_id=target_enterprise_id, make_id=1,
													is_faulty=invoice_item.is_faulty, oa_no=internal_oa_id)
					item_entity = self.getOAParticularByOAId(oa_id=internal_oa_id, item_id=item_id)
					invoice_mat.quantity = invoice_item.quantity
					invoice_mat.rate = item_entity.price
					invoice_mat.hsn_code = item_entity.hsn_code
					invoice_mat.discount = item_entity.discount
					invoice_mat.entry_order = item_count
					grand_total += (item_entity.price * invoice_item.quantity) * ((100 - item_entity.discount) / 100)
					internal_inv_items.append(invoice_mat)
					break
		return internal_inv_items, grand_total

	def create_internal_invoice_entity(self, enterprise_id, invoice, target_enterprise_id, internal_inv_items, grand_total, user_id, internal_list):
		internal_oa_id = internal_list[0]['internal_oa_id']
		parent_project_id = self.invoice_dao.db_session.query(OA.project_code).filter(OA.id==internal_oa_id).scalar()
		entity = Invoice(enterprise_id=target_enterprise_id)
		entity.issued_on = datetime.now()
		entity.type = invoice.type
		ledger = self.getLedgerId(enterprise_id=target_enterprise_id, group_id=8)
		entity.sale_account_id = ledger.id
		party_id = self.getPartyIdByProjectId(primary_enterprise_id=enterprise_id, project_enterprise_id=target_enterprise_id, project_id=parent_project_id)
		if not party_id:
			party_name, party_code = self.getProjectNameByProjectId(enterprise_id=enterprise_id, project_id=parent_project_id)
			party_id = createPartyToProjectEnterprise(project_enterprise_id=target_enterprise_id,
						party_name=party_name, party_code=party_code, user_id=user_id, db_session=self.invoice_dao.db_session)
		entity.party_id = party_id
		entity.items = internal_inv_items
		entity.grand_total = grand_total
		entity.project_code = parent_project_id
		entity.parent_invoice_id = invoice.id
		return entity

	def getProjectEnterpriseId(self, enterprise_id, project_id):
		"""
		Get Project Enterprise ID by parent Enterprise ID & Project ID
		"""
		project_enterprise_id = None
		try:
			project_enterprise_query = """SELECT project_enterprise_id FROM projects 
			WHERE enterprise_id={enterprise_id} AND id={project_id}""".format(enterprise_id=enterprise_id, project_id=project_id)
			query_data = executeQuery(project_enterprise_query)
			project_enterprise_id = query_data[0][0] if query_data else None
		except Exception as e:
			logger.exception("Get project Enterprise Id Failed... - %s" % str(e))
		return project_enterprise_id

	def getPartyIdByProjectId(self, primary_enterprise_id, project_enterprise_id, project_id):
		party_id = None
		try:
			query = """SELECT party_id FROM party_master WHERE enterprise_id={project_enterprise_id} AND 
			party_code = (SELECT code FROM projects WHERE id={project_id} AND enterprise_id={enterprise_id})""".format(enterprise_id=primary_enterprise_id,
			project_enterprise_id=project_enterprise_id, project_id=project_id)
			query_data = executeQuery(query)
			if query_data:
				party_id = query_data[0][0]
		except Exception as e:
			logger.exception("Party get failed from Project.... - %s" % str(e))
		return party_id

	def getProjectNameByProjectId(self, enterprise_id, project_id):
		party_name = party_code = None
		try:
			query = """SELECT name, code FROM projects WHERE id={project_id} AND enterprise_id={enterprise_id}""".format(enterprise_id=enterprise_id,
																														 project_id=project_id)
			query_data = executeQuery(query)
			party_name = query_data[0][0] if query_data else None
			party_code = query_data[0][1] if query_data else None
		except Exception as e:
			logger.exception("Party get failed from Project.... - %s" % str(e))
		return party_name, party_code

	def getOAParticularByOAId(self,oa_id,item_id):
		return self.invoice_dao.db_session.query(OAParticulars.internal_oa_id,OAParticulars.hsn_code,OAParticulars.price,
													   OAParticulars.discount).filter(OAParticulars.oa_id==oa_id,OAParticulars.item_id==item_id).first()

	def getLedgerId(self, enterprise_id, group_id):
		return self.invoice_dao.db_session.query(Ledger).filter(Ledger.group_id == group_id,
											   Ledger.enterprise_id == enterprise_id).first()

	def getPartyIdByPrimaryPartyname(self, enterprise_id, target_enterprise_id, primary_party_id):
		party_id = None
		try:
			query = """SELECT party_id FROM party_master WHERE enterprise_id={target_enterprise_id} AND 
			party_name = (SELECT party_name FROM party_master WHERE enterprise_id={enterprise_id} AND party_id={primary_party_id})""".format(enterprise_id=enterprise_id,
			target_enterprise_id=target_enterprise_id, primary_party_id=primary_party_id)
			query_data = executeQuery(query)
			if query_data:
				party_id = query_data[0][0]
		except Exception as e:
			logger.exception("Party get failed.... - %s" % str(e))
		return party_id

	def getEnterpriseIdByOaId(self, oa_id):
		project_enterprise_id = None
		try:
			project_enterprise_id = self.invoice_dao.db_session.query(OA.enterprise_id).filter(OA.id == oa_id).scalar()
		except Exception as e:
			logger.exception("Failed to Get Project Enterprise")
		return project_enterprise_id