"""
"""
from django.conf.urls import patterns, url

from erp.sales import json_api
from erp.sales.json_api import getOATax, getPartyList
from erp.sales.oa_views import manageOAPage, saveOA, approveOA, rejectOA, loadOA, \
	editOA, jobOAUsage, jobOAAgainstReceived, getPartySE, getSEDetails
from erp.sales.reports import generateSalesReport, generateInvoiceTaxReport, generateMaterialWiseTaxReport, generateOAReport
from erp.sales.se_views import manageSalesEstimatePage, loadSalesEstimate, saveSalesEstimate, \
	editSalesEstimate, approveSalesEstimate, rejectSalesEstimate, saveSalesEstimateContacts, reviewSalesEstimate, \
	updateSalesEstimateClientStatus, updateSEStatusforClient
from erp.sales.views import saveInvoice, manageInvoice, manageSalesHome, loadTax, loadTaxEdit, \
	approveInvoice, loadGSTaxes, rejectInvoice, sales_report_page, getPOMaterials, getPPStackQty, \
	getOAMaterials, getGRNMaterials, getOAHeader, getRecInvoiceQty, getOATags, getPOTags, loadPartyRate, \
	returnDcMaterial, \
	listCatalogueMaterialsJSON, loadBomCatalogueMaterials, getPODate, getOADueToday, getPaymentCollection, getOAOverDue, \
	getRecentCustomers, \
	getRecentSoldItems, getTopGeographies, loadInvoiceTags, getTopSalesCustomers, checkAgainstIssueReturnMaterial, \
	getDCHeader, getDCMaterials, itemInvoiced, loadInvoiceList, getSalesEstimateLogList, getSalesEstimateLogData, \
	getInvoiceEinvoiceList, getEinvoiceJson, cancelIrn, getInvoiceLogList, getInvoiceLogData, updateIrnResponse, \
	checkConsignment, getMRSMaterials, update_enterpriseid_in_session, getInternalInvoiceQty, getTotalCreditDebit, \
	saveProjectCashflow, getForecastVersions, getForecastByObjectId, getLedgerNameByAccountGroupId, \
	get_cashflow_overview_template, get_project_wise_cashflow_overview, consolidated_cashflow_report
from erp.stores.grn_views import manageSalesReturns, manageSalesReturn

__author__ = 'kalaivanan'

urlpatterns = patterns(
	'', url('home/$', manageSalesHome),
	url('sr_list/$', manageSalesReturns),
	url('sr/$', manageSalesReturn),
	url('invoice_list/$', loadInvoiceList),
	url('invoice/$', manageInvoice),
	url('issue/add/$', saveInvoice),
	url('invoice/approve/$', approveInvoice),
	url('invoice/reject/$', rejectInvoice),
	url('invoice/checkconsignment/$', checkConsignment),
	url('json/invoice/load_gst_choices/$', loadGSTaxes),
	url('json/invoice/getTax/$', loadTax),
	url('json/invoice/editTax/$', loadTaxEdit),
	url('json/invoice/getPOMaterials/$', getPOMaterials),
	url('json/invoice/getPPStockqty/$', getPPStackQty),
	url('json/invoice/get_catalogue_materials/$', listCatalogueMaterialsJSON),
	url('json/bom_catalogue_materials/$', loadBomCatalogueMaterials),
	url('json/invoice/getMRSMaterials/$', getMRSMaterials),

	url('json/oa_materials/$', getOAMaterials),
	url('json/invoice/getGRNMaterials/$', getGRNMaterials),
	url('json/invoice/getOAHeader/$', getOAHeader),
	url('json/invoice/load_oa_tags/$', getOATags),
	url('json/invoice/load_po_tags/$', getPOTags),
	url('json/invoice/load_invoice_tags/$', loadInvoiceTags),
	url('json/invoice/loadPartyRate/$', loadPartyRate),
	url('json/invoice/load_po_date/$', getPODate),
	url('json/invoice/getDCHeader/$', getDCHeader),
	url('json/invoice/getDCMaterials/$', getDCMaterials),
	url('json/invoice/getInvoiceEinvoiceList', getInvoiceEinvoiceList),
	url('json/invoice/getEinvoiceJson', getEinvoiceJson),
	url('json/invoice/cnlIrn/$', cancelIrn),
	url('json/invoice/uploadEinvoiceAcknowledgement/$', updateIrnResponse),

	url('json/invoice/getinvoiceloglist/$', getInvoiceLogList),
	url('json/invoice/getinvoicelogdata/$', getInvoiceLogData),

	url('json/invoice/dc_return/$', returnDcMaterial),
	url('json/invoice/item_invoiced/$', itemInvoiced),

	url('json/invoice/get_ordersdue_today/$', getOADueToday),
	url('json/invoice/get_payment_collection/$', getPaymentCollection),
	url('json/invoice/get_oa_overdue/$', getOAOverDue),
	url('json/invoice/get_recent_customers/$', getRecentCustomers),
	url('json/invoice/get_recent_sold_items/$', getRecentSoldItems),
	url('json/invoice/top_geographies/$', getTopGeographies),
	url('json/invoice/top_customers/$', getTopSalesCustomers),
	url('json/invoice/check_against_issue_return_material/$', checkAgainstIssueReturnMaterial),

	url('reports/$', generateSalesReport),
	url('inv_tax_report/$', generateInvoiceTaxReport),
	url('material_tax_report/$', generateMaterialWiseTaxReport),
	url('oa_report/$', generateOAReport),

	# Sales report page
	url('sales-report-statement/$', sales_report_page),

	# Sales API Service
	url('json/dashboard/', json_api.getDashboardData),
	url('json/salesDetail/', json_api.getPaymentOADetail),
	url('json/finance_year/', json_api.getInvoiceFinanceYear),
	url('json/invoiceSearch/', json_api.searchInvoice),
	url('json/draft_invoice_fetch/', json_api.fetchDraftInvoice),
	url('json/invoice_material/', json_api.getInvoiceMaterial),
	url('json/invoice_material_overdue/', json_api.getInvoiceMaterialOverDue),
	url('json/save_invoice_page/', json_api.saveInvoice),
	url('json/oa_finance_year/', json_api.getOAFinanceYear),
	url('json/oa_search/', json_api.searchOA),
	url('json/draft_oa/', json_api.fetchDraftOA),
	url('json/oa_material/', json_api.getOaMaterial),
	url('json/oa_doc/', json_api.downloadOADocument),
	url('json/inv_doc/', json_api.downloadInvoiceDocument),
	url('json/oa_status/', json_api.getOAStatus),
	url('json/se_doc/', json_api.downloadSalesEstimateDocument),
	url('json/se/se_attachment_upload/$', json_api.uploadAttachmentSE),
	url('json/se/se_attachment_list/$', json_api.getAllAttachmentSE),
	url('json/se/delete_se_attachment/$', json_api.deleteAttachmentSE),
	url('json/invoiceDispatch/', json_api.getInvoiceforDispatch),
	url('json/serialNumberSave/', json_api.saveInvoiceSerialNumber),
	url('json/serialNumberDelete/', json_api.deleteInvoiceSerialNumber),
	url('json/invoicePackingInfoSave/', json_api.saveInvoicePackingInfo),
	url('json/print_packing_slip/', json_api.generatePackingSlip),

	# OA Page URLs

	url('oa/$', manageOAPage),
	url('oa/view/$', loadOA),
	url('oa/addOA/$', saveOA),
	url('oa/editOA/$', editOA),
	url('oa/approve/$', approveOA),
	url('oa/reject/$', rejectOA),
	url('json/order_ack/$', json_api.orderAcknowledgement),
	url('json/get_party_se/$', getPartySE),
	url('json/get_se_details/$', getSEDetails),
	url('json/oa/checkoainvoice_qty/$', getRecInvoiceQty),
	url('json/get_latest_invoice_serial_no/', json_api.getLatestInvoiceSerialNo),
	url('json/super_edit_invoice_code/', json_api.superEditInvoiceCode),
	url('json/super_edit_issue_code/', json_api.superEditIssueCode),
	url('json/super_edit_oa_code/', json_api.superEditOACode),
	url('json/super_edit_se_code/', json_api.superEditSECode),
	url('json/get_invoice_linked_message/', json_api.getInvoiceLinkedMessage),
	url('json/get_dc_linked_message/', json_api.getDCLinkedMessage),
	url('json/get_oa_linked_message/', json_api.getOALinkedMessage),
	url('json/get_jo_linked_message/', json_api.getJOLinkedMessage),
	url('json/getpartydc/', json_api.getPartyDC),
	url('json/get_party_financial_years/', json_api.getPartyFinancialYears),
	url('json/load_selected_dc_numbers/', json_api.getInvoiceDC),
	url('json/oa/job_oa_received/$', jobOAAgainstReceived),
	url('json/oa/job_oa_usage/$', jobOAUsage),
	url('json/get_oa_tax/$', getOATax),
	url('json/party_list/$', getPartyList),

	# Sales Estimate Page URLs
	url('sales_estimate/$', manageSalesEstimatePage),
	url('sales_estimate/view/$', loadSalesEstimate),
	url('sales_estimate/addSalesEstimate/$', saveSalesEstimate),
	url('sales_estimate/editSalesEstimate/$', editSalesEstimate),
	url('sales_estimate/approve/$', approveSalesEstimate),
	url('sales_estimate/reject/$', rejectSalesEstimate),
	url('sales_estimate/review/$', reviewSalesEstimate),
	url('sales_estimate/saveSalesContactPerson/$', saveSalesEstimateContacts),
	url('sales_estimate/getloglist/$', getSalesEstimateLogList),
	url('sales_estimate/getlogdata/$', getSalesEstimateLogData),
	url('sales_estimate/client_status/$', updateSalesEstimateClientStatus),
	url('sales_estimate/update_client_status/$', updateSEStatusforClient),

	# Cash Flow APIs
	url('update/enterprise_id/$', update_enterpriseid_in_session),
	url('json/total_credit_debit/$', getTotalCreditDebit),
	url('json/internal_invoice_qty/$', getInternalInvoiceQty),
	url('json/save_forecast/$', saveProjectCashflow),
	url('json/forecast_versions/$', getForecastVersions),
	url('json/getforecastdata/$', getForecastByObjectId),
	url('json/getledgername/$', getLedgerNameByAccountGroupId),
	url('json/get_project_wise_cashflow_overview/$', get_project_wise_cashflow_overview),
	url('get_project_wise_cashflow_overview/$', get_cashflow_overview_template),
	url('consolidated_cashflow_report/$', consolidated_cashflow_report)
)
