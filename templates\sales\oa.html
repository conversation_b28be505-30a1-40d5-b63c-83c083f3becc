{% extends "sales/sidebar.html" %}
{% block oa %}
<style type="text/css">
	.file {
	  visibility: hidden;
	  position: absolute;
	}

	.form-inline .fixed-width-medium,
	.form-inline .fixed-width-medium {
		width: 200px;
		max-width: 250px;
		min-width: 180px;
	}

	.btn-basic:focus {
		background: #209be1;
		color: #FFF;
	}

	.form-inline .chosen-container {
		display: inline-block;
		width: 180px !important;
	}

	@-moz-document url-prefix() {
	  .moz-style-width {
	     width: 396px !important;
	  }
	}

	.net_total {
		font-size: 34px;
		font-weight: bold;
		font-family: monospace;
	}

	.basic_total {
		font-size: 20px !important;
		font-weight: bold;
	}

	#add_new_material:focus,
	#add_new_material_ns:focus {
		background: #209be1;
		color: #FFF;
	}

	.bootstrap-filestyle input {
		width: 241px !important;
	}

	#oa_particulars_table tbody tr td,
	#oa_particulars_table_ns tbody tr td {
		vertical-align: top !important;
	}

	#oa_particulars_table .custom-error-message,
	#oa_particulars_table_ns .custom-error-message {
		position: relative;
	}

	.bootstrap-filestyle {
		display: none !important;
	}

	#id_oa-document_description {
		width: 100%;
	}

	#id_oa_particular-__prefix__-all_units {
	    padding: 4px 6px;
	    height: 26px;
	    box-shadow: 0 0;
	    font-size: 12px;
	 }
	.table.text_box_in_table .chosen-container-single .chosen-single div b {
		margin-top: 2px;
	}
	.table.text_box_in_table .chosen-single {
			height: 26px;
			height: 26px;
		    font-size: 13px;
		    line-height: 24px;
		    padding: 0 0 0 8px;
	}

	#div_con_rate .custom-error-message {
		display: block;
		margin-left: 205px;
	}
	.hsn-suggestion-internal {
	    margin-top: 0 !important;
	}
	.item_text_box,
	 .item-with-service input{
		width: calc(100% - 30px);
	}

	.service-item-flag.floated-right-flag {
		float: right;
    	margin-top: -20px;
	}
</style>
<link rel="stylesheet" href="/site_media/css/quality-inspection-report.css?v={{ current_version }}" >
<link rel="stylesheet" type="text/css" href="/site_media/css/erp_default.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/oa.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/tags.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/party_list.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/usage_report.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/load_material.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/Component/projects.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/quality-inspection-report.js?v={{ current_version }}"></script>

<div style="display:none">
	<form id="oa_submit" method="post" action="/erp/sales/oa/editOA/">
		{%csrf_token%}
		<input type="hidden" value="{% if oa.id.value %}{{oa.id.value}}{% endif %}" id="id_oa-id" name="oa_no"/>
		<input type="submit" value="Edit" id="oa_resubmit" hidden="hidden"/>
	</form>
</div>
<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">{% if oa.id.value == '' or oa.id.value == None %} New {% endif %}Order Acknowledgement</span>
	</div>
	<div class="container-fluid" style="margin:0 15px;">
		<div class="page-heading_new" style="padding: 0;">
			<span class="page_header">
				<span class="header_current_page"> {% if oa.id.value != '' and oa.id.value != None %} {{ oa.code.value }} {% endif %}</span>
			</span>
			<input type="hidden" value={{oa.status.value}} id="approved_status">
			{% if logged_in_user.is_super %}
				<a class="btn super_user_icon hide" onclick="editOANumber(true);" data-tooltip="tooltip" data-placement="bottom" data-title="Super User"  style="">
					<i class="fa fa-pencil"></i>
				</a>
				<div class="xsid_number_edit hide">
					<form class="form-inline" style="display: inline-block;" action="">
					    <div class="form-group">
					      	<input type="text" class="form-control" id="oa_financial_year" name="oa_financial_year" maxlength="5">
					    </div>
					    <div class="form-group">
						    <select class="form-control" name="oa_type" id="oa_type" style="width: 60px;">
							   {% for type in oa_type %}
							        <option value="{{type.0}}" data-val="{{type.2}}">&nbsp;{{type.2}} &nbsp;&nbsp;&nbsp;({{type.1}})</option>
							   {% endfor %}
						    </select>
					    </div>
					    <div class="form-group">
					    	<input type="text" id="oa_number" name="oa_number" class="form-control" maxlength="6" onfocus="setNumberRangeOnFocus(this,6,0,true)" style="width: 90px;" />
					    </div>
					    <div class="form-group">
					      	<input type="text" class="form-control text-lowercase" onkeypress="validateStringOnKeyPress(this,event,'character');" onblur="validateStringOnBlur(this,event,'character');" id="oa_number_division" name="oa_number_division" maxlength="1" >
					    </div>
					    <div class="form-group super_edit_submit_icon">
					    	<span role='button' data-tooltip="tooltip" title="Save" class="super_edit_save" onclick="SaveOANumber();"><i class="fa fa-check"  aria-hidden="true" style="color: green;"></i></span>
					    	<span role='button' data-tooltip="tooltip" title="Cancel" class="super_edit_cancel" onclick="DiscardEditOANumber();"><i class="fa fa-times" aria-hidden="true" style="color: red;"></i></span>
					    </div>
			  		</form>
			  		<div class="save_xsid_error_format hide">Required. Please check help tool for valid format.</div>
			  	</div>
			  	<div class="super_user_tool_tip hide"><span class="question-mark"></span></div>
				<!--<span class="super_user_tool_tip hide"><img src="/site_media/images/tooltip.png" style="vertical-align: top;" /></span>-->
			{%else%}
				<a class="btn super_user_icon hide" onclick="" data-tooltip="tooltip" data-placement="right" data-title="Only Super User can edit this field">
					<i class="fa fa-pencil" style="color:#777;" ></i>
				</a>
			{% endif %}
			<a href="/erp/sales/oa/view/" class="btn btn-add-new pull-right" data-tooltip="tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
			<span class="prev_next_container"></span>
			{% if oa.id.value != '' and oa.id.value != None and oa.type.value == 'Job' and oa.status.value > 0 %}
				<div class="pull-right">
					<a class="btn pull-right btn-warning-border" style="margin-right: 7px;" onclick="javascript:showJobOaUsageReport();">Material Usage</a>
					<a class="btn pull-right btn-warning-border" style="margin-right: 7px;" onclick="javascript:showReceivedItems();">Materials Received</a>
				</div>
			{%endif%}
		</div>
		 <form action="" id="gcs_upload_general" name="gcs_upload_oa" class="google_upload_form" method="POST" enctype='multipart/form-data'>
			<span id="oa_attachment_uploader_container"></span>
			<input type='file' name='file' onChange="convertFileToBase64(this)" style="display:none" />
		    <input type='submit' value='Upload File' style="display:none" />
        </form>
		<form id="oa_add"  method="post" action="/erp/sales/oa/addOA/" class="col-sm-12 remove-padding">
			{%csrf_token %}
			<div class="row">
				<div class="col-sm-8"><!-- Tags are hidden on the 2.16.3 release -->
					<div class="col-sm-12 form-group hide" style="padding: 0;" id="tags">
						<label>Tags</label>
						<label class="ui-autocomplete-loading hide">&nbsp;</label>
						<ul id="ul-tagit-display" class="tagit-display form-control">
							{% for tag_form in tags_formset.initial_forms %}
							<li class="li-tagit-display" id="{{ tag_form.prefix }}">
								<div hidden="hidden">{{ tag_form.tag }}
									{{ tag_form.DELETE }}{{ tag_form.ORDER }}</div>
								<label class="label-tagit-display"  id="id_{{tag_form.prefix}}-tag_label">{{ tag_form.tag.value }}</label>&nbsp;
								<a class="delete_tag" id="{{tag_form.prefix}}-del_button"></a>
								&nbsp;
							</li>
						{% endfor %}
							<li id="tag-__dummy__" hidden="hidden">
								<div hidden="hidden">{{ tags_formset.empty_form.tag }}
								{{ tags_formset.empty_form.ORDER }}
								{{ tags_formset.empty_form.DELETE }}</div>
								<label class="label-tagit-display"  id="id_{{tags_formset.empty_form.prefix}}-tag_label"></label>&nbsp;
								<a class="delete_tag" id="{{tags_formset.empty_form.prefix}}-del_button"></a>
								&nbsp;
							</li>

						<span>
							{{ tags_formset.management_form }}
							<span id="{{ tags_formset.empty_form.prefix }}" class="text_for_tag" >
								{{ tags_formset.empty_form.tag }}
								{{ tags_formset.empty_form.ORDER }}
								<span hidden="hidden">{{ tags_formset.empty_form.DELETE }}</span>
							</span>
						</span>
						</ul>
						<span style="position: absolute; margin-top: -8px; color: #aaa; font-size: 11px;">Type and press enter to add new tag.</span>
					</div>
					<div class="col-sm-2 hide" style="margin-top: 21px;">
						<input id="add_tag" class="btn btn-save btn-margin-1" value="+" type="button">
					</div>
				</div>
				<div class="col-sm-4"></div>
			</div>
			<div class="row">
				<div class="col-sm-8">
					<div class="row">
						<div class="form-group col-sm-4 for-primary-ent">
							<div class="tour_project_tag">
								<div class="component_project" data-id="id_oa-project_code" data-name="oa-project_code" data-isSuper={{logged_in_user.is_super}} data-value="{% if selected_project_code %}{{ selected_project_code }}{% else %}none{% endif %}"></div>
							</div>
						<label id="expRev" style="display: block;margin-top: 20px;">Revenue: <span id="revenue" style="margin-right: 10px;">0</span>Expense : <span id="expense" style="margin-right: 10px;">0</span> </label>
<!--							<span class="side-header" style="width: 190px;">Project/Tag</span>-->
<!--							<span class="side-content ">{{ oa.project_code }}</span>-->
						</div>
						<div class="form-group col-sm-8"></div>
					</div>
					<div class="row">
						<div class="col-sm-4">
							<label>
								{% if logged_in_user.is_super %}
									<a class="super_edit_field super_edit_party hide for-primary-ent" onclick="SuperEditOADetails(this);" data-tooltip="tooltip" data-placement="left" title="Super Edit">
										<i class="fa fa-pencil super_edit_in_field"></i>
									</a>
								{%else%}
									<a class="super_edit_field super_edit_party hide for-primary-ent" onclick="" data-tooltip="tooltip" data-placement="left" title="Only Super User can edit this field">
										<i class="fa fa-pencil super_edit_in_field" style="color:#777;"></i>
									</a>
								{%endif%}
								Party Name<span class="mandatory_mark"> *</span>
							</label>
							<div class="{% if oa.status.value > 0 %}div-disabled{% endif %}">
							{{oa.party_id}}
							<input type="hidden" value="" id="se_party"/>
							</div>
							{{oa.enterprise_id}}
							{{oa.id}}
							{{oa.prepared_on}}
							{{oa.oa_no}}
							{{oa.se_id}}
							{{oa.se_date}}
						</div>
						<div class="col-sm-4">
							<label>
							{% if logged_in_user.is_super %}
								<a class="super_edit_field super_edit_type hide" onclick="SuperEditOADetails(this);" data-tooltip="tooltip" data-placement="left" title="Super Edit">
									<i class="fa fa-pencil super_edit_in_field"></i>
								</a>
							{%else%}
								<a class="super_edit_field super_edit_type hide" onclick="" data-tooltip="tooltip" data-placement="left" title="Only Super User can edit this field">
									<i class="fa fa-pencil super_edit_in_field" style="color:#777;"></i>
								</a>
							{%endif%}
							Type</label>
							<div class="{% if oa.status.value > 0 %}div-disabled{% endif %}">
								{{oa.type}}
							</div>
						</div>
					</div>
				</div>

				{% if oa.id.value != '' and oa.id.value != None %}
					<div class="col-sm-4" style="margin-top: 16px; float:right;">
						<table border="0" class="side-table table text_box_in_table">
							<tr class="se_no_row for-primary-ent">
								<td class="side-header" style="width: 180px;">Estimate No.</td>
								<td class="side-content" id="div_se_no_display">
									{{oa.se_no}}
									<select class="form-control chosen-select" name="oa-se_no_display" id="id_oa-se_no_display" style="display: none;">
										<option value="0">--select--</option>
									</select>
								</td>
							</tr>
							<tr>
								<td class="side-header" id="oaNo_header" style="width: 150px;">O.A No.</td>
								<td class="side-content">{{ oa.code.value }}</td>
							</tr>
							<tr>
								<td class="side-header" id="oaDate_header" style="width: 150px;">O.A Date& Time</td>
								<td class="side-content">{{oa.approved_on.value|date:'M d, Y H:i:s'}}</td>
							</tr>
						</table>
					</div>
				{% else %}
					<div class="col-sm-4" style="margin-top: 16px;float:right;">
						<table border="0" class="side-table table text_box_in_table">
							<tr class="se_no_row for-primary-ent">
								<td class="side-header" style="width: 180px;">Estimate No.</td>
								<td class="side-content" id="div_se_no_display">
									{{oa.se_no}}
									<select class="form-control chosen-select" name="oa-se_no_display" id="id_oa-se_no_display" style="display: none;">
										<option value="0">--select--</option>
									</select>
								</td>
							</tr>
						</table>
					</div>
				{% endif %}
			</div>
			<div class="row form-group">

			<div class="col-sm-4">
				{{oa.grand_total}}
			</div>
		</div>
		<div class="row">
			<div class="col-sm-12">
				<div style="padding: 10px 5px;">
					<ul class="nav nav-tabs list-inline">
						<li class="active"><a data-toggle="tab" href="#tab1" id="tabs_stock">Item Particulars</a></li>
						<li style="margin-top: 11px"><span class='service-item-flag'></span> - Service</li>
						<span class="pull-right" style="font-size: 16px; margin-top: -10px;">Grand Total : <span class="net_total"></span></span>
					</ul>
				</div>
				<div class="tab-content">
					<a href="#"  id="short_close" class="download_stock_report pull-right for-primary-ent" data-tableid="tablesorter" >Short Close</a>
					<div id="tab1" class="tab-pane fade in active">

							<table class="table custom-table table-bordered" id="oa_particulars_table">
								<thead>
									<tr>
										<th width="50px">S.No.</th>
										<th style="min-width: 200px;">ITEM NAME</th>
										<th style="width: 100px;" class="textbox_header_amt">HSN/SAC</th>
										<th style="width: 200px;">REMARKS</th>
										<th class="textbox_header_amt">PRICE / UNIT</th>
										<th style="width: 100px;" class="textbox_header_amt">Disc.(%)</th>
										<th class="textbox_header_amt" id="for-primary-ent-oa_qty">OA QTY</th>
										<th class="textbox_header_amt">INVOICE QTY</th>
										<th class="textbox_header_amt">UNIT</th>
										<th class="textbox_header_amt">AMOUNT</th>
										<th width="50px">ACTION</th>
									</tr>
								</thead>
								<tbody class="item-for-goods hide">
									{{ oa_particular_formset.management_form }}
									{{oa_particular_formset.empty}}


									<!-- Item editing List -->
									{% for oa_particulars_form in oa_particular_formset.initial_forms %}
										{% if oa_particulars_form.is_service.value == "False" %}
										<tr bgcolor="#ececec" id="{{oa_particulars_form.prefix}}">
											<td hidden="hidden">
												{{ oa_particulars_form.oa_id }}
												{{ oa_particulars_form.DELETE }}
												{{ oa_particulars_form.enterprise_id }}
												{{ oa_particulars_form.make_label }}
												{{ oa_particulars_form.make_id }}
												{{ oa_particulars_form.inv_quantity }}
												{{ oa_particulars_form.is_faulty }}
												{{ oa_particulars_form.is_service }}
												{{ oa_particulars_form.item_id }}
												{{ oa_particulars_form.alternate_unit_id }}
												{{ oa_particulars_form.scale_factor }}
												<input name="item_tax"  type="text" hidden="hidden" id="id_{{oa_particulars_form.prefix}}-item_tax">
												{{oa_particulars_form.project_id}}
												{{oa_particulars_form.internal_price}}
												{{oa_particulars_form.internal_oa_id}}
											</td>
											<td class="text-center s_no_item">{{forloop.counter}}</td>
											<td class="table_text_box">
												{{ oa_particulars_form.item_code }}
												{{ oa_particulars_form.item_name }}
											</td>
											<td hidden="hidden" class='text-left'><div id="id_{{ oa_particulars_form.prefix }}-makeLabel"></div></td>
											<td class="table_text_box td-hsn-code hsn-wrapper">
												{{ oa_particulars_form.hsn_code }}
											</td>
											<td class="table_text_box">
												{{ oa_particulars_form.remarks }}
											</td>
											<td class="table_text_box">
												{{ oa_particulars_form.price }}
											</td>
											<td class="table_text_box">
												{{ oa_particulars_form.discount }}
											</td>
											<td class="table_text_box">
												{{ oa_particulars_form.quantity }}
											</td>
											<td class="text-right table_text_box">
												<div id="id_{{ oa_particulars_form.prefix }}-inv_qtyLabel">{% if oa.id.value and oa.id.value != "" %} {{ oa_particulars_form.inv_quantity.value }} {% endif %}</div>
											</td>
											<td class="text-center table_text_box">
												{{ oa_particulars_form.unit_id.value }}
											</td>
											<td class="text-right table_text_box">
												{{oa_particulars_form.amount }}
											</td>
											<td class="text-center">
												<a href="#" tabindex="-1"
												   id="id_{{ oa_particulars_form.prefix }}-deleteOAParticulars"
												   onclick="javascript:deleteOAParticulars('{{ oa_particulars_form.prefix }}')">
													<i class="fa fa-trash-o"></i>
												</a>

												<a class="for-primary-ent" href="#" data-tooltip="tooltip" data-placement="right" title="Internal Work Order" onclick="addInternalPrice(this)">
													{% if oa_particulars_form.project_id.value == "None" %}
													<img id="oa_parti_details" src='/site_media/images/clipboard-solid.png' style="width: 15px; margin-top: -6px;" />
													{% else %}
														<img src='/site_media/images/clipboard-regular.png' style="width: 30px; margin-top: -6px;" />
													{% endif %}
												</a>
											</td>
										</tr>
										{% endif %}
									{% endfor %}
								</tbody>
								<tbody class="item-for-service hide">
									{% for oa_particulars_form in oa_particular_formset.initial_forms %}
										{% if oa_particulars_form.is_service.value == "True" %}
										<tr bgcolor="#ececec" id="{{oa_particulars_form.prefix}}">
											<td hidden="hidden">
												{{ oa_particulars_form.oa_id }}
												{{ oa_particulars_form.DELETE }}
												{{ oa_particulars_form.enterprise_id }}
												{{ oa_particulars_form.make_label }}
												{{ oa_particulars_form.make_id }}
												{{ oa_particulars_form.inv_quantity }}
												{{ oa_particulars_form.is_faulty }}
												{{ oa_particulars_form.is_service }}
												{{ oa_particulars_form.item_id }}
												{{ oa_particulars_form.alternate_unit_id }}
												{{ oa_particulars_form.scale_factor }}
												<input name="item_tax"  type="text" hidden="hidden" id="id_{{oa_particulars_form.prefix}}-item_tax">
												{{oa_particulars_form.project_id}}
												{{oa_particulars_form.internal_price}}
												{{oa_particulars_form.internal_oa_id}}
											</td>
											<td class="text-center s_no_item">{{forloop.counter}}</td>
											<td class="table_text_box item-with-service">
												{{ oa_particulars_form.item_code }}
												{{ oa_particulars_form.item_name }}
												<span class="service-item-flag floated-right-flag"></span>
											</td>
											<td hidden="hidden" class='text-left'><div id="id_{{ oa_particulars_form.prefix }}-makeLabel"></div></td>
											<td class="table_text_box td-hsn-code hsn-wrapper">
												{{ oa_particulars_form.hsn_code }}
											</td>
											<td class="table_text_box">
												{{ oa_particulars_form.remarks }}
											</td>
											<td class="table_text_box">
												{{ oa_particulars_form.price }}
											</td>
											<td class="table_text_box">
												{{ oa_particulars_form.discount }}
											</td>
											<td class="table_text_box">
												{{ oa_particulars_form.quantity }}
											</td>
											<td class="text-right table_text_box">
												<div id="id_{{ oa_particulars_form.prefix }}-inv_qtyLabel">{% if oa.id.value and oa.id.value != "" %} {{ oa_particulars_form.inv_quantity.value }} {% endif %}</div>
											</td>
											<td class="text-center table_text_box">
												{{ oa_particulars_form.unit_id.value }}
											</td>
											<td class="text-right table_text_box">
												{{oa_particulars_form.amount }}
											</td>
											<td class="text-center">
												<a href="#" tabindex="-1"
												   id="id_{{ oa_particulars_form.prefix }}-deleteOAParticulars"
												   onclick="javascript:deleteOAParticulars('{{ oa_particulars_form.prefix }}')">
													<i class="fa fa-trash-o"></i>
												</a>
												<a class="for-primary-ent" href="#" data-tooltip="tooltip" data-placement="right" title="Internal Work Order" onclick="addInternalPrice(this)">
													{% if oa_particulars_form.project_id.value == "None" %}
													<img id="oa_parti_details" src='/site_media/images/clipboard-solid.png' style="width: 15px; margin-top: -6px;" />
													{% else %}
														<img src='/site_media/images/clipboard-regular.png' style="width: 30px; margin-top: -6px;" />
													{% endif %}
												</a>
											</td>
										</tr>
										{% endif %}
									{% endfor %}
								</tbody>
								<tfoot>
									<tr hidden="hidden" bgcolor="#ececec" id="oa_particular-__dummy__">
										<td hidden="hidden">
											{{ oa_particular_formset.empty_form.oa_id }}
											{{ oa_particular_formset.empty_form.DELETE }}
											{{ oa_particular_formset.empty_form.enterprise_id }}
											{{ oa_particular_formset.empty_form.make_id }}
											{{ oa_particular_formset.empty_form.is_faulty }}
											{{ oa_particular_formset.empty_form.is_service }}
											{{ oa_particular_formset.empty_form.inv_quantity }}
											{{ oa_particular_formset.empty_form.item_id }}
											{{ oa_particular_formset.empty_form.alternate_unit_id }}
											{{ oa_particular_formset.empty_form.scale_factor }}
											<input name="item_tax" id="id_oa_particular-__prefix__-item_tax" type="text" hidden="hidden">
											{{ oa_particular_formset.empty_form.project_id }}
											{{ oa_particular_formset.empty_form.internal_price }}
											{{ oa_particular_formset.empty_form.internal_oa_id}}
										</td>
										<td class="text-center s_no_item"><div id="id_oa_particular-__prefix__-s_no"></div></td>
										<td class="table_text_box">
											{{ oa_particular_formset.empty_form.item_code }}
											{{ oa_particular_formset.empty_form.item_name}}
										</td>
										<td class="table_text_box td-hsn-code hsn-wrapper">
											<div>{{ oa_particular_formset.empty_form.hsn_code }}</div>
										</td>
										<td class="table_text_box">
											{{ oa_particular_formset.empty_form.remarks}}
										</td>
										<td class="table_text_box">
											{{ oa_particular_formset.empty_form.price}}
										</td>
										<td class="table_text_box">
											<div>{{ oa_particular_formset.empty_form.discount }}</div>
										</td>
										<td class="table_text_box">
											{{ oa_particular_formset.empty_form.quantity }}
										</td>
										<td>

										</td>
										<td class="table_text_box">
											{{ oa_particular_formset.empty_form.unit_id }}
											<div class="alternate_unit_select_box hide">
												{{ oa_particular_formset.empty_form.alternate_units }}
											</div>
										</td>
										<td class="text-right table_text_box">
											{{oa_particular_formset.empty_form.amount }}
										</td>
										<td class="text-center">
											<a href="#" tabindex="-1"
											   id="id_{{ oa_particular_formset.empty_form.prefix }}-deleteOAParticulars"
											   onclick="javascript:deleteOAParticulars('{{ oa_particular_formset.empty_form.prefix }}')">
												<i class="fa fa-trash-o"></i>
											</a>
											<a class="for-primary-ent" href="#" data-tooltip="tooltip" data-placement="right" title="Internal Work Order" onclick="addInternalPrice(this)">
												<img id="oa_parti_details" src='/site_media/images/clipboard-solid.png' style="width: 15px; margin-top: -6px;" />
											</a>
										</td>
									</tr>
									<tr bgcolor="#ececec" id="{{oa_particular_formset.empty_form.prefix}}" class="for-primary-ent">
										<td hidden="hidden">
											{{ oa_particular_formset.empty_form.oa_id }}
											{{ oa_particular_formset.empty_form.DELETE }}
											{{ oa_particular_formset.empty_form.enterprise_id }}
											{{ oa_particular_formset.empty_form.make_id }}
											{{ oa_particular_formset.empty_form.is_faulty }}
											{{ oa_particular_formset.empty_form.is_service }}
											{{ oa_particular_formset.empty_form.item_id }}
											{{ oa_particular_formset.empty_form.alternate_unit_id }}
											{{ oa_particular_formset.empty_form.scale_factor }}
											<input type="text" value="" class="" id="material_id_hidden" placeholder="" hidden="hidden">
										</td>
										<td class="text-center">{{forloop.counter}}</td>
										<td class="table_text_box">
											{{ oa_particular_formset.empty_form.item_code }}
											{{ oa_particular_formset.empty_form.item_name}}
											<span class="material-removal-icon removal-icon-table hide" style="padding: 4px 15px; margin-top: -26px;">
												<i class="fa fa-times"></i>
											</span>
										</td>
										<td class="table_text_box td-hsn-code hsn-wrapper">
											{{ oa_particular_formset.empty_form.hsn_code }}
										</td>
										<td class="table_text_box">
											{{ oa_particular_formset.empty_form.remarks}}
										</td>
										<td class="table_text_box">
											{{ oa_particular_formset.empty_form.price}}
											<input type="hidden" id="material_rate_hidden" class="form-control"/>
										</td>
										<td class="table_text_box">
											{{ oa_particular_formset.empty_form.discount }}
										</td>
										<td class="table_text_box">
											{{ oa_particular_formset.empty_form.quantity }}
										</td>
										<td></td>
										<td class="table_text_box">
											{{ oa_particular_formset.empty_form.unit_id }}
											<div class="alternate_unit_select_box hide">
												{{ oa_particular_formset.empty_form.alternate_units }}
											</div>
											<div class="all_units_select_box hide">
												{{ oa_particular_formset.empty_form.all_units }}
											</div>
										</td>
										<td class="text-right table_text_box">
											{{oa_particular_formset.empty_form.amount }}
										</td>
										<td></td>
									</tr>
									<tr class="oa_new_content" id="id-total_section">
										<td style="border: none;"></td>
										<td colspan="2"  style="border: none;">
											<span id="description_display" placeholder="Display Description" class="textbox-as-label" style="background-color: #eee; cursor: text;"></span>
										</td>
										<td colspan="5" style="border: none;"></td>
										<td class="grand-total-text text-center" style="vertical-align: middle !important;"><b>TOTAL</b></td>
										<td class="text-right oa_total_amt grand-total-amount" style="width: auto; vertical-align: middle !important;">0.00</td>
										<td class="text-center for-primary-ent"><input type="button" class="btn" id="add_new_material" onclick="AddNewMateriaRow();" value="Add"></td>
									</tr>
								</tfoot>
							</table>

						</div>
					</div>
				</div>
			</div>
			<div class="row for-primary-ent">
				<div class="col-sm-5">
					<div class="form-inline">
						<div class="form-group" style="margin-bottom: 20px;">
						    <label for="currency" style="width: 202px;">
							    {% if logged_in_user.is_super %}
								    <a class="super_edit_inline_field super_edit_party currency" onclick="SuperEditOADetails(this);" data-tooltip="tooltip" data-placement="left" title="Super Edit">
										<i class="fa fa-pencil super_edit_in_field"></i>
									</a>
							    {%else%}
							        <a class="super_edit_inline_field super_edit_party currency" onclick="" data-tooltip="tooltip" data-placement="left" title="Only Super User can edit this field">
										<i class="fa fa-pencil super_edit_in_field" style="color:#777;"></i>
									</a>
							    {%endif%}
							    Currency
                            </label>
							<span class="div-disabled">
						    {{ oa.currency_id}}
							</span>
                            <div id="div_con_rate" class="conversion_rate_container hide" style="margin-left: 208px;">
                                <span class="currency_convertor_section">
                                    <span>1</span>
                                    <span class="converted_currency_txt"></span>
                                    <span>=</span>
                                </span>
                                <span>{{ oa.currency_conversion_rate}}</span>
                                <span class="base_currency_txt"></span>
                            </div>
						</div>
						<div class="clearfix"></div>
						<div class="form-group" style="margin-bottom: 20px;">
						    <label style="width: 202px;">Party Ref.P.O No</label>
							{{oa.po_no}}
						</div>
						<div class="form-group" style="margin-bottom: 20px;">
						    <label style="width: 202px;">Party Ref.P.O Date</label>
							{{oa.po_date}}
						    <input type="text" class="form-control custom_datepicker" placeholder="Select Date" id="quotation_date" readonly="readonly" style="width: 200px;">
						    <i class="glyphicon glyphicon-calendar custom-calender-icon" style="left: 205px; top: 35px;"></i>
						</div>
						<div class="col-sm-12" style="padding: 0; margin-bottom: 15px;">
							{{ oa.document }}
							<input type="hidden" class="file_upload_json" name="oa_attachment_upload_json" value="">
							<div class="btn-browse-container" style="width: 400px;">
								{% if oa_attachment != None and oa_attachment != "" %}
									<div class="btn-browse active" data-tooltip="tooltip" >
										<input type="hidden" class="base64-file-value base64" data-filename="{{oa_attachment.name}}.{{oa_attachment.ext}}" data-extension="{{oa_attachment.ext}}" value="{{oa_attachment.file}}">
										<i class="fa fa-file" aria-hidden="true"></i>
		        			    		<span class="browsed-text" style="margin-left: 15px;" data-default="Upload Document">{{oa_attachment.name}}.{{oa_attachment.ext}}</span>
										<div id='loadingmessage2_ie' style="margin-left:180px;margin-top:-20px">
											<img src='{{ MEDIA_URL }}../images/ajax-loader.gif'/>
										</div>
									</div>
									{% else %}
										<div class="btn-browse " data-tooltip="tooltip" >
											<input type="hidden" class="base64-file-value base64" data-filename="">
											<i class="fa fa-file" aria-hidden="true"></i>
			        			    		<span class="browsed-text" style="margin-left: 15px;" data-default="Upload Document">Upload Document</span>
											<div id='loadingmessage2_ie' style="margin-left:180px;margin-top:-20px">
											<img src='{{ MEDIA_URL }}../images/ajax-loader.gif'/>
										</div>
										</div>
									{% endif %}
								<i class="fa fa-times-circle btn-browse-remove" onClick="removeBrowsedFile(this)" aria-hidden="true"></i>
							</div>
						</div>
					</div>
				</div>
				<div class="col-sm-4 remove-padding">
					<div class="form-inline" >
						<div hidden="hidden">
							{{ tax_list.empty_form.tax_code }}
							{{ tax_list.empty_form.enterprise_id }}
							{{ tax_list.empty_form.oa_id }}
						</div>
						<div class="form-group" style="margin-bottom: 18px;">
							<div class="taxable non-form-element" id="tax_choices" style="padding-left: 0;">
								<label style="width: 173px;">Taxes</label>
								{{ tax_list.management_form }}
								<select class="form-control chosen-select" name="select" id="oa_tax_list">
									{% for tax in load_tax %}
									<option value="{{ tax.0 }}">{{ tax.1 }}</option>
									{% endfor %}
								</select>
								<span class="btn btn-add-tax" id="add_oa_tax" data-tooltip="tooltip" title="Add tax" style="margin-top: 0px;">
									<i class="fa fa-angle-double-right" aria-hidden="true"></i>
								</span>
								<td><input type=hidden id="id_se_taxes"
														   value=""/></td>
							</div>
						</div>
						<div class="form-group" style="margin-bottom: 15px;">
						    <label for="delDueDate" style="width: 173px;">Delivery Due Date</label>
						    {{oa.delivery_due_date}}
						    {% if oa.prepared_on.value|date:'Y-m-d' == "" %}
								<input type="text" class="form-control custom_datepicker set-my-start-date" data-setDate = "{{ oa.prepared_on.value }}" placeholder="Select Date" id="del_dueDate" readonly="readonly" style="width: 200px;">
							{% else %}
								<input type="text" class="form-control custom_datepicker set-my-start-date" data-setDate = "{{ oa.prepared_on.value|date:'Y-m-d' }}" placeholder="Select Date" id="del_dueDate" readonly="readonly" style="width: 200px;">
							{% endif %}
						    <i class="glyphicon glyphicon-calendar custom-calender-icon" style="margin-left: 177px;"></i>
						</div>

						<div class="form-group">
						    <label for="paymentTerms" style="width: 173px;">Payment Terms</label>
						    {{oa.payment_terms}}
						</div>
					</div>
				</div>
				<div class="col-sm-3">
					<div class="col-sm-12 ">
						<div id="oa_taxes" style="padding-left: 0;">
							<div style="padding-right: 0;">
								<div class="col-sm-12 po_taxes_table">
									<table id="oa_tax_table" border="0" width="100%" class="taxable ">
										<tbody>
											<tr id="oa_tax-__dummy__">
												<td hidden="hidden">
													{{ tax_list.empty_form.DELETE }}
													{{ tax_list.empty_form.enterprise_id }}
													{{ tax_list.empty_form.oa_id }}
													{{ tax_list.empty_form.tax_code}}
													<a href="#"
													   id="id_{{ tax_list.empty_form.prefix }}-deleteOATax"
													   onclick="javascript:deleteOATax('{{ tax_list.empty_form.prefix }}')">
														<img src="/site_media/images/deleteButton.png"
															 title="Delete" alt="Delete"
															 align="center"/></a>
												</td>
											</tr>
											{% for tax_form in tax_list.initial_forms %}
											<tr id="{{ tax_form.prefix }}">
												<td hidden="hidden">
													{{ tax_form.DELETE }}
													{{ tax_form.enterprise_id }}
													{{ tax_form.oa_id }}
													{{ tax_form.tax_code }}
													<a href="#"
													   id="id_{{ tax_form.prefix }}-deleteOATax"
													   onclick="javascript:deleteOATax('{{ tax_form.prefix }}')">
														<img src="/site_media/images/deleteButton.png"
															 title="Delete" alt="Delete"
															 align="center"/></a>
												</td>
											</tr>
											{% endfor %}
											<tr hidden="hidden">
												<td><input type=hidden id="id_edit_data"
														   value="{{oa_edit_id}}"/></td>
												<td><input type=hidden id="id_promote_se_data"
												           value="{{promote_oa_se_id}}"/></td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
							<div class="clearfix"></div>
						</div>
					</div>
				</div>
			</div>

			<div class="row for-primary-ent">
				<div class="col-sm-8">
					<label style="width: 202px;">Document Description</label>
					{{ oa.document_description }}
				</div>
			</div>

			<div class="clearfix"><br></div>

			<div class="row">
				<div class="col-sm-4">
					<div class="form-group" style="margin-bottom: 10px;">
					    <label for="id_oa-special_instructions" style="width: 200px;">Instructions</label>
						{{oa.special_instructions}}
					</div>
				</div>
				<div class="col-sm-4">
					<div class="form-group" style="margin-bottom: 10px;">
					    <label for="id_oa-remarks" style="width: 200px;">Remarks</label>
					    <div class="remarks_count_link remarks_count disabled" onclick="remarks_history();">
							<span class="remarks_counter">No</span><span> remarks</span>
						</div>
						{{oa.remarks}}
					</div>
				</div>
				<div class="col-sm-4" style="margin-top: 62px; text-align: right;">
					{% if access_level.edit %}
						{% if status == 0 or status == None %}
							<input type="button" class="btn btn-save" id="cmd_save_draft" value="Save"/>
						{% endif %}	
						{% if logged_in_user.is_super %}
							{% if status != None and status != 0 %}
								<input type="button" class="btn btn-save" id="amend_oa" value="Amend" />
							{% endif %}
						{% endif %}	
					{% endif %}
				</div>
			</div>
		</form>
	</div>
</div>

<div id="seMaterialModal" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg" style="width: 60%;">
	    <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title"><span class="se_no_heading"> View SE Materials </span></h4>
            </div>
            <div class="modal-body" style="overflow-x: auto;">
                <table id="seMaterialTable" class="table table-bordered custom-table custom-table-large tableWithText table-striped">
					<thead>
						<tr align="center">
							<th>Action</th>
							<th class="hide">S.No</th>
							<th class="hide">Drawing No/Material</th>
							<th>Material Name</th>
							<th>HSN/SAC</th>
							<th>Remarks</th>
							<th>Qty</th>
							<th>Price/Unit</th>
							<th>Disc.(%)</th>
							<th style="min-width: 50px;">Unit</th>
							<th>Amount</th>
						</tr>
					</thead>
					<tbody></tbody>
				</table>
            </div>
			<div class="modal-footer">
				<span class="pull-left nonStock-text">  <span class=" nonStock-text non_stock-flag"></span> - NON-STOCKABLE</span>
				<span class="pull-left nonStock-text">  <span class=" nonStock-text service-item-flag"></span> - SERVICE</span>
            	<button  id="add_se_items" type="button" class="btn btn-save" onclick="validateAndAddSEItems();">Add</button>
            	<button  id="cancel_se_items" data-dismiss="modal" type="button" class="btn btn-cancel" >Cancel</button>
            </div>
			<input type="hidden" value="" id="se_taxes"/>
            <div id="SEButton"></div>
        </div>
    </div>
</div>

{% include "attachment_popup.html" %}
{% include "masters/add_party_modal.html" %}
{% include "masters/add_material_modal.html" %}
{% include "modal-window/iwo_modal.html" %}

<script type="text/javascript">
$(document).ready(function(){
	OASuperEditInit();
	displayRemarksHistory('remarks_list', $("#remarks_list_json").val(), "remarks_count");
	oaPrevNextPaging();
	$('#loadingmessage2_ie').hide();
	if($(".header_current_page").text().trim() == "" || $(".header_current_page").text().indexOf("New") >=0 || $(".header_current_page").text().indexOf("PF") >=0) {
		$(".super_user_icon").addClass('hide');
		$(".super_edit_party, .super_edit_type").addClass('hide');
		$(".currency").removeClass('hide');
	}
	else{
		$(".super_user_icon, .super_edit_party, .super_edit_type, .currency").removeClass('hide');
	}
	$(".new-tour").addClass('hide');

	if($("#oa_particulars_table tbody.item-for-goods tr").length >= 1){
        $(".item-for-goods").removeClass('hide');
	}
	if($("#oa_particulars_table tbody.item-for-service tr").length >= 1){
		$(".item-for-service").removeClass('hide');
	}
});
$(window).load(function() {
 	$('.nav-pills li').removeClass('active');
    $("#li_oa").addClass('active');
    $(".slide_container_part").removeClass('selected');
    $("#menu_sales").addClass('selected');

});

$("#cancel_se_items").click(function(){
    $("#id_oa-se_no_display").val($('#id_oa-se_no').val()).trigger("chosen:updated");
});
</script>
{% endblock %}
