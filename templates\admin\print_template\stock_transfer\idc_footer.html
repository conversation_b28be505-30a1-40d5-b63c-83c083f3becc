<!DOCTYPE html>
<style type="text/css">
	.col-sm-12 {
		width: 100%;
	}
	.col-sm-4 {
		width: 33.33333333%;
		float: left;
	}
	.text-center {
		text-align: center;
	}
	.text-right {
		text-align: right;
	}

	@font-face {
	        font-family: 'pdf_Roboto';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/roboto.regular.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Times New Roman';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/times.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Helvetica';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/roboto.regular.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Ubuntu';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/Ubuntu-Regular.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Comic Sans';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/comicsansms3.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_DejaVu Sans';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/DejaVuSans.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Courier New';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/cour.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Verdana';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/verdana.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Open Sans';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/open-sans-Regular.ttf) format('truetype');
	}
</style>
<script>
	function subst1() {
		var vars={};
		var x=document.location.search.substring(1).split('&');
		for (var i in x) {
			var z=x[i].split('=',2);
			vars[z[0]] = unescape(z[1]);
		}
		var x=['frompage','topage','page','webpage','section','subsection','subsubsection'];
		for (var i in x) {
			var y = document.getElementsByClassName(x[i]);
			for (var j=0; j<y.length; ++j)
				y[j].textContent = vars[x[i]];

			if(document.getElementById("footer_banner_images_first")){
				if(vars['page'] == 1){
				   document.getElementById("footer_banner_images_first").style.display = 'block';
				}
				else {
				   document.getElementById("footer_banner_images_first").style.display = 'none';
				}
			}
			else {
				if(vars['page'] == vars['topage']){
				   document.getElementById("footer_banner_images_last").style.display = 'block';
				}
				else {
				   document.getElementById("footer_banner_images_last").style.display = 'none';
				}
			}
		}
	}
</script>
<body onload="subst1()">
	<table style="width: 100%; border: none;">
		<tr>
			<td class="text-center" style="font-size: {{ summary_res.font_size }}pt; font-family: pdf_{{general_res.base_font}};width: 33%;vertical-align: bottom;">
				{% if approved_by %}
					{% if approved_by %} {{ approved_by }} {% endif %}
					<br>
					<span class="pdf_prepared_sign"><b class="pdf_prepared_sign_txt">Approved By</b></span>
				{% endif %}
			</td>
			<td class="text-center" style="font-size: {{ summary_res.font_size }}pt; font-family: pdf_{{general_res.base_font}};width: 33%;vertical-align: bottom;">
				{% if prepared_by %}
					{% if prepared_by %} {{ prepared_by }} {% endif %}
					<br>
					<span class="pdf_prepared_sign"><b class="pdf_prepared_sign_txt">Prepared By</b></span>
				{% endif %}
			</td>
			{% if received_by %}
			<td class="text-left" style="font-size: {{ summary_res.font_size }}pt; font-family: pdf_{{general_res.base_font}}; width: 33%;vertical-align: bottom;">
				{% if is_receiver_sign_enabled %}
					<b>Receiver Sign : {{received_by}}<br/>
					Date: {{source.received_on}} </b>
				{% endif %}
			</td>
			{%endif%}
		</tr>
	</table>
	<div style="clear: both;"></div>
	<div style="width: 100%; border-bottom: solid 1px #666666;"></div>

	<div class="col-sm-12 footer_notes remove-padding" style="font-size: {{ misc_res.font_size }}pt; font-family: pdf_{{general_res.base_font}};">SChnell</div>
</body>
