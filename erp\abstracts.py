"""
Abstract Classes with basic implementation of certain repeated functionalities
"""
from datetime import datetime, date
from decimal import Decimal, ROUND_HALF_UP

from erp import logger

__author__ = 'ka<PERSON>vanan'


class AutoSerialize(object):
	"""Mixin for retrieving public fields of model in json-compatible format"""
	__public__ = None
	
	def getPublicValue(self, exclude=(), extra=()):
		"""Returns model's PUBLIC data for jsonify"""
		data = {}
		keys = self._sa_instance_state.attrs.items()
		public = self.__public__ + extra if self.__public__ else extra
		for k, field in keys:
			if public and k not in public:
				continue
			if k in exclude:
				continue
			value = self._serialize(field.value)
			if value:
				data[k] = value
		return data
	
	def _serialize(self, value):
		try:
			if type(value) in (datetime, date):
				ret = value.isoformat()
			elif type(value) is Decimal:
				ret = float(value)
			elif hasattr(value, '__iter__'):
				ret = []
				for v in value:
					ret.append(self._serialize(v))
			elif AutoSerialize in value.__class__.__bases__:
				ret = value.getPublicValue()
			else:
				ret = value
			return ret
		except ValueError as e:
			logger.debug("Value %s " % value)
			logger.exception(e)
			return None


class TaxableItem:
	"""
	Abstract Class/Interface that abstracts the tax calculation behaviours of an Item-Particular. Not to be instantiated
	"""
	
	def getTaxesOfType(self, tax_type=""):
		"""
		Returns a list of taxes of the type mentioned associated with an Item
		:param tax_type:
		:return: array of <ItemTax>
		"""
		taxes = []
		for tax in self.taxes:
			if tax.tax.type.upper() == tax_type.upper():
				taxes.append(tax)
		return taxes if len(taxes) > 0 else [None]

	def getItemValue(self):
		"""
		get Item total value
		:return:
		"""
		item_value = 0
		if "quantity" in self.__dict__:
			item_value = Decimal("%0.2f" % (Decimal(self.quantity) * Decimal(self.rate)))
		if 'discount' in self.__dict__:
			item_value = Decimal("%0.2f" % (item_value * Decimal(100 - self.discount) / 100))
		return item_value
	
	def getTaxValueForType(self, tax_type=""):
		"""
		Calculates & returns tax_value for an item against the Taxes of the type mentioned
		:return: a Decimal
		"""
		value = 0
		item_value = self.rate
		if "quantity" in self.__dict__:
			item_value = Decimal("%0.2f" % (Decimal(self.quantity) * Decimal(self.rate)))
		if 'discount' in self.__dict__:
			item_value = Decimal("%0.2f" % (item_value * Decimal(100 - self.discount) / 100))
		for tax in self.taxes:
			value += Decimal("%0.2f" % Decimal((item_value * Decimal(
				tax.tax.net_rate / 100))).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)) if tax.tax.type.upper() == tax_type.upper() else 0
		return value
	
	def getTaxValue(self):
		"""
		Calculates & returns total value of all taxes associated with an item

		:return:
		"""
		tax_value = 0
		item_value = self.rate
		if "quantity" in self.__dict__:
			item_value = Decimal("%0.2f" % (Decimal(self.quantity) * Decimal(self.rate)))
		if 'discount' in self.__dict__:
			item_value = Decimal("%0.2f" % (item_value * Decimal(100 - self.discount) / 100))
		for tax in self.taxes:
			tax_value += Decimal(Decimal(item_value * Decimal(tax.tax.net_rate / 100)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP))
		return tax_value


class Taxable(object):
	"""
	Abstract Class/Interface that Implements general tax and price calculation methods for an ERP flow-object that will
	have multiple Materials/Items associated with prices, taxes and sometimes discounts
	"""
	items = []
	non_stock_items = []
	
	def getTaxes(self):
		"""
		Object-Tax association are always rendered as per the tax_order. Yet the tax_order does not group them into simple
		and compound taxes, which is necessary for appropriately applying the compound taxes.
		Such short-coming is rectified by this method, as it groups the already ordered Object-Tax association as
		Simple-taxes, followed by Compound taxes, so that all compound rates are properly compounded.

		:return: sorted Object-Tax association list grouped based on the tax-type.
		"""
		sorted_taxes = []
		compound_taxes = []
		for tax in self.taxes:
			# Segregates Simple and compound taxes separately
			compound_taxes.append(tax) if tax.tax.is_compound else sorted_taxes.append(tax)
		# Appends the compound-taxes at the end, so that all simple taxes are compounded.
		sorted_taxes.extend(compound_taxes)
		return sorted_taxes
	
	def getTotalMaterialValue(self):
		"""
		Calculates the Gross Total Price of all the Items associated with the object, without taxes

		:return: a Decimal value
		"""
		material_value = 0
		for material in self.items:
			material_value += Decimal(Decimal(round(material.quantity * material.rate * (
					100 - material.discount) / 100, 2)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP))
		for material in self.non_stock_items:
			material_value += Decimal(Decimal(round(material.quantity * material.rate * (
					100 - material.discount) / 100, 2)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP))
		return Decimal(material_value)
	
	def getTotalTaxableValue(self):
		return self.getTotalMaterialValue()

	def getCommonTaxValueByType(self, item_value=0, tax_type=""):
		"""

		:param item_value:
		:param tax_type:
		:return:
		"""
		value = 0
		for tax in self.taxes:
			if tax_type != "":
				value += Decimal("%0.2f" % Decimal((item_value * Decimal(
					tax.tax.net_rate / 100))).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)) if tax.tax.type.upper() == tax_type.upper() else 0
			elif tax.tax.type.upper() not in ['CGST', 'SGST', 'IGST']:
				value += Decimal("%0.2f" % Decimal((item_value * Decimal(tax.tax.net_rate / 100))).quantize(Decimal('.01'), rounding=ROUND_HALF_UP))
		return value
	
	def getNetMaterialTaxForType(self, tax_type=""):
		"""
		Calculates Net Tax amount applied across all Items for a particular Tax-type (say, Duty, GST, VAT & the likes)

		:param tax_type:
		:return: a Decimal value
		"""
		net_value = 0
		item_total = 0
		for item in self.items:
			net_value += item.getTaxValueForType(tax_type=tax_type)
			item_total += item.getItemValue()
		for ns_item in self.non_stock_items:
			net_value += ns_item.getTaxValueForType(tax_type=tax_type)
			item_total += ns_item.getItemValue()
		if net_value == 0:
			net_value = self.getCommonTaxValueByType(item_value=item_total, tax_type=tax_type)
		return Decimal(round(net_value, 2))
	
	def getTaxValues(self, discounts_applied=True):
		"""
		Creates & Returns a Map of tax_codes & net value against each tax_code

		:param discounts_applied:
		:return: a dict [tax_code: tax_value]
		"""
		sorted_taxes = self.getTaxes()
		tax_values = {}
		for item in self.items:
			self._extractMaterialTaxValues(
				item=item, sorted_taxes=sorted_taxes, tax_values=tax_values, discount_applied=discounts_applied)
		for ns_item in self.non_stock_items:
			self._extractMaterialTaxValues(
				item=ns_item, sorted_taxes=sorted_taxes, tax_values=tax_values, discount_applied=discounts_applied)
		return tax_values
	
	@staticmethod
	def _extractMaterialTaxValues(item=None, sorted_taxes=[], tax_values={}, discount_applied=True):
		"""
		Extracts Net Tax-value for a material and places them in a dict against tax_code

		:param item:
		:param sorted_taxes:
		:param tax_values: the dict that holds the Tax_code, Tax_Value as key:Value pairs
		:param discount_applied: Boolean to say if item-discounts are to be considered or not
		:return: None
		"""
		item_quantity = 1
		material_tax = Decimal("%0.2f" % item.getTaxValue())
		is_tax_positive = material_tax >= 0
		if 'is_credit' in item.__dict__:
			is_tax_positive = is_tax_positive and item.is_credit
		if 'quantity' in item.__dict__:
			item_quantity = item.quantity
		logger.debug("Item Tax: %s |Is Credit: %s" % (material_tax, is_tax_positive))
		for inv_tax in sorted_taxes:
			assess_rate = (Decimal((100 - item.discount) if (
					(
								100 - item.discount) > inv_tax.tax.assess_rate) else inv_tax.tax.assess_rate) / 100) if discount_applied else 1
			if inv_tax.tax.is_compound:
				tax_value = Decimal("%0.2f" % Decimal(round(((Decimal(material_tax) + (Decimal(item.rate) * Decimal(
					item_quantity) * assess_rate)) * (1 if is_tax_positive else -1) * Decimal(
					inv_tax.tax.net_rate / 100)), 2)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP))
			else:
				tax_value = Decimal("%0.2f" % Decimal(round(((Decimal(item.rate) * Decimal(item_quantity) * assess_rate * Decimal(
					inv_tax.tax.net_rate / 100) * (1 if is_tax_positive else -1))), 2)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP))
			if inv_tax.tax.code in tax_values:
				tax_values[inv_tax.tax.code] += tax_value
			else:
				tax_values[inv_tax.tax.code] = tax_value
			material_tax += Decimal("%0.2f" % tax_value)
			logger.debug("Tax %s of rate - %s applied on Item %s of price %s is %s" % (
				inv_tax.tax.code, inv_tax.tax.net_rate, item, item.rate * item_quantity, material_tax))
		# Including Material Taxes as well
		for item_tax in item.taxes:
			item_value = Decimal("%0.2f" % Decimal((float(item.rate) * float(item_quantity) * float(
				100 - (item.discount if discount_applied else 0)) / 100)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP))
			if item_tax.tax.code in tax_values:
				tax_values[item_tax.tax.code] += Decimal(
					"%0.2f" % Decimal((item_value * Decimal(item_tax.tax.net_rate / 100)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP))) * (1 if is_tax_positive else -1)
			else:
				tax_values[item_tax.tax.code] = Decimal(
					"%0.2f" % Decimal((item_value * Decimal(item_tax.tax.net_rate / 100)).quantize(Decimal('.01'), rounding=ROUND_HALF_UP))) * (1 if is_tax_positive else -1)
			logger.debug("Tax Value for Tax: %s is %s" % (item_tax.tax, Decimal(
				"%0.2f" % (item_value * Decimal(item_tax.tax.net_rate / 100))) * (1 if is_tax_positive else -1)))
	
	def getConsolidatedTaxList(self):
		"""
		:return: a set that contains all taxes associated at both Document & Item level
		"""
		consolidated_tax_list = set()
		for tax in self.getTaxes():
			consolidated_tax_list.add(tax.tax)
		for item in self.items:
			for tax in item.taxes:
				consolidated_tax_list.add(tax.tax)
		for item in self.non_stock_items:
			for tax in item.taxes:
				consolidated_tax_list.add(tax.tax)
		return consolidated_tax_list


class Chargeable(Taxable):
	charges = []
	
	def getTotalTaxableValue(self):
		total_value = super(Chargeable, self).getTotalTaxableValue()
		total_value += self.getTotalChargeValue()
		return total_value
	
	def getTotalChargeValue(self):
		total_value = 0
		for charge in self.charges:
			total_value += Decimal(round(charge.rate * (100 - charge.discount) / 100, 2))
		return total_value

	def getTotalChargeValueByType(self, charge_type):
		total_value = 0
		for charge in self.charges:
			if charge.item_name == charge_type:
				total_value += Decimal(round(charge.rate * (100 - charge.discount) / 100, 2))
		return total_value
	
	def getNetMaterialTaxForType(self, tax_type=""):
		net_value = super(Chargeable, self).getNetMaterialTaxForType(tax_type=tax_type)
		for charge in self.charges:
			net_value += charge.getTaxValueForType(tax_type=tax_type)
		return Decimal(round(net_value, 2))
	
	def getTaxValues(self, discounts_applied=True):
		tax_values = super(Chargeable, self).getTaxValues(discounts_applied=discounts_applied)
		for charge in self.charges:
			self._extractMaterialTaxValues(
				item=charge, sorted_taxes=self.getTaxes(), tax_values=tax_values, discount_applied=discounts_applied)
		return tax_values
	
	def getConsolidatedTaxList(self):
		consolidated_tax_list = super(Chargeable, self).getConsolidatedTaxList()
		for item in self.charges:
			for tax in item.taxes:
				consolidated_tax_list.add(tax.tax)
		return consolidated_tax_list


class Remarkable:
	"""
	Remarks standard across the application
	"""
	
	def __init__(self, *args, **kwargs):
		self.remarks = kwargs['remarks'] if 'remarks' in kwargs else None
	
	def updateRemarks(self, remarks=None, user=None):
		"""

		:param remarks:
		:param user:
		:return:
		"""
		try:
			if not remarks or remarks == "":
				return
			if self.remarks is None:
				self.remarks = []
			# JSON field should be replace by another object to update in db
			self.remarks = [remark for remark in self.remarks]
			self.remarks.append(dict(
				date=datetime.today().strftime("%Y-%m-%d %H:%M:%S"),
				remarks=remarks.replace('\n', '<BR/>'), by="%s" % user))
		except Exception as e:
			logger.error("Updating remarks failed %s" % e.message)
