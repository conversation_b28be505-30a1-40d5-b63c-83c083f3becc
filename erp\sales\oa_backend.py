import datetime
import uuid
from collections import namedtuple
from decimal import Decimal

from sqlalchemy import func

from erp import helper, APPROVED_ORDER_ACKNOWLEDGEMENT, IS_FAULTY_TRUE
from erp.admin.backend import User<PERSON>O
from erp.dao import DataAccessObject, executeQuery
from erp.forms import OAForm
from erp.formsets import OAParticularsFormset, TagFormset, OATaxFormset
from erp.helper import getUser
from erp.models import OA, OAParticulars, OATag, InvoiceMaterial, \
	Invoice, User, OATax, SalesEstimate, Party
from erp.sales import logger, SE_EXCLUDE_FIELD_LIST, SE_PARTICULAR_EXCLUDE_FIELD_LIST, SE_TAX_EXCLUDE_FIELD_LIST,\
	OA_EXCLUDE_FIELD_LIST, OA_PARTICULARS_EXCLUDE_FIELD_LIST
from erp.sales.backend import InvoiceService
from erp.sales.views import getInvoicedOAQty
from erp.tags import extractEntityTagMapsFromFormset, generateTagFormset
from settings import TEMP_DIR, SQLASession
from util.api_util import response_code
from util.ftp_helper import FTPUtil
from util.helper import constructFormInitializer, writeFile, copyFormToEntity, copyDictToDict
from util.helper import getFinancialYear
from erp.masters.backend import createPartyLedgers

OA_FORM_PREFIX = 'oa'
OA_PARTICULARS_PREFIX = 'oa_particular'
OA_NON_STOCK_PARTICULARS_PREFIX = 'oa_non_stock_particular'
OA_TAG_PREFIX = 'tag'
OA_TAX_PREFIX = 'oa_tax'


class OrderAcknowledgementVO(object):
	"""
	This Class holds both OA Form and OA Form Set
	"""

	def __init__(
			self, oa_form=OAForm(prefix=OA_FORM_PREFIX),
			oa_particulars_formset=OAParticularsFormset(prefix=OA_PARTICULARS_PREFIX),
			oa_tax_formset=OATaxFormset(prefix=OA_TAX_PREFIX),
			oa_tag_formset=TagFormset(prefix=OA_TAG_PREFIX), remarks_list=[]):
		self.oa_form = oa_form
		self.oa_particulars_formset = oa_particulars_formset
		self.oa_tax_formset = oa_tax_formset
		self.oa_tag_formset = oa_tag_formset
		self.remarks_list = remarks_list

	def is_valid(self):
		try:
			logger.info(
				"OA - Valid Header: %s, Valid Particulars: %s,Valid Tax: %s" % (
					self.oa_form.is_valid(), self.oa_particulars_formset.is_valid(),
					self.oa_tax_formset.is_valid()))
			logger.info("OA Form Errors: \n%s\n%s\n%s\n%s" % (
				self.oa_form.errors, self.oa_particulars_formset.errors,
				self.oa_tag_formset.errors, self.oa_tax_formset.errors))
		except:
			logger.exception("The Form Set Validation Error...")
		return self.oa_form.is_valid() and self.oa_particulars_formset.is_valid()

	def __repr__(self):
		return 'OA Form : %s\n OA particulars Formset : %s\n OA Tag Formset :%s \n OA Tax Formset :%s ' % (
			self.oa_form, self.oa_particulars_formset, self.oa_tag_formset, self.oa_tax_formset)


class OrderAcknowledgementDAO(DataAccessObject):
	"""
	Class that handles all the OA module related DB activities - like fetching, save and deleting persistent objects
	"""

	def getOA(self, oa_id=None):
		oa_query = self.db_session.query(OA).filter(OA.id == oa_id)
		return oa_query.first()

	def getLatestOACode(self, financial_year=None, enterprise_id=None):
		"""
		Generate the new OA code, combined with the financial year.
		As per requirement the numeric part of code is to be recycled every year.
		:param financial_year:
		:param enterprise_id:
		:return:
		"""
		financial_year = financial_year if financial_year else datetime.datetime.now()
		latest_oa_id = len(self.db_session.query(OA.id).filter(
			OA.financial_year == financial_year, OA.enterprise_id == enterprise_id).all())
		logger.info('Latest OA Code: %s' % (
			1 if not ('%s' % latest_oa_id).isdigit() else (int('%s' % latest_oa_id) + 1)))
		return 1 if not ('%s' % latest_oa_id).isdigit() else (int('%s' % latest_oa_id) + 1)

	def getOAParticulars(self, oa_id, item_id, enterprise_id, make_id, is_faulty):
		return self.db_session.query(OAParticulars).filter(
			OAParticulars.oa_id == oa_id, OAParticulars.item_id == item_id,
			OAParticulars.make_id == make_id, OAParticulars.is_faulty == is_faulty,
			OAParticulars.enterprise_id == enterprise_id).first()

	def getOATax(self, oa_id, tax_code, enterprise_id):
		return self.db_session.query(OATax).filter(
			OATax.oa_id == oa_id, OATax.tax_code == tax_code,
			OATax.enterprise_id == enterprise_id).first()

	def getInvoiceId(self, enterprise_id=None, oa_id=None):
		"""

		:return:
		"""
		try:
			invoices = self.db_session.query(Invoice).join(Invoice.items).filter(
				InvoiceMaterial.oa_no == oa_id, InvoiceMaterial.enterprise_id == enterprise_id,
				Invoice.status >= Invoice.STATUS_APPROVED).group_by(Invoice.id).all()
			invoice_codes = [invoice.getCode() for invoice in invoices]
			if invoice_codes:
				return invoice_codes
		except:
			raise


class OrderAcknowledgementService:
	def __init__(self):
		"""

		"""
		self.oa_dao = OrderAcknowledgementDAO()

	def constructOrderAcknowledgementVo(self, oa=OA()):
		"""
		Converts the DB persistent entity into a Value Object presentable to the UI layer.

		:param oa:
		:return:
		"""

		if oa.remarks:
			remarks_list = oa.remarks
			for remark in remarks_list:
				remark['date'] = datetime.datetime.strptime(remark['date'], '%Y-%m-%d %H:%M:%S').strftime("%b %d, %Y")
		else:
			remarks_list = []

		return OrderAcknowledgementVO(
			self._generateOAForm(oa=oa, prefix=OA_FORM_PREFIX),
			self._genereateOAParticulrasFormset(oa.items),
			self._generateOATaxFormset(oa.taxes),
			generateTagFormset(tags=oa.tags, prefix='tag'), remarks_list=remarks_list)

	def approveOa(self, enterprise_id=None, oa_id=None, user_id=None, remarks=None, project_code=None, primary_enterprise_id=None):
		"""

		:param enterprise_id:
		:param oa_id:
		:param user_id:
		:param remarks:
		:return:
		"""
		user = getUser(enterprise_id=enterprise_id, user_id=user_id)
		db_session = SQLASession()
		db_session.begin(subtransactions=True)
		try:
			oa_to_approve = db_session.query(OA).filter(OA.id == oa_id).first()
			if oa_to_approve.oa_no and oa_to_approve.oa_no != '0':
				if oa_to_approve.status == OA.STATUS_APPROVED:
					if project_code:
						oa_to_approve.project_code = project_code
						db_session.add(oa_to_approve)
						db_session.commit()
					else:
						db_session.rollback()
				if oa_to_approve.status == OA.STATUS_CANCELLED:
					oa_to_approve.status = OA.STATUS_APPROVED
					db_session.add(oa_to_approve)
					db_session.commit()
				response = response_code.success()
				response['custom_message'] = "OA has been already Approved - OA No: %s" % oa_to_approve.getCode()
				response['code'] = oa_to_approve.getCode()
				return response
			approved_date = datetime.datetime.now()
			current_fy = getFinancialYear(for_date=approved_date, fy_start_day=oa_to_approve.enterprise.fy_start_day)
			latest_oa_no = db_session.query(func.max(OA.oa_no)).filter(
				OA.financial_year == current_fy, OA.enterprise_id == oa_to_approve.enterprise.id,
				OA.id != '0', OA.type == oa_to_approve.type).first()
			logger.info('Recent OA No & Type: %s' % latest_oa_no)
			new_oa_no = 0
			if latest_oa_no and latest_oa_no[0] is not None:
				new_oa_no = int(latest_oa_no[0])
			new_oa_no += 1
			oa_to_approve.oa_no = new_oa_no
			logger.info("The Status of OA :%s" % OA.STATUS_APPROVED)
			oa_to_approve.status = OA.STATUS_APPROVED
			oa_to_approve.approved_on = approved_date.strftime('%Y-%m-%d %H:%M:%S')
			oa_to_approve.financial_year = current_fy
			oa_to_approve.approved_by = user_id
			oa_to_approve.last_modified_by = user_id
			oa_to_approve.enterprise_id = enterprise_id
			logger.info("Project Code:%s" % project_code)
			if project_code:
				oa_to_approve.project_code = project_code
				logger.info("In If Project Code:%s" % project_code)
			if remarks and remarks.strip() != "":
				oa_to_approve.updateRemarks(remarks="Approved Remarks: %s" % remarks, user=user)
			db_session.add(oa_to_approve)
			db_session.commit()
			### Create Internal OA
			internal_oa = InternalOrderAcknowledgementService()
			internal_oa.createInternalOA(enterprise_id=enterprise_id, user_id=user_id, parent_oa_id=oa_id, primary_enterprise_id=primary_enterprise_id)
			logger.debug('OA in orm session: %s' % db_session.dirty)
			InvoiceService().notifyOAapproveCount(
				enterprise_id=enterprise_id, sender_id=user_id, oa=oa_to_approve,
				code=oa_to_approve.getCode(), type=oa_to_approve.type)
			response = response_code.success()
			response['code'] = oa_to_approve.getCode()
			response['id'] = oa_id
		except Exception as e:
			db_session.rollback()
			raise e
		return response

	def saveOaFromJson(self, enterprise_id=None, user_id=None, oa_dict=None):
		"""
		:param enterprise_id:
		:param user_id:
		:param oa_dict:
		:return:
		"""
		self.oa_dao.db_session.begin(subtransactions=True)
		try:
			oa = self.oa_dao.getOA(oa_id=oa_dict["oa_id"] if "oa_id" in oa_dict else None)
			oa_particulars = []
			if not oa:
				oa = OA(enterprise_id=enterprise_id, prepared_by=user_id)
			copyDictToDict(source=oa_dict, destination=oa.__dict__, exclude_keys=OA_EXCLUDE_FIELD_LIST)
			user = self.oa_dao.db_session.query(User).filter(User.id == user_id).first()
			oa.updateRemarks(remarks=oa_dict["remarks"], user=user)
			for item in oa_dict["materials"]:
				if item['mat_type'] == 0:
					oa_item = OAParticulars(enterprise_id=enterprise_id)
					copyDictToDict(source=item, destination=oa_item.__dict__, exclude_keys=OA_PARTICULARS_EXCLUDE_FIELD_LIST)
					if oa_item.make_id == "":
						oa_item.make_id = 1
					oa_particulars.append(oa_item)

			prepared_on = datetime.datetime.now()
			oa.approved_on = prepared_on
			oa.last_modified_on = prepared_on
			oa.prepared_on = prepared_on
			oa.approved_by = user_id
			oa.prepared_by = user_id
			oa.delivery_due_date = prepared_on.strftime('%Y-%m-%d 00:00:00')
			oa.type = 'Job'
			oa.last_modified_by = user_id
			oa.items = oa_particulars
			self.oa_dao.db_session.add(oa)
			self.oa_dao.db_session.flush([oa])
			self.oa_dao.db_session.refresh(oa)
			self.oa_dao.db_session.commit()
			return oa.id
		except Exception as e:
			self.oa_dao.db_session.rollback()
			raise e

	def saveOA(self, oa_vo=None, user_id=None, oa_attachment_json=None):
		"""

		:param oa_vo:
		:param user_id:
		:param oa_attachment_json:
		:return:
		"""
		oa_id = None
		db_session = self.oa_dao.db_session
		db_session.begin(subtransactions=True)
		try:
			if oa_vo.is_valid():
				oa_id = oa_vo.oa_form.cleaned_data['id']
				entity = self.oa_dao.getOA(oa_id)
				entity = entity if entity else OA()
				[db_session.delete(tag) for tag in entity.tags]
				oa_to_be_saved = self._copyOAVOtoEntity(oa_vo, entity)
				oa_to_be_saved.document = oa_attachment_json if oa_attachment_json != None else oa_to_be_saved.document
				logger.debug('OA to be saved: %s' % oa_to_be_saved)
				modifying_user = getUser(enterprise_id=entity.enterprise_id, user_id=user_id)
				oa_to_be_saved.updateRemarks(remarks=oa_vo.oa_form.cleaned_data['remarks'], user=modifying_user)
				oa_to_be_saved.prepared_by = user_id
				oa_to_be_saved.last_modified_by = user_id
				modified_date = datetime.datetime.now()
				# TODO capture is amend / normal save
				if oa_to_be_saved.status and oa_to_be_saved.status < 0:
					oa_to_be_saved.status = APPROVED_ORDER_ACKNOWLEDGEMENT
				oa_to_be_saved.last_modified_on = modified_date.strftime('%Y-%m-%d %H:%M:%S')
				db_session.add(oa_to_be_saved)
				db_session.commit()
				db_session.refresh(oa_to_be_saved)
				oa_id = oa_to_be_saved.id
				# GCS save enabled so below is no needed
				# self.__uploadDocument(oa=oa_to_be_saved, oa_dict=oa_vo.oa_form.cleaned_data)
			else:
				db_session.rollback()

		except Exception as e:
			db_session.rollback()
			logger.exception("Error in Save OA - %s" % e.message)
			pass
		return oa_vo, oa_id

	def _copyOAVOtoEntity(self, oa_vo, entity):
		"""
		Method converts VO into persist-able data entity.
		Caution: Don't use this method unless the data is to be persisted immediately, as it might lead to inconsistent
			OA numbers
		:param oa_vo:
		:param entity:
		:return:
		"""
		try:
			logger.info("OA Id fetched from OA_VO: %s" % oa_vo.oa_form.cleaned_data['id'])
			entity = self.__copyOAFormToEntity(oa_vo.oa_form, entity)
			entity.items = self.__extractOAParticularsFromFormset(
				oa_vo.oa_particulars_formset, oa_vo.oa_form.cleaned_data['id'])
			entity.taxes = self.__extractOATaxFormset(
				oa_vo.oa_tax_formset, oa_vo.oa_form.cleaned_data['id'])
			entity.tags = extractEntityTagMapsFromFormset(
				tag_formset=oa_vo.oa_tag_formset, enterprise_id=entity.enterprise_id,
				map_class=OATag, db_session=self.oa_dao.db_session)
			return entity
		except:
			logger.exception("Error in Copy OA Vo to Entity")
		return entity

	def _generateOAForm(self, oa=OA(), prefix=OA_FORM_PREFIX):
		initializer = constructFormInitializer(entity=oa, exclude_field_keys=('remarks',))
		initializer[u'code'] = oa.getInternalCode()
		return OAForm(
			enterprise_id=oa.enterprise_id, initial=initializer, prefix=prefix)

	def _genereateOAParticulrasFormset(self, oa_items=()):
		initializer = []
		for oa_particular in oa_items:
			form_initializer = constructFormInitializer(oa_particular)
			make_name = helper.constructDifferentMakeName(oa_particular.item.makes_json)
			make = " [" + make_name + "]" if make_name else ""
			description = oa_particular.item.name
			if oa_particular.item.drawing_no:
				description += " - %s" % oa_particular.item.drawing_no
			form_initializer['item_name'] = "%s  %s %s " % (
				description, make, "[Faulty]" if oa_particular.is_faulty == IS_FAULTY_TRUE else "")
			form_initializer['unit_id'] = "%s" % oa_particular.item.unit.unit_name
			form_initializer['item_code'] = "%s" % oa_particular.item.drawing_no
			form_initializer['hsn_code'] = "%s" % oa_particular.hsn_code
			form_initializer['is_service'] = "%s" % oa_particular.item.is_service
			form_initializer['discount'] = "%s" % oa_particular.discount
			form_initializer['make_label'] = "%s" % oa_particular.make.__repr__() if oa_particular.make.label else "-NA-"
			form_initializer['project_id'] = "%s" % oa_particular.project_id
			form_initializer['internal_price'] = "%s" % oa_particular.internal_price
			form_initializer['internal_oa_id'] = "%s" % oa_particular.internal_oa_id
			inv_qty = getInvoicedOAQty(
					item_id=oa_particular.item_id, make_id=oa_particular.make_id, enterprise_id=oa_particular.enterprise_id,
					oa_id=oa_particular.oa_id, invoice_id=None)
			form_initializer['inv_quantity'] = inv_qty
			if oa_particular.alternate_unit_id:
				scale_factor = helper.getScaleFactor(
						enterprise_id=oa_particular.enterprise_id, item_id=oa_particular.item_id, alternate_unit_id=oa_particular.alternate_unit_id)
				if scale_factor:
					form_initializer['quantity'] = round(Decimal(oa_particular.quantity) / Decimal(scale_factor), 3)
					form_initializer['price'] = round(Decimal(oa_particular.price) * Decimal(scale_factor), 5)
					form_initializer['unit_id'] = oa_particular.alternate_unit.unit.unit_name
					form_initializer['inv_quantity'] = Decimal(inv_qty) / Decimal(scale_factor) if inv_qty != 0 else 0
					form_initializer['scale_factor'] = scale_factor if scale_factor != 0 else 0
			logger.debug("after resetting the item name: %s" % ' '.join(form_initializer).encode('utf-8'))
			initializer.append(form_initializer)
		oa_particulars_formset = OAParticularsFormset(initial=initializer, prefix=OA_PARTICULARS_PREFIX)
		return oa_particulars_formset

	def _generateOATaxFormset(self, oa_taxes=()):
		initializer = []
		logger.info("OA has %s oa_taxes associated" % len(oa_taxes))
		for tax in oa_taxes:
			form_initializer = constructFormInitializer(tax)
			initializer.append(form_initializer)
		return OATaxFormset(initial=initializer, prefix=OA_TAX_PREFIX)

	def __copyOAFormToEntity(self, oa_form, entity):
		try:
			if entity.id is None or entity.id == '':
				entity = copyFormToEntity(entity=entity, form=oa_form, exclude_field_list=('document', 'document_data', 'remarks'))
			else:
				entity.enterprise_id = oa_form.cleaned_data['enterprise_id']
				entity.po_date = oa_form.cleaned_data['po_date']
				entity.po_no = oa_form.cleaned_data['po_no']
				entity.party_id = oa_form.cleaned_data['party_id']
				entity.oa_no = oa_form.cleaned_data['oa_no']
				entity.grand_total = oa_form.cleaned_data['grand_total']
				entity.round_off = oa_form.cleaned_data['round_off']
				entity.currency_id = oa_form.cleaned_data['currency_id']
				entity.payment_terms = oa_form.cleaned_data['payment_terms']
				entity.special_instructions = oa_form.cleaned_data['special_instructions']
				entity.currency_conversion_rate = oa_form.cleaned_data['currency_conversion_rate']
				entity.type = oa_form.cleaned_data['type']
				entity.delivery_due_date = oa_form.cleaned_data['delivery_due_date']
				entity.se_id = oa_form.cleaned_data['se_id']
				entity.document_description = oa_form.cleaned_data['document_description']
				entity.project_code = oa_form.cleaned_data['project_code']
		except:
			logger.exception("Error in Copy Form to Entity Part")
		return entity

	def __extractOAParticularsFromFormset(self, oa_particulars_formset, oa_id):
		oa_items = []
		for oa_particular_form in oa_particulars_formset:
			quantity = oa_particular_form.cleaned_data['quantity']
			if not oa_particular_form.cleaned_data['DELETE'] and quantity > 0:
				item_id = oa_particular_form.cleaned_data['item_id']
				enterprise_id = oa_particular_form.cleaned_data['enterprise_id']
				make_id = oa_particular_form.cleaned_data['make_id']
				is_faulty = oa_particular_form.cleaned_data['is_faulty']
				alternate_unit_id = oa_particular_form.cleaned_data['alternate_unit_id']
				oa_particular = self.oa_dao.getOAParticulars(
					oa_id=oa_id, item_id=item_id, enterprise_id=enterprise_id, make_id=make_id,
					is_faulty=is_faulty)
				if not oa_particular:
					oa_particular = OAParticulars(
						oa_id=oa_id, item_id=item_id, enterprise_id=enterprise_id, make_id=make_id,
						is_faulty=is_faulty)
				oa_particular.quantity = quantity
				oa_particular.hsn_code = oa_particular_form.cleaned_data['hsn_code']
				oa_particular.discount = oa_particular_form.cleaned_data['discount']
				oa_particular.price = oa_particular_form.cleaned_data['price']
				oa_particular.project_id = oa_particular_form.cleaned_data['project_id'] if oa_particular_form.cleaned_data['project_id'] not in ('False', 'None') else None
				oa_particular.internal_price = oa_particular_form.cleaned_data['internal_price']
				oa_particular.internal_oa_id = oa_particular_form.cleaned_data['internal_oa_id'] if oa_particular_form.cleaned_data['internal_oa_id'] not in ('False', 'None') else None
				if alternate_unit_id:
					scale_factor = helper.getScaleFactor(
						enterprise_id=enterprise_id, item_id=item_id, alternate_unit_id=alternate_unit_id)
					if scale_factor and scale_factor != 0:
						oa_particular.quantity = Decimal(oa_particular_form.cleaned_data['quantity']) * Decimal(scale_factor)
						oa_particular.price = Decimal(oa_particular_form.cleaned_data['price']) / Decimal(scale_factor) if oa_particular_form.cleaned_data['price'] else 0
						oa_particular.alternate_unit_id = alternate_unit_id
				oa_particular.remarks = oa_particular_form.cleaned_data['remarks']
				oa_items.append(oa_particular)
		logger.debug('OA Material constructed: %s' % oa_items)
		return oa_items

	def __extractOATaxFormset(self, oa_tax_formset, oa_id):
		oa_tax_list = []
		try:
			for oa_tax_form in oa_tax_formset:
				if not oa_tax_form.cleaned_data['DELETE']:
					tax_code = oa_tax_form.cleaned_data['tax_code']
					enterprise_id = oa_tax_form.cleaned_data['enterprise_id']
					oa_tax = self.oa_dao.getOATax(
						oa_id=oa_id, tax_code=tax_code, enterprise_id=enterprise_id)
					if not oa_tax:
						oa_tax = OATax(oa_id=oa_id, tax_code=tax_code, enterprise_id=enterprise_id)
					oa_tax_list.append(oa_tax)
			logger.debug('OA Tax constructed: %s' % oa_tax_list)
		except Exception as e:
			logger.exception("OA Tax Extraction from Formset failed...\n %s" % e.message)
			pass
		return oa_tax_list

	def __uploadDocument(self, oa=None, oa_dict=None):
		"""

		:param oa:
		:param oa_dict:
		:return:
		"""
		try:
			if 'document_data' in oa_dict:
				data = oa_dict['document_data']
				if data is None or data.strip() == "":
					return
				self.oa_dao.db_session.begin(subtransactions=True)
				logger.info("New OA Document attached")
				fil_ext = oa_dict['document'].split(".")[-1].lower()
				if oa.document and len(oa.document) > 0:
					oa.document = "%s.%s" % (oa.document.split(".")[0], fil_ext)
				else:
					oa.document = "%s.%s" % (uuid.uuid4(), fil_ext)
				temp_file = "/site_media/tmp/%s" % oa.document

				if data.__contains__(","):
					data = data.split(',')[-1]
				decoded_data = data.decode('base64')
				writeFile(decoded_data, temp_file)
				# Updating document in ftp
				FTPUtil().upload(
					file_path=oa.enterprise_id, filename=oa.document,
					fp=open("%s/%s" % (TEMP_DIR, oa.document)))
				self.oa_dao.db_session.add(oa)
				self.oa_dao.db_session.commit()
		except:
			self.oa_dao.db_session.rollback()
			raise

	def getOAWithStatus(self, since=None, till=None, enterprise_id=None):
		"""

		:param since:
		:param till:
		:param enterprise_id:

		:return:
		"""
		try:
			# Reading order acknowledge quantity of profiled materials
			oa_list = self.getOAWithMaterialQuantity(since=since, till=till, enterprise_id=enterprise_id)
			oa_map = self.updateOAToMap(oa_map={}, oa_list=oa_list)

			# Reading invoices against the fetched OA List
			invoices = self.getInvoiceWithMaterialQuantity(enterprise_id=enterprise_id, oa_ids=oa_map.keys())
			invoice_map = self.updateInvoiceToMap(invoice_map={}, invoices=invoices)

			return self.getOAStatus(oa_map=oa_map, invoice_map=invoice_map)
		except:
			raise

	def getOADeliveryStatus(self, since=None, till=None, enterprise_id=None):
		"""
		get OA count with delivery status
		:param since:
		:param till:
		:param enterprise_id:
		:return:
		"""
		oa_status = dict(oa_on_track=0, oa_overdue=0)
		daterange_query = ""
		try:
			if since and till:
				daterange_query = " AND order_acknowledgement.prepared_on >= DATE('%s') AND order_acknowledgement.prepared_on <= DATE('%s') " % (since, till)
			elif since:
				daterange_query = " AND order_acknowledgement.prepared_on >= DATE('%s') " % since
			elif till:
				daterange_query = " AND order_acknowledgement.prepared_on <= DATE('%s') " % till
			query = """SELECT 
					    IF(order_acknowledgement.oa_no IS NOT NULL,
					        IF(order_acknowledgement.status > 0,
					            CONCAT(order_acknowledgement.financial_year,
					                    '/',
					                    LEFT(order_acknowledgement.type, 1),
					                    '/',
					                    LPAD(order_acknowledgement.oa_no, 5, 0),
					                    IFNULL(order_acknowledgement.sub_number, '')),
					            '-NA-'),
					        '-NA-') AS oa_code,
					    party_master.party_name,
					    order_acknowledgement.grand_total,
					    order_acknowledgement.id,
					    IFNULL(order_acknowledgement.delivery_due_date,
					            NOW()) AS due_date,
					    SUM(IFNULL(oa_particulars.quantity, 0)) AS oa_qty,
					    IFNULL(invoice_materials.qty, 0) AS in_qty
					FROM
					    order_acknowledgement
					        LEFT JOIN
					    (oa_particulars) ON oa_particulars.oa_id = order_acknowledgement.id
					        AND oa_particulars.enterprise_id = order_acknowledgement.enterprise_id
					        LEFT JOIN
					    (SELECT 
					        invoice_materials.invoice_id,
					            invoice_materials.oa_no,
					            invoice_materials.enterprise_id,
					            SUM(invoice_materials.qty) AS qty,
					            invoice.status
					    FROM
					        invoice_materials
					    INNER JOIN invoice ON invoice.id = invoice_materials.invoice_id
					        AND invoice.enterprise_id = invoice_materials.enterprise_id
					    WHERE
					        invoice.status IN (0 , 1)
					            AND invoice_materials.oa_no IS NOT NULL
					            AND invoice_materials.oa_no != ''
					            AND invoice.enterprise_id = %s
					    GROUP BY invoice_materials.oa_no) invoice_materials ON invoice_materials.oa_no = order_acknowledgement.id
					        AND invoice_materials.enterprise_id = order_acknowledgement.enterprise_id
					        LEFT JOIN
					    (party_master) ON party_master.party_id = order_acknowledgement.party_id
					        AND party_master.enterprise_id = order_acknowledgement.enterprise_id
					WHERE
					    order_acknowledgement.enterprise_id = %s
					        AND order_acknowledgement.status = 1 %s 
					        AND order_acknowledgement.type != 'MRS'
					GROUP BY order_acknowledgement.id
					""" % (enterprise_id, enterprise_id, daterange_query)
			overdue_query = query + " HAVING DATE(due_date) < CURDATE() AND oa_qty > in_qty"
			on_track_query = query + " HAVING DATE(due_date) >= CURDATE() AND oa_qty > in_qty"
			overdue_result_set = executeQuery(overdue_query)
			on_track_result_set = executeQuery(on_track_query)
			oa_status['oa_on_track'] = len(on_track_result_set) if on_track_result_set is not None else 0
			oa_status['oa_overdue'] = len(overdue_result_set) if overdue_result_set is not None else 0
			oa_status['oa_pending'] = oa_status['oa_on_track'] + oa_status['oa_overdue']
			return oa_status
		except:
			raise

	def updateInvoiceToMap(self, invoice_map=None, invoices=()):
		"""

		:param invoice_map:
		:param invoices:
		:return:
		"""

		for invoice in invoices:
			invoice_dict = invoice._asdict()
			if invoice_dict['qty'] is None:
				invoice_dict['qty'] = 0
			key = "%s" % invoice.oa_id
			if key in invoice_map:
				oa_dict_existing = invoice_map[key]
				oa_dict_existing['qty'] = oa_dict_existing['qty'] + invoice_dict['qty']
			else:
				invoice_map[key] = invoice_dict
		return invoice_map

	def updateOAToMap(self, oa_map=None, oa_list=()):
		"""

		:param oa_map:
		:param oa_list:
		:return:
		"""

		for oa in oa_list:
			oa_dict = oa._asdict()
			if oa_dict['qty'] is None:
				oa_dict['qty'] = 0
			key = "%s" % oa.oa_id
			if key in oa_map:
				oa_dict_existing = oa_map[key]
				oa_dict_existing['qty'] = oa_dict_existing['qty'] + oa_dict['qty']
			else:
				oa_map[key] = oa_dict
		return oa_map

	def getOAWithMaterialQuantity(self, since=None, till=None, enterprise_id=None):
		"""

		:param since:
		:param till:
		:param enterprise_id:
		:return:
		"""
		logger.info("\nFetching OA material quantity for period %s-%s" % (since, till))
		query = self.oa_dao.db_session.query(
			OA.id.label('oa_id'), OA.delivery_due_date.label('delivery_due_date'),
			func.sum(func.ifnull(OAParticulars.quantity, 0)).label('qty')
		).outerjoin(OA.items).filter(OA.status == OA.STATUS_APPROVED, OA.enterprise_id == enterprise_id)
		if since and till:
			query = query.filter(OA.prepared_on.between(since, till))
		elif since:
			query = query.filter(OA.prepared_on >= since)
		elif till:
			query = query.filter(OA.prepared_on <= till)
		oa_list = query.group_by(OA.id).all()
		logger.info("Fetching %s OAs" % len(oa_list))
		return oa_list

	def getInvoiceWithMaterialQuantity(self, enterprise_id=None, oa_ids=()):
		"""

		:param enterprise_id:
		:param oa_ids:
		:return:
		"""
		query = self.oa_dao.db_session.query(
			InvoiceMaterial.oa_no.label('oa_id'), func.sum(InvoiceMaterial.quantity).label('qty')
		).join(InvoiceMaterial.invoice).filter(
			InvoiceMaterial.oa_no.in_(oa_ids),
			InvoiceMaterial.enterprise_id == enterprise_id, Invoice.status.in_(
				(Invoice.STATUS_APPROVED, Invoice.STATUS_DRAFT))).group_by(
			InvoiceMaterial.oa_no)
		invoice_list = query.all()
		logger.info("Fetching %s Invoices" % len(invoice_list))
		return invoice_list

	def getOAStatus(self, oa_map=None, invoice_map=None):
		"""

		:param oa_map:
		:param invoice_map:
		:return:
		"""
		try:
			oa_status = dict(oa_on_track=0, oa_overdue=0)
			today = datetime.datetime.strptime(datetime.datetime.now().strftime('%Y-%m-%d'), "%Y-%m-%d")
			for oa_id in oa_map:
				oa = oa_map[oa_id]
				if oa_id not in invoice_map or oa['qty'] > invoice_map[oa_id]['qty']:
					if oa['delivery_due_date'] and oa['delivery_due_date'] != '':
						if oa['delivery_due_date'] < today:
							oa_status['oa_overdue'] += 1
						else:  # oa['delivery_due_date'] >= today
							oa_status['oa_on_track'] += 1
			logger.info("Delivery status: %s " % oa_status)
			oa_status['oa_pending'] = oa_status['oa_on_track'] + oa_status['oa_overdue']
			return oa_status
		except:
			raise

	def generateOAFromSE(self, oa_obj=None, se_no=None, enterprise_id=None):
		"""

		:param oa_obj:
		:param se_no:
		:param enterprise_id:
		:return:
		"""
		try:
			se_obj = InvoiceService().invoice_dao.getSalesEstimate(se_id=se_no, enterprise_id=enterprise_id)
			se_code = se_obj.getCode()
			copyDictToDict(source=se_obj.__dict__, destination=oa_obj.__dict__, exclude_keys=SE_EXCLUDE_FIELD_LIST)
			oa_obj.se_id = se_obj.id
			oa_obj.se_date = se_obj.client_revised_on
			oa_obj.enterprise = se_obj.enterprise

			oa_items = []
			for item in se_obj.items:
				oa_item = OAParticulars(enterprise_id=enterprise_id)
				copyDictToDict(
							source=item.__dict__, destination=oa_item.__dict__, exclude_keys=SE_PARTICULAR_EXCLUDE_FIELD_LIST)
				oa_item.price = item.unit_rate
				oa_item.item = item.item
				oa_item.make = item.make
				oa_item.alternate_unit = item.alternate_unit
				oa_item.oa = oa_obj
				oa_items.append(oa_item)
			logger.info(oa_items)
			oa_obj.items = oa_items

			oa_taxes = []
			for tax in se_obj.taxes:
				oa_tax = OATax(enterprise_id=enterprise_id)
				copyDictToDict(
							source=tax.__dict__, destination=oa_tax.__dict__, exclude_keys=SE_TAX_EXCLUDE_FIELD_LIST)
				oa_taxes.append(oa_tax)
			oa_obj.taxes = oa_taxes

			return oa_obj, se_code
		except Exception as e:
			logger.exception("OA generation from SE Failed...\n%s" % e.message)
			raise

	def getSECodeByPartyAndType(self, enterprise_id=None, party_id=None, se_id=None, oa_type=None):
		try:
			result = []
			db_session = SQLASession()
			if party_id:
				se_materials = db_session.query(
					SalesEstimate.id.label("id"), SalesEstimate.se_no.label("se_no"),
					SalesEstimate.financial_year.label("financial_year"),
					SalesEstimate.sub_number.label("sub_number"),
					SalesEstimate.type.label("type")
				).filter(
					SalesEstimate.enterprise_id == enterprise_id, SalesEstimate.party_id == party_id, SalesEstimate.type == oa_type,
					SalesEstimate.status == 3, SalesEstimate.expiry_date >= datetime.datetime.now().date()).group_by(
					SalesEstimate.id).all()

				se_items = {}
				for se_material in se_materials:
					se_items[se_material.id] = se_material._asdict()
				if se_id is not None:
					selected_se = db_session.query(
						SalesEstimate.id.label("id"), SalesEstimate.se_no.label("se_no"),
						SalesEstimate.financial_year.label("financial_year"),
						SalesEstimate.sub_number.label("sub_number"),
						SalesEstimate.type.label("type")
					).outerjoin(SalesEstimate.items).filter(
						SalesEstimate.enterprise_id == enterprise_id, SalesEstimate.id == se_id).first()
					se_items[selected_se.id] = selected_se._asdict()
				for se_dict in se_items.values():
					se = namedtuple("SEObject", se_dict.keys())(*se_dict.values())
					se_code = SalesEstimate.generateInternalCode(
						financial_year=se.financial_year, se_id=se.id, se_no=se.se_no,
						type=se.type, sub_number=se.sub_number)
					result.append(
						{"code": se_code, "id": se.id})

			return sorted(result, key=lambda a: a['id'])
		except:
			raise

class InternalOrderAcknowledgementService:

	def __init__(self):
		"""
		"""
		self.oa_dao = OrderAcknowledgementDAO()

	def createInternalOA(self, enterprise_id, user_id, parent_oa_id, primary_enterprise_id):
		project_oa_id_map = {}
		##  The project OA id map for Group OA items by projects  { project_id : internal_oa_id }
		try:
			oa_vo = self.oa_dao.getOA(oa_id=parent_oa_id)
			user = UserDAO().getUserById(enterprise_id=primary_enterprise_id, user_id=user_id)
			parent_project_id = int(oa_vo.project_code)
			for oa_item in oa_vo.items:
				exist_internal_oa_id = oa_item.internal_oa_id if oa_item.internal_oa_id not in ('False', 'None', None, False) else None
				project_id = oa_item.project_id if oa_item.project_id not in ('False', 'None') else None
				item_id = oa_item.item_id
				if project_id:
					project_enterprise_id = self.getProjectEnterpriseId(enterprise_id=primary_enterprise_id, project_id=project_id)
					party_id = self.getPartyIdByProjectId(primary_enterprise_id=primary_enterprise_id, project_enterprise_id=project_enterprise_id, project_id=parent_project_id)
					if not party_id:
						party_name, party_code = self.getProjectNameByProjectId(enterprise_id=primary_enterprise_id, project_id=parent_project_id)
						party_id = self.createPartyToProjectEnterprise(project_enterprise_id=project_enterprise_id,
									party_name=party_name, party_code=party_code, user_id=user_id)
					if exist_internal_oa_id:
						entity = self.oa_dao.getOA(exist_internal_oa_id)
					elif project_id not in project_oa_id_map:
						entity = OA()
					else:
						entity = self.oa_dao.getOA(project_oa_id_map[project_id])
					entity = self.__extractOAForm(oa_vo=oa_vo, entity=entity, project_enterprise_id=project_enterprise_id,
												  party_id=party_id, project_id=parent_project_id)
					entity.items, entity.grand_total = self.addItemOAParticulars(entity.items, oa_item, project_enterprise_id)
					entity.prepared_by = user_id
					entity.last_modified_by = user_id
					modified_date = datetime.datetime.now()
					entity.last_modified_on = modified_date.strftime('%Y-%m-%d %H:%M:%S')
					if entity.status == 0:
						entity.remarks = ([dict(date=modified_date.strftime("%Y-%m-%d %H:%M:%S"),
												remarks="OA No - " + str(oa_vo.getSimpleCode()), by="%s" % user)])
						entity = self.internalOAApprove(enterprise_id=project_enterprise_id, entity=entity, user_id=user_id, approved_date=modified_date)
					internal_oa_id = self.saveInternalOA(entity)
					project_oa_id_map[project_id] = internal_oa_id
					if not exist_internal_oa_id:
						self.updateInternalOAId(enterprise_id=enterprise_id, parent_oa_id=parent_oa_id, item_id=item_id, internal_oa_id=internal_oa_id)
		except Exception as e:
			logger.exception("Construct Internal OA particular Failed... - %s" % e.message)

	def getProjectEnterpriseId(self, enterprise_id, project_id):
		"""
		Get Project Enterprise ID by parent Enterprise ID & Project ID
		"""
		project_enterprise_id = None
		try:
			project_enterprise_query = """SELECT project_enterprise_id FROM projects 
			WHERE enterprise_id={enterprise_id} AND id={project_id}""".format(enterprise_id=enterprise_id, project_id=project_id)
			query_data = executeQuery(project_enterprise_query)
			project_enterprise_id = query_data[0][0] if query_data else None
		except Exception as e:
			logger.exception("Get project Enterprise Id Failed... - %s" % e.message)
		return project_enterprise_id

	def getPartyIdByProjectId(self, primary_enterprise_id, project_enterprise_id, project_id):
		party_id = None
		try:
			query = """SELECT party_id FROM party_master WHERE enterprise_id={project_enterprise_id} AND 
			party_code = (SELECT code FROM projects WHERE id={project_id} AND enterprise_id={enterprise_id})""".format(enterprise_id=primary_enterprise_id,
			project_enterprise_id=project_enterprise_id, project_id=project_id)
			query_data = executeQuery(query)
			if query_data:
				party_id = query_data[0][0]
		except Exception as e:
			logger.exception("Party get failed from Project.... - %s" % e.message)
		return party_id

	def addItemOAParticulars(self, entity_items, item, project_enterprise_id):
		"""
		Add a New Item with existing OA particulars.
		"""
		internal_oa_item = []
		isItemUpdate = False
		grand_total = 0
		try:
			for rec in entity_items:
				if rec.item_id == int(item.item_id):
					rec.is_faulty = item.is_faulty
					rec.quantity = item.quantity
					rec.hsn_code = item.hsn_code
					rec.discount = item.discount
					rec.price = item.internal_price
					rec.remarks = item.remarks
					isItemUpdate = True
				grand_total += (rec.price * rec.quantity) * ((100 - rec.discount) / 100)
				internal_oa_item.append(rec)
			if not isItemUpdate:
				oa_particular = OAParticulars(
					item_id=item.item_id, enterprise_id=project_enterprise_id, make_id=item.make_id,
					is_faulty=item.is_faulty)
				oa_particular.quantity = item.quantity
				oa_particular.hsn_code = item.hsn_code
				oa_particular.discount = item.discount
				oa_particular.price = item.internal_price
				oa_particular.remarks = item.remarks
				grand_total += (oa_particular.price * oa_particular.quantity) * ((100 - oa_particular.discount) / 100)
				internal_oa_item.append(oa_particular)
		except Exception as e:
			logger.exception("Add OA Item for Internal Work Order Failed... - %s" % e.message)
		return internal_oa_item, grand_total

	def removeItemOAParticulars(self, entity_items, item_id):
		"""
		Remove a Specified Item from Internal OA particulars
		"""
		internal_oa_item = []
		try:
			for rec in entity_items:
				if rec.item_id != int(item_id):
					internal_oa_item.append(rec)
		except Exception as e:
			logger.exception("Remove OA Item for Internal Work Order Failed... - %s" % e.message)
		return internal_oa_item

	def __extractOAForm(self, oa_vo, entity, project_enterprise_id, party_id, project_id):
		try:
			entity.enterprise_id = project_enterprise_id
			entity.po_date = oa_vo.po_date
			entity.party_id = party_id
			entity.round_off = oa_vo.round_off
			entity.currency_id = oa_vo.currency_id
			entity.payment_terms = oa_vo.payment_terms
			entity.special_instructions = oa_vo.special_instructions
			entity.currency_conversion_rate = oa_vo.currency_conversion_rate
			entity.type = "IWO"
			entity.delivery_due_date = oa_vo.delivery_due_date
			entity.document_description = oa_vo.document_description
			entity.project_code = project_id
		except Exception as e:
			logger.exception("Extract Internal OA Form Failed... - %s" % e.message)
		return entity

	def saveInternalOA(self, entity):
		"""
		Save an Internal OA
		"""
		internal_oa_id = None
		db_session = self.oa_dao.db_session
		db_session.begin(subtransactions=True)
		try:
			db_session.add(entity)
			db_session.commit()
			db_session.refresh(entity)
			internal_oa_id = entity.id
		except Exception as e:
			logger.exception("Save Internal OA Failed... - %s" % e.message)
			db_session.rollback()
		return internal_oa_id

	def deleteInternalOA(self, entity):
		"""
		Delete an Internal OA
		"""
		db_session = self.oa_dao.db_session
		db_session.begin(subtransactions=True)
		try:
			db_session.delete(entity)
			db_session.commit()
			return True
		except Exception as e:
			logger.exception("Remove Internal OA Failed... - %s" % e.message)
			db_session.rollback()

	def updateInternalOAId(self, enterprise_id, parent_oa_id, item_id, internal_oa_id):
		try:
			update_query = """UPDATE oa_particulars SET internal_oa_id = {internal_oa_id} WHERE oa_id = {parent_oa_id}  AND
			 enterprise_id = {enterprise_id} AND item_id = {item_id}""".format(enterprise_id=enterprise_id, parent_oa_id=parent_oa_id,
																			   item_id=item_id, internal_oa_id=internal_oa_id)
			executeQuery(query=update_query)
		except Exception as e:
			logger.exception("Save Internal OA Failed... - %s" % e.message)
		return

	def createPartyToProjectEnterprise(self, project_enterprise_id, party_name, party_code, user_id):
		party_id = None
		db_session = self.oa_dao.db_session
		db_session.begin(subtransactions=True)
		try:
			party_to_be_created = Party(name=str(party_name), enterprise_id=project_enterprise_id, code=str(party_code), config_flags=4)
			db_session.add(party_to_be_created)
			db_session.commit()
			db_session.refresh(party_to_be_created)
			party_id = party_to_be_created.id
			createPartyLedgers(
				party=party_to_be_created, user_id=user_id, db_session=db_session,
				is_customer=True, is_supplier=True)
		except Exception as e:
			logger.exception("Party Create Failed for Project.... - %s" % e.message)
			db_session.rollback()
		return party_id

	def getProjectNameByProjectId(self, enterprise_id, project_id):
		party_name = party_code = None
		try:
			query = """SELECT name, code FROM projects WHERE id={project_id} AND enterprise_id={enterprise_id}""".format(enterprise_id=enterprise_id,
																														 project_id=project_id)
			query_data = executeQuery(query)
			party_name = query_data[0][0] if query_data else None
			party_code = query_data[0][1] if query_data else None
		except Exception as e:
			logger.exception("Party get failed from Project.... - %s" % e.message)
		return party_name, party_code


	def internalOAApprove(self, enterprise_id, entity, user_id, approved_date):
		try:
			current_fy = getFinancialYear()
			db_session = self.oa_dao.db_session
			latest_oa_no = db_session.query(func.max(OA.oa_no)).filter(
				OA.financial_year == current_fy, OA.enterprise_id == enterprise_id,
				OA.id != '0', OA.type == entity.type).first()
			entity.status = OA.STATUS_APPROVED
			entity.approved_on = approved_date.strftime('%Y-%m-%d %H:%M:%S')
			entity.financial_year = current_fy
			entity.approved_by = user_id
			new_oa_no = 0
			if latest_oa_no and latest_oa_no[0] is not None:
				new_oa_no = int(latest_oa_no[0])
			new_oa_no += 1
			entity.oa_no = new_oa_no
		except Exception as e:
			logger.exception("Failed to Approve Internal Work Order.... - %s" % e.message)
		return entity
