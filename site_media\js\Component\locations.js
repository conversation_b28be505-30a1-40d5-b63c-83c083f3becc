$(document).ready(function() {
    if ($('.component_location').length > 0) {
        $('.component_location').each(function(index, element) {
            constructLocation(element);
        });
    }
});

const enableLocationEdit = (field) => {
    $(field).closest("label").next("select").removeAttr("disabled");
	$('.chosen-select').trigger('chosen:updated');
	$(field).addClass("hide");
}

const constructLocation = async(current) => {
    const locationListAll = await getLocations();
    const fromLocation = "from";
    const toLocation = "to";
    locationList = locationListAll[fromLocation]
    console.log("locationList",locationList);
    if ($(current).attr("data-id") == 'id_stock_transfer_to') {
        locationList = locationListAll[toLocation]
    }
    const value = $(current).attr("data-value");

    const isEditDisable = $(current).attr("data-isEditDisable");
    let editIcon = "";
    if(isEditDisable == 'true') {
        const isSuper = $(current).attr("data-isSuper");
        if(isSuper) {
            editIcon += `<a class="super_edit_field super_edit_for_draft hide" onclick="enableLocationEdit(this);" data-tooltip="tooltip" data-placement="left" title="Super Edit">
                            <i class="fa fa-pencil super_edit_in_field"></i>
                         </a>`;
        }
        else {
            editIcon += `<a class="super_edit_field super_edit_for_draft hide" onclick="" data-tooltip="tooltip" data-placement="left" title="Only Super User can edit this field">
                            <i class="fa fa-pencil super_edit_in_field" style="color:#777;"></i>
                        </a>`;
        }

    }

    let locationComponent = ``
    const label = `<label>Location <span class="mandatory_mark"> *</span>${editIcon}</label>`;

    const id = $(current).attr("data-id");
    const name = $(current).attr("data-name");

    let optionsHTML = '';

    if (id !== 'id_stock_transfer_from') {
        optionsHTML = '<option value="" disabled selected>Select Location</option>';
    }
    if (locationList) {
        locationList.forEach(location => {
            optionsHTML += `<option value="${location.id}" ${location.id == value ? 'selected' : ''}>${location.name}</option>`;
        });
    }

    const select = `<select class="form-control chosen-select location_select_dropdown" ${ isEditDisable == 'true' ? "disabled" : ""} id=${id} name=${name}>
                        ${optionsHTML}
                    </select>`

    locationComponent += label;
    locationComponent += select;
    $(current).append(locationComponent);

    $(".chosen-select").chosen()
    .on('chosen:showing_dropdown', function() {
        $(current).find('.chosen-results li').each(function() {
            $(this).html($(this).html().replace(/ &gt; /g, ' <i class="fa fa-chevron-circle-right" aria-hidden="true"></i> '));
        });
    }).change(function() {
        $(current).find(".chosen-single").html($(current).find(".chosen-single").html().replace(/ &gt; /g, ' <i class="fa fa-chevron-circle-right" aria-hidden="true"></i> '));
    });

    setTimeout(() => {
        $(current).find(".chosen-single").html($(current).find(".chosen-single").html().replace(/ &gt; /g, ' <i class="fa fa-chevron-circle-right" aria-hidden="true"></i> '));
    }, 100)
}