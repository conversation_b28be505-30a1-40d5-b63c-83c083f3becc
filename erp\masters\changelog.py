"""

"""
import re
from datetime import datetime
from deepdiff import DeepDiff

from erp.accounts import logger
from erp.helper import getUser, constructDifferentMakeName
from erp.models import Material
from settings import SQLASession
from util.api_util import response_code
from util.changelog import ChangeLog
import json

__author__ = 'charlesmichel'

CONST_LOG_VC1 = '%s <b>%s</b> added'
CONST_LOG_VC2 = '%s <b>%s</b> removed'
CONST_LOG_VC3 = ' <b>%s</b>: <i>%s</i>'


class MaterialChangelog(ChangeLog):
	"""
	Changelog to do mongodb operations
	"""
	material = {}
	collection = 'materials'
	uid = "material_id"

	def __init__(self, material_type):
		self.db_session = SQLASession()
		super(MaterialChangelog, self).__init__(collection=self.collection, uid=self.uid)
		self.response_log = []
		self.logged_key_set = []
		self.material_type = material_type
		self.module_data_set = {
			'category': 'Material Category' if(self.material_type) else 'Service Category', 'name': 'Material Name' if(self.material_type) else 'Service Name', 'tariff_no': 'HSN/SAC CODE' if(self.material_type) else 'SAC CODE', 'price': 'Price',
			'unit': 'Unit', 'minimum_stock_level': 'MSL', 'makes': {
				'title': 'Make', 'make': 'Name', 'part_no': 'MPN', 'standard_packing_quantity': 'Standard Packing Quantity'},
			'alternate_unit': {'title': 'Alternate unit', 'unit': 'Alternate Unit', 'scale_factor': 'Scale Factor'},
			'sample_size': 'Sample Size', 'lot_size': 'Lot Size',
			'qc_method': 'QC Method', 'reaction_plan': 'Reaction Plan', 'standard_packing_qty': 'Standard Packing Qty',
			'description': 'Description', 'in_use': 'In Use', 'is_stocked': 'Stockable', 'remarks': 'Remarks',
			'bill_of_materials': {
				'title': 'Bill of Materials' if(self.material_type) else 'Service Package',
				'drawing_no': 'Drawing No' if(self.material_type) else 'Item Code',
				'name': 'Material Name' if(self.material_type) else 'Service Name',
				# 'makes': 'Makes',
				'qty': 'Qty'
			}, 'supplier_price_profile': {
				'title': 'Supplier Price Profile',
				'supplier': 'Supplier',
				# 'makes': 'Makes',
				'price': 'Price',
				'effect_since': 'Effect Since',
				'effect_till': 'Effect Till',
				'status': 'Status',
				'store_value': 'Store Value',
				'job': 'Job',
				'moq': 'MOQ',
				'lead_time': 'Lead Time'
			}, 'specification': {
				'title': 'Specification',
				'parameter': 'Parameter',
				'comments': 'Comments',
				'inspection_method': 'Inspection Method',
				'unit': 'Unit',
				'min_value': 'Min Value',
				'max_value': 'Max Value',
				'qc_critical': 'QC Critical',
				'reaction_plan': 'Reaction Plan',
			}, 'drawing_no': 'Drawing No' if(self.material_type) else 'Item Code'}
		self.price_profile_status = {0: 'Pending', 1: 'Approved', -1: 'Rejected'}
		logger.info('Material Changelog module initiated :)')

	def buildInsertDataStructure(self, user=None, data=None):
		"""
		Build/formulate material data to insert on mongoDB
		:param user:
		:param data:
		:return:
		"""
		response = dict()
		response['bill_of_materials'] = []
		response['supplier_price_profile'] = []
		response['makes'] = []
		response['alternate_unit'] = []
		response['specification'] = []
		response['modified_at'] = datetime.now()
		try:
			response['username'] = [user.first_name, user.last_name]
			response['drawing_no'] = str(data.drawing_no) if data.drawing_no else ''
			response['category'] = str(data.category)
			response['name'] = str(data.name) if data.name else ''
			response['price'] = str(data.price)
			response['unit'] = str(data.unit)
			response['tariff_no'] = str(data.tariff_no) if data.tariff_no else ''
			response['in_use'] = 'Checked' if bool(data.in_use) is True else 'UnChecked'
			for unit in data.materials_alternate_units:
				item = dict()
				item['unit'] = str(unit.unit)
				item['scale_factor'] = str(unit.scale_factor)
				response['alternate_unit'].append(item)
			if (self.material_type != True):
				response['minimum_stock_level'] = str(data.minimum_stock_level)
				response['is_stocked'] = 'Checked' if bool(data.is_stocked) is True else 'UnChecked'
				response['standard_packing_qty'] = str(data.standard_packing_qty) if data.standard_packing_qty else ''
				response['sample_size'] = str(data.sample_size) if data.sample_size else ''
				response['lot_size'] = str(data.lot_size) if data.lot_size else ''
				response['qc_method'] = str(data.qc_method)
				response['reaction_plan'] = str(data.reaction_plan)
				response['remarks'] = str(data.remarks)
				if data.makes_json:
					for make in json.loads(data.makes_json):
						item = dict()
						item['make'] = str(make['make'])
						item['part_no'] = str(make['mpn']) if make['mpn'] is not None else ''
						item['standard_packing_quantity'] = ''
						response['makes'].append(item)
			response['description'] = str(data.description)
			for bom in data.bill_of_materials:
				if bom.material.name is not None:
					material_name = str(bom.material.name)
					make_name = constructDifferentMakeName(bom.material.makes_json)
				else:
					material_object = self.db_session.query(Material).filter(
						Material.drawing_no == bom.material.drawing_no).first()
					material_name = str(material_object.name)
					make_name = constructDifferentMakeName(material_object.makes_json)
				material_name = material_name + ' [' + make_name + ']'if make_name else material_name
				item = dict()
				item['drawing_no'] = str(bom.material.drawing_no)
				item['name'] = str(material_name)
				item['qty'] = str(bom.quantity) if bom.quantity else ''
				response['bill_of_materials'].append(item)
			for price_profile in data.supplier_wise_materials_price:
				item = dict()
				item['supplier'] = str(price_profile.supplier.name)
				item['store_value'] = 'Checked' if bool(price_profile.is_primary) is True else 'UnChecked'
				item['job'] = 'Checked' if bool(price_profile.is_service) is True else 'UnChecked'
				item['makes'] = str(price_profile.make)
				item['price'] = str(price_profile.price)
				item['effect_since'] = str(datetime.strptime(str(price_profile.effect_since), '%Y-%m-%d %H:%M:%S'))
				item['effect_till'] = str(datetime.strptime(str(price_profile.effect_till), '%Y-%m-%d %H:%M:%S')) \
					if price_profile.effect_till is not None else ''
				item['status'] = self.price_profile_status[int(price_profile.status)]
				item['remarks'] = str(price_profile.remarks)
				item['moq'] = str(price_profile.moq) if price_profile.moq is not None else ''
				item['lead_time'] = str(price_profile.lead_time) if price_profile.lead_time is not None else ''
				response['supplier_price_profile'].append(item)
			for spec in data.specifications:
				item = dict()
				item['parameter'] = str(spec.parameter)
				item['comments'] = str(spec.comments)
				item['inspection_method'] = str(spec.inspection_method)
				item['min_value'] = str(spec.min_value) if spec.min_value else ''
				item['max_value'] = str(spec.max_value) if spec.max_value else ''
				item['unit'] = str(spec.unit)
				item['qc_critical'] = 'Checked' if bool(spec.qc_critical) is True else 'UnChecked'
				item['reaction_plan'] = str(spec.reaction_plan)
				response['specification'].append(item)

		except Exception as e:
			logger.exception(e)
		return response

	def queryInsert(self, user_id=None, enterprise_id=None, data=None):
		"""
		:param user_id:
		:param enterprise_id:
		:param data:
		:return:
		"""
		response = {}
		try:
			user = getUser(enterprise_id=enterprise_id, user_id=user_id)
			formatted_data = self.buildInsertDataStructure(user=user, data=data)
			response = self.insert(id=data.material_id, enterprise_id=enterprise_id, data=formatted_data)
		except Exception as e:
			logger.exception(e)
		return response

	def fetchLogList(self, material_id=None, enterprise_id=None, offset=None, limit=None):
		"""
		get listed voucher log data
		:param material_id:
		:param enterprise_id:
		:return:
		"""
		response = {}
		result = []
		try:
			query = self.fetchLog(id=int(material_id), enterprise_id=int(enterprise_id), offset=offset, limit=limit)
			for material in query:
				material['created_at'] = str(material['created_at'])
				material['log']['modified_at'] = str(material['log']['modified_at'])
				result.append(material)
			response = response_code.success() if len(result) > 0 else response_code.failure()
			response['data'] = result
		except Exception as e:
			logger.exception(e)
		return response

	def fetchLogData(self, material_id=None, enterprise_id=None, modified_at=None):
		"""
		get individual log data
		:param material_id:
		:param enterprise_id:
		:param modified_at:
		:return:
		"""
		response = response_code.failure()
		result = []
		try:
			query = self.fetchLogDetails(id=int(material_id), enterprise_id=int(enterprise_id), identifier=modified_at)
			for voucher in query:
				del voucher['log']['modified_at']
				result.append(voucher)
			response = response_code.success() if len(result) > 0 else response
			if len(result) > 1:
				response['data'] = self.buildLogData(result[1]['log'], result[0]['log'])
			elif len(result) > 0:
				response['data'] = self.buildLogData(None, result[0]['log'])
			response['data'] = response['data'] if len(response['data']) > 0 else ['No change detected']
		except Exception as e:
			logger.exception(e)
			response = response_code.failure()
		return response

	def dictKeyBaseLogData(self, old=None, new=None, key=None, identifier=None):
		"""

		:param old:
		:param new:
		:param key:
		:param identifier:
		:return:
		"""
		diff = DeepDiff(old[key], new[key], ignore_order=False)
		for oitem in old[key]:
			for nitem in new[key]:
				if oitem[identifier] == nitem[identifier]:
					self.dictChangeIterator(diff=diff, old=old, new=new, key=key)
				else:
					self.dictAddIterator(diff=diff, key=key)
					self.dictRemoveIterator(diff=diff, key=key)

		return False

	def dictChangeIterator(self, diff=None, old=None, new=None, key=None):
		"""

		:param diff:
		:param old:
		:param new:
		:param key:
		:return:
		"""
		if 'values_changed' in diff:
			for item in diff['values_changed'].keys():
				var_chg = re.findall("\[(.*?)\]", item.replace('\'', ''))
				if var_chg[1] not in self.logged_key_set:
					self.response_log.append('<b>%s</b> of %s <b>%s</b> has changed to <b>%s</b>' % (
						var_chg[1].capitalize(), key, old[key][int(var_chg[0])][var_chg[1]],
						new[key][int(var_chg[0])][var_chg[1]]))
					self.logged_key_set.append(var_chg[1])
		return False

	def dictAddIterator(self, diff=None, key=None):
		"""

		:param diff:
		:param key:
		:return:
		"""
		if 'iterable_item_added' in diff:
			for item_added in diff['iterable_item_added'].items():
				if key == 'makes':
					log = '%s <b>%s</b> added %s <b>%s</b>%s<br />' % (
						self.module_data_set[key]['title'], str(item_added[1]['make']),
						'with MPN' if item_added[1]['part_no'] != '' else "", str(item_added[1]['part_no']),
						' {cons} SPQ <b>{spq}</b>'.format(
							spq=item_added[1]['standard_packing_quantity'], cons=' and' if item_added[1]['part_no'] != '' else ' with') if item_added[1]['standard_packing_quantity'] != '' else "")
					self.response_log.append('%s' % log)
				elif type(self.module_data_set[key]) is not str:
					log = '<b>%s</b> added with following values: <br />' % self.module_data_set[key]['title']
					for i, elem in self.module_data_set[key].items():
						if i != 'title':
							log += CONST_LOG_VC3 % (elem, ', '.join(str(l_data) for l_data in item_added[1][i]) if type(
								item_added[1][i]) == list else str(item_added[1][i]))
					self.response_log.append('%s' % log)
				else:
					self.response_log.append(
						'%s named <b>%s</b> was added' % (self.module_data_set[key], item_added[1]))
		return False

	def dictRemoveIterator(self, diff=None, key=None):
		"""

		:param diff:
		:param key:
		:return:
		"""
		if 'iterable_item_removed' in diff:
			for item_removed in diff['iterable_item_removed'].items():
				if key == 'makes':
					log = '%s <b>%s</b> removed %s <b>%s</b><br />' % (
						self.module_data_set[key]['title'], str(item_removed[1]['make']),
						'with MPN' if item_removed[1]['part_no'] != '' else "", str(item_removed[1]['part_no']))
					self.response_log.append('%s' % log)
				elif type(self.module_data_set[key]) is not str:
					log = '<b>%s</b> removed with following values: <br />' % self.module_data_set[key]['title']
					for i, elem in self.module_data_set[key].items():
						if i != 'title':
							log += CONST_LOG_VC3 % (elem, ', '.join(
								str(l_data) for l_data in item_removed[1][i]) if type(
								item_removed[1][i]) == list else str(item_removed[1][i]))
					self.response_log.append('%s' % log)
				else:
					self.response_log.append(
						'%s named <b>%s</b> was removed' % (self.module_data_set[key], item_removed[1]))

	def fieldLogData(self, key=None, title=None, old=None, new=None):
		"""

		:param key:
		:param title:
		:param old:
		:param new:
		:return:
		"""
		if old is not None and key in old:
			if type(new[key]) == list and type(old[key]) == list:
				diff = DeepDiff(old[key], new[key], ignore_order=True)
				if key in ['specification'] and old[key]:
					self.dictKeyBaseLogData(old=old, new=new, key=key, identifier='parameter')
				else:
					self.dictAddIterator(diff=diff, key=key)
					self.dictRemoveIterator(diff=diff, key=key)
					self.dictChangeIterator(diff=diff, old=old, new=new, key=key)
			else:
				diff = DeepDiff(old[key], new[key])
				if 'values_changed' in diff:
					if str(diff['values_changed']['root']['old_value']) == '' and str(
							diff['values_changed']['root']['new_value']) != '':
						self.response_log.append(
							CONST_LOG_VC1 % (title, diff['values_changed']['root']['new_value']))
					elif str(diff['values_changed']['root']['new_value']) == '' and str(
							diff['values_changed']['root']['old_value']) != '':
						self.response_log.append(
							CONST_LOG_VC2 % (title, diff['values_changed']['root']['new_value']))
					else:
						self.response_log.append('%s changed from <b>%s</b> to <b>%s</b>' % (
							title,
							diff['values_changed']['root']['old_value'],
							diff['values_changed']['root']['new_value']))
		elif key in new:
			if type(new[key]) == list:
				if type(title) == dict:
					if key == 'makes':
						for item in new[key]:
							log = '%s <b>%s</b> added %s <b>%s</b>%s<br />' % (
								self.module_data_set[key]['title'], str(item['make']),
								'with MPN' if item['part_no'] != '' else "", str(item['part_no']),
								' {cons} SPQ <b>{spq}</b>'.format(
									spq=item['standard_packing_quantity'], cons=' and' if item['part_no'] != '' else ' with') if item['standard_packing_quantity'] != '' else "")
							self.response_log.append('%s' % log)
					else:
						for item in new[key]:
							log = '<b>%s</b> added with following values: <br />' % self.module_data_set[key]['title']
							for i, elem in self.module_data_set[key].items():
								if i != 'title':
									log += CONST_LOG_VC3 % (elem, ', '.join(str(l_data) for l_data in item[i]) if type(
										item[i]) == list else str(item[i]))
							self.response_log.append('%s' % log)
				else:
					self.response_log.append(CONST_LOG_VC1 % (title, ', '.join(str(l_data) for l_data in new[key])))
			elif new[key] != '':
				self.response_log.append(CONST_LOG_VC1 % (title, new[key]))
		return False

	def buildLogData(self, old=None, new=None):
		"""

		:param old:
		:param new:
		:return:
		"""
		for key, title in self.module_data_set.items():
			self.fieldLogData(key=key, title=title, old=old, new=new)
		return self.response_log
