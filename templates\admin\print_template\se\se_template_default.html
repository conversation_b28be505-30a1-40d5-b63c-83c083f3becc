<style>
    .pdf_others_details {
        width: 85px;
        display: inline-table;
        word-break: break-word;
    }
    .payment_term {
		  width:150px;
		  display:inline-block;
		  vertical-align:top;
	}
	.payment_details{
		  width: calc(100% - 180px);
		  display: inline-block
	}
	.pdf_enterprise_details{
	    width:140px !important;
	}
	.registration_label {
        width: 30%;
        display: inline-block;
        word-break: break-word;
        vertical-align: top;
    }
    .registration_data{
		width: 180px;
		display: inline-table;
		word-break: break-word;
		vertical-align: top;
	}
</style>
<div class="se_pdf_editor">
	<div class="row">
        <div class="col-sm-5 pdf_company_address_container" style="padding-left:1px !important;">
            <img src="{{enterprise_logo}}" class="pdf_company_logo" style="height:{{header_res.company.logo.size}}px;"><br>
            {% if header_res.company.company_name.print %}
                {% if header_res.company.company_name.font_size != '0' %}
                    <span class="pdf_company_name" style="font-size:12px;font-family: 'pdf_{[header_res.company.company_name.font_family]}'">{{source.enterprise.name}}</span><br>
                {% endif %}
            {% endif %}
        </div>
        <div class="col-sm-7 text-right" style="display: inline-block;">
            <div class="col-sm-12" style="margin-top: 0px;">
                <span class="pdf_form_name text-right pdf_form_name_text_size" style="font-size: 20px;word-break: break-all;"><b class="pdf_form_name_text">{{ header_res.form_name.label.se }}</b></span>
                {% if header_res.font_size != '0' %}
                    <span class="pdf_sales_estimate text-left pdf_company_details_container" style="font-size: 12px; padding: 0;word-break: break-all;"><b class="pdf_sales_estimate_prefix">#</b> <span class="pdf_sales_estimate preview_sales_estimate_number">{{ source.getCode }} </span></span><br>
                {% endif %}
            </div>
            <div class="col-sm-12 text-right" style="margin-top: 3px;padding-left:54px">
                 {% if header_res.font_size != '0' %}
                    <span class="pdf_indent_date" style="font-size: 10px;">
                        <span class="pdf_indent_date_txt pdf_company_details_container" style="width:40% !important">{{ header_res.field_name.se_date.label }}</span> <b>
                        <span class="pdf_company_details_container" style="word-break: break-all">{{ se_date }}</span></b>
                    </span>
                {% endif %}
            </div>
        </div>
		<div class="clearfix"></div>
		<div class="col-sm-12 notes_bottom">
            <hr style="margin:0;border: 1pt solid #000; " />
        </div>
        <div class="col-sm-6 pdf_company_address_container" style="font-size: 14px;">
            {% if header_res.font_size != '0' %}
                {% if header_res.company_info.print_address %}
                    <span class="pdf_company_address" style="word-break: break-word;">
                        {{ enterprise_address }}
                    </span>
                {% endif %}
                {% if header_res.company_info.print_phone_no %}
                    <span class="pdf_company_contact pdf_company_address_container" style="font-size: 14px;word-break: break-word;">
                        <br><b>Ph:</b> {% if source.enterprise.primary_contact_details.contact.phone_no %} {{ source.enterprise.primary_contact_details.contact.phone_no }} {% endif %}
                    </span>
                {% endif %}
                {% if header_res.company_info.print_email %}
                    <span class="pdf_company_email pdf_company_address_container" style="font-size: 14px;word-break: break-word;">
                        <b>E-mail:</b>{% if source.enterprise.primary_contact_details.contact.email %} {{ source.enterprise.primary_contact_details.contact.email }} {% endif %}
                    </span>
                {% endif %}
                {% if header_res.company_info.print_fax %}
                    <span class="pdf_company_fax pdf_company_address_container" style="font-size: 14px;word-break: break-word;">
                        <b>Fax:</b>{% if source.enterprise.primary_contact_details.contact.fax_no %} {{ source.enterprise.primary_contact_details.contact.fax_no }} {% endif %}
                    </span>
                {% endif %}
            {% endif %}
        </div>
        <div class="col-sm-6 pdf_company_address_container" style="padding: 3px 15px; font-size: 12px;word-break: break-all;">
			{% for reg_detail in enterprise_reg %}
                {% if header_res.font_size != '0' %}
			        <span class="pdf_registration_{{ reg_detail.label_id }}"><b class="pdf_enterprise_details registration_label" >{{ reg_detail.label }}</b><span class="registration_data">:  {{ reg_detail.details }}</span><br /></span>
                {% endif %}
			{% endfor %}
		</div>
        <div class="clearfix"></div>
		<div class="col-sm-12 notes_bottom">
            <hr style="margin:0;border: 1pt solid #000; " />
        </div>
        <div class="col-sm-5  pdf_bill_to_address pdf_company_details_container" style="font-size: 14px;">
            <span style="font-size:14pt;word-break: break-word;">
                {% if header_res.font_size != '0' %}
                    <b> {{header_res.billing_address.label}}</b>
                    <span class="notes_bottom">
                        <hr style="margin:0;border: 1px solid #000; ">
                    </span>
                {% endif %}
            </span>
            <div style="word-break: break-word;">
                 {% if header_res.font_size != '0' %}
                    <b>{{ source.customer.name }}</b><br>
                    {{ source.customer.address_1 }}, {{ source.customer.address_2 }}<br>
                    {{ source.customer.city }}, {{ source.customer.state }},{{ source.customer.pin_code }},
                    {% for country in country_list %}
                        {% if country.country_code == source.customer.country_code %}
                            {{ country.country_name|upper }}
                        {% endif %}
                    {% endfor %}
                    {% if source.customer.primary_contact_details.contact.email %}
                        <br><b>E-mail:</b>{{ source.customer.primary_contact_details.contact.email }}
                    {% endif %}
                    {% if source.customer.primary_contact_details.contact.phone_no %}<br><b>Ph:</b> {{ source.customer.primary_contact_details.contact.phone_no }} {% endif %}<br>
                    {% for reg_detail in source.customer.registration_details %}
                        {% if reg_detail.label == "GSTIN" and reg_detail.details != "" %}
                            <b>GSTIN:</b> {{ reg_detail.details }}
                        {% endif %}
                    {% endfor %}
                    {% for reg_detail in source.customer.registration_details %}
                        {% if reg_detail.label != "GSTIN" and reg_detail.details != "" %}
                            <b>{{ reg_detail.label }}:</b> {{ reg_detail.details }}
                        {% endif %}
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        <div class="col-sm-6 pdf_company_details_container">
            {% if header_res.field_name.payment_terms.print %}
                {% if header_res.font_size != '0' %}
                    <div class="pdf_payment_term">
                        <span  class="pdf_enterprise_details payment_term pdf_payment_term_txt" style="font-weight: bold;word-break:break-all;"><b>{{ header_res.field_name.payment_terms.label }}</b></span>
                        <span class="pdf_enterprise_details payment_details">: {{ payment_terms }}</span>
                    </div>
                {% endif %}
            {% endif %}
            {% if header_res.field_name.expiry_date.print %}
                {% if header_res.font_size != '0' %}
                    <div class="pdf_expiry_date">
                        <span class="pdf_company_details_container payment_term pdf_expiry_date_txt pdf_enterprise_details" style="font-size:12px;font-weight: bold;word-break:break-all;"><b>{{header_res.field_name.expiry_date.label}}</b></span>
                        <span class="pdf_enterprise_details payment_details">: {{ se_expiry_date }}</span>
                    </div>
                {% endif %}
            {% endif %}
		</div>
        <div class="clearfix"></div><br>
        <div class="col-sm-12">
            <table class="table item_table table-bordered row-seperator column-seperator" id="item_table" style="font-size:11px;">
                 <thead>
                    <tr class="row_seperator column_seperator header_shading">
                        <th class="text-center td_sno td_sno_text" rowspan="2" scope="col" style="width: 6%;">S.No</th>
                        <th class="text-center td_description td_description_text" rowspan="2" style="width:30%;">Description</th>
                        <th class="text-center pdf_item_hsn_code_txt td_hsn_code" rowspan="2">HSN/SAC</th>
                        <th class="text-center td_qty td_qty_text" rowspan="2" scope="col">Qty</th>
                        <th class="text-center td_uom td_uom_text" rowspan="2" scope="col">UOM</th>
                        <th class="text-center td_price td_price_text" rowspan="2" scope="col">Price<br>({{ source.currency.code }})</th>
                        <th class="text-center td_disc td_disc_text" rowspan="2" scope="col">Disc<br>(%)</th>
                        <th class="text-center td_tax td_tax_text" rowspan="2" scope="col">Total<br>({{ source.currency.code }})</th>
                    </tr>
                </thead>
                <tbody>
                    {% for material in se_item_details %}
                        <tr class="row_seperator column_seperator header_shading">
                            <td class="text-center td_sno">{{forloop.counter}}</td>
                            <td><span class="pdf_item_name"><span class="pdf_item_name_txt"></span> {{ material.material_name }}<br></span>
                                {% if material.material_drawing_no %}
                                    <span class="pdf_item_drawing_number">
                                        <span class="pdf_item_drawing_number_txt"></span>
                                        {{ material.material_drawing_no }}<br />
                                    </span>
                                {% endif %}
                                {% if item_res.item_table.item_details.make.print and item_res.item_table.item_details.part_no.print %}
                                {% if material.material_make and material.material_make != "" %}
                                    <span class="pdf_item_make">~lsqb;
                                        <i class="pdf_item_make_txt"></i>{{ material.material_make }}
                                    </span>
                                    {% if material.part_no and material.part_no != "" %}
                                        <span class="pdf_item_part">
                                            <i class="pdf_item_part_txt"></i>
                                        </span>
                                        <span>{{ material.part_no }} ~rsqb;<br></span>
                                    {% else %}
                                         ~rsqb;<br>
                                    {% endif %}
                                {% endif %}
                                {% elif item_res.item_table.item_details.make.print or item_res.item_table.item_details.part_no.print %}
                                    {% if item_res.item_table.item_details.make.print %}
                                        {% if material.material_make and material.material_make != "" %}
                                            <span class="pdf_item_make">~lsqb;
                                                <i class="pdf_item_make_txt"></i>{{ material.material_make }} ~rsqb;<br>
                                            </span>
                                        {% endif %}
                                    {% else %}
                                        {% if material.part_no and material.part_no != "" %}
                                            <span class="pdf_item_part">~lsqb;
                                                <i class="pdf_item_part_txt"></i>
                                            </span>
                                            <span>{{ material.part_no }} ~rsqb;<br></span>
                                        {% endif %}
                                    {% endif %}
                                {% endif %}
                                {% if material.hsn_code %}
                                    <span class="pdf_item_hsn_code"><i class="pdf_item_hsn_code_txt"></i> {{ material.hsn_code }}<br /></span>
                                {% endif %}
                                {% if material.material_description %}
                                    <span class="pdf_item_desc"><span class="pdf_item_desc_txt"></span>{{ material.material_description }}<br></span>
                                {% endif %}
                            </td>
                            <td class="text-center td_hsn_code">{{ material.hsn_code }}</td>
                            <td class="text-right td_qty">{{ material.material_quantity|floatformat:2 }}<br /><span class="pdf_unit_in_price hide">({{ material.material_unit }})</span></td>
                            <td class="text-center td_uom">{{ material.material_unit }}</td>
                            <td class="text-right td_price">{{ material.material_rate|floatformat:2 }}</td>
                            <td class="text-right td_disc">{{ material.material_discount }}</td>
                            <td class="text-right td_tax">{{ material.material_taxable_value }}</td>
                        </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="row_seperator column_seperator total_section">
                        <td colspan="3" class="text-right total_section_1"><b>Grand Total</b><span class="total_inclusice_tax hide"> (inclusive tax)</span></td>
                        <td colspan="8" class="text-right total_section_3">{{ source.currency.code }} <b>{{ source.grand_total }}</b></td>
                    </tr>
                    <tr class="tr_total_in_words row_seperator show_total_in_words total_in_words">
                        <td colspan="8" class="full-length-td"><b>Total Value ({{ source.currency.code }}):</b> {{ total_in_words|upper }}</td>
                    </tr>
                    {% if header_res.field_name.special_instructions.print %}
                        {% if special_instructions %}
                            <tr class="se_special_instruction">
                                <td colspan="8" class="full-length-td"><b class="se_special_instruction_txt">{{ header_res.field_name.special_instructions.label }}</b>:  {{ special_instructions }}</td>
                            </tr>
                        {% endif %}
                    {% endif %}
                    {% if source.notes != "" %}
                        <tr>
                            <td colspan="8">
                                <strong>Notes:</strong>
                                {% autoescape off %} {{ source.notes }} {% endautoescape %}
                            </td>
                        </tr>
                    {% endif %}
                </tfoot>
            </table>
        </div>
		<div id="footer" class=" auth_footer" style="margin-top:6px;width:100%;">
			<div class="col-sm-4 text-left signatory_content"></div>
			<div class="col-sm-4 text-center signatory_content">
			</div>
			<div class="col-sm-4 text-right signatory_content pdf_auth_sign">
				<span class="sign_company_name" style="word-break: break-all;">
					<i>For {{ source.enterprise.name }}</i>
				</span>
				<br>
                {% if header_res.field_name.authorized_sign.print %}
                    {% if header_res.field_name.authorized_sign.print_approversign %}
                        <div style="min-height: 50px;">
                            <span class="show_approver_signature"><img src="{{ approver_signature }}" style="max-width: 100px;" /><br></span>
                        </div>
                    {% endif %}
                    <br>
                    <span><b class="pdf_auth_sign_txt" style="word-break: break-all;">{{ header_res.field_name.authorized_sign.label }}</b></span>
                    </span>
                {% endif %}
			</div>
            <div class="col-sm-12" style="font-size: 8px;">
                <hr style="margin: 0;" />
            </div>
		</div>
	</div>
</div>