var prepareUsageReportModal = function () {
    if ($("#usageReport").text() == "") {
        $("body").append(`<div id="usageReport" class="modal fade" role="dialog">
	    <div class="modal-dialog modal-lg">
	        <div class="modal-content">
	            <div class="modal-header">
	                <button type="button" class="close" data-dismiss="modal">&times;</button>
	                <h4 class="modal-title">Material Usage</h4>
	            </div>
	            <div class="modal-body">
			        <div style="position: absolute; top: -44px; right: 44px;">
					    <a role="button" class="btn btn-add-new pull-right" onclick="GeneralExportTableToCSV.apply(this, [$('#usageReportTable'), 'Material_Usage_Report.csv']);" data-tooltip="tooltip" title="Download&nbsp;as&nbsp;CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
			        </div>
			        <table id="usageReportTable" class="table table-bordered custom-table table-striped tableWithText no-sorting">
						<thead><tr>
							<th style="width: 50px;" >S.No</th>
	                        <th >Drawing No</th>
							<th >Name</th>
	                        
							<th style="width: 100px;" > Faulty</th>
							<th style="width: 80px;" >UoM</th>
							<th style="width: 100px;"  class="for_dc">Expected <span class="th-sub-heading bracket-enclosed">Based on BoM & JO</span></th>
							<th style="width: 100px;" class="for_dc" >Received</th>
							<th style="width: 100px;" class="for_dc" >Returned</th>
							<th style="width: 100px;" class="for_dc" >To Be Received</th>
							<th style="width: 100px;" class="for_dc" >Excess Received</th>
						</tr></thead> <tbody></tbody>
					</table>
			        <div id='loadingmessage' class="text-center" style='display:none;margin-top:95px'>
                        <img src='/site_media/images/loading_spinner.gif' style="width:75px"/>
                        <br/> Please wait...
			         </div>
	            </div>
                <div class="modal-footer">
                     <span class="pull-left nonStock-text">  <span class=" nonStock-text non_stock-flag"></span> - NON-STOCKABLE</span>
                     <span class="pull-left nonStock-text">  <span class=" nonStock-text service-item-flag"></span> - SERVICE</span>
                    <a class="btn btn-cancel" data-dismiss="modal">Close</a>
                </div>
	        </div>
	    </div></div>`);
    }
    $("#usageReport").modal("show");
    $("#loadingmessage").removeClass("hide");
}

var constructUsageReportTableRow = function (item, i, child) {
	var childArrow = "";
    if(child == false && item['invoice_id'] =="")
        childArrow = `<i class='fa fa-caret-down fa-for-arrow'></i> <span>${item.drawing_no}</span>`;
    else if (child == false && item['invoice_id'] != "")
		childArrow = `<span style='display:block'>${item.drawing_no}</span>`;
	else
		childArrow = `<span style='padding-left: 8px;display:block'>${item.drawing_no}</span>`;
	var material_name = item.name;
    if (item.material_type==1){
        classname='no-bg';
        material_name = childArrow;
    } else{ classname='tr-alert-danger' }
    if(item.drawing_no == "-NA-" || item.drawing_no == null){
       item.drawing_no = "";
    }
    var makes = "";
    if (item.makes.length > 0){
        makes = ``;
        for (j = 0; j <= item.makes.length - 1 ; j++) {
            if (item.makes[j][1] == "") { item.makes[j][1] = "-NA-"}
            makes += `<span> ${item.makes[j][1]}</span>`;
        }
    }
    else {
        makes = item.make_name;
    }
	var qty = `<input type="text" id="id_catmaterial_-${i}-qty" name="catmaterial_qty" class="form-control text-right setUnitValue text-left bom_po_qty bom_amt_calc mandatory_field" maxlength="16" onfocus="setNumberRangeOnFocus(this,12,3)" autocomplete="off" value="0" />`;
    var unit_id = `<input hidden="hidden" id="id_oamaterial_unit_id_${i}" type="text" class="form-control text-left " name="oamaterial_unit_id" value='${item.unit_id}' >`;
    var difference = item.qty - item.actual_qty;
    var returned = 0
    if(item.returned != 'undefined' && item.returned != undefined) {
        returned = item.returned
    }
    var toBeReceived = (item.qty + returned) - item.supplied
    var excessReceived = (item.supplied - returned) - item.qty
    if (excessReceived < 0) {
        excessReceived = excessReceived * -1
    }
    if (toBeReceived > 0 ){
        excessReceived = 0
    }
    if (excessReceived > 0 ){
        toBeReceived = 0
    }
    if (item.qty < item.actual_qty){
        classname='tr-alert-danger'
    }
    var faulty  = (item.is_faulty == 1 ? 'YES' : 'NO');
    var item_name = item.name;
    var itemTypeFlag = "";
    if(item.is_service == 1){
        itemTypeFlag = `<span class="service-item-flag"></span>`;
    }
    if(item.material_type == 0 && item.is_service != 1) {
        itemTypeFlag = `<span class='non_stock-flag'></span>`;
    }
	// return `<tr class="${classname}" data-toggle='close' data-padding='0' data-parent='${item.cat_code}_${sessionStorage.clickcount}' id='${item.cat_code}_${sessionStorage.clickcount}' bgcolor='#ececec' border='1' align='left' style='font-size:12px; font-weight:normal;'>
    //     <td hidden=hidden class="exclude_export"> ${item.cat_code} - ${item.drawing_no} </td>
    //     <td class='text-center bom-sno'>${(i+1)}</td>
    //     <td width='15%'>${item.drawing_no}</td>
    //     <td width='19%'>${item_name} ${itemTypeFlag}</td>
    //     <td width='15%'>${makes}</td>
    //     <td width='5%' class='text-center'>${faulty}</td>
    //     <td width='5%' class='text-center'>${item.unit}</td>
    //     <td width='5%'class='text-center'>${item.qty.toFixed(3)}</td>
    //     <td width='5%'class='text-center'>${item.supplied.toFixed(3)}</td>
    //     <td width='5%'class='text-center'>${returned.toFixed(3)}</td>
    //     <td width='5%' class='text-center'>${toBeReceived.toFixed(3)}</td>
    //     <td width='5%' class='text-center'>${excessReceived.toFixed(3)}</td>
    //     </tr>`;
    return `<tr class="${classname}" data-toggle='close' data-padding='0' data-parent='${item.cat_code}_${sessionStorage.clickcount}' id='${item.cat_code}_${sessionStorage.clickcount}' bgcolor='#ececec' border='1' align='left' style='font-size:12px; font-weight:normal;'>
        <td hidden=hidden class="exclude_export"> ${item.cat_code} - ${item.drawing_no} </td>
        <td class='text-center bom-sno'>${(i+1)}</td>
        <td width='15%'>${item.drawing_no}</td>
        <td width='19%'>${item_name} ${itemTypeFlag}</td>
        <td width='5%' class='text-center'>${faulty}</td>
        <td width='5%' class='text-center'>${item.unit}</td>
        <td width='5%'class='text-center'>${item.qty.toFixed(3)}</td>
        <td width='5%'class='text-center'>${item.supplied.toFixed(3)}</td>
        <td width='5%'class='text-center'>${returned.toFixed(3)}</td>
        <td width='5%' class='text-center'>${toBeReceived.toFixed(3)}</td>
        <td width='5%' class='text-center'>${excessReceived.toFixed(3)}</td>
        </tr>`;
}

var showUsageReport = function (url="", params={}) {
    prepareUsageReportModal();
    $("#usageReportTable tbody").html("");
    setTimeout(function () {
        $.ajax({
            url: url,
            type: "post",
            datatype: "json",
            data: params,
            success: function(response) {
                $("#loadingmessage").addClass("hide");
                if (response.response_code != 400 && response.length != 0) {
                    $.each(response, function (i, item) {
                        $('#usageReportTable tbody').append(constructUsageReportTableRow(item, i, child=false)).addClass('tbl');
                        if (item.child.length > 0) {
                            $.each(item.child, function (ix, itemx) {
                                $('#usageReportTable tbody').append(constructUsageReportTableRow(itemx, ix, child=true)).addClass('tbl');
                            });
                        }
                    });// css for update the table row color and border line
                    TooltipInit();
                }
            }
        });
    }, 200);
}
