"""
Backend for the Stores Module.
"""
import json
from datetime import datetime
from decimal import Decimal

from sqlalchemy import Integer, cast

from erp import logger, helper
from erp.accounts.backend import AccountService
from erp.dao import DataAccessObject, executeQuery
from erp.masters.backend import MasterDAO
from erp.models import Indent, IndentMaterial, MaterialAlternateUnit, PurchaseOrder, OAIndentMap, \
	PurchaseOrderMaterial, Enterprise, PurchaseOrderMaterialDeliverySchedules, Receipt, ReceiptMaterial, \
	InvoiceMaterial, Invoice, MRP_Materials
from erp.purchase.changelog import PurchaseChangelog

__author__ = 'saravanan'

from erp.production.queries import production_plan_staus_query, getProductionLogQuery, getMIMaterialQuery, \
	getManufactureIndentListQuery, getOADetailsQuery, getIndentCompletedStatusQuery

from erp.purchase.service import PurchaseService

from erp.sales.oa_backend import OrderAcknowledgementService
from erp.tasks import closing_stock_material_wise_queue_creator
from util.helper import getFinancialYear
from erp.purchase.json_api import party_delivery_details

PRODUCTION_INDENT = 1


class ProductionDAO(DataAccessObject):
	"""
	Class that handles all the Production module related DB activities - like fetching,
	save and deleting persistent objects
	"""

	def getAllManufactureIndents(self, enterprise_id):
		return self.db_session.query(Indent).filter(
			Indent.enterprise_id == enterprise_id, Indent.indent_module_id == PRODUCTION_INDENT).all()

	def getManufactureIndent(self, indent_no=None, enterprise_id=None):
		"""

		:param indent_no:
		:param enterprise_id:
		:return:
		"""
		return self.db_session.query(Indent).filter(
			Indent.indent_no == indent_no, Indent.enterprise_id == enterprise_id).first()

	def getManufactureIndentThroughOA(self, oa_id=None, enterprise_id=None):
		"""

		:param oa_id:
		:param enterprise_id:
		:return:
		"""
		return self.db_session.query(OAIndentMap.indent_no).filter(
			OAIndentMap.oa_id == oa_id, OAIndentMap.enterprise_id == enterprise_id).first()

	def getIndentMaterial(self, indent_no=None, item_id=None, enterprise_id=None, make_id=None):
		ind_mat_query = self.db_session.query(IndentMaterial).filter(
			IndentMaterial.indent_no == indent_no, IndentMaterial.item_id == item_id,
			IndentMaterial.enterprise_id == enterprise_id, IndentMaterial.make_id == make_id)
		return ind_mat_query.first()

	def getIndentMaterials(self, indent_no=None, enterprise_id=None):
		ind_mat_query = self.db_session.query(IndentMaterial).filter(
			IndentMaterial.indent_no == indent_no, IndentMaterial.enterprise_id == enterprise_id)
		return ind_mat_query.all()

	def getScaleFactor(self, enterprise_id=None, item_id=None, alternate_unit_id=None):
		scale_factor = self.db_session.query(MaterialAlternateUnit.scale_factor).filter(
			MaterialAlternateUnit.enterprise_id == enterprise_id, MaterialAlternateUnit.item_id == item_id,
			MaterialAlternateUnit.alternate_unit_id == alternate_unit_id).first()
		return scale_factor.scale_factor
	
	def getLatestIndentCode(self, financial_year=None, enterprise_id=None):
		"""
		Generate the new indent code, combined with the financial year. As per requirement the numeric part of code is
		to be recycled every year.
		Latest indent number called to generate the new indent no. This method is called instead of the DB's
		autoincrement as id counter will increment even when the data persist is not successful, which hinders the
		functional requirement that indent's must be sequentially numbered.
		:return:
		"""
		financial_year = financial_year if financial_year else datetime.now().strftime('%Y')
		fy_latest_indent_code = self.db_session.query(Indent.indent_id).filter(
			Indent.financial_year == financial_year, Indent.enterprise_id == enterprise_id,
			Indent.indent_module_id == PRODUCTION_INDENT,
			Indent.indent_id.isnot(None)).order_by(cast(Indent.indent_id, Integer).desc()).first()
		indent_code = 1 if fy_latest_indent_code is None or not (
				'%s' % fy_latest_indent_code.indent_id).isdigit() else (
				int('%s' % fy_latest_indent_code.indent_id) + 1)
		logger.debug('Latest Indent Code: %s' % indent_code)
		return indent_code
	
	def saveIndent(self, indent=None):
		"""

		:param indent:
		:return:
		"""
		try:
			if indent.indent_id is None:
				indent.indent_id = self.getLatestIndentCode(
					financial_year=indent.financial_year, enterprise_id=indent.enterprise_id)
				indent.raised_date = datetime.now()
			indent.modified_date = datetime.now()
			self.db_session.add(indent)
		except Exception:
			raise

	def getPurchaseOrder(self, enterprise_id=None, po_id=None):
		"""
		enterprise_id and  po_id
		:return:
		"""
		try:
			return self.db_session.query(PurchaseOrder).filter(
				PurchaseOrder.enterprise_id == enterprise_id, PurchaseOrder.po_id == po_id).first()

		except Exception:
			raise

	def getPurchaseMaterial(self, po_id=None, item_id=None, enterprise_id=None, make_id=None):
		"""

		:return:
		"""
		try:
			pur_mat_query = self.db_session.query(PurchaseOrderMaterial).filter(
				PurchaseOrderMaterial.po_id == po_id, PurchaseOrderMaterial.item_id == item_id,
				PurchaseOrderMaterial.enterprise_id == enterprise_id, PurchaseOrderMaterial.make_id == make_id)
			return pur_mat_query.first()
		except Exception:
			raise

	def getPurchaseMaterialDeliverySchedule(
			self, po_id=None, item_id=None, enterprise_id=None, make_id=None, due_date=None, start_date=None):
		"""

		:return:
		"""
		try:
			if start_date:
				pur_mat_query = self.db_session.query(PurchaseOrderMaterialDeliverySchedules).filter(
					PurchaseOrderMaterialDeliverySchedules.po_id == po_id, PurchaseOrderMaterialDeliverySchedules.item_id == item_id,
					PurchaseOrderMaterialDeliverySchedules.enterprise_id == enterprise_id,
					PurchaseOrderMaterialDeliverySchedules.make_id == make_id,
					cast(PurchaseOrderMaterialDeliverySchedules.quantity, Integer) == 0)
			else:
				pur_mat_query = self.db_session.query(PurchaseOrderMaterialDeliverySchedules).filter(
					PurchaseOrderMaterialDeliverySchedules.po_id == po_id, PurchaseOrderMaterialDeliverySchedules.item_id == item_id,
					PurchaseOrderMaterialDeliverySchedules.enterprise_id == enterprise_id,
					PurchaseOrderMaterialDeliverySchedules.make_id == make_id,
					cast(PurchaseOrderMaterialDeliverySchedules.quantity, Integer) != 0)
			return pur_mat_query.first()
		except Exception:
			raise

	def getLatestPONo(self, financial_year=None, enterprise_id=None, po_type=None):
		financial_year = financial_year if financial_year else datetime.now().strftime('%Y')
		fy_latest_po_no = self.db_session.query(PurchaseOrder.po_no).filter(
			PurchaseOrder.financial_year == financial_year, PurchaseOrder.enterprise_id == enterprise_id,
			PurchaseOrder.type == po_type, PurchaseOrder.po_no.isnot(None)).order_by(
			cast(PurchaseOrder.po_no, Integer).desc()).first()
		po_no = 1 if fy_latest_po_no is None or not (
				'%s' % fy_latest_po_no.po_no).isdigit() else (
				int('%s' % fy_latest_po_no.po_no) + 1)
		logger.debug('Latest po no: %s' % po_no)
		return po_no

	def getLatestReceiptNo(self, financial_year=None, enterprise_id=None):
		financial_year = financial_year if financial_year else datetime.now().strftime('%Y')
		latest_receipt_no = self.db_session.query(Receipt.receipt_code).filter(
			Receipt.financial_year == financial_year, Receipt.enterprise_id == enterprise_id,
			Receipt.received_against == 'Issues', Receipt.receipt_code != '0').order_by(
			Receipt.approved_on.desc()).first()
		receipt_no = "1" if not ('%s' % latest_receipt_no).isdigit() or latest_receipt_no == "0" else int(
			'%s' % latest_receipt_no) + 1
		return receipt_no

	def getReceipt(self, enterprise_id=None, pl_id=None):
		"""
		enterprise_id and  pl_id
		:return:
		"""
		try:
			return self.db_session.query(Receipt).filter(
				Receipt.enterprise_id == enterprise_id, Receipt.receipt_no == pl_id).first()

		except Exception:
			raise

	def getReceiptMaterial(self, pl_id=None, item_id=None, enterprise_id=None, make_id=None):
		"""

		:return:
		"""
		try:
			receipt_mat_query = self.db_session.query(ReceiptMaterial).filter(
				ReceiptMaterial.receipt_no == pl_id, ReceiptMaterial.item_id == item_id,
				ReceiptMaterial.enterprise_id == enterprise_id, ReceiptMaterial.make_id == make_id)
			return receipt_mat_query.first()
		except Exception:
			raise

	def getDCDetails(self, pp_id=None, item_id=None, enterprise_id=None, make_id=None):
		"""

		:return:
		"""
		try:
			invoice_id_query = self.db_session.query(InvoiceMaterial.invoice_id).join(InvoiceMaterial.invoice).filter(
				Invoice.job_po_id == pp_id, Invoice.enterprise_id == enterprise_id,
				Invoice.status > 0, InvoiceMaterial.item_id == item_id, InvoiceMaterial.make_id == make_id)
			return invoice_id_query.first()
		except Exception:
			raise


class ProductionService:
	"""
	Service class that handles the backend business logic and other manipulations related to presentation for the various
	Production modules - Receipts, Indents, etc.
	"""

	def __init__(self):
		"""

		"""
		self.production_dao = ProductionDAO()

	def getOADetails(self, enterprise_id=None, from_date=None, to_date=None, party=None, mi_not_applicable=None):
		"""

		:param enterprise_id:
		:param from_date:
		:param to_date:
		:param party:
		:param mi_not_applicable:
		:return:
		"""
		party_condition = ""
		mi_not_applicable_condition = ""
		if party and party != '-1':
			party_condition = ' AND pa.party_id= %s' % party
		if mi_not_applicable != 'true':
			mi_not_applicable_condition = ' AND (oap.mi_not_applicable = 0 or oap.mi_not_applicable is NULL) '

		oa_date_condition = """ AND o.prepared_on BETWEEN '{from_date}' AND '{to_date}' """.format(
			from_date=from_date, to_date=to_date)
		indent_date_condition = """ AND ind.raised_date BETWEEN '{from_date}' AND '{to_date}' """.format(
			from_date=from_date, to_date=to_date)

		query = getOADetailsQuery.format(
			enterprise_id=enterprise_id, party_condition=party_condition,
			mi_not_applicable_condition=mi_not_applicable_condition, oa_date_condition=oa_date_condition,
			indent_date_condition=indent_date_condition, mi_condition='')
		oa_details = executeQuery(query, as_dict=True)
		for item in oa_details:
			item_name = item['item_name']
			item['grn_qty'] = 0
			if item['mi_id']:
				grn_qty = self.getManufactureIndentCompletedStatus(enterprise_id=enterprise_id, indent_no=item['mi_id'], item_id=item['item_id'])
				item['grn_qty'] = grn_qty
			make_name = helper.constructDifferentMakeName(item['make_name'])
			if make_name:
				item_name = item_name + " [" + make_name + "]"
			item['item_name'] = item_name
		return oa_details

	@staticmethod
	def getManufactureIndentList(enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		query = getManufactureIndentListQuery.format(enterprise_id=enterprise_id)
		manufacture_indent_details = executeQuery(query, as_dict=True)
		return manufacture_indent_details

	def getManufactureIndentCompletedStatus(self, enterprise_id=None, indent_no=None, item_id=None):
		query = getIndentCompletedStatusQuery.format(enterprise_id=enterprise_id, indent_no=indent_no, item_id=item_id)
		indent_status_details = executeQuery(query, as_dict=True)
		return indent_status_details[0] if indent_status_details and indent_status_details[0] else 0

	@staticmethod
	def getProductionPlanList(
			enterprise_id=None, from_date=None, to_date=None, status=None, timeliness=None, assigned_to=None, pp_type=None):
		"""

		:param enterprise_id:
		:param from_date:
		:param to_date:
		:param status:
		:param timeliness:
		:param assigned_to:
		:return:
		"""
		assigned_to_condition = ""
		if assigned_to and assigned_to != 'all':
			if pp_type == "internal":
				assigned_to_condition = " AND po.issue_to = '%s' " % assigned_to
			else:
				assigned_to_condition = " AND party_id = '%s' " % assigned_to
		status_condition = ""
		if int(status) == 1:
			status_condition = " WHERE status in ('Yet to Start') "
		elif int(status) == 2:
			status_condition = " WHERE status in ('In Progress') "
		elif int(status) == 3:
			status_condition = " WHERE status in ('Yet to Start', 'In Progress') "
		elif int(status) == 4:
			status_condition = " WHERE status in ('Completed') "
		elif int(status) == 5:
			status_condition = " WHERE status in ('Yet to Start', 'Completed') "
		elif int(status) == 6:
			status_condition = " WHERE status in ('In Progress', 'Completed') "

		timeliness_condition = ""
		if int(timeliness) == 1:
			if status_condition == "":
				timeliness_condition = " WHERE timeliness = 'Overdue' "
			else:
				timeliness_condition = " AND timeliness = 'Overdue' "
		elif int(timeliness) == 2:
			if status_condition == "":
				timeliness_condition = " WHERE timeliness = 'ON Time' "
			else:
				timeliness_condition = " AND timeliness = 'ON Time' "
		date_condition = "AND po.drafted_on BETWEEN '{from_date}' AND '{to_date}' {assigned_to_condition}".format(
			from_date=from_date, to_date=to_date, assigned_to_condition=assigned_to_condition)
		filter_condition = "{status_condition} {timeliness_condition}".format(
			status_condition=status_condition, timeliness_condition=timeliness_condition)
		po_id_condition = ""
		query = production_plan_staus_query.format(
			date_condition=date_condition, filter_condition=filter_condition, enterprise_id=enterprise_id,
			po_id_condition=po_id_condition)
		production_plan_details = executeQuery(query, as_dict=True)
		for item in production_plan_details:
			item_name = item['item_name']
			if item['make_name']:
				make_name = helper.constructDifferentMakeName(item['make_name'])
				if make_name:
					item_name = item_name + " [" + make_name + "]"
			item['item_name'] = item_name
		return production_plan_details

	@staticmethod
	def getMIMaterial(enterprise_id=None, mi_id=None):
		"""

		:param enterprise_id:
		:param mi_id:
		:return:
		"""
		query = getMIMaterialQuery.format(enterprise_id=enterprise_id, mi_id=mi_id)
		mi_material = executeQuery(query, as_dict=True)
		for item in mi_material:
			item_name = item['item_name']
			if item['make_name']:
				make_name = helper.constructDifferentMakeName(item['make_name'])
				if make_name:
					item_name = item_name + " [" + make_name + "]"
			item['item_name'] = item_name
		return mi_material

	@staticmethod
	def getProductionLog(enterprise_id=None, pp_id=None):
		"""

		:param enterprise_id:
		:param pp_id:
		:return:
		"""
		query = getProductionLogQuery.format(enterprise_id=enterprise_id, pp_id=pp_id)
		production_log = executeQuery(query, as_dict=True)
		return production_log

	def saveManufactureIndent(self, indent, user_id=None, enterprise_id=None):
		"""

		:param indent:
		:param user_id:
		:param enterprise_id:
		:return:
		"""
		self.production_dao.db_session.begin(subtransactions=True)
		try:
			fy_start_day = self.production_dao.db_session.query(Enterprise.fy_start_day).filter(
				Enterprise.id == enterprise_id).first()
			current_fy = getFinancialYear(for_date=datetime.now(), fy_start_day=fy_start_day[0])
			if 'mi_id' in indent and indent['mi_id'] and indent['mi_id'] != "" and indent['mi_id'] != "null":
				entity = self.production_dao.getManufactureIndent(indent_no=indent['mi_id'], enterprise_id=enterprise_id)
			elif 'oa_id' in indent and indent['oa_id'] and indent['oa_id'] != "":
				indent_no = self.production_dao.getManufactureIndentThroughOA(oa_id=indent['oa_id'], enterprise_id=enterprise_id)
				if indent_no:
					entity = self.production_dao.getManufactureIndent(indent_no=indent_no, enterprise_id=enterprise_id)
				else:
					entity = Indent()
					entity.financial_year = current_fy
					entity.indent_oa = OAIndentMap(oa_id=indent['oa_id'], indent_no=entity.indent_no, enterprise_id=enterprise_id)
			else:
				entity = Indent()
				entity.financial_year = current_fy
			indent_materials = self.constructIndentMaterials(entity=entity, indent=indent, enterprise_id=enterprise_id)
			entity.materials = indent_materials
			entity.purpose = str(indent['purpose']) if indent['purpose'] else ""
			entity.expected_date = indent['delivery_date']
			entity.indent_module_id = 1
			entity.enterprise_id = enterprise_id
			entity.modified_by = user_id
			entity.raised_by = user_id
			self.production_dao.saveIndent(entity)
			self.production_dao.db_session.commit()
			self.production_dao.db_session.refresh(entity)
			indent_dict = self.getManufactureIndentStatus(
				mi_id=entity.indent_no, item_id=indent['item_id'], make_id=indent['make_id'], enterprise_id=enterprise_id)
			grn_qty = self.getManufactureIndentCompletedStatus(enterprise_id=enterprise_id, indent_no=entity.indent_no,
															   item_id=indent['item_id'])
			indent_dict['grn_qty'] = grn_qty
			return indent_dict

		except Exception as e:
			self.production_dao.db_session.rollback()
			logger.exception("Saving Indent failed %s" % e.message)
		return False

	def constructIndentMaterials(self, entity=None, indent=None, enterprise_id=None):
		indent_materials = []
		try:
			indent_materials = self.production_dao.getIndentMaterials(indent_no=entity.indent_no, enterprise_id=enterprise_id)
			if not indent_materials:
				indent_material = IndentMaterial(
					indent_no=entity.indent_no, item_id=indent['item_id'], enterprise_id=enterprise_id,
					make_id=indent['make_id'])
			else:
				indent_material = self.production_dao.getIndentMaterial(
					indent_no=entity.indent_no, item_id=indent['item_id'], enterprise_id=enterprise_id, make_id=indent['make_id'])
				if not indent_material:
					if indent['indent_type'] == 'forecast':
						indent_materials = []
					indent_material = IndentMaterial(
						indent_no=entity.indent_no, item_id=indent['item_id'], enterprise_id=enterprise_id,
						make_id=indent['make_id'])
			if indent['alternate_unit_id'] and int(indent['alternate_unit_id']) != 0:
				scale_factor = helper.getScaleFactor(
					enterprise_id=enterprise_id, item_id=indent['item_id'], alternate_unit_id=indent['alternate_unit_id'])
				if scale_factor:
					indent_material.quantity = Decimal(indent['quantity']) * Decimal(scale_factor)
					indent_material.alternate_unit_id = indent['alternate_unit_id']
			else:
				indent_material.alternate_unit_id = None
				indent_material.quantity = indent['quantity']
			indent_materials.append(indent_material)
		except Exception as e:
			logger.exception("Construct Indent Material process is failed %s" % e.message)
		return indent_materials

	@staticmethod
	def removeManufactureIndent(indent=None, enterprise_id=None):
		"""

		:param indent:
		:param enterprise_id:
		:return:
		"""
		oa_service = OrderAcknowledgementService()
		oa_service.oa_dao.db_session.begin(subtransactions=True)
		try:
			oa_material = oa_service.oa_dao.getOAParticulars(
				oa_id=indent['oa_id'], item_id=indent['item_id'], make_id=indent['make_id'],
				enterprise_id=enterprise_id, is_faulty=0)
			oa_material.mi_not_applicable = 1 if indent['action'] == 'remove' else 0
			oa_service.oa_dao.db_session.add(oa_material)
			oa_service.oa_dao.db_session.commit()
			return True
		except Exception as e:
			oa_service.oa_dao.db_session.rollback()
			logger.exception("Set N/A flag to OA Material process is failed %s" % e.message)
		return False

	def saveProductionPlan(self, production_plan, user_id=None, enterprise_id=None):
		"""

		:param production_plan:
		:param user_id:
		:param enterprise_id:
		:return:
		"""
		try:
			purchase_materials = []
			self.production_dao.db_session.begin(subtransactions=True)
			po_type = ""
			if 'pp_id' in production_plan and production_plan['pp_id'] and production_plan['pp_id'] != "":
				entity = self.production_dao.getPurchaseOrder(enterprise_id=enterprise_id, po_id=production_plan['pp_id'])

			else:
				entity = PurchaseOrder()
				if 'pp_type' in production_plan and production_plan['pp_type'] is not None:
					po_type = 2 if production_plan['pp_type'] == "internal" else 1
				if po_type == 2:
					fy_start_day = self.production_dao.db_session.query(Enterprise.fy_start_day).filter(
						Enterprise.id == enterprise_id).first()
					current_fy = getFinancialYear(for_date=datetime.now(), fy_start_day=fy_start_day[0])
					entity.po_no = self.production_dao.getLatestPONo(
						financial_year=current_fy, enterprise_id=enterprise_id, po_type=po_type)
					entity.financial_year = current_fy
				else:
					entity.po_no = 0
				entity.quotation_ref_no = ""
				entity.type = po_type
				entity.project_code = AccountService().accounts_dao.getDefaultProjectID(enterprise_id=enterprise_id)
				entity.status = po_type if po_type == 2 else 0
				entity.closing_remarks = ""
				entity.purpose = ""
				entity.total = 0
				entity.user = user_id
				entity.approved_by = user_id
				entity.drafted_by = user_id
				entity.enterprise_id = enterprise_id
				entity.location_id = production_plan['location_id']
			material = self.production_dao.getPurchaseMaterial(
				po_id=entity.po_id, item_id=production_plan['item_id'], enterprise_id=enterprise_id,
				make_id=production_plan['make_id'])
			if not material:
				material = PurchaseOrderMaterial(
					po_id=entity.po_id, item_id=production_plan['item_id'], enterprise_id=enterprise_id,
					make_id=production_plan['make_id'])
			material.delivery_scedules = self.constructDeliverySchedule(
				entity=entity, production_plan=production_plan, enterprise_id=enterprise_id)
			material.quantity = production_plan['quantity']
			if po_type == 1:
				entity.supplier_id = production_plan['assigned_to']
				delivery_details = party_delivery_details(enterprise_id)
				entity.shipping_name = delivery_details['shipping_name']
				entity.shipping_address = delivery_details['shipping_address']
				material_price = MasterDAO().getCurrentMaterialSupplierPrice(
					item_id=production_plan['item_id'], enterprise_id=enterprise_id, supp_id=production_plan['assigned_to'],
					make_id=production_plan['make_id'], is_job=1)
				if material_price:
					material.rate = float(material_price.price)
				else:
					item = MasterDAO().getMaterialByItemId(item_id=production_plan['item_id'], enterprise_id=enterprise_id)
					material.rate = item.price if item else 0
					entity.total = Decimal(material.quantity) * Decimal(material.rate)
			else:
				entity.issue_to = production_plan['assigned_to']
			if production_plan['alternate_unit_id'] and int(production_plan['alternate_unit_id']) != 0:
				scale_factor = helper.getScaleFactor(
					enterprise_id=enterprise_id, item_id=production_plan['item_id'],
					alternate_unit_id=production_plan['alternate_unit_id'])
				if scale_factor:
					material.quantity = Decimal(production_plan['quantity']) * Decimal(scale_factor)
					if po_type == 1:
						material.rate = Decimal(material.rate) / Decimal(scale_factor)
						entity.total = Decimal(material.quantity) * Decimal(material.rate)
				material.alternate_unit_id = production_plan['alternate_unit_id']
			purchase_materials.append(material)
			entity.items = purchase_materials
			entity.indent_no = production_plan['mi_id']
			entity.delivery = production_plan['end_date']
			entity.last_modified_on = datetime.now()
			entity.last_modified_by = user_id
			entity.location_id = production_plan['location_id']
			self.production_dao.db_session.add(entity)
			self.production_dao.db_session.commit()
			pp_dict = self.getProductionPlanStatus(pp_id=entity.po_id, enterprise_id=enterprise_id)
			self.saveChangeLog(
				entity=entity, production_plan=production_plan, material=material, user_id=user_id, enterprise_id=enterprise_id)
			return pp_dict

		except Exception as e:
			self.production_dao.db_session.rollback()
			logger.exception("Saving production_plan failed %s" % e.message)
		return False

	def constructDeliverySchedule(self, entity=None, production_plan=None, enterprise_id=None):
		"""
		:param entity:
		:param production_plan:
		:param enterprise_id:
		"""
		delivery_schedules = []
		try:
			delivery_schedule_start_date = self.production_dao.getPurchaseMaterialDeliverySchedule(
				po_id=entity.po_id, item_id=production_plan['item_id'], enterprise_id=enterprise_id,
				make_id=production_plan['make_id'], start_date=True, due_date=production_plan['start_date'])
			if delivery_schedule_start_date:
				delivery_schedule_start_date.due_date = production_plan['start_date']
			else:
				delivery_schedule_start_date = PurchaseOrderMaterialDeliverySchedules(
					po_id=entity.po_id, item_id=production_plan['item_id'], enterprise_id=enterprise_id,
					make_id=production_plan['make_id'], quantity=0, due_date=production_plan['start_date'])
			delivery_schedules.append(delivery_schedule_start_date)

			delivery_schedule_end_date = self.production_dao.getPurchaseMaterialDeliverySchedule(
				po_id=entity.po_id, item_id=production_plan['item_id'], enterprise_id=enterprise_id,
				make_id=production_plan['make_id'], start_date=False, due_date=production_plan['end_date'])
			if delivery_schedule_end_date:
				delivery_schedule_end_date.due_date = production_plan['end_date']
			else:
				delivery_schedule_end_date = PurchaseOrderMaterialDeliverySchedules(
					po_id=entity.po_id, item_id=production_plan['item_id'], enterprise_id=enterprise_id,
					make_id=production_plan['make_id'], quantity=production_plan['quantity'], due_date=production_plan['end_date'])
			delivery_schedules.append(delivery_schedule_end_date)
		except Exception as e:
			logger.exception("Construct delivery schedule process is failed %s" % e.message)
		return delivery_schedules

	@staticmethod
	def saveChangeLog(entity=None, production_plan=None, material=None, user_id=None, enterprise_id=None):
		"""

		"""
		try:
			pp_to_be_saved = {}
			pp_materials = {}
			materials = []
			pp_to_be_saved["type"] = 2
			pp_to_be_saved["id"] = entity.po_id
			pp_to_be_saved["user"] = entity.user
			pp_to_be_saved["indent_no"] = entity.indent_no
			if entity.type == 2:
				pp_to_be_saved["status"] = 2
				pp_to_be_saved["issue_to"] = entity.issue_to
				pp_to_be_saved["supplier_id"] = None
			else:
				pp_to_be_saved["status"] = 0
				pp_to_be_saved["issue_to"] = None
				pp_to_be_saved["supplier_id"] = entity.supplier_id
			pp_to_be_saved["approved_on"] = entity.approved_on
			pp_to_be_saved["approved_by"] = entity.approved_by
			pp_to_be_saved["enterprise_id"] = entity.enterprise_id
			pp_to_be_saved["financial_year"] = entity.financial_year
			pp_to_be_saved["closing_remarks"] = entity.closing_remarks
			pp_to_be_saved["last_modified_on"] = entity.last_modified_on
			pp_to_be_saved["last_modified_by"] = entity.last_modified_by
			pp_to_be_saved["new_remarks"] = entity.remarks
			pp_materials["title"] = "Material"
			if material:
				pp_materials["drawing_name"] = production_plan["item_name"]
				pp_materials["quantity"] = float(production_plan["quantity"])
			materials.append(pp_materials)
			pp_to_be_saved["materials"] = materials
			PurchaseChangelog().queryInsert(user_id=user_id, enterprise_id=enterprise_id, data=pp_to_be_saved)
		except Exception as e:
			raise

	def saveProductionLog(self, production_log=None, enterprise_id=None, user=None, user_id=None, is_return=None):
		"""

		:param production_log:
		:param enterprise_id:
		:param user:
		:param user_id:
		:param is_return:
		:return:
		"""
		try:
			materials = []
			self.production_dao.db_session.begin(subtransactions=True)
			if 'pl_id' in production_log and production_log['pl_id'] and production_log['pl_id'] != "":
				entity = self.production_dao.getReceipt(enterprise_id=enterprise_id, pl_id=production_log['pl_id'])
			else:
				entity = Receipt()
				fy_start_day = self.production_dao.db_session.query(Enterprise.fy_start_day).filter(
					Enterprise.id == enterprise_id).first()
				current_fy = getFinancialYear(for_date=datetime.now(), fy_start_day=fy_start_day[0])
				entity.financial_year = current_fy
				entity.invoice_no = ""
				po_details = self.production_dao.getPurchaseOrder(enterprise_id=enterprise_id, po_id=production_log['pp_id'])
				if po_details.type == 1:
					entity.supplier_id = po_details.supplier_id
					entity.received_against = 'Job Work'
				elif po_details.type == 2:
					entity.supplier_id = None
					entity.received_against = 'Issues'
				entity.enterprise_id = enterprise_id
				entity.project_code = AccountService().accounts_dao.getDefaultProjectID(enterprise_id=enterprise_id)
			location_id = None
			for item in production_log['items']:
				location_id = item['location_id']
				material = self.production_dao.getReceiptMaterial(
					pl_id=entity.receipt_no, item_id=item['item_id'], enterprise_id=enterprise_id,
					make_id=item['make_id'])
				if not material:
					material = ReceiptMaterial(
						receipt_no=entity.receipt_no, item_id=item['item_id'], enterprise_id=enterprise_id,
						make_id=item['make_id'], po_no=production_log['pp_id'])
				material.quantity = item['quantity']
				material.received_qty = item['quantity']
				material.accepted_qty = item['quantity']
				material.location_id = item['location_id']
				if 'alternate_unit_id' in item:
					if item['alternate_unit_id'] and int(item['alternate_unit_id']) != 0:
						scale_factor = helper.getScaleFactor(
							enterprise_id=enterprise_id, item_id=item['item_id'],
							alternate_unit_id=item['alternate_unit_id'])
						if scale_factor:
							material.quantity = Decimal(item['quantity']) * Decimal(scale_factor)
							material.received_qty = Decimal(item['quantity']) * Decimal(scale_factor)
							material.accepted_qty = Decimal(item['quantity']) * Decimal(scale_factor)
						material.alternate_unit_id = item['alternate_unit_id']
				if is_return:
					dc_id = self.production_dao.getDCDetails(
						pp_id=production_log['pp_id'], item_id=item['item_id'], make_id=item['make_id'], enterprise_id=enterprise_id)
					if dc_id:
						material.dc_id = dc_id[0]
				materials.append(material)

			if 'mi_id' in production_log and production_log['mi_id'] and production_log['mi_id'] != "":
				indent = self.production_dao.getManufactureIndent(indent_no=production_log['mi_id'], enterprise_id=enterprise_id)
				if indent.indent_oa:
					oa_no = indent.indent_oa.oa.getCode()
					oa_date = indent.indent_oa.oa.approved_on
					remarks = "MI No:'%s',MI Date:'%s',OA No:'%s',OA Date:'%s',Created On: '%s'" % (
						production_log['mi_no'], production_log['mi_date'], oa_no, oa_date, datetime.now())
				else:
					remarks = "MI No:'%s',MI Date:'%s',Created On:'%s'" % (
						production_log['mi_no'], production_log['mi_date'], datetime.now())
				entity.updateRemarks(remarks=remarks, user=user.username)
			entity.items = materials
			entity.inspected_by = user.username
			entity.last_modified_by = user_id
			entity.location_id = location_id
			self.production_dao.db_session.add(entity)
			self.production_dao.db_session.commit()
			return True
		except Exception as e:
			self.production_dao.db_session.rollback()
			logger.exception("Save production log process is failed %s" % e.message)
		return False

	def shortCloseProductionPlan(self, production_plan=None, enterprise_id=None, user_id=None):
		"""

		:param production_plan:
		:param enterprise_id:
		:param user_id:
		:return:
		"""
		self.production_dao.db_session.begin(subtransactions=True)
		try:
			po_material = self.production_dao.getPurchaseMaterial(
				po_id=production_plan['pp_id'], item_id=production_plan['item_id'], make_id=production_plan['make_id'],
				enterprise_id=enterprise_id)
			po_material.quantity = production_plan['quantity']
			self.production_dao.db_session.add(po_material)
			self.production_dao.db_session.commit()
			purchase_service = PurchaseService()
			purchase_service.updateRemarks(
				po_id=production_plan['pp_id'], enterprise_id=enterprise_id, remarks=production_plan['remarks'], user_id=user_id)
			pp_status = ProductionService().getProductionPlanStatus(pp_id=production_plan['pp_id'], enterprise_id=enterprise_id)
			purchase = purchase_service.purchase_dao.getPurchaseOrder(enterprise_id=enterprise_id, po_id=production_plan['pp_id'])
			pp_to_be_saved = {}
			pp_materials = {}
			materials = []
			pp_to_be_saved["type"] = 2
			pp_to_be_saved["status"] = 2
			pp_to_be_saved["id"] = purchase.po_id
			pp_to_be_saved["user"] = user_id
			pp_to_be_saved["issue_to"] = purchase.issue_to
			pp_to_be_saved["indent_no"] = purchase.indent_no
			pp_to_be_saved["approved_on"] = purchase.approved_on
			pp_to_be_saved["approved_by"] = purchase.approved_by
			pp_to_be_saved["enterprise_id"] = purchase.enterprise_id
			pp_to_be_saved["financial_year"] = purchase.financial_year
			pp_to_be_saved["closing_remarks"] = purchase.closing_remarks
			pp_to_be_saved["last_modified_by"] = purchase.last_modified_by
			pp_to_be_saved["new_remarks"] = purchase.remarks
			if po_material:
				query_fetch_materials = """SELECT name from materials where id =  '{code}' """.format(
					code=production_plan['item_id'])
				material_details = executeQuery(query_fetch_materials)
				material_name = material_details[0][0]
				pp_materials["drawing_name"] = material_name
				pp_materials["quantity"] = float(production_plan["quantity"])
			materials.append(pp_materials)
			PurchaseChangelog().queryInsert(user_id=user_id, enterprise_id=enterprise_id, data=pp_to_be_saved)
			return pp_status
		except Exception as e:
			self.production_dao.db_session.rollback()
			logger.exception("Short close process for PP is failed %s" % e.message)
		return False

	@staticmethod
	def remarksProductionPlan(production_plan=None, enterprise_id=None, user_id=None):
		"""

		:param production_plan:
		:param enterprise_id:
		:param user_id:
		:return:
		"""
		purchase_service = PurchaseService()
		try:
			purchase_service.updateRemarks(
				po_id=production_plan['pp_id'], enterprise_id=enterprise_id, remarks=production_plan['remarks'], user_id=user_id)
			purchase_order = purchase_service.purchase_dao.getPurchaseOrder(
				enterprise_id=enterprise_id, po_id=production_plan['pp_id'])
			return purchase_order.remarks
		except Exception as e:
			logger.exception("Update Remarks process for PP is failed %s" % e.message)
		return False

	def instructionProductionPlan(self, production_plan=None, enterprise_id=None):
		"""

		:param production_plan:
		:param enterprise_id:
		:return:
		"""
		self.production_dao.db_session.begin(subtransactions=True)
		try:

			purchase_order = self.production_dao.getPurchaseOrder(
				enterprise_id=enterprise_id, po_id=production_plan['pp_id'])
			purchase_order.closing_remarks = production_plan['pp_instruction']
			self.production_dao.db_session.add(purchase_order)
			self.production_dao.db_session.commit()
			return True
		except Exception as e:
			self.production_dao.db_session.rollback()
			logger.exception("Update Instruction process for PP is failed %s" % e.message)
		return False

	def remarksManufactureIndent(self, indent_dict=None, enterprise_id=None, user=None):
		"""

		:param indent_dict:
		:param enterprise_id:
		:param user:
		:return:
		"""
		self.production_dao.db_session.begin(subtransactions=True)
		try:
			indent = self.production_dao.getManufactureIndent(indent_no=indent_dict['mi_id'], enterprise_id=enterprise_id)
			user = "%s %s" % (user.first_name.capitalize(), user.last_name.capitalize())
			indent.updateRemarks(remarks=indent_dict['remarks'], user=user)
			self.production_dao.db_session.add(indent)
			self.production_dao.db_session.commit()
			self.production_dao.db_session.refresh(indent)
			return indent.remarks
		except Exception as e:
			self.production_dao.db_session.rollback()
			logger.exception("Update Remarks process for MI is failed %s" % e.message)
		return False

	@staticmethod
	def getProductionPlanStatus(pp_id=None, enterprise_id=None):
		"""
		:param pp_id:
		:param enterprise_id:
		"""
		po_id_condition = " AND po.id = {pp_id} ".format(pp_id=pp_id)
		query = production_plan_staus_query.format(
			pp_id=pp_id, enterprise_id=enterprise_id, po_id_condition=po_id_condition, date_condition="", filter_condition="")
		production_plan_status = executeQuery(query, as_dict=True)
		return production_plan_status

	@staticmethod
	def getManufactureIndentStatus(mi_id=None, item_id=None, make_id=None, enterprise_id=None):
		"""
		:param mi_id:
		:param enterprise_id:
		:param item_id:
		:param make_id:
		"""
		mi_condition = " AND ind.indent_no = {mi_id}  AND im.item_id = {item_id} AND im.make_id= {make_id}".format(
			mi_id=mi_id, item_id=item_id, make_id=make_id)

		query = getOADetailsQuery.format(
			enterprise_id=enterprise_id, party_condition="", mi_not_applicable_condition="", oa_date_condition="",
			indent_date_condition="", mi_condition=mi_condition)
		manufacture_indent_status = executeQuery(query, as_dict=True)
		return manufacture_indent_status[0]

	def allocation_deallocation(self, allocated_materials=None, enterprise_id=None, user_id=None, status=None):
		"""
		Status codes: 1 = allocated, 0 = deallocated
		"""
		try:
			date = datetime.now()
			is_allocated = bool(int(status))
			new_item_list = []
			location_id = 0
			de_details = []
			self.production_dao.db_session.begin(subtransactions=True)
			materials = json.loads(allocated_materials)
			for item in materials:
				item_id = item.get("item_id")
				parent_id = item.get("parent_id")
				pp_id = item.get("pp_id")
				new_allocated_qty = float(item.get("allocated_qty"))
				required_qty = float(item.get("required_qty"))
				location_id = float(item.get("location_id"))
				is_expand = item.get("is_expanded")
				updated_by = user_id
				existing_qty = self.production_dao.db_session.query(MRP_Materials.allocated_qty).filter(
					MRP_Materials.item_id == item_id,
					MRP_Materials.pp_id == pp_id,
					MRP_Materials.enterprise_id == enterprise_id,
					(MRP_Materials.parent_id == parent_id) if parent_id else (MRP_Materials.parent_id == None)).first()
				if existing_qty:
					prev_qty = existing_qty[0]
				else:
					prev_qty = 0

				if new_allocated_qty > 0:
					new_item_list.append({"enterprise_id": enterprise_id, "item_id": item_id, "quantity": new_allocated_qty,
										  "is_faulty": 0})

				if is_allocated:
					allocated_qty = float(prev_qty) + float(new_allocated_qty)
				else:
					allocated_qty = float(prev_qty) - float(new_allocated_qty)
					if parent_id and is_expand == 1:
						de_details.append({"item_id":item_id,"parent_id":parent_id,"pp_id":pp_id})

				if item_id is not None and pp_id is not None:
					material = self.production_dao.db_session.query(MRP_Materials).filter_by(
						item_id=item_id, pp_id=pp_id, enterprise_id=enterprise_id, parent_id=parent_id).first()

					if material is not None:
						material.parent_id = parent_id
						material.allocated_qty = allocated_qty
						material.required_qty = required_qty
						material.updated_by = updated_by
						material.location_id = location_id
						material.updated_on = date
						material.is_expand = is_expand

					else:
						new_material = MRP_Materials(
							enterprise_id=enterprise_id,
							item_id=item_id,
							parent_id=parent_id,
							pp_id=pp_id,
							allocated_qty=allocated_qty,
							required_qty=required_qty,
							updated_on=date,
							updated_by=updated_by,
							location_id=location_id,
							is_expand=is_expand
						)
						self.production_dao.db_session.add(new_material)
			self.production_dao.db_session.commit()

			closing_stock_material_wise_queue_creator(item_list=new_item_list, is_sales=is_allocated, location_id=location_id)
			if is_allocated is False:
				if de_details:
					parent_id = de_details[0]["parent_id"]
					pp_id = de_details[0]["pp_id"]
					item_id = de_details[0]["item_id"]
					self.production_dao.db_session.query(MRP_Materials).filter_by(parent_id=parent_id, pp_id=pp_id).delete()
					self.production_dao.db_session.query(MRP_Materials).filter_by(item_id=parent_id, pp_id=pp_id).update({"is_expand": 0})
					self.production_dao.db_session.commit()

			return {"status": "success"}
		except Exception as e:
			self.production_dao.db_session.rollback()
			return {"status": "error", "message": str(e)}

		finally:
			self.production_dao.db_session.close()
