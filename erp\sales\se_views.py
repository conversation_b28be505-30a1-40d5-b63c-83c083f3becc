"""
"""
import json
from datetime import datetime
from decimal import Decimal

import jwt
import simplejson
from django.contrib import messages
from django.http import HttpResponseRedirect, HttpResponse
from django.shortcuts import render_to_response
from django.template.context import RequestContext
from django.template.response import TemplateResponse
from sqlalchemy import func

from erp import properties, dao
from erp.admin.backend import EnterpriseProfileService, UserDAO, PurchaseTemplateConfigService
from erp.auth import ENTERPRISE_IN_SESSION_KEY, SESSION_KEY, USER_IN_SESSION_KEY, ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.forms import SalesEstimateForm, SESearchForm
from erp.formsets import SEParticularsFormset, SETaxFormset
from erp.helper import populateMaterialChoices, populatePartyChoices, getUser, populateTaxChoices, getStateList
from erp.masters.backend import MasterService
from erp.models import SalesEstimate, <PERSON><PERSON><PERSON>cy, EnterpriseContactMap, SEContactMap
from erp.properties import TEMPLATE_TITLE_KEY
from erp.sales import logger
from erp.sales.backend import InvoiceService
from erp.sales.changelog import SalesEstimateChangelog
from erp.sales.se_backend import SalesEstimateVO, SE_FORM_PREFIX, SE_PARTICULARS_PREFIX, SalesEstimateService, \
	SE_TAX_PREFIX
from erp.tags import generateTagFormset
from settings import SQLASession, JWT_SECRET, CURRENT_VERSION, GCS_BUCKET_NAME
from util.api_util import JsonUtil, response_code

__author__ = 'benedict'

SALES_ESTIMATE_KEY = 'se'
SE_PK_FIELD_KEY = 'se_no'
SE_PARTICULAR_FORMSET_KEY = 'se_particular_formset'
SE_MATERIAL_POPULATE = 'material_list'
EDIT_SE_ID = 'se_edit_id'
PARTY_KEY = 'party'
SE_PURPOSE_FREQUENTS = 'frequents_purpose'
SE_TAG = 'tag'
SE_TAGS_FORMSET = 'tags_formset'
SE_LIST = 'se_list'
SE_TAX_LIST = 'tax_list'
SE_TAX_LOAD = 'load_tax'
SE_SEARCH = 'search'
SE_SALES_PERSON_LIST = 'sales_person_list'
MATERIAL_FORM_KEY = 'material_form'
BILL_OF_MATERIALS_FORMSET_KEY = 'bill_of_materials_formset'
SPECIFICATION_FORMSET_KEY = 'specifications_formset'
MATERIAL_MAKE_FORMSET_KEY = 'material_makes_formset'
PRICE_OF_SUPP_MATERIALS_FORMSET_KEY = 'materials_party_price_formset'
ALTERNATE_UNIT_FORMSET_KEY = 'alternate_unit_formset'


se_service = SalesEstimateService()


def manageSalesEstimatePage(request):
	request_handler = RequestHandler(request)
	master_service = MasterService()
	enterprise = request_handler.getSessionAttribute(ENTERPRISE_IN_SESSION_KEY)
	enterprise_id = enterprise.id
	gst_category = master_service.getGSTCategory()
	countries = master_service.getCountries()
	gst_category_list = []
	gst_country_list = []
	for category in gst_category:
		gst_category_list.append({"category_id": category.id, "category_name": category.name, "category_desc": category.description})

	for country in countries:
		gst_country_list.append({"country_code": country.code, "country_name": country.name})
	se_obj = SalesEstimate(
		prepared_by=request_handler.getSessionAttribute(SESSION_KEY), enterprise_id=enterprise.id,
		currency_id=enterprise.home_currency_id, prepared_on=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
	sales_estimate_vo = se_service.constructSalesEstimateVo(se_obj, enterprise)
	message = request_handler.setSessionAttribute("last_created_order", "")
	currency = SQLASession().query(Currency).order_by(Currency.code).all()
	logged_in_user = request.session[USER_IN_SESSION_KEY]
	access_level = logged_in_user.getAccessLevels("DEFAULT")

	query = """SELECT purpose, enterprise_id, count(1) as item_count from indents where enterprise_id= '%s'
					GROUP BY purpose, enterprise_id ORDER BY item_count DESC LIMIT 0,5""" % enterprise.id
	frequent_list = [item for item in dao.executeQuery(query)]
	frequent_list = [row[0] for row in frequent_list]

	enterprise_contacts = fetchEnterpriseContact(enterprise_id=enterprise.id)
	master_service = MasterService()
	material_vo = master_service.constructMaterialVO(enterprise_id=enterprise.id)

	se_id = request_handler.getPostData(SE_PK_FIELD_KEY)
	if se_id is None:
		template_config = PurchaseTemplateConfigService().purchase_template_dao.getTemplateConfig(
			enterprise_id=enterprise_id, collection='sales_estimate_template_config')
		template_misc_details = template_config["misc_config"]
		notes = se_obj.notes if se_obj.notes else template_misc_details["notes"]
		sales_estimate_vo = se_service.constructSalesEstimateVo(se_obj, default_notes=notes)
	else:
		notes = se_obj.notes if se_obj.notes else ""
		sales_estimate_vo = se_service.constructSalesEstimateVo(se_obj, default_notes=notes)
	return TemplateResponse(request=request, template=properties.MANAGE_SALES_ESTIMATE_TEMPLATE, context={
		SALES_ESTIMATE_KEY: sales_estimate_vo.sales_estimate_form, 'currency': currency,
		SE_PARTICULAR_FORMSET_KEY: sales_estimate_vo.se_particulars_formset,
		SE_MATERIAL_POPULATE: populateMaterialChoices(enterprise.id),
		PARTY_KEY: populatePartyChoices(enterprise.id), SE_PURPOSE_FREQUENTS: frequent_list,
		SE_TAX_LIST: sales_estimate_vo.se_tax_formset, 'enterprise_contacts_list': enterprise_contacts,
		SE_TAX_LOAD: populateTaxChoices(enterprise.id), "access_level": access_level,
		SE_TAGS_FORMSET: sales_estimate_vo.se_tag_formset, TEMPLATE_TITLE_KEY: "Sales Estimate",
		MATERIAL_FORM_KEY: material_vo.material_form,
		ALTERNATE_UNIT_FORMSET_KEY: material_vo.alternate_unit_formset,
		BILL_OF_MATERIALS_FORMSET_KEY: material_vo.material_formset,
		SPECIFICATION_FORMSET_KEY: material_vo.specification_formset,
		PRICE_OF_SUPP_MATERIALS_FORMSET_KEY: material_vo.supplier_price_material_formset,
		MATERIAL_MAKE_FORMSET_KEY: material_vo.material_make_formset,
		"GCS_BUCKET_NAME": GCS_BUCKET_NAME, "last_created_order": message if message else "",
		'gst_category_list': gst_category_list, 'gst_country_list': gst_country_list, 'state_list': getStateList()})


def saveSalesEstimate(request):
	logger.info("Save Sales Estimate Triggered...")
	request_handler = RequestHandler(request)
	try:
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		se_attachments = request_handler.getSessionAttribute('se_attachments')
		data = request_handler.getPostData()
		dict_data = dict(data.iterlists())
		logger.info("The Enterprise ID: %s" % enterprise_id)
		se_contact_list = dict_data['se_contact_id_list'] if 'se_contact_id_list' in dict_data.keys() else []
		change_as_draft = False
		if 'cmd_submit_for_approval' in dict_data.keys() and len(dict_data['cmd_submit_for_approval']) > 0 and dict_data['cmd_submit_for_approval'][0] != '':
			change_as_draft = True
		sales_estimate_vo = SalesEstimateVO(sales_estimate_form=SalesEstimateForm(
			data=data, prefix=SE_FORM_PREFIX, enterprise_id=enterprise_id),
			se_particulars_formset=SEParticularsFormset(data, prefix=SE_PARTICULARS_PREFIX),
			se_tax_formset=SETaxFormset(data, prefix=SE_TAX_PREFIX),
			se_tag_formset=generateTagFormset(data),
			se_contacts_list=se_contact_list)
		logger.info("The Sales Estimate Construction Completed...")
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		saved_sales_estimate_vo, se_id = se_service.saveSalesEstimate(
			sales_estimate_vo=sales_estimate_vo, enterprise_id=enterprise_id, user_id=user_id, change_as_draft=change_as_draft)

		se_service.saveSEAttachments(se_id=se_id, enterprise_id=enterprise_id, user_id=user_id, se_attachments=se_attachments)
		logger.info("The SaveSalesEstimate Event Triggered...")
		material_list = populateMaterialChoices(enterprise_id)

		messages.success(request, "Sales Estimate Saved Successfully....")
		request_handler.setSessionAttribute("last_created_order", "Sales Estimate has been saved successfully")
		if sales_estimate_vo.sales_estimate_form.cleaned_data['id'] is None:
			InvoiceService().notifyPendingSalesEstimateCount(enterprise_id=enterprise_id, sender_id=user_id)
	except Exception as e:
		logger.exception("Saving Sales Estimate Failed...\n%s" % e.message)
		raise
	finally:
		request_handler.removeSessionAttribute('se_attachments')
	if not saved_sales_estimate_vo.is_valid():
		return TemplateResponse(template=properties.MANAGE_SALES_ESTIMATE_TEMPLATE, request=request, context={
			SALES_ESTIMATE_KEY: saved_sales_estimate_vo.sales_estimate_form,
			SE_PARTICULAR_FORMSET_KEY: saved_sales_estimate_vo.se_particulars_formset,
			SE_MATERIAL_POPULATE: material_list,
			SE_TAX_LIST: saved_sales_estimate_vo.se_tax_formset,
			SE_TAX_LOAD: populateTaxChoices(enterprise_id),
			SE_TAGS_FORMSET: saved_sales_estimate_vo.se_tag_formset, "last_created_order": ""})
	return HttpResponseRedirect(properties.MANAGE_SALES_ESTIMATE_VIEW_URL)


def loadSalesEstimate(request):
	# Load Sales Estimate List
	try:
		logger.info("The Load Sales Estimate Triggered...")
		request_handler = RequestHandler(request)
		from_date, to_date = JsonUtil.getDateRange(
			rh=request_handler, since_session_key="se.since", till_session_key="se.till")
		party_id = request_handler.getPostData('party_name')
		status = request_handler.getPostData('status')
		project = request_handler.getPostData('project')
		request_handler.removeSessionAttribute('se_attachments')
		party_id = party_id if party_id else -1
		status = status if status else ""
		project = project if project else ""

		# Get the Enterprise Id
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		message = request_handler.getSessionAttribute("last_created_order")
		request_handler.setSessionAttribute("last_created_order", "")

		if request_handler.getData("view") == 'pending' and request_handler.getData("type") == 'se':
			se_initial_date = SQLASession().query(SalesEstimate.prepared_on).filter(
				SalesEstimate.enterprise_id == enterprise_id).order_by('prepared_on').first()
			from_date = se_initial_date[0].strftime('%Y-%m-%d')
			status = '0'
		return TemplateResponse(template=properties.MANAGE_SALES_ESTIMATE_LIST_TEMPLATE, request=request, context={
			SE_LIST: searchSalesEstimate(
				enterprise_id=enterprise_id, from_date=from_date, to_date=to_date,
				party_id=party_id, status=status, project=project),
			SE_TAX_LOAD: populateTaxChoices(enterprise_id),
			PARTY_KEY: populatePartyChoices(enterprise_id),
			SE_SEARCH: SESearchForm(enterprise_id=enterprise_id, initial={
				'to_date': to_date, 'from_date': from_date, 'party_name': party_id, 'project': project, 'status': status}),
			TEMPLATE_TITLE_KEY: "Sales Estimate", "last_created_order": message if message else ""})
	except Exception as e:
		logger.exception("Loading Sales Estimate Interrupted...\n%s" % e.message)
		raise


def searchSalesEstimate(enterprise_id=None, from_date=None, to_date=None, party_id=-1, status=None, project=None):
	logger.info("Enterprise Id : %s , From Date %s and Party id: %s Status: %s" % (
		enterprise_id, from_date, party_id, status))
	json_se = []
	condition = """sales_estimate.prepared_on between '%s' and '%s' and
		sales_estimate.enterprise_id = '%s' and sales_estimate.status %s %s 
		%s order by sales_estimate.prepared_on""" % (
		from_date, to_date, enterprise_id, " = '%s'" % status if status != '' else " >= -1",
		"" if int(party_id) == -1 else " and sales_estimate.party_id='%s'" % party_id,
		"" if project == 'None' or project == '' else " and sales_estimate.project_code='%s'" % project)
	query = SQLASession().query(SalesEstimate).filter(condition)
	se_list = query.all()
	for se in se_list:
		oa_value = se_service.sales_estimate_dao.getOATotal(enterprise_id=se.enterprise_id, se_id=se.id)
		invoice_value = se_service.sales_estimate_dao.getInvoiceTotal(enterprise_id=se.enterprise_id, se_id=se.id)
		se_item = dict()
		se_item['id'] = se.id
		se_item['enterprise_id'] = se.enterprise_id
		se_item['se_no'] = se.se_no
		se_item['code'] = se.getInternalCode()
		se_item['sub_number'] = se.sub_number
		se_item['financial_year'] = se.financial_year
		se_item['party_id'] = se.party_id
		se_item['customer'] = se.customer.name
		se_item['project_code'] = se.project_code
		se_item['status'] = se.status
		se_item['status_name'] = se.getStatusName()
		se_item['type'] = se.type
		se_item['currency_id'] = se.currency_id
		se_item['currency_conversion_rate'] = se.currency_conversion_rate
		se_item['grand_total'] = se.grand_total
		se_item['oa_value'] = oa_value[0] if oa_value else Decimal("%0.2f" % 0)
		se_item['inv_value'] = invoice_value[0] if invoice_value else Decimal("%0.2f" % 0)
		se_item['payment_terms'] = se.payment_terms
		se_item['remarks'] = se.remarks
		se_item['special_instructions'] = se.special_instructions
		se_item['round_off'] = se.round_off
		se_item['notes'] = se.notes
		se_item['ref_no'] = se.ref_no
		se_item['ref_date'] = se.ref_date
		se_item['purpose'] = se.purpose
		se_item['expiry_date'] = se.expiry_date
		se_item['prepared_by'] = se.prepared_by
		se_item['prepared_on'] = se.prepared_on
		se_item['last_modified_by'] = se.last_modified_by
		se_item['last_modified_on'] = se.last_modified_on
		se_item['approved_by'] = se.approved_by
		se_item['approved_on'] = se.approved_on
		se_item['super_modified_on'] = se.super_modified_on
		se_item['super_modified_by'] = se.super_modified_by
		se_item['client_revised_on'] = se.client_revised_on

		json_se.append(se_item)
	return json_se


def editSalesEstimate(request):
	try:
		request_handler = RequestHandler(request)
		enterprise = request_handler.getSessionAttribute(ENTERPRISE_IN_SESSION_KEY)
		enterprise_id = enterprise.id
		se_no_to_be_edited = request_handler.getPostData(SE_PK_FIELD_KEY)
		oa_total = 0
		invoice_total = 0
		request_handler.removeSessionAttribute('se_attachments')
		oa_details, inv_details = se_service.sales_estimate_dao.getOAInvoiceDetails(enterprise_id=enterprise_id, se_id=se_no_to_be_edited)
		se_to_edit = getSalesEstimate(se_id=se_no_to_be_edited, enterprise_id=enterprise_id)
		oa_value = se_service.sales_estimate_dao.getOATotal(enterprise_id=enterprise_id, se_id=se_no_to_be_edited)
		invoice_value = se_service.sales_estimate_dao.getInvoiceTotal(enterprise_id=enterprise_id, se_id=se_no_to_be_edited)
		if oa_value:
			oa_total = oa_value[0]
		if invoice_value:
			invoice_total = invoice_value[0]
		status = se_to_edit.status
		se_type = [(item[0], item[1], item[0][0]) for item in SalesEstimate.TYPE_CHOICES]
		enterprise_contacts = fetchEnterpriseContact(enterprise_id=enterprise_id)
		logged_in_user = request.session[USER_IN_SESSION_KEY]
		access_level = logged_in_user.getAccessLevels("DEFAULT")
		if se_to_edit:
			logger.debug("Sales Estimate To Edit : %s" % se_to_edit.getCode())

		sales_estimate_vo = se_service.constructSalesEstimateVo(se_to_edit, enterprise=enterprise, default_notes=se_to_edit.notes)
		material_list = populateMaterialChoices(enterprise_id)
		master_service = MasterService()
		material_vo = master_service.constructMaterialVO(enterprise_id=enterprise_id)
		se_contacts_list = []
		for contacts in sales_estimate_vo.se_contacts_list:
			se_contacts_list.append(contacts.contact_id)
		return TemplateResponse(template=properties.ADD_EDIT_SALES_ESTIMATE_TEMPLATE, request=request, context={
			SALES_ESTIMATE_KEY: sales_estimate_vo.sales_estimate_form,
			SE_PARTICULAR_FORMSET_KEY: sales_estimate_vo.se_particulars_formset,
			SE_MATERIAL_POPULATE: material_list, 'enterprise_contacts_list': enterprise_contacts,
			EDIT_SE_ID: se_no_to_be_edited, "access_level": access_level,
			SE_TAX_LIST: sales_estimate_vo.se_tax_formset,
			SE_TAX_LOAD: populateTaxChoices(enterprise_id),
			SE_TAGS_FORMSET: sales_estimate_vo.se_tag_formset,
			MATERIAL_FORM_KEY: material_vo.material_form,
			ALTERNATE_UNIT_FORMSET_KEY: material_vo.alternate_unit_formset,
			BILL_OF_MATERIALS_FORMSET_KEY: material_vo.material_formset,
			SPECIFICATION_FORMSET_KEY: material_vo.specification_formset,
			PRICE_OF_SUPP_MATERIALS_FORMSET_KEY: material_vo.supplier_price_material_formset,
			MATERIAL_MAKE_FORMSET_KEY: material_vo.material_make_formset,
			'remarks_list': json.dumps(sales_estimate_vo.remarks_list),
			'se_type': se_type, 'se_contacts_list': se_contacts_list, 'oa_total': oa_total, 'invoice_total': invoice_total,
			'oa_details': oa_details, 'inv_details': inv_details,
			'selected_project_code': se_to_edit.project_code if se_no_to_be_edited is not None else None,
			TEMPLATE_TITLE_KEY: se_to_edit.getInternalCode(), 'status': status})
	except Exception as e:
		logger.exception("Sales Estimate Rendering for Edit Failed...\n%s" % e.message)
		raise


def getSalesEstimate(se_id=None, enterprise_id=None):
	return SQLASession().query(SalesEstimate).filter(
		SalesEstimate.id == se_id, SalesEstimate.enterprise_id == enterprise_id).first()


def approveSalesEstimate(request):
	"""
	Assigns a permanent identification number for the Sales Estimate. Sets the Sales Estimate status as approved.
	:param request:
	:return: JSON dumps to the Ajax call that initiated this approval
	"""
	logger.info("Sales Estimate Approval Triggered...")
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getPostData('enterprise_id')
	se_attachments = request_handler.getSessionAttribute('se_attachments')
	se_id = request_handler.getPostData('se_id')
	user_id = request_handler.getPostData('user_id')
	remarks = request_handler.getPostData('approved_remarks')
	if user_id is None:
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
	if enterprise_id is None:
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		response = se_service.approveSalesEstimate(
			enterprise_id=enterprise_id, se_id=se_id, user_id=user_id, remarks=remarks, se_attachments=se_attachments)
		return HttpResponse(json.dumps(response), 'content-type=text/json')
	except Exception as e:
		logger.exception('Error in Sales Estimate Approval: %s' % e)
		response = response_code.internalError()
		response['error'] = "Approval Failed..."
		return HttpResponse(json.dumps(response), 'content-type=text/json')


def rejectSalesEstimate(request):
	"""
	Rejects

	:param request:
	:return:
	"""
	logger.debug('Rejecting a Sales Estimate...')
	request_handler = RequestHandler(request)
	se_id = request_handler.getPostData('se_id')
	enterprise_id = request_handler.getPostData('enterprise_id')
	user_id = request_handler.getPostData('user_id')
	rejection_remarks = request_handler.getPostData('remarks')
	logger.debug("Sales Estimate ID%s" % se_id)
	if user_id is None:
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
	if enterprise_id is None:
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		if request_handler.isSessionActive() and request_handler.isPostRequest():
			response = getRejectStatus(
				enterprise_id=enterprise_id, user_id=user_id, se_id=se_id,
				rejection_remarks=rejection_remarks, flag=True)
			return HttpResponse(simplejson.dumps(response))
	else:
		status = getRejectStatus(
			enterprise_id=enterprise_id, user_id=user_id, se_id=se_id,
			rejection_remarks=rejection_remarks, flag=False)
		if status == -1:
			response = response_code.failure()
		else:
			response = response_code.success()
		return HttpResponse(json.dumps(response), 'content-type=text/json')


def reviewSalesEstimate(request):
	"""
	Review

	:param request:
	:return:
	"""
	logger.info('Review a Sales Estimate...')
	db_session = SQLASession()
	request_handler = RequestHandler(request)
	user_id = request_handler.getPostData("user_id")
	if not user_id:
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
	db_session.begin(subtransactions=True)
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getPostData('enterprise_id')
		if enterprise_id is None:
			enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		reviewed_by = request_handler.getPostData("user_id")
		if reviewed_by is None:
			reviewed_by = request_handler.getSessionAttribute(SESSION_KEY)

		se_id = request_handler.getPostData('se_id')
		review_remarks = request_handler.getPostData('remarks')

		se_to_review = db_session.query(SalesEstimate).filter(
			SalesEstimate.id == se_id, SalesEstimate.enterprise_id == enterprise_id).first()
		se_to_review.status = SalesEstimate.STATUS_REVIEWED
		modifying_user = UserDAO().getUserById(enterprise_id=se_to_review.enterprise_id, user_id=user_id)
		se_to_review.updateRemarks(remarks=review_remarks, user=modifying_user)
		db_session.commit()
		se_service.notifySEReviewMessage(
			enterprise_id=se_to_review.enterprise_id, sender_id=reviewed_by, sales_estimate=se_to_review)
		response = response_code.success()
		response['se_no'] = str(se_to_review.se_no)
	except Exception as e:
		db_session.rollback()
		logger.exception("Review a SE failed... %s" % e.message)
		response = response_code.internalError()
		response['error'] = e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getRejectStatus(enterprise_id=None, user_id=None, se_id=None, rejection_remarks=None, flag=True):
	"""

	:return:
	"""
	db_session = SQLASession()
	try:
		db_session.begin(subtransactions=True)
		se_to_reject = db_session.query(SalesEstimate).filter(SalesEstimate.id == se_id).first()
		user = getUser(enterprise_id=enterprise_id, user_id=user_id)
		if se_to_reject.status == SalesEstimate.STATUS_DRAFT:
			logger.debug("Removing the Sales Estimate %s permanently" % se_id)
			se_to_reject.status = SalesEstimate.STATUS_REJECTED
			se_to_reject.updateRemarks(remarks="Rejection Remarks: %s" % rejection_remarks, user=user)
			db_session.commit()
			InvoiceService().notifySalesEstimateRejectCount(
				enterprise_id=enterprise_id, sender_id=user_id, sales_estimate=se_to_reject, code=se_to_reject.getInternalCode(),
				type=se_to_reject.type)
			return 0
		se_to_reject.status = SalesEstimate.STATUS_CANCELLED
		se_to_reject.updateRemarks(remarks="Cancelled Remarks: %s" % rejection_remarks, user=user)
		db_session.add(se_to_reject)
		db_session.commit()
		db_session.refresh(se_to_reject)
		SalesEstimateChangelog().queryInsert(
			user_id=user_id, enterprise_id=enterprise_id, data=se_to_reject)
		InvoiceService().notifySalesEstimateRejectCount(
			enterprise_id=enterprise_id, sender_id=user_id, sales_estimate=se_to_reject, code=se_to_reject.getInternalCode(),
			type=se_to_reject.type)
		return se_to_reject.getInternalCode()
	except Exception as e:
		db_session.rollback()
		logger.exception(e.message)
		if flag:
			return "Rejection Failed!!"
		else:
			return -1


def fetchEnterpriseContact(enterprise_id=None):
	try:
		contact_list = []
		enterprise_contact_details = SQLASession().query(
			EnterpriseContactMap).filter(EnterpriseContactMap.enterprise_id == enterprise_id).order_by(
			EnterpriseContactMap.sequence_id).all()

		logger.info("%s contacts available for the enterprise_id %s" % (len(enterprise_contact_details), enterprise_id))

		for enterprise_contact_map in enterprise_contact_details:
			contact_detail = {
				"name": enterprise_contact_map.contact.name, "contact_id": enterprise_contact_map.contact_id}
			contact_list.append(contact_detail)
		return contact_list
	except Exception as e:
		logger.exception("Fetching Sales person failed - %s" % e.message)
		raise


def fetchSEContact(enterprise_id=None):
	try:
		contact_list = []
		se_contact_details = SQLASession().query(
			SEContactMap).filter(SEContactMap.enterprise_id == enterprise_id).order_by(
			SEContactMap.sequence_id).all()

		logger.info("%s sales contacts available for the enterprise_id %s" % (len(se_contact_details), enterprise_id))

		for se_contact_map in se_contact_details:
			contact_detail = {
				"name": se_contact_map.contact.name, "contact_id": se_contact_map.contact_id}
			contact_list.append(contact_detail)
		return contact_list
	except Exception as e:
		logger.exception("Fetching Sales person failed - %s" % e.message)
		raise


def saveSalesEstimateContacts(request):
	logger.info("Save Sales Estimate contacts Triggered...")
	try:
		request_handler = RequestHandler(request)
		service = EnterpriseProfileService()
		enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		data = request_handler.getPostData()
		sequence_id = SQLASession().query(func.max(EnterpriseContactMap.sequence_id)).filter(
				EnterpriseContactMap.enterprise_id == enterprise_id).first()
		contact_exist = service.validateSalesPerson(
			enterprise_id=enterprise_id, name=data['name'], email=data['email'], phone_no=data['phone_no'], fax_no=data['fax_no'])
		if contact_exist is False:
			contact_details = service.updateEnterpriseContactDetails(
				enterprise_id=enterprise_id, sequence_id=sequence_id[0] + 1, name=data['name'], email=data['email'],
				phone_no=data['phone_no'], fax_no=data['fax_no'], is_whatsapp=0, user_id=user_id)
			service.updateEnterpriseContactMap(
				contact_id=contact_details.id, enterprise_id=enterprise_id, sequence_id=sequence_id[0] + 1)
			response = response_code.success()
			response['custom_message'] = "Enterprise Profile updated successfully"
			response['contact_id'] = contact_details.id
			response['contact_name'] = data['name']
		else:
			response = response_code.failure()
			response['custom_message'] = "Sales person already exist"
	except Exception as e:
		logger.exception("Sales estimate contact Update failed - %s" % e.message)
		response = response_code.internalError()
		response['exception'] = "%s" % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def updateSalesEstimateClientStatus(request):
	"""
	Assigns a permanent identification number for the Sales Estimate. Sets the Sales Estimate status as approved.
	:param request:
	:return: JSON dumps to the Ajax call that initiated this client approval
	"""
	logger.info("Sales Estimate Client Status Update Triggered...")
	response = response_code.success()
	try:
		token = None
		status = ""
		time_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
		if 'ase' in request.GET:
			token = request.GET['ase']
		if 'status' in request.GET:
			status = request.GET['status']
		if token:
			decoded_token = jwt.decode(token, JWT_SECRET)
			se_id = decoded_token['se_id']
			se_no = decoded_token['se_no']
			if decoded_token['expired_on'] >= time_now:
				sales_estimate = se_service.sales_estimate_dao.getSalesEstimate(se_id=se_id)
				if sales_estimate.status == 3 or sales_estimate.status == 4:
					message = "Sales Estimate - %s has already been %s on %s." % (
						se_no, "approved" if sales_estimate.status == 3 else "rejected", sales_estimate.client_revised_on)
					return render_to_response(
						properties.SALES_ESTIMATE_STATUS_TEMPLATE, {"message": message, "current_version": CURRENT_VERSION},
						context_instance=RequestContext(request))
				else:
					if status == 'approve':
						response = se_service.clientStatusSalesEstimate(
							se_id=se_id, client_status=SalesEstimate.STATUS_CLIENT_APPROVED, user_id=sales_estimate.modifier.id,
							enterprise_id=sales_estimate.enterprise_id)
						if response['response_message'] == "Success":
							message = "Sales Estimate %s has been approved" % se_no
						else:
							message = "Approval of Sales Estimate %s has failed" % se_no
						return render_to_response(
							properties.SALES_ESTIMATE_STATUS_TEMPLATE, {"message": message, "current_version": CURRENT_VERSION},
							context_instance=RequestContext(request))
					else:
						response = se_service.clientStatusSalesEstimate(
							se_id=se_id, client_status=SalesEstimate.STATUS_CLIENT_Rejected,  user_id=sales_estimate.modifier.id,
							enterprise_id=sales_estimate.enterprise_id)
						if response['response_message'] == "Success":
							message = "Sales Estimate %s has been rejected" % se_no
						else:
							message = "Rejection of Sales Estimate %s has failed" % se_no
						return render_to_response(
							properties.SALES_ESTIMATE_STATUS_TEMPLATE, {"message": message, "current_version": CURRENT_VERSION},
							context_instance=RequestContext(request))
			else:
				message = "Sales Estimate - %s has been expired on %s." % (se_no, decoded_token['expired_on'])
				return render_to_response(
					properties.SALES_ESTIMATE_STATUS_TEMPLATE, {"message": message, "current_version": CURRENT_VERSION},
					context_instance=RequestContext(request))
	except Exception as e:
		logger.exception('Error in Sales Estimate Client Status update: %s' % e)
		response = response_code.internalError()
		response['error'] = "Client Status Update Failed..."


def updateSEStatusforClient(request):
	"""
	Assigns a permanent identification number for the Sales Estimate. Sets the Sales Estimate client status as approved.
	:param request:
	:return: JSON dumps to the Ajax call that initiated this approval
	"""
	logger.info("Sales Estimate Client Status Update Triggered...")
	request_handler = RequestHandler(request)
	se_id = request_handler.getPostData('se_id')
	status = request_handler.getPostData('status')
	user_id = request_handler.getPostData('user_id')
	if user_id is None:
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
	remarks = request_handler.getPostData('remarks')
	sales_estimate = se_service.sales_estimate_dao.getSalesEstimate(se_id=se_id)
	try:
		response = response_code.success()
		if sales_estimate.status == 3 or sales_estimate.status == 4:
			message = "Sales Estimate - %s has already been %s on %s." % (
				sales_estimate.getCode(), "approved" if sales_estimate.status == 3 else "rejected",
				sales_estimate.client_revised_on)
		else:
			if status == 'approve':
				response = se_service.clientStatusSalesEstimate(
					se_id=se_id, client_status=SalesEstimate.STATUS_CLIENT_APPROVED, user_id=user_id,
					enterprise_id=sales_estimate.enterprise_id, remarks=remarks)
				if response['response_message'] == "Success":
					message = "Sales Estimate %s has been approved" % sales_estimate.getCode()
				else:
					message = "Approval of Sales Estimate %s has failed" % sales_estimate.getCode()
			else:
				response = se_service.clientStatusSalesEstimate(
					se_id=se_id, client_status=SalesEstimate.STATUS_CLIENT_Rejected,  user_id=user_id,
					enterprise_id=sales_estimate.enterprise_id, remarks=remarks)
				if response['response_message'] == "Success":
					message = "Sales Estimate %s has been rejected" % sales_estimate.getCode()
				else:
					message = "Rejection of Sales Estimate %s has failed" % sales_estimate.getCode()
		response['custom_message'] = message
		response['code'] = sales_estimate.getCode()
		return HttpResponse(json.dumps(response), 'content-type=text/json')
	except Exception as e:
		logger.exception('Error in Sales Estimate Client Status update: %s' % e)
		response = response_code.internalError()
		response['error'] = "Client Status Update Failed..."
		return HttpResponse(json.dumps(response), 'content-type=text/json')
