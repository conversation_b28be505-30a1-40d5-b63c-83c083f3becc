{% extends 'admin/sidebar.html' %}
{% block enterprise %}
<style>
    li.supplier_side_menu a {
        outline: none;
        background-color: #e6983c !important;
    }
    .left-form-group .custom-error-message,
    .tr-file-upload .custom-error-message {
        position: relative;
        display: block;
    }
    .left-form-group .row div {
        margin-bottom: 15px;
    }
    .edit_fixed td {
        background: #ccffcc;
    }
    .editable-textbox {
        background: transparent;
        border: none;
        border-bottom: 2px solid #004195;
        color: #004195;
        outline: none;
        padding: 1px;
        width: 100%;
    }
    input[disabled] {
        cursor: not-allowed;
        opacity: 0.4;
    }
    .edit_fixed td {
        padding: 4px 8px !important;
    }
    .esc-info-message {
        color: #a94442;
        background-color: #f2dede;
        border-color: #ebccd1;
        position: fixed;
        right: 60px;
        bottom: 59px;
        font-size: 25px;
        padding: 25px;
        border-radius: 5px;
        z-index: 10;
    }
    #importsupplier .custom-table-large td {
        font-size: 11px !important;
    }
    .tr-file-upload .bootstrap-filestyle {
        width: 600px;
        float: left;
    }
    .outer {
        max-width: 1000px;
        max-height: 300px;
        height: 100vh;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }
    .inner {
        flex: 1;
        overflow-y: scroll;
    }
    .create_location {
        border-radius: 50px !important;
        border-right: 1px solid #ccc;
    }
    .create_add_container {
        margin-right: 15px;
    }
    .create_add_container a.btn-new-item {
        box-shadow: -4px 4px 5px 0 rgba(60, 64, 67, 0.302), 0 1px 3px 1px rgba(60, 64, 67, 0.149);
    }
    .import_csv.btn-new-item {
        border-radius: 0 50px 50px 0;
    }
    .btn-new-item-label {
        color: #004195;
        padding: 6px !important;
    }
    .create_location:hover,
    .import_csv:hover {
        background: rgba(32, 155, 225, 0.1);
        box-shadow: -4px 4px 5px 0 rgba(60, 64, 67, 0.302), 0 1px 3px 1px rgba(60, 64, 67, 0.149) !important;
    }
    .show_save_button {
        display: none !important;
    }
</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>

<div class="right-content-container">
    <div class="page-title-container">
        <span class="page-title">Location</span>
    </div>
    <input type="hidden" value="0" id="is_edit_enabled">
    <div class="page-heading_new">
        <input type="hidden" class="agent" id="agent" name="agent" value="{{ agent }}" />
            <div class="create_add_container" style="height:55px;">
                {% if logged_in_user.is_super %}
                <a id="a-add-location" href="/erp/masters/location/" class="btn btn-new-item pull-right create_location" data-tooltip="tooltip" title="Add New Location">
                    <i class="fa fa-plus" aria-hidden="true"></i>
                    <span class="btn-new-item-label"></span>
                </a>
                <script type="text/javascript">$("#is_edit_enabled").val(1);</script>
                        {% endif %}
            </div>
        <a href="/erp/masters/catalogues/" id="a-back-location" class="btn btn-add-new pull-right view_material hide" data-tooltip="tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
    </div>
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <div class="content_bg">
                    <div class="col-sm-12 table-with-upload-button">
                        <div class='esc-info-message' style="display: none;">
                            <span>Press esc to cancel the edit</span>
                        </div>
                        <div class="csv_export_button">
                            <a role="button" id="a-export-location" class="btn btn-add-new pull-right export_csv export_location" onclick="GeneralExportTableToCSV.apply(this, [$('#locationTable'), 'Location_Details.csv']);" data-tooltip="tooltip" title="Download Location List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
                        </div>
                        <table class="table table-bordered custom-table table-striped" id="locationTable" style="width: 100%;">
                            <thead>
                                <tr>
                                    <th>S.No</th>
                                    <th style="width: 70px;min-width: 70px;">Code</th>
                                    <th>Name</th>
                                    <th>Address</th>
                                    <th style="width: 100px;">Phone No</th>
                                    <th style="width: 100px;">Email</th>
                                    <th style="width: 100px;">Fax</th>
                                    <th style="width: 80px;">City</th>
                                    <th style="width: 80px;">State</th>
                                    <th style="width: 80px;">Country</th>
                                    <th style="width: 80px;">Pin Code</th>
                                </tr>
                            </thead>
                            <tbody>
                            {% for rec in data %}
                                <tr bgcolor="#ECECEC" align="center">
                                    <td class="text-center td_sno" style="width: 6%;">{{forloop.counter}}</td>
                                    <td>{{ rec.code }}</td>
                                    <td class="td-party-name text-left">
                                        <span style="display: inline-block;">
                                            <a role="button" class="edit_link_code"
                                               onclick="editLocationRow('{{ rec.id }}', '{{ rec.enterprise_id }}', '{{ rec.name }}', '{{ rec.phone_no }}', '{{ rec.email }}', '{{ rec.city }}', '{{ rec.state }}', '{{ rec.country }}', '{{ rec.pin_code }}', '{{ rec.fax }}', '{{ rec.gst_label }}', '{{ rec.address1 }}', '{{ rec.code }}', '_self','{{ rec.contact_person }}','{{rec.is_default}}')">
                                                {{ rec.name }}
                                            </a>
                                        </span>
                                        {%if rec.is_default %}<sup style="font-size:10px">(Default)</sup>{% endif %}
                                        <span style="width: 140px; display: inline-block; float: right; text-align: right;">
                                            <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='View'
                                                  onclick="editLocationRow('{{ rec.id }}', '{{ rec.enterprise_id }}', '{{ rec.name }}', '{{ rec.phone_no }}', '{{ rec.email }}', '{{ rec.city }}', '{{ rec.state }}', '{{ rec.country }}', '{{ rec.pin_code }}', '{{ rec.fax }}', '{{ rec.gst_label }}', '{{ rec.address1 }}', '{{ rec.code }}', '_self','{{ rec.contact_person }}','{{rec.is_default}}')">
                                                <i class='fa fa-pencil'></i>
                                            </span>
                                            <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='View in New Tab'
                                                  onclick="editLocationRow('{{ rec.id }}', '{{ rec.enterprise_id }}', '{{ rec.name }}', '{{ rec.phone_no }}', '{{ rec.email }}', '{{ rec.city }}', '{{ rec.state }}', '{{ rec.country }}', '{{ rec.pin_code }}', '{{ rec.fax }}', '{{ rec.gst_label }}', '{{ rec.address1 }}', '{{ rec.code }}', '_blank','{{ rec.contact_person }}','{{rec.is_default}}')">
                                                <i class='fa fa-external-link'></i>
                                            </span>
                                        </span>
                                    </td>

                                    <td width="150px">{{ rec.address1 }}</td>
                                    <td>{{ rec.phone_no }}</td>
                                    <td>{{ rec.email }}</td>
                                    <td>{{ rec.fax }}</td>
                                    <td>{{ rec.city }}</td>
                                    <td id="stateCode{{ forloop.counter }}"> {{ rec.state }}</td>
                                    <td id="countryCode{{ forloop.counter }}"></td>
                                    <td>{{ rec.pin_code }}</td>
                                </tr>
                            {% endfor %}
<!--                            {% if data|length == 0 %}-->
<!--							<tr><td colspan="11" align="center"><b>No records found</b></td></tr>-->
<!--							{% endif %}-->
                            </tbody>
                        </table>
                    </div>
                    <div class="clearfix"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="">
<form id="location_edit_form" method="post" action="/erp/masters/location/">
    {% csrf_token %}
    <input type="hidden" id="edit_location_id" name="location_id" value="" />
    <input type="hidden" id="enterprise_id" name="enterprise_id" value="102" />
    <input type="hidden" id="name" name="name" value="" />
    <input type="hidden" id="phone_no" name="phone_no" value="" />
    <input type="hidden" id="email" name="email" value="" />
    <input type="hidden" id="city" name="city" value="" />
    <input type="hidden" id="state" name="state" value="" />
    <input type="hidden" id="country" name="country" value="" />
    <input type="hidden" id="pin_code" name="pin_code" value="" />
    <input type="hidden" id="fax" name="fax" value="" />
    <input type="hidden" id="gst_label" name="gst_label" value="" />
    <input type="hidden" id="address1" name="address1" value="" />
    <input type="hidden" id="code" name="code" value="" />
     <input type="hidden" id="is_default" name="is_default" value="" />
    <input type="hidden" id="contact_person" name="contact_person" value="" />
</form>


</div>
<script>
$(document).ready(function(){
   {% for rec in data %}
        var countryDetails = "{{ rec.country }}";
        var countryCode = countryDetails.split(',')[1];
        $('#countryCode{{ forloop.counter }}').html(countryCode);
        var stateDetails = "{{ rec.state }}";
        if(stateDetails.length >1){
        stateCode = stateDetails.split(',')[1];
        }
        if(stateDetails.length  == 1){
        stateCode = stateDetails;
        }
        $('#stateCode{{ forloop.counter }}').html(stateCode);
    {% endfor %}
	$('.nav-pills li').removeClass('active');
	$("#li_material_report").addClass('active');
	TableHeaderFixed();
});

$(window).load(function(){
	updateFilterText();
});

function updateFilterText() {

}

var oTable;
var oSettings;
function TableHeaderFixed(){
	oTable = $('#locationTable').DataTable({
		fixedHeader: false,
        "pageLength": 50,
        "scrollY": Number($(document).height() - 230),
        "scrollX": true,
		"pageLength": 50,
		"search": {
			"smart": false
		},
		"columns": [
			null,null,null,
			null,null,null,null,null,null,null,null
			]
	});

	oTable.on("draw",function() {
		var keyword = $('#locationTable_filter > label:eq(0) > input').val();
		$('#locationTable').unmark();
		$('#locationTable').mark(keyword,{});
		listTableHoverIconsInit('locationTable');
	});
	oTable.on('page.dt', function() {
	  $('html, body').animate({
		scrollTop: $(".dataTables_wrapper").offset().top - 15
	   }, 'slow');
	   listTableHoverIconsInit('locationTable');
	});
	oSettings = oTable.settings();
	 listTableHoverIconsInit('locationTable');
	$( window ).resize();
}

function editLocationRow(locationId,enterprise_id,name,phone_no,email,city,state,country,pin_code,fax,gst_label,address1,code,openTarget,contact_person,is_default) {
    $("#edit_location_id").val(locationId);
    $("#enterprise_id").val(enterprise_id);
    $("#name").val(name);
    $("#phone_no").val(phone_no);
    $("#email").val(email);
    $("#city").val(city);
    $("#state").val(state);
    $("#country").val(country);
    $("#pin_code").val(pin_code);
    $("#fax").val(fax);
    $("#gst_label").val(gst_label);
    $("#address1").val(address1);
    $("#code").val(code);
    $("#contact_person").val(contact_person);
    $("#is_default").val(is_default);
    $("#location_edit_form").attr("target", openTarget).submit();
}

</script>
{% endblock %}
