<!DOCTYPE html>
<style type="text/css">
	body{
		color: #000;
		font-family: 'Times New Roman';
	}
	.rejection_table{
		text-align:center;
		font-weight:bold;
	}

	@font-face {
     font-family: 'Times New Roman';
       font-style: normal;
      font-weight: 400;
       src: url(https://www.xserp.in/site_media/fonts/times.ttf) format('truetype');
	}
</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/bootstrap.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">

<table style="width: 100% !important;">
	<tbody>
		<style>
		sup {
			line-height: 1.6;
		}
		</style>
		<tr>
			<td>
				<div>
					<h6 class="rejection_table" style="font-size:11pt;">QUALITY INSPECTION REPORT for <i>{{ source.getCode }}</i></h6>
					{% for key, value in qir_data.items %}
						{% if value|length > 0 %}
						<b style="font-size: {{summary_res.hsn_tax_font_size}}pt;">{{key}}</b>
						<table border=1 bordercolor="#A9A9A9" class="table qir_table row-seperator column-seperator" style="width: 100% !important;">
							{% for data in value %}
								<tr>
									{% for cell_data in data %}
									<td {% if forloop.counter == 1 %}style="font-weight: bold; width: 20%;" class="text-left"{% else %}style="font-weight: normal; word-break: break-word; width: 16%" class="text-right"{% endif %}>
										{{ cell_data }}</td>
									{% endfor %}
								</tr>
							{% endfor %}
						</table>
						{% endif %}
					{% endfor %}
				</div>
			</td>
		</tr>
	</tbody>
</table>
