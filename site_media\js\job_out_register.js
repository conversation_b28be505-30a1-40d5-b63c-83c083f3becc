var onPageLoad = function() {
    $("#id-search_isr").click(function () {
        jobInRegisterReport();
    });
}

function TableHeaderFixed() {
    oTable = $('#tbl_job_out_register_report').DataTable({
        fixedHeader: false,
        "pageLength": 50,
        "scrollY": Number($(document).height() - 230),
        "scrollX": true,
        "orderCellsTop": true,
        "search": {
            "smart": false
        }

    });
    oTable.on("draw",function() {
        var keyword = $('#tbl_job_out_register_report_filter > label:eq(0) > input').val();
        $('#tbl_job_out_register_report').unmark();
        $('#tbl_job_out_register_report').mark(keyword,{});
    });
    oTable.on('page.dt', function() {
      $('html, body').animate({
        scrollTop: $(".dataTables_wrapper").offset().top - 15
       }, 'slow');
    });
    oSettings = oTable.settings();
    $( window ).resize();
}


function loadAllMaterials(itemId, makeId, isFaulty) {
    var dataToSend = {
        'type': $("#id_invoice-type option:selected").val(),
        'party_id': $("#id_invoice-party_id option:selected").val(),
        'module': 'invoice',
        'particulars':'invoice_materials'
    }
    generateMaterialAsComboBox(itemId, makeId, isFaulty, dataToSend)
    jobInRegisterReport();
}

function jobInRegisterReport() {
    $('#loading').show();

    var material = $('#id-material').val().split("[::]");
    var [itemId, makeId, isFaulty] = $('#id-material').val().split("[::]");
    var searchCriteria = {
        since: $('.fromdate').val(), till: $('.todate').val(),
        party_id: $('#id-party_id').val(),
        item_id: itemId, make_id: makeId, is_faulty: isFaulty
    };
    $.ajax({
        url: "/erp/stores/json/job_out_register/",
        type: "post",
        dataType: "json",
        data: searchCriteria,
        success: function(response) {
            $('#tbl_job_out_register_report').DataTable().clear();
            $('#tbl_job_out_register_report').DataTable().destroy();
            $("#tbl_job_out_register_report tbody").find("tr").remove();
            $.each(response.report, function (key, value) {
                constructReportRow(key, value);
            });
            TableHeaderFixed();
            updateFilterText();
            closeFilterOption();
            $('#loading').hide();
        }, error: function(xhr,errmsg,err) {
            $('#loading').hide();
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });
}

/**
 *
 */
function constructReportRow(key, value) {
    if(value.oa_date !="") {
        setOADateFormat = formatDate(value.oa_date)
    }
    else {
        var setOADateFormat="";
    }
    if(value.dc_date !="") {
        setDCDateFormat = formatDate(value.dc_date)
    }
    else {
        var setDCDateFormat="";
    }
    if(value.grn_date !="") {
        setGRNDateFormat = formatDate(value.grn_date)
    }
    else {
        var setGRNDateFormat="";
    }
    if(value.party_inv_date !="") {
        setINVDateFormat = formatDate(value.party_inv_date)
    }
    else {
        var setINVDateFormat="";
    }
    var row = `<tr bgcolor="#ececec" border="0" align="center" style="font-size:16px; font-weight:normal;">
        <td class="text-left"> ${value.oa_no} </td>
        <td class="text-center"> ${setOADateFormat} </td>
        <td class="text-left"> ${value.dc_no} </td>
        <td class="text-center"> ${setDCDateFormat} </td>
        <td class="text-left"> ${value.party_name} </td>
        <td class="text-left"> ${value.material_name} </td>
        <td> ${value.unit} </td>
        <td class="text-right"> ${value.issued} </td>
        <td class="text-right"> ${value.received} </td>
        <td class="text-left"> ${value.grn_no} </td>
        <td class="text-center"> ${setGRNDateFormat}</td>
        <td class="text-left"> ${value.party_inv_no} </td>
        <td class="text-center"> ${setINVDateFormat}</td></tr>`;
    $('#tbl_job_out_register_report').append(row);
}

function formatDate(change_date){
    var setDateTime = change_date.split(' ');
    var setDateFormat = setDateTime[0].split('-');
    setDateFormat = setDateFormat[1]+"/"+setDateFormat[2]+"/"+setDateFormat[0];
    setDateFormat = moment(setDateFormat).format('MMM D, YYYY')
    return setDateFormat
}