
from concurrent.futures import ThreadPoolExecutor

from migration.backend import ExportService

values = [{'enterprise_id':102, 'from_date':'2020-04-01', 'to_date':'2021-04-01'}, {'enterprise_id':103, 'from_date':'2020-04-01', 'to_date':'2021-04-01'}, {'enterprise_id':104, 'from_date':'2020-04-01', 'to_date':'2021-04-01'}]


def square(entrprise_id=None):
	return entrprise_id


def test_generateXML(data):
	export_service = ExportService()
	enterprise_id = data['enterprise_id']
	from_date = data['from_date']
	to_date = data['to_date']
	xml = export_service.generateTallyXML(enterprise_id=enterprise_id, from_date=from_date, to_date=to_date)
	return xml


def main():
	with ThreadPoolExecutor(max_workers=3) as executor:
		results = executor.map(test_generateXML, values)
	for result in results:
		print(result)


if __name__ == '__main__':
	main()
