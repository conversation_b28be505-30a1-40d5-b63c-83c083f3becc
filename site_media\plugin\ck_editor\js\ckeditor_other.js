﻿/**
 * Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see LICENSE.md or http://ckeditor.com/license
 */

/* exported initSample */

if ( CKEDITOR.env.ie && CKEDITOR.env.version < 9 )
	CKEDITOR.tools.enableHtml5Elements( document );

// The trick to keep the editor in the sample quite small
// unless user specified own height.
CKEDITOR.config.height = 150;
CKEDITOR.config.width = 'auto';

var RichTextEditor = ( function() {
	var wysiwygareaAvailable = isWysiwygareaAvailable(),
		isBBCodeBuiltIn = !!CKEDITOR.plugins.get( 'bbcode' );

	return function() {
		var editorElement = CKEDITOR.document.getById( 'purchase_editor' );

		// :(((
		if ( isBBCodeBuiltIn ) {
			editorElement.setHtml(
				'Hello world!\n\n' +
				'I\'m an instance of [url=http://ckeditor.com]CKEditor[/url].'
			);
		}

		// Depending on the wysiwygare plugin availability initialize classic or inline editor.
		if ( wysiwygareaAvailable ) {
			CKEDITOR.replace( 'purchase_editor' );
		} else {
			editorElement.setAttribute( 'contenteditable', 'true' );
			CKEDITOR.inline( 'purchase_editor' );

			// TODO we can consider displaying some info box that
			// without wysiwygarea the classic editor may not work.
		}
		
		var exciseRulesElement = CKEDITOR.document.getById( 'exc_rules_editor' );

		// :(((
		if ( isBBCodeBuiltIn ) {
			exciseRulesElement.setHtml(
				'Hello world!\n\n' +
				'I\'m an instance of [url=http://ckeditor.com]CKEditor[/url].'
			);
		}

		// Depending on the wysiwygare plugin availability initialize classic or inline editor.
		if ( wysiwygareaAvailable ) {
			CKEDITOR.replace( 'exc_rules_editor' );
		} else {
			exciseRulesElement.setAttribute( 'contenteditable', 'true' );
			CKEDITOR.inline( 'exc_rules_editor' );

			// TODO we can consider displaying some info box that
			// without wysiwygarea the classic editor may not work.
		}
		
		var exciseNotes1Element = CKEDITOR.document.getById( 'exc_notes1' );

		// :(((
		if ( isBBCodeBuiltIn ) {
			exciseNotes1Element.setHtml(
				'Hello world!\n\n' +
				'I\'m an instance of [url=http://ckeditor.com]CKEditor[/url].'
			);
		}

		// Depending on the wysiwygare plugin availability initialize classic or inline editor.
		if ( wysiwygareaAvailable ) {
			CKEDITOR.replace( 'exc_notes1' );
		} else {
			exciseNotes1Element.setAttribute( 'contenteditable', 'true' );
			CKEDITOR.inline( 'exc_notes1' );

			// TODO we can consider displaying some info box that
			// without wysiwygarea the classic editor may not work.
		}
		var exciseNotes2Element = CKEDITOR.document.getById( 'exc_notes2' );

		// :(((
		if ( isBBCodeBuiltIn ) {
			exciseNotes2Element.setHtml(
				'Hello world!\n\n' +
				'I\'m an instance of [url=http://ckeditor.com]CKEditor[/url].'
			);
		}

		// Depending on the wysiwygare plugin availability initialize classic or inline editor.
		if ( wysiwygareaAvailable ) {
			CKEDITOR.replace( 'exc_notes2' );
		} else {
			exciseNotes2Element.setAttribute( 'contenteditable', 'true' );
			CKEDITOR.inline( 'exc_notes2' );

			// TODO we can consider displaying some info box that
			// without wysiwygarea the classic editor may not work.
		}
		var exciseNotes3Element = CKEDITOR.document.getById( 'exc_notes3' );

		// :(((
		if ( isBBCodeBuiltIn ) {
			exciseNotes3Element.setHtml(
				'Hello world!\n\n' +
				'I\'m an instance of [url=http://ckeditor.com]CKEditor[/url].'
			);
		}

		// Depending on the wysiwygare plugin availability initialize classic or inline editor.
		if ( wysiwygareaAvailable ) {
			CKEDITOR.replace( 'exc_notes3' );
		} else {
			exciseNotes3Element.setAttribute( 'contenteditable', 'true' );
			CKEDITOR.inline( 'exc_notes3' );

			// TODO we can consider displaying some info box that
			// without wysiwygarea the classic editor may not work.
		}
		var nonExciseNotes3Element = CKEDITOR.document.getById( 'non_exc_notes3' );

		// :(((
		if ( isBBCodeBuiltIn ) {
			nonExciseNotes3Element.setHtml(
				'Hello world!\n\n' +
				'I\'m an instance of [url=http://ckeditor.com]CKEditor[/url].'
			);
		}

		// Depending on the wysiwygare plugin availability initialize classic or inline editor.
		if ( wysiwygareaAvailable ) {
			CKEDITOR.replace( 'non_exc_notes3' );
		} else {
			nonExciseNotes3Element.setAttribute( 'contenteditable', 'true' );
			CKEDITOR.inline( 'non_exc_notes3' );

			// TODO we can consider displaying some info box that
			// without wysiwygarea the classic editor may not work.
		}
		
		var nonExciseNotes2Element = CKEDITOR.document.getById( 'non_exc_notes2' );

		// :(((
		if ( isBBCodeBuiltIn ) {
			nonExciseNotes2Element.setHtml(
				'Hello world!\n\n' +
				'I\'m an instance of [url=http://ckeditor.com]CKEditor[/url].'
			);
		}

		// Depending on the wysiwygare plugin availability initialize classic or inline editor.
		if ( wysiwygareaAvailable ) {
			CKEDITOR.replace( 'non_exc_notes2' );
		} else {
			nonExciseNotes2Element.setAttribute( 'contenteditable', 'true' );
			CKEDITOR.inline( 'non_exc_notes2' );

			// TODO we can consider displaying some info box that
			// without wysiwygarea the classic editor may not work.
		}
		
		var nonExciseNotes1Element = CKEDITOR.document.getById( 'non_exc_notes1' );

		// :(((
		if ( isBBCodeBuiltIn ) {
			nonExciseNotes1Element.setHtml(
				'Hello world!\n\n' +
				'I\'m an instance of [url=http://ckeditor.com]CKEditor[/url].'
			);
		}

		// Depending on the wysiwygare plugin availability initialize classic or inline editor.
		if ( wysiwygareaAvailable ) {
			CKEDITOR.replace( 'non_exc_notes1' );
		} else {
			nonExciseNotes1Element.setAttribute( 'contenteditable', 'true' );
			CKEDITOR.inline( 'non_exc_notes1' );

			// TODO we can consider displaying some info box that
			// without wysiwygarea the classic editor may not work.
		}
	};

	function isWysiwygareaAvailable() {
		// If in development mode, then the wysiwygarea must be available.
		// Split REV into two strings so builder does not replace it :D.
		if ( CKEDITOR.revision == ( '%RE' + 'V%' ) ) {
			return true;
		}

		return !!CKEDITOR.plugins.get( 'wysiwygarea' );
	}
} )();

