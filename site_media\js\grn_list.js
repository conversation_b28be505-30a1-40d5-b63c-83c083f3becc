function tableHeaderFixedGrn() {
    oTable = $('#grnList').DataTable({
    fixedHeader: false,
    "scrollY": Number($(document).height() - 230),
    "scrollX": true,
    "pageLength": 50,
    "search": {
        "smart": false
    },
    "columns": [
        null,
        { "type": "date" },
        null,null,null,null,
        null,null,
        { "type": "date" },
        null,null,null,null
        ]
    });
    updateGrnListJson();
    oTable.on("draw",function() {
        var keyword = $('#grnList_filter > label:eq(0) > input').val();
        $('#grnList').unmark();
        $('#grnList').mark(keyword,{});
        updateGrnListJson();
        $( window ).resize();
        setHeightForTable();
        listTableHoverIconsInit('grnList');
    });
    oTable.on('page.dt', function() {
      $('html, body').animate({
        scrollTop: $(".dataTables_wrapper").offset().top - 15
       }, 'slow');
       listTableHoverIconsInit('grnList');
    });
    oSettings = oTable.settings();
    updateFilterText();
    closeFilterOption();
    listTableHoverIconsInit('grnList');
    $( window ).resize();
}

function updateGrnListJson() {
    setTimeout(function() {
        receiptIdList = [];
        var rows = oTable.rows( { page : 'current'} ).data();
        for(var i = 0; i < rows.length; i++) {
            var selectedRow = $(rows[i][3]);
            var selectedIDRow = $(rows[i][2]);
            grn = {}
            grn ["grnId"] = selectedIDRow.text().trim();
            grn ["grnNumber"] = selectedRow.text().trim();
            receiptIdList.push(grn);
        }
        var storageKey = 'grnListNav';
        if ($("title").text().trim() == "Internal Receipt") {
            storageKey = 'irnListNav';
        } else if ($("title").text().trim() == "Sales Return") {
            storageKey = 'srListNav';
        }
        localStorage.setItem(storageKey, JSON.stringify(receiptIdList));

    }, 10);
}

function getURLParameter(url, name) {
    return (RegExp(name + '=' + '(.+?)(&|$)').exec(url) || [, null])[1];
}

function updateExcludeListForExport() {
	var receivedAgainst = $("#rec_against").val();
    $("#grnList thead").find("th").not(".for-all").addClass("hide exclude_export");
    if(receivedAgainst == "Others" || receivedAgainst == "All") {
        $("#grnList thead").find("th.for-others").removeClass("hide exclude_export");
        $("#grnList thead").find("th.for-all").removeClass("hide exclude_export");
    } else if(receivedAgainst == "Issues") {
        $("#grnList thead").find("th.for-ir").removeClass("hide exclude_export");
    } else {
        $("#grnList thead").find("th.for-grn").removeClass("hide exclude_export");
        $("#grnList thead").find("th.for-sr").removeClass("hide exclude_export");
    }
    $('#po_details_delete').val("");
}

function initListPage() {
	if(getURLParameter(window.location.search,'view') == 'pending') {
	    if (["Issues", "Sales Return"].indexOf($("#rec_against").val()) == -1) {
            $("#rec_against").val("All")
            $("#rec_against").trigger("chosen:updated");
		}
	}

	$("#loadgrns").click(function() {
        setTimeout(function(){
            $(".filter-components-container").find(".dropdown").removeClass("open");
            $(".filter-components-container").find(".dropdown-menu").removeClass("show");
        },100);
	    if(oTable != undefined) {
            oTable.destroy();
        }
        $("#grnList tbody").html("");
        loadGrnList();
        updateExcludeListForExport();
    });
}

function editReceipt(receipt_no, openTarget="") {
    $("#id-edit_receipt_no").val(receipt_no);
    $("#id-edit_receipt_form").attr("target", openTarget).submit();
}

function loadGrnList(limit=500, offset=0, loop=true, nos=0) {
    const OFFSETVAL = 500;
    var receivedAgainst = $("#rec_against").val();
    sessionStorage.setItem("limit", limit);
    sessionStorage.setItem("offset", offset);
    $('#loading').hide();
    var searchCriteria = {
        receipt_title: $("title").text().trim(),
        received_against: receivedAgainst,
        since: $("#fromdate").val(),
        till: $("#todate").val(),
        status: $("#status").val(),
        limit: limit,
        offset: offset
        };
//	$('#loading').show();
    $("#loadingmessage").removeClass('hide');
	$.ajax({
		url: "/erp/stores/json/grn/loadgrnlist/",
		type: "post",
		datatype:"json",
		data: searchCriteria,
		success: function(response){
            if(response.response_message == "Session Timeout") {
                location.reload();
                return;
            }
			$.each(response, function(i, item) {
			    nos = nos + 1;
			    if(item['status'] == 0){
		            grn_status = "Draft";
                    grn_code = item['grn_no'];
                } else if(item['status'] == 4){
                    grn_status = "Returned";
                    grn_code = item['code'];
                } else if(item['status'] == -1){
                    grn_status = "Rejected";
                    grn_code = "<s>" + item['code'] + "</s>";
                } else if(item['status'] > 0){
                    grn_status = "Approved";
                    grn_code = item['code'];
                } else {
                    grn_status = "Invalid";
                    grn_code = item['grn_no'];
                }
				var gDate = item['grn_date'];
				gDate = moment(gDate, "DD/MM/YYYY").format("MMM D, YYYY");
				if(gDate.toLowerCase().trim() == "invalid date") {
                    gDate = '-';
                }
                // CSRF_TOKEN required while submitting the request form generated below
                var csrf_token = document.getElementsByName('csrfmiddlewaretoken')[0].value;

                var edit = `<a role='button' class='edit_link_code' onclick='editReceipt(${item['grn_no']});'>${grn_code}</a>`;
                var draft_no = `<a role='button' onclick='editReceipt(${item['grn_no']});'>${item['grn_no']}</a>`;
                var status=`<input name='receipt_no' value='${item['grn_no']}' class='grn_id_in_class' hidden />
                            <a role='button' class='table-inline-icon-bg ${grn_status.toLowerCase()} pdf_genereate'>${grn_status}</a>
                            <span class='table-inline-icon-container'>
                                <span class='table-inline-icon inline-icon-document hide' data-tooltip='tooltip' data-placement='left' title='Preview' onclick="generatePdfFromAjax('${item['grn_no']}', '${item['status']}', '${item['supplier_id']}', '${receivedAgainst}');">
                                    <i class='fa fa-file-text'></i>
                                </span>
                                <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit' onclick='editReceipt(${item['grn_no']});'>
                                    <i class='fa fa-pencil'></i>
                                </span>
                                <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit in New Tab' onclick='editReceipt(${item['grn_no']}, "_blank");'>
                                    <i class='fa fa-external-link'></i>
                                </span>
                            </span>`;
			    if(["Purchase Order", "Job Work", "Job In", "Delivery Challan", "Sales Return"].indexOf(receivedAgainst) != -1) {
                    var dcDate = item['inv_date'];
                    dcDate = moment(dcDate, "DD/MM/YYYY").format("MMM D, YYYY");
                    if(dcDate.toLowerCase().trim() == "invalid date") {
                        dcDate = '-';
                    }
		            var row = ` <tr data-grnid='${item['grn_no']}'>
			                        <td class='text-center'>${Number(nos)}.</td>
                                    <td class='text-center'>${gDate}</td>
                                    <td hidden='hidden' class='exclude_export'>${draft_no}</td>
                                    <td class='text-center'>${edit}</td>
                                    <td hidden='hidden' class='exclude_export'>
                                        <span data-tooltip='tooltip' data-placement='left' title='${item.reference_docs}'>
                			                ${item.reference_docs}
                                        </span>
                                    </td>
                                    <td class='text-left'>${item['project']}</td>
                                    <td class='text-left'>${item['supplier']}</td>
                                    <td data-sort="${item['inv_no'].replace(/\D/g,'')}">${item.inv_no}</td>
                                    <td class='text-center'>${dcDate}</td>
                                    <td class='text-right'>${Number(item['inv_value']).toFixed(2)}</td>
                                    <td hidden='hidden' class='exclude_export'>${item['material']}</td>
                                    <td hidden='hidden' class='exclude_export'>${item['accepted_qty']}</td>
                                    <td class='text-center td_status'>${status}</td>
                                </tr>`;
                                console.log(row)
		            $('#grnList tbody').append(row);
			    } 
                else if(receivedAgainst == "Issues") {
			        var supplier = item['supplier_name'] == null ? (item['supplier'] == null ? 'Internal':item['supplier']):item['supplier_name'];
		            var row = ` <tr data-grnid='${item['grn_no']}'>
                                    <td class='text-center'>${Number(nos)}.</td>
                                    <td class='text-center'>${gDate}</td>
                                    <td hidden='hidden' class='exclude_export'>${draft_no}</td>
                                    <td class='text-center'>${edit}</td>
                                    <td hidden='hidden' class='exclude_export'>
                                        <span data-tooltip='tooltip' data-placement='left' title='${item.reference_docs}'>
                                            ${item.reference_docs}
                                        </span>
                                    </td>
                                    <td>${item['project']}</td>
                                    <td>${supplier}</td>
                                    <td hidden='hidden' class='exclude_export'>${item['inv_no']}</td>
                                    <td hidden='hidden' class='exclude_export'>${item['inv_date']}</td>
                                    <td hidden='hidden' class='exclude_export'>${item['inv_value']}</td>
                                    <td>${item['material']}</td>
                                    <td class='text-right'>${item['accepted_qty']}</td>
                                    <td class='text-center td_status'>${status}</td>
                                </tr>`;
                    $('#grnList tbody').append(row);
			    } 
                else {
                    console.log(3)
		            var row = `<tr data-grnid='${item['grn_no']}'>
                                    <td class='text-center'>${Number(nos)}.</td>
                                    <td class='text-center'>${gDate}</td>
                                    <td hidden='hidden' class='exclude_export'>${draft_no}</td>
                                    <td class='text-center'>${edit}</td>
                                    <td hidden='hidden' class='exclude_export'></td>
                                    <td>${item['project']}</td>
                                    <td>${item['supplier']}</td>
                                    <td hidden='hidden' class='exclude_export'>${item['inv_no']}</td>
                                    <td hidden='hidden' class='exclude_export'>${item['inv_date']}</td>
                                    <td hidden='hidden' class='exclude_export'>${item['inv_value']}</td>
                                    <td>${item['material']}</td>
                                    <td class='text-right'>${item['accepted_qty']}</td>
                                    <td class='text-center td_status'>${status}</td>
                                </tr>`;
                    $('#grnList tbody').append(row);
			    }

			});
			if(response.length >= OFFSETVAL){
                limit = ("limit" in sessionStorage ? sessionStorage.getItem("limit") : OFFSETVAL);
                offset = ("offset" in sessionStorage ? sessionStorage.getItem("offset") : 0);
                limit = parseInt(limit);
                offset = parseInt(offset)+OFFSETVAL;
                sessionStorage.setItem("limit", limit);
                sessionStorage.setItem("offset", offset);
                setTimeout(function(){
                loadGrnList(limit=limit, offset=offset, loop=true, nos=nos);
                }, 100);
            }else{
                loop = false;
                $("#loadingmessage_count").addClass('hide');
                $("#loadingmessage").addClass('hide');
                updateExcludeListForExport();
//                $('#loading').hide();
                tableHeaderFixedGrn();
                TooltipInit();
            }

		},
        error: function (xhr, errmsg, err) {
            console.log(xhr.status + ": " + xhr.responseText);
        }
	});
}