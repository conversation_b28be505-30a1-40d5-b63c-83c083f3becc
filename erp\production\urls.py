"""
"""
from django.conf.urls import patterns, url
from erp.production.views import manageManufactureIndents, manageProductionPlan, getProductionPlanList, \
	getManufactureIndents, getOADetails, getMIMaterial, getIssueTo, saveManufactureIndent, saveProductionPlan, \
	removeManufactureIndent, getProductionLog, saveProductionLog, shortCloseProductionPlan, remarksProductionPlan, \
	getProductionPlan, remarksManufactureIndent, instructionProductionPlan, allocated_material_quantity

__author__ = 'saravanan'

urlpatterns = patterns(
	'',
	url('manufacturing_indent/$', manageManufactureIndents),
	url('save_manufacture_indent/$', saveManufactureIndent),
	url('manufacture_indent_remove/$', removeManufactureIndent),
	url('production_plan/$', manageProductionPlan),
	url('get_production_plan_list/$', getProductionPlanList),
	url('get_manufacture_indents/$', getManufactureIndents),
	url('get_oa_details/$', getOADetails),
	url('get_prod_plan/$', getProductionPlan),
	url('get_prod_log/$', getProductionLog),
	url('get_mi_material/$', getMIMaterial),
	url('get_issue_to/$', getIssueTo),
	url('save_prod_plan/$', saveProductionPlan),
	url('save_prod_log/$', saveProductionLog),
	url('short_close_prod_plan/$', shortCloseProductionPlan),
	url('remarks_prod_plan/$', remarksProductionPlan),
	url('instruction_prod_plan/$', instructionProductionPlan),
	url('remarks_manufacture_indent/$', remarksManufactureIndent),
	# allocated_material_quantity
	url('json/allocated_material_quantity/$', allocated_material_quantity),
)
