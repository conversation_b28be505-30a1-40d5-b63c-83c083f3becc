import os
import logging
from sqlalchemy import and_

from erp.accounts.backend import AccountService
from erp.dao import DataAccessObject, executeQuery
from erp.helper import getConsolidatedMaterial, getAccountGroupIDs
from erp.masters.backend import createPartyLedgers

from erp.models import Voucher, VoucherParticulars, LedgerBill, Ledger, LedgerBillSettlement, Project, Invoice, \
    InvoiceMaterial, AccountGroup, PartyLedgerMap, Party, Receipt
from erp.accounts import SUNDRY_DEBTORS_GROUP_ID, SUNDRY_CREDITORS_GROUP_ID, OTHER_CURRENT_LIABILITIES, \
    OTHER_CURRENT_ASSETS, CASH_VOUCHER, BANK_VOUCHER, NOTE_VOUCHER, PURCHASE_VOUCHER, GENERAL_VOUCHER, SALES_VOUCHER
from util import LOG_HANDLER, LEDGER_GROUP_MAPPING, VALID_GROUP_NAMES, CASH_OR_BANK, PROJECT_CLONE_STATUS, VALID_BUDGET_GROUP_NAMES

# Set up logger
Voucher_logger = logging.getLogger(os.path.splitext(os.path.basename(__file__))[0])
Voucher_logger.setLevel(logging.DEBUG)
Voucher_logger.addHandler(LOG_HANDLER)


class VoucherService:
    """
    Serves Financial Accounts related services like - Statement generation, Book Closure, etc.
    """

    def __init__(self):
        self.dao = DataAccessObject()

    def clone_sales_or_purchase_voucher(self, voucher_id, enterprise_id, project_code=None, _db_session=None):
        """
        The Function get the existing voucher object using the voucher_id.
        creates the new Vouchers using the that object data in projects.
        Params: voucher_id
        """
        if not _db_session:
            _db_session = self.dao.db_session
        try:
            is_debit = None
            party_is_debit = None
            ledger_id = None
            _lb = None
            _party_name = None
            _group_id = None
            _group_parent_id = None
            _group_name = None
            is_sales_or_purchase = False
            _voucher = _db_session.query(Voucher).filter_by(id=voucher_id, enterprise_id=enterprise_id).first()
            Voucher_logger.info("_voucher:%s" % _voucher)
            project_code = project_code if project_code else _voucher.project_code
            project_query_set = _db_session.query(Project).filter_by(id=project_code).first()
            Voucher_logger.info("project_query_set:%s" % project_query_set)
            project_enterprise_id = project_query_set.project_enterprise_id
            if project_enterprise_id and project_enterprise_id != _voucher.enterprise_id:
                _db_session.begin(subtransactions=True)
                voucher = Voucher(
                    enterprise_id=project_enterprise_id, voucher_date=_voucher.voucher_date,
                    voucher_no=_voucher.voucher_no, type_id=_voucher.type_id, narration=_voucher.narration,
                    created_by=_voucher.created_by, created_on=_voucher.created_on, status=_voucher.status,
                    financial_year=_voucher.financial_year, project_code=project_code, parent_voucher_id=_voucher.id)
                Voucher_logger.info("Voucher objects:%s" % voucher)
                i = 0
                amount = 0
                party_id = None
                voucher_particulars = []
                for particular in _voucher.particulars:
                    if particular.ledger.group_id in (SUNDRY_DEBTORS_GROUP_ID, SUNDRY_CREDITORS_GROUP_ID):
                        party_is_debit = particular.is_debit
                        party_id = self.getPartyIdByPrimaryEnterpriseId(primary_enterprise_id=_voucher.enterprise_id,
                                                                        project_enterprise_id=project_enterprise_id)
                        is_sales_or_purchase = True
                        if not party_id:
                            party_name, party_code = self.getParentEnterpriseParty(enterprise_id=_voucher.enterprise_id)
                            party_id = self.createPartyToProjectEnterprise(
                                project_enterprise_id=project_enterprise_id, party_name=party_name,
                                party_code=party_code, user_id=_voucher.created_by, db_session=_db_session)
                    elif particular.ledger.group_id in (OTHER_CURRENT_LIABILITIES, OTHER_CURRENT_ASSETS) \
                            and particular.ledger.name.find("TDS on") == -1:
                        party_is_debit = particular.is_debit
                        party_id = self.create_or_get_ledger(
                            enterprise_id=project_enterprise_id, group_id=particular.ledger.group_id,
                            ledger_name=particular.ledger.name, user_id=_voucher.created_by,
                            db_session=_db_session, group_name=particular.ledger.group.name,
                            primary_enterprise_id=_voucher.enterprise_id)
                    i += 1
                    valid_ledgers = self.validate_ledgers(enterprise_id=enterprise_id,
                                                          ledger_id=particular.ledger_id, db_session=_db_session)
                    Voucher_logger.info("valid_ledgers:%s" % valid_ledgers)
                    if valid_ledgers:
                        # ledger_id = self.get_ledger_id(
                        #     enterprise_id=project_enterprise_id,
                        #     group_id=LEDGER_GROUP_MAPPING.get(_voucher.type_id, CASH_VOUCHER))

                        ledger_id = self.create_or_get_ledger(
                            enterprise_id=project_enterprise_id, group_id=LEDGER_GROUP_MAPPING.get(_voucher.type_id, CASH_VOUCHER),
                            ledger_name=particular.ledger.name, user_id=_voucher.created_by,
                            db_session=_db_session, group_name=particular.ledger.group.name,
                            primary_enterprise_id=_voucher.enterprise_id)
                        Voucher_logger.info("Valid ledger else condition:%s" % ledger_id)
                        if ledger_id:
                            _vp = VoucherParticulars(
                                voucher_id=voucher.id, item_no=i, ledger_id=ledger_id, is_debit=particular.is_debit,
                                amount=particular.amount, enterprise_id=project_enterprise_id)
                            Voucher_logger.info("Voucher Particulars - Valid :%s" % _vp)
                            amount += particular.amount
                            is_debit = 1 if particular.is_debit == 0 else 0
                            voucher_particulars.append(_vp)
                    else:
                        Voucher_logger.info("else valid_ledgers:%s" % valid_ledgers)
                        if particular.ledger.bills:
                            for bill in particular.ledger.bills:
                                if bill.voucher_id == particular.voucher_id:
                                    Voucher_logger.info("In  - bill.voucher_id :%s" % bill.voucher_id)
                                    is_debit = particular.is_debit
                                    _lb = LedgerBill(
                                        bill_no=bill.bill_no, bill_date=bill.bill_date, is_debit=bill.is_debit,
                                        enterprise_id=project_enterprise_id, voucher_id=voucher.id,
                                        parent_ledgerbill_id=bill.id)
                                    if not voucher.bill_settlements:
                                        ledger_bill_settlement = LedgerBillSettlement()
                                        ledger_bill_settlement.voucher = voucher
                                        ledger_bill_settlement.bill = _lb
                                        ledger_bill_settlement.enterprise_id = project_enterprise_id
                                        for _bill in _voucher.bill_settlements:
                                            ledger_bill_settlement.dr_value = amount if is_debit else 0
                                            ledger_bill_settlement.cr_value = amount if not is_debit else 0
                if party_id:
                    is_debit = party_is_debit
                    is_supplier = False
                    if _voucher.type_id == PURCHASE_VOUCHER:
                        is_supplier = True
                    elif _voucher.type_id == NOTE_VOUCHER and _voucher.bill_settlements:
                        ledger_bill_settlement = LedgerBillSettlement()
                        ledger_bill_settlement.voucher = voucher
                        get_ledger_bill = _db_session.query(LedgerBill).filter_by(
                            parent_ledgerbill_id=_voucher.bill_settlements[0].bill_id).first()
                        if get_ledger_bill:
                            ledger_bill_settlement.bill = get_ledger_bill
                            ledger_bill_settlement.enterprise_id = project_enterprise_id
                            ledger_bill_settlement.dr_value = amount if is_debit else 0
                            ledger_bill_settlement.cr_value = amount if not is_debit else 0
                            get_ledger_bill.net_value = get_ledger_bill.net_value - amount
                            if is_debit:
                                is_supplier = True
                            else:
                                is_supplier = False
                    else:
                        is_supplier = False
                    if is_sales_or_purchase:
                        party_ledger_id = _db_session.query(PartyLedgerMap.ledger_id).filter_by(
                            party_id=party_id, is_supplier=is_supplier).first()
                        ledger_id = party_ledger_id[0]
                    else:
                        ledger_id = party_id

                _vp = VoucherParticulars(
                    voucher_id=voucher.id, item_no=i, ledger_id=ledger_id, is_debit=is_debit, amount=amount,
                    enterprise_id=project_enterprise_id)
                Voucher_logger.info("Voucher Particulars:%s" % _vp)

                if _lb:
                    _lb.net_value = amount
                    _lb.ledger_id = ledger_id
                    _lb.voucher = voucher
                voucher_particulars.append(_vp)
                voucher.particulars = voucher_particulars
                _db_session.add(voucher)
                _voucher.project_automation_status = PROJECT_CLONE_STATUS
                _db_session.add(_voucher)
                _db_session.commit()
                return True
            else:
                Voucher_logger.info("In Sales clone else condition")
                return False
        except Exception as e:
            _db_session.rollback()
            Voucher_logger.info("failed to clone the [sales, purchase, general, note] vouchers: %s", e)
            self.update_failed_voucher_status(voucher_id=voucher_id, enterprise_id=enterprise_id)
            raise e

    def clone_cash_or_bank_voucher(self, voucher_id, enterprise_id, _db_session=None):
        """
        The Function clones the cash and bank type vouchers to the internal project enterprises
        """
        if not _db_session:
            _db_session = self.dao.db_session
        _db_session.begin(subtransactions=True)
        try:
            _lb = None
            amount = 0
            _voucher = _db_session.query(Voucher).filter_by(id=voucher_id, enterprise_id=enterprise_id).first()
            ledger_bill_settlements = _voucher.bill_settlements
            if ledger_bill_settlements:
                for bill_settlement in ledger_bill_settlements:
                    Voucher_logger.info("parent_ledger_bill_id:%s" % bill_settlement.bill_id)
                    parent_ledger_bill_id = bill_settlement.bill_id
                    is_debit = 1 if bill_settlement.dr_value else 0
                    if is_debit == 0:
                        amount = bill_settlement.cr_value
                    elif is_debit == 1:
                        amount = bill_settlement.dr_value
                    amount = self.exclude_tax_amount(is_debit=is_debit, amount=amount,
                                                     parent_ledger_bill_id=parent_ledger_bill_id,
                                                     db_session=_db_session)
                    ledger_bills = _db_session.query(LedgerBill).filter_by(
                        parent_ledgerbill_id=parent_ledger_bill_id).all()
                    if ledger_bills:
                        for ledger_bill in ledger_bills:
                            project_enterprise_id = ledger_bill.enterprise_id
                            project_query_set = _db_session.query(Project).filter_by(
                                project_enterprise_id=project_enterprise_id).first()
                            oa_nos = self.check_internal_workorder(ledger_bill_id=parent_ledger_bill_id)
                            if oa_nos and amount > 0:
                                self.create_internal_workorder_settlements_v1(oa_nos=oa_nos, parent_voucher=_voucher,
                                                                              total_amount=amount, is_debit=is_debit,
                                                                              _db_session=_db_session)
                            bill_settlement_amount = amount if ledger_bill.net_value > amount else \
                                ledger_bill.net_value
                            voucher = self.create_settlement_entry(
                                project_enterprise_id=project_enterprise_id, parent_voucher=_voucher,
                                project_code=project_query_set.id,
                                ledger_bill=ledger_bill, is_debit=is_debit, amount=bill_settlement_amount, db_session=_db_session)
                            bill_settlement_dr_value = 0
                            bill_settlement_cr_value = 0
                            if is_debit:
                                ledger_bill.net_value = 0 if bill_settlement.dr_value > ledger_bill.net_value else \
                                    ledger_bill.net_value - bill_settlement.dr_value
                                bill_settlement_dr_value = bill_settlement_amount
                            else:
                                ledger_bill.net_value = 0 if bill_settlement.cr_value > ledger_bill.net_value else \
                                    ledger_bill.net_value - bill_settlement.cr_value
                                bill_settlement_cr_value = bill_settlement_amount
                            lbs = LedgerBillSettlement(
                                bill_id=ledger_bill.id, voucher_id=voucher.id,
                                dr_value=bill_settlement_dr_value, cr_value=bill_settlement_cr_value,
                                enterprise_id=ledger_bill.enterprise_id)
                            lbs.voucher = voucher
                            _db_session.add(voucher)
                    else:
                        get_parent_ledger_bill = _db_session.query(LedgerBill).filter_by(
                            id=parent_ledger_bill_id).first()
                        if get_parent_ledger_bill.voucher.type_id in (PURCHASE_VOUCHER, SALES_VOUCHER):
                            clone_status = self.clone_sales_or_purchase_voucher(
                                voucher_id=get_parent_ledger_bill.voucher_id, enterprise_id=get_parent_ledger_bill.enterprise_id,
                                project_code=get_parent_ledger_bill.voucher.project_code, _db_session=_db_session)
                            if clone_status is False:
                                clone_status = self.clone_sales_or_purchase_voucher(
                                    voucher_id=get_parent_ledger_bill.voucher_id,
                                    enterprise_id=get_parent_ledger_bill.enterprise_id,
                                    project_code=_voucher.project_code, _db_session=_db_session)
                        else:
                            Voucher_logger.info("In general voucher:%s" % get_parent_ledger_bill.voucher.type_id)
                            clone_status = self.clone_general_voucher(
                                voucher_id=get_parent_ledger_bill.voucher_id, enterprise_id=get_parent_ledger_bill.enterprise_id,
                                project_code=get_parent_ledger_bill.voucher.project_code, _db_session=_db_session)
                            if clone_status is False:
                                clone_status = self.clone_general_voucher(
                                    voucher_id=get_parent_ledger_bill.voucher_id,
                                    enterprise_id=get_parent_ledger_bill.enterprise_id,
                                    project_code=_voucher.project_code, _db_session=_db_session)
                        if clone_status:
                            ledger_bill = _db_session.query(LedgerBill).filter_by(
                                parent_ledgerbill_id=parent_ledger_bill_id).first()
                            project_query_set = _db_session.query(Project).filter_by(
                                project_enterprise_id=ledger_bill.enterprise_id).first()
                            bill_settlement_amount = amount if ledger_bill.net_value > amount else \
                                ledger_bill.net_value
                            voucher = self.create_settlement_entry(
                                project_enterprise_id=ledger_bill.enterprise_id, parent_voucher=_voucher,
                                project_code=project_query_set.id,
                                ledger_bill=ledger_bill, is_debit=is_debit, amount=bill_settlement_amount, db_session=_db_session)
                            bill_settlement_dr_value = 0
                            bill_settlement_cr_value = 0
                            if is_debit:
                                ledger_bill.net_value = 0 if bill_settlement.dr_value > ledger_bill.net_value else \
                                    ledger_bill.net_value - bill_settlement.dr_value
                                bill_settlement_dr_value = bill_settlement_amount
                            else:
                                ledger_bill.net_value = 0 if bill_settlement.cr_value > ledger_bill.net_value else \
                                    ledger_bill.net_value - bill_settlement.cr_value
                                bill_settlement_cr_value = bill_settlement_amount
                            lbs = LedgerBillSettlement(
                                bill_id=ledger_bill.id, voucher_id=voucher.id,
                                dr_value=bill_settlement_dr_value, cr_value=bill_settlement_cr_value,
                                enterprise_id=ledger_bill.enterprise_id)
                            lbs.voucher = voucher
                            _db_session.add(voucher)
                        else:
                            Voucher_logger.info("In else clone status")
                            continue
            else:
                project_code = _voucher.project_code
                project_query_set = _db_session.query(Project).filter_by(id=project_code).first()
                project_enterprise_id = project_query_set.project_enterprise_id
                if project_enterprise_id != _voucher.enterprise_id:
                    voucher_particulars = []
                    voucher = Voucher(
                        enterprise_id=project_enterprise_id, voucher_date=_voucher.voucher_date,
                        voucher_no=_voucher.voucher_no, type_id=_voucher.type_id, narration=_voucher.narration,
                        created_by=_voucher.created_by, created_on=_voucher.created_on, status=_voucher.status,
                        financial_year=_voucher.financial_year, project_code=project_code,
                        parent_voucher_id=_voucher.id)
                    i = 0
                    for particular in _voucher.particulars:
                        i += 1
                        # if particular.ledger.group_id in (SUNDRY_DEBTORS_GROUP_ID, SUNDRY_CREDITORS_GROUP_ID,
                        #                                   OTHER_CURRENT_LIABILITIES, OTHER_CURRENT_ASSETS):
                        #     party_id = self.getPartyIdByPrimaryEnterpriseId(primary_enterprise_id=_voucher.enterprise_id,
                        #                                                     project_enterprise_id=project_enterprise_id)
                        #     if not party_id:
                        #         party_name, party_code = self.getParentEnterpriseParty(enterprise_id=_voucher.enterprise_id)
                        #         party_id = self.createPartyToProjectEnterprise(
                        #             project_enterprise_id=project_enterprise_id, party_name=party_name,
                        #             party_code=party_code, user_id=_voucher.created_by, db_session=_db_session)
                        #     if particular.is_debit:
                        #         is_supplier = True
                        #     else:
                        #         is_supplier = False
                        #     party_ledger_id = _db_session.query(PartyLedgerMap.ledger_id).filter_by(
                        #         party_id=party_id, is_supplier=is_supplier).first()
                        #     ledger_id = party_ledger_id[0]
                        # else:
                        ledger_id = self.create_or_get_ledger(
                            enterprise_id=project_enterprise_id, group_id=particular.ledger.group_id,
                            ledger_name=particular.ledger.name, user_id=_voucher.created_by, db_session=_db_session,
                            group_name=particular.ledger.group.name,
                            primary_enterprise_id=_voucher.enterprise_id)
                        _vp = VoucherParticulars(
                            voucher_id=voucher.id, item_no=i, ledger_id=ledger_id, is_debit=particular.is_debit,
                            amount=particular.amount, enterprise_id=project_enterprise_id, budget_id=ledger_id)
                        voucher_particulars.append(_vp)
                    voucher.particulars = voucher_particulars
                    _db_session.add(voucher)
            _voucher.project_automation_status = PROJECT_CLONE_STATUS
            _db_session.add(_voucher)
            _db_session.commit()
            return True
        except Exception as e:
            _db_session.rollback()
            Voucher_logger.debug("Failed to clone the cash or bank vouchers: %s", e)
            self.update_failed_voucher_status(voucher_id=voucher_id, enterprise_id=enterprise_id)
            raise e

    def update_voucher(self, voucher_id):
        """
        The function updates the child voucher if there is any changes or update made in parent voucher
        """
        _db_session = self.dao.db_session
        _db_session.begin(subtransactions=True)
        _update_status = False
        delete_status = False
        try:
            parent_voucher = self.get_voucher(voucher_id=voucher_id, _db_session=_db_session, is_parent=True)
            child_vouchers = self.get_voucher(voucher_id=voucher_id, _db_session=_db_session)
            if parent_voucher and child_vouchers:
                for child_voucher in child_vouchers:
                    if child_voucher.type_id in (CASH_VOUCHER, BANK_VOUCHER):
                        bill_update_status = self.revert_ledger_bill_net_value(
                            bill_settlements=child_voucher.bill_settlements,
                            _db_session=_db_session)
                        if bill_update_status:
                            delete_status = self.delete_voucher(voucher=child_voucher, _db_session=_db_session)
                    elif child_voucher.type_id in (SALES_VOUCHER, PURCHASE_VOUCHER, NOTE_VOUCHER):
                        delete_status = self.delete_voucher(voucher=child_voucher, _db_session=_db_session)
                    elif child_voucher.type_id == GENERAL_VOUCHER:
                        delete_status = self.delete_voucher(voucher=child_voucher, _db_session=_db_session)

                clone_status = False
                if delete_status and parent_voucher.type_id in (CASH_VOUCHER, BANK_VOUCHER):
                    clone_status = self.clone_cash_or_bank_voucher(
                        voucher_id=voucher_id,
                        enterprise_id=parent_voucher.enterprise_id, _db_session=_db_session)
                elif delete_status and parent_voucher.type_id in (SALES_VOUCHER, PURCHASE_VOUCHER, NOTE_VOUCHER):
                    clone_status = self.clone_sales_or_purchase_voucher(
                        voucher_id=voucher_id,
                        enterprise_id=parent_voucher.enterprise_id, _db_session=_db_session)
                elif delete_status and parent_voucher.type_id == GENERAL_VOUCHER:
                    clone_status = self.clone_general_voucher(
                        voucher_id=voucher_id,
                        enterprise_id=parent_voucher.enterprise_id, _db_session=_db_session)
                if clone_status:
                    parent_voucher.project_automation_status = PROJECT_CLONE_STATUS
                _update_status = True
            else:
                parent_voucher.project_automation_status = 0
                Voucher_logger.info(
                    "Voucher not exists parent voucher: %s--child voucher: %s" % (parent_voucher,  child_vouchers))
            _db_session.commit()
        except Exception as e:
            _db_session.rollback()
            Voucher_logger.info("Failed to update vouchers: %s", e)
        return _update_status

    def revert_ledger_bill_net_value(self, bill_settlements, _db_session):
        try:
            _db_session.begin(subtransactions=True)
            for bill_settlement in bill_settlements:
                if bill_settlement.bill.is_debit == 0:
                    bill_settlement.bill.net_value = bill_settlement.bill.net_value + bill_settlement.cr_value
                else:
                    bill_settlement.bill.net_value = bill_settlement.bill.net_value + bill_settlement.dr_value
            _db_session.commit()
            for bill_settlement in bill_settlements:
                _db_session.refresh(bill_settlement.bill)
            return True
        except Exception as e:
            _db_session.rollback()
            Voucher_logger.info("Failed to revert the net value during voucher update: %s", e)
            raise e

    def get_voucher(self, voucher_id, _db_session, is_parent=False):
        try:
            if is_parent:
                return _db_session.query(Voucher).filter_by(id=voucher_id).first()
            else:
                return _db_session.query(Voucher).filter_by(parent_voucher_id=voucher_id).all()
        except Exception as e:
            Voucher_logger.info("Failed to get the child Voucher: %s", e)
            return None

    def delete_voucher(self, voucher, _db_session):
        parent_voucher_id = voucher.parent_voucher_id
        try:
            _db_session.begin(subtransactions=True)
            for particular in voucher.particulars:
                if particular:
                    for bill in particular.ledger.bills:
                        for settlement in bill.settlements:
                            if settlement.voucher_id == particular.voucher_id:
                                _db_session.delete(settlement)
                        if bill.voucher_id == particular.voucher_id:
                            _db_session.delete(bill)
                    _db_session.delete(particular)
            _db_session.delete(voucher)
            _db_session.commit()
            Voucher_logger.info("Child voucher was successfully deleted for parent voucher id: %s", parent_voucher_id)
            return True
        except Exception as e:
            _db_session.rollback()
            Voucher_logger.info("Failed to delete child voucher during voucher update: %s", e)
            raise e

    def create_settlement_entry(self, project_enterprise_id, parent_voucher, project_code, ledger_bill, is_debit,
                                amount, db_session=None):
        """
        Create settlement entry from the given data
        """
        Voucher_logger.info("Settlement entry creation initiated for the Project: %s Parent_voucher_id: %s" %
                            (project_enterprise_id, parent_voucher.id))
        try:
            voucher_particulars = []
            voucher = Voucher(
                enterprise_id=project_enterprise_id, voucher_date=parent_voucher.voucher_date,
                voucher_no=parent_voucher.voucher_no, type_id=parent_voucher.type_id,
                narration=parent_voucher.narration,
                created_by=parent_voucher.created_by, created_on=parent_voucher.created_on,
                status=parent_voucher.status,
                financial_year=parent_voucher.financial_year, project_code=project_code,
                parent_voucher_id=parent_voucher.id)
            party_ledger_id = self.create_or_get_ledger(
                enterprise_id=project_enterprise_id, group_id=ledger_bill.ledger.group_id,
                ledger_name=ledger_bill.ledger.name,
                user_id=parent_voucher.created_by, db_session=db_session, bill_ledger_id=ledger_bill.ledger.id)
            get_budget_details = self.get_budget_details(
                enterprise_id=project_enterprise_id, ledger_bill=ledger_bill, bill_ledger_id=ledger_bill.id,
                db_session=db_session)
            proportionally_splitted_settlements, excess = self.split_settlements(bills=get_budget_details,
                                                                                 settlement_amount=float(amount))
            i = 1
            for details in proportionally_splitted_settlements:
                _vp = VoucherParticulars(
                    voucher_id=voucher.id, item_no=i, ledger_id=party_ledger_id, is_debit=is_debit,
                    amount=details['amount'], enterprise_id=ledger_bill.enterprise_id, budget_id=details['budget_id'])
                voucher_particulars.append(_vp)
                i = i + 1
            bank_ledger_id = self.get_ledger_id(
                enterprise_id=ledger_bill.enterprise_id, group_id=CASH_OR_BANK.get(parent_voucher.type_id, 20))
            _vp = VoucherParticulars(
                voucher_id=voucher.id, item_no=2, ledger_id=bank_ledger_id, is_debit=0 if is_debit else 1,
                amount=amount,
                enterprise_id=ledger_bill.enterprise_id)
            voucher_particulars.append(_vp)
            voucher.particulars = voucher_particulars
            return voucher
        except Exception as e:
            Voucher_logger.info("Failed to create settlement entry: %s", e)
            raise e

    def create_internal_workorder_settlements_v1(self, oa_nos, parent_voucher, total_amount, is_debit, _db_session):
        """

        """
        Voucher_logger.info("Internal work-order Settlement entry creation initiated "
                            "for the OA: %s Parent_voucher_id: %s" % (oa_nos, parent_voucher.id))
        try:
            _db_session.begin(subtransactions=True)
            _lb = None
            internal_invoice_ledger_bills = self.get_internal_workorder_ledger_bill(
                oa_nos=tuple(item[0] for item in oa_nos), db_session=_db_session)
            for internal_invoice_ledger_bill in internal_invoice_ledger_bills:
                project_enterprise_id = internal_invoice_ledger_bill.enterprise_id
                project_query_set = _db_session.query(Project).filter_by(
                    project_enterprise_id=project_enterprise_id).first()
                bill_settlement_amount = total_amount if internal_invoice_ledger_bill.net_value > total_amount else \
                    internal_invoice_ledger_bill.net_value
                total_amount -= bill_settlement_amount
                if bill_settlement_amount > 0:
                    voucher = self.create_settlement_entry(
                        project_enterprise_id=project_enterprise_id, parent_voucher=parent_voucher,
                        project_code=project_query_set.id, ledger_bill=internal_invoice_ledger_bill,
                        is_debit=is_debit, amount=bill_settlement_amount, db_session=_db_session)
                    bill_settlement_dr_value = 0
                    bill_settlement_cr_value = 0
                    if is_debit:
                        internal_invoice_ledger_bill.net_value = 0 if \
                            bill_settlement_amount > internal_invoice_ledger_bill.net_value \
                            else internal_invoice_ledger_bill.net_value - bill_settlement_amount
                        bill_settlement_dr_value = bill_settlement_amount
                    else:
                        internal_invoice_ledger_bill.net_value = 0 if \
                            bill_settlement_amount > internal_invoice_ledger_bill.net_value \
                            else internal_invoice_ledger_bill.net_value - bill_settlement_amount
                        bill_settlement_cr_value = bill_settlement_amount
                    lbs = LedgerBillSettlement(
                        bill_id=internal_invoice_ledger_bill.id, voucher_id=voucher.id,
                        dr_value=bill_settlement_dr_value, cr_value=bill_settlement_cr_value,
                        enterprise_id=internal_invoice_ledger_bill.enterprise_id)
                    lbs.voucher = voucher
                    _db_session.add(voucher)

                    # Create internal purchase ledger bill settlement
                    get_internal_purchase_ledger_bill = _db_session.query(LedgerBill).filter_by(
                        parent_ledgerbill_id=internal_invoice_ledger_bill.id).first()
                    if get_internal_purchase_ledger_bill:
                        Voucher_logger.info("get_internal_purchase_ledger_bill id:%s" %
                                            get_internal_purchase_ledger_bill.id)
                        purchase_is_debit = 0 if is_debit else 1
                        voucher = self.create_settlement_entry(
                            project_enterprise_id=get_internal_purchase_ledger_bill.enterprise_id,
                            parent_voucher=parent_voucher, project_code=project_query_set.id,
                            ledger_bill=get_internal_purchase_ledger_bill, is_debit=purchase_is_debit,
                            amount=bill_settlement_amount, db_session=_db_session)
                        bill_settlement_dr_value = 0
                        bill_settlement_cr_value = 0
                        if purchase_is_debit:
                            get_internal_purchase_ledger_bill.net_value = 0 if \
                                bill_settlement_amount > get_internal_purchase_ledger_bill.net_value \
                                else get_internal_purchase_ledger_bill.net_value - bill_settlement_amount
                            bill_settlement_dr_value = bill_settlement_amount
                        else:
                            get_internal_purchase_ledger_bill.net_value = 0 if \
                                bill_settlement_amount > get_internal_purchase_ledger_bill.net_value \
                                else get_internal_purchase_ledger_bill.net_value - bill_settlement_amount
                            bill_settlement_cr_value = bill_settlement_amount
                        lbs = LedgerBillSettlement(
                            bill_id=get_internal_purchase_ledger_bill.id, voucher_id=voucher.id,
                            dr_value=bill_settlement_dr_value, cr_value=bill_settlement_cr_value,
                            enterprise_id=get_internal_purchase_ledger_bill.enterprise_id)
                        lbs.voucher = voucher
                        _db_session.add(voucher)

            _db_session.commit()
            return True
        except Exception as e:
            _db_session.rollback()
            Voucher_logger.info("Failed to create internal work-order settlements: %s", e)
            raise e

    @staticmethod
    def get_budget_details(enterprise_id=None, ledger_bill=None, bill_ledger_id=None, db_session=None):
        try:
            Voucher_logger.info("bill_ledger_id:%s" % bill_ledger_id)
            valid_group_ids = getAccountGroupIDs(enterprise_id=enterprise_id, names=VALID_BUDGET_GROUP_NAMES, deep=True)
            voucher_particulars = db_session.query(VoucherParticulars).filter_by(voucher_id=ledger_bill.voucher_id)
            budget_details = []
            for particular in voucher_particulars:
                budget_dict = {}
                if particular.ledger_id != bill_ledger_id:
                    if particular.ledger.group_id in valid_group_ids:
                        if particular.ledger.name != "Round-off" and particular.ledger.name.find("TDS on") == -1:
                            budget_dict["budget_id"] = particular.ledger_id
                            budget_dict["amount"] = float(particular.amount)
                            budget_details.append(budget_dict)
            Voucher_logger.info("budget_details:%s" % budget_details)
            return budget_details
        except Exception as e:
            Voucher_logger.info("Failed to get budget Id: %s", e)
            return None

    @staticmethod
    def split_settlements(bills=None, settlement_amount=None):
        settlements = []
        excess = 0
        try:
            # Calculate total bill amount
            total_bills = sum(bill['amount'] for bill in bills)
            # Prepare to store settlements for each bill and the excess amount

            if settlement_amount <= total_bills:
                # If settlement amount is less than or equal to the total bill amount
                proportions = [float(bill['amount']) / total_bills for bill in bills]
                for bill, proportion in zip(bills, proportions):
                    settlement = min(bill['amount'], proportion * settlement_amount)
                    settlements.append({"budget_id": bill['budget_id'], "amount": settlement})
            else:
                # If settlement amount is greater than the total bill amount
                proportions = [float(bill['amount']) / total_bills for bill in bills]
                for bill, proportion in zip(bills, proportions):
                    settlement = proportion * settlement_amount
                    if settlement > bill['amount']:
                        settlement = bill['amount']  # Cap at the bill amount
                    settlements.append({"budget_id": bill['budget_id'], "amount": settlement})
                excess = settlement_amount - total_bills
        except Exception as e:
            Voucher_logger.info("Failed to split the amount: %s", e)
        return settlements, excess

    @staticmethod
    def get_ledger_id(enterprise_id=None, group_id=None, name=None):
        try:
            if name:
                project_query = """SELECT id FROM account_ledgers WHERE enterprise_id = {enterprise_id} AND 
                name = "{name}" AND group_id={group_id}""".format(enterprise_id=enterprise_id, name=name,
                                                                  group_id=group_id)
            else:
                project_query = """SELECT id FROM account_ledgers WHERE enterprise_id = {enterprise_id} 
                and group_id={group_id}""".format(enterprise_id=enterprise_id, group_id=group_id)
            projects_details = executeQuery(query=project_query, as_dict=True)
            return projects_details[0]["id"] if projects_details else None
        except Exception as e:
            Voucher_logger.info("Failed to get Ledger Id: %s", e)
            return None

    def create_or_get_ledger(self, db_session, enterprise_id=None, group_id=None, ledger_name=None, user_id=None,
                             bill_ledger_id=None, group_name=None, primary_enterprise_id=None):
        ledger_id = None
        try:
            if bill_ledger_id:
                ledger = db_session.query(
                    Ledger.id.label('id'), Ledger.name.label('name'),
                    AccountGroup.name.label('group_name')).join(Ledger.group).filter(
                    Ledger.enterprise_id == enterprise_id, Ledger.name == ledger_name,
                    Ledger.id == bill_ledger_id).first()
            else:
                ledger = db_session.query(
                    Ledger.id.label('id'), Ledger.name.label('name'),
                    AccountGroup.name.label('group_name')).join(Ledger.group).filter(
                    Ledger.enterprise_id == enterprise_id, Ledger.name == ledger_name).first()
            if ledger:
                ledger_id = ledger.id
            else:
                accounts_group = db_session.query(
                        AccountGroup.id.label('id'), AccountGroup.name.label('group_name')).filter(
                        AccountGroup.enterprise_id == enterprise_id,  AccountGroup.name == group_name).first()
                if accounts_group:
                    group_id = accounts_group.id
                if not accounts_group:
                    group_parent_id = self.validateAccountGroup(
                        enterprise_id=enterprise_id, db_session=db_session, group_name=group_name, user_id=user_id,
                        primary_enterprise_id=primary_enterprise_id)
                    if group_parent_id:
                        group_id = self.createAccountGroup(
                            enterprise_id=enterprise_id, db_session=db_session, group_parent_id=group_parent_id,
                            group_name=group_name, user_id=user_id)
                try:
                    db_session.begin(subtransactions=True)
                    ledger = Ledger(
                        group_id=group_id, name=ledger_name, enterprise_id=enterprise_id, description=ledger_name,
                        created_by=user_id, billable=True)
                    db_session.add(ledger)
                    db_session.commit()
                    db_session.refresh(ledger)
                    ledger_id = ledger.id
                except Exception as e1:
                    db_session.rollback()
            return ledger_id
        except Exception as e:
            Voucher_logger.info("Failed to create Ledger: %s", e)
            raise e

    @staticmethod
    def validate_ledgers(ledger_id, enterprise_id, db_session):
        """
        identify the valid ledgers for cloning it to the in-house project
        """
        try:
            ledger_query_set = db_session.query(Ledger.group_id).filter_by(id=ledger_id).first()
            valid_group_ids = getAccountGroupIDs(enterprise_id=enterprise_id, names=VALID_GROUP_NAMES, deep=True)
            if ledger_query_set[0] in valid_group_ids:
                return True
            else:
                return False
        except Exception as e:
            Voucher_logger.info("Failed to validate ledgers: %s", e)

    @staticmethod
    def check_internal_workorder(ledger_bill_id):
        """
        Check the Internal work-order for the ledger bill against the OA
        """
        try:
            query = """SELECT 
                           internal_oa_id, oa.oa_no
                       FROM
                           invoice as inv
                           JOIN 
                           invoice_materials as im ON inv.id=im.invoice_id AND im.enterprise_id=inv.enterprise_id
                           LEFT JOIN order_acknowledgement as oa ON oa.id=im.oa_no AND oa.enterprise_id=im.enterprise_id
                           JOIN oa_particulars as oap ON oap.oa_id=oa.id
                       WHERE
                           inv.ledger_bill_id = %s group by oa.id,oap.internal_oa_id;""" % ledger_bill_id
            oa_data = executeQuery(query=query)
            return oa_data
        except Exception as e:
            Voucher_logger.info("failed to check the internal work-order:%s", e)

    @staticmethod
    def getParentEnterpriseParty(enterprise_id):
        party_name = party_code = None
        try:
            query = """SELECT name, code FROM projects WHERE enterprise_id={enterprise_id} and 
            project_enterprise_id={enterprise_id} and is_active=1 limit 1""".format(enterprise_id=enterprise_id)
            query_data = executeQuery(query)
            party_name = query_data[0][0] if query_data else None
            party_code = query_data[0][1] if query_data else None
        except Exception as e:
            Voucher_logger.exception("Party get failed from Project.... - %s" % e.message)
        return party_name, party_code

    @staticmethod
    def get_internal_workorder_ledger_bill(oa_nos, db_session):
        """
        Get the internal work-order ledger bill using the oa id
        """
        ledger_bill_objs = []
        try:
            ledger_bill_data = db_session.query(Invoice.id, LedgerBill) \
                .join(InvoiceMaterial, InvoiceMaterial.invoice_id == Invoice.id) \
                .join(LedgerBill, LedgerBill.id == Invoice.ledger_bill_id) \
                .filter(and_(InvoiceMaterial.oa_no.in_(oa_nos), LedgerBill.net_value > 0)) \
                .order_by(LedgerBill.net_value.asc()).all()
            if ledger_bill_data:
                ledger_bill_objs = [item[1] for item in ledger_bill_data]
        except Exception as e:
            Voucher_logger.info("failed to get internal work-order ledger bill:%s", e)
        return ledger_bill_objs

    @staticmethod
    def createPartyToProjectEnterprise(project_enterprise_id, party_name, party_code, user_id, db_session):
        try:
            db_session.begin(subtransactions=True)
            party_to_be_created = Party(name=party_name, enterprise_id=project_enterprise_id, code=party_code)
            db_session.add(party_to_be_created)
            db_session.commit()
            db_session.refresh(party_to_be_created)
            party_id = party_to_be_created.id
            try:
                createPartyLedgers(
                    party=party_to_be_created, user_id=user_id, db_session=db_session,
                    is_customer=True, is_supplier=True)
            except Exception as e1:
                Voucher_logger.exception("Party Create Success but PartyLedger Failed for Project.... - %s" % e1.message)
                # No need to rollback here, where we have done commit above to try block
            return party_id
        except Exception as e:
            Voucher_logger.exception("Party Create Failed for Project.... - %s" % e.message)
            db_session.rollback()
            raise e

    @staticmethod
    def getPartyIdByPrimaryEnterpriseId(primary_enterprise_id, project_enterprise_id):
        party_id = None
        try:
            query = """SELECT party_id,party_code,party_name FROM party_master WHERE 
            enterprise_id={project_enterprise_id} AND party_code = (SELECT code FROM projects WHERE 
            project_enterprise_id={enterprise_id} AND enterprise_id={enterprise_id} and is_active=1 limit 1)""".format(
                enterprise_id=primary_enterprise_id, project_enterprise_id=project_enterprise_id)
            query_data = executeQuery(query)
            if query_data:
                party_id = query_data[0][0]
        except Exception as e:
            Voucher_logger.exception("Party get failed from Project.... - %s" % e.message)
        return party_id

    def createAccountGroup(self, enterprise_id, db_session, group_parent_id, group_name, user_id):
        try:
            db_session.begin(subtransactions=True)
            latest_group_id = db_session.query(AccountGroup.id).filter(
                AccountGroup.enterprise_id == enterprise_id).order_by(AccountGroup.id.desc()).first()
            group = AccountGroup(parent_id=group_parent_id, name=group_name, description=group_name,
                                 config_flags=3, enterprise_id=enterprise_id, created_by=user_id)
            group.id = int(latest_group_id.id) + 1
            db_session.add(group)
            db_session.commit()
            db_session.refresh(group)
            group_id = group.id
            return group_id
        except Exception as e:
            Voucher_logger.exception("Accounts Group Creation Failed .... - %s" % e.message)
            db_session.rollback()
            raise e

    def validateAccountGroup(self, enterprise_id, db_session, group_name, user_id, primary_enterprise_id):
        try:
            parent_id = 0
            if group_name:
                service = AccountService()
                primary_account_group_list = service.getAccountGroupTree(enterprise_id=primary_enterprise_id, group_name=group_name)
                Voucher_logger.info("primary_account_group_list %s", primary_account_group_list)
                if primary_account_group_list:
                    for account_group in primary_account_group_list:
                        is_exists = db_session.query(AccountGroup).filter(
                            AccountGroup.enterprise_id == enterprise_id, AccountGroup.name == account_group.name).first()
                        if is_exists:
                            parent_id = is_exists.id
                        else:
                            parent_id = self.createAccountGroup(
                                enterprise_id=enterprise_id, db_session=db_session, group_parent_id=parent_id,
                                group_name=account_group.name, user_id=user_id)
            return parent_id
        except Exception as e:
            Voucher_logger.exception("Accounts Group Validation Failed .... - %s" % e.message)
            raise e

    def fetch_clone_pending_vouchers(self, parent_enterprise_id, voucher_date, project_code):
        pending_vouchers_list = []
        try:
            query_data = (
                self.dao.db_session.query(Voucher)
                .filter(
                    and_(
                        Voucher.enterprise_id == parent_enterprise_id,
                        Voucher.voucher_date > voucher_date,
                        Voucher.project_automation_status.in_([0, 2]),
                        Voucher.project_code != project_code,
                        Voucher.project_code > project_code,
                        Voucher.status == 1
                    )
                )
                .order_by(Voucher.voucher_date.asc())
                .all()
            )
            if query_data:
                for _voucher in query_data:
                    # Convert each Voucher object to a dictionary
                    voucher_data = _voucher.__dict__
                    voucher_data.pop('_sa_instance_state', None)
                    voucher_data['created_on'] = voucher_data['created_on'].strftime("%d-%b-%Y %H:%M:%S") if \
                        voucher_data['created_on'] else voucher_data['created_on']
                    voucher_data['approved_on'] = voucher_data['approved_on'].strftime("%d-%b-%Y %H:%M:%S") if \
                        voucher_data['approved_on'] else voucher_data['approved_on']
                    voucher_data['voucher_date'] = voucher_data['voucher_date'].strftime("%d-%b-%Y %H:%M:%S") if \
                        voucher_data['voucher_date'] else voucher_data['voucher_date']
                    pending_vouchers_list.append(voucher_data)

            # Serialize the list of dictionaries to JSON
            return pending_vouchers_list
        except Exception as e:
            Voucher_logger.info("An error occurred while fetching data from MySQL: %s", e)

    def exclude_tax_amount(self, is_debit, amount, parent_ledger_bill_id, db_session):
        total_tax_amount = 0
        total_material_amount = 0
        try:
            if is_debit:
                receipt = db_session.query(Receipt).filter(Receipt.ledger_bill_id == parent_ledger_bill_id).first()
                if receipt:
                    total_material_amount = receipt.getTotalMaterialValue()
                    tax_values = receipt.getTaxValues()
                    if tax_values:
                        for value in tax_values.values():
                            total_tax_amount += value
            else:
                invoice = db_session.query(Invoice).filter(Invoice.ledger_bill_id == parent_ledger_bill_id).first()
                if invoice:
                    total_material_amount = invoice.getTotalMaterialValue()
                    result, result_2 = getConsolidatedMaterial(invoice=invoice)
                    tax_summary = invoice.getTaxSummary(invoice_materials=result, source=invoice)
                    if tax_summary:
                        for value in tax_summary.values():
                            for tax_amount in value.values():
                                total_tax_amount += tax_amount
            if total_tax_amount and total_material_amount:
                tax_percent = (total_tax_amount / total_material_amount) * 100
                tax_percent = tax_percent / 100
                return int(amount - (amount / (1 + tax_percent) * tax_percent))
            else:
                return amount
        except Exception as e:
            Voucher_logger.info("Failed to exclude the tax amount: %s", e)
        return amount

    def clone_general_voucher(self, voucher_id, enterprise_id, project_code=None, _db_session=None):
        """
        The Function get the existing voucher object using the voucher_id.
        creates the new Vouchers using the that object data in projects.
        Params: voucher_id
        """
        if not _db_session:
            _db_session = self.dao.db_session
        try:
            _db_session.begin(subtransactions=True)
            ledger_group_ids = []
            _lb = None
            _party_name = None
            _group_id = None
            _group_parent_id = None
            _group_name = None
            _voucher = _db_session.query(Voucher).filter_by(id=voucher_id, enterprise_id=enterprise_id).first()
            Voucher_logger.info("_voucher:%s" % _voucher)
            project_code = project_code if project_code else _voucher.project_code
            project_query_set = _db_session.query(Project).filter_by(id=project_code).first()
            Voucher_logger.info("project_query_set:%s" % project_query_set)
            project_enterprise_id = project_query_set.project_enterprise_id
            if project_enterprise_id and project_enterprise_id != _voucher.enterprise_id:
                for particular in _voucher.particulars:
                    ledger_group_ids.append(particular.ledger.group_id)
                settlement_group_ids = getAccountGroupIDs(enterprise_id=_voucher.enterprise_id,
                                                          names=[AccountGroup.BANK_GROUP_NAME,
                                                                 AccountGroup.CASH_GROUP_NAME,
                                                                 AccountGroup.LT_BORROWINGS_GROUP_NAME,
                                                                 AccountGroup.ST_BORROWINGS_GROUP_NAME], deep=True)
                if any(group_id in settlement_group_ids for group_id in ledger_group_ids):
                    clone_status = self.clone_cash_or_bank_voucher(voucher_id=voucher_id, enterprise_id=enterprise_id,
                                                                   _db_session=_db_session)
                    if clone_status:
                        return True
                else:
                    voucher = Voucher(
                        enterprise_id=project_enterprise_id, voucher_date=_voucher.voucher_date,
                        voucher_no=_voucher.voucher_no, type_id=_voucher.type_id, narration=_voucher.narration,
                        created_by=_voucher.created_by, created_on=_voucher.created_on, status=_voucher.status,
                        financial_year=_voucher.financial_year, project_code=project_code, parent_voucher_id=_voucher.id)
                    i = 0
                    voucher_particulars = []
                    bill_set = set()
                    for particular in _voucher.particulars:
                        party_id = None
                        ledger_id = None
                        if particular.ledger.group_id in (SUNDRY_DEBTORS_GROUP_ID, SUNDRY_CREDITORS_GROUP_ID):
                            party_id = self.getPartyIdByPrimaryEnterpriseId(primary_enterprise_id=_voucher.enterprise_id,
                                                                            project_enterprise_id=project_enterprise_id)
                            if not party_id:
                                party_name, party_code = self.getParentEnterpriseParty(enterprise_id=_voucher.enterprise_id)
                                party_id = self.createPartyToProjectEnterprise(
                                    project_enterprise_id=project_enterprise_id, party_name=party_name,
                                    party_code=party_code, user_id=_voucher.created_by, db_session=_db_session)
                        if not party_id:
                            ledger_id = self.create_or_get_ledger(
                                enterprise_id=project_enterprise_id, group_id=particular.ledger.group_id,
                                ledger_name=particular.ledger.name, user_id=_voucher.created_by,
                                db_session=_db_session, group_name=particular.ledger.group.name,
                                primary_enterprise_id=_voucher.enterprise_id)
                        if party_id:
                            if particular.is_debit:
                                is_supplier = True
                            else:
                                is_supplier = False
                            party_ledger_id = _db_session.query(PartyLedgerMap.ledger_id).filter_by(
                                party_id=party_id, is_supplier=is_supplier).first()
                            ledger_id = party_ledger_id[0]
                        i += 1
                        if particular.ledger.bills:
                            for bill in particular.ledger.bills:
                                if bill.voucher_id == particular.voucher_id:
                                    if bill.bill_no not in bill_set:
                                        Voucher_logger.info("bill.bill_no not in bill_set")
                                        bill_set.add(bill.bill_no)
                                        is_debit = particular.is_debit
                                        _lb = LedgerBill(
                                            bill_no=bill.bill_no, bill_date=bill.bill_date, is_debit=bill.is_debit,
                                            enterprise_id=project_enterprise_id, voucher_id=voucher.id,
                                            parent_ledgerbill_id=bill.id, net_value=particular.amount)
                                        _lb.ledger_id = ledger_id
                                        _lb.voucher = voucher
                                        if not voucher.bill_settlements:
                                            ledger_bill_settlement = LedgerBillSettlement()
                                            ledger_bill_settlement.voucher = voucher
                                            ledger_bill_settlement.bill = _lb
                                            ledger_bill_settlement.enterprise_id = project_enterprise_id
                                            for _bill in _voucher.bill_settlements:
                                                ledger_bill_settlement.dr_value = particular.amount if is_debit else 0
                                                ledger_bill_settlement.cr_value = particular.amount if not is_debit else 0
                                        if ledger_id:
                                            _vp = VoucherParticulars(
                                                voucher_id=voucher.id, item_no=i, ledger_id=ledger_id, is_debit=particular.is_debit,
                                                amount=particular.amount, enterprise_id=project_enterprise_id)
                                            voucher_particulars.append(_vp)
                                            Voucher_logger.info("_vp:%s" % _vp)
                                        break
                        else:
                            if ledger_id:
                                _vp = VoucherParticulars(
                                    voucher_id=voucher.id, item_no=i, ledger_id=ledger_id, is_debit=particular.is_debit,
                                    amount=particular.amount, enterprise_id=project_enterprise_id)
                                voucher_particulars.append(_vp)
                                Voucher_logger.info("else _vp:%s" % _vp)
                    voucher.particulars = voucher_particulars
                    _db_session.add(voucher)
                _voucher.project_automation_status = PROJECT_CLONE_STATUS
                _db_session.add(_voucher)
                _db_session.commit()
                return True
            else:
                Voucher_logger.info("In General Voucher clone else condition")
                return False
        except Exception as e:
            _db_session.rollback()
            Voucher_logger.info("failed to clone the general voucher: %s", e)
            self.update_failed_voucher_status(voucher_id=voucher_id, enterprise_id=enterprise_id)
            raise e

    def update_failed_voucher_status(self, voucher_id=None, enterprise_id=None):
        try:
            _db_session = self.dao.db_session
            _voucher = _db_session.query(Voucher).filter_by(id=voucher_id, enterprise_id=enterprise_id).first()
            _db_session.begin(subtransactions=True)
            _voucher.project_automation_status = -1
            _db_session.add(_voucher)
            _db_session.commit()
        except Exception as e:
            Voucher_logger.info("failed to update voucher status: %s", e)

    def child_voucher_is_exist(self, voucher_id=None, enterprise_id=None):
        is_exists = False
        try:
            _db_session = self.dao.db_session
            Voucher_logger.info("voucher_id is exists: %s" % voucher_id)
            parent_voucher = _db_session.query(Voucher).filter_by(id=voucher_id, enterprise_id=enterprise_id).first()
            child_voucher = _db_session.query(Voucher).filter_by(parent_voucher_id=voucher_id).first()
            Voucher_logger.info("child_voucher is exists: %s" % child_voucher)
            if child_voucher:
                _db_session.begin(subtransactions=True)
                parent_voucher.project_automation_status = PROJECT_CLONE_STATUS
                _db_session.add(parent_voucher)
                _db_session.commit()
                is_exists = True
        except Exception as e:
            Voucher_logger.info("failed to check child voucher existing status: %s", e)
        return is_exists
