{% extends "stores/sidebar.html" %}
{% block grn %}
{% if access_level.view %}
<style>
    li.grn_side_menu a{
	    outline: none;
	    background-color: #e6983c !important;
	}
	.custom-file-input input {
		visibility: hidden;
		width: 250px
	}

	.custom-file-input:hover:before {
	    border: medium none;
	}
	.custom-file-input:active:before {
		background: #39658b;
	}
	.file-blue:before {
		content: 'Attach Invoice Copy';
		background-color: #39658b;
        font-weight: 30;
		padding: 6px 9px;
		color: #FFF;
		cursor: pointer;
		display: inline-block;
		font-family: "open_sansbold";
	}
	.file-blue:hover:before {
		border-color: #02b0e6;
	}
	.file-blue:active:before {
		background: #02b0e6;
	}

	#statusrow .selected label{
		color: #209be1;
	}

	.bootstrap-filestyle {
		display: none !important;
	}
	#invno + .custom-error-message {
		position: relative;
	}

	#id_gstin + .custom-error-message {
		position: relative;
	}

	.custom-table td.consolidated_price_column {
		vertical-align: top !important;
	}

	.consolidated_row td:first-child,
	.consolidated_row_dc td:first-child {
		cursor: pointer;
		color: #209be1 !important;
	}

	.consolidated_row .consolidated_material_name,
	.consolidated_row_dc .consolidated_material_name {
	    display: inline-block;
	    float: left;
	    width: 80%;
	    margin-left: 10px;
	    margin-top: -3px;
	}

	.consolidated_row .consolidated_material_name:hover,
	.consolidated_row_dc .consolidated_material_name:hover	{
		text-decoration: underline;
	}

	.grn_warning_message {
		color: #dd4b39;
	    float: right;
	    margin-top: -15px;
	}
	
	.grn_warning_message i {
		color: #dd4b39;
		cursor: pointer;
	    font-size: 10px;
	    border: solid 1px #dd4b39;
	    border-radius: 50px;
	    padding: 1px 4px 1px 4px;
	}	

	#issueyear ~ .btn-group .multiselect {
		border-radius: 4px 0 0 4px;
	}

	#issueno ~ .btn-group .multiselect {
		border-radius: 0 4px 4px 0;
		margin-left: -1px;
	}

	select.error-border ~ .btn-group .multiselect  {
		border: 1px solid #dd4b39 !important;
		background: #fdeded;
	}

	#statusrow div {
		display: inline-block;
		margin-right: 10px;
	}

	#statusrow i {
		font-size: 14px;
		font-weight: bold;
	}

	.ui-autocomplete {
		Z-index:10021 !important;
		max-width: 600px;
	}

	.td-oa-chosen + div.chosen-container {
	    position: relative;
	}

	#id-popOaTableBody .custom-error-message {
		position: relative;
	}

	#rejection-remarks-table input {
		padding: 8px;
	    height: 30px;
	    font-size: 12px;
	}

	.td_rejection_qty input,
	.td_rejection_debit input {
		text-align: right;
	}

	#materialtable th[name='price_col'] {
		width: 6%;
	}
	.unit_value{
		width:40px !important;
		padding: 6px !important;
		margin-top: -26px !important ;
		height: 25px;
		border-radius: 0 4px 4px 0;
        margin-right: 1px;
        background: #CCC;
        white-space: nowrap;
	    text-overflow: ellipsis;
	    overflow: hidden;
	}

	 .table > tbody > tr > td{
	    vertical-align : top !important;
	 }

	 #id-OANumbers .chosen-container-active {
		width: 127px !important;
	    position: absolute;
	    display: block;
	}
	.table-responsive{
	    overflow-x: visible;
	}

	#id-popTable tbody tr td .tree-view {
		position: relative;
	}

	#id-popTable tbody tr td .tree-view:before {
	    border-bottom: 1px solid #bbb;
	    content: "";
	    left: 6px;
	    position: absolute;
	    top: 6px;
	    width: 1.5em;
	}

	#id-popTable tbody tr td .tree-view:after {
	    border-left: 1px solid #bbb;
	    content: "";
	    left: 6px;
	    position: absolute;
	    top: -25px;
	    height: 4em;
	}

	#id-popTable .fa-plus:before,
	#id-popTable .fa-minus:before {
		border: solid 1px #333;
	    padding: 2px 3px 2px 3px;
	    border-radius: 3px;
	    font-size: 10px;
	}

	#id-popTable tr[data-toggle='open'] td.td-delete-row {
    opacity: 0.5;
    background: #ddd;
    pointer-events: none;
	}

	.unit_select_box{
		padding-right: 0px !important;
        padding-left: 0px !important;
		position: relative !important;
		margin-top: -26px !important;
        width: 50px !important;
        right: 0px !important;
        left: -15px;
		top: -8px;
	}

	.all_units_select_box{
		margin-top: 8px;
		margin-right: -15px;
	}

	.cmd_addmaterial{
		display: block;
		position: absolute;
		right:-3px;
		margin-top: -2px !important;
		border-radius:50px;


	}

</style>
<link rel="stylesheet" type="text/css" href="/site_media/css/erp_default.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/quality-inspection-report.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
<link rel="stylesheet" type="text/css" href="/site_media/css/erp_default.css?v={{ current_version }}">

<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-multiselect.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/tags.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/grn.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/party_list.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/make.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/material_selector.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/load_material.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/usage_report.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/quality-inspection-report.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/Component/projects.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/Component/locations.js?v={{ current_version }}"></script>


<div class="right-content-container main-container">
	<div class="page-title-container">
		<span class="page-title">New {{ template_title }}</span>
	</div>
	<div class="page-heading_new" style="padding: 0 30px;">
		<input type="hidden" id="id-page_header" value="{{ template_title }}"/>
		<input type="hidden" id="id-select_po_tag" value="true"/>
		<span class="page_header_grn"></span>
		{% if logged_in_user.is_super %}
			<a class="btn super_user_icon hide" onclick="editGrnNumber();" data-tooltip="tooltip" data-placement="bottom" data-title="Super User"  style="">
				<i class="fa fa-pencil"></i>
			</a>
			<div class="xsid_number_edit hide">
				<form class="form-inline" style="display: inline-block;" action="">
				    <div class="form-group">
				      	<input type="text" class="form-control" id="grn_financial_year" name="grn_financial_year" maxlength="5">
				    </div>
				    <div class="form-group">
					    <input type="text" class="form-control" id="grn_type" readonly="true" name="grn_type" maxlength="5">
				    </div>
				    <div class="form-group">
				      <input type="text" id="grn_number" name="grn_number" class="form-control" maxlength="6" onfocus="setNumberRangeOnFocus(this,6,0,true)">
				    </div>
				    <div class="form-group">
				      	<input type="text" class="form-control text-lowercase" onkeypress="validateStringOnKeyPress(this,event,'character');" onblur="validateStringOnBlur(this,event,'character');" id="grn_number_division" name="grn_number_division" maxlength="1" >
				    </div>
				    <div class="form-group super_edit_submit_icon">
				    	<span role='button' data-tooltip="tooltip" title="Save" class="super_edit_save" onclick="saveGrnNumber();"><i class="fa fa-check"  aria-hidden="true" style="color: green;"></i></span>
				    	<span role='button' data-tooltip="tooltip" title="Cancel" class="super_edit_cancel" onclick="discardEditGrnNumber();"><i class="fa fa-times" aria-hidden="true" style="color: red;"></i></span>
				    </div>
		  		</form>
		  		<div class="save_xsid_error_format hide">Required. Please check help tool for valid format.</div>
		  	</div>
		  	<div class="super_user_tool_tip hide"><span class="question-mark"></span></div>
		{%else%}
			<a class="btn super_user_icon hide" onclick="" style="color:#777;" data-tooltip="tooltip" data-placement="right" data-title="Only Super User can edit this field">
				<i class="fa fa-pencil"></i>
			</a>
		{% endif %}
		<a href="{{list_link}}" id="id-back_to_list" class="btn btn-add-new pull-right view_grn" data-tooltip="tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
		<span class="prev_next_container"></span>
		{% if module_access.icd %}
			{% if logged_in_user.is_super or logged_in_user|canView:'ICD' %}
				<div class="pull-right hide" id="id_grn_note_preview_div" >
					<div class="btn pull-right btn-warning-border" role="button" id="id_note_preview" style="margin-right: 7px;"> Note Preview </div>
				</div>
			{% endif %}
		{% endif %}
	</div>
	<div class="container-fluid">
		<div class="row">
			<div class="col-lg-12">
				<div class="content_bg">
					<div class="tab-content">
						<div>
							<div class="add_table">
								<div class="col-lg-12">
									<div class="row received_against_margin">
										<div class="col-lg-12 hide">
											<div class="sub-heading-content" style="padding: 0 0 15px" id="grn_no_label" hidden>
												<span class="font-bold mycolor-grey-6">GRN No: </span><span class="mycolor-blue-2 font-bold myfont-number" id="grnnolbl"></span>
											</div>
											<div class="sub-heading-content" style="padding: 0 0 15px" id="draft_grn_no_label" hidden>
												<span class="font-bold mycolor-grey-6">GRN Draft No: </span><span class="mycolor-blue-2 font-bold myfont-number" id="orderno"></span>
											</div>
										</div>
										{% if receipt_no != "" %}
										<div id="statusrow" class="col-lg-12" style="margin-bottom: 12px;">
											<div class="" id="chkApproved">
												<label>Approved</label>
											</div>
											<div class="grn_chk_class">
												<label><i class="fa fa-angle-right" aria-hidden="true"></i></label>
											</div>
											<div class="grn_chk_class" id="chkChecked">
												<label>ICD Checked</label>
											</div>
											<div class="grn_chk_class">
												<label><i class="fa fa-angle-right" aria-hidden="true"></i></label>
											</div>
											<div class="grn_chk_class" id="chkVerified">
												<label>ICD Verified</label>
											</div>
											<div class="grn_chk_class" id="chkAccounted_arrow">
												<label><i class="fa fa-angle-right" aria-hidden="true"></i></label>
											</div>
											<div class="grn_chk_class" id="chkAccounted">
												<label>Accounted</label>
											</div>
										</div>
										{% endif %}
									</div>
								</div>
								<div class="clearfix"></div>

								<div class="col-md-12 remove-padding">
									<div class="col-md-9 remove-padding">
										<input type="label" value="{{is_gate_inward_no_mandatory}}" class="txtbox10" id="is_gate_inward_no_mandatory" disabled="disabled"  width="30%" hidden="hidden">
										<input type="label" value="{{is_unique_in_fiscal_yr}}" class="txtbox10" id="is_unique_in_fiscal_yr" disabled="disabled"  width="30%" hidden="hidden">
										<input type="label" value="{{is_gate_inward_no_automated}}" class="txtbox10" id="is_gate_inward_no_automated" disabled="disabled"  width="30%" hidden="hidden">
										<input type="label" value="{{is_gate_inward_no_editable}}" class="txtbox10" id="is_gate_inward_no_editable" disabled="disabled"  width="30%" hidden="hidden">
										<input type="label" value="{{is_purchase_order_mandatory}}" class="txtbox10" id="is_purchase_order_mandatory" disabled="disabled"  width="30%" hidden="hidden">
										<input type="label" value="{{is_blanket_po}}" class="txtbox10" id="is_blanket_po_enabled" disabled="disabled"  width="30%" hidden="hidden">
										<input type="text" value="" class="textbox2" id="id_note_id"  hidden="hidden">
										<div class="col-sm-8 hide"  id="tags"><!-- Tags are hidden on the 2.16.3 release -->
											<label>Tags</label>
											<ul id="grn_tags_table" class="tagit-display form-control">
												<span class="text_for_tag">
													<input type="text" value="" class="form-control" name="grn_tags" id="id_grn_tag" placeholder ="Type your tags here" >
													<input type="text" value="" class="textbox2" id="grn_tag_value" maxlength="10" hidden="hidden">
												</span>
											</ul>
											<div class="col-sm-2 btn-margin hide">
												<input type="button" class="btn btn-save" id="add_grn_tag" value="+"/>
											</div>
										</div>
										<div class="col-md-4 form-group">
												<div class="component_project" style="margin: 0px 20px 0px 0px;" data-id="project" data-name="select" data-isSuper={{logged_in_user.is_super}} data-isEditDisable="{% if receipt_status == 3 %}true{% else %}false{% endif %}"></div>
											<label id="expRev" style="display: block;margin-top: 20px;">Revenue: <span id="revenue" style="margin-right: 10px;">0</span>Expense : <span id="expense" style="margin-right: 10px;">0</span> </label>
										</div>
										<div class="col-md-4 form-group">
											<div class="component_location" style="margin: 0px 10px 0px 0px;position: relative;"  data-value= {{ location_id }} data-id="grn-location" data-name="select" data-isSuper={{logged_in_user.is_super}} data-isEditDisable="{% if receipt_status == 3 %}true{% else %}false{% endif %}"></div>
										</div>
<!--										<div class="col-md-4 form-group">-->
<!--											<label>Project<span class="mandatory_mark"> *</span>-->
<!--												{% if logged_in_user.is_super %}-->
<!--													<a class="super_edit_field super_edit_for_draft hide" onclick="superEditGrnSelect(this);" data-tooltip="tooltip" data-placement="left" title="Super Edit">-->
<!--														<i class="fa fa-pencil super_edit_in_field"></i>-->
<!--													</a>-->
<!--												{%else%}-->
<!--													<a class="super_edit_field super_edit_for_draft hide" style="color:#777;" onclick="" data-tooltip="tooltip" data-placement="left" title="Only Super User can edit this field">-->
<!--														<i class="fa fa-pencil super_edit_in_field"></i>-->
<!--													</a>-->
<!--												{%endif%}-->
<!--											</label>-->
<!--											<select class="form-control chosen-select project_select_dropdown" name="select" id="project" onChange="validateProjectChange(this)">-->
<!--											<div id="add_new_project">-->
<!--												<option value="0"></option>-->
<!--												<option value="add_new_project" id="add_new_project">+ Add new </option>-->
<!--											</div>-->
<!--												<optgroup label="Frequently used">-->
<!--			                                        {% for j in frequent_projects %}-->
<!--													{% if forloop.first %}-->
<!--													<option value="{{ j.0 }}" selected="selected">{{ j.1 }}</option>-->
<!--												    {% else %}-->
<!--			                                        <option value="{{ j.0 }}">{{ j.1 }}</option>-->
<!--													{% endif %}-->
<!--			                                        {% endfor %}-->
<!--											    </optgroup>-->
<!--												<optgroup label="All">-->
<!--												{% for j in projects %}-->
<!--												<option value="{{ j.0 }}">{{ j.1 }}</option>-->
<!--												{% endfor %}-->
<!--											    </optgroup>-->
<!--											</select>-->
<!--										</div>-->
										<div class="col-md-4 form-group hide" id="div_pp_no">
											<label style="display: block;">Production Plan No.<span class="mandatory_mark"> *</span></label>
											<select class="form-control chosen-select pp_select_dropdown" name="select" id="id_wo" >
                                               <option value="0">-- Select an PP No --</option>
												<optgroup label="All">
												{% for j in wo_id %}
                                                    <option value="{{ j.0 }}">{{ j.1 }}</option>
												{% endfor %}
												</optgroup>
											</select>
										</div>
										<div class="clearfix"></div>
										{% if template_title == "Sales Return" %}
											<div class="hide">
												<select class="form-control" name="select" id="recagainst">
                                                    <option value="Sales Return">Sales Return</option>
											    </select>
											</div>
										{% else %}
											{% if template_title == "Internal Receipt" %}
												<div class="hide">
													<select class="form-control" name="select" id="recagainst">
	                                                    <option value="Issues">Issues</option>
												    </select>
												</div>
											{% else %}
												<div class="col-md-4 form-group">
													<label>Received Against
														{% if logged_in_user.is_super %}
															<a class="super_edit_field super_edit_for_draft hide" onclick="superEditGrnSelect(this);" data-tooltip="tooltip" data-placement="left" title="Super Edit">
																<i class="fa fa-pencil super_edit_in_field"></i>
															</a>
														{%else%}
															<a class="super_edit_field super_edit_for_draft hide" style="color:#777;" onclick="" data-tooltip="tooltip" data-placement="left" title="Only Super User can edit this field">
																<i class="fa fa-pencil super_edit_in_field"></i>
															</a>
														{%endif%}
													</label>
													<select class="form-control" name="select" id="recagainst">
														<option value="Purchase Order">Purchase</option>
														<option value="Job Work">Job Out</option>
														<option value="Job In">Job In</option>
														<option value="Delivery Challan" >Delivery Challan</option>
														<option value="Others">Others</option>
									                </select>
									            </div>
											{% endif %}
										{% endif %}
							            <div class="col-md-4 form-group" id="rec_from_div">
											<label>Received From<span class="mandatory_mark received_against_option"> *</span>
											{% if logged_in_user.is_super %}
												<a class="super_edit_field super_edit_for_draft hide" onclick="superEditGrnSelect(this);" data-tooltip="tooltip" data-placement="left" title="Super Edit">
													<i class="fa fa-pencil super_edit_in_field"></i>
												</a>
											{%else%}
												<a class="super_edit_field super_edit_for_draft hide" style="color:#777;" onclick="" data-tooltip="tooltip" data-placement="top" title="Only Super User can edit this field">
													<i class="fa fa-pencil super_edit_in_field"></i>
												</a>
											{%endif%}
											</label>
											<select class="form-control chosen-select party_select_dropdown" name="select" id="supplier">
												<div id="add_new_party">
													<option value="0"></option>
													<option value="add_new" id="add_new_employee" class="bold-option">+ Add new </option>
												</div>
												<optgroup label="Frequently used" class="">
													{% for j in frequent_dc_suppliers %}
														{% if forloop.first %}
														<option id="other_first" class="frequently_used_dc_supplier" value="{{ j.0 }}[::]{{ j.2 }}" >{{ j.1 }}</option>
													    {% else %}
				                                        <option value="{{ j.0 }}[::]{{ j.2 }}" class="frequently_used_dc_supplier">{{ j.1 }}</option>
														{% endif %}
			                                        {% endfor %}

													{% for j in frequent_suppliers %}
														{% if forloop.first %}
														<option value="{{ j.0 }}[::]{{ j.2 }}" class="frequently_used_supplier" selected="selected">{{ j.1 }}</option>
													    {% else %}
				                                        <option value="{{ j.0 }}[::]{{ j.2 }}" class="frequently_used_supplier">{{ j.1 }}</option>
														{% endif %}
			                                        {% endfor %}

													{% for j in other_frequent_suppliers %}
														{% if forloop.first %}
														<option id="other_first" value="{{ j.0 }}[::]{{ j.2 }}" class="other_frequently_used_supplier">{{ j.1 }}</option>
													    {% else %}
				                                        <option value="{{ j.0 }}[::]{{ j.2 }}" class="other_frequently_used_supplier">{{ j.1 }}</option>
														{% endif %}
			                                        {% endfor %}
													{% for j in frequent_job_work_suppliers %}
														{% if forloop.first %}
														<option id="other_first" value="{{ j.0 }}[::]{{ j.2 }}" class="frequently_used_jobwork_supplier">{{ j.1 }}</option>
													    {% else %}
				                                        <option value="{{ j.0 }}[::]{{ j.2 }}" class="frequently_used_jobwork_supplier">{{ j.1 }}</option>
														{% endif %}
			                                        {% endfor %}
													{% for j in frequent_job_work_in_party_list %}
														{% if forloop.first %}
														<option id="other_first" value="{{ j.0 }}[::]{{ j.2 }}" class="frequent_job_work_in_party_list">{{ j.1 }}</option>
													    {% else %}
				                                        <option value="{{ j.0 }}[::]{{ j.2 }}" class="frequent_job_work_in_party_list">{{ j.1 }}</option>
														{% endif %}
			                                        {% endfor %}
													{% for j in frequent_sales_return_suppliers %}
														{% if forloop.first %}
														<option id="other_first" value="{{ j.0 }}[::]{{ j.2 }}" class="frequently_used_salesreturn_supplier">{{ j.1 }}</option>
													    {% else %}
				                                        <option value="{{ j.0 }}[::]{{ j.2 }}" class="frequently_used_salesreturn_supplier">{{ j.1 }}</option>
														{% endif %}
			                                        {% endfor %}
												</optgroup>
												<optgroup label="All">
													{% for j in suppliers %}
								                    <option value="{{ j.0 }}[::]{{ j.2 }}">{{ j.1 }}</option>
								                    {% endfor %}
											    </optgroup>
											</select>
							            </div>
							            <div class="col-md-4 form-group invoicerow remove-padding">
							            	<!-- Temporarily hidden. Need for next release -->
							            	<div class="btn-browse-container" style="width: 300px;">
							            		<label>&nbsp;</label>
							            		<div class="cloud-upload-container grn_upload_a1 hide">
										            <form action='' id="gcs_upload_general" name="gcs_upload_grn_a1" class="google_upload_form" method="POST" enctype='multipart/form-data'>
											            <input type='file' name='file' onChange="convertFileToBase64(this)" />
												  	    <input type='submit' value='Upload File' />
										            </form>
										            <input type="hidden" class="file_upload_json" name="file_upload_json" value="">
							            		</div>
										            {% if attachment_file_name %}
									                 	<div class="btn-browse active" data-tooltip="tooltip" >
											                <input type="hidden" class="base64-file-value base64" data-filename="{{ attachment_file_name }}" data-extension="{{ attachment_file_extension }}" value="{{ attachment_data }}">
											                 <i class="fa fa-file" aria-hidden="true"></i>
								            			    <span class="browsed-text" style="margin-left: 15px;" data-default="DC / INVOICE COPY">{{ attachment_file_name }}</span>
										                     <div id='loadingmessage2_ie' style="margin-left:180px;margin-top:-20px">
																<img src='{{ MEDIA_URL }}../images/ajax-loader.gif'/>
															</div>
														</div>
													{% else %}
										              	<div class="btn-browse " data-tooltip="tooltip" >
															<input type="hidden" class="base64-file-value base64" data-filename="">
											              	<i class="fa fa-file" aria-hidden="true"></i>
						            			    		<span class="browsed-text" style="margin-left: 15px;" data-default="DC / INVOICE COPY">DC / INVOICE COPY</span>
											                 <div id='loadingmessage2_ie' style="margin-left:180px;margin-top:-20px">
																<img src='{{ MEDIA_URL }}../images/ajax-loader.gif'/>
															</div>
														</div>
													{% endif %}
							            		<i class="fa fa-times-circle btn-browse-remove" onClick="removeBrowsedFile(this)" aria-hidden="true"></i>
							            	</div>
										</div>
										<div style="width: 100%; display: inline-block;" class="indent_details_position">
								            <div class="col-md-4 form-group multiselect_option" id="po_div">
												<label id="po_name_display">Purchase Order No</label>{% if is_purchase_order_mandatory %}<span class="mandatory_mark received_against_option"> *</span>{% endif %}
												<select class="form-control" name="select" id="pos" multiple="multiple"></select>
												<input type="label" value="" class="txtbox10" id="po_details" disabled="disabled"  width="30%" hidden="hidden">
												<input type="label" value="" class="txtbox10" id="po_details_delete" disabled="disabled"  width="30%" hidden="hidden">
									            <input type="label" value="" class="txtbox10" id="issue_details_delete" disabled="disabled"  width="30%" hidden="hidden">
								            </div>
								            <div class="col-md-4 form-group" id="div_indtype">
												<label>Purchase Account (Indent Type)<span class="mandatory_mark"> *</span></label>
												<select class="form-control" name="select" id="purchase_account">
														{% for j in purchase_account %}
										                <option value="{{ j.0 }}">{{ j.1 }}</option>
										                {% endfor %}
									            </select>
								            </div>
								            <div class="col-md-4 form-group multiselect_option hide" id="party_div">
												<label id="party_name_display">Party DC No<span class="mandatory_mark"> *</span></label>
												<select class="form-control" name="select" id="party_DcNo" multiple="multiple">
												</select>
												<input type="label" value="" class="txtbox10" id="party_details" disabled="disabled"  width="30%" hidden="hidden">
												<input type="label" value="" class="txtbox10" id="party_details_delete" disabled="disabled"  width="30%" hidden="hidden">
										    </div>
								        </div>
							            
							            <div class="col-md-4 form-group for_issue" id="div_issued_to">
											<label>
												<span id="id_issued_to_label">Issued To<span class="mandatory_mark"> *</span></span>
											</label>
											<select class="form-control chosen-select" name="invoice-issued_to" id="id_invoice-issued_to" >
												<span><option value="0" hidden="hidden">Select an option</option></span>
													  <optgroup label="All">
														{% for j in issue_to %}
														        <option value="{{ j.0 }}">{{ j.0 }}</option>
														{% endfor %}
													  </optgroup>
											</select>
							            </div>
							            <div class="col-md-8 form-group multiselect_option" id="div_issueno">
											<label style="display: block;">Issue No<span class="mandatory_mark"> *</span></label>
											<div class="col-md-6 remove-padding" style="max-width: 170px">
												<select class="form-control" name="selectyear" id="issueyear" multiple="multiple"></select>
											</div>
											<div class="col-md-6 remove-padding" style="max-width: 230px">
												<select class="form-control" name="select" id="issueno" multiple="multiple"></select>
											</div>
											<input type="label" value="" class="txtbox10" id="issueno_details" disabled="disabled"  width="30%" hidden="hidden">
											<input type="label" value="" class="txtbox10" id="issueno_details_delete" disabled="disabled"  width="30%" hidden="hidden">
							            </div>

									</div>

									<div class="col-md-3">
										<div class="checkbox remove-padding" style="margin-top: 24px; padding-left: 20px !important;" id="div_goods_already_received">
											<input type="checkbox" name="goods_already_received" id="goods_already_received" class="checkbox" onchange="changeGoodsAlreadyReceivedEvent()">
											<label for="goods_already_received">Goods Already Received</label>
										</div>
										<label>&nbsp;</label>
										<table border="0" class="side-table" style="float: right;">
											<tr id="div_dc_invoice">
												<td class="side-header" style="width: 150px;">
													Received As
												</td>
												<td class="side-content text-center">
													<div id="switch_dc_no" class="switch-radio-button btn-group" style="max-width: 250px;">
														<a style="padding: 4px;" class="dc noActive" data-toggle="dc_invoice" data-title= "1" data-tooltip="tooltip" title="DC">DC</a>
														<a style="padding: 4px;" class="invoice noActive" data-toggle="dc_invoice" data-title="2" data-tooltip="tooltip" title="Invoice">Invoice</a>
													</div>
													<input type="hidden" name="select" id="dc_invoice" value="">
												</td>
											</tr>
											<tr class="invoicerow">
												<td class="side-header" style="width: 150px;">
													<span class="party_dc_text">Party DC No<span class="mandatory_mark"> *</span></span>
												</td>
												<td class="side-content">
													<input type="text" value="" class="form-control" id="invno" maxlength="50" width="30%" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" placeholder="Enter DC No">
												</td>
											</tr>
											<tr class="invoicerow">
												<td class="side-header party_date_text" style="width: 150px;">
													Party DC Date
												</td>
												<td class="side-content side-content-datepicker">
													<input type="text" id="invdate" class="form-control hide" />
													<input type="text" class="form-control custom_datepicker till-today" autocomplete="off" placeholder="Select Date" readonly="readonly" id="invdateNew">
													<i class="glyphicon glyphicon-calendar custom-calender-icon pull-right"></i>
												</td>
											</tr>
											<tr class="invoicerow">
												<td class="side-header" style="width: 150px;">
													Received Through
												</td>
												<td class="side-content">
													<select class="form-control" name="select" id="recthrough" style="border: none; padding: 0; height: 18px; box-shadow: 0 0;">
														<option value="1">Courier</option>
														<option value="2">Transport</option>
										            </select>
												</td>
											</tr>
											<tr class="invoicerow">
												<td class="side-header" style="width: 150px;">
													E-Commerce GSTIN
												</td>
												<td class="side-content side-content-datepicker">
													<input type="text" class="form-control " autocomplete="off" placeholder="Enter GSTIN"  id="id_gstin" maxlength="15" onkeypress="validateStringOnKeyPress(this,event,'alphanumeric');" onblur="validateStringOnBlur(this,event,'alphanumeric');">
												</td>
											</tr>
										</table>
									</div>
								</div>
								<div class="clearfix"></div>
								<div class="col-sm-6" style="position: absolute; right:15px; margin-top: -30px;">
									<div style="position: absolute; right: 15px; z-index: 1; margin-top: 30px;">
										<a href="#" role="button" id="id-invoicedItems" class="btn btn-save btn-margin-1 hide" onclick="javascript:showInvoicedItems();">Items Invoiced</a>
										<a href="#" role="button" id="id-materialUsage" class="btn btn-save btn-margin-1 hide" onclick="javascript:showGrnUsageReport();">Material Usage</a>
									</div>
								</div>
								<div id="stock_materials">
									<div class="col-sm-12" style="margin-top: 5px;">
										<ul class="nav nav-tabs">
										  <li id="mat_received" class="active"><a data-toggle="tab" href="#tab_invoice"><h5>Materials Received</h5></a></li>
										  <li id="mat_returned"><a data-toggle="tab" href="#tab_dc"><h5>Materials Returned</h5></a></li>
										  <li style="margin-top: 20px"><span class='service-item-flag'></span> - Service</li>
										</ul>
										<input type="hidden" id="materialTableRowCount" value="0"/>
										<input type="hidden" id="dcMaterialTableRowCount" value="0"/>
										<div class="tab-content item-particulars-add-container">
											<div id="tab_invoice" class="tab-pane fade in active">
												<div class="clearfix"></div>
											    <div class="table-responsive">
													<table class="table table table-striped custom-table custom-table-large table-bordered search_result_table tableWithText quality-inpection-table" id="materialtable">
														<thead class="th-vertical-center">
															<tr>
																<th rowspan="2" valign="center">PO No </th>
																<th rowspan="2">Material</th>
																<th colspan="6">Quantity</th>
																<th rowspan="2">Unit</th>
																<th rowspan="2" >HSN/ SAC</th>
																<th rowspan="2">Price/Unit</th>
																<th rowspan="2">Discount %</th>
																<th rowspan="2">Total Price</th>
																<th rowspan="2">CGST (%)<span class="th-sub-heading bracket-enclosed">Amount</span></th>
																<th rowspan="2">SGST (%)<span class="th-sub-heading bracket-enclosed">Amount</span></th>
																<th rowspan="2">IGST (%)<span class="th-sub-heading bracket-enclosed">Amount</span></th>
																<th rowspan="2" hidden="hidden">Blanket PO</th>
																<th rowspan="2" hidden="hidden">Valid from Date</th>
																<th rowspan="2" hidden="hidden">Valid till Date</th>
															</tr>
															<tr>
																<th>DC/Inv<span class="th-sub-heading bracket-enclosed">PO Pending</span></th>
																<th>Received<span class="th-sub-heading bracket-enclosed">Shortage</span></th>
																<th>Accepted<span class="th-sub-heading bracket-enclosed">Rejected</span></th>
															</tr>
														</thead>
														<tbody class='item-for-goods hide'></tbody>
														<tbody class='item-for-service hide'></tbody>
														<tbody class="add_item_details" id="materialrow">
															<tr class="tr-add_item_details">
																<td class="col-sm-2 OANumbers hide"></td>
																<td class="td_po_no"></td>
																<td>
																	<div class="material_name">
																		<input type="text" value="" class="form-control" id="materialrequired" placeholder ="Select Material" maxlength="100" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar');" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar');">
																		<input type="hidden" id="material_id_hidden" maxlength="10" value="">
																		<input type="hidden" value="" class="" id="material_is_service" placeholder="" hidden="hidden">
																		<input type="hidden" id="material_id" maxlength="10" value="">
																		<input type="hidden" id="materialprice1" maxlength="10" value="">
																		<input type="hidden" id="id-makeId" value="1"/>
																		<input type="hidden" id="grn_status" value="0"/>
																		<span class="material-removal-icon removal-icon-table hide" style="margin-top: -26px;width: 40px;height: 26px;">
																			<i class="fa fa-times"></i>
																		</span>
																		<div class="form-group checkbox chk-margin" style="margin-top: 10px;margin-left: -10px;margin-bottom: -4px;">
																			<input type="checkbox" class="chkcase" name="case" id="chkfaulty"  value="" />
																			<label for="chkfaulty">Is Faulty</label>
																		</div>
																	</div>
																</td>
																<td class="td_qty" colspan="3">
																	<input type="text" id="qty" class="form-control" placeholder="Enter Quantity" maxlength="16" onfocus="setNumberRangeOnFocus(this,12,3)"  />
																	<label id="unit_display" class="unit_display unit_display-small unit_value pull-right" style="">&nbsp;</label>
																	<div class="alternate_unit_select_box hide" style="position: relative;margin-top: 8px;margin-right: -16px">
																		<select class="form-control unit_select_box" name="select" id="id_material-alternate_units" >
																		</select>
																	</div>
																	<div class="all_units_select_box hide">
																		<select class="form-control unit_select_box" name="select" id="id_material-all_units">
																		</select>
																	</div>
																	<div class=" unitChoices-container hide">
																		<select class="form-control" name="select" id="unitChoices" ></select>
																	</div>
																</td>
																<td>
																	<div class="hsn-wrapper">
																		<input type="text" id="hsn_code" class="form-control hsn_code" placeholder="Enter HSN/SAC" maxlength="9" onchange="validateHsnWithSuggestion(this);" onkeypress="validateStringOnKeyPress(this,event,'hsn_specialChar');" onblur="return validateStringOnBlur(this, event, 'hsn_specialChar');" />
																	</div>
																</td>
																<td class="td_others"></td>
																<td class="td_others"></td>
																<td class="td_gst"></td>
																<td class="td_gst"></td>
																<td class="td_gst"></td>
																<td style="text-align:center;">
																	<button type="submit" class="btn btn-save btn-margin-1 cmd_addmaterial"  id="cmdadd">+</button><br>
																	<button type="button" id="explode_bom" onclick="javascript:loadBomMaterials();" class="btn btn-save btn-margin-1 hide" value="BoM" style="display: inline-block;position: absolute;right: -14px;margin-top: 21px;">BoM</button>
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											</div>
											<div id="tab_dc" class="tab-pane fade">
												<div class="table-responsive">
													<table class="table table table-striped custom-table custom-table-large table-bordered search_result_table tableWithText quality-inpection-table" id="dc_materialtable">
														<thead class="th-vertical-center">
														<tr>
															<th align="center" valign="middle" rowspan="2" width="5%" class="td-material">S.No</th>
															<th align="center" valign="middle" rowspan="2" width="10%" id="dc_issue_title" class="td-material">DC No</th>
															<th align="center" valign="middle" rowspan="2" width="20%" class="material-header">Material</th>
															<th align="center" valign="middle" colspan="3" class="taxable">Quantity</th>
															<th align="center" valign="middle" rowspan="2" width="10%">HSN/SAC</th>
														</tr>
														<tr class="taxable">
															<th >DC<span class="th-sub-heading bracket-enclosed po-pending" name="po-pending">PO Pending</span></th>
															<th >Received <span class="th-sub-heading bracket-enclosed">Shortage</span></th>
															<th >Accepted <span class="th-sub-heading bracket-enclosed">Rejected</span></th>
														</tr>
														</thead>
														<tbody class='item-for-goods hide'></tbody>
														<tbody class='item-for-service hide'></tbody>
													</table>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="col-lg-7">
									<div class="row from-group" id="gate_row">
										<div class="col-lg-4 col-md-4 col-sm-4 col-xs-4">
											<label>Gate Inward No{% if is_gate_inward_no_mandatory %}<span class="mandatory_mark received_against_option"> *</span>{% endif %}</label>
											<input class="form-control" type="text" id="inwardno" placeholder="Enter Inward No" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar');" maxlength="20">
										</div>
										<div class="col-lg-4 col-md-4 col-sm-4 col-xs-4">
											<label>Gate Inward Date<span class="mandatory_mark received_against_option"> *</span></label>
											<input type="text" id="old_inwdate" class="hide"/>
											<input type="text" id="inwdate" class="hide" placeholder="Select Date"  />
											<input type="text" class="form-control custom_datepicker till-today" autocomplete="off" placeholder="Select Date" readonly="readonly" id="inwdateNew">
											<i class="glyphicon glyphicon-calendar custom-calender-icon"></i>
										</div>
										<div class="col-lg-4 col-md-4 col-sm-4 col-xs-4">
											<label>Inspector Name<span class="mandatory_mark received_against_option"> *</span></label>
											<select class="form-control chosen-select" name="select" id="inspector" >
												<span><option value="0" hidden="hidden" disabled="disabled">Select an option</option></span>
												      <option value="add_new_inspector" id="add_new_inspector" class="bold-option">+ Add new </option>
												      {% if inspector.0 %}
														  <optgroup label="Frequently used">
															  {% for j in inspector.0%}
															    {% if forloop.first %}
															    <option value="{{ j.0|lower }}" selected="selected">{{ j.0 }}</option>
															    {% else %}
															    <option value="{{ j.0|lower }}">{{ j.0 }}</option>
															    {% endif %}
															  {% endfor %}
														  </optgroup>
													   {% endif %}
													  {% if inspector.1 %}
														  <optgroup label="All">
															{% for j in inspector.1 %}
															<option value="{{ j|lower }}">{{ j }}</option>
															{% endfor %}
														  </optgroup>
													  {% endif %}
											</select>
										</div>
									</div>
									<div class="row" id="spacer_1">&nbsp;</div>

									<div class="row" id="div_currency_details">
										<div class="col-sm-4 form-group">
											<label>Currency
											{% if logged_in_user.is_super %}
												<a class="super_edit_field super_edit_for_load" onclick="superEditGrnSelect(this);" data-tooltip="tooltip" data-placement="left" title="Super Edit">
													<i class="fa fa-pencil super_edit_in_field"></i>
												</a>
											{%else%}
												<a class="super_edit_field super_edit_for_load" style="color:#777;" onclick="" data-tooltip="tooltip" data-placement="left" title="Only Super User can edit this field">
													<i class="fa fa-pencil super_edit_in_field"></i>
												</a>
											{%endif%}
											</label>
											<select class="form-control currency_chosen_select" name="currency" id="id_currency">
												{% for currency in currency_list %}
												<option value="{{ currency.id }}">{{ currency.code }}</option>
												{% endfor %}
											</select>
                                            <div id="div_con_rate" class="conversion_rate_container hide">
                                                <span class="currency_convertor_section">
                                                    <span>1</span>
                                                    <span class="converted_currency_txt"></span>
                                                    <span>=</span>
                                                </span>
                                                <span><input type="text" id="currency_conversion_rate" class="form-control txt_conversion_rate" value="1.00" maxlength="16" onfocus="setNumberRangeOnFocus(this,10,5)"/></span>
                                                <span class="base_currency_txt"></span>
                                            </div>
											<input type="hidden" name="home_currency" id="id_home_currency" value="{{home_currency}}">
										</div>

										<div class="col-sm-3 form-group addDc_Invoice">
											<label>Taxes Applicable</label>
											<select class="form-control chosen-select" name="po_taxes" id="id_po_tax"></select>
										</div>
										<div class="col-sm-1 form-group addDc_Invoice">
											<span class="btn btn-add-tax" id="add_po_tax" data-tooltip="tooltip" title="" data-original-title="Add tax">
												<i class="fa fa-angle-double-right" aria-hidden="true"></i>
											</span>
										</div>
									</div>
									<div class="row"></div>
									<div class="row">
										<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 form-group hide" id="rejected-remarks">
											<label>Rejection Remarks<span class="mandatory_mark"> *</span></label>
											<textarea id="txtrejremarks" class="form-control" placeholder="Enter Rejection Remarks" rows="3" maxlength="300" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')"
											onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')"></textarea>
										</div>
										<div class="col-sm-6 form-group">
											<label>Remarks</label>
											<div class="remarks_count_link remarks_count disabled" onclick="remarks_history();">
												<span class="remarks_counter">No</span><span> remarks</span>
											</div>
											<textarea id="txtremarks" class="form-control" placeholder="Additional Comments" rows="3" 
											maxlength="300"	onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')"
											onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')"></textarea>
										</div>
									</div>
								</div>
								<div class="col-lg-5 view_table_content" id="inv_det" >
									<div class="row" id="packing_row">
										<div class="col-lg-1">&nbsp;</div>
										<div class="col-lg-6 checkbox text-right">
											<input type="checkbox" id="chk_packing"/>
											<label for="chk_packing" class="text-center">Packing & Forwarding</label>
										</div>
										<div class="col-sm-5">
											<input type="text" id="packing_charges" class="form-control text-right" value="0.00"
												   disabled="true" maxlength="16" onfocus="setNumberRangeOnFocus(this,13,2)" onblur="Calculate();" />
										</div>
									</div>
									<div class="row" id="total_row">
										<div class="col-lg-4">&nbsp;</div>
										<div class="col-lg-3 text-right">Total</div>
										<div class="col-lg-5">
											<input class="form-control text-right" type="text" id="txttotal" value="0.00"
											   disabled="true">
										</div>
									</div>
									<div class="row">
										<div class="col-lg-1">&nbsp;</div>

									</div>
									<div class="row" id="taxrow">
										<div class="col-lg-2">&nbsp;</div>
										<div class="col-lg-10 po_taxes_table">
											<table id="po_taxes_table"  border="0" width="100%" align="right">
											</table>
										</div></div>
									<div class="row">&nbsp;</div>
									<div class="row" id="transport">
										<div class="col-lg-1">&nbsp;</div>
										<div class="col-lg-6 checkbox text-right">
											<input type="checkbox" id="chk_transport" style="margin-top:0px !important;"/>
											<label for="chk_transport" class="text-center">Transport/ Freight</label>
										</div>
										<div class="col-lg-5">
											<input type="text" id="transport_charges" class="form-control text-right" value="0.00"
												   disabled="true" maxlength="16" onfocus="setNumberRangeOnFocus(this,13,2)" onblur="Calculate();" />
										</div>
									</div>
									<div class="row" id="others">
										<div class="col-lg-4">&nbsp;</div>
										<div class="col-lg-3 text-right">Others</div>
										<div class="col-lg-5" style="padding-bottom: 5px;">
											<input id="txtothers" class="form-control text-right" maxlength="16" onfocus="setNumberRangeOnFocus(this,13,2)" onblur="Calculate();" value="0.00" />
										</div>
									</div>
									<div class="row" id="rounded_off">
										<div class="col-lg-4">&nbsp;</div>
										<div class="col-lg-3 text-right">Round off</div>
										<div class="col-lg-5" style="padding-bottom: 5px;">
											<input type="text" id="txtround_off" class="form-control text-right" maxlength="8" onfocus="setNumberRangeOnFocus(this,5,2,false,true)" onblur="Calculate();" value="0.00" />
										</div>
									</div>
										<div class="row" id="grand_tot">
										<div class="col-lg-4">&nbsp;</div>
										<div class="col-lg-3 text-right">Grand Total</div>
										<div class="col-lg-5" style="padding-bottom: 5px;">
											<input id="txtnetvalue" readonly tabindex="-1" class="form-control text-right" />
										</div>
									</div>
									<div class="row form-group" id="duty_passed">
										<div class="col-lg-4">&nbsp;</div>
										<div class="col-lg-3 text-right"><span class="po_title_txt">Duty Passed</span></div>
										<div class="col-lg-5" style="padding-bottom: 5px;">
											<input type="text" id="txt_duty_passed" class="form-control text-right" maxlength="16" onfocus="setNumberRangeOnFocus(this,13,2)" value="0.00" />
										</div>
									</div>
									<input type="text" value="" class="" id="txtproject" maxlength="10" hidden="hiddden">
									<input type="text" value="" class="" id="price1" maxlength="10" hidden="hiddden">
								</div>
							</div>
							<div class="col-sm-12">
								<div class="text-right">
									<form id="pdf_generation" method="POST" action="/erp/stores/grn/generate_documents/">{% csrf_token %}
									<div class="material_txt">
										{% if access_level.edit %}
										<input type="button" class="btn btn-save" value="Save" id="cmdSavegrn"/>
											{% if receipt_no != "" %}
												{% if access_level.approve %}
													<input type="button" class="btn btn-save btn-special-update" value="Update" id="cmdUpdateGRN" disabled="disabled" />
													<input type="button" class="btn btn-save btn-save-update" value="Save & Approve" id="cmdSaveAndApprove" />
												{% else %}
													{% if receipt_status = 0 %}
														<input type="button" class="btn btn-save btn-update" value="Update" id="cmdUpdateGRN" disabled="disabled" />
													{% endif %}
												{% endif %}
											{% endif %}
										{% endif  %}
										<input type="hidden" name="receipt_no" id="id_receipt_no" value="{{receipt_no}}"/>
										<input type="hidden" name="grn_save_approve_status" id="id_grn_save_approve_status" value="0"/>
										{% if access_level.approve %}
										<span class="btn btn-save" id="view_receipt_document">Approve /Reject</span>
										{% endif %}
									</div>
									</form>
								</div>
							</div>
							<div class="row">&nbsp;</div>

							<div id="add_inspector"  class="modal fade"  role="dialog">
								<div class="modal-dialog">
							        <!-- Modal content-->
								    <div class="modal-content">
										<div class="modal-header">
											<button type="button" class="close" data-dismiss="modal">&times;</button>
											<h4 class="modal-title">Add Inspector</h4>
										</div>
										<div class="modal-body">
											<form class="form-horizontal" method="post"  id="add_purpose_form" action="">{% csrf_token %}
												<div class="form-group">
													<label class="control-label col-sm-3" >Inspector Name<span class="mandatory_mark"> *</span> </label>
													<div class="col-sm-6">
														<input  class="form-control" autocomplete="off" id="id_inspector" placeholder="Enter name" name="inspector" maxlength="30" data-enterkey-submit='add_inspector_button' >
														<div class="duplicate_inspector" hidden="hidden" style="font-size: 12px; color: #dd4b39;  position: absolute; letter-spacing: 0.30px; line-height: 20px;">Name already exists</div>
													</div>
													<button  id="add_inspector_button" type="button" class="btn btn-save">Add</button>
												</div>
										    </form>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="clearfix"></div>
						<div id="invoice_modal" class="modal fade" role="dialog" tabindex="-1">
							<div class="modal-dialog" style="width: 730px;">
								<div class="modal-content">
									<div class="modal-header">
										<button type="button" class="close" data-dismiss="modal">&times;</button>
										<h4 class="modal-title">Invoice</h4>
									</div>
									<div class="modal-body">
										<div id="invoice" class="full_txt_width">
											<object width="700" height="500" id="invoice_doc" data="" ></object>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="hide">
		<form id="id-edit_receipt_form" method="POST">{% csrf_token %}
			<input type="hidden" name="receipt_no" id="id-edit_receipt_no" value="" />
		</form>
	</div>
</div>

<div id="rejected-remarks-modal" class="modal fade" role="dialog" data-backdrop="static" data-keyboard="false">
  	<div class="modal-dialog modal-lg">
	    <div class="modal-content">
      		<div class="modal-header">
        		<!-- <button type="button" class="close" onclick="closeRejectedRemarksModal()">&times;</button> -->
        		<h4 class="modal-title">REJECTION PROFILE</h4>
      		</div>
      		<div class="modal-body">
      			<input type="hidden" id="current-editable-id" />
        		<table class="table table-bordered custom-table" id="rejection-remarks-table">
		    		<thead>
			    		<tr>
			    			<th style="width: 450px;">Reason</th>
			    			<th>Quantity</th>
			    			<th>Debit</th>
			    		</tr>
			    	</thead>
			    	<tbody></tbody>
		    	</table>
		    	<a role="button" class="pull-right" onclick="addRejectionProfileRow()">+ ADD</a>
      		</div>
      		<div class="modal-footer">
        		<button type="button" class="btn btn-save" onclick="closeRejectedRemarksModal()">Done</button>
      		</div>
    	</div>
  	</div>
</div>
<div id="grn_note_preview-modal" class="modal fade" role="dialog" data-backdrop="static" data-keyboard="false">
  	<div class="modal-dialog modal-lg">
	    <div class="modal-content">
      		<div class="modal-header">
		        <button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Note Preview</h4>
      		</div>
      		<div class="modal-body">
				<div id="id-invoice_data" class="full_txt_width invoice-table-height">
					<table border="0" width="100%" >
						<tr>
							<td>Party Name:</td>
							<td><label id="party_name_inv"  align="left"></label></td>
							<td>Receipt No:</td>
							<td><label id="grn_no_inv" align="left"></label></td>
						</tr>
						<tr>
							<td>Invoice No:</td>
							<td><label id="inv_no_inv" align="left"></label></td>
							<td>Invoice Date:</td>
							<td><label id="inv_date_inv" align="left"></label></td>
							<td>Invoice Value:</td>
							<td><label id="inv_value_inv"  align="left"></label></td>
						</tr>
					</table>
					<table class="table custom-table table-striped table-bordered custom-table-large " id="grn_note_preview_table" width="100%">
						<thead>
							<tr>
								<th rowspan="2" width="3%">S. No</th>
								<th rowspan="2">Description</th>
								<th rowspan="2">Reason</th>
								<th rowspan="2" width="8%">Quantity</th>
								<th rowspan="2" width="16%">Rate /<br> Unit</th>
								<th rowspan="2" width="10%">Amount </th>
								<th rowspan="2" width="6%">CGST</th>
								<th rowspan="2" width="6%">SGST</th>
								<th rowspan="2" width="6%">IGST</th>
							</tr>
						</thead>
						<tbody></tbody>
					</table>
				</div>
			</div>
		    <div class="modal-footer">
		        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
	        </div>
	    </div>
    </div>
</div>
{% include "modal-window/quality-inspection.html" %}
{% include "stores/grn_document.html" %}
{% include "attachment_popup.html" %}
{% include "masters/add_project_modal.html" %}
{% include "masters/add_material_modal.html" %}
{% include "modal-window/note_preview.html" %}
{% include "masters/add_party_modal.html" %}

<!-- /#wrapper -->
<script>
var event_action = "Create";
var [CGST_rates, SGST_rates, IGST_rates] = ["", "", ""];
var r_count = 0; // TODO this global variable has to be modified
var moduleName = "GRN";

$(window).load(function() {
	actualProjectsBudget($('#project').val(),$('#project').find(':selected').attr('project-type'));
    $('#project').change(function() {
            actualProjectsBudget($(this).val(),$('#project').find(':selected').attr('project-type'));
    });

	enterKeySubmitEvent();
	$('.nav-pills li').removeClass('active');
	$('.nav-pills li').removeClass('active');
	if ($(".page-title").text().indexOf("Internal Receipt") >=0) {
		$("#li_irn").addClass('active');
	} else if ($(".page-title").text().indexOf("Sales Return") >=0) {
		$("#li_sr").addClass('active');
	} else {
		$("#li_receipt").addClass('active');
	}

	setTimeout(function(){
		enableEditButtonInit();
		$('#cmdUpdateGRN').attr('title', 'No changes detected.');
	},1000);
});

$(document).ready(function() {
	{% if received_against %}
	   console.log('on rready event');
		var receivedAgainst = "{{ received_against }}";
		var receiptId = "{{ receipt_no }}";
		if ("Issues" == receivedAgainst) {
			moduleName = "IRN";
		} else if ("Sales Return" == receivedAgainst) {
			moduleName = "SR";
		}
		if (receiptId == "" && ["Issues", "Sales Return"].indexOf(receivedAgainst) == -1) {
			receivedAgainst = "Purchase Order";
		}
		$("#recagainst").val(receivedAgainst);
		$("#recagainst").trigger("chosen:updated");
	{% endif %}
	initializePage();
	{% if receipt_no == "" %}
		{% if template_title == "Goods Receipt" %}
			// TODO OCR concept in future
		{% endif %}
		$('#loading').hide();
	{% else %}
		loadReceipt(receiptId);
    {% endif %}
    $(".new-tour").addClass('hide');
});

function enableEditButtonInit(){
	$('body').on('change keypress paste', ':input', function(e) {
		if($(this).attr("id") !="materialrequired" && $(this).attr("id") !="qty" && $(this).attr("id") !="unitChoices" &&  $(this).attr("id") !="chkfaulty" && $(this).attr("id") !="id_po_tax") {
		    enableEditButton();
	    }
	});

	$('body').on('keyup', ':input', function(e) {
		 if(e.which == 8 || e.which == 46) {
		    enableEditButton();
		 }
	});

	$( "#switch_dc_no" ).click(function(e){
     enableEditButton();
	})

	$("#grn_tags_table").click(function(e){
	enableEditButton();
	})
}

function enableEditButton() {
	$('#cmdUpdateGRN').prop('disabled', false);
	$('#cmdUpdateGRN').attr('title', '');
}

function addRejectionProfileRow(){
	var currentRowId = $("#current-editable-id").val();
	var currentRowPrice = $("#materialtable, #dc_materialtable").find("tr[data-row-id='"+currentRowId+"']").find("td[name='price_col']").find("input").val();
	if (!Number(currentRowPrice)){
      currentRowPrice = 0.00;
   	}
    var row = ` <tr>
                    <td class='text-left td_rejection_reason'>
                        <input type="text" class="form-control" placeholder="Reason" maxlength='150' onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar');" >
                    </td>
                    <td class='text-right td_rejection_qty'>
                        <input type="text" class="form-control text-right" maxlength='16' onfocus="setNumberRangeOnFocus(this,10,5)" placeholder="Quantity" value="0.00" />
                    </td>
                    <td class='text-right td_rejection_debit'>
                        <input type="text" class="form-control text-right" maxlength='16' onfocus="setNumberRangeOnFocus(this,10,5)"  placeholder="Debit Value" value="${Number(currentRowPrice).toFixed(5)}" />
                    </td>
                </tr>`;
    $("#rejection-remarks-table tbody").append(row);
}



function updateNewRejectionRemarks(){
	var jsonconvert = [];
	var currentRowId = $("#current-editable-id").val();
	var currentRow = $("#materialtable, #dc_materialtable").find("tr[data-row-id='"+currentRowId+"']")
	$("#rejection-remarks-table").find("tbody tr").each(function(index, tr) {
		
		var item = {}
		item['make_id'] = $(currentRow).find("input[name='make_id']").val();
        item['reason'] = $(this).find('td.td_rejection_reason input').val().trim();
        item['oa_id'] = $(currentRow).find("input[name='oa_id']").val() == undefined || $(currentRow).find("input[name='oa_id']").val() == 'undefined' || $(currentRow).find("input[name='oa_id']").val() == '' ? "NULL" : $(currentRow).find("input[name='oa_id']").val();
        item['grnNumber'] = $("#id_receipt_no").val();
        item['gr.make_id'] = 1;
        item['po_no'] = $(currentRow).find("input[name='po_id']").val() == undefined || $(currentRow).find("input[name='po_id']").val() == 'undefined' || $(currentRow).find("input[name='po_id']").val() == '' ? "NULL" : $(currentRow).find("input[name='po_id']").val();
        item['debit'] = Number($(this).find('td.td_rejection_debit input').val().trim());
        item['item_id'] = $(currentRow).find("input[name='item_id']").val();
        item['enterprise_id'] = $("#enterprise_id").val();
        item['quantity'] = Number($(this).find('td.td_rejection_qty input').val().trim());
        item['dc_id'] = $(currentRow).find("input[name='dc_id']").val() == undefined || $(currentRow).find("input[name='dc_id']").val() == 'undefined' || $(currentRow).find("input[name='dc_id']").val() == '' ? "0" : $(currentRow).find("input[name='dc_id']").val();
        item['rec_grn_id'] = $(currentRow).find("input[name='rec_grn_id']").val() == undefined || $(currentRow).find("input[name='rec_grn_id']").val() == 'undefined' || $(currentRow).find("input[name='rec_grn_id']").val() == '' ? "NULL" : $(currentRow).find("input[name='rec_grn_id']").val();
        item['is_faulty'] = $(currentRow).find("input[name='is_faulty']").val();
        jsonconvert.push(item);
	});
	
	$(currentRow).find("input[name='rejection_profiles']").val(JSON.stringify(jsonconvert))
}

function updateRejectionRemarksJson(current) {
	openRejectionProfilePopup($(current).find("span[name='rej_qty']"), false)
}
</script>
{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
{% endblock %}