"""
Helper class that holds non-functional simplifications
"""
import ast
import os
import re
from collections import OrderedDict
from datetime import datetime
from decimal import Decimal
from sqlalchemy import and_
from sqlalchemy.orm import make_transient
from functools import wraps
from django.http import HttpResponse

from erp import logger, dao, IS_FAULTY_TRUE
from erp.accounts import SALES_ACCOUNT_GROUP_NAME
from erp.dao import executeQuery
from erp.models import Project, IndentType, Material, VoucherType, Ledger, AccountGroup, UnitMaster, Party, \
	Category, Tax, Currency, NoteReason, EnterpriseClaimHead, EnterpriseExpenseHead, Make, UserClaimHead, \
	UserExpenseHead, PurchaseOrder, Department, MaterialMakeMap, PayStructure, User, Attachment, MaterialAlternateUnit, \
	Invoice, ReceiptMaterial, Receipt, Enterprise, UserEnterpriseMap, OA, LocationMaster, UserLocationMap
from settings import SQLASession, MongoDbConnect
import json

__author__ = 'kalaivanan'

# JSON Dump literals
from util.api_util import response_code

TAX_ROW_DUMP = """"<tr name='tr_%s' class='delete_indv_tax'>
	<th><a role='button' title='Remove Tax' onclick="javascript:removeInvoiceTax('%s');">
		<input type='hidden' class='hnd_text_id' name='tax_code' class='form-control' value='%s'/>
		<i class='fa fa-window-close' aria-hidden='true' style='color: #000; font-size: 18px;'></i></a></th>
	<th><b>%s</b> <i>(Net Rate)</i></th>
	<td><b>%s %%</b><input type='hidden' name='net_rate%s' id='net_%s' value='%s'/></td><td width='10px'>&nbsp;</td>
	<td class='col-sm-4 remove-right-padding' style='padding-left: 10px;'>
		<input type='text' class='form-control' style='text-align:right;' name='tax%s' disabled/></td>
	<td><input type='hidden' class='form-control' style='text-align:right;' name='asses_rate%s' id='asses%s' value='%s'
			disabled/></td>
</tr>"""
SUB_TAX_ROW_DUMP = """<tr name='tr_%s'>
	<td colspan='2'>%s</td>
	<td>%s %%</td>
	<td width='10px'>&nbsp;</td>
	<td></td>
</tr>"""
TAX_BASE_RATE_ROW_DUMP = """<tr name='tr_%s'>
	<td colspan='2'><i>%s</i></td>
	<td>%s %%</td>
	<td width='10px'>&nbsp;</td>
	<td></td>
</tr>"""

EMPLOYMENT_TYPES = ["Full-Time", "Part-Time", "Contract", "Probationary", "Others"]
EMPLOYMENT_STATUS = ["Active", "Terminated", "Deceased", "Resigned"]

INVOICE_TEMPLATE_BASE_FONT = ["Roboto", "Times New Roman", "Helvetica", "Ubuntu", "Comic Sans", "DejaVu Sans", "Courier New", "Verdana", "Open Sans"]

PURCHASE_TEMPLATE_BASE_FONT = ["Roboto", "Times New Roman", "Helvetica", "Ubuntu", "Comic Sans", "DejaVu Sans", "Courier New", "Verdana", "Open Sans"]

DEFAULT_BILLABLE_ACCOUNT_GROUP = [
	AccountGroup.SUNDRY_CREDITOR_GROUP_NAME, AccountGroup.SUNDRY_DEBTOR_GROUP_NAME,
	AccountGroup.OTHER_CURRENT_LIABILITIES_GROUP_NAME, AccountGroup.OTHER_CURRENT_ASSETS_GROUP_NAME]


def populateProjectChoices(
		need_blank_first=True, enterprise_id=None, assort_frequent_choices=False, module_table_name=None,
		frequents_condition="", is_child=False):
	"""
	Populates Projects to a Foreign Key referenced Choice Field in a Form.

	:param need_blank_first:
	:param enterprise_id:
	:param assort_frequent_choices:
	:param module_table_name:
	:param frequents_condition:
	:return:
	"""
	enterprise = ""
	if is_child:
		enterprise = SQLASession().query(Enterprise.parent_enterprise_id).filter(Enterprise.id == enterprise_id).all()
	if enterprise and enterprise[0][0]:
		enterprise_id = enterprise[0][0]
	logger.info('populating Projects...')
	project_choices = []
	if not assort_frequent_choices:
		project_choices = [('', '--Projects--')] if need_blank_first else [(None, 'ALL')]
	project_list = SQLASession().query(Project).filter(Project.enterprise_id == enterprise_id, Project.is_active == True)
	for project in project_list.order_by(Project.name):
		project_choices.append((project.id, str(project.name) + " (" + str(project.code) + ")"))

	return project_choices


def populateSelectedProject(enterprise_id=None, project_code=None):
	"""
	Populates Projects to a Foreign Key referenced Choice Field in a Form.

	:param enterprise_id:
	:param project_code:
	:return:
	"""
	logger.info('populating Projects...')
	project = SQLASession().query(Project.id, Project.name).filter(Project.enterprise_id == enterprise_id, Project.id == project_code).first()
	return project


def populateAllProjectIds(enterprise_id=None):
	"""
	Populates Projects ids based on enterprise.

	:param enterprise_id:
	:return:
	"""
	projects = SQLASession().query(Project.id).filter(Project.enterprise_id == enterprise_id).all()
	project_ids = ""
	for project in projects:
		project_ids = project_ids + str(project[0]) + ","
	return project_ids[:-1] if project_ids != "" else ""


def getProjectByProjectEnterpriseId(project_enterprise_id):
	"""
	Get A specified Project By project Enterprise ID
	"""
	projects = SQLASession().query(Project.name, Project.code, Project.project_enterprise_id).filter(Project.project_enterprise_id == project_enterprise_id).first()
	return projects


def getProjectByProjectId(project_id):
	"""
	Get A specified Project By project ID
	"""
	project = SQLASession().query(Project).filter(Project.id == project_id).first()
	return project


def getProjectByProjectCode(project_code, enterprise_id):
	"""
	Get A specified Project By Project Code and Enterprise_id
	"""
	return SQLASession().query(Project).filter(Project.code == project_code, Project.enterprise_id == enterprise_id).first()


def getProjectByProjectNameAndParentId(project_name, parent_id):
	"""
	Get A specified Project By Project Name and Parent project Id
	"""
	return SQLASession().query(Project).filter(Project.parent_id == parent_id, Project.name == project_name).first()


def populateJOBPOChoices(need_blank_first=True, enterprise_id=None, party_id=None, order_type=None, invoice_id = None, po_id=None, is_dc_edit=False):
	"""
	Populates Projects to a Foreign Key referenced Choice Field in a Form.

	:param need_blank_first:
	:param enterprise_id:
	:param party_id:
	:param order_type:
	:return: list of Projects
	"""
	logger.info('populating Projects...')
	po_choices = [(0, 'Select a PP No/MRS No')] if need_blank_first else []
	if enterprise_id:
		if invoice_id:
			po_query = SQLASession().query(Invoice.id, PurchaseOrder.po_id).join(Invoice.job_po).filter(
				Invoice.enterprise_id == enterprise_id, Invoice.id == invoice_id).group_by(Invoice.id)
			po_query_data = po_query.all()
			po_id = po_query_data[0][1] if po_query_data else None
		query = SQLASession().query(PurchaseOrder).filter(
			PurchaseOrder.enterprise_id == enterprise_id, PurchaseOrder.status == 2, PurchaseOrder.type == order_type)
		if party_id:
			query = query.filter(PurchaseOrder.supplier_id == party_id)
		if order_type == 2:
			for po in query.all():
				if po_id == po.po_id or (not po.isAllMaterialsSupplied() and not isAllPPMaterialsIssue(po.po_id, enterprise_id)):
					po_choices.append((po.po_id, po.getCode()))

			query = SQLASession().query(OA).filter(
				OA.enterprise_id == enterprise_id, OA.status == 1, OA.type == 'MRS')
			for oa in query.all():
				if po_id == oa.id or not isAllMaterialsIssueAgainstMRS(oa.id, enterprise_id):
					po_choices.append((oa.id, oa.getMRSCode()))
		else:
			for po in query.all():
				if is_dc_edit and po.isAllMaterialsSupplied():
					continue
				po_choices.append((po.po_id, po.getCode()))
	return po_choices


def isAllMaterialsIssueAgainstMRS(oa_id, enterprise_id):
	"""
	Validate the All Production materials are issued
	"""
	response = True
	try:
		mrs_stock_query = """SELECT oa.id AS oa_id, oap.item_id, oap.quantity as mrs_qty, IFNULL(SUM(im.qty),0) as issue_qty
							FROM order_acknowledgement AS oa 
							JOIN oa_particulars AS oap ON oap.oa_id = oa.id AND oap.enterprise_id = oa.enterprise_id
							LEFT JOIN invoice_materials AS im ON oap.oa_id = im.oa_no AND im.item_id = oap.item_id
							WHERE oa.type = 'MRS' AND oa.enterprise_id = {enterprise_id} AND oa.id = {oa_id}
							GROUP BY oa.id , oap.item_id""".format(oa_id=oa_id, enterprise_id=enterprise_id)
		materials = dao.executeQuery(mrs_stock_query, as_dict=True)
		if materials:
			for rec in materials:
				mrs_qty = rec['mrs_qty']
				issue_qty = rec['issue_qty']
				if mrs_qty > issue_qty:
					return False
		return response
	except Exception as e:
		logger.exception("Failed to Validate the MRS Qty- %s" %str(e))


def isAllPPMaterialsIssue(po_id, enterprise_id):
	"""
	Validate the All Production materials are issued
	"""
	response = True
	try:
		total_pp_qty = 0
		po_query = "SELECT pur_qty, item_id FROM purchase_order_material WHERE pid = %s AND enterprise_id = %s" % (po_id, enterprise_id)
		po_query_data = dao.executeQuery(po_query)
		if po_query_data:
			for po_item in po_query_data:
				query = "SELECT qty FROM cat_materials WHERE parent_id = %s AND enterprise_id = %s" % (int(po_item[1]), enterprise_id)
				query_data = dao.executeQuery(query)
				if query_data:
					for catalogue in query_data:
						total_pp_qty += int(catalogue[0] * po_item[0])

		pp_stock_query = """SELECT sum(IFNULL(inv_m.qty,0)) as issue_qty
						FROM invoice as inv 
						LEFT JOIN invoice_materials as inv_m ON inv.enterprise_id = inv_m.enterprise_id AND inv.id = inv_m.invoice_id
						WHERE inv.job_po_id = {po_id} AND inv.enterprise_id = {enterprise_id}"""\
			.format(po_id=po_id, enterprise_id=enterprise_id)
		materials = dao.executeQuery(pp_stock_query, as_dict=True)
		if materials:
			pp_qty = total_pp_qty
			issue_qty = materials[0]['issue_qty']
			if pp_qty and pp_qty == issue_qty:
				return True
			else:
				return False
		return response
	except Exception as e:
		logger.exception("Failed to Validate the PP Qty- %s" %str(e))

def populateProductionPlanDetails(enterprise_id=None, order_type=None):

	"""
	"""
	wo_query = """ SELECT id, wo_no  from (SELECT 
					o.id AS id,
					IFNULL(CONCAT(o.financial_year,
					IF(o.type=2,'/PP/','/JO/'),
					LPAD(o.orderno, 6, 0),
					IFNULL(o.sub_number, '')),
					'') AS wo_no,      
					pom.pur_qty as pur_qty,
					IFNULL(sum(gm.acc_qty), 0) as rec_qty
					FROM
					purchase_order AS o
					JOIN
					purchase_order_material pom ON pom.pid = o.id
					AND o.enterprise_id = pom.enterprise_id
					LEFT JOIN
					grn_material AS gm ON  pom.item_id = gm.item_id
					AND pom.make_id = gm.make_id
					AND pom.enterprise_id = gm.enterprise_id
					AND pom.pid = gm.po_no 
					LEFT JOIN
					grn as g ON g.grn_no = gm.grnNumber 
					AND gm.enterprise_id = g.enterprise_id   
					AND g.status > -1
					WHERE
					o.enterprise_id = {enterprise_id} AND type = {order_type} 
					group by o.id,pom.item_id,pom.make_id    
					ORDER BY o.id) as po_list where  pur_qty > rec_qty""".format(
		enterprise_id=enterprise_id, order_type=order_type)
	wo_ids = executeQuery(wo_query)
	wo_id = []
	for i in wo_ids:
		wo_id.append(i)
	return wo_id


def generateIndentTypeChoices(enterprise_id=None):
	"""
	Populates Indent list for a given project to a Foreign Key referenced Choice Field in a Form.

	:param enterprise_id:
	:return:
	"""
	indent_type_choices = []
	if enterprise_id is None:
		indent_types = SQLASession().query(IndentType)
	else:
		indent_types = SQLASession().query(IndentType).filter(IndentType.enterprise_id == enterprise_id)
	for indent_type in indent_types.order_by(IndentType.name):
		indent_type_choices.append((indent_type.id, indent_type.description))
	return indent_type_choices


def populateExpenseHeadChoices(enterprise_id=None):
	"""
	
	:param enterprise_id: 
	:return: 
	"""
	return populateAccountLedgerChoices(enterprise_id=enterprise_id, group_names=(
		'Direct Expenses', 'Indirect Expenses', 'Other Direct Expenses'))


def populateClaimHeadChoices(enterprise_id=None):
	"""
	
	:param enterprise_id: 
	:return: 
	"""
	return populateAccountLedgerChoices(enterprise_id=enterprise_id, group_names=(
		'Current Liabilities', 'Other Current Liabilities', 'Non-Current Liabilities',
		'Other Non-current Liabilities'))


def populateSaleAccountChoices(enterprise_id=None):
	"""
	
	:param enterprise_id:
	:return:
	"""
	return populateAccountLedgerChoices(enterprise_id=enterprise_id, group_names=(SALES_ACCOUNT_GROUP_NAME,))


def getAccountGroupIDs(enterprise_id=None, names=(), deep=True):
	"""
	
	:param enterprise_id:
	:param names:
	:param deep:
	:return:
	"""
	_group_ids = set()
	if enterprise_id:
		_account_groups = SQLASession().query(AccountGroup).filter(
			AccountGroup.name.in_(names), AccountGroup.enterprise_id == enterprise_id)
		# Considering all sub-groups under the Account Groups as well
		for _group in _account_groups:
			_group_ids.add(_group.id)
			if deep:
				for _child in _group.getSubAccountGroups():
					_group_ids.add(_child.id)
	return [_id for _id in _group_ids]


def populateAccountLedgerChoices(enterprise_id=None, group_names=(), with_group_name=True, is_debtor_creditor=False):
	_account_ledgers = []
	_ledger_choices = []
	if enterprise_id:
		_account_ledgers = SQLASession().query(Ledger.id, Ledger.name, AccountGroup.name.label("group"), Ledger.billable).join(
			Ledger.group).filter(Ledger.enterprise_id == enterprise_id, AccountGroup.id.in_(
			getAccountGroupIDs(enterprise_id=enterprise_id, names=group_names)), Ledger.status == 1).order_by(
			AccountGroup.name, Ledger.name).all()
	for _ledger in _account_ledgers:
		if is_debtor_creditor:
			if _ledger.billable:
				_ledger_choices.append(
					(_ledger.id, "[%s] %s" % (_ledger.group, _ledger.name) if with_group_name else _ledger.name))
		else:
			_ledger_choices.append(
				(_ledger.id, "[%s] %s" % (_ledger.group, _ledger.name) if with_group_name else _ledger.name))
	return _ledger_choices


def populateBankLedgerChoices(enterprise_id=None):
	"""
	Lists Bank Ledger Option choices to be populated in various modules, say, Vouchers, Dashboards, etc
	
	:param enterprise_id:
	:return:
	"""
	bank_ledger_choices = [(None, '')]
	bank_ledger_choices.extend(populateAccountLedgerChoices(
		enterprise_id=enterprise_id, group_names=[
			AccountGroup.BANK_GROUP_NAME, AccountGroup.LT_BORROWINGS_GROUP_NAME, AccountGroup.ST_BORROWINGS_GROUP_NAME],
		with_group_name=True))
	return bank_ledger_choices


def populateCashLedgerChoices(enterprise_id=None):
	"""
	Lists Cash Ledger Option choices to be populated in various modules, say, Vouchers, Dashboards, etc
	
	:param enterprise_id:
	:return:
	"""
	cash_ledger_choices = [(None, '')]
	cash_ledger_choices.extend(populateAccountLedgerChoices(
		enterprise_id=enterprise_id, group_names=[AccountGroup.CASH_GROUP_NAME], with_group_name=True))
	return cash_ledger_choices


def populateUserClaimHeadChoices(enterprise_id=None, user_id=None, include_default=None):
	"""
	It loads the Enterprise claim values
	:param enterprise_id:
	:param user_id:
	:param include_default:
	:return:
	"""
	_claim_head_choices = []
	if include_default:
		_claim_head_choices.append(include_default)
	logger.info("Populating user claim head choices for {enterprise_id:%s, user_id:%s}" % (enterprise_id, user_id))
	if enterprise_id and user_id:
		ledgers = SQLASession().query(Ledger.id, Ledger.name).join(
			UserClaimHead, and_(UserClaimHead.ledger_id == Ledger.id)
		).filter(UserClaimHead.enterprise_id == enterprise_id, UserClaimHead.user_id == user_id).order_by(
			Ledger.name).all()
		for ledger in ledgers:
			_claim_head_choices.append((ledger.id, ledger.name))
	return _claim_head_choices


def populateUserExpenseHeadChoices(enterprise_id=None, user_id=None, include_default=None):
	"""
	It loads the Enterprise Expense values

	:param enterprise_id:
	:param user_id:
	:param include_default:
	:return:
	"""
	_expense_head_choices = []
	if include_default:
		_expense_head_choices.append(include_default)
	logger.info("Populating expense head choices for {enterprise_id:%s, user_id:%s}" % (enterprise_id, user_id))
	if enterprise_id and user_id:
		ledgers = SQLASession().query(Ledger.id, Ledger.name).join(
			UserExpenseHead, and_(UserExpenseHead.ledger_id == Ledger.id)
		).filter(UserExpenseHead.enterprise_id == enterprise_id, UserExpenseHead.user_id == user_id).order_by(
			Ledger.name).all()
		for ledger in ledgers:
			_expense_head_choices.append((ledger.id, ledger.name))
	return _expense_head_choices


def populateEnterpriseClaimHeadChoices(enterprise_id=None):
	"""
	It loads the Enterprise claim values
	:param enterprise_id:
	:return:
	"""
	_enterprise_claim_head_ledgers = []
	_enterprise_claim_ledger_choices = []
	if enterprise_id:
		_enterprise_claim_head_ledgers = SQLASession().query(Ledger.id, Ledger.name).join(EnterpriseClaimHead, and_(
			EnterpriseClaimHead.ledger_id == Ledger.id,
			EnterpriseClaimHead.enterprise_id == Ledger.enterprise_id)).filter(
			EnterpriseClaimHead.enterprise_id == enterprise_id).order_by(Ledger.name)
	for _claim_ledger in _enterprise_claim_head_ledgers:
		_enterprise_claim_ledger_choices.append((_claim_ledger.id, _claim_ledger.name))
	return _enterprise_claim_ledger_choices


def populateEnterpriseExpenseHeadChoices(enterprise_id=None):
	"""
	It loads the Enterprise Expense values

	:param enterprise_id:
	:return:
	"""
	_enterprise_expense_head_ledgers = []
	_enterprise_expense_ledger_choices = []
	if enterprise_id:
		_enterprise_expense_head_ledgers = SQLASession().query(Ledger.id, Ledger.name).join(EnterpriseExpenseHead, and_(
			EnterpriseExpenseHead.ledger_id == Ledger.id,
			EnterpriseExpenseHead.enterprise_id == Ledger.enterprise_id)).filter(
			EnterpriseExpenseHead.enterprise_id == enterprise_id).order_by(Ledger.name)
	for _expense_ledger in _enterprise_expense_head_ledgers:
		_enterprise_expense_ledger_choices.append((_expense_ledger.id, _expense_ledger.name))
	return _enterprise_expense_ledger_choices


def populateMakes(enterprise_id=None):
	logger.debug("Enterprise ID:%s" % enterprise_id)
	make_choices = []
	if enterprise_id:
		makes = SQLASession().query(Make).filter(Make.enterprise_id == enterprise_id, Make.id != 1)
		for make in makes.order_by(Make.label):
			make_choices.append((make.id, make.label))
	return make_choices


# Its Load Party Details in Search Invoice Form
def populateInvoicePartyChoices(enterprise_id=None):
	"""
	Populates List of Parties profiled for the Enterprise mentioned for invoice
	:param enterprise_id:
	:return:
	"""
	party_customers = []
	party_non_customers = []
	party_list = SQLASession().query(Party.id, Party.name, Party.config_flags, Party.code).filter(
		Party.enterprise_id == enterprise_id, Party.code != 'SELF').order_by(Party.name)
	for party in party_list:
		if Party.isCustomerFor(party):
			party_customers.append((party.id, "%s (%s)" % (party.name, party.code)))
	for party in party_list:
		if not Party.isCustomerFor(party):
			party_non_customers.append((party.id, "%s (%s)" % (party.name, party.code)))
	return [("-1", 'ALL'), ("CUSTOMERS", party_customers), ("OTHERS", party_non_customers)]


# Its Load Party Details in Sales Invoice Form
def populatePartyChoices(
		enterprise_id=None, populate_all=True, is_customer=False, is_supplier=False, assort_frequent_choices=False,
		frequents_condition="", module_table_name=""):
	"""

	Populates List of Parties profiled for the Enterprise mentioned depending on the parameters
	
	:param enterprise_id:
	:param populate_all: If true the default FIRST option will be 'ALL' (Search pages) else 'None' (Edit pages)
	:param is_customer: In combination with is_supplier filters the parties based on their Customer/Supplier
		configuration; When none of the flags are explicitly mentioned, the listing ignores such profiling
	:param is_supplier: Read is_customer
	:param assort_frequent_choices:
	:param frequents_condition:
	:param module_table_name:
	:return:
	"""
	party_choices = []
	if not assort_frequent_choices:
		party_choices = [("-1", 'ALL')] if populate_all else [(None, "--")]
	party_list = SQLASession().query(Party.id, Party.name, Party.config_flags, Party.code).filter(
		Party.enterprise_id == enterprise_id, Party.code != 'SELF').order_by(Party.name)
	if is_supplier or is_customer:
		for party in party_list:
			if (is_supplier and Party.isSupplierFor(party)) or (is_customer and Party.isCustomerFor(party)):
				party_choices.append((party.id, "%s (%s)" % (party.name, party.code)))
	else:
		for party in party_list:
			party_choices.append((party.id, "%s (%s)" % (party.name, party.code)))
	if assort_frequent_choices:
		party_choices = [
			(None, ''), ('add_new_party', '+ Add New'), ("Frequently used", getPartyFrequents(
				table=module_table_name, enterprise_id=enterprise_id, condition=frequents_condition)),
			("All", party_choices)]
	return party_choices


def populateCurrencyChoices():
	currency_choices = [(None, '-- Currency --')]
	try:
		currency_list = SQLASession().query(Currency).order_by(Currency.code).all()
		for currency in currency_list:
			currency_choices.append((currency.id, currency.code))
	except Exception as e:
		logger.exception("Error while populating Currency choices :%s" % str(e))
		pass
	return currency_choices

def populateFiscalYear():
	fiscal_years = [
		("01/01", "January - December"), ("01/02", "February - January"), ("01/03", "March - February"), ("01/04", "April - March"), ("01/05", "May - April"),
		("01/06", "June - May"), ("01/07", "July - June"), ("01/08", "August - July"), ("01/09", "September - August"),
		("01/10", "October - September"), ("01/11", "November - October") , ("01/12", "December - November")]
	return fiscal_years

def populateMaterialChoices(enterprise_id=None):
	materials = [(None, '-- Material --')]
	try:
		if enterprise_id is not None:
			material_list = SQLASession().query(Material).filter(Material.enterprise_id == enterprise_id).all()
		else:
			material_list = SQLASession().query(Material).group_by(Material.item_id).all()
		for material in material_list:
			materials.append((material.drawing_no, "%s - %s" % (material.drawing_no, material.name)))
	except Exception as e:
		logger.exception("Error while populating Material choices :%s" % str(e))
		pass
	return materials


def populateTaxChoices(enterprise_id=None):
	logger.debug("Control Enter into To Populate Tax...")
	tax_choices = [(None, '-- Tax --')]
	try:
		if enterprise_id is not None:
			tax_list = SQLASession().query(Tax).filter(Tax.enterprise_id == enterprise_id)
		else:
			tax_list = SQLASession().query(Tax).group_by(Tax.code)
		for tax in tax_list.order_by(Tax.name):
			tax_choices.append((tax.code, tax.name))
	except Exception as e:
		logger.exception("Error while populating Tax choices :%s" % e.message)
		pass
	return tax_choices


def populateMaterialCategoryChoices(enterprise_id=None, chosen_category_id=None, is_service=None):
	"""
	
	:param enterprise_id:
	:param chosen_category_id:
	:param is_service:
	:return:
	"""
	category_options = []
	try:
		logger.info("Material Categories for the Enterprise in context: %s" % [enterprise_id, chosen_category_id, is_service])
		material_categories = SQLASession().query(Category).filter(
			Category.enterprise_id == enterprise_id, Category.is_service == is_service)
		for category in material_categories:
			category_options.append({
				"category_id": category.id, "label": category.name,
				"attrs": "selected" if ("%s" % category.id) == ("%s" % chosen_category_id) else ""})
	except Exception as e:
		logger.exception("Material Category option construction failed... %s" % e.message)
	return category_options


def populateUnit(enterprise_id=None):
	logger.debug('Listing Unit Choices...')
	units = []
	try:
		unit_query = SQLASession().query(UnitMaster)
		if enterprise_id:
			unit_query = unit_query.filter(UnitMaster.enterprise_id == enterprise_id)
		units_list = unit_query.group_by(UnitMaster.unit_id).order_by(UnitMaster.unit_id).all()
		for unit in units_list:
			units.append((unit.unit_id, "%s (%s)" % (unit.unit_name, unit.unit_description)))
	except Exception as e:
		logger.exception("Can't populate Unit choices :%s" % e.message)
		pass
	return units


def populateAlternateUnits(enterprise_id=None, item_id=None):
	logger.debug('Listing Alternate Unit Choices...')
	units = []
	try:
		unit_query = SQLASession().query(
			MaterialAlternateUnit.alternate_unit_id, UnitMaster.unit_name, UnitMaster.unit_description,
			MaterialAlternateUnit.scale_factor).join(
			UnitMaster, and_(
				UnitMaster.unit_id == MaterialAlternateUnit.alternate_unit_id,
				UnitMaster.enterprise_id == MaterialAlternateUnit.enterprise_id))
		if item_id:
			unit_query = unit_query.filter(
				MaterialAlternateUnit.item_id == item_id, MaterialAlternateUnit.enterprise_id == enterprise_id)
		units_list = unit_query.group_by(
			MaterialAlternateUnit.alternate_unit_id).order_by(MaterialAlternateUnit.alternate_unit_id).all()
		for unit in units_list:
			units.append({
				"alternate_unit_id": unit.alternate_unit_id, "unit_name": unit.unit_name, "scale_factor": unit.scale_factor})
	except Exception as e:
		logger.exception("Can't populate Alternate Unit choices :%s" % e.message)
		pass
	return units


def populateAllUnits(enterprise_id=None):
	logger.debug('Listing Unit Choices...')
	units = []
	try:
		unit_query = SQLASession().query(UnitMaster)
		if enterprise_id:
			unit_query = unit_query.filter(UnitMaster.enterprise_id == enterprise_id)
		units_list = unit_query.group_by(UnitMaster.unit_id).order_by(UnitMaster.unit_id).all()
		for unit in units_list:
			units.append({
				"unit_id": unit.unit_id, "unit_name": "%s (%s)" % (unit.unit_name, unit.unit_description)})
	except Exception as e:
		logger.exception("Can't populate Unit choices :%s" % e.message)
		pass
	return units


def getScaleFactor(enterprise_id=None, item_id=None, alternate_unit_id=None):
	"""

	:return: Returns plain text or None
	"""
	scale_factor = SQLASession().query(MaterialAlternateUnit.scale_factor).filter(
		MaterialAlternateUnit.enterprise_id == enterprise_id, MaterialAlternateUnit.alternate_unit_id == alternate_unit_id
		, MaterialAlternateUnit.item_id == item_id).first()
	if scale_factor:
		return scale_factor.scale_factor
	return None


def getUnitName(enterprise_id=None, unit_id=None):
	"""

	:return: Returns unit name for request id
	"""
	unit_name = SQLASession().query(UnitMaster.unit_name).filter(
		UnitMaster.enterprise_id == enterprise_id, UnitMaster.unit_id == unit_id).first()
	if unit_name:
		return unit_name.unit_name
	return None


def getAlternateUnitCount(enterprise_id=None, item_id=None):
	"""

	:return: Returns unit count for request item_id
	"""
	unit_count = SQLASession().query(MaterialAlternateUnit).filter(
		MaterialAlternateUnit.enterprise_id == enterprise_id, MaterialAlternateUnit.item_id == item_id).all()
	if unit_count:
		return len(unit_count)
	return 0


def populateUnitChoices(enterprise_id=None, chosen_unit_id=None):
	"""
	
	:param enterprise_id:
	:param chosen_unit_id:
	:return:
	"""
	unit_options = []
	try:
		logger.info("Units for the Enterprise in context: %s" % enterprise_id)
		units = SQLASession().query(UnitMaster).filter(
			UnitMaster.enterprise_id == enterprise_id).order_by(UnitMaster.unit_id)
		for unit in units:
			unit_options.append({
				"category_id": unit.unit_id, "label": "%s (%s)" % (unit.unit_name, unit.unit_description),
				"attrs": "selected" if ("%s" % unit.unit_id) == ("%s" % chosen_unit_id) else ""})
	except Exception as e:
		logger.exception("Unit option construction failed... %s" % e.message)
	return unit_options


def populateAccountGroup(enterprise_id=None, need_blank_first=True):
	groups = [(None, '--Account Group--')] if need_blank_first else []
	try:
		if enterprise_id is not None:
			groups_list = SQLASession().query(AccountGroup).filter(AccountGroup.enterprise_id == enterprise_id).all()
		else:
			groups_list = SQLASession().query(AccountGroup).group_by(AccountGroup.id).all()
		logger.info('Populating Account Groups... %s' % len(groups_list))
		for group in groups_list:
			groups.append((group.id, group.name))
	except Exception as e:
		logger.exception("Can't populate Account Group choices :%s" % e.message)
		pass
	return groups


def constructAccountGroupOptionTree(
		enterprise_id=None, selected_group_id=None, need_add_option=True, for_ledger_creation=False,
		for_subgroup_creation=False):
	"""
	AJAX Call to restructure Account Groups in Tree format to populate it in drop-down
	
	:param enterprise_id:
	:param selected_group_id:
	:param need_add_option:
	:param for_ledger_creation:
	:param for_subgroup_creation:
	:return:
	"""
	account_group_options = []
	try:
		logger.info("Populating Account Groups for given Data: %s-%s-%s-%s-%s" % (
			enterprise_id, selected_group_id, need_add_option, for_ledger_creation, for_subgroup_creation))
		root_group = SQLASession().query(AccountGroup).filter(
			AccountGroup.enterprise_id == enterprise_id, AccountGroup.id == 0).first()
		account_group_options.extend(constructOptionTree(
			root=root_group, selected_group_id=selected_group_id, for_subgroup_creation=for_subgroup_creation,
			for_ledger_creation=for_ledger_creation, for_import_voucher=False))
		if need_add_option:
			account_group_options.extend([
				{"value": "", "label": "-----------", "attrs": "disabled"},
				{"value": "add_account_group", "label": "[+] New Account Group", "attrs": ""}])
		logger.debug(("%s" % account_group_options).replace("&nbsp;", " ").replace("<option", "\n<option"))
	except Exception as e:
		logger.exception("Account Group options construction failed due to %s" % e.message)
	return account_group_options


def constructOptionTree(
		root=None, level=-1, selected_group_id=None, for_ledger_creation=False, for_subgroup_creation=False,
		for_import_voucher=False):
	"""
	Construct Account Group Tree-node

	:param root:
	:param level:
	:param selected_group_id:
	:param for_ledger_creation:
	:param for_subgroup_creation:
	:param for_import_voucher:
	:return:
	"""
	options = []
	if root:
		option_attribute = " selected" if ("%s" % root.id == "%s" % selected_group_id) else ""
		option_attribute += "" if (for_ledger_creation and root.isLedgerCreationAllowed()) or (
				for_subgroup_creation and root.isSubGroupCreationAllowed()) else " disabled"
		if for_import_voucher:
			if root.id != 0:
				options = "<tr><td>'%s'</td><td>%s%s'</td></tr>" % (
					(root.id if root else "") if option_attribute != ' disabled' else '-NA-',
					"&nbsp;" * 8 * level, root.name if root else "")
			else:
				options = ''
		else:
			options.append({"value": "%s" % root.id if root else "", "label": "%s%s" % (
				"&nbsp;" * 8 * level, root.name if root else ""), "attrs": option_attribute})
		level += 1
		for child in root.children:
			if len(child.children) > 0:
				if for_import_voucher:
					options += constructOptionTree(
						root=child, level=level, selected_group_id=selected_group_id,
						for_ledger_creation=for_ledger_creation, for_subgroup_creation=for_subgroup_creation,
						for_import_voucher=for_import_voucher)
				else:
					options.extend(constructOptionTree(
						root=child, level=level, selected_group_id=selected_group_id,
						for_ledger_creation=for_ledger_creation, for_subgroup_creation=for_subgroup_creation))
			else:
				option_attribute = " selected" if ("%s" % child.id == "%s" % selected_group_id) else ""
				option_attribute += "" if (for_ledger_creation and child.isLedgerCreationAllowed()) or (
						for_subgroup_creation and child.isSubGroupCreationAllowed()) else " disabled"
				if for_import_voucher:
					options += "<tr><td>'%s'</td><td>%s%s'</td></tr>" % (
						child.id if option_attribute != ' disabled' else '-NA-', "&nbsp;" * 8 * level,
						child.name)
				else:
					options.append({
						"value": child.id, "label": "%s%s" % ("&nbsp;" * 8 * level, child.name),
						"attrs": option_attribute})
		return options


def populateAccountLedger(enterprise_id=None):
	ledgers = [(None, '-- Ledgers --')]
	logger.info('populating Acc Ledgers...')
	try:
		ledgers_list = []
		if enterprise_id is not None:
			ledgers_list = SQLASession().query(Ledger.id, Ledger.name).filter(
				Ledger.enterprise_id == enterprise_id).order_by(Ledger.name).all()
		for ledger in ledgers_list:
			ledgers.append((ledger.id, ledger.name))
	except Exception as e:
		logger.exception("Error while populating Ledger choices :%s" % e.message)
		pass
	return ledgers


def populateDrCr():
	transaction_types = [(None, '-- Dr/Cr --'), ("0", "Cr"), ("1", "Dr")]
	return transaction_types


def populateVoucherTypeWithCode(exclude_header=False):
	logger.info('Listing Voucher type choices with code...')

	voucher_types = [] if exclude_header else [(None, '-- Voucher Type --', '')]
	try:
		voucher_type_list = SQLASession().query(VoucherType).order_by('name').all()
		for voucher_type in voucher_type_list:
			voucher_types.append((voucher_type.id, voucher_type.name, voucher_type.code))
	except Exception as e:
		logger.exception("Can't populate Voucher Type choices :%s" % e.message)
		pass
	return voucher_types


def populateVoucherType(exclude_none=False):
	logger.info('Listing Voucher type choices...')
	voucher_types = [(None, 'ALL')] if exclude_none else [(None, '-- Voucher Type --')]
	try:
		voucher_type_list = SQLASession().query(VoucherType).order_by('name').all()
		for voucher_type in voucher_type_list:
			voucher_types.append((voucher_type.id, voucher_type.name))
	except Exception as e:
		logger.exception("Can't populate Voucher Type choices :%s" % e.message)
		pass
	return voucher_types


def populateNoteReason():
	logger.info('Listing Note Reason choices...')
	reason_types = []
	try:
		reason_type_list = SQLASession().query(NoteReason).all()
		for reason_type in reason_type_list:
			reason_types.append((reason_type.id, reason_type.reason))
	except Exception as e:
		logger.exception("Can't populate Note Reason choices :%s" % e.message)
		pass
	return reason_types


def populateNoteType():
	types = [(None, '-- Select Type --'), (1, "Credit"), (0, "Debit")]
	return types


def populateTaxType():
	types = [
		("CGST", "CGST"), ("SGST", "SGST"), ("IGST", "IGST"), ("BED", "BED"), ("VAT", "VAT"), ("CST", "CST"),
		("SALES", "SALES"), ("SERVICE", "SERVICE"), ("CESS", "CESS")]
	return types


def generateTaxJSONDump(tax):
	"""
	:param tax:
	:return:
	"""
	try:
		compound_suffix = '_compound' if tax.is_compound else ''
		logger.debug('The Compound Suffice :%s' % compound_suffix)
		tax_json_dump = [
			TAX_ROW_DUMP % (
				tax.code, tax.code, tax.code, tax.name, tax.net_rate, compound_suffix, tax.code,
				tax.net_rate, compound_suffix, compound_suffix, tax.code, tax.assess_rate),
			TAX_BASE_RATE_ROW_DUMP % (tax.code, 'Base Rate', tax.base_rate)]
		for sub_tax in tax.sub_taxes:
			tax_json_dump.append(SUB_TAX_ROW_DUMP % (tax.code, sub_tax.name, sub_tax.rate))
			logger.info("The Generated Tax Table : %s" % tax_json_dump)
		return tax_json_dump
	except Exception as e:
		logger.exception("Tax JSON Construction failed... %s" % e.message)
		raise


def extractUserSimpleData(user=None):
	"""

	:param user:
	:return: map of id, username, first_name, last_name
	"""
	try:
		if user:
			return {
				'user_id': user.id, 'username': user.username, 'first_name': user.first_name,
				'last_name': user.last_name}
	except Exception:
		raise
	return None


def getValidDateTime(dict_key=None, data_dict=None, assign_now=False):
	"""

	:param dict_key:
	:param data_dict:
	:param assign_now:
	:return:
	"""
	if assign_now:
		return datetime.now()
	elif dict_key and dict_key in data_dict:
		date_string = data_dict.get(dict_key)
		if date_string != '':
			return datetime.strptime(date_string, "%Y-%m-%d %H:%M:%S")
	return None


def populatePayType():
	types = [
		(None, '-- Select Type --'), ("Earning", "Earning"), ("Deduction", "Deduction"),
		("Employer Contribution", "Employer Contribution")]
	return types


def populateDepartment(enterprise_id=None, need_blank_first=True, with_new_department=True):
	departments = [(None, '--Department--')] if need_blank_first else []
	try:
		if enterprise_id:
			department_list = SQLASession().query(Department).filter(
				Department.enterprise_id == enterprise_id).order_by(Department.name).all()
			logger.info('Populating Department... %s' % len(department_list))
			for department in department_list:
				departments.append((department.id, department.name))
		departments.append((None, '[+] Add New Department')) if with_new_department else ""
	except Exception as e:
		logger.exception("Can't populate Department choices :%s" % e.message)
		pass
	return departments


def populateMaterialMakes(enterprise_id=None, item_id=None):
	logger.debug("Enterprise ID:%s Drawing No: %s" % (enterprise_id, item_id))
	make_choices = []
	try:
		make_query = SQLASession().query(MaterialMakeMap).filter(
			MaterialMakeMap.enterprise_id == enterprise_id, MaterialMakeMap.item_id == item_id)
		for make_map in make_query.all():
			make_name = "%s%s" % (make_map.make, " - " + make_map.part_no if(make_map.part_no is not None) else "")
			make_choices.append((
				make_map.make_id, make_name, make_map.standard_packing_quantity if make_map.standard_packing_quantity else ""))
	except Exception as e:
		logger.exception("Can't populate Make choices :%s" % e.message)
		pass
	logger.debug("Populating %s Makes for %s-%s" % (len(make_choices), enterprise_id, item_id))
	return make_choices


def populateEmploymentType():
	types = [(None, '-- Select Type --')]
	for employment_type in EMPLOYMENT_TYPES:
		types.append((employment_type, employment_type))
	return types


def populateEmployeeStatus():
	status = [(None, '-- Select Status --')]
	for employment_status in EMPLOYMENT_STATUS:
		status.append((employment_status, employment_status))
	return status


def populateEmployeeGender():
	gender = [(None, '-- Select Gender --'), ("Male", "Male"), ("Female", "Female"), ("Others", "Others")]
	return gender


def populateEmployeeMartialStatus():
	status = [
		(None, '-- Select Martial Status --'), ("Married", "Married"), ("Unmarried", "Unmarried"),
		("Widowed", "Widowed"), ("Divorced", "Divorced")]
	return status


def populateEmployeeBloodGroup():
	blood_group = [
		(None, '-- Select Blood Group --'), ("A+", "A+"), ("A-", "A-"), ("B+", "B+"), ("B-", "B-"), ("AB+", "AB+"),
		("AB-", "AB-"), ("O+", "O+"), ("O-", "O-")]
	return blood_group


def populateAccountType():
	types = [
		("-NA-", "-NA-"),
		("Savings", "Savings"),
		("Salary", "Salary"),
		("Current", "Current"),
		("Others", "Others"),
	]
	return types


def populatePayStructure(enterprise_id=None, need_blank_first=True):
	pay_structures = [(None, '--Pay Structure--')] if need_blank_first else []
	try:
		if enterprise_id:
			pay_structure_list = SQLASession().query(PayStructure).filter(
				PayStructure.enterprise_id == enterprise_id).order_by(PayStructure.description).all()
			logger.info('Populating Pay Structure... %s' % len(pay_structure_list))
			for pay_structure in pay_structure_list:
				pay_structures.append((pay_structure.id, pay_structure.description))
	except Exception as e:
		logger.exception("Can't populate Pay Structure choices :%s" % e.message)
		pass
	return pay_structures


def updateRemarks(json_remarks_list=None, remarks_text=None, user=None):
	"""

	:param json_remarks_list: a json list
	:param remarks_text: remark text
	:param user:
	:return:
	"""
	updated_remarks_list = []
	try:
		if not remarks_text or remarks_text == "":
			return json_remarks_list
		if json_remarks_list:
			updated_remarks_list = [remarks for remarks in json_remarks_list]
		updated_remarks_list.append(dict(
			date=datetime.today().strftime("%Y-%m-%d %H:%M:%S"),
			remarks=remarks_text.replace('\n', '<BR/>'), by="%s" % user))
	except Exception as e:
		logger.exception("Updating remarks failed %s" % e.message)
		pass
	return updated_remarks_list


def populatePriceApprovalChoices():
	options = [
		(0, "Pending"),
		(1, "Approved"),
		(-1, "Rejected"),
	]
	return options


def getPartyFrequents(**kwargs):
	"""

	:param kwargs:
	:return:
	"""
	frequent_party_list = []
	try:
		frequent_party_query = """
			SELECT t1.party_id, pm.party_name, pm.config_flags, t1.enterprise_id, COUNT(1) AS item_count, pm.party_code
				FROM %s t1 JOIN party_master pm ON t1.party_id = pm.party_id AND t1.enterprise_id = pm.enterprise_id
				WHERE t1.enterprise_id = '%s' %s
				GROUP BY t1.party_id, t1.enterprise_id
				ORDER BY item_count DESC LIMIT 0, 5""" % (
			kwargs['table'], kwargs['enterprise_id'], kwargs['condition'] if 'condition' in kwargs else '')
		logger.debug("Frequent Party query:\n%s" % frequent_party_query)
		party_frequents = executeQuery(frequent_party_query)
		if 'need_configs' in kwargs and kwargs['need_configs']:
			frequent_party_list = [(item[0], "%s (%s)" % (item[1], item[5]), item[2]) for item in party_frequents]
		else:
			frequent_party_list = [(item[0], "%s (%s)" % (item[1], item[5])) for item in party_frequents]
	except Exception as e:
		logger.exception("Fetching Party Frequents Failed - %s" % e)
	return frequent_party_list


def getProjectFrequents(**kwargs):
	"""

	:param kwargs:
	:return:
	"""
	frequent_project_choices = []
	try:
		if kwargs['table'] != 'crdrnote':
			join = "JOIN projects p ON t1.project_code = p.id AND t1.enterprise_id = p.enterprise_id"
		else:
			join = """JOIN projects p ON t1.project_code = p.id AND t1.enterprise_id = p.enterprise_id 
			JOIN crdrnote c ON t1.grn_no = c.grn_no AND t1.enterprise_id = c.enterprise_id"""
		kwargs['table'] = 'grn' if kwargs['table'] == 'crdrnote' else kwargs['table']
		frequent_project_query = """SELECT t1.project_code,p.name,t1.enterprise_id,COUNT(1) AS item_count from %s t1
				%s WHERE t1.enterprise_id = '%s' and p.is_active = 1 %s GROUP BY t1.project_code, t1.enterprise_id
				ORDER BY item_count DESC LIMIT 0, 5""" % (
			kwargs['table'], join , kwargs['enterprise_id'], kwargs['condition'] if 'condition' in kwargs else '')
		logger.debug("Frequent Project Query:\n%s" % frequent_project_query)
		frequent_project_choices = executeQuery(frequent_project_query)
		frequent_project_choices = [(each[0], each[1]) for each in frequent_project_choices]
	except Exception as e:
		logger.exception("Fetching Project Frequents failed - %s" % e)
	return frequent_project_choices


def getMaterialFrequents(**kwargs):
	frequent_material_list = []
	frequent_material_query = """		%s COUNT(1) AS item_count FROM %s JOIN %s  ON %s	WHERE %s %s	ORDER BY item_count DESC LIMIT 0 , 5  """
	try:
		if kwargs['type'] != '':
			item_no_alias = 'item_id' if kwargs['particulars'] == 'purchase_order_material' else 'item_id'
			column_name = ('pid' if kwargs['particulars'] == 'purchase_order_material' else (
				'invoice_id' if kwargs['particulars'] == 'invoice_materials' else (
					'se_id' if kwargs['particulars'] == 'se_particulars' else (
						'indent_no' if kwargs['particulars'] == 'indent_material' else 'oa_id'))))
			party_col = ('supplier_id' if kwargs['particulars'] == 'purchase_order_material' else (
				'party_id' if kwargs['particulars'] == 'invoice_materials' else 'party_id'))
			if kwargs['type'] != 'indents':
				on_stmt = kwargs['table'] + '.id =' + kwargs['particulars'] + '.' + column_name
			else:
				on_stmt = kwargs['table'] + '.indent_no =' + kwargs['particulars'] + '.' + column_name
			select_stmt = 'select ' + kwargs['particulars'] + '.' + item_no_alias + ',' + kwargs[
				'particulars'] + '.' + 'make_id' + ',' + kwargs['particulars'] + '.' + 'enterprise_id ' + ','
			party_id = '' if kwargs['party_id'] == 'None' else kwargs['party_id']
			if kwargs['type'] != 'indents':
				where_stmt = (kwargs['table'] + '.enterprise_id = %s  AND ' + kwargs['table'] + ".type = '%s' ") % (
					kwargs['enterprise_id'], kwargs['type']) if party_id == '' \
					else (kwargs['table'] + '.enterprise_id = %s  AND ' + kwargs['table'] + ".type = '%s'  AND " +
					      kwargs['table'] + '.' + party_col + ' = %s  ') % (kwargs['enterprise_id'], kwargs['type'], party_id)
			else:
				where_stmt = (kwargs['table'] + '.enterprise_id = %s') % (kwargs['enterprise_id'])

			group_by_stmt = 'GROUP BY ' + kwargs['particulars'] + '.' + item_no_alias + ',' + kwargs[
				'particulars'] + '.make_id , ' + kwargs['table'] + '.enterprise_id'
			frequent_material_query = frequent_material_query % (
				select_stmt, kwargs['table'], kwargs['particulars'], on_stmt, where_stmt, group_by_stmt)
			logger.debug(frequent_material_query)
			frequent_list = [item for item in executeQuery(frequent_material_query, as_dict=True)]
			frequent_material_list = [{'item_id': row['item_id'], 'make_id': row['make_id']} for row in frequent_list]
			frequent_list = [item for item in executeQuery(frequent_material_query, as_dict=True)]
			frequent_material_list = [{'item_id': row['item_id'], 'make_id': row['make_id']} for row in frequent_list]
	except Exception as e:
		logger.exception("Frequent Material list fetching failed - %s" % e.message)
	return frequent_material_list


def inspectorFrequents(**kwargs):
	frequent_inspector_list = []
	if 'table' in kwargs:
		query = """SELECT inspector, COUNT(1) AS item_count FROM %s WHERE inspector !='' AND enterprise_id= %s
			GROUP BY inspector, enterprise_id ORDER BY item_count DESC LIMIT 0, 5""" % (
			kwargs['table'], kwargs['enterprise_id'])
		frequent_inspector_list = [item for item in executeQuery(query)]
	return frequent_inspector_list


def listUniqueEntriesFromDB(enterprise_id=None, table=None, column=None, frequents_only=False):
	"""

	:param enterprise_id:
	:param table:
	:param column:
	:param frequents_only:
	:return:
	"""
	if table is None or column is None:
		return []
	if frequents_only:
		order_by = "ORDER BY item_count DESC LIMIT 0, 5"
	else:
		order_by = "ORDER BY %s ASC" % column
	query = """SELECT %s, COUNT(1) AS item_count FROM %s 
		WHERE %s !='' AND %s IS NOT NULL AND enterprise_id=%s GROUP BY %s %s""" % (
		column, table, column, column, enterprise_id, column, order_by)
	return [item for item in executeQuery(query)]


def listAllIssueTo(enterprise_id=None, table=None, column=None):
	"""

	:param enterprise_id:
	:param table:
	:param column:
	:return:
	"""
	query = """SELECT %s  FROM %s 
		WHERE %s !='' AND %s IS NOT NULL AND enterprise_id=%s GROUP BY %s """ % (
		column, table, column, column, enterprise_id, column)
	issue_to_list = [item for item in executeQuery(query)]
	return issue_to_list


def listAllInspectors(**kwargs):
	query = """SELECT DISTINCT inspector FROM %s WHERE inspector !='' AND enterprise_id= %s 
		ORDER BY inspector ASC""" % (kwargs['table'], kwargs['enterprise_id'])
	frequent_inspector_list = [item[0] for item in executeQuery(query)]
	return frequent_inspector_list


def validateFileHeaders(**kwargs):
	status = False
	try:
		if kwargs['module'] == 'party':
			header_dict = {
				'payable_opening': 'opening', 'city': 'city', 'code': 'code', 'as_on': 'as_on', 'name': 'name',
				'receivable_opening': 'opening', 'payment_credit_days': 'credit_days', 'phone': 'phone', 'state': 'state',
				'country_code': 'country', 'pin_code': 'pin_code', 'contact': 'contact', 'address_1': 'address_1',
				'address_2': "", 'receipt_credit_days': 'credit_days',	'email': 'email', 'fax_no': 'fax_no',
				'gst_category': 'gst_category', 'port': 'port', 'gstin': 'gstin', 'pan': 'pan'}
			converted_dict = dict(
				(k.lower() if isinstance(k, basestring) else k, v.lower().rstrip() if isinstance(v, basestring) else v)
				for k, v
				in kwargs['title_row'].iteritems())
			if header_dict != converted_dict:
				status = False
			else:
				status = True
		if kwargs['module'] == 'accounts':
			header_dict = {
				'ledger_name': 'ledger_name', 'account_group_id': 'account_group_id', 'opening_debit': 'opening_debit',
				'opening_credit': 'opening_credit'}
			converted_dict = dict((k.lower() if isinstance(k, basestring) else k, v.lower().rstrip() if isinstance(v, basestring) else v) for k, v in kwargs['title_row'].iteritems())
			if header_dict != converted_dict:
				status = False
			else:
				status = True
		if kwargs['module'] == 'masters_goods':
			header_dict = {
				'drawing_no': 'drawing_no', 'name': 'name', 'category': 'category', 'description': 'description',
				'price': 'price', 'in_use': 'in_use', 'unit': 'unit', 'faultless': 'faultless',
				'faulty': 'faulty', 'hsn_sac': 'hsn'}
		elif kwargs['module'] == 'masters_service':
			header_dict = {
				'drawing_no': 'item_code', 'name': 'name', 'category': 'category', 'description': 'description',
				'price': 'price', 'in_use': 'in_use', 'unit': 'unit', 'hsn_sac': 'sac'}
		converted_dict = dict(
			(k.lower() if isinstance(k, basestring) else k, v.lower() if isinstance(v, basestring) else v) for k, v
			in kwargs['title_row'].iteritems())
		if header_dict == converted_dict:
			status = True
		else:
			status = False
	except Exception as e:
		logger.error("Error occurred %s:" % e)
	return status


def getTagList(tags=()):
	"""
	Forms the string array from tag model list

	:param tags:
	:return:
	"""
	tag_list = []
	for item in tags:
		tag_list.append(item.tag.tag)
	return tag_list


def getNumberOfMaterialWithNegativeStock(enterprise_id=None):
	"""
	:return: The count of materials profiled negative minimum stock level
	"""
	return dao.executeQuery("""select count(1) from materials 
				where enterprise_id=%s and minimum_stock_level < 0""" % enterprise_id)[0][0]


def resetNegativeStockToZero(enterprise_id=None):
	"""
	Resets the Minimum stock level to 0 where it is less than 0
	:return:
	"""
	logger.info("Reset the negative stock to zero")
	dao.executeQuery("""UPDATE materials SET minimum_stock_level='0'
			where enterprise_id=%s and minimum_stock_level < 0""" % enterprise_id)


def getUser(enterprise_id=None, user_id=None):
	"""

	:return: Returns user object after detach from Session
	"""
	user = SQLASession().query(User).join(UserEnterpriseMap).filter(UserEnterpriseMap.enterprise_id == enterprise_id, User.id == user_id).first()
	if user:
		make_transient(user)
	return user


def getEnterprise(enterprise_id=None):
	"""

	:return: Returns enterprise object after detach from Session
	"""
	enterprise = SQLASession().query(Enterprise).filter(Enterprise.id == enterprise_id).first()
	return enterprise


def getMakePartNumber(enterprise_id=None, item_id=None, make_id=None):
	"""

	:return: Returns plain text or None
	"""
	make_map = SQLASession().query(MaterialMakeMap.part_no).filter(
		MaterialMakeMap.enterprise_id == enterprise_id, MaterialMakeMap.make_id == make_id
		, MaterialMakeMap.item_id == item_id).first()
	if make_map:
		return make_map.part_no
	return None


def getPOMDeliverySchedules(po_id=None, enterprise_id=None, item_id=None, make_id=None):
	"""

	:param po_id:
	:param enterprise_id:
	:param item_id:
	:param make_id:
	:return:
	"""
	ds_query = """
			SELECT DATE(due_date) as due_date, qty from purchase_order_material_delivery_schedules WHERE po_id = %s AND item_id = %s 
							AND make_id = %s AND enterprise_id = %s order by due_date asc""" % (
		po_id, item_id, make_id, enterprise_id)
	ds_details = executeQuery(query=ds_query, as_dict=True)
	return ds_details


def smart_tax_sales_return(invoice_id=None, item_id=None, make_id=None, supplier_id=None, stockable=None, enterprise_id=None):
	cgst_tax, sgst_tax, igst_tax = 0, 0, 0
	if stockable:
		smart_tax_query = """
						select IFNULL((select (group_concat( DISTINCT JSON_OBJECT('code', t.code,'type', t.type )  SEPARATOR ';')) as code  from invoice_item_tax as it  join tax  as t on t.code=it.tax_code
						where invoice_id={invoice_id} and it.item_id = '{item_id}' and it.make_id ={make_id}),IFNULL((SELECT group_concat(JSON_OBJECT('code', t.code,'type', t.type )  SEPARATOR ';') AS tcode  FROM
						grn gn JOIN	grn_material_tax gmt ON gn.grn_no = gmt.grn_no AND gmt.enterprise_id = gn.enterprise_id
						JOIN tax AS t ON gmt.tax_code = t.code	AND gn.enterprise_id = t.enterprise_id	WHERE
						gmt.item_id = '{item_id}'	AND t.type  IN  ('SGST','CGST','IGST')	AND gmt.make_id = {make_id}
						AND gmt.enterprise_id = {enterprise_id}	AND gn.status > 0 AND gn.rec_against='Sales Return' AND gn.party_id={supplier_id} group by gn.grn_no ORDER BY gn.approved_on DESC
						limit 1),(SELECT group_concat(JSON_OBJECT('code', t.code,'type', t.type )  SEPARATOR ';') AS tcode FROM grn gn JOIN grn_material_tax gmt ON gn.grn_no = gmt.grn_no
						AND gmt.enterprise_id = gn.enterprise_id JOIN	tax AS t ON gmt.tax_code = t.code	AND gn.enterprise_id = t.enterprise_id
						WHERE	gmt.item_id = '{item_id}'	AND t.type  IN  ('SGST','CGST','IGST')	AND gmt.make_id = {make_id}
						AND gmt.enterprise_id = {enterprise_id}	AND gn.status > 0 AND gn.rec_against='Sales Return' group by gn.grn_no ORDER BY gn.approved_on DESC
						limit 1))) as code""".format(invoice_id=invoice_id, item_id=item_id, make_id=make_id, enterprise_id=enterprise_id, supplier_id=supplier_id)
	smart_taxes = executeQuery(query=smart_tax_query, as_dict=True)
	if smart_taxes[0]['code']:
		smart_taxes = smart_taxes[0]['code'].split(";")
		for tax in smart_taxes:
			if ast.literal_eval(tax)['type'] =='CGST':
				cgst_tax = ast.literal_eval(tax)['code']
			elif ast.literal_eval(tax)['type'] == 'SGST':
				sgst_tax = ast.literal_eval(tax)['code']
			else:
				igst_tax = ast.literal_eval(tax)['code']
	return {'cgst_tax': cgst_tax, 'sgst_tax': sgst_tax, 'igst_tax': igst_tax}


def populateInvoiceTemplateBaseFont():
	fonts = []
	for invoice_template_base_font in INVOICE_TEMPLATE_BASE_FONT:
		fonts.append((invoice_template_base_font, invoice_template_base_font))
	return fonts


def populatePurchaseTemplateBaseFont():
	fonts = []
	for purchase_template_base_font in PURCHASE_TEMPLATE_BASE_FONT:
		fonts.append({"font_value": purchase_template_base_font, "font_name": purchase_template_base_font})
	return fonts


def getProjectListBasedPermissions(user_id):
	project_list = []
	try:
		query = """SELECT p.id FROM projects p JOIN auth_user_enterprise_map ae ON p.project_enterprise_id = ae.enterprise_id 
		WHERE ae.user_id = {user_id} and status = 1""".format(user_id=user_id)
		query_data = executeQuery(query)
		for project_id in query_data:
			project_list.append(project_id[0])
	except Exception as e:
		logger.exception("Failed to Get permission project list - %s" %str(e))
	return project_list


def populateAllProjects(enterprise_id=None):
	project_query = """SELECT id, parent_id, name, code, project_enterprise_id, working_capital*-1 as cash_allocated FROM projects WHERE 
					enterprise_id = {enterprise_id} and is_active=1 ORDER BY parent_id ASC""".format(enterprise_id=enterprise_id)
	projects_details = executeQuery(query=project_query, as_dict=True)
	return projects_details


def getPermissionProjectsId(enterprise_id, user_id):
	project_ids = []
	db_session = SQLASession()
	try:
		user = db_session.query(User).filter(User.id == user_id).first()
		project_query = """SELECT id FROM projects WHERE enterprise_id = {enterprise_id} and is_active=1 and 
		project_enterprise_id in (select enterprise_id from auth_user_enterprise_map where user_id={user_id} and status=1) 
		ORDER BY parent_id ASC""".format(enterprise_id=enterprise_id, user_id=user_id)
		if user and user.is_super:
			project_query = "SELECT id FROM projects WHERE enterprise_id = {enterprise_id} and is_active=1".format(enterprise_id=enterprise_id)
		query_data = executeQuery(project_query)
		for project_id in query_data:
			project_ids.append(project_id[0])
	except Exception as e:
		logger.exception("Failed to Get permission project list - %s" % str(e))
	return project_ids


def saveAttachment(
		enterprise_id=None, label=None, file_to_be_saved=None, ext='', uploaded_by=None, uploaded_on=None, gcs_key=None,
		db_session=None):
	"""
	:return:
	"""
	if not db_session:
		db_session = SQLASession()
	db_session.begin(subtransactions=True)
	try:
		attachment = Attachment(
			label=str(label), enterprise_id=enterprise_id, file=str(file_to_be_saved), file_ext=str(ext),
			uploaded_on=uploaded_on, uploaded_by=uploaded_by, gcs_key=gcs_key)
		db_session.add(attachment)
		db_session.flush([attachment])
		attachment_id = attachment.attachment_id
		db_session.commit()
		logger.info("Saved attachment successfully with id %s" % attachment_id)
		return attachment_id
	except Exception as e:
		db_session.rollback()
		logger.exception("Failed persisting attachment... %s" % e.message)


def getConsolidatedMaterial(invoice=None, date_format='%Y-%m-%d %H:%M:%S'):
	db_session = SQLASession()
	available_materials = {}
	oa_se_details = ""
	try:
		for material in invoice.items:
			consolidated_oa_no = ""
			consolidated_delivered_dc = {}
			consolidated_grn = {}
			cgst_rate, sgst_rate, igst_rate = 0, 0, 0
			primary_unit_value = 0
			delivered_dc = db_session.query(Invoice).filter(
				Invoice.enterprise_id == material.enterprise_id, Invoice.id == material.delivered_dc_id).first()
			job_in_receipts = db_session.query(Receipt).filter(
				Receipt.enterprise_id == material.enterprise_id, Receipt.receipt_no == material.receipt_no).first()
			if material.getTaxesOfType("CGST")[0]:
				cgst_rate = material.getTaxesOfType("CGST")[0].tax.net_rate

			if material.getTaxesOfType("SGST")[0]:
				sgst_rate = material.getTaxesOfType("SGST")[0].tax.net_rate

			if material.getTaxesOfType("IGST")[0]:
				igst_rate = material.getTaxesOfType("IGST")[0].tax.net_rate
			material_name = material.item.name if material.item is not None else ""
			if material.item and material.item.makes_json != "":
				make_name = constructDifferentMakeName(material.item.makes_json)
				if make_name:
					material_name = material_name + " [" + make_name + "]"
			material.item_name = material_name
			material_make = material.make.label if material.make is not None else ""
			item_key = "%s,%s,%s,%s,%s,%s,%s%s,%s" % (
				material_name, material.rate, material_make, cgst_rate, sgst_rate, igst_rate,
				material.discount, material.hsn_code, "Faulty" if material.is_faulty == IS_FAULTY_TRUE else "")
			if item_key in available_materials.keys():
				consolidated_oa_no = available_materials[item_key]["consolidated_oa_no"]
				consolidated_delivered_dc = available_materials[item_key]["consolidated_dc_details"]
				consolidated_grn = available_materials[item_key]["consolidated_grn_details"]
				consolidated_quantity = available_materials[item_key]["consolidated_quantity"] + material.quantity
				quantity = material.quantity
				if material.alternate_unit_id:
					scale_factor = material.alternate_unit.scale_factor
					if scale_factor:
						consolidated_quantity = available_materials[item_key]["consolidated_quantity"] + (Decimal(material.quantity) / Decimal(scale_factor))
						quantity = Decimal(material.quantity) / Decimal(scale_factor)
						primary_unit_value = consolidated_quantity * Decimal(scale_factor)

				consolidated_remarks = available_materials[item_key]["consolidated_remarks"] + ", " + material.remarks
				cgst_rate = available_materials[item_key]["cgst_rate"]
				sgst_rate = available_materials[item_key]["sgst_rate"]
				igst_rate = available_materials[item_key]["igst_rate"]
				consolidated_cgst_value = available_materials[item_key][
					                          "consolidated_cgst_value"] + invoice.getConsolidatedTaxValue(
					rate=cgst_rate, item_quantity=material.quantity, item_rate=material.rate,
					item_discount=material.discount)
				consolidated_sgst_value = available_materials[item_key][
					                          "consolidated_sgst_value"] + invoice.getConsolidatedTaxValue(
					rate=sgst_rate, item_quantity=material.quantity, item_rate=material.rate,
					item_discount=material.discount)
				consolidated_igst_value = available_materials[item_key][
					                          "consolidated_igst_value"] + invoice.getConsolidatedTaxValue(
					rate=igst_rate, item_quantity=material.quantity, item_rate=material.rate,
					item_discount=material.discount)
				consolidated_taxable_value = available_materials[item_key]["consolidated_taxable_value"] + (
						material.rate * material.quantity * (100 - material.discount) / 100)
				entry_order = min(material.entry_order, available_materials[item_key]["entry_order"])
				if material.inv_oa not in (None, ""):
					job_in_dc_details = ""
					if material.inv_oa.type == 'Job':
						job_in_item = db_session.query(ReceiptMaterial).filter(
							ReceiptMaterial.enterprise_id == material.enterprise_id,
							ReceiptMaterial.oa_id == material.oa_no).first()
						if job_in_item:
							job_in_dc_details = " [%s - %s]" % (job_in_item.receipt.invoice_no, str(
								job_in_item.receipt.invoice_date.strftime(str(date_format))))
					consolidated_oa_no += ", " + "%s%s" % (material.inv_oa.getInternalCode(), job_in_dc_details)
				if material.delivered_dc_id not in (None, "", 0):
					consolidated_delivered_dc[material.delivered_dc_id] = {
						'code': delivered_dc.getInternalCode(), 'qty': quantity,
						'approved_on': str(delivered_dc.issued_on.strftime(str(date_format)))}
				if material.receipt_no not in (None, "", 0):
					consolidated_grn[material.receipt_no] = {'code': job_in_receipts.getCode(), 'qty': material.quantity}
				inv_material = {
					"material": material, "consolidated_oa_no": consolidated_oa_no,
					"consolidated_quantity": consolidated_quantity, "consolidated_remarks": consolidated_remarks,
					"cgst_rate": cgst_rate, "sgst_rate": sgst_rate, "igst_rate": igst_rate,
					"consolidated_cgst_value": consolidated_cgst_value,
					"consolidated_sgst_value": consolidated_sgst_value,
					"consolidated_igst_value": consolidated_igst_value, "entry_order": entry_order,
					"consolidated_dc_details": consolidated_delivered_dc,
					"consolidated_grn_details": consolidated_grn,
					"consolidated_taxable_value": consolidated_taxable_value, "primary_unit_value": primary_unit_value}
				available_materials[item_key] = inv_material
			else:
				consolidated_quantity = material.quantity
				quantity = material.quantity
				if material.alternate_unit_id:
					scale_factor = material.alternate_unit.scale_factor
					if scale_factor:
						consolidated_quantity = Decimal(material.quantity) / Decimal(scale_factor)
						quantity = Decimal(material.quantity) / Decimal(scale_factor)
						primary_unit_value = consolidated_quantity * Decimal(scale_factor)
				consolidated_remarks = material.remarks
				consolidated_cgst_value = invoice.getConsolidatedTaxValue(
					rate=cgst_rate, item_quantity=material.quantity, item_rate=material.rate,
					item_discount=material.discount)
				consolidated_sgst_value = invoice.getConsolidatedTaxValue(
					rate=sgst_rate, item_quantity=material.quantity, item_rate=material.rate,
					item_discount=material.discount)
				consolidated_igst_value = invoice.getConsolidatedTaxValue(
					rate=igst_rate, item_quantity=material.quantity, item_rate=material.rate,
					item_discount=material.discount)
				consolidated_taxable_value = material.rate * material.quantity * (100 - material.discount) / 100
				entry_order = material.entry_order
				if material.inv_oa not in (None, ""):
					job_in_dc_details = ""
					if material.inv_oa.type == 'Job':
						job_in_item = db_session.query(ReceiptMaterial).filter(
							ReceiptMaterial.enterprise_id == material.enterprise_id,
							ReceiptMaterial.oa_id == material.oa_no).first()
						if job_in_item:
							job_in_dc_details = " [%s - %s]" % (job_in_item.receipt.invoice_no, str(
								job_in_item.receipt.invoice_date.strftime(str(date_format))))
					consolidated_oa_no = "%s%s" % (material.inv_oa.getInternalCode(), job_in_dc_details)
				if material.delivered_dc_id not in (None, "", 0):
					consolidated_delivered_dc[material.delivered_dc_id] = {
						'code': delivered_dc.getInternalCode(), 'qty': quantity,
						'approved_on': str(delivered_dc.issued_on.strftime(str(date_format)))}
				if material.receipt_no not in (None, "", 0):
					consolidated_grn[material.receipt_no] = {'code': job_in_receipts.getCode(), 'qty': material.quantity}
				inv_material = {
					"material": material, "consolidated_oa_no": consolidated_oa_no,
					"consolidated_quantity": consolidated_quantity, "consolidated_remarks": consolidated_remarks,
					"cgst_rate": cgst_rate, "sgst_rate": sgst_rate, "igst_rate": igst_rate,
					"consolidated_cgst_value": consolidated_cgst_value,
					"consolidated_sgst_value": consolidated_sgst_value,
					"consolidated_igst_value": consolidated_igst_value, "entry_order": entry_order,
					"consolidated_dc_details": consolidated_delivered_dc,
					"consolidated_grn_details": consolidated_grn,
					"consolidated_taxable_value": consolidated_taxable_value, "primary_unit_value": primary_unit_value}
				available_materials[item_key] = inv_material

			if material.inv_oa and material.inv_oa.se_id and material.inv_oa.se_id != "":
				se_date = str(material.inv_oa.se_date.strftime(str(date_format))) if material.inv_oa.se_date is not None and material.inv_oa.se_date != '0000-00-00 00:00:00' else ""
				oa_se_details = "{default}{separator}{se_code} {connector} {se_date}".format(
					default=oa_se_details, separator=", " if oa_se_details != "" else "", se_code=material.inv_oa.se.getInternalCode(),
					connector="-" if se_date != "" else "", se_date=se_date)
		return available_materials, oa_se_details
	except Exception as e:
		logger.exception("Failed to consolidate invoice material... %s" % e.message)


def getHSNSummary(invoice=None):
	consolidated_materials = getConsolidatedMaterial(invoice)
	available_materials = {}
	try:
		for item_key in consolidated_materials[0]:
			material_items = consolidated_materials[0][item_key]
			material = material_items['material']

			cgst_rate, sgst_rate, igst_rate = 0, 0, 0
			if material.getTaxesOfType("CGST")[0]:
				cgst_rate = material.getTaxesOfType("CGST")[0].tax.net_rate

			if material.getTaxesOfType("SGST")[0]:
				sgst_rate = material.getTaxesOfType("SGST")[0].tax.net_rate

			if material.getTaxesOfType("IGST")[0]:
				igst_rate = material.getTaxesOfType("IGST")[0].tax.net_rate

			item_key = "%s,%s,%s,%s" % (
				round(cgst_rate, 2), round(sgst_rate, 2), round(igst_rate, 2), material.hsn_code)
			consolidated_quantity = material_items["consolidated_quantity"]
			if item_key in available_materials.keys():
				consolidated_cgst_rate = cgst_rate
				consolidated_sgst_rate = sgst_rate
				consolidated_igst_rate = igst_rate
				consolidated_cgst_value = available_materials[item_key][
					                          "consolidated_cgst_value"] + invoice.getConsolidatedTaxValue(
					rate=cgst_rate, item_quantity=consolidated_quantity, item_rate=material.rate,
					item_discount=material.discount)
				consolidated_sgst_value = available_materials[item_key][
					                          "consolidated_sgst_value"] + invoice.getConsolidatedTaxValue(
					rate=sgst_rate, item_quantity=consolidated_quantity, item_rate=material.rate,
					item_discount=material.discount)
				consolidated_igst_value = available_materials[item_key][
					                          "consolidated_igst_value"] + invoice.getConsolidatedTaxValue(
					rate=igst_rate, item_quantity=consolidated_quantity, item_rate=material.rate,
					item_discount=material.discount)
				consolidated_taxable_value = available_materials[item_key]["consolidated_taxable_value"] + (
						material.rate * material_items["consolidated_quantity"] * (100 - material.discount) / 100)
				hsn_summary = {
					"consolidated_taxable_value": consolidated_taxable_value,
					"hsn_code": material.hsn_code,
					"consolidated_cgst_rate": consolidated_cgst_rate,
					"consolidated_sgst_rate": consolidated_sgst_rate,
					"consolidated_igst_rate": consolidated_igst_rate,
					"consolidated_cgst_value": consolidated_cgst_value,
					"consolidated_sgst_value": consolidated_sgst_value,
					"consolidated_igst_value": consolidated_igst_value}
				available_materials[item_key] = hsn_summary
			else:
				consolidated_cgst_rate = cgst_rate
				consolidated_sgst_rate = sgst_rate
				consolidated_igst_rate = igst_rate
				consolidated_cgst_value = invoice.getConsolidatedTaxValue(
					rate=cgst_rate, item_quantity=consolidated_quantity, item_rate=material.rate,
					item_discount=material.discount)
				consolidated_sgst_value = invoice.getConsolidatedTaxValue(
					rate=sgst_rate, item_quantity=consolidated_quantity, item_rate=material.rate,
					item_discount=material.discount)
				consolidated_igst_value = invoice.getConsolidatedTaxValue(
					rate=igst_rate, item_quantity=consolidated_quantity, item_rate=material.rate,
					item_discount=material.discount)
				consolidated_taxable_value = material.rate * material_items["consolidated_quantity"] * (
						100 - material.discount) / 100
				hsn_summary = {
					"consolidated_taxable_value": consolidated_taxable_value,
					"hsn_code": material.hsn_code,
					"consolidated_cgst_rate": consolidated_cgst_rate,
					"consolidated_sgst_rate": consolidated_sgst_rate,
					"consolidated_igst_rate": consolidated_igst_rate,
					"consolidated_cgst_value": consolidated_cgst_value,
					"consolidated_sgst_value": consolidated_sgst_value,
					"consolidated_igst_value": consolidated_igst_value}
				available_materials[item_key] = hsn_summary

		for charge in invoice.charges:
			if charge.hsn_code:
				tax = charge.getInvoiceChargeTaxValue()
				cgst_rate = tax['cgst_rate']
				sgst_rate = tax['sgst_rate']
				igst_rate = tax['igst_rate']
				cgst_value = tax['cgst_value']
				sgst_value = tax['sgst_value']
				igst_value = tax['igst_value']
				item_key = "%s,%s,%s,%s" % (
					round(cgst_rate, 2), round(sgst_rate, 2), round(igst_rate, 2), charge.hsn_code)
				if item_key in available_materials.keys():
					consolidated_cgst_rate = cgst_rate
					consolidated_sgst_rate = sgst_rate
					consolidated_igst_rate = igst_rate
					consolidated_cgst_value = available_materials[item_key]["consolidated_cgst_value"] + cgst_value
					consolidated_sgst_value = available_materials[item_key]["consolidated_sgst_value"] + sgst_value
					consolidated_igst_value = available_materials[item_key]["consolidated_igst_value"] + igst_value
					consolidated_taxable_value = available_materials[item_key]["consolidated_taxable_value"] + (
							charge.rate * (100 - (charge.discount if charge.discount else 0)) / 100)
					hsn_summary = {
						"consolidated_taxable_value": consolidated_taxable_value,
						"hsn_code": charge.hsn_code,
						"consolidated_cgst_rate": consolidated_cgst_rate,
						"consolidated_sgst_rate": consolidated_sgst_rate,
						"consolidated_igst_rate": consolidated_igst_rate,
						"consolidated_cgst_value": consolidated_cgst_value,
						"consolidated_sgst_value": consolidated_sgst_value,
						"consolidated_igst_value": consolidated_igst_value}
					available_materials[item_key] = hsn_summary
				else:
					consolidated_cgst_rate = cgst_rate
					consolidated_sgst_rate = sgst_rate
					consolidated_igst_rate = igst_rate
					consolidated_cgst_value = cgst_value
					consolidated_sgst_value = sgst_value
					consolidated_igst_value = igst_value
					consolidated_taxable_value = charge.rate * (100 - (charge.discount if charge.discount else 0)) / 100
					hsn_summary = {
						"consolidated_taxable_value": consolidated_taxable_value,
						"hsn_code": charge.hsn_code,
						"consolidated_cgst_rate": consolidated_cgst_rate,
						"consolidated_sgst_rate": consolidated_sgst_rate,
						"consolidated_igst_rate": consolidated_igst_rate,
						"consolidated_cgst_value": consolidated_cgst_value,
						"consolidated_sgst_value": consolidated_sgst_value,
						"consolidated_igst_value": consolidated_igst_value}
					available_materials[item_key] = hsn_summary

		return available_materials
	except Exception as e:
		logger.exception("Failed to consolidate hsn summary... %s" % e.message)


def getStateList(country_code=None):
	try:
		collection = 'countries'
		db = MongoDbConnect[collection]

		if country_code:
			country = db.find_one({"code": country_code})
		else:
			country = db.find_one({"code": "IN"})

		if country:
			state_list = country["states"]
			sorted_state_list = sorted(state_list, key=sortFunction)
			return sorted_state_list
		else:
			logger.error("Country code %s not found" % country_code)
			return None
	except Exception as e:
		logger.exception("Failed to fetch state list... %s" % e.message)
		return None


def sortFunction(value):
	return value["description"]


def saveEInvoiceAckData(entity=None, ack_data=None, user_id=None, db_session=None):
	"""

	:param entity:
	:param ack_data:
	:param user_id:
	:param db_session:
	"""
	try:
		db_session.begin(subtransactions=True)

		entity.irn_ack_json = ack_data
		entity.last_modified_on = datetime.now()
		entity.last_modified_by = user_id

		db_session.add(entity)
		db_session.commit()

	except Exception as e:
		logger.exception(e.message)
		db_session.rollback()
		raise e
	return True


class EinvoiceJsonMaker:
	"""

	"""
	einvoice_keyset = OrderedDict()
	regexp_fieldset = {}
	error_message = []

	def __init__(self):
		self.regexp_fieldset = {
			'TaxSch': ['Y', '^(GST)$', 'GST- Goods and Services Tax Scheme'],
			'SupTyp': ['Y', '(?i)^((B2B)|(SEZWP)|(SEZWOP)|(EXPWP)|(EXPWOP)|(DEXP))$', 'Type of Supply'],
			'Typ': ['Y', '(?i)^((INV)|(CRN)|(DBN))$', 'Document Type'],
			'No': ['Y', '^([a-zA-Z1-9]{1}[a-zA-Z0-9\/-]{0,15})$', 'Document Number'],
			'Dt': ['Y', '^[0-3][0-9]\/[0-1][0-9]\/[2][0][1-2][0-9]$', 'Document date'],
			'Gstin': ['Y', '([0-9]{2}[0-9A-Z]{13})', 'GSTIN of supplier'],
			'LglNm': ['Y', """^(.|\s)*[a-zA-Z]+(.|\s){3,100}$""", 'Legal Name'],
			'Addr1': ['Y', """^^(.|\s)*[a-zA-Z]+(.|\s){1,100}$""", 'Building/Flat no, Road/Street'],
			'Loc': ['Y', """^(.|\s)*[a-zA-Z]+(.|\s){3,50}$""", 'Location'],
			'Pin': ['Y', '^[1-9][0-9]{5}$', 'Pincode'],
			'Stcd': ['Y', '^(?!0+$)([0-9]{1,2})$', 'State Code Of Supplier'],
			'Pos': ['Y', '^(?!0+$)([0-9]{1,2})$', 'State code of Place of supply'],
			'AssVal': ['Y', '^\d+.?\d{0,3}$', 'Total Assessable value'],
			'TotInvVal': ['Y', '^\d+.?\d{0,3}$', 'Final Invoice Value'],
			'IsServc': ['Y', '^([Y|N]{1})$', 'the supply is service or not.'],
			'HsnCd': ['Y', '^(?!0+$)([0-9]{4}|[0-9]{6}|[0-9]{6, 8})$', 'HSN Code'],
			'UnitPrice': ['Y', '^\d+.?\d{0,3}$', 'Unit Price'],
			'Qty': ['Y', '^\d*\.\d+|\d+', 'Qty'],
			'Unit': ['Y', '^([a-zA-Z]{3})$', 'Unit'],
			'TotAmt': ['Y', '', 'Gross Amount'],
			'AssAmt': ['Y', '', 'Taxable Value'],
			'GstRt': ['Y', '', 'GST rate'],
			'TotItemVal': ['Y', '^\d+.?\d{0,2}$', 'Total Item Value']
		}

		self.extend_validation_fields = {
			'Pin': {'idx': 1, 'collection': "pincode", 'title': "Pin code"},
			'HsnCd': {'idx': 2, 'collection': "hsn_code", 'title': "HSN code"},
			'Stcd': {'idx': 0, 'collection': "state_code", 'title': "State code"},
			'Unit': {'idx': 0, 'collection': "uqc_code", 'title': "Unit code"}
		}

	def validateFieldsByRegisteredData(self, key=None, data=None):
		"""

		:param key:
		:param data:
		:return:
		"""
		try:
			db = MongoDbConnect[self.extend_validation_fields[key]['collection']]
			fetch = db.find_one({'code': str(data).lstrip('0')})
			if fetch is not None and len(fetch) > 0:
				return True
			else:
				self.error_message.append({'ErrorMessage': "%s is not valid" % self.extend_validation_fields[key]['title']})
				return False
		except Exception as e:
			self.error_message.append({'ErrorMessage': "%s is not valid" % key})
			logger.exception(e.message)
			return False

	def validateWithRegExp(self, key=None, data=None, title=None):
		"""
		:param key:
		:param data:
		:param title:
		:return:
		"""
		if str(key) in self.extend_validation_fields:
			return_response = self.validateFieldsByRegisteredData(key=key, data=data)
		else:
			data = str(data)
			regex = re.compile(r'%s' % self.regexp_fieldset[key][1])
			if not re.match(regex, data):
				self.error_message.append({'ErrorMessage': "%s is invalid '%s'" % (self.regexp_fieldset[key][2], " in %s" % title if title is not None else "")})
			return_response = re.match(regex, data)
		return return_response

	def validateEInvoiceJson(self, invoice=None, item_data=None, common_tax_data=None):
		"""

		:param invoice:
		:param item_data:
		:return:
		"""
		self.error_message = []
		try:
			response = OrderedDict()
			response['Version'] = str(invoice['Version'])

			response['TranDtls'] = {}
			if self.validateWithRegExp(key="TaxSch", data=invoice['TaxSch'], title='Transaction Details'):
				response['TranDtls']['TaxSch'] = invoice['TaxSch']
			if self.validateWithRegExp(key="SupTyp", data=invoice['SupTyp'], title='Transaction Details'):
				response['TranDtls']['SupTyp'] = invoice['SupTyp']

			response['DocDtls'] = {}
			if self.validateWithRegExp(key="Typ", data=invoice['Typ'], title='Document Details'):
				response['DocDtls']['Typ'] = invoice['Typ']
			if self.validateWithRegExp(key="No", data=invoice['No'], title='Document Details'):
				response['DocDtls']['No'] = invoice['No']
			if self.validateWithRegExp(key="Dt", data=invoice['Dt'].strftime('%d/%m/%Y'), title='Document Details'):
				response['DocDtls']['Dt'] = invoice['Dt'].strftime('%d/%m/%Y')

			response['SellerDtls'] = {}
			if self.validateWithRegExp(key="Gstin", data=invoice['Gstin'], title='Seller Details'):
				response['SellerDtls']['Gstin'] = invoice['Gstin']
			if self.validateWithRegExp(key="LglNm", data=invoice['LglNm'], title='Seller Details'):
				response['SellerDtls']['LglNm'] = invoice['LglNm']
			if self.validateWithRegExp(key="Addr1", data=invoice['Addr1'], title='Seller Details'):
				response['SellerDtls']['Addr1'] = invoice['Addr1']
			if self.validateWithRegExp(key="Loc", data=invoice['Loc'], title='Seller Details'):
				response['SellerDtls']['Loc'] = invoice['Loc']
			if self.validateWithRegExp(key="Pin", data=invoice['Pin'], title='Seller Details'):
				response['SellerDtls']['Pin'] = int(invoice['Pin'])
			if self.validateWithRegExp(key="Stcd", data=invoice['Stcd'], title='Seller Details'):
				response['SellerDtls']['Stcd'] = invoice['Stcd']

			response['BuyerDtls'] = {}
			if self.validateWithRegExp(key="Gstin", data=invoice['p_Gstin'], title='Buyer Details'):
				response['BuyerDtls']['Gstin'] = invoice['p_Gstin']
			if self.validateWithRegExp(key="LglNm", data=invoice['p_LglNm'], title='Buyer Details'):
				response['BuyerDtls']['LglNm'] = invoice['p_LglNm']
			if self.validateWithRegExp(key="Pos", data=invoice['p_Pos'], title='Buyer Details'):
				response['BuyerDtls']['Pos'] = invoice['p_Pos']
			if self.validateWithRegExp(key="Addr1", data=invoice['p_Addr1'], title='Buyer Details'):
				response['BuyerDtls']['Addr1'] = invoice['p_Addr1']
			if self.validateWithRegExp(key="Loc", data=invoice['p_Loc'], title='Buyer Details'):
				response['BuyerDtls']['Loc'] = invoice['p_Loc']
			if self.validateWithRegExp(key="Pin", data=invoice['p_Pin'], title='Buyer Details'):
				response['BuyerDtls']['Pin'] = int(invoice['p_Pin'])
			if self.validateWithRegExp(key="Stcd", data=invoice['p_Stcd'], title='Buyer Details'):
				response['BuyerDtls']['Stcd'] = invoice['p_Stcd']

			response['ItemList'] = []
			for idx, item in enumerate(item_data):
				item_list = {}
				item_list['SlNo'] = str(idx+1)
				if self.validateWithRegExp(key="IsServc", data=item['IsServc'], title='Item Details'):
					item_list['IsServc'] = item['IsServc']
				if self.validateWithRegExp(key="HsnCd", data=item['HsnCd'], title='Item Details'):
					item_list['HsnCd'] = item['HsnCd']
				if self.validateWithRegExp(key="Qty", data=item['Qty'], title='Item Details'):
					item_list['Qty'] = item['Qty']
				if self.validateWithRegExp(key="Unit", data=item['Unit'], title='Item Details'):
					item_list['Unit'] = item['Unit']
				item_list['Discount'] = item['Discount'] if item['Discount'] is not None else 0
				item_list['CgstAmt'] = item['CgstAmt'] if item['CgstAmt'] is not None else 0.0
				item_list['SgstAmt'] = item['SgstAmt'] if item['SgstAmt'] is not None else 0.0
				if not item_list['CgstAmt'] > 0 or not item_list['SgstAmt'] > 0:
					item_list['IgstAmt'] = item['IgstAmt'] if item['IgstAmt'] is not None else 0.0
				if common_tax_data[0]['CesRt'] is not None and float(common_tax_data[0]['CesRt']) > 0:
					item_list['CesAmt'] = round(float(item['AssAmt']) * float(common_tax_data[0]['CesRt'])/100, 2)
					item_list['CesRt'] = float(common_tax_data[0]['CesRt'])
				item_list['GstRt'] = item['GstRt'] if item['GstRt'] is not None else 0
				if self.validateWithRegExp(key="UnitPrice", data=item['UnitPrice'], title='Item Details'):
					item_list['UnitPrice'] = item['UnitPrice']
				if self.validateWithRegExp(key="TotAmt", data=item['TotAmt'], title='Item Details'):
					item_list['TotAmt'] = item['TotAmt']
				if self.validateWithRegExp(key="AssAmt", data=item['AssAmt'], title='Item Details'):
					item_list['AssAmt'] = item['AssAmt']
				if self.validateWithRegExp(key="GstRt", data=item['GstRt'], title='Item Details'):
					item_list['GstRt'] = item['GstRt'] if item['GstRt'] is not None else 0
				if self.validateWithRegExp(key="TotItemVal", data=item['TotItemVal'], title='Item Details'):
					tot_item_val = 0.0
					tot_item_val = float(item['CgstAmt']) if item['CgstAmt'] is not None else tot_item_val
					tot_item_val = float(item['SgstAmt']) + tot_item_val if item['SgstAmt'] is not None else tot_item_val
					tot_item_val = float(item['IgstAmt']) + tot_item_val if item['IgstAmt'] is not None else tot_item_val
					tot_item_val = float(item['CesAmt']) + tot_item_val if item['CesAmt'] is not None else tot_item_val
					item['AssAmt'] = item['AssAmt'] if item['AssAmt'] is not None else 0.0
					item_list['TotItemVal'] = float(item['AssAmt']) + tot_item_val
					item_list['TotItemVal'] = round(item_list['TotItemVal'], 2)
				response['ItemList'].append(item_list)

			response['ValDtls'] = {}
			AssVal = 0.0
			CgstVal = 0.0
			SgstVal = 0.0
			IgstVal = 0.0
			CesVal = 0.0
			for idx, item in enumerate(item_data):
				if item['AssAmt'] is not None:
					AssVal = AssVal + float(item['AssAmt'])
				if item['CgstAmt'] is not None:
					CgstVal = CgstVal + float(item['CgstAmt'])
				if item['SgstAmt'] is not None:
					SgstVal = SgstVal + float(item['SgstAmt'])
				if (not item['CgstAmt'] > 0 or not item['SgstAmt'] > 0) and item['IgstAmt'] is not None:
					IgstVal = IgstVal + float(item['IgstAmt'])
				if common_tax_data[0]['CesRt'] is not None and float(common_tax_data[0]['CesRt']) > 0:
					CesVal = round((AssVal * float(common_tax_data[0]['CesRt'])/100) + CesVal, 2)
			response['ValDtls']['AssVal'] = round(AssVal, 2)
			response['ValDtls']['CgstVal'] = round(CgstVal, 2)
			response['ValDtls']['SgstVal'] = round(SgstVal, 2)
			if CesVal > 0:
				response['ValDtls']['CesVal'] = CesVal
			if not CgstVal > 0 or not SgstVal > 0:
				response['ValDtls']['IgstVal'] = round(IgstVal, 2)
			if self.validateWithRegExp(key="TotInvVal", data=invoice['TotInvVal'], title='Value Details'):
				response['ValDtls']['TotInvVal'] = 0 if invoice['TotInvVal'] is None else invoice['TotInvVal']

		except Exception as e:
			logger.exception(e.message)
			raise e
		return {'response': response, 'error': self.error_message}


class CustomJSONEncoder(json.JSONEncoder):
	def default(self, obj):
		if isinstance(obj, datetime):
			return obj.isoformat()
		elif isinstance(obj, Decimal):
			return float(obj)
		return super(CustomJSONEncoder, self).default(obj)


def model_to_dict(model_instance):
	"""
	Helper function to convert SQLAlchemy model instance to a dictionary.
	"""
	return {c.name: getattr(model_instance, c.name) for c in model_instance.__table__.columns}


def constructDifferentMakeName(make_list):
	make_names = ""
	try:
		make_name_list = []
		if make_list:
			for rec in json.loads(make_list):
				make_name_list.append(
					rec['make'] + '-' + str(rec.get('mpn', '')) if rec.get('mpn') else rec['make'])
		make_names = " / ".join(make_name_list)
	except Exception as e:
		logger.exception("Failed to Concat Make name list due to - %s" % str(e))
	return make_names

def constructMpnMakeName(make_list):
	make_names = ""
	try:
		make_name_list = []
		if make_list:
			for rec in json.loads(make_list):
				make_name_list.append(
					(str(rec.get('mpn', '')) + "[" + rec['make'] + "]") if rec.get('mpn') else rec['make'])
		make_names = ", ".join(make_name_list)
	except Exception as e:
		logger.exception("Failed to Concat Mpn with Make  name list due to - %s" % str(e))
	return make_names

def validate_payload(required_fields):
	def decorator(func):
		@wraps(func)
		def wrapper(request, *args, **kwargs):
			missing_fields = [field for field in required_fields if not request.POST.get(field)]
			if missing_fields:
				response = response_code.paramMissing()
				response["missing_params"] = missing_fields
				return HttpResponse(json.dumps(response), status=response['response_code'], mimetype='application/json')
			return func(request, *args, **kwargs)
		return wrapper
	return decorator

def validate_payload_with_file(required_fields):
	def decorator(func):
		@wraps(func)
		def wrapper(request, *args, **kwargs):
			# Check for missing fields
			missing_fields = [field for field in required_fields if not request.POST.get(field)]
			if missing_fields:
				response = response_code.paramMissing()
				response["missing_params"] = missing_fields
				return HttpResponse(json.dumps(response), status=response['response_code'], content_type='application/json')

			# Validate file type
			if 'file' not in request.FILES:
				response = response_code.paramMissing()
				response["missing_params"] = ['file']
				return HttpResponse(json.dumps(response), status=response['response_code'], content_type='application/json')

			file = request.FILES['file']
			file_extension = os.path.splitext(file.name)[1].lower()
			if file_extension not in ['.csv', '.xlsx']:
				response = response_code.failure()
				response['error'] = "Invalid file type. Only .csv or .xlsx files are accepted."
				return HttpResponse(json.dumps(response), content_type='application/json', status=400)

			return func(request, *args, **kwargs)
		return wrapper
	return decorator


def populateLocationChoices(enterprise_id=None, user_id=None):
	"""
	populate the locations based on the user
	"""
	location_list = []
	try:
		user_data = SQLASession().query(User).filter(
			User.id == user_id, User.enterprise_id == enterprise_id).first()
		if user_data.is_super:
			location_list = SQLASession().query(LocationMaster.id, LocationMaster.name).filter(
				LocationMaster.enterprise_id == enterprise_id).all()
		else:
			location_list = SQLASession().query(LocationMaster.id, LocationMaster.name).join(UserLocationMap,
			                                                                                 LocationMaster.id == UserLocationMap.location_id).filter(
				UserLocationMap.user_id == user_id,
				UserLocationMap.enterprise_id == enterprise_id,
				UserLocationMap.status == 1,
				).all()
	except Exception as e:
		logger.info("failed to populate the location choices: %s", e)
	return location_list


def create_or_update_closing_stock(enterprise_id=None, item_id=None, is_sales=None, quantity=None, is_faulty=None,
							location_id=None):
	try:
		# Parameterized query to fetch stock record
		query = """SELECT * FROM closing_stock
				   WHERE enterprise_id=%s AND item_id=%s AND is_faulty=%s AND location_id=%s;"""
		stock_to_be_update = executeQuery(query=query, query_data=(enterprise_id, item_id, is_faulty, location_id),
										  as_dict=True)
		if stock_to_be_update:
			logger.info("Closing stock update triggered")
			# Calculate closing quantity based on sales or purchase
			closing_qty = float(stock_to_be_update[0]["qty"]) - quantity if is_sales else float(
				stock_to_be_update[0]["qty"]) + quantity
			# Update query with parameterized values
			update_query = """UPDATE closing_stock 
							  SET qty=%s, last_updated_on=%s 
							  WHERE enterprise_id=%s AND item_id=%s AND is_faulty=%s AND location_id=%s;"""
			executeQuery(query=update_query,
						 query_data=(closing_qty, datetime.today(), enterprise_id, item_id, is_faulty, location_id))
		else:
			logger.info("Closing stock insert triggered")
			# Insert new stock record
			qty = -abs(quantity) if is_sales else abs(quantity)
			insert_query = """INSERT INTO closing_stock(item_id, location_id, qty, last_updated_on, enterprise_id, is_faulty)
			                  VALUES (%s, %s, %s, %s, %s, %s);"""

			executeQuery(query=insert_query,
						 query_data=(item_id, location_id, qty, datetime.today(), enterprise_id, is_faulty))
			logger.info("Closing stock insert successfully")
	except Exception as e:
		logger.error("Failed to calculate the closing stock: %s", e)

