<style>
	.po_template_view .table.row-seperator th,
	.po_template_view .table.row-seperator td,
	.po_template_view .table.row-seperator th,
	.po_template_view .table.row-seperator td {
	    border-top: 1px solid #000 !important;
	    border-bottom: 1px solid #000 !important;
	}

	.po_template_view .table.column-seperator th,
	.po_template_view .table.column-seperator td,
	.po_template_view .table.column-seperator th,
	.po_template_view .table.column-seperator td {
	    border-right: 1px solid #000 !important;
	    border-left: 1px solid #000 !important;
	}

</style>
<atable class="table item_table table-bordered row-seperator column-seperator" id="item_table" style="font-size:11px; width: 100%">
    <athead>
        <atr class="row_seperator column_seperator header_shading">
            <ath class="text-center td_sno td_sno_text" rowspan="2" scope="col">S.no</ath>
            <ath class="text-center td_description td_description_text" rowspan="2" style="width:30%;">Description</ath>
            <ath class="text-center pdf_item_hsn_code_txt td_hsn_code hide for-non-pp" rowspan="2">HSN/SAC</ath>
            <ath class="text-center td_qty td_qty_text" rowspan="2" scope="col">Qty</ath>
            <ath class="text-center td_uom td_uom_text" rowspan="2" scope="col">UOM</ath>
            <ath class="text-center td_price td_price_text for-non-pp" rowspan="2" scope="col">Price<br>USD</ath>
            <ath class="text-center td_disc td_disc_text for-non-pp" rowspan="2" scope="col">Disc<br>%</ath>
            <ath class="text-center td_tax td_tax_text for-non-pp" rowspan="2" scope="col">Total<br>USD</ath>
			<ath class="text-center td_cgst tax_rate_column for-non-pp" colspan="2" style="width: 12%; ">CGST</ath>
			<ath class="text-center td_sgst tax_rate_column for-non-pp" colspan="2" style="width: 12%; ">SGST</ath>
			<ath class="text-center td_igst tax_rate_column for-non-pp" colspan="2" style="width: 12%;  border-right-color: #ccc;">IGST</ath>
			<ath class="text-center td_tax_amt tax_one_column hide for-non-pp" rowspan="2" style="width: 6%">CGST</ath>
			<ath class="text-center td_tax_amt tax_one_column hide for-non-pp" rowspan="2" style="width: 6%">SGST</ath>
			<ath class="text-center td_tax_amt tax_one_column hide for-non-pp" rowspan="2" style="width: 6%">IGST</ath>
			</atr>
			<atr class="row_seperator column_seperator tax_rate_column header_shading tr_second_row for-non-pp">
			<ath class="text-center td_cgst_rate td_tax_rate td_gst_rate">%</ath>
			<ath class="text-center td_cgst_amt td_tax_amt td_gst_amt">{[ source.currency.code ]}</ath>
			<ath class="text-center td_sgst_rate td_tax_rate td_gst_rate">%</ath>
			<ath class="text-center td_sgst_amt td_tax_amt td_gst_amt">{[ source.currency.code ]}</ath>
			<ath class="text-center td_igst_rate td_tax_rate td_gst_rate">%</ath>
			<ath class="text-center td_igst_amt td_tax_amt td_gst_amt">{[ source.currency.code ]}</ath>
		</atr>
	</athead>
	<atbody>
		[% for material in po_item_details %]
			[%  if forloop.counter|divisibleby:2 %]
			<atr class="row_seperator column_seperator row_shading" style="background: #efefef;">
			[% else %]
			<atr class="row_seperator column_seperator row_shading" style="background: #ffffff;">
			[% endif %]
				<atd class="text-center td_sno">{[forloop.counter]}</atd>
				<atd>
                    <span class="pdf_item_name">
                        <span class="pdf_item_name_txt"></span>
                        {[ material.material_name ]}<br />
                    </span>
                    [% if material.material_drawing_no %]
                        <span class="pdf_item_drawing_number">
                            <span class="pdf_item_drawing_number_txt"></span>
                            {[ material.material_drawing_no ]}<br />
                        </span>
                    [% endif %]
<!--					[% if item_res.item_table.item_details.make.print and item_res.item_table.item_details.part_no.print %]-->
<!--						[% if material.material_make and material.material_make != "" %]-->
<!--							<span class="pdf_item_make">~lsqb;-->
<!--								<i class="pdf_item_make_txt"></i>{[ material.material_make ]}-->
<!--							</span>-->
<!--							[% if material.part_no and material.part_no != "" %]-->
<!--								- -->
<!--								<span class="pdf_item_part">-->
<!--									<i class="pdf_item_part_txt"></i>-->
<!--								</span>-->
<!--								<span>{[ material.part_no ]} ~rsqb;<br></span>-->
<!--							[% else %]-->
<!--								 ~rsqb;<br>-->
<!--							[% endif %]-->
<!--						[% endif %]-->
<!--					[% elif item_res.item_table.item_details.make.print or item_res.item_table.item_details.part_no.print %]-->
<!--						[% if item_res.item_table.item_details.make.print %]-->
<!--							[% if material.material_make and material.material_make != "" %]-->
<!--								<span class="pdf_item_make">~lsqb;-->
<!--									<i class="pdf_item_make_txt"></i>{[ material.material_make ]} ~rsqb;<br>-->
<!--								</span>-->
<!--							[% endif %]-->
<!--						[% else %]-->
<!--							[% if material.part_no and material.part_no != "" %]-->
<!--								<span class="pdf_item_part">~lsqb;-->
<!--									<i class="pdf_item_part_txt"></i>-->
<!--								</span>-->
<!--								<span>{[ material.part_no ]} ~rsqb;<br></span>-->
<!--							[% endif %]-->
<!--						[% endif %]-->
<!--					[% endif %]-->
                    [% if material.hsn_code %]
					    <span class="pdf_item_hsn_code"><i class="pdf_item_hsn_code_txt"></i> {[ material.hsn_code ]}<br /></span>
                    [% endif %]
                    [% if material.material_description %]
					    <span class="pdf_item_desc"><span class="pdf_item_desc_txt"></span>{[ material.material_description ]}<br></span>
                    [% endif %]
					<span class="tax_in_description hide">~lsqb;CGST @ {[ material.cgst_rate ]}: {[ material.cgst_value ]} - SGST @ {[ material.sgst_rate ]}: {[ material.sgst_value ]} - IGST @ {[ material.igst_rate ]}: {[ material.igst_value ]}~rsqb;</span>
				</atd>
				<atd class="text-center td_hsn_code hide for-non-pp">{[ material.hsn_code ]}</atd>
				<atd class="text-right td_qty">{[ material.material_quantity|floatformat:2 ]}<br /><span class="pdf_unit_in_price hide">({[ material.material_unit ]})</span></atd>
				<atd class="text-center td_uom">{[ material.material_unit ]}</atd>
				<atd class="text-right td_price for-non-pp">{[ material.material_rate|floatformat:2 ]}</atd>
				<atd class="text-right td_disc for-non-pp">{[ material.material_discount ]}</atd>
				<atd class="text-right td_tax for-non-pp">{[ material.material_taxable_value ]}</atd>
				<atd class="text-right td_cgst tax_rate_column td_gst_rate for-non-pp">{[ material.cgst_rate ]}</atd>
				<atd class="text-right td_cgst tax_rate_column td_gst_amt for-non-pp">{[ material.cgst_value ]}</atd>
				<atd class="text-right td_sgst tax_rate_column td_gst_rate for-non-pp">{[ material.sgst_rate ]}</atd>
				<atd class="text-right td_sgst tax_rate_column td_gst_amt for-non-pp">{[ material.sgst_value ]}</atd>
				<atd class="text-right td_igst tax_rate_column td_gst_rate for-non-pp">{[ material.igst_rate ]}</atd>
				<atd class="text-right td_igst tax_rate_column td_gst_amt for-non-pp" style="border-right-color: #ccc;">{[ material.igst_value ]}</atd>
                <atd class="text-center tax_one_column hide for-non-pp"><span class="tax_one_column_amt">{[ material.cgst_value ]}<br></span><span class="tax_one_column_rate"><small>@ {[ material.cgst_rate ]}%</small></span></atd>
                <atd class="text-center tax_one_column hide for-non-pp"><span class="tax_one_column_amt">{[ material.sgst_value ]}<br></span><span class="tax_one_column_rate"><small>@ {[ material.sgst_rate ]}%</small></span></atd>
                <atd class="text-center tax_one_column hide for-non-pp"><span class="tax_one_column_amt">{[ material.igst_value ]}<br></span><span class="tax_one_column_rate"><small>@ {[ material.igst_rate ]}%</small></span></atd>
			</atr>
		[% endfor %]
	</atbody>
	<atfoot>
		<atr class="row_seperator column_seperator total_section sub_total_section for-non-pp">
			<atd colspan="5" class="text-right total_section_1"><b>Sub-Total</b></atd>
			<atd class="total_section_2 hide">{[ total_quantity ]}</atd>
			<atd colspan="2" class="text-right total_section_3">{[ total_value]}</atd>
			<atd class="tax_rate_column td_gst_rate"></atd>
			<atd class="tax_rate_column text-right td_gst_amt">{[ total_cgst_value ]}</atd>
			<atd class="tax_rate_column td_gst_rate"></atd>
			<atd class="tax_rate_column text-right td_gst_amt">{[ total_sgst_value ]}</atd>
			<atd class="tax_rate_column td_gst_rate"></atd>
			<atd class="tax_rate_column text-right td_gst_amt" style="border-right-color: #ccc;">{[ total_igst_value ]}</atd>
            <atd class="text-center tax_one_column hide"><span class="tax_one_column_amt">{[ total_cgst_value ]}</span></atd>
            <atd class="text-center tax_one_column hide"><span class="tax_one_column_amt">{[ total_sgst_value ]}</span></atd>
            <atd class="text-center tax_one_column hide"><span class="tax_one_column_amt">{[ total_igst_value ]}</span></atd>
		</atr>
		[% for tax in po_taxes %]
			<atr style="[% if misc_res.print_summary_first_page and appendix_pages > 0 %]display:none;[% endif %]" class="row_seperator column_seperator other_tax_column for-non-pp">
				<atd colspan="5" class="text-right total_section_1">{[ tax.tax_name ]} @ {[ tax.tax_rate ]}%</atd>
				<atd colspan="2" class="text-right total_section_3">{[ tax.tax_value ]}</atd>
			</atr>
		[% endfor %]
		[% for key, value in tax_summary.cgst_summary.items %]
			<atr style="[% if misc_res.print_summary_first_page and appendix_pages > 0 %]display:none;[% endif %]" class="row_seperator column_seperator consolidated_tax for-non-pp">
				<atd colspan="5" class="text-right total_section_1">CGST @ {[ key|floatformat:2 ]}%</atd>
				<atd colspan="2" class="text-right total_section_3">{[ value|floatformat:2 ]}</atd>
			</atr>
		[% endfor %]
		[% for key, value in tax_summary.sgst_summary.items %]
			<atr style="[% if misc_res.print_summary_first_page and appendix_pages > 0 %]display:none;[% endif %]" class="row_seperator column_seperator consolidated_tax for-non-pp">
				<atd colspan="5" class="text-right total_section_1">SGST @ {[ key|floatformat:2 ]}%</atd>
				<atd colspan="2" class="text-right total_section_3">{[ value|floatformat:2 ]}</atd>
			</atr>
		[% endfor %]
		[% for key, value in tax_summary.igst_summary.items %]
			<atr style="[% if misc_res.print_summary_first_page and appendix_pages > 0 %]display:none;[% endif %]" class="row_seperator column_seperator consolidated_tax for-non-pp">
				<atd colspan="5" class="text-right total_section_1">IGST @ {[ key|floatformat:2 ]}%</atd>
				<atd colspan="2" class="text-right total_section_3">{[ value|floatformat:2 ]}</atd>
			</atr>
		[% endfor %]
		[% if source.round_off != None %]
		<atr class="row_seperator column_seperator total_section for-non-pp">
			<atd colspan="5" class="text-right total_section_1"><b>Round Off</b></atd>
			<atd colspan="2" class="text-right total_section_3">{[ source.round_off ]}</atd>
		</atr>
		[% endif %]
		<atr style="[% if misc_res.print_summary_first_page and appendix_pages > 0 %]display:none;[% endif %]" class="row_seperator column_seperator total_section for-non-pp">
			<atd colspan="5" class="text-right total_section_1"><b>Grand Total</b><span class="total_inclusice_tax hide"> (inclusive tax)</span></atd>
			<atd colspan="2" class="text-right total_section_3">{[ source.currency.code ]} <b>{[ source.total ]}</b></atd>
		</atr>
		<atr style="[% if misc_res.print_summary_first_page and appendix_pages > 0 %]display:none;[% endif %]" class="tr_total_in_words row_seperator show_total_in_words total_in_words for-non-pp">
			<atd colspan="13" class="full-length-td"><b>Total Value ({[ source.currency.code ]}):</b> {[ total_in_words|upper ]}</atd>
		</atr>
	</atfoot>
</atable>