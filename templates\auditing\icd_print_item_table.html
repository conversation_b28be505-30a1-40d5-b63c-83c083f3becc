<style>
	.table.row-seperator th,
	.table.row-seperator td,
	.table.row-seperator th,
	.table.row-seperator td {
	    border-top: 1px solid #666666 !important;
	    border-bottom: 1px solid #666666 !important;
	}

	.table.column-seperator th,
	.table.column-seperator td,
	.table.column-seperator th,
	.table.column-seperator td {
	    border-right: 1px solid #666666 !important;
	    border-left: 1px solid #666666 !important;
	}

	table, th, td {
		border-collapse: collapse;
	}

	.reason{
		text-align:left;
		padding-left:2px;
	}

</style>
<table border=1 bordercolor="#A9A9A9" class="row-seperator column-seperator">
	<tr>
		<th rowspan="2">S.No</th>
		<th rowspan="2">Drawing No<br>Description</th>
		<th rowspan="2" style="width:40px;">Reason</th>
		<th rowspan="2">Quantity</th>
		<th rowspan="2" style="width:40px;">Unit</th>
		<th rowspan="2">Rate</th>
		<th rowspan="2">Amount</th>
		<th colspan="2">CGST</th>
		<th colspan="2" >SGST</th>
		<th colspan="2" >IGST</th>
	</tr>
	<tr>
		<th style="width:40px;">%</th>
		<th style="width:40px;">({{ source.currency }})</th>
		<th style="width:40px;">%</th>
		<th style="width:40px;">({{ source.currency }})</th>
		<th style="width:40px;">%</th>
		<th style="width:40px;">({{ source.currency }})</th>
	 </tr>
	{% for material in note_item_details %}
		<tr class="sub_total" >
			<td>{{forloop.counter}}</td>
			<td style="text-align:left; width: 245px;padding-left:4px;"><i>{% if material.item_code and material.item_code != "" %} {{ material.item_code }} - {% endif %} </i>{% if material.item_description and material.item_description != "" %} {{ material.item_description }} {% endif %} <i> {{ material.make }}</i> {% if material.hsn_code and material.hsn_code != "" %} <br> HSN/ SAC: {{ material.hsn_code }} {% endif %}
			</td>
			<td class="reason">{{ material.reason }}</td>
			<td> {{ material.quantity }}</td>
			<td style="text-align:left; padding-left:3px;">{{ material.unit_name }}</td>
			<td>{{ material.rate }}</td>
			<td>{{ material.amount }}</td>
			<td>{{ material.cgst_rate }}</td>
			<td>{{ material.cgst_value }}</td>
			<td>{{ material.sgst_rate }}</td>
			<td>{{ material.sgst_value }}</td>
			<td>{{ material.igst_rate }}</td>
			<td>{{ material.igst_value }}</td>
		</tr>
	{% endfor %}
	{% if appendix_pages > 0 %}
		<tr class="sub_total">
			<th colspan="2" style="text-align:center;"><b>Total</b></th>
			<th colspan="2"><b>{{ summary_details.total_qty }}</b></th>
			<th colspan="1"></th>
			<th colspan="2"><b>{{ summary_details.total }}</b></th>
			<th colspan="2"><b>{{ summary_details.net_cgst }}</b></th>
			<th colspan="2"><b>{{ summary_details.net_sgst }}</b></th>
			<th colspan="2"><b>{{ summary_details.net_igst }}</b></th>
		</tr>
	{% else %}
		<tr class="sub_total">
			<td colspan="2" style="border-left:hidden;border-bottom:hidden;border-right:hidden;">
			<td colspan="2" style="border-right:hidden;"><b>Sub Total</b></td>
			<td colspan="3" style="border-right:hidden;"><b>{{ summary_details.total }} </b></td>
			<td colspan="2" style="border-right:hidden;">{{ summary_details.net_cgst }}</td>
			<td colspan="2" style="border-right:hidden;">{{ summary_details.net_sgst }}</td>
			<td colspan="2" style="border-right:hidden;">{{ summary_details.net_igst }}</td>
		</tr>
	{% endif %}
	{% for tax in note_taxes %}
		<tr {% if appendix_pages > 0 %} style="display:none;" {% endif %}>
			<td colspan="2" class="total_gst">{{ tax.tax_name }}</td>
			<td colspan="3" class="total_gst">{{ tax.tax_rate }}</td>
			<td class="total_gst" style="text-align:left">%</td>
			<td class="total_gst">{{ tax.tax_value }}</td>
			<td class="total_gst"></td>
		</tr>
	{% endfor %}
	{% if note_taxes %}
		<tr {% if appendix_pages > 0 %} style="display:none;" {% endif %}>
			<td style=":border-top:1px solid #000;"></td>
		</tr>
	{% endif %}
	{% if source.round_off and source.round_off != 0 %}
		<tr>
			<td colspan="2" class="grand_total"></td>
			<td colspan="2" class="grand_total" style="border-bottom:1px;"><b>Round-off</b></td>
			<td class="grand_total" style="border-bottom:1px;">{% if source.currency != None %} {{ source.currency }} {% endif %}</td>
			<td colspan="2" class="grand_total" style="border-bottom:1px;"><b>{{ summary_details.round_off }}</b></td>
			<td colspan="2" class="grand_total" style="border-bottom:1px;"></td>
		</tr>
	{% endif %}
	<tr {% if appendix_pages > 0 %} style="display:none;" {% endif %}>
		<td colspan="4" style="border-bottom:1px;border-left:hidden;" ><b>Total Value</b></td>
		<td style="border-bottom:1px;border-left:hidden;">{% if source.currency != None %} {{ source.currency }} {% endif %}</td>
		<td colspan="2" style="border-bottom:1px;border-right:hidden;border-left:hidden;"><b>{{ summary_details.grand_total }}</b></td>
		<td colspan="6" style="border-right:hidden"></td>
	</tr>
</table>