{% extends "auditing/sidebar.html" %}
{% block expense %}

<style>
	li.expense_menu a{
		background-color:#fff;
		color:#306082 !important;
		text-decoration:none;
	}

	.toggle.btn {
		width: 100% !important;
		margin-top: -8px;
		background: #209be1;
		border-radius: 25px;
		margin-top: 0;
	}

	.toggle .toggle-handle {
		border-radius: 50px;
	    margin-left: -35px;
	    height: 25px;
	    margin-top: 3px;
	}

	.toggle .toggle-handle circle {
	    display: block;
	    margin-left: -9px;
	    margin-top: 5px;
	    font-weight: bold;
	    width: 20px;
	    background: url(/site_media/images/left_arrow.png) no-repeat;
	    height: 14px;
	}

	.toggle.off .toggle-handle circle {
	    background: url(/site_media/images/right_arrow.png) no-repeat;
	}

	.toggle.off .toggle-handle {
	    margin-left: 35px;
	}

	.toggle .btn-primary {
		background: #004195;
    	border-color: transparent;
	}

	.toggle .btn-primary:hover {
		background: #004195;
    	border-color: transparent;
    	opacity: 0.8;
	}

	.toggle .btn-default {
		background: #209be1;
    	border-color: transparent;
    	color: #FFF;
	}

	.toggle .btn-default:hover {
		background: #209be1;
    	border-color: transparent;
    	color: #FFF;
    	opacity: 0.8;
	}

	.toggle .toggle-handle.btn-default {
		background: #FFF;
	}

	.exp_amount input,
	.exp_audit_amount input,
	.exp_approve_amount input,
	.exp_net_amount input {
		text-align: right;
	}

	#expensetable .manual-gly {
		float: left;
	    margin-top: -20px;
	    margin-left: 9px;
	}

	#expensetable .single-datepicker-manual {
		padding-left: 30px;
	}

	.chosen-select-table + .chosen-container {
		position: absolute;
		margin-top: -13px;
		max-width: 200px;
	}

	.chosen-select-table + .chosen-container .chosen-single {
		height: 26px;
    	line-height: 26px;
    	font-size: 12px;
	}

	.chosen-select-table + .chosen-container .chosen-single div b {
		margin-top: 0;
	}

	.expense_btn_container input {
		margin-left: 4px;
	}

</style>

<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-toggle.min.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/expense.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/tags.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-toggle.min.js?v={{ current_version }}"></script>

<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">Expenses</span>
	</div>
	<div>
		<div class="col-lg-12">
			<div class="content_bg">
				<div class="row">
					<div class="col-sm-2 hide" style="position: absolute; z-index: 10; padding-right: 0; margin-top: -4px;">
						<input type="checkbox" id="expense_type" data-toggle="toggle" data-on="My Expenses" data-off="Other Expenses" checked="checked" />
					</div>
					<input type="hidden" id="current_user_id" value="{{user_id}}">
					<div class="col-sm-12 page_header-container hide">
						<h2 style="color: #113344; margin: 8px auto;">
							<span class="page_header hide" style="padding-left: 15px;"></span>
							{% if logged_in_user.is_super %}
							<a class="btn super_user_icon" onclick="EditExpenseNumber();" style="">
								<i class="fa fa-pencil"></i>
							</a>
							<div class="xsid_number_edit hide">
								<form class="form-inline" style="display: inline-block;" action="">
								    <div class="form-group">
								      	<input type="text" class="form-control" id="exp_financial_year" name="exp_financial_year" maxlength="5">
								    </div>
								    <div class="form-group">
									    <input type="text" class="form-control" id="exp_type" name="exp_type" maxlength="3" readonly="true">
								    </div>
								    <div class="form-group">
								      	<input type="text" id="exp_number" name="exp_number" class="form-control" maxlength="6" onfocus="setNumberRangeOnFocus(this,6,0,true)">
								    </div>
								    <div class="form-group">
								      	<input type="text" class="form-control text-lowercase" onkeypress="validateStringOnKeyPress(this,event,'character');" onblur="validateStringOnBlur(this,event,'character');" id="exp_number_division" name="exp_number_division" maxlength="1" >
								    </div>
								    <div class="form-group super_edit_submit_icon">
								    	<span role='button' data-tooltip="tooltip" title="Save" class="super_edit_save" onclick="SaveExpenseNumber();"><i class="fa fa-check"  aria-hidden="true" style="color: green;"></i></span>
								    	<span role='button' data-tooltip="tooltip" title="Cancel" class="super_edit_cancel" onclick="DiscardEditExpenseNumber();"><i class="fa fa-times" aria-hidden="true" style="color: red;"></i></span>
								    </div>
						  		</form>
						  		<div class="save_xsid_error_format hide">Required. Please check help tool for valid format.</div>
						  	</div>  
						  	<div class="super_user_tool_tip hide"><span class="question-mark"></span></div>
							<!--<span class="super_user_tool_tip hide"><img src="/site_media/images/tooltip.png" style="vertical-align: top;" /></span>-->
						{%else%}
							<a class="btn super_user_icon" style="color:#777;" onclick="" style="" data-tooltip="tooltip" data-placement="right" data-title="Only Super User can edit this field">
								<i class="fa fa-pencil"></i>
							</a>
						{% endif %}
							<a onclick="goBackToExpenses()" role="button" class="btn btn-add-new pull-right view_expenses hide" style="margin-right: 10px; margin-top: 2px;" data-tooltip="tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true" ></i></a>
						</h2>
					</div>
				</div>
				
				<input type="hidden" id="save_expense_response" value="{{save_expense_response}}"/>
				<div class="contant-container-nav" style="padding-bottom: 0;">
					<ul class="nav nav-tabs list-inline">
						<li class="active">
							<a data-toggle="tab" id="nav_tab_1" href="#tab1">MY EXPENSES
								{% if logged_in_user|canApprove:'ICD' or logged_in_user|canEdit:'ICD' or logged_in_user|canApprove:'EXPENSES' or logged_in_user|canEdit:'EXPENSES'  %}
								<span class="inline-notification-count" data-badge="-" id="my_expenses_count"></span>
								{% endif %}
							</a>
						</li>
							<li><a data-toggle="tab" href="#tab2" id="nav_tab_2">OTHER EXPENSES
								{% if logged_in_user|canApprove:'ICD' or logged_in_user|canEdit:'ICD' or logged_in_user|canApprove:'EXPENSES' or logged_in_user|canEdit:'EXPENSES'  %}
								<span class="inline-notification-count" data-badge="-" id="other_expenses_count"></span>
							{% endif %}
							</a></li>
						<li><a class="hide" data-toggle="tab" href="#tab3" id="nav_tab_3">ADD</a></li>
					</ul>
				</div>
				<div class="tab-content">
					<div id="tab1" class="tab-pane fade in active">
						<div class="col-lg-12 view_list_table">
							<div class="filter-components">
								<div class="filter-components-container">
									<div class="dropdown">
										<button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
											<i class="fa fa-filter"></i>
										</button>
										<span class="dropdown-menu arrow_box arrow_box_filter">
											<div class="col-sm-12 form-group" >
												<label>Date Range</label>
												<div id="reportrange" class="report-range form-control">
													<i class="glyphicon glyphicon-calendar"></i>&nbsp;
													<span></span> <b class="caret"></b>
													<input type="hidden" class="fromdate" id="etl_fromdate" name="fromdate" value="{{ from_date }}" />
													<input type="hidden" class="batch_fromdate" id="batch_fromdate" name="batch_fromdate" value="{{ from_date }}" />
													<input type="hidden" class="todate" id="etl_todate" name="todate" />
													<input type="hidden" class="status" id="status" name="status" value="{{ status }}" />
													<input type="hidden" class="badge_hit" id="badge_hit" name="badge_hit" value="{{ badge_hit }}" />
												</div>
											</div>
											<div class="filter-footer">
												<button type="submit" class="btn btn-save" id="expense_list">Apply</button>
					      					</div>
										</span>
									</div>
									<span class='filtered-condition filtered-date'>Date: <b></b></span>
								</div>
							</div>
							<div class="row">
								<div style="position: absolute; right: 30px; margin-top: -110px;">
									{% if logged_in_user|canEdit:'EXPENSES' %}
										<a data-toggle="tab" href="#tab3" id="a-create-my-exp" class="btn btn-new-item pull-right create_expenses" data-tooltip="tooltip" title="Create New Expenses">
											<i class="fa fa-plus" aria-hidden="true"></i>
											<span class="btn-new-item-label"></span>
										</a>
									{% else %}
										<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in Audit Module. Please contact the administrator.">
											<a role="button" id="a-create-my-exp" class="btn btn-new-item pull-right create_expenses disabled">
												<i class="fa fa-plus" aria-hidden="true"></i>
												<span class="btn-new-item-label"></span>
											</a>
										</div>
									{% endif %}
								</div>
								<div class="col-sm-12">
									<div class="csv_export_button">
										<a role="button" id="a-download-my-exp" class="btn btn-add-new pull-right export_csv my_exp_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#expense_table_list'), 'My_Expense.csv']);" data-tooltip="tooltip" title="Download My Expenses as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
									</div>
									<table class="table table-bordered custom-table table-striped" id="expense_table_list" style="width: 100%;">
										<thead>
										<tr>
											<th> S. No</th>
											<th style="min-width: 200px; max-height: 200px;"> Expense No</th>
											<th> Created On</th>
											<th> Claim Head</th>
											<th> Group Description</th>
											<th> Amount</th>
											<th> Status</th>
										</tr>
										</thead>
										<tbody id="expense_table_list_tbody">

										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
					<div id="tab2" class="tab-pane fade">
						<div class="view_table add_table">
							<div class="col-lg-12 view_list_table">
								<div class="row">
									<div class="filter-components">
										<div class="filter-components-container">
											<div class="dropdown">
												<button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
													<i class="fa fa-filter"></i>
												</button>
												<span class="dropdown-menu arrow_box arrow_box_filter">
													<div class="col-sm-12 form-group" >
														<label>Date Range</label>
														<div id="etlo_reportrange" class="report-range form-control">
															<i class="glyphicon glyphicon-calendar"></i>&nbsp;
															<span></span> <b class="caret"></b>
															<input type="hidden" class="fromdate" id="etlo_fromdate" name="fromdate" value="{{ from_date }}" />
															<input type="hidden" class="todate" id="etlo_todate" name="todate" />
														</div>
													</div>
													<div class="filter-footer">
														<button type="submit" class="btn btn-save" id="expense_approved_list">Apply</button>
							      					</div>
												</span>
											</div>
											<span class='filtered-condition filtered-date'>Date: <b></b></span>
										</div>
									</div>
									<div class="col-sm-12">
										<div class="csv_export_button">
											<a role="button" id="a-download-other-exp" class="btn btn-add-new pull-right export_csv others_exp_csv hide" onclick="GeneralExportTableToCSV.apply(this, [$('#expense_table_list_others'), 'Others_Expense.csv']);" data-tooltip="tooltip" title="Download Other Expenses as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
										</div>
										<table class="table table-bordered custom-table table-striped" id="expense_table_list_others" style="width: 100%;">
											<thead>
											<tr>
												<th> S. No</th>
												<th style="min-width: 200px; max-height: 200px;"> Expense No</th>
												<th> Confirmed On</th>
												<th> Claim Head</th>
												<th> Group Description</th>
												<th> Claimed By</th>
												<th> Claimed Amount</th>
												<th> Approved Amount</th>
												<th> Status</th>
											</tr>
											</thead>
											<tbody id="expense_table_list_tbody_others">

											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>
					</div>


					<div id="tab3" class="tab-pane fade">
						<form id="save_expense" method="post" action="/erp/expenses/save/#tab3"> {% csrf_token %}
							<div class="add_table">
								<div class="col-lg-12 add_table_content">
									<div style="display:none;">
										{{ exp_form.id }}
										{{ exp_form.enterprise_id }}
										{{ exp_form.financial_year }}
										{{ exp_form.created_by }}
										{{ exp_form.created_on }}
										{{ exp_form.confirmed_on }}
										{{ exp_form.approved_by }}
										{{ exp_form.approved_on }}
										{{ exp_form.checked_by }}
										{{ exp_form.checked_on }}
										{{ exp_form.verified_by }}
										{{ exp_form.verified_on }}
										{{ exp_form.status }}
									</div>
									<div class="row">
										<div class="col-sm-7">
											<div class="form-group hide" id="tags"><!-- Tags are hidden on the 2.16.3 release -->
												<label>Tags</label>
												<ul id="ul-tagit-display" class="tagit-display form-control">
													{% for tag_form in tags_formset.initial_forms %}
														<li class="li-tagit-display" id="{{ tag_form.prefix }}">
															<div hidden="hidden">{{ tag_form.tag }}
																{{ tag_form.DELETE }}{{ tag_form.ORDER }}</div>
															<label class="label-tagit-display"  id="id_{{tag_form.prefix}}-tag_label">{{ tag_form.tag.value }}</label>&nbsp;
															<a class="delete_tag" id="{{tag_form.prefix}}-del_button"></a>
															&nbsp;
														</li>
													{% endfor %}
													<li id="tag-__dummy__" hidden="hidden">
														<div hidden="hidden">{{ tags_formset.empty_form.tag }}
														{{ tags_formset.empty_form.ORDER }}
														{{ tags_formset.empty_form.DELETE }}</div>
														<label class="label-tagit-display"  id="id_{{tags_formset.empty_form.prefix}}-tag_label"></label>&nbsp;
														<a class="delete_tag" id="{{tags_formset.empty_form.prefix}}-del_button"></a>
														&nbsp;
													</li>

													<span>
														{{ tags_formset.management_form }}
														<span id="{{ tags_formset.empty_form.prefix }}" class="text_for_tag" >
															{{ tags_formset.empty_form.tag }}
															{{ tags_formset.empty_form.ORDER }}
															<span hidden="hidden">{{ tags_formset.empty_form.DELETE }}</span>
														</span>
													</span>
												</ul>
												<span style="position: absolute; margin-top: -8px; color: #aaa; font-size: 11px;">Type and press enter to add new tag.</span>
											</div>
											<div class="col-sm-2 hide" style="margin-top: 21px;">
												<input id="add_tag" class="btn btn-save btn-margin-1" value="+" type="button">
											</div>
											<div class="col-sm-6" style="padding-left: 0;">
												<label>Claim Head<span class="mandatory_mark"> *</span></label>
												<div>
													{% if exp_form.status.value == 0 or exp_form.status.value == 1 and exp_form.created_by.value == user_id %}
														{{exp_form.claim_head_ledger_id}}
														<div style="display:none;">{{exp_form.claim_head_ledger_name }}</div>
													{% else %}
														{% if logged_in_user.is_super %}
															<a class="super_edit_field" onclick="SuperEditExpSelect(this);" data-tooltip="tooltip" data-placement="left" title="Super Edit" style="margin-top: 1px;">
																<i class="fa fa-pencil super_edit_in_field"></i>
															</a>
															<div class="div-disabled">{{ exp_form.claim_head_ledger_id }}</div>
															
														{% else %}
															<a class="super_edit_field" onclick="" data-tooltip="tooltip" data-placement="left" title="Only Super User can edit this field " style="margin-top: 1px;">
																<i class="fa fa-pencil super_edit_in_field" style="color:#777;" ></i>
															</a>
															{{ exp_form.claim_head_ledger_name }}
															<div style="display:none;">{{ exp_form.claim_head_ledger_id }}</div>
														{% endif %}	
													{% endif %}
												</div>
											</div>
											<div class="col-sm-6" style="padding-right: 0">
												<label>Group Description<span class="mandatory_mark"> *</span></label>
												{% if exp_form.status.value == 0 or exp_form.status.value == 1 and exp_form.created_by.value == user_id %}
													{{ exp_form.group_description }}
												{% else %}
													{% if logged_in_user.is_super %}
													<a class="super_edit_field" onclick="SuperEditExpInput(this);" data-tooltip="tooltip" data-placement="left" title="Super Edit" style="margin-top: 19px;">
														<i class="fa fa-pencil super_edit_in_field"></i>
													</a>
													{%else%}
													<a class="super_edit_field" onclick="" data-tooltip="tooltip" data-placement="left" title="Only Super User can edit this field" style="margin-top: 19px;">
														<i class="fa fa-pencil super_edit_in_field" style="color:#777;"></i>
													</a>
													{% endif %}	
													<!-- Disabled fields are not read in POST, hence the hidden field -->
													<input type="text" value="{{ exp_form.group_description.value }}"
												       class="form-control" id="id_exp-group_description"
												       disabled placeholder="Group Description" name="exp-group_description"  maxlength="150"
												       onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')">
												{% endif %}
											</div>
										</div>
										<div class="col-sm-5">
											{% if exp_form.id.value != '' and exp_form.id.value != None %}
												<script type="text/javascript">
													$(".page_header").html('<span class="header_current_page"> {{ exp_form.code.value }}</span>').removeClass('hide');
													$(".page-title").text("Expenses");
												</script>
												<div class="form-group" style="margin-top: 17px; margin-bottom: 0">
													<table border="0" class="side-table">
														<tr class="hide">
															<td class="side-header" style="width: 150px;">Expenses No</td>
															<td class="side-content">{{ exp_form.code.value }}</td>
														</tr>
														{% if exp_form.confirmed_on.value != None and exp_form.confirmed_on.value != "" %}
														<tr>
															<td class="side-header" style="width: 150px;">Expense Date & Time</td>
															<td class="side-content">{{exp_form.confirmed_on.value|date:'M d, Y H:i:s'}}</td>
														</tr>
														{% else %}{% if exp_form.created_on.value != None and exp_form.created_on.value != "" %}
														<tr>
															<td class="side-header" style="width: 150px;">Draft updated on</td>
															<td class="side-content" id="exp_id_visible">{{exp_form.created_on.value|date:'M d, Y H:i:s'}}</td>
														</tr>
														{% endif %}
														{% endif %}
														<tr>
															<td class="side-header" style="width: 150px;">Claimed By</td>
															<td class="side-content" id="exp_id_visible">{{ exp_form.claimed_user.value }}</td>
														</tr>
														{% if exp_form.status_code.value != '' and exp_form.status_code.value != None %}
														<tr>
															<td class="side-header" style="width: 150px;">Status</td>
															<td class="side-content" id="exp_id_visible">{{ exp_form.status_code.value }}</td>
														</tr>
														{% endif %}
													</table>
												</div>
											{% else %}
												<script type="text/javascript">
													var url = window.location.href;
													if(url.indexOf("tab3") >= 0) {
														$(".page-title").text("New Expenses");
													}
												</script>	
											{% endif %}
										</div>
										<div class="clearfix"></div>
									</div>
									<div class="row">
										
									</div>
									<div class="add_expenses">
										{% if logged_in_user.is_super or exp_form.status.value == 0 or exp_form.status.value == 1 and exp_form.created_by.value == user_id %}
										<div class="row form-group">
											<div class="col-sm-12"><hr />
												<h3 style="padding: 0;">Add Expenses</h3>
											</div>
											<div class="col-sm-2">
												<label>Spent On<span class="mandatory_mark"> *</span></label>
												<input type="text" id="exp_item_spent_on" class="form-control hide" />
												<input type="text" class="form-control custom_datepicker till-today" placeholder="Select Date" id="voucher_date" readonly="readonly">
												<i class="glyphicon glyphicon-calendar custom-calender-icon"></i>
											</div>

											<div class="col-sm-4">
												<label>Description<span class="mandatory_mark"> *</span></label>
												{{ exp_item_formset.empty_form.description }}
											</div>
											<div class="col-sm-3">
												<label>Expense Head<span class="mandatory_mark"> *</span></label>
												{{exp_form.expense_head_ledger}}
											</div>
											<div class="col-sm-2">
												<label>Amount<span class="mandatory_mark"> *</span></label>
												<input type="text" id="id_exp_item-__prefix__-amount" name="exp_item-__prefix__-amount" class="form-control" placeholder="0.00" value="0.00" maxlength="16" onfocus="setNumberRangeOnFocus(this,13,2)" data-enterkey-submit="cmdadd">
											</div>
										</div>
										<div class="row">
											<div class="col-sm-2 form-group checkbox" style="margin-top: 30px;" >
												<input type="checkbox" name="case" id="id_exp_item-__prefix__-bill_available" value="" />
												<label for="id_exp_item-__prefix__-bill_available">Bill Available</label>
											</div>
											<div class="col-sm-4">
												<label>Bill Copy</label>
												<div class="material_txt">
													<input type="file" id="bill_copy_uploader" name="bill_copy_uploader" accept=".pdf, .jpeg, .jpg, .png" class="filestyle" data-buttonBefore="true" data-buttonText= "Attachment" onchange="convertToBase64();">
													<input type="button" name="upload" id="bill_upload" hidden="hidden" value="Upload" />
													<input type="hidden" id="id_exp_item-__prefix__-document_data"
													       class="modal-part bill-copy"/>
												</div>
											</div>
											<div class="col-sm-1">
												<input type="button" id="cmdadd" class="btn btn-save btn-margin-1" value="+"/>
											</div>
										</div>
										{% endif %}
										<div class="row">
											<div class="col-lg-12">
												<h3 style="padding: 0;">Expense Details</h3>
												<div>
													{{ exp_item_formset.management_form }}
													<table class="table table-bordered table-striped custom-table vertical-center tableWithText" id="expensetable">
														<thead>
														<tr>
															<th rowspan="2" style="max-width:60px; width: 60px;">S.No</th>
															<th rowspan="2" style="max-width:140px; width: 140px;">Date</th>
															<th rowspan="2">Description</th>
															<th rowspan="2" style="width: 220px; min-width: 220px; max-width: 220px;">Expense HEAD</th>
															<th rowspan="2">Amount</th>
															{% if access_level.approve or logged_in_user|canEdit:'ICD' or logged_in_user|canApprove:'ICD' %}
															{% if exp_form.status.value > 1 or exp_form.status.value == 1 and exp_form.created_by.value != user_id %}
																<th colspan {% if  module_access.icd %}="2" {% else %} ="1" {% endif%} class="exp_debit ">Debit Amount</th>
																<th rowspan="2">Remark</th>
															{% endif %}
															{% endif %}
															{% if exp_form.status.value > 1 or exp_form.status.value == 1 and exp_form.created_by.value != user_id %}
																<th rowspan="2" class="exp_net">Net Amount</th>
															{% endif %}
															<th rowspan="2">Bill</th>
															<th rowspan="2">Bill Copy</th>
															<th rowspan="2">Action</th>
															{% if logged_in_user|canEdit:'ICD' and exp_form.status.value == 2 %}
															<th rowspan="2" style="max-width:70px; width: 70px;"></th>
															{% endif %}
														</tr>
														{% if access_level.approve or logged_in_user|canEdit:'ICD' or logged_in_user|canApprove:'ICD' %}
															{% if exp_form.status.value > 1 or exp_form.status.value == 1 and exp_form.created_by.value != user_id %}
															<tr>
																<th style="max-width:120px; width: 120px;" class="exp_debit exp_approver ">Approver</th>
																{% if  module_access.icd %}
																<th style="max-width:120px; width: 120px;" class="exp_debit exp_audit ">Audit</th>
																{% endif %}
															</tr>
															{% endif %}
														{% endif %}
														</thead>
														<tbody>
														<tr bgcolor="#ececec" align="center" id="exp_item-__dummy__"
														    hidden="hidden">
															<td hidden="hidden">
																{{ exp_item_formset.empty_form.expense_id }}
																{{ exp_item_formset.empty_form.enterprise_id }}
																{{ exp_item_formset.empty_form.DELETE }}
																{{ exp_item_formset.empty_form.item_no}}
																{{ exp_item_formset.empty_form.expense_head_ledger_id }}
																{{ exp_item_formset.empty_form.approver_debit }}
																{{ exp_item_formset.empty_form.audit_debit }}
																{{ exp_item_formset.empty_form.document }}
																{{ exp_item_formset.empty_form.document_data }}
															</td>
															<td>
																<div class="td_serial_no" id="id_exp_item-__prefix__-s_no"></div>
															</td>
															<td class="text-center">
																{{ exp_item_formset.empty_form.spent_on }}
																<input type='text' class='form-control single-datepicker-manual till-today' />
																<i class='manual-gly glyphicon glyphicon-calendar'></i>
															</td>
															<td class='text-left exp_description'>
																<div>{{ exp_item_formset.empty_form.description }}</div>
															</td>
															<td class='text-left'>
																<div>{{ exp_item_formset.empty_form.expense_head_ledger_name }}</div>
															</td>
															<td class="exp_amount">
																<div>{{ exp_item_formset.empty_form.amount }}</div>
															</td>
															{% if exp_form.status.value > 1 or exp_form.status.value == 1 and exp_form.created_by.value != user_id %}
															<td class="td_textbox_right">
																<div>{{ exp_item_formset.empty_form.approver_debit }}</div>
															</td>
															<td class="td_textbox_right">
																<div>{{ exp_item_formset.empty_form.audit_debit }}</div>
															</td>
															<td></td>
															{% endif %}
															{% if exp_form.status.value > 1 or exp_form.status.value == 1 and exp_form.created_by.value != user_id %}
															<td class="td_textbox_right exp_net_amount">
																<div>{{ exp_item_formset.empty_form.net_amount }}</div>
															</td>
															{% endif %}
															<td><div>{{ exp_item_formset.empty_form.bill_available}}</div></td>
															<td class="exp_document" style="padding: 2px;">
																<span id="id_exp_item-__prefix__-bill_copy" class="document_attachment base64 linked_text" onclick="viewAttachedDocument(this)"></span>
																<!--<span id="id_exp_item-__prefix__-bill_copy"
																      class='modal-part bill-copy' data-base64=""
																      data-filename=""></span>-->
															</td>
															<td><a role="button"
															       id="id_{{ exp_item_formset.empty_form.prefix }}-deleteExpenseItem"
															       onclick="javascript:deleteExpenseItem('{{ exp_item_formset.empty_form.prefix }}')">
																<i class="fa fa-trash-o"></i></a>
															</td>
															{% if exp_form.status.value == 2 %}
															<td></td>
															{% endif %}
														</tr>
														<input type="hidden" id="attachment_json_data" name="attachment_json_data" value="{{ attachment }}">
														{% for exp_item_form in exp_item_formset.initial_forms %}

														<tr align="center" bgcolor="#ececec" id="{{ exp_item_form.prefix }}">
															<td hidden="hidden">
																{{ exp_item_form.expense_id }}
																{{ exp_item_form.enterprise_id }}
																{{ exp_item_form.item_no }}
																{{ exp_item_form.spent_on }}
																{{ exp_item_form.expense_head_ledger_id }}
																{{ exp_item_form.DELETE }}
																{{ exp_item_form.document }}
																{{ exp_item_form.document_data }}
															</td>
															<td><div class="td_serial_no" id="id_{{ exp_item_form.prefix }}-s_no">
																{{ forloop.counter }}
															</div></td>
															<td class='text-center'>
																{% if logged_in_user.is_super %}
																	{{ exp_item_form.spent_on }}
																	<input type="text" class="form-control single-datepicker-manual till-today">
																	<i class="manual-gly glyphicon glyphicon-calendar"></i>
																{% else %}
																	{{ exp_item_form.spent_on.value }}
																{% endif %}

															</td>
															<td class='text-left exp_description'>
																{% if logged_in_user.is_super or exp_form.status.value == 0 or exp_form.status.value == 1 and exp_form.created_by.value == user_id %}
																	<div>{{ exp_item_form.description }}</div>
																{% else %}
																	<div style="display:none;">{{ exp_item_form.description }}</div>
																	<div>{{ exp_item_form.description.value }}</div>
																{% endif %}
															</td>
															<td class='text-left expense_head'>
																<div>
																	{% if logged_in_user.is_super %}
																		<select id="id_{{ exp_item_form.prefix }}-expense_head_ledgers" class="form-control chosen-select-table"></select>
																	{% else %}
																		{{ exp_item_form.expense_head_ledger_name }}
																	{% endif %}
																</div>
															</td>
															<td align="right" class="exp_amount">
																<div>
																	{% if logged_in_user.is_super or exp_form.status.value == 0 or exp_form.status.value == 1 and exp_form.created_by.value == user_id %}
																		{{ exp_item_form.amount }}
																	{% else %}
																		<div style="display:none;">{{ exp_item_form.amount }}</div>
																		{{ exp_item_form.amount.value }}
																	{% endif %}
																</div>
															</td>
															{% if access_level.approve or logged_in_user|canEdit:'ICD' or logged_in_user|canApprove:'ICD' %}
															{% if exp_form.status.value > 1 or exp_form.status.value == 1 and exp_form.created_by.value != user_id %}
																<td align="center" class="exp_approve_amount">
																	<div>
																		{% if logged_in_user.is_super or exp_form.status.value == 1 %}
																			{{ exp_item_form.approver_debit }}
																		{% else %}
																			<div style="display:none;">{{ exp_item_form.approver_debit }}</div>
																			{{ exp_item_form.approver_debit.value }}
																		{% endif %}
																	</div>
																</td>
																{% if  module_access.icd %}
																<td align="right" class="exp_audit_amount">
																	<div>
																		{% if logged_in_user.is_super and exp_form.status.value > 1 or exp_form.status.value == 2 or exp_form.status.value == 3 %}
																			{% if logged_in_user.is_super and exp_form.status.value > 1 or logged_in_user|canEdit:'ICD' or logged_in_user|canApprove:'ICD' %}
																				{{ exp_item_form.audit_debit }}
																			{% else %}
																				<div style="display:none;">{{ exp_item_form.audit_debit }}</div>
																				{% if exp_form.status.value > 2 %}
																				{{ exp_item_form.audit_debit.value }}
																				{%endif%}
																		{% endif %}
																		{% else %}
																			<div style="display:none;">{{ exp_item_form.audit_debit }}</div>
																			{% if exp_form.status.value > 2 %}
																			{{ exp_item_form.audit_debit.value }}
																			{%endif%}
																		{% endif %}
																	</div>
																</td>
															    {% endif %}
																<td>
																	{% if exp_item_form.remarks_list.value != "" %}
																		<div class='text-left'>{{ exp_item_form.remarks_list.value }}</div>
																	{% endif %}
																	{% if logged_in_user.is_super or exp_form.status.value < 4 %}
																		{% if logged_in_user|canEdit:'ICD' or logged_in_user|canApprove:'ICD' %}
																			<div>{{ exp_item_form.remarks }}</div>
																		{% endif %}
																	{% endif %}
																</td>
															{% endif %}
															{% endif %}
															{% if exp_form.status.value > 1 or exp_form.status.value == 1 and exp_form.created_by.value != user_id %}
																<td align="right"  class="exp_net_amount">
																	<div>{{ exp_item_form.net_amount }}</div>
																</td>
															{% endif %}
															<td align="center">
																<div>{{ exp_item_form.bill_available }}</div>
															</td>
															<td style="padding: 2px;">
																{% if exp_item_form.document.value != None and exp_item_form.document.value != "" %}
																<span class="document_attachment linked_text base64-file-value base64" onclick="viewAttachedDocument(this)" data-extension="" data-count="{{ forloop.counter0 }}" data-url="{{ exp_item_form.document.value }}"></span>
																<!--<span class='modal-part bill-copy png' data-base64=""
																      data-filename="{{ exp_item_form.document.value }}"></span>-->

																{% endif %}
															</td>
															<td>
															{% if logged_in_user.is_super or exp_form.status.value == 0 or exp_form.status.value == 1 and exp_form.created_by.value == user_id %}
																<a role="button"
															       id="id_{{ exp_item_form.prefix }}-delete_expense_item"
															       onclick="javascript:deleteExpenseItem('{{ exp_item_form.prefix }}')">
																<i class="fa fa-trash-o"></i></a>
															{% endif %}
															</td>
															{% if exp_form.status.value == 2 %}
															<td class="text-center">
																<span class="checkbox checkbox-border pull-right" style="margin-top: 0px;">
																	<input  name="is_checked"  type="checkbox" class="multi_check" id="{{ exp_item_form.prefix }}_is_checked">
																	<label for="{{ exp_item_form.prefix }}_is_checked"></label>
																</span>
															</td>
															{% endif %}
														</tr>
														{% endfor %}
														<tr>
															<td colspan="4" class="text-right">
																<span class="grand-total-text">Claimed Amount</span>
															</td>
															<td>
																<input type="text" value="{{ claimed_amount }}" class="grand-total-amount" id="id_exp-claimed_amount" maxlength="15" Disabled>
															</td>
															{% if exp_form.status.value > 1 or exp_form.status.value == 1 and exp_form.created_by.value != user_id %}
															<td colspan="3" class="text-right">
																<span class="grand-total-text">Approved Amount</span>
															</td>
															<td>
																<input type="text" value="{{ approved_amount }}" class="grand-total-amount" id="id_exp-approved_amount" maxlength="15" Disabled>
															</td>
															{% endif %}
															<td colspan="4">
															</td>
														</tr>
														</tbody>
													</table>
												</div>
											</div>
										</div>
									</div>
									<div class="clearfix"></div>
									<div class="row">
										<div class="col-sm-4 form-group">
											<label>Remarks</label>
										    <div class="remarks_count_link remarks_count disabled" onclick="remarks_history();">
												<span class="remarks_counter">No</span><span> remarks</span>
											</div>
											{{ exp_form.remarks }}
										</div>
									</div>
								</div>
							</div>
							<div class="material_txt expense_btn_container col-sm-12 text-right" style="margin-top: -52px; float: right; width: 50%;">
								{% if access_level.edit or access_level.approve or logged_in_user|canEdit:'ICD' or logged_in_user|canApprove:'ICD' %}
									{% if exp_form.status.value == 0 or exp_form.status.value == 1 and exp_form.created_by.value == user_id %}
										{% if exp_form.status.value == 0 %}
										<input type="submit" class="btn btn-save" value="Save" id="cmd_exp_save" />
										{% endif %}
										<input type="submit" class="btn btn-save pull-right" value="Confirm" id="cmd_exp_confirm" />
										<input type="hidden" id="id_exp-status_to_be_changed" value="0" class="form-control text-left" name="exp-status_to_be_changed">
									{% else %}
										{% if logged_in_user.is_super %}
											{% if exp_form.status.value == 4 %}
											<input type="hidden" id="id_exp-status_to_be_changed" value="4" class="form-control text-left" name="exp-status_to_be_changed">
											{% endif %}
											<input type="hidden" id="id_exp-super_edit" value="1" class="form-control text-left" name="exp-super_edit">
											<input type="button" class="btn btn-save" value="Update" id="cmd_exp_update" />
											<input type="submit" class="btn btn-save hide" value="Update" id="cmd_exp_save" />
										{% endif %}
										{% if access_level.approve and exp_form.status.value == 1 %}
											<input type="submit" class="btn btn-save pull-right submit-expense" id="cmd_exp_approve" value="Approve"/>
											<input type="hidden" id="id_exp-status_to_be_changed" value="2" class="form-control text-left" name="exp-status_to_be_changed">
										{% else %}
											{% if logged_in_user|canEdit:'ICD' and exp_form.status.value == 2 %}
												<input type="submit" class="btn btn-save pull-right submit-expense" id="cmd_checked_ok" value="Checked Ok" disabled="true"/>
												<input type="hidden" id="id_exp-status_to_be_changed" value="3" class="form-control text-left" name="exp-status_to_be_changed">
											{% else %}
												{% if logged_in_user|canApprove:'ICD' and exp_form.status.value == 3 %}
												<input type="submit" class="btn btn-save pull-right submit-expense" id="cmd_exp_verify" value="Verify"/>
												<input type="hidden" id="id_exp-status_to_be_changed" value="4" class="form-control text-left" name="exp-status_to_be_changed">
												{% endif %}
											{% endif %}
										{% endif %}
									{% endif %}
								{% endif %}
								<!--<input type="button" class="btn btn-cancel" value="Back" id="cmdcancel"/>-->
							</div>
						</form>
						<form action='#'  id="gcs_upload_expense" name="gcs_upload_expense" method="POST" enctype='multipart/form-data'>
				        </form>
						<input type="hidden" class="audit_expense_attachment_upload_json" name="audit_expense_attachment_upload_json" value="">
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

{% include "attachment_popup.html" %}

<br clear="all"/>
<div id="error_messages" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Expense Validation</h4>
			</div>
			<div class="modal-body">
				<table class="error_display" cellpadding="1"
				       cellspacing="1" border="0">
					<tr>
						<td style="align:center">
							<div>
								{% if exp_form.errors %}
								<h4>Expense Header:</h4>
								{% endif %}
								{% for field in exp_form.visible_fields %}
								{% for error in field.errors %}
								<li style="list-style-type: none;">{{field.label_tag}} :{{error}}</li>
								{% endfor %}
								{% endfor %}
							</div>
							<div>
								{% if exp_item_formset.errors %}
								<h4>Expense Particulars:</h4>
								{% endif %}
								{% for exp_particular_form in exp_item_formset.forms %}
								{% if exp_particular_form.errors %}
								<b>{{ exp_particular_form.item_no.value }}</b>:
								{% endif %}
								{% for field in exp_particular_form.visible_fields %}
								{% if field.errors%}
								<br><i>{{field.label_tag}}</i>:
								{% endif %}
								<ul>
									{% for error in field.errors %}
									<li style="list-style-type: none;">{{error}}</li>
									{% endfor %}
								</ul>
								{% endfor %}
								{% endfor %}
							</div>
						</td>
					</tr>
					<tr>
						<td>
							<input type="text" value='{{ exp_form.errors }}' id="form_errors" hidden="hidden"/>
							<input type="text" value='{{ exp_item_formset.errors }}' id="formset_errors" hidden="hidden"/>
						</td>
					</tr>
				</table>
			</div>
		</div>
	</div>
</div>

<div class="hide">
	<form id="id-edit_expense_form" method="post" action="/erp/expenses/home/<USER>">
		{% csrf_token %}
		<input type="hidden" id="id-edit_expense_id" name="expense_id" value="">
	</form>
</div>

<script>
$(function(){
  var hash = window.location.hash;
  hash && $('ul.nav a[href="' + hash + '"]').tab('show');

  $('.nav-tabs a').click(function (e) {
    $(this).tab('show');
    var scrollmem = $('body').scrollTop() || $('html').scrollTop();
    window.location.hash = this.hash;
    $('html,body').scrollTop(scrollmem);
  });
});

$(window).load(function(){
	updateFilterText();
});

function updateFilterText() {
	$(".filtered-date b").text($("#reportrange").find("span").text());
}

$(document).ready(function(){
	var url=window.location.href;
	url = url.substring(7)
	window.urlpath = url.substring(0,url.indexOf('/'));
	if($(".header_current_page").text() == ""  || $(".header_current_page").text().indexOf("New") >=0 || $(".header_current_page").text().indexOf("DRAFT") >=0) {
		$(".super_user_icon").addClass('hide');
	}
	else{
		$(".super_user_icon").removeClass('hide');
	}
	if ($("#save_expense_response").val() != "") {
        swal({
        	title: $("#save_expense_response").val()
        });
    }
    {% if logged_in_user.is_super %}
		$(".expense_head select").each(function() {
			$(this).html($("#id_exp-expense_head_ledger").html());
			var expense_head_ledger_id = "id_" + $(this).closest('tr').attr('id') + '-expense_head_ledger_id';
			$(this).val($("#" + expense_head_ledger_id).val()).trigger("chosen:updated");
			$(this).change(function() {
				$("#" + expense_head_ledger_id).val($(this).val());
			});
		});

	{% endif %}
	$(".chosen-select, .chosen-select-table").chosen();

	setTimeout(function () {
	    if(window.location.href.indexOf('tab2') >= 0){
			$("#expense_approved_list").trigger('click');
		}
		else if(window.location.href.indexOf('tab1') >= 0 || window.location.href.indexOf("tab3") < 0) {
			$("#expense_list").trigger('click');
		}

	}, 1);

	setTimeout(function(){
		calculateNetAmount();
		ExpensesVerifiedClick();
		ExpensesVerifiedCheck();
		enterKeySubmitEvent();
		load_expense_badge_count();
	},100);

	$(".document_attachment").each(function() {
        var self = $(this);
        filename = self.attr('data-url');
        counter = self.attr('data-count');
        attachment = JSON.parse($('#attachment_json_data').val());
        if (filename != null) {
            filename = attachment[counter]['name'];
            var extension = attachment[counter]['ext'];
            self.addClass(extension);
            self.attr('data-extension', extension);
            self.attr('data-url', attachment[counter]['data']);
        }
    });
	$(".toggle-handle").append("<circle></circle>");
	setPartialDatePicker();
	if ($("#remarks_list_json") != null) {
		displayRemarksHistory('remarks_list', $("#remarks_list_json").val(), "remarks_count")
	}
	if($("title").text() == "Expense") {
		$("title").text("Expenses")
	}
});

function load_expense_badge_count(){
 $.ajax({
	        url: '/erp/commons/json/get_badge_count/',
	        type: "POST",
	        dataType: "json",
	        data: {"status":"pending"},
	        success: function (data) {
		        if (data.response_message =="Success") {
			        $('#my_expenses_count').attr("data-badge", ((parseInt(data.result.myexpense) != 0 ? parseInt(data.result.myexpense) : '-')));
			        $('#other_expenses_count').attr("data-badge", ((parseInt(data.result.otherexpense) != 0 ? parseInt(data.result.otherexpense) : '-')));
		        }
	        },
	        error: function () {
	            SingleDatePickerInit();
	        }
	});
	 $.ajax({
	        url: '/erp/commons/json/get_initial_date/',
	        type: "POST",
	        dataType: "json",
	        data: { list: ['voucher', 'icd', 'expense', 'credit_debit', 'po', 'grn'], status:"pending" },
	        success: function (data) {
		        if (data.response_message =="Success") {
			        $('#batch_fromdate').val(data.result.expense);
		        }
	        },
	        error: function () {
	            SingleDatePickerInit();
	        }
	 });
}

function validateExpense(){
	$(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();

    var ControlCollections = [
        {
            controltype: 'dropdown',
            controlid: 'id_exp-claim_head_ledger',
            isrequired: true,
            errormsg: 'Claim Head is required.'
        },
        {
            controltype: 'textbox',
            controlid: 'id_exp-group_description',
            isrequired: true,
            errormsg: 'Expense Group is required.'
        }
    ];
    var result = JSCustomValidator.JSvalidate(ControlCollections);
	return result;
}

var oTable;
var oSettings;
function TableHeaderFixed(){
	oTable = $('#expense_table_list').DataTable({
		fixedHeader: false,
        "scrollY": Number($(document).height() - 230),
        "scrollX": true,
		"pageLength": 50,
		"search": {
			"smart": false
		},
		"columns": [
			null,null,					
			{ "type": "date" },
			null,null,null,null
			]
	});
	oTable.on("draw",function() {
		var keyword = $('#expense_table_list_filter > label:eq(0) > input').val();
		$('#expense_table_list').unmark();
		$('#expense_table_list').mark(keyword,{});
		setHeightForTable();
		listTableHoverIconsInit('expense_table_list');
	});
	oTable.on('page.dt', function() {
	  $('html, body').animate({
		scrollTop: $(".dataTables_wrapper").offset().top - 15
	   }, 'slow');
	   listTableHoverIconsInit('expense_table_list');
	});
	oSettings = oTable.settings();
	listTableHoverIconsInit('expense_table_list');
	$( window ).resize();
}

function TableHeaderFixedOthers(){
	oTable = $('#expense_table_list_others').DataTable({
		fixedHeader: false,
        "scrollY": Number($(document).height() - 230),
        "scrollX": true,
		"pageLength": 50,
		"search": {
			"smart": false
		},
		"columns": [
			null,null,					
			{ "type": "date" },
			null,null,null,null,null,null
			]
	});
	oTable.on("draw",function() {
		var keyword = $('#expense_table_list_others_filter > label:eq(0) > input').val();
		$('#expense_table_list_others').unmark();
		$('#expense_table_list_others').mark(keyword,{});
		setHeightForTable();
	    listTableHoverIconsInit('expense_table_list_others');
	});
	oTable.on('page.dt', function() {
	  $('html, body').animate({
		scrollTop: $(".dataTables_wrapper").offset().top - 15
	   }, 'slow');
       listTableHoverIconsInit('expense_table_list_others');
	});
	oSettings = oTable.settings();
    listTableHoverIconsInit('expense_table_list_others');
	$( window ).resize();
}

function convertToBase64() {
	var fileExtension = ['jpeg', 'jpg', 'png', 'pdf'];
	if ($.inArray($("#bill_copy_uploader").val().split('.').pop().toLowerCase(), fileExtension) == -1) {
		swal("Invalid File Format", "Please upload only "+fileExtension.join(', ')+" file format.", "warning");
		setTimeout(function(){
			$("#bill_copy_uploader").val('').clone(true);
			$("#bill_copy_uploader").closest('form').find('.bootstrap-filestyle').find('input.form-control ').val('');
			$("#id_exp_item-__prefix__-document_data").val('');
		},200);
	}
	else {
		<!--  code for gcs file upload -->
		var base_htm = '<span id="audit_expense_attachment_uploader_container"></span><input type="submit" value="Upload File" style="display:none"/>';
        $("#gcs_upload_expense").html(base_htm);
		var clone = $('#bill_copy_uploader').clone();
        clone.attr('id', 'bill_copy_uploader_cpy');
        clone.attr('name', 'file');
        $('#audit_expense_attachment_uploader_container').html(clone);

		var selectedFile = document.getElementById("bill_copy_uploader").files;
		if (selectedFile.length > 0) {
			var fileToLoad = selectedFile[0];
			var fileReader = new FileReader();
			var base64;
			fileReader.onload = function(fileLoadedEvent) {
				base64 = fileLoadedEvent.target.result;
				$("#id_exp_item-__prefix__-document_data").val(base64);
			};
			fileReader.readAsDataURL(fileToLoad);
		}
	}
}

$("#add_exp_tag").click(function(){
	var tag_code = $("#exp_tag_value").val();
	var tag_text = $("#id_exp_tag").val();
	if (tag_code == ""){
		if (tag_text == ""){
			return;
		}
		else {
			$(".li-tagit-display").removeClass('flash');
			var currentFieldName = $("#id_exp_tag").val();
			$("#exp_tags_table").find('.li-tagit-display:visible').each(function(){
				if(currentFieldName.toLowerCase().trim() == $(this).find('.label-tagit-display').text().toLowerCase().trim()){
					$('#id_exp_tag').val('');
					$(this).addClass('flash');
					return;
				}
			});

			if($("#id_exp_tag").val().trim() !="") {
				var row = "<li class='li-tagit-display'>" +
							"<label class='label-tagit-display'>"+$("#id_exp_tag").val()+"</label>"+
							"<div class='hidden-div-tagit-id' hidden='hidden'>0</div>"+
							"<a class='delete_tag'></a>"  + "</li>";
				$('#exp_tags_table').append(row).addClass('tbl');
			}
		}
	}
	else {
		$(".li-tagit-display").removeClass('flash');
		var currentFieldName = $("#id_exp_tag").val();
		$("#exp_tags_table").find('.li-tagit-display:visible').each(function(){
			if(currentFieldName.toLowerCase().trim() == $(this).find('.label-tagit-display').text().toLowerCase().trim()){
				$('#exp_tag_value').val('');
				$('#id_exp_tag').val('');
				$(this).addClass('flash');
				return;
			}
		});

		if($("#id_exp_tag").val().trim() !="") {
			var row = "<li class='li-tagit-display'>" +
						"<label class='label-tagit-display'>"+$("#id_po_tag").val()+"</label>"+
						"<div class='hidden-div-tagit-id' hidden='hidden'>"+ $("#exp_tag_value").val() +"</div>"+
						"<a class='delete_tag'></a>"  + "</li>";
			$('#po_tags_table').append(row).addClass('tbl');
		}
	}
	$('#exp_tag_value').val('');
	$('#id_exp_tag').val('');
	create_delete_tag_button();
});

$("#expense_list").click(function () {
	if(/[?&]view=/.test(location.search)) {
	    $(".fromdate, .todate").val('');
	 }
	reqAjax(true, false);
});
$("#expense_approved_list").click(function () {
if(/[?&]view=/.test(location.search)) {
    $(".fromdate, .todate").val('');
 }
	reqAjax(false, false);
});

function goBackToExpenses(){
	var url=window.location.href;
	if(url.indexOf("tab3") > 0) {
		parent.history.back();
	}
	else {
		location.reload();
	}
}

function formatGetData(is_expense, data){
	if(is_expense == 'true'){
 		if (data['expenses'].length <= 0) {
            row = "";
        }
        else {
            $.each(data['expenses'], function (index, value) {
	            if(value['created_by'] == {{ user_id }}){
					if(value["created_on"] !="") {
						var setDateTime = value["created_on"].split(' ');
					}
	                row += `<tr>
	                			<td class="text-center">${(count++)}</td>
	                            <td class="text-center">
	                            	<a role="button" class="edit_link_code" onclick="editExpenseRow('${value["id"]}');">
                            			${value["code"]}
                        			</a>
                    			</td>
                                <td class="text-center">${moment(setDateTime[0]).format('MMM D, YYYY')}</td>
                                <td class="text-left">${value["claim_head_ledger"]["name"]}</td>
                                <td class="text-left">${value["group_description"]}</td>
                                <td class="text-right">${value["claimed_amount"].toFixed(2)}</td>
                                <td class="text-center">
	                                <span role="button" class="${value[`status_code`].toLowerCase()} table-inline-icon-bg">
                        				${value["status_code"]}
                    				</span>
                    				<span class="table-inline-icon hide" data-tooltip="tooltip" data-placement="left" title="Edit" role="button" onclick="editExpenseRow('${value["id"]}');">
											<i class="fa fa-pencil"></i>
									</span>
									<span class="table-inline-icon hide" data-tooltip="tooltip" data-placement="left" title="Edit in New Tab" role="button" onclick="editExpenseRow('${value["id"]}', '_blank');">
											<i class="fa fa-external-link"></i>
									</span>
								</td>
							</tr>`;
	            }
            });
        }
        $('#expense_table_list_tbody').html(row);
		TableHeaderFixed();
	}
	else {
		if (data['expenses'].length <= 0) {
            row = "";
        }
        else {
 			$.each(data['expenses'], function (index, value) {
                if(value['created_by'] != {{ user_id }}){
            		if(value["created_on"] !="") {
						var setDateTime = value["confirmed_on"].split(' ');
					}
	                row += `<tr>
                                <td class="text-center">${(count++)}</td>
                                <td class="text-center">
                                	<a role="button" class="edit_link_code" onclick="editExpenseRow('${value["id"]}');">
                            			${value["code"]}
                        			</a>
                    			</td>
                                <td class="text-center">${moment(setDateTime[0]).format('MMM D, YYYY')}</td>
                                <td class="text-left">${value['claim_head_ledger']['name']}</td>
                                <td class="text-left">${value["group_description"]}</td>
                                <td class="text-left">${value["created_user"]['first_name']} ${value["created_user"]['last_name']}</td>
                                <td class="text-right">${value["claimed_amount"].toFixed(2)}</td>
                                <td class="text-right">${value["approved_amount"].toFixed(2)}</td>
                                <td class="text-center">
	                                <span role="button" class="${value[`status_code`].toLowerCase()} table-inline-icon-bg">
                        				${value["status_code"]}
                    				</span>
                    				<span class="table-inline-icon hide" data-tooltip="tooltip" data-placement="left" title="Edit" role="button" onclick="editExpenseRow('${value["id"]}');">
											<i class="fa fa-pencil"></i>
									</span>
									<span class="table-inline-icon hide" data-tooltip="tooltip" data-placement="left" title="Edit in New Tab" role="button" onclick="editExpenseRow('${value["id"]}', '_blank');">
											<i class="fa fa-external-link"></i>
									</span>
								</td>
                            </tr>`;

	            }
            });
        }
        $('#expense_table_list_tbody_others').html(row);
		TableHeaderFixedOthers();
	}
}

function formatGetDraftData(is_expense, data){
	$('#status').val('None');
	if(is_expense == 'true'){
 		if (data['Draft'].length <= 0) {
            row = "";
        }
        else {
            $.each(data['Draft'], function (index, value) {
	            if(value['created_by'] == {{ user_id }}){
					if(value["created_on"] !="") {
						var setDateTime = value["created_on"].split(' ');
					}
	                row += `<tr>
		                        <td class="text-center">${(count++)}</td>
		                        <td class="text-center">
                                	<a role="button" class="edit_link_code" onclick="editExpenseRow('${value["id"]}');">
                            			${value["code"]}
                        			</a>
                    			</td>
		                        <td class="text-center">${moment(setDateTime[0]).format('MMM D, YYYY')}</td>
		                        <td class="text-left">${value["claim_head_ledger"]["name"]}</td>
		                        <td class="text-left">${value["group_description"]}</td>
		                        <td class="text-right">${value["claimed_amount"].toFixed(2)}</td>
		                        <td class="text-center">
	                                <span role="button" class="${value[`status_code`].toLowerCase()} table-inline-icon-bg">
                        				${value["status_code"]}
                    				</span>
                    				<span class="table-inline-icon hide" data-tooltip="tooltip" data-placement="left" title="Edit" role="button" onclick="editExpenseRow('${value["id"]}');">
											<i class="fa fa-pencil"></i>
									</span>
									<span class="table-inline-icon hide" data-tooltip="tooltip" data-placement="left" title="Edit in New Tab" role="button" onclick="editExpenseRow('${value["id"]}', '_blank');">
											<i class="fa fa-external-link"></i>
									</span>
								</td>
		                    </tr>`;
	            }
            });
        }
        $('#expense_table_list_tbody').html(row);
		TableHeaderFixed();
	}
	else {
		if (data['Draft'].length <= 0) {
            row = "";
        }
        else {
 			$.each(data['Draft'], function (index, value) {
                if(value['created_by'] != {{ user_id }}){
            		if(value["created_on"] !="") {
						var setDateTime = value["confirmed_on"].split(' ');
					}
	                row += `<tr>
                                <td class="text-center">${(count++)}</td>
                                <td class="text-center">
                                	<a role="button" class="edit_link_code" onclick="editExpenseRow('${value["id"]}');">
                            			${value["code"]}
                        			</a>
                    			</td>
                                <td class="text-center">${moment(setDateTime[0]).format('MMM D, YYYY')}</td>
                                <td class="text-left">${value['claim_head_ledger']['name']}</td>
                                <td class="text-left">${value["group_description"]}</td>
                                <td class="text-left">${value["created_user"]['first_name']} ${value["created_user"]['last_name']}</td>
                                <td class="text-right">${value["claimed_amount"].toFixed(2)}</td>
                                <td class="text-right">${value["approved_amount"].toFixed(2)}</td>
                                <td class="text-center">
	                                <span role="button" class="${value[`status_code`].toLowerCase()} table-inline-icon-bg">
                        				${value["status_code"]}
                    				</span>
                    				<span class="table-inline-icon hide" data-tooltip="tooltip" data-placement="left" title="Edit" role="button" onclick="editExpenseRow('${value["id"]}');">
											<i class="fa fa-pencil"></i>
									</span>
									<span class="table-inline-icon hide" data-tooltip="tooltip" data-placement="left" title="Edit in New Tab" role="button" onclick="editExpenseRow('${value["id"]}', '_blank');">
											<i class="fa fa-external-link"></i>
									</span>
								</td>
                            </tr>`;

	            }
            });
        }
        $('#expense_table_list_tbody_others').html(row);
		TableHeaderFixedOthers();
	}
}

function reqAjax(is_expense, is_badge) {
	$("#loading").show();
	if(is_expense == true) {
		from_date = $('#etl_fromdate').val();
	    to_date = $('#etl_todate').val();
	    status = $('#status').val();
	}
	else {
		from_date = $('#etlo_fromdate').val();
	    to_date = $('#etlo_todate').val();
	}
	if(is_badge == true || $("#badge_hit").val()== "True"){
		if($('#batch_fromdate').val() != "None"){
			from_date = $('#batch_fromdate').val();
			status = 0;
			$("#badge_hit").val("False");
		}
	}else{
		if($('#batch_fromdate').val() != "None"){
			status = $('#status').val();
		}
	}
    $.ajax({
        url: '/erp/expenses/json/expense_list/',
        type: "POST",
        dataType: "json",
        data: {
            from_date: from_date,
            to_date: to_date,
            user_id: {{ user_id }},
            enterprise_id:{{ enterprise_id }},
            is_expense:is_expense,
            status:status
        },
        success: function (data) {
        	if(data['response_message'] == "Session Timeout") {
                location.reload();
                return;
            }
        	$('#expense_table_list, #expense_table_list_others').DataTable().clear();
			$('#expense_table_list, #expense_table_list_others').DataTable().destroy();
            row = "";count=1;
            if(data['expenses'] == null){
            formatGetDraftData(data['is_expense'],data)
            }else{
            formatGetData(data['is_expense'],data)
            }
            $("#loading").hide();
            updateFilterText();
            closeFilterOption();
            $(window).resize();
        },
        error: function (xhr, errmsg, err) {
        	$("#loading").hide();
             console.log(xhr.status + ": " + xhr.responseText);
        }
   	});
}

function deleteExpenseItem(deleteId) {
	swal({
	  title: "Are you sure?",
	  text: "Do you want to delete this Expenses?",
	  type: "warning",
	  showCancelButton: true,
	  confirmButtonColor: "#209be1",
	  confirmButtonText: "Yes, delete it!",
	  closeOnConfirm: true
	},
	function(){
	  $("#expensetable").find("#"+deleteId).hide();
	  $("#expensetable").find("#id_"+deleteId+"-DELETE").attr('checked', true);
	  CalculateClaimedAmount();
	  SerialNumberReorder();
	});
}

function SerialNumberReorder(){
	var sNo = 1;
	$("#expensetable tbody").find("tr:visible").each(function(){
		$(this).find('.td_serial_no:visible').text(sNo);
		sNo++;
	});
}

function ExpensesVerifiedClick() {
	$(".multi_check").on("click",function(){
		ExpensesVerifiedCheck();
	});
}

function ExpensesVerifiedCheck(){
	if($(".multi_check").length == $(".multi_check:checked").length) {
		$('#cmd_checked_ok').prop('disabled',false);
	} else {
		$('#cmd_checked_ok').prop('disabled',true);
	}
}

$(function() {
    $('a[data-toggle="tab"]').on('click', function(e) {
		if($("#exp_id_visible").is(":visible")){
			if($(e.target).attr('href') == '#tab1'){
				window.location = "/erp/expenses/home/";
			}
			else if($(e.target).attr('href') == '#tab2') {
				$("#loading").show();
				window.location = "/erp/expenses/home/<USER>";
			}
			else {
				window.location = "/erp/expenses/home/<USER>";
			}
		}
		DateRangeInit();
		SingleDatePickerInit();
		if($(e.target).attr('href') == '#tab1') {
			$("#expense_list").click();
		} else if($(e.target).attr('href') == '#tab2'){
			$("#expense_approved_list").click();
		}
    });

	if(window.location.href.indexOf('tab2') >= 0){
		window.location = "/erp/expenses/home/<USER>";
	}
});

$(".nav-tabs li a").click(function(){
	if($(this).attr("id") == "nav_tab_1") {
		$(".my_exp_csv").removeClass('hide');
		$(".others_exp_csv").addClass('hide');
	}
	else {
		$(".my_exp_csv").addClass('hide');
		$(".others_exp_csv").removeClass('hide');
	}
	setTimeout(function(){
		$(window).resize();
	},500);
});
$("#my_expenses_count").click(function(){
	if($("#batch_fromdate").val() != "None"){
		$(".fromdate").val($("#batch_fromdate").val());
		reqAjax(true, true);
	}
});

$("#other_expenses_count").click(function(){
	if($("#batch_fromdate").val() != "None"){
		$(".fromdate").val($("#batch_fromdate").val());
		reqAjax(false, true);
	}
});


$(".create_expenses").click(function(){
	$(".toggle, .create_expenses").addClass('hide');
	$(".view_expenses, .page_header, .page_header-container").removeClass('hide');
	$(".contant-container-nav").addClass("hide");
	$(".export_csv").addClass('hide');
	$(".page-title").html('New Expenses');	
	if($("#expense_table_list").hasClass('dataTable') || $("#expense_table_list_others").hasClass('dataTable')) {
		oTable.destroy();
	}
});

if(window.location.href.indexOf('tab2') >= 0){
	$(".my_exp_csv").addClass('hide');
	$(".others_exp_csv").removeClass('hide');
}
else if(window.location.href.indexOf('tab3') >= 0){
	setTimeout(function(){
		$(".toggle, .create_expenses").addClass('hide');
	},100);
	$(".view_expenses, .page_header, .page_header-container").removeClass('hide');
	$(".contant-container-nav").addClass("hide");
	$(".export_csv").addClass('hide');
	ExpenseSuperEditInit();
}

function setPartialDatePicker(){
	$('.single-datepicker-view-manual').each(function(){
		var thisDate = $(this).val().split('-');
		thisDate = thisDate[1]+"/"+thisDate[2]+"/"+thisDate[0];
		var formattedDate = (moment(thisDate)).format('MMM D, YYYY');
		$(this).next('.single-datepicker-manual').val(formattedDate);
	});
	$('.single-datepicker-manual').datepicker({
		format: 'M d, yyyy'
	}).change(dateChangedManual);

	$(".single-datepicker-manual").keydown(function(e){
		e.preventDefault();
	});
}

function dateChangedManual(ev) {
  $('.single-datepicker-manual').each(function(){
	  var thisVal = $(this).val();
	  var formattedDate = (moment(thisVal)).format('YYYY-M-D');
	  $(this).prev('.single-datepicker-view-manual').val(formattedDate);
  });
}

function ExpenseSuperEditInit(){
	if($(".header_current_page").text() == ""  || $(".header_current_page").text().indexOf("New") >=0 || $(".header_current_page").text().indexOf("DRAFT") >=0) {
		$(".super_user_icon, .xsid_number_edit, .super_user_tool_tip").remove();
	}
	else {
		$(".super_user_icon, .super_user_tool_tip").removeClass("hide");
	}
	$('.super_user_tool_tip span').qtip({
	   content: {
	        text: "<ul class='ul_for_tool_tip'><li>Super-edit enables modifying the Expense Code subject to Duplication Check.</li>\
	        		   <li>Code format will be 'FY-FY/EXP/NNNNNNx', <br />eg. '18-19/EXP/000731b'.<br />\
	        		   FY details - 5 characters (max), <br />Expense Type - 3 characters, <br />Number - 6 digits (max), <br />Sub-number - 1 alphabet (max).</li>\
	        		   <li>Subsequent numbering of Expense will pick from the highest of the Expense Code, <br/> i.e., gaps will not be filled automatically.</li></ul>",

	        title: 'Super-Edit'
	    }
	});
}

function EditExpenseNumber() {
	var expenseNumber = $(".header_current_page").text().trim();
	var expenseNumberSplit = expenseNumber.split("/");
	$("#exp_financial_year").val(expenseNumberSplit[0]);
	$("#exp_type").val(expenseNumberSplit[1]);
	if($.isNumeric( expenseNumberSplit[2].substr(-1) )){
		$("#exp_number").val(expenseNumberSplit[2]);	
	}
	else {
		$("#exp_number").val(expenseNumberSplit[2].slice(0, -1));
		$("#exp_number_division").val(expenseNumberSplit[2].substr(-1));
	}
	$(".xsid_number_edit").removeClass("hide");
	$(".super_user_icon, .header_current_page").addClass("hide")
}

function DiscardEditExpenseNumber(){
	$(".xsid_number_edit").addClass("hide");
	$(".super_user_icon, .header_current_page").removeClass("hide")
}

function SaveExpenseNumber(){
	if($("#exp_financial_year").val() =="" || $("#exp_number").val() == "") {
		$(".save_xsid_error_format").removeClass("hide");
		if($("#exp_financial_year").val() == "") $("#exp_financial_year").addClass("super_edit_error_border");
		if($("#exp_number").val() == "") $("#exp_number").addClass("super_edit_error_border");
	}
	else {
		$(".save_xsid_error_format").addClass("hide");
		if (parseInt($("#id_exp-status").val().trim()) == 4) {
			$.ajax({
                url: '/erp/expenses/json/expense_account_linked_message/',
                type: "POST",
                dataType: "json",
                data: {
                    expense_code: $(".header_current_page").text().trim()
                },
                success: function (response) {
                    if (response.response_message == "Success") {
                        if (response.custom_message == "") {
                            superEditExpenseCode();
                        } else {
                            swal({title: "", text: response.custom_message, type: "warning",
                                showCancelButton: true,
                                confirmButtonColor: "#209be1",
                                confirmButtonText: "Yes, do it!",
                                closeOnConfirm: true, closeOnCancel: true
                            },
	                        function(isConfirm){
	                            if (isConfirm) {
	                                setTimeout(function() {superEditExpenseCode();}, 500);
	                            } else {
	                                DiscardEditExpenseNumber();
	                            }
	                        });
                        }
                    } else {
                        swal({title: "", text: response.custom_message, type: "warning"});
                    }
                },
                error: function (xhr, errmsg, err) {
                    console.log(errmsg,  [xhr.responseText, err]);
                    swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
                }
            });
		} else {
			superEditExpenseCode();
		}
	}
}

function superEditExpenseCode() {
	$("#exp_number_division").val($("#exp_number_division").val().toLowerCase());
	$.ajax({
		url: "erp/expenses/json/super_edit_expense_code/",
		method: "POST",
		data:{
			expense_id: $("#id_exp-id").val(),
			new_financial_year: $("#exp_financial_year").val().trim(),
			new_expense_no: $("#exp_number").val().trim(),
			new_sub_number: $("#exp_number_division").val().trim()
		},
		success: function(response) {
			if (response.response_message == "Success") {
				swal({title: "", text: response.custom_message, type: "success"});
				DiscardEditExpenseNumber();
				$(".header_current_page, title").text(response.code);
			} else {
				swal({title: "", text: response.custom_message, type: "warning"});
			}
            ga('send', 'event', 'Expense', 'Super-Edit Code', $('#enterprise_label').val(), 1);
		},
		error: function (xhr, errmsg, err) {
            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
        }
	});
}

function SuperEditExpSelect(field) {
	$(field).closest("div").find(".div-disabled").removeClass("div-disabled");
	$(field).addClass("hide");
}

function SuperEditExpInput(field) {
	$(field).closest("div").find("input").removeAttr("disabled");
	$(field).addClass("hide");
}
$("#cmd_exp_save").click(function(){
    ga('send', 'event', 'Expense', 'Save Draft', $('#enterprise_label').val(), 1);
});
$("#cmd_exp_confirm").click(function(){
    ga('send', 'event', 'Expense', 'Submit for Approval', $('#enterprise_label').val(), 1);
});
$("#cmd_exp_approve").click(function(){
    ga('send', 'event', 'Expense', 'Approve', $('#enterprise_label').val(), 1);
});
$("#cmd_checked_ok").click(function(){
    ga('send', 'event', 'Expense', 'Checked by Internal Audit', $('#enterprise_label').val(), 1);
});
$("#cmd_exp_verify").click(function(){
    ga('send', 'event', 'Expense', 'Verified', $('#enterprise_label').val(), 1);
});

function editExpenseRow(expense_id, openTarget="") {
	$("#id-edit_expense_id").val(expense_id);
	$("#id-edit_expense_form").attr("target", openTarget).submit();
}

</script>
{% endblock %}