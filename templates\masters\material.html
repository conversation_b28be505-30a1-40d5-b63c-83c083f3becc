{% extends "masters/sidebar.html" %}
{% block catalogues %}
<style>
	.tool-tip {
	  	display: inline-block;
	}

	.tool-tip [disabled] {
	  	pointer-events: none;
	}

	li.material_side_menu a{
	    outline: none;
	    background-color: #e6983c !important;
	}

	.bootstrap-filestyle {
		width: 100%;
	}

	.loading-main-container {
		display: block;
	}

	.a_material_inuse input[type=checkbox] {
		width:  16px;
		height:  16px;
	} 
	
 	.outer {
	  	max-width:1000px;
	  	max-height:300px;
	  	height: 100vh;
	  	overflow: hidden;
	  	display: flex;
	  	flex-direction: column;
	}
   	.inner {
  		flex: 1;
  		overflow-y: scroll;
 	}

	#searchResult tbody td:nth-child(1),
	#searchResult tbody td:nth-child(13),
	#searchResult tbody td:nth-child(14),
	#searchResult tbody td:nth-child(4){
	    text-align: center;
	}

	#searchResult tbody td:nth-child(8),
	#searchResult tbody td:nth-child(9),
	#searchResult tbody td:nth-child(10),
	#searchResult tbody td:nth-child(7){
	    text-align: right;
	}

	#searchResult tbody td:last-child {
	    display:none;
	}

	#searchResult tbody td:last-child.dataTables_empty {
		display: table-cell;
	}

 	.table-non-price-profiled .td-material-profile,
 	.table-non-price-profiled .th-material-profile {
 		display: none;
 	}

 	.create_material {
		border-radius: 50px 0 0 50px !important;
    	border-right: 1px solid #ccc;
	}

	.create_add_container a.btn-new-item {
		box-shadow: -4px 4px 5px 0 rgba(60,64,67,0.302), 0 1px 3px 1px rgba(60,64,67,0.149);
	}

	.import_csv.btn-new-item  {
		border-radius: 0 50px 50px 0;
	}

	.btn-new-item-label {
    	color: #004195;
    	padding: 6px !important;
	}

	.create_material:hover,
	.import_csv:hover {
		background: rgba(32, 155, 225,0.1);
		box-shadow: -4px 4px 5px 0 rgba(60,64,67,0.302), 0 1px 3px 1px rgba(60,64,67,0.149) !important;
	}

	.price_profile_remarks {
		position: absolute;
	    right: -4px;
	    border-radius: 50px;
	    padding: 7px 10px;
	    margin-top: -6px;
	    font-size: 14px;
	    cursor: pointer;
	}
	.price_profile_remarks:hover {
		background: #ccc;
	}

	.material_specification_container_label {
		border-radius: 6px;
		background: #f9f5f4;
		cursor: pointer;
		border: solid 1px #ccc;
		transition: all 0.5s;
	}

	.material_specification_container_label:hover {
		background: #fff5f4;
		border: solid 1px #666;
	}

	.material_specification_container_delete {
		display: none;
	}

	.material_specification_container_label:hover + .material_specification_container_delete,
	.material_specification_container_delete:hover {
		display: contents;
	}
	.
	.material_specification_remarks {
		max-width: 90%;
		display: inline-block;
	}

	.material_specification_container_label .material_specification_qc.checked {
	   	padding: 3px 6px;
	    border: solid 1px #ccc;
	    font-size: 10px;
	    border-radius: 4px;
	}


	.material_specification_container_label .material_specification_qc.checked:after {
	    content: "QC Critical";
	}

	.material_specification_container_label .material_specification_parameter,
	.material_specification_container_label .material_specification_minMaxValue {
		font-size: 16px;
	}

	.material_specification_container_label .material_specification_unit {
		font-size: 12px;
	}

	.material_specification_container_label .material_specification_inspectionMethod,
	.material_specification_container_label .material_specification_reactionPlan,
	.material_specification_container_label .material_specification_remarks {
		font-size: 12px;
		display: block;
	}

	.material_specification_container_label .material_specification_inspectionMethod:before,
	.material_specification_container_label .material_specification_reactionPlan:before,
	.material_specification_container_label .material_specification_remarks:before {
		width: 125px;
	    display: inline-block;
	    font-weight: bold;
	}


	.material_specification_container_label .material_specification_inspectionMethod:before {
		content: 'Inspection Method: ';
	}

	.material_specification_container_label .material_specification_reactionPlan:before {
		content: 'Reaction Plan: ';
	}

	.material_specification_container_label .material_specification_remarks:before {
		content: 'Remarks: ';
	}

	.material_specification_container_label .material_specification_inspectionMethod:empty,
	.material_specification_container_label .material_specification_reactionPlan:empty,
	.material_specification_container_label .material_specification_remarks:empty {
		display: none;
	}

	.material_specification_container_label .material_specification_parameter {
		margin-right: 30px;
		line-height: 30px;
	    display: inline-block;
	}


	.material_specification_delete {
	    position: absolute;
	    right: 4px;
	    margin-top: -10px;
	    border: solid 1px transparent;
	}

	.material_specification_delete a {
	    font-size: 14px;
	    color: #FFF;
	    border-radius: 50px;
	    padding: 2px 5px;
	    cursor: pointer;
	    background: #dd4b39;
	}

	.material-profile-image {
	  	display: inline-block;
		margin-right: 10px;
	  	position: relative;
	  	border: dashed 1px #ccc;
	    height: 175px;
	    width: 175px;
	    cursor: pointer;
	}

	.material-profile-image img {
		position: absolute;
	    top: 50%;
	    left: 50%;
	    margin-right: -50%;
	    transform: translate(-50%, -50%);
	    padding: 8px;
	}

	span.camera-roll {
		background:center center no-repeat #fff;
		height: 170px;
		position: absolute;
	  	text-align: center;
		width: 170px;
		z-index: 10;
	}

	.camera-roll:before {
	    content: "\f030";
	    font-family: FontAwesome;
	    font-style: normal;
	    font-weight: normal;
	    text-decoration: inherit;
	    color: #333333;
	    font-size: 30px;
	    padding-right: 0.5em;
	    position: absolute;
	    top: 66px;
	    left: 71px;
	}

	.available_stock_container {
		display: inline-block;
	    padding: 0 15px;
	    border: dashed 1px #CCC;
	    /* cursor: pointer; */
	    position: absolute;
	    top: -49px;
	}

	.available_stock_container:hover {
		background: #FFFFF0;
		border-color: #000;
	}


	input[type="checkbox"],
	input[type="checkbox"]:focus,
	input[type="checkbox"]:before:focus,
	input[type="checkbox"]:after:focus {
		outline: none !important;
	}

	.stock_total {
	    margin-top: 0;
	    max-width: 380px;
	    word-break: break-all;
	    margin-bottom: 0;
	    display: inline-block;
	    float: left;
	    margin-left: 10px;
	    font-size: 23px;
	}

	.currency-conversion-indv input {
		width: 100px;
	    display: inline-block;
	    height: 26px;
	    font-size: 12px;
	    padding: 4px 12px;
	    margin: 0 6px 6px;
	}

	#cheapest_supplier_bom tbody tr td .tree-view {
	position: relative;
	}

	#cheapest_supplier_bom tbody tr td .tree-view:before {
	    border-bottom: 1px solid #bbb;
	    content: "";
	    left: 6px;
	    position: absolute;
	    top: 6px;
	    width: 1.5em;
	}

	#cheapest_supplier_bom tbody tr td .tree-view:after {
	    border-left: 1px solid #bbb;
	    content: "";
	    left: 6px;
	    position: absolute;
	    top: -25px;
	    height: 4em;
	}

	#cheapest_supplier_bom .fa-plus:before,
	#cheapest_supplier_bom .fa-minus:before {
		border: solid 1px #333;
	    padding: 2px 3px 2px 3px;
	    border-radius: 3px;
	    font-size: 10px;
	}

	.filter_item_stock_table{
	    border-top: hidden !important;
	    border-left: hidden !important;
	    border-right: hidden !important;
	}

	.dropdown-menu .custom-error-message {
		margin-top: -52px;
	}

</style>

<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-multiselect.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/make.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/catalogue.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/party_list.js?v={{ current_version }}"></script>

<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">Material</span>
	</div>
	<div class="page-heading_new" style="margin: 0 15px;">
		<span class="page_header hide">
			{% if material_form.drawing_no.value != None and material_form.drawing_no.value != "" %}
				{{ material_form.name.value }}
			{% endif %}	
		</span>

		{% if access_level.edit %}
			<div class="create_add_container">
				<a class="btn btn-new-item pull-right import_csv" onclick="javascript:showImportMaterial();" data-tooltip="tooltip" title="Import Bulk Materials List">
					<i class="fa fa-upload" aria-hidden="true"></i>
					<span class="btn-new-upload-label"></span>
				</a>
				<a data-toggle="tab" id="add_new_material" class="btn btn-new-item pull-right create_material" data-tooltip="tooltip" title="Add New Material">
					<i class="fa fa-plus" aria-hidden="true"></i>
					<span class="btn-new-item-label"></span>
				</a>
			</div>
		{% else %}
			<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in Profile Module. Please contact the administrator.">
				<a class="btn btn-new-item pull-right import_csv disabled">
					<i class="fa fa-upload" aria-hidden="true"></i>
					<span class="btn-new-upload-label"></span>
				</a>
				<a class="btn btn-new-item pull-right create_material disabled">
					<i class="fa fa-plus" aria-hidden="true"></i>
					<span class="btn-new-item-label"></span>
				</a>
			</div>	
		{% endif %}


	</div>
	<div class="filter-components filter-materials" style="margin-left: 335px;">
		<div class="filter-components-container">
			<div class="dropdown">
				<button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
					<i class="fa fa-filter"></i>
				</button>
				<span class="dropdown-menu arrow_box arrow_box_filter tour_filter">
					<div class="col-sm-12">
						<table class="table table-bordered" id="filter_stock_notstock" style="border: 1px solid #ddd;">
							<thead></thead>
							<tbody>
								<tr class="filter_item_stock_table">
									<td class="filter_item_stock_table"></td>
									<td class="text-center filter_item_stock_table"><label>In Use</label></td>
									<td class="text-center filter_item_stock_table"><label>Not In Use</label></td>
									<td class="text-center filter_item_stock_table"><label>All</label></td>
								</tr>
								<tr class="item_stockable">
									<td><label>Stockable</label> </td>
									<td class="text-center">
										<div class="checkbox" style="margin-top:2px !important; margin-bottom:-1px !important;">
											  <input name="in_use_items" id="id_in_use_items" class="select_item_stocks select_stockable" type="checkbox" checked="checked" onchange="selectAllStockable();"/>
											  <label for="id_in_use_items"></label>
										</div>
									</td>
									<td class="text-center">
										<div class="checkbox" style="margin-top:2px !important; margin-bottom:-1px !important;">
											<input name="not_in_use_items" id="id_not_in_use_items" class="select_item_stocks select_stockable" type="checkbox" onchange="selectAllStockable();"/>
											 <label for="id_not_in_use_items"></label>
										</div>
									</td>
									<td class="text-center">
										<div class="checkbox" style="margin-top:2px !important; margin-bottom:-1px !important;">
											<input name="all_items" id="id_all_items" class="select_stockable" type="checkbox"/>
											<label for="id_all_items"></label>
										</div>
									</td>
								</tr>
								<tr class="item_non-stockable">
									<td><label class="for_service-label">Non Stockable </label> </td>
									<td class="text-center">
										<div class="checkbox" style="margin-top:0px !important; margin-bottom:0px !important;">
											<input name="non_stock_in_use_items " class="select_item_non-stocks select_stockable" id="id_non_stock_in_use_items" type="checkbox" onchange="selectAllNonStockable();"/>
											<label for="id_non_stock_in_use_items"></label>
										</div>
									</td>
									<td class="text-center">
										<div class="checkbox" style="margin-top:0px !important; margin-bottom:0px !important;">
											<input name="non_stock_not_in_use_items" class="select_item_non-stocks select_stockable" id="id_non_stock_not_in_use_items" type="checkbox" onchange="selectAllNonStockable();"/>
											<label for="id_non_stock_not_in_use_items"></label>
										</div>
									</td>
									<td class="text-center">
										<div class="checkbox" style="margin-top:0px !important; margin-bottom:0px !important;">
											<input name="non_stock_all_items" class=" select_stockable" id="id_non_stock_all_items" type="checkbox"/>
											<label for="id_non_stock_all_items"></label>
										</div>
									</td>
								</tr>
							</tbody>
						</table>
						<div class="checkbox" style="padding-left: 22px !important;">
							<input name="price_profiled" id="id_price_profiled" type="checkbox"/>
							<label for="id_price_profiled">Include Price Profile</label>
						</div>
					</div>

					<div class="filter-footer">
						<input type="submit" class="btn btn-save select_any_one_stocks" value="Apply" onclick="selectFilter()" />
  					</div>
				</span>
			</div>
			<span class='filtered-condition filtered-stock-material for_goods'>Stock Material: <b></b></span>
			<span class='filtered-condition filtered-non-stock for_goods'>Non Stock: <b></b></span>
			<span class='filtered-condition filtered-in-use for_service'>Service: <b></b></span>
			<span class='filtered-condition filtered-price-profile'>Price Profile: <b></b></span>
		</div>
	</div>
	<input type="hidden" id="is_access_level" value="{{access_level.delete }}" />
	<input type="hidden" id="material_type" value="{{material_type}}" />

	<div class="col-lg-12">
		<div class="content_bg">
			<div class="contant-container-nav hide">
				<ul class="nav nav-tabs list-inline">
					<li class="active"><a id="viewMaterial" href="/erp/masters/catalogues">VIEW</a></li>
					<li><a id="addMaterial" data-toggle="tab" href="#tab2">ADD</a></li>
				</ul>
			</div>
			<div class="tab-content">
				<div id="tab1" class="tab-pane fade in active">
					<div class="col-sm-12 table-with-upload-button">
						<div class="csv_export_button">
							<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#searchResult'), 'Material_List.csv']);" data-tooltip="tooltip" title="Download Materials List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
						</div>
						<table class="table table-bordered custom-table table-striped" name="material_list" id="searchResult" style="width: 100%;">
							<thead>
							<tr>
								<th rowspan='2'>S.No</th>
								<th rowspan='2' style="width: 12%;">
									{% if material_type == 'service' %}
										Item code
									{% else %}
										Drawing No
									{% endif %}
								</th>
								<th rowspan='2' style="width: 25%;">Description</th>
								<th rowspan='2' style="width: 5%;">UOM</th>
								<th rowspan='2' class="{% if material_type == 'service' %}hide{% endif %}">Manufacturer Part Numbers</th>
								<th rowspan='2'>Category</th>
								<th colspan="3" class="{% if material_type == 'service' %}hide{% endif %}">Stock</th>
								<th rowspan='2' class='th-store-price'>Store Price</th>
								<th rowspan='2' class='th-material-profile'>Price Profiles</th>
								<th rowspan='2' class='th-material-profile'>Price Profiles Status</th>
								<th rowspan='2'>Last Modified On</th>
								<th rowspan='2' class="hide">STATUS</th>
								<th rowspan='2' class="exclude_export">In Use</th>
							</tr>
							<tr>
								<th class="{% if material_type == 'service' %}hide{% endif %}">In Store</th>
								<th class="{% if material_type == 'service' %}hide{% endif %}">Allocated</th>
								<th class="{% if material_type == 'service' %}hide{% endif %}">Available</th>
							</tr>
							</thead>
							<tbody>
							</tbody>
						</table>
					</div>
					<div class="clearfix"></div>
				</div>
				<div id="tab2" class="tab-pane fade">
					<div class="add_table container" id="material_container" style="margin-top: 10px;">
						{% if material_type == 'goods' %}
							<a href="/erp/masters/catalogues/" class="btn btn-add-new pull-right view_material hide" data-tooltip="tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i>
							</a>
						{% else %}
							<a href="/erp/masters/services/" class="btn btn-add-new pull-right view_material hide" data-tooltip="tooltip" title="Back">	<i class="fa fa-arrow-left" aria-hidden="true"></i>
							</a>
						{% endif %}
						{% if access_level.view %}
							<a role="button" onclick="javascript:showUploadMaterialModal();" style="margin-right: 6px; font-size: 20px; padding: 2px 10px;" id="material-attachment-icon" class="btn btn-add-new pull-right view_material hide" data-tooltip="tooltip" title="Attachments"><i class="fa fa-paperclip" aria-hidden="true"></i></a>
						{% else %}
							<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in Profile Module. Please contact the administrator.">
								<a role="button" style="margin-right: 6px; font-size: 20px; padding: 2px 10px;" id="material-attachment-icon" class="btn btn-add-new pull-right view_material disabled hide"><i class="fa fa-paperclip" aria-hidden="true"></i></a>
							</div>
						{% endif %}
						<div id="supplierPriceBomMaterial">
							<a role="button" class="btn btn-add-new pull-right btn-margin-1" style="margin-right: 10px;margin-top:0px; padding: 4px 10px;" onclick="javascript:showCheapestSupplierBom(this);"  data-tooltip="tooltip" title="" data-placement="left" data-original-title="BOM Costing">
								<img src="/site_media/images/costing_sheet_icon.svg" style="width: 26px;" />
							</a>
						</div>
						<a role="button" data-toggle="modal" data-target="#material_bom_modal" onclick="javascript:getBomParentId();" style="margin-right: 6px; font-size: 20px; padding: 1px 5px;" class="btn btn-add-new pull-right show-bom-icon view_material hide" data-tooltip="tooltip" title="Bill of Materials">
							<img src="/site_media/images/bom_icon.svg" style="width: 29px;" />
						</a>
						<a role="button" onclick="loadPriceProfileUnits()" data-toggle="modal" data-target="#material_price_profile" style="margin-right: 6px; padding: 1px 5px;" class="btn btn-add-new pull-right show-price-profile-icon view_material hide" data-tooltip="tooltip" title="Price Profile">
							<img src='/site_media/images/price_profile_icon.svg' style="width: 30px;" />
						</a>
						<form action="/erp/masters/catalogues/save/" method="post" id="edit_catalogue_form" enctype="multipart/form-data">{% csrf_token %}
						{% include "masters/add_material_react_form.html" %}
						{% include "masters/add_material_supporting_modal.html" %}
						</form>

						<form id="edit_material_from_bom" action="/erp/masters/catalogues/edit/#tab2" method="post" target="_blank">
							<input type='hidden' id="hdn_edit_material_from_bom" name='item_id' value=''/>
							{% if material_type == 'service' %}
								<input type='hidden' id="edit_material_type_from_bom" name='edit_material_type' value='true'/>
							{% else %}
								<input type='hidden' id="edit_material_type_from_bom" name='edit_material_type' value='false'/>
							{% endif%}
							<input type="hidden" name="csrfmiddlewaretoken" value='{{ csrf_token }}' />
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>


<div id="error_messages" class="modal fade" role="dialog">
  	<div class="modal-dialog">
    	<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Errors in Material Creation</h4>
      		</div>
      		<div class="modal-body">
      			<h5 style="color: #dd4b39;">Process Failed due to incomplete form submission. Please fill the following data:</h5>
        		<ul>
					{% if material_form.errors %}
					<h4>Material Errors:</h4>
					{% endif %}
					{% for field in material_form.hidden_fields %}
						{% for error in field.errors %}
							<li>{{field.label_tag}} : {{error}}</li>
						{% endfor %}
					{% endfor %}
					{% for field in material_form.visible_fields %}
						{% for error in field.errors %}
							<li>{{field.label_tag}} : {{error}}</li>
						{% endfor %}
					{% endfor %}
				</ul>
				<ul>
					{% if bill_of_materials_formset.errors %}
					<h4>BoM Materials Errors:</h4>
					{% endif %}
					{% for form in bill_of_materials_formset.initial_forms %}
						{% for field in form.hidden_fields %}
							{% for error in field.errors %}
								<li>Material {{form.prefix}}:{{field.label_tag}}:{{error}}</li>
							{%endfor%}
						{% endfor %}
						{% for field in form.visible_fields %}
							{% for error in field.errors %}
								<li>Material {{form.prefix}}:{{field.label_tag}}:{{error}}</li>
							{%endfor%}
						{% endfor %}
					{% endfor %}
					{% for error in bill_of_materials_formset.errors %}
						{{error}}
					{% endfor %}

					{% if price_of_materials_formset.errors %}
					<h4>Supplier Price Materials Errors:</h4>
					{% endif %}
					{%for form in price_of_materials_formset.initial_forms %}
						{% for field in form.hidden_fields %}
							{% for error in field.errors %}
								<li>Supplier Wise Material Price  {{form.prefix}}:{{field.label_make}}:{{error}}</li>
							{%endfor%}
						{% endfor %}
						{% for field in form.visible_fields %}
							{% for error in field.errors %}
								<li>Supplier Wise Material Price {{form.prefix}}:{{field.label_make}}:{{error}}</li>
							{%endfor%}
						{% endfor %}
					{% endfor %}
					{%for error in price_of_materials_formset.errors %}
						{{error}}
					{% endfor %}
				</ul>
      		</div>
      		<div class="modal-footer">
        		<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        		<input type="text" value='{{ material_form.errors.as_text }}' id="form_errors" hidden="hidden"/>
				<input type="text" value='{{bill_of_materials_formset.errors.as_text }}' id="formset_errors" hidden="hidden"/>
      		</div>
    	</div>
  	</div>
</div>

<!-- /#wrapper -->
<div id="importmaterial" class="modal fade" role="dialog">
  	<div class="modal-dialog modal-lg">
    	<div class="modal-content">
	      	<div class="modal-header">
	        	<button type="button" class="close" data-dismiss="modal">&times;</button>
	        	<h4 class="modal-title">Import Materials</h4>
	      	</div>
	      	<div class="modal-body">
			  	<form>
		      		<table id="importtable" cellpadding="2" align="center" cellspacing="0" border="0" class="table table-bordered custom-table table-striped">
						<thead>
						    <tr bgcolor="#525151" style="color:#FFFFFF; font-weight:normal; align:center !important">
								<th width="15%" class="service_item-code">Drawing_No</th>
								<th width="20%">Name</th>
								<th width="20%">Category</th>
								<th width="25%">Description</th>
							    <th width="40%" class="service_sac">HSN</th>
								<th width="5%">Price</th>
								<th width="5%">In_Use</th>
								<th width="40%">Unit</th>
								<th width="40%" class="service_fault">Faultless</th>
							    <th width="40%" class="service_fault">Faulty</th>
						    </tr>
						</thead>
						<tbody>
							<tr>
								<td>4120140100400000</td>
								<td>POWER WIRE,1.5SQ MM,YELLOW</td>
								<td>Components(CO)</td>
								<td>POWER WIRE USED TO ENABLE POWER SUPPLY</td>
								<td>A00483</td>
								<td>123.45</td>
								<td>0/1</td>
								<td>2</td>
								<td class="service_fault">200</td>
								<td class="service_fault">100</td>
							</tr>
							<tr>
								<td style="font-size:10px">(Alpha-numeric Code)</td>
								<td style="font-size:10px">(Alpha-numeric Text)</td>
								<td style="font-size:10px">(Alpha-numeric Text)</td>
								<td style="font-size:10px">(Alpha-numeric Text)</td>
								<td style="font-size:10px">(Alpha-numeric Code)</td>
								<td style="font-size:10px">(Numeric)</td>
								<td style="font-size:10px">(0-False, 1-True)</td>
								<td style="font-size:10px">{%for unit in unit_list %} {{ unit.0 }} - {{ unit.1 }}<br> {% endfor %} </td>
								<td style="font-size:10px" class="service_fault">(Numeric)</td>
								<td style="font-size:10px" class="service_fault">(Numeric)</td>
							</tr>
							<tr>
								<td colspan="7" style="border: none;font-weight:bold;" class="service_file_import">Please Select the File for Import Materials</td>
								<td colspan="3" style="border: none;" class="text-right service_download-sample"><a href="/site_media/docs/import_material_sample.csv" class="service_sample-csv">Download Sample</a> </td>
							</tr>
							<tr>
								<td colspan="10">
									<input type="file" class="filestyle" data-buttonBefore="true" type="file" id="fileUpload" />
								</td>

							</tr>
						    <tr>
							    <td colspan="10">
									<div class="material_txt" style="margin-right:0px;">
										<a href="#" class="btn btn-save" id="cmdUploadMaterial"height="40">Upload</a>
										<a href="#" class="btn btn-cancel" id="cmdhideUpload" height="40" data-dismiss="modal">Cancel</a>
									</div>
								</td>
						    </tr>
						</tbody>
					</table>
		  		</form>
	      	</div>
	    </div>
  	</div>
</div>

<div id="importmaterial_status_modal" class="modal fade" role="dialog">
  	<div class="modal-dialog modal-lg">
    	<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Import Material status</h4>
      		</div>
      		<div class="modal-body">
	      		<div>
		      		<h4 id="import_message"></h4>
	      		</div>
	      		<br>
	      		<a role="button" class="pull-right" onclick="GeneralExportTableToCSV.apply(this, [$('#failed_import_table'), 'import_failed_items.csv']);">Download failed items csv</a><br>
	    		<div style='overflow:auto' class="outer">
	    			<div style='overflow:auto' class="inner">
	    				{% include "admin/material_failure.html" %}<br>
	    			</div>
	    		</div><br><br>
      		</div>
		</div>
  	</div>
</div>

<div id="supplierPriceBom" class="modal fade" role="dialog">
  	<div class="modal-dialog modal-lg" style="width: 90%; max-width: 1300px;">
    	<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
      			<div>
	      			<div style="width:50%;float:left;">
		      			<h4 class="modal-title">BOM Costing</h4>
	        			<span><label >Based on recent cheapest valid price</label></span>
	     			 </div>
	      			<div style="width:30%;float:right;margin-right:15px; margin-top: -15px;">
		      			<a role="button" class="btn btn-add-new pull-right btn-margin-1 export_bom_list" onclick="costingBOMDownload(this);"  data-tooltip="tooltip" title="" data-original-title="Download Costing BoM List as CSV" data-placement="bottom"><i class="fa fa-download" aria-hidden="true"></i></a>
	     			</div>
      			</div>
      		</div>
      		<div class="modal-body">
      			<div class="converted-rate-text hide" style="margin-bottom: 5px; font-style: oblique;">
      				Kindly provide appropriate Exchange Rates to know the actual Costs.
      			</div>
      			<div class="currency-conversion-container">
      				
      			</div>
				<div class="pull-right hide" id="expandAllCheckboxDiv">
					<input type="checkbox" id="expandAllCheckbox">
					<label for="expandAllCheckbox">Expand/Collapse All</label>
				</div>
				<table id="cheapest_supplier_bom" cellpadding="2" align="center" cellspacing="0" border="0" class="table table-bordered custom-table table-striped">
					<thead>
						<tr>
							<th width="20px">S.No</th>
							<th width="350px">Name</th>
							<th width="225px" class="service_item_code">Drawing No</th>
							<!-- <th width="74px">Make</th> -->
							<!-- <th width="74px">MPN</th> -->
							<th width="74px">Unit</th>
							<th width="74px">Qty</th>
							<th width="200px" class="supplierForGoods">Supplier <br />(Valid Approved)</th>
							<th width="74px">Store price</th>
							<th width="74px" class="goodsData">Last Purchased Price</th>
							<th width="74px" class="goodsData">Valid Approved Price</th>
							<th width="74px">Cost of Store Price<br />(<span style="text-transform: lowercase;">in</span> <span class='home-currency'></span>)</th>
							<th width="74px" class="goodsData">Cost of Last Purchased Price<br />(<span style="text-transform: lowercase;">in</span> <span class='home-currency'></span>)</th>
							<th width="74px" class="goodsData">Cost of Valid Approved Price<br />(<span style="text-transform: lowercase;">in</span> <span class='home-currency'></span>)</th>
						</tr>
					 <div id='loadingmessage_changelog_listing_ie1' style="position:absolute;text-align:center;position: absolute;z-index: 1;left: 40%;top: 30%;display: none;">
						<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' width="100">
						<br /> Please wait...
			    </div>
					</thead>
					<tbody>
						<div id='loading-icon' class="text-center" style='display:box;'>
							<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' style="width:75px"/>
						    <br>
						    Please wait...
						</div>
					</tbody>
				</table>
      		</div>
	    	<div class="modal-footer">
			   <span class="pull-left nonStock-text">  <span class=" nonStock-text non_stock-flag"></span> - NON-STOCKABLE</span>
				<span class="pull-left nonStock-text">  <span class=" nonStock-text service-item-flag"></span> - SERVICE</span>
				<a href="#" class="btn btn-cancel" data-dismiss="modal">Close</a>
			</div>
	    </div>
  	</div>
</div>

<div id="importBom" class="modal fade" role="dialog">
  	<div class="modal-dialog modal-lg">
    	<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Import Bill of Materials</h4>
      		</div>
      		<div class="modal-body">

		  		<form id="files" onsubmit="return false;" method="post" enctype="multipart/form-data">{% csrf_token %}
          			<input type=hidden name="drawing_no_bom" id="drawing_no_bom" value="{{ material_form.drawing_no.value }}">
		  			<input type=hidden name="parent_id" id="id-parent_id" value="{{ material_form.material_id.value }}">
					<table>
						<td>
							<h6 class="warning_material_text">Warning:Existing bill of materials/Drawing number will be replaced by the materials/Drawing number from the document! </h6>
							<h6 class="warning_qty_text">Warning:Materials/Drawing number will be skipped if the quantity is zero ! </h6>
						</td>
						<td>
							<div id ="materal_box" style="text-align: right; margin-left: 100px;">
								<p style="display: inline-block; margin-right: 10px;">MATERIAL NAME</p>
								<input type="radio" id="material_name" name="select_field" value="MATERIAL NAME" onclick="handleCheckboxClick('material_name')" checked/>
							</div>
							<div id="drawing_box" style="text-align: right; margin-left: 70px;">
								<p style="display: inline-block; margin-right: 10px;" class="drawing_number">DRAWING NUMBER</p>
								<input type="radio" id="drawing_no" name="select_field" value="DRAWING NUMBER" onclick="handleCheckboxClick('drawing_no')"/>
							</div>
						</td>
					</table>
	      			<table id="importBomTable" cellpadding="2" align="center" cellspacing="0" border="0" class="table table-bordered custom-table table-striped">
						<thead>
						    <tr bgcolor="#525151" style="color:#FFFFFF; font-weight:normal; align:center !important">
								<th id="tableHeader" width="20%" >Material Name</th>
								<th width="25%">Qty</th>
						    </tr>
						</thead>
						<tbody>
							<tr>
								<td id="exampleValue" style="text-align: center;">40W ELECTRONIC CHOKE</td>
								<td style="text-align: center;">10</td>
							</tr>
							<tr style="text-align: center;">
								<td style="font-size:10px">(Alpha-numeric)</td>
								<td style="font-size:10px">(Numeric)</td>
							</tr>
							<tr>
								<td colspan="5"><b class="service_import_file">Please Select the File for Import Bill of Materials</b></td>
							</tr>
							<tr>
								<td colspan="5">
									<input class="filestyle" data-buttonBefore="true" required="true" name="bomfile" type="file" id="fileUploadbom" />
								</td>
							</tr>
						    <tr>
							    <td colspan="5">
							    	<button id="xlhideUploadButton" class="btn btn-save">Upload</button>
									<a href="#" class="btn btn-cancel" id="xlhideUpload" height="40" data-dismiss="modal">Cancel</a>
								</td>
						    </tr>
						</tbody>
					</table>
				</form>
      		</div>
		</div>
  	</div>
</div>

<div id="upload_material_modal" class="modal fade" role="dialog">
  	<div class="modal-dialog">
    	<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Attachments for <div class="attachment-title" style="text-shadow: 0 0 #000; display: inline-block;"></div></h4>
      		</div>
      		<div class="modal-body">
		  		<form action="https://storage.googleapis.com/{{ GCS_BUCKET_NAME }}/" id="material_attachment_upload"  onsubmit="return false;" method="post" enctype="multipart/form-data">
		  			<div class="col-sm-12 form-group">
		  				<label style="width: 160px;">Label/ Description<span class="mandatory_mark"> *</span></label>
                        <input type="text" name="label" id="document_label" class="form-control" maxlength="120" autocomplete="off" placeholder="" />
		  			</div>

	    		</form>
		         <form action='https://storage.googleapis.com/{{ GCS_BUCKET_NAME }}/' id="material_attachment_uploading" name="material_attachment_uploading" method="POST" enctype='multipart/form-data'>
			         <div class="col-md-12 form-group" style="width: 500px;">
		  				<input type="file"  class="filestyle col-sm-12" data-buttonBefore="true" name="file" id="attachmentupload"/>
		  			</div>
			        {% if access_level.edit %}
			        <div class="text-right" style="margin-right: 15px;">
				        <input type='submit' id="materialhideUploadButton" class="btn btn-save" value="Add" disabled="true"/>
					</div>
			        {% endif%}
				</form>

	    		<hr />
	    		<div class="col-sm-12 remove-padding" id="materials_attachment_container" style="display: contents;">
				    <div id='loadingmessage_material_attachment' style="text-align:center">
						<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' width="100">
						<br /> Please wait...
			    </div>
			    </div>
      		</div>
      		<div class="modal-footer">
      			<a class="btn btn-cancel" id="materialhideUpload" onclick="closeMaterialUpload();">Close</a>
      		</div>
    	</div>
  	</div>
</div>

<div id="change_log_modal" class="modal" tabindex="-1" role="dialog" aria-labelledby="" aria-hidden="true">
   <div class="modal-dialog">
	    <div class="modal-content">
		    <div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Activity Log</h4>
	      	</div>
      		<div class="modal-body remove-padding">
		        <div id='loadingmessage_changelog_listing_ie' style="text-align:center">
						<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' width="100">
						<br /> Please wait...
			    </div>
        		<ul class="history-log-container timeline">

        		</ul>
        		<ul class="show-more-log" style="list-style: none; text-align: right; margin-right: 15px; cursor: pointer;">
			        <li onclick="shoMoreLogs(20,20)">Show More</li>
		        </ul>
      		</div>
      		<div class="modal-footer">
      			<a href="#" class="btn btn-cancel" data-dismiss="modal">Close</a>
      		</div>
    	</div>
  	</div>
</div>
<div class="hide">
	<form id="material_edit_form" action='/erp/masters/catalogues/edit/#tab2' method='post'>
		{% csrf_token %} 
        <input type='hidden' id="edit_drawing_no" name='drawing_no' value=''/>
        <input type='hidden' id="edit_item_id" name='item_id' value=''/>
		<input type='hidden' id="edit_material_type" name='edit_material_type' value=''/>
    </form>
</div>
{% include "attachment_popup.html" %}
{% include "masters/add_party_modal.html" %}
<style type="text/css">
	.history-log-part {
		width: 100%;
	    margin: 0;
	    padding: 8px 15px;
	    cursor: pointer;
	    padding-left: 60px;
	}
	.history-log-part:last-child {
		border-bottom: none;
	}
	.history-log-part:hover {
		background: #e8f0fe;
	}
	.history-log-part:hover .history-log-date {
		font-weight: bold;
	}
	.history-log-date {
	   font-style: italic;
	}
	.history-log-username {
		font-size: 15px;
		padding-top: 3px;
		font-weight: bold;
	}

	.history-log-content {
		margin-top: 8px;
	}

	.history-log-content ul {
		padding: 0px 10px 0px 35px;
	}
	.history-log-content li {
		margin-bottom: 10px;
	}

	.history-log-content li:last-child {
		margin-bottom: 0;
	}

	.history-log-part.active .history-log-date,
	.history-log-part.active .history-log-username {
		color: #004195;
	}

	.history-log-part.active .history-log-date {
	    text-align: right;
		display: block;
	}

	.history-log-part .fa-chevron-down:before {
		content: "\f078";
		color: #999;
	}

	.history-log-part.active .fa-chevron-down:before {
		content: "\f077";
		color: #999;
	}

	#change_log_modal .modal-body {
		overflow: auto;
	}


	ul.timeline {
	    list-style-type: none;
	    position: relative;
		padding: 0px;
	}
	ul.timeline:before {
	    content: ' ';
	    background: #d4d9df;
	    display: inline-block;
	    position: absolute;
	    left: 29px;
	    width: 2px;
	    height: 94%;
	    z-index: 400;
        margin-top: 12px;
	}
	ul.timeline > li {

	}
	ul.timeline > li:before {
	    content: ' ';
	    background: white;
	    display: inline-block;
	    position: absolute;
	    border-radius: 50%;
	    border: 3px solid #209be1;
	    left: 23px;
	    width: 14px;
	    height: 14px;
	    z-index: 400;
	    margin-top: 4px;
	}
	ul.timeline > li:last-child:before {
		background: #209be1;
	}
	body {
		overflow-y: scroll;
	}
</style>
<script language="javascript">

function handleCheckboxClick(checkboxId) {
	if (checkboxId === 'material_name' && $("#material_type").val() === "goods") {
		document.getElementById('tableHeader').innerHTML = 'Material Name';
		document.getElementById('exampleValue').innerHTML = '40W ELECTRONIC CHOKE';
		document.getElementById('drawing_no').checked = false;
		document.getElementById('drawing_no').removeAttribute('checked');
		document.getElementById('material_name').setAttribute('checked', 'true');

	} else if (checkboxId === 'drawing_no' && $("#material_type").val() === "goods") {
		document.getElementById('material_name').checked = false;
		document.getElementById('tableHeader').innerHTML = 'Drawing Number';
		document.getElementById('exampleValue').innerHTML = 'ZWZ6404ZZ';
		document.getElementById('material_name').removeAttribute('checked');
		document.getElementById('drawing_no').setAttribute('checked', 'true');
	}
	if (checkboxId === 'material_name' && $("#material_type").val()== "service") {
		document.getElementById('drawing_no').checked = false;
		document.getElementById('tableHeader').innerHTML = 'Material Name';
		document.getElementById('exampleValue').innerHTML = '40W ELECTRONIC CHOKE';
		document.getElementById('drawing_no').removeAttribute('checked');
		document.getElementById('material_name').setAttribute('checked', 'true');

	} else if (checkboxId === 'drawing_no' && $("#material_type").val()== "service") {
		document.getElementById('material_name').checked = false;
		document.getElementById('tableHeader').innerHTML = 'ITEM CODE';
		document.getElementById('exampleValue').innerHTML = 'ZWZ6404ZZ';
		document.getElementById('material_name').removeAttribute('checked');
		document.getElementById('drawing_no').setAttribute('checked', 'true');
	}
	}


$('#loading').hide();
if($("#id_material-drawing_no").val()==""){
   $("#importbutton").addClass("disabled");
   }

$( "#id_material-drawing_no" ).change(function() {
   $("#drawing_no_bom").val($(this).val());
   if( $("#drawing_no_bom").val() ){
     $("#importbutton").removeClass("disabled");
    }
    else{
      $("#importbutton").addClass("disabled");
      $('[data-toggle="tooltip"]').tooltip({
      		trigger : 'hover'
      })
    }

});

$(document).ready(function() {
	if($("#material_type").val()== "goods"){
		$(".for_service").addClass('hide');
	}
	$('#loadingmessage_material_attachment').hide();
    $('.modal').on('hidden.bs.modal', function(e) {
        <!--$(this).find('form')[0].reset();-->
    }) ;
    $('#xlhideUploadButton').submit(function(e) {
		$('#importBom').modal('toggle'); //or  $('#IDModal').modal('hide');
		return false;
	});
	var url=window.location.href;
	materialSelected();
	if(url.indexOf("edit") < 0 ){
		$(".export_csv, .import_csv, .create_material, .filter-materials").removeClass('hide');
		$(".view_material").addClass('hide');
		$("#id-change-log").addClass("hide");
		if(url.indexOf("services") > 0){
			populateMaterials(1);
		}else{
			populateMaterials(0);
		}
	}
	else {
		$(".export_csv, .import_csv, .create_material, .filter-materials").addClass('hide');
		$(".view_material").removeClass('hide');
		changeLogActivityInit();
	}
	materialGoodsAndService();

});
$(function() {
	$('#id_bill_material-__prefix__-drawing_no').click(function () {
        document.getElementById('id_bill_material-__prefix__-units').value = $('#id_bill_material-__prefix__-drawing_no option:selected').val();
    });

    $('#id_cat_material-__prefix__-drawing_no').change(function () {
        document.getElementById('id_cat_material-__prefix__-units').value = $('#id_cat_material-__prefix__-drawing_no option:selected').val();;
    });
});

$(function(){
  var hash = window.location.hash;
  hash && $('ul.nav a[href="' + hash + '"]').tab('show');

  $('.nav-tabs a').click(function (e) {
    $(this).tab('show');
    var scrollmem = $('body').scrollTop() || $('html').scrollTop();
    window.location.hash = this.hash;
    $('html,body').scrollTop(scrollmem);
  });
});

function shoMoreLogs(offset, limit){
	if($("#change_log_modal").hasClass("change_log")) {
		var material_id = $("#id_material-material_id").val();
		$.ajax({
	        url: '/erp/masters/material/getloglist/',
	        type: "POST",
	        dataType: "json",
	        data: {'material_id': material_id, offset: offset, limit: limit},
	        success: function (data) {
		        if (data['response_code'] != 400){
		            x = data['data'];
					var row = "";
					var i2 = offset;
					for(var i = 0; i < x.length; i++) {
					    var obj = x[i].log;
					    var row = `<li class="history-log-part" id="history-part-${i2}" onclick="loadHistoryContent('${data['data'][0].material_id}', '${obj.modified_at}', ${i2})">
					                        <span class="history-log-username">${obj.username[0]+" "+obj.username[1]}</span>
					                        <div class="history-log-content" style="display: none;"></div>
					                        <span class="history-log-date">${moment(obj.modified_at).format("MMM DD, YYYY hh:mm:ss a")}</span>
					                    </li>`;
		                $(".history-log-container").append(row);
		                var displayedCount = $(".history-log-container").find("li").length;
		                i2++;
		                if(x.length < 20){
		                    $(".show-more-log").addClass("hide");
		                }
		                else {
		                    $(".show-more-log").find("li").attr("onclick", "shoMoreLogs("+displayedCount+", 20)");
	                    }
					}
				}
				else {
					$(".show-more-log").addClass("hide");
				}
	        },
	        error: function ($xhr,textStatus,errorThrown) {
		        console.log("ERROR : ", $xhr);
		        if ($xhr.responseText.indexOf("Your session has expired") != -1){
		            location.reload();
		        }
	        }
		});
	}
}


function changeLogActivityInit(){
		$("#id-change-log").removeClass("hide");
		$("#btn_change_log").click(function(){
			$("#change_log_modal").addClass("change_log").removeClass("change_preference_log");
			$("#change_log_modal").modal("show");
			$("#loadingmessage_changelog_listing_ie").show();
			$(".history-log-container").html("");
			var material_id = $("#id_material-material_id").val();
			var material_type = $("#material_type").val()
			$.ajax({
		        url: '/erp/masters/material/getloglist/',
		        type: "POST",
		        dataType: "json",
		        data: {'material_id': material_id, 'offset': 0, 'limit': 20, 'material_type': material_type},
		        success: function (data) {
		        if (data['response_code'] != 400){
		            x = data['data'];
					var row = "";
					for(var i = 0; i < x.length; i++) {
					    var obj = x[i].log;
					    var row = `<li class="history-log-part" id="history-part-${i}" onclick="loadHistoryContent('${data['data'][0].material_id}', '${obj.modified_at}', ${i})">
				                        <span class="history-log-username">${obj.username[0]+" "+obj.username[1]}</span>
				                        <div class="history-log-content" style="display: none;"></div>
				                        <span class="history-log-date">${moment(obj.modified_at).format("MMM DD, YYYY hh:mm:ss a")}</span>
				                    </li>`;
				        $("#loadingmessage_changelog_listing_ie").hide();
		                $(".history-log-container").append(row);
		                var displayedCount = $(".history-log-container").find("li").length;
					}
						if(x.length < 20){
		                    $(".show-more-log").addClass("hide");
		                }
		                else {
		                    $(".show-more-log").removeClass("hide");
		                    $(".show-more-log").find("li").attr("onclick", "shoMoreLogs("+displayedCount+", 20)");
	                    }
				}
				else {
				    var row = `<span style="width: 100%; padding: 15px; text-align:center; display: block;">No Activity Log has been captured.</span>`;
					$("#loadingmessage_changelog_listing_ie").hide();
		            $("#change_log_modal .modal-body").html(row);
				}
		        },
		        error: function ($xhr,textStatus,errorThrown) {
			        console.log("ERROR : ", $xhr);
			        if ($xhr.responseText.search("Session Expired!") != -1){
			            location.reload();
			        }
		        }
			});
		});

		$("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
	}

	function loadHistoryContent(material_id, modified_at, i) {
		var currentLogContent = $("#history-part-"+i).find('.history-log-content');
		var material_type = $("#material_type").val()
		if(currentLogContent.text() == "") {
			currentLogContent.html(`<img src="/site_media/images/loading_spinner.gif" style="width: 30px;"> Loading, Please wait...`).show();
			currentLogContent.closest(".history-log-part").addClass("active");
			setTimeout(function(){
				$.ajax({
			        url: '/erp/masters/material/getlogdata/',
			        type: "POST",
			        dataType: "json",
			        data: {"material_id":material_id, "modified_at": modified_at, "material_type": material_type},
			        success: function (data) {
			        if (data['response_code'] != 400){
			        var row = '';
				        $.each( data['data'], function( key, value ) {
						  row += `<li>` + value + `</li>`;
						});
			        currentLogContent.html(`<ul>` + row + `</ul>`);
					currentLogContent.hide();
					currentLogContent.slideDown();
					}else{
					currentLogContent.html(`log data not available`);
					currentLogContent.hide();
					currentLogContent.slideDown();

					}
			        },
			        error: function () {
			        console.log('Changelog listing failed to load');

			        }
				});

			},100);
		}
		else {
			if(currentLogContent.find("img").length > 0) {
				return;
			}
			if(currentLogContent.is(":visible")) {
				currentLogContent.slideUp('fast', function() {
				   currentLogContent.closest(".history-log-part").removeClass("active");
				});

			}
			else {
				currentLogContent.slideDown();
				currentLogContent.closest(".history-log-part").addClass("active");
			}
		}
		$("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
	}


function updateStockDetails(){
	$.ajax({
		url: "/erp/stores/json/material_stock/",
		type: "post",
		datatype: "json",
		data: {item_id:$("#id_material-material_id").val()},
		success: function (response) {
			try {
				var total = 0;
				if(response.material_stock.length != 0)
				{
					$.each(response.material_stock, function(i, item) {
				        if(item.is_faulty)
					    {
					        var row = "<tr>"+
				                    "<td class='text-center'>"+ (i + 1) + ".</td><td align='left'>"+item.make_name + item.part_name + "(FAULTY)" + "</td><td align='right'>" +
				                    item.closing_stock.toFixed(2) + "</td></tr>";
				        }
				        else
				        {
				            var row = "<tr>"+
				                    "<td class='text-center'>"+ (i + 1) + ".</td><td align='left'>"+item.make_name + item.part_name + "</td><td align='right'>" +
				                    item.closing_stock.toFixed(2) + "</td></tr>";
				        }
	                    $('#supp_material_table tbody').append(row);
					    total = total + item.closing_stock;
					});
					$('#closing_stock_total, .stock_total').text(total.toFixed(2));
				}
				else
				{
					var row = "<tr>"+
				                    "<td colspan=3 class='text-center' style='font-weight: bold;'>"+ "No stock available!" + "</td></tr>";
				    $('#supp_material_table tbody').append(row);
				    $('#supp_material_table tfoot').addClass("hide");
				}
			}
			catch (Exception) {
			   console.log("Exception:" + Exception);
			}
		},
		error: function (xhr, errmsg, err) {
            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
        }
	});
}

function getCheapestMaterialListBOM(){
	$(".currency-conversion-container").html("");
	$(".converted-rate-text").addClass("hide");
	$.ajax({
		url: "/erp/masters/json/materials/get_cheapest_supplier_bom/",
		type: "post",
		datatype: "json",
		data: {material_id:$("#id_material-material_id").val()},
		success: function (response) {
			try {
				$('#cheapest_supplier_bom tbody').html('');
				$("#loading-icon").addClass("hide");
				if(response.length != 0) {
					var currenyList = [];
					var homeCurrency = $("#home_currency_id").val();
					$.each(response, function(i, item) {
	                    if(typeof(Storage) !== "undefined") {
	                        if (sessionStorage.clickcount) {
	                            sessionStorage.clickcount = Number(sessionStorage.clickcount) + 1;
	                        } else {
	                            sessionStorage.clickcount = 1;
	                        }
	                    }

						var drawingNo = "";
						var currency = "-NA-";
						var item_name = item.name;
						if(item.drawing_no != "" && item.drawing_no != null) {
							drawingNo = item.drawing_no;
						}
						if(item.currency != "" && item.currency != null) {
							currency = item.currency;
							if(currenyList.indexOf(currency) === -1 && currency.indexOf(homeCurrency) === -1){
							     currenyList.push({text:item.currency,id:item.currency_code});
							 }
						}
						var item_type_flag = "";
						if(item.is_stock != 1) {
							item_type_flag = `<span class='non_stock-flag'></span>`;
						}

						if(item.is_service == 1){
							item_type_flag = `<span class="service-item-flag"></span>`;
						}


						var currency_code = "-NA-";
						if(item.currency_code != "" && item.currency_code != null) {
							currency_code = item.currency_code;
						}

						if (item.is_stock == 1){ classname='' } else{ classname='nonStock-mark' }
						if(item.hasChildren) {
							var childArrow = "<i class='fa fa-plus fa-for-arrow' role='button' onclick='return appendgetCheapestMaterialListBOM(\""+ item.item_id +"\", \""+ sessionStorage.clickcount +"\", \""+item.item_id+ "_" + sessionStorage.clickcount +"\")'><a style='padding-left: 26px; display: block; margin-top: -12px;' >"+item.name +" "+ item_type_flag+"</a></i>"
						}
						else {childArrow = item.name+" "+item_type_flag}

				        // var row = `	<tr data-currency='${currency.toLowerCase()}' data-currency-code="${item.currency_code}" data-toggle='close' data-padding='0' data-parent= "${ item.item_id }_${ sessionStorage.clickcount }" id= "${ item.item_id }_${ sessionStorage.clickcount }">
				        //             	<td class='text-left bom-sno'>${(i + 1)}</td>
				        //             	<td align='left'>${childArrow}</td>
				        //             	<td align='left'>${drawingNo}</td>
				        //             	<td align='left'>${item.make}</td>
			            //         		<td align='left'>${item.mpn}</td>
				        //             	<td align='left'>${item.unit}</td>
				        //             	<td align='right' class='item-qty'>${item.qty}</td>
				        //             	<td align='left'>${item.supplier}</td>
				        //             	<td align='right' class='item-price'>${item.price.toFixed(2)}</td>
				        //             	<td align='center'>${currency}[${currency_code}]</td>
				        //             	<td align='right' class='item-total'>${(item.qty * item.price).toFixed(2)}</td>
			            //         	</tr>`;
						var row = `	<tr data-currency='${currency.toLowerCase()}' data-currency-code="${item.currency_code}" data-toggle='close' data-padding='0' data-parent= "${ item.item_id }_${ sessionStorage.clickcount }" id= "${ item.item_id }_${ sessionStorage.clickcount }">
				                    	<td class='text-left bom-sno'>${(i + 1)}</td>
				                    	<td align='left'>${childArrow}</td>
				                    	<td align='left'>${drawingNo}</td>      	
			                    		<td align='left'>${item.unit}</td>
				                    	<td align='right' class='item-qty'>${item.qty}</td>
				                    	<td align='left'>${item.supplier}</td>
				                    	<td align='right' class='item-price'>${item.stored_price == '-NA-' ? '-NA-' : `${item.stored_price.toFixed(2)}<br/><span style="font-size:9px">(${item.stored_currency_code})</span>`}</td>
										<td align='right' class='goodsData'>${item.purchase_material_price == '-NA-' ? '-NA-' : `${item.purchase_material_price.toFixed(2)}<br/><span style="font-size:9px">(${item.purchase_currency_code})</span>`}</td>
										<td align='right' class='goodsData'>${item.approved_price == '-NA-' ? '-NA-' :`${item.approved_price.toFixed(2)}<br/><span style="font-size:9px">(${item.approved_currency_code})</span>`}</td>
										<td align='right' class='item-total'>${item.stored_price == '-NA-' ? '-NA-' : (item.qty * item.stored_price).toFixed(2)}</td>
										<td align='right' class='goodsData'>${item.purchase_material_price == '-NA-' ? '-NA-' : (item.qty * item.purchase_material_price).toFixed(2)}</td>
										<td align='right' class='goodsData'>${item.approved_price == '-NA-' ? '-NA-' : (item.qty * item.approved_price).toFixed(2)}</td>
			                    	</tr>`;
	                    $('#cheapest_supplier_bom tbody').append(row);
					});
					currenyList.forEach(createCurrencyConverionRow);
					expandedPlusRows = $('#cheapest_supplier_bom').find('.fa-plus');
					if(expandedPlusRows.length > 0){
						$('#expandAllCheckboxDiv').removeClass('hide');
					}
					else{
						$('#expandAllCheckboxDiv').addClass('hide');
					}
					updateCurrencyConverter();
					materialSelected();
				}
				else {
					if($("#material_type").val() == "goods"){
						var row = `	<tr>
				                        <td colspan='12' class='text-center' style='font-weight: bold;'>No BOM Costing profiled yet!</td>
			                        </tr>`;
			        }
		            else{

                        var row = `	<tr>
	                                    <td colspan='12' class='text-center ' style='font-weight: bold;'>No Service Costing profiled yet!</td>
                                     </tr>`;
		            }
				    $('#cheapest_supplier_bom tbody').append(row);
				    $('#cheapest_supplier_bom tfoot').addClass("hide");
				}
			}
			catch (Exception) {
			   console.log("Exception:" + Exception);
			}
		},
		error: function (xhr, errmsg, err) {
            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
        }
	});
}

function appendgetCheapestMaterialListBOM(cat_code, dataChild, dataParent){
    var currentCatChild = $("#"+cat_code + "_" + dataChild).attr('id');
    var constructedRow = '';
    var current_bom_sno = $("#"+dataParent).find(".bom-sno").text().trim();
    if($("#"+currentCatChild).attr('data-toggle') == 'open') {
        var curPadding = $("#"+currentCatChild).attr('data-padding');
        $("#"+currentCatChild).nextAll('tr').each(function(){
            if(Number($(this).attr('data-padding')) > Number(curPadding)) {
                $(this).remove();
            }
            else {
                return false;
            }
        });
        $("#cheapest_supplier_bom #"+cat_code + "_" + dataChild).find('.fa.fa-for-arrow').removeClass('fa-minus').addClass('fa-plus');
        $("#cheapest_supplier_bom #"+cat_code + "_" + dataChild).attr('data-toggle','close');
    }
    else {
        $("#cheapest_supplier_bom #"+cat_code + "_" + dataChild).find('.fa.fa-for-arrow').removeClass('fa-plus').addClass('fa-minus');
        $("#cheapest_supplier_bom #"+cat_code + "_" + dataChild).attr('data-toggle','open');
        $("#loadingmessage_changelog_listing_ie1").show();
	    $.ajax({
			url: "/erp/masters/json/materials/get_cheapest_supplier_bom/",
			type: "post",
			datatype: "json",
			data: {material_id:cat_code},
			success: function (response) {
				try {
					if(response.length != 0) {
						var currenyList = [];
						var homeCurrency = $("#home_currency_id").val();
						$.each(response, function(i, item) {
		                    if(typeof(Storage) !== "undefined") {
		                        if (sessionStorage.clickcount) {
		                            sessionStorage.clickcount = Number(sessionStorage.clickcount) + 1;
		                        } else {
		                            sessionStorage.clickcount = 1;
		                        }
		                    }
							var itemTypeFlag ="";
							if (item.is_service == 1){  itemTypeFlag = `<span class="service-item-flag"></span>`}
							if (item.is_stock != 1 && item.is_service != 1){  itemTypeFlag = `<span class='non_stock-flag'></span>`}

							var drawingNo = "";
							var currency = "-NA-";
                            var dataPadding = Number($("#"+cat_code + "_" + dataChild).data('padding') + 30);
							if(item.drawing_no != "" && item.drawing_no != null) {
								drawingNo = item.drawing_no;
							}

							if(item.currency != "" && item.currency != null) {
								currency = item.currency;
								if(currenyList.indexOf(currency) === -1 && currency.indexOf(homeCurrency) === -1){
								     currenyList.push({text:item.currency,id:item.currency_code});
								 }
							}
							var currency_code = "-NA-";
							if(item.currency_code != "" && item.currency_code != null) {
							currency_code = item.currency_code;
							}
							if (item.is_stock == 1){ classname='' } else{ classname='nonStock-mark' }
							if(item.hasChildren) {
							var childArrow = "<i class='fa fa-plus fa-for-arrow' role='button' onclick='return appendgetCheapestMaterialListBOM(\""+ item.item_id +"\", \""+ sessionStorage.clickcount +"\", \""+item.item_id+ "_" + sessionStorage.clickcount +"\")'><a style='padding-left: 26px; display: block; margin-top: -12px;' >"+item.name+" "+itemTypeFlag+"</a></i>"
							}
							else {childArrow = item.name+itemTypeFlag;}

					        // var row = `	<tr data-currency='${currency.toLowerCase()}' data-currency-code="${item.currency_code}" data-toggle='close' data-padding='${ dataPadding }' data-parent= "${ dataParent }" id= "${ item.item_id }_${ sessionStorage.clickcount }" data-child="${ item.cat_code }_${ dataChild }">
				            //         	<td class='text-left bom-sno'>${current_bom_sno}.${(i + 1)}</td>
			                //     		<td style='padding-left:${Number(dataPadding-20)}px'><span style='padding-left:30px; display: block;' class='tree-view'>${childArrow}</span></td>
				            //         	<td align='left'>${drawingNo}</td>
				            //         	<td align='left'>${item.make}</td>
			                //     		<td align='left'>${item.mpn}</td>
				            //         	<td align='left'>${item.unit}</td>
				            //         	<td align='right' class='item-qty'>${item.qty}</td>
				            //         	<td align='left'>${item.supplier}</td>
				            //         	<td align='right' class='item-price'>${item.price.toFixed(2)}</td>
				            //         	<td align='center'>${currency}[${currency_code}]</td>
				            //         	<td align='right' class='item-total'>${(item.qty * item.price).toFixed(2)}</td>
			                //     	</tr>`;
							var row = `	<tr data-currency='${currency.toLowerCase()}' data-currency-code="${item.currency_code}" data-toggle='close' data-padding='${ dataPadding }' data-parent= "${ dataParent }" id= "${ item.item_id }_${ sessionStorage.clickcount }" data-child="${ item.cat_code }_${ dataChild }">
				                    	<td class='text-left bom-sno'>${current_bom_sno}.${(i + 1)}</td>
			                    		<td style='padding-left:${Number(dataPadding-20)}px'><span style='padding-left:30px; display: block;' class='tree-view'>${childArrow}</span></td>
				                    	<td align='left'>${drawingNo}</td>
				                    	<td align='left'>${item.unit}</td>
				                    	<td align='right' class='item-qty'>${item.qty}</td>
				                    	<td align='left'>${item.supplier}</td>
				                    	<td align='right' class='item-price'>${item.stored_price == '-NA-' ? '-NA-' : `${item.stored_price}<br/><span style="font-size:9px">(INR)</span>`}</td>
				                    	<td align='right' class='goodsData'>${item.purchase_material_price == '-NA-' ? '-NA-' : `${item.purchase_material_price.toFixed(2)}<br/><span style="font-size:9px">(${item.purchase_currency_code})</span>`}</td>
				                    	<td align='right' class='goodsData'>${item.approved_price == '-NA-' ? '-NA-' :`${item.approved_price.toFixed(2)}<br/><span style="font-size:9px">(${item.approved_currency_code})</span>`}</td>
				                    	<td align='right' class='item-total'>${item.stored_price == '-NA-' ? '-NA-' : (item.qty * item.stored_price).toFixed(2)}</td>
				                    	<td align='right' class='item-total goodsData'>${item.purchase_material_price == '-NA-' ? '-NA-' : (item.qty * item.purchase_material_price).toFixed(2)}</td>
				                    	<td align='right' class='item-total goodsData'>${item.approved_price == '-NA-' ? '-NA-' : (item.qty * item.approved_price).toFixed(2)}</td>
			                    	</tr>`;
			                constructedRow = constructedRow+row
			                $("#loadingmessage_changelog_listing_ie1").hide();
						});
						$('#cheapest_supplier_bom  #'+cat_code + "_" + dataChild).after(constructedRow);
						currenyList.forEach(createCurrencyConverionRow);
						updateCurrencyConverter();
						materialSelected();
					}
				}
				catch (Exception) {
				   console.log("Exception:" + Exception);
				}
			},
			error: function (xhr, errmsg, err) {
	            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
	        }
		});
	}
	expandedMinusRows = $('#cheapest_supplier_bom').find('.fa-minus');
	if(expandedMinusRows.length <= 0){
		$('#expandAllCheckbox').prop('checked',false);
	}

}

function updateCurrencyConverter() {
    $(".currency-conversion-indv input").each(function(){
        var baseCurrencyTxt = $("#home_currency_id").val();
        var currentCurrency = $(this).attr("data-forCurrency");
        var currentElement = $(this);
        var item_json = {"from_currency": currentCurrency, "to_currency": baseCurrencyTxt};
        $.ajax({
           url : "/erp/commons/json/currency_converter/",
           type : "POST",
           dataType: "json",
           data :  item_json,
           async: true,
           success : function(response) {
              currentElement.val(response.result);
              $("#cheapest_supplier_bom").find(`tr[data-currency-code='${currentCurrency}']`).each(function(){
                  var convertedValue = $(this).find(".item-qty").text() * $(this).find(".item-price").text() * currentElement.val();
                  $(this).find(".item-total").text(convertedValue.toFixed(2))
               })
           }
        });
    });
}

function createCurrencyConverionRow(value){
  var homeCurrency = $("#home_currency_id").val().toUpperCase();
  var isCurrencyAppended = false;
  $(".currency-conversion-container .currency-conversion-indv").each(function(){
    if($(this).find(".currency-convertor-txt").text().toUpperCase() == value.id.toUpperCase()) {
        isCurrencyAppended = true;
    }
  });
  if(!isCurrencyAppended) {
      var row = `<div class="currency-conversion-indv">
                  1 <span class="currency-convertor-txt">${value.id.toUpperCase()}</span> = <input type="text" id="currency_value" class="form-control text-right" onfocus="setNumberRangeOnFocus(this,10,3)" onkeyup="updateCurrencyValue(this)" data-forCurrency = "${value.id.toUpperCase()}" value = "1.00" /> ${homeCurrency}
               </div>`;
      $(".currency-conversion-container").append(row);
  }
  $(".converted-rate-text").removeClass("hide");
}

function updateCurrencyValue(current) {
    var currentCurrency = $(current).attr("data-forCurrency");
    $("#cheapest_supplier_bom").find(`tr[data-currency-code='${currentCurrency}']`).each(function(){
      var convertedValue = $(this).find(".item-qty").text() * $(this).find(".item-price").text() * $(current).val();
      $(this).find(".item-total").text(convertedValue.toFixed(2))
   })
}

function updateFilterText() {
    $(".filtered-price-profile b").text(($("#id_price_profiled").is(":checked")? "Included":"Excluded"));
    if($("#id_all_items").is(":checked")){
    	$(".filtered-stock-material b").text("All");
    }
    else if($("#id_in_use_items").is(":checked")){
    	$(".filtered-stock-material b").text("In Use");
    }
    else if($("#id_not_in_use_items").is(":checked")){
    	$(".filtered-stock-material b").text("Not In Use");
    }
    if($(".select_item_stocks:checked").length ==  0){
    	$(".filtered-stock-material b").text("Excluded");
    }

    if($("#id_non_stock_all_items").is(":checked")){
    	$(".filtered-non-stock b").text("All");
    	$(".filtered-in-use b").text("All");
    }
    else if($("#id_non_stock_in_use_items").is(":checked")){
    	$(".filtered-non-stock b").text("In Use");
    	$(".filtered-in-use b").text("In Use");
    }
    else if($("#id_non_stock_not_in_use_items").is(":checked")){
    	$(".filtered-non-stock b").text("Not In Use");
    	$(".filtered-in-use b").text("Not In Use");
    }
    if($(".select_item_non-stocks:checked").length ==  0){
    	$(".filtered-non-stock b").text("Excluded");
    	$(".filtered-in-use b").text("Excluded");
    }
}

function loadPriceProfileUnits(){
	if($("#alternateUoms").find(".alternate-unit-count").text() >= 1) {
		$("#price-profile-default-unit").text($("#id_material-unit_id option:selected").text())
		$("#price_profile-alternate_units").removeClass("hide");
	}
	else {
		$("#quantity_unit_display").text($("#id_material-unit_id option:selected").text().split("(")[0].trim())
		$("#price_profile-alternate_units").addClass("hide");
	}
}

function materialGoodsAndService(){
	$("#add_new_material").click(function(){
		$("#id_material-drawing_no").val('');
	    $("#id_material-material_id").val('');
	    $("#id_material-name").val('');
	    $("#tariff_no").find("label").text("HSN");
	    $("#tab1").removeClass("in active");
	    $("#tab2").addClass("in active");
	    $("#searchResult").html('');
		oTable.destroy();
		$(".export_csv, .import_csv, .create_material, .filter-materials").addClass('hide');
		$(".view_material").removeClass('hide');
		$(".show-price-profile-icon, .available_stock_container, .available_stock_container_faulty").addClass("hide");
		$("#switch_material_service").addClass('hide');

		if($("#material_type").val()== "goods"){
			$(".page-title").html('New Goods');
		}
		else if($("#material_type").val()== "service"){
			$(".page-title").html('New Service');
			$("#tariff_no").find("label").text("SAC");
			$(".service_item_code").find("label").text("Item Code");
			$("#id_material-drawing_no").attr("placeholder", "Enter Item Code");
			$("#id_material-is_stocked").prop("checked", false);
			$("#id_material-is_service").prop("checked", true);
			$(".for_goods").addClass('hide');
		}
	});
}

function materialSelected(){
	$("#switch_material_service").addClass('hide');

	if($("#material_type").val()== "goods"){
		$('.nav-pills li').removeClass('active');
		$('#li_goods').addClass('active');
		$(".page-title").html('Goods');
	}
	else if($("#material_type").val()== "service"){
		$('.nav-pills li').removeClass('active');
		$('#li_service').addClass('active');
		$(".page-title").html('Service');
		$("#id_material-is_stocked").prop("checked", false);
		$("#id_material-is_service").prop("checked", true);
		$(".for_goods").addClass('hide');
		$('.material-container').css("left","350px");
		$(".service_item_code").find("label").text("Item Code");
		$("#material_bom_modal").find(".modal-title").text("Service Package");
		$("#bill_of_material").find(".service_material_container").text("Service Package containing this Material");
		$(".show-bom-icon ").attr("data-tooltip", "tooltip").attr("title", "Service Package");
		$(".service_package_profile").text("No Service Package profiled yet!");
		$(".service_part_material").text("This Material does not form part of any other Material's Service Package!");
		$("#supplierPriceBom").find(".modal-title").text("Service Package Costing");
		$("#supplierPriceBom").find(".goodsData").addClass("hide");
		$("#supplierPriceBom").find(".supplierForGoods").text("SUPPLIER");
		$("#supplierPriceBomMaterial").find("a").attr('data-original-title', 'Service Package Costing').attr('title', '');
		$(".show-bom-icon").attr('data-original-title', 'Service Package').attr('title', '');
		$(".service_costing").attr('data-original-title', 'Service Package Costing');
		$("#importbutton").attr('data-original-title', 'Upload Bulk Service Package');
		$(".export_bom_list").attr('data-original-title', 'Download Service Package List as CSV');
		$("#importBom").find(".service_import_file").text("Please Select the File for Import Service Package");
		$("#importBom").find(".modal-title").text("Import Service Package");
		$("#importBom").find(".warning_material_text").text("Warning: Existing service/item code package will be replaced by the materials/item code from the document!");
		$("#importBom").find(".warning_qty_text").text("Warning: service/item code package will be skipped if the quantity is zero!");
		$("#importBom").find(".drawing_number").text("ITEM CODE");
		$("#importBom").find("#drawing_no").val("ITEM CODE");
		$("#importBom").find("#materal_box").css({ "text-align": "right","margin-left": "160px"});
		$("#importBom").find("#drawing_box").css({ "text-align": "right","margin-left": "160px"});
		$("#materialtable_bom thead").find(".service_item_code").text("Item Code");
		$("#participating_bom_materialtable thead").find(".service_item_code").text("Item Code");
		$("#cheapest_supplier_bom thead").find(".service_item_code").text("Item Code");
		$(".import_csv").attr("data-tooltip", "tooltip").attr("data-original-title","Import bulk service list");
		$("#add_new_material").attr("data-tooltip", "tooltip").attr("data-original-title","Add New Service");
		$(".export_csv").attr("data-tooltip", "tooltip").attr("data-original-title","Download Service list as CSV");
		$("#importmaterial").find(".modal-title").text("Import Service");
		$("#importtable thead").find(".service_item-code").text("Item Code");
		$("#importtable thead").find(".service_sac").text("SAC");
		$(".service_file_import").text("Please Select the File for Import service");
		$(".service_fault").addClass('hide');
		$(".service_sample-csv").attr("href", "/site_media/docs/import_service_sample.csv")
		$(".service_file_import").attr("colspan","5");
		$(".service_download-sample").css("border-right","1px solid #ccc");
		$(".item_stockable").addClass('hide');
		$(".for_service-label").text("Services");
		$("#id_non_stock_in_use_items").prop("checked",true);
	}
}

$("#id_all_items").change(function(){
	$('#id_in_use_items, #id_not_in_use_items').prop('checked', $(this).prop("checked"));
	$(".error-border").removeClass('error-border');
});

$("#id_non_stock_all_items").change(function(){
	$('#id_non_stock_in_use_items, #id_non_stock_not_in_use_items').prop('checked', $(this).prop("checked"));
	$(".error-border").removeClass('error-border');
});

function selectAllStockable(){
	if ($('.select_item_stocks:checked').length == $('.select_item_stocks').length) {
		$('#id_all_items').prop('checked', true);
	} else {
		$('#id_all_items').prop('checked', false);
	}
}

function selectAllNonStockable(){
	if ($('.select_item_non-stocks:checked').length == $('.select_item_non-stocks').length) {
		$('#id_non_stock_all_items').prop('checked', true);
	} else {
		$('#id_non_stock_all_items').prop('checked', false);
	}
}

function selectFilter(){
	$(".error-border").removeClass('error-border');
	$(".custom-error-message").remove();
	if ($('.select_stockable:visible:checked').length ==  0){
		var ControlCollections = [
			{
				controltype: 'textbox',
				controlid: 'filter_stock_notstock',
				isrequired: true,
				errormsg: 'Select at least one criteria.',
				margintop: '-52px',
			}
		];
		var result = JSCustomValidator.JSvalidate(ControlCollections);
	}
	else{
		populateMaterials();
	}

}
$('#expandAllCheckbox').click(function() {
    if (this.checked) {
        const quantityValue = $('#id_bill_material-__prefix__-quantity').val();
        const catItemQty = quantityValue.trim() === '' ? 1 : quantityValue;
        $("#loading").show();
        $.ajax({
            url: '/erp/masters/json/extract_catalogue/',
            type: "POST",
            dataType: "json",
            data: {
                "cat_code": $('#id_material-material_id').val(),
                "cat_item_qty": catItemQty,
                "depth": 2,
                "enterprise_id": $('#enterprise_id').val()
            },
            success: function(data) {
                $("#loading").hide();
                const tableBody = $('#cheapest_supplier_bom tbody');
                tableBody.empty();
                let serialNumber = 1;
                for (const key in data.materials) {
                    if (data.materials.hasOwnProperty(key)) {
                        const item = data.materials[key];
                        var level = 0;
                        const row = createHTMLRow(item, level, serialNumber);
                        tableBody.append(row);
                        if (item.hasChildren) {
                            level++;
                            addSubChildMaterials(item.sub_child_materials, tableBody, serialNumber - 1, serialNumber, level);
                        }
                    }
                    serialNumber++;
                }
            }
        });
    } else {
        showCheapestSupplierBom(this);
    }
});

function createHTMLRow(item, level = 0, serialNumber) {
    const row = document.createElement('tr');
    row.setAttribute('data-currency', item.currency_name || '-na-');
    row.setAttribute('data-currency-code', item.currency_code || 'null');
    row.setAttribute('data-toggle', 'open');

    let dataParent = "";
    let dataChild = "";
    if (item.hasChildren) {
        dataParent = `${item.item_id}_${sessionStorage.clickcount}`;
        dataChild = `${item.item_id}_${sessionStorage.clickcount}`
    }
    row.setAttribute('data-parent', dataParent);
    row.setAttribute('data-child', dataChild);
    row.setAttribute('id', `${item.item_id}_${sessionStorage.clickcount}`);
    let paddingLeft = 10 + (level * 30);
    row.setAttribute('data-padding', paddingLeft);
    let childArrow;
    if (item.hasChildren) {
        childArrow = `<i class='fa fa-minus fa-for-arrow' role='button' onclick='return appendgetCheapestMaterialListBOM("${item.item_id}", "${sessionStorage.clickcount}", "${item.item_id}_${sessionStorage.clickcount}")'><a style='padding-left: 26px; display: block; margin-top: -12px;'> ${item.name}</a></i>`;
    } else {
        let treeViewClass = "";
        let paddingStyle =""
        const serialParts = serialNumber.toString().split('.');
        if (serialParts.length > 1) {
            treeViewClass = "tree-view";
            paddingStyle= "padding-left:25px"
        }
        childArrow = `<span style='${paddingStyle}' class='${treeViewClass}'> ${item.name} </span>`;
    }

    row.innerHTML = `
        <td class="text-left bom-sno">${serialNumber}</td>
        <td align="left" style='padding-left:${Number(paddingLeft - 20)}px'>
            ${childArrow}
        </td>
        <td align="left">${item.drawing_no}</td>
        <td align="left">${item.unit_name}</td>
        <td align="right" class="item-qty">${item.qty}</td>
        <td align="left">${item.party_name}</td>
        <td align='right' class='item-price'>${item.store_price == '-NA-' ? '-NA-' : `${item.store_price.toFixed(2)}<br/><span style="font-size:9px">(INR)</span>`}</td>
		<td align='right' class='goodsData'>${item.purchase_Material_Price == '-NA-' ? '-NA-' : `${item.purchase_Material_Price.toFixed(2)}<br/><span style="font-size:9px">(${item.purchase_currency_code})</span>`}</td>
		<td align='right' class='goodsData'>${item.approved_price == '-NA-' ? '-NA-' :`${item.approved_price.toFixed(2)}<br/><span style="font-size:9px">(${item.approved_currency_code})</span>`}</td>
		<td align='right' class='item-total'>${item.store_price == '-NA-' ? '-NA-' : (item.qty * item.store_price).toFixed(2)}</td>
		<td align='right' class='goodsData'>${item.purchase_Material_Price == '-NA-' ? '-NA-' : (item.qty * item.purchase_Material_Price).toFixed(2)}</td>
		<td align='right' class='goodsData'>${item.approved_price == '-NA-' ? '-NA-' : (item.qty * item.approved_price).toFixed(2)}</td>
    `;

    return row;
}

function addSubChildMaterials(subMaterials, tableBody, parentSerialNumber, parentSerialPrefix, level) {
    var i = 1;
    for (const subKey in subMaterials) {
        if (subMaterials.hasOwnProperty(subKey)) {
            const subItem = subMaterials[subKey];
            const currentSerial = `${parentSerialPrefix}.${i}`;
            const subRow = createHTMLRow(subItem, level, currentSerial);
            tableBody.append(subRow);
            const nextLevel = level + 1;
            if (subItem.hasChildren) {
                addSubChildMaterials(subItem.sub_child_materials, tableBody, parentSerialNumber, currentSerial, nextLevel);
            }
            i++;
        }
    }
    materialSelected();
}

</script>

{% endblock %}