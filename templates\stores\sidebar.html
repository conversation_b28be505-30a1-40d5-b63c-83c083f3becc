{% extends "template.html" %}
{% block sidebar %}

{% if logged_in_user|canView:'STORES' %}
	{% block issue %}{% endblock %}
	{% block storesHome %}{% endblock %}
	{% block indent %}{% endblock %}
	{% block grn %}{% endblock %}
	{% block stockreport %}{% endblock %}
	{% block receipt_documents %}{% endblock %}
	{% block material_history %}{% endblock %}
	{% block shortage_list %}{% endblock %}
	{% block shortage_list_pp %}{% endblock %}
	{% block sales %}{% endblock %}
	{% block jobinreport %}{% endblock %}
	{% block joboutreport %}{% endblock %}
	{% block workorder %}{% endblock %}
{% elif logged_in_user|canView:'PURCHASE INDENT' %}
    {% block po_indent %}{% endblock %}
{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
<script>
		$(".slide_container_part").removeClass('selected');
		if ($("title").text().trim() == "Sales Return") {
			$("#menu_sales").addClass('selected');
			} else {
			$("#menu_stores").addClass('selected');
		}
		$(".slide_container_part").removeClass('selected');
		if ($("title").text().trim() == "Stores") {
			$("#menu_stores").addClass('selected');
			} else {
			$("#menu_production_planning").addClass('selected');
		}
		if ($("title").text().trim() == "Purchase Indent") {
			$('#li_indent').addClass('active');
		}
		$(".slide_container_part").removeClass('selected');
		if ($("title").text().trim() == "Internal Receipt" || $("title").text().trim() == "Shortage Calculator" || $("title").text().trim() == "Job In Stock Register" || $("title").text().trim() == "Job Out Stock Register") {
			$("#menu_production_planning").addClass('selected');
		}
		if ($("title").text().trim() == "Purchase Indent") {
			$("#menu_purchase").addClass('selected');
		}
</script>
{% endblock %}
