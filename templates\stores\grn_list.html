{% extends "stores/sidebar.html" %}
{% block grn %}
{% if access_level.view %}
<script type="text/javascript" src="/site_media/js/grn_list.js?v={{ current_version }}"></script>

<style type="text/css">
	.item-reference-docs-ellipsis {
		max-width: 100px;
	    overflow: hidden;
	    text-overflow: ellipsis;
	    white-space: nowrap;
	    cursor: pointer;
	}
</style>
<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">{{ template_title }}</span>
	</div>

	<div class="container-fluid">
		<div class="row">
			<div class="col-lg-12">
				<div class="content_bg">
					<div class="view_table add_table">
						<div class="col-lg-12">
							{% if template_title == "Sales Return" %}
								{% if logged_in_user|canEdit:'SALES' %}
									<div class="page-heading_new">
										<a href="{{ edit_link }}" id="id-edit_link" class="btn btn-new-item pull-right create_grn" data-tooltip="tooltip" title="Create New Sales Return" ><i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span></a>
									</div>
								{% else %}
									<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in Sales Module. Please contact the administrator.">
										<a class="btn btn-new-item pull-right create_grn disabled"><i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span></a>
									</div>
								{% endif %}
							{% else %}
								{% if logged_in_user|canEdit:'STORES' %}
									<div class="page-heading_new">
										<a href="{{ edit_link }}" id="id-edit_link" class="btn btn-new-item pull-right create_grn" data-tooltip="tooltip" title="Create New Receipt" ><i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span></a>
									</div>
								{% else %}
									<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in Stores Module. Please contact the administrator.">
										<a class="btn btn-new-item pull-right create_grn disabled"><i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span></a>
									</div>
								{% endif %}
							{% endif %}
							<div class="filter-components">
								<div class="filter-components-container">
									<div class="dropdown">
										<button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
											<i class="fa fa-filter"></i>
										</button>
										<span class="dropdown-menu arrow_box arrow_box_filter">
											<div class="col-sm-12 form-group" >
												<label>Date Range</label>
												<div id="reportrange" class="report-range form-control">
													<i class="glyphicon glyphicon-calendar"></i>&nbsp;
													<span></span> <b class="caret"></b>
													<input type="hidden" class="fromdate" id="fromdate" name="fromdate" value="{{ since }}" />
													<input type="hidden" class="todate" id="todate" name="todate" value="{{ till }}" />
													<input type="hidden" class="badge_hit" id="badge_hit" name="badge_hit" value="{{ badge_hit }}"/>
													<input type="hidden" class="status" id="status" name="status" value="{{ status }}" />
												</div>
											</div>
											<div class="col-sm-12 form-group">
												{% if template_title == "Sales Return" %}
												<div class="hide">
													<select class="form-control" name="select" id="rec_against">
			                                            <option value="Sales Return" >Sales Return</option>
												    </select>
												</div>
												{% else %}
												{% if template_title == "Internal Receipt" %}
													<div class="hide">
														<select class="form-control" name="select" id="rec_against">
			                                                <option value="Issues" >Issues</option>
													    </select>
													</div>
												{% else %}
													<div>
														<label>Received Against</label>
														<select class="form-control" name="select" id="rec_against">
							                                <option value="Purchase Order" >Purchase</option>
															<option value="Job Work" >Job Out</option>
															<option value="Job In">Job In</option>
															<option value="Delivery Challan" >Delivery Challan</option>
															<option value="Others" >Others</option>
															<option value="All" >All</option>
						                                </select>
													</div>
												{% endif %}
											{% endif %}
											</div>
											<div class="filter-footer">
												<input type="submit" class="btn btn-save" value="Apply" id="loadgrns"/>
					      					</div>
										</span>
									</div>
									<span class='filtered-condition filtered-date'>Date: <b></b></span>
									{% if template_title == "Goods Receipt" %}
										<span class='filtered-condition filtered-receivedAgainst'>Received Against: <b></b></span>
									{% endif %}
								</div>
							</div>
						</div>
					</div>
					<div class="clearfix"></div>
					<div class="search_result_table">
						<div class="col-lg-12" id="grn_search_result">
							<div class="csv_export_button">
                                <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#grnList'), '{{ template_title }}.csv']);" data-tooltip="tooltip" title="Download Receipt List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
                            </div>
							<table class="table table-bordered custom-table table-striped" id="grnList" style="width: 100%;">
								<thead>
									<tr>
										 <th class="for-all"> S. No </th>
										 <th class="for-all"> Date </th>
										 <th hidden="hidden" class="exclude_export"> Draft No </th>
										 <th class="for-all"> Receipt No </th>
										 <th hidden="hidden" class="exclude_export" class="for-grn for-ir for-sr" class style="width: 120px;"> Ref Doc NO </th>
										 <th class="for-all"> Project</th>
										 <th class="for-all"> Received From </th>
										 <th class="for-grn for-sr"> DC/Inv No </th>
										 <th class="for-grn for-sr"> DC/Inv Date </th>
										 <th class="for-grn for-sr"> Inv Value</th>
										 <th style="width: 300px;" class="for-others for-ir"> Material </th>
										 <th class="for-others for-ir"> Qty </th>
										 <th class="for-all"> Status </th>
									</tr>
								</thead>
								<tbody></tbody>
							</table>
							<div id='loadingmessage' class="text-center hide" >
		                        <img src='/site_media/images/loading_spinner.gif' style="width:75px"/>
		                        <br/> Please wait...
					         </div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="hide">
		<form id="id-edit_receipt_form" method="POST" action="{{edit_link}}">{% csrf_token %}
			<input type="hidden" name="receipt_no" id="id-edit_receipt_no" value="" />
		</form>
	</div>
</div>

{% include "stores/grn_document.html" %}
<!-- /#wrapper -->
<script>
var oTable;
var oSettings;
$(window).load(function() {
	loadGrnList();
	$('.nav-pills li').removeClass('active');
	if ($("title").text().trim() == "Internal Receipt") {
		$("#li_irn").addClass('active');
	} else if ($("title").text().trim() == "Sales Return") {
		$("#li_sr").addClass('active');
	} else {
		$("#li_receipt").addClass('active');
	}
	updateFilterText();
});

function updateFilterText() {
	$(".filtered-date b").text($("#reportrange").find("span").text());
	$(".filtered-receivedAgainst b").text($("#rec_against option:selected").text());
}

$(document).ready(function() {
	{% if received_against %}
		$("#rec_against").val("{{ received_against }}");
		$("#rec_against").trigger("chosen:updated");
	{% endif %}
	initListPage();
});
</script>
{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
{% endblock %}