# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from datetime import datetime

from django.test import TestCase

from erp.accounts import logger
from erp.accounts.backend import AccountService
from settings import SQLASession


class AccountsTestCase(TestCase):
	def setUp(self):
		self.db_session = SQLASession()

	def test_dashboard(self):
		fund, ledgers = AccountService().cashBalance(
			enterprise_id=102, since=datetime.strptime('2018-01-01 00:00:00', '%Y-%m-%d %H:%M:%S'),
			till=datetime.strptime('2019-12-01 00:00:00', '%Y-%m-%d %H:%M:%S'))
		logger.debug("Dashboard entries %s from %s ledgers" % (fund, len(ledgers)))
		self.assertEquals(fund, 200)
