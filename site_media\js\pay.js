var initializePage = function () {
	transformToDummyForm('pay_structure');
	$('#save_pay_button').click(function () {
		$(".error-border").removeClass('error-border');
		$(".custom-error-message").remove();

		var ControlCollections = [{
				controltype: 'textbox',
				controlid: 'id_pay-description',
				isrequired: true,
				errormsg: 'Pay Structure is required.'
			}];
		if (JSCustomValidator.JSvalidate(ControlCollections)) {
            if ($('#id_pay-description').is(':disabled')) {
                SavePayButton();
                return;
            } else {
                $.ajax({
                    url: "/erp/hr/json/pay/available_description/",
                    type: "POST",
                    dataType: "json",
                    data: {description: $("#id_pay-description").val().trim()},
                    success: function (response) {
                         if (response.response_message == "Success") {
                            if(response.is_valid) {
                                SavePayButton();
                            } else {
                                swal("", "This Pay Structure name already exist.", "warning");
                            }
                            $("#id_pay-description").focus();
                        } else {
                             swal("", response.custom_message, "error");
                        }
                    },
                    error: function (xhr, errmsg, err) {
                        console.log(xhr.status + ": " + xhr.responseText);
                    }
                });
            }
		}
	});

	$('#add_new_pay_details').click(function () {
	    $(".error-border").removeClass('error-border');
	    $(".custom-error-message").remove();

	    var ControlCollections = [
	        {
	            controltype: 'dropdown',
	            controlid: 'id_pay_structure-__prefix__-type',
	            isrequired: true,
	            errormsg: 'Item Type is required.'
	        },
	        {
	            controltype: 'textbox',
	            controlid: 'id_pay_structure-__prefix__-description',
	            isrequired: true,
	            errormsg: 'Description is required.'
	        }
	    ];

	    if (JSCustomValidator.JSvalidate(ControlCollections)) {
            $("#add_new_pay_details").attr("disabled", "disabled");
            setTimeout(function(){
    	        var itemType = $("#id_pay_structure-__prefix__-type").val();
                onNewPayStructureItem(itemType=itemType, descriptionElement=$("#id_pay_structure-__prefix__-description"), callback=function() {
                    addPayStructureItem();
                });
            },10)
	    }
	});

    $('#indMaterialForms').ready(function () {
        var initialFormCount = parseInt(document.getElementById('id_pay_structure-TOTAL_FORMS').value);
        for (i = 0; i < initialFormCount; i++) {
            $(`#id_pay_structure-${i}-s_no`).html(`${parseInt(i+1)}.`);
        }
    });

	if (window.location.href.indexOf('edit') > 0) {
		$("#payStructure_add").click();
	}
}

function onChangePayStructureItem (descriptionElement) {
    var itemType = $("#id_pay_structure-__prefix__-type").val();
    if ($(descriptionElement).attr("id").indexOf("__prefix__") == -1) {
        itemType = $(descriptionElement).closest("tr").find(".pay_type").text();
    }
    onNewPayStructureItem(itemType=itemType, descriptionElement=descriptionElement);
}

function checkUniqueDescription() {
    $(".inline-error-border").removeClass("inline-error-border");
    $(".inline-error-message").remove();
    $(".external-error-border").removeClass("external-error-border");
    $(".external-error-message").remove();
    $("#pay_details").find("tbody").find("tr:visible").each(function() {
        if($(this).attr("id") != "pay_structure-__dummy__") {
            var currentElement = $(this).find(".pay_desc").find("input");
            var currentDescription = $(currentElement).val().toLowerCase().trim();
            $("#pay_details").find("tbody").find("tr:visible").each(function() {
                if($(this).attr("id") != "pay_structure-__dummy__" && $(this).attr("id") != $(currentElement).closest("tr").attr("id")) {
                    if(currentDescription == $(this).find(".pay_desc").find("input").val().toLowerCase().trim()) {
                        $(currentElement).addClass('inline-error-border').after('<span class="inline-error-message">This Pay Structure description already exist.</span>');
                    }
                }
            });
        }
    });
}
function onNewPayStructureItem (itemType="", descriptionElement=null, callback=null) {
    var description = $(descriptionElement).val().toLowerCase();
    checkUniqueDescription();
    $.ajax({
        url: "/erp/hr/json/pay/validate_item/",
        type: "POST",
        dataType: "json",
        data: {item_type: itemType, description: description},
        success: function (response) {
             if (response.response_message == "Success") {
                if (response.is_valid) {
                    if (callback != null) {
                        callback();
                    }
                    $(descriptionElement).removeClass('external-error-border').next(".external-error-message").remove();
                } else {
                    $(descriptionElement).removeClass('external-error-border').next(".external-error-message").remove();
                    $(descriptionElement).addClass('external-error-border').after('<span class="external-error-message">Given description is already saved with different item type in other pay structure.</span>');
                }
                //$(descriptionElement).focus();
            } else {
                swal("", response.custom_message, "error");
            }
            $("#add_new_pay_details").removeAttr("disabled");
        },
        error: function (xhr, errmsg, err) {
            console.log(xhr.status + ": " + xhr.responseText);
            $("#add_new_pay_details").removeAttr("disabled");
        }
    });
}

function addPayStructureItem() {
    refreshSessionPerNActions(10);
    if ($("#id_pay_structure-__prefix__-type").val() != "") {
        var addable = true;
        var description = $('#id_pay_structure-__prefix__-description').val();
        $("#pay_details").find("tbody").find("tr:visible").each(function() {
            if($(this).attr("id") != "pay_structure-__dummy__") {
                var currentElement = $(this).find(".pay_desc").find("input");
                var currentDescription = $(currentElement).val().toLowerCase().trim();
                if(currentDescription == description) {
                    $('#id_pay_structure-__prefix__-description').addClass('inline-error-border').after('<span class="inline-error-message">This Pay Structure description already exist.</span>');
                    addable = false;
                }
             }
        });
        if (addable) {
            var paytable = document.getElementById("pay_details");
            var payrowCount = paytable.rows.length;
            var match = false;
            generateFormsetFormRowBefore('pay_structure');
            var index = parseInt(parseInt($('#id_pay_structure-TOTAL_FORMS').val()) - 1);
            var item_label = document.getElementById('id_pay_structure-' + index + '-itemLabel');
            var s_no = document.getElementById("id_pay_structure-" + index + "-s_no");
            copyFromEmptyForm('pay_structure', index, 'pay-id', 'pay_structure_id');
            item_label.innerHTML = $('#id_pay_structure-__prefix__-type option:selected').text();
            s_no.innerHTML = index + 1;
            $('#id_pay_structure-__prefix__-type').val('None');
        }
    }
}

function SavePayButton() {
    $("#loading").show();
    checkUniqueDescription();
    setTimeout(function() {
        if($("#pay_details").find(".inline-error-message").length <= 0 && $("#pay_details").find(".external-error-message").length <= 0) {
            var form_count = parseInt(document.getElementById('id_pay_structure-TOTAL_FORMS').value);
            var item_list = 0;
            for (i=0; i<form_count; i++) {
                if(document.getElementById('id_pay_structure-'+i+'-pay_structure_id').value == '')
                    document.getElementById('id_pay_structure-'+i+'-pay_structure_id').value=document.getElementById('id_pay-id').value;
            }

            for(i=0; i<form_count; i++) {
                if(!document.getElementById("id_pay_structure-"+i+"-DELETE").checked){
                    item_list = item_list + 1;
                }
            }

            if(item_list == 0) {
                swal('WARNING', 'Please add at-least one particulars', 'warning');
            } else {
                $("#save_pay_button").text("Processing...").addClass("btn-processing");
                clickButton('savePay');
                var event_action = $('#id_pay-description').is(':disabled') ? "Create" : "Update";
                ga('send', 'event', 'Pay Structure', event_action, $('#enterprise_label').val(), 1);
            }
        }
        else {
            swal("","Few duplicate description appears in this Pay Structure.<br /> Please fix them before saving.", "warning")
        }
        $("#loading").hide();
    }, 2000);
    
}

function copyFromEmptyForm(form_prefix, form_idx, fk_field_name, new_form_fk_field_name) {
    var new_form_fk_field = document.getElementById('id_' + form_prefix + '-' + form_idx + '-' + new_form_fk_field_name);
    var new_form_enterprise_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-enterprise_id');
    var new_form_description = document.getElementById('id_' + form_prefix + '-' + form_idx + '-description');
    var new_form_type = document.getElementById('id_' + form_prefix + '-' + form_idx + '-type');

    new_form_fk_field.value = document.getElementById('id_' + fk_field_name).value;
    new_form_enterprise_id.value = document.getElementById('id_' + form_prefix + '-__prefix__-enterprise_id').value;
    new_form_description.value = document.getElementById('id_' + form_prefix + '-__prefix__-description').value;
    new_form_type.value = document.getElementById('id_' + form_prefix + '-__prefix__-type').value;

    document.getElementById('id_' + form_prefix + '-__prefix__-description').value = '';
}

function deletePayItem(payItemFormID) {
    confirmAction(message="Do you want to delete Item!", callback=function(isConfirm) {
        if (isConfirm) {
            var deleteFlag = document.getElementById('id_' + payItemFormID + '-DELETE');
            var deleteRow = document.getElementById(payItemFormID);
            deleteFlag.checked = true;
            deleteRow.style.display = 'none';
            checkUniqueDescription();
        }
    });
}

function deletePayStructureConfirm(payCode) {
	$.ajax({
		url: "/erp/hr/json/pay/check/",
		type: "post",
		datatype: "json",
		data: {pay_structure_id: payCode},
		success: function (response) {
			if (response == 0) {
                confirmAction(message="Do you want to delete this Pay Structure?", callback=function(isConfirm) {
                    if(isConfirm) {
                        deletePayStrucuteRow(payCode);
                    }
                });
			} else {
			    swal("WARNING", "You can't delete this Pay Structure because it has been used in employee's profile.", "warning");
            }
		}
	});
}