import logging
from logging import handlers

__author__ = 'ka<PERSON>vanan'

logger = logging.getLogger(__name__)

PARENT_ENTERPRISE_ID = 102
PROJECT_CODE = 4550
VOUCHER_DATE = "2024-03-29"

PROJECT_CLONE_STATUS = 1

# Voucher Automation mapping
CASH_OR_BANK = {2: 20, 3: 20}
LEDGER_GROUP_MAPPING = {2: 20, 3: 20, 4: 7, 5: 8, 6: 7}

VALID_GROUP_NAMES = ["Direct Expenses", "Indirect Expenses", "Bank Account", "Cash in Hand",
                     "Direct Income", "Indirect Incomes"]

VALID_BUDGET_GROUP_NAMES = ["Direct Expenses", "Indirect Expenses", "Direct Income", "Indirect Incomes"]

# Voucher clone Logs
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log_path = "/voucher_clone.log"
LOG_HANDLER = handlers.TimedRotatingFileHandler(log_path, when='midnight', interval=1, backupCount=100)
LOG_HANDLER.setFormatter(formatter)
