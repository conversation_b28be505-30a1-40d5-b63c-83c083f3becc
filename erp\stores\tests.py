"""
"""
import unittest
from unittest import TestCase
import datetime

from erp.dao import DataAccessObject
from erp.models import StockTransfer
from erp.stores.backend import StoresService

__author__ = 'nandha'

from erp.stores.document_compiler import StockTransferPDFGenerator

from util.helper import getFormattedDocPath, writeFile


class StoreTest(TestCase):
	def __init__(self, *args, **kwargs):
		super(StoreTest, self).__init__(*args, **kwargs)
		self.store_service = StoresService()

	def testLoadingPendingJobOaNumbers(self):
		oa_list = self.store_service.loadPendingJobOaNumbers(enterprise_id=None, party_id=None, receipt_no=None)
		self.assertEquals(oa_list, [])

	def testgetReceipt(self):
		receipt_object = self.store_service.stores_dao.getReceipt(enterprise_id=None, receipt_no= None)
		self.assertEquals(receipt_object, None)

	def test1getReceipt(self):
		receipt_object = self.store_service.stores_dao.getReceipt(enterprise_id=None, receipt_no=4216)
		self.assertEquals(receipt_object, None)

	def test2getReceipt(self):
		receipt_object = self.store_service.stores_dao.getReceipt(enterprise_id=102, receipt_no=None)
		self.assertEquals(receipt_object, None)

	def test3getReceipt(self):
		receipt_object = self.store_service.stores_dao.getReceipt(enterprise_id=102, receipt_no=4216)
		self.assertEquals(receipt_object, self.store_service.stores_dao.getReceipt())

	def testgetIndent(self):
		indent_list = self.store_service.stores_dao.getIndent(indent_no=None, enterprise_id=None)
		self.assertEquals(indent_list, None)

	def test1getIndent(self):
		indent_list = self.store_service.stores_dao.getIndent(indent_no=12, enterprise_id=None)
		self.assertEquals(indent_list, None)

	def test2getIndent(self):
		indent_list = self.store_service.stores_dao.getIndent(indent_no= None, enterprise_id= 101)
		self.assertEquals(indent_list, None)

	def test3getIndent(self):
		indent_list = self.store_service.stores_dao.getIndent(indent_no=12, enterprise_id=101)
		self.assertEquals(indent_list, self.store_service.stores_dao.getIndent())

	def testgetIndentMaterial(self):
		intendmaterial_list = self.store_service.stores_dao.getIndentMaterial(indent_no=None, item_id=None, enterprise_id=None, make_id=None)
		self.assertEquals(intendmaterial_list, None)

	def test1getIndentMaterial(self):
		intendmaterial_list = self.store_service.stores_dao.getIndentMaterial(indent_no=477, item_id=None, enterprise_id=None, make_id=None)
		self.assertEquals(intendmaterial_list, None)

	def test2getIndentMaterial(self):
		intendmaterial_list = self.store_service.stores_dao.getIndentMaterial(indent_no=None, item_id=875, enterprise_id=None, make_id=None)
		self.assertEquals(intendmaterial_list, None)

	def test3getIndentMaterial(self):
		intendmaterial_list = self.store_service.stores_dao.getIndentMaterial(indent_no=None, item_id=None, enterprise_id=102, make_id=None)
		self.assertEquals(intendmaterial_list, None)

	def test4getIndentMaterial(self):
		intendmaterial_list = self.store_service.stores_dao.getIndentMaterial(indent_no=None, item_id=None, enterprise_id=None, make_id=1)
		self.assertEquals(intendmaterial_list, None)

	def test5getIndentMaterial(self):
		intendmaterial_list = self.store_service.stores_dao.getIndentMaterial(indent_no=477, item_id=875, enterprise_id=102, make_id=1)
		self.assertEquals(intendmaterial_list.items, self.store_service.stores_dao.getIndentMaterial())

	def testgetPartyDetails(self):
		partydetails_list = self.store_service.stores_dao.getPartyDetails(enterprise_id=None)
		self.assertEquals(partydetails_list, None)

	def test1getPartyDetails(self):
		partydetails_list = self.store_service.stores_dao.getPartyDetails(enterprise_id=102)
		self.assertEquals(partydetails_list, self.store_service.stores_dao.getPartyDetails())

	def test1getALLReceipts(self):
		receipts = self.store_service.getAllReceipts(
			received_against='ALL', enterprise_id='102', from_date=datetime.datetime(2020, 10, 29, 0, 0),
			to_date=datetime.datetime(2020, 11, 27, 23, 59, 59), status=None, limit=500, offset=0)
		self.assertNotEquals(receipts, None)

class test_StoresService_get_nms (unittest.TestCase):
	def test01_getnms_stquery(self):
		obj = StoresService()
		result = obj.getnmsStockQuery(enterprise_id=102, days= 90)





class TestStockTransfer(unittest.TestCase):

	def setUp(self):
		self.dao = DataAccessObject()

	def test_tc(self):
		db_session = self.dao.db_session
		_st = db_session.query(StockTransfer).filter(StockTransfer.transfer_id == 12).first()
		print(_st.getCode())
		temp_doc_path = getFormattedDocPath(code="receipt_code", id=_st.transfer_id)
		_document_generator = StockTransferPDFGenerator(target_file_path=temp_doc_path)
		document_pdf = _document_generator.generatePDF(_st)
		# print(document_pdf)
		writeFile(document_pdf, temp_doc_path)



