{% extends "purchase/sidebar.html" %}
{% block purchaseHome %}

<script>
var myCalendar;
	function doOnLoad() {
		myCalendar = new dhtmlXCalendarObject(["calendar","calendar1"]);
		myCalendar.setDateFormat("%d/%m/%Y");
	}
</script>
<script type="text/javascript" src="/site_media/js/jquery.easyResponsiveTabs.js?v={{ current_version }}"></script>

<link type="text/css" rel="stylesheet" href="/site_media/css/account.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/dashboard.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/Fonts.css?v={{ current_version }}">

<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/jquery.slimscroll.js?v={{ current_version }}"></script>

<div class="right-content-container col-sm-12">
    <div class="page-title-container">
        <span class="page-title">Dashboard</span>
    </div>
    <div class="container" style="margin-top: 20px;">
        <div class="header-container">
            <div class="row">
                <div class="col-md-4 form-group" >
                    <div class="header-box header-box-1 form-group">
                        <div class="header-box-value">
                            <i class="fa fa-inr fa-black fa-5" aria-hidden="true"></i>
                            <span id="expense_total">0.00</span>
                        </div>
	                    <span style="font-size: 10px; position: absolute; top: 67px; left: 150px;">(Since <span id="since_field"></span>)</span>
                        <div class="header-box-text">
                            Purchase Expense
                        </div>
                    </div>
                    <div class="header-box header-box-3 form-group" data-toggle="modal" data-target="#delivery_overduel_modal" role="button">
                        <div class="header-box-value">
                            <span id="delivery_overdue_count">0.00</span>
                        </div>
                        <div class="header-box-text">
                            Delivery Overdue
                        </div>
                    </div>
                    <h3>Status</h3>
                    <div class="dashboard-status-box">
                        <table class="table" style="margin-bottom: 0;">
                            <tbody>
                                <tr class="dashboard-status-box-content" role="button">
                                    <form id="indent_pending" method="post" action="/erp/stores/indent_list/loadindents/"> {% csrf_token %}
									<td class='dashboard-status-box-heading'>Indent Pending
										<input type="hidden" id="3_since" value="" name="since"/>
										<input type="hidden" id="3_till" value="" name="till"/>
										<input type="hidden" id="pending" value="pending" name="status"/>
									</td>
									<td align="right"><input type="submit" id="btn_pending_indent" value="  {{ dash_res.2 }} " class="dashboard-status-box-btn" />  </td>
									</form>
                                </tr>
                                <tr class="dashboard-status-box-content" role="button">
									<form id="indent_pending_due_po" method="post" action="/erp/stores/indent_list/loadindents/"> {% csrf_token %}
									<td class='dashboard-status-box-heading' style="padding-left: 58px !important;">Due to P.O
										<input type="hidden" id="4_since" value="" name="since"/>
										<input type="hidden" id="4_till" value="" name="till"/>
										<input type="hidden" id="pending_due_po" value="pending_due_po" name="status"/>
									</td>
									<td align="right"><input type="submit" id="btn_pending_po" value="  {{ dash_res.3 }} " class="dashboard-status-box-btn" />  </td>
									</form>
								</tr>
								<tr class="dashboard-status-box-content" role="button">
									<form id="indent_pending_due_material" method="post" action="/erp/stores/indent_list/loadindents/"> {% csrf_token %}
									<td class='dashboard-status-box-heading' style="padding-left: 58px !important;">Due to Material
										<input type="hidden" id="5_since" value="" name="since"/>
										<input type="hidden" id="5_till" value="" name="till"/>
										<input type="hidden" id="pending_due_material" value="pending_due_material" name="status"/>
									</td>
									<td align="right"><input type="submit" id="btn_pending_material" value="  {{ dash_res.4 }} " class="dashboard-status-box-btn" />  </td>
									</form>
								</tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col-sm-8">
                    <h3 style="margin-top: 0;">PO due to be received Today</h3>
                    <div class="po-scroll-container">
                        <table class="table table-bordered custom-table dashboard_table_blue">
                            <thead>
                                <tr>
                                    <th>PO Code</th>
                                    <th>Party</th>
                                    <th>Value</th>
                                </tr>
                            </thead>
                            <tbody id="po_received_today">

                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="3" class="text-right"><a href="/erp/purchase/po/reports/" target="_blank">Goto Report</a></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="clearfix"></div>                
                <div class="col-sm-5">
                    <h3>On-time Supplies</h3>
                    <div id="supplier_chart"></div>
                </div>
                <div class="col-sm-7">
                    <h3>Bad Suppliers</h3>
                    <table class="table table-bordered custom-table dashboard_table_red">
                        <thead>
                            <tr>
                                <th>Party</th>
                                <th>Purchase Value</th>
                                <th>Rejection Value</th>
                                <th>Rejection %</th>
                            </tr>
                        </thead>
                        <tbody id="po_bad_suppliers">

                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="4" class="text-right"><a data-toggle="modal" data-target="#bad_supplier_modal" role="button" onclick="badSuppliersData()">Show All</a></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                <div class="col-sm-12">
                    <h3>Items Received Recently</h3>
                    <table class="table table-bordered custom-table dashboard_table_blue">
                        <thead>
                            <tr>
                                <th>Material</th>
                                <th>Quantity</th>
	                            <th>Unit</th>
                                <th style="min-width: 150px;">GRN Code</th>
                                <th>Gate Inward Date</th>
                                <th>PO Code</th>
                                <th>Party</th>
                            </tr>
                        </thead>
                        <tbody id="po_items_received">
                            <tfoot>
                            <tr>
                                <td colspan="7" class="text-right"><a href="/erp/stores/grn-report-statement/" target="_blank">Goto Report</a></td>
                            </tr>
                        </tfoot>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="bad_supplier_modal" class="modal fade" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Bad suppliers</h4>
            </div>
            <div class="modal-body">
                <table class="table table-bordered custom-table dashboard_table_red">
                    <thead>
                        <tr>
                            <th style="width: 50%;">Party</th>
                            <th>Purchase Value</th>
                            <th>Rejection Value</th>
                            <th>Rejection %</th>
                        </tr>
                    </thead>
                    <tbody id="po_bad_suppliers_data">
                    </tbody>
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="delivery_overduel_modal" class="modal fade" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Delivery Overdue</h4>
            </div>
            <div class="modal-body">
               <div class="po_delivery_overdue_scroll_container">
                        <table class="table table-bordered custom-table dashboard_table_green">
                            <thead>
                                <tr><th>S.No</th>
                                    <th>PO Code</th>
                                    <th>Party</th>
                                    <th>Del. Due Date</th>
                                    <th>Value</th>
                                </tr>
                            </thead>
                            <tbody id="po_delivery_overdue">
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="4" class="text-right"><a href="/erp/purchase/po/reports/" target="_blank">Goto Report</a></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
$(document).ready(function () {
	$('#table_tab').easyResponsiveTabs({
        type: 'default', //Types: default, vertical, accordion
        width: 'auto', //auto or any width like 600px
        fit: true, // 100% fit in a container
        closed: 'accordion', // Start closed if in accordion view
        tabidentify: 'hor_1', // The tab groups identifier
        activate: function (event) { // Callback function if tab is switched
            var $tab = $(this);
            var $info = $('#nested-tabInfo');
            var $name = $('span', $info);

            $name.text($tab.text());

            $info.show();
        }
    });
    $('.nav-pills li').removeClass('active');
	$('#li_po_dashboard').addClass('active');
 });
	
if ($(this).width() <= 767 ) {
	$(document).ready(function () {
		$('#table_tab h2:first').click(); 
 });
}

$(window).on('load', function() {
    $.ajaxSetup({
        headers: {"X-CSRFToken": getCookie('csrftoken')}
    });
    purchaseExpenses();
    purchaseDue();
    recentItemsReceived();
    recentItemsOverdue();
    badSuppliers();
});

function purchaseExpenses(){
	$.ajax({
        url: 'erp/purchase/json/po/get_po_total/',
        type: "POST",
        data: {},
        beforeSend: function () {
            $("#loadingmessage_ie").show();
        },
        success: function (response) {
                $("#expense_total").html(response['total']);
                $("#since_field").html(response['since']);
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(xhr.status + ": " + xhr.responseText);
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });
}

function ontimeSupplies(){
	var res = '';
	$.ajax({
        url: 'erp/purchase/json/po/get_purchase_performance/',
        type: "POST",
        data: {},
        beforeSend: function () {
            $("#loadingmessage_ie").show();
        },
        success: function (response) {
        res = response['performance'];
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(xhr.status + ": " + xhr.responseText);
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });

    return res;
}

function purchaseDue(){
	$.ajax({
        url: 'erp/purchase/json/po/get_po_delivery_due/',
        type: "POST",
        data: {},
        beforeSend: function () {
            $("#loadingmessage_ie").show();
        },
        success: function (response) {
                po_due_html = '';
                if(response.length > 0){
                $.each( response, function( key, value ) {
                po_due_html += '<tr> \
	                               <td class="text-center"><a href="erp/purchase/po/?type=edit&pid='+value['po_id']+'&status='+ value['status'] +'" target="_blank">'+ value['financial_year'] + (value['type'] ? '/JO/' : '/PO/') + value['po_no'] + (value['sub_number'] == null ? '' : value['sub_number']) +'</a></td> \
	                                <td>'+ value['name'] +'</td> \
	                                <td class="text-right">'+ value['total'].toFixed(2) +'</td> \
	                            </tr>';
				});
				}else{
				po_due_html = '<tr> \
	                               <td class="text-center"></a></td> \
	                                <td>No due available for Today.</td> \
	                                <td class="text-right"></td> \
	                            </tr>';
				}
                $("#po_received_today").html(po_due_html);
                $('.po-scroll-container').slimScroll({
                    height: "400px",
                    alwaysVisible: true
                });
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(xhr.status + ": " + xhr.responseText);
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });
}

function recentItemsReceived(){
    $.ajax({
            url: 'erp/purchase/json/po/get_received_items/',
            type: "POST",
            data: {},
            beforeSend: function () {
                $("#loadingmessage_ie").show();
            },
            success: function (response) {
                 po_received_items_html = '';
                if(response.length > 0){
                $.each( response, function( key, value ) {
                make_name = ""
                if (value['make_name'] != "")
                {
                 make_name = ' [' + value['make_name'] +  ']'
                }
                if (value['purchase_order_material_drawing_no']==""){
                    item = value['materials_name'] + make_name;
                }else{
                    item = value['purchase_order_material_drawing_no'] + ' - ' + value['materials_name'] + make_name;
                }
                po_received_items_html += '<tr> \
                                <td> '+ item + ' </td> \
                                <td class="text-center">'+ value['acc_qty'] +'</td> \
                                <td class="text-right">'+ value['unit_master_unit_name'] +'</td> \
                                <td class="text-center">'+ value['grn_no'] +'</td> \
                                <td class="text-center">'+ value['grn_grn_date'] +'</td> \
                                <td class="text-center">'+ value['po_no'] +'</td> \
                                <td>'+ value['party_master_party_name'] +'</td> \
                            </tr>';

				});
				}else{
				po_received_items_html = '<tr> \
	                               <td class="text-center"></a></td> \
	                                <td>No items received.</td> \
	                                <td class="text-right"></td> \
	                            </tr>';
				}
                $("#po_items_received").html(po_received_items_html);
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.log(xhr.status + ": " + xhr.responseText);
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });

}

function recentItemsOverdue(){
    $.ajax({
            url: 'erp/purchase/json/po/get_overdue_items/',
            type: "POST",
            data: {},
            beforeSend: function () {
                $("#loadingmessage_ie").show();
            },
            success: function (response) {
                po_overdue_html = '';
                if(response.length > 0){
                var i = 0;
                $.each( response, function( key, value ) {
                    i++;
                    po_overdue_html += '<tr><td>' + i + '.</td> \
	                               <td class="text-center"><a>'+ value['po_code'] +'</a></td> \
	                                <td>'+ value['supplier'] +'</td> \
	                                <td class="text-center">'+ value['po_due_on'] +'</td> \
	                                <td class="text-right">'+ value['po_value'].toFixed(2) +'</td> \
	                            </tr>';
				});
				}else{
				po_overdue_html = '<tr> \
	                               <td class="text-center"></a></td> \
	                                <td>No overdue available.</td> \
	                                <td class="text-right"></td> \
	                            </tr>';
				}
                $("#po_delivery_overdue").html(po_overdue_html);
                $("#delivery_overdue_count").html(response.length);
                $('.po_delivery_overdue_scroll_container').slimScroll({
                    height: "370px",
                    alwaysVisible: true
                });

            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.log(xhr.status + ": " + xhr.responseText);
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });

}

function badSuppliers(){
    $.ajax({
            url: 'erp/purchase/json/po/get_bad_suppliers/',
            type: "POST",
            data: {},
            beforeSend: function () {
                $("#loadingmessage_ie").show();
            },
            success: function (response) {
                bad_suppliers_html = '';
                bad_suppliers_full_html = '';
                if(response.length > 0){
                $.each( response, function( key, value ) {
                bad_suppliers_html += '<tr> \
                                            <td>'+ value[1] +'</td> \
                                            <td class="text-right">'+ value[2] +'</td> \
                                            <td class="text-right">'+ value[3].toFixed(2) +'</td> \
                                            <td class="text-center">'+ value[4].toFixed(2) +'% </td> \
                                        </tr>';
                 return key < 4;

				});

				$.each( response, function( key, value ) {

				bad_suppliers_full_html += '<tr> \
                                            <td>'+ value[1] +'</td> \
                                            <td class="text-right">'+ value[2] +'</td> \
                                            <td class="text-right">'+ value[3].toFixed(2) +'</td> \
                                            <td class="text-center">'+ value[4].toFixed(2) +'% </td> \
                                        </tr>';


				});

				}else{
				bad_suppliers_html = '<tr> \
                                            <td></td> \
                                            <td class="text-right"></td> \
                                            <td class="text-right">No bad suppliers</td> \
                                            <td class="text-center"></td> \
                                        </tr>';
				}
                $("#po_bad_suppliers").html(bad_suppliers_html);
                $("#po_bad_suppliers_data").html(bad_suppliers_full_html);
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.log(xhr.status + ": " + xhr.responseText);
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });

}

</script>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
  <script type="text/javascript">
    function drawVisualization() {
        // Create and populate the data table.
      var vval = ontimeSupplies();
      var data = google.visualization.arrayToDataTable(vval);

      // Create and draw the visualization.
      new google.visualization.ColumnChart(document.getElementById('supplier_chart')).
          draw(data,
               {title:"",
                width: '100%',
                height: 400,
                'chartArea': {'width': '80%', 'height': '75%'},
                legend: {position: 'top'},
                vAxis: {title: "Received Count"}, isStacked: true,
                hAxis: {title: "Month"}}
          );
    }

    google.load("visualization", "1", {packages:["corechart"]});
    google.setOnLoadCallback(drawVisualization);
  </script>
<div id="columnchart_values" style="width: 900px; height: 300px;"></div>
{% endblock %}