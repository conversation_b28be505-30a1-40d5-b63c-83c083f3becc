"""

"""
import re
from datetime import datetime

from deepdiff import DeepDiff

from erp.accounts import logger
from erp.helper import getUser
from util.api_util import response_code
from util.changelog import ChangeLog

__author__ = 'charlesmichel'


class VoucherChangelog(ChangeLog):
	"""
	Changelog to do mongodb operations
	"""
	voucher = {}
	collection = 'vouchers'
	uid = "voucher_id"

	def __init__(self):
		super(VoucherChangelog, self).__init__(collection=self.collection, uid=self.uid)
		self.response_log = []
		self.voucher_context_set = dict(tags='Tag', type='Type', voucher_code='Voucher number', voucher_date='Date', status='Status', project='Project',
		                         narration='Narration', transaction_instrument_no='Transaction Instrument No',
		                         transaction_description='Transaction Description', particulars='Particulars')
		self.voucher_status = {0: 'drafted', 1: 'approved'}
		logger.info('VoucherChangelog __init__')

	def buildVoucherData(self, user=None, data=None):
		"""

		:param user:
		:param data:
		:return:
		"""
		try:
			self.voucher['tags'] = []
			for tag in data.tags:
				self.voucher['tags'].append(str(tag.tag))
			self.voucher['type'] = data.type.name
			self.voucher['voucher_code'] = data.getCode()
			self.voucher['username'] = [user.first_name, user.last_name]
			self.voucher['modified_at'] = datetime.now()
			self.voucher['voucher_date'] = str(datetime.strptime(str(data.voucher_date.date()), '%Y-%m-%d'))
			self.voucher['project'] = str(data.project.name if data.project is not None else '')
			self.voucher['transaction_instrument_no'] = str(data.transaction_instrument_no)
			self.voucher['transaction_description'] = str(data.transaction_description)
			self.voucher['narration'] = str(data.narration)
			self.voucher['status'] = self.voucher_status[int(data.status)]
			self.voucher['particulars'] = []
			if len(data.particulars) > 0:
				for ledger in data.particulars:
					item = dict()
					item['name'] = str(ledger.ledger)
					item['amount'] = float(ledger.amount)
					item['type'] = 'Dr' if ledger.is_debit == 1 else 'Cr'
					self.voucher['particulars'].append(item)
		except Exception as e:
			logger.exception(e)
		return self.voucher

	def queryVoucherInsert(self, user_id=None, enterprise_id=None, data=None):
		"""
		:param user_id:
		:param enterprise_id:
		:param data:
		:return:
		"""
		response = {}
		try:
			user = getUser(enterprise_id=enterprise_id, user_id=user_id)
			dataset = self.buildVoucherData(user=user, data=data)
			response = self.insert(id=data.id, enterprise_id=enterprise_id, data=dataset)
		except Exception as e:
			logger.exception(e)
		return response

	def fetchLogList(self, voucher_id=None, enterprise_id=None):
		"""
		get listed voucher log data
		:param voucher_id:
		:param enterprise_id:
		:return:
		"""
		response = {}
		result = []
		try:
			query = self.fetchLog(id=int(voucher_id), enterprise_id=int(enterprise_id))
			for voucher in query:
				voucher['created_at'] = str(voucher['created_at'])
				voucher['log']['modified_at'] = str(voucher['log']['modified_at'])
				result.append(voucher)
			response = response_code.success() if len(result) > 0 else response_code.failure()
			response['data'] = result
		except Exception as e:
			logger.exception(e)
		return response

	def fetchLogData(self, voucher_id=None, enterprise_id=None, modified_at=None):
		"""
		get individual log data
		:param voucher_id:
		:param enterprise_id:
		:param modified_at:
		:return:
		"""
		response = response_code.failure()
		result = []
		try:
			query = self.fetchLogDetails(id=int(voucher_id), enterprise_id=int(enterprise_id), identifier=modified_at)
			for voucher in query:
				del voucher['log']['modified_at']
				result.append(voucher)
			response = response_code.success() if len(result) > 0 else response
			if len(result) > 1:
				response['data'] = self.buildLogData(result[1]['log'], result[0]['log'])
			elif len(result) > 0:
				response['data'] = self.buildLogData(None, result[0]['log'])
			response['data'] = response['data'] if len(response['data']) > 0 else ['No change detected']
		except Exception as e:
			logger.exception(e)
			response = response_code.failure()
		return response

	def fieldLogData(self, key=None, title=None, old=None, new=None):
		"""

		:param key:
		:param title:
		:param old:
		:param new:
		:return:
		"""
		if old is not None and key in old:
			diff = DeepDiff(old[key], new[key], ignore_order=True) if key != 'particulars' else DeepDiff(old[key], new[key])
			if 'iterable_item_added' in diff:
				if key == 'tags':
					for item in diff['iterable_item_added'].items():
						self.response_log.append('%s <b>%s</b> were added' % (title, item[1]))
			if 'iterable_item_removed' in diff:
				if key == 'tags':
					for item in diff['iterable_item_removed'].items():
						self.response_log.append('%s <b>%s</b> was removed' % (title, item[1]))
			if 'values_changed' in diff:
				if key == 'tags':
					self.response_log.append('tag <b>%s</b> removed' % diff['values_changed']['root[0]']['old_value'])
					self.response_log.append('tag <b>%s</b> added' % diff['values_changed']['root[0]']['new_value'])
				elif key == 'particulars':
					if 'iterable_item_added' in diff:
						for item in diff['iterable_item_added'].items():
							self.response_log.append('%s <b>%s</b> of value <b>%s %s</b> added' % (title,
							item[1]['name'], item[1]['amount'], item[1]['type']))
					if 'iterable_item_removed' in diff:
						for item in diff['iterable_item_removed'].items():
							self.response_log.append('%s <b>%s</b> of value <b>%s %s</b> was removed' % (title,
						                                                                       item[1]['name'],
						                                                                       item[1]['amount'],
						                                                                       item[1]['type']))
					if 'values_changed' in diff:
						for item in diff['values_changed'].keys():
							var_chg = re.findall("\[(.*?)\]", item.replace('\'', ''))
							self.response_log.append('<b>%s</b> of ledger <b>%s</b> has changed to <b>%s</b>' % (
							var_chg[1].capitalize(), old[key][int(var_chg[0])]['name'], new[key][int(var_chg[0])][var_chg[1]]))
				else:
					if str(diff['values_changed']['root']['old_value']) == '' and str(
							diff['values_changed']['root']['new_value']) != '':
						self.response_log.append(
							'%s <b>%s</b> added' % (title, diff['values_changed']['root']['new_value']))
					elif str(diff['values_changed']['root']['new_value']) == '' and str(
							diff['values_changed']['root']['old_value']) != '':
						self.response_log.append(
							'%s <b>%s</b> removed' % (title, diff['values_changed']['root']['new_value']))
					else:
						self.response_log.append('%s changed from <b>%s</b> to <b>%s</b>' % (
							title,
							diff['values_changed']['root']['old_value'],
							diff['values_changed']['root']['new_value']))
		elif key in new:
			if type(new[key]) == list:
				for item in new[key]:
					if type(item) == dict:
						self.response_log.append('%s <b>%s</b> of value <b>%s %s</b> added' % (title, item['name'], item['amount'], item['type']))
					else:
						self.response_log.append('%s <b>%s</b> added' % (title, item))
			elif new[key] != '':
				if key in ['status']:
					self.response_log.append('%s <b>%s</b>' % (title, new[key]))
				elif key in ['voucher_date', 'type', 'project']:
					self.response_log.append('%s <b>%s</b> selected' % (title, new[key]))
				else:
					self.response_log.append('%s <b>%s</b> added' % (title, new[key]))
		logger.info('tags LOG DATA updated %s' % self.response_log)
		return False

	def buildLogData(self, old=None, new=None):
		"""

		:param old:
		:param new:
		:return:
		"""
		for key, title in self.voucher_context_set.items():
			self.fieldLogData(key=key, title=title, old=old, new=new)
		return self.response_log
