import collections

__author__ = 'charles'


class PurchaseReportProperties(object):
	"""

	"""
	join_constraint = {
		'indents': ['indent_id', 'indent_code', 'indent_date'],
		'indent_material': ['indent_qty', 'indent_received_qty', 'indent_ordered_qty', 'indent_pending_qty'],
		'projects': ['project'],
		'purchase_order': [
			'po_status', 'project', 'po_no', 'po_draft_no', 'po_draft_date', 'po_due_date', 'po_date',
			'po_value', 't_and_f_extra', 'po_currency'],
		'purchase_order_material': [
			'material_name', 'material_drawing_no', 'material_stock_price', 'material_make', 'material_unit',
			'indent_received_qty', 'indent_ordered_qty', 'indent_pending_qty', 'po_qty', 'po_price_material',
			'received_qty_po', 'pending_qty_po', 'material_status', 'delivery_status', 'po_status', 'delivery_overdue'],
		'materials': ['material_name', 'material_stock_price', 'material_make'],
		'material_make_map': ['material_make'],
		'make': ['material_make'],
		'unit_master': ['material_unit'],
		'grn': [
			'qty_per_invoice', 'qty_received_per_invoice', 'qty_shortage', 'qty_accepted', 'qty_rejected',
			'received_qty_po', 'pending_qty_po', 'disc_percent_per_invoice', 'material_status', 'delivery_status',
			'grn_number', 'party_inv_no', 'grn_date', 'delivery_overdue', 'basic_price_per_invoice', 'total_tax_value',
			'total_price_w_inv'],
		'grn_material': [
			'qty_per_invoice', 'qty_received_per_invoice', 'qty_shortage', 'qty_accepted', 'qty_rejected',
			'received_qty_po', 'pending_qty_po', 'disc_percent_per_invoice', 'po_status', 'basic_price_per_invoice',
			'total_tax_value', 'total_price_w_inv'],
		'party_master': ['supplier_name'],
		'currency': ['po_currency'],
		'billing': ['payment_status', 'pending_amount', 'aging'],
		'enterprise': ['enterprise_name'],
		'po_delivery': ['delivery_overdue', 'delivery_status', 'po_due_date']
	}

	type_of_consolidation = {
		'string': [
			'indent_code', 'project', 'material_name', 'material_drawing_no', 'material_make', 'material_unit',
			'supplier_name', 'po_no', 'po_draft_no', 'material_status', 'delivery_status', 'po_status', 'po_currency',
			'grn_number', 'party_inv_no', 'payment_status'],
		'date': ['indent_date', 'po_draft_date', 'po_due_date', 'po_date', 'grn_date'],
		'integer': [
			'material_stock_price', 'indent_qty', 'indent_received_qty', 'indent_pending_qty', 'indent_ordered_qty',
			'po_price_unit', 'po_qty', 'po_price_material', 'qty_per_invoice', 'qty_received_per_invoice',
			'qty_shortage', 'qty_accepted', 'qty_rejected', 'received_qty_po', 'pending_qty_po', 'disc_percent_per_invoice',
			'delivery_overdue', 'po_value', 't_and_f_extra', 'pending_amount', 'aging', 'basic_price_per_invoice',
			'total_tax_value', 'total_price_w_inv']
	}

	join_relative_addition = {
		'indents': ['indent_material'],
		'billing': ['grn', 'grn_material'],
		'grn': ['grn_material', 'billing'],
		'grn_material': ['grn', 'billing'],
		'party_master': ['billing'],
		'purchase_order_material': ['grn', 'billing'],
		'make': ['material_make_map']
	}

	grouping_fields = {
		'indent_code': ['indents.indent_no'],
		'indent_date': [],
		'indent_qty': ['materials.drawing_no'],
		'indent_received_qty': ['materials.drawing_no'],
		'indent_pending_qty': ['materials.drawing_no'],
		'indent_ordered_qty': ['materials.drawing_no'],
		'supplier_id': ['purchase_order.supplier_id'],
		'supplier_name': ['party_master.party_id'],
		'material_drawing_no': ['materials.drawing_no'],
		'material_name': ['materials.drawing_no', 'purchase_order_material.item_name'],
		'material_make': ['make.id'],
		'material_unit': ['materials.drawing_no'],
		'material_stock_price': ['materials.drawing_no'],
		'po_no': ['purchase_order.id'],
		'po_draft_no': ['purchase_order.id'],
		'po_draft_date': [],
		'po_price_unit': ['materials.drawing_no'],
		'po_date': [],
		'po_due_date': [],
		'po_price_material': ['materials.drawing_no'],
		'po_status': ['purchase_order.id'],
		'project': ['projects.id'],
		'po_qty': ['materials.drawing_no'],
		'qty_shortage': ['materials.drawing_no'],
		'qty_accepted': ['materials.drawing_no'],
		'qty_rejected': ['materials.drawing_no'],
		'received_qty_po': ['materials.drawing_no'],
		'pending_qty_po': ['materials.drawing_no'],
		't_and_f_extra': ['t_and_f_extra'],
		'po_currency': ['currency.id'],
		'material_status': ['purchase_order.id'],
		'delivery_status': ['purchase_order.id'],
		'delivery_overdue': [],
		'grn_number': ['grn.grn_no'],
		'payment_status': ['payment_status'],
		'aging': ['aging'],
		'basic_price_per_invoice': [],
		'total_tax_value': [],
		'qty_per_invoice': ['materials.drawing_no'],
		'qty_received_per_invoice': ['materials.drawing_no'],
		'total_price_w_inv': []
	}

	column_calc_data = {
		'indent_code': {
			'column_data': """IF(indents.indent_id IS NOT NULL, CONCAT(indents.financial_year, "/IND/", 
								LPAD(indents.indent_id, 6, 0), IFNULL(indents.sub_number,"")),"-") AS indent_code""",
			'where_clause': """""",
			'having_clause': 'indent_code'},
		'indent_id': {
			'column_data': """indents.indent_id AS indent_id""",
			'where_clause': """indents.indent_id""",
			'having_clause': ''},
		'indent_date': {
			'column_data': """IF(indents.raised_date IS NOT NULL,indents.raised_date,"-") AS indent_date""",
			'where_clause': """indents.raised_date""",
			'having_clause': ''},
		'indent_qty': {
			'column_data': """SUM(IFNULL(indent_material.request_qty, 0.00)) AS indent_qty""",
			'where_clause': """""",
			'having_clause': 'indent_qty'},
		'indent_received_qty': {
			'column_data': """ROUND(SUM(IFNULL(IF(indent_material.request_qty IS NOT NULL, purchase_order_material.rec_qty, 0.00) , 0.00)), 2) AS indent_received_qty""",
			'where_clause': """indent_material.indent_no""",
			'having_clause': 'indent_received_qty'},
		'indent_ordered_qty': {
			'column_data': """IFNULL(ROUND((indent_material.request_qty - IFNULL(SUM(purchase_order_material.order_qty)
								, 0)), 2), "0") AS indent_ordered_qty""",
			'where_clause': """""",
			'having_clause': 'indent_ordered_qty'},
		'indent_pending_qty': {
			'column_data': """IFNULL(indent_material.request_qty, 0.00) - IFNULL((indent_material.request_qty - 
								IFNULL(SUM(purchase_order_material.order_qty), 0)) - IFNULL(SUM(purchase_order_material.rec_qty),0),0) AS indent_pending_qty""",
			'where_clause': """""",
			'having_clause': 'indent_pending_qty'},
		'enterprise_id': {
			'column_data': """purchase_order.enterprise_id""",
			'where_clause': """purchase_order.enterprise_id""",
			'having_clause': ''},
		'purchase_order_id': {
			'column_data': """purchase_order.id""",
			'where_clause': """purchase_order.id""",
			'having_clause': ''},
		'supplier_id': {
			'column_data': """purchase_order.supplier_id""",
			'where_clause': """purchase_order.supplier_id""",
			'having_clause': ''},
		'supplier_name': {
			'column_data': """IFNULL(party_master.party_name,"-NA-") AS supplier_name""",
			'where_clause': """party_master.party_name""",
			'having_clause': ''},
		'material_name': {
			'column_data': """IFNULL(IFNULL(materials.name, purchase_order_material.item_name), 
								IFNULL(purchase_order_material.drawing_no,"-NA-")) AS material_name""",
			'where_clause': """""",
			'having_clause': 'material_name'},
		'material_drawing_no': {
			'column_data': """IFNULL(purchase_order_material.drawing_no, "-NA-") AS material_drawing_no""",
			'where_clause': """purchase_order_material.drawing_no""",
			'having_clause': ''},
		'material_make': {
			'column_data': """IF(CONCAT(IFNULL(make.make_name,''), IFNULL(material_make_map.part_no,'')) <> '', 
								CONCAT(IFNULL(make.make_name,''), "-",IFNULL(material_make_map.part_no,'')), '-NA-') AS material_make""",
			'where_clause': """make.make_name""",
			'having_clause': ''},
		'material_faulty': {
			'column_data': """IFNULL(purchase_order_material.is_faulty,"-NA-") AS material_faulty""",
			'where_clause': """purchase_order_material.is_faulty""",
			'having_clause': 'material_faulty'},
		'material_unit': {
			'column_data': """IFNULL(unit_master.unit_name,"-NA-") AS material_unit""",
			'where_clause': """unit_master.unit_name""",
			'having_clause': ''},
		'material_stock_price': {
			'column_data': """IFNULL(ROUND(materials.price,2),0) AS material_stock_price""",
			'where_clause': """materials.price""",
			'having_clause': ''},
		'po_no': {
			'column_data': """IFNULL(CONCAT(purchase_order.financial_year, IF(purchase_order.type =1,"/JO/","/PO/"), 
								LPAD(purchase_order.orderno, 7, 0), IFNULL(purchase_order.sub_number,"")), "") AS po_no""",
			'where_clause': """""",
			'having_clause': 'po_no'},
		'po_draft_no': {
			'column_data': """IFNULL(purchase_order.id,"") AS po_draft_no""",
			'where_clause': """purchase_order.id""",
			'having_clause': ''},
		'po_draft_date': {
			'column_data': """IFNULL(purchase_order.drafted_on,"") AS po_draft_date""",
			'where_clause': """purchase_order.drafted_on""",
			'having_clause': ''},
		'po_due_date': {
			'column_data': """IFNULL(purchase_order.delivery,"") AS po_due_date""",
			'where_clause': """purchase_order.delivery""",
			'having_clause': ''},
		'po_date': {
			'column_data': """IFNULL(CAST(purchase_order.approved_on AS char),"") AS po_date""",
			'where_clause': """purchase_order.approved_on""",
			'having_clause': ''},
		'po_price_unit': {
			'column_data': """IFNULL(ROUND(purchase_order_material.po_price - (purchase_order_material.po_price * (
								purchase_order_material.discount / 100)), 2),0) AS po_price_unit""",
			'where_clause': """""",
			'having_clause': 'po_price_unit'},
		'po_price_material': {
			'column_data': """ROUND(SUM(IFNULL((purchase_order_material.po_price - (purchase_order_material.po_price * 
								(purchase_order_material.discount / 100))), 0) * IFNULL((purchase_order_material.order_qty), 0)), 2) AS po_price_material""",
			'where_clause': """""",
			'having_clause': 'po_price_material'},
		'po_status': {
			'column_data': """IFNULL(IF(purchase_order.status != 3,IF(IF(purchase_order_material.order_qty IS NOT NULL, 
								SUM(purchase_order_material.order_qty)/IF((COUNT(grn_material.grn_no) = NULL) OR 
								(COUNT(grn_material.grn_no) = 0),1,COUNT(grn_material.grn_no)), 0) <= IF(purchase_order_material.rec_qty IS NOT NULL, 
								(purchase_order_material.rec_qty), 0), CONCAT('Completed/', MAX(date(purchase_order_material.max_inward_date))),
								"Pending"),""),"") AS po_status""",
			'where_clause': """""",
			'having_clause': 'po_status'},
		'project': {
			'column_data': """projects.name AS project""",
			'where_clause': """projects.name""",
			'having_clause': ''},
		'po_value': {
			'column_data': """IFNULL(SUM(purchase_order.total),0) AS po_value""",
			'where_clause': """""",
			'having_clause': 'po_value'},
		'po_qty': {
			'column_data': """SUM(IFNULL(purchase_order_material.order_qty, 0)) AS po_qty""",
			'where_clause': """""",
			'having_clause': 'po_qty'},
		'received_qty_po': {
			'column_data': """SUM(IF(grn_material.acc_qty IS NOT NULL AND grn.status IS NOT NULL, IFNULL(IF(purchase_order.status != 3, 
								purchase_order_material.rec_qty, 0),0),0)) AS received_qty_po""",
			'where_clause': """""",
			'having_clause': 'received_qty_po'},
		'pending_qty_po': {
			'column_data': """SUM(IFNULL(IF(purchase_order.status != 3, purchase_order_material.order_qty, 0) - 
								IF(grn_material.acc_qty is not NULL AND grn.status IS NOT NULL, IF(purchase_order.status != 3, 
								purchase_order_material.rec_qty, 0),0),0)) AS pending_qty_po""",
			'where_clause': """""",
			'having_clause': 'pending_qty_po'},
		't_and_f_extra': {
			'column_data': """IFNULL(IF(purchase_order.packing_forwarding = 0, "NO", "YES"),"-NA-") AS t_and_f_extra""",
			'where_clause': """""",
			'having_clause': 't_and_f_extra'},
		'po_currency': {
			'column_data': """IFNULL(currency.code,"-NA-") AS po_currency""",
			'where_clause': """currency.code""",
			'having_clause': ''},
		'material_status': {
			'column_data': """IFNULL((CASE							
								WHEN purchase_order.status > 1 AND purchase_order.status < 3
								THEN (CASE
										WHEN grn.status IS NOT NULL
										THEN (CASE
												WHEN SUM(purchase_order_material.order_qty) > purchase_order_material.rec_qty
												THEN 'PARTIAL'																	 
												ELSE 'SUPPLIED'
											  END)
										ELSE 'PENDING'
									 END)			
							END), "") AS material_status""",
			'where_clause': """""",
			'having_clause': 'material_status'},
		'delivery_status': {
			'column_data': """IFNULL((CASE							
								WHEN purchase_order.status > 1 AND purchase_order.status < 3
								THEN (CASE
										WHEN SUM(purchase_order_material.order_qty) > purchase_order_material.rec_qty
										THEN (CASE 
												WHEN (CURDATE() > IF(MAX(po_delivery_schedule.due_date) IS NULL, purchase_order.order_date, MAX(po_delivery_schedule.due_date)))
												THEN 'DELAYED'
												ELSE 'ON_TRACK'
											 END)																	 
										ELSE (CASE 
												WHEN (MAX(purchase_order_material.max_inward_date) <= IF(MAX(po_delivery_schedule.due_date) IS NULL, purchase_order.order_date, MAX(po_delivery_schedule.due_date))) AND grn.status IS NOT NULL
												THEN 'ON_TIME'
												ELSE 'DELAYED'
											 END)
									 END)			
							END), "") AS delivery_status""",
			'where_clause': """""",
			'having_clause': 'delivery_status'},
		'delivery_overdue': {
			'column_data': """IFNULL(
								(CASE
									WHEN purchase_order.status > -1 AND purchase_order.status < 3
									THEN (CASE
											WHEN SUM(purchase_order_material.order_qty) > purchase_order_material.rec_qty
											THEN (CASE
													WHEN (NOW() > IF(MAX(po_delivery_schedule.due_date) IS NULL, purchase_order.order_date, MAX(po_delivery_schedule.due_date)))
													THEN DATEDIFF(NOW(), IF(MAX(po_delivery_schedule.due_date) IS NULL , purchase_order.order_date, MAX(po_delivery_schedule.due_date)))							                            
												 END)
											ELSE (CASE
													WHEN MAX(grn.inward_date) > IF(MAX(po_delivery_schedule.due_date) IS NULL, purchase_order.order_date, MAX(po_delivery_schedule.due_date))
													THEN DATEDIFF(MAX(grn.inward_date),IF(MAX(po_delivery_schedule.due_date) IS NULL , purchase_order.order_date, MAX(po_delivery_schedule.due_date)))					                            
												 END)
										 END)
								END), "") AS delivery_overdue""",
			'where_clause': """""",
			'having_clause': 'delivery_overdue'},
		'grn_number': {
			'column_data': """IF(grn.receipt_no IS NOT NULL,IF(grn.status > 0, CONCAT(grn.financial_year, "/GRN/", 
								LPAD(grn.receipt_no, 6, 0), IFNULL(grn.sub_number,"")), grn.grn_no),"-NA-") AS grn_number""",
			'where_clause': """""",
			'having_clause': 'grn_number'},
		'grn_date': {
			'column_data': """IF(grn.inward_date IS NOT NULL AND grn.status IS NOT NULL,grn.inward_date,"") AS grn_date""",
			'where_clause': """grn.inward_date""",
			'having_clause': ''},
		'party_inv_no': {
			'column_data': """IF(grn.status IS NOT NULL,IFNULL(grn.invno,0),0) AS party_inv_no""",
			'where_clause': """grn.invno""",
			'having_clause': ''},
		'qty_per_invoice': {
			'column_data': """IF(grn.status IS NOT NULL, ROUND(SUM(IFNULL(grn_material.dc_qty, 0))/IF(count(grn_material.grn_no)=0 OR 
								count(grn_material.grn_no) IS NULL, 1,count(grn_material.grn_no)),2),0) AS qty_per_invoice""",
			'where_clause': """""",
			'having_clause': 'qty_per_invoice'},
		'qty_received_per_invoice': {
			'column_data': """IF(grn.status IS NOT NULL, ROUND(SUM(IFNULL(grn_material.rec_qty, 0))/IF(count(grn_material.grn_no)=0 OR 
								count(grn_material.grn_no) IS NULL, 1,count(grn_material.grn_no)),2),0) AS qty_received_per_invoice""",
			'where_clause': """""",
			'having_clause': 'qty_received_per_invoice'},
		'qty_shortage': {
			'column_data': """IF(grn.status IS NOT NULL, ROUND(IF(grn.rec_against IN ("Purchase Order" , "Job Work", "Delivery Challan"), 
								(grn_material.dc_qty - grn_material.rec_qty), 0),2),0) AS qty_shortage""",
			'where_clause': """""",
			'having_clause': 'qty_shortage'},
		'qty_accepted': {
			'column_data': """IF(grn.status IS NOT NULL, ROUND(SUM(IFNULL(grn_material.acc_qty, 0))/IF(count(grn_material.grn_no)=0 OR 
								count(grn_material.grn_no) IS NULL, 1,count(grn_material.grn_no)),2),0) AS qty_accepted""",
			'where_clause': """""",
			'having_clause': 'qty_accepted'},
		'qty_rejected': {
			'column_data': """IF(grn.status IS NOT NULL, ROUND(SUM(IFNULL(grn_material.rec_qty - grn_material.acc_qty, 0))/IF(count(grn_material.grn_no)=0 OR 
								count(grn_material.grn_no) IS NULL, 1,count(grn_material.grn_no)),2),0) AS qty_rejected""",
			'where_clause': """""",
			'having_clause': 'qty_rejected'},
		'disc_percent_per_invoice': {
			'column_data': """IF(grn.status IS NOT NULL, IFNULL(ROUND(IFNULL(grn_material.discount, 0), 2), 0), 0) 
								AS disc_percent_per_invoice""",
			'where_clause': """grn_material.discount""",
			'having_clause': ''},
		'basic_price_per_invoice': {
			'column_data': """SUM(IF(grn.status IS NOT NULL, IFNULL(ROUND(grn_material.dc_qty * grn_material.inv_rate * 
								((100-grn_material.discount) / 100), 2),0),0)) AS basic_price_per_invoice""",
			'where_clause': """""",
			'having_clause': 'basic_price_per_invoice'},
		'total_price_w_inv': {
			'column_data': """SUM(IF(grn.status IS NOT NULL, IF(grn.net_inv_value IS NOT NULL, grn.net_inv_value, 0), 0)) 
								AS total_price_w_inv""",
			'where_clause': """grn.net_inv_value""",
			'having_clause': 'total_price_w_inv'},
		'total_tax_value': {
			'column_data': """SUM(IF(grn.status IS NOT NULL, IF(grn.net_inv_value IS NOT NULL, grn.net_inv_value, 0), 0))-
								SUM(IF(grn.status IS NOT NULL, IFNULL(ROUND(grn_material.dc_qty * grn_material.inv_rate * 
								((100-grn_material.discount) / 100), 2),0),0)) AS total_tax_value""",
			'where_clause': """""",
			'having_clause': 'total_tax_value'},
		'aging': {
			'column_data': """IFNULL(DATEDIFF(CURDATE(), ledger_bills.bill_date),0) AS aging""",
			'where_clause': """""",
			'having_clause': 'aging'},
		'payment_status': {
			'column_data': """CASE
										WHEN ledger_bills.net_value = 0 THEN "Received"
										ELSE
											CASE
												WHEN DATE_ADD(grn.approved_on, INTERVAL @party_master.payment_credit_days DAY) >= CURDATE()
												THEN "On Track"
												ELSE 
													CASE
														WHEN DATEDIFF(CURDATE(), ledger_bills.bill_date) > party_master.payment_credit_days
														THEN "Delayed"
														ELSE "Pending"
											END
													END
									END AS payment_status""",
			'where_clause': """""",
			'having_clause': 'payment_status'},
		'pending_amount': {
			'column_data': """IFNULL(ledger_bills.net_value, 0) AS pending_amount""",
			'where_clause': """""",
			'having_clause': 'pending_amount'},
		'status': {
			'column_data': """IFNULL(purchase_order.status,"") AS status""",
			'where_clause': """purchase_order.status""",
			'having_clause': ''},
	}

	def getJoinConstraintFields(self, fields=None):
		"""
		This method used to combine the field joins with essential joins which required to address the table relations
		ex.) to get grn  we should join both po material table and grn material table and if get billing we should get grn's
		:param fields:
		:return join constraint list
		"""
		join_idx_data = []
		for idx, elem in self.join_constraint.items():
			join_idx_data.append(idx) if any(item in elem for item in fields) else None
		for idx, elem in self.join_relative_addition.items():
			join_idx_data.append(idx) if any(item in join_idx_data for item in elem) else None
		return list(dict.fromkeys(join_idx_data))


class SalesReportProperties(object):
	join_enabler = {'invoice_item_tax_abstract': [
		'product_name', 'hsn_code', 'product_make', 'product_faulty', 'matl_invoice_qty',
		'matl_unit_price', 'matl_invoice_value', 'matl_invoice_value_ex_tax', 'matl_cgst_tax', 'matl_sgst_tax',
		'matl_sgst_tax', 'matl_igst_tax', 'matl_total_tax', 'matl_other_tax', 'oa_number', 'other_tax', 'total_invoice'],
		'invoice_item_tax_abstract_invoicewise': [
			'total_tax', 'other_tax', 'total_invoice', 'total_cgst', 'total_sgst', 'total_igst'],
		'material_make_map': ['product_make'], 'make': ['product_make'], 'order_acknowledgement': ['oa_number'],
		'party_master': ['invoice_payment_status', 'invoice_pending_amount', 'credit_period', 'party_name'], 'projects': ['project'],
		'ledger_bills': ['invoice_payment_status', 'invoice_pending_amount', 'aging'], 'invoice_tags': ['tags'],
		'tags': ['tags']}

	gfields = {
		'party_name': ['party_master.party_id'],
		'credit_period': ['party_master.party_id'],
		'product_name': [
			'IFNULL(invoice_item_tax_abstract.drawing_no, invoice_item_tax_abstract.item_name)'],
		'hsn_code': ['invoice_item_tax_abstract.hsn_code'],
		'product_make': ['invoice_item_tax_abstract.make_id'],
		'product_faulty': ['invoice_item_tax_abstract.is_faulty'],
		'matl_invoice_qty': ['IFNULL(invoice_item_tax_abstract.drawing_no, invoice_item_tax_abstract.item_name)'],
		'matl_unit_price': ['IFNULL(invoice_item_tax_abstract.drawing_no, invoice_item_tax_abstract.item_name)'],
		'matl_invoice_value': ['IFNULL(invoice_item_tax_abstract.drawing_no, invoice_item_tax_abstract.item_name)'],
		'matl_invoice_value_ex_tax': ['IFNULL(invoice_item_tax_abstract.drawing_no, invoice_item_tax_abstract.item_name)'],
		'matl_cgst_tax': ['IFNULL(invoice_item_tax_abstract.drawing_no, invoice_item_tax_abstract.item_name)'],
		'matl_sgst_tax': ['IFNULL(invoice_item_tax_abstract.drawing_no, invoice_item_tax_abstract.item_name)'],
		'matl_igst_tax': ['IFNULL(invoice_item_tax_abstract.drawing_no, invoice_item_tax_abstract.item_name)'],
		'matl_total_tax': ['IFNULL(invoice_item_tax_abstract.drawing_no, invoice_item_tax_abstract.item_name)'],
		'matl_other_tax': ['IFNULL(invoice_item_tax_abstract.drawing_no, invoice_item_tax_abstract.item_name)'],

		'oa_number': ['order_acknowledgement.oa_no'],
		'oa_date': [],
		'project': ['projects.id'],
		'invoice_no': ['invoice.id'],
		'invoice_payment_status': ['invoice.id'],
		'invoice_pending_amount': ['invoice.id'],
		'aging': ['invoice.id'],
		'invoice_date': [],
		'tags': [],
		'total_tax': [],
		'other_tax': [],
		'total_invoice': [],
		'total_cgst': [],
		'total_sgst': [],
		'total_igst': [],
		'gross_invoice_value': []}

	selector_field_calc = {
		'party_name': 'party_master.party_name',
		'credit_period': 'party_master.receipt_credit_days',
		'product_name': """IFNULL(invoice_item_tax_abstract.item_name, '-NA-')""",
		'hsn_code': """IFNULL(invoice_item_tax_abstract.hsn_code,'-NA-')""",
		'product_make': """IF(CONCAT(IFNULL(make.make_name,''), IFNULL(material_make_map.part_no,'')) <> '',CONCAT(IFNULL(make.make_name,''), IFNULL(material_make_map.part_no,'')), '-NA-')""",
		'product_faulty': 'IF(invoice_item_tax_abstract.is_faulty = 1, "YES", "NO")',
		'matl_invoice_qty': 'IFNULL(ROUND(SUM(invoice_item_tax_abstract.qty), 2), 0)',
		'matl_unit_price': 'IFNULL(ROUND(SUM(invoice_item_tax_abstract.unit_price), 2), 0)',
		'matl_invoice_value': """IFNULL(ROUND((((invoice_item_tax_abstract.qty * invoice_item_tax_abstract.unit_price) * (100 - invoice_item_tax_abstract.discount)) / 100), 2)
										+ ROUND(((((invoice_item_tax_abstract.qty * invoice_item_tax_abstract.unit_price) * (100 - invoice_item_tax_abstract.discount)) / 100) * (invoice_item_tax_abstract.cgst_rate / 100)), 2)
										+ ROUND(((((invoice_item_tax_abstract.qty * invoice_item_tax_abstract.unit_price) * (100 - invoice_item_tax_abstract.discount)) / 100) * (invoice_item_tax_abstract.sgst_rate / 100)), 2)
										+ ROUND(((((invoice_item_tax_abstract.qty * invoice_item_tax_abstract.unit_price) * (100 - invoice_item_tax_abstract.discount)) / 100) * (invoice_item_tax_abstract.igst_rate / 100)), 2), 0)""",
		'matl_invoice_value_ex_tax': 'IFNULL(SUM(ROUND((((invoice_item_tax_abstract.qty * invoice_item_tax_abstract.unit_price) * (100 - invoice_item_tax_abstract.discount)) / 100), 2)), 0)',
		'matl_cgst_tax': 'IFNULL(SUM(ROUND(((((invoice_item_tax_abstract.qty * invoice_item_tax_abstract.unit_price) * (100 - invoice_item_tax_abstract.discount)) / 100) * (invoice_item_tax_abstract.cgst_rate / 100)), 2)), 0)',
		'matl_sgst_tax': 'IFNULL(SUM(ROUND(((((invoice_item_tax_abstract.qty * invoice_item_tax_abstract.unit_price) * (100 - invoice_item_tax_abstract.discount)) / 100) * (invoice_item_tax_abstract.sgst_rate / 100)), 2)), 0)',
		'matl_igst_tax': 'IFNULL(SUM(ROUND(((((invoice_item_tax_abstract.qty * invoice_item_tax_abstract.unit_price) * (100 - invoice_item_tax_abstract.discount)) / 100) * (invoice_item_tax_abstract.igst_rate / 100)), 2)), 0)',
		'matl_total_tax': """IFNULL(SUM(ROUND(((((invoice_item_tax_abstract.qty * invoice_item_tax_abstract.unit_price) * (100 - invoice_item_tax_abstract.discount)) / 100) * (invoice_item_tax_abstract.cgst_rate / 100)), 2))
										+ SUM(ROUND(((((invoice_item_tax_abstract.qty * invoice_item_tax_abstract.unit_price) * (100 - invoice_item_tax_abstract.discount)) / 100) * (invoice_item_tax_abstract.sgst_rate / 100)), 2))
										+ SUM(ROUND(((((invoice_item_tax_abstract.qty * invoice_item_tax_abstract.unit_price) * (100 - invoice_item_tax_abstract.discount)) / 100) * (invoice_item_tax_abstract.igst_rate / 100)), 2)), 0)""",
		'matl_other_tax': 'IFNULL(SUM(ROUND(((((invoice_item_tax_abstract.qty * invoice_item_tax_abstract.unit_price) * (100 - invoice_item_tax_abstract.discount)) / 100) * IF(invoice_item_tax_abstract.other_rate=0, 1, (invoice_item_tax_abstract.other_rate / 100))), 2)), 0)',

		'oa_number': 'GROUP_CONCAT(DISTINCT IFNULL(CONCAT(order_acknowledgement.financial_year, "/",SUBSTR(order_acknowledgement.type, 1, 1), LPAD(order_acknowledgement.oa_no, 7, 0)),""))',
		'oa_date': 'invoice.order_accept_date',
		'project': """IFNULL(projects.name, '-NA-')""",
		'invoice_no': 'IFNULL(invoice.invoice_code, CONCAT(invoice.financial_year, "/",SUBSTR(invoice.type, 1, 1), LPAD(invoice.invoice_no, 7, 0), IFNULL(invoice.sub_number, "")))',
		'invoice_payment_status': """CASE
											WHEN ledger_bills.net_value = 0
											THEN "Received"
											ELSE
												CASE
													WHEN DATE_ADD(invoice.approved_on, INTERVAL @party_master.receipt_credit_days DAY) >= CURDATE()
													THEN "On Track"
													ELSE 
														CASE
															WHEN DATEDIFF(CURDATE(), ledger_bills.bill_date) > party_master.receipt_credit_days
															THEN "Delayed"
															ELSE "Pending"
														END
												END
										END""",
		'invoice_pending_amount': """CASE
											WHEN ledger_bills.net_value = 0 OR invoice.ledger_bill_id is NULL THEN IFNULL(invoice.ledger_bill_id, invoice.grand_total)
											ELSE
												CASE
													WHEN DATE_ADD(invoice.approved_on, INTERVAL @party_master.receipt_credit_days DAY) >= CURDATE()
													THEN 0
													ELSE 
														CASE
															WHEN DATEDIFF(CURDATE(), ledger_bills.bill_date) > party_master.receipt_credit_days
															THEN IFNULL(ledger_bills.net_value, 0)
															ELSE IFNULL(ledger_bills.net_value, 0)
														END	
												END
										END""",
		'aging': """IFNULL(DATEDIFF(CURDATE(), ledger_bills.bill_date), '-NA-')""",
		'invoice_date': 'invoice.approved_on',
		'tags': """IFNULL(GROUP_CONCAT(DISTINCT tags.tag SEPARATOR ", "),"")""",
		'total_tax': """IFNULL(invoice_item_tax_abstract_invoicewise.total_tax,0)""",
		'other_tax': """IFNULL(SUM(ROUND((((invoice_item_tax_abstract.qty * invoice_item_tax_abstract.unit_price) * (100 - invoice_item_tax_abstract.discount)) / 100) * (invoice_item_tax_abstract.other_rate / 100), 2)),0)""",
		'total_invoice': """IFNULL(SUM(ROUND((((invoice_item_tax_abstract.qty * invoice_item_tax_abstract.unit_price) * (100 - invoice_item_tax_abstract.discount)) / 100), 2)),0)""",
		'total_cgst': """IFNULL(invoice_item_tax_abstract_invoicewise.cgst_tax, 0)""",
		'total_sgst': """IFNULL(invoice_item_tax_abstract_invoicewise.sgst_tax, 0)""",
		'total_igst': """IFNULL(invoice_item_tax_abstract_invoicewise.igst_tax, 0)""",
		'gross_invoice_value': 'IFNULL(invoice.grand_total, 0)'}

	selector_field_where = {
		'party_name': 'party_master.party_name',
		'credit_period': 'party_master.receipt_credit_days',
		'product_name': 'invoice_item_tax_abstract.item_name',
		'hsn_code': 'invoice_item_tax_abstract.hsn_code',
		'product_make': """make.make_name""",
		'product_faulty': '',
		'matl_invoice_qty': '',
		'matl_unit_price': '',
		'matl_invoice_value': '',
		'matl_invoice_value_ex_tax': '',
		'matl_cgst_tax': '',
		'matl_sgst_tax': '',
		'matl_igst_tax': '',
		'matl_total_tax': '',
		'matl_other_tax': '',
		'oa_number': '',
		'oa_date': 'invoice.order_accept_date',
		'project': 'projects.name',
		'invoice_no': '',
		'invoice_payment_status': '',
		'invoice_pending_amount': '',
		'aging': '',
		'invoice_date': 'invoice.approved_on',
		'tags': """tags.tag""",
		'total_tax': """invoice_item_tax_abstract_invoicewise.total_tax""",
		'other_tax': '',
		'total_invoice': '',
		'total_cgst': """invoice_item_tax_abstract_invoicewise.cgst_tax""",
		'total_sgst': """invoice_item_tax_abstract_invoicewise.sgst_tax""",
		'total_igst': """invoice_item_tax_abstract_invoicewise.igst_tax""",
		'gross_invoice_value': 'invoice.grand_total'}

	selector_field_having = {
		'party_name': '',
		'credit_period': '',
		'product_name': '',
		'hsn_code': '',
		'product_make': '',
		'product_faulty': 'product_faulty',
		'matl_invoice_qty': 'matl_invoice_qty',
		'matl_unit_price': 'matl_unit_price',
		'matl_invoice_value': 'matl_invoice_value',
		'matl_invoice_value_ex_tax': 'matl_invoice_value_ex_tax',
		'matl_cgst_tax': 'matl_cgst_tax',
		'matl_sgst_tax': 'matl_sgst_tax',
		'matl_igst_tax': 'matl_igst_tax',
		'matl_total_tax': 'matl_total_tax',
		'matl_other_tax': 'matl_other_tax',
		'oa_number': 'oa_number',
		'oa_date': '',
		'project': '',
		'invoice_no': 'invoice_no',
		'invoice_payment_status': 'invoice_payment_status',
		'invoice_pending_amount': 'invoice_pending_amount',
		'aging': 'aging',
		'invoice_date': '',
		'tags': '',
		'total_tax': '',
		'other_tax': 'other_tax',
		'total_invoice': 'total_invoice',
		'total_cgst': '',
		'total_sgst': '',
		'total_igst': '',
		'gross_invoice_value': ''}

	@staticmethod
	def constructSalesQueryJoins(fields):
		"""

		:param fields:
		:return:
		"""
		joins = collections.OrderedDict()
		join_enabler = SalesReportProperties.join_enabler
		if any(elem in fields for elem in join_enabler['invoice_item_tax_abstract']):
			joins["invoice_item_tax_abstract"] = (
				'LEFT', 'invoice_item_tax_abstract.invoice_id',
				'invoice.id AND invoice_item_tax_abstract.enterprise_id = invoice.enterprise_id')
		if any(elem in fields for elem in join_enabler['invoice_item_tax_abstract_invoicewise']):
			joins["invoice_item_tax_abstract_invoicewise"] = (
				'LEFT', 'invoice_item_tax_abstract_invoicewise.invoice_id',
				'invoice.id AND invoice_item_tax_abstract_invoicewise.enterprise_id = invoice.enterprise_id')
		if any(elem in fields for elem in join_enabler['material_make_map']):
			joins["material_make_map"] = (
				'LEFT', 'material_make_map.item_id',
				'invoice_item_tax_abstract.item_id AND material_make_map.make_id = invoice_item_tax_abstract.make_id \
				AND material_make_map.enterprise_id = invoice_item_tax_abstract.enterprise_id')
		if any(elem in fields for elem in join_enabler['make']):
			joins["make"] = (
				'LEFT', 'make.id',
				'material_make_map.make_id AND make.enterprise_id = material_make_map.enterprise_id')
		if any(elem in fields for elem in join_enabler['order_acknowledgement']):
			joins["order_acknowledgement"] = (
				'LEFT', 'order_acknowledgement.id',
				'invoice_item_tax_abstract.oa_no AND order_acknowledgement.enterprise_id = invoice_item_tax_abstract.enterprise_id')
		if any(elem in fields for elem in join_enabler['party_master']):
			joins["party_master"] = (
				'LEFT', 'party_master.party_id',
				'invoice.party_id AND party_master.enterprise_id = invoice.enterprise_id')
		if any(elem in fields for elem in join_enabler['projects']):
			joins["projects"] = (
				'LEFT', 'projects.id',
				'invoice.project_code AND projects.enterprise_id = invoice.enterprise_id')
		if any(elem in fields for elem in join_enabler['ledger_bills']):
			joins["ledger_bills"] = (
				'LEFT', 'ledger_bills.id',
				'invoice.ledger_bill_id AND ledger_bills.enterprise_id = invoice.enterprise_id')
		if any(elem in fields for elem in join_enabler['invoice_tags']):
			joins["invoice_tags"] = (
				'LEFT', 'invoice_tags.invoice_id',
				'invoice.id AND invoice.enterprise_id = invoice_tags.enterprise_id')
		if any(elem in fields for elem in join_enabler['tags']):
			joins["tags"] = (
				'LEFT', 'tags.id',
				'invoice_tags.tag_id AND tags.enterprise_id = invoice_tags.enterprise_id')
		return joins

	def salesreport_get_select(self, fields):
		"""

		:param fields:
		:return:
		"""
		select_fields = fields[:]
		where_fields = fields[:]
		having_fields = fields[:]
		for idx, field in enumerate(fields):
			select_fields[idx] = self.selector_field_calc[field] + ' AS ' + field
			where_fields[idx] = self.selector_field_where[field]
			having_fields[idx] = self.selector_field_having[field]
		return select_fields, where_fields, having_fields
