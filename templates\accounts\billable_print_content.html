<style>
   body{
		color: #000;
		font-family: 'Times New Roman';
	}

	.enterprise_details{
		font-size:15px;
	}

	.enterprise_name{
		font-size:15px;
	}

	.vendor_data{
<!--		width: calc(100% - 1em);-->
		float: left;
		margin-left: 160px;
		margin-top: -16px;
		font-size:14px;
	}

	.vendor_list{
		font-weight:bold;
		font-size:16px;
	}

	hr{
		border: 0;
  		border-top: 1px solid #ddd !important;
	}

	@font-face {
        font-family: 'Times New Roman';
        font-style: normal;
        font-weight: 400;
        src: url(https://www.xserp.in/site_media/fonts/times.ttf) format('truetype');
	}
  table{
		border: solid 1px #ddd;
		width:100%;
		max-width: 100%;
        margin-bottom: 20px;
        border-spacing: 0;
        border-collapse: collapse;
  }

  .table > thead:first-child > tr:first-child > th {
      border: none;
      border-left: 1px solid #bbb;
  }

  .custom-table thead tr th {
      text-align: center;
      background-color: #ddd !important;
      color: #444 !important;
      font-size: 14px !important;
      letter-spacing: 1px;
      vertical-align: middle;
      border-color: #bbb;
      padding: 8px;
      line-height: 1.42857143;
  }

  .table-striped > tbody > tr:nth-of-type(odd) {
      background-color: rgba(0, 65, 149,0.01);
  }

  .table-striped > tbody > tr:nth-of-type(even) {
      background-color: #f9f5f4;
  }

  .custom-table tbody tr {
      color: #333;
      transition: background 0.35s;
  }

  .table-bordered > thead > tr > th,
  .table-bordered > tbody > tr > th,
  .table-bordered > tfoot > tr > th,
  .table-bordered > thead > tr > td,
  .table-bordered > tbody > tr > td,
  .table-bordered > tfoot > tr > td {
	    border: 1px solid #ccc;
  }

  .table > thead > tr > th,
  .table > tbody > tr > th,
  .table > tfoot > tr > th,
  .table > thead > tr > td,
  .table > tbody > tr > td,
  .table > tfoot > tr > td {
      padding: 8px;
      line-height: 1.42857143;
      vertical-align: top;
      border-top: 1px solid #ddd;
  }

  .custom-table tbody td {
      word-break: break-word;
      letter-spacing: 0.2px;
      color: #444;
      vertical-align: middle !important;
      font-size: 14px;
  }

  .text-right {
      text-align: right;
  }

  .hide{
    display:none;
  }
</style>
<body onload="subst1()">
    <div id="header_div">
            <div class="pdf_title" style="text-align:right; font-size:20px;"><b>{{section_title|upper}}</b></div>
            <div>
                <div class="col-sm-8">
                    <img src="{{enterprise_logo}} " style="max-height: 10mm">
                    <div class="enterprise_name">{{ enterprise.name }}</div>
                    <div class="enterprise_details"> {{ enterprise.address_1 }}, {% if enterprise.address_2 and enterprise.address_2 != "" %}{{ enterprise.address_2 }}, {% endif %}{{ enterprise.city }} - {{ enterprise.pin_code }}, {{ enterprise.state }}<br></div>
                    <div class="enterprise_details"><b>Ph:</b>{% if enterprise.primary_contact_details.contact.phone_no %} {{ enterprise.primary_contact_details.contact.phone_no }} {% endif %}<b>E-mail:</b>{% if enterprise.primary_contact_details.contact.email %} {{ enterprise.primary_contact_details.contact.email }} {% endif %}</div>
                </div>
            </div>
            <div>
                <div><br>
                    <div class="vendor_list" >Party Name </div>
                    <div class="vendor_data">: <b>{% if party_details.party_name %} {{ party_details.party_name }} {% else %} {{ledger_name}} {% endif %}</b></div>
                </div>
               {% if party_details.address_1 %}
	                <div>
	                    <div class="vendor_list">Party Address</div>
	                    <div class="vendor_data">: {{ party_details.address_1 }},{% if party_details.address_2 and party_details.address_2 != "" %}{{ party_details.address_2 }},{% endif %}<br></div><br>
	                    <div class="vendor_data">&nbsp; {{ party_details.city }},{{ party_details.state }}</div>
	                </div>
               {% endif %}
               {% if party_details.phone_no %}
                    <div>
                        <div class="vendor_list" >Phone No </div>
                        <div class="vendor_data">: {{ party_details.phone_no }}</div>
                    </div>
                {% endif %}
            </div>
            <div class= "col-sm-12">
                <p>Dear Sir/Madam,</p>
                {% if show_advance_outsourcing == "true" %}
	            <p style="text-align:center"><b>Reg : </b> Outstanding as on <b>{{ current_date }}</b> Value: <b>{{ outsourcing_value }}</b></p>
                    <p>We wish to inform you having Outstanding with us as follows :</p><br>
                {% else %}
                <p>Settlement details for the {{content_text}} transactions made during the period <b>{{from_date}} - {{to_date}}</b></p><br>
                {% endif %}

            </div>
	</div>
    {% if show_advance_outsourcing == "true" and advance_outstanding and advance_outstanding > 0 %}
        <div>{% autoescape off %}
            {{ ageing_table }}
        {% endautoescape %}
        </div>
    {% endif %}
    <div>{% autoescape off %}
        {{ data }}
    {% endautoescape %}
    </div>


</body>

