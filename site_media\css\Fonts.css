/* latin */
@font-face {
  font-family: 'Kameron';
  font-style: normal;
  font-weight: 400;
  src: local('Kameron'), url(../fonts/_GHDZfFYphTmB3ibRDN_ZA.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215;
}

/* latin-ext */
@font-face {
  font-family: 'Monda';
  font-style: normal;
  font-weight: 400;
  src: local('Monda Regular'), local('Monda-Regular'), url(../fonts/t3vZkumW9T_w4ukdwBVHtA.woff2) format('woff2');
  unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Monda';
  font-style: normal;
  font-weight: 400;
  src: local('Monda Regular'), local('Monda-Regular'), url(../fonts/9IGqbwlMn4Zg3as8alsdNA.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215;
}

/* latin */
@font-face {
  font-family: 'Permanent Marker';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local('Permanent Marker Regular'), local('PermanentMarker-Regular'), url(../fonts/Fh4uPib9Iyv2ucM6pGQMWimMp004La2Cfw.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}