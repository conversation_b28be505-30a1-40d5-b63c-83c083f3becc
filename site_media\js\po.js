var isDateChanged = false;
$(function () {
    var isSave = false;
    $('#catAdd').click(function () {
        addCatalogues();
    });
    if ($("#is_blanket_po").val() == "True"){
        $(".blanket_po").removeClass('hide');
    }
    else{
        $(".blanket_po").addClass('hide');
        $(".open_po").addClass('hide');
    }
    $('#supplier').change(function () {
        loadJobPrice();
        if($("#supplier option:selected").val() == "0"){
            $("#modalPartyDetails").modal('show');
            if($("#tab2").hasClass("tab_indent_po")) {
                $("#indents").val(0);
                $("#indents").trigger('chosen:updated');
            }
        }
        else {
            if($("#tab2").hasClass("tab_indent_po")) {
                var table = document.getElementById('po_materials_table');
                var rowcount = Number($('#po_materials_table tfoot tr:visible').length) + Number($('#po_materials_table tbody tr:visible').length);
                if (rowcount > 1) {
                    if(!$("#chknonstockable").is(':checked')){
                        $("#po_materials_table tbody.item-for-goods, #po_materials_table tbody.item-for-service, #po_materials_table tfoot").empty();
                        loadIndentMaterial(supp_id = $("#supplier option:selected").val(), is_job=parseInt($("#po_order").val()), 0);
                    }
                }
            }
        }
        ChangePartyCurrency($("#supplier option:selected").val(),"currency","");
    });

    $('#chknonstockable').change(function() {
        if (!$(this).is(':checked')) {
            $('#nonstockable').hide();
            $('#stockable').show();
            $('#projectheader').prop('disabled', true);
            $('#purpose').prop('readonly', true);
            $('#indents').prop('disabled', false);
            $("#po_materials_table tbody.item-for-goods, #po_materials_table tbody.item-for-service, #po_materials_table tfoot").empty();
            loadIndentMaterial(supp_id="", is_job=parseInt($("#po_order").val()), 0);
        }else{
            $('#nonstockable').show();
            $('#stockable').hide();
            $('#projectheader').prop('disabled', false);
            $('#purpose').prop('readonly', false);
            $('#indents').prop('disabled', true);
            $("#instructions").val("")
        }
        loadProfiledSuppliers();
    });

    $("select#indents").change(function () {
        var selected_indent = document.getElementById('indents');
        if (selected_indent.selectedIndex != 0 ) {
            $('#job_stockable').hide();
            $("#item_particulars a").click();
            $("#other_particulars").addClass("hide");
            $(".blanket_po").addClass('hide');
            $(".for-indent-no").addClass('hide');
            $("#id_purchase-shipping_address").val('');
            $("#id_purchase-shipping_name").val('');
        }
        else {
            $('#job_stockable').show(); 
            $("#item_particulars a").click();
            $("#other_particulars").removeClass("hide");
            if ($("#is_blanket_po").val() == "True"){
                $(".blanket_po").removeClass('hide');
                $(".for-indent-no").removeClass('hide');
            }
        }
        loadProfiledSuppliers();
        load_indent_header();
        $("#po_materials_table tbody.item-for-goods, #po_materials_table tbody.item-for-service, #po_materials_table tfoot").empty();
        loadIndentMaterial(supp_id="", is_job=parseInt($("#po_order").val()), 0);
    });

    if($('#create_id').val() != "" && $('#create_id').val() != "None"){
        loadPendingIndents();
    }

    $("select#pay_type").change(function () {
        if ($("#pay_type option:selected").val() ==1){
            $('#no_of_days').prop('disabled', false);
        }else{
            $('#no_of_days').prop('disabled', true);
            $('#no_of_days').val("");
        }
    });

    $("select#pay_type").click(function () {
        if ($("#pay_type option:selected").val() ==1){
            $('#no_of_days').prop('disabled', false);
        }else{
            $('#no_of_days').prop('disabled', true);
            $('#no_of_days').val("");
        }
    });

    $("#add_po_tax").click(function(){
        tax_code = $("#id_po_tax option:selected").val();
        if (tax_code != ""){
            addPoTax(tax_code);
        }
        $('.chosen-select').trigger('chosen:updated');
        enableEditButton();
    });

    $("#add_po_tag").click(function(){
        var tag_code = $("#po_tag_value").val();
        var tag_text = $("#id_po_tag").val();

        if (tag_code == "") {
            if (tag_text == "") {
                return;
            }
            else {
                $(".li-tagit-display").removeClass('flash');
                var currentFieldName = $("#id_po_tag").val();
                $("#po_tags_table").find('.li-tagit-display:visible').each(function(){
                    if(currentFieldName.toLowerCase().trim() == $(this).find('.label-tagit-display').text().toLowerCase().trim()){
                        $('#id_po_tag').val('');
                        $(this).addClass('flash');
                        return;
                    }
                });

                if($("#id_po_tag").val().trim() !="") {
                    var row = "<li class='li-tagit-display'>" +
                                "<label class='label-tagit-display'>"+$("#id_po_tag").val()+"</label>"+
                                "<div class='hidden-div-tagit-id' hidden='hidden'>0</div>"+
                                "<a class='delete_tag'></a>"  + "</li>";
                    $(row).insertBefore("#id_text_for_tag");
                }
            }
        }
        else {
            $(".li-tagit-display").removeClass('flash');
            var currentFieldName = $("#id_po_tag").val();
            $("#po_tags_table").find('.li-tagit-display:visible').each(function(){
                if(currentFieldName.toLowerCase().trim() == $(this).find('.label-tagit-display').text().toLowerCase().trim()){
                    $('#po_tag_value').val('');
                                        $('#id_po_tag').val('');
                    $(this).addClass('flash');
                    return;
                }
            });

            if($("#id_po_tag").val().trim() !="") {
                var row = "<li class='li-tagit-display'>" +
                            "<label class='label-tagit-display'>"+$("#id_po_tag").val()+"</label>"+
                            "<div class='hidden-div-tagit-id' hidden='hidden'>"+ $("#po_tag_value").val() +"</div>"+
                            "<a class='delete_tag'></a>"  + "</li>";
                $(row).insertBefore("#id_text_for_tag");
            }
        }
        $('#po_tag_value').val('');
        $('#id_po_tag').val('');
        create_delete_tag_button();
    });

    $('#cmdSave, #cmdSaveApprove').click(function () {
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();
        var ControlCollections = [
            {
                controltype: 'dropdown',
                controlid: 'supplier',
                isrequired: true,
                errormsg: 'Supplier Name is required.'
            },
            {
                controltype: 'dropdown',
                controlid: 'projectheader',
                isrequired: true,
                errormsg: 'Project is required.'
            },
            {
                controltype: 'textbox',
                controlid: 'po_order',
                isrequired: true,
                errormsg: 'Type is required.'
            }
        ];
        var result = JSCustomValidator.JSvalidate(ControlCollections);
        if(result){
            var isValidated = false;
            var project_type = $('#projectheader').find(':selected').attr('project-type');
	        if (project_type == "Secondary"){
//	        Temporary Fix
//                if (parseFloat($('#expense').text()) >= parseFloat($('#budgetAmount').text())) {
//                    swal("", "Budget Amount Exceeds.", "error");
//                    return;
//                }
                if (parseFloat($('#cash_allocated').text()) == 0 || (parseFloat($('#cashflow_amount').text()) < parseFloat($('#cash_allocated').text()))) {
                    swal("", "* Actual Cash Flow Amount Exceeds. ", "error");
                    return;
                }
            }
            if($("#materialrequiredjob").val() != "") {
                if($("#job_price").val() == 0 || $("#job_price").val() == "" || $("#job_qty").val() == "" || $("#job_qty").val() == 0 ||  $("#id_ind_material-__prefix__-make_id").val() == "" || $("#materialrequiredjob").val().indexOf($("#id_ind_material-__prefix__-make_id").val()) < 0) {
                  $("#addmaterials").trigger('click');
                  $("material-removal-icon").trigger("click");
                }
                else {
                    $("#addmaterials").trigger('click');
                    isValidated = true;
                }
            }
            else {
                isValidated = true;
            }
        }
        if(isValidated) {
            if(result){
                $(".custom-error-message").remove();
                if($(this).attr("id") == "cmdSave") {
                    insert_Details(1);
                }
                else {
                    insert_Details(4);
                }
            }
            else {
                    $("html, body").animate({ scrollTop: 0 }, "fast");
                    $("#loading").hide();
            }
            return result;
        }
    });


     $('#cmdupdate').click(function () {
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();
        var ControlCollections = [
            {
                controltype: 'dropdown',
                controlid: 'supplier',
                isrequired: true,
                errormsg: 'Supplier Name is required.'
            }
        ];
        var result = JSCustomValidator.JSvalidate(ControlCollections);
        if(result){
            $(".custom-error-message").remove();
            insert_Details(2);
        }
        else {
            $("html, body").animate({ scrollTop: 0 }, "fast");
            $("#loading").hide();
        }
        return result;
    });

    $('#cmdreview').click(function () {
        insert_Details(2);
    });

    $('#cmdcancel').click(function () {
        window.location.assign('/erp/purchase/po_list/');
    });
});

$.extend($.expr[":"], {
    "contains-ci": function (elem, i, match, array) {
        return (elem.textContent || elem.innerText || $(elem).text() || "").toLowerCase().indexOf((match[3] || "").toLowerCase()) >= 0;
    }
});

$(document).ready(function () {
    if($("#id_order_type").val() == "po"){
        $("#po_order").val('0')
    }
    if($("#id_order_type").val() == "jo"){
        $("#po_order").val('1')
    }
    try{
        $('#loading').show();
        $("#dc_report_button").addClass("hide");
        $("#dc_usage_button").addClass("hide");

        var url = window.location.href;
        url = url.substring(7)
        window.urlpath = url.substring(0, url.indexOf('/'));
        action_count = 0;
        materialListBlurEvent('materialrequiredjob');
        loadAllUnits("#id_material-all_units");

        $('#cmdupdate').addClass("hide")
        $('#cmdreview').addClass("hide");

        $("#txtpayment").val("100");
        $("#no_of_days").val("0");
        // Load Po list Approved and Non Approved

        $('#chknonstockable').prop('disabled',false);
        $('#currency').prop('disabled',true);
        $("#po_form").trackChanges();
        SupplierChange();
        CustomerChange();
        $("#id_is_supplier").change(function(){
            SupplierChange();
        });

        $("#id_is_customer").change(function(){
            CustomerChange();
        });

        var select = document.getElementById("currency");
        if (select != null) {
            for (var i = 0; i < select.options.length; i++) {
                if (select.options[i].text == $("#id_home_currency").val()) {
                    select.options[i].selected = true;
                }
            }
        }

        DateChangeEvent();
        loadMaterial("onload");
        $("#po_document_container").html(getPdfLoadingImage());

    } catch(e) {
        console.log(e);
    }
    if(window.location.href.indexOf('#tab2') > 0) {
        if ($("#po_id").val() == "") {
            loadPendingIndents()
        }
        $(".view_po").removeClass('hide');
        $(".create_quick_po, .create_indent_po, .create_po_container, .export_csv").addClass('hide');
        $("#tab2").addClass("tab_indent_po").removeClass("tab_quick_po");
    }
    $('#loading').hide();
});

$(window).load(function(){
    var po_id = $("#po_id").val();
    if (po_id == "" ){
        latestUsedSupplierDetails();
    }
})

function latestUsedSupplierDetails(){
        var selected_supplier_value = null;
        if ($("#supplier option:selected").val() != undefined){
            selected_supplier_value = $("#supplier option:selected").val().split("[::]")[0];
        }
        if (selected_supplier_value != null){
            $.ajax({
                url: "/erp/purchase/json/purchaseLatestUsedSupplierDetails/",
                type: "post",
                datatype:"json",
                data: {party_id: selected_supplier_value},
                success: function(response){
                    party_details = response['party_details']
                    $("#id_purchase-shipping_address").val(party_details['shipping_address'])
                    $("#id_purchase-shipping_name").val(party_details['shipping_name'])
                }
            });
        }
    }

function DateChangeEvent() {
    $('.report-date').on('hide.daterangepicker', function(ev, picker) {
        isDateChanged = true;
    });
}

// request permission on page load
document.addEventListener('DOMContentLoaded', function () {
  if (Notification.permission !== "granted")
    Notification.requestPermission();
});

function showpopup(){
   $("#popup_box").fadeToggle();
   $("#popup_box").css({"visibility":"visible","display":"block"});
}

function hidepopup(){
   $("#popup_box").fadeToggle();
   $("#popup_box").css({"visibility":"hidden","display":"none"});
}

function addPoTax(tax_code){
// TODO Tax Row generation: Reorganise JS
        $.ajax({
        url: "/erp/purchase/json/po/add_tax/",
        type: "POST",
        dataType: "json",
        data: {code: tax_code},
        success:function (response) {
            $.each(response, function(i, tax_detail_as_row){
                                $('#po_taxes_table').append(tax_detail_as_row);
            });
                        calculateTotalPOValue();    // Calculate the Total Price after applying new Tax profile
        },
        error: function (xhr, errmsg, err) {
            alert("Tax population failed!" + xhr.status + ": " + xhr.responseText);
        }
    });
        $("#id_po_tax option:selected").hide(); // Remove the added Tax from the Tax-list to avoid multiple addition
        $("#id_po_tax").val('');    // Reset the Tax Select input
}

function highLightRow(){
    var table = document.getElementById("po_materials_table");
    var rowCount = table.rows.length;
    input_obj = document.getElementsByName('purchase');
    if (rowCount >= 1) {
        for (var i = 0; i < input_obj.length; i++) {
            if (input_obj[i].value > 0) {
                table.rows[i+2].style.backgroundColor="rgba( 75, 181, 67,0.2)";
            }else{
                table.rows[i+2].style.backgroundColor="";
            }
        }
    }
}


function removePoTax(tax_code){
    tax_rows = document.getElementsByName('tr_' + tax_code);
    for(i=tax_rows.length - 1; i>=0; i--){
            // Removed in reverse order as the removal renumbers the RowIndices immediately
            document.getElementById("po_taxes_table").deleteRow(tax_rows[i].rowIndex);
    }
    $("#id_po_tax option[value='"+tax_code+"']").show();    // Re-show the tax in the Tax list
    $('.chosen-select').trigger('chosen:updated');
    calculateTotalPOValue();    // Calculate the PO price after Removing the Tax
}

function loadIndentMaterial(supp_id, is_job, page_view) {
    $("#loading").show();
    var indent_no = $("#indents option:selected").val();
    po_id = $("#po_id").val()
    item_details="";
    var isDeliveryDue = false;
    if($("#is_delivery_schedule").val() == "True") {
        isDeliveryDue = true;
    }
    var isPriceModificationDisabled = false;
    if($("#is_price_modification_disabled").val() == "True") {
        isPriceModificationDisabled = true;
    }
    if (indent_no != "") {
        $.ajax({
            url: "/erp/stores/json/indent/loadindentmaterial/",
            type: "post",
            datatype: "json",
            data: {"indent_no": indent_no, "party_id": supp_id, "is_job":is_job, "po_id":po_id},
            success: function (response) {
                //$("#po_materials_table tbody.item-for-goods, #po_materials_table tbody.item-for-service, #po_materials_table tfoot").empty();
                var has_stocked_material = false;
                var hassuppclass =""
                $.each(response, function (i, item) {
                    balqty = item.quantity - item.pur_qty

                    var table = document.getElementById('po_materials_table')
                    var rowcount = document.getElementById('po_materials_table').rows.length;
                    var item_make = document.getElementsByName('item_make');
                    item_details = item.name
                    if (item.drawing_no != ""  && item.drawing_no !=null) {item_details +=  " - " + item.drawing_no};
                    if (item.make_name != ""){ item_details += " <i>[" + item.make_name + "]</i>"; }
                    if(item.is_service == 1) {item_details = item_details+ "<span class='service-item-flag'></span>";}
                    match = false;

                    for(k=2;k<rowcount;k++){
                        if (item.drawing_no != "" && item.drawing_no !=null){
                            if(trim($(table.rows[k].cells[2]).text()) == trim(item.drawing_no)  && item_make[k-2].value == item.make_id){
                                match = true;
                            }
                        }else{
                            if(trim($(table.rows[k].cells[1]).text()) == trim(item.name)  && item_make[k-2].value == item.make_id){
                                match = true;
                            }
                        }
                    }
                    if (item.price > 0){
                        hassuppclass = 'hassuppmaterial';
                    }
                    else {
                        hassuppclass = 'nosuppmaterial hide';
                    }
                    if($('#create_id').val() != "custom-tab3") {
                        price_attributes = `<input type='text' id='txtpurprice${i}' name='purchaseprice' class='form-control text-right' value ='${item.price}' maxlength='19' onfocus="setNumberRangeOnFocus(this,13,5)" onblur='calculateTotalPOValue();' ${(isPriceModificationDisabled)? "disabled": ""} >`
                    }else{
                        price_attributes = `<input type='text' id='txtpurprice${i}' name='purchaseprice' class='form-control text-right' value ='${item.price}' maxlength='19' onfocus="setNumberRangeOnFocus(this,13,5)" onblur='calculateTotalPOValue();'`
                    }
                    if (match!=true){
                        var deliveryDueJson = [];
                        var deliveryDueObj = {
                            delivery_date: $("#deliverydate").val(),
                            delivery_qty: 0.00
                        };
                        deliveryDueJson.push(deliveryDueObj);
                        var row =   `<tr class='po_material_row ${hassuppclass}'>
                                        <td class='text-center'>
                                            <span class="po-item-sno">${((rowcount-2)+1)}.</span>
                                        </td>
                                        <td class='text-left'>${item_details}</td>
                                        <td hidden=hidden>
                                            <input type='text' name='item_tax' id='tax_sub_total_${i}' value ='0' disabled='true'>
                                            <input type='text' name='item_make' id='item_make_${i}' value=${item.make_id}>
                                            <input type='text' name='item_id' id='item_id_${i}' value=${item.item_id}>
                                            <input type='text' name='item_unit' id='item_unit_${i}' value =${item.unit}>
                                            <input type='text' name='item_spq' id='item_spq_${i}' value ='${item.spq}'>
                                            <input type='text' name='item_moq' id='item_moq_${i}' value ='${item.moq}'>
                                            ${item.drawing_no}
                                            <input type='text' name='alternate_unit_id' id='alternate_unit_id_${i}' value=${item.alternate_unit_id}>
                                        <input type='text' name='scale_factor' id='scale_factor_${i}' value=${item.scale_factor}>
                                    </td>
                                        <td class='for_indent_available text-right hide'>
                                            <span class='td_indent_qty'>${item.quantity.toFixed(3)}</span>
                                            <span class='td_ordered_qty bracket-enclosed'>${item.pur_qty.toFixed(3)}</span>
                                        </td>
                                        <td class='text-right'>
                                            <span class='td_po_qty'>
                                                <input type='text' id='txtpurqty${i}' name='purchase' class='form-control text-right' value ='0' maxlength='16' onfocus="setNumberRangeOnFocus(this,12,3)" onblur='calculateTotalPOValue();' onChange='validateInlineSpq(this);'>
                                            </span>
                                            <span class='for_indent_available td_balance_qty bracket-enclosed hide'>${balqty.toFixed(3)}</span>
                                        </td>
                                        <td class='text-right'>
                                            <span class='td_received_qty'>${item.rec_qty.toFixed(3)}</span>
                                            <span class="td-uom">${item.unit_name}</span>
                                        </td>
                                        <td class='for_indent_po'>
                                            ${price_attributes}
                                        </td>
                                        <td>
                                            <input type='text' id='txtdiscount${i}' name='discount' class='form-control text-right' value ='0' maxlength='6' onfocus="setNumberRangeOnFocus(this,3,2)" onblur='validatePercentage(this, event); calculateTotalPOValue();' >
                                        </td>
                                        <td class='text-right'>
                                            <input type='text' class='form-control text-right' name='unitrate' id='txtunitrate${i}' value ='0' disabled='true'>
                                            <span class='for_indent_po td-approved-price td-sub-content bracket-enclosed'>${item.price.toFixed(2)}</span>
                                        </td>
                                        <td>
                                            <input type='text' class='form-control text-right' name='purchasevalue' id='txtpurvalue${i}' value ='0' disabled='true'>
                                        </td>
                                        <td hidden='hidden'> 0 </td>
                                        <td class='for_indent_po'>
                                            <select class='form-control' name='cgst' onchange='calculateTotalPOValue()' id='txtcgst${i}'>${CGST_rates}</select>
                                            <span name='cgstamt' class='td-sub-content bracket-enclosed' id='txtcgstamt${i}'>0.00</span>
                                        </td>
                                        <td class='for_indent_po'>
                                            <select class='form-control' name='sgst' onchange='calculateTotalPOValue()' id='txtsgst${i}'>${SGST_rates}</select>
                                            <span name='sgstamt' class='td-sub-content bracket-enclosed' id='txtsgstamt${i}'>0.00</span>
                                        </td>
                                        <td class='for_indent_po'>
                                            <select class='form-control' name='igst' onchange='calculateTotalPOValue()' id='txtigst${i}'>${IGST_rates}</select>
                                            <span name='igstamt' class='td-sub-content bracket-enclosed' id='txtigstamt${i}'>0.00</span>
                                        </td>
                                        <td class='${(!isDeliveryDue)? "hide": ""}'>
                                            <span class='edit_delivery_schedule table-inline-icon hide' data-tooltip='tooltip' title='Edit Delivery Schedule' onclick='openDeliveryScheduleParticulars(this, ${i});'>
                                                <img src='/site_media/images/delivery-dispatch.png' />
                                            </span>
                                            <span class='total-delivery-date text-left'>${$("#po_delivery_date").val()}</span>
                                            <span class='td-sub-content bracket-enclosed text-right'>
                                                <span class='next-delivery-date'>${$("#po_delivery_date").val()}</span>
                                                -
                                                <span class='next-delivery-qty'>0.00</span>
                                            </span>
                                        </td>
                                        <td hidden=hidden>
                                            <textarea name='item_delivery_schedules' class='item_delivery_schedules' id='item_delivery_schedules_${i}'>${JSON.stringify(deliveryDueJson)}</textarea>
                                        </td>
                                    </tr>`;
                        if(item.is_service == 1) {
                            $('#po_materials_table tbody.item-for-service').append(row);
                        }
                        else {
                            $('#po_materials_table tbody.item-for-goods').append(row);
                        }
                   }
                });
                if($(".tr-total-row").length <= 0) {
                    var row = `<tr class='tr-view-all-material'>
                                    <td colspan='16' class='text-right'>
                                        <span style='font-size: 12px;'>
                                            <span style='font-size: 16px; color: #209be1;' class='view_all_material' onclick='toggleNonSupplierItems(this)' role='button'>Click here</span>
                                            to <span class='view_all_material_text'>view</span> material without price for the supplier.
                                         </span>
                                    </td>
                                </tr>`;
                    $('#po_materials_table tfoot').append(row);
                    var row =   `<tr class='1 po_material_row tr-total-row'>
                                    <td colspan='6' class='for_quick_po grand-total-text text-right'>Total</td>
                                    <td colspan='7' class='for_indent_po colspan_indent_ava_total grand-total-text text-right'>Total</td>
                                    <td class='text-right'>
                                        <span class='td-grand-total' id='total_price' style='word-break: break-all;'></span>
                                    </td>
                                    <td class='for_indent_po text-right'>
                                        <span class='td-grand-total' id='cgst_total' style='word-break: break-all;'></span>
                                    </td>
                                    <td class='for_indent_po text-right'>
                                        <span class='td-grand-total' id='sgst_total' style='word-break: break-all;'></span>
                                    </td>
                                    <td class='for_indent_po text-right'>
                                        <span class='td-grand-total' id='igst_total' style='word-break: break-all;'></span>
                                    </td>
                                    <td class='${(!isDeliveryDue)? "hide": ""}''></td>
                                </tr>`;
                    $('#po_materials_table tfoot').append(row);
                }
                ViewAllMaterial();
                poMaterialTableSerialNumberInit();
                listTableHoverIconsInit('po_materials_table');
                setTimeout(function(){
                    calculateTotalPOValue();
                    if($("#indents").val() == 0 || $("#indents").val().toLowerCase() == "null" || $("#indent_access").val() =='False'){
                        $(".for_indent_available").addClass("hide");
                        $(".for_indent_unavailable").removeClass("hide");
                        $(".colspan_indent_ava_qty").attr("colspan","2").css("width","150px");
                        $(".colspan_indent_ava_total").attr("colspan","7");
                        $(".tr-view-all-material").find("td").attr("colspan",`${(isDeliveryDue?12:11)}`);
                    }
                    else {
                        $(".for_indent_available").removeClass("hide");
                        $(".for_indent_unavailable").addClass("hide");
                        $(".colspan_indent_ava_qty").attr("colspan","3").css("width","250px");;
                        $(".colspan_indent_ava_total").attr("colspan","8");
                        $(".tr-view-all-material").find("td").attr("colspan",`${(isDeliveryDue?13:12)}`);
                    }
                    $("#loading").hide();
                },1000);
            },
            error: function (xhr, errmsg, err) {
                $("#loading").hide();
            }
        });
    }
    else {
        $("#loading").hide();
    }
}

function indentRemarksHistory(){
    $("#show_indent_remarks_history_modal").modal('show');
}

function load_indent_header(){
    var indent_no = $("#indents option:selected").val() + ",";
        indent_no += $("#supplier option:selected").val();
    if (indent_no != "") {
        $.ajax({
            url: "/erp/stores/json/indent/loadindentheader/",
            type: "post",
            datatype: "json",
            data: {indent_no: indent_no},
            success: function (response) {
                $.each(response, function (i, item) {
                    $("#projectheader").val(item[0]).trigger("chosen:updated");
                    updateProjectChosen(item[0], "projectheader");
                    document.getElementById("purpose").value = item[1];
                    document.getElementById('chknonstockable').checked = item[4] == 0;
                    $("#indent_type").val(item[3]);
                    setIndentRemarks(item[2]);
                    loadProfiledSuppliers();
                    $('#po_delivery_date').val(moment(item[5]).format("MMM DD, YYYY"));
                });// css for update the table row color and border line
            }
        });

        $.ajax({
            url: "/erp/purchase/json/po/load_ind_tags/",
            type: "post",
            datatype: "json",
            data: {indent_no: indent_no} ,
            success: function (response) {
                $("#po_tags_table .li-tagit-display").remove();
                $.each(response, function (i, item) {
                    var row = "<li class='li-tagit-display'>" +
                                "<label class='label-tagit-display'>"+item[0]+"</label>"+
                                "<div class='hidden-div-tagit-id' hidden='hidden'>"+ item[1] +"</div>"+
                                "<a class='delete_tag' ></a>"  + "</li>";
                    $(row).insertBefore("#id_text_for_tag");

                });
                create_delete_tag_button();
            }
        });
    }
    if($("#indents").val() != 0) {
//        $("#projectheader").prop("disabled", true).trigger("chosen:updated");
    }
    else {
        var defaultSelected = $("#projectheader option[selected]").val();
        $("#indent_type #indent_type_default_option").removeAttr("disabled");
        $("#indent_type").val(0);
        $("#purpose, #indent_remarks").val("");
        $("#projectheader").val(defaultSelected).prop("disabled", false).trigger("chosen:updated");
    }
}

function loadProfiledSuppliers() {
    console.log("loading profiled suppliers ");
    loadSuppliers("/erp/stores/json/indent/loadsupplierlist/", null);
}

function loadSuppliers(api_url, indent_no) {
    var isJPO = false;
    if (api_url == null) {
        api_url = "/erp/stores/json/indent/loadsupplierlist/";
    }
    if (indent_no == null) {
        indent_no = $("#indents option:selected").val();
    }
    if($("#po_order").val() == "1") {
        isJPO = true;
    }

    if (indent_no != "" && indent_no != "NULL" && indent_no != 0) {
        console.log("Loading: " + api_url);
        $.ajax({
            url: api_url,
            type: "post",
            datatype: "json",
            data: {'indent_no' : indent_no, 'isjpo':isJPO} ,
            success: function (response) {
                try {
                    var select = document.getElementById("supplier");
                    select.options.length = 0;
                    var option = document.createElement('option');
                        select.add(option, 0);
                    var optgroup_priced = "<optgroup label='Prices profiled for'>";
                    $.each(response[0], function (i, item) {
                        optgroup_priced += "<option value='" + item[0] + "'>" + item[1] + "</option>"
                    });
                    optgroup_priced += "</optgroup>"
                    $('#supplier').append(optgroup_priced);
                    var optgroup = "<optgroup label='Others'>";
                     $.each(response[1], function (i, item) {
                        optgroup += "<option value='" + item[0] + "'>" + item[1] + "</option>"
                    });
                    optgroup += "</optgroup>"
                    $('#supplier').append(optgroup);
                    var option = document.createElement('option');
                        option.text = "+ Add new Supplier";
                        option.id = "cmdSupAdd";
                        option.value = "0";
                        select.add(option, -1);
                    $('#supplier').trigger("chosen:updated");
                } catch(e) {
                    console.log("List", e)
                }
            }
        });
    } else {
        console.log("Indent is not selected");
    }
}

function calculateTotalPOValue() {
    refreshSessionPerNActions(30);
    highLightRow();
    var i;
    // row-count is reduced by 3 to ignore the 3 meta-rows (2 header + 1 Footer)
    pur_quantities = document.getElementsByName('purchase');
    var data_count = pur_quantities.length;
    var simple_rates = document.getElementsByName('net_rate');
    var compound_rates = document.getElementsByName('net_rate_compound');
    var tax_wise_subtotal = document.getElementsByName('tax');
    var compound_tax_wise_subtotal = document.getElementsByName('tax_compound');
    var total = 0;
    purchase_prices = document.getElementsByName('purchaseprice');
    purchase_values = document.getElementsByName('purchasevalue');
    unit_rate = document.getElementsByName('unitrate');
    discount  = document.getElementsByName('discount');
    simple_assess_rates = document.getElementsByName('asses_rate');
    compound_assess_rates = document.getElementsByName('asses_rate_compound');
    item_taxes = document.getElementsByName('item_tax');

    //GST Tax Purpose
    var txt_cgst = document.getElementsByName('cgst');
    var txt_sgst = document.getElementsByName('sgst');
    var txt_igst = document.getElementsByName('igst');

    var txt_cgst_amt = document.getElementsByName('cgstamt');
    var txt_sgst_amt = document.getElementsByName('sgstamt');
    var txt_igst_amt = document.getElementsByName('igstamt');

    var cgst_total = 0;
    var sgst_total = 0;
    var igst_total = 0;

    var txt_other_cgst = document.getElementsByName('other_cgst');
    var txt_other_sgst = document.getElementsByName('other_sgst');
    var txt_other_igst = document.getElementsByName('other_igst');

    var txt_other_cgst_amt = document.getElementsByName('other_cgstamt');
    var txt_other_sgst_amt = document.getElementsByName('other_sgstamt');
    var txt_other_igst_amt = document.getElementsByName('other_igstamt');

    var other_cgst_total = 0;
    var other_sgst_total = 0;
    var other_igst_total = 0;

    var gst_total = 0;
    var other_gst_total= 0;

    //Check the Purchase Qty
    var table = document.getElementById('po_materials_table');
    var poQuantityCheck = true;

    for (i = 0; i < data_count; i++) {
       if (pur_quantities[i].value == "") pur_quantities[i].value = 0;
       if (purchase_prices[i].value == "") purchase_prices[i].value = 0;
       if (discount[i].value == "") discount[i].value = 0;
    }

    for (i = 0; i < data_count; i++) {
        if ($(table.rows[i+2]).find('.td_balance_qty').text() !="-NA-"){
            $(pur_quantities[i]).removeClass("error-border").tooltip('destroy');
            if (parseFloat(pur_quantities[i].value) > parseFloat($(table.rows[i+2]).find('.td_balance_qty').text())) {
                var spqValue = $(table.rows[i+2]).find("input[name='item_spq']").val();
                if(spqValue > 0) {
                    var sqpRoundValue = qty_round_ceil(spqValue, parseFloat($(table.rows[i+2]).find('.td_balance_qty').text()))
                    if(sqpRoundValue != parseFloat(pur_quantities[i].value)) {
                        $(pur_quantities[i]).addClass("error-border").attr("data-tooltip", "tooltip").attr("title", "PO Quantity is greater than Balance Quantity.");
                        TooltipInit();
                        poQuantityCheck = false;
                    }
                }
                else {
                    $(pur_quantities[i]).addClass("error-border").attr("data-tooltip", "tooltip").attr("title", "PO Quantity is greater than Balance Quantity.");
                    TooltipInit();
                    poQuantityCheck = false;
                }

            }
        }
    }

    if(!poQuantityCheck) {
        return;
    }

    for (i = 0; i < data_count; i++) { // row-count is reduced by 2 to ignore the 2 header columns
        if (parseFloat(discount[i].value) != 0){
            pur_value =(parseFloat(pur_quantities[i].value) * parseFloat(purchase_prices[i].value)).toFixed(2);
            dis_value = ((pur_value*parseFloat(discount[i].value))/100);
            purchase_values[i].value = (pur_value-dis_value).toFixed(2);
        }else{
            purchase_values[i].value = (parseFloat(pur_quantities[i].value) * parseFloat(purchase_prices[i].value)).toFixed(2);
        }
        unit_rate[i].value = (parseFloat(purchase_prices[i].value) - ((parseFloat(purchase_prices[i].value) * parseFloat(discount[i].value))/100)).toFixed(2);
        total = (parseFloat(total) + parseFloat(purchase_values[i].value)).toFixed(2);

        //GST Tax Values
        cgst_rate = parseFloat(txt_cgst[i].options[txt_cgst[i].selectedIndex].text);
        if(parseFloat(purchase_values[i].value) > 0 && cgst_rate > 0){
            txt_cgst_amt[i].innerText = ((parseFloat(purchase_values[i].value) * cgst_rate )/100).toFixed(2);
        }else{
            txt_cgst_amt[i].innerText = "0.00";
        }

        sgst_rate = parseFloat(txt_sgst[i].options[txt_sgst[i].selectedIndex].text);
        if(parseFloat(purchase_values[i].value) > 0 && sgst_rate > 0){
            txt_sgst_amt[i].innerText = ((parseFloat(purchase_values[i].value) * sgst_rate )/100).toFixed(2);
        }else{
            txt_sgst_amt[i].innerText = "0.00";
        }

        igst_rate = parseFloat(txt_igst[i].options[txt_igst[i].selectedIndex].text);
        if(parseFloat(purchase_values[i].value) > 0 && igst_rate > 0){
            txt_igst_amt[i].innerText = ((parseFloat(purchase_values[i].value) * igst_rate )/100).toFixed(2);
        }else{
            txt_igst_amt[i].innerText = "0.00";
        }
        cgst_total = cgst_total + (parseFloat(txt_cgst_amt[i].innerText));
        sgst_total = sgst_total + (parseFloat(txt_sgst_amt[i].innerText));
        igst_total = igst_total + (parseFloat(txt_igst_amt[i].innerText));
    }
    $("#total_price").text(total);
    if ($("#cgst_total") != null) {
        $("#cgst_total").text(cgst_total.toFixed(2));
    }
    if ($("#sgst_total") != null) {
        $("#sgst_total").text(sgst_total.toFixed(2));
    }
    if ($("#igst_total") != null) {
        $("#igst_total").text(igst_total.toFixed(2));
    }

    net_tax = 0;
    simple_tax_count = simple_rates.length;
    compound_tax_count = compound_rates.length;

    for (i=0; i<simple_tax_count; i++){tax_wise_subtotal[i].value=0;}
    for (i=0; i<compound_tax_count; i++){compound_tax_wise_subtotal[i].value=0;}
    var simple_tax_total = 0;
    var compound_tax_total = 0;
    var net_tax =0 ;

    for (j = 0; j < data_count; j++) { // row-count is reduced by 2 to ignore the 2 header columns
        if(!isNaN(parseFloat(pur_quantities[j].value)) && parseFloat(pur_quantities[j].value) > 0){
            // Calculating the net Taxes
            item_taxes[j].value = 0;
            for (i=0; i<simple_tax_count; i++){
                var sub_total=0;
                var pur_value =(parseFloat(pur_quantities[j].value) * parseFloat(purchase_prices[j].value)).toFixed(2);
                if (( 100-parseFloat(discount[j].value)) > parseFloat(simple_assess_rates[i].value)){
                    item_total = (pur_value*(100- parseFloat(discount[j].value))/100).toFixed(2);
                }else{
                    item_total = ((pur_value*parseFloat(simple_assess_rates[i].value))/100).toFixed(2);
                }

                sub_total = Math.round(item_total * parseFloat(simple_rates[i].value))/100;
                tax_wise_subtotal[i].value = (parseFloat(tax_wise_subtotal[i].value) + parseFloat(sub_total)).toFixed(2);
                item_taxes[j].value = parseFloat(item_taxes[j].value) + parseFloat(sub_total);
            }
            for (i=0; i<compound_tax_count; i++){
                var pur_value =(parseFloat(pur_quantities[j].value) * parseFloat(purchase_prices[j].value)).toFixed(2);
                if (( 100-parseFloat(discount[j].value)) > parseFloat(compound_assess_rates[i].value)){
                    item_total = (pur_value*(100- parseFloat(discount[j].value))/100).toFixed(2);
                }else{
                    item_total = ((pur_value*parseFloat(compound_assess_rates[i].value))/100).toFixed(2);
                }
                item_total = parseFloat(item_total) + (parseFloat(txt_cgst_amt[j].innerText) + parseFloat(txt_sgst_amt[j].innerText) + parseFloat(txt_igst_amt[j].innerText))
                compound_tax = Math.round((parseFloat(item_total)+parseFloat(item_taxes[j].value)) * parseFloat(compound_rates[i].value))/100;
                item_taxes[j].value = parseFloat(item_taxes[j].value) + parseFloat(compound_tax);
                compound_tax_wise_subtotal[i].value = (parseFloat(compound_tax_wise_subtotal[i].value) + parseFloat(compound_tax)).toFixed(2);
            }
        }
        net_tax = parseFloat(net_tax) + parseFloat(item_taxes[j].value);
    }
    var prev_total = parseFloat(document.getElementById("txttotalpurvalue").value).toFixed(2);
    gst_total = parseFloat(cgst_total)+ parseFloat(sgst_total)+ parseFloat(igst_total);
    other_gst_total = parseFloat(other_cgst_total)+ parseFloat(other_sgst_total)+ parseFloat(other_igst_total);
    document.getElementById("txttotalpurvalue").value = (parseFloat(total) + parseFloat($('#txt_round_off').val()) + gst_total  + other_gst_total + parseFloat(net_tax)).toFixed(2);
        // To Check if there's been a change in any of the values
    if (prev_total != parseFloat($("#txttotalpurvalue").val()).toFixed(2)){
        $("#dirt_flag").click();
    }
}

function addupdate_job(addorupdate) {
    $('#Po_type').val();
    var spq_value = $('#material_spq').val();
    var moq_value = $('#material_moq').val();;
    var request_qty = $("#mat_indent_qty").val();
    if ($('#material_id_hidden').val() != "" && $('#job_qty').val() != "") {
        var i;
        var table = document.getElementById('po_materials_table');
        var rowcount = document.getElementById('po_materials_table').rows.length-1;
        var val1 = "";
        for (i = 1; i < rowcount; i++) {
            if (trim($('#materialrequiredjob').val()) == trim($(table.rows[i].cells[1]).text())) {
                if ($("#id_material-alternate_units:visible").length >= 1){
                    swal("", "Material already Exists!.", "error");
                    return;
                }else{
                    swal({
                        title: "Material already Exists!",
                        text: "Do you still want to add its Quantity?",
                        type: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#209be1",
                        confirmButtonText: "Yes, do it!",
                        closeOnConfirm: true,
                        closeOnCancel: true
                    },
                    function(isConfirm){
                        if (isConfirm) {
                            var currentVal = $(table.rows[i]).find('.td_po_qty').find("input").val();
                            var updatedVal = Number(currentVal) + Number($("#job_qty").val());
                            $(table.rows[i]).find('.td_po_qty').find("input").val(updatedVal.toFixed(2));
                            calculateTotalPOValue();
                            $('#materialrequiredjob').val('').removeAttr("readonly");
                            $('#job_price, #job_qty, #job_discount').val('0.00');
                            $(".material-removal-icon").addClass("hide");
                            return;
                        }
                        else {
                            $('#materialrequiredjob').val('').removeAttr("readonly");
                            $('#job_price, #job_qty, #job_discount').val('0.00');
                            $(".material-removal-icon").addClass("hide");
                            return;
                        }
                    });
                    return;
                }
            }
        }
        var isDeliveryDue = false;
        if($("#is_delivery_schedule").val() == "True") {
            isDeliveryDue = true;
        }
        var isPriceModificationDisabled = false;
        if($("#is_price_modification_disabled").val() == "True") {
            isPriceModificationDisabled = true;
        }
        var po_total=0,po_value=0;
        po_total = parseFloat( $('#job_qty').val()) * parseFloat(trim($('#job_price').val()));
        if ((parseFloat($('#job_discount').val())!= 0 && parseFloat($('#job_discount').text())!= "") && $('#job_discount').val()!= "" ) {
            discount = parseFloat(po_total) * parseFloat($('#job_discount').val())/100;
        }else{
            discount = 0;
        }
        po_value = po_total-discount;
        var rowcount = document.getElementById('po_materials_table').rows.length-2;
        if (rowcount==0){
            var row = ` <tr class='po_material_row tr-total-row' bgcolor='#ececec' border='0' align='center' style='font-size:12px; font-weight:normal;'>
                            <td colspan='6' class='for_quick_po grand-total-text text-right'>Total</td>
                            <td colspan='7' class='for_indent_po colspan_indent_ava_total grand-total-text text-right'>Total</td>
                            <td class='text-right'>
                                <span class='td-grand-total' id='total_price'></span>
                            </td>
                            <td class='for_indent_po text-right'>
                                <span class='td-grand-total' id='cgst_total'></span>
                            </td>
                            <td class='for_indent_po text-right'>
                                <span class='td-grand-total' id='sgst_total'></span>
                            </td>
                            <td class='for_indent_po text-right'>
                                <span class='td-grand-total' id='igst_total'></span>
                            </td>
                            <td class='${(!isDeliveryDue)? "hide": ""}'>
                        </tr>
                        <tr></tr>`;
            $('#po_materials_table tfoot').append(row).addClass('tbl');
            rowcount=rowcount+1;
        }
        make_id=$('#id_ind_material-__prefix__-make_id').val();
        item_unit=$('#job_unit option:selected').val();
        drawing_no=trim($('#materialrequiredjob').val());
        item_id=trim($('#material_id').val());
        var insertRowCount = document.getElementById('po_materials_table').rows.length-3;
        var unit_name = ""
        var alternate_unit_id = 0
        var scale_factor = 1
        if ($('#id_material-alternate_units').val() != 0 && $('#id_material-alternate_units').val() != null ) {
            unit_name = $('#id_material-alternate_units option:selected').text()
            alternate_unit_id = $('#id_material-alternate_units').val()
            scale_factor = $('#id_material-alternate_units option:selected').attr("data-val")
        }else{
            unit_name = $('#job_unit option:selected').text()
        }

        var deliveryDueJson = [];
        var deliveryDueObj = {
            delivery_date: $("#deliverydate").val(),
            delivery_qty: $('#job_qty').val().trim()
        };
        deliveryDueJson.push(deliveryDueObj);
        var item_material_list = trim($("#materialrequiredjob").val());
        if($("#material_is_service").val() == 1){
           item_material_list = item_material_list+ "<span class='service-item-flag'></span>";
        }
        if($('#create_id').val() != "custom-tab3") {
            price_attributes = `<input type='text' id='txtpurprice${i}' name='purchaseprice' class='form-control text-right' value ='${trim($('#job_price').val())}' maxlength='19' onfocus="setNumberRangeOnFocus(this,13,5)" onblur='calculateTotalPOValue();' ${(isPriceModificationDisabled)? "disabled": ""}>`
        }else{
            price_attributes = `<input type='text' id='txtpurprice${i}' name='purchaseprice' class='form-control text-right' value ='${trim($('#job_price').val())}' maxlength='19' onfocus="setNumberRangeOnFocus(this,13,5)" onblur='calculateTotalPOValue();'>`
        }
        var row = `<tr class='po_material_row'>
                    <td class='text-center'>
                        <span class='table-inline-icon-bg po-item-sno'>${insertRowCount}.</span>
                        <span class='table-inline-icon hide' data-tooltip='tooltip' title='Delete' onclick='deleteItemMaterial(this);'>
                            <i class='fa fa-trash-o for_indent_unavailable'></i>
                        </span>

                    </td>
                    <td>${item_material_list}</td>
                    <td hidden=hidden>
                        <input type='text' name='item_unit' id='item_unit_${i}' value ='${item_unit}'>
                        <input type='text' name='item_make' id='item_make_${i}' value ='${make_id}''>
                        <input type='text' name='item_id' id='item_id_${i}' value ='${item_id}'>
                        <input type='text' name='alternate_unit_id' id='alternate_unit_id_${i}' value ='${alternate_unit_id}'>
                        <input type='text' name='scale_factor' id='scale_factor_${i}' value ='${scale_factor}'>
                        <input type='text' name='item_tax' id='tax_sub_total_${insertRowCount}' value ='0' disabled='true'>
                        <input type='text' name='item_spq' id='item_spq_${i}' value ='${spq_value}'>
                        <input type='text' name='item_moq' id='item_moq_${i}' value ='${moq_value}'>
                        ${$("#material_id_hidden").val()}
                    </td>
                    <td class='for_indent_available text-right hide'>
                        <span class='td_indent_qty'>-NA-<span>
                        <span class='td_ordered_qty bracket-enclosed'>-NA-</span>
                    </td>
                    <td class='text-right'>
                        <span class='td_po_qty'>
                            <input type='text' id='txtpurqty_${i}' name='purchase' class='form-control text-right' value ='${trim($('#job_qty').val())}' maxlength='16' onfocus="setNumberRangeOnFocus(this,12,3)" onblur='calculateTotalPOValue();' onChange='validateInlineSpq(this);'>
                        </span>
                        <span class='for_indent_available td_balance_qty bracket-enclosed hide'>-NA-</span>
                    </td>
                    <td class='for_indent_po text-right'>
                        <span class='td_received_qty'>0.00</span>
                        <span class="td-uom">${unit_name}</span>
                    </td>
                    <td>
                        ${price_attributes}
                    </td>
                    <td>
                        <input type='text' id='txtdiscount${i}' name='discount' class='form-control text-right' value ='${trim($('#job_discount').val())}' maxlength='6' onfocus="setNumberRangeOnFocus(this,10,5)" onblur='validatePercentage(this, event); calculateTotalPOValue();'>
                    </td>
                    <td class='text-right'>
                        <input type='text' id='txtunitrate${i}' name='unitrate' class='form-control text-right' value ='0' disabled='true'>
                        <span class='for_indent_po td-approved-price td-sub-content bracket-enclosed'>0.00</span>
                    </td>
                    <td>
                        <input type='text' id='txtpurvalue${i}' name='purchasevalue' class='form-control text-right' value ='0.00' disabled='true'>
                    </td>
                    <td hidden='hidden'></td>
                    <td class='for_indent_po'>
                        <select class='form-control' name='cgst' onchange='calculateTotalPOValue()' id='txtcgst${i}'>${CGST_rates}</select>
                        <span name='cgstamt' class='td-sub-content bracket-enclosed' id='txtcgstamt${i}'>0.00</span>
                    </td>
                    <td class='for_indent_po'>
                        <select class='form-control' name='sgst' onchange='calculateTotalPOValue()' id='txtsgst${i}'>${SGST_rates}</select>
                        <span name='sgstamt' class='td-sub-content bracket-enclosed' id='txtsgstamt${i}'>0.00</span>
                    </td>
                    <td class='for_indent_po'>
                        <select class='form-control' name='igst' onchange='calculateTotalPOValue()' id='txtigst${i}'>${IGST_rates}</select>
                        <span name='igstamt' class='td-sub-content bracket-enclosed' id='txtigstamt${i}'>0.00</span>
                    </td>
                    <td class='${(!isDeliveryDue)? "hide": ""}'>
                        <span class='edit_delivery_schedule table-inline-icon hide' data-tooltip='tooltip' title='Edit Delivery Schedule' onclick='openDeliveryScheduleParticulars(this, ${i});'>
                            <img src='/site_media/images/delivery-dispatch.png' />
                        </span>
                        <span class='total-delivery-date text-left'>${$("#po_delivery_date").val()}</span>
                        <span class='td-sub-content bracket-enclosed text-right'>
                            <span class='next-delivery-date'>${$("#po_delivery_date").val()}</span>
                            -
                            <span class='next-delivery-qty'>${$("#job_qty").val()}</span>
                        </span>
                    </td>
                    <td hidden=hidden>
                        <textarea name='item_delivery_schedules' class='item_delivery_schedules' id='item_delivery_schedules_${i}'>${JSON.stringify(deliveryDueJson)}</textarea>
                    </td>
                </tr>`;
        if($("#material_is_service").val() == 1){
            $("#po_materials_table tbody.item-for-service").append(row)
        }
        else {
            $("#po_materials_table tbody.item-for-goods").append(row)
        }
                
        //$(row).insertBefore('#po_materials_table tbody tr:nth-child('+insertRowCount+')');
        poMaterialTableSerialNumberInit();
        calculateTotalPOValue();
        validateTextInputs();
        listTableHoverIconsInit('po_materials_table');
        $('#job_price, #job_qty, #job_discount').val('0.00');
        $('#id_material-alternate_units').html("");
        $("#unit_display").html("&nbsp;");
        $(".alternate_unit_select_box").addClass("hide");
        $('#materialrequiredjob').val('').focus().removeAttr("readonly");
        $('#material_id_hidden').val('').focus();
        $(".material-removal-icon").addClass("hide");
        if (addorupdate == 2) {
            $('#cmdupdate').addClass("hide")
            $('#cmddelete').hide();
            $('#cmdadd').show();
        }
    }
    else {
        if ($('#material_id_hidden').val() == "" && $('#materialrequiredjob').val() != ""){
            var materialName = $("#materialrequiredjob").val();
            var materialUnit = $('#id_material-all_units').val();
            var materialPrice = $('#job_price').val();
            addNewMaterial(materialName, materialUnit, "", materialPrice);
        }
    }
}

function openDeliveryScheduleParticulars(current, id){
    $("#deliveryScheduleParticularsTable").find("tbody").find("tr").remove();
    $("#deliveryScheduleParticulars").modal("show");
    if($("#item_delivery_schedules_"+id).val() != ''){
         $.each(JSON.parse($("#item_delivery_schedules_"+id).val()), function (i, item){
            var row = `<tr data-order='${moment(item.delivery_date).format("YYYYMMDD")}'>
                            <td class='hide td_unique_id'>
                                ${id}
                            </td>
                            <td class='text-center td_delivery_date'>
                                ${moment(item.delivery_date).format("MMM DD, YYYY")}
                            </td>
                            <td class='text-right td_delivery_quantity'>
                                ${Number(item.delivery_qty).toFixed(2)}
                            </td>
                            <td class="text-center">
                                <img role="button" class='img-split' src="/site_media/images/split.png" data-tooltip="tooltip" title="" onclick="splitDeliverySchedule(this)" data-original-title="Split / Move">
                            </td>
                        </tr>`;
            $("#deliveryScheduleParticularsTable").find("tbody").append(row);
         });
    }
    else {
      var row = `<tr data-order='${moment($("#po_delivery_date").val()).format("YYYYMMDD")}'>
                    <td class='hide td_unique_id'>
                        ${id}
                    </td>
                    <td class='text-center td_delivery_date'>
                        ${$("#po_delivery_date").val()}
                    </td>
                    <td class='text-right td_delivery_quantity'>
                        ${$(current).closest("tr").find(".td_po_qty").find("input").val()}
                    </td>
                    <td class="text-center">
                        <img role="button" class='img-split' src="/site_media/images/split.png" data-tooltip="tooltip" title="" onclick="splitDeliverySchedule(this)" data-original-title="Split / Move">
                    </td>
                </tr>`;
      $("#deliveryScheduleParticularsTable").find("tbody").append(row);
    }
}

function splitDeliverySchedule(current){
    if($("#deliveryScheduleParticularsTable .deliverydue-temporary-row").length <= 0) {
        var row = ` <tr class='deliverydue-temporary-row'>
                        <td>
                            <input type="text" id="deliveryDueDate" class="form-control hide" placeholder="Select Date" />
                            <input type="text" class="form-control custom_datepicker full-datepicker fixed-width-medium" placeholder="Select Date" id="delivery_due_date" readonly="readonly">
                            <i class="glyphicon glyphicon-calendar custom-calender-icon" style="margin-left: 1px; margin-top: -34px;"></i>
                        </td>
                        <td>
                            <input type="text" id="deliveryDueQty" class="form-control text-right" placeholder="Quantity" value="0.00" maxlength="16" onfocus="setNumberRangeOnFocus(this,12,3)">
                        </td>
                        <td class="text-center">
                            <button class="btn btn-save" onclick="addNewDeliverySchedule()">
                                <i class="fa fa-check" aria-hidden="true"></i>
                            </button>
                            <button class="btn btn-delete" onclick="cancelNewDeliverySchedule()">
                                <i class="fa fa-times" aria-hidden="true"></i>
                            </button>
                        </td>
                    </tr>`;
        $(current).closest("tr").addClass("active-tr");
        $('#deliveryScheduleParticularsTable tbody tr').not(".active-tr").addClass('blurred-tr');
        $( row ).insertAfter($(current).closest("tr"));
        CustomDatePickerInit();
    }
}

function addNewDeliverySchedule(){
    $(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();
    var ControlCollections = [
        {
            controltype: 'textbox',
            controlid: 'deliveryDueQty',
            isrequired: true,
            errormsg: '',
            mindigit: 0.001,
            mindigiterrormsg: ''
        }
    ];
    var result = JSCustomValidator.JSvalidate(ControlCollections);
    if(result) {
        var baseQty = $(".active-tr").find(".td_delivery_quantity").text().trim();
        var baseQtyDate = $(".active-tr").find(".td_delivery_date").text().trim();
        var currentQty = $("#deliveryDueQty").val().trim();
        var currentQtyDate = $("#delivery_due_date").val().trim();
        var uid = $("#deliveryScheduleParticularsTable").find("td.td_unique_id:first").text().trim();
        if(Number(currentQty) > Number(baseQty)) {
            swal("","The entered quantity is greater than the base quantity.","warning")
        }
        else {
            var isMatch = false;
            $("#deliveryScheduleParticularsTable").find("tbody tr").each(function(){
                if($(this).find(".td_delivery_date").text().trim() == currentQtyDate) {
                    var consolidateQty =Number(currentQty) + Number($(this).find(".td_delivery_quantity").text().trim());
                    $(this).find(".td_delivery_quantity").text(consolidateQty.toFixed(3));
                    isMatch = true;
                }
            });
            if(!isMatch) {
                var row = `<tr data-order='${moment(currentQtyDate).format("YYYYMMDD")}'>
                                <td class='hide td_unique_id'>
                                    ${uid}
                                </td>
                                <td class='text-center td_delivery_date'>
                                    ${currentQtyDate}
                                </td>
                                <td class='text-right td_delivery_quantity'>
                                    ${currentQty}
                                </td>
                                <td class="text-center">
                                    <img role="button" class='img-split' src="/site_media/images/split.png" data-tooltip="tooltip" title="" onclick="splitDeliverySchedule(this)" data-original-title="Split / Move">
                                </td>
                            </tr>`;
                $("#deliveryScheduleParticularsTable").find("tbody").append(row);
            }
            $(".active-tr").find(".td_delivery_quantity").text(Number($(".active-tr").find(".td_delivery_quantity").text() - Number(currentQty)).toFixed(3));
            $(".deliverydue-temporary-row").remove();
            $(".active-tr").removeClass("active-tr");
            $(".blurred-tr").removeClass("blurred-tr");

            var $tbody = $('#deliveryScheduleParticularsTable tbody');

            $tbody.find('tr').sort(function(a, b) {
              var tda = $(a).attr('data-order');
              var tdb = $(b).attr('data-order');
              return tda > tdb ? 1
                : tda < tdb ? -1
                : 0;
            }).appendTo($tbody);

            $tbody.find("tr").each(function(){
                if($(this).find(".td_delivery_quantity").text().trim() == 0) {
                    $(this).remove();
                }
            });
            updateNewDeliverySchedule();
        }
    }
}

function cancelNewDeliverySchedule(){
    $(".deliverydue-temporary-row").remove();
    $(".active-tr").removeClass("active-tr");
    $(".blurred-tr").removeClass("blurred-tr");
}

function updateNewDeliverySchedule(){
    var jsonconvert = [];
    var uid = 0;
    var dates = [];
    $("#deliveryScheduleParticularsTable").find("tbody tr").not('.deliverydue-temporary-row').each(function(index, tr) {
        jsonconvert[index] = {};
        jsonconvert[index]['delivery_date'] = moment($(this).find('td.td_delivery_date').text().trim()).format("YYYY-MM-DD");
        jsonconvert[index]['delivery_qty'] = $(this).find('td.td_delivery_quantity').text().trim();
        uid = ($(this).find('td.td_unique_id').text() != '' ? $(this).find('td.td_unique_id').text().trim() : uid);
        dates.push($(this).find('td.td_delivery_date').text().trim());
    });
     $('#item_delivery_schedules_'+uid).text(JSON.stringify(jsonconvert));
    var today = new Date();
    var closest = Infinity;

    dates.forEach(function(d) {
       var date = new Date(d+" 23:59:59");
       if (date >= today && (date < new Date(closest) || date < closest)) {
          closest = d;
       }
    });
    var lastDate = $("#deliveryScheduleParticularsTable").find("tbody tr:last").find(".td_delivery_date").text();
    var currentTR = $('#item_delivery_schedules_'+uid).closest("tr");
    if(closest != Infinity) {
        var closestDate = moment(closest).format("YYYYMMDD");
        var closestQty = $("#deliveryScheduleParticularsTable").find("tbody tr[data-order='"+closestDate+"']").find(".td_delivery_quantity").text().trim();
        currentTR.find(".next-delivery-date").text(moment(closestDate).format("MMM D, YYYY"));
        currentTR.find(".next-delivery-qty").text(closestQty);
    }
    else {
        currentTR.find(".next-delivery-date").text("");
        currentTR.find(".next-delivery-qty").text("");
    }
    currentTR.find(".total-delivery-date").text(lastDate);
    $('#po_delivery_date').val(moment(lastDate).format("MMM D, YYYY"));
    $('#deliverydate').val(moment(lastDate).format("YYYY-MM-DD"));
}

function showConsolidatedDeliverySchedule(){
    $("#consolidatedDeliverySchedule").modal("show");
}

function deleteItemMaterial(selectedItem){
    swal({
        title: "Are you sure!",
        text: "Do you want to delete this Material?",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#209be1",
        confirmButtonText: "Yes, do it!",
        closeOnConfirm: true,
        closeOnCancel: true
    },
    function(){
        $(selectedItem).closest("tr").remove();
        calculateTotalPOValue();    
    });
}


function clearTable() {
    $("#po_materials_table tbody.item-for-goods, #po_materials_table tbody.item-for-service").empty();
    clear_po_tax_table();
}

function clear_po_tax_table() {
    var table = document.getElementById("po_taxes_table");
    var rowCount = table.rows.length;
    if (rowCount >= 0) {
        for (var i = 0; i < rowCount; i++) {
            table.deleteRow(i);
            rowCount--;
            i--;
        }
    }
}

function closeCatalogues() {
    document.getElementById("catcss").style.display = "none";
}

function addCatalogues() {
    var table = document.getElementById("cattable");
    var rowCount = table.rows.length;
    var input_obj = document.getElementsByName('catmaterial');
    if (rowCount > 1) {
        for (i = 1; i < rowCount - 1; i++) {
            var row = "<tr><td>" +
                $(table.rows[i].cells[0]).text() + "</td><td>" +
                input_obj[i - 1].value + "</td><td>" +
                "<a href='#' onclick='deleteRow(this)'>Delete </a>" + "</td><td hidden='hidden'>" +
                $(table.rows[i]).find('.td_indent_qty').text() + "</td><td hidden='hidden'>" +
                $(table.rows[i]).find('.td_ordered_qty').text() + "</td></tr>"
            $('#po_materials_table').append(row).addClass('tbl');
        }
    }
    document.getElementById("catcss").style.display = "none";
}

function deleteRow(currentRow) {
    try {
        if (window.confirm('Do you want delete this row?')) {
            var table = document.getElementById("po_materials_table");
            var rowCount = table.rows.length;
            for (var i = 0; i < rowCount; i++) {
                var row = table.rows[i];
                if (row == currentRow.parentNode.parentNode) {
                    if (rowCount <= 1) {
                        alert("Cannot delete all the rows.");
                        break;
                    }
                    table.deleteRow(i);
                    rowCount--;
                    i--;
                }
            }
        } else {
            e.preventDefault();
        }
                calculateTotalPOValue();
    } catch (e) {
        console.log(e);
    }
}

function deleteCatRow(currentRow) {
    try {
        if (window.confirm('Do you want delete this row?')) {
            var table = document.getElementById("cattable");
            var rowCount = table.rows.length;
            for (var i = 0; i < rowCount; i++) {
                var row = table.rows[i];
                if (row == currentRow.parentNode.parentNode) {
                    if (rowCount <= 1) {
                        swal("", "Cannot delete all the rows.", "warning");
                        break;
                    }
                    table.deleteRow(i);
                    rowCount--;
                    i--;
                }
            }
        } else {
            e.preventDefault();
        }
    } catch (e) {
        console.log(e);
    }
}


function showReport() {
    jop_po_id=$("#id_job_po").val()
    $.ajax({
        url: "/erp/stores/json/dc/job_dc_supplied/",
        type: "post",
        datatype: "json",
        data: {jop_po_id : jop_po_id },
        success: function(response){
            $("#dc_invoice_list").find("tbody").html('');
            $.each(response, function (i, item){
                var csrf_token = document.getElementsByName('csrfmiddlewaretoken')[0].value;
                var item_name=""
                var itemTypeFlag=""
                var make_name = constructDifferentMakeName(item.make);
                if (make_name != "") {
                    item_name  = item.item_code + " [" + make_name + "]"
                }else{
                    item_name  = item.item_code
                }
                if (item.is_faulty != 0){
                    item_name += " [Faulty]"
                }
                if(item.is_service == true){
                    itemTypeFlag = `<span class="service-item-flag"></span>`;
                }
                if(item.is_stock==0 && item.is_service != true){
                    itemTypeFlag = `<span class="non_stock-flag"></span>`;
                }
                var pdf_generation_form = "<form target='_blank' id='pdf_generation' method='POST' action='/erp/sales/invoice/'>"+
                                "<input type=\'hidden\' name=\'csrfmiddlewaretoken\' value=\'" +csrf_token+ "\' />"+
                                "<input name='invoice_no' value='"+ item.invoice_id +"' hidden/>"+
                                "<input name='edit_dc_type' value='dc' hidden/>"+
                                "<a href=\"#\" onclick=\"javascript:clickButton(\'editInvoice_" + item.invoice_id + "\');\">"+item.code+"</a>" +
                                "<input type='submit' hidden id='editInvoice_"+ item.invoice_id + "' /></form>";
                row="<tr align='center'>"+
                    "<td width='5%'>"+(i+1)+"</td>"+
                    "<td width='12%'>"+ pdf_generation_form +"</td>"+
                    "<td width='15%'>"+ item_name +" "+itemTypeFlag+"</td>"+
                    "<td width='15%'>"+item.hsn_code+"</td>"+
                    "<td width='20%' align='left'>" +item.quantity+"</td>"+
                    "<td width='20%' align='left'>" +item.uom+"</td>"+
                    "</tr>";
                $('#dc_invoice_list').append(row);
            });
            if($("#dc_invoice_list tbody").find("tr").length <= 0) {
                var row = "<tr><td colspan='8' class='text-center' style='font-weight: bold;'>No Records Found!</td></tr>";
                $('#dc_invoice_list').append(row);
            }
        }
    });
    $("#dcReport").modal('show');
}

function saveReturnItem() {
    po_id=$("#id_job_po").val();
    var material_list = []
    $('#cattable_3 tbody tr').each(function(){
        return_to_qty = $(this).find('.po-return-qty').val();
        if (return_to_qty > 0) {
            item_id = $(this).find(".item-id-content").attr("data-item-id");
            make_id = $(this).find(".make-id-content").attr("data-make-id");
            if (make_id == "undefined" || make_id == ""){
                make_id = "1"
            }
            material = {
                quantity: parseFloat(return_to_qty).toFixed(3),
                item_id: item_id,
                make_id: make_id }
            material_list.push(material)
        }
    });
    var jo_dict = {
                pl_id: "",
                pp_id: po_id,
                mi_id: "",
                mi_no: "",
                mi_date: "",
                inward_date: moment($("#pp_log_date").val()).format("YYYY-MM-DD"),
                items: material_list
        }
    $.ajax({
            url: "/erp/purchase/json/po/save_return_qty/",
            type: "post",
            datatype: "json",
            data: {jo_detail:JSON.stringify(jo_dict) },
            async: false,
            success: function(response){
                    if(response.response_message.toLowerCase() == "success" && response.production_log_details != false) {
                        swal("SUCCESS", `IRN has been Created successfully`, "success");
                    }
                    else {
                        swal("Failure", `Due to technical issue, unable to request the process.<br />Please contact support.`, "warning")
                    }
                },
            error: function (xhr, errmsg, err) {
                swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
            }
        });
}

function showUsageReport() {
    jop_po_id=$("#id_job_po").val();
    $("#cattable_3 tbody tr").remove();
    $("#loadingmessage").show();
    $("#dcUsageReport").modal('show');
    setTimeout(function(){
        $.ajax({
            url: "/erp/stores/json/dc/job_dc_usage/",
            type: "post",
            datatype: "json",
            data: {po_id:jop_po_id},
            async: false,
            success: function(response){
                if (response.response_code!=400 && response.length != 0){
                    $("#cattable_3").find("tbody").remove();
                    $("#catButton").find('.modal-footer').remove();
                    $.each(response, function (i, item) {
                        if(typeof(Storage) !== "undefined") {
                            sessionStorage.clickcount = (sessionStorage.clickcount ? Number(sessionStorage.clickcount)+1 : 1)
                        }
                        var childArrow = "";
                        var is_service_value = item.is_service==true?1:0;
                        var is_service = '<input hidden="hidden" name="catmaterial_is_service" value='+is_service_value+' >';
                        var itemTypeFlag = "";
                        var material_name = item.name;
                        if (item.material_type==1){
                            classname='no-bg';
                            material_name = childArrow;
                        } else{ classname='tr-alert-danger' }
                        if(item.is_service == true){
                            itemTypeFlag = `<span class="service-item-flag"></span>`;
                        }
                        if(!item.material_type && item.is_service != true) {
                            itemTypeFlag = `<span class="non_stock-flag"></span>`;
                        }
                        if(item.child.length > 0) {
                            childArrow = "<i class='fa fa-plus fa-for-arrow' role='button' onclick='return appendMaterial(\""+ item.item_id +"\", \""+ sessionStorage.clickcount +"\", \""+item.item_id+ "_" + sessionStorage.clickcount +"\", \""+jop_po_id +"\")'><a style='padding-left: 26px; display: block; margin-top: -12px;' >"+item.name+" "+itemTypeFlag+"</a></i>";
                        } else { childArrow = item.name+" "+itemTypeFlag
                        }
                        var toBeSupplied = (item.qty + item.returned) - item.supplied
                        var excessSupplied = (item.supplied - item.returned) - item.qty
                        if (excessSupplied < 0) {
                            excessSupplied = excessSupplied * -1
                        }
                        if (toBeSupplied > 0 ){
                            excessSupplied = 0
                        }
                        if (excessSupplied > 0 ){
                            toBeSupplied = 0
                        }
                        if(item.drawing_no == null ){
                           item.drawing_no = "";
                        }
                        if(item.material_type == 0 && item.is_service != true) {
                           itemTypeFlag += `<span class="non_stock-flag"></span>`;
                        }
                        var makes = "";
                        if (item.makes.length > 0){
                            makes = ``;
                            for (j = 0; j <= item.makes.length - 1 ; j++) {
                                if (item.makes[j][1] == "") { item.makes[j][1] = "-NA-"}
                                makes += `<span> ${item.makes[j][1]}</span>`;
                            }
                        }
                        else{
                            makes = item.make_name;
                        }
                        var description = item.cat_code +" - " +item.drawing_no
                        var row = "<tr data-toggle='close' data-padding='0' data-parent='"+item.item_id+ "_" + sessionStorage.clickcount + "' id=\""+ item.item_id + "_" + sessionStorage.clickcount +
                                            "\" bgcolor='#ececec' border='1' align='left' style='font-size:12px; font-weight:normal;'>"+
                                        "<td hidden=hidden class='exclude_export item-id-content' data-item-id ='"+item.item_id+"'> "+item.item_id+" </td>" +
                                        "<td hidden=hidden class='exclude_export make-id-content' data-make-id ='"+item.make_id+"'> "+item.make_id+" </td>" +
                                        "<td hidden=hidden class='exclude_export'>" + description +"</td>"+
                                        "<td class='text-center bom-sno'>" + (i+1) + "</td>"+
                                        "<td width='59%'>"+ childArrow +"</td>"+
                                        "<td width='5%' class='bom-expected-qty'>" + item.qty.toFixed(3) + "</td>"+
                                        "<td width='5%' class='bom-supplied-qty'>" + item.supplied.toFixed(3) + "</td>"+
                                        "<td width='5%' class='bom-returned-qty'>" + item.returned.toFixed(3) + "</td>"+
                                        "<td width='5%' class='bom-toBeSupplied-qty'>" + toBeSupplied.toFixed(3) + "</td>"+
                                        "<td width='5%' class='bom-excessSupplied-qty'>" + excessSupplied.toFixed(3) + "</td>"+
                                        "<td width='10%' class='exclude_export bom-return-to-qty'>"+"<input type='textbox' name='returnQty' value=0 class='po-return-qty form-control'>"+"</td>" +
                                    "</tr   >";
                        $('#cattable_3').append(row).addClass('tbl');
                    });
                    $("#loadingmessage").hide();
                    TooltipInit();
                }
            }
        });
    },200);
}

function appendMaterial(cat_code, dataChild, dataP, jop_po_id) {
    var currentCatChild = $("#"+cat_code + "_" + dataChild).attr('id');
    var current_bom_sno = $("#"+cat_code + "_" + dataChild).find(".bom-sno").text().trim();
    var constructedRow = '';
    var currentRow = $("#cattable_3").find(`tr#${cat_code}_${dataChild}`);
    if($("#"+currentCatChild).attr('data-toggle') == 'open') {
        $(currentRow).removeClass("strikeout");
        $("#return_to_qty").addClass('hide');
        var curPadding = $("#"+currentCatChild).attr('data-padding');
        $("#"+currentCatChild).nextAll('tr').each(function(){
            if(Number($(this).attr('data-padding')) > Number(curPadding)) {
                $(this).remove();
            }
            else {
                return false;
            }
        });
        $("#cattable_3 #"+cat_code + "_" + dataChild).find('.fa.fa-for-arrow').removeClass('fa-minus').addClass('fa-plus');
        $("#cattable_3 #"+cat_code + "_" + dataChild).attr('data-toggle','close');
    }
    else {
        $("#cattable_3 #"+cat_code + "_" + dataChild).find('.fa.fa-for-arrow').removeClass('fa-plus').addClass('fa-minus');
        $("#cattable_3 #"+cat_code + "_" + dataChild).attr('data-toggle','open');
        $(currentRow).addClass("strikeout");
        $.ajax({
            url: "/erp/stores/json/catalogue_materials/",
            type: "post",
            dataType: "json",
            data: {'cat_code': cat_code},
            success: function (response) {
                $.each(response, function (i, item) {
                var currentRow = $("#cattable_3").find(`tr#${cat_code}_${dataChild}`);
                var baseExpectedQty = currentRow.find(".bom-expected-qty").text();
                var suppliedQty = 0
                var returnedQty = 0
                var toBeSuppliedQty = 0
                var excessSuppliedQty = 0
                if(typeof(Storage) !== "undefined") {
                    if (sessionStorage.clickcount) {
                        sessionStorage.clickcount = Number(sessionStorage.clickcount)+1;
                    } else {
                        sessionStorage.clickcount = 1;
                    }
                }
                $.ajax({
                    url: "/erp/purchase/json/po/get_dc_materials/",
                    type: "post",
                    dataType: "json",
                    async: false,
                    data: {'po_id': jop_po_id, 'item_id': item.item_id},
                    success: function (response) {
                        suppliedQty = response["supplied_qty"];
                        returnedQty = response["returned_qty"];
                    }
                });
                var childArrow ="";
                var makes = "-NA-";
                var dataParent = dataP;
                var dataPadding = Number($("#"+cat_code + "_" + dataChild).data('padding') + 30);
                var drawing_no = "";
                var isStockableIcon = "";
                var isServiceIcon = "";
                var is_service = '<input hidden="hidden" name="catmaterial_is_service" value='+item.is_service+' >';

                if (item.drawing_no !="" && item.drawing_no!=null) {
                    drawing_no = item.drawing_no;
                }
                if(item.make_name != ""){
                    var item_name = item.name + " [" + item.make_name + "]";
                }else{
                    var item_name = item.name
                }

                if(item.is_service == true){
                    isServiceIcon = `<span class="service-item-flag"></span>`;
                }
                if(!item.material_type && !item.is_service){
                    isStockableIcon = `<span class="non_stock-flag"></span>`;
                }
                if(item.hasChildren) {
                    childArrow = "<i class='fa fa-plus fa-for-arrow' role='button' onclick='return appendMaterial(\""+ item.item_id +"\", \""+ sessionStorage.clickcount +"\", \""+ dataParent +"\", \""+jop_po_id +"\")'><a style='padding-left: 26px; display: block; margin-top: -12px;'>"+item_name+" "+isServiceIcon+" "+isStockableIcon+"</a></i>";
                } else {
                    childArrow = item_name+" "+isServiceIcon+" "+isStockableIcon;
                }
                if (item.makes.length > 0){
                    makes = "<select class='form-control' name='make' id='txtmake" + i + "'>";
                    for (j = 0; j <= item.makes.length - 1 ; j++) {
                        makes = `${makes}<option value="${item.makes[j].make_id}" data-quantity="${item.makes[j].stock}">${item.makes[j].make_name}</option>`;
                    }
                    makes += "</select>";
                }
                var is_service_value = item.is_service==true?1:0;
                var description = item.cat_code +" - " +item.drawing_no
                baseExpectedQty = item.quantity * baseExpectedQty
                toBeSuppliedQty = (baseExpectedQty) + returnedQty - suppliedQty
                excessSuppliedQty = (suppliedQty>(baseExpectedQty) ? (baseExpectedQty) - suppliedQty : 0)
                var row = "<tr data-toggle='close' data-padding='"+dataPadding+"' data-parent='"+item.item_id+ "_" + sessionStorage.clickcount + "' id=\""+ item.item_id + "_" + sessionStorage.clickcount +
                                            "\" bgcolor='#ececec' border='1' align='left' style='font-size:12px; font-weight:normal;'>"+
                                        "<td hidden=hidden class='exclude_export item-id-content' data-item-id ='"+item.item_id+"'> "+item.item_id+" </td>" +
                                        "<td hidden=hidden class='exclude_export make-id-content' data-make-id ='"+item.make_id+"'> "+item.make_id+" </td>" +
                                        "<td hidden=hidden class='exclude_export'>" + description +"</td>"+
                                        "<td class='text-center bom-sno'>" +current_bom_sno+ "."+(i+1) + "</td>"+
                                        "<td width='59%' style='padding-left:"+Number(dataPadding-20)+"px'><span style='padding-left:30px; display: block;' class='tree-view'>"+ childArrow +"</span></td>"+
                                        "<td width='5%' class='bom-expected-qty'>" + (baseExpectedQty).toFixed(3) + "</td>"+
                                        "<td width='5%' class='bom-supplied-qty'>" + (suppliedQty).toFixed(3) + "</td>"+
                                        "<td width='5%' class='bom-returned-qty'>" + (returnedQty).toFixed(3) + "</td>"+
                                        "<td width='5%' class='bom-toBeSupplied-qty'>" + (toBeSuppliedQty).toFixed(3) + "</td>"+
                                        "<td width='5%' class='bom-excessSupplied-qty'>" + (excessSuppliedQty).toFixed(3) + "</td>"+
                                        "<td width='10%' class='exclude_export bom-return-to-qty'>"+"<input type='textbox' name='returnQty' value=0 class='po-return-qty form-control'>"+"</td>" +

                                    "</tr>";
                 constructedRow = constructedRow+row
                });
                $('#cattable_3 #'+cat_code + "_" + dataChild).after(constructedRow);
            }
        });
    }
}
function editrow(po_id, status) {
    $('#loading').show();
    $("#id_job_po").val(po_id);
    stock_type =0;
    indent_no=0;
    item_details=""
    $("#po_id").val(po_id)
    $("#generate_pdf").attr('data-status', status);
    $(".xsid_number_edit").addClass("hide");
    var title_po = 'Purchase Order';
    $.ajax({
        url: "/erp/purchase/json/po/loadpoheader/",
        type: "post",
        datatype: "json",
        data: {po_id: po_id},
        success: function (response) {
            $.each(response, function (i, item) {
                console.log("ITEM", item)
                stock_type=item.stock_type;
                po_type=item.po_type;
                is_blanket_po = item.is_blanket_po;
                var order_code = "";
                var po_no = item.orderno

                $("#id_issued_to").val(item.issue_to)

                while(po_no.length < 7){
                    po_no = '0' + po_no;
                }
                if (po_type ==1){
                    if (item.orderno != 0 &&  item.status != 3){
                        $("#dc_report_button").removeClass("hide");
                        $("#dc_usage_button").removeClass("hide");
                    }
                    order_code = item.financial_year + "/JO/" + po_no + item.po_sub_number;
                }else{
                   $("#dc_report_button").addClass("hide");
                   $("#dc_usage_button").addClass("hide");
                   order_code = item.financial_year + "/PO/" + po_no + item.po_sub_number;
                }
                var isPriceModificationDisabled = false;
                if(item.indent_id == 0){
                    if($("#is_price_modification_disabled_quick_po").val() == "True") {
                        isPriceModificationDisabled = true;
                    }
                    $("#job_stockable").show();
                    $("#item_particulars a").click();
                    $("#other_particulars").removeClass("hide");
                }else{
                    if($("#is_price_modification_disabled").val() == "True") {
                        isPriceModificationDisabled = true;
                    }
                    $("#job_stockable").hide();
                    $("#item_particulars a").click();
                    $("#other_particulars").addClass("hide");
                }
                if (item.approvedOn != "" && item.approvedOn.indexOf("0000-00-00") == -1) {
                    $("#approve_on_date").val(moment(item.approvedOn));
                } else {
                    $("#approve_on_date").val("");
                    $("#div_approved_on_date").remove();
                }
                $('#supplier').prop('disabled', true);
                $('#txt_round_off').val(item.round_off);
                if (item.orderno == 0){
                    $('#po_no_label').hide();
                    $('#draft_po_no_label').show();
                    $('#generate_pdf').show();
                    $('#amend_po').hide();
                    document.getElementById("orderno").innerHTML = item.id;
                    title_po = item.id;
                } else {
                    $('#po_no_label').show();
                    $('#draft_po_no_label').hide();
                    $('#generate_pdf').hide();
                    $('#amend_po').show();

                    if(item.revision_no > 1){
                        order_code = order_code + " (#" + (parseInt(item.revision_no)-1) + ")";
                    }
                    document.getElementById("orderno").innerHTML = order_code;
                    title_po = order_code;
                }
                $("#id_po_draft_no").val(item.id);
                $("#id_revision_no").val(item.revision_no);
                if(item.tDate != ""){
                    var dateformat = item.tDate.split(" ");
                    dateformat = dateformat[0].split("/")
                    $("#po_draft_date").val(dateformat[2]+"-"+dateformat[1]+"-"+dateformat[0]);
                }

                $("#po_id").val(item.id);
                $("#projectheader").val(item.code).trigger("chosen:updated");
                updateProjectChosen(item.code, "projectheader");
                indent_no =  item.indent_no

                document.getElementById("purpose").value = item.purpose;
                document.getElementById("instructions").value = item.sp_instructions;
                document.getElementById("refno").value = item.quotation_refno;
                if (item.qDate != "0000-00-00" || item.qDate != null) {
                    document.getElementById("quotdate").value = item.qDate;
                }
                document.getElementById("txtpayment").value = item.payment;
                if (item.dDate=="0000-00-00"){
                    $('#deliverydate').datepicker('setDate', '+0');
                }else{
                    document.getElementById("deliverydate").value = item.dDate;
                }
                document.getElementById("txttotalpurvalue").value = item.total;
                setIndentRemarks(item.indent_sp_instructions);

                if (item.transport==0){
                    $('#txttransport').attr('checked', false);
                }else if (item.transport==1){
                    $('#txttransport').attr('checked', true);
                }
                if (item.packing_forwarding==0){
                    $('#txtpacking').attr('checked', false);
                }else if (item.packing_forwarding==1){
                    $('#txtpacking').attr('checked', true);
                }

                if (item.tax1type == 0) {
                    $('#vat').attr('checked', false);
                    $('#excise').attr('checked', false);
                }
                else if (item.tax1type == 2) {
                    $('#vat').attr('checked', true);
                    $('#excise').attr('checked', false);
                }
                else if (item.tax1type == 1) {
                    $('#excise').attr('checked', true);
                    $('#vat').attr('checked', false);
                }

                if (item.tax2amt > 0) $('#servicetax').attr('checked', true);
                else $('#servicetax').attr('checked', false);

                var select = document.getElementById("indent_type");
                for (var i = 0; i < select.options.length; i++) {
                    if (select.options[i].value == item.purchase_account_id) {
                        select.options[i].selected = true;
                    }
                }

                $("#currency").val(item.currency_code).trigger("chosen:updated");
                $('#indents').prop('disabled', true);
                var indent_no = item.indent_id
                while(indent_no.length < 7){
                    indent_no = '0' + indent_no;
                }
                if(item.indent_module_id==0){
                    loadPendingIndents(item.indent_no, item.indent_financiyal_year +"/IND/"+indent_no + item.indent_sub_number)
                }else{
                    loadPendingIndents(item.indent_no, item.indent_financiyal_year +"/MI/"+indent_no + item.indent_sub_number)
                }
                if (item.status > 0) $('#cmdupdate').addClass("hide");
                else $('#cmdupdate').removeClass("hide");

                $('#chknonstockable').prop('disabled',true);

                var select = document.getElementById("supplier");
                for (var i = 0; i < select.options.length; i++) {
                    if (select.options[i].value == item.party_id) {
                        select.options[i].selected = true;
                    }
                }
                $('#supplier').trigger("chosen:updated");
                $("#id_purchase-shipping_name").val(item.shipping_name)
                $("#id_purchase-shipping_address").val(item.shipping_address)
                var supp_id = item.party_id
                var select = document.getElementById("pay_type");
                for (var i = 0; i < select.options.length; i++) {
                    if (select.options[i].value == item.payment_type) {
                        select.options[i].selected = true;
                    }
                }
                document.getElementById("no_of_days").value = item.payment_days;
                var select = document.getElementById("pay_mode");
                for (var i = 0; i < select.options.length; i++) {
                    if (select.options[i].value == item.payment_mode) {
                        select.options[i].selected = true;
                    }
                }

                if (item.status == 1) {
                    $('#cmdreview').removeClass("hide");
                    $('#generate_pdf').hide();
                    $('#review_po_no_label').show();
                    $('#review_remarks').show();
                    document.getElementById("review_remarks").innerHTML = item.remarks;
                }else{
                    $('#cmdreview').addClass("hide");
                    $('#generate_pdf').show();
                    $('#review_po_no_label').hide();
                }

                clearTable();
                $.ajax({
                    url: "/erp/purchase/json/po/load_po_tags/",
                    type: "post",
                    datatype: "json",
                    data: {po_ids: po_id},
                    success: function (response) {
                        $("#po_tags_table .li-tagit-display").remove();
                        $.each(response, function (i, item) {
                            var row = "<li class='li-tagit-display'>" +
                                        "<label class='label-tagit-display'>"+item[0]+"</label>"+
                                        "<div class='hidden-div-tagit-id' hidden='hidden'>"+ item[1] +"</div>"+
                                        "<a class='delete_tag' ></a>"  + "</li>";
                            $(row).insertBefore("#id_text_for_tag");

                        });
                        create_delete_tag_button();
                    }
                });

                if (stock_type==0) {
                    $('#chknonstockable').prop('checked',false);
                }else{
                    $('#chknonstockable').prop('checked',true);
                }
                if (is_blanket_po==0) {
                    $('#txt_blanket_open_po').prop('checked',false);
                    $(".valid_until").addClass('hide');
                    if ($("#is_blanket_po").val() == "True"){
                        $(".open_po").removeClass('hide');
                        $(".blanket_po").removeClass('hide');
                        $(".for-indent-no").removeClass('hide');
                    }
                }else{
                    $('#txt_blanket_open_po').prop('checked',true);
                    $(".valid_until").removeClass('hide');
                    $("#fromdate").val(item.valid_since);
                    $("#todate").val(item.valid_till);
                    $(".open_po").removeClass('hide');
                    $(".blanket_po").removeClass('hide');
                    $(".for-indent-no").removeClass('hide');
                }


                $.ajax({
                    url: "/erp/purchase/json/po/loadpomaterial/",
                    type: "post",
                    datatype: "json",
                    data: {po_ids: po_id},
                    success: function (response) {
                        $("#po_materials_table tbody.item-for-goods, #po_materials_table tbody.item-for-service, #po_materials_table tfoot").empty();
                        $("#deliveryScheduleParticularsTable").find("tr:gt(1)").remove();
                        $('#consolidatedDeliveryScheduleTable').find("tr:gt(0)").remove();
                        var total_price = 0;
                        $.each(response[0], function (i, item) {
                            item_details = item.name
                            if (item.drawing_no != ""  && item.drawing_no !=null) {item_details +=  " - " + item.drawing_no};
                            if (item.make_name != null && item.make_name != "" && item.make_name != "[]")
                            { item_details += " <i>[" + constructDifferentMakeName(item.make_name) + "]</i>"; }
                            if (item.is_service == 1){item_details= item_details+ "<span class='service-item-flag'></span>"}
                            unit_rate = item.po_price - (item.po_price * item.discount)/100
                            var ind_qty = ""
                            var already_po = ""
                            var bal_qty = ""
                            if(item.indent_type==0){
                                ind_qty = "-NA-"
                                already_po = "-NA-"
                                bal_qty = "-NA-"
                            }else{
                                ind_qty = item.ind_qty
                                already_po = item.already_po_qty
                                bal_qty = (item.ind_qty-item.already_po_qty)
                            }
                            var get_next_ds_date = '';
                            var get_next_ds_qty = 0.00;
                            var get_next_ds_date_flag = false;
                            var get_max_delivery_date = '';
                            if(isEmpty(item.delivery_schedules)){
                                get_max_delivery_date = (get_max_delivery_date == ''? item.delivery_date : get_max_delivery_date);
                                 var deliveryDueJson = [];
                                    var deliveryDueObj = {
                                        delivery_date: moment(item.delivery_date).format("YYYY-MM-DD"),
                                        delivery_qty: bal_qty
                                    };
                                  var get_next_ds_date_output = moment(item.delivery_date).format("MMM DD, YYYY");
                                 if ($.isNumeric(bal_qty)){
                                    var get_next_ds_qty = bal_qty.toFixed(2)
                                 }
                                 item.delivery_schedules.push(deliveryDueObj);
                                  ds_all_row = `<tr class="base-delivery-schedule">
                                                            <td class="text-left td_delivery_date">${item_details}</td>
                                                            <td class="text-center td_delivery_date">
                                                                ${ moment(item.delivery_date).format("MMM DD, YYYY")}
                                                            </td>
                                                            <td class="text-right base-delivery-quantity td_delivery_quantity">
                                                                ${bal_qty}
                                                            </td>
                                                        </tr>`;
                                   $('#consolidatedDeliveryScheduleTable').append(ds_all_row).addClass('tbl');
                            }else{
                                 $.each(item.delivery_schedules, function (index, item) {
                                    if(moment(new Date(item.delivery_date)).format("YYYY/MM/DD") >= moment(new Date()).format("YYYY/MM/DD") && get_next_ds_date_flag == false){
                                        get_next_ds_date = item.delivery_date;
                                        get_next_ds_qty = item.delivery_qty;
                                        get_next_ds_date_flag = true;
                                    }
                                    if(get_next_ds_date_flag == false){
                                        get_next_ds_date = "";
                                        get_next_ds_qty = 0;
                                        get_next_ds_date_flag = true;
                                    }
                                    get_max_delivery_date = (get_max_delivery_date == ''? new Date(item.delivery_date): (new Date(item.delivery_date) >= get_max_delivery_date ? new Date(item.delivery_date) : get_max_delivery_date));
                                    ds_row = `<tr class="base-delivery-schedule">
                                                <td hidden=hidden>${i}</td>
                                                <td class="text-center td_delivery_date">
                                                    ${item.delivery_date}
                                                </td>
                                                <td class="text-right base-delivery-quantity td_delivery_quantity">
                                                    ${item.delivery_qty}
                                                </td>
                                                <td class="text-center"><span onclick="removeNewDeliverySchedule()"><i class="fa fa-times" aria-hidden="true"></i></span></td>
                                            </tr>`;
                                    ds_all_row = `<tr class="base-delivery-schedule">
                                                   <td class="text-left td_delivery_date">${item_details}</td>
                                                    <td class="text-center td_delivery_date">
                                                        ${ moment(item.delivery_date).format("MMM DD, YYYY")}
                                                    </td>
                                                    <td class="text-right base-delivery-quantity td_delivery_quantity">
                                                        ${item.delivery_qty.toFixed(3)}
                                                    </td>
                                                </tr>`;
                                    $('#deliveryScheduleParticularsTable').append(ds_row).addClass('tbl');
                                    $('#consolidatedDeliveryScheduleTable').append(ds_all_row).addClass('tbl');
                                });

                            }

                            var isDeliveryDue = false;
                            if($("#is_delivery_schedule").val() == "True") {
                                isDeliveryDue = true;
                            }

                            var nextDelivery = "";
                            if(get_next_ds_date != "") {
                                var get_next_ds_date_output = moment(get_next_ds_date).format("MMM DD, YYYY");
                                var get_next_ds_qty = get_next_ds_qty.toFixed(2)
                                nextDelivery = `<span class='td-sub-content bracket-enclosed text-right'>
                                                    <span class='next-delivery-date'>${(item.delivery_schedules.length >= 1 ? get_next_ds_date_output: '')}</span>
                                                    -
                                                    <span class='next-delivery-qty'>${(item.delivery_schedules.length >= 1 ? get_next_ds_qty : '')}</span>
                                                </span>`;
                            }
                            var tableInlineBg = "";
                            if(indent_no == 0) {
                                tableInlineBg = `table-inline-icon-bg`
                            }
                            var row =   `<tr class='po_material_row'>
                                            <td class='text-center'>
                                                <span class='${tableInlineBg} po-item-sno'>${(i+1)}.</span>
                                                <span class="for_indent_unavailable">
                                                    <span class='table-inline-icon hide' data-tooltip='tooltip' title='Delete' onclick='deleteItemMaterial(this);'>
                                                        <i class='fa fa-trash-o'></i>
                                                    </span>
                                                </span>
                                            </td>
                                            <td>${item_details}</td>
                                            <td hidden=hidden>
                                                <input type='text' name='item_tax' id='tax_sub_total_${i}' value ='0' disabled='true'>
                                                <input type='text' name='item_make' id='item_make_${i}' value = ${item.make_id}>
                                                <input type='text' name='item_unit' id='item_unit_${i}' value = ${item.unit_id}>
                                                <input type='text' name='item_id' id='item_id_${i}' value = ${item.item_id}>
                                                ${item.drawing_no}
                                                <input type='text' name='alternate_unit_id' id='alternate_unit_id_${i}' value=${item.alternate_unit_id}>
                                                <input type='text' name='scale_factor' id='scale_factor_${i}' value=${item.scale_factor}>
                                                <input type='text' name='item_spq' id='item_spq_${i}' value=${item.spq} />
                                                <input type='text' name='item_moq' id='item_moq_${i}' value=${item.moq} />
                                            </td>
                                            <td class='for_indent_available text-right hide'>
                                                <span class='td_indent_qty'>${$.isNumeric(ind_qty) ? ind_qty.toFixed(3) : ind_qty}</span>
                                                <span class='td_ordered_qty bracket-enclosed'>${$.isNumeric(already_po) ? already_po.toFixed(3) : already_po}</span>
                                            </td>
                                            <td class='text-right'>
                                                <span class='td_po_qty'>
                                                    <input type='text' id='txtpurqty${i}' name='purchase' class='form-control text-right' value ='${item.pur_qty.toFixed(3)}' maxlength='16' onfocus="setNumberRangeOnFocus(this,12,3)" onblur='calculateTotalPOValue();' onChange='validateInlineSpq(this);'>
                                                </span>
                                                <span class='for_indent_available td_balance_qty bracket-enclosed hide'>${$.isNumeric(bal_qty) ? bal_qty.toFixed(3) : bal_qty}</span>
                                            </td>
                                            <td class='for_indent_po text-right'>
                                                <span class='td_received_qty'>${item.rec_qty.toFixed(3)}</span>
                                                <span class="td-uom">${item.unit}</span>
                                            </td>
                                            <td>
                                                <input type='text' id='txtpurprice${i}' name='purchaseprice' class='form-control text-right' value ='${item.po_price}' maxlength='19' onfocus="setNumberRangeOnFocus(this,13,5)" onblur='calculateTotalPOValue();' ${(isPriceModificationDisabled)? "disabled": ""}>
                                            </td>
                                            <td>
                                                <input type='text' id='txtdiscount${i}' name='discount' class='form-control text-right' value ='${item.discount}' maxlength='6' onfocus="setNumberRangeOnFocus(this,3,2)" onblur="validatePercentage(this, event); calculateTotalPOValue();">
                                            </td>
                                            <td class='text-right'>
                                                <input type='text' id='txtunitrate${i}' name='unitrate' class='form-control text-right' value ='${unit_rate}' disabled='true'>
                                                <span class='for_indent_po td-approved-price td-sub-content bracket-enclosed'>${$.isNumeric(item.price) ? item.price.toFixed(2) : item.price}</span>
                                            </td>
                                            <td>
                                                <input type='text' id='txtpurvalue${i}' name='purchasevalue' class='form-control text-right' value ='${item.total.toFixed(2)}' disabled='true'>
                                            </td>
                                            <td hidden='hidden'>${item.pur_qty}</td>
                                            <td>
                                                <select class='form-control' name='cgst' onchange='calculateTotalPOValue()' id='txtcgst${i}'>${CGST_rates}</select>
                                                <span name='cgstamt' class='td-sub-content bracket-enclosed' id='txtcgstamt${i}'>0.00</span>
                                            </td>
                                            <td>
                                                <select class='form-control' name='sgst' onchange='calculateTotalPOValue()' id='txtsgst${i}'>${SGST_rates}</select>
                                                <span name='sgstamt' class='td-sub-content bracket-enclosed' id='txtsgstamt${i}'>0.00</span>
                                            </td>
                                            <td>
                                                <select class='form-control' name='igst' onchange='calculateTotalPOValue()' id='txtigst${i}'>${IGST_rates}</select>
                                                <span name='igstamt' class='td-sub-content bracket-enclosed' id='txtigstamt${i}'>0.00</span>
                                            </td>
                                            <td class='${(!isDeliveryDue)? "hide": ""}'>
                                                <span class='edit_delivery_schedule table-inline-icon hide' id="edit_delivery" data-tooltip='tooltip' title='Edit Delivery Schedule' onclick='openDeliveryScheduleParticulars(this, ${i});'>
                                                    <img src='/site_media/images/delivery-dispatch.png' />
                                                </span>
                                                <span class='total-delivery-date text-left'>${moment(get_max_delivery_date).format("MMM DD, YYYY")}</span>
                                                `+ nextDelivery +`
                                            </td>
                                            <td hidden=hidden>
                                                <textarea name='item_delivery_schedules' class='item_delivery_schedules' id='item_delivery_schedules_${i}'>${JSON.stringify(item.delivery_schedules)}</textarea>
                                            </td>
                                        </tr>`;
                            if (item.is_service == 1) {
                                $('#po_materials_table tbody.item-for-service').append(row);
                            }
                            else {
                                $('#po_materials_table tbody.item-for-goods').append(row);
                            }
                            
                            $("#txtcgst"+i).val(item.CGST);
                            $("#txtsgst"+i).val(item.SGST);
                            $("#txtigst"+i).val(item.IGST);
                            total_price += item.total;
                        });
                        poMaterialTableSerialNumberInit();
                        listTableHoverIconsInit('po_materials_table');

                        $('#stockable').show();
//                        $("#projectheader").prop('disabled', true).trigger("chosen:updated");
                        $('#purpose').prop('readonly', true);
                        $('#indents').prop('disabled', true);
                        $('#reportrange').addClass('div-disabled');
                        $('.blanket_po').addClass('div-disabled');
                        $('#supplier').prop('disabled', true);
                        if(item.purchase_account_id==0) {
                            $('#job_stockable').show();
                            $("#item_particulars a").click();
                            $("#other_particulars").removeClass("hide");
                        }


                        $.ajax({
                            url: "/erp/purchase/json/po/load_po_taxes/",
                            type: "post",
                            datatype: "json",
                            data: {po_id: po_id},
                            success: function (response) {
                                $.each(response, function (i, po_tax_detail_as_row) {
                                    $('#po_taxes_table').append(po_tax_detail_as_row).addClass('tbl');
                                });
                                $("#po_taxes_table").find('.hnd_text_id').each(function(){
                                    var selectedTax = $(this).val();
                                    $("#id_po_tax option[value="+selectedTax+"]").hide();
                                    $('.chosen-select').trigger('chosen:updated');
                                });
                                calculateTotalPOValue();
                            }
                        });
                        if($('#indent_access').val() === 'False' ){
                                $(".for_indent_available_side_container").addClass("hide");
                            }
                    }
                });

                $('#purpose').prop('readonly', true);
                displayRemarksHistory("remarks_list", item.remarks, "remarks_count");
            });
            $('#template_title').text(title_po);
            calculateTotalPOValue();
            DateRangeInit();
            CustomDatePickerInit();
            initializePage();
            poPrevNextPaging();
            listTableHoverIconsInit('po_materials_table');
        }
    });
    $("#tab_add").click();
    $('#cmdSave').hide();
    $("#tickettype").focus();
    $("#po_form").data("changed", false);
    $('#loading').hide();
}

function IsIndentContainerVisible(){
    if($("#tab2").hasClass("tab_indent_po")) {
        if($(".header_current_page").text().trim() == "New" || $(".header_current_page").length == 0 ) {
            $(".for_indent_available_side_container").removeClass("hide");
        }
        else if($("#indents").val() == "0" || $("#indents").val().toLowerCase() == "null") {
            if($("#is_super_user").val().toLowerCase() == 'true') {
                $(".for_indent_available_side_container").removeClass("hide");
            }
            else {
                $(".for_indent_available_side_container").addClass("hide");
            }
        }
        else {
            $(".for_indent_available_side_container").removeClass("hide");
        }
    }
    else {
        $(".for_indent_available_side_container").addClass("hide");
    }
    //$('#loading').hide();
}

function load_tax_details(){
// TODO reuse method grn.js -> load_tax_details()
    var seltext= "";
    $.ajax({
        url: "/erp/purchase/json/po/loadtaxdetails/",
        type: "post",
        datatype:"json",
        data: seltext,
        success: function(response){
            $('#id_po_tax').val("");
            var items =  "";
            items = "<option value='0'> -NA- </option>";
                    for (i = 0; i <= response.length - 1 ; i++) {
                        items += "<option value='"+response[i][0]+"'> "+response[i][1]+" </option>";
            }
            $("#id_po_tax").html(items);
        }
    });
}

$(function () {
    var tags = [];
    var zid =  "";
    $.ajax({
        url: "/erp/purchase/json/po/loadtags/",
        type: "post",
        datatype:"json",
        data: zid,
        success: function(response){
            for (i = 0; i <= response.length - 1 ; i++) {
                tags.push({value:response[i][0],label:response[i][1]});
            }
        }
    });
    $("#id_po_tag").autocomplete({
        source: function(request, response) {
            var results = $.ui.autocomplete.filter(tags, request.term);
            response(results.slice(0, 30));
        },
        focus: function (event, ui) {
            event.preventDefault();
        },
        minLength: 0,
        select: function (event, ui) {
            event.preventDefault();
            $("#id_po_tag").val(ui.item.label);
            $("#po_tag_value").val(ui.item.value);
            $("#add_po_tag").click();
            $("#add_po_tag").focus();
            setTimeout(function(){
                $("#id_po_tag").focus();
            },500);
        }
   }).focus(function () {
        var that = $(this)
        $("#id_po_tag").addClass('ui-autocomplete-loading');
        setTimeout(function(){
            that.autocomplete("search");
        },300);
    });
    $("#id_po_tag").keydown(function(event,ui){
        if(event.keyCode == 13) {
            if($(this).val() != "") {
                $("#add_po_tag").click();
                setTimeout(function(){
                    $(this).focus();
                },10);
            }
            event.preventDefault();
        }
    });
});

$(function () {
    CGST_rates="";
    SGST_rates="";
    IGST_rates="";

    var zid = 0;
    var transport_details = [];
    $.ajax({
        url: "/erp/purchase/json/po/loadtransportdetails/",
        type: "post",
        datatype: "json",
        data: zid,
        success: function (response) {
            for (i = 0; i <= response.length - 1; i++) {
                transport_details.push({label: "" + response[i][0] + ""});
            }
        }
    });

    var pack_details = [];
    $.ajax({
        url: "/erp/purchase/json/po/loadpackdetails/",
        type: "post",
        datatype: "json",
        data: zid,
        success: function (response) {
            for (i = 0; i <= response.length - 1; i++) {
                pack_details.push({label: "" + response[i][0] + ""});
            }
        }
    });


    $.ajax({
        url: "/erp/purchase/json/po/load_gst_tax/",
        type: "post",
        datatype:"json",
        data: {gst_type: "CGST"},
        success: function(response){
            CGST_rates += "<option value=0></option>";
            for (i = 0; i <= response.length - 1 ; i++) {
                CGST_rates += "<option value='" + response[i][0] + "'> " + response[i][2] + " </option>";
            }
        }
    });

    $.ajax({
        url: "/erp/purchase/json/po/load_gst_tax/",
        type: "post",
        datatype:"json",
        data: {gst_type: "SGST"},
        success: function(response){
            SGST_rates += "<option value=0></option>";
            for (i = 0; i <= response.length - 1 ; i++) {
                SGST_rates += "<option value='" + response[i][0] + "'> " + response[i][2] + " </option>";
            }
        }
    });


    $.ajax({
        url: "/erp/purchase/json/po/load_gst_tax/",
        type: "post",
        datatype:"json",
        data: {gst_type: "IGST"},
        success: function(response){
            IGST_rates += "<option value=0></option>";
            for (i = 0; i <= response.length - 1 ; i++) {
                IGST_rates += "<option value='" + response[i][0] + "'> " + response[i][2] + " </option>";
            }
        }
    });
});

function insert_Details(type){
    var material;
    var purchase_order = "";
    var data = "";
    var tax1type = 0;
    var detcount = 0;
    var stock_type =0;
    var urlstring = ""
    var no_of_days = 0;
    var transport =0;
    var packing = 0;
    var val1 = "";
    var indent_no=0;
    var pur_quantities = document.getElementsByName('purchase');
    var check_qty = false;
    var po_id=""
    var poQuantityCheck = true;

    isSave = false;

    if ($("#excise").checked = true) tax1type = 1;
    else if ($("#vat").checked = true) tax1type = 2;
    if ($('#no_of_days').val() == "") {
                no_of_days=0;
    }
    else no_of_days= $("#no_of_days").val();
    if (document.getElementById('txttransport').checked) {
        transport=1;
    }
    if (document.getElementById('txtpacking').checked) {
        packing=1;
    }

    //Check the Purchase Qty
    var data_count = Number($('#po_materials_table tbody.item-for-goods tr:visible').length + $('#po_materials_table tbody.item-for-service tr:visible').length);
    var isPoAvailable = $("#po_materials_table tbody tr.po_material_row").length;
    var table = document.getElementById('po_materials_table');
    if (isPoAvailable < 1){
        if($(".for_indent_available_side_container").is(":visible")){
            swal("", "Please add atleast one material / indent.", "error");
            return;
        }
        else {
            swal("", "Please add atleast one material.", "error");   
            return;
        }
    }

    for (i = 0; i < data_count; i++) {
        if ($(table.rows[i+2]).find('.td_balance_qty').text() != "-NA-"){
            $(pur_quantities[i]).removeClass("error-border").tooltip('destroy');
            if (parseFloat(pur_quantities[i].value) > parseFloat($(table.rows[i+2]).find('.td_balance_qty').text())) {
                var spqValue = $(table.rows[i+2]).find("input[name='item_spq']").val();
                if(spqValue > 0) {
                    var sqpRoundValue = qty_round_ceil(spqValue, parseFloat($(table.rows[i+2]).find('.td_balance_qty').text()))
                    if(sqpRoundValue != parseFloat(pur_quantities[i].value)) {
                        swal("", "One or more PO quantity is more than the Balance Quantity.<br />Please Check the PO Quantity.", "error");
                        $(pur_quantities[i]).addClass("error-border").attr("data-tooltip", "tooltip").attr("title", "PO Quantity is greater than Balance Quantity.");
                        TooltipInit();
                        poQuantityCheck = false;
                    }
                }
                else {
                    swal("", "One or more PO quantity is more than the Balance Quantity.<br />Please Check the PO Quantity.", "error");
                    $(pur_quantities[i]).addClass("error-border").attr("data-tooltip", "tooltip").attr("title", "PO Quantity is greater than Balance Quantity.");
                    TooltipInit();
                    poQuantityCheck = false;
                }
            }
        }
    }

    if(!poQuantityCheck) {
        return;
    }

    check_qty = false;
    for (i = 0; i < data_count; i++) {
        if (parseFloat(pur_quantities[i].value) > 0){
            check_qty = true;
        }
    }
    if (check_qty == false){
        swal("", "Please enter quantity.", "error");
        return;
    }

    indent_no = $("#indents option:selected").val();
    approved_on = $("#approve_on_date").val()
    if (approved_on == undefined){
        approved_on = '';
    }
    if(!$("#txt_blanket_open_po").is(":checked")){
		$("#validuntildate").val('');
	}
	var closingRemarks = $("#instructions").val().replace(/'/g, "\\'");
	var poRemarks = $("#id_po-remarks").val().replace(/'/g, "\\'");
	var purpose = $("#purpose").val().replace(/'/g, "\\'");
    var purchase_order_dict = {
                                "project_code":$("#projectheader").val(),
                                "indent_no":indent_no,
                                "purpose": purpose,
                                "closing_remarks":closingRemarks,
                                "supplier_id":$("#supplier option:selected").val(),
                                "quotation_ref_no":$("#refno").val(),
                                "quotation_date":$("#quotdate").val(),
                                "payment":$("#txtpayment").val(),
                                "transport":transport,
                                "packing":packing,
                                "delivery":$("#deliverydate").val(),
                                "approved_on":approved_on,
                                "total":$("#txttotalpurvalue").val(),
                                "tax1type":tax1type,"tax1":0,"tax2":0,
                                "purchase_account_id":$("#indent_type option:selected").val(),
                                "currency_code":$("#currency option:selected").val(),
                                "pay_against":$("#pay_type option:selected").val(),
                                "pay_in_days":no_of_days,"pay_through":$("#pay_mode option:selected").val(),
                                "po_remarks":poRemarks,
                                "po_type":$("#po_order").val(),
                                "is_blanket_po": $("#txt_blanket_open_po").is(":checked"),
                                "valid_since":$("#txt_blanket_open_po").is(":checked") ? $("#fromdate").val() : "",
                                "valid_till":$("#txt_blanket_open_po").is(":checked") ? $("#todate").val() : "",
                                "round_off":$("#txt_round_off").val(),
                                "issue_to":"",
                                "shipping_name":$("#id_purchase-shipping_name").val(),
                                "shipping_address":$("#id_purchase-shipping_address").val()
                              };
    if($("#chknonstockable").is(':checked')) {
        stock_type = 1;
    }
    else {
        stock_type = 0;
    }

    var i;
    var table = document.getElementById('po_materials_table')
    var rowcount = document.getElementById('po_materials_table').rows.length;

    if (rowcount == 4) {
        swal("", "Please Add Material", "error");
        return;
    }
    input_obj = document.getElementsByName('purchase');
    input_obj2 = document.getElementsByName('purchaseprice');
    input_obj3 = document.getElementsByName('purchasevalue');
    input_obj4 = document.getElementsByName('discount');
    input_obj5 = document.getElementsByName('item_make');
    unit_obj = document.getElementsByName('item_unit');
    item_id_obj = document.getElementsByName('item_id');
    alternate_unit_id_obj = document.getElementsByName('alternate_unit_id');

    var cgst = document.getElementsByName('cgst');
    var sgst = document.getElementsByName('sgst');
    var igst = document.getElementsByName('igst');

    var selected_indent = document.getElementById('indents');
    selected_indent.selectedIndex == 0
    var check_tax = false
    var po_materials_list = []
    for (i = 1; i < rowcount-4; i++) {
        if (input_obj[i - 1].value > 0) {
            detcount = detcount + 1;
            var material_obj = {
                "drawing_name":$(table.rows[i+1].cells[1]).text().trim(), "drawing_no":$(table.rows[i+1].cells[2]).text().trim(),
                "request_qty":$(table.rows[i+1]).find('.td_indent_qty').text(),
                "raised_qty":$(table.rows[i+1]).find('.td_ordered_qty').text(), "balance_qty":$(table.rows[i+1]).find('.td_balance_qty').text(),
                "quantity":input_obj[i-1].value, "approved_price":$(table.rows[i+1]).find(".td-approved-price").text(),"item_id":item_id_obj[i - 1].value,
                "alternate_unit_id":alternate_unit_id_obj[i-1].value,
                "rate":input_obj2[i - 1].value,"total_price":input_obj3[i - 1].value,"discount":input_obj4[i - 1].value,
                "cgst":cgst[i-1].options[cgst[i-1].selectedIndex].value,"sgst":sgst[i-1].options[sgst[i-1].selectedIndex].value,
                "igst":igst[i-1].options[igst[i-1].selectedIndex].value ,"item_make":input_obj5[i - 1].value,"unit_id":unit_obj[i - 1].value,
                "material_taxes": [ cgst[i-1].options[cgst[i-1].selectedIndex].value,
                                    sgst[i-1].options[sgst[i-1].selectedIndex].value,
                                    igst[i-1].options[igst[i-1].selectedIndex].value
                                   ],
                "delivery_schedules":$(table.rows[i+1]).find('.item_delivery_schedules').text()
                                   };
            if (cgst[i-1].options[cgst[i-1].selectedIndex].value=="0" && sgst[i-1].options[sgst[i-1].selectedIndex].value=="0" && igst[i-1].options[igst[i-1].selectedIndex].value=="0"){
                check_tax = true
            }
            po_materials_list.push(material_obj)
        }
    }

    tax_codes = document.getElementsByName('po_tax_code');
    tax_order = 1;
    if($("#tab2").hasClass("tab_indent_po")){
        if((tax_codes.length == 0 && check_tax) ) {
            swal({
                title: "Please Confirm",
                text: "One or more item has no tax attached!",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#209be1",
                confirmButtonText: "Yes, do it!",
                closeOnConfirm: true,
                closeOnCancel: true
            },
            function(){
                setTimeout(function(){
                        checkAndSave_po();
                },100);
             });
        }
        else {
            checkAndSave_po();
        }
    }
    else {
        checkAndSave_po();
    }

    function checkAndSave_po(){
        if ($("#po_id").val() == "" || $("#is_super_user").val() == "False") {
            addSaveFunction();
        }
        else {
            var po_type = $("#orderno").text().split("/")[1];
            var url = "/erp/purchase/json/get_po_linked_message/";
            if(po_type == "JO") {
                url = "/erp/sales/json/get_jo_linked_message/";
            }
            $.ajax({
                url: url,
                method: "POST",
                data: {
                    po_id: $("#po_id").val()
                },
                success: function(response) {
                    if (response.response_message =="Success") {
                        if(response.custom_message == "") {
                           addSaveFunction();
                        } else {
                            swal({title: "", text: response.custom_message, type: "warning",
                                showCancelButton: true,
                                confirmButtonColor: "#209be1",
                                confirmButtonText: "Yes, do it!",
                                closeOnConfirm: true,
                                closeOnCancel: true
                            },
                            function(isConfirm){
                                if (isConfirm) {
                                    addSaveFunction();
                                }
                            });
                        }
                    } else {
                        swal({title: "", text: response.custom_message, type: "error"});
                    }
                },
                error: function (xhr, errmsg, err) {
                    swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
                }
            });
        }
    }

    function addSaveFunction(){
        $("#loading").show();
        var tax_list = [];
        var po_type="Purchase";
        for(i=0; i<tax_codes.length; i++){
            tax_obj={'tax_code':tax_codes[i].value,'tax_order':tax_order, 'tax_value': $(tax_codes[i]).closest("tr").find(".tax-value").val() }
            tax_list.push(tax_obj)
            tax_order += 1;
        }
        $('#loading').show();
        var tag_list = []
        $("#po_tags_table").find('.li-tagit-display:visible').each(function(){
            tag_obj={'tag_id':$(this).find('.hidden-div-tagit-id').text(),'tag_name':$(this).find('.label-tagit-display').text() }
            tag_list.push(tag_obj)
        });
        var list_url = '/erp/purchase/po_list/';
        if($("#po_order").val() == "1") {
             po_type="Job";
             list_url = '/erp/purchase/jo_list/';
        }

        po_id = $("#po_id").val();
        purchase_order_dict = Object.assign({"materials":po_materials_list}, purchase_order_dict)
        purchase_order_dict = Object.assign({"stock_type":stock_type}, purchase_order_dict)
        purchase_order_dict = Object.assign({"detcount":detcount}, purchase_order_dict)
        purchase_order_dict = Object.assign({"taxes":tax_list}, purchase_order_dict)
        purchase_order_dict = Object.assign({"tags":tag_list}, purchase_order_dict)
        purchase_order_dict = Object.assign({"po_id":po_id}, purchase_order_dict)
        purchase_order_dict = Object.assign({"quick_po_type":type}, purchase_order_dict)
        var token = getCookie('csrftoken');
        if (type==1 || type== 4 ){
            urlstring ="/erp/purchase/json/po/save/";
        }
        else {
            urlstring ="/erp/purchase/json/po/edit/";
        }
        $.ajax({
            url: urlstring,
            type: "POST",
            dataType: "json",
            data: { 'csrfmiddlewaretoken': token,'purchase_order_dict':JSON.stringify(purchase_order_dict) } ,
            success: function (json) {
                $('#loading').hide();
                if (json.response_message == 'Failure') {
                    swal({title: "", text: json.custom_message, type: "error"});
                    return;
                }
                if (po_id == "" && json.draft_no != "") {
                    po_id = json.draft_no;
                }
                isSave = true;
                $("#drawingno").val("")
                $("#qty").val("0")
                $("#po_id").val("")
                $("#purpose").val("")
                $("#instructions").val("")
                $("#indent_remarks").val("")
                $("#txtlocation").val("")
                $("#txtproject").val("")
                $("#refno").val("");
                $("#txttotalpurvalue").val("0.00");
                $("#txttransport").val("");
                $("#txtpayment").val("100");
                $("#txtpacking").val("0");
                $("#no_of_days").val("0");
                $("#id_po-remarks").val("");
                $("#po_type").val("");
                $('#txt_round_off').val("0.00");
                load_tax_details();
                $('#po_no_label').hide();
                $('#draft_po_no_label').hide();
                $('#review_po_no_label').hide();
                $('#review_remarks').hide();
                $('#orderno').hide();
                $('#generate_pdf').hide();
                $('#cmdSave').show();
                $('#cmdupdate').addClass("hide");
                $('#cmdreview').addClass("hide");
                $('#indents').prop('disabled', false);
                $('#deliverydate').datepicker('setDate', '+0');
                //$("#loading").hide();
                if (type==2) {
                    date = new Date();
                    $.ajax({
                        url: "/erp/purchase/json/po/draft/?now=" + date.toString(),
                        type: "POST",
                        dataType: "json",
                        data: {po_id: po_id},
                        success: function (json) {
                            $("#loading").hide();
                            ga('send', 'event', po_type + ' Order', 'Submit for Approval', $('#enterprise_label').val(), 1);
                        },
                        error: function (xhr, errmsg, err) {
                            console.log(xhr.status + ": " + xhr.responseText);
                        }
                    });
                }
                $('#pdf_po_id').val(po_id);
                if(type == 1 || type == 2) {
                    var googleStatus = (type == 1) ? "Create":"Update";
                    var swalStatus = (json['message'].indexOf('success') >= 0) ? "success":"warning";
                    var swalTitle = (json['message'].indexOf('success') >= 0) ? "Saved Successfully":"";
                    swal({
                        title: swalTitle,
                        text: json['message'], 
                        type: swalStatus
                    },
                    function(){
                        $("#loading").show();
                        window.location.assign(list_url);
                        ga('send', 'event', po_type + ' Order', googleStatus , $('#enterprise_label').val(), 1);
                    });
                }
                else if(type==3) {
                    //$('#generate_pdf_quick_po').click();
                    generate_pdf_ajax(po_id, $("#po_order").val(), 2);
                    calculateTotalPOValue();
                    $("#po_document_modal").on('hidden.bs.modal', function () {
                        window.location.assign(list_url);
                    });
                    ga('send', 'event', po_type + ' Order', 'Amend', $('#enterprise_label').val(), 1);
                    $('#cmdSave').hide();
                }
                else if (type == 4) {
                    //$('#generate_pdf_quick_po').click();
                    generate_pdf_ajax(po_id, $("#po_order").val(), 2);
                    $("#po_document_modal").on('hidden.bs.modal', function () {
                        window.location.assign(list_url);
                    });
                    ga('send', 'event', po_type + ' Order', 'Create & Approve', $('#enterprise_label').val(), 1);
                }
                else {
                    window.location.assign(list_url);
                    ga('send', 'event', po_type + ' Order', 'Create', $('#enterprise_label').val(), 1);
                }
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
                $("#loading").hide();
            }
        });
        return false;
    }
}

function create_delete_tag_button(){
    $(".delete_tag").click(function(){
        $(this).closest('li').remove();
    });
}

function CheckNo(sender) {
    if(!isNaN(sender.value)) {
        if(sender.value > 100)
            sender.value = 100;
        if(sender.value < 0)
            sender.value = 0;
    } else {
        sender.value = 0;
    }
    calculateTotalPOValue();
}

function SupplierChange() {
    if($("#id_is_supplier").is(":checked")){
        $("#div_supplier").removeClass('hide');
    } else {
        $("#div_supplier").addClass('hide');
    }
}

function CustomerChange() {
    if($("#id_is_customer").is(":checked")){
        $("#div_customer").removeClass('hide');
    } else {
        $("#div_customer").addClass('hide');
    }
}

function loadMaterial(loadType = "") {
    var type = '';
    if($('#po_order').val() == 0 || $('#po_order').val() == 1) {
        var type = Number($('#po_order').val());
    }
    var dataToSend = {
        'type': type,
        'party_id': $("#supplier option:selected").val(),
        'module': 'purchase_order',
        'particulars':'purchase_order_material'
    }
    var local_material_list  = JSON.parse(localStorage.getItem('goods_service_master_data'));
    if(loadType == "onchange" && local_material_list) {
        var material_choices = local_material_list[0].material;
    }
    else {
        var material_choices = generateMaterialAsAutoComplete(dataToSend);
    }
    var frequently_material_choices = generateFrequentlyMaterialAsAutoComplete(dataToSend);

    $("#materialrequiredjob").materialAutocomplete({
        source: frequently_material_choices.concat(material_choices),
        minLength:1,
        response: function( event, ui ) {
            ui.content.unshift({value:"add_new_item", label:"+ ADD NEW MATERIAL ", category: ""});
        },
        open: function(event, ui) {
            $("ul.ui-autocomplete.ui-menu a:contains('+ ADD NEW MATERIAL')").addClass("add_new_material_link");
        },
        select: function (event, ui) {
            event.preventDefault();
            if(ui.item.value != "add_new_item"){
                var itemName = ui.item.label.replace("<span class='service-item-flag'></span>", '');
                $("#material_id_hidden").val(ui.item.id);
                $("#material_id").val(ui.item.id);
                $("#material_spq").val(ui.item.spq);
                $("#material_is_service").val(ui.item.is_service);
                $("#materialrequiredjob").val(itemName).attr("readonly", true);
                $("#id_ind_material-__prefix__-make_id").val(ui.item.mid);
                $("#job_unit option:selected").text(ui.item.unit);
                $("#unit_display").text(ui.item.unit);
                loadJobPrice();
                $(".material-removal-icon").removeClass('hide');
                if($('#is_multiple_units').val()=="True" ){
                    if (ui.item.alt_uom > 0){
                        loadAlternateUnits(ui.item.id, ui.item.unit, '#id_material-alternate_units');
                        $(".alternate_unit_select_box").removeClass("hide");
                    }
                    else {
                        $(".alternate_unit_select_box").addClass("hide");
                        $('#id_material-alternate_units').html("");
                    }
                }
                setTimeout(function(){
                    $("#job_qty").focus();
                },250);
            }
            else {
                openMaterialAddModal();
            }
        }
    }).on('focus', function() { $(this).keydown(); });
}

$(function () {
    $('#edit_catalogue_form').submit(function() {
        $.ajax({
            data: $(this).serialize(),
            type: $(this).attr('method'),
            url: $(this).attr('action'),
            success: function(response) {
                $('#loading').hide();
                $("#material_id_hidden").val(response['item_id']);
                $("#material_id").val(response['item_id']);
                $("#materialrequiredjob").val(response['name']).attr("readonly","readonly");
                $("#materialrequiredjob").closest("div").find(".material-removal-icon").removeClass("hide");
                $("#id_ind_material-__prefix__-make_id").val(response['make_id']);
                $("#unit_display").text(response['unit_name']);
                $("#material_is_service").val(response['is_service']);
                $("#job_unit option:selected").text(response['unit_name']);
                $("#job_price").val(response['price'].toFixed(5));
                if(!$('#add_material_modal').hasClass('in')) {
                    $("#addmaterials").trigger("click");
                }
                $('#add_material_modal').modal('hide')
                setTimeout(function(){
                    $("#job_qty").focus();
                },250);
            }
        });
        return false;
    });

    $("select#supplier").change(function(){
        loadMaterial("onchange");
         $(".material-removal-icon").trigger("click");
    });
});

function loadJobPrice(){
    var service_type = 0
    if($("#po_order").val() == "1") {
         service_type = 1
    }
    $.ajax({
        url: "/erp/purchase/json/po/load_job_price/",
        type: "post",
        datatype: "json",
        data: {item_id: $('#material_id').val(),supplier_id: $("#supplier option:selected").val(),
                make_id: $("#id_ind_material-__prefix__-make_id").val(), service_type: service_type},
        success: function(response) {
            if(response !=""){
                $("#job_price").val(response['job_price']);
                $("#job_price_hidden").val(response['job_price']);
                $("#material_moq").val(response['moq']);
            }else{
                $("#job_price").val(0.00);
                $("#job_price_hidden").val(0.00);
                $("#material_moq").val(0);
            }
        }
    });
}

function ViewAllMaterial() {
    if($("#po_materials_table .nosuppmaterial").length <= 0 ){
        $(".tr-view-all-material").addClass('hide');
    }
    else {
        $(".tr-view-all-material").removeClass('hide');
    }
}

function toggleNonSupplierItems(current) {
    if($(current).hasClass('open')) {
        $(".nosuppmaterial").addClass('hide');
        $(current).removeClass('open');
        $(".view_all_material_text").text('show');
        $(".nosuppmaterial").find('.form-control').val('0');
    }
    else {
        $(".nosuppmaterial").removeClass('hide');
        $(current).addClass('open');
        $(".view_all_material_text").text('remove');
    }
    poMaterialTableSerialNumberInit();
}

function loadAlternateUnitPrice()
{
    if($("#id_material-alternate_units option:selected").attr('data-val') !=0 && $("#job_price_hidden").val() != 0 && $("#job_price_hidden").val() != ""){
        $("#job_price").val( $("#id_material-alternate_units option:selected").attr('data-val') * $("#job_price_hidden").val());
    }else{
        $("#job_price").val("0.00")
    }
}

function loadPendingIndents(selectedVal, selectedText){
    setTimeout(function(){
        $("#indents_chosen").addClass("chosen-item-disabled").closest("td").attr("title", "Loading... Please wait");
        var indentType = ""
        if($("#id_order_type").val() == "po"){
            indentType = 0
        }
        $.ajax({
            url: "/erp/purchase/po/loadinddetails/",
            type: "post",
            datatype: "json",
            data: {indent_type: indentType},
            success: function(response) {
                $('#indents').val("");
                var items =  "";
                var selectedOptionExists = false;
                items = "<option value='0'> --Select an Indent-- </option>";
                        for (i = 0; i <= response.length - 1 ; i++) {
                            items += "<option value='"+response[i][0] +"'>"+response[i][6] +"</option>";
                    if(response[i][0] == selectedVal) {
                        selectedOptionExists = true;
                    }
                }
                $("#indents").html(items);
                if (selectedVal != null && selectedVal != 0) {
                    if (!selectedOptionExists) {
                        var option = "<option value='"+selectedVal+"'> "+selectedText+" </option>";
                        $("#indents").append(option);
                    }
                    $("#indents").val(selectedVal);
                }
                $('.chosen-select').trigger('chosen:updated');
                $("#indents_chosen").removeClass("chosen-item-disabled").closest("td").attr("title", "");
                IsIndentContainerVisible();
                loadIndentMaterial(supp_id=$("#supplier").val(), is_job=parseInt($("#po_order").val()), 0);
            },
            error: function() {
                $("#indents_chosen").removeClass("chosen-item-disabled").closest("td").attr("title", "");
            }
        });
    },100);
}

function enableShippingAddress() {
    $("#id_purchase-shipping_name, #id_purchase-shipping_address").removeAttr("readonly");
    $(".enable_edit_option").addClass("hide");
}

if (performance.navigation.type == 1) {
    //window.location.href = "/erp/purchase/po/";
}

function calculateDeliveryDue(current){
    if($("#is_delivery_schedule").val() == "True") {
        var currentJson = JSON.parse($(current).closest("tr").find(".item_delivery_schedules").text());
        var totalQty = 0;
        var currentQty = $(current).val();
        var currentTR = $(current).closest("tr");
        for (i = 0; i < currentJson.length; i++) {  
            totalQty += Number(currentJson[i].delivery_qty);
        }
        if(currentQty > totalQty) {
            var updatedQty = Number(currentQty) - Number(totalQty);
            var consolidatedQty = Number(currentJson[currentJson.length -1 ]['delivery_qty']) + Number(updatedQty);
            currentJson[currentJson.length -1 ]['delivery_qty'] = consolidatedQty.toFixed(3);
            currentTR.find(".item_delivery_schedules").text(JSON.stringify(currentJson));
            var nextScheduleDate = moment(currentTR.find(".next-delivery-date").text()).format("YYYY-MM-DD");
            var nextScheduleQty = getSearchObjects(currentJson, 'delivery_date', nextScheduleDate);
            currentTR.find(".next-delivery-qty").text(nextScheduleQty[0].delivery_qty)
        }
        else if(currentQty < totalQty) {
            swal({
                title: "Do you want to Reset?",
                text: "Total Delivery Due Quantity<b>("+totalQty.toFixed(3)+")</b> is greater than the updated PO Quantity<b>("+Number(currentQty).toFixed(3)+")</b>.<br />Do you want to reset the Delivery Schedule?",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#209be1",
                confirmButtonText: "Yes, do it!",
                closeOnConfirm: true,
                closeOnCancel: true,
                allowOutsideClick: false,
                allowEscapeKey: false
            },
            function(isConfirm){
                if(isConfirm) {
                    var deliveryDueJson = [];
                    var deliveryDueObj = {
                        delivery_date: $("#deliverydate").val(),
                        delivery_qty: currentQty
                    };
                    deliveryDueJson.push(deliveryDueObj);
                    var currentTR = $(current).closest("tr");
                    currentTR.find(".item_delivery_schedules").text("");
                    currentTR.find(".item_delivery_schedules").text(JSON.stringify(deliveryDueJson));
                    currentTR.find(".total-delivery-date").text($("#po_delivery_date").val());
                    currentTR.find(".next-delivery-date").text($("#po_delivery_date").val());
                    currentTR.find(".next-delivery-qty").text(currentQty);
                }
                else {
                    $(current).val(totalQty.toFixed(3))
                }
            });
        }
    }    
}

function getSearchObjects(obj, key, val) {
    var objects = [];
    for (var i in obj) {
        if (!obj.hasOwnProperty(i)) continue;
        if (typeof obj[i] == 'object') {
            objects = objects.concat(obj[i]);
        } else if (i == key && obj[key] == val) {
            objects.push(obj);
        }
    }
    return objects;
}


function validateInlineSpq(current){
    var currentSpqValue = $(current).closest("tr").find(`input[name='item_spq']`).val() / $(current).closest("tr").find(`input[name='scale_factor']`).val();
    var currentMoqValue = $(current).closest("tr").find(`input[name='item_moq']`).val() / $(current).closest("tr").find(`input[name='scale_factor']`).val();
    var currentQty = $(current).val();
    var currentUnit = $(current).closest("tr").find(".td-uom").text();
    var currentElementId = $(current).attr("id");
    var isSpqValidated = confirmOrderQtyFromClient(currentSpqValue, currentMoqValue, currentQty, currentUnit, currentElementId);
    if(isSpqValidated) {
        calculateDeliveryDue($("#"+currentElementId));
        $("#"+currentElementId).trigger("blur");
    }
}

function gstCategoryChangeEvent(){
    var gstCategory = $("#id_gst_category-__prefix__-make_choices").val();
    if(["5", "6", "7", "9"].indexOf(gstCategory) != -1){
        $("#party-port-contianer").removeClass("hide");
    }
    else {
        $("#party-port-contianer").addClass("hide");
    }
    if(["3", "4", "5"].indexOf(gstCategory) == -1){
        $(".gstin_mandate").removeClass("hide");
    }
    else {
        $(".gstin_mandate").addClass("hide");
    }
    $("#modalPartyDetails .error-border").removeClass("error-border");
    $("#modalPartyDetails .custom-error-message").remove();
}

function poMaterialTableSerialNumberInit() {
    $("#po_materials_table tbody.item-for-goods tr:visible, #po_materials_table tbody.item-for-service tr:visible").each(function(i) {
       $(this).find(".po-item-sno").text(i+1+".")
    })
}

function setIndentRemarks(remarksString){
    if (remarksString != "") {
        remarks = [];
        $.each(JSON.parse(remarksString), function( index, value ) {
             remarks.push(value['remarks']);
         });
        if(remarks[remarks.length-1]) {
            $("#indent_remarks").val(remarks[remarks.length-1].replace(/(<|&lt;)BR\s*\/*(>|&gt;)/g,'. '));
        }
        else {
            remarksString = null;
        }
        displayRemarksHistory("indentRemarksList", remarksString, "indent_remarks_count");
        $('#id_showmore_po').show();
    }
}