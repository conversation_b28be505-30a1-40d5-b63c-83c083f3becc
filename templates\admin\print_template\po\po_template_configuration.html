{% extends 'admin/sidebar.html' %}
{% block po_template %}
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/bootstrap-input-spinner.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/jquery.slimscroll.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>
<link type="text/css" rel="stylesheet" href="/site_media/css/po_template_preview.css">
<link type="text/css" rel="stylesheet" href="/site_media/plugin/image_cropper/css/imgareaselect-default.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/plugin/image_cropper/css/jquery.awesome-cropper.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/plugin/ck_editor/js/ckeditor.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/plugin/ck_editor/js/ckeditor_other.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/plugin/image_cropper/js/jquery.imgareaselect.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/plugin/image_cropper/js/jquery.awesome-cropper.js?v={{ current_version }}"></script>
<style>
	.history-log-part {
	width: 100%;
    margin: 0;
    padding: 8px 15px;
    cursor: pointer;
    padding-left: 60px;
	}
	.history-log-part:last-child {
		border-bottom: none;
	}
	.history-log-part:hover {
		background: #e8f0fe;
	}
	.history-log-part:hover .history-log-date {
		font-weight: bold;
	}
	.history-log-date {
	   font-style: italic;
	}
	.history-log-username {
		font-size: 15px;
		padding-top: 3px;
		font-weight: bold;
	}

	.history-log-content {
		margin-top: 8px;
	}

	.history-log-content ul {
		padding: 0px 10px 0px 35px;
	}
	.history-log-content li {
		margin-bottom: 10px;
	}

	.history-log-content li:last-child {
		margin-bottom: 0;
	}

	.history-log-part.active .history-log-date,
	.history-log-part.active .history-log-username {
		color: #004195;
	}

	.history-log-part.active .history-log-date {
	    text-align: right;
		display: block;
	}

	.history-log-part .fa-chevron-down:before {
		content: "\f078";
		color: #999;
	}

	.history-log-part.active .fa-chevron-down:before {
		content: "\f077";
		color: #999;
	}

	#change_log_modal .modal-body {
		overflow: auto;
	}


	ul.timeline {
	    list-style-type: none;
	    position: relative;
		padding: 0px;
	}
	ul.timeline:before {
	    content: ' ';
	    background: #d4d9df;
	    display: inline-block;
	    position: absolute;
	    left: 29px;
	    width: 2px;
	    height: 94%;
	    z-index: 400;
        margin-top: 12px;
	}
	ul.timeline > li {

	}
	ul.timeline > li:before {
	    content: ' ';
	    background: white;
	    display: inline-block;
	    position: absolute;
	    border-radius: 50%;
	    border: 3px solid #209be1;
	    left: 23px;
	    width: 14px;
	    height: 14px;
	    z-index: 400;
	    margin-top: 4px;
	}
	ul.timeline > li:last-child:before {
		background: #209be1;
	}
	body {
		overflow-y: scroll;
	}

	.div_tax_types label{
		width: 100%;
	}

	.po_template_editor select.form-control {
		padding: 0;
	}

	#id_pth-name_font {
		width: auto;
		margin-bottom: 4px;
	}

	.tax_rate_column.tr_second_row.hide {
		/*display: table-column !important;
		visibility: hidden;*/
	}

	.bill_to_text,
	.ship_to_text {
		width: 100%;
		display: block;
    	margin-bottom: 4px;
    	padding: 2px;
	}
	.po_pdf_editor {
        width: 210mm;
        padding: 2mm;
        float: left;
        min-height: 257px;
        margin-left: 3px;
        margin-bottom: 10px;
        box-shadow: 0 0 3px #000;
        border-radius: 3px;
        line-height: 1.2;
	}
	.banner-image {
		width: 100px;
		height: 100px;
		border: dashed 1px #ccc;
		padding: 0;
		margin-right: 7px;
		margin-top: 8px;
	}

	.banner-image .uploader {
	    margin-top: 25px;
	    display: inline-block;
	    text-align: center;
	}

	.banner-container {
		padding: 0;
	}

	.banner-image.image-uploaded {
		border-style: solid;
		padding: 0;
	}

	.banner-image.image-uploaded:hover .uploaded-banner-image {
		opacity: 0.3;
	}

	.banner-image.image-uploaded:hover .uploader {
		display: inline-block !important;
	    position: absolute;
	    margin-left: 12px;
	    color: #000;
	}

	.banner-image .uploaded-banner-image {
		width: 100%;
		height: 100%;
		margin: 0;
		padding: 3px;
    	border-radius: 6px;
	}

	.uploaded-banner-image {
		width:100px;
		height: 100px;
		margin-top: 10px;
	}

	.remove-banner-image {
		position: absolute;
	    left: 90px;
	    top: -1px;
	    font-size: 13px;
	    color: red;
	    border: solid 1px red;
	    border-radius: 50px;
	    padding: 1px 2px 1px 3px;
	}
	.enterprise_total_details label {
	 width: 124px;
	margin-bottom: 5px;
	float: left;
	font-size: 10px;
}
.progress + .cropped_image {
		display: none;
	}
</style>

<div class="right-content-container">
	<div class="container" style="border: solid 1px #ccc; padding-right: 0">
		<h2 style="color: #113344; margin: 15px 0;">
			<span class="page_header_grn" style="margin-bottom: 15px;">Purchase Order Template
				<small class="last-modified-user-container">Last modified by <b id="last-modified-user" style="font-size: 12px;">{{modified_by.first_name}} {{modified_by.last_name}}</b> on <b>{{modified_on}}</b></small></span></span>
			{% if logged_in_user|canApprove:'PURCHASE' %}
				<input type="button" class="btn btn-save pull-right" style="margin-left: 8px; margin-right: 15px;" value="Save" onclick=' saveTemplateChanges()'/>
			{% else %}
				<input type="button" class="btn btn-save pull-right disabled" data-tooltip="tooltip" title="You do not have adequate permission to modify the Purchase Template" style="margin-left: 8px; margin-right: 15px; background: #999" value="Save" />
			{% endif %}
			{% if is_po_available %}
				<input type="button" class="btn btn-warning pull-right" id="pdf_preview" value="PDF Preview" onclick='previewTemplateChanges("/erp/admin/po_template/preview/")' />
			{% endif %}
		</h2>
		<div class="clearfix"></div>

		<input type="hidden" id="is_print_template_available" value = {{print_template_available}} />

		<div class="po_template_container row" style="width: 100%;">
			<div class="po_template_side_bar">
				<ul class="nav nav-stacked nav_purchase_template">
					<li data-field="select_appearence" data-tab="1" class="active">General</li>
					<li data-field="select_header" data-tab="2">Header</li>
					<li data-field="select_table" data-tab="3">Item Table</li>
					<li data-field="select_total" data-tab="4">Totals</li>
					<li data-field="select_misc" data-tab="5">Misc</li>
				</ul>
			</div>

			<div class="po_template_editor">
				<input type="hidden" id="saved_purchase_template" value="">
				<input id="id_ptc-template_id" name="ptc-template_id" type="hidden" value="{{template_id}}">
				<div class="po_template_edit select_appearence">
					<div class="form-horizontal">
						<div class="form-group">
							<label class="control-label col-sm-5">Base Font</label>
							<div class="col-sm-6">
								<select class="form-control chosen-select" id="id_ptg-base_font" name="ptg-base_font">
									{% for font in base_fonts %}
										<option value="{{ font.font_value }}" {% if font.font_value|upper == general_res.font_family|upper %} selected {% endif %}>{{ font.font_name }}</option>
									{% endfor %}
								</select>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-sm-5">Page Margin</label>
							<div class="col-sm-7 document-margin">
								<div class="col-sm-12 remove-padding">
							            <label class="spinner-label">TOP</label>
							            <input class="input_spinner" id="id_ptg-margin_top" max="100" min="0" name="ptg-margin_top" spinner_for="page_margin_top" step="1" type="number" value="{{ general_res.margin.top }}" style="display: none;">
							            <span class="doc-margin-prefix"> mm</span>
								</div>
								<div class="col-sm-12 remove-padding">
									<label class="spinner-label">LEFT</label>
									<input class="input_spinner" id="id_ptg-margin_left" max="100" min="0" name="ptg-margin_left" spinner_for="page_margin_left" step="1" type="number" value="{{ general_res.margin.left }}" style="display: none;">
									<span class="doc-margin-prefix"> mm</span>
								</div>
								<div class="col-sm-12 remove-padding">
									<label class="spinner-label">BOTTOM</label>
									<input class="input_spinner" id="id_ptg-margin_bottom" max="100" min="0" name="ptg-margin_bottom" spinner_for="page_margin_bottom" step="1" type="number" value="{{ general_res.margin.bottom }}" style="display: none;">
									<span class="doc-margin-prefix"> mm</span>
								</div>
								<div class="col-sm-12 remove-padding">
									<label class="spinner-label">RIGHT</label>
									<input class="input_spinner" id="id_ptg-margin_right" max="100" min="0" name="ptg-margin_right" spinner_for="page_margin_right" step="1" type="number" value="{{ general_res.margin.right }}" style="display: none;">
									<span class="doc-margin-prefix"> mm</span>
								</div>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-sm-5">Page Size</label>
							<div class="col-sm-6" style="margin-top: 9px;">
								A4 (210mm x 297mm) <i class="fa fa-info-circle" style="color: #666;" aria-hidden="true" data-tooltip="tooltip" title="" data-original-title="Other page size will be available in future release."></i>
							</div>
						</div>
						<div class="form-group">
							<label class="date-format-label" style="margin-left: 15px;">Date Format</label>
							<br />
							<div class="container" style="width:327px;border: 1px solid #ccc;">
								<table id="dateFormatter" class="form-group" style="max-width: 383px; float: left;display: block;width:319px;padding-top: 10px;padding-left: 15px;">
									<tr>
										<td style="padding-right: 10px;">
											<label style="cursor: move;"><i style='font-size: 14px;' class="fa fa-bars" aria-hidden="true"></i> Date</label>
											<select class="form-control date-formatter date-formatter-date">
												<option value="" data-value="">-</option>
												<option value="%d" data-value="D">D</option>
											</select>
										</td>
										<td style="padding-right: 10px;">
											<label></label>
											<select class="form-control date-formatter date-formatter-seperator1">
												<option value=" " data-value=" "> </option>
												<option value="/" data-value="/">/</option>
												<option value="-" data-value="-">-</option>
												<option value="." data-value=".">.</option>
												<option value=", " data-value=", ">,</option>
											</select>
										</td>
										<td style="padding-right: 10px;">
											<label style="cursor: move;"><i style='font-size: 14px;' class="fa fa-bars" aria-hidden="true"></i> Month</label>
											<select class="form-control date-formatter date-formatter-month">
												<option value="" data-value="">-</option>
												<option value="%m" data-value="MM">MM</option>
												<option value="%b" data-value="MMM">MMM</option>
												<option value="%B" data-value="MMMM">MMMM</option>
											</select>
										</td>
										<td style="padding-right: 10px;">
											<label></label>
											<select class="form-control date-formatter date-formatter-seperator2">
												<option value=" " data-value=" "> </option>
												<option value="/" data-value="/">/</option>
												<option value="-" data-value="-">-</option>
												<option value="." data-value=".">.</option>
												<option value=", " data-value=", ">,</option>
											</select>
										</td>
										<td style="padding-right: 10px;">
											<label style="cursor: move;"><i style='font-size: 14px;' class="fa fa-bars" aria-hidden="true"></i> Year</label>
											<select class="form-control date-formatter date-formatter-year">
												<option value="" data-value="">-</option>
												<option value="%y" data-value="YY">YY</option>
												<option value="%Y" data-value="YYYY">YYYY</option>
											</select>
										</td>
									</tr>
								</table>

								<table id="timeFormatter" style="margin-bottom: 10px;">
									<tr>
										<td style="padding-right:9px;">
											<label>Hour</label>
											<select class="form-control time-formatter time-formatter-hour" style="width: 60px;">
												<option value="" data-value="">-</option>
												<option value=" %H" data-value="HH">HH</option>
												<option value=" %I" data-value="hh">hh</option>
											</select>
										</td>
										<td style="padding-right:5px;">
											<label>Minutes</label>
											<select class="form-control time-formatter time-formatter-minute" style="width: 60px;">
												<option value="" data-value="">-</option>
												<option value=":%M" data-value="mm">mm</option>
											</select>
										</td>
										<td style="padding-left:7px;">
											<label>Seconds</label>
											<select class="form-control time-formatter time-formatter-second" style="width: 60px;";>
												<option value="" data-value="">-</option>
												<option value=":%S" data-value="ss">ss</option>
											</select>
										</td>
									</tr>
								</table>
								<span style="font-size: 10px;">* This date format will apply for all the dates in Purchase document.</span>
							</div>
							<input id="id_ptg-po_doc_datetime_format" name="ptg-po_doc_datetime_format" type="hidden" value="{{general_res.datetime}}">
						</div>
					</div>
				</div>

				<div class="po_template_edit select_header hide">
					<div class="form-horizontal">
						<div class="form-group form-group-small">
							<label class="control-label col-sm-3" style="color: #209be1; text-shadow: 0 0 #000;">Company</label>
						        <div class="col-sm-12 checkbox">
							        <input checkboxfor="pdf_company_logo" {% if header_res.company.logo.print %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_pth-include_logo" name="pth-include_logo" type="checkbox">
									<label for="id_pth-include_logo" style="float: left;">LOGO</label>
									<span class="slidecontainer" style="float: left; margin-left: 15px;">
										<input class="slider" id="id_pth-logo_size" max="100" min="0" name="pth-logo_size" step="1" type="range" value="{{header_res.company.logo.size}}">
							        </span>
							        <span id="logo_size_value" style="margin-left: 10px;">{{header_res.company.logo.size}}</span>
						        </div>
						</div>
						<div class="form-group form-group-small" style="border-bottom: solid 1px #eee;">
							<div class=" checkbox">
								<input checkboxfor="pdf_company_name" {% if header_res.company.company_name.print %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_pth-include_name" name="pth-include_name" type="checkbox">
								<label for="id_pth-include_name">Name</label>
							</div>
							<div class="form-horizontal">
								<div class="col-sm-12" style="margin-left: 40px;">
									<label class="spinner-label" style="width: 100px; font-size: 10px;">FONT</label>
									<select class="form-control chosen-select" id="id_pth-name_font" name="pth-name_font" style="width: 53%;">
										{% for font in base_fonts %}
											<option value="{{ font.font_value }}" {% if font.font_value|upper == header_res.company.company_name.font_family|upper %} selected {% endif %}>{{ font.font_name }}</option>
										{% endfor %}
									</select>
									<div class="document-margin">
										<label class="spinner-label" style="width: 100px; font-size: 10px;">Font Size</label>
										<input class="input_spinner" id="id_pth-name_font_size" max="100" min="0" name="pth-name_font_size" spinner_for="pdf_company_name" step="1" type="number" value="{{header_res.company.company_name.font_size}}" style="display: none;">
										<span class="doc-margin-prefix"> px</span>
									</div>
								</div>
							</div>
						</div>
						<div class="form-group form-group-small">
							<label class="control-label col-sm-12" style="color: #209be1; text-shadow: 0 0 #000;">Form Name</label>
							<div class="form-horizontal">
								<div class="col-sm-12 document-margin">
									<label class="spinner-label" style="width: 100px;">Font Size</label>
									<input class="input_spinner" id="id_pth-form_name_font_size" max="100" min="0" name="pth-form_name_font_size" spinner_for="pdf_form_name" step="1" type="number" value="{{header_res.form_name.font_size}}" style="display: none;">
									<span class="doc-margin-prefix"> px</span>
								</div>
							</div>
						</div>
							<div class="form-group form-group-small form-medium-text" style="border-bottom: solid 1px #eee;">
					            <div class="col-sm-12 enterprise_form_details" style="margin-top: 10px; padding-left: 30px;">
							        <div class="form-group" style="margin-bottom: 12px;">
										<label>Purchase order</label>
								        <input class="form-control form-alternate-text" id="id_pth-purchase_order_label" maxlength="30" name="pth-gst_label" textboxfor="pdf_form_name_text" type="text" value="{{header_res.form_name.label.po}}">
									</div>
							        <div class="form-group" style="margin-bottom: 12px;">
										<label>Job order</label>
								        <input class="form-control form-alternate-text" id="id_pth-job_order_label" maxlength="30" name="pth-service_label" textboxfor="pdf_form_name_text" type="text" value="{{header_res.form_name.label.jo}}">
									</div>
						        </div>
						    </div>
						    <div class="form-group form-group-small">
						    	<div class="form-horizontal">
									<div class="col-sm-12 document-margin">
							            <label class="spinner-label" style="width: 100px;">Font Size</label>
							                <input class="input_spinner" id="id_pth-font_size" max="100" min="0" name="pth-font_size" spinner_for="pdf_company_details" step="1" type="number" value="{{header_res.font_size}}" style="display: none;">
							            <span class="doc-margin-prefix"> px</span>
							        </div>
							    </div>
						        <div class="col-sm-12 checkbox enterprise_header_details">
						            <div class="col-sm-12 remove-padding checkbox">
							            <div class="col-sm-6 remove-padding">
							            	<input checkboxfor="pdf_company_address" {% if header_res.company_info.print_address %} checked="checked" {% endif %} class="required_checkbox" id="id_pth-include_address" name="pth-include_address" type="checkbox">
											<label for="id_pth-include_address">Address</label>
										</div>
										<div class="col-sm-6 remove-padding">
								            <input checkboxfor="pdf_company_contact" {% if header_res.company_info.print_phone_no %} checked="checked" {% endif %} class="required_checkbox" id="id_pth-include_phone_no" name="pth-include_phone_no" type="checkbox">
											<label for="id_pth-include_phone_no">Phone</label>
										</div>
										<div class="col-sm-6 remove-padding">
								            <input checkboxfor="pdf_company_email" {% if header_res.company_info.print_email %} checked="checked" {% endif %} class="required_checkbox" id="id_pth-include_email" name="pth-include_email" type="checkbox">
											<label for="id_pth-include_email">Email</label>
										</div>
										<div class="col-sm-6 remove-padding">
								            <input checkboxfor="pdf_company_fax" {% if header_res.company_info.print_fax %} checked="checked" {% endif %} class="required_checkbox" id="id_pth-include_fax" name="pth-include_fax" type="checkbox">
											<label for="id_pth-include_fax">Fax</label>
										</div>
						            </div>
						            <div class="col-sm-12 remove-padding" style="margin-left: -34px; margin-bottom: 7px; letter-spacing: 0.8px;">
						            	<span class="control-label col-sm-12" style="color: #209be1; text-shadow: 0 0 #000;">REGISTRATION DETAILS</span>
						            </div>
							        <div class="col-sm-12 remove-padding checkbox registration_details">
							            {% for item in enterprise_reg %}
								            <div class="col-sm-6 remove-padding">
								                <input checkboxfor="pdf_registration_{{ item.label_id }}" data-field-id="{{ item.label_id }}" class="required_checkbox" id="id_pth-include_in_purchase_template_{{ item.label_id }}" name="pth-include_in_purchase_template" type="checkbox"
								                       {% for i in included_reg_items %} {% if item.label_id|add:"0" == i.label_id|add:"0" %} checked="checked" {% endif %} {% endfor %}>
								                <label for="id_pth-include_in_purchase_template_{{ item.label_id }}" style="width: 125px;">{{ item.label }}</label>
								            </div>
										{% endfor %}
						            </div>
						            <div class="col-sm-12 remove-padding checkbox">
										<div class="form-group form-group-small form-medium-text">
										<div class="enterprise_details">
											<div class="checkbox form-group">
												<input checkboxfor="pdf_bill_to_address" {% if header_res.billing_address.print %} checked="checked" {% endif %}  class="checkbox required_checkbox"  style="margin-top:1px" id="id_pth-include_billingaddress" name="pth-include_billingaddress" type="checkbox">
												<label for="id_pth-include_billingaddress" style="width: 148px;margin-top:9px">Billing Address</label>
												<input class="form-control form-alternate-text" style="margin-top:1px" id="id_pth-billingaddress_label" maxlength="30" name="pth-billingaddress_label" textboxfor="bill_to_text" type="text" value="{{header_res.billing_address.label}}">
											</div>
											<div class="checkbox form-group">
													<input checkboxfor="pdf_ship_to_address" {% if header_res.shipping_address.print %} checked="checked" {% endif %}  class="checkbox required_checkbox" style="margin-top:1px" id="id_pth-include_shippingaddress" name="pth-include_shippingaddress" type="checkbox">
													<label for="id_pth-include_shippingaddress" style="width: 148px;margin-top:9px">Shipping Address</label>
													<input class="form-control form-alternate-text" style="margin-top:1px" id="id_pth-shippingaddress_label" maxlength="30" name="pth-shippingaddress_label" textboxfor="ship_to_text" type="text" value="{{header_res.shipping_address.label}}">
											</div>
										</div>
									</div>
						            </div>
								</div>
							</div>
						    <div class="form-group form-group-small form-medium-text">
						        <label class="control-label col-sm-3" style="color: #209be1; text-shadow: 0 0 #000;">Party</label>
								 <div class="col-sm-12 enterprise_details">
									<div class="checkbox form-group">
										<input checkboxfor="pdf_vendor_to_enterprise_name" {% if header_res.vendor.name.print %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_pth-include_vendorname" name="pth-include_vendorname" type="checkbox">
										<label for="id_pth-include_vendorname" style="width: 148px;">Name</label>
										<input class="form-control form-alternate-text" id="id_pth-vendornames_label" maxlength="30" name="pth-vendorname_label" textboxfor="vendorname_to_text" type="text" value="{{ header_res.vendor.name.label }}">

									</div>
							        <div class="checkbox form-group">
								        <input checkboxfor="pdf_vendor_to_address" {% if header_res.vendor.address.print %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_pth-include_vendoraddress" name="pth-include_vendoraddress" type="checkbox">
										<label for="id_pth-include_vendoraddress" style="width: 148px;"> Address</label>
								        <input class="form-control form-alternate-text" id="id_pth-vendoraddress_label" maxlength="30" name="pth-vendoraddress_label" textboxfor="vendoraddress_to_text" type="text" value="{{ header_res.vendor.address.label }}">
									</div>
									 <div class="checkbox form-group">
								        <input checkboxfor="pdf_vendor_to_enterprise_code" {% if header_res.vendor.code.print %} checked="checked" {% endif %} class="required_checkbox" id="id_pth-include_vendorcode" name="pth-include_partycode" type="checkbox">
										<label for="id_pth-include_vendorcode" style="width: 148px;">Code</label>
								        <input class="form-control form-alternate-text" id="id_pth-vendorcode_label" maxlength="30" name="pth-vendorcode_label" textboxfor="vendorcode_to_text" type="text" value="{{ header_res.vendor.code.label }}">
									</div>
									<div class="checkbox form-group checkbox" style="margin-left: 15px;">
										<input checkboxfor="pdf_vendor_to_contact" {% if header_res.vendor.print_phone_no %} checked="checked" {% endif %} class="required_checkbox" id="id_pth-include_vendor_phoneno" name="pth-include_vendor_phoneno" type="checkbox">
										<label for="id_pth-include_vendor_phoneno" style="width: 118px;">Phone</label>
										<input checkboxfor="pdf_vendor_to_email" {% if header_res.vendor.print_email %} checked="checked" {% endif %} class="required_checkbox" id="id_pth-include_vendor_email" name="pth-include_vendor_email" type="checkbox">
										<label for="id_pth-include_vendor_email" style="width: 118px;">Email</label>
									</div>
									 <div class="checkbox form-group">
										 <input checkboxfor="pdf_vendor_ack" {% if header_res.vendor.acknowledge.print %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_pth-include_vendoracknowledgement" name="pth-include_name" type="checkbox">
										 <label for="id_pth-include_vendoracknowledgement" style="width: 148px;">party acknowledgement</label>
										 <input class="form-control form-alternate-text" id="id_pth-vendoracknowledgement_label" maxlength="30" name="pth-name_label" textboxfor="vendoracknowledgement_to_text" type="text" value="{{ header_res.vendor.acknowledge.label }}">
									</div>
						        </div>
						    </div>
						    <div class="form-group form-medium-text" style="margin-bottom: 0;">
						        <label class="control-label col-sm-12" style="color: #209be1; text-shadow: 0 0 #000;">Field Name</label>
							</div>
							<div class="form-group form-group-small form-medium-text">
						        <label class="control-label col-sm-12"></label>
					            <div class="col-sm-12 enterprise_details enterprise_details_packing_details">
									<div class=" form-group "style="margin-bottom: 12px;padding-left:30px">
										<label>PO No</label>
						                <input class="form-control form-alternate-text" id="id_pth-pono_label" maxlength="30" name="pth-pono_label" textboxfor="pdf_po_no_txt" type="text" value="{{ header_res.field_name.pono_label }}">
									</div>
						            <div class=" form-group "style="margin-bottom: 12px;padding-left:30px">
											<label>jo No <i class="fa fa-info-circle" style="color: #666;" aria-hidden="true" data-tooltip="tooltip" title="PO No label will be replaced with this one, in case of a jo."></i></label>
						                <input class="form-control form-alternate-text" id="id_pth-jo_no_label" maxlength="30" name="pth-jo_no_label" textboxfor="pdf_jo_no_txt" type="text" value="{{ header_res.field_name.jono_label }}">
									</div>
									<div class=" form-group "style="margin-bottom: 12px;padding-left:30px">
										<label>PO Date</label>
						                <input class="form-control form-alternate-text" id="id_pth-podate_label" maxlength="30" name="pth-podate_label" textboxfor="pdf_podate_txt" type="text" value="{{ header_res.field_name.po_datelabel }}">
									</div>
						            <div class="checkbox form-group checkbox">
						                <input checkboxfor="pdf_indent_no" {% if header_res.field_name.indent_no.print %} checked="checked" {% endif %} class="required_checkbox" id="id_pth-include_indent_no" name="pth-include_indent_no" type="checkbox">
										<label for="id_pth-include_indent_no">Indent no</label>
						                <input class="form-control form-alternate-text" id="id_pth-indent_no_label" maxlength="30" name="pth-indent_no_label" textboxfor="pdf_indent_no_txt" type="text" value="{{ header_res.field_name.indent_no.label }}">
									</div>
						            <div class="checkbox form-group checkbox">
						                <input checkboxfor="pdf_indent_date" {% if header_res.field_name.indent_date.print %} checked="checked" {% endif %} class="required_checkbox" id="id_pth-include_indent_date" name="pth-include_indent_date" type="checkbox">
										<label for="id_pth-include_indent_date">Indent date</label>
						                <input class="form-control form-alternate-text" id="id_pth-indent_date_label" maxlength="30" name="pth-indent_date_label" textboxfor="pdf_indent_date_txt" type="text" value="{{ header_res.field_name.indent_date.label }}">
									</div>
					                <div class="checkbox form-group checkbox">
						                <input checkboxfor="pdf_transport_currency" {% if header_res.field_name.currency.print %} checked="checked" {% endif %} class="required_checkbox" id="id_pth-include_currency" name="pth-include_currency" type="checkbox">
										<label for="id_pth-include_currency">Currency</label>
						                <input class="form-control form-alternate-text" id="id_pth-currency_label" maxlength="30" name="pth-currency_label" textboxfor="pdf_currency_txt" type="text" value="{{ header_res.field_name.currency.label }}">
									</div>
									<div class="checkbox form-group checkbox">
										<input checkboxfor="pdf_quotn_ref" {% if header_res.field_name.quotn_ref_no.print %} checked="checked" {% endif %} class="required_checkbox" id="id_pth-include_quotn_ref" name="pth-include_quotn_ref" type="checkbox">
										<label for="id_pth-include_quotn_ref">Quotn ref no</label>
										<input class="form-control form-alternate-text" id="id_pth-quotn_ref_label" maxlength="30" name="pth-quotn_ref_label" textboxfor="pdf_quotn_ref_txt" type="text" value="{{ header_res.field_name.quotn_ref_no.label }}">
									</div>
									<div class="checkbox form-group checkbox">
										<input checkboxfor="pdf_quotn_date" {% if header_res.field_name.quotn_date.print %} checked="checked" {% endif %} class="required_checkbox" id="id_pth-include_quotn_date" name="pth-include_quotn_date" type="checkbox">
										<label for="id_pth-include_quotn_date">Quotn date</label>
										<input class="form-control form-alternate-text" id="id_pth-quotn_date_label" maxlength="30" name="pth-quotn_date_label" textboxfor="pdf_quotn_date_txt" type="text" value="{{ header_res.field_name.quotn_date.label }}">
									</div>
									<div class="checkbox form-group checkbox">
										<input checkboxfor="pdf_payment_term" {% if header_res.terms_conditions.payment_terms.print %} checked="checked" {% endif %} class="required_checkbox" id="id_pth-include_paymentterms" name="pth-include_paymentterms" type="checkbox">
										<label for="id_pth-include_paymentterms">Payment Terms</label>
										<input class="form-control form-alternate-text" id="id_pth-paymentterms_label" maxlength="30" name="pth-paymentterms_label" textboxfor="pdf_payment_term_txt" type="text" value="{{ header_res.terms_conditions.payment_terms.label }}">
									</div>
									<div class="checkbox form-group checkbox">
										<input checkboxfor="po_special_instruction" {% if header_res.terms_conditions.special_instructions.print %} checked="checked" {% endif %} class="required_checkbox" id="id_pth-include_splinstructions" name="pth-include_splinstructions" type="checkbox">
										<label for="id_pth-include_splinstructions">Special Instruction</label>
										<input class="form-control form-alternate-text" id="id_pth-splinstructions_label" maxlength="30" name="pth-splinstructions_label" textboxfor="po_special_instruction_txt" type="text" value="{{ header_res.terms_conditions.special_instructions.label }}">
									</div>
									<div class="checkbox form-group checkbox">
										<input checkboxfor="pdf_auth_sign" {% if header_res.field_name.authorized_sign.print %} checked="checked" {% endif %} class="required_checkbox" id="id_pth-include_authorizedsign" name="pth-include_authorizedsign" type="checkbox">
										<label for="id_pth-include_authorizedsign"> AUTHORISED SIGN</label>
										<input class="form-control form-alternate-text" id="id_pth-authorizedsign_label" maxlength="30" name="pth-authorizedsign_label" textboxfor="pdf_auth_sign_txt" type="text" value="{{ header_res.field_name.authorized_sign.label }}">
									</div>
									<div class="checkbox form-group checkbox">
										<input checkboxfor="pdf_prepared_sign" {% if header_res.field_name.prepared_by.print %} checked="checked" {% endif %} class="required_checkbox" id="id_pth-include_preparedsign" name="pth-include_preparedsign" type="checkbox">
										<label for="id_pth-include_preparedsign">PreparedBy & checkedby</label>
										<input class="form-control form-alternate-text" id="id_pth-preparedsign_label" maxlength="30" name="pth-preparedsign_label" textboxfor="pdf_prepared_sign_txt" type="text" value="{{ header_res.field_name.prepared_by.label }}">
									</div>
									<div class="checkbox form-group">
									<input checkboxfor="show_approver_signature" {% if header_res.field_name.authorized_sign.print_approversign %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_pth-include_approver_signature" name="pth-include_approver_signature" type="checkbox">
										<label for="id_pth-include_approver_signature" style="width: 100%;"> include Approver Signatory</label>
									</div>
									<div class="checkbox form-group">
									<input checkboxfor="show_prepared_signature" {% if header_res.field_name.prepared_by.print_approversign %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_pth-include_prepared_signature" name="pth-include_prepared_signature" type="checkbox">
										<label for="id_pth-include_prepared_signature" style="width: 100%;">prepared by sign</label>
									</div>
						        </div>
						    </div>
					    </div>
				</div>

				<div class="po_template_edit select_table hide">
					<div class="form-horizontal">
						<div class="col-sm-12 document-margin" style="padding: 0;">
					         <label class="spinner-label" style="width: 100px;">Font Size</label>
							<input class="input_spinner" id="id_pti-font_size" max="100" min="0" name="pti-font_size" spinner_for="table_font_size" step="1" type="number" value="{{ item_res.item_table.font_size }}" style="display: none;">
							<span class="doc-margin-prefix"> px</span>
					    </div>
					    <div class="col-sm-12 enterprise_details" style="padding: 0;">
					        <div style="display: inline-block;">
					              <label>Column</label>
					              <label>Label</label>
					               <label style="width: 60px; text-align: center;">Width%</label>
					        </div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="td_sno" {% if item_res.item_table.sno.print %} checked="checked" {% endif %} class="checkbox required_checkbox width_calc_checkbox" id="id_pti-include_sno" name="pti-include_sno" type="checkbox">
								<label for="id_pti-include_sno">S. No</label>
								<input class="form-control form-alternate-text" id="id_pti-sno_label" maxlength="30" name="pti-sno_label" textboxfor="td_sno_text" type="text" value="{{ item_res.item_table.sno.label }}">
								<input class="input_spinner input_spinner_set_width" id="id_pti-sno_width" max="100" min="0" name="pti-sno_width" spinner_for="td_sno" step="1" type="number" value="{{ item_res.item_table.sno.width }}" disabled="disabled" style="display: none;">
							</div>
							<div class="input_width_spinner form-group" style="margin-left: 20px; margin-bottom: -6px;">
								<label style="padding-left: 6px;">Item Details</label>
									<input class="form-control form-alternate-text" id="id_pti-itemdetails_label" maxlength="30" name="pti-itemdetails_label" textboxfor="td_description_text" type="text" value="{{ item_res.item_table.item_details.label }}">
									<input class="input_spinner input_spinner_set_width" disabled="disabled" id="id_pti-itemdetails_width" max="100" min="0" name="pti-itemdetails_width" spinner_for="td_description" step="1" type="number" value="{{ item_res.item_table.item_details.width }}" style="display: none;">
									<input id="id_pti-itemdetails_width_hidden_field" name="pti-itemdetails_width_hidden_field" type="hidden" value="{{ item_res.item_table.item_details.width }}">
							</div>
							<div class="form-group input_width_spinner input_sub_item" style="margin-top: 6px; margin-bottom: 5px;">
								<label style="padding-left: 5px;">Name</label>
								<input class="form-control form-alternate-text" id="id_pti-name_label" maxlength="30" name="pti-name_label" textboxfor="pdf_item_name_txt" type="text" value="{{ item_res.item_table.item_details.itemname_label }}">
							</div>
							<div class="checkbox form-group input_width_spinner input_sub_item">
								<input checkboxfor="pdf_item_drawing_number" {% if item_res.item_table.item_details.itemcode.print %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_pti-include_itemcode" name="pti-include_itemcode" type="checkbox">
								<label for="id_pti-include_itemcode">Item Code</label>
								<input class="form-control form-alternate-text" id="id_pti-itemcode_label" maxlength="30" name="pti-itemcode_label" textboxfor="pdf_item_drawing_number_txt" type="text" value="{{ item_res.item_table.item_details.itemcode.label }}">
							</div>
							<div class="checkbox form-group input_width_spinner input_sub_item" style="display:none">
									<input checkboxfor="pdf_item_make" {% if item_res.item_table.item_details.make.print %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_pti-include_make" name="pti-include_make" type="checkbox">
									<label for="id_pti-include_make">Make</label>
									<input class="form-control form-alternate-text" id="id_pti-make_label" maxlength="50" name="pti-make_label" textboxfor="pdf_item_make_txt" type="text" value="{{ item_res.item_table.item_details.make.label }}">
							</div>
							<div class="checkbox form-group input_width_spinner input_sub_item" style="display:none">
									<input checkboxfor="pdf_item_part" {% if item_res.item_table.item_details.part_no.print %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_pti-include_partno" name="pti-include_partno" type="checkbox">
									<label for="id_pti-include_partno">Part No.</label>
									<input class="form-control form-alternate-text" id="id_pti-partno_label" maxlength="50" name="pti-partno_label" textboxfor="pdf_item_part_txt" type="text" value="{{ item_res.item_table.item_details.part_no.label }}">
							</div>
							<div class="checkbox form-group input_width_spinner input_sub_item">
								<input checkboxfor="pdf_item_desc" {% if item_res.item_table.item_details.description.print %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_pti-include_description" name="pti-include_description" type="checkbox">
								<label for="id_pti-include_description">Description</label>
								<input class="form-control form-alternate-text" id="id_pti-description_label" maxlength="30" name="pti-description_label" textboxfor="pdf_item_desc_txt" type="text" value="{{ item_res.item_table.item_details.description.label }}">
							</div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="pdf_item_hsn_code" {% if item_res.item_table.hsnsac.print %} checked="checked" {% endif %} class="checkbox customized_checkbox width_calc_checkbox" id="id_pti-include_hsnsac" name="pti-include_hsnsac" type="checkbox">
								<label for="id_pti-include_hsnsac">HSN/SAC</label>
								<input class="form-control form-alternate-text" id="id_pti-hsnsac_label" maxlength="30" name="pti-hsnsac_label" textboxfor="pdf_item_hsn_code_txt" type="text" value="{{ item_res.item_table.hsnsac.label }}">
								<input class="input_spinner input_spinner_set_width" id="id_pti-hsnsac_width" max="100" min="0" name="pti-hsnsac_width" spinner_for="td_hsn_code" step="1" type="number" value="{{ item_res.item_table.hsnsac.width }}" disabled="disabled" style="display: none;">
								<input checkboxfor="pdf_hsn_in_description" {% if item_res.item_table.hsnsac.print_in_itemdetails %} checked="checked" {% endif %} class="checkbox customized_checkbox" id="id_pti-hsnsac_part_of_itemdetails" name="pti-hsnsac_part_of_itemdetails" type="checkbox">
								<label for="id_pti-hsnsac_part_of_itemdetails" style="margin-left: 125px; width: calc(100% - 125px);">Part of Item Details</label>
							</div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="td_qty" {% if item_res.item_table.quantity.print %} checked="checked" {% endif %} class="checkbox customized_checkbox width_calc_checkbox" id="id_pti-include_quantity" name="pti-include_quantity" type="checkbox">
								<label for="id_pti-include_quantity">Quantity</label>
								<input class="form-control form-alternate-text" id="id_pti-quantity_label" maxlength="20" name="pti-quantity_label" textboxfor="td_qty_text" type="text" value="{{ item_res.item_table.quantity.label }}">
								<input class="input_spinner input_spinner_set_width" id="id_pti-quantity_width" max="100" min="0" name="pti-quantity_width" spinner_for="td_qty_text" step="1" type="number" value="{{ item_res.item_table.quantity.width }}" style="display: none;">
							</div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="td_uom" {% if item_res.item_table.units.print %} checked="checked" {% endif %} class="checkbox customized_checkbox width_calc_checkbox" id="id_pti-include_units" name="pti-include_units" type="checkbox">
								<label for="id_pti-include_units">Units</label>
								<input class="form-control form-alternate-text" id="id_pti-units_label" maxlength="20" name="pti-units_label" textboxfor="td_uom_text" type="text" value="{{ item_res.item_table.units.label }}">
								<input class="input_spinner input_spinner_set_width" id="id_pti-units_width" max="100" min="0" name="pti-units_width" spinner_for="td_uom_text" step="1" type="number" value="{{ item_res.item_table.units.width }}" disabled="disabled" style="display: none;">
								<input checkboxfor="pdf_unit_in_price" {% if item_res.item_table.units.units_in_qty %} checked="checked" {% endif %} class="checkbox customized_checkbox" id="id_pti-units_in_quantity_column" name="pti-units_in_quantity_column" type="checkbox" disabled="disabled">
								<label for="id_pti-units_in_quantity_column" style="margin-left: 125px; width: calc(100% - 125px);">In Qty Column</label>
							</div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="td_price" {% if item_res.item_table.unit_price.print %} checked="checked" {% endif %} class="checkbox required_checkbox width_calc_checkbox" id="id_pti-include_unit_price" name="pti-include_unit_price" type="checkbox">
								<label for="id_pti-include_unit_price">Unit Price</label>
								<input class="form-control form-alternate-text" id="id_pti-unit_price_label" maxlength="30" name="pti-unit_price_label" textboxfor="td_price_text" type="text" value="{{ item_res.item_table.unit_price.label }}">
								<input class="input_spinner input_spinner_set_width" id="id_pti-unit_price_width" max="100" min="0" name="pti-unit_price_width" spinner_for="td_price_text" step="1" type="number" value="{{ item_res.item_table.unit_price.width }}" style="display: none;">
							</div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="td_disc" {% if item_res.item_table.discount.print %} checked="checked" {% endif %} class="checkbox required_checkbox width_calc_checkbox" id="id_pti-include_discount" name="pti-include_discount" type="checkbox">
								<label for="id_pti-include_discount">Discount</label>
								<input class="form-control form-alternate-text" id="id_pti-discount_label" maxlength="30" name="pti-discount_label" textboxfor="td_disc_text" type="text" value="{{ item_res.item_table.discount.label }}">
								<input class="input_spinner input_spinner_set_width" id="id_pti-discount_width" max="100" min="0" name="pti-discount_width" spinner_for="td_disc_text" step="1" type="number" value="{{ item_res.item_table.discount.width }}" style="display: none;">
							</div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="td_tax" {% if item_res.item_table.taxable_amount.print %} checked="checked" {% endif %} class="checkbox customized_checkbox width_calc_checkbox" id="id_pti-include_taxable_amount" name="pti-include_taxable_amount" type="checkbox">
								<label for="id_pti-include_taxable_amount">Taxable Amount</label>
								<input class="form-control form-alternate-text" id="id_pti-taxable_amount_label" maxlength="30" name="pti-taxable_amount_label" textboxfor="td_tax_text" type="text" value="{{ item_res.item_table.taxable_amount.label }}">
								<input class="input_spinner input_spinner_set_width" id="id_pti-taxable_amount_width" max="100" min="0" name="pti-taxable_amount_width" spinner_for="td_tax_text" step="1" type="number" value="{{ item_res.item_table.taxable_amount.width }}" style="display: none;">
							</div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="taxable_item" {% if item_res.item_table.tax.print %} checked="checked" {% endif %} class="checkbox customized_checkbox" id="id_pti-include_tax" name="pti-include_tax" type="checkbox">
								<label for="id_pti-include_tax">Tax</label>
							</div>
							<div class="form-group input_width_spinner div_tax_types">
								<ul id="id_pti-tax_type">
									{% for key, value in tax_type %}
										<label for="id_pti-tax_type_{{forloop.counter}}">
											<input {% if item_res.item_table.tax.field_type|add:"0" == key|add:"0" %}checked='checked'{% endif %} id="id_pti-tax_type_{{forloop.counter}}" name="pti-tax_type" type="radio" value="{{ key }}"> 
											{{ value }}
										</label>
									{% endfor %}
								</ul>
							</div>
							<div class="checkbox form-group input_width_spinner input_sub_item">
								<input checkboxfor="tax_rate_column" {% if item_res.item_table.tax.taxrate.print %} checked="checked" {% endif %} class="checkbox customized_checkbox width_calc_checkbox taxable_item" id="id_pti-include_taxrate" name="pti-include_taxrate" type="checkbox" disabled="disabled">
								<label for="id_pti-include_taxrate" style="width: 177px;">Tax Rate</label>
								<input class="input_spinner input_spinner_set_width taxable_item" id="id_pti-taxrate_width" max="100" min="0" name="pti-taxrate_width" spinner_for="td_tax_rate" step="1" type="number" value="{{ item_res.item_table.tax.taxrate.width }}" disabled="disabled" style="display: none;">
							</div>
							<div class="checkbox form-group input_width_spinner input_sub_item">
								<input checkboxfor="tax_amount_column" {% if item_res.item_table.tax.taxamount.print %} checked="checked" {% endif %} class="checkbox customized_checkbox width_calc_checkbox taxable_item" id="id_pti-include_taxamount" name="pti-include_taxamount" type="checkbox" disabled="disabled">
								<label for="id_pti-include_taxamount" style="width: 177px;">Tax Amount</label>
								<input class="input_spinner input_spinner_set_width taxable_item" id="id_pti-taxamount_width" max="100" min="0" name="pti-taxamount_width" spinner_for="td_tax_amt" step="1" type="number" value="{{ item_res.item_table.tax.taxamount.width }}" disabled="disabled" style="display: none;">
							</div>
							<div>
								<label style="margin: 15px 0 8px;" class="hide">Item Sort Order</label>
							</div>
							<div class="form-group input_width_spinner div_tax_types hide">
								<ul id="id_pti-item_sort_order">
									{% for key, value in item_sort_order %}
										<label for="id_pti-item_sort_order_{{forloop.counter}}"><input checked="checked" id="id_pti-item_sort_order_{{forloop.counter}}" name="pti-item_sort_order_" type="radio" value="{{ key }} "> {{ value }}</label>
									{% endfor %}
								</ul>
							</div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="row_seperator" {% if item_res.field_alignment.separator.row_separator %} checked="checked" {% endif %} class="checkbox customized_checkbox" id="id_pti-include_row_separator" name="pti-include_row_separator" type="checkbox">
								<label for="id_pti-include_row_separator">Row Separator</label>
							</div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="column_seperator" {% if item_res.field_alignment.separator.column_separator %} checked="checked" {% endif %} class="checkbox customized_checkbox" id="id_pti-include_column_separator" name="pti-include_column_separator" type="checkbox">
								<label for="id_pti-include_column_separator">Column Separator</label>
							</div>
							<div class="checkbox form-group input_width_spinner">
								<input checkboxfor="row_shading" {% if item_res.field_alignment.separator.alternative_row_shading %} checked="checked" {% endif %} class="checkbox customized_checkbox" id="id_pti-include_alternate_row_shading" name="pti-include_alternate_row_shading" type="checkbox">
								<label for="id_pti-include_alternate_row_shading" style="width: 177px;">Alternative Row Shading</label>
							</div>
						</div>
					</div>
				</div>

				<div class="po_template_edit select_total hide">
					<div class="form-horizontal">
						<div class="col-sm-12 document-margin remove-padding">
							<label class="spinner-label" style="width: 100px;">Font Size</label>
							<input class="input_spinner" id="id_pts-font_size" max="100" min="0" name="pts-font_size" spinner_for="total_font_size" step="1" type="number" value="{{ summary_res.totals.font_size }}" style="display: none;">
							<span class="doc-margin-prefix"> px</span>
						</div>
						<div class="col-sm-12 enterprise_total_details remove-padding">
							<div class="checkbox form-group">
								<input checkboxfor="show_total_section" {% if summary_res.totals.print %} checked="checked" {% endif %} class="checkbox customized_checkbox" id="id_pts-include_total" name="pts-include_total" type="checkbox">
								<label for="id_pts-include_total">Show	Total Section</label>
							</div>
							<div class="checkbox form-group" style="margin-left: 15px;">
								<input checkboxfor="show_sub_total_section" {% if summary_res.totals.print_subtotal %} checked="checked" {% endif %} class="checkbox customized_checkbox" id="id_pts-include_subtotal" name="pts-include_subtotal" type="checkbox">
								<label for="id_pts-include_subtotal">Sub Total</label>
							</div>
							<div class="checkbox form-group" style="margin-left: 15px;">
								<input checkboxfor="show_quantity_total" {% if summary_res.totals.print_qtytotal %} checked="checked" {% endif %} class="checkbox customized_checkbox" id="id_pts-include_qty_total" name="pts-include_qty_total" type="checkbox" disabled="disabled">
								<label for="id_pts-include_qty_total">Quantity Total</label>
							</div>
							<div class="checkbox form-group">
								<input checkboxfor="show_total_in_words" {% if summary_res.totals.print_total_in_words %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_pts-include_total_in_words" name="pts-include_total_in_words" type="checkbox">
								<label for="id_pts-include_total_in_words" style="width:180px">Total Value in Words</label>
							</div>
							<hr style="margin: 10px -13px 20px; border-color: #ccc;">
							<div class="checkbox form-group">
								<input checkboxfor="hsn_summary_contianer" {% if summary_res.hsn_summary.print %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_pts-include_hsn_summary" name="pts-include_hsn_summary" type="checkbox">
								<label for="id_pts-include_hsn_summary">HSN Summary</label>
							</div>
							<div class="document-margin">
								<label class="spinner-label" style="width: 100px; font-size: 0.9em;">Font Size</label>
								<input class="input_spinner" id="id_pts-hsn_tax_font_size" max="100" min="0" name="pts-hsn_tax_font_size" spinner_for="hsn_tax_font_size" step="1" type="number" value="{{ summary_res.hsn_summary.font_size }}" style="display: none;">
								<span class="doc-margin-prefix"> px</span>
							</div>
							<hr style="margin: 12px -27px;border-color:#ccc;">
							<div class="checkbox form-group">
								<input checkboxfor="delivery_schedule_contianer" {% if summary_res.delivery_schedules_fields.delivery_schedules.print %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_pts-include_delivery_schedule_summary" name="pts-include_delivery_schedule" type="checkbox">
								<label for="id_pts-include_delivery_schedule_summary">Delivery Shedule</label>
							</div>
							<div class="document-margin">
								<label class="spinner-label" style="width: 100px; font-size: 0.9em;">Font Size</label>
								<input class="input_spinner" id="id_pts-delivery_schedule_font_size" max="100" min="0" name="pts-delivery_schedule_font_size" spinner_for="delivery_schedule_font_size" step="1" type="number" value="{{ summary_res.delivery_schedules_fields.delivery_schedules.font_size }}" style="display: none;">
								<span class="doc-margin-prefix"> px</span>
							</div>
								<div class="col-sm-12 enterprise_details" style="padding: 0;">
								<div style="display: inline-block;">
									  <label style="width :124px !important;">Column</label>
									  <label>Label</label>
									   <label style="width: 60px; text-align: center;">Width%</label>
								</div>
								<div class="checkbox form-group input_width_spinner">
									<input checkboxfor="td_s_no" {% if summary_res.delivery_schedules_fields.sno.print %} checked="checked" {% endif %} class="checkbox required_checkbox width_calc_checkbox" id="id_pts-include_s_no" name="pts-include_sno" type="checkbox">
									<label for="id_pts-include_s_no">S. No</label>
									<input class="form-control form-alternate-text" id="id_pts-s_no_label" maxlength="30" name="pts-s_no_label" textboxfor="td_s_no_text" type="text" value="{{ summary_res.delivery_schedules_fields.sno.label }}">
									<input class="input_spinner input_spinner_set_width_ds" id="id_pts-s_no_width" max="100" min="0" name="pts-s_no_width" spinner_for="td_s_no" step="1" type="number" value="{{ summary_res.delivery_schedules_fields.sno.width }}" disabled="disabled" style="display: none;">
								</div>
								<div class="input_width_spinner form-group" style="margin-left: 20px; margin-bottom: -6px;">
									<label style="padding-left: 6px;">Item Details</label>
									<input class="form-control form-alternate-text" id="id_pts-delivery_details_label" maxlength="30" name="pts-item_details_label" textboxfor="td_delivery_description_text" type="text" value="{{ summary_res.delivery_schedules_fields.description.label }}">
									<input class="input_spinner input_spinner_set_width_ds" disabled="disabled" id="id_pts-delivery_details_width" max="100" min="0" name="pts-item_details_width" spinner_for="td_delivery_description" step="1" type="number" value="{{ summary_res.delivery_schedules_fields.description.width }}" style="display: none;">
									<input id="id_pts-item_details_width_hidden_field" name="pts-item_details_width_hidden_field" type="hidden" value="35">
								</div>
								<div class=" form-group input_width_spinner"style="margin-left: 20px; margin-bottom: -6px;    margin-top: 6px">
									<label style="padding-left: 6px;">delivery qty</label>
									<input class="form-control form-alternate-text" id="id_pts-delivery_qty_label" maxlength="30" name="pts-delivery_qty_label" textboxfor="td_delivery_qty_text" type="text" value="{{ summary_res.delivery_schedules_fields.quantity.label }}">
									<input class="input_spinner input_spinner_set_width_ds" id="id_pts-delivery_qty_width" max="100" min="0" name="pts-delivery_qty_width" spinner_for="td_delivery_qty" step="1" type="number" value="{{ summary_res.delivery_schedules_fields.quantity.width }}" style="display: none;">
								</div>
								<div class=" form-group input_width_spinner"style="margin-left: 20px; margin-bottom: -6px;    margin-top: 6px">
									<label style="padding-left: 6px;">delivery due on</label>
									<input class="form-control form-alternate-text" id="id_pts-delivery_due_label" maxlength="30" name="pts-delivery_due_label" textboxfor="td_delivery_due_text" type="text" value="{{ summary_res.delivery_schedules_fields.delivery_date.label }}">
									<input class="input_spinner input_spinner_set_width_ds" id="id_pts-delivery_due_width" max="100" min="0" name="pts-delivery_due_width" spinner_for="td_delivery_due" step="1" type="number" value="{{ summary_res.delivery_schedules_fields.delivery_date.width }}" style="display: none;">
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="po_template_edit select_misc hide">
					<div class="form-horizontal">
						<span class="control-label" style="color: #209be1; text-shadow: 0 0 #000;">GREETING DETAILS</span>
						<div  class="form-group" style="padding: 0 15px; margin-top: 15px;">
							<label>subject</label>
							<div style="float: right; margin-top: -1px; background: #FFF; margin-right: -8px; color: #004195; cursor: pointer;" onclick="openSubjectOverlay();">
								<i class="fa fa-pencil super_edit_in_field" style="margin-top: -3px; margin-right: 15px;"></i>
							</div>
							<textarea class="form-control" cols="40" id="id_subject-notes" name="ptm-notes" rows="10" style="min-height: 75px; display: none;" textboxfor="greeting_subject" contenteditable="false" >
								{% autoescape off %}
								    {{misc_res.sub}}
								{% endautoescape %}
							</textarea>
						</div>

						<div  class="form-group" style="padding: 0 15px; margin-top: 15px;">
							<label>Mail Body</label>
							<div style="float: right; margin-top: -1px; background: #FFF; margin-right: -8px; color: #004195; cursor: pointer;" onclick="openMailBodyOverlay();">
								<i class="fa fa-pencil super_edit_in_field" style="margin-top: -3px; margin-right: 15px;"></i>
							</div>
							<textarea class="form-control" cols="40" id="id_mail-notes" name="ptm-notes" rows="10" style="min-height: 75px; display: none;" textboxfor="greeting_mailbody" contenteditable="false">
								{% autoescape off %}
								    {{misc_res.mail_body}}
								{% endautoescape %}
							</textarea>
						</div>
						<div class="checkbox form-group">
							<input checkboxfor="page_number" {% if misc_res.print_pageno %} checked="checked" {% endif %} class="checkbox required_checkbox" id="id_ptm-include_page_no_in_footer" name="ptm-include_page_no_in_footer" type="checkbox">
							<label for="id_ptm-include_page_no_in_footer">Page Number</label>
						</div>
						<div class="form-group input_width_spinner hide" style="margin-left: 24px;">
							<ul id="id_ptm-footer_page_no_alignment">
								<label for="id_ptm-footer_page_no_alignment_0"><input checked="checked" id="id_ptm-footer_page_no_alignment_0" name="ptm-footer_page_no_alignment" type="radio" value="1"> Left</label>
								<label for="id_ptm-footer_page_no_alignment_1"><input id="id_ptm-footer_page_no_alignment_1" name="ptm-footer_page_no_alignment" type="radio" value="2"> Right</label>
							</ul>
						</div>
						<div  class="form-group" style="padding: 0 15px; margin-top: 15px;">
							<label>Notes</label>
							<div style="float: right; margin-top: -1px; background: #FFF; margin-right: -8px; color: #004195; cursor: pointer;" data-toggle="modal" data-target="#notesEditableContainer">
								<i class="fa fa-pencil super_edit_in_field" style="margin-top: -3px; margin-right: 15px;"></i>
							</div>
							<textarea class="form-control" cols="40" id="id_ptm-notes" name="ptm-notes" rows="10" style="min-height: 75px; display: none;" textboxfor="notes" contenteditable="false">
								{% autoescape off %}
								    {{misc_res.notes}}
								{% endautoescape %}
							</textarea>
						</div>
						<label>Multi Page Settings <i class="fa fa-info-circle" style="color: #666;" aria-hidden="true" data-tooltip="tooltip" title="" data-original-title="Can be viewed in PDF Preview"></i></label>
						<div class="checkbox form-group">
							<input checkboxfor="print_summary_first_page" {% if misc_res.print_summary_first_page %} checked="checked" {% endif %} class="checkbox" id="id_ptm-include_first_page_summary" name="ptm-include_first_page_summary" type="checkbox">
							<label for="id_ptm-include_first_page_summary">Summary in First Page</label>
						</div>
						<hr style="margin: 10px -13px 20px; border-color: #ccc;">
						<div class="document-margin" style="margin-top: 15px;">
							<label class="spinner-label" style="width: 100px; padding-top: 5px;">Font Size</label>
							<input class="input_spinner" id="id_ptm-font_size" max="100" min="0" name="ptm-font_size" spinner_for="misc_font_size" step="1" type="number" value="{{ misc_res.foot_note.font_size }}" style="display: none;">
							<span class="doc-margin-prefix"> px</span>
						</div>
						<div class="form-group" style="padding: 0 15px;">
							<label>Foot Note</label>
							<textarea class="form-control" cols="40" id="id_ptm-foot_note" maxlength="140" name="ptm-foot_note" onkeypress="return validateStringOnKeyPress(this,event, &quot;alphaSpecialChar&quot;);" onblur="return validateStringOnBlur(this, event, &quot;alphaSpecialChar&quot;);" rows="10" style="min-height : 120px" textboxfor="footer_notes">{{ misc_res.foot_note.notes }}</textarea>
						</div>
						<div class="document-margin" style="margin-top: 15px;margin-bottom: 15px;">
						    <label class="spinner-label" style="color: #209be1; text-shadow: 0 0 #000;width: 100%; padding-top: 5px;margin-bottom: 10px;">Banner Images</label>
							<label class="spinner-label" style="width:35%;margin-top:-7px;">Header</label>
							<label style="float: left;margin-top: 2px;">Size</label>

							<span class="slidecontainer" style="float: left; margin-left: 11px;margin-top:3px;margin-bottom: 11px;width: 43%;">
								<input class="slider" id="header_banner_size" max="100" min="0" name="header_size" step="1" type="range" value="{{ misc_res.banner_header.height }}">
					        </span>
							<span id="header_banner_value" style="margin-left: 10px;">{{ misc_res.banner_header.height }}</span>

                            <div class="col-sm-4 banner-container input_width_spinner" role="button">
                                <div class="btn banner-image banner-header">
                                    <span class="uploader">
                                        <i class="fa fa-upload" aria-hidden="true"></i>
                                        <br>Left Image
                                    </span>
                                    <input id="id_pthb-0-config_id" name="pthb-0-config_id" type="hidden" value="2">
                                    <input id="id_pthb-0-section" name="pthb-0-section" type="hidden" value="Header">
                                    <input id="id_pthb-0-position" name="pthb-0-position" type="hidden" value="Left">
                                    <input class="banner-image-size" id="id_pthb-0-size" name="pthb-0-size" type="hidden" value="14">
                                    <input id="id_pthb-0-attachment_id" name="pthb-0-attachment_id" type="hidden">
                                    <input class="hidden-attachment" id="id_pthb-0-uploaded_banner_image" name="pthb-0-uploaded_banner_image" type="hidden">
                                    <input class="remove-attachment" id="id_pthb-0-is_removed" name="pthb-0-is_removed" type="hidden">
									<input id="id_pthb-0-banner_image" name="pthb-0-banner_image" type="hidden">
	                                {% for banner in template_header_banner %}
	                                    {% if banner.position == 'left' %}
                                            <img class="uploaded-banner-image hide" src="{{ banner.banner_image }}">
	                                    {% endif %}
	                                {% endfor %}
                                </div>
                                <i class="fa fa-close remove-banner-image hide" aria-hidden="true"></i>
	                            {% for banner in template_header_banner %}
                                    {% if banner.position == 'left' %}
                                        <input class="header-banner" id="image_uploader-left" value="{{ banner.banner_image }}" type="hidden">
                                    {% endif %}
                                {% endfor %}
								<br>

	                            <input class="input_spinner input_spinner_set_width_header_banner" id="id_ptm-header-left_banner_width" max="100" min="0" name="ptm-header-left_banner_width" spinner_for="misc_header-left_banner" step="1" type="number" value="" disabled="disabled" style="display: none;">
								<span class="doc-margin-prefix"> %</span>
                            </div>

                            <div class="col-sm-4 banner-container input_width_spinner" role="button">
                                <div class="btn banner-image banner-header">
                                    <span class="uploader">
                                        <i class="fa fa-upload" aria-hidden="true"></i>
                                        <br>Center Image
                                    </span>
                                    <input id="id_pthb-1-config_id" name="pthb-1-config_id" type="hidden" value="2">
                                    <input id="id_pthb-1-section" name="pthb-1-section" type="hidden" value="Header">
                                    <input id="id_pthb-1-position" name="pthb-1-position" type="hidden" value="Center">
                                    <input class="banner-image-size" id="id_pthb-1-size" name="pthb-1-size" type="hidden" value="14">
                                    <input id="id_pthb-1-attachment_id" name="pthb-1-attachment_id" type="hidden">
                                    <input class="hidden-attachment" id="id_pthb-1-uploaded_banner_image" name="pthb-1-uploaded_banner_image" type="hidden">
                                    <input class="remove-attachment" id="id_pthb-1-is_removed" name="pthb-1-is_removed" type="hidden">
									<input id="id_pthb-1-banner_image" name="pthb-1-banner_image" type="hidden">
	                                {% for banner in template_header_banner %}
	                                    {% if banner.position == 'center' %}
                                            <img class="uploaded-banner-image hide" src="{{ banner.banner_image }}">
	                                    {% endif %}
	                                {% endfor %}
                                </div>
                                <i class="fa fa-close remove-banner-image hide" aria-hidden="true"></i>
	                            {% for banner in template_header_banner %}
                                    {% if banner.position == 'center' %}
                                        <input class="header-banner" id="image_uploader-center" value="{{ banner.banner_image }}" type="hidden">
                                    {% endif %}
                                {% endfor %}
								<br>
	                            {% for res in misc_res.banner_header.attachment %}
									{% if res.position == "center" %}
	                                    <input class="input_spinner input_spinner_set_width_header_banner" id="id_ptm-header-center_banner_width" max="100" min="0" name="ptm-header-center_banner_width" spinner_for="misc_header-center_banner" step="1" type="number" value="{{res.width}}" >
									{% endif %}
								{% endfor %}
								<span class="doc-margin-prefix"> %</span>
                            </div>

                            <div class="col-sm-4 banner-container input_width_spinner" role="button">
                                <div class="btn banner-image banner-header">
                                    <span class="uploader">
                                        <i class="fa fa-upload" aria-hidden="true"></i>
                                        <br>Right Image
                                    </span>
                                    <input id="id_pthb-2-config_id" name="pthb-2-config_id" type="hidden" value="2">
                                    <input id="id_pthb-2-section" name="pthb-2-section" type="hidden" value="Header">
                                    <input id="id_pthb-2-position" name="pthb-2-position" type="hidden" value="Right">
                                    <input class="banner-image-size" id="id_pthb-2-size" name="pthb-2-size" type="hidden" value="14">
                                    <input id="id_pthb-2-attachment_id" name="pthb-2-attachment_id" type="hidden">
                                    <input class="hidden-attachment" id="id_pthb-2-uploaded_banner_image" name="pthb-2-uploaded_banner_image" type="hidden">
                                    <input class="remove-attachment" id="id_pthb-2-is_removed" name="pthb-2-is_removed" type="hidden">
									<input id="id_pthb-2-banner_image" name="pthb-2-banner_image" type="hidden">
                                    {% for banner in template_header_banner %}
	                                    {% if banner.position == 'right' %}
                                            <img class="uploaded-banner-image hide" src="{{ banner.banner_image }}">
	                                    {% endif %}
	                                {% endfor %}
                                </div>
                                <i class="fa fa-close remove-banner-image hide" aria-hidden="true"></i>
	                            {% for banner in template_header_banner %}
                                    {% if banner.position == 'right' %}
                                        <input class="header-banner" id="image_uploader-right" value="{{ banner.banner_image }}" type="hidden">
                                    {% endif %}
                                {% endfor %}
								<br>

	                            {% for res in misc_res.banner_header.attachment %}
									{% if res.position == "right" %}
	                                    <input class="input_spinner input_spinner_set_width_header_banner" id="id_ptm-header-right_banner_width" max="100" min="0" name="ptm-header-right_banner_width" spinner_for="misc_header-right_banner" step="1" type="number" value="{{res.width}}" >
									{% endif %}
								{% endfor %}
								<span class="doc-margin-prefix"> %</span>
                            </div>
							<div class="col-sm-12"><hr style="margin: 12px -27px; border-color: #ccc;"></div>
							<div style="margin-top: 30px;">
								<label class="spinner-label" style="width:35%; margin-top:-9px;">Footer</label>
								<label style="float: left;margin-top: 2px;">Size</label>

								<span class="slidecontainer" style="float: left; margin-left: 11px;margin-top: 3px;margin-bottom:11px;width: 43%;">
									<input class="slider" id="footer_banner_size" max="100" min="0" name="footer_size" step="1" type="range" value="{{ misc_res.banner_footer.height }}">
								</span>
								<span id="footer_banner_value" style="margin-left: 10px;">{{ misc_res.banner_footer.height }}</span>

                                <div class="col-sm-4 banner-container input_width_spinner" role="button">
                                    <div class="btn banner-image banner-footer">
                                        <span class="uploader">
                                            <i class="fa fa-upload" aria-hidden="true"></i>
                                            <br>Left Image
                                        </span>
                                        <input id="id_ptfb-0-config_id" name="ptfb-0-config_id" type="hidden" value="2">
                                        <input id="id_ptfb-0-section" name="ptfb-0-section" type="hidden" value="Footer">
                                        <input id="id_ptfb-0-position" name="ptfb-0-position" type="hidden" value="Left">
                                        <input class="banner-image-size" id="id_ptfb-0-size" name="ptfb-0-size" type="hidden" value="14">
                                        <input id="id_ptfb-0-attachment_id" name="ptfb-0-attachment_id" type="hidden">
                                        <input class="hidden-attachment" id="id_ptfb-0-uploaded_banner_image" name="ptfb-0-uploaded_banner_image" type="hidden">
                                        <input class="remove-attachment" id="id_ptfb-0-is_removed" name="ptfb-0-is_removed" type="hidden">
										<input id="id_ptfb-0-banner_image" name="ptfb-0-banner_image" type="hidden">
                                        {% for banner in template_footer_banner %}
		                                    {% if banner.position == 'left' %}
	                                            <img class="uploaded-banner-image hide" src="{{ banner.banner_image }}">
		                                    {% endif %}
		                                {% endfor %}
                                    </div>
                                    <i class="fa fa-close remove-banner-image hide" aria-hidden="true"></i>
                                    {% for banner in template_footer_banner %}
                                        {% if banner.position == 'left' %}
                                            <input class="header-banner" id="footer_uploader-left" value="{{ banner.banner_image }}" type="hidden">
                                        {% endif %}
                                    {% endfor %}
		                                <input class="input_spinner input_spinner_set_width_footer_banner" id="id_ptm-footer-left_banner_width" max="100" min="0" name="ptm-footer-left_banner_width" spinner_for="misc_footer-left_banner" step="1" type="number" value="" disabled="disabled" style="display: none;" >
									<span class="doc-margin-prefix"> %</span>
                                </div>

                                <div class="col-sm-4 banner-container input_width_spinner" role="button">
                                    <div class="btn banner-image banner-footer">
                                        <span class="uploader">
                                            <i class="fa fa-upload" aria-hidden="true"></i>
                                            <br>Center Image
                                        </span>
                                        <input id="id_ptfb-1-config_id" name="ptfb-1-config_id" type="hidden" value="2">
                                        <input id="id_ptfb-1-section" name="ptfb-1-section" type="hidden" value="Footer">
                                        <input id="id_ptfb-1-position" name="ptfb-1-position" type="hidden" value="Center">
                                        <input class="banner-image-size" id="id_ptfb-1-size" name="ptfb-1-size" type="hidden" value="14">
                                        <input id="id_ptfb-1-attachment_id" name="ptfb-1-attachment_id" type="hidden">
                                        <input class="hidden-attachment" id="id_ptfb-1-uploaded_banner_image" name="ptfb-1-uploaded_banner_image" type="hidden">
                                        <input class="remove-attachment" id="id_ptfb-1-is_removed" name="ptfb-1-is_removed" type="hidden">
										<input id="id_ptfb-1-banner_image" name="ptfb-1-banner_image" type="hidden">
                                        {% for banner in template_footer_banner %}
		                                    {% if banner.position == 'center' %}
	                                            <img class="uploaded-banner-image hide" src="{{ banner.banner_image }}">
		                                    {% endif %}
		                                {% endfor %}
                                    </div>
                                    <i class="fa fa-close remove-banner-image hide" aria-hidden="true"></i>
									{% for banner in template_footer_banner %}
                                        {% if banner.position == 'center' %}
                                            <input class="header-banner" id="footer_uploader-center" value="{{ banner.banner_image }}" type="hidden">
                                        {% endif %}
                                    {% endfor %}
	                                {% for res in misc_res.banner_footer.attachment %}
										{% if res.position == "center" %}
	                                        <input class="input_spinner input_spinner_set_width_footer_banner" id="id_ptm-footer-center_banner_width" max="100" min="0" name="ptm-footer-center_banner_width" spinner_for="misc_footer-center_banner" step="1" type="number" value="{{res.width}}" >
										{% endif %}
									{% endfor %}
									<span class="doc-margin-prefix"> %</span>
                                </div>

                                <div class="col-sm-4 banner-container input_width_spinner" role="button">
                                    <div class="btn banner-image banner-footer">
                                        <span class="uploader">
                                            <i class="fa fa-upload" aria-hidden="true"></i>
                                            <br>Right Image
                                        </span>
                                        <input id="id_ptfb-2-config_id" name="ptfb-2-config_id" type="hidden" value="2">
                                        <input id="id_ptfb-2-section" name="ptfb-2-section" type="hidden" value="Footer">
                                        <input id="id_ptfb-2-position" name="ptfb-2-position" type="hidden" value="Right">
                                        <input class="banner-image-size" id="id_ptfb-2-size" name="ptfb-2-size" type="hidden" value="14">
                                        <input id="id_ptfb-2-attachment_id" name="ptfb-2-attachment_id" type="hidden">
                                        <input class="hidden-attachment" id="id_ptfb-2-uploaded_banner_image" name="ptfb-2-uploaded_banner_image" type="hidden">
                                        <input class="remove-attachment" id="id_ptfb-2-is_removed" name="ptfb-2-is_removed" type="hidden">
										<input id="id_ptfb-2-banner_image" name="ptfb-2-banner_image" type="hidden">
                                        {% for banner in template_footer_banner %}
		                                    {% if banner.position == 'right' %}
	                                            <img class="uploaded-banner-image hide" src="{{ banner.banner_image }}">
		                                    {% endif %}
		                                {% endfor %}
                                    </div>
                                    <i class="fa fa-close remove-banner-image hide" aria-hidden="true"></i>
									{% for banner in template_footer_banner %}
                                        {% if banner.position == 'right' %}
                                            <input class="header-banner" id="footer_uploader-right" value="{{ banner.banner_image }}" type="hidden">
                                        {% endif %}
                                    {% endfor %}
	                                {% for res in misc_res.banner_footer.attachment %}
										{% if res.position == "right" %}
	                                        <input class="input_spinner input_spinner_set_width_footer_banner" id="id_ptm-footer-right_banner_width" max="100" min="0" name="ptm-footer-right_banner_width" spinner_for="misc_footer-right_banner" step="1" type="number" value="{{res.width}}" >
										{% endif %}
									{% endfor %}
									<span class="doc-margin-prefix"> %</span>
                                </div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="purchase_template_default hide" style="float: right; margin-right: -3px; margin-top: -56px;">
				{% include "admin/print_template/po/preview/po_template_compact_preview.html" %}
			</div>
			<div class="purchase_template_1 hide" style="float: right; margin-right: -3px; margin-top: -56px;">
				{% include "admin/print_template/po/preview/po_template_simple_preview.html" %}
			</div>
			<div class="po_container_save hide" style="float: right; margin-right: -3px; margin-top: -56px;">
				{% include "admin/print_template/po/document/po_template_compact.html" %}
			</div>
			<div class="po_container_simple_save hide" style="float: right; margin-right: -3px; margin-top: -56px;">
				{% include "admin/print_template/po/document/po_template_simple.html" %}
			</div>
        </div>
		<div id="template_chooser" class="modal fade" role="dialog">
			<div class="modal-dialog" style="width: 80%; max-width: 1100px;">
				<div class="modal-content" style="min-height: 650px;">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal">&times;</button>
						<h4 class="modal-title">Choose Template</h4>
					</div>
					<div class="modal-body">
	                <div class="select_template row">
						<div class="col-sm-6 select_template_container text-center" id="id_template_2">
							<span style="font-size: 14px;">Simple</span>
							<a role="button" class="template_type" id="template_2" >
								<img src="/site_media/images/purchase_template_simple.png" style="width: 100%; border: solid 2px #333; border-radius: 4px;height: 506px !important;" />
							</a>
							<div class="selection_template">
								<i class="fa fa-check fa-6" aria-hidden="true"></i>
							</div>
						</div>
						<div class="col-sm-6 select_template_container text-center" id="id_template_1">
							<span style="font-size: 14px;">Compact</span>
							<a role="button" class="template_type" id="template_1" >
								<img src="/site_media/images/purchase_template_compact.png" style="width: 100%;  border: solid 2px #333; border-radius: 4px;height: 506px !important;" />
							</a>
							<div class="selection_template">
								<i class="fa fa-check fa-6" aria-hidden="true"></i>
							</div>
						</div>
					</div>
	            </div>

				</div>
			</div>
		</div>

		<div id="preview_modal" class="modal fade" role="dialog" tabindex="-1">
			<div class="modal-dialog modal-lg">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal">&times;</button>
						<h4 class="modal-title">PDF Preview</h4>
					</div>
					<div class="modal-body">
						<div id="preview_document_container" class="full_txt_width">
							<div class="contant-container-nav" style="padding-top: 0;">
								<ul class="nav nav-tabs list-inline" style="margin-left: 0;">
									<li class="active"><a data-toggle="tab" href="#tab1" id="single-page-invoice">Single Page PO</a></li>
									<li><a data-toggle="tab" href="#tab2">Multi-Page PO</a></li>
								</ul>
								<span class="text-warning" style="position: absolute; color: #f0ad4e; right: 15px; top: 15px; width: 280px; text-align: right;">
									* Data populated below are for representation & does not depict any actual Invoice
								</span>
							</div>
							<div class="tab-content" style="border: 1px solid #ccc; padding: 10px; margin-top: -16px;">
								<div id="tab1" class="tab-pane fade in active">
								</div>
								<div id="tab2" class="tab-pane">
								</div>
							</div>
						</div>
					</div>
					<div class="modal-footer">
		                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
		            </div>
				</div>
			</div>
		</div>
		<div id="notesEditableContainer" class="modal fade" role="dialog">
			<div class="modal-dialog modal-lg" style="width: 80%;">
			    <div class="modal-content">
		            <div class="modal-header">
		                <button type="button" class="close" data-dismiss="modal">&times;</button>
		                <h4 class="modal-title">Notes</h4>
		            </div>
		            <div class="modal-body">
		                <div id="notes_editor">
		                    {{ misc_res.notes }}
						</div>
		            </div>
		            <div class="modal-footer">
		                <button  id="add_purchase_notes" type="button" class="btn btn-save" onclick="addPurchaseNote();">Add</button>
		                <button  id="cancel_purchase_notes" data-dismiss="modal" type="button" class="btn btn-cancel">Cancel</button>
		            </div>
		        </div>
		    </div>
		</div>
		<div id="subjectEditableContainer" class="modal fade" role="dialog">
			<div class="modal-dialog modal-lg" style="width: 80%;">
			    <div class="modal-content">
		            <div class="modal-header">
		                <button type="button" class="close" data-dismiss="modal">&times;</button>
		                <h4 class="modal-title">Greeting</h4>
		            </div>
		            <div class="modal-body">
		                <div id="subject_editor">
		                    {{ misc_res.sub }}
						</div>
		            </div>
		            <div class="modal-footer">
		                <button  id="add_po_notes" type="button" class="btn btn-save" onclick="addSubjectNote();">Add</button>
		                <button  id="cancel_po_notes" data-dismiss="modal" type="button" class="btn btn-cancel">Cancel</button>
		            </div>
		        </div>
		    </div>
		</div>
		<div id="mailbodyEditableContainer" class="modal fade" role="dialog">
			<div class="modal-dialog modal-lg" style="width: 80%;">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal">&times;</button>
						<h4 class="modal-title">mailbody</h4>
					</div>
					<div class="modal-body">
						<div id="mailbody_editor">
							{{ misc_res.mail_body }}
						</div>
					</div>
					<div class="modal-footer">
						<button  id="add_mail_notes" type="button" class="btn btn-save" onclick="addMailBodyNote();">Add</button>
						<button  id="cancel_mail_notes" data-dismiss="modal" type="button" class="btn btn-cancel">Cancel</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<div id="change_log_modal" class="modal" tabindex="-1" role="dialog" aria-labelledby="" aria-hidden="true">
   <div class="modal-dialog">
	    <div class="modal-content">
		    <div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Activity Log</h4>
	      	</div>
      		<div class="modal-body remove-padding">
		        <div id='loadingmessage_changelog_listing_ie' style="text-align:center">
						<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' width="100">
						<br /> Please wait...
			    </div>
        		<ul class="history-log-container timeline">

        		</ul>
		         <ul class="show-more-log" style="list-style: none; text-align: right; margin-right: 15px; cursor: pointer;">
			        <li onclick="shoMoreLogs(20,20)">Show More</li>
		        </ul>
      		</div>
      		<div class="modal-footer">
      			<a href="#" class="btn btn-cancel" data-dismiss="modal">Close</a>
      		</div>
    	</div>
  	</div>
</div>
<script type="text/javascript">
$(window).load(function(){
	var urlParams = new URLSearchParams(window.location.search);
	if(urlParams.has('editRequired')) {
		poAutoSaveWithTour("po");
	}
	else if($("#is_print_template_available").val() == 0) {
		poAutoSaveWithTour("local");
	}
})
$(document).ready(function(){
	var urlParams = new URLSearchParams(window.location.search);
	if(urlParams.has('editRequired')) {
		$("#loading").removeClass("hide");
	}
	$('.header-banner').awesomeCropper(
        { debug: false }
	);
	applyCroppedImage();
	$("input[type='number']").inputSpinner();
	$('.spinner-textbox').on("cut copy paste",function(e) {
      e.preventDefault();
   });

	tabSelection();
	setCurrentDateTime();
	constructDateFormatField();
	logoSliderChangeEvent();
	headerSliderChangeEvent();
	footerSliderChangeEvent();
	inputSpinnerChangeEvent();
	dropDownChangeEvent();
	textboxBlurEvent();
	checkboxChangeEvent();
	imageClickEvent();
	removeBannerImageInit();
	leftContainerScrollInit();
	if($("#saved_purchase_template").val() != ""){
		swal("","P Configuration has been modified successfully.","success");
	}
	$(".form-control.input_spinner").attr({maxLength: "3", onfocus: "setNumberRangeOnFocus(this,3,0)"});

	if($("#last-modified-user").text().trim() != "") {
		$(".last-modified-user-container").removeClass("hide");
	}
	var editorElement = CKEDITOR.document.getById( 'id_ptm-notes' );
	editorElement.setAttribute( 'contenteditable', 'false' );
	CKEDITOR.inline( 'id_ptm-notes' );
	CKEDITOR.replace( 'notes_editor' );
	CKEDITOR.config.height = 300;
	var editorElement1 = CKEDITOR.document.getById( 'notes_editor' );
	var htmlText = $("#id_ptm-notes").text();
	editorElement1.setHtml(
		htmlText
	);
	var editorElement = CKEDITOR.document.getById( 'id_subject-notes' );
	editorElement.setAttribute( 'contenteditable', 'false' );
	CKEDITOR.inline( 'id_subject-notes' );
	CKEDITOR.replace( 'subject_editor' );
	CKEDITOR.config.height = 300;
	var editorElement1 = CKEDITOR.document.getById( 'subject_editor' );
	var htmlText = $("#id_subject-notes").text();
	editorElement1.setHtml(
		htmlText
	);
	var editorElement = CKEDITOR.document.getById( 'id_mail-notes' );
	editorElement.setAttribute( 'contenteditable', 'false' );
	CKEDITOR.inline( 'id_mail-notes' );
	CKEDITOR.replace( 'mailbody_editor' );
	CKEDITOR.config.height = 300;
	var editorElement1 = CKEDITOR.document.getById( 'mailbody_editor' );
	var htmlText = $("#id_mail-notes").text();
	editorElement1.setHtml(
		htmlText
	);
	changeLogActivityInit();
	scanCode();
});

function shoMoreLogs(offset, limit){
	if($("#change_log_modal").hasClass("change_log")) {
		$.ajax({
	        url: '/erp/admin/purchase_template/getloglist/',
	        type: "POST",
	        dataType: "json",
	        data: {offset: offset, limit: limit},
	        success: function (data) {
	        if (data['response_code'] != 400){
	        console.log(data);
	            x = data['data'];
				var row = "";
				var i2 = offset;
				for(var i = 0; i < x.length; i++) {
				    var obj = x[i].log;
				    var row = `<li class="history-log-part" id="history-part-${i2}" onclick="loadHistoryContent('${data['data'][0].eid}', '${obj.modified_at}', ${i2}, '${obj.preference}')">
			                        <span class="history-log-username">${obj.username[0]+" "+obj.username[1]}</span>
			                        <div class="history-log-content" style="display: none;"></div>
			                        <span class="history-log-date">${moment(obj.modified_at).format("MMM DD, YYYY hh:mm:ss a")}</span>
			                    </li>`;

	                $(".history-log-container").append(row);
	                var displayedCount = $(".history-log-container").find("li").length;
	                i2++;
	                if(x.length < 20){
	                    $(".show-more-log").addClass("hide");
	                }
	                else {
	                    $(".show-more-log").find("li").attr("onclick", "shoMoreLogs("+displayedCount+", 20)");
                    }
				}
			}
			else {
				$(".show-more-log").addClass("hide");
			}
	        },
	        error: function ($xhr,textStatus,errorThrown) {
		        console.log("ERROR : ", $xhr);
		        if ($xhr.responseText.indexOf("Your session has expired") != -1){
		            location.reload();
		        }
	        }
		});
	}
}

function changeLogActivityInit(){
	$("#id-change-log").removeClass("hide");
	$("#btn_change_log").click(function(){
		$("#change_log_modal").addClass("change_log").removeClass("change_preference_log");
		$("#change_log_modal").modal("show");
		$("#loadingmessage_changelog_listing_ie").show();
		$(".history-log-container").html("");
		$.ajax({
	        url: '/erp/admin/purchase_template/getloglist/',
	        type: "POST",
	        dataType: "json",
	        data: {offset: 0, limit: 20},
	        success: function (data) {
	        if (data['response_code'] != 400){
	            x = data['data'];
				var row = "";
				for(var i = 0; i < x.length; i++) {
				    var obj = x[i].log;
				    var row = `<li class="history-log-part" id="history-part-${i}" onclick="loadHistoryContent('${data['data'][0].eid}', '${obj.modified_at}', ${i}, '${obj.preference}')">
			                        <span class="history-log-username">${obj.username[0]+" "+obj.username[1]}</span>
			                        <div class="history-log-content" style="display: none;"></div>
			                        <span class="history-log-date">${moment(obj.modified_at).format("MMM DD, YYYY hh:mm:ss a")}</span>
			                    </li>`;
			        $("#loadingmessage_changelog_listing_ie").hide();
	                $(".history-log-container").append(row);
	                var displayedCount = $(".history-log-container").find("li").length;
				}
					if(x.length < 20){
	                    $(".show-more-log").addClass("hide");
	                }
	                else {
	                    $(".show-more-log").removeClass("hide");
	                    $(".show-more-log").find("li").attr("onclick", "shoMoreLogs("+displayedCount+", 20)");
                    }
			}
			else {
			    var row = `<span style="width: 100%; padding: 15px; text-align:center; display: block;">No Activity Log has been captured.</span>`;
				$("#loadingmessage_changelog_listing_ie").hide();
	            $("#change_log_modal .modal-body").html(row);
			}
	        },
	        error: function ($xhr,textStatus,errorThrown) {
		        console.log("ERROR : ", $xhr);
		        if ($xhr.responseText.indexOf("Your session has expired") != -1){
		            location.reload();
		        }
	        }
		});
	});

	$("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
}

function loadHistoryContent(voucher_id, modified_at, i, preference) {
	var currentLogContent = $("#history-part-"+i).find('.history-log-content');
	if(currentLogContent.text() == "") {
		currentLogContent.html(`<img src="/site_media/images/loading_spinner.gif" style="width: 30px;"> Loading, Please wait...`).show();
		currentLogContent.closest(".history-log-part").addClass("active");
		setTimeout(function(){
			$.ajax({
		        url: '/erp/admin/purchase_template/getlogdata/',
		        type: "POST",
		        dataType: "json",
		        data: {"voucher_id":voucher_id, "modified_at": modified_at, "preference": preference},
		        success: function (data) {
		        if (data['response_code'] != 400){
		        console.log(data);
		        var row = '';
			        $.each( data['data'], function( key, value ) {
					  row += `<li>` + value + `</li>`;
					});
		        currentLogContent.html(`<ul>` + row + `</ul>`);
				currentLogContent.hide();
				currentLogContent.slideDown();
				}else{
				currentLogContent.html(`log data not available`);
				currentLogContent.hide();
				currentLogContent.slideDown();

				}
		        },
		        error: function () {
		        console.log('Changelog listing failed to load');

		        }
			});

		},100);
	}
	else {
		if(currentLogContent.find("img").length > 0) {
			return;
		}
		if(currentLogContent.is(":visible")) {
			currentLogContent.slideUp('fast', function() {
			   currentLogContent.closest(".history-log-part").removeClass("active");
			});

		}
		else {
			currentLogContent.slideDown();
			currentLogContent.closest(".history-log-part").addClass("active");
		}
	}
	$("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
}

function loadPreferenceHistoryContent(voucher_id, modified_at, i) {
	var currentLogContent = $("#history-part-"+i).find('.history-log-content');
	if(currentLogContent.text() == "") {
		currentLogContent.html(`<img src="/site_media/images/loading_spinner.gif" style="width: 30px;"> Loading, Please wait...`).show();
		currentLogContent.closest(".history-log-part").addClass("active");
		setTimeout(function(){
			$.ajax({
		        url: '/erp/admin/enterprise/getpreferencelogdata/',
		        type: "POST",
		        dataType: "json",
		        data: {"voucher_id":voucher_id, "modified_at": modified_at},
		        success: function (data) {
		        if (data['response_code'] != 400){
		        var row = '';
			        $.each( data['data'], function( key, value ) {
					  row += `<li>` + value + `</li>`;
					});
		        currentLogContent.html(`<ul>` + row + `</ul>`);
				currentLogContent.hide();
				currentLogContent.slideDown();
				}else{
				currentLogContent.html(`log data not available`);
				currentLogContent.hide();
				currentLogContent.slideDown();

				}
		        },
		        error: function () {
		        console.log('Changelog listing failed to load');

		        }
			});

		},100);
	}
	else {
		if(currentLogContent.find("img").length > 0) {
			return;
		}
		if(currentLogContent.is(":visible")) {
			currentLogContent.slideUp('fast', function() {
			   currentLogContent.closest(".history-log-part").removeClass("active");
			});

		}
		else {
			currentLogContent.slideDown();
			currentLogContent.closest(".history-log-part").addClass("active");
		}
	}
	$("#change_log_modal .modal-body").css({maxHeight: Number($(window).height() - 210)+"px"})
}

function updateRegistrationDetails() {
	var registrationDetailsObj = [];
	$(".registration_details").find("input[type='checkbox']").each(function(){
		var current = $(this);
		if(current.is(":checked")) {
			var item = {};
			item ["label_id"] = Number(current.attr("data-field-id"));
            item ["label"] = current.next("label").text();
            registrationDetailsObj.push(item);
		}
	});
	registrationDetailsObj = JSON.stringify(registrationDetailsObj);
	$("#id_pth-included_reg_items").val(registrationDetailsObj)
}

$(window).load(function(){
	$(".po_template_editor").find("input[type='number']").trigger("change");
	$(".po_template_editor").find("select").trigger("change");
	$(".po_template_editor").find("input[type='text'], textarea").trigger("blur");
	$(".po_template_editor").find("input[type='checkbox']").trigger("change");
	$("input[name='pti-tax_type']").trigger("change");
	$("input[name='pti-tax_payable_on_reverse_charge']").trigger("change");
	var output = $("#logo_size_value").text();
	$(".pdf_company_logo").css({height: output});
	$(".purchase_template_default, .purchase_template_1, .purchase_template_2").addClass("hide");
	$(".select_template_container").removeClass("selected_template")
	if($("#id_ptc-template_id").val() == "1") {
		$(".purchase_template_default").removeClass("hide");
		$("#id_template_1").addClass("selected_template");
	}
	else if($("#id_ptc-template_id").val() == "2"){
		$(".purchase_template_1").removeClass("hide");
		$("#id_template_2").addClass("selected_template");
	}
	updateBannerImagesOnLoad();
});

function tabSelection() {
		$(".nav_purchase_template li").click(function(){
		$(".nav_purchase_template li").removeClass("active");
		var selectedLi = $(this).data("field");
		var selectedTab = $(this).data("tab");
		$(this).addClass("active");
		$(".po_template_edit").addClass("hide");
		$("."+selectedLi).removeClass("hide");
		$("#id_ptg-tab_retain").val(selectedTab)
	});
}

function setCurrentDateTime() {
	var dt = new Date();
	var datetime = moment(dt).format("YYYY-MM-DD hh:mm:ss")
	var date = moment(dt).format("YYYY-MM-DD")
		$(".issue_date_time").text(datetime);
		$(".pdf_po_date").text(date);
		$(".pdf_estimate_date").text(date);
}

function logoSliderChangeEvent() {
	var slider = document.getElementById("id_pth-logo_size");
	var output = document.getElementById("logo_size_value");
	output.innerHTML = slider.value;
	slider.oninput = function() {
	  output.innerHTML = this.value;
	  $(".pdf_company_logo").css({height: this.value});
	}
}

function inputSpinnerChangeEvent() {
	$("input[type='number']").on("change", function (event) {
		var spinner_for = $(this).attr("spinner_for");
		var spinnerValue = $(this).val();
		if(spinnerValue == "") spinnerValue = 0;
		if(spinner_for == "pdf_company_name") {
		    $(".pdf_company_name").css({fontSize: spinnerValue+"px"});
		}
		else if(spinner_for == "pdf_company_details") {
			$(".pdf_company_address_container, .pdf_company_details_container").css({fontSize: spinnerValue+"px"});
			$(".pdf_issue_date_time").css({fontSize: spinnerValue+"px"});
		}
		else if(spinner_for == "pdf_purchase_number") {
			$(".pdf_purchase_prefix, .pdf_purchase_no").css({fontSize: spinnerValue+"px"});
		}
		else if(spinner_for == "pdf_form_name") {
			$(".pdf_form_name_text_size").css({fontSize: spinnerValue+"px"});
			if(spinnerValue == 0) {
				$(".po_template_view .pdf_form_name_text_size").addClass("hide");
			}
			else {
				$(".po_template_view .pdf_form_name_text_size").removeClass("hide");
			}
		}
		else if(spinner_for == "page_margin_top") {
			$(".po_pdf_editor").css({paddingTop: spinnerValue+"mm"});
		}
		else if(spinner_for == "page_margin_bottom") {
			$(".po_pdf_editor").css({paddingBottom: spinnerValue+"mm"});
		}
		else if(spinner_for == "page_margin_left") {
			$(".po_pdf_editor").css({paddingLeft: spinnerValue+"mm"});
		}
		else if(spinner_for == "page_margin_right") {
			$(".po_pdf_editor").css({paddingRight: spinnerValue+"mm"});
		}
		else if(spinner_for == "table_font_size") {
			$(".item_table thead th, .item_table tbody td, .item_table athead ath, .item_table atbody atd").css("font-size", spinnerValue+"px");
		}
		else if(spinner_for == "total_font_size") {
			$(".item_table tfoot td, .item_table atfoot atd, .notes_section, .signatory_content").css({fontSize: spinnerValue+"px", lineHeight: spinnerValue+"px"});
		}
		else if(spinner_for == "hsn_tax_font_size") {
			$(".hsn_summary, .hsn_summary_title").css("font-size", spinnerValue+"px");
		}
		else if(spinner_for == "delivery_schedule_font_size") {
			$(".delivery_schedule, .delivery_schedule_title").css("font-size", spinnerValue+"px");
		}
		else if(spinner_for == "misc_font_size") {
			$(".footer_notes").css({fontSize: spinnerValue+"px"});
		}
		else {
			calculateWidthforItemTable();
			calculateWidthforDSTable();
			calculateWidthforBanner();
		}
	});
}

function dropDownChangeEvent(){
	$("select").change(function(){
		var selectField = $(this).attr("id");
		var selectValue = $(this).val();
		if(selectField == "id_ptg-base_font") {
			$(".po_pdf_editor").css({fontFamily: selectValue });
		}
		else if(selectField == "id_pth-name_font") {
			$(".po_pdf_editor .pdf_company_name").css({fontFamily: selectValue });
		}
	});
}

function textboxBlurEvent(){
	$("input[type='text'], textarea").keyup(function(){
		var checkbox_for = $(this).attr("textboxfor");
		$("."+checkbox_for).text($(this).val());
		if(checkbox_for == "bill_to_text" || checkbox_for == "ship_to_text") {
			if($(this).val().trim() == "") {
				$("."+checkbox_for).addClass("hide");
			}
			else {
				$("."+checkbox_for).removeClass("hide");
			}
		}
	});
	$("input[type='text'], textarea").blur(function(){
		var checkbox_for = $(this).attr("textboxfor");
		$("."+checkbox_for).html($(this).val());
		if(checkbox_for == "bill_to_text" || checkbox_for == "ship_to_text") {
			if($(this).val().trim() == "") {
				$("."+checkbox_for).addClass("hide");
			}
			else {
				$("."+checkbox_for).removeClass("hide");
			}
		}
	});
}

function checkboxChangeEvent() {
        $('#id_pti-include_make').prop('checked', true);
	    $('#id_pti-include_partno').prop('checked', true);
	$(".required_checkbox").change(function(){
		var checkbox_for = $(this).attr("checkboxfor");
		if($(this).is(":checked")) {
			$("."+checkbox_for).removeClass("hide")
		}
		else {
			$("."+checkbox_for).addClass("hide")
		}
		if(checkbox_for == 'pdf_item_make' || checkbox_for == 'pdf_item_part') {
			var isMakeChecked = $("#id_pti-include_make").is(":checked");
			var isPartChecked = $("#id_pti-include_partno").is(":checked");
			if( isMakeChecked && isPartChecked) {
				$(".pdf_item_make_start").html("[");
				$(".pdf_item_part_start").html("");
				$(".pdf_item_make_value").html("NEW - ");
				$(".pdf_item_part_value").html("PN534]<br>");
			}
			else if( isMakeChecked || isPartChecked) {
				if(isMakeChecked) {
					$(".pdf_item_make_start").html("[");
					$(".pdf_item_part_start").html("");
					$(".pdf_item_make_value").html("NEW]<br>");
				}
				else {
					$(".pdf_item_part_start").html("[");
					$(".pdf_item_make_start").html("");
					$(".pdf_item_part_value").html("PN534]<br>");
				}
			}
			else {
				$(".pdf_item_make_value").html("");
				$(".pdf_item_part_value").html("");
			}
		}
		if(checkbox_for == 'pdf_bill_to_enterprise_code' || checkbox_for == 'pdf_bill_to_enterprise_name') {
			var isPartyCodeChecked = $("#id_pth-include_partycode").is(":checked");
			var isPartyNameChecked = $("#id_pth-include_partyname").is(":checked");
			if( isPartyCodeChecked && isPartyNameChecked) {
				$(".pdf_bill_to_enterprise_detail").html("ABC SOFTWARE SOLUTION (ABC001)");
				$(".pdf_ship_to_enterprise_detail").html("ABC ENERGY PVT LTD (ABC123)");
			}
			else if( isPartyCodeChecked || isPartyNameChecked) {
				if(isPartyCodeChecked) {
					$(".pdf_bill_to_enterprise_detail").html("ABC001");
					$(".pdf_ship_to_enterprise_detail").html("ABC123");
				}
				else {
					$(".pdf_bill_to_enterprise_detail").html("ABC SOFTWARE SOLUTION");
					$(".pdf_ship_to_enterprise_detail").html("ABC ENERGY PVT LTD");
				}
			}
			else {
				$(".pdf_bill_to_enterprise_detail").html("");
				$(".pdf_ship_to_enterprise_detail").html("");
			}
		}
	});

	$(".customized_checkbox").change(function(){
		var checkbox_for = $(this).attr("checkboxfor");
		if(checkbox_for == "row_seperator") {
			if($(this).is(":checked")) {
				$(".item_table, .hsn_table").addClass("row-seperator");
			}
			else {
				$(".item_table, .hsn_table").removeClass("row-seperator");
			}
		}
		if(checkbox_for == "column_seperator") {
			if($(this).is(":checked")) {
				$(".item_table, .hsn_table").addClass("column-seperator");
			}
			else {
				$(".item_table, .hsn_table").removeClass("column-seperator");
			}
		}
		if(checkbox_for == "row_shading") {
			if($(this).is(":checked")) {
				$(".row_shading").addClass("shaded");
				$(".header_shading").addClass("shaded");
			}
			else {
				$(".row_shading").removeClass("shaded");
				$(".header_shading").removeClass("shaded");
			}
		}
		if(checkbox_for == "show_total_section") {
			if($(this).is(":checked")) {
				$(".total_section").removeClass("hide");
				$("input[name='pti-tax_type']").trigger("change");
				$("#id_pts-include_subtotal").removeAttr("disabled");
				if($("#id_pts-include_subtotal").is(":checked")) {
					$("#id_pts-include_qty_total").removeAttr("disabled");
				}
				else {
					$("#id_pts-include_qty_total").attr("disabled", "disabled");
				}
			}
			else {
				$(".total_section").addClass("hide");
				$("#id_pts-include_subtotal").attr("disabled", "disabled");
				$("#id_pts-include_qty_total").attr("disabled", "disabled");
			}
			$("#id_pts-include_subtotal").trigger("change");
		}

		if(checkbox_for == "show_sub_total_section") {
			if($(this).is(":checked") && $(this).is(":enabled")) {
				$(".sub_total_section").removeClass("hide");
				if($("#id_pts-include_total").is(":checked")) {
					$("#id_pts-include_qty_total").removeAttr("disabled");
				}
				else {
					$("#id_pts-include_qty_total").attr("disabled", "disabled");
				}
			}
			else {
				$(".sub_total_section").addClass("hide");
				$("#id_pts-include_qty_total").attr("disabled", "disabled");
			}
		}

		if(checkbox_for == "taxable_item") {
			if($(this).is(":checked")) {
				$("input[name='pti-tax_type'], .taxable_item").removeAttr("disabled");
				$(".tax_rate_column, .tax_one_column, .consolidated_tax, .tax_in_description, .other_tax_column").removeClass("hide");
				$("input[name='pti-tax_type']").trigger("change");
				$("#id_pti-show_tax_for_dc").removeAttr("disabled");
			}
			else {
				$("#id_pti-tax_type_hidden_field").val($("input[name='pti-tax_type']:checked").val())
				$("input[name='pti-tax_type'], .taxable_item").attr("disabled", "disabled");
				$(".tax_rate_column, .tax_one_column, .consolidated_tax, .tax_in_description, .other_tax_column").addClass("hide");
				$(".total_section_4").addClass("hide");
				$("#id_pti-show_tax_for_dc").attr("disabled", "disabled");
				colspanManipulation();
			}
			calculateWidthforItemTable();
		}

		if(checkbox_for == "td_tax"){
			if($(this).is(":checked")) {
				$(".td_tax").removeClass("hide");
			}
			else {
				$(".td_tax").addClass("hide");
			}
			colspanManipulation();
			calculateWidthforItemTable();
		}
		if(checkbox_for == "show_quantity_total"){
			if($(this).is(":checked")) {
				$(".total_section_2").removeClass("hide");
			}
			else {
				$(".total_section_2").addClass("hide");
			}
			colspanManipulation();
			calculateWidthforItemTable();
		}

		if(checkbox_for == "tax_rate_column" || checkbox_for == "tax_amount_column") {
			var isRateEnabled = $("#id_pti-include_taxrate").is(":checked") && $("#id_pti-include_taxrate").is(":enabled");
			var isAmtEnabled = $("#id_pti-include_taxamount").is(":checked") && $("#id_pti-include_taxamount").is(":enabled");
			$(".tax_one_column").find(".tax_one_column_amt").addClass("hide");
			$(".tax_one_column").find(".tax_one_column_rate").addClass("hide");
			if(isRateEnabled && isAmtEnabled) {
				$(".tax_one_column").addClass("hide");
				$(".tax_rate_column").removeClass("hide");
				$(".item_1 .tax_one_column_csgt").html("6.48<br><small>@ 2.50%</small>");
				$(".item_1 .tax_one_column_ssgt").html("6.48<br><small>@ 2.50%</small>");
				$(".item_1 .tax_one_column_isgt").html("-");
				$(".item_2 .tax_one_column_csgt").html("2.70<br><small>@ 2.50%</small>");
				$(".item_2 .tax_one_column_ssgt").html("2.70<br><small>@ 2.50%</small>");
				$(".item_2 .tax_one_column_isgt").html("-");
				$(".tax_one_column_csgt_total").html("9.35");
				$(".tax_one_column_ssgt_total").html("9.35");
				$(".tax_one_column_isgt_total").html("-");
				$(".total_section_4").attr({colspan: "6"}).removeClass("hide");
			}
			else if(isRateEnabled || isAmtEnabled) {
				$(".tax_one_column").removeClass("hide");
				$(".tax_rate_column").addClass("hide");
				if(isAmtEnabled) {
					var selected_tax = $("input[name='pti-tax_type']:checked").val();
					if(selected_tax == 4) {
						$(".item_1 .tax_one_column_csgt").html("6.48<br><small>@ 2.50%</small>");
						$(".item_1 .tax_one_column_ssgt").html("6.48<br><small>@ 2.50%</small>");
						$(".item_1 .tax_one_column_isgt").html("-");
						$(".item_2 .tax_one_column_csgt").html("2.70<br><small>@ 2.50%</small>");
						$(".item_2 .tax_one_column_ssgt").html("2.70<br><small>@ 2.50%</small>");
						$(".item_2 .tax_one_column_isgt").html("-");
						$(".tax_one_column_csgt_total").html("9.35");
						$(".tax_one_column_ssgt_total").html("9.35");
						$(".tax_one_column_isgt_total").html("-");
						$(".tax_one_column").find(".tax_one_column_rate").removeClass("hide");
						$(".tax_one_column").find(".tax_one_column_amt").removeClass("hide");
					}
					else {
						$(".item_1 .tax_one_column_csgt").text("6.65");
						$(".item_1 .tax_one_column_ssgt").text("6.65");
						$(".item_1 .tax_one_column_isgt").text("-");
						$(".item_2 .tax_one_column_csgt").text("2.70");
						$(".item_2 .tax_one_column_ssgt").text("2.70");
						$(".item_2 .tax_one_column_isgt").text("-");
						$(".tax_one_column_csgt_total").text("9.35");
						$(".tax_one_column_ssgt_total").text("9.35");
						$(".tax_one_column_isgt_total").text("-");
						$(".tax_one_column").find(".tax_one_column_amt").removeClass("hide");
					}
				}
				else {
					$(".tax_one_column_csgt").text("@2.5%");
					$(".tax_one_column_ssgt").text("@2.5%");
					$(".tax_one_column_isgt").text("-");
					$(".tax_one_column_csgt_total").text("");
					$(".tax_one_column_ssgt_total").text("");
					$(".tax_one_column_isgt_total").text("");
					$(".tax_one_column").find(".tax_one_column_rate").removeClass("hide");
				}
				$(".total_section_4").attr({colspan: "3"}).removeClass("hide");
			}
			else {
				$(".tax_one_column").addClass("hide");
				$(".tax_rate_column").addClass("hide");
				$(".tr_second_row").addClass("hide");
				$(".total_section_4").addClass("hide");
			}
			colspanManipulation();
			calculateWidthforItemTable();
		}
		if(checkbox_for == "pdf_item_hsn_code" || checkbox_for == "pdf_hsn_in_description") {
			var isHsnEnabled = $("#id_pti-include_hsnsac").is(":checked");
			var isHsnDescEnabled = $("#id_pti-hsnsac_part_of_itemdetails").is(":checked");
			if(isHsnEnabled && isHsnDescEnabled) {
				$(".pdf_item_hsn_code").removeClass("hide");
				$(".td_hsn_code").addClass('hide');
				$("#id_pti-hsnsac_width").attr("disabled", "disabled");
			}
			else if(isHsnEnabled || isHsnDescEnabled) {
				if(isHsnEnabled) {
					$(".pdf_item_hsn_code").addClass("hide");
					$(".td_hsn_code").removeClass('hide');
					$("#id_pti-hsnsac_width").removeAttr("disabled")
				}
				else {
					$(".td_hsn_code").addClass('hide');
					$(".pdf_item_hsn_code").addClass("hide");
					$("#id_pti-hsnsac_width").attr("disabled", "disabled")
				}

			}
			else {
				$(".pdf_item_hsn_code").addClass("hide");
				$(".td_hsn_code").addClass('hide');
				$("#id_pti-hsnsac_width").attr("disabled", "disabled")
			}
			if(isHsnEnabled) {
				$("#id_pti-hsnsac_part_of_itemdetails").removeAttr("disabled")
			}
			else {
				$("#id_pti-hsnsac_part_of_itemdetails").attr("disabled", "disabled")
			}
			setTimeout(function(){
				calculateWidthforItemTable();
				colspanManipulation();
			},10);
		}

		if(checkbox_for == "pdf_item_dc_no") {
			var isDcnoEnabled = $("#id_pti-include_dc_no").is(":checked");
			var isDcqtyEnabled = $("#id_pti-include_dc_qty").is(":checked");
			var isDcdateEnabled = $("#id_pti-include_dc_date").is(":checked");
			if(isDcnoEnabled) {
				$("#id_pti-include_dc_qty").removeAttr("disabled")
				$("#id_pti-include_dc_date").removeAttr("disabled")
				$(".pdf_item_dc_no").removeClass("hide");
			}
			else {
				$("#id_pti-include_dc_qty").attr("disabled", "disabled")
				$("#id_pti-include_dc_date").attr("disabled", "disabled")
				$(".pdf_item_dc_no").addClass("hide");
			}
		}

		if(checkbox_for == "td_uom" || checkbox_for == "pdf_unit_in_price") {
			var isUnitEnabled = $("#id_pti-include_units").is(":checked");
			var isUnitInQtyEnabled = $("#id_pti-units_in_quantity_column").is(":checked");
			if(isUnitEnabled && isUnitInQtyEnabled) {
				$(".pdf_unit_in_price").removeClass("hide");
				$(".td_uom").addClass('hide');
				$("#id_pti-units_width").attr("disabled", "disabled");
			}
			else if(isUnitEnabled || isUnitInQtyEnabled) {
				if(isUnitEnabled) {
					$(".pdf_unit_in_price").addClass("hide");
					$(".td_uom").removeClass('hide');
					$("#id_pti-units_width").removeAttr("disabled")
				}
				else {
					$(".td_uom").addClass('hide');
					$(".pdf_unit_in_price").addClass("hide");
					$("#id_pti-units_width").attr("disabled", "disabled")
				}
			}
			else {
				$(".pdf_unit_in_price").addClass("hide");
				$(".td_uom").addClass('hide');
				$("#id_pti-units_width").attr("disabled", "disabled")
			}
			if(isUnitEnabled) {
				$("#id_pti-units_in_quantity_column").removeAttr("disabled")
			}
			else {
				$("#id_pti-units_in_quantity_column").attr("disabled", "disabled")
			}
			setTimeout(function(){
				calculateWidthforItemTable();
				colspanManipulation();
			},10);
		}

		if(checkbox_for == "td_qty" || checkbox_for == "show_quantity_total") {
			var isQtyEnabled = $("#id_pti-include_quantity").is(":checked");
			var isQtyInTotalEnabled = $("#id_pts-include_qty_total").is(":checked");
			if(isQtyEnabled && isQtyInTotalEnabled) {
				$(".total_section_2").removeClass("hide");
				$(".td_qty").removeClass("hide")
			}
			else if(isQtyEnabled || isQtyInTotalEnabled) {
				$(".total_section_2").addClass("hide")
				if(isQtyEnabled) {
					$(".td_qty").removeClass("hide");
				}
				else {
					$(".td_qty").addClass("hide");
				}
			}
			else {
				$(".total_section_2").addClass("hide");
				$(".td_qty").addClass("hide");
			}
			setTimeout(function(){
				calculateWidthforItemTable();
				colspanManipulation();
			},10);
		}
	});
}

$(".width_calc_checkbox").change(function(){
	if($(this).is(":checked") && $(this).is(":enabled")) {
		$(this).closest("div").find(".input_spinner").removeAttr("disabled");
	}
	else {
		$(this).closest("div").find(".input_spinner").attr("disabled", "disabled");
	}
	setTimeout(function(){
		calculateWidthforItemTable();
		calculateWidthforDSTable();
		colspanManipulation();
	},10);
});



$("input[name='pti-tax_type']").change(function(){
	var selected_tax = $("input[name='pti-tax_type']:checked").val();
	if(selected_tax == 1) {
		$(".tax_rate_column").removeClass("hide");
		$(".tax_one_column, .consolidated_tax, .tax_in_description").addClass("hide");
		$(".total_section_4").attr({colspan: "6"}).removeClass("hide");
		if($("#id_pti-include_tax").is(":checked")) {
			$(".taxable_item").removeAttr("disabled");
		}
	}
	else if(selected_tax == 2) {
		$(".consolidated_tax").removeClass("hide");
		$(".tax_one_column, .tax_rate_column, .tax_in_description").addClass("hide");
		$(".total_section_4").addClass("hide");
		$(".taxable_item").attr("disabled", "disabled");
	}
	else if(selected_tax == 3) {
		$(".tax_in_description").removeClass("hide");
		$(".tax_one_column, .tax_rate_column, .consolidated_tax").addClass("hide");
		$(".total_section_4").addClass("hide");
		$(".taxable_item").attr("disabled", "disabled");
	}
	else if(selected_tax == 4) {
		$(".tax_one_column").removeClass("hide");
		$(".tax_rate_column, .consolidated_tax, .tax_in_description").addClass("hide");
		$(".total_section_4").attr({colspan: "3"}).removeClass("hide");
		$(".taxable_item").attr("disabled", "disabled");
		if($("#id_pti-include_tax").is(":checked")) {
			$("#id_pti-include_taxamount, #id_pti-taxamount_width").removeAttr("disabled")
		}
	}
	colspanManipulation();
	setTimeout(function(){
		calculateWidthforItemTable();
		$("#id_pti-include_taxrate").trigger("change");
		$("#id_pti-include_taxamount").trigger("change");
	},20);

});

function colspanManipulation() {
	var totalVisibleColumn = $(".item_table").find("tbody").find("tr:first-child").find("td:visible").length;
	var totalVisibleColumnForPrice = $(".item_table").find("thead").find("tr:first-child").find("th:lt(3):visible").length;
	var totalVisibleColumnForPrice_1 = $(".item_table").find("thead").find("tr:first-child").find("th:lt(3):visible").length;
	var isRateEnabled = $("#id_pti-include_taxrate").is(":checked") && $("#id_pti-include_taxrate").is(":enabled");
	var isAmtEnabled = $("#id_pti-include_taxamount").is(":checked") && $("#id_pti-include_taxamount").is(":enabled");



	var totalAmtVisibleColumn, totalTaxVisibleColumn = 0;
	if($(".td_uom_text").is(":visible")) {
		totalTaxVisibleColumn++;
	}
	if($(".td_price_text").is(":visible")) {
		totalTaxVisibleColumn++;
	}
	if($(".td_disc_text").is(":visible")) {
		totalTaxVisibleColumn++;
	}
	if($(".td_tax_text").is(":visible")) {
		totalTaxVisibleColumn++;
	}
	totalAmtVisibleColumn = totalTaxVisibleColumn;
	if($(".td_qty_text").is(":visible") && !$("#id_pts-include_qty_total").is(":checked")) {
		totalAmtVisibleColumn++;
	}
	if($(".td_qty_text").is(":visible")) {
		totalTaxVisibleColumn++;
	}
	if(isRateEnabled) {
		totalTaxVisibleColumn += 3;
	}
	if(isAmtEnabled) {
		totalTaxVisibleColumn += 3;
	}
	if(isRateEnabled && isAmtEnabled) {
		$(".total_section_1").attr("colspan", totalVisibleColumnForPrice_1);
		$(".total_section_3").attr("colspan", Number(totalVisibleColumn - totalVisibleColumnForPrice_1));
	}
	else {
		$(".total_section_1").attr("colspan", totalVisibleColumnForPrice);
		$(".total_section_3").attr("colspan", Number(totalVisibleColumn - totalVisibleColumnForPrice));
	}
	$(".sub_total_section .total_section_1").attr("colspan", totalVisibleColumnForPrice);
	$(".full-length-td").attr("colspan", totalVisibleColumn);
	$(".sub_total_section .total_section_3").attr("colspan", totalAmtVisibleColumn);
}

function calculateWidthforItemTable() {
	var totalWidthValue = 0;
	$(".form-control.input_spinner_set_width:enabled").each(function() {
		if($(this).closest(".input_width_spinner").find(".width_calc_checkbox").attr("id") == "id_pti-include_taxrate" && $(this).closest(".input_width_spinner").find(".width_calc_checkbox").is(":checked")) {
			totalWidthValue += Number($(this).val() * 3);
		}
		else if($(this).closest(".input_width_spinner").find(".width_calc_checkbox").attr("id") == "id_pti-include_taxamount" && $(this).closest(".input_width_spinner").find(".width_calc_checkbox").is(":checked")) {
			totalWidthValue += Number($(this).val() * 3);
		}
		else {
			totalWidthValue += Number($(this).val());
		}
		var widthForDescription = 100 - totalWidthValue;
		$("input[spinner_for='td_description']").val(widthForDescription);
		$("#id_pti-itemdetails_width_hidden_field").val(widthForDescription);
	});
	setWidthToItemTable();
}

function calculateWidthforDSTable() {
	var totalWidthValueDS = 0;
	$(".form-control.input_spinner_set_width_ds:enabled").each(function() {
		totalWidthValueDS += Number($(this).val());
	});

	var widthForDescriptionDS = 100 - totalWidthValueDS;
	$("input[spinner_for='td_delivery_description']").val(widthForDescriptionDS);
	$("#id_pts-item_details_width_hidden_field").val(widthForDescriptionDS);
	setWidthToDSTable();
}

function calculateWidthforBanner() {
	var totalWidthHeaderBanner = 0;
	var totalWidthFooterBanner = 0;
	$(".form-control.input_spinner_set_width_header_banner:enabled").each(function() {
		totalWidthHeaderBanner += Number($(this).val());
	});
	$(".form-control.input_spinner_set_width_footer_banner:enabled").each(function() {
		totalWidthFooterBanner += Number($(this).val());
	});

	var widthForHeaderBanner = 100 - totalWidthHeaderBanner;
	var widthForFooterBanner = 100 - totalWidthFooterBanner;

	$("input[spinner_for='misc_header-left_banner']").val(widthForHeaderBanner);
	$("input[spinner_for='misc_footer-left_banner']").val(widthForFooterBanner);
	setWidthToBannerImage();
}

$("input[name='pti-tax_payable_on_reverse_charge']").change(function(){
	var selected_option = $("input[name='pti-tax_payable_on_reverse_charge']:checked").val();
	if(selected_option == "1") {
		$(".pdf_tax_payable").removeClass("hide");
	}
	else {
		$(".pdf_tax_payable").addClass("hide");
	}
});

$("#id_pth-po_number_format").change(function(){
	var data = $(this).val();
	validatePurchaseNumberFormat(data, "edit", "");
});

function validatePurchaseNumberFormat(data, action, url){
	if(data != ""){
		$("#loading").show();
		$.ajax({
            url : "erp/admin/json/validate_purchase_number_format/",
			type: "POST",
		    dataType: "json",
		    async: false,
		    data : {"number_format": data},
            success: function (response){
                $("#purchase_number_validation_result").val('');
                $("#loading").hide();
                if(response.response_message =="Success"){
                    var formatted_number = response["formated_purchase_number"]
                    var text_length = formatted_number.length;
					$(".custom-error-message").addClass('hide');
					if(text_length >16 && text_length <= 30)
					{
						swal({
							title: "",
							text: "Purchase code exceeds 16 Characters of length, which is the GST norm.<br> Do you wish to continue with this format?",
							type: "warning",
							showCancelButton: true,
							confirmButtonColor: "#209be1",
							confirmButtonText: "Yes, do it!",
							closeOnConfirm: true,
						},
						function(isConfirm){
							if(isConfirm) {
								$(".preview_purchase_number").text(formatted_number);
								$(".preview_purchase_number").removeClass('hide');
								$(".custom-error-message").addClass('hide');
								if(action == "save"){
									$("#template_add").attr('action', url);
									$("#template_add").submit();
								}
							}else{
								$(".preview_purchase_number").trigger('focus');
							}
						});
					}else if(text_length > 30){
						$(".error-border").removeClass('error-border');
                        $(".custom-error-message").removeClass('hide');
                        $(".preview_purchase_number").addClass('hide');
                    }else{
						$(".preview_purchase_number").text(formatted_number);
						 if(action == "save"){
							$("#template_add").attr('action', url);
							$("#template_add").submit();
						 }
					}
				}
				else{
                    $("#purchase_number_validation_result").val("Invalid number format has been applied!<br> Please check the info");
                    swal({title: "", text: "Invalid Purchase number format has been applied!<br> Please check the info", type: "error"});
                    $(".preview_purchase_number").trigger('focus');
                }
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });
    }else{
    	 swal({title: "", text: "Purchase number format should not be empty!", type: "error"});
    	 $(".preview_purchase_number").trigger('focus');
    	 $("#purchase_number_validation_result").val("Purchase number format should not be empty!");
    }
}

function setWidthToItemTable() {
	$(".form-control.input_spinner_set_width").each(function() {
		var spinner_for = $(this).closest(".input_width_spinner").find("input[type='number']").attr('spinner_for');
		var spinner_value = $(this).val();
		$("."+spinner_for).css({width: spinner_value+"%" });
		if(spinner_for == "td_tax_rate") {
			var total = Number($("#id_pti-taxrate_width").val()) + Number($("#id_pti-taxamount_width").val());
			$(".item_table").find("th.td_cgst, th.td_sgst, th.td_igst").css({width: total+"%" });
		}
	});
}
$(".select_template div").click(function(){
		$(".purchase_template_default, .purchase_template_1").addClass("hide");
		if($(this).find("a").attr("id") == "template_1") {
			$(".purchase_template_default").removeClass("hide");
			$("#id_ptc-template_id").val("1");
		}
		else if($(this).find("a").attr("id") == "template_2") {
			$(".purchase_template_1").removeClass("hide");
			$("#id_ptc-template_id").val("2");
		}

		$(".select_template_container").removeClass("selected_template")
		$(this).addClass("selected_template");
		$("#template_chooser").modal("hide");
	});


function setWidthToDSTable() {
	$(".form-control.input_spinner_set_width_ds").each(function() {
		var spinner_for = $(this).closest(".input_width_spinner").find("input[type='number']").attr('spinner_for');
		var spinner_value = $(this).val();
		$("."+spinner_for).css({width: spinner_value+"%" });
	});
}

function setWidthToBannerImage() {
	$(".form-control.input_spinner_set_width_header_banner").each(function() {
		var spinner_for = $(this).closest(".input_width_spinner").find("input[type='number']").attr('spinner_for');
		var spinner_value = $(this).val();
		$("."+spinner_for).css({width: spinner_value+"%" });
		if(spinner_value <= 0) {
			$("."+spinner_for).addClass("hide");
		}
		else {
			$("."+spinner_for).removeClass("hide");
		}
	});
	$(".form-control.input_spinner_set_width_footer_banner").each(function() {
		var spinner_for = $(this).closest(".input_width_spinner").find("input[type='number']").attr('spinner_for');
		var spinner_value = $(this).val();
		$("."+spinner_for).css({width: spinner_value+"%" });
		if(spinner_value <= 0) {
			$("."+spinner_for).addClass("hide");
		}
		else {
			$("."+spinner_for).removeClass("hide");
		}
	});
}


$("#template_add").submit(function(event){
	var post_url = $(this).attr("action");
	var form_data = $(this).serialize();
	if(post_url.indexOf("preview") > 0){
		event.preventDefault();
		$.ajax({
            url : $(this).attr("action"),
			type: "POST",
		    dataType: "json",
		    data : $(this).serialize(),
            success: function (response){
                if(response.response_message =="Success"){
                    $("#preview_document_container").html();
					var row1 = '<object class="document_preview_viewer" data="'+response.single_page_data+'"></object>';
					var row2 = '<object class="document_preview_viewer" data="'+response.multi_page_data+'"></object>';
	                $("#preview_document_container #tab1").html(row1);
	                $("#preview_document_container #tab2").html(row2);
					$("#preview_modal").modal("show");
					$("#single-page-Purchase").trigger("click");
				}
				else{
                    swal({title: "", text: "Something went wrong!<br> Please try again", type: "error"});
                }
                $("#pdf_preview").val("PDF Preview").removeAttr("disabled");
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
                $("#pdf_preview").val("PDF Preview").removeAttr("disabled");
                if(xhr.responseText.indexOf('Your session has expired') != -1) {
                	location.reload();
                	return;
                }
            }
        });
    }
});

$("#pdf_preview").click(function(){
	$(this).val("Generating PDF...").attr("disabled", "disabled");
});

function openNotesOverlay() {
	$("#notesEditableContainer").modal("show");
}
function openSubjectOverlay(){
	$("#subjectEditableContainer").modal("show");
}

function openMailBodyOverlay(){
	$("#mailbodyEditableContainer").modal("show");
}

function addPurchaseNote() {
	var notes = CKEDITOR.instances.notes_editor.getData();
	$("#id_ptm-notes").html(notes);
	$("#id_ptm-notes").next("div").html(notes);
	$(".notes").html(notes);
	$("#notesEditableContainer").modal("hide");
}

function addSubjectNote(){
	var subjectNotes = CKEDITOR.instances.subject_editor.getData();
	$("#id_subject-notes").html(subjectNotes);
	$("#id_subject-notes").next("div").html(subjectNotes);
	$(".greeting_subject").html(subjectNotes);
	$("#subjectEditableContainer").modal("hide");
}

function addMailBodyNote(){
	var mailBodyNotes = CKEDITOR.instances.mailbody_editor.getData();
	$("#id_mail-notes").html(mailBodyNotes);
	$("#id_mail-notes").next("div").html(mailBodyNotes);
	$(".greeting_mailbody").html(mailBodyNotes);
	$("#mailbodyEditableContainer").modal("hide");
}

function previewTemplateChanges(url){
	if(url.indexOf("preview") > 0){
		event.preventDefault();
		poTemplateData = constructJSON();
		var poTemplateHeader = {'header_banner': [{'position': 'left', 'width': $("#id_ptm-header-left_banner_width").val(), 'banner_image': $("#image_uploader-left").val()},
								{'position': 'right', 'width': $("#id_ptm-header-right_banner_width").val(), 'banner_image': $("#image_uploader-right").val()},
								{'position': 'center', 'width': $("#id_ptm-header-center_banner_width").val(), 'banner_image': $("#image_uploader-center").val()}]}

		var poTemplateFooter = {'footer_banner': [{'position': 'left', 'width': $("#id_ptm-footer-left_banner_width").val(), 'banner_image': $("#footer_uploader-left").val()},
								{'position': 'right', 'width': $("#id_ptm-footer-right_banner_width").val(), 'banner_image': $("#footer_uploader-right").val()},
								{'position': 'center', 'width': $("#id_ptm-footer-center_banner_width").val(), 'banner_image': $("#footer_uploader-center").val()}]}
		var po_data = JSON.stringify(poTemplateData);
		var po_header = JSON.stringify(poTemplateHeader);
		var po_footer = JSON.stringify(poTemplateFooter);
		$.ajax({
            url : url,
			type: "POST",
		    dataType: "json",
		    data: {'data': po_data, 'header_banner': po_header, 'footer_banner': po_footer},
            success: function (response){
                if(response.response_message =="Success"){
                    $("#preview_document_container").html();
					var row1 = '<object class="document_preview_viewer" data="'+response.single_page_data+'"></object>';
					var row2 = '<object class="document_preview_viewer" data="'+response.multi_page_data+'"></object>';
	                $("#preview_document_container #tab1").html(row1);
	                $("#preview_document_container #tab2").html(row2);
					$("#preview_modal").modal("show");
					$("#single-page-invoice").trigger("click");
				}
				else{
                    swal({title: "", text: "Something went wrong!<br> Please try again", type: "error"});
                }
                $("#pdf_preview").val("PDF Preview").removeAttr("disabled");
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
                $("#pdf_preview").val("PDF Preview").removeAttr("disabled");
                if(xhr.responseText.indexOf('Your session has expired') != -1) {
                	location.reload();
                	return;
                }
            }
        });
    }
}

function imageClickEvent() {
	$(".banner-image").click(function(){
  		$(this).closest(".banner-container").find("input[type='file']").click();
  	});
}

function applyCroppedImage() {
	$(".apply-crop").click(function(){
		var currentContainer = $(this).closest(".banner-container");
		setTimeout(function(){
			var currentBase64Value = currentContainer.find(".header-banner").val();
			var currentImageId = currentContainer.find(".header-banner").attr("id");
			currentContainer.find("img.uploaded-banner-image").attr("src", currentBase64Value).removeClass("hide");
			currentContainer.find(".hidden-attachment").val(currentBase64Value);
			currentContainer.find(".uploader").addClass("hide");
			currentContainer.find(".remove-banner-image").removeClass("hide");
			currentContainer.find(".remove-attachment").val("False");
			$(".po_pdf_editor ."+currentImageId).attr("src",currentBase64Value);
			if(currentImageId.indexOf("footer") >= 0) {
				var imageSize = $("#footer_banner_value").text().trim();
			}
			else {
				var imageSize = $("#header_banner_value").text().trim();
			}
			currentContainer.find(".banner-image-size").val(imageSize);
			$("."+currentImageId).css({maxHeight: imageSize+"px"});
		},100);
	});
}

function updateBannerImagesOnLoad() {
	$(".uploaded-banner-image").each(function () {
		if($(this).attr("src") != "") {
			var currentContainer = $(this).closest(".banner-container");
			var currentBase64Value = $(this).attr("src");
			var currentImageId = currentContainer.find(".header-banner").attr("id");
			currentContainer.find("img.uploaded-banner-image").removeClass("hide");
			currentContainer.find(".uploader").addClass("hide");
			currentContainer.find(".remove-banner-image").removeClass("hide");
			currentContainer.find(".remove-attachment").val("False");
			$(".po_pdf_editor ."+currentImageId).attr("src",currentBase64Value);
			if(currentImageId.indexOf("footer") >= 0) {
				var imageSize = $("#footer_banner_value").text().trim();
			}
			else {
				var imageSize = $("#header_banner_value").text().trim();
			}
			currentContainer.find(".banner-image-size").val(imageSize);
			$("."+currentImageId).css({maxHeight: imageSize+"px"});
		}
	})
}

function headerSliderChangeEvent() {
	var slider = document.getElementById("header_banner_size");
	var output = document.getElementById("header_banner_value");
	output.innerHTML = slider.value;
	slider.oninput = function() {
	   	output.innerHTML = this.value;
	   	$(".banner-header .banner-image-size").val(this.value);
	   	$(".header-banner-container img").css({maxHeight:this.value+"px"});
	}
}

function footerSliderChangeEvent() {
	var slider = document.getElementById("footer_banner_size");
	var output = document.getElementById("footer_banner_value");
	output.innerHTML = slider.value;
	slider.oninput = function() {
	   	output.innerHTML = this.value;
	   	$(".banner-footer .banner-image-size").val(this.value);
    	$(".footer-banner-container img").css({maxHeight:this.value+"px"});
	}
}

function removeBannerImageInit() {
    $(".remove-banner-image").click(function(){
    	var currentContainer = $(this).closest(".banner-container");
       	currentContainer.find("img.uploaded-banner-image").removeAttr("src").addClass("hide");
       	currentContainer.find(".banner-image").removeClass("image-uploaded");
       	currentContainer.find(".uploader").removeClass("hide");
	   	currentContainer.find(".remove-banner-image").addClass("hide");
	   	currentContainer.find(".remove-attachment").val("True");
	   	currentContainer.find(".header-banner").val("");
	    var currentId = $(this).next("input.header-banner").attr("id");
	    $(".po_pdf_editor ."+currentId).removeAttr("src");

	})
}

function closeCropModal(current) {
    $(current).closest(".modal").modal("hide")
}

function constructDateFormat(){
	var dateFormat = "";
	var timeFormat = "";
	var dateFormatPy = "";
	var timeFormatPy = "";
	var is12HoursFormat = false;
	$(".date-formatter").each(function(){
		dateFormat += $(this).find("option:selected").attr("data-value");
		dateFormatPy += $(this).val();
	});
	$(".time-formatter").each(function(){
		var lastDataValue = $(this).find("option:selected").attr("data-value");
		if(lastDataValue !="") {
			timeFormat += lastDataValue+":";
			timeFormatPy += $(this).val();
			if(lastDataValue == "hh") {
				is12HoursFormat = true;
			}
		}
		if(timeFormatPy.indexOf(":") == 0) {
			timeFormatPy = timeFormatPy.substring(1);
		}
	});
	timeFormat = timeFormat.substring(0, timeFormat.length - 1);

	if(is12HoursFormat) {
		timeFormatPy+= " %p";
		timeFormat+= " A";
	}
	var dateTimeFormatPy = dateFormatPy.trim()+" "+timeFormatPy.trim();
	if(timeFormat == "") timeFormat = " ";
	$(".dateFormat-preview").text(moment().format(dateFormat));
	$(".timeFormat-preview").text(moment().format(timeFormat));
	$(".issue_date_time").text(moment().format(dateFormat) + " " + moment().format(timeFormat));
	$(".pdf_po_date").text(moment().format(dateFormat) + " " + moment().format(timeFormat));
	$(".pdf_estimate_date").text(moment().format(dateFormat));
	$(".pdf_item_dc_date").text(moment().format(dateFormat));
	$("#id_ptg-po_doc_datetime_format").val(dateTimeFormatPy.trim())
}

function constructDateFormatField(){
	var dateFormat = $("#id_ptg-po_doc_datetime_format").val();
	var format = "";
	var formattedArray = [];
	for ( var i = 0; i < dateFormat.length; i++ ) {
		format += dateFormat[i];
		if(format == "%" || format == " %" || format == ",") {
			continue;
		}
		formattedArray.push(format);
		format = "";
	}
	var list = "";
	formattedArray.forEach(function(item) {
		if(list.length < 10) {
			if(item == "%d") {
				$(".date-formatter-date").val(item);
				list += "1,"
			}
			else if(item == "%b" || item == "%B" || item == "%m") {
				$(".date-formatter-month").val(item);
				list += "3,"
			}
			else if(item == "%y" || item == "%Y") {
				$(".date-formatter-year").val(item);
				list += "5,"
			}
			else if(["/", ",", "-", ", ",".", " "].indexOf(item) >=0) {
				if(list.indexOf("2,") == -1) {
					list += "2,"
					$('.date-formatter-seperator1').val(item);
				}
				else {
					list += "4,"
					$('.date-formatter-seperator2').val(item);
				}
			}
		}
		if(item == "%H") {
			$(".time-formatter-hour").val(" %H");
		}
		if(item == "%I") {
			$(".time-formatter-hour").val(" %I");
		}
		else if(item == "%M") {
			$(".time-formatter-minute").val(":%M");
		}
		else if(item == "%S") {
			$(".time-formatter-second").val(":%S");
		}
	});
	for (var i = 1; i <= 5; i++) {
		if(list.indexOf(i) == -1) list += i+",";
	}
	var orderArray = list.split(',');
	var listArray = $('#dateFormatter td');
	for (var i = 0; i < orderArray.length; i++) {
   		$('#dateFormatter tr').append(listArray[orderArray[i]-1]);
	}
	setTimeout(function(){
		$("#dateFormatter").sortable({
		    items: 'tr td',
		    cursor: 'pointer',
		    axis: 'x',
		    dropOnEmpty: false,
		    start: function (e, ui) {
		        ui.item.addClass("selected");
		    },
		    stop: function (e, ui) {
		       constructDateFormat();
		    }
		});
	},2000);

	$(".date-formatter, .time-formatter").change(function(){
		constructDateFormat();
	});
	constructDateFormat();
}

$("#id_pth-include_pono, #id_pth-include_podate").change(function(){
	if(!$("#id_pth-include_pono").is(":checked") && !$("#id_pth-include_podate").is(":checked")) {
		$(".pdf_po_no_date").addClass("hide");
	}
	else {
		$(".pdf_po_no_date").removeClass("hide");
	}
})

function scanCode(){
	if($("#id_pth-scan_code_option_1").is(":checked")){
		$(".qrcode").removeClass("hide");
	}
	else if($("#id_pth-scan_code_option_2").is(":checked")){
		$(".barcode").removeClass("hide");
	}
}

function leftContainerScrollInit() {
	var windowHeight = Number($( window ).height() - 200);
		$('.po_template_editor').slimScroll({
		height: windowHeight,
		alwaysVisible: true,
		size : '4px'
	});
}

function constructJSON(){
	var po_html_code = `<link rel="stylesheet" type="text/css" href='https://xserp.in/site_media/css/bootstrap.css'>`;
		if($("#id_ptc-template_id").val() == 1){
	    	po_html_code = po_html_code+" "+$(".po_container_save").html();
	    }
	    else if($("#id_ptc-template_id").val() == 2){
	    	po_html_code = po_html_code+" "+$(".po_container_simple_save").html();
	    }
		po_html_code = po_html_code.replace(/[\[]+/g,'{');
		po_html_code = po_html_code.replace(/[\[\]]+/g,'}');
		po_html_code = po_html_code.replace(/atable/g, "table");
		po_html_code = po_html_code.replace(/athead/g, "thead");
		po_html_code = po_html_code.replace(/atbody/g, "tbody");
		po_html_code = po_html_code.replace(/atfoot/g, "tfoot");
		po_html_code = po_html_code.replace(/ath/g, "th");
		po_html_code = po_html_code.replace(/atr/g, "tr");
		po_html_code = po_html_code.replace(/atd/g, "td");
		po_html_code = po_html_code.replace(/&gt;/g, ">");
		po_html_code = po_html_code.replace(/&lt;/g, "<");
		po_html_code = po_html_code.replace(/&quot;/g, "'");
		po_html_code = po_html_code.replace(/~lsqb;/g, "[");
		po_html_code = po_html_code.replace(/~rsqb;/g, "]");
		po_html_code = po_html_code.replace(/=""/g, "");
		console.log(po_html_code);
	var notes = $("#id_ptm-notes").html();
	    notes = notes.replace(/&lt;/g, '<');
	    notes = notes.replace(/&gt;/g, '>');
	    notes = notes.replace(/\t    /g, '');
	    notes = notes.replace(/\t/g, '');
	    notes = notes.replace(/\n/g, '');
	var subject = $("#id_subject-notes").html()
		subject = subject.replace(/&lt;/g, '<');
	    subject = subject.replace(/&gt;/g, '>');
	    subject = subject.replace(/\t    /g, '');
	    subject = subject.replace(/\t/g, '');
	    subject = subject.replace(/\n/g, '');
	var mail_body = $("#id_mail-notes").html()
		mail_body = mail_body.replace(/&lt;/g, '<');
	    mail_body = mail_body.replace(/&gt;/g, '>');
	    mail_body = mail_body.replace(/\t    /g, '');
	    mail_body = mail_body.replace(/\t/g, '');
	    mail_body = mail_body.replace(/\n/g, '');

	var registrationDetails = [];
	$(".registration_details").find("div").each(function(){
		if($(this).find("input").is(":checked")) {
			registrationDetails.push({
				"label_id":$(this).find("input").attr("data-field-id"),
				"label":$(this).find("label").text()
			});
		}
	});
	var poDateTimeFormat = "";
	var poTo12Hrs = false;
	$("#dateFormatter td").each(function(){
		poDateTimeFormat += $(this).find("select").val();
	});
	$("#timeFormatter td").each(function(){
		poDateTimeFormat += $(this).find("select").val();
		if($(this).find("select").val() == " %I") {
			poTo12Hrs = true;
		}
	});
	if(poTo12Hrs) {
		poDateTimeFormat += " %p";
	}
	var poTemplateData = {
		"general_config":{
			"font_family": $("#id_ptg-base_font").val(),
			"margin":{
				"top" : $("#id_ptg-margin_top").val(),
				"left" : $("#id_ptg-margin_left").val(),
				"right" : $("#id_ptg-margin_right").val(),
				"bottom" : $("#id_ptg-margin_bottom").val(),
			},
			"page_size":"A4",
			"orientation":"portrait",
			"datetime": poDateTimeFormat
		},
		"header_config": {
			"company": {
				"logo": {
					"print": $("#id_pth-include_logo").is(":checked"),
					"size": $("#id_pth-logo_size").val(),
				},
				"company_name": {
					"print": $("#id_pth-include_name").is(":checked"),
					"font_family": $("#id_pth-name_font").val(),
					"font_size": $("#id_pth-name_font_size").val(),
				}
			},
			"form_name":{
				'font_size' : $("#id_pth-form_name_font_size").val() ,
				"label":{
					"po": $("#id_pth-purchase_order_label").val(),
					"jo": $("#id_pth-job_order_label").val(),
				}
			},
			"font_size":12,
			"company_info":{
				"print_address":$("#id_pth-include_address").is(":checked"),
				"print_phone_no":$("#id_pth-include_phone_no").is(":checked"),
				"print_email":$("#id_pth-include_email").is(":checked"),
				"print_fax":$("#id_pth-include_fax").is(":checked"),
			},
			"include_reg_items": registrationDetails,
			"vendor":{
				"name":{
					"print" : $("#id_pth-include_vendorname").is(":checked"),
					'label' : $("#id_pth-vendornames_label").val(),
				},
				"address":{
					"print" : $("#id_pth-include_vendoraddress").is(":checked"),
					'label' : $("#id_pth-vendoraddress_label").val()
				},
				"code":{
					"print" : $("#id_pth-include_vendorcode").is(":checked"),
					'label' : $("#id_pth-vendorcode_label").val()
				},
				"print_email":$("#id_pth-include_vendor_email").is(":checked"),
				"print_phone_no":$("#id_pth-include_vendor_phoneno").is(":checked"),
				"acknowledge":{
					"print" : $("#id_pth-include_vendoracknowledgement").is(":checked"),
					'label' : $("#id_pth-vendoracknowledgement_label").val()
				},
			},
			"field_name":{
				"pono_label" :$("#id_pth-pono_label").val(),
				"jono_label" :$("#id_pth-jo_no_label").val(),
				"po_datelabel":$("#id_pth-podate_label").val(),
				"indent_no":{
					'print' : $("#id_pth-include_indent_no").is(":checked"),
					'label' : $("#id_pth-indent_no_label").val()
				},
				"indent_date":{
					'print' : $("#id_pth-include_indent_date").is(":checked"),
					'label' : $("#id_pth-indent_date_label").val()
				},
				"quotn_ref_no":{
					'print' : $("#id_pth-include_quotn_ref").is(":checked"),
					'label' : $("#id_pth-quotn_ref_label").val(),
				},
				"quotn_date":{
					'print' : $("#id_pth-include_quotn_date").is(":checked"),
					'label' : $("#id_pth-quotn_date_label").val(),
				},
				"currency":{
					'print' : $("#id_pth-include_currency").is(":checked"),
					'label' : $("#id_pth-currency_label").val(),
				},
				"authorized_sign":{
					'print' : $("#id_pth-include_authorizedsign").is(":checked"),
					'label' : $("#id_pth-authorizedsign_label").val() ,
					'print_approversign' : $("#id_pth-include_approver_signature").is(":checked"),
				},
				"prepared_by":{
					'print' : $("#id_pth-include_preparedsign").is(":checked"),
					'label' : $("#id_pth-preparedsign_label").val() ,
					'print_approversign' : $("#id_pth-include_prepared_signature").is(":checked"),
				},
			},
			"terms_conditions":{
				"payment_terms":{
					'print' : $("#id_pth-include_paymentterms").is(":checked"),
					'label' : $("#id_pth-paymentterms_label").val()
				},
				"special_instructions":{
					'print' : $("#id_pth-include_splinstructions").is(":checked"),
					'label' : $("#id_pth-splinstructions_label").val()
				},
			},
			"billing_address":{
				'print' : $("#id_pth-include_billingaddress").is(":checked"),
				'label' : $("#id_pth-billingaddress_label").val()
			},
			"shipping_address":{
				'print' : $("#id_pth-include_shippingaddress").is(":checked"),
				'label' : $("#id_pth-shippingaddress_label").val(),
			},
		},
		"items_config":{
			"item_table":{
				"font_size":$("#id_pti-font_size").val(),
				"sno":{
					'print' : $("#id_pti-include_sno").is(":checked"),
					'label' : $("#id_pti-sno_label").val(),
					'width' : $("#id_pti-sno_width").val()
				},
				"item_details":{
					'label' : $("#id_pti-itemdetails_label").val(),
					'width' : $("#id_pti-itemdetails_width").val(),
					'itemcode' : {
						'print' : $("#id_pti-include_itemcode").is(":checked"),
						'label' : $("#id_pti-itemcode_label").val()
					},
					'itemname_label' : $("#id_pti-name_label").val(),
					'make' : {
						'print' : $("#id_pti-include_make").is(":checked"),
						'label' : $("#id_pti-make_label").val()
					},
					'part_no' : {
						'print' : $("#id_pti-include_partno").is(":checked"),
						'label' : $("#id_pti-partno_label").val()
					},
					'description' :{
						'print' : $("#id_pti-include_description").is(":checked"),
						'label' : $("#id_pti-description_label").val(),
					},
				},
				"hsnsac":{
					'print' : $("#id_pti-include_hsnsac").is(":checked"),
					'label' : $("#id_pti-hsnsac_label").val(),
					'width' : $("#id_pti-hsnsac_width").val() ,
					'print_in_itemdetails' :$("#id_pti-hsnsac_part_of_itemdetails").is(":checked"),
				},
				"quantity":{
					'print' : $("#id_pti-include_quantity").is(":checked"),
					'label' : $("#id_pti-quantity_label").val() ,
					'width' : $("#id_pti-quantity_width").val()
				},
				"units":{
					'print' : $("#id_pti-include_units").is(":checked"),
					'label' : $("#id_pti-units_label").val() ,
					'width' : $("#id_pti-units_width").val() ,
					'units_in_qty' : $("#id_pti-units_in_quantity_column").is(":checked")
				},
				"unit_price":{
					'print' : $("#id_pti-include_unit_price").is(":checked"),
					'label' : $("#id_pti-unit_price_label").val(),
					'width' : $("#id_pti-unit_price_width").val()
				},
				"discount":{
					'print' : $("#id_pti-include_discount").is(":checked"),
					'label' : $("#id_pti-discount_label").val(),
					'width' : $("#id_pti-discount_width").val()
				},
				"taxable_amount":{
					'print' : $("#id_pti-include_taxable_amount").is(":checked"),
					'label' : $("#id_pti-taxable_amount_label").val() ,
					'width' : $("#id_pti-taxable_amount_width").val() ,
				},
				"tax":{
					'print' : $("#id_pti-include_tax").is(":checked"),
					'field_type' : $("#id_pti-tax_type input:checked").val(),
					'taxrate' : {
						'print' : $("#id_pti-include_taxrate").is(":checked"),
						'width' : $("#id_pti-taxrate_width").val() ,
					},
					'taxamount' : {
						'print' : $("#id_pti-include_taxamount").is(":checked"),
						'width' : $("#id_pti-taxamount_width").val()
					}
				}
			},
			"field_alignment":{
				"separator":{
					'row_separator' : $("#id_pti-include_row_separator").is(":checked"),
					'column_separator' : $("#id_pti-include_column_separator").is(":checked"),
					'alternative_row_shading' : $("#id_pti-include_alternate_row_shading").is(":checked")
				},
				"item_sorter":$("#id_pti-item_sort_order").val() ,
			}
		},
		"summary_config":{
			"totals":{
				'print' : $("#id_pts-include_total").is(":checked"),
				'font_size' : $("#id_pts-font_size").val() ,
				'print_subtotal' : $("#id_pts-include_subtotal").is(":checked"),
				'print_qtytotal' : $("#id_pts-include_qty_total").is(":checked"),
				'print_total_in_words' : $("#id_pts-include_total_in_words").is(":checked")
			},
			"delivery_schedules_fields":{
				"delivery_schedules":{
					'print' : $("#id_pts-include_delivery_schedule_summary").is(":checked"),
					'font_size' : $("#id_pts-delivery_schedule_font_size").val() ,
				},
				"sno":{
					'print' : $("#id_pts-include_s_no").is(":checked"),
					'label' : $("#id_pts-s_no_label").val() ,
					'width' : $("#id_pts-s_no_width").val()
				},
				"description":{
					'label' : $("#id_pts-delivery_details_label").val() ,
					'width' : $("#id_pts-delivery_details_width").val() ,
				},
				"delivery_date":{
					'label' : $("#id_pts-delivery_due_label").val() ,
					'width' : $("#id_pts-delivery_due_width").val() ,
				},
				"quantity":{
					'label' : $("#id_pts-delivery_qty_label").val() ,
					'width' : $("#id_pts-delivery_qty_width").val()
				}
			},
			"hsn_summary":{
				'print' : $("#id_pts-include_hsn_summary").is(":checked"),
				'font_size' : $("#id_pts-hsn_tax_font_size").val()
			}
		},
		"misc_config":{
			"banner_header" : {
				"attachment" : [
					{
						"attachment_id" : "",
						"uploaded_on" : "",
						"last_modified_by" : "",
						"width" : "",
						"uploaded_by" : "",
						"position" : "left",
						"last_modified_on" : ""
					},
					{
						"attachment_id" : "",
						"uploaded_on" : "",
						"last_modified_by" : "",
						"width" : "",
						"uploaded_by" : "",
						"position" : "center",
						"last_modified_on" : ""
					},
					{
						"attachment_id" : "",
						"uploaded_on" : "",
						"last_modified_by" : "",
						"width" : "",
						"uploaded_by" : "",
						"position" : "right",
						"last_modified_on" : ""
					}
				],
				"height" : $("#header_banner_size").val()
			},
			"banner_footer" : {
				"attachment" : [
					{
						"attachment_id" : "",
						"uploaded_on" : "",
						"last_modified_by" : "",
						"width" : "",
						"uploaded_by" : "",
						"position" : "left",
						"last_modified_on" : ""
					},
					{
						"attachment_id" : "",
						"uploaded_on" : "",
						"last_modified_by" : "",
						"width" : "",
						"uploaded_by" : "",
						"position" : "center",
						"last_modified_on" : ""
					},
					{
						"attachment_id" : "",
						"uploaded_on" : "",
						"last_modified_by" : "",
						"width" : "",
						"uploaded_by" : "",
						"position" : "right",
						"last_modified_on" : ""
					}
				],
				"height" : $("#footer_banner_size").val()
			},
			"print_pageno": $("#id_ptm-include_page_no_in_footer").is(":checked"),
			"notes":notes,
			"sub":subject,
			"mail_body":mail_body,
			"print_summary_first_page": $("#id_ptm-include_first_page_summary").is(":checked"),
			"foot_note":{
				'font_size' : $("#id_ptm-font_size").val(),
				'notes' : $("#id_ptm-foot_note").val(),
			}
		},
		"print_template": po_html_code,
		"template_id" : $("#id_ptc-template_id").val()
	}
	return poTemplateData;
}
function saveTemplateChanges(pageType=""){

	poTemplateData = constructJSON();

	var poTemplateHeader = {'header_banner': [{'position': 'left', 'width': $("#id_ptm-header-left_banner_width").val(), 'banner_image': $("#image_uploader-left").val()},
							{'position': 'right', 'width': $("#id_ptm-header-right_banner_width").val(), 'banner_image': $("#image_uploader-right").val()},
							{'position': 'center', 'width': $("#id_ptm-header-center_banner_width").val(), 'banner_image': $("#image_uploader-center").val()}]}

	var poTemplateFooter = {'footer_banner': [{'position': 'left', 'width': $("#id_ptm-footer-left_banner_width").val(), 'banner_image': $("#footer_uploader-left").val()},
							{'position': 'right', 'width': $("#id_ptm-footer-right_banner_width").val(), 'banner_image': $("#footer_uploader-right").val()},
							{'position': 'center', 'width': $("#id_ptm-footer-center_banner_width").val(), 'banner_image': $("#footer_uploader-center").val()}]}
	var po_data = JSON.stringify(poTemplateData);
	var po_header =  JSON.stringify(poTemplateHeader);
	var po_footer = JSON.stringify(poTemplateFooter);
	$.ajax({
		url:"/erp/admin/po_template/save/",
		type:"POST",
		datatype:"json",
		data: {'data': po_data, 'header_banner': po_header, 'footer_banner': po_footer},
		success:function(response){
			if (typeof response.custom_message === 'undefined') {
				location.reload();
			}
			else if(response.custom_message == 'Purchase Configuration saved successfully') {
				if(pageType == "po") {
					window.location.replace('/erp/purchase/po_list/');
				}
				else if(pageType == "local") {
					swal("","PO configuration updated successfully", "success")
					tour.end()
				}
				else {
					swal("","PO configuration saved successfully", "success")
				}
			}
			else {
				swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
			}
		},
		error: function (xhr, errmsg, err) {
            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
        }
    });
}

var tour;
function poAutoSaveWithTour(pageType) {
	tour = new Tour({
	    storage : false,
	    backdrop: true,
	    template: `<div class='popover tour'><div class='arrow'></div> <h3 class='popover-title'></h3><div class='popover-content'></div> <div class='popover-navigation'> <button class='btn btn-default' data-role='ok' onclick='saveTemplateChanges("${pageType}")'>OK</button></div></div>`,
	    steps: [
		    {
		      element: "#round-off-container",
		      placement: "left",
		      title: "",
		      content: "Round-off field has been added in PO form. It is placed as shown below in print.",
		    }
	 	]
	});
	$("#loading").addClass("hide");
	tour.restart();
}
</script>

{% endblock %}