.xs-tab-container * {
    box-sizing: border-box;
}
.xs-tab-container li a {
    min-height: 50px;
    max-height: 50px;
    text-align: center;
}
.xs-tab-container {
    height: 42px;
}
.xs-tab-container .tab-content {
    clear: left;
}
.xs-tab-container.xs-bootstrap4 .xs-tabs-movable-container > .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
}
.xs-tabs-fixed-container {
    float: left;
    height: auto;
    overflow: hidden;
    width: 100%;
}
.xs-tabs-movable-container {
    position: relative;
}
.xs-tabs-movable-container .tab-content {
    display: none;
}
.xs-tab-container.xs-rtl .xs-tabs-movable-container > ul.nav-tabs {
    padding-right: 0;
}
.xs-tab-scroll-arrow {
    color: #428bca;
    cursor: pointer;
    display: none;
    float: left;
    font-size: 20px;
    margin-bottom: -1px;
    padding-top: 30px;
    width: 20px;
}
.xs-tab-scroll-arrow:hover {
    color: #209be1;
}
.xs-tab-scroll-arrow.xs-disable {
    color: #ddd;
    cursor: default;
}
.xs-tabs-fixed-container ul.nav-tabs > li {
    min-width: 150px;
    max-width: 240px;
}
.xs-tabs-fixed-container ul.nav-tabs > li.active a {
    background: #fafafa;
}
.xs-vertical-tabs__header {
    font-size: 20px;
    color: #209be1;
}
.xs-vertical-tabs__sub-header {
    font-size: 11px;
    color: #aaa;
}
.xs-vertical-tabs__title {
    text-align: center;
    font-size: 24px;
    padding-top: 18px;
}
.report-tab-content {
    padding: 15px;
    border: solid 1px #ddd;
    background: #fafafa;
    margin-top: 7px;
    width: fit-content;
}