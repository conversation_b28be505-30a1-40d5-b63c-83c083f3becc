"""

"""
import json
from datetime import datetime, date
from decimal import Decimal

import pymysql
import simplejson
from django.contrib.auth import SESSION_KEY
from django.http import HttpResponse
from django.http import HttpResponseRedirect
from django.template.response import TemplateResponse
from sqlalchemy import and_, or_

from erp import properties, helper, dao
from erp.admin import enterprise_module_settings
from erp.auth import USER_IN_SESSION_KEY, ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.dao import executeQuery
from erp.forms import IndentForm
from erp.formsets import IndentMaterialFormset
from erp.helper import generateIndentTypeChoices, getProjectFrequents, constructDifferentMakeName
from erp.masters.backend import MasterService
from erp.models import Indent, IndentMaterial, Party, MaterialSupplierProfile, Enterprise
from erp.properties import TEMPLATE_TITLE_KEY
from erp.stores import logger, INDENT_COMPLETED, INDENT_PENDING_DUE_TO_PO, INDENT_PENDING_DUE_TO_MATERIAL
from erp.stores.backend import StoresService, INDENT_FORM_PREFIX, IND_MAT_PREFIX, IndentVO, StoresDAO
from erp.stores.queries import INDENT_MATERIALS_QUERY
from erp.tags import generateTagFormset
from settings import HOST, USER, PASSWORD, DBNAME, PORT, SQLASession
from util.api_util import JsonUtil

__author__ = 'saravanan'

# Key-String constants
INDENTS_KEY = 'indents'
INDENT_FORM_KEY = 'indent_form'
INDENT_NO_FIELD_KEY = 'indent_no'
IND_MATERIAL_FORMSET_KEY = 'materials_formset'
IND_TAG_FORMSET_KEY = 'tags_formset'
PROJECTS_KEY = 'projects'
DELETE_INDENT_KEY = 'indent_no'
INDENTS_PURPOSE_FREQUENTS = 'frequents_purpose'
MATERIAL_FORM_KEY = 'material_form'
BILL_OF_MATERIALS_FORMSET_KEY = 'bill_of_materials_formset'
SPECIFICATION_FORMSET_KEY = 'specifications_formset'
MATERIAL_MAKE_FORMSET_KEY = 'material_makes_formset'
ALTERNATE_UNIT_FORMSET_KEY = 'alternate_unit_formset'
PRICE_OF_SUPP_MATERIALS_FORMSET_KEY = 'materials_party_price_formset'
INDENT_SUPPLIERS = 'indent_suppliers'
SUPPLIER_BUCKETS = 'supplier_buckets'


def manageIndents(request):
	"""
	Renders the view to manage indents.

	:param request: a Http-URL-request asking for Indent Management Webservice
	"""
	stores_service = StoresService()
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	status = request_handler.getPostData("status")
	request_handler.removeSessionAttribute(INDENT_NO_FIELD_KEY)
	from_date, to_date = JsonUtil.getDateRange(
		rh=request_handler, since_session_key='indent.since',
		till_session_key='indent.till') if request_handler.getPostData("since") != "" else (None, None)

	enterprise = stores_service.stores_dao.db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
	module_access = enterprise.setting_flags & enterprise_module_settings['indent_flag'] > 0
	project_code = request_handler.getAndCacheData(key=PROJECTS_KEY, session_key="indent.project_code")
	logger.info("Search project code on load indent %s, %s" % (project_code, type(project_code)))
	project_code = project_code if project_code and project_code != 'None' else None

	logger.info("Filtering Indents b/w date range: %s - %s for project %s, %s" % (
		from_date, to_date, project_code, status))
	status_filter = (INDENT_COMPLETED, INDENT_PENDING_DUE_TO_PO, INDENT_PENDING_DUE_TO_MATERIAL)
	if status == "completed":
		status_filter = (INDENT_COMPLETED,)
	elif status == "pending":
		status_filter = (INDENT_PENDING_DUE_TO_PO, INDENT_PENDING_DUE_TO_MATERIAL)
	elif status == "pending_due_po":
		status_filter = (INDENT_PENDING_DUE_TO_PO,)
	elif status == "pending_due_material":
		status_filter = (INDENT_PENDING_DUE_TO_MATERIAL,)

	indents = stores_service.getIndentsWithStatus(
		since=from_date, till=to_date if to_date is not None else None,
		enterprise_id=enterprise_id, project_code=project_code,
		status=status_filter)

	from_date, to_date = (indents[0]['raised_date'], datetime.today()) if indents and to_date is None else (
		from_date, to_date)

	query = """SELECT purpose, enterprise_id, count(1) as item_count from indents where enterprise_id= '%s'
				GROUP BY purpose, enterprise_id ORDER BY item_count DESC LIMIT 0,5""" % enterprise_id
	frequent_list = [item for item in dao.executeQuery(query)]
	frequent_list = [row[0] for row in frequent_list]

	return TemplateResponse(template=properties.MANAGE_INDENT_TEMPLATES, request=request, context={
		'module_access': module_access, 'edit_link': properties.EDIT_INDENT_URL, INDENTS_PURPOSE_FREQUENTS: frequent_list,
		INDENTS_KEY: indents, PROJECTS_KEY: helper.populateProjectChoices(enterprise_id=enterprise_id, need_blank_first=False),
		'filter_from': from_date, 'filter_to': to_date, 'search_project': project_code, TEMPLATE_TITLE_KEY: "Purchase Indent"})


def getSelectedProject(request):
	"""
	Used to fetch the selected project in edit page if project is in inactive state.
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	project_code = request_handler.getPostData("project_code")
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		project = helper.populateSelectedProject(project_code=project_code, enterprise_id=enterprise_id)
	except Exception as e:
		project = ""
		logger.exception("Failed edit indent... %s" % e.message)
	return HttpResponse(content=simplejson.dumps(project), mimetype='application/json')


def manageIndent(request):
	"""
	Renders the Indent Edit form - loaded with a chosen Indent & associated IndentMaterial data.

	:param request: a Http-URL-request asking to edit a chosen Indent
	:return: a Http response, populated with the Indent and its associated IndentMaterials
	"""
	stores_service = StoresService()
	request_handler = RequestHandler(request)
	mat_wise_supp_list = []
	indent_no = request_handler.getPostData(INDENT_NO_FIELD_KEY)
	if indent_no:
		logger.info('Indent: %s is being edited' % indent_no)
	else:
		indent_no = request_handler.getSessionAttribute(INDENT_NO_FIELD_KEY)
		logger.info('Indent: %s is being redirected to edit' % indent_no)
	enterprise = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise

	if indent_no is None:
		indent = Indent(raised_by=request_handler.getSessionAttribute(SESSION_KEY))
		indent.enterprise = enterprise
		indent.enterprise_id = enterprise.id
		logger.info("Enterprise loaded for the Indent: %s" % indent.enterprise_id)
		indent_vo = stores_service.constructIndentVO(indent)
		master_service = MasterService()
		material_vo = master_service.constructMaterialVO(enterprise_id=enterprise.id)
		frequent_project_list = getProjectFrequents(table='indents', enterprise_id=enterprise.id, condition='')
		project_code = None
	else:
		indent_to_be_edited = stores_service.stores_dao.getIndent(indent_no, enterprise.id)
		sql = """ SELECT * FROM indents where indent_no='%s' and enterprise_id='%s' """ % (indent_no, enterprise.id)
		result = dao.executeQuery(sql, as_dict=True)
		remarks_list = result[0]['sp_instructions']
		indent_vo = stores_service.constructIndentVO(indent_to_be_edited)
		master_service = MasterService()
		material_vo = master_service.constructMaterialVO(enterprise_id=enterprise.id)
		from_date, to_date = JsonUtil.getDateRange(
			rh=request_handler, since_session_key='indent.since', till_session_key='indent.till')

		project_code = request_handler.getAndCacheData(key=PROJECTS_KEY, session_key="indent.project_code")
		project_code = project_code if project_code and project_code != '0' else None
		all_suppliers = dao.executeQuery(
			"SELECT party_id, party_name FROM party_master WHERE enterprise_id='%s' ORDER BY party_name" % enterprise.id)

		buckets, other_po_value = stores_service.getSupplierBucket(
			enterprise_id=enterprise.id, indent_to_be_edited=indent_to_be_edited)
		total_indent_value = stores_service.getIndentValue(indent_no, enterprise.id)
		try:
			if buckets[0][0] == 'no-supplier':
				is_no_supplier_bucket = True
			else:
				is_no_supplier_bucket = False
		except Exception as e:
			is_no_supplier_bucket = False
			logger.exception("Failed edit indent... %s" % e.message)

	context = {
		'search_project': project_code, 'list_link': properties.MANAGE_INDENTS_URL,
		MATERIAL_FORM_KEY: material_vo.material_form, BILL_OF_MATERIALS_FORMSET_KEY: material_vo.material_formset,
		SPECIFICATION_FORMSET_KEY: material_vo.specification_formset,
		PRICE_OF_SUPP_MATERIALS_FORMSET_KEY: material_vo.supplier_price_material_formset,
		MATERIAL_MAKE_FORMSET_KEY: material_vo.material_make_formset, INDENT_FORM_KEY: indent_vo.indent_form,
		ALTERNATE_UNIT_FORMSET_KEY: material_vo.alternate_unit_formset,
		PROJECTS_KEY: helper.populateProjectChoices(enterprise_id=enterprise.id, need_blank_first=False),
		IND_MATERIAL_FORMSET_KEY: indent_vo.indent_material_formset,
		IND_TAG_FORMSET_KEY: indent_vo.indent_tag_formset,
		'selected_project_code': indent_to_be_edited.project_code if indent_no is not None else None,
		'is_price_modification_disabled': enterprise.is_price_modification_disabled}
	if indent_no is None:
		context['most_used_project'] = frequent_project_list[0][0] if len(frequent_project_list) != 0 else None
		context['make_list'] = []
		context[TEMPLATE_TITLE_KEY] = "Purchase Indent"

	else:
		context['filter_from'] = from_date
		context['filter_to'] = to_date
		context['suppliers'] = all_suppliers
		context['other_po_value'] = other_po_value
		context['is_no_supplier_bucket'] = is_no_supplier_bucket
		context['remarks_list'] = remarks_list
		context[TEMPLATE_TITLE_KEY] = indent_to_be_edited.getCode()
		context[INDENT_SUPPLIERS] = mat_wise_supp_list
		context[SUPPLIER_BUCKETS] = buckets
		context['total_indent_value'] = "%0.2f" % total_indent_value
	request_handler.setSessionAttribute(key=INDENT_NO_FIELD_KEY, value=None)
	return TemplateResponse(template=properties.MANAGE_INDENT_TEMPLATE, request=request, context=context)


def saveIndent(request):
	"""
	Requests the backend to persist the information of the Indent in the page to the DB.

	:param request: a Http-URL-request asking to save either a new Indent or an edit to a chosen Indent
	:return: a Http response, populated with information of the Indent just saved
	"""
	stores_service = StoresService()
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	logger.info("Saving indent for enterprise %s" % enterprise_id)

	indent_tag_formset = generateTagFormset(request_handler.getPostData())
	indent_vo = IndentVO(
		indent_form=IndentForm(
			data=request_handler.getPostData(), prefix=INDENT_FORM_PREFIX, enterprise_id=enterprise_id),
		indent_material_formset=IndentMaterialFormset(request_handler.getPostData(), prefix=IND_MAT_PREFIX),
		indent_tag_formset=indent_tag_formset)
	logger.debug(indent_tag_formset.as_table())
	saved_indent = stores_service.saveIndent(indent_vo, user_id=user_id, enterprise_id=enterprise_id)
	indent_no = request.POST.get('indent-indent_no')
	if indent_vo.is_valid():
		if saved_indent and saved_indent.indent_form:
			indent_no = saved_indent.indent_form.__dict__['initial']['indent_no']
		request_handler.setSessionAttribute(key=INDENT_NO_FIELD_KEY, value=indent_no)
		return HttpResponseRedirect(properties.EDIT_INDENT_URL)

	master_service = MasterService()
	material_vo = master_service.constructMaterialVO(enterprise_id=enterprise_id)
	all_suppliers = dao.executeQuery(
		"SELECT party_id, party_name FROM party_master WHERE enterprise_id='%s' ORDER BY party_name" % enterprise_id)
	return TemplateResponse(template=properties.MANAGE_INDENT_TEMPLATES, request=request, context={
		MATERIAL_FORM_KEY: material_vo.material_form, BILL_OF_MATERIALS_FORMSET_KEY: material_vo.material_formset,
		SPECIFICATION_FORMSET_KEY: material_vo.specification_formset,
		'list_link': properties.MANAGE_INDENTS_URL,
		PRICE_OF_SUPP_MATERIALS_FORMSET_KEY: material_vo.supplier_price_material_formset,
		MATERIAL_MAKE_FORMSET_KEY: material_vo.material_make_formset,
		INDENTS_KEY: [], INDENT_FORM_KEY: saved_indent.indent_form,
		IND_MATERIAL_FORMSET_KEY: saved_indent.indent_material_formset,
		IND_TAG_FORMSET_KEY: saved_indent.indent_tag_formset,
		PROJECTS_KEY: helper.populateProjectChoices(need_blank_first=False, enterprise_id=enterprise_id),
		'suppliers': all_suppliers, TEMPLATE_TITLE_KEY: saved_indent.indent_form.initial['indent_code']})


def deleteIndent(request):
	"""
	Requests the backend to delete a chosen Indent

	:param request: a Http-URL-request asking to delete a chosen Indent and its associated Indent Materials
	:return: a Http response redirected to the Indent Management page
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		stores_service = StoresService()
		request_handler = RequestHandler(request)
		indent_no = request_handler.getPostData(DELETE_INDENT_KEY)
		logger.info("Deleting indent for indent_no %s" % indent_no)
		stores_service.deleteIndent(indent_no=indent_no, enterprise_id=enterprise_id)
		return HttpResponseRedirect(properties.MANAGE_INDENTS_URL)
	except Exception as e:
		logger.exception("Failed deleting indent... %s" % e.message)
		raise


def loadCatalogueMaterials(request):
	"""
	Append selected material subset
	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	cat_code = request_handler.getPostData('cat_code')
	alternate_unit_id = request_handler.getPostData('alternate_unit_id')
	location_id = request_handler.getPostData('location_id')
	store_dao = StoresDAO()
	store_service = StoresService()
	catalogues = store_dao.getCatalogues(cat_code, enterprise_id)
	try:
		catalogue_material_data_dump = []
		materials = {}
		for catalogue in catalogues:
			if catalogue.item_id in materials:
				material = materials[catalogue.item_id]
			else:
				catalogues_child = store_dao.getCatalogues(catalogue.item_id, enterprise_id)
				make_name = constructDifferentMakeName(catalogue.makes_json)
				#stock_qty = store_dao.getClosingStock(enterprise_id=enterprise_id,item_id=catalogue.item_id,till=datetime.now(), is_faulty=0)
				stock_qty = 0
				stock_details = store_service.getClosingStock(
					mat_list=[int(catalogue.item_id)], enterprise_id=enterprise_id, is_faulty=0, location_id=location_id)
				for item in stock_details:
					stock_qty = item['closing_qty']
				material = {
					"name": catalogue.name, "quantity": catalogue.quantity, "drawing_no": catalogue.drawing_no,
					"unit_name": catalogue.unit_name, "makes": [], "hasChildren": len(catalogues_child) > 0,
					"price": catalogue.price, "tariff_no": catalogue.tariff_no if catalogue.tariff_no else "",
					"item_id": catalogue.item_id, "cat_code": catalogue.parent_id, "alternate_unit_id": 0,
					"scale_factor": 1, "unit_id": catalogue.unit_id, "material_type": catalogue.is_stocked,
					"is_service": catalogue.is_service, "make_name" : make_name, "stock_qty": stock_qty
				}
				if alternate_unit_id and int(alternate_unit_id) != 0:
					scale_factor = helper.getScaleFactor(
						enterprise_id=enterprise_id, item_id=material['cat_code'],
						alternate_unit_id=alternate_unit_id)
					if scale_factor:
						material['quantity'] = material['quantity'] * scale_factor
				catalogue_material_data_dump.append(material)
			materials[catalogue.item_id] = material

	except Exception:
		logger.info("Adding a material to a Catalogue failed...")
		from erp.middleware import EXCEPTION_RESPONSE_KEY
		request.session[EXCEPTION_RESPONSE_KEY] = HttpResponse(simplejson.dumps(""), mimetype='application/json')
		raise
	return HttpResponse(content=simplejson.dumps(catalogue_material_data_dump), mimetype='application/json')


def loadIndentMaterial(request):
	"""
	Loads IndentMaterial List for a particular Indent specified by the Indent_no in the request.

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	data = json.loads(json.dumps(request_handler.getPostData()))
	supp_id = 0
	is_service = 0
	po_id_condition = ""
	pur_qty_condition = ""
	if "party_id" in data and data["party_id"] != "":
		supp_id = data["party_id"]

	if "is_job" in data and data["is_job"] == '1':
		is_service = 1

	if "po_id" in data and data["po_id"] != "":
		po_id_condition = " AND b.po_no = "+data["po_id"]+""
		pur_qty_condition = " AND m.pid <> "+data["po_id"]+""

	indent_material_query = INDENT_MATERIALS_QUERY % (
		supp_id, is_service, supp_id, is_service, pur_qty_condition, po_id_condition, data["indent_no"], enterprise_id)

	try:
		logger.debug(indent_material_query)
		indent_materials_data_dump = executeQuery(indent_material_query, as_dict=True)
		for material in indent_materials_data_dump:
			material['make_name'] = helper.constructDifferentMakeName(material['make_name'])
			material['scale_factor'] = 1
			if int(material['alternate_unit_id']) != 0:
				scale_factor = helper.getScaleFactor(
					enterprise_id=enterprise_id, item_id=material['item_id'], alternate_unit_id=material['alternate_unit_id'])
				material['quantity'] = material['quantity'] / scale_factor
				material['scale_factor'] = scale_factor if scale_factor else 1
				material['price'] = material['price'] * scale_factor
				material['unit_name'] = helper.getUnitName(enterprise_id=enterprise_id, unit_id=material['alternate_unit_id'])
				material['pur_qty'] = material['pur_qty'] / scale_factor if material['pur_qty'] != 0 else 0
	except Exception:
		logger.info("Loading Indent Material Failed")
		from erp.middleware import EXCEPTION_RESPONSE_KEY
		request.session[EXCEPTION_RESPONSE_KEY] = HttpResponse(simplejson.dumps(""), mimetype='application/json')
		raise
	return HttpResponse(content=simplejson.dumps(indent_materials_data_dump), mimetype='application/json')


def loadIndent(request):
	"""
	Loads the header information about an Indent specified by Indent_no

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	indent_no = request_handler.getPostData("indent_no")
	indent_query = """SELECT b.id, a.purpose, a.sp_instructions, a.purchase_account_id, IFNULL(
			(SELECT COUNT(1) FROM indent_material WHERE indent_no = a.indent_no), 0) AS is_stockable, a.expected_date
		FROM indents AS a JOIN projects as b ON a.project_code= b.id AND b.enterprise_id=a.enterprise_id
		WHERE indent_no='%s' and a.enterprise_id='%s'""" % (indent_no, enterprise_id)
	try:
		indents_data_dump = dao.executeQuery(indent_query)
		indent_list = []
		for item in indents_data_dump:
			temp_list = list(item)
			date_time_obj = datetime.strptime(str(item[5]), '%Y-%m-%d %H:%M:%S')
			temp_list[5] = str(date_time_obj)
			indent_list.append(temp_list)
	except Exception:
		logger.info("Loading Indent Failed...")
		from erp.middleware import EXCEPTION_RESPONSE_KEY
		request.session[EXCEPTION_RESPONSE_KEY] = HttpResponse(simplejson.dumps(""), mimetype='application/json')
		raise
	return HttpResponse(content=simplejson.dumps(indent_list), mimetype='application/json')


def checkIndentPo(request):
	"""
	Loads the header information about an Indent specified by Indent_no

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	indent_no = request_handler.getPostData("indent_no")
	indent_query = "SELECT count(1) FROM purchase_order WHERE indent_no='%s' and enterprise_id='%s'" % (
		indent_no, enterprise_id)
	try:
		po_counts = dao.executeQuery(indent_query)
	except Exception:
		logger.info("Indent validation for PO Failed...")
		from erp.middleware import EXCEPTION_RESPONSE_KEY
		request.session[EXCEPTION_RESPONSE_KEY] = HttpResponse(simplejson.dumps(0), mimetype='application/json')
		raise
	return HttpResponse(content=simplejson.dumps(po_counts), mimetype='application/json')


def checkIndentPoMaterial(request):
	"""
	Loads the header information about an Indent specified by Indent_no

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	indent_no = request_handler.getPostData("indent_no")
	item_id = request_handler.getPostData("item_id")
	make_id = request_handler.getPostData("make_id")
	indent_query = """SELECT IFNULL(SUM(b.pur_qty),0) FROM purchase_order AS a, purchase_order_material AS b
						WHERE b.pid=a.id AND b.item_id='%s' AND a.indent_no='%s' AND a.enterprise_id='%s' 
						AND b.make_id ='%s' AND a.status <> 3 """ % (item_id, indent_no, enterprise_id, make_id)
	try:
		po_counts = dao.executeQuery(indent_query)
	except Exception:
		logger.info("Indent validation for PO Failed...")
		from erp.middleware import EXCEPTION_RESPONSE_KEY
		request.session[EXCEPTION_RESPONSE_KEY] = HttpResponse(simplejson.dumps(0), mimetype='application/json')
		raise
	return HttpResponse(content=simplejson.dumps(po_counts), mimetype='application/json')


def populateIndentTypeChoices(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	logger.info("Populating indent types for enterprise ID - %s" % enterprise_id)
	type_choices = generateIndentTypeChoices(enterprise_id=enterprise_id)
	return HttpResponse(content=simplejson.dumps(type_choices), mimetype="application/json")


def loadSupplierList(request):
	"""
	Loads All Material's primary information.

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	indent_no = request_handler.getPostData('indent_no')
	is_job = request_handler.getPostData('isjpo')
	mat_wise_supp_list = []
	try:
		logger.info("Fetching Suppliers for indent %s of enterprise %s" % (indent_no, enterprise_id))
		suppliers = SQLASession().query(
			IndentMaterial.indent_no, Party.id, Party.name, Party.code
		).join(MaterialSupplierProfile, and_(
			MaterialSupplierProfile.item_id == IndentMaterial.item_id,
			MaterialSupplierProfile.enterprise_id == IndentMaterial.enterprise_id)
			).join(Party, and_(
				Party.id == MaterialSupplierProfile.supp_id, Party.enterprise_id == MaterialSupplierProfile.enterprise_id)
		).filter(
			IndentMaterial.indent_no == indent_no, IndentMaterial.enterprise_id == enterprise_id,
			IndentMaterial.make_id == MaterialSupplierProfile.make_id,
			MaterialSupplierProfile.status == MaterialSupplierProfile.APPROVED,
			MaterialSupplierProfile.effect_since <= date.today(),
			or_(MaterialSupplierProfile.effect_till.is_(None), MaterialSupplierProfile.effect_till >= date.today()),
			MaterialSupplierProfile.is_service.is_(is_job == 'true')
		).group_by(Party.id)
		if len(suppliers.all()) > 0:
			supplier_ids_in_indent = ""
			for supplier in suppliers.all():
				name = supplier.name + " (" + supplier.code + ")"
				mat_wise_supp_list.append((supplier.id, name))
				if supplier_ids_in_indent == "":
					supplier_ids_in_indent = "%s" % supplier.id
				else:
					supplier_ids_in_indent = "%s, %s" % (supplier_ids_in_indent, supplier.id)

			other_suppliers_list = dao.executeQuery("""
				SELECT party_id, concat(party_name, ' (', party_code, ')') as name FROM party_master
				WHERE enterprise_id=%s and party_id not in (%s) 
				ORDER BY party_name""" % (enterprise_id, supplier_ids_in_indent))
			splited_supplier_list = [mat_wise_supp_list, other_suppliers_list]
		else:
			other_suppliers_list = dao.executeQuery("""
							SELECT party_id, concat(party_name, ' (', party_code, ')') as name FROM party_master
							WHERE enterprise_id=%s ORDER BY party_name""" % enterprise_id)
			splited_supplier_list = [[], other_suppliers_list]
	except Exception as e:
		logger.exception(e)
		splited_supplier_list = []
	return HttpResponse(content=simplejson.dumps(splited_supplier_list), mimetype='application/json')


def getTagDetails(request):
	"""
	Fetch tag details.

	:param request:
	:return:	"""
	request_handler = RequestHandler(request)
	tag_list = request.POST.getlist('tag_list[]')
	tag_list = [str(r) for r in tag_list]
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	tags = []
	try:
		# TODO avoid query in loop
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
		cur = conn.cursor()
		for tag_name in tag_list:
			tag_query = "SELECT id,tag FROM tags where enterprise_id = '%s' and tag = '%s'" % (enterprise_id, tag_name)
			cur.execute(tag_query)
			tag_data_dump = cur.fetchall()
			tags.append({'tag_name': tag_name, 'tag_id': tag_data_dump[0][0]})
		conn.close()
	except Exception as e:
		logger.exception('Error : %s' % e)
		tags = ""
	return HttpResponse(content=simplejson.dumps(tags), mimetype='application/json')


def loadMaterialPricedSuppliers(request):
	"""
	Load Material Priced Suppliers.

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	is_job = 'false'
	indent_no = int(str(request_handler.getPostData('indent_no')))
	item_id = request_handler.getPostData('item_id')
	make_id = request_handler.getPostData('make_id')
	party_id = ""
	po_type = ""
	if request_handler.getPostData('party_id') != 'no-supplier':
		if str(request_handler.getPostData('type')) == 'JO':
			po_type = 1
		else:
			po_type = 0
		party_id = int(str(request_handler.getPostData('party_id')))
	effect_date = datetime.today()
	try:
		suppliers = SQLASession().query(
			IndentMaterial.indent_no, MaterialSupplierProfile.supp_id.label('id'), Party.name, Party.currency, MaterialSupplierProfile.price,
			MaterialSupplierProfile.is_service, Party.code, MaterialSupplierProfile.effect_since
		).join(MaterialSupplierProfile, and_(
			MaterialSupplierProfile.item_id == IndentMaterial.item_id,
			MaterialSupplierProfile.enterprise_id == IndentMaterial.enterprise_id)
			).join(
			Party, and_(
				Party.id == MaterialSupplierProfile.supp_id, Party.enterprise_id == MaterialSupplierProfile.enterprise_id)
		).filter(
			IndentMaterial.indent_no == indent_no, IndentMaterial.enterprise_id == enterprise_id,
			IndentMaterial.make_id == MaterialSupplierProfile.make_id, IndentMaterial.item_id == item_id,
			MaterialSupplierProfile.status == MaterialSupplierProfile.APPROVED,
			MaterialSupplierProfile.make_id == make_id,
			MaterialSupplierProfile.effect_since <= effect_date,
			or_(MaterialSupplierProfile.effect_till.is_(None), MaterialSupplierProfile.effect_till >= effect_date),
			MaterialSupplierProfile.is_service.is_(is_job == 'true')
		).group_by(MaterialSupplierProfile.supp_id, MaterialSupplierProfile.effect_since).order_by(
			MaterialSupplierProfile.effect_since.desc(), MaterialSupplierProfile.price.asc())

		job_suppliers = SQLASession().query(
			IndentMaterial.indent_no, Party.id, Party.name, Party.currency, MaterialSupplierProfile.price,
			MaterialSupplierProfile.is_service, Party.code
		).join(MaterialSupplierProfile, and_(
			MaterialSupplierProfile.item_id == IndentMaterial.item_id,
			MaterialSupplierProfile.enterprise_id == IndentMaterial.enterprise_id)
				).join(
			Party, and_(
				Party.id == MaterialSupplierProfile.supp_id, Party.enterprise_id == MaterialSupplierProfile.enterprise_id)
			).filter(
			IndentMaterial.indent_no == indent_no, IndentMaterial.enterprise_id == enterprise_id,
			IndentMaterial.make_id == MaterialSupplierProfile.make_id, IndentMaterial.item_id == item_id,
			MaterialSupplierProfile.status == MaterialSupplierProfile.APPROVED,
			MaterialSupplierProfile.make_id == make_id,
			MaterialSupplierProfile.effect_since <= effect_date,
			or_(MaterialSupplierProfile.effect_till.is_(None), MaterialSupplierProfile.effect_till >= effect_date),
			MaterialSupplierProfile.is_service.is_('true' == 'true')
		).group_by(Party.id)

		suppliers_list = [item for item in job_suppliers] + [item for item in suppliers]
		suppliers_to_be_added = set()
		for item in suppliers_list:
			suppliers_to_be_added.add("%s-%s" % (item[1], item[5]))
		po_jo_suppliers = []
		for item in suppliers_list:
			if "%s-%s" % (item[1], item[5]) in suppliers_to_be_added:
				po_jo_suppliers.append({
					'supp_id': item[1], 'supplier_name': item[2], 'supplier_code': item[6], 'supp_mat_price': item[4],
					'is_service': item[5], 'supplier_currency': item[3]})
				suppliers_to_be_added.remove("%s-%s" % (item[1], item[5]))

		for item in po_jo_suppliers:
			if item['supp_id'] == party_id and item['is_service'] == po_type:
				po_jo_suppliers.remove(item)
		all_suppliers = dao.executeQuery(
			"""SELECT party_id, party_name, party_code FROM party_master WHERE enterprise_id='%s' 
				ORDER BY party_name""" % enterprise_id)
		other_suppliers_list = [supplier for supplier in all_suppliers]
		supplier_id_list = [item['supp_id'] for item in po_jo_suppliers]
		other_suppliers = []
		for supplier_id in supplier_id_list:
			for supplier in other_suppliers_list:
				if supplier[0] == supplier_id:
					other_suppliers.append(supplier)
					other_suppliers_list.remove(supplier)
		supp_list = [po_jo_suppliers, all_suppliers, other_suppliers_list]
	except Exception as e:
		logger.exception(e)
		supp_list = []
	return HttpResponse(content=simplejson.dumps(supp_list), mimetype='application/json')


def loadPO(request):
	try:
		request_handler = RequestHandler(request)
		indent_no = int(str(request_handler.getPostData('indent_no')))
		supplier_id = str(request_handler.getPostData('supplier_id'))
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		available_supplier_list = request.POST.getlist('available_supplier_list[]')
		suppliers = StoresService().getIndentSuppliers(
			indent_no=indent_no, enterprise_id=enterprise_id, is_job='false', date=datetime.today())
		job_suppliers = StoresService().getIndentSuppliers(
			indent_no=indent_no, enterprise_id=enterprise_id, is_job='true', date=datetime.today())
		all_suppliers = job_suppliers.all() + suppliers.all()
		supplier_id_list = []
		condition = ""
		for supplier in list(set(all_suppliers)):
			supplier_id_list.append(supplier.id)

		supplier_id_list = [str(_id) for _id in supplier_id_list]
		if supplier_id == 'others_drafts':
			other_supplier_str = ""
			for party_id in available_supplier_list:
				other_supplier_str = other_supplier_str + party_id + ','
			condition = " i.indent_no = '%s' and s.party_id not in %s and t.enterprise_id = '%s'" % (
				indent_no, '(' + other_supplier_str.rstrip(',') + ')', enterprise_id)

		if supplier_id == 'no-supplier':
			if supplier_id_list:
				supplier_list_str = ""
				for party_id in available_supplier_list:
					supplier_list_str = supplier_list_str + party_id + ','
					condition = " i.indent_no = '%s' and s.party_id not in %s and t.enterprise_id = '%s'" % (
						indent_no, '(' + supplier_list_str.rstrip(',') + ')', enterprise_id)
			else:
				condition = " i.indent_no = '%s' and t.enterprise_id = '%s'" % (indent_no, enterprise_id)
		elif supplier_id != 'others_drafts':
			condition = " i.indent_no = '%s' and s.party_id = '%s' and t.enterprise_id = '%s'" % (
				indent_no, supplier_id, enterprise_id)
		po_query = """SELECT t.id,DATE_FORMAT(t.order_date,'%%d-%%m-%%Y') AS tDate, concat(t.orderno, IFNULL(t.sub_number, '')), 
				DATE_FORMAT(t.approved_on,'%%d-%%m-%%Y') AS poDate,s.party_name, t.total, p.name, t.indent_no, t.status, 
				e.code, t.financial_year, concat(LPAD(i.indent_id, 6, 0), IFNULL(i.sub_number, '')), i.financial_year,t.type 
			FROM purchase_order t JOIN projects p ON t.project_code=p.id AND t.enterprise_id=p.enterprise_id 
				JOIN party_master s ON s.party_id=t.supplier_id AND s.enterprise_id=t.enterprise_id 
				JOIN enterprise e ON t.enterprise_id=e.id 
				LEFT JOIN indents as i ON i.indent_no=t.indent_no AND t.enterprise_id=i.enterprise_id 
			WHERE %s GROUP BY t.id ORDER by t.order_date, t.id""" % condition
		po_list = dao.executeQuery(po_query)
	except Exception as e:
		logger.exception('Exception has occurred while loading PO' % e.message)
		po_list = []
	return HttpResponse(content=simplejson.dumps(po_list), mimetype='application/json')


def loadStockQty(request):
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		indent_no = request_handler.getPostData("indent_no")
		store_service_dao = StoresService().stores_dao
		store_service = StoresService()
		indent_materials = store_service_dao.db_session.query(IndentMaterial).filter(
			IndentMaterial.enterprise_id == enterprise_id, IndentMaterial.indent_no == indent_no).all()
		materials = []
		for material in indent_materials:
			# stock_qty = store_service.getClosingStock(
			# 	enterprise_id=material.enterprise_id, item_id=material.item_id, is_faulty=0, till=datetime.now())
			stock_qty = 0
			stock_details = store_service.getClosingStock(
				mat_list=[int(material.item_id)], enterprise_id=enterprise_id, is_faulty=0)
			for item in stock_details:
				stock_qty = item['closing_qty']
			if material.alternate_unit_id:
				scale_factor = helper.getScaleFactor(
					enterprise_id=material.enterprise_id, item_id=material.item_id, alternate_unit_id=material.alternate_unit_id)
				stock_qty = Decimal(stock_qty) / Decimal(scale_factor) if stock_qty != 0 else 0

			materials_dict = {"item_id": material.item_id, "make_id": material.make_id, "stock_qty": stock_qty}
			materials.append(materials_dict)

	except Exception as e:
		logger.info("Load Stock Qty Failed...%s" % e.message)
	return HttpResponse(content=simplejson.dumps(materials), mimetype='application/json')


def loadReceivedQty(request):
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		indent_no = request_handler.getPostData("indent_no")
		store_service = StoresService().stores_dao
		indent_materials = store_service.db_session.query(IndentMaterial).filter(
			IndentMaterial.enterprise_id == enterprise_id, IndentMaterial.indent_no == indent_no).all()
		materials = []
		for material in indent_materials:
			mat_received_qty_query = """SELECT IFNULL(sum(gm.acc_qty),0) as grn_qty 
										FROM indent_po_pending_views AS pom
										LEFT JOIN			
											grn_material gm ON pom.po_id = gm.po_no
												AND gm.enterprise_id = pom.enterprise_id
												AND gm.item_id = pom.item_id
												AND gm.make_id = pom.make_id
												AND gm.is_faulty = pom.is_faulty
												AND gm.rec_grn_id IS NULL
												AND gm.dc_id IS NULL
											JOIN grn g ON g.grn_no = gm.grnNumber 
												AND g.status > -1					
										WHERE pom.indent_no = {indent_no} AND			    
											pom.enterprise_id = {enterprise_id}  AND 
											pom.item_id = {item_id} AND 
											pom.make_id= {make_id} GROUP BY  
											pom.indent_code, pom.item_id , pom.make_id , pom.is_faulty """.format(
				indent_no=material.indent_no, enterprise_id=material.enterprise_id, item_id=material.item_id,
				make_id=material.make_id)
			received_qty = executeQuery(mat_received_qty_query)
			grn_qty = 0
			if len(received_qty) > 0:
				grn_qty = received_qty[0][0]
			if material.alternate_unit_id:
				scale_factor = helper.getScaleFactor(
					enterprise_id=material.enterprise_id, item_id=material.item_id, alternate_unit_id=material.alternate_unit_id)
				if scale_factor:
					grn_qty = Decimal(grn_qty) / Decimal(scale_factor) if grn_qty != 0 else 0
			materials_dict = {"item_id": material.item_id, "make_id": material.make_id, "grn_qty": grn_qty}
			materials.append(materials_dict)
	except Exception as e:
		materials = []
		logger.info("Load Received Qty Failed...%s" % e.message)
	return HttpResponse(content=simplejson.dumps(materials), mimetype='application/json')
