'use strict';

class MaterialName extends React.Component{
/*
 This class used to manage material input component
*/
  constructor(props){
    super(props);
    this.state = { value: ''}
    this.component = document.getElementById('id_material_name-react_component');
    this.edit = this.component.getAttribute('allowEdit');
  }
  handleBlur(e){
    // Allow only alphanumeric characters
    return window.validateStringOnBlur(e.target, e.nativeEvent, "alphaSpecialChar");
  }
  handleKeyPress(e){
    // Allow only alphaSpecialChar
    return window.validateStringOnKeyPress(e.target, e.nativeEvent, "alphaSpecialChar");
  }
  isValidEdit(){
    return (sessionStorage.getItem('mdata') != null ? (JSON.parse(sessionStorage.getItem('mdata')).response_code == 200 && $('#id_material-material_id').val() != ''? true : false) : false)
  }
  componentDidMount(){
      if(this.edit == 'true')
      {
        if(this.isValidEdit())
        {
            let result = JSON.parse(sessionStorage.getItem('mdata'));
            this.setState({ value: result.name });
        }else{
            if($('#id_material-material_id').val() != '')
            {
                var promise = fetchMaterialData();
                promise.then(function(result) {
                   this.setState({ value: result.name });
                }.bind(this));
            }
        }
      }
  }
  render(){
    return(
        <div className="form-group">
            <label>Item Name<span className="mandatory_mark"> *</span></label>
            <input
              className="header-textbox form-control"
              id="id_material-name"
              maxLength="200"
              name="material-name"
              onBlur={this.handleBlur.bind(this)}
              onKeyPress={this.handleKeyPress.bind(this)}
              placeholder="Enter Item Name"
              type="text" defaultValue={this.state.value} />
        </div>
    );
  }
}

class DrawingNo extends React.Component{
    constructor(props){
      super(props);
      this.state = { value: ''}
      this.component = document.getElementById('id_material_drawingno-react_component');
      this.edit = this.component.getAttribute('allowEdit');
    }
  handleBlur(e){
    window.constructDrawingNo();
    return window.validateStringOnBlur(e.target, e.nativeEvent, "alphaSpecialChar");
  }
  handleKeyPress(e){
    return window.validateStringOnKeyPress(e.target, e.nativeEvent, "alphaSpecialChar");
  }
  isValidEdit(){
    return (sessionStorage.getItem('mdata') != null ? (JSON.parse(sessionStorage.getItem('mdata')).response_code == 200 && $('#id_material-material_id').val() != ''? true : false) : false)
  }
  componentDidMount(){
    if(this.edit == 'true')
      {
        if(this.isValidEdit())
        {
            let result = JSON.parse(sessionStorage.getItem('mdata'));
            this.setState({ value: result.drawing_no });
        }else{
            if($('#id_material-material_id').val() != '')
            {
                var promise = fetchMaterialData();
                promise.then(function(result) {
                   this.setState({ value: result.drawing_no });
                }.bind(this));
            }
        }
      }
  }
  render(){
    return(
      <input
        className="form-control"
        id="id_material-drawing_no"
        maxLength="20"
        name="material-drawing_no"
        onBlur={this.handleBlur.bind(this)}
        onKeyPress={this.handleKeyPress.bind(this)}
        placeholder={$("#material_type").val() == "service"?"Enter Item Code":"Enter Drawing Number"}
        type="text" defaultValue={this.state.value} />
    );
  }
}
class CategoryId extends React.Component{
    render(){
        return(
        <input id="id_material-category_id" name="material-category_id" type="hidden" />
        );
    }
}
class CategoryList extends React.Component{
    constructor()
    {
        super();
        this.state = {
            options: [],
            selected: "",
            validationError: ""
        }
        this.values = '';
        this.component = document.getElementById('id_material_category-react_component');
        this.edit = this.component.getAttribute('allowEdit');
    }
    isValidEdit()
    {
        return (sessionStorage.getItem('mdata') != null ? (JSON.parse(sessionStorage.getItem('mdata')).response_code == 200 && $('#id_material-material_id').val() != ''? true : false) : false)
    }
    handleOnClick(e){
      $("#materialcategory").modal('show');
    }
    componentDidMount()
    {
        this.$el = $(this.el);
        if ($("#is_access_level_edit").val() != 1){
          $("#addCatergoryLink").remove();
        }
        const csrfToken = getCookie('csrftoken');
        const requestOptions = {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', 'X-CSRFToken': csrfToken},
            body: JSON.stringify({ title: 'React POST Request Example', is_service: $("#id_material-is_service").is(":checked") })
        };
        fetch('/erp/masters/json/materials/populate_categories/', requestOptions)
          .then(response => {
            return response.json();
          })
          .then(data => {
            let teamsFromApi = data.map(option => {
              return { value: option.category_id, display: option.label };
            });
            this.setState({
              options: [
                {
                  value: "",
                  display:
                    "- Select Category -"
                }
              ].concat(teamsFromApi)
            });
            this.$el.chosen();
          })
          .then(set => {
              if(this.edit == 'true')
              {
                  if(this.isValidEdit())
                  {
                      let result = JSON.parse(sessionStorage.getItem('mdata'));
                      this.$el.context.value = result.category_id;
                      this.$el.trigger("chosen:updated");
                  }
              }
          })
          .catch(error => {
            console.log(error);
          });
  }
  componentWillUnmount()
  {
    this.$el.off('change', this.handleChange);
    this.$el.chosen('destroy');
  }
  render()
  {
    return (
        <div className="form-group col-sm-6 remove-padding-right">
            <label>Category<span className="mandatory_mark"> *</span></label>
            <a className="pull-right" id="addCatergoryLink" onClick={this.handleOnClick.bind(this)} role="button">+ Add New</a>
            <select
                id="id_material-category_select"
                className="form-control chosen-dropdown"
                name="material-category_id"
                ref={el => this.el = el}
                defaultValue={this.state.selected}
            >
            {this.state.options.map(option => (
                <option
                  key={option.value}
                  value={option.value}
                >
                  {option.display}
                </option>
            ))}
            </select>
            <div
              style={{
                color: "red"
              }}
            >
              {this.state.validationError}
            </div>
        </div>
    );
  }

}

class Description extends React.Component{
   constructor(){
    super();
    this.state = { value: ''}
    this.component = document.getElementById('id_material_description-react_component');
    this.edit = this.component.getAttribute('allowEdit');
  }
  handleBlur(e){
    return window.validateStringOnBlur(e.target, e.nativeEvent, "alphaSpecialChar");
  }
  handleKeyPress(e){
    return window.validateStringOnKeyPress(e.target, e.nativeEvent, "alphaSpecialChar");
  }
  isValidEdit(){
    return (sessionStorage.getItem('mdata') != null ? (JSON.parse(sessionStorage.getItem('mdata')).response_code == 200 && $('#id_material-material_id').val() != ''? true : false) : false)
  }
  componentDidMount(){
    if(this.edit == 'true')
      {
        if(this.isValidEdit())
        {
            let result = JSON.parse(sessionStorage.getItem('mdata'));
            this.setState({ value: result.description });
        }else{
            if($('#id_material-material_id').val() != '')
            {
                var promise = fetchMaterialData();
                promise.then(function(result) {
                   this.setState({ value: result.description });
                }.bind(this));
            }
        }
      }
  }
  render(){
    return(
      <div className="form-group">

        <label>Description</label>
        <textarea
          className="form-control"
          cols="40"
          id="id_material-description"
          maxLength="2500"
          name="material-description"
          onBlur={this.handleBlur.bind(this)}
          onKeyPress={this.handleKeyPress.bind(this)}
          rows="5" defaultValue = {this.state.value}>
        </textarea>

      </div>
    );
  }
}
/*
class MakeFormSet extends React.Component{
  handleBlur(e){
    return window.validateStringOnBlur(e.target, e.nativeEvent, "alphaSpecialChar");
  }
  handleKeyPress(e){
    return window.validateStringOnKeyPress(e.target, e.nativeEvent, "alphaSpecialChar");
  }
  render(){
    const divStyle ={
      color: '#999',
      textTransform: 'initial',
      float: 'right',
      right: 15,
      letterSpacing: '0.5px',
      fontSize: 11
    };
    return(
      <div className="form-group">
        <label style={{width: '100%'}}>
          Makes <small style={divStyle}>
          Type and press enter to add new make
        </small>
      </label>
      <input
        id="id_material_make-TOTAL_FORMS"
        name="material_make-TOTAL_FORMS"
        type="hidden"
        value="0" />

      <input
        id="id_material_make-INITIAL_FORMS"
        name="material_make-INITIAL_FORMS"
        type="hidden"
        value="0" />

      <input
        id="id_material_make-MAX_NUM_FORMS"
        name="material_make-MAX_NUM_FORMS"
        type="hidden"
        value="1000" />
      <div id="material_make-__prefix__">

        <div className="col-sm-12" >

          <input
            className="form-control ui-autocomplete-input"
            id="id_material_make-__prefix__-make"
            maxLength="40"
            name="material_make-__prefix__-make"
            placeholder="Type your makes here"
            type="text"
            autoComplete="off" />

          <span
            role="status"
            aria-live="polite"
            className="ui-helper-hidden-accessible">
          </span>

          <span hidden="hidden">
            <input
              id="id_material_make-__prefix__-DELETE"
              name="material_make-__prefix__-DELETE"
              type="checkbox" />
          </span>

          <span hidden="hidden">
            <input
              id="id_material_make-__prefix__-make_id"
              name="material_make-__prefix__-make_id"
              type="hidden" />
          </span>

        </div>

        <div className="col-sm-2 hide">

          <input
            id="add_make"
            className="btn btn-save"
            value="+"
            type="button" />

        </div>

      </div>
    </div>
  );
}
}
*/
class TariffNo extends React.Component{
  constructor(){
    super();
    this.state = {value:''};
    this.component = document.getElementById('id_material_tariffno-react_component');
    this.edit = this.component.getAttribute('allowEdit');
  }
  handleBlur(e){
    return window.validateStringOnBlur(e.target, e.nativeEvent, "alphaSpecialChar");
  }
  handleKeyPress(e){
    return window.validateStringOnKeyPress(e.target, e.nativeEvent, "alphaSpecialChar");
  }
  handleHsnKeyPress(e){
    return window.validateStringOnKeyPress(e.target, e.nativeEvent, "hsn_specialChar");
  }
  isValidEdit(){
    return (sessionStorage.getItem('mdata') != null ? (JSON.parse(sessionStorage.getItem('mdata')).response_code == 200 && $('#id_material-material_id').val() != ''? true : false) : false)
  }
  componentDidMount(){
    if(this.edit == 'true')
      {
        if(this.isValidEdit())
        {
            let result = JSON.parse(sessionStorage.getItem('mdata'));
            this.setState({ value: result.tariff_no });
        }else{
            if($('#id_material-material_id').val() != ''){
                var promise = fetchMaterialData();
                promise.then(function(result) {
                   this.setState({ value: result.tariff_no });
                }.bind(this));
            }
        }
      }
  }
  render(){
    return(
      <div
        className="form-group col-sm-6 remove-padding"
        id="tariff_no">
        <label>{$("#material_type").val() == "service"?"SAC":"HSN"}</label>
        <input
          className="form-control"
          id="id_material-tariff_no"
          maxLength="9"
          name="material-tariff_no"
          onBlur={this.handleBlur.bind(this)}
          onKeyPress={this.handleHsnKeyPress.bind(this)}
          placeholder="Enter Tariff No"
          type="text"  defaultValue = {this.state.value} />

      </div>
    );
  }
}
class Price extends React.Component{
  constructor(){
      super();
      this.state = {value: ''};
      this.component = document.getElementById('id_material_price-react_component');
      this.edit = this.component.getAttribute('allowEdit');
  }
  
  handleFocus(e){
    return window.setNumberRangeOnFocus(e.target,13 ,5)
  }
  isValidEdit(){
    return (sessionStorage.getItem('mdata') != null ? (JSON.parse(sessionStorage.getItem('mdata')).response_code == 200 && $('#id_material-material_id').val() != ''? true : false) : false)
  }
  componentWillMount(){
    if($('#id_material-material_id').val() == '')
      {
        this.setState({ value: '0.00' });
      }
  }
  componentDidMount(){
     if(this.edit == 'true')
      {
        if(this.isValidEdit())
        {
            let result = JSON.parse(sessionStorage.getItem('mdata'));
            this.setState({ value: result.price });
        }else{
            if($('#id_material-material_id').val() != ''){
                var promise = fetchMaterialData();
                promise.then(function(result) {
                   this.setState({ value: result.price });
                }.bind(this));
            }
        }
      }
  }
  render(){
    return(
      <div className="form-group col-sm-6 remove-padding-right">

        <label>Value<span className="mandatory_mark"> *</span></label>
        <input
          className="form-control"
          id="id_material-price"
          maxLength="19"
          name="material-price"
          onFocus={this.handleFocus.bind(this)}
          placeholder="Enter Value"
          type="text"
          defaultValue={this.state.value} />
        <input
          type="hidden"
          id="original_price"
          defaultValue={this.state.value} />

      </div>
    );
  }
}
class UnitList extends React.Component {
  constructor(){
    super();
    this.state = {
      options: []
    }
    this.values = '';
    this.component = document.getElementById('id_material_unit-react_component');
    this.edit = this.component.getAttribute('allowEdit');
    this.alternate_unit_count = document.getElementById('alternate_unit_count').value;
    this.is_multiple_units = document.getElementById('is_multiple_units').value;
  }
  isValidEdit(){
    return (sessionStorage.getItem('mdata') != null ? (JSON.parse(sessionStorage.getItem('mdata')).response_code == 200 && $('#id_material-material_id').val() != ''? true : false) : false)
  }
  handleChange(e){
    window.unitChangeEvent();
  }
  handleOnClick(e){
    return window.showAlternateUoms();
  }
  handleUnitOnClick(e){
    $("#add_new_unit_modal").modal("show");
  }
  componentDidMount(){
    this.$el = $(this.el);
    const csrfToken = getCookie('csrftoken');
    const requestOptions = {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'X-CSRFToken': csrfToken},
        body: JSON.stringify({ title: 'React POST Request Example' })
        };
    fetch('/erp/masters/json/materials/populate_unit_choices/', requestOptions)
          .then(response => {
            return response.json();
          })
          .then(data => {
            let teamsFromApi = data.map(option => {
              return { 0: option[0], 1: option[1] };
            });
            this.setState({
              options: [].concat(teamsFromApi)
            });
            this.$el.chosen();
          })
          .then(set => {
              if(this.edit == 'true')
              {
                  if(this.isValidEdit())
                  {
                      let result = JSON.parse(sessionStorage.getItem('mdata'));
                      console.log(result);
                      this.$el.context.value = result.unit_id;
                      this.$el.trigger("chosen:updated");
                      document.getElementById('id_material-current_unit').value = result.unit_id;
                  }
              }
          })
          .catch(error => {
            console.log(error);
          });

  }
  componentWillUnmount()
  {
    this.$el.off('change', this.handleChange);
    this.$el.chosen('destroy');
  }
  render(){
    return (
      <div className="form-group col-sm-12 remove-padding">
        <label>Unit<span className="mandatory_mark"> *</span></label>
        <a className="pull-right" id="add-new-unit" onClick={this.handleUnitOnClick.bind(this)} role="button">+ Add New</a>
        <select
          className="form-control unit_box chosen-dropdown"
          id="id_material-unit_id"
          name="material-unit_id"
          onChange={this.handleChange.bind(this)}
          ref={el => this.el = el}
          placeholder="Select Units">
          {this.state.options.map((option) =>
            <option key={option[0]} value={option[0]}>
              {option[1]}
            </option>
          )}
        </select>
        <a id="alternateUoms" style={{display: this.is_multiple_units == 'True' ? 'block' : 'none' }} onClick={this.handleOnClick.bind(this)} role="button"><i className="fa fa-list" aria-hidden="true"></i> Alternate UoMs <span className="alternate-unit-count bracket-enclosed">{this.alternate_unit_count}</span></a>
        <input
          id="id_material-current_unit"
          name="material-current_unit"
          type="hidden" defaultValue="" />
      </div>
    );
  }
}
class Msl extends React.Component{
  constructor(){
    super();
    this.state = { value: '' };
    this.component = document.getElementById('id_material_msl-react_component');
    this.edit = this.component.getAttribute('allowEdit');
    this.is_negative_stock_allowed = document.getElementById('is_negative_stock_allowed').value;
  }
  handleFocus(e){
    return (this.is_negative_stock_allowed == 'True'? window.setNumberRangeOnFocus(e.target,12,3,false, true) : window.setNumberRangeOnFocus(e.target,12,3));
  }
  isValidEdit(){
    return (sessionStorage.getItem('mdata') != null ? (JSON.parse(sessionStorage.getItem('mdata')).response_code == 200 && $('#id_material-material_id').val() != ''? true : false) : false)
  }
  componentWillMount(){
    if($('#id_material-material_id').val() == '')
      {
        this.setState({ value: '0.00' });
      }
  }
  componentDidMount(){
     if(this.edit == 'true')
      {
        if(this.isValidEdit())
        {
            let result = JSON.parse(sessionStorage.getItem('mdata'));
            this.setState({ value: result.msl });
        }else{
            if($('#id_material-material_id').val() != ''){
                var promise = fetchMaterialData();
                promise.then(function(result) {
                   this.setState({ value: result.msl });
                }.bind(this));
            }
        }
      }
  }
  render(){
    return(
      <div style={{ padding : "0px" }} className="form-group col-sm-6 remove-padding-right for_goods" >

        <label>Min Stock Level (MSL)<span className="mandatory_mark"> *</span></label>
        <input
          autoComplete="off"
          className="form-control"
          id="id_material-minimum_stock_level"
          maxLength="16"
          name="material-minimum_stock_level"
          onFocus={this.handleFocus.bind(this)}
          placeholder="Enter Minimum Stock Level"
          type="text"
          defaultValue={this.state.value} />

      </div>
    );
  }
}
/*
class SampleSize extends React.Component{
  handleFocus(e){
    return window.setNumberRangeOnFocus(e.target,7,3,true);
  }
  render(){
    return(
      <div className="form-group col-sm-6 remove-padding-left">

        <label>
          Sample Size
        </label>

        <input
          className="form-control"
          id="id_material-sample_size"
          maxLength="11"
          name="material-sample_size"
          onFocus={this.handleFocus.bind(this)}
          placeholder="Enter Sample Size"
          type="text"
          defaultValue="0.0" />

      </div>
    );
  }
}
class LotSize extends React.Component{
  handleFocus(e){
    return window.setNumberRangeOnFocus(e.target,7,3,true);
  }
  render(){
    return(
      <div className="form-group col-sm-6 remove-padding-right">

        <label>
          Lot Size
        </label>
        <input
          className="form-control"
          id="id_material-lot_size"
          maxLength="11"
          name="material-lot_size"
          onKeyPress={this.handleFocus.bind(this)}
          placeholder="Enter Lot Size"
          type="text" />
      </div>
    );
  }
}
class Qc extends React.Component{
  handleBlur(e){
    return window.validateStringOnBlur(e.target, e.nativeEvent, "alphaSpecialChar");
  }
  handleKeyPress(e){
    return window.validateStringOnKeyPress(e.target, e.nativeEvent, "alphaSpecialChar");
  }
  render(){
    return(
      <div className="form-group">

        <label>QC Method </label>
        <input
          className="form-control"
          id="id_material-qc_method"
          maxLength="100"
          name="material-qc_method"
          onBlur={this.handleBlur.bind(this)}
          onKeyPress={this.handleKeyPress.bind(this)}
          placeholder="Enter Quality Control Method"
          type="text" />
      </div>
    );
  }
}
class ReactionPlan extends React.Component{
  handleBlur(e){
    return window.validateStringOnBlur(e.target, e.nativeEvent, "alphaSpecialChar");
  }
  handleKeyPress(e){
    return window.validateStringOnKeyPress(e.target, e.nativeEvent, "alphaSpecialChar");
  }
  render(){
    return(
      <div className="form-group">

        <label>
          Reaction Plan
        </label>
        <textarea
          className="form-control"
          cols="40"
          id="id_material-reaction_plan"
          maxLength="450"
          name="material-reaction_plan"
          onBlur={this.handleBlur.bind(this)}
          onKeyPress={this.handleKeyPress.bind(this)}
          placeholder="Enter Reaction plan"
          rows="3">
        </textarea>

      </div>
    );
  }
} */


ReactDOM.render(
  <MaterialName />,
  document.getElementById('id_material_name-react_component')
);
ReactDOM.render(
  <UnitList   />,
  document.getElementById('id_material_unit-react_component')
);
ReactDOM.render(
  <DrawingNo   />,
  document.getElementById('id_material_drawingno-react_component')
);
ReactDOM.render(
  <CategoryList   />,
  document.getElementById('id_material_category-react_component')
);
ReactDOM.render(
  <Description   />,
  document.getElementById('id_material_description-react_component')
);
// ReactDOM.render(
  //  <MakeFormSet   />,
//  document.getElementById('id_material_makeformset-react_component')
// );
ReactDOM.render(
  <TariffNo   />,
  document.getElementById('id_material_tariffno-react_component')
);
ReactDOM.render(
  <Price   />,
  document.getElementById('id_material_price-react_component')
);
ReactDOM.render(
  <Msl   />,
  document.getElementById('id_material_msl-react_component')
);
/*
ReactDOM.render(
  <SampleSize   />,
  document.getElementById('id_material_samplesize-react_component')
);
ReactDOM.render(
  <LotSize   />,
  document.getElementById('id_material_lotsize-react_component')
);
ReactDOM.render(
  <Qc   />,
  document.getElementById('id_material_qc-react_component')
);
ReactDOM.render(
  <ReactionPlan   />,
  document.getElementById('id_material_reactionplan-react_component')
);
*/


function unitChangeEvent(){
    if($('#id_material-unit_id').val() == 0) {
        $("#add_new_unit_modal").modal("show");
    }
    else {
        if ($('#id_material-current_unit').val() != 'None' && ($('#id_material-unit_id').val() != $('#id_material-current_unit').val()) ) {
            $('#conversion_rate').removeClass("hide")
        } else {
            $('#id_material-unit_conversion_rate').val('1.0');
            $('#conversion_rate').addClass("hide")
        }
     }
}

$('#id_material-unit_id').blur(function () {
    if ($('#id_material-current_unit').val() != 'None' && ($('#id_material-unit_id').val() != $('#id_material-current_unit').val()) ) {
        unitChangeConversion();
    }
});

function unitChangeConversion() {
    if($('#is_multiple_units').val()=="True" && $(".alternate-unit-count").text() > 0){
        var currentUnit = $("#id_material-unit_id").val();
        var conversion_rate = $("#id_material-unit_conversion_rate").val();
        var material_table = document.getElementById("alternate_unit_table");
        var row_count = material_table.rows.length;
        for (i = 1; i < row_count; i++) {
            var row = document.getElementById('alternate_unit-' + parseInt(i-1));
            if ($('#id_alternate_unit-' + parseInt(i-1) + '-alternate_unit_id').val() == currentUnit) {
                document.getElementById('id_alternate_unit-' + parseInt(i-1) + '-DELETE').checked = true;
                row.style.display = 'none';
            }
            else {
                if ($('#id_alternate_unit-' + parseInt(i-1) + '-DELETE').is(':checked'))
                {
                    document.getElementById('id_alternate_unit-' + parseInt(i-1) + '-DELETE').checked = false;
                    row.style.display = 'table-row';
                }
                $('#id_alternate_unit-' + parseInt(i-1) + '-scale_factor').val( parseFloat($('#id_alternate_unit-' + parseInt(i-1) + '-scale_factor_value').val()) * parseFloat(conversion_rate));
                $('#id_alternate_unit-' + parseInt(i-1) + '-primary_unit_name').text($("#id_material-unit_id option:selected").text());
            }
        }
        showAlternateUoms()
    }
 }

function showAlternateUoms() {
    var currentUnit = $("#id_material-unit_id").val();
    $("#id_alternate_unit-__prefix__-alternate_unit_id").find("option").removeAttr("disabled");
    $("#id_alternate_unit-__prefix__-alternate_unit_id").find("option[value='"+currentUnit+"']").attr("disabled", true);
    if($("#id_alternate_unit-__prefix__-alternate_unit_id").find("option[value='0']").length<=0) {
        var row = `<option value='0' selected>--Select Unit--</option>`;
        $("#id_alternate_unit-__prefix__-alternate_unit_id").prepend( row );
    }
    $("#add_new_alternate_unit").modal("show")
    $("#alternate_primary_unit").text($("#id_material-unit_id option:selected").text()).attr("title", $("#id_material-unit_id option:selected").text())
}

function addNewAlternateUnit() {
    $(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();
    var ControlCollections = [
        {
            controltype: 'dropdown',
            controlid: 'id_alternate_unit-__prefix__-alternate_unit_id',
            isrequired: true,
            errormsg: 'Unit Name is required.'
        },
        {
            controltype: 'textbox',
            controlid: 'id_alternate_unit-__prefix__-scale_factor',
            isrequired: true,
            errormsg: 'Scale Factor is required.'
        }
    ];
    var result = JSCustomValidator.JSvalidate(ControlCollections);

    if(result) {
        refreshSessionPerNActions(10);
        var newQtyField = document.getElementById('id_alternate_unit-__prefix__-scale_factor');
        if (newQtyField.value == "" || newQtyField.value == 0) {
            return;
        } else {
            var material_table = document.getElementById("alternate_unit_table");
            var row_count = material_table.rows.length;
            var match = false;
            var make_match = false;

            for (i = 1; i < row_count; i++) {
                if ($('#id_alternate_unit-' + parseInt(i-1) + '-alternate_unit_id').val() == $('#id_alternate_unit-__prefix__-alternate_unit_id').val() && !document.getElementById('id_alternate_unit-' + parseInt(i-1) + '-DELETE').checked) {
                    match = true;
                    swal({
                            title: "Duplicate Units Exists!",
                            text: "Do you still want to replace the Units?",
                            type: "warning",
                            showCancelButton: true,
                            confirmButtonColor: "#209be1",
                            confirmButtonText: "Yes, do it!",
                            closeOnConfirm: true,
                            closeOnCancel: true
                        },
                        function(isConfirm){
                            if (isConfirm) {

                                if (document.getElementById('id_alternate_unit-' + parseInt(i-1) + '-DELETE').checked){
                                    var matched_row = document.getElementById('alternate_unit-' + parseInt(i-1));
                                    document.getElementById('id_alternate_unit-' + parseInt(i-1) + '-scale_factor').value = document.getElementById('id_alternate_unit-__prefix__-scale_factor').value;
                                    document.getElementById('id_alternate_unit-' + parseInt(i-1) + '-DELETE').checked = false;
                                    document.getElementById('id_alternate_unit-' + parseInt(i-1) + '-alternate_unit_id').value = $('#id_alternate_unit-__prefix__-alternate_unit_id').val();
                                    matched_row.style.display = '';
                                    document.getElementById('id_alternate_unit-__prefix__-scale_factor').value = '';
                                    $('#id_alternate_unit-__prefix__-alternate_unit_id').val(0)
                                }
                                else {
                                    document.getElementById('id_alternate_unit-' + parseInt(i-1) + '-scale_factor').value = parseFloat(document.getElementById('id_alternate_unit-__prefix__-scale_factor').value);
                                    document.getElementById('id_alternate_unit-__prefix__-scale_factor').value = '';
                                    $('#id_alternate_unit-__prefix__-alternate_unit_id').val(0)
                                }
                            }
                        });
                    break;
                }
            }
            if (!match) {
                var form_idx = parseInt($('#id_alternate_unit-TOTAL_FORMS').val());
                var new_form = '';
                var new_form_html = '';
                new_form = $('#alternate_unit-__dummy__').html().replace(/__prefix__/g, form_idx);
                new_form_html = `<tr id="alternate_unit-${form_idx}" class='newly_added_item'>${new_form}</tr>`;
                $(new_form_html).insertAfter('#alternate_unit-__dummy__');
                $('#id_alternate_unit-TOTAL_FORMS').val(form_idx + 1);
                copyFromEmptyFormAlternateUnit(form_idx);
                document.getElementById("id_alternate_unit-__prefix__-scale_factor").value = "";
                $('#id_alternate_unit-__prefix__-alternate_unit_id').val(0)
            }
            updateAlternateUnitCount();
        }
    }
}

function removeAlternateUnit(current) {
    $(current).closest(".alternate-indv-unit-container").remove();
}

function updateAlternateUnitCountInit() {
    var count = $("#alternate_unit_table").find("tbody").find("tr").length - 1;
    $(".alternate-unit-count").text(count);
}

function updateAlternateUnitCount() {
    var count = $("#alternate_unit_table").find("tbody").find("tr:visible").length;
    $(".alternate-unit-count").text(count);
}

function constructDrawingNo(){
    setTimeout(function(){
        var catalogue_code = document.getElementById('id_material-catalogue_code');
        catalogue_code.value = document.getElementById('id_material-drawing_no').value.replace(/ /g,"_").toUpperCase();
        $('#id_material-drawing_no').val(catalogue_code.value);
    },10);
}

$("#id_material-unit_conversion_rate").blur(function(){
    setTimeout(function(){
      if($("#id_material-unit_conversion_rate").val() == "" || $("#id_material-unit_conversion_rate").val() <= 0) {
        swal("","Conversion Rate is Required. Conversion Rate should be atleast greater than Zero.", "warning");
        $("#id_material-unit_conversion_rate").val("1.00")
      }
      var convertedValue = parseFloat($("#original_price").val())/parseFloat($("#id_material-unit_conversion_rate").val());
      $("#id_material-price").val(convertedValue.toFixed(2));
    },100);
});

function copyFromEmptyFormAlternateUnit(form_idx) {
    var new_form_alternate_unit_id = document.getElementById('id_alternate_unit-' + form_idx + '-alternate_unit_id');
    var new_form_enterprise_id = document.getElementById('id_alternate_unit-' + form_idx + '-enterprise_id');
    var new_form_scale_factor = document.getElementById('id_alternate_unit-' + form_idx + '-scale_factor');
    var new_form_unit_name = document.getElementById('id_alternate_unit-' + form_idx + '-unit_name');
    var new_form_primary_unit_name = document.getElementById('id_alternate_unit-' + form_idx + '-primary_unit_name');

    new_form_enterprise_id.value = document.getElementById('id_material-enterprise_id').value;
    new_form_alternate_unit_id.value = document.getElementById('id_alternate_unit-__prefix__-alternate_unit_id').value;
    new_form_scale_factor.value = document.getElementById('id_alternate_unit-__prefix__-scale_factor').value;
    new_form_unit_name.value = $("#id_alternate_unit-__prefix__-alternate_unit_id option:selected").text();
    $('#id_alternate_unit-' + form_idx + '-primary_unit_name').text($("#id_material-unit_id option:selected").text());

    document.getElementById('id_alternate_unit-__prefix__-scale_factor').value = '';
}

function isDeleteMaterialAlternateUnit(current, form_prefix, alternate_unit_id) {
    if($(current).closest("tr").find(".td-alternate-unit-name").find("input").length > 0) {
        var itemName = $(current).closest("tr").find(".td-alternate-unit-name").find("input").val();
    }
    else {
        var itemName = $(current).closest("tr").find(".td-alternate-unit-name").text();
    }
    if (alternate_unit_id != "None"){
        $.ajax({
            url: "/erp/masters/json/materials/check_alternate_unit/",
            type: "POST",
            dataType: "json",
            data: {alternate_unit_id: alternate_unit_id },
            success: function (alternate_unit_used_count) {
                if (alternate_unit_used_count > 0) {
                    swal("Unable to Delete", `Alternate Unit <b>[${itemName}]</b> is already used in one or more items!`, "warning");
                }
                else {
                    deleteMaterialAlternateUnit(current, form_prefix, alternate_unit_id, itemName);
                }
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });
    }
    else {
        deleteMaterialAlternateUnit(current, form_prefix, alternate_unit_id, itemName);
    }
}

function deleteMaterialAlternateUnit(current, form_prefix, alternate_unit_id, itemName) {
    var deleteFlag = document.getElementById('id_' + form_prefix + '-DELETE');
    var deleteRow = document.getElementById(form_prefix);
    swal({
      title: "Are you sure?",
      text: `Do you want to delete <b>${itemName}</b>?`,
      type: "warning",
      showCancelButton: true,
      confirmButtonColor: "#209be1",
      confirmButtonText: "Yes, delete it!",
      closeOnConfirm: true
    },
    function(){
        deleteFlag.checked = true;
        deleteRow.style.display = 'none'
        updateAlternateUnitCount();
    });
}

function openMaterialAddModal() {
    $(".chk-inUse").addClass("hide");
    $(".material-save-buttons").addClass('hide');
    $(".material-supporting-container").addClass('hide');
    $(".material-container").css({width: "100%"});
    $("#id_material-is_stocked").prop("checked", true);
    $('#add_material_modal').modal('show');
    $("#id_material_category_select_chosen").find(".chosen-drop").css({right: 0});
    $("#makes, #addCatergoryLink, #add-new-unit, #alternateUoms, .xs-styled-checkbox-inuse").addClass("hide");
    resetMaterialForm();
}

function resetMaterialForm() {
  $("#id_material-name, #id_material-drawing_no, #id_material-tariff_no, #id_material-description").val("");
  $("#id_material-category_select").val("").trigger("chosen:updated");
  $("#id_material-unit_id").val(1).trigger("chosen:updated");
  $("#id_material-price, #id_material-minimum_stock_level, #id_material-standard_packing_qty").val("0.00");
  $("#id_material-is_stocked, #id_material-in_use").prop("checked", true);
  $("#div_is_stocked").find(".checkbox.for_goods").removeClass("hide");
  $("#id_material-is_service").prop("checked", false);
  $("#switch_material_service .goods").removeClass("noActive").addClass("active");
  $("#switch_material_service .service").removeClass("active").addClass("noActive");
}

function addNewMaterial(materialName, materialUnit, materialHsn="", materialPrice=0) {
    $('#id_material-name').val(materialName);
    if(materialUnit == "" || materialUnit == undefined) materialUnit = 1;
    $('#id_material-unit_id').val(materialUnit);
    $("#id_material-tariff_no").val(materialHsn);
    $('#id_material-minimum_stock_level').val(0);
    $('#id_material-standard_packing_qty').val(0);
    $('#id_material-price').val(materialPrice);
    $("#id_material-is_stocked").prop("checked", false);
    $('#id_material-category_select option').each(function(){
        if($(this).text().toLowerCase() == 'others') {
            $('#id_material-category_select').val($(this).val()).trigger("chosen:updated");
            return;
        }
    });
    $(".all_units_select_box").addClass("hide");
    $('#saveCatalogueButton').trigger("click");
}

$('#id_material-unit_id').change(function () {
    if($("#id_material-material_id").val() != "") {
      if ($('#id_material-current_unit').val() != 'None' && ($('#id_material-unit_id').val() != $('#id_material-current_unit').val()) ) {
          $('#conversion_rate').removeClass("hide");
      } else {
          $('#id_material-unit_conversion_rate').val('1.0').blur();
          $('#conversion_rate').addClass("hide");
      }
    }
});