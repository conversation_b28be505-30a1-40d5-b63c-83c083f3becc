{% extends "hr/sidebar.html" %}
{% block paystructure %}
<style>
	li.tax_side_menu a{
	outline: none;
	background-color: #e6983c !important;
	}

	.inline-error-message,
	.external-error-message {
		color: #dd4b39;
	    font-size: 11px;
	    padding: 4px;
	}

	.inline-error-border,
	.external-error-border {
		border: 1px solid #dd4b39 !important;
    	background: #fdeded;
	}
</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">

<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/pay.js?v={{ current_version }}"></script>

<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">Pay Structure</span>
	</div>
	<div class="container-fluid">
		<div class="page-heading_new">
			{% if access_level.edit %}
				<a data-toggle="tab" href="#tab2" class="btn btn-new-item pull-right create_pay_structure" data-tooltip="tooltip" title="Add New Pay Structure"><i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span></a>
			{% else %}
				<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in HR Module. Please contact the administrator.">
					<a class="btn btn-new-item pull-right pay_structure_btn disabled"><i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span></a>
				</div>
			{% endif %}
			<a href="/erp/hr/pay/" class="btn btn-add-new pull-right view_pay_structure hide" data-tooltip="tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
		</div>
		<div class="col-lg-12 remove-padding">
			<div class="content_bg">
				<input type="hidden" id="response_message" value="{{response_message}}"/>
				<div class="contant-container-nav hide">
					<ul class="nav nav-tabs list-inline">
						<li class="active"><a href="/erp/hr/pay">VIEW</a></li>
						<li><a data-toggle="tab" href="#tab2" id="payStructure_add">ADD</a></li>
					</ul>
				</div>
				<div class="tab-content">
					<div id="tab1" class="tab-pane fade in active">
						<div class="col-lg-12">
							<div class="csv_export_button">
			                    <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#payList'), 'Pay_Structure_List.csv']);" data-tooltip="tooltip" title="Download Pay Structure as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
			                </div>
							<table class="table table-bordered table-striped custom-table" id="payList" style="width: 100%;">
								<thead>
									<tr>
										<th> S.No</th>
										<th> Pay Structure Name</th>
									</tr>
								</thead>
								<tbody>
									{% for pay_structure in paystructureForms %}
									<tr align="center">
										<td width="10%" align="center">{{forloop.counter}}.</td>
										<td width="75%" align="left">
											<span style='width: calc(100% - 100px); display: inline-block;'>
												<a class="edit_link_code" onclick="editPayStrucutreRow('{{ pay_structure.id }}');">
													{{pay_structure.description}}
												</a>
											</span>
											<span style='width: 100px; display: inline-block; float: right; text-align: right;'>
												<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='bottom' title='Edit' 
		                                            onclick="editPayStrucutreRow('{{ pay_structure.id }}');">
		                                                <i class='fa fa-pencil'></i>
	                                            </span>
	                                            <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='bottom' title='Edit in New Tab' onclick="editPayStrucutreRow('{{ pay_structure.id }}', '_blank');" >
	                                                <i class='fa fa-external-link'></i>
	                                            </span>
	                                            {% if access_level.edit %}
		                                            <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='bottom' title='Delete' onclick="deletePayStructureConfirm('{{ pay_structure.id }}');" >
		                                                <i class='fa fa-trash-o'></i>
		                                            </span>
		                                        {% endif %}    
	                                        </span>
										</td>
									</tr>
									{% endfor %}
								</tbody>
							</table>
						</div>
					</div>
					<div id="tab2" class="tab-pane fade">
						<div class="add_table">
							<form action="/erp/hr/pay/save/#tab2" method="post" id="addPay">{% csrf_token %}
								{% if payForm.id.value != '' and payForm.id.value != None %}
									<script type="text/javascript">
										$(".create_pay_structure, .pay_structure_btn, .export_csv").addClass('hide');
										$(".view_pay_structure").removeClass('hide');
										$(".page_header").html('<a href="/erp/hr/pay/" >Pay Structure</a> <i class="fa fa-angle-right" style="padding: 0 8px;" aria-hidden="true"></i> <span class="header_current_page"> {{payForm.description.value}} </span>');	
									</script>
								{% else %}
									<script type="text/javascript">$(".page_header").text('Pay Structure');</script>
								{% endif %}

								<div class="col-lg-4 add_table_content">
									<div class="form-group">
										{{ payForm.id }}
										<label>Pay Structure Name<span class="mandatory_mark"> *</span></label>
										<!-- Render the field in non-editable for Edit and editable for Add -->
										{% if payForm.id.value != '' and payForm.id.value != None %}
										<input type="text" class="form-control" disabled
											   value="{{payForm.description.value}}" id="id_{{payForm.prefix}}-description"
											   name="name_display"/>
										<!-- Disabled fields are not read in POST, hence the hidden field -->
										<input type="text" value="{{ payForm.description.value }}"
											   id="id_hidden_name" name="{{payForm.prefix}}-description" hidden="hidden"/>
										{% else %}
										{{ payForm.description }}
										{% endif %}
										{{ payForm.enterprise_id }}
									</div>
								</div>
								<div class="col-lg-12 add_table_content">
									<div class="row form-group">
										<div class="col-sm-4">
											<label>Item Type<span class="mandatory_mark"> *</span></label>
											<div hidden="hidden">
												{{ pay_structure_formset.empty_form.pay_structure_id }}
												{{ pay_structure_formset.empty_form.enterprise_id }}
												<input type="text" value="0" hidden="hidden" id="newFormCount"/>
											</div>
											{{ pay_structure_formset.empty_form.type }}
										</div>
										<div class="col-sm-4">
											<label>Description<span class="mandatory_mark"> *</span></label>
											<div >{{ pay_structure_formset.empty_form.description }}</div>
										</div>
										<div class="col-sm-2">
											<span class="po_title_txt">&nbsp;</span>
											<div class="material_txt">
												<input type='button' id="add_new_pay_details" class="btn btn-save" value="+"/>
											</div>
										</div>
									</div>
									<div class="clearfix"></div>
								</div>

								<div class="col-lg-12 add_table_content">
									<div class="row">
										<div class="table-responsive full_width_txt" id="indent_materials">
											<div class="col-sm-12">
												{{ pay_structure_formset.management_form }}
												<table class="table table-bordered table-striped custom-table" id="pay_details">
													<thead>
													<tr>
														<th style="width: 100px;">S No.</th>
														<th style="width: 200px;">Item Type</th>
														<th>Description</th>
														<th style="width: 100px;">Delete</th>
													</tr>
													</thead>
													<tbody>
													{% for pay_structure_form in pay_structure_formset.initial_forms %}

														<tr align="center" bgcolor="#ececec" id="{{ pay_structure_form.prefix }}">
															<td hidden="hidden">
																{{ pay_structure_form.pay_structure_id }}
																{{ pay_structure_form.enterprise_id}}
																{{ pay_structure_form.DELETE }}
																{{ pay_structure_form.type }}
															</td>
															<td ><div id="id_{{ pay_structure_form.prefix }}-s_no"></div></td>
															<td class="pay_type" align="left"><div id="id_{{ pay_structure_form.prefix }}-itemLabel"></div>{{ pay_structure_form.type.value }}</td>
															<td class="pay_desc" align="left"><div>{{ pay_structure_form.description }}</div></td>
															<td><a href="#" id="id_{{ pay_structure_form.prefix }}-deletePayItem"
															       onclick="javascript:deletePayItem('{{ pay_structure_form.prefix }}')">
																<i class="fa fa-trash-o"></i></a>
															</td>
														</tr>
													{% endfor %}
														<tr bgcolor="#ececec" align="center"id="pay_structure-__dummy__" hidden="hidden">
															<td hidden="hidden">
																{{ pay_structure_formset.empty_form.pay_structure_id }}
																{{ pay_structure_formset.empty_form.DELETE }}
																{{ pay_structure_formset.empty_form.enterprise_id }}
																{{ pay_structure_formset.empty_form.type }}
															</td>
															<td><div id="id_pay_structure-__prefix__-s_no"></div></td>
															<td class="pay_type" align="left"><div id="id_pay_structure-__prefix__-itemLabel"></div></td>
															<td class="pay_desc" align="left"><div>{{ pay_structure_formset.empty_form.description }}</div></td>
															<td><a href="#" id="id_{{ pay_structure_formset.empty_form.prefix }}-deletePayItem"
															       onclick="javascript:deletePayItem('{{ pay_structure_formset.empty_form.prefix }}')">
																<i class="fa fa-trash-o"></i></a> </td>
														</tr>
													</tbody>
												</table>
											</div>
										</div>
									</div>
									<div class="material_txt text-right">
										{% if access_level.edit %}
										<a href="#" id="save_pay_button" class="btn btn-save">Save</a>
										<input type="submit" hidden="hidden" id="savePay" value="Save"/>
										{% endif %}
										<a href="/erp/hr/pay/" class="btn btn-cancel">Cancel</a>
									</div>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<div class="hide">
	<form action="/erp/hr/pay/edit/#tab2" method="post" id="payStructure_edit_form">
		{% csrf_token %}
		<input type="hidden" id="edit_payStructure_id" name="code" value="" />
	</form>
	<form action="/erp/hr/pay/delete/" method="post" id="payStructure_delete_form">
		{% csrf_token %}
		<input type="hidden" id="delete_payStructure_id" name="deleteCode" value=""/>
	</form>
</div>
<script>
$(window).load(function() {
	$('.nav-pills li').removeClass('active');
	$('#li_paystructure').addClass('active');
});

$(document).ready(function() {
	initializePage();
	enterKeySubmitEvent();
	if ($("#response_message").val() != "") {
		if($("#response_message").val().indexOf('saved') >= 0) {
			swal("Success", $("#response_message").val(), "success");
		}
		else {
			swal("Failed", $("#response_message").val(),"warning");
		}
        
    }
    if(window.location.href.indexOf('edit') < 0) {
		TableHeaderFixed();
    }
});

var oTable;
var oSettings;
function TableHeaderFixed() {
	var columnCount = $("#payList thead tr").find("th").length;
	oTable = $('#payList').DataTable({
				fixedHeader: true,
				"pageLength": 50,
				"search": {
					"smart": false
				}
			});	
	oTable.on("draw",function() {
		var keyword = $('#payList_filter > label:eq(0) > input').val();
		$('#payList').unmark();
		$('#payList').mark(keyword,{});
		setHeightForTable();
		listTableHoverIconsInit('payList');
	});
	oTable.on('page.dt', function() {
	  $('html, body').animate({
		scrollTop: $(".dataTables_wrapper").offset().top - 15
	   }, 'slow');
	  listTableHoverIconsInit('payList');
	});
	oSettings = oTable.settings();
	listTableHoverIconsInit('payList');
	$( window ).resize();
}

$(".create_pay_structure").click(function(){
	$(this).addClass('hide');
	$(".export_csv").addClass('hide');
	$(".view_pay_structure").removeClass('hide');
	$(".page_header").html('<a href="/erp/hr/pay/" >Pay Structure</a> <i class="fa fa-angle-right" style="padding: 0 8px;" aria-hidden="true"></i> <span class="header_current_page"> New </span>');	
	if($("#payList").hasClass('dataTable')) {
		oTable.destroy();
	}
});

function editPayStrucutreRow(payStructureId,  openTarget="") {
	$("#edit_payStructure_id").val(payStructureId);
	$("#payStructure_edit_form").attr("target", openTarget).submit();
}

function deletePayStrucuteRow(payStructureId){
	$("#delete_payStructure_id").val(payStructureId);
	$("#payStructure_delete_form").submit();
}
</script>
<!-- /#wrapper -->
{% endblock %}