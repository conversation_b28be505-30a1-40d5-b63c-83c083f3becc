{% extends "masters/sidebar.html" %}
{% block party %}
<script type="text/javascript" src="/site_media/js/party.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/registration.css?v={{ current_version }}">
<style type="text/css">
	.registration-details .custom-error-message {
		display: block;
		margin-top: 33px;
	}
</style>
<div class="right-content-container">
	<div class="page-title-container">
        <span class="page-title">Party</span>
    </div>
	<div class="container" style="margin-top: 15px;">
		<div class="page-heading_new">
			<a href="/erp/masters/party_list/" class="btn btn-add-new pull-right view_po" style="margin-top: -15px; margin-bottom: 20px;" data-tooltip="	tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
		</div>
		<div class="col-sm-10">
			<div class=" party-name-container">
				<div class="party-name-edit-container" {% if party_details.id %}style="display: none;"{% endif %}>
					<div class="floating-label col-sm-6 remove-left-padding">
			      		<input type="text" class="form-control floating-input" id="id_party-name" maxlength="100" name="party-name" onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar');" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar');" placeholder=" "  value="{{party_details.name}}">
			      		<label >Party Name<span class="mandatory_mark"> *</span></label>
		      		</div>
		      		<div class="floating-label col-sm-4">
			      		<input class="form-control floating-input" id="id_party-code" name="party-code" type="text" placeholder=" " maxlength="10" value="{{party_details.code}}" onblur='constructSupplierCode()' style="max-width: 255px;">
			      		<label style="padding-left: 15px;">Party Code</label>
			      	</div>
		      		<input id="id_party-id" name="party-id" type="hidden" value="{{party_details.id}}">
		      	</div>
		      	{% if party_details.id %}
			      	<div class="party-name-lable-container" style="font-size:24px; cursor: pointer; display: block; margin-bottom: 7px;">
			      		{{ party_details.name }}
			      		{% if party_details.code %}<small style="font-size:12px;">(<span>{{ party_details.code }}</span>)</small>{% endif %}
			      		<i class="fa fa-pencil" style="visibility: hidden; font-size: 20px;" aria-hidden="true"></i>
			      	</div>
			    {% endif %}	
		    </div>
		    <div class="clearfix"></div>
		</div>
		<div class="col-lg-5 col-md-12">
		    <label class="enterprice-item-heading" style="margin-top: 10px;" >Primary Contact Details</label>
		    <div class="row address-container">
				<div class="floating-label col-sm-12 address-container-address remove-padding">
					<textarea class="form-control floating-input" cols="40" id="id_party-address" maxlength="300" name="party-address" onkeypress="validateStringOnKeyPress(this,event,'alphaSpecialChar');" onblur="validateStringOnBlur(this, event,'alphaSpecialChar');" placeholder=" " rows="3">{{party_details.address_1}} {{party_details.address_2}}</textarea>
					<label>Address</label>
				</div>
				<div class="floating-label col-sm-6 address-container-city" style="padding-left: 0;">
					<input type="text" class="form-control floating-input" id="id_party-city" maxlength="50" name="party-city" onkeypress="validateStringOnKeyPress(this,event, 'alphanumeric');" onblur="validateStringOnBlur(this, event, 'alphanumeric');" placeholder=" " value="{{party_details.city}}">
					<label>City</label>
				</div>
				<div class="floating-label col-sm-6 address-container-country" style="padding-left: 0;">
					<input type="hidden" id="party_country_code" value="{{party_details.country_code}}" >
					<select class="form-control" id="id_country-__prefix__-make_choices" onchange="updateCountryValue('onchange')" style="border: none; border-bottom: solid 1px #DDD; box-shadow: 0 0; border-radius: 0; padding: 0;">
						{% for country in gst_country_list %}
							<option value="{{ country.country_code }}" {% if country.country_code == party_details.country_code %} selected {% endif %}>{{ country.country_name }}</option>
						{% endfor %}
					</select>
					<label style="top: -18px; color: #777; left: 4px; font-size: 11px;">Country<span class="mandatory_mark"> *</span></label>
				</div>
				<div class="floating-label col-sm-6 address-container-state-list" style="padding-left: 0;">
					<select class="form-control" id="id_state-__prefix__-make_choices" onchange="updateStateValue()" style="border: none; border-bottom: solid 1px #DDD; box-shadow: 0 0; border-radius: 0; padding: 0;">
						{% for state in state_list %}
							<option value="{{ state.code}}" {% if state.description|upper == party_details.state|upper %} selected {% endif %}>{{ state.description }}</option>
						{% endfor %}
					</select>
				</div>
				<div class="floating-label col-sm-6 address-container-state-text" style="padding-left: 0;">
                    <input type="hidden" id="party_state_code" value="{{party_details.state}}" >
					<input type="text" class="form-control floating-input" id="id_party-state" maxlength="50" name="party-state" onkeypress="validateStringOnKeyPress(this,event, 'alphanumeric');" onblur="validateStringOnBlur(this, event, 'alphanumeric');" placeholder=" " value="{{party_details.state}}">
					<label>State<span class="mandatory_mark"> *</span></label>
				</div>
				<div class="floating-label col-sm-6 address-container-pin-code" style="padding-left: 0; padding-right: 0;">
					<input type="text" class="form-control floating-input" id="id_party-pincode" maxlength="10" name="party-pincode" onfocus="setNumberRangeOnFocus(this,10,0,true)" placeholder=" " value="{{party_details.pin_code}}">
					<label>Pincode</label>
				</div>
				<div class="col-sm-12 line-divider"></div>
			    {% if party_contact_details %}
					{% for contact_detail in party_contact_details %}
			            {% if contact_detail.sequence_id == 1 %}
						    <div class="floating-label col-sm-6" style="padding-left: 0;">
								<input class="form-control floating-input" id="id_party-contact_person" maxlength="50" name="party-contact_person"
								       onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');"
								       placeholder=" " type="text" value="{{ contact_detail.name }}">
								<label>Name</label>
							</div>
							<div class="floating-label col-sm-6" style="padding-left: 0; padding-right: 0;">
								<input class="form-control floating-input" id="id_party-email" maxlength="75" name="party-email"
								       onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');"
								       placeholder=" " type="text" value="{{ contact_detail.email }}">
								<label>Email</label>
							</div>
							<div class="floating-label col-sm-6" style="padding-left: 0;">
								<input class="form-control floating-input" id="id_party-phone" maxlength="30" name="party-phone" onblur="return validateStringOnBlur(this, event, 'ph_number');"
								       onkeypress="return validateStringOnKeyPress(this,event, 'ph_number');" placeholder=" " type="text" value="{{ contact_detail.phone_no }}">
		                        <label>Contact Number</label>
							</div>
							<div class="floating-label col-sm-6" style="padding-left: 0; padding-right: 0;">
								<input class="form-control floating-input" id="id_party-fax" maxlength="30" name="party-fax" onblur="return validateStringOnBlur(this, event, 'ph_number');"
								       onkeypress="return validateStringOnKeyPress(this,event, 'ph_number');" placeholder=" " type="text" value="{{ contact_detail.fax_no }}">
								<label>Fax</label>
							</div>
			            {% endif %}
			        {% endfor %}

					<div class="other_contact_details" style="display: none; float: left;">
						<div class="enterprice-item-heading other_contact_details-text" style="position: absolute; z-index: 10; margin-top: -6px; margin-left: -4px;">OTHER CONTACT DETAILS <span class="other-contact-count"></span></div>
						{% for contact_detail in party_contact_details %}
				            {% if contact_detail.sequence_id != 1 %}
								<div class="contact-details" data-sequence-id = "{{contact_detail.sequence_id}}">
				                    <div class="col-sm-12 line-divider">
				                        <i class="fa fa-minus-circle delete-contact-container" role="button" onclick="deleteContactDetails(this)"></i>
									</div>
									<div class="floating-label col-sm-6" style="padding-left: 0;">
										<input class="form-control floating-input contact-person-name" id="id_party-contact_person_{{contact_detail.sequence_id}}" maxlength="50" name="party-contact_person" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" placeholder=" " type="text" value="{{ contact_detail.name }}">
										<label>Name</label>
									</div>
									<div class="floating-label col-sm-6" style="padding-left: 0; padding-right: 0;">
										<input class="form-control floating-input contact-person-email" id="id_party-email_{{contact_detail.sequence_id}}" maxlength="75" name="party-email" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" placeholder=" " type="text" value="{{ contact_detail.email }}">
										<label>Email</label>
									</div>
									<div class="floating-label col-sm-6" style="padding-left: 0;">
										<input class="form-control floating-input contact-person-number" id="id_party-phone_{{contact_detail.sequence_id}}" maxlength="30" name="party-phone" onblur="return validateStringOnBlur(this, event, 'ph_number');" onkeypress="return validateStringOnKeyPress(this,event,'ph_number');" placeholder=" " type="text" value="{{ contact_detail.phone_no }}">
										<label>Contact Number</label>
									</div>
									<div class="floating-label col-sm-6" style="padding-left: 0; padding-right: 0;">
										<input class="form-control floating-input contact-person-fax" id="id_party-fax_{{contact_detail.sequence_id}}" maxlength="30" name="party-fax" onblur="return validateStringOnBlur(this, event, 'ph_number');" onkeypress="return validateStringOnKeyPress(this,event, 'ph_number');" placeholder=" " type="text" value="{{ contact_detail.fax_no }}">
										<label>Fax</label>
									</div>
								</div>
							{% endif %}
					    {% endfor %}
					</div>
			    {% else %}
			        <div class="floating-label col-sm-6" style="padding-left: 0;">
						<input class="form-control floating-input" id="id_party-contact_person" maxlength="50" name="party-contact_person"
						       onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');"
						       placeholder=" " type="text" value="">
						<label>Name</label>
					</div>
					<div class="floating-label col-sm-6" style="padding-left: 0; padding-right: 0;">
						<input class="form-control floating-input" id="id_party-email" maxlength="75" name="party-email"
						       onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');"
						       placeholder=" " type="text" value="">
						<label>Email</label>
					</div>
					<div class="floating-label col-sm-6" style="padding-left: 0;">
						<input class="form-control floating-input" id="id_party-phone" maxlength="30" name="party-phone" onblur="return validateStringOnBlur(this, event, 'ph_number');"
						       onkeypress="return validateStringOnKeyPress(this,event, 'ph_number');" placeholder=" " type="text" value="">
	                        <label>Contact Number</label>
					</div>
					<div class="floating-label col-sm-6" style="padding-left: 0; padding-right: 0;">
						<input class="form-control floating-input" id="id_party-fax" maxlength="30" name="party-fax" onblur="return validateStringOnBlur(this, event, 'ph_number');"
						       onkeypress="return validateStringOnKeyPress(this,event, 'ph_number');" placeholder=" " type="text" value="">
						<label>Fax</label>
					</div>
			        <div class="other_contact_details" style="display: none; float: left;">
			        	<div class="enterprice-item-heading other_contact_details-text" style="position: absolute; z-index: 10; margin-top: -6px; margin-left: -4px;">OTHER CONTACT DETAILS <span class="other-contact-count"></span></div>
			        </div>
			    {%endif %}
				<div class="col-sm-12 text-right remove-padding">
					<span class="col-sm-6 text-left remove-padding">
						{% if party_contact_details|length >= 2 %}
							<span class="show-hide-contacts" role="button" onclick="showHideOtherContacts(this)"><a>Show More <i class="fa fa-angle-down" 	aria-hidden="true" style="font-weight: bold;"></i></a></span>
						{% endif %}
					</span>
					<span class="col-sm-6 text-right remove-padding">
						<span role="button" onclick="addNewContact(this)"><a>+ Add Contact</a></span>
					</span>
				</div>
			</div>
		</div>
		<div class="col-lg-3 col-md-6">
			<label class="enterprice-item-heading" style="margin-top: 10px;">Registration Details</label>
			<div class="row" style="margin: 0; padding: 35px 15px 15px; border: solid 1px #209be1; margin-top: -30px; margin-bottom: 15px;">
				<label>GST Category<span class="mandatory_mark"> *</span></label>
				<select class="form-control" id="id_gst_category-__prefix__-make_choices" onchange="gstCategoryChangeEvent()">
					{% for category in gst_category_list %}
						<option value="{{ category.category_id }}" {% if category.category_id == party_details.category_id %} selected {% endif %} data-desc="{{ category.category_desc }}">{{ category.category_name }}</option>
					{% endfor %}
				</select>
				<div class="col-sm-12 remove-padding form-group hide" id="party-port-contianer" style="margin-top: 10px;">
					<label>Port</label>
					<input class="form-control" id="id_party-port" maxlength="30" name="party-port" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" placeholder="Enter PORT" type="text" value="{{ party_details.port }}">
					
				</div>
			 	<div class="registration-extra-details" style="margin-top: 15px;">
			 		<div class="col-sm-12 form-group registration-details" style="padding: 0;">
						<input type="text" class="form-control registration-label-id hide" value="1" />
						<label class="gstin_mandate" style="position: absolute; margin-left: 54px; margin-top: 8px;"><span class="mandatory_mark">*</span></label>
			            <input type="text" class="form-control registraion-key" maxlength="30"  onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" value="GSTIN" placeholder="Label" readonly="" />
			            <input type="text" class="form-control registraion-value" id="party_gstin_number" maxlength="150" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" value='{{ gstin_value }}'  placeholder="Detail" />
			        </div>
			        <div class="col-sm-12 form-group registration-details" style="padding: 0;">
						<input type="text" class="form-control registration-label-id hide" value="2" />
			            <input type="text" class="form-control registraion-key" maxlength="30"  onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" value="PAN" placeholder="Label" readonly="" />
			            <input type="text" class="form-control registraion-value" id="party_pan_number" maxlength="150" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" value='{{ pan_value }}'  placeholder="Detail" />
			        </div>
				    {% for reg_detail in party_reg_detail %}
						<div class="col-sm-12 form-group registration-details" style="padding: 0;">
							<input type="text" class="form-control registration-label-id hide" value={{ reg_detail.label_id }} />
				            <input type="text" class="form-control registraion-key" maxlength="30"  onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" value='{{ reg_detail.label }}' placeholder="Label" readonly="" />
				            <input type="text" class="form-control registraion-value" maxlength="150" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');" value='{{ reg_detail.details }}'  placeholder="Detail" />
							<i class="fa fa-minus-circle" role="button" onclick="deleteRegistraionDetails(this)" style="color: #dd4b39;position: absolute;margin-top: 10px;margin-left: -20px;"></i>
				        </div>
					{% endfor %}
			 	</div>
			 	<div class="col-sm-12 remove-padding">
			    	<span role="button" style="float: right; margin-right: 12px;" onclick="addNewRegistrationDetails(this)"><a>+ Add</a></span>
			    </div>
			</div>
		</div>
		<div class="col-lg-4 col-md-6">
			<label class="enterprice-item-heading" style="margin-top: 10px;">Payment Terms</label>
		    <div class="row" style="margin: 0; padding: 35px 15px 15px; border: solid 1px #209be1; margin-top: -30px; margin-bottom: 15px;">
		    	<label>Currency<span class="mandatory_mark"> *</span></label>
				<select class="form-control" id="id_currency">
					{% for j in currency %}
					<option value="{{ j.id }}" {% if j.id == party_details.currency %} selected {% endif %}>{{ j.code }}</option>
					{% endfor %}
				</select>
                <input type="hidden" id="party_currency_code" value="{{party_details.currency}}" >
				<div class="checkbox remove-chk-padding xs-styled-checkbox" style="margin-right: 15px;">
					<input type="checkbox" id="duty_pass_reminder" />
					<label for="duty_pass_reminder">Remind Duty Passed</label>
				</div>
				<div class="clearfix"></div>
				<div class="checkbox remove-chk-padding xs-styled-checkbox" style="margin-right: 15px;">
					<input type="checkbox" id="id_is_supplier" onchange="partyTypeChangeEvent(this, 'supplier')" />
					<label for="id_is_supplier">Supplier</label>
				</div>
				<div class="party-supplier-container hide" style="padding: 35px 15px 15px; border: solid 1px #209be1; margin-top: -30px; display: inline-block; width: 100%">
						<label>Supplier Cr. Period</label>
						<input type="text" id="pay_cre_days" class="form-control" maxlength="5" placeholder="Enter Supplier Credit Days" onfocus="setNumberRangeOnFocus(this,5,0)" autocomplete="off" value="{{party_details.payment_credit_days}}"/>
						<span class="unit_display pull-right">days</span>
		    	</div>
		    	<div class="checkbox remove-chk-padding xs-styled-checkbox" style="margin-right: 15px;">
					<input type="checkbox" id="id_is_customer" onchange="partyTypeChangeEvent(this, 'customer')"  />
					<label for="id_is_customer">Customer</label>
				</div>
				<div class="party-customer-container hide" style="padding: 35px 15px 15px; border: solid 1px #209be1; margin-top: -30px; display: inline-block; width: 100%">
					<label>Customer Cr. Period</label>
					<input type="text" id="rec_cre_days" class="form-control" maxlength="5" placeholder="Enter Customer Credit Days" onfocus="setNumberRangeOnFocus(this,5,0)" autocomplete="off" value="{{party_details.receipt_credit_days}}"/>
					<span class="unit_display pull-right">days</span>
		    	</div>
			    <input type="hidden" id="config_flags_value" value="{{ party_details.config_flags}}" />
			</div>
		</div>
		<div class="clearfix"></div>
		{% if access_level.edit %}
			<div class="pull-right" style="margin-right: 15px;">
				{% if party_details.id %}
					<button class="btn btn-save btn-party-save" id="update-Party-details" onclick="savePartyDetails('update')">Update</button>
				{% else %}
					<button class="btn btn-save btn-party-save" id="save-Party-details" onclick="savePartyDetails('save')">Save</button>
				{% endif %}
			</div>
		{% endif %}	
	</div>
</div>

<script type="text/javascript">
	$(document).ready(function(){
		editableByHoverInit();
		configFlagsInit();
		gstCategoryChangeEvent();
		$("#id_is_supplier").trigger("change");
		$("#id_is_customer").trigger("change");
		$('.nav-pills li').removeClass('active');
		$('#li_party').addClass('active');
		updateCountryValue();
	});

	function configFlagsInit() {
		var config_value = $("#config_flags_value").val();
		if(parseInt(config_value) & 1) {
			$("#duty_pass_reminder").prop("checked", true);
		}
		if(parseInt(config_value) & 2) {
			$("#id_is_supplier").prop("checked", true);
		}
		if(parseInt(config_value) & 4) {
			$("#id_is_customer").prop("checked", true);
		}
		if($("#id_party-id").val() == "" || $("#id_party-id").val() == "None") {
		    $("#id_country-__prefix__-make_choices").val($("#home_country_id").val())
		    if($("#home_country_id").val() == "IN") {
		        $("#id_state-__prefix__-make_choices option:contains(" + $("#home_state_id").val() + ")").attr('selected', 'selected');
                $("#id_state-__prefix__-make_choices").trigger("change");
            }
            else {
                $("#id_party-state").val($("#home_state_id").val())
            }
		    $("#id_currency option:contains(" + $("#home_currency_id").val() + ")").attr('selected', 'selected');
		}
	}


	function editableByHoverInit() {
		$( ".party-name-lable-container" ).hover(
		  	function() {
		    	$(this).find(".fa-pencil").css("visibility", "");
		  	}, function() {
		    	$(this).find(".fa-pencil").css("visibility", "hidden");
		  	}
		);

		$(".party-name-lable-container").click(function(){
			$(this).hide();
			$(".party-name-edit-container").show();
			var $initialVal = $("#id_party-name").val();
			$("#id_party-name").val("")
    		$("#id_party-name").focus().val($initialVal);
		});
	}

	function partyTypeChangeEvent(current, type) {
		if($(current).is(":checked")){
			$(`.party-${type}-container`).removeClass("hide");
		}
		else {
			$(`.party-${type}-container`).addClass("hide");
		}

		if($("#id_is_customer").is(":checked")) {
			$("#id_is_customer").closest(".checkbox").css({display: "block", width: "145px"})
		}
		else {
			$("#id_is_customer").closest(".checkbox").css({display: "inline-block", width: "145px"})
		}
	}

	function gstCategoryChangeEvent(){
		var gstCategory = $("#id_gst_category-__prefix__-make_choices").val();
		if(["5", "6", "7", "9"].indexOf(gstCategory) != -1){
			$("#party-port-contianer").removeClass("hide");
		}
		else {
			$("#party-port-contianer").addClass("hide");
		}
		if(["3", "4", "5"].indexOf(gstCategory) == -1){
			$(".gstin_mandate").removeClass("hide");
		}
		else {
			$(".gstin_mandate").addClass("hide");
		}
		$(".registration-extra-details .error-border").removeClass("error-border");
		$(".registration-extra-details .custom-error-message").remove();
	}

	function updateStateValue() {
		var selectedState = $("#id_state-__prefix__-make_choices").find("option:selected").text();
		$("#id_party-state").val(selectedState);
	}

	function updateCountryValue(loadtype='') {
		console.log($("#id_party-state").val());
		var selectedCountry = $("#id_country-__prefix__-make_choices").val();
		if(selectedCountry == "IN") {
			$(".address-container-state-text").addClass("hide");
			$(".address-container-state-list").removeClass("hide");
			$("#id_party-state").val($("#id_state-__prefix__-make_choices option:selected").text());
		}
		else {
			$(".address-container-state-text").removeClass("hide");
			$(".address-container-state-list").addClass("hide");
			if(loadtype == "onchange") {
				$("#id_party-state").val("");
			}
		}
	}
</script>


{% endblock %}