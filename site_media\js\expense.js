$(function() {
	$('#cmdcancel').click(function () {
		window.location = "/erp/expenses/home/";
	});
});

$(document).ready(function(){
	AddNewExpValidation();
	var url=window.location.href;
	url = url.substring(7)
	window.urlpath = url.substring(0,url.indexOf('/'));

	transformToDummyForm('exp_item');
    transformToDummyForm('tag');
    create_delete_tag_button();
});

function AddNewExpValidation(){
	$("#cmdadd").click(function(){
        refreshSessionPerNActions(3);
		$(".error-border").removeClass('error-border');
		$(".custom-error-message").remove();

		var ControlCollections = [
			{
				controltype: 'datepicker',
				controlid: 'id_exp_item-__prefix__-spent_on',
				isrequired: true,
				errormsg: 'Date is required.'
			},
			{
				controltype: 'textbox',
				controlid: 'id_exp_item-__prefix__-description',
				isrequired: true,
				errormsg: 'Description is required.'
			},
			{
				controltype: 'dropdown',
				controlid: 'id_exp-expense_head_ledger',
				isrequired: true,
				errormsg: 'Expense Head is required.'
			},
			{
				controltype: 'textbox',
				controlid: 'id_exp_item-__prefix__-amount',
				isrequired: true,
				errormsg: 'Amount is required.'
			}
		];
		var result = JSCustomValidator.JSvalidate(ControlCollections);
		if(result){
			AddNewExpense();
		}
		else {
			return result;
		}
	});
    $("#cmd_exp_update").click(function(){
        $("#id_exp-group_description").removeAttr("disabled");
        if (parseInt($("#id_exp-status").val().trim()) == 4) {
        	$("#loading").show();
            $.ajax({
                url: '/erp/expenses/json/expense_account_linked_message/',
                type: "POST",
                dataType: "json",
                data: {
                    expense_code: $(".header_current_page").text().trim()
                },
                success: function (response) {
                	$("#loading").hide();
                    if (response.response_message == "Success") {
                        if (response.custom_message == "") {
                            $("#cmd_exp_save").click();
                        } else {
                            swal({title: "", 
                            	text: response.custom_message, 
                            	type: "warning",
                                showCancelButton: true,
                                confirmButtonColor: "#209be1",
                                confirmButtonText: "Yes, do it!",
                                closeOnConfirm: true, closeOnCancel: true
                            },
	                        function(isConfirm){
	                            if (isConfirm) {
	                                $("#cmd_exp_save").click();
	                            }
	                        });
                        }
                    } else {
                        swal({title: "", text: response.custom_message, type: "warning"});
                    }
                },
                error: function (xhr, errmsg, err) {
					$("#loading").hide();
                    console.log(errmsg,  [xhr.responseText, err]);
                    swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
                }
            });
        } else {
            $("#cmd_exp_save").click();
        }
    });
	$("#cmd_exp_save").click(function(){
	    $("#id_exp-group_description").removeAttr("disabled");
		$(".error-border").removeClass('error-border');
		$(".custom-error-message").remove();
		var ControlCollections = [
			{
				controltype: 'dropdown',
				controlid: 'id_exp-claim_head_ledger_id',
				isrequired: true,
				errormsg: 'Claim Head is required.'
			},
			{
				controltype: 'textbox',
				controlid: 'id_exp-group_description',
				isrequired: true,
				errormsg: 'Description is required.'
			}
		];
		var result = JSCustomValidator.JSvalidate(ControlCollections);
		if(result){
			$("#expensetable tr:visible").find('.exp_description').each(function(){
				if($(this).find('input').val() == ""){
					$(this).find('input').addClass('error-border');
					result = false;
				}
			});
			if(!result){
				swal('','Item Descrption cannot be empty !','warning');
			}
			else {
				$("#loading").show();
				$("#cmd_exp_save").val('Processing...').addClass('btn-processing');
			}
		}
		$("#id_exp-status_to_be_changed").val(0);
		return result;
	});

	$("#cmd_exp_approve").click(function(){
		$(".error-border").removeClass('error-border');
		$(".custom-error-message").remove();
		var result = true;
		$("#expensetable tr:visible").find('.exp_approve_amount').each(function(){
			if($(this).find('input').val() == ""){
				$(this).find('input').addClass('error-border');
				result = false;
			}
		});
		if(!result){
			swal('','Approver Amount cannot be left empty!','warning');
			return result;
		}
		else {
		    $("#id_exp-super_edit").val(0);
	        $("#id_exp-group_description").removeAttr("disabled");
			$("#cmd_exp_approve").val('Processing...').addClass('btn-processing');
			$("#loading").show();
		}
	});

	$("#cmd_exp_verify, #cmd_checked_ok").click(function(){
		$(".error-border").removeClass('error-border');
		$(".custom-error-message").remove();
		var result = true;
		$("#expensetable tr:visible").find('.exp_audit_amount').each(function(){
			if($(this).find('input').val() == ""){
				$(this).find('input').addClass('error-border');
				result = false;
			}
		});
		if(!result){
			swal('','Audit Amount cannot be left empty!','warning');
			return result;
		}
		else {
            $("#id_exp-super_edit").val(0);
            $("#id_exp-group_description").removeAttr("disabled");
			$(this).val('Processing...').addClass('btn-processing');
			$("#loading").show();
		}
	});

	$("#cmd_exp_confirm").click(function(){
		$(".error-border").removeClass('error-border');
		$(".custom-error-message").remove();
		var ControlCollections = [
			{
				controltype: 'dropdown',
				controlid: 'id_exp-claim_head_ledger_id',
				isrequired: true,
				errormsg: 'Claim Head is required.'
			},
			{
				controltype: 'textbox',
				controlid: 'id_exp-group_description',
				isrequired: true,
				errormsg: 'Description is required.'
			}
		];
		var result = JSCustomValidator.JSvalidate(ControlCollections);
		if(result){
			if($("#expensetable tr:visible").length <=1 ) {
				result = false;
			}

			if(!result){
				swal('','Please add atlease one Expense to confirm !','warning');
				return result;
			}
			if(result){
				$("#expensetable tr:visible").find('.exp_description').each(function(){
					if($(this).find('input').val() == ""){
						$(this).find('input').addClass('error-border');
						result = false;
					}
				});
			}
			if(!result){
				swal('','Item Descrption cannot be empty !','warning');
				return result;
			}
			if(result){
				$("#expensetable tr:visible").find('.exp_amount').each(function(){
					if($(this).find('input').val() <= 0){
						$(this).find('input').addClass('error-border');
						result = false;
					}
				});
			}
			if(!result){
				swal('','Item Amount cannot be less than 0 !','warning');
				return result;
			}
			else {
			    $("#id_exp-group_description").removeAttr("disabled");
				$("#cmd_exp_confirm").val('Processing...').addClass('btn-processing');
				$("#loading").show();
			}
		}
		$("#id_exp-status_to_be_changed").val(1);
		return result;
	});
}


function AddNewExpense(){
        refreshSessionPerNActions(10);
	if ($('#id_exp_item-__prefix__-description').val() != "" && $('#id_exp_item-__prefix__-amount').val() != "" ){
		var expDate = $('#id_exp_item-__prefix__-spent_on').val();
		expDate = moment(expDate, "YYYY-MM-DD").format("MMM D, YYYY");
		if(expDate.toLowerCase().trim() == "invalid date") { expDate = '-'; }
		if($('#id_exp_item-__prefix__-bill_available').is(':checked')) var isBillAvail = 'Yes'; else var isBillAvail = 'No';
		var filename = $("#bill_copy_uploader").val().replace(/C:\\fakepath\\/i, '').toLowerCase();
		if(filename != "") {
			var extension = filename.replace(/^.*\./, '').toLowerCase();
			var base64val = $("#bill_base64Format").text();
			var billcopy = "<span class='document_attachment base64-file-value linked_text base64 "+extension+"' data-url="+base64val+" data-extension='"+extension+"' data-filename = "+filename.replace(/\s/g,"_")+" >";
		}
		else {
			var billcopy="";
		}
		var amount = $("#id_exp_item-__prefix__-amount").val();

		generateFormsetFormRow('exp_item');
		var index = parseInt(parseInt($('#id_exp_item-TOTAL_FORMS').val()) - 1);
        var expense_head_ledger_name = $('#id_exp_item-' + index + '-expense_head_ledger_name');
        var net_value = $('#id_exp_item-' + index + '-net_value');
        var s_no = document.getElementById("id_exp_item-" + index + "-s_no");
        $.when(getGCSKeyExpenseData()).done(function(){
            $.when(expenseAjaxSubmit()).done(function(){
                copyFromEmptyForm('exp_item', index, 'exp-expense_id', 'expense_id');
            });
        });
        expense_head_ledger_name.innerHTML = $('#id_exp_item-' + index + '-expense_head_ledger').val();
        net_value.innerHTML = calculateNetAmount()
        s_no.innerHTML = index + 1;
		//DeleteRowFunctionality();
		validateTextInputs();
		CalculateClaimedAmount();
		SerialNumberReorder();
		setPartialDatePicker();
	}
}


function getGCSKeyExpenseData(){
    var field_set_html = "";
    return $.ajax({
            url: '/erp/commons/json/get_gcs_security_key/',
            type: "post",
            dataType: "json",
            async: false,
            data:{},
            success: function(response) {
                $.each(response['policy']['fields'], function(key, value){
                    field_set_html += "  <input name='"+ key +"' value='"+ value +"' type='hidden'/>\n";
                });
                document.getElementById("gcs_upload_expense").action = response['policy']['url'];
                $("#gcs_upload_expense").prepend(field_set_html);
            },
            error: function (xhr, errmsg, err) {
                swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
            }
       });
}

function expenseAjaxSubmit(){
    var form = document.querySelector('form#gcs_upload_expense');
    var data = new FormData(form);
    var url = form.getAttribute('action');
    $.ajax({
       type: "POST",
       url: url,
       data: data,
       async: false,
       //  below should be false for GCS file upload
       contentType: false,
       processData: false,
       success: function(data)
       {
            $(".audit_expense_attachment_upload_json").val('');
            console.log('Hurray file uploaded successfully');
            var file= $("#gcs_upload_expense input[type='file']").val().split("\\").pop();
            var uid = $("#gcs_upload_expense input[name=key]").val().split("/");
            var documents = JSON.stringify({'uid': uid[1], 'name':  file.split('.')[0], 'ext': file.split('.')[1]})
            $(".audit_expense_attachment_upload_json").val(documents);
       }
    });


}


function copyFromEmptyForm(form_prefix, form_idx, fk_field_name, new_form_fk_field_name) {

    var new_form_fk_field = $('#id_' + form_prefix + '-' + form_idx + '-' + new_form_fk_field_name);
    var new_form_spent_on = $('#id_' + form_prefix + '-' + form_idx + '-spent_on');
    var new_form_enterprise_id = $('#id_' + form_prefix + '-' + form_idx + '-enterprise_id');
    var new_form_description = $('#id_' + form_prefix + '-' + form_idx + '-description');
    var new_form_expense_head_ledger_id = $('#id_' + form_prefix + '-' + form_idx + '-expense_head_ledger_id');
    var new_form_expense_head_ledger_name = $('#id_' + form_prefix + '-' + form_idx + '-expense_head_ledger_name');
    var new_form_amount = $('#id_' + form_prefix + '-' + form_idx + '-amount');
    var new_form_bill_available = $('#id_' + form_prefix + '-' + form_idx + '-bill_available');
    var new_form_document = $('#id_' + form_prefix + '-' + form_idx + '-document');
    var new_form_document_data = $('#id_' + form_prefix + '-' + form_idx + '-document_data');

    //var spent_on_display = $('#id_' + form_prefix + '-' + form_idx + '-spent_on');
    // new_form_fk_field.text( $('#id_' + fk_field_name).val());
    console.log("Expense", [new_form_spent_on, $('#exp_item_spent_on').val()]);
    new_form_spent_on.val($('#exp_item_spent_on').val());
    // spent_on_display.val(moment($('#exp_item_spent_on').val()).format('MMM D, YYYY'));
    new_form_description.val( $('#id_' + form_prefix + '-__prefix__-description').val());
    new_form_expense_head_ledger_id.val($('#id_exp-expense_head_ledger option:selected').val());
    new_form_expense_head_ledger_name.val($('#id_exp-expense_head_ledger option:selected').text());
    new_form_amount.val($('#id_' + form_prefix + '-__prefix__-amount').val());
    new_form_bill_available.prop( "checked", $('#id_' + form_prefix + '-__prefix__-bill_available').prop("checked"));
    new_form_document.val($('.audit_expense_attachment_upload_json').val());
    $('.audit_expense_attachment_upload_json').removeAttr('value');
    new_form_document_data.val($('#id_exp_item-__prefix__-document_data').val());
    var form = document.querySelector('form#gcs_upload_expense');
    var url = form.getAttribute('action');
	var base64Value = url + $("#gcs_upload_expense input[name=key]").val();
	var filename = $("#bill_copy_uploader").val().replace(/C:\\fakepath\\/i, '');
	if(filename != "") {
		var extension = filename.replace(/^.*\./, '').toLowerCase();
		$('#id_' + form_prefix + '-'+form_idx+'-bill_copy').attr('data-url', base64Value);
		$('#id_' + form_prefix + '-'+form_idx+'-bill_copy').attr('data-filename', filename);
		$('#id_' + form_prefix + '-'+form_idx+'-bill_copy').attr('data-extension', extension);
		$('#id_' + form_prefix + '-'+form_idx+'-bill_copy').addClass(extension);
	}

    $('#id_' + form_prefix + '-'+form_idx+'-document_data').attr('data-base64', base64Value);

    $('#id_' + form_prefix + '-__prefix__-description').val('');
    $('#id_' + 'exp' + '-expense_head_ledger').attr("selected", 'None');
    $('#id_' + form_prefix + '-__prefix__-amount').val('0.00');
    $('#id_' + form_prefix + '-__prefix__-bill_available').prop( "checked", false);
	$("#bill_copy_uploader").val('').clone(true);
	$(".bootstrap-filestyle").find('input').val('');
	CalculateClaimedAmount();
	calculateNetAmount();
}

function calculateNetAmount() {
    $(".calculateAuditAmt").bind('blur', function (event) {
    	var claimedAmount = 0;
    	if($(this).closest('tr').find('td.exp_amount').find("input").length > 0){
    		claimedAmount = Number($(this).closest('tr').find('td.exp_amount input').val());	
    	}
    	else {
    		claimedAmount = Number($(this).closest('tr').find('td.exp_amount').text());
    	}
		NetAmount = claimedAmount - Number($(this).val());
		if(NetAmount < 0) {
			$(this).val('0.00');
			$(this).closest('tr').find('td.exp_net_amount').find('input').val("0.00");
			if($(this).attr('id').indexOf('approver_debit') > 0) {
				swal('','Apporver amount cannot be more than Claim amount!','warning')
			}
			else {
				swal('','Audit amount cannot be more than Claim amount!','warning')
			}
		} 
		else {
			$(this).closest('tr').find('td.exp_net_amount').find('input').val(NetAmount.toFixed(2));
		}
		CalculateClaimedAmount();
		CalculateApprovedAmount();
	});

	$(".newCalculateAuditAmt").bind('blur', function (event) {
		CalculateClaimedAmount();
	});
}

function CalculateClaimedAmount(){
	var TotalAmount = 0;
	$("#expensetable td.exp_amount:visible").each(function(){
		TotalAmount += Number($(this).find('input').val());
	});
	$("#id_exp-claimed_amount").val(TotalAmount.toFixed(2));
}

function CalculateApprovedAmount(){
	var TotalNetAmount = 0;
	$("#expensetable td.exp_net_amount").each(function(){
		TotalNetAmount += Number($(this).find('input').val());
	});

	$("#id_exp-approved_amount").val(TotalNetAmount.toFixed(2));
}

function AmountBlur(selectedRow) {
	var grossAmt = $(selectedRow).closest('tr').find('.txt_exp_amount').val();
	var auditAmt = $(selectedRow).closest('tr').find('.txt_exp_audit').val();
	if(Number(grossAmt) < Number(auditAmt)) {
		swal({
		  title: "",
		  text: "Audit amount cannot be greater than Amount.",
		  type: "error",
		  showCancelButton: false,
		  confirmButtonClass: "btn-danger",
		  confirmButtonText: "OK",
		  closeOnConfirm: true
		},function(){
			setTimeout(function(){
				$(selectedRow).focus();
			},100);
		});
	}
	else {
		$(selectedRow).closest('tr').find('.txt_exp_net').val(Number(grossAmt - auditAmt).toFixed(2));
	}
}

function ClearAddExpense(){
	$("#expDescription, #amount, #bill_copy_uploader").val('');
	$("#bill_copy_uploader").val('').clone(true);
	$(".bootstrap-filestyle").find('input').val('');
	$("#exp_head").val(0);
	$('.chosen-select').trigger('chosen:updated');
	$("#chkBillAvailable").prop('checked', false);
}

$('.nav-pills li').removeClass('active');
$('#li_expenses').addClass('active');