$(function() {
	$("#cmdUpload").bind("click", function () {
        if(!$('#fileUpload').val()){
                swal({
				title:"File is required",
				text: "Please select CSV file. ",
				type: "warning",
				showCancelButton: false,
				confirmButtonColor: "#209be1",
				confirmButtonText: "OK",
				closeOnConfirm: true
			});
            return false;
        }
        var regex = /^([a-zA-Z0-9\s_\\.\-:])+(.csv|.txt)$/;
        if (regex.test($("#fileUpload").val().toLowerCase())) {
            if (typeof (FileReader) != "undefined") {
                var reader = new FileReader();
                reader.onload = function (e) {
                    var table = $("<table />");
                    var lines = this.result.split('\n');
                    var partyList = []
                    var as_on;
                    for(var line = 1; line < lines.length-1; line++){
                        party_data = lines[line].splitCSV();
                        if(line == 1 && party_data.length != 20) {
                            swal({
                                title:"Party Import Status",
                                text: "Please upload the valid file or check proper column names",
                                type: "warning",
                                showCancelButton: false,
                                confirmButtonColor: "#209be1",
                                confirmButtonText: "OK",
                                closeOnConfirm: true
                            });
                            $(".tr-file-upload").find("input[type='file']").val('').clone(true);
                            $(".tr-file-upload").find("input[type='text']").val('');
                            return false;
                        }
                        if ($('#agent').val() == 'Windows'){
                            if (line !=0){
                                if (party_data[15].length > 8){
                                    var date_str = party_data[15].trim()
                                    date_str = date_str.replace(/\//g, "-");
                                    var array = new Array();
                                    array = date_str.split('-');
                                    if(array[2].trim().length >=4){
                                        var newDate = (array[2] + "-" + array[0] + "-" + array[1]);;
                                        as_on = newDate
                                    }
                                    else{
                                        as_on = date_str
                                    }
                                }
                                else {
                                    as_on = party_data[15];
                                }
                            }
                            else{
                                as_on = party_data[15];
                            }
                        }
                        else {
                            as_on = party_data[15];
                        }
                        var partyobj = {
                            "code":party_data[0], "name":party_data[1], "address_1":party_data[2],
                            "address_2":"", "city":party_data[3], "state":party_data[4],
                            "country_code": party_data[5], "pin_code": party_data[6],
                            "contact":party_data[7], "phone":party_data[8], "email":party_data[9],
                            "fax_no":party_data[10],
                            "payment_credit_days":party_data[11],
                            "payable_opening":party_data[12], "receipt_credit_days":party_data[13],
                            "receivable_opening": party_data[14], "as_on":as_on, "gst_category": party_data[16],
                            "port": party_data[17], "gstin": party_data[18], "pan": party_data[19]
                            };
                        var partyJSON = JSON.stringify(partyobj)
                        partyList.push(partyJSON);                        
                    }
                    $('#loading').show();
                    $.ajax({
                        url : "/erp/masters/json/party/import/",
                        type : "POST",
                        dataType: "json",
                        data : {'import_party_data[]': partyList},
                        success : function(response) {
                            if(response['is_valid_file_headers'] == false){
                                 swal({
                                    title:"Party Import Status",
                                    text: response['message'],
                                    type: "warning",
                                    showCancelButton: false,
                                    confirmButtonColor: "#209be1",
                                    confirmButtonText: "OK",
                                    closeOnConfirm: true
                                });
                                $(".tr-file-upload").find("input[type='file']").val('').clone(true);
                                $(".tr-file-upload").find("input[type='text']").val('');
                            }
                            else{
                                if(response['failed_items'].length==0){
                                    swal({
                                        title:"Party Import Status",
                                        text: response['message'],
                                        type: "warning",
                                        showCancelButton: false,
                                        confirmButtonColor: "#209be1",
                                        confirmButtonText: "OK",
                                        closeOnConfirm: true
                                    });
                                    $(".tr-file-upload").find("input[type='file']").val('').clone(true);
                                    $(".tr-file-upload").find("input[type='text']").val('');
                                }
                                else {
                                    $("#modal_importsupplier").modal("hide");
                                    $("#import_message").html(response['message'])
                                    $("#import_party_status_modal").modal("show");
                                    $("#party_failed_import_table").find("tr:gt(0)").remove();
                                    $.each(response['failed_items'], function(i, item) {
                                        var row = "<tr bgcolor='#ececec' border='0' align='center' style='font-size:11px; font-weight:normal;'>"+
                                        "<td>"+item['code']+ "</td><td>"+item['name']+ "</td><td align='left'>" +
                                        item['address_1']  + "</td><td align='left'>" +
                                        item['city']  + "</td><td align='left'>" +
                                        item['state']  + "</td><td align='left'>" +
                                        item['country_code']  + "</td><td align='left'>" +
                                        item['pin_code']  + "</td><td align='left'>" +
                                        item['contact']  + "</td><td align='right'>" +
                                        item['phone']  + "</td><td align='left'>" +
                                        item['email']  + "</td><td align='right'>" +
                                        item['fax_no']  + "</td><td align='left'>" +
                                        item['payment_credit_days']  + "</td><td align='left'>" +
                                        item['payable_opening']  + "</td><td align='left'>" +
                                        item['receipt_credit_days']  + "</td><td align='left'>" +
                                        item['receivable_opening']  + "</td><td align='left'>" +
                                        item['as_on']  + "</td><td align='left'>" +
                                        item['gst_category']  + "</td><td align='left'>" +
                                        item['port']  + "</td><td align='left'>" +
                                        item['gstin']  + "</td><td align='left'>" +
                                        item['pan']  + "</td><td align='left'>" +
                                        item['error']  + "</td></tr>"
                                        $('#party_failed_import_table').append(row);
                                    });
                                }
                            }
                            $('#loading').hide();
                            ga('send', 'event', 'Party', 'Import', $('#enterprise_label').val(), partyList.length-response.failed_items.length);
                        }, error : function(xhr,errmsg,err) {
                            $('#loading').hide();
                            console.log(xhr.status + ": " + xhr.responseText);
                        }
                    });
                }
                reader.readAsText($("#fileUpload")[0].files[0]);
            } else {
                alert("This browser does not support HTML5.");
            }
        } else {
			swal({
				title:"Invalid File Format",
				text: "Please upload a valid CSV file. ",
				type: "warning",
				showCancelButton: false,
				confirmButtonColor: "#209be1",
				confirmButtonText: "OK",
				closeOnConfirm: true
			});
        }
    });

    $('#modal_importsupplier').on('hidden.bs.modal', function () {
        $("#fileUpload").val('').clone(true);
        $("#fileUpload").next('div').find('input').val('');
    });
});

function gstCategoryChangeEvent(){
    var gstCategory = $("#id_gst_category-__prefix__-make_choices").val();
    if(["5", "6", "7", "9"].indexOf(gstCategory) != -1){
        $("#party-port-contianer").removeClass("hide");
    }
    else {
        $("#party-port-contianer").addClass("hide");
    }
    if(["3", "4", "5"].indexOf(gstCategory) == -1){
        $(".gstin_mandate").removeClass("hide");
    }
    else {
        $(".gstin_mandate").addClass("hide");
    }
    $("#modalPartyDetails .error-border").removeClass("error-border");
    $("#modalPartyDetails .custom-error-message").remove();
}

$(document).ready(function(){
    var url=window.location.href;
    url = url.substring(7)
    window.urlpath = url.substring(0,url.indexOf('/'));
    $('#cmdupdate').hide();
    $('#cmddelete').hide();
    $('#code').prop('disabled', false);
    $('#code').focus();

    $('#import_party_status_modal').on('hidden.bs.modal', function () {
        window.location = "/erp/masters/party_list/";
    });
    $("#id_is_supplier").change(function(){
        SupplierChange();
    });

    $("#id_is_customer").change(function(){
        CustomerChange();
    });
});

function showImportmaterial() {
    $("#modal_importsupplier").modal('show');
}

function AddNewParty() {
    $(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();
    $('#modalPartyDetails').modal('show');
}

function hideImportmaterial() {
    $("#modal_importsupplier").modal('hide');
}

function loadparty() {
    $("#loading").show();
    $.ajax({
        url: "/erp/masters/party_list/",
        type: "post",
        datatype:"json",
        data: {'is_inline_edit': true},
        success: function(response){
            $("#dettable").find("tr:gt(0)").remove();
            $.each(response, function(i, item) {
                var edit =`<a role="button" class="edit_link_code" onclick="editPartyRow('${item.party_id}')">${item.name.trim()}</a>`;
                var party_type = "";
                if($("#is_edit_enabled").val() == 1 ) {
                    var inlineEditIcons = `<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Inline Edit' 
                                        onclick='editInlineParty(this);'>
                                                <i class='fa fa-pencil-square-o'></i>
                                            </span>`;
                    var partyDeleteIcons = `<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Delete' onclick="deleteParty('${item.party_id}')">
                                                <i class='fa fa-trash-o' aria-hidden='true'></i>
                                            </span>`;
                }
                else {
                    var inlineEditIcons = ``;
                    var partyDeleteIcons = ``;
                }
                var otherPartiesContact  =`<a class="table-inline-icon hide" style="position: absolute; margin-top: -5px; padding: 6px 5px 4px 9px; margin-left: -5px;" data-tooltip="tooltip" onclick="viewOtherPartyContacts('${item.party_id}', this)" title="View other Contacts">
                                            <i class="fa fa-address-book-o" aria-hidden="true" style="font-size: 16px;"></i>
                                          </a>`;
                if (item.config_flags==2 || item.config_flags==3 ){ party_type = "Supplier" }
                else if (item.config_flags==4 || item.config_flags==5){ party_type = "Customer" }
                else if (item.config_flags==6 || item.config_flags==7){ party_type = "Customer/Supplier" }
                var country = "";
                var gst_category = "";
                var port = "";
                var gstin = "";
                var pan = "";
                if(item.country_code != "None" && item.country_code != null){
                    country = item.country_code;
                }
                if(item.gst_category_id == 1) {
                    gst_category = 'Registered Business';
                }
                else if(item.gst_category_id == 2) {
                    gst_category = 'Registered Business - Composition';
                }
                else if(item.gst_category_id == 3) {
                    gst_category = 'Unregistered Business';
                }
                else if(item.gst_category_id == 4) {
                    gst_category = 'Unregistered Business';
                }
                else if(item.gst_category_id == 5) {
                    gst_category = 'Overseas';
                }
                else if(item.gst_category_id == 6) {
                    gst_category = 'Special Economic Zone (SEZ)';
                }
                else if(item.gst_category_id == 7) {
                    gst_category = 'Deemed Export';
                }
                else if(item.gst_category_id == 8) {
                    gst_category = 'Tax Deductor';
                }
                else if(item.gst_category_id == 9) {
                    gst_category = 'SEZ Developer';
                }
                if (item.port != 'None' && item.port != null){
                    port = item.port;
                }
                if (item.gstin != 'None' && item.gstin != null){
                    gstin = item.gstin;
                }
                if (item.pan != 'None' && item.pan != null){
                    pan = item.pan;
                }
                var row = ` <tr data-party-id='${item.party_id}'>
                                <td>
                                    <span class='td-party-code'>${item.code}</span>
                                </td>
                                <td class='td-party-name'>
                                    <span style="display: inline-block;">
                                        ${edit}
                                    </span>    
                                    <span style="width: 140px; display: inline-block; float: right; text-align: right;">
                                        ${inlineEditIcons}
                                        <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit' onclick="editPartyRow('${item.party_id}')" >
                                            <i class='fa fa-pencil'></i>
                                        </span>
                                        <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit in New Tab' onclick="editPartyRow('${item.party_id}', '_blank')" >
                                            <i class='fa fa-external-link'></i>
                                        </span>
                                        ${partyDeleteIcons}
                                        <span class='btn transparent-btn party-inline-action hide' data-tooltip='tooltip' title='Cancel' onclick="CancelInlineEdit()" style="padding: 2px 8px; font-size: 12px; float: right; border-color: #dd4b39">
                                            <i style="color: #dd4b39" class='fa fa-times' aria-hidden='true'></i>
                                        </span>
                                        <span class='btn transparent-btn party-inline-action save-party-inline hide' data-tooltip='tooltip' title='Update' style="padding: 2px 8px; font-size: 12px; float: right; margin-right: 8px;">
                                            <i class='fa fa-check' aria-hidden='true'></i>
                                        </span>
                                    </span>
                                </td>
                                <td hidden='hidden' class='td-party-address'>${item.address_1} ${item.address_2}</td>
                                <td hidden='hidden' class='td-party-city'>${item.city}</td>
                                <td hidden='hidden' class='td-party-state'>${item.state}</td>
                                <td hidden='hidden' class='td-party-country'>${country}</td>
                                <td class='edit edit-current contact_person'>
                                    <span style='width: 85%; display: inline-block;'>${item.primary_contact_name}</span>
                                    ${otherPartiesContact}
                                </td>
                                <td class='edit edit-current contact_number'>${item.primary_phone_no}</td>
                                <td class='edit edit-current contact_email text-lowercase'>${item.primary_email}</td>
                                <td class='edit edit-current contact_fax text-lowercase'>${item.primary_fax_no}</td>
                                <td hidden='hidden' class='exclude_export td_party_id'>${item.party_id}</td>
                                <td hidden='hidden' class='hdn_party_type exclude_export td_party_config'>${item.config_flags}</td>
                                <td class='text-left edit edit-current party_type td_party_type'>${party_type}</td>
                                <td class='text-center edit edit-current supplier_cr td_payment_days'>${item.payment_credit_days}</td>
                                <td class='text-center edit edit-current customer_cr td_receipt_days'>${item.receipt_credit_days}</td>
                                <td class='text-center edit edit-currency currency td-party-currency exclude_export' hidden='hidden'>${item.currency}</td>
                                <td hidden='hidden'>${gst_category}</td>
                                <td hidden='hidden'>${port}</td>
                                <td hidden='hidden'>${gstin}</td>
                                <td hidden='hidden'>${pan}</td>
                            </tr>`;
                $('#dettable tbody').append(row).addClass('tbl');
            });
            if($("#is_edit_enabled").val() == 1 ) {
    			generateInlineEdit();
    			saveIndividualParty();
            }
			TableHeaderFixed();
            if ($("#dettable tr").length == 1){
                var row = "";
            }
            $('#dettable').append(row).addClass('tbl');
            $("#loading").hide();
        },
        error : function(xhr,errmsg,err) {
            console.log(xhr.status + ": " + xhr.responseText);
            $("#loading").hide();
        }
    });
}

function editInlineParty(ele) {
    $(ele).closest('tr').find('.contact_person').click();
    $(ele).closest(".table-inline-icon").addClass("show_save_button");
}

var oTable;
var oSettings;

function TableHeaderFixed() {
    oTable = $('#dettable').DataTable({
            fixedHeader: false,
            "scrollY": Number($(document).height() - 230),
            "scrollX": true,
            "pageLength": 50,
            "search": {
                "smart": false
            }
    });
	oTable.on("draw",function() {
		var keyword = $('#dettable_filter > label:eq(0) > input').val();
		$('#dettable').unmark();
		$('#dettable').mark(keyword,{});
        setHeightForTable();
        listTableHoverIconsInit('dettable');
	});
	oTable.on('page.dt', function() {
        $('html, body').animate({
	       scrollTop: $(".dataTables_wrapper").offset().top - 15
        }, 'slow');
        listTableHoverIconsInit('dettable');
	});
    oSettings = oTable.settings();
    listTableHoverIconsInit('dettable');
    $( window ).resize();
}

function EditParty(selectedRow) {
    var currentTr = selectedRow.closest('tr');
    var party_config = parseInt(currentTr.find(".td_party_config").val());
    var config_flags = 0;
    if(!party_config % 2) {
        config_flags += 1;
    }

    if(currentTr.find(".td_party_type").text().indexOf("Supplier") >= 0) {
        config_flags += 2;
    }

    if(currentTr.find(".td_party_type").text().indexOf("Customer") >= 0) {
        config_flags += 4;
    }
    partyDetails = {config_flags:config_flags, 
                    payment_credit_days: Number(currentTr.find(".td_payment_days").text()),
                    receipt_credit_days: Number(currentTr.find(".td_receipt_days").text()),
                    currency: Number(currentTr.find(".td-party-currency").text()),
                    party_id: Number(currentTr.find(".td_party_id").text())
                }

    partyContactDetailsObj = [{
        'sequence_id': 1,
        'name': currentTr.find(".contact_person").text(),
        'email': currentTr.find(".contact_email").text(),
        'phone_no': currentTr.find(".contact_number").text(),
        'fax_no': currentTr.find(".contact_fax").text(),
        'is_whatsapp': 0,
        'is_deleted': 0
    }];
    $(".party_edited").removeClass("party_edited");
    $(".edit_fixed").removeClass("edit_fixed");
    $.ajax({
        url : "/erp/masters/json/party/update/",
        type : "POST",
        dataType: "json",
        data :  {party_details:JSON.stringify(partyDetails), party_contact_details: JSON.stringify(partyContactDetailsObj), inline_update: true},
        success : function(json) {
            if(json['response_message'] == "Session Timeout") {
                location.reload();
                return;
            }
            if(json['message'].indexOf("1062") != -1){
                swal("","Party already exists","error");
            }
            else {
                if(json['message'].indexOf("Success") > 0) {
                    swal("",json['message'],'success');
                }
                else {
                    swal("",json['message'],'warning');   
                }
                ga('send', 'event', 'Party', 'Update', $('#enterprise_label').val(), 1);
            }
            $("#loading").hide();
        },
        error : function(xhr,errmsg,err) {
          console.log(xhr.status + ": " + xhr.responseText);
          $("#cmdupdate").val('Update').removeClass('btn-processing');
          $("#loading").hide();
        }
    });
}

function SupplierChange() {
	if($("#id_is_supplier").is(":checked")){
		$("#div_supplier").removeClass('hide');
	}
	else {
		$("#div_supplier").addClass('hide');
		//$("#rec_cre_days").val(0);
	}
}

function CustomerChange() {
	if($("#id_is_customer").is(":checked")){
		$("#div_customer").removeClass('hide');
	}
	else {
		$("#div_customer").addClass('hide');
		//$("#pay_cre_days").val(0);
	}
}

var SelectedOldValue = "";
var ShowInfoMessage = true;
function generateInlineEdit(){
	$('#dettable tbody td.edit').click(function(){
		if($(this).hasClass('edit-current')){
			if($(document).find('.party_edited').length == 0){ //if no changes made in selected field
				CancelInlineEdit();
				var selectedField = $(this);
				if($(this).closest('tbody').find('tr.edit_fixed').length == 0){ //if one field is selected
					$(this).closest('tr').addClass('edit_fixed');
                    SelectedOldValue += $(this).closest('tr').find(".td_party_id").text().trim()+"#@#";
					$(this).closest('tr').find('td.edit').each(function(){
                        if($(this).hasClass("contact_person")) {
                            SelectedOldValue += $(this).find("span").text().trim()+"#@#";
                            var currentValue = $(this).find("span").text().trim();
                        }
                        else {
                            SelectedOldValue += $(this).text().trim()+"#@#";
                            var currentValue = $(this).text().trim();    
                        }
						
						$(this).closest('tr').find(".party_type").removeClass('edit-current');
						if($(this).hasClass('contact_person')) {
							var setAttr = "<input type='text' maxlength='50' class='editable-textbox' value='"+currentValue+"' onkeypress=\"validateStringOnKeyPress(this,event,'alphanumeric');\" onblur=\"validateStringOnBlur(this,event,'alphanumeric');\" />";
						}
						else if($(this).hasClass('contact_number')) {
							var setAttr = "<input type='text' maxlength='30' class='editable-textbox' value='"+currentValue+"' onkeypress=\"validateStringOnKeyPress(this,event,'ph_number');\" onblur=\"validateStringOnBlur(this,event,'ph_number');\" />";
						}
						else if($(this).hasClass('contact_email')) {
							var setAttr = "<input type='text' maxlength='75' class='editable-textbox editable-email' value='"+currentValue+"' onkeypress=\"validateStringOnKeyPress(this,event,'alphaSpecialChar');\" onblur=\"validateStringOnBlur(this,event,'alphaSpecialChar');\" />";
						}
                        else if($(this).hasClass('contact_fax')) {
                            var setAttr = "<input type='text' maxlength='30' class='editable-textbox editable-fax' value='"+currentValue+"' onkeypress=\"validateStringOnKeyPress(this,event,'ph_number');\" onblur=\"validateStringOnBlur(this,event,'ph_number');\" />";
                        }
						else if($(this).hasClass('supplier_cr')) {
							if($("#isSupplier").is(':checked')){
								var setAttr = "<input type='text' maxlength='5' class='editable-textbox' value='"+currentValue+"' onfocus='setNumberRangeOnFocus(this,6,0,true)' />";
							}
							else {
								var setAttr = "<input type='text' maxlength='5' class='editable-textbox' value='"+currentValue+"' onfocus='setNumberRangeOnFocus(this,6,0,true)' disabled />";
							}
						}
						else if($(this).hasClass('customer_cr')) {
							if($("#isCustomer").is(':checked')){
								var setAttr = "<input type='text' maxlength='5' class='editable-textbox' value='"+currentValue+"' onfocus='setNumberRangeOnFocus(this,6,0,true)' />";
							}
							else {
								var setAttr = "<input type='text' maxlength='5' class='editable-textbox' value='"+currentValue+"' onfocus='setNumberRangeOnFocus(this,6,0,true)' disabled />";
							}
						}
						else if($(this).hasClass('party_type')) {
							if($(this).text() == 'Supplier'){
								var setAttr = "<span style='font-size: 18px;'><input type='checkbox' id='isSupplier' checked /> S &nbsp;&nbsp;&nbsp; <input type='checkbox' id='isCustomer' /> C</span>";
							}
							else if($(this).text() == 'Customer'){
								var setAttr = "<span style='font-size: 18px;'><input type='checkbox' id='isSupplier' /> S &nbsp;&nbsp;&nbsp; <input type='checkbox' id='isCustomer' checked /> C</span>";
							}
							else if($(this).text() == 'Customer/Supplier'){
								var setAttr = "<span style='font-size: 18px;'><input type='checkbox' id='isSupplier' checked /> S &nbsp;&nbsp;&nbsp; <input type='checkbox' id='isCustomer' checked /> C</span>";
							}
							else {
								var setAttr = "<span style='font-size: 18px;'><input type='checkbox' id='isSupplier' /> S &nbsp;&nbsp;&nbsp; <input type='checkbox' id='isCustomer' /> C</span>";
							}
							
						}
						$(this).html('').append(setAttr);
						InlineKeypressValidation();
					});
                    $(this).closest('tr').find(".edit-party-inline").addClass("hide");
                    $(this).closest('tr').find(".party-inline-action").removeClass("hide");
					selectedField.find('input').focus();
					var tmpStr = selectedField.find('input').val();
					selectedField.find('input').val('');
					selectedField.find('input').val(tmpStr);
                    $(this).closest('tr').find(".table-inline-icon").addClass("show_save_button");
				}
			}
		}
		if(ShowInfoMessage){
			$(".esc-info-message").show();
			setTimeout(function(){
				$(".esc-info-message").fadeOut(1000);
			},3000);
		}
		ShowInfoMessage = false;
	});
}

function saveIndividualParty(){ 
	$(".save-party-inline").click(function(){
		var emailRegexInline = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
		var isEmailCorrectInline = emailRegexInline.test($(".editable-email").val());
		if(isEmailCorrectInline || $(".editable-email").val() == "") {
			$(this).closest('tr').find('td.edit').each(function(){
				var currentValue = $(this).find('input').val();
                var party_id = $(this).closest("tr").find('.td_party_id').text();
				if($(this).hasClass('party_type')) {
					if($("#isSupplier").is(':checked') && $("#isCustomer").is(':checked')) {
						currentValue = "Customer/Supplier";
						$(".hdn_party_type").text(6);
					}
					else if($("#isSupplier").is(':checked')) {
						currentValue = "Supplier";
						$(".hdn_party_type").text(2);
					}
					else if($("#isCustomer").is(':checked')) {
						currentValue = "Customer";
						$(".hdn_party_type").text(4);
					}
					else {
						currentValue = "";
						$(".hdn_party_type").text('');
					}
				}
                if($(this).hasClass('contact_person')) {
                    var contactDetailRow = `<span style="width: 85%; display: inline-block;">${currentValue}</span> 
                                <a class="table-inline-icon" style="position: absolute; margin-top: -5px; padding: 6px 5px 4px 9px; margin-left: -5px;" data-tooltip="tooltip" onclick="viewOtherPartyContacts('${party_id}', this)" title="View other Contacts">
                                    <i class="fa fa-address-book-o" aria-hidden="true" style="font-size: 16px;"></i>
                                </a>`;
                }
				$(this).html('').html(currentValue);
                $('.edit_fixed').find(".edit.contact_person").html(contactDetailRow);
			});
			EditParty($(this));
            $(".edit-party-inline").removeClass("hide");
            $(".party-inline-action").addClass("hide");
            $("#dettable").find(".table-inline-icon").removeClass("show_save_button");
            $("#loading").show();
		}
		else {
			swal('','Invalid Email Address','error')
		}
	});
}

function InlineKeypressValidation(){	
	$(".editable-textbox").on('keydown', function(e) {
		if (e.which == 13) {
            var that = $(this);
            setTimeout(function(){
			     that.closest('tr').find('.save-party-inline').trigger('click');
            },50);
		}
	});
	
	$(".editable-textbox").on('input', function() {
		$(this).closest('td').addClass('party_edited');
	});
	
	$("#isSupplier, #isCustomer").change(function(){
		if($("#isSupplier").is(':checked')){
			$(".supplier_cr").find('input').removeAttr('disabled');
		}
		else {
			$(".supplier_cr").find('input').attr('disabled','').val(0);
		}
		if($("#isCustomer").is(':checked')){
			$(".customer_cr").find('input').removeAttr('disabled');
		}
		else {
			$(".customer_cr").find('input').attr('disabled','').val(0);
		}
		$(this).closest('td').addClass('party_edited');
	});
}

$(document).keyup(function(e) {
  if (e.keyCode === 27) {
	 CancelInlineEdit();
  }
});

function viewOtherPartyContacts(party_id, current){
    $("#loading").show();
    var party_name = $(current).closest("tr").find("form").text().trim();
    var party_code = $(current).closest("tr").find("td:first-child").text().trim();
    if(party_code != "") {
        party_name += ` <small>(${party_code})</small>`;
    }
    $("#other_party_list_modal").find(".party_name_title").html(party_name)
    $("#table-other-contact-details").find("tbody").html("");
    $.ajax({
        url:"erp/masters/fetch_party_contacts/",
        type:"post",
        dataType:"json",
        data:{"party_id": party_id},
        success: function(response){
            $("#loading").hide();
            $.each(response, function(i, item) {
                var badge = "";
                if(item.sequence_id == 1) {
                    var badge = `<span class="badge pull-right" style="font-weight: normal; background: #004195;">Primary</span>`
                }
                if(item.name != "" || item.phone_no != "" || item.email != "" || item.fax_no != "") {
                    var row = ` <tr>
                                    <td>${item.name} ${badge}</td>
                                    <td>${item.phone_no}</td>
                                    <td>${item.email}</td>
                                    <td>${item.fax_no}</td>
                                </tr>`;
                    $("#table-other-contact-details").find("tbody").append(row);
                }
            });
            
            if($("#table-other-contact-details").find("tbody tr").length <= 0) {
                var row = ` <tr>
                                <td class='text-center' colspan="4"><b>No Contacts Found!</b></td>
                            </tr>`;

                $("#table-other-contact-details").find("tbody").append(row);
            }
            $("#other_party_list_modal").modal("show");
            CancelInlineEdit();
        }
    });
}


function CancelInlineEdit(){
    $("#dettable").find(".table-inline-icon").removeClass("show_save_button");
	if(SelectedOldValue !="") {
		var splitedSelectedOldValue = SelectedOldValue.split("#@#");
        var contactDetailRow = `<span style="width: 85%; display: inline-block;">${splitedSelectedOldValue[1]}</span> 
                                <a class="table-inline-icon" style="position: absolute; margin-top: -5px; padding: 6px 5px 4px 9px; margin-left: -5px;" data-tooltip="tooltip" onclick="viewOtherPartyContacts('${splitedSelectedOldValue[0]}', this)" title="View other Contacts">
                                    <i class="fa fa-address-book-o" aria-hidden="true" style="font-size: 16px;"></i>
                                </a>`;
		$('.edit_fixed').find(".edit.contact_person").html(contactDetailRow);
		$('.edit_fixed').find(".edit.contact_number").text(splitedSelectedOldValue[2]);
		$('.edit_fixed').find(".edit.contact_email").text(splitedSelectedOldValue[3]);
        $('.edit_fixed').find(".edit.contact_fax").text(splitedSelectedOldValue[4]);
		$('.edit_fixed').find(".edit.party_type").text(splitedSelectedOldValue[5]);
		$('.edit_fixed').find(".edit.supplier_cr").text(splitedSelectedOldValue[6]);
		$('.edit_fixed').find(".edit.customer_cr").text(splitedSelectedOldValue[7]);
		$(".edit_fixed, .party_edited").removeClass('edit_fixed party_edited');
		SelectedOldValue="";
		$(".party_type").addClass('edit-current');
        $(".edit-party-inline").removeClass("hide");
        $(".party-inline-action").addClass("hide");
        TooltipInit();
	}
}

function constructSupplierCode(){
    setTimeout(function(){
        var supplier_code = $('#code').val().replace(/ /g,"_").toUpperCase();
        $('#code').val(supplier_code);
    },10);
}

function deleteParty(code) {
    var partyName = $(`#dettable tr[data-party-id='${code}']`).find(".td-party-name").text().trim();
    swal({
      title: "Are you sure?",
      text: "Do you want to delete the party <b>'"+partyName+"'</b>?",
      type: "warning",
      showCancelButton: true,
      confirmButtonColor: "#209be1",
      confirmButtonText: "Yes, delete it!",
      closeOnConfirm: true
    },
    function(){
        $.ajax({
            url : "/erp/masters/json/web/party/delete/",
            type : "POST",
            dataType: "json",
            data: {party_id:code},
            success : function(json) {
                console.log(json)
                setTimeout(function(){
                    if (json==0) {
                        swal({
                            title: "",
                            text: "Party Deleted Successfully",
                            type: "success",
                            allowOutsideClick: false,
                            allowEscapeKey: false
                        },
                        function(){
                            location.reload();
                        });
                    }
                    else if (json==1){
                        swal("", "Some POs or GRNs have been raised against this Party.<br />Please delete those PO before deleting this Party.", "warning");
                    }
                },260);
            },
            error : function(xhr,errmsg,err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });
    });    
}