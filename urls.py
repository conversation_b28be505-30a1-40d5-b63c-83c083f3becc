# patterns, url# include,
import os

from django.conf.urls import *
from django.contrib.staticfiles.urls import staticfiles_urlpatterns

from erp.auth.views import login, logout, renderLoginPage, json_login, json_logout, renderHomePage
from erp.commons import json_api
from erp.commons.json_api import getMailID, mailPDF
from erp.views import keepSessionAlive, populateTagOptions, populateMakeOptions, getPreviousClosureDate

__author__ = 'kalaivanan'

site_media = os.path.join(os.path.dirname(__file__), 'site_media')
urlpatterns = patterns(
       '',
   url(r'^site_media/(?P<path>.*)$', 'django.views.static.serve', {'document_root': site_media}),
   url(r'^erp/logout/$', logout),  # logout
   url(r'^erp/user/json/login_api/$', json_login),
   url(r'^erp/user/json/logout_api/$', json_logout),
   # Home page urls
   url(r'^erp/login/$', login),  # login handler
   url(r'^erp/home/<USER>', renderHomePage),  # home page
   url(r'^erp/public/', include('erp.public.urls')),
   url(r'^erp/auth/', include('erp.auth.urls')),
   url(r'^erp/commons/', include('erp.commons.urls')),
   url(r'^erp/accounts/', include('erp.accounts.urls')),
   url(r'^erp/auditing/', include('erp.icd.urls')),
   url(r'^erp/stores/', include('erp.stores.urls')),
   url(r'^erp/purchase/', include('erp.purchase.urls')),
   url(r'^erp/masters/', include('erp.masters.urls')),
   url(r'^erp/expenses/', include('erp.expenses.urls')),
   url(r'^erp/admin/', include('erp.admin.urls')),
   url(r'^erp/sales/', include('erp.sales.urls')),
   url(r'^erp/reports/', include('erp.reports.urls')),
   url(r'^erp/hr/', include('erp.hr.urls')),
   url(r'^erp/migration/', include('migration.urls')),
   url(r'^erp/json/dummy/$', keepSessionAlive),
   url(r'^erp/json/get_previous_closure_date/$', getPreviousClosureDate),
   url(r'erp/json/populate_tags/$', populateTagOptions),
   url(r'erp/json/populate_makes/$', populateMakeOptions),
   url('erp/firebase-messaging-sw.js', json_api.fire_base_messaging_sw),
   url(r'erp/json/getMailid/$', getMailID),
   url(r'erp/json/send_mail/$', mailPDF),
   url(r'production/', include('erp.production.urls')),
   url(r'voucher/', include('util.urls')),
   url(r'^$', renderLoginPage)  # login
)
urlpatterns += staticfiles_urlpatterns()
