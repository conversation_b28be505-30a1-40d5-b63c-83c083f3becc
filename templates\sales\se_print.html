<html>
	<link type="text/css" rel="stylesheet" href="/site_media/css/styles/bootstrap.css?v={{ current_version }}">
	<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<style>
	body{		
		font-family: 'Roboto';
		color: #000;
		margin-top: 50px;
		font-size:11pt;
		line-height:19px;
		word-break: break-all;
	}	
	img{
		width:20%;
		margin-top: 0px;
	}
	table{
		width:100%;
	}
	table, th, td {
		border: 1px solid black;
		border-collapse: collapse;
	}
	th, td {
		text-align: center;
		line-height: 22px;
		font-size:11pt;
	}
	hr{
		border: 1px solid #000;
		width:100%;
	}
	.page_tit{
		font-size: 19pt;
		margin-left: -160px;
	}
	.estimation_date{
		margin-left: -90px;
    	font-size: 12pt;
	}	

	.reg_data {
		width: calc(60% - 1em);
	    float: left;
	    font-size: 11pt;
	}
	.reg_details {
		 float: left;
	    font-weight: bold;
	    font-size: 11pt;
	}
	.pay_data{
		 width: calc(60% - 1em);
	   	 float: left;
		 margin-left: 160px;
   		 margin-top: -19px;
		 font-size:11pt;
		 width:100%;
	}
	.pay_details {
	    width: 50%;
	    float: left;
	    font-weight: bold;
	    margin-left: 33px;
	    font-size:11pt;
	}
	.tax_detail{
		text-align:right;
		padding-right:2px;
	}
	.grand_total{
		text-align:right;
		padding-right:8px;
		padding-left:5px;
		font-weight:bold;
	}
	.conditions{
		word-break: break-all;
		text-align:justify;
	}
	.bill_container{
		margin-left:-12px;
		margin-top:-10px;
	}

	.bill_container > hr {
		margin-bottom:4px;
		margin-top:2px;
	}
	.total_words{
		text-align:left;
		padding-left:3px;
		font-size:12pt;
	}
	.address{
		margin-left:-12px;
		font-size:12pt;
	}
	.align_table{
		text-align:right;
		padding-right:5px;
	}
	.description{
		text-align:left;
	}

	.test_environment{
		text-align:right;
		border-bottom:hidden;
		border-left:hidden;
		border-right:hidden;
		font-size:11pt;
	}

	@font-face {
	        font-family: 'Roboto';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/roboto.regular.ttf) format('truetype');
	}
	
</style>
<body>
	<div class="container">
		<div class="col-sm-12">
			<div class="col-sm-8" style="font-size:13pt;">
				<img src="{{enterprise_logo}}" style="max-height: 10mm">
				<div style="margin-bottom:2px;"><b>{{ source.enterprise.name }}</b></div>
			</div>

			<div class="col-sm-4">
			     <span class="page_tit">{{ form_name }} </span>  # <span>{{ se_no }}</span><br>
				<span class="estimation_date">Estimation Date & Time <b>:{{ se_date }}</b></span>
			</div>
			<hr style="margin-bottom:4px;">
		</div>

		<div class="col-sm-12">
			<div class="col-sm-6 address">
				<div>{{ enterprise_address }}</div>
				<div><b>Ph:</b>{% if source.enterprise.primary_contact_details.contact.phone_no %} {{ source.enterprise.primary_contact_details.contact.phone_no }} {% endif %}<b>E-mail:</b>{% if source.enterprise.primary_contact_details.contact.email %} {{ source.enterprise.primary_contact_details.contact.email }} {% endif %}</div>
			</div>

			<div class="col-sm-6" style="margin-left: -30px;">
				<div>
				     {% for reg_item in se_reg_details %}
						<div class="col-sm-4 reg_details"><b>{{ reg_item.label }}</b></div>
						<div class="col-sm-8 reg_data">:  {{ reg_item.details }}</div>
					{% endfor %}
				</div><br>
			</div>
			<hr style="margin-bottom:14px;">
		</div>

		<div class="col-sm-12">
			<div class="col-sm-4 bill_container">
				<span style="font-size:14pt;"> <b> BILL TO :</b></span><hr>
				<div> <b>{{ source.customer.name }}</b><br>
					{{ source.customer.address_1 }}, {{ source.customer.address_2 }}<br>
					{{ source.customer.city }}, {{ source.customer.state }},
					{% for country in country_list %}
						{% if country.country_code == source.customer.country_code %}
							{{ country.country_name|upper }}
						{% endif %}
					{% endfor %}<br>
					{% if source.customer.primary_contact_details.contact.phone_no %}<b>Ph:</b> {{ source.customer.primary_contact_details.contact.phone_no }} {% endif %}<br>
					{% for reg_detail in source.customer.registration_details %}
						{% if reg_detail.label == "GSTIN" %}
							<b>GSTIN:</b> {{ reg_detail.details }}
						{% endif %}
					{% endfor %}
					{% for reg_detail in source.customer.registration_details %}
						{% if reg_detail.label != "GSTIN" and reg_detail.details != "" %}
							<b>{{ reg_detail.label }}:</b> {{ reg_detail.details }}
						{% endif %}
					{% endfor %}
				</div>
			</div>
			<div class="col-sm-4 bill_container">
				<div>
					<div class="pay_details"><b>Payment Terms    </b></div>
					<div class="pay_data">  :  {{ source.payment_terms }}</div>
				</div>
				<div>
					<div class="pay_details"><b>Expiry Date    </b></div>
					<div class="pay_data">  : {{ se_expiry_date }}</div>
				</div>
				<br>
			</div>
		</div><br>

		<div class="col-sm-12">
			<table class="se-print-table">
				<tr>
					<th>S.No</th>
					<th>Description</th>
					<th>HSN/SAC</th>
					<th>Quantity</th>
					<th>UOM</th>
					<th>Unit Price</th>
					<th>Disc</th>
					<th>Total</th>
				</tr>
				{% for se_item in se_item_details %}
					<tr>
						<td>{{forloop.counter}}.</td>
						<td class="description" style="width:280px; padding: 0 20px 0 5px;">
							<span>{{ se_item.material_name }}<br></span>
							{% if se_item.material_drawing_no != None and se_item.material_drawing_no != ""  %}
								<span>{{ se_item.material_drawing_no }}</span><br>
							{% endif %}
							{% if se_item.material_make != "" %} <span>{{ se_item.material_make }}</span>{% endif %}
						</td>
						<td>{{ se_item.hsn_code }}</td>
						<td class="align_table">{{ se_item.material_quantity }}</td>
						<td>{{ se_item.material_unit }}</td>
						<td class="align_table">{{ se_item.material_rate }}</td>
						<td class="align_table">{{ se_item.material_discount }}</td>
						<td class="align_table">{{ se_item.material_taxable_value }}</td>
					</tr>
				{% endfor %}
				{% for tax in se_taxes %}
					<tr>
						<td colspan="6" class="tax_detail">{{ tax.tax_name }} @ {{ tax.tax_rate }} %</td>
						<td colspan="3" class="tax_detail">{{ tax.tax_value }}</td>
					</tr>
				{% endfor %}
				{% if source.round_off != 0 %}
					<tr>
						<td colspan="6" class="text-right" style="padding-right:2px;">Round-off</td>
						<td colspan="3" class="text-right" style="padding-right:2px;">{{ source.round_off }}</td>
					</tr>
				{% endif %}
				<tr>
					<td colspan="6" class="grand_total" >Grand Total</td>
					<td colspan="3" class="grand_total">{{ source.currency }} <b>{{ source.grand_total }}</b></td>
				</tr>
				<tr>
					<td colspan="12" class="total_words"><b>Total Value ({{ source.currency }}) : </b> {{ total_in_words|upper }}</td>
				</tr>
				{% if source.special_instructions %}
					<tr>
						<td colspan="12" class="conditions"><b>Special Instructions:</b> {{ source.special_instructions }}
						</td>
					</tr>
				{% endif %}
				{% if source.notes %}
					<tr>
						<td colspan="12" class="conditions"><br>{% autoescape off %}{{ source.notes }}{% endautoescape %}
						</td>
					</tr>
				{% endif %}
				<tr>
					<td colspan="12" class="test_environment"><i>For {{ source.enterprise.name }}</i></td>
				</tr>
			</table>
		</div>

		<div class="col-sm-12" style="text-align:right;margin-top:20px;">
			<span>{{ se_approver }}</span><br>
			<span><b>Authorized Signatory</b></span>
			<hr style="margin-top:5px;">
		</div>
	</div>
</body>
</html>
