{% extends "accounts/sidebar.html" %}
{% block ledger %}
    <style>
	    .loading-main-container {
			display: block;
		}

		.exclude_draft_style {
			width: 200px; 
			float: left; 
			margin-left: -15px; 
			margin-top: 20px;
		}
		
		.daterangepicker.dropdown-menu {
			z-index: 10020;
		}
		#ledgerList tbody td:nth-child(1) {
			text-align: center;
		}

		.input-group {
			width: 100%;
		}

		.create_ledger {
			border-radius: 50px 0 0 50px !important; 
		    border-right: 1px solid #ccc;
		}

		.create_add_container a.btn-new-item {
			box-shadow: -4px 4px 5px 0 rgba(60,64,67,0.302), 0 1px 3px 1px rgba(60,64,67,0.149);
		}

		.import_csv.btn-new-item  {
			border-radius: 0 50px 50px 0;
		}

		.btn-new-item-label {
		    color: #004195;
		    padding: 6px !important;
		}

		.create_ledger:hover,
		.import_csv:hover {
			background: rgba(32, 155, 225,0.1);
			box-shadow: -4px 4px 5px 0 rgba(60,64,67,0.302), 0 1px 3px 1px rgba(60,64,67,0.149) !important;
		}

		.account-billable{
			top:15px;
		}
		.toggle-round {
		width: 90px;
	    text-align: center;
	    display: inline-block;
	    float: left;
	    border-radius: 50px;
	    padding:  4px;
	}
	.toggle-round.on {
		background: #004195;
		color: #FFF;
	}
	.toggle-round.on {
    background: #004195;
    color: #FFF;
}
.toggle-round {
    width: 90px;
    text-align: center;
    display: inline-block;
    float: left;
    border-radius: 50px;
    padding: 4px;
}
		#activeStatus input:not(:checked) ~ .toggle-round.off {
		background: #004195;
		color: #FFF;
	}
	#activeStatus input:not(:checked) ~ .toggle-round.on {
		background : none;
		color: #777;
	}
		#activeStatus input:checked ~ .toggle-round.on {
		background: #004195;
		color: #FFF;
	}

	</style>
	<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
	<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/utility.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/erp.js?v={{ current_version }}"></script>
	<script type="text/javascript" src="/site_media/js/ledger.js?v={{ current_version }}"></script>

	<div class="right-content-container">
		<div class="page-title-container">
			<span class="page-title">Ledger</span>
		</div>
		<input type="hidden" class="next_opening_date" id="next_opening_date" name="next_opening_date" value="{{ next_opening_date|date:'M d, Y' }}"/>
		<div class="col-lg-12">
			<div class="page-heading_new">
				<span class="page_header"></span>
				{% if access_level.edit %}
					<div class="create_add_container">
						<a class="btn btn-new-item pull-right import_csv" onclick="javascript:showImportVoucher();" data-tooltip="tooltip" title="Import Trial Balance">
							<i class="fa fa-upload" aria-hidden="true"></i>
							<span class="btn-new-upload-label"></span>
						</a>
						<a data-toggle="tab" href="#tab2" class="btn btn-new-item pull-right create_ledger" data-tooltip="tooltip" title="Create New Ledger">
							<i class="fa fa-plus" aria-hidden="true"></i>
							<span class="btn-new-item-label"></span>
						</a>
					</div>
				{% else %}	
					<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in Accounts Module. Please contact the administrator.">
						<a class="btn btn-new-item pull-right import_csv disabled">
							<i class="fa fa-upload" aria-hidden="true"></i>
							<span class="btn-new-upload-label"></span>
						</a>
						<a class="btn btn-new-item pull-right create_ledger disabled">
							<i class="fa fa-plus" aria-hidden="true"></i>
							<span class="btn-new-item-label"></span>
						</a>
					</div>
				{% endif %}
				<span class="right-side-icon">
					<a href="/erp/accounts/ledger/" class="btn btn-add-new pull-right view_ledger hide" data-tooltip="tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
				</span>
			</div>
		</div>
		<div class="col-lg-12">
			<div class="content_bg">
				<div class="contant-container-nav hide">
					<ul class="nav nav-tabs list-inline">
						<li class="active"><a href="/erp/accounts/ledger">VIEW</a></li>
						<li><a data-toggle="tab" href="#tab2">ADD</a></li>
					</ul>
				</div>
				<div class="tab-content">
					<div id="tab1" class="tab-pane fade in active">
						<div class="col-lg-12 table-with-upload-button">
							<div class="csv_export_button">
								<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#ledgerList'), 'Ledger_List.csv']);" data-tooltip="tooltip" title="Download Ledger List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
							</div>
							<table class="table custom-table table-bordered table-striped" id ="ledgerList" style="width: 100%;" >
								<thead>
									<tr>
										<th style="width: 50px;"> S.No </th>
										<th style="width: 20%;"> Name </th>
			                            <th style="width: 20%;"> Group Name </th>
			                            <th> Description </th>
										<th style="width:20%" > Status </th>
									</tr>
								</thead>
								<tbody>
								</tbody>
							</table>
						</div>
					</div>
					<div id="tab2" class="tab-pane fade">
						<div class="col-lg-12" style="margin-top: 15px;">
							<div>
								<form action="/erp/accounts/ledger/save/" method="post" id="addLedger">{% csrf_token %}
                                    {% if ledgerForm.id.value != None and ledgerForm.id.value != '' %}
                                        <script type="text/javascript">$(".page_header").html('<span class="header_current_page"> {{ ledgerForm.name.value }}</span>');</script>
                                    {% else %}
                                        <script type="text/javascript">$(".page_header").text('');</script>
                                    {% endif %}
                                    <div hidden="hidden">
										{{ ledgerForm.id }}{{ ledgerForm.opening_balance }}{{ ledgerForm.is_opening_debit }}
									</div>
									<div class="row">
										<div class="form-group col-sm-3">
											<input type="hidden" id="associated_party_name" value="{{ party_name }}"/>
											<label class="po_title_txt">Name<span class="mandatory_mark"> *</span>
											{% if not is_default_ledger %}
												{% if logged_in_user.is_super and ledgerForm.id.value != None  and ledgerForm.id.value != '' %}
													<a class="super_edit_field super_edit_for_draft hide" onclick="SuperEditLedgerDetails(this);" data-tooltip="tooltip" data-placement="left" title="Super Edit">
														<i class="fa fa-pencil super_edit_in_field"></i>
													</a>
												{%else%}
													<a class="super_edit_field super_edit_for_draft hide" style="color:#777;" onclick="" data-tooltip="tooltip" data-placement="left" title="Only Super User can edit this field">
														<i class="fa fa-pencil super_edit_in_field"></i>
													</a>
												{% endif %}
											{% else %}
												<a class="info_icon">
													<i class="fa fa-info-circle" style="color: #666;position:absolute;right:20px;" aria-hidden="true" data-tooltip="tooltip" data-placement="left" title="System generated ledgers cannot be edited"></i>
												</a>
											{% endif %}
											</label>
											<!-- Render the field in non-editable for Edit and editable for Add -->
											{% if ledgerForm.id.value != None  and ledgerForm.id.value != '' %}
												<input type="text" class="form-control" disabled maxlength="100"
													   value="{{ ledgerForm.name.value }}" id="id_name" name="name"/>
											{% else %}
												{{ ledgerForm.name }}
											{% endif %}
											{{ ledgerForm.enterprise_id}}
										</div>
										<div class="form-group col-sm-3">
											<label class="po_title_txt">Description<span class="mandatory_mark"> *</span></label>
											{{ ledgerForm.description }}
										</div>

										<div class="form-group col-sm-3">
											<label class="po_title_txt">Account Group<span class="mandatory_mark"> *</span></label>
											{{ ledgerForm.group_id }}
											{% if is_default_ledger %}
												<select class="form-control" id="id_group_select" disabled></select>
											{%else%}
												<select class="form-control" id="id_group_select" ></select>
											{% endif %}
										</div>

										<div class="form-group col-sm-1 account-billable checkbox hide">
											{{ ledgerForm.billable }}
											<label for="id_billable" class="po_title_txt">Billable</label>
											<input type="hidden" value="{{is_ledger_billable}}" id="is_ledger_billable" name="is_ledger_billable">
										</div>

										<div class="material_txt col-sm-1">
											{% if access_level.edit %}
											<input type="submit" class="btn btn-save btn-margin-1" id="saveLedger" value="Save"/>
											{% endif %}
										</div>
									</div>
								</form>
								<div class="row">
									{% if ledgerForm.id.value %}
									<div class="col-lg-12">
										<hr/>
										<h4>Book of Entries</h4>
									</div>
									<form action="/erp/accounts/ledger/edit/#tab2" method="post"
										  id="edit_{{ ledgerForm.id.value }}">{% csrf_token %}
										<div class="col-sm-3 form-group">
											<label>Date Range</label>
											<div id="reportrange" class="report-range form-control">
												<i class="glyphicon glyphicon-calendar"></i>&nbsp;
												<span></span> <b class="caret"></b>
												{{ ledgerForm.opening_as_on }}
												{{ ledgerForm.closing_as_on }}
											</div>
										</div>
										<div class="col-sm-5">
											<div class="exclude_draft_style">
												<div class="checkbox">
													{{ ledgerForm.approved_only }}
													<label for="id_approved_only">Exclude Drafts</label>
												</div>
											</div>
											<div>
												<input type="hidden" value="{{ ledgerForm.id.value }}"
													   id="id_edit_ledger_id" name="ledger_id"/>
												<input type="submit" value="Refresh" id="id_refresh" class="btn btn-save btn-margin-1">
											</div>
										</div>
										
										<div class="col-sm-4 text-right" style="margin-top: 23px;">
											{% if voucher_entries|length > 0 %}
		                                		<a role="button" class="pull-right btn btn-add-new" onclick="GeneralExportTableToCSV.apply(this, [$('#book_of_entries'), $('#id_name').val() + '.csv']);" data-tooltip="tooltip" title="Download Book of Entries as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
		                                	{% else %}
		                                		<a role="button" class="pull-right btn btn-add-new" data-tooltip="tooltip" title="No Record found to download." style="opacity: 0.6;"><i class="fa fa-download" aria-hidden="true"></i></a>
		                                	{% endif %}	
										</div>
									</form>
									<div class="col-lg-12 table-responsive"
										 {% if voucher_entries|length > 20 %}
											style="height:420px !important;  overflow-y:scroll !important; align:center !important;"
										{% endif %}>
										<input type="hidden" id="entry_count" value="{{voucher_entries|length}}"/>
										<input type="hidden" id="balance_0_debit" value="{{ledgerForm.opening_debit.value}}"/>
										<input type="hidden" id="balance_0_credit" value="{{ledgerForm.opening_credit.value}}"/>
										<table class="table custom-table table-striped table-bordered" id="book_of_entries">
											<thead>
											<tr>
												<th hidden="hidden"></th>
												<th hidden="hidden"></th>
												<th hidden="hidden"></th>
												<th hidden="hidden"></th>
												<th width="25%" hidden="hidden">LEDGER NAME</th>
												<th width="25%" hidden="hidden">{{ ledgerForm.name.value }}</th>
											</tr>
											<tr>
												<th hidden="hidden"></th>
												<th hidden="hidden"></th>
												<th hidden="hidden"></th>
												<th hidden="hidden"></th>
												<th width="25%" hidden="hidden">Account Group</th>
												<th width="25%" hidden="hidden">{{ ledgerForm.group_label.value }}</th>
											</tr>
											{% if party_address %}
											<tr>
												<th hidden="hidden"></th>
												<th hidden="hidden"></th>
												<th hidden="hidden"></th>
												<th hidden="hidden"></th>
												<th width="25%" hidden="hidden">Party Address</th>
												<th width="25%" hidden="hidden">{{ party_address }}</th>
											</tr>
											{% endif %}
											{% if party_phone_no %}
											<tr>
												<th hidden="hidden"></th>
												<th hidden="hidden"></th>
												<th hidden="hidden"></th>
												<th hidden="hidden"></th>
												<th width="25%" hidden="hidden">Party Phone</th>
												<th width="25%" hidden="hidden">{{ party_phone_no }}</th>
											</tr>
											{% endif %}
<!--											For separating table-->
											<tr>
												<th hidden="hidden"></th>
											</tr>
											<tr>
												<th class="remove-th-border"></th>
												<th class="remove-th-border"></th>
												<th class="remove-th-border"></th>
												<th class="remove-th-border"></th>
												<th class="remove-th-border"></th>
												<th class="remove-th-border"></th>
												<th class="remove-th-border"></th>
												<th align="right" class="remove-th-border">
													<i>Opening Balance as on <b>{{ledgerForm.opening_as_on.value|date:'M d, Y'}}</b></i></th>
												<th style="text-align: right;">{{ledgerForm.opening_debit.value}}</th>
												<th style="text-align: right;">{{ledgerForm.opening_credit.value}}</th>
												<th></th>
											</tr>
											<tr>
												<th width="60px">S.No</th>
												<th width="8%">Date</th>
												<th width="6%">Type</th>
												<th width="11%">Voucher No</th>
												<th width="6%">Project</th>
												<th width="5%">Bills</th>
												<th width="5%">Bills Date</th>
												<th>Narration</th>
												<th width="10%">Debit</th>
												<th width="10%">Credit</th>
												<th width="10%" class="text-center">Cumulative<br/>Balance</th>
											</tr></thead>
											{% if voucher_entries %}
											{%for entry in voucher_entries %}
											<tr>
												<td align="center">{{ forloop.counter }}.</td>
												<td align="center">{{ entry.voucher.voucher_date|date:'M d, Y' }}</td>
												<td>{{ entry.voucher.type.name }}</td>
												<td>
													<form action="/erp/accounts/voucher/edit/#tab2" method="post"
														  id="edit_{{ entry.voucher.id }}_voucher" target="_blank">{% csrf_token %}
														<a class="edit_link_code" onclick="javascript:clickButton('edit_voucher_{{ entry.voucher.id }}');">{{ entry.voucher|getCode }}</a>
														<input type="hidden" value="{{ entry.voucher.id }}" id="id_voucher_id" name="voucher_no"/>
													<input type="hidden" value="{{ entry.voucher.type_id }}"  name="voucher_type"/>
														<input type="submit" value="Edit" id="edit_voucher_{{ entry.voucher.id }}" hidden="hidden"/>
													</form>
												</td>
												<td class='td-ellipsis-text-200'>{% if entry.voucher.project.code %} {{entry.voucher.project.name}} -({{entry.voucher.project.code}}) {% else %}{% endif %}</td>
												<td class='td-ellipsis-text-200'><a class="anchor-ellipsis" title="{% for settlement in entry.voucher.bill_settlements %}{{ settlement.bill.bill_no }}&#013;{% endfor %}">{% for settlement in entry.voucher.bill_settlements %}{{ settlement.bill.bill_no }}&nbsp;{% endfor %}</a></td>
												<td class='td-ellipsis-text-200'><a class="anchor-ellipsis" title="{% for settlement in entry.voucher.bill_settlements %}{{ settlement.bill.bill_date }}&#013;{% endfor %}">{% for settlement in entry.voucher.bill_settlements %}{{ settlement.bill.bill_date }}&nbsp;{% endfor %}</a></td>
												<td class='td-ellipsis-text-200'><a class="anchor-ellipsis" title="{{entry.voucher.narration}}">{{entry.voucher.narration}}</a></td>
												<td align="right">{% if entry.is_debit %}{{ entry.amount }}{% else %}0.00{% endif %}</td>
												<td align="right">{% if entry.is_debit %}0.00{% else %}{{ entry.amount }}{% endif %}</td>
												<td align="right">
													<input type="hidden" id="amount_{{forloop.counter}}" value="{{entry.amount}}"/>
													<input type="checkbox" id="is_debit_{{forloop.counter}}"{% if entry.is_debit %}checked="checked"{%endif%} hidden/>
													<input type="hidden" id="balance_{{forloop.counter}}_debit" value ="0.00"/>
													<input type="hidden" id="balance_{{forloop.counter}}_credit" value="0.00"/>
													<span id="balance_{{forloop.counter}}"></span>
												</td>
											</tr>
											{% endfor %}
											{% else %}
											<tr>
												<td colspan="10" align="center">
												No Voucher Entries made during the Period</td></tr>
											{%endif%}
											<tr bgcolor="#ededed">
												<td class="remove-td-border"></td>
												<td class="remove-td-border"></td>
												<td class="remove-td-border"></td>
												<td class="remove-td-border"></td>
												<td class="remove-td-border"></td>
												<td class="remove-td-border"></td>
												<td class="remove-td-border"></td>
												<td align="right" class="remove-td-border">Sub-total</td>
												<td align="right">{{ledgerForm.total_debit.value}}{{ledgerForm.total_debit}}</td>
												<td align="right">{{ledgerForm.total_credit.value}}{{ledgerForm.total_credit}}</td>
												<td align="right"><span id="period_closing"></span><br/><i>(Period Closing)</i></td>
											</tr>
											<tr>
												<td class="remove-td-border"></td>
												<td class="remove-td-border"></td>
												<td class="remove-td-border"></td>
												<td class="remove-td-border"></td>
												<td class="remove-td-border"></td>
												<td class="remove-td-border"></td>
												<td class="remove-td-border"></td>
												<td align="right" class="remove-td-border">
													<i>Closing Balance as on <b>{{ledgerForm.closing_as_on.value|date:'M d, Y'}}</b></i></td>
												<td align="right">{{ledgerForm.closing_debit.value}}</td>
												<td align="right">{{ledgerForm.closing_credit.value}}</td>
												<td></td>
											</tr>
										</table>
									</div>
									{% endif %}
								</div>
							</div>
						</div>
						<div class="clearfix"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div id="importVoucher" class="modal fade" role="dialog">
  	<div class="modal-dialog modal-lg">
    	<div class="modal-content">
	      	<div class="modal-header">
	        	<button type="button" class="close" data-dismiss="modal">&times;</button>
	        	<h4 class="modal-title">Import Ledger Trial Balance</h4>
	  		</div>
	      	<div class="modal-body">
			  	<form>
			   		<div class="form-group hide">
					  	<select id="id_group_id_import" class="form-control hide" name="id_group_id_import">
						  {% autoescape off %}
						  {{ account_group_options }}
						  {% endautoescape %}
					  	</select>
					  	<div style="max-height: 300px; overflow:auto;" class="hide">
				        {% autoescape off %}
					    {{ account_group_options }}
					    {% endautoescape %}
					  	</div>
			      	</div>
		      		<div>
		      			<div class="alert alert-warning" role="alert">
		      				<b>Warning:</b> Import will be failed if same ledger has both Debit and credit opening! Either one should exists.
		      			</div>
		     		</div>
		     		<a  role="button" class="pull-right" onclick="show_Account_Group_Modal();" >Account Group's with structure</a>
		      		<table id="importtable" class="table table-bordered custom-table table-striped">
						<thead>
						    <tr>
								<th width="25%">LEDGER_NAME</th>
								<th width="20%">ACCOUNT_GROUP_ID</th>
								<th width="20%">OPENING_DEBIT</th>
								<th width="25%">OPENING_CREDIT</th>
						    </tr>
						</thead>
						<tr>
							<td>SCHNELL ENERGY EQUIPMENTS</td>
							<td>24</td>
							<td>1500</td>
							<td></td>
						</tr>
					    <tr>
							<td>STATIC SYSTEMS PVT LTD</td>
							<td>24</td>
							<td></td>
							<td>2000</td>
						</tr>
						<tr>
							<td style="font-size:10px">(Alpha-numeric Text)</td>
							<td align="center" style="font-size:10px">(Numeric)</td>
							<td style="font-size:10px">(Numeric)</td>
							<td style="font-size:10px">(Numeric)</td>
						</tr>

						<tr>
							<td colspan="3" style="border: none;"><b>Please Select the File for Import Material</b></td>
							<td colspan="1" style="border: none;" class="text-right"><a href="/site_media/docs/sample_trial_balance.csv" >Download Sample</a> </td>
						</tr>

						<tr>
							<td colspan="3">
								<input class="filestyle" data-buttonBefore="true" type="file" id="fileUpload" />
							</td>
							<td colspan="2">
								<label>Voucher Date<span class="mandatory_mark"> *</span></label>
								<input type="text" class="form-control hide" id="voucher_import_date" name="voucher_date" >
								<input type="text" class="form-control custom_datepicker set-my-start-date" data-setdate="{{ next_opening_date|date:'Y-m-d' }}" placeholder="Select Date" id="voucher_import_date_field" readonly="readonly" autocomplete="off">
								<i class="glyphicon glyphicon-calendar custom-calender-icon"></i>
							</td>

						</tr>
					    <tr>
						    <td colspan="10">
								<div class="material_txt" style="margin-right:0px;">
									<a href="#" class="btn btn-save" id="cmdUpload"height="40">Upload</a>
									<a href="#" class="btn btn-cancel" id="cmdhideUpload" height="40" data-dismiss="modal">Cancel</a>
								</div>
							</td>
					    </tr>
					</table>
	  			</form>
	  		</div>
    	</div>
	</div>
</div>
<div id="import_voucher_status_modal" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Import Trial balance status</h4>
			</div>
			<div class="modal-body">
				<div>
					<h4 id="import_message"></h4>
				</div>
				<br>
				<a role="button" class="pull-right"
				   onclick="GeneralExportTableToCSV.apply(this, [$('#failed_item_table'), 'export_trial_balance_failed_items.csv']);">
					Download failed items (as CSV)</a><br>
				<div style='overflow:auto' class="outer">
					<div style='overflow:auto' class="inner">
						<table class="table table-bordered custom-table table-striped" id="failed_item_table">
							<thead>
							<tr align="center">
								<th>LEDGER NAME	</th>
								<th>ACCOUNT GROUP ID	</th>
								<th>OPENING DEBIT	</th>
								<th>OPENING CREDIT</th>
								<th>Error message</th>
							</tr>
							</thead>
							<tr>
								<td colspan="10">
									<div class="material_txt" style="margin-right:0px;">
										<a href="#" class="btn btn-cancel" id="cmdhideUpload" height="40"
										   data-dismiss="modal">Cancel</a>
									</div>
								</td>
							</tr>
							<tbody>
							</tbody>
							<tfoot class="indent_tfoot">
							</tfoot>
						</table>
						<br>
					</div>
				</div>
				<br><br>
			</div>
		</div>
	</div>
</div>
<div id="account_group_list_modal" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Account group with structure</h4>
				<a role="button" class="btn btn-add-new pull-right export_csv" style="position: absolute; right: 45px; top: 10px;" onclick="GeneralExportTableToCSV.apply(this, [$('#account_group_table'), 'account_group_structure.csv']);" data-tooltip="tooltip" data-placement="left" title="Download Account Gruop List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
			</div>
			<div class="modal-body">
				<div style="max-height: 600px; overflow:auto;">
					<table id="account_group_table" cellpadding="2" align="center" cellspacing="0" border="0" class="table table-bordered custom-table table-striped">
						<thead>
						    <tr bgcolor="#525151" style="color:#FFFFFF; font-weight:normal; align:center !important">
								<th width="10%"># ID </th>
								<th width="600%">Account group Name</th>
						    </tr>
						</thead>
						 {% autoescape off %}
						 {{ account_group_options }}
						 {% endautoescape %}
					</table>
				</div>
			</div>
		</div>
	</div>
</div>

<div id="error_messages" class="modal fade" role="dialog">
  	<div class="modal-dialog">
    	<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Errors in Ledger Creation</h4>
      		</div>
      		<div class="modal-body">
        		{% if ledgerForm.errors %}
        			<h5 style="color: #dd4b39;">Process Failed due to incomplete form submission. Please fill the following data:</h5>
    				{% for field in ledgerForm.visible_fields %}
						{% for error in field.errors %}
					 		{% if error != '' %}
								<li style="list-style-type: number;">{{field.label_tag}}:{{error}}</li>
							{% endif %}					
						{% endfor %}
					{% endfor %}
        		{% endif %}
      		</div>
      		<div class="modal-footer">
        		<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        		<input type="text" value="{{ ledgerForm.errors.as_text }}" id="form_errors" hidden="hidden"/>
        		<input type="text" value="{{ ledgerForm.errors.as_text }}" id="formset_errors" hidden="hidden"/>
      		</div>
    	</div>
  	</div>
</div>

<div id="modal_account_creation" class="modal fade" role="dialog" tabindex='-1'>
	<div class="modal-dialog" >
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">ADD NEW ACCOUNTS GROUP</h4>
			</div>
			<div class="modal-body">
				<div id="account_creation" class="material_txt ">
					<form id="account_group_form" method="POST" action="/erp/accounts/add_group/">
						{% csrf_token %}
						<div class="row">
							<div class="form-group intent_for col-sm-12">
						        <label>Account Head<span class="mandatory_mark"> *</span></label>
								{{ account_group_form.parent_id }}{{ account_group_form.enterprise_id }}
								{{ account_group_form.id }}
								<select id="id_{{account_group_form.prefix}}-parent_group_select" class="form-control"></select>
					        </div>
							<div class="form-group col-sm-12">
								<label>Name<span class="mandatory_mark"> *</span></label>
								{{ account_group_form.name }}
							</div>
							<div class="form-group col-sm-12">
								<label>Description<span class="mandatory_mark"> *</span></label>
								{{ account_group_form.description }}
							</div>

							<div class="col-sm-12">
								<label>Configurations</label>
								<table class="multi-checkbox-table">
									<tr class="checkbox" style="padding-left: 20px !important;">
										{{ account_group_form.config_flags }}
									</tr>
								</table>

							</div>
							<div class="form-group col-sm-12 checkbox hide">
								{{ account_group_form.billable }}
								<label for="id_account_group-billable" class="po_title_txt">Billable</label>
							</div>
						</div>
						<div class="modal-footer">
							{% if access_level.edit %}
							<input type="button" class="btn btn-save" id="save_account_group" value="Save"/>
							{% endif %}
							<input type="submit" class="btn btn-cancel" data-dismiss="modal" value="Close"/>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>
<div class="hide">
	<form target='_blank' action='/erp/accounts/ledger/edit/#tab2' method='post' id='ledger_edit_form'>
		{% csrf_token %} 
	 	<input type='hidden' id='edit_ledger_id' name='ledger_id' value="" />
 	</form>
 </div>
<!-- This Script is used to Point the  Active Menu -->
<script language="javascript">
$(function(){
  var hash = window.location.hash;
  hash && $('ul.nav a[href="' + hash + '"]').tab('show');

  $('.nav-tabs a').click(function (e) {
    $(this).tab('show');
    var scrollmem = $('body').scrollTop() || $('html').scrollTop();
    window.location.hash = this.hash;
    $('html,body').scrollTop(scrollmem);
  });
});

$( document ).ready(function() {
	var url = window.location.href;
	if($(".header_current_page").text().length >=1){
		$(".super_edit_for_draft").removeClass('hide');
	}
	else{
		$(".super_edit_for_draft").addClass('hide');
	}
    if(url.indexOf('tab2') < 0) {
        <!--TableHeaderFixed();-->
    }
    else {
    	$(".view_ledger").removeClass('hide');
        $(".create_ledger, .export_csv, .import_csv").addClass('hide');
    }
	ResetModelAccountCreation();
	$("#loading").hide();
	var url = window.location.href;
    if(url.indexOf('edit') < 0) {
        populateLedgers();
    }
});

$('.nav-pills li').removeClass('active');
$('#li_ledger').addClass('active');

$("#saveLedger").click(function(){
	$(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();

    var ControlCollections = [
        {
            controltype: 'textbox',
            controlid: 'id_name',
            isrequired: true,
            errormsg: 'Name is required.'
        },
        {
            controltype: 'textbox',
            controlid: 'id_description',
            isrequired: true,
            errormsg: 'Description is required.'
        },
        {
            controltype: 'dropdown',
            controlid: 'id_group_id',
            isrequired: true,
            errormsg: 'Account Group is required.'
        }
    ];
    var result = JSCustomValidator.JSvalidate(ControlCollections);
	$(".custom-error-message:not('.hide')").each(function(){
		$(this).closest('div').find('input').focus();
		$(this).closest('div').find('select').focus();
		return false;
	});
	if(result) {
		$("#id_name").prop("disabled", false);
		$(this).val('Processing...').addClass('btn-processing');
		if(($("id_id").val().trim().length > 0) && (parseInt($("id_id").val()) > 0)){
	        ga('send', 'event', 'Ledger', 'Create', $('#enterprise_label').val(), 1);
		} else {
	        ga('send', 'event', 'Ledger', 'Update', $('#enterprise_label').val(), 1);
		}
	}
	return result;
});

$("#save_account_group").click(function(){
	$(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();
    var ControlCollections = [
        {
            controltype: 'dropdown',
            controlid: 'id_account_group-parent_id',
            isrequired: true,
            errormsg: 'Account Head is required.'
        },
        {
            controltype: 'textbox',
            controlid: 'id_account_group-name',
            isrequired: true,
            errormsg: 'Name is required.'
        },
        {
            controltype: 'textbox',
            controlid: 'id_account_group-description',
            isrequired: true,
            errormsg: 'Description is required.'
        }
    ];
    var result = JSCustomValidator.JSvalidate(ControlCollections);
	if(result){
        ga('send', 'event', 'Account Group', 'Create', $('#enterprise_label').val(), 1);
		$("#account_group_form").submit();
	}
	return result;
});

function ResetModelAccountCreation(){
	$('#modal_account_creation').on('hidden.bs.modal', function () {
		$("#id_group_id").val(0);
	});
}

function editLedgerRow(ledgerId, openTarget="") {
	$("#edit_ledger_id").val(ledgerId);
	$("#ledger_edit_form").attr("target", openTarget).submit();
}

var oTable;
var oSettings;
function populateLedgers(){
    $('#loading').show();
    var voucher_type_no=$("#voucher_type option:selected").val()
    var data = [];
    $.ajax({
        url: "/erp/accounts/json/ledger/populate_ledger_lists/",
        type: "POST",
        dataType: "json",
        data: {"voucher_type_no": voucher_type_no},
        success: function(response) {
			var csrf_token = '<input type="hidden" name="csrfmiddlewaretoken" value="'+ getCookie('csrftoken') +'">';
            $.each(response, function (i, item) {
                var edit_name = `<a class='edit_link_code' onclick='editLedgerRow("${item.id}", "_blank");'>
        							${item.name.trim()}
        					 	 </a>
						         <input type="hidden" name="ledger_code" value="${item.id}" />`;
		 	 	var ledger_narration = `<span class='inline-icon-content' style='text-align: justify;'>${item.description}</span>`;
	            var status = `<div class='text-center'><label role='button' id="activeStatus" style="border: solid 1px #004195; border-radius: 50px;">
							<input class='hide' type="checkbox" name="is_active" ${item.status ? 'checked' : ''} />
							<span class="toggle-round round on" onClick="tagChangeEvent(this, ${item.id}, 1)">Active</span>
							${!item.is_default ? '<span class="toggle-round round off" onClick="tagChangeEvent(this, ' + item.id + ', 0)">Inactive</span>' : ''}
						</label></div>`;


                data.push( [ i+1, edit_name,item.group_name,ledger_narration,status ] );
            });
			if ($("#ledgerList tr").length == 1){
                var row = "";
                $('#ledgerList').append(row);
            }
            oTable = $('#ledgerList').DataTable( {
                data: data,
                deferRender: true,
                fixedHeader: false,
                "scrollY": Number($(document).height() - 230),
	            "scrollX": true,
                "pageLength": 50,
                "search": {
                    "smart": false
                }
            });
            oTable.on("draw",function() {
                var keyword = $('#ledgerList_filter > label:eq(0) > input').val();
                $('#ledgerList').unmark();
                $('#ledgerList').mark(keyword,{});
                setHeightForTable();
                listTableHoverIconsInit('ledgerList');
            });
            oTable.on('page.dt', function() {
              $('html, body').animate({
                scrollTop: $(".dataTables_wrapper").offset().top - 15
               }, 'slow');
              listTableHoverIconsInit('ledgerList');
            });
            oSettings = oTable.settings();
            $( window ).resize();
            listTableHoverIconsInit('ledgerList');
            $('#loading').hide();
        }

    });
    setTimeout(function(){
        $("#filter_textbox").on("focus", function() {
            if(oTable != undefined && $("#pagination-select").val() == -1) {
                $("#pagination-select").val(50).trigger('change');
                $("#pagination-select").after("<p class='pagination-warning-for-all'>For search speed, paging entries changed to 50. But your search result will show all your filtered data through pagination.</p>");
                setTimeout(function(){
                    $(".pagination-warning-for-all").fadeOut(1000);
                },10000)
            }
        });
    },1000);
}

function SuperEditLedgerDetails(field){
	var swalName = "";
	if ($("#associated_party_name").val() != "" && $(field).closest("label").next("input").attr("name") == "name") {
		swalName = "Party Name"
	}
	if($("#id_group_select").val() == 25) {
		if(swalName == "") {
			swalName = "Tax Name"
		}
		else {
			swalName += " and Tax Name"
		}
	}
	if(swalName != "") {
		swal({
	        title: "",
	        text: "Associated <b>"+swalName+"</b> will not be updated.<br/><br/>Do you still want to continue?",
	        type: "warning",
	        allowEscapeKey: false,
	        showCancelButton: true,
	        confirmButtonColor: "#209be1",
	        confirmButtonText: "Yes, do it!",
	        closeOnConfirm: true,
	        closeOnCancel: true
	    }, function(isConfirm){
	        if(isConfirm){
	            $(field).closest("label").next("input").removeAttr("disabled");
				$(field).addClass("hide");
	        }
	    });
    } else {
		$(field).closest("label").next("input").removeAttr("disabled");
		$(field).addClass("hide");
	}	
}

$(".create_ledger").click(function(){
	$(this).addClass('hide');
	$(".export_csv, .import_csv").addClass('hide');
	$(".view_ledger").removeClass('hide');
});
	function tagChangeEvent(current) {
		var currentInput = $(current).closest("td").find("input");
		var currentTag = $(current).closest("tr").find("td a.edit_link_code").text().trim();
		var currentTagStatus = currentInput.is(":checked");
		var currentLedgerCode = $(current).closest("tr").find("input[name='ledger_code']").val();
		if(currentTagStatus && $(current).hasClass("on")) {
			currentInput.prop("checked", false);
			return false;
		}
		if(!currentTagStatus && $(current).hasClass("off")) {
			currentInput.prop("checked", true);
			return false;
		}
		if(currentTagStatus) {
			currentInput.prop("checked", false);
			swal({
				title: "Are you sure?",
				text: `Do you want to Deactivate the Ledger <b>'${currentTag}'</b>?`,
				type: "warning",
				showCancelButton: true,
				confirmButtonColor: "#209be1",
				confirmButtonText: "Yes",
				closeOnConfirm: true
			},
			function(deleteUser) {
		      	if (deleteUser) {
		      		saveTag(currentLedgerCode, currentTag, 0)
		     	}
		   	});
		}
		else {
			currentInput.prop("checked", true);
			swal({
				title: "Are you sure?",
				text: `Do you want to Activate the Ledger <b>'${currentTag}'</b>?`,
				type: "warning",
				showCancelButton: true,
				confirmButtonColor: "#209be1",
				confirmButtonText: "Yes",
				closeOnConfirm: true
			},
			function(deleteUser) {
		      	if (deleteUser) {
		      		saveTag(currentLedgerCode, currentTag, 1)
		     	}
		   	});

		}
	}

	function saveTag(ledger_code, project_name, is_active){
		$.ajax({
			url:"/erp/accounts/ledger/status_update/",
			type: "post",
			datatype: "json",
			data: {
				id: ledger_code,
				status: is_active,
				enterprise_id:$("#enterprise_id").val()
			},
			success:function(response){
		                	var desiredId = response.id;
							$("#ledgerList").find("tr td input[name='ledger_code']").each(function() {
								if ($(this).val() == desiredId) {
									if (response.status === 0) {
										$(this).closest('tr').find("td input[name='is_active']").prop("checked", false);
									} else {
										$(this).closest('tr').find("td input[name='is_active']").prop("checked", true);
									}
								}
							});


	        },
	        error: function(){
	        	location.reload();
	        }
	    });
	}


</script>
{% endblock %}