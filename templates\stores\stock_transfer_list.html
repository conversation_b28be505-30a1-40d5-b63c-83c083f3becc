{% extends "stores/sidebar.html" %}
{% block indent %}

{% if logged_in_user|canView:'STORES' %}
<style>
    li.supplier_side_menu a {
        outline: none;
        background-color: #e6983c !important;
    }
    .left-form-group .custom-error-message,
    .tr-file-upload .custom-error-message {
        position: relative;
        display: block;
    }
    .left-form-group .row div {
        margin-bottom: 15px;
    }
    .edit_fixed td {
        background: #ccffcc;
    }
    .editable-textbox {
        background: transparent;
        border: none;
        border-bottom: 2px solid #004195;
        color: #004195;
        outline: none;
        padding: 1px;
        width: 100%;
    }
    input[disabled] {
        cursor: not-allowed;
        opacity: 0.4;
    }
    .edit_fixed td {
        padding: 4px 8px !important;
    }
    .esc-info-message {
        color: #a94442;
        background-color: #f2dede;
        border-color: #ebccd1;
        position: fixed;
        right: 60px;
        bottom: 59px;
        font-size: 25px;
        padding: 25px;
        border-radius: 5px;
        z-index: 10;
    }
    #importsupplier .custom-table-large td {
        font-size: 11px !important;
    }
    .tr-file-upload .bootstrap-filestyle {
        width: 600px;
        float: left;
    }
    .outer {
        max-width: 1000px;
        max-height: 300px;
        height: 100vh;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }
    .inner {
        flex: 1;
        overflow-y: scroll;
    }
    .create_location {
        border-radius: 50px !important;
        border-right: 1px solid #ccc;
    }
    .create_add_container {
        margin-right: 15px;
    }
    .create_add_container a.btn-new-item {
        box-shadow: -4px 4px 5px 0 rgba(60, 64, 67, 0.302), 0 1px 3px 1px rgba(60, 64, 67, 0.149);
    }
    .import_csv.btn-new-item {
        border-radius: 0 50px 50px 0;
    }
    .btn-new-item-label {
        color: #004195;
        padding: 6px !important;
    }
    .create_location:hover,
    .import_csv:hover {
        background: rgba(32, 155, 225, 0.1);
        box-shadow: -4px 4px 5px 0 rgba(60, 64, 67, 0.302), 0 1px 3px 1px rgba(60, 64, 67, 0.149) !important;
    }
    .show_save_button {
        display: none !important;
    }
</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>

<div class="right-content-container">
    <div class="page-title-container">
        <span class="page-title">Internal Stock Transfer</span>
    </div>
    <input type="hidden" value="0" id="is_edit_enabled">
			{% if logged_in_user|canEdit:'STORES' %}
	<a href="/erp/stores/stock_transfer_template/" role="button" class="btn btn-new-item pull-right" data-tooltip="tooltip" title="Create New Internal Stock Transfer" style="position: absolute; right: 85px; margin-top: 10px; z-index: 10;"><i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span></a>
	{% else %}
				<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in Stores Module. Please contact the administrator." style="position: absolute; right: 85px; margin-top: 5px; z-index: 10;">
					<a class="btn btn-new-item pull-right create_indent disabled">
						<i class="fa fa-plus" aria-hidden="true"></i><span class="btn-new-item-label"></span>
					</a>
				</div>
			{% endif %}
    <div class="container-fluid" id="container-fluid">
		<div>
			<div class="col-lg-12">
				<div class="content_bg">
					<div class="tab-content">
						<div>
							<div class="row">
								<div class="filter-components">
									<div class="filter-components-container">
										<div class="dropdown">
											<button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
												<i class="fa fa-filter"></i>
											</button>
											<span class="dropdown-menu arrow_box arrow_box_filter">
												<form action="/erp/stores/json/stock_transfer_list/" method="post">{% csrf_token %}
													<div class="col-sm-12 form-group">
														<label>Date Range</label>
														<div id="reportrange" class="report-range form-control">
															<i class="glyphicon glyphicon-calendar"></i>&nbsp;
															<span></span> <b class="caret"></b>
															<input type="hidden" class="fromdate" id="from_date" name="from_date" value="{{filter.from_date|date:'Y-m-d'}}" />
															<input type="hidden" class="todate" id="to_date" name="to_date" value="{{filter.to_date|date:'Y-m-d'}}" />
														</div>
													</div>
													<input type="hidden" name="enterprise_id" id="enterpriseId" value="" />
													<input type="hidden" class="user_id" id="userId" name="user_id" value="" />
<!--													<input type="hidden" class="status" id="status" name="status" value="{{status}}" />-->
													<div class="col-sm-12 form-group">
														<label>Status</label>
														<select class="form-control chosen-select" name="status" id="search_status" value="{{filter.status}}">
															<option value="" {% if filter.status == '' %}selected{% endif %}>All</option>
															<option value="1" {% if filter.status == '1' %}selected{% endif %}>Draft</option>
															<option value="2" {% if filter.status == '2' %}selected{% endif %}>InTransit</option>
															<option value="3" {% if filter.status == '3' %}selected{% endif %}>Received</option>
															<option value="4" {% if filter.status == '4' %}selected{% endif %}>Rejected</option>
														</select>
													</div>
													<div class="filter-footer">
														<input type="submit" class="btn btn-save search_button" value="Apply" id="id-searchStockTransfer"/>
													</div>
												</form>

											</span>
										</div>
										<span class='filtered-condition filtered-date'>Date: <b></b></span>
										<span class='filtered-condition filtered-status'>Status: <b></b></span>
									</div>
								</div>
								<div class="col-sm-12">

										<div class="csv_export_button">
											<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#stockTransferList'), 'Stock_Transfer_list.csv']);" data-tooltip="tooltip" title="Download Internal Stock Transfer List as CSV">
												<i class="fa fa-download" aria-hidden="true"></i>
											</a>
										</div>

									<table class="table table-bordered custom-table table-striped" id="stockTransferList" style="width: 100%;">
										<thead>
											<tr>
												<th width="5%"> S.No</th>
												<th hidden="hidden" class="exclude_export"> Transfer No</th>
												<th > Transfer Code</th>
                                                <th > Date</th>
												<th > From</th>
												<th > To </th>
												<th > Status </th>
											</tr>
										</thead>
										<tbody>
										{% for rec in data.data %}
											<tr bgcolor="#ECECEC" align="center">
												<td class="text-center td_sno" style="width: 6%;">{{forloop.counter}}</td>
												<td hidden="hidden" class="exclude_export">{{ rec.transfer_no }}</td>
												<td><a role="button" class="edit_link_code" onclick="editStockTransferRow('{{ rec.enterprise_id }}', '{{ rec.code }}', '{{ rec.total_qty }}', '{{ rec.total_value }}', '{{ rec.last_modified_by }}', '{{ rec.from_location_id }}', '{{ rec.to_location_id }}', '{{ rec.status }}', '{{ rec.created_by }}', '{{ rec.transfer_id }}', '{{ rec.remarks|escapejs }}','{{ rec.created_on }}','{{ rec.from_location }},'{{rec.to_location}}', '_blank','{% if rec.pp_id != None %}{{ rec.pp_id }}{% endif %}')">{{ rec.code }}</a></td>
												<td>{{ rec.created_on }}</td>
												<td>{{ rec.from_location }}</td>
												<td>{{ rec.to_location }}</td>
												<td align="center" class="td_status" style="padding: 0;">
														<a role="button" class="table-inline-icon-bg {% if rec.status == 'Approved' %}  approved {% elif rec.status == 'Draft' %} draft {% elif rec.status == 'Rejected' %}cancelled {% else %} Received {% endif %}" >
															{% if rec.status == 'Approved' %}  Intransit {% elif rec.status == 'Draft' %} Draft {% elif rec.status == 'Rejected' %} Rejected  {% else %} Received {% endif %}
														</a>
														<span class="table-inline-icon pdf_genereate hide" data-tooltip="tooltip" data-placement="left" title="" onclick="generate_pdf_ajax('{{ rec.enterprise_id }}','{{ rec.code }}','{{ rec.total_qty }}','{{ rec.total_value }}','{{ rec.last_modified_by }}','{{ rec.from_location_id }}','{{ rec.to_location_id }}','{{ rec.status_id }}','{{ rec.created_by }}','{{ rec.transfer_id }}','{% if rec.pp_id != None %}{{ rec.pp_id }}{% endif %}', '{{rec.from_location}}','{{rec.to_location}}',)" data-original-title="Preview">
															<i class="fa fa-file-text"></i>
														</span>
														<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='right' title='Edit' role='button' onclick="editStockTransferRow('{{ rec.enterprise_id }}','{{ rec.code }}','{{ rec.total_qty }}','{{ rec.total_value }}','{{ rec.last_modified_by }}','{{ rec.from_location_id }}','{{ rec.to_location_id }}','{{ rec.status }}','{{ rec.created_by }}','{{ rec.transfer_id }}','{{ rec.remarks|escapejs }}','{{ rec.created_on }}','{{ rec.from_location }}','_self','{% if rec.pp_id != None %}{{ rec.pp_id }}{% endif %}')">
															 <i class='fa fa-pencil'></i>
														</span>
														<span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='right' title='Edit in New Tab' role='button' onclick="editStockTransferRow('{{ rec.enterprise_id }}','{{ rec.code }}','{{ rec.total_qty }}','{{ rec.total_value }}','{{ rec.last_modified_by }}','{{ rec.from_location_id }}','{{ rec.to_location_id }}','{{ rec.status }}','{{ rec.created_by }}','{{ rec.transfer_id }}','{{ rec.remarks|escapejs }}','{{ rec.created_on }}','{{ rec.from_location }}','_blank','{% if rec.pp_id != None %}{{ rec.pp_id }}{% endif %}')">
															 <i class='fa fa-external-link'></i>
														</span>
													</td>
											</tr>
										{% endfor %}
										{% if data|length == 0 %}
										<tr><td colspan="12" align="center"><b>No records found</b></td></tr>
										{% endif %}
										</tbody>
									</table>
								</div>
							</div>
							<div class="clearfix"></div>
						</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% include "attachment_popup.html" %}
<div id="transfer_document_modal" class="modal fade" role="dialog">
		<input type="hidden" id="status_id" name="id" value="" />
		<input type="hidden" id="status_code" name="code" value="" />
		<input type="hidden" id="status_enteriprise_id" name="enterprise_id" value=""/>
		<input type="hidden" id="status_total_qty" name="total_qty" value="" />
		<input type="hidden" id="status_total_value" name="total_value" value=""/>
		<input type="hidden" id="status_last_modified_by" name="last_modified_by" value=""/>
		<input type="hidden" id="status_from_location" name="from_location" value="" />
		<input type="hidden" id="status_from_location_name" name="from_location_name" value="" />
		<input type="hidden" id="status_to_location_name" name="to_location_name" value="" />
		<input type="hidden" id="status_to_location" name="to_location" value="" />
		<input type="hidden" id="status_status" name="status" value="" />
		<input type="hidden" id="status_created_by" name="created_by" value="" />
		<input type="hidden" id="status_remarks" name="remarks" value="" />
  	<div class="modal-dialog modal-lg">
		<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Preview Document</h4>
      		</div>
      		<div class="modal-body text-center">
				<input id="modal_stock_transfer_id" name="transfer_id" value='' hidden="hidden"/>
		        <input id="modal_selected_stock_trasnsfer" name="mod_selected_supplier" value='' hidden="hidden"/>
		        <input id="modal_received_against" name="mod_received_against" value='' hidden="hidden"/>
      			<div class="row" style="text-align: left;">
					<div class="col-lg-12">
						<div class="content_bg">
							<div class="add_table">
								<form id="pdf_generation">{% csrf_token %}
									<input type="submit" hidden="hidden" value="Approve/Reject" id="view_receipt_document"/>
									<input type="hidden" name="receipt_no" id="id_receipt_no" value="{{receipt_no}}" />
									<div class="col-sm-6" style="padding-left: 0;">
										{%if access_level.edit or access_level.approve%}
											<input id="remarks" class="form-control" enterkey="do_nothing" maxlength="300" placeholder="Approval/Rejection Remarks" />
										{% endif %}
								       	<div class="remarks_count_link remarks_count disabled" onclick="remarksInSwal('stock_transfer_document_remarks');">
											<span class="remarks_counter">No</span><span> remarks</span>
										</div>
								   	</div>
								   	<div id="transfer_doc_btn">
										{% if access_level.approve %}
										<span class="material_txt">
											<a role='button' id='approve_stock_transfer' class="btn btn-save for_stock_transfer_approve" onclick="approveStockTransfer();">Approve</a>
										</span>
									    {% endif %}
									    {% if access_level.approve %}
											<a role='button' id='reject_stock_transfer' class="btn btn-danger for_stock_transfer_approve" onclick="rejectStockTransfer();">Reject</a>
										{% else %}
										{%if access_level.edit %}
											<a role='button' id='reject_stock_transfer' class="btn btn-danger for_stock_transfer_edit" onclick="rejectStockTransfer();">Discard</a>
										{% endif %}
										{% endif %}
									</div>
<!--									<div class="grn_icd_status" style="font-size: 16px; color: #28a745; float: right; margin-right: 30px;"></div>-->
								</form>
							</div>
						</div>
					</div>
				</div>
				<input type="hidden" id="stock_transfer_document_remarks" />
				<div id="stock_transfer_document_container"></div>
  			</div>
  			<div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
  		</div>
  	</div>
</div>


<div class="hide">
    <form id="edit_transfer_form" action="/erp/stores/update_stock_transfer/" method="post">
       {% csrf_token %}
       	<input type="hidden" id="id" name="id" value="" />
		<input type="hidden" id="code" name="code" value="" />
		<input type="hidden" id="enteriprise_id" name="enterprise_id" value="" />
		<input type="hidden" id="total_qty" name="total_qty" value="" />
		<input type="hidden" id="total_value" name="total_value" value="" />
		<input type="hidden" id="last_modified_by" name="last_modified_by" value="" />
		<input type="hidden" id="from_location" name="from_location" value="" />
		<input type="hidden" id="to_location" name="to_location" value="" />
		<input type="hidden" id="status" name="status" value="" />
		<input type="hidden" id="id_pp_id" name="pp_id" value="" />
		<input type="hidden" id="created_by" name="created_by" value="" />
		<input type="hidden" id="created_on" name="created_on" value="" />
		<input type="hidden" id="from_location_name" name="from_location_name" value="" />
		<input type="hidden" id="transfer_details" name="transfer_details" value="" />
		<input type="hidden" id="transfer_remarks" name="transfer_remarks" value="" />
    </form>
</div>
<script>
$(document).ready(function(){
	$('#enterpriseId').val($('#enterprise_id').val());
	$('#userId').val($('#login_user_id').val());
	$('.nav-pills li').removeClass('active');
	$("#li_material_report").addClass('active');
	TableHeaderFixed();
});

	$(window).load(function(){

    var oTable;
	var oSettings;
	var hash = window.location.hash;
	hash && $('ul.nav a[href="' + hash + '"]').tab('show');

	$('.nav-tabs a').click(function (e) {
		$(this).tab('show');
		var scrollmem = $('body').scrollTop() || $('html').scrollTop();
		window.location.hash = this.hash;
		$('html,body').scrollTop(scrollmem);
	});

	var url = window.location.href;
	NavTableRemove();

		updateFilterText();
		$("#loading").hide();
	});
	function NavTableRemove() {
	$('ul.nav-tabs li a').click(function() {
		if($(this).attr('id') != "tab_view") {
			if($("#stockTransferList").hasClass('dataTable')) {
				oTable.destroy();
			}
		}
		else {
			TableHeaderFixed();
		}
	});
}

function updateFilterText() {
	$(".filtered-date b").text($("#reportrange").find("span").text());
	$(".filtered-status b").text($("#search_status option:selected").text());
}

var oTable;
var oSettings;
function TableHeaderFixed(){
	oTable = $('#stockTransferList').DataTable({
			fixedHeader: false,
	        "scrollY": Number($(document).height() - 230),
	        "scrollX": true,
			"pageLength": 50,
			"search": {
				"smart": false
			},
			"columns": [
				null,null,null,null,
				{ "type": "date" },
				null,null
				]
		});
	updatestockTransferListJson();
	oTable.on("draw",function() {
		var keyword = $('#stockTransferList_filter > label:eq(0) > input').val();
		$('#stockTransferList').unmark();
		$('#stockTransferList').mark(keyword,{});
		updatestockTransferListJson();
		setHeightForTable();
		listTableHoverIconsInit('stockTransferList');
	});
	oTable.on('page.dt', function() {
	  $('html, body').animate({
		scrollTop: $(".dataTables_wrapper").offset().top - 15
	   }, 'slow');
	   listTableHoverIconsInit('stockTransferList');
	});
	oSettings = oTable.settings();
	listTableHoverIconsInit('stockTransferList');
	$( window ).resize();
}

function updatestockTransferListJson(){
	setTimeout(function(){
    	stockTransferListjsonObj = [];
    	var rows = oTable.rows( { page : 'current'} ).data();
        for(var i=0;i<rows.length;i++) {
        	var selectedRow = $(rows[i][1]);
        	stockTransfer = {}
	        stockTransfer ["transferId"] = selectedRow.attr("data-transferId")
	        stockTransfer ["stockTransferNumber"] = selectedRow.text().trim();
            stockTransferListjsonObj.push(stockTransfer);
        }
		localStorage.setItem('stockTransferListav', JSON.stringify(stockTransferListjsonObj));
	},10);
}

function editStockTransferRow(enterpriseId,code,total_qty,total_value,last_modified_by,from_location,to_location,status,created_by,transferID,remarks,createdOn,from_location_name,openTarget="", pp_id='') {
		$('#enteriprise_id').val(enterpriseId);
		$("#id").val(transferID);
		$("#code").val(code);
		$("#total_qty").val(total_qty);
		$("#total_value").val(total_value);
		$("#last_modified_by").val(last_modified_by);
		$("#from_location").val(from_location);
		$("#to_location").val(to_location);
		$("#status").val(status);
		$("#id_pp_id").val(pp_id);
		$("#created_by").val(created_by);
		$("#created_on").val(createdOn);
		$("#from_location_name").val(from_location_name);
		$("#transfer_remarks").val(JSON.stringify(remarks));
		 $.ajax({
			url: '/erp/stores/json/transfer_item_list/',
			method: 'POST',
			data: { transfer_id: transferID },
			success: function(response) {
				$("#transfer_details").val(JSON.stringify(response.data));
				$("#edit_transfer_form").attr("target", openTarget).submit();
			},
			error: function(xhr, status, error) {
				console.error('Error occurred while calling the API:', status, error);
			}
    });
	}

	function generate_pdf_ajax(enterpriseId,code,total_qty,total_value,last_modified_by,from_location,to_location,status,created_by,transfer_id, pp_id, from_location_name, to_location_name){
		$('#status_enteriprise_id').val(enterpriseId);
		$("#status_id").val(transfer_id);
		$("#status_code").val(code);
		$("#status_total_qty").val(total_qty);
		$("#status_total_value").val(total_value);
		$("#status_last_modified_by").val(last_modified_by);
		$("#status_from_location").val(from_location);
		$("#status_to_location").val(to_location);
		$("#status_from_location_name").val(from_location_name);
		$("#status_to_location_name").val(to_location_name);
		$("#status_status").val(status);
		$("#id_pp_id").val(pp_id);
		$("#status_created_by").val(created_by);
		$("#status_remarks").val(remarks);

    $("#transfer_doc_btn a.btn, #remarks").addClass("hide");
    $(".remarks_count_link").css({float: ""});
    $("#transfer_document_modal").modal("show");
    $.ajax({
        url: "/erp/stores/stock_transfer/document/",
        type: "post",
        datatype: "json",
        data: {transfer_id: transfer_id, enterprise_id : $('#enterprise_id').val()},
        success: function (response) {
            if(response.remarks != undefined) {
                if(response.remarks.length > 0){
                    $(".remarks_count_link").removeClass('disabled').find(".remarks_counter").text(response.remarks.length);
                }
                else {
                    $(".remarks_count_link").addClass('disabled').find(".remarks_counter").text("No ");
                }
                $("#stock_transfer_document_remarks").val(JSON.stringify(response.remarks));
            }
            var row = '<object id="stock_transfer_document" class="document_viewer" data="'+response.url+'">'+getPdfErrorMessage()+'</object>';
            $("#stock_transfer_document_container").html(row);
            $("#modal_transfer_id").val(transfer_id);
            $("#display_popup").removeClass('hide');
            if($("#status_status").val() == 1) {
                $("#approve_stock_transfer,#reject_stock_transfer, #remarks").removeClass("hide");
                $('#approve_stock_transfer').text("Approve");
                $("#reject_stock_transfer").text("Reject");
            }
            else if($("#status_status").val() == 4) {
                $("#approve_stock_transfer, #remarks").removeClass("hide");
                $('#approve_stock_transfer').text("Approve");
            }
            else if($("#status_status").val() == 2) {
				$("#approve_stock_transfer, #remarks, #reject_stock_transfer").removeClass("hide");
				$('#approve_stock_transfer').text("Received");
				$("#reject_stock_transfer").text("Reject");
            }

            if($("#remarks").hasClass("hide") || $("#remarks").length <= 0) {
                $(".remarks_count_link").css({float: "left"});
            }
            else {
                $(".remarks_count_link").css({float: "right"});
            }
            $("#remarks").val("");
        },
        error: function() {
            swal("","Unable to generate document. Please try again", "error");
            $("#transfer_document_modal").modal("hide");
        }
    });
    $('#transfer_document_modal').on('hidden.bs.modal', function () {
        $(".remarks_count_link").css({float: "right"});
        $("#stock_transfer_document").remove();
        $("#stock_transfer_document_container").html(getPdfLoadingImage());
    });
}
	function approveStockTransfer() {
	 	var remarks = $("#remarks").val().trim();
		 var jsonRemarksData = null;
		if(remarks){
		var jsonRemarksData = JSON.stringify(remarks);
		}
		transferStatus = $('#status_status').val();
		var transferStatus;
		if(transferStatus == 1){
			transfer_status = 2 }
		if(transferStatus == 2){
			transfer_status = 3 }
		if(transferStatus == 4){
			transfer_status = 2 }
   	   var stockTransferData = {
        "enterprise_id": $('#status_enteriprise_id').val(),
        "total_qty": $('#status_total_qty').val(),
        "total_value": $('#status_total_value').val(),
        "last_modified_by": $('#login_user_id').val(),
        "from_location": $('#status_from_location').val(),
        "from_location_name": $('#status_from_location_name').val(),
        "to_location_name": $('#status_to_location_name').val(),
        "to_location": $('#status_to_location').val(),
        "status": transfer_status,
        "pp_id":$('#id_pp_id').val(),
        "remarks": jsonRemarksData,
        "id": $('#status_id').val()
    };
    if (transferStatus == 1) {
        stockTransferData.approved_by = $('#login_user_id').val();
    } else if (transferStatus == 2) {
        stockTransferData.received_by = $('#login_user_id').val();
    } else {
        stockTransferData.created_by = $('#login_user_id').val();
    }

    console.log("stockTransferData", stockTransferData);

    $.ajax({
        url: "/erp/stores/json/stock_transfer/",
        type: "POST",
        dataType: "json",
        data: stockTransferData,
        success: function(response) {
            if (response.response_code == 200) {
                var text_message = (transfer_status == 2) ?
                    "Stock Transfer Request has been Approved successfully." :
                    "Stock Transfer Request has been Received successfully.";

                swal({
                    title: "",
                    text: text_message,
                    type: "success"
                }, function() {
                    var enterpriseId = $('#enterprise_id').val();
                    var userId = $('#login_user_id').val();
                    var url = "/erp/stores/json/stock_transfer_list/?enterprise_id=" + encodeURIComponent(enterpriseId) + "&user_id=" + encodeURIComponent(userId);
                    window.location.href = url;
                });
            } else {
                swal({
                    title: "",
                    text: "Failed to save Internal Stock Transfer Request",
                    type: "error"
                });
            }
        },
        error: function(xhr, status, error) {
            swal({
                title: "",
                text: "Failed to process the request. Please try again later.",
                type: "error"
            });
        }
    });

    $("html, body").animate({ scrollTop: 0 }, "fast");
}

function rejectStockTransfer(){
var remarks = $("#remarks").val().trim();
		 var jsonRemarksData = null;
		if(remarks){
		var jsonRemarksData = JSON.stringify(remarks);
		}
    if($('#reject_stock_transfer').text() == 'Reject'  && $('#remarks').val() == "") {
            swal({
                title: "<span style='color: #dd4b39;'>Remarks Required.</span>",
                text: "Please enter your purpose of rejecting this Internal Stock Transfer Request as remarks.",
                type: "warning",
                html: true
            },
            function(){
                $('#remarks').focus();
            });
    }
    else{
		transferStatus = $('#status_status').val();
   	   var stockTransferData = {
        "enterprise_id": $('#status_enteriprise_id').val(),
        "total_qty": $('#status_total_qty').val(),
        "total_value": $('#status_total_value').val(),
        "last_modified_by": $('#login_user_id').val(),
        "from_location": $('#status_from_location').val(),
        "from_location_name": $('#status_from_location_name').val(),
        "to_location": $('#status_to_location').val(),
        "status": 4,
        "pp_id":$('#id_pp_id').val(),
        "created_by": $('#login_user_id').val(),
        "remarks": jsonRemarksData,
        "id": $('#status_id').val()
    };
    $.ajax({
        url: "/erp/stores/json/stock_transfer/",
        type: "POST",
        dataType: "json",
        data: stockTransferData,
        success: function(response) {
            if (response.response_code == 200) {
                var text_message = "Internal Stock Transfer Request has been Rejected";

                swal({
                    title: "",
                    text: text_message,
                    type: "success"
                }, function() {
                    var enterpriseId = $('#enterprise_id').val();
                    var userId = $('#login_user_id').val();
                    var url = "/erp/stores/json/stock_transfer_list/?enterprise_id=" + encodeURIComponent(enterpriseId)+ "&user_id=" + encodeURIComponent(userId);;
                    window.location.href = url;
                });
            } else {
                swal({
                    title: "",
                    text: "Failed to save Internal Stock Transfer Request",
                    type: "error"
                });
            }
        },
        error: function(xhr, status, error) {
            swal({
                title: "",
                text: "Failed to process the request. Please try again later.",
                type: "error"
            });
        }
    });

    $("html, body").animate({ scrollTop: 0 }, "fast");
	}
	}
</script>
{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
{% endblock %}