<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg class="img-responsive" style="height: 75vh; min-height: 600px;"
	xmlns="http://www.w3.org/2000/svg"
	xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1240px" height="935px" viewBox="-0.5 -0.5 926 701" style="background-color: rgb(255, 255, 255);">
	<defs>
		<linearGradient x1="0%" y1="0%" x2="100%" y2="0%" id="mx-gradient-ea0001-1-ad0002-1-e-0">
			<stop offset="0%" style="stop-color:#EA0001"/>
			<stop offset="100%" style="stop-color:#AD0002"/>
		</linearGradient>
		<linearGradient x1="0%" y1="0%" x2="100%" y2="0%" id="mx-gradient-5d7f99-1-23445d-1-e-0">
			<stop offset="0%" style="stop-color:#5d7f99"/>
			<stop offset="100%" style="stop-color:#23445d"/>
		</linearGradient>
		<linearGradient x1="0%" y1="0%" x2="100%" y2="0%" id="mx-gradient-7fb61a-1-1b622a-1-e-0">
			<stop offset="0%" style="stop-color:#7FB61A"/>
			<stop offset="100%" style="stop-color:#1B622A"/>
		</linearGradient>
		<linearGradient x1="0%" y1="0%" x2="100%" y2="0%" id="mx-gradient-5398db-1-1247b9-1-e-0">
			<stop offset="0%" style="stop-color:#5398DB"/>
			<stop offset="100%" style="stop-color:#1247B9"/>
		</linearGradient>
		<linearGradient x1="0%" y1="0%" x2="100%" y2="0%" id="mx-gradient-8d029d-1-65009e-1-e-0">
			<stop offset="0%" style="stop-color:#8D029D"/>
			<stop offset="100%" style="stop-color:#65009E"/>
		</linearGradient>
		<linearGradient x1="0%" y1="0%" x2="100%" y2="0%" id="mx-gradient-f19001-1-c23603-1-e-0">
			<stop offset="0%" style="stop-color:#F19001"/>
			<stop offset="100%" style="stop-color:#C23603"/>
		</linearGradient>
		<linearGradient x1="0%" y1="0%" x2="100%" y2="0%" id="mx-gradient-004195-1-209be1-1-e-0">
			<stop offset="0%" style="stop-color:#004195"/>
			<stop offset="100%" style="stop-color:#209be1"/>
		</linearGradient>
		<linearGradient x1="0%" y1="0%" x2="100%" y2="0%" id="mx-gradient-10739e-1-9673a6-1-e-0">
			<stop offset="0%" style="stop-color:#10739E"/>
			<stop offset="100%" style="stop-color:#9673A6"/>
		</linearGradient>
		<style type="text/css">@import url(https://fonts.googleapis.com/css?family=Roboto);&#xa;</style>
		<filter id="dropShadow">
			<feGaussianBlur in="SourceAlpha" stdDeviation="1.7" result="blur"/>
			<feOffset in="blur" dx="3" dy="3" result="offsetBlur"/>
			<feFlood flood-color="#3D4574" flood-opacity="0.4" result="offsetColor"/>
			<feComposite in="offsetColor" in2="offsetBlur" operator="in" result="offsetBlur"/>
			<feBlend in="SourceGraphic" in2="offsetBlur"/>
		</filter>
	</defs>
	<g filter="url(#dropShadow)">
		<path d="M 474 60 L 919 60 L 919 60 L 919 140 L 919 220 L 919 220 L 474 220 L 504 140 Z" fill-opacity="0.5" fill="url(#mx-gradient-5d7f99-1-23445d-1-e-0)" stroke="none" transform="rotate(180,696.5,140)" pointer-events="none"/>
		<path d="M 110.03 427 L 110.03 442 L 408.63 442" fill="none" stroke="#65009e" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
		<path d="M 413.88 442 L 406.88 445.5 L 408.63 442 L 406.88 438.5 Z" fill="#65009e" stroke="#65009e" stroke-miterlimit="10" pointer-events="all"/>
		<path d="M 474 490.4 L 914 490.4 L 914 490.4 L 914 570.4 L 914 650.4 L 914 650.4 L 474 650.4 L 504 570.4 Z" fill-opacity="0.5" fill="url(#mx-gradient-ea0001-1-ad0002-1-e-0)" stroke="none" transform="rotate(180,694,570.4)" pointer-events="all"/>
		<path d="M 474 60 L 919 60 L 919 60 L 919 140 L 919 220 L 919 220 L 474 220 L 504 140 Z" fill-opacity="0.5" fill="url(#mx-gradient-7fb61a-1-1b622a-1-e-0)" stroke="none" transform="rotate(180,457.5,140)" pointer-events="all"/>
		<path d="M 0 267 L 440 267 L 440 267 L 440 347 L 440 427 L 440 427 L 0 427 L 30 347 Z" fill-opacity="0.5" fill="url(#mx-gradient-5398db-1-1247b9-1-e-0)" stroke="none" transform="rotate(180,220,347)" pointer-events="all"/>
		<path d="M 474 267 L 914 267 L 914 267 L 914 347 L 914 427 L 914 427 L 474 427 L 504 347 Z" fill-opacity="0.5" fill="url(#mx-gradient-8d029d-1-65009e-1-e-0)" stroke="none" transform="rotate(180,694,347)" pointer-events="all"/>
		<path d="M 0 490.4 L 440 490.4 L 440 490.4 L 440 570.4 L 440 650.4 L 440 650.4 L 0 650.4 L 30 570.4 Z" fill-opacity="0.5" fill="url(#mx-gradient-f19001-1-c23603-1-e-0)" stroke="none" transform="rotate(180,220,570.4)" pointer-events="all"/>
		<rect x="191" y="70" width="140" height="40" rx="6" ry="6" fill="#ffffff" stroke="#ffffff" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 90px; margin-left: 190px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<font size="1" color="#1b622a">
									<span style="font-size: 15px">Delivery Challan</span>
								</font>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="681" y="94" fill="#000000" font-family="Roboto" font-size="12px" text-anchor="middle">Delivery Challan</text>
			</switch>
		</g>
		<rect x="13.2" y="70" width="170" height="45" rx="7.5" ry="7.5" fill="#ffffff" stroke="#ffffff" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 168px; height: 1px; padding-top: 95px; margin-left: 14px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<font style="font-size: 15px" color="#1b622a">Goods Receipt Note (GRN)</font>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="116" y="99" fill="#000000" font-family="Roboto" font-size="12px" text-anchor="middle">Goods Receipt Note (GRN)</text>
			</switch>
		</g>
		<rect xmlns="http://www.w3.org/2000/svg" x="651.26" y="166.45" width="148.74" height="48" rx="14.4" ry="14.4" fill="#b1ddf0" stroke="none" pointer-events="none"/>
		<g xmlns="http://www.w3.org/2000/svg" transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
					<div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 147px; height: 1px; padding-top: 190px; margin-left: 652px;">
						<div data-drawio-colors="color: #10739E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
							<div style="display: inline-block; font-size: 14px; font-family: Roboto; color: rgb(16, 115, 158); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><span style="color: rgb(0 , 65 , 149)">Internal Stock Transfers</span></div>
						</div>
					</div>
				</foreignObject><text x="726" y="195" fill="#10739E" font-family="Roboto" font-size="14px" text-anchor="middle">Internal Stock Transf...</text>
			</switch>
		</g>

		<rect x="485" y="127.68" width="160" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#ffffff" pointer-events="none"/>
		<g xmlns="http://www.w3.org/2000/svg" transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
					<div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 143px; margin-left: 486px;">
						<div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
								<font style="font-size: 15px" color="#23445d">Bill of Materials</font>
							</div>
						</div>
					</div>
				</foreignObject><text x="565" y="146" fill="rgb(0, 0, 0)" font-family="Roboto" font-size="12px" text-anchor="middle">Bill of Materials</text>
			</switch>
		</g>

		<rect xmlns="http://www.w3.org/2000/svg" x="653" y="70" width="155" height="50" rx="7.5" ry="7.5" fill="#ffffff" stroke="#ffffff" pointer-events="none"/>
		<g xmlns="http://www.w3.org/2000/svg" transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
					<div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 153px; height: 1px; padding-top: 95px; margin-left: 654px;">
						<div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
								<font size="1" color="#23445d"><span style="font-size: 15px">Manufacturing<br />Indents</span></font>
							</div>
						</div>
					</div>
				</foreignObject><text x="731" y="99" fill="rgb(0, 0, 0)" font-family="Roboto" font-size="12px" text-anchor="middle">Manufacturing...</text>
			</switch>
		</g>

		<rect xmlns="http://www.w3.org/2000/svg" x="485" y="68.43" width="160" height="51.57" rx="7.74" ry="7.74" fill="#ffffff" stroke="#ffffff" pointer-events="none"/>
		<g xmlns="http://www.w3.org/2000/svg" transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
					<div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 94px; margin-left: 486px;">
						<div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
								<font size="1" color="#23445d"><span style="font-size: 15px">Production Plan</span></font>
							</div>
						</div>
					</div>
				</foreignObject><text x="565" y="98" fill="rgb(0, 0, 0)" font-family="Roboto" font-size="12px" text-anchor="middle">Production Plan</text>
			</switch>
		</g>



		<rect xmlns="http://www.w3.org/2000/svg" x="653" y="128" width="150" height="30" rx="9" ry="9" fill="#b1ddf0" stroke="none" pointer-events="none"/>
		<g xmlns="http://www.w3.org/2000/svg" transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
					<div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 143px; margin-left: 654px;">
						<div data-drawio-colors="color: #10739E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
							<div style="display: inline-block; font-size: 14px; font-family: Roboto; color: rgb(16, 115, 158); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><span style="color: rgb(0 , 65 , 149)">Shortage Calculator</span></div>
						</div>
					</div>
				</foreignObject><text x="728" y="147" fill="#10739E" font-family="Roboto" font-size="14px" text-anchor="middle">Shortage Calculator</text>
			</switch>
		</g>
		
		<rect x="643.5" y="0" width="75" height="20" fill="none" stroke="none" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 73px; height: 1px; padding-top: 10px; margin-left: 225px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<font style="font-size: 14px">
									<span>Goods Out</span>
								</font>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="681" y="14" fill="#000000" font-family="Roboto" font-size="12px" text-anchor="middle">Goods Out</text>
			</switch>
		</g>
		<path d="M 261 70 L 261 26.37" fill="none" stroke="#1b622a" stroke-miterlimit="10" pointer-events="stroke"/>
		<path d="M 261 21.12 L 264.5 28.12 L 261 26.37 L 257.5 28.12 Z" fill="#1b622a" stroke="#1b622a" stroke-miterlimit="10" pointer-events="all"/>
		<path d="M 503.92 262.55 L 503.92 244 L 222.5 244 L 222.5 226.37" fill="none" stroke="#1b622a" stroke-miterlimit="10" pointer-events="none"/>
		<path d="M 503.92 267.8 L 500.42 260.8 L 503.92 262.55 L 507.42 260.8 Z" fill="#1b622a" stroke="#1b622a" stroke-miterlimit="10" pointer-events="none"/>
		<path d="M 222.5 221.12 L 226 228.12 L 222.5 226.37 L 219 228.12 Z" fill="#1b622a" stroke="#1b622a" stroke-miterlimit="10" pointer-events="none"/>
		<g xmlns="http://www.w3.org/2000/svg" transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
					<div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 63px; height: 1px; padding-top: 243px; margin-left: 442px;">
						<div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
								<font style="font-size: 14px">
									<span>Goods Transfers</span>
								</font>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="474" y="247" fill="rgb(0, 0, 0)" font-family="Roboto" font-size="12px" text-anchor="middle">Goods Trans...</text>
			</switch>
		</g>

		<g xmlns="http://www.w3.org/2000/svg" transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
					<div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 63px; height: 1px; padding-top: 40px; margin-left: 477px;">
						<div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
								<font style="font-size: 14px">
									<span>Goods Transfers</span>
								</font>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="509" y="44" fill="rgb(0, 0, 0)" font-family="Roboto" font-size="12px" text-anchor="middle">Goods Trans...</text>
			</switch>
		</g>
		<path d="M 696.5 53.63 L 696.5 40 L 368.02 40 L 368.02 54.59" fill="none" stroke="#1b622a" stroke-miterlimit="10" pointer-events="none"/>
		<path d="M 696.5 58.88 L 693 51.88 L 696.5 53.63 L 700 51.88 Z" fill="#1b622a" stroke="#1b622a" stroke-miterlimit="10" pointer-events="none"/>
		<path d="M 368.02 59.84 L 364.52 52.84 L 368.02 54.59 L 371.52 52.84 Z" fill="#1b622a" stroke="#1b622a" stroke-miterlimit="10" pointer-events="none"/>
		<rect x="84" y="0" width="65" height="20" fill="none" stroke="none" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 63px; height: 1px; padding-top: 10px; margin-left: 85px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<font style="font-size: 14px">
									<span>Goods In</span>
								</font>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="117" y="14" fill="#000000" font-family="Roboto" font-size="12px" text-anchor="middle">Goods In</text>
			</switch>
		</g>
		<rect x="150" y="277" width="170" height="60" rx="9" ry="9" fill="#ffffff" stroke="#ffffff" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 168px; height: 1px; padding-top: 307px; margin-left: 151px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<font style="font-size: 15px" color="#1247b9">Purchase Order</font>
								<span style="font-size: 15px">
									<font color="#1247b9">
										<br />(PO)
									</font>
									<br />
								</span>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="235" y="311" fill="#000000" font-family="Roboto" font-size="12px" text-anchor="middle">Purchase Order...</text>
			</switch>
		</g>
		<path d="M 594.76 214 L 604.76 194 L 664.76 194 L 654.76 214 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
		<g xmlns="http://www.w3.org/2000/svg" transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
					<div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 202px; margin-left: 607px;">
						<div data-drawio-colors="color: #000099; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
							<div style="display: inline-block; font-size: 17px; font-family: Roboto; color: rgb(0, 0, 153); line-height: 1.2; pointer-events: none; white-space: nowrap;">
								<font style="font-size: 12px">Issues</font>
							</div>
						</div>
					</div>
				</foreignObject><text x="607" y="207" fill="#000099" font-family="Roboto" font-size="17px">Issues</text>
			</switch>
		</g>
		<path d="M 554.26 190.3 L 564.26 170.3 L 674.26 170.3 L 664.26 190.3 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
		<g xmlns="http://www.w3.org/2000/svg" transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
					<div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 178px; margin-left: 566px;">
						<div data-drawio-colors="color: #000099; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
							<div style="display: inline-block; font-size: 17px; font-family: Roboto; color: rgb(0, 0, 153); line-height: 1.2; pointer-events: none; white-space: nowrap;">
								<font style="font-size: 12px">Internal Receipts</font>
							</div>
						</div>
					</div>
				</foreignObject><text x="566" y="183" fill="#000099" font-family="Roboto" font-size="17px">Internal Rece...</text>
			</switch>
		</g>
		<path d="M 54.7 323 L 64.7 303 L 174.7 303 L 164.7 323 Z" fill="#b1ddf0" stroke="none" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 311px; margin-left: 67px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: left; ">
							<div style="display: inline-block; font-size: 17px; font-family: Roboto; color: #000099; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
								<span style="font-size: 12px">Job Orders
									<span> (Out)</span>
								</span>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="168" y="317" fill="#000099" font-family="Roboto" font-size="17px">Job Orders (O...</text>
			</switch>
		</g>
		<path d="M 14.6 348 L 24.6 328 L 164.6 328 L 154.6 348 Z" fill="#b1ddf0" stroke="none" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 336px; margin-left: 27px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: left; ">
							<div style="display: inline-block; font-size: 17px; font-family: Roboto; color: #000099; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
								<span style="font-size: 12px">Purchase Price profiles</span>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="27" y="341"  fill="#000099" font-family="Roboto" font-size="17px">Purchase Price p...</text>
			</switch>
		</g>
		<rect x="126.2" y="230.3" width="65" height="20" fill="none" stroke="none" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 63px; height: 1px; padding-top: 240px; margin-left: 127px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<font style="font-size: 14px">
									<span>Goods Transfers</span>
								</font>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="159" y="244" fill="#000000" font-family="Roboto" font-size="12px" text-anchor="middle">Goods Trans...</text>
			</switch>
		</g>
		<rect x="505" y="277" width="189" height="60" rx="9" ry="9" fill="#ffffff" stroke="#ffffff" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 187px; height: 1px; padding-top: 307px; margin-left: 506px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<font color="#65009e">
									<span style="font-size: 15px">Order Acknowledgement</span>
									<span style="font-size: 15px">
										<span>
											<br />(
										</span>
									</span>
									<span style="font-size: 15px">Sales Order</span>
								</font>
								<span style="font-size: 15px">
									<font color="#65009e">)</font>
									<br />
								</span>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="600" y="311" fill="#000000" font-family="Roboto" font-size="12px" text-anchor="middle">Order Acknowledgement...</text>
			</switch>
		</g>
		<path d="M 674.26 323 L 684.26 303 L 778.26 303 L 768.26 323 Z" fill="#b1ddf0" stroke="none" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 312px; margin-left: 691px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: left; ">
							<div style="display: inline-block; font-size: 17px; font-family: Roboto; color: #000099; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
								<span style="font-size: 11px; color: #000099;">Job Sales
									<span> (In)</span>
								</span>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="686" y="318" fill="#000099" font-family="Roboto" font-size="17px">Job Sales (...</text>
			</switch>
		</g>
		<path d="M 664.26 348 L 674.26 328 L 788.26 328 L 778.26 348 Z" fill="#b1ddf0" stroke="none" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 337px; margin-left: 682px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: left; ">
							<div style="display: inline-block; font-size: 17px; font-family: Roboto; color: #000099; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
								<span style="font-size: 11px; color: #000099">Sale Price profiles</span>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="676" y="343" fill="#000099" font-family="Roboto" font-size="17px">Sale Price pr...</text>
			</switch>
		</g>

		<path d="M 116.46 261.62 L 116.23 259.11 Q 116 256.6 116 246.6 L 116 226.37" fill="none" stroke="#1b622a" stroke-miterlimit="10" pointer-events="stroke"/>
		<path d="M 116.94 266.85 L 112.81 260.19 L 116.46 261.62 L 119.79 259.56 Z" fill="#1b622a" stroke="#1b622a" stroke-miterlimit="10" pointer-events="all"/>
		<path d="M 116 221.12 L 119.5 228.12 L 116 226.37 L 112.5 228.12 Z" fill="#1b622a" stroke="#1b622a" stroke-miterlimit="10" pointer-events="all"/>

		<rect x="31.2" y="450" width="65" height="20" fill="none" stroke="none" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 63px; height: 1px; padding-top: 460px; margin-left: 118px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<font style="font-size: 14px">
									<span>Purchase Invoice</span>
								</font>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="64" y="464" fill="#000000" font-family="Roboto" font-size="12px" text-anchor="middle">Purchase In...</text>
			</switch>
		</g>
		<path d="M 694 427 L 694 484.03" fill="none" stroke="#65009e" stroke-miterlimit="10" pointer-events="stroke"/>
		<path d="M 694 489.28 L 690.5 482.28 L 694 484.03 L 697.5 482.28 Z" fill="#65009e" stroke="#65009e" stroke-miterlimit="10" pointer-events="all"/>
		<rect x="487.25" y="583.9" width="122.5" height="47.5" rx="7.13" ry="7.13" fill="#ffffff" stroke="#ffffff" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 121px; height: 1px; padding-top: 608px; margin-left: 488px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<span style="font-size: 15px">
									<font color="#ad0002">Bank Reconciliation</font>
								</span>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="549" y="611" fill="#000000" font-family="Roboto" font-size="12px" text-anchor="middle">Bank Reconciliation</text>
			</switch>
		</g>
		<rect x="626.01" y="502.9" width="140" height="40.5" rx="6.08" ry="6.08" fill="#ffffff" stroke="#ffffff" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 523px; margin-left: 627px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<span style="font-size: 15px">
									<font color="#ad0002">Vouchers</font>
								</span>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="696" y="527" fill="#000000" font-family="Roboto" font-size="12px" text-anchor="middle">Vouchers</text>
			</switch>
		</g>
		<rect x="505" y="515.9" width="80" height="44" rx="6.6" ry="6.6" fill="#ffffff" stroke="#ffffff" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 538px; margin-left: 506px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<span style="font-size: 15px">
									<font color="#ad0002">Bills</font>
								</span>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="545" y="542" fill="#000000" font-family="Roboto" font-size="12px" text-anchor="middle">Bills</text>
			</switch>
		</g>
		<rect x="621.01" y="583.9" width="150" height="51.5" rx="7.72" ry="7.72" fill="#ffffff" stroke="#ffffff" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 610px; margin-left: 622px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<span style="font-size: 15px">
									<font color="#ad0002">Financial Statement</font>
								</span>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="696" y="613" fill="#000000" font-family="Roboto" font-size="12px" text-anchor="middle">Financial Statement</text>
			</switch>
		</g>
		<rect x="713" y="437" width="65" height="20" fill="none" stroke="none" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 63px; height: 1px; padding-top: 447px; margin-left: 714px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<font style="font-size: 14px">
									<span>Receivables</span>
								</font>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="746" y="451" fill="#000000" font-family="Roboto" font-size="12px" text-anchor="middle">Receivables</text>
			</switch>
		</g>
		<path d="M 480 442 L 589 442 L 589 484.03" fill="none" stroke="#c23603" stroke-miterlimit="10" pointer-events="stroke"/>
		<path d="M 589 489.28 L 585.5 482.28 L 589 484.03 L 592.5 482.28 Z" fill="#c23603" stroke="#c23603" stroke-miterlimit="10" pointer-events="all"/>
		<rect x="415" y="427" width="65" height="30" fill="none" stroke="none" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 63px; height: 1px; padding-top: 442px; margin-left: 416px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<font style="font-size: 14px">
									<span>Payables</span>
								</font>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="447" y="446" fill="#000000" font-family="Roboto" font-size="12px" text-anchor="middle">Payables</text>
			</switch>
		</g>
		<path d="M 270.03 490.4 L 270.03 442 L 408.63 442" fill="none" stroke="#c23603" stroke-miterlimit="10" pointer-events="stroke"/>
		<path d="M 413.88 442 L 406.88 445.5 L 408.63 442 L 406.88 438.5 Z" fill="#c23603" stroke="#c23603" stroke-miterlimit="10" pointer-events="all"/>
		
		<rect x="83.7" y="557.4" width="50" height="45" fill="none" stroke="none" transform="translate(2,3)" opacity="0.25"/>
		<rect x="83.7" y="557.4" width="50" height="45" fill="none" stroke="none" pointer-events="all"/>
		<path d="M 110.25 578.79 L 116.95 557.4 C 119.9 557.67 122.76 558.59 125.32 560.09 L 116.9 575.04 L 122.02 574.98 L 110.6 587.96 L 114.41 578.79 Z M 112.48 567.18 C 106.92 568.14 102.79 572.87 102.6 578.5 C 102.4 584.13 106.19 589.13 111.67 590.48 C 117.15 591.82 122.83 589.15 125.27 584.07 C 127.71 578.98 126.25 572.89 121.77 569.46 L 124.72 565.15 L 126.85 563.89 L 129.54 566.52 L 127.3 570.22 L 128.37 572.25 L 132.68 572.15 L 133.7 575.69 L 129.89 577.72 L 129.89 580.1 L 133.65 582.13 L 132.74 585.68 L 128.32 585.58 L 127.25 587.7 L 129.49 591.25 L 126.8 593.94 L 123.14 591.66 L 121.21 592.72 L 121.26 597.08 L 117.71 598.09 L 115.63 594.29 L 113.34 594.29 L 111.26 598.09 L 107.66 597.08 L 107.81 592.77 L 105.78 591.66 L 101.97 593.94 L 99.44 591.25 L 101.72 587.55 L 100.6 585.58 L 96.24 585.68 L 95.38 582.08 L 99.08 580.05 L 99.08 577.77 L 95.32 575.74 L 96.29 572.15 L 100.6 572.25 L 101.72 570.22 L 99.44 566.57 L 102.08 563.99 L 105.88 566.12 L 107.76 565.1 L 107.71 560.69 L 111.21 559.73 L 113.34 563.48 Z M 92.74 586.18 L 93.5 588.51 L 94.87 589.02 L 96.9 587.96 L 98.17 589.22 L 97.15 591.35 L 97.81 592.72 L 99.94 593.43 L 100.05 595.2 L 97.86 595.96 L 97.15 597.23 L 98.27 599.36 L 97 600.63 L 94.92 599.71 L 93.5 600.22 L 92.84 602.4 L 91.01 602.4 L 90.25 600.27 L 88.93 599.66 L 86.8 600.78 L 85.48 599.46 L 86.49 597.38 L 86.09 596.12 L 83.7 595.25 L 83.8 593.48 L 85.93 592.72 L 86.54 591.4 L 85.43 589.33 L 86.69 587.96 L 88.93 589.02 L 90.2 588.51 L 90.86 586.29 L 92.58 586.18 Z M 91.97 591.66 C 91.43 591.66 90.52 591.95 90 592.47 C 89.48 593 89.18 593.7 89.18 594.44 C 89.18 595.98 90.43 597.23 91.97 597.23 C 93.52 597.23 94.77 595.98 94.77 594.44 C 94.77 592.9 93.52 591.66 91.97 591.66" fill="#000000" stroke="none" transform="translate(2,3)rotate(180,108.7,579.9)" opacity="0.25"/>
		<path d="M 110.25 578.79 L 116.95 557.4 C 119.9 557.67 122.76 558.59 125.32 560.09 L 116.9 575.04 L 122.02 574.98 L 110.6 587.96 L 114.41 578.79 Z M 112.48 567.18 C 106.92 568.14 102.79 572.87 102.6 578.5 C 102.4 584.13 106.19 589.13 111.67 590.48 C 117.15 591.82 122.83 589.15 125.27 584.07 C 127.71 578.98 126.25 572.89 121.77 569.46 L 124.72 565.15 L 126.85 563.89 L 129.54 566.52 L 127.3 570.22 L 128.37 572.25 L 132.68 572.15 L 133.7 575.69 L 129.89 577.72 L 129.89 580.1 L 133.65 582.13 L 132.74 585.68 L 128.32 585.58 L 127.25 587.7 L 129.49 591.25 L 126.8 593.94 L 123.14 591.66 L 121.21 592.72 L 121.26 597.08 L 117.71 598.09 L 115.63 594.29 L 113.34 594.29 L 111.26 598.09 L 107.66 597.08 L 107.81 592.77 L 105.78 591.66 L 101.97 593.94 L 99.44 591.25 L 101.72 587.55 L 100.6 585.58 L 96.24 585.68 L 95.38 582.08 L 99.08 580.05 L 99.08 577.77 L 95.32 575.74 L 96.29 572.15 L 100.6 572.25 L 101.72 570.22 L 99.44 566.57 L 102.08 563.99 L 105.88 566.12 L 107.76 565.1 L 107.71 560.69 L 111.21 559.73 L 113.34 563.48 Z M 92.74 586.18 L 93.5 588.51 L 94.87 589.02 L 96.9 587.96 L 98.17 589.22 L 97.15 591.35 L 97.81 592.72 L 99.94 593.43 L 100.05 595.2 L 97.86 595.96 L 97.15 597.23 L 98.27 599.36 L 97 600.63 L 94.92 599.71 L 93.5 600.22 L 92.84 602.4 L 91.01 602.4 L 90.25 600.27 L 88.93 599.66 L 86.8 600.78 L 85.48 599.46 L 86.49 597.38 L 86.09 596.12 L 83.7 595.25 L 83.8 593.48 L 85.93 592.72 L 86.54 591.4 L 85.43 589.33 L 86.69 587.96 L 88.93 589.02 L 90.2 588.51 L 90.86 586.29 L 92.58 586.18 Z M 91.97 591.66 C 91.43 591.66 90.52 591.95 90 592.47 C 89.48 593 89.18 593.7 89.18 594.44 C 89.18 595.98 90.43 597.23 91.97 597.23 C 93.52 597.23 94.77 595.98 94.77 594.44 C 94.77 592.9 93.52 591.66 91.97 591.66" fill="#ffffff" stroke="none" transform="rotate(180,108.7,579.9)" pointer-events="all"/>
		<rect x="30.0" y="602.4" width="112.5" height="22" rx="6.3" ry="6.3" fill="#ffffff" stroke="#ffffff" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 101px; height: 1px; padding-top: 624px; margin-left: 36px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<span style="font-size: 15px">
									<font style="color: #c23603; font-size: 12px;">
										<div>Debit/ Credit Note</div>
									</font>
									<br />
								</span>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="79" y="627" fill="#000000" font-family="Roboto" font-size="12px" text-anchor="middle">Debit Note...</text>
			</switch>
		</g>
		
		<path d="M 110 427 L 110 460 L 110 483.63" fill="none" stroke="#1247b9" stroke-miterlimit="10" pointer-events="stroke" stroke-dasharray="0" class="path-to-icd"/>
		<path d="M 110 488.88 L 106.5 481.88 L 110 483.63 L 113.5 481.88 Z" fill="#1247b9" stroke="#1247b9" stroke-miterlimit="10" pointer-events="all"/>
		<path d="M 117 20 L 117 40 L 117 53.63" fill="none" stroke="#1b622a" stroke-miterlimit="10" pointer-events="stroke"/>
		<path d="M 117.7 58.88 L 114.2 51.88 L 117.7 53.63 L 121.2 51.88 Z" fill="#1b622a" stroke="#1b622a" stroke-miterlimit="10" pointer-events="all"/>
		
		<image x="319.5" y="509.9" width="120" height="120" xlink:href="/site_media/images/clip/audit.png" preserveAspectRatio="none"/>
		<image x="339.5" y="75.5" width="120" height="120" xlink:href="/site_media/images/clip/inventory.png" preserveAspectRatio="none"/>
		<image x="799.5" y="75.5" width="120" height="120" xlink:href="/site_media/images/clip/production.png" preserveAspectRatio="none"/>

		<rect x="340" y="183.3" width="120" height="47" rx="7.05" ry="7.05" fill="url(#mx-gradient-004195-1-209be1-1-e-0)" stroke="none" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 207px; margin-left: 346px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 18px; font-family: Roboto; color: #FFFFFF; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<span style="font-size: 17px">Inventory
									<br />Management
									<br style="font-size: 18px" />
								</span>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="860" y="212" fill="#FFFFFF" font-family="Roboto" font-size="18px" text-anchor="middle">Inventory...</text>
			</switch>
		</g>

		<rect x="800" y="183.3" width="120" height="47" rx="7.05" ry="7.05" fill="url(#mx-gradient-004195-1-209be1-1-e-0)" stroke="none" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 207px; margin-left: 801px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 18px; font-family: Roboto; color: #FFFFFF; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<span style="font-size: 17px">Planning &amp;
									<br />Production
									<br style="font-size: 18px" />
								</span>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="860" y="212" fill="#FFFFFF" font-family="Roboto" font-size="18px" text-anchor="middle">Inventory...</text>
			</switch>
		</g>
		<rect x="324.5" y="618.4" width="120" height="26" rx="3.9" ry="3.9" fill="url(#mx-gradient-004195-1-209be1-1-e-0)" stroke="none" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 631px; margin-left: 326px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 18px; font-family: Roboto; color: #FFFFFF; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<span style="font-size: 18px">Internal Audit
									<br style="font-size: 18px" />
								</span>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="385" y="637" fill="#FFFFFF" font-family="Roboto" font-size="18px" text-anchor="middle">Internal Audi...</text>
			</switch>
		</g>
		<rect x="320" y="397" width="120" height="26" rx="3.9" ry="3.9" fill="url(#mx-gradient-004195-1-209be1-1-e-0)" stroke="none" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 410px; margin-left: 321px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 18px; font-family: Roboto; color: #FFFFFF; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<div style="text-align: left">
									<span>Purchase</span>
								</div>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="380" y="415" fill="#FFFFFF" font-family="Roboto" font-size="18px" text-anchor="middle">Purchase</text>
			</switch>
		</g>
		<image x="799.5" y="276.5" width="120" height="120" xlink:href="/site_media/images/clip/sales.png" preserveAspectRatio="none"/>
		<rect x="800" y="385" width="120" height="50" rx="7.5" ry="7.5" fill="url(#mx-gradient-004195-1-209be1-1-e-0)" stroke="none" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 410px; margin-left: 801px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 18px; font-family: Roboto; color: #FFFFFF; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<span style="font-size: 18px">Sales &amp; Invoicing
									<br style="font-size: 18px" />
								</span>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="860" y="415" fill="#FFFFFF" font-family="Roboto" font-size="18px" text-anchor="middle">Sales &amp; Invoi...</text>
			</switch>
		</g>
		
		<rect x="521.5" y="364" width="190" height="40" rx="12" ry="12" fill="#b1ddf0" stroke="none" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 188px; height: 1px; padding-top: 384px; margin-left: 523px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 14px; font-family: Roboto; color: #10739E; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<a href="/erp/admin/invoice_template/" target="_blank">
									<font color="#000099">
										<span style="white-space: nowrap">Invoice Print Templates</span>
										<br style="white-space: nowrap" />
										<span style="white-space: nowrap">(Configurable)</span>
									</font>
								</a>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="617" y="388" fill="#10739E" font-family="Roboto" font-size="14px" text-anchor="middle">Invoice Print Templates...</text>
			</switch>
		</g>
		
		<image x="799.5" y="509.9" width="120" height="120" xlink:href="/site_media/images/clip/accounts.png" preserveAspectRatio="none"/>
		
		<rect x="800" y="618.4" width="120" height="26" rx="3.9" ry="3.9" fill="url(#mx-gradient-004195-1-209be1-1-e-0)" stroke="none" pointer-events="all"/>
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 631px; margin-left: 801px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 18px; font-family: Roboto; color: #FFFFFF; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
								<span style="font-size: 18px">Accounts
									<br style="font-size: 18px" />
								</span>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="860" y="637" fill="#FFFFFF" font-family="Roboto" font-size="18px" text-anchor="middle">Accounts&#xa;</text>
			</switch>
		</g>
		
		<image x="319.5" y="286.5" width="120" height="120" xlink:href="/site_media/images/clip/purchase.png" preserveAspectRatio="none"/>
		
		<g transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 275px; margin-left: 426px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
							<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: #000099; line-height: 1.2; pointer-events: all; white-space: nowrap; "></div>
						</div>
					</div>
				</foreignObject>
				<text x="426" y="287" fill="#000099" font-family="Roboto" font-size="12px" text-anchor="middle"></text>
			</switch>
		</g>





		<!-- ARRANGEMENT ORDER -->



		<!--VOUCHER AUTO-->
		<g id="voucher_auto" class="voucher_auto custom-class configurable-checkbox svg-flag-value" data-value="16" role="button" >
			<rect x="627.26" y="539.5" width="154.49" height="35" rx="10.5" ry="10.5" fill="#b1ddf0" stroke="none" pointer-events="all" class="box-color"/>
			<g transform="translate(-0.5 -0.5)">
				<switch>
					<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
						<div
							xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 129px; height: 1px; padding-top: 558px; margin-left: 648px;">
							<div style="box-sizing: border-box; font-size: 0; text-align: left; ">
								<div style="display: inline-block; font-size: 14px; font-family: Roboto; color: #FFFFFF; line-height: 1.0; pointer-events: all; white-space: normal; word-wrap: normal; ">
									<div style="text-align: center">
										<font color="#000099" class="text-color">
											<span style="font-size: 12px">Verify Auto-generated Vouchers</span>
										</font>
									</div>
								</div>
							</div>
						</div>
					</foreignObject>
					<text x="652" y="560" fill="#FFFFFF" font-family="Roboto" font-size="14px">Verify Auto-generat...</text>
				</switch>
			</g>
			<ellipse id="label_flag_4" cx="628.26" cy="557.9" rx="15" ry="15" fill="#10739e" stroke="#ffffff" stroke-width="3" pointer-events="all" class="ellipse-color"/>
			<image id="label_flag_44" x="617.76" y="550.4" width="19" height="14.6" class="item-checkbox" xlink:href="/site_media/images/tick.png" preserveAspectRatio="none"/>
		</g>


		<!--ICD CONFIG-->
		<g id="icd_config" class="icd_config custom-class configurable-checkbox svg-flag-value" data-value="2" role="button" >
			<rect x="27.5" y="504" width="221" height="50.8" rx="7.62" ry="7.62" fill="#ffffff" stroke="#ffffff" pointer-events="all" class="box-color"/>
			<g transform="translate(-0.5 -0.5)">
				<switch>
					<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
						<div style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 219px; height: 1px; padding-top: 529px; margin-left: 29px;">
							<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
								<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
									<span style="font-size: 15px">
										<font color="#c23603" class="text-color">Internal Control Department
											<br />(ICD)
										</font>
									</span>
								</div>
							</div>
						</div>
					</foreignObject>
					<text x="138" y="533" fill="#000000" font-family="Roboto" font-size="12px" text-anchor="middle">Internal Control Department...</text>
				</switch>
			</g>
			<ellipse id="label_flag_1" cx="245" cy="515" rx="15" ry="15" fill="#10739e" stroke="#ffffff" stroke-width="3" pointer-events="all" class="ellipse-color"/>
			<image id="label_flag_11" x="235" y="508.2" width="19" height="14.6" class="item-checkbox" xlink:href="/site_media/images/tick.png" preserveAspectRatio="none" />
		</g>

		<!--DR/CR AUTO-->
		<g id="dr_cr_auto" class="dr_cr_auto custom-class configurable-checkbox svg-flag-value" data-value="8" role="button" >
			<rect x="30" y="568.84" width="69.5" height="25" rx="7.5" ry="7.5" fill="#b1ddf0" stroke="none" pointer-events="all" class="box-color"/>
			<g xmlns="http://www.w3.org/2000/svg" transform="translate(-0.5 -0.5)">
				<switch>
					<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
						<div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 67px; height: 1px; padding-top: 582px; margin-left: 29px;">
							<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
								<div style="display: inline-block; font-size: 14px; font-family: Roboto; color: #FFFFFF; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
									<span style="font-size: 12px ; white-space: nowrap">
										<font color="#000099" class="text-color">Automate</font>
									</span>
								</div>
							</div>
						</div>
					</foreignObject>
					<text x="62" y="585" fill="#FFFFFF" font-family="Roboto" font-size="14px" text-anchor="middle">Automate</text>
				</switch>
			</g>
			<ellipse id="label_flag_3" cx="103" cy="581" rx="11" ry="11" fill="#10739e" stroke="#ffffff" stroke-width="2" pointer-events="all" class="ellipse-color"/>
			<image id="label_flag_33" x="97.5" y="576.5" width="11.3" height="8.68" class="item-checkbox" xlink:href="/site_media/images/tick.png" preserveAspectRatio="none"/>
		</g>

		<!--SUGGEST CR NOTE-->
		<g id="suggest_cr_note" class="suggest_cr_note custom-class configurable-checkbox svg-flag-value" data-value="4" role="button" >
			<rect x="129.5" y="552" width="130.5" height="25" rx="7.5" ry="7.5" fill="#b1ddf0" stroke="none" pointer-events="all" class="box-color"/>
			<g transform="translate(-0.5 -0.5)">
				<switch>
					<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
						<div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 126px; height: 1px; padding-top: 565px; margin-left: 136px;">
							<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
								<div style="display: inline-block; font-size: 14px; font-family: Roboto; color: #FFFFFF; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
									<font color="#004195" class="text-color">
										<span style="font-size: 12px; color: #000099;">Ignore Credit Notes</span>
									</font>
								</div>
							</div>
						</div>
					</foreignObject>
					<text x="198" y="567" fill="#FFFFFF" font-family="Roboto" font-size="14px" text-anchor="middle">Suggest Credit Not...</text>
				</switch>
			</g>
			<ellipse id="label_flag_2" cx="126.5" cy="565" rx="11" ry="11" fill="#10739e" stroke="#ffffff" stroke-width="2" pointer-events="all" class="ellipse-color"/>
			<image id="label_flag_22" x="120.35" y="560.82" width="11.3" height="8.68" class="item-checkbox" xlink:href="/site_media/images/tick.png" preserveAspectRatio="none"/>
		</g>

		<!--REQUEST ACK-->
		<g id="request_ack" class="request_ack custom-class configurable-checkbox" role="button" >
			<rect x="28.5" y="626.5" width="185" height="22" rx="7.5" ry="7.5" fill="#b1ddf0" stroke="none" pointer-events="all" class="box-color"/>
			<g transform="translate(-0.5 -0.5)">
				<switch>
					<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
						<div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 260px; height: 1px; padding-top: 638px; margin-left: -4px;">
							<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
								<div style="display: inline-block; font-size: 14px; font-family: Roboto; color: #FFFFFF; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
									<font color="#004195" class="text-color">
										<span style="font-size: 12px; color: #000099;">Request Acknowledgement</span>
									</font>
								</div>
							</div>
						</div>
					</foreignObject>
					<text x="198" y="567" fill="#FFFFFF" font-family="Roboto" font-size="14px" text-anchor="middle">Request Acknowledgement</text>
				</switch>
			</g>
			<ellipse cx="33.5" cy="637" rx="11" ry="11" fill="#10739e" stroke="#ffffff" stroke-width="2" pointer-events="all" class="ellipse-color"/>
			<image x="28.5" y="633" width="11.3" height="8.68" class="item-checkbox" xlink:href="/site_media/images/tick.png" preserveAspectRatio="none"/>
		</g>

		<!--MANDATE PO-->
		<g id="mandate_po" class="mandate_po custom-class configurable-checkbox" data-value="1" role="button" >
			<rect x="170" y="357" width="138.75" height="25" rx="7.5" ry="7.5" fill="#b1ddf0" stroke="none" pointer-events="all" class="box-color" />
			<g transform="translate(-0.5 -0.5)">
				<switch>
					<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
						<div
							xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 164px; height: 1px; padding-top: 371px; margin-left: 150px;">
							<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
								<div style="display: inline-block; font-size: 14px; font-family: Roboto; color: #10739E; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
									<p xmlns="http://www.w3.org/1999/xhtml" style="line-height: 90% ; font-size: 12px">
										<font color="#000099">Mandate PO <br />for all Purchase</font><br />
									</p>
								</div>
							</div>
						</div>
					</foreignObject>
					<text x="223" y="373" fill="#10739E" font-family="Roboto" font-size="14px" text-anchor="middle">Mandate PO...</text>
				</switch>
			</g>
			<ellipse cx="296.51" cy="369.5" rx="12.5" ry="12.5" fill="#10739e" stroke="#ffffff" stroke-width="3" pointer-events="all" class="ellipse-color"/>
			<image x="289.01" y="363.59" width="15" height="11.53" class="item-checkbox" xlink:href="/site_media/images/tick.png" preserveAspectRatio="none"/>
		</g>



		<!--BLANKET PO-->
		<g id="blanket_po" class="blanket_po custom-class configurable-checkbox" data-value="1" role="button" >
			<rect x="39.25" y="395" width="100" height="25" rx="7.5" ry="7.5" fill="#b1ddf0" stroke="none" pointer-events="all" class="box-color" />
			<g transform="translate(-0.5 -0.5)">
				<switch>
					<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
						<div
							xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 103px; height: 1px; padding-top: 408px; margin-left: 37px;">
							<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
								<div style="display: inline-block; font-size: 14px; font-family: Roboto; color: #10739E; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
									<p xmlns="http://www.w3.org/1999/xhtml" style="line-height: 90% ; font-size: 12px">
										<font color="#000099">Blanket PO</font><br />
									</p>
								</div>
							</div>
						</div>
					</foreignObject>
					<text x="232" y="407" fill="#10739E" font-family="Roboto" font-size="14px" text-anchor="middle">Blanket PO</text>
				</switch>
			</g>
			<ellipse cx="140.51" cy="408.5" rx="12.5" ry="12.5" fill="#10739e" stroke="#ffffff" stroke-width="3" pointer-events="all" class="ellipse-color"/>
			<image x="133.01" y="402.59" width="15" height="11.53" class="item-checkbox" xlink:href="/site_media/images/tick.png" preserveAspectRatio="none"/>
		</g>

		<!--DELIVERY SCHEDULE-->
		<g id="delivery_schedule" class="delivery_schedule custom-class configurable-checkbox" data-value="1" role="button" >
			<rect x="165.25" y="391" width="132.5" height="25" rx="7.5" ry="7.5" fill="#b1ddf0" stroke="none" pointer-events="all" class="box-color" />
			<g transform="translate(-0.5 -0.5)">
				<switch>
					<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
						<div
								xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 103px; height: 1px; padding-top: 404px; margin-left: 174px;">
							<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
								<div style="display: inline-block; font-size: 14px; font-family: Roboto; color: #10739E; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
									<p xmlns="http://www.w3.org/1999/xhtml" style="line-height: 90% ; font-size: 12px">
										<font color="#000099">Delivery Schedules</font><br />
									</p>
								</div>
							</div>
						</div>
					</foreignObject>
					<text x="232" y="407" fill="#10739E" font-family="Roboto" font-size="14px" text-anchor="middle">Delivery Schedules</text>
				</switch>
			</g>
			<ellipse cx="296.51" cy="403.5" rx="12.5" ry="12.5" fill="#10739e" stroke="#ffffff" stroke-width="3" pointer-events="all" class="ellipse-color"/>
			<image x="289.01" y="397.59" width="15" height="11.53" class="item-checkbox" xlink:href="/site_media/images/tick.png" preserveAspectRatio="none"/>
		</g>

		<g id="is_price_modification_disabled" class="is_price_modification_disabled custom-class configurable-checkbox" data-value="1" role="button" >
			<rect x="15.25" y="271" width="132.5" height="25" rx="7.5" ry="7.5" fill="#b1ddf0" stroke="none" pointer-events="all" class="box-color" />
			<g transform="translate(-0.5 -0.5)">
				<switch>
					<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
						<div
							xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 103px; height: 1px; padding-top: 284px; margin-left: 19px;">
							<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
								<div style="display: inline-block; font-size: 14px; font-family: Roboto; color: #10739E; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
									<p xmlns="http://www.w3.org/1999/xhtml" style="line-height: 104% ; font-size: 12px">
										<font color="#000099">Disable Price Modification</font><br />
									</p>
								</div>
							</div>
						</div>
					</foreignObject>
					<text x="232" y="407" fill="#10739E" font-family="Roboto" font-size="14px" text-anchor="middle">Delivery Schedules</text>
				</switch>
			</g>
			<ellipse cx="136.51" cy="282.5" rx="12.5" ry="12.5" fill="#10739e" stroke="#ffffff" stroke-width="3" pointer-events="all" class="ellipse-color"/>
			<image x="129.01" y="276.59" width="15" height="11.53" class="item-checkbox" xlink:href="/site_media/images/tick.png" preserveAspectRatio="none"/>
		</g>


		<!--NEGATIVE INVENTORY-->
		<g id="negative_invertory" class="negative_invertory custom-class configurable-checkbox" data-value="1" role="button" >
			<rect x="193.25" y="120" width="128.75" height="25" rx="7.5" ry="7.5" fill="#b1ddf0" stroke="none" pointer-events="all" class="box-color"/>
			<g transform="translate(-0.5 -0.5)">
				<switch>
					<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">						<div
							xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 105px; height: 1px; padding-top: 133px; margin-left: 204px;">
							<div style="box-sizing: border-box; font-size: 0; text-align: left; ">
								<div style="display: inline-block; font-size: 14px; font-family: Roboto; color: #FFFFFF; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
									<p style="line-height: 90% ; font-size: 12px">
										<span style="color: rgb(0 , 0 , 153) ; text-align: right">Negative Inventory</span><br />
									</p>
								</div>
							</div>
						</div>
					</foreignObject>
					<text x="715" y="135" fill="#FFFFFF" font-family="Roboto" font-size="14px">Negative Inventory</text>
				</switch>
			</g>
			<ellipse cx="323.51" cy="132.79" rx="12.5" ry="12.5" fill="#10739e" stroke="#ffffff" stroke-width="3" pointer-events="all" class="ellipse-color"/>
			<image x="316.01" y="127.18" width="15" height="11.53" class="item-checkbox" xlink:href="/site_media/images/tick.png" preserveAspectRatio="none"/>
		</g>

		<!--MULTIPLE UNITS-->
		<g id="multiple_units" class="multiple_units custom-class configurable-checkbox" data-value="1" role="button" >
			<rect x="130" y="154.3" width="193.51" height="25" rx="7.5" ry="7.5" fill="#b1ddf0" stroke="none" pointer-events="all" class="box-color"/>
			<g transform="translate(-0.5 -0.5)">
				<switch>
					<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
						<div
							xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 183px; height: 1px; padding-top: 168px; margin-left: 140px;">
							<div style="box-sizing: border-box; font-size: 0; text-align: left; ">
								<div style="display: inline-block; font-size: 14px; font-family: Roboto; color: #FFFFFF; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
									<p style="line-height: 90% ; font-size: 12px">
										<span style="color: rgb(0 , 0 , 153) ; text-align: right">Multiple Units of Measurement</span><br />
									</p>
								</div>
							</div>
						</div>
					</foreignObject>
					<text x="683" y="170" fill="#FFFFFF" font-family="Roboto" font-size="14px">Multiple Units of Measurement</text>
				</switch>
			</g>
			<ellipse cx="323.51" cy="166.8" rx="12.5" ry="12.5" fill="#10739e" stroke="#ffffff" stroke-width="3" pointer-events="all" class="ellipse-color"/>
			<image x="316.01" y="160.89" width="15" height="11.53" class="item-checkbox" xlink:href="/site_media/images/tick.png" preserveAspectRatio="none"/>
		</g>

		<!--GATE INWARD NUMBER-->
		<!--<g class="gate_inward_number custom-class configurable-checkbox" data-value="1" role="button" >-->
		<g>
			<rect x="13.2" y="115" width="118.8" height="22" rx="6.6" ry="6.6" fill="#b1ddf0" stroke="none" pointer-events="all"/>
			<g transform="translate(-0.5 -0.5)">
				<switch>
					<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
						<div
							xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 155px; height: 1px; padding-top: 126px; margin-left: 31px;">
							<div style="box-sizing: border-box; font-size: 0; text-align: left; ">
								<div style="display: inline-block; font-size: 14px; font-family: Roboto; color: #FFFFFF; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
									<font color="#004195" class="text-color">
										<span xmlns="http://www.w3.org/1999/xhtml" style="font-size: 12px ; white-space: nowrap">Gate Inward No</span>
									</font>
								</div>
							</div>
						</div>
					</foreignObject>
					<text xmlns="http://www.w3.org/2000/svg" x="45" y="132" fill="#FFFFFF" font-family="Roboto" font-size="14px">Gate Inward No</text>
				</switch>
			</g>
			<!--<ellipse cx="32.5" cy="135" rx="15" ry="15" fill="#10739e" stroke="#ffffff" stroke-width="3" pointer-events="all" class="ellipse-color" />
			<image x="23" y="128.2" width="19" height="14.6" class="item-checkbox" xlink:href="/site_media/images/tick.png" preserveAspectRatio="none" />-->
		</g>
		
		<path d="M 34 153 L 44 138 L 120 138 L 110 153 Z" fill="url(#mx-gradient-10739e-1-9673a6-1-e-0)" stroke="none" pointer-events="all"/>
		<g id="mandate_gate_inward" class="mandate_gate_inward custom-class configurable-checkbox configurable-checkbox-gate-inward configurable-checkbox-small item-disabled" data-value="1" role="button" transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 143px; margin-left: 55px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: left; ">
							<div style="display: inline-block; font-size: 17px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
								<font style="font-size: 10px" color="#e1d5e7">Mandatory</font>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="55" y="148" fill="#000000" font-family="Roboto" font-size="17px">Mandatory</text>
			</switch>
			<ellipse class="ellipse-color" cx="38.5" cy="145.5" rx="7.5" ry="7.5" fill="#10739e" stroke="#ffffff" pointer-events="all"/>
			<image class="item-checkbox" x="32.5" y="141.5" width="11" height="8.45" xlink:href="/site_media/images/tick.png" preserveAspectRatio="none"/>
		</g>
		

		<path d="M 34 169.3 L 44 154.3 L 129.8 154.3 L 119.8 169.3 Z" fill="url(#mx-gradient-10739e-1-9673a6-1-e-0)" stroke="none" pointer-events="all"/>
		<g id="unique_gate_inward" class="unique_gate_inward custom-class configurable-checkbox configurable-checkbox-gate-inward configurable-checkbox-small item-disabled" data-value="2" role="button" transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 159px; margin-left: 55px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: left; ">
							<div style="display: inline-block; font-size: 17px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
								<font style="font-size: 10px" color="#e1d5e7">Unique for FY</font>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="55" y="148" fill="#000000" font-family="Roboto" font-size="17px">Unique for FY</text>
			</switch>
			<ellipse class="ellipse-color" cx="38.5" cy="161.5" rx="7.5" ry="7.5" fill="#10739e" stroke="#ffffff" pointer-events="all"/>
			<image class="item-checkbox" x="32.5" y="157.5" width="11" height="8.45" xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="/site_media/images/tick.png" preserveAspectRatio="none"/>
		</g>
		

		<path d="M 34.2 185.6 L 44.2 170.6 L 110 170.6 L 100 185.6 Z" fill="url(#mx-gradient-10739e-1-9673a6-1-e-0)" stroke="none" pointer-events="all"/>
		<g id="automate_gate_inward" class="automate_gate_inward custom-class configurable-checkbox configurable-checkbox-gate-inward configurable-checkbox-small item-disabled" data-value="4" role="button" transform="translate(-0.5 -0.5)">
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 175px; margin-left: 55px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: left; ">
							<div style="display: inline-block; font-size: 17px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
								<font style="font-size: 10px" color="#e1d5e7">Automate</font>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="55" y="148" fill="#000000" font-family="Roboto" font-size="17px">Automate</text>
			</switch>
			<ellipse class="ellipse-color" cx="38.5" cy="177.8" rx="7.5" ry="7.5" fill="#10739e" stroke="#ffffff" pointer-events="all"/>
			<image class="item-checkbox" x="32.5" y="173.8" width="11" height="8.45" xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="/site_media/images/tick.png" preserveAspectRatio="none"/>
		</g>
		

		
		<g id="editable_gate_inward" class="editable_gate_inward custom-class configurable-checkbox configurable-checkbox-gate-inward configurable-checkbox-small item-disabled hide" data-value="8" role="button" transform="translate(-0.5 -0.5)">
			<path d="M 34.2 201.95 L 44.2 186.95 L 110 186.95 L 100 201.95 Z" fill="url(#mx-gradient-10739e-1-9673a6-1-e-0)" stroke="none" pointer-events="all"/>
			<switch>
				<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
					<div
						xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 191px; margin-left: 55px;">
						<div style="box-sizing: border-box; font-size: 0; text-align: left; ">
							<div style="display: inline-block; font-size: 17px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
								<font style="font-size: 10px" color="#e1d5e7">Editable</font>
							</div>
						</div>
					</div>
				</foreignObject>
				<text x="55" y="197" fill="#000000" font-family="Roboto" font-size="17px">Editable</text>
			</switch>
			<ellipse class="ellipse-color" cx="38.5" cy="194.15" rx="7.5" ry="7.5" fill="#10739e" stroke="#ffffff" pointer-events="all"/>
			<image class="item-checkbox" x="32.5" y="190.15" width="11" height="8.45" xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="/site_media/images/tick.png" preserveAspectRatio="none"/>

		</g>

		<!--INDENT-->
		<g id="indent-config" class="indent-config custom-class configurable-checkbox svg-flag-value" data-value="1" role="button"  >
			<rect x="15" y="355.15" width="140" height="35" rx="5.25" ry="5.25" fill="#ffffff" stroke="#ffffff" pointer-events="all" class="box-color"/>
			<g transform="translate(-0.5 -0.5)">
				<switch>
					<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
						<div
							xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 374px; margin-left: 22px;">
							<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
								<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
									<font size="1" color="#1247b9" class="text-color">
										<span style="font-size: 15px; line-height: 15px;">Purchase <br />Indents</span>
									</font>
								</div>
							</div>
						</div>
					</foreignObject>
					<text x="290" y="176" fill="#000000" font-family="Roboto" font-size="14px" text-anchor="middle">Indents</text>
				</switch>
			</g>
			<ellipse cx="150" cy="372.44" rx="15" ry="15" fill="#10739e" stroke="#ffffff" stroke-width="3" pointer-events="all" class="ellipse-color"/>
			<image x="140" y="364.64" width="19" height="14.6" class="item-checkbox" xlink:href="/site_media/images/tick.png" preserveAspectRatio="none" />
		</g>

		<!--EXPENSE CONFIGRATION -->
		<g id="expense_configration_settings" class="expense_configration_settings custom-class" data-toggle="modal" data-target="#exp_configration_modal" role="button" >
			<rect x="145" y="602.4" width="129" height="22" rx="4.8" ry="4.8" fill="#ffffff" stroke="#ffffff" pointer-events="all"/>
			<g transform="translate(-0.5 -0.5)">
				<switch>
					<foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
						<div
							xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 115px; height: 1px; padding-top: 614px; margin-left: 146px;">
							<div style="box-sizing: border-box; font-size: 0; text-align: center; ">
								<div style="display: inline-block; font-size: 12px; font-family: Roboto; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
									<span style="font-size: 15px">
										<font color="#c23603">Expenses</font>
									</span>
								</div>
							</div>
						</div>
					</foreignObject>
					<text x="204" y="627" fill="#000000" font-family="Roboto" font-size="12px" text-anchor="middle">Expenses</text>
				</switch>
			</g>
			<path d="M 259.97 628.34 C 262.73 628.34 264.86 626.12 264.86 623.43 C 264.86 621 263 618.57 259.88 618.57 C 257.65 618.57 255.11 620.4 255.11 623.46 C 255.11 626.01 257.16 628.34 259.97 628.34 Z M 258.66 633.4 C 257.91 633.27 257.17 633.1 256.47 632.82 L 256.51 631.17 C 255.99 630.97 255.47 630.68 254.96 630.26 L 253.5 631.12 C 252.98 630.6 252.47 630.08 251.96 629.44 L 252.9 628.03 C 252.6 627.64 252.26 627.04 252.06 626.4 L 250.41 626.38 C 250.24 625.72 250.09 624.99 250 624.11 L 251.54 623.51 C 251.5 622.95 251.59 622.17 251.7 621.62 L 250.37 620.7 C 250.59 620 250.85 619.3 251.23 618.62 L 252.85 618.93 C 253.13 618.41 253.54 617.92 254.01 617.45 L 253.36 615.97 C 253.9 615.46 254.54 615.02 255.23 614.62 L 256.44 615.74 C 257.03 615.45 257.7 615.26 258.2 615.2 L 258.47 613.54 C 259.24 613.41 260.06 613.4 260.75 613.47 L 261.13 615.03 C 261.73 615.13 262.34 615.26 262.96 615.49 L 264.06 614.3 C 264.75 614.61 265.41 615.01 266 615.45 L 265.51 616.98 C 266 617.46 266.48 617.93 266.74 618.38 L 268.35 617.97 C 268.78 618.62 269.11 619.29 269.37 619.98 L 268.11 621 C 268.29 621.62 268.39 622.32 268.42 622.87 L 270 623.36 C 269.97 624.12 269.91 625.06 269.75 625.6 L 268.1 625.8 C 267.94 626.44 267.7 627 267.42 627.52 L 268.46 628.81 C 268.14 629.33 267.61 630.01 267.04 630.57 L 265.57 629.82 C 265.2 630.19 264.66 630.58 264.06 630.85 L 264.21 632.53 C 263.58 632.84 262.87 633.08 262.09 633.27 L 261.26 631.82 C 260.75 631.92 259.95 631.98 259.39 631.89 Z" fill-opacity="0.8" fill="#000000" stroke="none" transform="translate(2,-10)" opacity="0.25"/>
			<path d="M 259.97 628.34 C 262.73 628.34 264.86 626.12 264.86 623.43 C 264.86 621 263 618.57 259.88 618.57 C 257.65 618.57 255.11 620.4 255.11 623.46 C 255.11 626.01 257.16 628.34 259.97 628.34 Z M 258.66 633.4 C 257.91 633.27 257.17 633.1 256.47 632.82 L 256.51 631.17 C 255.99 630.97 255.47 630.68 254.96 630.26 L 253.5 631.12 C 252.98 630.6 252.47 630.08 251.96 629.44 L 252.9 628.03 C 252.6 627.64 252.26 627.04 252.06 626.4 L 250.41 626.38 C 250.24 625.72 250.09 624.99 250 624.11 L 251.54 623.51 C 251.5 622.95 251.59 622.17 251.7 621.62 L 250.37 620.7 C 250.59 620 250.85 619.3 251.23 618.62 L 252.85 618.93 C 253.13 618.41 253.54 617.92 254.01 617.45 L 253.36 615.97 C 253.9 615.46 254.54 615.02 255.23 614.62 L 256.44 615.74 C 257.03 615.45 257.7 615.26 258.2 615.2 L 258.47 613.54 C 259.24 613.41 260.06 613.4 260.75 613.47 L 261.13 615.03 C 261.73 615.13 262.34 615.26 262.96 615.49 L 264.06 614.3 C 264.75 614.61 265.41 615.01 266 615.45 L 265.51 616.98 C 266 617.46 266.48 617.93 266.74 618.38 L 268.35 617.97 C 268.78 618.62 269.11 619.29 269.37 619.98 L 268.11 621 C 268.29 621.62 268.39 622.32 268.42 622.87 L 270 623.36 C 269.97 624.12 269.91 625.06 269.75 625.6 L 268.1 625.8 C 267.94 626.44 267.7 627 267.42 627.52 L 268.46 628.81 C 268.14 629.33 267.61 630.01 267.04 630.57 L 265.57 629.82 C 265.2 630.19 264.66 630.58 264.06 630.85 L 264.21 632.53 C 263.58 632.84 262.87 633.08 262.09 633.27 L 261.26 631.82 C 260.75 631.92 259.95 631.98 259.39 631.89 Z" fill-opacity="0.8" fill="#c23603" stroke="none" transform="translate(2,-10)" pointer-events="all"/>
		</g>

		<!--PURCHASE CONFIGRATION -->
		<g id="purchase_configration_settings" class="purchase_configration_settings custom-class" data-toggle="modal" data-target="#purchase_configration_modal" role="button" >
			<ellipse cx="426" cy="283" rx="8" ry="8" fill="#7ea6e0" stroke="none" pointer-events="all"/>
			<path d="M 425.96 290.41 C 430.09 290.41 433.3 287.08 433.3 283.04 C 433.3 279.4 430.5 275.75 425.82 275.75 C 422.47 275.75 418.66 278.5 418.66 283.09 C 418.66 286.92 421.75 290.41 425.96 290.41 Z M 423.99 298 C 422.87 297.8 421.76 297.56 420.71 297.13 L 420.77 294.66 C 419.99 294.36 419.21 293.92 418.43 293.29 L 416.25 294.58 C 415.47 293.79 414.71 293.02 413.93 292.06 L 415.36 289.95 C 414.9 289.36 414.38 288.46 414.1 287.49 L 411.61 287.48 C 411.35 286.48 411.13 285.39 411 284.06 L 413.3 283.16 C 413.26 282.33 413.38 281.16 413.54 280.33 L 411.55 278.96 C 411.89 277.89 412.28 276.84 412.84 275.83 L 415.28 276.29 C 415.69 275.51 416.3 274.78 417.02 274.07 L 416.04 271.85 C 416.85 271.09 417.81 270.43 418.84 269.82 L 420.66 271.51 C 421.54 271.08 422.55 270.79 423.3 270.7 L 423.7 268.21 C 424.86 268.01 426.09 268 427.13 268.11 L 427.7 270.44 C 428.6 270.59 429.51 270.79 430.43 271.14 L 432.09 269.35 C 433.12 269.81 434.12 270.42 435 271.08 L 434.27 273.37 C 435 274.09 435.72 274.79 436.11 275.46 L 438.53 274.85 C 439.17 275.83 439.67 276.84 440.05 277.86 L 438.16 279.39 C 438.43 280.33 438.58 281.38 438.64 282.2 L 441 282.93 C 440.95 284.07 440.86 285.49 440.63 286.31 L 438.16 286.61 C 437.92 287.56 437.56 288.4 437.14 289.18 L 438.69 291.12 C 438.21 291.89 437.41 292.92 436.57 293.75 L 434.35 292.63 C 433.79 293.18 433 293.77 432.09 294.17 L 432.32 296.69 C 431.37 297.16 430.3 297.52 429.14 297.8 L 427.88 295.63 C 427.12 295.78 425.92 295.86 425.08 295.74 Z" fill="#000000" stroke="none" transform="translate(2,3)" opacity="0.25"/>
			<path d="M 425.96 290.41 C 430.09 290.41 433.3 287.08 433.3 283.04 C 433.3 279.4 430.5 275.75 425.82 275.75 C 422.47 275.75 418.66 278.5 418.66 283.09 C 418.66 286.92 421.75 290.41 425.96 290.41 Z M 423.99 298 C 422.87 297.8 421.76 297.56 420.71 297.13 L 420.77 294.66 C 419.99 294.36 419.21 293.92 418.43 293.29 L 416.25 294.58 C 415.47 293.79 414.71 293.02 413.93 292.06 L 415.36 289.95 C 414.9 289.36 414.38 288.46 414.1 287.49 L 411.61 287.48 C 411.35 286.48 411.13 285.39 411 284.06 L 413.3 283.16 C 413.26 282.33 413.38 281.16 413.54 280.33 L 411.55 278.96 C 411.89 277.89 412.28 276.84 412.84 275.83 L 415.28 276.29 C 415.69 275.51 416.3 274.78 417.02 274.07 L 416.04 271.85 C 416.85 271.09 417.81 270.43 418.84 269.82 L 420.66 271.51 C 421.54 271.08 422.55 270.79 423.3 270.7 L 423.7 268.21 C 424.86 268.01 426.09 268 427.13 268.11 L 427.7 270.44 C 428.6 270.59 429.51 270.79 430.43 271.14 L 432.09 269.35 C 433.12 269.81 434.12 270.42 435 271.08 L 434.27 273.37 C 435 274.09 435.72 274.79 436.11 275.46 L 438.53 274.85 C 439.17 275.83 439.67 276.84 440.05 277.86 L 438.16 279.39 C 438.43 280.33 438.58 281.38 438.64 282.2 L 441 282.93 C 440.95 284.07 440.86 285.49 440.63 286.31 L 438.16 286.61 C 437.92 287.56 437.56 288.4 437.14 289.18 L 438.69 291.12 C 438.21 291.89 437.41 292.92 436.57 293.75 L 434.35 292.63 C 433.79 293.18 433 293.77 432.09 294.17 L 432.32 296.69 C 431.37 297.16 430.3 297.52 429.14 297.8 L 427.88 295.63 C 427.12 295.78 425.92 295.86 425.08 295.74 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
		</g>
	</g>
	<switch>
		<g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
		<a transform="translate(0,-5)" xlink:href="https://desk.draw.io/support/solutions/articles/16000042487" target="_blank">
			<text text-anchor="middle" font-size="10px" x="50%" y="100%">Viewer does not support full SVG 1.1</text>
		</a>
	</switch>
</svg>