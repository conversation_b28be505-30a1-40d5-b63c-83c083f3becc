
$(window).load(function(){
	populateMakes();
})

$(function () {
    $('#add_make').click(function () {
        generatePartNo();
    });
    addMakes();
    partNumberEditFunctionality();
});

function generateMakeandPart(makeValue, partValue, spqvalue){
	refreshSessionPerNActions(10);
    if (trim($("#id_material_make-__prefix__-make").val())== "")  {
            return;
    } else{
		$(".li-tagit-display").removeClass('flash');
		var currentFieldName = $("#id_material_make-__prefix__-make").val();
		$("#ul-tagit-display").find('.li-tagit-display:visible').each(function(){
			if(currentFieldName.toLowerCase().trim() == $(this).find('.label-tagit-display').text().toLowerCase().trim()){
				$('#id_material_make-__prefix__-make').val('');
				$(this).addClass('flash');
				return;
			}
		});
		if (trim($("#id_material_make-__prefix__-make").val())!= "") {
			generateFormsetForm('material_make');
			var index = parseInt(parseInt($('#id_material_make-TOTAL_FORMS').val()) - 1);
			$('#id_material_make-' + index + '-make_label').html($('#id_material_make-__prefix__-make').val().toUpperCase());
			$('#id_material_make-' + index + '-part_no').val(partValue.toUpperCase());
			$('#id_material_make-' + index + '-make').val(makeValue.toUpperCase());
			$('#id_material_make-' + index + '-standard_packing_quantity').val(spqvalue);
			$('#id_material_make-' + index + '-make_id').val($('#id_material_make-__prefix__-make_id').val());
			$('#id_material_make-' + index + '-make_label').attr('data-partno', partValue.toUpperCase()).attr('data-make', makeValue.toUpperCase()).attr('data-spq', spqvalue);
			$('#id_material_make-' + index + '-make_label').closest("li").find(".current_make_id").val($('#id_material_make-__prefix__-make_id').val());
			$('#id_material_make-__prefix__-make').val("");
			create_delete_make_button();
			partNumberEditFunctionality();
		}
    }
}

function create_delete_make_button(){
 $(".delete_tag").click(function(){

 $(this).closest('li').find("input[type='checkbox']").attr("checked", true);
 current_make = $(this).closest('li');

       swal({
                                        title: "Are you sure?",
                                        text: 'Do you want to delete this make?',
                                        type: "warning",
                                        showCancelButton: true,
                                        confirmButtonText: "Cancel",
                                        cancelButtonText: "Yes Delete It",
                                        backdrop: false,
                                        closeOnConfirm: true,
                                        focusCancel: true
                                    }, function (isConfirm) {
                                    if (isConfirm) {
                                    swal.close();
                                    }
                                    else {

                                      current_make.hide();
                                    }
                                    }


                  );



    });
}

function populateMakes(){
	var make_list = [];
	$.ajax({
		url: "/erp/json/populate_makes/",
		type: "post",
		datatype:"json",
		data: "",
		success: function(response){
		    for (i = 0; i <= response.length - 1 ; i++) {
		        make_list.push({value: " " + response[i][0] ,
                                    label: "" + response[i][1] + ""});

		    }
		    $("#id_material_make-__prefix__-make").keypress(function(e){
				if (e.which == 13) {
					$('#add_make').click();
				}
			});
		},
        error: function (xhr, errmsg, err) {
            console.log(xhr.status + ": " + xhr.responseText);
        }
	});
    $("#id_material_make-__prefix__-make").val("");
    $("#id_material_make-__prefix__-make").autocomplete({
        source: make_list,
        minLength: 0,
        focus: function (event, ui) {
            event.preventDefault();
        },
        select: function (event, ui) {
            event.preventDefault();
            var isDuplicate = false;
            $( "#ul-tagit-display li:visible" ).each(function( index ) {
				if((ui.item.value).trim() == ($( this ).find("input.current_make_id").val()).trim()){
					$('#id_material_make-__prefix__-make').val('');
					$(this).addClass('flash');
					isDuplicate = true;
					return false;
				}
			});
			setTimeout(function(){
				$(".flash").removeClass("flash");
			},700);
			if(!isDuplicate) {
				$("#id_material_make-__prefix__-make").val(ui.item.label);
	            $("#id_material_make-__prefix__-make_id").val(ui.item.value);
	            generatePartNo();
	        }
        }
    }).on('focus', function() { $(this).keydown(); });
}

function generatePartNo(){
    var makeValue = $("#id_material_make-__prefix__-make").val();
    if(makeValue.trim() != "") {
        $("#make_model .modal-title").text("" +makeValue.toUpperCase() +" - Details");
        $("#id_make_title").val(makeValue.toUpperCase())
        $("#make_model").modal('show');
    }
     $("#id_manufacture_value").val("");
     $("#id_packing_value ").val("");
}

function addMakes() {
    $("#make_model_save").click(function(){
        setTimeout(function(){
            if($("#moq_validation_modal").is(":visible")) {
                return false;
            }
            var manufactureValue = $("#id_manufacture_value").val();
            var standardPackingValue = ""
            if ($("#id_packing_value ").val() != "None" && $("#id_packing_value ").val() != 0){
                standardPackingValue = $("#id_packing_value").val();
            }
            if ($("#id_manufacture_value").val() === false) return false;
            var makeValue = $("#id_make_title").val();
            var makeText = makeValue;
            if (manufactureValue.trim() != "") {
                makeText += " - "+manufactureValue;
            }
            if (standardPackingValue.trim() != "") {
                makeText += " - "+standardPackingValue;
            }

            if($("#ul-tagit-display").find(`.label-tagit-display[data-make='${makeValue}']`).length == 0) {
                $("#id_material_make-__prefix__-make").text(makeValue+" - " +manufactureValue+" - " +standardPackingValue);
                $("#id_material_make-__prefix__-make").val(makeText);
                generateMakeandPart(makeValue, manufactureValue,standardPackingValue);
                create_delete_make_button();
            }
            else {
                var current = $("#ul-tagit-display").find(`.label-tagit-display[data-make='${makeValue}']`).closest("li");

                 $(current).find('.label-tagit-display').html(makeText.toUpperCase());
                 $(current).find('.label-tagit-display').attr("data-partno", manufactureValue.toUpperCase());
                 $(current).find('.label-tagit-display').attr("data-spq", standardPackingValue.toUpperCase());
                 $(current).find("input[id*='part_no']").val(manufactureValue.toUpperCase());
                 $(current).find("input[id*='standard_packing_quantity']").val(standardPackingValue);
                $('#id_material_make-__prefix__-make').val("");
            }
            $("#make_model").modal('hide');
        },250);
    });
}

function editPartNo(editId){
	var makeValue = $("#"+editId).attr('data-make');
	var partValue = $("#"+editId).attr('data-partno');
    var makeId = $("#"+editId).closest("li").find(".current_make_id").val();
	var spqvalue = "";
	var makecount = $("#"+editId).attr('id').split("-");
	if ($("#"+editId).attr('data-spq') != "None" && $("#"+editId).attr('data-spq') != 0){
	    spqvalue = $("#"+editId).attr('data-spq');
	}
	makecount = makecount[1];
     $("#make_model .modal-title").text("" +makeValue.toUpperCase() +" - Details");
     $("#id_make_title").val(makeValue.toUpperCase())
     $("#id_manufacture_value").val(partValue);
     $("#id_packing_value").val(spqvalue).attr("onchange", `validateMoqFactor(this, ${makeId})`);
     $("#make_model").modal('show');

	create_delete_make_button();
}


function partNumberEditFunctionality(){
	$(".label-tagit-display").click(function(){
		editPartNo($(this).attr("id"));

	});
}