{% extends "masters/sidebar.html" %}
{% block party %}
<style>
	li.supplier_side_menu a{
	outline: none;
	background-color: #e6983c !important;
	}

	.left-form-group .custom-error-message,
	.tr-file-upload .custom-error-message {
		position: relative;
		display: block;
	}
	
	.left-form-group .row div {
		margin-bottom: 15px;
	}
	
	.edit_fixed td{
		background: #ccffcc;
	}
	
	.editable-textbox {
		background: transparent;
		border: none;
		border-bottom: 2px solid #004195;
		color: #004195;
		outline: none;
		padding: 1px;
		width: 100%;
	}
	
	input[disabled] {
		cursor: not-allowed;
		opacity: 0.4;
	}
		
	td.party_edited {
		background:#efacac;
	}
	
	.edit_fixed td {
		padding: 4px 8px !important;
	}
	
	.esc-info-message {
		color: #a94442;
		background-color: #f2dede;
		border-color: #ebccd1;
		position: fixed;
		right: 60px;
		bottom: 59px;
		font-size: 25px;
		padding: 25px;
		border-radius: 5px;
		z-index: 10;
	}
	
	#importsupplier .custom-table-large td {
		font-size: 11px !important;
	}
	
	.party_type.edit-current  {
		font-size: 12px !important;
	}

	.tr-file-upload .bootstrap-filestyle {
		width:  600px;
		float: left;
	}

	.outer {
	  	max-width:1000px;
	  	max-height:300px;
	  	height: 100vh;
	  	overflow: hidden;
	  	display: flex;
	  	flex-direction: column;
	}
   	.inner {
  		flex: 1;
  		overflow-y: scroll;
 	}

 	.create_party {
		border-radius: 50px 0 0 50px !important; 
	    border-right: 1px solid #ccc;
	}

	.create_add_container {
		margin-right: 15px;
	}

	.create_add_container a.btn-new-item {
		box-shadow: -4px 4px 5px 0 rgba(60,64,67,0.302), 0 1px 3px 1px rgba(60,64,67,0.149);
	}

	.import_csv.btn-new-item  {
		border-radius: 0 50px 50px 0;
	}

	.btn-new-item-label {
	    color: #004195;
	    padding: 6px !important;
	}

	.create_party:hover,
	.import_csv:hover {
		background: rgba(32, 155, 225,0.1);
		box-shadow: -4px 4px 5px 0 rgba(60,64,67,0.302), 0 1px 3px 1px rgba(60,64,67,0.149) !important;
	}

	.show_save_button {
		display: none !important;
	}

</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/party_list.js?v={{ current_version }}_1"></script>

<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">Party</span>
	</div>
	<input type="hidden" value="0" id="is_edit_enabled">
	<div class="page-heading_new">
		<input type="hidden" class="agent" id="agent" name="agent" value="{{ agent }}" />
		{% if access_level.edit %}
			<div class="create_add_container">
				<a id="a-import-party" class="btn btn-new-item pull-right import_csv" onclick="javascript:showImportmaterial();" data-tooltip="tooltip" title="Import Bulk Party List">
					<i class="fa fa-upload" aria-hidden="true"></i>
					<span class="btn-new-upload-label"></span>
				</a>
				<a id="a-add-party" href="/erp/masters/party/" class="btn btn-new-item pull-right create_party" data-tooltip="tooltip" title="Add New Party">
					<i class="fa fa-plus" aria-hidden="true"></i>
					<span class="btn-new-item-label"></span>
				</a>
				<script type="text/javascript">$("#is_edit_enabled").val(1);</script>
			</div>
		{% else %}
			<div class="disabled-upload-container" data-tooltip="tooltip" data-placement="bottom" title="You do not have adequate permission to create/ upload in Profile Module. Please contact the administrator." style="margin-right: 15px;">
				<a class="btn btn-new-item pull-right import_csv disabled">
					<i class="fa fa-upload" aria-hidden="true"></i>
					<span class="btn-new-upload-label"></span>
				</a>
				<a class="btn btn-new-item pull-right create_party disabled">
					<i class="fa fa-plus" aria-hidden="true"></i>
					<span class="btn-new-item-label"></span>
				</a>
			</div>

		{% endif %}
		<a href="/erp/masters/catalogues/" id="a-back-party" class="btn btn-add-new pull-right view_material hide" data-tooltip="tooltip" title="Back"><i class="fa fa-arrow-left" aria-hidden="true"></i></a>
	</div>
	<div class="container-fluid">
		<div class="row">
			<div class="col-lg-12">
				<div class="content_bg">
					<div class="col-sm-12 table-with-upload-button">
						<div class='esc-info-message' style="display: none;">
							<span>Press esc to cancel the edit</span>
						</div>
						<div class="csv_export_button">
							<a role="button" id="a-export-party" class="btn btn-add-new pull-right export_csv export_party_details" onclick="GeneralExportTableToCSV.apply(this, [$('#dettable'), 'Party_Details.csv']);" data-tooltip="tooltip" title="Download Party List as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
							
						</div>
						<table class="table table-bordered custom-table table-striped" id="dettable" style="width: 100%;">
							<thead>
								<tr>
									<th style="width: 70px;min-width: 70px;">Code</th>
									<th>Name</th>
									<th hidden="hidden">Address1</th>
									<th hidden="hidden">City</th>
									<th hidden="hidden">State</th>
									<th hidden="hidden">Country</th>
									<th style="width: 100px;">Contact Person</th>
									<th style="width: 100px;">Phone No</th>
									<th style="width: 100px;">Email</th>
									<th style="width: 100px;">Fax</th>
									<th hidden="hidden" class='exclude_export'>Supplier ID</th>
									<th hidden="hidden" class="exclude_export">Party Type</th>
									<th style="width: 80px;">Party Type</th>
									<th style="width: 80px;">Supplier Cr. Period</th>
									<th style="width: 80px;">Customer Cr. Period</th>
									<th hidden="hidden" class="exclude_export">Currency</th>
									<th hidden="hidden">GST Category</th>
									<th hidden="hidden">Port</th>
									<th hidden="hidden">GSTIN</th>
									<th hidden="hidden">PAN</th>
								</tr>
							</thead>
							<tbody></tbody>
						</table>
					</div>
					<div class="clearfix"></div>
				</div>
			</div>
		</div>
	</div>
</div>

<div id="modal_importsupplier" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg" style="width: 94%">
		<!-- Modal content-->
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Import Party Details</h4>
			</div>
			<div class="modal-body">
				<div id="importsupplier">
					<table id="importtable" class="table table-bordered table-striped custom-table custom-table-large"
					       style="table-layout: fixed;">
						<thead>
						<tr align="center">
							<th colspan="2">Party Info</th>
							<th colspan="5">Address</th>
							<th colspan="4">Contact Info</th>
							<th colspan="2">Payable</th>
							<th colspan="2">Receivable</th>
							<th rowspan="1">Opening on</th>
							<th colspan="4">Registration Details</th>
						</tr>
						<tr>
							<th>code</th>
							<th>name *</th>
							<th>address_1</th>
							<th>city</th>
							<th>state *</th>
							<th>country *</th>
							<th>pin_code</th>
							<th>contact</th>
							<th>phone</th>
							<th>email</th>
							<th>fax_no</th>
							<th>credit_days</th>
							<th>opening</th>
							<th>credit_days</th>
							<th>opening</th>
							<th>as_on</th>
							<th>gst_category</th>
							<th>port</th>
							<th>gstin **</th>
							<th>pan</th>
						</tr>
						</thead>
						<tr>
							<td>SSCH_0001</td>
							<td>Schnell Energy Equipments (P) Ltd.</td>
							<td>Plot 6/9, Ranganayaki Nagar, PNKPalayam</td>
							<td>Coimbatore</td>
							<td>Tamil Nadu</td>
							<td>IN</td>
							<td>641019</td>
							<td>Karthic	k</td>
							<td>+91 98765 43210</td>
							<td><EMAIL></td>
							<td>0422 2697077</td>
							<td align="right">30</td>
							<td align="right">10030.00</td>
							<td align="right">60</td>
							<td align="right">652033.00</td>
							<td align="right">2018-04-01</td>
							<td>1</td>
							<td> </td>
							<td>GSTIN001234567</td>
							<td>PAN001234567</td>
						</tr>
						<tr>
							<td align="center" style="font-size:10px">Alpha-Numeric<br>(max 10 chars)</td>
							<td align="center" style="font-size:10px">Text</td>
							<td align="center" style="font-size:10px">Text</td>
							<td align="center" style="font-size:10px">Text</td>
							<td align="center" style="font-size:10px">Text</td>
							<td align="center" style="font-size:10px">Alpha-2 code<br />(max 2 chars)<br /><a href="/site_media/docs/country_code.csv" >see country Alpha-2 code</a></td>
							<td align="center" style="font-size:10px">Number</td>
							<td align="center" style="font-size:10px">Text</td>
							<td align="center" style="font-size:10px">Phone No.</td>
							<td align="center" style="font-size:10px">Email</td>
							<td align="center" style="font-size:10px">Phone No.</td>
							<td align="center" style="font-size:10px">Number</td>
							<td align="center" style="font-size:10px">+ve Number for Payables<br>-ve Number for any Advances</td>
							<td align="center" style="font-size:10px">Number</td>
							<td align="center" style="font-size:10px">+ve Number for Receivables<br>-ve Number for any Advances</td>
							<td align="center" style="font-size:10px">Date<br>(yyyy-mm-dd)<br>Empty will be considered as current FY opening date</td>
							<td align="center" style="font-size:10px">
								<b>Number</b><br />
								<small>1 - Registered Business<br />
								2 - Registered Business - Composition<br />
								3 - Unregistered Business<br />
								4 - Consumer<br />
								5 - Overseas<br />
								6 - Special Economic Zone (SEZ)<br />
								7 - Deemed Export<br />
								8 - Tax Deductor<br />
								9 - SEZ Developer<br /></small>
							</td>
							<td align="center" style="font-size:10px">Text</td>
							<td align="center" style="font-size:10px">Text</td>
							<td align="center" style="font-size:10px">Text</td>
						</tr>
						<tr>
							<td colspan="17"><b>Please Select the File for Import Party Details</b></td>
							<td colspan="3" style="border: none;" class="text-right"><a href="/site_media/docs/party_sample.csv" >Download Sample</a> </td>
						</tr>
						<tr class="tr-file-upload">
							<td colspan="20">
								<input type="file" class="load-map filestyle" id="fileUpload" data-buttonBefore="true" accept=".csv" />
			                    <a href="#" class="btn btn-save" id="cmdUpload" value="Add" style="margin-left: 15px;">Upload</a>
			                </td>
						</tr>
						<tr class="hide">
							<td colspan="19">
								<hr/>
								<div id="dvCSV"></div>
							</td>
						</tr>
					</table>
					<b>* - mandatory <br />
					** - mandatory if GST_CATEGORY value is 1, 2, 6, 7, 8 or 9.</b>
				</div>
			</div>
		</div>

	</div>
</div>

<div id="import_party_status_modal" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Import Party status</h4>
			</div>
			<div class="modal-body">
				<div>
					<h4 id="import_message"></h4>
				</div>
				<br>
				<a role="button" class="pull-right"
				   onclick="GeneralExportTableToCSV.apply(this, [$('#party_failed_import_table'), 'export_failed_items.csv']);">
					Download failed items (as CSV)</a><br>
				<div style='overflow:auto' class="outer">
					<div style='overflow:auto' class="inner">
						{% include "admin/party_failure.html" %}
						<br>
					</div>
				</div>
				<br><br>
			</div>
		</div>
	</div>
</div>

<div id="other_party_list_modal" class="modal fade" role="dialog">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title party_name_title"></h4>
			</div>
			<div class="modal-body">
				<table id="table-other-contact-details" class="table table-bordered custom-table table-striped">
					<thead>
						<tr>
							<th>Contact Person</th>
							<th>Phone</th>
							<th>Email Address</th>
							<th>Fax</th>
						</tr>
					</thead>
					<tbody>
						
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>
{% include "masters/add_party_modal.html" %}
<div class="hide">
	<form id="party_edit_form" method='post' action='/erp/masters/party/'>
        <{% csrf_token %} 
        <input type='hidden' id="edit_party_id" name='party_id' value="" />
    </form>
</div>
<script>

$(document).ready(function(){
	SupplierChange();
	CustomerChange();
	loadparty();
	$('.nav-pills li').removeClass('active');
	$('#li_party').addClass('active');
});

function editPartyRow(partyId, openTarget="") {
	$("#edit_party_id").val(partyId);
	$("#party_edit_form").attr("target", openTarget).submit();
}

</script>
<!-- /#wrapper -->
{% endblock %}
