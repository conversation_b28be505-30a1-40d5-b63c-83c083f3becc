<div class="modal fade" id="modalPartyDetails" role="dialog">
    <div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" id="close_img" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Add Party</h4>
			</div>
			<div class="modal-body">
			{% include "masters/add_party_form.html" %}
			</div>
			<div class="modal-footer">
				<div class="material_txt">
					{% if access_level.edit %}
					<input type="submit" class="btn btn-save" value="Save" onclick="savePartyInit()" id="cmdPartySave" />
					{% endif %}
					<input type="submit" class="btn btn-cancel" value="Cancel" data-dismiss="modal" id="cmdPartyCancel" />
				</div>
			</div>
		</div>
    </div>
</div>

<script type="text/javascript">
    $(document).ready(function(){
        updateCountryValueInline();
        partyModalCloseEvent();
    });

	function savePartyInit() {
        $(".error-border").removeClass('error-border');
	    $(".custom-error-message").remove();

	    var ControlCollections = [
	        {
	            controltype: 'textbox',
	            controlid: 'name',
	            isrequired: true,
	            errormsg: 'Name is required.'
	        },
            {
                controltype: 'textbox',
                controlid: 'state',
                isrequired: true,
                errormsg: 'State is required.'
            },
	        {
	            controltype: 'textbox',
	            controlid: 'email',
	            isemail: true,
	            emailerrormsg: 'Invalid Email Address.'
	        }
		];
        var gstCategory = $("#id_gst_category-__prefix__-make_choices").val();
        if(["3", "4", "5"].indexOf(gstCategory) == -1){
            var control = {
                controltype: 'textbox',
                controlid: 'gstno',
                isrequired: true,
                errormsg: 'GSTIN is Required.'
            };
            ControlCollections[ControlCollections.length] = control;
        }
        var result = JSCustomValidator.JSvalidate(ControlCollections);
        if(result) {
		    saveParty();
	    }
	}

    function saveParty() {
        var config_flags = parseInt($("#duty_pass_reminder").is(":checked") ? 1 : 0) +
        parseInt($("#id_is_supplier").is(":checked") ? 2 : 0) + parseInt($("#id_is_customer").is(":checked") ? 4 : 0);
        partyDetails = {
                        code: $("#code").val(), 
                        name: $("#name").val(), 
                        address_1: $("#add1").val(),
                        address_2: $("#add2").val(), 
                        city: $("#city").val(),
                        state: $("#state").val(), 
                        config_flags:config_flags, 
                        payment_credit_days: $("#pay_cre_days").val(),
                        receipt_credit_days: $("#rec_cre_days").val(), 
                        currency: $("#currency option:selected").val(),
                        pin_code: $("#pincode").val(),
                        country_code: $("#id_country-__prefix__-make_choices").val(),
                        category_id: $("#id_gst_category-__prefix__-make_choices").val(),
                        port: $("#id_party-port").val()
                    }
        $("#cmdPartySave").val('Processing...').addClass('btn-processing');

        partyContactDetailsObj = [{
			'sequence_id': 1,
			'name': $("#conperson").val(),
			'email': $("#email").val(),
			'phone_no': $("#phoneno").val(),
			'fax_no': $("#faxno").val(),
			'is_whatsapp': 0,
			'is_deleted': 0
		}];

        var gstin = {label_id: 1, details: $("#gstno").val().trim(), label: "GSTIN", is_deleted: 0};
        var pan = {label_id: 2, details: $("#panno").val().trim(), label: "PAN", is_deleted: 0};
        var partyRegistrationDetailsObj = [];
        partyRegistrationDetailsObj.push(gstin);
        partyRegistrationDetailsObj.push(pan);
		console.log(partyDetails)
        $.ajax({
            url : "/erp/masters/json/web/party/save/",
            type : "POST",
            dataType: "json",
            data :  {
                        party_details:JSON.stringify(partyDetails), 
                        party_contact_details: JSON.stringify(partyContactDetailsObj), 
                        party_reg_details: JSON.stringify(partyRegistrationDetailsObj)
                    },
            success : function(json) {
                console.log(json)
                if(json['message'].indexOf("1062") != -1){
                    swal("","Party already exists","error");
                    $("#cmdPartySave").val('Save').removeClass('btn-processing');
                }
                else {
                    swal("",json['message'],'success');
                    $("#modalPartyDetails").modal('hide');
                    $('.party_select_dropdown').append($('<option>', {
                        value: json['id'],
                        text: json['name']
                    }));
                    $(".party_select_dropdown").val(json['id']).trigger("chosen:updated").trigger("change");
                    ChangePartyCurrency(Number(json['id']),"currency");
                }
                $("#cmdPartySave").val('Save').removeClass('btn-processing');
                ga('send', 'event', 'Party', 'Create via In-place addition', $('#enterprise_label').val(), 1);
            },
            error : function(xhr,errmsg,err) {
                console.log(xhr.status + ": " + xhr.responseText);
                $("#cmdPartySave").val('Save').removeClass('btn-processing');
            }
        });
    }

	function updateStateValue() {
		var selectedState = $("#id_state-__prefix__-make_choices").find("option:selected").text();
		$("#state").val(selectedState);
	}

    function updateCountryValueInline(loadtype='') {
        var selectedCountry = $("#id_country-__prefix__-make_choices").val();
        if(selectedCountry == "IN") {
            $(".address-container-state-text").addClass("hide");
            $(".address-container-state-list").removeClass("hide");
            $("#state").val($("#id_state-__prefix__-make_choices option:selected").text());
        }
        else {
            $(".address-container-state-text").removeClass("hide");
            $(".address-container-state-list").addClass("hide");
            if(loadtype == "onchange") {
                $("#state").val("");
            }
        }
    }

    function partyModalCloseEvent() {
        $('#modalPartyDetails').on('show.bs.modal', function () {
            $("#id_country-__prefix__-make_choices").val($("#home_country_id").val())
		    if($("#home_country_id").val() == "IN") {
		        $("#id_state-__prefix__-make_choices option:contains(" + $("#home_state_id").val() + ")").prop('selected', 'selected');
                $("#id_state-__prefix__-make_choices").trigger("change");
            }
            else {
                $("#state").val($("#home_state_id").val())
            }
		    $("#currency option:contains(" + $("#home_currency_id").val() + ")").prop('selected', 'selected');
		    updateCountryValueInline();
        });
        $('#modalPartyDetails').on('hidden.bs.modal', function () {
            $(".error-border").removeClass('error-border');
            $(".custom-error-message").remove();
            $("#modalPartyDetails input[type='text'], #modalPartyDetails textarea").val("");
            $("#modalPartyDetails input[type='text']").val("");
            $("#modalPartyDetails input[type='checkbox']").prop("checked", false).trigger("change");
            $("#id_country-__prefix__-make_choices").val("IN").trigger("change");
            $("#id_state-__prefix__-make_choices").val("35.0").trigger("change");
            $("#id_gst_category-__prefix__-make_choices").val("1").trigger("change");
            $("#currency").val($("#currency option:eq(0)").val());

            if($(".party_select_dropdown").val()=='add_new_party' || $(".party_select_dropdown").val()=='0'){
               $(".party_select_dropdown").val($('.party_select_dropdown optgroup:eq(0) option:eq(0)').val()).trigger("chosen:updated").trigger("change");
            }
        });
    }
</script>