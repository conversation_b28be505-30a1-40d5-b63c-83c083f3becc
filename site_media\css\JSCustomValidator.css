.custom-error-message {
	font-size: 12px;
	color: #dd4b39;
	position: absolute;
	/*top: 30px;*/
	letter-spacing: 0.30px;
	line-height: 20px;
}

.error-border,
.qty-error-border {
	border: 1px solid #dd4b39 !important;
	background: #fdeded;
}

.warning-border {
	border: 1px solid #ffbf00 !important;
}
.error-border:focus:not([readonly]) {
	border: 1px solid #dd4b39 !important;
	box-shadow: 0 0 !important;
}

.date-format-error {
	font-size: 12px;
	color: #dd4b39;
	letter-spacing: 0.30px;
	line-height: 20px;
	position: absolute;
}

.error-border::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #999;
  opacity: 1; /* Firefox */
}

.error-border:-ms-input-placeholder { /* Internet Explorer 10-11 */
  color: red;
}

.error-border::-ms-input-placeholder { /* Microsoft Edge */
  color: red;
}

.hsn-suggestion .custom-error-message {
	position: relative;
}
