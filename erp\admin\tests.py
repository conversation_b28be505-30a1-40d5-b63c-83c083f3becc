"""
"""
import datetime
import unittest

from django.test import TestCase

from erp.admin.backend import UserDA<PERSON>
from erp.admin.pgi_backend import SubscriptionService
from erp.dao import DataAccessObject
from erp.models import InvoiceTemplateConfig
from erp.sales import logger
from erp.sales.backend import InvoiceService
from settings import SALES_LEDGER_ID, BILLING_ENTERPRISE_ID

__author__ = 'benedict'


class TestSubscription(TestCase):

	def setUp(self):
		pass

	def tearDown(self):
		pass

	def test_party_creation_pass(self):
		c = SubscriptionService().prepareParty(enterprise_id=102, user_enterprise_id=217, user_id=75)
		logger.info("party_creation_pass:%s" % c)
		self.assertNotEquals(c, None)

	def test_get_material_from_subscription(self):
		pgi_service = SubscriptionService()
		price = 2.95 * 100 / 118  # The paid amount contains tax; 100% amount + 18% tax
		paid_material = pgi_service.getMaterial(enterprise_id=BILLING_ENTERPRISE_ID, price=price)
		self.assertEquals(paid_material.name, "XSERP Basic Plan Annual Subscription Fee")

	def test_save_invoice(self):
		amount = 2950
		rate = amount * 100 / 118
		party_id = 100832
		prepared_by = 69
		enterprise_id = BILLING_ENTERPRISE_ID
		invoice_service = InvoiceService()
		invoice_template = invoice_service.invoice_dao.db_session.query(InvoiceTemplateConfig).filter(
			InvoiceTemplateConfig.enterprise_id == enterprise_id).first()

		prepared_date = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
		invoice_data = {
			'type': 'GST', 'prepared_by': prepared_by, 'enterprise_id': enterprise_id,
			'currency_id': '2', 'id': None,
			'order_accept_date': prepared_date, 'party_id': party_id,
			'currency_conversion_rate': 1.0,
			'po_date': prepared_date, 'tags': ["xserp", "online", "subscription"],
			'issued_on': prepared_date,
			'sale_account_id': SALES_LEDGER_ID,
			'goods_already_supplied': False,
			'grand_total': amount,
			'charges': [], 'taxes': [], 'deliver_to': '', 'gstin': '',
			'notes': invoice_template.template_misc_details.notes,
			'tax_payable_on_reverse_charge': False,
			'remarks': 'Auto created invoice for xserp online Subscription',
			'items': [{
				'material_type': 0,
				'item_id': 360749, 'make_id': 1, 'is_faulty': 0, 'oa_no': None,
				'rate': rate, 'discount': 0.0,
				'unit_id': '',
				'hsn_code': '',
				'enterprise_id': enterprise_id,
				'alternate_unit_id': 1,
				'taxes': [{'tax_code': 'IGST18', 'tax_type': '', 'rate': 18.0}],
				'is_returnable': False,
				'entry_order': 1,
				'quantity': 1.0}
			]
		}
		invoice = invoice_service.saveInvoice(
			enterprise_id=enterprise_id, user_id=prepared_by, invoice_data=invoice_data)
		self.assertEquals(len(invoice.items), 1)

	def test_mail_invoice(self):
		subscription_service = SubscriptionService()
		invoice = subscription_service.invoice_service.invoice_dao.getInvoice(invoice_id=96978, enterprise_id=102)
		latest_subscription = subscription_service.getLatestSubscription(
			enterprise_id=102)
		subscription_service.sendPaymentSuccessfulMail(
			invoice=invoice, paid_user_id=226, latest_subscription=latest_subscription, amount=2905)
		self.assertEquals(len(invoice.items), 1)

	def test_processPaymentSuccessEvent(self):
		subscription_service = SubscriptionService()
		payment_info = {"transaction_id": "123456-1234255", "amount": 1.18, "message": "UnitTest"}
		invoice = subscription_service.processPaymentSuccessEvent(
			enterprise_id=102, user_id=76, transaction_id=payment_info["transaction_id"],
			amount=1.18, payment_info=payment_info)
		self.assertNotEqual(invoice, None)

	def test_getLatestSubscription(self):
		subscription_service = SubscriptionService()
		latest_subscription = subscription_service.getLatestSubscription(
			enterprise_id=102)
		self.assertEquals(latest_subscription.till, datetime.datetime(2021, 10, 29, 14, 45, 57))

	def test1_getLatestSubscription(self):
		subscription_service = SubscriptionService()
		latest_subscription = subscription_service.getLatestSubscription(
			enterprise_id=None)
		self.assertEquals(latest_subscription, None)

	def test_getSubscriptionForTransaction(self):
		subscription_service = SubscriptionService()
		subscription_transaction = subscription_service.getSubscriptionForTransaction(enterprise_id=186, transaction_id='186-20210119143508964170')
		self.assertNotEquals(subscription_transaction, None)

	def test_getPgiMerchantInfo(self):
		subscription_service = SubscriptionService()
		merchan_info = subscription_service.getPgiMerchantInfo(enterprise_id=None)
		self.assertEquals(merchan_info, None)

	def test_createSubscriptionEntry(self):
		subscription_service = SubscriptionService()
		subscription_entry = subscription_service.createSubscriptionEntry(enterprise_id=None, paid_material=None, transaction_id=None, is_active=True, payment_info=None)
		self.assertEquals(subscription_entry, None)


class TestUserDao(unittest.TestCase):

	def setUp(self):
		self.dao = DataAccessObject()

	def test_user_map(self):
		user_dao = UserDAO()
		map_status = user_dao.update_user_location_map(user_id=49, location_id=1, enterprise_id=1, status=1)
		self.assertTrue(map_status)

	def test_user_map_update(self):
		user_dao = UserDAO()
		map_status = user_dao.update_user_location_map(user_id=49, location_id=1, enterprise_id=1, status=0)
		self.assertTrue(map_status)

	def test_user_map_create_new(self):
		user_dao = UserDAO()
		map_status = user_dao.update_user_location_map(user_id=49, location_id=2, enterprise_id=1, status=1)
		self.assertTrue(map_status)
