import base64
from io import String<PERSON>
import datetime
import json
from decimal import Decimal
from math import ceil

import barcode
import pdfkit
import qrcode
from barcode.writer import ImageWriter
from django.template import Context
from django.template.loader import get_template
from django.template import Template
from sqlalchemy import and_

from erp import DEFAULT_MAKE_ID, helper
from erp.admin.backend import EnterpriseProfileService, PurchaseTemplateConfigService
from erp.dao import executeQuery
from erp.masters.backend import MasterService
from erp.models import EnterpriseImages, InvoiceTemplateBannerImage, EnterpriseContactMap, SalesEstimate, \
	MaterialSerialNo, InvoiceMaterialSerialNo, InvoiceMaterial, Party, OA
from erp.properties import TEMPLATE_TITLE_KEY, CLIENT_APPROVAL_MAIL
from erp.sales import logger
from settings import SQLASession
from util.document_compiler import PDFGenerator
from util.ftp_helper import FTPUtil
from util.helper import writeFile, getAbsolutePath, readFile

INVOICE_TEMPLATE = 'master_res'
INVOICE_CONFIG_TEMPLATE = 'config_res'
INVOICE_GENERAL_TEMPLATE = 'general_res'
INVOICE_HEADER_TEMPLATE = 'header_res'
INVOICE_ITEM_TEMPLATE = 'item_res'
INVOICE_SUMMARY_TEMPLATE = 'summary_res'
INVOICE_MISC_TEMPLATE = 'misc_res'

INVOICE_DOC_HEADER_PATH = '/site_media/tmp/invoice_header_%s.html'
INVOICE_DOC_FOOTER_PATH = '/site_media/tmp/invoice_footer_%s.html'

SE_DOC_FOOTER_PATH = '/site_media/tmp/po_footer_%s.html'
SE_GENERAL_TEMPLATE = 'general_res'
SE_HEADER_TEMPLATE = 'header_res'
SE_ITEM_TEMPLATE = 'item_res'
SE_SUMMARY_TEMPLATE = 'summary_res'
SE_MISC_TEMPLATE = 'misc_res'

OA_DOC_FOOTER_PATH = '/site_media/tmp/po_footer_%s.html'
OA_GENERAL_TEMPLATE = 'general_res'
OA_HEADER_TEMPLATE = 'header_res'
OA_ITEM_TEMPLATE = 'item_res'
OA_SUMMARY_TEMPLATE = 'summary_res'
OA_MISC_TEMPLATE = 'misc_res'


__author__ = 'Kalaivanan, Saravanan'

TARGET_PATH = '/site_media/tmp/invoice.pdf'


class InvoicePDFGenerator(PDFGenerator):
	"""
	Generates a PDF replica of the Invoice that will be available printed on a pre-printed sheet.
	"""
	def __init__(self, inv_template_config=None, target_file_path=TARGET_PATH):
		super(InvoicePDFGenerator, self).__init__(target_file_path)
		self.inv_doc_datetime_formatter = inv_template_config.template_general_details.inv_doc_datetime_format

	def generatePDF(
			self, source=None, config_info=None, enterprise=None, logged_in_user=None, invoice_id=None, doc_status="",
			updated_banner_image=None, header_text=None, is_receiver_sign_enabled=False):
		"""

		:param source:
		:param config_info:
		:param enterprise:
		:param logged_in_user:
		:param invoice_id:
		:param doc_status:
		:param updated_banner_image:
		:param header_text:
		:param is_receiver_sign_enabled:
		:return:
		"""
		# Changes for HTML to PDF using pdfkit
		db_session = SQLASession()
		profile_service = EnterpriseProfileService()
		countries = MasterService().getCountries()
		se_details = ""
		enterprise_image = db_session.query(EnterpriseImages).filter(EnterpriseImages.enterprise_id == enterprise.id).first()
		if enterprise_image and enterprise_image.logo and enterprise_image.logo != "":
			logo = "data:image/%s;base64,%s" % (enterprise_image.ext, base64.encodestring(enterprise_image.logo))
		else:
			logo = ""
		enterprise_contact_detail = []
		enterprise_contacts = db_session.query(EnterpriseContactMap).filter(
				EnterpriseContactMap.enterprise_id == enterprise.id, EnterpriseContactMap.sequence_id == 1).order_by(
			EnterpriseContactMap.sequence_id)

		country_list = []
		for country in countries:
			country_list.append({"country_code": country.code, "country_name": country.name})

		for enterprise_contact_map in enterprise_contacts:
			enterprise_contact_detail_dict = {
				"sequence_id": enterprise_contact_map.sequence_id, "name": enterprise_contact_map.contact.name,
				"phone_no": enterprise_contact_map.contact.phone_no, "email": enterprise_contact_map.contact.email,
				"fax_no": enterprise_contact_map.contact.fax_no, "is_whatsapp": enterprise_contact_map.contact.is_whatsapp}
			enterprise_contact_detail.append(enterprise_contact_detail_dict)
		if config_info.template_header_details.included_reg_items:
			included_reg_items = json.loads(config_info.template_header_details.included_reg_items)
		else:
			included_reg_items = []
		enterprise_registration_details = []
		for item in included_reg_items:
			reg_detail = profile_service.profile_dao.getEnterpriseRegistrationDetail(
				enterprise_id=enterprise.id, label=item["label"])
			enterprise_registration_details.append(reg_detail)
		issued_date = str(source.issued_on.strftime(self.inv_doc_datetime_formatter)) if source.issued_on is not None and source.issued_on != '0000-00-00 00:00:00' else ""
		po_date = str(source.po_date.strftime(self.inv_doc_datetime_formatter.rsplit(" ", 1)[0])) if source.po_date is not None and source.po_date != '0000-00-00' else ""
		return_date = str(source.return_date.strftime(self.inv_doc_datetime_formatter.rsplit(" ", 1)[0])) if source.return_date is not None and source.return_date != '0000-00-00 00:00:00' else ""

		if source.se and source.se_id != "":
			se_date = str(source.se_date.strftime(self.inv_doc_datetime_formatter.rsplit(" ", 1)[0])) if source.se_date is not None and source.se_date != '0000-00-00 00:00:00' else ""
			if se_date != "":
				se_details = source.se.getInternalCode() + " - " + str(se_date)
			else:
				se_details = source.se.getInternalCode()
		consolidated_materials, oa_se_details = helper.getConsolidatedMaterial(invoice=source, date_format=self.inv_doc_datetime_formatter)
		sorted_material = {}
		item_sort_order = long(config_info.template_item_details.item_sort_order)
		for key in consolidated_materials:
			if item_sort_order == 1 and consolidated_materials[key]['entry_order'] != "" \
				and consolidated_materials[key]['entry_order'] is not None and consolidated_materials[key]['entry_order'] != 0:
				entry_order = consolidated_materials[key]['entry_order']
				sorted_material[entry_order] = consolidated_materials[key]
			else:
				material = consolidated_materials[key]['material']
				if "item_id" in material.__dict__:
					if item_sort_order == 3:
						item_key = str(material.item.material_id) + "_" + key.lower()
						sorted_material[item_key] = consolidated_materials[key]
					else:
						sorted_material[key.lower()] = consolidated_materials[key]
				else:
					sorted_material[key.lower()] = consolidated_materials[key]

		sorted_taxes = source.getTaxes()
		tax_summary = source.getTaxSummary(invoice_materials=consolidated_materials, source=source)
		total_in_words = PDFGenerator.getTotalInWords(value=abs(source.grand_total), currency=source.currency)
		invoice_materials = []
		hsn_summary = []

		for item_key in sorted(sorted_material.keys()):
			invoice_materials.append(sorted_material[item_key])

		if config_info.template_summary_details.include_hsn_summary:
			consolidated_hsn_summary = helper.getHSNSummary(invoice=source)
			hsn_summary_count = len(consolidated_hsn_summary)

			for summary_key in consolidated_hsn_summary:
				hsn_summary.append(consolidated_hsn_summary[summary_key])
		else:
			hsn_summary_count = 0

		item_column_specs = self.getItemColumnSpecs(
			config_info=config_info, hsn_summary_count=hsn_summary_count, source_type=source.type)

		item_rows = self.getItemCount(
			material_count=len(consolidated_materials), sorted_taxes_count=len(sorted_taxes), charges_count=len(source.charges),
			tax_type=config_info.template_item_details.tax_type, tax_summary=tax_summary)

		heading = {
			'GST': config_info.template_header_details.gst_label,
			'Service': config_info.template_header_details.service_label,
			'Trading': config_info.template_header_details.trading_label,
			'BoS': config_info.template_header_details.billofsupply_label,
			'DC': config_info.template_header_details.dc_label,
			'JDC': config_info.template_header_details.dc_label,
			'JIN': config_info.template_header_details.dc_label,
			'Issue': 'Issue'}

		form_name = heading[source.type] if source.type in heading else config_info.template_header_details.others_label
		po_no = source.job_po.getCode() if source.type == 'JDC' else (source.po_no if source.po_no else "")
		if source.type == "Issue":
			is_bill_to_and_ship_to_same = True
		else:
			delivery_address = ""
			if source.deliver_to:
				delivery_address = source.deliver_to.strip()
			is_bill_to_and_ship_to_same = False
			if source.customer:
				billing_address = source.customer.address_1 + source.customer.address_2 + ", " + source.customer.city + ", " + source.customer.state
				is_bill_to_and_ship_to_same = (billing_address.strip() == delivery_address) and (
					source.gstin == source.customer.gst_no)

		if source.approver and source.approver.images and source.approver.images.signature and source.approver.images.signature != "":
			approver_signature = "data:image/%s;base64,%s" % (source.approver.images.signature_ext, base64.encodestring(source.approver.images.signature))
		else:
			approver_signature = ""

		header_left_image = ""
		header_center_image = ""
		header_right_image = ""
		footer_left_image = ""
		footer_center_image = ""
		footer_right_image = ""
		header_image_size = 14
		footer_image_size = 14
		is_header_banner_image = False
		is_footer_banner_image = False

		if doc_status == "PREVIEW":
			if updated_banner_image:
				for banner in updated_banner_image:
					if banner['section'] == 'Header':
						header_image_size = banner['size']
						if banner['banner_image']:
							is_header_banner_image = True
						if banner['position'] == 'Left':
							header_left_image = banner['banner_image']
						if banner['position'] == 'Center':
							header_center_image = banner['banner_image']
						if banner['position'] == 'Right':
							header_right_image = banner['banner_image']

					if banner['section'] == 'Footer':
						footer_image_size = banner['size']
						if banner['banner_image']:
							is_footer_banner_image = True
						if banner['position'] == 'Left':
							footer_left_image = banner['banner_image']
						if banner['position'] == 'Center':
							footer_center_image = banner['banner_image']
						if banner['position'] == 'Right':
							footer_right_image = banner['banner_image']
		else:
			if source.type != "Issue":
				invoice_banner_image = db_session.query(InvoiceTemplateBannerImage).filter(
					InvoiceTemplateBannerImage.config_id == config_info.id)
				for image in invoice_banner_image:
					banner_image = FTPUtil().download(file_path=enterprise.id, filename=image.attachment.file)
					banner_image_base64 = "data:image/%s;base64,%s" % (
								image.attachment.file_ext, banner_image.encode('base64')) if banner_image else ""
					if image.section == 'Header':
						header_image_size = image.size
						if banner_image:
							is_header_banner_image = True
						if image.position == 'Left':
							header_left_image = banner_image_base64
						if image.position == 'Center':
							header_center_image = banner_image_base64
						if image.position == 'Right':
							header_right_image = banner_image_base64

					if image.section == 'Footer':
						footer_image_size = image.size
						if banner_image:
							is_footer_banner_image = True
						if image.position == 'Left':
							footer_left_image = banner_image_base64
						if image.position == 'Center':
							footer_center_image = banner_image_base64
						if image.position == 'Right':
							footer_right_image = banner_image_base64
		if not is_header_banner_image:
			header_image_size = 0

		if is_footer_banner_image:
			banner_margin = (footer_image_size * 0.2645833333)
		else:
			banner_margin = 0
			footer_image_size = 0

		scanning_code = ""
		if int(config_info.template_header_details.scan_code_option) == 2:
			# Generating QR code
			qr_code = qrcode.QRCode(version=1, error_correction=qrcode.constants.ERROR_CORRECT_H, box_size=10, border=4,)
			qr_code.add_data(source.getCode())
			qr_code.make(fit=True)
			qr_img = qr_code.make_image()
			buffer = StringIO()
			qr_img.save(buffer, format="JPEG")
			qr_img_base64 = base64.b64encode(buffer.getvalue())
			scanning_code = "data:image/%s;base64,%s" % ("jpeg", qr_img_base64)
		elif int(config_info.template_header_details.scan_code_option) == 3:
			# Generating Barcode
			barcode_class = barcode.get_barcode_class('code128')
			barcode_class.default_writer_options['write_text'] = False
			bar_code = barcode_class(str(source.getCode()), writer=ImageWriter())
			bar_code.save(getAbsolutePath('/site_media/tmp/bar_code'))
			bar_code_image = readFile('/site_media/tmp/bar_code.png')
			scanning_code = "data:image/%s;base64,%s" % ("png", base64.encodestring(bar_code_image))

		irn_scanning_code = None
		irn_details = ''
		irn_ack_json = source.irn_ack_json
		if irn_ack_json:
			irn_qr_code_data = irn_ack_json['SignedQRCode']

			if irn_qr_code_data:
				qr_code = qrcode.QRCode(version=1, error_correction=qrcode.constants.ERROR_CORRECT_H, box_size=10, border=4,)
				qr_code.add_data(irn_qr_code_data)
				qr_code.make(fit=True)
				qr_img = qr_code.make_image()
				buffer = StringIO()
				qr_img.save(buffer, format="JPEG")
				qr_img_base64 = base64.b64encode(buffer.getvalue())
				irn_scanning_code = "data:image/%s;base64,%s" % ("jpeg", qr_img_base64)

			ack_date = str(datetime.datetime.strptime(str(irn_ack_json['AckDt']), '%Y-%m-%d %H:%M:%S').strftime(
				self.inv_doc_datetime_formatter)) if irn_ack_json['AckDt'] is not None and irn_ack_json['AckDt'] != '0000-00-00 00:00:00' else ""

			irn_details = {
				'ack_no': irn_ack_json['AckNo'], 'ack_date': ack_date, 'irn': irn_ack_json['Irn'],
				'irn_scanning_code': irn_scanning_code}

		context = Context({
			'logged_in_user': logged_in_user, 'form_name': form_name, 'scanning_code': scanning_code, 'irn_details': irn_details,
			'labels': header_text, 'is_receiver_sign_enabled': is_receiver_sign_enabled, 'country_list': country_list,
			'enterprise': enterprise, 'enterprise_logo': logo, 'enterprise_reg': enterprise_registration_details,
			'source': source, 'invoice_no': source.getCode(), 'issued_date': issued_date, 'po_no': po_no, 'po_date': po_date,
			'se_no_date': oa_se_details if oa_se_details != "" else se_details,
			'se_no': source.se.getInternalCode() if source.se else "", 'return_date': return_date,
			'invoice_materials': invoice_materials, 'doc_status': doc_status,
			'sorted_taxes': sorted_taxes, 'tax_values': source.getTaxValues(), 'tax_summary': tax_summary,
			'item_rows': item_rows, 'item_column_span': item_column_specs, 'template_id': config_info.template_id,
			'cgst_total_value': source.getNetMaterialTaxForType(tax_type="CGST"),
			'sgst_total_value': source.getNetMaterialTaxForType(tax_type="SGST"),
			'igst_total_value': source.getNetMaterialTaxForType(tax_type="IGST"), 'hsn_summary': hsn_summary,
			INVOICE_CONFIG_TEMPLATE: config_info.template_details, 'qir_data': self._constructQIRData(source=source),
			INVOICE_GENERAL_TEMPLATE: config_info.template_general_details, 'has_qir': source.hasQIR(),
			INVOICE_HEADER_TEMPLATE: config_info.template_header_details,
			INVOICE_ITEM_TEMPLATE: config_info.template_item_details,
			INVOICE_SUMMARY_TEMPLATE: config_info.template_summary_details,
			INVOICE_MISC_TEMPLATE: config_info.template_misc_details, 'enterprise_contact_detail': enterprise_contact_detail,
			'header_left_image': header_left_image, 'header_center_image': header_center_image,
			'header_right_image': header_right_image, 'footer_left_image': footer_left_image,
			'footer_center_image': footer_center_image, 'footer_right_image': footer_right_image,
			'header_image_size': header_image_size, 'footer_image_size': footer_image_size,
			TEMPLATE_TITLE_KEY: "Invoice Template", "is_bill_to_and_ship_to_same": is_bill_to_and_ship_to_same,
			'scan_code_option': int(config_info.template_header_details.scan_code_option),
			'approver_signature': approver_signature, 'total_in_words': total_in_words})

		header_data = get_template(getAbsolutePath('/templates/admin/print_template/invoice/document/common/invoice_template_header.html')).render(context)
		header_file_name = INVOICE_DOC_HEADER_PATH % invoice_id
		writeFile(header_data, header_file_name)

		footer_data = get_template(getAbsolutePath('/templates/admin/print_template/invoice/document/common/invoice_template_footer.html')).render(context)
		footer_file_name = INVOICE_DOC_FOOTER_PATH % invoice_id
		writeFile(footer_data, footer_file_name)

		bottom_margin = config_info.template_general_details.margin_bottom + 30 + banner_margin

		page_title = "Preview" if doc_status == "PREVIEW" else source.getCode()
		if config_info.template_misc_details.include_page_no_in_footer:
			options = {
				'page-size': 'A4', 'orientation': 'Portrait',
				'title': page_title,
				'margin-top': '%smm' % config_info.template_general_details.margin_top,
				'margin-right': '%smm' % config_info.template_general_details.margin_right,
				'margin-bottom': '%smm' % bottom_margin,
				'margin-left': '%smm' % config_info.template_general_details.margin_left, 'encoding': "UTF-8",
				'header-html': getAbsolutePath(header_file_name),
				'--header-right': 'Page [page] of [topage]',
				'--header-font-size': '8', 'footer-html': getAbsolutePath(footer_file_name)
			}
		else:
			options = {
				'page-size': 'A4', 'orientation': 'Portrait',
				'title': page_title,
				'margin-top': '%smm' % config_info.template_general_details.margin_top,
				'margin-right': '%smm' % config_info.template_general_details.margin_right,
				'margin-bottom': '%smm' % bottom_margin,
				'margin-left': '%smm' % config_info.template_general_details.margin_left, 'encoding': "UTF-8",
				'header-html': getAbsolutePath(header_file_name),
				'footer-html': getAbsolutePath(footer_file_name)
			}

		css = [
			getAbsolutePath('/site_media/css/bootstrap.css'), getAbsolutePath('/site_media/css/invoice_template_preview.css'),
			getAbsolutePath('/site_media/css/invoice_template.css')]
		if config_info.template_id == 1:
			template_src = getAbsolutePath('/templates/admin/print_template/invoice/document/invoice_template_compact.html')
		elif config_info.template_id == 2:
			template_src = getAbsolutePath('/templates/admin/print_template/invoice/document/invoice_template_simple.html')
		else:
			template_src = getAbsolutePath('/templates/admin/print_template/invoice/document/invoice_template_elegant.html')

		template = get_template(template_src).render(context)
		data = pdfkit.from_string(template, False, options=options, css=css)

		return data

	def getItemColumnSpecs(self, config_info=None, hsn_summary_count=0, source_type=None):
		"""

		:param config_info:
		:param hsn_summary_count:
		:return:
		"""
		inv_item_details = config_info.template_item_details
		inv_summary_details = config_info.template_summary_details

		span_total_column = int(inv_item_details.include_sno) + 1
		if source_type != "Issue":
			span_total_column = span_total_column + int(
				inv_item_details.include_hsnsac and not inv_item_details.hsnsac_part_of_itemdetails)
		if source_type != "Issue" and inv_item_details.include_taxrate and inv_item_details.include_taxamount:
			total_text_column_count = int(inv_item_details.include_sno) + int(
				inv_item_details.include_hsnsac and not inv_item_details.hsnsac_part_of_itemdetails) + int(inv_item_details.include_quantity) + int(
				inv_item_details.include_units and not inv_item_details.units_in_quantity_column) + int(
				inv_item_details.include_unit_price) + int(inv_item_details.include_discount) + 1
			total_value_column_count = int(inv_item_details.include_taxable_amount)
		else:
			total_text_column_count = int(inv_item_details.include_sno) + 1
			total_value_column_count = int(inv_item_details.include_quantity) + int(
				inv_item_details.include_units and not inv_item_details.units_in_quantity_column)
			if source_type != "Issue":
				total_text_column_count = total_text_column_count + int(
					inv_item_details.include_hsnsac and not inv_item_details.hsnsac_part_of_itemdetails)
				total_value_column_count = total_value_column_count + int(
					inv_item_details.include_unit_price) + int(inv_item_details.include_discount) + int(
					inv_item_details.include_taxable_amount)

		span_total_summary = int(inv_item_details.include_quantity) + int(
			inv_item_details.include_units and not inv_item_details.units_in_quantity_column)
		if source_type != "Issue":
			span_total_summary = span_total_summary + int(
				inv_item_details.include_unit_price) + int(inv_item_details.include_taxable_amount) + int(
				inv_item_details.include_discount)
		if source_type != "Issue" and inv_item_details.include_tax:
			if inv_item_details.tax_type == 1:
				if inv_item_details.include_taxrate and inv_item_details.include_taxamount:
					span_tax_column = 6
					total_value_column_count += 6
				elif inv_item_details.include_taxrate or inv_item_details.include_taxamount:
					span_tax_column = 3
					total_value_column_count += 3
				else:
					span_tax_column = 0
			elif inv_item_details.tax_type == 4 and inv_item_details.include_taxamount:
				span_tax_column = 3
				total_value_column_count += 3
			else:
				span_tax_column = 0
		else:
			span_tax_column = 0

		span_all_column = span_total_column + span_total_summary + span_tax_column

		if inv_item_details.include_quantity:
			if inv_summary_details.include_qty_total:
				span_total_summary = span_total_summary - 1

		item_column_span = {
			'span_total_column': span_total_column, 'span_total_summary': span_total_summary,
			'span_tax_column': span_tax_column, 'span_all_column': span_all_column,
			'total_text_column_count': total_text_column_count, 'total_value_column_count': total_value_column_count,
			'hsn_summary_count': hsn_summary_count}
		return item_column_span

	def getItemCount(self, material_count=0, sorted_taxes_count=0, charges_count=0, tax_type=None, tax_summary=None):
		"""

		:param material_count:
		:param sorted_taxes_count:
		:param charges_count:
		:param tax_type:
		:param tax_summary:
		:return:
		"""
		annexure_count = 0
		annexure_count = annexure_count + sorted_taxes_count
		if tax_type == 2:
			annexure_count = annexure_count + (len(tax_summary['cgst_summary']) + len(tax_summary['sgst_summary']) + len(tax_summary['igst_summary'])) / 3
		annexure_count = annexure_count + charges_count
		item_count = material_count + annexure_count

		item_rows = {'item_count': item_count, 'annexure_count': annexure_count}

		return item_rows

	def _constructQIRData(self, source=None):
		"""
		Construct QIR Table as with data for a max of 5 Samples per row

		:param source:
		:return:
		"""
		if source.hasQIR():
			qir_data = {}
			for item in source.items:
				item_key = "%s%s%s" % (
					item.item.name, " (%s)" % item.item.drawing_no if item.item.drawing_no else "",
					" - %s" % item.inv_oa.getCode() if item.inv_oa else (" - %s" % item.grn.invoice_no if item.grn else ""))
				if item_key in qir_data:
					qir_data[item_key].extend(self.__constructQIRItemData(item=item))
				else:
					qir_data[item_key] = self.__constructQIRItemData(item=item)
			logger.info(qir_data)
			return qir_data
		return {}

	def __constructQIRItemData(self, item=None):
		"""
		Construct QIR table row data for every Item's Inspection log against each Parameter (max 5 samples per row)

		:param item:
		:return:
		"""
		quality_log_data = []
		if len(item.inspection_log) > 0:
			parameters = [specification.parameter for specification in item.item.specifications]
			inspection_logs = [log for log in item.inspection_log]
			samples_added = 0
			while True:
				header_row = ['Parameter \ Samples']
				next_sample_set_index = (samples_added + 5) if (samples_added + 5) < len(inspection_logs) else len(inspection_logs)
				samples = inspection_logs[samples_added:next_sample_set_index]
				remarks_row = ['Remarks']
				for sample in samples:
					header_row.append("%s (%s %s)" % (
						sample['sample_no'], sample['quantity'], item.item.unit))
					remarks_row.append(sample['remarks'])
				quality_log_data.append(header_row)
				for parameter in parameters:
					detail_row = [parameter]
					for sample in samples:
						detail_row.append(sample['observations'][parameter] if parameter in sample['observations'] else "")
					quality_log_data.append(detail_row)
				quality_log_data.append(remarks_row)
				samples_added = next_sample_set_index
				if samples_added >= len(inspection_logs):
					break
		return quality_log_data


class SalesEstimatePDFGenerator(PDFGenerator):
	"""
	Generates a PDF replica of the Sales Estimate that will be available printed on a pre-printed sheet.
	"""
	# def __init__(self, target_file_path=TARGET_PATH):
	def __init__(self, sales_estimate=None, target_file_path=TARGET_PATH):
		super(SalesEstimatePDFGenerator, self).__init__(target_file_path)
		self.sales_estimate = sales_estimate

	def getSEMaterialDetails(self, source=None):
		available_materials = []
		available_taxes = []
		total_qty = Decimal(0)
		total = Decimal(0)
		for material in source.items:
			material_make = ""
			material_drawing_no = ''
			material_name = ''
			material_unit = ''
			part_no = ''
			if material.item:
				if material.item.drawing_no and material.item.drawing_no is not None and material.item.drawing_no !='':
					material_drawing_no = material.item.drawing_no
				material_name = material.item.name
				material_unit = material.item.unit.unit_name
				material_description = material.item.description
			if material.item and material.item.makes_json != "":
				make_name = helper.constructDifferentMakeName(material.item.makes_json)
				if make_name:
					material_name = material_name + " [" + make_name + "]"
			if material.make_id != DEFAULT_MAKE_ID:
				part_no = helper.getMakePartNumber(
					enterprise_id=material.enterprise_id, item_id=material.item_id, make_id=material.make_id)
				material_make = material.make.__repr__()
			hsn_code = material.hsn_code if material.hsn_code and material.hsn_code != "" else ""
			material_quantity = material.quantity
			material_rate = material.unit_rate
			if material.alternate_unit_id:
				scale_factor = helper.getScaleFactor(
					enterprise_id=material.enterprise_id, item_id=material.item_id,
					alternate_unit_id=material.alternate_unit_id)
				material_unit = helper.getUnitName(enterprise_id=material.enterprise_id, unit_id=material.alternate_unit_id)
				if scale_factor:
					material_quantity = material.quantity / scale_factor
					material_rate = material.unit_rate * scale_factor
			if not material_quantity:
				material_quantity = 0
			total_qty += Decimal(material_quantity)
			if material.discount:
				material_discount = material.discount
			else:
				material_discount = 0
			material_taxable_value = "{:0.2f}".format(Decimal(material_rate) * Decimal(material_quantity) * (100 - Decimal(material.discount)) / 100)
			total += Decimal(material_taxable_value)
			se_material = {
				"material_drawing_no": material_drawing_no, "material_name": material_name, "material_make": material_make,
				"part_no": part_no, "hsn_code": hsn_code, "material_quantity": material_quantity, "material_unit": material_unit,
				"material_rate": "%0.5f" % material_rate, "material_discount": "%0.2f" % material_discount,
				"material_taxable_value": material_taxable_value, "material_description": material_description}
			available_materials.append(se_material)
		sorted_taxes = source.getTaxes()
		tax_value = 0
		total_value = total
		for se_tax in sorted_taxes:
			if se_tax.tax.is_compound:
				tax_value = Decimal(total_value) * Decimal(se_tax.tax.net_rate / 100)
			else:
				tax_value = Decimal(total) * Decimal(se_tax.tax.net_rate / 100)
			total_value = total_value + tax_value
			taxes = {'tax_name': se_tax.tax.name, 'tax_rate': se_tax.tax.net_rate, 'tax_value': round(tax_value, 2)}
			available_taxes.append(taxes)
		return available_materials, total_qty, available_taxes

	def generatePDF(self, source=None, template_config=None, doc_status="", logged_in_user=None, updated_banner_image=None, se_mail=False):
		try:
			profile_service = EnterpriseProfileService()
			countries = MasterService().getCountries()
			logger.info('Generating PDF for Sales Estimate: %s' % source.id)
			po_template_service = PurchaseTemplateConfigService()
			template_general_details = template_config["general_config"]
			template_header_details = template_config["header_config"]
			template_item_details = template_config["items_config"]
			template_summary_details = template_config["summary_config"]
			template_misc_details = template_config["misc_config"]
			se_template = template_config["print_template"]
			se_doc_datetime_formatter = template_general_details["datetime"]
			if source.enterprise.images and source.enterprise.images.logo and source.enterprise.images.logo != "":
				logo = "data:image/%s;base64,%s" % (source.enterprise.images.ext, base64.encodestring(source.enterprise.images.logo))
			else:
				logo = ""
			country_list = []
			enterprise_country = ""
			for country in countries:
				if source.enterprise.country_code == country.code:
					enterprise_country = country.name
				country_list.append({"country_code": country.code, "country_name": country.name})
			if source.enterprise.address_2 and source.enterprise.address_2 != "":
				address = source.enterprise.address_1 + ", " + source.enterprise.address_2
			else:
				address = source.enterprise.address_1
			enterprise_address = "{address}, {city}, {pin_code} {state}, {country}".format(
				address=address, city=source.enterprise.city, pin_code=source.enterprise.pin_code, state=source.enterprise.state,
				country=enterprise_country.upper())
			party_details = executeQuery(
				"""SELECT pm.party_name as party_name,pm.address1, pm.address2, pm.city,
					pm.pin_code, pm.state, pm.phone_no, pm.email, pm.gstno, pm.panno,
					se.payment_terms, se.special_instructions
				FROM party_master pm
				left join sales_estimate se on se.party_id = pm.party_id
				where se.id = %s and se.enterprise_id = %s
				LIMIT 1""" % (source.id, source.enterprise.id), as_dict=True)
			party_name = party_details[0]["party_name"]
			address1 = party_details[0]["address1"]
			address2 = party_details[0]["address2"]
			city = party_details[0]["city"]
			pin_code = party_details[0]["pin_code"]
			phone_no = party_details[0]["phone_no"]
			email = party_details[0]["email"]
			state = party_details[0]["state"]
			gstno = party_details[0]["gstno"]
			panno = party_details[0]["panno"]
			payment_terms = party_details[0]["payment_terms"]
			special_instructions = party_details[0]["special_instructions"]
			if source.enterprise.se_doc_items:
				included_reg_items = json.loads(source.enterprise.se_doc_items.se_doc_reg_items) if source.enterprise.se_doc_items.se_doc_reg_items else []
			else:
				included_reg_items = []
			se_registration_details = []
			for item in included_reg_items:
				reg_detail = profile_service.profile_dao.getEnterpriseRegistrationDetail(
					enterprise_id=source.enterprise.id, label=item["label"])
				se_registration_details.append(reg_detail)
			se_expiry_date = str(source.expiry_date.strftime(se_doc_datetime_formatter.rsplit(" ", 1)[0])) if source.expiry_date is not None and source.expiry_date != '0000-00-00' else ""
			se_date = str(datetime.datetime.strptime(str(source.last_modified_on), '%Y-%m-%d %H:%M:%S').strftime(str(se_doc_datetime_formatter))) if source.last_modified_on is not None and source.last_modified_on != '0000-00-00 00:00:00' else source.last_modified_on
			if source.status == SalesEstimate.STATUS_APPROVED:
				se_date = str(datetime.datetime.strptime(str(source.approved_on), '%Y-%m-%d %H:%M:%S').strftime(str(se_doc_datetime_formatter))) if source.approved_on is not None and source.approved_on != '0000-00-00 00:00:00' else source.approved_on
			se_item_details, total_quantity, se_taxes = self.getSEMaterialDetails(source=source)
			material_value = 0
			for material in source.items:
				if not material.quantity:
					material.quantity = Decimal(0.00)
				if not material.unit_rate:
					material.unit_rate = Decimal(0.00)
				if not material.discount:
					material.discount = Decimal(0.00)
				material_value += Decimal(round(material.quantity * material.unit_rate * (100 - material.discount) / 100, 2))
			total_value = Decimal(material_value)
			total_in_words = PDFGenerator.getTotalInWords(value=abs(source.grand_total), currency=source.currency)
			item_count = len(source.items)
			logger.info('No of Materials: %s' % item_count)
			se_approver = '-NA-' if source.approver is None else source.modifier.__repr__()
			drafter_signature = ""
			approver_signature = ""
			if source.approver and source.approver.images and source.approver.images.signature and source.approver.images.signature != "":
				approver_signature = "data:image/%s;base64,%s" % (
					source.approver.images.signature_ext, base64.encodestring(source.approver.images.signature))
			appendix_pages = 0
			if item_count >= 4:
				# Approximate space estimate to decide if appendix page is necessary
				appendix_pages = ceil(item_count / 15.0)
			hsn_summary = []
			hsn_list = []
			if template_summary_details["hsn_summary"]["print"]:
				for material in source.items:
					if material.hsn_code:
						hsn_total = 0
						hsn_code = ''
						hsn_dict = {}
						if material.hsn_code not in hsn_list:
							hsn_list.append(material.hsn_code)
							for item in source.items:
								if material.hsn_code == item.hsn_code:
									hsn_code = str(material.hsn_code)
									hsn_total += (item.unit_rate * item.quantity - (item.unit_rate * item.quantity * item.discount/100))
							hsn_dict["hsn_code"] = hsn_code
							hsn_dict["consolidated_taxable_value"] = hsn_total
							hsn_summary.append(hsn_dict)
			header_left_image = ""
			header_center_image = ""
			header_right_image = ""
			footer_left_image = ""
			footer_center_image = ""
			footer_right_image = ""
			header_image_height = template_misc_details["banner_header"]["height"]
			footer_image_height = template_misc_details["banner_footer"]["height"]
			is_header_banner_image = False
			is_footer_banner_image = False
			header_left_image_width = 0
			header_center_image_width = 0
			header_right_image_width = 0
			footer_left_image_width = 0
			footer_center_image_width = 0
			footer_right_image_width = 0

			if doc_status == "PREVIEW":
				if updated_banner_image:
					for banner in updated_banner_image:
						if banner['section'] == 'Header':
							if banner['banner_image']:
								is_header_banner_image = True
							if banner['position'] == 'left':
								header_left_image = banner['banner_image']
								header_left_image_width = banner['width']
							if banner['position'] == 'center':
								header_center_image = banner['banner_image']
								header_center_image_width = banner['width']
							if banner['position'] == 'right':
								header_right_image = banner['banner_image']
								header_right_image_width = banner['width']
						if banner['section'] == 'Footer':
							if banner['banner_image']:
								is_footer_banner_image = True
							if banner['position'] == 'left':
								footer_left_image = banner['banner_image']
								footer_left_image_width = banner['width']
							if banner['position'] == 'center':
								footer_center_image = banner['banner_image']
								footer_center_image_width = banner['width']
							if banner['position'] == 'right':
								footer_right_image = banner['banner_image']
								footer_right_image_width = banner['width']
			else:
				for attachment in template_misc_details["banner_header"]["attachment"]:
					banner_image, attachments = po_template_service.purchase_template_dao.getBannerAttachment(
								attachment_id=attachment["attachment_id"], enterprise_id=source.enterprise.id)
					banner_image_base64 = "data:image/%s;base64,%s" % (
								attachments.file_ext, banner_image.encode('base64')) if banner_image else ""
					if banner_image:
						is_header_banner_image = True
					if attachment["position"] == 'left':
						header_left_image = banner_image_base64
						header_left_image_width = attachment['width']
					if attachment["position"] == 'center':
						header_center_image = banner_image_base64
						header_center_image_width = attachment['width']
					if attachment["position"] == 'right':
						header_right_image = banner_image_base64
						header_right_image_width = attachment['width']
				for attachment in template_misc_details["banner_footer"]["attachment"]:
					banner_image, attachments = po_template_service.purchase_template_dao.getBannerAttachment(
								attachment_id=attachment["attachment_id"], enterprise_id=source.enterprise.id)
					banner_image_base64 = "data:image/%s;base64,%s" % (
								attachments.file_ext, banner_image.encode('base64')) if banner_image else ""
					if banner_image:
						is_footer_banner_image = True
					if attachment["position"] == 'left':
						footer_left_image = banner_image_base64
						footer_left_image_width = attachment['width']
					if attachment["position"] == 'center':
						footer_center_image = banner_image_base64
						footer_center_image_width = attachment['width']
					if attachment["position"] == 'right':
						footer_right_image = banner_image_base64
						footer_right_image_width = attachment['width']
			if not is_header_banner_image:
				header_image_height = 0
			if is_footer_banner_image:
				banner_margin = (int(footer_image_height) * 0.2645833333)
			else:
				banner_margin = 0
				footer_image_height = 0
			context = Context({
				'form_name': 'Sales Estimate', 'enterprise_logo': logo, 'enterprise_address': enterprise_address, 'source': source,
				'se_no': source.getCode(), 'se_reg_details': se_registration_details, 'se_date': se_date,
				'se_expiry_date': se_expiry_date, 'se_item_details': se_item_details, 'total_quantity': total_quantity, 'total_value': total_value,
				'se_taxes': se_taxes, 'total_in_words': total_in_words, 'se_approver': se_approver, 'country_list': country_list,
				'header_left_image': header_left_image, 'header_center_image': header_center_image,
				'header_right_image': header_right_image, 'footer_left_image': footer_left_image,
				'footer_center_image': footer_center_image, 'footer_right_image': footer_right_image,
				'header_image_size': header_image_height, 'footer_image_size': footer_image_height,
				'header_left_image_width': header_left_image_width, 'header_center_image_width': header_center_image_width,
				'header_right_image_width': header_right_image_width, 'footer_left_image_width': footer_left_image_width,
				'footer_center_image_width': footer_center_image_width, 'footer_right_image_width': footer_right_image_width,
				'party_name': party_name, 'address1': address1, 'address2': address2, 'city': city, 'state': state, 'pin_code': pin_code,
				'phone_no': phone_no, 'email': email, 'gstno': gstno, 'panno': panno, 'payment_terms': payment_terms,
				'drafter_signature': drafter_signature, 'approver_signature': approver_signature,
				'appendix_pages': appendix_pages, SE_GENERAL_TEMPLATE: template_general_details,
				SE_HEADER_TEMPLATE: template_header_details,
				SE_ITEM_TEMPLATE: template_item_details, SE_SUMMARY_TEMPLATE: template_summary_details,
				SE_MISC_TEMPLATE: template_misc_details, 'hsn_summary': hsn_summary, 'special_instructions': special_instructions,
				'template_title': "Sales Estimate Document"})
			footer_data = get_template(
				getAbsolutePath('/templates/admin/print_template/se/document/common/se_template_footer.html')).render(
				context)
			footer_file_name = SE_DOC_FOOTER_PATH % source.id
			writeFile(footer_data, footer_file_name)
			page_title = "Preview" if doc_status == "PREVIEW" else source.getCode()
			bottom_margin = int(template_general_details["margin"]["bottom"]) + 30 + banner_margin
			if template_misc_details["print_pageno"]:
				options = {
					'page-size': 'A4', 'orientation': 'Portrait',
					'margin-top': '%smm' % template_general_details["margin"]["top"],
					'margin-right': '%smm' % template_general_details["margin"]["right"],
					'margin-bottom': '%smm' % bottom_margin,
					'margin-left': '%smm' % template_general_details["margin"]["left"], 'encoding': "UTF-8",
					'title': page_title,
					'--header-right': 'Page [page] of [topage]',
					'--header-font-size': '8',
					'footer-html': getAbsolutePath(footer_file_name)
				}
			else:
				options = {
					'page-size': 'A4', 'orientation': 'Portrait',
					'margin-top': '%smm' % template_general_details["margin"]["top"],
					'margin-right': '%smm' % template_general_details["margin"]["right"],
					'margin-bottom': '%smm' % bottom_margin,
					'margin-left': '%smm' % template_general_details["margin"]["left"], 'encoding': "UTF-8",
					'title': page_title,
					'footer-html': getAbsolutePath(footer_file_name)
				}
			css = [
				getAbsolutePath('/site_media/css/bootstrap.css'), getAbsolutePath('/site_media/css/invoice_template_preview.css'),
				getAbsolutePath('/site_media/css/invoice_template.css')]

			if not se_mail:
				template = Template(se_template).render(context)
				data = pdfkit.from_string(template, False, options=options, css=css)
				return data
			else:
				template_src = getAbsolutePath(CLIENT_APPROVAL_MAIL)
				template = get_template(template_src).render(context)
				return template
		except Exception as e:
			logger.exception('Something went wrong while creating PDF self.canvas - %s' % e)


class OrderAcknowlegdementPDFGenerator(PDFGenerator):
	"""
	Generates a PDF replica of the Sales Estimate that will be available printed on a pre-printed sheet.
	"""
	# def __init__(self, target_file_path=TARGET_PATH):
	def __init__(self, order_acknowledgement=None, target_file_path=TARGET_PATH):
		super(OrderAcknowlegdementPDFGenerator, self).__init__(target_file_path)
		self.order_acknowledgement = order_acknowledgement

	def getOAMaterialDetails(self, source=None):
		available_materials = []
		available_taxes = []
		total_qty = Decimal(0)
		total = Decimal(0)
		for material in source.items:
			material_make = ""
			material_drawing_no = ''
			material_name = ''
			material_unit = ''
			part_no = ''
			material_description = ""
			if material.item:
				if material.item.drawing_no and material.item.drawing_no is not None and material.item.drawing_no !='':
					material_drawing_no = material.item.drawing_no
				material_name = material.item.name
				material_unit = material.item.unit.unit_name
				material_description = material.item.description
			if material.item and material.item.makes_json != "":
				make_name = helper.constructDifferentMakeName(material.item.makes_json)
				if make_name:
					material_name = material_name + " [" + make_name + "]"
			if material.make_id != DEFAULT_MAKE_ID:
				part_no = helper.getMakePartNumber(
					enterprise_id=material.enterprise_id, item_id=material.item_id, make_id=material.make_id)
				material_make = material.make.__repr__()
			hsn_code = material.hsn_code if material.hsn_code and material.hsn_code != "" else ""
			material_quantity = material.quantity
			material_rate = material.price
			if material.alternate_unit_id:
				scale_factor = helper.getScaleFactor(
					enterprise_id=material.enterprise_id, item_id=material.item_id,
					alternate_unit_id=material.alternate_unit_id)
				material_unit = helper.getUnitName(enterprise_id=material.enterprise_id, unit_id=material.alternate_unit_id)
				if scale_factor:
					material_quantity = material.quantity / scale_factor
					material_rate = material.price * scale_factor
			if not material_quantity:
				material_quantity = 0
			total_qty += Decimal(material_quantity)
			if material.discount:
				material_discount = material.discount
			else:
				material_discount = 0
			material_taxable_value = "{:0.2f}".format(Decimal(material_rate) * Decimal(material_quantity) * (100 - Decimal(material.discount)) / 100)
			total += Decimal(material_taxable_value)
			oa_material = {
				"material_drawing_no": material_drawing_no, "material_name": material_name, "material_make": material_make,
				"part_no": part_no, "hsn_code": hsn_code, "material_quantity": material_quantity, "material_unit": material_unit,
				"material_rate": "%0.5f" % material_rate, "material_discount": "%0.2f" % material_discount,
				"material_taxable_value": material_taxable_value, "material_description": material_description}
			available_materials.append(oa_material)
		sorted_taxes = source.getTaxes()
		tax_value = 0
		total_value = total
		for oa_tax in sorted_taxes:
			if oa_tax.tax.is_compound:
				tax_value = Decimal(total_value) * Decimal(oa_tax.tax.net_rate / 100)
			else:
				tax_value = Decimal(total) * Decimal(oa_tax.tax.net_rate / 100)
			total_value = total_value + tax_value
			taxes = {'tax_name': oa_tax.tax.name, 'tax_rate': oa_tax.tax.net_rate, 'tax_value': round(tax_value, 2)}
			available_taxes.append(taxes)
		return available_materials, total_qty, available_taxes

	def generatePDF(self, source=None, template_config=None, doc_status="", logged_in_user=None, updated_banner_image=None, se_mail=False):
		try:
			profile_service = EnterpriseProfileService()
			countries = MasterService().getCountries()
			logger.info('Generating PDF for Order Acknowledgement: %s' % source.id)
			po_template_service = PurchaseTemplateConfigService()
			template_general_details = template_config["general_config"]
			template_header_details = template_config["header_config"]
			template_item_details = template_config["items_config"]
			template_summary_details = template_config["summary_config"]
			template_misc_details = template_config["misc_config"]
			oa_template = template_config["print_template"]
			oa_doc_datetime_formatter = template_general_details["datetime"]
			if source.enterprise.images and source.enterprise.images.logo and source.enterprise.images.logo != "":
				logo = "data:image/%s;base64,%s" % (source.enterprise.images.ext, base64.encodestring(source.enterprise.images.logo))
			else:
				logo = ""
			country_list = []
			enterprise_country = ""
			for country in countries:
				if source.enterprise.country_code == country.code:
					enterprise_country = country.name
				country_list.append({"country_code": country.code, "country_name": country.name})
			if source.enterprise.address_2 and source.enterprise.address_2 != "":
				address = source.enterprise.address_1 + ", " + source.enterprise.address_2
			else:
				address = source.enterprise.address_1
			enterprise_address = "{address}, {city}, {pin_code} {state}, {country}".format(
				address=address, city=source.enterprise.city, pin_code=source.enterprise.pin_code, state=source.enterprise.state,
				country=enterprise_country.upper())
			if source.enterprise.se_doc_items:
				included_reg_items = json.loads(source.enterprise.se_doc_items.se_doc_reg_items) if source.enterprise.se_doc_items.se_doc_reg_items else []
			else:
				included_reg_items = []
			oa_registration_details = []
			for item in included_reg_items:
				reg_detail = profile_service.profile_dao.getEnterpriseRegistrationDetail(
					enterprise_id=source.enterprise.id, label=item["label"])
				oa_registration_details.append(reg_detail)
			payment_terms = source.payment_terms if source.payment_terms else ''
			special_instructions = source.special_instructions if source.special_instructions else ''
			po_no = source.po_no if source.po_no else ''
			po_date = str(source.po_date.strftime(oa_doc_datetime_formatter.rsplit(" ", 1)[0])) if source.po_date is not None and source.po_date != '0000-00-00' else ""
			se_no = ''
			se_date = ''
			if source.se_id:
				se_no_query = """SELECT CONCAT(financial_year, '/SE/S', LPAD(se_no, 5, '0'), IFNULL(sub_number, '')) AS se_no
						FROM sales_estimate where id = %s and enterprise_id = %s""" % (source.se_id, source.enterprise.id)
				se_no = executeQuery(se_no_query)
				se_no = se_no[0][0] if se_no else ''
				se_date = str(source.se_date.strftime(oa_doc_datetime_formatter.rsplit(" ", 1)[0])) if source.se_date is not None and source.se_date != '0000-00-00' else ""
			delivery_due_date = str(source.delivery_due_date.strftime(oa_doc_datetime_formatter.rsplit(" ", 1)[0])) if source.delivery_due_date is not None and source.delivery_due_date != '0000-00-00' else ""
			oa_date = str(datetime.datetime.strptime(str(source.last_modified_on), '%Y-%m-%d %H:%M:%S').strftime(str(oa_doc_datetime_formatter))) if source.last_modified_on is not None and source.last_modified_on != '0000-00-00 00:00:00' else source.last_modified_on
			if source.status == OA.STATUS_APPROVED:
				oa_date = str(datetime.datetime.strptime(str(source.approved_on), '%Y-%m-%d %H:%M:%S').strftime(str(oa_doc_datetime_formatter))) if source.approved_on is not None and source.approved_on != '0000-00-00 00:00:00' else source.approved_on
			oa_item_details, total_quantity, oa_taxes = self.getOAMaterialDetails(source=source)
			material_value = 0
			for material in source.items:
				if not material.quantity:
					material.quantity = Decimal(0.00)
				if not material.price:
					material.price = Decimal(0.00)
				if not material.discount:
					material.discount = Decimal(0.00)
				material_value += Decimal(round(material.quantity * material.price * (100 - material.discount) / 100, 2))
			total_value = Decimal(material_value)
			total_in_words = PDFGenerator.getTotalInWords(value=abs(source.grand_total), currency=source.currency)
			item_count = len(source.items)
			logger.info('No of Materials: %s' % item_count)
			oa_approver = '-NA-' if source.approver is None else source.modifier.__repr__()
			drafter_signature = ""
			approver_signature = ""
			if source.approver and source.approver.images and source.approver.images.signature and source.approver.images.signature != "":
				approver_signature = "data:image/%s;base64,%s" % (
					source.approver.images.signature_ext, base64.encodestring(source.approver.images.signature))
			appendix_pages = 0
			if item_count >= 4:
				# Approximate space estimate to decide if appendix page is necessary
				appendix_pages = ceil(item_count / 15.0)
			hsn_summary = []
			hsn_list = []
			if template_summary_details["hsn_summary"]["print"]:
				for material in source.items:
					if material.hsn_code:
						hsn_total = 0
						hsn_code = ''
						hsn_dict = {}
						if material.hsn_code not in hsn_list:
							hsn_list.append(material.hsn_code)
							for item in source.items:
								if material.hsn_code == item.hsn_code:
									hsn_code = str(material.hsn_code)
									hsn_total += (item.price * item.quantity - (item.price * item.quantity * item.discount/100))
							hsn_dict["hsn_code"] = hsn_code
							hsn_dict["consolidated_taxable_value"] = hsn_total
							hsn_summary.append(hsn_dict)
			header_left_image = ""
			header_center_image = ""
			header_right_image = ""
			footer_left_image = ""
			footer_center_image = ""
			footer_right_image = ""
			header_image_height = template_misc_details["banner_header"]["height"]
			footer_image_height = template_misc_details["banner_footer"]["height"]
			is_header_banner_image = False
			is_footer_banner_image = False
			header_left_image_width = 0
			header_center_image_width = 0
			header_right_image_width = 0
			footer_left_image_width = 0
			footer_center_image_width = 0
			footer_right_image_width = 0

			if doc_status == "PREVIEW":
				if updated_banner_image:
					for banner in updated_banner_image:
						if banner['section'] == 'Header':
							if banner['banner_image']:
								is_header_banner_image = True
							if banner['position'] == 'left':
								header_left_image = banner['banner_image']
								header_left_image_width = banner['width']
							if banner['position'] == 'center':
								header_center_image = banner['banner_image']
								header_center_image_width = banner['width']
							if banner['position'] == 'right':
								header_right_image = banner['banner_image']
								header_right_image_width = banner['width']
						if banner['section'] == 'Footer':
							if banner['banner_image']:
								is_footer_banner_image = True
							if banner['position'] == 'left':
								footer_left_image = banner['banner_image']
								footer_left_image_width = banner['width']
							if banner['position'] == 'center':
								footer_center_image = banner['banner_image']
								footer_center_image_width = banner['width']
							if banner['position'] == 'right':
								footer_right_image = banner['banner_image']
								footer_right_image_width = banner['width']
			else:
				for attachment in template_misc_details["banner_header"]["attachment"]:
					banner_image, attachments = po_template_service.purchase_template_dao.getBannerAttachment(
								attachment_id=attachment["attachment_id"], enterprise_id=source.enterprise.id)
					banner_image_base64 = "data:image/%s;base64,%s" % (
								attachments.file_ext, banner_image.encode('base64')) if banner_image else ""
					if banner_image:
						is_header_banner_image = True
					if attachment["position"] == 'left':
						header_left_image = banner_image_base64
						header_left_image_width = attachment['width']
					if attachment["position"] == 'center':
						header_center_image = banner_image_base64
						header_center_image_width = attachment['width']
					if attachment["position"] == 'right':
						header_right_image = banner_image_base64
						header_right_image_width = attachment['width']
				for attachment in template_misc_details["banner_footer"]["attachment"]:
					banner_image, attachments = po_template_service.purchase_template_dao.getBannerAttachment(
								attachment_id=attachment["attachment_id"], enterprise_id=source.enterprise.id)
					banner_image_base64 = "data:image/%s;base64,%s" % (
								attachments.file_ext, banner_image.encode('base64')) if banner_image else ""
					if banner_image:
						is_footer_banner_image = True
					if attachment["position"] == 'left':
						footer_left_image = banner_image_base64
						footer_left_image_width = attachment['width']
					if attachment["position"] == 'center':
						footer_center_image = banner_image_base64
						footer_center_image_width = attachment['width']
					if attachment["position"] == 'right':
						footer_right_image = banner_image_base64
						footer_right_image_width = attachment['width']
			if not is_header_banner_image:
				header_image_height = 0
			if is_footer_banner_image:
				banner_margin = (int(footer_image_height) * 0.2645833333)
			else:
				banner_margin = 0
				footer_image_height = 0
			context = Context({
				'form_name': 'Order Acknowledgement', 'enterprise_logo': logo, 'enterprise_address': enterprise_address, 'source': source,
				'oa_no': source.getCode(), 'oa_reg_details': oa_registration_details, 'oa_date': oa_date,
				'delivery_due_date': delivery_due_date, 'oa_item_details': oa_item_details, 'total_quantity': total_quantity, 'total_value': total_value,
				'oa_taxes': oa_taxes, 'total_in_words': total_in_words, 'oa_approver': oa_approver, 'country_list': country_list,
				'header_left_image': header_left_image, 'header_center_image': header_center_image,
				'header_right_image': header_right_image, 'footer_left_image': footer_left_image,
				'footer_center_image': footer_center_image, 'footer_right_image': footer_right_image,
				'header_image_size': header_image_height, 'footer_image_size': footer_image_height,
				'header_left_image_width': header_left_image_width, 'header_center_image_width': header_center_image_width,
				'header_right_image_width': header_right_image_width, 'footer_left_image_width': footer_left_image_width,
				'footer_center_image_width': footer_center_image_width, 'footer_right_image_width': footer_right_image_width,
				'drafter_signature': drafter_signature, 'approver_signature': approver_signature,
				'appendix_pages': appendix_pages, OA_GENERAL_TEMPLATE: template_general_details,
				OA_HEADER_TEMPLATE: template_header_details,
				OA_ITEM_TEMPLATE: template_item_details, OA_SUMMARY_TEMPLATE: template_summary_details,
				OA_MISC_TEMPLATE: template_misc_details, 'hsn_summary': hsn_summary,
				'payment_terms': payment_terms, 'special_instructions': special_instructions, 'po_no': po_no, 'po_date': po_date,
				'se_no': se_no, 'se_date': se_date,
				'template_title': "Order Acknowledgement Document"})
			footer_data = get_template(
				getAbsolutePath('/templates/admin/print_template/oa/document/common/oa_template_footer.html')).render(
				context)
			footer_file_name = OA_DOC_FOOTER_PATH % source.id
			writeFile(footer_data, footer_file_name)
			page_title = "Preview" if doc_status == "PREVIEW" else source.getCode()
			bottom_margin = int(template_general_details["margin"]["bottom"]) + 30 + banner_margin
			if template_misc_details["print_pageno"]:
				options = {
					'page-size': 'A4', 'orientation': 'Portrait',
					'margin-top': '%smm' % template_general_details["margin"]["top"],
					'margin-right': '%smm' % template_general_details["margin"]["right"],
					'margin-bottom': '%smm' % bottom_margin,
					'margin-left': '%smm' % template_general_details["margin"]["left"], 'encoding': "UTF-8",
					'title': page_title,
					'--header-right': 'Page [page] of [topage]',
					'--header-font-size': '8',
					'footer-html': getAbsolutePath(footer_file_name)
				}
			else:
				options = {
					'page-size': 'A4', 'orientation': 'Portrait',
					'margin-top': '%smm' % template_general_details["margin"]["top"],
					'margin-right': '%smm' % template_general_details["margin"]["right"],
					'margin-bottom': '%smm' % bottom_margin,
					'margin-left': '%smm' % template_general_details["margin"]["left"], 'encoding': "UTF-8",
					'title': page_title,
					'footer-html': getAbsolutePath(footer_file_name)
				}
			css = [
				getAbsolutePath('/site_media/css/bootstrap.css'), getAbsolutePath('/site_media/css/invoice_template_preview.css'),
				getAbsolutePath('/site_media/css/invoice_template.css')]

			if not se_mail:
				template = Template(oa_template).render(context)
				data = pdfkit.from_string(template, False, options=options, css=css)
				return data
			else:
				template_src = getAbsolutePath(CLIENT_APPROVAL_MAIL)
				template = get_template(template_src).render(context)
				return template
		except Exception as e:
			logger.exception('Something went wrong while creating PDF self.canvas - %s' % e)


class InvoicePackingListPDFGenerator(PDFGenerator):
	"""
	Generates a PDF replica of the Invoice Packing List that will be available printed on a pre-printed sheet.
	"""
	def __init__(self, target_file_path=TARGET_PATH):
		super(InvoicePackingListPDFGenerator, self).__init__(target_file_path)

	def getInvoicePackingDetails(self, source=None):
		invoice_items = []
		i = 0
		db_session = SQLASession()
		for item in source.items:
			item_quantity = item.quantity
			unit_name = item.item.unit.unit_name
			if item.alternate_unit_id:
				scale_factor = helper.getScaleFactor(
					enterprise_id=item.enterprise_id, item_id=item.item_id,
					alternate_unit_id=item.alternate_unit_id)
				unit_name = helper.getUnitName(enterprise_id=item.enterprise_id, unit_id=item.alternate_unit_id)
				if scale_factor:
					item_quantity = item.quantity / scale_factor

			invoice_items.append({
				"item_id": item.item_id, "item_name": item.item.name, "qty": item_quantity, "unit": unit_name,
				"serial_numbers": []})

			item_serial_nos = db_session.query(MaterialSerialNo).join(
					InvoiceMaterialSerialNo, (MaterialSerialNo.serial_number == InvoiceMaterialSerialNo.serial_number)).join(
					InvoiceMaterial, and_(
						InvoiceMaterial.invoice_id == InvoiceMaterialSerialNo.invoice_id,
						MaterialSerialNo.item_id == InvoiceMaterial.item_id, MaterialSerialNo.make_id == InvoiceMaterial.make_id,
						MaterialSerialNo.is_faulty == InvoiceMaterial.is_faulty, )).filter(
					InvoiceMaterial.invoice_id == item.invoice_id, MaterialSerialNo.enterprise_id == item.enterprise_id,
					MaterialSerialNo.item_id == item.item_id, MaterialSerialNo.make_id == item.make_id,
					MaterialSerialNo.is_faulty == item.is_faulty)

			for s_no in item_serial_nos:
				invoice_items[i]["serial_numbers"].append(s_no.serial_number)
			i = i + 1
		return invoice_items

	def generatePDF(self, source=None):
		try:
			logger.info('Generating PDF for Invoice Packing List: %s' % source.id)
			if source.enterprise.images and source.enterprise.images.logo and source.enterprise.images.logo != "":
				logo = "data:image/%s;base64,%s" % (source.enterprise.images.ext, base64.encodestring(source.enterprise.images.logo))
			else:
				logo = ""

			if source.enterprise.address_2 and source.enterprise.address_2 != "":
				address = source.enterprise.address_1 + ", " + source.enterprise.address_2
			else:
				address = source.enterprise.address_1

			enterprise_address = "{address}, {city}, {pin_code} {state}".format(
				address=address, city=source.enterprise.city, pin_code=source.enterprise.pin_code, state=source.enterprise.state)
			issued_date = str(datetime.datetime.strptime(str(source.issued_on), '%Y-%m-%d %H:%M:%S')) if source.issued_on is not None and source.issued_on != '0000-00-00 00:00:00' else source.issued_on
			invoice_items = self.getInvoicePackingDetails(source=source)

			item_count = len(source.items)
			logger.info('No of Materials: %s' % item_count)

			context = Context({
				'form_name': 'Packing Slip', 'enterprise_logo': logo, 'enterprise_address': enterprise_address, 'source': source,
				'invoice_no': source.getCode(), 'invoice_items': invoice_items, 'issued_date': issued_date,
				'template_title': "Invoice Packing List Document"})

			options = {
				'page-size': 'A4', 'orientation': 'Portrait', 'title': source.getCode(), '--header-right': 'Page [page] of [topage]',
				'--header-font-size': '8'}

			css = [
				getAbsolutePath('/site_media/css/bootstrap.css'), getAbsolutePath('/site_media/css/invoice_template_preview.css'),
				getAbsolutePath('/site_media/css/invoice_template.css')]

			template_src = getAbsolutePath('/templates/sales/invoice_packing_list.html')

			template = get_template(template_src).render(context)
			data = pdfkit.from_string(template, False, options=options, css=css)
			return data
		except Exception as e:
			logger.exception('Something went wrong while creating PDF self.canvas - %s' % e)
