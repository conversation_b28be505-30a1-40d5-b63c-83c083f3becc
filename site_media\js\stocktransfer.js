	$(window).load(function(){
		$("#loading").hide();
		loadMaterial("onload");
		$("#cmdshow").hide();
		var transfer_id = $('#transfer_id').val();
		if(transfer_id == ''){
			$(".page-title").text('New Internal Stock Transfer');
			$(".header_current_page").addClass("hide");
		}
		else{
			var from_location = $('#id_stock_transfer_from').val();
			var to_location = $('#id_stock_transfer_to').val();
			var remarksText = $('#remarksLength').text();
			if(remarksText != '')
			{
			    var parsedData = JSON.parse(remarksText);
                if(parsedData.length > 0)
                {
                    $('.remarks_counter').text(parsedData.length);
                    $('.remarks_count').removeClass('disabled');
                }
            }
		}
	});
	function loadMaterial(loadType = "") {
        var dataToSend = {
            'type': "indents",
            'module': 'indents',
            'particulars':'indent_material'
        }
        var local_material_list  = JSON.parse(localStorage.getItem('goods_service_master_data'));
        if(loadType == "onchange" && local_material_list) {
            var material_choices = local_material_list[0].material;
        }
        else {
            var material_choices = generateMaterialAsAutoComplete(dataToSend);
        }
        var frequently_material_choices = generateFrequentlyMaterialAsAutoComplete(dataToSend);

        $("#materialrequired").materialAutocomplete({
            source: frequently_material_choices.concat(material_choices),
            minLength:2,
            response: function( event, ui ) {
                ui.content.unshift({value:"add_new_item", label:"+ ADD NEW MATERIAL ", category: ""});
            },
            open: function(event, ui) {
                $("ul.ui-autocomplete.ui-menu a:contains('+ ADD NEW MATERIAL')").closest('li').addClass("add_new_material_link hide");
            },
            select: function (event, ui) {
                event.preventDefault();
                if(ui.item.value != "add_new_item") {
                    var itemName = ui.item.label.replace("<span class='service-item-flag'></span>", '');
                    $("#material_id_hidden").val(ui.item.id);
                    $("#materialrequired").val(itemName).attr("readonly", true)
                    $("#material_id").val(ui.item.id);
                    $("#cat_code").val(ui.item.id);
                    if(ui.item.store_price){
                    $("#store_price").val(ui.item.store_price);
                     $("#alt_uom").val(ui.item.alt_uom);
                    }
                    else{
                    $("#store_price").val(0);
                    }
                    var is_faulty = 0;
                    if($("#chkfaulty").is(':checked')){
                        is_faulty = 1;
                    }



                    $('#id_ind_material-__prefix__-alternate_units').html("");
//                    if($('#is_multiple_units').val()=="True" ){
//                        if (ui.item.alt_uom > 0){
//                            loadAlternateUnits(ui.item.id, ui.item.unit, '#id_ind_material-__prefix__-alternate_units');
//                            $(".alternate_unit_select_box").removeClass("hide");
//                        }
//                        else {
//                            $(".alternate_unit_select_box").addClass("hide");
//                        }
//                    }
                    getStockQty(ui.item.id, ui.item.alt_uom, is_faulty)
                        .then(stock_qty => {
                             $('#closingStkQty').val(stock_qty);
                            $('#stkQty').removeClass('hide');
                            $('#availableQty').text(stock_qty);
                            $('#loading').css('display', 'none');
                        })
                        .catch(error => {
                            console.error("Error fetching stock quantity:", error);
                            // Handle the error here
                    });
//                    $(".unit_select_box").change(function() {
//                        var selectedDataVal = $(".unit_select_box option:selected").attr("data-val");
//                        $('#closingStkQty').val(parseInt(stockQty) * selectedDataVal);
//                        $('#stkQty').removeClass('hide');
//                        $('#availableQty').text(parseInt(stockQty) * selectedDataVal);
//                    });

                    $("#id_ind_material-__prefix__-drawing_no").val(ui.item.id);
                    $("#id_ind_material-__prefix__-is_service").val(ui.item.is_service);
                    $("#id_ind_material-__prefix__-item_id").val(ui.item.id);
                    $("#id_ind_material-__prefix__-description").val(ui.item.label);
                    $("#id_ind_material-__prefix__-enterprise_id").val($("#id_indent-enterprise_id").val());
                    $("#id_ind_material-__prefix__-units").val(ui.item.unit) ;
                    $("#id_ind_material-__prefix__-make_label").val(ui.item.mid);
                    $("#ind_unit_display").text(ui.item.unit);
                    var selected_unit_name = ui.item.unit
                    $('#unit_id option').filter(function() {
                      return $(this).text().trim() === selected_unit_name;
                    }).prop('selected', true);
                    $('#unit_id').trigger('change');
                    if (parseInt(ui.item.bom) > 0 ){
                        $("#cmdshow").show()
                    }
                    else{
                        $("#cmdshow").hide()
                    }
                    $(".material-removal-icon").removeClass('hide')
                    setTimeout(function(){
                        $("#id_ind_material-__prefix__-quantity").focus();
                    },250);
                }
                else {
                    openMaterialAddModal();
                }
            }
        }).on('focus', function() { $(this).keydown(); });
    }
    function remarks_show(){
		var remarksText = $('#remarksLength').text();
		var parsedData = JSON.parse(remarksText);
		remarkList = JSON.parse(remarksText);
        if (remarkList.length > 0) {
        	$('.countContainer').html(remarkList.length);
        	$('countContainer').removeClass("disabled");
            var history = '<div class="chat-container">';
            $.each(remarkList, function(i, remark) {
            	var sDate = moment(remark.date).format('MMM D, YYYY');
            	if(sDate == 'Invalid date') sDate =' ';
                history += '<div class="chat-list">';
                history += '<span class="chat-list-name">' + remark.by + '</span>';
                history += '<span class="chat-list-date">' + sDate + '</span>';
                history += '<span class="chat-list-description">' + remark.remarks + '</span>';
                history += '</div>';
            });
            history += '</div><BR/>';
            $('#remarks_list').html(history);
             $("#show_remarks_history_modal").modal('show');
        }
	}
	function showCatalogues() {
        var catqty = $("#id_ind_material-__prefix__-quantity").val();
        $.ajax({
            url: "/erp/stores/json/catalogue_materials/",
            type: "post",
            datatype: "json",
            data: {'cat_code': $("#cat_code").val(), "alternate_unit_id":$('#unit_id').val()},
            success: function (response) {
            if (response.response_code != 400) {
                $("#cattable").find("tr:gt(0)").remove();
                $("#catButton").find('.modal-footer').remove();
                $.each(response, function (i, item) {
                    var childArrow = "";
                    var makes = "-NA-";
                    var drawing_no = "";
                    var description = "";
                    var is_service_value = item.is_service==true?1:0;
                    var is_service = '<input hidden="hidden" name="catmaterial_is_service" value='+is_service_value+' >';
                    var itemTypeFlag = "";
                    if(item.is_service == true){
                        itemTypeFlag = `<span class="service-item-flag"></span>`;
                    }
                    if(!item.material_type && item.is_service != true) {
                        itemTypeFlag = `<span class="non_stock-flag"></span>`;
                    }
                    var item_name = item.name;
                    if (item.make_name !="" && item.make_name !=null) {
                        item_name += " [" + item.make_name +"]";
                    }
                    if(item.hasChildren) {
                        childArrow = "<i class='fa fa-plus fa-for-arrow' role='button' onclick='return appendMaterial(\""+ item.item_id +"\", \""+ sessionStorage.clickcount +"\", \""+item.item_id+ "_" + sessionStorage.clickcount +"\")'><a style='padding-left: 26px; display: block; margin-top: -12px;' >"+item_name+" "+itemTypeFlag+"</a></i>";
                    } else { childArrow = item_name+" "+itemTypeFlag }

                    if (item.makes.length > 0) {
                        makes = "<select class='form-control' name='make' id='txtmake" + i + "'>";
                        for (j = 0; j <= item.makes.length - 1 ; j++) {
                            makes = `${makes}<option value="${item.makes[j].make_id}" data-quantity="${item.makes[j].stock}">${item.makes[j].make_name}</option>`;
                        }
                        makes += "</select>";
                    }
                    description = item.name;
                    if (item.drawing_no !="" && item.drawing_no!=null) {
                        drawing_no = item.drawing_no;
                        description += " - " + item.drawing_no;
                    }
                    var is_service_value = item.is_service==true?1:0;
                    var row = "<tr data-toggle='close' data-padding='0' data-parent='" + item.item_id + "_" + sessionStorage.clickcount + "' id='" + item.item_id + "_" + sessionStorage.clickcount + "' bgcolor='#ececec' border='1' align='left' style='font-size:12px; font-weight:normal;'><td hidden='hidden'>" +
                        description + "</td><td class='text-left bom-sno'>" +
                        (i + 1) + "</td><td>" + childArrow + "</td><td>" +
                        drawing_no + "</td><td hidden='hidden'>" + makes + "</td><td>" +
                        "<input type='text' name ='catmaterial' value='" + (item.quantity * catqty).toFixed(2) + "' class='form-control setUnitValue text-right' id='txtcatqty' maxlength='16' onblur='calculateItemAmount(),calculateTotalAmount()' onfocus='setNumberRangeOnFocus(this,12,3)' /></td><td>" +
                        "<input type='text' name ='catmaterial' value='" + (item.price).toFixed(2) + "' class='form-control text-right' id='unitPrice' maxlength='16' onblur='calculateItemAmount(),calculateTotalAmount()' onfocus='setNumberRangeOnFocus(this,12,3)' /></td><td id='amountPrice_" + i + "'>" +
                        (item.quantity * catqty * item.price).toFixed(2) + "</td><td>" +
                        item.unit_name + "</td><td hidden='hidden'>" + item.unit_id + "</td><td class='text-center'>" +
                        "<a href='#' onclick='deleteCatRow(this)'><i class='fa fa-trash-o'></i></a>" + "</td><td hidden='hidden'>" + drawing_no + "</td><td hidden='hidden'><input type='text' name ='catmaterial_item_id' value='" + item.item_id + "' ></td><td hidden='hidden'><input type='text' name ='catmaterial_is_service' value='" + is_service_value + "' ></td><td hidden='hidden'><input type='text' name ='make_name' value='" + item.make_name + "' ></td><td hidden='hidden'><input type='text' name ='stock_qty' value='" + item.stock_qty + "' ></td></tr>";
                    $('#cattable').append(row).addClass('tbl');
                });
                    var row = "<div class='modal-footer'><span class='pull-left nonStock-text'>  <span class='nonStock-text non_stock-flag'></span> - NON-STOCKABLE</span><span class='pull-left nonStock-text'>  <span class='nonStock-text service-item-flag'></span> - SERVICE</span><input type='button' onclick='addStockTransfer()' class='btn btn-save' id='catAdd' value='Add' /><a onclick='closeCatalogues()' class='btn btn-cancel' id='catCancel'>Close</a></div>"
                    $('#catButton').append(row).addClass('tbl');
                    $("#catalogueModal").modal('show');
                    $("#loadingmessage_changelog_listing_ie").hide();
            }
            else {
                // TODO why should I login again here
                swal(response.response_message, "Kindly <a href='/erp/login/'>Login</a> again.", "warning");
                document.location.href="/erp/login/";
                return;
            }
            $(".setUnitValue").blur(function(){
                var setID = $(this).closest('tr').attr('id');
                var setValue = $(this).val();
                $('#cattable').find("tr[data-child=\""+setID+"\"]").each(function(){
                    var setUnitValue = $(this).find('.setUnitValue').data('unitvalue');
                    $(this).find('.setUnitValue').val(Number(setValue * setUnitValue));
                });
            });
            }
        });
    }
    function addStockTransfer() {
        var materialtable = document.getElementById("stockTransferMaterial");
        var match =false;
        var rowIndexStockTransferTable = [];
        var rowIndexCattable = [];

        $('#cattable tbody tr').each(function(index, row) {
            $('#stockTransferMaterial tbody.item-for-goods tr').each(function(index1, row1) {
                var existingValue = $(row1).find('td:eq(2)').text().trim();
                var name = "catmaterial_item_id";
                var catmaterial_item_id =  $(row).find('td input[name="' + name + '"]').val();
                if (existingValue === catmaterial_item_id) {
                    rowIndexStockTransferTable =index1;
                    rowIndexCattable =index;
                    match = true;
                    return false;
                }
                else
                {
                match = false;
                }
            });
            if(match == false &&  $(row).data('toggle') === "close"){
                var serialNumber = materialtable.rows.length - 1;
                const makeText = $(row).find('td input[name="make_name"]').val();
                var materialName = $(row).find('td:eq(0)').text();
                if(makeText != ''){
                    var materialrequired = materialName +" " + '[' + makeText + ']'
                }
                else{
                    materialrequired = materialName;
                }
                if( $(row).find('td input[name="catmaterial_is_service"]').val() == 1 ){
                    serviceFlsg= "<span class='service-item-flag'></span>";
                }
                else{
                    serviceFlsg = "";
                }
                var newQtyField = $(row).find('td input#txtcatqty').val();
                var unitText = $(row).find('td:eq(8)').text();
                var unitId = $(row).find('td:eq(9)').text();
                var unit_rate = $(row).find('#unitPrice').val();
                var Amount = $('#amountPrice_'+index).val();
                var name = "catmaterial_item_id";
                var catmaterial_item_id =  $(row).find('td input[name="' + name + '"]').val();
                var load_received_qty = $('#load_received_qty').is(':checked');
                var load_stock_qty = $('#load_stock_qty').is(':checked');
                var stock_qty =  $(row).find('td input[name="stock_qty"]').val();
                is_faulty =0;
                if($("#chkfaulty").is(':checked')){
                    is_faulty =1;
                }
                $("#stockTransferMaterial tbody.item-for-goods").append("<tr><td class='text-center'>" + serialNumber + ".</td><td class='text-center item-name'>" + materialrequired + serviceFlsg + "</td><td hidden='hidden' id='table_material_id' class='exclude_export'>" + catmaterial_item_id + "</td><td class='td_textbox_right exclude_export'><input type='text' id='stockTransfer_qty' class='form-control stockTransfer_qty' onblur='calculateItemAmount(),calculateTotalAmount()' onfocus='setNumberRangeOnFocus(this,12,3)' value='" + newQtyField + "'><label style='display: none;float:right'>Max issue:<span id='closing_qty'></span> </label></td><td hidden='hidden'>" + newQtyField + "</td><td class='text-right exclude_export'><input type='text' id='unit_price' class='form-control text-right' onchange='calculateItemAmount(),calculateTotalAmount()' onfocus='setNumberRangeOnFocus(this,12,3)' value='" + unit_rate + "'>" + "</td><td hidden='hidden'>" + unit_rate + "</td><td class='text-right rec_amount'  id='rec_amount_" + serialNumber + "'>" + Amount + "</td><td hidden='hidden' class='exclude_export unit_id'>" + unit_id +"</td><td class='text-center' id='unit_name'>"+ unitText +"</td><td class='text-center exclude_export'><a href='#'><i class='fa fa-trash-o delete-row-btn'></i></a></td><td class='hide text-center exclude_export' id='is_faulty'>"+ is_faulty +"</td></tr>");
                calculateItemAmount();
                calculateTotalAmount();
            }
            else if(match == true &&  $(row).data('toggle') === "close"){

                var stockTransferMaterialValue = Number($('#stockTransferMaterial tbody tr:eq(' + rowIndexStockTransferTable + ')').find('td input#stockTransfer_qty').val());
                var cattableValue = Number($('#cattable tbody tr:eq(' + index + ')').find('td input#txtcatqty').val());
                var newValue = stockTransferMaterialValue + cattableValue;
                $('#stockTransferMaterial tbody tr:eq(' + rowIndexStockTransferTable + ')').find('td input#stockTransfer_qty').val(newValue.toFixed(2));
                calculateItemAmount();
                calculateTotalAmount();

            }
            $("#catalogueModal").modal('hide');
            $('#materialrequired, #id_ind_material-__prefix__-quantity').val('');
            $("#ind_unit_display").html('&nbsp;');
            $('#material_id_hidden').val('');
            $('#materialr_id').val('');
            $("#cmdshow").hide();
            $(".material-removal-icon").click();

         });
    }
    function closeCatalogues() {
        $("#catalogueModal").modal('hide');
    }
    function deleteCatRow(currentRow) {
        try {
            if (window.confirm('Do you want delete this row?')) {
                var table = document.getElementById("cattable");
                var rowCount = table.rows.length;
                for (var i = 0; i < rowCount; i++) {
                    var row = table.rows[i];
                    if (row == currentRow.parentNode.parentNode) {
                        if (rowCount <= 1) {
                            alert("Cannot delete all the rows.");
                            break;
                        }
                        table.deleteRow(i);
                        rowCount--;
                        i--;
                    }
                }
            }
        }
        catch (e) {
            alert(e);
        }
    }
    function appendMaterial(cat_code, dataChild, dataP) {
        var currentCatChild = $("#"+cat_code + "_" + dataChild).attr('id');
        var current_bom_sno = $("#"+cat_code + "_" + dataChild).find(".bom-sno").text().trim();
        var constructedRow = '';
        if($("#"+currentCatChild).attr('data-toggle') == 'open') {
            var curPadding = $("#"+currentCatChild).attr('data-padding');
            $("#"+currentCatChild).nextAll('tr').each(function(){
                if(Number($(this).attr('data-padding')) > Number(curPadding)) {
                    $(this).remove();
                }
                else {
                    return false;
                }
            });
            $("#cattable #"+cat_code + "_" + dataChild).find('.fa.fa-for-arrow').removeClass('fa-minus').addClass('fa-plus');
            $("#cattable #"+cat_code + "_" + dataChild).attr('data-toggle','close');
        }
        else {
            $("#cattable #"+cat_code + "_" + dataChild).find('.fa.fa-for-arrow').removeClass('fa-plus').addClass('fa-minus');
            $("#cattable #"+cat_code + "_" + dataChild).attr('data-toggle','open');
            $.ajax({
                url: "/erp/stores/json/catalogue_materials/",
                type: "post",
                dataType: "json",
                data: {'cat_code': cat_code},
                success: function (response) {
                    var catqty = $("#id_ind_material-__prefix__-quantity").val();
                    $.each(response, function (i, item) {
                    if(typeof(Storage) !== "undefined") {
                        if (sessionStorage.clickcount) {
                            sessionStorage.clickcount = Number(sessionStorage.clickcount)+1;
                        } else {
                            sessionStorage.clickcount = 1;
                        }
                    }
                    var childArrow ="";
                    var makes = "-NA-";
                    var dataParent = dataP;
                    var dataPadding = Number($("#"+cat_code + "_" + dataChild).data('padding') + 30);
                    var drawing_no = "";
                    var isStockableIcon = "";
                    var isServiceIcon = "";
                    var item_description = item.name;
                    var is_service = '<input hidden="hidden" name="catmaterial_is_service" value='+item.is_service+' >';
                    if (item.drawing_no !="" && item.drawing_no!=null) {
                        drawing_no = item.drawing_no;
                    }
                    if (item.make_name != ""){
                        item_description += " [" + item.make_name + "]";
                    }

                    if(item.is_service == true){
                        isServiceIcon = `<span class="service-item-flag"></span>`;
                    }
                    if(!item.material_type && !item.is_service){
                        isStockableIcon = `<span class="non_stock-flag"></span>`;
                    }
                    if(item.hasChildren) {
                        childArrow = "<i class='fa fa-plus fa-for-arrow' role='button' onclick='return appendMaterial(\""+ item.item_id +"\", \""+ sessionStorage.clickcount +"\", \""+ dataParent +"\")'><a style='padding-left: 26px; display: block; margin-top: -12px;'>"+item_description+" "+isServiceIcon+" "+isStockableIcon+"</a></i>";
                    } else {
                        childArrow = item_description +" "+isServiceIcon+" "+isStockableIcon;
                    }
                    if (item.makes.length > 0){
                        makes = "<select class='form-control' name='make' id='txtmake" + i + "'>";
                        for (j = 0; j <= item.makes.length - 1 ; j++) {
                            makes = `${makes}<option value="${item.makes[j].make_id}" data-quantity="${item.makes[j].stock}">${item.makes[j].make_name}</option>`;
                        }
                        makes += "</select>";
                    }
                    if(item.drawing_no != null){
                        var item_name = item.name + " - " + item.drawing_no
                    }else{
                        var item_name = item.name
                    }
                    var is_service_value = item.is_service==true?1:0;
                    var row = "<tr data-toggle='close' data-padding='"+dataPadding+"' data-parent='"+dataParent+"' id=\""+ item.item_id + "_" + sessionStorage.clickcount +"\" data-child=\""+ cat_code + "_" +dataChild +"\" bgcolor='#ececec' border='1' align='left' style='font-size:12px; font-weight:normal;'><td hidden=hidden>" +
                            item_name + "</td><td class='text-left bom-sno'>" +
                           current_bom_sno +"."+(i+1)+ "</td><td style='padding-left:"+Number(dataPadding-20)+"px'><span style='padding-left:30px; display: block;' class='tree-view'>"+childArrow+"</span></td><td>" +
                           drawing_no + "</td><td hidden='hidden'>" +
                           makes + "</td><td>" +
                           "<input type='text' id='txtcatqty' name ='catmaterial' class='form-control text-right setUnitValue' maxlength='16' onblur='calculateItemAmount(),calculateTotalAmount()' value='" + (item.quantity * catqty).toFixed(2) + "' data-unitvalue='"+item.quantity+"' /></td><td>" +
                           "<input type='text' name ='catmaterialPrice' value='" + item.price + "' class='form-control text-right' id='unitPrice' maxlength='16' onblur='calculateItemAmount(),calculateTotalAmount()' /></td><td id='amountPrice_" + i + "'>" + (item.quantity * catqty * item.price).toFixed(2) +"</td><td>" +
                           item.unit_name + "</td><td hidden='hidden'>" +
                            item.unit_id + "</td><td class='text-center'>" +
                           "<a href='#' onclick='deleteCatRow(this)'><i class='fa fa-trash-o'></i></a>" + "</td><td hidden=hidden>" +
                           item.drawing_no + "</td><td hidden=hidden><input type='text' name ='catmaterial_item_id' value='" + item.item_id + "' ></td><td hidden=hidden><input type='text' name ='catmaterial_is_service' value='" + is_service_value + "' ></td><td hidden=hidden><input type='text' name ='make_name' value='" + item.make_name + "' ></td><td hidden=hidden><input type='text' name ='stock_qty' value='" + item.stock_qty + "' ></td></tr>";

                     constructedRow = constructedRow+row
                    });
                    $('#cattable #'+cat_code + "_" + dataChild).after(constructedRow);
                    $("#"+currentCatChild).find('.setUnitValue').focus();
                    setTimeout(function(){
                        $("#"+currentCatChild).find('.setUnitValue').blur();
                    },500);

                    $(".setUnitValue").blur(function(){
                        var setID = $(this).closest('tr').attr('id');
                        var setValue = $(this).val();
                        $('#cattable').find("tr[data-child=\""+setID+"\"]").each(function(){
                            var setUnitValue = $(this).find('.setUnitValue').data('unitvalue');
                            $(this).find('.setUnitValue').val(Number(setValue * setUnitValue));
                            $(this).find("[id^='amountPrice_']").text($(this).find("#txtcatqty").val() * $(this).find("#unitPrice").val());
                        });
                    });
                }
            });
        }
    }
    $(document).ready(function(){
        $("#chkfaulty").change(function() {
            var is_faulty = $(this).is(':checked') ? 1 : 0;
            getStockQty($('#material_id_hidden').val(), $('#alt_uom').val(), is_faulty)
                .then(stock_qty => {
                    $('#closingStkQty').val(stock_qty);
                    $('#stkQty').removeClass('hide');
                    $('#availableQty').text(stock_qty);
                    $('#loading').css('display', 'none');
                    // Handle the stock_qty here
                })
                .catch(error => {
                    console.error("Error fetching stock quantity:", error);
                    // Handle the error here
            });


        });
        materialListBlurEvent('materialrequired');
        $("#add_new_stockTransfer").click(function(){
            $(".error-border").removeClass('error-border');
            $(".custom-error-message").remove();

            var ControlCollections = [
                {
                    controltype: 'textbox',
                    controlid: 'materialrequired',
                    isrequired: true,
                    errormsg: 'Material Name is required.'
                },

                {
                    controltype: 'textbox',
                    controlid: 'id_ind_material-__prefix__-quantity',
                    isrequired: true,
                    errormsg: 'Quantity is required.',
                    mindigit: 0.01,
                    mindigiterrormsg: 'Quantity cannot be 0.'
                }
            ];
            var result = JSCustomValidator.JSvalidate(ControlCollections);
            if(result){
                var newQtyField = document.getElementById('id_ind_material-__prefix__-quantity');
                var closingQty =  parseInt($('#closingStkQty').val());
                if ($('#material_id').val()!="" && newQtyField.value != "" && newQtyField.value >0 && closingQty >= newQtyField.value ) {
                    var materialtable = document.getElementById("stockTransferMaterial");
                    var materialrowCount = materialtable.rows.length;
                    var materialrequired = $('#materialrequired').val();
                    var is_faulty = 0;
                    if($("#chkfaulty").is(':checked')){
                        materialrequired += "[Faulty]";
                        is_faulty = 1;
                    }
                    var serialNumber = materialtable.rows.length - 1;
                    var selectedText = $("#id_ind_material-__prefix__-alternate_units option:selected").text();
                    var unitText = selectedText.trim() === "" ? $("#ind_unit_display").text() : selectedText;
                    var unit_id = $('#unit_id').val();
                    var alt_uom = $('#alt_uom').val();
                    var unit_rate = $('#store_price').val();
                    var unit_name = $('#unitName').val();
                    var amount = unit_rate * newQtyField.value
                    var material_id = $("#material_id_hidden").val();
                    var load_received_qty = $('#load_received_qty').is(':checked');
                    var load_stock_qty = $('#load_stock_qty').is(':checked');
                    var match = false;
                    var stock_qty = 0;
                    var rowIndex = 0;
                     if($('#id_ind_material-__prefix__-is_service').val() == 1){
                            var serviceFlsg= "<span class='service-item-flag'></span>";
                     }
                     else{
                        var serviceFlsg = "";
                     }
                    $('#stockTransferMaterial tbody tr').each(function(index, row) {
                        var existingValue = $(row).find('td:eq(2)').text().trim();
                        if (existingValue === material_id) {
                            rowIndex = index;
                            match = true;
                            return false;
                        }
                        return rowIndex;
                    });
                    if(match == false)
                        {
                            $('#loading').show();
                            $("#stockTransferMaterial tbody.item-for-goods").append("<tr><td class='text-center'>" + serialNumber + ".</td><td class='text-center item-name'>" + materialrequired + serviceFlsg + "</td><td hidden='hidden' id='table_material_id' class='exclude_export'>" + material_id + "</td><td hidden='hidden'>" + newQtyField.value + "</td><td class='td_textbox_right exclude_export'><input type='text' id='stockTransfer_qty' class='form-control stockTransfer_qty' onblur='calculateItemAmount(),calculateTotalAmount()' onfocus='setNumberRangeOnFocus(this,12,3)' value='" + newQtyField.value + "'><label style='display: none;float:right'>Max issue:<span id='closing_qty'></span> </label></td><td hidden='hidden'>" + unit_rate + "</td><td class='text-right exclude_export'><input type='text' id='unit_price' class='form-control text-right' onchange='calculateItemAmount(),calculateTotalAmount()' onfocus='setNumberRangeOnFocus(this,12,3)' value='" + unit_rate + "'>" + "</td><td class='text-right rec_amount'  id='rec_amount_" + serialNumber + "'>" + amount + "</td><td class='text-center' id='unit_name'>"+ unitText +"</td><td hidden='hidden' class='exclude_export alt_uom'>" + alt_uom +"</td><td hidden='hidden' class='exclude_export alt_uom'>" + alt_uom +"</td><td class='text-center exclude_export'><a href='#'><i class='fa fa-trash-o delete-row-btn'></i></a></td><td class='hide text-center exclude_export' id='is_faulty'>"+ is_faulty +"</td></tr>");
                            $("#materialrequired").val("");
                            $("#materialrequired").removeAttr("readonly");
                            $(".material-removal-icon").addClass("hide");
                            $('#id_ind_material-__prefix__-quantity').val("");
                            $('#loading').hide();
                            $('#stkQty').addClass('hide');
                            calculateItemAmount()
                            calculateTotalAmount();
                    }
                    else {
                        var existingQty  = $('#id_ind_material-__prefix__-quantity');
                        swal({
                            title: "Material already Exists!",
                            text: "Do you still want to add its Quantity?",
                            type: "warning",
                            showCancelButton: true,
                            confirmButtonColor: "#209be1",
                            confirmButtonText: "Yes, do it!",
                            closeOnConfirm: true,
                            closeOnCancel: true
                            },
                            function(isConfirm) {
                                if (isConfirm) {
                                    var newValue = Number($('#stockTransferMaterial tbody tr:eq(' + rowIndex + ')').find('td input').val()) + Number(existingQty.val());
                                    $('#stockTransferMaterial tbody tr:eq(' + rowIndex + ')').find('td input#stockTransfer_qty').val(newValue.toFixed(2));
                                    $("#materialrequired").val("");
                                    $("#materialrequired").removeAttr("readonly");
                                    $(".material-removal-icon").addClass("hide");
                                    existingQty.val("");
                                    calculateTotalAmount();
                                     calculateItemAmount()
                                } else {
                                    // Do nothing, just close the confirmation dialog
                                }
                            }
                        );
                    }
                }
                else if (closingQty < newQtyField.value) {
                    swal("", "Quantity should not be greater than closing stock qty (" + closingQty + ")", "warning");
                }
            }
	    });
	    $('#edit_catalogue_form').submit(function() {
       $.ajax({
           data: $(this).serialize(),
           type: $(this).attr('method'),
           url: $(this).attr('action'),
           success: function(response) {
               var item_name = ""
               $('#loading').hide();
               $("#material_id_hidden").val(response['item_id']);
               $("#materialrequired").val(response['name']).attr("readonly","readonly");
               $("#materialrequired").closest("div").find(".material-removal-icon").removeClass("hide");
               $("#material_id").val(response['item_id']);
               $("#id_ind_material-__prefix__-drawing_no").val(response['drawing_no']);
               $("#id_ind_material-__prefix__-is_service").val(response['is_service']);
               item_name = response['name']
               if(response['drawing_no'] !="" && response['drawing_no'] != null) {
                   item_name +=  " - "  + response['drawing_no']
               }
               if(response['is_service'] == 1){
                   item_name += `<span class="service-item-flag"></span>`;
               }
               $("#id_ind_material-__prefix__-description").val(item_name);
               $("#id_ind_material-__prefix__-enterprise_id").val(response['enterprise_id']);
               $("#id_ind_material-__prefix__-item_id").val(response['item_id']);
               $('#id_ind_material-__prefix__-alternate_units').html("");
               if($('#is_multiple_units').val()=="True" ){
                   if (response['alternate_unit_count'] > 0){
                       loadAlternateUnits(response['item_id'], response['unit_name'], '#id_ind_material-__prefix__-alternate_units');
                       $(".alternate_unit_select_box").removeClass("hide");
                   }
                   else {
                       $(".alternate_unit_select_box").addClass("hide");
                   }
               }
               $("#id_ind_material-__prefix__-units").val(response['unit_name']) ;
               $("#ind_unit_display").text(response['unit_name']);
               //loadMakeList();
               if(!$('#add_material_modal').hasClass('in')) {
                   $("#add_new_ind_material").trigger("click");
               }
               $('#add_material_modal').modal('hide')
               setTimeout(function(){
                   $("#id_ind_material-__prefix__-quantity").focus();
               },250);

           }
       });
       return false;
   });


 	$('#cmdshow').click(function () {
        if ($('#materialrequired').val()=="") {
            swal("","Please Select Material","warning");
            return;
        }
        var newQtyField = document.getElementById('id_ind_material-__prefix__-quantity');
        if (newQtyField.value == "" || newQtyField.value =="0.00") {
            swal("","Please Enter Qty","warning");
            return;
        }
        else {

            $("#loadingmessage_changelog_listing_ie").show();
            showCatalogues();
        }
    });
    $("#stockTransferMaterial").on("click", ".delete-row-btn", function () {
        var row = $(this).closest("tr");
        swal({
            title: "Are you sure?",
            text: "Do you want to delete this Material",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#209be1",
            confirmButtonText: "Yes, delete it!",
            closeOnConfirm: true
        },
        function(){
            row.remove();
            $("#stockTransferMaterial tbody.item-for-goods tr:visible, #stockTransferMaterial tbody.item-for-service tr:visible").each(function(i) {
               $(this).find('td:eq(0)').text(i+1+".")
            })
            calculateItemAmount();
            calculateTotalAmount();
        });
    });

    $("#save_stockTransfer_button").click(function(){
        var transfer_id = $('#transfer_id').val();

        var ControlCollections = [
           {
                controltype: 'dropdown',
                controlid: 'id_stock_transfer_from',
                isrequired: true,
                errormsg: 'From Location is required.'
            },
            {
                controltype: 'dropdown',
                controlid: 'id_stock_transfer_to',
                isrequired: true,
                errormsg: 'To Location is required.'
            }
        ];
        var result = JSCustomValidator.JSvalidate(ControlCollections);
        if (result) {
            var item_count = $("#stockTransferMaterial tbody.item-for-goods tr").length;
            if (item_count == 0) {
                swal('', 'Please add at least one material for request', 'error');
                return; // Exit early if no materials
            }

            var mat_list = [];
            $('#stockTransferMaterial tbody.item-for-goods tr').each(function() {
                var item_id = $(this).find('#table_material_id').text();
                var alt_uom = $(this).find('.alt_uom').val();
                var is_faulty = $(this).find('#is_faulty').text();
                var item = {
                    "item_id": item_id,
                    "make_id": '',
                    "is_faulty": is_faulty,
                    "invoice_id": "",
                    "alternate_unit_id": 0
                };
                mat_list.push(JSON.stringify(item));
            });
            $('#loading').css('display', 'block');
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: "/erp/stores/json/issue/closingstock/",
                    type: "POST",
                    dataType: "json",
                    data: {
                        "mat_list[]": mat_list,
                        "issued_on": null,
                        "location_id": $('#id_stock_transfer_from option:selected').val()
                    },
                    success: function(response) {
                        $('#loading').show();
                        var closing_stock_data = response['closing_stock'];
                        var stock_issue = false;
                        var stock_issue_items = [];

                        $('#stockTransferMaterial tbody.item-for-goods tr').each(function() {
                            var item_id = $(this).find('#table_material_id').text();
                            var transfer_qty = parseFloat($(this).find('#stockTransfer_qty').val());
                            var itemName = $(this).find('.item-name').text();

                                var closing_stock = closing_stock_data.find(stock => stock.item_id == item_id) ? closing_stock_data.find(stock => stock.item_id == item_id) : 0;
                                var stock_qty = closing_stock ? closing_stock.closing_qty : 0;

                            if (transfer_qty > stock_qty) {
                                stock_issue = true;
                                stock_issue_items.push(itemName);
                                $(this).find('#stockTransfer_qty').css('border', '2px solid red');
                                $(this).find('label').css('display', 'block');
                                $(this).find('#closing_qty').text(stock_qty);
                            } else {
                                $(this).find('#stockTransfer_qty').css('border', '');
                            }
                        });

                        if (stock_issue) {
                            swal('', "Stock issue with items: " + stock_issue_items.join(', '), 'error');
                        } else {
                            var faulty = $("#chkfaulty").is(':checked') ? 1 : 0;
                            var transferCode = $("#transfer_code").text().trim();
                            var fromLocation = $("#id_stock_transfer_from").val();
                            var toLocation = $("#id_stock_transfer_to").val();
                            var materials = [];

                            $("#stockTransferMaterial tbody.item-for-goods tr").not('#total').each(function() {
                                var row = $(this);
                                var unitRate = row.find("#unit_price").val();
                                var material = {
                                    item_id: parseInt(row.find("td:eq(2)").text().trim()),
                                    quantity: row.find('#stockTransfer_qty').val(),
                                    rate: unitRate,
                                    unit: row.find('#unit_name').text(),
                                    is_faulty: faulty
                                };
                                materials.push(material);
                            });

                            var total_qty = 0;
                            $('.stockTransfer_qty').each(function() {
                                var value = parseFloat($(this).val());
                                if (!isNaN(value)) {
                                    total_qty += value;
                                }
                            });

                            var statusValue = 1; // Default status
                            if ($('#status').val() === 'Approved') {
                                statusValue = 2;
                            } else if ($('#status').val() === 'Received') {
                                statusValue = 3;
                            } else if ($('#status').val() === 'Reject') {
                                statusValue = 3;
                            }

                            var remarks = $("#id_transfer-remarks").val().trim();
                            var jsonRemarksData = remarks ? JSON.stringify(remarks) : null;

                            var stockTransferData = {
                                "enterprise_id": $('#enterprise_id').val(),
                                "total_qty": total_qty,
                                "total_value": $('#total_amount').text(),
                                "last_modified_by": $('#login_user_id').val(),
                                "from_location": $('#id_stock_transfer_from').val(),
                                "from_location_name": $('#id_stock_transfer_from').val(),
                                "to_location": $('#id_stock_transfer_to').val(),
                                "status": statusValue,
                                "created_by": $('#login_user_id').val(),
                                "transfer_details": JSON.stringify(materials),
                                "remarks": jsonRemarksData,
                                "id": $('#transfer_id').val(),
                                "pp_id": $('#pp_id').val(),
                            };

                            if (fromLocation === toLocation) {
                                swal('', 'Stock Transfer from location and To location cannot be the same. Please select another location.', 'error');
                            } else {
                                $.ajax({
                                    url: "/erp/stores/json/stock_transfer/",
                                    type: "post",
                                    datatype: "json",
                                    data: stockTransferData,
                                    success: function(response) {
                                        if (response.response_code == 200) {
                                            var text_message = ($('#transfer_id').val() == "") ?
                                                "Internal Stock Transfer Request has been created successfully." :
                                                "Internal Stock Transfer Request has been modified successfully.";
                                            swal({
                                                    title: "",
                                                    text: text_message,
                                                    type: "success"
                                                },
                                                function() {
                                                    var enterpriseId = $('#enterprise_id').val();
                                                    var userId = $('#login_user_id').val();
                                                    var url = "/erp/stores/json/stock_transfer_list/?enterprise_id=" +
                                                        encodeURIComponent(enterpriseId) + "&user_id=" +
                                                        encodeURIComponent(userId);
                                                    window.location.href = url;
                                                });
                                        } else {
                                            swal({
                                                title: "",
                                                text: "Failed to save Material Requisition",
                                                type: "error"
                                            });
                                        }
                                    }
                                });
                            }

                            $("html, body").animate({ scrollTop: 0 }, "fast");
                        }

                        $('#loading').css('display', 'none');
                    },
                    error: function(error) {
                        console.error("Error fetching stock quantity:", error);
                    }
                });
            });
        }
	});
	$(".material-removal-icon").click(function(){
		$("#cmdshow").hide();
		$('#materialrequired').removeAttr("readonly").focus();
		$(this).addClass("hide");
		$('#materialrequired').val('');
		$('#description_display').html('');
        $(".alternate_unit_select_box, .all_units_select_box").addClass("hide");
        $('#stkQty').addClass('hide');
	});
});

function getStockQty(item_id, alternate_unit_id, is_faulty) {
    $('#loading').css('display', 'block');
    var item_json = {
        "item_id": item_id,
        "make_id": 1,
        "is_faulty": is_faulty,
        "invoice_id": "",
        "issued_on": null,
        "alternate_unit_id": 0,
        "location_id": $('#id_stock_transfer_from').val()
    };

    return new Promise((resolve, reject) => {
        $.ajax({
            url: "/erp/stores/json/issue/closingstock/",
            type: "POST",
            dataType: "json",
            data: item_json,
            success: function(response) {
                var stock_qty = 0;
                $.each(response['closing_stock'], function(i, item) {
                    stock_qty = item['closing_qty'];
                });
                resolve(stock_qty); // Resolve the promise with stock_qty
            },
            error: function(xhr, errmsg, err) {
                console.warn(xhr.status + ": " + xhr.responseText);
                reject(errmsg); // Reject the promise with error message
            }
        });
    });
    $('#loading').css('display', 'none');
}

function calculateTotalAmount() {
    var total = 0;
    $("#stockTransferMaterial tbody.item-for-goods tr:not(#total)").each(function(index, row) {
       var qty = parseFloat($(row).find("#stockTransfer_qty").val());
        var rate = parseFloat($(row).find("#unit_price").val());
        var amount = qty * rate;
        total += amount;
    });
    $("#total_amount").text(total.toFixed(2));
}

function calculateItemAmount(){
    $('#cattable tbody tr').each(function(index, row) {
        var qty = parseFloat($(row).find("#txtcatqty").val());
        var rate = parseFloat($(row).find("#unitPrice").val());

        if (!isNaN(qty) && !isNaN(rate)) {
            var amount = qty * rate;
            $(row).find('[id^="amountPrice_"]').text(amount);
        } else {
            $(row).find('[id^="amountPrice_"]').text("0.00");
        }
    });
   $("#stockTransferMaterial tbody.item-for-goods tr:not(#total)").each(function(index, row) {
        var qty = parseFloat($(row).find("#stockTransfer_qty").val());
        var rate = parseFloat($(row).find("#unit_price").val());

        if (!isNaN(qty) && !isNaN(rate)) {
            var amount = qty * rate;
            $(row).find("#rec_amount_" + (index + 1)).text(amount);
        } else {
            $(row).find("#rec_amount_" + (index + 1)).text("0.00");
        }
    });
}
