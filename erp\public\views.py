"""
"""
from django.template.response import TemplateResponse

from settings import CURRENT_VERSION

__author__ = 'nandha'


def terms(request):
	"""

	Terms and conditions
	:param request:
	:return:
	"""

	return TemplateResponse(request=request, template='public/terms.html', context={})


def privacy(request):
	"""

	Terms and conditions
	:param request:
	:return:
	"""

	return TemplateResponse(request=request, template='public/privacy.html', context={})


def release(request):
	"""

	Terms and conditions
	:param request:
	:return:
	"""

	return TemplateResponse(request=request, template='public/release_notes.html', context={})


def contact(request):
	"""

	Contact
	:param request:
	:return:
	"""

	return TemplateResponse(request=request, template='public/contact.html', context={"current_version": CURRENT_VERSION})
