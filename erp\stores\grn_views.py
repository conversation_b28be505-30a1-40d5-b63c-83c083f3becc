import json
import os
from datetime import datetime

import pymysql
import simplejson
from dateutil.relativedelta import relativedelta
from django.http import HttpResponse, HttpResponseRedirect
from django.template.response import TemplateResponse
from sqlalchemy import and_, or_
from sqlalchemy.orm import make_transient

from erp import helper, DEFAULT_MAKE_ID, IS_FAULTY_TRUE
from erp.accounts import PURCHASE_ACCOUNT_GROUP_NAME
from erp.admin import gate_inward_no_settings
from erp.auth import USER_IN_SESSION_KEY, SESSION_KEY, ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.dao import executeQuery
from erp.helper import getPartyFrequents, inspectorFrequents, listAllInspectors, getUser, getProjectFrequents, \
	smart_tax_sales_return, getStateList
from erp.masters.backend import MasterService
from erp.models import Tax, ReceiptTax, Receipt, Currency, Material, Invoice, ReceiptAttachment, ReceiptMaterial, \
	Make, MaterialMakeMap, Enterprise, OA
from erp.properties import LOGIN_URL, FILE_UPLOAD_PATH, TEMPLATE_TITLE_KEY, MANAGE_RECEIPTS_TEMPLATE, \
	GOODS_RECEIPT_EDIT_URL, \
	INTERNAL_RECEIPT_EDIT_URL, SALES_RETURN_EDIt_URL, SALES_RETURN_LIST_URL, INTERNAL_RECEIPT_LIST_URL, \
	EDIT_RECEIPT_TEMPLATE, GOODS_RECEIPT_LIST_URL
from erp.purchase.document_properties import GRN_INVOICE_DOC_PATH
from erp.sales.backend import InvoiceService
from erp.sales.views import DatetimeEncoder
from erp.stores import logger
from erp.stores.backend import StoresDAO, StoresService
from settings import HOST, USER, PASSWORD, DBNAME, PORT, SQLASession, GCS_PUBLIC_URL
from util.api_util import response_code, JsonUtil
from util.helper import getFinancialYear, writeFile, getAbsolutePath, getFormattedDocPath
from erp.tasks import closing_stock_material_wise_queue_creator

MATERIAL_FORM_KEY = 'material_form'
BILL_OF_MATERIALS_FORMSET_KEY = 'bill_of_materials_formset'
SPECIFICATION_FORMSET_KEY = 'specifications_formset'
MATERIAL_MAKE_FORMSET_KEY = 'material_makes_formset'
PRICE_OF_SUPP_MATERIALS_FORMSET_KEY = 'materials_party_price_formset'
ALTERNATE_UNIT_FORMSET_KEY = 'alternate_unit_formset'
EMPLOYEE_PAY_FORMSET_PREFIX = 'employee_pay_structure'
INDENT_FORM_KEY = 'indent_form'
PYMYSQL_CONVERSIONS = pymysql.converters.conversions.copy()
__author__ = 'saravanan'

# JSON Dump literals
# TODO Tax Row generation: Reorganise (Take it to front-end, probably as ReactJS component)
TAX_ROW_DUMP = "<tr name='tr_%s'><th><a role='button' title='Remove Tax' onclick=\"javascript:removePoTax('%s');\" >" \
               "<input type='hidden' class='hnd_text_id' name='po_tax_code' value='%s'/>" \
               "<i class='fa fa-window-close' aria-hidden='true'></i>" \
               "</a>&nbsp;</th><th><b>%s</b> <i>(Net Rate)</i></th>" \
               "<td><b>%s %%</b><input type='hidden' name='net_rate%s' id='net_%s' value='%s'/></td><td width='10px'>&nbsp;</td>" \
               "<td class='col-sm-6' style='padding-right:0px;'><input type='text' class='form-control' " \
               "style='text-align:right;' name='tax%s' disabled/></td></td>" \
               "<td><input type='hidden' class='po_txt_box' style='text-align:right;' name='asses_rate%s' " \
               "id='asses%s' value='%s' disabled/></td></tr>"
SUB_TAX_ROW_DUMP = "<tr name='tr_%s'><td colspan='2'>%s</td><td>%s %%</td><td width='10px'>&nbsp;</td><td></td></tr>"
TAX_BASE_RATE_ROW_DUMP = "<tr name='tr_%s'><td colspan='2'><i>%s</i></td><td>%s %%</td><td width='10px'>&nbsp;" \
                         "</td><td></td></tr>"

# TODO JSON formatted data instead Separators
COMMA_SEPARATOR = "%2C"  # ","


def manageAllReceipts(request, receipt_title="Goods Receipt", edit_link=GOODS_RECEIPT_EDIT_URL):
	"""
	Renders the main page to manage Receipt (GRN/IRN/SR) - Add, Search, Edit and Delete a GRN

	:param request:
	:param receipt_title:
	:param edit_link:
	:return: Receipt list page TemplateResponse will be loaded.
	"""
	request_handler = RequestHandler(request)
	since, till = JsonUtil.getDateRange(
		rh=request_handler, since_session_key="%s.since" % receipt_title, till_session_key="%s.till" % receipt_title)
	received_against = request_handler.getSessionAttribute("%s.received_against" % receipt_title)
	if received_against is None:
		received_against = "Purchase Order"
		request_handler.setSessionAttribute("%s.received_against" % receipt_title, received_against)
	badge_hit = 'False'
	status = ""
	if request_handler.getPostData('grn_fromdate') is not None:
		since = request_handler.getPostData('grn_fromdate')
		badge_hit = 'True'
		status = "0,4"

	return TemplateResponse(template=MANAGE_RECEIPTS_TEMPLATE, request=request, context={
		'edit_link': edit_link, TEMPLATE_TITLE_KEY: receipt_title, 'badge_hit': badge_hit, 'status': status,
		'since': since.strftime("%Y-%m-%d") if type(since) is datetime else since,
		'till': till.strftime("%Y-%m-%d"), 'received_against': received_against})


def manageInternalReceipts(request):
	receipt_title = "Internal Receipt"
	RequestHandler(request).setSessionAttribute(key="%s.received_against" % receipt_title, value="Issues")
	return manageAllReceipts(request, receipt_title=receipt_title, edit_link=INTERNAL_RECEIPT_EDIT_URL)


def manageSalesReturns(request):
	receipt_title = "Sales Return"
	RequestHandler(request).setSessionAttribute(key="%s.received_against" % receipt_title, value="Sales Return")
	return manageAllReceipts(request, receipt_title=receipt_title, edit_link=SALES_RETURN_EDIt_URL)


def manageReceipt(request, receipt_title="Goods Receipt", list_link=GOODS_RECEIPT_LIST_URL):
	"""
	Renders the main page to manage Receipt Note (GRN/IRN/SR) - Add and Edit a GRN

	:param request:
	:param receipt_title:
	:param list_link:
	:return: Receipt page TemplateResponse will be loaded
	"""
	request_handler = RequestHandler(request)
	master_service = MasterService()
	receipt_no = request_handler.getPostData("receipt_no")
	received_against = request_handler.getSessionAttribute("%s.received_against" % receipt_title)
	gst_category = master_service.getGSTCategory()
	countries = master_service.getCountries()
	gst_category_list = []
	gst_country_list = []
	for category in gst_category:
		gst_category_list.append({"category_id": category.id, "category_name": category.name, "category_desc": category.description})

	for country in countries:
		gst_country_list.append({"country_code": country.code, "country_name": country.name})
	if received_against is None:
		received_against = "Purchase Order"
		request_handler.setSessionAttribute("%s.received_against" % receipt_title, received_against)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	enterprise = SQLASession().query(Enterprise).filter(Enterprise.id == enterprise_id).first()
	is_gate_inward_no_mandatory = enterprise.gate_inward_no_flags & gate_inward_no_settings['mandatory'] > 0
	is_unique_in_fiscal_yr = enterprise.gate_inward_no_flags & gate_inward_no_settings['unique_in_fiscal_yr'] > 0
	is_gate_inward_no_automated = enterprise.gate_inward_no_flags & gate_inward_no_settings['automated'] > 0
	is_gate_inward_no_editable = enterprise.gate_inward_no_flags & gate_inward_no_settings['editable'] > 0
	attachment = {'name': '', 'data': '', 'ext': ''}

	logger.info('GRN Home - {%s} [%s]' % (request_handler.getSessionAttribute(SESSION_KEY), enterprise.id))

	projects = executeQuery(
		"SELECT id, name FROM projects WHERE enterprise_id='%s' AND is_active = 'True' ORDER BY name" % enterprise.id)

	purchase_account = helper.populateAccountLedgerChoices(enterprise_id=enterprise.id, group_names=(
			PURCHASE_ACCOUNT_GROUP_NAME,))

	suppliers = executeQuery("""SELECT party_id, CONCAT(party_name, ' (', party_code, ')'), config_flags FROM party_master AS a
		WHERE a.enterprise_id='%s' AND party_id<>0 order by party_name""" % enterprise.id)

	units = executeQuery(
		"SELECT unit_id, unit_name FROM unit_master WHERE enterprise_id='%s' ORDER BY unit_name" % enterprise.id)
	wo_id = helper.populateProductionPlanDetails(enterprise_id=enterprise_id, order_type=2)

	receipt_status = 0
	location_id = None
	if receipt_no:
		receipt_status = SQLASession().query(Receipt.status, Receipt.location_id).filter(
			Receipt.receipt_no == receipt_no, Receipt.enterprise_id == enterprise.id).first()
		location_id = receipt_status[1]
		receipt_status = receipt_status[0]

	taxes = SQLASession().query(Tax).filter(Tax.enterprise_id == enterprise.id).all()
	currency_list = SQLASession().query(Currency).order_by(Currency.code).all()
	frequent_po_party_list = getPartyFrequents(
		table='grn', enterprise_id=enterprise.id, condition="AND rec_against='Purchase Order'", need_configs=True)
	frequent_sales_return_party_list = getPartyFrequents(
		table='grn', enterprise_id=enterprise.id, condition="AND rec_against='Sales Return'", need_configs=True)
	frequent_other_party_list = getPartyFrequents(
		table='grn', enterprise_id=enterprise.id, condition="AND rec_against='Others'", need_configs=True)
	frequent_job_work_party_list = getPartyFrequents(
		table='grn', enterprise_id=enterprise.id,  condition="AND rec_against='Job Work'", need_configs=True)
	frequent_job_work_in_party_list = getPartyFrequents(
		table='grn', enterprise_id=enterprise.id,  condition="AND rec_against='Job In'", need_configs=True)
	frequent_dc_party_list = getPartyFrequents(
		table='grn', enterprise_id=enterprise.id, condition="AND rec_against='Delivery Challan'", need_configs=True)

	attachment_query = SQLASession().query(Receipt.documents).filter(
		Receipt.receipt_no == receipt_no, Receipt.enterprise_id == enterprise_id).first()

	if attachment_query and all(attachment_query):
		attachment_data = json.loads(json.dumps(attachment_query[0]))
		attachment = {
					'name': attachment_data['name'],
					'data': "{url}/{key}".format(url=GCS_PUBLIC_URL, key="{0}/{1}".format(
						enterprise_id, attachment_data['uid'])),
					'ext': attachment_data['ext']}

	master_service = MasterService()
	material_vo = master_service.constructMaterialVO(enterprise_id=enterprise.id)
	frequent_project_list = getProjectFrequents(table='grn', enterprise_id=enterprise.id)

	inspector_frequent_list = inspectorFrequents(table='grn', enterprise_id=enterprise.id)
	inspector = [inspector_frequent_list]
	all_inspectors = listAllInspectors(table='grn', enterprise_id=enterprise.id)
	inspector.append(all_inspectors)
	for item in inspector_frequent_list:
		if item[0] in all_inspectors:
			all_inspectors.remove(item[0])
	issue_to = []
	if receipt_title == "Internal Receipt":
		issue_query = """SELECT issued_to, invoice_no FROM invoice WHERE issued_to !='' AND issued_to 
		IS NOT NULL AND enterprise_id={enterprise_id} AND type='Issue'  GROUP BY issued_to UNION    
		SELECT issue_to, orderno FROM purchase_order WHERE issue_to !='' AND issue_to 
		IS NOT NULL AND enterprise_id={enterprise_id} AND type=2  GROUP BY issue_to """.format(enterprise_id=enterprise_id)
		issue_to = [item for item in executeQuery(issue_query)]
	return TemplateResponse(template=EDIT_RECEIPT_TEMPLATE, request=request, context={
		'receipt_no': receipt_no if receipt_no else "", 'received_against': received_against,
		'list_link': list_link, TEMPLATE_TITLE_KEY: receipt_title, 'receipt_status': receipt_status,
		'projects': projects, 'suppliers': suppliers, 'issue_to': issue_to, 'units': units, 'inspector': inspector,
		'frequent_projects': frequent_project_list, 'frequent_suppliers': frequent_po_party_list,
		'frequent_sales_return_suppliers': frequent_sales_return_party_list,
		'purchase_account': purchase_account, 'taxes': taxes, 'currency_list': currency_list, 'currency': currency_list,
		'other_frequent_suppliers': frequent_other_party_list, MATERIAL_FORM_KEY: material_vo.material_form,
		'frequent_job_work_suppliers': frequent_job_work_party_list, 'frequent_dc_suppliers': frequent_dc_party_list,
		'frequent_job_work_in_party_list': frequent_job_work_in_party_list,
		'home_currency': enterprise.home_currency_id,
		'is_gate_inward_no_mandatory': is_gate_inward_no_mandatory,
		'is_unique_in_fiscal_yr': is_unique_in_fiscal_yr, 'is_gate_inward_no_automated': is_gate_inward_no_automated,
		'is_gate_inward_no_editable': is_gate_inward_no_editable,
		'attachment_data': attachment['data'],
		'attachment_file_name': attachment['name'],
		'attachment_file_extension': attachment['ext'],
		'is_purchase_order_mandatory': enterprise.is_purchase_order_mandatory, 'is_blanket_po': enterprise.is_blanket_po,
		ALTERNATE_UNIT_FORMSET_KEY: material_vo.alternate_unit_formset,
		BILL_OF_MATERIALS_FORMSET_KEY: material_vo.material_formset,
		SPECIFICATION_FORMSET_KEY: material_vo.specification_formset,
		PRICE_OF_SUPP_MATERIALS_FORMSET_KEY: material_vo.supplier_price_material_formset,
		MATERIAL_MAKE_FORMSET_KEY: material_vo.material_make_formset,
		'gst_category_list': gst_category_list, 'gst_country_list': gst_country_list, 'state_list': getStateList(),
		'wo_id': wo_id, "location_id": location_id})


def manageInternalReceipt(request):
	"""

	:param request:
	:return:
	"""
	receipt_title = "Internal Receipt"
	RequestHandler(request).setSessionAttribute(key="%s.received_against" % receipt_title, value="Issues")
	return manageReceipt(request, receipt_title=receipt_title, list_link=INTERNAL_RECEIPT_LIST_URL)


def manageSalesReturn(request):
	"""

	:param request:
	:return:
	"""
	receipt_title = "Sales Return"
	RequestHandler(request).setSessionAttribute(key="%s.received_against" % receipt_title, value="Sales Return")
	return manageReceipt(request, receipt_title=receipt_title, list_link=SALES_RETURN_LIST_URL)


def saveReceipt(request):
	"""
	Create or update a Receipt. Processes and validates the data submitted through an AJAX call.

	:param request:
	:return: JSON dump with a success message and grn_no of the receipt considered
	"""
	request_handler = RequestHandler(request)
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	user = request_handler.getSessionAttribute(USER_IN_SESSION_KEY)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	enterprise = SQLASession().query(Enterprise).filter(Enterprise.id==enterprise_id).first()
	custom_message = ""
	grn_no = ""
	validate_invoice_no = True
	try:
		grn_header = json.loads(request_handler.getPostData('grn_header'))
		grn_material = grn_header['grn_materials']
		grn_taxes = grn_header['tax_details']
		grn_no = grn_header['receipt_no']
		grn_tags = grn_header['tag_details']
		grn_against_issue_no = grn_header['selectedIssueIds']
		grn_save_approve_status = grn_header['grn_save_approve_status']
		grn_documents = request_handler.getPostData('documents')
		store_service = StoresService()
		if str(request_handler.getPostData('validate_invoice_no')) == "false":
			validate_invoice_no = False
		custom_message, grn_no = store_service.saveGrnDetails(
			grn_header=grn_header, grn_no=grn_no, user=user, enterprise_id=enterprise_id,
			home_currency_id=enterprise.home_currency_id, grn_material=grn_material, grn_taxes=grn_taxes, grn_tags=grn_tags,
			grn_against_issue_no=grn_against_issue_no, is_super_edit=request_handler.getPostData('is_super_edit'),
			oa_code=request_handler.getPostData('oa_code') if request_handler.getPostData('oa_code') != "" else None,
			username=user.username, user_id=user_id, custom_message=custom_message, fy_start_day=enterprise.fy_start_day,
			documents=json.loads(grn_documents) if grn_documents and grn_documents != "" else "",
			grn_save_approve_status=grn_save_approve_status, validate_invoice_no=validate_invoice_no)
		response = response_code.success()
		response['custom_message'] = custom_message
	except Exception as e:
		logger.exception("Failed to Update GRN... %s" % e.message)
		custom_message += "<br /><b>Failed to Update GRN </b>"
		response = response_code.internalError()
		# e.message[0] - popup_message
		# e.message[1] - hard warning or soft warning
		response['custom_message'] = e.message[0]
		response['warning_type'] = e.message[1]
	response['grn_no'] = grn_no
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def handle_uploaded_file(instance, filename, order_no):
	# FIXME:2.9 this definition should in commons
	if not os.path.exists(getAbsolutePath(FILE_UPLOAD_PATH)):
		os.mkdir(getAbsolutePath(FILE_UPLOAD_PATH))
	ext = filename.split('.')[-1]
	target_filename = "%s.%s" % (order_no, ext)
	with open("%s%s" % (getAbsolutePath(FILE_UPLOAD_PATH), target_filename), 'wb+') as destination:
		for chunk in instance.chunks():
			destination.write(chunk)
	logger.info("Target File Name in Upload: %s" % target_filename)
	return ext


# File transfer to the Database Table Name - grn_document

def approveReceipt(request):
	"""
	Upgrades the receipt status as 'Approved', persists its document, makes it available for the ICD to audit.

	:param request:
	:return: JSON message telling if the Receipt is Approved successfully or not
	"""
	logger.info("Approval Module Triggered...")
	db_session = SQLASession()
	try:
		db_session.begin(subtransactions=True)  # Receipt Code generation as a separate Unit of Work
		request_handler = RequestHandler(request)
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		if user_id is None:
			user_id = request_handler.getPostData('user_id')
			enterprise_id = request_handler.getPostData('enterprise_id')
			fy_start_day = request_handler.getPostData('fy_start_day')
			icd_module_access = True if request_handler.getPostData('icd') == 'true' else False
			icd_auto_gen_voucher = True if request_handler.getPostData('icd_auto_gen_voucher') == 'true' else False
			icd_ignore_credit_note = True if request_handler.getPostData('icd_ignore_credit_note') == 'true' else False
		else:
			enterprise_in_session = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise
			fy_start_day = enterprise_in_session.fy_start_day
			enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
			icd_module_access = request.session['module_access']['icd']
			icd_auto_gen_voucher = request.session['module_access']['icd_auto_gen_voucher']
			icd_ignore_credit_note = request.session['module_access']['icd_ignore_credit_note']

		receipt_no = request_handler.getPostData('receipt_no')
		remarks = request_handler.getPostData('remarks')
		receipt = db_session.query(Receipt).filter(Receipt.receipt_no == receipt_no).first()
		icd_request_acknowledgement = db_session.query(Enterprise.is_icd_request_acknowledgement).filter(
			Enterprise.id == enterprise_id).first()
		approved_date = datetime.now()
		user = getUser(enterprise_id=enterprise_id, user_id=user_id)
		if receipt.status == Receipt.STATUS_REJECTED and user.is_super:
			receipt.super_modified_by = user_id
			receipt.super_modified_on = approved_date
		if receipt.status == Receipt.STATUS_DRAFT or receipt.receipt_code in ('0', None, ''):
			current_fy = getFinancialYear(for_date=receipt.inward_date, fy_start_day=receipt.enterprise.fy_start_day)
			receipt.financial_year = current_fy
			receipt.approved_on = approved_date
			if Receipt.TYPE_CODE[receipt.received_against] in ("IRN", "SR"):
				grn_types = [receipt.received_against]
			else:
				grn_types = [key if Receipt.TYPE_CODE[key] == 'GRN' else None for key in Receipt.TYPE_CODE]
			while None in grn_types:
				grn_types.remove(None)
			latest_receipt_no = db_session.query(Receipt.receipt_code).filter(
				Receipt.financial_year == current_fy, Receipt.enterprise_id == receipt.enterprise.id,
				Receipt.received_against.in_(grn_types), Receipt.receipt_code != '0').order_by(
				Receipt.approved_on.desc()).first()
			receipt.receipt_code = "1" if not ('%s' % latest_receipt_no).isdigit() or latest_receipt_no == "0" else int(
				'%s' % latest_receipt_no) + 1
		receipt.status = Receipt.STATUS_APPROVED
		receipt.approved_by = user_id
		receipt.updateRemarks(remarks=remarks, user=user)
		receipt.last_modified_by = user_id
		receipt.last_modified_on = approved_date
		logger.info("loading....%s" % receipt)
		db_session.add(receipt)
		db_session.commit()  # Committing it immediately to avoid any Code generation conflicts.
		response = response_code.success()
		response['code'] = receipt.getCode()
		response['custom_message'] = "GRN No:<b> %s </b>has been successfully approved" % (receipt.getCode())
		response['invoice_type'] = receipt.invoice_type
		store_service = StoresService()
		store_service.notifyReceiptApprovalCount(
			enterprise_id=enterprise_id, sender_id=user_id, receipt=receipt,
			icd=icd_module_access)
		logger.info("receipt_type: %s" % int(receipt.invoice_type))
		if int(receipt.invoice_type) == 2:
			from erp.icd import backend
			if icd_module_access:
				if icd_auto_gen_voucher:
					store_service.autoGenerateNote(
						receipt=receipt, enterprise_id=enterprise_id, user_id=user_id,
						icd_ignore_credit_note=icd_ignore_credit_note, fy_start_day=fy_start_day,
						icd_request_acknowledgement=icd_request_acknowledgement[0])
			else:
				if receipt.received_against == 'Sales Return':
					store_service.autoGenerateNote(
						receipt=receipt, enterprise_id=enterprise_id, user_id=user_id,
						icd_ignore_credit_note=icd_ignore_credit_note, fy_start_day=fy_start_day,
						icd_request_acknowledgement=icd_request_acknowledgement[0])
				else:
					if not icd_request_acknowledgement[0]:
						backend.ICDService().verifyNote(
							enterprise_id=enterprise_id, receipt_no=receipt.receipt_no, user_id=user_id,
							icd=icd_module_access)
		if not receipt.goods_already_received:
			closing_stock_material_wise_queue_creator.delay(
				item_list=[{"item_id": i.item_id, "quantity": float(i.accepted_qty), "is_faulty": i.is_faulty,
							"enterprise_id": i.enterprise_id} for i in receipt.items], is_sales=False,
				location_id=receipt.location_id)
		logger.info("Approved GRN receipt_no: %s" % receipt_no)
	except Exception as e:
		db_session.rollback()
		logger.exception(e)
		response = response_code.internalError()
		response['custom_message'] = "Grn Failed to Approved"
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def loadAllReceipts(request):
	"""
	Loads list of Receipts for the enterprise of the logged-in-user as JSON dumps.

	:param request:
	:return: JSON dump holding a list of Receipt details in a 2D array
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		receipt_title = request_handler.getPostData("receipt_title")
		from_date, to_date = JsonUtil.getDateRange(
			rh=request_handler, since_session_key="%s.since" % receipt_title, till_session_key="%s.till" % receipt_title)
		received_against = request_handler.getAndCacheData(
			key="received_against", session_key="%s.received_against" % receipt_title)
		status = request_handler.getPostData("status")
		limit = request_handler.getPostData("limit")
		offset = request_handler.getPostData("offset")
		if status == "":
			status = None
		json_receipts = StoresService().getAllReceipts(
			received_against=received_against, enterprise_id=enterprise_id, from_date=from_date,
			to_date=to_date, status=status, limit=limit, offset=offset)
		return HttpResponse(content=simplejson.dumps(json_receipts), mimetype='application/json')
	except Exception as e:
		logger.exception("Load Receipt is failed... %s" % e.message)
		response = response_code.internalError()
		response['error'] = "Failed due to internal server issue. Please contact support!"
		return HttpResponse(json.dumps(response), 'content-type=text/json')


def loadReceipt(request):
	"""

	:param request:
	:return: JSON dumps holding the details of the Receipt queried
	"""
	rh = RequestHandler(request=request)
	receipt_no = rh.getPostData("receipt_no")
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	query_receipt_by_no = """SELECT 
		i.rec_against AS received_against,
		i.grn_no AS grn_no, 
		s.party_id AS part_id, 
		s.config_flags AS config_flags, 
		p.id AS project_code, 
		i.inv_type AS invoice_type, 
		i.invno AS invoice_no, 
		DATE_FORMAT(i.inv_date, '%Y-%m-%d') AS invoice_date, 
		i.matrecthrough AS material_received_through, 
		i.purchase_account_id AS purchase_account_id, 
		i.duty_passed AS duty_passed, 
		i.packing_charges AS packing_charges, 
		i.transport_charges AS transport_charges, 
		i.inward_no AS inward_no, 
		DATE_FORMAT(i.inward_date,'%Y-%m-%d') AS inward_date, 
		i.inspector AS inspector,  
		i.remarks AS remarks, 
		i.rej_remarks AS rejected_remarks, 
		i.status AS status, 
		IF (i.rec_against = 'Note', i.receipt_no, LPAD(i.receipt_no, 6, '0')) as receipt_no, 
		i.financial_year AS financial_year, 
		i.net_inv_value AS net_invoice_value, 
		i.audit_remarks AS icd_remarks, 
		i.inv_currency_id AS inv_currency_id, 
		i.cur_conversion_rate AS cur_conversion_rate, 
		i.round_off AS round_off,
		IFNULL((SELECT round_off FROM crdrnote as cn JOIN crdrnote_grn_map as cgm ON cn.grn_no = cgm.crdrnote_id WHERE cgm.crdrnote_grn_no = {receipt_no}), '0') as icd_round_off,
		i.other_charges AS other_charges,
		s.party_name AS party_name,
		IFNULL((SELECT v.status FROM ledger_bills as l,voucher as v 
					WHERE v.id=i.voucher_id and l.id = i.ledger_bill_id), '0') AS acc_status,
		IFNULL(i.sub_number, '') AS sub_number, 
		i.goods_already_received AS goods_already_received, 
		i.ecommerce_gstin,
		IFNULL((SELECT crdrnote_id FROM crdrnote_grn_map WHERE crdrnote_grn_no = {receipt_no} LIMIT 1), '0') as note_id,		 
		i.documents AS attachment,
		IF((SELECT note_datetime FROM crdrnote AS cn JOIN crdrnote_grn_map AS cgm ON cn.grn_no = cgm.crdrnote_id 
		WHERE cgm.crdrnote_grn_no = {receipt_no}) >= CURDATE() - INTERVAL 1 DAY, 1, 0) as isval_einvoice,
		IFNULL((SELECT irn_ack_json FROM crdrnote AS cn JOIN crdrnote_grn_map AS cgm ON cn.grn_no = cgm.crdrnote_id 
		WHERE cgm.crdrnote_grn_no = {receipt_no}), 0) as is_cnl_einvoice,
		IFNULL((SELECT c.code FROM currency as c WHERE c.id=i.inv_currency_id), '') AS currency_code,
		i.location_id as location_id               
		FROM grn as i 
		LEFT JOIN projects as p ON p.id=i.project_code AND p.enterprise_id=i.enterprise_id 
		LEFT JOIN party_master as s  ON s.party_id=i.party_id AND s.enterprise_id=i.enterprise_id
		WHERE i.enterprise_id = '{enterprise_id}' AND i.grn_no = '{receipt_no}'
		GROUP BY i.grn_no;""".format(
		receipt_no=receipt_no, enterprise_id=enterprise_id)
	try:
		receipt_details = executeQuery(query_receipt_by_no, as_dict=True)
		for receipt in receipt_details:
			if receipt['is_cnl_einvoice'] == 'null':
				receipt['is_cnl_einvoice'] = 1
			if receipt['attachment']:
				attachment = json.loads(receipt['attachment'])
				if attachment:
					receipt['attachment_data'] = {
						'name': attachment['name'],
						'data': "{url}/{key}".format(
							url=GCS_PUBLIC_URL, key="{0}/{1}".format(enterprise_id, attachment['uid']))}
	except Exception as e:
		logger.exception(e.message)
		receipt_details = ""
	return HttpResponse(content=simplejson.dumps(receipt_details), mimetype='application/json')


def loadNoteHeader(request):
	"""

	:param request:
	:return: JSON dumps holding the details of the Note queried
	"""
	rh = RequestHandler(request=request)
	note_id = rh.getPostData("note_id")
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	query_note_details = """SELECT
		i.status AS status, i.attachment,
		s.party_id AS part_id,
		s.party_name AS party_name,		 
		IFNULL(i.inv_no, "-") AS invoice_no, 
		DATE_FORMAT(i.inv_date, '%Y-%m-%d') AS invoice_date,
		IFNULL(i.inv_value, 0) AS net_invoice_value,
		i.round_off AS round_off,
		i.financial_year AS financial_year,
		IFNULL(i.sub_number,"") as sub_number,
		LPAD(i.note_no, 6, '0') as note_no,
		IFNULL(i.receipt_code, "") as receipt_no,
		IF(i.note_datetime >= CURDATE() - INTERVAL 1 DAY, 1, 0) as isval_einvoice,
		IFNULL(i.irn_ack_json, 0) as is_cnl_einvoice			
		FROM crdrnote as i
		JOIN party_master as s  ON s.party_id=i.party_id WHERE i.grn_no = '{note_id}' GROUP BY i.grn_no;""".format(
		note_id=note_id, enterprise_id=enterprise_id)
	try:
		note_details = executeQuery(query_note_details, as_dict=True)
		for note in note_details:
			if note['is_cnl_einvoice'] == 'null':
				note['is_cnl_einvoice'] = 1
			if note['attachment']:
				attachment = json.loads(note['attachment'])
				if attachment:
					note['attachment_data'] = {
						'name': attachment['name'],
						'data': "{url}/{key}".format(
							url=GCS_PUBLIC_URL, key="{0}/{1}".format(enterprise_id, attachment['uid']))}
	except Exception as e:
		logger.exception(e.message)
		note_details = ""
	return HttpResponse(content=simplejson.dumps(note_details), mimetype='application/json')


def fetchRejectionProfiles(request):
	"""
	API to return a set of GRN Item Rejection Profile JSON objects, given GRN Item Details as JSON in request

	:param request: HttpRequest with GRN Item Details as JSON of the format:
		{
			enterprise_id:102, grn_id:1982, item_id:43321, make_id:3, is_faulty:False, po_id:None, rec_grn_id: None,
			oa_id:None, dc_id:None
		}
	:return: HttpResponse with the Item's Rejection Profiles as a set of JSON of the format:
		(
			{
				enterprise_id:102, grn_id:1982, item_id:43321, make_id:3, is_faulty:False, po_id:None, rec_grn_id: None,
				oa_id:None, dc_id:None, reason:"REJECTED", quantity:30, debit:300.00
			},
			{
				enterprise_id:102, grn_id:1982, item_id:43321, make_id:3, is_faulty:False, po_id:None, rec_grn_id: None,
				oa_id:None, dc_id:None, reason:"REJECTED", quantity:30, debit:300.00
			},
		)
	"""
	try:
		request_handler = RequestHandler(request)
		grn_item_details = simplejson.loads(request_handler.getPostData('grn_item_details'))
		rejection_query = """SELECT
				gr.enterprise_id, gr.grnNumber, gr.item_id, gr.make_id, gr.is_faulty, gr.make_id, gr.po_no, gr.rec_grn_id,
				gr.oa_id, gr.dc_id, gr.reason, gr.quantity, gr.debit
			FROM
				grn_material_rejection as gr
			WHERE
				gr.grnNumber = {grnNumber}
				AND gr.enterprise_id = {enterprise_id}
				AND gr.item_id = {item_id}
				AND gr.make_id = {make_id}
				AND gr.is_faulty = {is_faulty}
				AND gr.po_no = {po_id}
				AND gr.rec_grn_id = {rec_grn_id}
				AND gr.oa_id = {oa_id}
				AND gr.dc_id = {dc_id}""".format(
			grnNumber=grn_item_details['grn_id'], enterprise_id=grn_item_details['enterprise_id'],
			item_id=grn_item_details['item_id'], make_id=grn_item_details['make_id'],
			is_faulty=int(grn_item_details['is_faulty']), po_id=grn_item_details['po_id'],
			rec_grn_id=grn_item_details['rec_grn_id'], oa_id=grn_item_details['oa_id'],
			dc_id=grn_item_details['dc_id']).replace("= NULL", "IS NULL")
		logger.debug(rejection_query)
		grn_rejection_profiles = executeQuery(query=rejection_query, as_dict=True)
		logger.debug("Rejection Profile is Fetched : %s" % simplejson.dumps(grn_rejection_profiles))

	except Exception as e:
		logger.exception("could not fetch GRN Item Rejection Profiles: %s" % e)
		grn_rejection_profiles = []

	return HttpResponse(content=simplejson.dumps(grn_rejection_profiles), mimetype='application/json')


def loadReceiptMaterial(request):
	"""
	:param request:
	:return: JSON dumps holding the details of Materials associated with the Receipt queried
	"""

	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	grn_id = request_handler.getPostData('grn_id')
	received_against = request_handler.getPostData('received_against')
	selected_supplier = request_handler.getPostData('selected_supplier')
	is_goods_already_received = request_handler.getPostData('goods_already_received') == "true"
	current_grn_no = request_handler.getPostData('grn_no')
	grn_no = "%s" % grn_id
	try:
		grn_materials = StoresService().getReceiptMaterial(
			enterprise_id=enterprise_id, grn_no=grn_no, is_goods_already_received=is_goods_already_received,
			received_against=received_against, selected_supplier=selected_supplier, current_grn_no=current_grn_no)
		if grn_materials:
			issue_to_names = ""
			pp_nos = ""
			for grn_material in grn_materials:
				# if grn_material["make_name"]:
				# 	make_name = helper.constructDifferentMakeName(grn_material["make_name"])
				# 	if make_name:
				# 		grn_material["item"] = grn_material["item"] + " [" + make_name + "]"
				if grn_material["po_id"]:
					issue_to_query = """ SELECT issue_to FROM purchase_order WHERE enterprise_id={enterprise_id} and id={po_id} and 
							type = 2""".format(enterprise_id=enterprise_id, po_id=grn_material["po_id"])
					issue_to_names = executeQuery(issue_to_query)
					pp_no_query = """SELECT (CONCAT(po.financial_year, '/PP/', LPAD(po.orderno, 6, 0), IFNULL(po.sub_number, ''))) as 
								pp_no from purchase_order po WHERE po.enterprise_id={enterprise_id} and po.id={po_id}
								""".format(enterprise_id=enterprise_id, po_id=grn_material["po_id"])
					pp_nos = executeQuery(pp_no_query)
				if issue_to_names:
					issue_to = issue_to_names[0][0]
					grn_material["issue_to"] = issue_to
				if pp_nos:
					pp_no = pp_nos[0][0]
					grn_material["pp_no"] = pp_no
	except Exception as e:
		logger.exception(e)
		grn_materials = []
	return HttpResponse(content=simplejson.dumps(grn_materials), mimetype='application/json')


def checkStockAvailability(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	received_against = request_handler.getPostData('received_against')
	grn_id = request_handler.getPostData('grn_no')
	stock_materials = []
	store_service = StoresService()
	try:
		if grn_id:
			query = """ SELECT grn_mat.item_id as item_id, grn_mat.po_no as po_no, grn_mat.make_id as make_id, 
			grn_mat.is_faulty as is_faulty, DATE_FORMAT(r.inward_date,'%Y-%m-%d') as inward_date, grn_mat.dc_id as dc_id, 
			mat.is_stocked as is_stocked 
			FROM grn as r, grn_material as grn_mat, materials as mat
			WHERE r.grn_no=grn_mat.grnNumber AND r.grn_no={grn_no} AND r.rec_against='{received_against}' 
			AND grn_mat.enterprise_id={enterprise_id} AND mat.id = grn_mat.item_id
			GROUP BY grn_mat.grnNumber,grn_mat.item_id,grn_mat.make_id,grn_mat.is_faulty, grn_mat.po_no""".format(
				enterprise_id=enterprise_id, received_against=received_against, grn_no=grn_id)

			logger.debug("Validate Stock Query:%s" % query)
			materials = executeQuery(query, as_dict=True)

			till = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
			for material in materials:
				since = material["inward_date"] + " 23:59:59"
				query = store_service.getStockQuery(
					enterprise_id=enterprise_id, from_date=since, to_date=till, material=material)
				stocks = executeQuery(query, as_dict=True)
				for stock in stocks:
					stock['inward_date'] = material["inward_date"]
					stock['dc_id'] = material["dc_id"]
					stock['is_stocked'] = material["is_stocked"]
					stock_materials.append(stock)
	except Exception as e:
		logger.exception("Validating stock failed %s" % e.message)
	return HttpResponse(content=simplejson.dumps(stock_materials), mimetype='application/json')


def loadInvoiceByGrn(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	result = []
	try:
		grn_id = request_handler.getPostData('grn_id')
		query = """SELECT issued_to, invoice.financial_year, invoice.id FROM grn
					LEFT JOIN grn_material ON grn_material.grnNumber = grn.grn_no 
						AND grn_material.enterprise_id = grn.enterprise_id
					LEFT JOIN grn_issue_map as gim ON gim.grn_no = grn.grn_no
						AND gim.enterprise_id = grn.enterprise_id	
					RIGHT JOIN invoice ON invoice.enterprise_id = grn.enterprise_id AND 
						(invoice.id = grn_material.dc_id OR gim.issue_id = invoice.id)
					WHERE grn.grn_no = '%s' AND grn.enterprise_id = '%s' """ % (grn_id, enterprise_id)
		result = executeQuery(query=query, as_dict=False)
	except Exception as e:
		logger.info('Error : %s' % e.message)
	return HttpResponse(content=simplejson.dumps(result), mimetype='application/json')


def loadReceiptMaterialAgainstDC(request):
	"""
	:param request:
	:return: JSON dumps holding the details of Materials associated with the Receipt queried
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	grn_no = request_handler.getPostData("grn_no")
	result = []
	dc_item = []
	dc_item_val = {}

	try:
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
		cur = conn.cursor()
		grn_materials = SQLASession().query(ReceiptMaterial).filter(
			ReceiptMaterial.receipt_no == grn_no, ReceiptMaterial.enterprise_id == enterprise_id,
			ReceiptMaterial.dc_id != 0).all()
		# ReceiptMaterial

		for item in grn_materials:
			rec_qty = 0
			dc_qty = 0
			data_query = "select sum(qty) from invoice_materials as a,invoice as b where a.invoice_id=b.id and " \
			             "a.invoice_id =" + str(item.dc_id) + " and a.item_id='" + str(item.item_id) + "' " \
			                                                                                                "and a.make_id=" + str(
				item.make_id) + " and b.status > -1 and a.is_faulty = '" + str(item.is_faulty) + "'"
			cur.execute(data_query)
			dc_materials = cur.fetchall()
			if dc_materials:
				dc_qty = dc_materials[0][0] if dc_materials[0][0] is not None else '0'

			data_query = "select sum(acc_qty) from grn_material as a,grn as b where a.grnNumber=b.grn_no " \
			             "and a.item_id='" + str(item.item_id) + "' and a.make_id=" + str(item.make_id) + " " \
			                                                                                                    "and a.dc_id = " + str(
				item.dc_id) + " and b.status > -1 and a.is_faulty = '" + str(item.is_faulty) + "'"
			cur.execute(data_query)
			rec_materials = cur.fetchall()
			if rec_materials:
				rec_qty = rec_materials[0][0] if rec_materials[0][0] is not None else '0'
			dc_item_val['dc_mat_pending_qty'] = (float(dc_qty) + float(item.accepted_qty)) - float(rec_qty)
			dc_item_val['drawing_no'] = item.material.drawing_no
			dc_item_val['dc_id'] = item.dc_id
			dc_item_val['item'] = item.material.name
			dc_item_val['item_id'] = item.item_id
			dc_item_val['hsn_code'] = item.hsn_code
			dc_item_val['qty'] = str(item.quantity)
			dc_item_val['unit_name'] = item.material.unit.unit_name
			dc_item_val['unit_id'] = item.material.unit.unit_id
			dc_item_val['rate'] = str(item.rate)
			dc_item_val['discount'] = str(item.discount)
			dc_item_val['price'] = str(item.material.price)
			dc_item_val['po_id'] = item.dc_id
			dc_item_val['dc_qty'] = item.quantity
			dc_item_val['acc_qty'] = item.accepted_qty
			dc_item_val['rec_qty'] = item.received_qty
			dc_item_val['rej_qty'] = item.received_qty - item.accepted_qty
			dc_item_val['shortage_qty'] = item.received_qty - item.accepted_qty
			dc_item_val['make_id'] = item.make_id
			dc_item_val['make_name'] = str(item.make)
			dc_item_val['dc_code'] = item.dc.getInternalCode() if item.dc else ""
			dc_item_val['dc_item_display_name'] = item.material.name
			dc_item_val['is_service'] = item.material.is_service
			if item.material.drawing_no:
				dc_item_val['dc_item_display_name'] += " - %s" % item.material.drawing_no
			make_name = helper.constructDifferentMakeName(item.material.makes_json)
			if make_name:
				dc_item_val['dc_item_display_name'] += " [%s]" % make_name
			dc_item_val['is_faulty'] = item.is_faulty

			if item.is_faulty == IS_FAULTY_TRUE:
				dc_item_val['dc_item_display_name'] += " [Faulty]"
			dc_item_val['mat_type'] = 1 if item.material.is_stocked else 0
			dc_item_val['alternate_unit_id'] = 0
			dc_item_val['scale_factor'] = 1
			if item.alternate_unit_id and item.alternate_unit_id != '0' and int(item.alternate_unit_id) != 0:
				scale_factor = helper.getScaleFactor(
					enterprise_id=enterprise_id, item_id=item.item_id, alternate_unit_id=item.alternate_unit_id)
				if scale_factor:
					dc_item_val['dc_mat_pending_qty'] = float(dc_item_val['dc_mat_pending_qty']) / float(
						scale_factor) if dc_item_val[
						'dc_mat_pending_qty'] else 0
					dc_item_val['qty'] = float(dc_item_val['qty']) / float(scale_factor) if dc_item_val['qty'] else 0
					dc_item_val['dc_qty'] = float(dc_item_val['dc_qty']) / float(scale_factor) if dc_item_val['dc_qty'] else 0
					dc_item_val['acc_qty'] = float(dc_item_val['acc_qty']) / float(scale_factor) if dc_item_val['acc_qty'] else 0
					dc_item_val['rec_qty'] = float(dc_item_val['rec_qty']) / float(scale_factor) if dc_item_val['rec_qty'] else 0
					dc_item_val['rej_qty'] = float(dc_item_val['rej_qty']) / float(scale_factor) if dc_item_val['rej_qty'] else 0
					dc_item_val['shortage_qty'] = item.received_qty - item.accepted_qty
					dc_item_val['unit_name'] = helper.getUnitName(enterprise_id=enterprise_id,
					                                              unit_id=item.alternate_unit_id)
					dc_item_val['alternate_unit_id'] = item.alternate_unit_id
					dc_item_val['scale_factor'] = scale_factor if scale_factor else 1

			dc_item_val['inspection_log'] = json.dumps(item.inspection_log) if item.inspection_log != "" else ""
			dc_item.append(dc_item_val.copy())

		result.append(dc_item)
		conn.close()
	except Exception as e:
		logger.info('Error : %s' % e.message)
	return HttpResponse(content=simplejson.dumps(result), mimetype='application/json')


def loadPurchaseOrderNumbers(request):
	"""
	Loads the POs placed with the Supplier selected, for the chosen Project of the Enterprise associated with the
	logged-in-user

	:param request:
	:return: JSON dumps of the POs queried
	"""
	request_handler = RequestHandler(request)
	supplier_id = request_handler.getPostData('party_id')
	grn_no = request_handler.getPostData('grn_no')
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	inv_date = request_handler.getPostData('inv_date')
	response = response_code.success()
	response['po_numbers'] = []
	if supplier_id in ('0', None, ""):
		logger.warn("Failed loading PO Numbers for GRN: %s & supplier_id: %s" % ([enterprise_id, grn_no], supplier_id))
		return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')

	received_against = request_handler.getPostData('received_against')
	is_goods_already_received = request_handler.getPostData('goods_already_received') == "true"
	po_type = 1 if received_against == 'Job Work' else 0
	try:
		if grn_no != "" and inv_date is None:
			receipt_inv_date = SQLASession().query(Receipt.invoice_date).filter(
				Receipt.receipt_no == grn_no, Receipt.enterprise_id == enterprise_id).first()
			inv_date = receipt_inv_date[0].strftime("%Y-%m-%d")
		if is_goods_already_received is False:
			pos_by_enterprise_project_supplier = """
				SELECT  a.id,CONCAT(a.financial_year, '/', CASE  WHEN a.type = 1 THEN 'JO'  ELSE 'PO'  END, '/',  CASE
				WHEN LENGTH(a.orderno) < 6 THEN LPAD(a.orderno, 6, 0) ELSE a.orderno END, IFNULL(a.sub_number, '')) as code,
				CASE WHEN a.is_blanket_po = 1 THEN CONCAT('(Blanket', CASE  WHEN a.type = 1 THEN ' JO)'  ELSE ' PO)'  END) ELSE '' END as blanket_po
				FROM purchase_order AS a, enterprise AS b, purchase_order_material AS c 
				WHERE  b.id = a.enterprise_id AND a.type = '{po_type}' AND a.status = 2 AND c.pid = a.id 
				AND a.supplier_id = '{supplier_id}' 
				AND IF(a.is_blanket_po = 0 , IF((c.pur_qty - 
					IFNULL((SELECT SUM(acc_qty) FROM grn_material AS gm, grn AS g 
						WHERE g.enterprise_id = '{enterprise_id}' AND g.status > - 1 AND gm.grnNumber = g.grn_no 
						AND gm.po_no = c.pid AND gm.item_id = c.item_id AND gm.make_id = c.make_id AND g.inv_type = 2),0)
						) > 0, 
					1, 0), (a.valid_since <= '{valid_date}' AND a.valid_till >= '{valid_date}'))
				GROUP BY a.id """.format(
				po_type=po_type, supplier_id=supplier_id, enterprise_id=enterprise_id,
				valid_date=inv_date if inv_date else datetime.now().strftime("%Y-%m-%d"))
		else:
			pos_by_enterprise_project_supplier = """
				SELECT a.id,CONCAT(a.financial_year, '/', CASE  WHEN a.type = 1 THEN 'JO'  ELSE 'PO'  END, '/',  CASE
				WHEN LENGTH(a.orderno) < 6 THEN LPAD(a.orderno, 6, 0) ELSE a.orderno END, IFNULL(a.sub_number, '')) as code,
				'' as blanket_po
				FROM purchase_order AS a, enterprise AS b, purchase_order_material AS c, grn_material as gm_mat WHERE  b.id = a.enterprise_id 
				AND a.type = '{po_type}' AND a.status = 2 AND c.pid = a.id AND a.supplier_id = '{supplier_id}' AND gm_mat.po_no = a.id AND 
				IF(IFNULL((SELECT SUM(dc_qty) FROM  grn_material AS gm, grn AS g WHERE gm.rec_grn_id is NULL 
				AND g.enterprise_id = '{enterprise_id}' AND g.status > 0 AND gm.grnNumber=gm_mat.grnNumber AND gm.grnNumber = g.grn_no AND gm.po_no = a.id 
				AND gm.item_id = c.item_id AND gm.make_id = c.make_id AND g.inv_type=1), 0) > IFNULL((SELECT SUM(dc_qty) FROM  grn_material AS gm, grn AS g 
				WHERE gm.grnNumber = g.grn_no AND gm.rec_grn_id=gm_mat.grnNumber AND gm.make_id = c.make_id AND g.enterprise_id = '{enterprise_id}' AND g.status > - 1 AND gm.po_no = a.id 
				AND gm.item_id = c.item_id AND g.inv_type=2), 0), 1, 0)""".format(
				po_type=po_type, supplier_id=supplier_id, enterprise_id=enterprise_id)
		if grn_no != "":
			pos_by_enterprise_project_supplier_sub = """ 
				UNION SELECT p.id,CONCAT(p.financial_year, '/', CASE WHEN p.type = 1 THEN 'JO' ELSE 'PO' END, '/', CASE WHEN 
				LENGTH(p.orderno) < 6 THEN LPAD(p.orderno, 6, 0) ELSE p.orderno END, IFNULL(p.sub_number, '')),
				CASE WHEN p.is_blanket_po = 1 THEN CONCAT('(Blanket', CASE  WHEN p.type = 1 THEN ' JO)'  ELSE ' PO)'  END) ELSE '' END as blanket_po
				FROM grn_material AS a, enterprise AS b, purchase_order AS p WHERE a.enterprise_id = b.id 
				AND p.id = a.po_no AND p.supplier_id = {supplier_id} AND a.grnNumber = {grn_no} 
				AND (p.is_blanket_po = 0 
				OR (p.is_blanket_po = 1 AND (IFNULL(a.po_no, '') <> '' 
				OR (IFNULL(a.po_no, '') = '' AND p.valid_since <= '{valid_date}' AND p.valid_till >= '{valid_date}'))))""".format(
				supplier_id=supplier_id, grn_no=grn_no,
				valid_date=inv_date if inv_date else datetime.now().strftime("%Y-%m-%d"))
			pos_by_enterprise_project_supplier = "%s %s" % (
				pos_by_enterprise_project_supplier, pos_by_enterprise_project_supplier_sub)
		pos_by_enterprise_project_supplier = "SELECT DISTINCT supplier_pos.* FROM (%s) AS supplier_pos" % (
			pos_by_enterprise_project_supplier)
		if received_against == 'Job Work':
			pos_by_enterprise_project_supplier = "%s %s" % (
				pos_by_enterprise_project_supplier,
				"INNER JOIN invoice on invoice.job_po_id = supplier_pos.id WHERE invoice.status = 1")
		pos_by_enterprise_project_supplier = "%s %s" % (
			pos_by_enterprise_project_supplier, "ORDER BY supplier_pos.code")

		po_numbers = executeQuery(pos_by_enterprise_project_supplier)
		logger.debug("\n\nPurchase Order Fetched: %s\n\n" % [po_numbers])
		response['po_numbers'] = po_numbers
	except Exception as e:
		response = response_code.internalError()
		response['custom_message'] = "Could not load PO numbers"
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def loadOpenPONumbers(request):
	"""
	Loads the POs placed with the Supplier selected and invoice date, for the chosen Project of the Enterprise associated with the
	logged-in-user

	:param request:
	:return: JSON dumps of the POs queried
	"""
	request_handler = RequestHandler(request)
	supplier_id = request_handler.getPostData('party_id')
	grn_no = request_handler.getPostData('grn_no')
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	inv_date = request_handler.getPostData('inv_date')
	changed_inv_date = request_handler.getPostData('changed_inv_date')
	response = response_code.success()
	response['added_pos'] = []
	response['removed_pos'] = []
	if supplier_id in ('0', None, ""):
		logger.warn("Failed loading PO Numbers for GRN: %s & supplier_id: %s" % ([enterprise_id, grn_no], supplier_id))
		return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')

	received_against = request_handler.getPostData('received_against')
	is_goods_already_received = request_handler.getPostData('goods_already_received') == "true"
	po_type = 1 if received_against == 'Job Work' else 0
	try:
		loaded_pos = get_changed_pos(
			enterprise_id=enterprise_id, po_type=po_type, supplier_id=supplier_id,
			received_against=received_against, is_goods_already_received=is_goods_already_received, valid_date=inv_date)
		changed_pos = get_changed_pos(
			enterprise_id=enterprise_id, po_type=po_type, supplier_id=supplier_id,
			received_against=received_against, is_goods_already_received=is_goods_already_received, valid_date=changed_inv_date)
		response['removed_pos'] = list(set(loaded_pos) - set(changed_pos))
		response['added_pos'] = list(set(changed_pos) - set(loaded_pos))
	except Exception as e:
		response = response_code.internalError()
		response['custom_message'] = "Could not load PO numbers"
		logger.exception(e)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def get_changed_pos(enterprise_id=None, po_type=None, supplier_id=None, received_against=None, is_goods_already_received=None, valid_date=None):
	"""

	:param enterprise_id:
	:param po_type:
	:param supplier_id:
	:param received_against:
	:param is_goods_already_received:
	:param valid_date:
	:return:
	"""
	changed_po_numbers = []
	try:
		pos_by_enterprise_project_supplier = ''
		if is_goods_already_received is False:
			pos_by_enterprise_project_supplier = """
				SELECT  a.id,CONCAT(a.financial_year, '/', CASE  WHEN a.type = 1 THEN 'JO'  ELSE 'PO'  END, '/',  CASE
				WHEN LENGTH(a.orderno) < 6 THEN LPAD(a.orderno, 6, 0) ELSE a.orderno END, IFNULL(a.sub_number, '')) as code
				FROM purchase_order AS a, enterprise AS b, purchase_order_material AS c 
				WHERE  b.id = a.enterprise_id AND a.type = '{po_type}' AND a.status = 2 AND c.pid = a.id 
				AND a.supplier_id = '{supplier_id}' 
				AND a.is_blanket_po = 1 AND a.valid_since <= '{valid_date}' AND a.valid_till >= '{valid_date}'
				GROUP BY a.id """.format(
				po_type=po_type, supplier_id=supplier_id, enterprise_id=enterprise_id,
				valid_date=valid_date)
		pos_by_enterprise_project_supplier = "SELECT DISTINCT supplier_pos.* FROM (%s) AS supplier_pos" % (
			pos_by_enterprise_project_supplier)
		if received_against == 'Job Work':
			pos_by_enterprise_project_supplier = "%s %s" % (
				pos_by_enterprise_project_supplier,
				"INNER JOIN invoice on invoice.job_po_id = supplier_pos.id WHERE invoice.status = 1")
		pos_by_enterprise_project_supplier = "%s %s" % (
			pos_by_enterprise_project_supplier, "ORDER BY supplier_pos.code")

		changed_po_numbers = executeQuery(pos_by_enterprise_project_supplier)
	except Exception as e:
		logger.exception(e)
	return changed_po_numbers


def loadGoodsAlreadyReceivedNumbers(request):
	"""
	Loads the GRN numbers of Goods Already Received based on the parameters

	:param request:
	:return: JSON dumps of the POs queried
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	received_against = request_handler.getPostData('received_against')
	party_id = request_handler.getPostData('selected_supplier')
	grn_no = request_handler.getPostData('grn_no')
	try:
		if received_against == "Sales Return":
			if grn_no != "":
				dc_by_enterprise_project_supplier = """
					SELECT r.invno, a.grnNumber,
					CONCAT(r.financial_year, '/SR/', LPAD(r.receipt_no, 6, 0),
					IFNULL(r.sub_number,''))
					FROM grn as r, grn_material as a
					WHERE a.grnNumber = r.grn_no AND r.party_id = {party_id} AND
					r.inv_type = 1 AND
					r.rec_against = '{received_against}' AND r.status > 0 AND
					IFNULL((
						SELECT SUM(dc_qty) FROM grn_material as gm, grn as g
						WHERE r.grn_no = gm.grnNumber AND gm.grnNumber = g.grn_no AND gm.enterprise_id = {enterprise_id}
							AND gm.item_id = a.item_id AND g.status > -1 ),0) >
					IFNULL((
						SELECT SUM(dc_qty) FROM grn_material as gm, grn as g
						WHERE r.grn_no = gm.rec_grn_id AND gm.grnNumber = g.grn_no AND gm.enterprise_id = {enterprise_id}
							AND gm.item_id = a.item_id AND gm.grnNumber !={grn_no} AND g.status > -1 ),0)""".format(
					received_against=received_against, party_id=party_id, enterprise_id=enterprise_id, grn_no=grn_no)
			else:
				dc_by_enterprise_project_supplier = """
					SELECT r.invno, a.grnNumber, CONCAT(r.financial_year, '/SR/', LPAD(r.receipt_no, 6, 0),
					IFNULL(r.sub_number,''))
					FROM grn as r, grn_material as a
					WHERE a.grnNumber = r.grn_no AND r.party_id = {party_id} AND
					r.inv_type = 1 AND
					r.rec_against = '{received_against}' AND r.status > 0 AND
					IFNULL((
					SELECT SUM(dc_qty) FROM grn_material as gm, grn as g
					WHERE r.grn_no = gm.grnNumber AND gm.grnNumber = g.grn_no AND gm.enterprise_id = {enterprise_id}
						AND gm.item_id = a.item_id AND g.status > -1 ),0) >
					IFNULL((
					SELECT SUM(dc_qty) FROM grn_material as gm, grn as g
					WHERE r.grn_no = gm.rec_grn_id AND gm.grnNumber = g.grn_no AND gm.enterprise_id = {enterprise_id}
						AND gm.item_id = a.item_id AND g.status > -1 ),0)""".format(
					received_against=received_against, party_id=party_id, enterprise_id=enterprise_id)
		else:
			if grn_no != "":
				dc_by_enterprise_project_supplier = """SELECT r.invno, a.grnNumber, 
					CONCAT(r.financial_year, '/GRN/', LPAD(r.receipt_no, 6, 0),IFNULL(r.sub_number,'')) 
					FROM grn as r, grn_material as a 
					where a.grnNumber = r.grn_no AND r.party_id = {party_id} 
					AND r.inv_type = 1 AND 
					r.rec_against = '{received_against}' AND  r.status > 0 AND 
					IFNULL((SELECT SUM(dc_qty) FROM grn_material as gm, grn as g 
					WHERE r.grn_no = gm.grnNumber AND gm.grnNumber = g.grn_no AND gm.enterprise_id = {enterprise_id} 
					AND gm.item_id = a.item_id AND g.status > -1 ),0) > 
					IFNULL((SELECT SUM(dc_qty) FROM grn_material as gm, grn as g 
					WHERE r.grn_no = gm.rec_grn_id AND gm.grnNumber = g.grn_no AND gm.enterprise_id = {enterprise_id} 
					AND gm.item_id = a.item_id AND gm.grnNumber !={grn_no} AND g.status > -1 ),0)""".format(
					received_against=received_against, party_id=party_id, enterprise_id=enterprise_id, grn_no=grn_no)
			else:
				dc_by_enterprise_project_supplier = """SELECT r.invno, a.grnNumber, 
					CONCAT(r.financial_year, '/GRN/', LPAD(r.receipt_no, 6, 0), 
					IFNULL(r.sub_number,'')) 
					FROM grn as r, grn_material as a 
					where a.grnNumber = r.grn_no AND r.party_id = {party_id} 
					AND r.inv_type = 1 AND 
					r.rec_against = '{received_against}' AND  r.status > 0 AND 
					IFNULL((SELECT SUM(dc_qty) FROM grn_material as gm, grn as g 
					WHERE r.grn_no = gm.grnNumber AND gm.grnNumber = g.grn_no AND gm.enterprise_id = {enterprise_id} 
					AND gm.item_id = a.item_id AND g.status > -1),0) > 
					IFNULL((SELECT SUM(dc_qty) FROM grn_material as gm, grn as g 
					WHERE r.grn_no = gm.rec_grn_id AND gm.grnNumber = g.grn_no AND gm.enterprise_id = {enterprise_id} 
					AND gm.item_id = a.item_id AND g.status > -1),0)""".format(
					received_against=received_against, party_id=party_id, enterprise_id=enterprise_id)

		logger.debug("DC Query:%s" % dc_by_enterprise_project_supplier)
		dc = executeQuery(dc_by_enterprise_project_supplier)
	except Exception as e:
		logger.exception(e)
		dc = []
	return HttpResponse(content=simplejson.dumps(dc), mimetype='application/json')


def loadDeliveryChallans(request):  # load DC number
	"""
	Loads the DC placed with the Supplier selected, Enterprise associated with the
	logged-in-user

	:param request:
	:return: JSON dumps of the POs queried
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	grn_no = request_handler.getPostData('grn_no')
	received_against = request_handler.getPostData('received_against')
	party_id = request_handler.getPostData('party_id')
	goods_already_received = request_handler.getPostData('goods_already_received')

	dc_nos = ""
	is_returnable = ""
	rec_type = ""
	inv_type = ""
	if received_against == "Delivery Challan":
		rec_type = " in ('DC')"
		is_returnable = " AND c.is_returnable=1 "
	elif received_against == "Sales Return":
		rec_type = " not in ('DC', 'JDC','JIN')"
		inv_type = " AND g.inv_type = 2 "
	elif received_against == "Job Work":
		rec_type = " in ('Issue')"

	try:
		if party_id != '0':
			if goods_already_received == 'false':
				dc_by_enterprise_supplier = "SELECT a.id, b.code, a.financial_year," \
				                            "IFNULL(a.invoice_code, CONCAT(IFNULL(a.financial_year, 'PROFORMA'), '/', LEFT(a.type, 1), " \
				                            "IFNULL(CONVERT( LPAD(a.invoice_no, 6, '0') USING LATIN1), a.id), IFNULL(a.sub_number, ''))) AS inv_code " \
				                            " FROM invoice AS a, enterprise AS b, invoice_materials AS c " \
				                            "WHERE b.id=a.enterprise_id AND a.status=1 AND a.type %s AND c.invoice_id=a.id AND a.party_id='%s' %s" \
				                            "AND IF(IFNULL((SELECT SUM(acc_qty) FROM grn_material AS gm, grn as g " \
				                            "WHERE g.enterprise_id='%s' AND g.status > -1 AND gm.grnNumber=g.grn_no " \
				                            "AND (gm.po_no=a.id or gm.dc_id = a.id) %s AND gm.item_id=c.item_id), 0) < " \
				                            "(SELECT SUM(qty) FROM invoice_materials AS p  WHERE p.invoice_id=a.id AND p.item_id=c.item_id), 1, 0) " % (
					                            rec_type, party_id, is_returnable, enterprise_id, inv_type)
			else:
				dc_by_enterprise_supplier = "SELECT a.id, b.code, a.financial_year," \
				                            "IFNULL(a.invoice_code, CONCAT(IFNULL(a.financial_year, 'PROFORMA'), '/', LEFT(a.type, 1), " \
				                            "IFNULL(CONVERT( LPAD(a.invoice_no, 6, '0') USING LATIN1), a.id), IFNULL(a.sub_number, ''))) AS inv_code " \
				                            " FROM invoice AS a, enterprise AS b, invoice_materials AS c " \
				                            "WHERE b.id=a.enterprise_id AND a.status=1 AND a.type %s AND c.invoice_id=a.id AND a.party_id='%s' %s" \
				                            "AND IF(IFNULL((SELECT SUM(acc_qty) FROM  grn_material AS gm, grn AS g WHERE gm.rec_grn_id is NULL " \
				                            "AND g.enterprise_id = '%s' AND g.status > 0 AND gm.grnNumber = g.grn_no AND gm.dc_id = a.id " \
				                            "AND gm.item_id = c.item_id AND g.inv_type=1), 0) > IFNULL((SELECT SUM(acc_qty) FROM  grn_material AS gm, grn AS g " \
				                            "WHERE gm.rec_grn_id = g.grn_no AND g.enterprise_id = '%s' AND g.status > - 1 AND gm.dc_id = a.id " \
				                            "AND gm.item_id = c.item_id), 0), 1, 0) " % (
					                            rec_type, party_id, is_returnable, enterprise_id, enterprise_id,)
			if grn_no != "":
				dc_by_enterprise_supplier = "%s %s" % (
					dc_by_enterprise_supplier, "UNION select a.dc_id, b.code, p.financial_year, " \
					                           "IFNULL(p.invoice_code, CONCAT(IFNULL(p.financial_year, 'PROFORMA'), '/', LEFT(p.type, 1), " \
					                           "IFNULL(CONVERT( LPAD(p.invoice_no, 6, '0') USING LATIN1), p.id), IFNULL(p.sub_number, ''))) AS inv_code  "
					                           "FROM grn_material AS a, enterprise AS b,invoice as p " \
					                           "WHERE a.enterprise_id=b.id AND p.id=a.dc_id AND p.party_id='%s' AND a.grnNumber='%s'" % (
						party_id, grn_no))

			dc_by_enterprise_supplier += " GROUP BY inv_code"
			dc_nos = executeQuery(dc_by_enterprise_supplier)
		logger.debug("\n\nDC Details Fetched: %s\n\n" % [dc_nos])
	except Exception as e:
		logger.exception(e)
		dc_nos = []
	return HttpResponse(content=simplejson.dumps(dc_nos), mimetype='application/json')


def loadGRNIssue(request):  # load Issue number
	"""
	Loads the DC placed with the Supplier selected, Enterprise associated with the
	logged-in-user

	:param request:
	:return: JSON dumps of the POs queried
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	grn_no = request_handler.getPostData('receipt_no')
	issued_to = request_handler.getPostData('issued_to')
	groupbyyear = request_handler.getPostData('groupbyyear')
	dc_nos = ""
	try:
		grn_condition = ""
		if grn_no:
			grn_condition = " %s %s" % ("and gm.grnNumber != ", grn_no)
		if issued_to != '0':
			dc_by_enterprise_supplier = "SELECT a.id, b.code, a.financial_year, CONCAT(a.invoice_no,IFNULL(a.sub_number, '')) " \
			                            "FROM invoice AS a, enterprise AS b, invoice_materials AS c " \
			                            "WHERE b.id=a.enterprise_id AND a.status=1 AND a.type='Issue' AND a.enterprise_id='%s' AND c.invoice_id=a.id AND a.issued_to='%s' AND " \
			                            "IF(IFNULL((SELECT SUM(acc_qty) FROM grn_material AS gm, grn as g " \
			                            "WHERE g.enterprise_id='%s' AND g.status > -1 AND gm.grnNumber=g.grn_no %s " \
			                            "AND gm.po_no=a.id AND gm.item_id=c.item_id), 0) < " \
			                            "(SELECT SUM(qty) FROM invoice_materials AS p  WHERE p.invoice_id=a.id AND p.item_id=c.item_id), 1, 0) " \
			                            % (enterprise_id, issued_to, enterprise_id, grn_condition)
			if groupbyyear == 'groupbyyear':
				dc_by_enterprise_supplier = "%s %s" % (dc_by_enterprise_supplier, " group by a.financial_year ")
			else:
				dc_by_enterprise_supplier = "%s AND a.financial_year='%s'  GROUP BY a.id" % (dc_by_enterprise_supplier, groupbyyear)
			dc_nos = executeQuery(dc_by_enterprise_supplier)
		logger.debug("\n\nDC Details Fetched: %s\n\n" % [dc_nos])
	except Exception as e:
		logger.exception(e)
		dc_nos = []
	return HttpResponse(content=simplejson.dumps(dc_nos), mimetype='application/json')


def loadDcMaterials(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	received_against = request_handler.getPostData('received_against')
	dc_ids = request_handler.getPostData('dc_ids')
	receipt_no = request_handler.getPostData('receipt_no')
	if not receipt_no:
		receipt_no = -1
	supplier_id = request_handler.getPostData('supplier_id')

	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	result = []
	dc_item = []

	dc_ids = dc_ids.split(",")
	try:
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
		cur = conn.cursor()
		dc_invoices = SQLASession().query(Invoice).filter(
			Invoice.id.in_(dc_ids), Invoice.enterprise_id == enterprise_id, Invoice.status == 1).all()
		# ReceiptMaterial
		for invoice in dc_invoices:
			item_map = {}
			for item in invoice.items:
				if received_against == 'Sales Return' or received_against == 'Delivery Challan' and item.is_returnable is True:
					rec_qty = 0
					data_query = "select sum(acc_qty), IFNULL(a.alternate_unit_id,0) as alternate_unit_id from " \
						"grn_material as a,grn as b where a.grnNumber=b.grn_no and a.dc_id =" + str(item.invoice_id) + " " \
						"and a.item_id='" + str(item.item_id) + "' and a.make_id =" + str(item.make_id) + " and b.status > -1 " \
						"and a.is_faulty = " + str(item.is_faulty) + " and a.rec_grn_id is NULL AND a.grnNumber != %s" % receipt_no
					cur.execute(data_query)
					data_materials = cur.fetchall()
					if data_materials:
						rec_qty = data_materials[0][0] if data_materials[0][0] is not None else '0'
						if data_materials[0][1] and data_materials[0][1] != '0' and int(data_materials[0][1]) != 0:
							scale_factor = helper.getScaleFactor(
								enterprise_id=item.enterprise_id, item_id=item.item_id,
								alternate_unit_id=item.alternate_unit_id)
							if scale_factor:
								rec_qty = float(data_materials[0][0]) / float(scale_factor) if float(data_materials[0][0]) else 0

					key = "%s%s%s" % (item.item.material_id, item.make_id, item.is_faulty)
					alternate_unit_calculation_is_not_done = True
					if key in item_map:
						dc_item_val = item_map[key]
						quantity = float(item.quantity)
						if item.alternate_unit_id and item.alternate_unit_id != '0' and int(
								item.alternate_unit_id) != 0:
							scale_factor = helper.getScaleFactor(
								enterprise_id=item.enterprise_id, item_id=item.item_id,
								alternate_unit_id=item.alternate_unit_id)
							if scale_factor:
								quantity = float(item.quantity) / float(scale_factor) if float(item.quantity) else 0
								alternate_unit_calculation_is_not_done = False
						dc_item_val['qty'] = str(float(dc_item_val['qty']) + float(quantity))
					else:
						dc_item_val = {"qty": str(float(item.quantity))}
						item_map[key] = dc_item_val
						dc_item.append(dc_item_val)
					dc_item_val['price'] = str(item.item.price)
					dc_item_val['rate'] = str(item.rate)
					if item.alternate_unit_id and item.alternate_unit_id != '0' and int(item.alternate_unit_id) != 0 :
						if alternate_unit_calculation_is_not_done:
							scale_factor = helper.getScaleFactor(
								enterprise_id=item.enterprise_id, item_id=item.item_id,
								alternate_unit_id=item.alternate_unit_id)
							if scale_factor:
								dc_item_val['qty'] = float(dc_item_val['qty']) / float(scale_factor) if dc_item_val[
									'qty'] else 0
								dc_item_val['alternate_unit_id'] = item.alternate_unit_id if item.alternate_unit_id else 0
								dc_item_val['unit_name'] = helper.getUnitName(enterprise_id=enterprise_id, unit_id=item.alternate_unit_id)
								dc_item_val['price'] = float(dc_item_val['price']) * float(scale_factor) if dc_item_val[
									'price'] else 0
								dc_item_val['rate'] = float(dc_item_val['rate']) * float(scale_factor) if dc_item_val[
									'rate'] else 0
								dc_item_val['scale_factor'] = scale_factor if scale_factor else 1
					else:
						dc_item_val['alternate_unit_id'] = 0
						dc_item_val['scale_factor'] = 1
						dc_item_val['unit_name'] = item.item.unit.unit_name

					logger.info("%s - %s" % (float(dc_item_val['qty']), float(rec_qty)))
					dc_item_val['dc_mat_pending_qty'] = float(dc_item_val['qty']) - float(rec_qty)
					dc_item_val['drawing_no'] = item.item.drawing_no
					dc_item_val['item_id'] = item.item_id
					dc_item_val['dc_id'] = item.invoice_id
					dc_item_val['item'] = item.item.name
					dc_item_val['hsn_code'] = item.hsn_code
					dc_item_val['unit_id'] = item.item.unit.unit_id
					dc_item_val['discount'] = str(item.discount)
					dc_item_val['is_service'] = item.item.is_service
					if received_against == 'Delivery Challan':
						dc_item_val['po_id'] = item.invoice_id
					else:
						dc_item_val['po_id'] = 0
					dc_item_val['make_id'] = item.make_id
					dc_item_val['dc_code'] = invoice.getInternalCode() if invoice else ""
					dc_item_val['dc_item_display_name'] = item.item.name
					if item.item.drawing_no:
						dc_item_val['dc_item_display_name'] += " - %s" % item.item.drawing_no
					if item.item.makes_json:
						make_name = helper.constructDifferentMakeName(item.item.makes_json)
						if make_name:
							dc_item_val['dc_item_display_name'] += " [" + make_name + "]"
					if item.is_faulty is True:
						dc_item_val['is_faulty'] = 1
						dc_item_val['dc_item_display_name'] += " [Faulty]"
					else:
						dc_item_val['is_faulty'] = 0
					dc_item_val['mat_type'] = 1 if item.item.is_stocked else 0
					smart_tax = smart_tax_sales_return(
						invoice_id=item.invoice_id, item_id=item.item_id, enterprise_id=enterprise_id,
						make_id=item.make_id, stockable=True, supplier_id=supplier_id)
					dc_item_val['cgst_code'] = smart_tax['cgst_tax']
					dc_item_val['sgst_code'] = smart_tax['sgst_tax']
					dc_item_val['igst_code'] = smart_tax['igst_tax']
		result.append(dc_item)
		conn.close()
	except Exception as e:
		logger.info('Error : %s' % e)
	return HttpResponse(content=simplejson.dumps(result), mimetype='application/json')


def load_issue_material(request):  # load Issue materials
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	result = []
	dc_item = []
	issue_ids = request_handler.getPostData("issue_ids").split(",")
	try:
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT, charset="utf8mb4")
		cur = conn.cursor()
		dc_invoices = SQLASession().query(Invoice).filter(
			Invoice.id.in_(issue_ids), Invoice.enterprise_id == enterprise_id).all()
		# ReceiptMaterial
		for invoice in dc_invoices:
			for item in invoice.items:
				# TODO avoid query in loop
				data_query = "select sum(acc_qty) from grn_material as a,grn as b where a.grnNumber=b.grn_no " \
					"and a.dc_id =" + str(item.invoice_id) + " and a.item_id='" + str(item.item.material_id) + "' " \
					"and a.make_id= " + str(item.make_id) + " and a.is_faulty = " + str(item.is_faulty) + " and b.status >-1"
				cur.execute(data_query)
				data_materials = cur.fetchall()
				dc_mat_pending_qty = float(item.quantity)
				if data_materials and len(data_materials) > 0:
					dc_mat_pending_qty = dc_mat_pending_qty - float(data_materials[0][0] if data_materials[0][0] else 0)
				if dc_mat_pending_qty > 0:
					dc_item_val = {}
					dc_item_val['dc_mat_pending_qty'] = dc_mat_pending_qty
					dc_item_val['drawing_no'] = item.item.drawing_no
					dc_item_val['item_id'] = item.item.material_id
					dc_item_val['dc_id'] = item.invoice_id
					dc_item_val['item'] = item.item.name
					dc_item_val['hsn_code'] = item.hsn_code
					dc_item_val['qty'] = str(item.quantity)
					dc_item_val['unit_name'] = item.item.unit.unit_name
					dc_item_val['unit_id'] = item.item.unit.unit_id
					dc_item_val['rate'] = str(item.rate)
					dc_item_val['discount'] = str(item.discount)
					dc_item_val['price'] = str(item.item.price)
					dc_item_val['po_id'] = None
					dc_item_val['make_id'] = item.make_id
					dc_item_val['dc_code'] = invoice.getInternalCode() if invoice else ""
					item_name = item.item.name
					dc_item_val['dc_item_display_name'] = "%s" % item_name
					dc_item_val['is_service'] = item.item.is_service
					if item.item.drawing_no is not None:
						dc_item_val['dc_item_display_name'] += " -%s" % item.item.drawing_no
					make_name = helper.constructDifferentMakeName(item.item.makes_json)
					dc_item_val['make_name'] = item.item.makes_json if item.item.makes_json else ""
					if make_name:
						dc_item_val['dc_item_display_name'] += " [" + make_name  +"]"
					if item.is_faulty is True:
						dc_item_val['is_faulty'] = 1
						dc_item_val['dc_item_display_name'] += " [Faulty]"
					else:
						dc_item_val['is_faulty'] = 0
					dc_item_val['mat_type'] = 1 if item.item.is_stocked else 0
					dc_item_val['alternate_unit_id'] = 0
					dc_item_val['scale_factor'] = 1
					if item.alternate_unit_id and item.alternate_unit_id != '0' and int(item.alternate_unit_id) != 0:
						scale_factor = helper.getScaleFactor(
							enterprise_id=enterprise_id, item_id=item.item_id, alternate_unit_id=item.alternate_unit_id)
						if scale_factor:
							dc_item_val['dc_mat_pending_qty'] = float(dc_item_val['dc_mat_pending_qty']) / float(scale_factor) if dc_item_val[
								'dc_mat_pending_qty'] else 0
							dc_item_val['qty'] = float(dc_item_val['qty']) / float(scale_factor) if dc_item_val['qty'] else 0
							dc_item_val['unit_name'] = helper.getUnitName(enterprise_id=enterprise_id, unit_id=item.alternate_unit_id)
							dc_item_val['alternate_unit_id'] = item.alternate_unit_id
							dc_item_val['scale_factor'] = scale_factor if scale_factor else 1

					dc_item.append(dc_item_val)
		result.append(dc_item)
		conn.close()
	except Exception as e:
		logger.info('Error : %s' % e)
	return HttpResponse(content=simplejson.dumps(result), mimetype='application/json')


def loadPOList(request):  # load po number
	"""
	Loads the POs placed with the Supplier selected, for the chosen Project of the Enterprise associated with the
	logged-in-user

	:param request:
	:return: JSON dumps of the POs queried
	"""
	request_handler = RequestHandler(request)
	grn_no = request_handler.getPostData('grn_id')

	try:
		pos_by_grn_wise = """SELECT distinct(b.po_no), a.financial_year, concat(a.orderno, IFNULL(a.sub_number, '')) 
			FROM purchase_order AS a, grn_material AS b WHERE b.po_no=a.id AND b.grnNumber='%s' 
			UNION SELECT distinct(c.job_po_id), a.financial_year, a.orderno FROM purchase_order AS a,
			invoice AS c,grn_material as b WHERE c.job_po_id=a.id and b.dc_id = c.id and b.grnNumber ='%s'""" % (grn_no, grn_no)
		pos = executeQuery(pos_by_grn_wise)
	except Exception as e:
		logger.exception(e)
		pos = ""
	return HttpResponse(content=simplejson.dumps(pos), mimetype='application/json')


def loadDCList(request):  # load DC List
	"""
	Loads the DCs placed with the Supplier selected, for the chosen Project of the Enterprise associated with the
	logged-in-user

	:param request:
	:return: JSON dumps of the POs queried
	"""
	request_handler = RequestHandler(request)
	grn_no = request_handler.getPostData('grn_id')
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	goods_already_received = request_handler.getPostData('goods_already_received')
	try:
		if goods_already_received == 'false':
			dcs_by_grn_wise = """SELECT distinct(b.dc_id), a.financial_year, a.invoice_no, a.issued_to
				FROM invoice AS a, grn_material AS b WHERE b.dc_id=a.id AND b.grnNumber='%s'""" % grn_no
		else:
			dcs_by_grn_wise = """SELECT a.rec_grn_id FROM grn as r, grn_material as a 
				where a.grnNumber = {grn_no} AND r.grn_no = {grn_no} AND a.enterprise_id = {enterprise_id}"""\
				.format(enterprise_id=enterprise_id, grn_no=grn_no)
		dc = executeQuery(dcs_by_grn_wise)
	except Exception as e:
		logger.exception(e)
		dc = ""
	return HttpResponse(content=simplejson.dumps(dc), mimetype='application/json')


def __generateTaxJSONDump(tax):
	"""

	:param tax:
	:return: JSON dump holding the Tax details associated with the Receipt in a HTML table-row format
	"""
	compound_suffix = '_compound' if tax.is_compound else ''
	tax_json_dump = [TAX_ROW_DUMP % (
		tax.code, tax.code, tax.code, tax.name, tax.net_rate, compound_suffix, tax.code, tax.net_rate, compound_suffix,
		compound_suffix, tax.code, tax.assess_rate), TAX_BASE_RATE_ROW_DUMP % (tax.code, 'Base Rate', tax.base_rate)]
	for sub_tax in tax.sub_taxes:
		tax_json_dump.append(SUB_TAX_ROW_DUMP % (tax.code, sub_tax.name, sub_tax.rate))
	return tax_json_dump


def loadReceiptTaxes(request):
	"""

	:param request:
	:return: JSON dump holding the details of Taxes associated with the requested Receipt in the form of HTML table rows.
	"""
	request_handler = RequestHandler(request)
	taxes = []
	receipt_no = request_handler.getPostData("receipt_no")
	try:
		grn_taxes = SQLASession().query(ReceiptTax).filter(ReceiptTax.receipt_no == receipt_no).all()
		for grn_tax in grn_taxes:
			taxes.append(__generateTaxJSONDump(grn_tax.tax))
	except Exception as e:
		logger.exception('Fetching of GRN Taxes failed... %s ' % e.message)
	return HttpResponse(content=simplejson.dumps(taxes), mimetype='application/json')


def loadReceiptDocument(request):
	"""
	Loads the document of the Receipt requested into a temporary location and make it accessible in the Front-end

	:param request:
	:return: JSON dump holding the file-path of the document in the temporary location.
	"""
	request_handler = RequestHandler(request)
	if request_handler.isSessionActive() and request_handler.isPostRequest():
		grn_no = request_handler.getPostData('grn_id')
		doc, receipt_code = StoresService().generateGRNDocument(grn_no)
		return HttpResponse(content=simplejson.dumps(getFormattedDocPath(code=receipt_code, id=grn_no)), mimetype='application/json')
	return HttpResponseRedirect(LOGIN_URL)


def loadInvoiceDocument(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	if not request_handler.isPostRequest():
		return HttpResponseRedirect(LOGIN_URL)
	receipt_no = request_handler.getPostData('receipt_no')
	path = ""
	try:
		receipt_attachment = SQLASession().query(ReceiptAttachment).filter(
			ReceiptAttachment.receipt_no == receipt_no).order_by(ReceiptAttachment.revised_on.desc()).first()
		if receipt_attachment is None or receipt_attachment.invoice_doc is None:
			logger.info("Invoice not available...")
		else:
			logger.info("Invoice Document available...")
			path = GRN_INVOICE_DOC_PATH % (receipt_no, receipt_attachment.invoice_doc_ext)
			invoice_doc = receipt_attachment.invoice_doc
			writeFile(invoice_doc, path)
	except Exception as e:
		logger.info("Invoice Creation failed")
		logger.exception("Invoice creation failed: %s" % e)
	return HttpResponse(content=simplejson.dumps(path), mimetype='application/json')


def fetchChargeInclusionFlags(request):
	"""

	:param request:
	:return:
	"""
	packing_incl = False
	freight_incl = False
	try:
		po_ids = RequestHandler(request=request).getPostData("po_ids")
		if po_ids and len(po_ids) > 0:
			# TODO Improve query by adding filter AND transport > 0 OR packing_forwarding > 0
			po_flags_query = """
				SELECT transport, packing_forwarding 
				FROM purchase_order 
				WHERE id IN (%s) """ % po_ids
			logger.debug("PO Flag query - %s" % po_flags_query)
			po_flags = executeQuery(po_flags_query)
			for flags in po_flags:
				logger.info("Transport Charges Included: %s, Freight Included: %s" % (flags[0], flags[1]))
				packing_incl = packing_incl or (flags[1] > 0)
				freight_incl = freight_incl or (flags[0] > 0)
	except Exception as e:
		logger.info("Packing, Freight Inclusion Flags cannot be fetched - %s" % e)
	return HttpResponse(content=simplejson.dumps([freight_incl, packing_incl]), mimetype='application/json')


def rejectReceipt(request):
	"""

	:param request:
	:return:
	"""
	store_service = StoresService()
	db_session = store_service.stores_dao.db_session
	db_session.begin(subtransactions=True)
	logger.info("Rejecting a Receipt...")
	request_handler = RequestHandler(request)
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	is_flag = False
	if user_id is None:
		user_id = request_handler.getPostData('user_id')
		enterprise_id = request_handler.getPostData('enterprise_id')
		is_flag = True
	else:
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id

	receipt_no = request_handler.getPostData('receipt_no')
	remarks = request_handler.getPostData('remarks')

	can_delete_receipt = True
	reject_notify = False
	try:
		delete_from_db = False
		receipt_to_reject = db_session.query(Receipt).filter(Receipt.receipt_no == receipt_no).first()
		if receipt_to_reject.goods_already_received is False and receipt_to_reject.status :
			can_delete_receipt = store_service.stores_dao.canDeleteReceipt(
				enterprise_id=enterprise_id, receipt_no=receipt_no)
		logger.info("receipt_to_reject.status %s-%s" %(receipt_to_reject.status,receipt_no))
		if receipt_to_reject and can_delete_receipt:
			if receipt_to_reject.status == Receipt.STATUS_DRAFT:
				logger.info("Deleting the Receipt draft - %s" % receipt_no)
				if is_flag:
					response = response_code.success()
					response["response_message"] = 'Receipt Draft: %s has been removed permanently'%receipt_to_reject.receipt_no
				else:
					response = simplejson.dumps(
					'["Receipt Draft: <b>%s</b> has been removed permanently", %s]' % (
						receipt_to_reject.receipt_no, receipt_to_reject.status))
				# setting delete flag is necessary because we can't execute raw query inside a sub transaction
				delete_from_db = True
				reject_notify = True
			elif receipt_to_reject.status in (Receipt.STATUS_APPROVED, Receipt.STATUS_GRN_RETURNED):
				logger.info("Cancelling the Receipt - %s" % receipt_to_reject.getCode())
				receipt_to_reject.status = Receipt.STATUS_REJECTED
				receipt_to_reject.updateRemarks(
					remarks=remarks, user=getUser(enterprise_id=enterprise_id, user_id=user_id))
				db_session.add(receipt_to_reject)
				if is_flag:
					response = response_code.success()
					response["response_message"] = 'GRN No: %s has been successfully rejected'%receipt_to_reject.getCode()
				else:
					response = simplejson.dumps(
						'["GRN No:<b> %s </b>has been successfully rejected",%s]' % (
							receipt_to_reject.getCode(), receipt_to_reject.status))
				reject_notify = True
				if not receipt_to_reject.goods_already_received:
					closing_stock_material_wise_queue_creator.delay(
						item_list=[{"item_id": i.item_id, "quantity": float(i.accepted_qty), "is_faulty": i.is_faulty,
									"enterprise_id": i.enterprise_id} for i in receipt_to_reject.items],
						is_sales=True,
						location_id=receipt_to_reject.location_id)
			else:
				response = simplejson.dumps(
					'["No Action Taken - %s!", %s]' % (receipt_to_reject.getCode(), receipt_to_reject.status))
		else:
			if receipt_to_reject is None:
				if is_flag:
					response = response_code.failure()
					response["response_message"] = 'Receipt - %s Not found!'%receipt_no
				else:
					response = simplejson.dumps('["Receipt - %s Not found!", %s]' % (receipt_no, Receipt.STATUS_APPROVED))
				logger.info("No Receipt found for the draft no - %s" % receipt_no)
			else:
				if is_flag:
					response = response_code.failure()
					response["response_message"] = 'Receipt cannot be deleted due to less stock,1'
				else:
					response = simplejson.dumps('["Receipt cannot be deleted due to less stock",1]')
		db_session.commit()
		logger.info("Rejecting receipt %s" % receipt_to_reject.getCode())
		make_transient(receipt_to_reject)
		attachment_id = 0
		if delete_from_db is True:
			if receipt_to_reject.attachment:
				attachment_id = receipt_to_reject.attachment.attachment_id
			StoresDAO.deleteReceipt(enterprise_id=enterprise_id, receipt_no=receipt_no, attachment_id=attachment_id)
		if reject_notify:
			store_service.notifyReceiptRejectCount(enterprise_id=enterprise_id, sender_id=user_id,
			                                       receipt=receipt_to_reject)
		logger.info("Rejection Successful!!")
	except Exception as e:
		db_session.rollback()
		logger.exception("Rejecting receipt - %s failed - %s" % (receipt_no, e.message))
		if is_flag:
			response = response_code.failure()
			response["response_message"] = 'Attempt to reject the Receipt - %s failed!'%receipt_no
		else:
			response = simplejson.dumps(
				'["Attempt to reject the Receipt - %s failed!", %s]' % (receipt_no, Receipt.STATUS_APPROVED))
	if is_flag:
		return HttpResponse(simplejson.dumps(response), 'content-type=text/json')
	else:
		logger.info("loadng %s" %is_flag)
		return HttpResponse(response)


def load_grn_tag(request):  # load GRN Tags
	request_handler = RequestHandler(request)
	receipt_no = request_handler.getPostData('receipt_no')
	tag_query = """SELECT b.tag,a.tag_id FROM receipt_tags as a,tags as b
						WHERE b.id=a.tag_id and b.enterprise_id=a.enterprise_id AND a.receipt_id=%s""" % receipt_no
	try:
		tag_list = executeQuery(tag_query)
	except Exception as e:
		logger.exception(e)
		tag_list = ""
	response = HttpResponse(content=simplejson.dumps(tag_list), mimetype='application/json')
	return response


def lastUsedSupplierDetails(request):
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	party_id = request_handler.getPostData("party_id")
	query = """SELECT grn_no, party_id, matrecthrough, inv_currency_id, cur_conversion_rate FROM  grn
				WHERE party_id=%s AND enterprise_id=%s
				ORDER BY grn_no DESC LIMIT 1""" % (party_id, enterprise_id)
	grn = executeQuery(query)
	supp_dict = []
	if grn and len(grn) > 0:
		supp_dict = {'trans_mode': grn[0][2], 'currency_id': grn[0][3], 'conversion_rate': grn[0][4]}
	return HttpResponse(content=simplejson.dumps(supp_dict), mimetype='application/json')


def invoicedItems(request):
	invoice_materials_list = ()
	try:
		grn_id = RequestHandler(request).getPostData("receipt_no")
		received_against = SQLASession().query(Receipt.received_against).filter(Receipt.receipt_no == grn_id).first()
		if received_against[0] != 'Sales Return':
			dc_against_invoice_orm = SQLASession().query(
				Material.drawing_no.label('item_code'), Receipt.sub_number, Receipt.received_against,
				ReceiptMaterial.accepted_qty, Receipt.invoice_no, Receipt.invoice_date, Receipt.inward_date,
				Receipt.financial_year, Receipt.receipt_no, Receipt.receipt_code, Make.label.label('make'),
				Material.name.label('item_name'), ReceiptMaterial.is_faulty, MaterialMakeMap.part_no,
				ReceiptMaterial.make_id.label('make_id'), ReceiptMaterial.alternate_unit_id.label('alternate_unit_id'),
				ReceiptMaterial.item_id.label('item_id'), ReceiptMaterial.enterprise_id.label('enterprise_id'),
				Material.is_service.label('is_service'),
				Material.is_stocked.label('is_stock'), Material.makes_json
			).outerjoin(ReceiptMaterial.receipt).outerjoin(
				Make, and_(Make.id == ReceiptMaterial.make_id, Make.enterprise_id == ReceiptMaterial.enterprise_id)
			).outerjoin(MaterialMakeMap, and_(
				MaterialMakeMap.item_id == ReceiptMaterial.item_id
				, MaterialMakeMap.make_id == ReceiptMaterial.make_id
				, MaterialMakeMap.enterprise_id == ReceiptMaterial.enterprise_id)
					).outerjoin(ReceiptMaterial.material).filter(
				ReceiptMaterial.received_grn_id == grn_id, Receipt.status > -1, or_(
					ReceiptMaterial.dc_id == 0, ReceiptMaterial.dc_id.is_(None))).all()
		else:
			dc_against_invoice_orm = SQLASession().query(
				Material.drawing_no.label('item_code'), Receipt.sub_number, Receipt.received_against,
				ReceiptMaterial.accepted_qty, Receipt.invoice_no, Receipt.invoice_date, Receipt.inward_date,
				Receipt.financial_year, Receipt.receipt_no, Receipt.receipt_code, Make.label.label('make'),
				Material.name.label('item_name'), ReceiptMaterial.is_faulty, MaterialMakeMap.part_no,
				ReceiptMaterial.make_id.label('make_id'), ReceiptMaterial.alternate_unit_id.label('alternate_unit_id'),
				ReceiptMaterial.item_id.label('item_id'), ReceiptMaterial.enterprise_id.label('enterprise_id'),
				Material.is_service.label('is_service'),
				Material.is_stocked.label('is_stock')
			).outerjoin(ReceiptMaterial.receipt).outerjoin(
				Make, and_(Make.id == ReceiptMaterial.make_id, Make.enterprise_id == ReceiptMaterial.enterprise_id)
			).outerjoin(MaterialMakeMap, and_(
				MaterialMakeMap.item_id == ReceiptMaterial.item_id
				, MaterialMakeMap.make_id == ReceiptMaterial.make_id
				, MaterialMakeMap.enterprise_id == ReceiptMaterial.enterprise_id)
					).outerjoin(ReceiptMaterial.material).filter(
				ReceiptMaterial.received_grn_id == grn_id, Receipt.status > -1).all()

		invoice_materials_list = []
		for grn in dc_against_invoice_orm:
			grn_dict = grn._asdict()
			grn_dict['is_stock'] = 1 if grn.is_stock else 0
			grn_dict['item_code'] = '%s %s' % ('%s %s' % (grn.item_code, "-") if grn.item_code else "", grn.item_name)
			grn_dict['invoice_date'] = datetime.strptime(str(grn_dict['invoice_date']), '%Y-%m-%d %H:%M:%S').strftime(
				'%b %d,%Y')
			grn_dict['inward_date'] = datetime.strptime(str(grn_dict['inward_date']), '%Y-%m-%d %H:%M:%S').strftime(
				'%b %d,%Y')
			grn_dict['code'] = Receipt.generateReceiptCode(
				receipt_no=grn.receipt_no, receipt_code=grn.receipt_code, received_against=grn.received_against,
				financial_year=grn.financial_year, sub_number=grn.sub_number)
			part_no = ""
			# if grn.make_id != DEFAULT_MAKE_ID:
			# 	part_no = grn.part_no
			# grn_dict['make'] = "%s%s" % (grn.make, (" - %s" % part_no) if part_no else "")
			grn_dict['make'] = helper.constructDifferentMakeName(grn.makes_json)
			quantity = float(grn.accepted_qty)
			if grn.alternate_unit_id and grn.alternate_unit_id != '0' and int(
					grn.alternate_unit_id) != 0:
				scale_factor = helper.getScaleFactor(
					enterprise_id=grn.enterprise_id, item_id=grn.item_id,
					alternate_unit_id=grn.alternate_unit_id)
				if scale_factor:
					quantity = float(grn.accepted_qty) / float(scale_factor) if float(grn.accepted_qty) else 0
				grn_dict['accepted_qty'] = float(quantity)

			invoice_materials_list.append(grn_dict)

	except Exception as e:
		logger.exception("Fetching Invoice Details are Failed %s" % e.message)
	return HttpResponse(content=json.dumps(invoice_materials_list, cls=DatetimeEncoder), mimetype='application/json')


def checkGateInwardNo(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request=request)
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	inward_date = rh.getPostData("inward_date")
	gate_inward_no = rh.getPostData("gate_inward_no")
	receipt_no = rh.getPostData("receipt_no")
	for_date = datetime.strptime("%s 00:00:00" % inward_date, '%Y-%m-%d %H:%M:%S')
	fy_start_day = rh.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.fy_start_day
	[fiscal_year, validation_date] = getFinancialYearForGRN(for_date=for_date, fy_start_day=fy_start_day)
	try:
		response = response_code.success()
		query = SQLASession().query(Receipt.receipt_no).filter(
				Receipt.inward_no == gate_inward_no,or_(and_(
				or_(
					Receipt.financial_year == fiscal_year[0], Receipt.financial_year == fiscal_year[2], Receipt.status == 0),
				validation_date[0] < Receipt.inward_date), and_(
				or_(
					Receipt.financial_year == fiscal_year[1],
					Receipt.financial_year == fiscal_year[3] if len(fiscal_year) == 4 else Receipt.financial_year == fiscal_year[2],
					Receipt.status == 0),
				and_(validation_date[1] > Receipt.inward_date, validation_date[0] < Receipt.inward_date))),
				Receipt.status > -1, Receipt.receipt_no != receipt_no if receipt_no else "", Receipt.enterprise_id == enterprise_id)
		response["receipt_no"] = query.all()
	except Exception as e:
		logger.error(e.message)
		response = response_code.internalError()
		response["custom_message"] = "Failed to validate inward no for this financial year. Please contact Admin!"
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def generateInwardNo(request):
	"""

	:param request:
	:return:
	"""
	rh = RequestHandler(request=request)
	enterprise_id = rh.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	inward_date = rh.getPostData("inward_date")
	for_date = datetime.strptime("%s 00:00:00" % inward_date, '%Y-%m-%d %H:%M:%S')
	fy_start_day = rh.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.fy_start_day
	[fiscal_year, validation_date] = getFinancialYearForGRN(for_date=for_date, fy_start_day=fy_start_day)
	try:
		response = response_code.success()
		query = """
				SELECT MAX(grn.inward_no) from grn 
				where (((grn.financial_year = '{fiscal_yr_1}' OR grn.financial_year = '{fiscal_yr_3}' OR grn.status = 0)	
				AND '{validation_date_1}' <= grn.inward_date) OR ((grn.financial_year = '{fiscal_yr_2}' OR grn.financial_year = '{fiscal_yr_4}' OR grn.status = 0) 
				AND '{validation_date_2}' >= grn.inward_date AND '{validation_date_1}' <= grn.inward_date)) AND grn.status > -1 
				AND grn.enterprise_id  = '{enterprise_id}' AND grn.inward_no REGEXP '^[0-9]+$' """.format(
			fiscal_yr_1=fiscal_year[0], fiscal_yr_2=fiscal_year[1], fiscal_yr_3=fiscal_year[2],
			fiscal_yr_4=fiscal_year[3] if len(fiscal_year) == 4 else fiscal_year[2], validation_date_1=validation_date[0],
			validation_date_2=validation_date[1], enterprise_id=enterprise_id)
		inward_no = executeQuery(query)
		response["inward_no"] = inward_no[0]
	except Exception as e:
		logger.error(e.message)
		response = response_code.internalError()
		response["custom_message"] = "Failed to validate inward no for this financial year. Please contact Admin!"
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')


def getFinancialYearForGRN(for_date=None, fy_start_day='01/04'):
	"""
	Gives the Financial Year(FY) in which the given date falls, depending on a start day provided for the Financial year.

	:param for_date: Datetime object date for which the FY is to be determined.
	:param fy_start_day: Start day string in the form 'dd/mm'.
	:return: Start and end years of the FY in the format 'y1-y2' and respective year in 'yyyy' format.
	"""
	for_date = for_date if for_date else datetime.now()
	fy_start = fy_start_day.split('/')
	fy_start_date = datetime(day=int(fy_start[0]), month=int(fy_start[1]), year=for_date.year)
	fy = []
	if fy_start_day == '01/01':
		fy_start_date += relativedelta(years=-1)
		fy_end_date = fy_start_date + relativedelta(years=+2) + relativedelta(microseconds=-1)
		fy_start_date_1 = fy_start_date + relativedelta(years=1)
		fy_end_date_1 = fy_start_date_1 + relativedelta(years=+2) + relativedelta(microseconds=-1)
		validation_date = [fy_start_date_1, fy_start_date_1 + relativedelta(years=+1)]
		fy.append('%s-%s' % (('%s' % fy_start_date.year)[2:4], ('%s' % fy_end_date.year)[2:4]))
		fy.append('%s-%s' % (('%s' % fy_start_date_1.year)[2:4], ('%s' % fy_end_date_1.year)[2:4]))
		fy.append('%s' % for_date.year)
	else:
		fy_start_date += relativedelta(years=0 if for_date >= fy_start_date else -1)
		fy_end_date = fy_start_date + relativedelta(years=+1) + relativedelta(microseconds=-1)
		fy_start_date_1 = fy_start_date + relativedelta(years=+1 if for_date >= fy_start_date else 0)
		fy_end_date_1 = fy_start_date_1 + relativedelta(years=+1) + relativedelta(microseconds=-1)
		validation_date = [fy_start_date, fy_start_date + relativedelta(years=+1)]
		fy.append('%s-%s' % (('%s' % fy_start_date.year)[2:4], ('%s' % fy_end_date.year)[2:4]))
		fy.append('%s-%s' % (('%s' % fy_start_date_1.year)[2:4], ('%s' % fy_end_date_1.year)[2:4]))
		fy.append('%s' % fy_start_date.year)
		fy.append('%s' % fy_end_date.year)
	return fy, validation_date


def loadReceiptPreview(request):
	"""
	:param request:
	:return: JSON dumps holding the details of Materials associated with the Receipt queried
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	grn_id = request_handler.getPostData('grn_id')
	from erp.icd import backend
	icd_service = backend.ICDService()
	store_service = StoresService()
	db_session = store_service.stores_dao.db_session
	icd_ignore_credit_note = request.session['module_access']['icd_ignore_credit_note']
	try:
		grn = db_session.query(Receipt).filter(Receipt.receipt_no == grn_id).first()
		note_items = []
		note_header_details = [{
			'party_name': grn.supplier.name, 'receipt_no': grn.getCode(), 'invoice_no': grn.invoice_no,
			'invoice_date': grn.invoice_date.strftime("%d-%m-%Y"), 'invoice_value': grn.invoice_value}]
		grn_materials = icd_service.getIcdMaterials(
			enterprise_id=enterprise_id, grn_no=grn_id, received_against=grn.received_against)
		for material in grn_materials:
			note_item = {}
			item_name = material['item_name']
			note_item['cgst_rate'] = material['cgst_rate'] if material['cgst_rate'] else 0
			note_item['sgst_rate'] = material['sgst_rate'] if material['sgst_rate'] else 0
			note_item['igst_rate'] = material['igst_rate'] if material['igst_rate'] else 0
			if material['drawing_no']:
				item_details = item_name + " - " + material['drawing_no']
			else:
				item_details = item_name
			if material['make_id'] != 1:
				item_details = item_details + " <i>[" + material['make_name'] + "]</i>"
			if material['is_faulty'] == 1:
				item_details = item_details + " <i>[Faulty]</i>"

			note_item['description'] = item_details
			note_item['item_id'] = material['item_id']
			note_item['unit_name'] = material['unit_name']
			note_item['unit_id'] = material['unit_id']
			note_item['make_id'] = material['make_id']
			diff_qty = material['dc_qty'] - material['rec_qty']
			is_return_material = False
			price = 0
			if material['dc_id'] is not None and grn.received_against != 'Sales Return':
				is_return_material = True
			if is_return_material:
				price = material['store_price']
			elif not is_return_material:
				if grn.received_against != 'Sales Return':
					price = material['inv_rate']
				else:
					price = material['sales_return_rate']
			if material['po_no']:
				po_price = material['po_price']
			else:
				po_price = material['store_price']

			if grn.received_against == 'Sales Return':
				if material['dc_qty'] > 0:
					new_note_item = {}
					new_note_item.update(note_item)
					new_note_item['qty'] = material['dc_qty']
					new_note_item['reason'] = 'Return'
					new_note_item['rate'] = material['inv_rate']
					new_note_item['note_type'] = 'Cr'
					note_items.append(new_note_item)

			if diff_qty > 0:
				new_note_item = {}
				new_note_item.update(note_item)
				new_note_item['qty'] = diff_qty
				new_note_item['reason'] = 'Shortage'
				new_note_item['rate'] = price
				new_note_item['note_type'] = 'Dr'
				note_items.append(new_note_item)
			if diff_qty < 0 and not icd_ignore_credit_note:
				new_note_item = {}
				new_note_item.update(note_item)
				diff_qty = diff_qty * -1
				new_note_item['qty'] = diff_qty
				new_note_item['reason'] = 'Excess Received'
				new_note_item['rate'] = price
				new_note_item['note_type'] = 'Cr'
				note_items.append(new_note_item)
			if material['rec_qty'] - material['acc_qty'] > 0:
				new_note_item = {}
				new_note_item.update(note_item)
				rejected_qty = material['rec_qty'] - material['acc_qty']
				new_note_item['qty'] = rejected_qty
				new_note_item['reason'] = 'Rejected'
				new_note_item['rate'] = price
				new_note_item['note_type'] = 'Dr'
				note_items.append(new_note_item)
			if material['inv_rate'] > po_price and not is_return_material and grn.received_against != 'Sales Return':
				debitnote_rate_diff = material['inv_rate'] - po_price

				new_note_item = {}
				new_note_item.update(note_item)
				new_note_item['qty'] = material['acc_qty']
				new_note_item['reason'] = 'Rate Difference'
				new_note_item['rate'] = debitnote_rate_diff
				new_note_item['note_type'] = 'Dr'
				note_items.append(new_note_item)
			if material['inv_rate'] < po_price and not icd_ignore_credit_note and not is_return_material and grn.received_against != 'Sales Return':
				creditnote_rate_diff = po_price - material['inv_rate']
				new_note_item = {}
				new_note_item.update(note_item)
				new_note_item['qty'] = material['acc_qty']
				new_note_item['reason'] = 'Rate Difference'
				new_note_item['rate'] = creditnote_rate_diff
				new_note_item['note_type'] = 'Cr'
				note_items.append(new_note_item)
			if material['po_price'] > material['approved_rate'] and not is_return_material and grn.received_against != 'Sales Return':
				new_note_item = {}
				new_note_item.update(note_item)
				new_note_item['qty'] = material['acc_qty']
				new_note_item['reason'] = 'PO Rate Difference'
				new_note_item['rate'] = material['po_price'] - material['approved_rate']
				new_note_item['note_type'] = 'Dr'
				note_items.append(new_note_item)

	except Exception as e:
		logger.exception(e)
		note_items = []
		note_header_details = []
	return HttpResponse(content=simplejson.dumps([note_items, note_header_details]), mimetype='application/json')


def checkValidPOs(request):
	try:
		request_handler = RequestHandler(request)
		receipt_no = request_handler.getPostData('receipt_no')
		receipt = SQLASession().query(Receipt).filter(Receipt.receipt_no == receipt_no).first()
		invalid_pos = ""
		response = response_code.success()
		for item in receipt.items:
			if item.po:
				valid_since = item.po.valid_since.strftime('%Y-%m-%d') if item.po.valid_since is not None else '0000-00-00 00:00:00'
				valid_till = item.po.valid_till.strftime('%Y-%m-%d') if item.po.valid_till is not None else '0000-00-00 00:00:00'
				inv_date = receipt.invoice_date.strftime("%Y-%m-%d") if receipt.invoice_date is not None else '0000-00-00 00:00:00'

				if item.po.is_blanket_po:
					if valid_since <= inv_date <= valid_till:
						response = response_code.success()
					else:
						invalid_pos = invalid_pos + item.po.getCode() + ", "
		if invalid_pos != "":
			response = response_code.failure()
			response['invalid_pos'] = invalid_pos
	except Exception as e:
		logger.exception(e)
		response = response_code.internalError()
		response['custom_message'] = "Failed to check Valid POs"
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getWOMaterials(request):
	logger.info("The Edit Tax Loaded...")
	rh = RequestHandler(request)
	wo_id = rh.getPostData('wo_id')
	item_details = {}
	enterprise_id = rh.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	try:
		logger.info("Fetching purchase order material for {enterprise: %s, po_id:%s}" % (enterprise_id, wo_id))
		query = """SELECT issue_to from purchase_order 
						where id = {wo_id} and enterprise_id  = {enterprise_id} """.format(
			wo_id=wo_id, enterprise_id=enterprise_id)
		issue_to = executeQuery(query)
		if not issue_to:
			query = """SELECT issue_id from order_acknowledgement
					where id = {wo_id} and enterprise_id  = {enterprise_id} """.format(wo_id=wo_id, enterprise_id=enterprise_id)
			issue_to = executeQuery(query)
		invoice_dao = InvoiceService()
		po_materials = invoice_dao.getPOMaterials(enterprise_id=enterprise_id, po_id=wo_id)
		invoice_materials = invoice_dao.getInvoiceMaterials(enterprise_id=enterprise_id, po_id=wo_id)
		item_details['po_materials'] = po_materials
		item_details['invoice_materials'] = invoice_materials
		item_details['issue_to'] = issue_to[0][0]
	except Exception as e:
		logger.exception("WO Material retrieval fails... %s" % e.message)
	return HttpResponse(content=simplejson.dumps(item_details), mimetype='application/json')


def getPoQty(request):
	logger.info("The Get PO Completed Qty Loaded...")
	rh = RequestHandler(request)
	pp_id = rh.getPostData('wo_id')
	enterprise_id = rh.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
	response = {}
	try:
		logger.info("Fetching purchase order material for {enterprise: %s, po_id:%s}" % (enterprise_id, pp_id))
		query = """SELECT SUM(gm.acc_qty), pom.pur_qty AS acc_qty
					FROM purchase_order_material pom
					LEFT JOIN
						grn_material gm ON (gm.po_no = pom.pid AND gm.item_id = pom.item_id)
					LEFT JOIN
    					grn g ON g.grn_no = gm.grnNumber AND g.status > -1
					where pom.enterprise_id={enterprise_id}
					AND pom.pid = {pp_id} limit 1""".format(enterprise_id=enterprise_id, pp_id=pp_id)
		completed_qty = executeQuery(query)
		if completed_qty and completed_qty[0][0] != '' and completed_qty[0][0] != None:
			response["completed_qty"] = completed_qty[0][0]
		else:
			response["completed_qty"] = 0
		if completed_qty and completed_qty[0][1] != '' and completed_qty[0][1] != None:
			response["pp_qty"] = completed_qty[0][1]
		else:
			response["pp_qty"] = 0
	except Exception as e:
		logger.exception("WO Material retrieval fails... %s" % e.message)
	return HttpResponse(content=simplejson.dumps(response), mimetype='application/json')
