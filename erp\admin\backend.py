"""
All the backend modules servicing the User Profile management functionality are held here.
These include:
* UserDAO
* UserService
* UserVO
"""
import base64
import pymysql
import random
import string
import uuid
from datetime import datetime
from sqlalchemy import or_
from sqlalchemy.orm import make_transient

from erp import helper
from erp.admin import logger, enterprise_module_settings
from erp.admin.changelog import InvoiceTemplateChangelog, PurchaseTemplateChangelog
from erp.commons.backend import sendMail
from erp.dao import DataAccessObject
from erp.forms import UserForm, UserPermissionForm, EnterpriseForm, InvoiceTemplateConfigForm, \
	InvoiceTemplateGeneralConfigForm, InvoiceTemplateHeaderConfigForm, InvoiceTemplateItemsConfigForm, \
	InvoiceTemplateSummaryConfigForm, InvoiceTemplateMiscConfigForm
from erp.formsets import UserPermissionFormset, InvoiceTemplateHeaderBannerFormset, \
	InvoiceTemplateFooterBannerFormset
from erp.helper import saveAttachment
from erp.models import User, Module, UserPermission, Enterprise, EnterpriseNotes, \
	EnterpriseClaimHead, EnterpriseExpenseHead, EnterpriseImages, UserClaimHead, \
	UserExpenseHead, InvoiceTemplateConfig, InvoiceTemplateGeneralConfig, InvoiceTemplateHeaderConfig, \
	InvoiceTemplateItemsConfig, InvoiceTemplateSummaryConfig, InvoiceTemplateMiscConfig, EnterpriseRegistrationDetail, \
	ContactDetails, InvoiceTemplateBannerImage, ExpenseParticular, Attachment, EraseLog, EnterpriseContactMap, \
	SalesEstimateTemplateConfig, EnterpriseSubscription, UserImages, UserEnterpriseMap, UserLocationMap
from erp.properties import REGISTERED_INTIMATION_MAIL, REQUEST_MAIL_TEMPLATE
from settings import TEMP_DIR, XSASSIST_MAIL_ID, HOST, USER, PASSWORD, DBNAME, PORT, MongoDbConnect
from util.ftp_helper import FTPUtil
from util.helper import copyFormToEntity, constructFormInitializer, writeFile, readFile

__author__ = 'kalaivanan'

IT_CONFIG_FORM_PREFIX = 'itc'
IT_GENERAL_FORM_PREFIX = 'itg'
IT_HEADER_FORM_PREFIX = 'ith'
IT_ITEMS_FORM_PREFIX = 'iti'
IT_SUMMARY_FORM_PREFIX = 'its'
IT_MISC_FORM_PREFIX = 'itm'
IT_HEADER_BANNER_FORM_PREFIX = 'ithb'
IT_FOOTER_BANNER_FORM_PREFIX = 'itfb'


class UserVO(object):
	"""
	Value Object - designed for the convenience of presenting a User's various info forms and formsets as a single
	encapsulated object - encapsulates UserForm & a UserPermissionFormset objects.
	"""

	def __init__(self, user_form, permission_formset):
		self.user_form = user_form
		self.permission_formset = permission_formset

	def is_valid(self):
		return self.user_form.is_valid() and self.permission_formset.is_valid()

	def __repr__(self):
		return 'User Profile: %s\nPermission Formset: %s' % (self.user_form.as_p(), self.permission_formset.as_p())


class UserDAO(DataAccessObject):
	"""
	All User related Database operations are defined here.
	"""

	def getUserById(self, enterprise_id=None, user_id=None):
		"""

		:param enterprise_id:
		:param user_id:
		:return:
		"""
		query = self.db_session.query(User).filter(User.id == user_id)
		return query.first()

	def getUserByEmail(self, email=None):
		return self.db_session.query(User).filter(User.email == email).first()

	def getEnterprise(self, enterprise_id=None):
		return self.db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()

	def getUserEnterpriseMap(self, user_id=None):
		return self.db_session.query(UserEnterpriseMap).filter(UserEnterpriseMap.user_id == user_id, UserEnterpriseMap.status == 1).first()

	def getUserByName(self, enterprise_id=None, username=None):
		return self.db_session.query(User).filter(User.username == username).first()

	def getClaimHead(self, user_id=None, enterprise_id=None, claim_head_id=None):
		"""

		:param user_id:
		:param enterprise_id:
		:param claim_head_id:
		:return:
		"""
		claim_head_query = self.db_session.query(UserClaimHead).filter(
			UserClaimHead.user_id == user_id,
			UserClaimHead.enterprise_id == enterprise_id,
			UserClaimHead.ledger_id == claim_head_id)
		return claim_head_query.first()

	def getExpenseHead(self, user_id=None, enterprise_id=None, expense_head_id=None):
		"""

		:param user_id:
		:param enterprise_id:
		:param expense_head_id:
		:return:
		"""
		expense_head_query = self.db_session.query(UserExpenseHead).filter(
			UserExpenseHead.user_id == user_id,
			UserExpenseHead.enterprise_id == enterprise_id,
			UserExpenseHead.ledger_id == expense_head_id)
		return expense_head_query.first()

	def getAllUsers(self, enterprise_id=None, exclude_list=()):
		"""
		
		:param enterprise_id: 
		:param exclude_list: 
		:return: 
		"""
		query = self.db_session.query(User).join(UserEnterpriseMap).filter(UserEnterpriseMap.enterprise_id == enterprise_id)
		if exclude_list and len(exclude_list) > 0:
			query = query.filter(User.id.notin_(exclude_list))
		return query.all()

	def saveUser(self, user, permissions=None):
		# In case a different set of permissions is explicitly loaded
		try:
			if user.is_super:
				user.permissions = []
			elif permissions is not None:
				user.permissions = permissions
			self.db_session.add(user)
			logger.debug('Is DB Session disturbed? %s' % self.db_session.dirty)
		except:
			raise

	def deleteUser(self, user):
		"""
		Application level delete is just a soft-delete
		"""
		self.db_session.begin(subtransactions=True)
		try:
			user.is_active = False
			self.saveUser(user)
			self.db_session.commit()
		except:
			self.db_session.rollback()
			raise

	def activateUser(self, user):
		"""
		Application level delete is just a soft-delete
		"""
		self.db_session.begin(subtransactions=True)
		try:
			user.is_active = True
			self.saveUser(user)
			self.db_session.commit()
		except:
			self.db_session.rollback()
			raise

	def activateUserByName(self, username):
		"""
		Application level delete is just a soft-delete
		"""
		user = self.db_session.query(User).filter(User.username == username).first()
		self.activateUser(user)

	def activateUserByEmail(self, email, enterprise_id):
		user = self.db_session.query(User).join(UserEnterpriseMap).filter(
			User.email == email, UserEnterpriseMap.enterprise_id == enterprise_id).first()
		self.activateUser(user)

	def deleteUserByName(self, username):
		"""
		Application level delete is just a soft-delete
		"""
		user = self.db_session.query(User).filter(User.username == username).first()
		self.deleteUser(user)

	def deleteUserByEmail(self, email, enterprise_id):
		"""
		Application level delete is just a soft-delete
		"""
		user = self.db_session.query(User).join(UserEnterpriseMap).filter(
			User.email == email, UserEnterpriseMap.enterprise_id == enterprise_id).first()
		self.deleteUser(user)

	def getAllPermissionModules(self):
		return self.db_session.query(Module).all()

	def getUserPermission(self, user_id=None, enterprise_id=None, module_code=None):
		return self.db_session.query(UserPermission).filter(
			UserPermission.user_id == user_id, UserPermission.module_code == module_code,
			UserPermission.enterprise_id == enterprise_id).first()

	def getUserByEnterpriseId(self, enterprise_id=None):
		return self.db_session.query(User).filter(User.enterprise_id == enterprise_id).first()

	def update_user_location_map(self, user_id, location_id, enterprise_id, status):
		update_status = False
		try:
			self.db_session.begin(subtransactions=True)
			user_map_rec = self.db_session.query(UserLocationMap).filter(
				UserLocationMap.user_id == user_id,
				UserLocationMap.location_id == location_id,
				UserLocationMap.enterprise_id == enterprise_id).first()
			if user_map_rec:
				user_map_rec.status = status
			else:
				user_map_rec = UserLocationMap(user_id=user_id, location_id=location_id,
											   enterprise_id=enterprise_id, status=status)
			self.db_session.add(user_map_rec)
			self.db_session.commit()
			update_status = True
		except Exception as e:
			logger.info("Failed to map the location with user: %s", e)
		finally:
			self.db_session.close()
		return update_status

	def update_user_project_map(self, user_id, enterprise_id, status):
		update_status = False
		try:
			self.db_session.begin(subtransactions=True)
			user_map_rec = self.db_session.query(UserEnterpriseMap).filter(
				UserEnterpriseMap.user_id == user_id,
				UserEnterpriseMap.enterprise_id == enterprise_id
			).first()
			if user_map_rec:
				user_map_rec.status = status
			else:
				user_map_rec = UserEnterpriseMap(user_id=user_id, enterprise_id=enterprise_id, status=status)
			self.db_session.add(user_map_rec)
			self.db_session.commit()
			update_status = True
		except Exception as e:
			logger.info("Failed to map the project with user: %s", e)
		finally:
			self.db_session.close()
		return update_status


class UserService:
	"""
	Service class that handles the business logic of the User Profile management module
	"""

	def __init__(self):
		"""

		"""
		self.user_dao = UserDAO()

	def constructUserVO(self, email=None, enterprise_id=None):
		"""
		Construct a consolidated UserVO for a given 'email', representing the User's complete Profile, that will be
		used throughout the User management process.

		:param email:
		:param enterprise_id:
		:return:an instance object of UserVO, loaded with the profile info of the user identified with the email
		"""
		user = self.user_dao.getUserByEmail(email) if email is not None else User(enterprise_id=enterprise_id)
		user_vo = UserVO(self._generateUserForm(user, enterprise_id), self._generateUserPermissionFormset(user, enterprise_id))
		make_transient(user)
		return user_vo

	def saveUser(self, user_vo=None, username=None, profile_img=None, signature=None, url_base=None, enterprise_id=None, is_project_wise_pl=None):
		"""
		Adds or Updates a user after validating the VO whenever a save action is requested from the UI.

		:param user_vo:
		:param username:
		:param profile_img:
		:param signature:
		:param url_base:
		:param enterprise_id:
		:return: an UserVO instance, loaded with either the saved User info or the errors from validation failure.
		"""
		try:
			last_saved_user_detail = ""
			if user_vo.is_valid():
				self.user_dao.db_session.begin(subtransactions=True)
				try:
					user_queried = self.user_dao.getUserByEmail(email=user_vo.user_form.cleaned_data['email'])
					user_id = user_queried.id if user_queried is not None and user_queried.id is not None else None
					user_to_be_saved = self._convertVOtoEntity(
						user_vo=user_vo, entity=user_queried if user_queried else User())
					if user_to_be_saved.username is None or user_to_be_saved.username == "":
						user_to_be_saved.username = user_to_be_saved.email
					if profile_img:
						logo_as_texts = profile_img.split(",")
						img_extension = logo_as_texts[0][logo_as_texts[0].index('/') + 1:logo_as_texts[0].index(';')]
						image_data = ''
						if len(logo_as_texts) > 1:
							logger.debug("User Image %s (%s) - decoded string - (%s) - ext - %s" % (
								logo_as_texts[1], len(logo_as_texts[1]), len(base64.decodestring(logo_as_texts[1])),
								img_extension))
							image_data = logo_as_texts[1]
						profile_img = base64.decodestring(image_data)
						profile_img_ext = img_extension
					else:
						profile_img = ""
						profile_img_ext = ""
					if signature:
						logo_as_texts = signature.split(",")
						signature_extension = logo_as_texts[0][logo_as_texts[0].index('/') + 1:logo_as_texts[0].index(';')]
						image_data = ''
						if len(logo_as_texts) > 1:
							logger.debug("User Image %s (%s) - decoded string - (%s) - ext - %s" % (
								logo_as_texts[1], len(logo_as_texts[1]), len(base64.decodestring(logo_as_texts[1])),
								signature_extension))
							image_data = logo_as_texts[1]
						signature = base64.decodestring(image_data)
						signature_ext = signature_extension
					else:
						signature = ""
						signature_ext = ""
					user_to_be_saved.images = UserImages(
							enterprise_id=user_to_be_saved.enterprise_id, profile_img=profile_img,
							profile_img_ext=profile_img_ext, signature=signature, signature_ext=signature_ext)
					user_enterprise_map_list = user_to_be_saved.user_enterprise_map
					if not user_enterprise_map_list:
						user_enterprise_map = UserEnterpriseMap(enterprise_id=user_to_be_saved.enterprise_id, status=1)
						user_to_be_saved.user_enterprise_map = [user_enterprise_map]
					self.user_dao.saveUser(user_to_be_saved)
					self.user_dao.db_session.commit()
					message = "Your access permissions has been modified by %s " % username
					from erp.auth.backend import LoginService
					login_service = LoginService()
					if user_id is None:
						response = login_service.verify_and_set_password(
							url_base=url_base, user_email=user_to_be_saved.email, reset_password=False)
						last_saved_user_detail = response["custom_message"]
					else:
						last_saved_user_detail = """User <b>{first_name} {last_name} ({email})</b> has been updated successfully.""".format(
							first_name=user_to_be_saved.first_name, last_name=user_to_be_saved.last_name, email=user_to_be_saved.email)
					login_service.notifyPasswordChangedToAllMyDevices(
						user=user_to_be_saved, message=message, action="permission", enterprise_id=enterprise_id)
				except:
					self.user_dao.db_session.rollback()
					raise
			logger.info('User Errors: %s\nPermission Errors: %s' % (
				user_vo.user_form.errors, user_vo.permission_formset.errors))
			return last_saved_user_detail, user_vo
		except:
			raise

	def deleteUser(self, user_email, enterprise_id):
		"""
		Performs the delete operation on request from the UI. The delete here is a soft-delete, i.e., setting the
		is_active flag as False

		:param user_email: of the User to be deleted
		:param enterprise_id:
		:return: void
		"""
		self.user_dao.deleteUserByEmail(user_email, enterprise_id)

	def activateUser(self, user_email, enterprise_id):
		"""
		Performs the delete operation on request from the UI. The delete here is a soft-delete, i.e., setting the
		is_active flag as False

		:param user_email: of the User to be deleted
		:param enterprise_id:
		:return: void
		"""
		self.user_dao.activateUserByEmail(user_email, enterprise_id)

	@staticmethod
	def _generateUserForm(user=User(), enterprise_id=None):
		"""
		Populates a UserForm withe the information of the User object specified, and returns the same to be used for
		HTML rendering
		:param user:
		:param enterprise_id:
		:return:
		"""
		initial = {
			'id': user.id, 'username': user.username, 'first_name': user.first_name,
			'last_name': user.last_name, 'email': user.email, 'password': user.password,
			'is_super': user.is_super, 'enterprise_id': enterprise_id,
			'claim_heads': [claim_head.ledger_id for claim_head in user.claim_heads],
			'expense_heads': [expense_head.ledger_id for expense_head in user.expense_heads],
			'landing_url': user.landing_url}
		if user.images:
			if user.images.profile_img and user.images.profile_img != "":
				logger.debug(
					"Profile Image base64: %s Ext: %s" % (base64.encodestring(user.images.profile_img), user.images.profile_img_ext))
				initial['profile_img'] = "data:image/%s;base64,%s" % (
					user.images.profile_img_ext, base64.encodestring(user.images.profile_img))
			else:
				initial['profile_img'] = ""
			if user.images.signature and user.images.signature != "":
				logger.debug(
					"Signature base64: %s Ext: %s" % (base64.encodestring(user.images.signature), user.images.signature_ext))
				initial['signature'] = "data:image/%s;base64,%s" % (
					user.images.signature_ext, base64.encodestring(user.images.signature))
			else:
				initial['signature'] = ""
		else:
			initial['profile_img'] = ""
			initial['signature'] = ""
		user_form = UserForm(prefix='user', enterprise_id=enterprise_id, initial=initial)
		return user_form

	def _generateUserPermissionFormset(self, user, enterprise_id=None):
		"""
		Generates a formset holding the specified User's Permissions, to be rendered in the HTML format in the UI.

		:param user:
		:return:
		"""
		logger.debug('Generating formset for: %s' % user.permissions)
		permission_list = self.generatePermissionsStub(
			user, enterprise_id) if user.username == '' or user.is_super or user.permissions == [] else user.getAllPermissions()
		initial_perm_values = []
		module_codes = []
		module_urls = self.user_dao.db_session.query(Module.code, Module.landing_url).order_by(Module.code)
		module_urls_dict = {}
		for _url in module_urls:
			module_urls_dict[_url.code] = _url.landing_url

		for permission in permission_list:
			module_codes.append(permission.module_code)
			initial_perm_values.append({
				'user_id': permission.user_id, 'module_code': permission.module_code,
				'landing_url': module_urls_dict[permission.module_code], 'enterprise_id': enterprise_id,
				'bit_flags': permission.getAllAccessFlags()})

		if not user.is_super:
			action_modules = self.user_dao.db_session.query(Module).order_by(Module.action)
			for action_module in action_modules:
				if action_module.code not in module_codes:
					initial_perm_values.append({
						'user_id': user.id, 'module_code': action_module.code, 'enterprise_id': enterprise_id,
						'landing_url': action_module.landing_url, 'bit_flags': (0, 0, 0, 0, 0)})
		logger.info("Is Super User: %s \nPermissions : %s" % (user.is_super, permission_list))
		return UserPermissionFormset(initial=initial_perm_values, prefix='permission')

	def _convertVOtoEntity(self, user_vo=None, entity=None):
		"""
		Reconstruct the User information available with a UserVO to a persist-able object.

		:param user_vo:
		:param entity:
		:return: an ORM persist-able User object
		"""
		entity = self.__copyUserFormToEntity(form=user_vo.user_form, entity=entity)
		self.__extractPermissionsFromFormset(user_vo.permission_formset, entity)
		logger.info('Constructed Entity: %s' % entity)
		return entity

	def __copyUserFormToEntity(self, form=UserForm(), entity=None):
		"""
		Copy the User Profile information available with a UserForm to a persist-able User object passed along.
		Copying will enable updating the User info in the DB using ORM

		:param form: the form holding the user edit, probably got as POST request from the Front-end
		:param entity: an entity in the ORM session
		:return: the copied User entity
		"""
		# FIXME: Very crude manner to identify a new entity
		if not entity or not entity.id:
			entity = copyFormToEntity(form, entity, exclude_field_list=['password', 'claim_heads', 'expense_heads'])
			entity.setPassword(''.join(random.choice(string.digits) for _ in range(6)))
		else:
			logger.info('User Id: %s, Form User Id: %s' % (entity.id, form.cleaned_data['id']))
			entity.id = form.cleaned_data['id']
			entity.enterprise_id = form.cleaned_data['enterprise_id']
			entity.username = form.cleaned_data['username']
			entity.email = form.cleaned_data['email']
			entity.first_name = form.cleaned_data['first_name']
			entity.last_name = form.cleaned_data['last_name']
			entity.is_super = form.cleaned_data['is_super']
			entity.landing_url = form.cleaned_data['landing_url']
		claim_heads, expense_heads = [], []
		for claim_head_id in form.cleaned_data['claim_heads']:
			claim_head = self.user_dao.getClaimHead(
				user_id=entity.id, enterprise_id=entity.enterprise_id, claim_head_id=claim_head_id)
			claim_head = claim_head if claim_head else UserClaimHead(
				user_id=entity.id, enterprise_id=entity.enterprise_id, ledger_id=claim_head_id)
			claim_head.user = entity
			claim_heads.append(claim_head)
		for expense_head_id in form.cleaned_data['expense_heads']:
			expense_head = self.user_dao.getExpenseHead(
				user_id=entity.id, enterprise_id=entity.enterprise_id, expense_head_id=expense_head_id)
			expense_head = expense_head if expense_head else UserExpenseHead(
				user_id=entity.id, enterprise_id=entity.enterprise_id, ledger_id=expense_head_id)
			expense_head.user = entity
			expense_heads.append(expense_head)
		entity.claim_heads = claim_heads
		entity.expense_heads = expense_heads
		logger.info('User Profile Captured from Form: %s' % entity)
		return entity

	@staticmethod
	def __copyPermissionFormToEntity(form=UserPermissionForm(), entity=None, user_entity=None):
		"""
		Copy the UserPermission information available with a UserPermissionForm to a persist-able UserPermission object
		passed along. Copying will enable updating the UserPermission info in the DB using ORM.

		:param form: the form holding the user edit, probably got as POST request from the Front-end
		:param entity: an entity in the ORM session
		:return: the copied UserPermission entity
		"""
		entity = copyFormToEntity(
			form, entity, exclude_field_list=['bit_flags', 'user_id']) if entity is not None else UserPermission(
			module_code=form.cleaned_data['module_code'], enterprise_id=form.cleaned_data['enterprise_id'])
		entity.user = user_entity
		entity.bit_flags = sum(int(i) for i in form.cleaned_data['bit_flags'])
		logger.info('Permission entry captured from Formset: %s' % entity)
		return entity

	def __extractPermissionsFromFormset(self, perm_formset=UserPermissionFormset(), user_entity=None):
		"""
		Extracts a list of persist-able Permission object from a UserPermissionFormset, copying the same into a
		User object passed along.

		:param perm_formset:
		:param user_entity:
		:return: list of User Permission objects reconstructed from the Formset
		"""
		perm_list = []
		for perm_form in perm_formset:
			perm_entity = self.user_dao.getUserPermission(
				user_id=user_entity.id, enterprise_id=perm_form.cleaned_data['enterprise_id'],
				module_code=perm_form.cleaned_data['module_code'])
			logger.info('Perm entity queried for user-id %s and module-code %s: %s' % (
				user_entity.id, perm_form.cleaned_data['module_code'], perm_entity))
			perm_list.append(self.__copyPermissionFormToEntity(perm_form, perm_entity, user_entity))
		return perm_list

	def generatePermissionsStub(self, user=None, enterprise_id=None):
		"""
		Generates Stub entries for all the missing Module-UserPermission entries that are not yet defined.

		:param user:
		:return:
		"""
		if user.username == '' or user.is_super or user.permissions == []:
			modules = self.user_dao.getAllPermissionModules()
			permissions = []
			logger.info('Default permission : %d' % (31 if user.is_super else 0))
			for _module in modules:
				permissions.append(
					UserPermission(
						user_id=user.id, module_code=_module.code, enterprise_id=enterprise_id,
						bit_flags=31 if user.is_super else 0))
			logger.debug(permissions)
			return permissions
		else:
			return user.getAllPermissions()

	def constructUserMap(self, user=None, enterprise_id=None):
		"""
		Form a user data array
		:param user: user object from Model
		:param enterprise_id: enterprise id from session
		:return:
		"""
		latest_subscription = self.user_dao.db_session.query(EnterpriseSubscription).filter(
			EnterpriseSubscription.enterprise_id == enterprise_id,
			EnterpriseSubscription.is_active.is_(True)).order_by(EnterpriseSubscription.till.desc()).first()
		is_enterprise_active = latest_subscription.expired_on > datetime.now()
		enterprise = self.user_dao.db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
		user_map = {
			'id': user.id, 'username': user.username, 'user_email': user.email, 'first_name': user.first_name,
			'last_name': user.last_name, 'is_active': user.is_active, 'is_super': user.is_super,
			'is_enterprise_active': is_enterprise_active,
			'enterprise_id': enterprise_id, 'enterprise_name': enterprise.name, 'permissions': {}}

		for p in user.permissions:
			permit, is_module_active = True, is_enterprise_active
			if p.module_code == "ICD":
				permit = enterprise.setting_flags & enterprise_module_settings['icd_flag'] > 0
				is_module_active = is_enterprise_active and permit
			permission = {
				'module_code': p.module_code, 'view': permit and p.hasViewAccess() > 0,
				'edit': is_module_active and p.hasEditAccess() > 0, 'delete': is_module_active and p.hasDeleteAccess() > 0,
				'approve': is_module_active and p.hasApproveAccess() > 0, 'alert': is_module_active and p.hasNotifyAccess() > 0,
				'permission_value': p.bit_flags, 'enterprise_id': enterprise_id}
			user_map['permissions'][p.module_code] = permission
		return user_map

	def is_super_user(self, user_id, enterprise_id):
		is_super = False
		try:
			user_data =self.user_dao.db_session.query(User).filter(
				User.id == user_id, User.enterprise_id == enterprise_id).first()
			if user_data.is_super:
				is_super = True
		except Exception as e:
			logger.info("Failed to check the super user: %s", e)
		return is_super


class EnterpriseProfileDAO(DataAccessObject):
	"""
	
	"""

	def getProfile(self, enterprise_id=None):
		"""
		
		:param enterprise_id:
		:return:
		"""
		profile_query = self.db_session.query(Enterprise).filter(Enterprise.id == enterprise_id)
		return profile_query.first()

	def getClaimHead(self, enterprise_id=None, claim_head_id=None):
		"""
		
		:param enterprise_id:
		:param claim_head_id:
		:return:
		"""
		claim_head_query = self.db_session.query(EnterpriseClaimHead).filter(
			EnterpriseClaimHead.enterprise_id == enterprise_id,
			EnterpriseClaimHead.ledger_id == claim_head_id)
		return claim_head_query.first()

	def getExpenseHead(self, enterprise_id=None, expense_head_id=None):
		"""
		
		:param enterprise_id:
		:param expense_head_id:
		:return:
		"""
		expense_head_query = self.db_session.query(EnterpriseExpenseHead).filter(
			EnterpriseExpenseHead.enterprise_id == enterprise_id,
			EnterpriseExpenseHead.ledger_id == expense_head_id)
		return expense_head_query.first()

	def getEnterpriseNotes(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		enterprise_notes_query = self.db_session.query(EnterpriseNotes).filter(
			EnterpriseNotes.enterprise_id == enterprise_id)
		return enterprise_notes_query.first()

	def getEnterpriseRegistrationDetail(self, enterprise_id=None, label_id=None, label=None):
		return self.db_session.query(EnterpriseRegistrationDetail).filter(
			EnterpriseRegistrationDetail.enterprise_id == enterprise_id,
			or_(EnterpriseRegistrationDetail.label_id == label_id, EnterpriseRegistrationDetail.label == label)).first()

	def getEnterpriseContactDetail(self, enterprise_id=None, sequence_id=None):
		return self.db_session.query(EnterpriseContactMap).filter(
			EnterpriseContactMap.enterprise_id == enterprise_id,
			EnterpriseContactMap.sequence_id == sequence_id).first()

	def getSETemplateConfig(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		se_template_config_query = self.db_session.query(SalesEstimateTemplateConfig).filter(
			SalesEstimateTemplateConfig.enterprise_id == enterprise_id)
		return se_template_config_query.first()


class EnterpriseProfileService:
	"""
	
	"""

	def __init__(self):
		"""

		"""
		self.profile_dao = EnterpriseProfileDAO()

	def constructEnterpriseProfileForm(self, enterprise_id=None, enterprise=None):
		if not enterprise:
			enterprise = self.profile_dao.getProfile(enterprise_id) if enterprise_id else Enterprise()
		if enterprise.address_2 and enterprise.address_2 != "":
			enterprise.address_1 = enterprise.address_1 + ", \n" + enterprise.address_2
			enterprise.address_2 = ""
		form_initializer = constructFormInitializer(entity=[
			enterprise, enterprise.notes, enterprise.claim_heads,
			enterprise.expense_heads], exclude_field_keys=[
			'setting_flags', 'claim_heads', 'expense_heads', 'gate_inward_no_flags'])
		if enterprise.images and enterprise.images.logo and enterprise.images.logo != "":
			logger.debug(
				"Image base64: %s Ext: %s" % (base64.encodestring(enterprise.images.logo), enterprise.images.ext))
			form_initializer['logo'] = "data:image/%s;base64,%s" % (
				enterprise.images.ext, base64.encodestring(enterprise.images.logo))
		else:
			form_initializer['logo'] = ""
		form_initializer['setting_flags'] = [
			enterprise.setting_flags & 1, enterprise.setting_flags & 2, enterprise.setting_flags & 4,
			enterprise.setting_flags & 8, enterprise.setting_flags & 16]
		form_initializer['gate_inward_no_flags'] = [
			enterprise.gate_inward_no_flags & 1, enterprise.gate_inward_no_flags & 2,
			enterprise.gate_inward_no_flags & 4, enterprise.gate_inward_no_flags & 8]
		form_initializer['claim_heads'] = [claim_head.ledger_id for claim_head in enterprise.claim_heads]
		form_initializer['expense_heads'] = [expense_head.ledger_id for expense_head in enterprise.expense_heads]
		return EnterpriseForm(prefix='enterprise', enterprise_id=enterprise_id, initial=form_initializer)

	def getRegistrationDetails(self, enterprise_id=None):
		enterprise_reg_detail = []
		enterprise_registration_detail = self.profile_dao.db_session.query(
			EnterpriseRegistrationDetail).filter(
			EnterpriseRegistrationDetail.enterprise_id == enterprise_id).order_by(
			EnterpriseRegistrationDetail.label_id).all()
		for item in enterprise_registration_detail:
			enterprise_reg_detail_dict = {"label_id": item.label_id, "label": item.label, "details": item.details}
			enterprise_reg_detail.append(enterprise_reg_detail_dict)
		return enterprise_reg_detail

	def getContactDetails(self, enterprise_id=None, sequence_id=None):
		enterprise_contact_detail = []
		if sequence_id:
			enterprise_contact_details = self.profile_dao.db_session.query(
				EnterpriseContactMap).filter(
				EnterpriseContactMap.enterprise_id == enterprise_id,
				EnterpriseContactMap.sequence_id == sequence_id).order_by(
				EnterpriseContactMap.sequence_id).first()
			enterprise_contact_detail_dict = {
				"sequence_id": enterprise_contact_details.sequence_id, "name": enterprise_contact_details.contact.name,
				"phone_no": enterprise_contact_details.contact.phone_no,
				"email": enterprise_contact_details.contact.email,
				"fax_no": enterprise_contact_details.contact.fax_no,
				"is_whatsapp": enterprise_contact_details.contact.is_whatsapp}
			enterprise_contact_detail.append(enterprise_contact_detail_dict)
		else:
			enterprise_contact_details = self.profile_dao.db_session.query(
				EnterpriseContactMap).filter(
				EnterpriseContactMap.enterprise_id == enterprise_id).order_by(
				EnterpriseContactMap.sequence_id).all()
			for enterprise_contact_map in enterprise_contact_details:
				enterprise_contact_detail_dict = {
					"sequence_id": enterprise_contact_map.sequence_id, "name": enterprise_contact_map.contact.name,
					"phone_no": enterprise_contact_map.contact.phone_no, "email": enterprise_contact_map.contact.email,
					"fax_no": enterprise_contact_map.contact.fax_no,
					"is_whatsapp": enterprise_contact_map.contact.is_whatsapp}
				enterprise_contact_detail.append(enterprise_contact_detail_dict)
		return enterprise_contact_detail

	def saveEnterpriseDetails(self, enterprise_id=None, enterprise_detail=None, user_id=None):
		"""

		:param enterprise_id:
		:param enterprise_detail:
		:param user_id:
		:return:
		"""
		try:
			logger.info("Updating Enterprise Contact Details...")
			updated_enterprise_detail = None
			for enterprise in enterprise_detail:
				updated_enterprise_detail = self.updateEnterpriseDetails(
					enterprise_id=enterprise_id, code=enterprise["code"], name=enterprise["name"],
					address1=enterprise["address1"], city=enterprise["city"], country_code=enterprise["country_code"],
					state=enterprise["state"], pin_code=enterprise["pin_code"],
					fy_start_day=enterprise["fy_start_day"], currency_id=enterprise["currency_id"], user_id=user_id,
					logo=enterprise["logo"])
			return updated_enterprise_detail
		except Exception as e:
			logger.exception("Enterprise Contact Details Update failed - %s" % e.message)
			raise

	def saveEnterpriseContactDetails(self, enterprise_id=None, enterprise_contact_detail=None, user_id=None):
		"""

		:param enterprise_id:
		:param enterprise_contact_detail:
		:param user_id:
		:return:
		"""
		try:
			logger.info("Updating Enterprise Contact Details...")
			updated_contact_detail = []

			for contact_item in enterprise_contact_detail:
				if contact_item["is_deleted"] == 0:
					updated_contact_detail.append(contact_item)
					contact_details = self.updateEnterpriseContactDetails(
						enterprise_id=enterprise_id, sequence_id=contact_item["sequence_id"], name=contact_item["name"],
						email=contact_item["email"], phone_no=contact_item["phone_no"], fax_no=contact_item["fax_no"],
						is_whatsapp=contact_item["is_whatsapp"], user_id=user_id)
					self.updateEnterpriseContactMap(
						contact_id=contact_details.id, enterprise_id=enterprise_id,
						sequence_id=contact_item["sequence_id"])
				else:
					self.deleteEnterpriseContact(
						enterprise_id=enterprise_id, sequence_id=contact_item["sequence_id"])

			primary_contact = self.profile_dao.getEnterpriseContactDetail(enterprise_id=enterprise_id, sequence_id=1)
			if primary_contact:
				enterprise_detail = self.profile_dao.getProfile(enterprise_id=enterprise_id)
				enterprise_detail.contact_id = primary_contact.contact_id
				self.updateEnterpriseContactId(enterprise_id=enterprise_id, contact_id=primary_contact.contact_id)

			return updated_contact_detail
		except Exception as e:
			logger.exception("Enterprise Contact Details Update failed - %s" % e.message)
			raise

	def saveEnterpriseRegistration(self, enterprise_id=None, enterprise_reg_detail=None, user_id=None):
		"""

		:param enterprise_id:
		:param enterprise_reg_detail:
		:param user_id:
		:return:
		"""
		try:
			logger.info("Updating Enterprise Profile...")
			updated_reg_detail = []

			for reg_item in enterprise_reg_detail:
				if reg_item["is_deleted"] == 0:
					updated_reg_detail.append(reg_item)
					self.updateEnterpriseRegistration(
						enterprise_id=enterprise_id, label_id=reg_item["label_id"], label=reg_item["label"],
						details=reg_item["details"], user_id=user_id)
				else:
					self.deleteEnterpriseRegistration(
						enterprise_id=enterprise_id, label_id=reg_item["label_id"])
			return updated_reg_detail
		except Exception as e:
			logger.exception("Enterprise Registration Update failed - %s" % e.message)
			raise

	def updateEnterpriseProfile(self, form=None, user_id=None):
		"""

		:param form:
		:param user_id:
		:return:
		"""
		logger.info("Enterprise Update Service Call..")
		self.profile_dao.db_session.begin(subtransactions=True)
		try:
			entity = self.profile_dao.getProfile(enterprise_id=form.cleaned_data['id'])
			logger.info("Enterprise fetched - %s" % entity)
			enterprise = self._copyProfileFormToEntity(form=form, entity=entity)
			enterprise.last_modified_by = user_id
			self.profile_dao.db_session.add(enterprise)
			logger.info('Is DB Session disturbed? %s' % len(self.profile_dao.db_session.dirty))
			logger.debug('Save enterprise dirty %s' % self.profile_dao.db_session.dirty)
			self.profile_dao.db_session.commit()
			self.profile_dao.db_session.refresh(enterprise)
			if enterprise.is_negative_stock_allowed is False:
				helper.resetNegativeStockToZero(enterprise_id=enterprise.id)
		except Exception as e:
			self.profile_dao.db_session.rollback()
			logger.exception("Update Enterprise profile Failed - %s" % e.message)
			raise

	def updateSubscriptionExtension(self, enterprise_id=None, user_id=None):
		self.profile_dao.db_session.begin(subtransactions=True)
		try:
			latest_subscription = self.profile_dao.db_session.query(EnterpriseSubscription).filter(
				EnterpriseSubscription.enterprise_id == enterprise_id,
				EnterpriseSubscription.is_active.is_(True)).order_by(EnterpriseSubscription.till.desc()).first()
			latest_subscription.extension_requested_by = user_id
			latest_subscription.extension_requested_on = datetime.now()
			self.profile_dao.db_session.commit()
			self.profile_dao.db_session.refresh(latest_subscription)
			return latest_subscription
		except Exception as e:
			self.profile_dao.db_session.rollback()
			raise e

	def updateEnterpriseDetails(
			self, enterprise_id=None, code=None, name=None, address1=None, city=None, country_code=None, state=None, pin_code=None,
			fy_start_day=None, currency_id=None, user_id=None, logo=None):
		"""

		:param enterprise_id:
		:param code:
		:param name:
		:param address1:
		:param city:
		:param country_code:
		:param state:
		:param pin_code:
		:param fy_start_day:
		:param currency_id:
		:param user_id:
		:param logo:
		:return:
		"""
		logger.info("Enterprise detail Update Service Call..")
		self.profile_dao.db_session.begin(subtransactions=True)
		try:
			enterprise_detail = self.profile_dao.getProfile(enterprise_id=enterprise_id)

			if not enterprise_detail:

				enterprise_detail = Enterprise(
					id=enterprise_id, code=code, name=name, address_1=address1, city=city, country_code=country_code, state=state,
					pin_code=pin_code,
					home_currency_id=currency_id, fy_start_day=fy_start_day, created_by=user_id,
					last_modified_by=user_id)
			else:
				enterprise_detail.code = code
				enterprise_detail.name = name
				enterprise_detail.address_1 = address1
				enterprise_detail.address_2 = ""
				enterprise_detail.city = city
				enterprise_detail.country_code = country_code
				enterprise_detail.state = state
				enterprise_detail.pin_code = pin_code
				enterprise_detail.home_currency_id = currency_id
				enterprise_detail.fy_start_day = fy_start_day
				enterprise_detail.last_modified_by = user_id

			if logo:
				logo_as_texts = logo.split(",")
				logo_extension = logo_as_texts[0][logo_as_texts[0].index('/') + 1:logo_as_texts[0].index(';')]
				image_data = ''
				if len(logo_as_texts) > 1:
					logger.debug("Enterprise Image %s (%s) - decoded string - (%s) - ext - %s" % (
						logo_as_texts[1], len(logo_as_texts[1]), len(base64.decodestring(logo_as_texts[1])),
						logo_extension))
					image_data = logo_as_texts[1]
				enterprise_detail.images = EnterpriseImages(logo=base64.decodestring(image_data), ext=logo_extension)
			else:
				enterprise_detail.images = EnterpriseImages(logo="", ext="")

			logger.info("Enterprise Details fetched - %s" % enterprise_detail)
			self.profile_dao.db_session.add(enterprise_detail)
			logger.debug('Save enterprise dirty %s' % self.profile_dao.db_session.dirty)
			self.profile_dao.db_session.commit()
			self.profile_dao.db_session.refresh(enterprise_detail)
			return enterprise_detail
		except Exception as e:
			self.profile_dao.db_session.rollback()
			logger.exception("Update Enterprise detail Failed - %s" % e.message)
			raise

	def updateEnterpriseContactId(
			self, enterprise_id=None, contact_id=None):
		"""

		:param enterprise_id:
		:param contact_id:
		:return:
		"""
		logger.info("Enterprise Contact id details Update Service Call..")
		self.profile_dao.db_session.begin(subtransactions=True)
		try:
			enterprise_detail = self.profile_dao.getProfile(enterprise_id=enterprise_id)
			enterprise_detail.contact_id = contact_id
			self.profile_dao.db_session.add(enterprise_detail)
			self.profile_dao.db_session.commit()
		except Exception as e:
			self.profile_dao.db_session.rollback()
			logger.exception("Update Enterprise Contact id details Failed - %s" % e.message)
			raise

	def updateEnterpriseContactDetails(
			self, enterprise_id=None, sequence_id=None, name=None, email=None, phone_no=None, fax_no=None,
			is_whatsapp=None, user_id=None):
		"""

		:param enterprise_id:
		:param sequence_id:
		:param name:
		:param email:
		:param phone_no:
		:param fax_no:
		:param is_whatsapp:
		:param user_id:
		:return:
		"""
		logger.info("Enterprise Contact detail Update Service Call..")
		self.profile_dao.db_session.begin(subtransactions=True)
		try:
			enterprise_contact_detail = self.profile_dao.getEnterpriseContactDetail(enterprise_id=enterprise_id,
			                                                                        sequence_id=sequence_id)

			if not enterprise_contact_detail:
				contact_detail = ContactDetails(
					name=name, email=email, phone_no=phone_no, fax_no=fax_no,
					is_whatsapp=is_whatsapp, created_by=user_id, last_modified_by=user_id, enterprise_id=enterprise_id)
			else:
				contact_detail = enterprise_contact_detail.contact
				contact_detail.name = name
				contact_detail.email = email
				contact_detail.phone_no = phone_no
				contact_detail.fax_no = fax_no
				contact_detail.is_whatsapp = is_whatsapp
				contact_detail.last_modified_by = user_id
				contact_detail.enterprise_id=enterprise_id
			logger.info("Enterprise Contact Details fetched - %s" % contact_detail)
			self.profile_dao.db_session.add(contact_detail)
			logger.debug('Save enterprise dirty %s' % self.profile_dao.db_session.dirty)
			self.profile_dao.db_session.commit()
			self.profile_dao.db_session.refresh(contact_detail)
			return contact_detail
		except Exception as e:
			self.profile_dao.db_session.rollback()
			logger.exception("Update Enterprise Contact detail Failed - %s" % e.message)
			raise

	def validateSalesPerson(
			self, enterprise_id=None, name=None, email=None, phone_no=None, fax_no=None):
		"""

		:param enterprise_id:
		:param name:
		:param email:
		:param phone_no:
		:param fax_no:
		:return:
		"""
		logger.info("Validate Contact detail Service Call..")
		try:
			contact_details = self.profile_dao.db_session.query(
				ContactDetails.id).filter(
				ContactDetails.name == name, ContactDetails.phone_no == phone_no, ContactDetails.fax_no == fax_no,
				ContactDetails.email == email).all()

			is_exist = False
			if len(contact_details) > 0:
				if len(contact_details[0]) > 1:
					for contact_id in contact_details[0]:
						enterprise_details = self.profile_dao.db_session.query(EnterpriseContactMap).filter(EnterpriseContactMap.enterprise_id == enterprise_id, EnterpriseContactMap.contact_id == contact_id).first()
						if enterprise_details is not None:
							is_exist = True
				else:
					enterprise_details = self.profile_dao.db_session.query(EnterpriseContactMap).filter(
						EnterpriseContactMap.enterprise_id == enterprise_id,
						EnterpriseContactMap.contact_id == contact_details[0][0]).first()
					if enterprise_details is not None:
						is_exist = True
			return is_exist
		except Exception as e:
			logger.exception("Validation of Sales person Failed - %s" % e.message)
			raise

	def updateEnterpriseContactMap(
			self, contact_id=None, enterprise_id=None, sequence_id=None):
		"""

		:param enterprise_id:
		:param sequence_id:
		:return:
		"""
		logger.info("Enterprise Contact Map Update Service Call..")
		self.profile_dao.db_session.begin(subtransactions=True)
		try:
			enterprise_contact_detail = self.profile_dao.getEnterpriseContactDetail(
				enterprise_id=enterprise_id, sequence_id=sequence_id)

			if not enterprise_contact_detail:
				enterprise_contact_detail = EnterpriseContactMap(
					contact_id=contact_id, enterprise_id=enterprise_id, sequence_id=sequence_id)
			else:
				enterprise_contact_detail.contact_id = contact_id
				enterprise_contact_detail.enterprise_id = enterprise_id
				enterprise_contact_detail.sequence_id = sequence_id
			logger.info("Enterprise Contact Map fetched - %s" % enterprise_contact_detail)
			self.profile_dao.db_session.add(enterprise_contact_detail)
			logger.debug('Save enterprise dirty %s' % self.profile_dao.db_session.dirty)
			self.profile_dao.db_session.commit()
			self.profile_dao.db_session.refresh(enterprise_contact_detail)
		except Exception as e:
			self.profile_dao.db_session.rollback()
			logger.exception("Update Enterprise Contact Map Failed - %s" % e.message)
			raise

	def deleteEnterpriseContact(self, enterprise_id=None, sequence_id=None):
		"""

		:param enterprise_id:
		:param sequence_id:
		:return:
		"""
		logger.info('Enterprise Contact detail Delete Service Call..')
		self.profile_dao.db_session.begin(subtransactions=True)
		try:
			contact_id = self.profile_dao.db_session.query(EnterpriseContactMap.contact_id).filter(
				EnterpriseContactMap.enterprise_id == enterprise_id,
				EnterpriseContactMap.sequence_id == sequence_id).first()
			self.profile_dao.db_session.query(ContactDetails).filter(
				ContactDetails.id == contact_id[0]).delete()
			self.profile_dao.db_session.commit()
		except Exception as e:
			self.profile_dao.db_session.rollback()
			logger.exception("Delete Enterprise Contact Failed - %s" % e.message)

	def updateEnterpriseRegistration(self, enterprise_id=None, label_id=None, label=None, details=None, user_id=None):
		"""

		:param enterprise_id:
		:param label_id:
		:param label:
		:param details:
		:param user_id:
		:return:
		"""
		logger.info("Enterprise Registration detail Update Service Call..")
		self.profile_dao.db_session.begin(subtransactions=True)
		try:
			reg_detail = self.profile_dao.getEnterpriseRegistrationDetail(
				enterprise_id=enterprise_id, label_id=label_id, label=label)
			if not reg_detail:
				reg_detail = EnterpriseRegistrationDetail(
					enterprise_id=enterprise_id, label_id=label_id, label=label, details=details,
					created_by=user_id, last_modified_by=user_id)
			else:
				reg_detail.label = label
				reg_detail.details = details
				reg_detail.last_modified_by = user_id

			logger.info("Enterprise fetched - %s" % reg_detail)
			self.profile_dao.db_session.add(reg_detail)
			logger.debug('Save enterprise dirty %s' % self.profile_dao.db_session.dirty)
			self.profile_dao.db_session.commit()
			self.profile_dao.db_session.refresh(reg_detail)
		except Exception as e:
			self.profile_dao.db_session.rollback()
			logger.exception("Update Enterprise Registration detail Failed - %s" % e.message)
			raise

	def deleteEnterpriseRegistration(self, enterprise_id=None, label_id=None):
		"""

		:param enterprise_id:
		:param label_id:
		:return:
		"""
		logger.info('Enterprise Registration detail Delete Service Call..')
		self.profile_dao.db_session.begin(subtransactions=True)
		try:
			self.profile_dao.db_session.query(EnterpriseRegistrationDetail).filter(
				EnterpriseRegistrationDetail.enterprise_id == enterprise_id,
				EnterpriseRegistrationDetail.label_id == label_id).delete()
			self.profile_dao.db_session.commit()
		except Exception as e:
			self.profile_dao.db_session.rollback()
			logger.exception("Delete Enterprise Registration Failed - %s" % e.message)

	def updateEnterprisePreferences(
			self, enterprise_id=None, setting_flags=None, is_negative_stock_allowed=None,
			is_gate_inward_no_mandatory=None,
			gate_inward_no_flags=None, is_purchase_order_mandatory=None, user_id=None, is_multiple_units=None,
			is_delivery_schedule=None, is_icd_request_acknowledgement=None, is_blanket_po=None,
			is_price_modification_disabled=None):
		"""

		:param enterprise_id:
		:param setting_flags:
		:param is_negative_stock_allowed:
		:param is_gate_inward_no_mandatory:
		:param gate_inward_no_flags:
		:param is_purchase_order_mandatory:
		:param user_id:
		:param is_multiple_units:
		:param is_delivery_schedule:
		:param is_icd_request_acknowledgement:
		:param is_blanket_po:
		:param is_price_modification_disabled:
		:return:
		"""
		logger.info("Enterprise Preferences Update Service Call..")
		self.profile_dao.db_session.begin(subtransactions=True)
		try:
			enterprise_detail = self.profile_dao.getProfile(enterprise_id=enterprise_id)

			enterprise_detail.setting_flags = setting_flags
			enterprise_detail.is_negative_stock_allowed = is_negative_stock_allowed
			enterprise_detail.is_gate_inward_no_mandatory = is_gate_inward_no_mandatory
			enterprise_detail.gate_inward_no_flags = gate_inward_no_flags
			enterprise_detail.is_purchase_order_mandatory = is_purchase_order_mandatory
			enterprise_detail.last_modified_by = user_id
			enterprise_detail.is_multiple_units = is_multiple_units
			enterprise_detail.is_delivery_schedule = is_delivery_schedule
			enterprise_detail.is_icd_request_acknowledgement = is_icd_request_acknowledgement
			enterprise_detail.is_blanket_po = is_blanket_po
			enterprise_detail.is_price_modification_disabled = is_price_modification_disabled

			logger.info("Enterprise Details fetched - %s" % enterprise_detail)
			self.profile_dao.db_session.add(enterprise_detail)
			logger.debug('Save enterprise dirty %s' % self.profile_dao.db_session.dirty)
			self.profile_dao.db_session.commit()
			self.profile_dao.db_session.refresh(enterprise_detail)
			if enterprise_detail.is_negative_stock_allowed is False:
				helper.resetNegativeStockToZero(enterprise_id=enterprise_id)
			return enterprise_detail
		except Exception as e:
			self.profile_dao.db_session.rollback()
			logger.exception("Update Enterprise Preferences Failed - %s" % e.message)
			raise

	def updateEnterpriseClaimHeads(self, enterprise_id=None, claim_heads=None):
		"""

		:param enterprise_id:
		:param claim_heads:
		:return:
		"""
		logger.info("Enterprise Claim Heads Update Service Call..")
		self.profile_dao.db_session.begin(subtransactions=True)
		try:
			claim_head = None
			profiled_claim_head = self.profile_dao.db_session.query(EnterpriseClaimHead.ledger_id).filter(
				EnterpriseClaimHead.enterprise_id == enterprise_id).all()
			claim_head_ledgers = [str(c.ledger_id) for c in profiled_claim_head]

			added_claim_heads = (list(set(claim_heads) - set(claim_head_ledgers)))
			removed_claim_heads = (list(set(claim_head_ledgers) - set(claim_heads)))

			for claim_head_id in added_claim_heads:
				if claim_head_id != "":
					claim_head = EnterpriseClaimHead(enterprise_id=enterprise_id, ledger_id=claim_head_id)
					self.profile_dao.db_session.add(claim_head)

			for claim_head_id in removed_claim_heads:
				if claim_head_id != "":
					self.profile_dao.db_session.query(EnterpriseClaimHead).filter(
						EnterpriseClaimHead.enterprise_id == enterprise_id,
						EnterpriseClaimHead.ledger_id == claim_head_id).delete()
			logger.info("Enterprise Claim Heads fetched - %s" % claim_head)

			logger.debug('Save enterprise expenses %s' % self.profile_dao.db_session.dirty)
			self.profile_dao.db_session.commit()
			if claim_head:
				self.profile_dao.db_session.refresh(claim_head)
		except Exception as e:
			self.profile_dao.db_session.rollback()
			logger.exception("Update Enterprise Claim Heads Failed - %s" % e.message)
			raise

	def updateEnterpriseExpenseHeads(self, enterprise_id=None, expense_heads=None):
		"""

		:param enterprise_id:
		:param expense_heads:
		:return:
		"""
		logger.info("Enterprise Expense Heads Update Service Call..")
		self.profile_dao.db_session.begin(subtransactions=True)
		try:
			expense_head = None
			profiled_expense_head = self.profile_dao.db_session.query(EnterpriseExpenseHead.ledger_id).filter(
				EnterpriseExpenseHead.enterprise_id == enterprise_id).all()
			expense_head_ledgers = [str(c.ledger_id) for c in profiled_expense_head]

			added_expense_heads = (list(set(expense_heads) - set(expense_head_ledgers)))
			removed_expense_heads = (list(set(expense_head_ledgers) - set(expense_heads)))

			for expense_head_id in added_expense_heads:
				if expense_head_id != "":
					expense_head = EnterpriseExpenseHead(enterprise_id=enterprise_id, ledger_id=expense_head_id)
					self.profile_dao.db_session.add(expense_head)

			for expense_head_id in removed_expense_heads:
				if expense_head_id != "":
					self.profile_dao.db_session.query(EnterpriseExpenseHead).filter(
						EnterpriseExpenseHead.enterprise_id == enterprise_id,
						EnterpriseExpenseHead.ledger_id == expense_head_id).delete()
			logger.info("Enterprise Expense Heads fetched - %s" % expense_head)

			logger.debug('Save enterprise expenses %s' % self.profile_dao.db_session.dirty)
			self.profile_dao.db_session.commit()
			if expense_head:
				self.profile_dao.db_session.refresh(expense_head)
		except Exception as e:
			self.profile_dao.db_session.rollback()
			logger.exception("Update Enterprise Expense Heads Failed - %s" % e.message)
			raise

	def updateEnterprisePurchase(
			self, enterprise_id=None, purchase_notes=None, po_doc_reg_items=None, include_hsnsac=None,
			date_time_format=None, include_annexure=None, logo_size=None, include_hsnsac_column=None,
			is_price_modification_disabled=None, is_price_modification_disabled_quick_po=None):
		"""

		:param enterprise_id:
		:param purchase_notes:
		:param po_doc_reg_items:
		:param include_hsnsac:
		:param date_time_format:
		:param include_annexure:
		:param logo_size:
		:param include_hsnsac_column:
		:return:
		"""
		logger.info("Enterprise Notes Update Service Call..")
		self.profile_dao.db_session.begin(subtransactions=True)
		try:
			enterprise_notes = self.profile_dao.getEnterpriseNotes(enterprise_id=enterprise_id)
			enterprise = self.profile_dao.getProfile(enterprise_id=enterprise_id)

			if not enterprise_notes:
				enterprise_notes = EnterpriseNotes(
					enterprise_id=enterprise_id, purchase=purchase_notes, po_doc_reg_items=po_doc_reg_items,
					include_hsnsac=include_hsnsac, po_doc_datetime_format=date_time_format,
					include_annexure=include_annexure, logo_size=logo_size, include_hsnsac_column=include_hsnsac_column)
			else:
				enterprise_notes.purchase = purchase_notes
				enterprise_notes.po_doc_reg_items = po_doc_reg_items
				enterprise_notes.include_hsnsac = include_hsnsac
				enterprise_notes.po_doc_datetime_format = date_time_format
				enterprise_notes.include_annexure = include_annexure
				enterprise_notes.logo_size = logo_size
				enterprise_notes.include_hsnsac_column = include_hsnsac_column
			enterprise.is_price_modification_disabled_quick_po = is_price_modification_disabled_quick_po
			enterprise.is_price_modification_disabled = is_price_modification_disabled

			logger.info("Enterprise Notes fetched - %s" % enterprise_notes.purchase)
			self.profile_dao.db_session.add(enterprise_notes)
			logger.debug('Save enterprise notes dirty %s' % self.profile_dao.db_session.dirty)
			self.profile_dao.db_session.commit()
			self.profile_dao.db_session.refresh(enterprise_notes)
		except Exception as e:
			self.profile_dao.db_session.rollback()
			logger.exception("Update Enterprise Notes Failed - %s" % e.message)
			raise

	def updateSETemplateConfig(self, enterprise_id=None, se_doc_reg_items=None):
		"""

		:param enterprise_id:
		:param se_doc_reg_items:
		:return:
		"""
		logger.info("Sales Estimate Template Config Update Service Call..")
		self.profile_dao.db_session.begin(subtransactions=True)
		try:
			se_template_config = self.profile_dao.getSETemplateConfig(enterprise_id=enterprise_id)

			if not se_template_config:
				se_template_config = SalesEstimateTemplateConfig(enterprise_id=enterprise_id,
				                                                 se_doc_reg_items=se_doc_reg_items)
			else:
				se_template_config.se_doc_reg_items = se_doc_reg_items

			logger.info("Sales Estimate Template Config fetched - %s" % se_template_config)
			self.profile_dao.db_session.add(se_template_config)
			logger.debug('Save sales estimate template config dirty %s' % self.profile_dao.db_session.dirty)
			self.profile_dao.db_session.commit()
			self.profile_dao.db_session.refresh(se_template_config)
		except Exception as e:
			self.profile_dao.db_session.rollback()
			logger.exception("Update Sales Estimate Template Config Failed - %s" % e.message)
			raise

	def _copyProfileFormToEntity(self, form=None, entity=None):
		"""

		:param form:
		:return:
		"""
		if not entity:
			copyFormToEntity(form=form, entity=Enterprise())
		else:
			entity.code = form.cleaned_data['code']
			entity.name = form.cleaned_data['name']
			entity.address_1 = form.cleaned_data['address_1']
			entity.address_2 = form.cleaned_data['address_2']
			entity.state = form.cleaned_data['state']
			entity.city = form.cleaned_data['city']
			entity.pin_code = form.cleaned_data['pin_code']
			entity.phone = form.cleaned_data['phone']
			entity.email = form.cleaned_data['email']
			entity.fax = form.cleaned_data['fax']
			entity.fy_start_day = form.cleaned_data['fy_start_day']
			entity.home_currency_id = form.cleaned_data['home_currency_id']
			entity.setting_flags = sum(int(i) for i in form.cleaned_data['setting_flags'])
			entity.is_negative_stock_allowed = form.cleaned_data['is_negative_stock_allowed']
			entity.is_gate_inward_no_mandatory = form.cleaned_data['is_gate_inward_no_mandatory']
			entity.gate_inward_no_flags = sum(int(i) for i in form.cleaned_data['gate_inward_no_flags'])
			entity.is_purchase_order_mandatory = form.cleaned_data['is_purchase_order_mandatory']
		if len(form.cleaned_data['logo']) > 0:
			logo_as_texts = form.cleaned_data['logo'].split(",")
			logo_extension = logo_as_texts[0][logo_as_texts[0].index('/') + 1:logo_as_texts[0].index(';')]
			image_data = ''
			if len(logo_as_texts) > 1:
				logger.debug("Enterprise Image %s (%s) - decoded string - (%s) - ext - %s" % (
					logo_as_texts[1], len(logo_as_texts[1]), len(base64.decodestring(logo_as_texts[1])),
					logo_extension))
				image_data = logo_as_texts[1]
			entity.images = EnterpriseImages(logo=base64.decodestring(image_data), ext=logo_extension)
		else:
			entity.images = EnterpriseImages("", ext="")
		entity.notes = EnterpriseNotes(
			enterprise_id=entity.id, purchase=form.cleaned_data['purchase'], sales=form.cleaned_data['sales'])
		logger.info("Expense Heads - %s\nClaim Heads - %s" % (
			len(form.cleaned_data['claim_heads']), len(form.cleaned_data['expense_heads'])))
		claim_heads, expense_heads = [], []
		for claim_head_id in form.cleaned_data['claim_heads']:
			claim_head = self.profile_dao.getClaimHead(enterprise_id=entity.id, claim_head_id=claim_head_id)
			claim_heads.append(
				claim_head if claim_head else EnterpriseClaimHead(enterprise_id=entity.id, ledger_id=claim_head_id))
		for expense_head_id in form.cleaned_data['expense_heads']:
			expense_head = self.profile_dao.getExpenseHead(enterprise_id=entity.id, expense_head_id=expense_head_id)
			expense_heads.append(expense_head if expense_head else EnterpriseExpenseHead(
				enterprise_id=entity.id, ledger_id=expense_head_id))
		entity.claim_heads = claim_heads
		entity.expense_heads = expense_heads
		logger.debug("Enterprise Entities: %s\n%sn%s\n%s" % (
			entity, entity.notes, entity.claim_heads, entity.expense_heads))
		return entity

	def sendRegisteredIntimationMail(self, enterprise_email=None, first_name=None, last_name=None):
		"""

		:param enterprise_email:
		:param first_name:
		:param last_name:
		:return:
		"""
		try:
			enterprise = self.profile_dao.db_session.query(Enterprise).filter(
				Enterprise.email == enterprise_email).first()
			contact_name = first_name + " " + last_name
			message = readFile(REGISTERED_INTIMATION_MAIL).replace("{{enterprise_name}}", enterprise.name).replace(
				"{{registration_contact_name}}", contact_name).replace("{{status}}", "completed  with entire process").replace(
				"{{enterprise_code}}", enterprise.code).replace("{{registration_email}}", enterprise.email).replace(
				"{{registration_phone_no}}", enterprise.phone)
			sendMail(recipients=XSASSIST_MAIL_ID, subject="[Sign Up] %s" % enterprise.name.upper(), body=message)
		except Exception as e:
			logger.exception("Sending Sing-up intimation Mail Failed: %s" % e)

	def emailRequestFromUser(self, user_id=None, enterprise_id=None, subject='', message='', response=''):
		try:
			user = self.profile_dao.db_session.query(User).filter(User.id == user_id).first()
			enterprise = self.profile_dao.db_session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
			primary_contact_map = self.profile_dao.getEnterpriseContactDetail(
				enterprise_id=enterprise_id, sequence_id=1)
			primary_contact_person, primary_email, primary_phone_no, phone_type = ["", "", "", ""]
			if primary_contact_map:
				primary_contact_person, primary_email, primary_phone_no = [
					primary_contact_map.contact.name, primary_contact_map.contact.email,
					primary_contact_map.contact.phone_no]
				if primary_contact_map.contact.is_whatsapp is True:
					phone_type = "WhatsApp "

			mail_body = readFile(REQUEST_MAIL_TEMPLATE).format(
				name=enterprise.name,
				code=enterprise.code,
				email=user.email,
				phone_no=enterprise.phone,
				contact_person=('%s %s' % (user.first_name, user.last_name)).strip().title(),
				primary_contact_person=primary_contact_person,
				primary_email=primary_email,
				phone_type=phone_type,
				primary_phone_no=primary_phone_no,
				message=message)
			sendMail(recipients=XSASSIST_MAIL_ID, subject=subject % (enterprise.name.upper(), enterprise.code.upper()), body=mail_body)
			return response
		except Exception as e:
			logger.exception("Sending Request Mail Failed for subject %s: %s" % (subject, e))
			raise e

	def deleteExpenseParticularsFromFTP(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		self.profile_dao.db_session.begin(subtransactions=True)
		try:
			logger.info("Deleting expense particulars from FTP for the Enterprise Id %s" % enterprise_id)
			expense_particular = self.profile_dao.db_session.query(ExpenseParticular).filter(
				ExpenseParticular.enterprise_id == enterprise_id).all()
			for doc in expense_particular:
				if doc.document and doc.document != "":
					FTPUtil().delete(file_path=enterprise_id, filename=doc.document)
			self.profile_dao.db_session.commit()
			return True
		except Exception as e:
			self.profile_dao.db_session.rollback()
			logger.error("Could not delete documents from expense particular %s " % e)
		return False

	def deleteAttachmentsFromFTP(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		self.profile_dao.db_session.begin(subtransactions=True)
		try:
			logger.info("Deleting attachments from FTP for the Enterprise Id %s" % enterprise_id)
			attachment = self.profile_dao.db_session.query(Attachment).filter(
				ExpenseParticular.enterprise_id == enterprise_id).all()
			for doc in attachment:
				if doc.document and doc.document != "":
					FTPUtil().delete(file_path=enterprise_id, filename=doc.file)
			self.profile_dao.db_session.commit()
			return True
		except Exception as e:
			self.profile_dao.db_session.rollback()
			logger.error("Could not delete documents from attachment %s " % e)
		return False

	def deleteEnterpriseTransactions(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT)
		try:
			logger.info("Deleting Transactions for the Enterprise Id %s" % enterprise_id)
			cur = conn.cursor()
			for query in self.queriesToDeleteTransactions():
				cur.execute(query.format(enterprise_id=enterprise_id))
			conn.commit()
		except Exception as e:
			logger.error("Deleting Transactions for the Enterprise Id failed due to %s" % e.message)
			conn.rollback()
			raise e
		finally:
			conn.close()

	def resetEnterprise(self, enterprise_id=None):
		"""

		:param enterprise_id:
		:return:
		"""
		conn = pymysql.connect(HOST, USER, PASSWORD, DBNAME, PORT)
		try:
			logger.info("Reset Enterprise data for the Enterprise Id %s" % enterprise_id)
			cur = conn.cursor()
			for query in self.queriesToResetEnterprise():
				cur.execute(query.format(enterprise_id=enterprise_id))
			conn.commit()
		except Exception as e:
			logger.error("Reset Enterprise data for the Enterprise Id failed due to %s" % e.message)
			conn.rollback()
			raise e
		finally:
			conn.close()

	def queriesToDeleteTransactions(self):
		return [
			"DELETE FROM crdr_note_document WHERE enterprise_id={enterprise_id}",
			"DELETE FROM crdrnote_grn_map WHERE enterprise_id={enterprise_id}",
			"DELETE FROM crdr_details_nonstock_material_tax WHERE enterprise_id={enterprise_id}",
			"DELETE FROM crdr_details_nonstock_material WHERE enterprise_id={enterprise_id}",
			"DELETE FROM note_tags WHERE enterprise_id={enterprise_id}",
			"DELETE FROM crdrnote_attachment WHERE enterprise_id={enterprise_id}",
			"DELETE FROM crdr_tax WHERE enterprise_id={enterprise_id}",
			"DELETE FROM crdrnote WHERE enterprise_id={enterprise_id}",
			"DELETE FROM crdr_details_tax WHERE enterprise_id={enterprise_id}",
			"DELETE FROM crdr_details WHERE enterprise_id={enterprise_id}",
			"DELETE FROM oa_document WHERE enterprise_id={enterprise_id}",
			"DELETE FROM expense_particulars WHERE enterprise_id={enterprise_id}",
			"DELETE FROM grn_issue_map WHERE enterprise_id={enterprise_id}",
			"DELETE FROM grn WHERE enterprise_id={enterprise_id}",
			"DELETE FROM purchase_order WHERE enterprise_id={enterprise_id}",
			"DELETE FROM order_acknowledgement WHERE enterprise_id={enterprise_id}",
			"DELETE FROM indents WHERE enterprise_id={enterprise_id}",
			"DELETE FROM account_books WHERE enterprise_id={enterprise_id}",
			"DELETE FROM voucher WHERE enterprise_id={enterprise_id}",
			"DELETE FROM invoice_item_tax WHERE enterprise_id={enterprise_id}",
			"DELETE isnd FROM invoice_serial_number_details isnd INNER JOIN invoice inv ON inv.id = isnd.invoice_id WHERE inv.enterprise_id={enterprise_id}",
			"DELETE FROM invoice WHERE enterprise_id={enterprise_id}",
			"DELETE FROM se_attachment WHERE enterprise_id={enterprise_id}",
			"DELETE FROM se_document WHERE enterprise_id={enterprise_id}",
			"DELETE FROM sales_estimate WHERE enterprise_id={enterprise_id}",
			"DELETE FROM employee_attendance_log WHERE enterprise_id={enterprise_id}",
			"DELETE FROM employee_master_history WHERE enterprise_id={enterprise_id}",
			"DELETE FROM expenses WHERE enterprise_id={enterprise_id}",
			"DELETE FROM attachment WHERE enterprise_id={enterprise_id} and id not in ("
			"select attachment_id from invoice_template_banner_images bm inner join invoice_template_config tc on tc.id = bm.config_id "
			"where tc.enterprise_id={enterprise_id})",
			"DELETE FROM notification WHERE enterprise_id={enterprise_id}"
		]

	def queriesToResetEnterprise(self):
		return [
			"DELETE FROM enterprise_claim_heads WHERE enterprise_id={enterprise_id}",
			"DELETE FROM enterprise_expense_heads WHERE enterprise_id={enterprise_id}",
			"DELETE FROM material_history WHERE enterprise_id={enterprise_id}",
			"DELETE FROM specification WHERE enterprise_id={enterprise_id}",
			"DELETE FROM material_serial_number_details WHERE enterprise_id={enterprise_id}",
			"DELETE FROM material_attachment_map WHERE enterprise_id={enterprise_id}",
			"DELETE FROM cat_materials WHERE enterprise_id={enterprise_id}",
			"DELETE FROM materials WHERE enterprise_id={enterprise_id}",
			"DELETE FROM party_ledger_map WHERE enterprise_id={enterprise_id}",
			"DELETE FROM party_master WHERE enterprise_id={enterprise_id}",
			"DELETE tlm FROM tax_ledger_map tlm INNER JOIN account_ledgers al ON al.id = tlm.ledger_id WHERE al.is_default = 0 and al.enterprise_id={enterprise_id}",
			"DELETE FROM account_ledgers WHERE enterprise_id={enterprise_id} and is_default = 0",
			"DELETE FROM account_groups WHERE enterprise_id={enterprise_id} and is_default = 0",
			"DELETE FROM tax WHERE enterprise_id={enterprise_id} and is_default = 0",
			"DELETE FROM issue_master WHERE enterprise_id={enterprise_id}",
			"DELETE FROM employee_pay_structure_items WHERE enterprise_id={enterprise_id}",
			"DELETE FROM employee_salary WHERE enterprise_id={enterprise_id}",
			"DELETE FROM employee_master WHERE enterprise_id={enterprise_id}",
			"DELETE FROM department WHERE enterprise_id={enterprise_id}",
			"DELETE FROM pay_structure WHERE enterprise_id={enterprise_id}",
			"DELETE FROM pay_structure_items WHERE enterprise_id={enterprise_id}",
			"DELETE FROM enterprise_history WHERE enterprise_id={enterprise_id}",
			"DELETE FROM invoice_template_config WHERE enterprise_id={enterprise_id}",
			"SET FOREIGN_KEY_CHECKS=0",
			"DELETE FROM unit_master WHERE enterprise_id={enterprise_id} and unit_id > 6",
			"DELETE FROM auth_user WHERE enterprise_id={enterprise_id} and is_superuser=0",
			"SET FOREIGN_KEY_CHECKS=1"
		]

	def saveEraseLog(self, enterprise_id=None, erase_type=None, user_id=None, erased_on=None):
		"""

		:param enterprise_id:
		:param erase_type:
		:param user_id:
		:param erased_on:
		:return:
		"""

		logger.info("Erase Log Update Service Call..")
		db_session = self.profile_dao.db_session
		db_session.begin(subtransactions=True)
		try:
			erase_log = db_session.query(EraseLog).filter(
				EraseLog.enterprise_id == enterprise_id, EraseLog.erase_type == erase_type).first()
			if erase_log:
				erase_log.erased_by = user_id
				erase_log.erased_on = erased_on
			else:
				erase_log = EraseLog(enterprise_id=enterprise_id, erase_type=erase_type, erased_by=user_id,
				                     erased_on=erased_on)

			db_session.add(erase_log)
			logger.debug('Save Erase Log dirty %s' % db_session.dirty)
			db_session.commit()

		except Exception as e:
			db_session.rollback()
			logger.exception("Update Erase Log Failed - %s" % e.message)
			raise


class TemplateConfigService:

	def __init__(self):
		"""

		"""
		self.invoice_dao = InvoiceTemplateDAO()

	def constructTemplateConfigVo(self, itc=InvoiceTemplateConfig(), tab_to_be_retained="1"):
		"""
		Converts the DB persistent entity into a Value Object presentable to the UI layer.

		:param itc:
		:param tab_to_be_retained:
		:return:
		"""

		return InvoiceTemplateVO(
			self._generateInvoiceTemplateConfigForm(itc=itc, prefix=IT_CONFIG_FORM_PREFIX),
			self._generateInvoiceTemplateGeneralConfigForm(itc.template_general_details, tab_to_be_retained),
			self._generateInvoiceTemplateHeaderConfigForm(itc.template_header_details),
			self._generateInvoiceInvoiceTemplateItemsConfigForm(itc.template_item_details),
			self._generateInvoiceTemplateSummaryConfigForm(itc.template_summary_details),
			self._generateInvoiceTemplateMiscConfigForm(itc.template_misc_details),
			self._generateInvoiceTemplateHeaderBannerFormset(config_id=itc.id, enterprise_id=itc.enterprise_id,
			                                                 banner_details=itc.template_banner_details),
			self._generateInvoiceTemplateFooterBannerFormset(config_id=itc.id, enterprise_id=itc.enterprise_id,
			                                                 banner_details=itc.template_banner_details))

	def _generateInvoiceTemplateConfigForm(self, itc=InvoiceTemplateConfig(), prefix=IT_CONFIG_FORM_PREFIX):
		initializer = constructFormInitializer(entity=itc)

		return InvoiceTemplateConfigForm(initial=initializer, prefix=prefix)

	def _generateInvoiceTemplateGeneralConfigForm(self, itc_template_general_details=(), tab_to_be_retained="1"):
		initializer = constructFormInitializer(entity=itc_template_general_details)
		initializer['tab_retain'] = tab_to_be_retained
		return InvoiceTemplateGeneralConfigForm(initial=initializer, prefix=IT_GENERAL_FORM_PREFIX)

	def _generateInvoiceTemplateHeaderConfigForm(self, itc_template_header_details=()):
		initializer = constructFormInitializer(entity=itc_template_header_details)
		return InvoiceTemplateHeaderConfigForm(initial=initializer, prefix=IT_HEADER_FORM_PREFIX)

	def _generateInvoiceInvoiceTemplateItemsConfigForm(self, itc_template_item_details=()):
		initializer = constructFormInitializer(entity=itc_template_item_details)
		return InvoiceTemplateItemsConfigForm(initial=initializer, prefix=IT_ITEMS_FORM_PREFIX)

	def _generateInvoiceTemplateSummaryConfigForm(self, itc_template_summary_details=()):
		initializer = constructFormInitializer(entity=itc_template_summary_details)
		return InvoiceTemplateSummaryConfigForm(initial=initializer, prefix=IT_SUMMARY_FORM_PREFIX)

	def _generateInvoiceTemplateMiscConfigForm(self, itc_template_misc_details=()):
		initializer = constructFormInitializer(entity=itc_template_misc_details)
		return InvoiceTemplateMiscConfigForm(initial=initializer, prefix=IT_MISC_FORM_PREFIX)

	def _generateInvoiceTemplateHeaderBannerFormset(self, config_id=None, enterprise_id=None, banner_details=()):
		initializer = []
		for i in range(3):
			section = "Header"
			if i == 0:
				position = "Left"
			elif i == 1:
				position = "Center"
			else:
				position = "Right"
			size = 14
			banner_image = ""
			file_ext = ""
			if banner_details:
				for invoice_banner in banner_details:
					if invoice_banner.attachment:
						if invoice_banner.section == section and invoice_banner.position == position:
							size = invoice_banner.size
							file_ext = invoice_banner.attachment.file_ext
							banner_image = self.invoice_dao.getBannerAttachment(
								config_id=config_id, enterprise_id=enterprise_id, section=invoice_banner.section,
								position=invoice_banner.position)
			template_header_banner = {
				"config_id": config_id, "enterprise_id": enterprise_id, "section": section, "position": position,
				"size": size,
				"banner_image": "data:image/%s;base64,%s" % (
					file_ext, banner_image.encode('base64')) if banner_image != "" else ""}
			initializer.append(template_header_banner)

		return InvoiceTemplateHeaderBannerFormset(initial=initializer, prefix=IT_HEADER_BANNER_FORM_PREFIX)

	def _generateInvoiceTemplateFooterBannerFormset(self, config_id=None, enterprise_id=None, banner_details=()):
		initializer = []
		for i in range(3):
			section = "Footer"
			if i == 0:
				position = "Left"
			elif i == 1:
				position = "Center"
			else:
				position = "Right"
			size = 14
			banner_image = ""
			file_ext = ""
			if banner_details:
				for invoice_banner in banner_details:
					if invoice_banner.section == section and invoice_banner.position == position:
						size = invoice_banner.size
						file_ext = invoice_banner.attachment.file_ext
						banner_image = self.invoice_dao.getBannerAttachment(
							config_id=config_id, enterprise_id=enterprise_id, section=invoice_banner.section,
							position=invoice_banner.position)
			template_footer_banner = {
				"config_id": config_id, "enterprise_id": enterprise_id, "section": section, "position": position,
				"size": size,
				"banner_image": "data:image/%s;base64,%s" % (
					file_ext, banner_image.encode('base64')) if banner_image != "" else ""}

			initializer.append(template_footer_banner)

		return InvoiceTemplateFooterBannerFormset(initial=initializer, prefix=IT_FOOTER_BANNER_FORM_PREFIX)

	def saveTemplateConfig(self, enterprise_id=None, invoice_template_vo=None, user_id=None, last_modified_on=None):
		"""

		:param enterprise_id:
		:param invoice_template_vo:
		:param user_id:
		:param last_modified_on:
		:return:
		"""

		logger.info("Template config Update Service Call..")
		db_session = self.invoice_dao.db_session
		db_session.begin(subtransactions=True)
		try:
			if invoice_template_vo.is_valid():
				template_config = self.invoice_dao.getInvoiceTemplateConfig(enterprise_id=enterprise_id, module='Sales')
				template_to_be_saved = self.copyInvoiceTemplateVOToEntity(
					None, invoice_template_vo=invoice_template_vo, entity=template_config, user_id=user_id,
					last_modified_on=last_modified_on)
				db_session.add(template_to_be_saved)
				logger.info('Is DB Session disturbed? %s' % len(self.invoice_dao.db_session.dirty))
				logger.debug('Save template config dirty %s' % self.invoice_dao.db_session.dirty)
				db_session.commit()
				db_session.refresh(template_to_be_saved)

				invoice_template = db_session.query(InvoiceTemplateConfig).filter(
					InvoiceTemplateConfig.enterprise_id == enterprise_id).first()

				InvoiceTemplateChangelog().queryInsert(
					user_id=user_id, enterprise_id=enterprise_id, data=invoice_template, is_db_data=True)
			else:
				db_session.rollback()

		except Exception as e:
			self.invoice_dao.db_session.rollback()
			logger.exception("Update Template config Failed - %s" % e.message)
			raise
		return invoice_template_vo

	def __copyInvoiceTemplateConfigFormToEntity(self, form=None, entity=None, user_id=None, last_modified_on=None):
		"""

		:param form:
		:param entity:
		:return:
		"""
		try:
			if not entity:
				copyFormToEntity(form=form, entity=entity)
			else:
				entity.config_id = form.cleaned_data['id']
				entity.enterprise_id = form.cleaned_data['enterprise_id']
				entity.module = form.cleaned_data['module']
				entity.template_id = form.cleaned_data['template_id']
				entity.last_modified_by = user_id
				entity.last_modified_on = last_modified_on

			return entity
		except Exception as e:
			raise e

	def copyInvoiceTemplateVOToEntity(
			self, updated_banner_image, invoice_template_vo=None, entity=None, user_id=None,
			last_modified_on=None, is_pdf_preview=False):
		"""
		Method converts VO into persist-able data entity.
		:param updated_banner_image:
		:param invoice_template_vo:
		:param entity:
		:param user_id:
		:param last_modified_on:
		:param is_pdf_preview:
		:return:
		"""
		try:
			entity = self.__copyInvoiceTemplateConfigFormToEntity(
				invoice_template_vo.invoice_template_config_form, entity, user_id, last_modified_on)
			entity.template_general_details = self._copyTemplateGeneralFormToEntity(
				invoice_template_vo.invoice_template_generalconfig_form, entity.template_general_details)
			entity.template_header_details = self._copyTemplateHeaderFormToEntity(
				invoice_template_vo.invoice_template_headerconfig_form, entity.template_header_details)
			entity.template_item_details = self._copyTemplateItemFormToEntity(
				invoice_template_vo.invoice_template_itemsconfig_form, entity.template_item_details)
			entity.template_summary_details = self._copyTemplateSummaryFormToEntity(
				invoice_template_vo.invoice_template_summaryconfig_form, entity.template_summary_details)
			entity.template_misc_details = self._copyTemplateMiscFormToEntity(
				invoice_template_vo.invoice_template_miscconfig_form, entity.template_misc_details)
			entity.template_banner_details = self._copyTemplateBannerFormsetToEntity(
				updated_banner_image, invoice_template_vo.invoice_template_header_banner_formset,
				invoice_template_vo.invoice_template_footer_banner_formset, user_id=user_id,
				uploaded_on=last_modified_on, entity=entity, is_pdf_preview=is_pdf_preview)

			return entity
		except Exception as e:
			logger.exception("Error in Copy Invoice Template Vo to Entity: %s" % e)
		return entity

	def _copyTemplateGeneralFormToEntity(self, form=None, entity=None):
		"""

		:param form:
		:return:
		"""
		try:
			if not entity:
				copyFormToEntity(form=form, entity=InvoiceTemplateGeneralConfig())
			else:
				entity.config_id = form.cleaned_data['config_id']
				entity.page_size = form.cleaned_data['page_size']
				entity.alignment = form.cleaned_data['alignment']
				entity.base_font = form.cleaned_data['base_font']
				entity.margin_top = form.cleaned_data['margin_top']
				entity.margin_bottom = form.cleaned_data['margin_bottom']
				entity.margin_right = form.cleaned_data['margin_right']
				entity.margin_left = form.cleaned_data['margin_left']
				entity.inv_doc_datetime_format = form.cleaned_data['inv_doc_datetime_format']

			return entity
		except Exception as e:
			raise e

	def _copyTemplateHeaderFormToEntity(self, form=None, entity=None):
		"""

		:param form:
		:return:
		"""
		try:
			if not entity:
				copyFormToEntity(form=form, entity=InvoiceTemplateHeaderConfig())
			else:
				entity.config_id = form.cleaned_data['config_id']
				entity.include_logo = form.cleaned_data['include_logo']
				entity.logo_size = form.cleaned_data['logo_size']
				entity.include_name = form.cleaned_data['include_name']
				entity.name_font = form.cleaned_data['name_font']
				entity.name_font_size = form.cleaned_data['name_font_size']
				entity.font_size = form.cleaned_data['font_size']
				entity.form_name_font_size = form.cleaned_data['form_name_font_size']
				entity.include_address = form.cleaned_data['include_address']
				entity.include_email = form.cleaned_data['include_email']
				entity.include_phone_no = form.cleaned_data['include_phone_no']
				entity.include_fax = form.cleaned_data['include_fax']
				entity.include_cin = form.cleaned_data['include_cin']
				entity.include_gstin = form.cleaned_data['include_gstin']
				entity.include_pan = form.cleaned_data['include_pan']
				entity.include_tin = form.cleaned_data['include_tin']
				entity.include_ecc_no = form.cleaned_data['include_ecc_no']
				entity.include_cst_no = form.cleaned_data['include_cst_no']
				entity.include_tan = form.cleaned_data['include_tan']
				entity.include_partycode = form.cleaned_data['include_partycode']
				entity.include_partyname = form.cleaned_data['include_partyname']
				entity.include_billingaddress = form.cleaned_data['include_billingaddress']
				entity.billingaddress_label = form.cleaned_data['billingaddress_label']
				entity.include_shippingaddress = form.cleaned_data['include_shippingaddress']
				entity.shippingaddress_label = form.cleaned_data['shippingaddress_label']
				entity.include_party_email = form.cleaned_data['include_party_email']
				entity.include_party_phoneno = form.cleaned_data['include_party_phoneno']
				entity.gst_label = form.cleaned_data['gst_label']
				entity.service_label = form.cleaned_data['service_label']
				entity.trading_label = form.cleaned_data['trading_label']
				entity.billofsupply_label = form.cleaned_data['billofsupply_label']
				entity.others_label = form.cleaned_data['others_label']
				entity.invoiceno_label = form.cleaned_data['invoiceno_label']
				entity.invoicedate_label = form.cleaned_data['invoicedate_label']
				entity.include_paymentterms = form.cleaned_data['include_paymentterms']
				entity.paymentterms_label = form.cleaned_data['paymentterms_label']
				entity.include_pono = form.cleaned_data['include_pono']
				entity.pono_label = form.cleaned_data['pono_label']
				entity.include_podate = form.cleaned_data['include_podate']
				entity.podate_label = form.cleaned_data['podate_label']
				entity.include_estimate_nodate = form.cleaned_data['include_estimate_nodate']
				entity.estimate_nodate_label = form.cleaned_data['estimate_nodate_label']
				entity.include_transportmode = form.cleaned_data['include_transportmode']
				entity.transportmode_label = form.cleaned_data['transportmode_label']
				entity.include_splinstructions = form.cleaned_data['include_splinstructions']
				entity.splinstructions_label = form.cleaned_data['splinstructions_label']
				entity.include_roadpermitno = form.cleaned_data['include_roadpermitno']
				entity.roadpermitno_label = form.cleaned_data['roadpermitno_label']
				entity.include_lrnodate = form.cleaned_data['include_lrnodate']
				entity.lrnodate_label = form.cleaned_data['lrnodate_label']
				entity.include_packingslipno = form.cleaned_data['include_packingslipno']
				entity.packingslipno_label = form.cleaned_data['packingslipno_label']
				entity.include_packingdescription = form.cleaned_data['include_packingdescription']
				entity.packingdescription_label = form.cleaned_data['packingdescription_label']
				entity.include_authorizedsign = form.cleaned_data['include_authorizedsign']
				entity.authorizedsign_label = form.cleaned_data['authorizedsign_label']
				entity.include_approver_signature = form.cleaned_data['include_approver_signature']
				entity.dc_label = form.cleaned_data['dc_label']
				entity.dcno_label = form.cleaned_data['dcno_label']
				entity.included_reg_items = form.cleaned_data['included_reg_items']
				entity.inv_number_format = form.cleaned_data['inv_number_format']
				entity.invoice_number_font_size = form.cleaned_data['invoice_number_font_size']
				entity.scan_code_option = form.cleaned_data['scan_code_option']
				entity.include_preparedsign = form.cleaned_data['include_preparedsign']
				entity.preparedsign_label = form.cleaned_data['preparedsign_label']

			return entity
		except Exception as e:
			raise e

	def _copyTemplateItemFormToEntity(self, form=None, entity=None):
		"""

		:param form:
		:return:
		"""
		try:
			if not entity:
				copyFormToEntity(form=form, entity=InvoiceTemplateItemsConfig())
			else:
				entity.config_id = form.cleaned_data['config_id']
				entity.font_size = form.cleaned_data['font_size']
				entity.include_sno = form.cleaned_data['include_sno']
				entity.sno_label = form.cleaned_data['sno_label']
				if form.cleaned_data['sno_width'] is not None and form.cleaned_data['sno_width'] != "":
					entity.sno_width = form.cleaned_data['sno_width']
				entity.itemdetails_label = form.cleaned_data['itemdetails_label']
				if form.cleaned_data['itemdetails_width_hidden_field'] is not None and form.cleaned_data[
					'itemdetails_width_hidden_field'] != "":
					entity.itemdetails_width = form.cleaned_data['itemdetails_width_hidden_field']
				entity.include_itemcode = form.cleaned_data['include_itemcode']
				entity.itemcode_label = form.cleaned_data['itemcode_label']
				entity.name_label = form.cleaned_data['name_label']
				entity.include_make = form.cleaned_data['include_make']
				entity.make_label = form.cleaned_data['make_label']
				entity.include_partno = form.cleaned_data['include_partno']
				entity.partno_label = form.cleaned_data['partno_label']
				entity.include_description = form.cleaned_data['include_description']
				entity.description_label = form.cleaned_data['description_label']
				entity.include_remarks = form.cleaned_data['include_remarks']
				entity.remarks_label = form.cleaned_data['remarks_label']
				entity.include_hsnsac = form.cleaned_data['include_hsnsac']
				entity.hsnsac_label = form.cleaned_data['hsnsac_label']
				if form.cleaned_data['hsnsac_width'] is not None and form.cleaned_data['hsnsac_width'] != "":
					entity.hsnsac_width = form.cleaned_data['hsnsac_width']
				entity.hsnsac_part_of_itemdetails = form.cleaned_data['hsnsac_part_of_itemdetails']
				entity.include_oano = form.cleaned_data['include_oano']
				entity.oano_label = form.cleaned_data['oano_label']
				entity.include_dc_no = form.cleaned_data['include_dc_no']
				entity.dc_no_label = form.cleaned_data['dc_no_label']
				entity.include_dc_qty = form.cleaned_data['include_dc_qty']
				entity.include_dc_date = form.cleaned_data['include_dc_date']
				entity.include_quantity = form.cleaned_data['include_quantity']
				entity.quantity_label = form.cleaned_data['quantity_label']
				if form.cleaned_data['quantity_width'] is not None and form.cleaned_data['quantity_width'] != "":
					entity.quantity_width = form.cleaned_data['quantity_width']
				entity.include_units = form.cleaned_data['include_units']
				entity.units_label = form.cleaned_data['units_label']
				if form.cleaned_data['units_width'] is not None and form.cleaned_data['units_width'] != "":
					entity.units_width = form.cleaned_data['units_width']
				entity.units_in_quantity_column = form.cleaned_data['units_in_quantity_column']
				entity.include_primary_qty = form.cleaned_data['include_primary_qty']
				entity.include_unit_price = form.cleaned_data['include_unit_price']
				entity.unit_price_label = form.cleaned_data['unit_price_label']
				if form.cleaned_data['unit_price_width'] is not None and form.cleaned_data['unit_price_width'] != "":
					entity.unit_price_width = form.cleaned_data['unit_price_width']
				entity.include_discount = form.cleaned_data['include_discount']
				entity.discount_label = form.cleaned_data['discount_label']
				if form.cleaned_data['discount_width'] is not None and form.cleaned_data['discount_width'] != "":
					entity.discount_width = form.cleaned_data['discount_width']
				entity.include_taxable_amount = form.cleaned_data['include_taxable_amount']
				entity.taxable_amount_label = form.cleaned_data['taxable_amount_label']
				if form.cleaned_data['taxable_amount_width'] is not None and form.cleaned_data[
					'taxable_amount_width'] != "":
					entity.taxable_amount_width = form.cleaned_data['taxable_amount_width']
				entity.include_tax = form.cleaned_data['include_tax']
				if form.cleaned_data['tax_type'] is None or form.cleaned_data['tax_type'] == "":
					entity.tax_type = form.cleaned_data['tax_type_hidden_field']
				else:
					entity.tax_type = int(form.cleaned_data['tax_type'])
				if form.cleaned_data['include_taxrate'] is None and form.cleaned_data['include_taxrate'] != "":
					entity.include_taxrate = form.cleaned_data['include_taxrate_hidden_field']
				else:
					entity.include_taxrate = form.cleaned_data['include_taxrate']
				if form.cleaned_data['taxrate_width'] is not None and form.cleaned_data['taxrate_width'] != "":
					entity.taxrate_width = form.cleaned_data['taxrate_width']
				if form.cleaned_data['include_taxamount'] is None and form.cleaned_data['include_taxamount'] != "":
					entity.include_taxamount = form.cleaned_data['include_taxamount_hidden_field']
				else:
					entity.include_taxamount = form.cleaned_data['include_taxamount']
				if form.cleaned_data['taxamount_width'] is not None and form.cleaned_data['taxamount_width'] != "":
					entity.taxamount_width = form.cleaned_data['taxamount_width']
				if form.cleaned_data['show_tax_for_dc'] is not None and form.cleaned_data['show_tax_for_dc'] != "":
					entity.show_tax_for_dc = form.cleaned_data['show_tax_for_dc']
				entity.item_sort_order = form.cleaned_data['item_sort_order']
				entity.include_row_separator = form.cleaned_data['include_row_separator']
				entity.include_column_separator = form.cleaned_data['include_column_separator']
				entity.include_alternate_row_shading = form.cleaned_data['include_alternate_row_shading']
				entity.tax_payable_on_reverse_charge = form.cleaned_data['tax_payable_on_reverse_charge']
				entity.item_table_space = form.cleaned_data['item_table_space']
				entity.data_separator = form.cleaned_data['data_separator']

			return entity
		except Exception as e:
			raise e

	def _copyTemplateMiscFormToEntity(self, form=None, entity=None):
		"""

		:param form:
		:return:
		"""
		try:
			if not entity:
				copyFormToEntity(form=form, entity=InvoiceTemplateMiscConfig())
			else:
				entity.config_id = form.cleaned_data['config_id']
				entity.font_size = form.cleaned_data['font_size']
				entity.include_page_no_in_footer = form.cleaned_data['include_page_no_in_footer']
				entity.footer_page_no_alignment = form.cleaned_data['footer_page_no_alignment']
				entity.notes = form.cleaned_data['notes']
				entity.include_first_page_summary = form.cleaned_data['include_first_page_summary']
				entity.foot_note = form.cleaned_data['foot_note']

			return entity
		except Exception as e:
			raise e

	def _copyTemplateSummaryFormToEntity(self, form=None, entity=None):
		"""

		:param form:
		:return:
		"""
		try:
			if not entity:
				copyFormToEntity(form=form, entity=InvoiceTemplateSummaryConfig())
			else:
				entity.config_id = form.cleaned_data['config_id']
				entity.font_size = form.cleaned_data['font_size']
				entity.include_total = form.cleaned_data['include_total']
				entity.include_subtotal = form.cleaned_data['include_subtotal']
				entity.include_qty_total = form.cleaned_data['include_qty_total']
				entity.include_total_in_words = form.cleaned_data['include_total_in_words']
				entity.hsn_tax_font_size = form.cleaned_data['hsn_tax_font_size']
				entity.include_hsn_summary = form.cleaned_data['include_hsn_summary']

			return entity
		except Exception as e:
			raise e

	def _copyTemplateBannerFormsetToEntity(self, updated_banner_image, header_formset=None, footer_formset=None,
	                                       user_id=None, uploaded_on=None, entity=None, is_pdf_preview=False):
		"""

		:param header_formset:
		:param footer_formset:
		:param banner_details:
		:param user_id:
		:param uploaded_on:
		:param entity:
		:return:
		"""
		try:
			banner_details = []
			if header_formset.is_valid():
				for form in header_formset:
					if is_pdf_preview:
						banner_image = form.cleaned_data['uploaded_banner_image'] if form.cleaned_data[
							'uploaded_banner_image'] else form.cleaned_data['banner_image']
						updated_banner_image.append({
							'section': form.cleaned_data['section'], 'position': form.cleaned_data['position'],
							'size': form.cleaned_data['size'],
							'banner_image': banner_image if not form.cleaned_data['is_removed'] else ""})
					else:
						if form.cleaned_data['is_removed']:
							continue
						uploaded_banner_image = form.cleaned_data['uploaded_banner_image']

						header_banner_detail = self.invoice_dao.getInvoiceTemplateBannerDetails(
							config_id=entity.config_id, section=form.cleaned_data['section'],
							position=form.cleaned_data['position'])

						if uploaded_banner_image:
							if not header_banner_detail:
								file_to_be_saved = uploaded_banner_image.split(",")
								fil_ext = file_to_be_saved[0][
								          file_to_be_saved[0].index('/') + 1:file_to_be_saved[0].index(';')]
								doc_data = file_to_be_saved[1].decode('base64')
								logger.debug("File to be Saved:%s" % doc_data)
								filename = "%s.%s" % (uuid.uuid4(), fil_ext)
								temp_file = "/site_media/tmp/%s" % filename
								writeFile(doc_data, temp_file)
								FTPUtil().upload(
									file_path=entity.enterprise_id, filename=filename,
									fp=open("%s/%s" % (TEMP_DIR, filename), 'rb'))
								attachment_id = saveAttachment(
									enterprise_id=entity.enterprise_id,
									label="Invoice_Template_%s_Banner" % form.cleaned_data['section'],
									file_to_be_saved=filename, ext=fil_ext, uploaded_by=user_id,
									uploaded_on=uploaded_on,
									db_session=self.invoice_dao.db_session)

								header_banner_detail = InvoiceTemplateBannerImage(
									config_id=entity.id, section=form.cleaned_data['section'],
									position=form.cleaned_data['position'],
									attachment_id=attachment_id, uploaded_by=user_id, last_modified_by=user_id)
							else:
								file_to_be_saved = uploaded_banner_image.split(",")
								fil_ext = file_to_be_saved[0][
								          file_to_be_saved[0].index('/') + 1:file_to_be_saved[0].index(';')]
								doc_data = file_to_be_saved[1].decode('base64')
								logger.debug("File to be Saved:%s" % doc_data)
								filename = "%s.%s" % (header_banner_detail.attachment.file.split(".")[0], fil_ext)
								temp_file = "/site_media/tmp/%s" % filename
								writeFile(doc_data, temp_file)
								FTPUtil().upload(
									file_path=entity.enterprise_id, filename=filename,
									fp=open("%s/%s" % (TEMP_DIR, filename), 'rb'))
								header_banner_detail.attachment.file = filename
								header_banner_detail.attachment.file_ext = fil_ext
							logger.info("Invoice Template Header Banner Attachment imported successfully")
							self.invoice_dao.db_session.add(header_banner_detail)
						if header_banner_detail:
							header_banner_detail.size = form.cleaned_data['size']
							banner_details.append(header_banner_detail)

			if footer_formset.is_valid():
				for form in footer_formset:
					if is_pdf_preview:
						banner_image = form.cleaned_data['uploaded_banner_image'] if form.cleaned_data[
							'uploaded_banner_image'] else form.cleaned_data['banner_image']
						updated_banner_image.append({
							'section': form.cleaned_data['section'], 'position': form.cleaned_data['position'],
							'size': form.cleaned_data['size'],
							'banner_image': banner_image if not form.cleaned_data['is_removed'] else ""})
					else:
						if form.cleaned_data['is_removed']:
							continue
						uploaded_banner_image = form.cleaned_data['uploaded_banner_image']

						footer_banner_detail = self.invoice_dao.getInvoiceTemplateBannerDetails(
							config_id=entity.config_id, section=form.cleaned_data['section'],
							position=form.cleaned_data['position'])
						if uploaded_banner_image:
							if not footer_banner_detail:
								file_to_be_saved = uploaded_banner_image.split(",")
								fil_ext = file_to_be_saved[0][
								          file_to_be_saved[0].index('/') + 1:file_to_be_saved[0].index(';')]
								doc_data = file_to_be_saved[1].decode('base64')
								logger.debug("File to be Saved:%s" % doc_data)
								filename = "%s.%s" % (uuid.uuid4(), fil_ext)
								temp_file = "/site_media/tmp/%s" % filename
								writeFile(doc_data, temp_file)
								FTPUtil().upload(
									file_path=entity.enterprise_id, filename=filename,
									fp=open("%s/%s" % (TEMP_DIR, filename), 'rb'))
								attachment_id = saveAttachment(
									enterprise_id=entity.enterprise_id,
									label="Invoice_Template_%s_Banner" % form.cleaned_data['section'],
									file_to_be_saved=filename, ext=fil_ext, uploaded_by=user_id,
									uploaded_on=uploaded_on,
									db_session=self.invoice_dao.db_session)

								footer_banner_detail = InvoiceTemplateBannerImage(
									config_id=entity.id, section=form.cleaned_data['section'],
									position=form.cleaned_data['position'],
									attachment_id=attachment_id, uploaded_by=user_id, last_modified_by=user_id)
							else:
								file_to_be_saved = uploaded_banner_image.split(",")
								fil_ext = file_to_be_saved[0][
								          file_to_be_saved[0].index('/') + 1:file_to_be_saved[0].index(';')]
								doc_data = file_to_be_saved[1].decode('base64')
								logger.debug("File to be Saved:%s" % doc_data)
								filename = "%s.%s" % (footer_banner_detail.attachment.file.split(".")[0], fil_ext)
								temp_file = "/site_media/tmp/%s" % filename
								writeFile(doc_data, temp_file)
								FTPUtil().upload(
									file_path=entity.enterprise_id, filename=filename,
									fp=open("%s/%s" % (TEMP_DIR, filename), 'rb'))
								footer_banner_detail.attachment.file = filename
								footer_banner_detail.attachment.file_ext = fil_ext
							logger.info("Invoice Template Footer Banner Attachment imported successfully")
							self.invoice_dao.db_session.add(footer_banner_detail)
						if footer_banner_detail:
							footer_banner_detail.size = form.cleaned_data['size']
							banner_details.append(footer_banner_detail)
			return banner_details
		except Exception as e:
			raise e


class InvoiceTemplateVO(object):
	"""
	This Class holds both Invoice Template Form and Invoice Template Form Set
	"""

	def __init__(
			self, invoice_template_config_form=InvoiceTemplateConfigForm(prefix=IT_CONFIG_FORM_PREFIX),
			invoice_template_generalconfig_form=InvoiceTemplateGeneralConfigForm(prefix=IT_GENERAL_FORM_PREFIX),
			invoice_template_headerconfig_form=InvoiceTemplateHeaderConfigForm(prefix=IT_HEADER_FORM_PREFIX),
			invoice_template_itemsconfig_form=InvoiceTemplateItemsConfigForm(prefix=IT_ITEMS_FORM_PREFIX),
			invoice_template_summaryconfig_form=InvoiceTemplateSummaryConfigForm(prefix=IT_SUMMARY_FORM_PREFIX),
			invoice_template_miscconfig_form=InvoiceTemplateMiscConfigForm(prefix=IT_MISC_FORM_PREFIX),
			invoice_template_header_banner_formset=InvoiceTemplateHeaderBannerFormset(
				prefix=IT_HEADER_BANNER_FORM_PREFIX),
			invoice_template_footer_banner_formset=InvoiceTemplateFooterBannerFormset(
				prefix=IT_FOOTER_BANNER_FORM_PREFIX)):
		self.invoice_template_config_form = invoice_template_config_form
		self.invoice_template_generalconfig_form = invoice_template_generalconfig_form
		self.invoice_template_headerconfig_form = invoice_template_headerconfig_form
		self.invoice_template_itemsconfig_form = invoice_template_itemsconfig_form
		self.invoice_template_summaryconfig_form = invoice_template_summaryconfig_form
		self.invoice_template_miscconfig_form = invoice_template_miscconfig_form
		self.invoice_template_header_banner_formset = invoice_template_header_banner_formset
		self.invoice_template_footer_banner_formset = invoice_template_footer_banner_formset

	def is_valid(self):
		try:
			is_valid = self.invoice_template_config_form.is_valid() and \
			           self.invoice_template_generalconfig_form.is_valid() and self.invoice_template_headerconfig_form.is_valid() \
			           and self.invoice_template_itemsconfig_form.is_valid() and self.invoice_template_summaryconfig_form.is_valid() \
			           and self.invoice_template_miscconfig_form.is_valid() and self.invoice_template_header_banner_formset.is_valid() \
			           and self.invoice_template_footer_banner_formset.is_valid()
			logger.info(
				"Invoice Template - Valid Config: %s,Valid General_Config: %s, Valid Header_Config: %s, Valid Item_Config: %s, "
				"Valid_Summary_Config: %s, Valid_Misc_Config: %s, Valid_Header_Banner_Details: %s, Valid_Footer_Banner_Details: %s" % (
					self.invoice_template_config_form.is_valid(),
					self.invoice_template_generalconfig_form.is_valid(),
					self.invoice_template_headerconfig_form.is_valid(),
					self.invoice_template_itemsconfig_form.is_valid(),
					self.invoice_template_summaryconfig_form.is_valid(),
					self.invoice_template_miscconfig_form.is_valid(),
					self.invoice_template_header_banner_formset.is_valid(),
					self.invoice_template_footer_banner_formset.is_valid()))
			if not is_valid:
				logger.info("Invoice Template Form Errors: \n%s\n%s\n%s\n%s\n%s\n%s\n%s\n%s" % (
					self.invoice_template_config_form.errors,
					self.invoice_template_generalconfig_form.errors, self.invoice_template_headerconfig_form.errors,
					self.invoice_template_itemsconfig_form.errors, self.invoice_template_summaryconfig_form.errors,
					self.invoice_template_miscconfig_form.errors, self.invoice_template_header_banner_formset.errors,
					self.invoice_template_footer_banner_formset.errors))
		except Exception as e:
			is_valid = False
			logger.exception("The Invoice Template Form Set Validation Error... %s" % e.message)
		return is_valid

	def __repr__(self):
		return "Invoice Template Config Form : %s\nInvoice Template GeneralConfig Form : %s" \
		       "\nInvoice Template HeaderConfig Form: %s\nInvoice Template ItemsConfig Form : %s" \
		       "\nInvoice Template SummaryConfig Form :%s\nInvoice Template MiscConfig Form :%s" \
		       "\nInvoice Template_Header Banner Details Form :%s\nInvoice Template_Footer Banner Details Form :%s" % (
			       self.invoice_template_config_form, self.invoice_template_generalconfig_form,
			       self.invoice_template_headerconfig_form, self.invoice_template_itemsconfig_form,
			       self.invoice_template_summaryconfig_form, self.invoice_template_miscconfig_form,
			       self.invoice_template_header_banner_formset, self.invoice_template_footer_banner_formset)


class InvoiceTemplateDAO(DataAccessObject):
	"""
	Class that handles all the Purchase module related DB activities - like fetching, save and deleting persistent objects
	"""

	def getInvoiceTemplateConfig(self, enterprise_id=None, module=None):
		"""
		enterprise_id and module are mandatory
		:param enterprise_id:
		:param module:
		:return:
		"""
		if enterprise_id and module:
			# Have removed module field in filter, since we don't have separate configuration page for dc and issues.
			return self.db_session.query(InvoiceTemplateConfig).filter(
				InvoiceTemplateConfig.enterprise_id == enterprise_id).first()

	def getInvoiceTemplateBannerDetails(self, config_id=None, section=None, position=None):
		if section:
			return self.db_session.query(InvoiceTemplateBannerImage).filter(
				InvoiceTemplateBannerImage.config_id == config_id, InvoiceTemplateBannerImage.section == section,
				InvoiceTemplateBannerImage.position == position if position else InvoiceTemplateBannerImage.position).first()
		else:
			return self.db_session.query(InvoiceTemplateBannerImage).filter(
				InvoiceTemplateBannerImage.config_id == config_id).all()

	def getBannerAttachment(self, config_id=None, enterprise_id=None, section=None, position=None):
		"""
		
		:param config_id: 
		:param enterprise_id: 
		:param section: 
		:param position: 
		:return: 
		"""
		try:
			template_banner = self.db_session.query(InvoiceTemplateBannerImage).filter(
				InvoiceTemplateBannerImage.config_id == config_id, InvoiceTemplateBannerImage.section == section,
				InvoiceTemplateBannerImage.position == position if position else InvoiceTemplateBannerImage.position).first()

			return FTPUtil().download(file_path=enterprise_id, filename=template_banner.attachment.file)
		except Exception as e:
			logger.exception("The Invoice Template Banner Attachment Fetch Error... %s" % e.message)
			raise


class PurchaseTemplateConfigService:

	def __init__(self):
		"""

		"""
		self.purchase_template_dao = PurchaseTemplateDAO()

	def savePurchaseTemplateConfig(
			self, enterprise_id=None, po_template_config=None, user_id=None, last_modified_on=None, header_banner=None,
			footer_banner=None):
		"""

		:param enterprise_id:
		:param po_template_config:
		:param user_id:
		:param last_modified_on:
		:param header_banner:
		:param footer_banner:
		:return:
		"""

		logger.info("Template config Update Service Call..")
		db_session = self.purchase_template_dao.db_session
		db_session.begin(subtransactions=True)
		try:
			updated_banner_image = []
			updated_banner_details = self.purchase_template_dao.updateTemplateBannerImages(
				updated_banner_image, enterprise_id=enterprise_id, template_header_banner=header_banner,
				template_footer_banner=footer_banner, user_id=user_id, uploaded_on=last_modified_on,
				db_session=db_session, collection='purchase_template_config')

			po_template_config["misc_config"]["banner_header"]["attachment"] = updated_banner_details[0]
			po_template_config["misc_config"]["banner_footer"]["attachment"] = updated_banner_details[1]

			collection = 'purchase_template_config'
			db = MongoDbConnect[collection]
			db.find_one_and_update({'enterprise_id': enterprise_id}, {'$set': po_template_config})
			db.find_one_and_update(
				{'enterprise_id': enterprise_id}, {'$set': {'last_modified_by': user_id, 'last_modified_on': last_modified_on}})

			db_session.commit()

			PurchaseTemplateChangelog().queryInsert(
				user_id=user_id, enterprise_id=enterprise_id, data=po_template_config)
		except Exception as e:
			db_session.rollback()
			logger.exception("Update Template config Failed - %s" % e.message)
			raise

	def saveSalesEstimateTemplateConfig(
			self, enterprise_id=None, template_config=None, user_id=None, last_modified_on=None, header_banner=None,
			footer_banner=None):
		"""

		:param enterprise_id:
		:param template_config:
		:param user_id:
		:param last_modified_on:
		:param header_banner:
		:param footer_banner:
		:return:
		"""

		logger.info("Sales Estimate Template config Update Service Call..")
		db_session = self.purchase_template_dao.db_session
		db_session.begin(subtransactions=True)
		try:
			updated_banner_image = []
			collection = 'sales_estimate_template_config'
			updated_banner_details = self.purchase_template_dao.updateTemplateBannerImages(
				updated_banner_image, enterprise_id=enterprise_id, template_header_banner=header_banner,
				template_footer_banner=footer_banner, user_id=user_id, uploaded_on=last_modified_on,
				db_session=db_session, collection=collection)

			template_config["misc_config"]["banner_header"]["attachment"] = updated_banner_details[0]
			template_config["misc_config"]["banner_footer"]["attachment"] = updated_banner_details[1]

			db = MongoDbConnect[collection]
			db.find_one_and_update({'enterprise_id': enterprise_id}, {'$set': template_config})
			db.find_one_and_update(
				{'enterprise_id': enterprise_id}, {'$set': {'last_modified_by': user_id, 'last_modified_on': last_modified_on}})

			db_session.commit()

		except Exception as e:
			db_session.rollback()
			logger.exception("Update Template config Failed - %s" % e.message)
			raise

	def saveOrderAcknowledgementTemplateConfig(
			self, enterprise_id=None, template_config=None, user_id=None, last_modified_on=None, header_banner=None,
			footer_banner=None):
		"""

		:param enterprise_id:
		:param template_config:
		:param user_id:
		:param last_modified_on:
		:param header_banner:
		:param footer_banner:
		:return:
		"""

		logger.info("Order Acknowledgement Template config Update Service Call..")
		db_session = self.purchase_template_dao.db_session
		db_session.begin(subtransactions=True)
		try:
			updated_banner_image = []
			collection = 'order_acknowledgement_template_config'
			updated_banner_details = self.purchase_template_dao.updateTemplateBannerImages(
				updated_banner_image, enterprise_id=enterprise_id, template_header_banner=header_banner,
				template_footer_banner=footer_banner, user_id=user_id, uploaded_on=last_modified_on,
				db_session=db_session, collection=collection)

			template_config["misc_config"]["banner_header"]["attachment"] = updated_banner_details[0]
			template_config["misc_config"]["banner_footer"]["attachment"] = updated_banner_details[1]

			db = MongoDbConnect[collection]
			db.find_one_and_update({'enterprise_id': enterprise_id}, {'$set': template_config})
			db.find_one_and_update(
				{'enterprise_id': enterprise_id}, {'$set': {'last_modified_by': user_id, 'last_modified_on': last_modified_on}})

			db_session.commit()

		except Exception as e:
			db_session.rollback()
			logger.exception("Update Template config Failed - %s" % e.message)
			raise


class PurchaseTemplateDAO(DataAccessObject):
	"""
	Class that handles all the Purchase Template related DB activities - like fetching, save and deleting persistent objects
	"""
	def getPurchaseTemplateConfig(self, enterprise_id=None):
		"""
		enterprise_id and module are mandatory
		:param enterprise_id:
		:return:
		"""
		purchase_template_config = None
		try:
			if enterprise_id:
				collection = 'purchase_template_config'
				db = MongoDbConnect[collection]
				enterprise_template_config = db.find({'enterprise_id': enterprise_id})
				for data in enterprise_template_config:
					purchase_template_config = data
			return purchase_template_config
		except Exception as e:
			logger.exception("Failed to fetch purchase template config... %s" % e.message)

	def getTemplateConfig(self, enterprise_id=None, collection=None):
		"""
		enterprise_id and module are mandatory
		:param enterprise_id:
		:param collection:
		:return:
		"""
		_template_config = None
		try:
			if enterprise_id:
				db = MongoDbConnect[collection]
				enterprise_template_config = db.find({'enterprise_id': int(enterprise_id)})
				for data in enterprise_template_config:
					_template_config = data
			return _template_config
		except Exception as e:
			logger.exception("Failed to fetch template config... %s" % e.message)

	def getBannerAttachment(self, attachment_id=None, enterprise_id=None):
		"""

		:param attachment_id:
		:param enterprise_id:
		:return:
		"""
		data = ""
		try:
			attachment = self.db_session.query(Attachment).filter(Attachment.attachment_id == attachment_id).first()

			if attachment:
				data = FTPUtil().download(file_path=enterprise_id, filename=attachment.file)
			return data, attachment
		except Exception as e:
			logger.exception("The Purchase Template Banner Attachment Fetch Error... %s" % e.message)
			raise

	def updateTemplateBannerImages(
			self, updated_banner_image, enterprise_id=None, template_header_banner=None, template_footer_banner=None,
			user_id=None, uploaded_on=None, is_pdf_preview=False, db_session=None, collection=None):
		"""

		:param updated_banner_image:
		:param enterprise_id:
		:param template_header_banner:
		:param template_footer_banner:
		:param user_id:
		:param uploaded_on:
		:param is_pdf_preview:
		:param db_session:
		:param collection:
		:return:
		"""
		try:
			banner_details = []
			header_banner_detail = []
			footer_banner_detail = []

			logger.info("collection:%s" % collection)
			po_template_config = PurchaseTemplateConfigService().purchase_template_dao.getTemplateConfig(
				enterprise_id=enterprise_id, collection=collection)
			template_misc_details = po_template_config["misc_config"]
			for banner in template_header_banner["header_banner"]:
				if is_pdf_preview:
					updated_banner_image.append({
						'section': 'Header', 'position': banner['position'], 'width': banner['width'],
						'banner_image': banner['banner_image']})
				else:
					uploaded_banner_image = banner['banner_image']
					for attachment in template_misc_details["banner_header"]["attachment"]:
						if attachment['position'] == banner['position']:
							attachments = db_session.query(Attachment).filter(Attachment.attachment_id == attachment['attachment_id']).first()
							if not attachments and uploaded_banner_image != "":
								file_to_be_saved = uploaded_banner_image.split(",")
								fil_ext = file_to_be_saved[0][file_to_be_saved[0].index('/') + 1:file_to_be_saved[0].index(';')]
								doc_data = file_to_be_saved[1].decode('base64')
								logger.debug("File to be Saved:%s" % doc_data)
								filename = "%s.%s" % (uuid.uuid4(), fil_ext)
								temp_file = "/site_media/tmp/%s" % filename
								writeFile(doc_data, temp_file)
								FTPUtil().upload(
									file_path=enterprise_id, filename=filename,
									fp=open("%s/%s" % (TEMP_DIR, filename), 'rb'))
								attachment_id = saveAttachment(
									enterprise_id=enterprise_id,
									label="Purchase_Template_Header_Banner",
									file_to_be_saved=filename, ext=fil_ext, uploaded_by=user_id,
									uploaded_on=uploaded_on,
									db_session=db_session)

								header_banner_detail.append({
									"attachment_id": attachment_id, "uploaded_on": uploaded_on, "last_modified_by": user_id,
									"width": banner['width'], "uploaded_by": user_id, "position": banner['position'],
									"last_modified_on": uploaded_on})
								logger.info("Purchase Template Header Banner Attachment imported successfully")
							else:
								if uploaded_banner_image != "":
									file_to_be_saved = uploaded_banner_image.split(",")
									fil_ext = file_to_be_saved[0][file_to_be_saved[0].index('/') + 1:file_to_be_saved[0].index(';')]
									doc_data = file_to_be_saved[1].decode('base64')
									logger.debug("File to be Saved:%s" % doc_data)
									filename = "%s.%s" % (attachments.file.split(".")[0], fil_ext)
									temp_file = "/site_media/tmp/%s" % filename
									writeFile(doc_data, temp_file)
									FTPUtil().upload(
										file_path=enterprise_id, filename=filename,
										fp=open("%s/%s" % (TEMP_DIR, filename), 'rb'))
									attachments.file = filename
									attachments.file_ext = fil_ext
									header_banner_detail.append({
										"attachment_id": attachment['attachment_id'], "uploaded_on": attachment['uploaded_on'],
										"last_modified_by": user_id, "width": banner['width'],
										"uploaded_by": attachment['uploaded_by'], "position": attachment['position'],
										"last_modified_on": uploaded_on})
									db_session.add(attachments)
									logger.info("Purchase Template Header Banner Attachment updated successfully")
								else:
									header_banner_detail.append({
										"attachment_id": '', "uploaded_on": attachment['uploaded_on'],
										"last_modified_by": user_id, "width": banner['width'],
										"uploaded_by": attachment['uploaded_by'], "position": attachment['position'],
										"last_modified_on": uploaded_on})
			banner_details.append(header_banner_detail)

			for banner in template_footer_banner["footer_banner"]:
				if is_pdf_preview:
					updated_banner_image.append({
						'section': 'Footer', 'position': banner['position'], 'width': banner['width'],
						'banner_image': banner['banner_image']})
				else:
					uploaded_banner_image = banner['banner_image']
					for attachment in template_misc_details["banner_footer"]["attachment"]:
						if attachment['position'] == banner['position']:
							attachments = db_session.query(Attachment).filter(Attachment.attachment_id == attachment['attachment_id']).first()
							if not attachments and uploaded_banner_image != "":
								file_to_be_saved = uploaded_banner_image.split(",")
								fil_ext = file_to_be_saved[0][file_to_be_saved[0].index('/') + 1:file_to_be_saved[0].index(';')]
								doc_data = file_to_be_saved[1].decode('base64')
								logger.debug("File to be Saved:%s" % doc_data)
								filename = "%s.%s" % (uuid.uuid4(), fil_ext)
								temp_file = "/site_media/tmp/%s" % filename
								writeFile(doc_data, temp_file)
								FTPUtil().upload(
									file_path=enterprise_id, filename=filename,
									fp=open("%s/%s" % (TEMP_DIR, filename), 'rb'))
								attachment_id = saveAttachment(
									enterprise_id=enterprise_id,
									label="Purchase_Template_Footer_Banner",
									file_to_be_saved=filename, ext=fil_ext, uploaded_by=user_id,
									uploaded_on=uploaded_on,
									db_session=db_session)

								footer_banner_detail.append({
									"attachment_id": attachment_id, "uploaded_on": uploaded_on, "last_modified_by": user_id,
									"width": banner['width'], "uploaded_by": user_id, "position": banner['position'],
									"last_modified_on": uploaded_on})
								logger.info("Purchase Template Footer Banner Attachment imported successfully")
							else:
								if uploaded_banner_image != "":
									file_to_be_saved = uploaded_banner_image.split(",")
									fil_ext = file_to_be_saved[0][file_to_be_saved[0].index('/') + 1:file_to_be_saved[0].index(';')]
									doc_data = file_to_be_saved[1].decode('base64')
									logger.debug("File to be Saved:%s" % doc_data)
									filename = "%s.%s" % (attachments.file.split(".")[0], fil_ext)
									temp_file = "/site_media/tmp/%s" % filename
									writeFile(doc_data, temp_file)
									FTPUtil().upload(
										file_path=enterprise_id, filename=filename,
										fp=open("%s/%s" % (TEMP_DIR, filename), 'rb'))
									attachments.file = filename
									attachments.file_ext = fil_ext
									footer_banner_detail.append({
										"attachment_id": attachment['attachment_id'], "uploaded_on": attachment['uploaded_on'],
										"last_modified_by": user_id, "width": banner['width'],
										"uploaded_by": attachment['uploaded_by'], "position": attachment['position'],
										"last_modified_on" : uploaded_on})
									db_session.add(attachments)
									logger.info("Purchase Template Footer Banner Attachment updated successfully")
								else:
									footer_banner_detail.append({
										"attachment_id": '', "uploaded_on": attachment['uploaded_on'],
										"last_modified_by": user_id, "width": banner['width'],
										"uploaded_by": attachment['uploaded_by'], "position": attachment['position'],
										"last_modified_on": uploaded_on})
			banner_details.append(footer_banner_detail)
			return banner_details
		except Exception as e:
			logger.exception("The Purchase Template Banner Attachments Import Error... %s" % e.message)
			raise e
