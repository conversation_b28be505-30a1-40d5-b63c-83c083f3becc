listen 8113

WSGISocketPrefix /var/run/wsgi

<VirtualHost *:8113>
	ServerName xserp.in
    ServerA<PERSON><PERSON> ka<PERSON><EMAIL>
    # Node Server hosting along side php and python on a single virtual host
    ProxyRequests Off
    ProxyPreserveHost On
    ProxyVia Full
    <Proxy *>
         Require all granted
    </Proxy>

    <Location /crm>
        ProxyPass http://127.0.0.1:8443
        ProxyPassReverse http://127.0.0.1:8443
    </Location>
    # PHP web site configuration
	AddType application/x-httpd-php .php
	DocumentRoot /var/www/html/xserp

	<Directory "/var/www/html/xserp">
		AllowOverride All
		Order deny,allow
		Require all granted
	</Directory>

    # ERP python application configuration
    WSGIDaemonProcess XSerp-dev python-path=/opt/deployment/XSerp:/opt/.envs/xserp_up/lib/python2.7/site-packages/
    WSGIProcessGroup XSerp-dev
    WSGIScriptAlias /erp /opt/deployment/XSerp/xserp.wsgi process-group=XSerp-dev
    Alias /site_media /opt/deployment/XSerp/site_media
    <Directory /erp/>
        Options Indexes FollowSymLinks
        AllowOverride None
        Allow from all
        LogLevel info
    </Directory>
    <Directory "/opt/deployment/XSerp/site_media">
        Order deny,allow
        Require all granted
    </Directory>
    <Directory "/opt/deployment/XSerp">
        AllowOverride All
        Order deny,allow
        Require all granted
        LogLevel info
    </Directory>

    # Apache Log for the application
    ErrorLog ${APACHE_LOG_DIR}/xserp-error.log
    CustomLog ${APACHE_LOG_DIR}/xserp-access.log combined

</VirtualHost>