$(function () {
    $(document).ready(function () {
		setTimeout(function(){
			$(".chosen-select").chosen();
		},500);
        $('#id_price_material-__prefix__-currency_id').prop('disabled',true);
        $('#importmaterial_status_modal').on('hidden.bs.modal', function () {
            window.location = "/erp/masters/catalogues/";
        });
        $('#id_bill_material-__prefix__-makes').multiselect();
        $("select#id_bill_material-__prefix__-makes").next('.btn-group').find('ul').find('li input').prop('checked', true)
        reassemblePriceProfile();
        create_delete_make_button();
    });
	if($("#last_saved_item_name").val()!=undefined){
        if($("#last_saved_item_name").val()!="" && $("#last_saved_item_name").val()!="None"){
            $("button.confirm").removeClass('processing');
            swal({
                text: "",
                title: "Last Modified Item Name:<br> " + $("#last_saved_item_name").val(),
                allowEscapeKey : false,
                allowOutsideClick: false
            },
            function(){
                setTimeout(function(){
                    $( window ).resize();
                },10);
            });
            $("#last_saved_item_name").val("");
        }
    }
    $('#add_new_catalogue_material').click(function () {
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();
        var ControlCollections =[
            {
                controltype: 'textbox',
                controlid: 'material_selected',
                isrequired: true,
                errormsg: 'Material name is required.'
            },
            {
                controltype: 'textbox',
                controlid: 'id_bill_material-__prefix__-quantity',
                isrequired: true,
                errormsg: 'Quantity is required.'
            }
        ];
        var result = JSCustomValidator.JSvalidate(ControlCollections);


        if(result) {
            refreshSessionPerNActions(10);
            var parent_bom = $("#id_material-material_id").val();
            var add_child_bom = $("#id_bill_material-__prefix__-item_id").val();
            var check_parent_item
            check_parent_item = false
            var parent_id = JSON.parse(sessionStorage.getItem("parent_mat_list"));
            for(var i = 0; i < parent_id.length ; i++){
                if (parent_id[i] == add_child_bom){
                    $("button.confirm").removeClass('processing');
                    swal('', 'Cannot add Items in Parental lineage to the BoM!', 'error');
                    check_parent_item = true;
                    return false;
                }
            }
            if (add_child_bom == parent_bom) {
                $("button.confirm").removeClass('processing');
                swal('', 'Selected material cannot be BOM of itself.', 'error');
                check_parent_item = true;
                return false;
            }

            if (check_parent_item == false) {
                var newQtyField = document.getElementById('id_bill_material-__prefix__-quantity');

                if (newQtyField.value == "" || newQtyField.value == 0) {
                    //alert("Please enter Qty");
                    return;
                } else {
                    var material_table = document.getElementById("materialtable_bom");
                    var row_count = material_table.rows.length;
                    var match = false;
                    var make_match = false;

                    for (i = 1; i < row_count; i++) {
                        if ($('#id_bill_material-' + parseInt(i-1) + '-item_id').val() == $('#id_bill_material-__prefix__-item_id').val()) {
                            match = true;
                            if (document.getElementById('id_bill_material-' + parseInt(i-1) + '-DELETE').checked){

                            var matched_row = document.getElementById('bill_material-' + parseInt(i-1));
                            document.getElementById('id_bill_material-' + parseInt(i-1) + '-quantity').value = document.getElementById('id_bill_material-__prefix__-quantity').value;
                            document.getElementById('id_bill_material-' + parseInt(i-1) + '-DELETE').checked = false;
                            if(!$(`#bill_material-${parseInt(i-1)}`).hasClass('newly_added_item')) {
                                document.getElementById('id_bill_material-' + parseInt(i-1) + '-quantityLabel').innerHTML = document.getElementById('id_bill_material-__prefix__-quantity').value ;
                            }
                            document.getElementById('id_bill_material-' + parseInt(i-1) + '-is_service').value = document.getElementById('id_bill_material-__prefix__-is_service').value ;

                                matched_row.style.display = '';
                                document.getElementById('id_bill_material-__prefix__-drawing_no').value = 'None';
                                document.getElementById('id_bill_material-__prefix__-item_id').value = '';
                                document.getElementById('id_bill_material-__prefix__-quantity').value = '';
                                document.getElementById('id_bill_material-__prefix__-units').value = '';
                                document.getElementById("material_selected").value = "";
                                document.getElementById("drawing_no_selected").value = "";
                                document.getElementById("unit_display").innerHTML = "&nbsp;";
                            }
                            else {
                                swal({
                                    title: "Duplicate Material Exists!",
                                    text: "Do you still want to replace the Material?",
                                    type: "warning",
                                    showCancelButton: true,
                                    confirmButtonColor: "#209be1",
                                    confirmButtonText: "Yes, do it!",
                                    closeOnConfirm: true,
                                    closeOnCancel: true
                                },
                                function(isConfirm){
                                    if (isConfirm) {
                                        document.getElementById('id_bill_material-' + parseInt(i-1) + '-quantity').value = parseFloat(document.getElementById('id_bill_material-__prefix__-quantity').value);

                                    if($('#id_bill_material-' + parseInt(i-1) + '-quantityLabel').find("input").length > 0) {
                                        $('#id_bill_material-' + parseInt(i-1) + '-quantityLabel').find("input").val(parseFloat($('#id_bill_material-' + parseInt(i-1) + '-quantity').val()).toFixed(5));
                                    }
                                    else {
                                        document.getElementById('id_bill_material-' + parseInt(i-1) + '-quantityLabel').innerHTML = parseFloat($('#id_bill_material-' + parseInt(i-1) + '-quantity').val()).toFixed(5);
                                    }
                                }
                                document.getElementById('id_bill_material-__prefix__-drawing_no').value = 'None';
                                document.getElementById('id_bill_material-__prefix__-item_id').value = '';
                                document.getElementById('id_bill_material-__prefix__-quantity').value = '';
                                document.getElementById('id_bill_material-__prefix__-units').value = '';
                                document.getElementById("material_selected").value = "";
                                document.getElementById("drawing_no_selected").value = "";
                                document.getElementById("unit_display").innerHTML = "&nbsp;";
                            });
                        }
                        break;
                    }
                }

                    if (!match) {
                        var form_idx = parseInt($('#id_bill_material-TOTAL_FORMS').val());
                        new_form = $('#bill_material-__dummy__').html().replace(/__prefix__/g, form_idx);
                        new_form_html = "<tr bgcolor=\"#ECECEC\" id=\"bill_material-" + form_idx +
                            "\" align=\"center\" class='newly_added_item'>" + new_form + "</tr>";
                        $(new_form_html).insertAfter('#bill_material-__dummy__');
                        $('#id_bill_material-TOTAL_FORMS').val(form_idx + 1);
                        copyFromEmptyForm(form_idx);
                        document.getElementById("material_selected").value = "";
                        document.getElementById("drawing_no_selected").value = "";
                        document.getElementById("unit_display").innerHTML = "&nbsp;";
                        $('#id_bill_material-__prefix__-makes').multiselect('destroy');
                        $('#id_bill_material-__prefix__-makes').html('');
                    }
                    $('#id_bill_material-__prefix__-makes').multiselect();
                }
            }


//            $.ajax({
//                url: "/erp/masters/json/materials/get_bom_parentid/",
//                type: "POST",
//                dataType: "json",
//                data: {material_id: parent_bom},
//                success: function (response) {
//                    try {
//                        $.each(response, function(i, item) {
//                            if (item.parent_id == add_child_bom){
//                                $("button.confirm").removeClass('processing');
//                                swal('', 'Selected material ....', 'error');
//                                check_parent_item = true;
//                                return false;
//                            }
//                        });
//                        if (add_child_bom == parent_bom) {
//                            $("button.confirm").removeClass('processing');
//                            swal('', 'Selected material cannot be BOM of itself.', 'error');
//                            check_parent_item = true;
//                            return;
//                        }
//                        if (check_parent_item == false) {
//                            var newQtyField = document.getElementById('id_bill_material-__prefix__-quantity');
//
//                            if (newQtyField.value == "" || newQtyField.value == 0) {
//                                //alert("Please enter Qty");
//                                return;
//                            } else {
//                                var material_table = document.getElementById("materialtable_bom");
//                                var row_count = material_table.rows.length;
//                                var match = false;
//                                var make_match = false;
//
//                                for (i = 1; i < row_count; i++) {
//                                    if ($('#id_bill_material-' + parseInt(i-1) + '-item_id').val() == $('#id_bill_material-__prefix__-item_id').val()) {
//                                        match = true;
//                                        if (document.getElementById('id_bill_material-' + parseInt(i-1) + '-DELETE').checked){
//
//                                            var matched_row = document.getElementById('bill_material-' + parseInt(i-1));
//                                            document.getElementById('id_bill_material-' + parseInt(i-1) + '-quantity').value = document.getElementById('id_bill_material-__prefix__-quantity').value;
//                                            document.getElementById('id_bill_material-' + parseInt(i-1) + '-DELETE').checked = false;
//                                            var selected_makes= "";
//                                            var selected_makes_id="";
//                                            var multi_select_make =  $("select#id_bill_material-__prefix__-makes").next('.btn-group').find('ul').find('li input:checked');
//                                            var make_ids = [];
//                                            var make_labels = [];
//                                            multi_select_make.each(function () {
//                                                make_labels.push($(this).closest('label').text().trim());
//                                                make_ids.push($(this).val());
//                                            });
//                                            if (make_ids.length > 0) {
//                                                selected_makes = make_labels.join(", ");
//                                                selected_makes_id = make_ids.join(",");
//                                            }
//                                            document.getElementById('id_bill_material-' + parseInt(i-1) + '-makeLabel').innerHTML = selected_makes ;
//                                            document.getElementById('id_bill_material-' + parseInt(i-1) + '-quantityLabel').innerHTML = document.getElementById('id_bill_material-__prefix__-quantity').value ;
//                                            document.getElementById('id_bill_material-' + parseInt(i-1) + '-make_id').value = selected_makes_id;
//
//                                            matched_row.style.display = '';
//                                            document.getElementById('id_bill_material-__prefix__-drawing_no').value = 'None';
//                                            document.getElementById('id_bill_material-__prefix__-item_id').value = '';
//                                            document.getElementById('id_bill_material-__prefix__-quantity').value = '';
//                                            document.getElementById('id_bill_material-__prefix__-units').value = '';
//                                            document.getElementById("material_selected").value = "";
//                                            document.getElementById("drawing_no_selected").value = "";
//                                            document.getElementById("unit_display").innerHTML = "&nbsp;";
//                                            $('#id_bill_material-__prefix__-makes').multiselect('destroy');
//                                            $('#id_bill_material-__prefix__-makes').html('');
//                                        }
//                                        else {
//                                            swal({
//                                                title: "Duplicate Material Exists!",
//                                                text: "Do you still want to replace the Material?",
//                                                type: "warning",
//                                                showCancelButton: true,
//                                                confirmButtonColor: "#209be1",
//                                                confirmButtonText: "Yes, do it!",
//                                                closeOnConfirm: true,
//                                                closeOnCancel: true
//                                            },
//                                            function(isConfirm){
//                                                if (isConfirm) {
//                                                    var selected_makes= "";
//                                                    var selected_makes_id="";
//                                                    var multi_select_make =  $("select#id_bill_material-__prefix__-makes").next('.btn-group').find('ul').find('li input:checked');
//
//                                                    var make_ids = [];
//                                                    var make_labels = [];
//                                                    multi_select_make.each(function () {
//                                                        make_labels.push($(this).closest('label').text().trim());
//                                                        make_ids.push($(this).val());
//                                                    });
//                                                    if (make_ids.length > 0) {
//                                                        selected_makes = make_labels.join(", ");
//                                                        selected_makes_id = make_ids.join(",");
//                                                    }
//
//                                                    if($('#id_bill_material-' + parseInt(i-1) + '-makeLabel').find("input").length > 0) {
//                                                        $('#id_bill_material-' + parseInt(i-1) + '-makeLabel').find("input").val(selected_makes);
//                                                    }
//                                                    else {
//                                                        $('#id_bill_material-' + parseInt(i-1) + '-makeLabel').html(selected_makes);
//                                                    }
//
//                                                    document.getElementById('id_bill_material-' + parseInt(i-1) + '-quantity').value = parseFloat(document.getElementById('id_bill_material-__prefix__-quantity').value);
//
//                                                    if($('#id_bill_material-' + parseInt(i-1) + '-quantityLabel').find("input").length > 0) {
//                                                        $('#id_bill_material-' + parseInt(i-1) + '-quantityLabel').find("input").val(parseFloat($('#id_bill_material-' + parseInt(i-1) + '-quantity').val()).toFixed(5));
//                                                    }
//                                                    else {
//                                                        document.getElementById('id_bill_material-' + parseInt(i-1) + '-quantityLabel').innerHTML = parseFloat($('#id_bill_material-' + parseInt(i-1) + '-quantity').val()).toFixed(5);
//                                                    }
//                                                    document.getElementById('id_bill_material-' + parseInt(i-1) + '-make_id').value = selected_makes_id;
//                                                }
//                                                document.getElementById('id_bill_material-__prefix__-drawing_no').value = 'None';
//                                                document.getElementById('id_bill_material-__prefix__-item_id').value = '';
//                                                document.getElementById('id_bill_material-__prefix__-quantity').value = '';
//                                                document.getElementById('id_bill_material-__prefix__-units').value = '';
//                                                document.getElementById("material_selected").value = "";
//                                                document.getElementById("drawing_no_selected").value = "";
//                                                document.getElementById("unit_display").innerHTML = "&nbsp;";
//                                                $('#id_bill_material-__prefix__-makes').multiselect('destroy');
//                                                $('#id_bill_material-__prefix__-makes').html('');
//                                            });
//                                        }
//                                        break;
//                                    }
//                                }
//
//                                if (!match) {
//                                    var form_idx = parseInt($('#id_bill_material-TOTAL_FORMS').val());
//                                    new_form = $('#bill_material-__dummy__').html().replace(/__prefix__/g, form_idx);
//                                    new_form_html = "<tr bgcolor=\"#ECECEC\" id=\"bill_material-" + form_idx +
//                                        "\" align=\"center\" class='newly_added_item'>" + new_form + "</tr>";
//                                    $(new_form_html).insertAfter('#bill_material-__dummy__');
//                                    $('#id_bill_material-TOTAL_FORMS').val(form_idx + 1);
//                                    copyFromEmptyForm(form_idx);
//                                    document.getElementById("material_selected").value = "";
//                                    document.getElementById("drawing_no_selected").value = "";
//                                    document.getElementById("unit_display").innerHTML = "&nbsp;";
//                                    $('#id_bill_material-__prefix__-makes').multiselect('destroy');
//                                    $('#id_bill_material-__prefix__-makes').html('');
//                                }
//                                $('#id_bill_material-__prefix__-makes').multiselect();
//                            }
//                        }
//
//                    }
//                    catch (Exception) {
//                       console.log("Exception:" + Exception);
//                    }
//                }
//            });
        }
    });

    $("#add_new_supplier_material").click(function(){
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();
        var ControlCollections = [
            {
                controltype: 'textbox',
                controlid: 'supplier_selected',
                isrequired: true,
                errormsg: 'Supplier is required.'
            },
            {
                controltype: 'textbox',
                controlid: 'id_price_material-__prefix__-price',
                isrequired: true,
                errormsg: 'Price is required.'
            },
            {
                controltype: 'textbox',
                controlid: 'id_material-effect_since',
                isrequired: true,
                errormsg: 'Date is required.'
            }
         ];
         if ($("#id_price_material-__prefix__-price").val() != "" && $("#supplier_selected").val().trim() !=""){
            var control = {
                controltype: 'dropdown',
                controlid: 'id_price_material-__prefix__-currency_id',
                isrequired: true,
                errormsg: 'Currency is required.'
            };
        }
        ControlCollections[ControlCollections.length] = control;
        var result = JSCustomValidator.JSvalidate(ControlCollections);
        if(result) {
            refreshSessionPerNActions(10);
            var newQtyField = document.getElementById('id_price_material-__prefix__-price');
            var spqValue = Number($('#id_price_material-__prefix__-make_choices option:selected').attr("data-spq"));
            var currentQty = Number($("#id_price_material-__prefix__-moq").val());
            var currentUnit = $("#quantity_unit_display").text();
            if (newQtyField.value == "" || newQtyField.value == 0) {
                swal({
                    title: "Are you sure?",
                    text: `The Price value for the Supplier <b>${$("#supplier_selected").val()}</b> is Zero. <br />Do you want to continue?`,
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#209be1",
                    confirmButtonText: "Yes!",
                    closeOnConfirm: true
                },
                function(){
                    validatePriceProfileMoq(spqValue, currentQty, currentUnit, 'add' ,'add');
                });
            }
            else {
                validatePriceProfileMoq(spqValue, currentQty, currentUnit, 'add' ,'add');
            }
        }
    });

    $('#id_material-unit_id').blur(function () {
        if ($('#id_material-current_unit').val() != 'None' && ($('#id_material-unit_id').val() != $('#id_material-current_unit').val()) ) {
            unitChangeConversion();
        }
    });

    $("#id_material-unit_conversion_rate").blur(function(){
        setTimeout(function(){
            var convertedValue = parseFloat($("#original_price").val())/parseFloat($("#id_material-unit_conversion_rate").val());
            $("#id_material-price").val(convertedValue.toFixed(2));
        },100);
    });

    $('#save_material_button').click(function () {
        clickButton('saveMaterial');
    });

    $(".set-price-primary").find('input').change((current) => {
        $(".set-price-primary").find('input').removeAttr("checked");
        const currentId = current.currentTarget.id;
        $(`#${currentId}`).prop("checked", true)
        change_store_value();
    });

    $('#indMaterialForms').ready(function () {
        var initialFormCount = parseInt(document.getElementById('id_price_material-TOTAL_FORMS').value);
        for (i = 0; i < initialFormCount; i++) {
            $('#id_price_material-' + i + '-makeLabel').html($('#id_price_material-' + i + '-make_select_field').val());
        }
    });
});

function validatePriceProfileMoq(spqValue, qty, unit, current, type) {
    var alternateUnitValue = 1;
    var defaultValue = qty;
    if(!$("#price_profile-alternate_units").hasClass("hide")) {
        if(type == "edit") {
            var alternate_unit_id = Number($("#"+current).closest("tr").find(`input[name*='alternate_unit_id']`).val());
            alternateUnitValue = Number($(`#price_profile-alternate_units option[value='${alternate_unit_id}']`).attr("data-val"));
        }
        else {
            alternateUnitValue = $("#price_profile-alternate_units option:selected").attr("data-val");  
        }
        
        unit = $("#price_profile-alternate_units option:selected").text();
        spqValue = spqValue / alternateUnitValue;
    }
    
    var checkMod = (qty / spqValue).toFixed(3);
    if (!Number.isInteger(Number(checkMod)) && spqValue > 0){
        var ceilValue = moq_round_ceil(spqValue, qty) ;
        var floorValue = moq_round_floor(spqValue, qty);
        var swalText = `<span class='btn-custom-swal' data-tooltip="tooltip" title="Round-down to previous SPQ" onclick="setMoqValue(${floorValue}, '${current}', '${type}');">${floorValue}</span>`;
        //swalText += `<span class='btn-custom-swal' data-tooltip="tooltip" title="Use As-is" onclick="setMoqValue(${defaultValue}, '${current}', '${type}');">${Number(defaultValue).toFixed(3)}</span>`;
        swalText += `<span class='btn-custom-swal' data-tooltip="tooltip" title="Round-up to next SPQ" onclick="setMoqValue(${ceilValue}, '${current}', '${type}');">${ceilValue}</span>`;
        swal({
            title: "<h4 style='line-height:23px;'>Minimum Order Quantity specified does not confirm with SPQ!<br /><small>(not a multiple of the SPQ - " + spqValue + " "+ unit + ").</small></h4><h5 style='margin-top: 15px;'>Choose Appropriate Quantity</h5>",
            text: swalText,
            type: "warning",
            showCancelButton: false,
            showConfirmButton: false,
            closeOnConfirm: true,
            allowOutsideClick: false,
            allowEscapeKey: false
        });
        TooltipInit();
    }
    else {
        if(type == "add") {
            addSupplierProfilePrice();
        }
    }
}

function setMoqValue(value, current, type) {
    swal.close();
    if(type == "add") {
        $("#id_price_material-__prefix__-moq").val(Number(value).toFixed(3));
        addSupplierProfilePrice();
    }
    else if(type == "edit") {
        $("#"+current).val(Number(value).toFixed(3));
    }
}

function moq_round_ceil(moq_value, input_value) {
    return (Math.ceil(input_value / moq_value) * moq_value).toFixed(3);
}

function moq_round_floor(moq_value, input_value) {
    return (Math.floor(input_value / moq_value) * moq_value).toFixed(3);
}

function validateInlineMoq(current){
    var currentElementId = $(current).attr("id");
    if(currentElementId != "id_price_material-__prefix__-moq") {
        var currentMakeId = $(current).closest("tr").find(`input[name*='make_id']`).val();
        var currentSpqValue = $(`#id_price_material-__prefix__-make_choices option[value=${currentMakeId}]`).attr("data-spq");
        var currentQty = $(current).val();
        var currentUnit = $(current).closest("tr").find(".td-uom").text();
        validatePriceProfileMoq(currentSpqValue, currentQty, currentUnit, currentElementId, 'edit');
    }
}

function addSupplierProfilePrice() {
    var supplier_price_table = document.getElementById("supp_price_table");
    var profile_count = parseInt($('#id_price_material-TOTAL_FORMS').val());
    var match = false;

    for (i = 0; i < profile_count; i++) {
        if ($('#id_price_material-' + i + '-supp_id').val() == $('#id_price_material-__prefix__-supp_id').val() && $('#id_price_material-' + i + '-effect_since').val() == $('#id_material-effect_since').val() && $('#id_price_material-__prefix__-make_choices option:selected').val()==$('#id_price_material-' + i + '-make_id').val()) {
            $('#id_price_material-' + i + '-DELETE').attr("checked", true);
            $('#price_material-' + i).hide();
            break;
        }
    }

    var form_idx = parseInt($('#id_price_material-TOTAL_FORMS').val());
    new_form = $('#price_material-__dummy__').html().replace(/__prefix__/g, form_idx);

    new_form_html = `<tr style="background: rgba(32,155,255,0);" id="price_material-${form_idx}" align="center" class="effect_date_parent">${new_form}</tr>`;

    if($("#supp_price_table").find("tr[data-rowHeading='"+$("#supplier_no_selected").val()+"']").length <=0) {
        var row = ` <tr style="background: rgba(32,155,255,0.15) !important;" data-rowHeading = '${$("#supplier_no_selected").val()}'>
                        <td style="color: #444;font-size: 106%; text-shadow: 0 0 #444;letter-spacing: 0.5px;" colspan='11'>${$("#supplier_selected").val()}</td>
                    </tr>`;
        $(row).insertAfter('#price_material-__dummy__');
    }

    var row = `<tr class="general-remarks hide">
                    <td colspan="3" class="text-right"><label>Remarks:</label></td>
                    <td colspan="8">
                        <input class="form-control" id="id_price_material-${form_idx}-remarks" maxlength="160" name="price_material-${form_idx}-remarks" placeholder="Remarks" type="text">
                    </td>
                </tr>`;
    new_form_html += row;

    $(new_form_html).insertAfter($("#supp_price_table").find("tr[data-rowHeading='"+$("#supplier_no_selected").val()+"']"));
    $('#id_price_material-TOTAL_FORMS').val(form_idx + 1);
    copyFromEmptyForm_supp_price(form_idx);
    $("#supplier_selected").val("");
    $("#drawing_no_selected").val("");

    document.getElementById('id_price_material-__prefix__-item_id').value = '';
    document.getElementById('id_price_material-__prefix__-price').value = '0.00';
    document.getElementById('id_price_material-__prefix__-remarks').value = '';
    document.getElementById('id_price_material-__prefix__-effect_till').value = '';
    document.getElementById('id_price_material-__prefix__-make_choices').value = '1';
    $('#id_material-effect_till, #effect_till_date').val('');
    $('#currency_unit_display').html('&nbsp');
    $(".supplier-removal-icon").addClass("hide");
    setTimeout(function(){
        CustomDatePickerInit();
        ValidateEffectDate();
        $(".price_profile_remarks").attr("data-tooltip","tooltip");
        TooltipInit();
        $("#supp_price_table").find(".effect_since_child").each(function(){
            var selDate = $(this).prev("input").val();
            $(this).closest(".effect_date_parent").find(".effect_till_child").datepicker("setStartDate", new Date(selDate));
        })
    },100);
}



function change_store_value(){
    var supplier_price_table = document.getElementById("supp_price_table");
        var row_count = supplier_price_table.rows.length;
        for (i = 0; i < row_count-1; i++) {
            if (document.getElementById('id_price_material-' + i + '-is_primary').checked) {
                const currency_id = $('#price_material-' + i).find('.price-currency-field').text().trim();
                const homeCurrencyValue = $("#home_currency_id").val();
                if(currency_id !== homeCurrencyValue) {
                    var item_json = {"from_currency": currency_id, "to_currency": homeCurrencyValue};
                    $.ajax({
                       url : "/erp/commons/json/currency_converter/",
                       type : "POST",
                       dataType: "json",
                       data :  item_json,
                       success : function(response) {
                          const convertedValue = response.result;
                          const currentPrice = $(`#id_price_material-${i}-price`).val();
                          document.getElementById('id_material-price').value = convertedValue * currentPrice;
                       }
                    });
                }
                else {
                    document.getElementById('id_material-price').value = document.getElementById('id_price_material-' + i + '-price').value;
                }
                return false
            }

        }
}

function import_catalogue_material(data){

    for (j=0; j< data.length; j++) {
        //event.preventDefault();
        $("#drawing_no_selected").val(data[j]['drawing_no_selected']);
        $("#material_selected").val(data[j]['material_selected']);
        $("#unit_display").html(data[j]['unit_display']);
        $("#id_bill_material-__prefix__-material_select_field").val(data[j]['material_selected']);
        $("#id_bill_material-__prefix__-drawing_no").val(data[j]['drawing_no_selected']);
        $("#id_bill_material-__prefix__-item_id").val(data[j]['item_id']);
        $("#id_bill_material-__prefix__-is_service").val(data[j]['is_service']);
        $("#id_bill_material-__prefix__-quantity").val(data[j]['id_bill_material-__prefix__-quantity']);
        $("#id_bill_material-__prefix__-units").val(data[j]['unit_display']);
        $("#id_bill_material-__prefix__-make_select_field").val(data[j]['selected_makes']);
        $("#id_bill_material-__prefix__-make_id").val(data[j]['selected_id']);
        $('#add_new_catalogue_material').trigger('click');

    }
}


function copyFromEmptyForm(form_idx) {
    var new_form_drawing_no = document.getElementById('id_bill_material-' + form_idx + '-drawing_no');
    var new_form_item_id = document.getElementById('id_bill_material-' + form_idx + '-item_id');
    var new_form_enterprise_id = document.getElementById('id_bill_material-' + form_idx + '-enterprise_id');
    var new_form_material_select_field = document.getElementById('id_bill_material-' + form_idx + '-material_select_field');
    var new_form_quantity = document.getElementById('id_bill_material-' + form_idx + '-quantity');
    var new_form_units = document.getElementById('id_bill_material-' + form_idx + '-units');
    var new_form_make_select_field = document.getElementById('id_bill_material-' + form_idx + '-make_select_field');
    var new_form_make_id = document.getElementById('id_bill_material-' + form_idx + '-make_id');
    var new_form_is_service = document.getElementById('id_bill_material-' + form_idx + '-is_service');

    new_form_enterprise_id.value = document.getElementById('id_material-enterprise_id').value;
    new_form_drawing_no.value = document.getElementById('id_bill_material-__prefix__-drawing_no').value;
    new_form_item_id.value = document.getElementById('id_bill_material-__prefix__-item_id').value;
    new_form_material_select_field.value = document.getElementById("id_bill_material-__prefix__-material_select_field").value;
    new_form_quantity.value = document.getElementById('id_bill_material-__prefix__-quantity').value;
    new_form_units.value = document.getElementById('id_bill_material-__prefix__-units').value;
//    new_form_make_id.value = document.getElementById('id_bill_material-__prefix__-make_id').value;
//    new_form_make_select_field.value = document.getElementById("id_bill_material-__prefix__-make_select_field").value;
    new_form_is_service.value = document.getElementById("id_bill_material-__prefix__-is_service").value;
    var selected_makes= "";
    var selected_makes_id="";
	var multi_select_make =  $("select#id_bill_material-__prefix__-makes").next('.btn-group').find('ul').find('li input:checked');
	var make_ids = [];
	var make_labels = [];
    multi_select_make.each(function () {
        make_labels.push($(this).closest('label').text().trim());
        make_ids.push($(this).val());
    });
    if (make_ids.length > 0) {
        new_form_make_select_field.value = make_labels.join(", ");
        new_form_make_id.value = make_ids.join(",");
    }
    if(new_form_is_service.value == 1) {
        $('<span class="service-item-flag"></span>').insertAfter( new_form_material_select_field );
        $(new_form_material_select_field).addClass("item_text_box");
        $("#materialtable_bom .service-item-flag").addClass("floated-right-flag");
    }
    document.getElementById('id_bill_material-__prefix__-item_id').value = '';
    document.getElementById('id_bill_material-__prefix__-drawing_no').value = '';
    document.getElementById('id_bill_material-__prefix__-material_select_field').value = '';
    document.getElementById('id_bill_material-__prefix__-quantity').value = '';
    document.getElementById('id_bill_material-__prefix__-units').value = '';
}

function copyFromEmptyFormAlternateUnit(form_idx) {
    var new_form_alternate_unit_id = document.getElementById('id_alternate_unit-' + form_idx + '-alternate_unit_id');
    var new_form_enterprise_id = document.getElementById('id_alternate_unit-' + form_idx + '-enterprise_id');
    var new_form_scale_factor = document.getElementById('id_alternate_unit-' + form_idx + '-scale_factor');
    var new_form_unit_name = document.getElementById('id_alternate_unit-' + form_idx + '-unit_name');
    var new_form_is_service = document.getElementById('id_alternate_unit-' + form_idx + '-is_service');
    var new_form_primary_unit_name = document.getElementById('id_alternate_unit-' + form_idx + '-primary_unit_name');
    new_form_enterprise_id.value = document.getElementById('id_material-enterprise_id').value;
    new_form_alternate_unit_id.value = document.getElementById('id_alternate_unit-__prefix__-alternate_unit_id').value;
    new_form_scale_factor.value = document.getElementById('id_alternate_unit-__prefix__-scale_factor').value;
    new_form_is_service.value = document.getElementById('id_alternate_unit-__prefix__-is_service').value;
    new_form_unit_name.value = $("#id_alternate_unit-__prefix__-alternate_unit_id option:selected").text();
    $('#id_alternate_unit-' + form_idx + '-primary_unit_name').text($("#id_material-unit_id option:selected").text());
    document.getElementById('id_alternate_unit-__prefix__-scale_factor').value = '';
}

function copyFromEmptySpecificationForm(form_prefix) {
    $('#id_specification-' + form_prefix + '-parameter').val($('#id_specification-__prefix__-parameter').val());
    $('#id_specification-' + form_prefix + '-comments').val($('#id_specification-__prefix__-comments').val());
    $('#id_specification-' + form_prefix + '-inspection_method').val($('#id_specification-__prefix__-inspection_method').val());
    $('#id_specification-' + form_prefix + '-min_value').val($('#id_specification-__prefix__-min_value').val());
    $('#id_specification-' + form_prefix + '-max_value').val($('#id_specification-__prefix__-max_value').val());
    $('#id_specification-' + form_prefix + '-unit').val($('#id_specification-__prefix__-unit').val());
    $('#id_specification-' + form_prefix + '-reaction_plan').val($('#id_specification-__prefix__-reaction_plan').val());
    $('#id_specification-' + form_prefix + '-qc_critical').prop("checked", $('#id_specification-__prefix__-qc_critical').prop("checked"));
    $('#id_specification-' + form_prefix + '-enterprise_id').val($('#id_specification_enterprise_id').val());
    $('#id_specification-' + form_prefix + '-material_id').val($('#id_specification_material_id').val());

    $("#edit_specification_modal").find("input[type='text']").val("");
    $("#edit_specification_modal").find("input[type='checkbox']").attr("checked", false);
}

function copyFromEmptyForm_supp_price(form_idx) {
    var new_form_supp_id = document.getElementById('id_price_material-' + form_idx + '-supp_id');
    var new_form_item_id = document.getElementById('id_price_material-' + form_idx + '-item_id');
    var new_form_enterprise_id = document.getElementById('id_price_material-' + form_idx + '-enterprise_id');
    var new_form_supplier_select_field = document.getElementById('id_price_material-' + form_idx + '-supplier_select_field');
    var new_form_price = document.getElementById('id_price_material-' + form_idx + '-price');
    var new_form_moq = document.getElementById('id_price_material-' + form_idx + '-moq');
    var new_form_alternate_unit_id = document.getElementById('id_price_material-' + form_idx + '-alternate_unit_id');
    var new_form_unit_name = document.getElementById('id_price_material-' + form_idx + '-unit_name');
    var new_form_lead_time = document.getElementById('id_price_material-' + form_idx + '-lead_time');
    var new_form_remarks = document.getElementById('id_price_material-' + form_idx + '-remarks');
    var new_form_effect_since = document.getElementById('id_price_material-' + form_idx + '-effect_since');
    var new_form_effect_till = document.getElementById('id_price_material-' + form_idx + '-effect_till');
    var new_form_currency_id = document.getElementById('id_price_material-' + form_idx + '-currency_id');
    var new_form_make_select_field = document.getElementById('id_price_material-' + form_idx + '-make_select_field');
    var new_form_make_id = document.getElementById('id_price_material-' + form_idx + '-make_id');
    var new_form_is_service = document.getElementById('id_price_material-' + form_idx + '-is_service');
    var new_form_currency_select_field = document.getElementById('id_price_material-' + form_idx + '-currency_select_field');

    new_form_supp_id.value = document.getElementById('id_price_material-__prefix__-supp_id').value;
    new_form_enterprise_id.value = document.getElementById('id_material-enterprise_id').value;
    new_form_item_id.value = document.getElementById('id_material-material_id').value;
    new_form_supplier_select_field.value = document.getElementById("id_price_material-__prefix__-supplier_select_field").value;
    new_form_moq.value = document.getElementById('id_price_material-__prefix__-moq').value;
    if($("#price_profile-alternate_units").val() != 0) {
        new_form_alternate_unit_id.value = document.getElementById('price_profile-alternate_units').value;
    }
    new_form_unit_name.value = document.getElementById('id_price_material-__prefix__-unit_name').value;
    new_form_lead_time.value = document.getElementById('id_price_material-__prefix__-lead_time').value;
    new_form_price.value = document.getElementById('id_price_material-__prefix__-price').value;
    new_form_remarks.value = document.getElementById('id_price_material-__prefix__-remarks').value;
    new_form_effect_since.value = document.getElementById('id_material-effect_since').value;
    new_form_effect_till.value = document.getElementById('id_material-effect_till').value;
    new_form_currency_id.value = document.getElementById('id_price_material-__prefix__-currency_id').value;
    new_form_make_id.value = document.getElementById('id_price_material-__prefix__-make_choices').value;
    new_form_is_service.value = document.getElementById('id_price_material-__prefix__-is_service').value;
    new_form_make_select_field.value = $("#id_price_material-__prefix__-make_choices option:selected").text();
    new_form_currency_select_field.value = $("#id_price_material-__prefix__-currency_id option:selected").text();

    $('#id_price_material-' + form_idx + '-currency_select_field').closest("td").find('.price-profile-unit').text(new_form_currency_select_field.value);
    if($("#price_profile-alternate_units").hasClass("hide")) {
        $('#id_price_material-' + form_idx + '-moq').closest("td").find('.price-profile-moq-unit').text($("#quantity_unit_display").text());
    }
    else {
        $('#id_price_material-' + form_idx + '-moq').closest("td").find('.price-profile-moq-unit').text($("#price_profile-alternate_units option:selected").text().split(" (")[0]);    
    }
    

    document.getElementById('id_price_material-__prefix__-item_id').value = '';
    document.getElementById('id_price_material-__prefix__-supp_id').value = '';
    document.getElementById('id_price_material-__prefix__-supplier_select_field').value = '';
    document.getElementById('id_price_material-__prefix__-price').value = '';
    document.getElementById('id_price_material-__prefix__-moq').value = '';
    document.getElementById('id_price_material-__prefix__-lead_time').value = '';
    document.getElementById('id_price_material-__prefix__-remarks').value = '';
    document.getElementById('id_price_material-__prefix__-currency_id').value = 'None';
    $('#currency_unit_display').html('&nbsp');
}

function loadMaterialLabels() {
    var initialFormCount = parseInt(document.getElementById('id_bill_material-INITIAL_FORMS').value);
    for (i = 0; i < initialFormCount; i++) {
        $('#id_bill_material-' + i + '-materialLabel').html($('#id_bill_material-' + i + '-material_select_field').val());
    }
}

function deleteCatMaterial(form_prefix) {
	var deleteFlag = document.getElementById('id_' + form_prefix + '-DELETE');
    var deleteRow = document.getElementById(form_prefix);
    $("button.confirm").removeClass('processing');
	swal({
	  title: "Are you sure?",
	  text: "Do you want to dissociate the Material from this Catalogue?",
	  type: "warning",
	  showCancelButton: true,
	  confirmButtonColor: "#209be1",
	  confirmButtonText: "Yes, delete it!",
	  closeOnConfirm: true
	},
	function(){
		deleteFlag.checked = true;
        deleteRow.style.display = 'none'
	});
}

function isDeleteMaterialAlternateUnit(current, form_prefix, alternate_unit_id) {
	if($(current).closest("tr").find(".td-alternate-unit-name").find("input").length > 0) {
        var itemName = $(current).closest("tr").find(".td-alternate-unit-name").find("input").val();
    }
    else {
        var itemName = $(current).closest("tr").find(".td-alternate-unit-name").text();
    }
	if (alternate_unit_id != "None"){
        $.ajax({
            url: "/erp/masters/json/materials/check_alternate_unit/",
            type: "POST",
            dataType: "json",
            data: {alternate_unit_id: alternate_unit_id },
            success: function (json) {
                if (json > 0) {
                    swal("Unable to Delete", `Alternate Unit <b>[${itemName}]</b> is already used in one or more items!`, "warning");
                }
                else {
                    deleteMaterialAlternateUnit(current, form_prefix, alternate_unit_id, itemName);
                }
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });
    }
    else {
        deleteMaterialAlternateUnit(current, form_prefix, alternate_unit_id, itemName);
    }
}

function deleteMaterialAlternateUnit(current, form_prefix, alternate_unit_id, itemName) {
    var deleteFlag = document.getElementById('id_' + form_prefix + '-DELETE');
    var deleteRow = document.getElementById(form_prefix);
    swal({
      title: "Are you sure?",
      text: `Do you want to delete <b>${itemName}</b>?`,
      type: "warning",
      showCancelButton: true,
      confirmButtonColor: "#209be1",
      confirmButtonText: "Yes, delete it!",
      closeOnConfirm: true
    },
    function(){
        deleteFlag.checked = true;
        deleteRow.style.display = 'none'
        updateAlternateUnitCount();
    });
}

function deleteSpecification(form_prefix) {
    var deleteFlag = document.getElementById('id_' + form_prefix +'-DELETE');
    var deleteRow = document.getElementById(form_prefix);
    $("button.confirm").removeClass('processing');
    swal({
        title: "Are you sure",
        text: "Do you want to dissociate the Specification from this Catalogue?",
	    type: "warning",
	    showCancelButton: true,
	    confirmButtonColor: "#209be1",
	    confirmButtonText: "Yes, delete it!",
	    closeOnConfirm: true
    },
        function(){
        deleteFlag.checked= true;
        deleteRow.style.display= 'none'
    });
}

function deleteSupplierPriceMaterial(form_prefix) {
    var confirmDelete = confirm('Do you want to delete supplier price?');
    var deleteFlag = document.getElementById('id_' + form_prefix + '-DELETE');
    var deleteRow = document.getElementById(form_prefix);
    if (confirmDelete == true) {
        deleteFlag.checked = true;
        deleteRow.hidden = 'hidden';
    }
}

function deleteCatalogue(catalogueCode) {
    var confirmDelete = confirm('Are you sure, you want to delete Catalogue - ' + catalogueCode + '?');
    if (confirmDelete == true)
        clickButton('deleteCatalogue_' + catalogueCode);
}

$(function () {
    $("#cmdUploadMaterial").bind("click", function () {
        if(!$('#fileUpload').val()) {
            $("button.confirm").removeClass('processing');
            swal({
				title:"File is required",
				text: "Please select CSV file. ",
				type: "warning",
				showCancelButton: false,
				confirmButtonColor: "#209be1",
				confirmButtonText: "OK",
				closeOnConfirm: true
			});
            return false;
        }
        m_type = $('#material_type').val();
        var regex = /^([a-zA-Z0-9\s_\\.\-:])+(.csv|.txt)$/;
        if (regex.test($("#fileUpload").val().toLowerCase())) {
            if (typeof (FileReader) != "undefined") {
                var reader = new FileReader();
                reader.onload = function (e) {
                    String.prototype.splitCSV = function(sep) {
                        for (var foo = this.split(sep = sep || ","), x = foo.length - 1, tl; x >= 0; x--) {
                            if (foo[x].replace(/"\s+$/, '"').charAt(foo[x].length - 1) == '"') {
                                if ((tl = foo[x].replace(/^\s+"/, '"')).length > 1 && tl.charAt(0) == '"') {
                                    foo[x] = foo[x].replace(/^\s*"|"\s*$/g, '').replace(/""/g, '"');
                                }
                                else if (x) {
                                    foo.splice(x - 1, 2, [foo[x - 1], foo[x]].join(sep));
                                }
                                else foo = foo.shift().split(sep).concat(foo);
                            }
                            else foo[x].replace(/""/g, '"');
                        }
                        return foo;
                    };
                    var mat_list = [];
                   if ($('#material_type').val() == 'goods'){
                    $.each(this.result.split('\n'), function(i, item){
                        var columns = item.splitCSV();
                        mat_list.push(JSON.stringify({
                            "drawing_no": columns[0],
                            "name": columns[1],
                            "category": columns[2],
                            "description": columns[3],
                            "hsn_sac": columns[4],
                            "price": columns[5],
                            "in_use": columns[6],
                            "unit": columns[7],
                            "faultless": columns[8],
                            "faulty": columns[9]
                        }));
                    });

                    }else{
                       $.each(this.result.split('\n'), function(i, item){
                           var columns = item.splitCSV();
                           mat_list.push(JSON.stringify({
                               "drawing_no": columns[0],
                               "name": columns[1],
                               "category": columns[2],
                               "description": columns[3],
                               "hsn_sac": columns[4],
                               "price": columns[5],
                               "in_use": columns[6],
                               "unit": columns[7],
                           }));
                       });
                    }

					$.ajax({
						url : "/erp/masters/json/materials/import/",
						type : "POST",
						dataType: "json",
						data : {'mat_list[]': mat_list, 'm_type': m_type},
						success : function(json) {
							if(json['is_valid_file_headers'] == false) {
                                 swal({
                                    title:"Material Import Status",
                                    text: json['message'],
                                    type: "warning",
                                    showCancelButton: false,
                                    confirmButtonColor: "#209be1",
                                    confirmButtonText: "OK",
                                    closeOnConfirm: true
                                },
                                function(){
                                    if(m_type=='goods'){
                                        window.location.href = "/erp/masters/catalogues/";
                                    }else{
                                        window.location.href = "/erp/masters/services/";
                                    }
                                });

                            }
							else {
							    if(json['message'].indexOf("1062") != -1) {
								    alert("Material already exists");
                                }
                                if (json['message'].match(/Item codes already profiled/i)) {
                                    $("button.confirm").removeClass('processing');
                                    swal({
                                        title: "Are you sure?",
                                        text: 'Existing/Duplicate material name is found in the <br />uploaded file. <br />Do you want to update them?',
                                        type: "warning",
                                        showCancelButton: true,
                                        confirmButtonColor: "#DD6B55",
                                        confirmButtonText: "Yes, update it!",
                                        closeOnConfirm: false
                                    }, function (isConfirm) {
                                        if (!isConfirm) return;
                                        $("button.confirm").addClass('processing').html('<span class="savingDots">Processing<span>.</span><span>.</span><span>.</span></span>');
                                        $.ajax({
                                            url: "/erp/masters/json/materials/import/",
                                            type: "POST",
                                            data : {'mat_list[]': mat_list,'is_user_update_accept': 1,'m_type': m_type},
                                            dataType: "json",
                                            success: function (json) {
                                                if (json['message'].match(/New categories found/g)){
                                                    $("button.confirm").removeClass('processing');
                                                    swal({
                                                        title: "Do you want to add the following mismatched categories?",
                                                        text: json["new_categories"],
                                                        type: "warning",
                                                        showCancelButton: true,
                                                        confirmButtonColor: "#DD6B55",
                                                        confirmButtonText: "Yes, import it!",
                                                        closeOnConfirm: false
                                                    }, function (isConfirm) {
                                                        if (!isConfirm) return;
                                                        $("button.confirm").addClass('processing').html('<span class="savingDots">Processing<span>.</span><span>.</span><span>.</span></span>');
                                                        $.ajax({
                                                            url: "/erp/masters/json/materials/import/",
                                                            type: "POST",
                                                            data : {'mat_list[]': mat_list,'is_user_accept': 1,'is_user_update_accept': 1,'m_type': m_type},
                                                            dataType: "json",
                                                            success: function (json) {
                                                                setTimeout(function() {
                                                                    $("button.confirm").removeClass('processing');
                                                                    if(json['failed_items'].length==0) {
                                                                        $("#importmaterial").modal("hide");
                                                                        swal({
                                                                            title: "Import details!",
                                                                            text: json['message'],
                                                                            type: "success"
                                                                        }, function() {
                                                                            if(m_type=='goods'){
                                                                                window.location.href = "/erp/masters/catalogues/";
                                                                            }else{
                                                                                window.location.href = "/erp/masters/services/";
                                                                            }
                                                                        });
                                                                        return false;
                                                                    }
                                                                    else {
                                                                        swal.close();
                                                                        $("button.confirm").removeClass('processing');
                                                                        $("#importmaterial").modal("hide");
                                                                        $("#import_message").html(json['message'])
                                                                        $("#importmaterial_status_modal").modal("show");
                                                                        $("#failed_import_table").find("tr:gt(0)").remove();
                                                                        $.each(json['failed_items'], function(i, item) {
                                                                            var row = "<tr bgcolor='#ececec' border='0' align='center' style='font-size:11px; font-weight:normal;'>"+
                                                                            "<td>"+item[0]+ "</td><td>"+item[1]+ "</td><td align='left'>" +
                                                                            item[2]  + "</td><td align='left'>" +
                                                                            item[3]  + "</td><td align='left'>" +
                                                                            item[4]  + "</td><td align='left'>" +
                                                                            item[5]  + "</td><td align='left'>" +
                                                                            item[6]  + "</td><td align='right'>" +
                                                                            item[7]  + "</td><td align='left'>" +
                                                                            item[8]  + "</td><td align='left'>" +
                                                                            item[9]  + "</td><td align='left'>" +
                                                                            item[10]  + "</td></tr>"
                                                                            $('#failed_import_table').append(row).addClass('tbl');
                                                                        });
                                                                    }
                                                                }, 1000);
                                                            },
                                                            error: function (xhr, ajaxOptions, thrownError) {
                                                                $("button.confirm").removeClass('processing');
                                                                swal("Error in Importing!", "Please try again", "error");
                                                            }
                                                        });
                                                    });
                                                    return false;
                                                }
                                                else {
                                                    setTimeout(function() {
                                                        $("button.confirm").removeClass('processing');
                                                        if(json['failed_items'].length==0) {
                                                            swal({
                                                                title: "Import details!",
                                                                text: json['message'],
                                                                type: "success"
                                                            }, function() {
                                                                if(m_type=='goods'){
                                                                    window.location.href = "/erp/masters/catalogues/";
                                                                }else{
                                                                    window.location.href = "/erp/masters/services/";
                                                                }
                                                            });
                                                            return false;
                                                        }
                                                        else {
                                                            swal.close();
                                                            $("#importmaterial").modal("hide");
                                                            $("#import_message").html(json['message'])
                                                            $("#importmaterial_status_modal").modal("show");
                                                            $("#failed_import_table").find("tr:gt(0)").remove();
                                                            $.each(json['failed_items'], function(i, item) {
                                                                var row = "<tr bgcolor='#ececec' border='0' align='center' style='font-size:11px; font-weight:normal;'>"+
                                                                "<td>"+item[0]+ "</td><td>"+item[1]+ "</td><td align='left'>" +
                                                                item[2]  + "</td><td align='left'>" +
                                                                item[3]  + "</td><td align='left'>" +
                                                                item[4]  + "</td><td align='left'>" +
                                                                item[5]  + "</td><td align='left'>" +
                                                                item[6]  + "</td><td align='right'>" +
                                                                item[7]  + "</td><td align='left'>" +
                                                                item[8]  + "</td><td align='left'>" +
                                                                item[9]  + "</td><td align='left'>" +
                                                                item[10]  + "</td></tr>"
                                                                $('#failed_import_table').append(row).addClass('tbl');
                                                            });
                                                        }
                                                    }, 1000);
                                                }
                                            },
                                            error: function (xhr, ajaxOptions, thrownError) {
                                                $("button.confirm").removeClass('processing');
                                                swal("Error in Importing!", "Please try again", "error");
                                            }
                                        });
                                    });
                                    return false;
                                }
                                if (json['message'].match(/New categories found/g)) {
                                    $("button.confirm").removeClass('processing');
                                    swal({
                                        title: "Do you want to add the following mismatched categories?",
                                        text: json["new_categories"],
                                        type: "warning",
                                        showCancelButton: true,
                                        confirmButtonColor: "#DD6B55",
                                        confirmButtonText: "Yes, import it!",
                                        closeOnConfirm: false
                                    }, function (isConfirm) {
                                        if (!isConfirm) return;
                                        $("button.confirm").addClass('processing').html('<span class="savingDots">Processing<span>.</span><span>.</span><span>.</span></span>');
                                        $.ajax({
                                            url: "/erp/masters/json/materials/import/",
                                            type: "POST",
                                            data : {'mat_list[]': mat_list,'is_user_accept': 1,'m_type': m_type},
                                            dataType: "json",
                                            success: function (json) {
                                                setTimeout(function() {
                                                    $("button.confirm").removeClass('processing');
                                                    if(json['failed_items'].length==0) {
                                                        $("#importmaterial").modal("hide");
                                                        swal({
                                                            title: "Import details!",
                                                            text: json['message'],
                                                            type: "success"
                                                        }, function() {
                                                            if(m_type=='goods'){
                                                                window.location.href = "/erp/masters/catalogues/";
                                                            }else{
                                                                window.location.href = "/erp/masters/services/";
                                                            }
                                                        });
                                                    }
                                                    else {
                                                        swal.close()
                                                        $("#importmaterial").modal("hide");
                                                        $("#import_message").html(json['message'])
                                                        $("#importmaterial_status_modal").modal("show");
                                                        $("#failed_import_table").find("tr:gt(0)").remove();
                                                        $.each(json['failed_items'], function(i, item) {
                                                            var row = "<tr bgcolor='#ececec' border='0' align='center' style='font-size:11px; font-weight:normal;'>"+
                                                            "<td>"+item[0]+ "</td><td>"+item[1]+ "</td><td align='left'>" +
                                                            item[2]  + "</td><td align='left'>" +
                                                            item[3]  + "</td><td align='left'>" +
                                                            item[4]  + "</td><td align='left'>" +
                                                            item[5]  + "</td><td align='left'>" +
                                                            item[6]  + "</td><td align='right'>" +
                                                            item[7]  + "</td><td align='left'>" +
                                                            item[8]  + "</td><td align='left'>" +
                                                            item[9]  + "</td><td align='left'>" +
                                                            item[10]  + "</td></tr>"
                                                            $('#failed_import_table').append(row).addClass('tbl');
                                                        });
                                                    }
                                                }, 1000);
                                                ga('send', 'event', 'Material', 'Import', $('#enterprise_label').val(), mat_list.length-json.failed_items.length);
                                            },
                                            error: function (xhr, ajaxOptions, thrownError) {
                                                $("button.confirm").removeClass('processing');
                                                swal("Error in Importing!", "Please try again", "error");
                                            }
                                        });
                                    });
                                    return false;
                                }
							    else {
                                    setTimeout(function() {
                                        $("button.confirm").removeClass('processing');
                                        if(json['failed_items'].length==0) {
                                            swal({
                                                title: "Import details!",
                                                text: json['message'],
                                                type: "success"
                                            }, function() {
                                                if(m_type=='goods'){
                                                    window.location.href = "/erp/masters/catalogues/";
                                                }else{
                                                    window.location.href = "/erp/masters/services/";
                                                }
                                            });
                                        }
                                        else {
                                            $("#importmaterial").modal("hide");
                                            $("#import_message").html(json['message'])
                                            $("#importmaterial_status_modal").modal("show");
                                            $("#failed_import_table").find("tr:gt(0)").remove();
                                            $.each(json['failed_items'], function(i, item) {
                                                var row = "<tr bgcolor='#ececec' border='0' align='center' style='font-size:11px; font-weight:normal;'>"+
                                                "<td>"+item[0]+ "</td><td>"+item[1]+ "</td><td align='left'>" +
                                                item[2]  + "</td><td align='left'>" +
                                                item[3]  + "</td><td align='left'>" +
                                                item[4]  + "</td><td align='left'>" +
                                                item[5]  + "</td><td align='left'>" +
                                                item[6]  + "</td><td align='right'>" +
                                                item[7]  + "</td><td align='left'>" +
                                                item[8]  + "</td><td align='left'>" +
                                                item[9]  + "</td><td align='left'>" +
                                                item[10]  + "</td></tr>"
                                                $('#failed_import_table').append(row).addClass('tbl');
                                            });
                                        }
                                    },1000);
                                }
                            }
						},
					    error : function(xhr,errmsg,err) {
						    console.log(xhr.status + ": " + xhr.responseText);
						}
					});
                }
                reader.readAsText($("#fileUpload")[0].files[0]);
            } else {
                alert("This browser does not support HTML5.");
            }
        } else {
            alert("Please upload a valid CSV file.");
        }
    });


    $("form#files").submit(function(){
        if (!$("#materialtable_bom tr td b.service_package_profile").length > 0 && $('#materialtable_bom tbody tr').length >= 2 && ($('#materialtable_bom tbody tr:not([style*="display: none"])').length >=2)){
            swal({
                           title: "",
                           text: "Existing bill of materials will be replaced by the materials from the document!. <br />Do you still want to continue?",
                           type: "warning",
                           showCancelButton: true,
                           confirmButtonColor: "#209be1",
                           confirmButtonText: "Yes, do it!",
                           closeOnConfirm: true,
                           closeOnCancel: true
                       },
                       function(){
                            AddBOMWithConfirmationMsg();
                       }
            );
        }
        else {
            AddBOMWithConfirmationMsg();
        }
    });
});
function AddBOMWithConfirmationMsg(){
            var regex = /^([a-zA-Z0-9\s_\\.\-:])+(.csv|.xlsx)$/;
            if (regex.test($("#fileUploadbom").val().toLowerCase())) {
                $("#xlhideUploadButton").text('Processing...').addClass('btn-processing');
                var formData = new FormData($('form#files')[0]);
                setTimeout(function(){
                    $.ajax({
                        url:"/erp/masters/json/materials/import_bill_of_materials/",
                        type: 'POST',
                        data: formData,
                        async: false,
                        success: function (data) {
                            if (data.match(/Columns are mismatched please enter proper column name/i)) {
                                    $('form#files')[0].reset(); // this will reset the form fields
                                    alert(data);
                                }
                            if (data.match(/Empty data at your file/i)) {
                                    $('form#files')[0].reset(); // this will reset the form fields
                                    alert(data);
                                    $('#importBom').modal('hide');
                                }
                            if (data.match(/Duplicate entries found for the following material/i)) {
                                    $('form#files')[0].reset(); // this will reset the form fields
                                    alert(data);
                                }
                            if (data.match(/Material name does not exist for the given Material name/i)) {
                                    $('form#files')[0].reset(); // this will reset the form fields
                                    alert(data);
                                }
                            if (data.match(/Drawing number does not exist for the given Drawing number/i)) {
                                    $('form#files')[0].reset(); // this will reset the form fields
                                    alert(data);
                                 }
                            if (data.match(/Item code does not exist for the given Item code/i)) {
                                    $('form#files')[0].reset(); // this will reset the form fields
                                    alert(data);
                                 }
                            if (data.match(/Import Proper csv or xlsx file instead you uploaded/i)) {
                                    $('form#files')[0].reset(); // this will reset the form fields
                                    alert(data);
                                }
                            if (data.match(/Oops exception occurred due to/i)) {
                                $('form#files')[0].reset(); // this will reset the form fields
                                alert(data);
                            }
                            if (data.match(/"material_selected"/i)) {
                                is_import=true
                                $('#importBom').modal('hide');
                                $("#materialtable_bom tr:gt(1)").remove();
                                $('#id_bill_material-TOTAL_FORMS').val(0);
                                data = JSON.parse(data)
                                if (data['not_in_use_materials'].length > 0) {
                                     alert("Following materials which are in NOT IN USE state  also got imported "+data['not_in_use_materials']);
                                }
                                import_catalogue_material(data['validated_objects']);
                                is_import=false
                            }
                            $("#xlhideUploadButton").text('Upload').removeClass('btn-processing');
                        },
                        error : function(xhr,errmsg,err) {
                            console.log(xhr.status + ": " + xhr.responseText);
                        },
                        cache: false,
                        contentType: false,
                        processData: false
                    });
                },10);
            }
            else {
               alert('Please upload the  xslx  or CSV file');
               return false;
            }
}
$("#cmdCatSave").bind("click", function () {
    if ($("#catName").val()!="") {
        $.ajax({
            url: "/erp/masters/json/materials/categorysave/",
            type: "POST",
            dataType: "json",
            data:{"name":$("#catName").val(), "is_service": $("#id_material-is_service").is(":checked")},
            success: function (json) {
                if (json["message"].indexOf("1062") != -1) {
                    $("button.confirm").removeClass('processing');
                    swal("","Category already exists","warning");
                    $("#catName").val("").focus();
                    removeValidationError();
                }
                else {
                    $("button.confirm").removeClass('processing');
                    swal("",json["message"],"");
                    $("#catName").val("");
                    $("#materialcategory").modal('hide');
                    populateCategoryOptions(json["new_category_id"]);
                    $("#id_material-category_id").val(json["new_category_id"]);
                }
                $("#id_material-category_select").focus();
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });
    }
});

function populateCategoryOptions(selected_category_id){
    $.ajax({
        url: "/erp/masters/json/materials/populate_categories/",
        type: "POST",
        dataType: "json",
        data: {"category_id": selected_category_id, "is_service": $("#material_type").val()},
        success: function(category_options) {
            option_html = "";
            for(i=0; i < category_options.length; i++){
                option_html += "<option value=\"" + category_options[i]["category_id"] + "\"" +
                                category_options[i]["attrs"] + ">" +
                                category_options[i]["label"] + "</option>";
            }
            $("#id_material-category_select").html(option_html).chosen().trigger("chosen:updated");
        }
    });
}
var oTable;
var oSettings;
function populateMaterials(is_service=0){
    $('#loading').show();
    var material_id=""
    var price_status=""
    var price_profile=($("#id_price_profiled").is(":checked"))?1:0;
    var in_use_items=($("#id_in_use_items").is(":checked"))?1:0;
    var not_in_use_items=($("#id_not_in_use_items").is(":checked"))?1:0;
    var all_items=($("#id_all_items").is(":checked"))?1:0;
    var non_stock_in_use_items=($("#id_non_stock_in_use_items").is(":checked"))?1:0;
    var non_stock_not_in_use_items=($("#id_non_stock_not_in_use_items").is(":checked"))?1:0;
    var non_stock_all_items=($("#id_non_stock_all_items").is(":checked"))?1:0;

    var data = [];
    var url=window.location.href;
    var filter = 0;
    if(url.indexOf("pending") > 0 ){
	   filter = 1
	   price_profile = 1
	   non_stock = 1
    }
    if(price_profile) {
        $("#searchResult").removeClass("table-non-price-profiled")
    }
    else {
        $("#searchResult").addClass("table-non-price-profiled")
    }

    if ($("#material_type").val() == "service"){
        is_service = 1
    }
    else {
        is_service = 0
    }
    $.ajax({
        url: "/erp/masters/json/materials/populate_materials/",
        type: "POST",
        dataType: "json",
        data: {
            "material_id": material_id,
            "filter": filter,
            "price_profile": price_profile,
            "is_service": is_service,
            "in_use_items": in_use_items,
            "not_in_use_items": not_in_use_items,
            "all_items":all_items,
            "non_stock_in_use_items": non_stock_in_use_items,
            "non_stock_not_in_use_items": non_stock_not_in_use_items,
            "non_stock_all_items":non_stock_all_items
        },
        success: function(response) {
            if(response['response_message'] == "Session Timeout") {
                location.reload();
                return;
            }
			var csrf_token = `<input type="hidden" name="csrfmiddlewaretoken" value="${getCookie('csrftoken')}">`;
            $.each(response, function (i, item) {
                price_status = item.price_approvals_pending + " - Pending"
                if (item.price_approvals_returned > 0){
                    price_status = price_status + " " + item.price_approvals_returned + " - Reviewed"
                }
                var status = "";
                var edit = "";
                var modDate = "";
                if (item.in_use==1) {
                    status = "In-Use"
                    if($("#is_access_level_edit").val().trim() == 1) {
                        edit = `<input checked='checked' type='checkbox' class='multi_check styled-checkbox' onchange='javascript:deleteMaterial("${item.item_id}");' id='inuse_${item.item_id}' data_material_name = '${item.name}'>
                                    <label for ='inuse_${item.item_id}'></label>
                                    <input type='hidden' value="${item.item_id}" id='id_delete_${item.item_id}' name='delete_item_id' />
                                    <input type='submit' value='Delete' hidden='hidden' id='deleteMaterial_${item.item_id}'>
                                    <input type='hidden' value="${item.is_stocked}" id='is_stocked_${item.item_id}'>`;
                    }
                    else {
                        edit = `<input checked='checked' class='multi_check styled-checkbox' type='checkbox' disabled='disabled' id='inuse_${item.item_id}'>
                                    <label for ='inuse_${item.item_id}'>
                                </label>`;
                    }
                }
                else {
                    status = "Not-In-Use"
                    edit = "-- NO --"
                }
                var attachmentIcon = "";
                var part_number = "";
                var Available = item.available ? parseFloat(item.available): 0.00;
                var allocated = item.allocated ? parseFloat(item.allocated) : 0.00;
                var in_stock = parseFloat(Available + allocated);
                var part_number = item.part_number == '[]' ? '' : item.part_number
                if($("#is_access_level_edit").val().trim() == 1) {
                    editAttachmentIcon = `  <span style='width: 100px; display: inline-block; float: right;'>
                                                <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='bottom' title='Edit'
                                                onclick="editMaterialRow('${item.item_id}', '${item.is_service}', '_blank')">
                                                    <i class='fa fa-pencil'></i>
                                                </span>
                                                <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='bottom' title='Edit in New Tab' onclick="editMaterialRow('${item.item_id}', '${item.is_service}', '_blank')" >
                                                    <i class='fa fa-external-link'></i>
                                                </span>
                                                <span class='table-inline-icon hide' data-tooltip='tooltip' title='Attachment' onclick="inlineAttachmentUpload('${item.name}','${item.item_id}')">
                                                    <i class='fa fa-paperclip' aria-hidden='true'></i>
                                                </span>
                                            </span>    `;
                }
                else {
                    editAttachmentIcon = `  <span style='width: 100px; display: inline-block; float: right;'>
                                                 <span class='table-inline-icon hide' data-tooltip='tooltip' title='Attachment' onclick="inlineAttachmentUpload('${item.name}','${item.item_id}')">
                                                     <i class='fa fa-paperclip' aria-hidden='true'></i>
                                                 </span>
                                             </span>    `;
                }
                var edit_name = `<a class='edit_link_code' style='display: inline-block;' onclick="editMaterialRow('${item.item_id}', '${item.is_service}', '_blank')">
                                    ${item.name}
                                </a>
                                ${editAttachmentIcon}`;
                if(item.last_modified_on != "" && item.last_modified_on != null){
                    var modDate = moment(item.last_modified_on).format('MMM D, YYYY, HH:mm ');
                }
                data.push( [ i+1, item.drawing_no, edit_name,item.uom,part_number, item.category,in_stock.toFixed(2),allocated.toFixed(2),Available.toFixed(2),item.price.toFixed(2),item.num_suppliers, price_status, modDate, edit, status] );
            });
            if ($("#searchResult tr").length == 1){
                var row = "";
                $('#searchResult').append(row).addClass('tbl');
            }

            var datesorter = [
                    null,null,null,null,null,null,null,null,null,null,null,null,
                    { "type": "date" },
                    null,null
                ]
            var lastColumnCount = 14;
            var materialType = $("#material_type").val();
            var materialTypeClass = materialType == 'goods' ? '' : 'hide';
            if(oTable != undefined) {
                 oTable.destroy();
            }
            oTable = $('#searchResult').DataTable( {
                data: data,
                deferRender:    true,
                fixedHeader: false,
                "scrollY": Number($(document).height() - 230),
                "scrollX": true,
                "pageLength": 50,
                "search": {
                    "smart": false
                },
                "columnDefs": [{
                "targets": lastColumnCount,
                    "orderable": false
                },
                { className: "td-material-profile", "targets": [ 11,10 ] },
                { className: materialTypeClass, "targets" :[4,6,7,8] }],
                "columns": datesorter,
                "createdRow": function ( row, data, index ) {
                    $('td', row).eq(lastColumnCount).addClass('exclude_export');
                }
            });
            oTable.on("draw",function() {
                var keyword = $('#searchResult_filter > label:eq(0) > input').val();
                $('#searchResult').unmark();
                $('#searchResult').mark(keyword,{});
                listTableHoverIconsInit("searchResult");
                setHeightForTable();
            });
            oTable.on('page.dt', function() {
                $('html, body').animate({
                    scrollTop: $(".dataTables_wrapper").offset().top - 15
                }, 'slow');
                listTableHoverIconsInit("searchResult");
            });
            oSettings = oTable.settings();
            listTableHoverIconsInit("searchResult");
            updateFilterText();
            $(".page-heading_new").click();
            $( window ).resize();
            closeFilterOption();
            $('#loading').hide();
        }
    });

    setTimeout(function(){
        $("#filter_textbox").on("focus", function() {
            if(oTable != undefined && $("#pagination-select").val() == -1) {
                $("#pagination-select").val(50).trigger('change');
                $("#pagination-select").after("<p class='pagination-warning-for-all'>For search speed, paging entries changed to 50. But your search result will show all your filtered data through pagination.</p>");
                setTimeout(function(){
                    $(".pagination-warning-for-all").fadeOut(1000);
                },10000)
            } 
        });
    },1000);
}

function editMaterialRow(materialId, is_service, openTarget="") {
    $("#edit_item_id").val(materialId);
    $("#edit_material_type").val(is_service)
    $("#material_edit_form").attr("target", openTarget).submit();
}

function deleteMaterial(itemId){
    var inUseItemId = $('input[id="inuse_'+itemId+'"]');
    var isStocked = $('input[id="is_stocked_'+itemId+'"]');
    var materialName = inUseItemId.attr('data_material_name');
    if (isStocked.val()=='true'){
        if(!inUseItemId.is(":checked")) {
            $.ajax({
                url: "/erp/masters/json/materials/checkstock/",
                type: "POST",
                dataType: "json",
                data: {item_id:itemId},
                success: function (json) {
                    if (json['stock_qty'] > 0 || json['p_bom_count'] > 0) {
                        if (json['stock_qty'] > 0){
                            swal_text = "Cannot delete Item: <span style='color:#209be1'>" + materialName + "</span>, as it is still available in stock!"
                        }else if (json['p_bom_count'] > 0){
                            swal_text = "Cannot be marked as Not-In-Use: <span style='color:#209be1'>" + materialName + "</span> is part of BoM.!"
                        }
                        $("button.confirm").removeClass('processing');
                        inUseItemId.prop("checked", true);
                        swal({
                            title: "",
                            text: swal_text,
                            type: "warning",
                            showCancelButton: false,
                            confirmButtonColor: "#209be1",
                            confirmButtonText: "OK",
                            closeOnConfirm: true
                        });
                        return;
                    }
                    else {
                        deleteMaterialSubmit(inUseItemId, materialName);
                    }
                },
                error: function (xhr, errmsg, err) {
                    console.log(xhr.status + ": " + xhr.responseText);
                }
            });
        }
    } else{
            deleteMaterialSubmit(inUseItemId, materialName);
    }
}

function deleteMaterialSubmit(inUseItemId, materialName){

    $("button.confirm").removeClass('processing');
    inUseItemId.prop("checked", true);
    swal({
      title: "Are you sure?",
      text: "Do you want to delete Material: <span style='color:#209be1'>" + materialName +"</span>?",
      type: "warning",
      showCancelButton: true,
      confirmButtonColor: "#209be1",
      confirmButtonText: "Yes, delete it!",
      closeOnConfirm: true
    },
    function(){
        var form_element = inUseItemId.closest('td').html();
        var url = '/erp/masters/materials/delete/';
        var csrf_token_val =  $('input[name=csrfmiddlewaretoken]').val();
        var csrf_token = '<input type="hidden" name="csrfmiddlewaretoken" value="'+csrf_token_val+'">';
        form_element += csrf_token;
        var form = $('<form action="' + url + '" method="post">'+ form_element +'</form>');
        inUseItemId.closest('td').html(form);
        form.submit();
    });
}

function showImportMaterial() {
    $("#importmaterial").modal('show');
}

function showImportBom() {
    $("#importBom").modal('show');
}

function showCheapestSupplierBom(){
    getCheapestMaterialListBOM();
    $("#cheapest_supplier_bom").find(".home-currency").text($("#home_currency_id").val())
    $("#supplierPriceBom").modal('show');
}

function AddMaterial() {
	$("#addMaterial").click();
}

function hideImportMaterial() {
    $("#importmaterial").modal('hide');
}

function showMaterialCategory() {
	$("#materialcategory").modal('show');
}

function showdoc(id){
    var doc_element;
    var expand_img;
    if (id=="1"){
        doc_element = document.getElementById('bill_of_material');
        expand_img = document.getElementById('bill_of_material_expand');
    }else if (id=="2"){
        doc_element = document.getElementById('material_sup_profile');
        expand_img = document.getElementById('material_sup_profile_expand');
    }
    if(doc_element.style.display=="none"){
        doc_element.style.display='block';
        expand_img.src = "/site_media/images/minus.jpg"
    }
    else{
        doc_element.style.display='none';
        expand_img.src = "/site_media/images/plus.jpg"
    }
}

var supplier_choices = [];
$(function () {
    var material_list = [];
    $.ajax({
        url: "/erp/masters/json/materials/populate_choices/",
        type: "post",
        datatype: "json",
        data: null,
        success: function (response) {
            for (i = 0; i <= response.length - 1; i++) {
                label = response[i]['name']
                if (response[i]['drawing_no'] !="" && response[i]['drawing_no'] != null){
                    label += " - " + response[i]['drawing_no']
                }
                if (response[i]['make_name'] !="" && response[i]['make_name'] != null){
                    label += " - [" + response[i]['make_name'] + "]"
                }
                if(response[i]['is_service'] == 1) {
                 label += "<span class='service-item-flag'></span>";
                }
                material_list.push({value: response[i]['drawing_no'],
                                    label: label,
                                    unit: "" + response[i]['unit_name'] + "", make: "" + response[i]['id'] + "", is_service: "" + response[i]['is_service'] + "",
                                    item_id: "" + response[i]['id'] + ""});

            }
        }
    });

    $("#material_selected").autocomplete({
        source: material_list,
        focus: function (event, ui) {
            event.preventDefault();
        },
        select: function (event, ui) {
            event.preventDefault();
            var item_name = ui.item.label.replace("<span class='service-item-flag'></span>", '');
            $("#drawing_no_selected").val(ui.item.value);
            $("#material_selected").val(item_name);
            $("#unit_display").html(ui.item.unit);
            $("#id_bill_material-__prefix__-material_select_field").val(item_name);
            $("#id_bill_material-__prefix__-drawing_no").val(ui.item.value);
            $("#id_bill_material-__prefix__-is_service").val(ui.item.is_service);
            $("#id_bill_material-__prefix__-item_id").val(ui.item.item_id);
            $("#id_bill_material-__prefix__-units").val(ui.item.unit);
        }
    });


     $.widget( "custom.catcomplete", $.ui.autocomplete, {
      _create: function() {
        this._super();
        this.widget().menu( "option", "items", "> :not(.ui-autocomplete-category)" );
        },
      _renderMenu: function( ul, items ) {
        var that = this,
          currentCategory = "";
        $.each( items, function( index, item ) {
          var li;
          if ( index==0 ) {
            ul.append( "<b><a  href=''>+ ADD NEW SUPPLIER</a><b>" );

          }

          if ( item.category != currentCategory ) {
            ul.append( "<b><li class='ui-autocomplete-category'>" + item.category + "</li></b>" );
            currentCategory = item.category;
          }
          li = that._renderItemData( ul, item );
          if ( item.category ) {
            li.attr( "aria-label", item.category + " : " + item.label );
          }
        });
      }
    });
    $.ajax({
        url: "/erp/masters/json/party/populate_party_lists/",
        type: "post",
        datatype: "json",
        data: null,
        success: function (response) {
			frequently_used_count=response[response.length-1]
            for (i = 1; i <= response.length - 1; i++) {
                supplier_choices.push({value: response[i][0],category: "All suppliers",label: "" + response[i][1] + ""});
            }
        },
        error: function (xhr, errmsg, err) {
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });
    $("#supplier_selected").catcomplete({
        source: supplier_choices,
        minLength:0,
        select: function (event, ui) {
            event.preventDefault();
            if(ui.item != undefined){
            $("#supplier_no_selected").val(ui.item.value);
            $("#supplier_selected").val(ui.item.label);
            $("#id_price_material-__prefix__-supplier_select_field").val(ui.item.label);
            $("#id_price_material-__prefix__-supp_id").val(ui.item.value);
            $(".supplier-removal-icon").removeClass("hide");
            ChangePartyCurrency(ui.item.value,"id_price_material-__prefix__-currency_id");
            }
            else{
                $('#modalPartyDetails').modal('show');
            }
        }
    }).on('focus', function() { $(this).keydown(); });


});
 function setParty(supplier_id,supplier_name) {
    supplier_choices.push({value: supplier_id,category: "All suppliers",label: "" + supplier_name + ""});
}




function reassemblePriceProfile() {
    var currentRow = 0;
    $("#supp_price_table").find("tr[data-supplier-id]").each(function(){
        if(currentRow != $(this).attr("data-supplier-id")) {
            var row = ` <tr style="background: rgba(32,155,255,0.15) !important;">
                            <td colspan='9' style="color: #444;font-size: 106%; text-shadow: 0 0 #444;letter-spacing: 0.5px;">${$(this).find(".td-supplier-name").text()}
                        </tr>`;
            $(row).insertBefore($(this));
            currentRow = $(this).attr("data-supplier-id");
        }
    })
}
function convertFieldsToCardUI(form_idx){
        $(".empty-specification").addClass("hide");
        var current = $("#specification_table").find(`#specification-${form_idx}`);
        current.find(".material_specification_container_label").html('');
        current.find(".material_specification_container_delete").html('');
        var isQcChecked = "";
        if(current.find("input[id*='qc_critical']").is(":checked")) {
            isQcChecked = "checked"
        }
        var quantityRange = "";
        if(current.find("input[id*='min_value']").val() == current.find("input[id*='max_value']").val()) {
            quantityRange = current.find("input[id*='min_value']").val();
        }
        else if(current.find("input[id*='min_value']").val() != "" && current.find("input[id*='max_value']").val() != "") {
            quantityRange = current.find("input[id*='min_value']").val() +" - "+ current.find("input[id*='max_value']").val();
        }
        else if(current.find("input[id*='min_value']").val() == "" && current.find("input[id*='max_value']").val() != "") {
            quantityRange = " <= "+ current.find("input[id*='max_value']").val();
        }
        else if(current.find("input[id*='min_value']").val() != "" && current.find("input[id*='max_value']").val() == "") {
            quantityRange = " >= "+ current.find("input[id*='min_value']").val();
        }
        var row = ` <span class="material_specification_parameter">${current.find("input[id*='parameter']").val()}</span>
                    <span class="material_specification_minMaxValue">${quantityRange}</span>
                    <span class="material_specification_unit">${current.find("input[id*='unit']").val()}</span>
                    <span class="material_specification_qc ${isQcChecked} pull-right"></span>
                    <span class="material_specification_inspectionMethod">${current.find("input[id*='inspection_method']").val()}</span>
                    <span class="material_specification_reactionPlan">${current.find("input[id*='reaction_plan']").val()}</span>
                    <span class="material_specification_remarks">${current.find("input[id*='comments']").val()}</span>`;
                    //console.log(row)
        current.find(".material_specification_container_label").append(row);
        var row = `<span class="material_specification_delete" role='button'>
                        <a onclick="javascript:deleteSpecification('specification-${form_idx}')">
                            <i class="fa fa-times"></i>
                        </a>
                    </span>`;
        current.find(".material_specification_container_delete").append(row);
    }

function editSpecification(currentRow) {
    removeValidationError();
    $("#edit_specification_modal").modal("show");
    $("#edit_specification_modal").find(".modal-title").text("Edit Specification");
    $("#add_new_specification").addClass("hide");
    $("#update_specification").removeClass("hide");
    var current = $(currentRow).closest("tr");
    var isQcChecked = "";
    if(current.find("input[id*='qc_critical']").is(":checked")) {
        $("#id_specification-__prefix__-qc_critical").prop("checked", true);
    }
    else {
        $("#id_specification-__prefix__-qc_critical").prop("checked", false);
    }
    $("#id_specification-__prefix__-parameter").val(current.find("input[id*='parameter']").val());
    $("#id_specification-__prefix__-min_value").val(current.find("input[id*='min_value']").val());
    $("#id_specification-__prefix__-max_value").val(current.find("input[id*='max_value']").val());
    $("#id_specification-__prefix__-unit").val(current.find("input[id*='unit']").val());
    $("#id_specification-__prefix__-inspection_method").val(current.find("input[id*='inspection_method']").val());
    $("#id_specification-__prefix__-reaction_plan").val(current.find("input[id*='reaction_plan']").val());
    $("#id_specification-__prefix__-comments").val(current.find("input[id*='comments']").val());
    $("#update_specification").attr("data-edited-specification", current.attr("id"));
}

function addSpecification(current, type){
    $(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();
    var ControlCollections = [
        {
            controltype: 'textbox',
            controlid: 'id_specification-__prefix__-parameter',
            isrequired: true,
            errormsg: 'Paramenter is required.'
        }
    ];
    var result = JSCustomValidator.JSvalidate(ControlCollections);
    if(result) {
        var currentParameterID = $(current).attr('data-edited-specification');
        var minValue = ($("#id_specification-__prefix__-min_value").val() == "") ? 0 : $("#id_specification-__prefix__-min_value").val();
        var maxValue = ($("#id_specification-__prefix__-max_value").val() == "") ? Infinity : $("#id_specification-__prefix__-max_value").val();
        if(Number(minValue) > Number(maxValue)){
            swal("",`Maximum value should be greater than Minimum value`,"warning");
            result = false;
            return false;
        }
        var matchCount = 0;
        if(currentParameterID) {
            $("#specification_table").find("tr:visible").not(`#${currentParameterID}`).each(function(){
                if($("#id_specification-__prefix__-parameter").val().trim().toLowerCase() == $(this).find(".material_specification_parameter").text().trim().toLowerCase()) {
                    matchCount++;
                }
            });
        }
        else {
            $("#specification_table").find("tr:visible").each(function(){
                if($("#id_specification-__prefix__-parameter").val().trim().toLowerCase() == $(this).find(".material_specification_parameter").text().trim().toLowerCase()) {
                    matchCount++;
                }
            });
        }

        if(matchCount > 0) {
            swal("",`Specification <b>'${$("#id_specification-__prefix__-parameter").val()}'</b> already exists for this Material.`,"warning");
            result = false;
        }
        if(result) {
            if(type == 'add') {
                var form_idx = parseInt($('#id_specification-TOTAL_FORMS').val());
                new_form = $('#specification-__dummy__').html().replace(/__prefix__/g, form_idx);
                new_form_html = `<tr id="specification-${form_idx}" class='newly_added_item'>${new_form}</tr>`;
                $(new_form_html).insertAfter('#specification-__dummy__');
                $('#id_specification-TOTAL_FORMS').val(form_idx + 1);
                copyFromEmptySpecificationForm(form_idx);
                convertFieldsToCardUI(form_idx);
            }
            else {
                form_idx = $(current).attr('data-edited-specification').split("-");
                copyFromEmptySpecificationForm(form_idx[1]);
                convertFieldsToCardUI(form_idx[1]);
            }
            $("#edit_specification_modal").modal("hide");
        }
    }
}

function addNewSpecification(){
    removeValidationError();
    $("#edit_specification_modal").find("input[type='text']").val("");
    $("#edit_specification_modal").find("input[type='checkbox']").prop("checked", false);
    $("#edit_specification_modal").modal("show");
    $("#edit_specification_modal").find(".modal-title").text("Add Specification");
    $("#add_new_specification").removeClass("hide");
    $("#update_specification").addClass("hide");
}

function browseProfileImage() {
    $("#material-profile-image").trigger("click");
}

function convertToBase64() {
    var fileExtension = ['jpeg', 'jpg', 'png'];
    if ($.inArray($("#material-profile-image").val().split('.').pop().toLowerCase(), fileExtension) == -1) {
        var selectedFile = document.getElementById("material-profile-image").files;
        if(selectedFile.length > 0) {
            swal("Invalid File Format", "Please upload only "+fileExtension.join(', ')+" file format.", "warning");
            setTimeout(function(){
                $("#material-profile-image").val('').clone(true);
                $("#id_image_data").val('');
            }, 200);
        }
    }
    else {
        var selectedFile = document.getElementById("material-profile-image").files;
        if (selectedFile.length > 0) {
            if((selectedFile[0].size) > 10485760) {
                swal("Large File Size", "Please upload a file with maximum of 10mb.", "warning");
                setTimeout(function(){
                    $("#material-profile-image").val('').clone(true);
                    $("#id_image_data").val('');
                }, 200);
            }
            else {
                var fileToLoad = selectedFile[0];
                var fileReader = new FileReader();
                var base64;
                fileReader.onload = function(fileLoadedEvent) {
                    base64 = fileLoadedEvent.target.result;
                    $("#id_image_data").val(base64);
                    $(".material-profile-image").find("img").attr("src", base64)
                };
                fileReader.readAsDataURL(fileToLoad);
            }
        }
    }
}

$(function() {
    $(".camera-roll").css("opacity","0");
    $(".camera-roll").hover(function () {
        $(this).stop().animate({
          opacity: .7
     }, "fast");
    },
    function () {
        $(this).stop().animate({
            opacity: 0
        }, "slow");
    });
});
