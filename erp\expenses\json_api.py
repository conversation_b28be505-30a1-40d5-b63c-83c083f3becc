"""
"""
import json

from django.http import HttpResponse

from erp import helper
from erp.auth import USER_IN_SESSION_KEY, SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.expenses import *
from erp.expenses.backend import ExpenseService
from util.api_util import response_code, JsonUtil

__author__ = 'nandha'


def getHeadLedgers(request):
	"""

	List Expenses
	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		user_id = rh.getPostData('user_id')
		head_type = rh.getPostData('type')  # claim_heads, expense_heads
		if not head_type:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')

		heads = head_type.split(',')
		response = response_code.success()
		for head in heads:
			head = head.strip()
			logger.info('Populating ledgers of %s' % head)
			ledger_choices = ()
			if head == 'claim_heads':
				ledger_choices = helper.populateUserClaimHeadChoices(enterprise_id=enterprise_id, user_id=user_id)
			elif head == 'expense_heads':
				ledger_choices = helper.populateUserExpenseHeadChoices(enterprise_id=enterprise_id, user_id=user_id)
			response[head] = []
			for ledger_choice in ledger_choices:
				response[head].append({'id': ledger_choice[0], 'name': ledger_choice[1]})

	except Exception as e:
		logger.exception("Failed loading expenses due to... %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % str(e.message)
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def listExpenses(request):
	"""

	List Own Expenses and expenses need to be approved by himself
	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		user_id = rh.getPostData('user_id')
		status = rh.getPostData('status') if rh.getPostData('status') != 'None' and rh.getPostData('status') != '' else None
		is_expense = rh.getPostData('is_expense')
		since, till = JsonUtil.getDateRange(rh=rh)

		my_expense_flag = is_expense.encode("utf-8") if is_expense else None
		if my_expense_flag == 'true':
			my_expense_flag = True
		if my_expense_flag == 'false':
			my_expense_flag = False

		response = response_code.success()
		list_key = "expenses"
		if status:
			list_key = STATUS_DISPLAY[int(status)]
		expense_service = ExpenseService()
		response[list_key] = expense_service.listExpenseData(
			enterprise_id=enterprise_id, user_id=user_id, since=since, till=till, status=status,
			my_expense_flag=my_expense_flag)
		response['is_expense'] = is_expense
	except Exception as e:
		logger.exception("Failed loading expenses due to... %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % str(e.message)
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def listExpensesGroup(request):
	"""

	List Expenses grouped by status
	:param request:
	:return:
	"""
	try:
		logger.info("Fetching expense list grouped by status")
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		user_id = rh.getPostData('user_id')
		since, till = JsonUtil.getDateRange(rh=rh)
		response = response_code.success()
		expense_service = ExpenseService()
		response.update(expense_service.listExpenseDataGroup(
			enterprise_id=enterprise_id, user_id=user_id, since=since, till=till))

	except Exception as e:
		logger.exception("Failed loading expenses due to... %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getExpense(request):
	"""

	List Expenses
	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = rh.getPostData('enterprise_id')
		expense_id = rh.getPostData('expense_id')

		expense_service = ExpenseService()
		expense = expense_service.expense_dao.getExpense(enterprise_id=enterprise_id, expense_id=expense_id)
		if expense:
			response = response_code.success()
			response.update(expense_service.extractExpenseData(expense=expense, exclude_particulars=False))
		else:
			response = response_code.failure()
			response['custom_message'] = "Requested data not found on server!"
	except Exception as e:
		logger.exception("Failed loading expenses due to... %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def saveExpense(request):
	"""

	Save Expense with new data / modified data
	:param request:
	:return:
	"""
	try:
		rh = RequestHandler(request)
		enterprise_id = int(rh.getPostData('enterprise_id'))
		user_id = int(rh.getPostData('user_id'))
		expense_json_data = rh.getPostData('expense_data')

		if not expense_json_data:
			return HttpResponse(json.dumps(response_code.paramMissing()), 'content-type=text/json')

		logger.info("Updating expense {enterprise:%s, by:%s}" % (enterprise_id, user_id))

		expense, is_success = ExpenseService().saveExpenseJSON(
			enterprise_id=enterprise_id, user_id=user_id, expense_dict=json.loads(expense_json_data))
		if is_success:
			response = response_code.success()
		else:
			response = response_code.failure()
	except Exception as e:
		logger.exception("Save expense failed due to... %s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def superEditExpenseCode(request):
	"""
	Super user can edit indent code through this api
	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		expense_id = request_handler.getPostData("expense_id")

		new_expense_no = int(request_handler.getPostData("new_expense_no"))
		new_financial_year = request_handler.getPostData("new_financial_year")
		new_sub_number = request_handler.getPostData("new_sub_number")
		if new_sub_number is not None and new_sub_number.strip() == "":
			new_sub_number = None
		response = ExpenseService().superEditExpenseCode(
			enterprise_id=enterprise_id, user_id=user_id, expense_id=expense_id, new_financial_year=new_financial_year,
			new_expense_no=new_expense_no, new_sub_number=new_sub_number)

	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getExpenseAccountsLinkedMessage(request):
	"""

	:param request:
	:return:
	"""
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		expense_code = request_handler.getPostData("expense_code")
		voucher = ExpenseService().expense_dao.getAssociatedVoucher(
			enterprise_id=enterprise_id, expense_code=expense_code)
		response = response_code.success()
		logger.info("Link %s" % [expense_code, voucher])
		if voucher:
			response['custom_message'] = """Voucher - <b><i>%s</i></b> is linked with this Expense. 
					Unfortunately system will not handle the perpetuation of these changes to the linked documents, 
					hence need to be addressed manually.<br/> Do you want to continue?""" % (
				voucher.getCode())
			logger.warn("Trying to edit expense %s that is linked to %s" % (expense_code, voucher.getCode()))
		else:
			response['custom_message'] = ""
	except Exception as e:
		logger.exception("%s" % e.message)
		response = response_code.internalError()
		response['error'] = '%s' % e.message
	return HttpResponse(json.dumps(response), 'content-type=text/json')
