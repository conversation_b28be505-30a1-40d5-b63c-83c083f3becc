import simplejson
from django.http import HttpResponseRedirect, HttpResponse
from django.template.response import TemplateResponse

from erp import properties, dao
from erp.auth import USER_IN_SESSION_KEY, SESSION_KEY, ENTERPRISE_ID_SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.forms import TaxForm
from erp.formsets import SubTaxFormset
from erp.masters import logger
from erp.masters.backend import createTaxLedgers
from erp.models import Tax, SubTax
from erp.properties import MANAGE_TAX_TEMPLATE, TEMPLATE_TITLE_KEY
from settings import SQLASession

__author__ = 'sa<PERSON><PERSON>n'


def manageTax(request):
	"""
	Renders the main page to manage Tax.
	:param request:
	:return:
	"""
	logger.info('Inside Manage Tax... ')
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	sub_tax_formset = SubTaxFormset(prefix="sub_tax")
	taxForms = SQLASession().query(Tax).filter(Tax.enterprise_id == enterprise_id).all()
	return TemplateResponse(template=MANAGE_TAX_TEMPLATE, request=request,
	                        context={'taxes': taxForms, 'taxForm': TaxForm(initial={
		                        'enterprise_id': enterprise_id}, prefix="tax"), 'sub_tax_formset': sub_tax_formset,
	                                TEMPLATE_TITLE_KEY: "Tax"})


def saveTax(request):
	"""
	Save Tax
	Persists them in appropriate tables after validation.

	:param request:
	:return:
	"""
	logger.info('Inside Save Taxes...')
	request_handler = RequestHandler(request)
	user_id = request_handler.getSessionAttribute(SESSION_KEY)
	db_session = SQLASession()
	db_session.begin(subtransactions=True)
	try:
		tax_form = TaxForm(data=request.POST, prefix="tax")
		sub_tax_formset = SubTaxFormset(request.POST, prefix="sub_tax")
		tax_to_be_saved = db_session.query(Tax).filter(
			Tax.code == request.POST['tax-code'],
			Tax.enterprise_id == request.session[ENTERPRISE_ID_SESSION_KEY]).first()
		logger.info("Tax to be saved: %s" % tax_to_be_saved)
		if tax_form.is_valid() and sub_tax_formset.is_valid():
			if tax_to_be_saved:
				_copyTaxFormToEntity(tax_form, tax_to_be_saved)
			else:
				tax_to_be_saved = _getEntityFromTaxForm(tax_form)
				db_session.add(tax_to_be_saved)
			createTaxLedgers(tax=tax_to_be_saved, created_by=user_id, db_session=db_session, is_input=True,
			                 is_output=True)
			# FIXME: Re-write the below logic in a more appropriate manner
			logger.info('Saving SubTaxes... %d' % len(sub_tax_formset))
			for sub_tax_form in sub_tax_formset:
				sub_tax = db_session.query(SubTax).filter(SubTax.name == sub_tax_form.cleaned_data['name'].strip(),
								SubTax.parent_code == tax_to_be_saved.code, SubTax.enterprise_id == tax_to_be_saved.enterprise_id).first()
				logger.info("SubTax queried for condition Name: %s, Parent_code: %s, Enterprise_id: %s- %s" % (
					sub_tax_form.cleaned_data['name'], tax_to_be_saved.code, tax_to_be_saved.enterprise_id, sub_tax))
				if sub_tax:
					if sub_tax_form.cleaned_data['DELETE']:
						logger.info("Sub Tax to be removed: %s" % sub_tax)
						db_session.delete(sub_tax)
						tax_to_be_saved.sub_taxes.remove(sub_tax)
					else:
						_copySubTaxFormToEntity(sub_tax_form, tax_form, sub_tax)
				elif not sub_tax_form.cleaned_data['DELETE']:
					sub_tax = _getEntityFromSubTaxForm(sub_tax_form, tax_form)
					db_session.add(sub_tax)
					tax_to_be_saved.sub_taxes.append(sub_tax)
			logger.debug('saveTax:Committing all transactions...\nSession Dirt: %s' % db_session.dirty)
			db_session.commit()
		else:
			logger.info('Tax Form errors: %s\nSub-Taxes Formset Errors: %s' % (tax_form.errors, sub_tax_formset.errors))
			db_session.rollback()
	except Exception as e:
		db_session.rollback()
		logger.exception(e)
		raise
	return TemplateResponse(template=MANAGE_TAX_TEMPLATE, request=request,
							context={'taxes': [], 'taxForm': tax_form, 'sub_tax_formset': sub_tax_formset,
							         TEMPLATE_TITLE_KEY: tax_form.cleaned_data['code']})


def editTax(request):
	logger.info('Editing a Tax...')
	if request.method == 'POST':
		tax_code = request.POST['code']
		enterprise_id = request.session[ENTERPRISE_ID_SESSION_KEY]
		logger.info("Tax Id selected: %s" % tax_code)
		tax_under_edit = SQLASession().query(Tax).filter(Tax.code == tax_code, Tax.enterprise_id == enterprise_id).first()
		tax_form = TaxForm(initial=_getTaxFormInitializerFromEntity(tax_under_edit), prefix="tax")
		sub_taxes = SQLASession().query(SubTax).filter(SubTax.parent_code == tax_code, Tax.enterprise_id == enterprise_id).all()
		logger.debug(sub_taxes)
		sub_tax_forms = []
		for sub_tax in sub_taxes:
			sub_tax_forms.append(_getSubTaxFormInitializerFromEntity(sub_tax))
		sub_tax_formset = SubTaxFormset(initial=sub_tax_forms, prefix="sub_tax")
		tax_forms = SQLASession().query(Tax).filter(Tax.enterprise_id == enterprise_id)
		edit_flag = "edit"

		return TemplateResponse(template=MANAGE_TAX_TEMPLATE, request=request,
		                        context={'taxes': tax_forms, 'taxForm': tax_form, 'sub_tax_formset': sub_tax_formset,
		                                 'edit': edit_flag, TEMPLATE_TITLE_KEY: tax_code})
	else:
		HttpResponseRedirect(properties.MANAGE_TAX_URL)


def deleteTax(request):
	"""
	Soft Delete of Tax - setting the status to 0
	:param request:
	:return:
	"""
	logger.info('Deleting a Tax...')
	db_session = SQLASession()
	db_session.begin(subtransactions=True)
	try:
		db_session.query(Tax).filter(Tax.code == request.POST['deleteCode'], Tax.enterprise_id == request.session[
			ENTERPRISE_ID_SESSION_KEY]).delete()
		db_session.commit()
	except Exception as e:
		db_session.rollback()
		logger.exception(e)
	return HttpResponseRedirect(properties.MANAGE_TAX_URL)


def _copyTaxFormToEntity(form, entity):
	entity.code = form.cleaned_data['code']
	entity.name = form.cleaned_data['name']
	entity.base_rate = form.cleaned_data['base_rate']
	entity.is_compound = form.cleaned_data['is_compound']
	entity.net_rate = form.cleaned_data['net_rate']
	entity.assess_rate = form.cleaned_data['assess_rate']
	entity.enterprise_id = form.cleaned_data['enterprise_id']
	entity.type = form.cleaned_data['type']


def _getEntityFromTaxForm(form):
	entity = Tax(code=form.cleaned_data['code'], name=form.cleaned_data['name'],
	             base_rate=form.cleaned_data['base_rate'], net_rate=form.cleaned_data['net_rate'],
	             is_compound=form.cleaned_data['is_compound'], assess_rate=form.cleaned_data['assess_rate'],
	             enterprise_id=form.cleaned_data['enterprise_id'], type=form.cleaned_data['type'])
	return entity


def _getTaxFormInitializerFromEntity(entity):
	return {'code': entity.code,
	        'name': entity.name,
	        'base_rate': entity.base_rate,
	        'is_compound': entity.is_compound,
	        'net_rate': entity.net_rate,
	        'assess_rate': entity.assess_rate, 'enterprise_id': entity.enterprise_id, 'type': entity.type}


def _getSubTaxFormInitializerFromEntity(entity):
	return {'parent_code': entity.parent_code,
	        'name': entity.name,
	        'rate': entity.rate,
	        }


def _copySubTaxFormToEntity(sub_tax_form, tax_form, sub_tax_entity):
	sub_tax_entity.parent_code = tax_form.cleaned_data['code']
	sub_tax_entity.name = sub_tax_form.cleaned_data['name']
	sub_tax_entity.rate = sub_tax_form.cleaned_data['rate']
	return sub_tax_entity


def _getEntityFromSubTaxForm(sub_tax_form, tax_form):
	sub_tax_entity = SubTax(parent_code=tax_form.cleaned_data['code'],
	                        name=sub_tax_form.cleaned_data['name'],
	                        rate=sub_tax_form.cleaned_data['rate'],
							enterprise_id=tax_form.cleaned_data['enterprise_id']
	                        )
	return sub_tax_entity


def checkTaxCode(request):
	"""

	:param request:
	:return:
	"""
	request_handler = RequestHandler(request)
	enterprise_id = request_handler.getSessionAttribute(ENTERPRISE_ID_SESSION_KEY)
	try:
		tax_code_query = "Select count(1) from tax where enterprise_id='%s' and code = '%s'" % (
				enterprise_id, request_handler.getPostData("tax_code"))
		tax_code_count = dao.executeQuery(tax_code_query)
		json = simplejson.dumps(str(tax_code_count[0][0]))
	except Exception as e:
		logger.exception(e)
		json = simplejson.dumps(str(e))
	return HttpResponse(json)