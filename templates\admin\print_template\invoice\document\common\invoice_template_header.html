<!DOCTYPE html>
<style>
	@font-face {
	        font-family: 'pdf_Roboto';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/roboto.regular.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Times New Roman';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/times.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Helvetica';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/roboto.regular.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Ubuntu';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/Ubuntu-Regular.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Comic Sans';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/comicsansms3.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_DejaVu Sans';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/DejaVuSans.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Courier New';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/cour.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Verdana';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/verdana.ttf) format('truetype');
	}

	@font-face {
	        font-family: 'pdf_Open Sans';
	        font-style: normal;
	        font-weight: 400;
	        src: url(https://www.xserp.in/site_media/fonts/open-sans-Regular.ttf) format('truetype');
	}

</style>
<script>
	function subst() {
		var vars={};
		var x=document.location.search.substring(1).split('&');
		for (var i in x) {
			var z=x[i].split('=',2);
			vars[z[0]] = unescape(z[1]);
		}
		var x=['frompage','topage','page','webpage','section','subsection','subsubsection'];
		for (var i in x) {
			var y = document.getElementsByClassName(x[i]);
			for (var j=0; j<y.length; ++j)
				y[j].textContent = vars[x[i]];

			if(vars['page'] == 1){
			   document.getElementById("annexure_header").style.display = 'none';
			   document.getElementById("label_header").style.display = 'inline-table';
			}
			else {
			   document.getElementById("annexure_header").style.display = 'inline-table';
			   document.getElementById("label_header").style.display = 'none';
			}
		}
	}
</script>
<body onload="subst()">
	{% if labels %}
		<table id="label_header" style="width: 100% !important; font-size: 10pt; font-family: pdf_{{general_res.base_font}};">
			<tr>
				<td style="text-align:right; letter-spacing: 1.4px; {% if misc_res.include_page_no_in_footer %}padding-right: 75px;{% endif %} ">
					<span style="border: solid 2px #CCC; padding: 2px 10px;">{{ labels }}</span>
				</td>
			</tr>
		</table>
	{% endif %}
	{% if misc_res.include_first_page_summary %}
		<table id="annexure_header" style="width: 100% !important; font-size: {{ item_res.font_size }}pt; font-family: pdf_{{general_res.base_font}};">
			<tr>
				<td style="text-align:center; letter-spacing: 1.4px;">ANNEXURE TO {{ form_name|upper }} # <b> {{ invoice_no }} </b> ISSUED ON <b> {{ issued_date }} </b></td>
			</tr>
		</table>
	{% endif %}
</body>
