{% extends "template.html" %}
{% block reportsHome %}
<!-- Sidebar Menu Items - These collapse to the responsive navigation menu on small screens -->
<link rel="stylesheet" type="text/css" href="/site_media/css/reports.css?v={{ current_version }}" >
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/bootstrap-multiselect.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/lodash.min.js?v={{ current_version }}"></script>
<style>

	.li-select-all label{
		color: #209be1;
	}
	
	.total_tax,
	.total_invoice,
	.total_cgst,
	.total_sgst,
	.total_igst,
	.gross_invoice_value {
		text-align: right;
	}

	.party_name a,
	.product_name a {
		margin-left: 30px !important;
	}
	.invoice_no a {
		margin-left: 30px !important;
	}

	.filtered_applied {
		font-size: 14px;
	    position: absolute;
	    margin: auto;
	   	left: 0;
	   	right: 0;
	    margin-top: -20px;
	    margin-left: 15px;
	}

	.filtered_applied .filter_file_name {
		font-size: 20px;
	}

	.total_col td {
		font-size: 16px;
		font-weight: bold;
	}

	#reportrange.div-disable {
		text-decoration: line-through;
	}

	.adv_filter_consolidated .styled-checkbox.chk-wo-margin + label:before {
		margin-right: 15px;
	}

	.adv_filter_consolidated .styled-checkbox.chk-wo-margin + label:after {
		left:  11px;
		top: 16px;
	}

	.tr_consolidated_name {
		text-transform: capitalize;
	}

	.daterangepicker.dropdown-menu {
		z-index: 10030;
	}

	#tablesorter th,
	#tablesorter td {
		min-width: 150px !important;
	}

	#tablesorter th:first-child,
	#tablesorter td:first-child {
		min-width: 70px !important;
	}

	#tablesorter th:last-child,
	#tablesorter td:last-child {
		min-width: 40px !important;
		border:  none;
	}
</style>
<div class="collapse navbar-collapse navbar-ex1-collapse left_nav_mob">
	<div class="side_bar_menu side-nav">
		<ul class="nav">
			<li class="active"><a href="#">Report</a></li>
		</ul>
	</div>
</div>
<!-- /.navbar-collapse -->
</nav>
<div class="right-content-container" style="margin-top: 35px;">
	<div class="page-title-container">
        <span class="page-title">Custom Sales Report</span>
    </div>
	<div class="container-fluid">
		<!-- Page Heading -->
		<div class="row">
			<div class="col-lg-12">
				<div class="content_bg">
					<div class="row">
						<div class="col-lg-12 add_table_content">
                            <div class="col-sm-3">
                            	<label>INVOICE APPROVED DATE RANGE</label>
                            	<a role="button" class="pull-right ignore-datepicker" onclick="ignoreDateRangePicker();">Ignore This</a>
                                <div id="reportrange" class="report-range form-control">
                                    <i class="glyphicon glyphicon-calendar"></i>&nbsp;
                                    <span></span> <b class="caret"></b>
                                    <input type="hidden" class="fromdate" id="fromdate"
                                           name="fromdate"/>
                                    <input type="hidden" class="todate" id="todate" name="todate"/>
                                </div>
                            </div>
							<div id="select_div" class="col-sm-3 form-group multiselect_option">
								<label>Select</label>
								<a class="pull-right" role="button" onclick="salesAdvancedFilter();" id="lbl_adv_filter">Advance Filter</a>
								<select  name="select" id="item_multuselect" multiple="multiple">
									<option value="" >SELECT ALL</option>
									<option data_for="string" value="party_name" >Customer Name</option>
									<option data_for="numeric" value="credit_period" class="party_name hide" >Credit Period</option>
									<option data_for="string" value="product_name" >Product Name</option>
									<option data_for="string" value="hsn_code" class="product_name hide">HSN Code</option>
									<option data_for="string" value="product_make" class="product_name hide">Make</option>
									<option data_for="string" value="product_faulty" class="product_name hide">Faulty</option>
									<option data_for="numeric" value="matl_invoice_qty" class="product_name hide" >Material Wise Invoice Qty</option>
									<option data_for="numeric" value="matl_unit_price" class="product_name hide">Material Wise Unit Price</option>
									<option data_for="numeric" value="matl_invoice_value" class="product_name hide">Material Wise Invoice Value</option>
									<option data_for="numeric" value="matl_invoice_value_ex_tax" class="product_name hide">Material Wise Invoice Value (excl. Tax)</option>
<!--									<option data_for="numeric" value="matl_cgst_tax" class="product_name hide">Material Wise CGST Tax</option>-->
<!--									<option data_for="numeric" value="matl_sgst_tax" class="product_name hide">Material Wise SGST Tax</option>-->
<!--									<option data_for="numeric" value="matl_igst_tax" class="product_name hide">Material Wise IGST Tax</option>-->
									<option data_for="numeric" value="matl_total_tax" class="product_name hide">Material Wise Total Tax</option>
<!--									<option data_for="numeric" value="matl_other_tax" class="product_name hide">Material Wise Other Tax</option>-->
									<option data_for="string" value="oa_number" >OA Number</option>
									<option data_for="date" value="oa_date" >OA Date</option>
									<!--<option value="oa_status" >OA Status</option>-->
									<option data_for="string" value="project" >Project/Tag</option>
									<option data_for="string" value="invoice_no" >Invoice Number</option>
									<option data_for="string" value="invoice_payment_status" class="invoice_no hide" >Invoice Payment Status</option>
									<option data_for="string" value="invoice_pending_amount" class="invoice_no hide">Pending Amount</option>
									<option data_for="numeric" value="aging" class="invoice_no hide" >Ageing (days)</option>
									<option data_for="date" value="invoice_date" >Invoice Date</option>
									<option data_for="string" value="tags" >Tags</option>
									<option data_for="numeric" value="total_tax" >Total Tax in Invoice</option>
<!--									<option data_for="numeric" value="other_tax" >Other Tax in Invoice</option>-->
									<option data_for="numeric" value="total_invoice" >Invoice Value (excl. taxes)</option>
<!--									<option data_for="numeric" value="total_cgst" >Total CGST in Invoice</option>-->
<!--									<option data_for="numeric" value="total_sgst" >Total SGST in Invoice</option>-->
<!--									<option data_for="numeric" value="total_igst" >Total IGST in Invoice</option>-->
									<option data_for="numeric" value="gross_invoice_value" >Gross Invoice Value</option>
								</select>
							</div>
                            <div class="col-sm-3 remove-padding">
                                <span class="material_txt">
                                    <input type="button" class="btn btn-save btn-margin-1" value="View Report" id="customReportView"/>
	                                <input type="hidden" value="" id="adv_filter_data"/>
                                </span>
                            </div>
                            <div class="col-sm-3" style="margin-top: 11px;">
								<label>SAVED TEMPLATES</label>
								<select name="report_list" class="chosen-select" id="report_list">
									<option value="0">-- Select Template --</option> R
									{% for code, name in report_list %}
										<option value="{{ code }}">{{name}}</option>
									{% endfor %}
								</select>
							</div>

                            <div class="clearfix"></div>
                            <div class="col-sm-12 text-right">
                            	<a role="button" class="btn btn-add-new export_csv btn-margin-1" data-tooltip="tooltip" title="Save this Template for later" id="saveModalReport"><i class="fa fa-save fa-4" aria-hidden="true"></i></a>
						        <a role="button" class="btn btn-add-new btn-margin-1" data-tooltip="tooltip" title="Delete this Template" id="deleteReportView"><i class="fa fa-trash fa-4" aria-hidden="true"></i></a>
								<a role="button" class="btn btn-add-new export_csv btn-margin-1" id="exportAsCSV" data-tooltip="tooltip" title="Download Custom Sales Report as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
							</div>
                            <div id="cus_sales_report">
								<div class="col-lg-12 search_result_table">
									<div class="text-center filtered_applied hide">
										<label></label>
										<span class="filter_file_name myfont-number mycolor-blue-2"></span>
										(<span class="filter_user_name myfont-number mycolor-grey-6"></span>)
									</div>
									<table class="table table-bordered custom-table table-striped hide" id="tablesorter" style="border: none;">
										<thead>
											<tr id="report_tbl_title">
												<th>S No</th>
											</tr>
										</thead>
										<tbody id="report_tbl_body">
										</tbody>
										<tfoot class="cs_tfoot">

										</tfoot>
									</table>
									<div id='loadingmessage' class="text-center hide">
											<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' style="width: 75px;" />
											<br/>Please wait...
									</div>
								</div>
                            </div>
                        </div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div id="saveFileName" class="modal fade" role="dialog">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Save Report</h4>
			</div>
			<div class="modal-body">
				<div>
					<label>File Name</label>
					<input type="text" name="laterFileName" id="laterFileName" class="form-control" maxlength="30">
				</div>
				<br/>
				<div>
					<label>Type</label><br/>
					<input type="radio" name="saveType" id="saveType" value="0" checked=""> Public
					<input type="radio" name="saveType" id="saveType" value="1"> Private
				</div>
			</div>
			<div class="modal-footer">
				<input type="button" class="btn btn-save" value="Save" id="saveReport"/>
				<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>
<div id="sales_advance_filter" class="modal fade" role="dialog" data-backdrop="static" data-keyboard="false">
  	<div class="modal-dialog modal-lg">
    	<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" onclick="filterCloseEvent();">&times;</button>
        		<h4 class="modal-title">Advance Filter</h4>
      		</div>
      		<div class="modal-body" style="padding: 0;">
      			<div class="col-md-4 adv_filter_list_container">
      			</div>
      			<div class="col-md-8 remove-padding hide" id="filter_container" >
      				<div class="col-md-12 adv_filter_consolidated hide" style="border-bottom: 1px solid #ccc; padding: 10px; padding-top: 0;">
      					<label style="margin-top: 6px; margin-left: -7px;">CONSOLIDATE BY</label>      				
      					<div class="chkcase">
	      					<input type="radio" name="adv_consolidate_selector" value="none" id="adv_consolidated_none" class="styled-checkbox chk-wo-margin rounded_chk" checked="true" />
	      					<label for="adv_consolidated_none">None</label>
      					
	      					<input type="radio" name="adv_consolidate_selector" value="date" id="adv_consolidated_date" class="styled-checkbox chk-wo-margin rounded_chk" />
	      					<label for="adv_consolidated_date">Date</label>
      					
	      					<input type="radio" name="adv_consolidate_selector" value="month" id="adv_consolidated_month" class="styled-checkbox chk-wo-margin rounded_chk" />
	      					<label for="adv_consolidated_month">Month</label>
      					
	      					<input type="radio" name="adv_consolidate_selector" value="year" id="adv_consolidated_year" class="styled-checkbox chk-wo-margin rounded_chk" />
	      					<label for="adv_consolidated_year">Year</label>
      					</div>
      				</div>
      				<div class="clearfix"></div>
      				<label style="margin-left: 10px; margin-top: 6px;">Conditions</label>
	      			<div class="col-md-12" style="border-bottom: 1px solid #ccc; padding: 10px; padding-top: 0;">
	      				<div class="adv_filter_condition chkcase">
	      					<div class="for_string for_numeric for_dates">
		      					<input type="radio" name="adv_condition_selector" value="equal_to" id="adv_equal_to" class="styled-checkbox chk-wo-margin rounded_chk" />
		      					<label for="adv_equal_to">Equal To</label>
		      				</div>
		      				<div class="for_string">
		      					<input type="radio" name="adv_condition_selector" value="equal_to_case" id="adv_equal_to_case" class="styled-checkbox chk-wo-margin rounded_chk" />
		      					<label for="adv_equal_to_case">Equal To (case-sensitive)</label>
		      				</div>
		      				<div class="for_string">
		      					<input type="radio" name="adv_condition_selector" value="contains" id="adv_contains" class="styled-checkbox chk-wo-margin rounded_chk" />
		      					<label for="adv_contains">Contains</label>
		      				</div>
		      				<div class="for_string">
		      					<input type="radio" name="adv_condition_selector" value="begins_with" id="adv_begins_with" class="styled-checkbox chk-wo-margin rounded_chk" />
		      					<label for="adv_begins_with">Begins with</label>
		      				</div>
		      				<div class="for_string">
		      					<input type="radio" name="adv_condition_selector" value="ends_with" id="adv_ends_with" class="styled-checkbox chk-wo-margin rounded_chk" />
		      					<label for="adv_ends_with">Ends with</label>
		      				</div>
		      				<div class="for_numeric">
		      					<input type="radio" name="adv_condition_selector" value="greater_than" id="adv_greater_than" class="styled-checkbox chk-wo-margin rounded_chk" />
		      					<label for="adv_greater_than">Greater than</label>
		      				</div>
		      				<div class="for_numeric">
		      					<input type="radio" name="adv_condition_selector" value="greater_than_equal" id="adv_greater_than_equal" class="styled-checkbox chk-wo-margin rounded_chk" />
		      					<label for="adv_greater_than_equal">Greater than & Equal to</label>
		      				</div>
		      				<div class="for_numeric">
		      					<input type="radio" name="adv_condition_selector" value="lesser_than" id="adv_lesser_than" class="styled-checkbox chk-wo-margin rounded_chk" />
		      					<label for="adv_lesser_than">Lesser than</label>
		      				</div>
		      				<div class="for_numeric">
		      					<input type="radio" name="adv_condition_selector" value="lesser_than_equal" id="adv_lesser_than_equal" class="styled-checkbox chk-wo-margin rounded_chk" />
		      					<label for="adv_lesser_than_equal">Lesser than & Equal to</label>
		      				</div>
		      				<div class="for_numeric for_dates">
		      					<input type="radio" name="adv_condition_selector" value="between" id="adv_between" class="styled-checkbox chk-wo-margin rounded_chk" />
		      					<label for="adv_between">Between</label>
		      				</div>
		      				<div class="for_dates">
		      					<input type="radio" name="adv_condition_selector" value="after" id="adv_after" class="styled-checkbox chk-wo-margin rounded_chk" />
		      					<label for="adv_after">After</label>
		      				</div>
		      				<div class="for_dates">
		      					<input type="radio" name="adv_condition_selector" value="before" id="adv_before" class="styled-checkbox chk-wo-margin rounded_chk" />
		      					<label for="adv_before">Before</label>
		      				</div>
	      				</div>
	      			</div>
	      		
	      			<div class="col-md-12 remove-padding" style="margin-top: 12px;">
	      				<div class="col-md-4 form-group adv_value_1">
		        			<label>Values</label>
		        			<input id="adv_filter_value_1" type="text" class="form-control" name="adv-filter-value-1" maxlength="40" disabled="disabled">
		        			<i class="glyphicon glyphicon-calendar custom-calender-icon hide"></i>
		        		</div>
		        		<div class="col-md-6 form-group adv_value_2 hide">
		        			<label>Values</label>
		        			<div id="reportrange_1" class="report-range_2 form-control">
                                <i class="glyphicon glyphicon-calendar"></i>&nbsp;
                                <span></span> <b class="caret"></b>
                                <input type="hidden" class="fromdate_2" id="fromdate" name="fromdate"/>
								<input type="hidden" class="todate_2" id="todate" name="todate"/>
                            </div>
		        		</div>
		        		<div class="col-md-4 form-group adv_value_3 hide">
		        			<span style="position: absolute; margin-left: -24px; margin-top: 28px; font-size: 11px; color: #999;">and</span>
		        			<label>Values</label>
                            <input id="adv_filter_value_2" type="text" class="form-control" name="adv-filter-value-2" maxlength="40">
		        		</div>
			        	<div class="col-md-4 form-group">
			        		<span class="btn btn-save" style="margin-top: 17px;" role="button" onclick="addAdvFilter();">Add Filter</span>
			        	</div>
	      			</div>
	      		</div>
	      		<div class="col-md-8">
	      			<h4 style="padding: 5px; padding-bottom: 0;">Selected Filters <span id="filter_date_range_container" style="font-size: 11px;">(including Invoice Approved date range between <span id="filter_date_range"></span>) <i class="fa fa-times" aria-hidden="true" role="button" onclick="removeAdvFilterDateRange();" data-tooltip="tooltip" title="Ignote this date range"></i></span></h4>
      				<table class="table table-bordered table-striped" id="adv_filter_table" style="border-collapse: inherit; margin-top: 15px;">
      					
      				</table>
      			</div>
  			</div>
  			<div class="modal-footer">
        		<button type="button" class="btn btn-cancel hide" data-dismiss="modal">Close</button>
        		<button type="button" class="btn btn-save" onclick="applyAdvFilter();">Apply</button>
        		<div class="apply_fail_text hide" style="color: #999; font-size: 12px;">Changes made in filter criteria are yet to be applied!</div>
      		</div>
  		</div>
  	</div>
</div>
<script>
var isAdvFilterEdited = false;
$(document).ready(function() {
	$('#item_multuselect').multiselect();
	$(".chosen-select").chosen();
	MultiselectInit();
	$("#saveModalReport, #exportAsCSV, #deleteReportView").attr("disabled", true);
	DateRangeInit_2();
});

 function filterCloseEvent() {
 	if(isAdvFilterEdited) {
 		swal({
            title: "",
            text: "Few changes are made in the Filter Criteria. Do you want to apply the changes?",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#209be1",
            confirmButtonText: "Apply",
            cancelButtonText: "Ignore",
            closeOnConfirm: true,
            closeOnCancel: true
        },
        function(isConfirm){
        	if (isConfirm) {
        		applyAdvFilter();
        	}
        	else {
        		$("#sales_advance_filter").modal("hide");
        		$(".apply_fail_text").removeClass("hide");
        	}
        });
 	}
 	else {
 		$("#sales_advance_filter").modal("hide");
 	}
 }

  function removeAdvFilterDateRange() {
 	$("#filter_date_range_container").addClass("hide");
 	$("#filter_date_range").text("");
 	ignoreDateRangePicker();
 }

function MultiselectInit() {
	setTimeout(function(){
		$("#item_multuselect").next('.btn-group').find(".multiselect-container li:nth-child(2)").each(function(){
			$(this).addClass('li-select-all');
		});
		$("#item_multuselect").next('.btn-group').find(".multiselect-container li:gt(1)").each(function(){
			$(this).addClass('li-all-others');
		});
		$('.li-all-others').on("change",function(){
			if($('.li-all-others').find('input:checked').length == $('.li-all-others').find('input').length) {
				$('.li-select-all').addClass('active').find('input').prop("checked", true);
			}
			else {
				$('.li-select-all').removeClass('active').find('input').prop("checked", false);
			}
			var currentInput = $(this).find('input').val();
			if(["product_name", "party_name", "invoice_no"].indexOf(currentInput) != -1){
				console.log(currentInput)
				if($(this).find('input').is(":checked")){
					$("."+currentInput).removeClass("hide");
				}
				else {
					$("."+currentInput).addClass('hide').closest("li").removeClass("active");
					$("."+currentInput).find('input').prop("checked", false);
				}
			}
			SelectedItemCount();
		});

		$('.li-select-all').on("change",function(){
			if($(this).find('input').is(":checked")){
				$('.li-all-others').addClass("active").find('input').prop("checked", true);
				$(".product_name, .party_name, .invoice_no").removeClass('hide');
			}
			else {
				$('.li-all-others').removeClass("active").find('input').prop("checked", false);
				$(".product_name, .party_name, .invoice_no").addClass('hide');
				$(".product_name, .party_name, .invoice_no").find('input').prop("checked", false);
			}
			SelectedItemCount();
		});
	},100);
}

function SelectedItemCount() {
	if($('.li-all-others').find('input:checked').length == 0) {
		$("#item_multuselect").next('.btn-group').find('.multiselect-selected-text').text('None selected');
	}
	else if($('.li-all-others').find('input:checked').length == $('.li-all-others').find('input').length) {
	    $("#item_multuselect").next('.btn-group').find('.multiselect-selected-text').text('All selected');
	 }
	else {
		$("#item_multuselect").next('.btn-group').find('.multiselect-selected-text').text($('.li-all-others').find('input:checked').length + ' Item(s) Selected');
	}
}

var myCalendar;
	function doOnLoad() {
		myCalendar = new dhtmlXCalendarObject(["calendar","calendar1"]);
		myCalendar.setDateFormat("%d/%m/%Y");
	}

</script>
<script src="/site_media/js/jquery.easyResponsiveTabs.js?v={{ current_version }}"></script>
<script type="text/javascript">
    jQuery(document).ready(function () {
		$('#table_tab').easyResponsiveTabs({
            type: 'default', //Types: default, vertical, accordion
            width: 'auto', //auto or any width like 600px
            fit: true, // 100% fit in a container
            closed: 'accordion', // Start closed if in accordion view
            tabidentify: 'hor_1', // The tab groups identifier
            activate: function (event) { // Callback function if tab is switched
                var $tab = $(this);
                var $info = $('#nested-tabInfo');
                var $name = $('span', $info);

                $name.text($tab.text());

                $info.show();
            }
        });
 });

if (jQuery(this).width() <= 767 ) {
	jQuery(document).ready(function () {
		$('#table_tab h2:first').click();
 });
}

function getConsolidateReport(type, from, to, values= false){
	$("#tablesorter").addClass("hide");
	var fields = [];
	var adv_filter = "";

	$('#tablesorter').DataTable().clear();
	$('#tablesorter').DataTable().destroy();
	if($('#reportrange').hasClass('div-disable')){
	from = '';
	to = '';
	}
	if(values == false) {
		$('.li-all-others').find('input:checked').each(function(){
			fields.push($(this).val());
		});
		$(".filtered_applied").addClass('hide');
	}
	else {
		$(".filtered_applied").removeClass('hide');
		fields = values.split(',');
	}
	var consolidate_fields = ["other_tax", "total_invoice", "total_cgst", "total_sgst", "total_igst", "gross_invoice_value", "total_tax"]
	if (fields.length == 0) {
		swal({
			title: "",
		  	text: "Please select at-least one field to generate report",
		  	type: "warning",
		});
		$("#saveModalReport, #exportAsCSV").attr("disabled", true);
		return;
	}
	if ($("#adv_filter_data").val()){
			adv_filter = $("#adv_filter_data").val();
	}
	$.ajaxSetup({
        headers: {"X-CSRFToken": getCookie('csrftoken')}
    });
    $.ajax({
        url: "/erp/reports/json/sales_report/",
        type: "POST",
        dataType: "json",
        data: {fields : fields, from : from, to : to, adv_filter: adv_filter},
        beforeSend: function () {
            $("#loadingmessage").addClass('show');
        },
        success: function (json) {
             if(json.response_code == 400){
                location.reload();
            }else if('error' in json){
				swal({
					title: 'Sorry!',
					text: 'Unable to fetch a report for the chosen Field combinations!',
					type: 'error'
				},function(){
		            $("#loadingmessage").removeClass('show');
		            $("#loadingmessage").addClass('hide');
				});
            }else{
                $("#saveModalReport, #exportAsCSV").attr("disabled", false);
                $("#loadingmessage").removeClass('show');
	            $("#loadingmessage").addClass('hide');
	            const grouped = _.groupBy(json, j => j.invoice_id);
	            var gross_val_sum = 0;
	            $.each(grouped, function(key, val) {
				     gross_val_sum += parseInt(val[0]['gross_invoice_value'],10);
				});

	            fields.unshift('S No');
	            document.getElementById("report_tbl_title").innerHTML ="";
	            document.getElementById("report_tbl_body").innerHTML ="";
	            report_title_html ="";report_data_html="";
	            $.each(fields, function(key, val){
	                var setVal = $("#item_multuselect").find("option[value='"+val+"']").text();
	                if(setVal == "")  {
	                	setVal = 'S No';
	                	report_title_html += '<th style="min-width: 75px;">' + setVal + '</th>';
	                }
	                else {
	                	report_title_html += '<th style="min-width: 150px;">' + setVal + '</th>';	
	                }
	                if(fields.length == (key + 1)){
                		report_title_html += '<th style="min-width: 40px; visibility: hidden;"></th>';	
                	}
	            });
	            document.getElementById("report_tbl_title").innerHTML = report_title_html;

	            $.each(json, function (idx, val) {
	                month_yr = '';
	                $.each(fields, function (key, value) {
	                    if(value == 'invoice_date'){
	                        month_yr =   moment(val[value]).format('MMM') + '_' + moment(val[value]).format('YYYY');
	                    }
	                });
		            report_data_html += '<tr class="' + month_yr +'">';
		            $.each(fields, function (key, value) {
		                if(value == 'invoice_date' || value == 'oa_date' ){
		                    val[value] = moment(val[value]).format('MMM D, YYYY');
		                }
		                if(consolidate_fields.indexOf(value) != -1){
		                    report_data_html += '<td class="'+value+'">' + val[value] + '</td>';
		                }else{
		                 report_data_html += '<td >' + val[value] + '</td>';
		                 }
		                 if(fields.length == (key + 1)){
	                		report_data_html += '<td style="min-width: 40px; visibility: hidden;"></td>';	
	                	}
		            });
		            report_data_html += '</tr>';
	            });
	            document.getElementById("report_tbl_body").innerHTML = report_data_html;
	            var sNo = 1;
	            $("#tablesorter #report_tbl_body").find("tr").each(function(){
	                $(this).find("td:first-child").text(sNo++).addClass('text-center');

	            });
	            ConsolidatedSum(gross_val_sum);
	            setTimeout(function(){
		            TableHeaderFixed();
		        },100);
	            $("#tablesorter").removeClass("hide");
            }
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });
        return false;
}

$(".chk-daterange").on("change", function(){
    $(".chk-daterange").prop('checked', false);
    $(this).prop('checked', true);
    $(".date-range").addClass('hide');
    $("."+$(this).attr('id')).removeClass('hide');
});

$('.search_month_from, .search_month_to').datepicker({
    autoclose: true,
    minViewMode: 1,
    format: 'M, yyyy'
});

$('.search_year').datepicker({
    autoclose: true,
    minViewMode: 2,
    format: 'yyyy'
});

$(".search_month_from").on("change",function(){
    //$('.search_month_to').datepicker('setStartDate', new Date($(this).val()));
});

$(".search_year_from").on("change",function(){
    //$('.search_year_to').datepicker('setStartDate', new Date($(this).val()));
});

$("#customReportView").click(function(){
	customSalesReportViewSubmit();
});

function customSalesReportViewSubmit(filterArray){
	$(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();
	getConsolidateReport(filterArray,  moment($("#fromdate").val()).format('YYYY-MM-DD'), moment($("#todate").val()).format('YYYY-MM-DD'));
	$(".filtered_applied").addClass('hide');
	$("#report_list").val(0).trigger("chosen:updated");
}

$("#report_list").on("change", function(){
	var report_code = $("#report_list").val();
	var report_name = $("#report_list option:selected").text();
	if(report_code == "0"){
	location.reload();
	}else{

	//$("#item_multuselect option").removeAttr('selected');
	$.ajax({
        url: "/erp/reports/json/get_report/",
        type: "POST",
        dataType: "json",
        data: {code : report_code},
        success: function (json) {
	        if(json["adv_filter"] != "" && json["adv_filter"] != null){
			    $("#adv_filter_data").val(json["adv_filter"]);
			    advFilterLoadOnEdit();
			}
			$("#saveModalReport, #exportAsCSV").attr("disabled", false);
            ga('send', 'event', 'Sales', 'View Saved Report', $('#enterprise_label').val(), 1);
            getConsolidateReport(json["type"],moment($("#fromdate").val()).format('YYYY-MM-DD'), moment($("#todate").val()).format('YYYY-MM-DD'),json["values"]);
            $(".chk-daterange").prop('checked', false);
            $(".date-range").addClass('hide');
            var selectedType = "";
            if(selectedType != "") {
	        	$("."+selectedType).removeClass('hide');
	        }
		  	var loadedValues = (json["values"]).split(",");
		  	$("#item_multuselect option").prop('selected', false);
		  	$.each( loadedValues, function( index, value ) {
		  		$("#item_multuselect option[value='"+value+"']").prop('selected', true);
		  		if(value == "party_name") {
		  			$(".party_name").removeClass('hide')
		  		}
		  		else if(value == "product_name" ) {
		  			$(".product_name").removeClass('hide');
		  		}
		  		else if(value == "invoice_no") {
		  			$(".invoice_no").removeClass('hide');
		  		}
		  	});
		  	$("#item_multuselect").multiselect("refresh");
            $(".filtered_applied").removeClass('hide');
            $(".filter_file_name").text(report_name);
            $(".filter_user_name").text(json["owner"]);
            $("#deleteReportView").attr("disabled", false);
            if(json["is_myrecord"] != '1'){
			    $("#deleteReportView").attr("disabled", "disabled");
			    $("#deleteReportView").off("click");
			    $("#deleteReportView").attr("data-original-title", "You can't delete this report");
            }
        }
    });

	}

});

$("#saveModalReport").click(function(){
	$("#saveFileName").modal('show');
});

$("#deleteReportView").click(function(){
    var del_report_code =  $('#report_list').val();
    var del_report_name = $('#report_list option:selected').text();
    var del_report = "SALES";
    if (del_report_code != '0'){
    	swal({
		  	title: "Are you sure?",
		  	text: "Do you want to delete Report: <br><b>" + del_report_name + "</b>",
		  	type: "warning",
		  	showCancelButton: true,
		  	confirmButtonColor: "#209be1",
		  	confirmButtonText: "Yes, delete it!",
		  	closeOnConfirm: true
		},
		function(){
		  	$.ajax({
	            url: "/erp/reports/json/delete_report/",
	            type: "POST",
	            dataType: "json",
	            data: {code : del_report_code, report_code: del_report},
	            success: function (json) {
		            var report_name = $('#report_list option:selected').text()
		            if (json == "success"){
                        ga('send', 'event', 'Sales', 'Delete Custom Report', $('#enterprise_label').val(), 1);
		            	swal({
	                          title: "",
	                          text: "The report <b>" + report_name + "</b> has been deleted successfully.",
	                          type: "success",
	                          showCancelButton: false,
	                          confirmButtonColor: "#209be1",
	                          confirmButtonText: "OK",
	                          closeOnConfirm: true
	                    },
	                    function(){
	                          location.reload();
	                    });
	                }
	                else{
		            	swal({
	                          title: "",
	                          text: "The report <b>" + report_name + "</b> not deleted due to server error. Please try again.",
	                          type: "warning",
	                          showCancelButton: false,
	                          confirmButtonColor: "#209be1",
	                          confirmButtonText: "OK",
	                          closeOnConfirm: true
	                    },
	                    function(){
	                          location.reload();
	                    });
	                }
	            }
			});
		});
    }
    else{
        swal("","Please select a report from saved templates to delete.", "warning");
    }
});

$("#saveReport").click(function(){
	$(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();

    var ControlCollections = [
        {
            controltype: 'textbox',
            controlid: 'laterFileName',
            isrequired: true,
            errormsg: 'File name is required.'
        }
    ];
    var result = JSCustomValidator.JSvalidate(ControlCollections);
	if(result){
		var report_name = $("#laterFileName").val();
		var group = $('input[name=saveType]:checked').val();
		var type='';
		var from_date ='';
		var to_date ='';
		var report = "SALES";
		var adv_filter = '';
		if($("#select-date").is(":checked")){
			type = "date";
			from_date = moment($("#fromdate").val()).format('YYYY-MM-DD');
			to_date = moment($("#todate").val()).format('YYYY-MM-DD');
		}
		else if($("#select-month").is(":checked")){
			type = "month";
			from_date = moment($(".search_month_from").val(), 'MMM-YYYY').startOf('month').format('YYYY-MM-DD');
			to_date = moment($(".search_month_to").val(), 'MMM-YYYY').endOf('month').format('YYYY-MM-DD');
		}
		else if($("#select-year").is(":checked")){
			type = "year";
			from_date = moment($(".search_year_from").val()).startOf('year').format('YYYY-MM-DD');
			to_date = moment($(".search_year_to").val()).endOf('year').format('YYYY-MM-DD');
		}else{

		}
		if($("#adv_filter_data").val() != ''){
			adv_filter = $("#adv_filter_data").val();
		}

		if(typeof(report_name) != "undefined" && report_name !== null) {
		    var values = [];
			$('.li-all-others').find('input:checked').each(function(){
				values.push($(this).val());
			});
	     	$.ajax({
	            url: "/erp/reports/json/save_report/",
	            type: "POST",
	            dataType: "json",
	            data: {name : report_name,values: values, from: from_date, to: to_date, group: group, report: report, adv_filter: adv_filter},
	            success: function (json) {
	            	$("#saveFileName").modal('hide');
	            	if(json == "duplicate"){
		                swal({
	                          title: "Name already exist",
	                          text: "The report name <b>" + report_name + "</b> already exist.",
	                          type: "warning",
	                          showCancelButton: false,
	                          confirmButtonColor: "#209be1",
	                          confirmButtonText: "OK",
	                          closeOnConfirm: true
	                    });
	                    $('#laterFileName').val('');
	            	}else{
                        ga('send', 'event', 'Sales', 'Save Custom Report', $('#enterprise_label').val(), 1);
		                swal({
	                          title: "",
	                          text: "The report <b>" + report_name + "</b> has been saved successfully.",
	                          type: "success",
	                          showCancelButton: false,
	                          confirmButtonColor: "#209be1",
	                          confirmButtonText: "OK",
	                          closeOnConfirm: true
	                    },
	                    function(){
	                          location.reload();
	                    });
                    }
	            }
            });
		}
	}
});

function ConsolidatedSum(gross_val_sum) {

	var consolidate_fields = ["other_tax", "total_tax", "total_invoice", "total_cgst", "total_sgst", "total_igst", "gross_invoice_value"];
	var fields = [];
	var tdCreate = "<tr class='total_col hide'><td class='text-center'>TOTAL</td>";
	$('.li-all-others').find('input:checked').each(function(key){
		fields.push($(this).val());
		tdCreate += "<td class='text-right'></td>";
		if($('.li-all-others').find('input:checked').length == (key + 1)){
    		tdCreate += '<td style="min-width: 40px; visibility: hidden;""></td>';	
    	}
	});
	tdCreate += "</tr>";
	$(".cs_tfoot").html('').append(tdCreate);
	$.each(consolidate_fields, function( index, value ) {
		var sum = 0;
		var fieldIndex = fields.indexOf(value) + 2;
		$("."+value).each(function(){
			sum += Number($(this).text());
		});
		if(fieldIndex > 1 ) {
			$(".total_col").removeClass('hide');
			$(".cs_tfoot").find("td:nth-child("+fieldIndex+")").text(sum.toFixed(2));
		}
	});

	/* Month Wise */
	var fields_month = [];
	$("#tablesorter tbody").find('tr').each(function(){
		fields_month.push($(this).attr('class'));
	});
	var unique_field_month = unique(fields_month);
	//alert(unique_field_month);
}

function unique(list) {
  var result = [];
  $.each(list, function(i, e) {
    if ($.inArray(e, result) == -1) result.push(e);
  });
  return result;
}

var oTable;
var oSettings;

function TableHeaderFixed() {
    oTable = $('#tablesorter').DataTable({
    fixedHeader: true,
    "pageLength": 50,
    "search": {
        "smart": false
    }
    });
    oTable.on("draw",function() {
        var keyword = $('#tablesorter_filter > label:eq(0) > input').val();
        $('#tablesorter').unmark();
        $('#tablesorter').mark(keyword,{});
    });
    oTable.on('page.dt', function() {
      $('html, body').animate({
        scrollTop: $(".dataTables_wrapper").offset().top - 15
       }, 'slow');
    });
    oSettings = oTable.settings();
}

$('.nav-pills li').removeClass('active');
$("#li_custom_report").addClass('active');

$(".slide_container_part").removeClass('selected');
$("#menu_sales").addClass('selected');

function salesAdvancedFilter() {
	if($('.li-all-others').find('input:checked').length <= 0){
		swal("","Please select at-least one field to add Advance Filter","warning")
	}
	else {
		if($(".ignore-datepicker").text().trim() == "Ignore This") {
			$("#filter_date_range").html($("#reportrange").text().trim());
			$("#filter_date_range_container").removeClass("hide");
		}
		else {
			$("#filter_date_range").html("");
			$("#filter_date_range_container").addClass("hide");
		}
		isAdvFilterEdited = false;
		$("#filter_container").addClass("hide");
		$("#sales_advance_filter").modal("show");
		$(".adv_filter_list_container").html("");
		$("#adv_filter_value_1").val("");
		$("#adv_filter_value_1, #adv_filter_value_2").val("");
		$(".adv_value_2, .adv_value_3").addClass("hide");
		$(".adv_value_1").removeClass("hide");
		var option = "";
		$('.li-all-others').find('input:checked').each(function(index){
			var filterText = $(this).closest('a').text();
			option += '<span class="adv_filter_list" data-value='+$(this).val()+'><i class="fa fa-check hide" style="color: green;" aria-hidden="true"></i>'+$(this).closest('a').text()+'</span>';
		});
		$(".adv_filter_list_container").append(option);
		$("#adv_filter_table tr").each(function(){
			if($(this).find("td").text().indexOf("Consolidated by")<0) {
				$(".adv_filter_list_container").find("span[data-value='"+$(this).attr('data-field-value')+"']").addClass('selected');
			}
		})

		
		$(".adv_filter_condition div").addClass("hide");
		$(".adv_filter_condition div input").prop("checked", false);
		setTimeout(function(){
			$(".adv_filter_list_container").find(".adv_filter_list").each(function(){
				if(!$(this).hasClass("selected")) {
					$(this).trigger("click");
					return false;
				}
			});
		},300);

		$(".adv_filter_list").click(function(){
			$(".adv_filter_list").removeClass("active");
			$(this).addClass("active");
			$(".adv_filter_condition div").addClass("hide");
			$(".adv_filter_condition div input").prop("checked", false);
			$(".adv_filter_consolidated input").prop("checked", false);
			var dataFor = $("#item_multuselect option[value='"+$(this).attr("data-value")+"']").attr("data_for");
			$(".adv_filter_consolidated").addClass("hide");
			if(dataFor == "string") {
				disableFilterDate();
				$(".adv_filter_condition div.for_string").removeClass("hide");
			}
			else if(dataFor == "numeric") {
				disableFilterDate();
				$(".adv_filter_condition div.for_numeric").removeClass("hide");
			}
			else if(dataFor == "date") {
				enableFilterDate();
				$(".adv_filter_condition div.for_dates").removeClass("hide");
				$(".adv_filter_consolidated").removeClass("hide");
				var consolidate_val = $(".tr_consolidated_row[data-field-value='"+$(this).attr('data-value')+"']").find(".tr_consolidated_name").text();
				if(consolidate_val != "") {
					$(".adv_filter_consolidated").find("input[value='"+consolidate_val+"']").prop("checked", true);
				}
				else {
					$(".adv_filter_consolidated").find("input[value='none']").prop("checked", true);
				}
			}
			$("#filter_container").removeClass("hide")
			$("#adv_filter_value_1").val("");
			$("#adv_filter_value_2").val("");
		});

		$(".adv_filter_condition input").change(function(){
			$("#adv_filter_value_1").removeAttr("disabled");
			if($(this).val() == "between") {
				var currentSelected = $(".adv_filter_list.active");
				var dataFor = $("#item_multuselect option[value='"+currentSelected.attr("data-value")+"']").attr("data_for");
				if(dataFor == "date") {
					$(".adv_value_2").removeClass("hide");
					$(".adv_value_1, .adv_value_3").addClass("hide");
				}
				else {
					$(".adv_value_2").addClass("hide");
					$(".adv_value_1, .adv_value_3").removeClass("hide");
				}
			}
			else {
				$(".adv_value_2, .adv_value_3").addClass("hide");
				$(".adv_value_1").removeClass("hide");
			}
		});
	}
}

function enableFilterDate(){
	$("#adv_filter_value_1").datepicker({
		format: "M d, yyyy",
		autoclose: true,
		orientation: 'bottom auto'
	}).css({paddingLeft: "50px"});;
	$("#sales_advance_filter").find(".glyphicon-calendar").removeClass("hide");
}

function disableFilterDate(){
	$("#adv_filter_value_1").datepicker('remove').css({paddingLeft: "6px"});
	$("#sales_advance_filter").find(".glyphicon-calendar").addClass("hide");
}

$("input[name='adv_consolidate_selector']").change(function(){
	var fieldName = $(".adv_filter_list_container").find(".adv_filter_list.active").text();
	var fieldValue = $(".adv_filter_list_container").find(".adv_filter_list.active").attr('data-value');
	var consolidated_val = $("input[name='adv_consolidate_selector']:checked").val();
	if(consolidated_val == "none") {
		$(".tr_consolidated_row[data-field-value='"+fieldValue+"']").remove();
	}
	else {
		if($(".tr_consolidated_row[data-field-value='"+fieldValue+"']").length > 0) {
			$(".tr_consolidated_row[data-field-value='"+fieldValue+"']").find(".tr_consolidated_name").text(consolidated_val);
			$(".tr_consolidated_row[data-field-value='"+fieldValue+"']").find(".tr_condition_val").text(consolidated_val);
		}
		else {
			var filterText = "<tr data-field-value= "+fieldValue+" class='tr_consolidated_row'><td>Consolidated by <b class='tr_field_name'>"+ fieldName +" </b> (<b class='tr_consolidated_name'>"+consolidated_val+"</b>)<span class='tr_condition_val hide'>"+consolidated_val+"</span></td><td class='text-center'><i class='fa fa-times' aria-hidden='true' role='button' onclick='removeAdvFilter(this);'></i></tr>";
			$("#adv_filter_table").append(filterText);
		}
	}
	isAdvFilterEdited = true;
});

function addAdvFilter() {
	var currentSelected = $(".adv_filter_list.active");
	var dataFor = $("#item_multuselect option[value='"+currentSelected.attr("data-value")+"']").attr("data_for");
	var conditionName = $(".adv_filter_condition input[name='adv_condition_selector']:checked").next('label').text();
	
	$(".error-border").removeClass('error-border');
	$(".custom-error-message").remove();
	if(conditionName == "Between" && dataFor == "date") {
		var result = true;
	}
	else {
		var ControlCollections = [
	        {
	            controltype: 'dropdown',
	            controlid: 'adv_filter_value_1',
	            isrequired: true,
	            errormsg: ''
	        }
	    ];
	    if(conditionName == "Between" && dataFor == "numeric") {
		    var control = {
		        controltype: 'dropdown',
		        controlid: 'adv_filter_value_2',
		        isrequired: true,
		        errormsg: ''
		    };
		    ControlCollections[ControlCollections.length] = control;
		}
	    var result = JSCustomValidator.JSvalidate(ControlCollections);
	}
    if(result) {
		var fieldName = $(".adv_filter_list_container").find(".adv_filter_list.active").text();
		var fieldValue = $(".adv_filter_list_container").find(".adv_filter_list.active").attr('data-value');
		var fieldType = $("#item_multuselect option[value='"+fieldValue+"']").attr("data_for");
		
		var conditionVal = $(".adv_filter_condition input[name='adv_condition_selector']:checked").val();
		if(conditionName == "Between") {
			var currentSelected = $(".adv_filter_list.active");
			var dataFor = $("#item_multuselect option[value='"+currentSelected.attr("data-value")+"']").attr("data_for");
			if(dataFor == "date") {
				var datevalue = $("#reportrange_1").text().trim().split("-");
				var value1 = datevalue[0].trim();
				var value2 = datevalue[1].trim();
			}
			else {
				var value1 = $("#adv_filter_value_1").val();
				var value2 = $("#adv_filter_value_2").val();	
			}
		}
		else {
			var value1 = $("#adv_filter_value_1").val();	
			var value2 = "";
		}
		if($(".tr_condition_row[data-field-value='"+fieldValue+"']").length > 0) {
			swal("","Filter for this field is already added. Please remove existing filter to add new one.", "warning");
			return;
		}

		if(fieldName.trim() !="" && conditionName.trim() !="" && value1.trim() !="") {
			if(conditionName == "Between") {
				var filterText = "<tr data-field-value= "+fieldValue+" class='tr_condition_row'><td><b class='tr_field_name'>"+ fieldName +"</b> <span class='tr_condition_val hide'>"+conditionVal+"</span> <span class='tr_condition_name'>"+conditionName.toLowerCase()+"</span> <b class='tr_value1_name'>"+value1+"</b> and <b class='tr_value2_name'>"+value2+"</b></td><td class='text-center'><i class='fa fa-times' aria-hidden='true' role='button' onclick='removeAdvFilter(this);'></i></tr>";
			}
			else {
				var filterText = "<tr data-field-value= "+fieldValue+" class='tr_condition_row'><td><b class='tr_field_name'>"+ fieldName +"</b> <span class='tr_condition_val hide'>"+conditionVal+"</span> <span class='tr_condition_name'>"+conditionName.toLowerCase()+"</span> <b class='tr_value1_name'>"+value1+"</b><b class='tr_value2_name'>"+value2+"</b></td><td class='text-center'><i class='fa fa-times' aria-hidden='true' role='button' onclick='removeAdvFilter(this);'></i></tr>";
			}
			$("#adv_filter_table").append(filterText);

			$(".adv_filter_list_container").find(".adv_filter_list.active").removeClass('active').addClass("selected");
			$(".adv_filter_condition input, .adv_filter_consolidated input").prop("checked", false);
			$(".adv_filter_consolidated input#adv_consolidated_none").prop("checked", true);
			$("#adv_filter_value_1, #adv_filter_value_2").val("");
			$("#filter_container").addClass("hide");
			isAdvFilterEdited = true;
			$(".adv_filter_list_container").find(".adv_filter_list").each(function(){
				if(!$(this).hasClass("selected")) {
					$(this).trigger("click");
					return false;
				}
			});
			$(".adv_value_2, .adv_value_3").addClass("hide");
			$(".adv_value_1").removeClass("hide");
		}
		else {
			swal("","Please select all parameters to create a filter option","warning");
		}
	}
}

function removeAdvFilter(current){
	var currentTR = $(current).closest("tr");
	if(!currentTR.hasClass('tr_consolidated_row')) {
		var delValue = currentTR.attr('data-field-value');
		$(".adv_filter_list_container").find("span[data-value='"+delValue+"']").removeClass("selected");
	}
	currentTR.remove();
	isAdvFilterEdited = true;
}

function applyAdvFilter() {
	var filterCount = $("#adv_filter_table tr").length;
	if(filterCount <= 0) {
		$("#lbl_adv_filter").text('Advance Filter');
	}
	else {
		$("#lbl_adv_filter").text(filterCount+" filter(s) applied");
	}
	$("#sales_advance_filter").modal("hide");
	if($("#adv_filter_table tr").length > 0){
		var advFilterArray = [];

		$("#adv_filter_table tr").each(function(){
			var dataVal = $(this).attr("data-field-value");
			var dataFor = $("#item_multuselect option[value='"+dataVal+"']").attr("data_for");
			var conType = $(this).find(".tr_condition_val").text();
			if(dataFor == "date") {
				var valText1 = moment($(this).find(".tr_value1_name").text()).format("YYYY-MM-DD");
				if($(this).find(".tr_value2_name").text() != "")
				var valText2 = moment($(this).find(".tr_value2_name").text()).format("YYYY-MM-DD");
				else
				var valText2 = "";
			}
			else {
				var valText1 = $(this).find(".tr_value1_name").text();
				var valText2 = $(this).find(".tr_value2_name").text();
			}
			var advFilterSubArray = [];
			advFilterSubArray.push(valText1,valText2);
			advFilterArray.push({
				fieldName: dataVal,
				conditionName: conType,
				valueName: advFilterSubArray
			});
		});
	$("#adv_filter_data").val(JSON.stringify(advFilterArray))
		customSalesReportViewSubmit(advFilterArray);
	}else{
	$("#adv_filter_data").val(JSON.stringify(advFilterArray))
		customSalesReportViewSubmit(advFilterArray);
	}
	isAdvFilterEdited = false;
	$(".apply_fail_text").addClass("hide");
}

function advFilterLoadOnEdit(){
	$("#adv_filter_table").html("");
	var data = $("#adv_filter_data").val();
	if(JSON.parse(data).length > 0) {
		$("#lbl_adv_filter").text(JSON.parse(data).length+" filter(s) applied");
		$.each(JSON.parse(data), function(i, item) {
		    var setFieldName = $("#item_multuselect").find("option[value='"+item.fieldName+"']").text();
		    var setConditionalName = $(".adv_filter_condition").find("input[value='"+item.conditionName+"']").next("label").text();
		    if(setConditionalName == "Between") {
				var filterText = "<tr data-field-value= "+item.fieldName+" class='tr_condition_row'><td><b class='tr_field_name'>"+ setFieldName +"</b>  <span class='tr_condition_val hide'>"+item.conditionName+"</span> <span class='tr_condition_name'>"+setConditionalName.toLowerCase()+"</span> '<b class='tr_value1_name'>"+item.valueName[0]+"</b>' and '<b class='tr_value2_name'>"+item.valueName[1]+"</b>'</td><td class='text-center'><i class='fa fa-times' aria-hidden='true' role='button' onclick='removeAdvFilter(this);'></i></tr>";
			}
			else {
				var filterText = "<tr data-field-value= "+item.fieldName+" class='tr_condition_row'><td><b class='tr_field_name'>"+ setFieldName +"</b>  <span class='tr_condition_val hide'>"+item.conditionName+"</span> <span class='tr_condition_name'>"+setConditionalName.toLowerCase()+"</span> '<b class='tr_value1_name'>"+item.valueName[0]+"</b>'<b class='tr_value2_name'>"+item.valueName[1]+"</b></td><td class='text-center'><i class='fa fa-times' aria-hidden='true' role='button' onclick='removeAdvFilter(this);'></i></tr>";
			}
			
			$("#adv_filter_table").append(filterText);
		})
	}
	else {
		$("#lbl_adv_filter").text('Advance Filter');
	}
}

$("#exportAsCSV").click(function(){
	if(!$(this).attr('disabled')){
		if ($('#report_tbl_body').text() != "") {
			GeneralExportTableToCSV.apply(this, [$('#tablesorter'), 'Custom_Sales_Report.csv']);
		} else {
			swal("No Record Found","Sorry!!! No record found to download.","warning");
		}
	}
});

function ignoreDateRangePicker(){
	if($("#reportrange").hasClass("div-disable")) {
		$("#reportrange").removeClass("div-disable");
		$(".ignore-datepicker").text("Ignore This")
	}
	else {
		$("#reportrange").addClass("div-disable")
		$(".ignore-datepicker").text("Enable This")
	}
	isAdvFilterEdited = true;
}
</script>
{% endblock %}