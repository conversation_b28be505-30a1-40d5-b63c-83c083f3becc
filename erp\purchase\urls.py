"""
"""
from django.conf.urls import patterns, url

from erp.purchase import json_api
from erp.purchase.reports import generatePurchaseMasterReport, generateMaterialWisePOReport
from erp.purchase.views import loadPO, loadPOMaterials, renderDashboard, renderPurchaseHome, savePO, managePO, editPO, \
	loadPOHeader, approve, reject, loadTransportDetails, loadPackingDetails, loadPOTaxes, loadIndentDetails, \
	review, draft, countMaterialsReceivedForPO, loadTaxDetails, \
	loadTagDetails, loadPOTags, loadIndentTags, loadGSTTaxDetails, loadJobPrice, loadTax, loadPartyCurrency, \
	loadPOTotal, loadPOByDue, loadRecentlyReceivedGRNByPO, loadRecentlyOverDuePO, loadRecentBadSuppliers, \
	loadPurchasePerformance, loadRemarks, loadJobOrderList, manageJO, getPOLogData, \
	getPOLogList, getDcMaterials, saveReturnQty, mask_po, renderProjectHome, renderProjectForeCast,\
	get_gst_wise_pending_po

__author__ = 'kalaivanan'

urlpatterns = patterns(
	'',
	url('home/$', renderDashboard),
	url('po/$', managePO),
	url('jo/$', manageJO),
	url('po_list/$', renderPurchaseHome),
	url('jo_list/$', loadJobOrderList),
	url('po/loadinddetails/$', loadIndentDetails),
	url('po/reports/$', generatePurchaseMasterReport),
	url('po/materialwisereports/$', generateMaterialWisePOReport),
	url('json/po/load_job_price/$', loadJobPrice),
	url('json/po/edit/$', editPO),
	url('json/po/save/$', savePO),
	url('json/po/loadpackdetails/$', loadPackingDetails),
	url('json/po/loadtransportdetails/$', loadTransportDetails),
	url('json/po/loadpos/$', loadPO),
	url('json/po/load_po_taxes/$', loadPOTaxes),
	url('json/po/loadpoheader/$', loadPOHeader),
	url('json/po/load_ind_tags/$', loadIndentTags),
	url('json/po/review/$', review),
	url('json/po/mask/$', mask_po),
	url('json/po/reject/$', reject),
	url('json/po/checkpogrn/$', countMaterialsReceivedForPO),
	url('json/po/approve/$', approve),
	url('json/po/draft/$', draft),
	url('json/po/loadtags/$', loadTagDetails),
	url('json/po/loadtaxdetails/$', loadTaxDetails),
	url('json/po/load_po_tags/$', loadPOTags),
	url('json/po/loadpomaterial/$', loadPOMaterials),
	url('json/po/load_gst_tax/$', loadGSTTaxDetails),
	url('json/po/get_po_total/$', loadPOTotal),
	url('json/po/get_po_delivery_due/$', loadPOByDue),
	url('json/po/get_received_items/$', loadRecentlyReceivedGRNByPO),
	url('json/po/get_overdue_items/$', loadRecentlyOverDuePO),
	url('json/po/get_bad_suppliers/$', loadRecentBadSuppliers),
	url('json/po/get_purchase_performance/$', loadPurchasePerformance),
	url('json/po/remarks/$', loadRemarks),
	url('json/purchaseLatestUsedSupplierDetails/$', json_api.latestUsedSupplierDetailsPurchase),

	url('json/po/getpologlist/$', getPOLogList),
	url('json/po/getpologdata/$', getPOLogData),
	url('json/po/get_dc_materials/$', getDcMaterials),
	url('json/po/save_return_qty/$', saveReturnQty),

	# Purchase API Service
	url('json/dashboard/', json_api.getDashboardData),
	url('json/purchasePerformance/', json_api.purchasePerformance),
	url('json/finance_year/', json_api.getPoFinanceYear),
	url('json/poSearch/', json_api.searchPO),
	url('json/po_draft/', json_api.fetchDraftPO),
	url('json/poDraftDetails/', json_api.poDraft),
	url('json/poMaterialDetail/', json_api.poMaterialDetails),
	url('json/poMaterial_overDue/', json_api.poMaterialOverDue),
	url('json/po_doc/', json_api.downloadPODocument),
	url('json/po/add_tax/$', loadTax),
	url('json/po/load_party_currency/$', loadPartyCurrency),
	url('json/super_edit_po_code/', json_api.superEditPOCode),
	url('json/get_po_linked_message/', json_api.getPOLinkedMessage),
	url('project_list/$', renderProjectHome),
	url('project_fore_cast/$', renderProjectForeCast),

	url('json/po/pending_po_report/$', get_gst_wise_pending_po),
)
