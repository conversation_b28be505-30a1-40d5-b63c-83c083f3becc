<style>
	.oa_template_view .table.row-seperator th,
	.oa_template_view .table.row-seperator td,
	.oa_template_view .table.row-seperator th,
	.oa_template_view .table.row-seperator td {
	    border-top: 1px solid #000 !important;
	    border-bottom: 1px solid #000 !important;
	}

	.oa_template_view .table.column-seperator th,
	.oa_template_view .table.column-seperator td,
	.oa_template_view .table.column-seperator th,
	.oa_template_view .table.column-seperator td {
	    border-right: 1px solid #000 !important;
	    border-left: 1px solid #000 !important;
	}
</style>
<div>
    <atable class="table  item_table table-bordered row-seperator column-seperator" id="item_table" style="font-size:11px;">
         <athead>
            <atr class="row_seperator column_seperator header_shading">
                <ath class="text-center td_sno td_sno_text" rowspan="2" scope="col" style="width: 6%;">S.no</ath>
                <ath class="text-center td_description td_description_text" rowspan="2" style="width:30%;">Description</ath>
                <ath class="text-center pdf_item_hsn_code_txt td_hsn_code" rowspan="2">HSN/SAC</ath>
                <ath class="text-center td_qty td_qty_text" rowspan="2" scope="col">Qty</ath>
                <ath class="text-center td_uom td_uom_text" rowspan="2" scope="col">UOM</ath>
                <ath class="text-center td_price td_price_text" rowspan="2" scope="col">Price<br>USD</ath>
                <ath class="text-center td_disc td_disc_text" rowspan="2" scope="col">Disc<br>%</ath>
                <ath class="text-center td_tax td_tax_text" rowspan="2" scope="col">Total<br>USD</ath>
            </atr>
        </athead>
        <atbody>
            [% for material in oa_item_details %]
                [%  if forloop.counter|divisibleby:2 %]
                   <atr class="row_seperator column_seperator row_shading">
                [% else %]
                    <atr class="row_seperator column_seperator row_shading" style="background: #ffffff;">
                [% endif %]
                    <atd class="text-center td_sno">{[forloop.counter]}</atd>
                    <atd>
                        <span class="pdf_item_name"><span class="pdf_item_name_txt"></span> {[ material.material_name ]}<br></span>
                        [% if material.material_drawing_no %]
                            <span class="pdf_item_drawing_number">
                                <span class="pdf_item_drawing_number_txt"></span>
                                {[ material.material_drawing_no ]}<br />
                            </span>
                        [% endif %]
<!--                        [% if item_res.item_table.item_details.make.print and item_res.item_table.item_details.part_no.print %]-->
<!--                            [% if material.material_make and material.material_make != "" %]-->
<!--                                <span class="pdf_item_make">~lsqb;-->
<!--                                    <i class="pdf_item_make_txt"></i>{[ material.material_make ]}-->
<!--                                </span>-->
<!--                                [% if material.part_no and material.part_no != "" %]-->
<!--                                    <span class="pdf_item_part">-->
<!--                                        <i class="pdf_item_part_txt"></i>-->
<!--                                    </span>-->
<!--                                    <span>{[ material.part_no ]} ~rsqb;<br></span>-->
<!--                                [% else %]-->
<!--                                     ~rsqb;<br>-->
<!--                                [% endif %]-->
<!--                            [% endif %]-->
<!--                        [% elif item_res.item_table.item_details.make.print or item_res.item_table.item_details.part_no.print %]-->
<!--                            [% if item_res.item_table.item_details.make.print %]-->
<!--                                [% if material.material_make and material.material_make != "" %]-->
<!--                                    <span class="pdf_item_make">~lsqb;-->
<!--                                        <i class="pdf_item_make_txt"></i>{[ material.material_make ]} ~rsqb;<br>-->
<!--                                    </span>-->
<!--                                [% endif %]-->
<!--                            [% else %]-->
<!--                                [% if material.part_no and material.part_no != "" %]-->
<!--                                    <span class="pdf_item_part">~lsqb;-->
<!--                                        <i class="pdf_item_part_txt"></i>-->
<!--                                    </span>-->
<!--                                    <span>{[ material.part_no ]} ~rsqb;<br></span>-->
<!--                                [% endif %]-->
<!--                            [% endif %]-->
<!--                        [% endif %]-->
                        [% if material.hsn_code %]
                            <span class="pdf_item_hsn_code"><i class="pdf_item_hsn_code_txt"></i> {[ material.hsn_code ]}<br /></span>
                        [% endif %]
                        [% if material.material_description %]
                            <span class="pdf_item_desc"><span class="pdf_item_desc_txt"></span>{[ material.material_description ]}<br></span>
                        [% endif %]
                        <span class="tax_in_description hide">~lsqb;CGST @ {[ material.cgst_rate ]}: {[ material.cgst_value ]} - SGST @ {[ material.sgst_rate ]}: {[ material.sgst_value ]} - IGST @ {[ material.igst_rate ]}: {[ material.igst_value ]}~rsqb;</span>
                    </atd>
                    <atd class="text-center td_hsn_code">{[ material.hsn_code ]}</atd>
                    <atd class="text-right td_qty">{[ material.material_quantity|floatformat:2 ]}<br /><span class="pdf_unit_in_price hide">({[ material.material_unit ]})</span></atd>
                    <atd class="text-center td_uom">{[ material.material_unit ]}</atd>
                    <atd class="text-right td_price">{[ material.material_rate|floatformat:2 ]}</atd>
                    <atd class="text-right td_disc">{[ material.material_discount ]}</atd>
                    <atd class="text-right td_tax">{[ material.material_taxable_value ]}</atd>
                </atr>
            [% endfor %]
        </atbody>
        <atfoot>
            <atr class="row_seperator column_seperator total_section sub_total_section">
                <atd colspan="3" class="text-right total_section_1" style="font-size: 12px; line-height: 12px;"><b>Sub-Total</b></atd>
                <atd class="total_section_2" style="font-size: 12px; line-height: 12px;">{[ total_quantity ]}</atd>
                <atd colspan="4" class="text-right total_section_3" style="font-size: 12px; line-height: 12px;">{[ total_value | floatformat:2 ]}</atd>
            </atr>
            [% for tax in oa_taxes %]
                <atr style="[% if misc_res.print_summary_first_page and appendix_pages > 0 %]display:none;[% endif %]" class="row_seperator column_seperator other_tax_column for-non-pp">
                    <atd colspan="3" class="text-right total_section_1">{[ tax.tax_name ]} @ {[ tax.tax_rate ]}%</atd>
                    <atd colspan="5" class="text-right total_section_3">{[ tax.tax_value ]}</atd>
                </atr>
            [% endfor %]
            <atr  style="[% if misc_res.print_summary_first_page and appendix_pages > 0 %]display:none;[% endif %]" class="row_seperator column_seperator total_section">
                <atd colspan="3" class="text-right total_section_1"><b>Grand Total</b><span class="total_inclusice_tax hide"> (inclusive tax)</span></atd>
                <atd colspan="5" class="text-right total_section_3">{[ source.currency.code ]} <b>{[ source.grand_total ]}</b></atd>
            </atr>
            <atr style="[% if misc_res.print_summary_first_page and appendix_pages > 0 %]display:none;[% endif %]" class="tr_total_in_words row_seperator show_total_in_words total_in_words">
                <atd colspan="8" class="full-length-td"><b>Total Value ({[ source.currency.code ]}):</b> {[ total_in_words|upper ]}</atd>
            </atr>
            [% if header_res.field_name.special_instructions.print %]
                [% if special_instructions %]
                    <atr class="oa_special_instruction">
                        <atd colspan="8" class="full-length-td"><b class="oa_special_instruction_txt">{[ header_res.field_name.special_instructions.label ]}</b>:  {[ special_instructions ]}</atd>
                    </atr>
                [% endif %]
            [% endif %]
            [% if source.notes != "" %]
                <atr>
                    <atd class="full-length-td">
                        <strong>Notes:</strong>
                         [% autoescape off %] {[ misc_res.notes ]} [% endautoescape %]
                    </atd>
                </atr>
            [% endif %]
        </atfoot>
    </atable>
</div>