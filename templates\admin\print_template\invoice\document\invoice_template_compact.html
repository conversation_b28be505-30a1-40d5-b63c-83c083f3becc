<!DOCTYPE html>
<style type="text/css">
	.inv_pdf_editor {
		box-shadow: 0 0;
		font-family: pdf_{{general_res.base_font}};
	}

	.pdf_company_name {
		font-size: {{header_res.name_font_size}}pt;
		font-family: pdf_{{header_res.name_font}};
	}

	.pdf_form_name {
		font-size: {{header_res.form_name_font_size}}pt;
	}

	.pdf_company_address_container,
	.pdf_company_details_container,
	.pdf_invoice_prefix,
	.pdf_issue_date_time{
		font-size: {{header_res.font_size}}pt;
	}

	.pdf_invoice_no{
		font-size: {{header_res.invoice_number_font_size}}pt;
	}

	.table>tbody>tr.item_empty_row>td {
		padding: 0 !important;
		line-height: 0 !important;
	}

	.item_table thead,
	.item_table tbody {
		font-size: {{item_res.font_size}}pt;
	}

	{% if item_res.include_alternate_row_shading %}
		.item_table thead tr,
		.hsn_table thead tr {
			background: rgba(0,0,0,0.2) !important;
		}

		.item_table tbody tr:nth-child(even),
		.hsn_table tbody tr:nth-child(even) {
			background: rgba(0,0,0,0.1);
		}

		.item_table tbody tr:nth-child(odd),
		.hsn_table tbody tr:nth-child(odd) {
			background: transparent;
		}
	{% endif %}

	.total_section_4 {
		display: none;
		{% if item_res.include_tax %}
			{% if item_res.tax_type == 1 or item_res.tax_type == 4 %}
				{% if item_res.include_taxrate or item_res.include_taxamount %}
					display: table-cell !important;
				{% endif %}
			{% endif %}
		{% endif %}
	}

	.item_table tfoot {
		font-size: {{summary_res.font_size}}pt;
	}

	{% if misc_res.include_first_page_summary %}
		{% if item_rows.item_count > 6 %}
			.item_empty_row {
				display: none;
			}
			.summary_table {
				display: table-row !important;
			}

			.for_single_page {
				display: none;
			}

			.for_summary_enabled {
				display: none !important;
			}

			.hsn_summary_container_1 {
				display: inherit !important;
			}

			.hsn_summary_container_2 {
				display: none;
			}

			.inv_pdf_editor {
				float: none !important;
			}
			{% if not summary_res.include_hsn_summary %}
				{% if item_rows.annexure_count <= 3 %}
					.annexure_height {
						height: 360px !important;
					}
				{% elif item_rows.annexure_count <= 6 %}
					.annexure_height {
						height: 145px !important;
					}
				{% else %}
					.annexure_height {
						height: 65px !important;
					}
				{% endif %}
			{% else %}
				{% if item_column_span.hsn_summary_count < 3 %}
					.annexure_height {
						height: 180px !important;
					}				
				{% elif item_column_span.hsn_summary_count < 8 %}
					.annexure_height {
						height: 100px !important;
					}
				{% else %}
					.annexure_height {
						height: 65px !important;
					}
				{% endif %}
			{% endif %}
		{% else %}
			{% if not summary_res.include_hsn_summary %}
				{% if item_rows.annexure_count <= 3 and item_rows.item_count <= 3 %}
					.item_empty_row {
						height: {{item_res.item_table_space|add:item_res.item_table_space}}px !important;
					}
				{% elif item_rows.annexure_count <= 3 and item_rows.item_count < 6 %}
					.item_empty_row {
						height: {{item_res.item_table_space}}px !important;
					}
				{% else %}
					.item_empty_row {
						display: none;
					}
				{% endif %}
			{% else %}
				{% if item_column_span.hsn_summary_count <= 3 and item_rows.item_count < 3 %}
					.item_empty_row {
						height: {{item_res.item_table_space|add:item_res.item_table_space}}px !important;
					}
				{% elif item_column_span.hsn_summary_count < 3 and item_rows.item_count < 6 %}
					.item_empty_row {
						height: {{item_res.item_table_space}}px !important;
					}
				{% else %}
					.item_empty_row {
						display: none;
					}
				{% endif %}
	.		{% endif %}
		{% endif %}
	{% else %}
		{% if not summary_res.include_hsn_summary %}
			{% if item_rows.annexure_count <= 3 and item_rows.item_count <= 3 %}
				.item_empty_row {
					height: {{item_res.item_table_space|add:item_res.item_table_space}}px !important;
				}
			{% elif item_rows.annexure_count <= 3 and item_rows.item_count < 6 %}
				.item_empty_row {
					height: {{item_res.item_table_space}}px !important;
				}
			{% else %}
				.item_empty_row {
					display: none;
				}
			{% endif %}
		{% else %}
			{% if item_column_span.hsn_summary_count <= 3 and item_rows.item_count < 3 %}
				.item_empty_row {
					height: {{item_res.item_table_space|add:item_res.item_table_space}}px !important;
				}
			{% elif item_column_span.hsn_summary_count < 6 and item_rows.item_count < 6 %}
				.item_empty_row {
					height: {{item_res.item_table_space}}px !important;
				}
			{% else %}
				.item_empty_row {
					display: none;
				}
			{% endif %}
		{% endif %}
	{% endif %}

	.pdf_company_details_container-irn .pdf_others_details {
		width: 150px;
	}
    .divTable {
        display:  table;
        width:auto;
    }

    .divRow {
       display:table-row;
       width:auto;
    }

    .divCell{
        float:left;
        display:table-column;
        width:65px;
        font-size: 14px;
    }

    .divCell-small {
    	float:left;
        display:table-column;
        width:10px;
        font-size: 14px;
    }

    .divCell-full {
    	float:left;
        display:table-column;
        font-size: 14px;
        word-break: break-word;
        width: calc(100% - 75px);
    }
    .pdf_others_details {
		width: 85px;
		display: inline-block;
		word-break: break-all;
		vertical-align:top;
	}
	invoice_other_details_list {
		width: 50px;
		font-weight: bold;
		display: inline-table;
		word-break: break-all;
		float:left;
	}
	.invoice_other_details_data {
		 width: 150px;
		 display: inline-table;
		 word-break: break-all;
		 vertical-align:top;
	}
</style>

<body {% if doc_status|lower == "preview" %} class="watermark preview_watermark" {% elif doc_status|lower == "cancelled" %} class="watermark cancelled_watermark" {% endif %}>
	<div class="inv_pdf_editor invoice_template_default" style="border-color: transparent; width: 100%">
		<div class="pdf_header">
			<div class="row">
				<div class="header-banner-container col-sm-12">
					<div class="col-sm-4 text-left" style="padding-left: 0;">
						<img class="image_uploader-left img-responsive" src="{{header_left_image}}" style="height:{{header_image_size}}px;">
					</div>
					<div class="col-sm-4 text-center">
						<img class="image_uploader-center img-responsive" src="{{header_center_image}}" style="margin:0 auto;height:{{header_image_size}}px;" >
					</div>
					<div class="col-sm-4 text-right" style="padding-right: 0;">
						<img class="image_uploader-right img-responsive" src="{{header_right_image}}" style="float: right;height:{{header_image_size}}px;">
					</div>
				</div>
				<div class="col-sm-5">
					{% if header_res.include_logo %}
						<img src="{{enterprise_logo}}" class="pdf_company_logo" style="height: {{header_res.logo_size}}"><br>
					{% endif %}
					{% if header_res.include_name %}
						<span class="pdf_company_name" style="display: inline-block; padding: 3px 0;"><b>{{enterprise.name}}</b></span>
					{% endif %}
				</div>
				<div class="col-sm-7 text-right">
					<div class="col-sm-12" style="margin-top: 15px;">
						{% if scan_code_option == 2 %}
							<span><img src="{{scanning_code}}" style="width:100px; height:100px;margin-top:5px;"></span><br>
						{% elif scan_code_option == 3 %}
							<span><img src="{{scanning_code}}" style="width:195px;;height:40px;margin-top:5px;"></span><br>
						{%endif%}
						<span class="pdf_form_name text-right" style="padding-left: 0; padding-right: 3px; width: 60%;"><b class="pdf_form_name_text">{{ form_name }}</b></span>
						<span class="pdf_invoice_no text-left" style="padding: 0; width: 40%;">
							{% if source.type == "DC" or source.type == "JDC" or source.type == "JIN" %}
								<b class="pdf_invoice_prefix">{{ header_res.dcno_label }}</b>
							{% elif source.type != "Issue" %}
								<b class="pdf_invoice_prefix">{{ header_res.invoiceno_label }}</b>
							{% endif %}
							 <b class="pdf_invoice_number" style=" word-wrap: break-word;">{{ invoice_no }}</b></span><br />
					</div>
					<div class="col-sm-12 text-right" style="margin-top: 12px;">
						<span class="pdf_issue_date_time">
							<span class="pdf_invoice_datetime">{{ header_res.invoicedate_label }}</span>:
							<b><span class="issue_date_time">{% if issued_date %} {{ issued_date }} {% endif %}</span></b>
						</span>
					</div>
				</div>
				<div class="clearfix"></div>
				{% if source.type != "Issue"%}
					<div class="col-sm-12 pdf_complete_address">
						<hr style="margin:0;border: 1pt solid #666; " />
					</div>
					<div class="col-sm-8 pdf_company_address_container" style="padding: 3px 15px;">
						{% if header_res.include_address %}
							<span class="pdf_company_address">
								{{ enterprise.address_1 }}, {% if enterprise.address_2 and enterprise.address_2 != "" %}{{ enterprise.address_2 }}, {% endif %}<BR/>{{ enterprise.city }} - {{ enterprise.pin_code }}, {{ enterprise.state }},
								{% for country in country_list %}
									{% if country.country_code == enterprise.country_code %}
										{{ country.country_name|upper }}
									{% endif %}
								{% endfor %}<br>
							</span>
						{% endif %}
						{% for contact_detail in enterprise_contact_detail %}
							{% if header_res.include_phone_no %}
								<span class="pdf_company_contact">
									<b>Ph:</b> {{ contact_detail.phone_no }}
								</span>
							{% endif %}
							{% if header_res.include_fax %}
								<span class="pdf_company_fax">
									<b>Fax:</b>{{ contact_detail.fax_no }}
								</span>
							{% endif %}
							{% if header_res.include_email %}
								<span class="pdf_company_email">
									<br /><b>E-mail:</b>{{ contact_detail.email }}
								</span>
							{% endif %}
						{% endfor %}
					</div>
					<div class="col-sm-4 pdf_company_address_container" style="padding: 3px 15px;">
						{% for reg_detail in enterprise_reg %}
							<span class="pdf_cin_number"><b class="pdf_enterprise_details">{{ reg_detail.label }}</b>: <span>{{ reg_detail.details }}</span><br /></span>
						{% endfor %}
						{% if source.type != "DC" and source.type != "JDC" and source.type != "JIN" and source.type != "Issue"%}
							{% if item_res.tax_payable_on_reverse_charge == 1 or item_res.tax_payable_on_reverse_charge == "1"%}
								<div class="pdf_tax_payable">TAX PAYABLE ON REVERSE CHARGE: {% if source.tax_payable_on_reverse_charge %}Yes{% else %}No{% endif %}</div>
							{% else %}
								{% if source.tax_payable_on_reverse_charge %}
									<div class="pdf_tax_payable">TAX PAYABLE ON REVERSE CHARGE: Yes</div>
								{% endif %}
							{% endif %}
						{% endif %}
					</div>
					<div class="clearfix"></div>
					<div class="col-sm-12">
						<hr style="margin:0;border: 1pt solid #666; " />
					</div>
					<div class="col-sm-8 remove-padding">
						{% if header_res.include_billingaddress %}
							<div class="col-sm-6 pdf_bill_to_address">
								<div class="pdf_company_details_container" style="padding: 4px;">
									<span style="word-wrap: break-word;">
										{% if header_res.billingaddress_label != "" %}
											<b class="bill_to_text">{{ header_res.billingaddress_label }}</b>
										{% endif %}
										<b class="pdf_bill_to_enterprise_detail">
											{% if header_res.include_partyname %}
												{% if header_res.include_partycode %}
													{{ source.customer.name }} ({{ source.customer.code }})
												{% else %}
													{{ source.customer.name }}
												{% endif %}
											{% elif header_res.include_partycode %}
												{{ source.customer.code }}
											{% endif %}
										</b>
										<span><br>
										{{ source.customer.address_1 }}, {{ source.customer.address_2 }}
										{{ source.customer.city }}, {{ source.customer.state }},
										{% for country in country_list %}
											{% if country.country_code == source.customer.country_code %}
												{{ country.country_name|upper }}
											{% endif %}
										{% endfor %} {{ source.customer.pin_code }} <br>
										{% for reg_detail in source.customer.registration_details %}
											{% if reg_detail.label == "GSTIN" %}
												<b>GSTIN:</b> {{ reg_detail.details }}
											{% endif %}
										{% endfor %}
										{% for reg_detail in source.customer.registration_details %}
											{% if reg_detail.label != "GSTIN" and reg_detail.details != "" %}
												<b>{{ reg_detail.label }}:</b> {{ reg_detail.details }}
											{% endif %}
										{% endfor %}
										</span>
									</span>
									<span>
										{% if source.customer.primary_contact_details.contact.phone_no and header_res.include_party_phoneno %}
											<span class="pdf_bill_to_contact"><br /><b>Ph:</b> {{ source.customer.primary_contact_details.contact.phone_no }}</span>
										{% endif %}
										{% if source.customer.primary_contact_details.contact.email and header_res.include_party_email %}
											<span class="pdf_bill_to_email"><br /><b>E-mail:</b>{{ source.customer.primary_contact_details.contact.email }}</span>
										{% endif %}
									</span>
								</div>
							</div>
						{% endif %}
						{% if source.deliver_to and header_res.include_shippingaddress %}
							<div class="col-sm-6 pdf_ship_to_address">
								<div class="pdf_company_details_container" style="padding: 4px;">
									<span>
										{% if header_res.shippingaddress_label != "" %}
											<b class="ship_to_text">{{ header_res.shippingaddress_label }}</b>
										{% endif %}
										<span style="word-wrap: break-word;">
											{% if source.ship_to_name != "" and source.ship_to_name != None %}
												<b class="pdf_bill_to_enterprise_detail"> {{ source.ship_to_name }} </b><br />
											{% endif %}
											{{ source.deliver_to }}<br />
											{% if source.gstin %}
												<b>GSTIN:</b> {{ source.gstin }}
											{% endif %}
										</span>
									</span>
								</div>
							</div>
						{% endif %}
						{% if irn_details != "" %}
							<div class="clearfix"></div>
							<div class="col-sm-12">
								<hr style="margin:0;border: 1pt solid #666; margin-bottom: 10px;" />
							</div>
							<div class="col-sm-12 pdf_company_details_container pdf_company_details_container-irn ">
						{% else %}
							</div>
							<div class="col-sm-4 pdf_company_details_container">
						{% endif %}
							{% if header_res.include_pono %}
								<span class="pdf_po_no">
									<b class="pdf_others_details invoice_other_details_list pdf_po_no_txt pdf_company_details_container">
										{{ header_res.pono_label }}
									</b><span class="invoice_other_details_data pdf_company_details_container">:
									{% if po_no %} {{ po_no }}{% endif %}</span><br />
								</span>
							{% endif %}
							{% if header_res.include_podate %}
								<span class="pdf_po_date_span">
									<b class="pdf_others_details invoice_other_details_list pdf_po_date_txt pdf_company_details_container">
										{{ header_res.podate_label }}
									</b><span class="invoice_other_details_data pdf_company_details_container pdf_po_date">:
									{% if po_date %} {{ po_date }} {% endif %}</span><br />
								</span>
							{% endif %}
							{% if header_res.include_estimate_nodate and se_no_date != "" %}
								<span class="pdf_estimate_no_date"><b class="pdf_others_details invoice_other_details_list pdf_estimate_no_date_txt pdf_company_details_container">{{ header_res.estimate_nodate_label }}</b><span class="invoice_other_details_data pdf_company_details_container pdf_estimate_date">:
										{{ se_no_date }}<br /></span>
							{% endif %}
							{% if header_res.include_transportmode and source.transport_mode %}
								<span class="pdf_transport_mode"><b class="pdf_others_details invoice_other_details_list pdf_transport_mode_txt pdf_company_details_container">{{ header_res.transportmode_label }}</b><span class="invoice_other_details_data pdf_company_details_container">: {{ source.transport_mode }}</span><br /></span>
							{% endif %}
							{% if header_res.include_lrnodate and source.lr_no %}
								<span class="pdf_lr_no"><b class="pdf_others_details invoice_other_details_list pdf_lr_no_txt pdf_company_details_container">{{ header_res.lrnodate_label }}</b><span class="invoice_other_details_data pdf_company_details_container">: {{ source.lr_no }}</span><br /></span>
							{% endif %}
							{% if header_res.include_roadpermitno and source.road_permit_no %}
								<span class="pdf_road_permit_no"><b class="pdf_others_details invoice_other_details_list pdf_road_permit_no_txt pdf_company_details_container">{{ header_res.roadpermitno_label }}</b><span class="invoice_other_details_data pdf_company_details_container">: {{ source.road_permit_no }}</span><br /></span>
							{% endif %}
							{% if header_res.include_packingslipno and source.packing_slip_no %}
								<span class="pdf_packing_slip_no"><b class="pdf_others_details invoice_other_details_list pdf_packing_slip_no_txt pdf_company_details_container">{{ header_res.packingslipno_label }}</b><span class="invoice_other_details_data pdf_company_details_container">: {{ source.packing_slip_no }}</span><br /></span>
							{% endif %}
							{% if header_res.include_packingdescription and source.packing_description %}
								<span class="pdf_packing_desc"><b class="pdf_others_details invoice_other_details_list pdf_packing_desc_txt pdf_company_details_container">{{ header_res.packingdescription_label }}</b><span class="invoice_other_details_data pdf_company_details_container">: {{ source.packing_description }}</span><br /></span>
							{% endif %}
							{% if header_res.include_paymentterms and source.payment_terms %}
								<span class="pdf_payment_term"><b class="pdf_others_details invoice_other_details_list pdf_payment_term_txt pdf_company_details_container">{{ header_res.paymentterms_label }}</b><span class="invoice_other_details_data pdf_company_details_container">: {{ source.payment_terms }}</span><br /></span>
							{% endif %}
							 {% if source.type == "DC" or source.type == "JDC" %}
							   {% if source.isRetunable %}
								  {% if source.return_date %}
									 <span><b class="pdf_others_details">Returnable</b>: Yes, {% if return_date %} {{ return_date }} {% endif %}<br /></span>
								  {% else %}
									 <span><b class="pdf_others_details">Returnable</b>: Yes<br /></span>
								  {% endif %}
							   {% else %}
								  <span><b class="pdf_others_details">Returnable</b>: No<br /></span>
							   {% endif %}
							{% endif %}
							{% if source.ecommerce_gstin %}
								<span><b class="pdf_others_details">E-Commerce GSTIN</b>: {{ source.ecommerce_gstin }}<br /></span>
							{% endif %}
						</div>
					{% if irn_details != "" %}
					</div>
						<div class="col-sm-4" style="margin-top: 2px;">
							<table class="table item_table" style="margin-bottom: 7px;">
								<tr>
									<td class="text-right">
										<img src="{{irn_details.irn_scanning_code}}" style="min-width: 275px; max-width: 275px; width: 275px;">
										<br />
										<span style="font-size: 14px;">Digitally Signed by NIC-IRP</span>
									</td>
								</tr>
							</table>
							<div class="divTable">
								<div class="divRow">
									<div class="divCell"><b style="font-weight: bold;">Ack No. </b></div>
									<div class="divCell-small"><b style="font-weight: bold;"> : </b></div>
									<div class="divCell-full">{{irn_details.ack_no}}</div>
								</div>
								<div class="divRow">
									<div class="divCell"><b style="font-weight: bold;">Ack Date</b></div>
									<div class="divCell-small"><b style="font-weight: bold;"> : </b></div>
									<div class="divCell-full">{{irn_details.ack_date}}</div>
							   	</div>
								<div class="divRow">
									<div class="divCell"><b style="font-weight: bold;">IRN</b></div>
									<div class="divCell-small"><b style="font-weight: bold;"> : </b></div>
									<div class="divCell-full">{{irn_details.irn}}</div>
							   	</div>
						  	</div>
						</div>
					{% endif %}
				{% else %}
					<div class="col-sm-12">
						<hr style="margin:0;border: 1pt solid #666; " />
					</div>
					<div class="col-sm-8 pdf_company_address_container" style="padding: 3px 15px;">
						{% if source.issued_to %}
							<span class="pdf_company_address">
								<b>Issued To: </b>{{source.issued_to}}<br>
							</span>
						{% endif %}
						{% if source.issued_for %}
							<span class="pdf_company_address">
								<b>Issued For: </b>{{source.issued_for}}<br>
							</span>
						{% endif %}
						{% if source.project %}
							<span class="pdf_company_contact">
								<b>Project/Tag: </b>{{source.project.name}}({{source.project.code}})<br>
							</span>
						{% endif %}
					</div>
					<div class="col-sm-4 pdf_company_address_container" style="padding: 3px 15px;">

					</div>
				{% endif %}
				<div class="clearfix"></div>
			</div>
		</div>

		<div class="invoice-table-container">
			<table border=2 bordercolor="#CCC" class="table item_table {% if item_res.include_row_separator %} row-seperator{% endif %}{% if item_res.include_column_separator %} column-seperator{% endif %} summary_table" style="width: 100% !important; display: none;">
				<thead>
					<tr class="header_shading">
						{% if item_res.include_sno %}
							<th class="text-center td_sno td_sno_text" rowspan="2" style="width: {{item_res.sno_width}}%">{{item_res.sno_label}}</th>
						{% endif %}
							<th class="text-center td_description td_description_text" rowspan="2" style="width: {{item_res.itemdetails_width}}%">{{ item_res.itemdetails_label }}</th>
						{% if source.type != "Issue" and item_res.include_hsnsac and not item_res.hsnsac_part_of_itemdetails %}
							<th class="text-center pdf_item_hsn_code_txt td_hsn_code" rowspan="2" style="width: {{item_res.hsnsac_width}}%">{{ item_res.hsnsac_label }}</th>
						{% endif %}
						{% if item_res.include_quantity %}
							<th class="text-center td_qty td_qty_text" rowspan="2" style="width: {{item_res.quantity_width}}%">{{ item_res.quantity_label }}</th>
						{% endif %}
						{% if item_res.include_units and not item_res.units_in_quantity_column %}
							<th class="text-center td_uom td_uom_text" rowspan="2" style="width: {{item_res.units_width}}%">{{ item_res.units_label }}</th>
						{% endif %}
						{% if source.type != "Issue" and item_res.include_unit_price %}
							<th class="text-center td_price td_price_text" rowspan="2" style="width: {{item_res.unit_price_width}}%">{{ item_res.unit_price_label }}<br>{{ source.currency }}</th>
						{% endif %}
						{% if source.type != "Issue" and item_res.include_discount %}
							<th class="text-center td_disc td_disc_text" rowspan="2" style="width: {{item_res.discount_width}}%">{{ item_res.discount_label }}<br>%</th>
						{% endif %}
						{% if source.type != "Issue" and item_res.include_taxable_amount %}
							<th class="text-center td_tax td_tax_text" rowspan="2" style="width: {{item_res.taxable_amount_width}}%">{{ item_res.taxable_amount_label }}</th>
						{% endif %}
						{% if source.type != "Issue" and item_res.include_tax %}
							{% if item_res.show_tax_for_dc %}
								{% if item_res.tax_type == 1 %}
									{% if item_res.include_taxrate and item_res.include_taxamount %}
										<th class="text-center td_cgst tax_rate_column" colspan="2" style="width: 12%; ">CGST</th>
										<th class="text-center td_sgst tax_rate_column" colspan="2" style="width: 12%; ">SGST</th>
										<th class="text-center td_igst tax_rate_column" colspan="2" style="width: 12%;  border-right-color: #ccc;">IGST</th>
									{% elif item_res.include_taxrate or item_res.include_taxamount %}
										<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {% if item_res.include_taxrate %} {{item_res.taxrate_width}}% {% else %} {{item_res.taxamount_width}}% {% endif %}">CGST</th>
										<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {% if item_res.include_taxrate %} {{item_res.taxrate_width}}% {% else %} {{item_res.taxamount_width}}% {% endif %}">SGST</th>
										<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {% if item_res.include_taxrate %} {{item_res.taxrate_width}}% {% else %} {{item_res.taxamount_width}}% {% endif %}">IGST</th>
									{% endif %}
								{% elif item_res.tax_type == 4 %}
									{% if item_res.include_taxamount %}
										<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {{item_res.taxamount_width}}%">CGST</th>
										<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {{item_res.taxamount_width}}%">SGST</th>
										<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {{item_res.taxamount_width}}%">IGST</th>
									{% endif %}
								{% endif %}
							{% else %}
								{% if source.type != "DC" and source.type != "JDC" %}
									{% if item_res.tax_type == 1 %}
										{% if item_res.include_taxrate and item_res.include_taxamount %}
											<th class="text-center td_cgst tax_rate_column" colspan="2" style="width: 12%; ">CGST</th>
											<th class="text-center td_sgst tax_rate_column" colspan="2" style="width: 12%; ">SGST</th>
											<th class="text-center td_igst tax_rate_column" colspan="2" style="width: 12%;  border-right-color: #ccc;">IGST</th>
										{% elif item_res.include_taxrate or item_res.include_taxamount %}
											<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {% if item_res.include_taxrate %} {{item_res.taxrate_width}}% {% else %} {{item_res.taxamount_width}}% {% endif %}">CGST</th>
											<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {% if item_res.include_taxrate %} {{item_res.taxrate_width}}% {% else %} {{item_res.taxamount_width}}% {% endif %}">SGST</th>
											<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {% if item_res.include_taxrate %} {{item_res.taxrate_width}}% {% else %} {{item_res.taxamount_width}}% {% endif %}">IGST</th>
										{% endif %}
									{% elif item_res.tax_type == 4 %}
										{% if item_res.include_taxamount %}
											<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {{item_res.taxamount_width}}%">CGST</th>
											<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {{item_res.taxamount_width}}%">SGST</th>
											<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {{item_res.taxamount_width}}%">IGST</th>
										{% endif %}
									{% endif %}
								{% endif %}
							{% endif %}
						{% endif %}
					</tr>
					{% if source.type != "Issue" and item_res.include_tax %}
						{% if item_res.show_tax_for_dc %}
							{% if item_res.tax_type == 1 %}
								{% if item_res.include_taxrate and item_res.include_taxamount %}
									<tr class="tax_rate_column header_shading tr_second_row">
										<th class="text-center td_cgst_rate td_tax_rate td_gst_rate" style="width: {{item_res.taxrate_width}}%">%</th>
										<th class="text-center td_cgst_amt td_tax_amt td_gst_amt"style="width: {{item_res.taxamount_width}}%">{{ source.currency.code }}</th>
										<th class="text-center td_sgst_rate td_tax_rate td_gst_rate" style="width: {{item_res.taxrate_width}}%">%</th>
										<th class="text-center td_sgst_amt td_tax_amt td_gst_amt"style="width: {{item_res.taxamount_width}}%">{{ source.currency.code }}</th>
										<th class="text-center td_igst_rate td_tax_rate td_gst_rate" style="width: {{item_res.taxrate_width}}%">%</th>
										<th class="text-center td_igst_amt td_tax_amt td_gst_amt"style="width: {{item_res.taxamount_width}}%">{{ source.currency.code }}</th>
									</tr>
								{% endif %}
							{% endif %}
						{% else %}
							{% if source.type != "DC" and source.type != "JDC" %}
								{% if item_res.tax_type == 1 %}
									{% if item_res.include_taxrate and item_res.include_taxamount %}
										<tr class="tax_rate_column header_shading tr_second_row">
											<th class="text-center td_cgst_rate td_tax_rate td_gst_rate" style="width: {{item_res.taxrate_width}}%">%</th>
											<th class="text-center td_cgst_amt td_tax_amt td_gst_amt"style="width: {{item_res.taxamount_width}}%">{{ source.currency.code }}</th>
											<th class="text-center td_sgst_rate td_tax_rate td_gst_rate" style="width: {{item_res.taxrate_width}}%">%</th>
											<th class="text-center td_sgst_amt td_tax_amt td_gst_amt"style="width: {{item_res.taxamount_width}}%">{{ source.currency.code }}</th>
											<th class="text-center td_igst_rate td_tax_rate td_gst_rate" style="width: {{item_res.taxrate_width}}%">%</th>
											<th class="text-center td_igst_amt td_tax_amt td_gst_amt"style="width: {{item_res.taxamount_width}}%">{{ source.currency.code }}</th>
										</tr>
									{% endif %}
								{% endif %}
							{% endif %}
						{% endif %}
					{% endif %}
				</thead>
				<tbody>
				    <tr class="annexure_height" style="{% if summary_res.include_hsn_summary %} height: auto !important; {% endif %}">
					{% if item_res.include_sno %}
						<td class="text-center td_sno">-</td>
					{% endif %}
					<td class="text-center pdf_item_name">AS PER ANNEXURE TO {{ form_name|upper }} NO:<br>{{ invoice_no }}</td>
					{% if source.type != "Issue" and item_res.include_hsnsac and not item_res.hsnsac_part_of_itemdetails %}
						<td class="text-center td_hsn_code">-</td>

					{% endif %}
					{% if item_res.include_quantity %}
						<td class="text-center td_qty">-</td>
					{% endif %}
					{% if item_res.include_units and not item_res.units_in_quantity_column %}
						<td class="text-center td_uom">-</td>
					{% endif %}
					{% if source.type != "Issue" and item_res.include_unit_price %}
						<td class="text-center td_price">-</td>
					{% endif %}
					{% if source.type != "Issue" and item_res.include_discount %}
						<td class="text-center td_disc">-</td>
					{% endif %}
					{% if source.type != "Issue" and item_res.include_taxable_amount %}
						<td class="text-center td_tax">-</td>
					{% endif %}
					{% if source.type != "Issue" and item_res.include_tax %}
						{% if item_res.show_tax_for_dc %}
							{% if item_res.tax_type == 1 %}
								{% if item_res.include_taxrate %}
									<td class="text-center td_cgst tax_rate_column td_gst_rate">-</td>
								{% endif %}
								{% if item_res.include_taxamount %}
									<td class="text-center td_cgst tax_rate_column td_gst_amt">-</td>
								{% endif %}
								{% if item_res.include_taxrate %}
									<td class="text-center td_sgst tax_rate_column td_gst_rate">-</td>
								{% endif %}
								{% if item_res.include_taxamount %}
									<td class="text-center td_sgst tax_rate_column td_gst_amt">-</td>
								{% endif %}
								{% if item_res.include_taxrate %}
									<td class="text-center td_igst tax_rate_column td_gst_rate">-</td>
								{% endif %}
								{% if item_res.include_taxamount %}
									<td class="text-center td_igst tax_rate_column td_gst_amt">-</td>
								{% endif %}
							{% elif item_res.tax_type == 4 and item_res.include_taxamount %}
								<td class="text-center tax_one_column tax_one_column_csgt ">-</td>
								<td class="text-center tax_one_column tax_one_column_ssgt ">-</td>
								<td class="text-center tax_one_column tax_one_column_isgt ">-</td>
							{% endif %}
					    {% else %}
							{% if source.type != "DC" and source.type != "JDC" %}
					            {% if item_res.tax_type == 1 %}
									{% if item_res.include_taxrate %}
										<td class="text-center td_cgst tax_rate_column td_gst_rate">-</td>
									{% endif %}
									{% if item_res.include_taxamount %}
										<td class="text-center td_cgst tax_rate_column td_gst_amt">-</td>
									{% endif %}
									{% if item_res.include_taxrate %}
										<td class="text-center td_sgst tax_rate_column td_gst_rate">-</td>
									{% endif %}
									{% if item_res.include_taxamount %}
										<td class="text-center td_sgst tax_rate_column td_gst_amt">-</td>
									{% endif %}
									{% if item_res.include_taxrate %}
										<td class="text-center td_igst tax_rate_column td_gst_rate">-</td>
									{% endif %}
									{% if item_res.include_taxamount %}
										<td class="text-center td_igst tax_rate_column td_gst_amt">-</td>
									{% endif %}
								{% elif item_res.tax_type == 4 and item_res.include_taxamount %}
									<td class="text-center tax_one_column tax_one_column_csgt ">-</td>
									<td class="text-center tax_one_column tax_one_column_ssgt ">-</td>
									<td class="text-center tax_one_column tax_one_column_isgt ">-</td>
								{% endif %}
							{% endif %}
					    {% endif %}
					{% endif %}
					</tr>
					{% if source.type != "Issue" %}
						{% for inv_charge in source.charges %}
							<tr>
								{% if item_res.include_sno %}
									<td class="text-center td_sno"></td>
								{% endif %}
								<td class="text-right"><span class="pdf_item_name">{{ inv_charge.item_name }}<br></span>
									{% if item_res.include_hsnsac and item_res.hsnsac_part_of_itemdetails and inv_charge.hsn_code %}
										<span class="pdf_item_hsn_code"><i>{% if item_res.hsnsac_label != "" %} {{ item_res.hsnsac_label }} {% endif %}</i> {{ inv_charge.hsn_code }}<br></span>
									{% endif %}
									{% if source.hasAssessRateTax %}
										<span class="pdf_item_hsn_code"><i>Disc. Rate:</i>{{ source.currency }} {{ inv_charge.getTotalCharge|floatformat:2 }} / <i>Ass. Rate:</i>{{ source.currency }} {{ inv_charge.getTotalCharge|floatformat:2 }}<br></span>
									{% endif %}
									{% if item_res.include_tax %}
										{% if item_res.show_tax_for_dc %}
											{% if item_res.tax_type == 3 %}
												{% if inv_charge.getInvoiceChargeTaxValue.cgst_rate != 0.00 or inv_charge.getInvoiceChargeTaxValue.sgst_rate != 0.00 or inv_charge.getInvoiceChargeTaxValue.igst_rate != 0.00 %}
													<span class="tax_in_description">[
													                                 {% if inv_charge.getInvoiceChargeTaxValue.cgst_rate != 0.00 %}
												                                        CGST @ {{ inv_charge.getInvoiceChargeTaxValue.cgst_rate|floatformat:1 }}: {{ inv_charge.getInvoiceChargeTaxValue.cgst_value|floatformat:2 }}
																							{% if inv_charge.getInvoiceChargeTaxValue.sgst_rate != 0.00 or inv_charge.getInvoiceChargeTaxValue.igst_rate != 0.00 %} - {% endif %}
														                             {% endif %}
																					 {% if inv_charge.getInvoiceChargeTaxValue.sgst_rate != 0.00 %}
												                                        SGST @ {{ inv_charge.getInvoiceChargeTaxValue.sgst_rate|floatformat:1 }}: {{ inv_charge.getInvoiceChargeTaxValue.sgst_value|floatformat:2 }}
																							{% if inv_charge.getInvoiceChargeTaxValue.igst_rate != 0.00 %} - {% endif %}
																					 {% endif %}
																					 {% if inv_charge.getInvoiceChargeTaxValue.igst_rate != 0.00 %}
													                                    IGST @ {{ inv_charge.getInvoiceChargeTaxValue.igst_rate|floatformat:1 }}: {{ inv_charge.getInvoiceChargeTaxValue.igst_value|floatformat:2 }}
																					 {% endif %}
																					 ]</span>
												{% endif %}
											{% endif %}
										{% else %}
											{% if source.type != "DC" and source.type != "JDC" %}
												{% if item_res.tax_type == 3 %}
													{% if inv_charge.getInvoiceChargeTaxValue.cgst_rate != 0.00 or inv_charge.getInvoiceChargeTaxValue.sgst_rate != 0.00 or inv_charge.getInvoiceChargeTaxValue.igst_rate != 0.00 %}
														<span class="tax_in_description">[
														                                 {% if inv_charge.getInvoiceChargeTaxValue.cgst_rate != 0.00 %}
													                                        CGST @ {{ inv_charge.getInvoiceChargeTaxValue.cgst_rate|floatformat:1 }}: {{ inv_charge.getInvoiceChargeTaxValue.cgst_value|floatformat:2 }}
																								{% if inv_charge.getInvoiceChargeTaxValue.sgst_rate != 0.00 or inv_charge.getInvoiceChargeTaxValue.igst_rate != 0.00 %} - {% endif %}
															                             {% endif %}
																						 {% if inv_charge.getInvoiceChargeTaxValue.sgst_rate != 0.00 %}
													                                        SGST @ {{ inv_charge.getInvoiceChargeTaxValue.sgst_rate|floatformat:1 }}: {{ inv_charge.getInvoiceChargeTaxValue.sgst_value|floatformat:2 }}
																								{% if inv_charge.getInvoiceChargeTaxValue.igst_rate != 0.00 %} - {% endif %}
																						 {% endif %}
																						 {% if inv_charge.getInvoiceChargeTaxValue.igst_rate != 0.00 %}
														                                    IGST @ {{ inv_charge.getInvoiceChargeTaxValue.igst_rate|floatformat:1 }}: {{ inv_charge.getInvoiceChargeTaxValue.igst_value|floatformat:2 }}
																						 {% endif %}
																						 ]</span>
													{% endif %}
												{% endif %}
											{% endif %}
										{% endif %}
									{% endif %}
								</td>
								{% if item_res.include_hsnsac and not item_res.hsnsac_part_of_itemdetails %}
									<td class="text-center td_hsn_code">{{ inv_charge.hsn_code }}</td>
								{% endif %}
								{% if item_res.include_quantity %}
									<td class="text-right td_qty"><br />
										{% if item_res.units_in_quantity_column %}
											<span class="pdf_unit_in_price hide"></span>
										{% endif %}
									</td>
								{% endif %}
								{% if item_res.include_units and not item_res.units_in_quantity_column %}
									{% if inv_material.material.item.unit.unit_name %}
										<td class="text-center td_uom"></td>
									{% else %}
										<td class="text-center td_uom"></td>
									{% endif %}
								{% endif %}
								{% if item_res.include_unit_price %}
									<td class="text-right td_price">{{ inv_charge.rate|floatformat:2 }}</td>
								{% endif %}
								{% if item_res.include_discount %}
									<td class="text-right td_disc">{{ inv_charge.discount|floatformat:2 }}</td>
								{% endif %}
								{% if item_res.include_taxable_amount %}
									<td class="text-right td_tax">{{ inv_charge.getTotalCharge|floatformat:2 }}</td>
								{% endif %}
								{% if item_res.include_tax %}
									{% if item_res.show_tax_for_dc %}
										{% if item_res.tax_type == 1 %}
											{% if item_res.include_taxrate %}
												<td class="text-right td_cgst tax_rate_column td_gst_rate">
													{% if item_res.include_taxamount %} {{ inv_charge.getInvoiceChargeTaxValue.cgst_rate|floatformat:2 }} {% else %} {{ inv_charge.getInvoiceChargeTaxValue.cgst_rate|floatformat:2 }}% {% endif %}</td>
											{% endif %}
											{% if item_res.include_taxamount %}
												<td class="text-right td_cgst tax_rate_column td_gst_amt">{{ inv_charge.getInvoiceChargeTaxValue.cgst_value|floatformat:2 }}</td>
											{% endif %}
											{% if item_res.include_taxrate %}
												<td class="text-right td_sgst tax_rate_column td_gst_rate">
													{% if item_res.include_taxamount %} {{ inv_charge.getInvoiceChargeTaxValue.sgst_rate|floatformat:2 }} {% else %} {{ inv_charge.getInvoiceChargeTaxValue.sgst_rate|floatformat:2 }}% {% endif %}</td>
											{% endif %}
											{% if item_res.include_taxamount %}
												<td class="text-right td_sgst tax_rate_column td_gst_amt">{{ inv_charge.getInvoiceChargeTaxValue.sgst_value|floatformat:2 }}</td>
											{% endif %}
											{% if item_res.include_taxrate %}
												<td class="text-right td_igst tax_rate_column td_gst_rate">
													{% if item_res.include_taxamount %} {{ inv_charge.getInvoiceChargeTaxValue.igst_rate|floatformat:2 }} {% else %} {{ inv_charge.getInvoiceChargeTaxValue.igst_rate|floatformat:2 }}% {% endif %}</td>
											{% endif %}
											{% if item_res.include_taxamount %}
												<td class="text-right td_igst tax_rate_column td_gst_amt">{{ inv_charge.getInvoiceChargeTaxValue.igst_value|floatformat:2 }}</td>
											{% endif %}
										{% elif item_res.tax_type == 4 and item_res.include_taxamount %}
											<td class="text-center tax_one_column tax_one_column_csgt ">{{ inv_charge.getInvoiceChargeTaxValue.cgst_value|floatformat:2 }}<br><small>@ {{ inv_charge.getInvoiceChargeTaxValue.cgst_rate|floatformat:2 }}%</small></td>
											<td class="text-center tax_one_column tax_one_column_ssgt ">{{ inv_charge.getInvoiceChargeTaxValue.sgst_value|floatformat:2 }}<br><small>@ {{ inv_charge.getInvoiceChargeTaxValue.sgst_rate|floatformat:2 }}%</small></td>
											<td class="text-center tax_one_column tax_one_column_isgt ">{{ inv_charge.getInvoiceChargeTaxValue.igst_value|floatformat:2 }}<br><small>@ {{ inv_charge.getInvoiceChargeTaxValue.igst_rate|floatformat:2 }}%</small></td>
										{% endif %}
									{% else %}
										{% if source.type != "DC" and source.type != "JDC" %}
											{% if item_res.tax_type == 1 %}
												{% if item_res.include_taxrate %}
													<td class="text-right td_cgst tax_rate_column td_gst_rate">
														{% if item_res.include_taxamount %} {{ inv_charge.getInvoiceChargeTaxValue.cgst_rate|floatformat:2 }} {% else %} {{ inv_charge.getInvoiceChargeTaxValue.cgst_rate|floatformat:2 }}% {% endif %}</td>
												{% endif %}
												{% if item_res.include_taxamount %}
													<td class="text-right td_cgst tax_rate_column td_gst_amt">{{ inv_charge.getInvoiceChargeTaxValue.cgst_value|floatformat:2 }}</td>
												{% endif %}
												{% if item_res.include_taxrate %}
													<td class="text-right td_sgst tax_rate_column td_gst_rate">
														{% if item_res.include_taxamount %} {{ inv_charge.getInvoiceChargeTaxValue.sgst_rate|floatformat:2 }} {% else %} {{ inv_charge.getInvoiceChargeTaxValue.sgst_rate|floatformat:2 }}% {% endif %}</td>
												{% endif %}
												{% if item_res.include_taxamount %}
													<td class="text-right td_sgst tax_rate_column td_gst_amt">{{ inv_charge.getInvoiceChargeTaxValue.sgst_value|floatformat:2 }}</td>
												{% endif %}
												{% if item_res.include_taxrate %}
													<td class="text-right td_igst tax_rate_column td_gst_rate">
														{% if item_res.include_taxamount %} {{ inv_charge.getInvoiceChargeTaxValue.igst_rate|floatformat:2 }} {% else %} {{ inv_charge.getInvoiceChargeTaxValue.igst_rate|floatformat:2 }}% {% endif %}</td>
												{% endif %}
												{% if item_res.include_taxamount %}
													<td class="text-right td_igst tax_rate_column td_gst_amt">{{ inv_charge.getInvoiceChargeTaxValue.igst_value|floatformat:2 }}</td>
												{% endif %}
											{% elif item_res.tax_type == 4 and item_res.include_taxamount %}
												<td class="text-center tax_one_column tax_one_column_csgt ">{{ inv_charge.getInvoiceChargeTaxValue.cgst_value|floatformat:2 }}<br><small>@ {{ inv_charge.getInvoiceChargeTaxValue.cgst_rate|floatformat:2 }}%</small></td>
												<td class="text-center tax_one_column tax_one_column_ssgt ">{{ inv_charge.getInvoiceChargeTaxValue.sgst_value|floatformat:2 }}<br><small>@ {{ inv_charge.getInvoiceChargeTaxValue.sgst_rate|floatformat:2 }}%</small></td>
												<td class="text-center tax_one_column tax_one_column_isgt ">{{ inv_charge.getInvoiceChargeTaxValue.igst_value|floatformat:2 }}<br><small>@ {{ inv_charge.getInvoiceChargeTaxValue.igst_rate|floatformat:2 }}%</small></td>
											{% endif %}
										{% endif %}
									{% endif %}
								{% endif %}
							</tr>
						{% endfor %}
					{% endif %}
				</tbody>
				<tfoot>
					{% if summary_res.include_total %}
						{% if source.type != "Issue" and summary_res.include_subtotal %}
							<tr class="total_section sub_total_section">
								<td colspan="{{item_column_span.span_total_column}}" class="text-right total_section_1"><b>Total</b></td>
								{% if item_res.include_quantity and summary_res.include_qty_total %}
									<td class="text-right total_section_2">{{ source.getConsolidatedQuantity|floatformat:2 }}</td>
								{% endif %}
								{% if source.type != "Issue" %}
									<td colspan="{{item_column_span.span_total_summary}}" class="text-right total_section_3">{{ source.getTotalTaxableValue|floatformat:2 }}</td>
								{% endif %}
								{% if source.type != "Issue" and item_res.include_tax %}
									{% if item_res.show_tax_for_dc %}
										{% if item_res.tax_type == 1 %}
											{% if item_res.include_taxrate %}
												<td class="tax_rate_column td_gst_rate"></td>
											{% endif %}
											{% if item_res.include_taxamount %}
												<td class="tax_rate_column text-right td_gst_amt">{{ cgst_total_value|floatformat:2 }}</td>
											{% endif %}
											{% if item_res.include_taxrate %}
												<td class="tax_rate_column td_gst_rate"></td>
											{% endif %}
											{% if item_res.include_taxamount %}
												<td class="tax_rate_column text-right td_gst_amt">{{ sgst_total_value|floatformat:2 }}</td>
											{% endif %}
											{% if item_res.include_taxrate %}
												<td class="tax_rate_column td_gst_rate"></td>
											{% endif %}
											{% if item_res.include_taxamount %}
												<td class="tax_rate_column text-right td_gst_amt" style="border-right-color: #ccc;">{{ igst_total_value|floatformat:2 }}</td>
											{% endif %}
										{% elif item_res.tax_type == 4 %}
											{% if item_res.include_taxamount %}
												<td class="text-right tax_one_column tax_one_column_csgt_total">{{ cgst_total_value|floatformat:2 }}<br></td>
												<td class="text-right tax_one_column tax_one_column_ssgt_total">{{ sgst_total_value|floatformat:2 }}<br></td>
												<td class="text-right tax_one_column tax_one_column_isgt_total" style="border-right-color: #ccc;">{{ igst_total_value|floatformat:2 }}<br></td>
											{% endif %}
										{% endif %}
									{% else %}
										{% if source.type != "DC" and source.type != "JDC" %}
											{% if item_res.tax_type == 1 %}
												{% if item_res.include_taxrate %}
													<td class="tax_rate_column td_gst_rate"></td>
												{% endif %}
												{% if item_res.include_taxamount %}
													<td class="tax_rate_column text-right td_gst_amt">{{ cgst_total_value|floatformat:2 }}</td>
												{% endif %}
												{% if item_res.include_taxrate %}
													<td class="tax_rate_column td_gst_rate"></td>
												{% endif %}
												{% if item_res.include_taxamount %}
													<td class="tax_rate_column text-right td_gst_amt">{{ sgst_total_value|floatformat:2 }}</td>
												{% endif %}
												{% if item_res.include_taxrate %}
													<td class="tax_rate_column td_gst_rate"></td>
												{% endif %}
												{% if item_res.include_taxamount %}
													<td class="tax_rate_column text-right td_gst_amt" style="border-right-color: #ccc;">{{ igst_total_value|floatformat:2 }}</td>
												{% endif %}
											{% elif item_res.tax_type == 4 %}
												{% if item_res.include_taxamount %}
													<td class="text-right tax_one_column tax_one_column_csgt_total">{{ cgst_total_value|floatformat:2 }}<br></td>
													<td class="text-right tax_one_column tax_one_column_ssgt_total">{{ sgst_total_value|floatformat:2 }}<br></td>
													<td class="text-right tax_one_column tax_one_column_isgt_total" style="border-right-color: #ccc;">{{ igst_total_value|floatformat:2 }}<br></td>
												{% endif %}
											{% endif %}
										{% endif %}
									{% endif %}
								{% endif %}
							</tr>
						{% elif item_res.include_quantity and summary_res.include_qty_total %}
							<tr class="total_section sub_total_section">
								<td colspan="{{item_column_span.span_total_column}}" class="text-right total_section_1"><b>Total</b></td>
								<td class="text-right total_section_2">{{ source.getConsolidatedQuantity|floatformat:2 }}</td>
								{% if item_res.include_units and not item_res.units_in_quantity_column %}
									<td></td>
								{% endif %}
							</tr>
						{% endif %}
					{% endif %}	
				
					{% if source.type != "Issue" %}
						{% for inv_tax in sorted_taxes %}
							{% for key, value in tax_values.items %}
								{% if key == inv_tax.tax_code %}
									<tr class="other_tax_column total_section" style="width: 100%">
										<td colspan="{{item_column_span.total_text_column_count}}" class="text-right total_section_1">{% if inv_tax.tax.assess_rate > 0 %} <i>Accessable Value - {{ source.getTotalTaxableValue|floatformat:2 }}</i><br>{% endif %}{{ inv_tax.tax.name }} @ {{ inv_tax.tax.net_rate }} %</td>
										<td colspan="{{item_column_span.total_value_column_count}}" class="text-right total_section_3">{{ value }}</td>
									</tr>
								{% endif %}
							{% endfor %}
						{% endfor %}
					{% endif %}
					{% if source.type != "Issue" and item_res.include_tax %}
						{% if item_res.show_tax_for_dc %}
							{% if item_res.tax_type == 2 %}
								{% for key, value in tax_summary.cgst_summary.items %}
									<tr class="consolidated_tax total_section" style="width: 100%">
										<td colspan="{{item_column_span.total_text_column_count}}" class="text-right total_section_1">CGST @ {{ key|floatformat:2 }} %</td>
										<td colspan="{{item_column_span.total_value_column_count}}" class="text-right total_section_3">{{ value|floatformat:2 }}</td>
									</tr>
								{% endfor %}
								{% for key, value in tax_summary.sgst_summary.items %}
									<tr class="consolidated_tax total_section" style="width: 100%">
										<td colspan="{{item_column_span.total_text_column_count}}" class="text-right total_section_1">SGST @ {{ key|floatformat:2 }} %</td>
										<td colspan="{{item_column_span.total_value_column_count}}" class="text-right total_section_3">{{ value|floatformat:2 }}</td>
									</tr>
								{% endfor %}
								{% for key, value in tax_summary.igst_summary.items %}
									<tr class="consolidated_tax total_section" style="width: 100%">
										<td colspan="{{item_column_span.total_text_column_count}}" class="text-right total_section_1">IGST @ {{ key|floatformat:2 }} %</td>
										<td colspan="{{item_column_span.total_value_column_count}}" class="text-right total_section_3">{{ value|floatformat:2 }}</td>
									</tr>
								{% endfor %}
							{% endif %}
						{% else %}
							{% if source.type != "DC" and source.type != "JDC" %}
								{% if item_res.tax_type == 2 %}
									{% for key, value in tax_summary.cgst_summary.items %}
										<tr class="consolidated_tax total_section" style="width: 100%">
											<td colspan="{{item_column_span.total_text_column_count}}" class="text-right total_section_1">CGST @ {{ key|floatformat:2 }} %</td>
											<td colspan="{{item_column_span.total_value_column_count}}" class="text-right total_section_3">{{ value|floatformat:2 }}</td>
										</tr>
									{% endfor %}
									{% for key, value in tax_summary.sgst_summary.items %}
										<tr class="consolidated_tax total_section" style="width: 100%">
											<td colspan="{{item_column_span.total_text_column_count}}" class="text-right total_section_1">SGST @ {{ key|floatformat:2 }} %</td>
											<td colspan="{{item_column_span.total_value_column_count}}" class="text-right total_section_3">{{ value|floatformat:2 }}</td>
										</tr>
									{% endfor %}
									{% for key, value in tax_summary.igst_summary.items %}
										<tr class="consolidated_tax total_section" style="width: 100%">
											<td colspan="{{item_column_span.total_text_column_count}}" class="text-right total_section_1">IGST @ {{ key|floatformat:2 }} %</td>
											<td colspan="{{item_column_span.total_value_column_count}}" class="text-right total_section_3">{{ value|floatformat:2 }}</td>
										</tr>
									{% endfor %}
								{% endif %}
							{% endif %}
						{% endif %}
					{% endif %}
					{% if source.round_off != 0 %}
						<tr class="total_section" style="width: 100%">
							<td colspan="{{item_column_span.total_text_column_count}}" class="text-right total_section_1">Round-off<span class="total_inclusice_tax hide"> (inclusive tax)</span></td>
							<td colspan="{{item_column_span.total_value_column_count}}" class="text-right total_section_3">{{ source.round_off }}</td>
						</tr>
					{% endif %}
				    {% if source.type != "Issue" and summary_res.include_total %}
						<tr class="total_section" style="width: 100%">
							<td colspan="{{item_column_span.total_text_column_count}}" class="text-right total_section_1"><b>Grand Total</b><span class="total_inclusice_tax hide"> (inclusive tax)</span></td>
							<td colspan="{{item_column_span.total_value_column_count}}" class="text-right total_section_3">{{ source.currency.code }} <b>{{ source.grand_total }}</b></td>
						</tr>
					{% endif %}
					{% if source.type != "Issue" and summary_res.include_total_in_words %}
						<tr class="tr_total_in_words show_total_in_words total_in_words" style="width: 100%">
							<td colspan="{{item_column_span.span_all_column}}" class="full-length-td"><b>Total Value ({{ source.currency.code }}):</b> {{ total_in_words|upper }}</td>
						</tr>
					{% endif %}
					{% if source.type != "Issue" and header_res.include_splinstructions and source.special_instruction %}
				    <tr>
						<td colspan="{{item_column_span.span_all_column}}" class="full-length-td tr_special_instruction">
							<b class="tr_special_instruction_txt">{{ header_res.splinstructions_label }}</b>
							{{ source.special_instruction|linebreaksbr }}
						</td>
				    </tr>
					{% endif %}
			    </tfoot>
		    </table>

			{% if source.type != "Issue" %}
				<div class="hsn_summary_container_1" style="display: none; width: 100%;">
					{% include "admin/print_template/invoice/document/common/invoice_template_hsn_summary.html" %}
				</div>
			{% endif %}
		        {% include "admin/print_template/invoice/document/common/invoice_template_item_table.html" %}
			{% if source.type != "Issue" %}
				<div class="hsn_summary_container_2">
					{% include "admin/print_template/invoice/document/common/invoice_template_hsn_summary.html" %}
				</div>
			{% endif %}
			{% if has_qir %}
				<p style="page-break-before: always"></p>
				{% include "admin/print_template/invoice/document/common/invoice_template_qir.html" %}
			{% endif %}
		</div>
	</div>
</body>