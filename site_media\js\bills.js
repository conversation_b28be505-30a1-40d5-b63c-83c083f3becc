$(document).ready(function(){
    $(".box-clickable").click(function () {
        $("#DueDetails").modal('show');
    });

    $(".box-clickable_2").click(function () {
        $("#DueDetailsByAging").modal('show');
    });
});

// Move to payment.js
var Ledger = function(data) {
    this.data = data
    this.constructRow = function() {
        var d = this.data
        return '<tr> \
            <td class="text-center">' + d.name + '</td>\
            <td class="text-right">' + d.aging.advance + '</td>\
            <td class="text-right">' + d.credit + '</td>\
            <td class="text-right">' + d.debit + '</td>\
            <td class="text-right">' + d.aging.age1 + '</td>\
            <td class="text-right">' + d.aging.age2 + '</td>\
            <td class="text-right">' + d.aging.age3 + '</td>\
            <td class="text-right">' + d.aging.age4 + '</td>\
        </tr>'
    }
}

var LedgerBill = function(data) {
    this.data = data;
    this.settled_amount = 0;
    this.paid_amount = 0;
    this.due = 0;
    this.autoFillSettlement = function(advance) {
        var balanceAdvance = advance;
        try {
            var due = this.data.due;
            var settled_amount = this.settled_amount;
            if (balanceAdvance > 0 && due > 0) {
                if (due <= balanceAdvance) {
                    settled_amount = due;
                    balanceAdvance -= due;
                    due = 0;
                } else {
                    settled_amount = advance;
                    due -= advance;
                    balanceAdvance = 0;
                }
            }
            this.due = due;
            this.settled_amount = settled_amount;
        } catch(e) {
            console.log("Auto filling against advance", e)
        }
        return balanceAdvance;
    }
    this.autoFillPayment = function(payment) {
        var balancePayment = payment;
        try {
            if (payment > 0 && this.due > 0) {
                if (this.due <= payment) {
                    this.paid_amount = this.due;
                    this.due -= 0
                    balancePayment -= this.due;
                } else {
                    this.paid_amount = payment;
                    this.due -= payment
                    balancePayment = 0
                }
            }
        } catch(e) {
            console.log("Auto filling against payment", e);
        }
        return balancePayment;
    }
    this.constructRow = function(i) {
        var d = this.data
        return '<tr class="bill_record">\
            <td class="text-right">' + i + '.</td>\
            <td class="text-left">' + d.bill_no + '<input type="hidden" id="id-bill_id" value="' + d.bill_id + '"/></td>\
            <td class="text-center">' + moment(d.bill_date).format('MMM DD, YYYY') + '</td>\
            <td class="text-right">' + d.age_days + '</td>\
            <td class="text-right"> <span class="bill">' + d.bill_value.toFixed(2) + '</span></td>\
            <td class="text-right"> <span class="settled">' + d.already_adjusted.toFixed(2) + '</span></td>\
            <td class="text-right" > <span class="due">' + d.due.toFixed(2) + '</span></td>\
            <td class="outstanding_pdf"><input type="text" id="settled_amount_'+i+'" name="settled_amount" class="form-control text-right table_text_box settled_amount" maxlength="16" onfocus="setNumberRangeOnFocus(this,13,2)" onblur="calculateAdvancesClaimedBlur(this);" disabled="disabled" style="font-weight: bold;" value="'+ this.settled_amount.toFixed(2) +'" placeholder="Enter Amount"></td>\
            <td class="outstanding_pdf"><input type="text" id="paid_amount_'+i+'" name="paid_amount" class="form-control text-right table_text_box paid_amount" maxlength="16" onfocus="setNumberRangeOnFocus(this,13,2)" onblur="calculatePaymentTotalBlur(this);" disabled="disabled" style="font-weight: bold;" value="' + this.paid_amount.toFixed(2) + '" placeholder="Enter Amount"></td>\
        </tr>'
    }
    this.insertTotalBillRow = function() {
        return '<tr>\
            <td colspan="4" class="text-right total_amount_field">TOTAL</td>\
            <td class="text-right bill_total total_amount_field">0.00</td>\
            <td class="text-right settled_total total_amount_field">0.00</td>\
            <td class="text-right due_total total_amount_field">0.00</td>\
            <td class="text-right advance_paid_total total_amount_field outstanding_pdf">0.00</td>\
            <td class="text-right current_paid_total total_amount_field outstanding_pdf">0.00</td>\
        </tr>'
    }
}

function populateUnsettledParticulars(particulars, title){
    if (particulars.length > 0){
        $("#particulars_tbody").html("");
        $("#particulars_title").html(title);
        $("#particulars_listing").modal('show');
        $("#loadingmessage").removeClass('hide');
        $("#particulars_table").addClass('hide');
        var voucher_id_list = [];
        var csrf_token = document.getElementsByName('csrfmiddlewaretoken')[0].value;
        $.each(particulars, function(i, item){
            var particular_row = '<tr><td class="text-center">' + (i+1) +
                '.</td><td class="text-left"><form target="_blank" action="/erp/accounts/voucher/edit/#tab2" method="post" id="edit_'+
                item.voucher_id + '_voucher"><input type="hidden" name="csrfmiddlewaretoken" value="' +
                csrf_token + '" />' +'<a role="button" onclick="javascript:clickButton(\'edit_voucher_' +
                item.voucher_id + '\');">' + item.voucher_code + '</a><input type="hidden" value="' +
                item.voucher_id + '" id="id_voucher_no" name="voucher_no"/><input type="hidden" value="' +
                item.type_id +'"  name="voucher_type"/><input type="submit" id="edit_voucher_' +
                item.voucher_id + '" hidden="hidden"/></form></td><td class="text-left">' +
                item.voucher_date + '</td><td class="text-left">' +
                item.narration + '</td><td class="text-right">' +
                item.amount.toFixed(2) + '</td></tr>';
            $("#particulars_tbody").append(particular_row);
        });
        $("#loadingmessage").addClass('hide');
        $("#particulars_table").removeClass('hide');

    }else{
        swal("", "No Records Found!", "warning");
    }
}

function populateAllUnsettledParticulars(ledger_abstracts, title){
    $("#particulars_tbody").html("");
    $("#particulars_title").html(title);
    $("#particulars_listing").modal('show');
    $("#loadingmessage").removeClass('hide');
    $("#particulars_table").addClass('hide');
    $("#particulars_thead").html("<tr><th>S.No</th><th class='text-center' colspan='4'>Ledger</th><th class='text-center'>Outstanding</th></tr>");
    var particulars_abstract = {};
    var voucher_ledger_map = {};
    ledger_idx = 1;
    for(var ledger in ledger_abstracts){
        if(ledger_abstracts[ledger]["total"] != 0){
            var voucher_id_list = [];
            var csrf_token = document.getElementsByName('csrfmiddlewaretoken')[0].value;
            var particular_row = "";
            if (ledger != "ALL"){
                particular_row += '<tr><td class="text-center">' + (ledger_idx++) +
                    '.</td><td  colspan=\"4\"><a role=\"button\" class=\"toggle_view\" onclick=\"javascript:$(\'.' +
                    ledger + '\').toggle();\">' + ledger_abstracts[ledger]["name"] +
                    '</td><td class="text-right" >' + ledger_abstracts[ledger]["total"].toFixed(2) +
                    "</td></tr>"+"<tr class='" + ledger + " text-center' style='display:none;'><th></th>"+"<th width='10px'>#</th>"+
                    "<th width='20%'>Voucher Code</th><th width='15%'>Date</th><th>Narration</th><th width='12%'>Outstanding</th></tr><tr id='"+
                    ledger +"' style='display:none'><td></td></tr>";
            } else {
                particular_row += '<tr><td colspan=\"5\" class="text-right text-total-as-label">' + ledger_abstracts[ledger]["name"] +
                '</td><td class="text-right text-totalamt-as-label" >' + ledger_abstracts[ledger]["total"].toFixed(2) + "</td></tr>";
            }
            $("#particulars_tbody").append(particular_row);
            $.each(ledger_abstracts[ledger]["particulars"], function(i, item){
                var particular_row = '<tr class="'+ ledger +
                        '" style="display:none;"><td></td><td class="text-right">' + (i+1) +
                    '.</td><td class="text-left"><form target="_blank" action="/erp/accounts/voucher/edit/#tab2" method="post" id="edit_'+
                    item.voucher_id + '_voucher"><input type="hidden" name="csrfmiddlewaretoken" value="' +
                    csrf_token + '" />' +'<a role="button" onclick="javascript:clickButton(\'edit_voucher_' +
                    item.voucher_id + '\');">' + item.voucher_code + '</a><input type="hidden" value="' +
                    item.voucher_id + '" id="id_voucher_no" name="voucher_no"/><input type="hidden" value="' +
                    item.type_id +'"  name="voucher_type"/><input type="submit" id="edit_voucher_' +
                    item.voucher_id + '" hidden="hidden"/></form></td><td class="text-left">' +
                    item.voucher_date + '</td><td class="text-left">' +
                    item.narration + '</td><td class="text-right">' +
                    item.amount.toFixed(2) + '</td></tr>';
                $(particular_row).insertBefore("#" + ledger);
            });
            $("#loadingmessage").addClass('hide');
            $("#particulars_table").removeClass('hide');
        }
    }
}

function getAgingByTotalDue(is_receivable){
    $(".a_consolidated_aging") .addClass("disable_download").attr("data-original-title","Loading... Please Wait.");
    $('#loadingmessage').show();
    $('#consolidated_aging').hide();
    document.getElementById("DueByAgingLedgerTBody").innerHTML = "";
    document.getElementsByClassName("DueByAgingLedgerTHead").innerHTML = "";
    document.getElementById("DueDetailsTTitleByAging").innerHTML = "In a moment...";
    var totaldue_localkey = (is_receivable ? 'receivable_totaldue' : 'payable_totaldue');
    var result = '';
    if (localStorage.getItem(totaldue_localkey) !== null) {
        result = JSON.parse(localStorage.getItem(totaldue_localkey));
        construct_report_table(result);
    }
    else {
        $.ajax({
            url: '/erp/accounts/json/aging_by_totaldue/',
            type: "GET",
            data: {
                "is_receivable": is_receivable
            },
            success: function (json) {
                if (json.response_message != "Success") {
                    $("#DueDetailsByAging").modal('hide');
                    $('#loadingmessage').hide();
                    swal("", json.error, "warning");
                    return
                }
                else{
                    localStorage.setItem(totaldue_localkey, JSON.stringify(json));
                    result = json;
                    construct_report_table(result);
                }
            }
        });
    }
}

function construct_report_table(result){
    var age1_total = 0.00;
    var age2_total = 0.00;
    var age3_total = 0.00;
    var age4_total = 0.00;
    var billable_total = 0.00;
    var advances_total = 0.00;
    var unbilled_total = 0.00;
    var excess_total = 0.00;
    var tbl_header = '<tr><th width="5%">S. No</th><th colspan="3">Ledger</th><th>Cr. Period</th><th>Total Due</th>' +
    '<th> < 30 Days</th><th>30 - 60 Days</th> <th>60 - 90 Days</th><th> > 90 Days</th>' +
    '<th>Advances</th><th>Un-Billed</th><th>Excess</th></tr>';
    $(".DueByAgingLedgerTHead").html(tbl_header);
    var age_list_html = '';
    var table_data = '';
    for (let i = 0; i < result['totaldue'].length; i++) {
        var s_no = i+1
        json_data = result['totaldue'][i]
        age_list_html += '<tr><td class="text-center serial_no"></td><td colspan="3" class="text-left table_ledger_name"><b>' + json_data.Ledger
            + '</b></td><td colspan="9" class="text-center"><p class="saving">Loading<span>.</span><span>.</span><span>.</span></p></td></tr>';
        table_data += '<tr><td class="text-center">' + s_no
                + '</td><td colspan="3" class="text-left table_ledger_name"><b>' + json_data.Ledger
                + '</b></td><td class="text-right">' + json_data['Cr. Period'] + ' days'
                + '</td><td class="text-right">' + json_data['Total_Due']
                + '</td><td class="text-right">' + json_data['< 30 Days']
                + '</td><td class="text-right">' + json_data['30 - 60 Days']
                + '</td><td class="text-right">' + json_data['60 - 90 Days']
                + '</td><td class="text-right">' + json_data['> 90 Days']
                + '</td><td class="text-right">' + json_data['Advances']
                + '</td><td class="text-right">' + json_data['Un-Billed']
                + '</td><td class="text-right">' + json_data['Excess']
                + '</td></tr>';
        age1_total += parseFloat(json_data['< 30 Days']);
        age2_total += parseFloat(json_data['30 - 60 Days']);
        age3_total += parseFloat(json_data['60 - 90 Days']);
        age4_total += parseFloat(json_data['> 90 Days']);
        billable_total += parseFloat(json_data['Total_Due']);
        advances_total += parseFloat(json_data['Advances']);
        unbilled_total += parseFloat(json_data['Un-Billed']);
        excess_total += parseFloat(json_data['Excess']);
    }
    table_data += '<td></td><td colspan="3"><b>Total</b></td><td></td><td><b>' + billable_total.toFixed(2)
                    + '</b></td><td class="text-right"><b>' + age1_total.toFixed(2)
                    + '</b></td><td class="text-right"><b>' + age2_total.toFixed(2)
                    + '</b></td><td class="text-right"><b>' + age3_total.toFixed(2)
                    + '</b></td><td class="text-right"><b>' + age4_total.toFixed(2)
                    + '</b></td><td class="text-right"><b>' + advances_total.toFixed(2)
                    + '</b></td><td class="text-right"><b>' + unbilled_total.toFixed(2)
                    + '</b></td><td class="text-right"><b>' + excess_total.toFixed(2) +'</b></td>';

    document.getElementById("DueByAgingLedgerTBody").innerHTML = table_data;
    $(".a_consolidated_aging").removeClass("disable_download").attr("data-original-title","Download as CSV");
    document.getElementById("DueDetailsTTitleByAging").innerHTML = "Total Due";
    $('#loadingmessage').hide();
    $('#consolidated_aging').show();
}

function getTotalDueConstruct(is_receivable){
    $.ajax({
        url: '/erp/accounts/json/aging_by_totaldue/',
        type: "GET",
        data: {
            "is_receivable": is_receivable
        },
        success: function (result) {
            if (result.response_message == "Success") {
                if (is_receivable == true){
                    localStorage.setItem('receivable_totaldue', JSON.stringify(result));
                }
                else{
                    localStorage.setItem('payable_totaldue', JSON.stringify(result));
                }
                return
            }
        }
    });
}

function getAgingByDetailedView(is_receivable, option, start_days, end_days, is_advance) {
    $(".a_consolidated_aging").addClass("disable_download").attr("data-original-title","Loading... Please Wait.");
    $('#loadingmessage').show();
    $('#consolidated_aging').hide();
    document.getElementById("DueByAgingLedgerTBody").innerHTML = "";
    document.getElementsByClassName("DueByAgingLedgerTHead").innerHTML = "";
    document.getElementById("DueDetailsTTitleByAging").innerHTML = "In a moment...";
    var ajaxCall = $.ajax({
        url: '/erp/accounts/json/aging_ledgers_by_detail/',
        type: "POST",
        data: {
            "is_receivable": is_receivable,
            "is_advance": is_advance,
            "start_days": start_days,
            "end_days": end_days,
            "option": option
        },
        success: function (json) {
            if (json.response_message != "Success") {
                $("#DueDetailsByAging").modal('hide');
                $('#loadingmessage').hide();
                swal("", json.custom_message, "warning");
                return
            }
            var option = "";
            var tbl_header="";
            var age_list_html_title = "";
            if (json["overdue"] != undefined){
                option = "overdue";
                if(is_receivable) {
                    age_list_html_title = "Receivable Overdue";
                }
                else {
                    age_list_html_title = "Payable Overdue";
                }
                tbl_header = '<tr> <th>S. No</th> <th colspan=\"5\">Ledger</th> <th style="width: 160px;"> Overdue</th></tr>';
            }else if(json["total_due"] != undefined){
                option = "total_due";
                age_list_html_title = "Total Due";
                tbl_header = '<tr><th width="5%">S. No</th><th colspan="3">Ledger</th><th>Cr. Period</th><th>Total Due</th>' +
                    '<th> < 30 Days</th><th>30 - 60 Days</th> <th>60 - 90 Days</th><th> > 90 Days</th>' +
                    '<th>Advances</th><th>Un-Billed</th><th>Excess</th></tr>';
            }else if(json["due_in_days"] != undefined){
                option = "due_in_days";
                age_list_html_title = "Due in Next 10 Days";
                tbl_header = '<tr> <th>S. No</th> <th colspan=\"5\">Ledger</th><th> Due in Next 10 Days</th></tr>';
            }

            var age_list_html = "";
            var ledger_list_html = "";
            var total_row = "";

            if (isEmpty(JSON.parse(JSON.stringify(json[option])))) {
                age_list_html = "<tr><td colspan='7' class='text-center'>No ledgers Found</td></tr>";
                $(".a_consolidated_aging").attr("data-original-title","No Ledger found to download");
            } else {
                var i =1, dues_Total = 0, overdue_Total = 0;
                var ledger_records = JSON.parse(JSON.stringify(json[option]))
                $.each(ledger_records, function (index, value) {
                    if(option == 'overdue'){
                        var bill_list_html = "";
                        age_list_html += '<tr><td class="text-center">' + (i++) +
                            '</td><td  colspan=\"5\"><a role=\"button\" class=\"toggle_view\" onclick=\"javascript:$(\'.' + i + '\').toggle();\">' + value["name"] + '</td>';
                        $.each(JSON.parse(JSON.stringify(value["bills"])), function (bill_index, bill_value) {
                            if((Number(bill_value.credit_period) - Number(bill_value.age_days)) <= 0){
                                bill_list_html += '<tr class="' + i + '" style="display:none"><td></td>' +
                                    '<td class="text-center">' + (bill_index + 1) + '.</td>\n' +
                                    '<td class="text-center">' + bill_value["bill_date"] + '</td>\n' +
                                    '<td class="text-left">' + bill_value["bill_no"] + '</td>\n' +
                                    '<td class="text-right">' + bill_value["credit_period"] + '</td>\n' +
                                    '<td class="text-right">' + bill_value["age_days"] + '</td>\n' +
                                    '<td class="text-right">' + (bill_value["due"] * (is_receivable ? -1:1)).toFixed(2) + '</td>\n' +
                                    '</tr></a>';
                            } else {
                                value.due -= bill_value["due"];
                            }
                        });
                        age_list_html += '<td class="text-right">' + (value["due"] * (is_receivable ? -1:1)).toFixed(2) + '</td></tr>';
                        age_list_html += '<tr class="' + i + ' sub_table_header" style="display:none"><th></th><th>#</th><th>Bill Date</th><th>Bill No</th><th>Cr. Period</th><th>Age (Days)</th><th>Value</th></tr>';
                        age_list_html += bill_list_html;
                        overdue_Total += Number(value["due"]);
                    } else if(option == 'due_in_days'){
                        var excluded_bill_total = 0, n = 1, bill_list_html = "";
                        age_list_html += '<tr><td class="text-center">' + (i++) +
                            '</td><td  colspan=\"5\"><a role=\"button\" class=\"toggle_view\" onclick=\"javascript:$(\'.' + i + '\').toggle();\">' + value["name"] + '</td>';
                        $.each(JSON.parse(JSON.stringify(value["bills"])), function (bill_index, bill_value) {
                            if(((Number(bill_value.credit_period) - Number(bill_value.age_days)) > 0) && ((Number(bill_value.credit_period) - Number(bill_value.age_days)) <= 10)){
                                bill_list_html += '<tr class="' + i + '" style="display:none"><td></td>' +
                                            '<td class="text-right" width="5%">' + (n++) + '.</td>\n' +
                                            '<td class="text-center">' + bill_value["bill_date"] + '</td>\n' +
                                            '<td class="text-left">' + bill_value["bill_no"] + '</td>\n' +
                                            '<td class="text-right">' + bill_value["credit_period"] + '</td>\n' +
                                            '<td class="text-right">' + bill_value["age_days"] + '</td>\n' +
                                            '<td class="text-right">' + (bill_value["due"] * (is_receivable ? -1:1)).toFixed(2) +
                                            '</td></tr></a>';
                            } else {
                                value.due -= bill_value["due"];
                            }
                        });
                        age_list_html += '<td class="text-right">' + (value["due"] * (is_receivable ? -1:1)).toFixed(2) + '</td></tr>';
                        age_list_html += '<tr class="' + i + ' sub_table_header" style="display:none"><th></th><th width="10px">#</th><th>Bill Date</th><th>Bill No</th><th>Cr. Period</th><th>Age (Days)</th><th>Value</th></tr>';
                        age_list_html += bill_list_html;
                        dues_Total += Number(value["due"]);
                    } else {
                        age_list_html += '<tr id="ledger_ageing_detail_' + value.ledger_id + '"><td class="text-center serial_no"></td><td colspan="3" class="text-left table_ledger_name"><b>' + value.ledger
                            + '</b></td><td colspan="9" class="text-center"><p class="saving">Loading<span>.</span><span>.</span><span>.</span></p></td>';

                    }
                    age_list_html += '</tr>';
                });
                total_row = "<tr class='text-right'>"+
                                "<td colspan='2' class='total_value text-total-as-label'>TOTAL</td>";
                if(option == 'overdue'){
                    total_row = "<tr class='text-right'><td></td><td colspan='5' class='total_value text-total-as-label'>TOTAL</td>"+
                                "<td class='total_value text-totalamt-as-label'>" + (overdue_Total * (is_receivable ? -1:1)).toFixed(2) + "</td>";
                    $(".a_consolidated_aging").removeClass("disable_download").attr("data-original-title","Download as CSV");
                }else if(option == 'due_in_days'){
                    total_row = "<tr class='text-right'><td></td><td colspan='5' class='total_value text-total-as-label'>TOTAL</td>"+
                                "<td class='total_value text-totalamt-as-label'>" + (dues_Total * (is_receivable ? -1:1)).toFixed(2) + "</td>";
                    $(".a_consolidated_aging").removeClass("disable_download").attr("data-original-title","Download as CSV");
                } else {
                    total_row = '<tr id="total_due_ageing_splits"><td colspan="5">Total</td><td colspan="8"></td>';
                }
                total_row += "</tr>";
            }

            $(".DueByAgingLedgerTHead").html(tbl_header);
            document.getElementById("DueByAgingLedgerTBody").innerHTML = age_list_html;
            document.getElementById("DueDetailsTTitleByAging").innerHTML = age_list_html_title;
            $("#DueByAgingLedgerTBody").append(total_row);
            $('#loadingmessage').hide();
            $('#consolidated_aging').show();

            if (option == 'total_due') {
                var total_ageing = 0, age1_Total = 0, age2_Total = 0, age3_Total = 0, age4_Total = 0;
                var advance_Total = 0, excess_total = 0, billable_total = 0, ageing_records = [], ageing_records_temp = [];
                var cCount = Object.keys(ledger_records).length
                $.each(ledger_records, function (index, item) {
                    $.ajax({
                        url: '/erp/accounts/json/ledger_aging/',
                        type: "POST",
                        data: {"ledger_id": item.ledger_id},
                        success: function (result) {
                            try{
                                var ledger_total_ageing = Number(result.aging.age1) + Number(result.aging.age2) + Number(result.aging.age3) + Number(result.aging.age4)
                                ledger_total_ageing += Number(result.aging.advance) + Number(result.aging.excess) + Number(result.aging.billable);
                                ageing_records.push({
                                    excess_total: Number(result.aging.excess),
                                    billable_total: Number(result.aging.billable),
                                    advance_Total: Number(result.aging.advance),
                                    age1_Total: Number(result.aging.age1),
                                    age2_Total: Number(result.aging.age2),
                                    age3_Total: Number(result.aging.age3),
                                    age4_Total: Number(result.aging.age4),
                                    ledger_total_ageing: ledger_total_ageing
                                });
                                if(ledger_total_ageing.toFixed(2) == 0) {
                                     $('#ledger_ageing_detail_' + item.ledger_id).remove();
                                }
                                else {
		                                 ageing_records_temp.push({
		                                    excess_total: Number(result.aging.excess),
		                                    billable_total: Number(result.aging.billable),
		                                    advance_Total: Number(result.aging.advance),
		                                    age1_Total: Number(result.aging.age1),
		                                    age2_Total: Number(result.aging.age2),
		                                    age3_Total: Number(result.aging.age3),
		                                    age4_Total: Number(result.aging.age4),
		                                    ledger_total_ageing: ledger_total_ageing
		                                });
                                    $('#ledger_ageing_detail_' + item.ledger_id).html(
                                        '<td class="text-center">' + Number($('#ledger_ageing_detail_' + item.ledger_id).prevAll("tr").length + 1)
                                        + '</td><td colspan="3" class="text-left table_ledger_name"><b>' + item.ledger
                                        + '</b></td><td class="text-right">' + result.credit_period + ' days'
                                        + '</td><td class="text-right">' + ledger_total_ageing.toFixed(2)
                                        + '</td><td class="text-right">' + result.aging.age1.toFixed(2)
                                        + '</td><td class="text-right">' + result.aging.age2.toFixed(2)
                                        + '</td><td class="text-right">' + result.aging.age3.toFixed(2)
                                        + '</td><td class="text-right">' + result.aging.age4.toFixed(2)
                                        + '</td><td class="text-right">' + result.aging.advance.toFixed(2)
                                        + '</td><td class="text-right">' + result.aging.billable.toFixed(2)
                                        + '</td><td class="text-right">' + result.aging.excess.toFixed(2)
                                        + '</td>');
                                }
                                if (Object.keys(ledger_records).length == ageing_records.length) {
                                    $.each(ageing_records_temp, function(index, record) {
                                        total_ageing += record.ledger_total_ageing;
                                        age1_Total += record.age1_Total;
                                        age2_Total += record.age2_Total;
                                        age3_Total += record.age3_Total;
                                        age4_Total += record.age4_Total;
                                        advance_Total += record.advance_Total;
                                        billable_total += record.billable_total;
                                        excess_total += record.excess_total;
                                    });
                                    $("#total_due_ageing_splits").html(
                                        '<td></td><td colspan="3"><b>Total</b></td><td></td><td><b>' + total_ageing.toFixed(2)
                                        + '</b></td><td class="text-right"><b>' + age1_Total.toFixed(2)
                                        + '</b></td><td class="text-right"><b>' + age2_Total.toFixed(2)
                                        + '</b></td><td class="text-right"><b>' + age3_Total.toFixed(2)
                                        + '</b></td><td class="text-right"><b>' + age4_Total.toFixed(2)
                                        + '</b></td><td class="text-right"><b>' + advance_Total.toFixed(2)
                                        + '</b></td><td class="text-right"><b>' + billable_total.toFixed(2)
                                        + '</b></td><td class="text-right"><b>' + excess_total.toFixed(2) +'</b></td>');
                                }
                            }catch(e){
                                console.log("EX", e);
                            }
                            if (!--cCount) {
                                $(".a_consolidated_aging").removeClass("disable_download").attr("data-original-title","Download as CSV");
                            }
                        },
                        error: function (xhr, errmsg, err) {
                            console.log(xhr.status + ": " + xhr.responseText);
                        }
                    });
                });
            }
        },
        error: function (xhr, errmsg, err) {
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });
    $('#DueDetailsByAging').on('hidden.bs.modal', function () {
        ajaxCall.abort();
    })

}

function getBillingDetails(ledger_id, is_receivable, option, start_days, end_days, is_advance){
 $.ajax({
        url: '/erp/accounts/json/aging_ledgers/',
        type: "POST",
        data: {
            "is_advance": is_advance,
            "is_receivable": is_receivable,
            "start_days": start_days,
            "end_days": end_days,
            "option": option,
            "ledger_id": ledger_id
        },
        success: function (json) {
            var age_list_html = "";
            var ledger_list_html = "";

            if (isEmpty(JSON.parse(JSON.stringify(json.ledgers)))) {
                age_list_html = "<tr><td colspan='7' class='text-center'>No Bills Found</td></tr>";
                ledger_list_html = "<tr><td colspan='7' class='text-center'>No ledgers Found</td></tr>";
            }
            var total_due = 0;
            var idx = $('#bills_table tr.' + json.ledgers[0].id +' td:first').text();
            $.each(JSON.parse(JSON.stringify(json.ledgers)), function (index, value) {
                ledger_list_html += '<tr >' + '<td class="text-center">' + (idx) +
                    '</td><td colspan=\"5\"><a role=\"button\" class=\"toggle_view\" onclick=\"javascript:$(\'.' +
                    idx + '\').toggle();\">' + value["name"] + '</a></td><td class="text-right">' +
                    (value["due"] * (is_receivable ? -1:1)).toFixed(2) + '</td></tr>';
                ledger_list_html += '<tr class="' + idx +
                    ' sub_table_header" style="display: table-row"><th></th><th>#</th><th>Bill Date</th><th>Bill No</th><th>Cr. Period</th><th>Age (Days)</th><th>Value</th></tr>';
                $.each(JSON.parse(JSON.stringify(value["bills"])), function (bill_index, bill_value) {
                    ledger_list_html += '<tr class="' + idx + '" style="display:table-row"><td></td>' +
                        '<td class="text-center">' + (bill_index + 1) + '</td>\n' +
                        '<td class="text-center">' + bill_value["bill_date"] + '</td>\n' +
                        '<td class="text-center">' + bill_value["bill_no"] + '</td>\n' +
                        '<td class="text-center">' + bill_value["credit_period"] + '</td>\n' +
                        '<td class="text-center">' + bill_value["age_days"] + '</td>\n' +
                        '<td class="text-right">' + (bill_value["due"] * (is_receivable ? -1:1)).toFixed(2) + '</td>\n' +
                        '</tr></a>';
                });
            });
            $('#bills_table tr.' + json.ledgers[0].id).after(ledger_list_html).remove();
            $('#loadingmessage_part').hide();
            $('#bills_table').show();
        },
        error: function (xhr, errmsg, err) {
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });
}

function getAgeingDetails(ledger_id, is_receivable, option, start_days, end_days, is_advance) {
    $('#loadingmessage_part').show();
    $('#bills_table').hide();
    document.getElementById("ledgerTBody").innerHTML = "";
    document.getElementById("DueDetailsTTitle").innerHTML = "";
    if ((start_days == 0) || (end_days == 0)) {
        if ((start_days == 0) && (end_days == 0))
            age_list_html_title = 'Settled In Excess';
        else if (start_days == 0)
            age_list_html_title = ' < 30 Days';
        else if (end_days == 0)
            age_list_html_title = ' > 90 Days';
    } else {
        age_list_html_title = start_days + ' - ' + end_days + ' Days';
    }
    $("#DueDetailsTTitle").html(age_list_html_title);
    $.ajax({
        url: '/erp/accounts/json/aging_ledgers/',
        type: "POST",
        data: {
            "is_advance": is_advance,
            "is_receivable": is_receivable,
            "start_days": start_days,
            "end_days": end_days,
            "option": option,
            "ledger_id": ledger_id,
            "ledger_only": "true"
        },
        success: function (json) {
            var age_list_html = "";
            var ledger_list_html = "";

            if (isEmpty(JSON.parse(JSON.stringify(json.ledgers)))) {
                age_list_html = "<tr><td colspan='7' class='text-center'>No Bills Found</td></tr>";
                ledger_list_html = "<tr><td colspan='7' class='text-center'>No ledgers Found</td></tr>";
            }
            var total_due = 0;
            $.each(JSON.parse(JSON.stringify(json.ledgers)), function (index, value) {
                ledger_list_html += '<tr class=\''+ value["id"] +'\'>' + '<td class="text-center">' + (index + 1) +
                    '</td><td colspan=\"5\"><a role=\"button\" class=\"toggle_view\" onclick=\"getBillingDetails(\''+ value["id"] +'\', \''+ json.params.is_receivable +'\', \''+ json.params.option +'\', \''+ json.params.start_days +'\', \''+ json.params.end_days +'\', \''+ json.params.is_advance +'\')\">' + value["name"] + '</a></td><td class="text-right">' +
                    (value["due"] * (is_receivable ? -1:1)).toFixed(2) + '</td></tr>';
                total_due += parseFloat((value["due"] * (is_receivable ? -1:1)).toFixed(2));
            });
            ledger_list_html += "<tr class='text-right'><td colspan='6' class='total_value text-total-as-label'>TOTAL</td><td class='total_value text-totalamt-as-label'>" +
                total_due.toFixed(2) + "</td></tr>";
            document.getElementById("ledgerTBody").innerHTML = ledger_list_html;
            $('#loadingmessage_part').hide();
            $('#bills_table').show();
        },
        error: function (xhr, errmsg, err) {
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });
    var label = is_receivable == 0 ? "Payable" : "Receivable";
    ga('send', 'event', 'Accounts', 'Dashboard - view ' + label + ' Ageing details', $('#enterprise_label').val());
}

