"""
"""
__author__ = 'pavithra'

GET_PURCHASE_PERFORMANCE_QUERY = """
	SELECT DATE_FORMAT(pomds.due_date, "%b' %Y") as `po_month`, 
	SUM(IF((@ontimesum:=CASE WHEN @prev = `pomds`.`po_id` THEN @ontimesum + `pomds`.`qty` ELSE `pomds`.`qty` END) <= (
	SELECT SUM(`gm`.`acc_qty`) FROM `grn_material` `gm` 
	INNER JOIN `grn` `g` ON `gm`.`grnNumber` = `g`.`grn_no` AND `gm`.`enterprise_id` = `g`.`enterprise_id` 
	AND `g`.`status` > - 1 
	WHERE `gm`.`enterprise_id` = `pomds`.`enterprise_id` AND `gm`.`item_id` = `pomds`.`item_id` 
	AND `gm`.`make_id` = `pomds`.`make_id` AND `gm`.`po_no` = `pomds`.`po_id` AND `g`.`inward_date` <= `pomds`.`due_date`),
	1, 0)) `on_time`, SUM(
	IF((@delaysum:=CASE WHEN @prev1 = `pomds`.`po_id` THEN @delaysum + `pomds`.`qty` ELSE `pomds`.`qty` END) <= (
	SELECT SUM(`gm`.`acc_qty`) FROM `grn_material` `gm` 
	INNER JOIN `grn` `g` ON `gm`.`grnNumber` = `g`.`grn_no` AND `gm`.`enterprise_id` = `g`.`enterprise_id` 
	AND `g`.`status` > - 1 
	WHERE `gm`.`enterprise_id` = `pomds`.`enterprise_id` AND `gm`.`item_id` = `pomds`.`item_id` 
	AND `gm`.`make_id` = `pomds`.`make_id` AND `gm`.`po_no` = `pomds`.`po_id` AND `g`.`inward_date` <= `pomds`.`due_date`), 
	0, 1)) `delayed`, @prev:=`pomds`.`po_id`, @prev1:=`pomds`.`po_id` 
	FROM purchase_order_material_delivery_schedules pomds CROSS JOIN (
	SELECT @ontimesum:=0, @delaysum:=0, @prev:=0, @prev1:=0) params 
	WHERE pomds.enterprise_id = {enterprise_id} AND pomds.due_date >=  CURDATE() - INTERVAL 6 MONTH 
	GROUP BY YEAR(pomds.due_date) , MONTH(pomds.due_date) 
	ORDER BY pomds.due_date ASC """

GET_PENDING_PO_QUERY = """SELECT 
            p.party_id,
            p.currency,
            p.party_name,
            a.enterprise_id,
            a.id as po_id,
            CONCAT(a.financial_year,
            '/',
            CASE
                WHEN a.type = 1 THEN 'JO'
                ELSE 'PO'
            END,
            '/',
            CASE
                WHEN LENGTH(a.orderno) < 6 THEN LPAD(a.orderno, 6, 0)
                ELSE a.orderno
            END,
            IFNULL(a.sub_number, '')) AS po_no,
            a.orderno,
            a.indent_no,
            a.indent_type,
            a.supplier_id,
            a.type,
            DATE_FORMAT(a.order_date, '%d/%m/%Y') as order_date,
            IFNULL(c.drawing_no, '') AS drawing_no,
            c.name AS item_name,
            IF(a.is_mask_po = 0, b.po_price, 0.0) AS unit_price,
            u.unit_name,
            b.pur_qty,
            IFNULL((SELECT 
                            SUM(acc_qty)
                        FROM
                            grn_material AS g,
                            grn AS gr
                        WHERE
                            gr.grn_date <= '{as_on}' AND
                            g.po_no = a.id AND g.item_id = b.item_id
                                AND g.make_id = b.make_id
                                AND g.is_faulty = b.is_faulty
                                AND gr.grn_no = g.grnNumber
                                AND g.rec_grn_id IS NULL
                                AND gr.status > - 1
                                AND ((b.make_id = g.make_id
                                AND b.enterprise_id = g.enterprise_id)
                                OR (b.make_id IS NULL AND g.make_id IS NULL))),
                    0) AS received_qty,
            (b.pur_qty - IFNULL((SELECT 
                            SUM(acc_qty)
                        FROM
                            grn_material AS g,
                            grn AS gr
                        WHERE
                            gr.grn_date <= '{as_on}' AND
                            g.po_no = a.id AND g.item_id = b.item_id
                                AND g.make_id = b.make_id
                                AND g.is_faulty = b.is_faulty
                                AND gr.grn_no = g.grnNumber
                                AND g.rec_grn_id IS NULL
                                AND gr.status > - 1
                                AND ((b.make_id = g.make_id
                                AND b.enterprise_id = g.enterprise_id)
                                OR (b.make_id IS NULL AND g.make_id IS NULL))),
                    0)) AS pending_qty,
            a.type,
            a.financial_year,
            c.makes_json AS make_name,
            c.id as item_id
        FROM
            purchase_order AS a,
            purchase_order_material AS b,
            materials AS c,
            unit_master AS u,
            party_master AS p,
            party_registration_detail as prd
        WHERE
                b.pid = a.id
                AND b.item_id = c.id
                AND p.party_id = a.supplier_id
                AND c.enterprise_id = p.enterprise_id
                AND u.unit_id = c.unit
                AND u.enterprise_id = c.enterprise_id
                AND p.enterprise_id = p.enterprise_id
                AND b.enterprise_id = a.enterprise_id
                AND a.supplier_id = p.party_id
                AND prd.label = "GSTIN"
                AND prd.details = '{gst_no}'
                AND p.party_id = prd.party_id
                AND prd.enterprise_id = {enterprise_id}
                AND (b.pur_qty - IFNULL((SELECT 
                            SUM(acc_qty)
                        FROM
                            grn_material AS g,
                            grn AS gr
                        WHERE
                            gr.grn_date <= '{as_on}' AND
                            g.po_no = a.id AND g.item_id = b.item_id
                                AND g.make_id = b.make_id
                                AND g.is_faulty = b.is_faulty
                                AND gr.grn_no = g.grnNumber
                                AND g.rec_grn_id IS NULL
                                AND gr.status > - 1),
                    0)) > 0
                AND a.status = 2
        ORDER BY orderno;"""
