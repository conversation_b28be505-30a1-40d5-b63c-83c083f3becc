"""
"""
import base64
try:
    import cString<PERSON> as String<PERSON>
except ImportError:
    from io import String<PERSON>
from datetime import datetime
from decimal import Decimal
from math import ceil

import pdfkit
import qrcode
from django.template import Context
from django.template.loader import get_template
from num2words import num2words

from erp import helper, DEFAULT_MAKE_ID
from erp.icd import logger
from erp.masters.backend import MasterService
from util.document_compiler import PDFGenerator
from util.document_properties import getStyleSheet
from util.helper import getAbsolutePath, writeFile
from util.properties import GST_LABEL

ICD_DOC_FOOTER_PATH = '/site_media/tmp/icd_footer_%s.html'

__author__ = 'kalaivanan'

styles = getStyleSheet()
TARGET_PATH = '/site_media/tmp/debit_note.pdf'
DEBIT_NOTE_DOC_PATH = '/site_media/tmp/debit_note_%s.pdf'


class DebitNoteDocumentGenerator(PDFGenerator):
	"""
	Generates a PDF replica of the GRN that will be available printed on a pre-printed sheet.
	"""
	def __init__(self, target_file_path=TARGET_PATH):
		super(DebitNoteDocumentGenerator, self).__init__(target_file_path)
		return

	def getICDMaterialDetails(self, source=None):
		note_materials = []
		available_taxes = []

		logger.info('No of Materials: %s' % len(source.items))

		index = 0
		total = Decimal(0)
		total_qty = Decimal(0)
		net_cgst = 0
		net_sgst = 0
		net_igst = 0
		for item in source.items:
			item_total = Decimal(item.rate * item.quantity) * (1 if item.is_credit else -1)

			if source.note_receipt_map.receipt.received_against == "Delivery Challan" or item.dc_id != 0:
				item_get_code = "%s" % item.dc.getInternalCode()
			elif source.note_receipt_map.receipt.received_against == "Purchase Order" or source.note_receipt_map.receipt.received_against == "Job Work":
				item_get_code = "" if item.purchase_order is None else "%s" % item.purchase_order.getCode()
			else:
				item_get_code = ""

			if item_total != 0:
				unit_name = item.material.unit
				if item.alternate_unit_id:
					unit_name = helper.getUnitName(enterprise_id=item.enterprise_id, unit_id=item.alternate_unit_id)

				index += 1
				total += Decimal("%0.2f" % item_total)
				total_qty += Decimal(item.quantity)
				make = ""
				cgst_rate = 0.00
				sgst_rate = 0.00
				igst_rate = 0.00
				cgst_value = 0.00
				sgst_value = 0.00
				igst_value = 0.00
				if item.make_id != DEFAULT_MAKE_ID:
					part_no = helper.getMakePartNumber(
						enterprise_id=item.enterprise_id, item_id=item.item_id, make_id=item.make_id)
					make = "[%s%s]" % (item.make.__repr__(), (" - %s" % part_no) if part_no else "")
				material_name = item.material.name
				if item.material and item.material.makes_json != "":
					make_name = helper.constructDifferentMakeName(item.material.makes_json)
					if make_name:
						material_name = material_name + " [" + make_name + "]"
				item_description = "%s%s %s" % (
					(" %s -" % item.material.drawing_no) if item.material.drawing_no else "", material_name, '[Faulty]' if item.is_faulty == 1 else "")
				rate = "%0.2f %s" % (item.rate, "(Cr)" if item.is_credit else "(Dr)")
				amount = "%0.2f" % (item.rate * item.quantity)
				for item_tax in item.getTaxesOfType("CGST"):
					cgst_rate = item_tax.tax.net_rate if item_tax else 0
					if cgst_rate != 0:
						cgst_value = Decimal(item.quantity) * Decimal(item.rate) * Decimal(cgst_rate) / 100
					net_cgst += Decimal("%0.2f" % cgst_value) * (1 if item.is_credit else -1)
				for item_tax in item.getTaxesOfType("SGST"):
					sgst_rate = item_tax.tax.net_rate if item_tax else 0
					if sgst_rate != 0:
						sgst_value = Decimal(item.quantity) * Decimal(item.rate) * Decimal(sgst_rate) / 100
					net_sgst += Decimal("%0.2f" % sgst_value) * (1 if item.is_credit else -1)
				for item_tax in item.getTaxesOfType("IGST"):
					igst_rate = item_tax.tax.net_rate if item_tax else 0
					if igst_rate != 0:
						igst_value = Decimal(item.quantity) * Decimal(item.rate) * Decimal(igst_rate) / 100
					net_igst += Decimal("%0.2f" % igst_value) * (1 if item.is_credit else -1)
				icd_items = {
					"item_index": index, "item_code": item_get_code, "item_description": item_description, "make": make,
					"reason": item.reason, "quantity": "%0.3f" % item.quantity, "unit_name": unit_name, "rate": rate,
					"amount": amount, "cgst_rate": "%0.2f" % cgst_rate, "cgst_value": "%0.2f" % cgst_value,
					"sgst_rate": "%0.2f" % sgst_rate, "sgst_value": "%0.2f" % sgst_value, "igst_rate": "%0.2f" % igst_rate,
					"igst_value": "%0.2f" % igst_value, "hsn_code": item.hsn_code}
				note_materials.append(icd_items)

		for item in source.non_stock_items:
			item_total = Decimal(item.rate * item.quantity) * (1 if item.is_credit else -1)
			po_code = ""
			make = ""
			if item.po_no:
				if source.note_receipt_map.receipt.received_against != "Delivery Challan":
					po_code = item.purchase_order.getCode() if item.purchase_order and item.po_no != 0 else ""
				else:
					po_code = "%s" % item.dc.getInternalCode()
			if item_total != 0:
				unit_name = item.unit.unit_name if item.unit.unit_name else ""
				index += 1
				total += Decimal("%0.2f" % item_total)
				total_qty += item.quantity
				cgst_rate = 0.00
				sgst_rate = 0.00
				igst_rate = 0.00
				cgst_value = 0.00
				sgst_value = 0.00
				igst_value = 0.00

				rate = "%0.2f %s" % (item.rate, "(Cr)" if item.is_credit else "(Dr)")
				amount = "%0.2f" % (item.rate * item.quantity)
				for item_tax in item.getTaxesOfType("CGST"):
					cgst_rate = item_tax.tax.net_rate if item_tax else 0
					if cgst_rate != 0:
						cgst_value = item.quantity * item.rate * Decimal(cgst_rate) / 100
					net_cgst += Decimal("%0.2f" % cgst_value) * (1 if item.is_credit else -1)
				for item_tax in item.getTaxesOfType("SGST"):
					sgst_rate = item_tax.tax.net_rate if item_tax else 0
					if sgst_rate != 0:
						sgst_value = item.quantity * item.rate * Decimal(sgst_rate) / 100
					net_sgst += Decimal("%0.2f" % sgst_value) * (1 if item.is_credit else -1)
				for item_tax in item.getTaxesOfType("IGST"):
					igst_rate = item_tax.tax.net_rate if item_tax else 0
					if igst_rate != 0:
						igst_value = item.quantity * item.rate * Decimal(igst_rate) / 100
					net_igst += Decimal("%0.2f" % igst_value) * (1 if item.is_credit else -1)
				icd_items = {
					"item_index": index, "item_code": po_code, "item_description": item.description, "make": make,
					"reason": item.reason, "quantity": "%0.3f" % item.quantity, "unit_name": unit_name, "rate": rate,
					"amount": amount, "cgst_rate": "%0.2f" % cgst_rate, "cgst_value": "%0.2f" % cgst_value,
					"sgst_rate": "%0.2f" % sgst_rate, "sgst_value": "%0.2f" % sgst_value, "igst_rate": "%0.2f" % igst_rate,
					"igst_value": "%0.2f" % igst_value, "hsn_code": item.hsn_code}
				note_materials.append(icd_items)
			logger.info('Total Purchase Amount: %s CGST: %s SGST: %s IGST: %s' % (total, net_cgst, net_sgst, net_igst))
		# Tax Calculation
		sorted_taxes = source.getTaxes()
		cascading_tax = Decimal(net_cgst + net_sgst + net_igst)
		for note_tax in sorted_taxes:
			tax_amount = Decimal(note_tax.tax.net_rate) * (
				(Decimal(total + cascading_tax)) if note_tax.tax.is_compound else Decimal(total)) / 100
			cascading_tax += tax_amount
			taxes = {
				'tax_name': note_tax.tax.name, 'tax_rate': note_tax.tax.net_rate,
				'tax_value': '%0.2f (%s)' % (abs(tax_amount), "Cr" if tax_amount > 0 else ("" if tax_amount == 0 else "Dr"))}
			available_taxes.append(taxes)

		total_value = '%0.2f (%s)' % (abs(total), "Cr" if total > 0 else ("" if total == 0 else "Dr"))
		total_cgst_value = '%0.2f %s' % (
				abs(net_cgst), "(%s)" % "Cr" if net_cgst > 0 else ("" if net_cgst == 0 else "Dr"))
		total_sgst_value = '%0.2f %s' % (
				abs(net_sgst), "(%s)" % "Cr" if net_sgst > 0 else ("" if net_sgst == 0 else "Dr"))
		total_igst_value = '%0.2f %s' % (
				abs(net_igst), "(%s)" % "Cr" if net_igst > 0 else ("" if net_igst == 0 else "Dr"))

		grand_total = Decimal(total + cascading_tax + source.round_off * (1 if source.is_credit else -1))
		grand_total_value = "%0.2f (%s)" % (abs(grand_total), "Cr" if grand_total > 0 else ("" if grand_total == 0 else "Dr"))
		if abs(grand_total) > 10000000000:
			grand_total_in_words = ""
		else:
			grand_total_in_words = PDFGenerator.getTotalInWords(value=abs(grand_total), currency=source.currency)
		note_total_details = {
			'total_qty': total_qty, 'total': total_value, 'net_cgst': total_cgst_value, 'net_sgst': total_sgst_value,
			'net_igst': total_igst_value, 'round_off': "%0.2f" % source.round_off, 'grand_total': grand_total_value,
			'grand_total_in_words': grand_total_in_words}
		return note_materials, available_taxes, note_total_details

	def generatePDF(self, source=None):
		try:
			# logger.info('Generating PDF for GRN: %s' % self.receipt)
			countries = MasterService().getCountries()
			form_name = "%s NOTE" % ("CREDIT" if source.is_credit else "DEBIT")
			if source.enterprise.images and source.enterprise.images.logo and source.enterprise.images.logo != "":
				logo = "data:image/%s;base64,%s" % (source.enterprise.images.ext, base64.encodestring(source.enterprise.images.logo))
			else:
				logo = ""

			country_list = []
			enterprise_country = ""
			for country in countries:
				if source.enterprise.country_code == country.code:
					enterprise_country = country.name
				country_list.append({"country_code": country.code, "country_name": country.name})

			if source.enterprise.address_2 and source.enterprise.address_2 != "":
				address = source.enterprise.address_1 + ", " + source.enterprise.address_2
			else:
				address = source.enterprise.address_1

			enterprise_address = "{address}, {city}, {pin_code} {state}, {country}".format(
				address=address, city=source.enterprise.city, pin_code=source.enterprise.pin_code, state=source.enterprise.state,
				country=enterprise_country.upper())

			gst_label = GST_LABEL
			gst_detail = ""
			for item in source.enterprise.registration_details:
				if item.label.find("GST") != -1:
					gst_label = item.label
					gst_detail = item.details

			note_date = str(datetime.strptime(str(source.created_on), '%Y-%m-%d %H:%M:%S')) if source.created_on is not None and source.created_on != '0000-00-00 00:00:00' else source.created_on
			receipt_date = str(datetime.strptime(str(source.approved_on), '%Y-%m-%d %H:%M:%S')) if source.approved_on is not None and source.approved_on != '0000-00-00 00:00:00' else source.approved_on
			invoice_date = str(datetime.strptime(str(source.inv_date), '%Y-%m-%d %H:%M:%S')) if source.inv_date is not None and source.inv_date != '0000-00-00 00:00:00' else source.inv_date
			verified_date = str(datetime.strptime(str(source.approved_on), '%Y-%m-%d %H:%M:%S')) if source.approved_on is not None and source.approved_on != '0000-00-00 00:00:00' else source.approved_on

			mail_subject = "In Reference to %s <b>Invoice No.: %s (<i>Date: %s Value: %s</i>)" % (
				"our" if source.is_credit else "your", source.inv_no, source.inv_date.strftime('%d/%m/%Y') if source.inv_date is not None and source.inv_date != '0000-00-00 00:00:00' else "",
				source.inv_value)
			body = "We wish to advice having %s your Account with us as follows :" % (
				"credited" if source.is_credit else "debited")

			note_item_details, note_taxes, summary_details = self.getICDMaterialDetails(source=source)

			item_count = len(source.items)
			logger.info('No of Materials: %s' % item_count)
			appendix_pages = 0
			tax_count = ceil(len(source.taxes) / 3.0)
			logger.info("The Total Tax Count %s:" % tax_count)
			if (item_count + tax_count) >= 4:
				# Approximate space estimate to decide if appendix page is necessary
				appendix_pages = ceil(item_count / 20.0)
			logger.info('Appendix Pages: %s' % appendix_pages)
			note_drafter = source.note_creator if source.note_creator else ""
			note_approver = source.note_approver if source.note_approver and source.status > 2 else ""

			irn_scanning_code = None
			irn_details = ''
			irn_ack_json = source.irn_ack_json

			if irn_ack_json:
				irn_qr_code_data = irn_ack_json['SignedQRCode']

				if irn_qr_code_data:
					qr_code = qrcode.QRCode(version=1, error_correction=qrcode.constants.ERROR_CORRECT_H, box_size=10,
											border=4, )
					qr_code.add_data(irn_qr_code_data)
					qr_code.make(fit=True)
					qr_img = qr_code.make_image()
					buffer = cStringIO.StringIO()
					qr_img.save(buffer, format="JPEG")
					qr_img_base64 = base64.b64encode(buffer.getvalue())
					irn_scanning_code = "data:image/%s;base64,%s" % ("jpeg", qr_img_base64)

				irn_details = {
					'ack_no': irn_ack_json['AckNo'], 'ack_date': irn_ack_json['AckDt'], 'irn': irn_ack_json['Irn'],
					'irn_scanning_code': irn_scanning_code}

			context = Context({
				'form_name': form_name, 'enterprise_logo': logo, 'enterprise_address': enterprise_address, 'note_date': note_date,
				'receipt_date': receipt_date, 'verified_date': verified_date, 'source': source, 'gst_label': gst_label,
				'gst_detail': gst_detail, 'subject': mail_subject, 'body': body, 'invoice_date': invoice_date,
				'note_item_details': note_item_details, 'note_taxes': note_taxes, 'summary_details': summary_details,
				'appendix_pages': appendix_pages, 'note_drafter': note_drafter, 'note_approver': note_approver,
				'irn_details': irn_details, 'country_list': country_list, 'template_title': "Note Document"})

			footer_data = get_template(getAbsolutePath('/templates/auditing/icd_print_footer.html')).render(context)
			footer_file_name = ICD_DOC_FOOTER_PATH % source.id
			writeFile(footer_data, footer_file_name)

			options = {
				'page-size': 'A4', 'orientation': 'Portrait', 'margin-top': '9', 'margin-right': '5', 'margin-bottom': '15',
				'margin-left': '5', 'title': source.getCode(), '--header-right': 'Page [page] of [topage]',
				'--header-font-size': '9', 'footer-html': getAbsolutePath(footer_file_name)}

			css = [
				getAbsolutePath('/site_media/css/bootstrap.css'), getAbsolutePath('/site_media/css/invoice_template_preview.css'),
				getAbsolutePath('/site_media/css/invoice_template.css')]

			template_src = getAbsolutePath('/templates/auditing/icd_print.html')

			template = get_template(template_src).render(context)
			data = pdfkit.from_string(template, False, options=options, css=css)
			return data
		except Exception as e:
			logger.exception('Something went wrong while creating PDF self.canvas - %s' % e)
