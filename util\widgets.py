"""
Custom widgets to be used across the Project's presentation layer
"""
from itertools import chain

from django import forms
from django.forms.widgets import CheckboxInput
from django.utils.encoding import force_str
from django.utils.html import conditional_escape
from django.utils.safestring import mark_safe

__author__ = 'kalaivanan'


class CheckboxSelectMultipleRow(forms.CheckboxSelectMultiple):
	"""
	Customized CheckboxSelectMultiple widget that renders the Checkbox group choices as table columns instead of the
	default list format
	"""

	def render(self, name, value, attrs=None, choices=()):
		if value is None: value = []
		has_id = attrs and 'id' in attrs
		final_attrs = self.build_attrs(attrs, name=name)
		output = [u'']
		# Normalize to strings
		str_values = set([force_str(v) for v in value])
		for i, (option_value, option_label) in enumerate(chain(self.choices, choices)):
			# If an ID attribute was given, add a numeric index as a suffix,
			#  so that the checkboxes don't all have the same ID attribute.
			if has_id:
				final_attrs = dict(final_attrs, id='%s_%s' % (attrs['id'], i))
				label_for = u' for="%s"' % final_attrs['id']
			else:
				label_for = ''

			cb = CheckboxInput(final_attrs, check_test=lambda value: value in str_values)
			option_value = force_str(option_value)
			rendered_cb = cb.render(name, option_value)
			option_label = conditional_escape(force_str(option_label))
			output.append(u'<td>%s<label%s> %s</label></td>' % (rendered_cb, label_for, option_label))
		output.append(u'')
		return mark_safe(u'\n'.join(output))


class CheckboxSelectMultipleColumn(forms.CheckboxSelectMultiple):
	"""
	Customized CheckboxSelectMultiple widget that renders the Checkbox group choices as table rows instead of the
	default list format
	"""

	def render(self, *args, **kwargs):
		output = super(CheckboxSelectMultipleColumn, self).render(*args, **kwargs)
		return mark_safe(
				output.replace(u'<ul>', u'').replace(u'</ul>', u'').replace(u'<li>', u'<tr><td>').replace(u'</li>',
				                                                                                          u'</td></tr>'))


class RadioSelectAsRow(forms.RadioSelect):
	"""
	Customized RadioSelect widget that renders the Radio group choices as table columns instead of the
	default list format
	"""

	def render(self, *args, **kwargs):
		output = super(RadioSelectAsRow, self).render(*args, **kwargs)
		return mark_safe(
			output.replace(u'<ul>', u'').replace(u'</ul>', u'').replace(u'<li>', u'<td border=\'0\'>').replace(
				u'</li>', u'</td>'))
