function initializePage(){
	$(".chosen-select").chosen();
    loadPurchaseOrders();
	if(getURLParameter(window.location.search,'type') == 'edit'){
	    editrow(getURLParameter(window.location.search,'pid'), getURLParameter(window.location.search,'status'));
	}

	TableHeaderFixed();
    updateFilterText();
    setHeightForTable();
    $( window ).resize();
    closeFilterOption();

    $("#filterPO").click(function () {
        loadPurchaseOrders();
    });

    var iTable;
    if ($("#indent_access").val() == 'False'){
         $('.create_indent_po').attr('disabled','disabled').css({opacity: 0.4});
         $('.create_indent_po').off('click');
        return false;
    }
    else {
        iTable = $('#indent_po').DataTable({
            fixedHeader: false,
            "search": {
                "smart": false
            },
            "columns": [
                null,null,
                { "type": "date" },
                null
            ]
        });
        iTable.on("draw",function() {
            var keyword = $('#indent_po_filter > label:eq(0) > input').val();
            $('#indent_po').unmark();
            $('#indent_po').mark(keyword,{});
        });
    }
}

function getURLParameter(url, name){
    return (RegExp(name + '=' + '(.+?)(&|$)').exec(url) || [, null])[1];
}
function TableHeaderFixed(){
    if ($('#indent_access').val() === 'True'){
    	var cols = [
			null,null,
			{ "type": "date" },
			null,
			{ "type": "date" },
			null,null,null,null,null,null
			]
		var aTarget = 12;
	}
	else {
		var cols = [
			null,null,
			{ "type": "date" },
			null,
			{ "type": "date" },
			null,null,null,null,null
			]
	}
	oTable = $('#po_list').DataTable({
		fixedHeader: false,
        "scrollY": Number($(document).height() - 230),
        "scrollX": true,
		"pageLength": 50,
		"search": {
			"smart": false
		},
		"columns": cols
	});
	updatePOListJson();
	oTable.on("draw",function() {
		var keyword = $('#po_list_filter > label:eq(0) > input').val();
		$('#po_list').unmark();
		$('#po_list').mark(keyword,{});
		setHeightForTable();
		updatePOListJson();
        listTableHoverIconsInit('po_list');
	});
	oTable.on('page.dt', function() {
	  $('html, body').animate({
		scrollTop: $(".dataTables_wrapper").offset().top - 15
	   }, 'slow');
	   listTableHoverIconsInit('po_list');
	});
	oSettings = oTable.settings();
	listTableHoverIconsInit('po_list');
}

function updatePOListJson(){
    setTimeout(function(){
        POListjsonObj = [];
        var rows = oTable.rows( { page : 'current'} ).data();
        for(var i=0;i<rows.length;i++) {
            var selectedRow = $(rows[i][3]);
            var selectedIDRow = $(rows[i][1]);
            po = {}
            po ["poId"] = selectedIDRow.selector;
            po ["poNumber"] = selectedRow.text().trim();
            POListjsonObj.push(po);
        }
        localStorage.setItem('poListNav', JSON.stringify(POListjsonObj));
    },10);
}

function updateFilterText() {
    $(".filtered-date b").text($("#reportrange").find("span").text());
    $(".filtered-project b").text($("#project option:selected").text());
    $(".filtered-status b").text($("#status option:selected").text());
}

function loadPurchaseOrders() {
    var filter = {
        since: $("#fromdate").val(),
        till: $("#todate").val(),
        status: $("#status option:selected").val(),
        project_id: $("#project option:selected").val(),
        order_type: $("#id_order_type").val()
    }

    $('#loading').show();
    $.ajax({
        url: "/erp/purchase/json/po/loadpos/",
        type: "post",
        datatype: "json",
        data: filter,
        success: function (response) {
            $("#po_list").find("tr:gt(0)").remove();
                        $('#po_list').DataTable().clear();
                        $('#po_list').DataTable().destroy();
            $.each(response, function (i, item) {
                // CSRF_TOKEN required while submitting the request form generated below
                var csrf_token = document.getElementsByName('csrfmiddlewaretoken')[0].value;
                if (item[8] == 0) {
                    status="Draft";
                } else if (item[8] == 1) {
                    status="Reviewed";
                } else if (item[8] == 2) {
                    status="Approved";
                } else {
                    status="Rejected";
                }
                po_code = ""
                po_no = '' + item[2];
                if(po_no == '0') {
                    po_code = item[0];
                }
                else {
                    while(po_no.length < 7) {
                        po_no = '0' + po_no;
                    }
                    if (item[13] == 0) {
                        po_code = item[10] + "/PO/" + po_no;
                    }
                    else if(item[13] == 1) {
                        po_code = item[10] + "/JO/" + po_no;
                    }
                    else if(item[13] == 1) {
                        po_code = item[10] + "/WO/" + po_no;
                    }
                }
                if (item[11] == null || item[11]==""){
                    indent_no = "-NA-"
                } else {
                    indent_no = item[11];
                }

                var dDate = item[1];
                dDate = moment(dDate, "DD.MM.YYYY").format("MMM D, YYYY");
                if(dDate.toLowerCase().trim() == "invalid date") { dDate = '-'; }
                var pDate = item[3];
                pDate = moment(pDate, "DD.MM.YYYY").format("MMM D, YYYY");
                if(pDate.toLowerCase().trim() == "invalid date") { pDate = '-'; }
                if ($('#indent_access').val() === 'True'){
                    var indent_column = "<td class='text-center'>" +indent_no + "  </td>"
                }
                else{
                    var indent_column = ""
                }
                if (item[14] == 1 && $("#is_super_user").val().toLowerCase() == 'false') {
                    var document_process = `<td class='td_status'></td>`
                    var edit_po_document = `<td class='text-center'>
                                                <a role='button' style='min-width: 120px; display: inline-block;' >
                                                    ${po_code}
                                                </a>
                                            </td>`
                }else{
                    var document_process = `<td class='td_status'>
                                     <a class='${status.toLowerCase()} pdf_genereate table-inline-icon-bg' role='button'>
                                         ${status}
                                     </a>
                                     <span class='table-inline-icon inline-icon-document hide' data-tooltip='tooltip' data-placement='left' title='Preview' onclick="generate_pdf_ajax(${item[0]}, ${item[13]}, ${item[8]}, false, ${item[14]})">
                                         <i class="fa fa-file-text"></i>
                                     </span>
                                     <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit' role='button' data-status='${item[8]}' onclick='javascript:editrow(${item[0]},${item[8]});'>
                                         <i class='fa fa-pencil'></i>
                                     </span>
                                     <span class='table-inline-icon hide' data-tooltip='tooltip' data-placement='left' title='Edit in New Tab' role='button' data-status='${item[8]}' onclick='javascript:editrow(${item[0]},${item[8]},"_blank");'>
                                         <i class='fa fa-external-link'></i>
                                     </span>
                                 </td> `
                    var edit_po_document = `<td class='text-center'>
                                                <a role='button' style='min-width: 120px; display: inline-block;' class='edit_link_code' onclick='editrow(${item[0]}, ${item[13]}, ${item[8]});'>
                                                    ${po_code}
                                                </a>
                                            </td>`

                }

                 var row = `<tr align='center' data-poid = ${item[0]}>
                                <td align='center'>${(i+1)}.</td>
                                <td class='exclude_export' hidden='hidden'>${item[0]}</td>
                                <td>${dDate}</td>
                                ${edit_po_document}
                                <td class='td_po_date'>${pDate}</td>
                                <td class='text-left'>${item[4]}</td>
                                <td class='text-right'>${item[5]}</td>
                                <td class='text-left'>${item[6]}</td>
                                ${indent_column}
                                <td hidden='hidden' class='exclude_export'>${status}</td>
                                ${document_process}
                            </tr>`;
                $('#po_list').append(row);
            });
            if ($("#po_list tr").length == 1){
                var row = "";
                $('#po_list').append(row);
            }

            TableHeaderFixed();
            updateFilterText();
            setHeightForTable();
            $( window ).resize();
            closeFilterOption();
            $('#loading').hide();
        }
    });
}

function editrow(po_id, status, openTarget="") {
    $("#loading").show();
    $("#id-edit_po_id").val(po_id);
    $("#id-edit_po_status").val(status);
    $("#id-edit_po_form").attr("target", openTarget);
    $("#id-edit_po_form").submit();
    $("#loading").hide();
}

