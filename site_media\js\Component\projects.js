$(document).ready(function() {
    if ($('.component_project').length > 0) {
        $('.component_project').each(function(index, element) {
            constructProject(element);
        });
    }
});

const enableProjectEdit = (field) => {
    $(field).closest("label").next("select").removeAttr("disabled");
	$('.chosen-select').trigger('chosen:updated');
	$(field).addClass("hide");
}

const constructProject = async(current) => {
    const projectList = await getProjects();
    const projectWise = projectList ?projectList.project_wise : null;
    $('#is_project_wise_pl').val(projectWise)
    if(!projectWise){
       $('#expRev').hide();
    }
    const formattedProjectList = formatProjectListForDropdown(projectList);
    const value = $(current).attr("data-value");

    const isEditDisable = $(current).attr("data-isEditDisable");
    let editIcon = "";
    if(isEditDisable == 'true') {
        const isSuper = $(current).attr("data-isSuper");
        if(isSuper) {
            editIcon += `<a class="super_edit_field super_edit_for_draft hide" onclick="enableProjectEdit(this);" data-tooltip="tooltip" data-placement="left" title="Super Edit">
                            <i class="fa fa-pencil super_edit_in_field"></i>
                         </a>`;
        }
        else {
            editIcon += `<a class="super_edit_field super_edit_for_draft hide" onclick="" data-tooltip="tooltip" data-placement="left" title="Only Super User can edit this field">
                            <i class="fa fa-pencil super_edit_in_field" style="color:#777;"></i>
                        </a>`;
        }

    }

    const sessionProject = localStorage.getItem("project");
    const project = JSON.parse(sessionProject); // Declare project variable here
    let className = ''
    const optionElements = formattedProjectList.map(option => {
        const projectType = (option.parent_id == 0) ? 'Primary' : 'Secondary';
        const primaryProject = (option.parent_id == 0) ? 'hide' :'';
        const isSelected = value ? option.id === Number(value) ? "selected" : "" : "";
        const isAccessible = (!projectList.permission_list.includes(option.id) && $(current).hasClass("proj_forcast")) ? "disabled" : "";
//        const className = (project && option.id == project.id && !($(current).hasClass("proj_forcast")) && !($(current).attr("data-id") === "iwo-project")) ? "hide" : "";
        const className = (project && option.id == project.id && ($(current).attr("data-id") === "iwo-project")) ? "hide" : "";
        return `<option value="${option.id}" project-type=${projectType} ${isSelected} ${isAccessible} class="${className} ${primaryProject}" >${option.name}</option>`;
    });
    optionElements.unshift('<option value="" disabled selected>Select an Option</option>');
    const optionsHTML = optionElements.join('');

    let projectComponent = ``
    const label = `<label>Project <span class="mandatory_mark"> *</span>${editIcon}</label>`;

    const id = $(current).attr("data-id");
    const name = $(current).attr("data-name");
    const select = `<select class="form-control chosen-select project_select_dropdown" ${ isEditDisable == 'true' ? "disabled" : ""} id=${id} name=${name}>
                        ${optionsHTML}
                    </select>`

    projectComponent += label;
    projectComponent += select;
    $(current).append(projectComponent);

    $(".chosen-select").chosen()
    .on('chosen:showing_dropdown', function() {
        $(current).find('.chosen-results li').each(function() {
            $(this).html($(this).html().replace(/ &gt; /g, ' <i class="fa fa-chevron-circle-right" aria-hidden="true"></i> '));
        });
    }).change(function() {
        $(current).find(".chosen-single").html($(current).find(".chosen-single").html().replace(/ &gt; /g, ' <i class="fa fa-chevron-circle-right" aria-hidden="true"></i> '));
    });

    setTimeout(() => {
        $(current).find(".chosen-single").html($(current).find(".chosen-single").html().replace(/ &gt; /g, ' <i class="fa fa-chevron-circle-right" aria-hidden="true"></i> '));
    }, 100)
}
const actualProjectsBudget = async (projectId, projectType="Secondary") => {
    if(projectType == "Secondary"){
//        $('#expRev').show();
        $('#loading').show();
            const response = await $.ajax({
                url: '/erp/sales/json/total_credit_debit/',
                type: "GET",
                data: { 'project_id': projectId }
            });
            $('#loading').hide();
            if(response.response_code == 200){
                $('#budgetAmount').text(response['budget_amount']);
                if(response.data.length > 0){
                    $('#expense').text(response.data[0].total_debit);
                    $('#revenue').text(response.data[0].total_credit);
                    $('#cashflow_amount').text(response.data[0].cashflow_amount);
                    $('#cash_allocated').text(response.data[0].cash_allocated);
                }
            }
    }
    else{
        $('#expRev').hide();
    }
};