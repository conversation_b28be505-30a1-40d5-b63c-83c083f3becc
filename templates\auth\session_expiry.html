<!DOCTYPE html>
<html lang="en">
<head>
  <title>xserp</title>
  <meta charset="utf-8">
  <link rel="icon" href="/site_media/images/xs-logo-with-border.png" type="image/png">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" type="text/css" href="/site_media/css/bootstrap.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/erp_default.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/JSCustomValidator.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/sweetalert.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/roboto-font.css?v={{ current_version }}" >
  <link rel="stylesheet" type="text/css" href="/site_media/css/login.css?v={{ current_version }}" >
  <link rel="stylesheet" type="text/css" href="/site_media/css/login-menu.css?v={{ current_version }}" >
  <link rel="stylesheet" type="text/css" href="/site_media/css/font-awesome.min.css?v={{ current_version }}" >

  <script type="text/javascript" src="/site_media/js/jquery-3.1.1.min.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/bootstrap.min.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/jquery-ui.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/JSCustomValidator.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/sweetalert.min.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/field-validation.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/cookies.js?v={{ current_version }}"></script>
  <style type="text/css">
		.session_out_container {
	    	width: 600px;
	    	text-align: center;
	    	margin: auto;
	    	padding: 14px 0 20px;
	    	margin: 0;
		    position: absolute;
		    top: 35%;
		    left: 50%;
		    margin-right: -50%;
		    transform: translate(-50%, -50%)
		}
	</style>
</head>
</head>

<body style="background: #f9f9f9;">
    
    <!-- main menu -->
    {% include "public/theme-header.html" %}
    <div class="main__theme__wrap contact-main-container">
    	<div class="contact-container session_out_container">
    		<div class="contact-header text-center">
    			{{message}}
  			</div>
  			<div class="xserp-contact text-center">
	  			<button class="btn btn-lg btn-primary" id="fb-button" style="background: transparent;color: #333; border: none; box-shadow: 0 0; opacity: 0.5; position: absolute; margin-top: 40px; font-size: 12px; left: 220px; border-radius: 0;" onClick="showFeedbackForm()">Send Feedback</button>
	  			<a class="btn btn-lg btn-primary" href="/erp/" style="width: 300px;" type="submit">Login Again</a>
	  			
	  		</div>
		</div>
    
	    <div class="container login-testimonial-container hide" style="max-width: 700px; margin-top: 100px;">
		  	<div class="col-sm-12 contact-container">
			    <div class="col-sm-12 xserp-contact" style="margin-bottom: 45px;">
			    	<div class="xserp-heading">
	                	<div class="contact-header text-center">
	            			Feedback
	          			</div>
	          			<div style="position: absolute; right: 5px; top: 15px; font-size: 20px; color: #666; cursor: pointer;">
	          				<i class="fa fa-times" aria-hidden="true" onclick="showSessionTimeout()"></i>
	          			</div>
	             	</div>
	      			<form name="thisForm" method="post" action="" onsubmit="return validateform()">
	      				{% csrf_token %}
						<div class="form-group wrap-input floating-label">
	                 		<textarea rows="3" class="form-control floating-input" style="min-height: 80px;" name="user_feedback" id="user_feedback" placeholder=" " maxlength="500" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');"></textarea>
	                 		<label>Feedback</label>
	              		</div>
	              		<div class="form-group" style="margin-top: 40px;">
	                     	<button class="btn btn-lg btn-primary btn-block" id="send_mail_form" type="button">Send</button>
	                  	</div>
	               	</form>
	       	 	</div>
		  	</div> 
		</div>
	</div>
    <!-- footer -->
    {% include "public/theme-footer.html" %}
    <script type="text/javascript" src="/site_media/js/jquery.js.download"></script>
    <script type="text/javascript" src="/site_media/js/login-menu.js?v={{ current_version }}"></script>
    <script type="text/javascript">
    	$(document).ready(function(){
	        $(".contact-main-container").css({minHeight: ( (window.innerHeight))+"px"});
	  	});

	  	function showFeedbackForm() {
	  		$(".login-testimonial-container").removeClass("hide");
	  		$(".session_out_container").addClass("hide");
	  	}

	  	function showSessionTimeout() {
	  		$(".login-testimonial-container").addClass("hide");
	  		$(".session_out_container").removeClass("hide");
	  	}

	  	$("#send_mail_form").click(function(){
	      	$("#send_mail_form").addClass("btn-theme-processing").text("Processing...");
		    $(".error-border").removeClass('error-border');
		    $(".custom-error-message").remove();
		    var ControlCollections = [{
		            controltype: 'textbox',
		            controlid: 'user_feedback',
		            isrequired: true,
		            errormsg: 'Message is required.'
		        }
		    ];
		    var result = JSCustomValidator.JSvalidate(ControlCollections);

		    if (result) {
				var name = getCookie('user_name');
				var email = getCookie('user_email');
				var enterprise = getCookie('user_enterprise');
				var feedback = $("#user_feedback").val();
				var subject = "[Feedback] from " + name + " - " + enterprise;
				$.ajax({
					url: "/erp/public/send_contact_us_mail/",
					type: "post",
					data: {
						name: name, user_email: email, feedback: feedback, subject: subject
					},
					success: function(response) {
						if (response.response_message === "Success") {
             				 $("#fb-button").text("Feedback sent Successfully!").attr("disabled", "disabled").css('left', '190px');;
             				 showSessionTimeout();
						} else {
							swal({title: "", text: response.custom_message, type: "error"});
              				$("#send_mail_form").removeClass("btn-theme-processing").text("Send");
						}
					},
					error: function(xhr, errmsg, err) {
						console.log(xhr.status + ": " + xhr.responseText);
					}
				});
			}
			else {
				$("#send_mail_form").removeClass("btn-theme-processing").text("Send");
			}
		});
    </script>
</body>
</html>