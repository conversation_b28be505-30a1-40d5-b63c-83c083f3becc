import base64
from datetime import datetime
from num2words import num2words
import pdfkit
from django.template import Context
from django.template.loader import get_template

from erp import helper
from erp.helper import constructDifferentMakeName
from erp.masters.backend import MasterService, LocationService
from erp.stores import logger
from util.document_compiler import PDFGenerator
from util.document_properties import getStyleSheet
from util.helper import getAbsolutePath, writeFile
from util.properties import CIN_LABEL

GRN_DOC_FOOTER_PATH = '/site_media/tmp/grn_footer_%s.html'
MRS_DOC_FOOTER_PATH = '/site_media/tmp/mrs_footer_%s.html'
STOCK_TRANSFER_DOC_FOOTER_PATH = '/site_media/tmp/idc_footer_%s.html'
STOCK_TRANSFER_DOC_HEADER_PATH = '/site_media/tmp/idc_header_%s.html'

__author__ = 'ka<PERSON>vanan'

styles = getStyleSheet()
TARGET_PATH = '/site_media/tmp/grn.pdf'
MRS_TARGET_PATH = '/site_media/tmp/mrs.pdf'
STOCK_TRANSFER_TARGET_PATH = '/site_media/tmp/idc.pdf'

ANNEXURE_HEADER = 'ANNEXURE TO GRN %s DATED %s'


def _splitRemarks(string, length):
	res = []
	for i in range(0, len(string), length):
		res.append(string[i:i + length])
	return res


class ReceiptPDFGenerator(PDFGenerator):
	"""
	Generates a PDF replica of the GRN that will be available printed on a pre-printed sheet.
	"""
	def __init__(self, receipt=None, target_file_path=TARGET_PATH):
		super(ReceiptPDFGenerator, self).__init__(target_file_path)
		self.receipt = receipt

	def getGRNMaterialDetails(self, source=None):
		materials_received = []
		materials_returned = []

		item_index = 0

		logger.info('No of Materials: %s' % len(source.items))

		for material in source.items_received:
			quantity = material.quantity
			received_qty = material.received_qty
			accepted_qty = material.accepted_qty
			unit_name = material.material.unit.unit_name
			if material.alternate_unit_id:
				scale_factor = helper.getScaleFactor(
					enterprise_id=material.enterprise_id, item_id=material.item_id,
					alternate_unit_id=material.alternate_unit_id)
				unit_name = helper.getUnitName(enterprise_id=material.enterprise_id, unit_id=material.alternate_unit_id)
				quantity = material.quantity / scale_factor
				received_qty = material.received_qty / scale_factor
				accepted_qty = material.accepted_qty / scale_factor
			
			item_index += 1
			shortage_qty = 0
			rejected_qty = 0
			ref_code = "-"
			ref_date_string = ""
			if source.received_against in ['Purchase Order', 'Job Work', 'Job In']:
				shortage_qty = quantity - received_qty
				rejected_qty = received_qty - accepted_qty
				if material.po:
					ref_code = material.po.getInternalCode()
					ref_date_string = material.po.approved_on.strftime('%Y-%m-%d')
				if source.received_against == 'Job In' and material.oa:
					ref_code = material.oa.getInternalCode()
					ref_date_string = material.oa.approved_on.strftime('%Y-%m-%d')
			material_make = ""
			material_name = material.material.name
			if material.material and material.material.makes_json != "":
				make_name = helper.constructDifferentMakeName(material.material.makes_json)
				if make_name:
					material_name = material_name + " [" + make_name + "]"

			grn_material_recieved = {
				"item_index": item_index, "ref_code": ref_code, "ref_date_string": ref_date_string,
				"material_drawing_no": material.material.drawing_no, "material_name": material_name,
				"material_make": material_make, "is_faulty": material.is_faulty, "material_unit": unit_name,
				"quantity": "%0.3f" % quantity, "received_qty": "%0.3f" % received_qty, "accepted_qty": "%0.3f" % accepted_qty,
				"shortage": "%0.3f" % shortage_qty, "rejected_qty": "%0.3f" % rejected_qty, "material_hsn_code": material.hsn_code}
			materials_received.append(grn_material_recieved)

		item_index = 0
		for material in source.items_returned:
			quantity = material.quantity
			received_qty = material.received_qty
			accepted_qty = material.accepted_qty
			unit_name = material.material.unit.unit_name
			if material.alternate_unit_id:
				scale_factor = helper.getScaleFactor(
					enterprise_id=material.enterprise_id, item_id=material.item_id,
					alternate_unit_id=material.alternate_unit_id)
				unit_name = helper.getUnitName(enterprise_id=material.enterprise_id, unit_id=material.alternate_unit_id)
				quantity = material.quantity / scale_factor
				received_qty = material.received_qty / scale_factor
				accepted_qty = material.accepted_qty / scale_factor
			item_index += 1

			shortage_qty = quantity - received_qty
			rejected_qty = received_qty - accepted_qty
			ref_code = material.dc.getInternalCode() if material.dc else "-"
			ref_date_string = material.dc.approved_on.strftime('%Y-%m-%d') if material.dc and material.dc.approved_on else ""
			make = ""
			material_name = material.material.name
			if material.material and material.material.makes_json != "":
				make_name = helper.constructDifferentMakeName(material.material.makes_json)
				if make_name:
					material_name = material_name + " [" + make_name + "]"
			grn_material_returned = {
				"item_index": item_index, "ref_code": ref_code, "ref_date_string": ref_date_string,
				"material_drawing_no": material.material.drawing_no, "material_name": material_name, "material_make": make,
				"is_faulty": material.is_faulty, "material_unit": unit_name, "quantity": "%0.3f" % quantity,
				"received_qty": "%0.3f" % received_qty, "accepted_qty": "%0.3f" % accepted_qty, "shortage": "%0.3f" % shortage_qty,
				"rejected_qty": "%0.3f" % rejected_qty, "material_hsn_code": material.hsn_code}
			materials_returned.append(grn_material_returned)

		return materials_received, materials_returned

	def generatePDF(self, source=None):
		try:
			logger.info('Generating PDF for GRN: %s' % self.receipt)
			countries = MasterService().getCountries()
			if source.enterprise.images and source.enterprise.images.logo and source.enterprise.images.logo != "":
				logo = "data:image/%s;base64,%s" % (source.enterprise.images.ext, base64.encodestring(source.enterprise.images.logo))
			else:
				logo = ""

			country_list = []
			enterprise_country = ""
			for country in countries:
				if source.enterprise.country_code == country.code:
					enterprise_country = country.name
				country_list.append({"country_code": country.code, "country_name": country.name})

			if source.enterprise.address_2 and source.enterprise.address_2 != "":
				address = source.enterprise.address_1 + ", " + source.enterprise.address_2
			else:
				address = source.enterprise.address_1

			enterprise_address = "{address}, {city}, {pin_code} {state}, {country}".format(
				address=address, city=source.enterprise.city, pin_code=source.enterprise.pin_code, state=source.enterprise.state,
				country=enterprise_country.upper())

			cin_label = CIN_LABEL
			cin_detail = ""
			for item in source.enterprise.registration_details:
				if item.label.find("CIN") != -1:
					cin_label = item.label
					cin_detail = item.details

			no_of_items_returned = len(source.items_returned)
			no_of_items_received = len(source.items_received)

			receipt_date = str(datetime.strptime(str(source.receipt_date), '%Y-%m-%d %H:%M:%S')) if source.receipt_date is not None and source.receipt_date != '0000-00-00 00:00:00' else source.receipt_date
			invoice_date = str(datetime.strptime(str(source.invoice_date), '%Y-%m-%d %H:%M:%S')) if source.invoice_date is not None and source.invoice_date != '0000-00-00 00:00:00' else source.invoice_date

			grn_materials_received, grn_materials_returned = self.getGRNMaterialDetails(source=source)

			rejection_count = 0

			for item in source.items:
				logger.info("Item Rejections count: %s" % len(item.rejections))
				if len(item.rejections) > 0:
					rejection_count += 1

			item_count = len(source.items)
			logger.info('No of Materials: %s' % item_count)

			context = Context({
				'form_name': "GRN Inspection Report", 'enterprise_logo': logo, 'enterprise_address': enterprise_address,
				'source': source, 'cin_label': cin_label, 'cin_detail': cin_detail, 'receipt_date': receipt_date,
				'invoice_date': invoice_date, 'no_of_items_returned': no_of_items_returned, 'country_list': country_list,
				'no_of_items_received': no_of_items_received, 'grn_materials_received': grn_materials_received,
				'grn_materials_returned': grn_materials_returned, 'qir_data': self._constructQIRData(source=source),
				'has_qir': source.hasQIR(), 'rejection_count': rejection_count, 'template_title': "GRN Document"})

			footer_data = get_template(getAbsolutePath('/templates/stores/grn_print_footer.html')).render(context)
			footer_file_name = GRN_DOC_FOOTER_PATH % source.receipt_no
			writeFile(footer_data, footer_file_name)

			options = {
				'page-size': 'A4', 'orientation': 'Portrait', 'margin-top': '9', 'margin-right': '5', 'margin-bottom': '15',
				'margin-left': '5', 'title': source.getCode(), '--header-right': 'Page [page] of [topage]',
				'--header-font-size': '9', 'footer-html': getAbsolutePath(footer_file_name)}

			css = [
				getAbsolutePath('/site_media/css/bootstrap.css'), getAbsolutePath('/site_media/css/invoice_template_preview.css'),
				getAbsolutePath('/site_media/css/invoice_template.css')]

			template_src = getAbsolutePath('/templates/stores/grn_print.html')

			template = get_template(template_src).render(context)
			data = pdfkit.from_string(template, False, options=options, css=css)
			return data
		except Exception as e:
			logger.exception('Something went wrong while creating PDF self.canvas - %s' % e)

	def _constructQIRData(self, source=None):
		"""
		Construct QIR Table as with data for a max of 5 Samples per row

		:param source:
		:return:
		"""
		if source.hasQIR():
			qir_data = {}
			for item in source.items:
				item_key = "%s%s%s" % (
					item.material.name, " (%s)" % item.material.drawing_no if item.material.drawing_no else "",
					" - %s" % item.oa.getCode() if item.oa else (" - %s" % item.dc.invoice_no if item.dc else ""))
				if item_key in qir_data:
					qir_data[item_key].extend(self.__constructQIRItemData(item=item))
				else:
					qir_data[item_key] = self.__constructQIRItemData(item=item)
			logger.info(qir_data)
			return qir_data
		return {}

	def __constructQIRItemData(self, item=None):
		"""
		Construct QIR table row data for every Item's Inspection log against each Parameter (max 5 samples per row)

		:param item:
		:return:
		"""
		quality_log_data = []
		if len(item.inspection_log) > 0:
			parameters = [specification.parameter for specification in item.material.specifications]
			inspection_logs = [log for log in item.inspection_log]
			samples_added = 0
			while True:
				header_row = ['Parameter \ Samples']
				next_sample_set_index = (samples_added + 5) if (samples_added + 5) < len(inspection_logs) else len(inspection_logs)
				samples = inspection_logs[samples_added:next_sample_set_index]
				remarks_row = ['Remarks']
				for sample in samples:
					header_row.append("%s (%s %s)" % (
						sample['sample_no'], sample['quantity'], item.material.unit))
					remarks_row.append(sample['remarks'])
				quality_log_data.append(header_row)
				for parameter in parameters:
					detail_row = [parameter]
					for sample in samples:
						detail_row.append(sample['observations'][parameter] if parameter in sample['observations'] else "")
					quality_log_data.append(detail_row)
				quality_log_data.append(remarks_row)
				samples_added = next_sample_set_index
				if samples_added >= len(inspection_logs):
					break
		return quality_log_data


class MaterialRequisitionPDFGenerator(PDFGenerator):
	"""
	Generates a PDF replica of the GRN that will be available printed on a pre-printed sheet.
	"""
	def __init__(self, target_file_path=MRS_TARGET_PATH):
		super(MaterialRequisitionPDFGenerator, self).__init__(target_file_path)
		self.target_file_path = target_file_path

	def generatePDF(self, source=None):
		try:
			logger.info('Generating PDF for MRS: ')
			countries = MasterService().getCountries()
			if source.enterprise.images and source.enterprise.images.logo and source.enterprise.images.logo != "":
				logo = "data:image/%s;base64,%s" % (source.enterprise.images.ext, base64.encodestring(source.enterprise.images.logo))
			else:
				logo = ""
			country_list = []
			enterprise_country = ""
			for country in countries:
				if source.enterprise.country_code == country.code:
					enterprise_country = country.name
				country_list.append({"country_code": country.code, "country_name": country.name})

			if source.enterprise.address_2 and source.enterprise.address_2 != "":
				address = source.enterprise.address_1 + ", " + source.enterprise.address_2
			else:
				address = source.enterprise.address_1

			enterprise_address = "{address}, {city}, {pin_code} {state}, {country}".format(
				address=address, city=source.enterprise.city, pin_code=source.enterprise.pin_code, state=source.enterprise.state,
				country=enterprise_country.upper())
			cin_label = CIN_LABEL
			cin_detail = ""
			for item in source.enterprise.registration_details:
				if item.label.find("CIN") != -1:
					cin_label = item.label
					cin_detail = item.details
			mrs_materials = self.getMRSMaterials(enterprise_id=source.enterprise_id, source=source)
			approved_by = helper.getUser(enterprise_id=source.enterprise_id, user_id=source.approved_by) if source.approved_by else ""
			context = Context({
				'form_name': "Material Requisition Slip", 'enterprise_logo': logo, 'enterprise_address': enterprise_address,
				'source': source, 'cin_label': cin_label, 'cin_detail': cin_detail,
				'country_list': country_list,
				'has_qir': "", 'template_title': "MRS Document",
				'mrs_materials': mrs_materials,
				'requisition_by': source.issue_id,
				'requisition_for': source.document_description,
				'prepared_on' : source.prepared_on.strftime('%Y-%m-%d'),
				'approved_by' : approved_by
			})
			footer_data = get_template(getAbsolutePath('/templates/stores/mrs_print_footer.html')).render(context)
			footer_file_name = MRS_DOC_FOOTER_PATH % source.oa_no
			writeFile(footer_data, footer_file_name)
			pdf_title = source.getMRSCode()
			options = {
				'page-size': 'A4', 'orientation': 'Portrait', 'margin-top': '9', 'margin-right': '5', 'margin-bottom': '15',
				'margin-left': '5', 'title': pdf_title, '--header-right': 'Page [page] of [topage]',
				'--header-font-size': '9', 'footer-html': getAbsolutePath(footer_file_name)}
			css = [
				getAbsolutePath('/site_media/css/bootstrap.css'), getAbsolutePath('/site_media/css/invoice_template_preview.css'),
				getAbsolutePath('/site_media/css/invoice_template.css')]
			template_src = getAbsolutePath('/templates/stores/mrs_print.html')
			template = get_template(template_src).render(context)
			data = pdfkit.from_string(template, False, options=options, css=css)
			return data
		except Exception as e:
			logger.exception('Something went wrong while creating PDF self.canvas - %s' % e)

	def getMRSMaterials(self, enterprise_id,source):
		available_materials = []
		try:
			for item in source.items:
				material_unit = helper.getUnitName(enterprise_id=enterprise_id, unit_id=item.alternate_unit_id)
				item_description = item.item.name + " - " + item.item.drawing_no if item.item.drawing_no else item.item.name
				make_name = helper.constructDifferentMakeName(make_list=item.item.makes_json)
				item_description = item_description + " [" + make_name +"]" if make_name else item_description
				oa_material = {
					"material_name": item.item.name,
					"item_description" : item_description,
					"material_quantity": item.quantity,
					"material_unit": material_unit
				}
				available_materials.append(oa_material)
		except Exception as e:
			logger.exception('Something went wrong while creating PDF self.canvas - %s' % e)
		return available_materials


class StockTransferPDFGenerator(PDFGenerator):
	"""
	Generates a PDF replica of the GRN that will be available printed on a pre-printed sheet.
	"""
	def __init__(self, target_file_path=STOCK_TRANSFER_TARGET_PATH):
		super(StockTransferPDFGenerator, self).__init__(target_file_path)
		self.target_file_path = target_file_path

	def generatePDF(self, source=None):
		try:
			logger.info('Generating PDF for Stock transfer: ')
			countries = MasterService().getCountries()
			if source.enterprise.images and source.enterprise.images.logo and source.enterprise.images.logo != "":
				logo = "data:image/%s;base64,%s" % (source.enterprise.images.ext, base64.encodestring(source.enterprise.images.logo))
			else:
				logo = ""
			country_list = []
			enterprise_country = ""
			for country in countries:
				if source.enterprise.country_code == country.code:
					enterprise_country = country.name
				country_list.append({"country_code": country.code, "country_name": country.name})

			if source.enterprise.address_2 and source.enterprise.address_2 != "":
				address = source.enterprise.address_1 + ", " + source.enterprise.address_2
			else:
				address = source.enterprise.address_1

			enterprise_address = "{address}, {city}, {pin_code} {state}, {country}".format(
				address=address, city=source.enterprise.city, pin_code=source.enterprise.pin_code, state=source.enterprise.state,
				country=enterprise_country.upper())
			cin_label = CIN_LABEL
			cin_detail = ""
			for item in source.enterprise.registration_details:
				if item.label.find("CIN") != -1:
					cin_label = item.label
					cin_detail = item.details
			prepared_by = helper.getUser(enterprise_id=source.enterprise_id, user_id=source.created_by) if source.created_by else ""
			approved_by = helper.getUser(enterprise_id=source.enterprise_id, user_id=source.approved_by) if source.approved_by else ""
			received_by = helper.getUser(enterprise_id=source.enterprise_id, user_id=source.received_by) if source.received_by else ""
			for _item in source.transfer_details:
				_item.material_name = _item.item
			location_service = LocationService()
			from_location_details = location_service.get_location(location_id=source.from_location)
			to_location_details = location_service.get_location(location_id=source.to_location)
			for item in source.transfer_details:
				make_name = constructDifferentMakeName(item.item.makes_json)
				item.make_name = make_name
			context = Context({
				'form_name': "Delivery Challan", 'enterprise_logo': logo, 'enterprise_address': enterprise_address,
				'source': source, 'cin_label': cin_label, 'cin_detail': cin_detail,
				'idc_no': source.getCode(),
				'country_list': country_list,
				'has_qir': "", 'template_title': "Delivery Challan",
				'from_location': from_location_details,
				'to_location': to_location_details,
				'approved_by': approved_by,
				'received_by': received_by,
				'prepared_by': prepared_by,
				'prepared_on': source.created_on.strftime('%Y-%m-%d'),
				'transfer_details': source.transfer_details,
				'total_value': source.total_value,
				'is_receiver_sign_enabled': True,
				'total_in_words': (num2words(source.total_value, lang='en_IN').upper()),
			})
			footer_template = get_template(getAbsolutePath('/templates/admin/print_template/stock_transfer/idc_footer.html')).render(context)
			footer_file_name = STOCK_TRANSFER_DOC_FOOTER_PATH % source.transfer_id
			writeFile(footer_template, footer_file_name)

			header_template = get_template(getAbsolutePath('/templates/admin/print_template/stock_transfer/idc_header.html')).render(context)
			header_file_name = STOCK_TRANSFER_DOC_HEADER_PATH % source.transfer_id
			writeFile(header_template, header_file_name)

			options = {
				'page-size': 'A4',
				'orientation': 'Portrait',
				'margin-top': '9',
				'margin-right': '5',
				'margin-bottom': '15',
				'margin-left': '5',
				'title': source.getCode(),
				'--header-right': 'Page [page] of [topage]',
				'--header-font-size': '8',
				'footer-html': getAbsolutePath(footer_file_name),
				'header-html': getAbsolutePath(header_file_name),
				'encoding': "utf-8"}
			css = [
				getAbsolutePath('/site_media/css/bootstrap.css'), getAbsolutePath('/site_media/css/invoice_template_preview.css'),
				getAbsolutePath('/site_media/css/invoice_template.css')]
			template_src = getAbsolutePath('/templates/admin/print_template/stock_transfer/stock_transfer_print.html')
			template = get_template(template_src).render(context)
			data = pdfkit.from_string(template, False, options=options, css=css)
			return data
		except Exception as e:
			logger.exception('Something went wrong while creating PDF self.canvas - %s' % e)