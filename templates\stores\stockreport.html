{% extends "stores/sidebar.html" %}
{% block stockreport %}
{% if access_level.view %}
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/chosen.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/bootstrap-datepicker.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/report.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-multiselect.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/chosen.jquery.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/jquery.easyResponsiveTabs.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/daterangepicker.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/load_material.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/Component/locations.js?v={{ current_version }}"></script>

<style>
	li.stock_report_menu a{
		outline: none;
		background-color: #e6983c !important;
	}
	#locationList{
	margin-top:0px;
	min-width : 200px;
	}
	.checkbox{
		white-space: normal
	}
</style>
<div class="right-content-container download-icon-onTop">
	<div class="page-title-container">
		<span class="page-title">Stock Report</span>
	</div>
	<div class="container-fluid">
		<div class="row">
			<div class="col-lg-12">
				<div class="content_bg">
					<ul class="resp-tabs-list hor_1"></ul>
					<div class="resp-tabs-container hor_1">
						<div class="row">&nbsp;</div>
						<div class="row">
							<div class="add_table">
								<div class="col-lg-12 add_table_content">
									<div class="col-sm-2">
										<label>Report</label>
										<select class="form-control" name="select" id="reportname">
											<option value="">Closing Stock</option>
											<option value="">PO Pending Report</option>
											{% if module_access.indent %}
												<option value="">Indent Pending Report</option>
											{% endif %}
											<option value="">Non Moving Stock Report</option>
											<option value="">Blocked Stock Report</option>
											<option value="">MSL Stock Report</option>
										</select>
									</div>
									<div class="col-sm-3 col-md-2 col-lg-3 form-group" id="dateRange">
										<label>Date Range</label>
										<div id="reportrange" class="report-range form-control">
											<i class="glyphicon glyphicon-calendar"></i>&nbsp;
											<span></span> <b class="caret"></b>
											<input type="hidden" class="fromdate" id="fromdate" name="fromdate" />
											<input type="hidden" class="todate" id="todate" name="todate" />
										</div>
									</div>
									<div id="days_range" class="col-sm-3" style="display:none">
										<label>Date Range</label>
										<select  name="select" id="daysRange" class="form-control">
											<option value="30" >30 Days</option>
											<option value="60" >60 Days</option>
											<option value="3" >3 Months</option>
											<option value="6" >6 Months</option>
											<option value="12" >1 Year</option>
											<option value="1" > Above 1 Year</option>
										</select>
									</div>
									<div id="select_div" class="col-sm-2 multiselect_option">
										<label>Select</label>
										<select  name="select" id="search" multiple="multiple">
											<option value="2" >Category</option>
											<option value="3" >Drawing No</option>
											<option value="4" >Name</option>
											<option value="5" >Rack</option>
											<option value="6" >Units</option>
											<option value="7" >Price</option>
											<option value="8" >Opening Balance Qty</option>
											<option value="9" >Receipt Qty</option>
											<option value="10" >Issue Qty</option>
											<option value="11" >Closing Balance Qty</option>
											<option value="12" >Opening Value</option>
											<option value="13" >Receipt Value</option>
											<option value="14" >Issue Value</option>
											<option value="15" >Closing Value</option>
											<option value="16" >In Transit Qty</option>

										</select>
									</div>
									<div id="stock_type_div" class="col-sm-2 multiselect_option">
										<label>Material Type</label>
										<select  name="stock_type" id="stock_type" multiple="multiple">
											<option value="1" selected="selected" >Stockable</option>
											<option value="0" >Non Stockable</option>
										</select>
									</div>
									<div id="stock_location" class="col-sm-2 multiselect_option">
										<label>Location</label>
										<select  name="location_id" id="location_id" multiple="multiple">
										</select>
									</div>

									<div id="pendingdet" hidden="hidden" style="margin-top: 75px;">
										<div class="col-sm-2">
											<label>Supplier</label>
											<select class="form-control chosen-select" name="select" id="cmbsupplier">
												<option value="">All</option>
												{% for j in suppliers %}
							                        <option value="{{ j.id }}">{{ j.name }} ({{ j.code }})</option>
							                    {% endfor %}
											</select>
										</div>
										<div class="col-sm-2" >
											<label>Material Name</label>
                                                <select class="form-control chosen-select" name="select" id="id-material">
                                                </select>
										</div>

										<div class="col-sm-2" >
											<label>Type</label>
											<select class="form-control" name="select" id="cmbreptype">
												<option value="0">All</option>
												<option value="1">Pending</option>
												<option value="2">Completed</option>
											</select>
										</div>
									</div>

									<div class="col-sm-1">
										<div class="material_txt">
											<span class="po_title_txt">&nbsp;</span>
											<input type="submit" class="btn btn-save" value="View Report" id="reportview"/>
										</div>
									</div>

									<div id="Closing_Stock" hidden="hidden">
										<div class="col-lg-12">
											<div class="col-lg-12 search_result_table">
												<div>&nbsp;</div>
												<div align="center">
													<h3 style="margin: 0; margin-bottom: 5px;">Closing Stock Report</h3>
												</div>
											</div>
										</div>
										<div class="col-lg-12">
											<div class="search_result_table">
												<div class="csv_export_button">
								                    <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#tablesorter'), 'Closing_Stock_Report.csv']);" data-tooltip="tooltip" title="Download&nbsp;Stock&nbsp;Report as&nbsp;CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
								                </div>
												<table class="table table-bordered custom-table table-striped" id="tablesorter" style="width: 100%; margin-right: 80px; display: inline-table;">
													<thead>
													<tr>
														<th>S.No</th>
														<th style="min-width: 100px;">Category</th>
														<th style="min-width: 100px;">Drawing No</th>
														<th style="min-width: 150px;">Name</th>
														<th>Rack</th>
														<th>Units</th>
														<th>Price</th>
														<th>Opening Qty</th>
														<th>Receipt</th>
														<th>Issue</th>
														<th>Closing Qty</th>
														<th>Opening Value</th>
														<th>Receipt Value</th>
														<th>Issue Value</th>
														<th>Closing Value</th>
														<th>In Transit Qty</th>
													</tr>
													</thead>
													<tbody>
													</tbody>
													<tfoot class="cs_tfoot">
													
													</tfoot>
												</table>
											</div>
										</div>
									</div>
									<div id="Po_Pending" hidden="hidden">
										<div class="col-lg-12 search_result_table">
											<div align="center"><h3 style="margin-bottom: 5px;"> PO Pending Report </h3></div>
										</div>
										<div class="col-lg-12 search_result_table">
											<div class="csv_export_button">
							                    <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#po_pending_table'), 'PO_Pending_Report.csv']);" data-tooltip="tooltip" title="Download&nbsp;Report&nbsp;as&nbsp;CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
							                </div>
											<table class="table table-bordered custom-table table-striped" id="po_pending_table">
												<thead>
												<tr>
													<th align="left">S.No</th>
													<th align="left">Order No</th>
													<th align="left">Date</th>
													<th align="left">Party Name</th>
													<th align="left">Drawing No</th>
													<th align="left">Name</th>
													<th align="left">Price</th>
													<th align="right">Units</th>
													<th align="right">Order Qty</th>
													<th align="right">Received Qty</th>
													<th align="right">Pending Qty</th>
												</tr>
												</thead>
												<tbody>
												</tbody>
												<tfoot class="po_tfoot">
												
												</tfoot>
											</table>
										</div>
									</div>

									<div id="Ind_Pending" hidden="hidden">
										<div class="col-lg-12 search_result_table">
											<div>&nbsp;</div>
											<div align="center"><h3 style="margin-bottom: 5px;">Indent Pending Report </h3></div>
										</div>
										<div class="col-lg-12 search_result_table">
											<div class="csv_export_button">
							                    <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#ind_pending_table'), 'Indent_Pending_Report.csv']);" data-tooltip="tooltip" title="Download&nbsp;Report&nbsp;as&nbsp;CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
							                </div>
											<table class="table table-bordered custom-table table-striped" id="ind_pending_table">
												<thead>
												<tr>
													<th align="left">S.No</th>
													<th align="left">Indent No</th>
													<th align="left">Date</th>
													<th align="left">Project/Tag</th>
													<th align="left">Drawing No</th>
													<th align="left">Name</th>
													<th align="left">Price</th>
													<th align="right">Units</th>
													<th align="right">Indent Qty</th>
													<th align="right">Po Qty</th>
													<th align="right">GRN Qty</th>
													<th align="right">Pending Qty</th>
												</tr>
												</thead>
												<tbody>
												</tbody>
												<tfoot class="indent_tfoot">
													
												</tfoot>
											</table>
										</div>
									</div>

									<div id="nms_report" hidden="hidden">
										<div class="col-lg-12 search_result_table">
											<div align="center"><h3 style="margin-bottom: 5px;"> Non Moving Stock Report</h3></div>
										</div>
										<div class="col-lg-12 search_result_table">
											<div class="csv_export_button">
							                    <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#nms_report_table'), 'NMS_Stock_Report.csv']);" data-tooltip="tooltip" title="Download&nbsp;Report&nbsp;as&nbsp;CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
							                </div>
											<table class="table table-bordered custom-table table-striped" id="nms_report_table">
												<thead>
												<tr>
													<th align="left" style="width:50px">S.No</th>
													<th align="left" style="width:80px">Category</th>
													<th align="left" style="width:100px">Unit Name</th>
													<th align="left" style="width:80px">Last Used date</th>
													<th align="left" style="width:100px">Name</th>
													<th align="left" style="width:50px">Price</th>
													<th align="left" style="width:80px">Drawing No</th>
													<th align="right" style="width:50px">Rack</th>
													<th align="right" style="width:50px">Closing Qty</th>
												</tr>
												</thead>
												<tbody>
												</tbody>
												<tfoot class="po_tfoot">

												</tfoot>
											</table>
										</div>
									</div>
									<div id="pp_blocked" hidden="hidden">
										<div class="col-lg-12 search_result_table">
											<div align="center"><h3 style="margin-bottom: 5px;"> Blocked Stock Report </h3></div>
										</div>
										<div class="col-lg-12 search_result_table">
											<div class="csv_export_button">
							                    <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#pp_blocked_table'), 'Blocked_Stock_Report.csv']);" data-tooltip="tooltip" title="Download&nbsp;Report&nbsp;as&nbsp;CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
							                </div>
											<table class="table table-bordered custom-table table-striped" id="pp_blocked_table">
												<thead>
												<tr>
													<th align="left" style="width:50px">S.No</th>
													<th align="left" style="width:80px">Drawing No</th>
													<th align="left" style="width:150px">Name</th>
													<th align="left" style="width:80px">PP No</th>
													<th align="left" style="width:50px">Required Qty</th>
													<th align="left" style="width:50px">Allocated Qty</th>
													<th align="left" style="width:50px">Issued Qty</th>
													<th align="right" style="width:50px">Blocked Qty</th>
												</tr>
												</thead>
												<tbody>
												</tbody>
												<tfoot class="po_tfoot">

												</tfoot>
											</table>
										</div>
									</div>
									<div id="msl_stock" hidden="hidden">
										<div class="col-lg-12 search_result_table">
											<div align="center"><h3 style="margin-bottom: 5px;"> Minimum Stock Level Report </h3></div>
										</div>
										<div class="col-lg-12 search_result_table">
											<div class="csv_export_button">
							                    <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#msl_report_table'), 'Minimum_Stock_Level_Report.csv']);" data-tooltip="tooltip" title="Download&nbsp;Report&nbsp;as&nbsp;CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
							                </div>
											<table class="table table-bordered custom-table table-striped" id="msl_report_table">
												<thead>
												<tr>
													<th align="left" style="width:50px">S.No</th>
													<th align="left" style="width:100px">Drawing No</th>
													<th align="left" style="width:250px">Name</th>
													<th align="left" style="width:50px">Unit</th>
													<th align="left" style="width:50px">Minimum stock level</th>
													<th align="left" style="width:50px">Closing Qty</th>
													<th align="left" style="width:50px">To be Purchased</th>
												</tr>
												</thead>
												<tbody>
												</tbody>
												<tfoot class="po_tfoot">

												</tfoot>
											</table>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						{% block stock_statement %}
					    {% endblock %}
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
$(document).ready(function(){
	calctotal();
	fetchAndPopulateLocations();
	$('.multiselect-search').on('keyup', function() {
		var searchText = $(this).val().toLowerCase();
		var items = $('.multiselect-container li:not(.filter)');

		items.each(function() {
			var text = $(this).text().toLowerCase();
			if (text.indexOf(searchText) === -1) {
				$(this).hide();
			} else {
				$(this).show();
			}
		});
	});

	$('.multiselect-clear-filter').on('click', function() {
		$('.multiselect-search').val('');
		$('.multiselect-container li:not(.filter)').show();
	});
});

function calctotal() {
	var i;
	var table = document.getElementById('tablesorter')
	var rowcount = document.getElementById('tablesorter').rows.length;
	var openingQuantity = 0;
	var recceiptQuantity =0;
	var issueQuantity =0;
	var closingQuantity=0;
	var openingValue=0;
	var receiptValue=0;
	var issueValue=0;
	var closingValue=0;
	
	for(i=1;i<rowcount;i++)	{
		openingQuantity = Number(openingQuantity) + Number($(table.rows[i]).find(".cs-opening-qty").text());
		recceiptQuantity = Number(recceiptQuantity) + Number($(table.rows[i]).find(".cs-receipt-qty").text());
		issueQuantity = Number(issueQuantity) + Number($(table.rows[i]).find(".cs-issue-qty").text());
		closingQuantity = Number(closingQuantity) + Number($(table.rows[i]).find(".cs-closing-qty").text());
		openingValue = Number(openingValue) + Number($(table.rows[i]).find(".cs-opening-value").text());
		receiptValue = Number(receiptValue) + Number($(table.rows[i]).find(".cs-receipt-value").text());
		issueValue = Number(issueValue) + Number($(table.rows[i]).find(".cs-issue-value").text());
		closingValue = Number(closingValue) + Number($(table.rows[i]).find(".cs-closing-value").text());
	}
	var row1 = `<tr>
					<td></td>
					<td></td>
					<td class="text-right">Grand Total (overall)</td>
					<td></td>
					<td></td>
					<td></td>
					<td></td>
					<td class='text-right total-font-style'>${openingQuantity.toFixed(2)}</td>
					<td class='text-right total-font-style'>${recceiptQuantity.toFixed(2)}</td>
					<td class='text-right total-font-style'>${issueQuantity.toFixed(2)}</td>
					<td class='text-right total-font-style'>${closingQuantity.toFixed(2)}</td>
					<td class='text-right total-font-style'>${openingValue.toFixed(2)}</td>
					<td class='text-right total-font-style'>${receiptValue.toFixed(2)}</td>
					<td class='text-right total-font-style'>${issueValue.toFixed(2)}</td>
					<td class='text-right total-font-style'>${closingValue.toFixed(2)}</td>
				</tr>`;
	$('.cs_tfoot').append(row1);
}

function calc_po_total() {
	var i;
	var table = document.getElementById('po_pending_table')
	var rowcount = document.getElementById('po_pending_table').rows.length;
	var purchase=0;
	var received=0;
	var pending =0;
	for(i=1;i<rowcount;i++)	{
		purchase = parseFloat(purchase)+ parseFloat($(table.rows[i].cells[8]).text());
		received = parseFloat(received)+ parseFloat($(table.rows[i].cells[9]).text());
		pending = parseFloat(pending)+ parseFloat($(table.rows[i].cells[10]).text());
	}
	var row1 = 	`<tr>
					<td></td>
					<td></td>
					<td></td>
					<td></td>
					<td></td>
					<td class="text-right">Grand Total (overall)</td>
					<td></td>
					<td></td>
					<td class="text-right total-font-style">${purchase.toFixed(2)}</td>
					<td class="text-right total-font-style">${received.toFixed(2)}</td>
					<td class="text-right total-font-style">${pending.toFixed(2)}</td>
				</tr>`;
	$('.po_tfoot').append(row1).addClass('tbl');
}

function calc_ind_total() {
	var i;
	var table = document.getElementById('ind_pending_table')
	var rowcount = document.getElementById('ind_pending_table').rows.length;
	var indent=0;
	var po=0;
	var grn =0;
	var pending =0;
	for(i=1;i<rowcount;i++)	{
		indent = parseFloat(indent)+ parseFloat($(table.rows[i].cells[8]).text());
		po = parseFloat(po)+ parseFloat($(table.rows[i].cells[9]).text());
		grn = parseFloat(grn)+ parseFloat($(table.rows[i].cells[10]).text());
		pending = parseFloat(pending)+ parseFloat($(table.rows[i].cells[11]).text());
	}
	var row1 = `<tr>
					<td></td>
					<td></td>
					<td></td>
					<td></td>
					<td></td>
					<td class="text-right">Grand Total (overall)</td>
					<td></td>
					<td></td>
					<td class="text-right total-font-style">${indent.toFixed(2)}</td>
					<td class="text-right total-font-style">${po.toFixed(2)}</td>
					<td class="text-right total-font-style">${grn.toFixed(2)}</td>
					<td class="text-right total-font-style">${pending.toFixed(2)}</td>
				</tr>`;
	$('.indent_tfoot').append(row1).addClass('tbl');
}

setTimeout(function(){
	$("#search").next('.btn-group').find(".multiselect-container li:nth-child(2)").each(function(){
		$(this).addClass('li-select-all').find("label").append('<span class="checkmark"></span>');
	});
	$("#search").next('.btn-group').find(".multiselect-container li:gt(1)").each(function(){
		$(this).addClass('li-all-others');
	});
	$("#search").find("option").each(function(i, item) {
		$("#search").multiselect('select', $(item).val(), true);
	});
	$("#stock_type").next('.btn-group').find(".multiselect-container li:nth-child(1)").each(function(){
		$(this).addClass('li-select-all').find("label").append('<span class="checkmark"></span>');
	});
	$("#location_id").next('.btn-group').find(".multiselect-container li:nth-child(1)").each(function(){
		$(this).addClass('li-select-all').find("label").append('<span class="checkmark"></span>');
	});
},100);

function FieldRequiredInTable() {
	$('#loading').show();
	setTimeout(function(){
		if(oTable != undefined) {
			oSettings[0]._iDisplayLength = oSettings[0].fnRecordsTotal();              
			oTable.draw();
		}
		$("#tablesorter").find('tr').each(function(){
		   $(this).find('td').hide();
		   $(this).find('th').hide().removeClass('exclude_export');
		   $(this).find('td:nth-child(1)').show();
		   $(this).find('th:nth-child(1)').show();
		});
		var selectedValue="1";
		$(".li-all-others").each(function(){
			if($(this).find('input').is(":checked")){
				selectedValue+=","+$(this).find('input').val();
			}
		});

		if(selectedValue != "") {
			var splitSelectedValue = selectedValue.split(',');
			$.each(splitSelectedValue,function(i){
			   $("#tablesorter").find('tr').each(function(){
				   $(this).find('td:nth-child('+splitSelectedValue[i]+')').show();
				   $(this).find('th:nth-child('+splitSelectedValue[i]+')').show();
			   });
			});
		}

		$("#tablesorter").find('tr').each(function(){
			$(this).find('td').not(':visible').remove();
			$(this).find('th').not(':visible').addClass('exclude_export');
		});
		
		$(".no_match_found, .empty_search_result").attr('colspan',$('#mod_tbl tr th:visible').length);
		$("#pagination-select").trigger('change');
		$('#loading').hide();
	},10);
}

 $(document).ready(function() {
	$('#search,#stock_type,#location_id').multiselect({
		includeSelectAllOption: true
	});
});

var oTable;
var oSettings;
function TableHeaderFixedCS(){
	oTable = $('#tablesorter').DataTable({
		fixedHeader: true,
		"pageLength": 50,
		"search": {
			"smart": false
		}
	});
	oTable.on("draw",function() {
		var keyword = $('#tablesorter_filter > label:eq(0) > input').val();
		$('#tablesorter').unmark();
		$('#tablesorter').mark(keyword,{});
	});
	oTable.on('page.dt', function() {
	  $('html, body').animate({
		scrollTop: $(".dataTables_wrapper").offset().top - 15
	   }, 'slow');
	});
	oSettings = oTable.settings();
}

function TableHeaderFixedPO(){
	oTable = $('#po_pending_table').DataTable({
		fixedHeader: true,
		"pageLength": 50,
		"search": {
			"smart": false
		},
		"columns": [
			null,null,
			{ "type": "date" },
			null,null,null,null,null,null,null,null
			]
	});
	oTable.on("draw",function() {
		var keyword = $('#po_pending_table_filter > label:eq(0) > input').val();
		$('#po_pending_table').unmark();
		$('#po_pending_table').mark(keyword,{});
	});
	oTable.on('page.dt', function() {
	  $('html, body').animate({
		scrollTop: $(".dataTables_wrapper").offset().top - 15
	   }, 'slow');
	});
	oSettings = oTable.settings();
}

function TableHeaderFixedIndent(){	
	oTable = $('#ind_pending_table').DataTable({
		fixedHeader: true,
		"pageLength": 50,
		"search": {
			"smart": false
		},
		"columns": [
			null,null,
			{ "type": "date" },
			null,null,null,null,null,null,null,null,null
			]
	});
	oTable.on("draw",function() {
		var keyword = $('#ind_pending_table_filter > label:eq(0) > input').val();
		$('#ind_pending_table').unmark();
		$('#ind_pending_table').mark(keyword,{});
	});
	oTable.on('page.dt', function() {
	  $('html, body').animate({
		scrollTop: $(".dataTables_wrapper").offset().top - 15
	   }, 'slow');
	});
	oSettings = oTable.settings();
}
function calc_nms_total() {
	var i;
	var table = document.getElementById('nms_report_table')
	var rowcount = document.getElementById('nms_report_table').rows.length;
	var indent=0;
	var po=0;
	var grn =0;
	var pending =0;
	for(i=1;i<rowcount;i++)	{
		indent = parseFloat(indent)+ parseFloat($(table.rows[i].cells[8]).text());
		po = parseFloat(po)+ parseFloat($(table.rows[i].cells[9]).text());
		grn = parseFloat(grn)+ parseFloat($(table.rows[i].cells[10]).text());
		pending = parseFloat(pending)+ parseFloat($(table.rows[i].cells[11]).text());
	}
	var row1 = `<tr>
					<td></td>
					<td></td>
					<td></td>
					<td></td>
					<td></td>
					<td class="text-right">Grand Total (overall)</td>
					<td></td>
					<td></td>
					<td class="text-right total-font-style">${indent.toFixed(2)}</td>
					<td class="text-right total-font-style">${po.toFixed(2)}</td>
					<td class="text-right total-font-style">${grn.toFixed(2)}</td>
					<td class="text-right total-font-style">${pending.toFixed(2)}</td>
				</tr>`;
	$('.indent_tfoot').append(row1).addClass('tbl');
}

function TableHeaderFixedNms(){
	oTable = $('#nms_report_table').DataTable({
		fixedHeader: true,
		"pageLength": 50,
		"search": {
			"smart": false
		},
		"columns": [
			null,null,null,{ "type": "date" },null,null,null,null,null
			]
	});
	oTable.on("draw",function() {
		var keyword = $('#nms_report_table_filter > label:eq(0) > input').val();
		$('#nms_report_table').unmark();
		$('#nms_report_table').mark(keyword,{});
	});
	oTable.on('page.dt', function() {
	  $('html, body').animate({
		scrollTop: $(".dataTables_wrapper").offset().top - 15
	   }, 'slow');
	});
	oSettings = oTable.settings();
}

function TableHeaderFixedBlockRep(){
	oTable = $('#pp_blocked_table').DataTable({
		fixedHeader: true,
		"pageLength": 50,
		"search": {
			"smart": false
		},
		"columns": [
			null,null,null,null,null,null,null,null
			]
	});
	oTable.on("draw",function() {
		var keyword = $('#pp_blocked_table_filter > label:eq(0) > input').val();
		$('#pp_blocked_table').unmark();
		$('#pp_blocked_table').mark(keyword,{});
	});
	oTable.on('page.dt', function() {
	  $('html, body').animate({
		scrollTop: $(".dataTables_wrapper").offset().top - 15
	   }, 'slow');
	});
	oSettings = oTable.settings();
}
function TableHeaderMSLRep(){
	oTable = $('#msl_report_table').DataTable({
		fixedHeader: true,
		"pageLength": 50,
		"search": {
			"smart": false
		},
		"columns": [
			null,null,null,null,null,null,null
			]
	});
	oTable.on("draw",function() {
		var keyword = $('#msl_report_table_filter > label:eq(0) > input').val();
		$('#msl_report_table').unmark();
		$('#msl_report_table').mark(keyword,{});
	});
	oTable.on('page.dt', function() {
	  $('html, body').animate({
		scrollTop: $(".dataTables_wrapper").offset().top - 15
	   }, 'slow');
	});
	oSettings = oTable.settings();
}

$('.nav-pills li').removeClass('active');
$("#li_stock_report").addClass('active');

	const fetchAndPopulateLocations = async () => {
    const locationListAll = await getLocations();
    const fromLocation = "from";
    locationList = locationListAll[fromLocation]
    console.log("locationList",locationList);
     const $locationSelect = $('#location_id');
    $.each(locationList, (index, location) => {
            const $option = $(`<option value="${location.id}">${location.name}</option>`);
            $locationSelect.append($option);
    });
    $locationSelect.find('option').prop('selected', true);
    $locationSelect.multiselect({
            includeSelectAllOption: true,
            enableFiltering: true,
            buttonWidth: '100%',
            nonSelectedText: 'Select Locations'
        });

        // Refresh the multiselect plugin
        $locationSelect.multiselect('rebuild');
	};
</script>

{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}

{% endblock %}