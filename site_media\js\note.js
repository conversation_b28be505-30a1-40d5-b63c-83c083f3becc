$(function() {
	$('#cmdadd').click(function () {
		addupdate(1);
	});

    CGST_rates="";
    SGST_rates="";
    IGST_rates="";

    $.ajax({
        url: "/erp/purchase/json/po/load_gst_tax/",
        type: "post",
        datatype:"json",
        data: {gst_type: "CGST"},
        success: function(response){
            CGST_rates += "<option value=0></option>";
            for (i = 0; i <= response.length - 1 ; i++) {
                CGST_rates += "<option value='" + response[i][0] + "'> " + response[i][2] + " </option>";
            }
        }
    });
    $.ajax({
        url: "/erp/purchase/json/po/load_gst_tax/",
        type: "post",
        datatype:"json",
        data: {gst_type: "SGST"},
        success: function(response){
            SGST_rates += "<option value=0></option>";
            for (i = 0; i <= response.length - 1 ; i++) {
                SGST_rates += "<option value='" + response[i][0] + "'> " + response[i][2] + " </option>";
            }
        }
    });

    $.ajax({
        url: "/erp/purchase/json/po/load_gst_tax/",
        type: "post",
        datatype:"json",
        data: {gst_type: "IGST"},
        success: function(response){
            IGST_rates += "<option value=0></option>";
            for (i = 0; i <= response.length - 1 ; i++) {
                IGST_rates += "<option value='" + response[i][0] + "'> " + response[i][2] + " </option>";
            }
        }
    });


    $("#add_grn_tag").click(function(){
		var tag_code = $("#grn_tag_value").val();
		var tag_text = $("#id_grn_tag").val();

		if (tag_code == ""){
			if (tag_text == ""){
				return;
            } else {
                $(".li-tagit-display").removeClass('flash');
                var currentFieldName = $("#id_grn_tag").val();
                $("#note_tags_table").find('.li-tagit-display:visible').each(function(){
                    if(currentFieldName.toLowerCase().trim() == $(this).find('.label-tagit-display').text().toLowerCase().trim()){
                        $('#id_grn_tag').val('');
                        $(this).addClass('flash');
                        return;
                    }
                });
                if($("#id_grn_tag").val().trim() !="") {
                    var row = "<li class='li-tagit-display'>" +
                                "<label class='label-tagit-display'>"+$("#id_grn_tag").val()+"</label>"+
                                "<div class='hidden-div-tagit-id' hidden='hidden'>0</div>"+
                                "<a class='delete_tag'></a>"  + "</li>";
                    $('#note_tags_table').append(row).addClass('tbl');
                }
			}
        } else {
            $(".li-tagit-display").removeClass('flash');
            var currentFieldName = $("#id_grn_tag").val();
            $("#note_tags_table").find('.li-tagit-display:visible').each(function(){
                if(currentFieldName.toLowerCase().trim() == $(this).find('.label-tagit-display').text().toLowerCase().trim()){
                    $('#grn_tag_value').val('');
                    $('#id_grn_tag').val('');
                    $(this).addClass('flash');
                    return;
                }
            });
            if($("#id_grn_tag").val().trim() !="") {
                var row = "<li class='li-tagit-display'>" +
                            "<label class='label-tagit-display'>"+$("#id_grn_tag").val()+"</label>"+
                            "<div class='hidden-div-tagit-id' hidden='hidden'>"+ $("#grn_tag_value").val() +"</div>"+
                            "<a class='delete_tag' ></a>"  + "</li>";
                $('#note_tags_table').append(row).addClass('tbl');
            }
		}
		$('#grn_tag_value').val('');
		$('#id_grn_tag').val('');
        create_delete_tag_button();
	});


	$("#add_note_tax").click(function(){
		$(".error-border").removeClass('error-border');
		$(".custom-error-message").remove();

		var ControlCollections = [
			{
				controltype: 'dropdown',
				controlid: 'note_tax_list',
				isrequired: true,
				errormsg: 'Tax type is required.'
			}
		];
		var result = JSCustomValidator.JSvalidate(ControlCollections);
		if(result) {
			AddnoteTaxClick();
		}
		return result;
	});

	function AddnoteTaxClick() {
		var selected_item =$("#note_tax_list option:selected").val();
		enterprise_id = $("#id_note-enterprise_id").val();
        if($("#note_tax_table").find("tr[name*='"+selected_item+"']").length){
            swal("Tax Already Added...");
        }
        else{
            /* Fetching Assessment Value */
            generateFormsetFormRow('note_tax');
			copyTaxEmptyForm('note_tax', parseInt($('#id_note_tax-TOTAL_FORMS').val()) - 1);
            noteTaxListAdding();
        }
        calculateGrandTotal();
	}

	$('#id_note_item-__prefix__-quantity, #id_note_item-__prefix__-rate').blur(function () {
        setTimeout(function(){
            calc_note_item_value();    
        },100)
	});

	$('#note_tax_list').change(function(){
		$("#id_note_tax-__prefix__-tax_code").val($(this).find("option:selected").attr("value"));
		$("#id_note_tax-__prefix__-receipt_no").val($("#id_receipt-receipt_no").val());
		$("#id_note_tax-__prefix__-enterprise_id").val($("#id_note-enterprise_id").val());
	});

	$("select#id_note-supplier_id").change(function(){
        var party_id = $("#id_note-supplier_id").val();
        ChangePartyCurrency(party_id,"id_note-currency_id","");
        currencyChangeEvent("onchange");
	});

	$("#id_note-invoice_no, #id_note-invoice_date, #id_note-supplier_id").change(function(){
	    if($("#id_note-invoice_no").val().trim() != ""){
            $("#loading").show();
            $.ajax({
                data: {"invoice_no": $("#id_note-invoice_no").val(), "invoice_date": $("#id_note-invoice_date").val(),
                        "party_id": $("#id_note-supplier_id").val(), "current_note_no": $("#id_edit_data").val()},
                type: "POST",
                url: "/erp/auditing/icd/check_invoice_no/",
                success: function(response){
                    $("#loading").hide();
                    if (response.response_message == "Session Timeout") {
                        location.reload();
                        return;
                    }
                    if (response.response_message == "Success") {
                        if(response.warning_type == "Hard_warning"){
                            setTimeout(function() {
                                swal({
                                    title: "Invoice number duplicates!",
                                    text: response.custom_message,
                                    type: "error"
                                },
                                function(){
                                    $("#id_note-ledger_bill_id").val("");
                                    $("#id_note-invoice_no").val("");
                                });
                            }, 100);
                        }else if(response.warning_type == "Soft_warning"){
                            confirmAction(message=response.custom_message, callback=function(isConfirm){
                                if (isConfirm){
                                    $("#id_note-ledger_bill_id").val(response["bill_id"]);
                                }else{
                                    $("#id_note-ledger_bill_id").val("");
                                    $("#id_note-invoice_no").val("");
                                }
                            });
                        }
                    }
                }
            });
        }
	});
});

$(document).ready(function(){
	var url=window.location.href;
	url = url.substring(7)
	window.urlpath = url.substring(0,url.indexOf('/'));
    $('.nav-pills li').removeClass('active');
    $('#credit_debit').addClass('active');
	transformToDummyForm('note_item');
    transformToDummyForm('note_tax');
    transformToDummyForm('tag');
    create_delete_tag_button();

	$("#txtsearch").keyup(function(){
	    if($("#search option:selected").text()!="ALL"){
            if( $(this).val().toUpperCase() != ""){
                var searchtext =$(this).val().toUpperCase();
                var position = $("#search option:selected").val();
                $("#icdList tbody>tr").show();
                $("#icdList tbody>tr").each(function() {
                    var txt = $(this).find("td:eq('"+position+"')").text().toUpperCase();
                    if( txt.indexOf(searchtext) ==  -1){ $(this).hide();}
                });
            } else {
                $("#icdList tbody>tr").show();
            }
	    } else {
            if( $(this).val() != ""){
                $("#icdList tbody>tr").hide();
                $("#icdList td:contains-ci('" + $(this).val() + "')").parent("tr").show();
	        }else{
	            $("#icdList tbody>tr").show();
	        }
	    }
	});

    // Date format & Default value
    $("#fromdate").datepicker({dateFormat: "yy-mm-dd"});
    $("#fromdate").datepicker('setDate', '-30d');
    $("#todate").datepicker({dateFormat: "yy-mm-dd"});
    $("#todate").datepicker('setDate', '+0');
    $('#todate').datepicker('setStartDate', '-30d');

    if ($("#id_receipt-receipt_no").val() == "") {
        var select = document.getElementById("id_note-currency_id");
        for (var i = 0; i < select.options.length; i++) {
            if (select.options[i].text == $("#id_home_currency").val()) {
                select.options[i].selected = true;
            }
        }
    }

    $("#id_note-currency_id").change(function(){
        currencyChangeEvent("onchange");
    });

	$('#table_tab h2:first').click();

	$("#add_note_item").click(function(){
		$(".error-border").removeClass('error-border');
		$(".custom-error-message").remove();
		$(".suggestion-container").remove();

		var ControlCollections = [
			{
				controltype: 'textbox',
				controlid: 'materialrequired',
				isrequired: true,
				errormsg: 'Description is required.'
			},
			{
				controltype: 'textbox',
				controlid: 'id_note_item-__prefix__-quantity',
				isrequired: true,
				errormsg: 'Quantity is required.',
				mindigit: 0.001,
				mindigiterrormsg: 'Quantity is required.'
			},
			{
				controltype: 'textbox',
				controlid: 'id_note_item-__prefix__-hsn_code',
				isrequired: true,
				errormsg: 'HSN/SAC is required.',
				mindigiterrormsg: 'HSN/SAC is required.',
				ishsn: true,
                hsnerrormsg: 'Invalid HSN Code.'
			},
			{
				controltype: 'dropdown',
				controlid: 'id_note_item-__prefix__-unit_id',
				isrequired: true,
				errormsg: 'Unit is required.'
			},
			{
				controltype: 'textbox',
				controlid: 'id_note_item-__prefix__-rate',
				isrequired: true,
				errormsg: 'Unit Rate is required.',
				mindigit: 0.00001,
				mindigiterrormsg: 'Rate cannot be 0.'
			},
			{
				controltype: 'dropdown',
				controlid: 'id_note_item-__prefix__-reason_id',
				isrequired: true,
				errormsg: 'Reason is required.'
			}

		];
		var result = JSCustomValidator.JSvalidate(ControlCollections);
		if(result) {
			AddNoteItemClick();
		}
		return result;
	});


	function AddNoteItemClick() {
	    if (!($('#materialrequired').val()  != "" && $('#id_note_item-__prefix__-quantity').val() != "" && $('#id_note_item-__prefix__-quantity').val() != 0 )) {
			swal("","Please Enter a Description and Enter a Qty","error");
			return;
		}
	    enterprise_id = $("#id_note-enterprise_id").val();
	    $( "#id_note_item-__prefix__-enterprise_id").val(enterprise_id);

	    var Qty = document.getElementById('id_note_item-__prefix__-quantity');
	    if (Qty.value== ""){
	        swal("Please Enter The Quantity In Numbers...");
	        return;
	    }
		else {
            var materialtable = document.getElementById("note_item_table");
            var materialrowCount = materialtable.rows.length-1;
            var match = false;
            var row_id = 0;

            for (j = 1; j <= materialrowCount; j++) {
            if ($('#id_note_item-' + parseInt(j - 1) + '-item_name').val() == $('#materialrequired').val() &&  $('#id_note_item-' + parseInt(j - 1) + '-reason_id option:selected').val() == $('#id_note_item-__prefix__-reason_id option:selected').val() ) {
                    if(!document.getElementById('id_note_item-' + parseInt(j - 1) + '-DELETE').checked) {
                        match = true;
                        swal("This Description and Reason Already Added ...");
                        return;
                    }
                }
            }

            // Update the repeated values in the invoice material table...
            generateFormsetFormRow('note_item');
            copyNoteEmptyForm('note_item', parseInt($('#id_note_item-TOTAL_FORMS').val()) - 1, 'note_item-note_id', 'note_id');
            var index = parseInt(parseInt($('#id_note_item-TOTAL_FORMS').val()) - 1) ;
            var s_no = document.getElementById("id_note_item-" + index + "-s_no");
            s_no.innerHTML = index + 1;

            $('#id_note_item-__prefix__-quantity').val(0.00)
			$('#id_note_item-__prefix__-rate').val(0.000)
			$('#id_note_item-__prefix__-amount_display').val(0.000)
		}
	    calculateGrandTotal();
	    $('#materialrequired').val("");
	    $('#materialrequired').focus();
		setTimeout(function(){
			$(".error-border").removeClass('error-border');
		},100);
		$(".custom-error-message").remove();
	}

    $("#cmdApprove").click(function(){
        var isAnyTotalFieldIsZero = IsAnyTotalFieldIsZero();
        if(isAnyTotalFieldIsZero) {
            swal("", "Any one or more Quantity/ Rate is seems to be Zero. Please edit the Quantity/ Rate field to continue.", "warning")
        }
        else {
            $("#id_approve_status").val(1);
            if ($("#id_approve_status").val()==1){
                $('#cmdSave').trigger('click');
            }
        }
    });

    $("#cmdSave").click(function(){
		$(".error-border").removeClass('error-border');
		$(".custom-error-message, .suggestion-container").remove();
		var ControlCollections = [
			{
				controltype: 'dropdown',
				controlid: 'id_note-supplier_id',
				isrequired: true,
				errormsg: 'Party is required.'
			},
			{
				controltype: 'dropdown',
				controlid: 'id_note-project_code',
				isrequired: true,
				errormsg: 'Project is required.'
			},
			{
				controltype: 'textbox',
				controlid: 'id_note-invoice_value',
				isrequired: true,
				errormsg: 'Invoice Value is required.',
				mindigit: 0.01,
				mindigiterrormsg: 'Invoice Value cannot be 0.'
			},
			{
				controltype: 'textbox',
				controlid: 'id_note-invoice_no',
				isrequired: true,
				errormsg: 'Invoice No is required.',
				mindigit: 0.01,
				mindigiterrormsg: 'Invoice No is requires.'
			}
		];

        if ($("#div_con_rate").is(":visible")){
            var control = {
                controltype: 'textbox',
                controlid: 'id_note-currency_conversion_rate',
                isrequired: true,
                errormsg: 'Required.',
                mindigit: 0.00001,
                mindigiterrormsg: 'Rate cannot be 0.'
            };
            ControlCollections[ControlCollections.length] = control;
        }

        $("#note_item_table tr").not("#note_item-__dummy__").each(function(){
            var currentHsn = $(this).find("input[id*='hsn_code']");
            var currentElementId = currentHsn.attr("id");
            var control = {
                controltype: 'textbox',
                controlid: currentElementId,
                isrequired: true,
                errormsg: 'HSN/SAC is required',
                ishsn: true,
                hsnerrormsg: 'Invalid HSN Code',
                suggestioncontainer: "hsn-wrapper"
            }
            ControlCollections[ControlCollections.length] = control;            
        });

		var result = JSCustomValidator.JSvalidate(ControlCollections);
        if(result) {
            var current_currency = $("#id_note-currency_id option:selected").text();
            var home_currency = $("#home_currency_id").val();
            var currency_conversion_reminder_message = `Rate of Currency Conversion from ${current_currency} to ${home_currency} is mentioned as 1.00`;
    		var confirm_message =  "Do you confirm?";
    		var allow_save_flag = true;
            if(current_currency != home_currency){
                if(parseFloat($("#id_note-currency_conversion_rate").val())==parseFloat(1)){
                    confirm_message = currency_conversion_reminder_message + '\n' + confirm_message;
                    allow_save_flag = false;
                }
            }
            if(!allow_save_flag){
                $("#loading").hide();
                allow_save_flag = window.confirm(confirm_message);
                result = allow_save_flag
            }
    		if(result) {
    			NoteSaveFunction();
    		}
    		else {
    			$("html, body").animate({ scrollTop: 0 }, "fast");
    			$("#id_approve_status").val("");
    		}
        }
		return result;
	});

	function NoteSaveFunction(){
        $("#loading").show();
        var item_count = parseFloat($("#id_note_item-TOTAL_FORMS").val());
        var note_tax_count = parseFloat($("#id_note_tax-TOTAL_FORMS").val());
        var item_list =0
        var tax_list = 0
        if($('#id_note-party_id option:selected').val() == "None"){
            $("#loading").hide();
			swal('Please Select a Party');
			return;
        }
        var check_tax = false
        for(i=0;i<item_count;i++){
			if(!document.getElementById("id_note_item-"+i+"-DELETE").checked){
				item_list = item_list+1
				if (document.getElementById("id_note_item_tax_"+i+"-0-rate").value==0 && document.getElementById("id_note_item_tax_"+i+"-1-rate").value==0 && document.getElementById("id_note_item_tax_"+i+"-2-rate").value==0){
                    check_tax = true
                }
			}
		}
		for(i=0;i<note_tax_count;i++){
			if(!document.getElementById("id_note_tax-"+i+"-DELETE").checked){
				tax_list = tax_list+1;
			}
		}

        if(item_list == 0) {
            $("#loading").hide();
			swal('','Please add atleast one Item in Note Creation','warning');
		}
		else {
            var isAnyTotalFieldIsZero = IsAnyTotalFieldIsZero();
            if(isAnyTotalFieldIsZero) {
                $("#loading").hide();
                swal("", "Any one or more Quantity/ Rate is seems to be Zero. Please edit the Quantity/ Rate field to continue.", "warning")
            } else if(tax_list == 0 && check_tax) {
                $("#loading").hide();
                swal({
                        title: "Please Confirm",
                        text: "One or more item has no tax attached!",
                        type: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#209be1",
                        confirmButtonText: "Yes, do it!",
                        closeOnConfirm: true,
                        closeOnCancel: true
                    },
                    function() {
                     setTimeout(function() {
                            saveNote();
                      }, 500);
                });
            } else {
                saveNote();
            }

            function saveNote(){
                $("#loading").show();
                if($("#id_approve_status").val() == 1) {
                    $("#cmdApprove").val('Processing').addClass('btn-processing');
                    $("#cmdSave").addClass('hide');
                }
                else {
                    $("#cmdSave").val('Processing').addClass('btn-processing');
                    $("#cmdApprove").addClass('hide');
                }
                calculateGrandTotal();
                $("#note_add").submit();
                ga('send', 'event',
                    $("#id_note-is_credit").is(':checked') ? "Credit Note" : "Debit Note",
                    $("#id_approve_status").val()==1 ? 'Verified':'Created Manually',
                    $('#enterprise_label').val(), 1);
            }
		}
    }

    function IsAnyTotalFieldIsZero(){
        var isAnyTotalFieldIsZero = false;
        var materialtable = document.getElementById("note_item_table");
        var materialrowCount = materialtable.rows.length-1;
        var isAnyTotalFieldIsZero = false;

        for (j = 1; j <= materialrowCount; j++) {
            if($('#note_item-' + parseInt(j - 1)).is(":visible")) {
                if ($('#note_item-' + parseInt(j - 1)).find(".table_display_amt").find("input").val() <= 0) {
                    isAnyTotalFieldIsZero = true;
                }
            }
        }
        return isAnyTotalFieldIsZero;
    }

    customAutoComplete();
    generateTaxList();
    calculateGrandTotal();
    changeNoteType()

    $('#modalProjectDetails').on('hidden.bs.modal', function(e) {
        $(this).find('.error-border').removeClass("error-border");
        $(this).find('form')[0].reset();
        $(this).find('.custom-error-message').remove();
    });

    if($("#id_note-project_code").val()==='None') {
        $("#id_note-project_code").val($('#id_note-project_code optgroup:eq(0) option:eq(0)').val()).trigger("chosen:updated");
    }
    populateMaterialUnit();
    currencyChangeEvent("onload")
});


function generateTaxList(){
    if ($("#id_edit_data").val() != ""){
        $.ajax({
            url: "/erp/auditing/json/note/editTax/",
            type: "post",
            datatype: "json",
            data: {note_id: $("#id_edit_data").val()},
            success: function (response) {
                $.each(response, function (i, note_tax_detail_as_row) {
                    $('#note_tax_table').append(note_tax_detail_as_row).addClass('tbl');
                    calculateGrandTotal();
                });

                $("#note_tax_table").find('.hnd_text_id').each(function(){
                    var selectedTax = $(this).val();
                    $("#note_tax_list option[value='"+selectedTax+"']").hide();
                    $('.chosen-select').trigger('chosen:updated');
                });
            }
        });
    }
}

function addRow(){
    var rowcount = document.getElementById('drcrtable').rows.length-1;
    var row = ` <tr>
                    <td>${rowcount}</td>
                    <td>
                        <input type='text' id='txtDescription' name='otherDescription' class='form-control' value='' placeholder='Enter Description' maxlength='150' onkeypress="validateStringOnKeyPress(this,event, 'alphaSpecialChar')" onblur="validateStringOnBlur(this, event, 'alphaSpecialChar')" />
                    </td>
                    <td>
                        <select class='form-control' name='txtreason' onchange='Calculate()' id='txtreason_${rowcount}'>${IGST_rates}</select>
                    </td>
                    <td>
                        <input type='text' id='txtQty' name='otherQty' class='form-control text-right' value='0.00' onfocus='setNumberRangeOnFocus(this,12,3)' onblur='Calculate()' />
                    </td>
                    <td>
                        <input type='text' id='txtRate' name='otherRate' class='form-control text-right' value='0.00' onfocus='setNumberRangeOnFocus(this,12,3)' onblur='Calculate()' />
                    </td>
                    <td>
                        <input type='text' id='txtAmount' name='otherAmount' class='form-control text-right' disabled='disabled' value='0.00'/>
                        <span class='checkbox checkbox-border' style='padding-top: 0 !important; padding-bottom: 0 !important;'>
                            <input name='is_credit' id='id_is_credit_${rowcount}' type='checkbox' onChange='Calculate()'>
                            <label for='id_is_credit_${rowcount}'>Is Credit</label>
                        </span>
                    </td>
                    <td hidden='hidden'>Dr</td>
                    <td hidden='hidden'>0</td>
                    <td hidden='hidden'>0</td>
                    <td>
                        <select class='form-control' name='other_cgst' onchange='Calculate()' id='txtothercgst_${rowcount}'>${CGST_rates}</select>
                        <span class="td-sub-content bracket-enclosed" name='other_cgstamt' id="txtothercgstamt${rowcount}">0.00</span>
                    </td>
                    <td>
                        <select class='form-control' name='other_sgst' onchange='Calculate()' id='txtothersgst_${rowcount}'>${SGST_rates}</select>
                        <span class="td-sub-content bracket-enclosed" name='other_sgstamt' id=txtothersgstamt${rowcount}">0.00</span>
                    </td>
                    <td>
                        <select class='form-control' name='other_igst' onchange='Calculate()' id='txtotherigst_${rowcount}'>${IGST_rates}</select>
                        <span class="td-sub-content bracket-enclosed" name='other_igstamt' id=txtotherigstamt${rowcount}">0.00</span>
                    </td>
                </tr>`;
    $(row).insertBefore('#drcrtable tbody tr:last-child').addClass('tbl');
}


function noteTaxListAdding(){
	$.ajax({
        url: "/erp/sales/json/invoice/getTax/",
        type: "post",
        datatype:"json",
        data: {tax_code: $("#note_tax_list option:selected").val() },
        success: function(response){
			$.each(response, function(i, tax_detail_as_row){
				$('#note_tax_table').append(tax_detail_as_row);
            });
			// Calculate the Total Price after applying new Tax profile
            calculateGrandTotal();
        },
        error: function (xhr, errmsg, err) {
           console.log(xhr.status + ": " + xhr.responseText);
        }
    });
	$("#note_tax_list option:selected").hide(); // Remove the added Tax from the Tax-list to avoid multiple addition
	$("#note_tax_list").val('None');// Reset the Tax Select input non_excise_note_tax_table
	$('.chosen-select').trigger('chosen:updated');
}


function copyTaxEmptyForm(form_prefix, form_idx) {
	var new_form_tax_code = document.getElementById('id_' + form_prefix + '-' + form_idx + '-tax_code');
	var new_form_enterprise_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-enterprise_id');
	var new_form_receipt_no = document.getElementById('id_' + form_prefix + '-' + form_idx + '-receipt_no');

	new_form_tax_code.value = document.getElementById('id_' + form_prefix + '-__prefix__-tax_code').value;
	new_form_enterprise_id.value = document.getElementById('id_' + form_prefix + '-__prefix__-enterprise_id').value;
	new_form_receipt_no.value = document.getElementById('id_' + form_prefix + '-__prefix__-receipt_no').value;

	document.getElementById('id_' + form_prefix + '-__prefix__-tax_code').value = '';
	document.getElementById('id_' + form_prefix + '-__prefix__-enterprise_id').value = '';
	document.getElementById('id_' + form_prefix + '-__prefix__-receipt_no').value = '';

}/*Tax Copy Processing End here...*/

function deleteNoteTax(catTaxFormId) {
	var deleteFlag = document.getElementById('id_' + catTaxFormId + '-DELETE');
	var deleteRow = document.getElementById(catTaxFormId);
	deleteFlag.checked = true;
	deleteRow.style.display = 'none';
}

function removeInvoiceTax(tax_code){
	var data_count = document.getElementById('note_tax_table').rows.length - 1;
	var row_id=0;
	var param_tax;
	for(i=0;i<=data_count;i++){
		if($("#id_note_tax-"+i+"-tax_code").val()== tax_code)
			row_id = i;
    }
	tax_rows = document.getElementsByName('tr_' + tax_code);
	for(i=tax_rows.length - 1; i>=0; i--){
		// Removed in reverse order as the removal renumbers the RowIndices immediately
		document.getElementById("note_tax_table").deleteRow(tax_rows[i].rowIndex);
	}
	$("#note_tax_list option[value='"+ tax_code +"']").show();
	param_tax ="note_tax-"+row_id;

	deleteNoteTax(param_tax);
	calculateGrandTotal();
    // Re-show the tax in the Tax list
}


function copyNoteEmptyForm(form_prefix, form_idx, fk_field_name, new_form_fk_field_name) {

    var new_form_item_name = document.getElementById('id_' + form_prefix + '-' + form_idx + '-item_name');
    var new_form_qty = document.getElementById('id_' + form_prefix + '-' + form_idx + '-quantity');
    var new_form_hsn_code = document.getElementById('id_' + form_prefix + '-' + form_idx + '-hsn_code');
    var new_form_unit_rate = document.getElementById('id_' + form_prefix + '-' + form_idx + '-rate');
    var new_form_unit_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-unit_id');
    var new_form_amount = document.getElementById('id_' + form_prefix + '-' + form_idx + '-amount');
    var new_form_amount_display = document.getElementById('id_' + form_prefix + '-' + form_idx + '-amount_display');
    var new_form_item_tax = document.getElementById('id_'+ form_prefix +'-'+form_idx+'-item_tax');
    var new_form_enterprise_id= document.getElementById('id_'+ form_prefix +'-'+form_idx+'-enterprise_id');
    var new_form_reason_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-reason_id');

    new_form_item_name.value = document.getElementById('materialrequired').value;
    new_form_qty.value = document.getElementById('id_' + form_prefix + '-__prefix__-quantity').value;
    new_form_hsn_code.value = document.getElementById('id_' + form_prefix + '-__prefix__-hsn_code').value;
    new_form_unit_rate.value = document.getElementById('id_' + form_prefix + '-__prefix__-rate').value;
    new_form_amount.value = document.getElementById('id_'+form_prefix + '-__prefix__-amount').value;
    new_form_amount_display.value = document.getElementById('id_'+form_prefix + '-__prefix__-amount_display').value;
    new_form_enterprise_id.value = document.getElementById('id_'+form_prefix +'-__prefix__-enterprise_id').value;
    new_form_reason_id.value = document.getElementById('id_'+form_prefix +'-__prefix__-reason_id').value;
    new_form_unit_id.value = document.getElementById('id_'+form_prefix +'-__prefix__-unit_id').value;


    document.getElementById('id_' + form_prefix + '-__prefix__-quantity').value = '';
    document.getElementById('id_' + form_prefix + '-__prefix__-hsn_code').value = '';
    document.getElementById('id_' + form_prefix + '-__prefix__-rate').value = '';
    document.getElementById('id_' + form_prefix + '-__prefix__-amount').value = '';
    document.getElementById('id_' + form_prefix + '-__prefix__-amount_display').value = '';

} /* Copy Form End Here */


function calculateGSTAmount(tax_form_prefix, item_form_prefix){
    var rate = $("#id_" + tax_form_prefix + "-rate_drop_down option:selected").text();
    if(!isNaN(rate) && rate!=""){
        $("#id_" + tax_form_prefix + "-rate").val(parseFloat(rate));
    } else {
        $("#id_" + tax_form_prefix + "-rate").val('0.00');
    }
    $("#id_" + tax_form_prefix + "-tax_code").val($("#id_" + tax_form_prefix + "-rate_drop_down option:selected").val());
    var item_price = parseFloat($("#id_"+item_form_prefix+"-rate").val());
    var discount = 0;
    var quantity = parseFloat($("#id_"+item_form_prefix+"-quantity").val());
    $("#id_" + tax_form_prefix + "-amount").val((parseFloat($("#id_" + tax_form_prefix + "-rate").val()) * (item_price * quantity * (100 - discount)/(100 * 100))).toFixed(2));
    $("#id_span_" + tax_form_prefix + "-amount").text((parseFloat($("#id_" + tax_form_prefix + "-rate").val()) * (item_price * quantity * (100 - discount)/(100 * 100))).toFixed(2));
    $("#id_" + tax_form_prefix + "-amount_display").val((parseFloat($("#id_" + tax_form_prefix + "-rate").val()) * (item_price * quantity * (100 - discount)/(100 * 100))).toFixed(2));
}

function calculateGSTTotals(){
    var cgst_values = document.getElementsByName("CGST_AMT");
    $("#net_cgst_value").val(0);
    for(i=0; i<cgst_values.length; i++){
        if(!isNaN(parseFloat(cgst_values[i].value)))
            $("#net_cgst_value").val((parseFloat($("#net_cgst_value").val()) + parseFloat(cgst_values[i].value)).toFixed(2));
    }

    var sgst_values = document.getElementsByName("SGST_AMT");
    $("#net_sgst_value").val(0);
    for(i=0; i<sgst_values.length; i++){
        if(!isNaN(parseFloat(sgst_values[i].value)))
            $("#net_sgst_value").val((parseFloat($("#net_sgst_value").val()) + parseFloat(sgst_values[i].value)).toFixed(2));
    }

    var igst_values = document.getElementsByName("IGST_AMT");
    $("#net_igst_value").val(0);
    for(i=0; i<igst_values.length; i++){
        if(!isNaN(parseFloat(igst_values[i].value)))
            $("#net_igst_value").val((parseFloat($("#net_igst_value").val()) + parseFloat(igst_values[i].value)).toFixed(2));
    }
}


function customAutoComplete(){
    var cgst_options = "<option value=''>--</option>";
    var sgst_options = "<option value=''>--</option>";
    var igst_options = "<option value=''>--</option>";
    $.ajax({
        url: "/erp/sales/json/invoice/load_gst_choices/",
        type: "post",
        datatype: "json",
        data: {tax_type: "CGST" },
        success: function (response) {
            populateGSTOptions("CGST", cgst_options, response);
        }
    });

    $.ajax({
        url: "/erp/sales/json/invoice/load_gst_choices/",
        type: "post",
        datatype: "json",
        data: {tax_type: "SGST" },
        success: function (response) {
            populateGSTOptions("SGST", sgst_options, response);
        }
    });

    $.ajax({
        url: "/erp/sales/json/invoice/load_gst_choices/",
        type: "post",
        datatype: "json",
        data: {tax_type: "IGST" },
        success: function (response) {
            populateGSTOptions("IGST", igst_options, response);
        }
    });
}

function populateGSTOptions(type, options, response){
    for (i = 0; i < response.length; i++) {
        options = options + "<option value='" + response[i]["code"] + "'>" + response[i]["rate"] + "</option>";
    }
    gst_tax_fields = document.getElementsByName(type);
    for(i=0; i < gst_tax_fields.length; i++){
        gst_tax_fields[i].innerHTML = gst_tax_fields[i].innerHTML + options;
    }
}

//Calculate the Note Total Amount before adding the main table..
function calc_note_item_value(){
	var discount =0;
	var amount =0;
	var discount=0;
	var discountValue=0;
	var net_value=0;
	if (parseFloat($("#id_note_item-__prefix__-quantity").val()) !=0 &&  parseFloat($("#id_note_item-__prefix__-rate").val()) !=0) {
        amount =  parseFloat($("#id_note_item-__prefix__-quantity").val()) * parseFloat($("#id_note_item-__prefix__-rate").val())
        net_value = parseFloat(amount)
		$("#id_note_item-__prefix__-amount").val(net_value.toFixed(3));
		$("#id_note_item-__prefix__-amount_display").val(net_value.toFixed(3));
	} else {
		$("#id_note_item-__prefix__-amount").val(0);
		$("#id_note_item-__prefix__-amount_display").val(0);
	}
}


// Delete The Note Item list Items
function deleteInvoiceMaterial(catMatFormId) {
	swal({
		title: "Are you sure?",
		text: "Do you want to delete this Item!",
		type: "warning",
		showCancelButton: true,
		confirmButtonColor: "#209be1",
		confirmButtonText: "Yes, delete it!",
		closeOnConfirm: true
	},
	function(){
		var deleteFlag = document.getElementById('id_' + catMatFormId + '-DELETE');
		var deleteRow = document.getElementById(catMatFormId);
		deleteFlag.checked = true;
		deleteRow.style.display = 'none';
		calculateGrandTotal();
	});
}


function calculateGrandTotal() {
    setTimeout(function(){
        var i;
        var round_off =0;
        var simple_rates = document.getElementsByName('net_rate');
        var compound_rates = document.getElementsByName('net_rate_compound');
        var tax_wise_subtotal = document.getElementsByName('tax');
        var compound_tax_wise_subtotal = document.getElementsByName('tax_compound');
        var table = document.getElementById('note_item_table');
        var simple_assess_rates = document.getElementsByName('asses_rate');
        var compound_assess_rates = document.getElementsByName('asses_rate_compound');
        var item_taxes = document.getElementsByName('item_tax');
        if(!isNaN(parseFloat($("#id_note-round_off").val()))){
    		round_off = parseFloat(document.getElementById("id_note-round_off").value);
    	}else{
    		round_off =0
    	}

        //Calculate The Total with out Tax
    	var item_count = parseFloat($("#id_note_item-TOTAL_FORMS").val());
    	var i, total= 0,discount_val =0,assess_total=0,net_value_for_tax=0;
        //	Calculate The Net Value
    	for (i=0; i<simple_rates.length; i++){tax_wise_subtotal[i].value=0;}
        for (i=0; i<compound_rates.length; i++){compound_tax_wise_subtotal[i].value=0;}
        var net_tax = 0;
        var gst_amt = 0;

    	for(i=0;i<item_count;i++){
            // This if condition is used to avoid deleted item in calculation
    		if(!document.getElementById("id_note_item-"+i+"-DELETE").checked){
    		    $("#id_note_item-"+i+"-amount").val(calculateAssessValue($("#id_note_item-"+i+"-quantity").val(), $("#id_note_item-"+i+"-rate").val(), 0, 0));
    		    $("#id_note_item-"+i+"-amount_display").val(calculateAssessValue($("#id_note_item-"+i+"-quantity").val(), $("#id_note_item-"+i+"-rate").val(), 0, 0));
                total+= parseFloat($("#id_note_item-"+i+"-amount").val());
                calculateGSTAmount("note_item_tax_"+i+"-0","note_item-"+i);
                calculateGSTAmount("note_item_tax_"+i+"-1","note_item-"+i);
                calculateGSTAmount("note_item_tax_"+i+"-2","note_item-"+i);
                item_taxes[i].value = 0;
                if(!isNaN(parseFloat($("#id_note_item-"+i+"-quantity").val())) && parseFloat($("#id_note_item-"+i+"-quantity").val()) > 0){
                    var item_simple_tax = calculateItemTax($("#id_note_item-"+i+"-quantity").val(), $("#id_note_item-"+i+"-rate").val(), 0, simple_rates, simple_assess_rates, tax_wise_subtotal, false, 0);
                    gst_amt =  parseFloat($("#id_note_item_tax_"+i+"-0-amount").val()) + parseFloat($("#id_note_item_tax_"+i+"-1-amount").val()) +parseFloat($("#id_note_item_tax_"+i+"-2-amount").val());
                    item_simple_tax = item_simple_tax + gst_amt;
                    item_taxes[i].value = parseFloat(item_taxes[i].value) + parseFloat(item_simple_tax) +
                            parseFloat(calculateItemTax($("#id_note_item-"+i+"-quantity").val(), $("#id_note_item-"+i+"-rate").val(), 0, compound_rates, compound_assess_rates, compound_tax_wise_subtotal, true, parseFloat(item_simple_tax)));
                    net_tax = parseFloat(net_tax) + parseFloat(item_taxes[i].value);
                }
    		}else{
    		    $("#id_note_item_tax_"+i+"-0-amount").val(0);
    		    $("#id_note_item_tax_"+i+"-1-amount").val(0);
    		    $("#id_note_item_tax_"+i+"-2-amount").val(0);
    		}
    	}
    	calculateGSTTotals();
        total = total.toFixed(2);
    	$("#note_tot").val(total);
     	var grand_total = parseFloat(total) + parseFloat(net_tax) + parseFloat(round_off);
     	$("#id_note-value").val(grand_total.toFixed(2));
     	$("#grand_total_value").val(grand_total.toFixed(2));
    },100);
}


function calculateAssessValue(quantity, price, discount, assess_rate){
    var value = (parseFloat(quantity) * parseFloat(price)).toFixed(2);
    if (( 100-parseFloat(discount)) > parseFloat(assess_rate)){
        return (value * (100- parseFloat(discount))/100).toFixed(2);
    }else{
        return (value * parseFloat(assess_rate)/100).toFixed(2);
    }
}

function calculateItemTax(quantity, unit_rate, discount, tax_rates, assess_rates, tax_subtotal, is_compound, cascading_item_tax){
    var item_tax = 0;
    var tax_count = tax_rates.length;
    // Calculating the net Taxes
    for (i=0; i<tax_count; i++){
        var item_assess_value = calculateAssessValue(quantity, unit_rate, discount, assess_rates[i].value);
        if (is_compound){
            var sub_total = Math.round((parseFloat(item_assess_value) + parseFloat(cascading_item_tax)) * parseFloat(tax_rates[i].value))/100;
            cascading_item_tax = parseFloat(cascading_item_tax) + parseFloat(sub_total);
        } else {
            var sub_total = Math.round(parseFloat(item_assess_value) * parseFloat(tax_rates[i].value))/100;
        }
        tax_subtotal[i].value = (parseFloat(tax_subtotal[i].value) + parseFloat(sub_total)).toFixed(2);
        item_tax = parseFloat(item_tax) + parseFloat(sub_total);
    }
    return item_tax;
}

function changeNoteType() {
    $("#chkCrDr").change(function(){

        var isChecked = $(this).find('.switch_on').is(':checked');
        if (isChecked){
            $("#id_note-is_credit").prop('checked', true);
         }else{
            $("#id_note-is_credit").prop('checked', false);
         }
    });
}

$(function () {
    var tags = [];
    var zid =  "";
    $.ajax({
        url: "/erp/purchase/json/po/loadtags/",
        type: "post",
        datatype:"json",
        data: zid,
        success: function(response){
            for (i = 0; i <= response.length - 1 ; i++) {
                tags.push({value:response[i][0],label:response[i][1]});
            }
        }
    });

    $("#id_grn_tag").autocomplete({
        source: function(request, response) {
            var results = $.ui.autocomplete.filter(tags, request.term);
            response(results.slice(0, 30));
        },
        select: function (event, ui) {
            event.preventDefault();
            $("#id_grn_tag").val(ui.item.label);
            $("#grn_tag_value").val(ui.item.value);
            $("#add_grn_tag").click();
        }
    });
});

function create_delete_tag_button(){
    $(".delete_tag").click(function(){
        $(this).closest('li').remove();
    });
}

function populateMaterialUnit(){
    $.ajax({
        url: "/erp/masters/json/materials/populate_unit_choices/",
        type: "POST",
        dataType: "json",
        data: "",
        success: function(unit_options) {
            option_html = "";
            for(i=0; i < unit_options.length; i++){
                option_html += "<option value='"+unit_options[i][0]+"'>" + unit_options[i][1] + "</option>";
            }
            $("#id_note_item-__prefix__-unit_id").html(option_html);
        }
    });
}

$(window).load(function(){
    if($("#id_note-id").val() == "") {
        $("#id_note-supplier_id").trigger("change");
    }
    actualProjectsBudget($('#id_note-project_code').val());
    $('#id_note-project_code').change(function() {
            actualProjectsBudget($(this).val());
    });
});