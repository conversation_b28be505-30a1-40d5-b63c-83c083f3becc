{% extends "sales/sidebar.html" %}
{% block material_tax_report %}
<style xmlns="http://www.w3.org/1999/html">
li.po_reports_side_menu a{
    outline: none;
    background-color: #e6983c !important;
}
.td-width-4 {
	min-width: 40px;
	width: 40px !important;
}
.td-width-6 {
	min-width: 60px;
	width: 60px !important;
}
.td-width-8 {
	min-width: 80px;
	width: 80px !important;
}
.td-width-10 {
	min-width: 100px;
	width: 100px !important;
}
.td-width-12 {
	min-width: 120px;
	width: 120px !important;
}
</style>

<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/tablesort.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/purchase_report.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-multiselect.js?v={{ current_version }}"></script>

<div class="right-content-container download-icon-onTop">
	<div class="page-title-container">
		<span class="page-title">Material Report</span>
	</div>	
	<div class="container-fluid">
		<div class="view_table add_table row">
			<form action="/erp/sales/material_tax_report/" method="post">{% csrf_token %}
				<div class="col-lg-12 view_list_table" style="align:center;">
					<div class="filter-components">
						<div class="filter-components-container">
							<div class="dropdown">
								<button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
									<i class="fa fa-filter"></i>
								</button>
								<span class="dropdown-menu arrow_box arrow_box_filter">
									<div class="col-sm-12 form-group" >
										<label>Date Range</label>
										<div id="reportrange" class="report-range form-control">
											<i class="glyphicon glyphicon-calendar"></i>&nbsp;
											<span></span> <b class="caret"></b>
											<input type="hidden" class="fromdate" id="fromdate" name="since" value={{from_date}} />
											<input type="hidden" class="todate" id="todate" name="till" value={{to_date}} />
										</div>
									</div>
									<div class="col-sm-12 form-group">
										<label>Party Name</label>
										<select class="form-control chosen-select" name="party_id" id="party_id" >
											{% for j in customers %}
											<option value="{{j.0}}"{% if j.0 == party_id %}selected{% endif %}>{{ j.1 }}</option>
											{% endfor %}
										</select>
									</div>
									<div class="filter-footer">
										<input type="submit" class="btn btn-save" value="Apply" id="refresh"/>
			      					</div>
								</span>
							</div>
							<span class='filtered-condition filtered-date'>Date: <b></b></span>
							<span class='filtered-condition filtered-party'>Party Name: <b></b></span>
						</div>
					</div>
				</div>
			</form>
		</div>

		<div class="view_table add_table search_result_table">
			<div class="col-lg-12 ">
				<div class="csv_export_button">
                    <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#report-table'), 'Material_Report.csv']);" data-tooltip="tooltip" data-placement="bottom" title="Download&nbsp;Material&nbsp;Report as&nbsp;CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
                </div>
				<table class="table custom-table table-striped table-bordered" id="report-table" style="margin-right: 80px; display: inline-table;">
					<thead>
						<tr align="left" valign="left">
							<th class="td-width-4">S.No.</th>
							<th class="td-width-8">Invoice No</th>
							<th class="td-width-6" >Date</th>
							<th class="td-width-6" >Type</th>
							<th class="td-width-8" >OA No</th>
							<th class="td-width-6" >OA Date</th>
							<th class="td-width-10">Project/Tag</th>
							<th class="td-width-12">Customer</th>
							<th class="td-width-12">Material</th>
							<th class="td-width-6" >Qty</th>
							<th class="td-width-6">Rate</th>
							<th class="td-width-6">Value</th>
							<th class="td-width-6">CGST</th>
							<th class="td-width-6">SGST</th>
							<th class="td-width-6">IGST</th>
							<th class="td-width-6">Total</th>
						</tr>
					</thead>
					<tbody>
						{% for report_item in report_rows %}
						<tr align="left" valign="left">
							<td class="text-center">{{ forloop.counter}}.</td>
							<td class="text-center">{{ report_item.inv_no }}</td>
							<td class="text-center">{{ report_item.inv_date|date:"M d, Y" }}</td>
							<td class="text-center">{{ report_item.inv_type }}</td>
							<td>{{ report_item.oa_no }}</td>
							<td class="text-center">{{ report_item.oa_date|date:"M d, Y" }}</td>
							<td>{{ report_item.project }}</td>
							<td>{{ report_item.customer }}</td>
							<td>{{ report_item.description }}</td>
							<td class="text-right">{{ report_item.qty }}</td>
							<td class="text-right">{{ report_item.rate }}</td>
							<td class="text-right">{{ report_item.value }}</td>
							<td class="text-right">{{ report_item.cgst }}</td>
							<td class="text-right">{{ report_item.sgst }}</td>
							<td class="text-right">{{ report_item.igst }}</td>
							<td class="text-right">{{ report_item.inv_value }}</td>
						</tr>
						{% endfor %}
					</tbody>
					{% if report_rows|length >= 1 %}
						<tfoot>
							<tr>
								<td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td class="text-right"><b style="font-size: 20px;">{{total_row.description}} </b>*<br><small style="color: #FA8072; font-size: 12px;">* includes all field</td><td></td><td></td>
								<td align="right">{{total_row.value}}</td>
								<td align="right">{{total_row.cgst}}</td>
								<td align="right">{{total_row.sgst}}</td>
								<td align="right">{{total_row.igst}}</td>
								<td align="right">{{total_row.inv_value}}</td>
							</tr>
						</tfoot>
					{% endif %}	
				</table>
			</div>
		</div>
	</div>
</div>
<script>
	$(document).ready(function(){
		TableHeaderFixed();
	});

	$(window).load(function(){
		updateFilterText();
	});

	function updateFilterText() {
		$(".filtered-date b").text($("#reportrange").find("span").text());
		$(".filtered-party b").text($("#party_id option:selected").text());
	}

	var oTable;
	var oSettings;
	function TableHeaderFixed() {
		oTable = $('#report-table').DataTable({
		fixedHeader: true,
		"pageLength": 50,
		"search": {
			"smart": false
		},
		"columns": [
			null,null,					
			{ "type": "date" },
			null,null,
			{ "type": "date" },
			null,null,null,null,
			null,null,null,null,
			null,null
			]
		});
		oTable.on("draw",function() {
			var keyword = $('#report-table_filter > label:eq(0) > input').val();
			$('#report-table').unmark();
			$('#report-table').mark(keyword,{});
			setHeightForTable();
		});
		oTable.on('page.dt', function() {
		  $('html, body').animate({
			scrollTop: $(".dataTables_wrapper").offset().top - 15
		   }, 'slow');
		});
		oSettings = oTable.settings();
		$( window ).resize();
	}

	$('.nav-pills li').removeClass('active');
	$("#li_sales_material_report").addClass('active');
	$(".slide_container_part").removeClass('selected');
	$("#menu_sales").addClass('selected');
</script>
{% endblock %}